<%--
    Document   : include_TreeViewDRE
    Created on : 02/10/2012, 09:49:37
    Author     : <PERSON><PERSON>
--%>
<%@page pageEncoding="ISO-8859-1"%>
<%@include file="../includes/include_imports.jsp" %>
<script type="text/javascript">
	var $i = jQuery.noConflict();
	$i(function(){
		$i(".wrapper1").scroll(function(){
			$i(".wrapper2")
					.scrollLeft( $i(".wrapper1").scrollLeft());
		});
		$i(".wrapper2").scroll(function(){
			$i(".wrapper1")
					.scrollLeft( $i(".wrapper2").scrollLeft());
		});
	});

</script>

  <!-- ------------------------ Começo relatório TreeView  ------------------------------- -->
     <h:panelGroup >
     	<c:if test="${not empty DREControle.listaDFBrowser}">
		<table width="100%">
			<tr>
			<td width="100%" style="text-align: center;">
                <a4j:commandLink value="Expandir Tudo " rev="#posicaoDados" styleClass="expandir" oncomplete="alterarAlturaMenuLateral();"></a4j:commandLink>
                <a4j:commandLink value="Expandir " rev="#posicaoDados" styleClass="expandirUm" oncomplete="alterarAlturaMenuLateral();"></a4j:commandLink>
                <a4j:commandLink value="Retrair " rev="#posicaoDados" styleClass="retrairUm" oncomplete="alterarAlturaMenuLateral();"></a4j:commandLink>
                <a4j:commandLink value="Retrair Tudo " rev="#posicaoDados" styleClass="retrair" oncomplete="alterarAlturaMenuLateral();"></a4j:commandLink>
				</td>
			</tr>
		</table>

		<table width="100%">
			<tr>
				<td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td>
				<td width="100%">

				<table width="100%" cellpadding="3" cellspacing="0"
					border="0" class="example" id="dnd-example">
					<thead>
						<tr>
							<td width="${tamColNomeAgrupador}"></td>
							<%-- Criar o cabeçalho para os totalizadores dos meses --%>
							<c:if test="${DREControle.centroCustos}">
								<c:forEach var="centro"
									items="${DREControle.centros}">
									<th  align="center"><FONT
										COLOR=#333333 FACE="Arial,Helvetica,sans-serif"
										SIZE=2x> ${centro.nomeCentro} </FONT></th>
								</c:forEach>
							</c:if>
							<c:if test="${not DREControle.centroCustos}">
								<c:forEach var="mes"
									items="${DREControle.listaMesProcessar}">
									<th  align="center"><FONT
										COLOR=#333333 FACE="Arial,Helvetica,sans-serif"
										SIZE=2x> ${mes.nomeMes} </FONT></th>
								</c:forEach>
                            </c:if>
                                      <th align="right"><font size="2x" face="Arial,Helvetica,sans-serif" color="#333333">
										Total </font> </th>
										<th align="right"><font size="2x" face="Arial,Helvetica,sans-serif" color="#333333">
										% </font> </th>
						</tr>
					</thead>
					<tbody>
						<c:forEach var="df" varStatus="indice"
							items="${DREControle.listaDFBrowser}">

							<%-- Definir as cores da tabela zebrada --%>
							<c:choose>
								<c:when test="${indice.count % 2 == 0}">
									<c:set var="corLinha" value="#FFFFFF" scope="request" />
									<c:set var="corLinhaRessaltada" value="#93DB70"
										scope="request" />
								</c:when>
								<c:otherwise>
									<c:set var="corLinha" value="#DFE8EF" scope="request" />
									<c:set var="corLinhaRessaltada" value="#93DB70"
										scope="request" />
								</c:otherwise>
							</c:choose>
							<c:if test="${df.dre}">
								<c:set var="corLinha" value="#ADD8E6" scope="request" />
							</c:if>
							<c:choose>
								<c:when
									test="${fn:indexOf(df.codigoAgrupador, '.') > 0}">
									<%-- Definir classe para os níveis filhos  --%>
									<c:set var="noPai"
										value="${fn:substring(df.codigoAgrupador,0, fn:length(df.codigoAgrupador) -4)}"
										scope="request" />
									<c:set var="classecss" scope="request"
										value="child-of-${fn:replace(noPai,'.', '')}" />
								</c:when>
								<c:otherwise>
									<%-- Definir classe para os níveis pais  --%>
									<c:set var="classecss" scope="request" value="" />
								</c:otherwise>
							</c:choose>

							<%-- Criar cada linha da tabela  --%>

							<tr bgcolor="${corLinha}" id="${df.codigoNode}"
										class="${classecss}"
										onmouseover="mudar_cor(this,'${corLinhaRessaltada}');"
										onmouseout="mudar_cor(this,'${corLinha}');">

										<td class="tituloDemonstrativo" align="left"
											width="${tamColNomeAgrupador}">
										${df.nomeAgrupador} </td>



										<%-- Mostrar os totalizadores de cada mês  --%>
										<c:if test="${DREControle.centroCustos}">
										<c:forEach var="centro" items="${df.listaCentros}">

											<td align="right">
											<table width="100%">
												<tr>
												<c:if test="${df.link && centro.total != 0.0}">
													<td align="right">
													
													<a href="#"
														class="linkTabela"
														onclick="preencherValorChamarBotao('formDRE:botaoVisualizarLancamentosDRE',
																							'formDRE:idCentro',
																							'<c:out value="${centro.codigo}"></c:out>',
																							'formDRE:idPlano',
																							'<c:out value="${df.codigoAgrupador}"></c:out>',
																							'formDRE:idTipoListaMostrar',
																							1)"> <font color="${centro.corLinkTotalTodosMeses}">
													${centro.valorApresentar} </font> </a>
													
													
													</td>
													</c:if>
													
													<c:if test="${!df.link || centro.total == 0.0}">
													<td align="right">
														<font color="${centro.corLinkTotalTodosMeses}" class="linkTabela">
															${centro.valorApresentar} </font> 
															</td>
													</c:if>
												</tr>
												
											</table>
											</td>

										</c:forEach>
										
										</c:if>
										<c:if test="${not DREControle.centroCustos}">
										<c:forEach var="mes" items="${df.listaMeses}">

											<td align="right">
											<table>
												<tr>
													
													<c:if test="${df.link && mes.total != 0.0}">
													<td align="right">
													<a href="#" 
														class="linkTabela"
														onclick="preencherValorChamarBotao('formDRE:botaoVisualizarLancamentos',
																							'formDRE:idMesSelecionado',
																							'<c:out value="${mes.mesAno}"></c:out>',
																							'formDRE:idCodigoSelecionado',
																							'<c:out value="${df.codigoAgrupador}"></c:out>',
																							'formDRE:idTipoListaMostrar',
																							1)"> <font color="${mes.corLinkTotalTodosMesesDre}">
																							${mes.valorApresentar} </font> </a>
													</td>																							
													</c:if>
													<c:if test="${!df.link || mes.total == 0.0}">
															<td align="center">
																<font color ="${mes.corLinkTotalTodosMesesDre}" class="linkTabela">
																	${mes.valorApresentar} </font>
															</td>		
													</c:if>

												</tr>
											</table>
											</td>

										</c:forEach>
										</c:if>
											<%-- Mostrar o total de todos os meses de cada nível --%>
											<td align="right"
                                                                                            class="linkTabela">
                                                                                              <font color="${df.corLinkTotalTodosMeses}">
											  ${df.totalTodosMesesApresentarDRETela} </font></td>
											  <td align="right"
                                                                                            class="linkTabela">
                                                                                             <font color="${df.corMeta}">
											  ${df.percentualApresentar} </font></td>
									</tr>

						</c:forEach>
                                                 <tr> <td style="padding-top: 39px;">
                                                         <!-- Incluido a tag <br> porque o InternetExplorer não reconhece o  style="padding-top: 39px;" -->
                                                         <br/>
                                                     </td> </tr>
                                                 <!-- Início Imprimir os totalizadores de Entrada e Saída -->
						<tr bgcolor="#DFE8EF" onmouseover="mudar_cor(this,'#93DB70');"
										onmouseout="mudar_cor(this,'#DFE8EF');">
						<td class="tituloDemonstrativo" align="left">
										Ponto de equilíbrio </td>
							<c:if test="${DREControle.centroCustos}">
								<c:forEach var="centro"
									items="${DREControle.centros}">
									<th  align="center"></th>
								</c:forEach>
							</c:if>
							<c:if test="${not DREControle.centroCustos}">
								<c:forEach var="mes"
									items="${DREControle.listaMesProcessar}">
									<th  align="center"></th>
								</c:forEach>
                            </c:if>
	                       <td align="right"><font size="2x" face="Arial,Helvetica,sans-serif" color="${DREControle.corPE}">
								${DREControle.pontoEquilibrioApresentar} </font> </td>
							<td align="right"></td>				
						</tr>       
		               <tr onmouseover="mudar_cor(this,'#93DB70');"
										onmouseout="mudar_cor(this,'#FFFFFF');"> <td class="tituloDemonstrativo" align="left">
                           Realizado no período
                        </td> 
                        <c:if test="${DREControle.centroCustos}">
								<c:forEach var="centro"
									items="${DREControle.centros}">
									<th  align="center"></th>
								</c:forEach>
							</c:if>
							<c:if test="${not DREControle.centroCustos}">
								<c:forEach var="mes"
									items="${DREControle.listaMesProcessar}">
									<th  align="center"></th>
								</c:forEach>
                            </c:if>
	                       <td align="right"><font size="2x" face="Arial,Helvetica,sans-serif" color="${DREControle.corRP}">
								${DREControle.realizadoApresentar} </font> </td>
							<td align="right"></td>
                        
                        
                        </tr> 
                       <tr bgcolor="#DFE8EF"  onmouseover="mudar_cor(this,'#93DB70');"
										onmouseout="mudar_cor(this,'#DFE8EF');"> <td class="tituloDemonstrativo" align="left">
                           Não realizado
                        </td> 
                       <c:if test="${DREControle.centroCustos}">
								<c:forEach var="centro"
									items="${DREControle.centros}">
									<th  align="center"></th>
								</c:forEach>
							</c:if>
							<c:if test="${not DREControle.centroCustos}">
								<c:forEach var="mes"
									items="${DREControle.listaMesProcessar}">
									<th  align="center"></th>
								</c:forEach>
                            </c:if>
	                       <td align="right"><font size="2x" face="Arial,Helvetica,sans-serif" color="${DREControle.corNR}">
								${DREControle.naoRealizadoApresentar} </font> </td>
							<td align="right"></td>
                        
                        
                        </tr>               
		                <tr onmouseover="mudar_cor(this,'#93DB70');"
										onmouseout="mudar_cor(this,'#FFFFFF');"> <td class="tituloDemonstrativo" align="left">
                           Resultado do exercício
                        </td> 
                       <c:if test="${DREControle.centroCustos}">
								<c:forEach var="centro"
									items="${DREControle.centros}">
									<th  align="center"></th>
								</c:forEach>
							</c:if>
						<c:if test="${not DREControle.centroCustos}">
								<c:forEach var="mes"
									items="${DREControle.listaMesProcessar}">
									<th  align="center"></th>
								</c:forEach>
                            </c:if>
	                       <td align="right" class="linkTabela"><font size="2x" face="Arial,Helvetica,sans-serif" color="${DREControle.corRE}">
								${DREControle.resultadoExercicioApresentar} </font> </td>
							
                        	<td align="right" class="linkTabela"><font size="2x" face="Arial,Helvetica,sans-serif" color="${DREControle.corRE}">
								${DREControle.resultadoExercicioPorcApresentar} </font> </td>
							
                        </tr>          
					</tbody>
				</table>
				</td>
				<td></td>
			</tr>
			

		</table>
        <br>
		<br>
	</c:if>
</h:panelGroup>
<!-- ------------------------ Fim relatório TreeView  ------------------------------- -->




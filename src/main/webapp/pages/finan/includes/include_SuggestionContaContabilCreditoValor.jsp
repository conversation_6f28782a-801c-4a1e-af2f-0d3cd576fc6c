<%@page pageEncoding="ISO-8859-1"%>
<%-- CONTA CREDITO --%>
<h:outputText rendered="#{MovContaControle.integracaoContabilAlterData}"
              value="Conta contábil credora:"
              style="vertical-align: middle"
              styleClass="tituloCampos"></h:outputText>
<h:panelGroup rendered="#{MovContaControle.integracaoContabilAlterData}">
    <h:inputText  id="nomeCreditoValor"
                  size="50"
                  style="vertical-align: middle"
                  maxlength="50"
                  onblur="blurinput(this);"
                  rendered="#{MovContaControle.integracaoContabilAlterData}"
                  onfocus="focusinput(this);"
                  styleClass="form"
                  value="#{MovContaControle.movContaVO.movContaContabilVO.contaContabilCreditoValor.descricao}" >
        <a4j:support event="onchange" action="#{MovContaControle.setarContaCredoraValorVazio}" reRender="form"/>
    </h:inputText>

    <rich:suggestionbox   height="200" width="400"
                          for="nomeCreditoValor"
                          rendered="#{MovContaControle.integracaoContabilAlterData}"
                          status="true"
                          fetchValue="#{resultContaCredoraValor.descricao}"
                          suggestionAction="#{MovContaControle.executarAutocompleteConsultarContaContabil}"
                          minChars="1"
                          nothingLabel="Nenhum registro  encontrado!"
                          rowClasses="linhaImpar, linhaPar"
                          var="resultContaCredoraValor"  id="suggestionCreditoValor">

        <a4j:support event="onselect"
                     reRender="form"
                     focus="nomeDebitoValor"
                     ignoreDupResponses="true"
                     action="#{MovContaControle.selecionarContaCredoraValorSuggestionBox}">
        </a4j:support>
        <h:column>
            <f:facet name="header">
                <h:outputText value="Descrição"  styleClass="textverysmall"/>
            </f:facet>
            <h:outputText styleClass="textverysmall" value="#{resultContaCredoraValor.descricao}" />
        </h:column>
    </rich:suggestionbox>
</h:panelGroup>



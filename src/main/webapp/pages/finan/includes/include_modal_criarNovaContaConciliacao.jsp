<%--/**--%>
<%--* Created with IntelliJ IDEA.--%>
<%--* User: <PERSON>--%>
<%--* Date: 21/07/2021--%>
<%--*/--%>

<%@page contentType="text/html" pageEncoding="ISO-8859-1" %>
<%@include file="imports.jsp" %>
<a4j:outputPanel>
    <rich:modalPanel id="modalPanelCriarNovaContaConciliacao" autosized="true"
                     styleClass="novaModal"
                     minWidth="400" height="350" width="650" shadowOpacity="true">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="#{MovContaControle.labelLancarNovaContaPagarReceberConc}"/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText
                        styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                        id="hidelinkNovaPagConc"/>
                <rich:componentControl for="modalPanelCriarNovaContaConciliacao" attachTo="hidelinkNovaPagConc"
                                       operation="hide" event="onclick"/>
            </h:panelGroup>
        </f:facet>
        <h:form id="formCriarNovaContaConciliacao">
            <h:panelGrid width="100%" columns="2" cellpadding="2" columnClasses="tituloCampos"
                         rowClasses="linhaPar, linhaImpar">


                <%--FORNECEDOR--%>
                <h:outputText styleClass="tituloCampos"
                              style="display: block;margin-top: 5px;"
                              value="#{MovContaControle.labelPagarParaOuReceberDeConc}:*"/>

                <%--FORNECEDOR PADRÃO--%>
                <h:panelGrid rendered="#{MovContaControle.usarFornecedorPadrao}"
                             width="100%" columns="3" cellpadding="2" columnClasses="tituloCampos"
                             style="display: -webkit-inline-box;"
                             rowClasses="linhaPar, linhaImpar">

                    <h:panelGroup id="panelNomeFornCadastrar">
                        <h:outputText styleClass="tituloCampos"
                                      value="#{MovContaControle.pluggyTransactionCriarNovaConta.merchant.businessName}"/>
                    </h:panelGroup>

                    <h:panelGroup style="margin-top: 6px; margin-left: 4px;">
                        <a4j:commandLink id="alterarFornecedor"
                                         style="float:right; display:block;margin-top: -1px; text-decoration: none"
                                         action="#{MovContaControle.prepararDadosEdicaoFornecedor}"
                                         reRender="formCriarNovaContaConciliacao">
                            <i class="fa-icon-edit tooltipster"
                               style="font-size: 18px;"
                               title="Editar Fornecedor"></i>
                        </a4j:commandLink>
                    </h:panelGroup>

                    <h:panelGroup style="display: inline-grid; margin-left: 4px; " id="utilizandoFornPadrao">
                        <i class="fa-icon-question-sign tooltipster"
                           style="font-size: 18px; margin-top: -11px; position: absolute;"
                           title="${MovContaControle.titleFornecedorPadraoConc}"></i>
                    </h:panelGroup>

                </h:panelGrid>


                <%--FORNECEDOR EXISTENTE OU NOVO--%>
                <h:panelGroup id="boxFornecedorConc" rendered="#{!MovContaControle.usarFornecedorPadrao}">
                    <h:inputText id="pessoa" size="50" maxlength="80"
                                 onkeypress="if (event.keyCode == 13) { document.getElementById('formLanc:descricao').focus();return false;};"
                                 onfocus="focusinput(this);" styleClass="form"
                                 value="#{MovContaControle.pluggyTransactionCriarNovaConta.movContaVO.pessoaVO.nome}">
                        <a4j:support event="onblur" reRender="form, modalPanelCadastrarPessoaSimplificada"
                                     oncomplete="#{MovContaControle.msgAlert}"
                                     action="#{MovContaControle.cadastrarNovaPessoaConciliacao}" requestDelay="500"/>
                    </h:inputText>

                    <rich:suggestionbox
                            height="200" width="444"
                            for="pessoa"
                            fetchValue="#{result}"
                            suggestionAction="#{MovContaControle.executarAutocompleteConsultaDePessoa}"
                            minChars="1" rowClasses="20"
                            status="true"
                            nothingLabel="Nenhuma pessoa encontrada!"
                            var="result" id="suggestionPessoa">
                        <a4j:support event="onselect" ignoreDupResponses="true"
                                     action="#{MovContaControle.selecionarPessoaSuggestionBoxConc}" focus="descricao"
                                     reRender="modalPanelCadastrarPessoaSimplificada"/>
                        <h:column>
                            <f:facet name="header">
                                <h:outputText styleClass="textverysmall" value="Nome"/>
                            </f:facet>
                            <h:outputText value="#{result.nome}"/>
                        </h:column>
                        <h:column>
                            <f:facet name="header">
                                <h:outputText styleClass="textverysmall" value="Tipo"/>
                            </f:facet>
                            <h:outputText value="#{result.tipoPessoa}"/>
                        </h:column>
                        <h:column>
                            <f:facet name="header">
                                <h:outputText styleClass="textverysmall" value="CPF/CNPJ"/>
                            </f:facet>
                            <h:outputText value="#{result.cfp}"/>
                        </h:column>
                    </rich:suggestionbox>

                    <h:panelGroup id="panelForn" style="margin-top: 4px; position: fixed; margin-left: 8px;">
                        <h:panelGroup id="panelFornPadrao" style="">
                            <h:panelGroup
                                    rendered="#{!MovContaControle.usarFornecedorPadrao && MovContaControle.possuiFornecedorPadrao}">
                                <a4j:commandLink
                                        style=""
                                        id="fornPadrao"
                                        action="#{MovContaControle.voltarUtilizarFornecedorPadrao}"
                                        reRender="formCriarNovaContaConciliacao">
                                    <i class="fa-icon-undo tooltipster"
                                       style="font-size: 18px; "
                                       title="Utilizar Fornecedor padrão do lançamento."></i>
                                </a4j:commandLink>
                            </h:panelGroup>
                        </h:panelGroup>


                        <h:panelGroup id="infoforn"
                                      style="">
                            <i class="fa-icon-question-sign tooltipster"
                               style="font-size: 18px; position: absolute; font-size: 18px; position: absolute; margin-left: 5px;"
                               title="${MovContaControle.titleFornecedorNovoConc}"></i>
                        </h:panelGroup>
                    </h:panelGroup>
                </h:panelGroup>

                <%--DESCRIÇÃO--%>
                <h:outputText styleClass="tituloCampos"
                              style="display: block;margin-top: 11px;"
                              value="Descrição:*"/>
                <h:inputText styleClass="tituloCampos"
                             style="width: 100%; text-align: left;"
                             value="#{MovContaControle.descricaoNovaContaConciliacao}"/>

                <%--VALOR--%>
                <h:outputText styleClass="tituloCampos"
                              style="display: block;"
                              value="Valor:"/>

                <h:panelGroup id="panelValor">
                    <h:panelGroup id="valorFinal" style="margin-left: 4px; display: flex">
                        <h:outputText styleClass="tituloCampos tooltipster"
                                      value="R$ #{MovContaControle.pluggyTransactionCriarNovaConta.valorApresentarSemSinal}"/>
                        <h:panelGroup id="iconValor" style="margin-left: 6px; margin-top: 5px;">
                            <i class="fa-icon-question-sign tooltipster"
                               style="font-size: 18px;"
                               title="O valor não pode ser alterado pois deve ser o mesmo valor que o item do extrato."
                        </h:panelGroup>
                    </h:panelGroup>
                </h:panelGroup>

                <%--VENCIMENTO--%>
                <h:outputText styleClass="tituloCampos tooltipster"
                              style="display: grid; margin-top: 9px;"
                              value="Vencimento:*"/>
                <h:panelGroup style="display: grid; grid-template-columns: 20% 4%">
                    <h:panelGroup styleClass="dateTimeCustom"
                                  style="font-size: 11px !important;">
                        <rich:calendar
                                value="#{MovContaControle.pluggyTransactionCriarNovaConta.movContaVO.dataVencimento}"
                                id="dataVencNovaConta"
                                inputStyle="width: 80px;"
                                inputSize="10"
                                inputClass="form"
                                oninputblur="blurinput(this);"
                                oninputfocus="focusinput(this);"
                                oninputchange="return validar_Data(this.id);"
                                datePattern="dd/MM/yyyy"
                                buttonIcon="/imagens_flat/calendar-button.svg"
                                enableManualInput="true"
                                zindex="2"
                                showWeeksBar="false"/>
                    </h:panelGroup>
                </h:panelGroup>

                <%--COMPETÊNCIA--%>
                <h:outputText styleClass="tituloCampos tooltipster"
                              style="display: grid; margin-top: 9px;"
                              value="Competência:*"/>
                <h:panelGroup style="display: grid; grid-template-columns: 20% 4%">
                    <h:panelGroup styleClass="dateTimeCustom"
                                  style="font-size: 11px !important;">
                        <rich:calendar
                                value="#{MovContaControle.pluggyTransactionCriarNovaConta.movContaVO.dataCompetencia}"
                                id="dataQuitacaoNovaConta"
                                inputStyle="width: 80px;"
                                inputSize="10"
                                inputClass="form"
                                oninputblur="blurinput(this);"
                                oninputfocus="focusinput(this);"
                                oninputchange="return validar_Data(this.id);"
                                datePattern="dd/MM/yyyy"
                                buttonIcon="/imagens_flat/calendar-button.svg"
                                enableManualInput="true"
                                zindex="2"
                                showWeeksBar="false"/>
                    </h:panelGroup>
                </h:panelGroup>


                <%--PLANO DE CONTAS--%>
                <h:outputText styleClass="tituloCampos tooltipster"
                              style="display: block;"
                              value="Plano de Contas:"/>
                <h:panelGroup id="planoContasCriarNovaConta">
                    <h:inputText id="nomePlanoSelecionadoRateioNovaConta" size="50" maxlength="50"
                                 onfocus="focusinput(this);" styleClass="form"
                                 value="#{PlanoContasControle.planoNome}">
                        <a4j:support event="onchange"
                                     action="#{PlanoContasControle.setarPlanoPaiVazio}" reRender="formLanc"/>
                    </h:inputText>

                    <rich:suggestionbox height="200" width="400"
                                        for="nomePlanoSelecionadoRateioNovaConta" status="statusInComponent"
                                        immediate="true"
                                        nothingLabel="Nenhum Plano de Contas encontrado"
                                        suggestionAction="#{PlanoContasControle.executarAutocompletePesqPlanoContas}"
                                        minChars="1" rowClasses="linhaImpar, linhaPar" var="result"
                                        id="suggestionResponsavelRateio">
                        <a4j:support event="onselect" reRender="planoContasCriarNovaConta"
                                     focus="nomeCentroSelecionadoRateio"
                                     action="#{PlanoContasControle.selecionarPlanoContas}">
                        </a4j:support>
                        <h:column>
                            <f:facet name="header">
                                <h:outputText value="Nome" styleClass="textverysmall"/>
                            </f:facet>
                            <h:outputText styleClass="textverysmall"
                                          value="#{result.descricaoCurta}"/>
                        </h:column>
                        <h:column>
                            <f:facet name="header">
                                <h:outputText value="Tipo" styleClass="textverysmall"/>
                            </f:facet>
                            <h:outputText styleClass="textverysmall"
                                          value="#{result.tipoPadrao.descricao}"/>
                        </h:column>
                    </rich:suggestionbox>
                </h:panelGroup>


                <%--CENTRO DE CUSTOS--%>
                <h:outputText styleClass="tituloCampos tooltipster"
                              style="display: block;"
                              value="Centro de Custo:"/>
                <h:panelGroup id="centroCustosCriarNovaConta">
                    <h:inputText id="nomeCentroSelecionadoRateioNovaConta" size="50" maxlength="50"
                                 onblur="blurinput(this);" onfocus="focusinput(this);"
                                 styleClass="form" value="#{CentroCustosControle.centroNome}">
                        <a4j:support event="onchange"
                                     action="#{CentroCustosControle.setarCentroVazio}" reRender="formLanc"/>
                    </h:inputText>

                    <rich:suggestionbox height="200" width="400"
                                        for="nomeCentroSelecionadoRateioNovaConta" status="statusInComponent"
                                        immediate="true"
                                        nothingLabel="Nenhum Centro de Custos encontrado"
                                        suggestionAction="#{CentroCustosControle.executarAutocompletePesqCentroCusto}"
                                        minChars="1" rowClasses="linhaImpar, linhaPar" var="result"
                                        id="suggestionCentroCustoRateio">
                        <a4j:support event="onselect" reRender="centroCustosCriarNovaConta" focus="rateioFormaPagto"
                                     action="#{CentroCustosControle.selecionarCentroCusto}">
                        </a4j:support>
                        <h:column>
                            <f:facet name="header">
                                <h:outputText value="Nome" styleClass="textverysmall"/>
                            </f:facet>
                            <h:outputText styleClass="textverysmall"
                                          value="#{result.descricaoCurta}"/>
                        </h:column>
                    </rich:suggestionbox>
                </h:panelGroup>


                <%--                <h:outputText value="" id="tex"/>--%>
                <%--                <h:panelGroup style="display: block">--%>
                <%--                    <h:outputText value="Preencher em caso de quitação"--%>
                <%--                                  title="Os dados abaixo são obrigatórios somente se você utilizar a opção \"Criar e quitar\""--%>
                <%--                                  styleClass="tooltipster"--%>
                <%--                                  style="color: #777; margin-top: 5px; display: inline-block; margin-left: 60px;"/>--%>
                <%--                </h:panelGroup>--%>


                <%--CONTA--%>
                <h:outputText styleClass="tituloCampos"
                              style="display: grid; margin-top: 9px;"
                              value="Conta:*"/>
                <h:panelGroup layout="block" styleClass="cb-container">
                    <h:selectOneMenu value="#{MovContaControle.codContaQuitacaoConciliacao}"
                                     id="contaCriar"
                                     onblur="blurinput(this);"
                                     onfocus="focusinput(this);">

                        <f:selectItems value="#{MovContaControle.listaSelectItemContaPagamentoConciliacao}"/>
                    </h:selectOneMenu>
                </h:panelGroup>

                <%--DATA QUITACAO--%>
                <h:outputText styleClass="tituloCampos tooltipster"
                              style="display: grid; margin-top: 9px;"
                              value="Data Quitação:"/>
                <h:panelGroup style="display: grid; grid-template-columns: 45% 4%">
                    <h:inputText id="dataQuitacao"
                                 disabled="true"
                                 styleClass="form"
                                 value="#{MovContaControle.pluggyTransactionCriarNovaConta.dataApresentar}"/>

                    <h:panelGroup style="margin-top: 6px; margin-left: 6px;">
                        <i class="fa-icon-question-sign tooltipster"
                           style="font-size: 18px;"
                           title="Não é possível alterar a data de quitação. Ela será a mesma data do lançamento do seu
                           extrato bancário.</br><b> A data de quitação aqui só será utilizada caso você clique em 'Criar'.</br> </b>
							<b>A data de lançamento ficará sendo a mesma que essa data de quitação.</b>"</i>
                    </h:panelGroup>
                </h:panelGroup>

                <%--FORMA DE PAGAMENTO--%>
                <h:panelGroup id="textFormaPgtoCriar">
                    <h:outputText styleClass="tituloCampos"
                                  style="display: grid; margin-top: 6px;"
                                  value="Forma Pagamento:*"/>
                </h:panelGroup>

                <h:panelGroup layout="block" styleClass="cb-container">

                    <h:selectOneMenu value="#{MovContaControle.codFormaPgtoQuitacaoConciliacao}"
                                     id="selecionarFormaPGTOCriar"
                                     onblur="blurinput(this);"
                                     onfocus="focusinput(this);">
                        <f:selectItems value="#{MovContaControle.listaSelectItemFormaPagamentoConciliacao}"/>
                    </h:selectOneMenu>
                </h:panelGroup>

            </h:panelGrid>


            <h:panelGrid columns="1" styleClass="centralizado" width="100%" style="margin-top: 6px">
                <h:panelGroup>
                    <a4j:commandButton id="criarEQuitarNovaContaConc"
                                       action="#{MovContaControle.criarEQuitarContaConciliacao}"
                                       value="Quitar/Conciliar"
                                       reRender="form:panelDetalhesConciliacao"
                                       styleClass="botoes nvoBt"
                                       style="background-color: #00c350 !important;"
                                       oncomplete="#{MovContaControle.mensagemNotificar}#{MovContaControle.msgAlert}">
                    </a4j:commandButton>

                </h:panelGroup>
            </h:panelGrid>
        </h:form>
    </rich:modalPanel>
</a4j:outputPanel>

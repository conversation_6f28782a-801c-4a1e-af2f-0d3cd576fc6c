<%-- 
    Document   : include_menu_caixa
    Created on : 19/03/2012, 10:05:37
    Author     : <PERSON><PERSON><PERSON>
--%>

<%@include file="../includes/include_imports.jsp" %>
<!-- inicio box -->
<div class="box">
    <div class="boxtop">
        <img src="${contextoFinan}/images/box_top.png" />
    </div>

    <div class="boxmiddle">
        <h:panelGroup id="formMenu">
            <!-- TODO: Gerar Wiki do Financeiro -->

            <!-- Inicio - Abrir Caixa -->
            <h:panelGroup rendered="#{!(CaixaControle.usuarioTemCaixaEmAberto)}">
                <table width="146" border="0" cellspacing="0" cellpadding="0" style="padding-bottom:6px;">
                    <tr>
                        <td colspan="2" align="left" valign="top">
                            <img style="vertical-align:middle;margin-right:9px;" width="12" height="15"
                                 src="${contextoFinan}/images/btn_lancamentos.png" />

                            <h:outputLink value="#{SuperControle.urlBaseConhecimento}relatorio-ver-lancamentos/"
                                          title="Clique e saiba mais: Lan�amentos Financeiros" target="_blank">
                                <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                            </h:outputLink>

                            <a4j:commandLink  id="abrirCaixaLink" reRender="modalAbrirCaixa"
                                              action="#{CaixaControle.abrirCaixa}"
                                              oncomplete="#{CaixaControle.abrirModalCaixa}">
                                <h:outputText value="Abrir Caixa"/>
                            </a4j:commandLink>
                        </td>
                    </tr>
                </table>

                <div class="sepmenu">
                   <img src="${contextoFinan}/images/shim.gif" />
                </div>
            </h:panelGroup>
            <!-- Fim - Abrir Caixa -->

            <!-- Inicio - Fechar Caixa -->
            <h:panelGroup rendered="#{CaixaControle.usuarioTemCaixaEmAberto}">
                <table width="146" border="0" cellspacing="0" cellpadding="0" style="padding-bottom:6px;">
                    <tr>
                        <td colspan="2" align="left" valign="top">
                            <img style="vertical-align:middle;margin-right:9px;"
                                 src="${contextoFinan}/images/btn_lancamentos.png"
                                 width="12" height="15" />

                            <h:outputLink value="#{SuperControle.urlBaseConhecimento}relatorio-ver-lancamentos/"
                                          title="Clique e saiba mais: Lan�amentos Financeiros" target="_blank">
                                <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                            </h:outputLink>

                            <a4j:commandLink  id="fecharCaixaLink"
                                              action="#{CaixaControle.fecharCaixa}"
                                              oncomplete="#{CaixaControle.msgAlert}">
                                <h:outputText value="Fechar Caixa"/>
                            </a4j:commandLink>
                        </td>
                    </tr>
                </table>

                <div class="sepmenu">
                   <img src="${contextoFinan}/images/shim.gif" />
                </div>
            </h:panelGroup>
            <!-- Fim - Fechar Caixa -->

            <!-- Inicio - Consultar Caixa -->
            <table width="146" border="0" cellspacing="0" cellpadding="0" style="padding-bottom:6px;">
                <tr>
                    <td colspan="2" align="left" valign="top">
                        <img style="vertical-align:middle;margin-right:9px;"
                             src="${contextoFinan}/images/btn_lancamentos.png"
                             width="12" height="15" />

                        <h:outputLink value="#{SuperControle.urlBaseConhecimento}relatorio-ver-lancamentos/"
                                      title="Clique e saiba mais: Lan�amentos Financeiros" target="_blank">
                            <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                        </h:outputLink>

                        <a4j:commandLink  id="consultarCaixaLink"
                                          action="#{CaixaControle.abrirModalHistoricoCaixa}"
                                          oncomplete="#{CaixaControle.abrirModalConsultarCaixa}"
                                          reRender="formHistorico">
                            <h:outputText value="Consultar Caixa"/>
                        </a4j:commandLink>
                    </td>
                </tr>
            </table>
            <!-- Fim - Consultar Caixa -->

            <div class="sepmenu">
                <img src="${contextoFinan}/images/shim.gif" />
            </div>
            <!-- fim item-->

            <table width="146" border="0" cellspacing="0" cellpadding="0" style="padding-bottom:6px;">
                <tr>
                    <td colspan="2" align="left" valign="top">
                        <img style="vertical-align:middle;margin-right:7px;"
                             src="${contextoFinan}/images/arrow3.gif"
                             width="14" height="15" />
                        <a class="titulo2" href="telaInicialFinan.jsp">Menu anterior</a>
                    </td>
                </tr>
            </table>
        </h:panelGroup>
    </div>

    <div class="boxbottom">
        <img src="${contextoFinan}/images/box_bottom.png" />
    </div>
</div>
<!-- fim box -->

<%@include file="include_box_financeiro.jsp" %>
<%@include file="../includes/include_carregando.jsp" %>
<%@page contentType="text/html" pageEncoding="ISO-8859-1" %>
<%-- 
    Document   : include_lancamentos
    Created on : 22/08/2011, 17:51:16
    Author     : carla
--%>
<%@include file="../includes/include_imports.jsp" %>
<!-- Inicio Tela de Lançamentos-->


<%@include file="include_visualizacaoLancamentoDados.jsp" %>


<!--INICIO Rateio-->
<h:panelGrid width="100%" id="rateioGrid">
<h:panelGroup>
<h:panelGrid rendered="#{MovContaControle.existeMaisDeUmMovContaRateio}" id="tituloDivisaoRateio" styleClass="tablepreviewtotal" width="100%" columnClasses="colunaCentralizada">
        <h:outputText style="font-family:Arial, Helvetica, sans-serif;font-size:14px;font-weight:bold;text-decoration:none;color:#0f4c6b;" value="#{msg_aplic.prt_Finan_Lancamentos_rateioValor}"/>
</h:panelGrid>

                          
<h:panelGrid id="tabelaMovRateio" rendered="#{MovContaControle.existeMaisDeUmMovContaRateio}" columns="1" width="100%" columnClasses="colunaCentralizada">
        <h:dataTable id="itemMovContaRateio" headerClass="subordinado" rows="5"
                     rowClasses="linhaImpar, linhaPar" columnClasses="colunaCentralizada" width="80%"
                     value="#{MovContaControle.movContaVO.movContaRateios}" var="itemMovContaRateio" style="padding-left:200px;">
            <h:column>
                <f:facet name="header">
                    <h:outputText  value="#{msg_aplic.prt_Finan_Lancamento_planoContas}" />
                </f:facet>
                <h:outputText  value="#{itemMovContaRateio.planoContaVO.codigoPlano} - #{itemMovContaRateio.planoContaVO.descricao}" />
            </h:column>
            <h:column>
                <f:facet name="header">
                    <h:outputText  value="#{msg_aplic.prt_Finan_Lancamento_centroCusto}" />
                </f:facet>
                <h:outputText  value="#{itemMovContaRateio.centroCustoVO.codigoCentro} - #{itemMovContaRateio.centroCustoVO.descricao}" />
            </h:column>
            <h:column>
                <f:facet name="header">
                    <h:outputText  value="#{msg_aplic.prt_Finan_Lancamento_descricao}" />
                </f:facet>
                <h:outputText  value="#{itemMovContaRateio.descricao}" />
            </h:column>
            <h:column>
                <f:facet name="header">
                    <h:outputText  value="#{msg_aplic.prt_Finan_Lancamento_valor}" />
                </f:facet>
                <h:outputText  value="#{itemMovContaRateio.valor}" >
                    <f:converter converterId="FormatadorNumerico"/>
                </h:outputText>
            </h:column>
            
        </h:dataTable>
        <rich:datascroller align="center" for="form:itemMovContaRateio" maxPages="10"
                           id="scResultadoItemVenda" />
</h:panelGrid>

<!--Fim Rateio-->


<h:panelGrid id="panelMensagem" columns="1" width="100%" styleClass="tabMensagens">
    <h:panelGrid columns="3" width="100%" styleClass="tabMensagens">
        <h:panelGrid columns="1" width="100%">
            
                <h:outputText value=" "/>
            
        </h:panelGrid>
        <h:commandButton  rendered="#{MovContaControle.sucesso}" image="/imagens/sucesso.png"/>
        <h:commandButton rendered="#{MovContaControle.erro}" image="/imagens/erro.png"/>
        <h:panelGrid columns="1" width="100%">
            <h:outputText styleClass="mensagem"  value="#{MovContaControle.mensagem}"/>
            <h:outputText styleClass="mensagemDetalhada" value="#{MovContaControle.mensagemDetalhada}"/>
        </h:panelGrid>
    </h:panelGrid>
</h:panelGrid>
<h:panelGrid  id="panelBotoes2"  width="100%" columnClasses="colunaCentralizada" columns="1" >
    <h:panelGroup>
        <a4j:commandButton id="voltar" rendered="#{MovContaControle.mostrarBotoesTelaLancamento}"
                           action="#{MovContaControle.voltar}" value="Voltar" accesskey="1"
                           styleClass="botoes nvoBt btSec"/>
        <c:if test="${MovContaControle.foiExcluido}">
            <script>
                document.getElementById('form:voltar').click();
            </script>
        </c:if>
        <rich:spacer width="10"/>
        <a4j:commandButton id="confirmarExclusaoPagamento" reRender="panelMensagem, panelAutorizacaoFuncionalidade, formConfirmaExclusaoPararAgendamento"
                           oncomplete="#{MovContaControle.msgAlert}"
                           value="Confirmar exclusão"
                           action="#{MovContaControle.operacaoExclusao}"
                           styleClass="botoes nvoBt btPerigo">
        </a4j:commandButton>
        <rich:spacer width="10"/>

        <a4j:commandLink id="consultar" action="#{MovContaControle.realizarConsultaLogObjetoSelecionado}"
                         reRender="form"
                         rendered="#{MovContaControle.mostrarBotoesTelaLancamento}"
                         oncomplete="#{MovContaControle.oncompleteLog}"
                         style="display: inline-block; padding: 8px 15px;"
                         title="Visualizar Log" accesskey="4" styleClass="botoes nvoBt btSec">
            <i class="fa-icon-list"></i>
        </a4j:commandLink>

        <h:commandButton id="excluirDerivados"
                         action="#{MovContaControle.excluirDerivados}"
                         styleClass="botoes" style="display: none;">
        </h:commandButton>
    </h:panelGroup>
</h:panelGrid>
</h:panelGroup>
</h:panelGrid>
<!--Fim - Tela de Lançamento-->

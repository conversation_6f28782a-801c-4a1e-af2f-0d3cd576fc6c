<%@page pageEncoding="ISO-8859-1"%>
<%@include file="/pages/finan/includes/imports.jsp" %>
<link href="${root}/css/otimize.css" rel="stylesheet" type="text/css">
<rich:modalPanel id="modalAbrirCaixa" domElementAttachment="parent" autosized="true" shadowOpacity="true" width="500" height="250"
                 top="100"
                 showWhenRendered="#{FuncionalidadeControle.funcionalidadeNome eq 'ABRIR_CAIXA'}"
                 styleClass="novaModal">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText  value="Abertura de Caixa"/>
        </h:panelGroup>
    </f:facet>

    <f:facet name="controls">
        <a4j:form id="formFecharModalAbrirCaixa">
            <a4j:commandButton id="btnFecharModalAbrirCaixa"
                               style="cursor:pointer; background:none; border:none; outline:none; position: relative; right: 13px;"
                               action="#{CaixaControle.onCloseModalFromNovoFront}"
                               oncomplete="#{rich:component('modalAbrirCaixa')}.hide();"
                               reRender="@none">
                <f:facet name="label">
                    <i class="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign"></i>
                </f:facet>

            </a4j:commandButton>
        </a4j:form>
    </f:facet>

    <h:form id="formModalLanc">
        <h:panelGrid  columnClasses="direita, esquerda" cellpadding="3" columns="2" style="font-size: 11px;">
            <h:outputText value="Usuário: " styleClass="tituloCampos" />
            <h:outputText styleClass="tituloCampos" value="#{CaixaControle.usuarioLogado.username}" />

            <h:outputText styleClass="tituloCampos" value="Empresa:" />

            <h:panelGroup layout="block" styleClass="block cb-container"
                          rendered="#{LoginControle.permissaoAcessoMenuVO.abrirConsultarHistCaixaAdmTodasEmpresas}">
                <h:selectOneMenu id="empresa"
                                 style="color:black;" styleClass="form"
                                 value="#{CaixaControle.empresaSelecionada.codigo}" >
                    <f:selectItems value="#{CaixaControle.listaComboEmpresa}" />
                    <a4j:support action="#{CaixaControle.preencherListaCaixaContaPorEmpresa}"
                                 reRender="formModalLanc" event="onchange">
                    </a4j:support>
                </h:selectOneMenu>
            </h:panelGroup>

            <h:outputText styleClass="tituloCampos" value="#{CaixaControle.empresaSelecionada.nome}"
                          rendered="#{!LoginControle.permissaoAcessoMenuVO.abrirConsultarHistCaixaAdmTodasEmpresas}" />

            <h:outputText value="Data de Trabalho: " styleClass="tituloCampos" />
            <h:panelGroup styleClass="dateTimeCustom">
                <rich:calendar id="dataTrabalho" value="#{CaixaControle.dataTrabalho}" inputSize="6" inputClass="form"
                               oninputblur="blurinput(this);" oninputfocus="focusinput(this);" oninputchange="return validar_Data(this.id);"
                               datePattern="dd/MM/yyyy" enableManualInput="true" zindex="2" showWeeksBar="false"
                               buttonIcon="/imagens_flat/calendar-button.svg"/>
                <h:inputText id="autoCompleteHidden" size="3" maxlength="10" style="margin-left:6px;opacity:0;"
                             value="#{CaixaControle.usuarioLogado.username}"/>
            </h:panelGroup>
            <h:outputText value="Senha ou PIN: " styleClass="tituloCampos" />
            <h:inputSecret id="senha" autocomplete="off" redisplay="false"
                           styleClass="inputTextClean"
                           value="#{CaixaControle.senhaUsuario}" />
        </h:panelGrid>

        <h:panelGrid rendered="#{(not empty CaixaControle.listaContasAbrirCaixa) and (CaixaControle.empresaSelecionada.codigo > 0)}" width="100%"
                     styleClass="tituloCampos" columnClasses="esquerda">
            <p style="margin-bottom:6px;">
                Selecione as contas que o usuário vai operar no Caixa:
            </p>

            <div class="sep" style="margin:4px 0 10px 0;"></div>

            <rich:dataTable id="listaContas" styleClass="textsmall" width="100%" style="border-color:#FFF"
                            columnClasses="centralizado, centralizado, centralizado" rows="7"
                            headerClass="subordinado" rowClasses="linhaImpar, linhaPar"
                            value="#{CaixaControle.listaContasAbrirCaixa}" var="caixaConta">

                <rich:column style="border-color:#FFF" width="50px" >
                    <f:facet name="header">
                        <h:selectBooleanCheckbox id="selecionaTodasContas" value="#{CaixaControle.todasContasMarcadas}"
                                                 title="Selecionar todas as Contas">
                            <a4j:support event="onclick" action="#{CaixaControle.marcarDesmarcarContas}"
                                         ajaxSingle="true" reRender="panelMensagem2, listaContas" />
                        </h:selectBooleanCheckbox>
                    </f:facet>
                    <h:selectBooleanCheckbox id="conta"
                                             value="#{caixaConta.contaVo.contaEscolhida}"
                                             title="Selecionar a conta">
                        <a4j:support event="onclick" ajaxSingle="true" reRender="panelMensagem2, listaContas"/>
                    </h:selectBooleanCheckbox>
                </rich:column>

                <rich:column style="border-color:#FFF" width="200px">
                    <f:facet name="header">
                        <h:outputText style="font-weight: bold; font-size:12px;" value="Conta" />
                    </f:facet>
                    <h:outputText style="font-weight: bold; font-size:12px;" styleClass="blue"
                                  value="#{caixaConta.contaVo.descricao}" />
                </rich:column>
            </rich:dataTable>

            <rich:datascroller for="listaContas" />
            <div class="sep" style="margin:10px 0 10px 0;" ></div>

            <a4j:commandLink id="gravar"
                             styleClass="pure-button pure-button-primary"
                             action="#{CaixaControle.gravarAberturaCaixa}"
                             reRender="form:container-mask-menus"
                             oncomplete="#{CaixaControle.msgAlert}#{CaixaControle.mensagemNotificar};document.getElementById('hidelinkAbriCaxixa').click();document.querySelectorAll('.menu-ABRIR_CAIXA')[0].innerText = 'Fechar caixa';"
                             value="Gravar">
                            <%-- TODO: .querySelectorAll('.menu-ABRIR_CAIXA')[0].innerText é uma solução provisória até que seja resolvido o problema do rerender do menu explorar. Votlar aqui após desabilitar/remover o menu legado. --%>
            </a4j:commandLink>
        </h:panelGrid>

        <script>
            carregarMaskInput();
        </script>
    </h:form>
</rich:modalPanel>

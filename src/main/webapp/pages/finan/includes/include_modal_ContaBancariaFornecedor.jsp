<%@page contentType="text/html" pageEncoding="ISO-8859-1" %>
<%@include file="imports.jsp" %>
<a4j:outputPanel>
    <rich:modalPanel id="modalContaBancariaFornecedor" autosized="true" styleClass="novaModal"
                     minWidth="550" width="550" minHeight="300" height="300" top="130" shadowOpacity="true">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Cadastrar conta bancária do fornecedor"></h:outputText>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText
                        styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign "
                        id="hidelinkmodalContaBancariaFornecedor"/>
                <rich:componentControl for="modalContaBancariaFornecedor"
                                       attachTo="hidelinkmodalContaBancariaFornecedor"
                                       operation="hide" event="onclick">
                </rich:componentControl>
            </h:panelGroup>
        </f:facet>
        <h:form id="formContaBancariaFornecedor">

            <h:panelGrid width="100%"
                         columns="2" cellpadding="2" style="text-align-last: right;" id="panelGrid00"
                         columnClasses="classEsquerda, cssColunaDireita">

                <%--FORNECEDOR--%>
                <h:outputText styleClass="tituloCampos"
                              value="Fornecedor:"/>
                <h:outputText styleClass="tituloCampos"
                              style="float: left; margin-left: -6px;"
                              value="#{MovContaControle.movContaVO.pessoaVO.nome}"/>

                <%--CPF/CNPJ--%>
                <h:outputText styleClass="tituloCampos" value="CPF/CNPJ:" style="margin-top: 7px; display: block;"/>
                <h:panelGroup>
                    <h:inputText styleClass="tituloCampos tooltipster"
                                 onkeypress="return mascara(this.form, this.id, '99999999999999', event);"
                                 maxlength="18"
                                 onkeyup="somenteNumeros(this);"
                                 onblur="somenteNumeros(this);"
                                 style="display: block; width: 120px; margin-left: -7px; margin-top: 7px;"
                                 title="Informe o número do CPF ou do CNPJ da conta bancária do fornecedor"
                                 value="#{MovContaControle.movContaVO.contaBancariaFornecedorVO.cpfOuCnpj}"/>
                </h:panelGroup>

                <%--BANCO--%>
                <h:outputText styleClass="tituloCampos" value="Banco:" style="margin-top: 7px; display: block;"/>
                <h:panelGroup id="panelBancoSuggestion" style="margin-left: -8px; display: block;">
                    <%@include file="include_modal_suggestion_banco.jsp" %>
                </h:panelGroup>

            </h:panelGrid>

            <h:panelGrid width="100%"
                         columns="2" cellpadding="2" style="margin-top: -1px;" id="panelGrid01"
                         columnClasses="classEsquerda, cssColunaDireita">
                <%--AGÊNCIA--%>
                <h:outputText styleClass="tituloCampos"
                              value="Agência:"/>
                <h:panelGrid width="100%" columns="3" cellpadding="2"
                             columnClasses="classEsquerda, none, classEsquerda" id="panelGrid02">
                    <h:inputText styleClass="tituloCampos tooltipster"
                                 maxlength="5"
                                 style="margin-left: -13px; width: 113%;  margin-top: 4px;"
                                 title="Informe o número da agência bancária"
                                 value="#{MovContaControle.movContaVO.contaBancariaFornecedorVO.agency_number}"/>
                    <h:outputText styleClass="tituloCampos tooltipster"
                                  style="display: flex;"
                                  value="-"/>
                    <h:inputText styleClass="tituloCampos tooltipster"
                                 maxlength="1"
                                 style="width: 30px; margin-left: -81%; display: grid; margin-top: 4px;"
                                 title="Informe o dígito da agência bancária"
                                 value="#{MovContaControle.movContaVO.contaBancariaFornecedorVO.agency_digit}"/>
                </h:panelGrid>

                <%--CONTA--%>
                <h:outputText styleClass="tituloCampos"
                              value="Conta:"/>
                <h:panelGrid width="100%" columns="3" cellpadding="2"
                             columnClasses="classEsquerda, none, classEsquerda" id="panelGrid03">
                    <%--NOVO--%>
                    <h:inputText styleClass="tituloCampos tooltipster"
                                 maxlength="10"
                                 style="margin-left: -13px; width: 113%;"
                                 title="Informe o número da conta bancária"
                                 value="#{MovContaControle.movContaVO.contaBancariaFornecedorVO.account_number}"/>
                    <h:outputText styleClass="tituloCampos tooltipster"
                                  style="display: flex;"
                                  value="-"/>
                    <h:inputText styleClass="tituloCampos tooltipster"
                                 maxlength="1"
                                 style="width: 30px; margin-left: -81%; display: grid;"
                                 title="Informe o dígito da conta bancária"
                                 value="#{MovContaControle.movContaVO.contaBancariaFornecedorVO.account_digit}"/>
                </h:panelGrid>
            </h:panelGrid>

            <h:panelGroup id="panel01ContaForn" style="display: block; margin-top: 5px;">
                <h:panelGroup id="panel02ContaForn" style="display: ruby-text;">
                    <a4j:commandButton
                            id="btnCancelarContaBancariaFornecedorLanc"
                            action="#{MovContaControle.cancelarModalContaBancariaFornecedor}"
                            value="Cancelar"
                            styleClass="botoes nvoBt btSec"
                            oncomplete="#{MovContaControle.mensagemNotificar}#{MovContaControle.abrirFecharModalDadosContaBancariaFornecedor}"/>
                    <a4j:commandButton
                            title="Salvar conta bancária"
                            reRender="formLanc"
                            id="btnSalvarContaBancariaFornecedorLanc"
                            action="#{MovContaControle.salvarContaBancariaFornecedor}"
                            value="Salvar"
                            styleClass="botoes nvoBt tooltipster"
                            oncomplete="#{MovContaControle.mensagemNotificar}#{MovContaControle.abrirFecharModalDadosContaBancariaFornecedor}"/>
                </h:panelGroup>
            </h:panelGroup>

        </h:form>
    </rich:modalPanel>
</a4j:outputPanel>

<style>
    .cssColunaDireita {
        text-align: left;
        padding-left: 10px;
        font-size: 12px;
    }
</style>

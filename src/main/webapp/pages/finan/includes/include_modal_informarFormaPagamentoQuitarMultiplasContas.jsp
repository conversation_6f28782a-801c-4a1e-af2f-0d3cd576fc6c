<%-- HIBRAEL A ALVES - 21/10/2024 --%>
<%@page contentType="text/html" pageEncoding="ISO-8859-1" %>
<%@include file="imports.jsp" %>
<a4j:outputPanel>
    <rich:modalPanel id="modalPanelInformarFormaPagamentoMultiplasContas" autosized="true"
                     styleClass="novaModal"
                     minWidth="720" height="350" width="720" shadowOpacity="true">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Informar Forma de Pagamento"/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <a4j:form id="formFEinfFormPgt">
                <a4j:commandButton id="btnFecharModalInformarFormaPagamento" style="cursor:pointer; background:none; border:none; outline:none; position: relative; right: 13px;"
                                   action="#{MovContaControle.fecharModalInformarFormaPagamentoMultiplasContas}"
                                   oncomplete="#{rich:component('modalPanelInformarFormaPagamentoMultiplasContas')}.hide();"
                                   reRender="formAdicionarMultiplasContasConciliacao">
                    <f:facet name="label">
                        <i class="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign"></i>
                    </f:facet>

                </a4j:commandButton>
            </a4j:form>
        </f:facet>
        <h:form id="formInformarFormaPagamentoMultiplasContas">
            <h:panelGrid width="100%" columns="2" cellpadding="2" columnClasses="tituloCampos"
                         rowClasses="linhaPar, linhaImpar">

                <%-- CONTA --%>
                <h:outputText styleClass="tituloCampos tooltipster"
                              title="Todos registros/contas informados na tela anterior serão quitadas com essa Conta"
                              style="display: grid; margin-top: 9px;"
                              value="Conta:*"/>
                <h:panelGroup layout="block" styleClass="cb-container">
                    <h:selectOneMenu value="#{MovContaControle.codContaVOUtilizarMultiplasContas}"
                                     id="selectContaQuitarMultiplasContas"
                                     onblur="blurinput(this);"
                                     onfocus="focusinput(this);">

                        <f:selectItems value="#{MovContaControle.listaSelectItemContaPagamentoConciliacao}"/>
                    </h:selectOneMenu>
                </h:panelGroup>

                <%--FORMA DE PAGAMENTO--%>
                <h:panelGroup id="textFormaPgto">
                    <h:outputText styleClass="tituloCampos tooltipster"
                                  title="Todos registros/contas informadas na tela anterior serão quitadas com essa Forma de Pagamento"
                                  style="display: grid; margin-top: 6px;"
                                  value="Forma Pagamento:*"/>
                </h:panelGroup>

                <h:panelGroup layout="block" styleClass="cb-container">

                    <h:selectOneMenu value="#{MovContaControle.codFormaPgtoUtilizarMultiplasContas}"
                                     id="selectFormaPgtoMultiplasContas"
                                     onblur="blurinput(this);"
                                     onfocus="focusinput(this);">
                        <f:selectItems value="#{MovContaControle.listaSelectItemFormaPagamentoConciliacao}"/>
                    </h:selectOneMenu>
                </h:panelGroup>
            </h:panelGrid>

            <h:panelGrid columns="1" styleClass="centralizado" width="100%" style="margin-top: 6px">
                <h:panelGroup>
                    <a4j:commandButton id="btnFinalizarQuitarMultiplasContas"
                                       action="#{MovContaControle.quitarMultiplosItensConciliacao}"
                                       value="Quitar/Conciliar"
                                       reRender="form:panelDetalhesConciliacao, formAdicionarMultiplasContasConciliacao"
                                       styleClass="botoes nvoBt"
                                       style="background-color: #00c350 !important;"
                                       oncomplete="#{MovContaControle.mensagemNotificar}#{MovContaControle.msgAlert}">
                    </a4j:commandButton>

                </h:panelGroup>
            </h:panelGrid>
        </h:form>
    </rich:modalPanel>
</a4j:outputPanel>

         <a4j:outputPanel>
        <rich:modalPanel id="modalConfirmaEstornoTransferencia" styleClass="novaModal" autosized="true" width="400" height="130"  shadowOpacity="true">
            <f:facet name="header">
                <h:panelGroup>
                    <h:outputText value="Confirma��o"></h:outputText>
                </h:panelGroup>
            </f:facet>
            <f:facet name="controls">
                <h:panelGroup>
                    <h:outputText
                            styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                            id="hidelinkDep"/>
                    <rich:componentControl for="modalConfirmaEstornoTransferencia" attachTo="hidelinkDep" operation="hide" event="onclick"/>
                </h:panelGroup>
            </f:facet>
            <h:form id="formConfirmaEstornoTransferencia">
                <rich:panel>
                    <h:outputText
                                  styleClass="tituloDemonstrativo"  value="Confirma o estorno da Transfer�ncia Online?"/>
                </rich:panel>
                <center>
                    <a4j:commandButton id="sim"
                                       value="Sim"
                                       action="#{MovContaControle.estornarTransferencia}"
                                       oncomplete="Richfaces.hideModalPanel('modalConfirmaEstornoTransferencia');"
                                       reRender="form"
                                       styleClass="botoes nvoBt">
                    </a4j:commandButton>
                    <rich:spacer width="30px;"></rich:spacer>
                    <a4j:commandButton id="nao"
                                       styleClass="botoes nvoBt btSec"
                                       value="N�o"
                                       oncomplete="Richfaces.hideModalPanel('modalConfirmaEstornoTransferencia');">
					</a4j:commandButton>
              	</center>
            </h:form>
        </rich:modalPanel>
    </a4j:outputPanel>
<%-- HIBRAEL A ALVES - 21/10/2024 --%>
<%@page contentType="text/html" pageEncoding="ISO-8859-1" %>
<%@include file="imports.jsp" %>
<a4j:outputPanel>
    <rich:modalPanel id="modalPanelInformarPlanoContasCentroCusto" autosized="true"
                     styleClass="novaModal"
                     minWidth="720" height="350" width="720" shadowOpacity="true">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Informar/Alterar Plano de Contas e Centro de Custo"/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <a4j:form id="formFEInfPlCntCentCust">
                <a4j:commandButton id="btnFecharModalInformarPlanoContasCentroCusto"
                                   style="cursor:pointer; background:none; border:none; outline:none; position: relative; right: 13px;"
                                   action="#{MovContaControle.fecharModalInformarPlanoContasCentroCusto}"
                                   oncomplete="#{rich:component('modalPanelInformarPlanoContasCentroCusto')}.hide();"
                                   reRender="formInformarPlanoContasCentroCusto, formAdicionarMultiplasContasConciliacao:panelTableContasAdicionadas, formAdicionarMultiplasContasConciliacao:panelInformarNovaContaConciliacao">
                    <f:facet name="label">
                        <i class="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign"></i>
                    </f:facet>

                </a4j:commandButton>
            </a4j:form>
        </f:facet>
        <h:form id="formInformarPlanoContasCentroCusto">
            <h:panelGrid width="100%" columns="2" cellpadding="2" columnClasses="tituloCampos"
                         rowClasses="linhaPar, linhaImpar">

                <%-- DESCRIÇÃO CONTA A PAGAR--%>
                <h:outputText value="Conta A Pagar:" styleClass="tituloCampos" style="display: block; margin-top: 11px;"/>
                <h:inputText value="#{MovContaControle.movContaInformarPlanoContaCentroCusto.descricao}"
                    styleClass="tituloCampos"
                    style="width: 100%; text-align: left; height: 2.5em;"
                    size="40"
                    disabled="true"/>

                <%-- VENCIMENTO CONTA A PAGAR--%>
                <h:outputText value="Vencimento:" styleClass="tituloCampos" style="display: block; margin-top: 11px;"/>
                <h:inputText value="#{MovContaControle.movContaInformarPlanoContaCentroCusto.dataVencimento_Apresentar}"
                    styleClass="tituloCampos"
                    style="width: 100%; text-align: left; height: 2.5em;"
                    size="40"
                    disabled="true"/>

                <%-- VALOR CONTA A PAGAR--%>
                <h:outputText value="Valor:" styleClass="tituloCampos" style="display: block; margin-top: 11px;"/>
                <h:inputText value="#{MovContaControle.movContaInformarPlanoContaCentroCusto.valorApresentar}"
                    styleClass="tituloCampos"
                    style="width: 100%; text-align: left; height: 2.5em;"
                    size="40"
                    disabled="true"/>

                <%-- PLANO DE CONTAS CONTA A PAGAR--%>
                <h:outputText value="Plano de Contas:" styleClass="tituloCampos" style="display: block; margin-top: 11px;"/>
                <h:panelGroup id="planoContasAdicionarMultiplasContas">
                    <h:inputText id="nomePlanoSelecionadoRateioMultiplasContas" size="50" maxlength="50"
                                 onfocus="focusinput(this);" styleClass="form"
                                 value="#{PlanoContasControle.planoNome}">
                        <a4j:support event="onchange"
                                     action="#{PlanoContasControle.setarPlanoPaiVazio}" reRender="formLanc"/>
                    </h:inputText>

                    <rich:suggestionbox height="200" width="400"
                                        for="nomePlanoSelecionadoRateioMultiplasContas" status="statusInComponent"
                                        immediate="true"
                                        nothingLabel="Nenhum Plano de Contas encontrado"
                                        suggestionAction="#{PlanoContasControle.executarAutocompletePesqPlanoContas}"
                                        minChars="1" rowClasses="linhaImpar, linhaPar" var="result"
                                        id="suggestionPlanoContaMultiplasContas">
                        <a4j:support event="onselect" reRender="planoContasAdicionarMultiplasContas"
                                     focus="nomePlanoSelecionadoRateioMultiplasContas"
                                     action="#{PlanoContasControle.selecionarPlanoContas}">
                        </a4j:support>
                        <h:column>
                            <f:facet name="header">
                                <h:outputText value="Nome" styleClass="textverysmall"/>
                            </f:facet>
                            <h:outputText styleClass="textverysmall"
                                          value="#{result.descricaoCurta}"/>
                        </h:column>
                        <h:column>
                            <f:facet name="header">
                                <h:outputText value="Tipo" styleClass="textverysmall"/>
                            </f:facet>
                            <h:outputText styleClass="textverysmall"
                                          value="#{result.tipoPadrao.descricao}"/>
                        </h:column>
                    </rich:suggestionbox>
                </h:panelGroup>

                <%-- CENTRO DE CUSTOS CONTA A PAGAR--%>
                <h:outputText value="Centro de Custos:" styleClass="tituloCampos" style="display: block; margin-top: 11px;"/>
                <h:panelGroup id="centroCustosAdicionarMultiplasContas">
                    <h:inputText id="nomeCentroSelecionadoRateioAdicionarMultiplasContas" size="50" maxlength="50"
                                 onblur="blurinput(this);" onfocus="focusinput(this);"
                                 styleClass="form" value="#{CentroCustosControle.centroNome}">
                        <a4j:support event="onchange"
                                     action="#{CentroCustosControle.setarCentroVazio}" reRender="formLanc"/>
                    </h:inputText>

                    <rich:suggestionbox height="200" width="400"
                                        for="nomeCentroSelecionadoRateioAdicionarMultiplasContas" status="statusInComponent"
                                        immediate="true"
                                        nothingLabel="Nenhum Centro de Custos encontrado"
                                        suggestionAction="#{CentroCustosControle.executarAutocompletePesqCentroCusto}"
                                        minChars="1" rowClasses="linhaImpar, linhaPar" var="result"
                                        id="suggestionCentroCustoAdicionarMultiplasContas">
                        <a4j:support event="onselect" reRender="centroCustosAdicionarMultiplasContas" focus="rateioFormaPagto"
                                     action="#{CentroCustosControle.selecionarCentroCusto}">
                        </a4j:support>
                        <h:column>
                            <f:facet name="header">
                                <h:outputText value="Nome" styleClass="textverysmall"/>
                            </f:facet>
                            <h:outputText styleClass="textverysmall"
                                          value="#{result.descricaoCurta}"/>
                        </h:column>
                    </rich:suggestionbox>
                </h:panelGroup>
            </h:panelGrid>

            <h:panelGrid columns="1" styleClass="centralizado" width="100%" style="margin-top: 6px">
                <h:panelGroup>
                    <a4j:commandButton id="salvarAlteracoesPlanoContaCentroCusto"
                                       action="#{MovContaControle.salvarAlteracoesPlanoContaCentroCustoModalMultiplasContas}"
                                       value="Salvar Alterações"
                                       reRender="formInformarPlanoContasCentroCusto, formAdicionarMultiplasContasConciliacao:panelTableContasAdicionadas, formAdicionarMultiplasContasConciliacao:panelInformarNovaContaConciliacao "
                                       styleClass="botoes nvoBt"
                                       style="background-color: #00c350 !important;"
                                       oncomplete="#{MovContaControle.mensagemNotificar}#{MovContaControle.msgAlert}">
                    </a4j:commandButton>

                </h:panelGroup>
            </h:panelGrid>
        </h:form>
    </rich:modalPanel>
</a4j:outputPanel>

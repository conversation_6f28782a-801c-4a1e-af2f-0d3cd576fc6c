<%--/**--%>
<%--* Created with IntelliJ IDEA.--%>
<%--* User: <PERSON>--%>
<%--* Date: 20/07/2021--%>
<%--*/--%>

<%@page contentType="text/html" pageEncoding="ISO-8859-1" %>
<%@include file="imports.jsp" %>
<a4j:outputPanel>
    <rich:modalPanel id="modalPanelLancarPagamentoCon<PERSON>liacao" autosized="true"
                     styleClass="novaModal"
                     minWidth="400" height="190" width="650" shadowOpacity="true">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="#{MovContaControle.labelLancarPagamentoOuRecebimentoConc}"/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText
                        styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                        id="hidelinkPagConc"/>
                <rich:componentControl for="modalPanelLancarPagamentoConciliacao" attachTo="hidelinkPagConc"
                                       operation="hide" event="onclick"/>
            </h:panelGroup>
        </f:facet>
        <h:form id="formLancarPagamentoConciliacao">
            <h:panelGrid width="100%" columns="2" cellpadding="2" columnClasses="tituloCampos"
                         rowClasses="linhaPar, linhaImpar">

                <%--CÓD.--%>
                <h:outputText styleClass="tituloCampos"
                              value="Cód:"/>
                <h:outputText styleClass="tituloCampos tooltipster"
                              title="Código da conta que será quitada"
                              value="#{MovContaControle.pluggyTransactionQuitarConta.movContaVO.codigo}"/>

                <%--DESCRIÇÃO--%>
                <h:outputText styleClass="tituloCampos"
                              value="Descrição:"/>
                <h:outputText styleClass="tituloCampos"
                              value="#{MovContaControle.pluggyTransactionQuitarConta.movContaVO.descricao}"/>

                <%--VALOR--%>
                <h:outputText styleClass="tituloCampos"
                              style="display: block;"
                              value="Valor:"/>

                <h:panelGroup id="valor" style="margin-left: 4px; display: flex"
                              rendered="#{MovContaControle.valorDiferenteParaConciliacao}">
                    <h:outputText styleClass="tituloCampos tooltipster"
                                  style="text-decoration: line-through"
                                  value="R$ #{MovContaControle.pluggyTransactionQuitarConta.movContaVO.valorApresentar}"/>
                    <h:graphicImage value="/imagens/alert-yellow.gif"
                                    title="#{MovContaControle.msgExibirModalPagamentoConciliacao}"
                                    style="width: 20px; height: 19px; margin-left: 6px; margin-top: 2px;"
                                    styleClass="tooltipster tooltipster">
                    </h:graphicImage>
                </h:panelGroup>

                <h:panelGroup style="display: block"
                              rendered="#{!MovContaControle.valorDiferenteParaConciliacao}">
                    <h:outputText styleClass="tituloCampos"
                                  value="#{MovContaControle.pluggyTransactionQuitarConta.movContaVO.valorApresentar}"/>
                </h:panelGroup>

                <%--VENCIMENTO--%>
                <h:outputText styleClass="tituloCampos"
                              value="Vencimento:"/>
                <h:outputText styleClass="tituloCampos"
                              value="#{MovContaControle.pluggyTransactionQuitarConta.movContaVO.dataVencimento_Apresentar}"/>

                <%--CONTA--%>
                <h:outputText styleClass="tituloCampos"
                              style="display: grid; margin-top: 9px;"
                              value="Conta:*"/>
                <h:panelGroup layout="block" styleClass="cb-container">
                    <h:selectOneMenu value="#{MovContaControle.pluggyTransactionQuitarConta.movContaVO.contaVO.codigo}"
                                     id="conta"
                                     onblur="blurinput(this);"
                                     onfocus="focusinput(this);">

                        <f:selectItems value="#{MovContaControle.listaSelectItemContaPagamentoConciliacao}"/>
                    </h:selectOneMenu>
                </h:panelGroup>

                <%--DATA QUITAÇÃO--%>
                <h:outputText styleClass="tituloCampos tooltipster"
                              title="Não é possível alterar a data de quitação. Ela será a mesma data do lançamento do seu extrato bancário"
                              style="display: grid; margin-top: 6px;"
                              value="Data Quitação:"/>
                <h:panelGroup style="display: grid; grid-template-columns: 20% 4%">
                    <h:inputText id="dataQuitacao"
                                 disabled="true"
                                 styleClass="form"
                                 value="#{MovContaControle.pluggyTransactionQuitarConta.dataApresentar}"/>

                    <h:panelGroup style="margin-top: 6px; margin-left: 4px;">
                        <i class="fa-icon-question-sign tooltipster"
                           style="font-size: 18px;"
                           title="Não é possível alterar a data de quitação. Ela será a mesma data do lançamento do seu extrato bancário"
                    </h:panelGroup>
                </h:panelGroup>


                <%--VALOR FINAL--%>
                <h:outputText styleClass="tituloCampos"
                              value="Valor Final:"/>

                <h:panelGroup id="valorFinal" style="margin-left: 4px;">
                    <h:panelGroup>
                        <h:outputText styleClass="tituloCampos"
                                      value="#{MovContaControle.pluggyTransactionQuitarConta.valorApresentarSemSinal}"/>
                    </h:panelGroup>

                    <h:panelGroup style="margin-top: 6px; margin-left: 4px;">
                        <i class="fa-icon-question-sign tooltipster"
                           style="font-size: 18px;"
                           title="Valor final a ser conciliado."></i>
                    </h:panelGroup>
                </h:panelGroup>

                <%--FORMA DE PAGAMENTO--%>
                <h:panelGroup id="textFormaPgto">
                    <h:outputText styleClass="tituloCampos"
                                  style="display: grid; margin-top: 6px;"
                                  value="Forma Pagamento:*"/>
                </h:panelGroup>

                <h:panelGroup layout="block" styleClass="cb-container">

                    <h:selectOneMenu value="#{MovContaControle.codFormaPgtoQuitacaoConciliacao}"
                                     id="selecionarFormaPGTO"
                                     onblur="blurinput(this);"
                                     onfocus="focusinput(this);">
                        <f:selectItems value="#{MovContaControle.listaSelectItemFormaPagamentoConciliacao}"/>
                    </h:selectOneMenu>
                </h:panelGroup>


                <%--PLANO DE CONTAS--%>
                <h:outputText styleClass="tituloCampos tooltipster"
                              style="display: block;"
                              value="Plano de Contas:"/>
                <h:panelGroup id="planoContasQuitarConta">
                    <h:inputText id="nomePlanoSelecionadoRateioQuitarConta" size="50" maxlength="50"
                                 onfocus="focusinput(this);" styleClass="form"
                                 value="#{PlanoContasControle.planoNome}">
                        <a4j:support event="onchange"
                                     action="#{PlanoContasControle.setarPlanoPaiVazio}" reRender="formLanc"/>
                    </h:inputText>

                    <rich:suggestionbox height="200" width="400"
                                        for="nomePlanoSelecionadoRateioQuitarConta" status="statusInComponent"
                                        immediate="true"
                                        nothingLabel="Nenhum Plano de Contas encontrado"
                                        suggestionAction="#{PlanoContasControle.executarAutocompletePesqPlanoContas}"
                                        minChars="1" rowClasses="linhaImpar, linhaPar" var="result"
                                        id="suggestionResponsavelRateioQuitar">
                        <a4j:support event="onselect" reRender="planoContasCriarNovaConta"
                                     focus="nomeCentroSelecionadoRateio"
                                     action="#{PlanoContasControle.selecionarPlanoContas}">
                        </a4j:support>
                        <h:column>
                            <f:facet name="header">
                                <h:outputText value="Nome" styleClass="textverysmall"/>
                            </f:facet>
                            <h:outputText styleClass="textverysmall"
                                          value="#{result.descricaoCurta}"/>
                        </h:column>
                        <h:column>
                            <f:facet name="header">
                                <h:outputText value="Tipo" styleClass="textverysmall"/>
                            </f:facet>
                            <h:outputText styleClass="textverysmall"
                                          value="#{result.tipoPadrao.descricao}"/>
                        </h:column>
                    </rich:suggestionbox>
                </h:panelGroup>


                <%--CENTRO DE CUSTOS--%>
                <h:outputText styleClass="tituloCampos tooltipster"
                              style="display: block;"
                              value="Centro de Custo:"/>
                <h:panelGroup id="centroCustosQuitarConta">
                    <h:inputText id="nomeCentroSelecionadoRateioQuitarConta" size="50" maxlength="50"
                                 onblur="blurinput(this);" onfocus="focusinput(this);"
                                 styleClass="form" value="#{CentroCustosControle.centroNome}">
                        <a4j:support event="onchange"
                                     action="#{CentroCustosControle.setarCentroVazio}" reRender="formLanc"/>
                    </h:inputText>

                    <rich:suggestionbox height="200" width="400"
                                        for="nomeCentroSelecionadoRateioQuitarConta" status="statusInComponent"
                                        immediate="true"
                                        nothingLabel="Nenhum Centro de Custos encontrado"
                                        suggestionAction="#{CentroCustosControle.executarAutocompletePesqCentroCusto}"
                                        minChars="1" rowClasses="linhaImpar, linhaPar" var="result"
                                        id="suggestionCentroCustoRateioQuitar">
                        <a4j:support event="onselect" reRender="centroCustosCriarNovaConta" focus="rateioFormaPagto"
                                     action="#{CentroCustosControle.selecionarCentroCusto}">
                        </a4j:support>
                        <h:column>
                            <f:facet name="header">
                                <h:outputText value="Nome" styleClass="textverysmall"/>
                            </f:facet>
                            <h:outputText styleClass="textverysmall"
                                          value="#{result.descricaoCurta}"/>
                        </h:column>
                    </rich:suggestionbox>
                </h:panelGroup>


            </h:panelGrid>

            <%--BOTÕES--%>
            <h:panelGrid columns="1" styleClass="centralizado" width="100%">
                <h:panelGroup>
                    <a4j:commandButton id="confirmacao"
                                       action="#{MovContaControle.quitarItemConciliacao}"
                                       value="Quitar/Conciliar"
                                       reRender="form:panelGroupAtualizarLancarPagamento"
                                       styleClass="botoes nvoBt"
                                       style="background-color: #00c350 !important;"
                                       oncomplete="#{MovContaControle.msgAlert}#{MovContaControle.mensagemNotificar}">
                    </a4j:commandButton>
                </h:panelGroup>
            </h:panelGrid>
        </h:form>
    </rich:modalPanel>
</a4j:outputPanel>

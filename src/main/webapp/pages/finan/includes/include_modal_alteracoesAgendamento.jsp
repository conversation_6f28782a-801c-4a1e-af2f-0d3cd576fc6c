<%@page pageEncoding="ISO-8859-1" %>
<a4j:outputPanel>
    <rich:modalPanel id="modalAlteracoesAgendamento" autosized="false" width="400" height="200" shadowOpacity="true" styleClass="novaModal">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText rendered="#{!MovContaControle.gravarAntesQuitar}" value="Confirmação"/>
                <h:outputText rendered="#{MovContaControle.gravarAntesQuitar}" value="Atenção!"/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText
                        styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                        id="hidelinkAlteracoesAgendamento"/>
                <rich:componentControl for="modalAlteracoesAgendamento" attachTo="hidelinkAlteracoesAgendamento"
                                       operation="hide" event="onclick"/>
            </h:panelGroup>
        </f:facet>


        <h:form id="formAlteracoesAgendamento">
            <h:panelGroup style="font-size: 16px" layout="block">
                <h:outputText value="#{MovContaControle.mensagemAlteracao} "/>
                <h:outputText  rendered="#{!MovContaControle.gravarAntesQuitar}"
                              value="#{msg_aplic.prt_Finan_Lancamentos_alteracao}"/>
                <h:outputText rendered="#{MovContaControle.gravarAntesQuitar}"
                              value="#{msg_aplic.prt_Finan_Lancamentos_alteracaoAntesQuitar}"/>
            </h:panelGroup>
            <center>
                <h:panelGroup rendered="#{!MovContaControle.gravarAntesQuitar}">
                    <a4j:commandButton id="sim"  styleClass="botoes nvoBt"
                                       action="#{MovContaControle.realizarAlteracoesTodasParcelas}"
                                       oncomplete="#{MovContaControle.mensagemNotificar};Richfaces.hideModalPanel('modalAlteracoesAgendamento')"
                                       reRender="formLanc:panelMensagem, formLanc:tabelaGeral, panelMensagem"
                                       value="Sim">
                    </a4j:commandButton>
                    <rich:spacer width="30px;"/>
                    <a4j:commandButton id="nao"  styleClass="botoes nvoBt btSec"
                                       action="#{MovContaControle.operacaoLancamento}"
                                       reRender="formLanc:panelMensagem, formLanc:tabelaGeral, panelMensagem"
                                       value="Não"
                                       oncomplete="#{MovContaControle.mensagemNotificar};Richfaces.hideModalPanel('modalAlteracoesAgendamento')">
                    </a4j:commandButton>
                </h:panelGroup>
                <h:panelGroup rendered="#{MovContaControle.gravarAntesQuitar}">
                    <a4j:commandButton id="OK" reRender="modalAlteracoesAgendamento"
                                       styleClass="botoes nvoBt" value="Ok"
                                       action="Richfaces.hideModalPanel('modalAlteracoesAgendamento');">
                    </a4j:commandButton>
                </h:panelGroup>
            </center>
        </h:form>
    </rich:modalPanel>
</a4j:outputPanel>

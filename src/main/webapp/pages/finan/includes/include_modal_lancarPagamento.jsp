<%@page contentType="text/html" pageEncoding="ISO-8859-1" %>
<%@include file="imports.jsp" %>
<a4j:outputPanel>
        <rich:modalPanel id="modalPanelLancarPagamento" autosized="true"
        				minWidth="400" height="190" width="600" top="100"  shadowOpacity="true">
            <f:facet name="header">
                <h:panelGroup>
                    <h:outputText value="#{MovContaControle.tituloModalQuitar}"></h:outputText>
                </h:panelGroup>
            </f:facet>
            <f:facet name="controls">
            </f:facet>
            <h:form id="formLancarPagamento">
                <h:panelGrid width="100%" columns="2" cellpadding="2" columnClasses="tituloCampos" rowClasses="linhaPar, linhaImpar">
                	<h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Finan_Lancamento_descricaoQuitacao}"></h:outputText>
                	<h:outputText styleClass="tituloCampos" rendered="#{MovContaControle.contaQuitacao.pagamentoSimples}" 
                				  value="#{MovContaControle.contaQuitacao.descricao}"></h:outputText>
                	<h:outputText styleClass="tituloCampos" rendered="#{!MovContaControle.contaQuitacao.pagamentoSimples}" 
                				  value="#{msg_aplic.prt_finan_pagar_conjunto_lbl}"></h:outputText>
                				  			  
                	<h:outputText styleClass="tituloCampos"  value="#{msg_aplic.prt_Finan_Lancamento_valorQuitacao}"></h:outputText>
                	<h:outputText styleClass="tituloCampos"  value="#{MovContaControle.contaQuitacao.valor_Apresentar}"></h:outputText>


					<h:outputText styleClass="tituloCampos" rendered="#{!MovContaControle.contaQuitacao.pagamentoSimples}"
								  value="Lançamentos selecionados:"></h:outputText>
					<h:outputText styleClass="tituloCampos" rendered="#{!MovContaControle.contaQuitacao.pagamentoSimples}"
								  value="#{fn:length(MovContaControle.contaQuitacao.contasAPagarConjunto)}"></h:outputText>

                	<h:outputText styleClass="tituloCampos" rendered="#{MovContaControle.contaQuitacao.pagamentoSimples}" value="#{msg_aplic.prt_Finan_Lancamento_vencimentoQuitacao}"></h:outputText>
                	<h:outputText styleClass="tituloCampos" rendered="#{MovContaControle.contaQuitacao.pagamentoSimples}" value="#{MovContaControle.contaQuitacao.dataVencimento_Apresentar}"></h:outputText>
                	
                        <h:outputText styleClass="tituloCampos" value="Conta:" rendered="#{MovContaControle.usarMovimentacao}"></h:outputText>
                        <h:selectOneMenu value="#{MovContaControle.contaQuitacao.contaVO.codigo}"
                                                         id="conta"
                         				 rendered="#{MovContaControle.usarMovimentacao}"
                                         onblur="blurinput(this);"
                                         onfocus="focusinput(this);"
                                         style="color:black;"
                                         styleClass="form">

                            <f:selectItems value="#{MovContaControle.listaComboContaQuitacao}"></f:selectItems>
                            <a4j:support event="onchange"
                            			 action="#{MovContaControle.selecionarConta}"
                            			 reRender="formLancarPagamento"
                            			 oncomplete="#{MovContaControle.msgAlert}"></a4j:support>
                        </h:selectOneMenu>

                        <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Finan_Lancamento_pagamentoQuitacao}"></h:outputText>
                        <h:panelGroup>
                           <h:inputText id="dataQuitacao"
                                         styleClass="form"
                                         onkeypress="return mascara2(this.form, this.id, '99/99/9999 - 99:99:99', event);"
                                         onchange="validar_Data_horas_minutos(this.id);"
                                         value="#{MovContaControle.contaQuitacao.dataQuitacao_Apresentar}"></h:inputText>

							<rich:calendar id="dataQuitacaoCalendar"
										   value="#{MovContaControle.dataQuitacaoCalendario}"
										   direction="bottom-left"
										   inputSize="10"
										   styleClass="tituloboxcentro2"
										   showInput="false"
										   inputClass="form"
										   datePattern="dd/MM/yyyy"
										   zindex="2"
										   showWeeksBar="false">
								<a4j:support event="onchanged" reRender="dataQuitacao"
											 action="#{MovContaControle.calendarioQuitacao}" />
							</rich:calendar>
					</h:panelGroup>

<%--					Um cliente pediu para tirar o recurso do AlterData em PagamentoConjunto.--%>
<%--					Em 8 anos, não sabia da existência desse recurso.--%>
<%--					Como não sei se outro cliente usa isso, no momento vou comentar e ver se alguém vai reclamar no futuro.--%>
<%--					<%@include file="include_SuggestionContaContabilCreditoValor.jsp" %>--%>
<%--					<%@include file="include_SuggestionContaContabilDebitoValor.jsp" %>--%>

					<h:outputText styleClass="tituloCampos" value="Adicionar Multa:" rendered="#{MovContaControle.integracaoContabilAlterData}"></h:outputText>
					<h:selectBooleanCheckbox rendered="#{MovContaControle.integracaoContabilAlterData}" value="#{MovContaControle.informarMulta}" id="multa">
						<a4j:support event="onchange"
									 action="#{MovContaControle.selecionarInformarMulta}"
									 reRender="formLancarPagamento:pgLabelaTotalLancamento,formLancarPagamento:pgInputTotalLancamento,formLancarPagamento:pgLabelCreditoMulta,formLancarPagamento:sugestionCreditoMulta,formLancarPagamento:labelDebitoMulta,formLancarPagamento:sugestionDebitoMulta,formLancarPagamento:labelValorMulta,formLancarPagamento:pgValorMulta,formLancarPagamento:tableFomaPagto" ></a4j:support>
					</h:selectBooleanCheckbox>
					<h:panelGroup layout="block" id="labelValorMulta">
					  <h:outputText rendered="#{MovContaControle.integracaoContabilAlterData && MovContaControle.informarMulta}" styleClass="tituloCampos"  value="Valor multa:" />
					</h:panelGroup>
					<h:panelGroup layout="block" id="pgValorMulta">
						<h:inputText  onkeypress="return Tecla(event);" size="20" maxlength="10" onblur="blurinput(this);"
									  rendered="#{MovContaControle.integracaoContabilAlterData && MovContaControle.informarMulta}"
									  id="idValorMulta"
									  onfocus="focusinput(this);" styleClass="form"
									  onkeyup="return moeda(this);"
									  value="#{MovContaControle.movContaContabilVO.valorMulta}" >
							<f:converter converterId="FormatadorNumerico" />
							<a4j:support event="onchange" reRender="tableFomaPagto"/>
						</h:inputText>
					</h:panelGroup>
					<%@include file="include_SuggestionContaContabilCreditoMulta.jsp" %>
					<%@include file="include_SuggestionContaContabilDebitoMulta.jsp" %>
					<h:outputText styleClass="tituloCampos" value="Adicionar Juro:" rendered="#{MovContaControle.integracaoContabilAlterData}"></h:outputText>
					<h:selectBooleanCheckbox rendered="#{MovContaControle.integracaoContabilAlterData}" value="#{MovContaControle.informarJuro}" id="juro">
						<a4j:support event="onchange"
									 action="#{MovContaControle.selecionarInformarJuro}"
									 reRender="formLancarPagamento:pgLabelaTotalLancamento,formLancarPagamento:pgInputTotalLancamento,formLancarPagamento:pgLabelCreditoJuro, formLancarPagamento:sugestionCreditoJuro, formLancarPagamento:labelDebitoJuro,formLancarPagamento:sugestionDebitoJuro,formLancarPagamento:labelValorJuro,formLancarPagamento:pgValorJuro, formLancarPagamento:tableFomaPagto" ></a4j:support>
					</h:selectBooleanCheckbox>
					<h:panelGroup layout="block" id="labelValorJuro">
						<h:outputText styleClass="tituloCampos"  rendered="#{MovContaControle.integracaoContabilAlterData && MovContaControle.informarJuro}" value="Valor Juro:" />
					</h:panelGroup>
					<h:panelGroup layout="block" id="pgValorJuro">
						<h:inputText  onkeypress="return Tecla(event);" size="20" maxlength="10" onblur="blurinput(this);"
									  rendered="#{MovContaControle.integracaoContabilAlterData && MovContaControle.informarJuro}"
									  id="idValorJuro"
									  onkeyup="return moeda(this);"
									  onfocus="focusinput(this);" styleClass="form"
									  value="#{MovContaControle.movContaContabilVO.valorJuro}" >
							<f:converter converterId="FormatadorNumerico" />
							<a4j:support event="onchange" reRender="tableFomaPagto"/>
						</h:inputText>
					</h:panelGroup>
					<%@include file="include_SuggestionContaContabilCreditoJuro.jsp" %>
					<%@include file="include_SuggestionContaContabilDebitoJuro.jsp" %>



                </h:panelGrid>
					<rich:dataTable id="tableFomaPagto" value="#{MovContaControle.formasPagamentoQuitacao}"  var="formaOffline" width="100%">
                        <rich:column>
                        
                           <h:panelGrid width="100%" columns="2" columnClasses="colunaDireita,colunaEsquerda" rowClasses="linhaPar, linhaImpar">
                        	<h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Finan_Lancamentos_formaPg}"></h:outputText>
                        	
                        	<h:panelGrid width="100%" columns="2" columnClasses="colunaEsquerda, colunaDireita" cellpadding="0" cellspacing="0" >
                    			
                    			<h:selectOneMenu value="#{formaOffline.formaPagamento.codigo}"
                                             id="selecionarFormaPGTO"
                                             onblur="blurinput(this);"
                                             onfocus="focusinput(this);"
                                             style="color:black;"
                                             styleClass="form">
                                <f:selectItems value="#{MovContaControle.listaComboFormaPagto}"></f:selectItems>
                                
                                <a4j:support action="#{MovContaControle.selecionarFormaPagamentoOffline}" reRender="formLancarPagamento"
                            			 	 event="onchange"
                            			 	 oncomplete="#{MovContaControle.msgAlert}"></a4j:support>
                                
                            	</h:selectOneMenu>
                    			<a4j:commandButton image="/images/desfazer_verde.png" style="width: 20px; height: 20px;"
                    							   title="Desfazer alterações"
                    							   reRender="formLancarPagamento"
                    							   action="#{MovContaControle.desfazerAlteracoes}"
                    							   rendered="#{MovContaControle.contaQuitacao.pagamentoSimples}">
                    			</a4j:commandButton>
                    		</h:panelGrid>


							<h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Finan_Lancamento_valorQuitacao}"></h:outputText>

						   <h:panelGroup>
								<h:outputText styleClass="tituloCampos" rendered="#{!formaOffline.editarValor}" value="#{formaOffline.valorFormatado}"></h:outputText>
								<h:panelGroup>
									<h:inputText id="idValorLancamentoQuitacao" styleClass="form" rendered="#{formaOffline.editarValor}" value="#{formaOffline.valor}">
										<f:converter converterId="FormatadorNumerico" />
										<a4j:support event="onblur" action="#{MovContaControle.verificarEscolhas}" reRender="formLancarPagamento, tableFomaPagto">
										</a4j:support>
									</h:inputText>
								</h:panelGroup>
						   </h:panelGroup>

						   <h:panelGroup rendered="#{MovContaControle.integracaoContabilAlterData}" id="pgLabelaTotalLancamento">
							   <h:outputText styleClass="tituloCampos"
											 value="Valor total:"
											 rendered="#{MovContaControle.integracaoContabilAlterData}"></h:outputText>
						   </h:panelGroup>

						   <h:panelGroup rendered="#{MovContaControle.integracaoContabilAlterData}" id="pgInputTotalLancamento">
							   <h:outputText styleClass="tituloCampos"
											 value="#{MovContaControle.valorTotalMovContaContabil_Apresentar}"
											 rendered="#{MovContaControle.integracaoContabilAlterData}"></h:outputText>
						   </h:panelGroup>


                            <h:outputText styleClass="tituloCampos" value="Lote: " rendered="#{formaOffline.pagarComLote}"></h:outputText>
                	    	<h:panelGroup rendered="#{formaOffline.pagarComLote}">
									<h:inputText id="lote" size="40" maxlength="40"
												onfocus="focusinput(this);" styleClass="form" value="#{formaOffline.lote.descricao}">
									 </h:inputText>
									 <rich:suggestionbox height="200" width="700"
													for="lote"
													fetchValue="#{result}"
													suggestionAction="#{MovContaControle.executarAutocompleteConsultaLote}"
													minChars="1" rowClasses="20"
													status="statusInComponent"
													nothingLabel="Nenhum lote encontrado!"
													var="result" id="suggestionLote">
									<a4j:support event="onselect"
												 action="#{MovContaControle.selecionarLoteSuggestionBox}"
												 reRender="formLancarPagamento"
												 oncomplete="#{MovContaControle.msgAlert}"/>

									<h:column>
									 <h:outputText value="#{result.codigo}"/>
								 </h:column>

									<h:column>
										<h:outputText value="#{result.descricao}"/>
									</h:column>

									<h:column>
										<h:outputText value="#{result.dataLancamento_Apresentar}"/>
									</h:column>
									 <h:column>
										 <h:outputText value="#{result.contaLote}"/>
									 </h:column>
									<h:column>
										<h:outputText value="#{result.valor_Apresentar}"/>
									</h:column>

									</rich:suggestionbox>
						    </h:panelGroup>
                           
                           
                           </h:panelGrid>
                           
                           <center>
                           		<a4j:commandButton reRender="panelCheque" value="Cheque" oncomplete="Richfaces.showModalPanel('panelCheque')"
                                               style="vertical-align:middle"
                                               image="/imagens/cheque.png"
                                               rendered="#{formaOffline.pagarComCheque && empty formaOffline.cheques}"
                                               action="#{MovContaControle.abrirModalCheques}"></a4j:commandButton>
                           </center>
                           
                           
                           <rich:dataTable id="chequeslista"
                               rowClasses="tablelistras" width="600px"
                               style="border:none;" columnClasses="colunaEsquerda"
                               rendered="#{not empty formaOffline.cheques}"
                               value="#{formaOffline.cheques}" var="cheque">

		                   <rich:column>
		                       <f:facet name="header">
		                           <h:outputText value="Banco" styleClass="textsmall" />
		                       </f:facet>
		                       <h:selectOneMenu id="bancoPreenchido"
												converter="simpleIndexConverter"
		                                        style="width:150px;" value="#{ChequeControle.bancoSelected}"
		                                        onblur="blurinput(this);" onfocus="focusinput(this);"
		                                        styleClass="form">
		                           <f:selectItems
		                               value="#{ChequeControle.listaSelectItemBanco}" />
		                       </h:selectOneMenu>
		                   </rich:column>
		
		                   <rich:column>
		                       <f:facet name="header">
		                           <h:outputText value="Agência" styleClass="textsmall" />
		                       </f:facet>
		                       <h:inputText id="agencia" value="#{cheque.agencia}"
		                                    size="4" maxlength="6" onblur="blurinput(this);"
		                                    onfocus="focusinput(this);" styleClass="form"/>
		                   </rich:column>
		
		                   <rich:column>
		                       <f:facet name="header">
		                           <h:outputText value="Conta" styleClass="textsmall" />
		                       </f:facet>
		                       <h:inputText id="conta" value="#{cheque.conta}"
		                                    size="6" maxlength="11" onblur="blurinput(this);"
		                                    onfocus="focusinput(this);" styleClass="form"/>
		                   </rich:column>
		
		                   <rich:column>
		                       <f:facet name="header">
		                           <h:outputText value="Cheque" styleClass="textsmall" />
		                       </f:facet>
		                       <h:inputText id="nDoc" value="#{cheque.numero}"
		                                    size="5" maxlength="10" onblur="blurinput(this);"
		                                    onfocus="focusinput(this);" styleClass="form"/>
		                   </rich:column>
		
		                   <rich:column>
		                       <f:facet name="header">
		                           <h:outputText value="Compensação"
		                                         styleClass="textsmall" />
		                       </f:facet>
		                       <h:panelGroup id="dataCheque">
		                           <rich:calendar id="dataCompensacao"
		                                          value="#{cheque.dataCompensacao}" inputSize="7"
		                                          inputClass="form" oninputblur="blurinput(this);"
		                                          oninputfocus="focusinput(this);"
		                                          oninputchange="return validar_Data(this.id);"
		                                          oninputkeypress="return mascara(this.form, this.id, '99/99/9999', event);"
		                                          datePattern="dd/MM/yyyy" enableManualInput="true"
		                                          showWeeksBar="false" />
		                           <h:message for="dataCompensacao"
		                                      styleClass="mensagemDetalhada" />
		                       </h:panelGroup>
		                   </rich:column>
		
		                   <rich:column>
		                       <f:facet name="header">
		                           <h:outputText value="Valor" styleClass="textsmall" />
		                       </f:facet>
		                       <h:inputText id="valor" value="#{cheque.valor}"
		                                    size="5" maxlength="10"
		                                    onfocus="focusinput(this);" styleClass="form">
		                           <f:converter converterId="FormatadorNumerico" />
		                           <a4j:support event="onblur" action="#{MovContaControle.valorCheque}" reRender="formLancarPagamento">
		                           </a4j:support>
		                       </h:inputText>
		                   </rich:column>
		
		                   <rich:column>
		                       <f:facet name="header">
		                           <h:outputText value="Op." styleClass="textsmall" />
		                       </f:facet>
		                       
		
		                       <a4j:commandButton id="btnRemoverCheque" value="remover"
		                                          action="#{MovContaControle.removerCheque}"
		                                          reRender="formLancarPagamento"
		                                          image="../../../images/icon_delete.gif"
		                                          title="Excluir Cheque" />
		
		                   </rich:column>
		               </rich:dataTable>
                           		
                           </rich:column>

                    </rich:dataTable>
					
					<h:panelGrid rendered="#{(MovContaControle.exibirMaior || MovContaControle.exibirMenor || MovContaControle.exibirParcial)
                                                     && MovContaControle.contaQuitacao.pagamentoSimples}">
						<h:outputText styleClass="mensagemDetalhada" style="font-size: 10pt;"
								      value="#{msg_aplic.prt_Finan_Questao}"></h:outputText>
					</h:panelGrid>

                                        <h:panelGrid rendered="#{(MovContaControle.exibirMaior || MovContaControle.exibirMenor || MovContaControle.exibirParcial)
                                                     && !MovContaControle.contaQuitacao.pagamentoSimples}">
						<h:outputText styleClass="mensagemDetalhada" style="font-size: 10pt;"
								      value="#{msg_aplic.prt_pagamentoconjuntodiferentevalor}"></h:outputText>
					</h:panelGrid>

					<h:panelGroup layout="block" id="opcoes"
								  rendered="#{MovContaControle.contaQuitacao.pagamentoSimples}">
						
						<h:selectBooleanCheckbox value="#{MovContaControle.pagamentoMaior}"
												 id="maiorCheck"
												 rendered="#{MovContaControle.exibirMaior}">
							<a4j:support actionListener="#{MovContaControle.marcarOpcao}" 
										 event="onclick"
										 reRender="formLancarPagamento">
										 <f:attribute name="codigoOpcao" value="MAIOR"></f:attribute>
										 </a4j:support>
						</h:selectBooleanCheckbox>
						<h:outputText styleClass="tituloCampos tooltipster"
									  rendered="#{MovContaControle.exibirMaior}"
									  title="#{msg.msg_quitacaoPagamentoMaior}"
									  value="#{msg_aplic.prt_Finan_pagamentoMaior}"/>

						<br/>
						<h:panelGroup layout="block" id="definirPlanoContaCentroCustoValorSuperiorGeral"
									  rendered="#{MovContaControle.exibirMaior && MovContaControle.pagamentoMaior}">

							<h:panelGroup layout="block"
										  id="definirPlanoContaCentroCustoValorSuperiorGroup1">
								<h:selectBooleanCheckbox id="definirPlanoContaCentroCustoValorSuperior"
														 value="#{MovContaControle.contaQuitacao.definirPlanoContaCentroCustoValorSuperior}">
									<a4j:support event="onclick" reRender="definirPlanoContaCentroCustoValorSuperiorGeral"/>
								</h:selectBooleanCheckbox>
								<h:outputText styleClass="tituloCampos"
											  value="Definir plano de conta e centro de custo para valor superior"/>
							</h:panelGroup>

							<h:panelGroup layout="block"
										  id="definirPlanoContaCentroCustoValorSuperiorGroup2"
										  style="padding-top: 10px"
										  rendered="#{MovContaControle.contaQuitacao.definirPlanoContaCentroCustoValorSuperior}">
								<h:outputText styleClass="tituloCampos"
											  value="#{msg_aplic.prt_Finan_Lancamento_planoContas}"/>
								<br/>
								<h:inputText id="nomePlanoSelecionadoRateio" size="50" maxlength="50"
											 onfocus="focusinput(this);" styleClass="form"
											 value="#{PlanoContasControle.planoNome}">
									<a4j:support event="onchange"
												 action="#{PlanoContasControle.setarPlanoPaiVazio}"
												 reRender="formLanc"/>
								</h:inputText>

								<rich:suggestionbox height="200" width="400"
													for="nomePlanoSelecionadoRateio"
													status="statusInComponent"
													immediate="true"
													nothingLabel="Nenhum Plano de Contas encontrado"
													suggestionAction="#{PlanoContasControle.executarAutocompletePesqPlanoContas}"
													minChars="1" rowClasses="linhaImpar, linhaPar"
													var="result"
													id="suggestionResponsavelRateio">
									<a4j:support event="onselect" reRender="formLanc"
												 focus="nomeCentroSelecionadoRateio"
												 action="#{PlanoContasControle.selecionarPlanoContas}">
									</a4j:support>
									<h:column>
										<f:facet name="header">
											<h:outputText value="Nome" styleClass="textverysmall"/>
										</f:facet>
										<h:outputText styleClass="textverysmall"
													  value="#{result.descricaoCurta}"/>
									</h:column>
									<h:column>
										<f:facet name="header">
											<h:outputText value="Tipo" styleClass="textverysmall"/>
										</f:facet>
										<h:outputText styleClass="textverysmall"
													  value="#{result.tipoPadrao.descricao}"/>
									</h:column>
								</rich:suggestionbox>
								<a4j:commandLink
										action="#{PlanoContasControle.verificarConsultaCorretaRateio}"
										reRender="modalPlanos"
										id="btAddPlanoRateio" value="Consultar"
										oncomplete="Richfaces.showModalPanel('modalPlanos')"/>
							</h:panelGroup>

							<h:panelGroup layout="block"
										  id="definirPlanoContaCentroCustoValorSuperiorGroup3"
										  style="padding-top: 10px; padding-bottom: 10px"
										  rendered="#{MovContaControle.contaQuitacao.definirPlanoContaCentroCustoValorSuperior}">
								<h:outputText styleClass="tituloCampos"
											  value="#{msg_aplic.prt_Finan_Lancamento_centroCusto}"/>
								<br/>
								<h:inputText id="nomeCentroSelecionadoRateio" size="50" maxlength="50"
											 onblur="blurinput(this);" onfocus="focusinput(this);"
											 styleClass="form" value="#{CentroCustosControle.centroNome}">
									<a4j:support event="onchange"
												 action="#{CentroCustosControle.setarCentroVazio}"
												 reRender="formLanc"/>
								</h:inputText>

								<rich:suggestionbox height="200" width="400"
													for="nomeCentroSelecionadoRateio"
													status="statusInComponent"
													immediate="true"
													nothingLabel="Nenhum Centro de Custos encontrado"
													suggestionAction="#{CentroCustosControle.executarAutocompletePesqCentroCusto}"
													minChars="1" rowClasses="linhaImpar, linhaPar"
													var="result"
													id="suggestionCentroCustoRateio">
									<a4j:support event="onselect" reRender="formLanc"
												 focus="rateioFormaPagto"
												 action="#{CentroCustosControle.selecionarCentroCusto}">
									</a4j:support>
									<h:column>
										<f:facet name="header">
											<h:outputText value="Nome" styleClass="textverysmall"/>
										</f:facet>
										<h:outputText styleClass="textverysmall"
													  value="#{result.descricaoCurta}"/>
									</h:column>
								</rich:suggestionbox>
								<a4j:commandLink action="#{CentroCustosControle.loadTree}"
												 reRender="modalCentros"
												 id="btAddCentroRateio" value="Consultar"
												 oncomplete="Richfaces.showModalPanel('modalCentros')"/>
							</h:panelGroup>
						</h:panelGroup>


						<h:selectBooleanCheckbox  value="#{MovContaControle.pagamentoMenor}" id="menorCheck"
										 rendered="#{MovContaControle.exibirMenor}">
							<a4j:support actionListener="#{MovContaControle.marcarOpcao}" 
										 event="onclick"
										 reRender="formLancarPagamento">
										 <f:attribute name="codigoOpcao" value="MENOR"></f:attribute>
										 </a4j:support>
						</h:selectBooleanCheckbox>
						<rich:toolTip for="menorCheck" rendered="#{MovContaControle.exibirMenor}"
                                			  followMouse="true" direction="top-right"  style="width:200px;height: 90px;">
                                		<h:outputText styleClass="tituloCampos" 
                                					  value="#{msg.msg_quitacaoPagamentoMenor}"></h:outputText>
                                </rich:toolTip> 
                                 
						<h:outputText styleClass="tituloCampos" rendered="#{MovContaControle.exibirMenor}" value="#{msg_aplic.prt_Finan_pagamentoMenor}"></h:outputText>
						
						
						<h:selectBooleanCheckbox value="#{MovContaControle.pagamentoParcial}" id="parcialCheck"
										 rendered="#{MovContaControle.exibirParcial}">
							<a4j:support actionListener="#{MovContaControle.marcarOpcao}" 
										 event="onclick"
										 reRender="formLancarPagamento">
										 <f:attribute name="codigoOpcao" value="PARCIAL"></f:attribute>
										 </a4j:support>
						</h:selectBooleanCheckbox>
						<rich:toolTip for="parcialCheck" rendered="#{MovContaControle.exibirParcial}"
                                			  followMouse="true" direction="top-right"  style="width:300px;height: 90px;">
                                		<h:outputText styleClass="tituloCampos" 
                                					  value="#{msg.msg_quitacaoPagamentoParcial}"></h:outputText>
                                </rich:toolTip>
                                  
						<h:outputText rendered="#{MovContaControle.exibirParcial}" styleClass="tituloCampos" value="#{msg_aplic.prt_Finan_pagamentoParcial}"></h:outputText>
						
						
						
					</h:panelGroup>
					<h:panelGrid columns="1" styleClass="centralizado" width="100%" >
                    <h:panelGroup>
                        <a4j:commandButton id="confirmacao"
                                               action="#{MovContaControle.gravarQuitacaoOffline}"
                                               onclick="if(!validar_Data_horas_minutos('formLancarPagamento:dataQuitacao')){ return false};"
                                               value="OK"
                                               rendered="#{MovContaControle.exibirBotaoOk}"
										   	   reRender="panelGroupAtualizarLancarPagamento, formLanc"
										       styleClass="botoes nvoBt"
                                               oncomplete="#{MovContaControle.abrirFecharModalPagto} #{MovContaControle.mensagemNotificar}">
                        </a4j:commandButton>
                        
                        <a4j:commandButton id="gravarsemquitar"
                                               action="#{MovContaControle.lancarPagamentoSemQuitar}"
                                               value="Gravar sem Quitar"
                                               rendered="#{MovContaControle.exibirBotaoOk && MovContaControle.contaQuitacao.pagamentoSimples}"
										 	   reRender="panelGroupAtualizarLancarPagamento"
                                               oncomplete="#{MovContaControle.abrirFecharModalPagto} #{MovContaControle.mensagemNotificar}"
										       styleClass="botoes nvoBt btSec"
                                               style="padding-left:10px">
                        </a4j:commandButton>
                        
                           <a4j:commandButton id="btnCancelar"
                                           style="padding-left:10px"
									       value="Cancelar"
									       styleClass="botoes nvoBt btSec"
                                           action="#{MovContaControle.cancelarQuitacao}"
										   reRender="panelGroupAtualizarLancarPagamento, form:linkModalSelecionados"
                                           oncomplete="#{MovContaControle.abrirFecharModalPagto}">
                        </a4j:commandButton>


                    </h:panelGroup>
                </h:panelGrid>
                 <script>
			                var modal = document.getElementById('modalPanelLancarPagamentoCDiv');
			                var tamanho = modal.offsetWidth;
			                var altura = modal.offsetHeight;
			                if(altura > 600){
					            modal.style.top = (window.innerHeight - altura) / 4;
			                }
			                modal.style.left = (window.innerWidth - tamanho) / 2;
							carregarMaskInput();
			     </script>
            </h:form>
        </rich:modalPanel>
    </a4j:outputPanel>
<%@include file="/include_gerarCheque.jsp"%>

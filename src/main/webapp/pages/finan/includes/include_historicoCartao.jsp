<%@include file="imports.jsp" %>
    <a4j:outputPanel>
        <rich:modalPanel id="modalHistoricoCartao" autosized="true" width="650"  shadowOpacity="true" styleClass="novaModal" >
            <f:facet name="header">
                <h:panelGroup>
                    <h:outputText value="#{msg_aplic.prt_HistoricoCartao}"></h:outputText>
                </h:panelGroup>
            </f:facet>
            <f:facet name="controls">
                <h:panelGroup>
                    <h:outputText
                            styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign"
                            id="hidelinkHistoricoCartao"/>
                    <rich:componentControl for="modalHistoricoCartao" attachTo="hidelinkHistoricoCartao"
                                           operation="hide" event="onclick">
                    </rich:componentControl>
                </h:panelGroup>
                
                
            </f:facet>
            <h:form id="formHistoricoCartao">
            	<h:outputText value="#{msg_aplic.prt_Cheque_data}: #{GestaoRecebiveisControle.historicoCartao.dataInicioApresentar}"
                              rendered="#{(GestaoRecebiveisControle.formaPagamentoSelecionada.tipoFormaPagamento != 'CD')}"
                              styleClass=" cinza texto-size-16"
                              style="margin-left: 15px;"></h:outputText>
                
                <h:outputText value=" - Data original: #{GestaoRecebiveisControle.historicoCartao.dataInicioApresentar}"
                              styleClass=" cinza texto-size-16"
                              rendered="#{(GestaoRecebiveisControle.historicoCartao.dataFimApresentar != null) && (GestaoRecebiveisControle.formaPagamentoSelecionada.tipoFormaPagamento != 'CD')}"></h:outputText>
            	<br/><br/>
                <h:outputText value="N�o existem movimenta��es para este cart�o!"
                              rendered="#{fn:length(GestaoRecebiveisControle.listaHistoricoCartao) == 0}"
                              style="font-weight: bold;font-size: larger;"/>
            	<rich:dataTable var="historico" width="100%" value="#{GestaoRecebiveisControle.listaHistoricoCartao}"
            	             styleClass="tabelaDados semZebra borderTr"
                                                rendered="#{fn:length(GestaoRecebiveisControle.listaHistoricoCartao) > 0}">

                    <rich:column width="10px">
                        <f:facet name="header">
                            <h:outputText value="Data in�cio"/>
                        </f:facet>
                        <h:outputText value="#{historico.dataInicioApresentar}"></h:outputText>
                    </rich:column>

                    <rich:column width="10px">
                        <f:facet name="header">
                            <h:outputText value="Data fim"/>
                        </f:facet>
                        <h:outputText
                                value="#{historico.dataFimApresentar}"></h:outputText>
                    </rich:column>

                    <rich:column rendered="#{GestaoRecebiveisControle.formaPagamentoSelecionada.tipoFormaPagamento != 'CD'}">
                        <f:facet name="header">
                            <h:outputText value="Lote"/>
                        </f:facet>
                        <h:outputText value="#{historico.lote.codigo}"></h:outputText>
                    </rich:column>
                    <rich:column style="padding-left: 10px">
                        <f:facet name="header">
                            <h:outputText value="Conta"/>
                        </f:facet>
                        <h:outputText value="#{historico.movConta.contaVO.descricao}"></h:outputText>
                    </rich:column>
                    
                    <rich:column>
                        <a4j:commandLink value="Lan�amento" title="Ver o lan�amento dessa opera��o"
                                           action="#{GestaoRecebiveisControle.visualizarOperacaoCartaoCredito}"
                                           rendered="#{historico.movConta.codigo > 0}"
                                           styleClass="tooltipster texto-cor-azul linkPadrao inline"
                                           oncomplete="abrirPopup('includes/include_LancamentosDF.jsp', 'TelaLan�amento', 1200, 700);"></a4j:commandLink>
                        
                    </rich:column>
            	</rich:dataTable>
            <br/>
            
            </h:form>
       </rich:modalPanel>
   </a4j:outputPanel>    
            
            
            
<%@include file="imports.jsp" %>
<a4j:outputPanel>
<rich:modalPanel id="modalConsCaixa" autosized="true" shadowOpacity="true" width="750" height="300" showWhenRendered="#{FuncionalidadeControle.funcionalidadeNome eq 'CONSULTAR_CAIXA'}">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText  value="Consulta Hist�rico de Caixas"/>
        </h:panelGroup>
    </f:facet>

    <f:facet name="controls">
        <a4j:form id="formFecharModalConsultarCaixa">
            <a4j:commandButton id="btnFecharModalConsultarCaixa"
                               style="cursor:pointer; background:none; border:none; outline:none; position: relative; right: 13px;"
                               action="#{CaixaControle.onCloseModalFromNovoFront}"
                               oncomplete="#{rich:component('modalConsCaixa')}.hide();"
                               reRender="@none">
                <f:facet name="label">
                    <i class="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"></i>
                </f:facet>

            </a4j:commandButton>
        </a4j:form>
    </f:facet>

    <h:form id="formHistorico">
        <h:panelGrid columns="1" rowClasses="linhaImpar, linhaPar" columnClasses="colunaEsquerda" width="100%" >
            <h:panelGrid columns="6" bgcolor="#EEEEEE" cellpadding="3" columnClasses="centralizado">
                <!-- EMPRESA -->
                <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Finan_MetaFinanceiro_empresa}:" rendered="#{CaixaControle.visualizarTodasEmpresas}"/>
                <h:selectOneMenu id="empresa" styleClass="form" rendered="#{CaixaControle.visualizarTodasEmpresas}"
                                 value="#{CaixaControle.empresaSelecionada.codigo}" >
                    <f:selectItems value="#{CaixaControle.listaComboEmpresa}" />
                    <a4j:support action="#{CaixaControle.montarListaComboUsuario}"
                            			 reRender="usuario"
                            			 event="onchange"/>
                </h:selectOneMenu>

                <!-- Usuario -->
                <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Usuario_tituloForm}:"/>
                <h:selectOneMenu id="usuarioDisabled" styleClass="form"
                                 rendered="#{!CaixaControle.verificarPermissaoConsultarCaixa}"
                                 disabled="#{!CaixaControle.verificarPermissaoConsultarCaixa}"
                                 title="A permiss�o '9.25 - Reabrir e consultar caixa administrativo de todos os usu�rios de todas as unidades', deve estar marcada"
                                 value="#{CaixaControle.usuarioSelecionado.codigo}" >
                    <f:selectItems value="#{CaixaControle.listaComboUsuario}" />
                     
                </h:selectOneMenu>

                <h:selectOneMenu id="usuario" styleClass="form"
                                 rendered="#{CaixaControle.verificarPermissaoConsultarCaixa}"
                                 value="#{CaixaControle.usuarioSelecionado.codigo}" >
                    <f:selectItems value="#{CaixaControle.listaComboUsuario}" />

                </h:selectOneMenu>

                <!-- BOTAO CONSULTA -->
                <h:panelGroup id="panelBtn" layout="block" style="width: 110px;">
                <a4j:commandLink id="consultar" styleClass="botoes nvoBt" value="#{msg_bt.btn_consultar}"
                                   action="#{CaixaControle.consultarHistoricoCaixa}" 
                                   reRender="listaHistorico, mensagens, panelPeriodoAbertura, panelPeriodoFechamento,scResultadoHistorico,containerFuncMask"
                                   title="#{msg.msg_consultar_dados}">
                    <i class="fa-icon-search"></i>
                </a4j:commandLink>
                </h:panelGroup>

            </h:panelGrid>
            <h:panelGrid columns="6" width="100%" bgcolor="#EEEEEE" columnClasses="centralizado">
                <h:outputText styleClass="tituloDemonstrativo" value="#{msg_aplic.prt_Finan_Caixa_codigo}"/>
                <h:inputText onkeypress="return mascara(this.form, 'form:codigoTrib', '9999999999', event);" size="5"
                             onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" value="#{CaixaControle.codigoCaixa}"></h:inputText>
                <!--periodo abertura-->
                <h:outputText styleClass="tituloDemonstrativo" value="#{msg_aplic.prt_Finan_Caixa_periodoAbertura}:" />
                <h:panelGroup id="panelPeriodoAbertura" >
                    <rich:calendar id="dataIniAbertura"
                                   value="#{CaixaControle.dataIniAbertura}"
                                   inputSize="6"
                                   inputClass="form"
                                   oninputblur="blurinput(this);"
                                   oninputfocus="focusinput(this);"
                                   oninputchange="return validar_Data(this.id);"
                                   datePattern="dd/MM/yyyy"
                                   enableManualInput="true"
                                   zindex="2"
                                   showWeeksBar="false" />
                    <rich:spacer width="3"/>
                    <h:outputText styleClass="tituloDemonstrativo" value="#{msg_aplic.prt_Finan_Lancamentos_a}"/>
                    <rich:spacer width="3"/>
                    <rich:calendar id="dataFimAbertura"
                                   value="#{CaixaControle.dataFimAbertura}"
                                   inputSize="6"
                                   inputClass="form"
                                   oninputblur="blurinput(this);"
                                   oninputfocus="focusinput(this);"
                                   oninputchange="return validar_Data(this.id);"
                                   datePattern="dd/MM/yyyy"
                                   enableManualInput="true"
                                   zindex="2"
                                   showWeeksBar="false" />
                    <rich:spacer width="3"/>
                    <a4j:commandButton id="limparPeriodoAbertura"
                                       action="#{CaixaControle.limparPeriodoAbertura}"
                                       image="/images/limpar.gif" title="Limpar per�odo de abertura."
                                       reRender="dataIniAbertura, dataFimAbertura,containerFuncMask"/>
                </h:panelGroup>

                <!--periodo Fechamento-->
                <h:outputText styleClass="tituloDemonstrativo" value="#{msg_aplic.prt_Finan_Caixa_periodoFechamento}:" />
                <h:panelGroup id="panelPeriodoFechamento" >
                    <rich:calendar id="dataIniFechamento"
                                   value="#{CaixaControle.dataIniFechamento}"
                                   inputSize="6"
                                   inputClass="form"
                                   oninputblur="blurinput(this);"
                                   oninputfocus="focusinput(this);"
                                   oninputchange="return validar_Data(this.id);"
                                   datePattern="dd/MM/yyyy"
                                   enableManualInput="true"
                                   zindex="2"
                                   showWeeksBar="false" />
                    <rich:spacer width="3"/>
                    <h:outputText styleClass="tituloDemonstrativo" value="#{msg_aplic.prt_Finan_Lancamentos_a}"/>
                    <rich:spacer width="3"/>
                    <rich:calendar id="dataFimFechamento"
                                   value="#{CaixaControle.dataFimFechamento}"
                                   inputSize="6"
                                   inputClass="form"
                                   oninputblur="blurinput(this);"
                                   oninputfocus="focusinput(this);"
                                   oninputchange="return validar_Data(this.id);"
                                   datePattern="dd/MM/yyyy"
                                   enableManualInput="true"
                                   zindex="2"
                                   showWeeksBar="false" />
                    <rich:spacer width="3"/>
                    <a4j:commandButton id="limparPeriodoFechamento"
                                       action="#{CaixaControle.limparPeriodoFechamento}"
                                       image="/images/limpar.gif" title="Limpar per�odo de fechamento."
                                       reRender="dataIniFechamento, dataFimFechamento,containerFuncMask"/>
                </h:panelGroup>
				

            </h:panelGrid>
            <h:panelGrid columns="2" width="100%" bgcolor="#EEEEEE" >
                <!--periodo trabalho-->
                <h:panelGroup id="panelPeriodoTrbalho" >
                    <h:outputText styleClass="tituloDemonstrativo" value="#{msg_aplic.prt_Finan_Caixa_periodoTrabalho}:" />
                    <rich:spacer width="5"/>
                    <rich:calendar id="dataIniTrabalho"
                                   value="#{CaixaControle.dataIniTrabalho}"
                                   inputSize="6"
                                   inputClass="form"
                                   oninputblur="blurinput(this);"
                                   oninputfocus="focusinput(this);"
                                   oninputchange="return validar_Data(this.id);"
                                   datePattern="dd/MM/yyyy"
                                   enableManualInput="true"
                                   zindex="2"
                                   showWeeksBar="false" />
                    <rich:spacer width="5"/>
                    <h:outputText styleClass="tituloDemonstrativo" value="#{msg_aplic.prt_Finan_Lancamentos_a}"/>
                    <rich:spacer width="3"/>
                    <rich:calendar id="dataFimTrabalho"
                                   value="#{CaixaControle.dataFimTrabalho}"
                                   inputSize="6"
                                   inputClass="form"
                                   oninputblur="blurinput(this);"
                                   oninputfocus="focusinput(this);"
                                   oninputchange="return validar_Data(this.id);"
                                   datePattern="dd/MM/yyyy"
                                   enableManualInput="true"
                                   zindex="2"
                                   showWeeksBar="false" />
                    <rich:spacer width="3"/>
                    <a4j:commandButton id="limparPeriodoTrabalho"
                                       action="#{CaixaControle.limparPeriodoTrabalho}"
                                       image="/images/limpar.gif" title="Limpar per�odo de trabalho."
                                       reRender="dataIniTrabalho, dataFimTrabalho,containerFuncMask"/>
                </h:panelGroup>
            </h:panelGrid>
            <rich:spacer height="10"/>

            <!-- RESULTADO DA CONSULTA -->
            <rich:dataTable id="listaHistorico" width="100%" columnClasses="centralizado,centralizado,centralizado,centralizado,centralizado"
                         headerClass="subordinado" rowClasses="linhaImpar, linhaPar" value="#{CaixaControle.listaHistoricoCaixa}" var="caixa"
                         rows="10">
                         
                 <rich:column sortBy="#{caixa.codigo}">
                    <f:facet name="header">
                        <h:outputText value="Nr." />
                    </f:facet>
                    <h:outputText styleClass="tituloCampos" value="#{caixa.codigo}"/>
                </rich:column>
                         
                <!-- empresa -->
                <rich:column sortBy="#{caixa.empresaVo.nome}">
                    <f:facet name="header">
                        <h:outputText value="#{msg_aplic.prt_Finan_MetaFinanceiro_empresa}" />
                    </f:facet>
                    <h:outputText styleClass="tituloCampos" value="#{caixa.empresaVo.nome}"/>
                </rich:column>

                <!-- Usu�rio -->
                <rich:column sortBy="#{caixa.usuarioVo.nome}">
                    <f:facet name="header">
                        <h:outputText value="Usu�rio" />
                    </f:facet>
                    <h:outputText styleClass="tituloCampos" value="#{caixa.usuarioVo.nome}"/>
                </rich:column>

                <!-- Data Abetura -->
                <rich:column sortBy="#{caixa.dataAbertura}">
                    <f:facet name="header">
                        <h:outputText value="Data Abertura" />
                    </f:facet>
                    <h:outputText styleClass="tituloCampos" value="#{caixa.dataAbertura}">
                        <f:convertDateTime pattern="dd/MM/yyyy HH:mm:ss"/>
                    </h:outputText>
                </rich:column>

                <!-- Data Fechamento -->
                <rich:column sortBy="#{caixa.dataFechamento}">
                    <f:facet name="header">
                        <h:outputText value="Data Fechamento" />
                    </f:facet>
                    <h:outputText styleClass="tituloCampos" value="#{caixa.dataFechamento}">
                        <f:convertDateTime pattern="dd/MM/yyyy HH:mm:ss"/>
                    </h:outputText>
                </rich:column>
                <!--DATA TRABALHO--!>
                <rich:column sortBy="#{caixa.dataTrabalho_Apresentar}">
                    <f:facet name="header">
                        <h:outputText value="Data Trabalho" />
                    </f:facet>
                    <h:outputText styleClass="tituloCampos" value="#{caixa.dataTrabalho_Apresentar}">
                        <f:convertDateTime pattern="dd/MM/yyyy HH:mm:ss"/>
                    </h:outputText>
                </rich:column>

                <!-- OPCOES -->
                <rich:column>
                    <f:facet name="header">
                        <h:outputText value="#{msg_aplic.prt_Finan_MetaFinanceiro_opcoes}"/>
                    </f:facet>
                    <h:commandLink styleClass="botoes nvoBt btSec" value="Visualizar" action="#{CaixaControle.visualizarCaixa}">
                        <a4j:support event="onclick" oncomplete="Richfaces.hideModalPanel('modalConsCaixa')" reRender="form"/>
                    </h:commandLink>
                </rich:column>
            </rich:dataTable>
			 <rich:datascroller align="center" for="listaHistorico" maxPages="10"
                               id="scResultadoHistorico" />

            <!-- PANEL DE MENSAGENS -->
            <h:panelGrid id="mensagens" columns="1" width="100%" styleClass="tabMensagens">
                <h:outputText styleClass="mensagem"  value="#{CaixaControle.mensagem}"/>
                <h:outputText styleClass="mensagemDetalhada" value="#{CaixaControle.mensagemDetalhada}" />
            </h:panelGrid>
        </h:panelGrid>
        <h:panelGroup layout="block" id="containerFuncMask">
            <script>
                carregarMaskInput();
            </script>
        </h:panelGroup>
    </h:form>
</rich:modalPanel>
 </a4j:outputPanel>



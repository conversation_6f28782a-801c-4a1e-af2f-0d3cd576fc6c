<%@ taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%-- CONTA CREDITO --%>
<h:panelGroup id="pgLabelCreditoMulta">
    <h:outputText rendered="#{MovContaControle.integracaoContabilAlterData && MovContaControle.informarMulta}"
                  value="Conta cont�bil credora multa:"
                  style="vertical-align: middle"
                  styleClass="tituloCampos"></h:outputText>
</h:panelGroup>
<h:panelGroup id="sugestionCreditoMulta">
    <h:inputText  id="nomeCreditoMulta"
                  size="50"
                  style="vertical-align: middle"
                  maxlength="50"
                  onblur="blurinput(this);"
                  rendered="#{MovContaControle.integracaoContabilAlterData && MovContaControle.informarMulta}"
                  onfocus="focusinput(this);"
                  styleClass="form"
                  value="#{MovContaControle.movContaContabilVO.contaContabilCreditoMulta.descricao}" >
        <a4j:support event="onchange" action="#{MovContaControle.setarContaCredoraMultaVazio}" reRender="form"/>
    </h:inputText>

    <rich:suggestionbox   height="200" width="400"
                          rendered="#{MovContaControle.integracaoContabilAlterData && MovContaControle.informarMulta}"
                          for="nomeCreditoMulta"
                          fetchValue="#{resultContaCredoraMulta.descricao}"
                          status="true"
                          nothingLabel="Nenhum registro  encontrado!"
                          style="vertical-align: middle"
                          suggestionAction="#{MovContaControle.executarAutocompleteConsultarContaContabil}"
                          minChars="1"
                          rowClasses="linhaImpar, linhaPar"
                          var="resultContaCredoraMulta"  id="suggestionCreditoMulta">
        <a4j:support event="onselect"
                     reRender="form"
                     focus="nomeDebitoMulta"
                     ignoreDupResponses="true"
                     action="#{MovContaControle.selecionarContaCredoraMultaSuggestionBox}">
        </a4j:support>
        <h:column>
            <f:facet name="header">
                <h:outputText value="Descri��o"  styleClass="textverysmall"/>
            </f:facet>
            <h:outputText styleClass="textverysmall" value="#{resultContaCredoraMulta.descricao}" />
        </h:column>
    </rich:suggestionbox>
</h:panelGroup>



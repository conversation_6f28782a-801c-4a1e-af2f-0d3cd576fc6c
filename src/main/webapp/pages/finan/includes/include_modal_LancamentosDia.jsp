<%@include file="include_imports.jsp" %>

<a4j:outputPanel id="outPanelLancamentos">
    <a4j:form id="formModalLancamentosDia">

        <rich:modalPanel id="modalLancamentos" autosized="true"
                         shadowOpacity="true" width="800" height="250">
            <f:facet name="header">
                <h:panelGroup>
                    <h:outputText id="listaLancamentos" value="Lista dos Lan�amentos"/>
                </h:panelGroup>
            </f:facet>
            <f:facet name="controls">
                        <a4j:commandButton image="/imagens/close.png" style="cursor:pointer"
                                           action="#{MetaFinanceiroBIControle.zerarDatasRealizadoAcumulado}"
                                           oncomplete="Richfaces.hideModalPanel('modalLancamentos');"
                                           reRender="form"
                                           id="hidelinkLancamentos"/>
            </f:facet>

            <h:panelGrid width="100%">
                <h:panelGroup>
                <h:outputText style="font-size: 14px;" value="Atualizar dados:"/>
                <a4j:commandLink id="atualizarListaLancamentos" reRender="tableLancamentos, paginacaoDataScroller, totalRegistrosDiaSelecionado, valorTotalRegistrosDiaSelecionado"
                                 oncomplete="montarTips();"
                                 title="Obter dados em tempo real"
                                 style="font-size: 15px; margin-left: 5px;"
                                 styleClass="fa-icon-refresh"
                                 action="#{MetaFinanceiroBIControle.atualizarListaFaturamentoRealizadoAcumulado}">
                </a4j:commandLink>
                </h:panelGroup>
                <rich:dataTable id="tableLancamentos" width="100%" headerClass="consulta" rowClasses="linhaPar,linhaImpar "
                                reRender="panelListaLancamentosDF"
                                columnClasses="centralizado, colunaEsquerda, centralizado, centralizado, colunaEsquerda, colunaEsquerda, colunaDireita"
                                value="#{MetaFinanceiroBIControle.listaDiaDetalhada}"
                                rows="10" var="lancamento">
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_Finan_Dia}"/>
                        </f:facet>
                        <h:outputText value="#{lancamento.dia}">
                            <f:convertDateTime pattern="dd/MM/yyyy HH:mm:ss"/>
                        </h:outputText>
                    </rich:column>

                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_Finan_Cliente}"/>
                        </f:facet>
                        <h:outputText value="#{lancamento.cliente}"/>
                    </rich:column>

                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_Finan_Contrato}"/>
                        </f:facet>
                        <h:outputText rendered="#{lancamento.contrato > 0}" value="#{lancamento.contrato}"/>
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_Duracao_tituloForm}"/>
                        </f:facet>
                        <h:outputText value="#{lancamento.duracaoContrato}"/>
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText rendered="#{MetaFinanceiroBIControle.metaFinanceiraPorFaturamento}"
                                          value="#{msg_aplic.prt_Finan_ConsultorAtual}"/>
                            <h:outputText rendered="#{!MetaFinanceiroBIControle.metaFinanceiraPorFaturamento}"
                                          value="#{msg_aplic[MetaFinanceiroBIControle.tituloConsultor]}"/>
                        </f:facet>
                        <h:outputText value="#{lancamento.responsavel}"/>
                    </rich:column>

                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_CaixaPorOperador_quemLancou}"/>
                        </f:facet>
                        <h:outputText value="#{lancamento.responsavelParcela}"/>
                    </rich:column>
                    <rich:column rendered="#{!MetaFinanceiroBIControle.metaFinanceiraPorFaturamento}">
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_Finan_formaPagamento}"/>
                        </f:facet>
                        <h:outputText value="#{lancamento.formaPagamento}"/>
                    </rich:column>

                    <rich:column>
                        <f:facet name="header">
                            <h:outputText id="lblContrato" value="#{msg_aplic.prt_Finan_Valor}"/>
                        </f:facet>
                        <h:outputText id="valorContrato" value="#{lancamento.valor}">
                            <f:converter converterId="FormatadorNumerico"/>
                        </h:outputText>
                    </rich:column>

                </rich:dataTable>
                <rich:datascroller id="paginacaoDataScroller" status="false" for="tableLancamentos" maxPages="10"/>
            </h:panelGrid>
            <table width="100%">
                <tr>
                    <td width="33%"></td>

                    <td width="33%" class="centralizado">
                        <h:outputText style="color: #474747; font-family: Arial,Verdana,sans-serif; font-size: 11px;"
                                      value="#{msg_aplic.prt_Finan_RegistrosTotal}: "/>
                        <h:outputText id="totalRegistrosDiaSelecionado" style="color: #474747; font-family: Arial,Verdana,sans-serif; font-size: 11px;"
                                      value="#{MetaFinanceiroBIControle.totalRegistrosDiaSelecionado}"/>
                    </td>
                    <td width="33%" class="colunaDireita">
                        <h:outputText style="color: #474747; font-family: Arial,Verdana,sans-serif; font-size: 11px;"
                                      value="#{msg_aplic.prt_Finan_ValorTotal}: "/>
                        <h:outputText id="valorTotalRegistrosDiaSelecionado" style="color: #474747; font-family: Arial,Verdana,sans-serif; font-size: 11px;"
                                      value="#{MetaFinanceiroBIControle.valorDiaSelecionado}"/>
                    </td>
                </tr>
            </table>

        </rich:modalPanel>

    </a4j:form>

</a4j:outputPanel>
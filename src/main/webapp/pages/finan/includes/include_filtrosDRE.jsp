<div id="divFiltros" style="display:none; width:100%; color: #F5F5F5 ">


  <table id="tabelaFiltros"  style="display:block;"> 
 				
 				<tr>
 				<!--<td>
            <table width="100%" >
			<tr>
				<td width="30%"><h:outputText value="Planos de Contas" styleClass="tituloBoldPequeno" /></td>
				<td width="70%" style="text-align: right;">
					<a href="#"
     		   class="expandirPlano">
     		   Expandir Tudo
     		   </a>
     		   &nbsp;
     		   <a href="#"
     		   class="expandirUmPlano">
     		   Expandir
     		   </a>
     		   &nbsp;
     		   <a href="#"
     		   class="retrairUmPlano">
     		   Retrair
     		   </a>
     		   &nbsp;
     		   <a href="#"
     		   class="retrairPlano">
     		   Retrair Tudo
     		   </a>
				</td>
			</tr>
		</table> 
		</td>-->
 			 	<td></td>
 				<td>
             <table width="100%" >
			<tr>
				<td width="30%"><h:outputText value="Centros de Custos" styleClass="tituloBoldPequeno" /></td>
				<td width="70%" style="text-align: right;">
                <a href="#"
     		   class="expandirCentro">
     		   Expandir Tudo
     		   </a>
     		   &nbsp;
     		   <a href="#"
     		   class="expandirUmCentro">
     		   Expandir
     		   </a>
     		   &nbsp;
     		   <a href="#"
     		   class="retrairUmCentro">
     		   Retrair
     		   </a>
     		   &nbsp;
     		   <a href="#"
     		   class="retrairCentro">
     		   Retrair Tudo
     		   </a>
     		   </td>
     		   </tr>
     		   	</table>
     		   	</td>
     		   	<td>
					<h:outputText value="#{msg_aplic.prt_contasApresentarBI}" styleClass="tituloBoldPequeno" style="margin-left: 20px;" />

     		   	</td>
     		   	</tr>
 				<tr style="vertical-align: top;">
 			        <%-- <td>
                                  
                		<div id="caixaPlanosFiltros" style="display: block; width: 400px; height: 200px; overflow-x:hidden; overflow-y:auto; border:solid 1px #A9A9A9; background-color: #FFFFFF;" >
                			<table bgcolor="#FFFFFF" width="100%">
                			<tr>
                			<td>&nbsp;&nbsp;&nbsp;</td>
                			<td>
                			<table border="0"
                                    class="filtroPlanos"
                                    id="dnd-filtroPlanos" 
                                    width="100%">
                              
                              <tbody>
                              <c:forEach var="plano" varStatus="indice"
                                         items="${DemonstrativoFinanceiroControle.listaPlanoContaTree}">
                                         
                                <!-- Definir as cores da tabela zebrada -->
									<c:choose>
										<c:when test="${indice.count % 2 == 0}">
											<c:set var="corLinhaP" value="#F5F5F5" scope="request" />
										</c:when>
									   <c:otherwise>
											<c:set var="corLinhaP" value="#FFFFFF" scope="request" />
									   </c:otherwise>
									  </c:choose>
								
                                      <c:choose>
                                          <c:when test="${fn:indexOf(plano.codigoAgrupador, '.') > 0}">
                                               <c:set var="noPai" value="${fn:substring(plano.codigoAgrupador,0, fn:length(plano.codigoAgrupador) -4)}" scope="request" />

                                                <tr bgcolor="${corLinhaP}" id="${plano.codigoNode}"
                                                    class="child-of-${fn:replace(noPai,'.', '') + 0 }">
                                                    <td class="textblack">
                                                    	<input type="checkbox" onclick="adicionarFiltro('${plano.codigoEntidade}', this, 'formDF:planosSelecionados');" <c:if test="${plano.selecionado}"> checked="checked" </c:if> />
                                                    	<c:out  value="${plano.nome}"></c:out>
                                                    </td>
                                                </tr>
                                          </c:when>
                                          <c:otherwise>
                                                    <tr bgcolor="${corLinhaP}" id="${plano.codigoNode}">
                                                    	
                                                        <td class="textblack">
                                                        	<input type="checkbox" onclick="adicionarFiltro('${plano.codigoEntidade}', this, 'formDF:planosSelecionados');" 
                                                        															<c:if test="${plano.selecionado}"> checked="checked" </c:if> />
                                                       		<c:out value="${plano.nome}"></c:out> 
                                                       	</td>
                                                    </tr>
                                          </c:otherwise>
                                      </c:choose>
                               </c:forEach>
                              </tbody>
                            </table> 
                          </td>
                	</tr>
                	</table>
                
                </div>
                </td> --%>
                <td>
                &nbsp;&nbsp;
                </td>
                <td>
                	<div id="caixaCentrosFiltros" style="width: 400px; height: 200px; overflow-x:hidden; overflow-y:auto; border:solid 1px #A9A9A9;background-color: #FFFFFF;"  >
                		<table bgcolor="#FFFFFF" width="100%">
                			<tr>
                			<td>&nbsp;&nbsp;</td>
               				 <td>
                 			<table border="0"  class="filtroCentros"
                                    id="dnd-filtroCentros" 
                                    width="100%">
                              
                              <tbody>
                              <c:forEach var="centro" varStatus="indice"
                                         items="${DREControle.listaCentroCustoTree}">
                                         
                                   <%-- Definir as cores da tabela zebrada --%>
									<c:choose>
										<c:when test="${indice.count % 2 == 0}">
											<c:set var="corLinhaC" value="#F5F5F5" scope="request" />
										</c:when>
									   <c:otherwise>
											<c:set var="corLinhaC" value="#FFFFFF" scope="request" />
									   </c:otherwise>
									  </c:choose>
								
                                      <c:choose>
                                          <c:when test="${fn:indexOf(centro.codigoAgrupador, '.') > 0}">
                                               <c:set var="noPai" value="${fn:substring(centro.codigoAgrupador,0, fn:length(centro.codigoAgrupador) -4)}" scope="request" />

                                                <tr bgcolor="${corLinhaC}" id="${centro.codigoNode}"
                                                    class="child-of-${fn:replace(noPai,'.', '') + 0 }">
                                                    <td class="textblack">
                                                    	<input onclick="adicionarFiltro('${centro.codigoEntidade}', this, 'formDRE:centrosSelecionados');"
                                                        															<c:if test="${centro.selecionado}"> checked="checked" </c:if>  type="checkbox" />
                                                    	<c:out  value="${centro.nome}"></c:out>
                                                    </td>
                                                </tr>
                                          </c:when>
                                          <c:otherwise>
                                                    <tr bgcolor="${corLinhaC}" id="${centro.codigoNode}">
                                                    	
                                                        <td class="textblack">
                                                        	<input type="checkbox" onclick="adicionarFiltro('${centro.codigoEntidade}', this, 'formDRE:centrosSelecionados');"
                                                        															<c:if test="${centro.selecionado}"> checked="checked" </c:if>  />
                                                       		<c:out value="${centro.nome}"></c:out> 
                                                       	</td>
                                                    </tr>
                                          </c:otherwise>
                                      </c:choose>
                               </c:forEach>
                              </tbody>
                            </table>
                          </td>
                		</tr>
                	</table>
                </div>
				</td>

						<td>
							<div id="contas" style="overflow-x:hidden; overflow-y:auto; border:solid 1px #A9A9A9;background-color: #FFFFFF;height: 200px; width: 400px; margin-left: 20px;" >
								<table bgcolor="#FFFFFF" width="100%">
									<tr>
										<td>&nbsp;&nbsp;</td>
										<td>
											<rich:dataTable style="border:none;" columnClasses="colunaFinan" width="100%" rowClasses="linhaParFinan,linhaImparFinan"
															var="conta" value="#{DREControle.contasFiltro}">
												<h:column>
													<h:selectBooleanCheckbox value="#{conta.contaEscolhida}"/>
													<h:outputText value="#{conta.descricao}"
																  rendered="#{conta.codigo > 0}"
																  styleClass="textblack"/>

													<h:outputText value="#{msg_aplic.prt_nao_movimentados}"
																  title="#{msg_aplic.prt_nao_movimentados_tip}"
																  rendered="#{conta.codigo < 0}"
																  styleClass="textblack"/>
												</h:column>
											</rich:dataTable>

										</td>
									</tr>
								</table>
							</div>
						</td>

 				</tr>
        </table>
                  
 </div>


    <h:inputHidden id="planosSelecionados" value="#{DREControle.planosSelecionados}" />
    <h:inputHidden id="centrosSelecionados" value="#{DREControle.centrosSelecionados}" />
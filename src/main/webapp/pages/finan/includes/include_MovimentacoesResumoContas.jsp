<%--
    Document   : include_TreeViewDF
    Created on : 04/10/2011, 16:49:37
    Author     : <PERSON><PERSON><PERSON>
--%>
<%@page pageEncoding="ISO-8859-1" %>
<%@include file="../includes/include_imports.jsp" %>
<!-- ------------------------ Começo Movimentações Resumo de Contas ------------------------------- -->
<rich:dataTable
        value="#{GerenciadorContaControle.listaMovimentacoes}"
        id="listaMov"
        var="movContaRateio"
        width="100%"
        headerClass="consulta"
        columnClasses=""
        styleClass="tabelaSimplesCustom showCellEmpty tabela-zebra"
        stateVar="status"
        rendered="#{not empty GerenciadorContaControle.listaMovimentacoes and not GerenciadorContaControle.contaSelecionada.contaIntegracaoOpenBank}">

    <rich:column width="2%" styleClass="centralizado" style="padding-left: 0px">
        <f:facet name="header">
            <a4j:commandLink id="checkboxSelecionarTodasMovimentacoes"
                             status="none"
                             process="listaMov"
                             styleClass="texto-cor-azul botao-checkbox"
                             action="#{GerenciadorContaControle.selecionarOuDesmarcarTodasMovimentacoes}"
                             reRender="containerTotalizadores, checkboxMovimentacao, checkboxSelecionarTodasMovimentacoes">
                <h:outputText styleClass="icon #{GerenciadorContaControle.algumaMovimentacaoNaoSelecionada ? 'fa-icon-check-empty' : 'fa-icon-check'}"/>
            </a4j:commandLink>
        </f:facet>
        <a4j:commandLink id="checkboxMovimentacao" style="padding-left: 0px"
                         status="none"
                         process="listaMov"
                         styleClass="texto-cor-azul botao-checkbox"
                         reRender="containerTotalizadores, checkboxMovimentacao, checkboxSelecionarTodasMovimentacoes">
            <h:outputText styleClass="icon #{movContaRateio.escolhido ? 'fa-icon-check' : 'fa-icon-check-empty'}"/>
            <f:setPropertyActionListener value="#{!movContaRateio.escolhido}" target="#{movContaRateio.escolhido}"/>
        </a4j:commandLink>
    </rich:column>

    <rich:column headerClass="esquerda texto-cor-cinza"
                 sortBy="#{movContaRateio.movConta.codigo}">
        <f:facet name="header">
            <h:outputText value="Cod"/>
        </f:facet>
        <h:outputText value="#{movContaRateio.movConta.codigo}"/>
    </rich:column>

    <rich:column width="15%" headerClass="esquerda texto-cor-cinza" sortBy="#{movContaRateio.movConta.dataQuitacao}">
        <f:facet name="header">
            <h:outputText value="#{msg_aplic.prt_Finan_Lancamentos_dataQuitacao_abreviado}"/>
        </f:facet>
        <h:outputText value="#{movContaRateio.movConta.dataQuitacao_Apresentar}"/>
    </rich:column>

    <rich:column width="15%" headerClass="esquerda texto-cor-cinza" sortBy="#{movContaRateio.movConta.dataLancamento}">
        <f:facet name="header">
            <h:outputText value="#{msg_aplic.prt_Finan_Lancamentos_dataLancamento_abreviado}"/>
        </f:facet>
        <h:outputText value="#{movContaRateio.movConta.dataLancamento_Apresentar}"/>
    </rich:column>

    <rich:column width="15%" headerClass="esquerda texto-cor-cinza" sortBy="#{movContaRateio.movConta.dataCompetencia}">
        <f:facet name="header">
            <h:outputText value="#{msg_aplic.prt_Finan_Lancamento_dataCompetencia}"/>
        </f:facet>
        <h:outputText value="#{movContaRateio.movConta.dataCompetencia_Apresentar}"/>
    </rich:column>

    <rich:column headerClass="esquerda texto-cor-cinza" sortBy="#{movContaRateio.descricao}">
        <f:facet name="header">
            <h:outputText value="#{msg_aplic.prt_Finan_Lancamentos_descricao}"/>
        </f:facet>
        <p style="word-wrap: break-word; width: 200px">
            <a4j:commandLink action="#{OperacaoContaControle.editarLancamentoPopUp}"
                             oncomplete="abrirPopup('includes/include_LancamentosDF.jsp', 'TelaLançamento', 1200, 700);">

                <h:outputText value="#{movContaRateio.descricao}"/>

            </a4j:commandLink>
        </p>
    </rich:column>

    <rich:column headerClass="esquerda texto-cor-cinza" sortBy="#{movContaRateio.numeroDocumento}">
        <f:facet name="header">
            <h:outputText value="#{msg_aplic.prt_Finan_Lancamento_numDocumento}" styleClass="texto-cor-cinza esquerda" />
        </f:facet>
        <h:outputText value="#{(movContaRateio.numeroDocumento != null && not empty movContaRateio.numeroDocumento) ? movContaRateio.numeroDocumento : movContaRateio.movConta.numeroDocumento}"/>
    </rich:column>

    <rich:column headerClass="esquerda texto-cor-cinza" sortBy="#{movContaRateio.formaPagamentoVO.tipoFormaPagamento_Apresentar}">
        <f:facet name="header">
            <h:outputText value="#{msg_aplic.prt_Finan_Conta_formaPgto}" styleClass="texto-cor-cinza esquerda" />
        </f:facet>
        <h:outputText value="#{movContaRateio.formaPagamentoVO.tipoFormaPagamento_Apresentar}"/>
    </rich:column>

    <rich:column headerClass="esquerda texto-cor-cinza" sortBy="#{movContaRateio.movConta.nomePlanoConta}">
        <f:facet name="header">
            <h:outputText value="#{msg_aplic.prt_Finan_Lancamento_planoContas}" styleClass="esquerda" />
        </f:facet>
        <h:outputText value="#{movContaRateio.planoContaVO.codigo}"/>
    </rich:column>


    <rich:column headerClass="esquerda texto-cor-cinza" sortBy="#{movContaRateio.tipoES.descricao}">
        <f:facet name="header">
            <h:outputText value="E/S"/>
        </f:facet>
        <h:outputText value="#{movContaRateio.tipoES.descricao}"/>
    </rich:column>

    <rich:column headerClass="esquerda texto-cor-cinza" sortBy="#{movContaRateio.movConta.tipoOperacaoLancamento.descricao}">
        <f:facet name="header">
            <h:outputText value="#{msg_aplic.prt_Finan_Lancamentos_operacoes}" styleClass="texto-cor-cinza esquerda" />
        </f:facet>
        <h:outputText value="#{movContaRateio.movConta.tipoOperacaoLancamento.descricao}"/>
    </rich:column>

    <rich:column headerClass="esquerda texto-cor-cinza">
        <f:facet name="header">
            <h:outputText value="#{msg_aplic.prt_Finan_GestaoRecebiveis_Lote}"/>
        </f:facet>
        <a4j:commandLink
                rendered="#{movContaRateio.movConta.lote.codigo != null && movContaRateio.movConta.lote.codigo > 0}"
                action="#{GestaoLotesControle.editarLote}">
            <h:outputText value="#{movContaRateio.movConta.lote.codigo}"/>
            <a4j:actionparam name="codigoLote"
                             value="#{movContaRateio.movConta.lote.codigo}"
                             assignTo="#{GestaoLotesControle.codigoLote}"/>
            <a4j:actionparam name="origem" value="resumoContas" assignTo="#{GestaoLotesControle.origem}"/>
        </a4j:commandLink>
    </rich:column>


    <rich:column headerClass="direita texto-cor-cinza" styleClass="direita" sortBy="#{movContaRateio.valor}">
        <f:facet name="header">
            <h:outputText value="#{msg_aplic.prt_Finan_GestaoRecebiveis_valor}"/>
        </f:facet>
        <h:outputText styleClass="negrito" value="#{MovPagamentoControle.empresaLogado.moeda} #{movContaRateio.valorFormatado}"
                      style="white-space: nowrap; color: #{movContaRateio.tipoES.cor}"/>
    </rich:column>

    <rich:column  headerClass="direita texto-cor-cinza" styleClass="direita" sortBy="#{movContaRateio.saldo}">
        <f:facet name="header">
            <h:outputText value="Saldo"/>
        </f:facet>

        <h:outputText value="#{MovPagamentoControle.empresaLogado.moeda} #{movContaRateio.saldoFormatado}" style="white-space: nowrap;"/>
    </rich:column>

    <rich:column width="8.5%" styleClass="direita">
        <a4j:commandLink id="excluir"
                         style="font-size: 1.2em !important;color:#29AAE2"
                         rendered="#{movContaRateio.movConta.tipoOperacaoLancamento.descricao eq 'Pagamento' or
                                                                      movContaRateio.movConta.tipoOperacaoLancamento.descricao eq 'Recebimento' }"
                         action="#{MovContaControle.preparaExclusaoVindoDoResumoConta}"
                         reRender="modalPanelLancarPagamento,panelAutorizacaoFuncionalidade"
                         title="#{msg_bt.btn_excluir}">
            <h:outputText styleClass="icon fa-icon-trash"/>
        </a4j:commandLink>
        <a4j:commandLink style="font-size: 1.2em !important;color:#29AAE2"
                         actionListener="#{OperacaoContaControle.imprimirComprovanteListener}"
                         title="Imprimir"
                         oncomplete="abrirPopupPDFImpressao('../../relatorio/#{OperacaoContaControle.nomeArquivoRelatorioGeradoAgora}','', 780, 595);">
            <f:attribute name="movconta" value="#{movContaRateio.movConta.codigo}"/>
            <h:outputText styleClass="icon fa-icon-print"/>
        </a4j:commandLink>
    </rich:column>

</rich:dataTable>
<div>
    <h:outputText
            rendered="#{empty GerenciadorContaControle.listaMovimentacoes and not GerenciadorContaControle.contaSelecionada.contaIntegracaoOpenBank}"
            styleClass="tituloDemonstrativo"
            value="Não existem movimentações nesta conta para o período informado."/>
</div>
<h:panelGroup id="totalizadorResultados"
              layout="block"
              styleClass="container">
    <h:outputText
            style="padding-bottom: 10px;"
            rendered="#{fn:length(GerenciadorContaControle.listaMovimentacoes) == 1}"
            styleClass="col-md-12 dataTables_info texto-cor-cinza"
            value="Total: #{fn:length(GerenciadorContaControle.listaMovimentacoes)} item"/>
    <h:outputText
            style="padding-bottom: 10px;"
            rendered="#{fn:length(GerenciadorContaControle.listaMovimentacoes) > 1}"
            styleClass="col-md-12 dataTables_info texto-cor-cinza"
            value="Total: #{fn:length(GerenciadorContaControle.listaMovimentacoes)} itens"/>
</h:panelGroup>
<!-- ------------------------ Fim Movimentações Resumo de Contas ------------------------------- -->




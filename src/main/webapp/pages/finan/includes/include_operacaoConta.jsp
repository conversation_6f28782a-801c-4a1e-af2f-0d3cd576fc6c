<%@include file="imports.jsp" %>
<rich:modalPanel id="panelDeposito" autosized="true" shadowOpacity="true" width="550" height="120"
                 styleClass="novaModal"
                 onshow="document.getElementById('formDeposito:descricaoDeposito').focus();">

    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="#{OperacaoContaControle.labelModal}"/>
        </h:panelGroup>
    </f:facet>

    <a4j:form id="formDeposito" ajaxSubmit="true" >
        <div style="margin:10px 0 0 0;"></div>

        <rich:spacer width="63"/>
        <h:panelGrid columns="2" width="100%" columnClasses="direita, esquerda" style="font-size: 12px">


            <h:outputText rendered="#{OperacaoContaControle.apresentarLote}"  
                          styleClass="tituloCampos" style="font-weight: normal;"
                          value="#{msg_aplic.prt_Finan_GestaoRecebiveis_descricao}"/>
            <h:inputText rendered="#{OperacaoContaControle.apresentarLote}"  id="descricaoDeposito" styleClass="campos" size="50" maxlength="50"
                         value="#{OperacaoContaControle.lote.descricao}"/>

            <h:outputText  rendered="#{!MovContaControle.exibirDataDeposito}" 
                           styleClass="tituloCampos" style="font-weight: normal;"
                           value="#{msg_aplic.prt_Finan_GestaoRecebiveis_movimentacaoDeposito}" />
            <h:outputText  rendered="#{MovContaControle.exibirDataDeposito}"  
                           styleClass="tituloCampos" style="font-weight: normal;"
                           value="#{msg_aplic.prt_Finan_GestaoRecebiveis_dataDeposito}" />
            <h:panelGroup styleClass="dateTimeCustom"
                          layout="block"
                          style="margin-top: 8px;">
                <rich:calendar id="dataLancamento"
                           value="#{OperacaoContaControle.dataDeposito}"
                           inputSize="10"
                           inputClass="form"
                           oninputblur="blurinput(this);"
                           oninputfocus="focusinput(this);"
                           oninputchange="return validar_Data(this.id);"
                           datePattern="dd/MM/yyyy"
                           enableManualInput="true"
                           zindex="2"
                           buttonIcon="/imagens_flat/calendar-button.svg"
                           showWeeksBar="false"/>
                
            </h:panelGroup>
            

            <h:outputText rendered="#{MovContaControle.movContaVO.usuarioVO != null && MovContaControle.movContaVO.usuarioVO.codigo > 0 && MovContaControle.movContaVO.codigo > 0}"
                          styleClass="tituloCampos" style="font-weight: normal;" value="#{msg_aplic.prt_Finan_GestaoRecebiveis_dataDeposito}"/>
            <h:outputText rendered="#{MovContaControle.movContaVO.usuarioVO != null && MovContaControle.movContaVO.usuarioVO.codigo > 0 && MovContaControle.movContaVO.codigo > 0}" 
                          styleClass="tituloCampos" style="font-weight: normal;" value="#{MovContaControle.movContaVO.usuarioVO.nome}"/>

            <!-- empresa -->
            <h:outputText rendered="#{MovContaControle.mostrarCampoEmpresa}" 
                          styleClass="tituloCampos" style="font-weight: normal;" 
                          value="#{msg_aplic.prt_Finan_Lancamento_empresa}"/>
            <h:panelGroup layout="block" style="margin-top: 5px;" styleClass="cb-container" rendered="#{MovContaControle.mostrarCampoEmpresa}">
                <h:selectOneMenu
                                id="empresa" 
                                value="#{MovContaControle.movContaVO.empresaVO.codigo}">
                   <f:selectItems value="#{MovContaControle.listaSelectItemEmpresa}"/>
                   <a4j:support event="onchange" action="#{MovContaControle.verificarPreenchimentoCampoEmpresa}"
                                reRender="pessoa, suggestionPessoa, nomePessoaLabel, contaSelectitem"/>
               </h:selectOneMenu>
            </h:panelGroup>
            

            <!-- pessoa -->
            <h:outputText styleClass="tituloCampos" style="font-weight: normal;"
                          rendered="#{MovContaControle.botoesCamposEnabled or MovContaControle.exibirFavorecidoMovimentacaoGestaoRecebiveis}"
                          value="#{MovContaControle.nomeCampoPessoa}"/>
            <h:panelGroup layout="block" style="margin-top: 5px;"
                          rendered="#{MovContaControle.botoesCamposEnabled or MovContaControle.exibirFavorecidoMovimentacaoGestaoRecebiveis}">

                <h:inputText id="pessoa" size="50" maxlength="50"
                             rendered="#{MovContaControle.botoesCamposEnabled or MovContaControle.exibirFavorecidoMovimentacaoGestaoRecebiveis}"
                             onkeypress="if (event.keyCode == 13) { document.getElementById('form:descricao').focus();return false;};"
                             onfocus="focusinput(this);" styleClass="form" value="#{MovContaControle.movContaVO.pessoaVO.nome}">
                    <a4j:support event="onblur"
                                 oncomplete="#{MovContaControle.msgAlert} document.getElementById('form:descricao').focus();"
                                 action="#{MovContaControle.cadastrarNovaPessoa}"/>
                </h:inputText>

                <rich:suggestionbox height="200" width="650"
                                    for="pessoa"
                                    rendered="#{MovContaControle.botoesCamposEnabled or MovContaControle.exibirFavorecidoMovimentacaoGestaoRecebiveis}"
                                    fetchValue="#{result}"
                                    suggestionAction="#{MovContaControle.executarAutocompleteConsultaPessoa}"
                                    minChars="1" rowClasses="20"
                                    status="true"
                                    nothingLabel="Nenhuma pessoa encontrada!"
                                    var="result" id="suggestionPessoa">
                    <a4j:support event="onselect" ignoreDupResponses="true" action="#{MovContaControle.selecionarPessoaSuggestionBox}" focus="descricao" reRender="formDeposito"/>

                    <h:column>
                        <f:facet name="header">
                            <h:outputText styleClass="textverysmall" value="Nome"/>
                        </f:facet>
                        <h:outputText value="#{result.nome}"/>
                    </h:column>
                    <h:column>
                        <f:facet name="header">
                            <h:outputText styleClass="textverysmall" value="Tipo"/>
                        </f:facet>
                        <h:outputText value="#{result.tipoPessoa}"/>
                    </h:column>
                    <h:column>
                        <f:facet name="header">
                            <h:outputText styleClass="textverysmall" value="CPF/CNPJ"/>
                        </f:facet>
                        <h:outputText value="#{result.cfp}"/>
                    </h:column>
                    <h:column>
                        <f:facet name="header">
                            <h:outputText styleClass="textverysmall" value="Empresa"/>
                        </f:facet>
                        <h:outputText value="#{result.nomeEmpresa}"/>
                    </h:column>
                </rich:suggestionbox>
                <rich:spacer width="15px"/>
            </h:panelGroup>

            <!-- descricao -->
            <h:panelGroup rendered="#{MovContaControle.botoesCamposEnabled || !OperacaoContaControle.apresentarLote}">
                <h:outputText styleClass="tituloCampos" style="font-weight: normal;" value="#{msg_aplic.prt_Finan_Lancamento_descricao}"/>
            </h:panelGroup>
            <h:panelGroup rendered="#{MovContaControle.botoesCamposEnabled || !OperacaoContaControle.apresentarLote}">
                <h:inputText id="descricao" size="50" maxlength="100" onblur="blurinput(this);" onfocus="focusinput(this);"
                             styleClass="form" value="#{MovContaControle.movContaVO.descricao}"/>
            </h:panelGroup>

            <h:panelGroup>
                <table align="right" style="vertical-align: top; font-size: 12px;" width="100%" height="100%" cellspacing="0">
                    <!-- valor -->
                    <tr class="linhaPar" align="right">
                        <td style="padding-bottom: 7px; padding-top: 3px;">
                            <h:outputText styleClass="tituloCampos" style="font-weight: normal;" value="#{msg_aplic.prt_Finan_Lancamento_valor}"/>
                        </td>
                    </tr>
                    <c:if test="${MovContaControle.botoesCamposEnabled}">
                        <!-- data lan�amento -->
                        <tr class="linhaImPar" align="right">
                            <td style="padding-bottom: 7px; padding-top: 3px;">
                                <h:outputText styleClass="tituloCampos" style="font-weight: normal;" value="#{msg_aplic.prt_Finan_Lancamento_dataLancamento}"/>
                            </td>
                        </tr>
                        <!-- data vencimento -->
                        <tr class="linhaPar" align="right">
                            <td style="padding-bottom: 7px; padding-top: 3px;">
                                <h:outputText styleClass="tituloCampos" style="font-weight: normal;" value="#{msg_aplic.prt_Finan_Lancamento_dataVencimento}"/>
                            </td>
                        </tr>
                        <!-- data competencia -->
                        <tr class="linhaImPar" align="right">
                            <td style="padding-bottom: 7px; padding-top: 3px;">
                                <h:outputText styleClass="tituloCampos" style="font-weight: normal;" value="#{msg_aplic.prt_Finan_Lancamento_dataCompetencia}"/>
                            </td>
                        </tr>
                    </c:if>
                </table>
            </h:panelGroup>

            <h:panelGroup>
                <table align="left" style="vertical-align: top; font-size: 12px;" width="100%" height="100%" cellspacing="0">
                    <tr valign="top">
                        <td style="padding-bottom: 4px; padding-top: 4px;">
                            <table align="left" style="vertical-align: top; font-size: 12px;" width="100%" height="100%" cellspacing="0">
                                <!-- valor -->
                                <tr class="linhaPar" align="left" valign="top">
                                    <td>
                                        <h:inputText id="valor" style="width: 90px;" maxlength="40" onfocus="focusinput(this);"
                                                     onkeyup="return moeda(this);"
                                                     rendered="#{!MovContaControle.valorReadOnly}"
                                                     styleClass="form" value="#{MovContaControle.movContaVO.valor}">
                                            <a4j:support event="onblur" action="#{MovContaControle.removerRateiosSugeridos}"
                                                         rendered="#{MovContaControle.limparRateioSugerido}"
                                                         reRender="form:idCaixaCorpo" oncomplete="#{MovContaControle.msgAlert}"
                                                         ajaxSingle="false" focus="form:dataVencimentoInputDate"
                                                         status="false"/>
                                            <f:converter converterId="FormatadorNumerico"/>
                                        </h:inputText>

                                        <h:inputText id="valorRO" style="width: 90px;" maxlength="40" onfocus="focusinput(this);"
                                                     rendered="#{MovContaControle.valorReadOnly}"
                                                     readonly="true"
                                                     styleClass="form" value="#{MovContaControle.movContaVO.valor}"
                                                     title="#{msg_aplic.prt_Finan_GestaoRecebiveis_valorNaoAlterado}">
                                            <f:converter converterId="FormatadorNumerico"/>
                                        </h:inputText>
                                    </td>
                                </tr>
                                <c:if test="${MovContaControle.botoesCamposEnabled}">
                                    <!-- data lan�amento -->
                                    <tr class="linhaImPar" align="left">
                                        <td style="padding-bottom: 4px; padding-top: 4px;">
                                            <rich:calendar id="dtLancamento" rendered="#{MovContaControle.botoesCamposEnabled}"
                                                           value="#{MovContaControle.movContaVO.dataLancamento}"
                                                           inputSize="8"
                                                           inputClass="form"
                                                           oninputchange="return validar_DataFatura(this.id);"
                                                           oninputblur="blurinput(this);"
                                                           oninputfocus="focusinput(this);"
                                                           datePattern="dd/MM/yyyy"
                                                           enableManualInput="true"
                                                           showWeeksBar="false"/>
                                        </td>
                                    </tr>

                                    <!-- data vencimento -->
                                    <tr class="linhaPar" align="left">
                                        <td style="padding-bottom: 4px; padding-top: 4px;">
                                            <rich:calendar id="dataVencimento" rendered="#{MovContaControle.botoesCamposEnabled}"
                                                           value="#{MovContaControle.movContaVO.dataVencimento}"
                                                           inputSize="8"
                                                           onchanged="document.getElementById('form:dataCompetenciaInputDate').value=document.getElementById('form:dataVencimentoInputDate').value;"
                                                           oninputchange="if(validar_DataFatura(this.id)){document.getElementById('form:dataCompetenciaInputDate').value=document.getElementById('form:dataVencimentoInputDate').value;}"
                                                           inputClass="form"
                                                           oninputblur="blurinput(this);"
                                                           oninputfocus="focusinput(this);"
                                                           datePattern="dd/MM/yyyy"
                                                           enableManualInput="true"
                                                           showWeeksBar="false"/>
                                        </td>
                                    </tr>

                                    <!-- data competencia -->
                                    <tr class="linhaImPar" align="left">
                                        <td style="padding-bottom: 4px; padding-top: 4px;">
                                            <rich:calendar id="dataCompetencia"
                                                           value="#{MovContaControle.movContaVO.dataCompetencia}"
                                                           inputSize="8"
                                                           inputClass="form" rendered="#{MovContaControle.botoesCamposEnabled}"
                                                           oninputblur="blurinput(this);"
                                                           oninputfocus="focusinput(this);"
                                                           oninputchange="return validar_DataFatura(this.id);"
                                                           datePattern="dd/MM/yyyy"
                                                           enableManualInput="true"
                                                           zindex="2"
                                                           showWeeksBar="false"/>
                                        </td>
                                    </tr>
                                </c:if>
                            </table>
                        </td>
                        <td style="padding-left: 0;" valign="top">
                            <!-- observacoes -->
                            <h:outputText rendered="#{MovContaControle.botoesCamposEnabled || MovContaControle.conciliarSaldo}"
                                          styleClass="tituloCampos" style="font-weight: normal;" value="#{msg_aplic.prt_Finan_Lancamento_observacoes}"/><br/>
                            <h:inputTextarea style="height: 63px; resize: none;"
                                             rendered="#{MovContaControle.botoesCamposEnabled || MovContaControle.conciliarSaldo}"
                                             id="observacaoRichEditor"
                                             value="#{MovContaControle.movContaVO.observacoes}" rows="3" cols="31"/>
                        </td>
                    </tr>
                </table>
            </h:panelGroup>



            <!-- forma de pagamento -->
            <h:outputText rendered="#{!MovContaControle.existeMaisDeUmMovContaRateio 
                                      && !MovContaControle.conciliarSaldo && (OperacaoContaControle.tipoOperacao!= null && OperacaoContaControle.tipoOperacao.codigo != 4)
                                      && (ConfiguracaoFinanceiroControle.confFinanceiro.usarMovimentacaoContas
                                      || MovContaControle.botoesCamposEnabled)}" 
                                      styleClass="tituloCampos" style="font-weight: normal;"
                          value="#{msg_aplic.prt_Finan_Lancamento_formaPagamento}"/>
            <h:panelGroup layout="block" styleClass="cb-container" rendered="#{!MovContaControle.existeMaisDeUmMovContaRateio
                                         && !MovContaControle.conciliarSaldo
                                         && (OperacaoContaControle.tipoOperacao!= null && OperacaoContaControle.tipoOperacao.codigo != 4)
                                         && (ConfiguracaoFinanceiroControle.confFinanceiro.usarMovimentacaoContas
                                         || MovContaControle.botoesCamposEnabled)}">
                <h:selectOneMenu
                                 id="formaPagamento"
                                 value="#{MovContaControle.movContaRateioVO.formaPagamentoVO.codigo}">
                    <f:selectItems value="#{MovContaControle.listaSelectItemFormaPagamento}"/>
                    <a4j:support action="#{MovContaControle.selecionarContaCustodia(false)}" reRender="formDeposito"  event="onchange"/>
                </h:selectOneMenu>
            </h:panelGroup>

            <!-- data quitacao -->
            <h:panelGroup rendered="#{!MovContaControle.movContaVO.pagar_Apresentar && MovContaControle.botoesCamposEnabled}">
                <h:outputText styleClass="tituloCampos" style="font-weight: normal;" value="#{msg_aplic.prt_Finan_Lancamento_dataQuitacao}"/>
            </h:panelGroup>
            <h:panelGroup id="quitacao" rendered="#{!MovContaControle.movContaVO.pagar_Apresentar && MovContaControle.botoesCamposEnabled}">
                <h:inputText id="dataQuitacao"
                             style="width: 130px;"
                             styleClass="form" value="#{MovContaControle.movContaVO.dataQuitacao_Apresentar}"
                             rendered="#{!MovContaControle.movContaVO.pagar_Apresentar}"
                             readonly="true"/>

                <rich:spacer width="10px;"/>
                <a4j:commandLink rendered="#{!MovContaControle.movContaVO.pagar_Apresentar
                                             && MovContaControle.botoesCamposEnabled
                                             && not empty MovContaControle.movContaVO.conjuntoPagamento}"
                                 value="#{msg_aplic.prt_Finan_GestaoRecebiveis_lancamentoPagoConjuntos}"
                                 reRender="modalVerPagamentoConjunto"
                                 oncomplete="#{MovContaControle.msgAlert}"
                                 action="#{MovContaControle.verConjuntoPagamentos}">
                </a4j:commandLink>
                <h:outputText rendered="#{!MovContaControle.movContaVO.pagar_Apresentar
                                          && MovContaControle.botoesCamposEnabled
                                          && not empty MovContaControle.movContaVO.conjuntoPagamento}" value="/ "/>

                <a4j:commandLink rendered="#{!MovContaControle.movContaVO.pagar_Apresentar && MovContaControle.botoesCamposEnabled}"
                                 value="#{MovContaControle.nomeEstornar}"
                                 status="false"
                                 reRender="form:idCaixaCorpo"
                                 action="#{MovContaControle.verificarPermissaoEstorno}"
                                 oncomplete="#{MovContaControle.onCompleteEstornarPagamento}">
                </a4j:commandLink>

            </h:panelGroup>

            <!-- tipo de documento -->
            <h:outputText  rendered="#{!MovContaControle.existeMaisDeUmMovContaRateio && MovContaControle.botoesCamposEnabled}" 
                           styleClass="tituloCampos" style="font-weight: normal;"
                           value="#{msg_aplic.prt_Finan_Lancamento_tipoDocumento}"/>
            <h:panelGroup layout="block" styleClass="cb-container" rendered="#{!MovContaControle.existeMaisDeUmMovContaRateio && MovContaControle.botoesCamposEnabled}">
                <h:selectOneMenu style="width: 130px;"
                                 id="tipoDocumento"
                                 value="#{MovContaControle.movContaRateioVO.tipoDocumentoVO.codigo}">
                    <f:selectItems value="#{MovContaControle.listaSelectItemTipoDocumento}"/>
                </h:selectOneMenu>
            </h:panelGroup>
            


            <!-- plano de contas -->
            <h:outputText rendered="#{!MovContaControle.existeMaisDeUmMovContaRateio && MovContaControle.botoesCamposEnabled}" 
                          styleClass="tituloCampos" style="font-weight: normal;"
                          value="#{msg_aplic.prt_Finan_Lancamento_planoContas}"/>
            <h:panelGroup rendered="#{!MovContaControle.existeMaisDeUmMovContaRateio && MovContaControle.botoesCamposEnabled}">
                <table cellpadding="0" cellspacing="0">
                    <tr valign="top">
                        <td>
                            <%@include file="include_SuggestionPlanoConta.jsp" %>
                        </td>
                        <td> &nbsp;<a4j:commandLink action="#{PlanoContasControle.verificarConsultaLancamento}"
                                         reRender="modalPlanos"
                                         id="btAddPlano" value="Consultar"
                                         oncomplete="Richfaces.showModalPanel('modalPlanos')"/></td>
                    </tr>
                </table>
            </h:panelGroup>

            <!-- centro de custos -->

            <h:outputText rendered="#{!MovContaControle.existeMaisDeUmMovContaRateio && MovContaControle.botoesCamposEnabled}" 
                          styleClass="tituloCampos" style="font-weight: normal;"
                          value="#{msg_aplic.prt_Finan_Lancamento_centroCusto}"/>
            <h:panelGroup rendered="#{!MovContaControle.existeMaisDeUmMovContaRateio && MovContaControle.botoesCamposEnabled}">
                <table cellpadding="0" cellspacing="0">
                    <tr valign="top">
                        <td>
                            <%@include file="include_SuggestionCentroCusto.jsp" %>
                        </td>
                        <td> &nbsp;<a4j:commandLink reRender="modalCentros"
                                         id="btAddCentro" value="Consultar"
                                         oncomplete="Richfaces.showModalPanel('modalCentros')"/></td>
                    </tr>
                </table>
            </h:panelGroup>

            <h:outputText
                rendered="#{!MovContaControle.botoesCamposEnabled && MovContaControle.usarMovimentacao && MovContaControle.movContaVO.codigo == 0}"
                styleClass="tituloCampos" style="font-weight: normal;" value="Conta:"/>
            <h:panelGroup
                rendered="#{!MovContaControle.botoesCamposEnabled && MovContaControle.usarMovimentacao && MovContaControle.movContaVO.codigo == 0}">
                <h:panelGroup layout="block" styleClass="cb-container"
                              style="margin-bottom: 5px;margin-top: 5px;">
                    <h:selectOneMenu  value="#{MovContaControle.movContaVO.contaVO.codigo}"
                                    id="contaSelectitem"
                                    styleClass="form">
                      <f:selectItems value="#{MovContaControle.listaComboConta}"/>
                      <a4j:support action="#{MovContaControle.selecionarContaCustodia(false)}" reRender="formDeposito,taxaDoPix,formDeposito:valorLiquido" event="onchange"/>
                  </h:selectOneMenu>
                </h:panelGroup>
                
            </h:panelGroup>
            <h:outputText styleClass="tituloCampos" style="font-weight: normal;display: block;" rendered="#{MovContaControle.apresentarLabelCartao}" value="#{msg_aplic.prt_FormaPagamento_taxaCartao}"/>
            <h:outputText id="labelTaxaPix" styleClass="tituloCampos" style="font-weight: normal;display: block;" rendered="#{MovContaControle.apresentarLabelPix}" value="#{msg_aplic.prt_FormaPagamento_taxaPix}"/>
            <h:panelGroup rendered="#{MovContaControle.apresentarValorLiquido}">
                <h:outputText rendered="#{MovContaControle.apresentarLabelCartao}"
                              id="taxaCartao"
                              styleClass="tituloCampos"
                              value="#{MovContaControle.formapagamentoVO.taxaCartaoApresentar}">
                </h:outputText>
                <h:outputText rendered="#{MovContaControle.apresentarLabelPix}"
                              id="taxaDoPix"
                              styleClass="tituloCampos"
                              value="#{MovContaControle.taxaPixApresentar}">
                </h:outputText>
            </h:panelGroup>

            <h:outputText styleClass="tituloCampos" rendered="#{MovContaControle.apresentarLabelAntecipacao && MovContaControle.apresentarValorLiquido}"
                          style="font-weight: normal;display: block" value="#{msg_aplic.prt_FormaPagamento_taxaAntecipacao}"/>
            <h:panelGroup rendered="#{MovContaControle.apresentarLabelAntecipacao && MovContaControle.apresentarValorLiquido}">
                <h:outputText styleClass="tituloCampos" id="taxaAntecipacao" style="display: block"
                              value="#{MovContaControle.movContaVO.taxaAntecipacaoApresentar}">
                </h:outputText>
            </h:panelGroup>

            <h:outputText styleClass="tituloCampos" style="font-weight: normal;" rendered="#{MovContaControle.apresentarValorLiquido}"
                          value="#{msg_aplic.prt_Finan_Lancamento_valorLiquido}"/>
            <h:panelGroup rendered="#{MovContaControle.apresentarValorLiquido}">
                <h:inputText id="valorLiquido" style="width: 90px;" maxlength="40" onfocus="focusinput(this);"
                             onkeypress="return(currencyFormat(this,'.',',',event));"
                             styleClass="form" value="#{MovContaControle.movContaVO.valorLiquido}">
                    <f:converter converterId="FormatadorNumerico"/>
                </h:inputText>
            </h:panelGroup>

            <h:outputText styleClass="tituloCampos" style="font-weight: normal;" rendered="#{MovContaControle.apresentarTaxaBoleto}" value="Taxa do Boleto:"/>
            <h:panelGroup rendered="#{MovContaControle.apresentarTaxaBoleto}">
                <c:if test="${MovContaControle.taxaBoletoVO.tipo == 1}">
                    <h:inputText id="taxaBoletoPercentual" disabled="true" style="width: 90px;" maxlength="40" onfocus="focusinput(this);"
                                 onkeypress="return(currencyFormat(this,'.',',',event));"
                                 styleClass="form" value="#{MovContaControle.taxaBoletoVO.taxa}">
                        <f:converter converterId="FormatarPercentual"/>
                    </h:inputText>
                </c:if>
                <c:if test="${MovContaControle.taxaBoletoVO.tipo == 2}">
                    <h:inputText id="taxaBoletoValor" disabled="true" style="width: 90px;" maxlength="40" onfocus="focusinput(this);"
                                 onkeypress="return(currencyFormat(this,'.',',',event));"
                                 styleClass="form" value="#{MovContaControle.taxaBoletoVO.taxa}">
                        <f:converter converterId="FormatadorNumerico"/>
                    </h:inputText>
                </c:if>
            </h:panelGroup>
            <h:outputText styleClass="tituloCampos" style="font-weight: normal;" rendered="#{MovContaControle.apresentarTaxaBoleto}"
                          value="#{msg_aplic.prt_Finan_Lancamento_valorLiquido}"/>
            <h:panelGroup rendered="#{MovContaControle.apresentarTaxaBoleto}">
                <h:inputText id="Boleto" style="width: 90px;" maxlength="40" onfocus="focusinput(this);"
                             onkeypress="return(currencyFormat(this,'.',',',event));"
                             styleClass="form" value="#{MovContaControle.movContaVO.valorLiquido}">
                    <f:converter converterId="FormatadorNumerico"/>
                </h:inputText>
            </h:panelGroup>

            <h:outputText
                rendered="#{MovContaControle.movContaVO.apresentarRecebido && ConfiguracaoFinanceiroControle.confFinanceiro.usarMovimentacaoContas}"
                styleClass="tituloCampos" style="font-weight: normal;" value="#{msg_aplic.prt_Finan_GestaoRecebiveis_apresentarCaixa}:"/>
            <h:selectBooleanCheckbox value="#{MovContaControle.movContaVO.apresentarNoCaixa}"
                                     rendered="#{MovContaControle.movContaVO.apresentarRecebido
                                                 && ConfiguracaoFinanceiroControle.confFinanceiro.usarMovimentacaoContas}">
            </h:selectBooleanCheckbox>

            <h:outputText
                rendered="#{MovContaControle.loteUsado != null && MovContaControle.loteUsado.codigo > 0 && ConfiguracaoFinanceiroControle.confFinanceiro.usarMovimentacaoContas}"
                styleClass="tituloCampos" style="font-weight: normal;" value="#{msg_aplic.prt_Finan_Lancamentos_loteusadoparapagar}"/>
            <h:commandLink action="#{GestaoLotesControle.abrirEdicaoLote}"
                           actionListener="#{GestaoLotesControle.listenerEdicaoLote}"
                           rendered="#{MovContaControle.loteUsado != null && MovContaControle.loteUsado.codigo > 0 && ConfiguracaoFinanceiroControle.confFinanceiro.usarMovimentacaoContas}">
                <h:outputText styleClass="tituloCampos" style="font-weight: normal; text-decoration: underline;"
                              value="#{MovContaControle.loteUsado.descLoteUsadoParaPagar_apresentar}"/>
                <f:attribute name="codigoLote" value="#{MovContaControle.loteUsado.codigo}"/>
            </h:commandLink>

            <h:outputText
                rendered="#{MovContaControle.movContaVO.lote != null && MovContaControle.movContaVO.lote.codigo > 0 && ConfiguracaoFinanceiroControle.confFinanceiro.usarMovimentacaoContas}"
                styleClass="tituloCampos" style="font-weight: normal;" value="Lote:"/>

            <h:commandLink action="#{GestaoLotesControle.abrirEdicaoLote}"
                           actionListener="#{GestaoLotesControle.listenerEdicaoLote}"
                           rendered="#{MovContaControle.movContaVO.lote != null && MovContaControle.movContaVO.lote.codigo > 0 && ConfiguracaoFinanceiroControle.confFinanceiro.usarMovimentacaoContas}">
                <h:outputText styleClass="tituloCampos" style="font-weight: normal; text-decoration: underline;"
                              value="#{MovContaControle.movContaVO.lote.codigo}"/>
                <f:attribute name="codigoLote" value="#{MovContaControle.movContaVO.lote.codigo}"/>
            </h:commandLink>

            <h:outputText rendered="#{MovContaControle.movContaVO.caixa != null && MovContaControle.movContaVO.caixa > 0}"
                          styleClass="tituloCampos" style="font-weight: normal;" value="Pertence ao caixa:"/>
            <h:commandLink action="#{MovContaControle.visualizarCaixa}"
                           rendered="#{MovContaControle.movContaVO.caixa != null && MovContaControle.movContaVO.caixa > 0}">
                <h:outputText styleClass="tituloCampos" style="font-weight: normal; text-decoration: underline;"
                              value="#{MovContaControle.movContaVO.caixa}"/>
            </h:commandLink>
        </h:panelGrid>


        <h:panelGrid columns="1" width="100%" columnClasses="centralizado" style="margin-top: 10px;">
            <h:panelGroup>
                <a4j:commandLink action="#{OperacaoContaControle.gravarOperacaoConta}"
                                   reRender="form:idCaixaCorpo, formDeposito"
                                   id="btnOKModalOperacaoComRecebiveis"
                                   oncomplete="#{OperacaoContaControle.onCompleteModalOperacao}"
                                   styleClass="pure-button pure-button-primary">
                    OK
                </a4j:commandLink>
                <rich:spacer width="10"/>
                <a4j:commandLink reRender="panelMensagemOp, formDeposito" 
                                 styleClass="pure-button"
                                 oncomplete="Richfaces.hideModalPanel('panelDeposito')">
                    Fechar
                </a4j:commandLink>
            </h:panelGroup>
        </h:panelGrid>
        <h:panelGrid id="panelMensagemOp" columns="1" width="100%" >
            <h:panelGrid columns="2" width="100%" >
                <h:panelGrid columns="1" width="100%">

                    <h:outputText value=" "/>

                </h:panelGrid>
                <h:panelGrid columns="1" width="100%">
                    <h:outputText styleClass="mensagem" value="#{OperacaoContaControle.mensagem}"/>
                    <h:outputText styleClass="mensagemDetalhada" value="#{OperacaoContaControle.mensagemDetalhada}"/>
                </h:panelGrid>
            </h:panelGrid>
        </h:panelGrid>
    </a4j:form>
</rich:modalPanel>

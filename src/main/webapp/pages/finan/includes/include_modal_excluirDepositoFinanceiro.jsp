<%@page contentType="text/html" pageEncoding="ISO-8859-1" %>
<%@include file="imports.jsp" %>
<a4j:outputPanel>
    <rich:modalPanel id="modalExcluirDepositoFinanceiro" autosized="true" styleClass="novaModal"
                     minWidth="650" height="190" width="850" top="30" shadowOpacity="true">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Informações sobre o Depósito"></h:outputText>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText
                        styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign "
                        id="hidelinkExcluirDep"/>
                <rich:componentControl for="modalExcluirDepositoFinanceiro" attachTo="hidelinkExcluirDep"
                                       operation="hide" event="onclick"/>
            </h:panelGroup>
        </f:facet>
        <h:form id="formExcluirDepositoFinanceiro">
            <h:panelGrid id="panelCamposExcluir" columns="2" rowClasses="linhaImpar, linhaPar"
                         columnClasses="classEsquerda, classDireita" width="100%">

                <!-- empresa -->
                <h:outputText styleClass="tituloCampos" value="Empresa:"/>
                <h:outputText style="margin-left: 2px" styleClass="tituloCampos"
                              value="#{GestaoRecebiveisControle.movContaVOExcluirDeposito.empresaVO.nome}"/>

                <!-- pessoa -->
                <h:outputText style="display: flow; margin-top: 3px;" styleClass="tituloCampos" value="Pagar Para:"/>
                <h:outputText style="display: flow; float: left; margin-top: 3px; margin-left: 2px"
                              styleClass="tituloCampos"
                              value="#{GestaoRecebiveisControle.movContaVOExcluirDeposito.pessoaVO.nome}"/>

                <!-- responsável -->
                <h:outputText style="display: flow; margin-top: -3px;" styleClass="tituloCampos" value="Responsável:"/>
                <h:outputText style="display: flow; float: left; margin-top: -3px; margin-left: 2px"
                              styleClass="tituloCampos"
                              value="#{GestaoRecebiveisControle.movContaVOExcluirDeposito.usuarioVO.nome}"/>

                <!-- conta -->
                <h:outputText style="display: flow; margin-top: -3px;" styleClass="tituloCampos" value="Conta:"/>
                <h:outputText style="display: flow; float: left; margin-top: -3px; margin-left: 2px"
                              styleClass="tituloCampos"
                              value="#{GestaoRecebiveisControle.movContaVOExcluirDeposito.contaVO.descricao}"/>

                <!-- plano de contas -->
                <h:outputText rendered="#{not empty MovContaControle.movContaRateioVO.planoContaVO.descricao}"
                              style="display: flow; margin-top: -3px;" styleClass="tituloCampos"
                              value="Plano de Contas:"/>
                <h:outputText rendered="#{not empty MovContaControle.movContaRateioVO.planoContaVO.descricao}"
                              style="display: flow; float: left; margin-top: -3px; margin-left: 2px"
                              styleClass="tituloCampos"
                              value="#{MovContaControle.movContaRateioVO.planoContaVO.descricao}"/>

                <!-- centro de custos -->
                <h:outputText rendered="#{not empty MovContaControle.movContaRateioVO.centroCustoVO.descricao}"
                              style="display: flow; margin-top: -3px;" styleClass="tituloCampos"
                              value="Centro de Custo:"/>
                <h:outputText rendered="#{not empty MovContaControle.movContaRateioVO.centroCustoVO.descricao}"
                              style="display: flow; float: left; margin-top: -3px; margin-left: 2px"
                              styleClass="tituloCampos"
                              value="#{MovContaControle.movContaRateioVO.centroCustoVO.descricao}"/>

                <!-- forma de pagamento -->
                <h:outputText style="display: flow; margin-top: -3px;" styleClass="tituloCampos"
                              value="Forma de Pagamento:"/>
                <h:outputText style="display: flow; float: left; margin-top: -3px; margin-left: 2px"
                              styleClass="tituloCampos"
                              value="#{GestaoRecebiveisControle.movContaRateioVOExcluirDeposito.formaPagamentoVO.descricao}"/>

                <!-- tipo de documento -->
                <h:outputText style="display: flow; margin-top: -3px;"
                              rendered="#{not empty GestaoRecebiveisControle.movContaRateioVOExcluirDeposito.tipoDocumentoVO.descricao}"
                              styleClass="tituloCampos" value="Tipo de Documento:"/>
                <h:outputText style="display: flow; float: left; margin-top: -3px; margin-left: 2px"
                              rendered="#{not empty GestaoRecebiveisControle.movContaRateioVOExcluirDeposito.tipoDocumentoVO.descricao}"
                              styleClass="tituloCampos"
                              value="#{GestaoRecebiveisControle.movContaRateioVOExcluirDeposito.tipoDocumentoVO.descricao}"/>

                <!-- descricao -->
                <h:outputText style="display: flow; margin-top: -3px;" styleClass="tituloCampos" value="Descrição:"/>
                <h:outputText style="display: flow; float: left; margin-top: -3px; margin-left: 2px"
                              styleClass="tituloCampos"
                              value="#{GestaoRecebiveisControle.movContaVOExcluirDeposito.descricao}"/>

                <!-- valor -->
                <h:outputText style="display: flow; margin-top: -3px;" styleClass="tituloCampos" value="Valor:"/>
                <h:outputText style="display: flow; float: left; margin-top: -3px; margin-left: 2px"
                              styleClass="tituloCampos"
                              value="#{GestaoRecebiveisControle.movContaVOExcluirDeposito.valor}">
                    <f:converter converterId="FormatadorNumerico"/>
                </h:outputText>

                <!-- data quitacao -->
                <h:outputText style="display: flow; margin-top: -3px;" styleClass="tituloCampos"
                              value="Data de Quitação:"/>
                <h:outputText style="display: flow; float: left; margin-top: -3px; margin-left: 2px"
                              styleClass="tituloCampos"
                              value="#{GestaoRecebiveisControle.movContaVOExcluirDeposito.dataQuitacao_Apresentar}"/>

                <!-- caixa -->
                <h:outputText style="display: flow; margin-top: -3px;" styleClass="tituloCampos" value="Caixa:"/>
                <h:panelGrid columns="2" style="display: inline">
                    <h:outputText style="display: flow; float: left; margin-top: -3px; margin-left: 2px"
                                  styleClass="tituloCampos tooltipster"
                                  title="#{GestaoRecebiveisControle.titleCaixaDiferente}"
                                  value="#{GestaoRecebiveisControle.movContaVOExcluirDeposito.caixa}">
                    </h:outputText>
                    <h:graphicImage id="iconWarningCaixaDiferente"
                                    rendered="#{GestaoRecebiveisControle.caixaAtualDiferenteDoCaixaDoDepositoExcluir}"
                                    styleClass="tooltipster"
                                    title="#{GestaoRecebiveisControle.titleCaixaDiferenteIcon}"
                                    style="inline-size: 18px; margin-top: -9px; margin-left: 3px;"
                                    value="../../../images/warning.png"/>
                </h:panelGrid>

                <!-- observacoes -->
                <h:outputText styleClass="tituloCampos"
                              style="display: flow; margin-top: -3px;"
                              rendered="#{not empty GestaoRecebiveisControle.movContaVOExcluirDeposito.observacoes_Apresentar}"
                              value="Observações:"/>
                <h:panelGroup styleClass="classEsquerda"
                              rendered="#{not empty GestaoRecebiveisControle.movContaVOExcluirDeposito.observacoes_Apresentar}">
                    <h:outputText styleClass="tituloCampos"
                                  style="display: flow; float: left; margin-top: -3px; margin-left: 2px"
                                  escape="false"
                                  value="#{GestaoRecebiveisControle.movContaVOExcluirDeposito.observacoes_Apresentar}"/>
                </h:panelGroup>
            </h:panelGrid>

            <rich:dataTable rowClasses="linhaPar,linhaImpar" rowKeyVar="status" id="tblTransacoesDepExcluir"
                            styleClass="tabelaDados font-size-Em"
                            onRowMouseOver="this.addClassName('background-color-tabelaDados-over')"
                            onRowMouseOut="this.removeClassName('background-color-tabelaDados-over')"
                            value="#{GestaoRecebiveisControle.listaMovPagamentoExcluirDeposito}"
                            rendered="#{GestaoRecebiveisControle.listaMovPagamentoExcluirDepositoPaginada.count > 0}"
                            var="item">

                <%@include file="/pages/ce/includes/include_contador_richtable.jsp" %>

                <rich:column sortBy="#{item.matriculaPagador}" style="text-transform: none;">
                    <f:facet name="header">
                        <h:outputText value="Matrícula"
                                      style="font-size: 11px;"
                                      styleClass="tooltipster"/>
                    </f:facet>
                    <h:outputText styleClass="tooltipster"
                                  style="font-size: 11px;"
                                  value="#{item.matriculaPagador}"/>
                </rich:column>

                <rich:column sortBy="#{item.nomePagador}" style="text-transform: none;">
                    <f:facet name="header">
                        <h:outputText value="Nome Pagador"
                                      style="font-size: 11px;"
                                      styleClass="tooltipster"/>
                    </f:facet>
                    <h:outputText styleClass="tooltipster"
                                  style="font-size: 11px;"
                                  value="#{item.nomePagador}"/>
                </rich:column>

                <rich:column sortBy="#{item.cpfPagador}" style="text-transform: none;">
                    <f:facet name="header">
                        <h:outputText value="CPF"
                                      style="font-size: 11px;"
                                      styleClass="tooltipster"/>
                    </f:facet>
                    <h:outputText styleClass="tooltipster"
                                  style="font-size: 11px;"
                                  value="#{item.cpfPagador}"/>
                </rich:column>

                <rich:column sortBy="#{item.valor}" style="text-transform: none;">
                    <f:facet name="header">
                        <h:outputText value="Valor"
                                      style="font-size: 11px;"
                                      styleClass="tooltipster"/>
                    </f:facet>
                    <h:outputText styleClass="tooltipster"
                                  style="font-size: 11px;"
                                  value="R$ #{item.valorApresentar}"/>
                </rich:column>

                <rich:column sortBy="#{item.contaFinanceiro}" style="text-transform: none;">
                    <f:facet name="header">
                        <h:outputText value="Conta"
                                      style="font-size: 11px;"
                                      styleClass="tooltipster"/>
                    </f:facet>
                    <h:outputText styleClass="tooltipster"
                                  style="font-size: 11px;"
                                  value="#{item.contaFinanceiro}"/>
                </rich:column>

                <rich:column sortBy="#{item.dataMovimento}" style="text-transform: none;">
                    <f:facet name="header">
                        <h:outputText value="Dt. Movimentação"
                                      style="font-size: 11px;"
                                      styleClass="tooltipster"/>
                    </f:facet>
                    <h:outputText styleClass="tooltipster"
                                  style="font-size: 11px;"
                                  value="#{item.dataMovimento_Apresentar}"/>
                </rich:column>

            </rich:dataTable>

            <h:panelGrid columns="1"
                         width="100%"
                         columnClasses="colunaCentralizada">
                <table width="100%" border="0" cellspacing="0" cellpadding="0">
                    <tr>
                        <td align="center" valign="middle">
                            <h:panelGroup layout="block"
                                          styleClass="paginador-container">
                                <h:panelGroup styleClass="pull-left"
                                              layout="block">
                                    <h:outputText
                                            styleClass="texto-size-14 cinza"
                                            value="Total #{GestaoRecebiveisControle.listaMovPagamentoExcluirDepositoPaginada.count} itens"></h:outputText>
                                </h:panelGroup>
                                <h:panelGroup layout="block"
                                              style="align-items: center;position: absolute;margin-left: 34%;">
                                    <a4j:commandLink styleClass="linkPadrao texto-cor-azul texto-size-20-real"
                                                     reRender="formExcluirDepositoFinanceiro"
                                                     actionListener="#{GestaoRecebiveisControle.primeiraPagina}">
                                        <i class="fa-icon-double-angle-left"></i>
                                        <f:attribute name="tipo" value="LISTA_MOVPAGAMENTO_EXCLUIR_DEPOSITO"/>
                                    </a4j:commandLink>

                                    <a4j:commandLink styleClass="linkPadrao texto-cor-azul texto-size-20-real"
                                                     reRender="formExcluirDepositoFinanceiro"
                                                     actionListener="#{GestaoRecebiveisControle.paginaAnterior}">
                                        <i class="fa-icon-angle-left"></i>
                                        <f:attribute name="tipo" value="LISTA_MOVPAGAMENTO_EXCLUIR_DEPOSITO"/>
                                    </a4j:commandLink>

                                    <h:outputText styleClass="texto-font texto-cor-cinza texto-size-16-real"
                                                  value="#{msg_aplic.prt_msg_pagina} #{GestaoRecebiveisControle.listaMovPagamentoExcluirDepositoPaginada.paginaAtualApresentar}"
                                                  rendered="true"/>
                                    <a4j:commandLink
                                            styleClass="linkPadrao texto-font texto-cor-azul texto-size-20-real"
                                            reRender="formExcluirDepositoFinanceiro"
                                            actionListener="#{GestaoRecebiveisControle.proximaPagina}">
                                        <i class="fa-icon-angle-right"></i>
                                        <f:attribute name="tipo" value="LISTA_MOVPAGAMENTO_EXCLUIR_DEPOSITO"/>
                                    </a4j:commandLink>

                                    <a4j:commandLink styleClass="linkPadrao texto-cor-azul texto-size-20-real"
                                                     reRender="formExcluirDepositoFinanceiro"
                                                     actionListener="#{GestaoRecebiveisControle.ultimaPagina}">
                                        <i class="fa-icon-double-angle-right"></i>
                                        <f:attribute name="tipo" value="LISTA_MOVPAGAMENTO_EXCLUIR_DEPOSITO"/>
                                    </a4j:commandLink>
                                </h:panelGroup>
                            </h:panelGroup>
                        </td>
                    </tr>
                </table>
            </h:panelGrid>

            <h:panelGroup id="btnExcluirDep" style="display: block">
                <a4j:commandButton
                        title="Excluir Depósito com todos os lançamentos listados acima"
                        reRender="modalPerguntaExcluirDepositoFinanceiro"
                        id="btnExcluirDepFinanceiro"
                        action="#{GestaoRecebiveisControle.modalConfirmacaoExclusaoDeposito}"
                        value="Excluir depósito"
                        style="background-color: #BC2525; float: right;"
                        styleClass="botoes nvoBt botaoExcluir btPerigo tooltipster"
                        oncomplete="#{GestaoRecebiveisControle.abrirFecharModalPerguntaExclusaoDeposito}"/>
            </h:panelGroup>

        </h:form>
    </rich:modalPanel>
</a4j:outputPanel>


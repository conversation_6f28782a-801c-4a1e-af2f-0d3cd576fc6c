<%--
  Created by IntelliJ IDEA.
  User: Rafael
  Date: 16/03/2016
  Time: 11:01
  To change this template use File | Settings | File Templates.
--%>

<%@page contentType="text/html"%>
<%@page pageEncoding="ISO-8859-1"%>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<f:loadBundle var="msg_menu" basename="propriedades.Menu"/>
<!-- inicio box -->
<h:panelGroup layout="block" styleClass="menuLateral">

  <jsp:include page="./../../../includes/include_menulateral_acessorapido.jsp" flush="true" />

  <h:panelGroup layout="block" styleClass="grupoMenuLateral">
    <h:panelGroup layout="block" styleClass="grupoMenuTitulo">
      <i class="fa-icon-briefcase"></i> Cadastros
    </h:panelGroup>

    <h:panelGroup layout="block" styleClass="grupoMenuItem">

      <a href="telaCadastroAuxiliares.jsp" class="titulo3 linkFuncionalidade">
        <h:outputText id="CadAuxFinan" value="Cadastros Auxiliares"/>
      </a>
      <h:outputLink styleClass="linkWiki"
                    value="#{SuperControle.urlWiki}Cadastros:Cadastros_Auxiliares"
                    title="Clique e saiba mais: Cadastros Auxiliares"
                    target="_blank">
        <i class="fa-icon-question-sign" style="font-size: 18px"></i>
      </h:outputLink>

    </h:panelGroup>

    <h:panelGroup layout="block" styleClass="grupoMenuItem">

      <a href="telaCadastroConfigFinanceira.jsp" class="titulo3 linkFuncionalidade">
        <h:outputText id="ConfFinan" value="Config. financeiras"/>
      </a>
      <h:outputLink styleClass="linkWiki"
                    value="#{SuperControle.urlWiki}Cadastros:Config._Financeiras"
                    title="Clique e saiba mais: Config. Financeiras"
                    target="_blank">
        <i class="fa-icon-question-sign" style="font-size: 18px"></i>
      </h:outputLink>

    </h:panelGroup>

  </h:panelGroup>

</h:panelGroup>
<%--
    Document   : include_TreeViewDF
    Created on : 16/03/2020, 16:49:37
    Author     : <PERSON>
--%>
<%@include file="../includes/include_imports.jsp" %>
<!-- ------------------------ <PERSON>�<PERSON> Movimenta��es OpenBank Resumo de Contas ------------------------------- -->
<rich:dataTable
        value="#{GerenciadorContaControle.listaMovimentacoesOpenBank}"
        id="listaExt"
        var="extratoOpenBank"
        width="100%"
        headerClass="consulta"
        columnClasses=""
        styleClass="tabelaSimplesCustom showCellEmpty tabela-zebra"
        stateVar="status"
        rendered="#{not empty GerenciadorContaControle.listaMovimentacoesOpenBank and GerenciadorContaControle.contaSelecionada.contaIntegracaoOpenBank}">

    <rich:column headerClass="texto-cor-cinza" style="text-align: center" sortBy="#{extratoOpenBank.dateCreate_Convertido}">
        <f:facet name="header">
            <h:outputText value="Data Opera��o"/>
        </f:facet>
        <h:outputText value="#{extratoOpenBank.dateCreate_Convertido}"/>
    </rich:column>

    <rich:column headerClass="texto-cor-cinza" style="text-align: center" sortBy="#{extratoOpenBank.tipoMovimento_Apresentar}">
        <f:facet name="header">
            <h:outputText value="Descri��o Movimenta��o"/>
        </f:facet>
        <h:outputText styleClass="tooltipster" title="#{extratoOpenBank.titleFormatadoStone}" value="#{extratoOpenBank.tipoMovimento_Apresentar}"/>
    </rich:column>

    <rich:column headerClass="texto-cor-cinza" style="text-align: center" sortBy="#{extratoOpenBank.operation}">
        <f:facet name="header">
            <h:outputText value="Opera��o"/>
        </f:facet>
        <h:outputText value="#{extratoOpenBank.operacao_Apresentar}"/>
        <h:outputText style="margin-left: 3px" styleClass="#{(extratoOpenBank.operation eq 'credit') ? 'fa-icon-arrow-up green' : 'fa-icon-arrow-down red'}"/>
    </rich:column>


    <rich:column headerClass="texto-cor-cinza" style="text-align: center" sortBy="#{extratoOpenBank.amout_calculado}">
        <f:facet name="header">
            <h:outputText value="Valor"/>
        </f:facet>
        <h:outputText value="#{MovPagamentoControle.empresaLogado.moeda} #{extratoOpenBank.amout_calculado}" style="white-space: nowrap;"/>
    </rich:column>

    <rich:column  headerClass="texto-cor-cinza" style="text-align: center" sortBy="#{extratoOpenBank.balanceAfter_calculado}">
        <f:facet name="header">
            <h:outputText value="Saldo"/>
        </f:facet>
        <h:outputText value="#{MovPagamentoControle.empresaLogado.moeda} #{extratoOpenBank.balanceAfter_calculado}" style="white-space: nowrap;"/>
    </rich:column>

    <rich:column  headerClass="texto-cor-cinza" style="text-align: center" sortBy="#{extratoOpenBank.status}">
        <f:facet name="header">
            <h:outputText value="Status"/>
        </f:facet>
        <h:outputText style="margin-left: 3px" styleClass="#{extratoOpenBank.status_Style}" title="#{extratoOpenBank.status_Title}"/>
    </rich:column>
</rich:dataTable>
<h:panelGroup layout="block">
    <h:outputText
            rendered="#{empty GerenciadorContaControle.listaMovimentacoesOpenBank and GerenciadorContaControle.contaSelecionada.contaIntegracaoOpenBank}"
            styleClass="tituloDemonstrativo"
            value="N�o existem movimenta��es nesta integra��o openbank para o per�odo informado."/>
</h:panelGroup>
<h:panelGroup id="totalizadorResultadosExtrato"
              layout="block"
              styleClass="container">
    <h:outputText
            style="padding-bottom: 10px;"
            rendered="#{fn:length(GerenciadorContaControle.listaMovimentacoesOpenBank) == 1}"
            styleClass="col-md-12 dataTables_info texto-cor-cinza"
            value="Total: #{fn:length(GerenciadorContaControle.listaMovimentacoesOpenBank)} item"/>
    <h:outputText
            style="padding-bottom: 10px;"
            rendered="#{fn:length(GerenciadorContaControle.listaMovimentacoesOpenBank) > 1}"
            styleClass="col-md-12 dataTables_info texto-cor-cinza"
            value="Total: #{fn:length(GerenciadorContaControle.listaMovimentacoesOpenBank)} itens"/>
</h:panelGroup>
<!-- ------------------------ Fim Movimenta��es OpenBank Resumo de Contas ------------------------------- -->




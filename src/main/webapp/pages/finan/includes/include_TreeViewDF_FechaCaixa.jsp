<%--
    Document   : include_TreeViewDF_FechaCaixa
    Created on : 12/04/2012, 16:49:37
    Author     : <PERSON><PERSON><PERSON>
--%>
  <!-- ------------------------ Come�<PERSON> relat�rio TreeView  ------------------------------- -->

<h:panelGroup id="panelTreeViewDF">
    <c:set var="tamColNomeAgrupador" value="70%" scope="request" />
    <c:set var="tamColNomeMes" value="10%" scope="request" />

    <c:if test="${not empty CaixaControle.listaDF}">
        <table width="100%">
            <tr>
                <td width="100%" style="text-align: center;">
                    &nbsp;
                    <a id="refExpandir" href="#posicaoDados" class="expandir"> Expandir Tudo </a>
                    &nbsp;
                    <a href="#posicaoDados" class="expandirUm"> Expandir </a>
                    &nbsp;
                    <a href="#posicaoDados" class="retrairUm"> Retrair </a>
                    &nbsp;
                    <a href="#posicaoDados" class="retrair"> Retrair Tudo </a>
                </td>
            </tr>
        </table>

        <table width="100%">
            <tr>
                <td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td>
                <td width="100%">
                    <table width="100%" cellpadding="3" cellspacing="0"
                           border="0" class="example" id="dnd-example">
                        <thead>
                            <tr>
                                <td width="${tamColNomeAgrupador}"></td>
                                <%-- Criar o cabe�alho para os totalizadores dos meses 
                                <c:forEach var="mes" items="${DemonstrativoFinanceiroControle.listaMesProcessar}">
                                    <th  align="right">
                                        <FONT COLOR=#333333 FACE="Arial,Helvetica,sans-serif" SIZE=2x> ${mes.nomeMes} </FONT>
                                    </th>
                                </c:forEach>--%>
                                <th align="right">
                                    <font size="2x" face="Arial,Helvetica,sans-serif" color="#333333"> Total </font>
                                </th>
                            </tr>
                        </thead>

                        <tbody>
                            <c:forEach var="df" varStatus="indice" items="${CaixaControle.listaDF}">
                                <%-- Definir as cores da tabela zebrada --%>
                                <c:choose>
                                    <c:when test="${indice.count % 2 == 0}">
                                        <c:set var="corLinha" value="#FFFFFF" scope="request" />
                                        <c:set var="corLinhaRessaltada" value="#93DB70" scope="request" />
                                    </c:when>

                                    <c:otherwise>
                                        <c:set var="corLinha" value="#DFE8EF" scope="request" />
                                        <c:set var="corLinhaRessaltada" value="#93DB70" scope="request" />
                                    </c:otherwise>
                                </c:choose>

                                <c:choose>
                                    <c:when test="${fn:indexOf(df.codigoAgrupador, '.') > 0}">
                                        <%-- Definir classe para os n�veis filhos  --%>
                                        <c:set var="noPai"
                                               value="${fn:substring(df.codigoAgrupador,0, fn:length(df.codigoAgrupador) -4)}"
                                               scope="request" />
                                        <c:set var="classecss" scope="request"
                                               value="child-of-${fn:replace(noPai,'.', '') + 0 }" />
                                    </c:when>

                                    <c:otherwise>
                                        <%-- Definir classe para os n�veis pais  --%>
                                        <c:set var="classecss" scope="request" value="" />
                                    </c:otherwise>
                                </c:choose>

                                <%-- Criar cada linha da tabela  --%>
                                <c:if test="${indice.count == DemonstrativoFinanceiroControle.indiceNaoInformado}">
                                    <c:set var="corLinha" value="#FA6161" scope="request" />
                                </c:if>

                                <%-- Criar a linha da tabela  --%>
                                <tr bgcolor="${corLinha}" id="${df.codigoNode}" class="${classecss}"
                                    onmouseover="mudar_cor(this,'${corLinhaRessaltada}');"
                                    onmouseout="mudar_cor(this,'${corLinha}');">
                                    <td class="tituloDemonstrativo" align="left" width="${tamColNomeAgrupador}">
                                            ${df.nomeAgrupador}
                                        <c:if test="${df.dfConta}">
                                                <span class="tooltipster" title="<div class='esquerda'>
<div class='display:block;'><span class='text' style='font-weight: bold'>O saldo inicial das contas apresentadas, refere-se ao valor no momento da abertura do caixa.</span></div>
<div class='display:block;'><span class='text' style='font-weight: bold'> Opera��es retroativas podem causar diverg�ncia nos valores do saldo inicial das contas.</span></div>
</div>">
                                                        - Saldo Inicial: R$ ${df.saldoInicialApresentar} / Saldo Final: R$ ${df.saldoFinalApresentar}
                                                </span>
                                        </c:if>
                                    </td>

                                    <%-- Mostrar os totalizadores de cada m�s  
                                    <c:forEach var="mes" items="${df.listaMeses}">
                                        <td align="right">
                                            <a href="#" class="linkTabela" onclick="preencherValorChamarBotao(
                                               'formDF:botaoVisualizarLancamentos', '<c:out value="${mes.mesAno}"></c:out>',
                                               'formDF:idCodigoSelecionado', '<c:out value="${df.codigoAgrupador}"></c:out>',
                                               'formDF:idTipoListaMostrar', 1)"> <font color="${mes.corLinkTotal}">
                                                ${mes.valorApresentar} </font>
                                            </a>
                                        </td>
                                        </c:forEach> --%>

                                    <%-- Mostrar o total de todos os meses de cada n�vel --%>
                                    <td align="right" class="linkTabela">
                                        <font color="${df.corLinkTotalTodosMeses}"> ${df.totalTodosMesesApresentarTela} </font>
                                    </td>
                                </tr>
                            </c:forEach>

                            <tr>
                                <td style="padding-top: 39px;">
                                <!-- Incluido a tag <br> porque o InternetExplorer n�o reconhece o  style="padding-top: 39px;" -->
                                    <br>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </td>
                <td></td>
            </tr>
        </table>
        <br>
        <br>
    </c:if>
</h:panelGroup>
<!-- ------------------------ Fim relat�rio TreeView  ------------------------------- -->

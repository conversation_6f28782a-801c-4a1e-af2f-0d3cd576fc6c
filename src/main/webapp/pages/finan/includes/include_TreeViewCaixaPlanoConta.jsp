<%@page contentType="text/html" pageEncoding="ISO-8859-1" %>
<%@include file="../includes/include_imports.jsp" %>
<!-- ------------------------ Começo relatório TreeView ------------------------------- -->
<h:panelGroup id="panelTreeViewDF">
    <c:if test="${not empty FechamentoCaixaPlanoContaControle.listaDFBrowser}">
        <table width="100%">
            <tr>
                <td width="100%" style="text-align: center;">
                    <a4j:commandLink value="Expandir Tudo " rev="#posicaoDados" styleClass="expandir" oncomplete="alterarAlturaMenuLateral();"></a4j:commandLink>
                    <a4j:commandLink value="Expandir " rev="#posicaoDados" styleClass="expandirUm" oncomplete="alterarAlturaMenuLateral();"></a4j:commandLink>
                    <a4j:commandLink value="Retrair " rev="#posicaoDados" styleClass="retrairUm" oncomplete="alterarAlturaMenuLateral();"></a4j:commandLink>
                    <a4j:commandLink value="Retrair Tudo " rev="#posicaoDados" styleClass="retrair" oncomplete="alterarAlturaMenuLateral();"></a4j:commandLink>
                </td>
            </tr>
        </table>

        <table width="100%">
            <tr>
                <td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td>
                <td width="100%">

                    <table width="100%" cellpadding="3" cellspacing="0"
                           border="0" class="example" id="dnd-example">
                        <thead>
                        </thead>
                        <tbody>
                        <c:forEach var="df" varStatus="indice"
                                   items="${FechamentoCaixaPlanoContaControle.listaDFBrowser}">

                            <%-- Definir as cores da tabela zebrada --%>
                            <c:choose>
                                <c:when test="${indice.count % 2 == 0}">
                                    <c:set var="corLinha" value="#FFFFFF" scope="request"/>
                                    <c:set var="corLinhaRessaltada" value="#93DB70"
                                           scope="request"/>
                                </c:when>
                                <c:otherwise>
                                    <c:set var="corLinha" value="#DFE8EF" scope="request"/>
                                    <c:set var="corLinhaRessaltada" value="#93DB70"
                                           scope="request"/>
                                </c:otherwise>
                            </c:choose>


                            <c:choose>
                                <c:when
                                        test="${fn:indexOf(df.codigoAgrupador, '.') > 0}">
                                    <%-- Definir classe para os níveis filhos  --%>
                                    <c:set var="noPai"
                                           value="${fn:substring(df.codigoAgrupador,0, fn:length(df.codigoAgrupador) -4)}"
                                           scope="request"/>
                                    <c:set var="classecss" scope="request"
                                           value="child-of-${fn:replace(noPai,'.', '') + 0 }"/>
                                </c:when>
                                <c:otherwise>
                                    <%-- Definir classe para os níveis pais  --%>
                                    <c:set var="classecss" scope="request" value=""/>
                                </c:otherwise>
                            </c:choose>

                            <%-- Criar cada linha da tabela  --%>

                                    <c:if test="${indice.count == FechamentoCaixaPlanoContaControle.indiceNaoInformado}">
                                        <c:set var="estilo" value="font-weight: bold; color: red;" scope="request"/>
                                    </c:if>

                                    <%-- Criar a linha da tabela sem o contador "Não Atribuido"  --%>

                                    <tr bgcolor="${corLinha}" id="${df.codigoNode}"
                                        class="${classecss}"
                                        onmouseover="mudar_cor(this,'${corLinhaRessaltada}');"
                                        onmouseout="mudar_cor(this,'${corLinha}');">

                                        <td class="tituloDemonstrativo" align="left"
                                            width="100%"
                                            style="${estilo}">
                                                <div class="hint">
                                                        ${df.codigoAgrupador} - ${df.nomeAgrupador}
                                                            <c:if test="${((df.codigoPlanoContaTotalFormaPag eq df.codigoAgrupador) or (df.nomeAgrupador eq 'Não Informado Plano de Conta'))}">
                                                        ${df.totalPorFormaPagamentoFormatado}
                                                            </c:if>
                                                    <c:if test="${indice.count == FechamentoCaixaPlanoContaControle.indiceNaoInformado}">
                                                        <div class="hint-text">Esses valores não foram vinculados a nenhum Plano de Conta.<br>Se for um valor do ADM, informar no Rateio Integração.<br>Se for um valor do Financeiro, informar no Contas a Pagar ou Contas a Receber.</div>
                                                    </c:if>
                                                </div>
                                        </td>
                                    </tr>
                        </c:forEach>
                        <tr>
                            <td style="padding-top: 39px;">
                                <!-- Incluido a tag <br> porque o InternetExplorer não reconhece o  style="padding-top: 39px;" -->
                                <br>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                </td>
                <td></td>
            </tr>

        </table>
        <br>
        <br>
    </c:if>
</h:panelGroup>
<!-- ------------------------ Fim relatório TreeView ------------------------------- -->
<style>
    .hint {
        position: relative;
        display: inline-block;
    }

    .hint .hint-text {
        visibility: hidden;
        width: 500px;
        background-color: #f9f9f9;
        color: #333;
        text-align: left;
        border-radius: 6px;
        border: 1px solid black;
        padding: 5px;
        position: absolute;
        z-index: 1;
        bottom: 125%;
        left: 250px;
        transform: translateX(-50%);
        opacity: 0;
        transition: opacity 0.3s;
    }

    .hint:hover .hint-text {
        visibility: visible;
        opacity: 1;
    }
</style>

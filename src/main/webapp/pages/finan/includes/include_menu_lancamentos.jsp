<%@include file="../includes/include_imports.jsp" %>
<h:panelGroup layout="block" styleClass="menuLateral">
    <h:panelGroup layout="block" styleClass="grupoMenuLateral">
        <h:panelGroup layout="block" styleClass="grupoMenuTitulo">
            <i class="fa-icon-rocket"></i> Lan�amentos
        </h:panelGroup>

        <!-- Empresa -->
        <h:panelGroup layout="block" styleClass="grupoMenuItem">
            <a4j:commandLink id="VerLancamentosLink"
                             value="Ver Lan�amentos"
                             styleClass="titulo3 linkFuncionalidade"
                             action="#{FuncionalidadeControle.abrirComCasoNavegacao}"
                             actionListener="#{FuncionalidadeControle.prepararFuncionalidade}"
                             oncomplete="#{FuncionalidadeControle.abrirPopUp}">
                <f:attribute name="funcionalidade" value="LANCAMENTOS" />
            </a4j:commandLink>

            <h:outputLink styleClass="linkWiki"
                          value="#{SuperControle.urlBaseConhecimento}relatorio-ver-lancamentos/"
                          title="Clique e saiba mais: Lan�amentos Financeiros"
                          target="_blank">
                <i class="fa-icon-question-sign" style="font-size: 18px"></i>
            </h:outputLink>
        </h:panelGroup>

        <h:panelGroup layout="block" styleClass="grupoMenuItem">
            <a4j:commandLink id="ContasPagarLink"
                             value="Contas a pagar"
                             styleClass="titulo3 linkFuncionalidade"
                             action="#{FuncionalidadeControle.abrirComCasoNavegacao}"
                             actionListener="#{FuncionalidadeControle.prepararFuncionalidade}"
                             oncomplete="#{FuncionalidadeControle.abrirPopUp}">
                <f:attribute name="funcionalidade" value="CONTAS_PAGAR" />
            </a4j:commandLink>
        </h:panelGroup>

        <h:panelGroup layout="block" styleClass="grupoMenuItem" rendered="#{MovContaControle.contasPagar}">
            <a4j:commandLink id="NovaContaPagarLink"
                             value="Nova contas a pagar"
                             styleClass="titulo3 linkFuncionalidade"
                             action="#{FuncionalidadeControle.abrirComCasoNavegacao}"
                             actionListener="#{FuncionalidadeControle.prepararFuncionalidade}"
                             oncomplete="#{FuncionalidadeControle.abrirPopUp}">
                <f:attribute name="funcionalidade" value="NOVA_CONTA_PAGAR" />
            </a4j:commandLink>
        </h:panelGroup>

        <h:panelGroup layout="block" styleClass="grupoMenuItem">
            <a4j:commandLink id="ContasReceberLink"
                             value="Contas a receber"
                             styleClass="titulo3 linkFuncionalidade"
                             action="#{FuncionalidadeControle.abrirComCasoNavegacao}"
                             actionListener="#{FuncionalidadeControle.prepararFuncionalidade}"
                             oncomplete="#{FuncionalidadeControle.abrirPopUp}">
                <f:attribute name="funcionalidade" value="CONTAS_RECEBER" />
            </a4j:commandLink>
        </h:panelGroup>

        <h:panelGroup layout="block" styleClass="grupoMenuItem" rendered="#{MovContaControle.contasReceber}">
            <a4j:commandLink id="NovaContaReceberLink"
                             value="Nova contas a pagar"
                             styleClass="titulo3 linkFuncionalidade"
                             action="#{FuncionalidadeControle.abrirComCasoNavegacao}"
                             actionListener="#{FuncionalidadeControle.prepararFuncionalidade}"
                             oncomplete="#{FuncionalidadeControle.abrirPopUp}">
                <f:attribute name="funcionalidade" value="NOVA_CONTAS_RECEBER" />
            </a4j:commandLink>
        </h:panelGroup>

    </h:panelGroup>
    <%@include file="include_box_financeiro.jsp" %>
</h:panelGroup>
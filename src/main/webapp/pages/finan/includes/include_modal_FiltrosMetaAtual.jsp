<%@include file="include_imports.jsp" %>

<rich:modalPanel id="modalPanelFiltrosDetalhamentoMeta" autosized="true" shadowOpacity="true" width="600" height="300"
                 top="50">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="#{msg_aplic.prt_Finan_tituloFiltroDetalheMeta}"/>
        </h:panelGroup>
    </f:facet>
    <f:facet name="controls">
        <h:panelGroup>
            <h:graphicImage value="/imagens/close.png" style="cursor:pointer" id="hidelink1"/>
            <rich:componentControl for="modalPanelFiltrosDetalhamentoMeta" attachTo="hidelink1" operation="hide"
                                   event="onclick"/>
        </h:panelGroup>
    </f:facet>

    <a4j:form id="formFiltrosDetalhamentoMeta">
        <table width="100%">
            <tr>
                <td align="left" width="50%">
                    <h:panelGroup rendered="#{!MetaFinanceiroBIControle.metaFinanceiraPorFaturamento}">
                        <h:selectBooleanCheckbox value="#{MetaFinanceiroBIControle.consultarAtual}"/>
                        <h:outputText styleClass="text" value="Consultor atual"/>
                    </h:panelGroup>
                </td>
                <td align="right" width="50%">
                    <h:panelGroup id="totalSelecionado" style="vertical-align:middle;"
                                  rendered="#{TreeViewControle.exibirListaNodesMarcados }">
                        <h:outputText styleClass="tituloDemonstrativo"
                                      value="#{fn:length(TreeViewControle.nodesMarcados)} Selecionado(s)"/>
                    </h:panelGroup>
                </td>
            </tr>
        </table>
        <h:panelGrid columnClasses="tituloCamposTop" columns="1" width="100%" cellpadding="0" cellspacing="0">

            <h:panelGrid width="250" columnClasses="tituloSmart"
                         style="padding: 6px 0 0 6px;font-weight:bold;margin:2px 2px 2px 2px;" columns="1">
                <h:panelGroup id="esconderOpcoes" layout="block" style="width:100%;">

                    <h:panelGroup >
                        <h:outputText value="Filtros por colaboradores:"/>
                        <br/>
                        <div style="max-height: 400px; width:720px; overflow: auto">
                            <h:panelGrid id="panelArvore" columns="1" columnClasses="tituloCamposMiddle,tituloCamposTop">

                                <h:panelGrid columnClasses="tituloCamposMiddle,tituloCamposMiddle,tituloCamposMiddle,tituloCamposMiddle" columns="4" style="text-align:left;">
                                    <h:panelGrid style="cursor:pointer;" columnClasses="tituloCamposMiddle,tituloCamposMiddle" columns="2">
                                        <h:panelGrid columns="2" columnClasses="tituloCamposMiddle,tituloCamposMiddle" title="Marcar todos colaboradores">
                                            <h:graphicImage url="images/checkbox_yes.png" rendered="#{TreeViewControle.marcarTodos}"/>
                                            <h:graphicImage url="images/checkbox_no.png" rendered="#{!TreeViewControle.marcarTodos}"/>
                                            <h:outputText value="Todos"/>
                                            <a4j:support event="onclick" actionListener="#{TreeViewControle.marcarNenhumOuTodosListener}" reRender="panelArvore" status="statusInComponent">
                                                <f:attribute name="opcao" value="#{true}"/>
                                            </a4j:support>
                                        </h:panelGrid>

                                    </h:panelGrid>
                                    <h:panelGrid style="cursor:pointer;" columnClasses="tituloCamposMiddle,tituloCamposMiddle" columns="2">
                                        <h:panelGrid columns="2" columnClasses="tituloCamposMiddle,tituloCamposMiddle" title="Desmarcar todos colaboradores">
                                            <h:graphicImage url="images/checkbox_no.png" rendered="#{TreeViewControle.marcarTodos}"/>
                                            <h:graphicImage url="images/checkbox_yes.png" rendered="#{!TreeViewControle.marcarTodos}"/>
                                            <h:outputText value="Nenhum"/>
                                            <a4j:support event="onclick" actionListener="#{TreeViewControle.marcarNenhumOuTodosListener}" reRender="panelArvore" status="statusInComponent">
                                                <f:attribute name="opcao" value="#{false}"/>
                                            </a4j:support>
                                        </h:panelGrid>
                                    </h:panelGrid>
                                </h:panelGrid>

                                <rich:tree styleClass="tituloCamposMiddle"
                                           nodeFace="node"
                                           status="statusInComponent"
                                           selectedClass="none"
                                           reRender="panelArvore,totalSelecionado" ajaxSubmitSelection="true" switchType="client"
                                           nodeSelectListener="#{TreeViewControle.processSelectionMetaTree}"
                                           value="#{TreeViewControle.treeNode}" var="item" ajaxKeys="#{null}">
                                    <rich:treeNode nodeClass="#{item.css}" id="tNodeP" type="node">
                                        <h:panelGroup>
                                            <h:graphicImage style="vertical-align:middle;" rendered="#{item.marcado && item.objeto != null}" url="images/checkbox_yes.png">
                                                <a4j:support status="statusInComponent" reRender="panelArvore" actionListener="#{TreeViewControle.processSelectionMeta}" event="onclick">
                                                    <f:attribute name="nodeSelecionado" value="#{item}"/>
                                                </a4j:support>
                                            </h:graphicImage>

                                            <h:graphicImage style="vertical-align:middle;" rendered="#{!item.marcado && item.objeto != null}" url="images/checkbox_no.png">
                                                <a4j:support status="statusInComponent" reRender="panelArvore" actionListener="#{TreeViewControle.processSelectionMeta}" event="onclick">
                                                    <f:attribute name="nodeSelecionado" value="#{item}"/>
                                                </a4j:support>
                                            </h:graphicImage>

                                            <h:outputLabel styleClass="tituloCamposMiddle" value="#{item.descricao}">
                                                <a4j:support status="statusInComponent" reRender="panelArvore" actionListener="#{TreeViewControle.processSelectionMeta}" event="onclick">
                                                    <f:attribute name="nodeSelecionado" value="#{item}"/>
                                                </a4j:support>
                                            </h:outputLabel>
                                        </h:panelGroup>
                                    </rich:treeNode>
                                </rich:tree>
                            </h:panelGrid>
                        </div>

                        <br/>
                        <h:outputText value="Filtros por produtos"/>
                        <br/>
                        <h:panelGroup>
                            <h:selectBooleanCheckbox value="#{MetaFinanceiroBIControle.consultarMatricula}"/>
                            <h:outputText styleClass="text" value="Matr�culas"/>
                        </h:panelGroup>
                        <br/>
                        <h:panelGroup>
                            <h:selectBooleanCheckbox value="#{MetaFinanceiroBIControle.consultarRematricula}"/>
                            <h:outputText styleClass="text" value="Rematr�culas"/>
                        </h:panelGroup>
                        <br/>
                        <h:panelGroup>
                            <h:selectBooleanCheckbox value="#{MetaFinanceiroBIControle.consultarRenovacao}"/>
                            <h:outputText styleClass="text" value="Renova��es"/>
                        </h:panelGroup>
                        <br/>
                    </h:panelGroup>
                </h:panelGroup>
            </h:panelGrid>
        </h:panelGrid>

        <a4j:commandButton image="/imagens/OK_Modal.png"
                           oncomplete="Richfaces.hideModalPanel('modalPanelFiltrosDetalhamentoMeta');"
                           action="#{MetaFinanceiroBIControle.aplicarAtualizandoFiltros}" reRender="panelArvore"/>
    </a4j:form>
</rich:modalPanel>

<%@page pageEncoding="ISO-8859-1" %>
<a4j:outputPanel>
    <rich:modalPanel id="modalConfirmaEstornoQuitacaoEmMassa" autosized="true" width="560" height="150"
                     shadowOpacity="true" styleClass="novaModal">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Confirmação"></h:outputText>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText
                        styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                        id="hidelinkmodalConfirmaEstornoQuitacaoEmMassa"/>
                <rich:componentControl for="modalConfirmaEstornoQuitacaoEmMassa"
                                       attachTo="hidelinkmodalConfirmaEstornoQuitacaoEmMassa"
                                       operation="hide" event="onclick"/>
            </h:panelGroup>
        </f:facet>
        <h:form id="formConfirmaEstornoQuitacaoEmMassa">
            <h:panelGrid width="100%" columns="1" cellpadding="1" columnClasses="tituloCampos">

                <h:outputText styleClass="text" style="text-align: left; display: block;"
                              value="Você tem certeza que deseja prosseguir com o Estorno da Quitação de Todas as Contas Selecionadas? Esta ação é irreversível!"/>

                <rich:spacer width="100%" height="10px;" rendered="#{MovContaControle.estornarEmConjuntoPossuiContaPagaEmConjunto}"/>

                <h:outputText rendered="#{MovContaControle.estornarEmConjuntoPossuiContaPagaEmConjunto}"
                              styleClass="tituloDemonstrativo" style="text-align: left; display: block;"
                              value="ATENÇÃO! Foram identificadas nas contas selecionadas, contas pagas em conjunto com outras contas, ao prosseguir com a operação seus pagamentos serão estornados automaticamente."/>

            </h:panelGrid>

            <h:panelGrid columns="1" styleClass="centralizado" width="100%" style="margin-top: 15px;">
                <h:panelGroup>
                    <a4j:commandButton id="sim"
                                       action="#{MovContaControle.realizarEstornoDeQuitacaoEmMassa}"
                                       oncomplete="#{MovContaControle.msgAlert} #{MovContaControle.mensagemNotificar}"
                                       value="Sim"
                                       reRender="formLanc, modalResultadoEstornoQuitacaoEmMassa, formResultadoEstornoQuitacaoEmMassa"
                                       styleClass="botoes nvoBt">
                    </a4j:commandButton>

                    <a4j:commandButton id="nao"
                                       status="statusInComponent nvoBt"
                                       value="Não"
                                       oncomplete="Richfaces.hideModalPanel('modalConfirmaEstornoQuitacaoEmMassa');"
                                       styleClass="botoes nvoBt"
                                       style="background-color: #d5d2d2 !important; color: #555;">
                    </a4j:commandButton>
                </h:panelGroup>
            </h:panelGrid>

        </h:form>
    </rich:modalPanel>
</a4j:outputPanel>

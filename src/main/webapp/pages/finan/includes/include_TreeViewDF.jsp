<%--
    Document   : include_TreeViewDF
    Created on : 04/10/2011, 16:49:37
    Author     : <PERSON><PERSON><PERSON>
--%>
<%@page contentType="text/html" pageEncoding="ISO-8859-1" %>
<%@include file="../includes/include_imports.jsp" %>
<!-- ------------------------ Começo relatório TreeView ------------------------------- -->
<h:panelGroup id="panelTreeViewDF">
    <c:if test="${not empty DemonstrativoFinanceiroControle.listaDFBrowser}">
        <table width="100%">
            <tr>
                <td width="100%" style="text-align: center;">
                    <a4j:commandLink value="Expandir Tudo " rev="#posicaoDados" styleClass="expandir" oncomplete="alterarAlturaMenuLateral();"></a4j:commandLink>
                    <a4j:commandLink value="Expandir " rev="#posicaoDados" styleClass="expandirUm" oncomplete="alterarAlturaMenuLateral();"></a4j:commandLink>
                    <a4j:commandLink value="Retrair " rev="#posicaoDados" styleClass="retrairUm" oncomplete="alterarAlturaMenuLateral();"></a4j:commandLink>
                    <a4j:commandLink value="Retrair Tudo " rev="#posicaoDados" styleClass="retrair" oncomplete="alterarAlturaMenuLateral();"></a4j:commandLink>
                </td>
            </tr>
        </table>

        <table width="100%">
            <tr>
                <td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td>
                <td width="100%">

                    <table width="100%" cellpadding="3" cellspacing="0"
                           border="0" class="example" id="dnd-example">
                        <thead>
                        <tr>
                            <td width="${tamColNomeAgrupador}"></td>
                                <%-- Criar o cabeçalho para os totalizadores dos meses --%>
                            <c:forEach var="mes"
                                       items="${DemonstrativoFinanceiroControle.listaMesProcessar}">
                                <th align="right"><FONT
                                        COLOR=#333333 FACE="Arial,Helvetica,sans-serif"
                                        SIZE=2x> ${mes.nomeMes} </FONT></th>
                                <th></th>
                            </c:forEach>
                            <th align="right"><font size="2x" face="Arial,Helvetica,sans-serif" color="#333333">
                                Total </font></th>
                        </tr>
                        </thead>
                        <tbody>
                        <c:forEach var="df" varStatus="indice"
                                   items="${DemonstrativoFinanceiroControle.listaDFBrowser}">

                            <%-- Definir as cores da tabela zebrada --%>
                            <c:choose>
                                <c:when test="${indice.count % 2 == 0}">
                                    <c:set var="corLinha" value="#FFFFFF" scope="request"/>
                                    <c:set var="corLinhaRessaltada" value="#93DB70"
                                           scope="request"/>
                                </c:when>
                                <c:otherwise>
                                    <c:set var="corLinha" value="#DFE8EF" scope="request"/>
                                    <c:set var="corLinhaRessaltada" value="#93DB70"
                                           scope="request"/>
                                </c:otherwise>
                            </c:choose>


                            <c:choose>
                                <c:when
                                        test="${fn:indexOf(df.codigoAgrupador, '.') > 0}">
                                    <%-- Definir classe para os níveis filhos  --%>
                                    <c:set var="noPai"
                                           value="${fn:substring(df.codigoAgrupador,0, fn:length(df.codigoAgrupador) -4)}"
                                           scope="request"/>
                                    <c:set var="classecss" scope="request"
                                           value="child-of-${fn:replace(noPai,'.', '') + 0 }"/>
                                </c:when>
                                <c:otherwise>
                                    <%-- Definir classe para os níveis pais  --%>
                                    <c:set var="classecss" scope="request" value=""/>
                                </c:otherwise>
                            </c:choose>

                            <%-- Criar cada linha da tabela  --%>

                            <c:choose>
                                <c:when test="${(df.houveNaoAtribuido)}">
                                    <%-- Criar a linha da tabela com o contador "Não Atribuido"  --%>
                                    <tr bgcolor="${corLinha}" id="${df.codigoNode}"
                                        class="${classecss}"
                                        onmouseover="mudar_cor(this,'${corLinhaRessaltada}');"
                                        onmouseout="mudar_cor(this,'${corLinha}');">

                                        <td class="tituloDemonstrativo">
                                            <table style="margin-top: -19px;">
                                                <tr>
                                                    <td class="tituloDemonstrativo" align="left"
                                                        width="${tamColNomeAgrupador}">
                                                            ${df.nomeAgrupador}
                                                        <font class="comparativo">
                                                                ${df.percPretendidoApresentar}
                                                        </font>
                                                    </td>
                                                </tr>
                                            </table>
                                        </td>

                                            <%-- Mostrar os totalizadores de cada mês  --%>
                                        <c:forEach var="mes" items="${df.listaMeses}">
                                            <td align="right">
                                                <table>
                                                    <c:if test="${DemonstrativoFinanceiroControle.tipoVisualizacao.codigo == 2}">
                                                        <tr>
                                                            <td align="right" style="font-size: 13px">
                                                                <p>${mes.entradaApresentar}</p>
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <td align="right" style="font-size: 13px">
                                                                <p>${mes.saidaApresentar}</p>
                                                            </td>
                                                        </tr>
                                                    </c:if>
                                                        <tr>
                                                            <td align="right">
                                                                <c:choose>
                                                                    <c:when test="${DemonstrativoFinanceiroControle.tipoVisualizacao.codigo == 2}">
                                                                        <a  class="linkTabela"
                                                                            onclick="preencherValorChamarBotao('formDF:botaoVisualizarLancamentos',
                                                                                    'formDF:idMesSelecionado',
                                                                                    '<c:out value="${mes.mesAno}"></c:out>',
                                                                                    'formDF:idCodigoSelecionado',
                                                                                    '<c:out value="${df.codigoAgrupador}"></c:out>',
                                                                                    'formDF:idTipoListaMostrar',
                                                                                    1)">
                                                                            <font color="${mes.corLinkTotal}">=${mes.valorApresentar}</font>
                                                                        </a>
                                                                    </c:when>
                                                                    <c:otherwise>
                                                                        <a  class="linkTabela"
                                                                            onclick="preencherValorChamarBotao('formDF:botaoVisualizarLancamentos',
                                                                                    'formDF:idMesSelecionado',
                                                                                    '<c:out value="${mes.mesAno}"></c:out>',
                                                                                    'formDF:idCodigoSelecionado',
                                                                                    '<c:out value="${df.codigoAgrupador}"></c:out>',
                                                                                    'formDF:idTipoListaMostrar',
                                                                                    1)">
                                                                            <font color="${mes.corLinkTotal}">${mes.valorApresentar}</font>
                                                                        </a>
                                                                    </c:otherwise>
                                                                </c:choose>
                                                            </td>
                                                        </tr>
                                                </table>
                                            </td>
                                            <td>
                                                <table>
                                                    <tr>
                                                        <td>
                                                            <font class="comparativo"
                                                                  style="color: ${mes.corPretendido}">${mes.valorAcumuladoApresentar} </font>
                                                        </td>

                                                    </tr>
                                                    <tr>
                                                        <td>
                                                            <font class="comparativo"
                                                                  style="color: ${mes.corPretendidoNA}">${mes.valorAcumuladoNaoAtribuidoApresentar} </font>
                                                        </td>
                                                    </tr>
                                                </table>
                                            </td>

                                        </c:forEach>
                                            <%-- Mostrar o total de todos os meses de cada nível --%>
                                        <td align="right">
                                            <table>
                                                <c:if test="${DemonstrativoFinanceiroControle.tipoVisualizacao.codigo == 2}">
                                                    <tr>
                                                        <td align="right" style="font-size: 13px">
                                                            <font>${df.totalEntradaTodosMesesApresentarTela}</font>
                                                        </td>
                                                    </tr>
                                                    <tr>
                                                        <td align="right" style="font-size: 13px">
                                                            <font>${df.totalSaidaTodosMesesApresentarTela}</font>
                                                        </td>
                                                    </tr>
                                                </c:if>
                                                        <c:choose>
                                                        <c:when test="${DemonstrativoFinanceiroControle.tipoVisualizacao.codigo == 2}">
                                                        <tr>
                                                            <td class="linkTabela" align="right">
                                                                <a  class="linkTabela"
                                                                    onclick="preencherValorChamarBotao('formDF:botaoVisualizarLancamentos',
                                                                        'formDF:idMesSelecionado',
                                                                        '<c:out value="${mes.mesAno}"></c:out>',
                                                                        'formDF:idCodigoSelecionado',
                                                                        '<c:out value="${df.codigoAgrupador}"></c:out>',
                                                                        'formDF:idTipoListaMostrar',
                                                                        1)">
                                                                    <font color="${df.corLinkTotalTodosMeses}">=${df.totalTodosMesesApresentarTela}</font>
                                                                </a>
                                                            </td>
                                                        </tr>
                                                        </c:when>
                                                        <c:otherwise>
                                                            <tr>
                                                                <td class="linkTabela" align="right">
                                                                    <a  class="linkTabela"
                                                                        onclick="preencherValorChamarBotao('formDF:botaoVisualizarLancamentos',
                                                                            'formDF:idMesSelecionado',
                                                                            '<c:out value="${mes.mesAno}"></c:out>',
                                                                            'formDF:idCodigoSelecionado',
                                                                            '<c:out value="${df.codigoAgrupador}"></c:out>',
                                                                            'formDF:idTipoListaMostrar',
                                                                            1)">
                                                                        <font color="${df.corLinkTotalTodosMeses}">${df.totalTodosMesesApresentarTela}</font>
                                                                    </a>
                                                                </td>
                                                            </tr>
                                                        </c:otherwise>
                                                    </c:choose>
                                            </table>
                                        </td>
                                        <td width="1%">
                                            <font class="comparativo"
                                                  style="color: ${df.corPretendido}">${df.valorAcumuladoApresentar} </font>
                                        </td>
                                    </tr>

                                </c:when>
                                <c:otherwise>
                                    <c:if test="${indice.count == DemonstrativoFinanceiroControle.indiceNaoInformado}">
                                        <c:set var="estilo" value="font-weight: bold; color: red;" scope="request"/>
                                    </c:if>

                                    <%-- Criar a linha da tabela sem o contador "Não Atribuido"  --%>
                                    <tr bgcolor="${corLinha}" id="${df.codigoNode}"
                                        class="${classecss}"
                                        onmouseover="mudar_cor(this,'${corLinhaRessaltada}');"
                                        onmouseout="mudar_cor(this,'${corLinha}');">

                                        <td class="tituloDemonstrativo" align="left"
                                            width="${tamColNomeAgrupador}"
                                            style="${estilo}">
                                                <div class="hint">
                                                    ${df.nomeAgrupador}
                                                    <c:if test="${indice.count == DemonstrativoFinanceiroControle.indiceNaoInformado}">
                                                        <div class="hint-text">Esses valores não foram vinculados a nenhum Plano de Conta.<br>Se for um valor do ADM, informar no Rateio Integração.<br>Se for um valor do Financeiro, informar no Contas a Pagar ou Contas a Receber.</div>
                                                    </c:if>
                                                </div>
                                            <font class="comparativo">
                                                    ${df.percPretendidoApresentar}
                                            </font>
                                        </td>


                                            <%-- Mostrar os totalizadores de cada mês  --%>
                                        <c:forEach var="mes" items="${df.listaMeses}" varStatus="myIndex">
                                            <td align="right">
                                                <table>
                                                    <c:if test="${DemonstrativoFinanceiroControle.tipoVisualizacao.codigo == 2}">
                                                        <tr>
                                                            <td align="right" style="font-size: 13px">
                                                                <p>${mes.entradaApresentar}</p>
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <td align="right" style="font-size: 13px">
                                                                <p>${mes.saidaApresentar}</p>
                                                            </td>
                                                        </tr>
                                                    </c:if>
                                                    <tr>
                                                        <td align="right">
                                                            <c:choose>
                                                                <c:when test="${DemonstrativoFinanceiroControle.tipoVisualizacao.codigo == 2}">
                                                                    <a  class="linkTabela"
                                                                        onclick="preencherValorChamarBotao('formDF:botaoVisualizarLancamentos',
                                                                                'formDF:idMesSelecionado',
                                                                                '<c:out value="${mes.mesAno}"></c:out>',
                                                                                'formDF:idCodigoSelecionado',
                                                                                '<c:out value="${df.codigoAgrupador}"></c:out>',
                                                                                'formDF:idTipoListaMostrar',
                                                                                1)">
                                                                        <font color="${mes.corLinkTotal}">=${mes.valorApresentar}</font>
                                                                    </a>
                                                                </c:when>
                                                                <c:otherwise>
                                                                    <a  class="linkTabela"
                                                                        onclick="preencherValorChamarBotao('formDF:botaoVisualizarLancamentos',
                                                                                'formDF:idMesSelecionado',
                                                                                '<c:out value="${mes.mesAno}"></c:out>',
                                                                                'formDF:idCodigoSelecionado',
                                                                                '<c:out value="${df.codigoAgrupador}"></c:out>',
                                                                                'formDF:idTipoListaMostrar',
                                                                                1)">
                                                                        <font color="${mes.corLinkTotal}">${mes.valorApresentar}</font>
                                                                    </a>
                                                                </c:otherwise>
                                                            </c:choose>
                                                        </td>
                                                    </tr>
                                                </table>
                                            </td>
                                            <td>
                                                <font class="comparativo"
                                                      style="color: ${mes.corPretendido}">${mes.valorAcumuladoApresentar}</font>
                                            </td>
                                        </c:forEach>
                                            <%-- Mostrar o total de todos os meses de cada nível --%>
                                        </td>

                                        <td align="right">
                                            <table>
                                                <c:if test="${DemonstrativoFinanceiroControle.tipoVisualizacao.codigo == 2}">
                                                    <tr>
                                                        <td align="right" style="font-size: 13px">
                                                            <font>${df.totalEntradaTodosMesesApresentarTela}</font>
                                                        </td>
                                                    </tr>
                                                    <tr>
                                                        <td align="right" style="font-size: 13px">
                                                            <font>${df.totalSaidaTodosMesesApresentarTela}</font>
                                                        </td>
                                                    </tr>
                                                </c:if>
                                                <tr>
                                                    <td>
                                                        <c:choose>
                                                            <c:when test="${DemonstrativoFinanceiroControle.tipoVisualizacao.codigo == 2}">
                                                                <a id="botaoTotal"
                                                                   class="linkTabela"
                                                                   onclick="preencherValorChamarBotao('formDF:botaoVisualizarLancamentos',
                                                                           'formDF:idMesSelecionado',
                                                                           '<c:out value="${mes.mesAno}"></c:out>',
                                                                           'formDF:idCodigoSelecionado',
                                                                           '<c:out value="${df.codigoAgrupador}"></c:out>',
                                                                           'formDF:idTipoListaMostrar',
                                                                           1)">
                                                                    <font color="${df.corLinkTotalTodosMeses}">=${df.totalTodosMesesApresentarTela}</font>
                                                                </a>
                                                            </c:when>
                                                            <c:otherwise>
                                                                <a id="botaoTotal"
                                                                   class="linkTabela"
                                                                   onclick="preencherValorChamarBotao('formDF:botaoVisualizarLancamentos',
                                                                           'formDF:idMesSelecionado',
                                                                           '<c:out value="${mes.mesAno}"></c:out>',
                                                                           'formDF:idCodigoSelecionado',
                                                                           '<c:out value="${df.codigoAgrupador}"></c:out>',
                                                                           'formDF:idTipoListaMostrar',
                                                                           1)"><font
                                                                        color="${df.corLinkTotalTodosMeses}">

                                                                        ${df.totalTodosMesesApresentarTela} </font> </a>
                                                            </c:otherwise>
                                                        </c:choose>
                                                    </td>
                                                </tr>
                                            </table>
                                        </td>
                                        <td>
                                            <font class="comparativo"
                                                  style="color: ${df.corPretendido}">${df.valorAcumuladoApresentar}</font>
                                        </td>
                                    </tr>
                                </c:otherwise>
                            </c:choose>
                        </c:forEach>
                        <tr>
                            <td style="padding-top: 39px;">
                                <!-- Incluido a tag <br> porque o InternetExplorer não reconhece o  style="padding-top: 39px;" -->
                                <br>
                            </td>
                        </tr>
                        <!-- Início Imprimir os totalizadores de Entrada e Saída -->
                        <c:forEach var="dftot" varStatus="indiceTot"
                                   items="${DemonstrativoFinanceiroControle.listaTotalizadoresDF}">

                            <%-- Definir as cores da tabela zebrada --%>
                            <c:choose>
                                <c:when test="${indiceTot.count % 2 == 0}">
                                    <c:set var="corLinha1" value="#FFFFFF" scope="request"/>
                                    <c:set var="corLinhaRessaltada1" value="#93DB70" scope="request"/>
                                </c:when>
                                <c:otherwise>
                                    <c:set var="corLinha1" value="#DFE8EF" scope="request"/>
                                    <c:set var="corLinhaRessaltada1" value="#93DB70" scope="request"/>
                                </c:otherwise>
                            </c:choose>

                            <tr bgcolor="${corLinha1}"

                                onmouseover="mudar_cor(this,'${corLinhaRessaltada1}');"
                                onmouseout="mudar_cor(this,'${corLinha1}');">

                                <td class="tituloDemonstrativo" align="left"

                                    width="${tamColNomeAgrupador}">
                                        ${dftot.nomeAgrupador}</td>

                                    <%-- Mostrar os totalizadores de cada mês  --%>
                                <c:forEach var="mesTot" items="${dftot.listaTotalizadorMeses}" varStatus="myIndex1">
                                    <td class="linkTabela" align="right"><font color="${mesTot.corLinkTotalNivel}"
                                                                               id="totalGeral${myIndex1.index}">
                                            ${mesTot.valorApresentarTela} </font></td>
                                    <td></td>
                                </c:forEach>
                                    <%-- Mostrar o Resultado de todos os meses --%>
                                <td class="linkTabela" align="right"
                                ><font color="${dftot.corLinkTotalTodosMeses}">
                                        ${dftot.totalTodosMesesApresentarTela} </font>

                                </td>
                                <td></td>

                            </tr>
                        </c:forEach>

                        <!-- Fim Imprimir os totalizadores de Entrada e Saída -->

                        </tbody>
                    </table>
                </td>
                <td></td>
            </tr>

        </table>
        <br>
        <br>
    </c:if>
</h:panelGroup>
<!-- ------------------------ Fim relatório TreeView ------------------------------- -->
<style>
    .hint {
        position: relative;
        display: inline-block;
    }

    .hint .hint-text {
        visibility: hidden;
        width: 500px;
        background-color: #f9f9f9;
        color: #333;
        text-align: left;
        border-radius: 6px;
        border: 1px solid black;
        padding: 5px;
        position: absolute;
        z-index: 1;
        bottom: 125%;
        left: 250px;
        transform: translateX(-50%);
        opacity: 0;
        transition: opacity 0.3s;
    }

    .hint:hover .hint-text {
        visibility: visible;
        opacity: 1;
    }
</style>

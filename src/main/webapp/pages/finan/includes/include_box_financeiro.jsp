<%@page contentType="text/html"%>
<%@page pageEncoding="ISO-8859-1"%>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<h:panelGroup id="panelCaixaUsuario"  styleClass="grupoMenuLateral" rendered="#{CaixaControle.caixaVoEmAberto.codigo > 0 && ConfiguracaoFinanceiroControle.confFinanceiro.usarMovimentacaoContas}">
   <h:panelGroup layout="block" styleClass="box-separador"/>
    <div class="box">
        <div  class="boxmiddle">
            <table width="150" border="0" cellspacing="0" cellpadding="0" style="padding-bottom:6px;" id="totalizadores">
                <tr>
                    <td>
                        <a4j:commandLink styleClass="titulo2" id="linkAbrirCaixa"
                                         action="#{CaixaControle.abrirTelaCaixaAdm}"
                                         oncomplete="#{CaixaControle.msgAlert}">
                            <h:outputText value="Caixa: "/>
                            <h:outputText value="#{CaixaControle.caixaVoEmAberto.codigo}"/>
                        </a4j:commandLink>

                        <a4j:commandLink value="Últimos(IAE)"
                                         id="linkUltimasIAE"
                                         styleClass="titulo2"
                                         style="margin-left: 10px;float: right;"
                                         title="Visualizar últimos lançamentos financeiros(inclusões, alterações e exclusões) desde a data de abertura do caixa."
                                         action="#{FuncionalidadeControle.abrirComCasoNavegacao}"
                                         actionListener="#{FuncionalidadeControle.prepararFuncionalidade}"
                                         oncomplete="#{FuncionalidadeControle.abrirPopUp}">
                            <f:attribute name="funcionalidade" value="ULTIMOS_LANCAMENTOS" />
                        </a4j:commandLink>

                    </td>
                </tr>
            </table>
            <table width="150" border="0" cellspacing="0" cellpadding="0" >
                <tr>
                    <td width="80%"  valign="top" style="font-size:12px">
                        <h:outputText styleClass="tituloCamposNegrito" value="Usuário: "/>
                        <h:outputText style="float:right;" 
                                      value="#{CaixaControle.caixaVoEmAberto.usuarioVo.username}"/>
                    </td>
                </tr>
                <tr>
                    <td  valign="top" style="font-size:12px">
                       <h:outputText styleClass="tituloCamposNegrito" value="Total Entrada: "/>
                       <h:outputText style="float:right;"
                                     value="#{CaixaControle.caixaVoEmAberto.totalEntradaMonetario}"/>
                    </td>
                </tr>
                <tr>
                    <td  valign="top" style="font-size:12px">
                       <h:outputText styleClass="tituloCamposNegrito" value="Total Saída: "/>
                       <h:outputText style="float:right;"
                                     value="#{CaixaControle.caixaVoEmAberto.totalSaidaMonetario}"/>
                    </td>
                </tr>

                <tr>
                    <td  valign="top" style="font-size:12px">
                       <h:outputText styleClass="tituloCamposNegrito" value="Transferências: "/>
                       <h:outputText style="float:right;"
                                     value="#{CaixaControle.caixaVoEmAberto.totalTransferenciaMonetario}"/>
                    </td>
                </tr>
            </table>
        </div>
    </div>
</h:panelGroup>
<%@include file="include_imports.jsp" %>
<%@page pageEncoding="ISO-8859-1" %>

<link href="../css/tooltipster/tooltipster.css" rel="stylesheet" type="text/css"/>
<link href="../css/tooltipster/tooltipster-light.css" rel="stylesheet" type="text/css"/>

<script type="text/javascript" src="../../script/tooltipster/jquery.tooltipster.min.js"></script>

<a4j:outputPanel id="outPanelLancamentos">
    <a4j:form id="formModalLancamentosDF">


        <rich:modalPanel styleClass="novaModal" id="modalLancamentos" autosized="true"
                         shadowOpacity="true" width="850" height="250">
            <f:facet name="header">
                <h:panelGroup>
                    <h:outputText id="listaLancamentos" value="Lista dos Lançamentos"></h:outputText>
                </h:panelGroup>
            </f:facet>
            <f:facet name="controls">
                <h:panelGroup>
                    <h:graphicImage value="/imagens/close.png" style="cursor:pointer"
                                    id="hidelinkLancamentos" />
                    <rich:componentControl for="modalLancamentos"
                                           attachTo="hidelinkLancamentos" operation="hide" event="onclick" />
                </h:panelGroup>
            </f:facet>

            <h:panelGrid id="header" styleClass="textsmall" columns="4" width="50%">
                <h:panelGroup>
                    <h:outputText rendered="#{!DemonstrativoFinanceiroControle.dre && !DemonstrativoFinanceiroControle.resumoContas}" id="labelAgrupador"  value="#{DemonstrativoFinanceiroControle.labelAgrupadorSelecionado}:" />
                    <h:outputText id="nomeAgrupadorSelecionado"  value=" #{DemonstrativoFinanceiroControle.nomeAgrupadorSelecionado}" />
                </h:panelGroup>
                <a4j:commandLink  target="_blank" title="Imprimir EXCEL"
                                  action="#{DemonstrativoFinanceiroControle.imprimirExcel}"
                                  oncomplete="location.href='../../DownloadSV?mimeType=application/vnd.ms-excel&relatorio=DemonstrativoFinanceiroRelExcel.xlsx'">
                    <h:graphicImage url="/imagens/btn_excel.png" style="border:none;" />
                </a4j:commandLink>
                <a4j:commandButton id="imprimir" image="/imagens/imprimir.png"
                                   value="#{msg_aplic.prt_SituacaoContratoAnaliticoDW_imprimir}"
                                   title="Imprimir PDF"
                                   action="#{DemonstrativoFinanceiroControle.imprimirPDF(DemonstrativoFinanceiroControle.getDre() ? DREControle.getTipoRelatorioDf().codigo : DemonstrativoFinanceiroControle.getTipoRelatorioDf().codigo)}"
                                   oncomplete="abrirPopupPDFImpressao('../../relatorio/#{DemonstrativoFinanceiroControle.demonstrativoFinanceiroRel.nomeArquivoRelatorioGeradoAgora}','', 780, 595);"/>

				<h:panelGroup style="align:right">
					<h:selectBooleanCheckbox value="#{DemonstrativoFinanceiroControle.paginada}">
	                    <a4j:support event="onchange" action="#{DemonstrativoFinanceiroControle.mudarPaginacao}" reRender="tabelas" ></a4j:support>
	                </h:selectBooleanCheckbox>
	                <h:outputText value=" Exibir com paginação" styleClass="text"></h:outputText>
                </h:panelGroup>

                <h:panelGroup >
                    <h:outputText rendered="#{!DemonstrativoFinanceiroControle.dre && !DemonstrativoFinanceiroControle.resumoContas && !DemonstrativoFinanceiroControle.totalMeses}" id="mesRefLanc"  value="Mês Referência: " />

                    <h:outputText rendered="#{!DemonstrativoFinanceiroControle.dre && !DemonstrativoFinanceiroControle.resumoContas}" id="idMesSelec"  value="#{DemonstrativoFinanceiroControle.mesSelecionado}" />
                </h:panelGroup>
            </h:panelGrid>
            <h:panelGrid width="100%" id="tabelas">
                <rich:dataTable id="tableLancamentos"
                				width="100%"
                				headerClass="consulta"
                				rowClasses="linhaPar,linhaImpar "
                                reRender="panelListaLancamentosDF"
                                rendered="#{DemonstrativoFinanceiroControle.consultaPaginada}"
                                columnClasses="centralizado, centralizado, centralizado, centralizado, centralizado, centralizado, centralizado"
                                value="#{DemonstrativoFinanceiroControle.listaLancamentosDF}"
                                rows="10"
                                var="lancamentoDF">

                    <rich:column id="descricaoLancamento" sortBy="#{lancamentoDF.descricaoLancamento}"  filterEvent="onkeyup" style="width: 35%;">
                        <f:facet  name="header">
                            <h:outputText id="Descricao"  value="Descrição "/>
                        </f:facet>


                        <c:if test="${MovContaControle.visualizarLancamentos}">
                            <a4j:commandLink action="#{DemonstrativoFinanceiroControle.visualizarLancamentoFinanceiro}"
                                             rendered="#{lancamentoDF.movConta >0}"
                                             oncomplete="abrirPopup('includes/include_LancamentosDF.jsp', 'TelaLançamento', 1200, 700);"
                                             id="idLancamentoFinanc">
                                <h:outputText id="descLancamento" value="#{lancamentoDF.descricaoLancamento}" />
                            </a4j:commandLink>

                            <a4j:commandLink action="#{DemonstrativoFinanceiroControle.visualizarLancamentoZillyonWeb}"
                                             rendered="#{lancamentoDF.movConta == 0}"
                                             oncomplete="abrirPopup('includes/include_DetalhamentoLancamentoZillyonWeb.jsp', 'DetalhamentoLançamento', 750, 620);"
                                             id="linkidLancamentoZillyonWeb">
                                <h:outputText rendered="#{lancamentoDF.movConta == 0}"  id="idLancamentoZillyonWeb" value="#{lancamentoDF.descricaoLancamento}" />
                            </a4j:commandLink>
                        </c:if>

                        <c:if test="${!MovContaControle.visualizarLancamentos}">
                                <h:outputText id="descLancamento" value="#{lancamentoDF.descricaoLancamento}" />
                                <h:outputText rendered="#{lancamentoDF.movConta == 0}"  id="idLancamentoZillyonWeb" value="#{lancamentoDF.descricaoLancamento}" />
                        </c:if>

                    </rich:column>
                    <rich:column id="contrato" sortBy="#{lancamentoDF.contrato}"  filterEvent="onkeyup">
                        <f:facet name="header">
                            <h:outputText id="lblContrato" value="Contrato/Produto"/>
                        </f:facet>
                        <h:outputText id="valorContrato"  value="#{lancamentoDF.contrato}" rendered="#{lancamentoDF.contrato != 0}"
                            styleClass="tooltipster" title="Código de Contrato"/>
                        <h:outputText id="valorCodProduto"  value="#{lancamentoDF.codProduto}" rendered="#{lancamentoDF.contrato == 0}"
                            styleClass="tooltipster" title="Código de Produto"/>
                    </rich:column>

                    <rich:column id="nomePessoa" sortBy="#{lancamentoDF.nomePessoa}"  filterEvent="onkeyup">
                        <f:facet name="header">
                            <h:outputText value="Cliente "/>
                        </f:facet>
                        <h:outputText  value="#{lancamentoDF.nomePessoa}" />
                    </rich:column>


                   <%-- <c:if test="${(DemonstrativoFinanceiroControle.tipoRelatorioDf.codigo == 2)}">
                        <rich:column  sortBy="#{lancamentoDF.contrato}"  filterEvent="onkeyup">
                            <f:facet name="header">
                                <h:outputText id="lblMovProduto" value="MovProduto "/>
                            </f:facet>
                            <h:outputText id="valorMovProduto"  value="#{lancamentoDF.movProduto}" />
                        </rich:column>
                    </c:if>--%>

                    <rich:column  sortBy="#{lancamentoDF.descricaoFormaPagamento}" rendered="#{DemonstrativoFinanceiroControle.mostrarFormaPagamento}" filterEvent="onkeyup">
                        <f:facet name="header">
                            <h:outputText id="FormaPagto" value="Forma de Pagamento"/>
                        </f:facet>
                        <h:outputText id="DescricaoFormaPagto" title="#{lancamentoDF.tipoFormaPagto.hintExplicativo}" value="#{lancamentoDF.descricaoFormaPagamento}" />
                    </rich:column>

                    <rich:column  sortBy="#{lancamentoDF.dataApresentar}" rendered="#{!DemonstrativoFinanceiroControle.mesReferencia}" filterEvent="onkeyup">
                        <f:facet name="header">
                            <h:outputText id="dataLancamento" value="#{DemonstrativoFinanceiroControle.tituloData}"/>
                        </f:facet>
                        <h:outputText value="#{lancamentoDF.dataApresentar}" />
                    </rich:column>

                    <rich:column  sortBy="#{lancamentoDF.dataMesApresentar}" rendered="#{DemonstrativoFinanceiroControle.mesReferencia}" filterEvent="onkeyup">
                        <f:facet name="header">
                            <h:outputText id="dataLancamentoMesReferencia" value="#{DemonstrativoFinanceiroControle.tituloData}"/>
                        </f:facet>
                        <h:outputText value="#{lancamentoDF.dataMesApresentar}" />
                    </rich:column>
                    <%--
                        <rich:column  sortBy="#{lancamentoDF.movPagamento}"  filterEvent="onkeyup">
                            <f:facet name="header">
                                <h:outputText id="lblmovPagamento" value="MovPagamento"/>
                            </f:facet>
                            <h:outputText id="valormovPagamento"  value="#{lancamentoDF.movPagamento}" />
                        </rich:column>
                         <rich:column  sortBy="#{lancamentoDF.movConta}"  filterEvent="onkeyup">
                             <f:facet name="header">
                                 <h:outputText id="lblMovConta"  value="MovConta"/>
                             </f:facet>
                             <h:outputText id="ValorMovConta"  value="#{lancamentoDF.movConta}" />
                         </rich:column>
                    --%>
                    <rich:column id="valorLancamento" style="#{lancamentoDF.styleValor}" title="#{lancamentoDF.titleValor}" sortBy="#{lancamentoDF.valorLancamento}"  filterEvent="onkeyup">
                        <f:facet name="header" >
                            <h:outputText id="lblValor" value="Valor"/>
                        </f:facet>

                        <h:outputText id="ValorValor" value="#{lancamentoDF.valorLancamentoApresentar}" />
                    </rich:column>

                </rich:dataTable>
                
                <rich:datascroller for="tableLancamentos" maxPages="10"  rendered="#{DemonstrativoFinanceiroControle.consultaPaginada}"/>

	<%------------------------------------------------------   CONSULTA NÃO PAGINADA   -----------------------------------------------------------------------%>
                <rich:extendedDataTable id="tableLancamentosNaoPaginada"
                                        width="100%"
                                        height="400px"
                                        headerClass="consulta"
                                        rowClasses="linhaPar,linhaImpar "
		                                reRender="panelListaLancamentosDF"
		                                rendered="#{!DemonstrativoFinanceiroControle.consultaPaginada}"
		                                columnClasses="centralizado, centralizado, centralizado, centralizado, centralizado, centralizado, centralizado"
		                                value="#{DemonstrativoFinanceiroControle.listaLancamentosDF}"
		                                var="lancamentoDF">
                    <rich:column label="Descrição"
                    			 id="descricaoLancamento"
                    			 width="20%"
                    			 sortable="true"
                           		 selfSorted="true"
                                 sortBy="#{lancamentoDF.descricaoLancamento}"
                                 filterEvent="onkeyup">
                        <f:facet  name="header">
                            <h:outputText id="Descricao" value="Descrição"/>
                        </f:facet>

                        <a4j:commandLink action="#{DemonstrativoFinanceiroControle.visualizarLancamentoFinanceiro}"
                                         rendered="#{lancamentoDF.movConta >0}"
                                         oncomplete="abrirPopup('includes/include_LancamentosDF.jsp', 'TelaLançamento', 1200, 700);"
                                         id="idLancamentoFinanc">
                            <h:outputText id="descricaoLanc" value="#{lancamentoDF.descricaoLancamento}" />
                        </a4j:commandLink>

                        <a4j:commandLink action="#{DemonstrativoFinanceiroControle.visualizarLancamentoZillyonWeb}"
                                         rendered="#{lancamentoDF.movConta == 0}"
                                         oncomplete="abrirPopup('includes/include_DetalhamentoLancamentoZillyonWeb.jsp', 'DetalhamentoLançamento', 750, 620);"
                                         id="linkidLancamentoZillyonWeb">
                            <h:outputText rendered="#{lancamentoDF.movConta == 0}"  id="idLancamentoZillyonWeb" value="#{lancamentoDF.descricaoLancamento}" />
                        </a4j:commandLink>
                    </rich:column>
                    
                    <rich:column label="Contrato"
                    			 id="contrato"
                    			 width="10%"
                    			 sortable="true"
                           		 selfSorted="true"
                                 sortBy="#{lancamentoDF.contrato}"
                                 filterEvent="onkeyup">
                        <f:facet name="header">
                            <h:outputText id="lblContrato" value="Contrato"/>
                        </f:facet>
                        
                        <h:outputText id="valorContrato" value="#{lancamentoDF.contrato}" />
                    </rich:column>

                    <rich:column label="Cliente"
                                 id="nomePessoa"
                                 width="42%"
                                 sortable="true"
                           		 selfSorted="true"
                                 sortBy="#{lancamentoDF.nomePessoa}"
                                 filterEvent="onkeyup">
                        <f:facet name="header">
                            <h:outputText value="Cliente "/>
                        </f:facet>
                        
                        <h:outputText  value="#{lancamentoDF.nomePessoa}" />
                    </rich:column>

                    <rich:column label="Forma de Pagamento"
                                 id="FormaDePagamento"
                                 width="20%"
                                 sortable="true"
                                 selfSorted="true"
                                 sortBy="#{lancamentoDF.descricaoFormaPagamento}"
                                 filterEvent="onkeyup">
                        <f:facet name="header">
                            <h:outputText id="FormaPagto" value="Forma de Pagamento"/>
                        </f:facet>
                        
                        <h:outputText id="DescricaoFormaPagto" title="#{lancamentoDF.tipoFormaPagto.hintExplicativo}" value="#{lancamentoDF.descricaoFormaPagamento}" />
                    </rich:column>

                    <rich:column label="Valor"
                                 id="valorLancamento"
                                 style="text-align:right"
                                 width="8%"
                                 sortable="true"
                           		 selfSorted="true"
                                 sortBy="#{lancamentoDF.valorLancamento}"
                                 filterEvent="onkeyup">
                        <f:facet name="header" >
                            <h:outputText id="lblValor" value="Valor"/>
                        </f:facet>

                        <h:outputText id="ValorValor" value="#{lancamentoDF.valorLancamentoApresentar}" />
                    </rich:column>
                </rich:extendedDataTable>
            </h:panelGrid>
	<%------------------------------------------------------   FIM CONSULTA NÃO PAGINADA   -------------------------------------------------------------------%>

            <table class="textsmall" width="100%">
                <tr>
                    <td align="center" colspan="2">
                        <h:outputText id="totalRegistros"  value="Total Registros: " />
                        <h:outputText  id="valorTotalRegistros"  value="#{DemonstrativoFinanceiroControle.totalRegistros}" />
                    </td>
                </tr>
                <tr>
                    <td align="right">
                        <h:outputText rendered="#{!DemonstrativoFinanceiroControle.dre}" id="totalSelEntrada"  value="Total Entrada(+):   " />

                    </td>
                    <td align="right" width="10%">
                        <h:outputText rendered="#{!DemonstrativoFinanceiroControle.dre}" id="ValorTotalSelEntrada"  value="#{DemonstrativoFinanceiroControle.totalSelecionadoEntrada}" />
                    </td>
                </tr>
                <tr>
                    <td align="right">
                        <h:outputText rendered="#{!DemonstrativoFinanceiroControle.dre}" id="totalSelSaida" value="Total Saída(-):   " />
                    </td>
                    <td align="right" width="10%">
                        <h:outputText  rendered="#{!DemonstrativoFinanceiroControle.dre}" id="ValortotalSelSaida"  value="#{DemonstrativoFinanceiroControle.totalSelecionadoSaida}" />
                    </td>
                </tr>
                <tr>
                    <td align="right">
                        <h:outputText rendered="#{!DemonstrativoFinanceiroControle.dre}" id="TotalLanc"  value="Resultado(=):   " />
                        <h:outputText rendered="#{DemonstrativoFinanceiroControle.dre}" value="Total:   " />
                    </td>
                    <td align="right" width="10%">
                        <h:outputText id="ValorTotalLanc"  value="#{DemonstrativoFinanceiroControle.totalSelecionado}" />
                    </td>
                </tr>
            </table>



            <h:panelGrid id="panelMensagens" columns="1" width="100%" styleClass="tabMensagens">
                <h:outputText styleClass="mensagemDetalhada" id="mensagemDetalhada2" value="#{DemonstrativoFinanceiroControle.mensagemDetalhada}"/>
            </h:panelGrid>



        </rich:modalPanel>

    </a4j:form>

</a4j:outputPanel>

<%-- 
    Document   : include_bi_demonstrativo_resumo
    Created on : 01/09/2011, 14:09:15
    Author     : Waller
--%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"
    "http://www.w3.org/TR/html4/loose.dtd">
<%@page contentType="text/html" pageEncoding="ISO-8859-1"%>
<%@include file="imports.jsp" %>
<%@taglib prefix="fiji" uri="http://exadel.com/fiji"%>

<c:set var="root" value="${pageContext.request.contextPath}" scope="request" />


<h:panelGrid columns="3" columnClasses="alinhamentoSuperior, colunaCentralizada, alinhamentoSuperior" width="98%" style="clear:both;" cellpadding="0" cellspacing="0">
    
    <div style="">
        
        
    </div>



    <rich:simpleTogglePanel width="100%" label="Resumo Financeiro" switchType="client">

        <h:panelGroup rendered="#{LoginControle.usuario.administrador}" >

            <h:outputText styleClass="titulo3" value="Empresa:" />
            <rich:spacer width="10px" />
            <h:selectOneMenu  id="comboEmpresaResumoFinandeiro" styleClass="form"  onblur="blurinput(this);"  onfocus="focusinput(this);" value="#{DemonstrativoFinanceiroControle.empresaVO.codigo}" >
                <f:selectItems  value="#{DemonstrativoFinanceiroControle.listaSelectItemEmpresa}" />
            </h:selectOneMenu>
            <rich:spacer width="5px"/>
        </h:panelGroup>

        <h:panelGroup>
            <h:outputText style="font-weight:normal" value="Mês/Ano: " />
            <h:inputText id="dataFimResumo" value="#{DemonstrativoFinanceiroControle.dataFim}"
                         onkeypress="return mascara(this.form, this.id, '99/9999', event);"
                         size="7" styleClass="form" onfocus="focusinput(this);" onblur="blurinput(this);"
                         maxlength="7">
                <f:convertDateTime dateStyle="short" locale="#{SuperControle.localeDefault}" pattern="MM/yyyy"/>
            </h:inputText>
            <rich:hotKey selector="#dataInicioResumo" key="return"
                                     handler="#{rich:element('btnAtualizarResumoFinanceiro')}.click();return false;"/>
            <a4j:commandButton style="vertical-align:middle; width:16px;height:16px;"
                               id="btnAtualizarResumoFinanceiro" reRender="chartResumoFinanceiro"
                               title="Atualizar Resumo Demonstrativo Financeiro"
                               status="statusInComponent"
                               action="#{DemonstrativoFinanceiroControle.montarChart}"
                               image="/images/update.png"/>            
        </h:panelGroup>
        <h:panelGrid width="100%" columns="2" id="chartResumoFinanceiro">

            <h:panelGroup rendered="#{SuperControle.suportaFlash}">
                <fiji:columnChart value="#{DemonstrativoFinanceiroControle.data}" title="Resumo Financeiro"
                                  barColors="#{DemonstrativoFinanceiroControle.colors}" captionY="Montante" toolTipValue="{x} {name} are sold in {y}"
                                  legendCaption="Legenda" legendPosition="bottom" width="450" height="350"
                                  captionX="Mes"                                  
                                  rulersValuesHighlight="none">                    
                    <fiji:chartData type="name" value="#{DemonstrativoFinanceiroControle.names}" />
                </fiji:columnChart>
            </h:panelGroup>
            <h:panelGroup rendered="#{!SuperControle.suportaFlash}">
                <jsfChart:chart
                    datasource="#{DemonstrativoFinanceiroControle.dataSetBarra}"                    
                    type="bar"
                    is3d="true"
                    background="#FFFFFF"
                    foreground="#FFFFFF"
                    depth="24"
                    colors="#847CDE, #F21105"
                    alpha="65"
                    antialias="true"                    
                    legend="true"
                    height="350"
                    width="450">
                </jsfChart:chart>
            </h:panelGroup>
        </h:panelGrid>

    </rich:simpleTogglePanel>
    <h:panelGroup style="padding: 15px;">
        <h:graphicImage id="imageLoading" style="visibility: hidden;vertical-align: middle;align:center" url="/images/loading.gif"/>
    </h:panelGroup>

    <rich:simpleTogglePanel width="100%" label="Receitas Sintético" switchType="client">

        <h:panelGroup rendered="#{LoginControle.usuario.administrador}" >

            <h:outputText styleClass="titulo3" value="Empresa:" />
            <rich:spacer width="10px" />
            <h:selectOneMenu  id="comboEmpresaResumoReceitaSintetico" styleClass="form"  onblur="blurinput(this);"  onfocus="focusinput(this);" value="#{ReceitaPorPeriodoSinteticoRelControleRel.empresa.codigo}" >
                <f:selectItems  value="#{ReceitaPorPeriodoSinteticoRelControleRel.listaSelectItemEmpresa}" />
            </h:selectOneMenu>
            <rich:spacer width="5px"/>
        </h:panelGroup>

        <h:panelGroup>

            <h:inputText id="dataInicioReceitas" value="#{ReceitaPorPeriodoSinteticoRelControleRel.receitaPorPeriodoSinteticoRel.dataInicio}"
                         onkeypress="return mascara(this.form, this.id, '99/9999', event);"
                         size="7" styleClass="form" onfocus="focusinput(this);" onblur="blurinput(this);"
                         maxlength="7">
                <f:convertDateTime dateStyle="short" locale="#{SuperControle.localeDefault}" pattern="MM/yyyy"/>
            </h:inputText>

            <rich:hotKey selector="#dataInicioReceitas" key="return"
                                     handler="#{rich:element('btnAtualizarResumoReceitas')}.click();return false;"/>

            <a4j:commandButton id="btnAtualizarResumoReceitas" reRender="chartResumoReceitas"
                               title="Atualizar Resumo de Receitas"
                               style="vertical-align:middle; width:16px;height:16px;"
                               status="statusInComponent"
                               action="#{ReceitaPorPeriodoSinteticoRelControleRel.montarChart}"
                               image="/images/update.png"/>

        </h:panelGroup>
        <h:panelGrid width="100%" columns="1" id="chartResumoReceitas">

            <h:panelGroup rendered="#{SuperControle.suportaFlash}">
                <fiji:barChart value="#{ReceitaPorPeriodoSinteticoRelControleRel.data}" title="Receitas / Forma de Pagamento"
                                  barColors="#{ReceitaPorPeriodoSinteticoRelControleRel.colors}" captionY="Montante" 
                                  toolTipValue="{x} {name} are sold in {y}"                                  
                                  legendCaption="Legenda" legendPosition="bottom" width="450" height="350"
                                  captionX="Mes"
                                  rulersValuesHighlight="none">
                    <fiji:chartData type="name" value="#{ReceitaPorPeriodoSinteticoRelControleRel.names}" />
                </fiji:barChart>
                
            </h:panelGroup>
            <h:panelGroup rendered="#{!SuperControle.suportaFlash}">
                <jsfChart:chart
                    datasource="#{ReceitaPorPeriodoSinteticoRelControleRel.dataSetPizza}"
                    type="pie"
                    is3d="true"
                    background="#FFFFFF"
                    foreground="#FFFFFF"
                    depth="20"
                    colors="#C48FE3,#8FE3AA,#F2E37E,#7EF2E7,#F21111,#BAAB3C"
                    alpha="65"
                    antialias="true"
                    legend="false"
                    height="350"
                    width="450">
                </jsfChart:chart>
            </h:panelGroup>
        </h:panelGrid>

    </rich:simpleTogglePanel>


</h:panelGrid>


<%@include file="include_imports.jsp" %>
<!-- -------------------DETALHAMENTO DO MES --------------------------- -->
<table width="100%">
						<tr>
						<!-- DIA -->						
									<td width="8%" style="padding: 3px;" class="headerDetalhamentoCumprimentoMetas">
									<span class="tituloDemonstrativo">	
										${msg_aplic.prt_Finan_Dia}
									</span>
									</td>
							<!-- FATURAMENTO -->
									<td width="12%" style="padding: 3px;" class="headerDetalhamentoCumprimentoMetas">
										<span class="tituloDemonstrativo">
											${msg_aplic.prt_Finan_fatRealizado}
										</span>	
									</td>
							<!-- ACUMULADO -->
									<td width="15%" style="padding: 3px;" class="headerDetalhamentoCumprimentoMetas">
									<span class="tituloDemonstrativo">
										${msg_aplic.prt_Finan_fatAcumulado}
									</span>
									</td>
									<td width="1%" style="padding: 3px;">
									
									</td>
							<!-- METAS -->		
									<c:forEach items="${MetaFinanceiroBIControle.meta.valores}" var="descricaoValores">
										<td width="10%" class="headerDetalhamentoCumprimentoMetas"
											style="background-color: ${descricaoValores.cor};">
										<span class="tituloDemonstrativo"  style="color: ${descricaoValores.corTexto};">
											<c:out value="${descricaoValores.observacao}" />
										</span>
										</td>
									</c:forEach>	
							</tr>
						<tr>
							<!-- DIA -->						
									<td width="8%" style="padding: 3px;background-color: #F5F5F5;">
									
									</td>
							<!-- FATURAMENTO -->
									<td width="12%" style="padding: 3px;background-color: #F5F5F5;">
										
									</td>
							<!-- ACUMULADO -->
									<td width="15%" style="padding: 3px;background-color: #F5F5F5;">
									
									</td>
									<td width="1%" style="padding: 3px;">
									
									</td>
							<!-- METAS -->		
									<c:forEach items="${MetaFinanceiroBIControle.meta.valores}" var="descricaoValores">
										<td width="10%" style="padding: 3px; background-color: #F5F5F5; padding-right: 4%;" align="right">
										<span class="tituloDemonstrativo">
											<c:out value="${descricaoValores.valor_Apresentar}" />
										</span>
										</td>
									</c:forEach>	
							</tr>
						
						
							<c:forEach items="${MetaFinanceiroBIControle.listaDetalhamento}" var="detalhamento">
							<tr>
							<!-- DIA -->						
									<td width="8%" style="padding: 3px; background-color: #F5F5F5;"
										class="colunaCentralizada">
									<span class="tituloDemonstrativo">	
										 <c:out value="${detalhamento.dia_Semana_Apresentar}" />
									</span>
									</td>
							<!-- FATURAMENTO -->
									<td width="12%" style="padding: 3px; background-color: #F5F5F5; padding-right: 4%;"
										align="right">
										<a href="#" class="tituloDemonstrativo"
										   onclick="preencherHiddenChamarBotaoFat('form:botaoVisualizarListaRealizados',
																					'form:idDataFatRealizado',
																					'<c:out value="${detalhamento.diaComAno}"/>');">
											<font color="#000000">${detalhamento.faturamentoRealizado_Apresentar} </font></a>
									</td>
							<!-- ACUMULADO -->
									<td title="${detalhamento.observacao}" width="15%" 
									style="padding: 3px; background-color: ${detalhamento.cor}; padding-right: 4%;"
										align="right">
									<a href="#" class="tituloDemonstrativo"
										   onclick="preencherHiddenChamarBotaoFat('form:botaoVisualizarListaAcumulados',
																					'form:idDataFatAcumulado',
																					'<c:out value="${detalhamento.diaComAno}"></c:out>');">
																		<font color="${detalhamento.corTexto}">${detalhamento.faturamentoAcumulado_Apresentar}</font> </a>
										
									</td>
									<td width="1%" style="padding: 3px;">
									
									</td>
							<!-- METAS -->		
									<c:forEach items="${detalhamento.metas}" var="meta">
										<td title="${meta.observacao}" width="10%" style="padding: 3px; background-color: #F5F5F5; padding-right: 4%;"
										     align="right">
										<span class="tituloDemonstrativo">
											<c:if test="${detalhamento.diaCalculado}">
												<c:out value="${meta.valor_Apresentar}" />
											</c:if>
											<c:if test="${not detalhamento.diaCalculado}">
												-
											</c:if>
										</span>
										</td>
									</c:forEach>	
							</tr>
							</c:forEach>
						</table>
						<rich:spacer height="30px"/>
						<h:panelGrid columns="3" width="100%">
						<rich:spacer width="15px"/>
							<h:panelGroup>
								<h:outputText styleClass="tituloDemonstrativo" value="#{msg_aplic.prt_Finan_legenda}: ">
								</h:outputText>
								<rich:panel>
									<table>
										<tr>
										<td title="${msg_aplic.prt_Finan_Lancamentos_abaixoMetaMinima}" 
											width="15%" style="background-color: #FF0000; padding: 4px; border-width: 2px; border-style: solid; border-color: #FFF;"
												     class="colunaCentralizada">
												<span class="tituloDemonstrativo" style="color: #FFFFFF;">
													<c:out value="${msg_aplic.prt_Finan_Lancamentos_abaixoMetaMinima}" />
												</span>
												</td>
										
										
											<!-- METAS -->		
											<c:forEach items="${MetaFinanceiroBIControle.meta.valores}" var="meta">
												<td title="${meta.observacao}" width="10%" style="background-color: ${meta.cor};padding: 3px; border-width: 2px; border-style: solid; border-color: #FFF;"
												     class="colunaCentralizada">
												<span class="tituloDemonstrativo" style="color: ${meta.corTexto};">
													<c:out value="${meta.valor_Apresentar}" />
												</span>
												</td>
											</c:forEach>
									</tr>
									</table>
								</rich:panel>
							</h:panelGroup>
						
						<h:panelGroup>
							<rich:spacer width="15px"/>
							<h:outputText styleClass="tituloDemonstrativo" value="#{msg_aplic.prt_Finan_nrDiasAcademiaAberta}: "></h:outputText>
							<h:outputText styleClass="tituloDemonstrativo" value="#{MetaFinanceiroBIControle.numeroDiasAcademiaAberta}"></h:outputText>
							
						</h:panelGroup>
						</h:panelGrid>
						
						
						
						
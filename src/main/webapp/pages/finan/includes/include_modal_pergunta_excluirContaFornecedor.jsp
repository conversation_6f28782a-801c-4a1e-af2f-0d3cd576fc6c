<%@page contentType="text/html" pageEncoding="ISO-8859-1" %>
<%@include file="imports.jsp" %>
<a4j:outputPanel>
    <rich:modalPanel id="modalExcluirContaFornecedor" width="450" autosized="true" styleClass="novaModal"
                     shadowOpacity="true">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Exclusão de Conta Bancária do Fornecedor"></h:outputText>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText
                        styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign "
                        id="hidelinkPergExcluirContaForn"/>
                <rich:componentControl for="modalExcluirContaFornecedor" attachTo="hidelinkPergExcluirContaForn"
                                       operation="hide" event="onclick"/>
            </h:panelGroup>
        </f:facet>
        <h:form id="formExcluirContaFornecedor">
            <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                <h:panelGroup layout="block" styleClass="texto-size-16-real texto-cor-preto">
                    <h:outputText value="Deseja realmente excluir a conta bancária do fornecedor?"
                                  styleClass="texto-size-16-real texto-cor-preto"/>
                </h:panelGroup>
                <h:panelGroup styleClass="margin-box">
                    <a4j:commandLink action="#{MovContaControle.excluirContaBancariaFornecedor}" value="Sim"
                                     id="sim"
                                     reRender="formLanc"
                                     styleClass="botaoPrimario texto-size-16-real"
                                     oncomplete="#{MovContaControle.mensagemNotificar}#{MovContaControle.abrirFecharModalExcluirContaFornecedor}"/>
                    <rich:spacer width="30px;"/>
                    <a4j:commandLink oncomplete="Richfaces.hideModalPanel('modalExcluirContaFornecedor')"
                                     id="nao"
                                     styleClass="botaoSecundario texto-size-16-real"
                                     value="Não"/>
                </h:panelGroup>
            </h:panelGrid>
        </h:form>
    </rich:modalPanel>
</a4j:outputPanel>


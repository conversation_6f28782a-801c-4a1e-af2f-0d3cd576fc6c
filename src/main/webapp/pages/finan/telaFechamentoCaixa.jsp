<%-- 
    Document   : telaFechamentoCaixa
    Created on : 12/04/2012, 17:44:40
    Author     : <PERSON><PERSON><PERSON>
--%>

<%@page contentType="text/html" pageEncoding="ISO-8859-1"%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@include file="includes/include_imports.jsp" %>
<%@taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>
<head><script type="text/javascript" language="javascript" src="../../script/script.js"></script></head>

<link href="../../css_pacto.css" rel="stylesheet" type="text/css">
<link href="../../css/financeiro.css" rel="stylesheet" type="text/css">
<link href="../../css/otimize.css" rel="stylesheet" type="text/css">
<link href="../../css/pacto_flat_2.16.min.css" rel="stylesheet" type="text/css">
<link href="../../css/jquery.treeTable.css" rel="stylesheet" type="text/css">
<link href="../../css/tooltipster/tooltipster.css" rel="stylesheet" type="text/css"/>
<link href="../../css/tooltipster/tooltipster-light.css" rel="stylesheet" type="text/css"/>
<link href="../../beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<head>
    <script type="text/javascript" language="javascript" src="../../bootstrap/jquery.js"></script>
    <script type="text/javascript" src="../../script/tooltipster/jquery.tooltipster.min.js"></script>
    <script type="text/javascript" src="../../script/demonstrativoFinan.js"></script>
    <script type="text/javascript" src="../../script/jquery.treeTable.js"></script>
</head>

<script type="text/javascript">
    jQuery.noConflict();

    function carregarTooltipsterConfiguracao(){
        carregarTooltipsterConf(jQuery('.tooltipster'));
    }
    function carregarTooltipsterConf(el) {
        el.tooltipster({
            theme: 'tooltipster-light',
            position: 'bottom',
            animation: 'grow',
            contentAsHTML: true
        });
    }
</script>

<f:view>
    <jsp:include page="../../includes/include_carregando_ripple.jsp" flush="true"/>
    <html>
        <!-- Inclui o elemento HEAD da página -->
        <head>
            <script type="text/javascript" language="javascript">
                $.noConflict();
            </script>
        </head>
        <body onload="atualizarTreeViewDF()" style="background-color:white;">

        <rich:modalPanel id="modalConfirmacaoFecharCaixa" autosized="true" styleClass="novaModal" shadowOpacity="true" width="450" height="130">
            <f:facet name="header">
                <h:panelGroup>
                    <h:outputText value="Confirmação Fechamento de Caixa"/>
                </h:panelGroup>
            </f:facet>
            <f:facet name="controls">
                <h:panelGroup>
                    <h:outputText
                            styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                            id="hidelinkModalConfirmacaoFecharCaixa"/>
                    <rich:componentControl for="modalConfirmacaoFecharCaixa" attachTo="hidelinkModalConfirmacaoFecharCaixa" operation="hide"  event="onclick"/>
                </h:panelGroup>
            </f:facet>
            <a4j:form id="formGravarFecharCaixa">
                <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                    <h:panelGroup id="textoInformativo" style="margin: 14px 0px 10px 0px;" styleClass="font-size-Em texto-size-20 texto-cor-cinza-3 texto-font container-botoes">
                        <h:outputText styleClass="mensagem" style="line-height: 50px;" value="Confirma o fechamento do Caixa? "/>
                    </h:panelGroup>
                    <h:panelGrid id="panelResponderConfirmaFechamento" columns="2" width="100%" columnClasses="colunaEsquerda" styleClass="font-size-Em ">
                        <a4j:commandLink id="btnSimFecharCaixa"
                                         style="float: right" value="Sim"
                                         styleClass="botaoPrimario texto-size-14"
                                         action="#{CaixaControle.confirmarFechamento}"
                                         reRender="modalConfirmacaoFecharCaixa,formFechamentoCaixa"
                                         oncomplete="#{CaixaControle.mensagemNotificar};fireElementFromParent('form:atualizaMenuLateral');fireElementFromParent('form:atualizarMenuExplorar');">
                        </a4j:commandLink>

                        <a4j:commandLink id="btnNão" value="Não"
                                         styleClass="botaoSecundario texto-size-14"
                                         reRender="mensagem"
                                         onclick="Richfaces.hideModalPanel('modalConfirmacaoFecharCaixa')"/>
                    </h:panelGrid>
                </h:panelGrid>
            </a4j:form>
        </rich:modalPanel>

        <h:form id="formFechamentoCaixa" style="overflow:visible">
                <h:panelGrid columns="1" styleClass="text" width="100%" style="font-size:14"  >

                    <h:panelGroup>
                        <p style="margin-bottom:6px; font-weight: bold">
                            <img src="${contextoFinan}/images/arrow2.gif" width="16"
                                 height="16" style="vertical-align:middle;margin-right:6px;">Resumo Caixa
                        </p>
                        <h:outputText style="padding-left:20px" value="Caixa: " ></h:outputText>
                        <h:outputText value="#{CaixaControle.caixaVoFechamento.codigo}" ></h:outputText>
                    </h:panelGroup>
                    <h:panelGroup style="padding-left:20px">
                        <h:outputText value="Operador: " ></h:outputText>
                        <h:outputText value="#{CaixaControle.caixaVoFechamento.usuarioVo.nome}" ></h:outputText>
                    </h:panelGroup>
                    <h:panelGroup style="padding-left:20px">
                        <h:outputText value="Data Abertura: " ></h:outputText>
                        <h:outputText value="#{CaixaControle.caixaVoFechamento.dataAbertura}" >
                            <f:convertDateTime pattern="dd/MM/yyyy HH:mm:ss" ></f:convertDateTime>
                        </h:outputText>
                    </h:panelGroup>
                    <h:panelGroup style="padding-left:20px">
                        <h:outputText value="Data Fechamento: " ></h:outputText>
                        <h:outputText id="dataFechamento" value="#{CaixaControle.caixaVoFechamento.dataFechamento}" >
                            <f:convertDateTime pattern="dd/MM/yyyy HH:mm:ss" ></f:convertDateTime>
                        </h:outputText>
                    </h:panelGroup>

					<h:panelGroup style="padding-left:20px">
                        <h:selectOneRadio style="font-size: 14;" styleClass="text" value="#{CaixaControle.tipoMovimentacao}">
                        	<f:selectItem itemLabel="#{msg_aplic.prt_Finan_Caixa_planoContas}" itemValue="1"/>
                        	<f:selectItem itemLabel="#{msg_aplic.prt_Finan_Caixa_Contas}" itemValue="2"/>
                        	<a4j:support event="onclick" oncomplete="atualizarTreeViewDF();carregarTooltipsterConfiguracao()"
                        				 action="#{CaixaControle.atualizarVisualizacaoMovimentacoes}" 
                        				 reRender="telaView"></a4j:support>
                        </h:selectOneRadio>
                    </h:panelGroup>



                    <h:panelGroup id="telaView">
                        <h:panelGrid styleClass="text" width="80%" style="font-size:14">
                            <p style="margin-bottom:6px;font-weight: bold;">
                                <img src="${contextoFinan}/images/arrow2.gif" width="16"
                                     height="16" style="vertical-align:middle;margin-right:6px;">
                                     Movimentações
                            </p>


                            <%@include file="includes/include_TreeViewDF_FechaCaixa.jsp" %>
                        </h:panelGrid>

                        <h:panelGrid columns="1" columnClasses="colunaCentralizada" width="100%" id="botoes">
                        	<a4j:commandLink value="Confirmar Fechamento"
                                 styleClass="botaoPrimario texto-size-16"
                                 id="btnFechaCaixa"
                        		 rendered="#{!CaixaControle.apresentarBotaoImprimirCaixa}"
                                 onclick="Richfaces.showModalPanel('modalConfirmacaoFecharCaixa')"
                                 reRender="panelTreeViewDF,form,menuLateral">

                            </a4j:commandLink>

				            <a4j:commandLink id="botaoImprimirCaixa"
                                rendered="#{CaixaControle.apresentarBotaoImprimirCaixa}"
                                onclick="window.print();return false;">
                                <h:graphicImage value="/imagens/botoesCE/imprimir.png"  style="border: 0px;"/>
				            </a4j:commandLink>
       				     </h:panelGrid>                                           
                    </h:panelGroup>
                </h:panelGrid>
            </h:form>

        </body>
    </html>

    <SCRIPT>
        carregarTooltipsterConfiguracao();
    </SCRIPT>
</f:view>

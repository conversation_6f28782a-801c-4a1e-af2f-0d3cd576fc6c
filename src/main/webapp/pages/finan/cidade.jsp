<%--
    Document   : cidade
    Created on : 22/08/2011, 14:01:22
    Author     : carla
--%>
<a4j:outputPanel>
    <rich:modalPanel id="panelCidade" autosized="true" width="550" height="250"
                     styleClass="novaModal"
                     shadowOpacity="true">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Cadastro de Cidade"/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText
                        styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                        id="hidelinkpanelCidade"/>
                <rich:componentControl for="panelCidade"
                                       attachTo="hidelinkpanelCidade" operation="hide" event="onclick"/>
            </h:panelGroup>
        </f:facet>

        <a4j:form id="formCidade">
            <h:panelGrid columns="1" width="100%">

                <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar" columnClasses="classEsquerda, classDireita" width="100%">

                    <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Cidade_codigo}" />
                    <h:panelGroup>
                        <h:inputText id="cidade_codigo" size="10" maxlength="10" readonly="true" onblur="blurinput(this);"
                                     onfocus="focusinput(this);" styleClass="camposSomenteLeitura" value="#{PessoaSimplificadoControle.pessoaVO.cidade.codigo}" />
                        <h:message for="cidade_codigo" styleClass="mensagemDetalhada" />
                    </h:panelGroup>
                    <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Cidade_nome}" />
                    <h:panelGroup>
                        <h:inputText id="cidade_nome" size="40" maxlength="40" onblur="blurinput(this);" onfocus="focusinput(this);"
                                     styleClass="form" value="#{PessoaSimplificadoControle.pessoaVO.cidade.nome}" />
                    </h:panelGroup>
                    <%-- <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Cidade_pais}" />
                     <h:panelGroup>
                         <h:selectOneMenu id="cidade_pais" disabled="true" onblur="blurinput(this);" onfocus="focusinput(this);"
                                          styleClass="form" value="#{PessoaSimplificadoControle.pessoaVO.cidade.pais.codigo}">
                    <%--a4j:support  event="onchange" action="#{ClienteControle.montarListaSelectItemEstadoCadastroCidade}"/--%>
                    <%--<f:selectItems value="#{PessoaSimplificadoControle.pessoaVO.cidade.listaSelectItemPais}" />
                </h:selectOneMenu>
            </h:panelGroup>--%>
                    <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Cidade_estado}" />
                    <h:panelGroup styleClass="block cb-container" layout="block">
                        <h:selectOneMenu id="cidade_estado" disabled="true" onblur="blurinput(this);" onfocus="focusinput(this);"
                                         styleClass="form" value="#{PessoaSimplificadoControle.cepVO.ufSigla}">
                            <f:selectItems value="#{PessoaSimplificadoControle.listaSelectItemEstado}" />
                        </h:selectOneMenu>
                    </h:panelGroup>
                </h:panelGrid>
                <h:panelGrid id="panelMensagem" columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid columns="1" width="100%">
                        <h:outputText styleClass="mensagem" value="#{PessoaSimplificadoControle.mensagem}" />
                        <h:outputText styleClass="mensagemDetalhada" value="#{PessoaSimplificadoControle.mensagemDetalhada}" />
                    </h:panelGrid>
                    <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                        <h:panelGroup>
                            <a4j:commandButton id="salvarCidade" reRender="pais,estado,cidade" focus="cidade" action="#{PessoaSimplificadoControle.gravarCidade}"
                                               oncomplete="Richfaces.hideModalPanel('panelCidade')"
                                               alt="#{msg.msg_gravar_dados}" accesskey="2" value="Gravar"
                                               styleClass="botoes nvoBt btSec btPerigo"/>
                        </h:panelGroup>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
        </a4j:form>
    </rich:modalPanel>
</a4j:outputPanel>


<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@page contentType="text/html" %>
<%@page pageEncoding="UTF-8" %>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core" %>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax" %>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich" %>
<%@taglib prefix="jsfChart" uri="http://sourceforge.net/projects/jsf-comp" %>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<f:loadBundle var="CElabels" basename="propriedades.CElabels"/>
<f:loadBundle var="Mensagens" basename="propriedades.Mensagens"/>
<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>
<c:set var="contextoFinan" value="${pageContext.request.contextPath}" scope="session"/>
<c:set var="root" value="${pageContext.request.contextPath}" scope="request"/>
<script src="${root}/script/packJQueryPlugins.min.js" type="text/javascript"></script>
<script src="${root}/script/gestao_Recebiveis.js" type="text/javascript"></script>
<script type="text/javascript" language="javascript" src="${root}/script/time_1.3.js"></script>
<script type="text/javascript" src="${root}/script/jquery.maskedinput-1.2.2.js"></script>
<script type="text/javascript" language="javascript" src="${root}/script/gobackblock.js"></script>
<head>
    <%@include file="../../includes/include_import_minifiles.jsp" %>
    <link href="${root}/css/gestaoRecebiveis1.0.min.css" rel="stylesheet" type="text/css">
    <link href="${root}/css/packcss1.0.min.css" rel="stylesheet" type="text/css">
    <link href="${root}/css/pacto_flat_2.16.min.css" rel="stylesheet" type="text/css"/>
    <script src="${root}/script/all_script_3.4.min.js" type="text/javascript"></script>
    <link href="${root}/beta/css/font-awesome_2.0.min.css" rel="stylesheet" type="text/css"/>
    <style type="text/css">
        .titulo3:hover {
            text-decoration: none;
        }

        .titulo4 {
            font-family: Arial, Helvetica, sans-serif;
            font-size: 11px;
            text-decoration: none;
            line-height: normal;
            font-weight: normal;
            text-transform: none;
            color: #297d92;
        }
        .titulo3 {
            font-family: Arial, Helvetica, sans-serif;
            font-size: 11px;
            text-decoration: none;
            line-height: normal;
            font-weight: normal;
            text-transform: none;
            color: #0f4c6b;
        }
        .label-gestao {
            font-size: 1em;
            color: #777;
            padding: 7px 0;
        }
        a {
            text-decoration: none !important;
        }
    </style>
    <script type="text/javascript">
        jQuery.noConflict();

        function setarMostrarCancelados(check) {
            var checkServidor = document.getElementById('formFiltrosGestaoRecebiveis:idmostrarcancelados');
            var checkboxesMT = document.getElementsByName('mostrarCancelados');
            checkServidor.checked = check.checked;
            for (var i = 0, n = checkboxesMT.length; i < n; i++) {
                checkboxesMT[i].checked = check.checked;
            }
        }

        function setarApresentarPagamentosCancelados(check) {
            var checkServidor = document.getElementById('formFiltrosGestaoRecebiveis:idapresentarPagamentosCancelados');
            var checkboxesMT = document.getElementsByName('apresentarPagamentosCancelados');
            checkServidor.checked = check.checked;
            for (var i = 0, n = checkboxesMT.length; i < n; i++) {
                checkboxesMT[i].checked = check.checked;
            }
        }

        function inicializarMostrarCancelados() {
            var checkServidor = document.getElementById('formFiltrosGestaoRecebiveis:idmostrarcancelados');
            var checkboxesMT = document.getElementsByName('mostrarCancelados');
            for (var i = 0, n = checkboxesMT.length; i < n; i++) {
                checkboxesMT[i].checked = checkServidor.checked;
            }
        }

        function inicializarApresentarPagamentosCancelados() {
            var checkServidor = document.getElementById('formFiltrosGestaoRecebiveis:idapresentarPagamentosCancelados');
            var checkboxesMT = document.getElementsByName('apresentarPagamentosCancelados');
            for (var i = 0, n = checkboxesMT.length; i < n; i++) {
                checkboxesMT[i].checked = checkServidor.checked;
            }
        }
    </script>
</head>

<f:view>
    <jsp:include page="../../includes/include_carregando_ripple.jsp"/>
    <title>
        <h:outputText value="Gestão de Recebíveis"/>
    </title>
    <jsp:include page="includes/include_operacaoConta.jsp" flush="true"/>

    <h:form id="form">
        <style>
            .tabelaDados td {
                font-size: .9em !important;
            }

            .tabelaDados th {
                font-size: .8em !important;
            }

        </style>

        <rich:modalPanel id="modalInfoItemExtrato" autosized="true" width="650" height="450" styleClass="novaModal">
            <f:facet name="header">
                <h:panelGroup>
                    <h:outputText value="Detalhes item extrato diário"/>
                </h:panelGroup>
            </f:facet>
            <f:facet name="controls">
                <h:panelGroup>
                    <h:outputText
                            styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                            id="hiperlinkInfoItemExtrato"/>
                    <rich:componentControl for="modalInfoItemExtrato" attachTo="hiperlinkInfoItemExtrato"
                                           operation="hide" event="onclick"/>
                </h:panelGroup>
            </f:facet>
            <h:panelGroup layout="block" id="painelInfoItemExtrato"
                          style="text-align: center; overflow-y: auto; max-height: 250px; display: contents">
                <h:panelGrid columns="2" columnClasses="infoNegrito,info" width="100%">

                    <h:outputText value="Codigo Interno Item:" styleClass="label-gestao"/>
                    <h:outputText value="#{GestaoRecebiveisControle.itemSelecionado.codigo}"/>

                    <c:if test="${GestaoRecebiveisControle.itemSelecionado.tipoConvenioCobrancaEnum.codigo == 50 && GestaoRecebiveisControle.itemSelecionado.stoneModeloPSP}">
                        <h:outputText value="Gateway_id:" styleClass="label-gestao"/>
                        <h:outputText value="#{GestaoRecebiveisControle.itemSelecionado.idExterno}"/>

                        <h:outputText value="Charge_id:" styleClass="label-gestao"/>
                        <h:outputText value="#{GestaoRecebiveisControle.itemSelecionado.idExterno2}"/>
                    </c:if>

                    <c:if test="${GestaoRecebiveisControle.itemSelecionado.nomeAlunoApresentar != ''}">
                        <h:outputText value="Aluno:" styleClass="label-gestao"/>
                        <h:outputText value="#{GestaoRecebiveisControle.itemSelecionado.nomeAlunoApresentar}"/>
                    </c:if>

                    <c:if test="${GestaoRecebiveisControle.itemSelecionado.nomeAlunoApresentar == '' && GestaoRecebiveisControle.itemSelecionado.tipoConciliacaoEnum.codigo != 8}">
                        <h:outputText value="Aluno:" styleClass="label-gestao"/>
                        <h:outputText value="Nenhum aluno no sistema possui essa autorização/nsu"/>
                    </c:if>

                    <c:if test="${GestaoRecebiveisControle.itemSelecionado.nomeAlunoApresentar == '' && GestaoRecebiveisControle.itemSelecionado.tipoConciliacaoEnum.codigo == 8}">
                        <h:outputText value="Tipo:" styleClass="label-gestao"/>
                        <h:outputText value="Estorno Chargeback. Fica como Estornado sistema, para não duplicar totalizadores."/>
                    </c:if>

                    <c:if test="${GestaoRecebiveisControle.itemSelecionado.matricula ne null}">
                        <h:outputText value="Matrícula:" styleClass="label-gestao"/>
                        <h:outputText value="#{GestaoRecebiveisControle.itemSelecionado.matricula}"/>
                    </c:if>

                    <c:if test="${GestaoRecebiveisControle.itemSelecionado.autorizacao != ''}">
                    <h:outputText value="Autorização:" styleClass="label-gestao"/>
                    <h:outputText value="#{GestaoRecebiveisControle.itemSelecionado.autorizacao}"/>
                    </c:if>

                    <c:if test="${GestaoRecebiveisControle.itemSelecionado.nsu == ''}">
                        <h:outputText value="NSU:" styleClass="label-gestao"/>
                        <h:outputText value="Não informado pela operadora"/>
                    </c:if>

                    <c:if test="${GestaoRecebiveisControle.itemSelecionado.nsu != ''}">
                        <h:outputText value="NSU:" styleClass="label-gestao"/>
                        <h:outputText value="#{GestaoRecebiveisControle.itemSelecionado.nsu}"/>
                    </c:if>

                    <c:if test="${GestaoRecebiveisControle.itemSelecionado.autorizacao == ''}">
                        <h:outputText value="Autorização:" styleClass="label-gestao"/>
                        <h:outputText value="Não informado pela operadora"/>
                    </c:if>

                    <c:if test="${GestaoRecebiveisControle.itemSelecionado.nrCartao ne null}">
                    <h:outputText value="Nr. Cartão:" styleClass="label-gestao"/>
                    <h:outputText value="#{GestaoRecebiveisControle.itemSelecionado.nrCartao}"/>
                    </c:if>

                    <h:outputText value="Tipo:" styleClass="label-gestao"/>
                    <h:outputText value=" Crédito" rendered="#{GestaoRecebiveisControle.itemSelecionado.credito}"/>
                    <h:outputText value=" Débito" rendered="#{!GestaoRecebiveisControle.itemSelecionado.credito}"/>

                    <h:outputText value="Antecipado:" title="Informa se o recebível teve antecipação" styleClass="label-gestao tooltipster"/>
                    <h:outputText title="Informa se o recebível teve antecipação" styleClass="tooltipster"
                                  value="#{GestaoRecebiveisControle.itemSelecionado.antecipadoApresentar}"/>

                    <h:outputText value="Data lançamento:" styleClass="label-gestao"/>
                    <h:outputText value="#{GestaoRecebiveisControle.itemSelecionado.dataLancamentoApresentar}"/>

                    <h:outputText value="Data prevista pagamento:" styleClass="label-gestao"/>
                    <h:outputText value="#{GestaoRecebiveisControle.itemSelecionado.dataPrevistaPagamentoApresentar}"/>

                    <c:if test="${GestaoRecebiveisControle.itemSelecionado.antecipacao}">
                    <h:outputText value="Data prevista original:" styleClass="label-gestao"/>
                    <h:outputText value="#{GestaoRecebiveisControle.itemSelecionado.dataPgtoOriginalAntesDaAntecipacaoApresentar}"/>
                    </c:if>

                    <h:outputText value="Valor bruto:" styleClass="label-gestao tooltipster" title="Valor bruto sem nenhum desconto de taxas"/>
                    <h:outputText styleClass="tooltipster" title="Valor bruto sem nenhum desconto de taxas"
                                  value="#{GestaoRecebiveisControle.itemSelecionado.valorBrutoApresentarInfoItem}"/>

                    <h:outputText value="Valor líquido:" styleClass="label-gestao tooltipster" title="Valor líquido já com o desconto de todas as taxas"
                                  rendered="#{GestaoRecebiveisControle.itemSelecionado.valorPositivo}" />
                    <h:outputText styleClass="tooltipster" title="Valor líquido já com o desconto de todas as taxas"
                                  value="#{GestaoRecebiveisControle.itemSelecionado.valorLiquidoApresentar}"
                                  rendered="#{GestaoRecebiveisControle.itemSelecionado.valorPositivo}" />

                    <h:outputText value="Taxa do Cartão:" styleClass="label-gestao"
                                  rendered="#{GestaoRecebiveisControle.itemSelecionado.valorPositivo}" />
                    <h:outputText value="#{GestaoRecebiveisControle.itemSelecionado.taxaCartaoApresentar}"
                                  rendered="#{GestaoRecebiveisControle.itemSelecionado.valorPositivo}" />

                    <c:if test="${GestaoRecebiveisControle.itemSelecionado.antecipacao}">
                        <h:outputText value="Taxa da Antecipação:" styleClass="label-gestao" rendered="#{GestaoRecebiveisControle.itemSelecionado.antecipacao}"/>
                        <h:outputText value="#{GestaoRecebiveisControle.itemSelecionado.taxaAntecipacaoApresentar}" rendered="#{GestaoRecebiveisControle.itemSelecionado.antecipacao}"/>

                        <h:outputText value="Taxa Total:" styleClass="label-gestao tooltipster" title="Considera a taxa do cartão + taxa de antecipação em percentual"/>
                        <h:outputText styleClass="tooltipster" title="Considera a taxa do cartão + taxa de antecipação em percentual"
                                      value="#{GestaoRecebiveisControle.itemSelecionado.taxaTotalApresentarPercentual}"/>

                        <h:outputText value="Valor descontado pela antecipação:" styleClass="label-gestao tooltipster" title="Apenas o valor descontado pela antecipação do recebível"
                                      rendered="#{GestaoRecebiveisControle.itemSelecionado.antecipacao}"/>
                        <h:outputText styleClass="tooltipster" title="Apenas o valor descontado pela antecipação do recebível"
                                      rendered="#{GestaoRecebiveisControle.itemSelecionado.antecipacao}"
                                      value="#{GestaoRecebiveisControle.itemSelecionado.valorDescontadoTaxaAntecipacaoApresentar}"/>
                    </c:if>

                    <h:outputText value="Valor descontado Taxa Total:" styleClass="label-gestao tooltipster" title="Considera a taxa do cartão + taxa de antecipação"
                                  rendered="#{GestaoRecebiveisControle.itemSelecionado.valorPositivo}" />
                    <h:outputText styleClass="tooltipster" title="Considera a taxa do cartão + taxa de antecipação"
                                  value="#{GestaoRecebiveisControle.itemSelecionado.taxaTotalApresentar}"
                                  rendered="#{GestaoRecebiveisControle.itemSelecionado.valorPositivo}" />

                    <c:if test="${GestaoRecebiveisControle.itemSelecionado.reciboPagamentoVO.codigo != 0}">
                        <h:outputText value="Recibo:" styleClass="label-gestao"/>
                        <h:outputText value="#{GestaoRecebiveisControle.itemSelecionado.reciboPagamentoVO.codigo}"/>
                    </c:if>

                    <c:if test="${GestaoRecebiveisControle.itemSelecionado.codConvenio != 0}">
                        <h:outputText value="Cód. Convênio:" styleClass="label-gestao tooltipster"
                                      title="Cód. do convênio de cobrança do item"/>
                        <h:outputText styleClass="tooltipster"
                                      title="Cód. do convênio de cobrança do item"
                                      value="#{GestaoRecebiveisControle.itemSelecionado.codConvenio}"/>
                    </c:if>

                    <c:if test="${GestaoRecebiveisControle.itemSelecionado.tipoConciliacao != 0 && GestaoRecebiveisControle.itemSelecionado.estorno}">
                        <h:outputText value="Tipo Cancelamento:" styleClass="label-gestao tooltipster"
                                      title="Exibe se é cancelamento ou chargeback"/>
                        <h:outputText styleClass="tooltipster"
                                      title="Exibe se é cancelamento ou chargeback"
                                      value="#{GestaoRecebiveisControle.itemSelecionado.tipoConciliacaoApresentar}"/>
                    </c:if>

                    <c:if test="${GestaoRecebiveisControle.itemSelecionado.parcelasApresentar != ''}">
                        <h:outputText value="Parcela(s):" styleClass="label-gestao"/>
                        <h:outputText value="#{GestaoRecebiveisControle.itemSelecionado.parcelasApresentar}"
                                      escape="false"/>
                    </c:if>

                    <c:if test="${GestaoRecebiveisControle.itemSelecionado.observacao != ''}">
                        <h:outputText value="Observação:" styleClass="label-gestao"/>
                        <h:outputText value="#{GestaoRecebiveisControle.itemSelecionado.observacao}"
                                      escape="false"/>
                    </c:if>

                </h:panelGrid>
            </h:panelGroup>
        </rich:modalPanel>
        <a4j:keepAlive beanName="ExportadorListaControle"/>
        <html>
        <body>
        <a4j:commandLink action="#{GestaoRecebiveisControle.consultarRecebiveis}"
                         reRender="tabelaExtratoDiario, listaCartoes"
                         style="display:none;"
                         id="btnAtualizarRecAvulso">
        </a4j:commandLink>

        <h:panelGroup layout="block" style="display: inline-table;width: 100%;" styleClass="fundoCinza">
            <h:panelGroup layout="block" styleClass="bgtop topoZW" rendered="#{MenuControle.apresentarTopo}">
                <jsp:include page="../../include_topo_novo.jsp" flush="true"/>
                <jsp:include page="../../include_menu_fin_flat.jsp" flush="true"/>
                <rich:jQuery selector=".item1" query="addClass('menuItemAtual')"/>
            </h:panelGroup>
            <h:panelGroup layout="block" styleClass="caixaCorpo">
                <h:panelGroup layout="block" style="height: 80%;width: 100%">
                    <h:panelGroup layout="block" styleClass="caixaMenuLatel">

                        <h:panelGroup id="idCaixaCorpo" layout="block" styleClass="container-imagem container-conteudo-central">
                            <h:panelGroup layout="block" style="min-width: 1000px;" styleClass="container-box zw_ui especial ">
                                <h:panelGroup id="panelGruoupAbasRecebiveisConciliacao" style="min-width: 980px;" styleClass="container-box-header" layout="block">
                                    <h:panelGroup layout="block" styleClass="margin-box"
                                                  style="display:flex; justify-content: space-between; align-items: center">
                                        <h:panelGroup layout="block" styleClass="text" style="margin-top: 6px;">
                                            <a4j:commandLink
                                                    action="#{GestaoRecebiveisControle.sairModoConciliacao}" id="linkGestaoRecebiveis"
                                                    reRender="idCaixaCorpo, formFiltrosGestaoRecebiveis"
                                                    oncomplete="reRenderMenuLateral()"
                                                    styleClass="tudo"
                                                    style="padding: 15px; display: inline-block; #{GestaoRecebiveisControle.visaoConciliacao ? 'opacity: 0.3;' : 'border-bottom: solid #094771;'}">
                                                <h:outputText styleClass="container-header-titulo"
                                                              value="#{msg_aplic.prt_Finan_GestaoRecebiveis_tituloForm}" />
                                                <h:outputLink styleClass="linkWiki"
                                                              value="#{SuperControle.urlBaseConhecimento}o-que-e-gestao-de-recebiveis/"
                                                              title="Clique e saiba mais: Gestão de Recebíveis" target="_blank">
                                                    <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                                                </h:outputLink>

                                            </a4j:commandLink>

                                            <a4j:commandLink action="#{GestaoRecebiveisControle.entrarModoConciliacao}" id="link_Conciliacao_Recebiveis"
                                                             reRender="idCaixaCorpo, formFiltrosGestaoRecebiveis"
                                                             styleClass="tudo step6"
                                                             oncomplete="#{GestaoRecebiveisControle.msgAlert};#{GestaoRecebiveisControle.mensagemNotificar};reRenderMenuLateral()"
                                                             style="padding: 15px; display: inline-block; #{GestaoRecebiveisControle.visaoConciliacao ? 'border-bottom: solid #094771;' : 'opacity: 0.3;'}">
                                                <h:outputText styleClass="container-header-titulo" value="Conciliação"/>
                                                <h:outputLink styleClass="linkWiki"
                                                              value="#{SuperControle.urlBaseConhecimento}como-funciona-a-conciliacao-automatica-no-sistema-pacto/"
                                                              title="Clique e saiba mais: Conciliação"
                                                              target="_blank">
                                                    <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                                                </h:outputLink>

                                            </a4j:commandLink>

                                        </h:panelGroup>

                                    </h:panelGroup>
                                </h:panelGroup>
                                <h:panelGroup id="panelGroupBotoesFiltrosExportar" layout="block" styleClass="margin-box" style="padding-bottom: 0; min-width: 980px;">
                                    <a4j:commandLink
                                            oncomplete="Richfaces.showModalPanel('modalPanelFiltrosGestaoRecebiveis');"
                                            id="refazerConsulta"
                                            reRender="modalPanelFiltrosGestaoRecebiveis"
                                            style="cursor: pointer;"
                                            styleClass="pure-button pure-button-primary step5 tudo">
                                        <i class="fa-icon-refresh"></i> &nbsp Refazer consulta
                                    </a4j:commandLink>


                                    <div id="botoesTL" style="display: inline-flex; padding: 10px;">
                                        <a4j:commandLink action="#{GestaoRecebiveisControle.consultarFaturadosOntem}"
                                                         id="btnCaixaOntem"
                                                         style="margin-left: 10px;"
                                                         oncomplete="jQuery('.btnfaturadosOntem').addClass('ativo');#{GestaoRecebiveisControle.mensagemNotificar}"
                                                         rendered="#{!GestaoRecebiveisControle.visaoConciliacao}"
                                                         onclick="addMsg('Obtendo Recebíveis faturados ontem...');jQuery('.growl-container').empty();"
                                                         styleClass="step1 tudo botaoModoTimeLine btnfaturadosOntem #{GestaoRecebiveisControle.resetou ? 'ativo' : ''}"
                                                         reRender="dataInicioL,dataTerminoL,dataInicioC,dataTerminoC,periodoLancamentos,periodoPesquisa,idCaixaCorpo">
                                            <i class="fa-icon-credit-card"></i> &nbsp Caixa de ontem
                                        </a4j:commandLink>
                                        <a4j:commandLink action="#{GestaoRecebiveisControle.consultarCompensadosHoje}"
                                                         rendered="#{!GestaoRecebiveisControle.visaoConciliacao}"
                                                         style="margin-left: 10px;"
                                                         oncomplete="jQuery('.idCompensadosHoje').addClass('ativo');#{GestaoRecebiveisControle.mensagemNotificar}"
                                                         onclick="addMsg('Obtendo Recebíveis que compensam hoje...');jQuery('.growl-container').empty();"
                                                         styleClass="botaoModoTimeLine idCompensadosHoje step4 tudo "
                                                         reRender="dataInicioL,dataTerminoL,dataInicioC,dataTerminoC,periodoLancamentos,periodoPesquisa,idCaixaCorpo">
                                            <i class="fa-icon-money"></i> &nbsp Compensados hoje
                                        </a4j:commandLink>
                                    </div>

                                    <a4j:commandLink oncomplete="#{GestaoRecebiveisControle.msgAlert}"
                                                     styleClass="tudo texto-cor-azul linkPadrao tooltipster"
                                                     title="Visualizar logs"
                                                     rendered="#{!GestaoRecebiveisControle.visaoConciliacao}"
                                                     action="#{GestaoRecebiveisControle.realizarConsultaLogMovimentacao}"
                                                     style="margin-left: 10px; float: right;">
                                        <i class="fa-icon-list"></i>
                                    </a4j:commandLink>
                                    <a4j:commandLink action="#{GestaoRecebiveisControle.abrirModalImpressao}"
                                                     style="margin-left: 10px; float: right;"
                                                     styleClass="tudo texto-cor-azul linkPadrao tooltipster"
                                                     title="Imprimir PDF"
                                                     rendered="#{!GestaoRecebiveisControle.visaoConciliacao && GestaoRecebiveisControle.empresasSelecionadas.size() <= 1}"
                                                     oncomplete="Richfaces.showModalPanel('modalImpressao')"
                                                     reRender="formImpressao">
                                        <i class="fa-icon-print"></i>
                                    </a4j:commandLink>

                                    <a4j:commandLink style="margin-left: 10px; float: right;"
                                                     styleClass="tudo texto-cor-azul linkPadrao tooltipster"
                                                     title="Exportar excel em formato resumido"
                                                     action="#{GestaoRecebiveisControle.notificarRecursoEmpresaExportarExcelResumido}"
                                                     rendered="#{!GestaoRecebiveisControle.visaoConciliacao}"
                                                     actionListener="#{GestaoRecebiveisControle.exportarExcelResumido}"
                                                     oncomplete="abrirPopup('../../../UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/vnd.ms-excel','GestaoRecebiveis', 800,200);#{ExportadorListaControle.msgAlert}"
                                                     accesskey="3">
                                        <f:attribute name="tipo" value="xls"/>
                                        <f:attribute name="atributos" value="empresa.nome=Empresa,
                                                             tipoRecebivel=Tipo do Recebível,
                                                             matricula=Matricula,
                                                             nomePessoa=Nome do pagador,
                                                             nomeAlunosDaParcela=Nome do(s) Aluno(s),
                                                             cpfPagador=#{GestaoRecebiveisControle.displayIdentificadorFront[0]},
                                                             valor=Valor,
                                                             codigosParcelas=Cód das parcela(s),
                                                             vencimentosParcelas=Venc da(s) parcela(s),
                                                             numerosParcelas=Num da(s) parcela(s),
                                                             produtos=Tipo da(s) parcela(s),
                                                             dataCompensacao=Compensação,
                                                             dataLancamento=Data do lançamento,
                                                             recibo=Recibo,
                                                             operadora=Operadora,
                                                             documentoIntegracaoSesi=Documento,
                                                             autorizacao=Autorização,
                                                             nsu=NSU,
                                                             codigoPessoa=Código da pessoa,
                                                             planoContrato=Plano,
                                                             modalidade=Modalidade,
                                                             usuarioResponsavelApresentar=Resp. Recebimento"/>
                                        <f:attribute name="prefixo" value="GestaoRecebiveis"/>
                                        <i class="fa-icon-file-excel-o"></i>
                                    </a4j:commandLink>

                                    <a4j:commandLink id="btnExcelGestaoRecebiveis"
                                                     style="margin-left: 10px; float: right;"
                                                     styleClass="tudo texto-cor-azul linkPadrao tooltipster"
                                                     title="Exportar excel"
                                                     rendered="#{!GestaoRecebiveisControle.visaoConciliacao}"
                                                     actionListener="#{GestaoRecebiveisControle.exportar}"
                                                     oncomplete="abrirPopup('../../../UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/vnd.ms-excel','GestaoRecebiveis', 800,200);#{ExportadorListaControle.msgAlert}"
                                                     accesskey="3">
                                        <f:attribute name="tipo" value="xls"/>
                                        <f:attribute name="atributos" value="empresa.nome=Empresa,
                                                             tipoRecebivel=tipoRecebivel,
                                                             matricula=Matrícula,
                                                             nomePessoa=Nome do pagador,
                                                             nomeAlunosDaParcela=Nome do(s) Aluno(s),
                                                             cpfPagador=#{GestaoRecebiveisControle.displayIdentificadorFront[0]},
                                                             conta=Conta,
                                                             valor=Valor,
                                                             dataLancamento=Dt. Lançamento,
                                                             dataCompensacao=Dt. Compensação,
                                                             dataOriginal=Dt. Original,
                                                             antecipacaoApresentar=Antecipado,
                                                             codigo=Código,
                                                             recibo=Recibo,
                                                             numeroLote=Número Lote,
                                                             operadora=Operadora,
                                                             documentoIntegracaoSesi=Documento,
                                                             codigosComposicao=codigosComposicao,
                                                             autorizacao=Autorização,
                                                             nsu=NSU,
                                                             codigoContaContido=codigoContaContido,
                                                             contaContido=contaContido,
                                                             removido=Removido,
                                                             codigoPessoa=codigoPessoa,
                                                             movConta=movConta,
                                                             nomeNoCheque=nomeNoCheque,
                                                             numero=numero,
                                                             agencia=agencia,
                                                             numeroBanco=numeroBanco,
                                                             dataFim=dataFim,
                                                             pagaMovConta=pagaMovConta,
                                                             loteAvulso=loteAvulso,
                                                             nomeOperadorCartaoApresentar=Op.Cartão,
                                                             valorTotal=valorTotal,
                                                             credito=credito,
                                                             creditoApresentar=creditoApresentar,
                                                             nrParcelaCartaoCredito=nrParcelaCartaoCredito,
                                                             saldoContaCorrenteCliente=saldoContaCorrenteCliente,
                                                             tipoPagador=tipoPagador,
                                                             convenio=convenio,
                                                             operadoraCartaoVO=operadoraCartaoVO,
                                                             pessoa=pessoa,
                                                             responsavelPagamento=responsavelPagamento,
                                                             reciboPagamento=reciboPagamento,
                                                             opcaoPagamentoCheque=opcaoPagamentoCheque,
                                                             opcaoPagamentoCartaoCredito=opcaoPagamentoCartaoCredito,
                                                             opcaoPagamentoCartaoDebito=opcaoPagamentoCartaoDebito,
                                                             opcaoPagamentoDinheiro=opcaoPagamentoDinheiro,
                                                             opcaoPagamentoTransferencia=opcaoPagamentoTransferencia,
                                                             opcaoPagamentoBoleto=opcaoPagamentoBoleto,
                                                             opcaoPagamentoContaCorrenteCliente=opcaoPagamentoContaCorrenteCliente,
                                                             mostraContaCorrenteAcademia=mostraContaCorrenteAcademia,
                                                             movPagamentoEscolhida=movPagamentoEscolhida,
                                                             apresentarCampoCPF=apresentarCampoCPF,
                                                             apresentarCampoCNPJ=apresentarCampoCNPJ,chequeTransiente=chequeTransiente,
                                                             valorReceberOuDevolverContaCorrente=valorReceberOuDevolverContaCorrente,
                                                             usarPagamentoDigital=usarPagamentoDigital,
                                                             usarPagamentoAprovaFacil=usarPagamentoAprovaFacil,
                                                             dataAlteracaoManual=dataAlteracaoManual,
                                                             observacao=observacao,
                                                             movPagamentoEscolhidaFinan=movPagamentoEscolhidaFinan,
                                                             autorizacaoCartao=autorizacaoCartao,
                                                             movPagamentoOrigemCredito=movPagamentoOrigemCredito,
                                                             dataPrevistaDeposito=dataPrevistaDeposito,
                                                             produtosPagos=produtosPagos,
                                                             nrCheques=nrCheques,
                                                             cupomEmitido=cupomEmitido,
                                                             contaFinanceiro=contaFinanceiro,
                                                             dataMovimento=dataMovimento,
                                                             produtos=produtos,
                                                             planoContrato=planoContrato,
                                                             modalidade=modalidade,
                                                             usuarioResponsavelApresentar=Resp. Recebimento,
                                                             reciboPagamentoApresentar=Recibo,
                                                             contratoReciboApresentar=Contrato"/>
                                        <f:attribute name="prefixo" value="GestaoRecebiveis"/>
                                        <i class="fa-icon-file-excel-o"></i>
                                    </a4j:commandLink>
                                    <rich:panel id="filtros"
                                                style="border-color: #eee; background-color: #eee; margin-top: 15px;"
                                                styleClass="tudo">
                                        <h:outputText styleClass="tituloCamposNegrito" value="Filtros:"/>
                                        <h:outputText styleClass="tituloDemonstrativo"
                                                      value="#{GestaoRecebiveisControle.filtros}"/>
                                    </rich:panel>
                                </h:panelGroup>
                                <jsp:include page="gestaoRecebiveis_conciliacao.jsp" flush="true"/>
                                <h:panelGroup id="panelGroupRecebiveisContainer"
                                              style="min-width: 980px;"
                                              layout="block"
                                              styleClass="margin-box"
                                              rendered="#{!GestaoRecebiveisControle.visaoConciliacao}">
                                    <script>
                                        msgs = ['Consultando banco de dados...', 'Processando resultado da consulta', 'Verificando pagamentos a vista...', 'Listando parcelas de Cartão de crédito...', 'Listando cheques', 'Verificando lotes', 'Carregando histórico de cheques', 'Procurando por devoluções', 'Preenchendo contas'];

                                        function exibirEspecie() {
                                            jQuery('.formaEspecie').toggleClass('formaSelecionada');
                                        }

                                        function exibirBoleto() {
                                            jQuery('.formaBoleto').toggleClass('formaSelecionada');
                                        }

                                        function exibirCC() {
                                            jQuery('.formaCC').toggleClass('formaSelecionada');
                                        }
                                    </script>

                                    <table id="tableGraficosETotais" style="width: 100%; min-height: 20vh; min-width: 980px;">
                                        <tr style="vertical-align: top; ">
                                            <td style="position: relative; width: 60%;">
                                                <div id="chartdiv"
                                                     style="width: 100%; top: 0;left: 0;position: absolute; height: 100%;"
                                                     class="step2 tudo"></div>
                                            </td>
                                            <td style="width: 40%; text-align: right;">
                                                <h:panelGroup id="idlistasformas" styleClass="tudo">
                                                    <table id="tabelaFormas" class="tudo step2" style="width: 100%;">
                                                        <tr>
                                                            <td>
                                                                <h:outputText styleClass="linkLegenda legendaFormas"
                                                                              style="text-align: left;font-weight: bold; margin: 0px;"
                                                                              value="FORMAS DE PAGAMENTO:"/>
                                                            </td>
                                                        </tr>
                                                    </table>
                                                    <table id="tabelaFormas" class="tudo step2" style="width: 100%;">
                                                        <c:forEach items="${GestaoRecebiveisControle.lista}" var="forma"
                                                                   varStatus="myIndex">
                                                            <c:if test="${forma.valor gt 0.0 or GestaoRecebiveisControle.mostrarZerados}">
                                                                <tr style="${GestaoRecebiveisControle.formaPagamentoSelecionada.codigo eq forma.codigo ? ' background-color: #e5e5e5; font-weight: bold;' : ''}">
                                                                    <td class="${forma.classeCssPorTipoForma}"
                                                                        style="width: 10px;">
                                                                        <a onclick="selecionarForma(${forma.codigo}, '${forma.tipoFormaPagamento}');"
                                                                           style="width: 10px;height:25px;background-color: ${forma.cor}; display: block;">
                                                                        </a>
                                                                    </td>
                                                                    <td class="${forma.classeCssPorTipoForma}">
                                                                        <a onclick="selecionarForma(${forma.codigo}, '${forma.tipoFormaPagamento}');"
                                                                           id="formaPagamento${myIndex.index}"
                                                                           style="margin: 7px;"
                                                                           class="linkLegenda legendaFormas ${forma.classeCssPorTipoForma}">
                                                                                ${forma.descricao}
                                                                        </a>
                                                                    </td>
                                                                    <td class="${forma.classeCssPorTipoForma}"
                                                                        style="text-align: right;">
                                                                        <a onclick="selecionarForma(${forma.codigo}, '${forma.tipoFormaPagamento}');"
                                                                           style="margin: 7px;"
                                                                           class="linkLegenda legendaFormas ${forma.classeCssPorTipoForma} tooltipster"
                                                                           title="<ul>
                                                                                    <li>Total de alunos por <b>Forma de Pagamento</b>.</li>
                                                                                    <li>Neste campo é apresentado a quantidade unica de alunos, ou seja, caso o aluno tenha <br><b>pagado o plano com 2 cheques ou mais</b>, ele irá aparecer apenas uma vez nesse totalizador.</li>
                                                                                  </ul>">
                                                                                ${forma.quantidadeAlunos}
                                                                        </a>
                                                                    </td>
                                                                    <td class="${forma.classeCssPorTipoForma}"
                                                                        style="text-align: right;">
                                                                        <a onclick="selecionarForma(${forma.codigo}, '${forma.tipoFormaPagamento}');"
                                                                           style="margin: 7px;"
                                                                           class="linkLegenda legendaFormas ${forma.classeCssPorTipoForma} tooltipster"
                                                                           title="<ul>
                                                                                    <li>Total de itens recebidos por <b>Forma de Pagamento</b>.</li>
                                                                                    <li>Ex: Se o aluno pagou o plano em 3 cheques, sera apresentado 3 vezes.</li>
                                                                                  </ul>">
                                                                                ${forma.quantidade}
                                                                        </a>
                                                                    </td>
                                                                    <td class="${forma.classeCssPorTipoForma}"
                                                                        style="text-align: right;">
                                                                        <a onclick="selecionarForma(${forma.codigo}, '${forma.tipoFormaPagamento}');"
                                                                           id="formaPagamentoValor${myIndex.index}"
                                                                           style="margin: 7px;"
                                                                           class="linkLegenda legendaFormas tooltipster"
                                                                           title="<ul>
                                                                                    <li>Total em valor recebido por <b>Forma de Pagamento</b>.</li>
                                                                                  </ul>">
                                                                                ${forma.valorApresentar}
                                                                        </a>
                                                                    </td>
                                                                    <td class="${forma.classeCssPorTipoForma}"
                                                                        style="text-align: right;">
                                                                        <a onclick="selecionarForma(${forma.codigo}, '${forma.tipoFormaPagamento}');"
                                                                           style="margin: 7px;"
                                                                           class="linkLegenda legendaFormas tooltipster"
                                                                           title="<ul>
                                                                                    <li>Porcentagem que cada valor representa no total dos <b>Recebíveis</b>.</li>
                                                                                  </ul>">
                                                                                ${forma.percentualApresentar}%
                                                                        </a>
                                                                    </td>
                                                                </tr>
                                                            </c:if>
                                                        </c:forEach>
                                                    </table>
                                                    <rich:spacer height="15"/>
                                                    <table id="tabelaFormas" class="tudo step2" style="width: 100%;">
                                                        <tr>
                                                            <td>
                                                                <h:outputText styleClass="linkLegenda legendaFormas"
                                                                              style="text-align: left;font-weight: bold; margin: 0px;"
                                                                              value="TOTAIS:"/>
                                                            </td>
                                                        </tr>
                                                    </table>
                                                    <table class="tudo" style="width: 100%;">
                                                        <tr>
                                                            <td style="width: 10px;">

                                                            </td>
                                                            <td><span onmouseover="exibirEspecie();"
                                                                      onmouseout="exibirEspecie();"
                                                                      class="linkLegenda legendaFormas"
                                                                      style="margin: 7px;">Espécie</span></td>

                                                            <td style="text-align: right;">
                                                                        <span onmouseover="exibirEspecie();"
                                                                              onmouseout="exibirEspecie();"
                                                                              id="totalEspecie"
                                                                              class="linkLegenda legendaFormas"
                                                                              style="margin: 7px;">${GestaoRecebiveisControle.empresaLogado.moeda} ${GestaoRecebiveisControle.totalEspecieApresentar}</span>
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <td></td>
                                                            <td><span onmouseover="exibirBoleto();"
                                                                      onmouseout="exibirBoleto();"
                                                                      class="linkLegenda legendaFormas"
                                                                      style="margin: 7px;">Boleto</span></td>

                                                            <td style="text-align: right;">
                                                                        <span onmouseover="exibirBoleto();"
                                                                              onmouseout="exibirBoleto();"
                                                                              id="totalBoleto"
                                                                              class="linkLegenda legendaFormas"
                                                                              style="margin: 7px;">${GestaoRecebiveisControle.empresaLogado.moeda} ${GestaoRecebiveisControle.totalBoletoApresentar}</span>
                                                            </td>
                                                        </tr>
                                                        <c:if test="${GestaoRecebiveisControle.totalDevolucoes > 0.0}">
                                                            <tr style="${GestaoRecebiveisControle.formaPagamentoSelecionada.tipoFormaPagamento eq 'DEVOLUCOES' ? ' background-color: lightgray; font-weight: bold;' : ''}">
                                                                <td></td>
                                                                <td>
                                                                    <a4j:commandLink
                                                                            action="#{GestaoRecebiveisControle.selecionarDevolucoes}"
                                                                            reRender="listaFormas,idlistas,idlistasformas">
                                                                        <h:outputText value="Devoluções"
                                                                                      styleClass="linkLegenda legendaFormas"
                                                                                      style="margin: 7px;"/>
                                                                    </a4j:commandLink>

                                                                </td>

                                                                <td style="text-align: right;">
                                                                    <a4j:commandLink
                                                                            action="#{GestaoRecebiveisControle.selecionarDevolucoes}"
                                                                            reRender="listaFormas,idlistas,idlistasformas">
                                                                        <h:outputText id="totalDevolucoes"
                                                                                      value="#{GestaoRecebiveisControle.empresaLogado.moeda} #{GestaoRecebiveisControle.totalDevolucoesApresentar}"
                                                                                      styleClass="linkLegenda legendaFormas"
                                                                                      style="margin: 7px;"/>
                                                                    </a4j:commandLink>

                                                                </td>
                                                            </tr>
                                                        </c:if>

                                                        <tr>
                                                            <td></td>
                                                            <td>
                                                                <h:outputText style="font-weight: bold; margin: 7px;"
                                                                              value="Total"
                                                                              styleClass="linkLegenda legendaFormas"/>
                                                            </td>

                                                            <td style="text-align: right;">
                                                                <h:outputText style="font-weight: bold; margin: 7px;"
                                                                              styleClass="linkLegenda legendaFormas"
                                                                              id="totalGeral"
                                                                              value="#{GestaoRecebiveisControle.empresaLogado.moeda} #{GestaoRecebiveisControle.totalTotalApresentar}"/>
                                                            </td>
                                                        </tr>
                                                        <c:if test="${GestaoRecebiveisControle.totalNaoFisicamente > 0.0}">
                                                            <tr>
                                                                <td></td>
                                                                <td></td>
                                                                <td></td>
                                                            </tr>
                                                            <tr>
                                                                <td></td>
                                                                <td><span onmouseover="exibirCC();"
                                                                          onmouseout="exibirCC();"
                                                                          class="linkLegenda legendaFormas"
                                                                          style="margin: 7px;">Conta Corrente Cliente</span>
                                                                </td>

                                                                <td style="text-align: right;">
                                                                            <span onmouseover="exibirCC();"
                                                                                  onmouseout="exibirCC();"
                                                                                  id="totalNaoEntraFisicamente"
                                                                                  class="linkLegenda legendaFormas"
                                                                                  style="margin: 7px;">${GestaoRecebiveisControle.empresaLogado.moeda} ${GestaoRecebiveisControle.totalNaoFisicamenteApresentar}</span>
                                                                </td>
                                                            </tr>
                                                        </c:if>
                                                    </table>
                                                </h:panelGroup>

                                            </td>
                                        </tr>
                                    </table>


                                    <script>
                                        var chart = AmCharts.makeChart("chartdiv", {
                                            "type": "serial",
                                            "theme": "light",
                                            "marginRight": 70,
                                            "dataProvider": ${GestaoRecebiveisControle.grafico},
                                            "valueAxes": [{
                                                "axisAlpha": 0,
                                                "position": "left"
                                            }],
                                            "startDuration": 1,
                                            "graphs": [{
                                                "balloonText": "<b>[[description]]: [[value]]</b>",
                                                "balloonFunction": function (item) {
                                                    return item.description + ': ' + '${MovPagamentoControle.empresaLogado.moeda} ' + converteFloatMoeda(item.values.value);
                                                },
                                                "fillAlphas": 0.9,
                                                "colorField": "cor",
                                                "lineAlpha": 0.2,
                                                "descriptionField": "forma",
                                                "type": "column",
                                                "valueField": "valor"
                                            }],
                                            "chartCursor": {
                                                "categoryBalloonEnabled": false,
                                                "cursorAlpha": 0,
                                                "zoomable": false
                                            },
                                            "categoryField": "sigla",
                                            "categoryAxis": {
                                                "gridPosition": "start",
                                                "labelRotation": 0
                                            },
                                            "export": {
                                                "enabled": true
                                            }

                                        });

                                        chart.addListener("clickGraphItem", function (event) {
                                            selecionarForma(event.item.dataContext.codigo, event.item.dataContext.tipo);
                                        });

                                        function selecionarForma(codigo, tipo) {
                                            document.getElementById('form:idFp').value = codigo;
                                            addMsgPorTipo(tipo);
                                            selecionarFPFunction();
                                        }
                                    </script>

                                    <%-- FIM RESUMO DAS FORMAS DE PAGAMENTO --%>
                                    <h:panelGroup id="idlistas" layout="block" style="margin-top: 15px; min-width: 980px;" styleClass="tudo">
                                        <h:inputHidden id="idSelecionados"
                                                       value="#{GestaoRecebiveisControle.selecionados}"/>
                                        <h:inputHidden id="idFp" value="#{GestaoRecebiveisControle.descricaoFp}"/>
                                        <h:inputHidden id="idTotalSelecionados"
                                                       value="#{GestaoRecebiveisControle.totalSelecionado}"/>
                                        <a4j:jsFunction name="selecionarFPFunction"
                                                        action="#{GestaoRecebiveisControle.selecionarFormaPagamentoHidden}"
                                                        reRender="listaFormas,idlistas,idlistasformas">
                                        </a4j:jsFunction>
                                        <h:inputHidden id="idcodigolote" value="#{GestaoLotesControle.codigoLote}"/>
                                        <h:commandLink action="#{GestaoLotesControle.prepareLoteCh}"
                                                       style="display: none;" id="idabrirlote"/>
                                        <jsp:include page="gestaoRecebiveis_cartao.jsp" flush="true"/>
                                        <jsp:include page="gestaoRecebiveis_devolucoes.jsp" flush="true"/>
                                        <jsp:include page="gestaoRecebiveis_cheques.jsp" flush="true"/>
                                        <jsp:include page="gestaoRecebiveis_outros.jsp" flush="true"/>
                                        <script>
                                            carregarTooltipster();
                                        </script>
                                    </h:panelGroup>
                                </h:panelGroup>
                            </h:panelGroup>
                        </h:panelGroup>
                        <jsp:include page="includes/include_box_menulateral.jsp" flush="true"/>

                    </h:panelGroup>
                </h:panelGroup>
            </h:panelGroup>
            <jsp:include page="../../include_rodape_flat.jsp" flush="true"/>
        </h:panelGroup>
        </body>
        </html>
        <a4j:jsFunction action="#{DicasControle.marcarEsconder}" name="marcarExibirTutorial"
                        reRender="containerFaqToid">
            <f:setPropertyActionListener value="#{DicasControle.exibirDicaTutorial}"
                                         target="#{DicasControle.naoMostrarMais}"/>
        </a4j:jsFunction>
        <h:panelGroup layout="block" rendered="#{DicasControle.exibirDicaTutorial}" id="containerFaqToid">
            <%--Retirado apenas o Passo a passo do FAQ para caso necessite colocar novamente, estão o espaço reservado--%>
        </h:panelGroup>
    </h:form>
    <rich:modalPanel id="panelLoteAvulso" autosized="true" shadowOpacity="true" width="250" height="120"
                     onshow="document.getElementById('formDeposito:descricaoDeposito').focus();"
                     styleClass="novaModal">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Lote Avulso"></h:outputText>
            </h:panelGroup>
        </f:facet>
        <a4j:form id="formLoteAvulso" ajaxSubmit="true">
            <h:panelGrid columns="2" width="100%" columnClasses="direita, esquerda"
                         rowClasses="linhaPar,linhaImpar">
                <h:outputText styleClass="tituloCampos"
                              style="font-weight: normal;"
                              value="Lançamento/Depósito:"/>
                <h:panelGroup styleClass="dateTimeCustom"
                              layout="block"
                              style="margin-top: 8px;">
                    <rich:calendar id="dataDepositoLoteAvulso"
                                   oninputchange="return validar_Data(this.id);"
                                   inputClass="form"
                                   buttonIcon="/imagens_flat/calendar-button.svg"
                                   value="#{GestaoRecebiveisControle.lote.dataDeposito}"
                                   enableManualInput="true" popup="true" inputSize="10" datePattern="dd/MM/yyyy"
                                   showApplyButton="false"
                                   showWeeksBar="false" firstWeekDay="0"/>
                </h:panelGroup>
                <h:outputText style="font-weight: normal;" styleClass="tituloCampos"
                              value="#{msg_aplic.prt_Finan_GestaoRecebiveis_descricao}"/>
                <h:inputText id="descricaoDeposito" styleClass="campos" size="40" maxlength="50"
                             value="#{GestaoRecebiveisControle.lote.descricao}"/>
            </h:panelGrid><br/>
            <center>
                <a4j:commandLink styleClass="pure-button pure-button-primary"
                                 id="gravarLoteAvulso"
                                 action="#{GestaoRecebiveisControle.salvarLoteAvulso}"
                                 oncomplete="#{GestaoRecebiveisControle.msgAlert}"
                                 reRender="form">
                    <i class="fa-icon-save"></i> &nbsp Gravar
                </a4j:commandLink>
                <a4j:commandLink style="padding-left:10px"
                                 styleClass="pure-button"
                                 oncomplete="Richfaces.hideModalPanel('panelLoteAvulso');">
                    Cancelar
                </a4j:commandLink>
            </center>
        </a4j:form>
    </rich:modalPanel>
    <rich:modalPanel id="modalAlterarCompensacao"
                     width="450" autosized="true"
                     shadowOpacity="true" styleClass="novaModal">

        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Editar data compensação de cartões"/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText
                        styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign"
                        id="btnFecharAlt"/>
                <rich:componentControl for="modalAlterarCompensacao" attachTo="btnFecharAlt" operation="hide"
                                       event="onclick"/>

            </h:panelGroup>
        </f:facet>
        <h:panelGroup id="panelConteudo">
            <a4j:form id="formEdicaoVigencia">
                <h:outputText value="Data de compensação" styleClass="tituloCampos upper flex"/>
                <div class="dateTimeCustom">
                    <rich:calendar id="dataVigencia"
                                   oninputchange="return validar_Data(this.id);"
                                   buttonIcon="/imagens_flat/calendar-button.svg"

                                   value="#{GestaoRecebiveisControle.dataNovaCompensacaoCartoes}"
                                   enableManualInput="true" popup="true" inputSize="10" datePattern="dd/MM/yyyy"

                                   showWeeksBar="false" firstWeekDay="0"/>
                </div>
                <br/>
                <a4j:commandLink
                        styleClass="tooltipster inlineBlock pure-button pure-button-small pure-button-primary texto-size-18"
                        title="Grava as alterações realizadas"
                        action="#{GestaoRecebiveisControle.alterarDataCompensacaoCartao}"
                        reRender="form"
                        style="margin-right: 10px"
                        oncomplete="#{GestaoRecebiveisControle.msgAlert}">
                    <i class="fa-icon-save"></i> &nbsp Gravar
                </a4j:commandLink>
                <a4j:commandLink styleClass="tooltipster inlineBlock pure-button pure-button-small texto-size-18"
                                 onclick="Richfaces.hideModalPanel('modalAlterarCompensacao');">
                    Cancelar
                </a4j:commandLink>
            </a4j:form>
        </h:panelGroup>
    </rich:modalPanel>
    <rich:modalPanel id="modalAlterarCompensacaoBoleto"
                     width="450" autosized="true"
                     shadowOpacity="true" styleClass="novaModal">

        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Editar data compensação de boletos"/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText
                        styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign"
                        id="btnFecharAltBoleto"/>
                <rich:componentControl for="modalAlterarCompensacaoBoleto" attachTo="btnFecharAltBoleto" operation="hide"
                                       event="onclick"/>
            </h:panelGroup>
        </f:facet>
        <h:panelGroup id="panelConteudoBoleto">
            <a4j:form id="formEdicaoVigenciaBoleto">
                <h:outputText value="Data de compensação" styleClass="tituloCampos upper flex"/>
                <div class="dateTimeCustom">
                    <rich:calendar id="dataVigenciaBole"
                                   oninputchange="return validar_Data(this.id);"
                                   buttonIcon="/imagens_flat/calendar-button.svg"
                                   value="#{GestaoRecebiveisControle.dataNovaCompensacaoBoletos}"
                                   enableManualInput="true" popup="true" inputSize="10" datePattern="dd/MM/yyyy"
                                   showWeeksBar="false" firstWeekDay="0"/>
                </div>
                <br/>
                <a4j:commandLink
                        styleClass="tooltipster inlineBlock pure-button pure-button-small pure-button-primary texto-size-18"
                        title="Grava as alterações realizadas"
                        action="#{GestaoRecebiveisControle.alterarDataCompensacaoBoleto}"
                        reRender="form"
                        style="margin-right: 10px"
                        oncomplete="#{GestaoRecebiveisControle.msgAlert}">
                    <i class="fa-icon-save"></i> &nbsp Gravar
                </a4j:commandLink>
                <a4j:commandLink styleClass="tooltipster inlineBlock pure-button pure-button-small texto-size-18"
                                 onclick="Richfaces.hideModalPanel('modalAlterarCompensacaoBoleto');">
                    Cancelar
                </a4j:commandLink>
            </a4j:form>
        </h:panelGroup>
    </rich:modalPanel>
    <rich:modalPanel id="modalImpressao" width="300" autosized="true" shadowOpacity="true" styleClass="novaModal">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Dados da Impressão"/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText
                        styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign"
                        id="hiperlinkModalImpressao"/>
                <rich:componentControl for="modalImpressao" attachTo="hiperlinkModalImpressao" operation="hide"
                                       event="onclick"/>
            </h:panelGroup>
        </f:facet>
        <a4j:form id="formImpressao">
            <h:panelGrid columns="2">
                <h:selectBooleanCheckbox id="imprimirDiario" value="#{GestaoRecebiveisControle.imprimirDiario}">
                    <a4j:support event="onclick" reRender="formImpressao" status="false"/>
                </h:selectBooleanCheckbox>
                <h:outputText value="Imprimir Valores Diários" styleClass="tituloDemonstrativo"/>

                <c:if test="${!GestaoRecebiveisControle.imprimirDiario}">
                    <h:selectBooleanCheckbox id="detalharRelatorio"
                                             value="#{GestaoRecebiveisControle.detalharRelatorio}">
                        <a4j:support event="onclick" reRender="formImpressao" status="false"/>
                    </h:selectBooleanCheckbox>
                    <h:outputText value="Detalhar recebíveis selecionados" styleClass="tituloDemonstrativo"/>
                </c:if>
            </h:panelGrid>
            <h:panelGrid rendered="#{GestaoRecebiveisControle.imprimirDiario}">
                <h:outputText
                        value="No caso de imprimir valores diários o sistema não irá detalhar os recebíveis na impressão."
                        styleClass="mensagemDetalhada"/>
                <h:selectOneRadio id="agruparPor" tabindex="1"
                                  styleClass="tituloCampos" value="#{GestaoRecebiveisControle.agruparPor}">
                    <f:selectItem itemLabel="Lançamento" itemValue="1"/>
                    <f:selectItem itemLabel="Compensação" itemValue="2"/>
                </h:selectOneRadio>
            </h:panelGrid>
            <h:panelGroup layout="block" style="height: 400px; overflow-y: auto">
                <rich:dataTable id="tblFormasPagamento" width="100%"
                                value="#{GestaoRecebiveisControle.itemImpressao.formasPagamento}"
                                rendered="#{!GestaoRecebiveisControle.imprimirDiario}" var="forma">
                    <rich:column>
                        <h:selectBooleanCheckbox value="#{forma.imprimir}" rendered="#{!forma.disabled}"/>
                        <h:selectBooleanCheckbox disabled="true" rendered="#{forma.disabled}"
                                                 title="Esta forma de pagamento não tem recebíveis para o período consultado"/>
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="Apresentar os seguintes recebíveis:"/>
                        </f:facet>
                        <h:outputText value="#{forma.formaPagamento}"/>
                    </rich:column>
                </rich:dataTable>
            </h:panelGroup>
            <br/>
            <div style="text-align: center;">
                <a4j:commandLink id="btnImprimirPDFGeralGestaoRecebiveis"
                                 styleClass="pure-button pure-button-primary" reRender="tblFormasPagamento"
                                 oncomplete="#{GestaoRecebiveisControle.msgAlert}"
                                 action="#{GestaoRecebiveisControle.imprimir}">
                    <i class="fa-icon-print"></i> &nbsp Imprimir
                </a4j:commandLink>
            </div>
        </a4j:form>
    </rich:modalPanel>
    <rich:modalPanel id="modalPendenciaItemExtrato" autosized="true"
                     shadowOpacity="true" width="400" height="250" styleClass="novaModal">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Pendência item extrato diário"/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText
                        styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                        id="hiperlinkPendenciaItemExtrato"/>
                <rich:componentControl for="modalPendenciaItemExtrato" attachTo="hiperlinkPendenciaItemExtrato"
                                       operation="hide" event="onclick"/>
            </h:panelGroup>
        </f:facet>
        <a4j:form>
            <h:panelGroup layout="block" id="painelPendenciaItemExtrato" style="text-align: center;">
                <h:outputText style="font-size:14px; display:block;" value="#{msg.extrato_diario_data_incompativel}"
                              rendered="#{GestaoRecebiveisControle.itemSelecionado.somenteDatas}"/>
                <h:outputText style="font-size:14px; display:block;" value="#{msg.extrato_diario_valor_incompativel}"
                              rendered="#{GestaoRecebiveisControle.itemSelecionado.valorDiferente and !GestaoRecebiveisControle.itemSelecionado.somenteDatas}"/>
                <a4j:commandLink oncomplete="#{GestaoRecebiveisControle.msgAlert}"
                                 style="font-size:14px; margin-top: 20px;"
                                 rendered="#{GestaoRecebiveisControle.itemSelecionado.somenteDatas}"
                                 styleClass="pure-button pure-button-small pure-button-primary"
                                 reRender="idCaixaCorpo"
                                 onclick="addMsg('Alterando cartão e atualizando lista...');"
                                 action="#{GestaoRecebiveisControle.alterarPagamento}">
                    <i class="fa-icon-ok"></i> &nbsp Alterar pagamento no sistema
                </a4j:commandLink>
            </h:panelGroup>
        </a4j:form>
    </rich:modalPanel>
    <rich:modalPanel id="modalMesclarCartao" autosized="true"
                     shadowOpacity="true" width="400" height="250" styleClass="novaModal">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Mesclar"/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText
                        styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                        id="hiperlinkmesclarItemExtrato"/>
                <rich:componentControl for="modalMesclarCartao" attachTo="hiperlinkmesclarItemExtrato" operation="hide"
                                       event="onclick"/>
            </h:panelGroup>
        </f:facet>
        <a4j:form>
            <h:panelGroup layout="block" id="painelmodalMesclarCartao" style="text-align: center;">
                <h:outputText style="font-size:14px; display:block;"
                              value="#{msg.extrato_mesclar}"/>
                <a4j:commandLink oncomplete="#{GestaoRecebiveisControle.msgAlert}"
                                 style="font-size:14px; margin-top: 20px;"
                                 styleClass="pure-button pure-button-small pure-button-primary"
                                 reRender="idCaixaCorpo"
                                 onclick="addMsg('Mesclando cartão e atualizando lista...');"
                                 action="#{GestaoRecebiveisControle.mesclarLancamentos}">
                    <i class="fa-icon-ok"></i> &nbsp Confirmar
                </a4j:commandLink>
            </h:panelGroup>
        </a4j:form>
    </rich:modalPanel>

    <rich:modalPanel id="modalEstornoChargeback" autosized="true"
                     shadowOpacity="true" width="400" height="250" styleClass="novaModal">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Pendência item extrato diário"/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText
                        styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                        id="hiperlinkModalEstornoChargeback"/>
                <rich:componentControl for="modalEstornoChargeback" attachTo="hiperlinkModalEstornoChargeback"
                                       operation="hide" event="onclick"/>
            </h:panelGroup>
        </f:facet>
        <a4j:form>
            <h:panelGroup layout="block" id="painelPendenciaItemExtratoEstorno" style="text-align: center;">
                <h:outputText style="font-size:14px; display:block;" value="Pagamento estornado pela Adquirente, favor verificar com o Aluno e/ou Adquirente sobre o motivo!"/>
                <a4j:commandLink style="font-size:14px; margin-top: 20px;"
                                 styleClass="pure-button pure-button-small pure-button-primary"
                                 reRender="idCaixaCorpo1"
                                 onclick="Richfaces.hideModalPanel('modalEstornoChargeback')">
                    <i class="fa-icon-ok"></i> &nbsp Ok
                </a4j:commandLink>
            </h:panelGroup>
        </a4j:form>
    </rich:modalPanel>

    <rich:modalPanel id="modalEstornoExtrato" autosized="true"
                     shadowOpacity="true" width="400" height="250" styleClass="novaModal">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Pendência item extrato diário"/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText
                        styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                        id="hiperlinkModalEstornoExtrato"/>
                <rich:componentControl for="modalEstornoExtrato" attachTo="hiperlinkModalEstornoExtrato"
                                       operation="hide" event="onclick"/>
            </h:panelGroup>
        </f:facet>
        <a4j:form>
            <h:panelGroup layout="block" id="painelPendenciaItemExtratoEstorno" style="text-align: center;">
                <h:outputText style="font-size:14px; display:block;" value="Confirma estorno do recibo?"/>
                <a4j:commandLink oncomplete="#{GestaoRecebiveisControle.msgAlert}"
                                 style="font-size:14px; margin-top: 20px;"
                                 rendered="#{GestaoRecebiveisControle.itemSelecionado.estorno}"
                                 styleClass="pure-button pure-button-small pure-button-primary"
                                 reRender="idCaixaCorpo"
                                 onclick="addMsg('Estornando o recibo...');"
                                 action="#{GestaoRecebiveisControle.estornarRecibo}">
                    <i class="fa-icon-ok"></i> &nbsp Estornar recibo
                </a4j:commandLink>
            </h:panelGroup>
        </a4j:form>
    </rich:modalPanel>

    <rich:modalPanel id="modalEstornoExtratoTodos" autosized="true"
                     shadowOpacity="true" width="400" height="250" styleClass="novaModal">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Pendência item extrato diário"/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText
                        styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                        id="hiperlinkModalEstornoExtratoTodos"/>
                <rich:componentControl for="modalEstornoExtratoTodos" attachTo="hiperlinkModalEstornoExtratoTodos"
                                       operation="hide" event="onclick"/>
            </h:panelGroup>
        </f:facet>
        <a4j:form>
            <h:panelGroup layout="block" id="painelPendenciaItemExtratoEstornoTodos" style="text-align: center;">
                <h:outputText style="font-size:14px; display:block;" value="Confirma estorno de todos os recibos?"/>
                <a4j:commandLink style="font-size:14px; margin-top: 20px;"
                                 styleClass="pure-button pure-button-small pure-button-primary"
                                 reRender="idCaixaCorpo"
                                 onclick="addMsg('Estornando os recibos...');"
                                 action="#{GestaoRecebiveisControle.estornarTodos}"
                                 oncomplete="#{GestaoRecebiveisControle.msgAlert}">
                    <i class="fa-icon-ok"></i> &nbsp Estornar recibos
                </a4j:commandLink>
            </h:panelGroup>
        </a4j:form>
    </rich:modalPanel>

    <rich:modalPanel id="modalAlertaTodos" autosized="true"
                     shadowOpacity="true" width="400" height="250" styleClass="novaModal">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Pendência item extrato diário"/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText
                        styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                        id="hiperlinkModalAlertaTodos"/>
                <rich:componentControl for="modalAlertaTodos" attachTo="hiperlinkModalAlertaTodos" operation="hide"
                                       event="onclick"/>
            </h:panelGroup>
        </f:facet>
        <a4j:form>
            <h:panelGroup layout="block" id="painelModalAlertaTodos" style="text-align: center;">
                <h:outputText style="font-size:14px; display:block;" value="Sistema irá ajustar todos os pagamentos onde a data do pagamento está diferente do sistema.
                <br/>Itens que estão com valores diferentes devem ser estornados e lançados novamente, esses itens não serão modificados nesse processo."
                              escape="false"/>
                <a4j:commandLink style="font-size:14px; margin-top: 20px;"
                                 styleClass="pure-button pure-button-small pure-button-primary"
                                 reRender="idCaixaCorpo"
                                 onclick="addMsg('Ajustando datas...');"
                                 action="#{GestaoRecebiveisControle.processarTodosAlertas}"
                                 oncomplete="#{GestaoRecebiveisControle.msgAlert}">
                    <i class="fa-icon-ok"></i> &nbsp Ajustar
                </a4j:commandLink>
            </h:panelGroup>
        </a4j:form>
    </rich:modalPanel>

    <rich:modalPanel id="modalPropagandaFaciliteConciliacao" styleClass="novaModal noMargin" shadowOpacity="true"
                     width="800" height="675">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Fypay"/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText
                        styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                        id="hidemodalPropagandaFaciliteConciliacao"/>
                <rich:componentControl for="modalPropagandaFaciliteConciliacao" attachTo="hidemodalPropagandaFaciliteConciliacao"
                                       operation="hide" event="onclick"/>
            </h:panelGroup>
        </f:facet>
        <h:panelGroup layout="block" style="margin: 10 20 10 20;">
            <a href="https://fypay.com.br" target="_blank" >
                <img border="none" class="img-responsive imagemApresentacao" src="../../images/FACILITEPAY_CONCILIACAO_CARTAO.jpg"/>
            </a>
        </h:panelGroup>
    </rich:modalPanel>
    <jsp:include page="include_modalFiltrosGestaoRecebiveis.jsp" flush="true"/>
    <jsp:include page="includes/include_historicoCheque.jsp" flush="true"/>
    <jsp:include page="includes/include_historicoCartao.jsp" flush="true"/>
    <jsp:include page="includes/include_modalSelecaoPlanoConta.jsp" flush="true"/>
    <jsp:include page="includes/include_modalSelecaoCentroCusto.jsp" flush="true"/>
    <jsp:include page="includes/include_modal_abrirCaixa.jsp" flush="true"/>
    <jsp:include page="includes/include_modal_consultarCaixa.jsp" flush="true"/>
    <jsp:include page="includes/include_box_fecharCaixas.jsp" flush="true"/>
    <jsp:include page="include_modal_retirarChequeLote.jsp" flush="true"/>
    <jsp:include page="includes/include_modal_lancarPagamento.jsp" flush="true"/>
    <jsp:include page="includes/include_modal_excluirDepositoFinanceiro.jsp" flush="true"/>
    <jsp:include page="includes/include_modal_pergunta_excluirDepositoFinanceiro.jsp" flush="true"/>
    <jsp:include page="../../includes/autorizacao/include_autorizacao_funcionalidade.jsp" flush="true"/>
    <jsp:include page="../../includes/include_panelMensagem_goBackBlock.jsp" flush="true"/>
    <jsp:include page="../../includes/include_modal_mensagem_generica.jsp" flush="true"/>
</f:view>

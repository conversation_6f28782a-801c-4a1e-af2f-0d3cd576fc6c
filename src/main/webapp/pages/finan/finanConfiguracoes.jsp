<%@include file="includes/include_imports.jsp" %>
<%@include file="/includes/verificaModulo.jsp" %>
<%@page pageEncoding="ISO-8859-1"%>
<head>
    <script type="text/javascript" language="javascript" src="../../bootstrap/jquery.js"></script>
    <script type="text/javascript" src="../../script/tooltipster/jquery.tooltipster.min.js"></script>
    <script type="text/javascript" src="../../script/demonstrativoFinan.js"></script>
    <script type="text/javascript">
        jQuery.noConflict();

        function carregarTooltipsterConfiguracao(){
            carregarTooltipsterConf(jQuery('.tooltipster'));
        }
        function carregarTooltipsterConf(el) {
            el.tooltipster({
                theme: 'tooltipster-light',
                position: 'bottom',
                animation: 'grow',
                contentAsHTML: true
            });
        }
    </script>
</head>
<link href="../../css/otimize.css" rel="stylesheet" type="text/css">
<link href="../../css/financeiro.css" rel="stylesheet" type="text/css">
<link href="../../beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<f:view>
	<jsp:include page="/includes/include_carregando_ripple.jsp" flush="true"/>
    <title><h:outputText
            value="Configurações Financeiras" /></title>

    <c:set var="titulo" scope="session" value="Configurações Financeiras"/>
    <c:set var="modulo" scope="request" value="${modulo}"/>
    <c:set var="urlWiki" scope="session" value="${SuperControle.urlBaseConhecimento}configuracoes-da-engrenagem-do-modulo-financeiro/"/>

    <f:facet name="header">
        <jsp:include page="../../topoReduzido_material.jsp"/>
    </f:facet>
    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <h:form id="form">
        <html>
        <!-- Inclui o elemento HEAD da página -->
        <head>
            <%@include file="includes/include_head_finan.jsp" %>
            <script type="text/javascript" language="javascript"
                    src="${contextoFinan}/script/telaInicial.js"></script>
        </head>
        <body style="background-color: #FFFFFF;">
        <input type="hidden" name="modulo" value="${modulo}">

		<rich:tabPanel width="100%" activeTabClass="true" >
					
					<rich:tab id="dadosCentral" label="Configurações Gerais" switchType="Client">
                        <h:panelGrid columnClasses="colunaDireita, colunaEsquerda" rowClasses="linhaPar, linhaImpar"
                                     columns="2" width="100%">
                            <h:selectBooleanCheckbox
                                    rendered="#{ConfiguracaoFinanceiroControle.apresentarConfigCentralEventos}"
									value="#{ConfiguracaoFinanceiroControle.confFinanceiro.usarCentralEventos}"/>
                            <h:outputText rendered="#{ConfiguracaoFinanceiroControle.apresentarConfigCentralEventos}"
									 	  styleClass="tituloCampos"
                                          value="#{msg_aplic.prt_Finan_usarCentralFinanceiro}"/>
                            
                            <h:selectBooleanCheckbox
                                    value="#{ConfiguracaoFinanceiroControle.confFinanceiro.permitirContaOutraUnidade}"/>
                            <h:outputText styleClass="tituloCampos"
                                          value="#{msg_aplic.prt_Finan_selecionarContaOutraEmpresa}"/>

                            <h:selectBooleanCheckbox
                                    id="espeficicarCompetencia"
                                    value="#{ConfiguracaoFinanceiroControle.confFinanceiro.especificarCompetencia}"
                                    styleClass="tooltipster"
                                    title="Mostrar no Demonstrativo Financeiro e DRE os parametros Competência Quitada e Competência Não Quitada"/>
                            <h:outputText styleClass="tituloCampos tooltipster"
                                          value="#{msg_aplic.prt_Finan_especificarCompetencia}"
                                    title="Mostrar no Demonstrativo Financeiro e DRE os parametros Competência Quitada e Competência Não Quitada"/>
                            
                            
                            <h:selectBooleanCheckbox
                                    id="fecharCaixa"
                                    value="#{ConfiguracaoFinanceiroControle.confFinanceiro.fecharCaixaAutomaticamente}"
                                    styleClass="tooltipster"
                                    title="Fechar automaticamente caixas abertos todos os dias, na madrugada do dia posterior à data de abertura do caixa."/>
                            <h:outputText styleClass="tituloCampos tooltipster"
                                          value="Fechar automaticamente caixas abertos"
                                    title="Fechar automaticamente caixas abertos todos os dias, na madrugada do dia posterior à data de abertura do caixa."/>

                            <h:selectBooleanCheckbox
                                    id="movimentacaoconta"
                                    rendered="#{ConfiguracaoFinanceiroControle.usuarioLogado.administrador}"
                                    value="#{ConfiguracaoFinanceiroControle.confFinanceiro.usarMovimentacaoContas}">
							<a4j:support event="onchange" reRender="form:mensagembuilder"
										 action="#{ConfiguracaoFinanceiroControle.registrarMarcarDesmarcarConfiguracaoUsarMovimentacaoContas}"/>
							</h:selectBooleanCheckbox>

                            <h:outputText styleClass="tituloCampos"
                                          rendered="#{ConfiguracaoFinanceiroControle.usuarioLogado.administrador}"
                                          value="#{msg_aplic.prt_Finan_SolicitaSenhaLancarConta}"/>

                            <h:selectBooleanCheckbox
                                    id="habilitarExportacaoAlterData"
                                    value="#{ConfiguracaoFinanceiroControle.confFinanceiro.habilitarExportacaoAlterData}"
                                    styleClass="tooltipster"
                                    title="Marque esta opção somente se utilizar o sistema AlterData e desejar trabalhar com exportação de dados para este sistema. Com a opçao habilitada, será exibido novos campos no contas a pagar e no contas a receber, todos referentes ao mercado contábil. Se você não utiliza o sistema contábil AlterData, então desabilite esta opção!"/>
                            <h:outputText styleClass="tituloCampos tooltipster"
                                          value="#{msg_aplic.prt_Finan_exportacaoAlterData}"
                                          title="Marque esta opção somente se utilizar o sistema AlterData e desejar trabalhar com exportação de dados para este sistema. Com a opçao habilitada, será exibido novos campos no contas a pagar e no contas a receber, todos referentes ao mercado contábil. Se você não utiliza o sistema contábil AlterData, então desabilite esta opção!"/>

							<h:selectBooleanCheckbox
									id="cnpjObrigatorioFornecedor"
									value="#{ConfiguracaoFinanceiroControle.confFinanceiro.cnpjObrigatorioFornecedor}"
									styleClass="tooltipster"
									title="Marque esta opção somente se deseja habilitar a obrigatoriedade do campo CNPJ no cadastro de fornecedor."/>
							<h:outputText styleClass="tituloCampos tooltipster"
										  value="Obrigatorio informar CPF/CNPJ no cadastro de Fornecedor"
										  title="Marque esta opção somente se deseja habilitar a obrigatoriedade do campo CNPJ no cadastro de fornecedor."/>

							<h:selectBooleanCheckbox
									id="contaPagarCompraEstoque"
									value="#{ConfiguracaoFinanceiroControle.confFinanceiro.contaPagarCompraEstoque}"
									styleClass="tooltipster"
									title="Marque esta opção somente se deseja habilitar a criação de conta a pagar assim que uma compra de itens para o estoque é lançada."/>
							<h:outputText styleClass="tituloCampos tooltipster"
										  value="Criar conta a pagar no financeiro ao lançar compra no estoque"
										  title="Marque esta opção somente se deseja habilitar a criação de conta a pagar assim que uma compra de itens para o estoque é lançada."/>

							<h:selectBooleanCheckbox
									id="ordemCompra"
									value="#{ConfiguracaoFinanceiroControle.confFinanceiro.ordemCompraEstoque}"
									styleClass="tooltipster"
									title="Marque esta opção somente se deseja habilitar o fluxo de ordem de compra ao lançar uma compra de estoque fazendo com que seja necessario autorizar ou negar."/>
							<h:outputText styleClass="tituloCampos tooltipster"
										  value="Habilitar ordem de compra ao lançar compra estoque"
										  title="Marque esta opção somente se deseja habilitar o fluxo de ordem de compra ao lançar uma compra de estoque fazendo com que seja necessario autorizar ou negar."/>

							<h:selectBooleanCheckbox
									id="colaboradorInativo"
									value="#{ConfiguracaoFinanceiroControle.confFinanceiro.contaPagarReceberColabInativo}"
									styleClass="tooltipster"
									title="Marque esta opção caso deseje que os colaboradores INATIVOS também apareçam </br> na lista de \"Pagar para\" e \"Receber de\" no contas a pagar e a receber."/>
							<h:outputText styleClass="tituloCampos tooltipster"
										  value="Permite Pagar/Receber contas de colaboradores inativos"
										  title="Marque esta opção caso deseje que os colaboradores INATIVOS também apareçam </br> na lista de \"Pagar para\" e \"Receber de\" no contas a pagar e a receber."/>

							<h:selectBooleanCheckbox
									id="habilitarAlteracaoValorPrevisto"
									value="#{ConfiguracaoFinanceiroControle.confFinanceiro.habilitarAlteracaoValorPrevisto}"
									styleClass="tooltipster"
									title="Marque esta opção se deseja habilitar que o campo valor previsto nas contas a pagar/receber sejam atualizados ao alterar o valor original da conta!"/>
							<h:outputText styleClass="tituloCampos tooltipster"
										  value="Alterar o valor previsto quando editar o valor da Conta a Pagar/Receber"
										  title="Marque esta opção se deseje que quando alterar o valor da conta a pagar/receber, automaticamente já altere o valor previsto também."/>

							<c:if test="${ConfiguracaoFinanceiroControle.usuarioLogado.usuarioPactoSolucoes}">
								<h:selectBooleanCheckbox id="apresentarValorPago"
														 value="#{ConfiguracaoFinanceiroControle.confFinanceiro.apresentarValorPago}"
														 styleClass="tooltipster"
														 title="Marque esta opção caso deseje que seja apresentado em \"Ver Lan?amentos\" uma coluna com o valor pago da conta antes da quitação e caso seu valor tenha sido alterado."/>
								<h:outputText styleClass="tituloCampos tooltipster"
											  value="Apresentar valor original da conta antes da quitação"
											  title="Marque esta opção caso deseje que seja apresentado em \"Ver Lançamentos\" uma coluna com o valor pago da conta antes da quitação e caso seu valor tenha sido alterado."/>

								<h:selectBooleanCheckbox id="permitirTipoPlano"
														 value="#{ConfiguracaoFinanceiroControle.confFinanceiro.permitirTipoPlanoContaFilho}"
														 styleClass="tooltipster"
														 title="Ao habilitar essa configuração será possível escolher se o novo plano de contas será entrada e saída independente do tipo definido no plano de contas pai."/>
								<h:outputText styleClass="tituloCampos tooltipster"
											  value="Permitir informar tipo de plano de conta filho"
											  title="Ao habilitar essa configuração será possível escolher se o novo plano de contas será entrada e saída independente do tipo definido no plano de contas pai."/>

								<h:selectBooleanCheckbox id="informarFavorecidoNaMovimentacao"
														 value="#{ConfiguracaoFinanceiroControle.confFinanceiro.informarFavorecidoAoRealizarMovimentacaoRecebiveis}"
														 styleClass="tooltipster"
														 title="Ao habilitar essa configuração será possível escolher o favorecido na movimentação de recebíveis."/>
								<h:outputText styleClass="tituloCampos tooltipster"
											  value="Informar favorecido ao realizar a movimentação de recebíveis"
											  title="Ao habilitar essa configuração será possível escolher o favorecido na movimentação de recebíveis."/>

								<h:selectBooleanCheckbox id="buscarFornecedorTodasUnidades"
														 value="#{ConfiguracaoFinanceiroControle.confFinanceiro.buscarFornecedorTodasUnidades}"
														 styleClass="tooltipster"
														 title="Ao habilitar essa configuração, quando for consultar o nome de um fornecedor no contas a pagar/receber ou na movimentação de recebíveis</br>
											   então o sistema irá consultar os fornecedores independente da unidade logada, trazendo de várias unidades e possibilitando selecionar."/>
								<h:outputText styleClass="tituloCampos tooltipster"
											  value="Buscar fornecedores de todas unidades (multiempresa)"
											  title="Ao habilitar essa configuração, quando for consultar o nome de um fornecedor no contas a pagar/receber ou na movimentação de recebíveis</br>
											   então o sistema irá consultar os fornecedores independente da unidade logada, trazendo de várias unidades e possibilitando selecionar."/>

								<h:selectBooleanCheckbox id="obrigarPreenchimentoManualDtCompetencia"
														 value="#{ConfiguracaoFinanceiroControle.confFinanceiro.obrigarPreenchimentoManualDtCompetencia}"
														 styleClass="tooltipster"
														 title="Ao habilitar essa configuração, o sistema não preencherá automaticamente o campo data de competência nos lançamentos de contas a pagar e a receber."/>
								<h:outputText styleClass="tituloCampos tooltipster"
											  value="Obrigar preenchimento manual da data de competência"
											  title="Ao habilitar essa configuração, o sistema não preencherá automaticamente o campo data de competência nos lançamentos de contas a pagar e a receber."/>

							</c:if>
						</h:panelGrid>

						<h:panelGroup rendered="#{ConfiguracaoFinanceiroControle.usuarioLogado.administrador}" layout="block" id="panelFinanAvancado" style="margin-left: 4%;">
							<h:panelGrid columnClasses="colunaCenter" rowClasses="linhaPar, linhaImpar" columns="1"
										 width="44%"
										 style="border: 5px solid silver">
								<h:outputText style="font-weight: bold; font-size: 14px"
											  value="Financeiro Avançado"/>
								<h:outputText styleClass="text" value=""/>

								<h:panelGrid columnClasses="colunaDireita, colunaEsquerda"
											 rowClasses="linhaPar, linhaImpar"
											 style="display: inline;"
											 columns="2" width="100%">
									<h:outputText style="font-weight: bold; font-size: 12px"
												  value="Status: "/>
									<h:outputText style="font-weight: bold; font-size: 12px; color: green"
												  rendered="#{ConfiguracaoFinanceiroControle.statusFinanceiroAvancadoExibir eq 'Ativado'}"
												  value="#{ConfiguracaoFinanceiroControle.statusFinanceiroAvancadoExibir}"/>
									<h:outputText style="font-weight: bold; font-size: 12px; color: red"
												  rendered="#{ConfiguracaoFinanceiroControle.statusFinanceiroAvancadoExibir eq 'Desativado'}"
												  value="#{ConfiguracaoFinanceiroControle.statusFinanceiroAvancadoExibir}"/>
								</h:panelGrid>

								<a4j:commandButton value="Habilitar"
												   rendered="#{!ConfiguracaoFinanceiroControle.usarFinanceiroAvancado}"
												   action="#{ConfiguracaoFinanceiroControle.habilitarFinanceiroAvancado}"
												   reRender="form"
												   oncomplete="#{ConfiguracaoFinanceiroControle.mensagemNotificar}"/>
								<a4j:commandButton value="Desabilitar"
												   rendered="#{ConfiguracaoFinanceiroControle.usarFinanceiroAvancado}"
												   action="#{ConfiguracaoFinanceiroControle.desabilitarFinanceiroAvancado}"
												   reRender="form"
												   oncomplete="#{ConfiguracaoFinanceiroControle.mensagemNotificar}"/>
								<h:outputText value=""/>

							</h:panelGrid>
						</h:panelGroup>

                    </rich:tab>
                    <rich:tab id="dadosBasico" label="Taxa da operadora de cartão" switchType="Client">
                    	
                    	&nbsp;<h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_finan_configuracaoFinanceiro_taxa}:"/>
                    	
                    	<h:panelGrid columnClasses="colunaDireita, colunaEsquerda" rowClasses="linhaPar, linhaImpar" columns="2" width="100%"> 
                    		    <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Finan_Lancamento_planoContas}"/>
							    <h:panelGroup>
									<table cellpadding="0" cellspacing="0">
							            <tr valign="top">
							                <td><h:inputText id="nomePlanoSelecionadoRateio" size="50" maxlength="50"
							                                 onfocus="focusinput(this);" styleClass="form"
							                                 value="#{PlanoContasControle.planoNome}">
							                    <a4j:support event="onchange"
							                                 action="#{PlanoContasControle.setarPlanoPaiVazio}" reRender="form"/>
							                </h:inputText>
							
							                    <rich:suggestionbox height="200" width="400"
							                                        for="nomePlanoSelecionadoRateio" status="statusInComponent"
							                                        immediate="true"
							                                        suggestionAction="#{PlanoContasControle.executarAutocompletePesqPlanoContas}"
							                                        minChars="1" rowClasses="linhaImpar, linhaPar" var="result"
							                                        id="suggestionResponsavelRateio">
							                        <a4j:support event="onselect" reRender="form"
							                                     focus="nomeCentroSelecionadoRateio"
							                                     action="#{PlanoContasControle.selecionarPlanoContas}">
							                        </a4j:support>
							                        <h:column>
							                            <f:facet name="header">
							                                <h:outputText value="Nome" styleClass="textverysmall"/>
							                            </f:facet>
							                            <h:outputText styleClass="textverysmall"
							                                          value="#{result.descricaoCurta}"/>
							                        </h:column>
							                        <h:column>
							                            <f:facet name="header">
							                                <h:outputText value="Tipo" styleClass="textverysmall"/>
							                            </f:facet>
							                            <h:outputText styleClass="textverysmall"
							                                          value="#{result.tipoPadrao.descricao}"/>
							                        </h:column>
							                    </rich:suggestionbox></td>
							                <td>&nbsp;<a4j:commandLink action="#{PlanoContasControle.verificarConsultaCorretaRateio}"
							                                           reRender="modalPlanos"
							                                           id="btAddPlanoRateio" value="Consultar"
							                                           oncomplete="Richfaces.showModalPanel('modalPlanos')"/>
							            </tr>
							        </table>
							    </h:panelGroup>
							    <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Finan_Lancamento_centroCusto}"/>
							    <h:panelGroup>
							
							        <table cellpadding="0" cellspacing="0">
							            <tr valign="top">
							                <td>
							                    <h:inputText id="nomeCentroSelecionadoRateio" size="50" maxlength="50"
							                                 onblur="blurinput(this);" onfocus="focusinput(this);"
							                                 styleClass="form" value="#{CentroCustosControle.centroNome}">
							                        <a4j:support event="onchange"
							                                     action="#{CentroCustosControle.setarCentroVazio}" reRender="form"/>
							                    </h:inputText>
							
							                    <rich:suggestionbox height="200" width="400"
							                                        for="nomeCentroSelecionadoRateio" status="statusInComponent"
							                                        immediate="true"
							                                        suggestionAction="#{CentroCustosControle.executarAutocompletePesqCentroCusto}"
							                                        minChars="1" rowClasses="linhaImpar, linhaPar" var="result"
							                                        id="suggestionCentroCustoRateio">
							                        <a4j:support event="onselect" reRender="form" focus="rateioFormaPagto"
							                                     action="#{CentroCustosControle.selecionarCentroCusto}">
							                        </a4j:support>
							                        <h:column>
							                            <f:facet name="header">
							                                <h:outputText value="Nome" styleClass="textverysmall"/>
							                            </f:facet>
							                            <h:outputText styleClass="textverysmall"
							                                          value="#{result.descricaoCurta}"/>
							                        </h:column>
							                    </rich:suggestionbox>
							                </td>
							                <td>&nbsp;<a4j:commandLink reRender="modalCentros"
							                                           id="btAddCentroRateio" value="Consultar"
							                                           oncomplete="Richfaces.showModalPanel('modalCentros')"/>
							                </td>
							            </tr>
							        </table>

							    </h:panelGroup>
                                                            <center>
                                                             <h:panelGroup>
                                                                  
                                                                 <a4j:commandButton reRender="modalProcessoRealizado"
                                                                                    value="Atualizar taxas já lançadas"
                                                                                    onclick="if(!confirm('A alteração do centro de custo e o plano de contas de taxas já lançadas pode alterar relatórios de meses passados. Deseja continuar?')){return false;};"
                                                                                    rendered="#{ConfiguracaoFinanceiroControle.usuarioLogado.administrador}"
                                                                                    oncomplete="Richfaces.showModalPanel('modalProcessoRealizado');"
                                                                                    action="#{ConfiguracaoFinanceiroControle.rodarAtualizacaoTaxaCartao}"/>
                                                                
                                                                </h:panelGroup>
                                                                </center>
                    		
                    	</h:panelGrid>
                    </rich:tab>

			<rich:tab id="dadosBasicoBoleto" label="Taxa Boleto" switchType="Client">

				&nbsp;<h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_finan_configuracaoFinanceiro_taxaBoleto}:"/>

				<h:panelGrid columnClasses="colunaDireita, colunaEsquerda" rowClasses="linhaPar, linhaImpar" columns="2" width="100%">
					<h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Finan_Lancamento_planoContas}"/>
					<h:panelGroup>
						<table cellpadding="0" cellspacing="0">
							<tr valign="top">
								<td><h:inputText id="nomePlanoSelecionadoRateioBoleto" size="50" maxlength="50"
												 onfocus="focusinput(this);" styleClass="form"
												 value="#{PlanoContasControle.planoNomeBoleto}">
									<a4j:support event="onchange"
												 action="#{PlanoContasControle.setarPlanoPaiVazio}" reRender="form"/>
								</h:inputText>

									<rich:suggestionbox height="200" width="400"
														for="nomePlanoSelecionadoRateioBoleto" status="statusInComponent"
														immediate="true"
														suggestionAction="#{PlanoContasControle.executarAutocompletePesqPlanoContas}"
														minChars="1" rowClasses="linhaImpar, linhaPar" var="resultBoleto"
														id="suggestionResponsavelRateioBoleto">
										<a4j:support event="onselect" reRender="form"
													 focus="nomeCentroSelecionadoRateioBoleto"
													 action="#{PlanoContasControle.selecionarPlanoContasBoleto}">
										</a4j:support>
										<h:column>
											<f:facet name="header">
												<h:outputText value="Nome" styleClass="textverysmall"/>
											</f:facet>
											<h:outputText styleClass="textverysmall"
														  value="#{resultBoleto.descricaoCurta}"/>
										</h:column>
										<h:column>
											<f:facet name="header">
												<h:outputText value="Tipo" styleClass="textverysmall"/>
											</f:facet>
											<h:outputText styleClass="textverysmall"
														  value="#{resultBoleto.tipoPadrao.descricao}"/>
										</h:column>
									</rich:suggestionbox></td>
								<td>&nbsp;<a4j:commandLink action="#{PlanoContasControle.verificarConsultaCorretaRateio}"
														   reRender="modalPlanos"
														   id="btAddPlanoRateioBoleto" value="Consultar"
										oncomplete="Richfaces.showModalPanel('modalPlanos')">
									<a4j:support event="onclick" action="#{PlanoContasControle.editandoTaxaBoleto}"/>
									</a4j:commandLink>
							</tr>
						</table>
					</h:panelGroup>
					<h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Finan_Lancamento_centroCusto}"/>
					<h:panelGroup>

						<table cellpadding="0" cellspacing="0">
							<tr valign="top">
								<td>
									<h:inputText id="nomeCentroSelecionadoRateioBoleto" size="50" maxlength="50"
												 onblur="blurinput(this);" onfocus="focusinput(this);"
												 styleClass="form" value="#{CentroCustosControle.centroNomeBoleto}">
										<a4j:support event="onchange"
													 action="#{CentroCustosControle.setarCentroVazio}" reRender="form"/>
									</h:inputText>

									<rich:suggestionbox height="200" width="400"
														for="nomeCentroSelecionadoRateioBoleto" status="statusInComponent"
														immediate="true"
														suggestionAction="#{CentroCustosControle.executarAutocompletePesqCentroCusto}"
														minChars="1" rowClasses="linhaImpar, linhaPar" var="resultBoleto"
														id="suggestionCentroCustoRateioBoleto">
										<a4j:support event="onselect" reRender="form" focus="rateioFormaPagto"
													 action="#{CentroCustosControle.selecionarCentroCustoBoleto}">
										</a4j:support>
										<h:column>
											<f:facet name="header">
												<h:outputText value="Nome" styleClass="textverysmall"/>
											</f:facet>
											<h:outputText styleClass="textverysmall"
														  value="#{resultBoleto.descricaoCurta}"/>
										</h:column>
									</rich:suggestionbox>
								</td>
								<td>&nbsp;<a4j:commandLink reRender="modalCentros"
														   id="btAddCentroRateioBoleto" value="Consultar"
														   oncomplete="Richfaces.showModalPanel('modalCentros')">
									<a4j:support event="onclick" action="#{CentroCustosControle.editandoTaxaBoleto}"/>
								</a4j:commandLink>
								</td>
							</tr>
						</table>

					</h:panelGroup>
					<center>
						<h:panelGroup>

							<a4j:commandButton reRender="modalProcessoRealizado"
											   value="Atualizar taxas já lançadas"
											   onclick="if(!confirm('A alteração do centro de custo e o plano de contas de taxas já lançadas pode alterar relatórios de meses passados. Deseja continuar?')){return false;};"
											   rendered="#{ConfiguracaoFinanceiroControle.usuarioLogado.administrador}"
											   oncomplete="Richfaces.showModalPanel('modalProcessoRealizado');"
											   action="#{ConfiguracaoFinanceiroControle.rodarAtualizacaoTaxaBoleto}"/>

						</h:panelGroup>
					</center>

				</h:panelGrid>
			</rich:tab>

			<rich:tab id="dadosBasicoPix" label="Taxa Pix" switchType="Client"
					  action="#{ConfiguracaoFinanceiroControle.iniciarTabTaxaPix}">
				&nbsp;<h:outputText styleClass="tituloCampos"
									value=" Informe o Plano de Contas e o Centro de Custos do lançamento das taxas de Pix:"/>
				<h:panelGrid id="pnConfTaxaPix" columnClasses="colunaDireita, colunaEsquerda"
							 rowClasses="linhaPar, linhaImpar" columns="2" width="100%">

					<h:outputText styleClass="tituloCampos tooltipster" value="Plano de Contas:"
								  title="Defina aqui o Plano de contas para os pix que serão movimentados para uma conta do tipo Banco."/>
					<h:panelGroup>
						<table cellpadding="0" cellspacing="0">

							<tr valign="top">
								<td>
									<h:inputText id="nomePlanoSelecionadoRateioPix" size="50" maxlength="50"
												 onfocus="focusinput(this);" styleClass="form"
												 value="#{ConfiguracaoFinanceiroControle.nomePlanoDefaultTaxaPix}">
										<a4j:support event="onchange"
													 action="#{ConfiguracaoFinanceiroControle.setarPlanoPaiVazioTaxaPix}"/>
									</h:inputText>

									<rich:suggestionbox height="200" width="400"
														for="nomePlanoSelecionadoRateioPix" status="statusInComponent"
														immediate="true"
														suggestionAction="#{ConfiguracaoFinanceiroControle.executarAutocompletePesqPlanoContas}"
														minChars="1" rowClasses="linhaImpar, linhaPar" var="result"
														nothingLabel="Nenhum Plano de contas encontrada com este nome"
														id="suggestionResponsavelRateioPix">
										<a4j:support event="onselect" reRender="pnConfTaxaPix"
													 action="#{ConfiguracaoFinanceiroControle.selecionarPlanoContasTaxaPix}">
										</a4j:support>
										<h:column>
											<f:facet name="header">
												<h:outputText value="Nome" styleClass="textverysmall"/>
											</f:facet>
											<h:outputText styleClass="textverysmall"
														  value="#{result.descricaoCurta}"/>
										</h:column>
										<h:column>
											<f:facet name="header">
												<h:outputText value="Tipo" styleClass="textverysmall"/>
											</f:facet>
											<h:outputText styleClass="textverysmall"
														  value="#{result.tipoPadrao.descricao}"/>
										</h:column>
									</rich:suggestionbox></td>
								<td>
							</tr>
						</table>
					</h:panelGroup>

					<h:outputText styleClass="tituloCampos tooltipster" value="Centro de Custo:"
								  title="Defina aqui o Centro de custo para os pix que serão movimentados para uma conta do tipo Banco."/>
					<h:panelGroup>
						<table cellpadding="0" cellspacing="0">

							<tr valign="top">
								<td>
									<h:inputText id="nomeCentroCustoSelecionadoRateioPix" size="50" maxlength="50"
												 onfocus="focusinput(this);" styleClass="form"
												 value="#{ConfiguracaoFinanceiroControle.nomeCentroCustoDefaultTaxaPix}">
										<a4j:support event="onchange"
													 action="#{ConfiguracaoFinanceiroControle.setarCentroCustoDevolucaoVazioTaxaPix}"/>
									</h:inputText>

									<rich:suggestionbox height="200" width="400"
														for="nomeCentroCustoSelecionadoRateioPix"
														status="statusInComponent"
														immediate="true"
														suggestionAction="#{ConfiguracaoFinanceiroControle.executarAutocompletePesqCentrosCusto}"
														minChars="1" rowClasses="linhaImpar, linhaPar" var="result"
														nothingLabel="Nenhum Centro de custo encontrada com este nome"
														id="suggestionCentroCustoResponsavelRateioPix">
										<a4j:support event="onselect" reRender="pnConfTaxaPix"
													 action="#{ConfiguracaoFinanceiroControle.selecionarCentroCustoTaxaPix}">
										</a4j:support>
										<h:column>
											<f:facet name="header">
												<h:outputText value="Nome" styleClass="textverysmall"/>
											</f:facet>
											<h:outputText styleClass="textverysmall"
														  value="#{result.descricaoCurta}"/>
										</h:column>
									</rich:suggestionbox>
								</td>
							</tr>
						</table>
					</h:panelGroup>
				</h:panelGrid>
			</rich:tab>

			<rich:tab id="devolucaoCheque" label="Devolução de cheques" switchType="Client" actionListener="#{ConfiguracaoFinanceiroControle.iniciarTabDevolucaoCheque}">
                    	<h:panelGrid id="pnConfDevCheq" columnClasses="colunaDireita, colunaEsquerda" rowClasses="linhaPar, linhaImpar" columns="2" width="100%">
							<h:selectBooleanCheckbox
									value="#{ConfiguracaoFinanceiroControle.confFinanceiro.adicionarDevolucaoRelatorioComissao}"
									styleClass="tooltipster"
									title="Ao marcar esta opção, o valor referente aos cheques devolvidos será abatido no relatório de comissão do consultor como um valor negativo."/>
							<h:outputText styleClass="tituloCampos tooltipster"
										  value="Considerar cheques devolvidos no relatório de comissão para consultor"
										  title="Ao marcar esta opção, o valor referente aos cheques devolvidos será abatido no relatório de comissão do consultor como um valor negativo."/>


							<h:outputText styleClass="tituloCampos tooltipster" value="Plano de contas de cheques devolvidos (Despesa)"
										  title="Defina aqui o Plano de contas DESPESA para os cheques que serão devolvidos e movimentados para uma conta do tipo pendência.
Obs: Para definir o Plano de Contas referente à RECEITA oriunda de cheques devolvidos, faça isso pelo rateio integração. Procure pelo produto: 'PAGAMENTO DE CHEQUES DEVOLVIDOS'."/>
							<h:panelGroup>
								<table cellpadding="0" cellspacing="0">

									<tr valign="top">
										<td>
                                            <h:inputText id="nomePlanoSelecionadoRateioDevolucao" size="50" maxlength="50"
														 onfocus="focusinput(this);" styleClass="form"
														 value="#{ConfiguracaoFinanceiroControle.nomePlanoDefaultChequesDevolvidos}">
											<a4j:support event="onchange"
														 action="#{ConfiguracaoFinanceiroControle.setarPlanoPaiVazio}"/>
										</h:inputText>

											<rich:suggestionbox height="200" width="400"
																for="nomePlanoSelecionadoRateioDevolucao" status="statusInComponent"
																immediate="true"
																suggestionAction="#{ConfiguracaoFinanceiroControle.executarAutocompletePesqPlanoContas}"
																minChars="1" rowClasses="linhaImpar, linhaPar" var="result"
																nothingLabel="Nenhum Plano de contas encontrada com este nome"
																id="suggestionResponsavelRateioDevolucao">
												<a4j:support event="onselect" reRender="pnConfDevCheq"
															 action="#{ConfiguracaoFinanceiroControle.selecionarPlanoContas}">
												</a4j:support>
												<h:column>
													<f:facet name="header">
														<h:outputText value="Nome" styleClass="textverysmall"/>
													</f:facet>
													<h:outputText styleClass="textverysmall"
																  value="#{result.descricaoCurta}"/>
												</h:column>
												<h:column>
													<f:facet name="header">
														<h:outputText value="Tipo" styleClass="textverysmall"/>
													</f:facet>
													<h:outputText styleClass="textverysmall"
																  value="#{result.tipoPadrao.descricao}"/>
												</h:column>
											</rich:suggestionbox></td>
										<td>
									</tr>
								</table>
							</h:panelGroup>

							<h:outputText styleClass="tituloCampos tooltipster" value="Centro de custo de cheques devolvidos (Despesa)"
										  title="Defina aqui o Centro de custo DESPESA para os cheques que serão devolvidos e movimentados para uma conta do tipo pendência.
Obs: Para definir o Centro de Custo referente à RECEITA oriunda de cheques devolvidos, faça isso pelo rateio integração. Procure pelo produto: 'PAGAMENTO DE CHEQUES DEVOLVIDOS'."/>
							<h:panelGroup>
								<table cellpadding="0" cellspacing="0">

									<tr valign="top">
										<td>
											<h:inputText id="nomeCentroCustoSelecionadoRateioDevolucao" size="50" maxlength="50"
														 onfocus="focusinput(this);" styleClass="form"
														 value="#{ConfiguracaoFinanceiroControle.nomeCentroCustoDefaultChequesDevolvidos}">
												<a4j:support event="onchange"
															 action="#{ConfiguracaoFinanceiroControle.setarCentroCustoDevolucaoVazio}"/>
											</h:inputText>

											<rich:suggestionbox height="200" width="400"
																for="nomeCentroCustoSelecionadoRateioDevolucao" status="statusInComponent"
																immediate="true"
																suggestionAction="#{ConfiguracaoFinanceiroControle.executarAutocompletePesqCentrosCusto}"
																minChars="1" rowClasses="linhaImpar, linhaPar" var="result"
																nothingLabel="Nenhum Centro de custo encontrada com este nome"
																id="suggestionCentroCustoResponsavelRateioDevolucao">
												<a4j:support event="onselect" reRender="pnConfDevCheq"
															 action="#{ConfiguracaoFinanceiroControle.selecionarCentroCusto}">
												</a4j:support>
												<h:column>
													<f:facet name="header">
														<h:outputText value="Nome" styleClass="textverysmall"/>
													</f:facet>
													<h:outputText styleClass="textverysmall"
																  value="#{result.descricaoCurta}"/>
												</h:column>
											</rich:suggestionbox>
										</td>
									</tr>
								</table>
							</h:panelGroup>


							<h:selectBooleanCheckbox
									value="#{ConfiguracaoFinanceiroControle.confFinanceiro.bloquearAlunoChequeDevolvido}"
									styleClass="tooltipster"

									title="Ao marcar esta opção, o(s) cliente(s) relacionado(s) ao cheque serão bloqueado(s).">

							</h:selectBooleanCheckbox>

							<h:outputText styleClass="tituloCampos tooltipster"
										  value="Bloquear aluno com cheque devolvido"
										  title="Ao marcar esta opção, o(s) cliente(s) relacionado(s) ao cheque serão bloqueado(s)."/>

							<h:outputText  styleClass="tituloCampos tooltipster"
										  value="Mensagem bloqueio catraca"/>

							<h:inputTextarea rows="5" style="width: 100%" value="#{ConfiguracaoFinanceiroControle.confFinanceiro.mensagembloqueio}"/>


                    	</h:panelGrid>
                    </rich:tab>
                        <rich:tab id="planoContasPadrao" label="Plano de contas padrão" switchType="Client"
                                  rendered="#{ConfiguracaoFinanceiroControle.usuarioLogado.administrador}">

                            <h:outputText styleClass="text" rendered="#{!ConfiguracaoFinanceiroControle.permitePlanoPadrao}"
                                          value="O plano de contas desta empresa não pode ser alterado pois possui rateios relacionados."/>
                            <center>
                                <h:panelGroup>
                                    <a4j:commandButton value="Repovoar o plano de contas"
                                                       onclick="if(!confirm('Este processo apaga o plano de contas atual e insere o padrão. Deseja continuar?')){return false;};"
                                                       action="#{ConfiguracaoFinanceiroControle.rePovoarPlanoContas}"
                                                       disabled="#{!ConfiguracaoFinanceiroControle.permitePlanoPadrao}"
                                                       reRender="form"/>

                                </h:panelGroup>
                            </center>
                        </rich:tab>

                        <rich:tab id="ESTORNORECIBODEVOLUCAO" label="Estornar devolução" switchType="Client"
                                  rendered="#{ConfiguracaoFinanceiroControle.usuarioLogado.administrador}">
                    	<h:panelGrid columnClasses="colunaDireita, colunaEsquerda" rowClasses="linhaPar, linhaImpar" columns="2" width="100%">
                            <h:outputText styleClass="text" value="Código contrato:"/>
                            <h:inputText value="#{ConfiguracaoFinanceiroControle.codigoContratoEstornoRecibo}"/>
                            <a4j:commandButton value="Estornar"
                                               action="#{ConfiguracaoFinanceiroControle.estornarReciboDevolucao}"
                                               oncomplete="#{ConfiguracaoFinanceiroControle.msgAlert}"/>
                            <h:outputText value=""/>
                    	</h:panelGrid>
                    </rich:tab>

			<rich:tab rendered="#{LoginControle.apresentarOpenBank}" id="openBank" label="OpenBank" switchType="Client">

					<rich:simpleTogglePanel switchType="client" label="Stone" width="100%" opened="false">
						<h:panelGrid columns="1" width="100%" headerClass="subordinado">

							<h:panelGroup rendered="#{!ConfiguracaoFinanceiroControle.contaStoneIncluida}">
								<h:outputText styleClass="font-weight: bold; font-size: 12px; color: red"
											  value="Obs: O consentimento só pode ser realizado uma unica vez por empresa. Após realizado, não há como reverter a operação.
								Tenha total certeza da conta stone que irá dar consentimento para a empresa em que se encontra logado no momento."/>
							</h:panelGroup>

							<h:panelGroup rendered="#{!ConfiguracaoFinanceiroControle.contaStoneIncluida}">
								<a4j:commandButton styleClass="botoes nvoBt"
											   value="Realizar Consentimento" oncomplete="#{ConfiguracaoFinanceiroControle.mensagemNotificar eq '' ? ConfiguracaoFinanceiroControle.msgAlert : ConfiguracaoFinanceiroControle.mensagemNotificar}"
											   action="#{ConfiguracaoFinanceiroControle.darConsentimentoStoneOpenBank}"
												reRender="mensagens"/>
							</h:panelGroup>
							<h:panelGroup rendered="#{ConfiguracaoFinanceiroControle.contaStoneIncluida}">
								<h:outputText styleClass="font-weight: bold; font-size: 12px; color: green"
											  value="Consentimento realizado com sucesso."/>
							</h:panelGroup>
							<h:panelGroup rendered="#{ConfiguracaoFinanceiroControle.contaStoneIncluida}">
								<h:outputText styleClass="font-weight: bold; font-size: 12px; color: green"
											  value="#{ConfiguracaoFinanceiroControle.contaStoneDetalhes}"/>
							</h:panelGroup>
						</h:panelGrid>
					</rich:simpleTogglePanel>

			</rich:tab>

			<rich:tab id="ProcessosFinan" label="Processos" switchType="Client"
					  rendered="#{ConfiguracaoFinanceiroControle.usuarioLogado.usuarioPactoSolucoes}">

				<h:panelGroup layout="block" id="panelGeralProcessos" style="height: 300px;">

					<rich:simpleTogglePanel switchType="client" label="Acrescentar dias na compensação de cheques"
											rendered="#{ConfiguracaoFinanceiroControle.usuarioLogado.administrador}"
											id="idAcresentarDiasCheque" width="100%" opened="false">
						<h:panelGrid columnClasses="colunaCenter" rowClasses="linhaPar, linhaImpar" columns="1"
									 width="100%"
									 style="border: 5px solid silver">
							<h:outputText style="font-weight: bold; font-size: 14px"
										  value="Acrescentar dias na compensação de cheques."/>
							<h:outputText styleClass="text" value=""/>

							<h:outputText style="font-weight: bold; font-size: 12px"
										  value="Cheques compensando a partir de: "/>
							<rich:calendar id="dataChequeAPartirDe"
										   value="#{ConfiguracaoFinanceiroControle.compensacaoChequeApartirDe}"
										   inputSize="6"
										   inputClass="form"
										   oninputblur="blurinput(this);"
										   oninputfocus="focusinput(this);"
										   oninputchange="return validar_Data(this.id);"
										   datePattern="dd/MM/yyyy"
										   enableManualInput="true"
										   zindex="2"
										   showWeeksBar="false"/>

							<h:outputText style="font-weight: bold; font-size: 12px"
										  value="Quantidade dias acrescentar: "/>
							<h:inputText value="#{ConfiguracaoFinanceiroControle.qtdDiasAcrescentarCompensacaoCheque}"
										 size="2"/>

							<a4j:commandButton value="Ajustar"
											   action="#{ConfiguracaoFinanceiroControle.realizarAjusteCompensacaoCheque}"
											   reRender="form"
											   oncomplete="#{ConfiguracaoFinanceiroControle.mensagemNotificar}"/>
							<h:outputText value=""/>

						</h:panelGrid>
					</rich:simpleTogglePanel>

					<rich:simpleTogglePanel switchType="client" label="Reprocessar dados financeiros"
											id="idReprocessarDadosFinanceiros" width="100%" opened="false">
						<h:panelGrid columnClasses="colunaCenter" rowClasses="linhaPar, linhaImpar" columns="1"
									 width="100%"
									 style="border: 5px solid silver">
							<h:outputText style="font-weight: bold; font-size: 14px"
										  value="Reprocessar dados financeiros"/>
							<h:outputText styleClass="text" value=""/>


							<h:outputLabel value="Empresa"/>
							<h:selectOneMenu value="#{ConfiguracaoFinanceiroControle.empresaSelecionada}">
								<f:selectItems value="#{ConfiguracaoFinanceiroControle.selectItemEmpresas}"/>
							</h:selectOneMenu>

							<h:outputText style="font-weight: bold; font-size: 12px"
										  value="Período processar"/>
							<h:panelGroup layout="block" id="panelDatasReprocessarFinanceiro">
								<rich:calendar id="dataInicioProcessar"
											   inputSize="4"
											   inputClass="form"
											   showWeeksBar="false"
											   oninputfocus="focusinput(this);"
											   oninputblur="blurinput(this);"
											   oninputchange="return validar_Data(this.id);"
											   datePattern="MM/yyyy"
											   enableManualInput="true"
											   reRender="btnAtualizarDadosRF"
											   value="#{ConfiguracaoFinanceiroControle.dataInicioProcessar}">
								</rich:calendar>
								<rich:jQuery id="mskDataInicio" selector=".rich-calendar-input" timing="onload"
											 query="mask('99/9999')"/>

								<h:outputText
										style="font-weight: bold; font-size: 12px; padding-left: 10px; padding-right: 10px"
										value="até"/>

								<rich:calendar id="dataFimProcessar"
											   inputSize="4"
											   inputClass="form"
											   showWeeksBar="false"
											   oninputfocus="focusinput(this);"
											   oninputblur="blurinput(this);"
											   oninputchange="return validar_Data(this.id);"
											   datePattern="MM/yyyy"
											   enableManualInput="true"
											   reRender="btnAtualizarDadosRF"
											   value="#{ConfiguracaoFinanceiroControle.dataFimProcessar}">
								</rich:calendar>
							</h:panelGroup>

							<a4j:commandButton value="Processar" reRender="form"
											   action="#{ConfiguracaoFinanceiroControle.processarDadosSintetico}"
											   oncomplete="#{ConfiguracaoFinanceiroControle.mensagemNotificar}"/>
							<h:outputText value=""/>

						</h:panelGrid>
					</rich:simpleTogglePanel>
				</h:panelGroup>
			</rich:tab>

			<rich:tab id="configuracaoConciliacao" label="Conciliação" switchType="Client"
					  action="#{ConfiguracaoFinanceiroControle.iniciarTabConfiguracoesConciliacao}">
				<h:panelGrid id="panelConfigsConciliacao" columnClasses="colunaDireita, colunaEsquerda"
							 rowClasses="linhaPar, linhaImpar" columns="2" width="100%">
					<h:selectBooleanCheckbox id="AltDataAutPgto"
											 value="#{ConfiguracaoFinanceiroControle.confFinanceiro.alterarDtPgtoZWAutomaticamenteConc}"
											 styleClass="tooltipster"
											 title="<b>Disponível somente para Conciliação Cielo</b></br></br> Configuração criada para que no momento do processamento do extrato o sistema compare a data prevista de pagamento que veio no extrato com a data do pagamento no sistema pacto e se elas forem diferentes, o sistema automaticamente irá alterar a data do sistema pacto, fazendo com que fique conciliado sem a necessidade de operações manuais lá na tela de conciliação.">
					</h:selectBooleanCheckbox>
					<h:outputText styleClass="tituloCampos tooltipster"
								  value="Permite conciliar data de pagamento do sistema pacto automaticamente (Cielo)"
								  title="<b>Disponível somente para Conciliação Cielo</b></br></br> Configuração criada para que no momento do processamento do extrato o sistema compare a data prevista de pagamento que veio no extrato com a data do pagamento no sistema pacto e se elas forem diferentes, o sistema automaticamente irá alterar a data do sistema pacto, fazendo com que fique conciliado sem a necessidade de operações manuais lá na tela de conciliação."/>

					<h:selectBooleanCheckbox id="criarMovimentacaoAtomaticaRecebiveisConciliacao"
											 value="#{ConfiguracaoFinanceiroControle.movimentacaoAutomaticaRecebiveisConciliacao}"
											 styleClass="tooltipster"
											 title="<b>Disponível para todos os tipos de convênio da conciliação</b></br></br> <b>Marque esta opção para habilitar o processo de movimentação automática de recebíveis da conciliação.</b></br></br>Com essa opção <b>marcada</b>, os recebíveis da conciliação serão movimentadas diariamente de forma automática, criando movimentações conforme a 'Descrição', 'Favorecido' e 'Conta Bancária' configuradas nos campos disponíveis.
						</br>Com essa opção <b>desmarcada</b>, continua sendo necessário realizar a movimentação manualmente lá na tela 'Gestão de Recebíveis' ou na própria tela de conciliação.</br>">
						<a4j:support
								action="#{ConfiguracaoFinanceiroControle.selecionarMovimentacaoAutomaticaRecebiveisConciliacao}"
								reRender="form" event="onchange"/>
					</h:selectBooleanCheckbox>
					<h:outputText styleClass="tituloCampos tooltipster"
								  value="Habilitar Movimentação Automática da Conciliação"
								  title="<b>Disponível para todos os tipos de convênio da conciliação</b></br></br> <b>Marque esta opção para habilitar o processo de movimentação automática de recebíveis da conciliação.</b></br></br>Com essa opção <b>marcada</b>, os recebíveis da conciliação serão movimentadas diariamente de forma automática, criando movimentações conforme a 'Descrição', 'Favorecido' e 'Conta Bancária' configuradas nos campos disponíveis.
						</br>Com essa opção <b>desmarcada</b>, continua sendo necessário realizar a movimentação manualmente lá na tela 'Gestão de Recebíveis' ou na própria tela de conciliação.</br>"/>
				</h:panelGrid>
				<c:if test="${ConfiguracaoFinanceiroControle.movimentacaoAutomaticaRecebiveisConciliacao}">
					<h:panelGrid id="tableMovimentacaoAutomaticaConc" style="border: ridge; border-color: #ffffff; width: 100%;">
						<h:panelGrid id="panelMovimentacaoAutomaticaConciliacao"
									 columnClasses="colunaDireita, colunaEsquerda" rowClasses="linhaPar, linhaImpar"
									 columns="2" width="100%">
							<f:facet name="header">
								<h:outputText value="Débito" style="font-weight: bold"/>
							</f:facet>

							<h:outputText styleClass="tituloCampos tooltipster"
										  title="<b>Sugestão:</b> Cartões de Débito"
										  value="Descrição Movimentação:"/>
							<h:inputText id="descricaoMovimentacaoAutomaticaRecebiveisConciliacao"
										 size="50"
										 maxlength="50"
										 onfocus="focusinput(this);"
										 title="<b>Sugestão:</b> Cartões de Débito"
										 styleClass="form tooltipster"
										 value="#{ConfiguracaoFinanceiroControle.confFinanceiro.descricaomovimentacaoautomaticaDebito}">
							</h:inputText>

							<h:outputText styleClass="tituloCampos tooltipster"
										  value="Favorecido:"/>
							<h:panelGroup layout="block" style="margin-top: 5px;">
								<h:inputText id="favorecidoMovimentacaoAutomaticaRecebiveisDebito" size="50"
											 maxlength="50"
											 onkeypress="if (event.keyCode == 13) { document.getElementById('form:favorecidoMovimentacaoAutomaticaRecebiveisDebito').focus();return false;};"
											 onfocus="focusinput(this);" styleClass="form"
											 value="#{ConfiguracaoFinanceiroControle.confFinanceiro.favorecidomovimentacaoautomaticaDebito.nome}"/>

								<rich:suggestionbox height="200" width="400"
													for="favorecidoMovimentacaoAutomaticaRecebiveisDebito"
													fetchValue="#{result}"
													suggestionAction="#{ConfiguracaoFinanceiroControle.executarAutocompleteConsultaFavorecido}"
													minChars="1" rowClasses="20"
													status="statusInComponent"
													nothingLabel="Nenhuma pessoa encontrada!"
													var="result"
													id="suggestionFavorecidoMovimentacaoAutomaticaRecebiveisDebito">
									<a4j:support event="onselect" ignoreDupResponses="true"
												 action="#{ConfiguracaoFinanceiroControle.selecionarFavorecidoDebitoSuggestionBox}"
												 reRender="form"/>

									<h:column>
										<f:facet name="header">
											<h:outputText styleClass="textverysmall" value="Nome"/>
										</f:facet>
										<h:outputText value="#{result.nome}"/>
									</h:column>
									<h:column>
										<f:facet name="header">
											<h:outputText styleClass="textverysmall" value="Tipo"/>
										</f:facet>
										<h:outputText value="#{result.tipoPessoa}"/>
									</h:column>
									<h:column>
										<f:facet name="header">
											<h:outputText styleClass="textverysmall" value="CPF/CNPJ"/>
										</f:facet>
										<h:outputText value="#{result.cfp}"/>
									</h:column>
								</rich:suggestionbox>
							</h:panelGroup>
							<h:outputText styleClass="tituloCampos tooltipster"
										  value="Conta Bancária:"
										  title="Conta que será vinculada a Conciliação Automática débito"/>
							<h:selectOneMenu
									value="#{ConfiguracaoFinanceiroControle.confFinanceiro.contamovimentacaoautomaticadebito.codigo}"
									id="contaBancariaMovimentacaoAutomaticaRecebiveisConciliacaoDebito"
									styleClass="form">
								<f:selectItems value="#{ConfiguracaoFinanceiroControle.listaComboConta}"/>
								<a4j:support
										action="#{ConfiguracaoFinanceiroControle.selecionarContaMovimentacaoAutomaticaDebito}"
										reRender="form" event="onchange"/>
							</h:selectOneMenu>
						</h:panelGrid>
						<h:panelGrid id="panelMovimentacaoAutomaticaConciliacaoCredito"
									 columnClasses="colunaDireita, colunaEsquerda" rowClasses="linhaPar, linhaImpar"
									 columns="2" width="100%">
							<f:facet name="header">
								<h:outputText value="Crédito" style="font-weight: bold"/>
							</f:facet>

							<h:outputText styleClass="tituloCampos tooltipster"
										  title="<b>Sugestão:</b> Cartões de Crédito"
										  value="Descrição Movimentação:"/>
							<h:inputText id="descricaoMovimentacaoAutomaticaRecebiveisConciliacaoCredito"
										 title="<b>Sugestão:</b> Cartões de Crédito"
										 size="50"
										 maxlength="50"
										 onfocus="focusinput(this);"
										 styleClass="form tooltipster"
										 value="#{ConfiguracaoFinanceiroControle.confFinanceiro.descricaomovimentacaoautomaticaCredito}">
							</h:inputText>

							<h:outputText styleClass="tituloCampos tooltipster"
										  value="Favorecido:"/>
							<h:panelGroup layout="block" style="margin-top: 5px;">
								<h:inputText id="favorecidoMovimentacaoAutomaticaRecebiveisConciliacaoCredito" size="50"
											 maxlength="50"
											 onkeypress="if (event.keyCode == 13) { document.getElementById('form:descricao').focus();return false;};"
											 onfocus="focusinput(this);" styleClass="form"
											 value="#{ConfiguracaoFinanceiroControle.confFinanceiro.favorecidomovimentacaoautomaticaCredito.nome}"/>

								<rich:suggestionbox height="200" width="400"
													for="favorecidoMovimentacaoAutomaticaRecebiveisConciliacaoCredito"
													fetchValue="#{result}"
													suggestionAction="#{ConfiguracaoFinanceiroControle.executarAutocompleteConsultaFavorecido}"
													minChars="1" rowClasses="20"
													status="statusInComponent"
													nothingLabel="Nenhuma pessoa encontrada!"
													var="result"
													id="suggestionFavorecidoMovimentacaoAutomaticaRecebiveisConciliacaoCredito">
									<a4j:support event="onselect" ignoreDupResponses="true"
												 action="#{ConfiguracaoFinanceiroControle.selecionarFavorecidoCreditoSuggestionBox}"
												 focus="descricao" reRender="form"/>

									<h:column>
										<f:facet name="header">
											<h:outputText styleClass="textverysmall" value="Nome"/>
										</f:facet>
										<h:outputText value="#{result.nome}"/>
									</h:column>
									<h:column>
										<f:facet name="header">
											<h:outputText styleClass="textverysmall" value="Tipo"/>
										</f:facet>
										<h:outputText value="#{result.tipoPessoa}"/>
									</h:column>
									<h:column>
										<f:facet name="header">
											<h:outputText styleClass="textverysmall" value="CPF/CNPJ"/>
										</f:facet>
										<h:outputText value="#{result.cfp}"/>
									</h:column>
								</rich:suggestionbox>
							</h:panelGroup>
							<h:outputText styleClass="tituloCampos tooltipster"
										  value="Conta Bancária:"
										  title="Conta que será vinculado a Conciliação Automática Crédito."/>
							<h:selectOneMenu
									value="#{ConfiguracaoFinanceiroControle.confFinanceiro.contamovimentacaoautomaticacredito.codigo}"
									id="contaBancariaMovimentacaoAutomaticaRecebiveisConciliacaoCredito"
									styleClass="form">
								<f:selectItems value="#{ConfiguracaoFinanceiroControle.listaComboConta}"/>
								<a4j:support
										action="#{ConfiguracaoFinanceiroControle.selecionarContaMovimentacaoAutomaticaCredito}"
										reRender="form" event="onchange"/>
							</h:selectOneMenu>
						</h:panelGrid>
					</h:panelGrid>
				</c:if>
				<h:panelGrid id="panelGeralLancAuto" columnClasses="colunaDireita, colunaEsquerda"
							 style="margin-left: -2.2%;;"
							 rowClasses="linhaPar, linhaImpar" columns="2" width="100%">
					<h:selectBooleanCheckbox id="criarContaPagarAutomatico"
											 value="#{ConfiguracaoFinanceiroControle.confFinanceiro.criarContaPagarAutomatico}"
											 styleClass="tooltipster"
											 title="<b>Disponível somente para Conciliação Stone e Cielo</b></br></br>Marque esta opção caso deseje que seja criado uma conta a pagar automaticamente quando a conciliação da Stone informar um cancelamento.">
						<a4j:support event="onchange" reRender="form"/>
					</h:selectBooleanCheckbox>
					<h:outputText styleClass="tituloCampos tooltipster"
								  value="Criar conta a pagar automaticamente (Stone, Cielo)"
								  title="<b>Disponível somente para Conciliação Stone e Cielo</b></br></br>Marque esta opção caso deseje que seja criado uma conta a pagar automaticamente quando a conciliação da Stone informar um cancelamento."/>
				</h:panelGrid>
				<c:if test="${ConfiguracaoFinanceiroControle.confFinanceiro.criarContaPagarAutomatico}">
					<h:panelGrid id="tableContaAPagarAutomaticamente" style="border: ridge; border-color: #ffffff; width: 100%;">
						<h:panelGroup id="panelGroupContaPagarAut">
						<h:panelGrid id="panelCriarContaPagarAutomatica" columnClasses="colunaDireita, colunaEsquerda"
									 rowClasses="linhaPar, linhaImpar" columns="2" width="100%">

							<h:outputText styleClass="tituloCampos tooltipster"
										  style="margin-left: -132px;"
										  value="Conta que será vinculado a conta que foi criada"/>
							<h:selectOneMenu id="contaCriarContaPagarAutomatico"
											 styleClass="form tooltipster"
											 title="Conta que será vinculado a conta que foi criada"
											 value="#{ConfiguracaoFinanceiroControle.confFinanceiro.contaCriarContaPagarAutomaticoVO.codigo}">
								<f:selectItems value="#{ConfiguracaoFinanceiroControle.contas}"/>
							</h:selectOneMenu>

							<h:outputText styleClass="tituloCampos tooltipster"
										  value="Plano de contas (Saída)"/>
							<h:panelGroup>
								<table cellpadding="0" cellspacing="0">

									<tr valign="top">
										<td>
											<h:inputText id="nomePlanoDefaultLancAutoSaida" size="50" maxlength="50"
														 onfocus="focusinput(this);" styleClass="form"
														 value="#{ConfiguracaoFinanceiroControle.nomePlanoDefaultLancAutoSaida}">
												<a4j:support event="onchange"
															 action="#{ConfiguracaoFinanceiroControle.setarPlanoContasLancamentoAutoSaidaVazio}"/>
											</h:inputText>

											<rich:suggestionbox height="200" width="400"
																for="nomePlanoDefaultLancAutoSaida"
																status="statusInComponent"
																immediate="true"
																suggestionAction="#{ConfiguracaoFinanceiroControle.executarAutocompletePesqPlanoContas}"
																minChars="1" rowClasses="linhaImpar, linhaPar"
																var="result"
																nothingLabel="Nenhum Plano de contas encontrada com este nome"
																id="suggestionNomePlanoDefaultLancAutoSaida">
												<a4j:support event="onselect" reRender="panelGeralLancAuto"
															 action="#{ConfiguracaoFinanceiroControle.selecionarPlanoContasSaida}">
												</a4j:support>
												<h:column>
													<f:facet name="header">
														<h:outputText value="Nome" styleClass="textverysmall"/>
													</f:facet>
													<h:outputText styleClass="textverysmall"
																  value="#{result.descricaoCurta}"/>
												</h:column>
												<h:column>
													<f:facet name="header">
														<h:outputText value="Tipo" styleClass="textverysmall"/>
													</f:facet>
													<h:outputText styleClass="textverysmall"
																  value="#{result.tipoPadrao.descricao}"/>
												</h:column>
											</rich:suggestionbox></td>
										<td>
									</tr>
								</table>
							</h:panelGroup>

							<h:outputText styleClass="tituloCampos tooltipster"
										  value="Centro de custo (Saída)"/>
							<h:panelGroup>
								<table cellpadding="0" cellspacing="0">

									<tr valign="top">
										<td>
											<h:inputText id="nomeCentroCustoDefaultLancAutoSaida" size="50"
														 maxlength="50"
														 onfocus="focusinput(this);" styleClass="form"
														 value="#{ConfiguracaoFinanceiroControle.nomeCentroCustoDefaultLancAutoSaida}">
												<a4j:support event="onchange"
															 action="#{ConfiguracaoFinanceiroControle.setarCentroCustoLancamentoAutoSaidaVazio}"/>
											</h:inputText>

											<rich:suggestionbox height="200" width="400"
																for="nomeCentroCustoDefaultLancAutoSaida"
																status="statusInComponent"
																immediate="true"
																suggestionAction="#{ConfiguracaoFinanceiroControle.executarAutocompletePesqCentrosCusto}"
																minChars="1" rowClasses="linhaImpar, linhaPar"
																var="result"
																nothingLabel="Nenhum Centro de custo encontrada com este nome"
																id="suggestionNomeCentroCustoDefaultLancAutoSaida">
												<a4j:support event="onselect" reRender="panelGeralLancAuto"
															 action="#{ConfiguracaoFinanceiroControle.selecionarCentroCustoSaida}">
												</a4j:support>
												<h:column>
													<f:facet name="header">
														<h:outputText value="Nome" styleClass="textverysmall"/>
													</f:facet>
													<h:outputText styleClass="textverysmall"
																  value="#{result.descricaoCurta}"/>
												</h:column>
											</rich:suggestionbox>
										</td>
									</tr>
								</table>
							</h:panelGroup>


							<h:outputText styleClass="tituloCampos tooltipster"
										  value="Plano de contas (Entrada)"/>
							<h:panelGroup>
								<table cellpadding="0" cellspacing="0">

									<tr valign="top">
										<td>
											<h:inputText id="nomePlanoDefaultLancAutoEntrada" size="50" maxlength="50"
														 onfocus="focusinput(this);" styleClass="form"
														 value="#{ConfiguracaoFinanceiroControle.nomePlanoDefaultLancAutoEntrada}">
												<a4j:support event="onchange"
															 action="#{ConfiguracaoFinanceiroControle.setarPlanoContasLancamentoAutoEntradaVazio}"/>
											</h:inputText>

											<rich:suggestionbox height="200" width="400"
																for="nomePlanoDefaultLancAutoEntrada"
																status="statusInComponent"
																immediate="true"
																suggestionAction="#{ConfiguracaoFinanceiroControle.executarAutocompletePesqPlanoContasEntrada}"
																minChars="1" rowClasses="linhaImpar, linhaPar"
																var="result"
																nothingLabel="Nenhum Plano de contas encontrada com este nome"
																id="suggestionNomePlanoDefaultLancAutoEntrada">
												<a4j:support event="onselect" reRender="panelGeralLancAuto"
															 action="#{ConfiguracaoFinanceiroControle.selecionarPlanoContasEntrada}">
												</a4j:support>
												<h:column>
													<f:facet name="header">
														<h:outputText value="Nome" styleClass="textverysmall"/>
													</f:facet>
													<h:outputText styleClass="textverysmall"
																  value="#{result.descricaoCurta}"/>
												</h:column>
												<h:column>
													<f:facet name="header">
														<h:outputText value="Tipo" styleClass="textverysmall"/>
													</f:facet>
													<h:outputText styleClass="textverysmall"
																  value="#{result.tipoPadrao.descricao}"/>
												</h:column>
											</rich:suggestionbox></td>
										<td>
									</tr>
								</table>
							</h:panelGroup>

							<h:outputText styleClass="tituloCampos tooltipster"
										  value="Centro de custo (Entrada)"/>
							<h:panelGroup>
								<table cellpadding="0" cellspacing="0">

									<tr valign="top">
										<td>
											<h:inputText id="nomeCentroCustoDefaultLancAutoEntrada" size="50"
														 maxlength="50"
														 onfocus="focusinput(this);" styleClass="form"
														 value="#{ConfiguracaoFinanceiroControle.nomeCentroCustoDefaultLancAutoEntrada}">
												<a4j:support event="onchange"
															 action="#{ConfiguracaoFinanceiroControle.setarCentroCustoLancamentoAutoEntradaVazio}"/>
											</h:inputText>

											<rich:suggestionbox height="200" width="400"
																for="nomeCentroCustoDefaultLancAutoEntrada"
																status="statusInComponent"
																immediate="true"
																suggestionAction="#{ConfiguracaoFinanceiroControle.executarAutocompletePesqCentrosCusto}"
																minChars="1" rowClasses="linhaImpar, linhaPar"
																var="result"
																nothingLabel="Nenhum Centro de custo encontrada com este nome"
																id="suggestionNomeCentroCustoDefaultLancAutoEntrada">
												<a4j:support event="onselect" reRender="panelGeralLancAuto"
															 action="#{ConfiguracaoFinanceiroControle.selecionarCentroCustoEntrada}">
												</a4j:support>
												<h:column>
													<f:facet name="header">
														<h:outputText value="Nome" styleClass="textverysmall"/>
													</f:facet>
													<h:outputText styleClass="textverysmall"
																  value="#{result.descricaoCurta}"/>
												</h:column>
											</rich:suggestionbox>
										</td>
									</tr>
								</table>
							</h:panelGroup>
						</h:panelGrid>
						</h:panelGroup>
					</h:panelGrid>
				</c:if>
			</rich:tab>
		</rich:tabPanel>

            <h:panelGrid columns="1" width="100%" styleClass="tabMensagens">
                <!-- tabela de mensagens -->
                <h:panelGrid columns="3" width="100%" styleClass="tabMensagens">
                    <h:panelGrid columns="1" width="100%">
                        <f:verbatim>
                            <h:outputText value=" " />
                        </f:verbatim>
                    </h:panelGrid>
                    <h:commandButton rendered="#{ConfiguracaoFinanceiroControle.sucesso}"
                                     image="/imagens/sucesso.png" />
                    <h:commandButton rendered="#{ConfiguracaoFinanceiroControle.erro}"
                                     image="/imagens/erro.png" />
                    <h:panelGrid columns="1" width="100%">
                        <h:outputText styleClass="mensagem"
                                      value="#{ConfiguracaoFinanceiroControle.mensagem}" />
                        <h:outputText styleClass="mensagemDetalhada"
                                      value="#{ConfiguracaoFinanceiroControle.mensagemDetalhada}" />
                    </h:panelGrid>
                </h:panelGrid>
                <!-- Botões -->
				<h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
					<h:panelGroup>
						<h:commandButton id="novo"
										 action="#{ConfiguracaoFinanceiroControle.gravar}"
										 value="Gravar"
										 styleClass="botoes nvoBt btSec"
										 style="padding-bottom: 5px;margin-top: -5px; "
										 accesskey="1" />

						<a4j:commandLink id="logConfFinanceiro"
										 action="#{ConfiguracaoFinanceiroControle.realizarConsultaLog}"
										 style="padding-bottom: 8px; padding-right: 15px;"
										 oncomplete="#{ConfiguracaoFinanceiroControle.oncompleteLog}"
										 title="Visualizar Log"
										 styleClass="botoes nvoBt btSec">
							<i class="fa-icon-list"/>
						</a4j:commandLink>
					</h:panelGroup>
				</h:panelGrid>
            </h:panelGrid>
            <!-- fim da Listagem -->
            </body>
            </html>
        </h:form>
    </h:panelGrid>
    
        <%@include file="includes/include_modalSelecaoPlanoConta.jsp" %>
    <%@include file="includes/include_modalSelecaoCentroCusto.jsp" %>

    <rich:modalPanel id="modalProcessoRealizado" autosized="true"
                     shadowOpacity="true" width="500" height="150">

        <f:facet name="header">
            <h:panelGroup>
                <h:outputText  value="Atualização de taxas administrativas do cartão"></h:outputText>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:graphicImage value="/imagens/close.png" style="cursor:pointer"
                                id="hideListaClientes" />
                <rich:componentControl for="modalProcessoRealizado"
                                       attachTo="hideListaClientes" operation="hide"
                                       event="onclick" />
            </h:panelGroup>
        </f:facet>
        <a4j:form id="formProcessoRealizado" ajaxSubmit="true">
            <h:outputText escape="false"
                          styleClass="tituloCamposMenor"
                          value="#{ConfiguracaoFinanceiroControle.descricaoProcessoRealizado}"></h:outputText>
            <br/>&nbsp;
            <center>
                <a4j:commandButton oncomplete="Richfaces.hideModalPanel('modalProcessoRealizado');"
                                   image="/imagens/OK_Modal.png">
                </a4j:commandButton>
            </center>
        </a4j:form>
    </rich:modalPanel>

    <SCRIPT>
        carregarTooltipsterConfiguracao();
    </SCRIPT>
</f:view>

<%@page pageEncoding="ISO-8859-1" %>
<%@include file="includes/imports.jsp" %>
<style>
    .subordinado {
        padding: 5px !important;
        background-color: #FFFFFF !important;
        border-bottom: 2px solid #C0C0C0;
    }
    .linhaImPar {
        background-color: #FFFFFF !important;
    }
    .textoEsquerda {
        text-align: left !important;
    }
</style>
<h:panelGroup
        rendered="#{GestaoRecebiveisControle.formaPagamentoSelecionada.codigo > 0 and GestaoRecebiveisControle.formaPagamentoSelecionada.tipoFormaPagamento == 'CA'}">

    <a4j:commandButton actionListener="#{EdicaoPagamentoControle.prepareReciboGestaoRecebiveis}"
                       id="ideditarrecibocar" style="display: none;"
                       oncomplete="#{EdicaoPagamentoControle.msgAlert}#{EdicaoPagamentoControle.mensagemNotificar}">
        <f:attribute name="codigoCartao" value="#{GestaoRecebiveisControle.cartaoSelecionado}"/>
        <f:attribute name="tipoEdicao" value="CA"/>
    </a4j:commandButton>
    <a4j:commandButton id="idhistoricocartao" style="display: none;" reRender="modalHistoricoCartao"
                       actionListener="#{GestaoRecebiveisControle.selecionarCartaoCreditoHistorico}"
                       oncomplete="Richfaces.showModalPanel('modalHistoricoCartao')">
        <f:attribute name="codigoCartao" value="#{GestaoRecebiveisControle.cartaoSelecionado}"/>

    </a4j:commandButton>


    <h:panelGroup layout="block" styleClass="painelGR cima" id="paineltotaiscartao1" style="min-width: 980px">
        <a4j:commandLink styleClass="texto-cor-azul linkPadrao tooltipster texto-size-14 inlineBlock"
                         id="btnExportXLSCartao"
                         style="float: left; margin: 1em;"
                         title="Exportar Excel"
                         actionListener="#{GestaoRecebiveisControle.prepararExportarRecebiceis}"
                         oncomplete="abrirPopup('../../UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/vnd.ms-excel','Transacoes', 800,200);#{ExportadorListaControle.msgAlert}">
            <f:attribute name="lista" value="#{GestaoRecebiveisControle.listaCartoes}"/>
            <f:attribute name="tipo" value="xls"/>
            <f:attribute name="prefixo" value="RecebiveisCartao"/>
            <f:attribute name="atributos"
                         value="empresa.nome=Empresa,matricula=Matrícula,cpfPagador=#{GestaoRecebiveisControle.displayIdentificadorFront[0]},nomePessoa=Nome Pagador,nomeAlunosDaParcela=Nome do(s) Aluno(s),
                         operadora=Operadora Cartão,dataLancamento=Data Lançamento,dataCompensacao=Data Compensação,
                         valor=Valor,documentoIntegracaoSesi=Documento,autorizacao=Autorização,nsu=NSU,numeroLote=Lote,contaContido=Conta,usuarioResponsavelApresentar=Resp. Recebimento,reciboPagamentoApresentar=Recibo,contratoReciboApresentar=Contrato"/>
            <i class="fa-icon-file-excel-o"></i>
        </a4j:commandLink>
        <div class="inlineBlock" style="float: left;">
            <span class="cinza texto-size-14">
                Ordenar por:
            </span>
            <span class="cinza negrito texto-size-14">
                <h:selectOneMenu id="ordenaColunaPorCartao" onblur="blurinput(this);"
                                 onfocus="focusinput(this);" styleClass="cinza texto-size-14"
                                 value="#{GestaoRecebiveisControle.ordenaColunaPor}">
                    <f:selectItem itemValue="" itemLabel="" itemDescription=""/>
                    <f:selectItem itemValue="matricula" itemLabel="Matrícula" itemDescription="Matrícula"/>
                    <f:selectItem itemValue="nomePagador" itemLabel="Nome Pagador" itemDescription="Nome Pagador"/>
                    <f:selectItem itemValue="cpfPagador" itemLabel="CPF" itemDescription="CPF"/>
                    <f:selectItem itemValue="operadora_Ordenar" itemLabel="Operadora Cartão" itemDescription="Operadora Cartão"/>
                    <f:selectItem itemValue="dataLancamento" itemLabel="Dt. Lançamento" itemDescription="Dt. Lançamento"/>
                    <f:selectItem itemValue="dataCompensacao" itemLabel="Dt. Compensação" itemDescription="Dt. Compensação"/>
                    <f:selectItem itemValue="valor" itemLabel="Valor" itemDescription="Valor"/>
                    <f:selectItem itemValue="autorizacao" itemLabel="Autorização" itemDescription="Autorização"/>
                    <f:selectItem itemValue="numeroLote" itemLabel="Lote" itemDescription="Lote"/>
                    <f:selectItem itemValue="contaContido" itemLabel="Conta" itemDescription="Conta"/>
                    <a4j:support event="onchange" action="#{GestaoRecebiveisControle.ordenaColunaDataTable}"
                                 reRender="paineltotaiscartao1, listaCartoes"/>
                </h:selectOneMenu>
            </span>
            <span class="cinza negrito texto-size-14">
                <h:selectOneMenu id="ordemDaColunaCartao" onblur="blurinput(this);"
                                 onfocus="focusinput(this);" styleClass="cinza texto-size-14"
                                 value="#{GestaoRecebiveisControle.ordemDaColuna}">
                    <f:selectItem itemValue="" itemLabel="" itemDescription=""/>
                    <f:selectItem itemValue="asc" itemLabel="Crescente" itemDescription="Crescente"/>
                    <f:selectItem itemValue="desc" itemLabel="Decrescente" itemDescription="Decrescente"/>
                    <a4j:support event="onchange" action="#{GestaoRecebiveisControle.ordenaColunaDataTable}"
                                 reRender="paineltotaiscartao1, listaCartoes"/>
                </h:selectOneMenu>
            </span>
        </div>
        <div class="inlineBlock">
            <span class="cinza texto-size-14">
                Total selecionados =
            </span>
            <span class="cinza negrito totalCartaoSelecionados texto-size-14">
                <h:outputText value="#{GestaoRecebiveisControle.qtdeTotalCartoes}"/>
            </span>
            <span class="cinza texto-size-14">
                Valor total=
            </span>
            <span class="cinza negrito valorCartaoSelecionados texto-size-14" style="margin-right: 10px;">
                ${GestaoRecebiveisControle.empresaLogado.moeda} <h:outputText value="#{GestaoRecebiveisControle.totalCartoes}">
                    <f:converter converterId="FormatadorNumerico"/>
                </h:outputText>
            </span>
        </div>

        <a4j:commandLink style="margin-left:10px;" id="iddatacompensacao2"
                         oncomplete="#{GestaoRecebiveisControle.msgAlert}"
                         action="#{GestaoRecebiveisControle.abrirModalAlteracaoCompensacaoCredito}"
                         reRender="modalAlterarCompensacao"
                         title="Modificar a data de compensação dos cartões selecionados"
                         styleClass="pure-button pure-button-small tooltipster texto-size-14 inlineBlock"
                         rendered="#{GestaoRecebiveisControle.empresasSelecionadas.size() <= 1}">
            <i class="fa-icon-calendar"></i>&nbsp Data de compensação
        </a4j:commandLink>

        <a4j:commandLink id="retirarCartoesLotes2" style="margin-left:10px;"
                         action="#{GestaoRecebiveisControle.abrirRetiradaLotes}"
                         reRender="formRetirarChequeLote"
                         oncomplete="#{GestaoRecebiveisControle.msgAlert}"
                         rendered="#{ConfiguracaoFinanceiroControle.confFinanceiro.usarMovimentacaoContas && GestaoRecebiveisControle.exibeRetirarCartaoLotes && GestaoRecebiveisControle.empresasSelecionadas.size() <= 1}"
                         styleClass="inlineBlock pure-button pure-button-small texto-size-14">
            <i class="fa-icon-trash"></i>&nbsp Retirar dos lotes
        </a4j:commandLink>

        <a4j:commandLink oncomplete="#{GestaoRecebiveisControle.onCompleteDepositar}"
                         action="#{GestaoRecebiveisControle.salvaEscolha}"
                         reRender="panelDeposito" style="margin: 0px 10px;"
                         rendered="#{ConfiguracaoFinanceiroControle.confFinanceiro.usarMovimentacaoContas && GestaoRecebiveisControle.empresasSelecionadas.size() <= 1}"
                         styleClass="inlineBlock pure-button pure-button-small pure-button-primary texto-size-14">
            <i class="fa-icon-usd"></i>&nbsp Movimentar
        </a4j:commandLink>
    </h:panelGroup>

    <rich:dataTable id="listaCartoes" width="100%" styleClass="tabelaDados tudo step3" style="border-color:#FFF; margin-top: 16px"
                    columnClasses="colunaEsquerda, colunaEsquerda, colunaEsquerda, centralizado, centralizado,
                                                                    colunaDireita, centralizado, centralizado,colunaEsquerda, colunaDireita"
                    headerClass="subordinado" rowClasses="linhaImpar, linhaPar"
                    value="#{GestaoRecebiveisControle.listaCartoes}" var="cart">

        <rich:column style="border-color:#FFF" id="empresaListaCartoes" styleClass="textoEsquerda" rendered="#{GestaoRecebiveisControle.empresasSelecionadas.size() > 1}">
            <f:facet name="header">
                <h:outputText style="font-weight: bold; font-size:9px;"
                              value="Empresa"/>
            </f:facet>
            <h:outputText value="#{cart.empresa.nome}" style="font-weight: bold; font-size:9px;" styleClass="blue"/>
        </rich:column>

        <rich:column style="border-color:#FFF" id="matriculaCC">
            <f:facet name="header">
                <h:outputText style="font-weight: bold; font-size:9px;"
                              value="#{msg_aplic.prt_RelatorioCliente_Matricula}"/>
            </f:facet>

            <a4j:commandLink action="#{GestaoRecebiveisControle.irParaTelaClienteCc}"
                             style="font-weight: bold; font-size:9px;" styleClass="blue"
                             value="#{cart.matricula}"
                             rendered="#{!cart.consumidor}"
                             oncomplete="abrirPopup('../../clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);"/>

            <h:outputText style="font-weight: bold; font-size:9px;" styleClass="blue"
                          value="" rendered="#{cart.consumidor}"/>
        </rich:column>

        <rich:column style="border-color:#FFF" id="nomePagador">
            <f:facet name="header">
                <h:outputText style="font-weight: bold; font-size:9px;"
                              value="Nome Pagador"/>
            </f:facet>
            <a4j:commandLink action="#{GestaoRecebiveisControle.irParaTelaClienteCc}"
                             style="font-weight: bold; font-size:9px;" styleClass="blue"
                             value="#{cart.nomePagador}"
                             rendered="#{!cart.fornecedor && !cart.exibirInfoPessoaContratoDiferenteDoPagador}"
                             oncomplete="abrirPopup('../../clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);"/>
            <a4j:commandLink action="#{GestaoRecebiveisControle.irParaTelaClienteCc}"
                             style="font-weight: bold; font-size:9px;"
                             styleClass="blue tooltipster"
                             title="#{cart.titleInfoPessoaContratoDiferenteDoPagador}"
                             value="#{cart.nomePagador}"
                             rendered="#{!cart.fornecedor && cart.exibirInfoPessoaContratoDiferenteDoPagador}"
                             oncomplete="abrirPopup('../../clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);"/>

            <h:outputText style="font-weight: bold; font-size:9px;" styleClass="blue"
                          value="#{cart.nomePagador}"
                          rendered="#{cart.fornecedor}"/>

        </rich:column>

        <rich:column style="border-color:#FFF" id="cpfCC" styleClass="textoEsquerda">
            <f:facet name="header">
                <h:outputText style="font-weight: bold; font-size:9px;"
                              value="#{GestaoRecebiveisControle.displayIdentificadorFront[0]}"/>
            </f:facet>
            <h:outputText value="#{cart.cpfPagador}" style="font-weight: bold; font-size:9px;" styleClass="blue"/>

        </rich:column>

        <rich:column style="border-color:#FFF" id="operadora" styleClass="textoEsquerda" >
            <f:facet name="header">
                <h:outputText style="font-weight: bold; font-size:9px;"
                              value="#{msg_aplic.prt_OperadoraCartao_tituloForm}"/>
            </f:facet>
            <h:outputText style="font-weight: bold; font-size:9px;"
                          styleClass="blue tooltipster" value="#{cart.operadora}" title="Adquirente: #{empty cart.adquirente ? ' Não informada' : cart.adquirente}"/>
        </rich:column>

        <rich:column style="border-color:#FFF" id="dataLancamento" styleClass="textoEsquerda">
            <f:facet name="header">
                <h:outputText style="font-weight: bold; font-size:9px;"
                              value="#{msg_aplic.prt_Finan_GestaoRecebiveis_dataLancamento}"/>
            </f:facet>
            <h:outputText style="font-weight: bold; font-size:9px;"
                          styleClass="blue"
                          value="#{cart.dataLancamento}">
                <f:convertDateTime pattern="dd/MM/yyyy"/>
            </h:outputText>
        </rich:column>

        <rich:column style="border-color:#FFF" id="dataCompensacao" styleClass="textoEsquerda">
            <f:facet name="header">
                <h:outputText style="font-weight: bold; font-size:9px;"
                              value="#{GestaoRecebiveisControle.considerarDataCompensacaoOriginal ? 'Compensação Original' : msg_aplic.prt_Finan_GestaoRecebiveis_dataCompensacao}"/>
            </f:facet>
            <div style="display: inline-flex;">
            <a4j:outputPanel rendered="#{!GestaoRecebiveisControle.considerarDataCompensacaoOriginal && !cart.antecipacao}"
                             title="Data de compensação original: #{cart.dataOriginalApresentar}"
                             styleClass="blue tooltipster">
                <h:outputText style="font-weight: bold; font-size:9px;"
                              styleClass="blue"
                              value="#{cart.dataCompensacao}">
                    <f:convertDateTime pattern="dd/MM/yyyy"/>
                </h:outputText>
            </a4j:outputPanel>
            <a4j:outputPanel rendered="#{!GestaoRecebiveisControle.considerarDataCompensacaoOriginal && cart.antecipacao}"
                             title="Data de compensação original: #{cart.dataPgtoOriginalAntesDaAntecipacaoApresentar}"
                             styleClass="blue tooltipster">
                <h:outputText style="font-weight: bold; font-size:9px;"
                              styleClass="blue"
                              value="#{cart.dataCompensacao}">
                    <f:convertDateTime pattern="dd/MM/yyyy"/>
                </h:outputText>
            </a4j:outputPanel>
            </div>

            <a4j:outputPanel rendered="#{GestaoRecebiveisControle.considerarDataCompensacaoOriginal}"
                             title="Data de compensação efetiva: #{cart.dataCompensacaoApresentar}">
                <h:outputText style="font-weight: bold; font-size:9px;"
                              styleClass="blue"
                              value="#{cart.dataOriginalApresentar}">
                </h:outputText>
            </a4j:outputPanel>
            <h:graphicImage value="/imagens/antecipacao.svg"
                            rendered="#{cart.antecipacao && !cart.alterouDataRecebimentoZWAutomaticamente}"
                            style="width:11px; height:11px; margin-left: 6px; margin-top: -2px"
                            styleClass="tooltipster"
                            title="Este recebimento foi antecipado na adquirente e teve sua data de compensação alterada automaticamente.</br>
                                                    Data de compensação original: #{cart.dataPgtoOriginalAntesDaAntecipacaoApresentar}">
            </h:graphicImage>
            <h:graphicImage value="/imagens/calendar-check-black.svg"
                            rendered="#{!cart.antecipacao && cart.alterouDataRecebimentoZWAutomaticamente}"
                            style="width:11px; height:11px; margin-left: 6px; margin-top: -2px"
                            styleClass="tooltipster"
                            title="Este recebimento teve sua data de compensação alterada automaticamente pois estava diferente da que veio no extrato.</br>
                                                        Data de compensação original Sistema Pacto: #{cart.dataPgtoOriginalZWAntesDaAlteracaoAutomaticaApresentar}">
            </h:graphicImage>
        </rich:column>

        <rich:column style="border-color:#FFF" id="valor" styleClass="textoEsquerda">
            <f:facet name="header">
                <h:outputText style="font-weight: bold; font-size:9px;"
                              value="#{msg_aplic.prt_Cheque_valor}"/>
            </f:facet>
            <h:outputText style="font-weight: bold; font-size:9px;"
                          styleClass="blue" value="#{cart.valor}">
                <f:converter converterId="FormatadorNumerico"/>
            </h:outputText>
        </rich:column>


        <rich:column style="border-color:#FFF" id="autorizacao" styleClass="textoEsquerda">
            <f:facet name="header">
                <h:outputText style="font-weight: bold; font-size:9px;"
                              value="#{msg_aplic.prt_Finan_GestaoRecebiveis_Autorizacao}"/>
            </f:facet>
            <h:outputText style="font-weight: bold; font-size:9px; text-transform: none;"
                          styleClass="blue" value="#{cart.autorizacao}">
            </h:outputText>
        </rich:column>

        <rich:column style="border-color:#FFF" id="nsu" rendered="#{GestaoRecebiveisControle.exibirNSU}" styleClass="textoEsquerda">
            <f:facet name="header">
                <h:outputText style="font-weight: bold; font-size:9px;"
                              value="#{msg_aplic.prt_Finan_GestaoRecebiveis_NSU}"/>
            </f:facet>
            <h:outputText style="font-weight: bold; font-size:9px;"
                          styleClass="blue" value="#{cart.nsu}"/>
        </rich:column>

        <rich:column style="border-color:#FFF" id="numeroLote" styleClass="textoEsquerda">
            <f:facet name="header">
                <h:outputText style="font-weight: bold; font-size:9px;"
                              value="#{msg_aplic.prt_Finan_GestaoRecebiveis_Lote}"/>
            </f:facet>
            <a4j:commandLink action="#{GestaoLotesControle.prepareLoteCc}"
                             style="font-weight: bold; font-size:9px;"
                             styleClass="blue" value="#{cart.numeroLote}"
                             oncomplete="#{GestaoLotesControle.msgAlert}"
                             rendered="#{cart.apresentarNumeroLote}"/>
        </rich:column>

        <rich:column style="border-color:#FFF" id="contaContidoCartao" rendered="#{ConfiguracaoFinanceiroControle.confFinanceiro.usarMovimentacaoContas}" styleClass="textoEsquerda">
            <f:facet name="header">
                <h:outputText style="font-weight: bold; font-size:9px;" value="Conta"/>
            </f:facet>
            <h:outputText style="font-weight: bold; font-size:9px;"
                          styleClass="blue" value="#{cart.contaContido}">
            </h:outputText>
        </rich:column>

        <rich:column style="border-color:#FFF" headerClass="colunaDireita" rendered="#{GestaoRecebiveisControle.empresasSelecionadas.size() <= 1}">
            <f:facet name="header">
                <h:panelGroup layout="block" styleClass="chk-fa-container inline">
                    <h:selectBooleanCheckbox value="#{GestaoRecebiveisControle.todosCartoesMarcados}"
                                             title="Adicionar todos os cartoes ao Lote">

                        <a4j:support event="onclick"
                                     action="#{GestaoRecebiveisControle.marcaDesmarcaCartoes}"
                                     reRender="panelMensagem, totalSelecionado2, listaCartoes, qtdeSelecionado2,botoesCartoes, paineltotaiscartao1, paineltotaiscartao2"/>
                    </h:selectBooleanCheckbox>
                    <span style="margin-top: 2px;"></span>
                </h:panelGroup>
            </f:facet>

            <h:panelGroup layout="block" styleClass="chk-fa-container inline" style="margin-right: 5px;">
                <h:selectBooleanCheckbox id="cartaoEscolhido" value="#{cart.cartaoEscolhido}"
                                         rendered="#{(ConfiguracaoFinanceiroControle.confFinanceiro.usarMovimentacaoContas || cart.numeroLote == 0)
                                                                                                 && cart.ativo}">
                    <a4j:support event="onclick"
                                 focus="cartaoEscolhido"
                                 action="#{GestaoRecebiveisControle.calcularTotalCartoes}"
                                 reRender="panelMensagem,totalSelecionado2,qtdeSelecionado2,botoesCartoes,paineltotaiscartao1, paineltotaiscartao2"/>
                </h:selectBooleanCheckbox>
                <span style="margin-top: 2px;"></span>
            </h:panelGroup>

            <h:outputText rendered="#{cart.movConta > 0}" value="(AVULSO)"
                          style="font-weight: bold; font-size:9px;"
                          styleClass="blue inline"/>
            <h:outputText rendered="#{!cart.ativo}" value="(CANCELADO)"
                          style="font-weight: bold; font-size:9px;"
                          styleClass="blue inline"/>

            <a4j:commandLink rendered="#{cart.movConta == 0}" title="Editar Código de Autorização"
                               reRender="panelAutorizacaoFuncionalidade"
                               actionListener="#{EdicaoPagamentoControle.prepareRecibo2}" styleClass="tooltipster inline"
                               oncomplete="#{EdicaoPagamentoControle.mensagemNotificar}">
                <i class="fa-icon-edit"></i>
                <f:attribute name="cartaoTO" value="#{cart}"/>
                <f:attribute name="tipoEdicao" value="CA"/>
            </a4j:commandLink>

            <a4j:commandLink title="Historico do Cartão" reRender="modalHistoricoCartao" styleClass="inline tooltipster"
                               actionListener="#{GestaoRecebiveisControle.selecionarCartaoCreditoHistorico}"
                               oncomplete="Richfaces.showModalPanel('modalHistoricoCartao')">
                <i class="fa-icon-hourglass"></i>
                <f:attribute name="cart" value="#{cart}"/>
            </a4j:commandLink>

            <h:panelGroup rendered="#{not ConfiguracaoFinanceiroControle.confFinanceiro.usarMovimentacaoContas and cart.numeroLote > 0}">
                <i class="fa-icon-edit tooltipster texto-cor-azul linkPadrao inline"
                   title="Este cartão já está depositado."></i>

            </h:panelGroup>

            <h:panelGroup rendered="#{not empty cart.movPagamentoVO and not empty cart.movPagamentoVO.mensagemDescricaoCentroCustos}">
                <h:outputText styleClass="fa-icon-info-sign tooltipster" title="#{cart.movPagamentoVO.mensagemDescricaoCentroCustos}"></h:outputText>
            </h:panelGroup>
        </rich:column>
    </rich:dataTable>

    <h:panelGroup layout="block" styleClass="painelGR baixo" id="paineltotaiscartao2">
        <a4j:commandLink oncomplete="#{GestaoRecebiveisControle.msgAlert}"
                         styleClass="texto-cor-azul linkPadrao"
                         style="float: left; margin-left: 10px;"
                         action="#{GestaoRecebiveisControle.realizarConsultaLogObjetoSelecionado}"
                         value="Log Alterações Data Compensação"/>

        <div class="inlineBlock">
            <span class="cinza texto-size-14">
                Cartões selecionados =
            </span>
            <span class="cinza texto-size-14 negrito totalCartaoSelecionados">
                <h:outputText value="#{GestaoRecebiveisControle.qtdeTotalCartoes}"/>
            </span>
            <span class="cinza texto-size-14">
                Valor total=
            </span>
            <span class="cinza texto-size-14 negrito valorCartaoSelecionados" style="margin-right: 10px;">
                ${GestaoRecebiveisControle.empresaLogado.moeda} <h:outputText value="#{GestaoRecebiveisControle.totalCartoes}">
                    <f:converter converterId="FormatadorNumerico"/>
                </h:outputText>
            </span>
        </div>


        <a4j:commandLink style="margin-left:10px;"
                         oncomplete="#{GestaoRecebiveisControle.msgAlert}"
                         action="#{GestaoRecebiveisControle.abrirModalAlteracaoCompensacaoCredito}"
                         reRender="modalAlterarCompensacao"
                         title="Modificar a data de compensação dos cartões selecionados"
                         styleClass="pure-button pure-button-small tooltipster texto-size-14 inlineBlock"
                         rendered="#{GestaoRecebiveisControle.empresasSelecionadas.size() <= 1}">
            <i class="fa-icon-calendar"></i>&nbsp Data de compensação
        </a4j:commandLink>

        <a4j:commandLink id="retirarCartoesLotes" style="margin-left:10px;"
                         action="#{GestaoRecebiveisControle.abrirRetiradaLotes}"
                         reRender="formRetirarChequeLote"
                         oncomplete="#{GestaoRecebiveisControle.msgAlert}"
                         rendered="#{ConfiguracaoFinanceiroControle.confFinanceiro.usarMovimentacaoContas && GestaoRecebiveisControle.exibeRetirarCartaoLotes && GestaoRecebiveisControle.empresasSelecionadas.size() <= 1}"
                         styleClass="inlineBlock pure-button pure-button-small texto-size-14">
            <i class="fa-icon-trash"></i>&nbsp Retirar dos lotes
        </a4j:commandLink>

        <a4j:commandLink id="depositarSimples2" style="margin-left:10px;"
                         oncomplete="#{GestaoRecebiveisControle.onCompleteDepositar}"
                         action="#{GestaoRecebiveisControle.salvaEscolha}"
                         reRender="panelDeposito"
                         rendered="#{!ConfiguracaoFinanceiroControle.confFinanceiro.usarMovimentacaoContas && GestaoRecebiveisControle.empresasSelecionadas.size() <= 1}"
                         styleClass="pure-button pure-button-small pure-button-primary texto-size-14 inlineBlock">
            <i class="fa-icon-usd"></i>&nbsp Depositar
        </a4j:commandLink>

        <a4j:commandLink id="depositar2" style="margin:0px 10px;"
                         oncomplete="#{GestaoRecebiveisControle.onCompleteDepositar}"
                         action="#{GestaoRecebiveisControle.salvaEscolha}" reRender="panelDeposito"
                         rendered="#{ConfiguracaoFinanceiroControle.confFinanceiro.usarMovimentacaoContas && GestaoRecebiveisControle.empresasSelecionadas.size() <= 1}"
                         styleClass="pure-button pure-button-small pure-button-primary texto-size-14 inlineBlock">
            <i class="fa-icon-usd"></i>&nbsp Movimentar
        </a4j:commandLink>

    </h:panelGroup>


</h:panelGroup>

<%@include file="../../includes/imports.jsp" %>



<script type="text/javascript">
    $.noConflict();
</script>
<h:form id="form">
<rich:spacer height="25px;"></rich:spacer>

<a href="#"
   class="expandirCentro">
     		   Expandir Tudo
</a>
&nbsp;
<a href="#"
   class="expandirUmCentro">
     		   Expandir Um N�vel
</a>
&nbsp;
<a href="#"
   class="retrairUmCentro">
     		   Retrair Um N�vel
</a>
&nbsp;
<a href="#"
   class="retrairCentro">
     		   Retrair Tudo
</a>

<!-- ---------------------------------------------- INICIO - ARVORE DE DADOS ------------------------------------------------- -->                                                        
<rich:panel id="dados">

    <table width="100%" >
        <tr>
            <td width="5%"><rich:spacer width="10px"></rich:spacer></td>


            <td width="95%">


                <table  border="0"
                        class="centroCustosTable"
                        id="dnd-centroCustosTable"
                        cellspacing="0"
                        width="100%">

                    <tbody>

                        <tr>

                            <td bgcolor="#FFFFFF" class="tituloBold" style="width: 50%;">

                            </td>

                            <td style="width: 50%; color: black;" class="codigoCentro" align="left">
                                C�digo Interno
                            </td>
                        </tr>

                        <c:forEach var="centro" varStatus="indice" items="${CentroCustosControle.listaCentros}">

                            <%-- Definir as cores da tabela zebrada --%>
                            <c:choose>
                                <c:when test="${indice.count % 2 == 0}">
                                    <c:set var="corLinha" value="#FFFFFF" scope="request" />
                                    <c:set var="corLinhaRessaltada" value="#93DB70"
                                           scope="request" />
                                </c:when>
                                <c:otherwise>
                                    <c:set var="corLinha" value="#DFE8EF" scope="request" />
                                    <c:set var="corLinhaRessaltada" value="#93DB70"
                                           scope="request" />
                                </c:otherwise>
                            </c:choose>

                            <c:choose>
                                <c:when test="${fn:indexOf(centro.codigoCentro, '.') > 0}">
                                    <c:set var="noPai" value="${fn:substring(centro.codigoCentro,0, fn:length(centro.codigoCentro) -4)}" scope="request" />

                                    <tr bgcolor="${corLinha}" id="${centro.codigoNode}"
                                        onmouseover="mudar_cor(this,'${corLinhaRessaltada}');"
                                        onmouseout="mudar_cor(this,'${corLinha}');"
                                        class="child-of-${fn:replace(noPai,'.', '') + 0 }">


                                        <td  class="tituloBold">
                                            <c:out value="${centro.descricaoCurta}"></c:out>
                                            <a href="#" name="naoMostrarBotaoJS"
                                               title="Editar Centro de Custos"
                                               class="fa fa-icon-pencil"
                                               onclick="setarCodigoNivelPai('${noPai}', 'form:idCentroPai'); preencherHiddenChamarBotao('form:chamaModalEdicao','form:idEntidade','<c:out value="${centro.codigo}"></c:out>')">
                                            </a>
                                        </td>

                                        <td class="codigoCentro" style="width: 50%;"  align="left">
                                            <c:out value="${centro.codigo}"></c:out>
                                        </td>

                                    </tr>
                                </c:when>
                                <c:otherwise>
                                    <tr bgcolor="${corLinha}"
                                        onmouseover="mudar_cor(this,'${corLinhaRessaltada}');"
                                        onmouseout="mudar_cor(this,'${corLinha}');"
                                        id="${centro.codigoNode}">
                                        <td class="tituloBold">
                                            <c:out value="${centro.descricaoCurta}"></c:out>
                                            <a href="#" name="naoMostrarBotaoJS"
                                               title="Editar Centro de Custos"
                                               class="fa fa-icon-pencil"
                                               onclick="preencherHiddenChamarBotao('form:chamaModalEdicao','form:idEntidade','<c:out value="${centro.codigo}"></c:out>')">
                                            </a>
                                            <span class="previa"></span>

                                        </td>

                                        <td class="codigoCentro" style="width: 50%;"  align="left">
                                            <c:out value="${centro.codigo}"></c:out>
                                        </td>

                                    </tr>
                                </c:otherwise>
                            </c:choose>


                        </c:forEach>

                    </tbody>
                </table>
            </td>
        </tr>
    </table>



    <h:inputHidden id="idEntidade" value="#{CentroCustosControle.codigoCentro}" />
    <h:inputHidden id="idCentroPai" value="#{CentroCustosControle.codigoCentroPaiSelecionado}" />
    <script>
        atualizarTreeViewCentroCustos(''+document.getElementById('form:idCentroPai').value);
    </script>
</rich:panel>
<!-- ---------------------------------------------- FIM - ARVORE DE DADOS ------------------------------------------------- -->
<a4j:commandButton action="#{CentroCustosControle.editar}"
                   id="chamaModalEdicao"
                   reRender="painelEdicaoCentroCustos"
                   style="visibility: hidden;"
                   oncomplete="Richfaces.showModalPanel('painelEdicaoCentroCustos');">
</a4j:commandButton>

</h:form>



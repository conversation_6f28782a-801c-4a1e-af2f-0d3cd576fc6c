<%@page contentType="text/html" pageEncoding="ISO-8859-1"%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@include file="includes/include_imports.jsp" %>
<link href="../../css_pacto.css" rel="stylesheet" type="text/css">
<link href="../../css/otimize.css" rel="stylesheet" type="text/css">
<link href="../../css/jquery.treeTable.css" rel="stylesheet" type="text/css">
<head><script type="text/javascript" language="javascript" src="../../script/script.js"></script></head>

<c:set var="moduloSession" value="1" scope="session" />
<script type="text/javascript">
    jQuery.noConflict();
</script>
<f:view>
    <jsp:include page="../../includes/include_carregando_ripple.jsp"/>
    <html>
        <!-- Inclui o elemento HEAD da página -->
        <head>
            <%@include file="includes/include_head_finan.jsp" %>
            <script type="text/javascript" language="javascript" src="${contextoFinan}/script/telaInicial.js"></script>
        </head>
        <body>
        <h:panelGroup layout="block" style="display: inline-table;width: 100%;" styleClass="fundoCinza">
            <h:form id="formTopo" style="overflow-x: visible !important;">
            <h:panelGroup layout="block" styleClass="bgtop topoZW" rendered="#{MenuControle.apresentarTopo}">
                <jsp:include page="../../include_topo_novo.jsp" flush="true"/>
                <jsp:include page="../../include_menu_fin_flat.jsp" flush="true"/>
            </h:panelGroup>
            </h:form>
            <h:panelGroup layout="block" styleClass="caixaCorpo">
                <h:panelGroup layout="block" style="height: 80%;width: 100%">
                    <h:panelGroup layout="block" styleClass="caixaMenuLatel">
                        <h:panelGroup layout="block" styleClass="container-imagem container-conteudo-central">

                            <h:panelGroup layout="block" styleClass="container-box zw_ui especial ">

                                <h:panelGroup styleClass="container-box-header" layout="block">
                                    <h:panelGroup layout="block" styleClass="margin-box">
                                        <h:outputText styleClass="container-header-titulo"
                                                      value="Rateio Integração "/>
                                        <h:outputLink styleClass="linkWiki"
                                                      value="#{SuperControle.urlBaseConhecimento}como-categorizar-receitas-e-despesas-no-financeiro-rateio-integracao/"
                                                      title="Clique e saiba mais: Rateio Integração"
                                                      target="_blank">
                                            <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                                        </h:outputLink>
                                    </h:panelGroup>
                                </h:panelGroup>

                                <h:panelGroup layout="block" styleClass="margin-box">
                                    <%@include file="../../include_rateioIntegracao.jsp" %>
                                </h:panelGroup>
                            </h:panelGroup>
                        </h:panelGroup>
                        <h:form id="formMenu" style="display: flex;align-items: stretch;flex-direction: row-reverse;">
                            <jsp:include page="includes/include_menu_relatorios.jsp" flush="true"/>
                        </h:form>
                    </h:panelGroup>
                </h:panelGroup>
            </h:panelGroup>

            <jsp:include page="../../include_rodape_flat.jsp" flush="true" />
        </h:panelGroup>
        </body>
    </html>
    <%@include file="includes/include_modal_abrirCaixa.jsp" %>
</f:view>

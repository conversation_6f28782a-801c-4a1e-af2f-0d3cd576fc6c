<%-- INICIO LISTA DE CHEQUE --%>
<%@page pageEncoding="ISO-8859-1" %>
<%@include file="includes/imports.jsp" %>
<h:panelGroup rendered="#{GestaoRecebiveisControle.formaPagamentoSelecionada.codigo > 0 and GestaoRecebiveisControle.formaPagamentoSelecionada.tipoFormaPagamento == 'CH'}">
    <c:if test="${not empty GestaoRecebiveisControle.listaCheques}">

        <a4j:commandButton actionListener="#{EdicaoPagamentoControle.prepareReciboGestaoRecebiveis}"
                           id="ideditarrecibo" style="display: none;"
                           oncomplete="#{EdicaoPagamentoControle.msgAlert}#{EdicaoPagamentoControle.mensagemNotificar}">
            <f:attribute name="codigoCheque" value="#{GestaoRecebiveisControle.chequeSelecionado}" />
            <f:attribute name="tipoEdicao" value="CH"/>
        </a4j:commandButton>
        <a4j:commandButton id="idhistoricocheque"  style="display: none;" reRender="modalHistoricoCheque"
                           actionListener="#{ChequeControle.escolherCheque}"
                           oncomplete="Richfaces.showModalPanel('modalHistoricoCheque')">
            <f:attribute name="codigoCheque" value="#{GestaoRecebiveisControle.chequeSelecionado}" />
        </a4j:commandButton>



        <h:panelGroup layout="block" styleClass="painelGR cima" id="painelchequestotal1">
            <a4j:commandLink styleClass="texto-cor-azul linkPadrao tooltipster texto-size-14 inlineBlock"
                             id="btnExportXLSCheque"
                             style="float: left; margin: 1em;"
                             title="Exportar Excel"
                             actionListener="#{GestaoRecebiveisControle.prepararExportarRecebiceis}"
                             oncomplete="abrirPopup('../../UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/vnd.ms-excel','Transacoes', 800,200);#{ExportadorListaControle.msgAlert}">
                <f:attribute name="lista" value="#{GestaoRecebiveisControle.listaCheques}"/>
                <f:attribute name="tipo" value="xls"/>
                <f:attribute name="prefixo" value="RecebiveisCheque"/>
                <f:attribute name="atributos" value="empresa.nome=Empresa,matricula=Matrícula,cpfPagador=#{GestaoRecebiveisControle.displayIdentificadorFront[0]},nomePagadorUpper=Nome Pagador,
                nomeAlunosDaParcela=Nome do(s) Aluno(s),numeroBanco=Banco,agencia=Agência,conta=Conta,numero=N. Cheque,nomeNoChequeUpper=Nome Terceiro,dataLancamento=Dt.Lançamento,
                dataCompensacao=Dt. Compensação,valorApresentar=Valor,numeroLote=Lote,usuarioResponsavelApresentar=Resp. Recebimento,reciboPagamentoApresentar=Recibo,contratoReciboApresentar=Contrato"/>
                <i class="fa-icon-file-excel-o" ></i>
            </a4j:commandLink>
            <div class="inlineBlock" style="float: left;">
            <span class="cinza texto-size-14">
                Ordenar por:
            </span>
                <span class="cinza negrito texto-size-14">
                <h:selectOneMenu id="ordenaColunaPorCheque" onblur="blurinput(this);"
                                 onfocus="focusinput(this);" styleClass="cinza texto-size-14"
                                 value="#{GestaoRecebiveisControle.ordenaColunaPor}">
                    <f:selectItem itemValue="" itemLabel="" itemDescription=""/>
                    <f:selectItem itemValue="matricula" itemLabel="Matrícula" itemDescription="Matrícula"/>
                    <f:selectItem itemValue="cpfPagador" itemLabel="CPF" itemDescription="CPF"/>
                    <f:selectItem itemValue="nomePagador" itemLabel="Nome Pagador" itemDescription="Nome Pagador"/>
                    <f:selectItem itemValue="nomeNoCheque" itemLabel="Nome Terceiro" itemDescription="Nome Terceiro"/>
                    <f:selectItem itemValue="numeroBanco" itemLabel="Banco" itemDescription="Banco"/>
                    <f:selectItem itemValue="conta" itemLabel="Conta/Agência" itemDescription="Conta/Agência"/>
                    <f:selectItem itemValue="numero" itemLabel="Número" itemDescription="Número"/>
                    <f:selectItem itemValue="dataLancamento" itemLabel="Lançamento" itemDescription="Lançamento"/>
                    <f:selectItem itemValue="dataCompensacao" itemLabel="Compensação" itemDescription="Compensação"/>
                    <f:selectItem itemValue="valor" itemLabel="Valor" itemDescription="Valor"/>
                    <f:selectItem itemValue="contaContido" itemLabel="Conta Financeiro" itemDescription="Conta Financeiro"/>
                    <f:selectItem itemValue="numeroLote" itemLabel="Lote" itemDescription="Lote"/>
                    <a4j:support event="onchange" action="#{GestaoRecebiveisControle.ordenaColunaDataTable}"
                                 reRender="painelchequestotal1, listaCheques"/>
                </h:selectOneMenu>
            </span>
                <span class="cinza negrito texto-size-14">
                <h:selectOneMenu id="ordemDaColunaCheque" onblur="blurinput(this);"
                                 onfocus="focusinput(this);" styleClass="cinza texto-size-14"
                                 value="#{GestaoRecebiveisControle.ordemDaColuna}">
                    <f:selectItem itemValue="" itemLabel="" itemDescription=""/>
                    <f:selectItem itemValue="asc" itemLabel="Crescente" itemDescription="Crescente"/>
                    <f:selectItem itemValue="desc" itemLabel="Decrescente" itemDescription="Decrescente"/>
                    <a4j:support event="onchange" action="#{GestaoRecebiveisControle.ordenaColunaDataTable}"
                                 reRender="painelchequestotal1, listaCheques"/>
                </h:selectOneMenu>
            </span>
            </div>
            <div class="inlineBlock" style="padding-right: 10px;">
                <span class="cinza texto-size-14">
                    Total selecionados =
                </span>
                <span class="cinza texto-size-14 negrito totalChequesSelecionados">
                    <h:outputText value="#{GestaoRecebiveisControle.qtdeTotalCheques}"/>
                </span>
                <span class="cinza texto-size-14">
                    Valor total=
                </span>
                <span class="cinza texto-size-14 negrito valorChequesSelecionados" style="margin-right: 10px;">
                    ${GestaoRecebiveisControle.empresaLogado.moeda} <h:outputText value="#{GestaoRecebiveisControle.totalCheques}">
                        <f:converter converterId="FormatadorNumerico"/>
                    </h:outputText>
                </span>
            </div>

            <a4j:commandLink id="retirarChequesLotes"  style="margin-left:10px;"
                             action="#{GestaoRecebiveisControle.abrirRetiradaLotes}"
                             reRender="formRetirarChequeLote"
                             oncomplete="#{GestaoRecebiveisControle.msgAlert}"
                             rendered="#{ConfiguracaoFinanceiroControle.confFinanceiro.usarMovimentacaoContas  && GestaoRecebiveisControle.exibeRetirarChequeLotes
                                            && GestaoRecebiveisControle.empresasSelecionadas.size() <= 1}"
                             styleClass="inlineBlock pure-button pure-button-small texto-size-14">
                <i class="fa-icon-trash" ></i>&nbsp Retirar dos lotes
            </a4j:commandLink>

            <a4j:commandLink id="loteAvulso" style="margin-left:10px"
                             oncomplete="#{GestaoRecebiveisControle.msgAlert}"
                             action="#{GestaoRecebiveisControle.gerarLoteAvulso}" reRender="formLoteAvulso"
                             rendered="#{ConfiguracaoFinanceiroControle.confFinanceiro.usarMovimentacaoContas && GestaoRecebiveisControle.empresasSelecionadas.size() <= 1}"
                             styleClass="pure-button pure-button-small texto-size-14 inlineBlock">
                Lote Avulso
            </a4j:commandLink>

            <a4j:commandLink id="depositar"  style="margin: 0 10px"
                             oncomplete="#{GestaoRecebiveisControle.onCompleteDepositar}"
                             action="#{GestaoRecebiveisControle.salvaEscolha}"
                             reRender="panelDeposito"
                             rendered="#{ConfiguracaoFinanceiroControle.confFinanceiro.usarMovimentacaoContas && GestaoRecebiveisControle.empresasSelecionadas.size() <= 1}"
                             styleClass="pure-button pure-button-small pure-button-primary texto-size-14 inlineBlock">
                <i class="fa-icon-usd" ></i>&nbsp Movimentar
            </a4j:commandLink>

        </h:panelGroup>

        <rich:dataTable id="listaCheques" width="100%" styleClass="tabelaDados tudo step3" style="border-color:#FFF; margin-top: 16px"
                        columnClasses="colunaEsquerda, colunaEsquerda, centralizado, centralizado, centralizado, centralizado, centralizado,
                                                                                    centralizado, centralizado, colunaDireita, centralizado, centralizado,colunaDireita"
                        headerClass="subordinado" rowClasses="linhaImpar, linhaPar"
                        value="#{GestaoRecebiveisControle.listaCheques}" var="cheq">

            <rich:column style="border-color:#FFF" id="empresaListaCheque" styleClass="textoEsquerda" rendered="#{GestaoRecebiveisControle.empresasSelecionadas.size() > 1}">
                <f:facet name="header">
                    <h:outputText style="font-weight: bold; font-size:9px;"
                                  value="Empresa"/>
                </f:facet>
                <h:outputText value="#{cheq.empresa.nome}" style="font-weight: bold; font-size:9px;" styleClass="blue"/>
            </rich:column>

            <rich:column style="border-color:#FFF" id="matricula">
                <f:facet name="header">
                    <h:outputText style="font-weight: bold; font-size:9px;"
                                  value="#{msg_aplic.prt_RelatorioCliente_Matricula}"/>
                </f:facet>
                <a4j:commandLink action="#{GestaoRecebiveisControle.irParaTelaClienteCh}"
                                 style="font-weight: bold; font-size:9px;" styleClass="blue"
                                 value="#{cheq.matricula}"
                                 rendered="#{!cheq.consumidor}"
                                 oncomplete="abrirPopup('../../clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);"/>

                <h:outputText style="font-weight: bold; font-size:9px;" styleClass="blue"
                              value="" rendered="#{cheq.consumidor}"/>
            </rich:column>


            <rich:column style="border-color:#FFF">
                <f:facet name="header">
                    <h:outputText style="font-weight: bold; font-size:9px;"
                                  value="#{GestaoRecebiveisControle.displayIdentificadorFront[0]}" />
                </f:facet>
                <h:outputText style="font-weight: bold; font-size:9px;"
                              styleClass="blue" value="#{cheq.cpfPagador}" />
            </rich:column>


            <rich:column style="border-color:#FFF" id="nomePagador">
                <f:facet name="header">
                    <h:outputText style="font-weight: bold; font-size:9px;"
                                  value="#{msg_aplic.prt_Finan_GestaoRecebiveis_nome}"/>
                </f:facet>

                <a4j:commandLink action="#{GestaoRecebiveisControle.irParaTelaClienteCh}"
                                 style="font-weight: bold; font-size:9px;" styleClass="blue"
                                 value="#{cheq.nomePagador}"
                                 rendered="#{!cheq.fornecedor}"
                                 oncomplete="abrirPopup('../../clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);"/>


                <h:outputText style="font-weight: bold; font-size:9px;" styleClass="blue"
                              rendered="#{cheq.fornecedor}"
                              value="#{cheq.nomePagador}" />
            </rich:column>

            <rich:column style="border-color:#FFF" id="nomeNoCheque">
                <f:facet name="header">
                    <h:outputText style="font-weight: bold; font-size:9px;"
                                  value="#{msg_aplic.prt_Finan_GestaoRecebiveis_nomeTerceiro}"/>
                </f:facet>
                <h:outputText style="font-weight: bold; font-size:9px;"
                              styleClass="blue" value="#{cheq.nomeNoCheque}" />
            </rich:column>

            <rich:column style="border-color:#FFF" id="numeroBanco">
                <f:facet name="header">
                    <h:outputText style="font-weight: bold; font-size:9px;"
                                  value="#{msg_aplic.prt_Cheque_banco}"/>
                </f:facet>
                <h:outputText style="font-weight: bold; font-size:9px;"
                              styleClass="blue"
                              value="#{cheq.numeroBanco}" />
            </rich:column>

            <rich:column style="border-color:#FFF" id="agencia">
                <f:facet name="header">
                    <h:outputText style="font-weight: bold; font-size:9px;"
                                  value="Conta/Agência"/>
                </f:facet>
                <h:outputText style="font-weight: bold; font-size:9px;"
                              styleClass="blue" value="#{cheq.conta} / #{cheq.agencia}" />
            </rich:column>

            <rich:column style="border-color:#FFF" id="numero">
                <f:facet name="header">
                    <h:outputText style="font-weight: bold; font-size:9px;"
                                  value="Número"/>
                </f:facet>
                <h:outputText style="font-weight: bold; font-size:9px;"
                              styleClass="blue" value="#{cheq.numero}" />
            </rich:column>

            <rich:column style="border-color:#FFF" id="dataLancamento">
                <f:facet name="header">
                    <h:outputText style="font-weight: bold; font-size:9px;"
                                  value="Lançamento"/>
                </f:facet>
                <h:outputText style="font-weight: bold; font-size:9px;"
                              styleClass="blue"
                              value="#{cheq.dataLancamento}">
                    <f:convertDateTime pattern="dd/MM/yyyy" />
                </h:outputText>
            </rich:column>
            <rich:column style="border-color:#FFF" id="dataCompensacao">
                <f:facet name="header">
                    <h:outputText style="font-weight: bold; font-size:9px;"
                                  value="#{GestaoRecebiveisControle.considerarDataCompensacaoOriginal ? 'Compensação Original' : 'Compensação'}"/>
                </f:facet>
                <a4j:outputPanel rendered="#{!GestaoRecebiveisControle.considerarDataCompensacaoOriginal}"
                                 title="Data de compensação original: #{cheq.dataOriginalApresentar}">
                    <h:outputText style="font-weight: bold; font-size:9px;"
                                  styleClass="blue"
                                  value="#{cheq.dataCompensacao}">
                        <f:convertDateTime pattern="dd/MM/yyyy" />
                    </h:outputText>
                </a4j:outputPanel>
                <a4j:outputPanel rendered="#{GestaoRecebiveisControle.considerarDataCompensacaoOriginal}"
                                 title="Data de compensação efetiva: #{cheq.dataCompensacaoApresentar}">
                    <h:outputText style="font-weight: bold; font-size:9px;"
                                  styleClass="blue"
                                  value="#{cheq.dataOriginalApresentar}">
                    </h:outputText>
                </a4j:outputPanel>

            </rich:column>
            <rich:column style="border-color:#FFF" id="valor">
                <f:facet name="header">
                    <h:outputText style="font-weight: bold; font-size:9px;"
                                  value="#{msg_aplic.prt_Cheque_valor}"/>
                </f:facet>
                <h:outputText style="font-weight: bold; font-size:9px;"
                              styleClass="blue" value="#{cheq.valor}">
                    <f:converter converterId="FormatadorNumerico" />
                </h:outputText>
            </rich:column>
            <rich:column style="border-color:#FFF" id="contaContido"
                         rendered="#{ConfiguracaoFinanceiroControle.confFinanceiro.usarMovimentacaoContas}">
                <f:facet name="header">
                    <h:outputText style="font-weight: bold; font-size:9px;"
                                  value="Conta Financeiro"/>
                </f:facet>
                <h:outputText style="font-weight: bold; font-size:9px;"
                              styleClass="blue" value="#{cheq.contaContido}"/>
            </rich:column>

            <rich:column style="border-color:#FFF" id="numeroLote">
                <f:facet name="header">
                    <h:outputText style="font-weight: bold; font-size:9px;"
                                  value="#{msg_aplic.prt_Finan_GestaoRecebiveis_Lote}"/>
                </f:facet>
                <a4j:commandLink action="#{GestaoLotesControle.prepareLoteCh}"
                                 style="font-weight: bold; font-size:9px;"
                                 styleClass="blue" value="#{cheq.numeroLote}"
                                 rendered="#{cheq.apresentarNumeroLote}"
                                 oncomplete="#{GestaoLotesControle.msgAlert}"/>
                <h:outputText value=" / " style="font-weight: bold; font-size:9px;"
                              styleClass="blue" rendered="#{cheq.loteAvulso > 0}"></h:outputText>

                <a4j:commandLink action="#{GestaoLotesControle.prepareLoteCh}"
                                 style="font-weight: bold; font-size:9px;" title="Lote Avulso"
                                 styleClass="blue" value="#{cheq.loteAvulso}"
                                 rendered="#{cheq.loteAvulso > 0}"
                                 oncomplete="#{GestaoLotesControle.msgAlert}"/>

            </rich:column>

            <rich:column style="border-color:#FFF"  headerClass="colunaDireita" rendered="#{GestaoRecebiveisControle.empresasSelecionadas.size() <= 1}">
                <f:facet name="header">
                    <h:panelGroup styleClass="chk-fa-container inline" layout="block">
                        <h:selectBooleanCheckbox value="#{GestaoRecebiveisControle.todosChequesMarcados}"
                                                 title="Adicionar todos os cheques ao Lote">
                            <a4j:support event="onclick" process="this" ajaxSingle="true"
                                         action="#{GestaoRecebiveisControle.marcaDesmarcaCheques}"
                                         reRender="panelMensagem, totalSelecionado, listaCheques, qtdeSelecionado,botoesCheques,painelchequestotal1,painelchequestotal2"/>
                        </h:selectBooleanCheckbox>
                        <span></span>
                    </h:panelGroup>


                </f:facet>

                <h:panelGroup styleClass="chk-fa-container inline" layout="block" style="margin-right: 5px">
                    <h:selectBooleanCheckbox id="chequeEscolhido" value="#{cheq.chequeEscolhido}"
                                             title="Adicionar a um Lote"
                                             rendered="#{cheq.loteAvulso == 0 && cheq.ativo
                                             && (ConfiguracaoFinanceiroControle.confFinanceiro.usarMovimentacaoContas || cheq.numeroLote == 0)}">
                        <a4j:support event="onclick" process="this" ajaxSingle="true"
                                     focus="chequeEscolhido"
                                     action="#{GestaoRecebiveisControle.calcularTotalCheques}"
                                     reRender="panelMensagem,totalSelecionado,qtdeSelecionado,botoesCheques,painelchequestotal1,painelchequestotal2"/>
                    </h:selectBooleanCheckbox>
                    <span></span>
                </h:panelGroup>

                <h:outputText rendered="#{cheq.movConta > 0}" value="(AVULSO)"
                              style="font-weight: bold; font-size:9px;"
                              styleClass="blue"/>
                <h:outputText rendered="#{!cheq.ativo}" value="(CANCELADO)"
                              style="font-weight: bold; font-size:9px;"
                              styleClass="blue"/>
                <a4j:commandLink rendered="#{cheq.movConta == 0}" title="Editar Cheques do Recibo"
                                 reRender="panelAutorizacaoFuncionalidade"
                                 styleClass="tooltipster inline"
                                 actionListener="#{EdicaoPagamentoControle.prepareRecibo2}"
                                 oncomplete="#{EdicaoPagamentoControle.mensagemNotificar}">
                    <i class="fa-icon-edit"></i>
                    <f:attribute name="chequeTO" value="#{cheq}" />
                </a4j:commandLink>


                <a4j:commandLink title="Historico do Cheque" reRender="modalHistoricoCheque" styleClass="tooltipster inline"
                                   actionListener="#{ChequeControle.escolherCheque}"
                                   oncomplete="Richfaces.showModalPanel('modalHistoricoCheque')">
                    <i class="fa-icon-hourglass"></i>
                    <f:attribute name="chequeTO" value="#{cheq}" />
                </a4j:commandLink>

                <h:panelGroup style="border: none;"
                              rendered="#{cheq.loteAvulso > 0}">
                    <i class="fa-icon-info-sign" title="Este cheque está em um lote avulso para ser usado para pagar um lançamento, e não pode ser movimentado." class="tooltipster"></i>
                </h:panelGroup>

                <h:panelGroup style="border: none;"
                              rendered="#{cheq.loteAvulso == 0 && !ConfiguracaoFinanceiroControle.confFinanceiro.usarMovimentacaoContas && cheq.numeroLote > 0}">
                    <i class="fa-icon-info-sign" title="Este cheque já está depositado." class="tooltipster"></i>
                </h:panelGroup>
            </rich:column>
        </rich:dataTable>


<h:panelGroup layout="block" styleClass="painelGR baixo" id="painelchequestotal2">

    <div class="inlineBlock">
        <span class="cinza texto-size-14">
            Cheques selecionados =
        </span>
        <span class="cinza texto-size-14 negrito totalChequesSelecionados">
            <h:outputText value="#{GestaoRecebiveisControle.qtdeTotalCheques}"/>
        </span>
        <span class="cinza texto-size-14">
            Valor total=
        </span>
        <span class="cinza texto-size-14 negrito valorChequesSelecionados" style="margin-right: 10px;">
            ${GestaoRecebiveisControle.empresaLogado.moeda} <h:outputText value="#{GestaoRecebiveisControle.totalCheques}">
                <f:converter converterId="FormatadorNumerico"/>
            </h:outputText>
        </span>
    </div>
    <a4j:commandLink id="retirarChequesLotes2"  style="margin-left:10px;"
                     action="#{GestaoRecebiveisControle.abrirRetiradaLotes}"
                     reRender="formRetirarChequeLote"
                     oncomplete="#{GestaoRecebiveisControle.msgAlert}"
                     rendered="#{ConfiguracaoFinanceiroControle.confFinanceiro.usarMovimentacaoContas && GestaoRecebiveisControle.exibeRetirarChequeLotes
                                    && GestaoRecebiveisControle.empresasSelecionadas.size() <= 1}"
                     styleClass="inlineBlock pure-button pure-button-small texto-size-14">
        <i class="fa-icon-trash" ></i>&nbsp Retirar dos lotes
    </a4j:commandLink>

    <a4j:commandLink id="loteAvulsocheques2" style="margin-left:10px"
                     oncomplete="#{GestaoRecebiveisControle.msgAlert}"
                     action="#{GestaoRecebiveisControle.gerarLoteAvulso}" reRender="formLoteAvulso"
                     rendered="#{ConfiguracaoFinanceiroControle.confFinanceiro.usarMovimentacaoContas && GestaoRecebiveisControle.empresasSelecionadas.size() <= 1}"
                     styleClass="pure-button pure-button-small texto-size-14 inlineBlock">
        Lote Avulso
    </a4j:commandLink>

    <a4j:commandLink id="depositarcheques2"  style="margin: 0 10px"
                     oncomplete="#{GestaoRecebiveisControle.onCompleteDepositar}"
                     action="#{GestaoRecebiveisControle.salvaEscolha}"
                     reRender="panelDeposito"
                     rendered="#{ConfiguracaoFinanceiroControle.confFinanceiro.usarMovimentacaoContas && GestaoRecebiveisControle.empresasSelecionadas.size() <= 1}"
                     styleClass="pure-button pure-button-small pure-button-primary texto-size-14 inlineBlock">
        <i class="fa-icon-usd" ></i>&nbsp Movimentar
    </a4j:commandLink>

    <a4j:commandLink id="depositarSimplesCheque" style="margin-left:10px;"
                     oncomplete="#{GestaoRecebiveisControle.onCompleteDepositar}"
                     action="#{GestaoRecebiveisControle.salvaEscolha}"
                     reRender="panelDeposito"
                     rendered="#{!ConfiguracaoFinanceiroControle.confFinanceiro.usarMovimentacaoContas && GestaoRecebiveisControle.empresasSelecionadas.size() <= 1}"
                     styleClass="pure-button pure-button-small pure-button-primary texto-size-14 inlineBlock">
        <i class="fa-icon-usd"></i>&nbsp Depositar
    </a4j:commandLink>
</h:panelGroup>


</c:if>
</h:panelGroup>

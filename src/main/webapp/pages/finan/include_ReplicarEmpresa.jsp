<c:if test="${PlanoContasControle.visaoReplicarEmpresa}">
    <h:form>
    <%@page pageEncoding="ISO-8859-1" %>
    <%@include file="includes/imports.jsp" %>

        <h:panelGroup layout="block" style="margin-bottom: 15px; text-align: right">
        </h:panelGroup>

    <h:panelGrid columns="1" width="100%" headerClass="subordinado"
                 columnClasses="colunaCentralizada">
        <f:facet name="header">
            <h:outputText value="#{msg_aplic.prt_PlanoReplicarEmpresa_tituloForm}"/>
        </f:facet>
        <h:panelGrid columns="3" style="border-style: solid;" id="contadorReplicaPlano"
                     columnClasses="colunaCentralizada, colunaCentralizada, colunaCentralizada"
                     width="100%">
            <h:outputText value="Unidades" styleClass="botoes nvoBt"/>
            <h:outputText value="Replicadas" styleClass="botoes nvoBt"/>
            <h:outputText value="Não Replicadas" styleClass="botoes nvoBt"/>
            <h:outputText value="#{PlanoContasControle.listaPlanoContaRedeEmpresaSize}"
                          style="font-size: 20pt; font-weight: bold;"/>
            <h:outputText value="#{PlanoContasControle.listaPlanoContaRedeEmpresaSincronizado}"
                          style="color: #0f4c36; font-size: 20pt; font-weight: bold;"/>
            <h:outputText
                    value="#{PlanoContasControle.listaPlanoContaRedeEmpresaSize - PlanoContasControle.listaPlanoContaRedeEmpresaSincronizado}"
                    style="color: #8b0000; font-size: 20pt; font-weight: bold;"/>
        </h:panelGrid>
        <h:panelGrid columns="1" id="contadorReplicaPlano2"
                     columnClasses="colunaDireita"
                     width="100%"
                     style="margin-top: 20px; margin-bottom: 1px">
            <h:panelGroup layout="block">
                <a4j:commandButton value="Replicar Todas" styleClass="botoes nvoBt"
                                   action="#{PlanoContasControle.replicarTodas}"
                                   reRender="listaEmpresasReplicar, contadorReplicaPlano"
                                   ajaxSingle="true" immediate="true"/>
                <a4j:commandButton value="Replicar Selecionadas" styleClass="botoes nvoBt btSec"
                                   action="#{PlanoContasControle.replicarSelecionadas}"
                                   reRender="listaEmpresasReplicar, contadorReplicaPlano"
                                   ajaxSingle="true" immediate="true"/>
                <a4j:commandButton value="Limpar Selecionadas" styleClass="botoes nvoBt btSec"
                                   action="#{PlanoContasControle.limparReplicar}"
                                   reRender="listaEmpresasReplicar, contadorReplicaPlano"
                                   ajaxSingle="true" immediate="true"/>
            </h:panelGroup>
        </h:panelGrid>
        <h:panelGrid columns="1" rowClasses="linhaImpar, linhaPar"
                     columnClasses="colunaCentralizada" width="100%">

            <h:dataTable id="listaEmpresasReplicar" width="100%" headerClass="subordinado"
                         styleClass="tabFormSubordinada"
                         rowClasses="linhaImpar, linhaPar"
                         columnClasses="colunaEsquerda, colunaEsquerda, colunaCentralizada, colunaEsquerda"
                         style="text-align: center;"
                         value="#{PlanoContasControle.listaPlanoContaRedeEmpresa}"
                         var="planoContaRedeEmpresaReplicacao">
                <h:column>
                    <f:facet name="header">
                        <h:outputText value=""/>
                    </f:facet>
                    <h:selectBooleanCheckbox id="check" styleClass="form"
                                             rendered="#{!planoContaRedeEmpresaReplicacao.dataAtualizacaoInformada}"
                                             value="#{planoContaRedeEmpresaReplicacao.selecionado}">
                        <a4j:support event="onchange" reRender="listaEmpresasReplicar"/>
                    </h:selectBooleanCheckbox>
                </h:column>
                <h:column>
                    <f:facet name="header">
                        <h:outputText value="#{msg_aplic.prt_PlanoRedeEmpresa_nomeUnidade}"/>
                    </f:facet>
                    <h:outputText value="#{planoContaRedeEmpresaReplicacao.nomeUnidade}"/>
                </h:column>
                <h:column>
                    <f:facet name="header">
                        <h:outputText value="#{msg_aplic.prt_PlanoRedeEmpresa_chave}"/>
                    </f:facet>
                    <h:outputText value="#{planoContaRedeEmpresaReplicacao.chaveDestino}"/>
                </h:column>
                <h:column>
                    <f:facet name="header">
                        <h:outputText value=""/>
                    </f:facet>
                    <h:panelGroup>
                        <a4j:commandButton id="replicarPlano"
                                           reRender="listaEmpresasReplicar, contadorReplicaPlano"
                                           ajaxSingle="true" immediate="true"
                                           rendered="#{!planoContaRedeEmpresaReplicacao.dataAtualizacaoInformada}"
                                           action="#{PlanoContasControle.replicarPlanoContaRedeEmpresaGeral}"
                                           value="Replicar"/>
                        <h:graphicImage url="./img/check.png"
                                        rendered="#{planoContaRedeEmpresaReplicacao.dataAtualizacaoInformada}"/>
                    </h:panelGroup>
                </h:column>
                <h:column>
                    <f:facet name="header">
                        <h:outputText
                                value="#{msg_aplic.prt_PlanoRedeEmpresa_mensagemSituacao}"/>
                    </f:facet>
                    <h:outputText value="#{planoContaRedeEmpresaReplicacao.mensagemSituacao}"/>
                </h:column>
                <h:column>
                    <f:facet name="header">
                        <h:outputText
                                value="Vínculo"/>
                    </f:facet>
                    <h:panelGroup>
                        <a4j:commandButton
                                rendered="#{planoContaRedeEmpresaReplicacao.exibirBotaoRetirarVinculo}"
                                reRender="listaEmpresasReplicar, contadorReplicaPlano"
                                ajaxSingle="true" immediate="true"
                                action="#{PlanoContasControle.retirarVinculoReplicacao}"
                                value="Retirar"/>
                    </h:panelGroup>
                </h:column>
            </h:dataTable>
        </h:panelGrid>
    </h:panelGrid>
    </h:form>
</c:if>
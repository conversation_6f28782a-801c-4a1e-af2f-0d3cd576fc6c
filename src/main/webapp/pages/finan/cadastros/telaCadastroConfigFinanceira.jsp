<%@include file="../includes/include_imports.jsp" %>

<f:view>
    <jsp:include page="/includes/include_carregando_ripple.jsp" flush="true"/>
    <h:form id="form">
        <html>

            <!-- Inclui o elemento HEAD da p�gina -->
            <%@include file="/pages/finan/includes/include_head_finan.jsp" %>

            <body>
            <h:panelGroup layout="block" style="display: inline-table;width: 100%;" styleClass="fundoCinza">
                <h:panelGroup layout="block" styleClass="bgtop topoZW" rendered="#{MenuControle.apresentarTopo}">
                    <jsp:include page="../../../include_topo_novo.jsp" flush="true"/>
                    <jsp:include page="../../../include_menu_fin_flat.jsp" flush="true"/>
                    <rich:jQuery selector=".item2" query="addClass('menuItemAtual')"/>
                </h:panelGroup>

                <h:panelGroup layout="block" styleClass="caixaCorpo">
                    <h:panelGroup layout="block" style="height: 80%;width: 100%">
                        <h:panelGroup layout="block" styleClass="caixaMenuLatel">
                            <h:panelGroup layout="block" styleClass="container-imagem container-conteudo-central">

                                <h:panelGroup layout="block" styleClass="container-box form-flat">

                                    <h:panelGroup styleClass="container-box-header" layout="block">
                                        <h:panelGroup layout="block" styleClass="margin-box">
                                            <h:outputText styleClass="container-header-titulo"
                                                          value="Config. Financeiras"/>
                                            <h:outputLink styleClass="linkWiki"
                                                          value="#{SuperControle.urlBaseConhecimento}configuracoes-da-engrenagem-do-modulo-financeiro/"
                                                          title="Clique e saiba mais: Config. Financeiras"
                                                          target="_blank">
                                                <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                                            </h:outputLink>
                                        </h:panelGroup>
                                    </h:panelGroup>

                                    <h:panelGroup layout="block" styleClass="margin-box">
                                        <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
                                            <h:panelGrid columnClasses="w33,w33,w33" columns="3" width="100%" cellpadding="0" cellspacing="0">
                                                <h:panelGrid columns="1" style="height:100%;" width="100%" cellpadding="5" cellspacing="5" >
                                                    <h:panelGroup>
                                                        <div>
                                                            <h:outputText styleClass="fa-icon-pushpin textoCorAzul"/>
                                                            <rich:spacer width="5px"/>
                                                            <a4j:commandLink  styleClass="tituloCampos" id="bancoDescritivo" rendered="#{LoginControle.permissaoAcessoMenuVO.banco}" onclick="abrirPopup('../../../faces/bancoCons.jsp', 'Banco', 800, 595);" value="Banco"/>
                                                            <h:outputText styleClass="text" id="bancoDescritivoText" rendered="#{!LoginControle.permissaoAcessoMenuVO.banco}"  value="Banco"/>
                                                        </div>
                                                        <rich:spacer height="10px"/>
                                                        <h:outputText styleClass="text" value="Aqui ser�o cadastrados os Bancos utilizados para os pagamentos efetuados com cheque. Lembre-se que cada banco possui seu pr�prio c�digo identificador e � extremamente importante que essa codifica��o seja seguida no momento de cadastro.
                                                                                  "/>
                                                    </h:panelGroup>
                                                    <h:panelGroup>
                                                        <h:outputText styleClass="text" value="Ex.: Ao efetuar o cadastro do Banco do Brasil definirei o c�digo como 01. Ao cadastrar a Caixa Econ�mica Federal irei lan�ar o c�digo como 104."/>
                                                    </h:panelGroup>
                                                </h:panelGrid>
                                                <h:panelGrid style="height:100%" columns="1" width="100%" cellpadding="5" cellspacing="5" >
                                                    <h:panelGroup>
                                                        <div>
                                                            <h:outputText styleClass="fa-icon-pushpin textoCorAzul"/>
                                                            <rich:spacer width="5px"/>
                                                            <a4j:commandLink  styleClass="tituloCampos" id="formaPagamentoDescritivo" rendered="#{LoginControle.permissaoAcessoMenuVO.formaPagamento}" onclick="abrirPopup('../../../faces/formaPagamentoCons.jsp', 'FormaPagamento', 800, 595);" value="Formas de Pagamento"/></div>
                                                        <h:outputText styleClass="text" id="formaPagamentoDescritivoText" rendered="#{!LoginControle.permissaoAcessoMenuVO.formaPagamento}"  value="Formas de Pagamento"/>
                                                        <rich:spacer height="10px"/>
                                                        <h:outputText styleClass="text"
                                                                      value="Quais as formas de pagamento voc� disponibiliza para seus Clientes?
                                                                                      Esta tela de lan�amento permite que voc� cadastre todas estas formas, mantendo assim,
                                                                                      o controle de todos os pagamentos efetuados pelos seus Alunos, de maneira r�pida e organizada."/>
                                                    </h:panelGroup>
                                                    <h:panelGroup>
                                                        <rich:spacer height="25px"/>
                                                        <div>
                                                            <h:outputText styleClass="fa-icon-pushpin textoCorAzul"/>
                                                            <rich:spacer width="5px"/>
                                                            <a4j:commandLink styleClass="tituloCampos" action="#{MetaFinanceiroControle.abrirTelaMetas}"
                                                                             value="Metas do Financeiro"
                                                                             oncomplete="#{MetaFinanceiroControle.msgAlert}"/>
                                                        </div>
                                                        <rich:spacer height="10px"/>
                                                        <h:outputText styleClass="text" value="Efetue o cadastro de Metas do Financeiro."/>
                                                    </h:panelGroup>
                                                </h:panelGrid>
                                                <h:panelGrid columns="1" width="100%" style="height:100%" cellpadding="5" cellspacing="5" >
                                                    <h:panelGroup>
                                                        <div><img src="${contextoFinan}/images/imagemTelaConfigsFinanceiras.png"></div>
                                                    </h:panelGroup>
                                                </h:panelGrid>

                                            </h:panelGrid>

                                        </h:panelGrid>
                                    </h:panelGroup>
                                </h:panelGroup>
                            </h:panelGroup>

                            <jsp:include page="../../../include_box_menulateral.jsp">
                                <jsp:param name="menu" value="FIN-CONFIG-FINANCEIRAS" />
                            </jsp:include>

                        </h:panelGroup>
                    </h:panelGroup>
                </h:panelGroup>

                <jsp:include page="../../../include_rodape_flat.jsp" flush="true" />
            </h:panelGroup>
            </body>
        </html>
    </h:form>
</f:view>
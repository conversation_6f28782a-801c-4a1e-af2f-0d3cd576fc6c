
<%@page contentType="text/html"%>
<%@page pageEncoding="ISO-8859-1"%>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<f:loadBundle var="msg_menu" basename="propriedades.Menu"/>
<!-- inicio box -->


<h:panelGroup layout="block" styleClass="menuLateral">
    <h:panelGroup layout="block" styleClass="grupoMenuLateral">
        <h:panelGroup layout="block" styleClass="grupoMenuTitulo">
            <i class="fa-icon-list"></i> Cadastros Auxiliares
        </h:panelGroup>

        <h:panelGroup layout="block" styleClass="grupoMenuItem">
            <a4j:commandLink value="Fornecedor"
                             styleClass="titulo3 linkFuncionalidade"
                             action="#{FuncionalidadeControle.abrirComCasoNavegacao}"
                             actionListener="#{FuncionalidadeControle.prepararFuncionalidade}"
                             oncomplete="#{FuncionalidadeControle.abrirPopUp}">
                <f:attribute name="funcionalidade" value="FORNCEDOR" />
            </a4j:commandLink>

            <h:outputLink styleClass="linkWiki"
                          value="#{SuperControle.urlBaseConhecimento}como-cadastrar-um-fornecedor/"
                          title="Clique e saiba mais: Fornecedor"
                          target="_blank">
                <i class="fa-icon-question-sign" style="font-size: 18px"></i>
            </h:outputLink>
        </h:panelGroup>

        <h:panelGroup layout="block" styleClass="grupoMenuItem">

            <a class="titulo3 linkFuncionalidade" id="pessoa" href="#"
               onclick="abrirPopup('${contextoFinan}/faces/faces/pessoaCons.jsp', 'Pessoa', 800, 595);">
                <h:outputText value="#{CElabels['menu.cadastros.pessoa']}"/>
            </a>

            <h:outputLink styleClass="linkWiki"
                          value="#{SuperControle.urlBaseConhecimento}cadastro-de-pessoa/"
                          title="Clique e saiba mais: Pessoa"
                          target="_blank">
                <i class="fa-icon-question-sign" style="font-size: 18px"></i>
            </h:outputLink>

        </h:panelGroup>

        <h:panelGroup layout="block" styleClass="grupoMenuItem" rendered="#{ConfiguracaoFinanceiroControle.confFinanceiro.usarMovimentacaoContas}">

            <a4j:commandLink value="Conta"
                             styleClass="titulo3 linkFuncionalidade"
                             action="#{FuncionalidadeControle.abrirComCasoNavegacao}"
                             actionListener="#{FuncionalidadeControle.prepararFuncionalidade}"
                             oncomplete="#{FuncionalidadeControle.abrirPopUp}">
                <f:attribute name="funcionalidade" value="FINAN_CONTA" />
            </a4j:commandLink>

            <h:outputLink styleClass="linkWiki"
                          value="#{SuperControle.urlBaseConhecimento}como-cadastrar-ou-editar-uma-conta-para-movimentacoes-financeiras/"
                          title="Clique e saiba mais: Conta"
                          target="_blank">
                <i class="fa-icon-question-sign" style="font-size: 18px"></i>
            </h:outputLink>

        </h:panelGroup>

        <h:panelGroup layout="block" styleClass="grupoMenuItem" rendered="#{ConfiguracaoFinanceiroControle.confFinanceiro.usarMovimentacaoContas}">

            <a4j:commandLink value="Tipo de Conta"
                             id="tipoConta"
                             styleClass="titulo3 linkFuncionalidade"
                             action="#{FuncionalidadeControle.abrirComCasoNavegacao}"
                             actionListener="#{FuncionalidadeControle.prepararFuncionalidade}"
                             oncomplete="#{FuncionalidadeControle.abrirPopUp}">
                <f:attribute name="funcionalidade" value="TIPO_CONTA" />
            </a4j:commandLink>

            <h:outputLink styleClass="linkWiki"
                          value="#{SuperControle.urlBaseConhecimento}tipo-de-contas-e-seus-comportamentos/"
                          title="Clique e saiba mais: Tipo de Conta"
                          target="_blank">
                <i class="fa-icon-question-sign" style="font-size: 18px"></i>
            </h:outputLink>

        </h:panelGroup>

        <h:panelGroup layout="block" styleClass="grupoMenuItem" rendered="#{ContaContabilControle.mostrarContaContabil}">

            <a4j:commandLink value="Conta Contábil"
                             id="contaContabil"
                             styleClass="titulo3 linkFuncionalidade"
                             action="#{ContaContabilControle.abrirContaContabil}"
                             oncomplete="#{ContaContabilControle.msgAlert}">
                <f:attribute name="funcionalidade" value="TIPO_CONTA" />
            </a4j:commandLink>

            <h:outputLink styleClass="linkWiki"
                          value="#{SuperControle.urlBaseConhecimento}tipo-de-contas-e-seus-comportamentos/"
                          title="Clique e saiba mais: Tipo de Conta"
                          target="_blank">
                <i class="fa-icon-question-sign" style="font-size: 18px"></i>
            </h:outputLink>

        </h:panelGroup>

        <h:panelGroup layout="block" styleClass="grupoMenuItem">

            <a4j:commandLink value="Tipo de Documento"
                             styleClass="titulo3 linkFuncionalidade"
                             action="#{FuncionalidadeControle.abrirComCasoNavegacao}"
                             actionListener="#{FuncionalidadeControle.prepararFuncionalidade}"
                             oncomplete="#{FuncionalidadeControle.abrirPopUp}">
                <f:attribute name="funcionalidade" value="TIPO_DOCUMENTO" />
            </a4j:commandLink>

            <h:outputLink styleClass="linkWiki"
                          value="#{SuperControle.urlBaseConhecimento}como-cadastrar-um-tipo-de-documento/"
                          title="Clique e saiba mais: Tipo de Documento"
                          target="_blank">
                <i class="fa-icon-question-sign" style="font-size: 18px"></i>
            </h:outputLink>

        </h:panelGroup>

        <h:panelGroup layout="block" styleClass="grupoMenuItem"  rendered="#{LoginControle.permissaoAcessoMenuVO.categoria}">

            <a4j:commandLink value="#{CElabels['menu.cadastros.categoriaCliente']}"
                             styleClass="titulo3 linkFuncionalidade"
                             action="#{FuncionalidadeControle.abrirComCasoNavegacao}"
                             actionListener="#{FuncionalidadeControle.prepararFuncionalidade}"
                             oncomplete="#{FuncionalidadeControle.abrirPopUp}">
                <f:attribute name="funcionalidade" value="CATEGORIA_CLIENTES" />
            </a4j:commandLink>

            <h:outputLink styleClass="linkWiki"
                          value="#{SuperControle.urlWiki}Cadastros:Cadastros_Auxiliares:Categoria_de_Clientes"
                          title="Clique e saiba mais: Categoria de cliente"
                          target="_blank">
                <i class="fa-icon-question-sign" style="font-size: 18px"></i>
            </h:outputLink>

        </h:panelGroup>

        <h:panelGroup layout="block" styleClass="grupoMenuItem"  rendered="#{LoginControle.permissaoAcessoMenuVO.cliente}">

            <a4j:commandLink id="cliente" value="#{CElabels['menu.cadastros.cliente']}"
                             styleClass="titulo3 linkFuncionalidade"
                             action="#{FuncionalidadeControle.abrirComCasoNavegacao}"
                             actionListener="#{FuncionalidadeControle.prepararFuncionalidade}"
                             oncomplete="#{FuncionalidadeControle.abrirPopUp}">
                <f:attribute name="funcionalidade" value="CLIENTE" />
            </a4j:commandLink>

            <h:outputLink styleClass="linkWiki"
                          value="#{SuperControle.urlBaseConhecimento}como-excluir-cadastro-de-pessoas-alunos/"
                          title="Clique e saiba mais: Cliente"
                          target="_blank">
                <i class="fa-icon-question-sign" style="font-size: 18px"></i>
            </h:outputLink>

        </h:panelGroup>

        <h:panelGroup layout="block" styleClass="grupoMenuItem"  rendered="#{LoginControle.permissaoAcessoMenuVO.colaborador}">

            <a4j:commandLink id="colaborador"
                             value="#{CElabels['menu.cadastros.colaborador']}"
                             styleClass="titulo3 linkFuncionalidade"
                             action="#{FuncionalidadeControle.abrirComCasoNavegacao}"
                             actionListener="#{FuncionalidadeControle.prepararFuncionalidade}"
                             oncomplete="#{FuncionalidadeControle.abrirPopUp}">
                <f:attribute name="funcionalidade" value="COLABORADOR" />
            </a4j:commandLink>

            <h:outputLink styleClass="linkWiki"
                          value="#{SuperControle.urlBaseConhecimento}como-cadastrar-um-novo-colaborador/"
                          title="Clique e saiba mais: Colaborador"
                          target="_blank">
                <i class="fa-icon-question-sign" style="font-size: 18px"></i>
            </h:outputLink>

        </h:panelGroup>

        <h:panelGroup layout="block" styleClass="grupoMenuItem"  rendered="#{LoginControle.permissaoAcessoMenuVO.grauInstrucao}">

            <a4j:commandLink id="grauInstrucao"
                             value="#{CElabels['menu.cadastros.grauInstrucao']}"
                             styleClass="titulo3 linkFuncionalidade"
                             action="#{FuncionalidadeControle.abrirComCasoNavegacao}"
                             actionListener="#{FuncionalidadeControle.prepararFuncionalidade}"
                             oncomplete="#{FuncionalidadeControle.abrirPopUp}">
                <f:attribute name="funcionalidade" value="GRAU_DE_INSTRUCAO" />
            </a4j:commandLink>

            <h:outputLink styleClass="linkWiki"
                          value="#{SuperControle.urlWiki}Cadastros:Cadastros_Auxiliares:Grau_de_Instrucao"
                          title="Clique e saiba mais: Grau de instrução"
                          target="_blank">
                <i class="fa-icon-question-sign" style="font-size: 18px"></i>
            </h:outputLink>

        </h:panelGroup>

        <h:panelGroup layout="block" styleClass="grupoMenuItem"  rendered="#{LoginControle.permissaoAcessoMenuVO.profissao}">

            <a4j:commandLink id="profissao"
                             value="#{CElabels['menu.cadastros.profissao']}"
                             styleClass="titulo3 linkFuncionalidade"
                             action="#{FuncionalidadeControle.abrirComCasoNavegacao}"
                             actionListener="#{FuncionalidadeControle.prepararFuncionalidade}"
                             oncomplete="#{FuncionalidadeControle.abrirPopUp}">
                <f:attribute name="funcionalidade" value="PROFISSAO" />
            </a4j:commandLink>

            <h:outputLink styleClass="linkWiki"
                          value="#{SuperControle.urlWiki}Cadastros:Cadastros_Auxiliares:Profissao"
                          title="Clique e saiba mais: Profissão"
                          target="_blank">
                <i class="fa-icon-question-sign" style="font-size: 18px"></i>
            </h:outputLink>

        </h:panelGroup>

        <h:panelGroup layout="block" styleClass="grupoMenuItem"  rendered="#{LoginControle.permissaoAcessoMenuVO.pais}">

            <a4j:commandLink id="pais"
                             value="#{CElabels['menu.cadastros.pais']}"
                             styleClass="titulo3 linkFuncionalidade"
                             action="#{FuncionalidadeControle.abrirComCasoNavegacao}"
                             actionListener="#{FuncionalidadeControle.prepararFuncionalidade}"
                             oncomplete="#{FuncionalidadeControle.abrirPopUp}">
                <f:attribute name="funcionalidade" value="PAIS" />
            </a4j:commandLink>

            <h:outputLink styleClass="linkWiki"
                          value="#{SuperControle.urlWiki}Cadastros:Cadastros_Auxiliares:Pais"
                          title="Clique e saiba mais: País"
                          target="_blank">
                <i class="fa-icon-question-sign" style="font-size: 18px"></i>
            </h:outputLink>

        </h:panelGroup>

        <h:panelGroup layout="block" styleClass="grupoMenuItem"  rendered="#{LoginControle.permissaoAcessoMenuVO.cidade}">

            <a4j:commandLink id="cidade"
                             value="#{CElabels['menu.cadastros.cidade']}"
                             styleClass="titulo3 linkFuncionalidade"
                             action="#{FuncionalidadeControle.abrirComCasoNavegacao}"
                             actionListener="#{FuncionalidadeControle.prepararFuncionalidade}"
                             oncomplete="#{FuncionalidadeControle.abrirPopUp}">
                <f:attribute name="funcionalidade" value="CIDADE" />
            </a4j:commandLink>

            <h:outputLink styleClass="linkWiki"
                          value="#{SuperControle.urlWiki}Cadastros:Cadastros_Auxiliares:Cidade"
                          title="Clique e saiba mais: Cidade"
                          target="_blank">
                <i class="fa-icon-question-sign" style="font-size: 18px"></i>
            </h:outputLink>

        </h:panelGroup>

    </h:panelGroup>

</h:panelGroup>
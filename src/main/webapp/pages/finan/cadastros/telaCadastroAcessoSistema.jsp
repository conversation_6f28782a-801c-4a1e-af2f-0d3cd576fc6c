<%@include file="../includes/include_imports.jsp" %>

<f:view>
    <jsp:include page="/includes/include_carregando_ripple.jsp" flush="true"/>
    <h:form id="form">
    	<html>
    	
		<!-- Inclui o elemento HEAD da p�gina -->
		<%@include file="/pages/ce/includes/include_head.jsp" %>
		
		<body>
        <table width="100%" height="100%" border="0" cellpadding="0" cellspacing="0">
        
            <%@include file="../includes/include_topo.jsp"%>
            
            <tr>
                <td align="left" valign="top" class="bglateral">
                    <table width="100%" height="100%" border="0" cellpadding="0" cellspacing="0">
                        <tr>
                            <td width="206" align="center" valign="top" class="bglateraltop" style="padding-top:6px;">
								<%@include file="/pages/ce/includes/include_box_menulatcadastrosacesso.jsp" %>
                            </td>
                            <td align="left" valign="top" style="padding:7px 15px 0 20px;">
                                <div style="clear:both;">
                                    <table width="98%" border="0" align="left" cellpadding="0" cellspacing="0" class="text" style="margin-right:30px;margin-bottom:20px;">
                                        <tr>
                                            <td width="19" height="50" align="left" valign="top"><img src="${contexto}/images/box_centro_top_left.gif" width="19" height="50"></td>
                                            <td align="left" valign="top" background="${contexto}/images/box_centro_top.gif" class="tituloboxcentro" style="padding:11px 0 0 0;">Acesso ao Sistema</td>
                                            <td width="19" align="left" valign="top"><img src="${contexto}/images/box_centro_top_right.gif" width="19" height="50"></td>
                                        </tr>
                                        <tr>
                                            <td align="left" valign="top" background="${contexto}/images/box_centro_left.gif"><img src="${contexto}/images/shim.gif"></td>
                                            <td align="left" valign="top" bgcolor="#ffffff" style="padding:15px 15px 5px 15px;">
                                                 <h:panelGrid columns="1" style="background-image:url('../../../imagens/backGroundTelaBasicoBack.png'); background-repeat:repeat-x;" width="100%" cellpadding="0" cellspacing="0">
                                                <h:panelGrid columnClasses="w33,w33,w33" style="background-image:url('../../../imagens/backGroundTelaBasico.png'); background-repeat: no-repeat;" columns="3" width="100%" cellpadding="0" cellspacing="0">


                                                    <h:panelGrid columns="1" style="height:100%;" width="100%" cellpadding="5" cellspacing="5" >
                                                        <h:panelGroup>
                                                            <div>
													        	 <h:outputLink styleClass="linkWiki"
													                                      value=""
													                                      title="Clique e saiba mais: Empresa"
													                                      target="_blank" >
                                                                     <i class="fa-icon-question-sign" style="font-size: 18px"></i>
													            </h:outputLink>
													        	<a class="titulo3" id="categoria" onclick="abrirPopup('${contexto}/faces/empresaCons.jsp', 'Empresa', 800, 595);" href="#">
													        		<h:outputText  value="#{CElabels['menu.cadastros.acessoSistema.empresa']}"/>
													            </a>
													        </div>
                                                            <rich:spacer height="25px"/>
                                                            <rich:spacer width="10px"/>
                                                            <h:outputText styleClass="text" value="Os sistemas Web da Pacto Solu��es trabalham com o conceito de multiempresa, onde atrav�s de apenas um sistema ser� poss�vel controlar v�rias filiais de uma mesma empresa. Nessa tela � poss�vel cadastrar as filiais que utilizar�o o sistema e acompanhar de perto o rendimento de cada uma delas."/>
                                                        </h:panelGroup>
                                                        <h:panelGroup>
                                                            <rich:spacer height="25px"/>
                                                            <div>
													        	<h:outputLink styleClass="linkWiki"
													                                      value=""
													                                      title="Clique e saiba mais: Perfis de Acesso"
													                                      target="_blank" >
                                                                    <i class="fa-icon-question-sign" style="font-size: 18px"></i>
													            </h:outputLink>
													        	<a class="titulo3" id="produto" onclick="abrirPopup('${contexto}/faces/perfilAcessoCons.jsp', 'perfilAcessoCons', 800, 595);" href="#">
													        		<h:outputText  value="#{CElabels['menu.cadastros.acessoSistema.perfisAcesso']}"/>
													            </a>
													        </div>
                                                            <rich:spacer height="25px"/>
                                                            <rich:spacer width="10px"/>
                                                            <h:outputText styleClass="text" value="O controle do que os colaboradores podem acessar ou n�o dentro de um sistema � muito importante e aqui voc� pode definir os perfis de acesso, limitando e permitindo que as pessoas tenham acessos diferentes ao sistema. Este � importante para a seguran�a do sistema, cadastre os perfis de acordo com a fun��o do colaborador."/>
                                                            <br />
                                                        </h:panelGroup>
                                                    </h:panelGrid>
                                                    <h:panelGrid style="height:100;%" columns="1" width="100%" cellpadding="5" cellspacing="5" >
                                                        <h:panelGroup>
                                                            <div>
													        	<h:outputLink styleClass="linkWiki"
													                                      value=""
													                                      title="Clique e saiba mais: Usu�rio"
													                                      target="_blank" >
                                                                    <i class="fa-icon-question-sign" style="font-size: 18px"></i>
													            </h:outputLink>
													        	<a class="titulo3" id="produtosLocacao" onclick="abrirPopup('${contexto}/faces/usuarioCons.jsp', 'Usu�rio', 820, 595);" href="#">
													        		<h:outputText  value="#{CElabels['menu.cadastros.acessoSistema.usuario']}"/>
													            </a>
													        </div>
                                                            <rich:spacer height="25px"/>
                                                            <rich:spacer width="10px"/>
                                                            <h:outputText styleClass="text" value="Cadastre aqui os usu�rios do sistema. Para acessar � necess�rio que o usu�rio seja devidamente cadastrado e possua Login (usu�rio) e Senha de acesso. Lembre-se que senhas s�o �nicas e n�o devem ser compartilhadas."/>
                                                        </h:panelGroup>
                                                        <h:panelGroup>
                                                            <rich:spacer height="25px"/>
                                                            <div>
													        	<h:outputLink styleClass="linkWiki"
													                                      value=""
													                                      title="Clique e saiba mais: Log do Sistema"
													                                      target="_blank" >
                                                                    <i class="fa-icon-question-sign" style="font-size: 18px"></i>
													            </h:outputLink>
													        	<a class="titulo3" id="tabelaPrecos" onclick="abrirPopup('${contexto}/faces/controleLogCons.jsp', 'controleLogCons', 800, 595);" href="#">
													        		<h:outputText  value="#{CElabels['menu.cadastros.acessoSistema.controleLog']}"/>
													            </a>
													        </div>
                                                            <rich:spacer height="25px"/>
                                                            <rich:spacer width="10px"/>
                                                            <h:outputText styleClass="text" value="Aqui temos os registros das opera��es que s�o realizadas no sistema, com as informa��es de qual usu�rio realizou a opera��o, em qual data e hor�rio, dentre outras informa��es. Por exemplo � poss�vel rastrear alguma opera��o que foi executada de forma indevida."/>
                                                        </h:panelGroup>

                                                    </h:panelGrid>
                                                    <h:panelGrid columns="1" width="100%" style="height:100%;" cellpadding="5" cellspacing="5" >
                                                        <h:panelGroup>
                                                            <div><img src="${contexto}/imagens/cadastros.png"></div>
                                                        </h:panelGroup>
                                                        
                                                    </h:panelGrid>

                                                </h:panelGrid>
                                            </h:panelGrid>
                                            </td>
                                            <td align="left" valign="top" background="${contexto}/images/box_centro_right.gif"><img src="${contexto}/images/shim.gif"></td>
                                        </tr>
                                        <tr>
                                            <td height="20" align="left" valign="top"><img src="${contexto}/images/box_centro_bottom_left.gif" width="19" height="20"></td>
                                            <td align="left" valign="top" background="${contexto}/images/box_centro_bottom.gif"><img src="${contexto}/images/shim.gif"></td>
                                            <td align="left" valign="top"><img src="${contexto}/images/box_centro_bottom_right.gif" width="19" height="20"></td>
                                        </tr>
                                    </table>
                                </div>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td height="93" align="left" valign="top" class="bgrodape"><jsp:include page="/include_rodape.jsp" flush="true" /></td>
            </tr>
        </table>
        </body>
        </html>
    </h:form>
</f:view>
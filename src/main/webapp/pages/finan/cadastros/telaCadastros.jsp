<%@include file="../includes/include_imports.jsp" %>

<f:view>
    <jsp:include page="/includes/include_carregando_ripple.jsp" flush="true"/>
    <h:form id="form">
        <html>

            <!-- Inclui o elemento HEAD da p�gina -->
            <%@include file="/pages/finan/includes/include_head_finan.jsp" %>

            <body>
                <table width="100%" height="100%" border="0" cellpadding="0" cellspacing="0">

                    <%@include file="../includes/include_topo.jsp"%>

                    <tr>
                        <td align="left" valign="top" class="bglateral">
                            <table width="100%" height="100%" border="0" cellpadding="0" cellspacing="0">
                                <tr>
                                    <td width="206" align="center" valign="top" class="bglateraltop" style="padding-top:6px;">
                                        <%@include file="includes/include_box_menulatcadastros.jsp" %>
                                    </td>
                                    <td align="left" valign="top" style="padding:7px 15px 0 20px;">
                                        <div style="clear:both;">
                                            <table width="98%" border="0" align="left" cellpadding="0" cellspacing="0" class="text" style="margin-right:30px;margin-bottom:20px;">
                                                <tr>
                                                    <td width="19" height="50" align="left" valign="top"><img src="${contextoFinan}/images/box_centro_top_left.gif" width="19" height="50"></td>
                                                    <td align="left" valign="top" background="${contextoFinan}/images/box_centro_top.gif" class="tituloboxcentro" style="padding:11px 0 0 0;">
                                                        Cadastros
                                                        <h:outputLink styleClass="linkWiki"
                                                                      value="#{SuperControle.urlWikiFIN}Cadastros:Cadastros"
                                                                      title="Clique e saiba mais: Cadastros"
                                                                      target="_blank" >
                                                            <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                                                        </h:outputLink>
                                                    </td>
                                                    <td width="19" align="left" valign="top"><img src="${contextoFinan}/images/box_centro_top_right.gif" width="19" height="50"></td>
                                                </tr>
                                                <tr>
                                                    <td align="left" valign="top" background="${contextoFinan}/images/box_centro_left.gif"><img src="${contextoFinan}/images/shim.gif"></td>
                                                    <td align="left" valign="top" bgcolor="#ffffff" style="padding:15px 15px 5px 15px;">
                                                        <h:panelGrid columns="1" style="background-image:url('../../../imagens/backGroundTelaBasicoBack.png'); background-repeat:repeat-x;" width="100%" cellpadding="0" cellspacing="0">
                                                            <h:panelGrid columnClasses="w33,w33,w33" style="background-image:url('../../../imagens/backGroundTelaBasico.png'); background-repeat: no-repeat;" columns="3" width="100%" cellpadding="0" cellspacing="0">

                                                                    <h:panelGroup>
                                                                        <rich:spacer height="25px"/>
                                                                        <div>
                                                                            <h:graphicImage alt="Marcador" value="../../../imagens/marcadorAzul.png"/>
                                                                            <rich:spacer width="5px"/>
                                                                            <a4j:commandLink action="#{MetaFinanceiroControle.abrirTelaMetas}"
                                                                                             value="Metas do Financeiro"
                                                                                             oncomplete="#{MetaFinanceiroControle.msgAlert}"/>
                                                                        </div>
                                                                        <rich:spacer height="25px"/>
                                                                        <rich:spacer width="10px"/>
                                                                        <h:outputText styleClass="text" value="Efetue o cadastro de Metas do Financeiro."/>
                                                                    </h:panelGroup>
                                                                </h:panelGrid>
                                                            </h:panelGrid>
                                                    </td>
                                                    <td align="left" valign="top" background="${contextoFinan}/images/box_centro_right.gif"><img src="${contextoFinan}/images/shim.gif"></td>
                                                </tr>
                                                <tr>
                                                    <td height="20" align="left" valign="top"><img src="${contextoFinan}/images/box_centro_bottom_left.gif" width="19" height="20"></td>
                                                    <td align="left" valign="top" background="${contextoFinan}/images/box_centro_bottom.gif"><img src="${contextoFinan}/images/shim.gif"></td>
                                                    <td align="left" valign="top"><img src="${contextoFinan}/images/box_centro_bottom_right.gif" width="19" height="20"></td>
                                                </tr>
                                            </table>
                                        </div>
                                    </td>
                                </tr>
                            </table>
                        </td>
                    </tr>
                    <tr>
                        <td height="93" align="left" valign="top" class="bgrodape"><jsp:include page="/include_rodape.jsp" flush="true" /></td>
                    </tr>
                </table>
            </body>
        </html>
    </h:form>
</f:view>
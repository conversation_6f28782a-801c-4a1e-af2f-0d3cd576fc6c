<%@include file="../includes/include_imports.jsp" %>

<!-- <PERSON><PERSON>i o elemento HEAD da p�gina -->
<%@include file="/pages/finan/includes/include_head_finan.jsp" %>

<f:view>
    <jsp:include page="/includes/include_carregando_ripple.jsp" flush="true"/>
    <h:form id="form">
        <html>


            <body>
            <h:panelGroup layout="block" style="display: inline-table;width: 100%;" styleClass="fundoCinza">
                <h:panelGroup layout="block" styleClass="bgtop topoZW" rendered="#{MenuControle.apresentarTopo}">
                    <jsp:include page="../../../include_topo_novo.jsp" flush="true"/>
                    <jsp:include page="../../../include_menu_fin_flat.jsp" flush="true"/>
                    <rich:jQuery selector=".item2" query="addClass('menuItemAtual')"/>
                </h:panelGroup>

                <h:panelGroup layout="block" styleClass="caixaCorpo">
                    <h:panelGroup layout="block" style="height: 80%;width: 100%">
                        <h:panelGroup layout="block" styleClass="caixaMenuLatel">
                            <h:panelGroup layout="block" styleClass="container-imagem container-conteudo-central">
                                <h:panelGroup layout="block" styleClass="container-box form-flat">

                                    <h:panelGroup styleClass="container-box-header" layout="block">
                                        <h:panelGroup layout="block" styleClass="margin-box">
                                            <h:outputText styleClass="container-header-titulo" value="Cadastros"/>
                                            <h:outputLink styleClass="linkWiki"
                                                          value="#{SuperControle.urlWikiFIN}M�duloEstrutural"
                                                          title="Clique e saiba mais: M�dulo Estrutural"
                                                          target="_blank">
                                                <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                                            </h:outputLink>
                                        </h:panelGroup>
                                    </h:panelGroup>

                                    <h:panelGroup layout="block" styleClass="margin-box">
                                        <h:panelGrid columns="1"  width="100%" cellpadding="0" cellspacing="0">
                                            <h:panelGrid columnClasses="w33,w33,w33"  columns="3" width="100%" cellpadding="0" cellspacing="0">

                                                <h:panelGrid columns="1" style="height:100%;" width="100%" cellpadding="5" cellspacing="5" >
                                                    <h:panelGroup>
                                                        <div>
                                                            <h:outputText styleClass="fa-icon-pushpin textoCorAzul"/>
                                                            <rich:spacer width="5px"/>
                                                            <a href="telaCadastroAuxiliares.jsp" class="tituloCampos"><h:outputText value="Cadastros Auxiliares"/>
                                                            </a>
                                                        </div>
                                                        <rich:spacer height="10px"/>
                                                        <h:outputText styleClass="text" value="
                                                                                      Defina as informa��es b�sicas necess�rias para realizar os cadastros
                                                                                      financeiros, permitindo que o sistema seja utilizado amplamente.
                                                                                      Esses cadastros se referem especificamente aos cadastros
                                                                                      financeiros pr�prios desse m�dulo (Fornecedor, Conta,
                                                                                      Tipo de Conta e Tipo de Documento).Defina as informa��es b�sicas necess�rias para realizar os
                                                                                      cadastros dos clientes e colaboradores, exemplo,
                                                                                      categorias de cliente, cidade, pa�s, profiss�es, etc.
                                                                                     pr�prios do ZillyonWeb.
                                                                                      "/>
                                                    </h:panelGroup>


                                                </h:panelGrid>
                                                <h:panelGrid columns="1" width="100%" style="height:100%;" cellpadding="5" cellspacing="5" >

                                                    <h:panelGroup>
                                                        <div>
                                                            <h:outputText styleClass="fa-icon-pushpin textoCorAzul"/>
                                                            <rich:spacer width="5px"/>
                                                            <a href="telaCadastroConfigFinanceira.jsp" class="tituloCampos"><h:outputText value="Config. Financeiras"/>
                                                            </a>
                                                        </div>
                                                        <rich:spacer height="10px"/>
                                                        <h:outputText styleClass="text" value="Cadastre as formas de pagamento e operadoras de cart�o que ser�o utilizados nos recebimentos. Ao realizar consultas as informa��es financeiras estar�o detalhadas. � poss�vel realizar estornos e verificar movimentos das contas dos clientes, cr�ditos ou d�bitos, se houver."/>
                                                    </h:panelGroup>
                                                </h:panelGrid>
                                            </h:panelGrid>
                                        </h:panelGrid>
                                    </h:panelGroup>
                                </h:panelGroup>
                            </h:panelGroup>

                            <jsp:include page="../../../include_box_menulateral.jsp">
                                <jsp:param name="menu" value="FIN-CADASTROS" />
                            </jsp:include>

                        </h:panelGroup>
                    </h:panelGroup>
                </h:panelGroup>

                <jsp:include page="../../../include_rodape_flat.jsp" flush="true" />
            </h:panelGroup>
            </body>
        </html>
    </h:form>
    <%@include file="../includes/include_modal_abrirCaixa.jsp" %>
</f:view>
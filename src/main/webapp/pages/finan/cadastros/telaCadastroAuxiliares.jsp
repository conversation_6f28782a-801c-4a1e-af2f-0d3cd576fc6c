<%@include file="../includes/include_imports.jsp" %>

<f:view>
    <jsp:include page="/includes/include_carregando_ripple.jsp" flush="true"/>
    <h:form id="form" styleClass="form-flat">
        <html>

            <!-- Inclui o elemento HEAD da p�gina -->
            <%@include file="/pages/finan/includes/include_head_finan.jsp" %>

            <body>
                <h:panelGroup layout="block" style="display: inline-table;width: 100%;" styleClass="fundoCinza">
                <h:panelGroup layout="block" styleClass="bgtop topoZW" rendered="#{MenuControle.apresentarTopo}">
                    <jsp:include page="../../../include_topo_novo.jsp" flush="true"/>
                    <jsp:include page="../../../include_menu_fin_flat.jsp" flush="true"/>
                    <rich:jQuery selector=".item2" query="addClass('menuItemAtual')"/>
                </h:panelGroup>

                <h:panelGroup layout="block" styleClass="caixaCorpo">
                    <h:panelGroup layout="block" style="height: 80%;width: 100%">
                        <h:panelGroup layout="block" styleClass="caixaMenuLatel">
                            <h:panelGroup layout="block" styleClass="container-imagem container-conteudo-central">
                                <h:panelGroup layout="block" styleClass="container-box zw_ui especial ">

                                    <h:panelGroup styleClass="container-box-header" layout="block">
                                        <h:panelGroup layout="block" styleClass="margin-box">
                                            <h:outputText styleClass="container-header-titulo" value="Cadastros Auxiliares"/>
                                            <h:outputLink styleClass="linkWiki"
                                                          value="#{SuperControle.urlWiki}Cadastros:Cadastros_Auxiliares"
                                                          title="Clique e saiba mais: Cadastros Auxiliares"
                                                          target="_blank" >
                                                <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                                            </h:outputLink>
                                        </h:panelGroup>
                                    </h:panelGroup>

                                    <h:panelGroup layout="block" styleClass="margin-box">
                                        <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
                                            <h:panelGrid columnClasses="w33,w33,w33"  columns="3" width="100%" cellpadding="0" cellspacing="0">


                                                <h:panelGrid columns="1" style="height:100%;" width="100%" cellpadding="5" cellspacing="5" >
                                                    <h:panelGroup>
                                                        <div>
                                                            <h:outputText styleClass="fa-icon-pushpin textoCorAzul"/>
                                                            <rich:spacer width="5px"/>
                                                            <a4j:commandLink id="fornecedor" styleClass="tituloCampos" action="#{FornecedorControle.abrirFornecedor}"
                                                                             value="Fornecedor"
                                                                             oncomplete="#{FornecedorControle.msgAlert}"/>
                                                        </div>
                                                        <rich:spacer height="10px"/>
                                                        <h:outputText styleClass="text" value="Aqui voc� poder� cadastrar todos os seus fornecedores de servi�os para a realiza��o do evento, podendo nos seus or�amentos inserir os terceirizados que ser�o necess�rios e seus valores n�o influenciaram para o cliente, ficar� somente como informa��o para realizar o evento. Ao realizar o check-list essas informa��es ser�o dispostas."/>
                                                    </h:panelGroup>
                                                    <h:panelGroup>
                                                        <div>
                                                            <h:outputText styleClass="fa-icon-pushpin textoCorAzul"/>
                                                            <rich:spacer width="5px"/>
                                                            <a4j:commandLink id="pessoa" styleClass="tituloCampos" action="#{PessoaSimplificadoControle.abrirCadastroSimplificado}"
                                                                             value="Pessoa"
                                                                             oncomplete="#{PessoaSimplificadoControle.msgAlert}"/>
                                                        </div>
                                                        <rich:spacer height="10px"/>
                                                        <h:outputText styleClass="text" value="Aqui voc� poder� cadastrar Pessoas de uma maneira simplificada. Podendo referencia-las em contas a pagar do financeiro."/>
                                                    </h:panelGroup>
                                                    <h:panelGroup rendered="#{ConfiguracaoFinanceiroControle.confFinanceiro.usarMovimentacaoContas}">
                                                        <rich:spacer height="25px"/>
                                                        <div>
                                                            <h:outputText styleClass="fa-icon-pushpin textoCorAzul"/>
                                                            <rich:spacer width="5px"/>

                                                            <a4j:commandLink styleClass="tituloCampos" action="#{ContaControle.abrirConta}"
                                                                             value="Conta"
                                                                             oncomplete="#{ContaControle.msgAlert}"/>
                                                        </div>
                                                        <rich:spacer height="25px"/>
                                                        <rich:spacer width="10px"/>
                                                        <h:outputText styleClass="text" value="Aqui voc� poder� cadastrar as poss�veis diferentes contas da sua academia, podendo ter um maior controle do dinheiro gasto e recebido de determinada conta."/>
                                                    </h:panelGroup>
                                                    <h:panelGroup rendered="#{ConfiguracaoFinanceiroControle.confFinanceiro.usarMovimentacaoContas}">
                                                        <rich:spacer height="25px"/>
                                                        <div>
                                                            <h:outputText styleClass="fa-icon-pushpin textoCorAzul"/>
                                                            <rich:spacer width="5px"/>
                                                            <a4j:commandLink styleClass="tituloCampos" action="#{TipoContaControle.abrirTipoConta}"
                                                                             value="Tipo de Conta"
                                                                             oncomplete="#{TipoContaControle.msgAlert}"/>
                                                        </div>
                                                        <rich:spacer height="10px"/>
                                                        <h:outputText styleClass="text" value="Aqui voc� poder� cadastrar os tipos de contas da sua academia."/>
                                                    </h:panelGroup>
                                                    <h:panelGroup rendered="#{ContaContabilControle.mostrarContaContabil}">
                                                        <div>
                                                            <h:outputText styleClass="fa-icon-pushpin textoCorAzul"/>
                                                            <rich:spacer width="5px"/>
                                                            <a4j:commandLink id="idContaContabil" styleClass="tituloCampos" action="#{ContaContabilControle.abrirContaContabil}"
                                                                             value="Conta Cont�bil"
                                                                             oncomplete="#{ContaContabilControle.msgAlert}"/>
                                                        </div>
                                                        <rich:spacer height="10px"/>
                                                        <h:outputText styleClass="text" value="Aqui voc� poder� cadastrar todas as contas cont�beis para a integra��o com outros sistemas de contabilidade."/>
                                                    </h:panelGroup>


                                                    <h:panelGroup>
                                                        <rich:spacer height="25px"/>
                                                        <div>
                                                            <h:outputText styleClass="fa-icon-pushpin textoCorAzul"/>
                                                            <rich:spacer width="5px"/>
                                                            <a4j:commandLink styleClass="tituloCampos" action="#{TipoDocumentoControle.abrirTipoDocumento}"
                                                                             value="Tipo de Documento"
                                                                             oncomplete="#{TipoDocumentoControle.msgAlert}"/>
                                                        </div>
                                                        <rich:spacer height="10px"/>
                                                        <h:outputText styleClass="text" value="Aqui voc� poder� cadastrar os tipos de documentos usados nas diferentes formas de pagamento presentes na academia."/>
                                                    </h:panelGroup>

                                                </h:panelGrid>


                                                <h:panelGrid columns="1" style="height:100%;" width="100%" cellpadding="5" cellspacing="5" >
                                                    <h:panelGroup>
                                                        <rich:spacer height="5px"/>
                                                        <div>
                                                            <h:outputText styleClass="fa-icon-pushpin textoCorAzul"/>
                                                            <rich:spacer width="5px"/>
                                                            <h:panelGroup rendered="#{LoginControle.permissaoAcessoMenuVO.categoria}">
                                                                <a class="tituloCampos" id="categoria" onclick="abrirPopup('${contextoFinan}/faces/faces/categoriaCons.jsp', '', 800, 595);" href="#">
                                                                    <h:outputText value="#{CElabels['menu.cadastros.categoriaCliente']}"/>
                                                                </a>
                                                            </h:panelGroup>
                                                            <h:outputText styleClass="tituloCampos" id="categoriaDescritivoText" rendered="#{!LoginControle.permissaoAcessoMenuVO.categoria}"  value="#{CElabels['menu.cadastros.categoriaCliente']}"/>

                                                        </div>
                                                        <rich:spacer height="10px"/>
                                                        <h:outputText styleClass="text" value="Utilize as categorias para diferenciar seus clientes. Dessa forma � poss�vel agrupar todos aqueles que possuem caracter�sticas semelhantes, facilitando o controle e principalmente, trazendo organiza��o para os dados do seu sistema. Um exemplo pode ser por tipo de eventos."/>
                                                    </h:panelGroup>
                                                    <h:panelGroup>
                                                        <rich:spacer height="25px"/>
                                                        <div>
                                                            <h:outputText styleClass="fa-icon-pushpin textoCorAzul"/>
                                                            <rich:spacer width="5px"/>
                                                            <h:panelGroup rendered="#{LoginControle.permissaoAcessoMenuVO.cliente}">
                                                                <a class="tituloCampos" id="cliente" onclick="abrirPopup('${contextoFinan}/faces/faces/clienteCons.jsp', 'Cliente', 800, 595);" href="#">
                                                                    <h:outputText value="#{CElabels['menu.cadastros.cliente']}"/>
                                                                </a>
                                                            </h:panelGroup>
                                                            <h:outputText styleClass="tituloCampos" id="clienteDescritivoText" rendered="#{!LoginControle.permissaoAcessoMenuVO.cliente}"  value="#{CElabels['menu.cadastros.cliente']}"/>

                                                        </div>
                                                        <rich:spacer height="10px"/>
                                                        <h:outputText styleClass="text" value="Aqui voc� poder� cadastrar todos os seus clientes. Lembre-se, a qualidade de suas informa��es depende do qu�o completo est�o os dados do cliente."/>
                                                        <br />
                                                        <h:outputText styleClass="text" value="� poss�vel definir alguns campos do cadastro como obrigat�rios para garantir que os mesmos ser�o sempre preenchidos."/>
                                                    </h:panelGroup>
                                                    <h:panelGroup>
                                                        <rich:spacer height="5px"/>
                                                        <div>
                                                            <h:outputText styleClass="fa-icon-pushpin textoCorAzul"/>
                                                            <rich:spacer width="5px"/>
                                                            <h:panelGroup rendered="#{LoginControle.permissaoAcessoMenuVO.colaborador}">
                                                                <a class="tituloCampos" id="colaborador" onclick="abrirPopup('${contextoFinan}/faces/faces/colaboradorCons.jsp', 'Colaborador', 800, 595);" href="#">
                                                                    <h:outputText value="#{CElabels['menu.cadastros.colaborador']}"/>
                                                                </a>
                                                            </h:panelGroup>
                                                            <h:outputText styleClass="text" id="colaboradorDescritivoText" rendered="#{!LoginControle.permissaoAcessoMenuVO.colaborador}"  value="#{CElabels['menu.cadastros.colaborador']}"/>
                                                        </div>
                                                        <rich:spacer height="10px"/>
                                                        <h:outputText styleClass="text" value="Cadastre todos os colaboradores da empresa. Apesar de serem inclusos em uma mesma tela eles se diferencia��o pelo tipo que dever� ser definido. E no cadastro de usu�rios pode ser selecionado o colaborador j� cadastrado."/>
                                                    </h:panelGroup>

                                                    <h:panelGroup>
                                                        <div>
                                                            <h:outputText styleClass="fa-icon-pushpin textoCorAzul"/>
                                                            <rich:spacer width="5px"/>
                                                            <h:panelGroup rendered="#{LoginControle.permissaoAcessoMenuVO.grauInstrucao}">
                                                                <a class="tituloCampos" id="grauInstrucao" onclick="abrirPopup('${contextoFinan}/faces/faces/grauInstrucaoCons.jsp', '', 800, 595);" href="#">
                                                                    <h:outputText value="#{CElabels['menu.cadastros.grauInstrucao']}"/>
                                                                </a>
                                                            </h:panelGroup>
                                                            <h:outputText styleClass="text" id="grauInstrucaoDescritivoText" rendered="#{!LoginControle.permissaoAcessoMenuVO.grauInstrucao}"  value="#{CElabels['menu.cadastros.grauInstrucao']}"/>

                                                        </div>
                                                        <rich:spacer height="10px"/>
                                                        <h:outputText styleClass="text" value="Aqui � poss�vel cadastrar grau de escolaridade para utilizar nos cadastros de cliente e colaborador."/>
                                                    </h:panelGroup>

                                                    <h:panelGroup>
                                                        <rich:spacer height="25px"/>
                                                        <div>
                                                            <h:outputText styleClass="fa-icon-pushpin textoCorAzul"/>
                                                            <rich:spacer width="5px"/>
                                                            <h:panelGroup rendered="#{LoginControle.permissaoAcessoMenuVO.profissao}">
                                                                <a class="tituloCampos" id="tabelaPrecos" onclick="abrirPopup('${contextoFinan}/faces/faces/profissaoCons.jsp', 'Profiss�o', 800, 595);" href="#">
                                                                    <h:outputText value="#{CElabels['menu.cadastros.profissao']}"/>
                                                                </a>
                                                            </h:panelGroup>
                                                            <h:outputText styleClass="text" id="profissaoDescritivoText" rendered="#{!LoginControle.permissaoAcessoMenuVO.profissao}"  value="#{CElabels['menu.cadastros.profissao']}"/>
                                                        </div>
                                                        <rich:spacer height="10px"/>
                                                        <h:outputText styleClass="text" value="Mantenha seu cadastro de profiss�o completo para que no momento de cadastrar novos clientes ou colaboradores esta op��o seja preenchida."/>
                                                    </h:panelGroup>

                                                    <h:panelGroup>
                                                        <rich:spacer height="25px"/>
                                                        <div>
                                                            <h:outputText styleClass="fa-icon-pushpin textoCorAzul"/>
                                                            <rich:spacer width="5px"/>
                                                            <h:panelGroup rendered="#{LoginControle.permissaoAcessoMenuVO.pais}">
                                                                <a class="tituloCampos" id="pais" onclick="abrirPopup('${contextoFinan}/faces/faces/paisCons.jsp', 'Pa�s', 800, 595);" href="#">
                                                                    <h:outputText value="#{CElabels['menu.cadastros.pais']}"/>
                                                                </a>
                                                            </h:panelGroup>
                                                            <h:outputText styleClass="text" id="paisDescritivoText" rendered="#{!LoginControle.permissaoAcessoMenuVO.pais}"  value="#{CElabels['menu.cadastros.pais']}"/>
                                                        </div>
                                                        <rich:spacer height="10px"/>
                                                        <h:outputText styleClass="text" value="Se necess�rio pode ser cadastrado novos Pa�ses para manter seu cadastro o mais completo poss�vel. Podendo haver tamb�m clientes de outros pa�ses."/>
                                                    </h:panelGroup>

                                                    <h:panelGroup>
                                                        <rich:spacer height="25px"/>
                                                        <div>
                                                            <h:outputText styleClass="fa-icon-pushpin textoCorAzul"/>
                                                            <rich:spacer width="5px"/>
                                                            <h:panelGroup rendered="#{LoginControle.permissaoAcessoMenuVO.cidade}">
                                                                <a class="tituloCampos" id="cidade" onclick="abrirPopup('${contextoFinan}/faces/faces/cidadeCons.jsp', 'Cidade', 800, 595);" href="#">
                                                                    <h:outputText value="#{CElabels['menu.cadastros.cidade']}"/>
                                                                </a>
                                                            </h:panelGroup>
                                                            <h:outputText styleClass="text" id="cidadeDescritivoText" rendered="#{!LoginControle.permissaoAcessoMenuVO.cidade}"  value="#{CElabels['menu.cadastros.cidade']}"/>
                                                        </div>
                                                        <rich:spacer height="10px"/>
                                                        <h:outputText styleClass="text" value="Efetue o cadastro de cidades, fazendo isso facilitar� o cadastro de clientes, e nos pr�ximos cadastros basta selecionar a cidade dentro de uma lista de cidades cadastradas. Esta op��o � muito �til quando n�o souber o CEP."/>
                                                    </h:panelGroup>

                                                </h:panelGrid>

                                            </h:panelGrid>



                                        </h:panelGrid>
                                    </h:panelGroup>
                                </h:panelGroup>
                            </h:panelGroup>

                            <jsp:include page="../../../include_box_menulateral.jsp" flush="true">
                                <jsp:param name="menu" value="FIN-CADASTROS-AUXILIARES" />
                            </jsp:include>

                        </h:panelGroup>
                    </h:panelGroup>
                </h:panelGroup>

                <jsp:include page="../../../include_rodape_flat.jsp" flush="true" />
            </h:panelGroup>
            </body>
        </html>
    </h:form>
</f:view>
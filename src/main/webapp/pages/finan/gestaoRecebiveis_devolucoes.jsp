<%@include file="includes/imports.jsp" %>
<h:panelGroup rendered="#{GestaoRecebiveisControle.formaPagamentoSelecionada.tipoFormaPagamento == 'DEVOLUCOES'}">
    <script src="../../beta/js/datatables1.10.12.min.js" type="text/javascript"></script>

    <table class="tabelaDados tudo step3" id="iddevolucoes" style="width:100%; margin-left:0px; ">
        <thead>
            <th>${msg_aplic.prt_Finan_Lancamentos_favorecido}</th>
            <th>${msg_aplic.prt_Finan_Lancamentos_dataVencimento}</th>
            <th>${msg_aplic.prt_Finan_Lancamentos_descricao}</th>
            <th class="hiden"></th>
            <th>${msg_aplic.prt_Finan_Lancamentos_valor}</th>
            <th>${msg_aplic.prt_Finan_Lancamentos_operacoes}</th>
        </thead>
        <a4j:repeat value="#{GestaoRecebiveisControle.devolucoes}"
                   var="movConta">
            <tr>
                <td>
                    <a4j:commandLink styleClass="texto-cor-azul linkPadrao"
                                     onclick="abrirPopup('../../clienteNav.jsp?page=viewCliente&matricula=#{movConta.matricula}', 'Cliente', 1024, 700);"
                                     value="#{movConta.pessoaVO.nomeMinusculo}">
                    </a4j:commandLink>
                </td>
                <td>
                    <h:outputText value="#{movConta.dataVencimento_Apresentar}"/>
                </td>
                <td>
                    <h:outputText value="#{movConta.descricao}"/>
                </td>
                <td class="hiden">
                    <h:outputText value="#{movConta.valor}"/>
                </td>
                <td>
                    <h:outputText value="#{movConta.valor_Apresentar}" style="float: right; margin-right: 10px;"/>
                </td>
                <td>
                    <a4j:commandLink value="#{movConta.tituloLinkQuitar}"
                                     rendered="#{movConta.pagar_Apresentar && !movConta.tipoRecebivel}"
                                     reRender="modalPanelLancarPagamento,panelAutorizacaoFuncionalidade"
                                     action="#{MovContaControle.selecionarLancamentoQuitacao}"
                                     styleClass="texto-cor-azul linkPadrao"
                                     oncomplete="#{MovContaControle.abrirFecharModalPagto}"></a4j:commandLink>
                </td>
            </tr>
        </a4j:repeat>
    </table>
            
            <script>
                    jQuery('#iddevolucoes').DataTable({
                        "bPaginate":   false,
                        "bInfo": false,
                        "aoColumnDefs": [
                            {'bVisible': false, 'aTargets': ['hiden'] }
                        ]
                    });
                </script>       
</h:panelGroup>

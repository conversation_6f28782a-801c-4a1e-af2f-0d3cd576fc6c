<%@page contentType="text/html" pageEncoding="ISO-8859-1"%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@include file="includes/include_imports.jsp" %>
<%@taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>

<link href="../../css_pacto.css" rel="stylesheet" type="text/css">
<link href="../../css/financeiro.css" rel="stylesheet" type="text/css">
<link href="../../css/otimize.css" rel="stylesheet" type="text/css">
<link href="../../css/jquery.treeTable.css" rel="stylesheet" type="text/css">

<%--<script type="text/javascript" src="../../script/jquery.js"></script>--%>
<script type="text/javascript" language="javascript" src="../../bootstrap/jquery.js"></script>
<script type="text/javascript" src="../../script/jquery.treeTable.js"></script>
<script type="text/javascript" src="../../script/demonstrativoFinan.js"></script>

<style type="text/css">
    /* Let's get this party started */
    ::-webkit-scrollbar {
        width: 12px;
    }

    /* Track */
    ::-webkit-scrollbar-track {
        -webkit-box-shadow: inset 0 0 9px rgba(0,0,0,0.3);
        -webkit-border-radius: 10px;
        border-radius: 10px;
    }

    /* Handle */
    ::-webkit-scrollbar-thumb {
        -webkit-border-radius: 7px;
        border-radius: 7px;
        background:rgba(88, 87, 80, 0.63);
        -webkit-box-shadow: inset 0 0 9px rgba(0,0,0,0.5);
    }
    ::-webkit-scrollbar-thumb:window-inactive {
        background: rgba(8, 28, 25, 0.4);
    }
    .wrapper1, .wrapper2{width: 1045px;
        overflow-x: scroll;}

</style>

<f:view>
    <html>

        <!-- Inclui o elemento HEAD da página -->
        <head>
            <%@include file="includes/include_head_finan.jsp" %>
            <script type="text/javascript" language="javascript" src="${contextoFinan}/script/telaInicial.js"></script>
            <script type="text/javascript" language="javascript">
              $.noConflict();

            </script>
        </head>
        <body onload="atualizarTreeViewDF()" >
        <h:panelGroup layout="block" style="display: inline-table;width: 100%;" styleClass="fundoCinza">
            <h:form id="formTopo" style="overflow-x: visible !important;">
                <h:panelGroup layout="block" styleClass="bgtop topoZW" rendered="#{MenuControle.apresentarTopo}">
                    <jsp:include page="../../include_topo_novo.jsp" flush="true"/>
                    <jsp:include page="../../include_menu_fin_flat.jsp" flush="true"/>
                    <rich:jQuery selector=".item3" query="addClass('menuItemAtual')"/>
                </h:panelGroup>
            </h:form>
            <h:panelGroup layout="block" styleClass="caixaCorpo">
                <h:panelGroup layout="block" style="height: 80%;width: 100%">
                    <h:panelGroup layout="block" styleClass="caixaMenuLatel">

                        <h:panelGroup layout="block" styleClass="container-imagem container-conteudo-central">
                            <h:panelGroup layout="block" styleClass="container-box zw_ui especial ">

                                <h:panelGroup styleClass="container-box-header" layout="block">
                                    <h:panelGroup layout="block" styleClass="margin-box">
                                        <h:outputText styleClass="container-header-titulo" value="Demonstração do resultado do exercício"/>
                                        <h:outputLink styleClass="linkWiki"
                                                      value="#{SuperControle.urlBaseConhecimento}o-que-e-dre-e-para-que-serve/"
                                                      title="Clique e saiba mais: Demonstrativo Financeiro"
                                                      target="_blank" >
                                            <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                                        </h:outputLink>
                                    </h:panelGroup>
                                </h:panelGroup>
                                <h:panelGroup layout="block" styleClass="margin-box">
                                   <h:form id="formDRE">
                                                        <!-- ------------------------ CONSULTA   ------------------------------- -->
                                                        <div id="CExpandido"
                                                             class="Passo"
                                                             onclick="esconderMostrar('consulta', true, 'CExpandido','CRetraido', 'formDRE:CControle');"
                                                             style="cursor: pointer; background-image: url(../../imagens/folder/Passo1.jpg); background-repeat: no-repeat;">
                                                            <div class="Imagem">
                                                                <img id="ctl00_cph_imgPasso1"
                                                                     src="../../imagens/folder/PassoExpandir.jpg"
                                                                     style="border-top-width: 0px; border-right-width: 0px; border-bottom-width: 0px; border-left-width: 0px;"
                                                                     title="Ocultar" />
                                                            </div>
                                                            <div class="Texto">Tipo Consulta</div>
                                                            <div class="Info">
                                                                <span id="ctl00_cph_lblPasso1">Ocultar</span>
                                                            </div>
                                                        </div>
                                                        <div id="CRetraido"
                                                             class="Passo"
                                                             onclick="esconderMostrar('consulta', false, 'CRetraido','CExpandido', 'formDRE:CControle');"
                                                             style="display: none; cursor: pointer; background-image: url(../../imagens/folder/Passo1.jpg); background-repeat: no-repeat;">
                                                            <div class="Imagem">
                                                                <img id="ctl00_cph_imgPasso1"
                                                                     src="../../imagens/folder/PassoOcultar.jpg"
                                                                     style="border-top-width: 0px; border-right-width: 0px; border-bottom-width: 0px; border-left-width: 0px;"
                                                                     title="Ocultar" />
                                                            </div>
                                                            <div class="Texto">Tipo Consulta</div>
                                                            <div class="Info">
                                                                <span id="ctl00_cph_lblPasso1">Expandir</span>
                                                            </div>
                                                        </div>

                                                        <table bgcolor="#F5F5F5" id="consulta" width="100%"
                                                               style="display: block;">
                                                            <tr>
                                                                <td>
                                                                    <h:panelGroup id="panelTipoRel" >
                                                                        <h:panelGroup  styleClass="panelLinkAbrirMenuTipoRel"  >
                                                                            <h:panelGrid columns="2" styleClass="maginTipoRel">
                                                                                    <h:panelGroup layout="block"  >
                                                                                        <h:outputText styleClass="tituloDemonstrativo"
                                                                                          value="Tipo: " />
                                                                                    </h:panelGroup>
                                                                                    <h:panelGroup styleClass="panelLikeSelectMenu" layout="block"  >
                                                                                        <h:outputText style="font-weight: bold" styleClass="tituloDemonstrativo" value=" #{DREControle.tipoRelatorioDf.descricao}: "/>
                                                                                        <h:outputText styleClass="tituloDemonstrativo" value="#{DREControle.tipoRelatorioDf.descricaoAuxiliar}"/>
                                                                                        <h:outputText style="font-family: Arial" styleClass="fa-icon-sort-down texto-cor-azul texto-font texto-size-16" />
                                                                                    </h:panelGroup>
                                                                            </h:panelGrid>
                                                                         </h:panelGroup>
                                                                        <a4j:commandLink styleClass="linkAbrirMenuTipoRel" style="display: none;" oncomplete="Richfaces.showModalPanel('formTiposRE:modalTiposRelatorio');" />

                                                                        <rich:jQuery query="click(function(){jQuery('.linkAbrirMenuTipoRel').click();});" selector=".panelLinkAbrirMenuTipoRel"/>
                                                                    </h:panelGroup>
                                                                 </td>
                                                            </tr>
                                                        </table>

                                                        <!-- ------------------------ tipo visualização  ------------------------------- -->
                                                        <div id="OCExpandido"
                                                             class="Passo"
                                                             onclick="esconderMostrar('TVconsulta', true, 'OCExpandido','OCRetraido', 'formDRE:OCControle');"
                                                             style="cursor: pointer; background-image: url(../../imagens/folder/Passo2.jpg); background-repeat: no-repeat;">
                                                            <div class="Imagem">
                                                                <img id="ctl00_cph_imgPasso1"
                                                                     src="../../imagens/folder/PassoExpandir.jpg"
                                                                     style="border-top-width: 0px; border-right-width: 0px; border-bottom-width: 0px; border-left-width: 0px;"
                                                                     title="Ocultar" />
                                                            </div>
                                                            <div class="Texto">Tipo Visualização</div>
                                                            <div class="Info">
                                                                <span id="ctl00_cph_lblPasso1">Ocultar</span>
                                                            </div>
                                                        </div>
                                                        <div id="OCRetraido"
                                                             class="Passo"
                                                             onclick="esconderMostrar('TVconsulta', false, 'OCRetraido','OCExpandido', 'formDRE:OCControle' );"
                                                             style="display: none; cursor: pointer; background-image: url(../../imagens/folder/Passo2.jpg); background-repeat: no-repeat;">
                                                            <div class="Imagem">
                                                                <img id="ctl00_cph_imgPasso1"
                                                                     src="../../imagens/folder/PassoOcultar.jpg"
                                                                     style="border-top-width: 0px; border-right-width: 0px; border-bottom-width: 0px; border-left-width: 0px;"
                                                                     title="Ocultar" />
                                                            </div>
                                                            <div class="Texto">Tipo Visualização</div>
                                                            <div class="Info">
                                                                <span id="ctl00_cph_lblPasso1">Expandir</span>
                                                            </div>
                                                        </div>


                                                        <table bgcolor="#F5F5F5" id="TVconsulta" width="100%"
                                                               style="display: block;">
                                                            <tr>
                                                                <td>
                                                                    <h:outputText styleClass="tituloDemonstrativo" value="#{msg_aplic.prt_Finan_dreCentroCustos}:"/>
                                                                    <h:selectBooleanCheckbox style="vertical-align: middle" id="chkMostraCentroCustos" onclick="alterarLabelAgruparMM()" value="#{DREControle.centroCustos}"/>
                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <td>
                                                                    <h:panelGroup>
                                                                        <h:outputText id="outDreAgruparProdutoMM" value="Agrupar valor da manutenção de modalidades ao centro de custos das modalidades equivalentes:" styleClass="tituloDemonstrativo"/>
                                                                        <h:selectBooleanCheckbox style="vertical-align: middle" id="chAgruparProdutoMM" value="#{DREControle.agruparValorProdutoMMasModalidades}"/>

                                                                    </h:panelGroup>
                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <td>
                                                                    <h:panelGrid id="apresentarDevolucoesDRE">
                                                                        <h:panelGroup
                                                                                rendered="#{DREControle.tipoRelatorioDf.codigo == 3}">
                                                                            <h:outputText
                                                                                    value="Mostrar pagamento de cheques devolvidos:"
                                                                                    styleClass="tituloDemonstrativo"/>
                                                                            <h:selectBooleanCheckbox style="vertical-align: middle"
                                                                                                     value="#{DREControle.apresentarDevolucoesRel}"/>
                                                                        </h:panelGroup>
                                                                    </h:panelGrid>
                                                                </td>
                                                            </tr>
                                                        </table>

                                                        <a name="posicaoDados"></a>

                                       <!-- ------------------------ Inicio filtros de Centro de Custo e Plano de Contas   ------------------------------- -->
                                       <div id="FMRetraidoDRE"
                                            class="Passo"
                                            onclick="esconderMostrar('divFiltros', false, 'FMRetraidoDRE', 'FMExpandidoDRE', 'formDRE:OCControle');"
                                            onclick="esconderMostrar('consulta', true, 'CExpandido','CRetraido', 'formDRE:CControle');"
                                            style=" display: block; cursor: pointer; background-image: url(../../imagens/folder/Passo3.jpg); background-repeat: no-repeat;">
                                           <div class="Imagem">
                                               <img id="ctl00_cph_imgPasso1"
                                                    src="../../imagens/folder/PassoOcultar.jpg"
                                                    style="border-top-width: 0px; border-right-width: 0px; border-bottom-width: 0px; border-left-width: 0px;"
                                                    title="Ocultar" />
                                           </div>
                                           <div class="Texto">Filtros</div>
                                           <div class="Info">
                                               <span>Expandir</span>
                                           </div>
                                       </div>
                                       <div id="FMExpandidoDRE"
                                            class="Passo"
                                            onclick="esconderMostrar('divFiltros', true, 'FMExpandidoDRE', 'FMRetraidoDRE', 'formDRE:OCControle');"
                                            style="display: none; cursor: pointer; background-image: url(../../imagens/folder/Passo3.jpg); background-repeat: no-repeat;">
                                           <div class="Imagem">
                                               <img id="ctl00_cph_imgPasso1"
                                                    src="../../imagens/folder/PassoExpandir.jpg"
                                                    style="border-top-width: 0px; border-right-width: 0px; border-bottom-width: 0px; border-left-width: 0px;"
                                                    title="Ocultar" />
                                           </div>
                                           <div class="Texto">Filtros</div>
                                           <div class="Info">
                                               <span>Ocultar</span>
                                           </div>
                                       </div>


                                       <%@include file="includes/include_filtrosDRE.jsp"%>

                                       <%--<script>--%>
                                       <%--window.location.hash = 'posicaoDados';--%>
                                       <%--</script>--%>
                                       <!-- ------------------------ Fim filtros de Centro de Custo e Plano de Contas   ------------------------------- -->
                                                        <!-- ------------------------ Intervalo e Resultado da Consulta   ------------------------------- -->
                                                        <div id="RCExpandido"
                                                             class="Passo"
                                                             onclick="esconderMostrar('rConsulta', true, 'RCExpandido','RCRetraido','formDRE:RCControle');"
                                                             style="cursor: pointer; background-image: url(../../imagens/folder/Passo4.jpg); background-repeat: no-repeat;">
                                                            <div class="Imagem">
                                                                <img id="ctl00_cph_imgPasso1"
                                                                     src="../../imagens/folder/PassoExpandir.jpg"
                                                                     style="border-top-width: 0px; border-right-width: 0px; border-bottom-width: 0px; border-left-width: 0px;"
                                                                     title="Ocultar" />
                                                            </div>
                                                            <div class="Texto">Intervalo da Consulta</div>
                                                            <div class="Info">
                                                                <span>Ocultar</span>
                                                            </div>
                                                        </div>
                                                        <div id="RCRetraido"
                                                             class="Passo"
                                                             onclick="esconderMostrar('rConsulta', false, 'RCRetraido','RCExpandido','formDRE:RCControle');"
                                                             style="display: none; cursor: pointer; background-image: url(../../imagens/folder/Passo4.jpg); background-repeat: no-repeat;">
                                                            <div class="Imagem">
                                                                <img id="ctl00_cph_imgPasso1"
                                                                     src="../../imagens/folder/PassoOcultar.jpg"
                                                                     style="border-top-width: 0px; border-right-width: 0px; border-bottom-width: 0px; border-left-width: 0px;"
                                                                     title="Ocultar" />
                                                            </div>
                                                            <div class="Texto">Intervalo da Consulta</div>
                                                            <div class="Info">
                                                                <span>Expandir</span>
                                                            </div>
                                                        </div>

                                                        <table bgcolor="#F5F5F5" id="rConsulta" width="100%"
                                                               style="display: block;">
                                                            <tr>
                                                                <td width="100%">
                                                                    <h:panelGrid id="painelDatas"

                                                                                 columns="1" rowClasses="linhaPar">
                                                                        <h:panelGroup>
                                                                            <h:outputText styleClass="tituloDemonstrativo"
                                                                                          value="Período: " />

                                                                            <h:panelGroup id="panelGroupDatas" >
                                                                                <h:panelGroup>
                                                                                    <rich:calendar id="dataInicioRelatorio"
                                                                                                   showWeekDaysBar="false"
                                                                                                   inputSize="#{DREControle.inputSize}"
                                                                                                   inputClass="form"
                                                                                                   showWeeksBar="false"
                                                                                                   oninputfocus="focusinput(this);"
                                                                                                   oninputblur="blurinput(this);"
                                                                                                   oninputchange="return validar_Data(this.id);"
                                                                                                   oninputkeypress="#{DREControle.oninputkeypress}"
                                                                                                   datePattern="#{DREControle.datePattern}"
                                                                                                   enableManualInput="true"
                                                                                                   value="#{DREControle.dataInicio}">
                                                                                    </rich:calendar>
                                                                                    <h:message for="dataInicioRelatorio" styleClass="mensagemDetalhada" />


                                                                                </h:panelGroup>
                                                                                <rich:spacer width="3"></rich:spacer>
                                                                                <h:outputText styleClass="tituloDemonstrativo"
                                                                                              value="#{msg_aplic.prt_ate} " />
                                                                                <h:panelGroup>

                                                                                    <rich:calendar
                                                                                            id="dataFimRelatorio"
                                                                                            showWeekDaysBar="false"
                                                                                            inputSize="#{DREControle.inputSize}"
                                                                                            inputClass="form"
                                                                                            showWeeksBar="false"
                                                                                            oninputkeypress="#{DREControle.oninputkeypress}"
                                                                                            oninputchange="return validar_Data(this.id);"
                                                                                            datePattern="#{DREControle.datePattern}"
                                                                                            enableManualInput="true"
                                                                                            value="#{DREControle.dataFim}">
                                                                                    </rich:calendar>
                                                                                    <h:message for="dataFimRelatorio" styleClass="mensagemDetalhada" />

                                                                                </h:panelGroup>

                                                                            </h:panelGroup>
                                                                            <rich:spacer width="8"></rich:spacer>
                                                                            <h:panelGroup id="panelEmpresa"
                                                                                          rendered="#{DREControle.mostrarCampoEmpresa}">
                                                                                <h:outputText styleClass="tituloDemonstrativo"
                                                                                              value="Empresa: "></h:outputText>

                                                                                <h:selectOneMenu id="empresa" styleClass="form"
                                                                                                 onblur="blurinput(this);" onfocus="focusinput(this);"
                                                                                                 value="#{DREControle.empresaVO.codigo}">
                                                                                    <f:selectItems
                                                                                            value="#{DREControle.listaSelectItemEmpresa}" />
                                                                                    <a4j:support action="#{DREControle.obterEmpresaEscolhida}" event="onchange"/>
                                                                                </h:selectOneMenu>
                                                                            </h:panelGroup>
                                                                            <rich:spacer width="8"></rich:spacer>

                                                                            <h:panelGroup id="panelFonteDadosDF">
                                                                                <h:outputText styleClass="tituloDemonstrativo"
                                                                                              value="Fonte de Dados: "></h:outputText>

                                                                                <h:selectOneMenu id="fonteDadosDF" styleClass="form"
                                                                                                 onblur="blurinput(this);" onfocus="focusinput(this);"
                                                                                                 value="#{DREControle.tipoFonteDadosDF}">
                                                                                    <f:selectItems
                                                                                            value="#{DREControle.listaFonteDados}" />
                                                                                </h:selectOneMenu>
                                                                            </h:panelGroup>
                                                                            <rich:spacer width="8"/>
                                                                            <h:panelGroup>
                                                                                <a4j:commandButton
                                                                                        action="#{DREControle.gerarDemonstrativoComThread}"
                                                                                        value="Visualizar Relatório"
                                                                                        style="vertical-align:middle"
                                                                                        image="../../imagens/btn_VisualizarRelatorio.png"
                                                                                        reRender="panelDfTreeView,panelImprimirRelatorio,panelScroll, panelMensagens, panelTreeViewDF, panelImprimirRelatorio, panelMensagens2, painelDatas"
                                                                                        oncomplete="atualizarTreeViewDF();alterarAlturaMenuLateral();"
                                                                                        id="demonstrativoFinanComThread" />
                                                                            </h:panelGroup>
                                                                        </h:panelGroup>

                                                                    </h:panelGrid> <h:panelGrid width="100%" id="panelDfTreeView">
                                                                    <h:panelGroup id="panelMostrarRelatorio"
                                                                                  rendered="#{!DREControle.houveErroNoRelatorio}">
                                                                        <c:set var="tamColNomeAgrupador" value="25%" scope="request" />
                                                                        <c:set var="tamColNomeMes" value="10%" scope="request" />



                                                                    </h:panelGroup>


                                                                </h:panelGrid></td>
                                                            </tr>
                                                        </table>
                                                        <h:panelGroup id="panelImprimirRelatorio" >
                                                            <h:panelGroup >
                                                                <c:if test="${not empty DREControle.listaDFBrowser}">
                                                                    <br>
                                                                    <div style="text-align: left;">
                                                                        <a4j:commandButton id="btnExcel"
                                                                                           image="../../imagens/btn_excel.png"
                                                                                           oncomplete="abrirPopup('../../UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/vnd.ms-excel','Transacoes', 800,200);#{ExportadorListaControle.msgAlert}"
                                                                                           action="#{DREControle.exportarExcel}" />
                                                                        <rich:spacer width="3"/>
                                                                        <a4j:commandButton oncomplete="abrirPopup('relatorioDRE.jsp', 'RelatórioDRE', 780, 595);"
                                                                                           image="../../imagens/btn_VisualizarImpressao.png"	value="Visualizar Impressão">
                                                                        </a4j:commandButton>


                                                                    </div>
                                                                </c:if>
                                                            </h:panelGroup>
                                                        </h:panelGroup>

                                                        <h:panelGrid id="panelMensagens2" columns="1" width="100%">
                                                            <h:outputText styleClass="mensagemDetalhada" id="mensagemDetalhada3"
                                                                          value="#{DREControle.mensagemDetalhada}"/>
                                                        </h:panelGrid>

                                                        <h:panelGroup id="panelScroll">
                                                            <c:if test="${not empty DREControle.listaDFBrowser}">
                                                                    <div id="divRelatorio" class="wrapper2 scrollbar" style="width: 100%;height: 100%; overflow: auto;">
                                                                        <div>

                                                                            <!-- ------------------------ Começo relatório TreeView  ------------------------------- -->
                                                                            <%@include
                                                                                    file="includes/include_TreeViewDRE.jsp" %>
                                                                            <!-- ------------------------ Fim relatório TreeView  ------------------------------- -->
                                                                        </div>
                                                                    </div>
                                                            </c:if>
                                                        </h:panelGroup>
                                                        <h:inputHidden id="idMesSelecionado"
                                                                       value="#{DREControle.mesSelecionado}" />
                                                        <h:inputHidden id="idCodigoSelecionado"
                                                                       value="#{DREControle.codigoAgrupadorSelecionado}" />
                                                        <h:inputHidden id="idTipoListaMostrar"
                                                                       value="#{DREControle.tipoListaLancamentosMostrar}" />
                                                        <a4j:commandButton style="visibility: hidden;"
                                                                           oncomplete="Richfaces.showModalPanel('modalLancamentos')"
                                                                           id="botaoVisualizarLancamentos" reRender="modalLancamentos"
                                                                           action="#{DREControle.visualizarLancamentosMes}">
                                                        </a4j:commandButton>

                                                        <h:inputHidden id="idCentro"
                                                                       value="#{DREControle.codigoCentro}" />
                                                        <h:inputHidden id="idPlano"
                                                                       value="#{DREControle.codigoPlano}" />
                                                        <a4j:commandButton style="visibility: hidden;"
                                                                           oncomplete="Richfaces.showModalPanel('modalLancamentos')"
                                                                           id="botaoVisualizarLancamentosDRE" reRender="modalLancamentos"
                                                                           action="#{DREControle.visualizarLancamentos}">
                                                        </a4j:commandButton>

                                                        <h:inputHidden id="CControle"
                                                                       value="#{DREControle.openConsulta}" />
                                                        <h:inputHidden id="OCControle"
                                                                       value="#{DREControle.openOpcoes}" />
                                                        <h:inputHidden id="FMControle"
                                                                       value="#{DREControle.openFiltrosMemoria}" />
                                                        <h:inputHidden id="RCControle"
                                                                       value="#{DREControle.openIntervalo}" />




                                                        <h:panelGrid id="panelMensagens" columns="1" width="100%"
                                                                     styleClass="tabMensagens">
                                                            <h:outputText styleClass="mensagem" id="mensagem2"
                                                                          value="#{DREControle.mensagem}" />
                                                            <h:outputText styleClass="mensagemDetalhada"
                                                                          id="mensagemDetalhada2"
                                                                          value="#{DREControle.mensagemDetalhada}" />
                                                        </h:panelGrid>
                                                    </h:form>
                                </h:panelGroup>
                            </h:panelGroup>

                        </h:panelGroup>
                        <h:form id="form" style="display: flex;align-items: stretch;flex-direction: row-reverse;">
                            <jsp:include page="includes/include_menu_relatorios.jsp" flush="true"/>
                        </h:form>
                    </h:panelGroup>
                </h:panelGroup>
            </h:panelGroup>

            <jsp:include page="../../include_rodape_flat.jsp" flush="true" />
        </h:panelGroup>
        <script type="text/javascript">
            alterarLabelAgruparMM();
        </script>

       </body>
    </html>
    <a4j:outputPanel id="outTiposRel">
        <a4j:form id="formTiposRE">


            <rich:modalPanel styleClass="novaModal" id="modalTiposRelatorio" autosized="true"
                             shadowOpacity="true" width="800" height="250">
                <f:facet name="header">
                    <h:panelGroup>
                        <h:outputText  value="Selecione o Tipo de Consulta"></h:outputText>
                    </h:panelGroup>
                </f:facet>
                <f:facet name="controls">
                    <h:panelGroup>
                        <h:graphicImage value="/imagens/close.png" style="cursor:pointer"
                                        id="hidelinkTiposRel" />
                        <rich:componentControl for="modalTiposRelatorio"
                                               attachTo="hidelinkTiposRel" operation="hide" event="onclick" />
                    </h:panelGroup>
                </f:facet>


                    <h:panelGroup layout="block" style="margin-top:10px;margin-bottom: 10px;VERTICAL-ALIGN: middle;" id="tabAutorizacao"
                                  styleClass="tabelaSimplesCustom grupoRadioButton border-color-c2">
                        <a4j:repeat value="#{DREControle.tiposRelatorios}" rowKeyVar="status" var="tipoRel">
                            <h:panelGroup layout="block"   styleClass="tipoRelatorioDF tipoRelatorioCodigo#{tipoRel.value.codigo}"  >
                                <div class="tooltipster" title="Clique para selecionar">
                                    <h:panelGroup layout="block" styleClass="maginTipoRel">
                                        <h:panelGroup layout="block" style="width: 100%" >
                                            <h:panelGroup layout="block"  style="display: inline-block;">
                                                <h:outputText style="font-family: Arial" styleClass="fa-icon-check-empty texto-cor-azul texto-font texto-size-16" />
                                                <h:outputText style="font-weight: bold" styleClass="texto-font texto-size-18" value=" #{tipoRel.value.descricao}: "/>
                                                <h:outputText styleClass="texto-font texto-size-16" value="#{tipoRel.value.descricaoAuxiliar}"/>
                                            </h:panelGroup>
                                        </h:panelGroup>
                                    </h:panelGroup>
                                </div>
                            </h:panelGroup>
                            <a4j:commandLink styleClass="tipoRelatorioCodigolink#{tipoRel.value.codigo}" style="display: none;" action="#{DREControle.selecionarTipoRelatorio}"  oncomplete="Richfaces.hideModalPanel('modalTiposRelatorio')" reRender="painelDatas,panelTipoRel,apresentarDevolucoesDRE" />

                            <rich:jQuery query="click(function(){jQuery('.tipoRelatorioCodigolink#{tipoRel.value.codigo}').click();});" selector=".tipoRelatorioCodigo#{tipoRel.value.codigo}"/>
                        </a4j:repeat>

                    </h:panelGroup>


            </rich:modalPanel>

        </a4j:form>

    </a4j:outputPanel>

    <%@include file="../../includes/include_carregando_ripple.jsp" %>
    <%@include file="includes/include_modalLancamentosDF.jsp" %>
    <%@include file="includes/include_modal_consultarCaixa.jsp" %>

</f:view>


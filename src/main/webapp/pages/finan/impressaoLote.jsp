<%-- INICIO DADOS LOTE --%>
<%@include file="includes/include_imports.jsp" %>

<f:view>
    <jsp:include page="/includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText value="#{msg_aplic.prt_Finan_GestaoLotes_tituloForm}"/>
    </title>

 	<%@include file="includes/include_operacaoConta.jsp" %>
    <a4j:loadScript src="/script/jquery.maskedinput-1.2.2.js" />
    <h:form id="form">
        <html>
            <head>
                <%@include file="includes/include_head_finan.jsp" %>
                <script type="text/javascript" language="javascript" src="${contextoFinan}/script/telaInicial.js"></script>
            </head>
            <body style="background-color: #FFFFFF !important">
                                                        <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar"
                                                                     columnClasses="classEsquerda, classDireita" width="100%">
                                                            <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Finan_Lancamentos_empresa}"
                                                                          rendered="#{GestaoLotesControle.lote.novoObj}"/>
                                                            <h:selectOneMenu id="empresa" styleClass="form" onblur="blurinput(this);"
                                                                             onfocus="focusinput(this);" rendered="#{GestaoLotesControle.lote.novoObj}"
                                                                             value="#{GestaoLotesControle.lote.empresa.codigo}" >
                                                                <f:selectItems value="#{GestaoLotesControle.listaSelectItemEmpresa}" />
                                                            </h:selectOneMenu>
                                                            <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Finan_GestaoLotes_codigo}" />
                                                            <h:inputText id="codigo" size="10" maxlength="10" onblur="blurinput(this);" readonly="true"
                                                                         onfocus="focusinput(this);" styleClass="form" value="#{GestaoLotesControle.lote.codigo}" />
                                                            <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Finan_GestaoLotes_descricao}" />
                                                            <h:panelGroup>
                                                                <h:inputText id="descricao" size="40" maxlength="50" onblur="blurinput(this);"
                                                                             onfocus="focusinput(this);" styleClass="form" value="#{GestaoLotesControle.lote.descricao}" />
                                                            </h:panelGroup>
                                                            <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Finan_GestaoRecebiveis_dataDeposito}" />
                                                            <h:panelGroup>
                                                                <rich:calendar id="dataInicioD"
                                                                               value="#{GestaoLotesControle.lote.dataDeposito}"
                                                                               inputSize="10"
                                                                               inputClass="form"
                                                                               oninputblur="blurinput(this);"
                                                                               oninputfocus="focusinput(this);"
                                                                               datePattern="dd/MM/yyyy"
                                                                               oninputchange="return validar_Data(this.id);"                                                                        enableManualInput="true"
                                                                               zindex="2"
                                                                               showWeeksBar="false"/>
                                                                <rich:jQuery id="mskData" selector=".rich-calendar-input" timing="onload" query="mask('99/99/9999')" />
                                                            </h:panelGroup>
                                                             <h:outputText styleClass="tituloCampos" rendered="#{GestaoLotesControle.lotePagaMovConta}"
                                                                           value="#{GestaoLotesControle.labelContasQueLotePaga}" />
                                                            <h:outputText styleClass="tituloCampos" rendered="#{GestaoLotesControle.lotePagaMovConta}"
                                                                      value="#{GestaoLotesControle.contasQueLotePaga}" />

                                                            
                                                        </h:panelGrid>

                                                        <%-- FIM DADOS LOTE --%>

                                                        <div class="sep" style="margin:10px 0 10px 0;"></div>

                                                        <rich:togglePanel id="listas" switchType="ajax" stateOrder="closed, cheque, cartao"
                                                                          value="#{GestaoLotesControle.escolha}">

                                                            <f:facet name="closed">
                                                                <h:panelGrid columns="1" width="100%" columnClasses="centralizado">
                                                                    <h:panelGroup>
                                                                    <a4j:commandButton id="voltar1" image="/imagens/Voltar_finan.png" 
                                                                                           action="#{GestaoLotesControle.voltarGestao}" title="Voltar para consulta de lotes"/>
                                                                        <rich:spacer width="10"/>                   
                                                                        <a4j:commandButton reRender="listas, panelMensagem, formConsultaCheque" styleClass="botoes" 
                                                                        				   action="#{GestaoLotesControle.incluirLote}"
                                                                                           oncomplete="#{GestaoLotesControle.abrirModalCheque}" 
                                                                                           image="/imagens/Adicionar_Cheque.png" title="Adicionar Cheques"/>
                                                                        <rich:spacer width="10"/>
                                                                        <a4j:commandButton reRender="listas, panelMensagem, formConsultaCartao" styleClass="botoes" 
                                                                                           action="#{GestaoLotesControle.incluirLote}"
                                                                                           oncomplete="#{GestaoLotesControle.abrirModalCartao}" 
                                                                                           image="/imagens/Adicionar_Cartao.png" title="Adicionar Cart�es de Cr�dito"/>
                                                                        
                                                                        
                                                                    </h:panelGroup>
                                                                </h:panelGrid>
                                                            </f:facet>

                                                            <%-- INICIO LISTA DE CHEQUE --%>

                                                            <f:facet name="cheque">
                                                                <h:panelGroup>
                                                                    <rich:dataTable id="listaChequesLote" width="100%" styleClass="textverysmall" style="border-color:#FFF"
                                                                                    columnClasses="colunaEsquerda, centralizado, centralizado, centralizado, centralizado,
                                                                                    centralizado, centralizado, colunaDireita, centralizado, centralizado"
                                                                                    headerClass="subordinado" rowClasses="linhaImpar, linhaPar"
                                                                                    value="#{GestaoLotesControle.listaChequesLote}" var="cheque">
                                                                        <rich:column style="border-color:#FFF">
                                                                            <f:facet name="header">
                                                                                <h:outputText style="font-weight: bold; font-size:9px;"
                                                                                              value="#{msg_aplic.prt_Finan_GestaoRecebiveis_nome}" />
                                                                            </f:facet>
                                                                            <h:outputText style="font-weight: bold; font-size:9px;"
                                                                                          styleClass="#{cheque.color}"
                                                                                          value="#{cheque.nomePagador}" />
                                                                        </rich:column>
                                                                        <rich:column style="border-color:#FFF">
                                                                            <f:facet name="header">
                                                                                <h:outputText style="font-weight: bold; font-size:9px;"
                                                                                              value="#{msg_aplic.prt_Cheque_banco}" />
                                                                            </f:facet>
                                                                            <h:outputText style="font-weight: bold; font-size:9px;"
                                                                                          styleClass="#{cheque.color}"
                                                                                          value="#{cheque.numeroBanco}" />
                                                                        </rich:column>
                                                                        <rich:column style="border-color:#FFF">
                                                                            <f:facet name="header">
                                                                                <h:outputText style="font-weight: bold; font-size:9px;"
                                                                                              value="#{msg_aplic.prt_Cheque_agencia}" />
                                                                            </f:facet>
                                                                            <h:outputText style="font-weight: bold; font-size:9px;"
                                                                                          styleClass="#{cheque.color}" value="#{cheque.agencia}" />
                                                                        </rich:column>
                                                                        <rich:column style="border-color:#FFF">
                                                                            <f:facet name="header">
                                                                                <h:outputText style="font-weight: bold; font-size:9px;"
                                                                                              value="#{msg_aplic.prt_Cheque_conta}" />
                                                                            </f:facet>
                                                                            <h:outputText style="font-weight: bold; font-size:9px;"
                                                                                          styleClass="#{cheque.color}" value="#{cheque.conta}" />
                                                                        </rich:column>
                                                                        <rich:column style="border-color:#FFF">
                                                                            <f:facet name="header">
                                                                                <h:outputText style="font-weight: bold; font-size:9px;"
                                                                                              value="#{msg_aplic.prt_Finan_GestaoRecebiveis_numeroCheque}" />
                                                                            </f:facet>
                                                                            <h:outputText style="font-weight: bold; font-size:9px;"
                                                                                          styleClass="#{cheque.color}" value="#{cheque.numero}" />
                                                                        </rich:column>
                                                                        <rich:column style="border-color:#FFF">
                                                                            <f:facet name="header">
                                                                                <h:outputText style="font-weight: bold; font-size:9px;"
                                                                                              value="#{msg_aplic.prt_Finan_GestaoRecebiveis_dataLancamento}" />
                                                                            </f:facet>
                                                                            <h:outputText style="font-weight: bold; font-size:9px;"
                                                                                          styleClass="#{cheque.color}"
                                                                                          value="#{cheque.dataLancamento}">
                                                                                <f:convertDateTime pattern="dd/MM/yyyy" />
                                                                            </h:outputText>
                                                                        </rich:column>
                                                                        <rich:column style="border-color:#FFF">
                                                                            <f:facet name="header">
                                                                                <h:outputText style="font-weight: bold; font-size:9px;"
                                                                                              value="#{msg_aplic.prt_Finan_GestaoRecebiveis_dataCompensacao}" />
                                                                            </f:facet>
                                                                            <h:outputText style="font-weight: bold; font-size:9px;"
                                                                                          styleClass="#{cheque.color}"
                                                                                          value="#{cheque.dataCompensacao}">
                                                                                <f:convertDateTime pattern="dd/MM/yyyy" />
                                                                            </h:outputText>
                                                                        </rich:column>
                                                                        <rich:column style="border-color:#FFF">
                                                                            <f:facet name="header">
                                                                                <h:outputText style="font-weight: bold; font-size:9px;"
                                                                                              value="#{msg_aplic.prt_Cheque_valor}" />
                                                                            </f:facet>
                                                                            <h:outputText style="font-weight: bold; font-size:9px;"
                                                                                          styleClass="#{cheque.color}" value="#{cheque.valor}">
                                                                                <f:converter converterId="FormatadorNumerico" />
                                                                            </h:outputText>
                                                                        </rich:column>
                                                                        
                                                                    </rich:dataTable>
                                                                   
                                                                   
                                                                    <h:panelGrid id="totalDepositadoCh" styleClass="tablepreviewtotal" width="100%"
                                                                                 columns="2" cellspacing="0" cellpadding="0"
                                                                                 columnClasses="colunaEsquerda,colunaDireita">
                                                                        <h:panelGroup>
                                                                            <h:outputText value="Total de registros: "/>
                                                                            <h:outputText value="#{fn:length(GestaoLotesControle.listaChequesLote)}"
                                                                                          styleClass="verde" />
                                                                        </h:panelGroup>
                                                                        <h:panelGroup>
                                                                            <h:outputText value="#{msg_aplic.prt_Finan_GestaoLotes_total} = "/>
                                                                            <h:outputText value="R$ " styleClass="verde"/>
                                                                            <h:outputText styleClass="verde" value="#{GestaoLotesControle.totalChequesLote}">
                                                                                <f:converter converterId="FormatadorNumerico"/>
                                                                            </h:outputText>
                                                                        </h:panelGroup>
                                                                    </h:panelGrid>
                                                                </h:panelGroup>
                                                            </f:facet>

                                                            <%-- FIM LISTA DE CHEQUE --%>
                                                            <%-- INICIO LISTA DE CARTAO --%>

                                                            <f:facet name="cartao">
                                                                <h:panelGroup>
                                                                    <rich:dataTable id="listaCartoesLote" width="100%" styleClass="textsmall" style="border-color:#FFF"
                                                                                    columnClasses="colunaEsquerda, colunaEsquerda, centralizado, centralizado, centralizado,
                                                                                    colunaDireita, centralizado, centralizado"
                                                                                    headerClass="subordinado" rowClasses="linhaImpar, linhaPar"
                                                                                    value="#{GestaoLotesControle.listaCartoesLote}" var="cartao">
                                                                        <rich:column style="border-color:#FFF">
                                                                            <f:facet name="header">
                                                                                <h:outputText style="font-weight: bold; font-size:9px;"
                                                                                              value="#{msg_aplic.prt_Finan_GestaoRecebiveis_nome}" />
                                                                            </f:facet>
                                                                            <h:outputText style="font-weight: bold; font-size:9px;"
                                                                                          styleClass="blue"
                                                                                          value="#{cartao.nomePagador}" />
                                                                        </rich:column>
                                                                        <rich:column style="border-color:#FFF">
                                                                            <f:facet name="header">
                                                                                <h:outputText style="font-weight: bold; font-size:9px;"
                                                                                              value="#{msg_aplic.prt_OperadoraCartao_tituloForm}" />
                                                                            </f:facet>
                                                                            <h:outputText style="font-weight: bold; font-size:9px;"
                                                                                          styleClass="blue" value="#{cartao.operadora}" />
                                                                        </rich:column>
                                                                        <rich:column style="border-color:#FFF">
                                                                            <f:facet name="header">
                                                                                <h:outputText style="font-weight: bold; font-size:9px;"
                                                                                              value="#{msg_aplic.prt_Finan_GestaoRecebiveis_dataLancamento}" />
                                                                            </f:facet>
                                                                            <h:outputText style="font-weight: bold; font-size:9px;"
                                                                                          styleClass="blue"
                                                                                          value="#{cartao.dataLancamento}">
                                                                                <f:convertDateTime pattern="dd/MM/yyyy" />
                                                                            </h:outputText>
                                                                        </rich:column>
                                                                        <rich:column style="border-color:#FFF">
                                                                            <f:facet name="header">
                                                                                <h:outputText style="font-weight: bold; font-size:9px;"
                                                                                              value="#{msg_aplic.prt_Finan_GestaoRecebiveis_dataCompensacao}" />
                                                                            </f:facet>
                                                                            <h:outputText style="font-weight: bold; font-size:9px;"
                                                                                          styleClass="blue"
                                                                                          value="#{cartao.dataCompensacao}">
                                                                                <f:convertDateTime pattern="dd/MM/yyyy" />
                                                                            </h:outputText>
                                                                        </rich:column>
                                                                        
                                                                         <rich:column style="border-color:#FFF">
                                                                                <f:facet name="header">
                                                                                    <h:outputText style="font-weight: bold; font-size:9px;"
                                                                                                  value="#{msg_aplic.prt_Finan_GestaoRecebiveis_Autorizacao}" />
                                                                                </f:facet>
                                                                                <h:outputText style="font-weight: bold; font-size:9px;"
                                                                                              styleClass="blue" value="#{cartao.autorizacao}">
                                                                                </h:outputText>
                                                                            </rich:column>
                                                                        
                                                                        <rich:column style="border-color:#FFF">
                                                                            <f:facet name="header">
                                                                                <h:outputText style="font-weight: bold; font-size:9px;"
                                                                                              value="#{msg_aplic.prt_Cheque_valor}" />
                                                                            </f:facet>
                                                                            <h:outputText style="font-weight: bold; font-size:9px;"
                                                                                          styleClass="blue" value="#{cartao.valor}">
                                                                                <f:converter converterId="FormatadorNumerico" />
                                                                            </h:outputText>
                                                                        </rich:column>
                                                                        
                                                                    </rich:dataTable>
                                                                    
                                                                    <h:panelGrid id="totalDepositadoCa" styleClass="tablepreviewtotal" width="100%"
                                                                                 columns="2" cellpadding="0" cellspacing="0"
                                                                                 columnClasses="colunaEsquerda,colunaDireita">
                                                                        <h:panelGroup>
                                                                            <h:outputText value="Total de registros: "/>
                                                                            <h:outputText styleClass="verde" value="#{fn:length(GestaoLotesControle.listaCartoesLote)}"/>
                                                                        </h:panelGroup>
                                                                        <h:panelGroup>
                                                                            <h:outputText value="#{msg_aplic.prt_Finan_GestaoLotes_total} = "/>
                                                                            <h:outputText value="R$ " styleClass="verde"/>
                                                                            <h:outputText styleClass="verde"  value="#{GestaoLotesControle.totalCartoesLote}">
                                                                                <f:converter converterId="FormatadorNumerico"/>
                                                                            </h:outputText>
                                                                        </h:panelGroup>
                                                                    </h:panelGrid>
                                                                </h:panelGroup>
                                                            </f:facet>

                                                            <%-- FIM LISTA DE CARTAO --%>

                                                        </rich:togglePanel>
                                                        
                                                        <center>
                                                        	 <h:commandLink
												                onclick="window.print();return false;">
												                <h:graphicImage value="/imagens/botoesCE/imprimir.png"  style="border: 0px; width: 65;"/>
												            </h:commandLink>
                                                        
                                                        </center>
                                                        </body>
                                                        </html>
                                                        </h:form>
                                                        </f:view>
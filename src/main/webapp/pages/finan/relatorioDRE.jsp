<%-- 
    Document   : relatorioDF
    Created on : 04/10/2011, 17:19:24
    Author     : <PERSON><PERSON><PERSON>
--%>

<%@page contentType="text/html" pageEncoding="ISO-8859-1"%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@include file="includes/include_imports.jsp" %>
<%@taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>

<link href="../../css_pacto.css" rel="stylesheet" type="text/css">
<link href="../../css/otimize.css" rel="stylesheet" type="text/css">
<link href="../../css/jquery.treeTable.css" rel="stylesheet" type="text/css">

<script type="text/javascript" src="../../script/jquery.js"></script>
<script type="text/javascript" src="../../script/jquery.treeTable.js"></script>
<script type="text/javascript">
    $.noConflict();
    jQuery(document).ready(function($) {
        $(".example").treeTable({
            initialState: "expanded"
        });
        $(".filtroPlanos").treeTable({
            initialState: "expanded"
        });
        $(".filtroCentros").treeTable({
            initialState: "expanded"
        });
    });


    jQuery(document).ready(function($){
        $(".expandir").click(function() {
            $(".example").expandirTudo();
       	});

        $(".expandirUm").click(function() {
            $(".example").expandirUmNivel("dnd-example");
        });

        $(".retrairUm").click(function() {
            $(".example").retrairUmNivel("dnd-example");
        });

        $(".retrair").click(function() {
            $(".example").retrairTudo();
        });
    });

    function atualizarTreeView() {
        window.location.reload();
    }

    function mudar_cor(celula, cor){
        celula.style.backgroundColor= cor;
    }

</script>

<f:view>
    <jsp:include page="/includes/include_carregando_ripple.jsp" flush="true"/>
    <a4j:form id="formRelDF">
        <table border="1" width="100%" >
            <tr>
                <td width="5px">
                    <a4j:mediaOutput element="img" id="foto"  style="width:120px;height:160px "  cacheable="false"
                                     createContent="#{DREControle.paintFoto}"  value="#{ImagemData}" mimeType="image/jpeg" >
                        <f:param value="#{SuperControle.timeStamp}" name="time"/>
                        <f:param name="largura" value="120"/>
                        <f:param name="altura" value="160"/>
                    </a4j:mediaOutput>
                </td>
                <td>
                    <center>
                        <h1>D.R.E. Financeiro</h1>
                    </center>
                </td>
                <td>
                    <table width="100%" >
                        <tr>
                            <td>Data: <h:outputText value="#{DREControle.dataAtual}"/> </td>
                        <tr>
                        <tr>
                            <td>Usuario: <h:outputText value="#{DREControle.nomeUsuarioLogado}"/></td>
                        <tr>

                    </table>
                </td>

            </tr>

        </table>
        <table style="padding-top:20px">
            <tr>
                <td style="font-weight: bold"> <h:outputText  value="Parâmetros:" /></td>
                <td> <h:outputText  value="" /></td>
            </tr>
            <tr>
                <td style="padding-left:20px"> <h:outputText value="  Consulta:" /> </td>
                <td>  <h:outputText value="#{DREControle.tipoRelatorioDf.descricao}" /></td>

            </tr>
            <tr>
                <td style="padding-left:20px"> <h:outputText value="Tipo Visualização:" /> </td>
                <td> <h:outputText value="#{DREControle.tipoVisualizacao.descricao}" /> </td>

            </tr>
            <tr>
                <td style="padding-left:20px"> <h:outputText value="Período:" /> </td>
                <td> <h:outputText value="#{DREControle.periodoRelatorio}" /> </td>
            </tr>
            <tr>
                <td style="padding-left:20px"> <h:outputText value="Fonte de Dados:" /> </td>
                <td> <h:outputText value="#{DREControle.tipoFonteDadosDF.descricao}" /> </td>
            </tr>
            <tr>
                <td style="padding-left:20px"> <h:outputText value="Empresa:" /> </td>
                <td> <h:outputText value="#{DREControle.nomeEmpresa}" /> </td>
            </tr>
        </table>
        <%--<h:panelGroup  rendered="#{not empty DREControle.listaCentroCustosSelecionados}">--%>
            <%--<table>--%>
                <%--<tr>--%>
                    <%--<td> <h:outputText style="padding-left:20px" value="Centro de Custos:" /> </td>--%>
                    <%--<td> <h:outputText  value="#{DREControle.lista_CC_Selecionado}" /> </td>--%>
                <%--</tr>--%>
            <%--</table>--%>
        <%--</h:panelGroup>--%>

        <%@include file="includes/include_TreeViewDRE.jsp" %>

        <h:panelGrid columns="1" columnClasses="colunaCentralizada" width="100%" style="background-color:#FFFFFF;">
            <h:commandLink
                onclick="window.print();return false;">
                <h:graphicImage value="/imagens/botoesCE/imprimir.png"  style="border: 0; width: 65px;"/>
            </h:commandLink>
        </h:panelGrid>

    </a4j:form>
</f:view>

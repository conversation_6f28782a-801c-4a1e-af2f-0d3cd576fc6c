<%-- 
    Document   : telaCaixa
    Created on : 19/03/2012, 10:53:22
    Author     : <PERSON><PERSON><PERSON>
--%>

<%@include file="includes/include_imports.jsp" %>
<c:set var="moduloSession" value="1" scope="session" />
<link href="../../css/packcss1.0.min.css" rel="stylesheet" type="text/css">
<link href="../../css_pacto.css" rel="stylesheet" type="text/css">
<link href="../../css/financeiro.css" rel="stylesheet" type="text/css">
<link href="../../css/otimize.css" rel="stylesheet" type="text/css">

<f:view>
    <jsp:include page="../../includes/include_carregando_ripple.jsp"/>
    <jsp:include page="../../includes/autorizacao/include_autorizacao_funcionalidade.jsp"/>
    <a4j:loadScript src="/script/jquery.maskedinput-1.2.2.js" />
    <h:form id="form">
        <!-- Inclui o elemento HEAD da p?gina -->
        <head>
            <%@include file="includes/include_head_finan.jsp" %>
            <script type="text/javascript" language="javascript" src="${contextoFinan}../script/telaInicial.js"></script>
        </head>

        <body>

        <h:panelGroup layout="block" style="display: inline-table;width: 100%;" styleClass="fundoCinza">
            <h:panelGroup layout="block" styleClass="bgtop topoZW" rendered="#{MenuControle.apresentarTopo}">
                <jsp:include page="../../include_topo_novo.jsp" flush="true"/>
                <jsp:include page="../../include_menu_fin_flat.jsp" flush="true"/>
                <rich:jQuery selector=".item1" query="addClass('menuItemAtual')"/>
            </h:panelGroup>

            <h:panelGroup layout="block" styleClass="caixaCorpo">
                <h:panelGroup layout="block" style="height: 80%;width: 100%">
                    <h:panelGroup layout="block" styleClass="caixaMenuLatel">
                        <h:panelGroup layout="block" styleClass="container-imagem  container-conteudo-central">
                            <h:panelGroup layout="block" styleClass="container-box zw_ui especial ">
                                <h:panelGroup styleClass="container-box-header" layout="block">
                                    <h:panelGroup layout="block" styleClass="margin-box">
                                        <h:outputText value="Opera��es realizadas no Caixa-Administrativo:#{CaixaControle.caixaVoMostrarMov.codigo}" styleClass="container-header-titulo"/>
                                        <h:outputLink styleClass="linkWiki"
                                                      value="#{SuperControle.urlBaseConhecimento}relatorio-caixa-administrativo/"
                                                      title="Clique e saiba mais: Caixa Administrativo"
                                                      target="_blank" >
                                            <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                                        </h:outputLink>
                                    </h:panelGroup>
                                </h:panelGroup>
                                <h:panelGroup layout="block" styleClass="margin-box">
                                    <h:panelGroup>
                                        <h:outputText rendered="#{(CaixaControle.caixaVoMostrarMov.codigo ==0)}"
                                                      style="color:red; font-size:16px"
                                                      value="Usu�rio n�o possui caixa em aberto." />
                                    </h:panelGroup>

                                    <h:panelGroup>
                                        <rich:spacer rendered="#{(CaixaControle.caixaVoMostrarMov.codigo ==0)}" height="50" ></rich:spacer>
                                    </h:panelGroup>

                                    <h:panelGrid width="100%" style="font-size:12px" columns="6" rendered="#{CaixaControle.caixaVoMostrarMov.codigo >0}">
                                        <h:panelGroup>
                                            <h:outputText  styleClass="tituloCamposNegrito" value="Usu�rio: "/>
                                            <h:outputText id="usuario"  value="#{CaixaControle.caixaVoMostrarMov.usuarioVo.username}" />
                                        </h:panelGroup>

                                        <h:panelGroup>
                                            <h:outputText  styleClass="tituloCamposNegrito" value="Abertura: "/>
                                            <h:outputText id="dataAbertura" value="#{CaixaControle.caixaVoMostrarMov.dataAbertura}">
                                                <f:convertDateTime pattern="dd/MM/yyyy HH:mm"/>
                                            </h:outputText>
                                        </h:panelGroup>

                                        <h:panelGroup rendered="#{CaixaControle.caixaVoMostrarMov.dataFechamento != null}">
                                            <h:outputText  styleClass="tituloCamposNegrito" value="Fechamento: "/>
                                            <h:outputText value="#{CaixaControle.caixaVoMostrarMov.dataFechamento}">
                                                <f:convertDateTime pattern="dd/MM/yyyy HH:mm"/>
                                            </h:outputText>
                                        </h:panelGroup>

                                        <h:panelGroup>
                                            <h:outputText  styleClass="tituloCamposNegrito" value="Total Entrada: "/>
                                            <h:outputText value="#{CaixaControle.caixaVoMostrarMov.totalEntradaMonetario}"/>
                                        </h:panelGroup>

                                        <h:panelGroup>
                                            <h:outputText  styleClass="tituloCamposNegrito" value="Total Sa�da: "/>
                                            <h:outputText value="#{CaixaControle.caixaVoMostrarMov.totalSaidaMonetario}"/>
                                        </h:panelGroup>

                                        <h:panelGroup>
                                            <a4j:commandLink rendered="#{CaixaControle.apresentarBotaoImprimirCaixaTelaPrincipal}"
                                                             action="#{CaixaControle.imprimirCaixa}"
                                                             oncomplete="#{CaixaControle.msgAlert}">
                                                <h:graphicImage value="/images/imprimir.png"  style="border: 0px;" title="Imprimir Caixa"/>
                                            </a4j:commandLink>
                                        </h:panelGroup>

                                        <a4j:commandButton id="fecharCaixaOutroBtn" rendered="#{CaixaControle.mostrarFecharCaixaOutro}"
                                                           oncomplete="#{CaixaControle.mensagemNotificar}"
                                                           value="Fechar Caixa"
                                                           action="#{CaixaControle.abrirModalFechamento}"
                                                           reRender="panelAutorizacaoFuncionalidade">
                                        </a4j:commandButton>
                                        <a4j:commandButton id="fecharCaixaBtn" rendered="#{CaixaControle.mostrarFecharCaixa}"
                                                           oncomplete="#{CaixaControle.msgAlert}"
                                                           value="Fechar Caixa"
                                                           action="#{CaixaControle.fecharCaixaVisualizado}">
                                        </a4j:commandButton>
                                    </h:panelGrid>

                                    <h:panelGroup>
                                        <h:selectBooleanCheckbox rendered="#{CaixaControle.caixaVoMostrarMov.codigo >0}" value="#{CaixaControle.mostrarPaginacao}" id="checadoCaixa" label="Mostrar Pagina??o" >
                                            <a4j:support event="onclick" reRender="paginaAtual,paginaAtualTop,painelPaginacaoTop, painelPaginacao, items" actionListener="#{CaixaControle.consultarPaginadoListener}"/>
                                        </h:selectBooleanCheckbox>
                                        <h:outputText rendered="#{CaixaControle.caixaVoMostrarMov.codigo >0}" styleClass="tituloDemonstrativo" value="Mostrar Pagina��o"/>
                                    </h:panelGroup>

                                    <table width="100%" border="0" cellspacing="0" cellpadding="0">
                                        <tr>
                                            <td align="center" valign="middle">
                                                <a4j:outputPanel id="painelPaginacaoTop">
                                                    <h:panelGroup id="painelPaginacaoTopManual" rendered="#{((CaixaControle.caixaVoMostrarMov.codigo >0) &&  (CaixaControle.confPaginacao.paginarBanco))}">
                                                        <a4j:commandLink id="pagiInicialTop" styleClass="tituloCampos" value="  <<  " reRender="items, paginaAtualTop,paginaAtual, painelPaginacaoTop, painelPaginacao" rendered="#{CaixaControle.confPaginacao.apresentarPrimeiro}" actionListener="#{CaixaControle.consultarPaginadoListener}">
                                                            <f:attribute name="pagNavegacao" value="pagInicial" />
                                                        </a4j:commandLink>

                                                        <a4j:commandLink id="pagiAnteriorTop" styleClass="tituloCampos" value="  <  " reRender="items, paginaAtualTop,paginaAtual, painelPaginacaoTop, painelPaginacao" rendered="#{CaixaControle.confPaginacao.apresentarAnterior}" actionListener="#{CaixaControle.consultarPaginadoListener}">
                                                            <f:attribute name="pagNavegacao" value="pagAnterior" />
                                                        </a4j:commandLink>

                                                        <h:outputText id="paginaAtualTop" styleClass="tituloCampos" value="#{msg_aplic.prt_msg_pagina} #{CaixaControle.confPaginacao.paginaAtualDeTodas}" rendered="true"/>

                                                        <a4j:commandLink id="pagiPosteriorTop" styleClass="tituloCampos" value="  >  " reRender="items, paginaAtualTop,paginaAtual, painelPaginacaoTop, painelPaginacao" rendered="#{CaixaControle.confPaginacao.apresentarPosterior}" actionListener="#{CaixaControle.consultarPaginadoListener}">
                                                            <f:attribute name="pagNavegacao" value="pagPosterior" />
                                                        </a4j:commandLink>

                                                        <a4j:commandLink id="pagiFinalTop" styleClass="tituloCampos" value="  >>  " reRender="items, paginaAtualTop,paginaAtual, painelPaginacaoTop, painelPaginacao" rendered="#{CaixaControle.confPaginacao.apresentarUltimo}" actionListener="#{CaixaControle.consultarPaginadoListener}">
                                                            <f:attribute name="pagNavegacao" value="pagFinal" />
                                                        </a4j:commandLink>

                                                        <h:outputText id="totalItensTop" styleClass="tituloCampos" value=" [#{msg_aplic.prt_msg_itens} #{CaixaControle.confPaginacao.numeroTotalItens}]" rendered="true"/>
                                                    </h:panelGroup>
                                                </a4j:outputPanel>
                                            </td>
                                        </tr>
                                    </table>

                                    <rich:dataTable id="items" width="100%" headerClass="consulta"
                                                    rowClasses="linhaPar,linhaImpar "
                                                    rendered="#{(CaixaControle.caixaVoMostrarMov.codigo >0)}"
                                                    reRender="paginaAtual, paginaAtualTop,painelPaginacaoTop,painelPaginacao"
                                                    columnClasses="centralizado, centralizado, esquerda, direita, esquerda, esquerda, centralizado"
                                                    value="#{CaixaControle.caixaVoMostrarMov.listaCaixaMovConta}" rows="100" var="caixaMovConta"
                                                    cellpadding="0"
                                                    cellspacing="0">
                                        <rich:column title="Data em que a opera??o foi realizada no Caixa." sortBy="#{caixaMovConta.movContaVo.dataLancamento}" filterEvent="onkeyup" >
                                            <f:facet name="header">
                                                <h:outputText value="#{msg_aplic.prt_Finan_Lancamentos_dataLancamento}"/>
                                            </f:facet>
                                            <a4j:commandLink action="#{MovContaControle.preparaEdicaoVindoCaixa}">
                                                <h:outputText value="#{caixaMovConta.movContaVo.dataLancamento_Apresentar}" />
                                            </a4j:commandLink>
                                        </rich:column>

                                        <rich:column  sortBy="#{caixaMovConta.movContaVo.dataQuitacao}" filterEvent="onkeyup" >
                                            <f:facet name="header">
                                                <h:outputText value="#{msg_aplic.prt_Finan_Lancamentos_dataQuitacao}"/>
                                            </f:facet>
                                            <a4j:commandLink action="#{MovContaControle.preparaEdicaoVindoCaixa}">
                                                <h:outputText value="#{caixaMovConta.movContaVo.dataQuitacao_Apresentar}" />
                                            </a4j:commandLink>
                                        </rich:column>

                                        <rich:column sortBy="#{caixaMovConta.movContaVo.pessoaVO.nome}" filterEvent="onkeyup" >
                                            <f:facet name="header">
                                                <h:outputText value="#{msg_aplic.prt_Finan_Lancamentos_favorecido}"/>
                                            </f:facet>
                                            <a4j:commandLink action="#{MovContaControle.preparaEdicaoVindoCaixa}">
                                                <h:outputText value="#{caixaMovConta.movContaVo.pessoaVO.nome}"/>
                                            </a4j:commandLink>
                                        </rich:column>

                                        <rich:column sortBy="#{caixaMovConta.valor}" filterEvent="onkeyup" >
                                            <f:facet name="header">
                                                <h:outputText value="#{msg_aplic.prt_Finan_Lancamentos_valor}"/>
                                            </f:facet>
                                            <a4j:commandLink action="#{MovContaControle.preparaEdicaoVindoCaixa}">
                                                <h:outputText value="#{caixaMovConta.valor_Apresentar}" />
                                            </a4j:commandLink>
                                        </rich:column>

                                        <rich:column  sortBy="#{caixaMovConta.descricao}" filterEvent="onkeyup" >
                                            <f:facet name="header">
                                                <h:outputText value="#{msg_aplic.prt_Finan_Lancamentos_descricao}"/>
                                            </f:facet>
                                            <a4j:commandLink action="#{MovContaControle.preparaEdicaoVindoCaixa}">
                                                <h:outputText value="#{caixaMovConta.descricao}"/>
                                            </a4j:commandLink>
                                        </rich:column>

                                        <rich:column sortBy="#{caixaMovConta.movContaVo.contaVO.descricao}" filterEvent="onkeyup" >
                                            <f:facet name="header">
                                                <h:outputText value="#{msg_aplic.prt_Finan_Lancamentos_conta}"/>
                                            </f:facet>
                                            <a4j:commandLink action="#{MovContaControle.preparaEdicaoVindoCaixa}">
                                                <h:outputText value="#{caixaMovConta.movContaVo.contaVO.descricao}"/>
                                            </a4j:commandLink>
                                        </rich:column>

                                        <rich:column  sortBy="#{caixaMovConta.movContaVo.tipoOperacaoLancamento.descricaoCurta}"  filterEvent="onkeyup">
                                            <f:facet name="header">
                                                <h:outputText value="#{msg_aplic.prt_Finan_Lancamentos_operacoes}"/>
                                            </f:facet>
                                            <a4j:commandLink action="#{MovContaControle.preparaEdicaoVindoCaixa}">
                                                <h:outputText  value="#{caixaMovConta.movContaVo.tipoOperacaoLancamento.descricaoCurta}"/>
                                            </a4j:commandLink>
                                        </rich:column>

                                        <rich:column  sortBy="#{caixaMovConta.movContaVo.statusRetornoStoneOpenBankingCaixa}"
                                                      rendered="#{MovContaControle.moduloOpenBankAtivado}"
                                                      filterEvent="onkeyup">
                                            <f:facet name="header">
                                                <h:outputText value="Status"/>
                                            </f:facet>
                                            <a4j:commandLink action="#{MovContaControle.preparaEdicaoVindoCaixa}"
                                                             rendered="#{not (caixaMovConta.movContaVo.statusRetornoStoneOpenBankingCaixa eq 'Transfer�ncia Recusada' or
                                                             caixaMovConta.movContaVo.statusRetornoStoneOpenBankingCaixa eq 'Transfer�ncia Agendada' or
                                                             caixaMovConta.movContaVo.statusRetornoStoneOpenBankingCaixa eq ' - ')}">
                                                <h:outputText  styleClass="texto-cor-verde" value="#{caixaMovConta.movContaVo.statusRetornoStoneOpenBankingCaixa}"/>
                                            </a4j:commandLink>
                                            <a4j:commandLink action="#{MovContaControle.verificarPermissaoEstornoTransferencia}"
                                                             rendered="#{caixaMovConta.movContaVo.statusRetornoStoneOpenBankingCaixa eq 'Transfer�ncia Recusada'}"
                                                             reRender="form"
                                                             oncomplete="#{MovContaControle.onCompleteEstornarPagamento}">
                                                <h:outputText  styleClass="texto-cor-vermelho" value="#{caixaMovConta.movContaVo.statusRetornoStoneOpenBankingCaixa}"/>
                                            </a4j:commandLink>

                                            <a4j:commandLink action="#{MovContaControle.verificarPermissaoEstornoTransferencia}"
                                                             rendered="#{caixaMovConta.movContaVo.statusRetornoStoneOpenBankingCaixa eq 'Transfer�ncia Agendada' or
                                                             caixaMovConta.movContaVo.statusRetornoStoneOpenBankingCaixa eq ' - '}"
                                                             reRender="form"
                                                             oncomplete="#{MovContaControle.onCompleteEstornarPagamento}">
                                                <h:outputText  styleClass="texto-cor-verde" value="#{caixaMovConta.movContaVo.statusRetornoStoneOpenBankingCaixa}"/>
                                            </a4j:commandLink>
                                        </rich:column>
                                    </rich:dataTable>

                                    <table width="100%" border="0" cellspacing="0" cellpadding="0">
                                        <tr>
                                            <td align="center" valign="middle">
                                                <a4j:outputPanel id="painelPaginacao">
                                                    <h:panelGroup id="painelPaginacaoManual" rendered="#{((CaixaControle.caixaVoMostrarMov.codigo >0) && (CaixaControle.confPaginacao.paginarBanco))}">
                                                        <a4j:commandLink id="pagiInicial" styleClass="tituloCampos" value="  <<  " reRender="items, paginaAtual,paginaAtualTop,painelPaginacaoTop, painelPaginacao" rendered="#{CaixaControle.confPaginacao.apresentarPrimeiro}" actionListener="#{CaixaControle.consultarPaginadoListener}">
                                                            <f:attribute name="pagNavegacao" value="pagInicial" />
                                                        </a4j:commandLink>

                                                        <a4j:commandLink id="pagiAnterior" styleClass="tituloCampos" value="  <  " reRender="items, paginaAtual,paginaAtualTop,painelPaginacaoTop, painelPaginacao" rendered="#{CaixaControle.confPaginacao.apresentarAnterior}" actionListener="#{CaixaControle.consultarPaginadoListener}">
                                                            <f:attribute name="pagNavegacao" value="pagAnterior" />
                                                        </a4j:commandLink>

                                                        <h:outputText id="paginaAtual" styleClass="tituloCampos" value="#{msg_aplic.prt_msg_pagina} #{CaixaControle.confPaginacao.paginaAtualDeTodas}" rendered="true"/>

                                                        <a4j:commandLink id="pagiPosterior" styleClass="tituloCampos" value="  >  " reRender="items, paginaAtual,paginaAtualTop,painelPaginacaoTop, painelPaginacao" rendered="#{CaixaControle.confPaginacao.apresentarPosterior}" actionListener="#{CaixaControle.consultarPaginadoListener}">
                                                            <f:attribute name="pagNavegacao" value="pagPosterior" />
                                                        </a4j:commandLink>

                                                        <a4j:commandLink id="pagiFinal" styleClass="tituloCampos" value="  >>  " reRender="items, paginaAtual,paginaAtualTop,painelPaginacaoTop, painelPaginacao" rendered="#{CaixaControle.confPaginacao.apresentarUltimo}" actionListener="#{CaixaControle.consultarPaginadoListener}">
                                                            <f:attribute name="pagNavegacao" value="pagFinal" />
                                                        </a4j:commandLink>

                                                        <h:outputText id="totalItens" styleClass="tituloCampos" value=" [#{msg_aplic.prt_msg_itens} #{CaixaControle.confPaginacao.numeroTotalItens}]" rendered="true"/>
                                                    </h:panelGroup>

                                                    <h:panelGroup id="painelPaginacaoScroller" rendered="#{!CaixaControle.confPaginacao.paginarBanco}">
                                                        <rich:datascroller align="center" for="form:items" maxPages="100" id="scitems" reRender="items, paginaAtual,paginaAtualTop,painelPaginacaoTop, painelPaginacao"/>
                                                    </h:panelGroup>
                                                </a4j:outputPanel>
                                            </td>
                                        </tr>
                                    </table>

                                    <br/>

                                    <center>
                                        <table>
                                            <tr valign="middle">
                                                <td>
                                                    <a4j:commandButton action="#{CaixaControle.abrirModalReabertura}"
                                                                       reRender="panelAutorizarFechamento"
                                                                       oncomplete="#{CaixaControle.oncompleteReabertura}"
                                                                       value="Reabrir" alt="Reabrir Caixa" title="Reabrir Caixa"
                                                                       styleClass="botoes" image="/imagens/reabrir-caixa.png"
                                                                       rendered="#{CaixaControle.apresentarBotaoImprimirCaixaTelaPrincipal}"/>
                                                </td>

                                                <td>
                                                    <a4j:commandButton action="#{CaixaControle.realizarConsultaLogObjetoSelecionado}"
                                                                       reRender="form"
                                                                       oncomplete="#{CaixaControle.msgAlert}"
                                                                       rendered="#{CaixaControle.caixaVoMostrarMov.codigo >0}"
                                                                       image="/imagens/botalVisualizarLog.png"
                                                                       alt="Visualizar LOG"
                                                                       title="Visualizar Log"
                                                                       styleClass="botoes"/>
                                                </td>
                                            </tr>
                                        </table>
                                    </center>

                                    <h:panelGrid id="panelMensagem" columns="1" width="100%" styleClass="tabMensagens">
                                        <h:panelGrid rendered="#{(CaixaControle.caixaVoMostrarMov.codigo >0)}" columns="3" width="100%" styleClass="tabMensagens">
                                            <h:panelGrid columns="1" width="100%">
                                                <h:outputText value=" "/>
                                            </h:panelGrid>

                                            <h:commandButton  rendered="#{CaixaControle.sucesso}" image="/imagens/sucesso.png"/>
                                            <h:commandButton rendered="#{CaixaControle.erro}" image="/imagens/erro.png"/>

                                            <h:panelGrid columns="1" width="100%">
                                                <h:outputText id="msgCaixa" styleClass="mensagem"  value="#{CaixaControle.mensagem}"/>
                                                <h:outputText styleClass="mensagemDetalhada" value="#{CaixaControle.mensagemDetalhada}"/>
                                            </h:panelGrid>
                                        </h:panelGrid>
                                    </h:panelGrid>
                                </h:panelGroup>
                            </h:panelGroup>
                        </h:panelGroup>
                        <jsp:include page="includes/include_box_menulateral.jsp" flush="true"/>
                    </h:panelGroup>
                </h:panelGroup>
            </h:panelGroup>

            <jsp:include page="../../include_rodape_flat.jsp" flush="true" />
        </h:panelGroup>
        </body>
    </h:form>

    <%@include file="includes/include_modal_abrirCaixa.jsp" %>
    <%@include file="include_modalConfigGestaoRecebiveis.jsp" %>
    <%@include file="includes/include_modal_consultarCaixa.jsp" %>
    <%@include file="includes/include_box_fecharCaixas.jsp" %>
    <%@include file="includes/include_modal_estornoTransferencia.jsp" %>
</f:view>

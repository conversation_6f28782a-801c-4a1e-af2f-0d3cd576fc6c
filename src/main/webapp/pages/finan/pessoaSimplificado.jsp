<%-- 
    Document   : pessoaSimplificado
    Created on : 19/07/2011, 14:01:22
    Author     : carla
--%>
<h:form id="formDefinirAutosized">
    <h:inputHidden id="inputAutosized" value="#{PessoaSimplificadoControle.usarAutosizedNoModal}" />

    <a4j:commandButton id="btnDefinirAutosized"
                       style="display:none"
                       reRender="modalPanelCadastrarPessoaSimplificada">
        <f:setPropertyActionListener value="#{PessoaSimplificadoControle.usarAutosizedNoModal}" target="#{PessoaSimplificadoControle.usarAutosizedNoModal}" />
    </a4j:commandButton>
</h:form>
<script type="text/javascript">
    window.onload = function() {
        //Validação para definir o uso do autosized no modal, em telas muito pequenas será mantido um tamanho padrão para o modal e adicionado o scroll,
        // caso contrário se adpta ao tamanho da tela
        var width = screen.width;
        var height = screen.height;
        var autosized = (width > 1024 && height > 768);

        document.getElementById("formDefinirAutosized:inputAutosized").value = autosized;

        document.getElementById("formDefinirAutosized:btnDefinirAutosized").click();
    };
</script>
<a4j:outputPanel>
    <rich:modalPanel id="modalPanelCadastrarPessoaSimplificada"
                     autosized="#{PessoaSimplificadoControle.usarAutosizedNoModal}"
                     minWidth="800" minHeight="500"
                     styleClass="novaModal"
                     shadowOpacity="true" style="#{PessoaSimplificadoControle.usarAutosizedNoModal ? 'overflow-y: none;' :'overflow-y: scroll;'}">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Cadastro de Pessoa"/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText
                        styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                        id="hidelinkCadastrarNovaPessoa"/>
                <rich:componentControl for="modalPanelCadastrarPessoaSimplificada"
                                       attachTo="hidelinkCadastrarNovaPessoa" operation="hide" event="onclick"/>
            </h:panelGroup>
        </f:facet>
        <a4j:form id="formCadastrarNovaPessoa">

            <h:panelGrid id="panelNovaPessoa" columns="2" rowClasses="linhaImpar, linhaPar" columnClasses="classEsquerda, classDireita" width="100%">

                <c:if test="${PessoaSimplificadoControle.mostrarCampoEmpresa}">
                    <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_CadastroPessoa_empresa}"/>

                    <h:panelGroup styleClass="block cb-container" layout="block">
                        <h:selectOneMenu disabled="true"
                                         id="empresaCad"
                                         onblur="blurinput(this);"
                                         onfocus="focusinput(this);" styleClass="form"
                                         value="#{PessoaSimplificadoControle.empresaVO.codigo}">
                            <f:selectItems value="#{PessoaSimplificadoControle.listaSelectItemEmpresa}"/>
                        </h:selectOneMenu>
                    </h:panelGroup>
                </c:if>

                <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_CadastroPessoa_nome}" />
                <h:panelGroup>
                    <h:inputText tabindex="2"  id="nome" size="50" maxlength="80" onblur="blurinput(this);"
                                 onfocus="focusinput(this);" styleClass="form" value="#{PessoaSimplificadoControle.pessoaVO.nome}" />
                    <h:message for="nome" styleClass="mensagemDetalhada"/>
                </h:panelGroup>

                <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_CadastroPessoa_tipo}" />
                <h:selectOneRadio tabindex="3" id="tipoPessoa" styleClass="tituloCampos" value="#{PessoaSimplificadoControle.pessoaVO.tipoPessoa}" >
                    <f:selectItems   value="#{PessoaSimplificadoControle.listaSelectItemTipoPessoa}"/>
                    <a4j:support focus="#{PessoaSimplificadoControle.pessoaVO.tipoPessoa == 'CL' ? 'cpf' : 'cnpj'}" event="onclick" reRender="panelNovaPessoa"/>
                </h:selectOneRadio>

                <h:outputText  rendered="#{PessoaSimplificadoControle.pessoaVO.tipoPessoa == 'CL'}"  styleClass="tituloCampos"
                               value="#{msg_aplic.prt_CadastroPessoa_cpf}" />
                <h:outputText  rendered="#{PessoaSimplificadoControle.pessoaVO.tipoPessoa == 'FO'}"  styleClass="tituloCampos"
                               value="#{msg_aplic.prt_CadastroPessoa_cnpj}" />
                <h:panelGroup>
                    <rich:jQuery id="mskCPF" selector="#cfp" timing="onload" query="mask('999.999.999-99')" />
                    <h:inputText tabindex="4" rendered="#{PessoaSimplificadoControle.pessoaVO.tipoPessoa == 'CL'}" id="cfp" size="14" maxlength="14"
                                 onblur="blurinput(this);"  onfocus="focusinput(this);"   styleClass="form"
                                 value="#{PessoaSimplificadoControle.pessoaVO.cfp}" >
                        <a4j:support focus="numeroTelefone" event="onchange" action="#{PessoaSimplificadoControle.validarCPF}" reRender="formCadastrarNovaPessoa"/>
                    </h:inputText>
                    <h:panelGroup>
                        <rich:jQuery id="mskCNPJ" selector="#cnpj" timing="onload" query="mask('99.999.999/9999-99')" />
                        <h:inputText tabindex="4" rendered="#{PessoaSimplificadoControle.pessoaVO.tipoPessoa == 'FO'}"
                                     id="cnpj"
                                     onblur="blurinput(this); "
                                     onfocus="focusinput(this);" styleClass="form" size="19"
                                     maxlength="18" style="border: 1px solid #8eb3c3"
                                     value="#{PessoaSimplificadoControle.fornecedorVO.cnpj}">
                            <a4j:support focus="numeroTelefone" event="onchange" action="#{PessoaSimplificadoControle.validarCNPJ}" reRender="formCadastrarNovaPessoa"/>
                        </h:inputText>
                    </h:panelGroup>
                </h:panelGroup>
                <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_CadastroPessoa_telefone}" />
                <h:panelGroup>
                    <h:inputText tabindex="5" id="numeroTelefone"
                                 size="13"
                                 maxlength="13"
                                 onchange="return validar_Telefone(this.id);"
                                 onblur="blurinput(this);"
                                 onkeypress="return mascara(this.form, this.id , '(99)999999999', event);"
                                 onfocus="focusinput(this);"
                                 styleClass="form"
                                 value="#{PessoaSimplificadoControle.telefoneVO.numero}"/>
                    <%--<rich:jQuery id="mskTelefone" selector="#numeroTelefone" timing="onload" query="mask('(99)9999-9999')" />--%>
                    <h:message for="numeroTelefone" styleClass="mensagemDetalhada"/>
                    <rich:spacer width="15px;"/>
                    <h:outputText  rendered="#{PessoaSimplificadoControle.pessoaVO.tipoPessoa == 'FO'}" style="vertical-align: middle;" styleClass="tituloCampos"
                                   value="*Contato" />
                    <h:panelGroup>
                        <h:inputText tabindex="6" id="contato" rendered="#{PessoaSimplificadoControle.pessoaVO.tipoPessoa == 'FO'}"
                                     size="14"
                                     maxlength="14"
                                     onfocus="focusinput(this);"
                                     styleClass="form" value="#{PessoaSimplificadoControle.fornecedorVO.contato}"/>
                    </h:panelGroup>
                </h:panelGroup>
                <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_CadastroPessoa_sexo}" />
                <h:panelGroup styleClass="block cb-container" layout="block">
                    <h:selectOneMenu tabindex="7" id="sexo"
                                     onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form"
                                     value="#{PessoaSimplificadoControle.pessoaVO.sexo}" >
                        <f:selectItems  value="#{PessoaSimplificadoControle.listaSelectItemSexoPessoa}" />
                    </h:selectOneMenu>
                </h:panelGroup>
                <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_CadastroPessoa_email}" />
                <h:panelGroup>
                    <h:inputText tabindex="8" id="email" size="30" maxlength="50" onblur="blurinput(this);"  onfocus="focusinput(this);"
                                  styleClass="form" value="#{PessoaSimplificadoControle.pessoaVO.email}" />
                </h:panelGroup>
                <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_CadastroPessoa_dataNascimento}" />
                <h:panelGroup>
                    <rich:jQuery id="mskDataNasc" selector="#dataNasc" timing="onload" query="mask('99/99/9999')" />
                    <h:inputText tabindex="9" id="dataNasc" value="#{PessoaSimplificadoControle.pessoaVO.dataNasc}" size="10"
                                 styleClass="form" onchange="return validar_Data(this.id);" onblur="blurinput(this);" onfocus="focusinput(this);">                                 
                        <f:convertDateTime pattern="dd/MM/yyyy" locale="#{SuperControle.localeDefault}" timeZone="#{SuperControle.timeZoneDefault}"/>
                    </h:inputText>
                    <h:message for="dataNasc"  styleClass="mensagemDetalhada"/>
                </h:panelGroup>

                <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_CadastroPessoa_endereco}" />
                <h:panelGroup>
                    <h:inputText tabindex="10" id="endereco" size="40" maxlength="40" onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form" value="#{PessoaSimplificadoControle.enderecoVO.endereco}" />
                    <rich:spacer width="15px"/>
                </h:panelGroup>

                <h:outputText  styleClass="tituloCampos" style="vertical-align: middle;" value="#{msg_aplic.prt_CadastroPessoa_bairro}" />
                <h:panelGroup>
                    <h:inputText tabindex="11"  size="20" maxlength="20" onblur="blurinput(this);"  onfocus="focusinput(this);"   styleClass="form"
                                 value="#{PessoaSimplificadoControle.enderecoVO.bairro}" />
                </h:panelGroup>

                <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_CadastroPessoa_pais}"/>
                <h:panelGroup>
                    <h:panelGroup>
                        <h:panelGroup styleClass="block cb-container" layout="block" style="display: inline-block">
                            <h:selectOneMenu tabindex="12" id="pessoapais" styleClass="campos" value="#{PessoaSimplificadoControle.pessoaVO.pais.codigo}" >
                                <f:selectItems value="#{PessoaSimplificadoControle.listaSelectItemPais}"/>
                            </h:selectOneMenu>
                        </h:panelGroup>
                        <rich:spacer width="15px;"/>
                        <h:outputText styleClass="tituloCampos" style="vertical-align: middle;" value="#{msg_aplic.prt_CadastroPessoa_uf}"/>
                        <h:panelGroup styleClass="block cb-container" layout="block" style="display: inline-block">
                            <h:selectOneMenu tabindex="12" id="estadoCEP" styleClass="campos" value="#{PessoaSimplificadoControle.cepVO.ufSigla}" >
                                <f:selectItems value="#{PessoaSimplificadoControle.listaSelectItemEstado}"/>
                                <a4j:support focus="cidade" event="onchange" action="#{PessoaSimplificadoControle.montarListaSelectItemCidade}" reRender="cidade"/>
                            </h:selectOneMenu>
                        </h:panelGroup>
                    </h:panelGroup>
                </h:panelGroup>

                <h:outputText styleClass="tituloCampos"  style="vertical-align: middle;" value="#{msg_aplic.prt_CadastroPessoa_cidade}"/>
                <h:panelGroup>
                    <h:panelGroup styleClass="block cb-container" layout="block" style="display: inline-block">
                        <h:selectOneMenu tabindex="13"  id="cidade" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" value="#{PessoaSimplificadoControle.pessoaVO.cidade.codigo}" >
                            <f:selectItems  value="#{PessoaSimplificadoControle.listaSelectItemCidade}" />
                        </h:selectOneMenu>
                    </h:panelGroup>
                    <rich:spacer width="15px;"/>
                    <a4j:commandLink id="atualizar_cidade" action="#{PessoaSimplificadoControle.montarListaSelectItemCidade}"
                                     reRender="formCadastrarNovaPessoa:cidade">
                        <i class="fa-icon-refresh"></i>
                    </a4j:commandLink>
                    <h:message for="cidade" styleClass="mensagemDetalhada"/>
                    <rich:spacer width="5px"/>
                    <a4j:commandLink id="consultaDadosCidades" reRender="formCidade"
                                     oncomplete="Richfaces.showModalPanel('panelCidade'), setFocus(formCidade,'formCidade:cidade_nome');">
                        <i class="fa-icon-plus-sign"></i>
                    </a4j:commandLink>
                    <rich:spacer width="15px"/>
                </h:panelGroup>

                <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_CadastroPessoa_numero}"/>
                    <h:inputText tabindex="14"  size="10" maxlength="10" onblur="blurinput(this);"  onfocus="focusinput(this);"   styleClass="form"
                                  value="#{PessoaSimplificadoControle.enderecoVO.numero}" />
            </h:panelGrid>

            <h:panelGrid width="80%" columnClasses="colunaDireita"  columns="2">
                <a4j:commandButton id="okPessoa" reRender="modalPanelCadastrarPessoaSimplificada, form"
                                   action="#{PessoaSimplificadoControle.gravar}"
                                   value="Gravar"
                                   styleClass="botoes nvoBt btSec btPerigo" oncomplete="#{PessoaSimplificadoControle.msgAlert}"
                                   focus="form:descricao">
                </a4j:commandButton>
                <a4j:commandButton id="cancelarPessoa"  action="#{PessoaSimplificadoControle.cancelar}"
                                   styleClass="botoes nvoBt"
                                   value="Cancelar"
                                   oncomplete="Richfaces.hideModalPanel('modalPanelCadastrarPessoaSimplificada');"
                                   reRender="form, modalPanelCadastrarPessoaSimplificada"
                                   focus="form:pessoa"/>
            </h:panelGrid>

            <h:panelGrid columns="1" width="100%" styleClass="tabMensagens">
                <h:panelGrid columns="3" width="100%" styleClass="tabMensagens">
                    <h:panelGrid columns="1" width="100%">

                            <h:outputText value=" "/>

                    </h:panelGrid>
                    <h:commandButton  rendered="#{PessoaSimplificadoControle.sucesso}" image="/imagens/sucesso.png"/>
                    <h:commandButton rendered="#{PessoaSimplificadoControle.erro}" image="/imagens/erro.png"/>
                    <h:panelGrid columns="1" width="100%">
                        <h:outputText styleClass="mensagem"  value="#{PessoaSimplificadoControle.mensagem}"/>
                        <h:outputText styleClass="mensagemDetalhada" value="#{PessoaSimplificadoControle.mensagemDetalhada}"/>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
        </a4j:form>
    </rich:modalPanel>
</a4j:outputPanel>

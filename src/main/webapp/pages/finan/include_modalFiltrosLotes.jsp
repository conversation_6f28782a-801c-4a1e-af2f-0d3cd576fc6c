<%--
    Created on : 11/07/2024
    Author     : <PERSON>
--%>

<%@page contentType="text/html" %>
<%@page pageEncoding="ISO-8859-1" %>
<%@ taglib prefix="r" uri="http://mojarra.dev.java.net/mojarra_ext" %>
<%@ taglib prefix="rick" uri="http://java.sun.com/jsf/core" %>
<%@include file="includes/imports.jsp" %>

<rich:modalPanel id="modalPanelFiltrosLotes" trimOverlayedElements="false" autosized="false"
                 shadowOpacity="true" width="974" height="620"
                 styleClass="novaModal">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="Filtros Lotes de Pagamento"/>
        </h:panelGroup>
    </f:facet>
    <f:facet name="controls">
        <h:panelGroup>
            <h:outputText
                    styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                    id="hidelinkFiltroLote"/>
            <rich:componentControl for="modalPanelFiltrosLotes"
                                   attachTo="hidelinkFiltroLote" operation="hide" event="onclick"/>
        </h:panelGroup>
    </f:facet>
    <a4j:form id="formFiltrosLotes" ajaxSubmit="true">

        <%--Títulos agrupadores--%>
        <h:panelGroup id="botoesTLLote"
                      style="display: inline-flex; padding-bottom: 10px; border-bottom:#E5E5E5 1px solid; width: 100%;"
                      layout="block">
            <a4j:commandLink id="filtrosPrincipaisConciliacao"
                             status="false" styleClass="botaoModoTimeLine ativo filtrosPrincipais"
                             onclick="trocarBloco('.filtrosPrincipais', '.painelFiltros', '.botaoModoTimeLine');">
                <h:outputText value="Filtros principais"/>
            </a4j:commandLink>
        </h:panelGroup>

        <%--PAINEL FILTROS--%>
        <h:panelGroup styleClass="painelFiltros filtrosPrincipais visivel" id="painelfiltrosinsidemodal"
                      style="width: 100%; min-height: 50vh; ">
            <div style="width: 100%; padding: 0 15px;  min-height: 50vh;">
                <h:outputText styleClass="tituloCampos  upper flex" value="Empresa"/>
                <div class="cb-container">
                    <h:selectOneMenu id="empresa"
                                     disabled="true"
                                     onfocus="focusinput(this);"
                                     value="#{MovContaControle.empresaIntegracaoKobana.codigo}">
                        <f:selectItems value="#{MovContaControle.listaSelectItemEmpresaIntegracaoKobana}"/>
                    </h:selectOneMenu>
                </div>

                <div style="padding-bottom: 10px; border-bottom:#E5E5E5 1px solid; margin-top: 15px; width: 100%;">

                        <%--DATA DE CRIAÇÃO--%>
                    <h:panelGroup id="panelDtCriacao" style="display: flex">
                        <h:outputText styleClass="tituloCampos upper flex"
                                      value="Data De Criação"/>
                    </h:panelGroup>
                    <h:panelGroup styleClass="flex" layout="block">
                        <h:panelGroup styleClass="dateTimeCustom" style="font-size: 11px !important;">
                            <rich:calendar id="dataInicioPesqLotes"
                                           value="#{MovContaControle.dataInicioPesquisaLotes}"
                                           inputSize="10"
                                           inputClass="form"
                                           buttonIcon="/imagens_flat/calendar-button.svg"
                                           oninputblur="blurinput(this);"
                                           oninputfocus="focusinput(this);"
                                           oninputchange="return validar_Data(this.id);"
                                           datePattern="dd/MM/yyyy"
                                           enableManualInput="true"
                                           zindex="2"
                                           showWeeksBar="false"/>
                        </h:panelGroup>

                        <h:outputText styleClass="tituloCampos"
                                      style="margin: 7px 9px 0px 10px;  font-size: 11px !important;"
                                      value="até"/>
                        <h:panelGroup styleClass="dateTimeCustom" style="font-size: 11px;">
                            <rich:calendar id="dataTerminoPesqLotes"
                                           value="#{MovContaControle.dataFimPesquisaLotes}"
                                           inputSize="10"
                                           buttonIcon="/imagens_flat/calendar-button.svg"
                                           inputClass="form"
                                           oninputblur="blurinput(this);"
                                           oninputfocus="focusinput(this);"
                                           oninputkeypress="return mascara2(this.form, this.id, '99/99/9999', event);"
                                           oninputchange="return validar_Data(this.id);"
                                           datePattern="dd/MM/yyyy"
                                           enableManualInput="true"
                                           zindex="2"
                                           showWeeksBar="false"/>
                        </h:panelGroup>


                        <div id="dtPreDefinida" class="cb-container"
                             style=" font-size: 11px !important;margin-left: 15px;">
                            <h:selectOneMenu id="periodoCriacaoLotes"
                                             onfocus="focusinput(this);"
                                             value="#{MovContaControle.periodoPesqLotes}">
                                <f:selectItems value="#{MovContaControle.listaPeriodosPlus}"/>
                                <a4j:support action="#{MovContaControle.alterarPeriodoCriacaoLotes}"
                                             event="onchange"
                                             reRender="dataInicioPesqLotes, dataTerminoPesqLotes"/>
                            </h:selectOneMenu>
                        </div>
                        <a4j:commandLink id="limparPeriodoLancamentoContaConc"
                                         action="#{MovContaControle.limparPeriodoPesqLotes}"
                                         reRender="formFiltrosLotes:periodoCriacaoLotes"
                                         style="position:relative; top:7px; left:5px; font-size: 11px; text-decoration: none"
                                         onclick="document.getElementById('formFiltrosLotes:dataInicioPesqLotesInputDate').value='';
                                         document.getElementById('formFiltrosLotes:dataTerminoPesqLotesInputDate').value='';"
                                         status="false">
                            <i class="fa-icon-eraser tooltipster"
                               title="Limpar período de pesquisa."></i>
                        </a4j:commandLink>

                    </h:panelGroup>
                </div>
            </div>
        </h:panelGroup>


        <%--BOTÕES--%>
        <div class="rodapeBotoes" style="padding-bottom: 20px;">
            <a4j:commandLink
                    action="#{MovContaControle.consultarLotes}"
                    id="btnConsultarLotes"
                    oncomplete="#{MovContaControle.mensagemNotificar}#{MovContaControle.abrirFecharModalFiltrosLotes}"
                    reRender="form:idLotes"
                    styleClass="pure-button pure-button-primary" style="margin-right:10px; margin-left: 15px;">
                <i style="font-size: 14px" class="fa-icon-search"></i> &nbsp
                <h:outputText style="font-size: 14px"
                              value="Pesquisar"/>
            </a4j:commandLink>

                <%--LIMPAR FILTROS--%>
            <a4j:commandLink id="limparFiltros"
                             reRender="formFiltrosLotes"
                             oncomplete="#{MovContaControle.mensagemNotificar}"
                             action="#{MovContaControle.redefinirFiltrosLotes}"
                             styleClass="pure-button">
                <h:outputText style="font-size: 14px" value="Redefinir filtros"/>

            </a4j:commandLink>

        </div>
        <script type="text/javascript" language="javascript">
            carregarTooltipster();
        </script>
        <h:panelGroup layout="block" id="containerFuncMask">
            <script>
                carregarMaskInput();
            </script>
        </h:panelGroup>
    </a4j:form>
</rich:modalPanel>



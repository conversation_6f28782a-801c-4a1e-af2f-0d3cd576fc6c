<%@include file="includes/include_imports.jsp" %>
<c:set var="moduloSession" value="1" scope="session"/>

<link href="../../css_pacto.css" rel="stylesheet" type="text/css">
<link href="../../css/financeiro.css" rel="stylesheet" type="text/css">
<link href="../../css/jquery.treeTable.css" rel="stylesheet" type="text/css">

<script type="text/javascript" src="../../script/jquery.js"></script>
<script type="text/javascript" src="../../script/jquery.treeTable.js"></script>
<script type="text/javascript" src="../../script/demonstrativoFinan.js"></script>
<script type="text/javascript" src="../../script/jquery.maskedinput-1.2.2.js"></script>
<head>
    <%@include file="includes/include_head_finan.jsp" %>
    <script type="text/javascript" language="javascript" src="${contextoFinan}/script/telaInicial.js"></script>
    <script type="text/javascript">
        jQuery.noConflict();
    </script>
</head>

<f:view>
    <jsp:include page="../../includes/include_carregando_ripple.jsp"/>
    <h:form id="form" style="overflow: initial;">
        <h:panelGroup layout="block" style="display: inline-table;width: 100%;" styleClass="fundoCinza">
            <h:panelGroup layout="block" styleClass="bgtop topoZW" rendered="#{MenuControle.apresentarTopo}">
                <jsp:include page="../../include_topo_novo.jsp" flush="true"/>
                <jsp:include page="../../include_menu_fin_flat.jsp" flush="true"/>
            </h:panelGroup>


            <h:panelGroup layout="block" styleClass="caixaCorpo">
                <h:panelGroup layout="block" style="height: 80%;width: 100%">
                    <h:panelGroup layout="block" styleClass="caixaMenuLatel">
                        <h:panelGroup layout="block" styleClass="container-imagem container-conteudo-central">
                            <h:panelGroup layout="block" styleClass="container-box zw_ui especial ">
                                <h:panelGroup styleClass="container-box-header" layout="block">
                                    <h:panelGroup layout="block" styleClass="margin-box">
                                        <h:outputText styleClass="container-header-titulo" value="#{MovContaControle.tituloTelaLancamento}"/>
                                    </h:panelGroup>
                                </h:panelGroup>
                                <h:panelGroup layout="block" styleClass="margin-box">
                                    <%@include file="includes/include_exclusaoLancamento.jsp" %>
                                </h:panelGroup>
                            </h:panelGroup>

                        </h:panelGroup>
                        <jsp:include page="includes/include_box_menulateral.jsp" flush="true"/>
                    </h:panelGroup>
                </h:panelGroup>
            </h:panelGroup>

            <jsp:include page="../../include_rodape_flat.jsp" flush="true" />
        </h:panelGroup>
    </h:form>
    <jsp:include page="../../includes/autorizacao/include_autorizacao_funcionalidade.jsp" flush="true"/>

    <a4j:outputPanel>
        <rich:modalPanel id="modalConfirmaExclusaoPararAgendamento" autosized="true" width="600" height="130"  shadowOpacity="true" styleClass="novaModal">
            <f:facet name="header">
                <h:panelGroup>
                    <h:outputText value="Parar agendamento"></h:outputText>
                </h:panelGroup>
            </f:facet>
            <f:facet name="controls">
                <h:panelGroup>
                    <h:outputText
                            styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign"
                            id="hidelinkmodalConfirmaExclusaoPararAgendamento"/>
                    <rich:componentControl for="modalConfirmaExclusaoPararAgendamento"
                                           attachTo="hidelinkmodalConfirmaExclusaoPararAgendamento" operation="hide"  event="onclick" />
                </h:panelGroup>
            </f:facet>
            <h:form id="formConfirmaExclusaoPararAgendamento">
                <rich:panel>
                    <h:outputText styleClass="tituloDemonstrativo"
                                  value="#{msg_aplic.prt_Finan_Lancamentos_confirmaExclusaoPararAgendamento}"/>

                </rich:panel>
                <center style="margin: 15px;">
                    <a4j:commandLink id="sim"
                                     action="#{MovContaControle.excluirFinalizandoAgendamento}"
                                     reRender="panelMensagem, panelAutorizacaoFuncionalidade, formConfirmaExclusaoPararAgendamento"
                                     oncomplete="Richfaces.hideModalPanel('modalConfirmaExclusaoPararAgendamento'); #{MovContaControle.msgAlert}"
                                     value="#{msg_aplic.prt_Finan_Lancamentos_confirmaExclusaoPararAgendamentoConfirmar}"
                                     styleClass="botoes nvoBt">
                    </a4j:commandLink>

                    <a4j:commandLink id="nao"
                                     action="#{MovContaControle.operacaoExclusaoSemValidar}"
                                     oncomplete="Richfaces.hideModalPanel('modalConfirmaExclusaoPararAgendamento'); #{MovContaControle.msgAlert}"
                                     reRender="panelMensagem, panelAutorizacaoFuncionalidade, formConfirmaExclusaoPararAgendamento" value="#{msg_aplic.prt_Finan_Lancamentos_confirmaExclusaoPararAgendamentoSomente}"
                                     styleClass="botoes nvoBt btSec">
                    </a4j:commandLink>


                </center>
            </h:form>
        </rich:modalPanel>
    </a4j:outputPanel>

</f:view>
<%@include file="includes/imports.jsp" %>
<%@page pageEncoding="ISO-8859-1" %>
<style>
    .textoEsquerda {
        text-align: left !important;
    }
</style>
<h:panelGroup
        rendered="#{GestaoRecebiveisControle.formaPagamentoSelecionada.codigo > 0 and GestaoRecebiveisControle.formaPagamentoSelecionada.tipoFormaPagamento != 'CA' and GestaoRecebiveisControle.formaPagamentoSelecionada.tipoFormaPagamento != 'CH'}">

    <a4j:commandButton actionListener="#{EdicaoPagamentoControle.prepareReciboGestaoRecebiveis}"
                       id="ideditarreciboDEBITO" style="display: none;"
                       oncomplete="#{EdicaoPagamentoControle.msgAlert}#{EdicaoPagamentoControle.mensagemNotificar}">
        <f:attribute name="codigoMP" value="#{GestaoRecebiveisControle.pagamentoSelecionado}"/>
        <f:attribute name="tipoEdicao" value="CD"/>
    </a4j:commandButton>
    <a4j:commandButton id="idhistoricocartaoDEBITO" style="display: none;" reRender="modalHistoricoCartao"
                       actionListener="#{GestaoRecebiveisControle.selecionarCartaoDebitoHistorico}"
                       oncomplete="Richfaces.showModalPanel('modalHistoricoCartao')">
    </a4j:commandButton>


    <h:panelGroup id="paineltotaloutros1" layout="block" styleClass="painelGR cima">
        <a4j:commandLink styleClass="texto-cor-azul linkPadrao tooltipster texto-size-14 inlineBlock"
                         id="btnExportXLSOutros" style="float: left; margin: 1em;"
                         actionListener="#{GestaoRecebiveisControle.prepararExportarRecebiceis}"
                         oncomplete="abrirPopup('../../UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/vnd.ms-excel','Transacoes', 800,200);#{ExportadorListaControle.msgAlert}"
                         accesskey="2">
            <f:attribute name="lista" value="#{GestaoRecebiveisControle.listaOutros}"/>
            <f:attribute name="tipo" value="xls"/>
            <f:attribute name="prefixo" value="RecebiveisOutros"/>
            <f:attribute name="#{GestaoRecebiveisControle.nameAtributoRecebiveisOutros}"
                         value="#{GestaoRecebiveisControle.valueAtributoRecebiveisOutros}"/>
            <i class="fa-icon-file-excel-o"></i>
        </a4j:commandLink>
        <div class="inlineBlock" style="float: left;">
            <span class="cinza texto-size-14">
                Ordenar por:
            </span>
            <span class="cinza negrito texto-size-14">
                <h:selectOneMenu id="ordenaColunaPorOutros" onblur="blurinput(this);"
                                 onfocus="focusinput(this);" styleClass="cinza texto-size-14"
                                 value="#{GestaoRecebiveisControle.ordenaColunaPor}">
                    <f:selectItem itemValue="" itemLabel="" itemDescription=""/>
                    <f:selectItem itemValue="matriculaPagador" itemLabel="Matrícula" itemDescription="Matrícula"/>
                    <f:selectItem itemValue="cpfPagador" itemLabel="CPF" itemDescription="CPF"/>
                    <f:selectItem itemValue="nomePagador" itemLabel="Nome Pagador" itemDescription="Nome Pagador"/>
                    <f:selectItem itemValue="dataLancamento" itemLabel="Dt. Lançamento" itemDescription="Dt. Lançamento"/>
                    <f:selectItem itemValue="dataPagamento" itemLabel="Dt. Compensação" itemDescription="Dt. Compensação"/>
                    <f:selectItem itemValue="autorizacaoCartao" itemLabel="Autorização" itemDescription="Autorização"/>
                    <f:selectItem itemValue="nomeOperadoraCartao" itemLabel="Operadora Cartão" itemDescription="Operadora Cartão"/>
                    <f:selectItem itemValue="valorTotal" itemLabel="Valor" itemDescription="Valor"/>
                    <f:selectItem itemValue="contaFinanceiro" itemLabel="Conta" itemDescription="Conta"/>
                    <f:selectItem itemValue="dataMovimento" itemLabel="Data Movimentação" itemDescription="Data Movimentação"/>
                    <a4j:support event="onchange" action="#{GestaoRecebiveisControle.ordenaColunaDataTable}"
                                 reRender="paineltotaloutros1, listaOutros"/>
                </h:selectOneMenu>
            </span>
            <span class="cinza negrito texto-size-14">
                <h:selectOneMenu id="ordemDaColunaOutros" onblur="blurinput(this);"
                                 onfocus="focusinput(this);" styleClass="cinza texto-size-14"
                                 value="#{GestaoRecebiveisControle.ordemDaColuna}">
                    <f:selectItem itemValue="" itemLabel="" itemDescription=""/>
                    <f:selectItem itemValue="asc" itemLabel="Crescente" itemDescription="Crescente"/>
                    <f:selectItem itemValue="desc" itemLabel="Decrescente" itemDescription="Decrescente"/>
                    <a4j:support event="onchange" action="#{GestaoRecebiveisControle.ordenaColunaDataTable}"
                                 reRender="paineltotaloutros1, listaOutros"/>
                </h:selectOneMenu>
            </span>
        </div>
        <div class="inlineBlock">
            <span class="cinza texto-size-14">
                Total selecionados =
            </span>
            <span class="cinza texto-size-14 negrito totalOutrosSelecionados">
                <h:outputText value="#{GestaoRecebiveisControle.qtdeTotalVista}"/>
            </span>
            <span class="cinza texto-size-14">
                Valor total=
            </span>
            <span class="cinza texto-size-14 negrito valorOutrosSelecionados" style="margin-right: 10px;">
                ${GestaoRecebiveisControle.empresaLogado.moeda} <h:outputText
                    value="#{GestaoRecebiveisControle.totalVista}">
                <f:converter converterId="FormatadorNumerico"/>
            </h:outputText>
            </span>
        </div>

        <a4j:commandLink style="margin-left:10px;"
                         id="btnModificarCompensacaoBoletoSup"
                         oncomplete="#{GestaoRecebiveisControle.msgAlert}"
                         action="#{GestaoRecebiveisControle.abrirModalAlteracaoCompensacaoBoleto}"
                         rendered="#{GestaoRecebiveisControle.exibirModificarCompensacaoBoleto && GestaoRecebiveisControle.empresasSelecionadas.size() <= 1}"
                         reRender="modalAlterarCompensacaoBoleto"
                         title="Modificar a data de compensação dos boletos selecionados"
                         styleClass="pure-button pure-button-small tooltipster texto-size-14 inlineBlock">
            <i class="fa-icon-calendar"></i>&nbsp Data de compensação
        </a4j:commandLink>

        <a4j:commandLink style="margin-left:10px;"
                         oncomplete="#{GestaoRecebiveisControle.msgAlert}"
                         action="#{GestaoRecebiveisControle.abrirModalAlteracaoCompensacaoDebito}"
                         rendered="#{GestaoRecebiveisControle.exibirAutorizacao && GestaoRecebiveisControle.empresasSelecionadas.size() <= 1}"
                         reRender="modalAlterarCompensacao"
                         title="Modificar a data de compensação dos cartões selecionados"
                         styleClass="pure-button pure-button-small tooltipster texto-size-14 inlineBlock">
            <i class="fa-icon-calendar"></i>&nbsp Data de compensação
        </a4j:commandLink>

        <a4j:commandLink id="depositaroutros2" style="margin: 0 10px;"
                         oncomplete="#{GestaoRecebiveisControle.onCompleteDepositar}"
                         action="#{GestaoRecebiveisControle.salvaEscolha}" reRender="panelDeposito"
                         rendered="#{!GestaoRecebiveisControle.contaCorrente && ConfiguracaoFinanceiroControle.confFinanceiro.usarMovimentacaoContas && GestaoRecebiveisControle.empresasSelecionadas.size() <= 1}"
                         styleClass="pure-button pure-button-small pure-button-primary  texto-size-14 inlineBlock">
            <i class="fa-icon-usd"></i>&nbsp Movimentar
        </a4j:commandLink>
    </h:panelGroup>

    <rich:dataTable id="listaOutros" width="100%" styleClass="tabelaDados tudo step3" style="border-color:#FFF"
                    columnClasses="colunaEsquerda, colunaEsquerda,centralizado,centralizado,centralizado,centralizado,colunaDireita,centralizado,centralizado,colunaDireita"
                    headerClass="subordinado" rowClasses="linhaImpar, linhaPar"

                    value="#{GestaoRecebiveisControle.listaOutros}" var="movp">

        <rich:column style="border-color:#FFF" id="empresaListaCartoesDebito" styleClass="textoEsquerda" rendered="#{GestaoRecebiveisControle.empresasSelecionadas.size() > 1}">
            <f:facet name="header">
                <h:outputText style="font-weight: bold; font-size:9px;"
                              value="Empresa"/>
            </f:facet>
            <h:outputText value="#{movp.empresa.nome}" style="font-weight: bold; font-size:9px;" styleClass="blue"/>
        </rich:column>

        <rich:column style="border-color:#FFF" id="nomePagyouador">
            <f:facet name="header">
                <h:outputText style="font-weight: bold; font-size:9px;"
                              value="#{msg_aplic.prt_RelatorioCliente_Matricula}"/>
            </f:facet>
            <a4j:commandLink action="#{GestaoRecebiveisControle.irParaTelaClienteMp}"
                             style="font-weight: bold; font-size:9px;" styleClass="blue"
                             value="#{movp.matriculaPagador}"
                             rendered="#{!movp.consumidor}"
                             oncomplete="abrirPopup('../../clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);"/>

            <h:outputText style="font-weight: bold; font-size:9px;" styleClass="blue"
                          value="" rendered="#{movp.consumidor}"/>

        </rich:column>


        <rich:column style="border-color:#FFF" id="nomePagadorCpf" styleClass="textoEsquerda">
            <f:facet name="header">
                <h:outputText style="font-weight: bold; font-size:9px;"
                              value="#{GestaoRecebiveisControle.displayIdentificadorFront[0]}"/>
            </f:facet>
            <a4j:commandLink action="#{GestaoRecebiveisControle.irParaTelaClienteMp}"
                             style="font-weight: bold; font-size:9px;" styleClass="blue"
                             value="#{movp.cpfPagador}"
                             rendered="#{!movp.consumidor}"
                             oncomplete="abrirPopup('../../clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);"/>

            <h:outputText style="font-weight: bold; font-size:9px;" styleClass="blue"
                          value="#{movp.cpfPagador}" rendered="#{movp.consumidor}"/>

        </rich:column>

        <rich:column style="border-color:#FFF" id="nomePagadorav" styleClass="textoEsquerda">
            <f:facet name="header">
                <h:outputText style="font-weight: bold; font-size:9px;"
                              value="#{msg_aplic.prt_Finan_GestaoRecebiveis_nome}"/>
            </f:facet>

            <a4j:commandLink action="#{GestaoRecebiveisControle.irParaTelaClienteMp}" id="nomepessoaoutros"
                             style="font-weight: bold; font-size:9px;" styleClass="blue, tooltipster"
                             value="#{movp.nomePagador}" rendered="#{!movp.consumidor}"
                             title="#{movp.responsavelPagador != '' || null ? movp.responsavelPagador : ''}"
                             oncomplete="abrirPopup('../../clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);"/>

            <h:outputText style="font-weight: bold; font-size:9px;" styleClass="blue" id="nomeconsumidor"
                          value="#{movp.nomePagador}" rendered="#{movp.consumidor}"/>

        </rich:column>

        <rich:column style="border-color:#FFF" id="dataLancamento" styleClass="textoEsquerda">
            <f:facet name="header">
                <h:outputText style="font-weight: bold; font-size:9px;"
                              value="#{msg_aplic.prt_Finan_GestaoRecebiveis_dataLancamento}"/>
            </f:facet>
            <h:outputText style="font-weight: bold; font-size:9px;"
                          styleClass="blue" value="#{movp.dataLancamento}">
                <f:convertDateTime pattern="dd/MM/yyyy"/>
            </h:outputText>
        </rich:column>

        <rich:column style="border-color:#FFF" id="dataPagamento" styleClass="textoEsquerda">
            <f:facet name="header">
                <h:outputText style="font-weight: bold; font-size:9px;"
                              value="#{msg_aplic.prt_Finan_GestaoRecebiveis_dataCompensacao}"/>
            </f:facet>
            <div style="display: inline-flex;">
            <h:outputText style="font-weight: bold; font-size:9px;"
                          styleClass="blue" value="#{movp.dataPagamento}">
                <f:convertDateTime pattern="dd/MM/yyyy"/>
            </h:outputText>
            <h:graphicImage value="/imagens/antecipacao.svg"
                            rendered="#{movp.antecipacao}"
                            style="width:11px;height:11px; margin-left: 6px; margin-top: -2px"
                            styleClass="tooltipster"
                            title="Este recebimento foi antecipado na adquirente e teve sua data de compensação alterada automaticamente.</br>
                                                    Data de compensação original: #{movp.dataPgtoOriginalAntesDaAntecipacaoApresentar}">
            </h:graphicImage>
            </div>
        </rich:column>

        <rich:column style="border-color:#FFF"
                     rendered="#{GestaoRecebiveisControle.exibirAutorizacao}"
                     id="autorizacaoCartao"
                     styleClass="textoEsquerda">
            <f:facet name="header">
                <h:outputText style="font-weight: bold; font-size:9px;"
                              value="#{msg_aplic.prt_Finan_GestaoRecebiveis_Autorizacao}"/>
            </f:facet>
            <h:outputText style="font-weight: bold; font-size:9px;"
                          styleClass="blue" value="#{movp.autorizacaoCartao}">
            </h:outputText>
        </rich:column>

        <rich:column style="border-color:#FFF" id="nsuoutros"
                     rendered="#{GestaoRecebiveisControle.exibirNSU}"
                     styleClass="textoEsquerda">
            <f:facet name="header">
                <h:outputText style="font-weight: bold; font-size:9px;"
                              value="#{msg_aplic.prt_Finan_GestaoRecebiveis_NSU}"/>
            </f:facet>
            <h:outputText style="font-weight: bold; font-size:9px;"
                          styleClass="blue" value="#{movp.nsu}"/>
        </rich:column>

        <rich:column style="border-color:#FFF" id="Operadora" rendered="#{GestaoRecebiveisControle.exibirAutorizacao}" styleClass="textoEsquerda">
            <f:facet name="header">
                <h:outputText style="font-weight: bold; font-size:9px;"
                              value="#{msg_aplic.prt_OperadoraCartao_tituloForm}"/>
            </f:facet>
            <h:outputText style="font-weight: bold; font-size:9px;"
                          styleClass="blue tooltipster" value="#{movp.operadoraCartaoVO.descricao}" title="Adquirente: #{empty movp.adquirenteVO.nome ? ' Não informada' : movp.adquirenteVO.nome}"/>
        </rich:column>
        <rich:column style="border-color:#FFF" id="valor" styleClass="textoEsquerda">
            <f:facet name="header">
                <h:outputText style="font-weight: bold; font-size:9px;"
                              value="#{msg_aplic.prt_Finan_GestaoRecebiveis_valor}"/>
            </f:facet>
            <h:outputText style="font-weight: bold; font-size:9px;"
                          styleClass="blue" value="#{movp.valorTotal_Apresentar}">
            </h:outputText>
        </rich:column>

        <rich:column style="border-color:#FFF" styleClass="textoEsquerda">
            <f:facet name="header">
                <h:outputText style="font-weight: bold; font-size:9px;"
                              value="#{msg_aplic.prt_Finan_GestaoRecebiveis_conta}"/>
            </f:facet>
            <h:outputText style="font-weight: bold; font-size:9px;"
                          styleClass="blue" value="#{movp.contaFinanceiro}">

            </h:outputText>
        </rich:column>

        <rich:column style="border-color:#FFF" styleClass="textoEsquerda">
            <f:facet name="header">
                <a4j:commandLink action="#{GestaoRecebiveisControle.ordenaColunaDataTable}"
                                 style="font-weight:bold;font-size:9px;color:#777;width:auto;"
                                 value="Data Movimentação">
                    <a4j:actionparam name="ordenaNomeColuna" value="dataMovimento"></a4j:actionparam>
                </a4j:commandLink>
            </f:facet>
            <h:panelGroup id="panelDtMovimento" style="display: inline-flex; margin-left: 5px;">
                <h:outputText style="font-weight: bold; font-size:9px;"
                              styleClass="blue" value="#{movp.dataMovimento_Apresentar}">
                </h:outputText>
                <a4j:commandLink id="excluirLancOrigemRecebiveis"
                                 rendered="#{movp.dataMovimento_Apresentar != ''}"
                                 style="margin-left: 3px; margin-top: -1px; text-decoration: none"
                                 actionListener="#{GestaoRecebiveisControle.abrirModalExcluirLancamentoFinanceiro}"
                                 oncomplete="#{GestaoRecebiveisControle.msgAlert}#{GestaoRecebiveisControle.mensagemNotificar}"
                                 reRender="modalExcluirDepositoFinanceiro">
                    <i class="fa-icon-trash tooltipster"
                       title="Excluir Depósito"
                       style="font-size: 12px;"/>
                    <f:attribute name="codMovConta" value="#{movp.movconta}"/>
                </a4j:commandLink>
            </h:panelGroup>
        </rich:column>

        <rich:column style="border-color:#FFF" headerClass="colunaDireita"
                     rendered="#{!GestaoRecebiveisControle.contaCorrente
                                    && (ConfiguracaoFinanceiroControle.confFinanceiro.usarMovimentacaoContas || GestaoRecebiveisControle.exibirAutorizacao)
                                    && GestaoRecebiveisControle.empresasSelecionadas.size() <= 1}">
            <f:facet name="header">
                <h:panelGroup styleClass="chk-fa-container inline" layout="block"
                              rendered="#{ConfiguracaoFinanceiroControle.confFinanceiro.usarMovimentacaoContas}">
                    <h:selectBooleanCheckbox
                            id="marcarTodosRecebiveisOutros"
                            value="#{GestaoRecebiveisControle.todosPagamentosMarcados}">
                        <a4j:support event="onclick" process="this" ajaxSingle="true"
                                     action="#{GestaoRecebiveisControle.marcaDesmarcaVista}"
                                     reRender="panelMensagem, totalSelecionado3, listaOutros, qtdeSelecionado3, paineltotaloutros1, paineltotaloutros2"/>
                    </h:selectBooleanCheckbox>
                    <span></span>
                </h:panelGroup>

            </f:facet>


            <h:panelGroup styleClass="chk-fa-container inline" layout="block" style="margin-right: 5px"
                          rendered="#{(not GestaoRecebiveisControle.contaCorrente
                          and (ConfiguracaoFinanceiroControle.confFinanceiro.usarMovimentacaoContas
                          or GestaoRecebiveisControle.exibirAutorizacao)) &&
                           (!((movp.contaFinanceiroMin != '') && ((movp.formaPagamento.tipoFormaPagamento eq 'PX') or (movp.formaPagamento.tipoFormaPagamento eq 'AV'))))}">
                <h:selectBooleanCheckbox value="#{movp.movPagamentoEscolhidaFinan}"
                >
                    <a4j:support event="onclick" process="this" ajaxSingle="true"
                                 action="#{GestaoRecebiveisControle.calcularTotalVista}"
                                 reRender="panelMensagem,totalSelecionado3,qtdeSelecionado3, paineltotaloutros1, paineltotaloutros2"/>
                </h:selectBooleanCheckbox>
                <span></span>
            </h:panelGroup>

            <a4j:commandLink title="Editar Código de Autorização"
                             reRender="panelAutorizacaoFuncionalidade"
                             rendered="#{GestaoRecebiveisControle.exibirAutorizacao}"
                             actionListener="#{EdicaoPagamentoControle.prepareRecibo2}"
                             styleClass="tooltipster inline"
                             oncomplete="#{EdicaoPagamentoControle.mensagemNotificar}">
                <f:attribute name="movpagamentoVO" value="#{movp}"/>
                <f:attribute name="tipoEdicao" value="CD"/>
                <i class="fa-icon-edit"></i>
            </a4j:commandLink>

            <a4j:commandLink title="Historico do Cartão" reRender="modalHistoricoCartao"
                             rendered="#{GestaoRecebiveisControle.exibirAutorizacao}"
                             actionListener="#{GestaoRecebiveisControle.selecionarCartaoDebitoHistorico}"
                             oncomplete="Richfaces.showModalPanel('modalHistoricoCartao')"
                             styleClass="tooltipster inline">
                <f:attribute name="movp" value="#{movp}"/>
                <i class="fa-icon-hourglass"></i>
            </a4j:commandLink>


            <h:panelGroup rendered="#{not empty movp.mensagemDescricaoCentroCustos}">
                <i class="fa-icon-info-sign tooltipster" title="${movp.mensagemDescricaoCentroCustos}"></i>
            </h:panelGroup>
        </rich:column>
    </rich:dataTable>

    <h:panelGroup id="paineltotaloutros2" layout="block" styleClass="painelGR baixo">
        <a4j:commandLink oncomplete="#{GestaoRecebiveisControle.msgAlert}"
                         action="#{GestaoRecebiveisControle.realizarConsultaLogObjetoSelecionado}"
                         value="Log Alterações Data Compensação"
                         styleClass="texto-cor-azul linkPadrao inlineBlock"
                         style="float: left; margin-left: 10px;"
                         rendered="#{GestaoRecebiveisControle.exibirAutorizacao}"/>

        <div class="inlineBlock">
            <span class="cinza texto-size-14">
                Total selecionados =
            </span>
            <span class="cinza texto-size-14 negrito totalOutrosSelecionados">
                <h:outputText value="#{GestaoRecebiveisControle.qtdeTotalVista}"/>
            </span>
            <span class="cinza texto-size-14">
                Valor total=
            </span>
            <span class="cinza texto-size-14 negrito valorOutrosSelecionados" style="margin-right: 10px;">
                ${GestaoRecebiveisControle.empresaLogado.moeda} <h:outputText
                    value="#{GestaoRecebiveisControle.totalVista}">
                <f:converter converterId="FormatadorNumerico"/>
            </h:outputText>
            </span>
        </div>

        <a4j:commandLink style="margin-left:10px;"
                         id="btnModificarCompensacaoBoletoInf"
                         oncomplete="#{GestaoRecebiveisControle.msgAlert}"
                         action="#{GestaoRecebiveisControle.abrirModalAlteracaoCompensacaoBoleto}"
                         rendered="#{GestaoRecebiveisControle.exibirModificarCompensacaoBoleto && GestaoRecebiveisControle.empresasSelecionadas.size() <= 1}"
                         reRender="modalAlterarCompensacaoBoleto"
                         title="Modificar a data de compensação dos boletos selecionados"
                         styleClass="pure-button pure-button-small tooltipster texto-size-14 inlineBlock">
            <i class="fa-icon-calendar"></i>&nbsp Data de compensação
        </a4j:commandLink>

        <a4j:commandLink style="margin-left:10px;" id="datacompensacaocartaodebito2"
                         oncomplete="#{GestaoRecebiveisControle.msgAlert}"
                         action="#{GestaoRecebiveisControle.abrirModalAlteracaoCompensacaoDebito}"
                         rendered="#{GestaoRecebiveisControle.exibirAutorizacao && GestaoRecebiveisControle.empresasSelecionadas.size() <= 1}"
                         reRender="modalAlterarCompensacao"
                         title="Modificar a data de compensação dos cartões selecionados"
                         styleClass="pure-button pure-button-small tooltipster texto-size-14 inlineBlock">
            <i class="fa-icon-calendar"></i>&nbsp Data de compensação
        </a4j:commandLink>

        <a4j:commandLink id="iddepositaroutros1" style="margin: 0 10px;"
                         oncomplete="#{GestaoRecebiveisControle.onCompleteDepositar}"
                         action="#{GestaoRecebiveisControle.salvaEscolha}" reRender="panelDeposito"
                         rendered="#{!GestaoRecebiveisControle.contaCorrente && ConfiguracaoFinanceiroControle.confFinanceiro.usarMovimentacaoContas && GestaoRecebiveisControle.empresasSelecionadas.size() <= 1}"
                         styleClass="pure-button pure-button-small pure-button-primary  texto-size-14 inlineBlock">
            <i class="fa-icon-usd"></i>&nbsp Movimentar
        </a4j:commandLink>
    </h:panelGroup>


</h:panelGroup>

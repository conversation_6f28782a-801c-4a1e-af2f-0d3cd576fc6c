<%@page contentType="text/html" pageEncoding="ISO-8859-1" %>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@include file="includes/include_imports.jsp" %>
<%@taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>

<link href="../../css_pacto.css" rel="stylesheet" type="text/css">
<link href="../../css/financeiro.css" rel="stylesheet" type="text/css">
<link href="../../css/otimize.css" rel="stylesheet" type="text/css">
<link href="../../css/jquery.treeTable.css" rel="stylesheet" type="text/css">

<script type="text/javascript" src="../../script/jquery.js"></script>
<script type="text/javascript" src="../../script/jquery.treeTable.js"></script>
<script type="text/javascript" src="../../script/demonstrativoFinan.js"></script>
<script type="text/javascript" src="../../script/jquery.maskedinput-1.7.6.js"></script>
<f:view>
    <html>

        <!-- Inclui o elemento HEAD da página -->
        <head>
            <%@include file="includes/include_head_finan.jsp" %>
            <script type="text/javascript" language="javascript" src="${contextoFinan}/script/telaInicial.js"></script>
            <script type="text/javascript" language="javascript">
                $.noConflict();
            </script>
        </head>
        <body onload="atualizarTreeViewFiltros();
                    atualizarTreeViewDF()" >
        <h:panelGroup layout="block" style="display: inline-table;width: 100%;" styleClass="fundoCinza">
            <h:form id="formTopo" style="overflow-x: visible !important;">
            <h:panelGroup layout="block" styleClass="bgtop topoZW" rendered="#{MenuControle.apresentarTopo}">
                <jsp:include page="../../include_topo_novo.jsp" flush="true"/>
                <jsp:include page="../../include_menu_fin_flat.jsp" flush="true"/>
                <rich:jQuery selector=".item3" query="addClass('menuItemAtual')"/>
            </h:panelGroup>
            </h:form>
            <h:panelGroup layout="block" styleClass="caixaCorpo">
                <h:panelGroup layout="block" style="height: 80%;width: 100%">
                    <h:panelGroup layout="block" styleClass="caixaMenuLatel">

                            <h:panelGroup layout="block" styleClass="container-imagem container-conteudo-central">
                                <h:panelGroup layout="block" styleClass="container-box zw_ui especial ">
                                    <h:panelGroup styleClass="container-box-header" layout="block"  >
                                        <h:panelGroup layout="block" styleClass="margin-box" style="display:flex; justify-content:space-between; align-items: center; height: 59px" >

                                            <h:panelGroup styleClass="text" layout="block" >
                                            <h:outputText styleClass="container-header-titulo"
                                                          value="Fechamento de Caixa por Plano de Contas"/>

                                            <h:outputLink styleClass="linkWiki"
                                                          value="#{SuperControle.urlBaseConhecimento}o-que-e-o-relatorio-demonstrativo-financeiro/"
                                                          title="Clique e saiba mais: Fechamento de Caixa por Plano de Contas"
                                                          target="_blank" >
                                                <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                                            </h:outputLink>
                                            </h:panelGroup>

                                            <h:outputLink style=" float: right; vertical-align: middle"
                                                          styleClass="tudo texto-cor-azul linkPadrao tooltipster"
                                                          value="#{SuperControle.urlBaseConhecimento}quais-relatorios-financeiros-sao-equivalentes/"
                                                          title="Relatórios Equivalentes" target="_blank">
                                                <h:graphicImage  value="/faces/beta/imagens/mini-icons-wiki.png"
                                                                 style="width:26px;height:26px;opacity: 0.4">
                                                </h:graphicImage>
                                            </h:outputLink>

                                        </h:panelGroup>

                                        </h:panelGroup>
                                    <h:panelGroup layout="block" styleClass="margin-box">
                                        <h:form id="formDF">

                                            <!-- ------------------------ Intervalo e Resultado da Consulta   ------------------------------- -->

                                            <table bgcolor="#F5F5F5" id="rConsulta" width="100%"
                                                   style="display: block;">
                                                <tr>
                                                    <td width="100%">
                                                        <h:panelGrid id="painelDatas"

                                                                     columns="1" rowClasses="linhaPar">
                                                            <h:panelGroup>
                                                                <h:outputText styleClass="tituloDemonstrativo"
                                                                              value="Período: " />

                                                                <h:panelGroup id="panelGroupDatas" >
                                                                    <h:panelGroup>
                                                                        <rich:calendar id="dataInicioRelatorio"
                                                                                       inputSize="#{FechamentoCaixaPlanoContaControle.inputSize}"
                                                                                       inputClass="form"
                                                                                       showWeeksBar="false"
                                                                                       oninputfocus="focusinput(this);"
                                                                                       oninputblur="blurinput(this);"
                                                                                       oninputchange="return validar_Data(this.id);"
                                                                                       oninputkeypress="#{FechamentoCaixaPlanoContaControle.oninputkeypress}"
                                                                                       datePattern="#{FechamentoCaixaPlanoContaControle.datePattern}"
                                                                                       enableManualInput="true"
                                                                                       value="#{FechamentoCaixaPlanoContaControle.dataInicioRelatorio}">
                                                                        </rich:calendar>
                                                                        <h:message for="dataInicioRelatorio" styleClass="mensagemDetalhada" />


                                                                    </h:panelGroup>
                                                                    <rich:spacer width="3"></rich:spacer>
                                                                    <h:outputText styleClass="tituloDemonstrativo"
                                                                                  value="#{msg_aplic.prt_ate} " />
                                                                    <h:panelGroup>

                                                                        <rich:calendar id="dataFimRelatorio"
                                                                                       inputSize="#{FechamentoCaixaPlanoContaControle.inputSize}"
                                                                                       inputClass="form"
                                                                                       showWeeksBar="false"
                                                                                       oninputfocus="focusinput(this);"
                                                                                       oninputblur="blurinput(this);"
                                                                                       oninputchange="return validar_Data(this.id);"
                                                                                       oninputkeypress="#{FechamentoCaixaPlanoContaControle.oninputkeypress}"
                                                                                       datePattern="#{FechamentoCaixaPlanoContaControle.datePattern}"
                                                                                       enableManualInput="true"
                                                                                       value="#{FechamentoCaixaPlanoContaControle.dataFimRelatorio}">
                                                                        </rich:calendar>
                                                                        <h:message for="dataFimRelatorio" styleClass="mensagemDetalhada" />
                                                                        <rich:jQuery id="mskData" selector=".rich-calendar-input" timing="onload" query="mask('99/99/9999')" />
                                                                    </h:panelGroup>

                                                                </h:panelGroup>
                                                                <rich:spacer width="8"></rich:spacer>
                                                                <h:panelGroup id="panelEmpresa"
                                                                              rendered="#{FechamentoCaixaPlanoContaControle.mostrarCampoEmpresa}">
                                                                    <h:outputText styleClass="tituloDemonstrativo"
                                                                                  value="Empresa: "></h:outputText>

                                                                    <h:selectOneMenu id="empresa" styleClass="form"
                                                                                     onblur="blurinput(this);" onfocus="focusinput(this);"
                                                                                     value="#{FechamentoCaixaPlanoContaControle.empresaVO.codigo}">
                                                                        <f:selectItems
                                                                                value="#{FechamentoCaixaPlanoContaControle.listaSelectItemEmpresa}" />
                                                                        <a4j:support action="#{FechamentoCaixaPlanoContaControle.obterEmpresaEscolhida}" event="onchange"/>
                                                                    </h:selectOneMenu>
                                                                </h:panelGroup>
                                                                <rich:spacer width="8"></rich:spacer>
                                                                <h:panelGroup>
                                                                    <a4j:commandButton
                                                                            action="#{FechamentoCaixaPlanoContaControle.gerarDemonstrativoComThread}"
                                                                            value="Visualizar Relatório"
                                                                            style="vertical-align:middle"
                                                                            image="../../imagens/btn_VisualizarRelatorio.png"
                                                                            reRender="panelDfTreeView, panelMensagens, panelTreeViewDF, panelImprimirRelatorio, panelMensagens2, painelDatas"
                                                                            oncomplete="atualizarTreeViewDF();alterarAlturaMenuLateral();"
                                                                            id="demonstrativoFinanComThread" />


                                                                </h:panelGroup>
                                                            </h:panelGroup>

                                                        </h:panelGrid> <h:panelGrid width="100%" id="panelDfTreeView">
                                                        <h:panelGroup id="panelMostrarRelatorio"
                                                                      rendered="#{!FechamentoCaixaPlanoContaControle.houveErroNoRelatorio}">
                                                            <c:set var="tamColNomeAgrupador" value="25%" scope="request" />
                                                            <c:set var="tamColNomeMes" value="10%" scope="request" />


                                                        </h:panelGroup>

                                                    </h:panelGrid></td>
                                                </tr>

                                            </table>

                                            </br>
                                            <h:panelGrid id="panelMensagens2" columns="1" width="100%">
                                                <h:outputText styleClass="mensagemDetalhada"
                                                              id="mensagemDetalhada3"
                                                              value="#{FechamentoCaixaPlanoContaControle.mensagemDetalhada}" />
                                            </h:panelGrid>


                                            <!-------------------------- Começo relatório TreeView  --------------------------------->
                                            <%@include file="includes/include_TreeViewCaixaPlanoConta.jsp" %>
                                            <h:panelGroup id="panelImprimirRelatorio">
                                                <c:if test="${not empty FechamentoCaixaPlanoContaControle.listaDFBrowser}">
                                                    <br>
                                                    <div style="text-align: center;">
                                                        <a4j:commandButton oncomplete="abrirPopup('relatorioCaixaPorPlanoConta.jsp', 'RelatórioDemonstrativoFinanceiro', 780, 595);"
                                                                           image="../../imagens/btn_VisualizarImpressao.png"	value="Visualizar Impressão">
                                                        </a4j:commandButton>
                                                    </div>
                                                </c:if>
                                            </h:panelGroup>
                                            <!-- ------------------------ Fim relatório TreeView  ------------------------------- -->

                                            <h:inputHidden id="idMesSelecionado"
                                                           value="#{FechamentoCaixaPlanoContaControle.mesSelecionado}" />
                                            <h:inputHidden id="idTodosMeses"
                                                           value="#{FechamentoCaixaPlanoContaControle.totalMeses}" />
                                            <h:inputHidden id="idCodigoSelecionado"
                                                           value="#{FechamentoCaixaPlanoContaControle.codigoAgrupadorSelecionado}" />
                                            <h:inputHidden id="idTipoListaMostrar"
                                                           value="#{FechamentoCaixaPlanoContaControle.tipoListaLancamentosMostrar}" />
                                            <a4j:commandButton style="visibility: hidden;"
                                                               oncomplete="Richfaces.showModalPanel('modalLancamentos')"
                                                               id="botaoVisualizarLancamentos" reRender="modalLancamentos"
                                                               action="#{FechamentoCaixaPlanoContaControle.editarLancamento}">
                                            </a4j:commandButton>

                                            <h:inputHidden id="CControle"
                                                           value="#{FechamentoCaixaPlanoContaControle.openConsulta}" />
                                            <h:inputHidden id="OCControle"
                                                           value="#{FechamentoCaixaPlanoContaControle.openOpcoes}" />
                                            <h:inputHidden id="FMControle"
                                                           value="#{FechamentoCaixaPlanoContaControle.openFiltrosMemoria}" />
                                            <h:inputHidden id="RCControle"
                                                           value="#{FechamentoCaixaPlanoContaControle.openIntervalo}" />


                                            <h:panelGrid id="panelMensagens" columns="1" width="100%"
                                                         styleClass="tabMensagens">
                                                <h:outputText styleClass="mensagem" id="mensagem2"
                                                              value="#{FechamentoCaixaPlanoContaControle.mensagem}" />
                                                <h:outputText styleClass="mensagemDetalhada"
                                                              id="mensagemDetalhada2"
                                                              value="#{FechamentoCaixaPlanoContaControle.mensagemDetalhada}" />
                                            </h:panelGrid>
                                        </h:form>
                                    </h:panelGroup>
                                </h:panelGroup>
                        </h:panelGroup>
                        <h:form id="form"  style="display: flex;align-items: stretch;flex-direction: row-reverse;">
                            <jsp:include page="includes/include_menu_relatorios.jsp" flush="true"/>
                        </h:form>
                    </h:panelGroup>
                </h:panelGroup>
            </h:panelGroup>

            <jsp:include page="../../include_rodape_flat.jsp" flush="true" />
        </h:panelGroup>
        <script type="text/javascript">
            reorganizarAreasFiltros();
            carregarTooltipster();
        </script>
        </body>

    </html>
    
    <a4j:outputPanel id="outTiposRel">
        <a4j:form id="formTiposRE">


            <rich:modalPanel styleClass="novaModal" id="modalTiposRelatorio" autosized="true"
                             shadowOpacity="true" width="800" height="250">
                <f:facet name="header">
                    <h:panelGroup>
                        <h:outputText  value="Selecione o Tipo de Consulta"></h:outputText>
                    </h:panelGroup>
                </f:facet>
                <f:facet name="controls">
                    <h:panelGroup>
                        <h:graphicImage value="/imagens/close.png" style="cursor:pointer"
                                        id="hidelinkTiposRel" />
                        <rich:componentControl for="modalTiposRelatorio"
                                               attachTo="hidelinkTiposRel" operation="hide" event="onclick" />
                    </h:panelGroup>
                </f:facet>


                    <h:panelGroup layout="block" style="margin-top:10px;margin-bottom: 10px;VERTICAL-ALIGN: middle;" id="tabAutorizacao"
                                  styleClass="tabelaSimplesCustom grupoRadioButton border-color-c2">
                        <a4j:repeat value="#{FechamentoCaixaPlanoContaControle.tiposRelatorios}" rowKeyVar="status" var="tipoRel">
                            <h:panelGroup layout="block"   styleClass="tipoRelatorioDF tipoRelatorioCodigo#{tipoRel.value.codigo}"  >
                                <div class="tooltipster" title="Clique para selecionar">
                                    <h:panelGroup layout="block" styleClass="maginTipoRel">
                                        <h:panelGroup layout="block" style="width: 100%" >
                                            <h:panelGroup layout="block"  style="display: inline-block;">
                                                <h:outputText style="font-family: Arial" styleClass="fa-icon-check-empty texto-cor-azul texto-font texto-size-16" />
                                                <h:outputText style="font-weight: bold" styleClass="texto-font texto-size-18" value=" #{tipoRel.value.descricao}: "/>
                                                <h:outputText styleClass="texto-font texto-size-16" value="#{tipoRel.value.descricaoAuxiliar}"/>
                                            </h:panelGroup>
                                        </h:panelGroup>
                                    </h:panelGroup>
                                </div>
                            </h:panelGroup>
                            <a4j:commandLink styleClass="tipoRelatorioCodigolink#{tipoRel.value.codigo}" style="display: none;" action="#{FechamentoCaixaPlanoContaControle.selecionarTipoRelatorio}"  oncomplete="Richfaces.hideModalPanel('modalTiposRelatorio')" reRender="painelDatas,panelTipoRel,apresentarDevolucoes" />

                            <rich:jQuery query="click(function(){jQuery('.tipoRelatorioCodigolink#{tipoRel.value.codigo}').click();});" selector=".tipoRelatorioCodigo#{tipoRel.value.codigo}"/>
                        </a4j:repeat>

                    </h:panelGroup>


            </rich:modalPanel>

        </a4j:form>

    </a4j:outputPanel>

    <%@include file="includes/include_modalLancamentosDF.jsp" %>
    <%@include file="../../includes/include_carregando_ripple.jsp" %>

</f:view>


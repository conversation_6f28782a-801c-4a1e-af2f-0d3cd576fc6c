<%@include file="includes/imports.jsp" %>

<script type="text/javascript">
            

    function carregarGrafico() {

        var chart2;

    ${BIFinanceiroControle.dadosGraficoReceita}
        try{
                        // SERIAL CHART
                        chart2 = new AmCharts.AmSerialChart();
                        chart2.dataProvider = chartData2;
                        chart2.categoryField = "tipo";
                        // the following two lines makes chart 3D
                        chart2.depth3D = 10;
                        chart2.angle = 20;

                        // AXES
                        // category
                        var categoryAxis = chart2.categoryAxis;
                        categoryAxis.labelRotation = 0;
                        categoryAxis.dashLength = 3;
                        categoryAxis.gridPosition = "start";

                        // value
                        var valueAxis = new AmCharts.ValueAxis();
                        valueAxis.dashLength = 3;
                        chart2.addValueAxis(valueAxis);

                        // GRAPH
                        var graph = new AmCharts.AmGraph();
                        graph.valueField = "valor";
                        graph.colorField = "color";
                        graph.balloonText = "<span style='font-size:14px'>[[category]]: <b>[[value]]</b></span>";
                        graph.type = "column";
                        graph.lineAlpha = 0;
                        graph.fillAlphas = 1;
                        chart2.addGraph(graph);
                        chart2.categoryField = "sigla";

                        // CURSOR
                        var chartCursor = new AmCharts.ChartCursor();
                        chartCursor.cursorAlpha = 0;
                        chartCursor.zoomable = false;
                        chartCursor.categoryBalloonEnabled = true;
                        chart2.addChartCursor(chartCursor);

                        // WRITE
                        chart2.write("chartdivformapagamento");

        }catch (err){

        }
                    }

                    function onStartReceita(){
                        document.getElementById('carregandoReceitas').style.display = 'block';
                        document.getElementById('chartdivformapagamento').style.display = 'none';
                        document.getElementById('form:dadosReceitaAtu').style.display = 'none';
                    }

                    function onStopReceita(){
                        document.getElementById('carregandoReceitas').style.display = 'none';
                        document.getElementById('chartdivformapagamento').style.display = 'block';
                        document.getElementById('form:dadosReceitaAtu').style.display = 'block';

                    }


</script>
</head>

<div id="chartdivformapagamento" style="width: 100%; height: 85%; padding: 0px !important;"></div>

<div id="carregandoReceitas" style="padding: 0px !important;display: none; margin: 20% auto 0 auto; width: 200px;" >
    <h:graphicImage value="../../imagens/carregando.gif" style="margin-right: 10px; vertical-align: middle;" />
    <h:outputText value="Atualizando dados..." styleClass="text" style="font-weight:bold;color:#0f4c6b;font-size:10pt;"/>

</div>

<script>
    carregarGrafico();
</script>
<%@page contentType="text/html" pageEncoding="ISO-8859-1" %>
<%@include file="includes/include_imports.jsp" %>

<script type="text/javascript">
    $.noConflict();
</script>

<rich:modalPanel id="modalPanelFiltrosLancamento" trimOverlayedElements="false"

                 autosized="false" shadowOpacity="true" width="870" height="650">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="Filtros Consulta de Lançamentos"/>
        </h:panelGroup>
    </f:facet>
    <f:facet name="controls">
        <h:panelGroup>
            <h:outputText
                    styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                    id="hidelink1"/>
            <rich:componentControl for="modalPanelFiltrosLancamento"
                                   attachTo="hidelink1" operation="hide" event="onclick" />
        </h:panelGroup>
    </f:facet>
    <a4j:form id="formFiltrosLancamento" ajaxSubmit="true">
        <div style="width: 100%; height: 510px; overflow-x:hidden; overflow-y:auto; border:none;background-color: #FFFFFF;" >

            <!-- ------------------------ INICIO PARAMETRO 1   ------------------------------- -->
            <div id="parametro1Expandido"
                 class="Passo"
                 onclick="esconderMostrar('tabelaParametros1', true, 'parametro1Expandido','parametro1Retraido', 'formFiltrosLancamento:parametro1');"
                 style="cursor: pointer; background-image: url(../../imagens/folder/Passo1normal.png); background-repeat: no-repeat;">
                <div class="Imagem">
                    <img id="ctl00_cph_imgPasso1"
                         src="../../imagens/folder/PassoExpandir.jpg"
                         style="border-top-width: 0px; border-right-width: 0px; border-bottom-width: 0px; border-left-width: 0px;"
                         title="Ocultar" />
                </div>
                <div class="Texto">Filtros Principais</div>
                <div class="Info">
                    <span id="ctl00_cph_lblPasso1">Ocultar</span>
                </div>
            </div>
            <div id="parametro1Retraido"
                 class="Passo"
                 onclick="esconderMostrar('tabelaParametros1', false, 'parametro1Retraido','parametro1Expandido', 'formFiltrosLancamento:parametro1');"
                 style="display: none; cursor: pointer; background-image: url(../../imagens/folder/Passo1normal.png); background-repeat: no-repeat;">
                <div class="Imagem">
                    <img id="ctl00_cph_imgPasso1"
                         src="../../imagens/folder/PassoOcultar.jpg"
                         style="border-top-width: 0px; border-right-width: 0px; border-bottom-width: 0px; border-left-width: 0px;"
                         title="Ocultar" />
                </div>
                <div class="Texto">Filtros Principais</div>
                <div class="Info">
                    <span id="ctl00_cph_lblPasso1">Expandir</span>
                </div>
            </div>
            <h:panelGroup id="parametrosUm">
                <table bgcolor="#F5F5F5" id="tabelaParametros1" width="100%"	style="display: block;">
                    <tr>
                        <td width="50%" valign="top">
                            <!------------------ Tipo Lançamento, favorecido, tipo de pesquisa --------------------------->
                            <h:panelGrid columns="2" columnClasses="classDireitaFinan,classEsquerdaFinan">
                                <h:outputText rendered="#{!MovContaControle.contasPagar && !MovContaControle.contasReceber}" styleClass="tituloDemonstrativo" value="#{msg_aplic.prt_Finan_Lancamentos_tipoLancamento}"/>
                                <h:panelGroup rendered="#{!MovContaControle.contasPagar && !MovContaControle.contasReceber}">

                                    <h:selectOneMenu style="width: 200px;" id="tipoLancamento" styleClass="form"
                                                     onblur="blurinput(this);" onfocus="focusinput(this);"
                                                     value="#{MovContaControle.filtrosLancamentos.tipoLancamento}">
                                        <f:selectItems value="#{MovContaControle.listaTipoLancamento}"/>
                                        <a4j:support event="onchange" action="#{MovContaControle.prepararTela}"
                                                     reRender="pnlRecebiveis, tipoPesquisa, parametrosUm,
                                                     dataInicioLancamento, dataTerminoLancamento,
                                                     dataInicioQuitacao, dataTerminoQuitacao,
                                                     dataInicioCompetencia, dataTerminoCompetencia,
                                                     apenasNaoQuitados,containerFuncMask"/>
                                    </h:selectOneMenu>
                                </h:panelGroup>
                                <h:outputText styleClass="tituloDemonstrativo"
                                              value="#{msg_aplic.prt_Finan_Lancamentos_favorecido}:"/>


                                <h:panelGroup style="text-align: right">
                                    <h:inputText id="pessoa" style="width: 200px;" styleClass="form" onfocus="this.value=''">
                                    </h:inputText>

                                    <rich:suggestionbox height="200" width="400"
                                                        for="pessoa"
                                                        fetchValue="#{result}"
                                                        suggestionAction="#{MovContaControle.executarAutocompleteConsultaFavorecido}"
                                                        minChars="1" rowClasses="20"
                                                        status="statusInComponent"
                                                        nothingLabel="Nenhuma pessoa encontrada!"
                                                        var="result"  id="suggestionPessoa">

                                        <a4j:support event="onselect" reRender="tableFavorecidos"
                                                     action="#{MovContaControle.selecionarFavorecidoSuggestionBox}"
                                                     oncomplete="document.getElementById('formFiltrosLancamento:pessoa').focus()"/>

                                        <h:column>
                                            <f:facet name="header">
                                                <h:outputText styleClass="text" value="Nome" />
                                            </f:facet>
                                            <h:outputText value="#{result.nome}"/>
                                        </h:column>

                                        <h:column>
                                            <f:facet name="header">
                                                <h:outputText styleClass="text" value="Tipo" />
                                            </f:facet>
                                            <h:outputText value="#{result.tipoPessoa}"  />
                                        </h:column>

                                        <h:column >
                                            <f:facet name="header">
                                                <h:outputText styleClass="text" value="CPF/CNPJ" />
                                            </f:facet>
                                            <h:outputText value="#{result.cfp}"  />
                                        </h:column>
                                    </rich:suggestionbox>

                                    <div style="overflow:auto; border:1px solid #ccc; width: 200px; height: 75px; text-align: right">
                                        <h:dataTable id="tableFavorecidos" width="100%" headerClass="subordinado"
                                                     rowClasses="linhaImpar, linhaPar"
                                                     columnClasses="colunaCentralizada, colunaEsquerda, colunaCentralizada"
                                                     value="#{MovContaControle.filtrosLancamentos.favorecidos}" var="favorecido">
                                            <h:column>
                                                <f:facet name="header">
                                                    <h:outputText value="Nome"/>
                                                </f:facet>
                                                <h:outputText value="#{favorecido.nome}"/>
                                            </h:column>
                                            <h:column>
                                                <f:facet name="header">
                                                    <a4j:commandButton id="removerTodosFavorecidos" reRender="tableFavorecidos"
                                                                       action="#{MovContaControle.removerTodosFavorecidos}"
                                                                       image="/images/limpar.gif" title="Remover todos os favorecidos."/>
                                                </f:facet>
                                                <h:panelGroup>
                                                    <a4j:commandButton id="removerFavorecido" reRender="tableFavorecidos"
                                                                       action="#{MovContaControle.removerFavorecido}"
                                                                       value="" image="../../imagens/botaoRemover.png"
                                                                       accesskey="7" styleClass="botoes">
                                                        <f:attribute name="favorecido" value="#{favorecido}" />
                                                    </a4j:commandButton>
                                                </h:panelGroup>
                                            </h:column>

                                        </h:dataTable>
                                    </div>
                                </h:panelGroup>



                                <h:outputText styleClass="tituloDemonstrativo" value="#{msg_aplic.prt_Finan_Lancamentos_tipoPesquisa}"/>
                                <h:panelGroup>
                                    <h:selectOneMenu style="width: 200px;" onblur="blurinput(this);"  onfocus="focusinput(this);"
                                                     id="tipoPesquisa" value="#{MovContaControle.filtrosLancamentos.tipoPesquisa}" styleClass="form">
                                        <f:selectItem itemValue="" itemLabel=" "/>
                                        <f:selectItems value="#{MovContaControle.listaSelectItemTipoPesquisa}" />
                                        <a4j:support  event="onchange" reRender="parametrosUm,containerFuncMask" action="#{MovContaControle.filtrosLancamentos.limparDatasTipoVazio}"/>
                                    </h:selectOneMenu>

                                </h:panelGroup>
                                <h:outputText rendered="#{MovContaControle.filtrosLancamentos.tipoPesquisa == 2}" styleClass="tituloDemonstrativo" value="#{msg_aplic.prt_Finan_Lancamentos_periodo}"/>
                                <h:panelGroup>
                                    <h:selectOneMenu rendered="#{MovContaControle.filtrosLancamentos.tipoPesquisa == 2}" style="width: 200px;"
                                                     onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" id="periodoPadrao"
                                                     value="#{MovContaControle.filtrosLancamentos.tipoPeriodo}">
                                        <f:selectItems value="#{MovContaControle.listaSelectItemPesquisaPadrao}" />
                                    </h:selectOneMenu>
                                </h:panelGroup>
                            </h:panelGrid>
                        </td>
                        <td>&nbsp;</td>
                        <!------------------ descrição e valores --------------------------->
                        <td width="50%" valign="top">
                            <h:panelGrid columns="2" columnClasses="classDireitaFinan,classDireitaFinan">
                                <h:outputText styleClass="tituloDemonstrativo" value="#{msg_aplic.prt_Finan_Lancamentos_descricao}:"/>
                                <h:panelGroup>
                                    <h:inputText  styleClass="form"  size="45" maxlength="45" value="#{MovContaControle.filtrosLancamentos.descricao}" id="descricaoFiltro"/>
                                </h:panelGroup>
                                <h:outputText styleClass="tituloDemonstrativo" value="#{msg_aplic.prt_Finan_Lancamento_numDocumento}"/>
                                <h:inputText  styleClass="form"  size="45" maxlength="45" value="#{MovContaControle.filtrosLancamentos.numeroDocumento}" id="numeroDocumento"/>
                                <h:outputText styleClass="tituloDemonstrativo" value="#{msg_aplic.prt_Finan_Lancamentos_valorDe}:"/>
                                <h:panelGroup>
                                    <table cellpadding="0" cellspacing="0" width="100%">
                                        <tr>
                                            <td width="24%"><h:inputText size="8" maxlength="12"
                                                         value="#{MovContaControle.filtrosLancamentos.valorMinimo}"
                                                         onkeypress="return(currencyFormat(this,'.',',',event));">
                                                    <f:converter converterId="FormatadorNumerico" />
                                                </h:inputText></td>

                                            <td width="8%" class="centralizado"><h:outputText styleClass="tituloDemonstrativo" value="  #{msg_aplic.prt_Finan_Lancamentos_a}  "/></td>

                                            <td width="24%" class="direita" style="text-align: right;"><h:inputText size="8" maxlength="12"
                                                         onkeypress="return(currencyFormat(this,'.',',',event));"
                                                         value="#{MovContaControle.filtrosLancamentos.valorMaximo}">
                                                    <f:converter converterId="FormatadorNumerico" />
                                                </h:inputText></td>
                                            <td width="18%" class="centralizado">
                                            	<h:outputText styleClass="tituloDemonstrativo" value="Cód.:"/>
                                            </td>  
                                            <td width="24%" class="direita">
	                                            <h:inputText size="6" maxlength="12" id="codigomovconta"
	                                                         onkeypress="return mascara(this.form, this.id, '99999999999', event);"
	                                                         value="#{MovContaControle.filtrosLancamentos.codigo}">
	                                                </h:inputText>
                                            </td>   
                                        </tr>
                                    </table>
                                </h:panelGroup>
                                <h:panelGroup>
                                    <h:selectBooleanCheckbox value="#{MovContaControle.filtrosLancamentos.apenasQuitados}"/>
                                    <h:outputText styleClass="tituloDemonstrativo" value="#{msg_aplic.prt_Finan_Lancamentos_somenteQuitados}"/>
                                </h:panelGroup>
                                <h:panelGroup id="apenasNaoQuitados">
                                    <h:selectBooleanCheckbox value="#{MovContaControle.filtrosLancamentos.apenasNaoQuitados}"/>
                                    <h:outputText styleClass="tituloDemonstrativo" value="#{msg_aplic.prt_Finan_Lancamentos_somenteNaoQuitados}"/>
                                </h:panelGroup>
                                <h:panelGroup id="pnlRecebiveis">
                                </h:panelGroup>
                                <h:panelGroup>
                                    <h:selectBooleanCheckbox id="recebiveisComLote" value="#{GestaoRecebiveisControle.pesquisarRecebiveisComLote}">
                                        <a4j:support action="#{GestaoRecebiveisControle.validarRecebiveisMarcadoComLote}" event="onchange" reRender="recebiveisComLote,recebiveisSemLote,recebiveis,containerFuncMask"/>
                                    </h:selectBooleanCheckbox>
                                    <h:outputText styleClass="tituloDemonstrativo" value="#{msg_aplic.prt_Finan_Lancamentos_incluirRecebiveisComLote}"/>
                                    <h:selectBooleanCheckbox id="recebiveisSemLote" value="#{GestaoRecebiveisControle.pesquisarRecebiveisSemLote}">
                                        <a4j:support action="#{GestaoRecebiveisControle.validarRecebiveisMarcadoSemLote}" event="onchange" reRender="recebiveisComLote,recebiveisSemLote,recebiveis,containerFuncMask"/>
                                    </h:selectBooleanCheckbox>
                                    <h:outputText styleClass="tituloDemonstrativo" value="#{msg_aplic.prt_Finan_Lancamentos_incluirRecebiveisSemLote}"/>
                                </h:panelGroup>
                                <h:panelGroup>
                                    <h:selectBooleanCheckbox id="pagamentosCOnjunto" value="#{MovContaControle.filtrosLancamentos.pagamentosConjunto}">
                                    </h:selectBooleanCheckbox>
                                    <h:outputText styleClass="tituloDemonstrativo" value="#{msg_aplic.prt_finan_pagos_conjunto_lbl}"/>
                                </h:panelGroup>
                                <h:panelGroup>
                                    <h:selectBooleanCheckbox id="agruparPorPlanoConta"
                                                             title="Marque para visualizar as colunas Plano de Contas e Centro de Custos, com suas respectivas descrições e valores definidos no rateio"
                                                             value="#{MovContaControle.filtrosLancamentos.agruparPorPlanoDeContasCentroCusto}">
                                    </h:selectBooleanCheckbox>
                                    <h:outputText styleClass="tituloDemonstrativo" value="Visualizar colunas Plano de Contas e Centro de Custos"/>
                                </h:panelGroup>
                                <h:panelGroup>
                                </h:panelGroup>
                                <h:panelGroup>
                                    <h:selectBooleanCheckbox id="somentecontasapp"
                                                             title="Somente contas lançadas pelo Pacto App"
                                                             value="#{MovContaControle.filtrosLancamentos.somenteContasMobile}">
                                    </h:selectBooleanCheckbox>
                                    <h:outputText styleClass="tituloDemonstrativo" value="Somente contas lançadas no Pacto App"/>
                                </h:panelGroup>

                                <h:panelGroup>
                                </h:panelGroup>
                                <h:panelGroup>
                                    <h:selectBooleanCheckbox id="agendamentosandamento"
                                                             title="Será mostrado todos os lançamentos que estão com agendamento configurado para gerar parcelas automaticamente, em aberto (Agendamentos ativos). O resultado trará apenas o último lançamento de cada conta. Será ignorado os agendamentos que são 'Parcelas Fixas'"
                                                             value="#{MovContaControle.filtrosLancamentos.agendamentosAndamento}">
                                    </h:selectBooleanCheckbox>
                                    <h:outputText styleClass="tituloDemonstrativo" value="Agendamentos ativos em andamento"/>
                                </h:panelGroup>

                                <h:panelGroup>
                                </h:panelGroup>

                                <h:panelGroup>
                                    <h:selectBooleanCheckbox id="movimentacoesExcluidas"
                                                             title="Marque caso deseje visualizar somente lançamentos excluidos ao realizar filtros utilizando a Dt. Interna(IAE)."
                                                             value="#{MovContaControle.filtrosLancamentos.movimentacoesExcluidas}">
                                    </h:selectBooleanCheckbox>
                                    <h:outputText styleClass="tituloDemonstrativo" value="Somente movimentações excluidas"/>
                                </h:panelGroup>

                            </h:panelGrid>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <!--Opção Pesquisa Período-->
                            <h:panelGrid columnClasses="classDireitaFinan,classEsquerdaFinan"
                                         rendered="#{MovContaControle.filtrosLancamentos.tipoPesquisa == 1}"
                                         columns="2"
                                         width="100%">
                                <!--data lançamento-->
                                <h:outputText styleClass="tituloDemonstrativo" value="#{msg_aplic.prt_Finan_Lancamentos_dataLancamento}:" />
                                <h:panelGroup >
                                    <rich:calendar id="dataInicioLancamento"
                                                   value="#{MovContaControle.filtrosLancamentos.inicioLancamento}"
                                                   inputSize="6"
                                                   inputClass="form"
                                                   oninputblur="blurinput(this);"
                                                   oninputfocus="focusinput(this);"
                                                   oninputchange="return validar_Data(this.id);"
                                                   onchanged="return false;"
                                                   datePattern="dd/MM/yyyy"
                                                   enableManualInput="true"
                                                   direction="bottom"
                                                   zindex="2"
                                                   showWeeksBar="false"/>
                                    <rich:spacer width="3"/>
                                    <h:outputText styleClass="tituloDemonstrativo" value="#{msg_aplic.prt_Finan_Lancamentos_a}"/>
                                    <rich:spacer width="3"/>
                                    <rich:calendar id="dataTerminoLancamento"
                                                   value="#{MovContaControle.filtrosLancamentos.finalLancamento}"
                                                   inputSize="6"
                                                   inputClass="form"
                                                   oninputblur="blurinput(this);"
                                                   oninputfocus="focusinput(this);"
                                                   oninputchange="return validar_Data(this.id);"
                                                   datePattern="dd/MM/yyyy"
                                                   enableManualInput="true"
                                                   direction="bottom"
                                                   zindex="2"
                                                   showWeeksBar="false" />
                                    <rich:spacer width="3"/>
                                    <a4j:commandButton id="limparPeriodoLancamento"
                                                       action="#{MovContaControle.limparPeriodoLancamento}"
                                                       image="/images/limpar.gif" title="Limpar período de lançamento."
                                                       reRender="dataInicioLancamento, dataTerminoLancamento,containerFuncMask"/>
                                </h:panelGroup>
                                <!--data vencimento-->
                                <h:outputText styleClass="tituloDemonstrativo" value="#{msg_aplic.prt_Finan_Lancamentos_dataVencimentoFiltros}:"/>
                                <h:panelGroup>
                                    <rich:calendar id="dataInicioVencimento"
                                                   value="#{MovContaControle.filtrosLancamentos.inicioVencimento}"
                                                   inputSize="6"
                                                   inputClass="form"
                                                   oninputblur="blurinput(this);"
                                                   oninputfocus="focusinput(this);"
                                                   oninputchange="return validar_Data(this.id);"
                                                   datePattern="dd/MM/yyyy"
                                                   enableManualInput="true"
                                                   direction="bottom"
                                                   zindex="2"
                                                   showWeeksBar="false" />
                                    <rich:spacer width="3"/>
                                    <h:outputText styleClass="tituloDemonstrativo" value="#{msg_aplic.prt_Finan_Lancamentos_a}"/>
                                    <rich:spacer width="3"/>
                                    <rich:calendar id="dataTerminoVencimento"
                                                   value="#{MovContaControle.filtrosLancamentos.finalVencimento}"
                                                   inputSize="6"
                                                   inputClass="form"
                                                   oninputblur="blurinput(this);"
                                                   oninputfocus="focusinput(this);"
                                                   oninputchange="return validar_Data(this.id);"
                                                   datePattern="dd/MM/yyyy"
                                                   enableManualInput="true"
                                                   direction="bottom"
                                                   zindex="2"
                                                   showWeeksBar="false" />
                                    <rich:spacer width="3"/>
                                    <a4j:commandButton id="limparPeriodoVencimento"
                                                       action="#{MovContaControle.limparPeriodoVencimento}"
                                                       image="/images/limpar.gif" title="Limpar período de vencimento."
                                                       reRender="dataInicioVencimento, dataTerminoVencimento,containerFuncMask"/>
                                </h:panelGroup>

                                <!--data última alteração-->
                                <h:outputText styleClass="tituloDemonstrativo" title="Últimas inclusões, alterações e exclusões." value="Dt. Interna(IAE):"/>
                                <h:panelGroup>
                                    <rich:calendar id="dataUltimaAlteracao"
                                                   value="#{MovContaControle.filtrosLancamentos.dataInicioUltimaAlteracao}"
                                                   inputSize="6"
                                                   inputClass="form"
                                                   oninputblur="blurinput(this);"
                                                   oninputfocus="focusinput(this);"
                                                   oninputchange="return validar_Data(this.id);"
                                                   datePattern="dd/MM/yyyy"
                                                   enableManualInput="true"
                                                   direction="bottom"
                                                   zindex="2"
                                                   showWeeksBar="false" />
                                    <rich:spacer width="3"/>
                                    <h:outputText styleClass="tituloDemonstrativo" value="#{msg_aplic.prt_Finan_Lancamentos_a}"/>
                                    <rich:spacer width="3"/>
                                    <rich:calendar id="dataFimUltimaAlteracao"
                                                   value="#{MovContaControle.filtrosLancamentos.dataFimUltimaAlteracao}"
                                                   inputSize="6"
                                                   inputClass="form"
                                                   oninputblur="blurinput(this);"
                                                   oninputfocus="focusinput(this);"
                                                   oninputchange="return validar_Data(this.id);"
                                                   datePattern="dd/MM/yyyy"
                                                   enableManualInput="true"
                                                   direction="bottom"
                                                   zindex="2"
                                                   showWeeksBar="false" />
                                    <rich:spacer width="3"/>
                                    <a4j:commandButton id="limparPeriodoUltimaAlteracao"
                                                       action="#{MovContaControle.limparPeriodoDataUltimaAlteracao}"
                                                       image="/images/limpar.gif" title="Limpar período de Dt. Interna(IAE)."
                                                       reRender="dataUltimaAlteracao, dataFimUltimaAlteracao"/>
                                </h:panelGroup>


                            </h:panelGrid>

                        </td>
                        <td>&nbsp;</td>
                        <td>
                            <!--Opção Pesquisa Período-->
                            <h:panelGrid columnClasses="classDireitaFinan,classEsquerdaFinan"
                                         rendered="#{MovContaControle.filtrosLancamentos.tipoPesquisa == 1}"
                                         columns="2"
                                         width="100%">
                                <!--data quitação-->

                                <h:outputText styleClass="tituloDemonstrativo" value="#{msg_aplic.prt_Finan_Lancamentos_dataQuitacao}:"/>
                                <h:panelGroup>
                                    <rich:calendar id="dataInicioQuitacao"
                                                   value="#{MovContaControle.filtrosLancamentos.inicioQuitacao}"
                                                   inputSize="6"
                                                   inputClass="form"
                                                   oninputblur="blurinput(this);"
                                                   oninputfocus="focusinput(this);"
                                                   oninputchange="return validar_Data(this.id);"
                                                   datePattern="dd/MM/yyyy"
                                                   enableManualInput="true"
                                                   direction="bottom"
                                                   zindex="2"
                                                   showWeeksBar="false" />
                                    <rich:spacer width="3"/>
                                    <h:outputText styleClass="tituloDemonstrativo" value="#{msg_aplic.prt_Finan_Lancamentos_a}"/>
                                    <rich:spacer width="3"/>
                                    <rich:calendar id="dataTerminoQuitacao"
                                                   value="#{MovContaControle.filtrosLancamentos.finalQuitacao}"
                                                   inputSize="6"
                                                   inputClass="form"
                                                   oninputblur="blurinput(this);"
                                                   oninputfocus="focusinput(this);"
                                                   oninputchange="return validar_Data(this.id);"
                                                   datePattern="dd/MM/yyyy"
                                                   enableManualInput="true"
                                                   direction="bottom"
                                                   zindex="2"
                                                   showWeeksBar="false" />
                                    <rich:spacer width="3"/>
                                    <a4j:commandButton id="limparPeriodoQuitacao"
                                                       action="#{MovContaControle.limparPeriodoQuitacao}"
                                                       image="/images/limpar.gif" title="Limpar período de quitação."
                                                       reRender="dataInicioQuitacao, dataTerminoQuitacao,containerFuncMask"/>
                                </h:panelGroup>


                                <!--data competência-->

                                <h:outputText styleClass="tituloDemonstrativo" value="#{msg_aplic.prt_Finan_Lancamentos_dataCompetencia}:"/>
                                <h:panelGroup>
                                    <rich:calendar id="dataInicioCompetencia"
                                                   value="#{MovContaControle.filtrosLancamentos.inicioCompetencia}"
                                                   inputSize="6"
                                                   inputClass="form"
                                                   oninputblur="blurinput(this);"
                                                   oninputfocus="focusinput(this);"
                                                   oninputchange="return validar_Data(this.id);"
                                                   datePattern="dd/MM/yyyy"
                                                   enableManualInput="true"
                                                   direction="bottom"
                                                   zindex="2"
                                                   showWeeksBar="false" />
                                    <rich:spacer width="3"/>
                                    <h:outputText styleClass="tituloDemonstrativo" value="#{msg_aplic.prt_Finan_Lancamentos_a}"/>
                                    <rich:spacer width="3"/>
                                    <rich:calendar id="dataTerminoCompetencia"
                                                   value="#{MovContaControle.filtrosLancamentos.finalCompetencia}"
                                                   inputSize="6"
                                                   inputClass="form"
                                                   oninputblur="blurinput(this);"
                                                   oninputfocus="focusinput(this);"
                                                   oninputchange="return validar_Data(this.id);"
                                                   datePattern="dd/MM/yyyy"
                                                   enableManualInput="true"
                                                   direction="bottom"
                                                   zindex="2"
                                                   showWeeksBar="false" />
                                    <rich:spacer width="3"/>
                                    <a4j:commandButton id="limparPeriodoCompetencia"
                                                       action="#{MovContaControle.limparPeriodoCompetencia}"
                                                       image="/images/limpar.gif" title="Limpar período de competência."
                                                       reRender="dataInicioCompetencia, dataTerminoCompetencia,containerFuncMask"/>
                                </h:panelGroup>

                            </h:panelGrid>
                        </td>
                    </tr>
                </table>
            </h:panelGroup>
            <br/>
            <!-- ------------------------ FIM PARAMETRO 1   ------------------------------- -->

            <!-- ------------------------ INICIO PARAMETRO 2  ------------------------------- -->
            <div id="parametro2Expandido"
                 class="Passo"
                 onclick="esconderMostrar('tabelaParametros2', true, 'parametro2Expandido','parametro2Retraido', 'formFiltrosLancamento:parametro2');"
                 style="display: none; cursor: pointer; background-image: url(../../imagens/folder/Passo2normal.png); background-repeat: no-repeat;">
                <div class="Imagem">
                    <img id="ctl00_cph_imgPasso1"
                         src="../../imagens/folder/PassoExpandir.jpg"
                         style="border-top-width: 0px; border-right-width: 0px; border-bottom-width: 0px; border-left-width: 0px;"
                         title="Ocultar" />
                </div>
                <div class="Texto">
                    <h:outputText rendered="#{ConfiguracaoFinanceiroControle.confFinanceiro.usarMovimentacaoContas}" value="Conta e Forma de Pagamento"/>
                    <h:outputText rendered="#{!ConfiguracaoFinanceiroControle.confFinanceiro.usarMovimentacaoContas}" value="Forma de Pagamento"/>
                </div>
                <div class="Info">
                    <span id="ctl00_cph_lblPasso1">Ocultar</span>
                </div>
            </div>
            <div id="parametro2Retraido"
                 class="Passo"
                 onclick="esconderMostrar('tabelaParametros2', false, 'parametro2Retraido','parametro2Expandido', 'formFiltrosLancamento:parametro2');"
                 style="cursor: pointer; background-image: url(../../imagens/folder/Passo2normal.png); background-repeat: no-repeat;">
                <div class="Imagem">
                    <img id="ctl00_cph_imgPasso1"
                         src="../../imagens/folder/PassoOcultar.jpg"
                         style="border-top-width: 0px; border-right-width: 0px; border-bottom-width: 0px; border-left-width: 0px;"
                         title="Ocultar" />
                </div>
                <div class="Texto">
                	<h:outputText rendered="#{ConfiguracaoFinanceiroControle.confFinanceiro.usarMovimentacaoContas}" value="Conta e Forma de Pagamento"></h:outputText>
                	<h:outputText rendered="#{!ConfiguracaoFinanceiroControle.confFinanceiro.usarMovimentacaoContas}" value="Forma de Pagamento"></h:outputText> 
                </div>
                <div class="Info">
                    <span id="ctl00_cph_lblPasso1">Expandir</span>
                </div>
            </div>
            <table bgcolor="#F5F5F5" id="tabelaParametros2" width="100%" style="display: none;">
                <tr>
                	<c:if test="${ConfiguracaoFinanceiroControle.confFinanceiro.usarMovimentacaoContas}">
                    <td width="50%">
                        <h:outputText styleClass="tituloDemonstrativo" value="#{msg_aplic.prt_Finan_Lancamentos_conta}"/>

                        <div id="caixaContas" style="width: 350px; height: 150px; overflow-x:hidden; overflow-y:auto; border:solid 1px #A9A9A9;background-color: #FFFFFF;"  >
                            <table bgcolor="#FFFFFF" width="100%">
                                <tr>
                                    <td>&nbsp;&nbsp;</td>
                                    <td>
                                        <rich:dataTable width="100%" rowClasses="linhaParFinan,linhaImparFinan"
                                                        style="border:none;" columnClasses="colunaFinan"
                                                        var="conta" value="#{MovContaControle.filtrosLancamentos.listaConta}">
                                            <h:column>
                                                <h:selectBooleanCheckbox value="#{conta.contaEscolhida}"/>
                                                <h:outputText value="#{conta.descricao}" styleClass="textblack"/>
                                            </h:column>
                                        </rich:dataTable>

                                    </td>
                                </tr>
                            </table>
                        </div>
                    </td>
                    <td>&nbsp;</td>
                    </c:if>
                    <td width="50%">
                        <h:outputText styleClass="tituloDemonstrativo" value="#{msg_aplic.prt_Finan_Lancamentos_formaPagamento}"/>

                        <div id="caixaFormaPagamento" style="width: 350px; height: 150px; overflow-x:hidden; overflow-y:auto; border:solid 1px #A9A9A9;background-color: #FFFFFF;"  >
                            <table bgcolor="#FFFFFF" width="100%">
                                <tr>
                                    <td>&nbsp;&nbsp;</td>
                                    <td>
                                        <rich:dataTable width="100%" rowClasses="linhaParFinan,linhaImparFinan"
                                                        style="border:none;" columnClasses="colunaFinan"
                                                        var="formaPgto" value="#{MovContaControle.filtrosLancamentos.listaFormaPgto}">
                                            <h:column>
                                                <h:selectBooleanCheckbox value="#{formaPgto.formaPagamentoEscolhida}"/>
                                                <h:outputText value="#{formaPgto.descricao}" styleClass="textblack"/>
                                            </h:column>
                                        </rich:dataTable>

                                    </td>
                                </tr>
                            </table>
                        </div>
                    </td>
                </tr>


            </table>
            <br/>
            <!-- ------------------------ FIM PARAMETRO 2  ------------------------------- -->

            <!-- ------------------------ INICIO PARAMETRO 3  ------------------------------- -->
            <div id="parametro3Expandido"
                 class="Passo"
                 onclick="esconderMostrar('tabelaParametros3', true, 'parametro3Expandido','parametro3Retraido', 'formFiltrosLancamento:parametro3');"
                 style="display: none; cursor: pointer; background-image: url(../../imagens/folder/Passo3normal.png); background-repeat: no-repeat;">
                <div class="Imagem">
                    <img id="ctl00_cph_imgPasso1"
                         src="../../imagens/folder/PassoExpandir.jpg"
                         style="border-top-width: 0px; border-right-width: 0px; border-bottom-width: 0px; border-left-width: 0px;"
                         title="Ocultar" />
                </div>
                <div class="Texto">Tipo Doc. e Plano Contas e Centro Custos</div>
                <div class="Info">
                    <span id="ctl00_cph_lblPasso1">Ocultar</span>
                </div>
            </div>
            <div id="parametro3Retraido"
                 class="Passo"
                 onclick="esconderMostrar('tabelaParametros3', false, 'parametro3Retraido','parametro3Expandido', 'formFiltrosLancamento:parametro3');"
                 style="cursor: pointer; background-image: url(../../imagens/folder/Passo3normal.png); background-repeat: no-repeat;">
                <div class="Imagem">
                    <img id="ctl00_cph_imgPasso1"
                         src="../../imagens/folder/PassoOcultar.jpg"
                         style="border-top-width: 0px; border-right-width: 0px; border-bottom-width: 0px; border-left-width: 0px;"
                         title="Ocultar" />
                </div>
                <div class="Texto">Tipo Doc. e Plano Contas e Centro Custos</div>
                <div class="Info">
                    <span id="ctl00_cph_lblPasso1">Expandir</span>
                </div>
            </div>

            <table bgcolor="#F5F5F5" id="tabelaParametros3" width="100%" style="display: none;">
                <tr>
                    <td width="50%">
                        <table><tr><td>
                                    <h:outputText styleClass="tituloDemonstrativo" value="#{msg_aplic.prt_Finan_Lancamento_tipoDocumento}"/>
                                </td></tr></table>

                        <div id="caixaTipoDocumento" style="width: 350px; height: 150px; overflow-x:hidden; overflow-y:auto; border:solid 1px #A9A9A9;background-color: #FFFFFF;"  >
                            <table bgcolor="#FFFFFF" width="100%">
                                <tr>
                                    <td>&nbsp;&nbsp;</td>
                                    <td>
                                        <rich:dataTable style="border:none;" columnClasses="colunaFinan" width="100%" rowClasses="linhaParFinan,linhaImparFinan"
                                                        var="tipo" value="#{MovContaControle.filtrosLancamentos.listaTipoDocumento}">
                                            <h:column>
                                                <h:selectBooleanCheckbox value="#{tipo.tipoDocumentoEscolhido}"/>
                                                <h:outputText value="#{tipo.descricao}" styleClass="textblack"/>
                                            </h:column>
                                        </rich:dataTable>

                                    </td>
                                </tr>
                            </table>
                        </div>
                    </td>
                    <td>&nbsp;</td>
                    <td width="50%">
                        <h:panelGroup id="linksControle">
                            <a4j:commandLink action="#{MovContaControle.inverterSelecao}"
                                             value="Mostrar Plano de Contas"
                                             rendered="#{!MovContaControle.mostrarPlanoContas}"
                                             reRender="linksControle"/>

                            <a4j:commandLink action="#{MovContaControle.inverterSelecao}"
                                             value="Mostrar Centro de Custos"
                                             rendered="#{MovContaControle.mostrarPlanoContas}"
                                             reRender="linksControle"/>

                            <h:panelGroup id="plnContas" rendered="true" layout="block"
                                          style="#{MovContaControle.mostrarPlanoContas ? '' : 'display:none;'}">
                                <table width="100%">
                                    <tr>
                                        <td width="30%">
                                            <h:outputText styleClass="tituloDemonstrativo"
                                                          value="#{msg_aplic.prt_Finan_Lancamento_planoContas}"/>
                                            <input type="text" style="margin: 5px" id="filtroInput"
                                                   onkeyup="filtrarLista()" placeholder="Filtrar plano de contas"/>
                                        </td>
                                        <td width="70%" style="text-align: right;">
                                            <a href="#" class="expandirPlanoLan"> Expandir Tudo </a>
                                            &nbsp;
                                            <a href="#" class="expandirUmPlanoLan"> Expandir </a>
                                            &nbsp;
                                            <a href="#" class="retrairUmPlanoLan"> Retrair </a>
                                            &nbsp;
                                            <a href="#" class="retrairPlanoLan"> Retrair Tudo </a>
                                        </td>
                                    </tr>
                                </table>

                                <h:panelGroup id="painelPlanoContas">
                                    <h:panelGroup layout="block"
                                                  style="width: 350px; height: 150px; overflow-x:hidden; overflow-y:auto; border:solid 1px #A9A9A9; background-color: #FFFFFF;">
                                        <h:dataTable value="#{MovContaControle.filtroPlanoContas.listaTree}" var="plano"
                                                     styleClass="filtroPlanosLan"
                                                     rowClasses="linhaParFinan,linhaImparFinan"
                                                     style="width: 100%; background-color: #FFFFFF;"
                                                     id="dnd-filtroPlanosLan">
                                            <h:column>
                                                <h:panelGroup layout="block" styleClass="textblack">
                                                    <h:selectBooleanCheckbox value="#{plano.selecionado}"
                                                                             onclick="selecionarFilhos('#{plano.codigoNode}', this.checked); adicionarFiltro('#{plano.codigoEntidade}', this, 'formFiltrosLancamento:filtroPlanoContas');"/>
                                                    <h:outputText value="#{plano.nome}"/>
                                                </h:panelGroup>
                                            </h:column>
                                        </h:dataTable>
                                    </h:panelGroup>
                                </h:panelGroup>

                                <script type="text/javascript">
                                    atualizarTreeViewFiltrosLancamento();
                                </script>
                            </h:panelGroup>
                            <h:panelGroup id="cntCustos" rendered="true"
                                          style="#{MovContaControle.mostrarPlanoContas ? 'display:none;' : ''}">
                                <table width="100%">
                                    <tr>
                                        <td width="30%">
                                            <h:outputText styleClass="tituloDemonstrativo"
                                                          value="#{msg_aplic.prt_Finan_Lancamento_centroCusto}"/>
                                            <input type="text" style="margin: 5px" id="filtroCentroCustoInput"
                                                   onkeyup="filtrarListaCentroCusto()"
                                                   placeholder="Filtrar centro de custo"/>
                                        </td>
                                        <td width="70%" style="text-align: right;">
                                            <a href="#" class="expandirCustosLan"> Expandir Tudo </a>
                                            &nbsp;
                                            <a href="#" class="expandirUmCustosLan"> Expandir </a>
                                            &nbsp;
                                            <a href="#" class="retrairUmCustosLan"> Retrair </a>
                                            &nbsp;
                                            <a href="#" class="retrairCustosLan"> Retrair Tudo </a>
                                        </td>
                                    </tr>
                                </table>

                                <h:panelGroup id="painelCentroCusto">
                                    <h:panelGroup layout="block"
                                                  style="width: 350px; height: 150px; overflow-x:hidden; overflow-y:auto; border:solid 1px #A9A9A9; background-color: #FFFFFF;">
                                        <h:dataTable value="#{MovContaControle.filtroCentroCustos.listaTree}"
                                                     var="custo"
                                                     styleClass="filtroCustosLan"
                                                     rowClasses="linhaParFinan,linhaImparFinan"
                                                     style="width: 100%; background-color: #FFFFFF;"
                                                     id="tabelaCentroCusto">
                                            <h:column>
                                                <h:panelGroup layout="block" styleClass="textblack">
                                                    <h:selectBooleanCheckbox value="#{custo.selecionado}"
                                                                             onclick="selecionarFilhos('#{custo.codigoNode}', this.checked); adicionarFiltro('#{custo.codigoEntidade}', this, 'formFiltrosLancamento:filtroCentroCustos');"/>
                                                    <h:outputText value="#{custo.nome}"/>
                                                </h:panelGroup>
                                            </h:column>
                                        </h:dataTable>
                                    </h:panelGroup>
                                </h:panelGroup>

                                <script type="text/javascript">
                                    atualizarTreeView('CustosLan');

                                    function filtrarListaCentroCusto() {
                                        let filtro = document.getElementById('filtroCentroCustoInput').value.toLowerCase();
                                        let linhas = document.querySelectorAll(".filtroCustosLan tr");

                                        linhas.forEach(function (linha) {
                                            let texto = linha.textContent || linha.innerText;
                                            linha.style.display = texto.toLowerCase().includes(filtro) ? '' : 'none';
                                        });
                                    }
                                </script>
                            </h:panelGroup>
                        </h:panelGroup>
                    </td>
                </tr>
            </table>
            <br/>
            <!-- ------------------------ FIM PARAMETRO 3  ------------------------------- -->

            <!-- ------------------------ INICIO PARAMETRO 4  ------------------------------- -->
            <div id="parametro4Expandido" class="Passo"
                 onclick="esconderMostrar('tabelaParametros4', true, 'parametro4Expandido','parametro4Retraido', 'formFiltrosLancamento:parametro4');"
                 style="display: none; cursor: pointer; background-image: url(../../imagens/folder/Passo4normal.png); background-repeat: no-repeat;">
                <div class="Imagem">
                    <img id="ctl00_cph_imgPasso1"
                         src="../../imagens/folder/PassoExpandir.jpg"
                         style="border-top-width: 0px; border-right-width: 0px; border-bottom-width: 0px; border-left-width: 0px;"
                         title="Ocultar" />
                </div>
                <div class="Texto">Empresa</div>
                <div class="Info">
                    <span id="ctl00_cph_lblPasso1">Ocultar</span>
                </div>
            </div>
            <div id="parametro4Retraido"
                 class="Passo"
                 onclick="esconderMostrar('tabelaParametros4', false, 'parametro4Retraido','parametro4Expandido', 'formFiltrosLancamento:parametro4');"
                 style="cursor: pointer; background-image: url(../../imagens/folder/Passo4normal.png); background-repeat: no-repeat;">
                <div class="Imagem">
                    <img id="ctl00_cph_imgPasso1"
                         src="../../imagens/folder/PassoOcultar.jpg"
                         style="border-top-width: 0px; border-right-width: 0px; border-bottom-width: 0px; border-left-width: 0px;"
                         title="Ocultar" />
                </div>
                <div class="Texto">Empresa</div>
                <div class="Info">
                    <span id="ctl00_cph_lblPasso1">Expandir</span>
                </div>
            </div>
            <table bgcolor="#F5F5F5" id="tabelaParametros4" width="100%" style="display: none;">
                <tr>
                    <td width="50%">
                        <table>
                            <tr>
                                <td>
                                    <h:panelGroup rendered="#{!LoginControle.permissaoAcessoMenuVO.lancarVisualizarLancamentosEmpresas}">
                                        <h:outputText styleClass="tituloDemonstrativo" value="#{msg_aplic.prt_Empresa_tituloForm}: #{LoginControle.nomeEmpresa}"/>
                                    </h:panelGroup>
                                </td>
                            </tr>
                        </table>

                        <h:panelGroup rendered="#{LoginControle.permissaoAcessoMenuVO.lancarVisualizarLancamentosEmpresas}">
                            <h:outputText styleClass="tituloDemonstrativo" value="#{msg_aplic.prt_Empresa_tituloForm}:"/>
                            <br/>
                            <!-- ----------------------EMPRESA ------------------------------------->
                            <div id="caixaEmpresas" style="width: 350px; height: 100px; overflow-x:hidden; overflow-y:auto; border:solid 1px #A9A9A9;background-color: #FFFFFF;"  >
                                <table bgcolor="#FFFFFF" width="100%">
                                    <tr>
                                        <td>&nbsp;&nbsp;</td>
                                        <td>
                                            <rich:dataTable style="border:none;" columnClasses="colunaFinan" width="100%" rowClasses="linhaParFinan,linhaImparFinan"
                                                            var="empresa" value="#{MovContaControle.filtrosLancamentos.listaEmpresa}">
                                                <h:column>
                                                    <h:selectBooleanCheckbox value="#{empresa.empresaEscolhida}"/>
                                                    <h:outputText value="#{empresa.nome}" styleClass="textblack"/>
                                                </h:column>
                                            </rich:dataTable>
                                        </td>
                                    </tr>
                                </table>
                            </div>
                        </h:panelGroup>
                    </td>
                    <td>&nbsp;</td>
                </tr>
            </table>

            <br/>
            <!-- ------------------------ FIM PARAMETRO 4  ------------------------------- -->
            <!-- ------------------------ INICIO PARAMETRO 5  ------------------------------- -->
            <div id="parametro5Expandido" class="Passo"
                 onclick="esconderMostrar('tabelaParametros5', true, 'parametro5Expandido','parametro5Retraido', 'formFiltrosLancamento:parametro5');"
                 style="display: none; cursor: pointer; background-image: url(../../imagens/folder/Passo5normal.png); background-repeat: no-repeat;">
                <div class="Imagem">
                    <img id="ctl00_cph_imgPasso1"
                         src="../../imagens/folder/PassoExpandir.jpg"
                         style="border-top-width: 0px; border-right-width: 0px; border-bottom-width: 0px; border-left-width: 0px;"
                         title="Ocultar" />
                </div>
                <div class="Texto">Filtro de pagamento Cheque</div>
                <div class="Info">
                    <span id="ctl00_cph_lblPasso1">Ocultar</span>
                </div>
            </div>
            <div id="parametro5Retraido"
                 class="Passo"
                 onclick="esconderMostrar('tabelaParametros5', false, 'parametro5Retraido','parametro5Expandido', 'formFiltrosLancamento:parametro5');"
                 style="cursor: pointer; background-image: url(../../imagens/folder/Passo5normal.png); background-repeat: no-repeat;">
                <div class="Imagem">
                    <img id="ctl00_cph_imgPasso1"
                         src="../../imagens/folder/PassoOcultar.jpg"
                         style="border-top-width: 0px; border-right-width: 0px; border-bottom-width: 0px; border-left-width: 0px;"
                         title="Ocultar" />
                </div>
                <div class="Texto">Filtro de pagamento Cheque</div>
                <div class="Info">
                    <span id="ctl00_cph_lblPasso1">Expandir</span>
                </div>
            </div>
            <table bgcolor="#F5F5F5" id="tabelaParametros5" width="100%" style="display: none;">
                <tr>
                    <td bgcolor="#F5F5F5" width="50%">
                        <h:panelGrid columns="4" rowClasses="linhaImpar, linhaPar"
                                     columnClasses="classEsquerda, classDireitaConfiguracaoSistema,classEsquerda, classDireitaConfiguracaoSistema" width="100%">

                            <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Finan_GestaoRecebiveis_banco}"/>
                            <h:selectOneMenu id="bancoPreenchido"
                                             value="#{MovContaControle.chequeVO.banco.codigo}"
                                             onblur="blurinput(this);"
                                             onfocus="focusinput(this);"
                                             styleClass="form">
                                <f:selectItems value="#{MovContaControle.listaSelectItemBanco}" />
                            </h:selectOneMenu>

                            <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Finan_GestaoRecebiveis_agencia}"/>
                            <h:inputText id="agencia" value="#{MovContaControle.chequeVO.agencia}"
                                         onblur="blurinput(this);" size="7" maxlength="5"
                                         onfocus="focusinput(this);" styleClass="form" />

                            <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Finan_GestaoRecebiveis_conta}" />
                            <h:inputText id="conta" value="#{MovContaControle.chequeVO.conta}"
                                         onblur="blurinput(this);" size="15" maxlength="20"
                                         onfocus="focusinput(this);" styleClass="form" />

                            <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Finan_GestaoRecebiveis_nCheque}" />
                            <h:inputText id="nDoc" value="#{MovContaControle.chequeVO.numero}"
                                         onblur="blurinput(this);" size="15" maxlength="15"
                                         onfocus="focusinput(this);" styleClass="form" />

                        </h:panelGrid>
                    </td>
                </tr>
            </table>

            <br/>
            <!-- ------------------------ FIM PARAMETRO 5  ------------------------------- -->
        </div>

        <br/>

        <a4j:commandButton id="consultarPesquisaPadrao"
                           style="vertical-align:middle;"
                           value="Pesquisar" actionListener="#{MovContaControle.consultarPaginadoListener}"
                           oncomplete="Richfaces.hideModalPanel('modalPanelFiltrosLancamento');"
                           reRender="form:pagarEmConjunto, items, painelPaginacao,containerFuncMask, painelPaginacaoTop, totalValor,totalRegistros,valorTotalReceb,valorTotalPag, panelMensagem, totalReg, valorTotal,panelTipoLancamento, filtros, checado, panelTotalizadoresTop, panelTotalizadoresButton, panelTotalizadoresTopNovo"
                           title="#{msg.msg_consultar_dados}" accesskey="2"
                           image="../../imagens/btn_Pesquisar.png">
            <f:attribute name="paginaInicial" value="paginaInicial" />
            <f:attribute name="tipoConsulta" value="detalhada"/>
        </a4j:commandButton>

        <rich:spacer width="20px"/>
        <a4j:commandButton style="vertical-align:middle;"
                           action="#{MovContaControle.limparFiltros}"
                           id="btnLimparFiltrosLanc"
                           value="Limpar Filtros"
                           reRender="formFiltrosLancamento"
                           oncomplete="if (document.getElementById('formFiltrosLancamento:plnContas')) atualizarTreeViewFiltrosLancamento();
                               if (document.getElementById('formFiltrosLancamento:cntCustos')) atualizarTreeView('CustosLan');"
                           accesskey="2"
                           image="../../imagens/btn_LimparFiltros.png"
                           title="#{msg.msg_consultar_dados}" />

        <h:inputHidden id="parametro1" value="#{MovContaControle.filtrosLancamentos.parametro1}"/>
        <h:inputHidden id="parametro2" value="#{MovContaControle.filtrosLancamentos.parametro2}"/>
        <h:inputHidden id="parametro3" value="#{MovContaControle.filtrosLancamentos.parametro3}"/>
        <h:inputHidden id="parametro4" value="#{MovContaControle.filtrosLancamentos.parametro4}"/>
        <h:inputHidden id="parametro5" value="#{MovContaControle.filtrosLancamentos.parametro5}"/>
        <h:inputHidden id="filtroPlanoContas" value="#{MovContaControle.filtroPlanoContas.codigosConcatenados}"/>
        <h:inputHidden id="filtroCentroCustos" value="#{MovContaControle.filtroCentroCustos.codigosConcatenados}"/>
        <h:panelGroup layout="block" id="containerFuncMask">
             <script>
                carregarMaskInput();
                function filtrarLista() {
                    var input, filtro, tabela, tr, td, i, valorInput;
                    input = document.getElementById("filtroInput");
                    filtro = input.value.toUpperCase();

                    // Usa querySelector com a classe, que não muda no JSF
                    tabela = document.getElementById("formFiltrosLancamento:dnd-filtroPlanosLan");// Encontra a tabela interna do dataTable
                    if (!tabela) return;

                    tr = tabela.getElementsByTagName("tr");
                    console.log("Linhas encontradas na tabela:", tr.length);

                    for (i = 0; i < tr.length; i++) {
                        td = tr[i].getElementsByTagName("td")[0];
                        if (td) {
                            valorInput = td.textContent || td.innerText;
                            if (valorInput.toUpperCase().indexOf(filtro) > -1) {
                                tr[i].style.display = "";
                            } else {
                                tr[i].style.display = "none";
                            }
                        }
                    }
                }
             </script>
        </h:panelGroup>
    </a4j:form>
</rich:modalPanel>



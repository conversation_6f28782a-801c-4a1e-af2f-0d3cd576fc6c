<%--
    Created on : 11/07/2024
    Author     : <PERSON>
--%>

<%@page contentType="text/html" %>
<%@page pageEncoding="ISO-8859-1" %>
<%@include file="includes/imports.jsp" %>
<h:panelGroup layout="block" styleClass="margin-box" rendered="#{MovContaControle.visaoLotes}" id="idLotes">
    <h:panelGroup layout="block" style="padding-bottom: 0;">
        <a4j:commandButton onclick="Richfaces.showModalPanel('modalPanelFiltrosLotes');"
                           action="#{MovContaControle.abrirModalRefazerConsultaLotes}"
                           id="refazerConsultaLotes"
                           value="Refazer consulta"
                           style="cursor: pointer;"
                           styleClass="botoes nvoBt"
                           reRender="formFiltrosLotes"
                           oncomplete="#{MovContaControle.abrirFecharModalFiltrosLotes}">
        </a4j:commandButton>
        <rich:panel id="filtrosLotesInfo"
                    style="border-color: #eee; background-color: #eee; margin-top: 15px;"
                    styleClass="tudo">
            <h:outputText styleClass="tituloCamposNegrito" value="Filtros:"/>
            <h:outputText styleClass="tituloDemonstrativo"
                          value="#{MovContaControle.filtrosLotes}"/>
        </rich:panel>
    </h:panelGroup>

    <rich:dataTable id="listaLotesPgto" width="100%" headerClass="consulta" rowClasses="linhaPar"
                    rendered="#{MovContaControle.listaLotesKobanaVO != null && !MovContaControle.listaLotesKobanaVO.isEmpty()}"
                    style="margin-top: 10px;"
                    reRender="paginaAtual, paginaAtualTop,painelPaginacaoTop,painelPaginacao"
                    columnClasses="centralizado, centralizado, centralizado, centralizado, direita, centralizado"
                    value="#{MovContaControle.listaLotesKobanaVO}"
                    rows="#{MovContaControle.listaLotes.limit}"
                    var="lote">

        <!-- código -->
        <rich:column>
            <f:facet name="header">
                <h:outputText styleClass="topoPequeno" value="Código"/>
            </f:facet>
            <h:panelGroup>
                <h:outputText value="#{lote.codigo}"/>
            </h:panelGroup>
        </rich:column>

        <!-- DT. criação -->
        <rich:column>
            <f:facet name="header">
                <h:outputText styleClass="topoPequeno" value="Dt. Criação"/>
            </f:facet>
            <h:outputText value="#{lote.createdAt_Apresentar}">
            </h:outputText>
        </rich:column>

        <!--Dt. Últ. atualização -->
        <rich:column>
            <f:facet name="header">
                <h:outputText styleClass="topoPequeno"
                              value="Dt. Últ. Atualização"/>
            </f:facet>
            <h:outputText value="#{lote.updatedAt_Apresentar}"
                          title="#{lote.titleUltAtualizacaoLoteExplicacao}"
                          styleClass="tooltipster">
            </h:outputText>
        </rich:column>

        <!-- tipo -->
        <rich:column>
            <f:facet name="header">
                <h:outputText styleClass="topoPequeno" value="Tipo"/>
            </f:facet>
            <h:panelGroup>
                <h:panelGrid columns="1" style="display: inline">
                    <h:graphicImage value="#{lote.imagemTipo_Apresentar}"
                                    style="#{lote.styleCssImagemTipo}"/>
                </h:panelGrid>
            </h:panelGroup>
        </rich:column>

        <!-- Qtd. Items -->
        <rich:column style="text-align-last: center;">
            <f:facet name="header">
                <h:outputText styleClass="topoPequeno" value="Qtd. Items"/>
            </f:facet>
            <h:outputText value="#{lote.qtd_Apresentar}">
            </h:outputText>
        </rich:column>

        <!-- valor -->
        <rich:column>
            <f:facet name="header">
                <h:outputText styleClass="topoPequeno" value="Valor"/>
            </f:facet>
            <h:outputText value="#{lote.valor_Apresentar}">
            </h:outputText>
        </rich:column>

        <!-- status -->
        <rich:column>
            <f:facet name="header">
                <h:outputText styleClass="topoPequeno" value="Status"/>
            </f:facet>
            <h:panelGroup id="statusLote" style="text-align-last: center;; display: block;">
                <a4j:commandLink style="#{lote.styleCssStatus}; border-radius: 20px!important; cursor: default"
                                 value="#{lote.status_Apresentar}"
                                 title="#{lote.titleExplicacaoStatusLote}"
                                 styleClass="botaoPrimario texto-size-10-real tooltipster"/>
            </h:panelGroup>
        </rich:column>

        <!-- status registro -->
        <rich:column>
            <f:facet name="header">
                <h:outputText styleClass="topoPequeno" value="Status do Registro"/>
            </f:facet>
            <h:outputText value="#{lote.registration_Status_Apresentar}">
            </h:outputText>
        </rich:column>

        <%--AÇÕES--%>
        <rich:column>
            <f:facet name="header">
                <h:outputText value="Ações"/>
            </f:facet>
            <a4j:commandLink id="aprovarLotePagamento"
                             actionListener="#{MovContaControle.prepararAprovarLotePagamento}"
                             title="#{lote.titleIconAprovarLote}"
                             disabled="#{!lote.permiteAprovarReprovarLote}"
                             style="text-decoration: none; #{lote.styleOpacityPermiteAprovarReprovarLote}"
                             styleClass="linkAzul texto-size-14 tooltipster"
                             reRender="mdlMensagemGenerica"
                             oncomplete="#{MovContaControle.mensagemNotificar}">
                <i class="fa-icon-thumbs-up" style="font-size: 18px; margin-top: 2px"></i>
                <f:attribute name="lote" value="#{lote}"/>
            </a4j:commandLink>
            <a4j:commandLink id="reprovarLotePagamento"
                             actionListener="#{MovContaControle.prepararReprovarLotePagamento}"
                             title="#{lote.titleIconReprovarLote}"
                             disabled="#{!lote.permiteAprovarReprovarLote}"
                             style="text-decoration: none; #{lote.styleOpacityPermiteAprovarReprovarLote}"
                             styleClass="linkAzul texto-size-14 tooltipster"
                             reRender="mdlMensagemGenerica"
                             oncomplete="#{MovContaControle.mensagemNotificar}">
                <i class="fa-icon-thumbs-down" style="font-size: 18px; margin-top: 2px; margin-left: 5px"></i>
                <f:attribute name="lote" value="#{lote}"/>
            </a4j:commandLink>
            <a4j:commandLink id="sincronizarLotePagamento"
                             actionListener="#{MovContaControle.prepararSincronizarLotePagamento}"
                             title="#{lote.titleIconSincronizarLote}"
                             disabled="#{!lote.permiteSincronizarLote}"
                             style="text-decoration: none; #{lote.styleOpacityPermiteSincronizarLote}"
                             styleClass="linkAzul texto-size-14 tooltipster"
                             reRender="mdlMensagemGenerica"
                             oncomplete="#{MovContaControle.mensagemNotificar}">
                <i class="fa-icon-refresh" style="font-size: 18px; margin-top: 2px; margin-left: 5px"></i>
                <f:attribute name="lote" value="#{lote}"/>
            </a4j:commandLink>
            <a4j:commandLink id="copiarUidDoLote"
                             rendered="#{lote.uid != '' && lote.uid != null}"
                             onclick="copyToClipboard('#{lote.uid}')"
                             title="#{lote.uidLoteCopiar}"
                             style="text-decoration: none;"
                             styleClass="linkAzul texto-size-14 tooltipster"
                             reRender="mdlMensagemGenerica">
                <i class="fa-icon-file-alt" style="font-size: 17px; margin-top: 2px; margin-left: 5px"></i>
            </a4j:commandLink>
            <a4j:commandLink id="detalhesLoteExistente"
                             actionListener="#{MovContaControle.abrirModalDetalheLotePagamentoExistente}"
                             title="Ver pagamentos do lote"
                             style="text-decoration: none"
                             styleClass="linkAzul texto-size-14 tooltipster"
                             reRender="modalLotePagamentoExistente"
                             oncomplete="#{MovContaControle.mensagemNotificar}#{MovContaControle.abrirFecharModalDetalheLotePagamentoExistente}">
                <h:panelGroup id="panelBotLista">
                    <i class="fa-icon-list" style="font-size: 16px; margin-left: 5px"></i>
                </h:panelGroup>
                <f:attribute name="lote" value="#{lote}"/>
            </a4j:commandLink>
        </rich:column>
    </rich:dataTable>

    <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada"
                 rendered="#{fn:length(MovContaControle.listaLotesKobanaVO) > 0}">
        <table width="100%" border="0" cellspacing="0" cellpadding="0">
            <tr>
                <td align="center" valign="middle">
                    <h:panelGroup layout="block"
                                  styleClass="paginador-container">
                        <h:panelGroup styleClass="pull-left"
                                      layout="block">

                            <h:outputText styleClass="texto-size-12 texto-cor-cinza"
                                          value="Total #{MovContaControle.listaLotes.count} itens"/>
                        </h:panelGroup>
                        <h:panelGroup layout="block"
                                      style="align-items: center">
                            <a4j:commandLink
                                    styleClass="linkPadrao texto-cor-azul texto-size-20-real"
                                    reRender="form:idLotes, listaLotesPgto"
                                    action="#{MovContaControle.primeiraPaginaConsultaLote}">
                                <i class="fa-icon-double-angle-left" id="primPagina"></i>
                            </a4j:commandLink>
                            <a4j:commandLink
                                    styleClass="linkPadrao texto-cor-azul texto-size-20-real"
                                    reRender="form:idLotes, listaLotesPgto"
                                    action="#{MovContaControle.paginaAnteriorConsultaLote}">
                                <i class="fa-icon-angle-left" id="pagAnt"></i>
                            </a4j:commandLink>
                            <h:outputText
                                    styleClass="texto-font texto-cor-cinza texto-size-16-real"
                                    value="Página #{MovContaControle.listaLotes.paginaAtualApresentar}"
                                    rendered="true"/>
                            <a4j:commandLink
                                    styleClass="linkPadrao texto-font texto-cor-azul texto-size-20-real"
                                    reRender="form:idLotes, listaLotesPgto"
                                    action="#{MovContaControle.proximaPaginaConsultaLote}">
                                <i class="fa-icon-angle-right" id="proxPag"></i>
                            </a4j:commandLink>
                            <a4j:commandLink
                                    styleClass="linkPadrao texto-cor-azul texto-size-20-real"
                                    reRender="form:idLotes, listaLotesPgto"
                                    action="#{MovContaControle.ultimaPaginaConsultaLote}">
                                <i class="fa-icon-double-angle-right" id="ultimaPagina"></i>
                            </a4j:commandLink>
                        </h:panelGroup>
                        <h:panelGroup layout="block" styleClass="paginador-itens-pagina">
                            <h:panelGroup styleClass="pull-right" layout="block">
                                <h:outputText styleClass="texto-size-12 texto-cor-cinza" value="Itens por página "/>
                            </h:panelGroup>
                            &nbsp;
                            <h:panelGroup styleClass="cb-container pl20" layout="block">
                                <h:selectOneMenu value="#{MovContaControle.listaLotes.limit}"
                                                 style="font-size: 14px !important;" id="qtdeItensPaginaProd">
                                    <f:selectItem itemValue="#{10}"/>
                                    <f:selectItem itemValue="#{20}"/>
                                    <a4j:support event="onchange"
                                                 action="#{MovContaControle.atualizarNumeroItensPagina}"
                                                 reRender="form:idLotes, listaLotesPgto">
                                    </a4j:support>
                                </h:selectOneMenu>
                            </h:panelGroup>

                        </h:panelGroup>
                    </h:panelGroup>
                </td>
            </tr>
        </table>
    </h:panelGrid>
</h:panelGroup>

<script>
    function copyToClipboard(elcopy) {
        console.log(elcopy);
        // Cria um campo de texto temporário para copiar o valor
        var el = document.createElement('textarea');
        el.value = elcopy;  // Usa o valor diretamente, sem necessidade de querySelector
        document.body.appendChild(el);
        el.select();
        document.execCommand('copy');
        document.body.removeChild(el);

        Notifier.info('Uid: (' + el.value + ') copiado para a área de transferência.');
    }
</script>

<style>
    .table-striped th {
        border: 1px solid #e5e5e5;
        border-right: none;
        border-left: none;
        padding: 5px 0;
        text-transform: uppercase;
    }

    .table-striped td {
        text -align: center;
        padding: 2px;
    }

    .table-striped tr:nth-child(even) {
        background: #e5e5e5
    }

    .table-striped tr:hover {
        background: #d3d3d3;
    }

    .table-striped thead tr:hover {
        background: #fff;
    }

    .table-striped thead tr th {
        padding: 10px 5px;
    }

    .table-striped tbody tr td a i {
        color: #777;
    }

    .table-striped-separator + .table-striped-separator {
        border -left: 1px solid black;
    }

    .table-striped-options a {
        margin: 2px;
    }

    .table-info tr td {
        padding: 10px;
        text-transform: uppercase;
    }

    .conciliacao-info div {
        flex: 1;
        text-align: center;
    }

    .conciliacao-info div > span {
        display: block;
        color: #666;
        font-size: 20px;
        margin-bottom: 20px;
    }

    .linkalunoextrato span {
        text -transform: capitalize;
        font-size: 14.5px !important;
        font-weight: normal;
    }

    .linhacancelamento td, .linhacancelamento td .linkalunoextrato span {
        color: red !important;
    }
</style>

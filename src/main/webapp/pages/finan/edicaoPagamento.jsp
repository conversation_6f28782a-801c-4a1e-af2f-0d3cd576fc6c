<%--
    Document   : edicaoPagamento.jsp
    Created on : 17/08/2011, 14:16:41
    Author     : <PERSON><PERSON><PERSON>
--%>
<%@page pageEncoding="ISO-8859-1" %>
<head>
    <%@include file="../../includes/include_import_minifiles.jsp"%>
    <script src="../../script/time_1.3.js" type="text/javascript" ></script>
     <script type="text/javascript" language="javascript" src="../../script/gobackblock.js"></script>
    <script src="../../script/packJQueryPlugins.min.js" type="text/javascript" ></script>
</head>
<link href="/css/otimize.css" rel="stylesheet" type="text/css">
<script type="text/javascript" language="javascript">
    setDocumentCookie('popupsImportante', 'close',1);
</script>
<style>
    .tablelistrasMy {
        border-top: none !important;
        border-bottom: none !important;
    }

    .tablelistrasMy td {
        border-top: none !important;
        border-bottom: none !important;
    }
</style>

<f:view>
    <jsp:include page="/includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText value="Edição de Pagamento"/>
    </title>
    <h:form id="form">
        <a4j:loadScript src="/script/jquery.maskedinput-1.2.2.js" />
        <html>
        <head>
            <script type="text/javascript" language="javascript" src="${contextoFinan}/script/telaInicial.js"></script>
        </head>
        <body>
        <h:panelGroup layout="block" style="display: inline-table;width: 100%;" styleClass="fundoCinza">
            <h:panelGroup layout="block" styleClass="bgtop topoZW menu-#{MenuControle.apresentarMenu}" rendered="#{MenuControle.apresentarTopo}">
                <c:if test="${MenuControle.apresentarMenu}">
                    <jsp:include page="../../include_topo_novo.jsp" flush="true"/>
                </c:if>
                <jsp:include page="../../include_menu_zw_flat.jsp" flush="true"/>
                <rich:jQuery selector=".item3" query="addClass('menuItemAtual')"/>
            </h:panelGroup>

            <h:panelGroup layout="block" styleClass="caixaCorpo">
                <h:panelGroup layout="block" style="height: 80%;width: 100%">
                    <h:panelGroup layout="block" styleClass="caixaMenuLatel">
                                <h:panelGroup layout="block" styleClass="container-imagem container-conteudo-central" style="width: 100%;">
                            <h:panelGroup layout="block" styleClass="container-box zw_ui especial ">
                                <h:panelGroup styleClass="container-box-header" layout="block">
                                    <h:panelGroup layout="block" styleClass="margin-box">
                                        <h:outputText styleClass="container-header-titulo" value="Edição de Pagamento "/>

                                    </h:panelGroup>
                                </h:panelGroup>
                                <h:panelGroup layout="block" styleClass="margin-box">
                                    <h:panelGroup rendered="#{!EdicaoPagamentoControle.processado}">
                                        <table width="100%" height="70%" border="0" cellpadding="0"
                                               cellspacing="0" class="textsmall"
                                               style="margin-bottom: 25px;">
                                            <tr>
                                                <td>
                                                    <table width="100%" border="0" cellspacing="0" cellpadding="0">
                                                        <tr>
                                                            <td>
                                                                <h:panelGrid id="mensagem" columns="2" width="100%">
                                                                    <c:if test="${not empty EdicaoPagamentoControle.mensagemDetalhada}">
                                                                        <h:outputText id="msgPagDet" styleClass="mensagemDetalhada"
                                                                                      value="#{EdicaoPagamentoControle.mensagemDetalhada}" />
                                                                    </c:if>
                                                                </h:panelGrid>
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <td align="left" valign="top">
                                                                <table width="100%" border="0" cellspacing="0" cellpadding="0">
                                                                    <tr>
                                                                        <td>
                                                                            <h:panelGrid style="margin: 10px 0 20px 20px;" styleClass="textsmall" border="0" cellspacing="0" cellpadding="0">
                                                                                <h:panelGroup>
                                                                                            <h:outputText style="font-weight: bold" styleClass="texto-font texto-size-14-real texto-bold texto-cor-cinza" value="Recibo: "/>
                                                                                            <h:outputText  styleClass="texto-font texto-size-16-real texto-cor-cinza"  value="#{EdicaoPagamentoControle.recibo.codigo}" />
                                                                                </h:panelGroup>
                                                                                <h:panelGroup>
                                                                                            <h:outputText  styleClass="texto-font texto-size-14-real texto-bold texto-cor-cinza" style="font-weight: bold"
                                                                                                  value="Nome Responsável Pagamento: "/>
                                                                                            <h:outputText  styleClass="texto-font texto-size-16-real texto-cor-cinza"
                                                                                                  value="#{EdicaoPagamentoControle.recibo.nomePessoaPagador}" />
                                                                                </h:panelGroup>
                                                                                <h:panelGroup>
                                                                                            <h:outputText styleClass="texto-font texto-size-14-real texto-bold texto-cor-cinza" style="font-weight: bold"
                                                                                                  value="Data Pagamento: "/>
                                                                                            <h:outputText  styleClass="texto-font texto-size-16-real texto-cor-cinza"
                                                                                                  value="#{EdicaoPagamentoControle.recibo.data_Apresentar}" />
                                                                                </h:panelGroup>
                                                                            </h:panelGrid>
                                                                        </td>
                                                                    </tr>
                                                                </table>
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <td align="left" valign="top">
                                                                <rich:dataTable id="MovPagamentoCheque" cellpadding="0"
                                                                                        width="100%" style="border:none;" columnClasses="colunaCentralizada"
                                                                                        value="#{EdicaoPagamentoControle.cheques}" styleClass="tabelaSimplesCustom"
                                                                                rendered="#{!EdicaoPagamentoControle.readOnly}" var="cheque">
                                                                            <rich:column styleClass="col-text-align-center" headerClass="col-text-align-center">
                                                                                <f:facet name="header">
                                                                                    <h:outputText value="Trocar"
                                                                                                  styleClass="texto-size-14-real texto-cor-cinza texto-bold texto-font"/>
                                                                                </f:facet>
                                                                                <h:selectBooleanCheckbox
                                                                                        title="#{EdicaoPagamentoControle.titleCheck}"
                                                                                        styleClass="tooltipster"
                                                                                        value="#{cheque.selecionado}"
                                                                                        rendered="#{((!cheque.temLote || !ConfiguracaoFinanceiroControle.confFinanceiro.usarMovimentacaoContas)
                                                                                                       		    && (cheque.situacao != 'CA' || cheque.selecionado == true) && cheque.situacao != 'DV'
                                                                                                                && !cheque.usadoPagarContaFinanceiro
                                                                                                                && EdicaoPagamentoControle.situacaoContratoRecibo != 'CA' )}">
                                                                                    <a4j:support event="onchange"
                                                                                                 action="#{EdicaoPagamentoControle.selecionarCheque}"
                                                                                                 reRender="MovPagamentoCheque, totalizadores, pnlParcela, mensagem, mensagem1"/>
                                                                                </h:selectBooleanCheckbox>

                                                                                <h:graphicImage id ="imgChequeCompensadoContratoCancelado"
                                                                                                rendered="#{((!cheque.temLote || !ConfiguracaoFinanceiroControle.confFinanceiro.usarMovimentacaoContas)
                                                                                                       		    && (cheque.situacao != 'CA' || cheque.selecionado == true) && cheque.situacao != 'DV'
                                                                                                                && !cheque.usadoPagarContaFinanceiro
                                                                                                                && EdicaoPagamentoControle.situacaoContratoRecibo == 'CA' )}"
                                                                                                url="../../imagens/atencaoRisco.gif"
                                                                                                styleClass="tooltipster"
                                                                                                style="width:15px;height:15px;"
                                                                                                title="Este cheque não pode ser trocado, pois refere-se ao pagamento de um contrato que está cancelado. Os recebimentos com compensação futura, no ato do cancelamento, não foram devolvidos/estornados."/>

                                                                        <h:graphicImage id ="imgAdvertencia" rendered="#{cheque.avisoVinculos != '' && ((!cheque.temLote || !ConfiguracaoFinanceiroControle.confFinanceiro.usarMovimentacaoContas)
                                                                                                       		    && ((cheque.situacao != 'CA' && cheque.situacao != 'DV') || cheque.selecionado == true)
                                                                                                                && !cheque.usadoPagarContaFinanceiro)}"
                                                                                        url="../../imagens/atencaoRisco.gif"
                                                                                        styleClass="tooltipster"
                                                                                        style="width:15px;height:15px;"
                                                                                        title="#{cheque.avisoVinculos}"/>

                                                                        <h:outputLink value="#{SuperControle.urlBaseConhecimento}como-editar-os-dados-de-um-recebivel-sem-estornar-o-recibo-edicao-de-recibo/"
                                                                                      target="_blank" style="vertical-align: top;"
                                                                                      title="Este cheque não pode ser excluído pois está no lote #{cheque.loteVO.codigo}. Clique para mais informações."
                                                                                      rendered="#{cheque.temLote && !cheque.usadoPagarContaFinanceiro
                                                                                                              && ConfiguracaoFinanceiroControle.confFinanceiro.usarMovimentacaoContas}">
                                                                            <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                                                                        </h:outputLink>

                                                                        <h:outputLink value="#{SuperControle.urlBaseConhecimento}como-editar-os-dados-de-um-recebivel-sem-estornar-o-recibo-edicao-de-recibo/"
                                                                                      target="_blank" style="vertical-align: top;"
                                                                                      title="Este cheque não pode ser editado ou excluído pois foi usado para pagar uma conta no financeiro. O código do lançamento ? #{cheque.pagaContaFinanceiro}. Clique e saiba mais: "
                                                                                      rendered="#{cheque.usadoPagarContaFinanceiro
                                                                                                              && ConfiguracaoFinanceiroControle.confFinanceiro.usarMovimentacaoContas}">
                                                                            <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                                                                        </h:outputLink>

                                                                        <h:outputLink value="#{SuperControle.urlBaseConhecimento}como-editar-os-dados-de-um-recebivel-sem-estornar-o-recibo-edicao-de-recibo/"
                                                                                      target="_blank" style="vertical-align: top;"
                                                                                      title="Este cheque não pode ser editado ou excluído pois está cancelado ou está associado a outro pagamento. Clique e saiba mais: "
                                                                                      rendered="#{(!cheque.usadoPagarContaFinanceiro)
                                                                                                               && (cheque.situacao == 'CA' && cheque.situacao != 'DV' && cheque.selecionado == false)
                                                                                                               && ConfiguracaoFinanceiroControle.confFinanceiro.usarMovimentacaoContas}">
                                                                            <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                                                                        </h:outputLink>
                                                                        <h:outputLink value="#{SuperControle.urlBaseConhecimento}como-editar-os-dados-de-um-recebivel-sem-estornar-o-recibo-edicao-de-recibo/"
                                                                                      target="_blank" style="vertical-align: top;"
                                                                                      title="Este cheque não pode ser editado ou excluído pois já teve devolução realizada. Clique e saiba mais: "
                                                                                      rendered="#{(!cheque.usadoPagarContaFinanceiro)
                                                                                                               && (cheque.situacao == 'DV')
                                                                                                               && ConfiguracaoFinanceiroControle.confFinanceiro.usarMovimentacaoContas}">
                                                                            <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                                                                        </h:outputLink>
                                                                    </rich:column>
                                                                            <rich:column styleClass="col-text-align-left" headerClass="col-text-align-left">
                                                                        <f:facet name="header">
                                                                                    <h:outputText value="Cód. Banco" styleClass="texto-size-14-real texto-cor-cinza texto-bold texto-font" />
                                                                        </f:facet>
                                                                                <h:inputText size="1" maxlength="3"
                                                                                     onfocus="focusinput(this);" onkeypress="return mascara(this.form, this.id, '999999', event);"
                                                                                             value="#{cheque.banco.codigoBanco}" styleClass="inputTextClean texto-size-14-real">
                                                                            <a4j:support event="onchange"
                                                                                         ajaxSingle="true"
                                                                                         reRender="MovPagamentoCheque,mensagem1,mensagem"
                                                                                         actionListener="#{EdicaoPagamentoControle.atualizarBancoEvent}"/>
                                                                        </h:inputText>
                                                                    </rich:column>
                                                                            <rich:column styleClass="font-size-Em-max col-text-align-left" headerClass="col-text-align-left">
                                                                        <f:facet name="header">
                                                                                    <h:outputText value="Banco" styleClass="texto-size-14-real texto-cor-cinza texto-bold texto-font"  />
                                                                        </f:facet>
                                                                                <h:panelGroup layout="block" styleClass="cb-container" rendered="#{cheque.situacao != 'CA' && cheque.situacao != 'DV'}">
                                                                                    <h:selectOneMenu
                                                                                            id="bancoPreenchido"

                                                                                            value="#{cheque.banco.codigo}"
                                                                                            onblur="blurinput(this);"
                                                                                            onfocus="focusinput(this);"
                                                                                         styleClass="form">
                                                                                        <f:selectItems
                                                                                                value="#{EdicaoPagamentoControle.listaSelectItemBanco}"/>
                                                                                        <a4j:support event="onchange"
                                                                                                     status="statusHora"
                                                                                                     action="#{EdicaoPagamentoControle.alterado}"
                                                                                                     reRender="MovPagamentoCheque"/>
                                                                        </h:selectOneMenu>
                                                                                </h:panelGroup>
                                                                        <h:inputText rendered="#{cheque.situacao == 'CA' || cheque.situacao == 'DV'}" readonly="true"
                                                                                             style="width:90%;" value="#{cheque.banco.nome}" styleClass="inputTextClean texto-size-14-real"/>
                                                                    </rich:column>
                                                                            <rich:column styleClass="col-text-align-left" headerClass="col-text-align-left">
                                                                        <f:facet name="header">
                                                                                    <h:outputText value="Agência" styleClass="texto-size-14-real texto-cor-cinza texto-bold texto-font"  />
                                                                        </f:facet>
                                                                        <h:inputText id="agencia" value="#{cheque.agencia}" readonly="#{cheque.situacao == 'CA' || cheque.situacao == 'DV'}"
                                                                                     size="4" maxlength="6" onblur="blurinput(this);"
                                                                                             onfocus="focusinput(this);" styleClass="inputTextClean texto-size-14-real">
                                                                            <a4j:support event="onchange" status="statusHora" action="#{EdicaoPagamentoControle.alterado}"/>
                                                                        </h:inputText>
                                                                    </rich:column>
                                                                            <rich:column styleClass="col-text-align-left" headerClass="col-text-align-left">
                                                                        <f:facet name="header">
                                                                                    <h:outputText value="Conta" styleClass="texto-size-14-real texto-cor-cinza texto-bold texto-font"  />
                                                                        </f:facet>
                                                                        <h:inputText id="conta" value="#{cheque.conta}" readonly="#{cheque.situacao == 'CA' || cheque.situacao == 'DV'}"
                                                                                             size="4" maxlength="11" onblur="blurinput(this);"
                                                                                             onfocus="focusinput(this);" styleClass="inputTextClean texto-size-14-real">
                                                                            <a4j:support event="onchange" status="statusHora" action="#{EdicaoPagamentoControle.alterado}"/>
                                                                        </h:inputText>
                                                                    </rich:column>
                                                                            <rich:column styleClass="col-text-align-left" headerClass="col-text-align-left">
                                                                        <f:facet name="header">
                                                                                    <h:outputText value="Cheque" styleClass="texto-size-14-real texto-cor-cinza texto-bold texto-font"  />
                                                                        </f:facet>
                                                                        <h:inputText id="nDoc" value="#{cheque.numero}" readonly="#{cheque.situacao == 'CA' || cheque.situacao == 'DV'}"
                                                                                             size="2" maxlength="10" onblur="blurinput(this);"
                                                                                             onfocus="focusinput(this);" styleClass="inputTextClean texto-size-14-real">
                                                                            <a4j:support event="onchange" status="statusHora" action="#{EdicaoPagamentoControle.alterado}"/>
                                                                        </h:inputText>
                                                                    </rich:column>
                                                                            <rich:column styleClass="col-text-align-left" headerClass="col-text-align-left">
                                                                        <f:facet name="header">
                                                                                    <h:outputText value="Nome Terceiro" styleClass="texto-size-14-real texto-cor-cinza texto-bold texto-font"  />
                                                                        </f:facet>
                                                                        <h:inputText id="nomeNoCheque" value="#{cheque.nomeNoCheque}" readonly="#{cheque.situacao == 'CA' || cheque.situacao == 'DV'}"
                                                                                     size="30" maxlength="120" onblur="blurinput(this);"
                                                                                             onfocus="focusinput(this);" styleClass="inputTextClean texto-size-14-real">
                                                                            <a4j:support event="onchange" status="statusHora" action="#{EdicaoPagamentoControle.alterado}"/>
                                                                        </h:inputText>
                                                                    </rich:column>
                                                                            <rich:column styleClass="col-text-align-left" headerClass="col-text-align-left">
                                                                        <f:facet name="header">
                                                                                    <h:outputText value="#{EdicaoPagamentoControle.displayIdentificadorFront[0]}/#{EdicaoPagamentoControle.displayIdentificadorFront[2]}" styleClass="texto-size-14-real texto-cor-cinza texto-bold texto-font"  />
                                                                        </f:facet>
                                                                        <h:panelGrid columnClasses="tablelistrasMy,tablelistrasMy" cellpadding="0" cellspacing="0" columns="2">
                                                                            <h:panelGroup>
                                                                                <h:selectOneRadio rendered="#{cheque.situacao != 'CA' && cheque.situacao != 'DV'}" id="radioCPFCNPJ" style="font-size:8px;" value="#{cheque.cpfOuCnpj}">
                                                                                    <f:selectItem itemLabel="#{ChequeControle.displayIdentificadorFront[0]}" itemValue="CPF"/>
                                                                                    <f:selectItem itemLabel="#{ChequeControle.displayIdentificadorFront[2]}" itemValue="CNPJ"/>
                                                                                    <a4j:support event="onchange" focus="#{cheque.cpfOuCnpj}" reRender="panelCPFCNPJ"/>
                                                                                </h:selectOneRadio>
                                                                            </h:panelGroup>
                                                                            <h:panelGroup id="panelCPFCNPJ">
                                                                                <h:inputText id="CPF" value="#{cheque.cpf}"
                                                                                             rendered="#{cheque.cpfOuCnpj == 'CPF'}"
                                                                                             onblur="blurinput(this);" readonly="#{cheque.situacao == 'CA' || cheque.situacao == 'DV'}"
                                                                                             onkeypress="return mascara(this.form, this.id, '999.999.999-99', event);"
                                                                                                     onfocus="focusinput(this);" size="10" maxlength="14"
                                                                                                     styleClass="inputTextClean texto-size-14-real">
                                                                                    <a4j:support event="onchange" status="statusHora" action="#{EdicaoPagamentoControle.alterado}"/>
                                                                                </h:inputText>
                                                                                <h:inputText id="CNPJ" value="#{cheque.cnpj}"
                                                                                             rendered="#{cheque.cpfOuCnpj == 'CNPJ'}"
                                                                                             onblur="blurinput(this);" readonly="#{cheque.situacao == 'CA' || cheque.situacao == 'DV'}"
                                                                                             onkeypress="return mascara(this.form,this.id,'99.999.999/9999-99', event);"
                                                                                                     onfocus="focusinput(this);" size="10" maxlength="18"
                                                                                                     styleClass="inputTextClean texto-size-14-real">
                                                                                    <a4j:support event="onchange" status="statusHora" action="#{EdicaoPagamentoControle.alterado}"/>
                                                                                </h:inputText>
                                                                            </h:panelGroup>
                                                                        </h:panelGrid>
                                                                    </rich:column>
                                                                    <rich:column rendered="#{!EdicaoPagamentoControle.apresentarCPF}">
                                                                        <f:facet name="header">
                                                                                    <h:outputText value="#{ChequeControle.displayIdentificadorFront[0]}" styleClass="texto-size-14-real texto-cor-cinza texto-bold texto-font"  />
                                                                        </f:facet>

                                                                    </rich:column>
                                                                            <rich:column styleClass="col-text-align-left" headerClass="col-text-align-left">
                                                                        <f:facet name="header">
                                                                                    <h:outputText value="Compensação" styleClass="texto-size-14-real texto-cor-cinza texto-bold texto-font"  />
                                                                        </f:facet>
                                                                                <h:panelGroup id="dataCheque" layout="block" styleClass="dateTimeCustom">
                                                                            <rich:calendar id="dataCompensacao" readonly="#{cheque.usadoPagarContaFinanceiro || cheque.situacao == 'CA' || cheque.situacao == 'DV'}"
                                                                                           value="#{cheque.dataCompensacao}" inputSize="7"
                                                                                           inputClass="form" oninputblur="blurinput(this);"
                                                                                           oninputfocus="focusinput(this);"
                                                                                                   buttonIcon="/imagens_flat/calendar-button.svg"
                                                                                           oninputchange="return validar_Data(this.id);"
                                                                                           datePattern="dd/MM/yyyy" enableManualInput="true"
                                                                                           showWeeksBar="false">
                                                                                <a4j:support event="onchange" status="statusHora" action="#{EdicaoPagamentoControle.alterado}"/>
                                                                            </rich:calendar>
                                                                            <h:message for="dataCompensacao"
                                                                                       styleClass="mensagemDetalhada" />
                                                                        </h:panelGroup>
                                                                    </rich:column>
                                                                            <rich:column styleClass="col-text-align-right" headerClass="col-text-align-right">
                                                                        <f:facet name="header">
                                                                                    <h:outputText value="Valor" styleClass="texto-size-14-real texto-cor-cinza texto-bold texto-font"  />
                                                                        </f:facet>
                                                                        <h:inputText id="valor" value="#{cheque.valor}"
                                                                                     readonly="true"
                                                                                             style="text-align:right" onblur="blurinput(this);" maxlength="7" size="8"
                                                                                             onfocus="focusinput(this);" styleClass="inputTextClean texto-size-14-real">
                                                                            <f:converter converterId="FormatadorNumerico" />
                                                                            <a4j:support event="onchange" reRender="totalizadores, mensagem, mensagem1" status="statusHora" action="#{EdicaoPagamentoControle.alteradoComValor}"/>
                                                                        </h:inputText>
                                                                    </rich:column>
                                                                </rich:dataTable>
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                                    <td align="right">
                                                                        <h:panelGroup layout="block"
                                                                                      styleClass="container-botoes" style="line-height: 80px;height: 80px;">
                                                                            <h:panelGroup id="pnlParcela" layout="block" style="display: inline-block;padding-right: 5px;margin-top: 10px;">
                                                                                <h:outputText style="display: block;line-height: normal;"  rendered="#{EdicaoPagamentoControle.existemChequesParaDevolver}" styleClass="texto-font textos-size-16-real texto-cor-cinza"
                                                                                  value="Data da nova parcela:"/>
                                                                            <h:panelGroup layout="block" style="display: inline-block"  rendered="#{EdicaoPagamentoControle.existemChequesParaDevolver}" styleClass="dateTimeCustom">
                                                                                <rich:calendar
                                                                                        value="#{EdicaoPagamentoControle.dataNovaParcela}"
                                                                                        inputSize="7"
                                                                                   direction="top-left"
                                                                                        inputClass="form"
                                                                                        oninputblur="blurinput(this);"
                                                                                   oninputfocus="focusinput(this);"
                                                                                   oninputchange="return validar_Data(this.id);"
                                                                                        buttonIcon="/imagens_flat/calendar-button.svg"
                                                                                        datePattern="dd/MM/yyyy"
                                                                                        enableManualInput="true"
                                                                                   showWeeksBar="false"/>
                                                                            </h:panelGroup>
                                                                                <rich:jQuery id="mskData"
                                                                                             selector=".rich-calendar-input"
                                                                                             timing="onload"
                                                                                             query="mask('99/99/9999')"/>
                                                                </h:panelGroup>
                                                                            <a4j:commandLink value="Resetar"
                                                                                             status="statusHora"
                                                                                   title="Volta a lista original de cheque."
                                                                                   action="#{EdicaoPagamentoControle.resetar}"
                                                                                   rendered="#{!EdicaoPagamentoControle.readOnly}"
                                                                                           styleClass="botaoSecundario texto-size-14-real"
                                                                                   reRender="MovPagamentoCheque, totalizadores, mensagem, mensagem1, richPanel">
                                                                        </a4j:commandLink>
                                                                            <a4j:commandLink value="Gerar Datas"
                                                                                             status="statusHora"
                                                                                   title="Programa as Datas dos Cheques de 30 em 30 dias.Não são alterados cheques em lote ou cancelados."
                                                                                   action="#{EdicaoPagamentoControle.gerarDatas}"
                                                                                   rendered="#{!EdicaoPagamentoControle.readOnly}"
                                                                                           style="margin-left: 8px;"
                                                                                           styleClass="botaoSecundario texto-size-14-real"
                                                                                   reRender="MovPagamentoCheque, mensagem1,mensagem">
                                                                        </a4j:commandLink>
                                                                        </h:panelGroup>
                                                            </td>
                                                        </tr>
                                                    </table>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>
                                                    <h:panelGroup rendered="#{EdicaoPagamentoControle.tipoEdicao == 'CA'}"
                                                                  id="abaCartaoCredito">
                                                        <fieldset>
                                                            <legend>
                                                                <h:outputText value="Cartão de Crédito"/>
                                                            </legend>


                                                            <h:panelGrid columns="2" columnClasses="colunaEsquerda,colunaEsquerda">
                                                                <h:outputText styleClass="text" value="Valor: "/>
                                                                <h:inputText id="valorCC"
                                                                             readonly="#{EdicaoPagamentoControle.readOnly}"
                                                                             value="#{EdicaoPagamentoControle.pagamento.valor}"
                                                                             onblur="blurinput(this);" onfocus="focusinput(this);"
                                                                             styleClass="form">
                                                                    <a4j:support event="onchange" status="statusHora" reRender="totalizadores, mensagem, mensagem1"
                                                                                 action="#{EdicaoPagamentoControle.calcularTotalFinal}"/>
                                                                    <f:converter converterId="FormatadorNumerico" />
                                                                </h:inputText>
                                                                <h:outputText value="Operadora de Cartão:" styleClass="text"/>
                                                                <h:selectOneMenu id="operadoraCartao"
                                                                                 value="#{EdicaoPagamentoControle.pagamento.operadoraCartaoVO.codigo}"
                                                                                 onblur="blurinput(this);" onfocus="focusinput(this);"
                                                                                 styleClass="form">
                                                                    <a4j:support event="onchange" reRender="form, nrParcelaCartao" action="#{EdicaoPagamentoControle.atualizarOperadoraCartao}"/>
                                                                    <f:selectItems value="#{EdicaoPagamentoControle.listaSelectItemOperadoraCartaoCredito}" />
                                                                </h:selectOneMenu>
                                                                <h:outputText value="Parcelas:" styleClass="text"/>
                                                                <h:selectOneMenu id="nrParcelaCartao"
                                                                                 disabled="#{EdicaoPagamentoControle.readOnly}"
                                                                                 value="#{EdicaoPagamentoControle.pagamento.nrParcelaCartaoCredito}"
                                                                                 onblur="blurinput(this);" onfocus="focusinput(this);"
                                                                                 styleClass="form">
                                                                    <f:selectItems value="#{EdicaoPagamentoControle.listaSelectItemNrParcelaCartao}" />
                                                                </h:selectOneMenu>
                                                                <h:outputText styleClass="text"
                                                                              value="Autorização:"/>
                                                                <h:inputText id="autorizacaoCC" size="15" maxlength="15"
                                                                             value="#{EdicaoPagamentoControle.pagamento.autorizacaoCartao}"
                                                                             onblur="blurinput(this);" onfocus="focusinput(this);"
                                                                             styleClass="form"/>

                                                                <h:outputText rendered="#{EdicaoPagamentoControle.pagamento.formaPagamento.apresentarNSU}" styleClass="text" value="NSU:"/>
                                                                <h:inputText rendered="#{EdicaoPagamentoControle.pagamento.formaPagamento.apresentarNSU}" id="nsuCC" size="10" maxlength="20"
                                                                             value="#{EdicaoPagamentoControle.pagamento.nsu}"
                                                                             onblur="blurinput(this);" onfocus="focusinput(this);"
                                                                             styleClass="form"/>

                                                                <h:outputText styleClass="text"
                                                                              value="Data Lançamento: "/>
                                                                <h:inputText styleClass="form"
                                                                             readonly="true"
                                                                             value="#{EdicaoPagamentoControle.recibo.data_Apresentar}"
                                                                             title="A data do lançaamento não pode ser alterada pois isso alteraria o passado de relatórios como o fechamento de caixa por operador. Se deseja alterar datas, estorne o recibo e lance o pagamento novamente.">
                                                                </h:inputText>
                                                            </h:panelGrid>
                                                        </fieldset>
                                                    </h:panelGroup>

                                                    <h:panelGroup rendered="#{EdicaoPagamentoControle.tipoEdicao == 'CD'}"
                                                                  id="abaCartaoDebito">
                                                        <fieldset>
                                                            <legend>
                                                                <h:outputText value="Cartão de Débito"/>
                                                            </legend>

                                                            <h:panelGrid columns="2" columnClasses="colunaEsquerda">
                                                                <h:outputText styleClass="text" value="Valor: "/>
                                                                <h:inputText id="valorCD"
                                                                             readonly="#{EdicaoPagamentoControle.readOnly}"
                                                                             value="#{EdicaoPagamentoControle.pagamento.valor}"
                                                                             onblur="blurinput(this);" onfocus="focusinput(this);"
                                                                             styleClass="form">
                                                                    <a4j:support event="onchange" status="statusHora" reRender="totalizadores, mensagem, mensagem1"
                                                                                 action="#{EdicaoPagamentoControle.calcularTotalFinal}"/>
                                                                    <f:converter converterId="FormatadorNumerico" />
                                                                </h:inputText>
                                                                <h:outputText value="Operadora de Cartão:" styleClass="text"/>
                                                                <h:selectOneMenu id="operadoraCartao1"
                                                                                 value="#{EdicaoPagamentoControle.pagamento.operadoraCartaoVO.codigo}"
                                                                                 onblur="blurinput(this);" onfocus="focusinput(this);"
                                                                                 styleClass="form">
                                                                    <f:selectItems value="#{EdicaoPagamentoControle.listaSelectItemOperadoraCartaoDebito}" />
                                                                </h:selectOneMenu>
                                                                <h:outputText styleClass="text"
                                                                              value="Autorização:"/>
                                                                <h:inputText size="15" maxlength="15"
                                                                             value="#{EdicaoPagamentoControle.pagamento.autorizacaoCartao}"
                                                                             onblur="blurinput(this);" onfocus="focusinput(this);"
                                                                             styleClass="form">
                                                                </h:inputText>

                                                                <h:outputText rendered="#{EdicaoPagamentoControle.pagamento.formaPagamento.apresentarNSU}" styleClass="text" value="NSU:"/>
                                                                <h:inputText rendered="#{EdicaoPagamentoControle.pagamento.formaPagamento.apresentarNSU}" id="nsuCD" size="10" maxlength="20"
                                                                             value="#{EdicaoPagamentoControle.pagamento.nsu}"
                                                                             onblur="blurinput(this);" onfocus="focusinput(this);"
                                                                             styleClass="form"/>

                                                                <h:outputText styleClass="text"
                                                                              value="Data Lançamento: "/>
                                                                <h:inputText styleClass="form"
                                                                             readonly="true"
                                                                             value="#{EdicaoPagamentoControle.recibo.data_Apresentar}"
                                                                             title="A data do lançamento não pode ser alterada pois isso alteraria o passado de relatórios como o fechamento de caixa por operador. Se deseja alterar datas, estorne o recibo e lance o pagamento novamente.">
                                                                </h:inputText>
                                                            </h:panelGrid>

                                                        </fieldset>
                                                    </h:panelGroup>

                                                    <table width="100%" border="0" cellspacing="0" cellpadding="0" class="bordaCinza">
                                                        <tr>
                                                            <td align="right" valign="middle" style=" padding:12px;">
                                                                <h:panelGrid id="totalizadores" columns="3" columnClasses="colunaEsquerda, colunaDireita, colunaDireita"
                                                                             cellspacing="0" cellpadding="0">
                                                                    <h:panelGroup>
                                                                        <script>
                                                                            montarTips();
                                                                        </script>
                                                                        <h:outputText styleClass="texto-size-16-real texto-cor-cinza texto-font"
                                                                                      value="Valor Original "/>
                                                                        <rich:spacer width="15"/>
                                                                    </h:panelGroup>
                                                                    <h:panelGroup>
                                                                        <h:outputText  styleClass="texto-size-16-real texto-cor-cinza texto-font" value="#{MovPagamentoControle.empresaLogado.moeda}"/>
                                                                        <rich:spacer width="10"/>
                                                                    </h:panelGroup>
                                                                    <h:outputText  styleClass="texto-size-16-real texto-cor-verde texto-font" id="valorOriginal"
                                                                                   value="#{EdicaoPagamentoControle.totalOriginal}">
                                                                        <f:converter converterId="FormatadorNumerico" />
                                                                    </h:outputText>
                                                                    <h:panelGroup>
                                                                        <h:outputText styleClass="texto-size-16-real texto-cor-cinza texto-font"
                                                                                      value="Valor a ser trocado por outra forma de pagamento"/>
                                                                        <rich:spacer width="15"/>
                                                                    </h:panelGroup>
                                                                    <h:panelGroup>
                                                                        <h:outputText  styleClass="texto-size-16-real texto-cor-cinza texto-font" value="#{MovPagamentoControle.empresaLogado.moeda}"/>
                                                                        <rich:spacer width="10"/>
                                                                    </h:panelGroup>
                                                                    <h:outputText styleClass="texto-size-16-real texto-cor-verde texto-font" id="totalDevolver"
                                                                                  value="#{EdicaoPagamentoControle.totalDevolver}">
                                                                        <f:converter converterId="FormatadorNumerico" />
                                                                    </h:outputText>
                                                                </h:panelGrid>
                                                            </td>
                                                        </tr>
                                                    </table>

                                                </td>
                                            </tr>
                                        </table>
                                        <h:panelGroup layout="block" styleClass="container-botoes" style="text-align: right">
                                            <a4j:commandLink action="#{EdicaoPagamentoControle.voltar}"
                                                             rendered="#{not empty EdicaoPagamentoControle.origem}"
                                                             style="float: left;" styleClass="linkPadrao texto-cor-azul texto-size-16-real" >
                                                <i class="fa-icon-arrow-left"></i> Voltar
                                            </a4j:commandLink>
                                            <a4j:commandLink id="botaoConfirmarcao2"
                                                             oncomplete="#{EdicaoPagamentoControle.mensagemNotificar}"
                                                             action="#{EdicaoPagamentoControle.validarPermissaoUsuario}"
                                                             reRender="panelAutorizacaoFuncionalidade"
                                                             styleClass="botaoPrimario texto-size-16-real"
                                                             value="Confirmar"
                                                             title="Confirmar"/>
                                        </h:panelGroup>
                                        <h:panelGrid id="mensagem1" columns="2" width="100%">
                                            <c:if test="${not empty EdicaoPagamentoControle.mensagemDetalhada}">
                                                <h:outputText id="msgPagDet1" styleClass="mensagemDetalhada"
                                                              value="#{EdicaoPagamentoControle.mensagemDetalhada}" />
                                            </c:if>
                                        </h:panelGrid>
                                        <!-- fim item -->
                                    </h:panelGroup>
                                </h:panelGroup>
                            </h:panelGroup>
                        </h:panelGroup>
                        <c:if test="${MenuControle.apresentarMenu}">
                            <jsp:include page="includes/include_box_menulateral.jsp" flush="true"/>
                        </c:if>
                    </h:panelGroup>
                </h:panelGroup>
            </h:panelGroup>
            <c:if test="${MenuControle.apresentarMenu}">
             <jsp:include page="../../include_rodape_flat.jsp" flush="true" />
            </c:if>
        </h:panelGroup>
        </body>
        </html>


        <script>
            jQuery(document).ready(function(){
                montarTips();
            });
            function montarTips() {
                jQuery('.tooltipster').tooltipster({
                    theme: 'tooltipster-light',
                    position: 'bottom',
                    interactive: true,
                    animation: 'grow',
                    contentAsHTML: true
                });
            }
        </script>

    </h:form>

    <rich:modalPanel domElementAttachment="parent" id="panelProcessado"
                     showWhenRendered="#{EdicaoPagamentoControle.processado}"
                     autosized="true" shadowOpacity="true" width="650" height="200">
        <h:form id="formPanelProcessado">
            <h:panelGrid columns="1" width="100%" cellspacing="10" style="text-align: center;">

                <img src="${root}/images/warning.png"/>
                <rich:spacer height="5px"/>
                <h:panelGroup>
                    <h:outputText  value="O sistema identificou que já houve uma operação sobre esse recibo após última consulta. Volte e tente fazer a operação novamente, ou continue e veja se a operação foi concluída!"/>
                </h:panelGroup>
                <rich:spacer height="10px"/>
                <h:panelGroup>
                    <h:panelGrid  columns="2"  width="100%" style="text-align: center;">

                        <a4j:commandLink id="botaoVoltarProcesssado" action="#{EdicaoPagamentoControle.voltar}"
                                         styleClass="botaoPrimarioMedio texto-font texto-size-16 noBorderRight">
                            <i class="fa-icon-hand-left texto-size-16 texto-cor-branco"></i> Voltar
                        </a4j:commandLink>

                        <a4j:commandLink id="botaoSeguirProcesssado" action="#{EdicaoPagamentoControle.seguir}"
                                         styleClass="botaoPrimarioMedio texto-font texto-size-16 noBorderRight">
                            Seguir <i class="fa-icon-hand-right texto-size-16 texto-cor-branco"></i>
                        </a4j:commandLink>

                    </h:panelGrid>
                </h:panelGroup>
                <jsp:include page="../../includes/include_panelMensagem_goBackBlock.jsp"/>
            </h:panelGrid>
        </h:form>
    </rich:modalPanel>
    <jsp:include page="../../includes/autorizacao/include_autorizacao_funcionalidade.jsp" />
</f:view>

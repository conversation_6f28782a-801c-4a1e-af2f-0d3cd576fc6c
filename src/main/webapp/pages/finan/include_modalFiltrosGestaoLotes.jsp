<%-- 
    Document   : include_modalFiltrosGestaoLotes
    Created on : 13/02/2012, 17:34:11
    Author     : carla
--%>
<%@include file="includes/include_imports.jsp" %>
<script type="text/javascript" src="../../script/ce_script.js"></script>

<rich:modalPanel id="modalPanelFiltrosGestaoLotes" autosized="false" trimOverlayedElements="false"
                 styleClass="novaModal" style="overflow: visible"
                 shadowOpacity="true" width="880" height="450">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="Filtros Consulta de Gest�o de Lotes"/>
        </h:panelGroup>
    </f:facet>
    <f:facet name="controls">
        <h:panelGroup>
            <h:outputText
                    styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign"
                    id="hidelink1"/>
            <rich:componentControl for="modalPanelFiltrosGestaoLotes"
                                   attachTo="hidelink1" operation="hide" event="onclick" />
        </h:panelGroup>
    </f:facet>
    <a4j:form id="formFiltrosGestaoLotes" ajaxSubmit="true">
        <h:panelGroup styleClass="painelFiltros " layout="block" style="width: 100%;">
            <div style="width: 100%; padding: 0 15px;">
                <h:panelGroup layout="block" styleClass="block" rendered="#{GestaoLotesControle.mostrarCampoEmpresa}">
                    <h:outputText  styleClass="tituloCampos upper flex" value="#{msg_aplic.prt_Finan_Lancamentos_empresa}"/>
                    <div class="block cb-container">
                        <h:selectOneMenu id="empresa" styleClass="form" onblur="blurinput(this);"
                                     onfocus="focusinput(this);"
                                     value="#{GestaoLotesControle.empresa.codigo}" >
                            <f:selectItems value="#{GestaoLotesControle.listaSelectItemEmpresa}" />
                            <a4j:support action="#{GestaoLotesControle.obterEmpresaEscolhida}" event="onchange"/>
                        </h:selectOneMenu>
                    </div>
                </h:panelGroup>
                <h:panelGroup layout="block" styleClass="block">
                    <h:outputText styleClass="tituloCampos upper flex" value="#{msg_aplic.prt_Finan_GestaoLotes_codigo}"/>
                    <h:inputText id="codigo" size="10" maxlength="10" onblur="blurinput(this);"
                                 styleClass="form" value="#{GestaoLotesControle.codigo}" />
                </h:panelGroup>
                <h:panelGroup layout="block" styleClass="block">
                    <h:outputText styleClass="tituloCampos upper flex" value="#{msg_aplic.prt_Finan_GestaoLotes_descricao}"/>
                    <h:inputText id="descricao" size="40" maxlength="40"
                                 styleClass="form" value="#{GestaoLotesControle.descricao}" />
                </h:panelGroup>

                <h:outputText styleClass="tituloCampos upper flex"
                              value="#{msg_aplic.prt_Finan_GestaoRecebiveis_periodoLancamento}"/>
                <h:panelGroup styleClass="flex" layout="block">
                    <h:panelGroup styleClass="dateTimeCustom"  style="font-size: 11px !important;">
                        <rich:calendar id="dataInicioL"
                                       value="#{GestaoLotesControle.dataInicialLancamento}"
                                       inputSize="10"
                                       inputClass="form"
                                       buttonIcon="/imagens_flat/calendar-button.svg"
                                       oninputblur="blurinput(this);"
                                       oninputfocus="focusinput(this);"
                                       oninputchange="return validar_Data(this.id);"
                                       datePattern="dd/MM/yyyy"
                                       enableManualInput="true"
                                       zindex="2"
                                       showWeeksBar="false"/>
                    </h:panelGroup>

                    <h:outputText styleClass="tituloCampos"
                                  style="margin: 7px 9px 0px 10px;  font-size: 11px !important;"
                                  value="#{msg_aplic.prt_ate}"/>

                    <h:panelGroup styleClass="dateTimeCustom"  style="font-size: 11px;">
                        <rich:calendar id="dataTerminoL"
                                       value="#{GestaoLotesControle.dataFinalLancamento}"
                                       inputSize="10"
                                       buttonIcon="/imagens_flat/calendar-button.svg"
                                       inputClass="form"
                                       oninputblur="blurinput(this);"
                                       oninputfocus="focusinput(this);"
                                       oninputkeypress="return mascara2(this.form, this.id, '99/99/9999', event);"
                                       oninputchange="return validar_Data(this.id);"
                                       datePattern="dd/MM/yyyy"
                                       enableManualInput="true"
                                       zindex="2"
                                       showWeeksBar="false"/>
                    </h:panelGroup>


                    <div class="cb-container" style=" font-size: 11px !important;margin-left: 10px;">
                        <h:selectOneMenu id="periodoLancamentos"
                                         onfocus="focusinput(this);"
                                         value="#{GestaoLotesControle.periodoLancamento}">
                            <f:selectItems value="#{GestaoLotesControle.listaPeriodos}"/>
                            <a4j:support action="#{GestaoLotesControle.alterarPeriodoLancamento}" event="onchange"
                                         reRender="dataTerminoL,dataInicioL,containerFuncMask"/>
                        </h:selectOneMenu>
                    </div>
                    <a4j:commandLink id="limparPeriodoLancamento"
                                     style="position:relative; top:7px; left:5px; font-size: 11px;"
                                     onclick="document.getElementById('formFiltrosGestaoLotes:dataInicioLInputDate').value='';
                                         document.getElementById('formFiltrosGestaoLotes:dataTerminoLInputDate').value='';"
                                     title="Limpar per�odo de faturamento." status="false">
                        <i class="fa-icon-eraser"></i>
                    </a4j:commandLink>

                </h:panelGroup>

                <h:outputText styleClass="tituloCampos upper flex"
                              value="#{msg_aplic.prt_Finan_GestaoLotes_periodoDeposito}"/>
                <h:panelGroup styleClass="flex" layout="block">
                    <h:panelGroup styleClass="dateTimeCustom"  style="font-size: 11px !important;">
                        <rich:calendar id="dataInicioD"
                                       value="#{GestaoLotesControle.dataInicialDeposito}"
                                       inputSize="10"
                                       inputClass="form"
                                       buttonIcon="/imagens_flat/calendar-button.svg"
                                       oninputblur="blurinput(this);"
                                       oninputfocus="focusinput(this);"
                                       oninputchange="return validar_Data(this.id);"
                                       datePattern="dd/MM/yyyy"
                                       enableManualInput="true"
                                       zindex="2"
                                       showWeeksBar="false"/>
                    </h:panelGroup>

                    <h:outputText styleClass="tituloCampos"
                                  style="margin: 7px 9px 0px 10px;  font-size: 11px !important;"
                                  value="#{msg_aplic.prt_ate}"/>

                    <h:panelGroup styleClass="dateTimeCustom"  style="font-size: 11px;">
                        <rich:calendar id="dataTerminoD"
                                       value="#{GestaoLotesControle.dataFinalDeposito}"
                                       inputSize="10"
                                       buttonIcon="/imagens_flat/calendar-button.svg"
                                       inputClass="form"
                                       oninputblur="blurinput(this);"
                                       oninputfocus="focusinput(this);"
                                       oninputkeypress="return mascara2(this.form, this.id, '99/99/9999', event);"
                                       oninputchange="return validar_Data(this.id);"
                                       datePattern="dd/MM/yyyy"
                                       enableManualInput="true"
                                       zindex="2"
                                       showWeeksBar="false"/>
                    </h:panelGroup>


                    <div class="cb-container" style="margin-left: 10px; font-size: 11px !important;">
                        <h:selectOneMenu id="periodoDeposito"
                                         onfocus="focusinput(this);"
                                         value="#{GestaoLotesControle.periodoDeposito}">
                            <f:selectItems value="#{GestaoLotesControle.listaPeriodos}"/>
                            <a4j:support action="#{GestaoLotesControle.alterarPeriodoDeposito}" event="onchange" reRender="dataTerminoD,dataInicioD,containerFuncMask"/>
                        </h:selectOneMenu>
                    </div>
                    <a4j:commandLink id="limparPeriodoDeposito"
                                     style="position:relative; top:7px; left:5px; font-size: 11px;"
                                     onclick="document.getElementById('formFiltrosGestaoLotes:dataInicioDInputDate').value='';
                                         document.getElementById('formFiltrosGestaoLotes:dataTerminoDInputDate').value='';"
                                     title="Limpar per�odo de faturamento." status="false">
                        <i class="fa-icon-eraser"></i>
                    </a4j:commandLink>

                </h:panelGroup>
            </div>
        </h:panelGroup>

        <div class="rodapeBotoes" style="padding-bottom: 20px;">
            <a4j:commandLink
                    oncomplete="Richfaces.hideModalPanel('modalPanelFiltrosGestaoLotes');"
                    reRender="form:filtros,form:listaLotes, form:panelMensagem, form:totais, form:totaisregistro"
                    action="#{GestaoLotesControle.consultarLotes}"
                    styleClass="pure-button pure-button-primary" style="margin-right:10px; margin-left: 15px;"
                    title="#{msg.msg_consultar_dados}">
                <i style="font-size: 14px" class="fa-icon-search"></i> &nbsp <h:outputText style="font-size: 14px"
                                                                                           value="Pesquisar"/>
            </a4j:commandLink>

            <a4j:commandLink reRender="formFiltrosGestaoLotes, form"
                             action="#{GestaoLotesControle.limparFiltros}"
                             styleClass="pure-button"
                             title="Limpar filtros">
                <h:outputText style="font-size: 14px" value="Limpar filtros"/>
            </a4j:commandLink>

        </div>

        <h:panelGroup layout="block" id="containerFuncMask">
            <script>
                carregarMaskInput();
            </script>
        </h:panelGroup>
    </a4j:form>
</rich:modalPanel>



<%--
    Created on : 18/07/2023
    Author     : <PERSON>
--%>

<%@page contentType="text/html" %>
<%@page pageEncoding="ISO-8859-1" %>
<%@include file="includes/imports.jsp" %>
<h:panelGroup layout="block" styleClass="margin-box" rendered="#{MovContaControle.visaoConciliacao}" id="idconciliacao">
    <style>
        .table-striped {
            width: 100%;
            font-size: 14px;
            color: #777;
            margin-bottom: 20px;
        }

        .footer-background {
            background-color: #e5e5e5;
            margin-top: -4px;
            display: flex;
            font-family: 'Nunito Sans', sans-serif;
        }

        .botaoConta:hover {
            /*background: white;*/
            background-color: #bdd6ff;
            color: #094771 !important;
            text-decoration: none;
        }

        .table-striped th {
            border: 1px solid #e5e5e5;
            border-right: none;
            border-left: none;
            padding: 5px 0;
            text-transform: uppercase;
        }

        .table-striped td {
            text-align: center;
            padding: 2px;
        }

        .table-striped tr:nth-child(even) {
            background: #e5e5e5
        }

        .table-striped tr:hover {
            background: #d3d3d3;
        }

        .table-striped thead tr:hover {
            background: #fff;
        }

        .table-striped thead tr th {
            padding: 10px 5px;
        }

        .table-striped tbody tr td a i {
            color: #777;
        }

        .table-striped-separator {
            width: 5px;
            background: #fff;
        }

        .table-striped-separator + .table-striped-separator {
            border-left: 1px solid black;
        }

        .table-striped-options {
            display: flex;
            justify-content: center;
        }

        .table-striped-options a {
            margin: 2px;
        }

        .table-info tr td {
            padding: 10px;
            text-transform: uppercase;
        }

        .conciliacao-container {
            display: flex;
            flex-direction: column;
            box-sizing: border-box;
        }

        .conciliacao-info {
            display: flex;
            justify-content: space-between;
        }

        .conciliacao-info div {
            flex: 1;
            text-align: center;
        }

        .conciliacao-info div > span {
            display: block;
            color: #666;
            font-size: 20px;
            margin-bottom: 20px;
        }

        .conciliacao-status {
            width: 600px;
            margin-bottom: 20px;
        }

        .conciliacao-info-zw,
        .conciliacao-info-extrato {
            padding: 0 10px;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
        }

        .alert {
            color: red;
        }

        .linkalunoextrato span {
            text-transform: capitalize;
            font-size: 14.5px !important;
            font-weight: normal;
        }

        .linhacancelamento td, .linhacancelamento td .linkalunoextrato span {
            color: red !important;
        }

    </style>

    <div class="conciliacao-container">

        <h:panelGroup layout="block" style="padding-bottom: 0;">
            <a onclick="Richfaces.showModalPanel('modalPanelFiltrosConciliacaoContas');"
               id="refazerConsulta"
               style="cursor: pointer;"
               class="pure-button pure-button-primary step5 tudo">
                <i class="fa-icon-refresh"></i> &nbsp Refazer consulta
            </a>
            <rich:panel id="filtrosInfo"
                        style="border-color: #eee; background-color: #eee; margin-top: 15px;"
                        styleClass="tudo">
                <h:outputText styleClass="tituloCamposNegrito" value="Filtros:"/>
                <h:outputText styleClass="tituloDemonstrativo"
                              value="#{MovContaControle.filtrosConc}"/>
            </rich:panel>
        </h:panelGroup>


        <table class="conciliacao-status" style="margin-top: 10px;">
            <tr>
                    <%--LEGENDA CONCILIADO--%>
                <td style="width: 0px">
                    <h:panelGroup layout="block"
                                  style="width: 5px;height:30px;background-color: #077113;"/>
                </td>
                <td style="width: 15%;">
                    <h:outputText value="Conciliado"
                                  styleClass="tooltipster"
                                  title="#{MovContaControle.titleItemConciliadoExplicacao}"
                                  style="font-size: 14px; margin-left: 2px;"/>
                </td>

                    <%--LEGENDA NÃO CONCILIADO--%>
                <td style="width: 0px">
                    <h:panelGroup layout="block"
                                  style="width: 5px;height:30px;background-color: #000000;"/>
                </td>
                <td style="width: 19%;">
                    <h:outputText value="Não Conciliado"
                                  styleClass="tooltipster"
                                  title="#{MovContaControle.titleItemNaoConciliadoExplicacao}"
                                  style="font-size: 14px; margin-left: 2px;"/>
                </td>

                    <%--LEGENDA PENDENTE--%>
                <td style="width: 0px">
                    <h:panelGroup layout="block"
                                  style="width: 5px;height:30px;background-color: #FFFF00;"/>
                </td>
                <td>
                    <h:outputText value="Pendente"
                                  styleClass="tooltipster"
                                  title="#{MovContaControle.titleItemPendenteExplicacao}"
                                  style="font-size: 14px; margin-left: 2px;"/>
                </td>
            </tr>
        </table>


            <%--BOTÕES FILTRO IMAGEM DO BANCO--%>
        <h:panelGroup id="panelFiltrosContas">
            <table id="tableFiltroContasBancarias">
                <tr style="display: block; border-radius: 10px; border: 1px solid #ede4e4;">
                    <a4j:repeat value="#{MovContaControle.bancosExibirFiltroConciliacao}" var="item">
                        <td style="padding-right: 15px;" class="botaoConta">
                            <a4j:commandLink actionListener="#{MovContaControle.filtrarContaPluggy}"
                                             style="display: flex; text-decoration: none; #{!item.selecionadoParaFiltrar ? 'opacity: 0.3;' : ''}"
                                             styleClass="tooltipster"
                                             oncomplete="#{MovContaControle.mensagemNotificar}"
                                             title="#{item.titleImagemConnector}"
                                             reRender="form:panelGroupAtualizarLancarPagamento, panelFiltrosContasModal">
                                <h:panelGroup style="display: flex">
                                    <%--Não é sandbox--%>
                                    <h:graphicImage rendered="#{!item.pluggySandbox}" style="width: 60px; height: 60px"
                                                    url="#{item.imageUrl}">
                                    </h:graphicImage>
                                    <h:outputText rendered="#{!item.pluggySandbox}"
                                                  styleClass="tooltipster"
                                                  style="align-self: center;"
                                                  value="#{item.nameConnector}"/>
                                    <%--É sandbox--%>
                                    <h:graphicImage rendered="#{item.pluggySandbox}" style="width: 40px; height: 40px"
                                                    url="#{item.imageUrlFacilitePay}">
                                    </h:graphicImage>
                                    <h:outputText rendered="#{item.pluggySandbox}"
                                                  styleClass="tooltipster"
                                                  style="align-self: center; margin-left: 5px;"
                                                  value="#{item.nameConnector}"/>
                                </h:panelGroup>
                                <f:attribute name="item" value="#{item}"/>
                            </a4j:commandLink>
                        </td>
                    </a4j:repeat>
                </tr>
            </table>
        </h:panelGroup>


        <div class="conciliacao-info" style="margin-top: 16px">
            <div>
                <h:outputText styleClass="tituloCampos" style="margin-right: 47%;" value="Extrato Bancário"/>
            </div>

            <div>
                <h:outputText styleClass="tituloCampos" style="margin-right: 47%;" value="Sistema Pacto"/>
            </div>
        </div>

        <h:panelGroup id="panelDetalhesConciliacao">
            <div id="divDetalhesConciliacao" style="overflow-x: auto">
                <table class="table-striped" id="tableConc">
                    <thead>
                        <%--TITULOS DAS COLUNAS--%>
                    <tr>
                            <%------------------------DADOS PLUGGY------------------------%>

                            <%--COR DA LINHA--%>
                        <th></th>

                            <%--CHECKBOX--%>
                        <th>
                            <h:selectBooleanCheckbox
                                    id="checkMarcarTodasTransaction"
                                    styleClass="tooltipster"
                                    title="Marcar/Desmarcar todos os pendentes"
                                    value="#{MovContaControle.marcandoTodasTransactions}"
                                    disabled="#{MovContaControle.checkBoxDesabilitado}">
                                <a4j:support event="onclick"
                                             action="#{MovContaControle.marcarDesmarcarTodosTransactionsPendentes}"
                                             reRender="checkMarcarTransaction, form:footerGeral"/>
                            </h:selectBooleanCheckbox>
                        </th>

                            <%--PAGAMENTO--%>
                        <th style="text-align: left; padding: 10px 0px">${MovContaControle.labelPagamentoRecebimentoConc}</th>

                            <%--DESCRICAO--%>
                        <th style="text-align: left;">Descrição</th>

                            <%--CATEGORIA--%>
                        <th><h:outputText value="Categoria"/></th>

                            <%--VALOR--%>
                        <th style="text-align: right; padding-right: 5px;">Valor</th>

                            <%--separadores--%>
                        <th class="table-striped-separator"></th>
                        <th class="table-striped-separator"></th>
                        <th></th>

                            <%------------------------DADOS SISTEMA PACTO------------------------%>

                            <%--DESCRICAO--%>
                        <th><h:outputText
                                value="#{MovContaControle.labelContaPagarReceberConc}"/></th>

                            <%--VENCIMENTO--%>
                        <th><h:outputText value="Vencimento"/></th>

                            <%--VALOR--%>
                        <th><h:outputText value="Valor"/></th>

                            <%--AÇÃO--%>
                        <th style="width: 8%;"><h:outputText value="Ação"/></th>

                    </tr>
                    </thead>
                    <tbody>


                    <a4j:repeat value="#{MovContaControle.transactions}" var="transaction">

                        <%--VALORES DAS COLUNAS--%>

                        <%------------------------DADOS PLUGGY------------------------%>
                        <tr>

                                <%--COR DA LINHA--%>
                            <td>
                                <h:panelGroup id="corLinha"
                                              layout="block"
                                              style="width: 5px;height:35px;background-color: #{transaction.corLinha};">
                                </h:panelGroup>
                            </td>

                                <%--CHECKBOX--%>
                            <td>
                                <h:selectBooleanCheckbox styleClass="tooltipster ccZW"
                                                         id="checkMarcarTransaction"
                                                         value="#{transaction.checkboxChecked}"
                                                         rendered="#{transaction.gravarPendente}">
                                    <a4j:support event="onclick"
                                                 actionListener="#{MovContaControle.marcarDesmarcarTransactionPendente}"
                                                 reRender="checkMarcarTransaction, form:footerGeral, form:checkMarcarTodasTransaction">
                                        <f:attribute name="transaction" value="#{transaction}"/>
                                    </a4j:support>
                                </h:selectBooleanCheckbox>
                            </td>

                                <%--DATA--%>
                            <td style="text-align: start">
                                <h:outputText value="#{transaction.dataApresentar}"
                                              styleClass="tooltipster"
                                              title="Essa é a data em que o pagamento foi realizado no extrato da sua conta bancária."/>
                            </td>

                                <%--DESCRIÇÃO--%>
                            <td style="text-align: left; max-width: 10%; text-overflow: ellipsis" width="150px">
                                <h:outputText
                                        value="#{transaction.description}"
                                        title="#{transaction.titleDescItemPluggyApresentar}"
                                        styleClass="tooltipster"/>
                            </td>

                                <%--CATEGORIA--%>
                            <td>
                                <h:outputText value="#{transaction.categoryTranslated}"/>
                            </td>

                                <%--VALOR--%>
                            <td style="text-align: right; padding-right: 5px; width: 8%; color: ${MovContaControle.corValorContaPagarReceberConc}; font-weight: bold;">
                                <h:outputText value="#{transaction.valorApresentarSemSinal}"/>
                            </td>

                                <%--separadores--%>
                            <td class="table-striped-separator"></td>
                            <td class="table-striped-separator"></td>

                                <%------------------------DADOS SISTEMA PACTO------------------------%>

                                <%--ITENS CONCILIADOS--%>
                            <th>
                                <h:panelGroup id="itemsConciliados" rendered="#{transaction.conciliado}">

                                    <%--DESCRICAO--%>
                            <td>
                                    <h:outputText rendered="#{transaction.multiplasContasPreenchidas}">Múltiplas Contas Informadas</h:outputText>
                                    <h:outputText
                                            rendered="#{transaction.movContaVO != null && !transaction.jaRecebidoZW && !transaction.multiplasContasPreenchidas}"
                                            value="#{transaction.movContaVO.descricao}"
                                            title="#{transaction.titleContaConciliadaApresentar}"
                                            styleClass="tooltipster"/>
                                    <h:outputText
                                             rendered="#{transaction.jaRecebidoZW && !transaction.multiplasContasPreenchidas}"
                                             value="#{transaction.movContaTransactionPluggyVO.pluggyJaRecebidoZw.descricao}"
                                             title="#{transaction.titleContaConciliadaApresentar}"
                                             styleClass="tooltipster"/>
                            </td>

                                <%--VENCIMENTO--%>
                            <td>
                                <h:outputText
                                        rendered="#{!transaction.jaRecebidoZW}"
                                        value="#{transaction.movContaVO.dataVencimento_Apresentar}"/>
                                <h:outputText
                                        rendered="#{transaction.jaRecebidoZW}"
                                        value="#{transaction.movContaTransactionPluggyVO.pluggyJaRecebidoZw.dataVencimento_Apresentar}"/>
                            </td>

                                <%--VALOR--%>
                            <td style="color: ${MovContaControle.corValorContaPagarReceberConc};font-weight: bold;">
                                <h:outputText
                                        rendered="#{!transaction.jaRecebidoZW && !transaction.multiplasContasPreenchidas}"
                                        value="#{transaction.movContaVO.valorApresentar}"/>
                                <h:outputText
                                        rendered="#{transaction.jaRecebidoZW && !transaction.multiplasContasPreenchidas}"
                                        value="#{transaction.movContaTransactionPluggyVO.pluggyJaRecebidoZw.valorApresentar}"/>
                                <h:outputText rendered="#{transaction.multiplasContasPreenchidas}"
                                    value="#{transaction.valorMultiplasContasApresentar}"/>
                            </td>
                            </h:panelGroup>


                                <%--ITENS NÃO CONCILIADOS--%>
                            <h:panelGroup id="itemsNaoConciliados"
                                          rendered="#{transaction.naoConciliado or transaction.gravarPendente}">
                                <td>
                                        <%--DESCRICAO--%>
                                    <h:outputText rendered="#{transaction.multiplasContasPreenchidas}">Múltiplas Contas Informadas</h:outputText>
                                    <h:inputText id="contaPagar" size="50" maxlength="80"
                                                 style="border-radius: 6px;"
                                                 rendered="#{!transaction.multiplasContasPreenchidas}"
                                                 onkeypress="if (event.keyCode == 13) { document.getElementById('contaPagar:descricao').focus(); return false;};"
                                                 onfocus="focusinput(this);" styleClass="form"
                                                 value="#{transaction.movContaVO.descricao}">
                                    </h:inputText>

                                    <rich:suggestionbox height="200" width="540"
                                                        for="contaPagar"
                                                        fetchValue="#{result}"
                                                        suggestionAction="#{MovContaControle.executarAutocompleteConsultaContasAPagarOuReceber}"
                                                        minChars="1" rowClasses="20"
                                                        status="true"
                                                        nothingLabel="Nenhuma conta a pagar encontrada!"
                                                        var="result" id="suggestionContaPagar">
                                        <a4j:support event="onselect" ignoreDupResponses="true"
                                                     reRender="contaPagar, panelDetalhesConciliacao, corLinha"
                                                     actionListener="#{MovContaControle.selecionarMovContaSuggestionBox}"
                                                     focus="descricao">
                                            <f:attribute name="transaction" value="#{transaction}"/>
                                        </a4j:support>

                                        <h:column>
                                            <f:facet name="header">
                                                <h:outputText styleClass="textverysmall" value="Descrição"/>
                                            </f:facet>
                                            <h:outputText value="#{result.descricao}"/>
                                        </h:column>
                                        <h:column>
                                            <f:facet name="header">
                                                <h:outputText styleClass="textverysmall" value="Vencimento"/>
                                            </f:facet>
                                            <h:outputText value="#{result.dataVencimento_Apresentar}"/>
                                        </h:column>
                                        <h:column>
                                            <f:facet name="header">
                                                <h:outputText styleClass="textverysmall" value="Valor"/>
                                            </f:facet>
                                            <h:outputText value="R$ #{result.valorApresentar}"/>
                                        </h:column>
                                    </rich:suggestionbox>
                                    <rich:spacer width="15px"/>
                                </td>

                                <%--VENCIMENTO--%>
                                <td>
                                    <h:outputText value="#{transaction.movContaVO.dataVencimento_Apresentar}"
                                                  rendered="#{!transaction.naoConciliado}"/>
                                </td>

                                <%--VALOR--%>
                                <td style="color: ${MovContaControle.corValorContaPagarReceberConc};font-weight: bold;">
                                    <h:outputText rendered="#{!transaction.multiplasContasPreenchidas}"
                                            value="#{transaction.movContaVO.valorApresentar}"/>
                                    <h:outputText rendered="#{transaction.multiplasContasPreenchidas}"
                                                  value="#{transaction.valorMultiplasContasApresentar}"/>
                                </td>
                            </h:panelGroup>

                            </th>


                            <td>
                                <div class="table-striped-options">
                                    <h:panelGroup rendered="#{transaction.gravarPendente}">

                                        <%--ITEM PENDENTE--%>
                                        <a4j:commandLink id="quitarItem"
                                                         style="text-decoration: none !important;"
                                                         value="Quitar"
                                                         reRender="modalPanelLancarPagamentoConciliacao"
                                                         rendered="#{!transaction.multiplasContasPreenchidas}"
                                                         actionListener="#{MovContaControle.prepararDadosEAbrirModalPagamento}"
                                                         oncomplete="#{MovContaControle.mensagemNotificar}#{MovContaControle.msgAlert}">
                                            <f:attribute name="transaction" value="#{transaction}"/>
                                        </a4j:commandLink>
                                        <h:outputText rendered="#{!transaction.multiplasContasPreenchidas}">|</h:outputText>
                                        <a4j:commandLink id="desfazerItemPendente"
                                                         style="text-decoration: none !important;"
                                                         value="Desfazer"
                                                         rendered="#{!transaction.multiplasContasPreenchidas}"
                                                         reRender="form:panelDetalhesConciliacao"
                                                         actionListener="#{MovContaControle.desfazerItemPendente}"
                                                         oncomplete="#{MovContaControle.mensagemNotificar}">
                                            <f:attribute name="transaction" value="#{transaction}"/>
                                        </a4j:commandLink>
                                        <a4j:commandLink id="btnDesfazerMultiplasContasSelecionadas"
                                                         style="text-decoration: none !important;"
                                                         value="Desfazer Múltiplos"
                                                         reRender="form:panelDetalhesConciliacao"
                                                         rendered="#{transaction.multiplasContasPreenchidas}"
                                                         actionListener="#{MovContaControle.desfazerMultiplasContasSelecionadas}"
                                                         oncomplete="#{MovContaControle.mensagemNotificar}">
                                            <f:attribute name="transaction" value="#{transaction}"/>
                                        </a4j:commandLink>

                                    </h:panelGroup>

                                        <%--ITEM CONCILIADO--%>
                                    <a4j:commandLink id="desfazerItemConciliado"
                                                     rendered="#{transaction.conciliado && !transaction.multiplasContasPreenchidas}"
                                                     style="text-decoration: none !important;"
                                                     value="Desfazer"
                                                     reRender="form:panelDetalhesConciliacao, mdlMensagemGenerica"
                                                     actionListener="#{MovContaControle.prepararExclusaoDePagamento}"
                                                     oncomplete="#{MovContaControle.mensagemNotificar}#{MovContaControle.msgAlert}">
                                        <f:attribute name="transaction" value="#{transaction}"/>
                                    </a4j:commandLink>
                                    <a4j:commandLink id="desfazerMultiplosItensConciliados"
                                                     rendered="#{transaction.conciliado && transaction.multiplasContasPreenchidas}"
                                                     style="text-decoration: none !important;"
                                                     value="Desfazer Múltiplos"
                                                     reRender="form:panelDetalhesConciliacao, mdlMensagemGenerica"
                                                     actionListener="#{MovContaControle.prepararExclusaoDePagamentoParaMultiplasContas}"
                                                     oncomplete="#{MovContaControle.mensagemNotificar}#{MovContaControle.msgAlert}">
                                        <f:attribute name="transaction" value="#{transaction}"/>
                                    </a4j:commandLink>

                                    <h:outputText rendered="#{transaction.gravarPendente or transaction.conciliado}"> |</h:outputText>
                                    <a4j:commandLink id="visualizarMultiplosItensConc"
                                                     rendered="#{(transaction.gravarPendente or transaction.conciliado) and transaction.multiplasContasPreenchidas}"
                                                     style="text-decoration: none !important;"
                                                     reRender="modalPanelAdicionarMultiplasContasConciliacao"
                                                     actionListener="#{MovContaControle.prepararDadosEAbrirModalAdicionarMultiplasContas}"
                                                     oncomplete="#{MovContaControle.mensagemNotificar}#{MovContaControle.msgAlert}">
                                        <f:attribute name="transaction" value="#{transaction}"/>
                                        <i class="fa-icon-eye-open tooltipster"
                                           style="font-size: 14px; color: #1E60FA;"
                                           title="Visualizar/Editar Múltiplas Contas Informadas"></i>
                                    </a4j:commandLink>

                                        <%--ITEM NÃO CONCILIADO--%>
                                    <a4j:commandLink id="criarItemConc"
                                                     rendered="#{transaction.naoConciliado}"
                                                     style="text-decoration: none !important;"
                                                     value="Criar"
                                                     reRender="modalPanelCriarNovaContaConciliacao"
                                                     actionListener="#{MovContaControle.prepararDadosEAbrirModalCriarNovaConta}"
                                                     oncomplete="#{MovContaControle.mensagemNotificar}#{MovContaControle.msgAlert}">
                                        <f:attribute name="transaction" value="#{transaction}"/>
                                    </a4j:commandLink>
                                    <h:outputText rendered="#{transaction.naoConciliado && MovContaControle.contasReceber}">  |</h:outputText>
                                    <a4j:commandLink  target="_blank" title="Conciliar já recebido no ADM"
                                                      rendered="#{transaction.naoConciliado && MovContaControle.contasReceber}"
                                                      styleClass="tooltipster"
                                                      reRender="form:panelDetalhesConciliacao, mdlMensagemGenerica"
                                                      actionListener="#{MovContaControle.prepararDadosEAbrirModalConciliarJaRecebidoZW}"
                                                      oncomplete="#{MovContaControle.mensagemNotificar}#{MovContaControle.msgAlert}">
                                        <h:graphicImage url="/imagens/dolar.png" style="border:none; width: 15px; height: 15px; margin-top: -1px" />
                                        <f:attribute name="transaction" value="#{transaction}"/>
                                    </a4j:commandLink>
                                    <h:outputText rendered="#{transaction.naoConciliado && MovContaControle.contasPagar}"> |</h:outputText>
                                    <%--Modal Adicionar Múltiplas Contas --%>
                                    <a4j:commandLink id="adicionarMultiplosItensConc"
                                                     rendered="#{transaction.naoConciliado && MovContaControle.contasPagar}"
                                                     style="text-decoration: none !important;"
                                                     reRender="modalPanelAdicionarMultiplasContasConciliacao"
                                                     actionListener="#{MovContaControle.prepararDadosEAbrirModalAdicionarMultiplasContas}"
                                                     oncomplete="#{MovContaControle.mensagemNotificar}#{MovContaControle.msgAlert}">
                                        <f:attribute name="transaction" value="#{transaction}"/>
                                        <i class="fa-icon-plus-sign tooltipster"
                                           style="font-size: 14px; color: #1E60FA;"
                                           title="Adicionar Múltiplas Contas"></i>
                                    </a4j:commandLink>
                                </div>
                            </td>
                        </tr>
                    </a4j:repeat>

                    </tbody>
                </table>
            </div>
        </h:panelGroup>
    </div>

    <%--Footer--%>
    <h:panelGroup id="footerGeral"
                  styleClass="footer-background"
                  style="display: flex;">
        <%--Totalizadores--%>
        <h:panelGrid rendered="#{not empty MovContaControle.listaTransactionsConciliarEmMassa}"
                     style="display: block"
                     columns="2" width="100%">
            <%--        <h:outputText styleClass="" value="Total de registros: "/>--%>
            <%--        <h:outputText styleClass="" value="#{MovContaControle.qtdTotalTransactions}"/>--%>
            <%--        <h:outputText styleClass="" value="Valor total: "/>--%>
            <%--        <h:outputText styleClass="" value="#{MovContaControle.valorTotalTransactionsApresentar}"/>--%>
            <h:outputText style="color: #777;" value="Total Itens:  "/>
            <h:outputText style="color: #777;" value="#{MovContaControle.qtdTotalSelecionadoParaConciliacaoEmMassa}"/>
            <h:outputText style="color: #777;" value="Valor Total:  "/>
            <h:outputText style="color: #777;" value="#{MovContaControle.valorTotalSelecionadoParaConciliacaoEmMassa}"/>
        </h:panelGrid>
        <%--BTN QUITAR TODOS--%>
        <h:panelGroup
                rendered="#{not empty MovContaControle.listaTransactionsConciliarEmMassa}"
                id="panelBtnQuitarTodosSelecionados" layout="block"
                style="width: 100%; text-align: right; margin-top: 14px;">
            <a4j:commandLink id="btnQuitarTodosSelecionados"
                             styleClass="botoes nvoBt btSec inline"
                             style="background-color: #00c350; color: white; display: flex; height: 15px; border-radius: 2px;"
                             action="#{MovContaControle.prepararDadosEAbrirModalQuitacaoEmMassa}"
                             oncomplete="#{MovContaControle.mensagemNotificar}#{MovContaControle.msgAlert}"
                             reRender="modalPanelLancarPagamentoConciliacaoEmMassa">
                <i class="fa-icon-usd"></i>
                <h:outputText style="margin-top: -12px;" value=" Quitar Todos Selecionados"/>
            </a4j:commandLink>
        </h:panelGroup>
    </h:panelGroup>
    <script>
        carregarTooltipster();
    </script>
</h:panelGroup>

<rich:modalPanel id="modalDecidir" styleClass="novaModal noMargin" shadowOpacity="true"
                 width="500" height="200">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="Conciliação"/>
        </h:panelGroup>
    </f:facet>
    <f:facet name="controls">
        <h:panelGroup>
            <h:outputText
                    styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                    id="hidelink7"/>
            <rich:componentControl for="modalDecidir" attachTo="hidelink7" operation="hide"
                                   event="onclick"/>
        </h:panelGroup>
    </f:facet>
    <h:panelGroup layout="block" style="margin: 10 20 10 20;" id="painelNaoConciliados">


    </h:panelGroup>
</rich:modalPanel>


<rich:modalPanel id="modalPropagandaFacilite" styleClass="novaModal noMargin" shadowOpacity="true"
                 width="800" height="675">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="Fypay"/>
        </h:panelGroup>
    </f:facet>
    <f:facet name="controls">
        <h:panelGroup>
            <h:outputText
                    styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                    id="hidemodalPropagandaFacilite"/>
            <rich:componentControl for="modalPropagandaFacilite" attachTo="hidemodalPropagandaFacilite" operation="hide"
                                   event="onclick"/>
        </h:panelGroup>
    </f:facet>
    <h:panelGroup layout="block" style="margin: 10 20 10 20;">
        <a href="https://fypay.com.br" target="_blank" >
            <img border="none" class="img-responsive imagemApresentacao" src="../../images/FACILITEPAY_CONCILIACAO_CONTA.jpg"/>
        </a>
    </h:panelGroup>
</rich:modalPanel>

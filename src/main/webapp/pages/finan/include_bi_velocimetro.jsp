<%@page pageEncoding="ISO-8859-1"%>
<%@include file="includes/imports.jsp" %>

<script type="text/javascript">
    var chart3;
    var arrow;
    var axis;
    function carregarVelocimetro(){
        // create angular gauge
        chart3 = new AmCharts.AmAngularGauge();


        // create axis
        axis = new AmCharts.GaugeAxis();
        axis.startValue = 0;
        axis.axisThickness = 1;
        axis.endValue = ${BIFinanceiroControle.velocimetro.valorFinal};
        axis.valueInterval=${BIFinanceiroControle.intervalo};
        axis.minorTickInterval=${BIFinanceiroControle.intervaloBarras};

        // color bands
        var band1 = new AmCharts.GaugeBand();
        band1.startValue = 0;
        band1.endValue = ${BIFinanceiroControle.velocimetro.valorInicialAmarelo};
        band1.color = "${BIFinanceiroControle.velocimetro.cor1}";
        band1.innerRadius = "10%";

        var band2 = new AmCharts.GaugeBand();
        band2.startValue = ${BIFinanceiroControle.velocimetro.valorInicialAmarelo};
        band2.endValue = ${BIFinanceiroControle.velocimetro.valorFinalAmarelo};
        band2.color = "#F0E909";
        band2.innerRadius = "10%";

        var band3 = new AmCharts.GaugeBand();
        band3.startValue = ${BIFinanceiroControle.velocimetro.valorFinalAmarelo};
        band3.endValue = ${BIFinanceiroControle.velocimetro.valorFinal};
        band3.color = "${BIFinanceiroControle.velocimetro.cor2}";
        band3.innerRadius = "10%";

        axis.bands = [band1, band2, band3];

        // bottom text
        axis.bottomTextYOffset = 20;
        axis.setBottomText("${BIFinanceiroControle.labelVelocimetro}");
        chart3.addAxis(axis);

        // gauge arrow
        arrow = new AmCharts.GaugeArrow();
        chart3.addArrow(arrow);
        try{
            chart3.write("chartvelocimetro");
            arrow.setValue(${BIFinanceiroControle.velocimetro.valorPonteiro});
        }catch(err){

        }




    }
    function onStartVeloc(){
        document.getElementById('carregandoVelocimetro').style.display = 'block';
        document.getElementById('tableVelocimetro').style.display = 'none';
        document.getElementById('atualizarDadosGrafico').style.display = 'none';
        document.getElementById('tableVelocimetroAtualizar').style.display = 'none';
    }

    function onStopVeloc(){
        document.getElementById('carregandoVelocimetro').style.display = 'none';
        document.getElementById('tableVelocimetro').style.display = 'block';
        document.getElementById('atualizarDadosGrafico').style.display = 'block';
        document.getElementById('tableVelocimetroAtualizar').style.display = 'block';
    }


</script>


<table width="100%" id="tableVelocimetro">
    <tr style="width: 100%;">
        <td width="100%" align="center">
            <h:selectOneRadio value="#{BIFinanceiroControle.velocimetro.tipo}" styleClass="text">
                <f:selectItems value="#{BIFinanceiroControle.tiposVelocimetro}"/>
                <a4j:support action="#{BIFinanceiroControle.configurarVelocimetro}"
                             event="onchange" reRender="painelVeloc" status="false"/>
            </h:selectOneRadio>
        </td>
    </tr>
    <c:if test="${!BIFinanceiroControle.metaZerada && !BIFinanceiroControle.metaNaoAberta}">
    <tr style="width: 100%;">
        <td width="100%" align="center">
            <div id="chartvelocimetro" style="width:100%; height: 260px;"></div>

        </td>

    </tr>
    </table>
    <table width="100%" id="tableVelocimetroAtualizar">
    <tr>
        <td width="100%" align="left">
            <h:outputText value="Dados atualizados em #{BIFinanceiroControle.dataAtualizacaoDados}" style="font-size:8pt;color:#5E5757;"  styleClass="text"/>
            <h:panelGroup id="grupoBtnAtualizarVel">
                <a4j:commandLink id="grupoBtnAtualizarVelLink"  reRender="mdlMensagemGenerica"
                                 oncomplete="#{BIFinanceiroControle.msgAlert}" action="#{BIFinanceiroControle.confirmarExcluir}"
                                 value=" Atualizar dados"  />
            </h:panelGroup>
        </td>
        <td align="right">
            <a4j:commandLink action="#{MetaFinanceiroControle.abrirTelaMetas}"
                             oncomplete="#{MetaFinanceiroControle.msgAlert}"
                             title="Ir ao cadastro de metas">
                <h:outputText value="Metas" style="font-size:8pt;"/>
            </a4j:commandLink>
        </td>
    </tr>
    </c:if>
</table>
        <c:if test="${BIFinanceiroControle.metaNaoAberta}">
            <div style="padding: 0px !important; margin: 23% auto 0 auto; width: 100%; text-align: center;" >
            <h:outputText  styleClass="text" value="Não existe meta aberta para a empresa no mês selecionado."/>
            <br/>
            <a4j:commandLink action="#{MetaFinanceiroControle.abrirTelaMetas}"
                             oncomplete="#{MetaFinanceiroControle.msgAlert}">
                <h:outputText value="Cadastre uma nova meta!"  style="font-size:12pt;"/>
            </a4j:commandLink>
            </div>
        </c:if>

        <c:if test="${BIFinanceiroControle.metaZerada}">
            <div style="padding: 0px !important; margin: 23% auto 0 auto; width: 100%; text-align: center;" >
            <h:outputText  styleClass="text" value="A meta está com valores zerados."/>
            <br/>
            <a4j:commandLink action="#{MetaFinanceiroControle.abrirTelaMetasEdicao}"
                             oncomplete="#{MetaFinanceiroControle.msgAlert}">
                <h:outputText value="Atualize a meta deste mês!"  style="font-size:12pt;"/>
                <f:param name="chavePrimaria" value="#{BIFinanceiroControle.codigoMeta}"/>
            </a4j:commandLink>
            </div>
        </c:if>
        
        
<div id="carregandoVelocimetro" style="padding: 0px !important;display: none; margin: 25% auto 0 auto; width: 200px;" >
    <h:graphicImage value="../../imagens/carregando.gif" style="margin-right: 10px; vertical-align: middle;" />
    <h:outputText value="Atualizando dados..." styleClass="text" style="font-weight:bold;color:#0f4c6b;font-size:10pt;"/>

</div>
<a4j:region id="extr2">
    <a4j:status id="velocimetrostatus" onstart="onStartVeloc()" onstop="onStopVeloc()"></a4j:status>
</a4j:region>


<script>
    carregarVelocimetro();
</script>





<%@page contentType="text/html" %>
<%@page pageEncoding="ISO-8859-1" %>
<head>
    <%@include file="../../includes/include_import_minifiles.jsp"%>
    <script type="text/javascript" language="javascript" src="../../script/Notifier.js"></script>
</head>
<script type="text/javascript" language="javascript" src="../hoverform.js"></script>
<link href="../../css/otimize.css" rel="stylesheet" type="text/css">
<link href="../../beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<link href="../../css/packcss1.0.min.css" rel="stylesheet" type="text/css">
<title>Relatório de Cheques Devolvidos ou Trocados</title>

<%@taglib prefix="f" uri="http://java.sun.com/jsf/core" %>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax" %>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich" %>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<%@include file="/includes/verificaModulo.jsp" %>
<f:view>
    <jsp:include page="../../includes/include_carregando_ripple.jsp"/>
    <c:set var="titulo" scope="session" value="Relatório de Cheques Devolvidos ou Trocados"/>
    <c:set var="urlWiki" scope="session" value="${SuperControle.urlBaseConhecimento}como-vejo-os-cheques-que-foram-devolvidos/"/>
    <c:set var="modulo" scope="request" value="financeiroWeb"/>
    <a4j:loadScript src="../../script/jquery.maskedinput-1.2.2.js"/>

        <f:facet name="header">
            <jsp:include page="../../topoReduzido_material.jsp"/>
        </f:facet>

        <h:form id="form" style="height: auto;overflow: visible;"  target="_blank">




              <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar" columnClasses="classEsquerda, classDireita" width="100%" style="padding-left:0px;">
                   <h:panelGroup rendered="#{RelatorioDevolucaoChequeControle.mostrarCampoEmpresa}">

                   <h:outputText  styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold" value="Empresa:"/>
                   </h:panelGroup>
                       <h:panelGroup
                               rendered="#{RelatorioDevolucaoChequeControle.mostrarCampoEmpresa}"
                               styleClass="font-size-em-max" >
                           <div class="cb-container margenVertical">
                                <h:selectOneMenu id="empresa"

                                                 rendered="#{RelatorioDevolucaoChequeControle.mostrarCampoEmpresa}"
                                                 onblur="blurinput(this);" onfocus="focusinput(this);"
                                                 value="#{RelatorioDevolucaoChequeControle.empresaVO.codigo}">
                                    <f:selectItems
                                            value="#{RelatorioDevolucaoChequeControle.listaSelectItemEmpresa}" />
                                </h:selectOneMenu>
                           </div>
                       </h:panelGroup>
                  <h:outputText  styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold" value="Tipo:"/>
                  <h:panelGroup
                          styleClass="font-size-em-max" >
                      <div class="cb-container margenVertical">
                          <h:selectOneMenu onblur="blurinput(this);" onfocus="focusinput(this);"
                                           value="#{RelatorioDevolucaoChequeControle.tipoConsulta}">
                              <f:selectItems
                                      value="#{RelatorioDevolucaoChequeControle.listaSelectTipos}" />
                          </h:selectOneMenu>
                      </div>
                  </h:panelGroup>


                        <h:outputText  styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold" value="Período devolução:"/>
                        <h:panelGroup styleClass="flex" layout="block">
                            <div class="margenVertical">
                            <h:panelGroup  styleClass="dateTimeCustom">
                                <rich:calendar id="dataInicio"
                                               value="#{RelatorioDevolucaoChequeControle.inicio}"
                                               inputSize="10"
                                               inputClass="form"
                                               oninputblur="blurinput(this);"
                                               oninputfocus="focusinput(this);"
                                               oninputchange="return validar_Data(this.id);"
                                               datePattern="dd/MM/yyyy"
                                               enableManualInput="true"
                                               zindex="2"
                                               showWeeksBar="false"
                                               buttonIcon="/imagens_flat/calendar-button.svg">
                                </rich:calendar>
                                <h:message for="dataInicio"  styleClass="mensagemDetalhada"/>
                           </h:panelGroup>
                            <rich:spacer width="5px"/>
                            <h:outputText  style="margin: 7px 9px 0 0px;margin-left: 15px;" styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold" value="Até:"/>
                            <rich:spacer width="10px"/>
                            <h:panelGroup styleClass="dateTimeCustom" >
                                <rich:calendar id="dataTermino"
                                               value="#{RelatorioDevolucaoChequeControle.fim}"
                                               inputSize="10"
                                               inputClass="form"
                                               oninputblur="blurinput(this);"
                                               oninputfocus="focusinput(this);"
                                               oninputchange="return validar_Data(this.id);"
                                               datePattern="dd/MM/yyyy"
                                               enableManualInput="true"
                                               zindex="2"
                                               showWeeksBar="false"
                                               buttonIcon="/imagens_flat/calendar-button.svg">
                                </rich:calendar>
                                <h:message for="dataTermino"  styleClass="mensagemDetalhada"/>
                                <rich:jQuery id="mskData" selector=".rich-calendar-input" timing="onload" query="mask('99/99/9999')" />
                            </h:panelGroup>
                        </h:panelGroup>


                        <h:outputText  styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold" value="Número do cheque:"/>
                        <h:panelGroup styleClass="font-size-em-max" >
                           <h:inputText style="margin-top: 5px;margin-bottom: 5px;" styleClass="inputTextClean" value="#{RelatorioDevolucaoChequeControle.numeroCheque}"></h:inputText>
                        </h:panelGroup>

                        <h:outputText  styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold" value="Agência:"/>
                        <h:panelGroup styleClass="font-size-em-max" >
                          <h:inputText style="margin-top: 5px;margin-bottom: 5px;" styleClass="inputTextClean" value="#{RelatorioDevolucaoChequeControle.agencia}" ></h:inputText>
                        </h:panelGroup>
                       <h:outputText  styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold" value="Conta:"/>
                       <h:panelGroup styleClass="font-size-em-max" >
                        <h:inputText style="margin-top: 5px;margin-bottom: 5px;"  value="#{RelatorioDevolucaoChequeControle.conta}" styleClass="inputTextClean"></h:inputText>
                      </h:panelGroup>

                      <h:outputText  styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold" value="Banco:"/>
                       <h:panelGroup styleClass="font-size-em-max" >
                           <div class="cb-container margenVertical">
                               <h:selectOneMenu id="comboBanco"
                                                onblur="blurinput(this);" onfocus="focusinput(this);"
                                                value="#{RelatorioDevolucaoChequeControle.banco}">
                                   <f:selectItems
                                           value="#{RelatorioDevolucaoChequeControle.listaSelectItemBanco}" />
                               </h:selectOneMenu>
                           </div>
                       </h:panelGroup>

                        <h:outputText  styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold" value="Cliente:"/>
                       <h:panelGroup id="panelPessoa" >
                           <h:panelGroup styleClass="font-size-em-max">
                               <h:inputText id="pessoa" size="50" maxlength="80"
                                            styleClass="inputTextClean"
                                            style="margin-top: 5px;margin-bottom: 5px;"
                                            tabindex="3"
                                            onfocus="focusinput(this);" value="#{RelatorioDevolucaoChequeControle.pessoaVO.nome}">
                               </h:inputText>

                               <a4j:commandLink id="limparPessoa"
                                                style="vertical-align: middle;"
                                                onclick="document.getElementById('form:pessoa').value='';"
                                                title="Limpar filtro de cliente.">
                                   <i class="fa-icon-eraser"></i>
                               </a4j:commandLink>
                           </h:panelGroup>


                           <rich:suggestionbox height="200" width="500"
                                               for="pessoa"
                                               fetchValue="#{result}"
                                               suggestionAction="#{RelatorioDevolucaoChequeControle.executarAutocompleteConsultaPessoa}"
                                               minChars="1" rowClasses="20"
                                               status="true"
                                               ajaxSingle="false"
                                               reRender="mensagem, pgNovaPessoa"
                                               nothingLabel="Nenhuma pessoa encontrada!"
                                               var="result" id="suggestionPessoa">
                               <a4j:support event="onselect" ignoreDupResponses="true" action="#{RelatorioDevolucaoChequeControle.selecionarPessoaSuggestionBox}" focus="descricao" reRender="form"/>

                               <h:column>
                                   <f:facet name="header">
                                       <h:outputText styleClass="textverysmall" value="Nome"/>
                                   </f:facet>
                                   <h:outputText value="#{result.nome}"/>
                               </h:column>
                               <h:column>
                                   <f:facet name="header">
                                       <h:outputText styleClass="textverysmall" value="CPF"/>
                                   </f:facet>
                                   <h:outputText value="#{result.cfp}"/>
                               </h:column>
                           </rich:suggestionbox>


                       </h:panelGroup>

                      <h:outputText  styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold" value="Considerar apenas os que não foram recebidos novamente:"/>
                      <h:panelGroup layout="block" id="pgApenasNaoRecebidos" style="text-align: left;" styleClass="checkbox-fa semEventoClasse">
                          <a4j:commandLink reRender="form:pgApenasNaoRecebidos"  styleClass="linkPadrao">
                              <f:setPropertyActionListener  value="#{!RelatorioDevolucaoChequeControle.consultarSomenteChequesNaoRecebidos}" target="#{RelatorioDevolucaoChequeControle.consultarSomenteChequesNaoRecebidos}"/>
                              <h:outputText style="font-family: Arial;font-size: 1.6em!important;margin-left: 5px" styleClass="#{RelatorioDevolucaoChequeControle.consultarSomenteChequesNaoRecebidos ? 'fa-icon-check' : 'fa-icon-check-empty'} texto-cor-cinza texto-font"
                                            value="" title="Ao Marcar esta opção, serão consultados apenas cheques devolvidos que estão em aberto"/>
                          </a4j:commandLink>
                      </h:panelGroup>
              </h:panelGrid>

            <%-- Remoção da mensagem de aviso conforme ticket M2-2827  --%>
            <%--
            <h:panelGrid columns="1" style="margin-top: 20px;" columnClasses="colunaCentralizada" width="100%" >
                <h:outputText style="color: #777" value="Atenção!"></h:outputText>
                <h:outputText style="color: #777" value="Este relatório irá considerar apenas os cheques que foram devolvidos à partir de 16/12/2017, pelo fato de ser um relatório novo e que engloba apenas os novos casos de devoluções de cheques à partir da data mencionada."></h:outputText>
            </h:panelGrid> --%>

             <h:panelGrid columns="1" style="margin-top: 20px;" width="100%" >
                        <h:panelGrid id="mensagem" columns="1" width="100%" columnClasses="colunaEsquerda">
                            <h:outputText styleClass="mensagemDetalhada" value="#{RelatorioDevolucaoChequeControle.mensagemDetalhada}"/>
                        </h:panelGrid>
                        <h:panelGrid columns="1" width="100%"  columnClasses="colunaCentralizada">
                            <h:panelGroup>
                                <h:panelGroup >

                                    <a4j:commandLink
                                                     reRender="form"
                                                     id="pesquisar"
                                                     oncomplete="#{RelatorioDevolucaoChequeControle.mensagemNotificar}"
                                                     action="#{RelatorioDevolucaoChequeControle.consultarRelatorio}"
                                                     styleClass="pure-button pure-button-primary" style="margin-right:10px; margin-left: 15px;"
                                                     title="#{msg.msg_consultar_dados}">
                                        <i style="font-size: 14px" class="fa-icon-search"></i> &nbsp <h:outputText style="font-size: 14px"
                                                                                                                   value="Pesquisar"/>
                                    </a4j:commandLink>

                                </h:panelGroup>

                                <a4j:commandLink reRender="form"
                                                 action="#{RelatorioDevolucaoChequeControle.limparFiltros}"
                                                 styleClass="pure-button">
                                    <h:outputText style="font-size: 14px" value="Limpar filtros"/>
                                </a4j:commandLink>

                            </h:panelGroup>
                        </h:panelGrid>
              </h:panelGrid>
            <h:panelGroup rendered="#{RelatorioDevolucaoChequeControle.mostrarRelatorio}">
                <h:panelGrid  width="100%"
                             style="margin-bottom: 8px;text-align: right;">
                    <h:panelGroup layout="block">
                        <%--BOTÃO EXCEL--%>
                        <a4j:commandLink id="exportarExcelProdutos"
                                         style="margin-left: 8px;"
                                         actionListener="#{RelatorioDevolucaoChequeControle.exportarRelatorio}"
                                         rendered="#{not empty RelatorioDevolucaoChequeControle.listaRelatorio}"
                                         oncomplete="abrirPopup('../../UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/vnd.ms-excel','Transacoes', 800,200);#{ExportadorListaControle.msgAlert}"
                                         accesskey="2" styleClass="botoes linkPadrao">
                            <f:attribute name="lista" value="#{RelatorioDevolucaoChequeControle.listaRelatorio}"/>
                            <f:attribute name="tipo" value="xls"/>
                            <f:attribute name="atributos"
                                         value="nomePessoa=Cliente,nomeNoCheque=Nome no Cheque,numeroCheque=Número,agenciaCheque=Agência,contaCheque=Conta,nomeBancoCheque=Banco,dataCompensacao_apresentar=Data compensação,dataDevolucao_apresentar=Data devolução,valor_apresentar=Valor"/>
                            <f:attribute name="prefixo" value="RelatorioDevolucaoCheque"/>
                            <h:outputText title="Exportar para o formato Excel" styleClass="btn-print-2 excel"/>
                        </a4j:commandLink>
                        <%--BOTÃO PDF--%>
                        <a4j:commandLink id="exportarPdfProdutos"
                                         style="margin-left: 8px;"
                                         actionListener="#{RelatorioDevolucaoChequeControle.exportarRelatorio}"
                                         rendered="#{not empty RelatorioDevolucaoChequeControle.listaRelatorio}"
                                         oncomplete="abrirPopup('../../UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/pdf','Transacoes', 800,200);#{ExportadorListaControle.msgAlert}"
                                         accesskey="2" styleClass="botoes linkPadrao">
                            <f:attribute name="lista" value="#{RelatorioDevolucaoChequeControle.listaRelatorio}"/>
                            <f:attribute name="tipo" value="pdf"/>
                            <f:attribute name="atributos"
                                         value="nomePessoa=Cliente,nomeNoCheque=Nome no Cheque,numeroCheque=Número,agenciaCheque=Agência,contaCheque=Conta,nomeBancoCheque=Banco,dataCompensacao_apresentar=Data compensação,dataDevolucao_apresentar=Data devolução,valor_apresentar=Valor"/>
                            <f:attribute name="prefixo" value="RelatorioDevolucaoCheque"/>
                            <h:outputText title="Exportar para o formato PDF" styleClass="btn-print-2 pdf"/>
                        </a4j:commandLink>
                    </h:panelGroup>
                </h:panelGrid>

                <rich:dataTable id="clientes" value="#{RelatorioDevolucaoChequeControle.listaRelatorio}" var="item" rowKeyVar="status" width="100%"
                                rowClasses="linhaImpar, linhaPar" columnClasses="colunaCentralizada,colunaEsquerda,colunaEsquerda,colunaCentralizada,colunaCentralizada,colunaCentralizada,colunaEsquerda,colunaCentralizada,colunaCentralizada,colunaDireita" rows="30">
                    <%@include file="/pages/ce/includes/include_contador_richtable.jsp" %>
                    <rich:column sortBy="#{item.nomePessoa}">
                        <f:facet name="header">
                            <h:outputText value="Cliente"/>
                        </f:facet>
                        <h:outputText value="#{item.nomePessoa}"/>
                    </rich:column>
                    <rich:column sortBy="#{item.nomeNoCheque}">
                        <f:facet name="header">
                            <h:outputText value="Nome Terceiro"/>
                        </f:facet>
                        <h:outputText value="#{item.nomeNoCheque}"/>
                    </rich:column>
                    <rich:column sortBy="#{item.numeroCheque}">
                        <f:facet name="header">
                            <h:outputText value="Número"/>
                        </f:facet>
                        <h:outputText value="#{item.numeroCheque}"/>
                    </rich:column>
                    <rich:column sortBy="#{item.agenciaCheque}">
                        <f:facet name="header">
                            <h:outputText value="Agência"/>
                        </f:facet>
                        <h:outputText value="#{item.agenciaCheque}"/>
                    </rich:column>
                    <rich:column sortBy="#{item.contaCheque}">
                        <f:facet name="header">
                            <h:outputText value="Conta"/>
                        </f:facet>
                        <h:outputText value="#{item.contaCheque}"/>
                    </rich:column>
                    <rich:column sortBy="#{item.nomeBancoCheque}">
                        <f:facet name="header">
                            <h:outputText value="Banco"/>
                        </f:facet>
                        <h:outputText value="#{item.nomeBancoCheque}"/>
                    </rich:column>

                    <rich:column sortBy="#{item.dataCompensacao}">
                        <f:facet name="header">
                            <h:outputText value="Data compensação"/>
                        </f:facet>
                        <h:outputText value="#{item.dataCompensacao_apresentar}"/>
                    </rich:column>

                    <rich:column sortBy="#{item.dataDevolucao}">
                        <f:facet name="header">
                            <h:outputText value="Data devolução"/>
                        </f:facet>
                        <h:outputText value="#{item.dataDevolucao_apresentar}"/>
                    </rich:column>

                    <rich:column sortBy="#{item.valorCheque}">
                        <f:facet name="header">
                            <h:outputText value="Valor"/>
                        </f:facet>
                        <h:outputText value="#{item.valor_apresentar}"/>
                    </rich:column>

                    <rich:column sortBy="#{item.devolvido}">
                        <f:facet name="header">
                            <h:outputText value="Tipo"/>
                        </f:facet>
                        <h:outputText rendered="#{item.devolvido}" value="Devolvido"/>
                        <h:outputText rendered="#{!item.devolvido}" value="Trocado"/>
                    </rich:column>


                </rich:dataTable>
                <h:outputText  style="float: right;font-family: Arial,Verdana,sans-serif;font-size: 12px;margin-top: 5px;margin-right: 3px; margin-bottom: 40px"
                               value="Total: #{RelatorioDevolucaoChequeControle.totalRelatorio_apresentar}"/>

                <rich:datascroller for="clientes" maxPages="20"  id="scClientes"/>
            </h:panelGroup>
        </h:form>
</f:view>

<script type="text/javascript">
    document.getElementById("form:dataInicio").focus();
</script>

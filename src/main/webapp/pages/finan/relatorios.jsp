<%@include file="includes/include_imports.jsp" %>
<!-- <PERSON><PERSON><PERSON> o elemento HEAD da p�gina -->
<head>
<style>
    .rich-panel-body{
        padding: 0px !important;
    }
    .rich-panel-header {
        height: 20px !important;
    }
    html, body {
        width: 100%;
        height: 100%;
        margin: 0 0 0 0;
        padding: 0 0 0 0;
    }

    .colunaCentralizada:hover > a {
        text-decoration: none;
    }

    .textoPadraoLabel {
        font-family: Arial;
        font-size: 12px;
        font-weight: normal;
        font-style: normal;
        font-stretch: normal;
        line-height: 1.43;
        letter-spacing: normal; /*text-align: center;*/
        color: #777777;
    }
</style>

    <%@include file="includes/include_head_finan.jsp" %>
    <script type="text/javascript" language="javascript" src="${contextoFinan}/script/telaInicial.js"></script>
    <script type="text/javascript" language="javascript" src="${contextoFinan}/script/script2.js"></script>

    <script src="../../script/amcharts.js" type="text/javascript"></script>
    <script src="../../script/serial.js" type="text/javascript"></script>
    <script src="../../script/gauge.js" type="text/javascript"></script>


    <link href="../../css/smartbox/smartbox.css" rel="stylesheet" type="text/css">


</head>

<c:set var="moduloSession" value="1" scope="session"/>
<%
    String m = request.getParameter("m");
    if(m == null){
        m = "web";
    }
    pageContext.setAttribute("modoView",m);
%>
<f:view>
    <jsp:include page="/includes/include_carregando_ripple.jsp" flush="true"/>
    <a4j:jsFunction name="reRenderValidMenuLateral" reRender="validMenuLateral"/>
    <h:form id="form">
        <html>
            <body onload="initReceita(); initReceber();reRenderValidMenuLateral();" class="finanBI">
                 <h:panelGroup layout="block" style="display: inline-table;width: 100%;" styleClass="fundoCinza">
                <h:panelGroup layout="block" styleClass="bgtop topoZW" rendered="#{MenuControle.apresentarTopo}">
                    <jsp:include page="../../include_topo_novo.jsp" flush="true"/>
                    <jsp:include page="../../include_menu_fin_flat.jsp" flush="true"/>
                    <rich:jQuery selector=".item3" query="addClass('menuItemAtual')"/>
                </h:panelGroup>

                <h:panelGroup layout="block" styleClass="caixaCorpo">
                    <h:panelGroup layout="block" style="height: 80%;width: 100%">
                        <h:panelGroup layout="block" styleClass="caixaMenuLatel">
                            <div  style="width: ${modoView != 'mobile' ? 'calc(100% - 240px);' : 'calc(100%);'}; height: fit-content;" class="container-imagem container-conteudo-central">
                                <h:panelGrid columns="2" columnClasses="w30, w70" width="100%" styleClass="crm">

                                    <c:if test="${not BIFinanceiroControle.configuracaoFinan.usarMovimentacaoContas}">
                                        <%@include file="include_contas_pagar_receber.jsp" %>
                                    </c:if>

                                    <rich:panel style="width: 100%;height: 400px;" id="painelResumoContas"
                                                headerClass="headerSanfona"
                                                rendered="#{BIFinanceiroControle.configuracaoFinan.usarMovimentacaoContas}">
                                        <f:facet name="header">
                                            <h:panelGrid columns="2" width="100%" columnClasses="esquerda, direita" cellpadding="0" cellspacing="0">
                                                <h:panelGroup>
                                                    <h:outputLink styleClass="linkWiki"
                                                                  value="#{SuperControle.urlBaseConhecimento}bi-saldos-financeiro/"
                                                                  title="Clique e saiba mais: Saldos"
                                                                  target="_blank">
                                                        <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                                                    </h:outputLink>
                                                    <h:outputText value="Saldos" styleClass="text" style="font-weight:bold;color:#0f4c6b;font-size:10pt;"/>
                                                </h:panelGroup>

                                                <h:panelGroup>
                                                    <h:selectBooleanCheckbox id="agruparConta" value="#{BIFinanceiroControle.agruparTipoConta}">
                                                        <a4j:support action="#{BIFinanceiroControle.agruparPorTipoConta}" reRender="painelResumoContas"
                                                                     event="onclick"
                                                                     status="false"></a4j:support>
                                                    </h:selectBooleanCheckbox>

                                                    <h:outputText value="Agrupar por tipo" styleClass="text" style="font-weight:bold;font-size:8pt;"> </h:outputText>
                                                    <rich:spacer width="10px"/>
                                                    <a4j:commandButton id="btnAtualizarSaldo" reRender="painelResumoContas"
                                                                       title="Atualizar saldos das contas"
                                                                       style="vertical-align:middle; width:16px;height:16px;"
                                                                       status="statusInComponent"
                                                                       action="#{BIFinanceiroControle.atualizarResumoContas}"
                                                                       image="/images/update.png"/>
                                                </h:panelGroup>
                                            </h:panelGrid>
                                        </f:facet>
                                        <div  style="overflow-x: visible; overflow-y: scroll; height: 90%">
                                            <h:panelGrid width="100%">


                                                <h:dataTable value="#{BIFinanceiroControle.resumoContasTela}" width="100%" var="tipo"
                                                             rendered="#{!BIFinanceiroControle.agruparTipoConta}">
                                                    <h:column>
                                                        <h:outputText value="#{tipo.descricao}" style="color:#0f4c6b;font-size:9pt;font-weight:bold;"  styleClass="text" />
                                                        <rich:separator width="100%" height="2px" />
                                                        <h:dataTable value="#{tipo.genericos}" width="100%" var="conta"
                                                                     rowClasses="linhaPar, linhaImpar" columnClasses="esquerda, direita">
                                                            <h:column>
                                                                <h:commandLink action="#{GerenciadorContaControle.visualizarContaBI}">
                                                                    <h:outputText value="#{conta.label}" style="color:#0f4c6b;font-size:9pt;"  styleClass="text" />
                                                                </h:commandLink>
                                                            </h:column>
                                                            <h:column>
                                                                <h:outputText value="#{conta.valor_Apresentar}" styleClass="text" style="color:#{conta.color};font-size:9pt;font-weight:bold;"/>
                                                            </h:column>
                                                        </h:dataTable>
                                                    </h:column>

                                                </h:dataTable>

                                                <h:dataTable value="#{BIFinanceiroControle.resumoContasTela}" width="100%" var="conta"
                                                             id="saldosBIResumido"
                                                             rowClasses="linhaPar, linhaImpar" rendered="#{BIFinanceiroControle.agruparTipoConta}">
                                                    <h:column>
                                                        <h:panelGrid columns="2" columnClasses="esquerda, direita" width="100%" cellpadding="4" cellspacing="4">
                                                            <h:outputText value="#{conta.descricao}" style="color:#0f4c6b;font-size:9pt;font-weight:bold;"  styleClass="text" />
                                                            <h:outputText id="caixaBI" value="#{conta.valor_Apresentar}" styleClass="text" style="color:#{conta.color};font-size:9pt;font-weight:bold;"/>
                                                        </h:panelGrid>
                                                    </h:column>
                                                </h:dataTable>
                                            </h:panelGrid>
                                        </div>

                                    </rich:panel>
                                    <rich:panel id="panelBIFinanceiro" style="#{BIFinanceiroControle.configuracaoFinan.usarMovimentacaoContas ? 'width: 100%' : 'width: 90%'};height: 400px;">

                                        <%@include file="include_grafico_bi_financeiro.jsp" %>

                                    </rich:panel>


                                    <%@include file="include_velocimetro.jsp" %>





                                    <h:panelGroup>
                                        <h:panelGrid columnClasses="#{BIFinanceiroControle.configuracaoFinan.usarMovimentacaoContas ? 'w50,w50' : 'w100'}"
                                                     columns="2"
                                                     width="#{BIFinanceiroControle.configuracaoFinan.usarMovimentacaoContas ? '100%' : '90%'}"
                                                     style="height: 350px;" cellpadding="0" cellspacing="0">

                                            <c:if test="${BIFinanceiroControle.configuracaoFinan.usarMovimentacaoContas}">
                                                <%@include file="include_contas_pagar_receber.jsp" %>
                                            </c:if>



                                            <rich:panel id="graficoFP" style="height: 350px; padding: 0px !important;" headerClass="headerSanfona">

                                                <f:facet name="header">
                                                    <h:panelGrid columns="4" width="100%" columnClasses="esquerda, esquerda, centralizado, direita" cellpadding="0" cellspacing="0">
                                                        <h:outputLink styleClass="linkWiki"
                                                                      value="#{SuperControle.urlBaseConhecimento}bi-receita-por-formas-de-pagamento-financeiro/"
                                                                      title="Clique e saiba mais: Receita por formas de pagamento"
                                                                      target="_blank">
                                                            <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                                                        </h:outputLink>
                                                        <h:outputText value="Receita por formas de pagamento" styleClass="text" style="font-weight:bold;color:#0f4c6b;font-size:10pt;"/>

                                                        <a4j:region id="extr">
                                                            <a4j:status id="receitastatus" onstart="onStartReceita()" onstop="onStopReceita()">
                                                            </a4j:status>
                                                        </a4j:region>

                                                        <h:panelGroup>

                                                            <h:inputText id="dataInicioReceitas" value="#{BIFinanceiroControle.dataReceita}"
                                                                         onkeypress="return mascara(this.form, this.id, '99/9999', event);"
                                                                         size="7" styleClass="form" onfocus="focusinput(this);" onblur="blurinput(this);"
                                                                         maxlength="7">
                                                                <f:convertDateTime dateStyle="short" locale="#{SuperControle.localeDefault}" pattern="MM/yyyy"/>
                                                            </h:inputText>

                                                            <rich:hotKey selector="#dataInicioReceitas" key="return"
                                                                         handler="#{rich:element('btnAtualizarResumoReceitas')}.click();return false;"/>

                                                            <a4j:commandButton id="btnAtualizarResumoReceitas" reRender="graficoFP"
                                                                               title="Atualizar Resumo de Receitas"
                                                                               status="receitastatus"
                                                                               style="vertical-align:middle; width:16px;height:16px;"
                                                                               action="#{BIFinanceiroControle.gerarReceitaPorFormaPagamento}"
                                                                               image="/images/update.png"/>
                                                        </h:panelGroup>

                                                    </h:panelGrid>
                                                </f:facet>
                                                <h:panelGroup rendered="#{LoginControle.usuario.administrador}" >

                                                    <h:outputText styleClass="titulo3" value="Empresa:" />
                                                    <rich:spacer width="10px" />
                                                    <h:selectOneMenu  id="comboEmpresaResumoReceitaSintetico" styleClass="form"  onblur="blurinput(this);"  onfocus="focusinput(this);" value="#{BIFinanceiroControle.empresaReceitaFormas.codigo}" >
                                                        <f:selectItems  value="#{BIFinanceiroControle.listaSelectItemEmpresaReceitaFormas}" />
                                                    </h:selectOneMenu>
                                                    <rich:spacer width="5px"/>
                                                </h:panelGroup>


                                                <%@include file="include_bi_grafico_formapagamento.jsp" %>
                                                <h:panelGroup rendered="#{BIFinanceiroControle.mostrarDataAtualizacaoReceita}">
                                                    <h:outputText id="dadosReceitaAtu" value="Dados do m�s de #{BIFinanceiroControle.receitaSint.mesDescricao} atualizados at� #{BIFinanceiroControle.receitaSint.dataExecucaoApresentar}"/>
                                                </h:panelGroup>

                                            </rich:panel>
                                            <a4j:jsFunction  name="initReceita"
                                                             action="#{BIFinanceiroControle.gerarReceitaPorFormaPagamentoPrimeiraVez}"
                                                             reRender="graficoFP" status="receitastatus"/>


                                        </h:panelGrid>
                                    </h:panelGroup>

                                </h:panelGrid>
                            </div>
                            <h:panelGroup layout="block" id="validMenuLateral">
                                <c:if test="${modoView != 'mobile'}">
                                    <jsp:include page="../../include_box_menulateral.jsp">
                                        <jsp:param name="menu" value="FIN-BI"/>
                                    </jsp:include>
                                </c:if>
                            </h:panelGroup>
                        </h:panelGroup>
                    </h:panelGroup>
                </h:panelGroup>

                <jsp:include page="../../include_rodape_flat.jsp" flush="true" />
            </h:panelGroup>

                 <c:if test="${SuperControle.menuZwUi}">
                     <script>
                         fecharMenu();
                     </script>
                 </c:if>
        </body>
        </html>
    </h:form>

    <%@include file="/includes/include_modal_mensagem_generica.jsp"%>
    <%@include file="includes/include_modal_abrirCaixa.jsp" %>
    <%@include file="includes/include_modal_consultarCaixa.jsp" %>
</f:view>

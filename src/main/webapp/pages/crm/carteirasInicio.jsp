<%@ page contentType="text/html;charset=ISO-8859-1" pageEncoding="ISO-8859-1" language="java" %>
<%@ include file="/includes/imports.jsp" %>
<link rel="stylesheet" href="${pageContext.request.contextPath}/beta/css/pure-min.css"/>
<link rel="stylesheet" href="${pageContext.request.contextPath}/beta/css/pure-ext.css"/>
<link rel="stylesheet" href="${pageContext.request.contextPath}/beta/css/carteiraCRM.css"/>
<script type="text/javascript" src="script/tooltipster/jquery.tooltipster.min.js"></script>

<c:set var="contexto" value="${pageContext.request.contextPath}" scope="request"/>
<a4j:jsFunction name="reRenderSelecionados" action="#{CarteirasControle.setarQuantidadeClienteSelecionado}" reRender="selecionarTodosCarteira,botaoSelecionarDeselecionar">
    <a4j:actionparam noEscape="true" name="clientesSelecionados"
                     value="retornaArrayCheckados()"/>
    <a4j:actionparam noEscape="true" name="arrayItensPagina"
                     value="retornaArrayItensPagina()"/>
</a4j:jsFunction>
<h:panelGroup id="inicialCarteiras" styleClass="inicioCarteiras">
    <h:panelGroup layout="block" styleClass="inicio pure-g-r pure-u-1">
        <h:panelGroup layout="block" styleClass="blocoCarteiras">
            <h:outputText styleClass="tituloFormulario">
                <h3 style="margin-left: 30px; margin-top: -10px; font-size: 16px;line-height: 28px; display: inline-block">Gestão de
                    Carteiras</h3>
                <h:outputLink value="#{SuperControle.urlBaseConhecimento}o-que-e-a-gestao-de-carteiras/"
                              styleClass="fa-icon-question-sign"
                              style="display: inline-block; margin-top: 5px; color: #9e9e9e; font-size: 18px"
                              title="Clique e saiba mais: Gestão de Carteiras" target="_blank">
                </h:outputLink>
            </h:outputText>

            <h:panelGroup layout="block" styleClass="blocoCarteirasInterno pure-form pure-form-aligned">

                <h:panelGrid columns="3" width="100%">
                    <h:panelGroup>
                        <span class="texto">Filtrar Colaboradores</span>

                        <h:panelGroup id="selEmpresa" layout="block" style="margin-left: -110px; margin-top: 10px;"
                                      styleClass="pure-control-group"
                                      rendered="#{CarteirasControle.usuarioLogado.administrador}">
                            <label>Empresa</label>
                            <h:selectOneMenu onblur="blurinput(this);"
                                             styleClass="inputTextClean texto-cor-cinza texto-font"
                                             style="font-size: 12px !important; margin-top: 10px; width: 361px; height: 33px;"
                                             onfocus="focusinput(this);"
                                             value="#{CarteirasControle.filtroCarteira.empresaVO.codigo}">
                                <f:selectItems value="#{CarteirasControle.listaEmpresas}"/>
                                <a4j:support event="onchange" action="#{CarteirasControle.alterarEmpresa}"
                                             reRender="selTipoColaborador, selColaborador, pnlSugestoes, inicialCarteiras"/>
                            </h:selectOneMenu>
                        </h:panelGroup>

                        <h:panelGroup id="selTipoColaborador" layout="block"
                                      style="margin-left: -110px; margin-top: 10px;" styleClass="pure-control-group">

                            <h:panelGroup layout="block" styleClass="pure-control-group">
                                <label>Tipo</label>

                                <h:selectOneMenu onblur="blurinput(this);"
                                                 id="TipoColaborador"
                                                 styleClass="inputTextClean texto-cor-cinza texto-font"
                                                 style="font-size: 12px !important; width: 45%; height: 33px;"
                                                 onfocus="focusinput(this);"
                                                 value="#{CarteirasControle.filtroCarteira.tipoVinculo}">
                                    <f:selectItems value="#{CarteirasControle.listaTiposColaboradores}"/>
                                    <a4j:support event="onchange"
                                                 actionListener="#{CarteirasControle.alterarTipoColaborador}"
                                                 reRender="selColaborador, selGrupoColaborador, infoColaborador, mostrarColaboradores, selTipoColaborador">
                                        <f:attribute name="filtro" value="#{CarteirasControle.filtroCarteira}"/>
                                    </a4j:support>
                                </h:selectOneMenu>

                            </h:panelGroup>


                        </h:panelGroup>

                        <h:panelGroup id="infoColaborador">
                            <h:panelGroup id="selColaborador" layout="block"
                                          style="margin-left: -110px; margin-top: 10px;"
                                          styleClass="pure-control-group">
                                <label>Nome</label>
                                <h:selectOneMenu onblur="blurinput(this);" id="selColab"
                                                 styleClass="inputTextClean texto-cor-cinza texto-font"
                                                 style="font-size: 12px !important; margin-top: 10px; width: 45%;  height: 33px;"
                                                 onfocus="focusinput(this);"
                                                 value="#{CarteirasControle.filtroCarteira.colaborador.codigo}">
                                    <f:selectItems value="#{CarteirasControle.listaColaboradores}"/>
                                </h:selectOneMenu>
                            </h:panelGroup>
                        </h:panelGroup>
                    </h:panelGroup>
                    <h:panelGroup>
                        <span class="texto">Filtrar Tipos de Clientes</span>
                        <h:panelGroup layout="block" styleClass="pure-control-group">
                            <label>Situação atual</label>
                            <h:selectOneMenu onblur="blurinput(this);"
                                             id="situacaoAtual"
                                             styleClass="inputTextClean texto-cor-cinza texto-font"
                                             style="font-size: 12px !important; margin-top: 10px; width: 45%; height: 33px;"
                                             onfocus="focusinput(this);"
                                             value="#{CarteirasControle.filtroCarteira.situacaoCliente}">
                                <f:selectItems value="#{CarteirasControle.listaSituacaoCliente}"/>
                            </h:selectOneMenu>
                        </h:panelGroup>

                        <h:panelGroup layout="block" styleClass="pure-control-group">
                            <label>Período mais acessado</label>
                            <h:selectOneMenu onblur="blurinput(this);"
                                             id="PeriodoAcessado"
                                             styleClass="inputTextClean texto-cor-cinza texto-font"
                                             style="font-size: 12px !important; margin-top: 10px; width: 45%; height: 33px;"
                                             onfocus="focusinput(this);"
                                             value="#{CarteirasControle.filtroCarteira.periodoAcesso}">
                                <f:selectItems value="#{CarteirasControle.listaPeriodoAcesso}"/>
                            </h:selectOneMenu>
                        </h:panelGroup>

                    </h:panelGroup>

                    <h:panelGroup>
                        <h:panelGroup id="lnkFiltrosAvancados" layout="block"
                                      style="position: initial;margin-top: -59px;"
                                      styleClass="pure-control-group">
                            <a4j:commandLink styleClass="pure-input-1-2"
                                             action="#{CarteirasControle.inicializarFiltrosAvancado}"
                                             oncomplete="Richfaces.showModalPanel('modalFiltroGestaoCarteiras')"
                                             reRender="containerFiltrosAvancados, lnkFiltrosAvancados, modalFiltroGestaoCarteiras">
                                <h:outputText value="Filtros Avançados"
                                              style="font-size: 14px!important; position: relative!important;"
                                              styleClass="link"/>
                            </a4j:commandLink>
                        </h:panelGroup>
                    </h:panelGroup>

                </h:panelGrid>

                <h:panelGroup
                        style="position: absolute;width: 98%;height: 0;top: 160px;border: 1px solid #DCDDDF;transform: rotate(180deg);"/>

                <h:panelGroup layout="block"
                              style="display: inline-block; width: 100% ;text-align: left; margin-top: 30px;">

                    <h:panelGroup layout="block" style="display: inline-block;width: 49%">
                        <a4j:commandLink onclick="Richfaces.showModalPanel('modalReverterCarteira')"
                                         style="border-radius: 3px !important; border: 1px solid #1998FC;box-sizing: border-box; color: #1998FC; margin-top: 12px; background-color: transparent !important;"
                                         id="reverterTransBnt"
                                         value="Reverter Transferência"
                                         styleClass="pure-button margin-h-10"/>
                    </h:panelGroup>

                    <h:panelGroup layout="block" style="display: inline-block; width: 50% ;text-align: right">
                        <a4j:commandLink id="log" action="#{CarteirasControle.realizarConsultaLogObjetoSelecionado}"
                                         styleClass="pure-button"
                                         style="margin-left: 12px; margin-top: 12px; background: #B4B7BB;"
                                         oncomplete="abrirPopup('visualizadorLogForm.jsp', 'VisualizadorLog', 785, 595);"
                                         title="Visualizar Log">
                            <i class="fa-icon-align-justify"></i> &nbsp Log
                        </a4j:commandLink>

                        <a4j:commandLink style="padding: 7px; background: #B4B7BB; margin: 0px 18px 0px 18px;"
                                         id="btnCasosImportantes"
                                         styleClass="pure-button" action="#{CarteirasControle.voltarSugestoes}"
                                         value="Casos importantes a avaliar"
                                         oncomplete="resetSelecConta();" reRender="conteudoCRM, includesCRM">
                        </a4j:commandLink>
                        <a4j:commandLink styleClass="pure-button pure-button-primary" value="Buscar"
                                         id="btnBuscarVinculos"
                                         action="#{CarteirasControle.buscarFiltroSelecionado}"
                                         oncomplete="#{CarteirasControle.dispararDataTables}resetSelecConta();"
                                         reRender="conteudoCRM, includesCRM, mensagens,carteirasColaboradores,totalRegistro,totalClientes">
                        </a4j:commandLink>
                    </h:panelGroup>
                </h:panelGroup>
            </h:panelGroup>
        </h:panelGroup>

        <h:panelGroup rendered="#{!CarteirasControle.panelCarteiras && !CarteirasControle.panelTodasCarteiras}">
            <h:panelGroup id="pnlSugestoes" layout="block" rendered="#{not empty CarteirasControle.listaSugestoes}"
                          styleClass="blocoCarteiras">
                <h:panelGroup layout="block" style="width: 100%">
                    <h3 style="font-size: 16px;display: inline-block;">Casos importantes a avaliar</h3>
                </h:panelGroup>

                <h:panelGroup styleClass="indicacoes" rendered="#{CarteirasControle.clientesSemProfessor}"
                              layout="block">
                        <h:outputText
                                style="font-family: Arial;font-style: normal;font-weight: bold;font-size: 14px;color: #DB2C3D;"
                                value="Clientes ativos sem professor"/>
                    <a4j:repeat id="semVinculo" value="#{CarteirasControle.listaSugestoes}" var="sugestao">
                        <h:panelGroup layout="block" styleClass="pure-control-group"
                                      rendered="#{sugestao.clientesSemVinculo && sugestao.tipoVinculo == 'PR'}"
                                      style="padding: 10px 10px 10px 0px;">
                            <a4j:commandLink id="ativos"
                                             styleClass="indicacoesLink"
                                             onfocus="focusinput(this);"
                                             onblur="blurinput(this);"
                                             action="#{CarteirasControle.escolherSugestao}"
                                             oncomplete="dispararDatatables(true);"
                                             reRender="conteudoCRM, includesCRM">
                                <b><h:outputText value="#{sugestao.qtdVinculos}"/></b>
                                <h:outputText value="Clientes ativos sem #{sugestao.tipoVinculo_Apresentar}"/>
                            </a4j:commandLink>
                        </h:panelGroup>
                        <h:panelGroup layout="block" styleClass="pure-control-group"
                                      rendered="#{sugestao.alunosTreinoSemVinculo}" style="padding: 10px 10px 10px 0px;">
                            <a4j:commandLink id="treinoWeb"
                                             styleClass="indicacoesLink"
                                             onfocus="focusinput(this);"
                                             onblur="blurinput(this);"
                                             action="#{CarteirasControle.escolherSugestao}"
                                             oncomplete="dispararDatatables(true);"
                                             reRender="conteudoCRM, includesCRM">
                                <b><h:outputText  value="#{sugestao.qtdVinculos}"/></b>
                                <h:outputText value="Clientes no TreinoWeb sem professor"/>
                            </a4j:commandLink>
                        </h:panelGroup>
                    </a4j:repeat>
                </h:panelGroup>

                <h:panelGroup styleClass="indicacoes" rendered="#{CarteirasControle.colabInativosVinculos}"
                              layout="block">
                    <h:outputText
                            style="font-family: Arial;font-style: normal;font-weight: bold;font-size: 14px;color: #DB2C3D;"
                            value="Colaboradores inativos que ainda possuem vínculos"/>
                    <a4j:repeat id="aindaComVinculo" value="#{CarteirasControle.listaSugestoes}" var="sugestao">
                        <h:panelGroup layout="block" styleClass="pure-control-group"
                                      rendered="#{sugestao.consultorInativoIrregular}" style="padding: 10px 10px 10px 0px;">
                            <a4j:commandLink id="inativos"
                                             styleClass="indicacoesLink"
                                             onfocus="focusinput(this);"
                                             onblur="blurinput(this);"
                                             action="#{CarteirasControle.escolherSugestao}"
                                             oncomplete="dispararDatatables(true);"
                                             reRender="conteudoCRM, includesCRM">
                               <h:outputText value="O #{sugestao.tipoVinculo_Apresentar} "/>
                                <b><h:outputText value="#{sugestao.nomeColaborador} "/></b>
                                <h:outputText value="está inativo e tem #{sugestao.qtdVinculos} vínculo(s)"/>
                            </a4j:commandLink>
                        </h:panelGroup>
                    </a4j:repeat>
                </h:panelGroup>

                <h:panelGroup styleClass="indicacoes" rendered="#{CarteirasControle.clienteComVinculos}"
                              style="width: 15%; margin-left: 20px;" layout="block">
                    <h:outputText
                            style="font-family: Arial;font-style: normal;font-weight: bold;font-size: 14px;color: #DB2C3D;"
                            value="Cliente(s) com vínculo(s)"/>

                    <a4j:repeat id="comVinculo" value="#{CarteirasControle.listaSugestoes}" var="sugestao">
                        <h:panelGroup layout="block" styleClass="pure-control-group"
                                      rendered="#{sugestao.clientesInconsistentes}" style="padding: 10px 10px 10px 0px;">
                            <a4j:commandLink id="inconsistentes"
                                             styleClass="indicacoesLink"
                                             onfocus="focusinput(this);"
                                             onblur="blurinput(this);"
                                             action="#{CarteirasControle.escolherSugestao}"
                                             oncomplete="dispararDatatables(true);"
                                             reRender="conteudoCRM, includesCRM">
                                <b><h:outputText value="#{sugestao.qtdVinculos}"/></b>
                                <h:outputText value="Cliente(s) com vínculo(s) de #{sugestao.tipoVinculo_Apresentar} da empresa "/>
                                <b><h:outputText value="#{sugestao.empresa_Apresentar}"/></b>
                            </a4j:commandLink>
                        </h:panelGroup>
                    </a4j:repeat>
                </h:panelGroup>

                <h:panelGroup styleClass="indicacoes" rendered="#{CarteirasControle.clientesSemCon}"
                              style="width: 15%; margin-left: 20px;" layout="block">
                    <h:outputText
                            style="font-family: Arial;font-style: normal;font-weight: bold;font-size: 14px;color: #DB2C3D;"
                            value="Cliente(s) sem consultor"/>
                    <a4j:repeat id="semConsultor" value="#{CarteirasControle.listaSugestoes}" var="sugestao">
                        <h:panelGroup layout="block" styleClass="pure-control-group"
                                      rendered="#{sugestao.clientesSemVinculo && sugestao.tipoVinculo == 'CO'}"
                                      style="padding: 10px 10px 10px 0px;">
                            <a4j:commandLink id="cliente"
                                             styleClass="indicacoesLink"
                                             onfocus="focusinput(this);"
                                             onblur="blurinput(this);"
                                             action="#{CarteirasControle.escolherSugestao}"
                                             oncomplete="dispararDatatables(true);"
                                             reRender="conteudoCRM, includesCRM">
                                <b><h:outputText value="#{sugestao.qtdVinculos}"/></b>
                                <h:outputText value="Cliente(s) sem #{sugestao.tipoVinculo_Apresentar}"/>
                            </a4j:commandLink>
                        </h:panelGroup>
                    </a4j:repeat>
                </h:panelGroup>

                <h:panelGroup styleClass="indicacoes" rendered="#{CarteirasControle.consultorCartVazia}" layout="block">
                    <h:outputText
                            style="font-family: Arial;font-style: normal;font-weight: bold;font-size: 14px;color: #DB2C3D;"
                            value="Consultor com carteira vazia"/>

                    <a4j:repeat id="carteiraVazia" value="#{CarteirasControle.listaSugestoes}" var="sugestao">
                        <h:panelGroup layout="block" styleClass="pure-control-group"
                                      rendered="#{sugestao.consultorCarteiraVazia}" style="padding: 10px 10px 10px 0px;">
                            <a4j:commandLink id="consultor"
                                             styleClass="indicacoesLink"
                                             onfocus="focusinput(this);"
                                             onblur="blurinput(this);"
                                             action="#{CarteirasControle.escolherSugestao}"
                                             oncomplete="alert('1 - Você pode adicionar vínculos para este colaborador.\r\n2 - Verifique se ele não deveria estar inativo.\r\n3 - Verifique os tipos de colaborador dele.')"
                                             reRender="conteudoCRM, includesCRM">
                               <h:outputText value="O #{sugestao.tipoVinculo_Apresentar} "/>
                                <b><h:outputText value="#{sugestao.nomeColaborador} "/></b>
                                <h:outputText value="está com a carteira vazia"/>
                            </a4j:commandLink>
                        </h:panelGroup>
                    </a4j:repeat>
                </h:panelGroup>

            </h:panelGroup>
            <h:panelGroup id="pnlParabens" layout="block" rendered="#{empty CarteirasControle.listaSugestoes}"
                          styleClass="blocoCarteiras pure-u-11-12 margin-0-auto parabens">
                <h3>Parabéns</h3>
                <h:panelGroup layout="block" styleClass="blocoCarteirasInterno listado">
                    <span style="font-weight: bold;">Suas carteiras estão atualizadas!</span>
                </h:panelGroup>
            </h:panelGroup>
        </h:panelGroup>

        <h:panelGroup id="pnlCarteiras" styleClass="inicioCarteiras"
                      rendered="#{CarteirasControle.panelCarteiras && !CarteirasControle.panelTodasCarteiras}"
                      layout="block">
            <a4j:keepAlive beanName="ExportadorListaControle"/>
            <h:panelGroup layout="block" styleClass="inicio">
                <h:panelGroup layout="block" styleClass="blocoCarteiras">
                    <h:panelGroup style="margin: 10px 0px 7px 0px;" layout="block">
                        <span class="texto">Resultado - </span>
                        <h:outputText styleClass="texto" style="color: #727273;"
                                      rendered="#{not empty CarteirasControle.filtroBuscarCarteira.tipoVinculo_Apresentar && empty CarteirasControle.filtroBuscarCarteira.nomeColaborador && empty CarteirasControle.resultado}"
                                      value="#{CarteirasControle.filtroBuscarCarteira.tipoVinculo_Apresentar}"/>
                        <h:outputText styleClass="texto" style="color: #727273;"
                                      rendered="#{not empty CarteirasControle.filtroBuscarCarteira.tipoVinculo_Apresentar && not empty CarteirasControle.filtroBuscarCarteira.nomeColaborador && empty CarteirasControle.resultado}"
                                      value="#{CarteirasControle.filtroBuscarCarteira.tipoVinculo_Apresentar}, "/>
                        <h:outputText styleClass="texto" style="color: #727273;"
                                      rendered="#{not empty CarteirasControle.filtroBuscarCarteira.nomeColaborador && empty CarteirasControle.resultado}"
                                      value="#{CarteirasControle.filtroBuscarCarteira.nomeColaborador}"/>
                        <h:outputText styleClass="texto" style="color: #727273;"
                                      rendered="#{not empty CarteirasControle.resultado}"
                                      value="#{CarteirasControle.resultado}"/>
                    </h:panelGroup>
                    <h:panelGroup
                            style="position: absolute;width: 98%;height: 0;top: 50px;border: 1px solid #DCDDDF;transform: rotate(180deg);"/>
                    <h:panelGroup layout="block">
                        <h:panelGroup id="carteirasColaborador" layout="block"
                                      rendered="#{not empty CarteirasControle.filtroBuscarCarteira.nomeColaborador}"
                                      styleClass="blocoMenuLateral pure-u-1-4 pure-g">
                            <h:panelGrid id="blocoDadosColaborador" columns="2">
                                <h:panelGroup id="fotoColaborador" layout="block" style="margin-right: 10px">
                                    <a4j:mediaOutput element="img" id="imagemFoto"
                                                     style="width:93px;height:93px; border-radius: 50%;font-size: 16px;vertical-align: middle;"
                                                     cacheable="false"
                                                     rendered="#{!SuperControle.fotosNaNuvem}"
                                                     createContent="#{CarteirasControle.paintFoto}"
                                                     value="#{ImagemData}"
                                                     mimeType="image/jpeg">
                                        <f:param name="largura" value="100"/>
                                        <f:param name="altura" value="100"/>
                                    </a4j:mediaOutput>
                                    <h:graphicImage rendered="#{SuperControle.fotosNaNuvem}"
                                                    style="left:0px;width:93px;height:93px;border-radius: 50%;font-size: 16px;vertical-align: middle;"
                                                    url="#{CarteirasControle.paintFotoDaNuvem}"/>
                                </h:panelGroup>
                                <h:panelGroup>
                                    <h:panelGroup layout="block" styleClass="pure-control-group">
                                        <h:outputText styleClass="texto" style="color: #51555A;font-size: 16px;"
                                                      value="#{CarteirasControle.filtroBuscarCarteira.nomeColaborador}"/>
                                    </h:panelGroup>
                                    <h:panelGroup layout="block" styleClass="pure-control-group">
                                        <h:outputText styleClass="texto"
                                                      style="color: #51555a; font-weight: normal;font-size: 16px;"
                                                      value="Status: "/>
                                        <h:outputText styleClass="texto texto-upper"
                                                      style="color: #{CarteirasControle.filtroBuscarCarteira.colaborador.situacao eq 'AT' ? '#59dd0f;' : '#C81810;'}
                                                      font-weight: normal;font-size: 16px;"
                                                      value="#{CarteirasControle.filtroBuscarCarteira.colaborador.situacao_Apresentar}"/>
                                    </h:panelGroup>

                                    <h:panelGroup layout="block" styleClass="pure-control-group"
                                                  style="margin-top: 26px;">
                                        <h:outputText styleClass="texto"
                                                      style="color: #51555A; font-weight: normal;font-size: 16px;"
                                                      value="Total de Clientes:"/>
                                        <h:outputText styleClass="texto" style="font-size: 16px;color: #51555A;"
                                                      value="#{CarteirasControle.filtroBuscarCarteira.totalDeVinculos}"/>
                                    </h:panelGroup>
                                </h:panelGroup>
                                &nbsp;</h:panelGrid>
                            <h:panelGroup layout="block" styleClass="pure-g-r pure-u-1">
                                <h:panelGroup layout="block" styleClass="pure-u-1-2 text-left">
                                    <h:outputText value="Carteiras" styleClass="texto" style="font-size: 16px;"/>
                                </h:panelGroup>

                                <h:panelGroup layout="block" styleClass="pure-u-1-2 text-left">
                                    <h:outputText value="Vínculos" styleClass="texto" style="font-size: 16px;"/>
                                </h:panelGroup>
                            </h:panelGroup>
                            <a4j:repeat value="#{CarteirasControle.filtroBuscarCarteira.listaDeVinculos}"
                                        var="vinculo">
                                <a4j:commandLink action="#{CarteirasControle.alterarSelecionado}"
                                                 styleClass="pure-button"
                                                 style="width: 88%;font-size:14px; #{vinculo.selecionado_Apresentar}"
                                                 oncomplete="tabelaAtual.fnReloadAjax();"
                                                 reRender="carteirasColaborador">
                                    <h:outputText style="width: 46%;margin-right: -5%;"
                                                  value="#{vinculo.qtdVinculos}"/>
                                    <h:outputText style="width: 50%;margin-left: -2%;"
                                                  value="#{vinculo.tipoVinculo_Apresentar}"/>
                                    <h:panelGroup rendered="#{vinculo.selecionado}"
                                                  style="width: 0; height: 0;top: 0%; left: 100%; border-top: 18px solid transparent; border-left: 16px solid #F3F3F4; border-bottom: 18px solid transparent;"/>
                                </a4j:commandLink>
                            </a4j:repeat>
                            <h:panelGroup layout="block"
                                          style="position: absolute; top: 0px; left: 90%; width: 0%;height: 100%; border: 1px solid #DCDDDF; transform: rotate(180deg);"/>
                        </h:panelGroup>

                        <h:panelGroup layout="block" id="blocoTabela" styleClass="blocoPrincipal pure-u-3-4 pure-g"
                                      style="#{CarteirasControle.apresentarBlocoTabela}">
                            <h:panelGroup layout="block" styleClass="pure-u-1 pure-g text-right">
                                <table class="tabelaVinculos pure-u-1">
                                    <h:panelGroup layout="block" styleClass="pure-g-r pure-u-1">
                                        <h:panelGroup id="botaoSelecionarDeselecionar" layout="block" styleClass="pure-u-1-2" style="width: 40%;text-align: left">
                                            <a4j:commandLink action="#{CarteirasControle.selecionarTodos}"
                                                             rendered="#{!CarteirasControle.marcarTodos}"
                                                             onclick="selecTodos();"
                                                             style="border-radius: 3px !important; border: 1px solid #B4B7BB;box-sizing: border-box; color: #B4B7BB; margin-top: 12px; background-color: transparent !important;"
                                                             value="Selecionar todos"
                                                             styleClass="pure-button tooltipster"
                                                             reRender="selecionarTodosCarteira,botaoSelecionarDeselecionar"
                                                             title="Clique para selecionar os itens de todas as páginas.">
                                                <a4j:actionparam noEscape="true" name="clientesSelecionados"
                                                                 value="retornaArrayCheckados()"/>
                                                <f:attribute name="filtro"
                                                             value="#{CarteirasControle.filtroOrganizarCarteira}"/>
                                            </a4j:commandLink>

                                            <a4j:commandLink action="#{CarteirasControle.selecionarTodos}"
                                                             onclick="resetSelecConta();"
                                                             rendered="#{CarteirasControle.marcarTodos}"
                                                             value="Deselecionar todos" styleClass="pure-button tooltipster"
                                                             style="border-radius: 3px !important; border: 1px solid #1998FC;;box-sizing: border-box; color: #FFFFFF; margin-top: 12px; background-color: #1998FC !important;"
                                                             reRender="selecionarTodosCarteira,botaoSelecionarDeselecionar"
                                                             title="Clique para deselecionar os itens de todas as páginas.">
                                                <a4j:actionparam noEscape="true" name="clientesSelecionados"
                                                                 value="retornaArrayCheckados()"/>
                                                <f:attribute name="filtro"
                                                             value="#{CarteirasControle.filtroOrganizarCarteira}"/>
                                            </a4j:commandLink>

                                            <h:outputText styleClass="texto"
                                                          style="font-size: 14px;color: #B4B7BB;font-weight: normal;margin-left: 10px;"
                                                          value="#{CarteirasControle.quantidadeClienteSelecionado}"/>
                                            <span class="texto"
                                                  style="font-size: 14px;color: #B4B7BB;font-weight: normal;"> alunos selecionados</span>
                                        </h:panelGroup>
                                        <h:panelGroup layout="block" styleClass="pure-u-1-2" style="width: 60%">
                                            <h:panelGroup styleClass="dataTables_filter" style="top: 5px;"></h:panelGroup>
                                            <a4j:commandLink id="linkOrg2"
                                                             style="margin-top: 10px"
                                                             action="#{CarteirasControle.abrirModalOrganizar}"
                                                             value="Organizar"
                                                             styleClass="pure-button pure-button-primary pull-right margin-h-6"
                                                             reRender="formMdlOrganizar"
                                                             oncomplete="#{CarteirasControle.msgAlert}">
                                                <a4j:actionparam noEscape="true" name="clientesSelecionados"
                                                                 value="retornaArrayCheckados()"/>
                                                <f:attribute name="filtro"
                                                             value="#{CarteirasControle.filtroOrganizarCarteira}"/>
                                            </a4j:commandLink>

                                            <a4j:commandLink id="btnExcel"
                                                             style="margin-top: 10px"
                                                             styleClass="pure-button pull-right margin-h-6"
                                                             actionListener="#{CarteirasControle.exportar}"
                                                             oncomplete="abrirPopup('UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/vnd.ms-excel','Transacoes', 800,200);#{ExportadorListaControle.msgAlert}"
                                                             accesskey="3"
                                                             value="Excel">
                                                <f:attribute name="tipo" value="xls"/>
                                                <f:attribute name="atributos"
                                                             value="matricula=Matrícula,nome=Cliente,nomeColaborador=Colaborador,situacao=Situação,dataCadastro=Fim Cont.,risco=Risco,diasFalta=Sem Acesso,categoria=Período"/>
                                                <f:attribute name="prefixo" value="Carteiras"/>
                                                <a4j:actionparam noEscape="true" name="paramsTableFiltrada"
                                                                 value="retornaDadosDT(true)"/>
                                            </a4j:commandLink>

                                            <a4j:commandLink id="btnPDF"
                                                             style="margin-top: 10px"
                                                             styleClass="pure-button pull-right margin-h-6"
                                                             actionListener="#{CarteirasControle.exportar}"
                                                             oncomplete="abrirPopup('UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/pdf','Transacoes', 800,200);#{ExportadorListaControle.msgAlert}"
                                                             accesskey="4"
                                                             value="PDF">
                                                <f:attribute name="tipo" value="pdf"/>
                                                <f:attribute name="atributos"
                                                             value="matricula=Matrícula,nome=Cliente,nomeColaborador=Colaborador,situacao=Situação,dataCadastro=Fim Cont.,risco=Risco,diasFalta=Sem Acesso,categoria=Período"/>
                                                <f:attribute name="prefixo" value="Carteiras"/>
                                                <a4j:actionparam noEscape="true" name="paramsTableFiltrada"
                                                                 value="retornaDadosDT(true)"/>
                                            </a4j:commandLink>
                                        </h:panelGroup>
                                    </h:panelGroup>
                                    <thead>
                                    <tr>
                                        <th>
                                            <h:panelGroup id="selecionarTodosCarteira" layout="block">
                                                <a4j:commandLink status="none"
                                                                 styleClass="botao-checkbox texto-size-18 tooltipster"
                                                                 onclick="selecTodosPagina();"
                                                                 rendered="#{!CarteirasControle.marcarTodosPagina}"
                                                                 reRender="selecionarTodosCarteira,botaoSelecionarDeselecionar"
                                                                 title="Clique para selecionar os itens desta página."
                                                                 action="#{CarteirasControle.selecionarTodosPagina}">
                                                    <a4j:actionparam noEscape="true" name="clientesSelecionados"
                                                                     value="retornaArrayCheckados()"/>
                                                    <h:outputText
                                                            styleClass="icon fa-icon-check-empty"/>
                                                </a4j:commandLink>
                                                <a4j:commandLink status="none"
                                                                 styleClass="botao-checkbox texto-size-18 tooltipster"
                                                                 onclick="resetSelecConta();"
                                                                 rendered="#{CarteirasControle.marcarTodosPagina}"
                                                                 reRender="selecionarTodosCarteira,botaoSelecionarDeselecionar"
                                                                 title="Clique para deselecionar os itens desta página."
                                                                 action="#{CarteirasControle.selecionarTodosPagina}">
                                                    <a4j:actionparam noEscape="true" name="clientesSelecionados"
                                                                     value="retornaArrayCheckados()"/>
                                                    <h:outputText
                                                            styleClass="icon fa-icon-check"/>
                                                </a4j:commandLink>
                                            </h:panelGroup>
                                        </th>
                                        <th>Cód Cliente</th>
                                        <th>Matrícula</th>
                                        <th>Cliente</th>
                                        <th>Colaborador</th>
                                        <th>Situação</th>
                                        <th id="fimContr">Fim Cont.</th>
                                        <rich:toolTip value="Data de término ajustada do contrato." for="fimContr"/>
                                        <th>Risco</th>
                                        <th>Sem Acesso</th>
                                        <th>Período</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    </tbody>
                                </table>
                            </h:panelGroup>
                        </h:panelGroup>
                    </h:panelGroup>
                </h:panelGroup>
            </h:panelGroup>
        </h:panelGroup>

        <h:panelGroup id="carteirasColaboradores" layout="block" styleClass="blocoCarteiras"
                      rendered="#{CarteirasControle.panelTodasCarteiras && !CarteirasControle.panelCarteiras}">
            <h:panelGroup style="margin: 17px 0px 0px 23px;" layout="block">
                <span class="texto">Resultado - Todas Carteiras </span>
            </h:panelGroup>
            <h:panelGroup layout="block"
                          style="max-height: 400px; overflow-y: scroll; margin-bottom: 39px; width: 100% ">
                <rich:dataTable value="#{CarteirasControle.filtroVerTodasCarteiras.infoCarteiraTOs}"
                                styleClass="tabelaSimplesCustom showCellEmpty "
                                style="text-align-last: start; box-sizing: border-box; border-collapse: collapse; border-bottom: 1px solid #DCDDDF;"
                                id="infoCarteiras"
                                var="infoCarteira" width="100%" columns="1" cellpadding="0">
                    <rich:column id="carteira" style="padding: 0 !important;">
                        <rich:simpleTogglePanel switchType="client" width="100%" style="border: none;margin-left: 0px;"
                                                bodyClass="bodyToogle" headerClass="headerToogle"
                                                opened="#{infoCarteira.aberto}">
                            <f:facet name="header">
                                <h:panelGroup layout="block">

                                    <h:panelGroup>
                                        <a4j:mediaOutput element="img"
                                                         style="left:0px;width:93px;height:93px;border-radius: 50%;font-size: 16px;vertical-align: middle;"
                                                         cacheable="false"
                                                         rendered="#{!SuperControle.fotosNaNuvem}"
                                                         createContent="#{infoCarteira.paintFoto}"
                                                         value="#{ImagemData}"
                                                         mimeType="image/jpeg">
                                            <f:param name="largura" value="43"/>
                                            <f:param name="altura" value="43"/>
                                        </a4j:mediaOutput>
                                        <h:graphicImage rendered="#{SuperControle.fotosNaNuvem}"
                                                        style="left:0px;width:53px;height:53px;border-radius: 50%;font-size: 16px;vertical-align: middle;"
                                                        url="#{infoCarteira.paintFotoDaNuvem}"/>

                                        <h:outputText styleClass="nomeCol texto"
                                                      style="color: #51555A;font-size: 18px; vertical-align: middle; margin-left:10px;display: inline-block; width: 25%"
                                                      value="#{infoCarteira.nomeColaborador}"
                                                      id="nome"/>
                                    </h:panelGroup>

                                    <h:panelGroup >
                                        <h:outputText styleClass="vinc texto"
                                                      style="color: #51555A;font-size: 18px; font-weight: normal; vertical-align: middle;margin-left:19%"
                                                      value="#{infoCarteira.qtdCarteiras} carteira(s)"/>
                                    </h:panelGroup>

                                    <h:panelGroup >
                                        <h:outputText styleClass="vinc texto"
                                                      style="color: #51555A;font-size: 18px; font-weight: normal; vertical-align: middle;margin-right:10%;line-height: 55px;"
                                                      value="#{infoCarteira.totalVinculos} cliente(s)"/>
                                    </h:panelGroup>
                                </h:panelGroup>
                            </f:facet>
                            <f:facet name="openMarker">
                                <h:panelGroup>
                                    <i class="fa-icon-chevron-down" style="font-size: 20px; text-align: center;"></i>
                                </h:panelGroup>
                            </f:facet>
                            <f:facet name="closeMarker">
                                <h:panelGroup>
                                    <i class="fa-icon-chevron-up" style="font-size: 20px; text-align: center;"></i>
                                </h:panelGroup>
                            </f:facet>

                            <h:panelGroup layout="block" id="tableToogleTable" style="padding: 0px !important;">
                                <rich:dataTable columns="2" value="#{infoCarteira.qtdVinculosColaborador}"
                                                styleClass="tabelaSimplesCustom showCellEmpty"
                                                style="box-sizing: border-box; border-collapse: collapse; border-bottom: 1px solid #DCDDDF;"
                                                var="tipoVinculoCarteira" width="100%"
                                                id="tipoVinculoCarteira"
                                                columnsWidth="25%" rowClasses="linhaClicka">
                                    <rich:column id="colunaQtdVinculo"
                                                 style="width: 100%;background: #F3F3F4;padding: 0px !important;height: 45px;border-top: none">
                                        <a4j:commandLink style="display: inline-block; width: 100%;"
                                                         action="#{CarteirasControle.prepararBuscarTodasCarteiras}"
                                                         oncomplete="#{CarteirasControle.mensagemNotificar};dispararDatatables(true)"
                                                         reRender="conteudoCRM, includesCRM, mensagens,carteirasColaboradores,totalRegistro,totalClientes">
                                            <h:outputText styleClass="texto" style="display: inline-block; width: 39%; margin-left: 20%" value="#{tipoVinculoCarteira.tipoVinculo_Apresentar}"/>
                                            <h:outputText styleClass="texto" style="display: inline-block;" value="#{tipoVinculoCarteira.qtdVinculos_Apresentar}"/>
                                            <f:attribute name="infoCarteira" value="#{infoCarteira}"/>
                                            <f:attribute name="tipoVinculoCarteira" value="#{tipoVinculoCarteira}"/>
                                        </a4j:commandLink>
                                    </rich:column>
                                </rich:dataTable>
                            </h:panelGroup>
                        </rich:simpleTogglePanel>
                    </rich:column>
                </rich:dataTable>
            </h:panelGroup>
        </h:panelGroup>

    </h:panelGroup>
</h:panelGroup>

<a4j:jsFunction name="carregarSmartBoxModal" action="#{CarteirasControle.abrirTelaClienteColaboradorCarteiras}"
                oncomplete="#{CarteirasControle.onCompleteDetalhes}">
    <a4j:actionparam noEscape="true" name="codCliente" value="retornaCodCliModal()"/>
</a4j:jsFunction>

<script type="text/javascript">
    jQuery(document).delegate("i#dtInicioTrigger", "click", function () {
        jQuery('.btDtInicio').trigger('click');
    });
    jQuery(document).delegate("i#dtFimTrigger", "click", function () {
        jQuery('.btDtFim').trigger('click');
    });

    jQuery(window).on("load", function () {
        dispararDatatables(true);
        jQuery(document).bind("click", ".linhaClicka", function () {
            jQuery(".linhaClicka").removeClass("linhaSelec");
            jQuery(this).addClass("linhaSelec");
        });
        setTimeout(function () {
            jQuery('.searchbox-icon').click();
            jQuery('.searchbox-input').focus();
        }, 1000);
    });

    function dispararDatatables(isPaginado) {
        selCount = 0;
        cadastrosCheckados = [];
        pagCheckadas = [];
        jQuery(document).undelegate(".alumarcado", "change");

        if (isPaginado) {
            iniciarTabelaPaginacao("tabelaVinculos", "${contexto}/prest/relatorio/carteiras", 3, "asc", "", true, true, true, true, true, [1], 'filtrar');
            return;
        }

        iniciarTabela("tabelaVinculos", "${contexto}/prest/relatorio/carteiras", 3, "asc", "", true, true, true, true, true, [1], 'filtrar');
    }
</script>

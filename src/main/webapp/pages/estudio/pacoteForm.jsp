<%@page contentType="text/html;charset=UTF-8" %>
<head><script type="text/javascript" language="javascript" src="../../script/script.js"></script></head>
<link href="../../css/otimize.css" rel="stylesheet" type="text/css">
<link href="../../beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<script type="text/javascript" language="javascript" src="hoverform.js"></script>
<script type="text/javascript" language="javascript" src="../../script/ce_script.js"></script>

<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<f:view>
    <jsp:include page="/includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText value="Pacote"/>
    </title>
    <c:set var="titulo" scope="session" value="Pacote"/>
    <f:facet name="header">
        <jsp:include page="../../topoReduzido_material.jsp"/>
    </f:facet>
    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <h:form id="form">

            <h:panelGrid columns="1"  width="100%">
                <h:panelGrid columns="1" columnClasses="colunaCentralizada" width="100%">
                    <!-- PACOTES -->
                    <h:panelGrid id="confPacotes" columns="1" width="100%">

                        <h:panelGrid id="panelPacotes"
                                     columns="2" headerClass="subordinado"
                                     rowClasses="linhaImpar, linhaPar" width="100%" columnClasses="classEsquerda, classDireita" >
                            <h:outputText value="Título:" />
                            <h:panelGroup>
                                <h:inputText  id="tituloPacote" size="15" maxlength="30" onblur="blurinput(this);"
                                              onfocus="focusinput(this);" styleClass="form"
                                              value="#{pacoteControle.pacoteVO.titulo}"/>
                            </h:panelGroup>
                            <h:outputText  value="Descrição:" />
                            <h:panelGroup>
                                <h:inputTextarea id="descricaoPacote" onblur="blurinput(this);"
                                                 onfocus="focusinput(this);"
                                                 cols="40" rows="4"
                                                 value="#{pacoteControle.pacoteVO.descricao}" />
                            </h:panelGroup>
                            <h:outputText value="Validade Inicial:" />
                            <h:panelGroup>

                                <rich:calendar  id="valInicialPacote" styleClass="form"
                                                locale="pt_BR"
                                                inputSize="10"
                                                inputClass="form"
                                                oninputblur="blurinput(this);"
                                                oninputfocus="focusinput(this);"
                                                oninputchange="return validar_Data(this.id);"
                                                oninputkeypress="return mascara(this, '99/99/9999', event); "
                                                oninputkeydown="return tabOnEnter(event, 'hInicial');"
                                                datePattern="dd/MM/yyyy"
                                                enableManualInput="true"
                                                zindex="99"
                                                showWeeksBar="false"
                                                popup="true"
                                                value="#{pacoteControle.pacoteVO.validadeInicial}"/>
                            </h:panelGroup>
                            <h:outputText  value="Validade Final:" />
                            <rich:calendar  id="valFinalPacote" styleClass="form"
                                            locale="pt_BR"
                                            inputSize="10"
                                            inputClass="form"
                                            oninputblur="blurinput(this);"
                                            oninputfocus="focusinput(this);"
                                            oninputchange="return validar_Data(this.id);"
                                            oninputkeypress="return mascara(this, '99/99/9999', event); "
                                            oninputkeydown="return tabOnEnter(event, 'hInicial');"
                                            datePattern="dd/MM/yyyy"
                                            enableManualInput="true"
                                            zindex="99"
                                            showWeeksBar="false"
                                            popup="true"
                                            value="#{pacoteControle.pacoteVO.validadeFinal}"/>

                            <h:outputText  value="#{msg_aplic.prt_Produto_tipoVigencia}" />
                            <h:panelGroup >
                                <h:selectOneMenu id="tipoVigencia"  onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form"  value="#{pacoteControle.pacoteVO.tipoVigencia}" >
                                    <a4j:support event="onchange"  focus="tipoVigencia" reRender="form"/>
                                    <f:selectItems  value="#{pacoteControle.listaSelectItemTipoVigenciaProduto}" />
                                </h:selectOneMenu>
                                <h:message for="tipoVigencia" styleClass="mensagemDetalhada"/>
                            </h:panelGroup>

                            <h:outputText rendered="#{pacoteControle.pacoteVO.tipoVigencia == 'PF'}" value="#{msg_aplic.prt_Produto_dataInicioVigencia}" />
                            <h:panelGroup rendered="#{pacoteControle.pacoteVO.tipoVigencia == 'PF'}">
                                <rich:calendar id="dataInicioVigencia"
                                               value="#{pacoteControle.pacoteVO.dataInicioVigencia}"
                                               inputSize="10"
                                               inputClass="form"
                                               oninputblur="blurinput(this);"
                                               oninputfocus="focusinput(this);"
                                               oninputchange="return validar_Data(this.id);"
                                               oninputkeypress="return mascara(this.form, this.id, '99/99/9999', event);"
                                               datePattern="dd/MM/yyyy"
                                               enableManualInput="true"
                                               zindex="2"
                                               showWeeksBar="false" />
                                <h:message for="dataInicioVigencia"  styleClass="mensagemDetalhada"/>
                            </h:panelGroup>

                            <h:outputText rendered="#{pacoteControle.pacoteVO.tipoVigencia == 'PF'}" value="#{msg_aplic.prt_Produto_dataFinalVigenciaFixa}" />
                            <h:panelGroup rendered="#{pacoteControle.pacoteVO.tipoVigencia == 'PF'}">
                                <rich:calendar id="dataFinalVigenciaFixa"
                                               value="#{pacoteControle.pacoteVO.dataFinalVigencia}"
                                               inputSize="10"
                                               inputClass="form"
                                               oninputblur="blurinput(this);"
                                               oninputfocus="focusinput(this);"
                                               oninputchange="return validar_Data(this.id);"
                                               oninputkeypress="return mascara(this.form, this.id, '99/99/9999', event);"
                                               datePattern="dd/MM/yyyy"
                                               enableManualInput="true"
                                               zindex="2"
                                               showWeeksBar="false" />
                                <h:message for="dataFinalVigenciaFixa"  styleClass="mensagemDetalhada"/>
                            </h:panelGroup>

                            <h:outputText rendered="#{pacoteControle.pacoteVO.tipoVigencia == 'ID'}" value="#{msg_aplic.prt_Produto_nrDiasVigencia}" />
                            <h:inputText rendered="#{pacoteControle.pacoteVO.tipoVigencia == 'ID'}"  id="nrDiasVigencia" onkeypress="return Tecla(event);"
                                         onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" size="10" maxlength="10"
                                         value="#{pacoteControle.pacoteVO.nrDiasVigencia}" />


                            <h:outputText  value="Pacote Genérico:" />
                            <h:selectBooleanCheckbox disabled="#{pacoteControle.naoPermiteEditar}" id="pacoteGenerico" onblur="blurinput(this);"
                                                     onfocus="focusinput(this);" styleClass="form"
                                                     value="#{pacoteControle.pacoteVO.pacoteGenerico}">
                                <a4j:support event="onclick" action="#{pacoteControle.apagarProdutoGenerico}" reRender="form"/>
                            </h:selectBooleanCheckbox>
                            <h:outputText value="Valor Total: " />
                            <h:inputText id="valorPacote" value="#{pacoteControle.pacoteVO.valorTotal}" disabled="#{!pacoteControle.pacoteVO.pacoteGenerico}">
                                <f:converter converterId="FormatadorNumerico" />
                            </h:inputText>

                            <h:outputText rendered="#{pacoteControle.pacoteVO.pacoteGenerico}" value="Qtd. de Produtos: " />
                            <rich:inputNumberSpinner id="qtdProdutos" rendered="#{pacoteControle.pacoteVO.pacoteGenerico}"
                                                     step="1"  value="#{pacoteControle.pacoteVO.qtdProdutosGenericos}"
                                                     enableManualInput="true"
                                                     oninputclick="return tabOnEnter(event, 'valorUnitarioProduto');"
                                                     oninputkeypress="return mascara(this, '999', event);"
                                                     onblur="blurinput(this);"
                                                     onfocus="focusinput(this);"
                                                     styleClass="form"/>

                            <h:outputText  value="Desativar:" />
                            <h:selectBooleanCheckbox id="desativarPacote" onblur="blurinput(this);"
                                                     onfocus="focusinput(this);" styleClass="form"
                                                     value="#{pacoteControle.pacoteVO.desativado}"/>
                            <h:outputText value="Imagem:" title="Imagem"/>
                            <h:panelGrid columns="2">
                                <%--             <a4j:mediaOutput element="img" id="imagemUploadPacote"  style="width:100px;height:100px;"
                                                              cacheable="false" session="true"
                                                              createContent="#{pacoteControle.paintFotoAdicionar}"
                                                              value="#{ImagemData}" mimeType="image/jpeg">
                                             </a4j:mediaOutput>

                                <a4j:mediaOutput  element="img" id="imagemUploadPacote" value="#{pacoteControle.pacoteVO}"  style="width:100px;height:100px;"
                                                  cacheable="false" createContent="#{pacoteControle.paintFotoAdicionadar}"
                                                  mimeType="image/jpeg">
                                </a4j:mediaOutput>
                                --%>

                                <h:panelGroup>

                                    <a4j:jsFunction name="postSave" action="#{pacoteControle.setarUrl}">
                                        <a4j:actionparam name="param1" assignTo="#{CapturaFotoControle.retorno}"/>
                                    </a4j:jsFunction>

                                    <a4j:mediaOutput rendered="#{!SuperControle.fotosNaNuvem}"
                                                     element="img" id="imagem1" style="width:150px;height:180px "
                                                     cacheable="false" session="false"
                                                     createContent="#{pacoteControle.paintFoto}"
                                                     value="#{ImagemData}" mimeType="image/jpeg">
                                        <f:param value="#{SuperControle.timeStamp}" name="time"/>
                                        <f:param name="largura" value="100"/>
                                        <f:param name="altura" value="100"/>
                                    </a4j:mediaOutput>

                                    <h:graphicImage id="imagePacote" rendered="#{SuperControle.fotosNaNuvem}" 
                                                    width="100"  height="100" style="width:100px;height:100px" 
                                                    url="#{pacoteControle.paintFotoDaNuvem}"/>

                                </h:panelGroup>

                                <a4j:jsFunction name="updateFoto" action="#{ClienteControle.recarregarFoto}" 
                                                oncomplete="location.reload();"/>

                                <a4j:commandButton actionListener="#{CapturaFotoControle.selecionarTipoDocumento}"
                                                   value="#{msg_bt.btn_capturarfoto}" image="../../imagens/picture.png"
                                                   oncomplete="initFlex('#{ClienteControle.key}', '#{ClienteControle.clienteVO.pessoa.codigo}', '#{ClienteControle.contextPath}', '#{pacoteControle.midiaEntidadeEnum}');Richfaces.showModalPanel('modalCapFoto');"
                                                   alt="#{msg_bt.btn_capturarfoto}" title="#{msg_bt.btn_capturarfoto}" styleClass="botoes">
                                    <f:attribute name="tipoDoc" value="#{pacoteControle.midiaEntidadeEnum}"/>
                                    <f:attribute name="pessoa" value="#{pacoteControle.pacoteVO.codigo}"/>
                                </a4j:commandButton>

                                <%--

                                <rich:fileUpload listHeight="0"
                                                 listWidth="0"
                                                 noDuplicate="false"
                                                 fileUploadListener="#{pacoteControle.upload}"
                                                 maxFilesQuantity="1"
                                                 addControlLabel="Procurar..."
                                                 cancelEntryControlLabel="Cancelar"
                                                 doneLabel="Pronto"
                                                 sizeErrorLabel="Arquivo não Enviado. Tamanho Máximo Permitido 100 MB"
                                                 progressLabel="Enviando"
                                                 stopControlLabel="Parar"
                                                 uploadControlLabel="Enviar"
                                                 transferErrorLabel="Falha de Transmissão"
                                                 stopEntryControlLabel="Parar"
                                                 id="upload"
                                                 immediateUpload="true"
                                                 autoclear="true"
                                                 acceptedTypes="png,gif,jpg,jpeg,ico,bmp,PNG,GIF,JPG,JPEG,ICO,BMP">

                                    <a4j:support event="onuploadcomplete" reRender="upload,imagemUploadPacote,panelMensagemErro" />
                                </rich:fileUpload>

                                --%>



                            </h:panelGrid>
                        </h:panelGrid>
                    </h:panelGrid>
                </h:panelGrid>

                <h:panelGrid columns="1" width="100%" headerClass="subordinado" columnClasses="colunaCentralizada" id="produtos">
                    <f:facet name="header">
                        <h:outputText value="Produtos"/>
                    </f:facet>
                    <h:panelGroup>
                        <h:panelGrid columns="1" width="100%" headerClass="subordinado"
                                     columnClasses="colunaCentralizada" >
                            <h:panelGrid columns="2" width="100%" id="dadosProduto" rowClasses="linhaImpar, linhaPar" columnClasses="classEsquerda, classDireita">
                                <h:outputText value="Produto:" />
                                <h:panelGroup>

                                    <h:selectOneMenu id="selectItemProduto" value="#{pacoteControle.pacoteProdutoVO.produtoVO}"
                                                     onkeydown="return tabOnEnter(event, 'spinnerQuantidadeProduto');"
                                                     onblur="blurinput(this);"
                                                     converter="produtoConverter"
                                                     onfocus="focusinput(this);"
                                                     styleClass="form">
                                        <f:selectItem itemLabel="Selecione um produto!" itemValue="0" />
                                        <f:selectItems value="#{pacoteControle.listaSelectProduto}" />
                                        <a4j:support event="onchange" action="#{pacoteControle.associarValorProdutoSelecionado}"
                                                     reRender="valorUnitarioProduto,panelMensagemErro"/>
                                    </h:selectOneMenu>
                                    <a4j:commandButton id="atualizar_produto" action="#{pacoteControle.getListaSelectProduto}"
                                                       image="../../imagens/atualizar.png" immediate="true" ajaxSingle="true" reRender="selectItemProduto"/>
                                </h:panelGroup>
                                <h:outputText rendered="#{!pacoteControle.pacoteVO.pacoteGenerico}" value="Quantidade:" />
                                <h:panelGroup rendered="#{!pacoteControle.pacoteVO.pacoteGenerico}">
                                    <rich:inputNumberSpinner id="spinnerQuantidadeProduto"
                                                             step="1"  value="#{pacoteControle.pacoteProdutoVO.quantidade}"
                                                             enableManualInput="true"
                                                             oninputclick="return tabOnEnter(event, 'valorUnitarioProduto');"
                                                             oninputkeypress="return mascara(this, '999', event);"
                                                             onblur="blurinput(this);"
                                                             onfocus="focusinput(this);"
                                                             styleClass="form"/>
                                    <h:message for="spinnerQuantidadeProduto"/>
                                </h:panelGroup>
                                <h:outputText rendered="#{!pacoteControle.pacoteVO.pacoteGenerico}" value="Valor Unitário:" />
                                <h:panelGroup>
                                    <h:inputText rendered="#{!pacoteControle.pacoteVO.pacoteGenerico}" value="#{pacoteControle.pacoteProdutoVO.valorUnitario}"
                                                 id="valorUnitarioProduto"
                                                 onkeypress="return formatar_moeda(this,'','.',event);"
                                                 onkeydown="return tabOnEnter(event, 'buttonConfimarPacoteProduto');"
                                                 onblur="blurinput(this);"
                                                 onfocus="focusinput(this);"
                                                 styleClass="form"/>
                                    <h:message for="valorUnitarioProduto"/>
                                </h:panelGroup>
                            </h:panelGrid>
                            <a4j:commandButton  rendered="#{!pacoteControle.pacoteVO.pacoteGenerico}" value="Confirmar" image= "../../imagens/botaoAdicionar.png" size="200" title="Confirmar"
                                                id="buttonConfimarPacoteProduto"
                                                action="#{pacoteControle.acaoConfirmarPacoteProduto}"
                                                reRender="listaPacoteProduto,panelMensagemErro,dadosProduto,valorPacote" />
                            <a4j:commandButton rendered="#{pacoteControle.pacoteVO.pacoteGenerico}" value="Confirmar" image= "../../imagens/botaoAdicionar.png" size="200" title="Confirmar"
                                               id="buttonConfimarPacProduto"
                                               action="#{pacoteControle.acaoConfirmarPacoteGenericoProduto}"
                                               reRender="listaPacoteProdutoGenerico,panelMensagemErro,dadosProduto,valorPacote" />
                            <h:panelGrid columns="1" width="100%" styleClass="tabFormSubordinada">
                                <h:dataTable id="listaPacoteProduto" rendered="#{!pacoteControle.pacoteVO.pacoteGenerico}" width="100%" headerClass="subordinado" styleClass="tabFormSubordinada"
                                             rowClasses="linhaImpar, linhaPar" columnClasses="colunaAlinhamento"
                                             value="#{pacoteControle.pacoteVO.listaPacoteProduto}" var="itemProduto">
                                    <h:column>
                                        <f:facet name="header">
                                            <h:outputText value="Produto" />
                                        </f:facet>
                                        <h:outputText value="#{itemProduto.produtoVO.descricao}" />
                                    </h:column>
                                    <h:column rendered="#{!pacoteControle.pacoteVO.pacoteGenerico}">
                                        <f:facet name="header">
                                            <h:outputText value="Quantidade" />
                                        </f:facet>
                                        <h:outputText value="#{itemProduto.quantidade}" />
                                    </h:column>
                                    <h:column rendered="#{!pacoteControle.pacoteVO.pacoteGenerico}">
                                        <f:facet name="header">
                                            <h:outputText value="Valor" />
                                        </f:facet>
                                        <h:outputText value="#{itemProduto.valorUnitario}" >
                                            <f:convertNumber maxFractionDigits="2" groupingUsed="true" maxIntegerDigits="14" type="currency" currencySymbol="#{pacoteControle.empresa.moeda} " />
                                        </h:outputText>
                                    </h:column>
                                    <h:column>
                                        <f:facet name="header">
                                            <h:outputText  value="#{msg_bt.btn_opcoes}" />
                                        </f:facet>
                                        <h:panelGroup>
                                            <a4j:commandButton id="editarProduto"  value="#{msg_bt.btn_editar}" action="#{pacoteControle.editarPacoteProduto}"
                                                               image="../../imagens/botaoEditar.png" accesskey="6" styleClass="botoes" reRender="produtos,panelMensagemErro,valorPacote"/>
                                            <a4j:commandButton id="removerProduto" value="#{msg_bt.btn_excluir}" action="#{pacoteControle.excluirPacoteProduto}"
                                                               image="../../imagens/botaoRemover.png" accesskey="7" styleClass="botoes" reRender="produtos,panelMensagemErro,valorPacote"/>
                                        </h:panelGroup>
                                    </h:column>
                                </h:dataTable>
                                <h:dataTable id="listaPacoteProdutoGenerico" rendered="#{pacoteControle.pacoteVO.pacoteGenerico}" width="100%" headerClass="subordinado" styleClass="tabFormSubordinada"
                                             rowClasses="linhaImpar, linhaPar" columnClasses="colunaAlinhamento"
                                             value="#{pacoteControle.pacoteVO.listaPacoteProdutoGenerico}" var="itemProduto">
                                    <h:column>
                                        <f:facet name="header">
                                            <h:outputText value="Produto" />
                                        </f:facet>
                                        <h:outputText value="#{itemProduto.produtoVO.descricao}" />
                                    </h:column>
                                    <h:column rendered="#{!pacoteControle.pacoteVO.pacoteGenerico}">
                                        <f:facet name="header">
                                            <h:outputText value="Quantidade" />
                                        </f:facet>
                                        <h:outputText value="#{itemProduto.quantidade}" />
                                    </h:column>
                                    <h:column rendered="#{!pacoteControle.pacoteVO.pacoteGenerico}">
                                        <f:facet name="header">
                                            <h:outputText value="Valor" />
                                        </f:facet>
                                        <h:outputText value="#{itemProduto.valorUnitario}" >
                                            <f:convertNumber maxFractionDigits="2" groupingUsed="true" maxIntegerDigits="14" type="currency" currencySymbol="#{pacoteControle.empresa.moeda} " />
                                        </h:outputText>
                                    </h:column>
                                    <h:column>
                                        <f:facet name="header">
                                            <h:outputText  value="#{msg_bt.btn_opcoes}" />
                                        </f:facet>
                                        <h:panelGroup>
                                            <a4j:commandButton id="editarProduto"  value="#{msg_bt.btn_editar}" action="#{pacoteControle.editarPacoteProdutoGenerico}"
                                                               image="../../imagens/botaoEditar.png" accesskey="6" styleClass="botoes" reRender="produtos,panelMensagemErro,valorPacote"/>
                                            <a4j:commandButton id="removerProduto" value="#{msg_bt.btn_excluir}" action="#{pacoteControle.excluirPacoteProdutoGenerico}"
                                                               image="../../imagens/botaoRemover.png" accesskey="7" styleClass="botoes" reRender="produtos,panelMensagemErro,valorPacote"/>
                                        </h:panelGroup>
                                    </h:column>
                                </h:dataTable>
                            </h:panelGrid>
                        </h:panelGrid>
                    </h:panelGroup>
                </h:panelGrid>

                <h:panelGrid columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid id="panelMensagemErro" columns="3" width="100%" styleClass="tabMensagens">
                        <h:panelGrid columns="1" width="100%">

                            <h:outputText value=" "/>

                        </h:panelGrid>
                        <h:commandButton  rendered="#{pacoteControle.sucesso}" image="../../imagens/sucesso.png"/>
                        <h:commandButton rendered="#{pacoteControle.erro}" image="../../imagens/erro.png"/>
                        <h:panelGrid columns="1" width="100%">
                            <h:outputText id="msgPacote" styleClass="mensagem"  value="#{pacoteControle.mensagem}"/>
                            <h:outputText id="msgPacoteDet" styleClass="mensagemDetalhada" value="#{pacoteControle.mensagemDetalhada}"/>
                        </h:panelGrid>
                    </h:panelGrid>
                    <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                        <h:panelGroup>
                            <a4j:commandButton
                                styleClass="botoes nvoBt btSec"
                                id="novoButtonPacote" reRender="form"
                                action="#{pacoteControle.acaoNovoPacote}"
                                value="Novo"
                                title="Novo" >
                            </a4j:commandButton>
                            <h:outputText value="      "/>
                            <a4j:commandButton
                                action="#{pacoteControle.acaoAdicionarPacote}"
                                styleClass="botoes nvoBt"
                                id="addButtonPacote"
                                value="Adicionar" reRender="form"
                                title="Adicionar">
                            </a4j:commandButton>

                            <h:outputText value="    "/>

                            <h:panelGroup id="grupoBtnExcluir">
                                <a4j:commandButton id="excluir"  reRender="mdlMensagemGenerica"
                                                   oncomplete="#{pacoteControle.msgAlert}" action="#{pacoteControle.confirmarExcluir}"
                                                   value="#{msg_bt.btn_excluir}" title="#{msg.msg_excluir_dados}" accesskey="3" styleClass="botoes nvoBt btSec"/>
                            </h:panelGroup>



                            <h:outputText value="    "/>

                            <h:commandButton id="consultar" immediate="true" action="#{pacoteControle.consultar}"
                                             value="#{msg_bt.btn_voltar_lista}" alt="#{msg.msg_consultar_dados}"
                                             accesskey="4" styleClass="botoes nvoBt btSec"/>
                        </h:panelGroup>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
        </h:form>
    </h:panelGrid>
    <jsp:include page="../../includes/cliente/include_modal_capfoto.jsp" flush="true"/>
    <jsp:include page="../../includes/include_modal_mensagem_generica.jsp"/>
</f:view>

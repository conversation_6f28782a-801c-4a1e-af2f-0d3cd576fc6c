<%-- 
    Document   : pacote
    Created on : Apr 18, 2012, 10:21:39 AM
    Author     : <PERSON> GeoInova <PERSON>
--%>

<%@page contentType="text/html" pageEncoding="ISO-8859-1"%>
<%@include file="includes/include_imports.jsp" %>
<c:set var="moduloSession" value="1" scope="session" />


<html>
    <head>
        <script type="text/javascript" src="${contexto}/script/basico.js"></script>
        <link href="${contexto}/css/otimize.css" rel="stylesheet" type="text/css">
        <link href="${contexto}/css/estudio.css" rel="stylesheet" type="text/css">
        <link href="${contexto}/css/otimizeCRM.css" rel="stylesheet" type="text/css">
        <jsp:include page="includes/include_head.jsp" />

        <style type="text/css">
            TABLE.panelGridClass  {
                position: relative;
                vertical-align: top;
            }
            TABLE.panelGridClass td {
                position: absolute; 
            }

            .column1 {
                vertical-align: top;
            }

            .rich-extdt-maindiv {
                height: auto !important;
            }

            .rich-extdt-maindiv, .extdt-innerdiv {
                position: relative !important;
            }

            .extdt-outerdiv {
                height: auto !important;
                overflow: visible !important;
            }

            .extdt-content {
                height: auto !important;
                min-height: 10px;
            }
            .pacote {
                width: 48%;
                float: left;
                padding-right: 5px;
                padding-bottom: 20px;
            }

            .texto {
                text-align: justify;
            }

        </style>
    </head>
    <body>
        <f:view>
            <jsp:include page="/includes/include_carregando_ripple.jsp" flush="true"/>
        <title>
            <h:outputText value="Consulta de Disponibilidade"/>
        </title>
        <h:form id="agendaGeral" prependId="false">

            <h:panelGroup layout="block" style="display: inline-table;width: 100%;" styleClass="fundoCinza">
                <h:panelGroup layout="block" styleClass="bgtop topoZW" rendered="#{MenuControle.apresentarTopo}">
                    <jsp:include page="../../include_topo_novo.jsp" flush="true"/>
                    <jsp:include page="../../include_menu_zw_flat.jsp" flush="true"/>
                    <rich:jQuery selector=".item6" query="addClass('menuItemAtual')"/>
                </h:panelGroup>

                <h:panelGroup layout="block" styleClass="caixaCorpo">
                    <h:panelGroup layout="block" style="height: 80%;width: 100%">
                        <h:panelGroup layout="block" styleClass="caixaMenuLatel">
                            <h:panelGroup layout="block" styleClass="container-imagem container-conteudo-central">
                                <h:panelGroup layout="block" styleClass="container-box zw_ui especial ">
                                    <h:panelGroup styleClass="container-box-header" layout="block">
                                        <h:panelGroup layout="block" styleClass="margin-box">
                                            <h:outputText value="Pacote" styleClass="container-header-titulo"/>
                                            <h:outputLink styleClass="linkWiki"
                                                          value="#{SuperControle.urlBaseConhecimento}como-cadastrar-pacote-para-o-modulo-agenda-studio/"
                                                          title="Pacote"
                                                          target="_blank" >
                                                <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                                            </h:outputLink>
                                        </h:panelGroup>
                                    </h:panelGroup>
                                    <h:panelGroup layout="block" styleClass="margin-box" style="display: table">
                                        <a4j:repeat value="#{pacoteControle.listaPacote}" var="item" rowKeyVar="idx">

                                            <div class="pacote">
                                                <table width="100%" height="100%" align="center" border="0" cellpadding="1" cellspacing="1">
                                                    <tr>
                                                        <td style="width:100px;">
                                                            <h:graphicImage id="imagePacote" rendered="#{SuperControle.fotosNaNuvem}" width="100"  height="100" style="width:100px;height:100px" url="#{pacoteControle.paintFotoDaNuvemLista}"/>
                                                        </td>
                                                        <td align="left" valign="top" width="390px;">
                                                            <table width="100%" height="100%" align="center" border="0" cellpadding="1" cellspacing="0">
                                                                <tr>
                                                                    <td align="left" valign="top">
                                                                        <h:outputText style="font-weight:bold;font-size:13px" value="#{item.titulo}" styleClass="texto"/>
                                                                    </td>
                                                                </tr>
                                                                <tr>

                                                                    <td align="left" valign="top" style="text-align:justify; padding-right: 10px;">
                                                                        <h:outputText style="font-size:12;" value="#{item.descricao}" />
                                                                    </td>
                                                                </tr>
                                                                <tr>
                                                                    <td align="left" valign="top">
                                                                        <h:outputText rendered="#{item.pacoteGenerico}">
                                                                            <h:outputText styleClass="texto" value="Produtos Permitidos: <br/>" escape="false"/>
                                                                            <a4j:repeat value="#{item.listaPacoteProdutoGenerico}" var="itemProdGen" rowKeyVar="idx">
                                                                                <h:outputText styleClass="texto" style="font-size:12;"
                                                                                              value="- #{itemProdGen.produtoVO.descricao} <br/>" escape="false" />
                                                                            </a4j:repeat>
                                                                        </h:outputText>
                                                                    </td>
                                                                </tr>
                                                            </table>
                                                        </td>

                                                        <td align="left">
                                                            <rich:dataTable
                                                                    value="#{item.listaPacoteProduto}"
                                                                    var="itemProduto"
                                                                    width="100px">

                                                                <rich:column width="30px">
                                                                    <f:facet name="header">
                                                                        <h:outputText value="Qtd"/>
                                                                    </f:facet>
                                                                    <h:outputText value="#{itemProduto.quantidade}"/>
                                                                </rich:column>
                                                                <rich:column>
                                                                    <f:facet name="header">
                                                                        <h:outputText value="Produto"/>
                                                                    </f:facet>
                                                                    <h:outputText value="#{itemProduto.produtoVO.descricao}"/>
                                                                </rich:column>
                                                            </rich:dataTable>
                                                            <h:outputText value="#{item.valorTotal_Apresentar}"/>
                                                            <br>
                                                            <br>
                                                            <h:outputText rendered="#{!item.acaoValidade}"
                                                                          style="font-size:12;color:red;"
                                                                          value="Pacote expirado ou ainda não válido na data atual." />
                                                            <a4j:commandButton
                                                                    rendered="#{item.acaoValidade}"
                                                                    image="#{context}/imagens/estudio/comprar.png"
                                                                    value="Comprar"
                                                                    id="add" action="#{pacoteControle.acaoComprar}">
                                                                <f:setPropertyActionListener target="#{pacoteControle.pacoteVO}" value="#{item}"/>
                                                            </a4j:commandButton>
                                                        </td>
                                                    </tr>
                                                </table>
                                            </div>
                                        </a4j:repeat>
                                    </h:panelGroup>
                                </h:panelGroup>
                                <a4j:commandButton style="visibility: hidden;" reRender="panelExpiracaoSenha"
                                                   id="btnAtualizaPagina">
                                </a4j:commandButton>
                            </h:panelGroup>
                            <jsp:include page="includes/include_box_menulateral_sc.jsp" flush="true"/>
                        </h:panelGroup>
                    </h:panelGroup>
                </h:panelGroup>

                <jsp:include page="../../include_rodape_flat.jsp" flush="true"/>
            </h:panelGroup>
            <script>
                window.addEventListener('load', (event) =>{
                    montaModulomenu();
                });
            </script>
        </body>
    </html>
</h:form>
</f:view>



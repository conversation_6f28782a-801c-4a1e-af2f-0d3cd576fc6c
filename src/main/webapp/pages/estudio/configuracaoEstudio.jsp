<%@page contentType="text/html" pageEncoding="UTF-8"%>
<%@include file="includes/include_imports.jsp" %>


<html>
    <head>
        <script type="text/javascript" src="${contexto}/script/basico.js"></script>
        <script type="text/javascript" src="${contexto}/script/script2.js"></script>
        <link href="${contexto}/css/otimize.css" rel="stylesheet" type="text/css">
        <link href="${contexto}/css/estudio.css" rel="stylesheet" type="text/css">
        <link href="${contexto}/css/otimizeCRM.css" rel="stylesheet" type="text/css">
        <link href="${contexto}/beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
        <jsp:include page="includes/include_head.jsp" />
        <style>
            .rich-color-picker-wrapper {
                z-index: 99;
            }
            .menuBar {
                width: 25%;
            }
            .conteudo {
                width: 74%;
            }
            .wrapper{
                background-color: #fff !important;
            }
        </style>
        <script>
            function moedaEstudio(z){
                v = z.value;
                v=v.replace(/\D/g,"");  //permite digitar apenas números
                v=v.replace(/[0-9]{12}/,"inválido");   //limita pra máximo 999.999.999.999,99
                v=v.replace(/(\d{1})(\d{1,2})$/,"$1.$2");	//coloca ponto antes dos últimos 2 digitos
                z.value = v;
            }
        </script>
    </head>
    <body>
        <f:view>
            <jsp:include page="/includes/include_carregando_ripple.jsp" flush="true"/>
        <title>
            <h:outputText value="#{msg_aplic.prt_ConfiguracaoSistema_tituloForm}"/>
        </title>
        <c:set var="titulo" scope="session" value="${msg_aplic.prt_ConfiguracaoSistema_tituloForm}"/>
        <c:set var="urlWiki" scope="session" value="${SuperControle.urlBaseConhecimento}como-configurar-o-modulo-agenda-studio/"/>
        <f:facet name="header">
            <jsp:include page="../../topoReduzido_material.jsp"/>
        </f:facet>
        <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">

            <h:form id="form" prependId="false">
                <h:panelGrid columns="1" width="100%" style="vertical-align:top;">
                    <h:panelGrid columns="2" style="vertical-align:top;" columnClasses="menuBar, conteudo">
                        <h:panelGrid columns="1" width="100%" style="padding-right:20px; vertical-align:top;" >
                            <rich:panelMenu  style="vertical-align:top;" width="100%"
                                             topGroupClass="tabitemtitulomenu"  hoveredGroupClass="tabitemtitulomenuhovered"
                                             topItemClass="tabitemmenu"   hoveredItemClass="tabitemmenuhovered" expandSingle="false"
                                             mode="ajax">
                                <rich:panelMenuItem label="Empresa" style="font-size:14px;" action="#{configuracaoEstudioControle.menuEmpresa}" reRender="form"/>
                                <rich:panelMenuItem label="Pacote" style="font-size:14px;"  oncomplete="abrirPopup('pacoteCons.jsp', 'Pacote', 780, 595);" action="#{pacoteControle.abrirJaConsultado}"/>
                                <rich:panelMenuItem label="Relação Produto-Ambiente" style="font-size:14px;" action="#{configuracaoEstudioControle.menuRelProdutoAmbiente}" reRender="form"/>
                                <rich:panelMenuItem label="Relação Produto-Colaborador" style="font-size:14px;" action="#{configuracaoEstudioControle.menuRelProdutoColaborador}" reRender="form"/>
                                <rich:panelMenuItem label="Tipo de Horário" style="font-size:14px;" action="#{configuracaoEstudioControle.menuTipoHorario}" reRender="form" id="tipoHorarioConfigStudio"/>
                            </rich:panelMenu>
                        </h:panelGrid>

                        <!-- RELAÇÃO PRODUTO AMBIENTE -->
                        <h:panelGrid id="relacaoProdutoAmbiente" rendered="#{configuracaoEstudioControle.panelRelProdutoAmbiente}"
                                     columns="1"
                                     rowClasses="linhaImpar, linhaPar"
                                     width="100%">

                            <h:outputLabel
                                value="Categoria:" styleClass="texto_disponibilidade"/>
                            <h:selectOneMenu id="relacaoProdutoAmbiente-categoria"
                                             style="width:200px"
                                             converter="categoriaProdutoConverter"
                                             value="#{configuracaoEstudioControle.categoriaProdutoVO}"
                                             title="Categoria"
                                             disabled="#{configuracaoEstudioControle.relProdutoAmbienteVO.produtoVO.codigo > 0}"
                                             onkeydown="return tabOnEnter(event, 'relacaoProdutoAmbiente-ambiente');"
                                             onblur="blurinput(this);"
                                             onfocus="focusinput(this);"
                                             styleClass="form">
                                <f:selectItem itemLabel="Selecione uma categoria!" />
                                <f:selectItems value="#{configuracaoEstudioControle.listaSelectCategoriaProduto}"  />
                                <a4j:support event="onchange" action="#{configuracaoEstudioControle.acaoSelectCategoria}" reRender="relacaoProdutoAmbiente-produto,panelmensagem"/>
                            </h:selectOneMenu >

                            <h:outputLabel
                                value="Produto:" styleClass="texto_disponibilidade"/>
                            <h:selectOneMenu id="relacaoProdutoAmbiente-produto"
                                             style="width:200px"
                                             converter="produtoConverter"
                                             disabled="#{configuracaoEstudioControle.categoriaProdutoVO.codigo > 0}"
                                             value="#{configuracaoEstudioControle.relProdutoAmbienteVO.produtoVO}"
                                             title="Serviço"
                                             onkeydown="return tabOnEnter(event, 'relacaoProdutoAmbiente-ambiente');"
                                             onblur="blurinput(this);"
                                             onfocus="focusinput(this);"
                                             styleClass="form">
                                <f:selectItem itemLabel="Selecione um produto!" />
                                <f:selectItems value="#{configuracaoEstudioControle.listaSelectProduto}"  />
                                <a4j:support event="onchange" action="#{configuracaoEstudioControle.acaoSelectProduto}" reRender="relacaoProdutoAmbiente-categoria,panelmensagem" />
                            </h:selectOneMenu >


                            <h:outputLabel
                                value="Ambiente:" styleClass="texto_disponibilidade"/>
                            <h:selectOneMenu id="relacaoProdutoAmbiente-ambiente"
                                             style="width:200px"
                                             converter="ambienteConverter"
                                             value="#{configuracaoEstudioControle.relProdutoAmbienteVO.ambienteVO}"
                                             title="Ambiente"
                                             onkeydown="return tabOnEnter(event, 'addProduto');"
                                             onblur="blurinput(this);"
                                             onfocus="focusinput(this);"
                                             styleClass="form">
                                <f:selectItem itemLabel="Selecione um ambiente!" />
                                <f:selectItems value="#{configuracaoEstudioControle.listaSelectAmbiente}"  />
                                <a4j:support event="onchange" action="#{configuracaoEstudioControle.acaoSelectColaborador}" reRender="panelmensagem" />
                            </h:selectOneMenu >
                            <h:panelGroup>
                                <a4j:commandButton
                                    styleClass="botoes nvoBt btSec"
                                    value="Adicionar"
                                    id="addAmbiente"
                                    focus="relacaoProdutoAmbiente-categoria"
                                    reRender="relacaoProdutoAmbiente,modalPanelErro,panelmensagem"
                                    action="#{configuracaoEstudioControle.acaoAdicionaRelProdutoAmbiente}"
                                    title="Adiciona a relação informada na tabela"/>
                                <rich:spacer height="5" width="200"/>
                                <a4j:commandButton
                                    styleClass="botoes nvoBt btSec"
                                    value="Ver Detalhes" action="#{configuracaoEstudioControle.listarProdutosAmbientes}"
                                    id="verDetalhes" oncomplete="abrirPopup('listaProdutoAmbienteCategoria.jsp', 'ListaInteira', 800, 650);"
                                    title="Ver a lista inteira"/>
                            </h:panelGroup>
                            <h:panelGrid id="tabelaAmbienteServico" columns="1">
                                <rich:dataTable id="configuracaoEstudioControle-listaServicoAmbiente" var="item" style="height: 132px" width="630px" rows="10" value="#{configuracaoEstudioControle.listaRelProdutoAmbienteVO}">
                                    <rich:column sortBy="#{item.ambienteVO.codigo}" filterEvent="onkeyup" label="Código Ambiente"
                                            width="120px">
                                        <f:facet name="header">
                                            <h:outputText value="Código Ambiente" styleClass="texto_disponibilidade" />
                                        </f:facet>
                                        <h:outputText
                                            value="#{item.ambienteVO.codigo}"
                                            style="float:right; margin-right: 5px;"/>
                                    </rich:column>
                                    <rich:column  sortBy="#{item.ambienteVO.descricao}" filterEvent="onkeyup" label="Descrição Ambiente"
                                                  width="180px">
                                        <f:facet name="header">
                                            <h:outputText value="Descrição Ambiente" styleClass="texto_disponibilidade" />
                                        </f:facet>
                                        <h:outputText
                                            value="#{item.ambienteVO.descricao}"
                                            style="margin-left: 5px; position:relative;"/>
                                    </rich:column>
                                    <rich:column sortExpression="#{item.produtoVO.codigo}"
                                                 width="100px">
                                        <f:facet name="header">
                                            <h:outputText value="Código Produto" styleClass="texto_disponibilidade" />
                                        </f:facet>
                                        <h:outputText
                                            value="#{item.produtoVO.codigo}"
                                            style="float:right; margin-right: 5px;"/>
                                    </rich:column>
                                    <rich:column sortExpression="#{item.produtoVO.descricao}"
                                                 width="200px">
                                        <f:facet name="header">
                                            <h:outputText value="Descrição Produto" styleClass="texto_disponibilidade" />
                                        </f:facet>
                                        <h:outputText
                                            value="#{item.produtoVO.descricao}"
                                            style="margin-left: 5px; position:relative;"/>
                                    </rich:column>
                                    <rich:column width="50px">
                                        <a4j:commandLink action="#{configuracaoEstudioControle.acaoRemoverRelProdutoAmbiente}"
                                                         reRender="tabelaAmbienteServico,modalPanelErro,panelmensagem">
                                            <h:graphicImage value="../../imagens/estudio/icon_delete.png" style="cursor:pointer" id="rmvItem" />
                                            <f:setPropertyActionListener target="#{configuracaoEstudioControle.relProdutoAmbienteVO}" value="#{item}"/>
                                        </a4j:commandLink>
                                    </rich:column>
                                </rich:dataTable>
                                <rich:datascroller align="center" for="form:configuracaoEstudioControle-listaServicoAmbiente" maxPages="10" id="scrlListaServicoAmbiente"/>
                            </h:panelGrid>
                            <br>
                            <a4j:commandButton reRender="tabelaAmbienteServico,modalPanelErro,panelmensagem"
                                value="Configurar Todos Automaticamente"
                                id="configurarTodos" action="#{configuracaoEstudioControle.configurarTodosRelAmbProdutoAutomaticamente}" />
                            
                        </h:panelGrid>

                        <!-- RELAÇÃO PRODUTO COLABORADOR -->
                        <h:panelGrid id="relacaoProdutoColaborador" rendered="#{configuracaoEstudioControle.panelRelProdutoColaborador}"
                                     columns="1"
                                     rowClasses="linhaImpar, linhaPar"
                                     width="100%">

                            <h:outputLabel
                                value="Categoria:" styleClass="texto_disponibilidade"/>
                            <h:selectOneMenu id="relacaoProdutoColaborador-categoria"
                                             style="width:200px"
                                             converter="categoriaProdutoConverter"
                                             value="#{configuracaoEstudioControle.categoriaProdutoVO}"
                                             title="Categoria"
                                             disabled="#{configuracaoEstudioControle.relProdutoColaboradorVO.produtoVO.codigo > 0}"
                                             onkeydown="return tabOnEnter(event, 'relacaoProdutoColaborador-colaborador');"
                                             onblur="blurinput(this);"
                                             onfocus="focusinput(this);"
                                             styleClass="form">
                                <f:selectItem itemLabel="Selecione uma categoria!" />
                                <f:selectItems value="#{configuracaoEstudioControle.listaSelectCategoriaProduto}"  />
                                <a4j:support event="onchange" action="#{configuracaoEstudioControle.acaoSelectCategoria}" reRender="relacaoProdutoColaborador-produto,panelmensagem"/>
                            </h:selectOneMenu >

                            <h:outputLabel
                                value="Produto:" styleClass="texto_disponibilidade"/>
                            <h:selectOneMenu id="relacaoProdutoColaborador-produto"
                                             style="width:200px"
                                             converter="produtoConverter"
                                             disabled="#{configuracaoEstudioControle.categoriaProdutoVO.codigo > 0}"
                                             value="#{configuracaoEstudioControle.relProdutoColaboradorVO.produtoVO}"
                                             title="Serviço"
                                             onkeydown="return tabOnEnter(event, 'relacaoProdutoColaborador-colaborador');"
                                             onblur="blurinput(this);"
                                             onfocus="focusinput(this);"
                                             styleClass="form">
                                <f:selectItem itemLabel="Selecione um produto!" />
                                <f:selectItems value="#{configuracaoEstudioControle.listaSelectProduto}"  />
                                <a4j:support event="onchange" action="#{configuracaoEstudioControle.acaoSelectProduto}" reRender="relacaoProdutoColaborador-categoria,panelmensagem" />
                            </h:selectOneMenu >


                            <h:outputLabel
                                value="Colaborador:" styleClass="texto_disponibilidade"/>
                            <h:selectOneMenu id="relacaoProdutoColaborador-colaborador"
                                             style="width:200px"
                                             converter="colaboradorConverter"
                                             value="#{configuracaoEstudioControle.relProdutoColaboradorVO.colaboradorVO}"
                                             title="Colaborador:"
                                             onkeydown="return tabOnEnter(event, 'relacaoProdutoColaborador-porcComissao');"
                                             onblur="blurinput(this);"
                                             onfocus="focusinput(this);"
                                             styleClass="form">
                                <f:selectItem itemLabel="Selecione um colaborador!" />
                                <f:selectItems value="#{configuracaoEstudioControle.listaSelectColaborador}"  />
                                <a4j:support event="onchange" action="#{configuracaoEstudioControle.acaoSelectColaborador}" reRender="panelmensagem" />
                            </h:selectOneMenu >

                            <h:outputText  styleClass="texto_disponibilidade" value="Porcentagem de Comissão:" />
                            <h:inputText  id="relacaoProdutoColaborador-porcComissao" size="15" maxlength="30" onblur="blurinput(this);"
                                          onfocus="focusinput(this);" styleClass="form"
                                          onkeyup="return moedaEstudio(this);"
                                          onkeydown="return tabOnEnter(event, 'addProduto');"
                                          value="#{configuracaoEstudioControle.relProdutoColaboradorVO.porcComissao}"/>

                            <h:panelGroup>
                                <a4j:commandButton
                                    styleClass="botoes nvoBt btSec"
                                    value="Adicionar"
                                    id="addProduto"
                                    focus="panelFiltroServico-servico-codigo"
                                    reRender="relacaoProdutoColaborador, configuracaoEstudioControle-listaServico,
                                    relacaoProdutoColaborador-colaborador,
                                    relacaoProdutoColaborador-produto,
                                    relacaoProdutoColaborador-categoria,
                                    relacaoProdutoColaborador-porcComissao,
                                    modalPanelErro"
                                    action="#{configuracaoEstudioControle.acaoAdicionaRelProdutoColaborador}"
                                    title="Adiciona a relação informada na tabela."/>
                                <rich:spacer height="5" width="200"/>
                                <a4j:commandButton
                                    value="Ver Detalhes"
                                    styleClass="botoes nvoBt btSec"
                                    id="verDetalhesColaborador" oncomplete="abrirPopup('listaColaboradorProdutoCategoria.jsp', 'ListaInteira', 800, 650);"
                                    title="Ver a lista inteira"/>
                            </h:panelGroup>
                            <h:panelGrid columns="1">
                                <rich:dataTable id="configuracaoEstudioControle-listaServico" var="item" style="height: 132px" width="630px" rows="10" value="#{configuracaoEstudioControle.listaRelProdutoColaboradorVO}">

                                    <rich:column sortExpression="#{item.produtoVO.codigo}"
                                                 width="100px">
                                        <f:facet name="header">
                                            <h:outputText value="Código Produto" styleClass="texto_disponibilidade" />
                                        </f:facet>
                                        <h:outputText
                                            value="#{item.produtoVO.codigo}"
                                            style="float:right; margin-right: 5px;"/>
                                    </rich:column>
                                    <rich:column sortExpression="#{item.produtoVO.descricao}"
                                                 width="125px">
                                        <f:facet name="header">
                                            <h:outputText value="Descrição Produto" styleClass="texto_disponibilidade" />
                                        </f:facet>
                                        <h:outputText
                                            value="#{item.produtoVO.descricao}"
                                            style="margin-left: 5px; position:relative;"/>
                                    </rich:column>

                                    <rich:column sortExpression="#{item.colaboradorVO.pessoa.nome}"
                                                 width="270px">
                                        <f:facet name="header">
                                            <h:outputText value="Colaborador Nome" styleClass="texto_disponibilidade" />
                                        </f:facet>
                                        <h:outputText
                                            value="#{item.colaboradorVO.pessoa.nome}"
                                            style="margin-left: 5px; position:relative;"/>
                                    </rich:column>
                                    <rich:column width="80px" sortExpression="#{item.porcComissao}">
                                        <f:facet name="header" >
                                            <h:outputText value="Comissão" styleClass="texto_disponibilidade"/>
                                        </f:facet>
                                        <h:outputText value="#{item.porcComissao}" style="float:right; margin-right: 5px;">
                                            <f:convertNumber maxFractionDigits="2" groupingUsed="true" maxIntegerDigits="14" type="currency" currencySymbol="% " />
                                        </h:outputText>
                                    </rich:column>
                                    <rich:column width="20px">
                                        <a4j:commandLink id="acaoRemover"
                                                         action="#{configuracaoEstudioControle.acaoRemoverRelProdutoColaborador}"
                                                         reRender="relacaoProdutoColaborador, configuracaoEstudioControle-listaServico,modalPanelErro,panelmensagem">
                                            <rich:toolTip value="Remover" for="acaoRemover" direction="bottom-left"/>
                                            <h:graphicImage value="../../imagens/estudio/icon_delete.png" style="cursor:pointer" id="rmvItem" />
                                            <f:setPropertyActionListener target="#{configuracaoEstudioControle.relProdutoColaboradorVO}" value="#{item}"/>
                                        </a4j:commandLink>
                                    </rich:column>
                                    <rich:column width="20px">
                                        <a4j:commandLink id="acaoEditar"
                                                action="#{configuracaoEstudioControle.editarRelProdutoColaborador}"
                                                reRender="relacaoProdutoColaborador, relacaoProdutoColaborador-categoria,
                                                          relacaoProdutoColaborador-produto,
                                                          relacaoProdutoColaborador-colaborador,
                                                          relacaoProdutoColaborador-porcComissao,
                                                          configuracaoEstudioControle-listaServico,
                                                          modalPanelErro">
                                            <rich:toolTip value="Editar" for="acaoEditar" direction="bottom-left" />
                                            <h:graphicImage value="../../imagens/estudio/btnAcaoEditar.png"
                                                            style="cursor:pointer" id="editItem"/>
                                            <f:setPropertyActionListener
                                                    target="#{configuracaoEstudioControle.relProdutoColaboradorVO}"
                                                    value="#{item}"/>
                                        </a4j:commandLink>
                                    </rich:column>
                                </rich:dataTable>
                                <rich:datascroller align="center" for="configuracaoEstudioControle-listaServico" maxPages="10" id="scrlListaServico"/>
                            </h:panelGrid>
                        </h:panelGrid>


                        <!-- EMPRESA -->
                        <h:panelGrid id="empresaFuncionamento" rendered="#{configuracaoEstudioControle.panelEmpresa}"
                                     columns="1"
                                     rowClasses="linhaImpar, linhaPar"
                                     width="100%">

                            <h:outputText style="font-weight:bold;"
                                          styleClass="texto_disponibilidade"
                                          value="Horário de Funcionamento da Empresa:"/>
                            <h:panelGrid id="panelEmpresaFunc" columns="5">
                                <rich:spacer width="21px"/>
                                <h:outputText styleClass="texto_disponibilidade"
                                              value="Abertura:"/>
                                <rich:spacer width="17px"/>
                                <rich:inputNumberSpinner id="abertura"
                                                         oninputkeydown="return tabOnEnter(event, 'fechamento');"
                                                         value="#{configuracaoEstudioControle.horaInicioEmpresa}" maxValue="24" minValue="1"/>
                                <h:outputText id="h1"
                                              styleClass="texto_disponibilidade"
                                              value=":00 (h)"/>

                                <rich:spacer width="21px"/>
                                <h:outputText styleClass="texto_disponibilidade"
                                              value="Fechamento:"/>
                                <rich:spacer width="17px"/>
                                <rich:inputNumberSpinner id="fechamento"
                                                         oninputkeydown="return tabOnEnter(event, 'diaDaSemanaSelect');"
                                                         value="#{configuracaoEstudioControle.horaFinalEmpresa}" maxValue="24" minValue="1"/>
                                <h:outputText id="h2"
                                              styleClass="texto_disponibilidade"
                                              value=":00 (h)"/>
                            </h:panelGrid>
                            <h:outputText style="font-weight:bold;"
                                          styleClass="texto_disponibilidade"
                                          value="Horário de Indisponibilidade da Empresa:"/>
                            <h:panelGrid columns="3">
                                <rich:spacer width="21px"/>
                                <h:outputLabel styleClass="texto_disponibilidade"
                                               value="Dia da Semana: "/>

                                <h:selectOneMenu styleClass="texto_disponibilidade"
                                                 id="diaDaSemanaSelect"
                                                 onkeydown="return tabOnEnter(event, 'configuracaoEstudioControle-empresaIndisp-diaMesInputDate');"
                                                 converter="diaDaSemanaConverter"
                                                 value="#{configuracaoEstudioControle.empresaFechadaVO.diaSemana}">
                                    <f:selectItem  itemLabel="Selecione o dia da semana" itemValue=""/>
                                    <f:selectItems value="#{configuracaoEstudioControle.diaDaSemanaSelect}" />
                                </h:selectOneMenu>

                                <rich:spacer width="21px"/>

                                <h:outputLabel
                                    value="Dia Mês:" styleClass="texto_disponibilidade" />
                                <rich:calendar
                                    locale="pt_BR"
                                    inputSize="10"
                                    inputClass="form"
                                    oninputblur="blurinput(this);"
                                    oninputfocus="focusinput(this);"
                                    oninputchange="return validar_Data(this.id);"
                                    oninputkeypress="return mascara(this, '99/99/9999', event); "
                                    oninputkeydown="return tabOnEnter(event, 'hInicial');"
                                    datePattern="dd/MM/yyyy"
                                    enableManualInput="true"
                                    zindex="99"
                                    showWeeksBar="false"
                                    value="#{configuracaoEstudioControle.empresaFechadaVO.diaMes}"
                                    id="configuracaoEstudioControle-empresaIndisp-diaMes"
                                    popup="true"  styleClass="texto_disponibilidade">
                                </rich:calendar>
                            </h:panelGrid>
                            <h:panelGrid id="panelIndispEmpresa" columns="5">
                                <rich:spacer width="21px"/>
                                <h:outputText styleClass="texto_disponibilidade"
                                              value="Hora Inicial:"/>
                                <rich:spacer width="22px"/>
                                <rich:inputNumberSpinner id="hInicial"
                                                         value="#{configuracaoEstudioControle.horaInicioIndispEmpresa}"
                                                         maxValue="24" minValue="1"/>
                                <h:outputText
                                    styleClass="texto_disponibilidade"
                                    value=":00 (h)"/>

                                <rich:spacer width="21px"/>
                                <h:outputText styleClass="texto_disponibilidade"
                                              value="Hora Final:"/>
                                <rich:spacer width="22px"/>
                                <rich:inputNumberSpinner id="hFinal"
                                                         value="#{configuracaoEstudioControle.horaFinalIndispEmpresa}"
                                                         maxValue="24" minValue="1"/>

                                <h:panelGrid columns="3">
                                    <h:outputText
                                        styleClass="texto_disponibilidade"
                                        value=":00 (h)"/>
                                    <rich:spacer width="10px"/>
                                    <a4j:commandButton
                                        styleClass="botoes nvoBt btSec"
                                        value="Adicionar"
                                        id="add"
                                        focus="panelFiltroServico-servico-codigo"
                                        reRender="configuracaoEstudioControle-listaEmpresaIndisp,diaDaSemanaSelect,configuracaoEstudioControle-empresaIndisp-diaMes,
                                        hInicial, hFinal, modalPanelErro, empresaFechada-motivo"
                                        action="#{configuracaoEstudioControle.acaoAdicionarEmpresaFechada}"
                                        title="Adicionar"/>
                                </h:panelGrid>
                            </h:panelGrid>

                            <h:panelGrid columns="3">
                                <rich:spacer width="21px"/>
                                <h:outputText
                                    styleClass="texto_disponibilidade"
                                    value="Motivo:"/>
                                <h:inputText
                                    id="empresaFechada-motivo"
                                    onkeydown="return tabOnEnter(event, 'add');"
                                    value="#{configuracaoEstudioControle.empresaFechadaVO.motivo}"
                                    size="59"
                                    maxlength="60"/>
                            </h:panelGrid>

                            <rich:scrollableDataTable
                                id="configuracaoEstudioControle-listaEmpresaIndisp"
                                var="item"
                                height="90px"
                                value="#{configuracaoEstudioControle.listaEmpresaFechada}"
                                width="474px"
                                frozenColCount="1">

                                <rich:column
                                    width="90px">
                                    <f:facet name="header">
                                        <h:outputText value="Dia da Semana" style="font-size:12px;"  />
                                    </f:facet>
                                    <h:outputText
                                        value="#{item.diaSemana.descricao}"
                                        style="float:right; margin-right: 5px;"/>
                                </rich:column>
                                <rich:column
                                    width="100px">
                                    <f:facet name="header">
                                        <h:outputText value="Dia do Mês" style="font-size:12px;"  />
                                    </f:facet>
                                    <h:outputText
                                        converter="dataConverter"
                                        value="#{item.diaMes}"
                                        style="margin-left: 5px; position:relative;"/>
                                </rich:column>
                                <rich:column
                                    width="70px">
                                    <f:facet name="header">
                                        <h:outputText value="Hora Inicial" styleClass="texto_disponibilidade" />
                                    </f:facet>
                                    <h:outputText
                                        converter="timeConverter"
                                        value="#{item.horaInicial}"
                                        style="margin-left: 5px; position:relative;"/>
                                </rich:column>
                                <rich:column
                                    width="70px">
                                    <f:facet name="header">
                                        <h:outputText
                                            value="Hora Final"
                                            styleClass="texto_disponibilidade" />
                                    </f:facet>
                                    <h:outputText
                                        value="#{item.horaFinal}"
                                        converter="timeConverter2"
                                        style="margin-left: 5px; position:relative;"/>
                                </rich:column>

                                <rich:column
                                    width="100px">
                                    <f:facet name="header">
                                        <h:outputText value="Motivo" style="font-size:12px;"  />
                                    </f:facet>
                                    <h:outputText
                                        value="#{item.motivo}"
                                        style="margin-left: 5px; position:relative;"/>
                                </rich:column>

                                <rich:column width="20px">
                                    <a4j:commandLink action="#{configuracaoEstudioControle.acaoRemoverEmpresaFechada}" reRender="configuracaoEstudioControle-listaEmpresaIndisp">
                                        <h:graphicImage value="#{context}/imagens/estudio/icon_delete.png" style="cursor:pointer" id="rmvItem" />
                                        <f:setPropertyActionListener target="#{configuracaoEstudioControle.empresaFechadaVO}" value="#{item}"/>
                                    </a4j:commandLink>
                                </rich:column>
                            </rich:scrollableDataTable>

                        </h:panelGrid>

                        <!-- TIPO HORÁRIO -->
                        <h:panelGrid id="tipoHorario" rendered="#{configuracaoEstudioControle.panelTipoHorario}" columns="1"
                                     rowClasses="linhaImpar, linhaPar" width="100%"
                                     columnClasses="classEsquerdaConfiguracao, classDireitaConfiguracao" >
                            <h:panelGrid id="paneltituloTipoHorario" columns="1" width="100%" headerClass="subordinado" columnClasses="colunaCentralizada"
                                         style="margin-right:15px;background-color:#B5B5B5;" >
                                <h:panelGroup >
                                    <h:outputText style="font-size:12px;font-weight:bold;margin-right:5px;" value="Tipo Horário"/>
                                    <h:outputLink styleClass="linkWiki"
                                                  value="#{SuperControle.urlBaseConhecimento}como-configurar-tipo-de-horario-no-modulo-studio/"
                                                  title="Clique e saiba mais: Configurações: Tipo Horário" target="_blank" >
                                        <h:graphicImage styleClass="linkWiki" url="../../imagens/wiki_bco.gif"/>
                                    </h:outputLink>
                                </h:panelGroup>
                            </h:panelGrid>
                            <h:panelGrid id="panelTipoHorario" columns="2" headerClass="subordinado"
                                         rowClasses="linhaImpar, linhaPar" width="100%">
                                <h:outputText  styleClass="tituloCampos" value="Descrição" />
                                <h:inputText  id="descLegenda" size="15" maxlength="30" onblur="blurinput(this);"
                                              onfocus="focusinput(this);" styleClass="form" disabled="#{!configuracaoEstudioControle.permiteEditarTodosDadosTipoHorario}"
                                              value="#{configuracaoEstudioControle.tipoHorarioVO.descricao}"/>

                                <h:outputText  styleClass="tituloCampos" value="Cor" />
                                <rich:colorPicker  colorMode="hex" id="corLegenda"  inputSize="15"
                                                   value="#{configuracaoEstudioControle.tipoHorarioVO.cor}"/>

                                <h:outputText  styleClass="tituloCampos" value="Sigla" />
                                <h:panelGroup>
                                   
                                    <h:inputText  id="descricaoLegenda" size="15" maxlength="2" onblur="blurinput(this);"
                                                  onfocus="focusinput(this);" styleClass="form" disabled="#{!configuracaoEstudioControle.permiteEditarTodosDadosTipoHorario}"
                                                  value="#{configuracaoEstudioControle.tipoHorarioVO.sigla}"/>
                                    <h:message for="descricaoLegenda" styleClass="mensagemDetalhada"/>
                                </h:panelGroup>

                                <h:outputText  styleClass="tituloCampos" value="Desativar" />
                                <h:selectBooleanCheckbox id="desativarLegenda" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form"
                                                         value="#{configuracaoEstudioControle.tipoHorarioVO.desativado}"/>

                            </h:panelGrid>
                            <h:panelGrid id="panelButtonTipoHorario" columns="2" headerClass="subordinado"
                                         rowClasses="linhaImpar, linhaPar" width="40%" >
                                <a4j:commandButton
                                    image="../../imagens/estudio/novo.png"
                                    id="novoButton"
                                    action="#{configuracaoEstudioControle.acaoNovoTipoHorario}"
                                    value="Novo" reRender="panelTipoHorario"
                                    title="Novo" style="width:75px; vertical-align: middle;" >
                                </a4j:commandButton>
                                <a4j:commandButton
                                    styleClass="botoes nvoBt btSec"
                                    id="addButton"
                                    action="#{configuracaoEstudioControle.acaoAdicionarTipoHorario}"
                                    value="Adicionar" reRender="panelListagemTipoHorario, modalPanelErro, panelTipoHorario,form"
                                    title="Adicionar" style="width:75px; vertical-align: middle; float: right;" >
                                </a4j:commandButton>
                            </h:panelGrid>

                            <h:panelGrid id="panelListagemTipoHorario" columns="1" >
                                <rich:scrollableDataTable width="415px"  height="150px" value="#{configuracaoEstudioControle.listaTipoHorario}" id="listaAdicionadosConfigEstudio"
                                                          var="item" >
                                    <rich:column width="215px">
                                        <f:facet name="header" >
                                            <h:outputText value="Descrição"/>
                                        </f:facet>
                                        <h:outputText value="#{item.descricao}"/>
                                    </rich:column>
                                    <rich:column width="50px"  style="background-color: #{item.cor}">
                                        <f:facet name="header" >
                                            <h:outputText value="Cor"/>
                                        </f:facet>
                                        <h:outputText value="#{item.cor}" style="background-color: #{item.cor}"/>
                                    </rich:column>
                                    <rich:column width="30px">
                                        <f:facet name="header" >
                                            <h:outputText value="Sigla"/>
                                        </f:facet>
                                        <h:outputText value="#{item.sigla}"/>
                                    </rich:column>
                                    <rich:column width="60px">
                                        <f:facet name="header" >
                                            <h:outputText value="Desativado"/>
                                        </f:facet>
                                        <h:selectBooleanCheckbox disabled="true" value="#{item.desativado}" />
                                    </rich:column>
                                    <a4j:support event="onRowClick" reRender="panelTipoHorario"
                                                 action="#{configuracaoEstudioControle.acaoEditarTipoHorario}" >
                                        <f:setPropertyActionListener target="#{configuracaoEstudioControle.tipoHorarioVO}"
                                                                     value="#{item}" />
                                    </a4j:support>
                                </rich:scrollableDataTable>
                            </h:panelGrid>
                        </h:panelGrid>

                        <!-- PACOTES -->
                        <h:panelGrid id="confPacotes" rendered="#{configuracaoEstudioControle.panelPacote}" columns="1"
                                     rowClasses="linhaImpar, linhaPar" width="100%"
                                     columnClasses="classEsquerdaConfiguracao, classDireitaConfiguracao" >
                            <h:panelGrid id="paneltituloPacote" columns="1" width="100%" headerClass="subordinado" columnClasses="colunaCentralizada"
                                         style="margin-right:15px;background-color:#B5B5B5;" >
                                <h:panelGroup >
                                    <h:outputText style="font-size:12px;font-weight:bold;margin-right:5px;" value="Pacotes"/>
                                    <h:outputLink styleClass="linkWiki"
                                                  value="#{SuperControle.urlWiki}Configurações#Pacotes"
                                                  title="Clique e saiba mais: Configurações: Pacotes" target="_blank" >
                                        <h:graphicImage styleClass="linkWiki" url="../../imagens/wiki_bco.gif"/>
                                    </h:outputLink>
                                </h:panelGroup>
                            </h:panelGrid>
                            <h:panelGrid id="panelPacotes"
                                         columns="2" headerClass="subordinado"
                                         rowClasses="linhaImpar, linhaPar" width="100%">
                                <h:outputText  styleClass="tituloCampos" value="Título" />
                                <h:inputText  id="tituloPacote" size="15" maxlength="30" onblur="blurinput(this);"
                                              onfocus="focusinput(this);" styleClass="form"
                                              value="#{configuracaoEstudioControle.pacoteVO.titulo}"/>

                                <h:outputText  styleClass="tituloCampos" value="Descricao" />
                                <h:inputTextarea id="descricaoPacote" onblur="blurinput(this);"
                                                 onfocus="focusinput(this);"
                                                 cols="40" rows="4"
                                                 value="#{configuracaoEstudioControle.pacoteVO.descricao}" />

                                <h:outputText  styleClass="tituloCampos" value="Validade Inicial" />
                                <rich:calendar  id="valInicialPacote" styleClass="form"
                                                locale="pt_BR"
                                                inputSize="10"
                                                inputClass="form"
                                                oninputblur="blurinput(this);"
                                                oninputfocus="focusinput(this);"
                                                oninputchange="return validar_Data(this.id);"
                                                oninputkeypress="return mascara(this, '99/99/9999', event); "
                                                oninputkeydown="return tabOnEnter(event, 'hInicial');"
                                                datePattern="dd/MM/yyyy"
                                                enableManualInput="true"
                                                zindex="99"
                                                showWeeksBar="false"
                                                popup="true"
                                                value="#{configuracaoEstudioControle.pacoteVO.validadeInicial}"/>

                                <h:outputText  styleClass="tituloCampos" value="Validade Final" />
                                <rich:calendar  id="valFinalPacote" styleClass="form"
                                                locale="pt_BR"
                                                inputSize="10"
                                                inputClass="form"
                                                oninputblur="blurinput(this);"
                                                oninputfocus="focusinput(this);"
                                                oninputchange="return validar_Data(this.id);"
                                                oninputkeypress="return mascara(this, '99/99/9999', event); "
                                                oninputkeydown="return tabOnEnter(event, 'hInicial');"
                                                datePattern="dd/MM/yyyy"
                                                enableManualInput="true"
                                                zindex="99"
                                                showWeeksBar="false"
                                                popup="true"
                                                value="#{configuracaoEstudioControle.pacoteVO.validadeFinal}"/>

                                <h:outputText  styleClass="tituloCampos" value="Valor Total" />
                                <h:inputText  id="valorPacote" size="15" maxlength="30" onblur="blurinput(this);"
                                              onfocus="focusinput(this);" styleClass="form"
                                              onkeypress="return formatar_moeda(this,'','.',event);"
                                              value="#{configuracaoEstudioControle.pacoteVO.valorTotal}"/>

                                <h:outputText  styleClass="tituloCampos" value="Desativar" />
                                <h:selectBooleanCheckbox id="desativarPacote" onblur="blurinput(this);"
                                                         onfocus="focusinput(this);" styleClass="form"
                                                         value="#{configuracaoEstudioControle.pacoteVO.desativado}"/>

                                <h:outputText  styleClass="tituloCampos" value="Produtos:" />
                                <h:panelGroup>
                                    <a4j:commandButton value="Adicionar Produto"
                                                       image="../../imagens/estudio/adicionar_produto.png"
                                                       action="#{configuracaoEstudioControle.acaoAbrirModalItemPacote}"

                                                       reRender="modalItensPacote, modalPanelErro"/>
                                    <rich:scrollableDataTable value="#{configuracaoEstudioControle.listaPacoteProduto}"
                                                              var="itemProduto" id="scrollabePacoteProduto" width="250px"
                                                              height="90px" style="padding-left: 3px;">
                                        <rich:column width="100px">
                                            <f:facet name="header" >
                                                <h:outputText value="Produto" />
                                            </f:facet>
                                            <h:outputText value="#{itemProduto.produtoVO.descricao}" />
                                        </rich:column>
                                        <rich:column width="50px">
                                            <f:facet name="header" >
                                                <h:outputText value="Quantidade" />
                                            </f:facet>
                                            <h:outputText value="#{itemProduto.quantidade}" />
                                        </rich:column>
                                        <rich:column width="52px">
                                            <f:facet name="header" >
                                                <h:outputText value="Valor" />
                                            </f:facet>
                                            <h:outputText value="#{itemProduto.valorUnitario}" >
                                                <f:convertNumber maxFractionDigits="2" groupingUsed="true" maxIntegerDigits="14" type="currency" currencySymbol="#{configuracaoEstudioControle.empresa.moeda} " />
                                            </h:outputText>
                                        </rich:column>
                                        <rich:column width="20px">
                                            <a4j:commandButton  value="Excluir" title="Excluir"
                                                                image="/imagens/estudio/icon_delete.png"
                                                                reRender="scrollabePacoteProduto"
                                                                action="#{configuracaoEstudioControle.acaoRemoverPacoteProduto}">
                                                <f:setPropertyActionListener value="#{itemProduto}"
                                                                             target="#{configuracaoEstudioControle.pacoteProdutoVO}" />
                                            </a4j:commandButton>
                                        </rich:column>
                                    </rich:scrollableDataTable>
                                </h:panelGroup>

                                <h:outputText styleClass="tituloCampos" value="Imagem:" title="Imagem"/>
                                <h:panelGrid columns="2">
                                    <a4j:mediaOutput element="img" id="imagemUploadPacote"  style="width:80px;height:100px;"
                                                     cacheable="false" session="true"
                                                     createContent="#{configuracaoEstudioControle.paintFoto}"
                                                     value="#{ImagemData}" mimeType="image/jpeg"/>
                                    <rich:fileUpload listHeight="0"
                                                     listWidth="0"
                                                     noDuplicate="false"
                                                     fileUploadListener="#{configuracaoEstudioControle.upload}"
                                                     maxFilesQuantity="1"
                                                     addControlLabel="Procurar..."
                                                     cancelEntryControlLabel="Cancelar"
                                                     doneLabel="Pronto"
                                                     sizeErrorLabel="Arquivo não Enviado. Tamanho Máximo Permitido 100 MB"
                                                     progressLabel="Enviando"
                                                     stopControlLabel="Parar"
                                                     uploadControlLabel="Enviar"
                                                     transferErrorLabel="Falha de Transmissão"
                                                     stopEntryControlLabel="Parar"
                                                     id="upload"
                                                     immediateUpload="true"
                                                     autoclear="true"
                                                     acceptedTypes="png,gif,jpg,jpeg,ico,bmp,PNG,GIF,JPG,JPEG,ICO,BMP">

                                        <a4j:support event="onuploadcomplete" reRender="upload, imagemUploadPacote" />
                                    </rich:fileUpload>
                                </h:panelGrid>


                            </h:panelGrid>
                            <h:panelGrid id="panelButtonPacote" columns="2" headerClass="subordinado"
                                         rowClasses="linhaImpar, linhaPar" width="100%" >
                                <a4j:commandButton
                                    image="../../imagens/estudio/novo.png"
                                    id="novoButtonPacote" reRender="confPacotes, modalPanelErro"
                                    action="#{configuracaoEstudioControle.acaoNovoPacote}"
                                    value="Novo"
                                    title="Novo" style="width:75px; vertical-align: middle;" >
                                </a4j:commandButton>
                                <a4j:commandButton
                                    action="#{configuracaoEstudioControle.acaoAdicionarPacote}"
                                    styleClass="botoes nvoBt btSec"
                                    id="addButtonPacote"
                                    value="Adicionar"  reRender="confPacotes, modalPanelErro"
                                    title="Adicionar" style="width:75px; vertical-align: middle; float: right;" >
                                </a4j:commandButton>
                            </h:panelGrid>

                            <h:panelGrid id="panelListagemPacotes" columns="1" >
                                <h:panelGroup>
                                <rich:dataTable style="width:450px;  height:150px" var="itemPacote"
                                                          id="scrollabePanelListagemPacotes"
                                                          value="#{configuracaoEstudioControle.listaPacote}">
                                    <rich:column width="125px">
                                        <f:facet name="header" >
                                            <h:outputText value="Título" title="Título" />
                                        </f:facet>
                                        <h:outputText value="#{itemPacote.titulo}"/>
                                    </rich:column>
                                    <rich:column width="55px" >
                                        <f:facet name="header" >
                                            <h:outputText value="Valor" title="Valor"/>
                                        </f:facet>
                                        <h:outputText value="#{itemPacote.valorTotal}" >
                                            <f:convertNumber maxFractionDigits="2" groupingUsed="true" maxIntegerDigits="14" type="currency" currencySymbol="#{configuracaoEstudioControle.empresa.moeda} " />
                                        </h:outputText>
                                    </rich:column>
                                    <rich:column width="90px">
                                        <f:facet name="header" >
                                            <h:outputText value="Validade Inicial" title="Validade Inicial"/>
                                        </f:facet>
                                        <h:outputText value="#{itemPacote.validadeInicial}" converter="dataConverter"/>
                                    </rich:column>
                                    <rich:column width="90px">
                                        <f:facet name="header" >
                                            <h:outputText value="Validade Final" title="Validade Final"/>
                                        </f:facet>
                                        <h:outputText value="#{itemPacote.validadeFinal}" converter="dataConverter"/>
                                    </rich:column>
                                    <rich:column width="20px">
                                        <f:facet name="header" >
                                            <h:outputText title="Desativado" value="Des"/>
                                        </f:facet>
                                        <h:selectBooleanCheckbox disabled="true" value="#{itemPacote.desativado}" />
                                    </rich:column>
                                    <rich:column width="20px">
                                        <a4j:commandButton  value="Editar" title="Editar"
                                                            image="/imagens/estudio/btnAcaoEditar.png"
                                                            reRender="confPacotes">
                                            <f:setPropertyActionListener target="#{configuracaoEstudioControle.pacoteVO}"
                                                                         value="#{itemPacote}" />
                                            <f:setPropertyActionListener target="#{configuracaoEstudioControle.listaPacoteProduto}"
                                                                         value="#{itemPacote.listaPacoteProduto}" />
                                        </a4j:commandButton>
                                    </rich:column>
                                    <rich:column width="20px">
                                        <a4j:commandButton  value="Excluir" title="Excluir"
                                                            image="/imagens/estudio/icon_delete.png"
                                                            reRender="scrollabePanelListagemPacotes, modalPanelErro, confPacotes"
                                                            action="#{configuracaoEstudioControle.acaoRemoverPacote}">
                                            <f:setPropertyActionListener target="#{configuracaoEstudioControle.pacoteVO}"
                                                                         value="#{itemPacote}" />
                                        </a4j:commandButton>
                                    </rich:column>
                                </rich:dataTable>
                                </h:panelGroup>
                            </h:panelGrid>
                        </h:panelGrid>

                    </h:panelGrid>
                </h:panelGrid>


                <h:panelGrid id="panelmensagem" columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid columns="3" width="100%" styleClass="tabMensagens">
                        <h:panelGrid columns="1" width="100%">
                            <f:verbatim>
                                <h:outputText value=" "/>
                            </f:verbatim>
                        </h:panelGrid>
                        <h:commandButton  rendered="#{configuracaoEstudioControle.sucesso}" image="../../imagens/sucesso.png"/>
                        <h:commandButton rendered="#{configuracaoEstudioControle.erro}" image="../../imagens/erro.png"/>
                        <h:panelGrid columns="1" width="100%">
                            <h:outputText styleClass="mensagem"  value="#{configuracaoEstudioControle.mensagem}"/>
                            <h:outputText styleClass="mensagemDetalhada" value="#{configuracaoEstudioControle.mensagemDetalhada}"/>
                        </h:panelGrid>
                    </h:panelGrid>

                    <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                        <h:panelGroup>
                            <h:commandButton id="salvar" action="#{configuracaoEstudioControle.gravar}" value="#{msg_bt.btn_gravar}"
                                             alt="#{msg.msg_gravar_dados}" accesskey="2"
                                             styleClass="botoes nvoBt btSec" rendered="#{configuracaoEstudioControle.apresentarBotaoGravar}"/>
                        </h:panelGroup>
                    </h:panelGrid>
                </h:panelGrid>

            </h:form>
        </h:panelGrid>


        <rich:modalPanel id="modalPanelErro" autosized="true" shadowOpacity="true"
                         showWhenRendered="#{configuracaoEstudioControle.apresentarModalErro}" width="450"
                         height="80" onshow="focusAt('formModalPanelErro:fecharButton');"
                         zindex="99">
            <f:facet name="header">
                <h:panelGroup>
                    <h:outputText value="Atenção!"></h:outputText>
                </h:panelGroup>
            </f:facet>
            <a4j:form id="formModalPanelErro" prependId="false">
                <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada" >

                    <h:panelGrid columns="2" width="100%">
                        <h:graphicImage url="../../imagens/erro.png" />
                        <h:outputText styleClass="mensagemDetalhada" value="#{configuracaoEstudioControle.mensagemDetalhada}"/>
                    </h:panelGrid>
                </h:panelGrid>
                <rich:spacer height="05px"/>
                <h:panelGrid style="position: relative; float:right; ">
                    <a4j:commandButton
                        image="/imagens/estudio/fechar.png"
                        id="fecharButton"
                        value="Fechar"
                        title="Fechar" status="statusHora"
                        action="#{configuracaoEstudioControle.acaoFecharModalErro}" reRender="modalPanelErro" />
                </h:panelGrid>
            </a4j:form>
        </rich:modalPanel>

        <rich:modalPanel id="modalItensPacote" autosized="true" shadowOpacity="true"
                         showWhenRendered="#{configuracaoEstudioControle.apresentarModalItemPacote}" width="450"
                         height="80" onshow="focusAt('formItemPacote:selectItemProduto');"
                         zindex="90">
            <f:facet name="header">
                <h:panelGroup>
                    <h:outputText value="Cadastrar Item do Pacote"></h:outputText>
                </h:panelGroup>
            </f:facet>
            <a4j:form id="formItemPacote" prependId="false">

                <h:panelGrid columns="2" style="float: right;">
                    <a4j:commandButton value="Confirmar" image="../../imagens/estudio/confirmar.png" title="Confirmar"
                                       id="buttonConfimarPacoteProduto"
                                       action="#{configuracaoEstudioControle.acaoConfirmarPacoteProduto}"
                                       reRender="modalPanelErro, modalItensPacote, scrollabePacoteProduto, valorPacote" />
                    <a4j:commandButton
                        image="/imagens/estudio/fechar.png"
                        id="fecharButtonItemPacote"
                        value="Fechar"
                        title="Fechar" status="statusHora"
                        action="#{configuracaoEstudioControle.acaoFecharModalItemPacote}" reRender="modalItensPacote" />
                </h:panelGrid>
            </a4j:form>
        </rich:modalPanel>

    </f:view>
</body>
</html>



<%-- 
    Document   : indisponibilidadeColaborador
    Created on : May 7, 2012, 10:31:59 AM
    Author     : <PERSON> GeoInova Soluções
--%>

<%@page contentType="text/html;charset=UTF-8" %>
<%@include file="includes/include_imports.jsp" %>
<!DOCTYPE html>
<html>
    <head>
        <script type="text/javascript" src="${contexto}/script/basico.js"></script>
        <link href="${contexto}/css/estudio.css" rel="stylesheet" type="text/css">
        <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
        <%@include file="/includes/include_import_minifiles.jsp" %>
        <script type="text/javascript" language="javascript" src="hoverform.js"></script>
    </head>
    <body>

            <h:panelGrid id="panelIndisponibilidadeColaborador" columns="1" width="100%" headerClass="subordinado" columnClasses="colunaCentralizada">
                <f:facet name="header">
                    <h:outputText value="Indisponibilidade Estúdio"/>
                </f:facet>
                <h:panelGrid columns="2"  columnClasses="classEsquerda, classDireita" width="100%">
                    <h:outputLabel styleClass="texto_disponibilidade"
                                   value="Dia da Semana: "/>

                    <h:selectOneMenu styleClass="texto_disponibilidade"
                                     id="diaDaSemanaSelect"
                                     onkeydown="return tabOnEnter(event, 'configuracaoEstudioControle-colaboradorIndispVO-diaMesInputDate');"
                                     converter="diaDaSemanaConverter"
                                     value="#{configuracaoEstudioControle.colaboradorIndispVO.diaSemana}">
                        <f:selectItem  itemLabel="Selecione o dia da semana" itemValue=""/>
                        <f:selectItems value="#{configuracaoEstudioControle.diaDaSemanaSelect}" />
                    </h:selectOneMenu>

                    <h:outputLabel
                        value="Dia Mês:" styleClass="texto_disponibilidade" />
                    <rich:calendar
                        locale="pt_BR"
                        inputSize="10"
                        inputClass="form"
                        oninputblur="blurinput(this);"
                        oninputfocus="focusinput(this);"
                        oninputchange="return validar_Data(this.id);"
                        oninputkeypress="return mascara(this, '99/99/9999', event); "
                        oninputkeydown="return tabOnEnter(event, 'hInicial');"
                        datePattern="dd/MM/yyyy"
                        enableManualInput="true"
                        zindex="99"
                        showWeeksBar="false"
                        value="#{configuracaoEstudioControle.colaboradorIndispVO.diaMes}"
                        id="configuracaoEstudioControle-colaboradorIndispVO-diaMes"
                        popup="true"  styleClass="texto_disponibilidade">
                    </rich:calendar>
                    <h:outputText styleClass="texto_disponibilidade"
                                  value="Hora Inicial:"/>
                    <h:panelGrid columns="2">
                            <h:inputText id="hInicial"
                                         onkeypress="return mascaraTodos(this.form, 'form:hInicial', '99:99', event);"
                                         size="5" maxlength="5" onblur="blurinput(this);" onfocus="focusinput(this);"
                                         styleClass="form" value="#{configuracaoEstudioControle.horaInicioIndispColaborador}"/>
                            <h:message for="hInicial" styleClass="mensagemDetalhada"/>
                    </h:panelGrid>


                    <h:outputText styleClass="texto_disponibilidade"
                                  value="Hora Final:"/>
                    <h:panelGrid columns="2">
                        <h:inputText id="hFinal"
                                     onkeypress="return mascaraTodos(this.form, 'form:hFinal', '99:99', event);"
                                     size="5" maxlength="5" onblur="blurinput(this);" onfocus="focusinput(this);"
                                     styleClass="form" value="#{configuracaoEstudioControle.horaFinalIndispColaborador}"/>
                        <h:message for="hFinal" styleClass="mensagemDetalhada"/>
                    </h:panelGrid>

                    <h:outputText 
                        styleClass="texto_disponibilidade"
                        value="Motivo:"/>
                    <h:inputText 
                        id="colaboradorIndispVO-motivo"
                        onkeydown="return tabOnEnter(event, 'add');"
                        value="#{configuracaoEstudioControle.colaboradorIndispVO.motivo}" 
                        size="59"
                        maxlength="60"/>
                </h:panelGrid>
                <h:panelGroup layout="block" styleClass="container-botoes">
                 <a4j:commandLink
                        value="Adicionar"
                        styleClass="botaoSecundario texto-size-14-real"
                        id="add"
                        focus="panelFiltroServico-servico-codigo"
                        reRender="configuracaoEstudioControle-listaColaboradorIndisp, msgColaboradorDet, panelIndisponibilidadeColaborador"
                        action="#{configuracaoEstudioControle.acaoAdicionarColaboradorIndisp}"
                        title="Adicionar"/>
                </h:panelGroup>
                <h:panelGrid columns="1" width="100%" styleClass="tabFormSubordinada">
                    <h:dataTable rendered="#{!empty configuracaoEstudioControle.listaColaboradorIndisp}"
                        id="configuracaoEstudioControle-listaColaboradorIndisp"
                        var="item"
                        value="#{configuracaoEstudioControle.listaColaboradorIndisp}"
                        width="100%" headerClass="subordinado" styleClass="tabFormSubordinada"
                        rowClasses="linhaImpar, linhaPar" columnClasses="colunaAlinhamento">

                        <h:column>
                            <f:facet name="header">
                                <h:outputText value="Dia da Semana"   />
                            </f:facet>
                            <h:outputText
                                value="#{item.diaSemana.descricao}"/>
                        </h:column>
                        <h:column>
                            <f:facet name="header">
                                <h:outputText value="Dia do Mês"   />
                            </f:facet>
                            <h:outputText
                                converter="dataConverter"
                                value="#{item.diaMes}"/>
                        </h:column>
                        <h:column>
                            <f:facet name="header">
                                <h:outputText value="Hora Inicial"  />
                            </f:facet>
                            <h:outputText
                                converter="timeConverter"
                                value="#{item.horaInicial}"/>
                        </h:column>
                        <h:column>
                            <f:facet name="header">
                                <h:outputText 
                                    value="Hora Final" />
                            </f:facet>
                            <h:outputText
                                value="#{item.horaFinal}"
                                converter="timeConverter2"/>
                        </h:column>

                        <h:column>
                            <f:facet name="header">
                                <h:outputText value="Motivo" />
                            </f:facet>
                            <h:outputText
                                value="#{item.motivo}"/>
                        </h:column>

                        <h:column>
                            <f:facet name="header">
                                <h:outputText value="Excluir" />
                            </f:facet>
                            <a4j:commandLink action="#{configuracaoEstudioControle.acaoRemoverColaboradorIndisp}" reRender="configuracaoEstudioControle-listaColaboradorIndisp, panelIndisponibilidadeColaborador">
                                <h:graphicImage value="/imagens/estudio/icon_delete.png" styleClass="botoes" style="cursor:pointer" id="rmvItem" />
                                <f:setPropertyActionListener target="#{configuracaoEstudioControle.colaboradorIndispVO}" value="#{item}"/>
                            </a4j:commandLink>
                        </h:column>
                    </h:dataTable>
                </h:panelGrid>
            </h:panelGrid>


    </body>
</html>

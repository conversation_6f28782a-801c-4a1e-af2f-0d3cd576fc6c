<%-- 
    Document   : agendaAmbiente
    Created on : Feb 28, 2012, 3:24:58 PM
    Author     : <PERSON>?nio
--%>


<%@include file="includes/include_imports.jsp" %>
<c:set var="moduloSession" value="1" scope="session" />


<html>
    <head>
        <script type="text/javascript" language="javascript" src="${contexto}/script/basico.js"></script>
        <script type="text/javascript" language="javascript" src="${root}/script/smartbox/smartbox.js"></script>
        <script type="text/javascript" language="javascript" src="${root}/script/tooltipster/jquery.tooltipster.min.js"></script>
        <link href="${root}/css_pacto.css" rel="stylesheet" type="text/css">
        <link href="${root}/css/smartbox/smartbox.css" rel="stylesheet" type="text/css">
        <jsp:include page="includes/include_head.jsp" />
        <%@ taglib uri="http://java.sun.com/jsp/jstl/functions" prefix="fn"%>

        <style>
            .cols{
                vertical-align:top;
            }
            .row_agenda {
                text-align: left;
            }
            .row_agenda_geral {
                border-width: 0;
                text-align: center;
            }
            .celula_agenda {
                border-style: none;
                background: red;
            }

        </style>       
    </head>
    <body>
        <f:view>
            <jsp:include page="/includes/include_carregando_ripple.jsp" flush="true"/>
        <title>
            <h:outputText value="Agenda Ambiente"/>
        </title>

        <a4j:form id="agendaGeral" prependId="false" ajaxSubmit="true" >
            <c:set var="moduloEstudio" scope="request" value="estudio"/>
            <h:panelGroup layout="block" style="display: inline-table;width: 100%;" styleClass="fundoCinza">
                <h:panelGroup layout="block" styleClass="bgtop topoZW" rendered="#{MenuControle.apresentarTopo}">
                    <jsp:include page="../../include_topo_novo.jsp" flush="true"/>
                    <jsp:include page="../../include_menu_zw_flat.jsp" flush="true"/>
                    <rich:jQuery selector=".item6" query="addClass('menuItemAtual')"/>
                </h:panelGroup>

                <h:panelGroup layout="block" styleClass="caixaCorpo">
                    <h:panelGroup layout="block" style="height: 80%;width: 100%">
                        <h:panelGroup layout="block" styleClass="caixaMenuLatel">
                                                <h:panelGroup id="expansores" layout="block">

                                                    <h:panelGroup id="expansor1" styleClass="menulateral_restaurado" rendered="#{SuperControle.exibirMenuLateralEstudio}">
                                                        <a4j:commandButton
                                                                image="/css/smartbox/botao_minimizar.png"
                                                                actionListener="#{SuperControle.toggleMenuLateralEstudio}"
                                                                status="statusHora"
                                                                reRender="tabelaConteudo,expansores"/>
                                                    </h:panelGroup>

                                                    <h:panelGroup id="expansor2" styleClass="menulateral_retraido" rendered="#{!SuperControle.exibirMenuLateralEstudio}">
                                                        <a4j:commandButton
                                                                image="/css/smartbox/botao_maximizar.png"
                                                                actionListener="#{SuperControle.toggleMenuLateralEstudio}"
                                                                status="statusHora"
                                                                reRender="tabelaConteudo,expansores"/>
                                                    </h:panelGroup>
                                                </h:panelGroup>
                                                <h:panelGrid width="100%" border="0" id="tabelaConteudo"
                                                             columns="3"
                                                             cellpadding="0" cellspacing="0">

                                                    <rich:column width="237" styleClass="#{SuperControle.exibirMenuLateralEstudio ? 'mostra' : 'esconde'}" style="padding: 10px 5px 0px 0px;vertical-align: top;">
                                                        <h:panelGroup layout="block" rendered="#{SuperControle.exibirMenuLateralEstudio}">
                                                            <h:panelGroup layout="block" styleClass="cantos_t">
                                                                <h:panelGroup layout="block" styleClass="cantos_l">
                                                                    <h:panelGroup layout="block" styleClass="cantos_r">
                                                                        <h:panelGroup layout="block" styleClass="cantos_b">
                                                                            <h:panelGroup layout="block" styleClass="cantos_tl">
                                                                                <h:panelGroup layout="block" styleClass="cantos_tr">
                                                                                    <h:panelGroup layout="block" styleClass="cantos_bl">
                                                                                        <h:panelGroup layout="block" styleClass="cantos_br">
                                                                                            <h:panelGroup layout="block" styleClass="cantos">
                                                                                                <h:panelGroup layout="block" style="padding: 20px 20px 20px 20px">
                                                                                                    <jsp:include page="includes/include_box_menulateral.jsp" />
                                                                                                    <jsp:include page="includes/include_box_descricao.jsp" />
                                                                                                </h:panelGroup>
                                                                                            </h:panelGroup>
                                                                                        </h:panelGroup>
                                                                                    </h:panelGroup>
                                                                                </h:panelGroup>
                                                                            </h:panelGroup>
                                                                        </h:panelGroup>
                                                                    </h:panelGroup>
                                                                </h:panelGroup>
                                                            </h:panelGroup>
                                                        </h:panelGroup>
                                                    </rich:column>

                                                    <rich:column style="vertical-align: top;" width="100%">
                                                        <h:panelGroup layout="block" styleClass="container-box zw_ui especial ">
                                                            <h:panelGroup layout="block" styleClass="container-box-header">
                                                                <h:panelGroup layout="block" styleClass="margin-box">
                                                                    <h:outputText value=" Agenda Ambiente" styleClass="container-header-titulo"/>
                                                                    <h:outputLink styleClass="linkWiki"
                                                                                  value="#{SuperControle.urlBaseConhecimento}como-cadastrar-ambiente-para-o-modulo-studio/"
                                                                                  title="Agenda de Ambiente"
                                                                                  target="_blank" >
                                                                        <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                                                                    </h:outputLink>
                                                                </h:panelGroup>
                                                            </h:panelGroup>
                                                            <h:panelGroup layout="block" styleClass="margin-box">
                                                                <h:panelGroup id="pnlFiltro" layout="block" style="padding: 0 0 0 0; float:right;vertical-align:middle;">
                                                                    <a4j:commandButton id="btnBuscar"
                                                                                       image="#{context}/imagens/estudio/filtro_ambiente.png"
                                                                                       action="#{AgendaAmbienteColaboradorControle.buscarListaAmbientes}"
                                                                                       oncomplete="#{rich:component('panelFiltro')}.show()"
                                                                                       value="Filtro" reRender="agendaAmbienteProfissionalControle-listaAmbiente, modalPanelErro,formPanelFiltro"
                                                                                       title="Filtro">
                                                                        <f:setPropertyActionListener target="#{AgendaAmbienteColaboradorControle.parBotao}" value="true" />
                                                                    </a4j:commandButton>

                                                                    <a4j:commandButton id="btnAtualizar" style="vertical-align:top;"
                                                                                       image="#{context}/imagens/estudio/btn_atualizar.png" value="Atualizar"
                                                                                       title="Atualizar" action="#{AgendaAmbienteColaboradorControle.verPreferencias}"/>
                                                                </h:panelGroup>
                                                            </h:panelGroup>
                                                            <h:panelGroup layout="block" styleClass="margin-box">
                                                                <h:panelGroup layout="block" style="height: 100%; padding-top: 05px;" >
                                                                    <table cellpadding="0" cellspacing="0" width="100%" id="agenda_ambiente" style="border-collapse: collapse;">
                                                                        <tr style="height:30px;">

                                                                            <rich:column colspan="#{fn:length(AgendaAmbienteColaboradorControle.listaAmbienteAnalisados)+2}"
                                                                                         style="text-align: center;"
                                                                                         styleClass="texto_agenda" >
                                                                                <h:outputText style="font-weight:bold;" value="#{AgendaAmbienteColaboradorControle.hojePorExtenso}"/>
                                                                            </rich:column>
                                                                        </tr>

                                                                        <tr>
                                                                            <td style="height:30px; text-align: center;">
                                                                                <h:outputText value="Hor�rio" style="text-align: center;" styleClass="texto_agenda"/>
                                                                            </td>
                                                                            <a4j:repeat value="#{AgendaAmbienteColaboradorControle.listaAmbienteAnalisados}" var="item" rowKeyVar="idx">
                                                                                <rich:column style="height:30px;text-align: center;">
                                                                                    <h:outputText  value="#{item.descricao}" styleClass="texto_agenda" />
                                                                                </rich:column>
                                                                            </a4j:repeat>
                                                                            <rich:column >
                                                                                <h:outputText style="text-align: center;" value="Hor�rio" styleClass="texto_agenda" />
                                                                            </rich:column>
                                                                        </tr>

                                                                        <a4j:repeat value="#{AgendaAmbienteColaboradorControle.listaHoras}"
                                                                                    binding="#{AgendaAmbienteColaboradorControle.ajaxRepeatHora}" var="hour" rowKeyVar="idh" >
                                                                            <tr>
                                                                                <td style="width: 50px; height:30px; text-align: center;" >
                                                                                    <h:outputText value="#{hour}" styleClass="texto_agenda" />
                                                                                </td>
                                                                                <a4j:repeat value="#{AgendaAmbienteColaboradorControle.listaAmbienteAnalisados}"
                                                                                            binding="#{AgendaAmbienteColaboradorControle.ajaxRepeatAmbiente}"
                                                                                            var="item" rowKeyVar="idx">

                                                                                    <rich:column style="text-align: center; background-color: #{AgendaAmbienteColaboradorControle.verificarCorCelula}"
                                                                                                 styleClass="#{AgendaAmbienteColaboradorControle.verificarCorCelula}">
                                                                                        <h:panelGroup layout="block" style="float: right; font-size: xx-small; " >
                                                                                            <a4j:commandButton image="#{context}/imagens/estudio/adicionar_mais.png" title="Adicionar Agendamento"
                                                                                                               action="#{AgendaAmbienteColaboradorControle.verificarDisponibilidadeCelula}"
                                                                                                               reRender="panelAgendaAluno, modalPanelErro" value="Agendar"
                                                                                                               rendered="#{AgendaAmbienteColaboradorControle.podeEfetuarAgendamento}"/>
                                                                                        </h:panelGroup>
                                                                                        <h:panelGroup layout="block" style=" float: left;">
                                                                                            <rich:dataTable value="#{AgendaAmbienteColaboradorControle.buscarListaAgendas}" var="itensAgenda" rowKeyVar="idxAgendas" id="repeatItensAgendaAmbiente"
                                                                                                            width="100%" border="none" align="center" styleClass="texto_agenda tabelaSemBorda" style="border-style:none; padding: 0 0 0 0;" >
                                                                                                <rich:column  styleClass="texto_agenda tabelaSemBorda" style="border-style:none; padding: 0 0 0 0;">
                                                                                                    <rich:dataTable
                                                                                                            columnsWidth="100%" value="#{itensAgenda}"
                                                                                                            var="itemAgenda" styleClass="texto_agenda tabelaSemBorda"
                                                                                                            rowClasses="row_agenda" style="border-style:none; padding: 0 0 0 0;" binding="#{AgendaAmbienteColaboradorControle.dataTableItemAgenda}">
                                                                                                        <h:inputHidden value="#{itemAgenda.clienteVO.pessoa.nome}"
                                                                                                                       binding="#{AgendaAmbienteColaboradorControle.inputNomeReduzido}"/>
                                                                                                        <rich:column style="border-style:none; padding: 0 0 0 0;background-color:#{AgendaAmbienteColaboradorControle.corStatus};" >
                                                                                                            <a4j:commandLink action="#{AgendaAmbienteColaboradorControle.editarAgendamento}" style="color: black;"
                                                                                                                             id="commandLinkAjaxToolAmbiente" reRender="toolTipAgenda"
                                                                                                                             status="statusHora">
                                                                                                                <h:outputText
                                                                                                                        value="#{itemAgenda.status.id}"
                                                                                                                        styleClass="text_agenda" style="cursor: pointer; display: inline;"
                                                                                                                        id="dataTable-statusIdAmbiente"/>
                                                                                                                <h:outputText
                                                                                                                        value=" - #{AgendaAmbienteColaboradorControle.retornarNomeReduzido} - #{itemAgenda.produtoVO.descricao}"
                                                                                                                        styleClass="text_agenda" style="cursor: pointer;"
                                                                                                                        id="dataTable-nomeServicoAmbiente" />
                                                                                                                <f:setPropertyActionListener target="#{AgendaAmbienteColaboradorControle.itemAgenda}" value="#{itemAgenda}" />
                                                                                                            </a4j:commandLink>
                                                                                                        </rich:column>

                                                                                                    </rich:dataTable>

                                                                                                </rich:column>
                                                                                            </rich:dataTable>

                                                                                        </h:panelGroup>
                                                                                    </rich:column>

                                                                                </a4j:repeat>

                                                                                <td style="width: 50px; height:30px; text-align: center;">
                                                                                    <h:outputText value="#{hour}" styleClass="texto_agenda" />
                                                                                </td>
                                                                            </tr>
                                                                        </a4j:repeat>
                                                                    </table>
                                                                </h:panelGroup>
                                                            </h:panelGroup>
                                                            <h:panelGroup layout="block" styleClass="margin-box">
                                                                <f:verbatim>

                                                                    <fieldset class="texto_agenda">
                                                                        <legend class="texto_agenda">Legendas</legend>
                                                                        <h:panelGrid columns="2" styleClass="colunaEsquerda,colunaDireita" width="100%">
                                                                            <h:panelGrid  columns="6">
                                                                                <div class="cor_empresa_fechada" style="width: 20px; height: 20px;"></div>
                                                                                <h:outputText value="Empresa Fechada" style="padding: 0 10px 0 0;" styleClass="texto_agenda" />

                                                                                <div class="cor_feriado" style="width: 20px; height: 20px;"></div>
                                                                                <h:outputText value="Feriado" style="padding: 0 10px 0 0;" styleClass="texto_agenda" />

                                                                                <div class="cor_indisponibilidade" style="width: 20px; height: 20px;"></div>
                                                                                <h:outputText value="Indisponibilidade" style="padding: 0 10px 0 0;" styleClass="texto_agenda" />

                                                                                <div style="width: 20px; height: 20px;background-color:#cca8d0;" ></div>
                                                                                <h:outputText value="Falta Justificada" style="padding: 0 10px 0 0;" styleClass="texto_agenda" title="#{msg_aplic.legenda_agenda_falta_justificada}" />

                                                                                <div style="width: 20px; height: 20px;background-color:#7FFFD4;" ></div>
                                                                                <h:outputText value="Falta e N�o Considera" style="padding: 0 10px 0 0;" styleClass="texto_agenda" title="#{msg_aplic.legenda_agenda_falta_semDebito}" />

                                                                                <div style="width: 20px; height: 20px;background-color:#f39b9b;"></div>
                                                                                <h:outputText value="Falta e Considera" style="padding: 0 10px 0 0;" styleClass="texto_agenda" title="#{msg_aplic.legenda_agenda_falta}" />

                                                                                <div  style="width: 20px; height: 20px;background-color:#a8d3af;"></div>
                                                                                <h:outputText value="Sess�o Ministrada" style="padding: 0 10px 0 0;" styleClass="texto_agenda" title="#{msg_aplic.legenda_agenda_ministrada}"/>

                                                                                <div  style="width: 20px; height: 20px;background-color:#a3ccee;"></div>
                                                                                <h:outputText value="Sess�o Confirmada" style="padding: 0 10px 0 0;" styleClass="texto_agenda" title="#{msg_aplic.legenda_agenda_sessao_confirmada}" />

                                                                                <div  style="width: 20px; height: 20px;background-color:#fcd5b4;"></div>
                                                                                <h:outputText value="Remarcada" style="padding: 0 10px 0 0;" styleClass="texto_agenda" title="#{msg_aplic.legenda_agenda_sessao_remarcada}"/>

                                                                                <div  style="width: 20px; height: 20px;background-color:#e6f1f1;"></div>
                                                                                <h:outputText value="Sem Defini��o" style="padding: 0 10px 0 0;" styleClass="texto_agenda" title="#{msg_aplic.legenda_agenda_sem_definicao}"/>
                                                                            </h:panelGrid>
                                                                        </h:panelGrid>
                                                                    </fieldset>
                                                                </f:verbatim>
                                                            </h:panelGroup>
                                                        </h:panelGroup>
                                                    </rich:column>
                                                </h:panelGrid>
                        </h:panelGroup>
                    </h:panelGroup>
                </h:panelGroup>

                <jsp:include page="../../include_rodape_flat.jsp" flush="true"/>
            </h:panelGroup>
        </a4j:form>

        <rich:modalPanel id="panelFiltro" autosized="true" shadowOpacity="true"
                         onshow="focusAt('modalAmbienteCodg-ambiente-codigo');#{rich:component('keyEscFiltro')}.enable();"
                         showWhenRendered="#{AgendaAmbienteColaboradorControle.renderedModal}" width="500"
                         height="280" >
            <f:facet name="header">
                <h:panelGroup>
                    <h:outputText value="Filtro"/>
                </h:panelGroup>
            </f:facet>
            <f:facet name="controls">
                <h:panelGroup>
                    <h:graphicImage value="imagens/close.png" style="cursor:pointer" id="hidelinkPanelFiltro" />
                    <rich:componentControl for="panelFiltro" attachTo="hidelinkPanelFiltro"
                                           operation="hide" event="onclick" >
                        <rich:hotKey
                            id="keyEscFiltro"
                            key="esc"
                            handler="#{rich:component('panelFiltro')}.hide();"/>
                        <a4j:support event="onclick" oncomplete="#{rich:component('keyEscFiltro')}.disable();" />
                    </rich:componentControl>
                </h:panelGroup>
            </f:facet>

            <a4j:form id="formPanelFiltro" ajaxSubmit="true" prependId="false">

                <rich:tabPanel
                    switchType="ajax" id="tabPanel" height="250">
                    <rich:tab id="abaAmbiente" focus="modalAmbienteCodg-ambiente-codigo"
                              name="Ambiente" label="Ambiente">
                        <h:panelGrid columns="5" id="dadosDependenteAmbiente" >
                            <h:outputLabel
                                value="N� do Ambiente" />
                            <rich:spacer width="20px"/>
                            <h:outputLabel
                                value="Nome" />
                            <rich:spacer width="10px"/>
                            <rich:spacer width="10px"/>

                            <h:inputText
                                title="C�digo do Ambiente"
                                onblur="blurinput(this);"
                                onfocus="focusinput(this);"
                                styleClass="form"
                                maxlength="4"
                                onkeydown="return tabOnEnter(event, 'modalAmbienteCodg-ambiente-descricao');"
                                autocomplete="off"
                                size="3"
                                value="#{AgendaAmbienteColaboradorControle.ambienteSelecionado.codigo}"
                                id="modalAmbienteCodg-ambiente-codigo">
                                <a4j:support event="onchange" oncomplete="focusAt('addButton');" action="#{AgendaAmbienteColaboradorControle.acaoProcurarAmbiente}"
                                             reRender="modalAmbienteCodg-ambiente-descricao,modalAmbienteSuggestion, modalAmbienteCodg-ambiente-codigo, modalPanelErro"/>
                            </h:inputText>
                            <rich:spacer width="20px"/>
                            <h:inputText
                                onblur="blurinput(this);"
                                onfocus="focusinput(this);"
                                styleClass="form"
                                maxlength="20"
                                autocomplete="off"
                                onkeydown="return tabOnEnter(event, 'addButton');"
                                style="width:250px;"
                                value="#{AgendaAmbienteColaboradorControle.ambienteSelecionado.descricao}"
                                id="modalAmbienteCodg-ambiente-descricao">
                            </h:inputText>
                            <rich:suggestionbox id="modalAmbienteSuggestion" for="modalAmbienteCodg-ambiente-descricao"
                                                title="Comece a digitar o nome do Ambiente e selecione um"
                                                width="250"
                                                suggestionAction="#{AgendaAmbienteColaboradorControle.listarAmbientes}"
                                                var="item" fetchValue="#{item.descricao}"
                                                nothingLabel="Nenhum dado encontrado" status="statusHora" >
                                <h:column>
                                    <h:outputText value="#{item.descricao}"/>
                                </h:column>
                                <a4j:support event="onselect" reRender="modalAmbienteCodg-ambiente-codigo">
                                    <f:setPropertyActionListener target="#{AgendaAmbienteColaboradorControle.ambienteSelecionado}" value="#{item}" />
                                </a4j:support>
                            </rich:suggestionbox>
                            <a4j:commandButton
                                image="#{context}/imagens/estudio/adicionar.png"
                                id="addButton"
                                value="Adicionar"
                                oncomplete="#{AgendaAmbienteColaboradorControle.apresentarRichModalErro} ? focusAt('fecharButton') : focusAt('modalAmbienteCodg-ambiente-codigo');"
                                title="Adicionar" style="width:75px"
                                action="#{AgendaAmbienteColaboradorControle.acaoAdicionarAmbiente}"
                                reRender="agendaAmbienteProfissionalControle-listaAmbiente, modalAmbienteCodg-ambiente-descricao,
                                modalAmbienteCodg-ambiente-codigo, modalAmbienteSuggestion, modalPanelErro"/>
                        </h:panelGrid>

                        <rich:spacer height="5" />
                        <h:panelGrid columns="2">
                            <rich:scrollableDataTable
                                id="agendaAmbienteProfissionalControle-listaAmbiente"
                                var="item"
                                height="130px"
                                width="462px"
                                frozenColCount="1"
                                value="#{AgendaAmbienteColaboradorControle.listaAmbientesSelecionados}">
                                <rich:column sortable="false" width="25" id="columnCheckBox">
                                    <f:facet name="header">
                                        <h:selectBooleanCheckbox
                                            id="selecionarTodosAmbiente"
                                            value="#{AgendaAmbienteColaboradorControle.selecionarTodosAmbiente}">
                                            <a4j:support
                                                action="#{AgendaAmbienteColaboradorControle.acaoSelecionarTodosAmbientes}"
                                                event="onclick"
                                                status="none"
                                                reRender="itemSolicitacao-selecionado-ambiente">
                                            </a4j:support>
                                        </h:selectBooleanCheckbox>
                                    </f:facet>
                                    <h:selectBooleanCheckbox
                                        id="itemSolicitacao-selecionado-ambiente"
                                        value="#{item.selecionado}">
                                        <a4j:support action="#{AgendaAmbienteColaboradorControle.acaoSelecionarUmAmbiente}"
                                                     status="none"
                                                     event="onclick" reRender="selecionarTodosAmbiente">
                                        </a4j:support>
                                    </h:selectBooleanCheckbox>
                                </rich:column>

                                <rich:column width="65px" >
                                    <f:facet name="header">
                                        <h:outputText value="C�digo" />
                                    </f:facet>
                                    <h:outputText
                                        value="#{item.codigo}"
                                        style="float:right; margin-right: 5px;"/>
                                </rich:column>
                                <rich:column
                                    width="330px"
                                    sortBy="#{item.descricao}"
                                    filterBy="#{item.descricao}"
                                    filterEvent="onchange">
                                    <f:facet name="header">
                                        <h:outputText value="Descri��o" />
                                    </f:facet>
                                    <h:outputText
                                        value="#{item.descricao}"
                                        style="margin-left: 5px; position:relative;"/>
                                </rich:column>
                                <rich:column width="20px">
                                    <a4j:commandLink action="#{AgendaAmbienteColaboradorControle.acaoRemoverAmbiente}" reRender="agendaAmbienteProfissionalControle-listaAmbiente">
                                        <h:graphicImage value="#{context}/imagens/estudio/icon_delete.png" style="cursor:pointer" id="removerAmbienteSelecionado" />
                                        <f:setPropertyActionListener target="#{AgendaAmbienteColaboradorControle.ambienteSelecionado}" value="#{item}"/>
                                    </a4j:commandLink>
                                </rich:column>
                            </rich:scrollableDataTable>
                        </h:panelGrid>
                        <a4j:commandButton
                            image="#{context}/imagens/estudio/pesquisar.png"
                            action="#{AgendaAmbienteColaboradorControle.acaoPesquisarAmbiente}"
                            value="Pesquisar"
                            title="Pesquisar" style="width:75px; float:right; margin-right: 4px"
                            reRender="agendaGeral, panelFiltro, modalPanelErro" />
                    </rich:tab>
                </rich:tabPanel>
            </a4j:form>

        </rich:modalPanel>
        <%@include  file="includes/include_modal_dados_aluno.jsp" %>
        <%@include  file="includes/include_modal_agenda_aluno.jsp" %>

            <rich:modalPanel id="modalPanelErro" autosized="true" shadowOpacity="true"
                             showWhenRendered="#{AgendaAmbienteColaboradorControle.apresentarRichModalErro}" width="450"
                             height="80" onshow="focusAt('fecharButton');">
                <f:facet name="header">
                    <h:panelGroup>
                        <h:outputText value="Aten��o!"></h:outputText>
                    </h:panelGroup>
                </f:facet>
                <a4j:form id="formModalPanelErro" prependId="false">
                    <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">

                        <h:panelGrid columns="1" width="100%">
                            <h:outputText styleClass="mensagemDetalhada"
                                          value="#{AgendaAmbienteColaboradorControle.mensagemDetalhada}"/>
                        </h:panelGrid>
                    </h:panelGrid>
                    <rich:spacer height="05px"/>
                    <h:panelGrid style="position: relative; float:right; ">
                        <a4j:commandButton
                                image="#{context}/imagens/estudio/fechar.png"
                                id="fecharButton"
                                value="Fechar"
                                title="Fechar" status="statusHora"
                                focus="modalAmbienteCodg-ambiente-codigo"
                                action="#{AgendaAmbienteColaboradorControle.acaoFecharModalErro}"
                                reRender="modalPanelErro,panelAgendaAluno"/>
                    </h:panelGrid>
                </a4j:form>
            </rich:modalPanel>

            <%@include file="includes/include_modal_sucesso.jsp" %>
        </f:view>
    </body>
</html>

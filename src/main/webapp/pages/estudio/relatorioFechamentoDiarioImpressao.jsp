<%-- 
    Document   : relatorioFechamentoDiario
    Created on : Aug 15, 2012, 5:37:59 PM
    Author     : <PERSON> - GeoInova Soluções
--%>

<%@page contentType="text/html" pageEncoding="ISO-8859-1"%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@include file="includes/include_imports.jsp" %>
<%@taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>

<html>
    <head>
        <script type="text/javascript" src="${contexto}/script/relatorioFechamentoDiario.js"></script>
        <script type="text/javascript" src="${contexto}/script/basico.js"></script>
        <script type="text/javascript" src="${contexto}/script/jquery.js"></script>
        <script type="text/javascript" src="${contexto}/script/jquery.treeTable.js"></script>
        <link href="${contexto}/css/otimize.css" rel="stylesheet" type="text/css">
        <link href="${contexto}/css/estudio.css" rel="stylesheet" type="text/css">
        <link href="${contexto}/css/otimizeCRM.css" rel="stylesheet" type="text/css">
        <link href="${contexto}/css/jquery.treeTable.css" rel="stylesheet" type="text/css">
        <jsp:include page="includes/include_head.jsp" />

        <script type="text/javascript">
            $.noConflict();
        </script>
    </head>
    <body onload="atualizarTreeViewFD();">
        <f:view>
            <jsp:include page="/includes/include_carregando_ripple.jsp" flush="true"/>
        <title>Relatório Fechamento Diário</title>
        <a4j:form id="formRelFD">

            <table border="1" width="100%" >
                <tr>
                    <td width="5px">
                        <a4j:mediaOutput element="img" id="foto"  style="width:120px;height:160px "  cacheable="false"
                                         createContent="#{RelatorioFechamentoDiarioControle.paintFoto}"  value="#{ImagemData}" mimeType="image/jpeg" >
                            <f:param value="#{SuperControle.timeStamp}" name="time"/>
                            <f:param name="largura" value="120"/>
                            <f:param name="altura" value="160"/>
                        </a4j:mediaOutput>
                    </td>
                    <td>
                        <center>
                            <h1>Relatório Fechamento Diário</h1>
                        </center>
                    </td>
                    <td>
                        <table width="100%" >
                            <tr>
                                <td>Data: <h:outputText value="#{RelatorioFechamentoDiarioControle.dataAtual}" ></h:outputText> </td>
                            <tr>
                            <tr>
                                <td>Usuario: <h:outputText value="#{RelatorioFechamentoDiarioControle.nomeUsuarioLogado}" ></h:outputText></td>
                            <tr>
                        </table>
                    </td>
                </tr>
            </table>
            <table style="padding-top:20px">
                <tr>
                    <td style="font-weight: bold"> <h:outputText  value="Parâmetros:" ></h:outputText></td>
                    <td> <h:outputText  value="" ></h:outputText></td>
                </tr>
                <tr>
                    <td style="padding-left:20px"> <h:outputText value="Período:" ></h:outputText> </td>
                    <td>
                        <h:outputText value="#{RelatorioFechamentoDiarioControle.periodoInicial}" converter="dataConverter"/>
                        <h:outputText value=" a "/>
                        <h:outputText value="#{RelatorioFechamentoDiarioControle.periodoFinal}" converter="dataConverter"/>
                    </td>
                </tr>
                <c:if test="${RelatorioFechamentoDiarioControle.horaInicial != null}">
                    <tr>
                        <td style="padding-left:20px"> <h:outputText value="Horário:" ></h:outputText> </td>
                        <td>  
                            <h:outputText value="#{RelatorioFechamentoDiarioControle.horaInicial}" converter="timeConverter"/>
                            <h:outputText value=" às "/>
                            <h:outputText value="#{RelatorioFechamentoDiarioControle.horaFinal}" converter="timeConverter"/>
                        </td>
                    </tr>
                </c:if>
                <c:if test="${RelatorioFechamentoDiarioControle.usuarioVO.nome != ''}">
                    <tr>
                        <td style="padding-left:20px"> <h:outputText value="Usuário:" ></h:outputText> </td>
                        <td> <h:outputText value="#{RelatorioFechamentoDiarioControle.usuarioVO.nome}" ></h:outputText> </td>
                    </tr>
                </c:if>
                <c:if test="${RelatorioFechamentoDiarioControle.profissionalVO.pessoa.nome != ''}">
                    <tr>
                        <td style="padding-left:20px"> <h:outputText value="Profissional: " ></h:outputText> </td>
                        <td> <h:outputText value="#{RelatorioFechamentoDiarioControle.profissionalVO.pessoa.nome}" ></h:outputText> </td>
                    </tr>
                </c:if>
            </table>
            <%@include file="relatorioFechamentoDiarioTree.jsp" %>

            <h:panelGrid columns="1" columnClasses="colunaCentralizada" width="100%" style="background-color:#FFFFFF;">
                <h:commandLink
                    onclick="window.print();return false;">
                    <h:graphicImage value="/imagens/botoesCE/imprimir.png"  style="border: 0px; width: 65;"/>
                </h:commandLink>
            </h:panelGrid>
        </a4j:form>

    </f:view>
</body>
</html>



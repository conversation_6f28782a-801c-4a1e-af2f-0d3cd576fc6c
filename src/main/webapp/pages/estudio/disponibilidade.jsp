<%--
    Document   : disponibilidade
    Created on : Mar 2, 2012, 9:59:28 AM
    Author     : <PERSON>?nio - GeoInova Solu??es
--%>

<%@include file="includes/include_imports.jsp" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/functions" prefix="fn"%>
<c:set var="moduloSession" value="1" scope="session" />

<html>
    <head>
        <script type="text/javascript" src="${contexto}/script/basico.js"></script>
        <link href="${contexto}/css/otimize.css" rel="stylesheet" type="text/css">
        <link href="${contexto}/css/estudio.css" rel="stylesheet" type="text/css">
        <link href="${contexto}/css/otimizeCRM.css" rel="stylesheet" type="text/css">
        <script type="text/javascript" language="javascript">
                setDocumentCookie('popupsImportante', 'close',1);
        </script>
        <jsp:include page="includes/include_head.jsp" />

        <style type="text/css">
            TABLE.panelGridClass  {
                position: relative;
                vertical-align: top;
            }
            TABLE.panelGridClass td {
                position: absolute;
            }

            .column1 {
                vertical-align: top;
            }

            .rich-extdt-maindiv {
                height: auto !important;
            }

            .rich-extdt-maindiv, .extdt-innerdiv {
                position: relative !important;
            }

            .extdt-outerdiv {
                height: auto !important;
                overflow: visible !important;
            }

            .extdt-content {
                height: auto !important;
                min-height: 10px;
            }
            .columconfirm1 {

            }

            .columconfirm2 {
                vertical-align: bottom;   
            }

        </style>
        <style type="text/css">
            .rich-stglpanel-header {
                color: #0f4c6b;
            }

        </style>
        <style type="text/css">
            .rich-stglpanel-header {
                background-color: #ACBECE;
                border-color: #ACBECE;
                font-size: 12px;
            }
        </style>
    </head>
    <body>
        <f:view>
            <jsp:include page="/includes/include_carregando_ripple.jsp" flush="true"/>
        <title>
            <h:outputText value="Consulta de Disponibilidade"/>
        </title>
        <h:form id="agendaGeral" prependId="false">
            <h:panelGroup layout="block" style="display: inline-table;width: 100%;" styleClass="fundoCinza">
                <h:panelGroup layout="block" styleClass="bgtop topoZW" rendered="#{MenuControle.apresentarTopo}">
                    <jsp:include page="../../include_topo_novo.jsp" flush="true"/>
                    <jsp:include page="../../include_menu_zw_flat.jsp" flush="true"/>
                    <rich:jQuery selector=".item6" query="addClass('menuItemAtual')"/>
                </h:panelGroup>

                <h:panelGroup layout="block" styleClass="caixaCorpo">
                    <h:panelGroup layout="block" style="height: 80%;width: 100%">
                        <h:panelGroup layout="block" styleClass="caixaMenuLatel">
                            <h:panelGroup layout="block" styleClass="container-imagem container-conteudo-central">
                                <h:panelGroup layout="block" styleClass="container-box zw_ui especial ">
                                    <h:panelGroup styleClass="container-box-header" layout="block">
                                        <h:panelGroup layout="block" styleClass="margin-box">
                                            <h:outputText value="Disponibilidade" styleClass="container-header-titulo"/>
                                            <h:outputLink styleClass="linkWiki"
                                                          value="#"
                                                          title="Clique e saiba mais: Disponibilidade"
                                                          target="_blank" >
                                                <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                                            </h:outputLink>
                                        </h:panelGroup>
                                    </h:panelGroup>
                                    <h:panelGroup layout="block" styleClass="margin-box">
                                        <h:outputText styleClass="text" value="Filtros" style="font-weight:bold;color:#0f4c6b;font-size:10pt;"/>
                                        <rich:spacer height="25px"/>
                                        <rich:simpleTogglePanel switchType="client" label="Servi�o" style="margin-top: 8px" opened="false" onexpand="false">
                                            <h:panelGrid columns="3" id="dadosServico">
                                                <h:outputLabel
                                                        value="C�digo" styleClass="texto_disponibilidade"/>
                                                <rich:spacer width="20px"/>
                                                <h:outputLabel
                                                        value="Descri��o" styleClass="texto_disponibilidade" />

                                                <h:inputText
                                                        maxlength="4"
                                                        autocomplete="off"
                                                        onkeydown="return tabOnEnter(event, 'panelFiltroServico-servico-descricao');"
                                                        size="3"
                                                        onblur="blurinput(this);" onfocus="focusinput(this); getById('panelFiltroServico-servico-codigo').select();"
                                                        styleClass="form"
                                                        value="#{disponibilidadeControle.produtoVO.codigo}"
                                                        id="panelFiltroServico-servico-codigo">
                                                    <a4j:support event="onchange" oncomplete="focusAt('add');" action="#{disponibilidadeControle.acaoProcurarServico}"
                                                                 reRender="disponibilidadeControle-listaServico,modalServicoSuggestion,modalPanelErro,panelFiltroServico-servico-codigo"/>
                                                </h:inputText>
                                                <rich:spacer width="20px"/>
                                                <h:panelGrid columns="4">
                                                    <h:inputText
                                                            maxlength="50"
                                                            autocomplete="off"
                                                            style="width: 290px;"
                                                            onblur="blurinput(this);" onfocus="focusinput(this); getById('panelFiltroServico-servico-descricao').select();"
                                                            onkeydown="return tabOnEnter(event, 'add');"
                                                            styleClass="form"
                                                            value="#{disponibilidadeControle.produtoVO.descricao}"
                                                            id="panelFiltroServico-servico-descricao">
                                                    </h:inputText>
                                                    <rich:suggestionbox
                                                            id="modalServicoSuggestion"
                                                            width="290"
                                                            status="statusHora"
                                                            immediate="true"
                                                            for="panelFiltroServico-servico-descricao"
                                                            suggestionAction="#{disponibilidadeControle.listarServico}"
                                                            minChars="1" rowClasses="30"
                                                            fetchValue="#{item.descricao}"
                                                            var="item"
                                                            nothingLabel="Nenhum dado encontrado" >
                                                        <h:column>
                                                            <h:outputText value="#{item.descricao}"/>
                                                        </h:column>
                                                        <a4j:support event="onselect" reRender="disponibilidadeControle-listaServico, panelFiltroServico-servico-descricao, modalServicoSuggestion" action="#{disponibilidadeControle.acaoAdicionarServico}" focus="panelFiltroServico-servico-descricao">
                                                            <f:setPropertyActionListener
                                                                    target="#{disponibilidadeControle.produtoVO}"
                                                                    value="#{item}" />
                                                        </a4j:support>
                                                    </rich:suggestionbox>
                                                </h:panelGrid>
                                            </h:panelGrid>

                                            <rich:spacer height="5" />
                                            <h:panelGrid columns="2">
                                                <rich:scrollableDataTable
                                                        id="disponibilidadeControle-listaServico"
                                                        onRowClick="getById('disponibilidadeControle-listaServico:'+ this.rowIndex +':filtro').onclick();"
                                                        var="item"
                                                        height="132px"
                                                        width="472px"
                                                        frozenColCount="1"
                                                        value="#{disponibilidadeControle.listaServicoTabela}">

                                                    <rich:column sortable="false" width="38px">
                                                        <f:facet name="header">
                                                            <h:selectBooleanCheckbox
                                                                    id="selecionarTodosProduto"
                                                                    value="#{disponibilidadeControle.selecionarTodosItemServico}">
                                                                <a4j:support
                                                                        status="statusHora"
                                                                        action="#{disponibilidadeControle.acaoSelecionarTodosServico}"
                                                                        event="onclick"
                                                                        reRender="itemSolicitacao-selecionado-servico">
                                                                </a4j:support>
                                                            </h:selectBooleanCheckbox>
                                                        </f:facet>
                                                        <h:selectBooleanCheckbox
                                                                id="itemSolicitacao-selecionado-servico"
                                                                style="margin-left: 14px;"
                                                                value="#{item.selecionado}">
                                                            <a4j:support
                                                                    status="statusHora"
                                                                    action="#{disponibilidadeControle.acaoSelecionarUmServico}"
                                                                    event="onclick"
                                                                    reRender="selecionarTodosProduto">
                                                            </a4j:support>
                                                        </h:selectBooleanCheckbox>
                                                    </rich:column>

                                                    <rich:column
                                                            width="70px">
                                                        <f:facet name="header">
                                                            <h:outputText value="C�digo" styleClass="texto_disponibilidade" />
                                                        </f:facet>
                                                        <h:outputText
                                                                value="#{item.codigo}"
                                                                style="float:right; margin-right: 5px;"/>
                                                    </rich:column>
                                                    <rich:column
                                                            width="321px"
                                                            sortBy="#{item.descricao}"
                                                            filterBy="#{item.descricao}"
                                                            filterEvent="onchange">
                                                        <f:facet name="header">
                                                            <h:outputText value="Descri��o" styleClass="texto_disponibilidade" />
                                                        </f:facet>
                                                        <h:outputText
                                                                value="#{item.descricao}"
                                                                style="margin-left: 5px; position:relative;"/>
                                                    </rich:column>
                                                    <rich:column width="20px">
                                                        <a4j:commandLink action="#{disponibilidadeControle.acaoRemoverServico}" reRender="disponibilidadeControle-listaServico">
                                                            <h:graphicImage value="#{context}/imagens/estudio/icon_delete.png" style="cursor:pointer" id="rmvItem" />
                                                            <f:setPropertyActionListener target="#{disponibilidadeControle.produtoVO}" value="#{item}"/>
                                                        </a4j:commandLink>
                                                    </rich:column>
                                                </rich:scrollableDataTable>
                                            </h:panelGrid>
                                        </rich:simpleTogglePanel>
                                        <rich:simpleTogglePanel switchType="client" label="Profissional" opened="false" onexpand="false">
                                            <h:panelGroup id="co">
                                                <rich:dataGrid value="#{disponibilidadeControle.listaColaboradorVO}"
                                                               var="profissional" width="100%" columns="3">
                                                    <rich:column>
                                                        <h:selectBooleanCheckbox value="#{profissional.colaboradorEscolhido}"/>
                                                        <h:outputText value="#{profissional.pessoa.nome}">

                                                        </h:outputText>
                                                    </rich:column>
                                                </rich:dataGrid>
                                            </h:panelGroup>
                                        </rich:simpleTogglePanel>
                                        <rich:simpleTogglePanel switchType="client" label="Ambiente" opened="false" onexpand="false">
                                            <h:panelGroup id="am">
                                                <rich:dataGrid value="#{disponibilidadeControle.listaAmbiente}"
                                                               var="ambiente" width="100%" columns="3">
                                                    <rich:column>
                                                        <h:selectBooleanCheckbox value="#{ambiente.ambienteEscolhido}"/>
                                                        <h:outputText value="#{ambiente.descricao}">

                                                        </h:outputText>
                                                    </rich:column>
                                                </rich:dataGrid>
                                            </h:panelGroup>
                                        </rich:simpleTogglePanel>
                                        <rich:simpleTogglePanel id="abaDiaHora" switchType="client" label="Dia e Hora" opened="true" onexpand="false">
                                            <h:panelGrid columns="1">
                                                <h:panelGrid columns="2">
                                                    <h:outputLabel styleClass="texto_disponibilidade"
                                                                   value="Dia da Semana: "/>

                                                    <h:selectManyCheckbox styleClass="texto_disponibilidade"
                                                                          id="diaDaSemanaSelect"
                                                                          converter="diaDaSemanaConverter"
                                                                          value="#{disponibilidadeControle.listaDiaDaSemana}">
                                                        <f:selectItems value="#{disponibilidadeControle.diaDaSemanaSelect}" />
                                                    </h:selectManyCheckbox>

                                                </h:panelGrid>
                                                <h:panelGrid columns="2">
                                                    <h:selectBooleanCheckbox id="apresentarHorariosFixos" value="#{disponibilidadeControle.gerarApenasIntervalo}">
                                                        <a4j:support event="onclick"
                                                                     action="#{disponibilidadeControle.selecionarIntervalosFixos}"
                                                                     reRender="abaDiaHora" />
                                                    </h:selectBooleanCheckbox>
                                                    <h:outputText styleClass="tituloCampos" value="Apresentar Hor�rios Fixos" />
                                                </h:panelGrid>
                                                <h:panelGroup id ="listahorarios" rendered="#{!disponibilidadeControle.gerarApenasIntervalo}" >
                                                    <h:panelGrid columns="2">
                                                        <h:outputLabel
                                                                value="Hor�rios: " styleClass="texto_disponibilidade"/>

                                                        <h:selectManyCheckbox
                                                                id="horariosSelect"
                                                                disabled="#{disponibilidadeControle.gerarApenasIntervalo}"
                                                                layout="lineDirection"
                                                                style="text-align: left;"
                                                                styleClass="texto_disponibilidade"
                                                                value="#{disponibilidadeControle.listaHoraSelecionada}">
                                                            <f:selectItems value="#{disponibilidadeControle.listaHora}" />
                                                        </h:selectManyCheckbox>

                                                    </h:panelGrid>
                                                </h:panelGroup>
                                                <h:panelGroup id ="horariosFixos" rendered="#{disponibilidadeControle.gerarApenasIntervalo}" >
                                                    <h:panelGrid columns="5">
                                                        <h:outputLabel id="lb-horaInicioFixo"
                                                                       styleClass="classLabel texto_agenda"
                                                                       value="Hora Inicial *:"/>
                                                        <h:inputText disabled="#{!disponibilidadeControle.gerarApenasIntervalo}" id="horarioInicioFixo" size="10" maxlength="5"
                                                                     onblur="blurinput(this);"
                                                                     onfocus="focusinput(this);"
                                                                     styleClass="form"
                                                                     value="#{disponibilidadeControle.timeHorarioInicialFixo}"/>
                                                        <rich:spacer width="10px"/>
                                                        <h:outputLabel  id="lb-horaFinalFixo"
                                                                        styleClass="classLabel texto_agenda"
                                                                        value="Hora Final *:"/>
                                                        <h:inputText disabled="#{!disponibilidadeControle.gerarApenasIntervalo}" id="horarioFinalFixo" size="10" maxlength="5"
                                                                     onblur="blurinput(this);"
                                                                     onfocus="focusinput(this);"
                                                                     styleClass="form"
                                                                     value="#{disponibilidadeControle.timeHorarioFinalFixo}"/>
                                                    </h:panelGrid>
                                                </h:panelGroup>
                                                <h:panelGrid columns="11" id="periodos">
                                                    <h:outputLabel
                                                            value="Per�odo:" styleClass="texto_disponibilidade" />
                                                    <rich:calendar
                                                            locale="pt_BR"
                                                            inputSize="10"
                                                            inputClass="form"
                                                            oninputblur="blurinput(this);"
                                                            oninputfocus="focusinput(this);"
                                                            oninputchange="return validar_Data(this.id);"
                                                            oninputkeypress="return mascara(this, '99/99/9999', event);"
                                                            datePattern="dd/MM/yyyy"
                                                            enableManualInput="true"
                                                            zindex="2"
                                                            showWeeksBar="false"
                                                            value="#{disponibilidadeControle.periodoInicial}"
                                                            id="disponibilidadeControle-periodoInicial"
                                                            popup="true"  styleClass="texto_disponibilidade">
                                                    </rich:calendar>

                                                    <h:outputLabel
                                                            value=" a " styleClass="texto_disponibilidade"/>
                                                    <rich:calendar
                                                            locale="pt_BR"
                                                            inputSize="10"
                                                            inputClass="form"
                                                            oninputblur="blurinput(this);"
                                                            oninputfocus="focusinput(this);"
                                                            oninputchange="return validar_Data(this.id);"
                                                            oninputkeypress="return mascara(this, '99/99/9999', event);"
                                                            datePattern="dd/MM/yyyy"
                                                            enableManualInput="true"
                                                            zindex="2"
                                                            showWeeksBar="false"
                                                            value="#{disponibilidadeControle.periodoFinal}"
                                                            id="disponibilidadeControle-periodoFinal"
                                                            popup="true" styleClass="texto_disponibilidade" >
                                                    </rich:calendar>

                                                    <rich:spacer width="10px"/>
                                                    <h:outputLabel
                                                            value="Intervalo:" styleClass="texto_disponibilidade"/>
                                                    <h:selectOneMenu
                                                            id="disponibilidadeControle-listPeriodo"
                                                            value="#{disponibilidadeControle.periodoEnum}"
                                                            onblur="blurinput(this);"
                                                            onfocus="focusinput(this);"
                                                            styleClass="form">
                                                        <f:selectItems value="#{disponibilidadeControle.periodoSelect}" />
                                                        <a4j:support  event="onchange"
                                                                      action="#{disponibilidadeControle.acaoPeriodoIntervalo}"
                                                                      reRender="disponibilidadeControle-periodoInicial,disponibilidadeControle-periodoFinal" />
                                                    </h:selectOneMenu>
                                                    <rich:spacer width="20px;"/>
                                                    <h:selectBooleanCheckbox rendered="#{!disponibilidadeControle.gerarApenasIntervalo}"  id="apresentarHorariosQuebrados" value="#{disponibilidadeControle.apresentarHorariosQuebrados}"  />
                                                    <h:outputText rendered="#{!disponibilidadeControle.gerarApenasIntervalo}"  styleClass="tituloCampos" value="Apresentar Hor�rios Quebrados" />
                                                </h:panelGrid>
                                            </h:panelGrid>
                                        </rich:simpleTogglePanel>
                                        <rich:spacer height="35px"/>
                                        <a4j:commandButton
                                                image="#{context}/imagens/estudio/pesquisar.png"
                                                title="Pesquisa Inteligente"
                                                reRender="disponibilidadeControle-disponibilidade, periodos, modalPanelErro, panelRegistro"
                                                action="#{disponibilidadeControle.pesquisar}"/>
                                        <rich:spacer width="20px"/>
                                        <a4j:commandButton
                                                image="#{context}/imagens/estudio/limpar.png"
                                                reRender="agendaGeral"
                                                title="Limpar Dados"
                                                action="#{disponibilidadeControle.limparDados}"/>
                                        <h:panelGrid columns="2" style="width:100%" columnClasses="column1, column1 ">

                                            <rich:column width="100%" style="vertical-align: top; border-bottom: 0; border-right: 0;">

                                                <rich:spacer height="05px" />
                                                <h:panelGrid columns="3">

                                                    <h:panelGrid columns="3" id="panelRegistro">
                                                        <h:outputText
                                                                id="registro"
                                                                value="N�mero de registros:" style="font-size:10; "
                                                                rendered="#{fn:length(disponibilidadeControle.listaDisponibilidade) > 0}" />
                                                        <h:outputText
                                                                id="tamanho"
                                                                style="font-size:10;"
                                                                rendered="#{fn:length(disponibilidadeControle.listaDisponibilidade) > 0}"
                                                                value="#{fn:length(disponibilidadeControle.listaDisponibilidade)}"/>
                                                        <h:outputText
                                                                id="disponivel"
                                                                value=" dispon�veis" style="font-size:10;"
                                                                rendered="#{fn:length(disponibilidadeControle.listaDisponibilidade) > 0}" />
                                                        <h:outputText
                                                                id="SemDisponibilidade"
                                                                value="N�o h� disponibilidade para os filtros informados!" style="font-size:12; color: red"
                                                                rendered="#{disponibilidadeControle.apresentarMensagemSemDisponibilidade}" />
                                                    </h:panelGrid>
                                                </h:panelGrid>

                                                <rich:dataTable
                                                        id="disponibilidadeControle-disponibilidade"
                                                        width="100%"
                                                        rows="50"
                                                        style="border: 0;"
                                                        reRender="ds"
                                                        headerClass="consulta"
                                                        rowClasses="linhaImpar, linhaPar"
                                                        columnClasses="colunaAlinhamento"
                                                        value="#{disponibilidadeControle.listaDisponibilidade}"
                                                        var="item">
                                                    <rich:column
                                                            id="item-servico"
                                                            sortBy="#{item.produtoVO.descricao}"
                                                            dir="RTL">
                                                        <f:facet name="header">
                                                            <h:outputText value="Servi�o"/>
                                                        </f:facet>
                                                        <a4j:commandLink value="#{item.produtoVO.descricao}" action="#{disponibilidadeControle.editarDisponibilidade}"
                                                                         reRender="modalPanelMaisDados"/>
                                                    </rich:column>
                                                    <rich:column
                                                            id="item-profissional"
                                                            sortBy="#{item.colaboradorVO.pessoa.nome}"
                                                            dir="RTL">
                                                        <f:facet name="header">
                                                            <h:outputText value="Profissional"/>
                                                        </f:facet>
                                                        <a4j:commandLink value="#{item.colaboradorVO.pessoa.nome}" action="#{disponibilidadeControle.editarDisponibilidade}"
                                                                         reRender="modalPanelMaisDados"/>
                                                    </rich:column>
                                                    <rich:column
                                                            id="item-ambiente"
                                                            sortBy="#{item.ambienteVO.descricao}"
                                                            dir="RTL">
                                                        <f:facet name="header">
                                                            <h:outputText value="Ambiente"/>
                                                        </f:facet>
                                                        <a4j:commandLink value="#{item.ambienteVO.descricao}" action="#{disponibilidadeControle.editarDisponibilidade}"
                                                                         reRender="modalPanelMaisDados"/>
                                                    </rich:column>
                                                    <rich:column
                                                            id="item-tipo"
                                                            dir="RTL">
                                                        <f:facet name="header">
                                                            <h:outputText value="TH"/>
                                                        </f:facet>
                                                        <a4j:commandLink value="#{item.tipoHorarioVO.descricao}" action="#{disponibilidadeControle.editarDisponibilidade}"
                                                                         reRender="modalPanelMaisDados"/>
                                                    </rich:column>
                                                    <rich:column
                                                            id="item-dia"
                                                            dir="RTL">
                                                        <f:facet name="header">
                                                            <h:outputText value="Dia da Semana"/>
                                                        </f:facet>
                                                        <a4j:commandLink value="#{item.diaSemanaEnum.descricao}" action="#{disponibilidadeControle.editarDisponibilidade}"
                                                                         reRender="modalPanelMaisDados"/>
                                                    </rich:column>
                                                    <rich:column
                                                            id="item-hora">
                                                        <f:facet name="header">
                                                            <h:outputText value="Hor�rio"/>
                                                        </f:facet>
                                                        <a4j:commandLink value="#{item.horaInicial_Apresentar}" action="#{disponibilidadeControle.editarDisponibilidade}"
                                                                         reRender="modalPanelMaisDados">
                                                        </a4j:commandLink>
                                                    </rich:column>
                                                    <rich:column
                                                            id="item-ml"
                                                            filterEvent="onkeyup"
                                                            dir="RTL">
                                                        <f:facet name="header">
                                                            <h:outputText value="Resrvd."/>
                                                        </f:facet>
                                                        <a4j:commandLink value="#{item.matriculados}" action="#{disponibilidadeControle.editarDisponibilidade}"
                                                                         reRender="modalPanelMaisDados"/>
                                                    </rich:column>
                                                    <rich:column
                                                            id="item-mlC"
                                                            filterEvent="onkeyup"
                                                            dir="RTL">
                                                        <f:facet name="header">
                                                            <h:outputText value="Capacd."/>
                                                        </f:facet>
                                                        <a4j:commandLink value="#{item.produtoVO.capacidade}" action="#{disponibilidadeControle.editarDisponibilidade}"
                                                                         reRender="modalPanelMaisDados"/>
                                                    </rich:column>
                                                    <rich:column
                                                            id="item-data"
                                                            sortBy="#{item.dataMes}"
                                                            filterEvent="onkeyup"
                                                            dir="RTL">
                                                        <f:facet name="header">
                                                            <h:outputText value="Data"/>
                                                        </f:facet>
                                                        <a4j:commandLink value="#{item.dataMes_Apresentar}" action="#{disponibilidadeControle.editarDisponibilidade}"
                                                                         reRender="modalPanelMaisDados">
                                                        </a4j:commandLink>
                                                    </rich:column>

                                                    <rich:column id="colunaSelecionar" sortable="false" width="38px">
                                                        <f:facet name="header">
                                                            <h:selectBooleanCheckbox
                                                                    id="selecionarTodosDisponibilidade"
                                                                    value="#{disponibilidadeControle.selecionarTodosItemDisponibilidade}">
                                                                <a4j:support
                                                                        action="#{disponibilidadeControle.acaoSelecionarTodosDisponibilidade}"
                                                                        event="onclick"
                                                                        reRender="itemSolicitacao-selecionado-disponibilidade">
                                                                </a4j:support>
                                                            </h:selectBooleanCheckbox>
                                                        </f:facet>
                                                        <h:selectBooleanCheckbox
                                                                id="itemSolicitacao-selecionado-disponibilidade"
                                                                style="margin-left: 14px;"
                                                                value="#{item.selecionado}">
                                                            <a4j:support
                                                                    status="statusHora"
                                                                    action="#{disponibilidadeControle.acaoSelecionarUmDisponibilidade}"
                                                                    event="onclick"
                                                                    reRender="selecionarTodosDisponibilidade">
                                                            </a4j:support>
                                                        </h:selectBooleanCheckbox>
                                                    </rich:column>

                                                    <f:facet name="footer">
                                                        <rich:datascroller id="ds" fastControls="auto" boundaryControls="auto" stepControls="auto" for="disponibilidadeControle-disponibilidade" />
                                                    </f:facet>

                                                </rich:dataTable>

                                                <%--<h:panelGrid columns="1">
                                                    <rich:toggleControl for="tooglePanel" switchToState="open">
                                                        <a4j:commandButton
                                                            status="statusHora"
                                                            immediate="true"
                                                            ignoreDupResponses="true"
                                                            styleClass="texto_disponibilidade"
                                                            reRender="tooglePanel"
                                                            action="#{disponibilidadeControle.acaoAdicionarAgendamento}"
                                                            value="Adicionar Agendamento"/>
                                                    </rich:toggleControl>
                                                </h:panelGrid>--%>
                                            </rich:column>

                                            <rich:togglePanel switchType="ajax" label="Detalhes"
                                                              id="tooglePanel"
                                                              stateOrder="closed,open">

                                                <f:facet name="closed">
                                                    <rich:toggleControl>
                                                        <h:outputText value="Detalhes" />
                                                    </rich:toggleControl>
                                                </f:facet>

                                                <f:facet name="open">

                                                    <h:panelGrid columns="1" >
                                                        <rich:toggleControl switchToState="closed" >
                                                            <h:outputText value="Detalhes" style="float:right;"/>
                                                        </rich:toggleControl>


                                                        <rich:extendedDataTable
                                                                value="#{disponibilidadeControle.listaDisponibilidadeAgendado}"
                                                                rows="50"
                                                                var="item" id="disponibilidadeControle-disponibilidadeSelecionado"
                                                                headerClass="consulta"
                                                                reRender="ds2"
                                                                width="263px"
                                                                noDataLabel="Sem dados"
                                                                rowClasses="linhaImpar, linhaPar"
                                                                columnClasses="colunaAlinhamento"
                                                                onRowMouseOver="this.style.backgroundColor='#F1F1F1'"
                                                                onRowMouseOut="this.style.backgroundColor='#{a4jSkin.tableBackgroundColor}'"
                                                                groupingColumn="item-servicoSelecionado">

                                                            <rich:column
                                                                    id="item-servicoSelecionado"
                                                                    rendered="false"
                                                                    sortable="true"
                                                                    selfSorted="true"
                                                                    sortBy="#{item.produtoVO.descricao}">
                                                                <f:facet name="header">
                                                                    <h:outputText value="Servi�o"/>
                                                                </f:facet>
                                                                <h:outputText value="#{item.produtoVO.descricao}" />
                                                            </rich:column>

                                                            <rich:column
                                                                    id="item-data"
                                                                    width="70px"
                                                                    dir="RTL">
                                                                <f:facet name="header">
                                                                    <h:outputText value="Data"/>
                                                                </f:facet>
                                                                <h:outputText value="#{item.dataMes}" converter="dataConverter"/>
                                                            </rich:column>

                                                            <rich:column
                                                                    id="item-hora"
                                                                    width="100px"
                                                                    dir="RTL">
                                                                <f:facet name="header">
                                                                    <h:outputText value="Hor�rio"/>
                                                                </f:facet>
                                                                <h:outputText value="#{item.horaInicial_Apresentar}"  />
                                                            </rich:column>

                                                            <rich:column
                                                                    width="70px"
                                                                    id="item-preco">
                                                                <f:facet name="header">
                                                                    <h:outputText value="Pre�o"/>
                                                                </f:facet>
                                                                <h:outputText value="#{item.produtoVO.valorFinal}" />
                                                            </rich:column>

                                                            <rich:column width="23px">
                                                                <a4j:commandLink
                                                                        action="#{disponibilidadeControle.acaoRemoverAgendamento}"
                                                                        ignoreDupResponses="true"
                                                                        reRender="disponibilidadeControle-disponibilidadeSelecionado,disponibilidadeControle-disponibilidade">
                                                                    <h:graphicImage value="#{context}/imagens/estudio/icon_delete.png" style="cursor:pointer" id="rmvAgendado" />
                                                                    <f:setPropertyActionListener target="#{disponibilidadeControle.disponibilidadeVO}" value="#{item}"/>
                                                                </a4j:commandLink>
                                                            </rich:column>

                                                            <f:facet name="footer">

                                                                <h:panelGrid columns="1">
                                                                    <h:panelGrid columns="3">
                                                                        <h:outputText value="Total"/>
                                                                        <rich:spacer/>
                                                                        <h:outputText value="#{disponibilidadeControle.totalPrecoAgendamento}">
                                                                            <f:convertNumber   pattern="$####.00"  />
                                                                        </h:outputText>
                                                                    </h:panelGrid>
                                                                    <rich:datascroller
                                                                            id="ds2" fastControls="auto" boundaryControls="hide" stepControls="auto"
                                                                            for="disponibilidadeControle-disponibilidadeSelecionado" />

                                                                </h:panelGrid>

                                                            </f:facet>

                                                        </rich:extendedDataTable>

                                                        <a4j:commandButton
                                                                image="#{context}/imagens/estudio/concluir_agendamento.png"
                                                                action="#{disponibilidadeControle.acaoConcluirAgendamento}"
                                                                styleClass="texto_disponibilidade"
                                                                reRender="formModalPanelConfirmacao"
                                                                title="Bot�o de confirma��o das agendas para o modo compra"
                                                                oncomplete="focusAt('panelPesquisarAluno-clienteConfirmacao-codigo');"
                                                                onclick="#{rich:component('modalPanelConfirmacao')}.show()"
                                                                value="Concluir Agendamento"/>
                                                    </h:panelGrid>

                                                </f:facet>
                                            </rich:togglePanel>

                                        </h:panelGrid>
                                    </h:panelGroup>
                                <a4j:commandButton style="visibility: hidden;" reRender="panelExpiracaoSenha"
                                                   id="btnAtualizaPagina">
                                </a4j:commandButton>
                              </h:panelGroup>
                            </h:panelGroup>

                            <jsp:include page="includes/include_box_menulateral_sc.jsp" flush="true"/>
                        </h:panelGroup>
                    </h:panelGroup>
                </h:panelGroup>

                <jsp:include page="../../include_rodape_flat.jsp" flush="true"/>
            </h:panelGroup>
            <div id="ToolbarZillyonWeb" style="display: block; margin-bottom: 0px; ">
                <h:panelGrid columns="1" style="float:right; margin-right:05%;">
                    <rich:toggleControl for="tooglePanel" switchToState="open">
                        <a4j:commandButton
                            image="#{context}/imagens/estudio/adicionar_agendamento.png"
                            status="statusHora"
                            immediate="true"
                            title="Adiciona as agendas selecionada para o modo compra."
                            ignoreDupResponses="true"
                            styleClass="texto_disponibilidade"
                            reRender="tooglePanel"
                            action="#{disponibilidadeControle.acaoAdicionarAgendamento}"
                            value="Adicionar Agendamento"/>
                    </rich:toggleControl>
                </h:panelGrid>
            </div>
        </h:form>

        <rich:modalPanel id="modalPanelErro" autosized="true" shadowOpacity="true"
                         onshow="focusAt('btnFecharErro');"
                         zindex="999"
                         showWhenRendered="#{disponibilidadeControle.apresentarRichModalErro}"
                         width="450" height="150">
            <f:facet name="header">
                <h:panelGroup>
                    <h:outputText value="Aten��o!"></h:outputText>
                </h:panelGroup>
            </f:facet>
            <a4j:form id="formModalPanelErro" prependId="false">
                <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada" >

                    <h:panelGrid columns="1" width="100%">
                        <h:outputText styleClass="mensagemDetalhada" value="#{disponibilidadeControle.mensagemDetalhada}"/>
                    </h:panelGrid>
                </h:panelGrid>
                <rich:spacer height="05px"/>
                <h:panelGrid style="position: relative; float:right; ">
                    <a4j:commandButton
                        image="#{context}/imagens/estudio/fechar.png"
                        value="Fechar"
                        id="btnFecharErro"
                        focus="panelFiltroServico-servico-codigo"
                        onclick="#{rich:component('modalPanelErro')}.hide();"
                        action="#{disponibilidadeControle.acaoFecharModalErro}" reRender="modalPanelErro"/>
                </h:panelGrid>
            </a4j:form>
        </rich:modalPanel>

        <rich:modalPanel id="modalPanelConfirmacao" autosized="true" shadowOpacity="true"
                         onshow="focusAt('panelPesquisarAluno-clienteConfirmacao-codigo');"
                         showWhenRendered="#{disponibilidadeControle.panelModalConfirmacao}"
                         width="1000" height="150">
            <f:facet name="header">
                <h:panelGroup>
                    <h:outputText value="Confirma��o de Agendamento"></h:outputText>
                </h:panelGroup>
            </f:facet>
            <f:facet name="controls">
                <h:panelGroup>
                    <h:graphicImage value="#{context}/imagens/close.png" style="cursor:pointer" id="hidelinkmodalPanelConfirmacao" />
                    <rich:componentControl for="modalPanelConfirmacao" attachTo="hidelinkmodalPanelConfirmacao"
                                           operation="hide" event="onclick" />
                </h:panelGroup>
            </f:facet>
            <a4j:form id="formModalPanelConfirmacao" prependId="false">

                <h:panelGrid columns="3" id="dadosAlunoConfirmacao">
                    <h:outputLabel
                        value="Matr�cula" />
                    <rich:spacer width="20px"/>
                    <h:outputLabel
                        value="Nome" />

                    <h:inputText
                        maxlength="10"
                        autocomplete="off"
                        onblur="blurinput(this);" onfocus="focusinput(this); getById('panelPesquisarAluno-clienteConfirmacao-codigo').select();"
                        styleClass="form"
                        onkeydown="return tabOnEnter(event, 'panelPesquisarAluno-clienteConfirmacao-descricao');"
                        size="9"
                        value="#{disponibilidadeControle.clienteVO.codigoMatricula}"
                        id="panelPesquisarAluno-clienteConfirmacao-codigo">
                        <a4j:support event="onchange" oncomplete="focusAt('btnConfirmarConfirmacao');" action="#{disponibilidadeControle.acaoProcurarCliente}"
                                     reRender="panelPesquisarAluno-clienteConfirmacao-descricao,modalClienteConfirmacaoSuggestion,modalPanelErro,
                                     disponibilidadeControle-listaAAgendar,panelmensagem-listaAAgendar,modalPanelConfirmacao"/>
                    </h:inputText>
                    <rich:spacer width="20px"/>
                    <h:panelGrid columns="4">

                        <h:inputText
                            maxlength="50"
                            autocomplete="off"
                            style="width: 450px;"
                            onkeydown="return tabOnEnter(event, 'btnConfirmarConfirmacao');"
                            onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form"
                            value="#{disponibilidadeControle.clienteVO.pessoa.nome}"
                            id="panelPesquisarAluno-clienteConfirmacao-descricao">
                        </h:inputText>

                        <rich:suggestionbox
                            id="modalClienteConfirmacaoSuggestion"
                            for="panelPesquisarAluno-clienteConfirmacao-descricao"
                            status="statusHora"
                            suggestionAction="#{disponibilidadeControle.listarClientes}"
                            minChars="1" rowClasses="30"
                            width="400"
                            fetchValue="#{item.pessoa.nome}"
                            var="item"
                            nothingLabel="Nenhum dado encontrado">
                            <h:column>
                                <h:outputText value="#{item.pessoa.nome}"/>
                            </h:column>
                            <a4j:support event="onselect" oncomplete="focusAt('btnConfirmarConfirmacao');" action="#{disponibilidadeControle.carregarListaAAgendar}"
                                         reRender="panelPesquisarAluno-clienteConfirmacao-codigo,disponibilidadeControle-listaAAgendar,
                                         panelmensagem-listaAAgendar,modalPanelConfirmacao">
                                <f:setPropertyActionListener
                                    target="#{disponibilidadeControle.clienteVO}"
                                    value="#{item}" />
                            </a4j:support>
                        </rich:suggestionbox>
                    </h:panelGrid>
                </h:panelGrid>

                <f:verbatim rendered="#{disponibilidadeControle.apresentarListaAgendados}">
                    <div id="tableConfirmDisponibilidade">
                        <rich:dataTable
                            id="disponibilidadeControle-confirmacao-disponibilidadeAgendado"
                            width="100%"
                            headerClass="consulta"
                            rendered="#{disponibilidadeControle.apresentarListaAgendados}"
                            columnClasses="colunaAlinhamento"
                            value="#{disponibilidadeControle.listaDisponibilidadeConcluido}"
                            var="item">
                            <rich:column
                                id="item-confirmacao-servico"
                                dir="RTL">
                                <f:facet name="header">
                                    <h:outputText value="Servi�o"/>
                                </f:facet>
                                <h:outputText value="#{item.produtoVO.descricao}" />
                            </rich:column>
                            <rich:column
                                id="item-confirmacao-profissional"
                                dir="RTL">
                                <f:facet name="header">
                                    <h:outputText value="Profissional"/>
                                </f:facet>
                                <h:outputText value="#{item.colaboradorVO.pessoa.nome}" />
                            </rich:column>
                            <rich:column
                                id="item-confirmacao-ambiente"
                                dir="RTL">
                                <f:facet name="header">
                                    <h:outputText value="Ambiente"/>
                                </f:facet>
                                <h:outputText value="#{item.ambienteVO.descricao}" />
                            </rich:column>
                            <rich:column
                                id="item-confirmacao-dia"
                                dir="RTL">
                                <f:facet name="header">
                                    <h:outputText value="Dia da Semana"/>
                                </f:facet>
                                <h:outputText value="#{item.diaSemanaEnum.descricao}" />
                            </rich:column>
                            <rich:column
                                id="item-confirmacao-tipoHorario">
                                <f:facet name="header">
                                    <h:outputText value="T.H."/>
                                </f:facet>
                                <h:outputText value="#{item.tipoHorarioVO.descricao}" />
                            </rich:column>
                            <rich:column
                                id="item-confirmacao-hora"
                                dir="RTL">
                                <f:facet name="header">
                                    <h:outputText value="Hor�rio"/>
                                </f:facet>
                                <h:outputText value="#{item.horaInicial_Apresentar}" />
                            </rich:column>
                            <rich:column
                                id="item-confirmacao-data"
                                dir="RTL">
                                <f:facet name="header">
                                    <h:outputText value="Data"/>
                                </f:facet>
                                <h:outputText value="#{item.dataMes}" converter="dataConverter"/>
                            </rich:column>
                            <rich:column
                                id="item-confirmacao-ml"
                                dir="RTL">
                                <f:facet name="header">
                                    <h:outputText value="Matriculados"/>
                                </f:facet>
                                <h:outputText value="#{item.matriculados}"/>
                            </rich:column>
                            <rich:column
                                id="item-confirmacao-mlC"
                                dir="RTL">
                                <f:facet name="header">
                                    <h:outputText value="Capacidade"/>
                                </f:facet>
                                <h:outputText value="#{item.produtoVO.capacidade}"/>
                            </rich:column>
                            <rich:column
                                width="70px"
                                id="item-confirmacao-preco">
                                <f:facet name="header">
                                    <h:outputText value="Pre�o"/>
                                </f:facet>
                                <h:outputText value="#{item.produtoVO.valorFinal}" />
                            </rich:column>

                            <f:facet name="footer">
                                <rich:columnGroup>
                                    <rich:column>
                                        <h:outputText value="Total"/>
                                    </rich:column>
                                    <rich:column colspan="7">
                                        <h:outputText value="#{fn:length(disponibilidadeControle.listaDisponibilidadeAgendado)}"/>
                                    </rich:column>
                                    <rich:column  style="text-align: center;">
                                        <h:outputText value="#{disponibilidadeControle.totalPrecoAgendamento}">
                                            <f:convertNumber pattern="$####.00"  />
                                        </h:outputText>
                                    </rich:column>
                                    <rich:column>
                                    </rich:column>
                                </rich:columnGroup>
                            </f:facet>
                        </rich:dataTable>
                    </div>
                </f:verbatim>

                <f:verbatim rendered="#{fn:length(disponibilidadeControle.listaDisponibilidadeSaldo) > 0}">
                    <h:panelGrid
                        id="panelmensagem-saldo"  columns="1" width="100%">
                        <h:outputText style="color:blue;"  value="Lista de agendamentos debitados pelo saldo"/>
                    </h:panelGrid>
                    <div id="tableConfirmDisponibilidade">
                        <rich:dataTable
                            id="disponibilidadeControle-confirmacao-disponibilidadeSaldo"
                            width="100%"
                            rendered="#{fn:length(disponibilidadeControle.listaDisponibilidadeSaldo) > 0}"
                            headerClass="consulta"
                            columnClasses="colunaAlinhamento"
                            value="#{disponibilidadeControle.listaDisponibilidadeSaldo}"
                            var="item">
                            <rich:column
                                id="item-confirmacao-servico-saldo"
                                dir="RTL">
                                <f:facet name="header">
                                    <h:outputText value="Servi�o"/>
                                </f:facet>
                                <h:outputText value="#{item.produtoVO.descricao}" />
                            </rich:column>
                            <rich:column
                                id="item-confirmacao-profissional"
                                dir="RTL">
                                <f:facet name="header">
                                    <h:outputText value="Profissional"/>
                                </f:facet>
                                <h:outputText value="#{item.colaboradorVO.pessoa.nome}" />
                            </rich:column>
                            <rich:column
                                id="item-confirmacao-ambiente-saldo"
                                dir="RTL">
                                <f:facet name="header">
                                    <h:outputText value="Ambiente"/>
                                </f:facet>
                                <h:outputText value="#{item.ambienteVO.descricao}" />
                            </rich:column>
                            <rich:column
                                id="item-confirmacao-dia-saldo"
                                dir="RTL">
                                <f:facet name="header">
                                    <h:outputText value="Dia da Semana"/>
                                </f:facet>
                                <h:outputText value="#{item.diaSemanaEnum.descricao}" />
                            </rich:column>
                            <rich:column
                                id="item-confirmacao-hora-saldo"
                                dir="RTL">
                                <f:facet name="header">
                                    <h:outputText value="Hora In�cio"/>
                                </f:facet>
                                <h:outputText value="#{item.horaInicial}" converter="timeConverter" />
                            </rich:column>
                            <rich:column
                                id="item-confirmacao-data-saldo"
                                dir="RTL">
                                <f:facet name="header">
                                    <h:outputText value="Data"/>
                                </f:facet>
                                <h:outputText value="#{item.dataMes}" converter="dataConverter"/>
                            </rich:column>
                            <rich:column
                                id="item-confirmacao-ml-saldo"
                                dir="RTL">
                                <f:facet name="header">
                                    <h:outputText value="Matriculados"/>
                                </f:facet>
                                <h:outputText value="#{item.matriculados}"/>
                            </rich:column>
                            <rich:column
                                id="item-confirmacao-mlC-saldo"
                                dir="RTL">
                                <f:facet name="header">
                                    <h:outputText value="Capacidade"/>
                                </f:facet>
                                <h:outputText value="#{item.produtoVO.capacidade}"/>
                            </rich:column>
                            <rich:column
                                width="70px"
                                id="item-confirmacao-preco-saldo">
                                <f:facet name="header">
                                    <h:outputText value="Pre�o"/>
                                </f:facet>
                                <h:outputText value="#{item.produtoVO.valorFinal}" />
                            </rich:column>

                            <f:facet name="footer">
                                <rich:columnGroup>
                                    <rich:column>
                                        <h:outputText value="Total"/>
                                    </rich:column>
                                    <rich:column colspan="7">
                                        <h:outputText value="#{fn:length(disponibilidadeControle.listaDisponibilidadeSaldo)}"/>
                                    </rich:column>
                                    <rich:column  style="text-align: center;">
                                        <h:outputText value="#{disponibilidadeControle.totalPrecoSaldo}">
                                            <f:convertNumber pattern="$####.00"  />
                                        </h:outputText>
                                    </rich:column>
                                    <rich:column>
                                    </rich:column>
                                </rich:columnGroup>
                            </f:facet>
                        </rich:dataTable>
                    </div>
                </f:verbatim>

                <f:verbatim rendered="#{fn:length(disponibilidadeControle.listaDisponibilidadeComprar) > 0}">
                    <h:panelGrid
                        id="panelmensagem-comprar" columns="1" width="100%">
                        <h:outputText style="color:red;"  value="Lista a ser comprada"/>
                    </h:panelGrid>
                    <div id="tableConfirmDisponibilidade">
                        <rich:dataTable
                            id="disponibilidadeControle-confirmacao-disponibilidadeComprar"
                            width="100%"
                            rendered="#{fn:length(disponibilidadeControle.listaDisponibilidadeComprar) > 0}"
                            headerClass="consulta"
                            columnClasses="colunaAlinhamento"
                            value="#{disponibilidadeControle.listaDisponibilidadeComprar}"
                            var="item">
                            <rich:column
                                id="item-confirmacao-servico"
                                dir="RTL">
                                <f:facet name="header">
                                    <h:outputText value="Servi�o"/>
                                </f:facet>
                                <h:outputText value="#{item.produtoVO.descricao}" />
                            </rich:column>
                            <rich:column
                                id="item-confirmacao-profissional"
                                dir="RTL">
                                <f:facet name="header">
                                    <h:outputText value="Profissional"/>
                                </f:facet>
                                <h:outputText value="#{item.colaboradorVO.pessoa.nome}" />
                            </rich:column>
                            <rich:column
                                id="item-confirmacao-ambiente"
                                dir="RTL">
                                <f:facet name="header">
                                    <h:outputText value="Ambiente"/>
                                </f:facet>
                                <h:outputText value="#{item.ambienteVO.descricao}" />
                            </rich:column>
                            <rich:column
                                id="item-confirmacao-dia"
                                dir="RTL">
                                <f:facet name="header">
                                    <h:outputText value="Dia da Semana"/>
                                </f:facet>
                                <h:outputText value="#{item.diaSemanaEnum.descricao}" />
                            </rich:column>
                            <rich:column
                                id="item-confirmacao-hora"
                                dir="RTL">
                                <f:facet name="header">
                                    <h:outputText value="Hora In�cio"/>
                                </f:facet>
                                <h:outputText value="#{item.horaInicial}" converter="timeConverter" />
                            </rich:column>
                            <rich:column
                                id="item-confirmacao-data"
                                dir="RTL">
                                <f:facet name="header">
                                    <h:outputText value="Data"/>
                                </f:facet>
                                <h:outputText value="#{item.dataMes}" converter="dataConverter"/>
                            </rich:column>
                            <rich:column
                                id="item-confirmacao-ml"
                                dir="RTL">
                                <f:facet name="header">
                                    <h:outputText value="Matriculados"/>
                                </f:facet>
                                <h:outputText value="#{item.matriculados}"/>
                            </rich:column>
                            <rich:column
                                id="item-confirmacao-mlC"
                                dir="RTL">
                                <f:facet name="header">
                                    <h:outputText value="Capacidade"/>
                                </f:facet>
                                <h:outputText value="#{item.produtoVO.capacidade}"/>
                            </rich:column>
                            <rich:column
                                width="70px"
                                id="item-confirmacao-preco">
                                <f:facet name="header">
                                    <h:outputText value="Pre�o"/>
                                </f:facet>
                                <h:outputText value="#{item.produtoVO.valorFinal}" />
                            </rich:column>

                            <f:facet name="footer">
                                <rich:columnGroup>
                                    <rich:column>
                                        <h:outputText value="Total"/>
                                    </rich:column>
                                    <rich:column colspan="7">
                                        <h:outputText value="#{fn:length(disponibilidadeControle.listaDisponibilidadeComprar)}"/>
                                    </rich:column>
                                    <rich:column  style="text-align: center;">
                                        <h:outputText value="#{disponibilidadeControle.totalPrecoComprar}">
                                            <f:convertNumber pattern="$####.00"  />
                                        </h:outputText>
                                    </rich:column>
                                    <rich:column>
                                    </rich:column>
                                </rich:columnGroup>
                            </f:facet>
                        </rich:dataTable>
                    </div>
                </f:verbatim>


                <rich:spacer height="5px"/>
                <h:panelGrid
                    id="panelmensagem-listaAAgendar"  columns="1" width="100%">
                    <h:outputText
                        style="color:blue;font-size: small;" value="Saldo do Cliente"
                        rendered="#{fn:length(disponibilidadeControle.listaAAgendar) > 0}"/>
                    <h:outputText
                        style="color:red;font-size: small;" value="Cliente n�o possui saldo"
                        rendered="#{fn:length(disponibilidadeControle.listaAAgendar) == 0 && disponibilidadeControle.clienteVO.codigo > 0}"/>
                </h:panelGrid>
                <h:panelGrid columns="2" width="100%" style="vertical-align: bottom;" columnClasses="columconfirm1,columconfirm2">
                    <f:verbatim rendered="#{(fn:length(disponibilidadeControle.listaAAgendar) > 0)}">
                        <div style="width: 315px; overflow-y: auto; height: 180px; padding: 0 0 0 0;" >
                            <rich:dataTable
                                id="disponibilidadeControle-listaAAgendar"
                                style="float:left;"
                                width="300px"
                                rendered="#{fn:length(disponibilidadeControle.listaAAgendar) > 0}"
                                rowClasses="textsmall"
                                headerClass="consulta"
                                columnClasses="colunaAlinhamento"
                                value="#{disponibilidadeControle.listaAAgendar}"
                                var="item">

                                <rich:column
                                    id="item-saldo">
                                    <f:facet name="header">
                                        <h:outputText value="Saldo"/>
                                    </f:facet>
                                    <h:outputText value="#{item.saldoProduto} #{item.saldoProduto == 1 ? ' sess�o' :' sess�es'}"/>
                                </rich:column>

                                <rich:column
                                    id="item-produto-agendar">
                                    <f:facet name="header">
                                        <h:outputText value="Produto"/>
                                    </f:facet>
                                    <h:outputText value="#{item.produtoVO.descricao}"/>
                                </rich:column>
                            </rich:dataTable>
                        </div>
                    </f:verbatim>

                    <h:panelGrid style="float:right;" columns="3">
                        <a4j:commandButton
                            value="Debitar Saldo"
                            rendered="#{disponibilidadeControle.apresentarBotaoDebitarSaldo}"
                            image="#{context}/imagens/estudio/debitar_saldo.png"
                            title="Bot�o para verifica��o de saldo do cliente"
                            id="btnVerificarSaldo"
                            onclick="if (!confirm('Deseja consultar e debitar saldos dispon�veis ?')  ) {return false;}"
                            action="#{disponibilidadeControle.acaoVerificarSaldo}"
                            reRender="formModalPanelConfirmacao,modalPanelConfirmacao,modalPanelSucesso,
                            modalPanelErro,agendaGeral,btnVerificarSaldo"/>
                        <a4j:commandButton
                            image="#{context}/imagens/estudio/comprar.png"
                            value="Confirmar"
                            title="Bot�o de confirma��o das agendas selecionadas."
                            id="btnConfirmarConfirmacao"
                            onclick="if (!confirm('Deseja efetuar a compra dos agendamentos?')  ) {#{rich:component('modalPanelConfirmacao')}.hide();return false;}"
                            action="#{disponibilidadeControle.acaoSalvar}"
                            reRender="modalPanelConfirmacao,modalPanelErro,agendaGeral"/>
                        <a4j:commandButton
                            image="#{context}/imagens/estudio/voltar.png"
                            value="Voltar"
                            action="#{disponibilidadeControle.acaoEntrar}"
                            title="Bot�o de voltar para tela de disponibilidade."
                            id="btnFecharErroConfirmacao"
                            onclick="#{rich:component('modalPanelConfirmacao')}.hide();"
                            reRender="modalPanelConfirmacao,disponibilidadeControle-disponibilidade, tooglePanel"/>
                    </h:panelGrid>
                </h:panelGrid>
            </a4j:form>
        </rich:modalPanel>

        <rich:modalPanel id="modalPanelSucesso" autosized="true" shadowOpacity="true"
                         showWhenRendered="#{disponibilidadeControle.apresentarRichModalSucesso}" width="450"
                         height="80" onshow="focusAt('okButton');">
            <f:facet name="header">
                <h:panelGroup>
                    <h:outputText value="Agendamento Salvo!"></h:outputText>
                </h:panelGroup>
            </f:facet>
            <a4j:form id="formModalPanelSucesso" prependId="false">
                <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada" >

                    <h:panelGrid columns="1" width="100%">
                        <h:outputText styleClass="mensagem" value="#{disponibilidadeControle.mensagem}"
                                      escape="false"/>
                        <rich:spacer height="10px"/>
                        <h:outputText styleClass="mensagem" value="Deseja verificar na tela do aluno a compra/agendamento?"/>
                    </h:panelGrid>
                </h:panelGrid>
                <rich:spacer height="05px"/>
                <h:panelGrid columns="2" style="position: relative; float:right; ">
                    <a4j:commandButton
                        image="#{context}/imagens/estudio/botaoSim.png"
                        id="botaoSim"
                        value="Sim"
                        style="padding-right:10px;"
                        title="Sim" status="statusHora"
                        action="#{disponibilidadeControle.acaoFecharModalSucessoCliente}" reRender="modalPanelSucesso" />
                    <a4j:commandButton
                        image="#{context}/imagens/estudio/botaoNao.png"
                        id="botaoNao"
                        value="N�o"
                        title="N�o" status="statusHora"
                        action="#{disponibilidadeControle.acaoFecharModalSucesso}"
                        reRender="modalPanelConfirmacao,modalPanelSucesso,
                        modalPanelErro,agendaGeral" />
                </h:panelGrid>
            </a4j:form>
        </rich:modalPanel>
        <%@include file="includes/include_modal_disponibilidade.jsp" %>
    </f:view>
</body>
</html>

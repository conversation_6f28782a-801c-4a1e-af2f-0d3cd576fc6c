<%--
    Document   : listaColaboradorAmbienteCategoria
    Created on : 02/10/2012, 10:22:16
    Author     : Carla
--%>

<%@page contentType="text/html"%>
<%@page pageEncoding="UTF-8"%>
<head><script type="text/javascript" language="javascript" src="/script/script.js"></script></head>
<link href="../../css/otimize.css" rel="stylesheet" type="text/css">
<link href="../../css/estudio.css" rel="stylesheet" type="text/css">
<link href="../../css_pacto.css" rel="stylesheet" type="text/css">
<script type="text/javascript" language="javascript" src="hoverform.js"></script>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>
<%@taglib prefix="jsfChart" uri="http://sourceforge.net/projects/jsf-comp" %>
<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<f:view>
    <jsp:include page="/includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText value="Lista da Relação Produto - Colaborador"/>
    </title>
    <html>
        <h:form id="form">
            <jsp:include page="includes/include_head.jsp" flush="true" />
            <table width="100%" align="center" height="100%" border="0" cellpadding="0" cellspacing="0">

                <tr>
                    <td align="left" valign="top" width="100%"  style="padding:7px 20px 0 20px;">
                        <h:panelGroup id="panelGroup">
                            <table width="100%" height="100%" border="0" align="left" cellpadding="0" cellspacing="0" class="text" style="margin-bottom:20px;">
                                <tr>
                                    <td width="19" height="50" align="left" valign="top"><img src="../../images/box_centro_top_left.gif" width="19" height="50"></td>
                                    <td align="left" valign="top" background="../../images/box_centro_top.gif" class="tituloboxcentro" style="padding:11px 0 0 0;">

                                        <h:outputText value="Lista da Relação Produto - Colaborador"/>

                                    </td>
                                    <td width="19" align="left" valign="top"><img src="../../images/box_centro_top_right.gif" width="19" height="50"></td>
                                </tr>
                                <tr>
                                    <td align="left" valign="top" background="../../images/box_centro_left.gif"></td>
                                    <td align="left" valign="top" bgcolor="#ffffff" style="padding:5px 15px 5px 15px;">
                                        <h:panelGrid id="mainPanelModal" columns="1" width="100%" columnClasses="colunaCentralizada" >
                                            <rich:extendedDataTable
                                                id="listaProdutoColaborador"
                                                width="100%"
                                                height="500"
                                                reRender="scResultadoListaAVendaModal"
                                                rowClasses="textsmall"
                                                styleClass="textsmall"
                                                columnClasses="centralizado, centralizado, centralizado"
                                                value="#{configuracaoEstudioControle.listaRelProdutoColaboradorVO}"
                                                var="item">
                                                <rich:column sortBy="#{item.produtoVO.codigo}" label="Código Produto"
                                                             width="20%">
                                                    <f:facet name="header">
                                                        <h:outputText value="Código Produto" styleClass="texto_disponibilidade" />
                                                    </f:facet>
                                                    <h:outputText
                                                        value="#{item.produtoVO.codigo}"
                                                        style="float:right; margin-right: 5px;"/>
                                                </rich:column>
                                                <rich:column sortBy="#{item.produtoVO.descricao}" label="Descrição Produto"
                                                             width="30%">
                                                    <f:facet name="header">
                                                        <h:outputText value="Descrição Produto" styleClass="texto_disponibilidade" />
                                                    </f:facet>
                                                    <h:outputText
                                                        value="#{item.produtoVO.descricao}"
                                                        style="margin-left: 5px; position:relative;"/>
                                                </rich:column>

                                                <rich:column sortBy="#{item.colaboradorVO.pessoa.nome}" label="Nome Colaborador"
                                                             width="30%">
                                                    <f:facet name="header">
                                                        <h:outputText value="Nome Colaborador" styleClass="texto_disponibilidade" />
                                                    </f:facet>
                                                    <h:outputText
                                                        value="#{item.colaboradorVO.pessoa.nome}"
                                                        style="margin-left: 5px; position:relative;"/>
                                                </rich:column>
                                                <rich:column width="15%" sortBy="#{item.porcComissao}" label="Comissão">
                                                    <f:facet name="header" >
                                                        <h:outputText value="Comissão" styleClass="texto_disponibilidade"/>
                                                    </f:facet>
                                                    <h:outputText value="#{item.porcComissao}" style="float:right; margin-right: 5px;">
                                                        <f:convertNumber maxFractionDigits="2" groupingUsed="true" maxIntegerDigits="14" type="currency" currencySymbol="% " />
                                                    </h:outputText>
                                                </rich:column>
                                                <rich:column width="5%">
                                                    <a4j:commandLink action="#{configuracaoEstudioControle.acaoRemoverRelProdutoColaborador}"
                                                                     reRender="configuracaoEstudioControle-listaServico,modalPanelErro,panelmensagem">
                                                        <h:graphicImage value="../../imagens/estudio/icon_delete.png" style="cursor:pointer" id="rmvItem" />
                                                        <f:setPropertyActionListener target="#{configuracaoEstudioControle.relProdutoColaboradorVO}" value="#{item}"/>
                                                    </a4j:commandLink>
                                                </rich:column>
                                            </rich:extendedDataTable>
                                        </h:panelGrid>


                                    </td>
                                    <td align="left" valign="top" background="../../images/box_centro_right.gif"><img src="../../images/shim.gif"></td>

                                </tr>
                                <tr>
                                    <td height="20" align="left" valign="top"><img src="../../images/box_centro_bottom_left.gif" width="19" height="20"></td>
                                    <td align="left" valign="top" background="../../images/box_centro_bottom.gif"><img src="../../images/shim.gif"></td>
                                    <td align="left" valign="top"><img src="../../images/box_centro_bottom_right.gif" width="19" height="20"></td>
                                </tr>
                            </table>
                        </h:panelGroup>
                    </td>
                </tr>
            </table>
        </h:form>
    </f:view>
</body>
</html>



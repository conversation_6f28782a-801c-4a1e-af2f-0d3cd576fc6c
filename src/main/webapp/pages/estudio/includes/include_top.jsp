<%@page contentType="text/html"%>
<%@page pageEncoding="ISO-8859-1"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>
<%@include file="../includes/include_imports.jsp" %>
<%@include file="../../../includes/include_identificadorModuloEstudio.jsp" %>

<a4j:loadStyle src="/css/toggle_menu.css"/>
<a4j:loadScript src="/script/script2.js"/>
<a4j:loadScript src="/script/toggle_menu.js"/>

<c:set var="contexto" value="${pageContext.request.contextPath}" scope="request" />

<table width="100%" height="77"  align="left" border="0" cellpadding="0" cellspacing="0">
    <tr>
        <td width="20%" align="left" valign="middle">
            <div class="logoEstudio"></div>
        </td>
        <%@include file="/includes/include_expiracao.jsp" %>
        <td align="right" valign="top" style="padding-right:13px;">
            <table border="0" cellspacing="0" cellpadding="0">
                <tr>
                    <td align="left" valign="top">
                        <div style="padding:0px 6px; background-image: url(${root}/beta/imagens/bg-topo-menu-15.png); border-top: none; border-bottom-left-radius: 15px; border-bottom-right-radius: 15px; overflow: hidden;">
                            <ul class="abatop">
                                    <li>
                                        <%@include file="/includes/include_menu_modulos.jsp" %>
                                    </li>

                                    <li><img src="${root}/images/aba_sep.gif"></li>

                                    <%@include file="/include_top_socialMailing.jsp" %>

                                    <li>
                                        <rich:dropDownMenu>
                                            <f:facet name="label">
                                                <h:panelGroup>
                                                    <i class="fa-icon-question-sign fa-icon-2x"></i>
                                                </h:panelGroup>
                                            </f:facet>
                                            <rich:menuItem submitMode="none"
                                                           icon="/faces/beta/imagens/mini-icons-wiki.png">
                                                <h:outputLink id="linkwiki"
                                                              style="color:#555;" target="_blank" value='#{SuperControle.urlWikiRaiz}/ZillyonWeb:Módulo_Gestao_Studio'>
                                                    WikiPacto
                                                </h:outputLink>
                                            </rich:menuItem>
                                            <rich:menuItem submitMode="none"
                                                    icon="/faces/beta/imagens/mini-icons-suport.png">
                                                <h:outputLink id="linksolicitacao"
                                                              style="color:#555;"  target="_blank" value="#{SuporteControle.urlSolicitacao}">
                                                    Suporte
                                                    <a4j:support event="onclick" action="#{SuporteControle.prepareUserService}" oncomplete="#{SuporteControle.loginService}"/>
                                                </h:outputLink>
                                            </rich:menuItem>

                                        </rich:dropDownMenu>
                                    </li>

                                    <li>
                                        <rich:dropDownMenu itemClass="itemMnuTpo">
                                            <f:facet name="label">
                                                <h:panelGroup>
                                                    <i class="fa-icon-cog fa-icon-2x"></i>
                                                </h:panelGroup>
                                            </f:facet>
                                            <rich:menuItem submitMode="none"
                                                    icon="/faces/beta/imagens/mini-icons-config.png">

                                                <a4j:commandLink action="#{configuracaoEstudioControle.acaoEntrar}"
                                                                 title="Configurações do Módulo Gestão Estúdio"
                                                                 value="Configurações"
                                                                 style="color: #555;"
                                                                 oncomplete="#{configuracaoEstudioControle.msgAlert}"/>

                                                <%--<a4j:commandLink action="#{configuracaoEstudioControle.acaoEntrar}"--%>
                                                                 <%--oncomplete="abrirPopup('#{contexto}/pages/estudio/configuracaoEstudio.jsp', 'ConfiguracaoSistema', 880, 595);"--%>
                                            </rich:menuItem>
                                            <rich:menuItem submitMode="none"
                                                    icon="/faces/beta/imagens/mini-icons-calend.png">
                                                <a4j:commandLink style="color: #555" id="linkGoogleCalendar" rendered="#{!fn:contains(uriPagina, 'pages')}" action="#{ConfiguracaoSistemaControle.novo}"
                                                                 oncomplete="window.open('#{uriPagina}googlecalendar.jsp', 'GoogleCalendar', 820, 620);" value="Google Calendar"/>
                                            </rich:menuItem>
                                            <rich:menuItem submitMode="none"
                                                    icon="/faces/beta/imagens/mini-icons-velo.png">
                                                <a4j:commandLink style="color: #555" id="linkVelocimetro" rendered="#{!fn:contains(uriPagina, 'pages')}" oncomplete="abrirPopup('#{urlPath}velocimetro.jsp', 'Velocimetro', 500, 300);"  value="Velocímetro"/>
                                            </rich:menuItem>
                                            <rich:menuItem submitMode="none"
                                                    icon="/faces/beta/imagens/mini-icons-key.png">
                                                <a4j:commandLink rendered="#{LoginControle.usuario.permiteAlterarPropriaSenha}" value="Alterar Senha"
                                                                 onclick="abrirPopup('#{root}/faces/alterarSenhaClienteForm.jsp', 'AlterarSenha', 410, 350);"
                                                                 title="Alterar senha" styleClass="text2"
                                                                 style="valign:middle;cursor:pointer;color: #555;"/>
                                            </rich:menuItem>
                                            <rich:menuSeparator id="menuSeparator11" />

                                            <rich:menuItem submitMode="none" value="Sair"
                                                           icon="/faces/beta/imagens/mini-icons-sair.png"
                                                           onclick="document.location.href='#{LogoutControle.redirectLogout}'" />
                                        </rich:dropDownMenu>
                                    </li>
                                </ul>
                        </div>
                    </td>
                </tr>
                <jsp:include page="/includes/include_top_datahora.jsp" flush="true"/>
            </table>
        </td>
    </tr>
</table>
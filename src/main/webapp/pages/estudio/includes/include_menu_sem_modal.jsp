<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>

<script type="text/javascript" language="javascript" src="${contexto}/script/script.js"></script>
<table width="100%"height="48" border="0" cellpadding="0" cellspacing="0">
    <tr>
        <td width="50%" align="left" valign="top" class="bgmenuleft" style="padding:9px 0 0 80px;">
            <div style="float:left;">
                <ul class="btnazul">
                    <li><p class="btnleft"></p>
                        <p class="btnmiddle"><a id="linkInicialMenuSuperior" href="indexEstudio.jsp">Inicial</a></p>
                        <p class="btnright"></p></li>
                    <li><p class="btnleft"></p>
                        <p class="btnmiddle"><a id="linkCadastroMenuSuperior" href="$#">Clientes</a></p>
                        <p class="btnright"></p></li>
                    <li><p class="btnleft"></p>
                        <p class="btnmiddle">
                            <a4j:commandLink id="linkBIMenuSuperior" action="#">
                                Relat�rios
                            </a4j:commandLink>
                        </p>
                        <p class="btnright"></p></li>
                    <li><p class="btnleft"></p>
                        <p class="btnmiddle"><a id="linkCadastroMenuSuperior" href="https://docs.google.com/a/geoinova.com.br/?tab=mo#folders/0B5c3XdY9PSgSMzFmN2M5ZTMtYzViYi00NDFkLWI2NGMtMmE5NTk1OGU5OTk5">Documenta��o T�cnica</a></p>
                        <p class="btnright"></p></li>
                </ul>
            </div>
        </td>

        <td width="50%" align="right" valign="top" style="padding:9px 13px 0 0;">
            <div style="position:relative;">

                <h:inputText id="textfield2"
                             size="220"
                             maxlength="220"
                             style="background: url(../../images/nav_logo37.png) bottom;
                             border: 1px solid #CCC;
                             border-bottom-color: #999;
                             border-right-color: #999;
                             height:26px;
                             color: black;
                             margin: 0px;
                             width:220px;
                             padding: 5px 5px 5px 5px;
                             vertical-align: middle;"
                             styleClass="form" value="#{ClienteControle.valorConsultaParametrizada}"
                             onfocus="if(this.value=='Palavra-chave')this.value=''"
                             onblur="if(this.value=='')this.value='Palavra-chave'">

                </h:inputText>

                <h:graphicImage id="botaoBuscar" value="../../images/icon_lupa.png"
                                style="opacity:0.6;width:20px;height:20px;position:absolute;cursor:pointer;right:15px;top:2px;z-index:300">
                    <a4j:support action="#{ClienteControle.acao}"
                                 actionListener="#{ClienteControle.consultarPaginadoListener}"
                                 event="onclick">
                        <f:attribute name="paginaInicial" value="paginaInicial" />
                        <f:attribute name="tipoConsulta" value="parametrizado"/>
                    </a4j:support>
                </h:graphicImage>

                <rich:hotKey selector="#textfield2" key="return"
                             handler="#{rich:element('botaoBuscar')}.onclick();return false;"/>
            </div>
        </td>
    </tr>
</table>

<a4j:status forceId="true" id="statusHora" onstart="" onstop="" />

<a4j:status forceId="true" id="statusInComponent"
            onstart="document.getElementById('form:imageLoading').style.visibility = '';"
            onstop="document.getElementById('form:imageLoading').style.visibility = 'hidden';"/>

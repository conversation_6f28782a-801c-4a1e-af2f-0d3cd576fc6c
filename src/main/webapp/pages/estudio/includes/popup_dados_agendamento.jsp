<%-- 
    Document   : popup_dados_agendamento
    Created on : 11/12/2012, 09:28:42
    Author     : carla
--%>
<%@include file="/includes/imports.jsp" %>
<%@include file="/includes/include_import_minifiles.jsp" %>
<link href="../../../css/otimize.css" rel="stylesheet" type="text/css">
<script>
     setInterval(function() { getDocumentCookie('popupsImportante') == 'close' ? this.close() : ''; }, 500);
</script>
<f:view>
    <h:form>
        <title>
            Venda e Utiliza��o de Sess�es
        </title>

        <table border="1" width="100%" >
            <tr>
                <td width="5px">
                    <a4j:mediaOutput element="img" id="fotoRelatorio"  style="width:130px;height:80px "  cacheable="false"
                                     createContent="#{clienteEstudioControle.paintFoto}"  value="#{ImagemData}" mimeType="image/jpeg" >
                        <f:param value="#{SuperControle.timeStamp}" name="time"/>
                    </a4j:mediaOutput>
                </td>
                <td>
                    <center>
                        <h:outputText style="font-weight: bold;font-size:20px;" value="Venda e Utiliza��o de Sess�es"
                                      styleClass="text"/>
                        <h2> </h2>
                    </center>
                </td>
                <td>
                    <table width="100%" >
                        <tr>
                            <td class="text" style="font-size:12px;">Data: <h:outputText value="#{clienteEstudioControle.dataAtual}" ></h:outputText> </td>
                        <tr>
                        <tr>
                            <td class="text" style="font-size:12px;">Usu�rio: <h:outputText value="#{clienteEstudioControle.nomeUsuarioLogado}" ></h:outputText></td>
                        <tr>

                    </table>
                </td>

            </tr>

        </table>
        <h:panelGrid columns="6" columnClasses="w15,w40,w13,w22" width="100%"
                     style="margin-right:20px;margin-bottom:5px;padding:10px;" >
            <h:outputText style="font-weight: bold;float:right;" value="Data da Venda:" styleClass="text"/>
            <h:outputText value="#{clienteEstudioControle.itemVendaVO.dataVenda}" styleClass="textsmall">
                <f:convertDateTime pattern="dd/MM/yyyy HH:mm"/>
            </h:outputText>
            <h:outputText style="font-weight: bold;float:right;" value="Qtd. Vendidos:" styleClass="text" />
            <h:outputText  style="font-weight: bold;" value="#{clienteEstudioControle.itemVendaVO.quantidade}" styleClass="text" />
            <h:outputText style="font-weight: bold;float:right;" value="Restante:" styleClass="text" />
            <h:outputText  style="font-weight: bold;font-size:15px;" value="#{clienteEstudioControle.qtdRestanteSessoes}" styleClass="text" />
            <h:outputText style="font-weight: bold;float:right;" value="Resp. Venda:" styleClass="text"/>
            <h:outputText  value="#{clienteEstudioControle.itemVendaVO.usuarioVO.nome}" styleClass="textsmall"/>
            <h:outputText style="font-weight: bold;float:right;" value="Qtd. A Agendar:" styleClass="text" />
            <h:outputText  style="font-weight: bold;" value="#{clienteEstudioControle.qtdRestanteAAgendar}" styleClass="text" />
            <h:outputText style="font-weight: bold;" value="" styleClass="text" />
            <h:outputText  style="font-weight: bold;" value="" styleClass="text" />
            <h:outputText style="font-weight: bold;float:right;" value="Produto:" styleClass="text" />
            <h:outputText  value="#{clienteEstudioControle.itemVendaVO.produto.descricao}" styleClass="textsmall" />
            <h:outputText style="font-weight: bold;float:right;" value="Qtd    . Utilizada:" styleClass="text" title="Quantidade de Sess�es j� Ministradas ou com Faltas"/>
            <h:outputText  style="font-weight: bold;" value="#{clienteEstudioControle.qtdUtilizadaSessoes}" styleClass="text" title="Quantidade de Sess�es j� Ministradas ou com Faltas"/>
            <h:outputText style="font-weight: bold;float:right;" value="Pacote:" styleClass="text"/>
            <h:outputText  value="#{clienteEstudioControle.itemVendaVO.pacoteVO.titulo}" styleClass="textsmall"/>
        </h:panelGrid>

        <h:panelGrid id="mainPanelModalAgenda" columns="1" width="100%" columnClasses="colunaCentralizada">

            <h:panelGroup layout="block">
                <rich:dataTable
                    id="clienteEstudioControle-listaAgenda-modal"
                    width="100%"
                    rowClasses="textsmall"
                    styleClass="textsmall"
                    columnClasses="centralizado, centralizado, centralizado"
                    value="#{clienteEstudioControle.listaAgendamentosDeVendas}"
                    var="item">
                    <rich:column
                        id="item-dataAula"
                        sortable="true"
                        width="10%"
                        selfSorted="true"
                        sortBy="#{item.dataAula}"
                        filterEvent="onkeyup">
                        <f:facet name="header">
                            <h:outputText value="Data Aula"/>
                        </f:facet>
                        <h:outputText value="#{item.dataAula}" converter="dataConverter"/>
                    </rich:column>
                    <rich:column
                        id="item-diaSemana"
                        width="8%"
                        sortable="true"
                        selfSorted="true"
                        sortBy="#{item.diaSemana_Apresentar}"
                        filterEvent="onkeyup">
                        <f:facet name="header">
                            <h:outputText value="Dia Sem"/>
                        </f:facet>
                        <h:outputText value="#{item.diaSemana_Apresentar}" />
                    </rich:column>
                    <rich:column
                        id="item-horaInicio"
                        width="13%"
                        sortable="true"
                        selfSorted="true"
                        sortBy="#{item.horaInicio}"
                        filterEvent="onkeyup">
                        <f:facet name="header">
                            <h:outputText value="Hor�rio"/>
                        </f:facet>
                        <h:outputText value="#{item.horaInicio_Apresentar}" />
                    </rich:column>
                    <rich:column
                        id="item-produto-venda"
                        width="20%"
                        sortable="true"
                        selfSorted="true"
                        sortBy="#{item.produtoVO.descricao}"
                        filterEvent="onkeyup">
                        <f:facet name="header">
                            <h:outputText value="Produto"/>
                        </f:facet>
                        <h:outputText value="#{item.produtoVO.descricao}"/>
                    </rich:column>
                    <rich:column
                        id="item-professor"
                        width="15%"
                        sortable="true"
                        selfSorted="true"
                        sortBy="#{item.colaboradorVO.pessoa.nome}"
                        filterEvent="onkeyup">
                        <f:facet name="header">
                            <h:outputText value="Colaborador"/>
                        </f:facet>
                        <h:outputText value="#{item.colaboradorVO.pessoa.nome}"/>
                    </rich:column>
                    <rich:column
                        id="item-ambiente"
                        width="12%"
                        sortable="true"
                        selfSorted="true"
                        sortBy="#{item.ambienteVO.descricao}"
                        filterEvent="onkeyup">
                        <f:facet name="header">
                            <h:outputText value="Ambiente"/>
                        </f:facet>
                        <h:outputText value="#{item.ambienteVO.descricao}"/>
                    </rich:column>
                    <rich:column
                        id="item-status"
                        width="17%"
                        sortable="true"
                        selfSorted="true"
                        sortBy="#{item.status.descricao}"
                        filterEvent="onkeyup">
                        <f:facet name="header">
                            <h:outputText value="Status"/>
                        </f:facet>
                        <h:outputText value="#{item.status.descricao}"/>
                    </rich:column>
                    <rich:column
                        id="item-tipoHorarioVO"
                        sortable="true"
                        width="10%"
                        selfSorted="true"
                        sortBy="#{item.tipoHorarioVO.descricao}"
                        filterEvent="onkeyup">
                        <f:facet name="header">
                            <h:outputText value="Tipo Hor�rio"/>
                        </f:facet>
                        <h:outputText value="#{item.tipoHorarioVO.descricao}"/>
                    </rich:column>
                    <rich:column
                        id="item-responsavel"
                        sortable="true"
                        width="13%"
                        selfSorted="true"
                        sortBy="#{item.usuarioLancamento.nome}"
                        filterEvent="onkeyup">
                        <f:facet name="header">
                            <h:outputText value="Resp. Agend."/>
                        </f:facet>
                        <h:outputText value="#{item.usuarioLancamento.nome}"/>
                    </rich:column>
                </rich:dataTable>
                <br>
                <center>
                    <a4j:commandLink
                        onclick="window.print();return false;" >
                        <h:graphicImage value="/imagens/botoesCE/imprimir.png"/>
                    </a4j:commandLink>
                </center>
            </h:panelGroup>

        </h:panelGrid>

    </h:form>
</f:view>


        <rich:modalPanel id="modalPanelErro" autosized="true" shadowOpacity="true"
                         showWhenRendered="#{AgendaAmbienteColaboradorControle.apresentarRichModalErro}" width="450"
                         height="80" onshow="focusAt('fecharButton');">
            <f:facet name="header">
                <h:panelGroup>
                    <h:outputText value="Aten��o!"></h:outputText>
                </h:panelGroup>
            </f:facet>
            <a4j:form id="formModalPanelErro" prependId="false">
                <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada" >

                    <h:panelGrid columns="1" width="100%">
                        <h:outputText styleClass="mensagemDetalhada" value="#{AgendaAmbienteColaboradorControle.mensagemDetalhada}"/>
                    </h:panelGrid>
                </h:panelGrid>
                <rich:spacer height="05px"/>
                <h:panelGrid style="position: relative; float:right; ">
                    <a4j:commandButton
                        image="/imagens/estudio/fechar.png"
                        id="fecharButton"
                        value="Fechar"
                        title="Fechar" status="statusHora"
                        focus="modalAmbienteCodg-ambiente-codigo"
                        action="#{AgendaAmbienteColaboradorControle.acaoFecharModalErro}" reRender="modalPanelErro,panelAgendaAluno" />
                </h:panelGrid>
            </a4j:form>
        </rich:modalPanel>

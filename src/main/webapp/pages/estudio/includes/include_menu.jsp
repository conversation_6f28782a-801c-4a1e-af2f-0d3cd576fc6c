<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>

<script type="text/javascript" language="javascript" src="${contexto}/script/script.js"></script>
<table width="100%"height="48" border="0" cellpadding="0" cellspacing="0">
    <tr>
        <td width="50%" align="left" valign="top" class="bgmenuleft" style="padding:9px 0 0 80px;">
            <jsp:include page="../../../include_botoes_menu.jsp" flush="true"/>
        </td>

        <td width="50%" align="right" valign="top" style="padding:9px 13px 0 0;">
            <%@include file="../../../include_pesquisarFuncionalidade.jsp" %>
        </td>
    </tr>
</table>

<rich:modalPanel id="panelStatus1" autosized="true">
    <h:panelGrid columns="3">
        <h:graphicImage url="/imagens/carregando.gif" style="border:none"/>
        <h:outputText styleClass="titulo3" value="Carregando..."/>
    </h:panelGrid>
</rich:modalPanel>
<a4j:status id="status" onstart="Richfaces.showModalPanel('panelStatus1');"
            onstop="#{rich:component('panelStatus1')}.hide();">
</a4j:status>

<a4j:status forceId="true" id="statusHora" onstart="" onstop="" />

<a4j:status forceId="true" id="statusInComponent"
            onstart="document.getElementById('form:imageLoading').style.visibility = '';"
            onstop="document.getElementById('form:imageLoading').style.visibility = 'hidden';"/>

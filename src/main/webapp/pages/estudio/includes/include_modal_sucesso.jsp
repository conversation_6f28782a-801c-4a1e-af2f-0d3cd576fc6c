<%-- 
    Document   : include_modal_sucesso
    Created on : 16/11/2012, 09:05:17
    Author     : carla
--%>
<rich:modalPanel id="modalPanelSucesso" autosized="true" shadowOpacity="true"
                 showWhenRendered="#{AgendaAmbienteColaboradorControle.apresentarRichModalSucesso}" width="450"
                 height="80" onshow="focusAt('okButton');">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="Informa��o do Agendamento"/>
        </h:panelGroup>
    </f:facet>
    <a4j:form id="formModalPanelSucesso" prependId="false">
        <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada" >

            <h:panelGrid columns="1" width="100%">
                <h:outputText styleClass="mensagem"  value="#{AgendaAmbienteColaboradorControle.mensagem}"
                              escape="false"/>
            </h:panelGrid>
        </h:panelGrid>
        <rich:spacer height="05px"/>
        <h:panelGrid style="position: relative; float:right; ">
            <a4j:commandButton
                image="/imagens/estudio/confirmar.png"
                id="okButton"
                value="Ok"
                title="Ok" status="statusHora"
                action="#{AgendaAmbienteColaboradorControle.acaoFecharModalSucesso}" reRender="modalPanelSucesso" />
        </h:panelGrid>
    </a4j:form>
</rich:modalPanel>
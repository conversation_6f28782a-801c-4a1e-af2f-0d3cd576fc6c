<%@include file="imports.jsp" %>
<%@include file="/includes/autorizacao/include_autorizacao_funcionalidade.jsp" %>
<rich:modalPanel id="panelAgendaAluno" autosized="true" shadowOpacity="true"
                 showWhenRendered="#{AgendaAmbienteColaboradorControle.apresentarPanelAgendaAluno}"
                 width="500" height="290" onshow="focusAt('panelAluno-matricula');">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText rendered="#{!AgendaAmbienteColaboradorControle.apresentarTodosCampos && AgendaAmbienteColaboradorControle.ambiente}" value="Agenda Aluno - #{AgendaAmbienteColaboradorControle.agendaSelecionada.ambienteVO.descricao}" />
            <h:outputText rendered="#{AgendaAmbienteColaboradorControle.profissional}" value="Agenda Aluno - #{AgendaAmbienteColaboradorControle.agendaSelecionada.colaboradorVO.pessoa.nome}" />
            <h:outputText rendered="#{AgendaAmbienteColaboradorControle.apresentarTodosCampos}" value="Agenda Aluno" />
        </h:panelGroup>
    </f:facet>
    <f:facet name="controls">
        <h:panelGroup>
            <a4j:form>
                <a4j:commandButton reRender="panelAgendaAluno,panelGroup" image="#{context}/imagens/close.png"
                                   action="#{AgendaAmbienteColaboradorControle.acaoFecharModalPanelAluno}" title="Fechar"/>
            </a4j:form>
        </h:panelGroup>
    </f:facet>

    <h:form id="formPanelAgendaAluno" prependId="false">

        <h:panelGrid columns="3" >

            <h:outputText value="Matr�cula*" style="position:relative; left:3px;" styleClass="texto_agenda"/>
            <h:outputText value="Aluno*" style="position:relative; left:3px;" styleClass="texto_agenda"/>
            <rich:spacer  />

            <h:panelGrid columns="3">
                <h:inputText
                    value="#{AgendaAmbienteColaboradorControle.agendaSelecionada.clienteVO.codigoMatricula}"
                    onblur="blurinput(this);"
                    id="panelAluno-matricula"
                    style="#{!AgendaAmbienteColaboradorControle.novaAgenda ? 'background-color: #D2D2D2; color: #666666; background-image: none;' : ''}"
                    disabled="#{!AgendaAmbienteColaboradorControle.novaAgenda}"
                    onfocus="focusinput(this);"
                    styleClass="form"
                    autocomplete="off"
                    onkeydown="return tabOnEnter(event, 'panelAluno-nome');" size="9">
                    <a4j:support
                        event="onchange" oncomplete="focusAt('panelAluno-servico');"
                        action="#{AgendaAmbienteColaboradorControle.acaoProcurarCliente}"
                        reRender="panelAluno-nome, panelAluno-ClienteSuggestion, imagem1, panelAluno-matricula, modalPanelErro, botaoContatoAlunoEstudio, botaoClienteAlunoEstudio" />
                </h:inputText>
            </h:panelGrid>
            <h:panelGrid columns="2">
                <h:inputText
                    value="#{AgendaAmbienteColaboradorControle.agendaSelecionada.clienteVO.pessoa.nome}"
                    disabled="#{!AgendaAmbienteColaboradorControle.novaAgenda}"
                    onblur="blurinput(this);"
                    onfocus="focusinput(this);"
                    id="panelAluno-nome"
                    styleClass="form"
                    onkeydown="return tabOnEnter(event, 'panelAluno-servico');"
                    style="width:200px; #{!AgendaAmbienteColaboradorControle.novaAgenda ? 'background-color: #D2D2D2; color: #666666; background-image: none;' : ''}"/>
                <rich:suggestionbox
                    id="panelAluno-ClienteSuggestion" for="panelAluno-nome"
                    width="200"
                    suggestionAction="#{AgendaAmbienteColaboradorControle.listarClientesSuggest}"
                    var="item" fetchValue="#{item.pessoa.nome}"
                    nothingLabel="Nenhum dado encontrado" status="statusHora" >
                    <h:column>
                        <h:outputText value="#{item.pessoa.nome}"/>
                    </h:column>
                    <a4j:support event="onselect" reRender="panelAluno-matricula, imagem1, panelAluno-servico, botaoContatoAlunoEstudio, botaoClienteAlunoEstudio"
                                 action="#{AgendaAmbienteColaboradorControle.analisarSeClienteComprouPacoteGenerico}">
                        <f:setPropertyActionListener target="#{AgendaAmbienteColaboradorControle.agendaSelecionada.clienteVO}" value="#{item}" />
                    </a4j:support>
                </rich:suggestionbox>
            </h:panelGrid>

            <h:panelGroup id="botaoClienteAlunoEstudio" >
                <a4j:commandLink actionListener="#{ClienteControle.abrirClienteDoEstudio}" 
                                 oncomplete="#{ClienteControle.msgAlert}"
                                 rendered="#{AgendaAmbienteColaboradorControle.agendaSelecionada.clienteVO.codigo > 0 && LoginControle.permissaoAcessoMenuVO.cliente}">
                    <h:graphicImage url="/imagens/botaoVisualizar.png" title="Visualizar o Cliente"
                                    height="18px"
                                    width="18px"
                                    style="border:none;">
                    </h:graphicImage>
                    <f:attribute value="#{AgendaAmbienteColaboradorControle.agendaSelecionada.clienteVO.codigo}" name="codigoCliente"/>
                </a4j:commandLink>

                <a4j:commandLink id="historicoBVCliente" rendered="#{AgendaAmbienteColaboradorControle.exibirHistoricoBV}"
                                 style="font-size: 23px;color:rgba(56, 53, 53, 0.65);"
                                 title="Visualizar Boletim Visita"
                                 onclick="setDocumentCookie('popupsImportante', '',1);abrirPopup('#{contexto}/faces/questionarioClienteCRMForm.jsp', 'HistoricoBVCliente', 1000, 650);">
                    <i class="fa-icon-time"/>
                </a4j:commandLink>
            </h:panelGroup>

        </h:panelGrid>


        <h:panelGrid columns="2" style="width:500px">
            <a4j:mediaOutput element="img" id="imagem1"  style="width:90px;height:130px "  cacheable="false" session="true"
                             rendered="#{!SuperControle.fotosNaNuvem}" 
                             createContent="#{AgendaAmbienteColaboradorControle.paintFoto}"  value="#{ImagemData}" mimeType="image/jpeg" >
            </a4j:mediaOutput>
            <h:graphicImage rendered="#{SuperControle.fotosNaNuvem}" 
                            width="90" height="130"                                        
                            style="width:90px;height:130px"
                            url="#{AgendaAmbienteColaboradorControle.paintFotoDaNuvem}"/>
            <h:panelGrid columns="2" id="panelAlunoAgenda">

                <h:outputLabel id="lb-servico" styleClass="classLabel texto_agenda" value="Servi�o*"/>
                <h:outputLabel id="lbl-status" styleClass="classLabel texto_agenda" value="Status:"/>
                <h:panelGroup>
                    <h:inputText id="itemServicos"   size="30"
                                 disabled="#{AgendaAmbienteColaboradorControle.agendaSelecionada.codigo > 0}"
                                 onblur="blurinput(this);"  styleClass="form"
                                 value="#{AgendaAmbienteColaboradorControle.agendaSelecionada.produtoVO.descricao}"
                                 title="#{AgendaAmbienteColaboradorControle.agendaSelecionada.produtoVO.observacao}"                                 
                                 onkeydown="bloquearCtrlJ()">
                    </h:inputText>

                    <rich:suggestionbox height="200" width="200"
                                        for="itemServicos"
                                        suggestionAction="#{AgendaAmbienteColaboradorControle.executarAutocompleteConsultaProduto}"
                                        minChars="1" rowClasses="20"
                                        status="statusHora" immediate="true"
                                        nothingLabel="Nenhum Servi�o encontrado !" 
                                        var="result" id="suggestionNomeServicos">

                        <a4j:support event="onselect"  focus="panelAluno-status" reRender="panelAlunoAgenda"
                                     action="#{AgendaAmbienteColaboradorControle.selecionarServicoSuggestionBox}"/>
                        <h:column>
                            <h:outputText value="#{result.descricao}" title="#{result.observacao}"/>
                        </h:column>

                    </rich:suggestionbox>
                </h:panelGroup>
                <h:selectOneMenu
                    id="panelAluno-status"
                    disabled="#{AgendaAmbienteColaboradorControle.bloquearStatus}"
                    onblur="blurinput(this);"
                    onfocus="focusinput(this);"
                    styleClass="form"
                    onkeydown="return tabOnEnter(event, 'panelAluno-horario');"
                    style="width:140px;
                    #{AgendaAmbienteColaboradorControle.bloquearStatus ? 'background-color: #D2D2D2; color: #666666;' : ''}"
                    value="#{AgendaAmbienteColaboradorControle.agendaSelecionada.status}"
                    title="Status">
                    <f:selectItems value="#{AgendaAmbienteColaboradorControle.buscarListaStatus}" />
                    <a4j:support event="onchange" reRender="panelAlunoAgenda"/>
                </h:selectOneMenu>


                <h:selectOneMenu
                    id="panelAluno-servico-generico"
                    style="width:200px;"
                    onblur="blurinput(this);"
                    onfocus="focusinput(this);"
                    onkeydown="return tabOnEnter(event, 'panelAluno-horario');"
                    rendered="#{!empty AgendaAmbienteColaboradorControle.servicoGenericoCombo && !AgendaAmbienteColaboradorControle.novaAgenda}"
                    value="#{AgendaAmbienteColaboradorControle.agendaSelecionada.produtoGenericoEscolhidoVO.codigo}"
                    title="Servi�o"
                    styleClass="form">
                    <f:selectItem itemLabel="Selecione um Servi�o!" />
                    <f:selectItems value="#{AgendaAmbienteColaboradorControle.servicoGenericoCombo}"  />
                    <a4j:support event="onchange" />
                </h:selectOneMenu >
                <h:outputText value="" rendered="#{!empty AgendaAmbienteColaboradorControle.servicoGenericoCombo && !AgendaAmbienteColaboradorControle.novaAgenda}"/>

                <h:outputText  value="Profissional*:" styleClass="texto_agenda" />
                <h:outputText  value="Profissional Indica��o:" styleClass="texto_agenda" />

                <h:selectOneMenu style="width:200px; #{!AgendaAmbienteColaboradorControle.verificarPreenchimentoParaProfissional
                                                       || AgendaAmbienteColaboradorControle.desabilitaStatus
                                                       || !AgendaAmbienteColaboradorControle.novaAgenda ? 'background-color: #D2D2D2; color: #666666;' : ''}"
                                 value="#{AgendaAmbienteColaboradorControle.agendaSelecionada.colaboradorVO}"
                                 id="listaProfissionalModal"
                                 onblur="blurinput(this);"
                                 onfocus="focusinput(this);"
                                 styleClass="form"
                                 converter="colaboradorConverter"
                                 onkeydown="return tabOnEnter(event, 'tipoHorario');"
                                 title="Profissional">
                    <f:selectItem itemLabel="Selecione um Profissional!" />
                    <f:selectItems  id="selectItemProfissional" value="#{AgendaAmbienteColaboradorControle.profissionalCombo}"/>
                    <a4j:support event="onchange" reRender="panelAluno-horario,formPanelAgendaAluno"
                                 action="#{AgendaAmbienteColaboradorControle.listarHorarios}" />
                </h:selectOneMenu>
                <h:selectOneMenu style="width:200px; #{!AgendaAmbienteColaboradorControle.verificarPreenchimentoParaProfissional
                                                       || AgendaAmbienteColaboradorControle.desabilitaStatus
                                                       || !AgendaAmbienteColaboradorControle.novaAgenda ? 'background-color: #D2D2D2; color: #666666;' : ''}"
                                 value="#{AgendaAmbienteColaboradorControle.agendaSelecionada.colaboradorIndicacaoVO}"
                                 id="listaProfissionalIndicacaoModal"
                                 onblur="blurinput(this);"
                                 onfocus="focusinput(this);"
                                 styleClass="form"
                                 converter="colaboradorConverter"
                                 onkeydown="return tabOnEnter(event, 'tipoHorario');"
                                 title="Profissional">
                    <f:selectItem itemLabel="Selecione um Profissional!" />
                    <f:selectItems  id="selectItemProfissionalIndicacao" value="#{AgendaAmbienteColaboradorControle.profissionalIndicacaoCombo}"/>
                </h:selectOneMenu>


                <h:outputText  value="Ambiente*:" styleClass="texto_agenda"  />
                <h:outputText  value="" styleClass=""  />
                <h:selectOneMenu style="width:200px; #{!AgendaAmbienteColaboradorControle.verificarPreenchimentoParaAmbiente
                                          || AgendaAmbienteColaboradorControle.desabilitaStatus
                                           ? 'background-color: #D2D2D2; color: #666666;' : ''}"
                                 value="#{AgendaAmbienteColaboradorControle.agendaSelecionada.ambienteVO}"
                                 id="listaAmbienteModal1"
                                 onblur="blurinput(this);"
                                 converter="ambienteConverter"
                                 onfocus="focusinput(this);"
                                 styleClass="form"
                                 onkeydown="return tabOnEnter(event, 'tipoHorario');"
                                 title="Ambiente">
                    <f:selectItem itemLabel="Selecione um Ambiente!"  />
                    <f:selectItems  id="selectItemAmbiente1" value="#{AgendaAmbienteColaboradorControle.ambienteCombo}"/>
                    <a4j:support event="onchange" reRender="panelAluno-horario,formPanelAgendaAluno"
                                 action="#{AgendaAmbienteColaboradorControle.listarHorarios}"/>
                </h:selectOneMenu>
                <h:outputText  value="" styleClass="" />

                <h:outputLabel id="lbl-data" styleClass="classLabel texto_agenda" value="Data da Aula*:"/>
                <h:outputLabel id="lb-tipoHorario" style="width:200px" styleClass="classLabel texto_agenda" value="Tipo Hor�rio*:"/>
                <rich:calendar
                    locale="pt/BR"
                    inputSize="10"
                    inputStyle="#{AgendaAmbienteColaboradorControle.desabilitaStatus ? 'background-color: #D2D2D2; color: #666666; background-image: none;' : ''}"
                    disabled="#{AgendaAmbienteColaboradorControle.desabilitaStatus}"
                    inputClass="form"
                    oninputblur="blurinput(this);"
                    oninputfocus="focusinput(this);"
                    oninputchange="return validar_Data(this.id);"
                    oninputkeypress="return mascara(this, '99/99/9999', event);"
                    oninputkeyup="return tabOnEnter(event, 'panelAluno-horario');"
                    datePattern="dd/MM/yyyy"
                    enableManualInput="true"
                    zindex="2"
                    showWeeksBar="false"
                    value="#{AgendaAmbienteColaboradorControle.agendaSelecionada.dataAula}"
                    id="panelAluno-dataAula"
                    popup="true" >
                    <a4j:support
                        event="onchanged"
                        action="#{AgendaAmbienteColaboradorControle.mudancaData}"
                        reRender="formPanelAgendaAluno,panelAluno-profissionalSuggestion"
                        ajaxSingle="true"/>
                </rich:calendar>
                <h:selectOneMenu
                        id="tipoHorario"
                        onblur="blurinput(this);"
                        onfocus="focusinput(this);"
                        styleClass="form"
                        onkeydown="return tabOnEnter(event, 'panelAluno-observacao');"
                        style="width:200px; #{AgendaAmbienteColaboradorControle.desabilitaStatus || !AgendaAmbienteColaboradorControle.novaAgenda ? 'background-color: #D2D2D2; color: #666666;' : ''}"
                        value="#{AgendaAmbienteColaboradorControle.agendaSelecionada.tipoHorarioVO.codigo}"
                        title="Tipo de Hor�rio" >
                    <f:selectItem itemLabel="Selecione um Tipo Hor�rio!"  />
                    <f:selectItems value="#{AgendaAmbienteColaboradorControle.buscarListaTipoHorarios}" />
                </h:selectOneMenu>



                <h:outputLabel rendered="#{AgendaAmbienteColaboradorControle.apresentarBotaoRetirarHorario!='S'}" id="lb-hora"  styleClass="classLabel texto_agenda"  value="Hora*:">
                    <rich:spacer width="25px"/>
                    <a4j:commandButton id="addHorario"
                                       rendered="#{!(!AgendaAmbienteColaboradorControle.verificarPreenchimentoParaHoras || AgendaAmbienteColaboradorControle.desabilitaStatus)
                                                   && (AgendaAmbienteColaboradorControle.apresentarBotaoRetirarHorario!='S' && AgendaAmbienteColaboradorControle.apresentarBotaoRetirarHorario!='')}"
                                       action="#{AgendaAmbienteColaboradorControle.adicionarHorario}" title="Adicionar hor�rio com intervalo diferente de uma hora"
                                       image="#{context}/imagens/estudio/adicionar_mais.png" reRender="formPanelAgendaAluno"/>
                </h:outputLabel>

                <h:panelGroup rendered="#{AgendaAmbienteColaboradorControle.apresentarBotaoRetirarHorario=='S'}">
                    <h:outputLabel id="lb-horainicial"
                                   styleClass="classLabel texto_agenda"
                                   value="Hora Inicial:"/>
                    <rich:spacer width="10px"/>
                    <h:outputLabel id="lb-horafinal"
                                   styleClass="classLabel texto_agenda"
                                   value="Hora Final:"/>
                </h:panelGroup>

                <rich:spacer rendered="#{!AgendaAmbienteColaboradorControle.remarcar}"/>
                <h:outputText rendered="#{AgendaAmbienteColaboradorControle.remarcar}"
                              value="Replicar altera��es:" styleClass="texto_agenda"  />

                <h:panelGroup id="panelAddHorario" rendered="#{AgendaAmbienteColaboradorControle.apresentarBotaoRetirarHorario=='S'}">
                    <h:inputText disabled="#{!AgendaAmbienteColaboradorControle.verificarPreenchimentoParaHoras || AgendaAmbienteColaboradorControle.desabilitaStatus}"
                                 id="horarioInicial" size="10" maxlength="5"
                                 onblur="blurinput(this);"
                                 onfocus="focusinput(this);"
                                 styleClass="form"
                                 value="#{AgendaAmbienteColaboradorControle.timeHorarioInicial}" />
                    <h:inputText disabled="#{!AgendaAmbienteColaboradorControle.verificarPreenchimentoParaHoras || AgendaAmbienteColaboradorControle.desabilitaStatus}"
                                 id="horarioFinal" size="10" maxlength="5"
                                 onblur="blurinput(this);"
                                 onfocus="focusinput(this);"
                                 styleClass="form"
                                 value="#{AgendaAmbienteColaboradorControle.timeHorarioFinal}"/>
                    <a4j:commandButton id="retHorario" disabled="#{!AgendaAmbienteColaboradorControle.verificarPreenchimentoParaHoras || AgendaAmbienteColaboradorControle.desabilitaStatus}"
                                       reRender="formPanelAgendaAluno"
                                       action="#{AgendaAmbienteColaboradorControle.retirarHorario}"
                                       image="#{context}/imagens/estudio/adicionar_menos.png" ajaxSingle="true"/>
                </h:panelGroup>

                <h:selectOneMenu
                        id="panelAluno-horario"
                        immediate="true"
                        rendered="#{AgendaAmbienteColaboradorControle.apresentarBotaoRetirarHorario=='' || AgendaAmbienteColaboradorControle.apresentarBotaoRetirarHorario=='N'}"
                        disabled="#{!AgendaAmbienteColaboradorControle.verificarPreenchimentoParaHoras || AgendaAmbienteColaboradorControle.desabilitaStatus}"
                        onblur="blurinput(this);"
                        onfocus="focusinput(this);"
                        styleClass="form"
                        onkeydown="return tabOnEnter(event, 'panelAluno-salvar');"
                        style="width:140px;
                    #{!AgendaAmbienteColaboradorControle.verificarPreenchimentoParaHoras || AgendaAmbienteColaboradorControle.desabilitaStatus ? 'background-color: #D2D2D2; color: #666666;' : ''}"
                        value="#{AgendaAmbienteColaboradorControle.timeSelecionado}"
                        title="Hor�rio" >
                    <f:selectItem itemLabel="Selecione um hor�rio!" itemValue="-1"/>
                    <f:selectItems value="#{AgendaAmbienteColaboradorControle.selectItemHorarios}" />

                </h:selectOneMenu>
                <rich:spacer rendered="#{!AgendaAmbienteColaboradorControle.remarcar}"/>
                <h:selectOneMenu
                        immediate="true"
                        onblur="blurinput(this);"
                        onfocus="focusinput(this);"
                        styleClass="form"
                        rendered="#{AgendaAmbienteColaboradorControle.remarcar}"
                        onkeydown="return tabOnEnter(event, 'panelAluno-salvar');"
                        style="width:140px;"
                        value="#{AgendaAmbienteColaboradorControle.opcaoReplicar}"
                        title="Replicar altera��es para agendamentos futuros" >
                    <f:selectItems value="#{AgendaAmbienteColaboradorControle.listaReplicar}" />

                </h:selectOneMenu>

                <h:outputLabel  id="lb-observacoes" styleClass="classLabel texto_agenda" value="Observa��es:"/>
                <rich:spacer />

                <h:inputTextarea
                        onblur="blurinput(this);"
                        id="panelAluno-observacao"
                        onfocus="focusinput(this);"
                        onkeyup="this.value = this.value.substring(0, 100);"
                        styleClass="form"
                        onkeydown="return tabOnEnter(event, 'panelAluno-status');"
                        value="#{AgendaAmbienteColaboradorControle.agendaSelecionada.observacao}"
                        style="width:200px; min-width: 200px; height:50px; min-height: 30px;"/>


<%--
                <h:panelGroup rendered="#{!AgendaAmbienteColaboradorControle.apresentarTodosCampos}" >

                </h:panelGroup>

                <h:outputText rendered="#{AgendaAmbienteColaboradorControle.apresentarTodosCampos}" value="Ambiente*:" styleClass="texto_agenda"  />
                <h:selectOneMenu rendered="#{AgendaAmbienteColaboradorControle.apresentarTodosCampos}"
                                 disabled="#{!AgendaAmbienteColaboradorControle.verificarPreenchimentoParaProfissional
                                             || AgendaAmbienteColaboradorControle.desabilitaStatus
                                             || !AgendaAmbienteColaboradorControle.novaAgenda}"
                                 style="width:200px; #{!AgendaAmbienteColaboradorControle.verificarPreenchimentoParaProfissional
                                                       || AgendaAmbienteColaboradorControle.desabilitaStatus
                                                       || !AgendaAmbienteColaboradorControle.novaAgenda ? 'background-color: #D2D2D2; color: #666666;' : ''}"
                                 value="#{AgendaAmbienteColaboradorControle.agendaSelecionada.ambienteVO}"
                                 id="listaAmbienteModal"
                                 onblur="blurinput(this);"
                                 onfocus="focusinput(this);"
                                 styleClass="form"
                                 converter="colaboradorConverter"
                                 onkeydown="return tabOnEnter(event, 'tipoHorario');"
                                 title="Profissional">
                    <f:selectItem itemLabel="Selecione um Ambiente!" />
                    <f:selectItems  id="selectItemAmbiente" value="#{AgendaAmbienteColaboradorControle.ambienteCombo}"/>
                    <a4j:support event="onchange" reRender="panelAluno-horario,panelAlunoAddHorario"
                                 action="#{AgendaAmbienteColaboradorControle.listarHorarios}" />
                </h:selectOneMenu>
--%>

                <h:panelGrid columns="1">

                    <h:panelGrid columns="2" style="float:right;">
                        <h:panelGroup id="botaoContatoAlunoEstudio">
                            <a4j:commandButton actionListener="#{HistoricoContatoControle.abrirContatoDoEstudio}"
                                               oncomplete="#{HistoricoContatoControle.msgAlert}"
                                               value="Realizar Contato"
                                               image="#{context}/imagens/estudio/realizar_contato.png"
                                               rendered="#{AgendaAmbienteColaboradorControle.agendaSelecionada.clienteVO.codigo > 0}">
                                <f:attribute value="#{AgendaAmbienteColaboradorControle.agendaSelecionada.clienteVO.codigo}" name="codigoCliente"/>
                            </a4j:commandButton> 
                        </h:panelGroup>
                        <a4j:commandButton
                            image="#{context}/imagens/estudio/gravar.png"
                            id="panelAluno-salvar"
                            disabled="#{AgendaAmbienteColaboradorControle.bloquearStatus}"
                            value="Salvar" action="#{AgendaAmbienteColaboradorControle.confirmarLancamentoProdutoPacoteExpirado}"
                            oncomplete="#{AgendaAmbienteColaboradorControle.msgAlert}"

                            reRender="agendaGeral,form:clienteEstudioControle-listaHistoricoAgenda-modal,panelAutorizacaoFuncionalidade,
                            relatorioFechamento, modalPanelErro, modalPanelSucesso #{AgendaAmbienteColaboradorControle.apresentarPanelAgendaAluno ? '' : ', panelAgendaAluno'}" />
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
        </h:panelGrid>
    </h:form>

</rich:modalPanel>
<%-- 
    Document   : include_modal_disponibilidade
    Created on : 29/01/2013, 10:58:31
    Author     : carla
--%>
<%@include file="include_imports.jsp" %>
<rich:modalPanel id="modalPanelMaisDados"
                 showWhenRendered="#{disponibilidadeControle.mostrarPanelMaisDados}"
                 autosized="true" shadowOpacity="true" width="300"
                 height="80" onshow="focusAt('okButton');">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="Disponibilidade"></h:outputText>
        </h:panelGroup>
    </f:facet>
    <f:facet name="controls">
        <h:panelGroup>
            <a4j:form>
                <a4j:commandButton reRender="modalPanelMaisDados" action="#{disponibilidadeControle.fecharModalPanelMaisDados}"
                                   image="#{context}/imagens/close.png" oncomplete="Richfaces.hideModalPanel('modalPanelMaisDados')" title="Fechar"/>
            </a4j:form>
        </h:panelGroup>
    </f:facet>
    <a4j:form id="formModalPanelMaisDados" prependId="false">
        <h:panelGrid columns="2" >
            <h:outputLabel id="tipoHorarioDisp"
                           styleClass="classLabel texto_agenda"
                           value="Tipo de Hor�rio*:"/>
            <h:selectOneMenu
                id="tipoHorario"
                onblur="blurinput(this);"
                onfocus="focusinput(this);"
                styleClass="form"
                onkeydown="return tabOnEnter(event, 'panelAluno-observacao');"
                value="#{disponibilidadeControle.disponibilidadeVO.tipoHorarioVO.codigo}"
                title="Tipo de Hor�rio" >
                <f:selectItems value="#{disponibilidadeControle.buscarListaTipoHorarios}"/>
                <a4j:support action="#{disponibilidadeControle.obterTipoHorario}" event="onchange"/>
            </h:selectOneMenu>
        </h:panelGrid>
        <h:panelGrid columns="2" rendered="#{disponibilidadeControle.apresentarBotaoRetirarHorario=='' || disponibilidadeControle.apresentarBotaoRetirarHorario=='N' }">
            <h:outputLabel id="lb-hora" styleClass="classLabel texto_agenda"
                           value="Hora*:">
                <rich:spacer width="25px"/>
                <h:selectOneMenu
                    id="panelAluno-horarioDisp"
                    immediate="true"
                    onblur="blurinput(this);"
                    onfocus="focusinput(this);"
                    styleClass="form"
                    onkeydown="return tabOnEnter(event, 'panelAluno-salvar');"
                    style="width:140px;"
                    value="#{disponibilidadeControle.timeSelecionado}"
                    title="Hor�rio" >
                    <f:selectItem itemLabel="Selecione um hor�rio!" itemValue="-1"/>
                    <f:selectItems value="#{disponibilidadeControle.selectItemHorarios}" />

                </h:selectOneMenu>
                <rich:spacer width="25px"/>
                <a4j:commandButton id="addHorario" action="#{disponibilidadeControle.adicionarHorario}"
                                   title="Adicionar hor�rio com intervalo diferente de uma hora"
                                   image="#{context}/imagens/estudio/adicionar_mais.png"
                                   reRender="formModalPanelMaisDados"/>
            </h:outputLabel>
        </h:panelGrid>
        <h:panelGrid columns="3" rendered="#{disponibilidadeControle.apresentarBotaoRetirarHorario=='S'}">
            <h:outputLabel id="lb-horaInicial"
                           styleClass="classLabel texto_agenda"
                           value="Hora Inicial *:"/>
            <h:inputText id="horarioInicial" size="10" maxlength="5"
                         onblur="blurinput(this);"
                         onfocus="focusinput(this);"
                         styleClass="form"
                         value="#{disponibilidadeControle.timeHorarioInicial}" />
            <a4j:commandButton id="retHorario" rendered="#{disponibilidadeControle.apresentarBotaoRetirarHorario=='S'}"
                               reRender="formModalPanelMaisDados"
                               action="#{disponibilidadeControle.retirarHorario}"
                               image="#{context}/imagens/estudio/adicionar_menos.png"/>
            <h:outputLabel id="lb-horaFinal"
                           styleClass="classLabel texto_agenda"
                           value="Hora Final *:"/>
            <h:inputText id="horarioFinal" size="10" maxlength="5"
                         onblur="blurinput(this);"
                         onfocus="focusinput(this);"
                         styleClass="form"
                         value="#{disponibilidadeControle.timeHorarioFinal}"/>


        </h:panelGrid>
        <h:panelGrid columns="1" width="100%">
            <h:outputText styleClass="mensagemDetalhada" value="#{disponibilidadeControle.mensagemDetalhada}"/>
        </h:panelGrid>
        <h:panelGrid columns="1" columnClasses="colunaCentralizada" width="100%">
            <a4j:commandButton
                image="/imagens/estudio/adicionar.png"
                id="botaoGravar"
                value="Sim"
                style="padding-right:10px;"
                status="statusHora"
                action="#{disponibilidadeControle.adicionarMaisDadosDisponibilidade}" reRender="disponibilidadeControle-disponibilidade, modalPanelMaisDados" />
        </h:panelGrid>
    </a4j:form>
</rich:modalPanel>

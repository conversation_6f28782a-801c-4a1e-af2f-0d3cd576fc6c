<%-- 
    Document   : relatorioFechamentoDiario
    Created on : Aug 15, 2012, 5:37:59 PM
    Author     : <PERSON>eoI<PERSON> Solu��es
--%>

<%@include file="includes/include_imports.jsp" %>

<h:panelGroup id="panelTreeViewDF">

    <table width="100%">
        <tr>
            <td width="100%" style="text-align: center;">
                &nbsp;
                <a id="refExpandir" href="#posicaoDados"
                   class="expandir"> Expandir Tudo </a>
                &nbsp;
                <a href="#posicaoDados" class="expandirUm">
                    Expandir </a>
                &nbsp;
                <a href="#posicaoDados" class="retrairUm">
                    Retrair </a>
                &nbsp;
                <a href="#posicaoDados" class="retrair">
                    Retrair Tudo </a>
            </td>
        </tr>
    </table>

    <table width="100%" cellpadding="2" cellspacing="2"
           border="0" class="fechamento" id="tree-fechamento">
           
<%-------------------------------------------------------------- IN�CIO HOR�RIOS POSS�VEIS -----------------------------------------------------------%>
        <tr id="node-horarios" >
            <td style="padding-left:30px;"><b><font color="#1E8FDE">${RelatorioFechamentoDiarioControle.totalHorariosPossiveis}</font></b> Hor�rios Poss�veis</td>
        </tr>
        
        <tr class="child-of-node-horarios">
            <td>
                <table width="100%" border="0">
                    <tr>
                        <td style="text-align:right"><b><font color="#1E8FDE">${RelatorioFechamentoDiarioControle.taxaOcupacao}%</font></b></td>
                        <td width="100%"><font size="2x" face="Arial,Helvetica,sans-serif" color="#333333">
                        		Taxa de Ocupa��o - (${RelatorioFechamentoDiarioControle.taxaOcupacaoRestante}% restante)
                        </font></td>
                    </tr>
                    
                    <tr>
                        <td style="text-align:right"><b><font color="#1E8FDE">${RelatorioFechamentoDiarioControle.taxaRealizacao}%</font></b></td>
                        <td width="100%"><font size="2x" face="Arial,Helvetica,sans-serif" color="#333333">
                        		Taxa de Realiza��o - (${RelatorioFechamentoDiarioControle.taxaRealizacaoRestante}% n�o realizado)
                        </font></td>
                    </tr>

			        <tr id="node-horarios-lista">
			            <td></td>
			        </tr>
			        
			        <tr class="child-of-node-horarios-lista">
			            <td colspan="2">
			                <table width="100%" cellpadding="2" cellspacing="0">
			                    <c:forEach var="horarios" varStatus="indiceHorarios"
			                               items="${RelatorioFechamentoDiarioControle.listaHorarios}">
			
			                        <%-- Definir as cores da tabela zebrada --%>
			                        <c:choose>
			                            <c:when test="${indiceHorarios.count % 2 != 0}">
			                                <c:set var="corLinha" value="#FFFFFF" scope="request" />
			                                <c:set var="corLinhaRessaltada" value="#CCC1F2"
			                                       scope="request" />
			                            </c:when>
			                            <c:otherwise>
			                                <c:set var="corLinha" value="#DFE8EF" scope="request" />
			                                <c:set var="corLinhaRessaltada" value="#CCC1F2"
			                                       scope="request" />
			                            </c:otherwise>
			                        </c:choose>
			
			                        <c:if test="${indiceHorarios.count == 1}">
			                            <tr style="background-color:#DFE8EF;">
			                                <td></td>
			                                <td>Hor�rios</td>
			                                <td>Faltas</td>
			                                <td>Faltas Justificadas</td>
			                                <td>Confirmados</td>
			                                <td>Realizados</td>
			                                <td>Pendentes</td>
			                                <td>Total</td>
			                            </tr>
			                        </c:if>
			
			                        <tr bgcolor="${corLinha}" 
			                            onmouseover="mudar_cor(this,'${corLinhaRessaltada}');"
			                            onmouseout="mudar_cor(this,'${corLinha}');">
			                            <td><font size="2x" face="Arial,Helvetica,sans-serif" color="#333333">${horarios.descAmbiente}</font></td>
			                            <td><font size="2x" face="Arial,Helvetica,sans-serif" color="#333333">${horarios.horarios}</font></td>
			                            <td><font size="2x" face="Arial,Helvetica,sans-serif" color="#333333">${horarios.falta}</font></td>
			                            <td><font size="2x" face="Arial,Helvetica,sans-serif" color="#333333">${horarios.justificada}</font></td>
			                            <td><font size="2x" face="Arial,Helvetica,sans-serif" color="#333333">${horarios.confirmado}</font></td>
			                            <td><font size="2x" face="Arial,Helvetica,sans-serif" color="#333333">${horarios.realizado}</font></td>
			                            <td><font size="2x" face="Arial,Helvetica,sans-serif" color="#333333">${horarios.pendente}</font></td>
			                            <td><font size="2x" face="Arial,Helvetica,sans-serif" color="#333333">${horarios.agendado}</font></td>
			                        </tr>
			
			                        <c:if test="${indiceHorarios.count == fn:length(RelatorioFechamentoDiarioControle.listaHorarios)}">
			                            <tr style="background-color:#DFE8EF;">
			                                <td></td>
			                                <td>${horarios.totalHorarios}</td>
			                                <td>${horarios.totalFalta}</td>
			                                <td>${horarios.totalJustificada}</td>
			                                <td>${horarios.totalConfirmado}</td>
			                                <td>${horarios.totalRealizado}</td>
			                                <td>${horarios.totalPendente}</td>
			                                <td>${horarios.totalAgendado}</td>
			                            </tr>
			                        </c:if>
			                    </c:forEach>
			                </table>
			            </td>
			        </tr>
                </table>
            </td>
        </tr>
<%-------------------------------------------------------------- FIM HOR�RIOS POSS�VEIS -----------------------------------------------------------%>

<%-------------------------------------------------------------- IN�CIO SESS�ES AGENDADAS -----------------------------------------------------------%>
        <tr id="node-sessoes" >
            <td style="padding-left:30px;"><b><font color="#1E8FDE">${RelatorioFechamentoDiarioControle.totalSessoesAgendadas}</font></b> Sess�es Agendadas</td>
        </tr>
        
        <tr class="child-of-node-sessoes">
            <td>
                <table width="100%" border="0">
			        <c:forEach var="sessoes" varStatus="indiceSessoes"
			                   items="${RelatorioFechamentoDiarioControle.listaSessoesTotalizador}">
			            <tr>
			                <td style="text-align:right"><b><font color="#1E8FDE">${sessoes.totalStatus}</font></b></td>
			                <td width="100%"><font size="2x" face="Arial,Helvetica,sans-serif" color="#333333">${sessoes.descStatus}</font></td>
			            </tr>
			            <c:if test="${sessoes.descStatus == 'PENDENTE'}">
			                <c:forEach var="pend" varStatus="indicePend"
			                           items="${RelatorioFechamentoDiarioControle.listaSessoesPendente}">
			
			                    <%-- Definir as cores da tabela zebrada --%>
			                    <c:choose>
			                        <c:when test="${indicePend.count % 2 != 0}">
			                            <c:set var="corLinha" value="#FFFFFF" scope="request" />
			                            <c:set var="corLinhaRessaltada" value="#CCC1F2"
			                                   scope="request" />
			                        </c:when>
			                        <c:otherwise>
			                            <c:set var="corLinha" value="#DFE8EF" scope="request" />
			                            <c:set var="corLinhaRessaltada" value="#CCC1F2"
			                                   scope="request" />
			                        </c:otherwise>
			                    </c:choose>
			
			                    <c:if test="${indicePend.count == 1}">
			                        <tr style="background-color:#DFE8EF;" id="node-column-${sessoes.descStatus}" class="child-of-node-${sessoes.descStatus}">
			                            <td>Data Aula</td>
			                            <td>Hor�rio</td>
			                            <td>Cliente</td>
			                            <td>Servi�o</td>
			                            <td>Profissional</td>
			                        </tr>
			                    </c:if>
			                    <tr bgcolor="${corLinha}" id="node-${pend.idAgenda}" class="child-of-node-${sessoes.descStatus}"
			                        onmouseover="mudar_cor(this,'${corLinhaRessaltada}');"
			                        onmouseout="mudar_cor(this,'${corLinha}');">
			                        <td colspan="2"><font size="2x" face="Arial,Helvetica,sans-serif" color="#333333">${pend.dataAula}</font></td>
			                        <td><font size="2x" face="Arial,Helvetica,sans-serif" color="#333333">${pend.horaInicio}</font>
			                            <c:if test="${pend.horaTermino !=null}">
			                                <font size="2x" face="Arial,Helvetica,sans-serif" color="#333333">- ${pend.horaTermino}</font>
			                            </c:if>
			                        </td>
			                        <td>
			                            <a href="#"
			                               onclick="preencherHiddenChamarBotao('visualizarCliente', 'idCliente','${pend.cliente}')">
			                                <font size="2x" face="Arial,Helvetica,sans-serif" color="#333333">${pend.descCliente}</font>
			                            </a>
			                        </td>
			                        <td>
			                            <a href="#"
			                               onclick="preencherHiddenChamarBotao('chamaModalAgenda', 'idEntidade','${pend.idAgenda}')">
			                                <font size="2x" face="Arial,Helvetica,sans-serif" color="#333333">${pend.descProduto}</font>
			                            </a>
			                        </td>
			                        <td colspan="13"><font size="2x" face="Arial,Helvetica,sans-serif" color="#333333">${pend.descColaborador}</font></td>
			                    </tr>
			                </c:forEach>
			            </c:if>
			            <c:if test="${sessoes.descStatus eq 'CONFIRMADO'}">
			                <c:forEach var="conf" varStatus="indiceConf"
			                           items="${RelatorioFechamentoDiarioControle.listaSessoesConfirmado}">
			
			                    <%-- Definir as cores da tabela zebrada --%>
			                    <c:choose>
			                        <c:when test="${indiceConf.count % 2 != 0}">
			                            <c:set var="corLinha" value="#FFFFFF" scope="request" />
			                            <c:set var="corLinhaRessaltada" value="#CCC1F2"
			                                   scope="request" />
			                        </c:when>
			                        <c:otherwise>
			                            <c:set var="corLinha" value="#DFE8EF" scope="request" />
			                            <c:set var="corLinhaRessaltada" value="#CCC1F2"
			                                   scope="request" />
			                        </c:otherwise>
			                    </c:choose>
			
			
			                    <c:if test="${indiceConf.count == 1}">
			                        <tr style="background-color:#DFE8EF;" id="node-column-${sessoes.descStatus}" class="child-of-node-${sessoes.descStatus}">
			                            <td>Data Aula</td>
			                            <td>Hor�rio</td>
			                            <td>Cliente</td>
			                            <td>Servi�o</td>
			                            <td>Profissional</td>
			                        </tr>
			                    </c:if>
			                    <tr bgcolor="${corLinha}" id="node-${conf.idAgenda}" class="child-of-node-${sessoes.descStatus}" 
			                        onmouseover="mudar_cor(this,'${corLinhaRessaltada}');"
			                        onmouseout="mudar_cor(this,'${corLinha}');">
			                        <td colspan="2"><font size="2x" face="Arial,Helvetica,sans-serif" color="#333333">${conf.dataAula}</font></td>
			                        <td><font size="2x" face="Arial,Helvetica,sans-serif" color="#333333">${conf.horaInicio}</font>
			                            <c:if test="${conf.horaTermino !=null}">
			                                <font size="2x" face="Arial,Helvetica,sans-serif" color="#333333">- ${conf.horaTermino}</font>
			                            </c:if>
			                        </td>
			                        <td>
			                            <a href="#"
			                               onclick="preencherHiddenChamarBotao('visualizarCliente', 'idCliente','${conf.cliente}')">
			                                <font size="2x" face="Arial,Helvetica,sans-serif" color="#333333">${conf.descCliente}</font>
			                            </a>
			                        </td>
			                        <td>
			                            <a href="#"
			                               onclick="preencherHiddenChamarBotao('chamaModalAgenda', 'idEntidade','${conf.idAgenda}')">
			                                <font size="2x" face="Arial,Helvetica,sans-serif" color="#333333">${conf.descProduto}</font>
			                            </a>
			                        </td>
			                        <td><font size="2x" face="Arial,Helvetica,sans-serif" color="#333333">${conf.descColaborador}</font></td>
			                    </tr>
			                </c:forEach>
			            </c:if>
			        </c:forEach>
	        		<tr>
			            <td style="text-align:right"><b><font color="#1E8FDE">${RelatorioFechamentoDiarioControle.totalSessoesAgendadasCliente}</font></b></td>
			            <td width="100%"><font size="2x" face="Arial,Helvetica,sans-serif" color="#333333">Clientes diferentes est�o envolvidos neste(s) ${RelatorioFechamentoDiarioControle.totalSessoesAgendadas} agendamento(s)</font></td>
			        </tr>
        
			        <tr id="node-sessoes-lista">
			            <td ></td>
			        </tr>
			        
			         <tr class="child-of-node-sessoes-lista">
			            <td colspan="2">
			                <table width="100%" cellpadding="2" cellspacing="0">
						        <c:forEach var="sessoesLista" varStatus="indiceSessoesLista"
						                   items="${RelatorioFechamentoDiarioControle.listaSessoesAgendadas}">
						
						            <%-- Definir as cores da tabela zebrada --%>
						            <c:choose>
						                <c:when test="${indiceSessoesLista.count % 2 != 0}">
						                    <c:set var="corLinha" value="#FFFFFF" scope="request" />
						                    <c:set var="corLinhaRessaltada" value="#CCC1F2"
						                           scope="request" />
						                </c:when>
						                <c:otherwise>
						                    <c:set var="corLinha" value="#DFE8EF" scope="request" />
						                    <c:set var="corLinhaRessaltada" value="#CCC1F2"
						                           scope="request" />
						                </c:otherwise>
						            </c:choose>
					
						            <c:if test="${indiceSessoesLista.count == 1}">
						                <tr style="background-color:#DFE8EF;">
						                    <td>Data Aula</td>
						                    <td>Hor�rio</td>
						                    <td>Cliente</td>
						                    <td>Servi�o</td>
						                    <td>Profissional</td>
						                    <td>Ambiente</td>
						                    <td>Status</td>
						                    <td>Tipo Hor�rio</td>
						                </tr>
						            </c:if>
						            <tr bgcolor="${corLinha}"
						                onmouseover="mudar_cor(this,'${corLinhaRessaltada}');"
						                onmouseout="mudar_cor(this,'${corLinha}');">
						                <td><font size="2x" face="Arial,Helvetica,sans-serif" color="#333333">${sessoesLista.dataAula}</font></td>
						                
						                <td>
						                    <font size="2x" face="Arial,Helvetica,sans-serif" color="#333333">${sessoesLista.horaInicio}</font>
						                    <c:if test="${sessoesLista.horaTermino !=null}">
						                        <font size="2x" face="Arial,Helvetica,sans-serif" color="#333333">- ${sessoesLista.horaTermino}</font>
						                    </c:if>
						                </td>
						                
						                <td>
						                    <a href="#" onclick="preencherHiddenChamarBotao('visualizarCliente', 'idCliente','${sessoesLista.cliente}')">
						                        <font size="2x" face="Arial,Helvetica,sans-serif" color="#333333">${sessoesLista.descCliente}</font>
						                    </a>
						                </td>
						                
						                <td>
						                    <a href="#" onclick="preencherHiddenChamarBotao('chamaModalAgenda', 'idEntidade','${sessoesLista.idAgenda}')">
						                        <font size="2x" face="Arial,Helvetica,sans-serif" color="#333333">${sessoesLista.descProduto}</font>
						                    </a>
						                </td>
						                
						                <td><font size="2x" face="Arial,Helvetica,sans-serif" color="#333333">${sessoesLista.descColaborador}</font></td>
						                <td><font size="2x" face="Arial,Helvetica,sans-serif" color="#333333">${sessoesLista.descAmbiente}</font></td>
						                <td><font size="2x" face="Arial,Helvetica,sans-serif" color="#333333">${sessoesLista.descStatus}</font></td>
						                <td><font size="2x" face="Arial,Helvetica,sans-serif" color="#333333">${sessoesLista.descTipoHorario}</font></td>
						            </tr>
						        </c:forEach>
					        </table>
				        </td>
			        </tr>
		        </table>
	        </td>
        </tr>
<%-------------------------------------------------------------- FIM SESS�ES AGENDADAS -----------------------------------------------------------%>

<%-------------------------------------------------------------- IN�CIO VENDAS DE SESS�ES -----------------------------------------------------------%>
        <tr id="node-vendas" >
            <td style="padding-left:30px;"><b><font color="#1E8FDE">${RelatorioFechamentoDiarioControle.totalVenda}</font></b> Vendas de Sess�es</td>
        </tr>

        <tr class="child-of-node-vendas">
        	<td width="100%">
                <table width="100%" border="0">
			        <tr id="node-faturado">
			            <td style="text-align:right;white-space: nowrap;"><b><font color="#1E8FDE">${RelatorioFechamentoDiarioControle.totalFaturado}</font></b></td>
			            <td width="100%"><font size="2x" face="Arial,Helvetica,sans-serif" color="#333333">Faturado</font></td>
			        </tr>
			        <tr class="child-of-node-faturado">
			            <td colspan="2">
			                <table width="100%" cellpadding="3" cellspacing="0">
			                    <c:forEach var="faturado" varStatus="indiceFaturado"
			                               items="${RelatorioFechamentoDiarioControle.listaVendas}">
			
			                        <%-- Definir as cores da tabela zebrada --%>
			                        <c:choose>
			                            <c:when test="${indiceFaturado.count % 2 != 0}">
			                                <c:set var="corLinha" value="#FFFFFF" scope="request" />
			                                <c:set var="corLinhaRessaltada" value="#CCC1F2"
			                                       scope="request" />
			                            </c:when>
			                            <c:otherwise>
			                                <c:set var="corLinha" value="#DFE8EF" scope="request" />
			                                <c:set var="corLinhaRessaltada" value="#CCC1F2"
			                                       scope="request" />
			                            </c:otherwise>
			                        </c:choose>
			
			                        <c:if test="${indiceFaturado.count == 1}">
			                            <tr style="background-color:#DFE8EF;">
			                                <td align="left">Data de Registro</td>
			                                <td align="left">Cliente</td>
			                                <td align="left">Produto</td>
			                                <td align="left">Pacote</td>
			                                <td align="left">Tipo Comprador</td>
			                                <td align="right">Qtd</td>
			                                <td align="right">Total</td>
			                            </tr>
			                        </c:if>
			                        <tr bgcolor="${corLinha}" id="node-faturado-${faturado.idAgenda}" 
			                            onmouseover="mudar_cor(this,'${corLinhaRessaltada}');"
			                            onmouseout="mudar_cor(this,'${corLinha}');">
			
			                            <td><font size="2x" face="Arial,Helvetica,sans-serif" color="#333333">${faturado.dataRegistro}</font></td>
			                            <td><font size="2x" face="Arial,Helvetica,sans-serif" color="#333333">${faturado.nomeComprador}</font></td>
			                            <td><font size="2x" face="Arial,Helvetica,sans-serif" color="#333333">${faturado.produtoDescricao}</font></td>
			                            <td><font size="2x" face="Arial,Helvetica,sans-serif" color="#333333">${faturado.descPacote}</font></td>
			                            <td><font size="2x" face="Arial,Helvetica,sans-serif" color="#333333">${faturado.tipoComprador_Apresentar}</font></td>
			                            <td align="right"><font size="2x" face="Arial,Helvetica,sans-serif" color="#333333">${faturado.quantidadeItemVenda}</font></td>
			                            <td align="right"><font size="2x" face="Arial,Helvetica,sans-serif" color="#333333">${faturado.valorTotal}</font></td>
			                        </tr>
			                    </c:forEach>
			                </table>
			            </td>
			        </tr>
			        
			        <tr id="node-recebido">
			            <td style="text-align:right;white-space: nowrap;"><b><font color="#1E8FDE">${RelatorioFechamentoDiarioControle.totalRecebido}</font></b></td>
			            <td><font size="2x" face="Arial,Helvetica,sans-serif" color="#333333">Recebido</font></td>
			        </tr>
			
					<tr class="child-of-node-recebido">
			            <td colspan="2">
			                <table width="100%" cellpadding="3" cellspacing="0">
			                    <c:forEach var="recebido" varStatus="indiceRecebido" items="${RelatorioFechamentoDiarioControle.listaRecebidos}">
			                        <%-- Definir as cores da tabela zebrada --%>
			                        <c:choose>
			                            <c:when test="${indiceRecebido.count % 2 != 0}">
			                                <c:set var="corLinha" value="#FFFFFF" scope="request" />
			                                <c:set var="corLinhaRessaltada" value="#CCC1F2" scope="request" />
			                            </c:when>
			
			                            <c:otherwise>
			                                <c:set var="corLinha" value="#DFE8EF" scope="request" />
			                                <c:set var="corLinhaRessaltada" value="#CCC1F2" scope="request" />
			                            </c:otherwise>
			                        </c:choose>
			
			                        <c:if test="${indiceRecebido.count == 1}">
			                            <tr style="background-color:#DFE8EF;">
			                                <td align="left">Data de Registro</td>
			                                <td align="left">Nome Comprador</td>
			                                <td align="left">Produto</td>
			                                <td align="left">Pacote</td>
			                                <td align="left">Tipo Comprador</td>
			                                <td align="right">Qtd</td>
			                                <td align="right">Total</td>
			                            </tr>
			                        </c:if>
			
			                        <tr bgcolor="${corLinha}"
			                            onmouseover="mudar_cor(this,'${corLinhaRessaltada}');"
			                            onmouseout="mudar_cor(this,'${corLinha}');">
			                            <td align="left"><font size="2x" face="Arial,Helvetica,sans-serif" color="#333333">${recebido.dataRegistro}</font></td>
			                            <td align="left"><font size="2x" face="Arial,Helvetica,sans-serif" color="#333333">${recebido.nomeComprador}</font></td>
			                            <td align="left"><font size="2x" face="Arial,Helvetica,sans-serif" color="#333333">${recebido.produtoDescricao}</font></td>
			                            <td align="left"><font size="2x" face="Arial,Helvetica,sans-serif" color="#333333">${recebido.descPacote}</font></td>
			                            <td align="left"><font size="2x" face="Arial,Helvetica,sans-serif" color="#333333">${recebido.tipoComprador_Apresentar}</font></td>
			                            <td align="right"><font size="2x" face="Arial,Helvetica,sans-serif" color="#333333">${recebido.quantidadeItemVenda}</font></td>
			                            <td align="right"><font size="2x" face="Arial,Helvetica,sans-serif" color="#333333">${recebido.valorTotal}</font></td>
			                        </tr>
			                    </c:forEach>
			                </table>
			            </td>
			        </tr>
			
			        <tr id="node-recebidoDCO">
			            <td style="text-align:right;white-space: nowrap;"><b><font color="#1E8FDE">${RelatorioFechamentoDiarioControle.totalVendaRecebidoContaCorrente}</font></b></td>
			            <td><font size="2x" face="Arial,Helvetica,sans-serif" color="#333333">Recebido Com D�bito Conta Corrente</font></td>
			        </tr>
			
					<tr class="child-of-node-recebidoDCO">
			            <td colspan="2">
			                <table width="100%" cellpadding="3" cellspacing="0">
			                    <c:forEach var="recebidoDCO" varStatus="indiceRecebidoDCO" items="${RelatorioFechamentoDiarioControle.listaRecebidosDCO}">
			                        <%-- Definir as cores da tabela zebrada --%>
			                        <c:choose>
			                            <c:when test="${indiceRecebidoDCO.count % 2 != 0}">
			                                <c:set var="corLinha" value="#FFFFFF" scope="request" />
			                                <c:set var="corLinhaRessaltada" value="#CCC1F2" scope="request" />
			                            </c:when>
			
			                            <c:otherwise>
			                                <c:set var="corLinha" value="#DFE8EF" scope="request" />
			                                <c:set var="corLinhaRessaltada" value="#CCC1F2" scope="request" />
			                            </c:otherwise>
			                        </c:choose>
			
			                        <c:if test="${indiceRecebidoDCO.count == 1}">
			                            <tr style="background-color:#DFE8EF;">
			                                <td align="left">Data de Registro</td>
			                                <td align="left">Nome Comprador</td>
			                                <td align="left">Produto</td>
			                                <td align="left">Pacote</td>
			                                <td align="left">Tipo Comprador</td>
			                                <td align="right">Qtd</td>
			                                <td align="right">Total</td>
			                            </tr>
			                        </c:if>
			
			                        <tr bgcolor="${corLinha}"
			                            onmouseover="mudar_cor(this,'${corLinhaRessaltada}');"
			                            onmouseout="mudar_cor(this,'${corLinha}');">
			
			                            <td align="left"><font size="2x" face="Arial,Helvetica,sans-serif" color="#333333">${recebidoDCO.dataRegistro}</font></td>
			                            <td align="left"><font size="2x" face="Arial,Helvetica,sans-serif" color="#333333">${recebidoDCO.nomeComprador}</font></td>
			                            <td align="left"><font size="2x" face="Arial,Helvetica,sans-serif" color="#333333">${recebidoDCO.produtoDescricao}</font></td>
			                            <td align="left"><font size="2x" face="Arial,Helvetica,sans-serif" color="#333333">${recebidoDCO.descPacote}</font></td>
			                            <td align="left"><font size="2x" face="Arial,Helvetica,sans-serif" color="#333333">${recebidoDCO.tipoComprador_Apresentar}</font></td>
			                            <td align="right"><font size="2x" face="Arial,Helvetica,sans-serif" color="#333333">${recebidoDCO.quantidadeItemVenda}</font></td>
			                            <td align="right"><font size="2x" face="Arial,Helvetica,sans-serif" color="#333333">${recebidoDCO.valorTotal}</font></td>
			                        </tr>
			                    </c:forEach>
			                </table>
			            </td>
			        </tr>
			
			        <tr id="node-recebidoCCO">
			            <td style="text-align:right;white-space: nowrap;"><b><font color="#1E8FDE">${RelatorioFechamentoDiarioControle.totalVendaRecebidoCreditoContaCorrente}</font></b></td>
			            <td><font size="2x" face="Arial,Helvetica,sans-serif" color="#333333">Recebido Com Cr�dito Conta Corrente</font></td>
			        </tr>
			
					<tr class="child-of-node-recebidoCCO">
			            <td colspan="2">
			                <table width="100%" cellpadding="3" cellspacing="0">
			                    <c:forEach var="recebidoCCO" varStatus="indiceRecebidoCCO" items="${RelatorioFechamentoDiarioControle.listaRecebidosCCO}">
			                        <%-- Definir as cores da tabela zebrada --%>
			                        <c:choose>
			                            <c:when test="${indiceRecebidoCCO.count % 2 != 0}">
			                                <c:set var="corLinha" value="#FFFFFF" scope="request" />
			                                <c:set var="corLinhaRessaltada" value="#CCC1F2" scope="request" />
			                            </c:when>
			
			                            <c:otherwise>
			                                <c:set var="corLinha" value="#DFE8EF" scope="request" />
			                                <c:set var="corLinhaRessaltada" value="#CCC1F2" scope="request" />
			                            </c:otherwise>
			                        </c:choose>
			
			                        <c:if test="${indiceRecebidoCCO.count == 1}">
			                            <tr style="background-color:#DFE8EF;">
			                                <td align="left">Data de Registro</td>
			                                <td align="left">Nome Comprador</td>
			                                <td align="left">Produto</td>
			                                <td align="left">Pacote</td>
			                                <td align="left">Tipo Comprador</td>
			                                <td align="right">Qtd</td>
			                                <td align="right">Total</td>
			                            </tr>
			                        </c:if>
			
			                        <tr bgcolor="${corLinha}"
			                            onmouseover="mudar_cor(this,'${corLinhaRessaltada}');"
			                            onmouseout="mudar_cor(this,'${corLinha}');">
			
			                            <td align="left"><font size="2x" face="Arial,Helvetica,sans-serif" color="#333333">${recebidoCCO.dataRegistro}</font></td>
			                            <td align="left"><font size="2x" face="Arial,Helvetica,sans-serif" color="#333333">${recebidoCCO.nomeComprador}</font></td>
			                            <td align="left"><font size="2x" face="Arial,Helvetica,sans-serif" color="#333333">${recebidoCCO.produtoDescricao}</font></td>
			                            <td align="left"><font size="2x" face="Arial,Helvetica,sans-serif" color="#333333">${recebidoCCO.descPacote}</font></td>
			                            <td align="left"><font size="2x" face="Arial,Helvetica,sans-serif" color="#333333">${recebidoCCO.tipoComprador_Apresentar}</font></td>
			                            <td align="right"><font size="2x" face="Arial,Helvetica,sans-serif" color="#333333">${recebidoCCO.quantidadeItemVenda}</font></td>
			                            <td align="right"><font size="2x" face="Arial,Helvetica,sans-serif" color="#333333">${recebidoCCO.valorTotal}</font></td>
			                        </tr>
			                    </c:forEach>
			                </table>
			            </td>
			        </tr>
			
			        <tr id="node-faturadoNaoRecebido">
			            <td style="text-align:right;white-space: nowrap;"><b><font color="#1E8FDE">${RelatorioFechamentoDiarioControle.totalVendaFaturadoNaoRecebido}</font></b></td>
			            <td><font size="2x" face="Arial,Helvetica,sans-serif" color="#333333">Faturado N�o Recebido</font></td>
			        </tr>
			
			        <tr class="child-of-node-faturadoNaoRecebido">
			            <td colspan="2">
			                <table width="100%" cellpadding="3" cellspacing="0">
			                    <c:forEach var="faturadoNaoRecebido" varStatus="indiceFaturadoNaoRecebido"
			                               items="${RelatorioFechamentoDiarioControle.listaFaturadosNaoRecebidos}">
			
			                        <%-- Definir as cores da tabela zebrada --%>
			                        <c:choose>
			                            <c:when test="${indiceFaturadoNaoRecebido.count % 2 != 0}">
			                                <c:set var="corLinha" value="#FFFFFF" scope="request" />
			                                <c:set var="corLinhaRessaltada" value="#CCC1F2"
			                                       scope="request" />
			                            </c:when>
			                            <c:otherwise>
			                                <c:set var="corLinha" value="#DFE8EF" scope="request" />
			                                <c:set var="corLinhaRessaltada" value="#CCC1F2"
			                                       scope="request" />
			                            </c:otherwise>
			                        </c:choose>
			
			                        <c:if test="${indiceFaturadoNaoRecebido.count == 1}">
			                            <tr style="background-color:#DFE8EF;">
			                                <td align="left">Data de Registro</td>
			                                <td align="left">Nome Comprador</td>
			                                <td align="left">Produto</td>
			                                <td align="left">Pacote</td>
			                                <td align="left">Tipo Comprador</td>
			                                <td align="right">Qtd</td>
			                                <td align="right">Total</td>
			                            </tr>
			                        </c:if>
			                        
			                        <tr bgcolor="${corLinha}" id="node-faturadoNaoRecebido-${faturadoNaoRecebido.idAgenda}" 
			                            onmouseover="mudar_cor(this,'${corLinhaRessaltada}');"
			                            onmouseout="mudar_cor(this,'${corLinha}');">
			
			                            <td align="left"><font size="2x" face="Arial,Helvetica,sans-serif" color="#333333">${faturadoNaoRecebido.dataRegistro}</font></td>
			                            <td align="left"><font size="2x" face="Arial,Helvetica,sans-serif" color="#333333">${faturadoNaoRecebido.nomeComprador}</font></td>
			                            <td align="left"><font size="2x" face="Arial,Helvetica,sans-serif" color="#333333">${faturadoNaoRecebido.produtoDescricao}</font></td>
			                            <td align="left"><font size="2x" face="Arial,Helvetica,sans-serif" color="#333333">${faturadoNaoRecebido.descPacote}</font></td>
			                            <td align="left"><font size="2x" face="Arial,Helvetica,sans-serif" color="#333333">${faturadoNaoRecebido.tipoComprador_Apresentar}</font></td>
			                            <td align="right"><font size="2x" face="Arial,Helvetica,sans-serif" color="#333333">${faturadoNaoRecebido.quantidadeItemVenda}</font></td>
			                            <td align="right"><font size="2x" face="Arial,Helvetica,sans-serif" color="#333333">${faturadoNaoRecebido.valorTotal}</font></td>
			                        </tr>
			                    </c:forEach>
			                </table>
			            </td>
			        </tr>
			
			        <tr id="node-novosClientesCadastrados">
			            <td style="text-align:right;white-space: nowrap;"><b><font color="#1E8FDE">${RelatorioFechamentoDiarioControle.totalVendaClienteNovo}</font></b></td>
			            <td><font size="2x" face="Arial,Helvetica,sans-serif" color="#333333">Novos Clientes - Cadastrados ${RelatorioFechamentoDiarioControle.filtroPeriodo}</font></td>
			        </tr>
				</table>
			</td>
		</tr>
<%-------------------------------------------------------------- FIM VENDAS DE SESS�ES -----------------------------------------------------------%>

<%-------------------------------------------------------------- IN�CIO PACOTES VENDIDOS -----------------------------------------------------------%>
        <tr id="node-pacotes-vendidos">
            <td style="padding-left:30px;"><b><font color="#1E8FDE">${RelatorioFechamentoDiarioControle.totalPacotesVendidos}</font></b> Pacotes Vendidos </td>
        </tr>
        
        <tr id="node-lista-pacotes" class="child-of-node-pacotes-vendidos">
            <td style="padding-left:30px;">
                <table width="100%">
                    <c:forEach var="vendaPacotes" varStatus="indiceVenda"
                               items="${RelatorioFechamentoDiarioControle.listaPacotes}">
                        <tr id="node-venda-${vendaPacotes.pacoteVO.codigo}">
                            <td>
                                <font size="2x" face="Arial,Helvetica,sans-serif" color="#333333"> ${vendaPacotes.pacoteVO.descricao} - Valor: ${vendaPacotes.valorTotal}
                                    <c:if test="${vendaPacotes.pacoteVO.pacoteGenerico}">
                                        - Produto Gen�rico
                                    </c:if>
                                </font></td>
                        </tr>
                        <tr id="node-lista-pacotes-itens" class="child-of-node-venda-${vendaPacotes.pacoteVO.codigo}">
                            <td>
                                <table width="100%">
                                    <c:forEach var="venda" varStatus="indiceVenda"
                                               items="${vendaPacotes.pacoteVO.listaRelFechamDiario}">

                                        <%-- Definir as cores da tabela zebrada --%>
                                        <c:choose>
                                            <c:when test="${indiceVenda.count % 2 != 0}">
                                                <c:set var="corLinha" value="#FFFFFF" scope="request" />
                                                <c:set var="corLinhaRessaltada" value="#CCC1F2"
                                                       scope="request" />
                                            </c:when>
                                            <c:otherwise>
                                                <c:set var="corLinha" value="#DFE8EF" scope="request" />
                                                <c:set var="corLinhaRessaltada" value="#CCC1F2"
                                                       scope="request" />
                                            </c:otherwise>
                                        </c:choose>

                                        <c:if test="${indiceVenda.count == 1}">
                                            <tr style="background-color:#DFE8EF;" id="node-venda">
                                                <td>Data de Registro</td>
                                                <td>Nome Comprador</td>
                                                <td>Produto</td>
                                                <td>Tipo Comprador</td>
                                                <td>Qtd</td>
                                                <td align="right">Total</td>
                                            </tr>
                                        </c:if>
                                        <tr bgcolor="${corLinha}" id="node-venda"
                                            onmouseover="mudar_cor(this,'${corLinhaRessaltada}');"
                                            onmouseout="mudar_cor(this,'${corLinha}');">

                                            <td><font size="2x" face="Arial,Helvetica,sans-serif" color="#333333">${venda.dataRegistro}</font></td>
                                            <td><font size="2x" face="Arial,Helvetica,sans-serif" color="#333333">${venda.nomeComprador}</font></td>
                                            <td><font size="2x" face="Arial,Helvetica,sans-serif" color="#333333">${venda.produtoDescricao}</font></td>
                                            <td><font size="2x" face="Arial,Helvetica,sans-serif" color="#333333">${venda.tipoComprador_Apresentar}</font></td>
                                            <td align="right"><font size="2x" face="Arial,Helvetica,sans-serif" color="#333333">${venda.quantidadeItemVenda}</font></td>
                                            <td align="right"><font size="2x" face="Arial,Helvetica,sans-serif" color="#333333">${venda.valorTotal}</font></td>
                                        </tr>
                                        
                                    </c:forEach>
                                </table>
                            </td>
                        </tr>
                    </c:forEach>
                </table>
            </td>
        </tr>
<%-------------------------------------------------------------- FIM PACOTES VENDIDOS -----------------------------------------------------------%>

<%-------------------------------------------------------------- IN�CIO AGENDAMENTOS LAN�ADOS -----------------------------------------------------------%>
        <tr id="node-agendamentos" >
            <td style="padding-left:30px;"><b><font color="#1E8FDE">${RelatorioFechamentoDiarioControle.totalAgendamentosLancados}</font></b> Agendamentos Lan�ados</td>
        </tr>
        <tr class="child-of-node-agendamentos">
            <td width="100%">
                <table width="100%" border="0">
                    <tr>
                        <td style="text-align:right"><b><font color="#1E8FDE">${RelatorioFechamentoDiarioControle.totalAgendamentosClientes}</font></b></td>
                        <td width="100%"><font size="2x" face="Arial,Helvetica,sans-serif" color="#333333">Clientes</font></td>
                    </tr>
                    <tr>
                        <td style="text-align:right"><b><font color="#1E8FDE">${RelatorioFechamentoDiarioControle.totalAgendamentosMes}</font></b></td>
                        <td width="100%"><font size="2x" face="Arial,Helvetica,sans-serif" color="#333333">Agendados dentro do m�s ${RelatorioFechamentoDiarioControle.mesInicial}</font></td>
                    </tr>
                    <tr>
                        <td style="text-align:right"><b><font color="#1E8FDE">${RelatorioFechamentoDiarioControle.totalAgendamentosPeriodo}</font></b></td>
                        <td width="100%"><font size="2x" face="Arial,Helvetica,sans-serif" color="#333333">Agendamentos para serem realizados ${RelatorioFechamentoDiarioControle.filtroPeriodo}</font></td>
                    </tr>
                    <tr>
                        <td style="text-align:right"><b><font color="#1E8FDE">${RelatorioFechamentoDiarioControle.totalAgendamentosRetroativo}</font></b></td>
                        <td width="100%"><font size="2x" face="Arial,Helvetica,sans-serif" color="#333333">Retroativo para datas anteriores ao dia ${RelatorioFechamentoDiarioControle.periodoInicial_Apresentar}</font></td>
                    </tr>
                    <tr >
                        <td style="text-align:right"><b><font color="#1E8FDE">${RelatorioFechamentoDiarioControle.totalAgendamentosProximosMeses}</font></b></td>
                        <td width="100%"><font size="2x" face="Arial,Helvetica,sans-serif" color="#333333">Agendamentos posteriores ao dia ${RelatorioFechamentoDiarioControle.periodoFinal_Apresentar}</font></td>
                    </tr>

			        <tr id="node-agendamentos-lista">
			            <td> </td>
			        </tr>
			        <tr class="child-of-node-agendamentos-lista">
			            <td colspan="2">
			                <table width="100%" cellpadding="3" cellspacing="0">
			                    <c:forEach var="agendamento" varStatus="indiceAgendamento"
			                               items="${RelatorioFechamentoDiarioControle.listaAgendamentosPeriodo}">
			                        <%-- Definir as cores da tabela zebrada --%>
			                        <c:choose>
			                            <c:when test="${indiceAgendamento.count % 2 != 0}">
			                                <c:set var="corLinha" value="#FFFFFF" scope="request" />
			                                <c:set var="corLinhaRessaltada" value="#CCC1F2"
			                                       scope="request" />
			                            </c:when>
			                            <c:otherwise>
			                                <c:set var="corLinha" value="#DFE8EF" scope="request" />
			                                <c:set var="corLinhaRessaltada" value="#CCC1F2"
			                                       scope="request" />
			                            </c:otherwise>
			                        </c:choose>
			
			                        <c:if test="${indiceAgendamento.count == 1}">
			                            <tr style="background-color:#DFE8EF;" id="node-column-${agendamento.idAgenda}">
			                                <td>Data Aula</td>
			                                <td>Hor�rio</td>
			                                <td>Cliente</td>
			                                <td>Servi�o</td>
			                                <td>Profissional</td>
			                                <td>Ambiente</td>
			                                <td>Status</td>
			                                <td>Tipo Hor�rio</td>
			                                <td>Usu�rio</td>
			                                <td>Data Lan�ada</td>
			                                <td>Pacote</td>
			                            </tr>
			                        </c:if>
			                        <tr bgcolor="${corLinha}" id="node-${agendamento.idAgenda}"
			                            onmouseover="mudar_cor(this,'${corLinhaRessaltada}');"
			                            onmouseout="mudar_cor(this,'${corLinha}');">
			                            <td><font size="2x" face="Arial,Helvetica,sans-serif" color="#333333">${agendamento.dataAula}</font></td>
			                            <td>
			                                <font size="2x" face="Arial,Helvetica,sans-serif" color="#333333">${agendamento.horaInicio}</font>
			                                <c:if test="${agendamento.horaTermino !=null}">
			                                    <font size="2x" face="Arial,Helvetica,sans-serif" color="#333333">- ${agendamento.horaTermino}</font>
			                                </c:if>
			                            </td>
			                            <td>
			                                <a href="#"
			                                   onclick="preencherHiddenChamarBotao('visualizarCliente', 'idCliente','${agendamento.cliente}')">
			                                    <font size="2x" face="Arial,Helvetica,sans-serif" color="#333333">${agendamento.descCliente}</font>
			                                </a>
			                            </td>
			                            <td>
			                                <a href="#"
			                                   onclick="preencherHiddenChamarBotao('chamaModalAgenda', 'idEntidade','${agendamento.idAgenda}')">
			                                    <font size="2x" face="Arial,Helvetica,sans-serif" color="#333333">${agendamento.descProduto}</font>
			                                </a>
			                            </td>
			                            <td><font size="2x" face="Arial,Helvetica,sans-serif" color="#333333">${agendamento.descColaborador}</font></td>
			                            <td><font size="2x" face="Arial,Helvetica,sans-serif" color="#333333">${agendamento.descAmbiente}</font></td>
			                            <td><font size="2x" face="Arial,Helvetica,sans-serif" color="#333333">${agendamento.descStatus}</font></td>
			                            <td><font size="2x" face="Arial,Helvetica,sans-serif" color="#333333">${agendamento.descTipoHorario}</font></td>
			                            <td><font size="2x" face="Arial,Helvetica,sans-serif" color="#333333">${agendamento.usuarioLancamento}</font></td>
			                            <td><font size="2x" face="Arial,Helvetica,sans-serif" color="#333333">${agendamento.dataLancamento}</font></td>
			                            <td><font size="2x" face="Arial,Helvetica,sans-serif" color="#333333">${agendamento.pacoteVO.titulo}</font></td>
			                        </tr>
			                    </c:forEach>
			                </table>
			            </td>
			        </tr>
                </table>
            </td>
        </tr>
<%-------------------------------------------------------------- FIM AGENDAMENTOS LAN�ADOS -----------------------------------------------------------%>

<%-------------------------------------------------------------- IN�CIO ALUNOS EM TURMAS MONITORADAS -----------------------------------------------------------%>
        <tr id="node-alunos" >
            <td style="padding-left:30px;"><b><font color="#1E8FDE">${RelatorioFechamentoDiarioControle.totalAlunosGeral} </font></b>Alunos em Turmas Monitoradas</td>
        </tr>
        
        <tr class="child-of-node-alunos">
        	<td width="100%">
                <table width="100%" border="0">
			        <tr>
			            <td style="text-align:right"><b><font color="#1E8FDE">${RelatorioFechamentoDiarioControle.totalAlunosPresencas}</font></b></td>
			            <td width="100%"><font size="2x" face="Arial,Helvetica,sans-serif" color="#333333">Presen�as</font></td>
			        </tr>
			        <tr>
			            <td style="text-align:right"><b><font color="#1E8FDE">${RelatorioFechamentoDiarioControle.totalAlunosFaltas}</font></b></td>
			            <td width="100%"><font size="2x" face="Arial,Helvetica,sans-serif" color="#333333">Faltas</font></td>
			        </tr>
			        <tr>
			            <td style="text-align:right"><b><font color="#1E8FDE">${RelatorioFechamentoDiarioControle.totalAlunosReposicoesPrevistas}</font></b></td>
			            <td width="100%"><font size="2x" face="Arial,Helvetica,sans-serif" color="#333333">Reposi��es Previstas</font></td>
			        </tr>
			        <tr id="node-alunos-realizadas">
			            <td style="text-align:right"><b><font color="#1E8FDE">${RelatorioFechamentoDiarioControle.totalAlunosReposicoesRealizadas}</font></b></td>
			            <td width="100%"><font size="2x" face="Arial,Helvetica,sans-serif" color="#333333">Reposi��es Realizadas</font></td>
			        </tr>
			        <tr class="child-of-node-alunos-realizadas">
			            <td colspan="2">
			                <table width="100%" cellpadding="3" cellspacing="0">
			                    <c:forEach var="reposicaoRealizada" varStatus="indiceReposicaoRealizada"
			                               items="${RelatorioFechamentoDiarioControle.listaReposicoesRealizadas}">
			                        <%-- Definir as cores da tabela zebrada --%>
			                        <c:choose>
			                            <c:when test="${indiceReposicaoRealizada.count % 2 != 0}">
			                                <c:set var="corLinha" value="#FFFFFF" scope="request" />
			                                <c:set var="corLinhaRessaltada" value="#CCC1F2"
			                                       scope="request" />
			                            </c:when>
			                            <c:otherwise>
			                                <c:set var="corLinha" value="#DFE8EF" scope="request" />
			                                <c:set var="corLinhaRessaltada" value="#CCC1F2"
			                                       scope="request" />
			                            </c:otherwise>
			                        </c:choose>
			
			                        <c:if test="${indiceReposicaoRealizada.count == 1}">
			                            <tr style="background-color:#DFE8EF;" id="node-column-${reposicaoRealizada.codigo}">
			                                <td>Nome Cliente</td>
			                                <td>Contrato</td>
			                                <td>Data Lan�amento</td>
			                                <td>Data Reposi��o</td>
			                                <td>Data Presen�a</td>
			                                <td>Turma Destino</td>
			                                <td>Dia Semana</td>
			                                <td>Hora Inicial</td>
			                                <td>Hora Final</td>
			                            </tr>
			                        </c:if>
			                        
			                        <tr bgcolor="${corLinha}" id="node-${reposicaoRealizada.codigo}"
			                            onmouseover="mudar_cor(this,'${corLinhaRessaltada}');"
			                            onmouseout="mudar_cor(this,'${corLinha}');">
			                            <td><font size="2x" face="Arial,Helvetica,sans-serif" color="#333333">${reposicaoRealizada.cliente.pessoa.nome}</font></td>
			                            <td><font size="2x" face="Arial,Helvetica,sans-serif" color="#333333">${reposicaoRealizada.contrato.codigo}</font></td>
			                            <td><font size="2x" face="Arial,Helvetica,sans-serif" color="#333333">${reposicaoRealizada.dataLancamento_Apresentar}</font></td>
			                            <td><font size="2x" face="Arial,Helvetica,sans-serif" color="#333333">${reposicaoRealizada.dataReposicao_Apresentar}</font></td>
			                            <td><font size="2x" face="Arial,Helvetica,sans-serif" color="#333333">${reposicaoRealizada.dataPresenca_Apresentar}</font></td>
			                            <td><font size="2x" face="Arial,Helvetica,sans-serif" color="#333333">${reposicaoRealizada.turmaDestino.descricao}</font></td>
			                            <td><font size="2x" face="Arial,Helvetica,sans-serif" color="#333333">${reposicaoRealizada.horarioTurma.diaSemana}</font></td>
			                            <td><font size="2x" face="Arial,Helvetica,sans-serif" color="#333333">${reposicaoRealizada.horarioTurma.horaInicial}</font></td>
			                            <td><font size="2x" face="Arial,Helvetica,sans-serif" color="#333333">${reposicaoRealizada.horarioTurma.horaFinal}</font></td>
			                        </tr>
			                    </c:forEach>
			                </table>
			            </td>
			        </tr>
			        <tr id="node-alunos-solicitacoes">
			            <td style="text-align:right"><b><font color="#1E8FDE">${RelatorioFechamentoDiarioControle.totalAlunosReposicoesSolicitadas}</font></b></td>
			            <td width="100%"><font size="2x" face="Arial,Helvetica,sans-serif" color="#333333"> Reposi��es Solicitadas</font></td>
			        </tr>
			        <tr class="child-of-node-alunos-solicitacoes">
			            <td colspan="2">
			                <table width="100%" cellpadding="3" cellspacing="0">
			                    <c:forEach var="reposicaoSolicitada" varStatus="indiceReposicaoSolicitada"
			                               items="${RelatorioFechamentoDiarioControle.listaReposicoesSolicitadas}">
			                        <%-- Definir as cores da tabela zebrada --%>
			                        <c:choose>
			                            <c:when test="${indiceReposicaoSolicitada.count % 2 != 0}">
			                                <c:set var="corLinha" value="#FFFFFF" scope="request" />
			                                <c:set var="corLinhaRessaltada" value="#CCC1F2"
			                                       scope="request" />
			                            </c:when>
			                            <c:otherwise>
			                                <c:set var="corLinha" value="#DFE8EF" scope="request" />
			                                <c:set var="corLinhaRessaltada" value="#CCC1F2"
			                                       scope="request" />
			                            </c:otherwise>
			                        </c:choose>
			
			                        <c:if test="${indiceReposicaoSolicitada.count == 1}">
			                            <tr style="background-color:#DFE8EF;" id="node-column-${reposicaoSolicitada.codigo}">
			                                <td>Nome Cliente</td>
			                                <td>Contrato</td>
			                                <td>Data Lan�amento</td>
			                                <td>Data Reposi��o</td>
			                                <td>Data Presen�a</td>
			                                <td>Turma Destino</td>
			                                <td>Dia Semana</td>
			                                <td>Hora Inicial</td>
			                                <td>Hora Final</td>
			                            </tr>
			                        </c:if>
			                        <tr bgcolor="${corLinha}" id="node-${reposicaoSolicitada.codigo}"
			                            onmouseover="mudar_cor(this,'${corLinhaRessaltada}');"
			                            onmouseout="mudar_cor(this,'${corLinha}');">
			                            <td>
			                                <font size="2x" face="Arial,Helvetica,sans-serif" color="#333333">${reposicaoSolicitada.cliente.pessoa.nome}</font>
			                            </td>
			                            <td>
			                                <font size="2x" face="Arial,Helvetica,sans-serif" color="#333333">${reposicaoSolicitada.contrato.codigo}</font>
			                            </td>
			                            <td>
			                                <font size="2x" face="Arial,Helvetica,sans-serif" color="#333333">${reposicaoSolicitada.dataLancamento_Apresentar}</font>
			                            </td>
			                            <td>
			                                <font size="2x" face="Arial,Helvetica,sans-serif" color="#333333">${reposicaoSolicitada.dataReposicao_Apresentar}</font>
			                            </td>
			                            <td>
			                                <font size="2x" face="Arial,Helvetica,sans-serif" color="#333333">${reposicaoSolicitada.dataPresenca_Apresentar}</font>
			                            </td>
			                            <td>
			                                <font size="2x" face="Arial,Helvetica,sans-serif" color="#333333">${reposicaoSolicitada.turmaDestino.descricao}</font>
			                            </td>
			                            <td>
			                                <font size="2x" face="Arial,Helvetica,sans-serif" color="#333333">${reposicaoSolicitada.horarioTurma.diaSemana}</font>
			                            </td>
			                            <td>
			                                <font size="2x" face="Arial,Helvetica,sans-serif" color="#333333">${reposicaoSolicitada.horarioTurma.horaInicial}</font>
			                            </td>
			                            <td>
			                                <font size="2x" face="Arial,Helvetica,sans-serif" color="#333333">${reposicaoSolicitada.horarioTurma.horaFinal}</font>
			                            </td>
			                        </tr>
			                    </c:forEach>
			                </table>
			            </td>
			        </tr>
				</table>
			</td>
		</tr>
<%-------------------------------------------------------------- FIM ALUNOS EM TURMAS MONITORADAS -----------------------------------------------------------%>

<%-------------------------------------------------------------- IN�CIO OUTRAS PEND�NCIAS DO EST�DIO -----------------------------------------------------------%>
        <tr id="node-outras">
            <td style="padding-left:30px;"><b><font color="#1E8FDE">${RelatorioFechamentoDiarioControle.totalOutrasGeral}</font></b> Outras Pend�ncias do Est�dio</td>
        </tr>
        
        <tr class="child-of-node-outras">
        	<td width="100%">
                <table width="100%" border="0">
			        <tr id="node-outras-faturar">
			            <td style="text-align:right;"><b><font color="#1E8FDE">${RelatorioFechamentoDiarioControle.totalOutrasAFaturar}</font></b></td>
			            <td width="100%"><font size="2x" face="Arial,Helvetica,sans-serif" color="#333333"> Agendamento(s) lan�ados e ainda n�o faturados (a Faturar)</font></td>
			        </tr>
			        <tr class="child-of-node-outras-faturar">
			            <td colspan="2">
			                <table width="100%" cellpadding="3" cellspacing="0">
			                    <c:forEach var="agendamento" varStatus="indiceAgendamento"
			                               items="${RelatorioFechamentoDiarioControle.listaAgendamentosAFaturar}">
			                        <%-- Definir as cores da tabela zebrada --%>
			                        <c:choose>
			                            <c:when test="${indiceAgendamento.count % 2 != 0}">
			                                <c:set var="corLinha" value="#FFFFFF" scope="request" />
			                                <c:set var="corLinhaRessaltada" value="#CCC1F2"
			                                       scope="request" />
			                            </c:when>
			                            <c:otherwise>
			                                <c:set var="corLinha" value="#DFE8EF" scope="request" />
			                                <c:set var="corLinhaRessaltada" value="#CCC1F2"
			                                       scope="request" />
			                            </c:otherwise>
			                        </c:choose>
			
			                        <c:if test="${indiceAgendamento.count == 1}">
			                            <tr style="background-color:#DFE8EF;" id="node-column-${agendamento.idAgenda}">
			                                <td>Data Aula</td>
			                                <td>Hor�rio</td>
			                                <td>Cliente</td>
			                                <td>Servi�o</td>
			                                <td>Profissional</td>
			                                <td>Ambiente</td>
			                                <td>Status</td>
			                                <td>Tipo Hor�rio</td>
			                                <td>Usu�rio</td>
			                                <td>Data Lan�ada</td>
			                                <td>Valor</td>
			                            </tr>
			                        </c:if>
			                        
			                        <tr bgcolor="${corLinha}" id="node-${agendamento.idAgenda}"
			                            onmouseover="mudar_cor(this,'${corLinhaRessaltada}');"
			                            onmouseout="mudar_cor(this,'${corLinha}');">
			                            <td><font size="2x" face="Arial,Helvetica,sans-serif" color="#333333">${agendamento.dataAula}</font></td>
			                            <td>
			                                <font size="2x" face="Arial,Helvetica,sans-serif" color="#333333">${agendamento.horaInicio}</font>
			                                <c:if test="${agendamento.horaTermino !=null}">
			                                    <font size="2x" face="Arial,Helvetica,sans-serif" color="#333333">- ${agendamento.horaTermino}</font>
			                                </c:if>
			                            </td>
			                            <td>
			                                <a href="#"
			                                   onclick="preencherHiddenChamarBotao('visualizarCliente', 'idCliente','${agendamento.cliente}')">
			                                    <font size="2x" face="Arial,Helvetica,sans-serif" color="#333333">${agendamento.descCliente}</font>
			                                </a>
			                            </td>
			                            <td>
			                                <a href="#"
			                                   onclick="preencherHiddenChamarBotao('chamaModalAgenda', 'idEntidade','${agendamento.idAgenda}')">
			                                    <font size="2x" face="Arial,Helvetica,sans-serif" color="#333333">${agendamento.descProduto}</font>
			                                </a>
			                            </td>
			                            <td><font size="2x" face="Arial,Helvetica,sans-serif" color="#333333">${agendamento.descColaborador}</font></td>
			                            <td><font size="2x" face="Arial,Helvetica,sans-serif" color="#333333">${agendamento.descAmbiente}</font></td>
			                            <td><font size="2x" face="Arial,Helvetica,sans-serif" color="#333333">${agendamento.descStatus}</font></td>
			                            <td><font size="2x" face="Arial,Helvetica,sans-serif" color="#333333">${agendamento.descTipoHorario}</font></td>
			                            <td><font size="2x" face="Arial,Helvetica,sans-serif" color="#333333">${agendamento.usuarioLancamento}</font></td>
			                            <td><font size="2x" face="Arial,Helvetica,sans-serif" color="#333333">${agendamento.dataLancamento}</font></td>
			                            <td><font size="2x" face="Arial,Helvetica,sans-serif" color="#333333">${agendamento.valorTotal}</font></td>
			                        </tr>
			                    </c:forEach>
			                </table>
			            </td>
			        </tr>
			        
			        <tr id="node-outras-sessoes-vendidas">
			            <td style="text-align:right;"><b><font color="#1E8FDE">${RelatorioFechamentoDiarioControle.totalOutrasAAgendar}</font></b></td>
			            <td width="100%"><font size="2x" face="Arial,Helvetica,sans-serif" color="#333333"> Sess�es vendidas/faturadas e ainda n�o agendadas</font></td>
			        </tr>
			        
			        <tr class="child-of-node-outras-sessoes-vendidas">
			            <td colspan="2">
			                <table width="100%" cellpadding="3" cellspacing="0">
			                    <c:forEach var="venda" varStatus="indiceAgendamento"
			                               items="${RelatorioFechamentoDiarioControle.listaSessoesVendidasAAgendar}">
			                        <%-- Definir as cores da tabela zebrada --%>
			                        <c:choose>
			                            <c:when test="${indiceAgendamento.count % 2 != 0}">
			                                <c:set var="corLinha" value="#FFFFFF" scope="request" />
			                                <c:set var="corLinhaRessaltada" value="#CCC1F2"
			                                       scope="request" />
			                            </c:when>
			                            <c:otherwise>
			                                <c:set var="corLinha" value="#DFE8EF" scope="request" />
			                                <c:set var="corLinhaRessaltada" value="#CCC1F2"
			                                       scope="request" />
			                            </c:otherwise>
			                        </c:choose>
			
			                        <c:if test="${indiceAgendamento.count == 1}">
			                            <tr style="background-color:#DFE8EF;" id="node-column-${venda.idVenda}">
			                                <td align="center">Data Registro</td>
			                                <td>Nome do Comprador</td>
			                                <td align="center">Tipo do Comprador</td>
			                                <td>Produto</td>
			                                <td align="center">Quantidade</td>
			                                <td align="center">Valor Total</td>
			                            </tr>
			                        </c:if>
			                        <tr bgcolor="${corLinha}" id="node-${agendamento.idVenda}"
			                            onmouseover="mudar_cor(this,'${corLinhaRessaltada}');"
			                            onmouseout="mudar_cor(this,'${corLinha}');">
			
			                            <td align="center"><font size="2x" face="Arial,Helvetica,sans-serif" color="#333333">${venda.dataRegistro}</font></td>
			                            <td align="left"><font size="2x" face="Arial,Helvetica,sans-serif" color="#333333">${venda.nomeComprador}</font></td>
			                            <td align="center"><font size="2x" face="Arial,Helvetica,sans-serif" color="#333333">${venda.tipoComprador_Apresentar}</font></td>
			                            <td><font size="2x" face="Arial,Helvetica,sans-serif" color="#333333">${venda.produtoDescricao}</font></td>
			                            <td align="center"><font size="2x" face="Arial,Helvetica,sans-serif" color="#333333">${venda.quantidadeItemVenda}</font></td>
			                            <td align="right"><font size="2x" face="Arial,Helvetica,sans-serif" color="#333333">${venda.valorTotal}</font></td>
			                        </tr>
			                    </c:forEach>
			                </table>
			            </td>
			        </tr>
			        
			        <tr id="node-outras-faltas">
			            <td style="text-align:right;"><b><font color="#1E8FDE">${RelatorioFechamentoDiarioControle.totalOutrasExcecao}</font></b></td>
			            <td width="100%"><font size="2x" face="Arial,Helvetica,sans-serif" color="#333333"> Falta(s) justificada(s) e ainda n�o agendadas (a Agendar)</font></td>
			        </tr>
			        
			        <tr class="child-of-node-outras-faltas">
			            <td colspan="2">
			                <table width="100%" cellpadding="3" cellspacing="0">
			                    <c:forEach var="agendamento" varStatus="indiceAgendamento"
			                               items="${RelatorioFechamentoDiarioControle.listaFaltasJustificadasAAgendar}">
			                        <%-- Definir as cores da tabela zebrada --%>
			                        <c:choose>
			                            <c:when test="${indiceAgendamento.count % 2 != 0}">
			                                <c:set var="corLinha" value="#FFFFFF" scope="request" />
			                                <c:set var="corLinhaRessaltada" value="#CCC1F2"
			                                       scope="request" />
			                            </c:when>
			                            <c:otherwise>
			                                <c:set var="corLinha" value="#DFE8EF" scope="request" />
			                                <c:set var="corLinhaRessaltada" value="#CCC1F2"
			                                       scope="request" />
			                            </c:otherwise>
			                        </c:choose>
			
			                        <c:if test="${indiceAgendamento.count == 1}">
			                            <tr style="background-color:#DFE8EF;" id="node-column-${agendamento.idAgenda}">
			                                <td>Data Aula</td>
			                                <td>Hor�rio</td>
			                                <td>Cliente</td>
			                                <td>Servi�o</td>
			                                <td>Profissional</td>
			                                <td>Ambiente</td>
			                                <td>Tipo Hor�rio</td>
			                                <td>Usu�rio</td>
			                                <td>Data Lan�ada</td>
			                            </tr>
			                        </c:if>
			                        
			                        <tr bgcolor="${corLinha}" id="node-${agendamento.idAgenda}"
			                            onmouseover="mudar_cor(this,'${corLinhaRessaltada}');"
			                            onmouseout="mudar_cor(this,'${corLinha}');">
			                            <td><font size="2x" face="Arial,Helvetica,sans-serif" color="#333333">${agendamento.dataAula}</font></td>
			                            <td>
			                                <font size="2x" face="Arial,Helvetica,sans-serif" color="#333333">${agendamento.horaInicio}</font>
			                                <c:if test="${agendamento.horaTermino !=null}">
			                                    <font size="2x" face="Arial,Helvetica,sans-serif" color="#333333">- ${agendamento.horaTermino}</font>
			                                </c:if>
			                            </td>
			                            <td><font size="2x" face="Arial,Helvetica,sans-serif" color="#333333">${agendamento.descCliente}</font></td>
			                            <td><font size="2x" face="Arial,Helvetica,sans-serif" color="#333333">${agendamento.descProduto}</font></td>
			                            <td><font size="2x" face="Arial,Helvetica,sans-serif" color="#333333">${agendamento.descColaborador}</font></td>
			                            <td><font size="2x" face="Arial,Helvetica,sans-serif" color="#333333">${agendamento.descAmbiente}</font></td>
			                            <td><font size="2x" face="Arial,Helvetica,sans-serif" color="#333333">${agendamento.descTipoHorario}</font></td>
			                            <td><font size="2x" face="Arial,Helvetica,sans-serif" color="#333333">${agendamento.usuarioLancamento}</font></td>
			                            <td><font size="2x" face="Arial,Helvetica,sans-serif" color="#333333">${agendamento.dataLancamento}</font></td>
			                        </tr>
			                    </c:forEach>
			                </table>
			            </td>
			        </tr>
				</table>
			</td>
		</tr>
<%-------------------------------------------------------------- FIM OUTRAS PEND�NCIAS DO EST�DIO -----------------------------------------------------------%>
    </table>
    <a4j:commandButton action="#{RelatorioFechamentoDiarioControle.editarAgendamento}"
                       id="chamaModalAgenda"
                       reRender="toolTipAgenda"
                       oncomplete="Richfaces.showModalPanel('toolTipAgenda')"
                       style="visibility: hidden;">
    </a4j:commandButton>
    <h:inputHidden id="idEntidade" value="#{RelatorioFechamentoDiarioControle.idAgenda}" />
    <h:inputHidden id="idCliente" value="#{RelatorioFechamentoDiarioControle.cliente}" />
    <a4j:commandButton id="visualizarCliente"
                       style="visibility: hidden;" oncomplete="abrirPopup('../../clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);"
                       action="#{RelatorioFechamentoDiarioControle.irParaTelaCliente}" >
        <f:param name="state" value="AC"/>
    </a4j:commandButton>
</h:panelGroup>

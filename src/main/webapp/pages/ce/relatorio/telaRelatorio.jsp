<%@page contentType="text/html" pageEncoding="ISO-8859-1" %>
<%@include file="../includes/include_imports.jsp" %>

<f:view>
    <jsp:include page="/includes/include_carregando_ripple.jsp" flush="true"/>
    <h:form id="form">
        <html>
        <!-- <PERSON><PERSON><PERSON> o elemento HEAD da página -->
        <head>
            <%@include file="../includes/include_head_ce.jsp" %>
        </head>
        <body class="ce">


        <h:panelGroup layout="block" style="display: inline-table;width: 100%;" styleClass="fundoCinza">

            <h:panelGroup layout="block" styleClass="bgtop topoZW" rendered="#{MenuControle.apresentarTopo}">
                <jsp:include page="../../../include_topo_2.0.jsp" flush="true"/>
                <jsp:include page="../../../include_menu_ce_flat.jsp" flush="true"/>
                <rich:jQuery selector=".item3" query="addClass('menuItemAtual')"/>
            </h:panelGroup>

            <h:panelGroup layout="block" styleClass="caixaCorpo">
                <h:panelGroup layout="block" style="height: 80%;width: 100%">
                    <h:panelGroup layout="block" styleClass="caixaMenuLatel">
                        <h:panelGroup layout="block" styleClass="container-imagem container-conteudo-central">
                            <h:panelGrid columnClasses="w33,w33,w33" columns="3" width="100%" cellpadding="0" cellspacing="0">


                                <h:panelGrid columns="1" style="height:100%;" width="100%" cellpadding="5" cellspacing="5" >
                                    <h:panelGroup>
                                        <rich:spacer height="5px"/>
                                        <div>
                                            <h:outputLink styleClass="linkWiki"
                                                          value="#{SuperControle.urlBaseConhecimento}como-gerar-uma-lista-de-aniversariantes/"
                                                          title="Clique e saiba mais: Clientes Aniversariantes"
                                                          target="_blank" >
                                                <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                                            </h:outputLink>


                                            <a4j:commandLink styleClass="titulo3" action="#{LoginControle.vazio}" oncomplete="abrirPopup('../../../relatorio/clientePorAniversarioRel.jsp?modulo=centralEventos', 'aniversariantes', 780, 595);">
                                                <h:outputText  value="#{CElabels['menu.consulta.relatorios.clientesAniversariantes']}"/>
                                            </a4j:commandLink>
                                        </div>
                                        <rich:spacer height="25px"/>
                                        <rich:spacer width="10px"/>
                                        <h:outputText styleClass="text" value="Com este relatório você pode saber quem são seus clientes aniversariantes e parabenizá-los. Emita o relatório escolhendo os filtros que desejar, por exemplo, selecionando o mês atual será consultado todos os clientes aniversariantes dentro do período escolhido, para isto os clientes devem estar cadastrados com a data de nascimento correta."/>
                                    </h:panelGroup>
                                    <h:panelGroup>
                                        <rich:spacer height="25px"/>
                                        <div>
                                            <h:outputLink styleClass="linkWiki"
                                                          value="#{SuperControle.urlBaseConhecimento}como-verificar-o-relatorio-de-fechamento-de-caixa-por-operador/"
                                                          title="Clique e saiba mais: Fechamento de Caixa Por Operador"
                                                          target="_blank" >
                                                <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                                            </h:outputLink>

                                            <a4j:commandLink styleClass="titulo3" action="#{CaixaPorOperadorRelControleRel.novo}" oncomplete="abrirPopup('../../../relatorio/caixaPorOperador.jsp?modulo=centralEventos', 'CaixaPorOperador', 780, 595);" >
                                                <h:outputText  value="#{CElabels['menu.consulta.relatorios.fechamentoCaixa']}"/>
                                            </a4j:commandLink>
                                        </div>
                                        <rich:spacer height="25px"/>
                                        <rich:spacer width="10px"/>
                                        <h:outputText styleClass="text" value="É muito importante realizar o fechamento de caixa diariamente com os consultores de vendas ou atendentes, para que tenha o controle financeiro da empresa de forma correta e segura. Neste relatório você tem todas as informações necessárias para realizar o fechamento do caixa da empresa, por exemplo, o responsável pelo recebimento, o nome do cliente, os valores, a forma de pagamento, os totais, dentre outras informações."/>

                                    </h:panelGroup>
                                </h:panelGrid>
                                <h:panelGrid style="height:100;%" columns="1" width="100%" cellpadding="5" cellspacing="5" >
                                    <h:panelGroup>
                                        <div>
                                            <h:outputLink styleClass="linkWiki"
                                                          value="#{SuperControle.urlBaseConhecimento}como-verificar-a-receita-por-periodo-da-empresa/"
                                                          title="Clique e saiba mais: Receita por Período"
                                                          target="_blank" >
                                                <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                                            </h:outputLink>

                                            <a4j:commandLink styleClass="titulo3"  oncomplete="abrirPopup('../../../relatorio/receitaPorPeriodoSinteticoRel.jsp?modulo=centralEventos', 'ReceitaPorPeriodoSintetico', 780, 595);" action="#{ReceitaPorPeriodoSinteticoRelControleRel.novo}" >
                                                <h:outputText  value="#{CElabels['menu.consulta.relatorios.receitaPeriodo']}"/>
                                            </a4j:commandLink>
                                        </div>
                                        <rich:spacer height="25px"/>
                                        <rich:spacer width="10px"/>
                                        <h:outputText styleClass="text" value="Com este relatório você pode verificar de forma resumida a sua receita para realizar investimentos. É possível saber os recebimentos pela forma de pagamento utilizada na quitação de parcelas. Exemplo: Cartão de Crédito, Cheque, Dinheiro, etc. E também visualizar por recebimentos compensados ou quando foi o lançamento no caixa."/>
                                    </h:panelGroup>
                                    <h:panelGroup>
                                        <rich:spacer height="25px"/>
                                        <div>
                                            <h:outputLink styleClass="linkWiki"
                                                          value="#{SuperControle.urlWiki}Relatorios:Parcelas_em_Aberto"
                                                          title="Clique e saiba mais: Parcelas Aberto"
                                                          target="_blank" >
                                                <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                                            </h:outputLink>

                                            <a4j:commandLink styleClass="titulo3"  oncomplete="abrirPopup('../../../relatorio/parcelaEmAbertoRel20.jsp?modulo=centralEventos', 'ParcelaEmAberto', 780, 585);" action="#{ParcelaEmAbertoControleRel.novo}" >
                                                <h:outputText  value="#{CElabels['menu.consulta.relatorios.parcelasAberto']}"/>
                                            </a4j:commandLink>
                                        </div>
                                        <rich:spacer height="25px"/>
                                        <rich:spacer width="10px"/>
                                        <h:outputText styleClass="text" value="Para não perder o controle do seu faturamento, este relatório traz todas as parcelas que se encontram em aberto para realizar o devido acompanhamento e quando necessário a cobrança. O relatório dispõe de diversos filtros para que tenha facilidade nas suas consultas, por exemplo, por cliente específico, por período, dentre outros filtros."/>
                                    </h:panelGroup>

                                </h:panelGrid>
                                <h:panelGrid columns="1" width="100%" style="height:100%;" cellpadding="5" cellspacing="5" >
                                    <h:panelGroup>
                                        <div><img src="${contexto}/imagens/cadastros.png"></div>
                                    </h:panelGroup>

                                    <h:panelGroup>
                                        <rich:spacer height="25px"/>
                                        <div>
                                            <h:outputLink styleClass="linkWiki"
                                                          value="#{SuperControle.urlBaseConhecimento}como-tirar-um-relatorio-dos-alunos-que-possuem-saldo-na-conta-corrente/"
                                                          title="Clique e saiba mais: Saldo de Conta Corrente Cliente"
                                                          target="_blank" >
                                                <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                                            </h:outputLink>


                                            <a4j:commandLink styleClass="titulo3"  oncomplete="abrirPopup('../../../relatorio/saldoContaCorrenteRel.jsp?modulo=centralEventos', 'ParcelaEmAberto', 780, 585);" action="#{ParcelaEmAbertoControleRel.novo}">
                                                <h:outputText  value="#{CElabels['menu.consulta.relatorios.saldoContaCorrenteCliente']}"/>
                                            </a4j:commandLink>
                                        </div>
                                        <rich:spacer height="25px"/>
                                        <rich:spacer width="10px"/>
                                        <h:outputText styleClass="text" value="Neste relatório você tem as informações de quais clientes tem saldo em conta com a empresa e quais são estes valores, para incentivar a utilização se for crédito ou o pagamento em caso de débito. Podem ser consultados todos os clientes, ou por cliente específico ou intervalo de valores por exemplo."/>
                                    </h:panelGroup>
                                </h:panelGrid>

                            </h:panelGrid>
                        </h:panelGroup>
                        <jsp:include page="../includes/include_box_menulatrelatorios.jsp" flush="true"/>
                    </h:panelGroup>
                </h:panelGroup>
            </h:panelGroup>

            <jsp:include page="../../../include_rodape_flat.jsp" flush="true" />
            <%@include file="../includes/include_focus.jsp" %>
        </h:panelGroup>
        </body>
        </html>
    </h:form>
</f:view>

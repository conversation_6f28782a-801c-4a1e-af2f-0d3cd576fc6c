<%@page contentType="text/html" pageEncoding="ISO-8859-1" %>
<%@include file="includes/include_imports.jsp" %>

<c:set var="moduloSession" value="1" scope="session"/>

<style>
    /* ---------------- <PERSON><PERSON> de rolagem Google Chrome---------------- */
    ::-webkit-scrollbar {
        width: 15px;
        height: 5px;
    }

    ::-webkit-scrollbar-track-piece {
        background: #aebcbf;
    }

    ::-webkit-scrollbar-thumb:vertical {
        height: 5px;
        background: #000;
        border-radius: 10px;
    }

    ::-webkit-scrollbar-thumb:horizontal {
        width: 5px;
        background-color: gray;
    }

</style>
<f:view>
    <jsp:include page="/includes/include_carregando_ripple.jsp" flush="true"/>
    <h:form id="form">
        <html>
        <!-- Inclui o elemento HEAD da página -->
        <head>
            <%@include file="includes/include_head_ce.jsp" %>
            <script type="text/javascript" language="javascript" src="${contexto}/script/telaInicial.js"></script>
        </head>
        <body class="ce">
        <h:panelGroup layout="block" style="display: inline-table;width: 100%;" styleClass="fundoCinza">

            <h:panelGroup layout="block" styleClass="bgtop topoZW" rendered="#{MenuControle.apresentarTopo}">
                <jsp:include page="../../include_topo_2.0.jsp" flush="true"/>
                <jsp:include page="../../include_menu_ce_flat.jsp" flush="true"/>
                <rich:jQuery selector=".item1" query="addClass('menuItemAtual')"/>
            </h:panelGroup>
            <script type="text/javascript" language="javascript">
              setDocumentCookie('popupsImportante', 'close',1);
            </script>

            <h:panelGroup layout="block" styleClass="caixaCorpo">
                <h:panelGroup layout="block" style="height: 80%;width: 100%">
                    <h:panelGroup layout="block" styleClass="caixaMenuLatel">
                        <h:panelGroup layout="block" styleClass="container-imagem container-conteudo-central">
                            <h:panelGroup layout="block" styleClass="container-box zw_ui especial ">
                                <h:panelGroup styleClass="container-box-header" layout="block">
                                    <h:panelGroup layout="block" styleClass="margin-box">
                                        <h:outputText styleClass="container-header-titulo" value="#{CElabels['menu.cadastros.perfisEventos.disponibilidadeAmb']}"/>
                                        <h:outputLink styleClass="linkWiki"
                                                      title="Clique e saiba mais: Agenda de Eventos"
                                                      target="_blank">
                                            <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                                        </h:outputLink>
                                    </h:panelGroup>
                                </h:panelGroup>
                                <h:panelGroup layout="block" styleClass="margin-box">
                                    <a4j:keepAlive beanName="ConsultaEventosControle"/>
                                    <h:graphicImage value="/pages/ce/img/hint.gif" style="border: 0px;display: inline-block;vertical-align: top"
                                                    onclick="ativarAjuda(['verificar', 'verificar1', 'verificar2', 'buscar', 'semanaAtual', 'semana', 'mes', 'quadra', 'salao']);"/>
                                    <h:panelGroup rendered="#{ConsultaEventosControle.validaMsg}">
                                        <h:panelGrid columns="1" width="100%" styleClass="tabMensagens">
                                            <h:panelGroup>
                                                <h:commandButton image="/imagens/bt_sucesso.png"/>&nbsp;&nbsp;&nbsp;
                                                <h:outputText styleClass="mensagem"
                                                              value="#{ConsultaEventosControle.msg}"/>
                                            </h:panelGroup>
                                        </h:panelGrid>
                                    </h:panelGroup>
                                    <table style="display:inline-table;">
                                        <tr>
                                            <td valign="top">
                                                <rich:simpleTogglePanel id="consultaFiltros"
                                                                        onclick="alternarMensagemFiltro();alternarMensagemAmbiente();"
                                                                        styleClass="colunaCentralizada"
                                                                        switchType="client" opened="false"
                                                                        width="670px" height="215px">
                                                    <f:facet name="header">
                                                        <h:outputText
                                                                value="#{CElabels['operacoes.consulta']}"/>
                                                    </f:facet>

                                                    <h:panelGrid columns="3" rowClasses="linhaImpar, linhaPar"
                                                                 columnClasses="classEsquerda, classDireita"
                                                                 width="100%">
                                                        <h:panelGrid columns="2" width="180px">
                                                            <h:column>
                                                                <a onmouseover="if (document.getElementById('hint-verificar').style.visibility != 'hidden') {toolTip('
                                                                    <h:outputText
                                                                            value="#{CElabels['entidade.dispAmbiente.hint.filtroData']}"/>' , 300 , 'blue')}"
                                                                   onmouseout="hideToolTip();">
                                                                    <img src="${contexto}/pages/ce/img/hint.gif"
                                                                         width="15px" id="hint-verificar"
                                                                         style="visibility: hidden;"
                                                                         height="15px" border="0px;"/>
                                                                </a>
                                                            </h:column>
                                                            <h:column>
                                                                <h:selectOneRadio id="tipoFiltroPeriodo"
                                                                                  layout="pageDirection"
                                                                                  styleClass="tituloCampos"
                                                                                  value="#{ConsultaEventosControle.tipoFiltroData}"
                                                                                  onclick="alternarFiltro();alternarMensagemFiltro();">
                                                                    <f:selectItems
                                                                            value="#{ConsultaEventosControle.tiposFiltroData}"/>
                                                                </h:selectOneRadio>
                                                            </h:column>

                                                        </h:panelGrid>
                                                        <h:panelGroup>
                                                            <div style="position: relative; top: 0px;">
                                                                <div id="filtroData"
                                                                     style="visibility: visible; position: absolute; top: 90px;">
                                                                    <rich:calendar id="data" styleClass="form"
                                                                                   value="#{ConsultaEventosControle.consultaEventosTO.data}"
                                                                                   datePattern="dd/MM/yyyy"
                                                                                   inputSize="10"
                                                                                   inputClass="form"
                                                                                   popup="false"
                                                                                   showHeader="true"
                                                                                   oninputblur="blurinput(this);"
                                                                                   oninputfocus="focusinput(this);"
                                                                                   style="position: absolute; top: -90px;"
                                                                                   onchanged="alternarMensagemFiltro();"
                                                                                   enableManualInput="true"
                                                                                   zindex="2"
                                                                                   showWeeksBar="false"/>
                                                                </div>

                                                                <div id="filtroMesSemana"
                                                                     style="visibility: hidden; position: absolute; top: 0px;">
                                                                    <h:panelGrid columns="1"
                                                                                 id="filtroMesSemanaGrid">
                                                                        <h:panelGroup>
                                                                            <h:outputText
                                                                                    styleClass="tituloCampos"
                                                                                    value="#{CElabels['entidade.consultaMes']}: "/><br/>
                                                                            <h:selectOneMenu
                                                                                    onblur="blurinput(this);"
                                                                                    onfocus="focusinput(this);"
                                                                                    id="meses"
                                                                                    value="#{ConsultaEventosControle.consultaEventosTO.codigoMes}"
                                                                                    style="width:175px">
                                                                                <f:selectItems
                                                                                        value="#{ConsultaEventosControle.meses}"/>
                                                                                <a4j:support event="onchange"
                                                                                             reRender="semanas"></a4j:support>
                                                                            </h:selectOneMenu><br/>
                                                                        </h:panelGroup>

                                                                        <h:panelGroup>
                                                                            <h:outputText
                                                                                    styleClass="tituloCampos"
                                                                                    value="#{CElabels['entidade.semana']}: "/>
                                                                            <br/>
                                                                            <h:selectOneMenu
                                                                                    onchange="alternarMensagemFiltro();"
                                                                                    onblur="blurinput(this);"
                                                                                    onfocus="focusinput(this);"
                                                                                    id="semanas"
                                                                                    value="#{ConsultaEventosControle.consultaEventosTO.codigoSemana}"
                                                                                    style="width:175px">
                                                                                <f:selectItem itemValue=""
                                                                                              itemLabel="#{CElabels['operacoes.selecione']}"/>
                                                                                <f:selectItems
                                                                                        value="#{ConsultaEventosControle.semanas}"/>
                                                                            </h:selectOneMenu>
                                                                        </h:panelGroup>
                                                                    </h:panelGrid>
                                                                </div>
                                                                <div id="filtroIntervalo"
                                                                     style="visibility: hidden;">
                                                                    <h:outputText styleClass="tituloCampos"
                                                                                  value="#{CElabels['entidade.data']}: "/><br/>
                                                                    <h:outputText styleClass="tituloCampos"
                                                                                  value="#{CElabels['entidade.data.de']} "/>
                                                                    <rich:calendar id="dataInicio"
                                                                                   styleClass="form"
                                                                                   value="#{ConsultaEventosControle.dataInicial}"
                                                                                   verticalOffset="-60"
                                                                                   onchanged="alternarMensagemFiltro();"
                                                                                   datePattern="dd/MM/yyyy"
                                                                                   inputSize="10"
                                                                                   inputClass="form"
                                                                                   oninputblur="blurinput(this);"
                                                                                   oninputfocus="focusinput(this);"
                                                                                   oninputchange="return validar_Data(this.id); limparMsgObrig('form:dataInicio', 'dataInicio');"
                                                                                   oninputkeypress="return mascara(this, '99/99/9999', event);"
                                                                                   enableManualInput="true"
                                                                                   zindex="2"
                                                                                   showWeeksBar="false"/><br/>

                                                                    <br/>
                                                                    <h:outputText styleClass="tituloCampos"
                                                                                  value="#{CElabels['entidade.dataAte']} "/>
                                                                    <rich:calendar id="dataFim"
                                                                                   styleClass="form"
                                                                                   value="#{ConsultaEventosControle.dataFinal}"
                                                                                   verticalOffset="-70"
                                                                                   horizontalOffset="-50"
                                                                                   datePattern="dd/MM/yyyy"
                                                                                   inputSize="10"
                                                                                   inputClass="form"
                                                                                   onchanged="alternarMensagemFiltro();"
                                                                                   oninputblur="blurinput(this);"
                                                                                   oninputfocus="focusinput(this);"
                                                                                   oninputchange="return validar_Data(this.id); limparMsgObrig('form:dataFim', 'dataFim');"
                                                                                   oninputkeypress="return mascara(this, '99/99/9999', event);"
                                                                                   enableManualInput="true"
                                                                                   zindex="2"
                                                                                   showWeeksBar="false"/>
                                                                </div>
                                                                <div id="divObg-dataFim"
                                                                     class="mensagemObrigatorio"></div>
                                                                <div id="divObg-filtroIntervalo"
                                                                     class="mensagemObrigatorio"></div>

                                                            </div>
                                                        </h:panelGroup>

                                                        <h:panelGrid columnClasses="classDireita" width="210px">
                                                            <h:panelGroup>
                                                                <a onmouseover="if (document.getElementById('hint-verificar1').style.visibility != 'hidden') {toolTip('
                                                                    <h:outputText
                                                                            value="#{CElabels['entidade.dispAmbiente.hint.categoriaAmbiente']}"/>' , 300 , 'blue')}"
                                                                   onmouseout="hideToolTip();">
                                                                    <img src="${contexto}/pages/ce/img/hint.gif"
                                                                         width="15px" id="hint-verificar1"
                                                                         style="visibility: hidden;"
                                                                         height="15px" border="0px;"/>
                                                                </a>&nbsp;&nbsp;&nbsp;<h:outputText
                                                                    styleClass="tituloCampos"
                                                                    value="#{CElabels['entidade.categoriaAmbiente']} "/>
                                                            </h:panelGroup>
                                                            <!-- Combo de Tipos de Ambiente -->
                                                            <h:selectOneMenu onblur="blurinput(this);"
                                                                             onfocus="focusinput(this);"
                                                                             id="tiposAmbiente"
                                                                             value="#{ConsultaEventosControle.consultaEventosTO.codigoTipoAmbiente}"
                                                                             style="width:175px">
                                                                <f:selectItem itemValue=""
                                                                              itemLabel="#{CElabels['operacoes.todos']}"/>
                                                                <f:selectItems
                                                                        value="#{ConsultaEventosControle.tiposAmbiente}"/>

                                                                <a4j:support event="onchange"
                                                                             reRender="ambientes"
                                                                             oncomplete="return alternarMensagemAmbiente();"></a4j:support>
                                                            </h:selectOneMenu>
                                                            <h:panelGroup>
                                                                <a onmouseover="if (document.getElementById('hint-verificar2').style.visibility != 'hidden') {toolTip('
                                                                    <h:outputText
                                                                            value="#{CElabels['entidade.dispAmbiente.hint.ambiente']}"/>' , 300 , 'blue')}"
                                                                   onmouseout="hideToolTip();">
                                                                    <img src="${contexto}/pages/ce/img/hint.gif"
                                                                         width="15px" id="hint-verificar2"
                                                                         style="visibility: hidden;"
                                                                         height="15px" border="0px;"/>
                                                                </a>&nbsp;&nbsp;&nbsp;<h:outputText
                                                                    styleClass="tituloCampos"
                                                                    value="#{CElabels['entidade.ambientes']}: "/>
                                                            </h:panelGroup>

                                                            <!-- Combo de Ambientes -->
                                                            <h:selectOneMenu onblur="blurinput(this);"
                                                                             onfocus="focusinput(this);"
                                                                             id="ambientes"
                                                                             value="#{ConsultaEventosControle.consultaEventosTO.codigoAmbiente}"
                                                                             style="width:175px"
                                                                             onchange="alternarMensagemAmbiente()">
                                                                <f:selectItem itemValue=""
                                                                              itemLabel="#{CElabels['operacoes.todos']}"/>
                                                                <f:selectItems
                                                                        value="#{ConsultaEventosControle.ambientes}"/>
                                                            </h:selectOneMenu>
                                                        </h:panelGrid>

                                                        <h:panelGrid columnClasses="classDireita">
                                                            <div id="divObg-mensagemFiltro" align="center"
                                                                 class="mensagemAzul"></div>
                                                        </h:panelGrid>
                                                        <h:panelGrid columnClasses="classDireita">

                                                        </h:panelGrid>
                                                        <h:panelGrid columnClasses="classDireita">
                                                            <div id="divObg-mensagemAmbiente" align="center"
                                                                 class="mensagemAzul"></div>
                                                        </h:panelGrid>
                                                        <h:panelGrid columnClasses="classDireita">
                                                        </h:panelGrid>
                                                    </h:panelGrid>
                                                    <br/>

                                                </rich:simpleTogglePanel>
                                            </td>
                                            <td valign="top">
                                                <rich:simpleTogglePanel id="legenda" switchType="client"
                                                                        opened="false" width="239px"
                                                                        height="215px">
                                                    <f:facet name="header">
                                                        <h:outputText
                                                                value="#{CElabels['entidade.consultaEventos.legenda']}"/>
                                                    </f:facet>
                                                    <table cellpadding="2" border="0">
                                                        <tr>
                                                            <td class="legenda-simbolo"
                                                                style="background: #E9E9E9;width: 10px;"></td>
                                                            <td class="legenda-descricao"><h:outputText
                                                                    styleClass="tituloLegenda"
                                                                    value="#{CElabels['entidade.consultaEventos.dia.situacao.vazio']}"/></td>
                                                        </tr>
                                                        <tr>
                                                            <td style="background: #FAFAD2"></td>
                                                            <td class="legenda-descricao"><h:outputText
                                                                    styleClass="tituloLegenda"
                                                                    value="#{CElabels['entidade.consultaEventos.dia.situacao.disp']}"/></td>
                                                        </tr>
                                                        <tr>
                                                            <td style="background: #CC9999"></td>
                                                            <td class="legenda-descricao"><h:outputText
                                                                    styleClass="tituloLegenda"
                                                                    value="#{CElabels['entidade.consultaEventos.dia.situacao.cheio']}"/></td>
                                                        </tr>
                                                        <tr>
                                                            <td><h:outputText style="font-weight:bold;"
                                                                              styleClass="tituloLegenda"
                                                                              value="PR"/>
                                                            </td>
                                                            <td class="legenda-descricao"><h:outputText
                                                                    styleClass="tituloLegenda"
                                                                    value="#{CElabels['entidade.consultaEventos.preReserva']}"/></td>
                                                        </tr>
                                                        <tr>
                                                            <td><h:outputText style="font-weight:bold;"
                                                                              styleClass="tituloLegenda"
                                                                              value="AE"/>
                                                            </td>
                                                            <td class="legenda-descricao"><h:outputText
                                                                    styleClass="tituloLegenda"
                                                                    value="#{CElabels['entidade.consultaEventos.autorizadoE']}"/></td>
                                                        </tr>
                                                        <tr>
                                                            <td><h:outputText style="font-weight:bold;"
                                                                              styleClass="tituloLegenda"
                                                                              value="E"/>
                                                            </td>
                                                            <td class="legenda-descricao"><h:outputText
                                                                    styleClass="tituloLegenda"
                                                                    value="#{CElabels['entidade.consultaEventos.encerrado']}"/></td>
                                                        </tr>
                                                        <tr>
                                                            <td><h:outputText style="font-weight:bold;"
                                                                              styleClass="tituloLegenda"
                                                                              value="AA"/>
                                                            </td>
                                                            <td class="legenda-descricao"><h:outputText
                                                                    styleClass="tituloLegenda"
                                                                    value="#{CElabels['entidade.consultaEventos.aguardando']}"/></td>
                                                        </tr>
                                                        <tr>
                                                            <td><h:outputText style="font-weight:bold;"
                                                                              styleClass="tituloLegenda"
                                                                              value="V"/>
                                                            </td>
                                                            <td class="legenda-descricao"><h:outputText
                                                                    styleClass="tituloLegenda"
                                                                    value="#{CElabels['entidade.consultaEventos.visita']}"/></td>
                                                        </tr>
                                                    </table>

                                                </rich:simpleTogglePanel>
                                                <br/>
                                            </td>
                                        </tr>
                                    </table>
                                    <table width="100%" border="0"  align="center" cellpadding="0"
                                           cellspacing="0" style="float: left;padding-bottom: 10px;">
                                        <tr>
                                            <td width="10"><img
                                                    src="${contexto}/imagens/agenda_imgs/menu_esq.jpg"
                                                    width="10" height="69"></td>
                                            <td background="${contexto}/imagens/agenda_imgs/menu_fundo.jpg">
                                                <table width="100%" border="0" cellspacing="0" cellpadding="0">
                                                    <tr>
                                                        <td align="left">
                                                            <a style="align:top;"
                                                               onmouseover="if (document.getElementById('hint-buscar').style.visibility != 'hidden') {toolTip('
                                                                   <h:outputText
                                                                           value="#{CElabels['entidade.dispAmbiente.hint.buscar']}"/>' , 300 , 'blue')}"
                                                               onmouseout="hideToolTip();">
                                                                <img src="${contexto}/pages/ce/img/hint.gif"
                                                                     width="15px" id="hint-buscar"
                                                                     style="visibility: hidden;" height="15px"
                                                                     border="0px;"/>
                                                            </a>
                                                            <a4j:commandLink
                                                                    action="#{ConsultaEventosControle.consultarPorFiltroSelecionado}"
                                                                    reRender="resultado"
                                                                    title="#{CElabels['operacoes.consulta.consultarDados']}"
                                                                    onclick="if(!validarFiltros()){return false;};"
                                                                    oncomplete=" setaMensagemConsulta();">
                                                                <img src="${contexto}/imagens/botoesCE/buscar.png"
                                                                     border="0" title="Buscar">
                                                            </a4j:commandLink>
                                                        </td>

                                                        <td align="right">
                                                            <!-- Semana Atual -->
                                                            <a onmouseover="if (document.getElementById('hint-semanaAtual').style.visibility != 'hidden') {toolTip('
                                                                <h:outputText
                                                                        value="#{CElabels['entidade.dispAmbiente.hint.semanaAtual']}"/>' , 300 , 'blue')}"
                                                               onmouseout="hideToolTip();">
                                                                <img src="${contexto}/pages/ce/img/hint.gif"
                                                                     width="15px" id="hint-semanaAtual"
                                                                     style="visibility: hidden;" height="15px"
                                                                     border="0px;"/>
                                                            </a>
                                                            <a4j:commandLink
                                                                    action="#{ConsultaEventosControle.consultarDispAmbSemanaAtual}"
                                                                    reRender="resultado, dataFim, dataInicio"
                                                                    oncomplete=" setaFiltroIntervalo(); setaMensagemConsulta();">
                                                                <img src="${contexto}/imagens/agenda_imgs/bt_consultarsemana.jpg"
                                                                     width="169" height="30" border="0"
                                                                     title="Consultar Semana Atual">
                                                            </a4j:commandLink>
                                                        </td>

                                                        <td align="right">
                                                            <!-- Botão Próxima semana -->
                                                            <a onmouseover="if (document.getElementById('hint-semana').style.visibility != 'hidden') {toolTip('
                                                                <h:outputText
                                                                        value="#{CElabels['entidade.dispAmbiente.hint.semana']}"/>' , 300 , 'blue')}"
                                                            >
                                                                <img src="${contexto}/pages/ce/img/hint.gif"
                                                                     width="15px" id="hint-semana"
                                                                     style="visibility: hidden;" height="15px"
                                                                     border="0px;"/>
                                                            </a>
                                                            <a4j:commandLink styleClass="botoes"
                                                                             action="#{ConsultaEventosControle.consultarDispAmbProximaSemana}"
                                                                             oncomplete="setaFiltroIntervalo(); setaMensagemConsulta();"
                                                                             reRender=" dataFim, dataInicio, resultado,">
                                                                <img src="${contexto}/imagens/agenda_imgs/bt_proximasemana.jpg"
                                                                     width="137" height="30" border="0"
                                                                     title="Próxima Semana">
                                                            </a4j:commandLink>
                                                        </td>

                                                        <td align="right">
                                                            <!-- Botão mês -->
                                                            <a onmouseover="if (document.getElementById('hint-mes').style.visibility != 'hidden') {toolTip('
                                                                <h:outputText
                                                                        value="#{CElabels['entidade.dispAmbiente.hint.mes']}"/>' , 300 , 'blue')}"
                                                               onmouseout="hideToolTip();">
                                                                <img src="${contexto}/pages/ce/img/hint.gif"
                                                                     width="15px" id="hint-mes"
                                                                     style="visibility: hidden;" height="15px"
                                                                     border="0px;"/>
                                                            </a>
                                                            <a4j:commandLink styleClass="botoes"
                                                                             action="#{ConsultaEventosControle.consultarDispAmbMes}"
                                                                             reRender="resultado, dataFim, dataInicio"
                                                                             oncomplete=" setaFiltroIntervalo(); setaMensagemConsulta();">
                                                                <img src="${contexto}/imagens/agenda_imgs/bt_consultarmes.jpg"
                                                                     width="123" height="30" border="0"
                                                                     title="Consultar Mês">
                                                            </a4j:commandLink>
                                                        </td>

                                                        <td align="right">
                                                            <!-- Botão Quadra -->
                                                            <a onmouseover="if (document.getElementById('hint-quadra').style.visibility != 'hidden') {toolTip('
                                                                <h:outputText
                                                                        value="#{CElabels['entidade.dispAmbiente.hint.quadra']}"/>' , 300 , 'blue')}"
                                                               onmouseout="hideToolTip();">
                                                                <img src="${contexto}/pages/ce/img/hint.gif"
                                                                     width="15px" id="hint-quadra"
                                                                     style="visibility: hidden;" height="15px"
                                                                     border="0px;"/>
                                                            </a>
                                                            <a4j:commandLink styleClass="botoes"
                                                                             action="#{ConsultaEventosControle.consultarDispAmbTipoQuadra}"
                                                                             reRender="resultado, tiposAmbiente"
                                                                             oncomplete=" setaMensagemConsulta();">
                                                                <img src="${contexto}/imagens/agenda_imgs/bt_consultarquadra.jpg"
                                                                     width="130" height="30" border="0"
                                                                     title="Consultar Quadra">
                                                            </a4j:commandLink>
                                                        </td>

                                                        <td align="right">
                                                            <!-- Botão Salão -->
                                                            <a onmouseover="if (document.getElementById('hint-salao').style.visibility != 'hidden') {toolTip('
                                                                <h:outputText
                                                                        value="#{CElabels['entidade.dispAmbiente.hint.salao']}"/>' , 300 , 'blue')}"
                                                               onmouseout="hideToolTip();">
                                                                <img src="${contexto}/pages/ce/img/hint.gif"
                                                                     width="15px" id="hint-salao"
                                                                     style="visibility: hidden;" height="15px"
                                                                     border="0px;"/>
                                                            </a>
                                                            <a4j:commandLink styleClass="botoes"
                                                                             action="#{ConsultaEventosControle.consultarDispAmbTipoSalao}"
                                                                             reRender="resultado, tiposAmbiente"
                                                                             oncomplete=" setaMensagemConsulta();">
                                                                <img src="${contexto}/imagens/agenda_imgs/bt_consultarsalao.jpg"
                                                                     width="120" height="30" border="0"
                                                                     title="Consultar Salão">
                                                            </a4j:commandLink>
                                                        </td>

                                                    </tr>
                                                </table>
                                            </td>
                                            <td width="10"><img
                                                    src="${contexto}/imagens/agenda_imgs/menu_dir.jpg"
                                                    width="10" height="69"></td>
                                        </tr>
                                    </table>

                                    <br/>

                                    <div id="divObg-mensagemBusca" class="mensagemAzul"></div>
                                    <h:panelGroup id="resultado" style="width: 100% !important;">
                                        <div id="geral" style="width: 100% !important;">

                                            <div id="divObg-filtroData" class="mensagemObrigatorio"></div>
                                            <table cellpadding="0" cellspacing="0" style="position: relative; width: 100%;">
                                                <tr>
                                                    <td valign="top" style="width: 158px;">
                                                        <div style=" overflow-x:hidden; overflow-y:hidden;">
                                                            <table id="dias" class="X3" cellpadding="0"
                                                                   cellspacing="0">
                                                                <tr>
                                                                    <td>
                                                                        <div style=" width: 140px; height: 60px;"></div>
                                                                    </td>
                                                                </tr>
                                                                <c:forEach var="dia" varStatus="indexDia"
                                                                           items="${ConsultaEventosControle.diasConsultados}">
                                                                    <tr>
                                                                        <c:choose>
                                                                            <c:when test="${indexDia.first}">
                                                                                <td style="height: 0px !important; padding: 0 !important;"></td>
                                                                            </c:when>
                                                                            <c:when test="${!indexDia.first}">
                                                                                <td width="100%" height="100%"
                                                                                    background="${contexto}/imagens/agenda_imgs/dia_semana_fundo.jpg"
                                                                                    class="borda_bege">
                                                                                    <div style="width: 140px; height: 60px;">
                                                                                        <strong><span
                                                                                                class="arial_11_preto_sublinhado"><c:out
                                                                                                value="${dia.data}"/></span></strong><br/>
                                                                                        <strong><span
                                                                                                class="arial_15_preto"><c:out
                                                                                                value="${dia.diaSemana}"/></span></strong>
                                                                                    </div>
                                                                                </td>
                                                                            </c:when>
                                                                        </c:choose>
                                                                    </tr>
                                                                </c:forEach>

                                                            </table>
                                                        </div>
                                                    </td>
                                                    <td style="position: absolute; width: calc( 100% - 158px);">

                                                        <div style=" overflow-x:scroll; overflow-y:hidden;  position: relative;">
                                                            <table id="cabecalho1" class="X3" cellpadding="0"
                                                                   cellspacing="0">
                                                                <tr>

                                                                    <c:forEach var="dia" varStatus="indexDia"
                                                                               items="${ConsultaEventosControle.diasConsultados}">
                                                                        <c:forEach var="disponibilidades"
                                                                                   varStatus="indexDisp"
                                                                                   items="${ConsultaEventosControle.resultadoConsulta}">
                                                                            <c:choose>
                                                                                <c:when test="${indexDia.first}">

                                                                                    <td align="center"
                                                                                        valign="middle"
                                                                                        background="${contexto}/imagens/agenda_imgs/salao_fundo.jpg"
                                                                                        class="borda_azul">
                                                                                        <div style=" width: 100px; height: 60px; overflow:hidden;"
                                                                                             class="arial_15_branca">
                                                                                            <c:out value='${disponibilidades["0"]["1"]}'/></div>
                                                                                    </td>
                                                                                </c:when>
                                                                            </c:choose>
                                                                        </c:forEach>
                                                                    </c:forEach>
                                                                </tr>
                                                            </table>


                                                            <!-- <div id="conteudo" style="height: 500px; overflow-y:auto; overflow-x: hidden;"> -->
                                                            <table id="tabelaResultados" class="X3"
                                                                   cellpadding="0" cellspacing="0">

                                                                <c:forEach var="dia" varStatus="indexDia"
                                                                           items="${ConsultaEventosControle.diasConsultados}">
                                                                    <tr>

                                                                        <!-- Obter índice da lista de dias como String para poder ser usada como chave do mapa de disponibilidades -->
                                                                        <c:set var="indDia"
                                                                               value='<%= ((javax.servlet.jsp.jstl.core.LoopTagStatus) pageContext.getAttribute("indexDia")).getIndex() + ""%>'/>

                                                                        <c:forEach var="disponibilidades"
                                                                                   varStatus="indexDisp"
                                                                                   items="${ConsultaEventosControle.resultadoConsulta}">
                                                                        <c:choose>
                                                                        <c:when test="${!indexDia.first}">
                                                                        <c:choose>
                                                                        <c:when test='${disponibilidades[indDia].status eq 1}'>

                                                                            <td bgcolor="#E9E9E9"
                                                                                class="borda_cinza"
                                                                                onclick="document.getElementById('form:detalharEventos').click();"
                                                                                onMouseOver="this.bgColor='#FFFFFF'; document.getElementById('form:indexDia').value=${indexDia.index}; document.getElementById('form:indexAmbiente').value=${indexDisp.index};"
                                                                                onMouseOut="this.bgColor='#E9E9E9'">
                                                                                <div class="dia_vazio_cinza"
                                                                                     style="width: 100px; height: 60px;"></div>


                                                                            </td>

                                                                        </c:when>
                                                                        <c:otherwise>
                                                                        <c:choose>
                                                                        <c:when test='${disponibilidades[indDia].status eq 2}'>
                                                                        <td bgcolor="#FAFAD2"
                                                                            class="borda_cinza"
                                                                            onclick="document.getElementById('form:detalharEventos').click();"
                                                                            onMouseOver="this.bgColor='#FFF68F';document.getElementById('form:indexDia').value=${indexDia.index}; document.getElementById('form:indexAmbiente').value=${indexDisp.index};"
                                                                            onMouseOut="this.bgColor='#FAFAD2'">
                                                                            <div class="ha_disponibilidade_azulescuro"
                                                                                 style="width: 100px; height: 60px;">
                                                                                <strong><c:out
                                                                                        value='${disponibilidades[indDia].evento1}'/></strong><br/>
                                                                                <strong><c:out
                                                                                        value='${disponibilidades[indDia].evento2}'/></strong><br/>
                                                                                <c:out value='${disponibilidades[indDia].legenda}'/>
                                                                            </div>


                                                                            </c:when>
                                                                            <c:when test='${disponibilidades[indDia].status eq 3}'>

                                                                        <td bgcolor="#CC9999"
                                                                            class="borda_cinza"
                                                                            onclick="document.getElementById('form:detalharEventos').click();"
                                                                            onMouseOver="this.bgColor='#F08080'; document.getElementById('form:indexDia').value=${indexDia.index}; document.getElementById('form:indexAmbiente').value=${indexDisp.index};"
                                                                            onMouseOut="this.bgColor='#CC9999'">
                                                                            <div class="ha_disponibilidade_branco"
                                                                                 style="width: 100px; height: 60px;">
                                                                                <strong><c:out
                                                                                        value='${disponibilidades[indDia].evento1}'/></strong><br/>
                                                                                <strong><c:out
                                                                                        value='${disponibilidades[indDia].evento2}'/></strong><br/>
                                                                                <c:out value='${disponibilidades[indDia].legenda}'/>
                                                                            </div>


                                                                            </c:when>
                                                                            </c:choose>

                                                                            </c:otherwise>
                                                                            </c:choose>
                                                                            </c:when>
                                                                            </c:choose>
                                                                            </c:forEach>
                                                                    </tr>
                                                                </c:forEach>
                                                            </table>
                                                            <!-- </div>  -->
                                                            <table id="cabecalho" class="X3" cellpadding="0"
                                                                   cellspacing="0">
                                                                <tr>

                                                                    <c:forEach var="dia" varStatus="indexDia"
                                                                               items="${ConsultaEventosControle.diasConsultados}">
                                                                        <c:forEach var="disponibilidades"
                                                                                   varStatus="indexDisp"
                                                                                   items="${ConsultaEventosControle.resultadoConsulta}">
                                                                            <c:choose>
                                                                                <c:when test="${indexDia.first}">

                                                                                    <td align="center"
                                                                                        valign="middle"
                                                                                        background="${contexto}/imagens/agenda_imgs/salao_fundo.jpg"
                                                                                        class="borda_azul">
                                                                                        <div style=" width: 100px; height: 60px; overflow: hidden;"
                                                                                             class="arial_15_branca">
                                                                                            <c:out value='${disponibilidades["0"]["1"]}'/></div>
                                                                                    </td>
                                                                                </c:when>
                                                                            </c:choose>
                                                                        </c:forEach>
                                                                    </c:forEach>
                                                                </tr>
                                                            </table>

                                                        </div>

                                                    </td>
                                                </tr>

                                            </table>


                                            <h:inputHidden id="indexDia"
                                                           value="#{ConsultaEventosControle.indexDia}"/>
                                            <h:inputHidden id="indexAmbiente"
                                                           value="#{ConsultaEventosControle.indexAmbiente}"/>
                                            <a4j:commandButton id="detalharEventos" style="display: none;"
                                                               reRender="panelEventos"
                                                               action="#{ConsultaEventosControle.detalharEventos}"
                                                               oncomplete="Richfaces.showModalPanel('panelEventos');"/>


                                        </div>
                                    </h:panelGroup>
                                    <br/>
                                    <table width="100%" border="0" align="center" cellpadding="0"
                                           cellspacing="0"
                                           style="margin-top: 100px;">
                                        <tr>
                                            <td width="10"><img
                                                    src="${contexto}/imagens/agenda_imgs/menu_esq.jpg"
                                                    width="10" height="69"></td>
                                            <td background="${contexto}/imagens/agenda_imgs/menu_fundo.jpg">
                                                <table width="100%" border="0" cellspacing="0" cellpadding="0">
                                                    <tr>
                                                        <td align="left">
                                                            <a4j:commandLink
                                                                    action="#{ConsultaEventosControle.consultarPorFiltroSelecionado}"
                                                                    reRender="resultado"
                                                                    title="#{CElabels['operacoes.consulta.consultarDados']}"
                                                                    onclick="if(!validarFiltros()){return false;};">
                                                                <img src="${contexto}/imagens/botoesCE/buscar.png"
                                                                     border="0"
                                                                     title="Buscar"></a4j:commandLink>
                                                        </td>

                                                        <td align="right">
                                                            <!-- Semana Atual -->
                                                            <a4j:commandLink
                                                                    action="#{ConsultaEventosControle.consultarDispAmbSemanaAtual}"
                                                                    reRender="resultado, dataFim, dataInicio">
                                                                <img src="${contexto}/imagens/agenda_imgs/bt_consultarsemana.jpg"
                                                                     width="169" height="30" border="0"
                                                                     title="Consultar Semana Atual"></a4j:commandLink>
                                                        </td>

                                                        <td align="right">
                                                            <!-- Botão Próxima semana -->
                                                            <a4j:commandLink styleClass="botoes"
                                                                             action="#{ConsultaEventosControle.consultarDispAmbProximaSemana}"
                                                                             reRender="resultado, dataFim, dataInicio">
                                                                <img src="${contexto}/imagens/agenda_imgs/bt_proximasemana.jpg"
                                                                     width="137" height="30" border="0"
                                                                     title="Próxima Semana">
                                                            </a4j:commandLink>
                                                        </td>

                                                        <td align="right">
                                                            <!-- Botão mês -->
                                                            <a4j:commandLink styleClass="botoes"
                                                                             action="#{ConsultaEventosControle.consultarDispAmbMes}"
                                                                             reRender="resultado, dataFim, dataInicio">
                                                                <img src="${contexto}/imagens/agenda_imgs/bt_consultarmes.jpg"
                                                                     width="123" height="30" border="0"
                                                                     title="Consultar Mês">
                                                            </a4j:commandLink>
                                                        </td>

                                                        <td align="right">
                                                            <!-- Botão Quadra -->
                                                            <a4j:commandLink styleClass="botoes"
                                                                             action="#{ConsultaEventosControle.consultarDispAmbTipoQuadra}"
                                                                             reRender="resultado, dataFim, dataInicio">
                                                                <img src="${contexto}/imagens/agenda_imgs/bt_consultarquadra.jpg"
                                                                     width="130" height="30" border="0"
                                                                     title="Consultar Quadra">
                                                            </a4j:commandLink>
                                                        </td>

                                                        <td align="right">
                                                            <!-- Botão Salão -->
                                                            <a4j:commandLink styleClass="botoes"
                                                                             action="#{ConsultaEventosControle.consultarDispAmbTipoSalao}"
                                                                             reRender="resultado, dataFim, dataInicio">
                                                                <img src="${contexto}/imagens/agenda_imgs/bt_consultarsalao.jpg"
                                                                     width="120" height="30" border="0"
                                                                     title="Consultar Salão">
                                                            </a4j:commandLink>
                                                        </td>

                                                    </tr>
                                                </table>
                                            </td>
                                            <td width="10"><img
                                                    src="${contexto}/imagens/agenda_imgs/menu_dir.jpg"
                                                    width="10" height="69"></td>
                                        </tr>
                                    </table>

                                    <a4j:commandButton style="visibility: hidden;" reRender="panelExpiracaoSenha"
                                                       id="btnAtualizaPagina">
                                    </a4j:commandButton>
                                </h:panelGroup>
                         </h:panelGroup>



                        </h:panelGroup>
                        <jsp:include page="includes/include_box_menulateral.jsp" flush="true"/>
                    </h:panelGroup>
                </h:panelGroup>
            </h:panelGroup>

            <jsp:include page="../../include_rodape_flat.jsp" flush="true"/>
            <%@include file="includes/include_focus.jsp" %>
        </h:panelGroup>
        </body>
        </html>
    </h:form>

    <%@include file="/include_load_configs.jsp" %>

    <%@include file="/pages/ce/includes/include_modal_disponibilidades.jsp" %>
    <%@include file="/include_modal_expiracaoSenha.jsp" %>
</f:view>


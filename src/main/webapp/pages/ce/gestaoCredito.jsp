<%@page contentType="text/html" pageEncoding="ISO-8859-1" %>
<%@include file="includes/include_imports.jsp" %>

<c:set var="moduloSession" value="1" scope="session" />

<f:view>
    <jsp:include page="/includes/include_carregando_ripple.jsp" flush="true"/>
    <h:form id="form">
        <html>
            <!-- Inclui o elemento HEAD da página -->
            <%@include file="includes/include_head.jsp" %>
            <body>
                <a4j:keepAlive beanName="GestaoCreditoControle" />
                <h:panelGroup layout="block" style="display: inline-table;width: 100%;" styleClass="fundoCinza">
                    <h:panelGroup layout="block" styleClass="bgtop topoZW" rendered="#{MenuControle.apresentarTopo}">
                        <jsp:include page="../../include_topo_2.0.jsp" flush="true"/>
                        <jsp:include page="../../include_menu_ce_flat.jsp" flush="true"/>
                    </h:panelGroup>

                    <h:panelGroup layout="block" styleClass="caixaCorpo">
                        <h:panelGroup layout="block" style="height: 80%;width: 100%">
                            <h:panelGroup layout="block" styleClass="caixaMenuLatel">
                                <h:panelGroup layout="block" styleClass="container-imagem container-conteudo-central">
                                    <h:panelGroup layout="block" styleClass="container-box zw_ui especial ">
                                        <h:panelGroup styleClass="container-box-header" layout="block">
                                            <h:panelGroup layout="block" styleClass="margin-box">
                                                <h:outputText styleClass="container-header-titulo" value="Gestão de Créditos"/>
                                                <h:outputLink styleClass="linkWiki"
                                                              value="#{SuperControle.urlWikiCE}"
                                                              title="Clique e saiba mais: Pesquisa Geral"
                                                              target="_blank">
                                                    <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                                                </h:outputLink>
                                            </h:panelGroup>
                                        </h:panelGroup>
                                        <h:panelGroup layout="block" styleClass="margin-box">
                                            <h:panelGrid id="mensagens" columns="1" width="100%" styleClass="tabMensagens">
                                                <h:panelGrid columns="3" width="100%" styleClass="tabMensagens">
                                                    <h:panelGrid columns="1" width="100%">

                                                        <h:outputText value=" "/>

                                                    </h:panelGrid>
                                                    <h:commandButton rendered="#{GestaoCreditoControle.sucesso}" image="/imagens/bt_sucesso.png"/>
                                                    <h:commandButton rendered="#{GestaoCreditoControle.erro}" image="/imagens/erro.png"/>
                                                    <h:panelGrid columns="1" width="100%">
                                                        <h:outputText styleClass="mensagem" value="#{GestaoCreditoControle.mensagem}"/>
                                                        <h:outputText styleClass="mensagemDetalhada" value="#{GestaoCreditoControle.mensagemDetalhada}"/>
                                                    </h:panelGrid>
                                                </h:panelGrid>
                                            </h:panelGrid>
                                            &nbsp;


                                            <rich:simpleTogglePanel id="Consulta" switchType="client" opened="false" height="220px">
                                                <f:facet name="header">
                                                    <h:outputText value="#{CElabels['entidade.filtroPesquisa']}:" />
                                                </f:facet>
                                                <h:panelGrid columns="2" rowClasses="linhaImpar,linhaPar" columnClasses="classEsquerda, classDireita" width="100%">
                                                    <h:outputText styleClass="tituloCampos" value=""/>
                                                    <h:selectOneRadio  id="tipoPesquisa" layout="lineDirection" styleClass="tituloCampos"
                                                                       value="#{GestaoCreditoControle.tipoConsulta}" >
                                                        <f:selectItem itemValue="1" itemLabel="Todos"/>
                                                        <f:selectItem itemValue="2" itemLabel="Somente não-devolvidos"/>
                                                        <f:selectItem itemValue="3" itemLabel="Somente devolvidos"/>

                                                    </h:selectOneRadio>

                                                    <h:outputText styleClass="tituloCampos" value="#{CElabels['entidade.cliente.nome']}:"/>
                                                    <h:inputText maxlength="100" id="nomeCliente" onblur="blurinput(this);"
                                                                 onfocus="focusinput(this);" styleClass="form" value="#{GestaoCreditoControle.nomeCliente}"/>

                                                    <h:outputText styleClass="tituloCampos" value="#{CElabels['entidade.evento.nome']}:"/>
                                                    <h:inputText maxlength="100" id="nomeEvento" onblur="blurinput(this);"
                                                                 onfocus="focusinput(this);" styleClass="form" value="#{GestaoCreditoControle.nomeEvento}"/>

                                                    <h:outputText styleClass="tituloCampos" value="Código do evento:"/>
                                                    <h:inputText maxlength="50" id="codEvento" size="6" onblur="blurinput(this);"
                                                                 onfocus="focusinput(this);" styleClass="form" value="#{GestaoCreditoControle.codigoEvento}"
                                                                 onkeypress="return mascara(this.form, this.id , '999999999', event);"/>


                                                    <h:outputText styleClass="tituloCampos" value="Data de lançamento:"/>
                                                    <h:panelGroup>
                                                        <h:outputText styleClass="tituloCampos" value="#{CElabels['entidade.data.de']}"/>&nbsp;
                                                        <rich:calendar id="dataInicio" styleClass="form" value="#{GestaoCreditoControle.inicioLancamento}"
                                                                       datePattern="dd/MM/yyyy" inputSize="10" inputClass="form" oninputblur="blurinput(this);" oninputfocus="focusinput(this);"
                                                                       oninputchange="if(!validar_Data(this.id)) {this.value = ''};"
                                                                       oninputkeypress="return mascara(this.form, this.id , '99/99/9999', event);"
                                                                       enableManualInput="true" zindex="2" showWeeksBar="false" />&nbsp;
                                                        <h:outputText styleClass="tituloCampos" value="#{CElabels['entidade.dataAte']}"/>&nbsp;
                                                        <rich:calendar id="dataFim" styleClass="form" value="#{GestaoCreditoControle.fimLancamento}"
                                                                       datePattern="dd/MM/yyyy" inputSize="10" inputClass="form" oninputblur="blurinput(this);" oninputfocus="focusinput(this);"
                                                                       oninputchange="if(!validar_Data(this.id)) {this.value = ''};"
                                                                       oninputkeypress="return mascara(this.form, this.id , '99/99/9999', event);"
                                                                       enableManualInput="true" zindex="2" showWeeksBar="false" />
                                                    </h:panelGroup>

                                                    <h:outputText styleClass="tituloCampos" value="Data do evento:"/>
                                                    <h:panelGroup>
                                                        <h:outputText styleClass="tituloCampos" value="#{CElabels['entidade.data.de']}"/>&nbsp;
                                                        <rich:calendar id="dataInicioEvento" styleClass="form" value="#{GestaoCreditoControle.inicioEvento}"
                                                                       datePattern="dd/MM/yyyy" inputSize="10" inputClass="form" oninputblur="blurinput(this);" oninputfocus="focusinput(this);"
                                                                       oninputchange="if(!validar_Data(this.id)) {this.value = ''};" oninputkeypress="return mascara(this.form, this.id , '99/99/9999', event);"
                                                                       enableManualInput="true" zindex="2" showWeeksBar="false" />&nbsp;
                                                        <h:outputText styleClass="tituloCampos" value="#{CElabels['entidade.dataAte']}"/>&nbsp;
                                                        <rich:calendar id="dataFimEvento" styleClass="form" value="#{GestaoCreditoControle.fimEvento}"
                                                                       datePattern="dd/MM/yyyy" inputSize="10" inputClass="form" oninputblur="blurinput(this);" oninputfocus="focusinput(this);"
                                                                       oninputchange="if(!validar_Data(this.id)) {this.value = ''};" oninputkeypress="return mascara(this.form, this.id , '99/99/9999', event);"
                                                                       enableManualInput="true" zindex="2" showWeeksBar="false" />
                                                    </h:panelGroup>

                                                </h:panelGrid>
                                            </rich:simpleTogglePanel>
                                            <br />
                                            <center>
                                                <a4j:commandLink id="consultarEspecifico" reRender="creditos, Consulta, mensagens"
                                                                 title="#{CElabels['operacoes.consulta.consultar']}"
                                                                 action="#{GestaoCreditoControle.consultarCreditos}">
                                                    <img src="${contexto}/imagens/botoesCE/buscar.png" border="0" title="Buscar">
                                                </a4j:commandLink>
                                            </center>
                                            <br />

                                            <rich:dataTable value="#{GestaoCreditoControle.creditos}" var="credito" id="creditos" width="100%"
                                                            columnClasses="centralizado" headerClass="consulta"
                                                            rowClasses="linhaImpar, linhaPar" rows="20">

                                                <rich:column sortable="true" sortBy="#{credito.codigoEvento}" width="10%">
                                                    <f:facet name="header">
                                                        <h:outputText value="Cód. Evento"/>
                                                    </f:facet>
                                                    <h:outputText value="#{credito.codigoEvento}"/>
                                                </rich:column>

                                                <rich:column sortable="true" sortBy="#{credito.nomeEvento}" width="20%">
                                                    <f:facet name="header">
                                                        <h:outputText value="#{CElabels['entidade.evento']}"/>
                                                    </f:facet>
                                                    <h:outputText value="#{credito.nomeEvento}"/>
                                                </rich:column>

                                                <rich:column sortable="true" sortBy="#{credito.nomeCliente}" width="20%">
                                                    <f:facet name="header">
                                                        <h:outputText value="#{CElabels['entidade.cliente']}"/>
                                                    </f:facet>
                                                    <h:outputText value="#{credito.nomeCliente}"/>
                                                </rich:column>

                                                <rich:column sortable="true" sortBy="#{credito.valor}" width="10%">
                                                    <f:facet name="header">
                                                        <h:outputText value="Crédito original"/>
                                                    </f:facet>
                                                    <h:outputText value="#{credito.valorApresentar}"/>
                                                </rich:column>

                                                <rich:column sortable="true" sortBy="#{credito.saldoAtual}" width="10%">
                                                    <f:facet name="header">
                                                        <h:outputText value="Crédito atual"/>
                                                    </f:facet>
                                                    <h:outputText style="font-color: #{credito.saldoAtual < 0.0 ? 'red' : 'green'}" value="#{credito.saldoAtualApresentar}"/>
                                                </rich:column>

                                                <rich:column sortable="true" sortBy="#{credito.dataEvento}" width="10%">
                                                    <f:facet name="header">
                                                        <h:outputText value="Dt. Evento"/>
                                                    </f:facet>
                                                    <h:outputText value="#{credito.dataEventoApresentar}"/>
                                                </rich:column>

                                                <rich:column sortable="true" sortBy="#{credito.dataLancamento}" width="10%">
                                                    <f:facet name="header">
                                                        <h:outputText value="Dt. Lançamento"/>
                                                    </f:facet>
                                                    <h:outputText value="#{credito.dataLancamentoApresentar}"/>
                                                </rich:column>

                                                <rich:column sortable="true" sortBy="#{credito.dataLancamento}" width="10%">
                                                    <f:facet name="header">
                                                        <h:outputText value="Opções"/>
                                                    </f:facet>


                                                    <a4j:commandLink onclick="preencherHiddenChamarBotao('form:botaoEdicao','form:codigoPerfilEdicao','#{credito.codigoEvento}');return false;"
                                                                     onmouseover="toolTip('Detalhamento do Evento' , 120 , 'gray')"
                                                                     onmouseout="hideToolTip();">
                                                        <h:graphicImage value="/imagens/bt_detalhes.png" style="border: 0px;" title="#{CElabels['operacoes.editar.editarDados']}"/>
                                                    </a4j:commandLink>&nbsp;
                                                    <a4j:commandLink action="#{GestaoCreditoControle.devolver}"
                                                                     oncomplete="Richfaces.showModalPanel('panelUsuarioSenha')"
                                                                     reRender="formUsuarioSenha"
                                                                     rendered="#{credito.saldoAtual > 0.0}">
                                                        <h:graphicImage value="/imagens/devolucao.png" width="25px" height="25px" style="border: 0px;" title="Devolver crédito"/>
                                                    </a4j:commandLink>

                                                </rich:column>

                                            </rich:dataTable>
                                            <rich:datascroller for="creditos"/>
                                        </h:panelGroup>
                                    </h:panelGroup>
                                </h:panelGroup>
                                <%@include file="includes/include_box_menulateral.jsp" %>
                            </h:panelGroup>
                        </h:panelGroup>
                    </h:panelGroup>

                    <jsp:include page="../../include_rodape_flat.jsp" flush="true" />
                    <rich:jQuery id="mskData" selector=".rich-calendar-input" timing="onload" query="mask('99/99/9999')" />
                </h:panelGroup>
                <%@include file="includes/include_focus.jsp" %>
            </body>

        </html>

        <h:commandButton id="botaoEdicao" action="#{CadastroInicialControle.abrirDetalhamento}" style="visibility: hidden;"/>
	<h:inputHidden id="codigoPerfilEdicao" value="#{CadastroInicialControle.codigoEventoInteresse}" />

    </h:form>

    <rich:modalPanel id="panelUsuarioSenha" autosized="true" shadowOpacity="true" width="450" height="200"
                     onshow="document.getElementById('formUsuarioSenha:senha').focus()">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Devolução de crédito"/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:graphicImage value="/imagens/close.png" style="cursor:pointer" id="hidelinkUsuarioSenha"/>
                <rich:componentControl for="panelUsuarioSenha" attachTo="hidelinkUsuarioSenha" operation="hide"  event="onclick">

                </rich:componentControl>
            </h:panelGroup>
        </f:facet>
        <h:form id="formUsuarioSenha">
            <input type="hidden" value="centralEventos" name="modulo"/>

            <h:panelGrid columns="1"  width="100%" columnClasses="colunaCentralizada">
                
                <h:panelGrid id="panelConfimacao" columns="2" width="100%" columnClasses="w30 colunaEsquerda" styleClass="tabForm">
                        <h:outputText styleClass="text"  value="Data da devolução:" />
                        <rich:calendar id="dataInicio" styleClass="form" value="#{GestaoCreditoControle.dataDevolucao}"
                                       datePattern="dd/MM/yyyy" inputSize="10" inputClass="form" oninputblur="blurinput(this);" oninputfocus="focusinput(this);"
                                       oninputchange="if(!validar_Data(this.id)) {this.value = ''};"
                                       oninputkeypress="return mascara(this.form, this.id , '99/99/9999', event);"
                                       enableManualInput="true" zindex="2" showWeeksBar="false" />
                        <h:inputText  style="opacity:0;height: 0px;" size="5" maxlength="7" />
                        <h:inputSecret  size="14" maxlength="64" style="opacity:0;height: 0px;"/>

                        <h:outputText styleClass="text"  value="Código:" />
                        <h:inputText id="codigoUsuario" size="3" maxlength="100"
                                     value="#{GestaoCreditoControle.usuarioDevolucao.codigo}">
                            <a4j:support event="onchange" focus="formUsuarioSenha:senha" 
                                         action="#{GestaoCreditoControle.consultarResponsavel}" reRender="panelConfimacao, mensagem"/>
                        </h:inputText>
                        <h:panelGroup>
                            <h:outputText styleClass="text"  value="Usuário:" />
                            <h:inputText id="autoCompleteHidden" size="3" maxlength="10" style="margin-left:6px;opacity:0;" value="#{GestaoCreditoControle.usuarioDevolucao.username}"/>
                            
                        </h:panelGroup>
                        
                        <h:outputText styleClass="text" 
                                      value="#{GestaoCreditoControle.usuarioDevolucao.username}" />
                        <h:outputText styleClass="text"  value="Senha:"/>
                        <h:inputSecret autocomplete="off" id="senha" size="14" maxlength="64" 
                                       value="#{GestaoCreditoControle.usuarioDevolucao.senha}"/>
                        <rich:hotKey selector="#senha" key="return"
                                     handler="#{rich:element('loginCaixa')}.onclick();return false;"/>
                </h:panelGrid>
                <h:outputText id="mensagem" styleClass="mensagemDetalhada"
                              value="#{GestaoCreditoControle.mensagemDetalhada}"/>
                <a4j:commandButton id="loginCaixa" value="#{msg_bt.btn_confirmar}"
                                 image="/imagens/botoesCE/confirmar.png" alt="#{msg.msg_gravar_dados}"
                                 reRender="form"
                                 oncomplete="Richfaces.hideModalPanel('panelUsuarioSenha')"
                                 action="#{GestaoCreditoControle.confirmarDevolucao}">
                </a4j:commandButton>
            </h:panelGrid>
        </h:form>
    </rich:modalPanel>
</f:view>


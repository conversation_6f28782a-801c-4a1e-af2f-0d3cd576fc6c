<%@include file="../includes/include_imports.jsp" %>

<f:view>
    <jsp:include page="/includes/include_carregando_ripple.jsp" flush="true"/>
    <h:form id="form">
        <html>
        <!-- Inclui o elemento HEAD da p�gina -->
        <head>
            <%@include file="../includes/include_head_ce.jsp" %>
        </head>
        <script>
            function validarCadastroParcial() {
                limparMensagem('nomeCliente');
                limparMensagem('nomeEvento');
                limparMensagem('telefoneCelular');
                limparMensagem('telefoneComercial');
                limparMensagem('telefoneFixo');
                limparMensagem('dataInicio');

                limparMensagem('ambienteInteresse');
                limparMensagem('formaContato');

                var dataInicio = document.getElementById('form:dataInicioInputDate');
                var nomeCliente = document.getElementById('form:nomeCliente');
                var nomeClienteOut = document.getElementById('form:nomeClienteOut');
                var validade = true;

                if (dataInicio == null || dataInicio.value == null || dataInicio.value == "") {
                    exibirMensagem('<h:outputText value="#{Mensagens[\'campoObrigatorio.dataInicio\']}"/>', 'dataInicio');
                    validade = false;
                }

                if (nomeCliente == null || nomeCliente.value == null || nomeCliente.value == "") {
                    if (nomeClienteOut == null || nomeClienteOut.value == null || nomeClienteOut.value == "") {
                        exibirMensagem('<h:outputText value="#{Mensagens[\'campoObrigatorio.nomeCliente\']}"/>', 'nomeCliente');
                        validade = false;
                    }
                }

                var formacontato = document.getElementById('form:formaContato');
                if (formacontato == 0 || formacontato.value == 0) {
                    exibirMensagem('<h:outputText value="#{Mensagens[\'campoObrigatorio.formasContato\']}"/>', 'formaContato');
                    validade = false;
                }
                return validade;
            }

            function validar() {

                limparMensagem('nomeCliente');
                limparMensagem('nomeEvento');
                limparMensagem('telefoneCelular');
                limparMensagem('telefoneComercial');
                limparMensagem('telefoneFixo');
                limparMensagem('dataInicio');

                limparMensagem('ambienteInteresse');
                limparMensagem('formaContato');

                var dataInicio = document.getElementById('form:dataInicioInputDate');
                var nomeCliente = document.getElementById('form:nomeCliente');
                var nomeClienteOut = document.getElementById('form:nomeClienteOut');
                var nomeEvento = document.getElementById('form:nomeEvento');
                var telefoneCelular = document.getElementById('form:telefoneCelular');
                var telefoneComercial = document.getElementById('form:telefoneComercial');
                var telefoneFixo = document.getElementById('form:telefoneFixo');


                var ambiente = document.getElementById('form:ambienteInteresse');
                var formacontato = document.getElementById('form:formaContato');


                var validade = true;

                if (dataInicio == null || dataInicio.value == null || dataInicio.value == "") {
                    exibirMensagem('<h:outputText value="#{Mensagens[\'campoObrigatorio.dataInicio\']}"/>', 'dataInicio');
                    validade = false;
                }
                if (nomeCliente == null || nomeCliente.value == null || nomeCliente.value == "") {
                    if (nomeClienteOut == null || nomeClienteOut.value == null || nomeClienteOut.value == "") {
                        exibirMensagem('<h:outputText value="#{Mensagens[\'campoObrigatorio.nomeCliente\']}"/>', 'nomeCliente');
                        validade = false;
                    }
                }
                if (nomeEvento == null || nomeEvento.value == null || nomeEvento.value == "") {
                    exibirMensagem('<h:outputText value="#{Mensagens[\'campoObrigatorio.nomeEvento\']}"/>', 'nomeEvento');
                    validade = false;
                }


                if (telefoneCelular == null || telefoneCelular.value == null || telefoneCelular.value == "") {
                    if (telefoneFixo == null || telefoneFixo.value == null || telefoneFixo.value == "") {
                        if (telefoneComercial == null || telefoneComercial.value == null || telefoneComercial.value == "") {
                            exibirMensagem('<h:outputText value="#{Mensagens[\'campoObrigatorio.telefone\']}"/>', 'telefoneFixo');
                            exibirMensagem('<h:outputText value="#{Mensagens[\'campoObrigatorio.telefone\']}"/>', 'telefoneCelular');
                            exibirMensagem('<h:outputText value="#{Mensagens[\'campoObrigatorio.telefone\']}"/>', 'telefoneComercial');
                            validade = false;
                        }
                    }
                }



                return validade;
            }
            function validarDisponibilidade(){
                limparMensagem('dataInicio');
                limparMensagem('ambienteInteresse');

                var dataInicio = document.getElementById('form:dataInicioInputDate');
                var ambiente = document.getElementById('form:ambienteInteresse');
                var validade = true;

                if (dataInicio == null || dataInicio.value == null || dataInicio.value == "") {
                    exibirMensagem('<h:outputText value="#{Mensagens[\'campoObrigatorio.dataInicio\']}"/>', 'dataInicio');
                    validade = false;
                }
                if (ambiente == 0 || ambiente.value == 0) {
                    exibirMensagem('<h:outputText value="#{Mensagens[\'campoObrigatorio.ambiente\']}"/>', 'ambienteInteresse');
                    validade = false;
                }

                if (ambiente == 0 || ambiente.value == 0) {
                    exibirMensagem('<h:outputText value="#{Mensagens[\'campoObrigatorio.ambiente\']}"/>', 'ambienteInteresse');
                    validade = false;
                }

                return validade;
            }

            function limparMsgObrigTextoPadrao() {
                var textoPadrao = document.getElementById('form:textoPadraoTextArea_ifr').contentWindow.document.body.firstChild.innerHTML;
                if (textoPadrao != null && textoPadrao != '<br mce_bogus="1">') {
                    limparMensagem('textoPadrao');
                }
            }

            function ativarAjuda(id) {
                if (typeof id == "string") {
                    var hint = document.getElementById('hint-' + id);
                    hint.style.visibility = hint.style.visibility == 'hidden' ? 'visible' : 'hidden';
                } else {
                    for (var i = 0; i < id.length; i++) {
                        var hint = document.getElementById('hint-' + id[i]);
                        hint.style.visibility = hint.style.visibility == 'hidden' ? 'visible' : 'hidden';
                    }
                }
            }

            function zerarConvidados(){
                var nrConvidados = document.getElementsByName('form:nrconvidados')[0].value;
                if(nrConvidados == null || nrConvidados == ""){
                    document.getElementsByName('form:nrconvidados')[0].value = 0;
                }
            }

        </script>
        <body class="ce">


        <h:panelGroup layout="block" style="display: inline-table;width: 100%;" styleClass="fundoCinza">

            <h:panelGroup layout="block" styleClass="bgtop topoZW" rendered="#{MenuControle.apresentarTopo}">
                <jsp:include page="../../../include_topo_2.0.jsp" flush="true"/>
                <jsp:include page="../../../include_menu_ce_flat.jsp" flush="true"/>
                <rich:jQuery selector=".item2" query="addClass('menuItemAtual')"/>
            </h:panelGroup>
            <a4j:keepAlive beanName="CadastroInicialControle" />
            <h:panelGroup layout="block" styleClass="caixaCorpo">
                <h:panelGroup layout="block" style="height: 80%;width: 100%">
                    <h:panelGroup layout="block" styleClass="caixaMenuLatel">
                        <h:panelGroup layout="block" styleClass="container-imagem container-conteudo-central">
                            <h:panelGroup layout="block" styleClass="container-box zw_ui especial ">
                                <h:panelGroup styleClass="container-box-header" layout="block">
                                    <h:panelGroup layout="block" styleClass="margin-box">
                                        <h:outputText styleClass="container-header-titulo" value="#{CElabels['menu.operacoesCE.cadastroInicial']}"/>
                                        <h:outputLink styleClass="linkWiki"
                                                      value="#{SuperControle.urlWikiCE}Cadastros:Cadastro_Inicial"
                                                      title="Clique e saiba mais: Cadastro Inicial"
                                                      target="_blank">
                                            <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                                        </h:outputLink>
                                    </h:panelGroup>
                                </h:panelGroup>
                                <h:panelGroup layout="block" styleClass="margin-box">
                                    <%@include file="../includes/include_cadastroInicial.jsp" %>
                                </h:panelGroup>
                                </h:panelGroup>
                        </h:panelGroup>
                        <jsp:include page="../includes/include_box_menulateral.jsp" flush="true"/>
                    </h:panelGroup>
                </h:panelGroup>
            </h:panelGroup>

            <jsp:include page="../../../include_rodape_flat.jsp" flush="true" />
            <%@include file="../includes/include_focus.jsp" %>
        </h:panelGroup>
        </body>
        </html>
    </h:form>

    <%@include file="/pages/ce/includes/include_modal_verificarDisponibilidades.jsp" %>
    <%@include file="/pages/ce/includes/include_modal_existe_cliente.jsp" %>
</f:view>

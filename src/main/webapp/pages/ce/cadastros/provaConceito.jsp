<%@include file="../includes/include_imports.jsp" %>

<f:view>
    <jsp:include page="/includes/include_carregando_ripple.jsp" flush="true"/>
    <html>
       	<!-- INCLUI UM ELEMENTO HEAD -->
        <%@include file="../includes/include_head_form.jsp" %>

        <title><h:outputText value="#{CElabels['entidade.provaConceito']}"/></title>

        <body>

            <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
                <f:facet name="header">
                    <!-- INCLUS�O DO TOPO DA P�GINA -->
                    <jsp:include page="../includes/topoReduzido.jsp"/>
                </f:facet>
            </h:panelGrid>

            <h:form id="form">

                <!-- MANTEM O CONTROLLER NA �RVORE DE COMPONENTES -->
                <a4j:keepAlive beanName="ProvaConceitoControle" />

                <h:panelGrid columns="1" width="100%" >
                    <h:panelGrid columns="1" style="height:25px; background-image:url('../../../imagens/fundoBarraTopo.png');background-repeat: repeat-x;" columnClasses="colunaCentralizada" width="100%">
                        <h:outputText styleClass="tituloFormulario" value="#{CElabels['entidade.provaConceito']}"/>
                    </h:panelGrid>
                </h:panelGrid>

                <h:panelGrid columns="2" width="100%">
                    <h:column>
                        <!-- CAMPO FILTRO PARA CONSULTA -->
                        <h:outputText styleClass="tituloCampos" value="Consulta por #{CElabels['entidade.nome']}:"/>
                        <h:inputText id="consultaNome" onblur="blurinput(this);"  onfocus="focusinput(this);"
                                     styleClass="form" value="#{ProvaConceitoControle.provaConceito.nome}"/>&nbsp;

                        <!-- BOT�O QUE INVOCA A A��O DE CONSULTAR -->
                        <h:commandButton id="consultar" type="submit" styleClass="botoes"
                                         value="#{CElabels['operacoes.consulta.consultar']}" action="#{ProvaConceitoControle.listar}"
                                         image="/imagens/botaoConsultar.png" alt="#{CElabels['operacoes.consulta.consultar']}">
                            <a4j:support event="onclick" reRender="itens"></a4j:support></h:commandButton>
                    </h:column>
                </h:panelGrid>

                <!-- TABELA CONTENDO O RESULTADO DA CONSULTA -->
                <rich:dataTable id="itens" width="100%" headerClass="consulta" rowClasses="linhaImpar, linhaPar"
                                columnClasses="colunaAlinhamento" value="#{ProvaConceitoControle.listaProvaConceito}"
                                rendered="#{ProvaConceitoControle.apresentarResultadoConsulta}" rows="10" var="provaConceito">
                    <h:column>
                        <f:facet name="header">
                            <h:outputText value="#{CElabels['entidade.codigo']}"/>
                        </f:facet>
                        <h:outputText value="#{provaConceito.codigo}"/>
                    </h:column>
                    <h:column>
                        <f:facet name="header">
                            <h:outputText value="#{CElabels['entidade.nome']}"/>
                        </f:facet>
                        <h:outputText value="#{provaConceito.nome}"/>
                    </h:column>
                    <h:column>
                        <f:facet name="header">
                            <h:outputText value="#{CElabels['entidade.descricao']}"/>
                        </f:facet>
                        <h:outputText value="#{provaConceito.descricao}"/>
                    </h:column>
                    <h:column>
                        <f:facet name="header">
                            <h:outputText value="#{CElabels['entidade.valor']}"/>
                        </f:facet>
                        <h:outputText value="#{provaConceito.valor}"/>
                    </h:column>

                    <!-- OPERA��ES POSS�VEIS DE SEREM REALIZADAS PARA CADA ITEM DO RESULTADO DA CONSULTA -->
                    <h:column>
                        <f:facet name="header">
                            <h:outputText value="#{CElabels['operacoes.opcoes']}"/>
                        </f:facet>
                        <h:commandLink action="#{ProvaConceitoControle.seleciona}">
                            <h:graphicImage value="/imagens/bt_editar.png" style="border: 0px;" alt="#{CElabels['operacoes.editar.editarDados']}"/>
                        </h:commandLink>

                        <h:commandLink action="#{ProvaConceitoControle.deleteProva}">
                            <h:graphicImage value="/imagens/botaoRemover.png" style="border: 0px;" alt="#{CElabels['operacoes.editar.editarDados']}"/>
                        </h:commandLink>

                    </h:column>

                    <!-- RODAP� -->
                    <f:facet name="footer">
                        <rich:datascroller id="ds" renderIfSinglePage="false" />
                    </f:facet>
                </rich:dataTable>

                <!-- �REA DE EXIBI��O DE MENSAGENS -->
                <h:panelGrid id="mensagens" columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid columns="3" width="100%" styleClass="tabMensagens">
                        <h:panelGrid columns="1" width="100%">

                            <h:outputText value=" "/>

                        </h:panelGrid>

                        <h:panelGrid columns="1" width="100%">
                            <h:outputText styleClass="mensagem" value="#{ProvaConceitoControle.mensagem}" />
                            <h:outputText styleClass="mensagemDetalhada" value="#{ProvaConceitoControle.mensagemDetalhada}" />
                        </h:panelGrid>
                    </h:panelGrid>

                    <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                        <h:commandLink action="#{ProvaConceitoControle.novo}">
                            <h:graphicImage value="/imagens/botaoNovo.png" style="border: 0px;" alt="#{CElabels['operacoes.editar.editarDados']}"/>
                        </h:commandLink>
                    </h:panelGrid>
                </h:panelGrid>
            </h:form>
        </body>
    </html>
</f:view>
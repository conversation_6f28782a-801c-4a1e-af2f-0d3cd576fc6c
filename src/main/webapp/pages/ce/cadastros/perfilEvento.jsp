<%--
  Created by IntelliJ IDEA.
  User: Rafael
  Date: 20/08/2015
  Time: 10:54
  To change this template use File | Settings | File Templates.
--%>
<%@include file="/includes/verificaModulo.jsp" %>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>


<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<c:set var="contexto" value="${pageContext.request.contextPath}" scope="request" />

<script>
	jQuery.noConflict();
</script>
<f:view>
	<jsp:include page="/includes/include_carregando_ripple.jsp" flush="true"/>
	<html>
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<link href="${contexto}/css/otimize.css" rel="stylesheet" type="text/css">
	<%@include file="../includes/include_head_form.jsp" %>
	<script type="text/javascript" language="javascript" src="${contexto}/hoverform.js"></script>
	<link href="${contexto}/beta/css/pure-min.css" type="text/css" rel="stylesheet"/>
	<link href="${contexto}/beta/css/bootstrap-tabs.css" type="text/css" rel="stylesheet"/>
	<link href="${contexto}/beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
	<link href="${contexto}/beta/css/pure-ext.css" type="text/css" rel="stylesheet"/>
	<script src="${contexto}/beta/js/jquery.js" type="text/javascript"></script>
	<script src="${contexto}/beta/js/jquery.dataTables.min.js" type="text/javascript"></script>
	<script src="${contexto}/beta/js/DT_bootstrap.js" type="text/javascript"></script>
	<script src="${contexto}/beta/js/bootstrap-tab.js" type="text/javascript"></script>
	<style type="text/css">
		.obrigatorio {
			color: #DDDD00;
		}

		.mensagemObrigatorio {
			color: #FF0000;
		}
	</style>
	<script>
		jQuery.noConflict();
	</script>
	<body>
	<c:set var="titulo" scope="session" value="Perfil Evento"/>
	<c:set var="urlWiki" scope="session" value="${SuperControle.urlWiki}Cadastros:Cadastros:PerfilEvento"/>
		<%-- INICIO HEADER --%>
	<f:facet name="header">
		<jsp:include page="../../../topoReduzido_material.jsp"/>
	</f:facet>
		<%-- FIM HEADER --%>

	<h:panelGroup layout="block" styleClass="pure-g-r">
		<h:form id="form" styleClass="pure-form pure-u-1">
			<a4j:keepAlive beanName="ExportadorListaControle"/>
			<input type="hidden" value="${modulo}" name="modulo"/>
			<input id="chavePrimaria" type="hidden" value="" name="chavePrimaria"/>

			<%-- INICIO CONTENT --%>
			<h:panelGroup>

				<%-- INICIO COMANDOS --%>
				<h:panelGroup layout="block" styleClass="pure-g-r pure-u-12-12 margin-0-auto titulo-topo">
					<h:panelGroup layout="block" styleClass="pure-form pure-form-aligned pure-u-2-3"></h:panelGroup>
					<h:panelGroup layout="block" styleClass="pure-u-1-3 text-right">
						<h:panelGroup layout="block" styleClass="controles">
							<a4j:commandLink id="btnExcel"
											 styleClass="exportadores"
											 actionListener="#{PerfilEventoControle.exportar}"
											 oncomplete="abrirPopup('../../../UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/vnd.ms-excel','Fornecedores', 800,200);#{ExportadorListaControle.msgAlert}"
											 accesskey="3">
								<f:attribute name="tipo" value="xls"/>
								<f:attribute name="atributos"
											 value="codigo=Código,nome_Apresentar=Fornecedor,contato=Contato"/>
								<f:attribute name="prefixo" value="Fornecedor"/>
								<a4j:actionparam noEscape="true" name="paramsTableFiltrada" value="retornaDadosDT()"/>
								<h:outputText title="Exportar para o formato Excel" styleClass="btn-print-2 excel"/>
							</a4j:commandLink>

							<a4j:commandLink id="btnPDF"
											 styleClass="exportadores margin-h-10"
											 actionListener="#{PerfilEventoControle.exportar}"
											 oncomplete="abrirPopup('../../../UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/pdf','Fornecedores', 800,200);#{ExportadorListaControle.msgAlert}"
											 accesskey="4">
								<f:attribute name="tipo" value="pdf"/>
								<f:attribute name="atributos"
											 value="codigo=Código,nome_Apresentar=Fornecedor,contato=Contato"/>
								<f:attribute name="prefixo" value="Fornecedor"/>
								<f:attribute name="titulo" value="Fornecedores"/>
								<a4j:actionparam noEscape="true" name="paramsTableFiltrada" value="retornaDadosDT()"/>
								<h:outputText title="Exportar para o formato PDF" styleClass="btn-print-2 pdf"/>
							</a4j:commandLink>

							<a4j:commandLink id="btnNovo"
											 styleClass="pure-button pure-button-primary"
											 action="#{PerfilEventoControle.novo}"
											 accesskey="1">
								&nbsp ${msg_bt.btn_cadastrar_novo}
							</a4j:commandLink>
						</h:panelGroup>
					</h:panelGroup>
				</h:panelGroup>
				<%-- FIM COMANDOS --%>

				<table id="tblPerfilEvento" class="tblPerfilEvento pure-g-r pure-u-11-12 margin-0-auto">
					<thead>
						<th>${CElabels['entidade.codigo']}</th>
						<th>${CElabels['entidade.nome']}</th>
						<th>${CElabels['entidade.dataInicio']}</th>
						<th>${CElabels['entidade.dataTermino']}</th>
					</thead>
					<tbody></tbody>
				</table>
				<tbody></tbody>
				</table>

				<a4j:jsFunction name="jsEditar" action="#{PerfilEventoControle.editar}" reRender="mensagem"/>

			</h:panelGroup>
			<%-- FIM CONTENT --%>

			<%-- INICIO FOOTER --%>
			<h:panelGroup id="mensagem" layout="block" styleClass="pure-u-11-12 margin-0-auto">
				<h:graphicImage id="iconSucesso" rendered="#{PerfilEventoControle.sucesso}" value="/imagens/sucesso.png"/>
				<h:graphicImage id="iconErro" rendered="#{PerfilEventoControle.erro}" value="/imagens/erro.png"/>

				<h:outputText styleClass="mensagem" rendered="#{not empty PerfilEventoControle.mensagem}"
							  value=" #{PerfilEventoControle.mensagem}"/>
				<h:outputText styleClass="mensagemDetalhada"
							  rendered="#{not empty PerfilEventoControle.mensagemDetalhada}"
							  value=" #{PerfilEventoControle.mensagemDetalhada}"/>
			</h:panelGroup>
			<%-- FIM FOOTER --%>
		</h:form>

		<%@include file="/pages/ce/includes/include_modal_exibeLogEntidade.jsp" %>
	</h:panelGroup>

	<script src="${contexto}/beta/js/ext-funcs.js" type="text/javascript"></script>


	<script>
		jQuery(window).on("load", function () {
			iniciarTabela("tblPerfilEvento", "${contexto}/prest/ce/perfilevento", 1, "asc", "", true);
		});
	</script>
	</body>
	</html>
</f:view>



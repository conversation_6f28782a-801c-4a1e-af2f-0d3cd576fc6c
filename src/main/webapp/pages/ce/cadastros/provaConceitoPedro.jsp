<%@include file="../includes/include_imports.jsp" %>

<f:view>
    <jsp:include page="/includes/include_carregando_ripple.jsp" flush="true"/>
    <html>
       	<!-- INCLUI UM ELEMENTO HEAD -->
        <%@include file="../includes/include_head_form.jsp" %>

        <title><h:outputText value="#{CElabels['entidade.provaConceito']}"/></title>

        <body>

            <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
                <f:facet name="header">
                    <!-- INCLUS�O DO TOPO DA P�GINA -->

                    <jsp:include page="../includes/topoReduzido.jsp"/>

                </f:facet>
            </h:panelGrid>

            <h:form id="form">

                <a4j:keepAlive beanName="ProvaConceitoPedroControle" />

                <h:panelGrid columns="2" width="100%">
                    <h:column>
                        <h:outputText styleClass="tituloCampos" value="Consulta por #{CElabels['entidade.nome']}:"/>
                        <h:inputText id="consultaNome" onblur="blurinput(this);"  onfocus="focusinput(this);"
                                     styleClass="form" value="#{ProvaConceitoPedroControle.provaConceitoPedroVO.nome}"/>&nbsp;

                        <h:commandButton id="consultar" type="submit" styleClass="botoes"
                                         value="#{CElabels['operacoes.consulta.consultar']}" action="#{ProvaConceitoPedroControle.listar}"
                                         image="/imagens/botaoConsultar.png" alt="#{CElabels['operacoes.consulta.consultar']}">
                            <a4j:support event="onclick" reRender="itens"></a4j:support></h:commandButton>

                        <h:commandLink action="#{ProvaConceitoPedroControle.novo}">
                            <h:graphicImage value="/imagens/botaoNovo.png" style="border: 0px;" alt="#{CElabels['operacoes.editar.editarDados']}"/>
                        </h:commandLink>
                    </h:column>
                </h:panelGrid>



                <rich:dataTable id="itens" width="100%" headerClass="consulta" rowClasses="linhaImpar, linhaPar"
                                columnClasses="colunaAlinhamento" value="#{ProvaConceitoPedroControle.listaProvaConceitoPedroVO}"
                                rendered="#{ProvaConceitoPedroControle.apresentarResultadoConsulta}" rows="10" var="provaConceitoPedro">
                    <h:column>
                        <f:facet name="header">
                            <h:outputText value="#{CElabels['entidade.codigo']}"/>
                        </f:facet>
                        <h:outputText value="#{provaConceitoPedro.codigo}"/>
                    </h:column>
                    <h:column>
                        <f:facet name="header">
                            <h:outputText value="#{CElabels['entidade.nome']}"/>
                        </f:facet>
                        <h:outputText value="#{provaConceitoPedro.nome}"/>
                    </h:column>
                    <h:column>
                        <f:facet name="header">
                            <h:outputText value="#{CElabels['entidade.descricao']}"/>
                        </f:facet>
                        <h:outputText value="#{provaConceitoPedro.descricao}"/>
                    </h:column>
                    <h:column>
                        <f:facet name="header">
                            <h:outputText value="#{CElabels['entidade.valor']}"/>
                        </f:facet>
                        <h:outputText value="#{provaConceitoPedro.valor}"/>
                    </h:column>

                    <h:column>
                        <f:facet name="header">
                            <h:outputText value="#{CElabels['operacoes.opcoes']}"/>
                        </f:facet>
                        <h:commandLink action="#{ProvaConceitoPedroControle.seleciona}">
                            <h:graphicImage value="/imagens/bt_editar.png" style="border: 0px;" alt="#{CElabels['operacoes.editar.editarDados']}"/>
                        </h:commandLink>

                        <h:commandLink action="#{ProvaConceitoPedroControle.deleteProva}">
                            <h:graphicImage value="/imagens/botaoRemover.png" style="border: 0px;" alt="#{CElabels['operacoes.editar.editarDados']}"/>
                        </h:commandLink>

                    </h:column>

                    <f:facet name="footer">
                        <rich:datascroller id="ds" renderIfSinglePage="false" />
                    </f:facet>
                </rich:dataTable>
            </h:form>
        </body>
    </html>
</f:view>
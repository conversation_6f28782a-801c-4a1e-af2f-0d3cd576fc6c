<%@page contentType="text/html" pageEncoding="ISO-8859-1" %>
<%@include file="../includes/include_imports.jsp"%>
<%@include file="/includes/verificaModulo.jsp" %>
    <script type="text/javascript" language="javascript" src="hoverform.js"></script>
    <script type="text/javascript" language="javascript" src="script/time_1.3.js"></script>
    <script src="${root}/script/packJQueryPlugins.min.js" type="text/javascript"></script>
    <script type="text/javascript" src="${root}/script/tooltipster/jquery.tooltipster.min.js"></script></head>
<link href="${contexto}/beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
    <script type="text/javascript" language="javascript">
            function validar(){

            if (document.getElementById('formConsultarCEP:estadoCEP').value == ""
                && document.getElementById('formConsultarCEP:cidadeCEP').value == ""
                && document.getElementById('formConsultarCEP:bairroCEP').value == ""
                && document.getElementById('formConsultarCEP:logradouroCEP').value == ""){

                alert("Ao menos um parâmetro deve ser informado!");
                return false;
            }


            return true;
        }
    </script>
<f:view>
    <jsp:include page="/includes/include_carregando_ripple.jsp" flush="true"/>
    <a4j:loadScript src="/script/jquery.maskedinput-1.2.2.js" />
    <html>
        <!-- INCLUI O ELEMENTO HEAD NA PÁGINA -->
        <%@include file="../includes/include_head_form.jsp"%>
        <script type="text/javascript">
            function hideOrShow(show) {
					 
                var obj = document.getElementById("form:panelPatrimonio"); // Get the panel using its ID
                var obj1 = document.getElementById("form:panelRastreamento");
                if (show) {
                    obj.style.display = "block";
                    obj1.style.display = "block";
                }
                else {
                    obj.style.display = "none";
                    obj1.style.display = "none";
                }
            }
        </script>
        <title><h:outputText
                value="#{CElabels['menu.cadastros.produtos.fornecedor']}" /></title>

        <style>
            .obrigatorio {
                color: #DDDD00;
            }

            .mensagemObrigatorio {
                color: #FF0000;
            }
        </style>
        <script>
            //Script de validação do formulário
            function exibirMensagem(mensagem, id) {
                var divObgMensagem = document.getElementById('divObg-' + id);
                if (divObgMensagem) {
                    divObgMensagem.innerHTML = mensagem;
                }
            }
            function limparMensagem(id) {
                var divObgMensagem = document.getElementById('divObg-' + id);
                divObgMensagem.innerHTML = "";
            }
            function validar() {
                limparMensagem('descricao');
				
                //Obtem os campos que serão validados
                var descricao = document.getElementById('form:descricao');
                var contato = document.getElementById('form:contato');
                var panelCep = document.getElementById('panelCEPContainer');
			
			
                //Verifica se estão preenchidos
                var validade = true;
                if (descricao == null || descricao.value == null || descricao.value == "") {
                    exibirMensagem('<h:outputText value="O campo Descrição deve ser preenchido"/>', 'descricao');
                    validade = false;
                }
				
				
                if (contato == null || contato.value == null || contato.value == "") {
                    exibirMensagem('<h:outputText value="O campo Contato deve ser preenchido"/>', 'contato');
                    validade = false;
                }

                if (panelCep) {
                    validade = true;
                }

                return validade;
            }
        </script>
        <body>
    <c:set var="titulo" scope="session" value="Fornecedor"/>
    <c:set var="urlWiki" scope="session" value="${SuperControle.urlBaseConhecimento}como-cadastrar-um-fornecedor/"/>
    <%-- INICIO HEADER --%>
    <f:facet name="header">
        <jsp:include page="../../../topoReduzido_material.jsp"/>
    </f:facet>

            <!-- Painel para consulta do CEP -->
            <rich:modalPanel id="panelCEP" autosized="true" shadowOpacity="true" width="550" height="250">
                <f:facet name="header">
                    <h:panelGroup>
                        <h:outputText value="#{msg_aplic.prt_CEP_tituloConsulta}"></h:outputText>
                    </h:panelGroup>
                </f:facet>
                <f:facet name="controls">
                    <h:panelGroup>
                        <h:graphicImage value="/imagens/close.png" style="cursor:pointer" id="hidelinkCEP"/>
                        <rich:componentControl for="panelCEP" attachTo="hidelinkCEP" operation="hide" event="onclick"/>
                    </h:panelGroup>
                </f:facet>
                <a4j:form id="formConsultarCEP" ajaxSubmit="true">
                    <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                        <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar" columnClasses="classEsquerda" width="100%">
                                        <h:outputText styleClass="tituloCampos" value="*#{msg_aplic.prt_CEP_estado}"/>
                                        <h:selectOneMenu id="estadoCEP" styleClass="campos" value="#{FornecedorControle.cepControle.cepVO.ufSigla}" >
                                            <f:selectItems value="#{FornecedorControle.listaSelectItemRgUfPessoa}"/>
                                        </h:selectOneMenu>
                                        <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_CEP_cidade}"/>
                                        <h:inputText id="cidadeCEP" size="20" styleClass="campos" value="#{FornecedorControle.cepControle.cepVO.cidadeDescricao}"/>
                                        <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_CEP_bairro}"/>
                                        <h:inputText id="bairroCEP" size="20" styleClass="campos" value="#{FornecedorControle.cepControle.cepVO.bairroDescricao}"/>
                                        <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_CEP_logradouro}"/>
                                        <h:inputText id="logradouroCEP" size="20" styleClass="campos" value="#{FornecedorControle.cepControle.cepVO.enderecoLogradouro}"/>

                         </h:panelGrid>
                         <h:panelGrid columns="1">
                       
                            <h:panelGroup>
                                    <h:outputText styleClass="textsmall" value="Informe o nome ou parte do seu logradouro, rua ou avenida. Não Inclua o tipo da via nem o número da sua casa." />
                                </h:panelGroup>
                        </h:panelGrid>
                            <h:panelGroup>
                                <c:choose>
                                    <c:when test="${modulo eq 'financeiroWeb'}">
                                        <a4j:commandButton id="btnConsultarCEP" onclick="if(!validar()){return false;};"  reRender="formConsultarCEP:mensagemConsultaCEP, formConsultarCEP:resultadoConsultaCEP, formConsultarCEP:scResultadoCEP"
                                                           action="#{FornecedorControle.cepControle.consultarCEPDetalhe}" styleClass="botoes" value="#{msg_bt.btn_consultar}"
                                                           image="./imagens/botaoConsultar.png" alt="#{msg.msg_consultar_dados}"/>
                                    </c:when>
                                    <c:when test="${modulo eq 'centralEventos'}">
                                        <a4j:commandButton id="btnConsultarCEP" onclick="if(!validar()){return false;};"  reRender="formConsultarCEP:mensagemConsultaCEP, formConsultarCEP:resultadoConsultaCEP, formConsultarCEP:scResultadoCEP"
                                                           action="#{FornecedorControle.cepControle.consultarCEPDetalhe}" styleClass="botoes" value="#{msg_bt.btn_consultar}"
                                                           image="../../../imagens/botoesCE/buscar.png" alt="#{msg.msg_consultar_dados}"/>
                                    </c:when>
                                    <c:otherwise>
                                        <a4j:commandButton id="btnConsultarCEP" onclick="if(!validar()){return false;};"  reRender="formConsultarCEP:mensagemConsultaCEP, formConsultarCEP:resultadoConsultaCEP, formConsultarCEP:scResultadoCEP"
                                                           action="#{FornecedorControle.cepControle.consultarCEPDetalhe}" styleClass="botoes" value="#{msg_bt.btn_consultar}"
                                                           image="./imagens/botaoConsultar.png" alt="#{msg.msg_consultar_dados}"/>
                                    </c:otherwise>
                                </c:choose>
                            </h:panelGroup>
                            <h:panelGroup layout="block" style="overflow:scroll; height:300px" >
                        <rich:dataTable id="resultadoConsultaCEP" width="100%" headerClass="consulta" rowClasses="linhaImpar, linhaPar"
                                        columnClasses="colunaAlinhamento" value="#{FornecedorControle.cepControle.listaConsultaCep}" rows="8" var="cep">
                            <rich:column>
                                <f:facet name="header">
                                    <h:outputText value="#{msg_aplic.prt_CEP_titulo}"/>
                                </f:facet>
                                <h:panelGroup>
                                    <a4j:commandLink action="#{FornecedorControle.selecionarCep}" focus="CEP"
                                                     reRender="form:panelEnderecoCliente" oncomplete="Richfaces.hideModalPanel('panelCEP')"
                                                     value="#{cep.enderecoCep}"/>
                                </h:panelGroup>
                            </rich:column>
                             <rich:column>
                                <f:facet name="header">
                                    <h:outputText value="#{msg_aplic.prt_CEP_cidadeC}"/>
                                </f:facet>
                                <h:panelGroup>
                                    <a4j:commandLink action="#{FornecedorControle.selecionarCep}" focus="CEP"
                                                     reRender="form:panelEnderecoCliente" oncomplete="Richfaces.hideModalPanel('panelCEP')"
                                                     value="#{cep.cidadeDescricao}"/>
                                </h:panelGroup>
                            </rich:column>
                            <rich:column>
                                <f:facet name="header">
                                    <h:outputText value="#{msg_aplic.prt_CEP_bairroC}"/>
                                </f:facet>
                                <a4j:commandLink action="#{FornecedorControle.selecionarCep}" focus="CEP"
                                                 reRender="form:panelEnderecoCliente" oncomplete="Richfaces.hideModalPanel('panelCEP')"
                                                 value="#{cep.bairroDescricao}"/>
                            </rich:column>
                            <rich:column>
                                <f:facet name="header">
                                    <h:outputText value="#{msg_aplic.prt_CEP_logradouroC}"/>
                                </f:facet>
                                <a4j:commandLink action="#{FornecedorControle.selecionarCep}" focus="CEP"
                                                 reRender="form:panelEnderecoCliente" oncomplete="Richfaces.hideModalPanel('panelCEP')"
                                                 value="#{cep.enderecoLogradouro}"/>
                            </rich:column>
                        </rich:dataTable>
                        <rich:datascroller align="center" for="formConsultarCEP:resultadoConsultaCEP" maxPages="10"
                                           id="scResultadoCEP" />
                        </h:panelGroup>
                        <h:panelGrid id="mensagemConsultaCEP" columns="1" width="100%" styleClass="tabMensagens">
                            <h:panelGrid columns="1" width="100%">
                                <h:outputText styleClass="mensagem"  value="#{FornecedorControle.cepControle.mensagem}"/>
                                <h:outputText styleClass="mensagemDetalhada" value="#{FornecedorControle.cepControle.mensagemDetalhada}"/>
                            </h:panelGrid>
                        </h:panelGrid>
                    </h:panelGrid>
                </a4j:form>
            </rich:modalPanel>
            <!-- FIM DO PAINEL PARA CONSULTA DO CEP -->

            <!-- FORMULÁRIO DE CADASTRO -->
            <h:form id="form">
                <hr style="border-color: #e6e6e6;"/>
 				<input type="hidden" value="${modulo}" name="modulo"/>
                <!-- MANTEM O CONTROLLER NA ÁRVORE DE COMPONENTES -->
                <a4j:keepAlive beanName="FornecedorControle" />
                <a4j:keepAlive beanName="PlanoContasControle" />
                <a4j:keepAlive beanName="CentroCustosControle" />
                <!-- CAMPOS PARA PREENCHIMENTO -->
                <h:panelGrid rowClasses="linhaImpar, linhaPar"
                             columnClasses="classEsquerda, classDireita"
                             style="background-repeat: no-repeat;padding: 8px;"
                             width="100%" columns="2">
                    <h:panelGroup rendered="#{FornecedorControle.usuarioLogado.administrador}">
                        <c:choose>
                            <c:when test="${modulo eq 'centralEventos'}">
                                <%@include file="../includes/include_obrigatorio.jsp"%>
                            </c:when>
                            <c:when test="${modulo eq 'financeiroWeb'}">
                                <h:outputText styleClass="tituloCampos" rendered="#{FornecedorControle.usuarioLogado.administrador}" value="#{msg_aplic.prt_Finan_Conta_empresa}" />
                            </c:when>
                            <c:otherwise>
                                <h:outputText styleClass="tituloCampos" rendered="#{FornecedorControle.usuarioLogado.administrador}" value="#{msg_aplic.prt_Finan_Conta_empresa}" />
                            </c:otherwise>

                        </c:choose>
                    </h:panelGroup>
                    <!-- Empresa -->
                    <h:panelGroup rendered="#{FornecedorControle.usuarioLogado.administrador}">
                        <h:selectOneMenu onblur="blurinput(this);" rendered="#{FornecedorControle.usuarioLogado.administrador}"
                                         onfocus="focusinput(this);" styleClass="form" id="empresaCombo"
                                         value="#{FornecedorControle.fornecedorVO.empresaVO.codigo}">
                            <f:selectItems value="#{FornecedorControle.empresaCombo}" />
                        </h:selectOneMenu>
                    </h:panelGroup>


                    <h:panelGroup>
                        <h:outputText styleClass="tituloCampos"
                                      value="#{CElabels['entidade.codigo']}:" />
                    </h:panelGroup>
                    <h:panelGroup>
                        <h:inputText
                                id="codigo"
                                disabled="true"
                                value="#{FornecedorControle.fornecedorVO.codigo}" ></h:inputText>
                    </h:panelGroup>

                    <h:panelGroup>
                        <c:choose>
                            <c:when test="${modulo eq 'centralEventos'}">
                                <%@include file="../includes/include_obrigatorio.jsp"%>
                            </c:when>
                            <c:when test="${modulo eq 'financeiroWeb'}">
                                <h:outputText styleClass="tituloCampos" value="*" />
                            </c:when>
                            <c:otherwise>
                                <h:outputText styleClass="tituloCampos" value="*" />
                            </c:otherwise>
                        </c:choose>
                    <h:outputText styleClass="tituloCampos"
                                  value="#{CElabels['entidade.nome']}:" />
                    </h:panelGroup>
                    <h:panelGroup>
                        <h:inputText
                            onblur="blurinput(this); limparMsgObrig('form:descricao', 'descricao');"
                            onfocus="focusinput(this);" styleClass="form" size="20"
                            maxlength="100"
                            value="#{FornecedorControle.fornecedorVO.descricao}" id="descricao"></h:inputText>
                        <div id="divObg-descricao" class="mensagemObrigatorio"></div>
                    </h:panelGroup>

                    <h:panelGroup rendered="#{FornecedorControle.empresaLogado.habilitarCadastroEmpresaSesi}">
                        <h:outputText styleClass="tituloCampos"
                                      value="Razão Social:" />
                    </h:panelGroup>
                    <h:panelGroup rendered="#{FornecedorControle.empresaLogado.habilitarCadastroEmpresaSesi}">
                        <h:inputText
                                styleClass="form" size="20"
                                maxlength="100"
                                value="#{FornecedorControle.fornecedorVO.razaoSocial}" id="razaoSocial"/>
                    </h:panelGroup>

                    <h:panelGroup rendered="#{FornecedorControle.empresaLogado.habilitarCadastroEmpresaSesi}">
                        <h:outputText styleClass="tituloCampos"
                                      value="*Nome Fantasia:" />
                    </h:panelGroup>
                    <h:panelGroup rendered="#{FornecedorControle.empresaLogado.habilitarCadastroEmpresaSesi}">
                        <h:inputText
                                styleClass="form" size="20"
                                maxlength="100"
                                value="#{FornecedorControle.fornecedorVO.nomeFantasia}" id="nomeFantasia"/>
                    </h:panelGroup>

                    <h:panelGroup rendered="#{FornecedorControle.empresaLogado.habilitarCadastroEmpresaSesi}">
                        <h:outputText styleClass="tituloCampos"
                                      value="*Data Cadastro:" />
                    </h:panelGroup>
                    <h:panelGroup rendered="#{FornecedorControle.empresaLogado.habilitarCadastroEmpresaSesi}">
                        <rich:calendar id="dataCadastro"
                                       value="#{FornecedorControle.fornecedorVO.dataCadastro}"
                                       inputSize="10"
                                       inputClass="form"
                                       oninputblur="blurinput(this);"
                                       oninputfocus="focusinput(this);"
                                       oninputchange="return validar_Data(this.id);"
                                       datePattern="dd/MM/yyyy"
                                       enableManualInput="true"
                                       zindex="2"
                                       showWeeksBar="false"/>
                        <h:message for="dataCadastro"  styleClass="mensagemDetalhada"/>
                    </h:panelGroup>

                    <h:panelGroup rendered="#{FornecedorControle.empresaLogado.habilitarCadastroEmpresaSesi}">
                        <h:outputText styleClass="tituloCampos"
                                      value="*Data Validade:" />
                    </h:panelGroup>
                    <h:panelGroup rendered="#{FornecedorControle.empresaLogado.habilitarCadastroEmpresaSesi}">
                        <rich:calendar id="dataValidade"
                                       value="#{FornecedorControle.fornecedorVO.dataValidade}"
                                       inputSize="10"
                                       inputClass="form"
                                       oninputblur="blurinput(this);"
                                       oninputfocus="focusinput(this);"
                                       oninputchange="return validar_Data(this.id);"
                                       datePattern="dd/MM/yyyy"
                                       enableManualInput="true"
                                       zindex="2"
                                       showWeeksBar="false"/>
                        <h:message for="dataValidade"  styleClass="mensagemDetalhada"/>
                    </h:panelGroup>

                    <h:panelGroup rendered="#{FornecedorControle.empresaLogado.habilitarCadastroEmpresaSesi}">
                        <h:outputText styleClass="tituloCampos"
                                      value="*CNAE:" />
                    </h:panelGroup>
                    <h:panelGroup rendered="#{FornecedorControle.empresaLogado.habilitarCadastroEmpresaSesi}">
                        <h:inputText
                                styleClass="form" size="20"
                                maxlength="18"
                                value="#{FornecedorControle.fornecedorVO.cnae}" id="cnae"/>
                    </h:panelGroup>

                    <h:panelGroup rendered="#{FornecedorControle.empresaLogado.habilitarCadastroEmpresaSesi}">
                        <h:outputText styleClass="tituloCampos"
                                      value="*Descrição CNAE:" />
                    </h:panelGroup>
                    <h:panelGroup rendered="#{FornecedorControle.empresaLogado.habilitarCadastroEmpresaSesi}">
                        <h:inputText
                                styleClass="form" size="20"
                                maxlength="100"
                                value="#{FornecedorControle.fornecedorVO.descricaoCnae}" id="descCnae"/>
                    </h:panelGroup>

                    <h:panelGroup rendered="#{FornecedorControle.empresaLogado.habilitarCadastroEmpresaSesi}">
                        <h:outputText styleClass="tituloCampos"
                                      value="Porte Empresa:" />
                    </h:panelGroup>
                    <h:panelGroup rendered="#{FornecedorControle.empresaLogado.habilitarCadastroEmpresaSesi}">
                        <h:selectOneMenu id="porteEmpresa" onblur="blurinput(this);" onfocus="focusinput(this);"
                                         styleClass="form"
                                         value="#{FornecedorControle.fornecedorVO.porteEmpresa}">
                            <f:selectItems value="#{FornecedorControle.montarListaSelectItemPorteEmpresa}"/>
                        </h:selectOneMenu>
                    </h:panelGroup>

                    <h:panelGroup rendered="#{FornecedorControle.empresaLogado.habilitarCadastroEmpresaSesi}">
                        <h:outputText styleClass="tituloCampos"
                                      value="Número Total Funcionarios:" />
                    </h:panelGroup>
                    <h:panelGroup rendered="#{FornecedorControle.empresaLogado.habilitarCadastroEmpresaSesi}">
                        <rich:inputNumberSpinner id="nrTotalFuncionarios" onblur="blurinput(this);"
                                                 onfocus="focusinput(this);" styleClass="form tooltipster"
                                                 value="#{FornecedorControle.fornecedorVO.nrTotalFuncionarios}" maxValue="99999"
                                                 minValue="0"/>
                    </h:panelGroup>

                    <h:panelGroup rendered="#{FornecedorControle.empresaLogado.habilitarCadastroEmpresaSesi}">
                        <h:outputText styleClass="tituloCampos"
                                      value="Grau Risco (NR4):" />
                    </h:panelGroup>
                    <h:panelGroup rendered="#{FornecedorControle.empresaLogado.habilitarCadastroEmpresaSesi}">
                        <rich:inputNumberSpinner id="grauRiscoNr4" onblur="blurinput(this);"
                                                 onfocus="focusinput(this);" styleClass="form tooltipster"
                                                 value="#{FornecedorControle.fornecedorVO.grauRiscoNr4}" maxValue="999"
                                                 minValue="0"/>
                    </h:panelGroup>

                    <h:panelGroup rendered="#{FornecedorControle.empresaLogado.habilitarCadastroEmpresaSesi}">
                        <h:outputText styleClass="tituloCampos"
                                      value="Grau Risco (INSS):" />
                    </h:panelGroup>
                    <h:panelGroup rendered="#{FornecedorControle.empresaLogado.habilitarCadastroEmpresaSesi}">
                        <rich:inputNumberSpinner id="grauRiscoInss" onblur="blurinput(this);"
                                                 onfocus="focusinput(this);" styleClass="form tooltipster"
                                                 value="#{FornecedorControle.fornecedorVO.grauRiscoInss}" maxValue="999"
                                                 minValue="0"/>
                    </h:panelGroup>

                    <h:panelGroup rendered="#{FornecedorControle.empresaLogado.habilitarCadastroEmpresaSesi}">
                        <h:outputText styleClass="tituloCampos"
                                      value="Código FPAS:" />
                    </h:panelGroup>
                    <h:panelGroup rendered="#{FornecedorControle.empresaLogado.habilitarCadastroEmpresaSesi}">
                        <h:inputText
                                styleClass="form" size="20"
                                maxlength="18"
                                value="#{FornecedorControle.fornecedorVO.codigoFpas}" id="codigoFpas"/>
                    </h:panelGroup>

                    <h:panelGroup rendered="#{FornecedorControle.empresaLogado.habilitarCadastroEmpresaSesi}">
                        <h:outputText styleClass="tituloCampos"
                                      value="Sindicato:" />
                    </h:panelGroup>
                    <h:panelGroup rendered="#{FornecedorControle.empresaLogado.habilitarCadastroEmpresaSesi}">
                        <h:inputText
                                styleClass="form" size="20"
                                maxlength="18"
                                value="#{FornecedorControle.fornecedorVO.sindicato}" id="sindicato"/>
                    </h:panelGroup>

                    <h:panelGroup rendered="#{FornecedorControle.empresaLogado.habilitarCadastroEmpresaSesi}">
                        <h:outputText styleClass="tituloCampos tooltipster" title="O tamanho do arquivo deve ser menor ou igual a 512KB"
                                      id="anexarDocumento"
                                      value="Anexar Documento: "></h:outputText>
                    </h:panelGroup>
                    <h:panelGroup rendered="#{FornecedorControle.empresaLogado.habilitarCadastroEmpresaSesi}">
                        <rich:fileUpload id="uploadComprovante"
                                         listHeight="50"
                                         listWidth="350"
                                         noDuplicate="false"
                                         fileUploadListener="#{FornecedorControle.uploadDocumento}"
                                         maxFilesQuantity="1"
                                         allowFlash="false"
                                         immediateUpload="false"
                                         acceptedTypes="jpg, jpeg, gif, png, bmp, pdf, JPG, JPEG, GIF, PNG, BMP, PDF"
                                         addControlLabel="Adicionar"
                                         cancelEntryControlLabel="Cancelar"
                                         doneLabel="Pronto"
                                         sizeErrorLabel="Arquivo não Enviado. Excedeu o tamanho permitido. 512KB"
                                         progressLabel="Enviando"
                                         clearControlLabel="Limpar"
                                         clearAllControlLabel="Limpar todos"
                                         stopControlLabel="Parar"
                                         uploadControlLabel="Enviar"
                                         transferErrorLabel="Falha de Transmissão"
                                         stopEntryControlLabel="Parar">
                            <a4j:support event="onadd" reRender="panelMensagem"/>
                            <a4j:support event="onerror" oncomplete="#{FornecedorControle.mensagemNotificar}" reRender="uploadComprovante, panelMensagem"/>
                            <a4j:support event="onupload" reRender="panelMensagem"/>
                            <a4j:support event="onuploadcomplete" reRender="panelMensagem"/>
                            <a4j:support event="onclear" action="#{FornecedorControle.limparArquivo}" oncomplete="#{FornecedorControle.mensagemNotificar}" reRender="uploadComprovante, panelMensagem"/>
                        </rich:fileUpload>
                    </h:panelGroup>
                    <h:panelGroup rendered="#{FornecedorControle.existeDocumento}">
                        <h:outputText value="Anexo Documento: "
                                      id="anexarContaFinanText"
                                      title="O tamanho do arquivo deve ser menor ou igual a 512KB"
                                      rendered="#{FornecedorControle.existeDocumento}"
                                      styleClass="tooltipster tituloCampos"/>
                    </h:panelGroup>

                    <h:panelGroup rendered="#{FornecedorControle.existeDocumento}" id="existeArquivoContaFinan">
                        <h:commandLink id="arqMovConta" style="margin-left: 12px"
                                       actionListener="#{FornecedorControle.downloadDocumentoListener}"
                                       value="DOWNLOAD"/>
                        <h:commandLink id="excluirArqConta" style="margin-left: 12px"
                                       actionListener="#{FornecedorControle.excluirArqDocumentoListener}"
                                       value="EXCLUIR"/>
                    </h:panelGroup>

                    <h:panelGroup>
                        <h:outputText styleClass="tituloCampos" rendered="#{FornecedorControle.empresaLogado.habilitarCadastroEmpresaSesi}"
                                      value="#{FornecedorControle.cnpjObrigatorioCadastro() ? '*' : ''}#{FornecedorControle.displayIdentificadorFront[2]}:" />
                        <h:outputText styleClass="tituloCampos" rendered="#{!FornecedorControle.empresaLogado.habilitarCadastroEmpresaSesi}"
                                      value="#{FornecedorControle.cnpjObrigatorioCadastro() ? '*' : ''}#{FornecedorControle.displayIdentificadorFront[0]}/#{FornecedorControle.displayIdentificadorFront[2]}:" />
                    </h:panelGroup>
                    <h:panelGroup>
                            <h:inputText
                                    styleClass="form" size="20"
                                    maxlength="18"
                                    value="#{FornecedorControle.fornecedorVO.cnpj}" id="cnpj"/>
                    </h:panelGroup>

                    <h:panelGroup>
                        <h:outputText styleClass="tituloCampos"
                                      value="#{CElabels['entidade.inscricaoEstadual']}:"/>
                    </h:panelGroup>
                    <h:panelGroup>
                        <h:inputText id="inscricaoEstadual"
                                     size="20"
                                     maxlength="20"
                                     value="#{FornecedorControle.fornecedorVO.inscricaoEstadual}"
                                ></h:inputText>
                    </h:panelGroup>

                    <h:panelGroup>
                        <h:outputText styleClass="tituloCampos"
                                      value="Inscrição Municipal:"/>
                    </h:panelGroup>
                    <h:panelGroup>
                        <h:inputText id="inscricaoMunicipal"
                                     size="20"
                                     maxlength="20"
                                     value="#{FornecedorControle.fornecedorVO.inscricaoMunicipal}"
                        ></h:inputText>
                    </h:panelGroup>

                    <h:panelGroup>
                        <h:outputText styleClass="tituloCampos"
                                      value="#{CElabels['entidade.cfdf']}:"/>
                    </h:panelGroup>
                    <h:panelGroup>
                        <h:inputText id="cfdf"
                                     size="20"
                                     maxlength="20"
                                     value="#{FornecedorControle.fornecedorVO.cfdf}"
                                ></h:inputText>
                    </h:panelGroup>

                    <h:panelGroup>
                        <h:outputText styleClass="tituloCampos"
                                      value="*Contato:" />
                    </h:panelGroup>
                    <h:panelGroup>
                        <h:inputText
                            onblur="blurinput(this); limparMsgObrig('form:contato', 'contato');"
                            onfocus="focusinput(this);" styleClass="form" size="20"
                            maxlength="20"
                            value="#{FornecedorControle.fornecedorVO.contato}" id="contato">
                        </h:inputText>
                        <div id="divObg-contato" class="mensagemObrigatorio"></div>
                    </h:panelGroup>


                    <h:panelGroup>
                        <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Pessoa_pais}"/>
                    </h:panelGroup>
                    <h:panelGroup>
                        <h:selectOneMenu id="pais" onblur="blurinput(this);" onfocus="focusinput(this);"
                                         styleClass="form"
                                         value="#{FornecedorControle.fornecedorVO.pessoa.pais.codigo}">
                            <a4j:support event="onchange" reRender="estado,cidade"
                                         action="#{FornecedorControle.montarListaSelectItemEstado}"/>
                            <f:selectItems value="#{FornecedorControle.listaSelectItemPais}"/>
                        </h:selectOneMenu>
                        <a4j:commandButton id="atualizar_pais" action="#{FornecedorControle.montarListaSelectItemPais}"
                                           image="/imagens/atualizar.png" immediate="true" ajaxSingle="true"
                                           reRender="form:pais"/>
                        <h:message for="pais" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>

                    <h:panelGroup>
                        <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Pessoa_estado}"/>
                    </h:panelGroup>
                    <h:panelGroup>
                        <h:selectOneMenu id="estado" onblur="blurinput(this);" onfocus="focusinput(this);"
                                         styleClass="form"
                                         value="#{FornecedorControle.fornecedorVO.pessoa.estadoVO.codigo}">
                            <f:selectItems value="#{FornecedorControle.listaSelectItemEstado}"/>
                            <a4j:support event="onchange" reRender="cidade"
                                         action="#{FornecedorControle.montarListaSelectItemCidade}"/>
                        </h:selectOneMenu>
                        <a4j:commandButton id="atualizar_estado"
                                           action="#{FornecedorControle.montarListaSelectItemEstado}"
                                           image="/imagens/atualizar.png" ajaxSingle="true" reRender="form:estado"/>
                        <h:message for="estado" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>

                    <h:panelGroup>
                        <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Pessoa_cidade}:"/>
                    </h:panelGroup>
                    <h:panelGroup>
                        <h:selectOneMenu id="cidade" onblur="blurinput(this);" onfocus="focusinput(this);"
                                         styleClass="form"
                                         value="#{FornecedorControle.fornecedorVO.pessoa.cidade.codigo}">
                            <f:selectItems value="#{FornecedorControle.listaSelectItemCidade}"/>
                        </h:selectOneMenu>
                        <a4j:commandButton id="atualizar_cidade"
                                           action="#{FornecedorControle.montarListaSelectItemCidade}"
                                           image="/imagens/atualizar.png" immediate="true" ajaxSingle="true"
                                           reRender="form:cidade"/>
                        <h:message for="cidade" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>

                    <!-- plano de contas -->
                    <h:outputText  styleClass="tituloCampos"
                                  value="#{msg_aplic.prt_Finan_Lancamento_planoContas}"/>
                    <h:panelGroup>
                        <table cellpadding="0" cellspacing="0">
                            <tr valign="top">
                                <td>
                                    <h:inputText id="nomePlanoSelecionado"
                                                 size="50"
                                                 maxlength="50"
                                                 onblur="blurinput(this);"
                                                 onfocus="focusinput(this);"
                                                 styleClass="form"
                                                 value="#{PlanoContasControle.planoNome}">
                                    </h:inputText>

                                    <rich:suggestionbox height="200" width="400"
                                                        for="nomePlanoSelecionado"
                                                        status="statusInComponent"
                                                        immediate="true"
                                                        suggestionAction="#{PlanoContasControle.executarAutocompletePesqPlanoContas}"
                                                        minChars="1"
                                                        nothingLabel="Nenhum Plano de Contas encontrado"
                                                        rowClasses="linhaImpar, linhaPar"
                                                        var="result" id="suggestionResponsavel">
                                        <a4j:support event="onselect"
                                                     reRender="form"
                                                     focus="nomeCentroSelecionado"
                                                     action="#{PlanoContasControle.selecionarPlanoContas}">
                                        </a4j:support>
                                        <h:column>
                                            <f:facet name="header">
                                                <h:outputText value="Nome" styleClass="textverysmall"/>
                                            </f:facet>
                                            <h:outputText styleClass="textverysmall"
                                                          value="#{result.descricaoCurtaComCodigo}"/>
                                        </h:column>
                                        <h:column>
                                            <f:facet name="header">
                                                <h:outputText value="Tipo" styleClass="textverysmall"/>
                                            </f:facet>
                                            <h:outputText styleClass="textverysmall"
                                                          value="#{result.tipoPadrao.descricao}"/>
                                        </h:column>
                                    </rich:suggestionbox>
                                </td>
                                <td> &nbsp;<a4j:commandLink action="#{PlanoContasControle.verificarConsultaLancamento}"
                                                            reRender="modalPlanos"
                                                            id="btAddPlano" value="Consultar"
                                                            oncomplete="Richfaces.showModalPanel('modalPlanos')"/></td>
                            </tr>
                        </table>
                    </h:panelGroup>

                    <!-- centro de custos -->

                    <h:outputText styleClass="tituloCampos"
                                  value="#{msg_aplic.prt_Finan_Lancamento_centroCusto}"/>
                    <h:panelGroup>
                        <table cellpadding="0" cellspacing="0">
                            <tr valign="top">
                                <td>
                                    <h:inputText id="nomeCentroSelecionado"
                                                 size="50"
                                                 maxlength="50"
                                                 onblur="blurinput(this);"
                                                 onfocus="focusinput(this);"
                                                 styleClass="form"
                                                 value="#{CentroCustosControle.centroNome}">
                                    </h:inputText>

                                    <rich:suggestionbox height="200" width="400"
                                                        for="nomeCentroSelecionado"
                                                        status="statusInComponent"
                                                        immediate="true"
                                                        suggestionAction="#{CentroCustosControle.executarAutocompletePesqCentroCusto}"
                                                        minChars="1"
                                                        nothingLabel="Nenhum Centro de Custos encontrado"
                                                        rowClasses="linhaImpar, linhaPar"
                                                        var="result" id="suggestionCentroCusto">
                                        <a4j:support event="onselect"
                                                     focus="observacao"
                                                     reRender="form"
                                                     action="#{CentroCustosControle.selecionarCentroCusto}">
                                        </a4j:support>
                                        <h:column>
                                            <f:facet name="header">
                                                <h:outputText value="Nome" styleClass="textverysmall"/>
                                            </f:facet>
                                            <h:outputText styleClass="textverysmall"
                                                          value="#{result.descricaoCurtaComCodigo}"/>
                                        </h:column>
                                    </rich:suggestionbox>
                                </td>
                                <td> &nbsp;<a4j:commandLink reRender="modalCentros"
                                                            id="btAddCentro" value="Consultar"
                                                            oncomplete="Richfaces.showModalPanel('modalCentros')"/></td>
                            </tr>
                        </table>
                    </h:panelGroup>


                    <h:outputText styleClass="tituloCampos" value="Observação:"/>
                    <h:inputTextarea id="observacao"
                                     value="#{FornecedorControle.fornecedorVO.observacao}"
                                     cols="51"
                                     rows="3"/>

                    <c:choose>
                        <c:when test="${modulo eq 'centralEventos'}">
                            <h:panelGroup>
                                <h:outputText styleClass="tituloCampos"
                                              value="#{CElabels['entidade.fornecedor.adicionarTercerizado']}:" />
                            </h:panelGroup>
                            <h:panelGroup>
                                <h:selectBooleanCheckbox
                                        value="#{FornecedorControle.fornecedorVO.checado}" id="checado">
                                    <a4j:support event="onclick" reRender="tableFornecedorServico" />
                                </h:selectBooleanCheckbox>
                            </h:panelGroup>
                        </c:when>
                        <c:otherwise>

                        </c:otherwise>
                    </c:choose>
                </h:panelGrid>
                <h:panelGrid id="tableFornecedorServico" columns="1" width="100%"
                             headerClass="subordinado" columnClasses="colunaCentralizada">
                    <rich:simpleTogglePanel
                        rendered="#{FornecedorControle.fornecedorVO.checado}"
                        switchType="client"
                        opened="#{!ProdutoLocacaoControle.visualizarRastreamento}"
                        onexpand="return false;" oncollapse="return false;">
                        <f:facet name="header">
                            <h:outputText
                                value="#{CElabels['entidade.fornecedor.adicionarTercerizado']}:" />
                        </f:facet>
                        <h:outputText styleClass="tituloCampos"
                                      value="#{CElabels['entidade.servico']}: " />
                        <h:selectOneMenu styleClass="form" onblur="blurinput(this);"
                                         onfocus="focusinput(this);" id="consultaServico"
                                         value="#{FornecedorControle.servico.codigo}">
                            <f:selectItem itemValue="0" itemLabel="--Selecione--" />
                            <f:selectItems value="#{FornecedorControle.listaServico}" />
                        </h:selectOneMenu>
                        <div align="center">
                            <a4j:commandButton
                                action="#{FornecedorControle.adicionaServicos}"
                                value="#{CElabels['operacoes.adicionar']}"
                                image="/imagens/botoesCE/incluir.png" styleClass="botoes"
                                reRender="tableServico, mensagemServico" />
                        </div>

                        <h:panelGrid columns="1" width="100%"
                                     styleClass="tabFormSubordinada">
                            <h:dataTable id="tableServico" width="100%"
                                         headerClass="subordinado" styleClass="tabFormSubordinada"
                                         rowClasses="linhaImpar, linhaPar"
                                         columnClasses="colunaAlinhamento"
                                         value="#{FornecedorControle.servico.fornecedorServicosTOs}"
                                         var="servico">

                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText value="#{CElabels['entidade.servico']}" />
                                    </f:facet>
                                    <h:outputText value="#{servico.descServico}" />
                                </h:column>
                                <h:column>
                                    <a4j:commandButton id="removerServico" reRender="tableServico"
                                                       ajaxSingle="true" immediate="true"
                                                       action="#{FornecedorControle.removerServicos}"
                                                       value="#{CElabels['operacoes.excluir']}"
                                                       image="/imagens/bt_excluir.png" styleClass="botoes" />
                                </h:column>
                            </h:dataTable>
                        </h:panelGrid>

                        <h:panelGrid id="mensagemServico" columns="1" width="100%">
                            <h:outputText styleClass="mensagemDetalhada"
                                          value="#{FornecedorControle.mensagemDetalhada}" escape="false" />
                        </h:panelGrid>
                    </rich:simpleTogglePanel>
                </h:panelGrid>

                <!-- ABAS PARA TELEFONE, ENDEREÇO E EMAIL -->
                <rich:tabPanel width="100%"
                               selectedTab="#{FornecedorControle.abaAtual}" switchType="client">
                    <!-- ABA DE ENDEREÇO -->
                    <rich:tab id="abaEndereco" label="Endereço">
                        <h:panelGrid id="panelEnderecoCliente" columns="1" width="100%"
                                     headerClass="subordinado" columnClasses="colunaCentralizada">
                            <f:facet name="header">
                                <h:outputText value="#{msg_aplic.prt_Endereco_tituloForm}" />
                            </f:facet>
                            <h:panelGrid id="panelEnderecoCliente1" columns="2"
                                         rowClasses="linhaImpar, linhaPar"
                                         columnClasses="classEsquerda, classDireita" width="100%">
                                <h:panelGroup>
                                    <h:outputText value="#{msg_aplic.prt_Asterisco}"
                                                  rendered="#{FornecedorControle.configuracaoSistema.cepOb}" />
                                    <h:outputText value="#{msg_aplic.prt_Endereco_cep}" />
                                </h:panelGroup>
                                <h:panelGroup>
                                    <h:inputText id="CEP" size="10" maxlength="10"
                                                 onkeypress="return mascara(this.form, 'form:CEP', '99.999-999', event);"
                                                 onblur="blurinput(this);" onfocus="focusinput(this);"
                                                 styleClass="form" value="#{FornecedorControle.enderecoVO.cep}"/>
                                    <rich:spacer width="10" />
                                    <a4j:commandLink id="buscaDadosCEP"
                                                     reRender="form:panelEnderecoCliente1,panelMensagemErro"
                                                     focus="form:enderecoCorresponencia"
                                                     action="#{FornecedorControle.consultarCEPCadastroCompleto}">Consulte o CEP</a4j:commandLink>
                                    <rich:spacer width="10" />
                                    <a4j:commandButton id="consultaDadosCEP" alt="Consultar CEP"
                                                       reRender="formConsultarCEP"
                                                       oncomplete="Richfaces.showModalPanel('panelCEP') , setFocus(formConsultarCEP,'formConsultarCEP:estadoCEP');"
                                                       image="../../../imagens/informacao.gif" />
                                </h:panelGroup>
                                <h:panelGroup>
                                    <h:outputText value="#{msg_aplic.prt_Asterisco}"
                                                  rendered="#{FornecedorControle.configuracaoSistema.enderecoOb or FornecedorControle.empresaLogado.habilitarCadastroEmpresaSesi}" />
                                    <h:outputText value="#{msg_aplic.prt_Endereco_endereco}" />
                                </h:panelGroup>
                                <h:inputText id="enderecoCliente" size="40" maxlength="40"
                                             onblur="blurinput(this);" onfocus="focusinput(this);"
                                             styleClass="form" value="#{FornecedorControle.enderecoVO.endereco}" />
                                <h:panelGroup>
                                    <h:outputText value="#{msg_aplic.prt_Asterisco}"
                                                  rendered="#{FornecedorControle.configuracaoSistema.enderecoComplementoOb}" />
                                    <h:outputText value="#{msg_aplic.prt_Endereco_complemento}" />
                                </h:panelGroup>
                                <h:inputText id="clienteComplemento" size="40" maxlength="40"
                                             onblur="blurinput(this);" onfocus="focusinput(this);"
                                             styleClass="form"
                                             value="#{FornecedorControle.enderecoVO.complemento}" />
                                <h:panelGroup>
                                    <h:outputText value="#{msg_aplic.prt_Asterisco}"
                                                  rendered="#{FornecedorControle.configuracaoSistema.numeroOb}" />
                                    <h:outputText value="#{msg_aplic.prt_Endereco_numero}" />
                                </h:panelGroup>
                                <h:inputText id="clienteNumeroEndereco" size="10" maxlength="10"
                                             onblur="blurinput(this);" onfocus="focusinput(this);"
                                             styleClass="form"
                                             value="#{FornecedorControle.enderecoVO.numero}" />
                                <h:panelGroup>
                                    <h:outputText value="#{msg_aplic.prt_Asterisco}"
                                                  rendered="#{FornecedorControle.configuracaoSistema.bairroOb}" />
                                    <h:outputText value="#{msg_aplic.prt_Endereco_bairro}" />
                                </h:panelGroup>
                                <h:inputText id="clienteBairro" size="35" maxlength="35"
                                             onblur="blurinput(this);" onfocus="focusinput(this);"
                                             styleClass="form" value="#{FornecedorControle.enderecoVO.bairro}" />
                                <h:outputText
                                    value="#{msg_aplic.prt_Endereco_enderecoCorrespondencia}" />
                                <h:selectBooleanCheckbox id="enderecoCorresponencia"
                                                         value="#{FornecedorControle.enderecoVO.enderecoCorrespondencia}" />
                                <h:panelGroup>
                                    <h:outputText value="#{msg_aplic.prt_Endereco_tipoEndereco}" />
                                </h:panelGroup>
                                <h:selectOneMenu id="Endereco_tipoEndereco"
                                                 onblur="blurinput(this);" onfocus="focusinput(this);"
                                                 styleClass="form"
                                                 value="#{FornecedorControle.enderecoVO.tipoEndereco}">
                                    <f:selectItems
                                        value="#{FornecedorControle.listaSelectItemTipoEnderecoEndereco}" />
                                </h:selectOneMenu>
                            </h:panelGrid>
                            <c:choose>
                                <c:when test="${modulo eq 'centralEventos'}">
                                    <a4j:commandButton id="btnAdicionarEndereco"
                                                       action="#{FornecedorControle.adicionarEndereco}"
                                                       reRender="panelEnderecoCliente,panelMensagemErro"
                                                       focus="form:enderecoCliente"
                                                       value="     #{msg_bt.btn_adicionar}     " accesskey="5"
                                                       image="../../../imagens/botoesCE/incluir.png" />
                                </c:when>
                                <c:when test="${modulo eq 'financeiroWeb'}">
                                    <a4j:commandButton id="btnGravar"
                                                       action="#{FornecedorControle.adicionarEndereco}"
                                                       reRender="panelEnderecoCliente,panelMensagemErro"
                                                       focus="form:enderecoCliente"
                                                       value="     #{msg_bt.btn_adicionar}     " accesskey="5" />
                                </c:when>
                                <c:otherwise>
                                    <a4j:commandButton id="btnGravar"
                                                       action="#{FornecedorControle.adicionarEndereco}"
                                                       reRender="panelEnderecoCliente,panelMensagemErro"
                                                       focus="form:enderecoCliente"
                                                       value="     #{msg_bt.btn_adicionar}     " accesskey="5"
                                                       image="../../../imagens/botaoGravar.png"/>
                                </c:otherwise>
                            </c:choose>
                            <h:panelGrid columns="1" width="100%">
                                <h:dataTable id="enderecoVO" width="100%"
                                             headerClass="subordinado" rowClasses="linhaImpar, linhaPar"
                                             columnClasses="colunaAlinhamento"
                                             value="#{FornecedorControle.fornecedorVO.enderecoVOs}" var="endereco">
                                    <h:column>
                                        <f:facet name="header">
                                            <h:outputText value="#{msg_aplic.prt_Endereco_endereco}" />
                                        </f:facet>
                                        <h:outputText value="#{endereco.endereco}" />
                                    </h:column>
                                    <h:column>
                                        <f:facet name="header">
                                            <h:outputText value="#{msg_aplic.prt_Endereco_complemento}" />
                                        </f:facet>
                                        <h:outputText value="#{endereco.complemento}" />
                                    </h:column>
                                    <h:column>
                                        <f:facet name="header">
                                            <h:outputText value="#{msg_aplic.prt_Endereco_numero}" />
                                        </f:facet>
                                        <h:outputText value="#{endereco.numero}" />
                                    </h:column>
                                    <h:column>
                                        <f:facet name="header">
                                            <h:outputText value="#{msg_aplic.prt_Endereco_bairro}" />
                                        </f:facet>
                                        <h:outputText value="#{endereco.bairro}" />
                                    </h:column>
                                    <h:column>
                                        <f:facet name="header">
                                            <h:outputText value="#{msg_aplic.prt_Endereco_cep}" />
                                        </f:facet>
                                        <h:outputText value="#{endereco.cep}" />
                                    </h:column>
                                    <h:column>
                                        <f:facet name="header">
                                            <h:outputText value="#{msg_aplic.prt_Endereco_tipoEndereco}" />
                                        </f:facet>
                                        <h:outputText value="#{endereco.tipoEndereco_Apresentar}" />
                                    </h:column>
                                    <h:column>
                                        <f:facet name="header">
                                            <h:outputText
                                                value="#{msg_aplic.prt_Endereco_enderecoCorrespondencia}" />
                                        </f:facet>
                                        <h:outputText
                                            value="#{endereco.enderecoCorrespondencia_Apresentar}" />
                                    </h:column>
                                    <h:column>
                                        <f:facet name="header">
                                            <h:outputText value="#{msg_bt.btn_opcoes}" />
                                        </f:facet>
                                        <h:panelGroup>
                                            <h:commandButton id="editarItemVenda"
                                                             action="#{FornecedorControle.editarEndereco}"
                                                             value="#{msg_bt.btn_editar}" image="../../../imagens/botaoEditar.png"
                                                             accesskey="6" styleClass="botoes" />

                                                <h:outputText value="    " />

                                            <h:commandButton id="removerItemVenda"
                                                             action="#{FornecedorControle.removerEndereco}"
                                                             value="#{msg_bt.btn_excluir}"
                                                             image="../../../imagens/botaoRemover.png" accesskey="7"
                                                             styleClass="botoes" />
                                        </h:panelGroup>
                                    </h:column>
                                </h:dataTable>
                            </h:panelGrid>
                        </h:panelGrid>
                    </rich:tab>
                    <!-- FIM DA ABA DE ENDEREÇO -->
                    <!-- ABA PARA O TELEFONE -->
                    <rich:tab id="abaTelefone" label="Telefone">
                        <h:panelGrid id="panelTelefoneCliente" columns="1" width="100%"
                                     headerClass="subordinado" columnClasses="colunaCentralizada">
                            <f:facet name="header">
                                <h:outputText value="#{msg_aplic.prt_Telefone_tituloForm}" />
                            </f:facet>
                            <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar"
                                         columnClasses="classEsquerda, classDireita" width="100%">
                                <h:panelGroup>
                                    <h:outputText value="#{msg_aplic.prt_Asterisco}"
                                                  rendered="#{FornecedorControle.configuracaoSistema.telefoneOb}" />
                                    <h:outputText value="#{msg_aplic.prt_Telefone_numero}" />
                                </h:panelGroup>

                                <h:panelGroup>

                                    <c:if test="${FornecedorControle.configuracaoSistema.usarSistemaInternacional}">
                                        <h:inputText id="ddiTelefoneFornecedor"
                                                     size="4" title="DDI"
                                                     maxlength="4"
                                                     onblur="blurinput(this);"
                                                     onkeypress="return mascara(this.form, this.id , '+999', event);"
                                                     onfocus="focusinput(this);"
                                                     styleClass="form"
                                                     value="#{FornecedorControle.telefoneVO.ddi}"
                                                     style="margin: 0 3px"/>

                                        <h:inputText id="numeroTelefoneCliente" size="13"
                                                     maxlength="20"
                                                     onblur="blurinput(this);"
                                                     onfocus="focusinput(this);"
                                                     styleClass="form"
                                                     value="#{FornecedorControle.telefoneVO.numero}"/>
                                    </c:if>
                                    <c:if test="${!FornecedorControle.configuracaoSistema.usarSistemaInternacional}">
                                        <h:inputText id="numeroTelefoneCliente" size="13"
                                                     maxlength="13"
                                                     onchange="return validar_Telefone(this.id);"
                                                     onblur="blurinput(this);"
                                                     onkeypress="return mascara(this.form, this.id , '(99)999999999', event);"
                                                     rendered="#{!FornecedorControle.configuracaoSistema.usarSistemaInternacional}"
                                                     onfocus="focusinput(this);"
                                                     styleClass="form"
                                                     value="#{FornecedorControle.telefoneVO.numero}"/>
                                    </c:if>
                                    <%--<rich:jQuery id="mskTelefone" selector="#numeroTelefoneCliente"--%>
                                                 <%--timing="onload" query="mask('(99)9999-9999')" />--%>
                                </h:panelGroup>
                                <h:outputText value="*Tipo do telefone:"/>
                                <h:selectOneMenu id="Telefone_tipoTelefone"
                                                 onblur="blurinput(this);" onfocus="focusinput(this);"
                                                 styleClass="form"
                                                 value="#{FornecedorControle.telefoneVO.tipoTelefone}">
                                    <f:selectItems
                                        value="#{FornecedorControle.listaSelectItemTipoTelefoneTelefone}" />
                                </h:selectOneMenu>
                                <h:outputText value="#{msg_aplic.prt_Telefone_descricao}" />
                                <h:inputText id="descricaoTelefone" size="25" maxlength="20"
                                             styleClass="form" value="#{FornecedorControle.telefoneVO.descricao}" />
                            </h:panelGrid>

                            <c:choose>
                                <c:when test="${modulo eq 'centralEventos'}">
                                    <a4j:commandButton id="btnAdicionarTelefone"
                                                       action="#{FornecedorControle.adicionarTelefone}"
                                                       reRender="panelTelefoneCliente,mensagens"
                                                       focus="form:numeroTelefoneCliente"
                                                       value="#{msg_bt.btn_adicionar}" accesskey="6"
                                                       image="../../../imagens/botaoAdicionar.png" />
                                </c:when>
                                <c:when test="${modulo eq 'financeiroWeb'}">
                                    <a4j:commandButton id="btnAdicionarTelefone"
                                                       action="#{FornecedorControle.adicionarTelefone}"
                                                       reRender="panelTelefoneCliente,mensagens"
                                                       focus="form:numeroTelefoneCliente"
                                                       value="#{msg_bt.btn_adicionar}" accesskey="6" />
                                </c:when>
                                <c:otherwise>
                                    <a4j:commandButton id="btnAdicionarTelefone"
                                                       action="#{FornecedorControle.adicionarTelefone}"
                                                       reRender="panelTelefoneCliente,mensagens"
                                                       focus="form:numeroTelefoneCliente"
                                                       value="#{msg_bt.btn_adicionar}" accesskey="6"
                                                       image="../../../imagens/botaoAdicionar.png"/>
                                </c:otherwise>
                            </c:choose>

                            <h:panelGrid columns="1" width="100%"
                                         styleClass="tabFormSubordinada">
                                <h:dataTable id="telefoneVO" width="100%"
                                             headerClass="subordinado" rowClasses="linhaImpar, linhaPar"
                                             columnClasses="colunaAlinhamento"
                                             value="#{FornecedorControle.fornecedorVO.telefoneVOs}" var="telefone">

                                    <h:column rendered="#{FornecedorControle.configuracaoSistema.usarSistemaInternacional}">
                                        <f:facet name="header">
                                            <h:outputText value="DDI" />
                                        </f:facet>
                                        <h:outputText value="#{telefone.ddi}" />
                                    </h:column>
                                    <h:column>
                                        <f:facet name="header">
                                            <h:outputText value="#{msg_aplic.prt_Telefone_numero}" />
                                        </f:facet>
                                        <h:outputText value="#{telefone.numero}" />
                                    </h:column>
                                    <h:column>
                                        <f:facet name="header">
                                            <h:outputText value="#{msg_aplic.prt_Telefone_tipoTelefone}" />
                                        </f:facet>
                                        <h:outputText value="#{telefone.tipoTelefone_Apresentar}" />
                                    </h:column>
                                    <h:column>
                                        <f:facet name="header">
                                            <h:outputText value="#{msg_aplic.prt_Telefone_descricao}" />
                                        </f:facet>
                                        <h:outputText value="#{telefone.descricao}" />
                                    </h:column>
                                    <h:column>
                                        <f:facet name="header">
                                            <h:outputText value="#{msg_bt.btn_opcoes}" />
                                        </f:facet>
                                        <h:panelGroup>
                                            <h:commandButton id="editarItemVenda"
                                                             action="#{FornecedorControle.editarTelefone}"
                                                             value="#{msg_bt.btn_editar}" image="../../../imagens/botaoEditar.png"
                                                             accesskey="6" styleClass="botoes" />

                                                <h:outputText value="    " />

                                            <h:commandButton id="removerItemVenda"
                                                             action="#{FornecedorControle.removerTelefone}"
                                                             value="#{msg_bt.btn_excluir}"
                                                             image="../../../imagens/botaoRemover.png" accesskey="7"
                                                             styleClass="botoes" />
                                        </h:panelGroup>
                                    </h:column>
                                </h:dataTable>
                            </h:panelGrid>
                        </h:panelGrid>
                    </rich:tab>
                    <!-- FIM DA ABA PARA O TELEFONE -->
                    <!-- ABA PARA OS EMAILS -->
                    <rich:tab id="abaEmail" label="E-mail">
                        <h:panelGrid id="panelEmail" columns="1" width="100%"
                                     headerClass="subordinado" columnClasses="colunaCentralizada">
                            <f:facet name="header">
                                <h:outputText value="#{msg_aplic.prt_Email_tituloForm}" />
                            </f:facet>
                            <h:panelGrid columns="3" rowClasses="linhaImpar, linhaPar"
                                         columnClasses="classEsquerda, classDireita" width="100%">
                                <h:panelGroup>
                                    <h:outputText value="#{msg_aplic.prt_Asterisco}"
                                                  rendered="#{FornecedorControle.configuracaoSistema.emailOb}" />
                                    <h:outputText value="#{msg_aplic.prt_Email_email}" />
                                </h:panelGroup>
                                <h:inputText id="emailCliente" size="40" maxlength="50"
                                             onblur="blurinput(this);" onfocus="focusinput(this);"
                                             styleClass="form" value="#{FornecedorControle.emailVO.email}" />
                                <rich:spacer width="1" />
                                <h:outputText value="#{msg_aplic.prt_Email_emailCorrespondencia}" />
                                <h:selectBooleanCheckbox id="emailCorrespondencia"
                                                         styleClass="campos"
                                                         value="#{FornecedorControle.emailVO.emailCorrespondencia}" />
                            </h:panelGrid>

                            <c:choose>
                                <c:when test="${modulo eq 'centralEventos'}">
                                    <a4j:commandButton id="btnAdicionarEmail"
                                                       action="#{FornecedorControle.adicionarEmail}"
                                                       reRender="panelEmail,panelMensagemErro" focus="form:emailCliente"
                                                       value="     #{msg_bt.btn_adicionar}     " accesskey="8"
                                                       image="../../../imagens/botaoAdicionar.png" />
                                </c:when>
                                <c:when test="${modulo eq 'financeiroWeb'}">
                                    <a4j:commandButton id="btnAdicionarEmail"
                                                       action="#{FornecedorControle.adicionarEmail}"
                                                       reRender="panelEmail,panelMensagemErro" focus="form:emailCliente"
                                                       value="     #{msg_bt.btn_adicionar}     " accesskey="8" />
                                </c:when>
                                <c:otherwise>
                                    <a4j:commandButton id="btnAdicionarEmail"
                                                       action="#{FornecedorControle.adicionarEmail}"
                                                       reRender="panelEmail,panelMensagemErro" focus="form:emailCliente"
                                                       value="     #{msg_bt.btn_adicionar}     " accesskey="8"
                                                       image="../../../imagens/botaoAdicionar.png"/>
                                </c:otherwise>
                            </c:choose>

                            <h:panelGrid columns="1" width="100%"
                                         styleClass="tabFormSubordinada">
                                <h:dataTable id="emailVO" width="100%" headerClass="subordinado"
                                             rowClasses="linhaImpar, linhaPar"
                                             columnClasses="colunaAlinhamento"
                                             value="#{FornecedorControle.fornecedorVO.emailVOs}" var="email">x
                                    <h:column>
                                        <f:facet name="header">
                                            <h:outputText value="#{msg_aplic.prt_Email_email}" />
                                        </f:facet>
                                        <h:outputText value="#{email.email}" />
                                    </h:column>
                                    <h:column>
                                        <f:facet name="header">
                                            <h:outputText
                                                value="#{msg_aplic.prt_Email_emailCorrespondencia}" />
                                        </f:facet>
                                        <h:outputText value="#{email.emailCorrespondencia_Apresentar}" />
                                    </h:column>
                                    <h:column>
                                        <f:facet name="header">
                                            <h:outputText value="#{msg_bt.btn_opcoes}" />
                                        </f:facet>
                                        <h:panelGroup>
                                            <a4j:commandButton id="editarItemVenda" reRender="panelEmail"
                                                               action="#{FornecedorControle.editarEmail}"
                                                               value="#{msg_bt.btn_editar}" image="../../../imagens/botaoEditar.png"
                                                               accesskey="6" styleClass="botoes" />

                                                <h:outputText value="    " />

                                            <a4j:commandButton id="removerItemVenda" reRender="emailVO"
                                                               action="#{FornecedorControle.removerEmail}"
                                                               value="#{msg_bt.btn_excluir}"
                                                               image="../../../imagens/botaoRemover.png" accesskey="7"
                                                               styleClass="botoes" />
                                        </h:panelGroup>
                                    </h:column>
                                </h:dataTable>
                            </h:panelGrid>
                        </h:panelGrid>
                    </rich:tab>
                    <!-- FIM DA ABA PARA OS EMAILS -->


                    <c:if test="${modulo ne 'centralEventos'}">
                    <!-- Aba para listar as compras do fornecedor -->
                    <rich:tab id="abaCompras" label="Histórico Compras">
                        <h:panelGrid id="panelCompras" columns="1" width="100%"
                                     headerClass="subordinado" columnClasses="colunaCentralizada">
                            <f:facet name="header">
                                <h:outputText value="Histórico de Compras" />
                            </f:facet>
                            <h:panelGrid columns="1" width="100%" bgcolor="#EEEEEE" columnClasses="centralizado">
                                <h:panelGroup id="panel1">
                                    <h:outputText styleClass="tituloCampos" style="padding-right:5px;padding-left:5px" value="Período cadastro:"></h:outputText>
                                    <rich:calendar id="dataInicio" value="#{FornecedorControle.dataIniCompra}"
                                                   showInput="true" datePattern="dd/MM/yyyy" zindex="2" showWeeksBar="false">
                                    </rich:calendar>
                                    <h:outputText styleClass="tituloCampos" value=" à "/>
                                    <rich:calendar id="dataFim" value="#{FornecedorControle.dataFimCompra}"
                                                   showInput="true" datePattern="dd/MM/yyyy" zindex="2" showWeeksBar="false">
                                    </rich:calendar>

                                    <!-- Número NF -->
                                    <h:outputText style="padding-left:5px;padding-right:5px;vertical-align:middle;" styleClass="tituloCampos"  value="Número NF:"/>
                                    <h:inputText id="numeroNF" style = "vertical-align: middle;" size="10" value="#{FornecedorControle.numeroNF}" />

                                    <a4j:commandButton id="btnPesquisarCompras" action="#{FornecedorControle.consultarCompras}" value="Pesquisar" reRender="itemsCompra, mensagens" ></a4j:commandButton>
                                </h:panelGroup>
                            </h:panelGrid>

                            <h:dataTable id="itemsCompra" width="100%" headerClass="consulta" rowClasses="linhaImpar, linhaPar"
                                         columnClasses="colunaCentralizada, colunaCentralizada, colunaCentralizada, colunaCentralizada, colunaCentralizada, colunaDireita, colunaCentralizada"
                                         value="#{FornecedorControle.listaCompras}"  rows="10" var="compra">

                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText value="Código"/>
                                    </f:facet>
                                    <h:commandLink action="#{CompraControle.editar}" id="codigo" value="#{compra.codigo}"/>
                                </h:column>

                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText value="Número NF"/>
                                    </f:facet>
                                    <h:outputText  id="numeroNF" value="#{compra.numeroNF}"/>
                                </h:column>

                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText value="Data Cadastro"/>
                                    </f:facet>
                                    <h:outputText style="font-weight: bold" styleClass="blue"
                                                  value="#{compra.dataCadastro}">
                                        <f:convertDateTime type="date" dateStyle="short"
                                                           locale="pt" timeZone="America/Sao_Paulo"
                                                           pattern="dd/MM/yyyy HH:mm" />
                                    </h:outputText>
                                </h:column>


                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText value="Status"/>
                                    </f:facet>
                                    <h:outputText  rendered="#{!compra.cancelada}" id="statusa" value="Ativa"/>
                                    <h:outputText  rendered="#{compra.cancelada}" id="statusc" value="Cancelada"/>
                                </h:column>

                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText value="Empresa"/>
                                    </f:facet>
                                    <h:outputText  id="empresa" value="#{compra.empresa.nome}"/>
                                </h:column>

                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText value="Valor Total"/>
                                    </f:facet>
                                    <h:outputText  value="#{compra.valorTotal}">
                                        <f:converter converterId="FormatadorNumerico" />
                                    </h:outputText>
                                </h:column>

                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText value="#{msg_bt.btn_opcoes}"/>
                                    </f:facet>

                                    <a4j:commandButton id="imprimir"
                                                       alt="Imprimir Compra"
                                                       value="imprimir" image="../../../imagens/imprimir.png"
                                                       action="#{RelatorioCompraControle.imprimirRelatorioCompraPDF}"
                                                       oncomplete="abrirPopupPDFImpressao('../../../relatorio/#{RelatorioCompraControle.nomeArquivoRelatorioGeradoAgora}','', 780, 595);">
                                    </a4j:commandButton>


                                </h:column>
                            </h:dataTable>

                            <rich:datascroller align="center"
                                for="itemsCompra" maxPages="10"
                              id="scResultadoPesqCompra" />

                        </h:panelGrid>
                    </rich:tab>
                    </c:if>

                    <!-- Aba para replicação de rede empresa -->
                    <c:if test="${FornecedorControle.exibirReplicarRedeEmpresa}">
                        <rich:tab id="tabReplicarEmpresasFornecedor" label="Replicar Empresas" rendered="#{FornecedorControle.exibirReplicarRedeEmpresa}">
                            <h:panelGrid columns="1" width="100%" headerClass="subordinado"
                                         columnClasses="colunaCentralizada">
                                <f:facet name="header">
                                    <h:outputText value="#{msg_aplic.prt_PlanoReplicarEmpresa_tituloForm}"/>
                                </f:facet>
                                <h:panelGrid columns="3" style="border-style: solid;" id="contadorReplicaFornecedor"
                                             columnClasses="colunaCentralizada, colunaCentralizada, colunaCentralizada"
                                             width="100%">
                                    <h:outputText value="Unidades" styleClass="botoes nvoBt"/>
                                    <h:outputText value="Replicadas" styleClass="botoes nvoBt"/>
                                    <h:outputText value="Não Replicadas" styleClass="botoes nvoBt"/>
                                    <h:outputText value="#{FornecedorControle.listaFornecedorRedeEmpresaSize}"
                                                  style="font-size: 20pt; font-weight: bold;"/>
                                    <h:outputText value="#{FornecedorControle.listaFornecedorRedeEmpresaSincronizado}"
                                                  style="color: #0f4c36; font-size: 20pt; font-weight: bold;"/>
                                    <h:outputText
                                            value="#{FornecedorControle.listaFornecedorRedeEmpresaSize - FornecedorControle.listaFornecedorRedeEmpresaSincronizado}"
                                            style="color: #8b0000; font-size: 20pt; font-weight: bold;"/>
                                </h:panelGrid>
                                <h:panelGrid columns="1" id="contadorReplicaFornecedor2"
                                             columnClasses="colunaDireita"
                                             width="100%"
                                             style="margin-top: 20px; margin-bottom: 1px">
                                    <h:panelGroup layout="block">
                                        <a4j:commandButton value="Replicar Todas" styleClass="botoes nvoBt"
                                                           action="#{FornecedorControle.replicarTodas}"
                                                           reRender="listaEmpresasReplicar, contadorReplicaFornecedor"
                                                           ajaxSingle="true" immediate="true"/>
                                        <a4j:commandButton value="Replicar Selecionadas" styleClass="botoes nvoBt btSec"
                                                           action="#{FornecedorControle.replicarSelecionadas}"
                                                           reRender="listaEmpresasReplicar, contadorReplicaFornecedor"
                                                           ajaxSingle="true" immediate="true"/>
                                        <a4j:commandButton value="Limpar Selecionadas" styleClass="botoes nvoBt btSec"
                                                           action="#{FornecedorControle.limparReplicar}"
                                                           reRender="listaEmpresasReplicar, contadorReplicaFornecedor"
                                                           ajaxSingle="true" immediate="true"/>
                                    </h:panelGroup>
                                </h:panelGrid>
                                <h:panelGrid columns="1" rowClasses="linhaImpar, linhaPar"
                                             columnClasses="colunaCentralizada" width="100%">

                                    <h:dataTable id="listaEmpresasReplicar" width="100%" headerClass="subordinado"
                                                 styleClass="tabFormSubordinada"
                                                 rowClasses="linhaImpar, linhaPar"
                                                 columnClasses="colunaEsquerda, colunaEsquerda, colunaCentralizada, colunaEsquerda"
                                                 style="text-align: center;"
                                                 value="#{FornecedorControle.listaFornecedorRedeEmpresa}"
                                                 var="fornecedorRedeEmpresaReplicacao">
                                        <h:column>
                                            <f:facet name="header">
                                                <h:outputText value=""/>
                                            </f:facet>
                                            <h:selectBooleanCheckbox id="check" styleClass="form"
                                                                     rendered="#{!fornecedorRedeEmpresaReplicacao.dataAtualizacaoInformada}"
                                                                     value="#{fornecedorRedeEmpresaReplicacao.selecionado}">
                                                <a4j:support event="onchange" reRender="listaEmpresasReplicar"/>
                                            </h:selectBooleanCheckbox>
                                        </h:column>
                                        <h:column>
                                            <f:facet name="header">
                                                <h:outputText value="#{msg_aplic.prt_PlanoRedeEmpresa_nomeUnidade}"/>
                                            </f:facet>
                                            <h:outputText value="#{fornecedorRedeEmpresaReplicacao.nomeUnidade}"/>
                                        </h:column>
                                        <h:column>
                                            <f:facet name="header">
                                                <h:outputText value="#{msg_aplic.prt_PlanoRedeEmpresa_chave}"/>
                                            </f:facet>
                                            <h:outputText value="#{fornecedorRedeEmpresaReplicacao.chave}"/>
                                        </h:column>
                                        <h:column>
                                            <f:facet name="header">
                                                <h:outputText value=""/>
                                            </f:facet>
                                            <h:panelGroup>
                                                <a4j:commandButton id="replicarFornecedor"
                                                                   reRender="listaEmpresasReplicar, contadorReplicaFornecedor"
                                                                   ajaxSingle="true" immediate="true"
                                                                   rendered="#{!fornecedorRedeEmpresaReplicacao.dataAtualizacaoInformada}"
                                                                   action="#{FornecedorControle.replicarFornecedorRedeEmpresaGeral}"
                                                                   value="Replicar"/>
                                                <h:graphicImage url="./images/check.png"
                                                                rendered="#{fornecedorRedeEmpresaReplicacao.dataAtualizacaoInformada}"/>
                                            </h:panelGroup>
                                        </h:column>
                                        <h:column>
                                            <f:facet name="header">
                                                <h:outputText
                                                        value="#{msg_aplic.prt_PlanoRedeEmpresa_mensagemSituacao}"/>
                                            </f:facet>
                                            <h:outputText value="#{fornecedorRedeEmpresaReplicacao.mensagemSituacao}"/>
                                        </h:column>
                                        <h:column>
                                            <f:facet name="header">
                                                <h:outputText
                                                        value="Vínculo"/>
                                            </f:facet>
                                            <h:panelGroup>
                                                <a4j:commandButton
                                                        rendered="#{fornecedorRedeEmpresaReplicacao.dataAtualizacaoInformada}"
                                                        reRender="listaEmpresasReplicar, contadorReplicaFornecedor"
                                                        ajaxSingle="true" immediate="true"
                                                        action="#{FornecedorControle.retirarVinculoReplicacao}"
                                                        value="Retirar"/>
                                            </h:panelGroup>
                                        </h:column>

                                    </h:dataTable>
                                </h:panelGrid>
                            </h:panelGrid>
                        </rich:tab>
                    </c:if>

                </rich:tabPanel>
                <!-- Fim das abas -->


                <!-- PAINEL DE EXIBIÇÃO DE MENSAGENS E AÇÕES -->
                <h:panelGrid id="mensagens" columns="1" width="100%"
                             styleClass="tabMensagens">

                    <h:panelGrid columns="3" width="100%" styleClass="tabMensagens">
                        <h:panelGrid columns="1" width="100%">

                                <h:outputText value=" " />

                        </h:panelGrid>

                        <!-- MENSAGENS -->
                        <h:commandButton rendered="#{FornecedorControle.sucesso}"
                                         image="/imagens/bt_sucesso.png" />
                        <h:commandButton rendered="#{FornecedorControle.erro}"
                                         image="/imagens/erro.png" />
                        <h:panelGrid columns="1" width="100%">
                            <h:outputText styleClass="mensagem"
                                          value="#{FornecedorControle.mensagem}" escape="false" />
                            <h:outputText styleClass="mensagemDetalhada"
                                          value="#{FornecedorControle.mensagemDetalhada}" escape="false" />
                        </h:panelGrid>
                    </h:panelGrid>

                    <!-- AÇÕES -->
                    <h:panelGrid columns="1" width="100%"
                                 columnClasses="colunaCentralizada">
                        <h:panelGroup>
                            <c:choose>
                                <c:when test="${modulo eq 'centralEventos'}">
                                    <!-- BOTÃO QUE SALVA UM NOVO OBJETO -->
                                    <h:commandButton id="salvarFornecedorCE" action="#{FornecedorControle.salvar}"
                                                     value="#{CElabels['operacoes.gravar']}"
                                                     onclick="if(!validar()){return false;};"
                                                     alt="#{CElabels['operacoes.gravar.dados']}" styleClass="botoes nvoBt"
                                                     actionListener="#{FornecedorControle.autorizacao}">
                                        <!-- Entidade.FORNECEDOR -->
                                        <f:attribute name="entidade" value="104" />
                                        <!-- Operacao.GRAVAR -->
                                        <f:attribute name="operacao" value="G" />
                                    </h:commandButton>
                                    <h:outputText value="    " />
                                    <h:panelGroup
                                            rendered="#{FornecedorControle.fornecedorVO.codigo gt 0}">
                                        <a4j:commandButton id="excluir"
                                                           action="#{FornecedorControle.confirmarExcluir}"
                                                           oncomplete="#{FornecedorControle.msgAlert}"
                                                           styleClass="botoes nvoBt btSec btPerigo" reRender="form"
                                                           actionListener="#{FornecedorControle.autorizacao}"
                                                           value="#{msg_bt.btn_excluir}" title="#{msg.msg_excluir_dados}" accesskey="3" />
                                        <h:outputText value="    "/>
                                        <!-- Entidade.fornecedor -->
                                        <f:attribute name="entidade" value="104"/>
                                        <!-- Operacao.consultar -->
                                        <f:attribute name="operacao" value="E"/>
                                    </h:panelGroup>
                                    <!-- BOTÃO QUE CHAMA O MÉTODO DE CONSULTA -->
                                    <h:commandButton id="consultarFornecedorCE" immediate="true"
                                                     action="#{NavegacaoControle.abrirFornecedor}"
                                                     value="#{msg_bt.btn_voltar_lista}"
                                                     alt="#{CElabels['operacoes.consulta.consultarDados']}"
                                                     styleClass="botoes nvoBt btSec"
                                                     actionListener="#{FornecedorControle.autorizacao}">
                                        <!-- Entidade.fornecedor -->
                                        <f:attribute name="entidade" value="104" />
                                        <!-- Operacao.consultar -->
                                        <f:attribute name="operacao" value="C" />
                                    </h:commandButton>

                                </c:when>

                                <c:when test="${modulo eq 'financeiroWeb'}">
                                    <h:panelGrid columns="1" width="100%" styleClass="tabMensagens">
                                        <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                                            <h:panelGroup>
                                                <!-- BOTÃO QUE SALVA UM NOVO OBJETO -->
                                                <h:commandButton id="salvarFornecedorFW" action="#{FornecedorControle.salvar}"
                                                                 value="#{CElabels['operacoes.gravar']}"
                                                                 onclick="if(!validar()){return false;};"
                                                                 alt="#{CElabels['operacoes.gravar.dados']}"
                                                                 styleClass="botoes nvoBt"
                                                                 actionListener="#{FornecedorControle.autorizacao}">
                                                    <!-- Entidade.FORNECEDOR -->
                                                    <f:attribute name="entidade" value="104" />
                                                    <!-- Operacao.GRAVAR -->
                                                    <f:attribute name="operacao" value="G" />
                                                </h:commandButton>

                                                <h:outputText value="    " />
                                                <a4j:commandButton id="excluir"  reRender="mdlMensagemGenerica"
                                                                   oncomplete="#{FornecedorControle.msgAlert}" action="#{FornecedorControle.confirmarExcluir}"
                                                                   value="#{msg_bt.btn_excluir}" title="#{msg.msg_excluir_dados}" accesskey="3" styleClass="botoes nvoBt btSec btPerigo"
                                                                   actionListener="#{FornecedorControle.autorizacao}">

                                                    <!-- Entidade.fornecedor -->
                                                    <f:attribute name="entidade" value="104" />
                                                    <!-- Operacao.consultar -->
                                                    <f:attribute name="operacao" value="E" />
                                                </a4j:commandButton>

                                                <h:outputText value="    " />
                                                <a4j:commandLink id="btnLog"
                                                                 styleClass="botoes nvoBt btSec"
                                                                 style="display: inline-block; padding: 9px 13px;"
                                                                 action="#{FornecedorControle.realizarConsultaLogObjetoSelecionado}"
                                                                 oncomplete="abrirPopup('../../../visualizadorLogForm.jsp', 'VisualizadorLog', 785, 595);">
                                                    <h:outputText title="visualizar log da entidade" styleClass="fa-icon-list"/>
                                                </a4j:commandLink>
                                                <h:outputText value="    " />
                                                <!-- BOTÃO QUE CHAMA O MÉTODO DE CONSULTA -->
                                                <h:commandButton id="consultarFornecedorFW" immediate="true"
                                                                 action="#{NavegacaoControle.abrirFornecedor}"
                                                                 value="#{msg_bt.btn_voltar_lista}"
                                                                 alt="#{CElabels['operacoes.consulta.consultarDados']}"
                                                                 styleClass="botoes nvoBt btSec"
                                                                 actionListener="#{FornecedorControle.autorizacao}">
                                                    <!-- Entidade.fornecedor -->
                                                    <f:attribute name="entidade" value="104" />
                                                    <!-- Operacao.consultar -->
                                                    <f:attribute name="operacao" value="C" />
                                                </h:commandButton>
                                            </h:panelGroup>
                                        </h:panelGrid>
                                    </h:panelGrid>
                                </c:when>
                                <c:otherwise>
                                    <h:commandButton id="salvar" action="#{FornecedorControle.salvar}"
                                                     value="#{CElabels['operacoes.gravar']}"
                                                     onclick="if(!validar()){return false;};"
                                                     alt="#{CElabels['operacoes.gravar.dados']}"
                                                     styleClass="botoes nvoBt"
                                                     actionListener="#{FornecedorControle.autorizacao}">
                                        <!-- Entidade.FORNECEDOR -->
                                        <f:attribute name="entidade" value="104" />
                                        <!-- Operacao.GRAVAR -->
                                        <f:attribute name="operacao" value="G" />
                                    </h:commandButton>

                                    <h:outputText value="    " />

                                    <h:panelGroup
                                            rendered="#{FornecedorControle.fornecedorVO.codigo gt 0}">
                                        <a4j:commandButton id="excluir"
                                                           action="#{FornecedorControle.confirmarExcluir}"
                                                           oncomplete="#{FornecedorControle.msgAlert}"
                                                           styleClass="botoes nvoBt btSec btPerigo" reRender="form"
                                                           actionListener="#{FornecedorControle.autorizacao}"
                                                           value="#{msg_bt.btn_excluir}" title="#{msg.msg_excluir_dados}" accesskey="3" >
                                        <
                                            <!-- Entidade.fornecedor -->
                                            <f:attribute name="entidade" value="104" />
                                            <!-- Operacao.consultar -->
                                            <f:attribute name="operacao" value="E" />
                                        </a4j:commandButton>

                                        <h:outputText value="    " />
                                    </h:panelGroup>
                                    <!-- BOTÃO QUE CHAMA O MÉTODO DE CONSULTA -->
                                    <h:commandButton id="consultar" immediate="true"
                                                     action="#{NavegacaoControle.abrirFornecedor}"
                                                     value="#{CElabels['operacoes.consulta.consultar']}"
                                                     alt="#{CElabels['operacoes.consulta.consultarDados']}"
                                                     styleClass="botoes nvoBt btSec"
                                                     actionListener="#{FornecedorControle.autorizacao}">
                                        <!-- Entidade.fornecedor -->
                                        <f:attribute name="entidade" value="104" />
                                        <!-- Operacao.consultar -->
                                        <f:attribute name="operacao" value="C" />
                                    </h:commandButton>

                                </c:otherwise>
                            </c:choose>
                        </h:panelGroup>

                    </h:panelGrid>

                </h:panelGrid>
            </h:form>
        </body>
    </html>
    <%@include file="../../../includes/include_modal_mensagem_generica.jsp"%>

    <%@include file="/pages/finan/includes/include_modalSelecaoPlanoConta.jsp"%>
    <%@include file="/pages/finan/includes/include_modalSelecaoCentroCusto.jsp"%>
</f:view>

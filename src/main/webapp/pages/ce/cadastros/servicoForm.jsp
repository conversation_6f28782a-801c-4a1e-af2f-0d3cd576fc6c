<%@include file="../includes/include_imports.jsp" %>


<f:view>
    <jsp:include page="/includes/include_carregando_ripple.jsp" flush="true"/>
    <html>
       	<!-- INCLUI O ELEMENTO HEAD NA P�GINA -->
        <%@include file="../includes/include_head_form.jsp" %>
        <title>
            <h:outputText value="#{CElabels['entidade.servico']}"/>
        </title>

        <style type="text/css">
            .obrigatorio {
                color: #DDDD00;
            }
            .mensagemObrigatorio {
                color: #FF0000;
            }
        </style>
        <script >
            //Script de valida��o do formul�rio
            function exibirMensagem(mensagem, id) {
                var divObgMensagem = document.getElementById('divObg-' + id);
                divObgMensagem.innerHTML = mensagem;
            }
            function limparMensagem(id) {
                var divObgMensagem = document.getElementById('divObg-' + id);
                divObgMensagem.innerHTML = "";
            }
            function validar() {
                limparMensagem('valor');
                //Obtem os campos que ser�o validados
                var valor = document.getElementById('form:valor');
				

                //Verifica se est�o preenchidos
                var validade = true;

                if (valor == null
                    || valor.value == null
                    || valor.value == "") {
                    exibirMensagem('<h:outputText value="O campo Valor deve ser preenchido"/>', 'valor');
                    validade = false;
                }
                return validade;
            }
	
   		
        </script>
        <body>
        <c:set var="titulo" scope="session" value="Servi�o"/>
        <c:set var="urlWiki" scope="session" value="${SuperControle.urlWikiCE}Cadastros:PP:Servi�o"/>
            <%-- INICIO HEADER --%>


            <!-- FORMUL�RIO DE CADASTRO -->
            <h:form id="form">

                <!-- MANTEM O CONTROLLER NA �RVORE DE COMPONENTES -->
                <a4j:keepAlive beanName="ServicoControle" />

                <f:facet name="header">
                    <jsp:include page="../../../topoReduzido_material.jsp"/>
                </f:facet>
                <!-- CAMPOS PARA PREENCHIMENTO -->
                <h:panelGrid rowClasses="linhaImpar, linhaPar" columnClasses="classEsquerda, classDireita" style="background-image:url('./imagens/backGroundTelaBasico.png'); background-repeat: no-repeat;padding: 8px;" width="100%" columns="2">
                    <h:panelGroup>
                        <%@include file="../includes/include_obrigatorio.jsp" %>
                        <h:outputText styleClass="tituloCampos" value="#{CElabels['entidade.descricao']}:" />
                    </h:panelGroup>
                    <h:panelGroup>
                        <h:inputText rendered="#{!ServicoControle.existeServico}" onblur="blurinput(this); limparMsgObrig('form:descricao', 'descricao');" onfocus="focusinput(this);"
                                     styleClass="form" size="30" maxlength="30"
                                     style="border: 1px solid #8eb3c3"
                                     value="#{ServicoControle.servico.descricao}" id="descricao"></h:inputText>
                        <div id="divObg-descricao" class="mensagemObrigatorio"></div>
                        <h:outputText rendered="#{ServicoControle.existeServico}"
                                      styleClass="form" value="#{ServicoControle.servico.descricao}" id="duracaoMinimaHrs1" />
                    </h:panelGroup>

                    <h:panelGroup>
                        <%@include file="../includes/include_obrigatorio.jsp" %>
                        <h:outputText styleClass="tituloCampos" value="#{CElabels['entidade.valor']}:" />
                    </h:panelGroup>
                    <h:panelGroup>
                        <h:inputText id="valor"
                                     onblur="blurinput(this); limparMsgObrig('form:valor', 'valor');"
                                     onfocus="focusinput(this);"
                                     onkeypress="return(currencyFormat(this,'.',',',event));"
                                     styleClass="form" maxlength="14"
                                     value="#{ServicoControle.servico.valorFormatado}" />
                        <div id="divObg-valor" class="mensagemObrigatorio"></div>

                    </h:panelGroup>
                </h:panelGrid>

                <!-- PAINEL DE EXIBI��O DE MENSAGENS E A��ES -->
                <h:panelGrid id="mensagens" columns="1" width="100%" styleClass="tabMensagens">

                    <h:panelGrid columns="3" width="100%" styleClass="tabMensagens">
                        <h:panelGrid columns="1" width="100%">

                            <h:outputText value=" "/>

                        </h:panelGrid>

                        <!-- MENSAGENS -->
                        <h:commandButton rendered="#{ServicoControle.sucesso}" image="/imagens/bt_sucesso.png"/>
                        <h:commandButton rendered="#{ServicoControle.erro}" image="/imagens/erro.png"/>
                        <h:panelGrid columns="1" width="100%">
                            <h:outputText styleClass="mensagem" value="#{ServicoControle.mensagem}"/>
                            <h:outputText styleClass="mensagemDetalhada" value="#{ServicoControle.mensagemDetalhada}"/>
                        </h:panelGrid>
                    </h:panelGrid>

                    <!-- A��ES -->
                    <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                        <h:panelGroup>
                            <!-- BOT�O QUE SALVA UM NOVO OBJETO -->
                            <h:commandButton id="salvar" action="#{ServicoControle.salvar}" value="#{CElabels['operacoes.gravar']}"
                                             onclick="if(!validar()){return false;};"
                                             alt="#{CElabels['operacoes.gravar.dados']}"
                                             styleClass="botoes nvoBt" actionListener="#{ServicoControle.autorizacao}">
                                <!-- Entidade.SERVICO -->
                                <f:attribute name="entidade" value="103" />
                                <!-- Operacao.gravar -->
                                <f:attribute name="operacao" value="G" />
                            </h:commandButton>

                            <h:outputText value="    " />

                            <h:panelGroup rendered="#{ServicoControle.servico.codigo gt 0}">

                                <a4j:commandButton id="excluir"
                                                   action="#{ServicoControle.confirmarExcluir}"
                                                   oncomplete="#{ServicoControle.msgAlert}"
                                                   styleClass="botoes nvoBt btSec btPerigo" reRender="form"
                                                   actionListener="#{ServicoControle.autorizacao}"
                                                   value="#{msg_bt.btn_excluir}" title="#{msg.msg_excluir_dados}" accesskey="3">
                                    <!-- Entidade.fornecedor -->
                                    <f:attribute name="entidade" value="103"/>
                                    <!-- Operacao.consultar -->
                                    <f:attribute name="operacao" value="E"/>
                                </a4j:commandButton>

                                <h:outputText value="    " />

                            </h:panelGroup>
                            <!-- BOT�O QUE CHAMA O M�TODO DE CONSULTA -->
                            <h:commandButton id="consultar" immediate="true" action="#{NavegacaoControle.abrirTelaServico}"
                                             value="#{CElabels['operacoes.consulta.consultar']}"
                                             alt="#{CElabels['operacoes.consulta.consultarDados']}"
                                             styleClass="botoes nvoBt btSec"
                                             actionListener="#{ServicoControle.autorizacao}">
                                <!-- Entidade.SERVICO -->
                                <f:attribute name="entidade" value="103" />
                                <!-- Operacao.consultar -->
                                <f:attribute name="operacao" value="C" />
                            </h:commandButton>
                        </h:panelGroup>
                    </h:panelGrid>

                </h:panelGrid>
            </h:form>
        </body>
    </html>
    <%@include file="../../../includes/include_modal_mensagem_generica.jsp"%>
</f:view>
<%@page contentType="text/html" pageEncoding="ISO-8859-1" %>
<%@include file="../includes/include_imports.jsp" %>

<!-- <PERSON>lui o elemento HEAD da página -->
<%@include file="/pages/ce/includes/include_head.jsp" %>

<f:view>
    <jsp:include page="/includes/include_carregando_ripple.jsp" flush="true"/>
    <h:form id="form">
        <html>
        <!-- Inclui o elemento HEAD da página -->
        <head>
            <%@include file="../includes/include_head_ce.jsp" %>
        </head>
        <body class="ce">


        <h:panelGroup layout="block" style="display: inline-table;width: 100%;" styleClass="fundoCinza">

            <h:panelGroup layout="block" styleClass="bgtop topoZW" rendered="#{MenuControle.apresentarTopo}">
                <jsp:include page="../../../include_topo_2.0.jsp" flush="true"/>
                <jsp:include page="../../../include_menu_ce_flat.jsp" flush="true"/>
                <rich:jQuery selector=".item2" query="addClass('menuItemAtual')"/>
            </h:panelGroup>

            <h:panelGroup layout="block" styleClass="caixaCorpo">
                <h:panelGroup layout="block" style="height: 80%;width: 100%">
                    <h:panelGroup layout="block" styleClass="caixaMenuLatel">
                        <h:panelGroup layout="block" styleClass="container-imagem">
                            <table width="98%" border="0" align="left" cellpadding="0" cellspacing="0" class="text" style="margin-right:30px;margin-bottom:20px;">
                                <tr>
                                    <td width="19" height="50" align="left" valign="top"><img src="${contexto}/images/box_centro_top_left.gif" width="19" height="50"></td>
                                    <td align="left" valign="top" background="${contexto}/images/box_centro_top.gif" class="tituloboxcentro" style="padding:11px 0 0 0;">Cadastros</td>
                                    <td width="19" align="left" valign="top"><img src="${contexto}/images/box_centro_top_right.gif" width="19" height="50"></td>
                                </tr>
                                <tr>
                                    <td align="left" valign="top" background="${contexto}/images/box_centro_left.gif"><img src="${contexto}/images/shim.gif"></td>
                                    <td align="left" valign="top" bgcolor="#ffffff" style="padding:15px 15px 5px 15px;"  class="headerIndex">
                                        <h:panelGrid columns="1"  width="100%" cellpadding="0" cellspacing="0">
                                            <h:panelGrid columnClasses="w33,w33,w33"  columns="3" width="100%" cellpadding="0" cellspacing="0">

                                                <h:panelGrid columns="1" style="height:100%;" width="100%" cellpadding="5" cellspacing="5" >
                                                    <h:panelGroup>
                                                        <div>
                                                            <h:outputLink styleClass="linkWiki"
                                                                          value="#{SuperControle.urlWikiCE}Cadastros:CadastroAuxiliares"
                                                                          title="Clique e saiba mais: Cadastros Auxiliares"
                                                                          target="_blank" >
                                                                <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                                                            </h:outputLink>
                                                            <a href="telaCadastroAuxiliares.jsp" class="titulo3"><h:outputText value="#{CElabels['menu.cadastros.cadastrosAuxiliares']}"/></a></div>
                                                        <rich:spacer height="25px"/>
                                                        <rich:spacer width="10px"/>
                                                        <h:outputText styleClass="text" value="Defina as informações básicas necessárias para realizar os cadastros dos clientes e colaboradores, exemplo, categorias de cliente, cidade, país, profissões, etc. O Sistema dispõe de muitas opções para que o cadastro fique o mais personalizado possivel e com o máximo de informações, isso ajudará em um contato futuro, auxiliando a entender melhor o perfil do cliente."/>
                                                    </h:panelGroup>
                                                    <h:panelGroup>
                                                        <rich:spacer height="25px"/>
                                                        <div>
                                                            <h:outputLink styleClass="linkWiki"
                                                                          value="#{SuperControle.urlWikiCE}Cadastros:ProdutoPerfisEvento"
                                                                          title="Clique e saiba mais: Produtos/Perfis de Eventos"
                                                                          target="_blank" >
                                                                <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                                                            </h:outputLink>
                                                            <a href="telaCadastroProdutosPerfis.jsp" class="titulo3"><h:outputText value="Produtos"/></a></div>
                                                        <rich:spacer height="25px"/>
                                                        <rich:spacer width="10px"/>
                                                        <h:outputText styleClass="text" value="Cadastre todos os produtos que podem ser fornecidos para os eventos, definindo o preço e o tipo de cada um. Com este cadastro vocÊ realiza seus orçamentos de forma detalhada e após, realiza o check-list dos itens que o cliente solicitou."/>
                                                    </h:panelGroup>
                                                </h:panelGrid>

                                                <h:panelGrid style="height:100%" columns="1" width="100%" cellpadding="5" cellspacing="5" >
                                                    <h:panelGroup>
                                                        <div>
                                                            <h:outputLink styleClass="linkWiki"
                                                                          value="#{SuperControle.urlWikiCE}Cadastros:ProdutoPerfisEvento"
                                                                          title="Clique e saiba mais: Produtos/Perfis de Eventos"
                                                                          target="_blank" >
                                                                <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                                                            </h:outputLink>
                                                            <a href="telaCadastroProdutosPerfis.jsp" class="titulo3"><h:outputText value="Perfis de Eventos"/></a></div>
                                                        <rich:spacer height="25px"/>
                                                        <rich:spacer width="10px"/>
                                                        <h:outputText styleClass="text" value="Cadastre perfis de eventos, ou seja, pacotes com ambientes, produtos e serviços que podem ser oferecidos. Com os perfis você torna o seu atendimento mais prático e ágil, podendo também ter promoções cadastrando dias mais baratos para os ambientes (sazonalidades). Aqui são inseridos também os modelos de documentos necessários como o contrato e orçamento para preenchimento automático e a possibilidade de imprimir ou enviar via e-mail."/>

                                                    </h:panelGroup>

                                                    <h:panelGroup>
                                                        <rich:spacer height="25px"/>
                                                        <div>
                                                            <h:outputLink styleClass="linkWiki"
                                                                          value="#{SuperControle.urlWikiCE}Cadastros:Acesso_ao_Sistema"
                                                                          title="Clique e saiba mais: Acesso ao Sistema"
                                                                          target="_blank" >
                                                                <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                                                            </h:outputLink>
                                                            <a href="telaCadastroAcessoSistema.jsp" class="titulo3"><h:outputText value="#{CElabels['menu.cadastros.acessoSistema']}"/></a></div>
                                                        <rich:spacer height="25px"/>
                                                        <rich:spacer width="10px"/>
                                                        <h:outputText styleClass="text" value="Para acessar o sistema é necessário cadastrar devidamente os usuários com senha. Através destes cadastros é possível ter o controle de segurança baseado em perfis de acesso conforme as atividades de cada um e podendo assim rastrear as operações que forem realizadas."/>
                                                    </h:panelGroup>

                                                </h:panelGrid>
                                                <h:panelGrid columns="1" width="100%" style="height:100%;" cellpadding="5" cellspacing="5" >
                                                    <h:panelGroup>
                                                        <div><img src="${contexto}/imagens/cadastros.png"></div>
                                                    </h:panelGroup>
                                                    <h:panelGroup>
                                                        <div>
                                                            <h:outputLink styleClass="linkWiki"
                                                                          value="#{SuperControle.urlWikiCE}Cadastros:Financeiro"
                                                                          title="Clique e saiba mais: Financeiro"
                                                                          target="_blank" >
                                                                <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                                                            </h:outputLink>
                                                            <h:commandLink styleClass="titulo3" id="menufinanceiro" action="#{NavegacaoControle.abrirFinanceiro}"><h:outputText value="#{CElabels['menu.operacoesCE.financeiro']}"/></h:commandLink></div>
                                                        <rich:spacer height="25px"/>
                                                        <rich:spacer width="10px"/>
                                                        <h:outputText styleClass="text" value="Cadastre as formas de pagamento e operadoras de cartão que serão utilizados nos recebimentos. Ao realizar consultas as informações financeiras estarão detalhadas. É possível realizar estornos e verificar movimentos das contas dos clientes, créditos ou débitos, se houver."/>
                                                    </h:panelGroup>
                                                </h:panelGrid>

                                            </h:panelGrid>
                                        </h:panelGrid>
                                    </td>
                                    <td align="left" valign="top" background="${contexto}/images/box_centro_right.gif"><img src="${contexto}/images/shim.gif"></td>
                                </tr>
                                <tr>
                                    <td height="20" align="left" valign="top"><img src="${contexto}/images/box_centro_bottom_left.gif" width="19" height="20"></td>
                                    <td align="left" valign="top" background="${contexto}/images/box_centro_bottom.gif"><img src="${contexto}/images/shim.gif"></td>
                                    <td align="left" valign="top"><img src="${contexto}/images/box_centro_bottom_right.gif" width="19" height="20"></td>
                                </tr>
                            </table>
                        </h:panelGroup>
                        <jsp:include page="../includes/include_box_menulatcadastros.jsp" flush="true"/>
                    </h:panelGroup>
                </h:panelGroup>
            </h:panelGroup>

            <jsp:include page="../../../include_rodape_flat.jsp" flush="true" />
            <%@include file="../includes/include_focus.jsp" %>
            </h:panelGroup>
         </body>
        </html>
    </h:form>
</f:view>

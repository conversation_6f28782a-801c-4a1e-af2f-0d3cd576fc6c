<%@include file="../includes/include_imports.jsp" %>

<f:view>
    <jsp:include page="/includes/include_carregando_ripple.jsp" flush="true"/>
    <h:form id="form">
    	<html>
    	
		<!-- Inclui o elemento HEAD da p�gina -->
		<%@include file="/pages/ce/includes/include_head.jsp" %>
		
		<body>
        <table width="100%" height="100%" border="0" cellpadding="0" cellspacing="0">
        
            <%@include file="../includes/include_topo.jsp"%>
            
            <tr>
                <td align="left" valign="top" class="bglateral">
                    <table width="100%" height="100%" border="0" cellpadding="0" cellspacing="0">
                        <tr>
                            <td width="206" align="center" valign="top" class="bglateraltop" style="padding-top:6px;">
								<%@include file="../includes/include_box_menulatcadastroscontrato.jsp" %>
                            </td>
                            <td align="left" valign="top" style="padding:7px 15px 0 20px;">
                                <div style="clear:both;">
                                    <table width="98%" border="0" align="left" cellpadding="0" cellspacing="0" class="text" style="margin-right:30px;margin-bottom:20px;">
                                        <tr>
                                            <td width="19" height="50" align="left" valign="top"><img src="${contexto}/images/box_centro_top_left.gif" width="19" height="50"></td>
                                            <td align="left" valign="top" background="${contexto}/images/box_centro_top.gif" class="tituloboxcentro" style="padding:11px 0 0 0;">&Iacute;ndice de Renova&ccedil;&atilde;o do M&ecirc;s </td>
                                            <td width="19" align="left" valign="top"><img src="${contexto}/images/box_centro_top_right.gif" width="19" height="50"></td>
                                        </tr>
                                        <tr>
                                            <td align="left" valign="top" background="${contexto}/images/box_centro_left.gif"><img src="${contexto}/images/shim.gif"></td>
                                            <td align="left" valign="top" bgcolor="#ffffff" style="padding:15px 15px 5px 15px;">
                                                 <h:panelGrid columns="1" style="background-image:url('../../../imagens/backGroundTelaBasicoBack.png'); background-repeat:repeat-x;" width="100%" cellpadding="0" cellspacing="0">
                                                <h:panelGrid columnClasses="w33,w33,w33" style="background-image:url('../../../imagens/backGroundTelaBasico.png'); background-repeat: no-repeat;" columns="3" width="100%" cellpadding="0" cellspacing="0">


                                                    <h:panelGrid columns="1" style="height:100%;" width="100%" cellpadding="5" cellspacing="5" >
                                                        <h:panelGroup>
                                                            <rich:spacer height="5px"/>
                                                            <div><h:outputLink styleClass="linkWiki" style="background:url('#{contexto}/imagens/wiki_link2.gif') no-repeat scroll right center transparent;padding-right:9px;padding-top:15px;" value="#{SuperControle.urlWiki}Entidade:Colaborador" title="Clique e saiba mais: Colaborador" target="_blank"/><rich:spacer width="5px"/><a4j:commandLink onclick="abrirPopup('faces/colaboradorCons.jsp', 'Colaborador', 800, 595);"  value="#{CElabels['menu.cadastros.configContrato']}"/></div>
                                                            <rich:spacer height="25px"/>
                                                            <rich:spacer width="10px"/>
                                                            <h:outputText styleClass="text" value="Lorem ipsum dolor sit amet, consectetur adipiscing elit. Mauris non velit et ipsum scelerisque suscipit. Curabitur pulvinar varius arcu, in sagittis dui mollis a. Curabitur ac lacus in massa sollicitudin bibendum non id sem. Ut vitae enim justo. Donec nec diam sed nulla dignissim malesuada et a leo."/>
                                                        </h:panelGroup>
                                                        <h:panelGroup>
                                                            <rich:spacer height="25px"/>
                                                            <div><h:outputLink styleClass="linkWiki" style="background:url('#{contexto}/imagens/wiki_link2.gif') no-repeat scroll right center transparent;padding-right:9px;padding-top:15px;" value="#{SuperControle.urlWiki}Entidade:ContratoTextoPadrao" title="Clique e saiba mais: Modelo de Contrato" target="_blank"/><rich:spacer width="5px"/><a4j:commandLink onclick="abrirPopup('faces/colaboradorCons.jsp', 'PlanoTextoPadrao', 800, 595);"  value="#{CElabels['menu.cadastros.configContrato.modelo']}"/></div>
                                                            <rich:spacer height="25px"/>
                                                            <rich:spacer width="10px"/>
                                                            <h:outputText styleClass="text" value="Nesta tela de Cadastro voc� poder� definir como ser� o Contrato de Presta��o de Servi�os de sua Academia. Poder� tamb�m, definir como ser�o os Recibos de venda de produtos e servi�os. Utilize o editor para criar seus layouts de acordo com suas necessidades."/>
                                                        </h:panelGroup>
                                                    </h:panelGrid>
                                                    <h:panelGrid style="height:100;%" columns="1" width="100%" cellpadding="5" cellspacing="5" >
                                                        <h:panelGroup>
                                                            <div><h:outputLink styleClass="linkWiki" style="background:url('#{contexto}/imagens/wiki_link2.gif') no-repeat scroll right center transparent;padding-right:9px;padding-top:15px;" value="#{SuperControle.urlWiki}Entidade:JustificativaOperacao" title="Clique e saiba mais: Justificativa de Opera��o" target="_blank"/><rich:spacer width="5px"/><a4j:commandLink onclick="abrirPopup('faces/colaboradorCons.jsp', 'JustificativaOperacao', 800, 595);"  value="#{CElabels['menu.cadastros.configContrato.justOperacao']}"/> </div>
                                                            <rich:spacer height="25px"/>
                                                            <rich:spacer width="10px"/>
                                                            <h:outputText styleClass="text" value="Os procedimentos de Lan�amento de B�nus, Atestado, Cancelamento, Trancamento e Troca de Planos, necessitam de lan�amento de justificativas, a fim de se mapear os motivos pelos quais os Alunos se afastam ou abandonam a atividade f�sica, mas, para conseguirmos tal n�vel de acompanhamento, precisamos cadastrar as Justificativas mais comuns atrav�s desta tela de cadastro."/>
                                                        </h:panelGroup>
                                                        <h:panelGroup>
                                                            <rich:spacer height="25px"/>
                                                            <div><h:outputLink styleClass="linkWiki" style="background:url('#{contexto}/imagens/wiki_link2.gif') no-repeat scroll right center transparent;padding-right:9px;padding-top:15px;" value="#{SuperControle.urlWiki}Entidade:Colaborador" title="Clique e saiba mais: Colaborador" target="_blank"/><rich:spacer width="5px"/><a4j:commandLink onclick="abrirPopup('faces/colaboradorCons.jsp', 'Colaborador', 800, 595);"  value="#{CElabels['menu.cadastros.configContrato.tabelaFeriados']}"/></div>
                                                            <rich:spacer height="25px"/>
                                                            <rich:spacer width="10px"/>
                                                            <h:outputText styleClass="text" value="Lorem ipsum dolor sit amet, consectetur adipiscing elit. Mauris non velit et ipsum scelerisque suscipit. Curabitur pulvinar varius arcu, in sagittis dui mollis a. Curabitur ac lacus in massa sollicitudin bibendum non id sem. Ut vitae enim justo. Donec nec diam sed nulla dignissim malesuada et a leo."/>
                                                        </h:panelGroup>
                                                    </h:panelGrid>
                                                    <h:panelGrid columns="1" width="100%" style="height:100%;" cellpadding="5" cellspacing="5" >
                                                        <h:panelGroup>
                                                            <div><img src="${contexto}/imagens/cadastros.png"></div>
                                                        </h:panelGroup>
                                                    </h:panelGrid>

                                                </h:panelGrid>
                                            </h:panelGrid>
                                            </td>
                                            <td align="left" valign="top" background="${contexto}/images/box_centro_right.gif"><img src="${contexto}/images/shim.gif"></td>
                                        </tr>
                                        <tr>
                                            <td height="20" align="left" valign="top"><img src="${contexto}/images/box_centro_bottom_left.gif" width="19" height="20"></td>
                                            <td align="left" valign="top" background="${contexto}/images/box_centro_bottom.gif"><img src="${contexto}/images/shim.gif"></td>
                                            <td align="left" valign="top"><img src="${contexto}/images/box_centro_bottom_right.gif" width="19" height="20"></td>
                                        </tr>
                                    </table>
                                </div>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td height="93" align="left" valign="top" class="bgrodape"><jsp:include page="/include_rodape.jsp" flush="true" /></td>
            </tr>
        </table>
        </body>
        </html>
    </h:form>
</f:view>
<%@include file="../includes/include_imports.jsp" %>
<f:view>
    <jsp:include page="/includes/include_carregando_ripple.jsp" flush="true"/>
    <html>
       	<!-- INCLUI O ELEMENTO HEAD NA P�GINA -->
        <%@include file="../includes/include_head_form.jsp" %>
        <title>
            <h:outputText value="#{CElabels['menu.cadastros.perfisEventos.tipoAmbiente']}"/>
        </title>

        <style>
            .obrigatorio {
                color: #DDDD00;
            }
            .mensagemObrigatorio {
                color: #FF0000;
            }
        </style>
        <script >
            //Script de valida��o do formul�rio
            function exibirMensagem(mensagem, id) {
                var divObgMensagem = document.getElementById('divObg-' + id);
                divObgMensagem.innerHTML = mensagem;
            }
            function limparMensagem(id) {
                var divObgMensagem = document.getElementById('divObg-' + id);
                divObgMensagem.innerHTML = "";
            }
            function validar() {
                limparMensagem('descricao');
                limparMensagem('duracaoMinimaHrs');
                limparMensagem('qtdMaximaReservasDia');
                limparMensagem('tempoAdicionalPosteriorMin');
                limparMensagem('horarioInicial');
                limparMensagem('horarioFinal');
				
                //Obtem os campos que ser�o validados
                var descricao = document.getElementById('form:descricao');
                var duracao = document.getElementById('form:duracaoMinimaHrs');
                var quantMax = document.getElementById('form:qtdMaximaReservasDia');
                var tempoAdicional = document.getElementById('form:tempoAdicionalPosteriorMin');
                var horarioInicio = document.getElementById('form:horarioInicial');
                var horarioFim = document.getElementById('form:horarioFinal');
			
                //Verifica se est�o preenchidos
                var validade = true;
                if (descricao == null || descricao.value == null || descricao.value == "") {
                    exibirMensagem('<h:outputText value="O campo Descri��o deve ser preenchido"/>', 'descricao');
                    validade = false;
                }
                if (duracao == null || duracao.value == null || duracao.value == "") {
                    exibirMensagem('<h:outputText value="O campo Dura��o deve ser preenchido"/>', 'duracaoMinimaHrs');
                    validade = false;
                }
                if (quantMax == null || quantMax.value == null || quantMax.value == "") {
                    exibirMensagem('<h:outputText value="O campo Quantidade deve ser preenchido"/>', 'qtdMaximaReservasDia');
                    validade = false;
                }
                if (tempoAdicional == null || tempoAdicional.value == null || tempoAdicional.value == "") {
                    exibirMensagem('<h:outputText value="O campo Tempo Adicional deve ser preenchido"/>', 'tempoAdicionalPosteriorMin');
                    validade = false;
                }
                if (horarioInicio == null || horarioInicio.value == null || horarioInicio.value == "") {
                    exibirMensagem('<h:outputText value="O campo Hor�rio Inicial deve ser preenchido"/>', 'horarioInicial');
                    validade = false;
                }
                if (horarioFim == null || horarioFim.value == null || horarioFim.value == "") {
                    exibirMensagem('<h:outputText value="O campo Hor�rio Final deve ser preenchido"/>', 'horarioFinal');
                    validade = false;
                }
				
                return validade;
            }
            function validarHorarioFinal() {
                limparMensagem('terminoDiaPosterior');
   	   			
                var horarioInicial = document.getElementById('form:horarioInicial');
                var horarioFinal = document.getElementById('form:horarioFinal');
                if(horarioInicial.value >= horarioFinal.value){
                    exibirMensagem('<h:outputText value="#{Mensagens[\'parametros.horaFinalMenorhoraInicial\']}"/>', 'terminoDiaPosterior');
                }
                if (horarioInicial != null && horarioInicial.value != null && horarioFinal != null && horarioFinal.value != null) {
                    if (horarioInicial.value.length == 5 && horarioFinal.value.length == 5) {
                        if (horarioFinal.value == "00:00") {
                            horarioFinal.value = "23:59";
                        } else {
                            var horIn = horarioInicial.value.replace(":", "");
                            var horFin = horarioFinal.value.replace(":", "");
                            horIn = parseInt(horIn, 10);
                            horFin = parseInt(horFin, 10);
                            if (horFin < horIn) {
                                if (horFin > 600) {
                                    horarioFinal.value = "06:00";
                                }
                                exibirMensagem('<h:outputText value="#{Mensagens[\'dados.horario.evento.terminoDiaPosterior\']}"/>', 'terminoDiaPosterior');
                            }
                        }
                    } else {
                        if (horarioInicial.value.length != 5)
                            horarioInicial.value = "";
                        if (horarioFinal.value.length != 5)
                            horarioFinal.value = "";
                    }
                }
   			
            }
            function formataHora(pStr, pFmt) {
                var reTime2 = /^([0-1]\d|2[0-3]):[0-5]\d$/;
                eval("reTime = reTime" + pFmt);
                if (reTime.test(pStr)) {
                    return true;
                } else if (pStr != null && pStr != "") {
                    return false;
                }
            }
        </script>
        <body>
        <c:set var="titulo" scope="session" value="Tipo Ambiente"/>
        <c:set var="urlWiki" scope="session" value="${SuperControle.urlWikiCE}Cadastros:PP:Tipo_Ambiente"/>

        <h:panelGroup layout="block" styleClass="pure-g-r">
            <f:facet name="header">
                <jsp:include page="../../../topoReduzido_material.jsp"/>
            </f:facet>
        </h:panelGroup>

            <!-- FORMUL�RIO DE CADASTRO -->
            <h:form id="form">

                <!-- MANTEM O CONTROLLER NA �RVORE DE COMPONENTES -->
                <a4j:keepAlive beanName="TipoAmbienteControle" />
                <h:panelGrid columns="1" width="100%" >
                    <h:panelGrid columns="1" style="height:25px; background-image:url('../../../imagens/fundoBarraTopo.png');background-repeat: repeat-x;" columnClasses="colunaCentralizada" width="100%">
                        <h:outputText styleClass="tituloFormulario" value="#{CElabels['menu.cadastros.perfisEventos.tipoAmbiente']}">
                        <h:outputLink value="#{SuperControle.urlWikiCE}Cadastros:PP:Tipo_Ambiente"
                                      title="Clique e saiba mais: Tipo Ambiente" target="_blank" >
                            <h:graphicImage styleClass="linkWiki" url="/imagens/wiki_bco.gif"/>
                        </h:outputLink>
                    </h:outputText>
                    </h:panelGrid>
                </h:panelGrid>

                <!-- CAMPOS PARA PREENCHIMENTO -->
                <h:panelGrid rowClasses="linhaImpar, linhaPar" columnClasses="classEsquerda, classDireita" style="background-image:url('./imagens/backGroundTelaBasico.png'); background-repeat: no-repeat;padding: 8px;" width="100%" columns="2">
                    <h:panelGroup>
                        <%@include file="../includes/include_obrigatorio.jsp" %>
                        <h:outputText styleClass="tituloCampos" value="#{CElabels['entidade.descricao']}:" />
                    </h:panelGroup>
                    <h:panelGroup>
                        <h:inputText onblur="blurinput(this); limparMsgObrig('form:descricao', 'descricao');" onfocus="focusinput(this);"
                                     styleClass="form" size="30" maxlength="30"
                                     style="border: 1px solid #8eb3c3"
                                     value="#{TipoAmbienteControle.tipoAmbiente.descricao}" id="descricao"></h:inputText>
                        <div id="divObg-descricao" class="mensagemObrigatorio"></div>
                    </h:panelGroup>
                    <h:panelGroup>
                        <%@include file="../includes/include_obrigatorio.jsp" %>
                        <h:outputText styleClass="tituloCampos" value="#{CElabels['entidade.duracaoMinimaHrs']}:" />
                    </h:panelGroup>
                    <h:panelGroup>
                        <h:inputText rendered="#{!TipoAmbienteControle.existeNegociacao}" onblur="blurinput(this); limparMsgObrig('form:duracaoMinimaHrs', 'duracaoMinimaHrs');" onfocus="focusinput(this);"
                                     styleClass="form" size="7" maxlength="2"
                                     style="border: 1px solid #8eb3c3"
                                     onkeypress="return mascara(this, '999', event);"
                                     value="#{TipoAmbienteControle.tipoAmbiente.duracaoMinimaHrs}" id="duracaoMinimaHrs">&nbsp;</h:inputText>&nbsp;<h:outputText styleClass="form" value="(Horas)"/>
                        <div id="divObg-duracaoMinimaHrs" class="mensagemObrigatorio"></div>
                        <h:outputText rendered="#{TipoAmbienteControle.existeNegociacao}"
                                      styleClass="form" value="#{TipoAmbienteControle.tipoAmbiente.duracaoMinimaHrs}" id="duracaoMinimaHrs1" />
                    </h:panelGroup>

                    <h:panelGroup>
                        <%@include file="../includes/include_obrigatorio.jsp" %>
                        <h:outputText styleClass="tituloCampos" value="#{CElabels['entidade.qtdMaximaReservasDia']}:" />
                    </h:panelGroup>
                    <h:panelGroup>
                        <h:inputText rendered="#{!TipoAmbienteControle.existeNegociacao}" onblur="blurinput(this); limparMsgObrig('form:qtdMaximaReservasDia', 'qtdMaximaReservasDia');" onfocus="focusinput(this);"
                                     styleClass="form" size="2" maxlength="2"
                                     style="border: 1px solid #8eb3c3"
                                     onkeypress="return mascara(this, '99', event);"
                                     value="#{TipoAmbienteControle.tipoAmbiente.qtdMaximaReservasDia}" id="qtdMaximaReservasDia"></h:inputText>&nbsp;<h:outputText styleClass="form" value="(M�ximo de Rervesas por Dia)"/>
                        <div id="divObg-qtdMaximaReservasDia" class="mensagemObrigatorio"></div>
                        <h:outputText rendered="#{TipoAmbienteControle.existeNegociacao}"
                                      styleClass="form" value="#{TipoAmbienteControle.tipoAmbiente.qtdMaximaReservasDia}" id="qtdMaximaReservasDia1" />
                    </h:panelGroup>

                    <h:panelGroup>
                        <%@include file="../includes/include_obrigatorio.jsp" %>
                        <h:outputText styleClass="tituloCampos" value="#{CElabels['entidade.tempoAdicionalPosteriorMin']}:" />
                    </h:panelGroup>
                    <h:panelGroup>
                        <h:inputText rendered="#{!TipoAmbienteControle.existeNegociacao}" styleClass="form" size="5" maxlength="5" onblur="blurinput(this); limparMsgObrig('form:tempoAdicionalPosteriorMin', 'tempoAdicionalPosteriorMin');"
                                     style="border: 1px solid #8eb3c3"
                                     value="#{TipoAmbienteControle.tipoAmbiente.tempoAdicionalPosteriorMin}"
                                     onkeypress="return mascara(this, '99999', event);"
                                     id="tempoAdicionalPosteriorMin" />&nbsp;<h:outputText styleClass="form" value="(Minutos)"/>
                        <div id="divObg-tempoAdicionalPosteriorMin" class="mensagemObrigatorio"></div>
                        <h:outputText rendered="#{TipoAmbienteControle.existeNegociacao}" styleClass="form" value="#{TipoAmbienteControle.tipoAmbiente.tempoAdicionalPosteriorMin}" id="tempoAdicionalPosteriorMin1" />
                    </h:panelGroup>

                    <h:panelGroup>
                        <%@include file="../includes/include_obrigatorio.jsp" %>
                        <h:outputText styleClass="tituloCampos" value="#{CElabels['entidade.horario.inicial']}:" />
                    </h:panelGroup>
                    <h:panelGroup>
                        <h:inputText rendered="#{!TipoAmbienteControle.existeNegociacao}"
                                     styleClass="form" size="7" maxlength="25" onblur="blurinput(this); limparMsgObrig('form:horarioInicial', 'horarioInicial');" onfocus="focusinput(this);"
                                     style="border: 1px solid #8eb3c3"
                                     value="#{TipoAmbienteControle.tipoAmbiente.horarioInicialString}"
                                     onkeypress="return mascara(this, '99:99', event);"
                                     id="horarioInicial" />
                        <div id="divObg-horarioInicial" class="mensagemObrigatorio"></div>
                        <h:outputText rendered="#{TipoAmbienteControle.existeNegociacao}"
                                      styleClass="form" value="#{TipoAmbienteControle.tipoAmbiente.horarioInicialString}" id="horarioInicial1" />
                    </h:panelGroup>

                    <h:panelGroup>
                        <%@include file="../includes/include_obrigatorio.jsp" %>
                        <h:outputText styleClass="tituloCampos" value="#{CElabels['entidade.horario.final']}:" />
                    </h:panelGroup>
                    <h:panelGroup>
                        <h:inputText rendered="#{!TipoAmbienteControle.existeNegociacao}"
                                     styleClass="form" size="7" maxlength="25" onblur="blurinput(this); limparMsgObrig('form:horarioFinal', 'horarioFinal');" onfocus="focusinput(this);"
                                     style="border: 1px solid #8eb3c3"
                                     value="#{TipoAmbienteControle.tipoAmbiente.horarioFinalExibicaoString}"
                                     onkeypress="return mascara(this, '99:99', event);"
                                     id="horarioFinal" />
                        <div id="divObg-horarioFinal" class="mensagemObrigatorio"></div>
                        <div id="divObg-terminoDiaPosterior" class="mensagemObrigatorio"></div>
                        <h:outputText rendered="#{TipoAmbienteControle.existeNegociacao}" styleClass="form" value="#{TipoAmbienteControle.tipoAmbiente.horarioFinalString}" id="horarioFinal1" />
                    </h:panelGroup>
                </h:panelGrid>

                <!-- PAINEL DE EXIBI��O DE MENSAGENS E A��ES -->
                <h:panelGrid id="mensagens" columns="1" width="100%" styleClass="tabMensagens">

                    <h:panelGrid columns="3" width="100%" styleClass="tabMensagens">
                        <h:panelGrid columns="1" width="100%">

                            <h:outputText value=" "/>

                        </h:panelGrid>

                        <!-- MENSAGENS -->
                        <h:commandButton rendered="#{PerfilEventoControle.sucesso}" image="/imagens/bt_sucesso.png"/>
                        <h:commandButton rendered="#{PerfilEventoControle.erro}" image="/imagens/erro.png"/>
                        <h:panelGrid columns="1" width="100%">
                            <h:outputText styleClass="mensagem" value="#{TipoAmbienteControle.mensagem}"/>
                            <h:outputText styleClass="mensagemDetalhada" value="#{TipoAmbienteControle.mensagemDetalhada}"/>
                        </h:panelGrid>
                    </h:panelGrid>

                    <!-- A��ES -->
                    <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                        <h:panelGroup>
                            <!-- BOT�O QUE SALVA UM NOVO OBJETO -->
                            <h:commandButton id="salvar" 
                                             rendered="#{!TipoAmbienteControle.existeNegociacao}"
                                             action="#{TipoAmbienteControle.salvar}" value="#{CElabels['operacoes.gravar']}"
                                             onclick="if(!validar()){return false;};"
                                             alt="#{CElabels['operacoes.gravar.dados']}"
                                             styleClass="botoes nvoBt"
                                             actionListener="#{TipoAmbienteControle.autorizacao}">
                                <!-- Entidade.tipo ambiente -->
                                <f:attribute name="entidade" value="106" />
                                <!-- Operacao.gravar -->
                                <f:attribute name="operacao" value="G" />
                            </h:commandButton>


                                <h:commandButton id="salvar2"
                                             rendered="#{TipoAmbienteControle.existeNegociacao}"
                                             action="#{TipoAmbienteControle.salvar}" value="#{CElabels['operacoes.gravar']}"
                                             alt="#{CElabels['operacoes.gravar.dados']}"
                                             styleClass="botoes nvoBt"
                                             actionListener="#{TipoAmbienteControle.autorizacao}">
                                <!-- Entidade.tipo ambiente -->
                                <f:attribute name="entidade" value="106" />
                                <!-- Operacao.gravar -->
                                <f:attribute name="operacao" value="G" />
                            </h:commandButton>

                            <h:outputText value="    " />


                            <h:panelGroup rendered="#{TipoAmbienteControle.tipoAmbiente.codigo gt 0}">
                                <a4j:commandButton id="excluir"
                                                   action="#{TipoAmbienteControle.confirmarExcluir}"
                                                   oncomplete="#{TipoAmbienteControle.msgAlert}"
                                                   styleClass="botoes nvoBt btSec btPerigo" reRender="form"
                                                   actionListener="#{TipoAmbienteControle.autorizacao}"
                                                   value="#{msg_bt.btn_excluir}" title="#{msg.msg_excluir_dados}" accesskey="3">
                                    <!-- Entidade.fornecedor -->
                                    <f:attribute name="entidade" value="106"/>
                                    <!-- Operacao.consultar -->
                                    <f:attribute name="operacao" value="E"/>
                                </a4j:commandButton>

                                <h:outputText value="    " />

                            </h:panelGroup>
                            <!-- BOT�O QUE CHAMA O M�TODO DE CONSULTA -->
                            <h:commandButton id="consultar" immediate="true" action="#{NavegacaoControle.abrirTelaTipoAmbiente}"
                                             value="#{CElabels['operacoes.consulta.consultar']}"
                                             alt="#{CElabels['operacoes.consulta.consultarDados']}"
                                             styleClass="botoes nvoBt btSec" actionListener="#{TipoAmbienteControle.autorizacao}">
                                <!-- Entidade.tipo ambiente -->
                                <f:attribute name="entidade" value="106" />
                                <!-- Operacao.consultar -->
                                <f:attribute name="operacao" value="C" />
                            </h:commandButton>


                        </h:panelGroup>
                    </h:panelGrid>

                </h:panelGrid>
            </h:form>
        </body>
    </html>
    <%@include file="../../../includes/include_modal_mensagem_generica.jsp"%>
</f:view>
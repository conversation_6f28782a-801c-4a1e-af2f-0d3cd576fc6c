<%@page contentType="text/html" pageEncoding="ISO-8859-1" %>
<%@include file="../includes/include_imports.jsp" %>

<f:view>
    <jsp:include page="/includes/include_carregando_ripple.jsp" flush="true"/>
    <h:form id="form">
    	<html>
    			
		<!-- <PERSON><PERSON><PERSON> o elemento HEAD da página -->
		<%@include file="/pages/ce/includes/include_head.jsp" %>
		
		<script>
		function limparMsgObrigTextoPadrao() {
				var textoPadrao = document.getElementById('form:textoPadraoTextArea_ifr').contentWindow.document.body.firstChild.innerHTML;
   			if (textoPadrao != null && textoPadrao != '<br mce_bogus="1">') {
   				limparMensagem('textoPadrao');
			}
			}
			function validarDisponibilidade(){
				limparMensagem('dataConsultaDispon');
				limparMensagem('ambientes');
				
	
				var data = document.getElementById('form:dataConsultaDisponInputDate');
				var ambiente = document.getElementById('form:ambientes');
				
				
				var validade = true;
	
				if (data == null || data.value == null || data.value == "") {
						exibirMensagem('<h:outputText value="#{Mensagens[\'campoObrigatorio.data\']}"/>', 'dataConsultaDispon');
						validade = false;
				}
				if (ambiente == 0 || ambiente.value == 0) {
						exibirMensagem('<h:outputText value="#{Mensagens[\'campoObrigatorio.ambiente\']}"/>', 'ambientes');
						validade = false;
				}
		
				return validade;
			}

			function validarDetalheCliente(){
				
				limparMensagem('pessoa');
	
				
				var detalheCliente=document.getElementById('form:codigoPessoa');
				
				var validade = true;
	
				if (detalheCliente == null || detalheCliente.value == null || detalheCliente.value == "") {
						exibirMensagem('<h:outputText value="#{Mensagens[\'campoObrigatorio.nomeCliente\']}"/>', 'pessoa');
						validade = false;
				}
						
				return validade;
			}

			function validarDetalheEvento() {
				
				limparMensagem('evento');
	
				
				var evento = document.getElementById('form:eventoInteresse');
				
				var validade = true;
	
				if (evento == null || evento.value == null || evento.value == "") {
						exibirMensagem('<h:outputText value="#{Mensagens[\'campoObrigatorio.evento\']}"/>', 'evento');
						validade = false;
				}
						
				return validade;
			}

			function validar() {
				
				limparMensagem('nomeCliente');
				limparMensagem('formaContato');
				limparMensagem('textoPadrao');
				
		   			
				
				var nomeCliente = document.getElementById('form:nomeCliente');
				var formaContato = document.getElementById('form:formaContato');
				var textoPadrao = document.getElementById('form:textoPadraoTextArea_ifr').contentWindow.document.body.firstChild.innerHTML;
				var telefoneCelular = document.getElementById('form:telefoneCelular');
				var telefoneComercial = document.getElementById('form:telefoneComercial');
				var telefoneFixo = document.getElementById('form:telefoneFixo');
				var data = document.getElementById('form:dataProximoContatoInputDate');

				
				var validade = true;
				textoPadrao = textoPadrao.replace(/(<[^>]*>)|(&nbsp;)+/g,'');
				
				if (nomeCliente == null || nomeCliente.value == null || nomeCliente.value == '') {
					exibirMensagem('<h:outputText value="#{Mensagens[\'campoObrigatorio.nomeCliente\']}"/>', 'nomeCliente');
					validade = false;
				
				}
				if (formaContato == null || formaContato.value == null || formaContato.value == "") {
					exibirMensagem(montarMsgObrigatoriedade('<h:outputText value="#{CElabels[\'entidade.formaContato\']}"/>'), 'formaContato');
					validade = false;
				
				}
				if (textoPadrao == null || textoPadrao == '<br mce_bogus="1">' || textoPadrao=='') {
					exibirMensagem('<h:outputText value="#{Mensagens[\'campoObrigatorio.textoPadrao\']}"/>', 'textoPadrao');
					validade = false;
				
				}
				if (telefoneCelular == null || telefoneCelular.value == null || telefoneCelular.value == "") {
					if (telefoneFixo == null || telefoneFixo.value == null || telefoneFixo.value == "") {
						if (telefoneComercial == null || telefoneComercial.value == null || telefoneComercial.value == "") {
								exibirMensagem('<h:outputText value="#{Mensagens[\'campoObrigatorio.telefone\']}"/>', 'telefoneFixo');
								exibirMensagem('<h:outputText value="#{Mensagens[\'campoObrigatorio.telefone\']}"/>', 'telefoneCelular');	
								exibirMensagem('<h:outputText value="#{Mensagens[\'campoObrigatorio.telefone\']}"/>', 'telefoneComercial');
								validade = false;
					}
				  }		
				}
				if (data == null || data.value == null || data.value == "") {
					if(validade){
						if(!confirm('Deseja registrar essa conversa sem uma data para um próximo contato?'))
						{
							validade = false;
						}
					}
				}
				
				return validade;
			}
			

			function ativarAjuda(id) {
				if (typeof id == "string") {
					var hint = document.getElementById('hint-' + id);
					hint.style.visibility = hint.style.visibility == 'hidden' ? 'visible' : 'hidden';
				} else {
					for (var i = 0; i < id.length; i++) {
						var hint = document.getElementById('hint-' + id[i]);
						hint.style.visibility = hint.style.visibility == 'hidden' ? 'visible' : 'hidden';
					}
				}
			}
			
				

		</script>
        <a4j:keepAlive beanName="ConversaControle" />
		<body>
        <h:panelGroup layout="block" style="display: inline-table;width: 100%;" styleClass="fundoCinza">
            <h:panelGroup layout="block" styleClass="bgtop topoZW" rendered="#{MenuControle.apresentarTopo}">
                <jsp:include page="../../../include_topo_2.0.jsp" flush="true"/>
                <jsp:include page="../../../include_menu_ce_flat.jsp" flush="true"/>
            </h:panelGroup>

            <h:panelGroup layout="block" styleClass="caixaCorpo">
                <h:panelGroup layout="block" style="height: 80%;width: 100%">
                    <h:panelGroup layout="block" styleClass="caixaMenuLatel">
                        <h:panelGroup layout="block" styleClass="container-imagem container-conteudo-central">
                            <h:panelGroup layout="block" styleClass="container-box zw_ui especial ">
                                <h:panelGroup styleClass="container-box-header" layout="block">
                                    <h:panelGroup layout="block" styleClass="margin-box">
                                        <h:outputText styleClass="container-header-titulo" value="#{CElabels['menu.operacoesCE.cadastroConversa']}"/>
                                        <h:outputLink styleClass="linkWiki"
                                                      value="#{SuperControle.urlWikiCE}Conversas:Conversas"
                                                      title="Clique e saiba mais: Conversas"
                                                      target="_blank">
                                            <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                                        </h:outputLink>
                                    </h:panelGroup>
                                </h:panelGroup>
                                <h:panelGroup layout="block" styleClass="margin-box">
                                    <%@include file="/pages/ce/includes/include_consultaConversas.jsp" %>

                                </h:panelGroup>
                            </h:panelGroup>
                        </h:panelGroup>
                        <jsp:include page="../includes/include_box_menulateral.jsp" flush="true"/>
                    </h:panelGroup>
                </h:panelGroup>
            </h:panelGroup>

            <jsp:include page="../../../include_rodape_flat.jsp" flush="true" />
            <rich:jQuery id="mskData" selector=".rich-calendar-input" timing="onload" query="mask('99/99/9999')" />
        </h:panelGroup>
        
        <%@include file="/pages/ce/includes/include_focus.jsp" %>

        </body>
        </html>
    </h:form>
    	<%@include file="/pages/ce/includes/include_modal_verificarDisponibilidades.jsp" %>
        <%@include file="/pages/ce/includes/include_modal_existeConversas.jsp" %>
        <%@include file="/pages/ce/includes/include_modal_detalheEventoConversa.jsp" %>
        <%@include file="/pages/ce/includes/include_modal_usuarioSenha_Conversas.jsp" %>
        <%@include file="../../../includes/include_modal_mensagem_generica.jsp"%>
</f:view>

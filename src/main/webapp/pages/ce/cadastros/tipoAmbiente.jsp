<%@page contentType="text/html;charset=ISO-8859-1" %>
<head><script type="text/javascript" language="javascript" src="${contexto}/script/script.js"></script></head>

<script type="text/javascript" language="javascript" src="${contexto}/hoverform.js"></script>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<meta name="viewport" content="width=device-width, initial-scale=1.0">

<script>
    jQuery.noConflict();
    document.getElementById("form:valorConsulta").focus();
</script>
<c:set var="contexto" value="${pageContext.request.contextPath}" scope="request" />
<%@include file="/includes/verificaModulo.jsp" %>


<f:view>
    <jsp:include page="/includes/include_carregando_ripple.jsp" flush="true"/>
    <link href="${contexto}/css/otimize.css" rel="stylesheet" type="text/css">
    <link href="${contexto}/beta/css/pure-min.css" type="text/css" rel="stylesheet"/>
    <link href="${contexto}/beta/css/bootstrap-tabs.css" type="text/css" rel="stylesheet"/>
    <link href="${contexto}/beta/css/pure-ext.css" type="text/css" rel="stylesheet"/>
    <link href="${contexto}/beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
    <script src="${contexto}/beta/js/jquery.js" type="text/javascript"></script>
    <script src="${contexto}/beta/js/jquery.dataTables.min.js" type="text/javascript"></script>
    <script src="${contexto}/beta/js/DT_bootstrap.js" type="text/javascript"></script>
    <script src="${contexto}/beta/js/bootstrap-tab.js" type="text/javascript"></script>
    <title>${msg_aplic.prt_TipoAmbiente_CE}</title>

    <%-- INICIO HEADER --%>
    <%
            pageContext.setAttribute("modulo","centralEventos");
    %>
    <c:set var="titulo" scope="session" value="Tipo Ambiente"/>
    <c:set var="urlWiki" scope="session" value="${SuperControle.urlWikiCE}Cadastros:PP:Tipo_Ambiente"/>

    <h:panelGroup layout="block" styleClass="pure-g-r">
        <f:facet name="header">
            <jsp:include page="../../../topoReduzido_material.jsp"/>
        </f:facet>
    </h:panelGroup>
    <%-- FIM HEADER --%>

    <h:panelGroup layout="block" styleClass="pure-g-r">
        <h:form id="form" styleClass="pure-form pure-u-1">
            <a4j:keepAlive beanName="ExportadorListaControle"/>
            <input type="hidden" value="${modulo}" name="modulo"/>
            <input id="chavePrimaria" type="hidden" value="" name="chavePrimaria"/>

            <%-- INICIO CONTENT --%>
            <h:panelGroup>

                <%-- INICIO COMANDOS --%>
                <h:panelGroup layout="block" styleClass="pure-g-r pure-u-11-12 margin-0-auto margin-v-10">
                    <h:panelGroup layout="block" styleClass="pure-form pure-form-aligned pure-u-2-3">
                        <a4j:commandLink id="btnExcel"
                                         styleClass="pure-button pure-button-small"
                                         actionListener="#{TipoAmbienteControle.exportar}"
                                         oncomplete="abrirPopup('../../../UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/vnd.ms-excel','Transacoes', 800,200);#{ExportadorListaControle.msgAlert}"
                                         accesskey="3">
                            <f:attribute name="tipo" value="xls"/>
                            <f:attribute name="atributos" value="codigo=Código,descricao=Descrição,qtdMaximaReservasDia=Reservas/dia(Máximo),tempoAdicionalPosteriorMin=Adicional,horarioInicial=Inicial,horarioFinal=Final"/>
                            <f:attribute name="prefixo" value="TipoAmbiente"/>
                            <f:attribute name="titulo" value="Tipo Ambiente"/>
                            <a4j:actionparam noEscape="true" name="paramsTableFiltrada" value="retornaDadosDT()"/>
                            <i class="fa fa-file-excel-o" ></i> &nbsp Excel
                        </a4j:commandLink>

                        <a4j:commandLink id="btnPDF"
                                         styleClass="pure-button pure-button-small margin-h-10"
                                         actionListener="#{TipoAmbienteControle.exportar}"
                                         oncomplete="abrirPopup('../../../UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/pdf','Transacoes', 800,200);#{ExportadorListaControle.msgAlert}"
                                         accesskey="4">
                            <f:attribute name="tipo" value="pdf"/>
                            <f:attribute name="atributos" value="codigo=Código,descricao=Descrição,qtdMaximaReservasDia=Reservas/dia(Máximo),tempoAdicionalPosteriorMin=Adicional,horarioInicial=Inicial,horarioFinal=Final"/>
                            <f:attribute name="prefixo" value="TipoAmbiente"/>
                            <f:attribute name="titulo" value="Tipo Ambiente"/>
                            <a4j:actionparam noEscape="true" name="paramsTableFiltrada" value="retornaDadosDT()"/>
                            <i class="fa fa-file-pdf-o" ></i> &nbsp PDF
                        </a4j:commandLink>

                        <c:if test="${modulo eq 'centralEventos'}">
                            <a4j:commandLink id="btnLog"
                                             styleClass="pure-button pure-button-small"
                                             reRender="formLog"
                                             actionListener="#{LogControle.entidadeListener}"
                                             oncomplete="Richfaces.showModalPanel('panelMasterLog');">
                                <f:attribute name="nomeEntidade" value="TIPO AMBIENTE"/>
                                <f:attribute name="funcao" value="118"/>

                                <i class="fa-icon-list" ></i> &nbsp Log
                            </a4j:commandLink>
                        </c:if>
                    </h:panelGroup>

                    <h:panelGroup layout="block" styleClass="pure-u-1-3 text-right">
                        <a4j:commandLink id="btnNovo"
                                         styleClass="pure-button pure-button-primary pure-button-small"
                                         action="#{TipoAmbienteControle.novo}"
                                         accesskey="1">
                            <i class="fa fa-plus" ></i> &nbsp ${msg_bt.btn_novo}
                        </a4j:commandLink>
                    </h:panelGroup>
                </h:panelGroup>
                <%-- FIM COMANDOS --%>

                <table id="tblTipoAmbiente" class="tabelaTipoAmbiente pure-g-r pure-u-11-12 margin-0-auto">
                    <thead>
                    <th>${msg_aplic.prt_TipoAmbiente_CE_codigo}</th>
                    <th>${msg_aplic.prt_TipoAmbiente_CE_Descricao}</th>
                    <th>${msg_aplic.prt_TipoAmbiente_CE_Reservas}</th>
                    <th>${msg_aplic.prt_TipoAmbiente_CE_Adicional}</th>
                    <th>${msg_aplic.prt_TipoAmbiente_CE_HoraInicial}</th>
                    <th>${msg_aplic.prt_TipoAmbiente_CE_HoraFinal}</th>
                    </thead>
                    <tbody></tbody>
                </table>

                <a4j:jsFunction name="jsEditar" action="#{TipoAmbienteControle.editar}" reRender="mensagem"/>

            </h:panelGroup>
            <%-- FIM CONTENT --%>

            <%-- INICIO FOOTER --%>
            <h:panelGroup id="mensagem" layout="block" styleClass="pure-u-11-12 margin-0-auto">
                <h:graphicImage id="iconSucesso" rendered="#{TipoAmbienteControle.sucesso}" value="./imagens/sucesso.png" />
                <h:graphicImage id="iconErro" rendered="#{TipoAmbienteControle.erro}" value="./imagens/erro.png" />

                <h:outputText styleClass="mensagem" rendered="#{not empty TipoAmbienteControle.mensagem}" value=" #{TipoAmbienteControle.mensagem}"/>
                <h:outputText styleClass="mensagemDetalhada" rendered="#{not empty TipoAmbienteControle.mensagemDetalhada}" value=" #{TipoAmbienteControle.mensagemDetalhada}"/>
            </h:panelGroup>
            <%-- FIM FOOTER --%>
        </h:form>

        <rich:modalPanel id="panelStatus" autosized="true">
            <h:panelGrid columns="2" styleClass="titulo3" columnClasses="titulo3">
                <h:graphicImage url="/imagens/carregando.gif" style="border:none"/>
                <h:outputText styleClass="titulo3" value="Carregando..."/>
            </h:panelGrid>
        </rich:modalPanel>
        <a4j:status onstart="Richfaces.showModalPanel('panelStatus');"
                    onstop="Richfaces.hideModalPanel('panelStatus');">
        </a4j:status>

        <%@include file="/pages/ce/includes/include_modal_exibeLogEntidade.jsp" %>
    </h:panelGroup>

    <script src="${contexto}/beta/js/ext-funcs.js" type="text/javascript"></script>

    <script>
        jQuery(window).on("load", function(){
            iniciarTabela("tabelaTipoAmbiente", "${contexto}/prest/basico/tipoambiente", 1, "asc", "", true);
        });
    </script>

</f:view>
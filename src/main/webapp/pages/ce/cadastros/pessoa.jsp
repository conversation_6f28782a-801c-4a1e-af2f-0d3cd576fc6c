<%@include file="../includes/include_imports.jsp" %>
<%@include file="/includes/verificaModulo.jsp" %>

<f:view>
    <jsp:include page="/includes/include_carregando_ripple.jsp" flush="true"/>
<html>
<title>
    <h:outputText value="#{CElabels['menu.cadastros.pessoa']}"/>
</title>
<%@include file="../includes/include_head_form.jsp" %>
<head>
    <%@include file="/includes/include_import_minifiles.jsp" %>
</head>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<link href="beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<a4j:loadScript src="/script/jquery.maskedinput-1.2.2.js" />
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>
<script type="text/javascript" src="hoverform.js"></script>
<style>
    .obrigatorio {
        color: #DDDD00;
    }

    .mensagemObrigatorio {
        color: #FF0000;
    }
</style>
<meta name="viewport" content="width=device-width, initial-scale=1.0">

<script>
    jQuery.noConflict();
</script>
<body>

<h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
    <f:facet name="header">
        <c:choose>
            <c:when test="${modulo eq 'centralEventos'}">
                <jsp:include page="/pages/ce/includes/topoReduzido.jsp"/>
            </c:when>
            <c:otherwise>
                <jsp:include page="../../../topoReduzido_material.jsp"/>
            </c:otherwise>
        </c:choose>
    </f:facet>
</h:panelGrid>
<h:panelGroup id="parametrosPaginacaoConsulta">
    <h:inputHidden value="#{PessoaSimplificadoControle.controleConsulta.paginaAtual}"/>
    <h:inputHidden value="#{PessoaSimplificadoControle.controleConsulta.tamanhoConsulta}"/>
</h:panelGroup>
<!-- FORMULÁRIO DE CADASTRO -->
<h:form id="formCadastrarNovaPessoa">
    <hr style="border-color: #e6e6e6;"/>
    <h:panelGrid id="panelNovaPessoa" columns="2" rowClasses="linhaImpar, linhaPar"
                 columnClasses="classEsquerda, classDireita" width="100%">

        <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_CadastroPessoa_nome}"/>
        <h:panelGroup>
            <h:inputText tabindex="2" id="nome" size="50" maxlength="50" onblur="blurinput(this);"
                         onfocus="focusinput(this);"
                         styleClass="form" value="#{PessoaSimplificadoControle.pessoaVO.nome}"/>
            <h:message for="nome" styleClass="mensagemDetalhada"/>
        </h:panelGroup>

        <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Pessoa_nome_registro}"/>
        <h:panelGroup>
            <h:inputText tabindex="2" id="nomeRegistro" size="50" maxlength="50" onblur="blurinput(this);"
                         onfocus="focusinput(this);"
                         styleClass="form" value="#{PessoaSimplificadoControle.pessoaVO.nomeRegistro}"/>
            <h:message for="nomeRegistro" styleClass="mensagemDetalhada"/>
        </h:panelGroup>

        <h:outputText rendered="#{PessoaSimplificadoControle.pessoaVO.tipoPessoa == 'CL'}" styleClass="tituloCampos"
                      value="#{ColaboradorControle.displayIdentificadorFront[0]}"/>
        <h:panelGroup>
            <c:if test="${!ColaboradorControle.configuracaoSistema.usarSistemaInternacional}">
                <rich:jQuery id="mskCPF" selector="#cfp" timing="onload" query="mask('999.999.999-99')"/>
                <h:inputText tabindex="4" rendered="#{PessoaSimplificadoControle.pessoaVO.tipoPessoa == 'CL'}" id="cfp"
                             size="14" maxlength="14"
                             onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form"
                             value="#{PessoaSimplificadoControle.pessoaVO.cfp}">
                    <a4j:support focus="numeroTelefone" event="onchange" action="#{PessoaSimplificadoControle.validarCPF}"
                                 reRender="formCadastrarNovaPessoa"/>
                </h:inputText>
            </c:if>
            <c:if test="${ColaboradorControle.configuracaoSistema.usarSistemaInternacional}">
                <h:inputText tabindex="4" rendered="#{PessoaSimplificadoControle.pessoaVO.tipoPessoa == 'CL'}" id="cfpInte"
                             size="14" maxlength="14"
                             onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form"
                             value="#{PessoaSimplificadoControle.pessoaVO.cfp}">
                </h:inputText>
            </c:if>
        </h:panelGroup>
        <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_CadastroPessoa_telefone}"/>
        <h:panelGroup>
            <h:inputText tabindex="5" id="numeroTelefone"
                         size="13"
                         maxlength="13"
                         onchange="return validar_Telefone(this.id);"
                         onblur="blurinput(this);"
                         onkeypress="return mascara(this.form, this.id , '(99)999999999', event);"
                         onfocus="focusinput(this);"
                         styleClass="form"
                         value="#{PessoaSimplificadoControle.telefoneVO.numero}"/>
            <%--<rich:jQuery id="mskTelefone" selector="#numeroTelefone" timing="onload" query="mask('(99)9999-9999')" />--%>
            <h:message for="numeroTelefone" styleClass="mensagemDetalhada"/>
            <rich:spacer width="15px;"/>
            <h:outputText rendered="#{PessoaSimplificadoControle.pessoaVO.tipoPessoa == 'FO'}" styleClass="tituloCampos"
                          value="*#{msg_aplic.prt_CadastroPessoa_contato}"/>
            <h:panelGroup>
                <h:inputText tabindex="6" id="contato"
                             rendered="#{PessoaSimplificadoControle.pessoaVO.tipoPessoa == 'FO'}"
                             size="14"
                             maxlength="14"
                             onfocus="focusinput(this);"
                             styleClass="form" value="#{PessoaSimplificadoControle.fornecedorVO.contato}"/>
            </h:panelGroup>
        </h:panelGroup>
        <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_CadastroPessoa_sexo}"/>
        <h:panelGroup>
            <h:selectOneMenu tabindex="7" id="sexo" onblur="blurinput(this);"
                             onfocus="focusinput(this);"
                             style="color:black;"
                             styleClass="form"
                             value="#{PessoaSimplificadoControle.pessoaVO.sexo}">
                <f:selectItems value="#{PessoaSimplificadoControle.listaSelectItemSexoPessoa}"/>
            </h:selectOneMenu>
        </h:panelGroup>

        <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Pessoa_genero}"/>
        <h:panelGroup>
            <h:selectOneMenu tabindex="7" id="genero" onblur="blurinput(this);"
                             onfocus="focusinput(this);"
                             style="color:black;"
                             styleClass="form"
                             value="#{PessoaSimplificadoControle.pessoaVO.genero}">
                <f:selectItems value="#{PessoaSimplificadoControle.listaSelectItemGeneroPessoa}"/>
            </h:selectOneMenu>
        </h:panelGroup>

        <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_CadastroPessoa_email}"/>
        <h:panelGroup>
            <h:inputText tabindex="8" id="email" size="30" maxlength="50" onblur="blurinput(this);"
                         onfocus="focusinput(this);"
                         styleClass="form" value="#{PessoaSimplificadoControle.pessoaVO.email}"/>
        </h:panelGroup>
        <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_CadastroPessoa_dataNascimento}"/>
        <h:panelGroup>
            <rich:jQuery id="mskDataNasc" selector="#dataNasc" timing="onload" query="mask('99/99/9999')"/>
            <h:inputText tabindex="9" id="dataNasc" value="#{PessoaSimplificadoControle.pessoaVO.dataNasc}" size="10"
                         styleClass="form" onchange="return validar_Data(this.id);" onblur="blurinput(this);"
                         onfocus="focusinput(this);">
                <f:convertDateTime pattern="dd/MM/yyyy" locale="#{SuperControle.localeDefault}"
                                   timeZone="#{SuperControle.timeZoneDefault}"/>
            </h:inputText>
            <h:message for="dataNasc" styleClass="mensagemDetalhada"/>
        </h:panelGroup>

        <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_CadastroPessoa_endereco}"/>
        <h:panelGroup>
            <h:inputText tabindex="10" id="endereco" size="40" maxlength="40" onblur="blurinput(this);"
                         onfocus="focusinput(this);"
                         styleClass="form" value="#{PessoaSimplificadoControle.enderecoVO.endereco}"/>
            <rich:spacer width="15px"/>
            <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_CadastroPessoa_bairro}"/>
            <h:panelGroup>
                <h:inputText tabindex="11" size="20" maxlength="20" onblur="blurinput(this);"
                             onfocus="focusinput(this);" styleClass="form"
                             value="#{PessoaSimplificadoControle.enderecoVO.bairro}"/>
            </h:panelGroup>
        </h:panelGroup>
        <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_CadastroPessoa_pais}"/>
        <h:panelGroup>
            <h:panelGroup>
                <h:selectOneMenu tabindex="12" id="pessoapais" styleClass="campos"
                                 value="#{PessoaSimplificadoControle.pessoaVO.pais.codigo}">
                    <f:selectItems value="#{PessoaSimplificadoControle.listaSelectItemPais}"/>
                </h:selectOneMenu>

                <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_CadastroPessoa_uf}"/>
                <h:selectOneMenu tabindex="12" id="estadoCEP" styleClass="campos"
                                 value="#{PessoaSimplificadoControle.cepVO.ufSigla}">
                    <f:selectItems value="#{PessoaSimplificadoControle.listaSelectItemEstado}"/>
                    <a4j:support focus="cidade" event="onchange"
                                 action="#{PessoaSimplificadoControle.montarListaSelectItemCidade}" reRender="cidade"/>
                </h:selectOneMenu>
                <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_CadastroPessoa_cidade}"/>
                <h:selectOneMenu tabindex="13" id="cidade" onblur="blurinput(this);" onfocus="focusinput(this);"
                                 styleClass="form" value="#{PessoaSimplificadoControle.pessoaVO.cidade.codigo}">
                    <f:selectItems value="#{PessoaSimplificadoControle.listaSelectItemCidade}"/>
                </h:selectOneMenu>
            </h:panelGroup>
            <a4j:commandButton id="atualizar_cidade" action="#{PessoaSimplificadoControle.montarListaSelectItemCidade}"
                               image="/imagens/atualizar.png" reRender="formCadastrarNovaPessoa:cidade"/>
            <h:message for="cidade" styleClass="mensagemDetalhada"/>
            <rich:spacer width="5px"/>
            <a4j:commandButton id="consultaDadosCidades" alt="Cadastrar Cidade" reRender="formCidade"
                               oncomplete="Richfaces.showModalPanel('panelCidade'), setFocus(formCidade,'formCidade:cidade_nome');"
                               image="/images/icon_add.gif"/>
            <rich:spacer width="15px"/>

        </h:panelGroup>
        <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_CadastroPessoa_numero}"/>
        <h:inputText tabindex="14" size="10" maxlength="10" onblur="blurinput(this);" onfocus="focusinput(this);"
                     styleClass="form"
                     value="#{PessoaSimplificadoControle.enderecoVO.numero}"/>
    </h:panelGrid>
    <h:panelGrid columns="2" width="100%" columnClasses="colunaCentralizada">
        <h:panelGroup>
            <a4j:commandButton tabindex="15" id="okPessoa"
                               reRender="modalPanelCadastrarPessoaSimplificada, form, tabMensagens"
                               action="#{PessoaSimplificadoControle.gravarPessoaSimplificada}"
                               value="Gravar"
                               styleClass="botoes nvoBt"
                               oncomplete="#{PessoaSimplificadoControle.msgAlert}">
            </a4j:commandButton>
            <a4j:commandButton id="excluir"
                               reRender="modalPanelCadastrarPessoaSimplificada, form, tabMensagens, mdlMensagemGenerica"
                               action="#{PessoaSimplificadoControle.confirmarExcluir()}"
                               value="#{msg_bt.btn_excluir}" title="#{msg.msg_excluir_dados}" accesskey="3" styleClass="botoes nvoBt btSec btPerigo"
                               oncomplete="#{PessoaSimplificadoControle.msgAlert}"
                               actionListener="#{PessoaSimplificadoControle.autorizacao}">
                <!-- Entidade.pessoa -->
                <f:attribute name="entidade" value="104" />
                <!-- Operacao.consultar -->
                <f:attribute name="operacao" value="E" />
            </a4j:commandButton>
            <h:commandButton id="consultar"
                             immediate="true" action="#{PessoaSimplificadoControle.inicializarConsultar}"
                             value="#{msg_bt.btn_voltar_lista}"
                             alt="#{msg.msg_consultar_dados}" accesskey="4" styleClass="botoes nvoBt btSec" style="margin-left: 15px;"/>
        </h:panelGroup>
    </h:panelGrid>

    <h:panelGrid id="tabMensagens" columns="1" width="100%" styleClass="tabMensagens">
        <h:panelGrid columns="3" width="100%" styleClass="tabMensagens">
            <h:panelGrid columns="1" width="100%">

                <h:outputText value=" "/>

            </h:panelGrid>
            <h:commandButton rendered="#{PessoaSimplificadoControle.sucesso}" image="/imagens/sucesso.png"/>
            <h:commandButton rendered="#{PessoaSimplificadoControle.erro}" image="/imagens/erro.png"/>
            <h:panelGrid columns="1" width="100%">
                <h:outputText styleClass="mensagem" value="#{PessoaSimplificadoControle.mensagem}"/>
                <h:outputText styleClass="mensagemDetalhada" value="#{PessoaSimplificadoControle.mensagemDetalhada}"/>
            </h:panelGrid>
        </h:panelGrid>
    </h:panelGrid>
</h:form>
</body>
</html>
    <%@include file="/includes/include_modal_mensagem_generica.jsp"%>
</f:view>

<%@page contentType="text/html" pageEncoding="ISO-8859-1" %>
<%@include file="../includes/include_imports.jsp" %>

<f:view>
    <jsp:include page="/includes/include_carregando_ripple.jsp" flush="true"/>
    <h:form id="form">
        <html>
        <!-- <PERSON><PERSON><PERSON> o elemento HEAD da página -->
        <head>
            <%@include file="../includes/include_head_ce.jsp" %>
        </head>
        <body class="ce">
        <h:panelGroup layout="block" style="display: inline-table;width: 100%;" styleClass="fundoCinza">

            <h:panelGroup layout="block" styleClass="bgtop topoZW" rendered="#{MenuControle.apresentarTopo}">
                <jsp:include page="../../../include_topo_2.0.jsp" flush="true"/>
                <jsp:include page="../../../include_menu_ce_flat.jsp" flush="true"/>
                <rich:jQuery selector=".item2" query="addClass('menuItemAtual')"/>
            </h:panelGroup>

            <h:panelGroup layout="block" styleClass="caixaCorpo">
                <h:panelGroup layout="block" style="height: 80%;width: 100%">
                    <h:panelGroup layout="block" styleClass="caixaMenuLatel">
                        <h:panelGroup layout="block" styleClass="container-imagem container-conteudo-central">
                            <h:panelGroup layout="block" style="clear:both;">
                                <table width="98%" border="0" align="left" cellpadding="0" cellspacing="0" class="text" style="margin-right:30px;margin-bottom:20px;">
                                    <tr>
                                        <td width="19" height="50" align="left" valign="top"><img src="${contexto}/images/box_centro_top_left.gif" width="19" height="50"></td>
                                        <td align="left" valign="top" background="${contexto}/images/box_centro_top.gif" class="tituloboxcentro" style="padding:11px 0 0 0;">Cadastros Auxiliares</td>
                                        <td width="19" align="left" valign="top"><img src="${contexto}/images/box_centro_top_right.gif" width="19" height="50"></td>
                                    </tr>
                                    <tr>
                                        <td align="left" valign="top" background="${contexto}/images/box_centro_left.gif"><img src="${contexto}/images/shim.gif"></td>
                                        <td align="left" valign="top" bgcolor="#ffffff" style="padding:15px 15px 5px 15px;"class="headerIndex">
                                            <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0" >
                                                <h:panelGrid columnClasses="w33,w33,w33" columns="3" width="100%" cellpadding="0" cellspacing="0">


                                                    <h:panelGrid columns="1" style="height:100%;" width="100%" cellpadding="5" cellspacing="5" >
                                                        <h:panelGroup>
                                                            <rich:spacer height="5px"/>
                                                            <div>
                                                                <h:outputLink styleClass="linkWiki"
                                                                              value="#{SuperControle.urlWiki}Cadastros:Cadastros_Auxiliares:Categoria_de_Clientes"
                                                                              title="Clique e saiba mais: Categoria de cliente"
                                                                              target="_blank" >
                                                                    <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                                                                </h:outputLink>
                                                                <a class="titulo3" id="categoria" onclick="abrirPopup('${contexto}/faces/faces/categoriaCons.jsp?modulo=centralEventos', '', 800, 595);" href="#">
                                                                    <h:outputText value="#{CElabels['menu.cadastros.categoriaCliente']}"/>
                                                                </a>
                                                            </div>
                                                            <rich:spacer height="25px"/>
                                                            <rich:spacer width="10px"/>
                                                            <h:outputText styleClass="text" value="Utilize as categorias para diferenciar seus clientes. Dessa forma é possível agrupar todos aqueles que possuem características semelhantes, facilitando o controle e principalmente, trazendo organização para os dados do seu sistema. Um exemplo pode ser por tipo de eventos."/>
                                                        </h:panelGroup>
                                                        <h:panelGroup>
                                                            <rich:spacer height="25px"/>
                                                            <div>
                                                                <h:outputLink styleClass="linkWiki"
                                                                              value="#{SuperControle.urlBaseConhecimento}como-excluir-cadastro-de-pessoas-alunos/"
                                                                              title="Clique e saiba mais: Cliente"
                                                                              target="_blank" >
                                                                    <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                                                                </h:outputLink>
                                                                <a class="titulo3" id="cliente" onclick="abrirPopup('${contexto}/faces/faces/clienteCons.jsp?modulo=centralEventos', 'Cliente', 800, 595);" href="#">
                                                                    <h:outputText value="#{CElabels['menu.cadastros.cliente']}"/>
                                                                </a>
                                                            </div>
                                                            <rich:spacer height="25px"/>
                                                            <rich:spacer width="10px"/>
                                                            <h:outputText styleClass="text" value="Aqui você poderá cadastrar todos os seus clientes. Lembre-se, a qualidade de suas informações depende do quão completo estão os dados do cliente."/>
                                                            <br />
                                                            <h:outputText styleClass="text" value="É possível definir alguns campos do cadastro como obrigatórios para garantir que os mesmos serão sempre preenchidos."/>
                                                        </h:panelGroup>
                                                        <h:panelGroup>
                                                            <rich:spacer height="5px"/>
                                                            <div>
                                                                <h:outputLink styleClass="linkWiki"
                                                                              value="#{SuperControle.urlBaseConhecimento}como-cadastrar-um-novo-colaborador/"
                                                                              title="Clique e saiba mais: Colaborador"
                                                                              target="_blank" >
                                                                    <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                                                                </h:outputLink>
                                                                <a class="titulo3" id="colaborador" onclick="abrirPopup('${contexto}/faces/faces/colaboradorCons.jsp?modulo=centralEventos', 'Colaborador', 800, 595);" href="#">
                                                                    <h:outputText value="#{CElabels['menu.cadastros.colaborador']}"/>
                                                                </a>
                                                            </div>
                                                            <rich:spacer height="25px"/>
                                                            <rich:spacer width="10px"/>
                                                            <h:outputText styleClass="text" value="Cadastre todos os colaboradores da empresa. Apesar de serem inclusos em uma mesma tela eles se diferenciarão pelo tipo que deverá ser definido. E no cadastro de usuários pode ser selecionado o colaborador já cadastrado."/>                                                        </h:panelGroup>

                                                    </h:panelGrid>
                                                    <h:panelGrid style="height:100;%" columns="1" width="100%" cellpadding="5" cellspacing="5" >

                                                        <h:panelGroup>

                                                            <div>
                                                                <h:outputLink styleClass="linkWiki"
                                                                              value="#{SuperControle.urlWiki}Cadastros:Cadastros_Auxiliares:Grau_de_Instrucao"
                                                                              title="Clique e saiba mais: Grau de instrução"
                                                                              target="_blank" >
                                                                    <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                                                                </h:outputLink>
                                                                <a class="titulo3" id="grauInstrucao" onclick="abrirPopup('${contexto}/faces/faces/grauInstrucaoCons.jsp?modulo=centralEventos', '', 800, 595);" href="#">
                                                                    <h:outputText value="#{CElabels['menu.cadastros.grauInstrucao']}"/>
                                                                </a>
                                                            </div>
                                                            <rich:spacer height="25px"/>
                                                            <rich:spacer width="10px"/>
                                                            <h:outputText styleClass="text" value="Aqui é possível cadastrar grau de escolaridade para utilizar nos cadastros de cliente e colaborador."/>
                                                        </h:panelGroup>

                                                        <h:panelGroup>
                                                            <rich:spacer height="25px"/>
                                                            <div>
                                                                <h:outputLink styleClass="linkWiki"
                                                                              value="#{SuperControle.urlWiki}Cadastros:Cadastros_Auxiliares:Profissao"
                                                                              title="Clique e saiba mais: Profissão"
                                                                              target="_blank" >
                                                                    <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                                                                </h:outputLink>
                                                                <a class="titulo3" id="tabelaPrecos" onclick="abrirPopup('${contexto}/faces/faces/profissaoCons.jsp?modulo=centralEventos', 'Profissão', 800, 595);" href="#">
                                                                    <h:outputText value="#{CElabels['menu.cadastros.profissao']}"/>
                                                                </a>
                                                            </div>
                                                            <rich:spacer height="25px"/>
                                                            <rich:spacer width="10px"/>
                                                            <h:outputText styleClass="text" value="Mantenha seu cadastro de profissão completo para que no momento de cadastrar novos clientes ou colaboradores esta opção seja preenchida."/>
                                                        </h:panelGroup>

                                                        <h:panelGroup>
                                                            <rich:spacer height="25px"/>
                                                            <div>
                                                                <h:outputLink styleClass="linkWiki"
                                                                              value="#{SuperControle.urlWiki}Cadastros:Cadastros_Auxiliares:Pais"
                                                                              title="Clique e saiba mais: País"
                                                                              target="_blank" >
                                                                    <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                                                                </h:outputLink>
                                                                <a class="titulo3" id="pais" onclick="abrirPopup('${contexto}/faces/faces/paisCons.jsp?modulo=centralEventos', 'País', 800, 595);" href="#">
                                                                    <h:outputText value="#{CElabels['menu.cadastros.pais']}"/>
                                                                </a>
                                                            </div>
                                                            <rich:spacer height="25px"/>
                                                            <rich:spacer width="10px"/>
                                                            <h:outputText styleClass="text" value="Se necessário pode ser cadastrado novos Países para manter seu cadastro o mais completo possível. Podendo haver também clientes de outros países."/>
                                                        </h:panelGroup>
                                                    </h:panelGrid>
                                                    <h:panelGrid columns="1" width="100%" style="height:100%;" cellpadding="5" cellspacing="5" >
                                                        <h:panelGroup>
                                                            <div><img src="${contexto}/imagens/cadastros.png"></div>
                                                        </h:panelGroup>

                                                        <h:panelGroup>
                                                            <rich:spacer height="25px"/>
                                                            <div>
                                                                <h:outputLink styleClass="linkWiki"
                                                                              value="#{SuperControle.urlWiki}Cadastros:Cadastros_Auxiliares:Cidade"
                                                                              title="Clique e saiba mais: Cidade"
                                                                              target="_blank" >
                                                                    <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                                                                </h:outputLink>
                                                                <a class="titulo3" id="cidade" onclick="abrirPopup('${contexto}/faces/faces/cidadeCons.jsp?modulo=centralEventos', 'Cidade', 800, 595);" href="#">
                                                                    <h:outputText value="#{CElabels['menu.cadastros.cidade']}"/>
                                                                </a>
                                                            </div>
                                                            <rich:spacer height="25px"/>
                                                            <rich:spacer width="10px"/>
                                                            <h:outputText styleClass="text" value="Efetue o cadastro de cidades, fazendo isso facilitará o cadastro de clientes, e nos próximos cadastros basta selecionar a cidade dentro de uma lista de cidades cadastradas. Esta opção é muito útil quando não souber o CEP."/>
                                                        </h:panelGroup>
                                                    </h:panelGrid>

                                                </h:panelGrid>
                                            </h:panelGrid>
                                        </td>
                                        <td align="left" valign="top" background="${contexto}/images/box_centro_right.gif"><img src="${contexto}/images/shim.gif"></td>
                                    </tr>
                                    <tr>
                                        <td height="20" align="left" valign="top"><img src="${contexto}/images/box_centro_bottom_left.gif" width="19" height="20"></td>
                                        <td align="left" valign="top" background="${contexto}/images/box_centro_bottom.gif"><img src="${contexto}/images/shim.gif"></td>
                                        <td align="left" valign="top"><img src="${contexto}/images/box_centro_bottom_right.gif" width="19" height="20"></td>
                                    </tr>
                                </table>
                            </h:panelGroup>
                        </h:panelGroup>
                        <jsp:include page="../includes/include_box_menulatcadastrosauxiliares.jsp" flush="true"/>
                    </h:panelGroup>
                </h:panelGroup>
            </h:panelGroup>

            <jsp:include page="../../../include_rodape_flat.jsp" flush="true"/>
            <%@include file="../includes/include_focus.jsp" %>
        </h:panelGroup>
        </body>
        </html>
    </h:form>
</f:view>

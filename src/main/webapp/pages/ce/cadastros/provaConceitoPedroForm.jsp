<%@include file="../includes/include_imports.jsp" %>

<f:view>
    <jsp:include page="/includes/include_carregando_ripple.jsp" flush="true"/>
    <html>
       	<!-- Inclui o elemento HEAD da p�gina -->
        <%@include file="../includes/include_head_form.jsp" %>

        <title><h:outputText value="#{CElabels['menu.cadastros.provaConceito']}"/></title>

        <script>
            function validar() {
	   		
                // LIMPA AS MENSAGENS CONTIDAS NAS DIVs DETERMINADAS
                limparMensagem('data');
                limparMensagem('nome');
                limparMensagem('valor');
                limparMensagem('descricao');

                // OBTEM OS CAMPOS QUE SER�O VALIDADOS
                var descricao = document.getElementById('form:descricao');
                var data = document.getElementById('form:dataInputDate');
                var valor = document.getElementById('form:valor');
                var nome = document.getElementById('form:nome');

                // VERIFICA SE EST�O PREENCHIDOS
                var validade = true;

                if (nome == null || nome.value == null || nome.value == "") {
                    exibirMensagem('<h:outputText value="O campo Nome deve ser preenchido"/>', 'nome');
                    validade = false;
                }
                if (data == null || data.value == null || data.value == "") {
                    exibirMensagem('<h:outputText value="O campo Data deve ser preenchido"/>', 'data');
                    validade = false;
                }
			
		
                return validade;
            }
        </script>

        <body>

            <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
                <f:facet name="header">
                    <!-- INCLUS�O DO TOPO DA P�GINA -->

                    <jsp:include page="../includes/topoReduzido.jsp"/>

                </f:facet>
            </h:panelGrid>

            <h:form id="form">

                <a4j:keepAlive beanName="ProvaConceitoPedroControle" />

                <table>
                    <tr>
                        <td>
                            <h:outputText styleClass="tituloCampos" value="Data" />
                        </td>
                        <td>
                            <rich:calendar id="data" styleClass="form" value="#{ProvaConceitoPedroControle.provaConceitoPedroVO.data}"
                                           datePattern="dd/MM/yyyy" inputSize="10" inputClass="form" enableManualInput="true"
                                           zindex="2" showWeeksBar="false" />
                            <%@include file="../includes/include_obrigatorio.jsp" %>
                            <div id="divObg-data" class="mensagemObrigatorio"></div>
                        </td>
                    </tr>

                    <tr>
                        <td>
                            <h:outputText styleClass="tituloCampos" value="Nome" />
                        </td>
                        <td>
                            <h:inputText onblur="blurinput(this);" onfocus="focusinput(this);"
                                         styleClass="form" size="30" maxlength="25"
                                         value="#{ProvaConceitoPedroControle.provaConceitoPedroVO.nome}" id="nome"></h:inputText>
                            <%@include file="../includes/include_obrigatorio.jsp" %>
                            <div id="divObg-nome" class="mensagemObrigatorio"></div>
                        </td>
                    </tr>

                    <tr>
                        <td>
                            <h:outputText styleClass="tituloCampos" value="Descri��o" />
                        </td>
                        <td>
                            <h:inputTextarea rows="3" cols="30" onblur="blurinput(this);"
                                             onfocus="focusinput(this);" id="descricao"
                                             value="#{ProvaConceitoPedroControle.provaConceitoPedroVO.descricao}">
                            </h:inputTextarea>
                            <div id="divObg-descricao" class="mensagemObrigatorio"></div>
                        </td>
                    </tr>

                    <tr>
                        <td>
                            <h:outputText styleClass="tituloCampos" value="Valor" />
                        </td>
                        <td>
                            <h:inputText onblur="blurinput(this);" onfocus="focusinput(this);"
                                         styleClass="form" size="10" maxlength="5" id="valor"
                                         value="#{ProvaConceitoPedroControle.provaConceitoPedroVO.valor}"></h:inputText>
                            <div id="divObg-valor" class="mensagemObrigatorio"></div>
                        </td>
                    </tr>

                    <tr>
                        <td>
                            <h:outputText styleClass="tituloCampos" value="Verdadeiro?" />
                        </td>
                        <td>
                            <h:selectBooleanCheckbox value="#{ProvaConceitoPedroControle.provaConceitoPedroVO.verdadeiro}" id="verdadeiro" />
                            <div id="divObg-verdadeiro" class="mensagemObrigatorio"></div>
                        </td>
                    </tr>

                </table>



                <!-- PAINEL DE EXIBI��O DE MENSAGENS E A��ES -->
                <h:panelGrid id="mensagens" columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid columns="3" width="100%" styleClass="tabMensagens">
                        <h:panelGrid columns="1" width="100%">

                            <h:outputText value=" "/>

                        </h:panelGrid>

                        <!-- MENSAGENS -->
                        <h:commandButton rendered="#{PerfilEventoControle.sucesso}" image="/imagens/bt_sucesso.png"/>
                        <h:commandButton rendered="#{PerfilEventoControle.erro}" image="/imagens/erro.png"/>

                        <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                            <h:outputText styleClass="mensagem" value="#{ProvaConceitoPedroControle.mensagem}"/>
                        </h:panelGrid>
                    </h:panelGrid>

                    <!-- A��ES -->
                    <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                        <h:panelGroup>
                            <h:commandButton id="novo" immediate="true" action="#{ProvaConceitoPedroControle.novo}"
                                             value="#{CElabels['operacoes.consulta.consultar']}" image="/imagens/botaoNovo.png"
                                             alt="#{CElabels['operacoes.consulta.consultarDados']}" styleClass="botoes" />

                            <h:outputText value="    " />

                            <h:commandButton id="salvar" action="#{ProvaConceitoPedroControle.salvar}" value="#{CElabels['operacoes.gravar']}"
                                             onclick="if(!validar()){return false;};" image="/imagens/botaoGravar.png"
                                             alt="#{CElabels['operacoes.gravar.dados']}" styleClass="botoes" />

                            <h:outputText value="    " />

                            <h:commandButton id="consultar" immediate="true" action="#{NavegacaoControle.abrirTelaProvaConceitoPedro}"
                                             value="#{CElabels['operacoes.consulta.consultar']}" image="/imagens/botaoConsultar.png"
                                             alt="#{CElabels['operacoes.consulta.consultarDados']}" styleClass="botoes" />

                            <h:outputText value="    " />

                            <h:commandLink action="#{ProvaConceitoPedroControle.voltar}">
                                <h:graphicImage value="/imagens/bt_voltar.jpg" styleClass="botoes" style="border: 0px;" alt="#{CElabels['operacoes.voltar']}"/>
                            </h:commandLink>
                        </h:panelGroup>
                    </h:panelGrid>
                </h:panelGrid>
            </h:form>
        </body>
    </html>
</f:view>
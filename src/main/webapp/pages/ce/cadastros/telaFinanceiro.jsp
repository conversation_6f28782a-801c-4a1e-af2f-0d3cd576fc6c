<%@page contentType="text/html" pageEncoding="ISO-8859-1" %>
<%@include file="../includes/include_imports.jsp" %>

<f:view>
    <jsp:include page="/includes/include_carregando_ripple.jsp" flush="true"/>
    <h:form id="form">
        <html>
        <!-- <PERSON><PERSON><PERSON> o elemento HEAD da página -->
        <head>
            <%@include file="../includes/include_head_ce.jsp" %>
        </head>
        <body class="ce">
        <h:panelGroup layout="block" style="display: inline-table;width: 100%;" styleClass="fundoCinza">

            <h:panelGroup layout="block" styleClass="bgtop topoZW" rendered="#{MenuControle.apresentarTopo}">
                <jsp:include page="../../../include_topo_2.0.jsp" flush="true"/>
                <jsp:include page="../../../include_menu_ce_flat.jsp" flush="true"/>
                <rich:jQuery selector=".item2" query="addClass('menuItemAtual')"/>
            </h:panelGroup>

            <h:panelGroup layout="block" styleClass="caixaCorpo">
                <h:panelGroup layout="block" style="height: 80%;width: 100%">
                    <h:panelGroup layout="block" styleClass="caixaMenuLatel">
                        <h:panelGroup layout="block" styleClass="container-imagem container-conteudo-central">
                            <h:panelGroup layout="block" style="clear:both;">

                                <table width="98%" border="0" align="left" cellpadding="0" cellspacing="0" class="text" style="margin-right:30px;margin-bottom:20px;">
                                    <tr>
                                        <td width="19" height="50" align="left" valign="top"><img src="${contexto}/images/box_centro_top_left.gif" width="19" height="50"></td>
                                        <td align="left" valign="top" background="${contexto}/images/box_centro_top.gif" class="tituloboxcentro" style="padding:11px 0 0 0;">Financeiro</td>
                                        <td width="19" align="left" valign="top"><img src="${contexto}/images/box_centro_top_right.gif" width="19" height="50"></td>
                                    </tr>
                                    <tr>
                                        <td align="left" valign="top" background="${contexto}/images/box_centro_left.gif"><img src="${contexto}/images/shim.gif"></td>
                                        <td align="left" valign="top" bgcolor="#ffffff" style="padding:15px 15px 5px 15px;" class="headerIndex">
                                            <h:panelGrid columns="1"  width="100%" cellpadding="0" cellspacing="0">
                                                <h:panelGrid columnClasses="w33,w33,w33" columns="3" width="100%" cellpadding="0" cellspacing="0">

                                                    <h:panelGrid columns="1" style="height:100%;" width="100%" cellpadding="5" cellspacing="5" >
                                                        <c:if test="${LoginControle.permissaoAcessoMenuVO.estornoRecibo}">
                                                            <h:panelGroup>
                                                                <rich:spacer height="5px"/>
                                                                <!-- inicio estornoRecibo-->
                                                                <div>
                                                                    <h:outputLink value="#{SuperControle.urlBaseConhecimento}como-estornar-o-recibo-de-pagamento-de-um-colaborador/"
                                                                                  title="Clique e saiba mais: Estorno de Recibo" target="_blank" rendered="#{LoginControle.permissaoAcessoMenuVO.estornoRecibo}">
                                                                        <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                                                                    </h:outputLink>
                                                                    <rich:spacer width="1px"/>
                                                                    <h:outputLink styleClass="titulo3" onclick="abrirPopup('../../../faces/estornoReciboCons.jsp?modulo=centralEventos', 'EstornoRecibo', 800, 595);" value="#">
                                                                        <h:outputText rendered="#{LoginControle.permissaoAcessoMenuVO.estornoRecibo}" value="#{CElabels['menu.consulta.financeiro.estornoRecibo']}"/>
                                                                        <f:attribute name="tipoConsulta" value="detalhada"/>
                                                                    </h:outputLink>
                                                                </div>

                                                                <rich:spacer height="25px"/>
                                                                <rich:spacer width="10px"/>
                                                                <h:outputText styleClass="text" value="É possível estornar recibos (parcelas ou valores que foram quitados). Aqui você pode consultar o recibo pelo código ou pelo nome do cliente que deseja estornar, são apresentadas todas as informações do pagamento e então realizar o estorno do recibo."/>
                                                                <!-- fim estornoRecibo-->
                                                            </h:panelGroup>
                                                        </c:if>
                                                        <c:if test="${LoginControle.permissaoAcessoMenuVO.movimentoContaCorrenteCliente}">
                                                            <h:panelGroup>
                                                                <rich:spacer height="25px"/>
                                                                <!-- inicio item MOVIMENTO CONTA CORRENTE-->
                                                                <div>
                                                                    <h:outputLink value="#{SuperControle.urlBaseConhecimento}como-lancar-credito-ou-debito-na-conta-corrente-do-aluno/"
                                                                                  title="Clique e saiba mais: Movimento da Conta Corrente do Cliente" target="_blank" rendered="#{LoginControle.permissaoAcessoMenuVO.movimentoContaCorrenteCliente}">
                                                                        <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                                                                    </h:outputLink>
                                                                    <rich:spacer width="1px"/>
                                                                    <h:outputLink  styleClass="titulo3" onclick="abrirPopup('../../../faces/movimentoContaCorrenteClienteCons.jsp?modulo=centralEventos', 'MovimentoContaCorrenteCliente', 800, 595);" value="#">
                                                                        <h:outputText rendered="#{LoginControle.permissaoAcessoMenuVO.movimentoContaCorrenteCliente}" value="#{CElabels['menu.consulta.financeiro.movimentoContaCorrenteCliente']}"/>
                                                                        <f:attribute name="tipoConsulta" value="detalhada"/>
                                                                    </h:outputLink>
                                                                </div>

                                                                <rich:spacer height="25px"/>
                                                                <rich:spacer width="10px"/>
                                                                <h:outputText styleClass="text" value="Aqui você pode consultar e realizar movimentações na conta do cliente. É possível consultar valores em créditos ou débitos que foram lançados, também é registrada a utilização destes créditos como, por exemplo, para quitar alguma parcela em aberto. Nesta tela também podem ser lançados créditos ou débitos para o cliente inserindo uma descrição para este lançamento. Conforme mencionado no item de perfis de acesso, somente usuários que tiverem permissão poderão realizar estas operações."/>
                                                                <!-- fim item MOVIMENTO CONTA CORRENTE-->
                                                            </h:panelGroup>
                                                        </c:if>
                                                    </h:panelGrid>
                                                    <h:panelGrid style="height:100;%" columns="1" width="100%" cellpadding="5" cellspacing="5" >
                                                        <c:if test="${LoginControle.permissaoAcessoMenuVO.formaPagamento}">
                                                            <h:panelGroup>
                                                                <!-- inicio item forma de pagamento -->
                                                                <div>
                                                                    <h:outputLink value="#{SuperControle.urlBaseConhecimento}como-cadastrar-uma-forma-de-pagamento/"
                                                                                  title="Clique e saiba mais: Formas de Pagamento" target="_blank" rendered="#{LoginControle.permissaoAcessoMenuVO.formaPagamento}">
                                                                        <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                                                                    </h:outputLink>
                                                                    <rich:spacer width="1px"/>
                                                                    <h:outputLink styleClass="titulo3" onclick="abrirPopup('../../../faces/formaPagamentoCons.jsp?modulo=centralEventos', 'FormaPagamento', 800, 595);" value="#">
                                                                        <h:outputText rendered="#{LoginControle.permissaoAcessoMenuVO.formaPagamento}" value="#{CElabels['menu.consulta.financeiro.formasPagamento']}"/>
                                                                    </h:outputLink>
                                                                </div>

                                                                <rich:spacer height="25px"/>
                                                                <rich:spacer width="10px"/>
                                                                <h:outputText styleClass="text" value="Cadastre aqui as formas de pagamento que são permitidas nas movimentações financeiras, ou seja, na quitação de parcelas. Exemplo: Cartão de Crédito, Cheque, Dinheiro, etc."/>
                                                                <!-- fim item forma de pagamento -->
                                                            </h:panelGroup>
                                                        </c:if>
                                                        <c:if test="${LoginControle.permissaoAcessoMenuVO.operadoraCartao}">
                                                            <h:panelGroup>
                                                                <rich:spacer height="25px"/>
                                                                <!-- inicio item OPERADORA DE CARTAO -->
                                                                <div>
                                                                    <h:outputLink value="#{SuperControle.urlWiki}Cadastros:Config._Financeiras:Operadora_de_Cartão"
                                                                                  title="Clique e saiba mais: Operadora de Cartão" target="_blank" rendered="#{LoginControle.permissaoAcessoMenuVO.operadoraCartao}">
                                                                        <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                                                                    </h:outputLink>
                                                                    <rich:spacer width="1px"/>
                                                                    <h:outputLink styleClass="titulo3" onclick="abrirPopup('../../../faces/operadoraCartaoCons.jsp?modulo=centralEventos', 'OperadoraCartao', 800, 595);" value="#">
                                                                        <h:outputText  rendered="#{LoginControle.permissaoAcessoMenuVO.operadoraCartao}" value="#{CElabels['menu.consulta.financeiro.operadorasCartao']}"/>
                                                                    </h:outputLink>
                                                                </div>

                                                                <rich:spacer height="25px"/>
                                                                <rich:spacer width="10px"/>
                                                                <h:outputText styleClass="text" value="Cadastre aqui as operadoras de cartão que são permitidas na quitação de parcelas, quais operadoras a empresa trabalha. Exemplo: VISA, MASTERCARD, etc."/>
                                                                <!-- fim item OPERADORA DE CARTAO -->
                                                            </h:panelGroup>
                                                        </c:if>
                                                    </h:panelGrid>
                                                    <h:panelGrid columns="1" width="100%" style="height:100%;" cellpadding="5" cellspacing="5" >
                                                        <h:panelGroup>
                                                            <div><img src="${contexto}/imagens/cadastros.png"></div>
                                                        </h:panelGroup>
                                                    </h:panelGrid>

                                                </h:panelGrid>
                                            </h:panelGrid>
                                        </td>
                                        <td align="left" valign="top" background="${contexto}/images/box_centro_right.gif"><img src="${contexto}/images/shim.gif"></td>
                                    </tr>
                                    <tr>
                                        <td height="20" align="left" valign="top"><img src="${contexto}/images/box_centro_bottom_left.gif" width="19" height="20"></td>
                                        <td align="left" valign="top" background="${contexto}/images/box_centro_bottom.gif"><img src="${contexto}/images/shim.gif"></td>
                                        <td align="left" valign="top"><img src="${contexto}/images/box_centro_bottom_right.gif" width="19" height="20"></td>
                                    </tr>
                                </table>

                            </h:panelGroup>

                        </h:panelGroup>
                        <jsp:include page="../includes/include_box_menulafinanceiro.jsp" flush="true"/>
                    </h:panelGroup>
                </h:panelGroup>
            </h:panelGroup>

            <jsp:include page="../../../include_rodape_flat.jsp" flush="true"/>
            <%@include file="../includes/include_focus.jsp" %>
        </h:panelGroup>
        </body>
        </html>



    </h:form>
</f:view>

<%@include file="../includes/include_imports.jsp" %>

<f:view>
    <jsp:include page="/includes/include_carregando_ripple.jsp" flush="true"/>
    <h:form id="form">
    	<html>
    			
		<!-- Inclui o elemento HEAD da p�gina -->
		<%@include file="/pages/ce/includes/include_head.jsp" %>
		<script>
		function validarDisponibilidade(){
			

			var data = document.getElementById('form:dataVisitaInputDate');
			var ambiente = document.getElementById('form:ambiente');
			
			
			var validade = true;

			if (data == null || data.value == null || data.value == "") {
					exibirMensagem('<h:outputText value="#{Mensagens[\'campoObrigatorio.data\']}"/>', 'dataVisita');
					validade = false;
			}
			if (ambiente == 0 || ambiente.value == 0) {
					exibirMensagem('<h:outputText value="#{Mensagens[\'campoObrigatorio.ambiente\']}"/>', 'ambiente');
					validade = false;
			}
	
			return validade;
		}
		
		function ativarAjuda(id) {
			if (typeof id == "string") {
				var hint = document.getElementById('hint-' + id);
				hint.style.visibility = hint.style.visibility == 'hidden' ? 'visible' : 'hidden';
			} else {
				for (var i = 0; i < id.length; i++) {
					var hint = document.getElementById('hint-' + id[i]);
					hint.style.visibility = hint.style.visibility == 'hidden' ? 'visible' : 'hidden';
				}
			}
		}

		    function Mascara_Hora(horarioFinalIn, campo){  
			       var hora01 = '';  
			       hora01 = hora01 + horarioFinalIn;  
			       if (hora01.length == 2){  
			          hora01 = hora01 + ':';  
			          campo.value = hora01;  
			       }  
			       if (hora01.length == 5){  
			          Verifica_Hora(campo);  
			      }  
			   }  
			     
			   function Verifica_Hora(campo){  
			      hrs = (campo.value.substring(0,2));  
			      min = (campo.value.substring(3,5));  
			      estado = "";  
			      if ((hrs < 00 ) || (hrs > 23) || ( min < 00) ||( min > 59)){  
			         estado = "errada";  
			      }  
			     
			      if (campo.value == "") {  
			         estado = "errada";  
			     }  
			      if (estado == "errada") {  
			         alert("Hora invalida!");
			         document.getElementById('form:horarioFinalIn').value=""; 
			         exibirMensagem('<h:outputText value="#{Mensagens[\'campoObrigatorio.horaInvalida\']}"/>', ['dataVisita','dataVisita-msgs','dataVisita-msgs-2']);
			         campo.focus();  
			      }  
			   }
			   function validar(){
				    var data = document.getElementById('form:dataVisitaInputDate');
				    var dataVisitaString = document.getElementById('form:dataVisitaString');
					var ambiente = document.getElementById('form:ambiente');
					var tipos = document.getElementById('form:tiposVisita');
					var horarioFinalIn = document.getElementById('form:horarioFinalIn');					
					var horaInicial = document.getElementById('form:horaInicial');
					
					var validade = true;
					
					if (dataVisitaString == null){
						if (data == null || data.value == null || data.value == "") {
								exibirMensagem('<h:outputText value="#{Mensagens[\'campoObrigatorio.data\']}"/>', 'dataVisita');
								validade = false;
						}
					}
					
					if (ambiente != null && (ambiente == 0 || ambiente.value == 0)) {
							exibirMensagem('<h:outputText value="#{Mensagens[\'campoObrigatorio.ambiente\']}"/>', 'ambiente');
							validade = false;
					}
					
					if (tipos != null && (tipos == 0 || tipos.value == 0)) {
						exibirMensagem('<h:outputText value="#{Mensagens[\'campoObrigatorio.agendaVisita.tipoVisita\']}"/>', 'tiposVisita');
						validade = false;
					}

					if (horaInicial == null || horaInicial.value == null || horaInicial.value == "") {
						exibirMensagem('<h:outputText value="#{Mensagens[\'campoObrigatorio.agendaVisita.horaInicial\']}"/>', 'horaInicial');
						validade = false;							
					}

					if (horarioFinalIn != null){					
						if((tipos != null && (tipos == 3 || tipos.value == 3)) && (horarioFinalIn.value == null || horarioFinalIn.value == "")){
							exibirMensagem('<h:outputText value="#{Mensagens[\'campoObrigatorio.agendaVisita.horaFinal\']}"/>', 'horarios');
							validade = false;
						}
						if(!formataHora(horaInicial.value,2)||!formataHora(horarioFinalIn.value,2)){
							exibirMensagem('<h:outputText value="#{Mensagens[\'campoObrigatorio.horaInvalida\']}"/>', ['dataVisita','dataVisita-msgs','dataVisita-msgs-2']);
							validade = false;
						}
						
						if(horaInicial.value >= horarioFinalIn.value){
							exibirMensagem('<h:outputText value="#{Mensagens[\'parametros.horaFinalMenorhoraInicial\']}"/>', 'horaInicial');
							validade = false;
						}
					}					
					
					return validade;				   
			   }
			   			      
			   function validarHorarioFinal() {
	   				limparMensagem('terminoDiaPosterior');
	   				limparMensagem('validarHora');
	   	   			
	   				var horarioInicial = document.getElementById('form:horaInicial');
	   				var horarioFinal = document.getElementById('form:horarioFinalIn');
	   				if(horarioInicial.value >= horarioFinal.value){
						exibirMensagem('<h:outputText value="#{Mensagens[\'parametros.horaFinalMenorhoraInicial\']}"/>', 'validarHora');
	   				}
	   				if (horarioInicial != null && horarioInicial.value != null && horarioFinal != null && horarioFinal.value != null) {
	   					if (horarioInicial.value.length == 5 && horarioFinal.value.length == 5) {
							if (horarioFinal.value == "00:00") {
								horarioFinal.value = "23:59";
							} else {
								var horIn = horarioInicial.value.replace(":", "");
								var horFin = horarioFinal.value.replace(":", "");
								horIn = parseInt(horIn, 10);
								horFin = parseInt(horFin, 10);
								if (horFin < horIn) {
									if (horFin > 600) {
										horarioFinal.value = "06:00";
									}
									exibirMensagem('<h:outputText value="#{Mensagens[\'dados.horario.evento.terminoDiaPosterior\']}"/>', 'terminoDiaPosterior');
									document.getElementById('form:horarioFinalIn').value="";
								}
							}
	   					} else {
	   	   					if (horarioInicial.value.length != 5)
	   	   						horarioInicial.value = "";
							if (horarioFinal.value.length != 5)
								horarioFinal.value = "";
	   					}
					}
	   			}
		
		</script>
		<body>
        <a4j:keepAlive beanName="AgendaVisitaControle" />
        <h:panelGroup layout="block" style="display: inline-table;width: 100%;" styleClass="fundoCinza">
            <h:panelGroup layout="block" styleClass="bgtop topoZW" rendered="#{MenuControle.apresentarTopo}">
                <jsp:include page="../../../include_topo_2.0.jsp" flush="true"/>
                <jsp:include page="../../../include_menu_ce_flat.jsp" flush="true"/>
            </h:panelGroup>

            <h:panelGroup layout="block" styleClass="caixaCorpo">
                <h:panelGroup layout="block" style="height: 80%;width: 100%">
                    <h:panelGroup layout="block" styleClass="caixaMenuLatel">
                        <h:panelGroup layout="block" styleClass="container-imagem">
                            <h:panelGroup layout="block" styleClass="container-box container-conteudo-central">
                                <h:panelGroup styleClass="container-box-header" layout="block">
                                    <h:panelGroup layout="block" styleClass="margin-box">
                                        <h:outputText styleClass="container-header-titulo" value="#{CElabels['menu.operacoesCE.agendaVisita']}"/>
                                        <h:outputLink styleClass="linkWiki"
                                                      value="#{SuperControle.urlWikiCE}Opera��es:AgendaVisita"
                                                      title="Clique e saiba mais: Agenda Visita"
                                                      target="_blank">
                                            <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                                        </h:outputLink>
                                    </h:panelGroup>
                                </h:panelGroup>
                                <h:panelGroup layout="block" styleClass="margin-box">
                                    <%@include file="../includes/include_agendaVisita.jsp" %>
                                </h:panelGroup>
                            </h:panelGroup>
                        </h:panelGroup>
                        <jsp:include page="../includes/include_box_menulateral.jsp" flush="true"/>
                    </h:panelGroup>
                </h:panelGroup>
            </h:panelGroup>

            <jsp:include page="../../../include_rodape_flat.jsp" flush="true" />
            <rich:jQuery id="mskData" selector=".rich-calendar-input" timing="onload" query="mask('99/99/9999')" />
        </h:panelGroup>
        </body>
        </html>
    </h:form>
    <%@include file="/pages/ce/includes/include_modal_existe_cliente_agendaVisita.jsp" %>
     <%@include file="/pages/ce/includes/include_modal_disponibilidades_agendaVisita.jsp" %>      
</f:view>

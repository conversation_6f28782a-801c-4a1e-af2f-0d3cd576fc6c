<%@include file="../includes/include_imports.jsp" %>
<%@include file="/includes/verificaModulo.jsp" %>
<head>
    <script type="text/javascript" language="javascript" src="../../../script/script.js"></script>
    <script type="text/javascript" language="javascript" src="../../../script/validarAmbiente.js"></script>
    <script type="text/javascript" language="javascript" src="../../../script/perfilEvento.js"></script>
</head>
<link href="${contexto}/beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>

<f:view>
    <jsp:include page="/includes/include_carregando_ripple.jsp" flush="true"/>
    <html>
    <!-- INCLUI O ELEMENTO HEAD NA PÁGINA -->
    <%@include file="../includes/include_head_form.jsp" %>

    <title><h:outputText
            value="#{CElabels['menu.cadastros.perfisEventos']}"/></title>


    <body>
    <c:set var="titulo" scope="session" value="Perfil Evento"/>
    <c:set var="urlWiki" scope="session" value="${SuperControle.urlWiki}Cadastros:Cadastros:PerfilEvento"/>
        <%-- INICIO HEADER --%>
    <f:facet name="header">
        <jsp:include page="../../../topoReduzido_material.jsp"/>
    </f:facet>


    <!-- FORMULÁRIO DE CADASTRO -->
    <h:form id="form">
        <hr style="border-color: #e6e6e6;"/>
        <input type="hidden" value="${modulo}" name="modulo"/>
        <!-- MANTEM O CONTROLLER NA ÁRVORE DE COMPONENTES -->
        <a4j:keepAlive beanName="PerfilEventoControle"/>

        <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar"
                     columnClasses="classEsquerda, classDireita" width="100%">

            <h:panelGroup>
                <%@include file="../includes/include_obrigatorio.jsp"%>
                <h:outputText styleClass="tituloCampos"
                              value="#{CElabels['entidade.perfilEvento.nome']}:" />
            </h:panelGroup>
            <h:panelGroup>
                <h:inputText id="descricao" size="50" maxlength="100"
                             onblur="blurinput(this); limparMsgObrig('form:descricao', 'descricao');"
                             onfocus="focusinput(this);" styleClass="form"
                             value="#{PerfilEventoControle.perfilEventoTO.descricao}" />
                <div id="divObg-descricao" class="mensagemObrigatorio"></div>
            </h:panelGroup>

        </h:panelGrid>

        <h:panelGroup>
            <table width="840">
                <tr align="center">
                    <td width="120"></td>
                    <td width="89"></td>
                    <td width="91"></td>
                    <td width="139"></td>
                    <td width="93"></td>
                    <td width="110"><a onmouseover="if (document.getElementById('hint-verificar').style.visibility != 'hidden') {toolTip('<h:outputText value="#{CElabels['entidade.hint.perfilEvento.dadosBasicos']}"/>' , 300 , 'blue')}"
                                       onmouseout="hideToolTip();"><img src="${contexto}/pages/ce/img/hint.gif" width="15px"
                                                                        id="hint-verificar" style="visibility: hidden;" height="15px" border="0px;" /></a></td>
                    <td width="158"></td>
                    <td width="173"></td>
                    <td width="100"></td>
                </tr>

            </table>
        </h:panelGroup>


        <rich:tabPanel width="100%" switchType="client">

            <rich:tab id="dadosPerfil"
                      label="#{CElabels['entidade.perfilEvento.dadosBasicos']}">
                <h:panelGrid id="panelDadosBasicos" columns="1" width="100%"
                             headerClass="subordinado" columnClasses="colunaCentralizada">
                    <f:facet name="header">
                        <h:outputText
                                value="#{CElabels['entidade.perfilEvento.dadosBasicos']}" />
                    </f:facet>
                    <h:panelGrid columns="2" rowClasses="linhaImparCE, linhaParCE"
                                 columnClasses="classEsquerda, classDireitaCE" width="100%">

                        <h:panelGroup>
                            <a onmouseover="if (document.getElementById('hint-dataInicio').style.visibility != 'hidden') {toolTip('<h:outputText value="#{CElabels['entidade.hint.perfilEvento.dataInicio']}"/>' , 300 , 'blue')}"
                               onmouseout="hideToolTip();"><img src="${contexto}/pages/ce/img/hint.gif" width="15px"
                                                                id="hint-dataInicio" style="visibility: hidden;" height="15px" border="0px;" /></a>&nbsp;&nbsp;&nbsp;
                            <%@include file="../includes/include_obrigatorio.jsp"%>
                            <h:outputText styleClass="tituloCampos"
                                          value="#{CElabels['entidade.dataInicio']}:" />
                        </h:panelGroup>
                        <h:panelGroup>
                            <rich:calendar id="dataInicio" styleClass="form"
                                           value="#{PerfilEventoControle.perfilEventoTO.dataInicio}"
                                           datePattern="dd/MM/yyyy" inputSize="10" inputClass="form"
                                           oninputblur="blurinput(this); limparMsgObrig('form:dataInicioInputDate', 'dataInicio'); limparMensagem('dataInicio2');"
                                           oninputfocus="focusinput(this);"
                                           oninputchange="if(!validar_Data(this.id)) {this.value = ''};"
                                           oninputkeypress="return mascara(this, '99/99/9999', event);"
                                           onchanged="limparMsgObrig('form:dataInicioInputDate', 'dataInicio'); limparMsgObrig('form:dataInicioInputDate', 'dataInicio2');"
                                           enableManualInput="true" zindex="2" showWeeksBar="false"
                                           disabled="#{PerfilEventoControle.perfilEventoTO.codigo gt 0}" />
                            <div id="divObg-dataInicio" class="mensagemObrigatorio"></div>
                        </h:panelGroup>

                        <h:panelGroup>
                            <%@include file="../includes/include_obrigatorio.jsp"%>
                            <h:outputText styleClass="tituloCampos"
                                          value="#{CElabels['entidade.dataTermino']}:" />
                        </h:panelGroup>
                        <h:panelGroup>
                            <rich:calendar id="dataTermino" styleClass="form"
                                           value="#{PerfilEventoControle.perfilEventoTO.dataTermino}"
                                           datePattern="dd/MM/yyyy" inputSize="10" inputClass="form"
                                           oninputblur="blurinput(this); limparMsgObrig('form:dataTerminoInputDate', 'dataTermino');limparMensagem('dataTermino2');"
                                           oninputfocus="focusinput(this);"
                                           oninputchange="if(!validar_Data(this.id)) {this.value = ''};"
                                           oninputkeypress="return mascara(this, '99/99/9999', event);"
                                           onchanged="limparMsgObrig('form:dataTerminoInputDate', 'dataTermino'); limparMsgObrig('form:dataTerminoInputDate', 'dataTermino2');"
                                           enableManualInput="true" zindex="2" showWeeksBar="false" />
                            <div id="divObg-dataTermino" class="mensagemObrigatorio"></div>
                        </h:panelGroup>

                        <h:panelGroup>
                            <%@include file="../includes/include_obrigatorio.jsp"%>
                            <h:outputText
                                    value="#{CElabels['entidade.perfilEvento.produtoPadrao']}:" />
                        </h:panelGroup>
                        <h:panelGroup>
                            <table>
                                <tr><td valign="middle">
                                    <h:inputText readonly="true" id="descricaoProduto" size="50"
                                                 maxlength="50"
                                                 onblur="blurinput(this); limparMsgObrig('form:descricaoProduto', 'descricaoProduto');"
                                                 onfocus="focusinput(this);" styleClass="form"
                                                 value="#{PerfilEventoControle.perfilEventoTO.produto.descricaoProduto}" />
                                </td>
                                    <td valign="middle">
                                        <a4j:commandButton id="consultaDadosProdutos"
                                                           focus="valorConsultaProduto"
                                                           title="#{CElabels['entidade.produtos.consulta']}"
                                                           oncomplete="Richfaces.showModalPanel('panelConsultaProduto'), setFocus(formConsultaProduto,'formConsultaProduto:valorConsultaProduto');"
                                                           image="/imagens/botoesCE/consulta_pedido.png" />
                                    </td>
                                    <td valign="middle">
                                        <a target="_self" id="produto"
                                           onclick="if(!confirm('Os dados editados do perfil serão perdidos. Deseja continuar?')){return false;};"
                                           href="${contexto}/faces/faces/produtoCons.jsp">
                                            <h:graphicImage value="/imagens/botoesCE/adicionar_produto.png" style="border: 0px;"
                                                            onmouseover="toolTip('#{CElabels['entidade.produto.cadastrar']}' , 120 , 'gray')"
                                                            onmouseout="hideToolTip();"/> </a>
                                    </td>
                                    <td valign="middle">
                                        <div id="divObg-descricaoProduto" class="mensagemObrigatorio"></div>
                                    </td>
                                </tr>
                            </table>

                        </h:panelGroup>

                        <h:panelGroup>
                            <%@include file="../includes/include_obrigatorio.jsp"%>
                            <h:outputText styleClass="tituloCampos"
                                          value="#{CElabels['entidade.perfilEvento.textoPadrao']}:" />
                        </h:panelGroup>
                        <h:panelGroup>
                            <rich:editor id="textoPadrao" useSeamText="false"
                                         viewMode="visual" width="400" height="200"
                                         value="#{PerfilEventoControle.perfilEventoTO.textoPadrao}"
                                         onchange="limparMsgObrigTextoPadrao();" />
                            <div id="divObg-textoPadrao" class="mensagemObrigatorio"></div>
                        </h:panelGroup>

                    </h:panelGrid>
                </h:panelGrid>
            </rich:tab>

            <rich:tab id="ambientes" label="#{CElabels['entidade.ambientes']}">
                <h:panelGrid id="panelAmbientes" columns="1" width="100%"
                             headerClass="subordinado" columnClasses="colunaCentralizada">
                    <rich:simpleTogglePanel switchType="client"
                                            opened="#{PerfilEventoControle.guiaVisualizada eq 1}"
                                            onexpand="return false;" oncollapse="return false;">
                        <f:facet name="header">
                            <h:outputText value="#{CElabels['entidade.ambientes']}" />
                        </f:facet>

                        <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar"
                                     columnClasses="classEsquerda, classDireita" width="100%">

                            <h:panelGroup>
                                <%@include file="../includes/include_obrigatorio.jsp"%>
                                <h:outputText value="#{CElabels['entidade.ambiente']}:" />
                            </h:panelGroup>
                            <h:panelGroup>
                                <h:selectOneMenu onblur="blurinput(this);"
                                                 disabled="#{PerfilEventoControle.edicaoPerfilEventoAmbiente}"
                                                 onfocus="focusinput(this);" styleClass="form" id="ambiente"
                                                 value="#{PerfilEventoControle.perfilEventoAmbienteTO.codigoAmbiente}"
                                                 onchange="limparMsgObrig('form:ambiente', 'ambiente');">
                                    <f:selectItem itemValue=""
                                                  itemLabel="#{CElabels['operacoes.selecione']}" />
                                    <f:selectItems value="#{PerfilEventoControle.itensAmbiente}" />
                                </h:selectOneMenu>
                                <div id="divObg-ambiente" class="mensagemObrigatorio"></div>
                            </h:panelGroup>

                            <h:panelGroup>
                                <%@include file="../includes/include_obrigatorio.jsp"%>
                                <h:outputText styleClass="tituloCampos"
                                              value="#{CElabels['entidade.valor']}:" />
                            </h:panelGroup>
                            <h:panelGroup>
                                <h:inputText id="valorPerfilEventoAmbiente"
                                             onblur="blurinput(this); limparMsgObrig('form:valorPerfilEventoAmbiente', 'valorPerfilEventoAmbiente');"
                                             onfocus="focusinput(this);"
                                             onkeypress="return(currencyFormat(this,'.',',',event));"
                                             styleClass="form" maxlength="14"
                                             value="#{PerfilEventoControle.perfilEventoAmbienteTO.valorFormatado}" />
                                <div id="divObg-valorPerfilEventoAmbiente"
                                     class="mensagemObrigatorio"></div>
                            </h:panelGroup>

                            <h:panelGroup>
                                <%@include file="../includes/include_obrigatorio.jsp"%>
                                <h:outputText styleClass="tituloCampos"
                                              value="#{CElabels['entidade.nrMaxConv']}:" />
                            </h:panelGroup>
                            <h:panelGroup>
                                <h:inputText id="numMaxConvidados"
                                             value="#{PerfilEventoControle.perfilEventoAmbienteTO.nrMaximoConvidados}"
                                             onkeypress="return mascara(this, '9999999', event);"
                                             styleClass="form">
                                </h:inputText>
                                <div id="divObg-nrMaximoConv" class="mensagemObrigatorio"></div>
                            </h:panelGroup>

                            <h:outputText styleClass="tituloCampos"
                                          value="#{CElabels['entidade.observacao']}:" />
                            <rich:editor id="observacao" useSeamText="false"
                                         viewMode="visual" width="400" height="200"
                                         value="#{PerfilEventoControle.perfilEventoAmbienteTO.observacao}" />

                        </h:panelGrid>

                        <center><a4j:commandButton
                                rendered="#{!PerfilEventoControle.edicaoPerfilEventoAmbiente}"
                                action="#{PerfilEventoControle.adicionarPerfilEventoAmbiente}"
                                reRender="panelAmbientes,panelMensagem"
                                value="#{CElabels['operacoes.adicionar']}"
                                image="/imagens/botaoAdicionar.png" styleClass="botoes"
                                onclick="if(!validarAmbiente()){return false;};"
                                oncomplete="limparMsgObrigAmbiente();" />
                            <a4j:commandButton
                                rendered="#{PerfilEventoControle.edicaoPerfilEventoAmbiente}"
                                action="#{PerfilEventoControle.confirmarAlteracaoPerfilEventoAmbiente}"
                                reRender="panelAmbientes,panelMensagem"
                                value="#{CElabels['operacoes.editar.confirmar']}"
                                image="/imagens/bt_editar.png" styleClass="botoes"
                                onclick="if(!validarAmbiente()){return false;};" /></center>

                        <h:panelGrid columns="1" width="100%"
                                     styleClass="tabFormSubordinada">
                            <h:dataTable id="tablePerfilEventoAmbiente" width="100%"
                                         headerClass="subordinado" styleClass="tabFormSubordinada"
                                         rowClasses="linhaImpar, linhaPar"
                                         columnClasses="colunaAlinhamento"
                                         value="#{PerfilEventoControle.perfilEventoTO.perfilEventoAmbienteTOs}"
                                         var="perfilEventoAmbiente">

                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText value="#{CElabels['entidade.ambiente']}" />
                                    </f:facet>
                                    <h:outputText
                                            value="#{perfilEventoAmbiente.descricaoAmbiente}" />
                                </h:column>
                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText value="#{CElabels['entidade.valor']}" />
                                    </f:facet>
                                    <h:outputText value="#{perfilEventoAmbiente.valorMonetario}" />
                                </h:column>
                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText value="#{CElabels['entidade.nrMaxConv']}" />
                                    </f:facet>
                                    <h:outputText
                                            value="#{perfilEventoAmbiente.nrMaximoConvidados}" />
                                </h:column>
                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText value="#{CElabels['operacoes.opcoes']}" />
                                    </f:facet>
                                    <h:panelGroup>
                                        <a4j:commandLink id="editarPerfilEventoAmbiente"
                                                         reRender="panelAmbientes,panelSazonalidades, panelLayouts, panelMensagem"
                                                         ajaxSingle="true" immediate="true"
                                                         action="#{PerfilEventoControle.editarPerfilEventoAmbiente}"
                                                         title="#{CElabels['operacoes.editar']}"
                                                         styleClass="botoes">
                                            <h:graphicImage value="/imagens/bt_editar.png" style="border: 0px;"/>
                                        </a4j:commandLink>&nbsp;&nbsp;


                                        <a4j:commandLink id="removerPerfilEventoAmbiente"
                                                         reRender="panelAmbientes,panelSazonalidades, panelLayouts, panelMensagem" ajaxSingle="true" immediate="true"
                                                         action="#{PerfilEventoControle.removerPerfilEventoAmbiente}"
                                                         title="#{CElabels['operacoes.excluir']}"
                                                         rendered="#{!perfilEventoAmbiente.contemEvento}"
                                                         styleClass="botoes">
                                            <h:graphicImage value="/imagens/bt_excluir.png" style="border: 0px;"/>
                                        </a4j:commandLink>&nbsp;&nbsp;

                                        <a4j:commandLink id="adicionarSazonalidade"
                                                         reRender="panelAmbientes,panelSazonalidades, panelLayouts, panelMensagem"
                                                         ajaxSingle="true" immediate="true"
                                                         title="#{CElabels['entidade.perfilEventoAmbiente.addSazon']}"
                                                         action="#{PerfilEventoControle.visualizarPerfilEventoSazonalidade}">
                                            <h:graphicImage value="/imagens/botoesCE/sazonalidade.png" style="border: 0px;"/>
                                        </a4j:commandLink>&nbsp;&nbsp;
                                        <a4j:commandLink id="adicionarLayout"
                                                         reRender="panelAmbientes,panelSazonalidades, panelLayouts, panelMensagem"
                                                         ajaxSingle="true" immediate="true"
                                                         action="#{PerfilEventoControle.visualizarLayouts}"
                                                         title="#{CElabels['entidade.perfilEventoAmbiente.addLayout']}">
                                            <h:graphicImage value="/imagens/botoesCE/adicionar_layout.png" style="border: 0px;"/>
                                        </a4j:commandLink>
                                    </h:panelGroup>
                                </h:column>

                            </h:dataTable>
                        </h:panelGrid>
                    </rich:simpleTogglePanel>
                </h:panelGrid>

                <h:panelGrid id="panelSazonalidades" columns="1" width="100%"
                             headerClass="subordinado" columnClasses="colunaCentralizada">
                    <rich:simpleTogglePanel switchType="client"
                                            opened="#{PerfilEventoControle.guiaVisualizada eq 2}"
                                            onexpand="return false;" oncollapse="return false;">
                        <f:facet name="header">
                            <h:outputText
                                    value="#{CElabels['entidade.perfilEventoAmbiente.sazonalidades']}" />
                        </f:facet>

                        <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar"
                                     columnClasses="classEsquerda, classDireita" width="100%">

                            <h:panelGroup>
                                <%@include file="../includes/include_obrigatorio.jsp"%>
                                <h:outputText styleClass="tituloCampos"
                                              value="#{CElabels['entidade.perfilEvento.ambienteEsc']}:" />
                            </h:panelGroup>
                            <h:panelGroup>
                                <h:inputText id="ambientePerfilEventoSazonalidade"
                                             onblur="blurinput(this); limparMsgObrig('form:ambientePerfilEventoSazonalidade', 'ambientePerfilEventoSazonalidade');"
                                             onfocus="focusinput(this);" styleClass="form" readonly="true"
                                             value="#{PerfilEventoControle.descricaoAmbiente}" />
                                <div id="divObg-ambientePerfilEventoSazonalidade"
                                     class="mensagemObrigatorio"></div>
                            </h:panelGroup>

                            <h:panelGroup>

                                <h:outputText value="#{CElabels['entidade.diaSemana']}:" />
                            </h:panelGroup>
                            <h:panelGroup>
                                <h:selectBooleanCheckbox value="#{PerfilEventoControle.perfilEventoSazonalidadeTO.seg}"/>
                                <h:outputText styleClass="text" value="Seg." />
                                <h:selectBooleanCheckbox value="#{PerfilEventoControle.perfilEventoSazonalidadeTO.ter}"/>
                                <h:outputText styleClass="text" value="Ter." />
                                <h:selectBooleanCheckbox value="#{PerfilEventoControle.perfilEventoSazonalidadeTO.qua}"/>
                                <h:outputText styleClass="text" value="Qua." />
                                <h:selectBooleanCheckbox value="#{PerfilEventoControle.perfilEventoSazonalidadeTO.qui}"/>
                                <h:outputText styleClass="text" value="Qui." />
                                <h:selectBooleanCheckbox value="#{PerfilEventoControle.perfilEventoSazonalidadeTO.sex}"/>
                                <h:outputText styleClass="text" value="Sex." />
                                <h:selectBooleanCheckbox value="#{PerfilEventoControle.perfilEventoSazonalidadeTO.sab}"/>
                                <h:outputText styleClass="text" value="Sab." />
                                <h:selectBooleanCheckbox value="#{PerfilEventoControle.perfilEventoSazonalidadeTO.dom}"/>
                                <h:outputText styleClass="text" value="Dom." />

                                <!--
                                <h:selectOneMenu onblur="blurinput(this);"
                                                 onfocus="focusinput(this);" styleClass="form"
                                                 id="diaSemanaSazonalidade"
                                                 value="#{PerfilEventoControle.perfilEventoSazonalidadeTO.diaSemana}"
                                                 onchange="limparMsgObrig('form:diaSemanaSazonalidade', 'diaSemanaSazonalidade');">
                                    <f:selectItem itemValue=""
                                                  itemLabel="#{CElabels['operacoes.selecione']}" />
                                    <f:selectItems value="#{PerfilEventoControle.diasSemana}" />
                                </h:selectOneMenu>-->

                            </h:panelGroup>

                            <h:panelGroup>
                                <%@include file="../includes/include_obrigatorio.jsp"%>
                                <h:outputText value="#{CElabels['entidade.dataInicio']}:" />
                            </h:panelGroup>
                            <h:panelGroup>
                                <rich:calendar id="dataInicioSazonalidade" styleClass="form"
                                               value="#{PerfilEventoControle.perfilEventoSazonalidadeTO.dataInicio}"
                                               datePattern="dd/MM/yyyy" inputSize="10" inputClass="form"
                                               oninputblur="blurinput(this); limparMsgObrig('form:dataInicioSazonalidadeInputDate', 'dataInicioSazonalidade');"
                                               onchanged="limparMsgObrig('form:dataInicioSazonalidadeInputDate', 'dataInicioSazonalidade');"
                                               oninputfocus="focusinput(this);"
                                               oninputchange="if(!validar_Data(this.id)) {this.value = ''};"
                                               oninputkeypress="return mascara(this, '99/99/9999', event);"
                                               enableManualInput="true" zindex="2" showWeeksBar="false" />
                                <div id="divObg-dataInicioSazonalidade"
                                     class="mensagemObrigatorio"></div>
                            </h:panelGroup>

                            <h:panelGroup>
                                <%@include file="../includes/include_obrigatorio.jsp"%>
                                <h:outputText styleClass="tituloCampos"
                                              value="#{CElabels['entidade.dataFim']}:" />
                            </h:panelGroup>
                            <h:panelGroup>
                                <rich:calendar id="dataFimSazonalidade" styleClass="form"
                                               value="#{PerfilEventoControle.perfilEventoSazonalidadeTO.dataFim}"
                                               datePattern="dd/MM/yyyy" inputSize="10" inputClass="form"
                                               oninputblur="blurinput(this); limparMsgObrig('form:dataFimSazonalidadeInputDate', 'dataFimSazonalidade');"
                                               onchanged="limparMsgObrig('form:dataFimSazonalidadeInputDate', 'dataFimSazonalidade');"
                                               oninputfocus="focusinput(this);"
                                               oninputchange="if(!validar_Data(this.id)) {this.value = ''};"
                                               oninputkeypress="return mascara(this, '99/99/9999', event);"
                                               enableManualInput="true" zindex="2" showWeeksBar="false" />
                                <div id="divObg-dataFimSazonalidade"
                                     class="mensagemObrigatorio"></div>
                            </h:panelGroup>

                            <h:panelGroup>
                                <%@include file="../includes/include_obrigatorio.jsp"%>
                                <h:outputText value="#{CElabels['entidade.tipoOperacao']}:" />
                            </h:panelGroup>
                            <h:panelGroup>
                                <h:selectOneMenu onblur="blurinput(this);"
                                                 onfocus="focusinput(this);" styleClass="form"
                                                 onchange="limparMsgObrig('form:tipoOperacaoSazonalidade', 'tipoOperacaoSazonalidade');"
                                                 id="tipoOperacaoSazonalidade"
                                                 value="#{PerfilEventoControle.perfilEventoSazonalidadeTO.tipoOperacao}">
                                    <f:selectItem itemValue=""
                                                  itemLabel="#{CElabels['operacoes.selecione']}" />
                                    <f:selectItems value="#{PerfilEventoControle.tiposOperacao}" />
                                </h:selectOneMenu>
                                <div id="divObg-tipoOperacaoSazonalidade"
                                     class="mensagemObrigatorio"></div>
                            </h:panelGroup>

                            <h:panelGroup>
                                <%@include file="../includes/include_obrigatorio.jsp"%>
                                <h:outputText value="#{CElabels['entidade.formaCalculo']}:" />
                            </h:panelGroup>
                            <h:panelGroup>
                                <h:selectOneMenu onblur="blurinput(this);"
                                                 onfocus="focusinput(this);" styleClass="form"
                                                 id="formaCalculoSazonalidade"
                                                 value="#{PerfilEventoControle.perfilEventoSazonalidadeTO.formaCalculo}">
                                    <f:selectItem itemValue=""
                                                  itemLabel="#{CElabels['operacoes.selecione']}" />
                                    <f:selectItems value="#{PerfilEventoControle.formasCalculo}" />
                                    <a4j:support event="onchange"
                                                 action="#{PerfilEventoControle.mostraCalculo}"
                                                 reRender="exibe" />
                                </h:selectOneMenu>&nbsp;
                                <h:outputText styleClass="calculo" id="exibe"
                                              value="#{PerfilEventoControle.exibeCalculo}" />
                                <div id="divObg-formaCalculoSazonalidade"
                                     class="mensagemObrigatorio"></div>
                            </h:panelGroup>

                            <h:panelGroup>
                                <%@include file="../includes/include_obrigatorio.jsp"%>
                                <h:outputText styleClass="tituloCampos"
                                              value="#{CElabels['entidade.valor']}:" />
                            </h:panelGroup>
                            <h:panelGroup>
                                <h:inputText id="valorPerfilEventoSazonalidade"
                                             onkeypress="return(currencyFormat(this,'.',',',event));"
                                             onfocus="focusinput(this);" styleClass="form" maxlength="14"
                                             value="#{PerfilEventoControle.perfilEventoSazonalidadeTO.valorFormatado}">
                                    <a4j:support event="onblur"
                                                 action="#{PerfilEventoControle.mostraCalculo}"
                                                 reRender="exibe" />
                                </h:inputText>
                                <div id="divObg-valorPerfilEventoSazonalidade"
                                     class="mensagemObrigatorio"></div>
                            </h:panelGroup>

                        </h:panelGrid>

                        <center><a4j:commandButton
                                rendered="#{!PerfilEventoControle.edicaoPerfilEventoSazonalidade}"
                                action="#{PerfilEventoControle.adicionarPerfilEventoSazonalidade}"
                                reRender="panelSazonalidades,panelMensagem"
                                value="#{CElabels['operacoes.adicionar']}"
                                image="/imagens/botaoAdicionar.png" styleClass="botoes"
                                onclick="if(!validarSazonalidade()){return false;};" />

                            <a4j:commandButton
                                    rendered="#{PerfilEventoControle.edicaoPerfilEventoSazonalidade}"
                                    action="#{PerfilEventoControle.confirmarAlteracaoPerfilEventoSazonalidade}"
                                    reRender="panelSazonalidades,panelMensagem"
                                    value="#{CElabels['operacoes.editar.confirmar']}"
                                    image="/imagens/bt_editar.png" styleClass="botoes" /></center>

                        <h:panelGrid columns="1" width="100%"
                                     styleClass="tabFormSubordinada">
                            <h:dataTable id="tablePerfilEventoSazonalidade" width="100%"
                                         headerClass="subordinado" styleClass="tabFormSubordinada"
                                         rowClasses="linhaImpar, linhaPar"
                                         columnClasses="colunaAlinhamento"
                                         value="#{PerfilEventoControle.perfilEventoSazonalidadeTOs}"
                                         var="perfilEventoSazonalidade">

                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText value="#{CElabels['entidade.diaSemana']}" />
                                    </f:facet>
                                    <h:outputText
                                            value="#{perfilEventoSazonalidade.descricaoDiaSemana}" />
                                </h:column>
                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText value="#{CElabels['entidade.dataInicio']}" />
                                    </f:facet>
                                    <h:outputText
                                            value="#{perfilEventoSazonalidade.dataInicioFormatada}" />
                                </h:column>
                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText value="#{CElabels['entidade.dataFim']}" />
                                    </f:facet>
                                    <h:outputText
                                            value="#{perfilEventoSazonalidade.dataFimFormatada}" />
                                </h:column>
                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText value="#{CElabels['entidade.tipoOperacao']}" />
                                    </f:facet>
                                    <h:outputText
                                            value="#{perfilEventoSazonalidade.descricaoTipoOperacao}" />
                                </h:column>
                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText value="#{CElabels['entidade.formaCalculo']}" />
                                    </f:facet>
                                    <h:outputText
                                            value="#{perfilEventoSazonalidade.descricaoFormaCalculo}" />
                                </h:column>
                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText value="#{CElabels['entidade.valor']}" />
                                    </f:facet>
                                    <h:outputText
                                            value="#{perfilEventoSazonalidade.valorMonetario}" />
                                </h:column>
                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText value="#{CElabels['operacoes.opcoes']}" />
                                    </f:facet>
                                    <a4j:commandLink id="editarPerfilEventoSazonalidade"
                                                     reRender="panelSazonalidades,panelMensagem" ajaxSingle="true" immediate="true"
                                                     action="#{PerfilEventoControle.editarPerfilEventoSazonalidade}"
                                                     title="#{CElabels['operacoes.editar']}"
                                                     styleClass="botoes">
                                        <h:graphicImage url="/imagens/bt_editar.png"></h:graphicImage>
                                    </a4j:commandLink>
                                    &nbsp;&nbsp;
                                    <a4j:commandLink id="removerPerfilEventoSazonalidade"
                                                     reRender="panelSazonalidades,panelMensagem" ajaxSingle="true" immediate="true"
                                                     action="#{PerfilEventoControle.removerPerfilEventoSazonalidade}"
                                                     title="#{CElabels['operacoes.excluir']}"
                                                     styleClass="botoes">
                                        <h:graphicImage url="/imagens/bt_excluir.png"></h:graphicImage>
                                    </a4j:commandLink>
                                </h:column>

                            </h:dataTable>
                        </h:panelGrid>

                        <center><a4j:commandLink
                                action="#{PerfilEventoControle.fecharVisualizacaoPerfilEventoSazonalidade}"
                                reRender="form"
                                styleClass="botoes">
                            <h:graphicImage value="/imagens/botoesCE/visual_ambiente.png" style="border: 0px;"
                                            onmouseover="toolTip('#{CElabels['entidade.perfilEventoAmbiente.visualizar']}' , 120 , 'gray')"
                                            onmouseout="hideToolTip();"/>
                        </a4j:commandLink> </center>
                    </rich:simpleTogglePanel>
                </h:panelGrid>


                <h:panelGrid id="panelLayouts" columns="1" width="100%"
                             headerClass="subordinado" columnClasses="colunaCentralizada">
                    <rich:simpleTogglePanel switchType="client"
                                            opened="#{PerfilEventoControle.guiaVisualizada eq 3}"
                                            onexpand="return false;" oncollapse="return false;">
                        <f:facet name="header">
                            <h:outputText value="#{CElabels['entidade.layouts']}" />
                        </f:facet>

                        &nbsp;<h:outputText styleClass="mensagemAzul" value="#{CElabels['entidade.layouts.tipoArquivo']}"/>

                        <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar"
                                     columnClasses="classEsquerda, classDireita" width="100%">

                            <h:panelGroup>
                                <%@include file="../includes/include_obrigatorio.jsp"%>
                                <h:outputText styleClass="tituloCampos"
                                              value="#{CElabels['entidade.perfilEvento.ambienteEsc']}:" />
                            </h:panelGroup>
                            <h:panelGroup>
                                <h:inputText id="ambienteLayout"
                                             onblur="blurinput(this); limparMsgObrig('form:ambientePerfilEventoSazonalidade', 'ambientePerfilEventoSazonalidade');"
                                             onfocus="focusinput(this);" styleClass="form" readonly="true"
                                             value="#{PerfilEventoControle.descricaoAmbiente}" />
                                <div id="divObg-ambientePerfilEventoSazonalidade"
                                     class="mensagemObrigatorio"></div>
                            </h:panelGroup>

                            <h:panelGroup>
                                <%@include file="../includes/include_obrigatorio.jsp"%>
                                <h:outputText value="#{CElabels['entidade.arquivo']}:" />
                            </h:panelGroup>
                            <h:panelGroup>
                                <rich:fileUpload
                                        fileUploadListener="#{PerfilEventoControle.uploadLayoutListener}"
                                        maxFilesQuantity="#{PerfilEventoControle.numeroArquivosLayout}"
                                        immediateUpload="false" acceptedTypes="jpg, gif, png, bmp, doc, docx, xls, xlsx"
                                        allowFlash="false" listHeight="58px" id="layoutUpload"
                                        onfileuploadcomplete="document.getElementById('form:arquivoLayoutCarregado').value = 'true';"
                                        cancelEntryControlLabel="#{CElabels['operacoes.cancelar']}"
                                        addControlLabel="#{CElabels['operacoes.adicionar']}"
                                        clearControlLabel="#{CElabels['operacoes.remover']}"
                                        clearAllControlLabel="#{CElabels['operacoes.remover.todos']}"
                                        doneLabel="#{CElabels['operacoes.concluido']}"
                                        sizeErrorLabel="#{Mensagens['operacoes.arquivo.upload.tamanhoLimiteExcedido']}"
                                        uploadControlLabel="#{CElabels['operacoes.carregar']}"
                                        transferErrorLabel="#{Mensagens['operacoes.arquivo.upload.erroTransferencia']}"
                                        stopControlLabel="#{CElabels['operacoes.parar']}"
                                        stopEntryControlLabel="#{CElabels['operacoes.parar']}"
                                        progressLabel="#{Mensagens['operacoes.arquivo.upload.carregando']}">
                                    <a4j:support event="onclear"
                                                 action="#{PerfilEventoControle.removerArquivoLayout}"
                                                 reRender="layoutUpload, arquivoLayoutCarregado" />
                                    <a4j:support event="onadd"
                                                 action="#{PerfilEventoControle.decrementarNumeroArquivosLayout}" />
                                </rich:fileUpload>
                                <div id="divObg-arquivoLayout" class="mensagemObrigatorio"></div>
                            </h:panelGroup>

                            <h:panelGroup>
                                <%@include file="../includes/include_obrigatorio.jsp"%>
                                <h:outputText value="#{CElabels['entidade.descricao']}:" />
                            </h:panelGroup>
                            <h:panelGroup>
                                <rich:editor id="descricaoLayout" useSeamText="false"
                                             viewMode="visual" width="400" height="200"
                                             value="#{PerfilEventoControle.layout.descricao}"
                                             onchange="limparMsgObrigTextoPadrao();" />
                                <div id="divObg-descricaoLayout" class="mensagemObrigatorio"></div>
                            </h:panelGroup>

                        </h:panelGrid>

                        <center><a4j:commandButton
                                action="#{PerfilEventoControle.adicionarLayout}"
                                reRender="panelLayouts, arquivoLayoutCarregado, panelMensagem"
                                onclick="if(!validarLayout()){return false;};"
                                value="#{CElabels['operacoes.adicionar']}"
                                image="/imagens/botaoAdicionar.png" styleClass="botoes" /></center>

                        <h:panelGrid columns="1" width="100%"
                                     styleClass="tabFormSubordinada">
                            <h:dataTable id="tableLayout" width="100%"
                                         headerClass="subordinado" styleClass="tabFormSubordinada"
                                         rowClasses="linhaImpar, linhaPar"
                                         columnClasses="colunaAlinhamento"
                                         value="#{PerfilEventoControle.layouts}" var="layout">

                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText value="#{CElabels['entidade.nome']}" />
                                    </f:facet>
                                    <h:outputText value="#{layout.nomeArquivo}" />
                                </h:column>

                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText value="#{CElabels['entidade.descricao']}" />
                                    </f:facet>
                                    <h:outputText escape="false" value="#{layout.descricao}" />
                                </h:column>

                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText value="#{CElabels['operacoes.opcoes']}" />
                                    </f:facet>
                                    <a4j:commandLink
                                            rendered="#{!layout.contemEvento}"
                                            action="#{PerfilEventoControle.removerLayout}"
                                            reRender="tableLayout">
                                        <h:graphicImage value="/imagens/bt_excluir.png"
                                                        style="border: 0px;"
                                                        alt="#{CElabels['operacoes.layout.remover']}" />
                                    </a4j:commandLink>&nbsp;&nbsp;
                                    <!-- Download da imagem do Layout -->
                                    <a4j:commandLink
                                            style="margin-top:5px;"
                                            action="#{PerfilEventoControle.exibir}" immediate="true" >
                                        <h:graphicImage value="/imagens/botoesCE/download_layout.png"
                                                        style="border: 0px;"
                                                        alt="#{CElabels['menu.operacoesCE.downloadLayout']}" />
                                    </a4j:commandLink>
                                </h:column>
                            </h:dataTable>
                        </h:panelGrid>

                        <center><a4j:commandLink
                                action="#{PerfilEventoControle.fecharVisualizacaoLayouts}"
                                reRender="form"
                                styleClass="botoes">
                            <h:graphicImage value="/imagens/botoesCE/visual_ambiente.png" style="border: 0px;"
                                            onmouseover="toolTip('#{CElabels['entidade.perfilEventoAmbiente.visualizar']}' , 120 , 'gray')"
                                            onmouseout="hideToolTip();"/>
                        </a4j:commandLink>
                        </center>
                    </rich:simpleTogglePanel>
                </h:panelGrid>

                <h:inputHidden id="arquivoLayoutCarregado"
                               value="#{PerfilEventoControle.arquivoLayoutCarregado}" />
            </rich:tab>

            <rich:tab id="servicos" label="#{CElabels['entidade.servicos']}">
                <h:panelGrid id="panelServicos" columns="1" width="100%"
                             headerClass="subordinado" columnClasses="colunaCentralizada">
                    <f:facet name="header">
                        <h:outputText value="#{CElabels['entidade.servicos']}" />
                    </f:facet>
                    <h:outputText styleClass="tituloCampos"
                                  value="#{CElabels['entidade.perfilEvento.permiteOutrosSer']}" />
                    <h:selectBooleanCheckbox id="permiteOutrosServicos"
                                             styleClass="campos"
                                             value="#{PerfilEventoControle.perfilEventoTO.permiteOutrosServicos}" />

                    <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar"
                                 columnClasses="classEsquerda, classDireita" width="100%">

                        <h:panelGroup>
                            <%@include file="../includes/include_obrigatorio.jsp"%>
                            <h:outputText value="#{CElabels['entidade.servico']}:" />
                        </h:panelGroup>
                        <h:panelGroup>
                            <table><tr>

                                <td valign="middle">
                                    <h:inputText readonly="true" id="descricaoServico" size="50"
                                                 maxlength="50"
                                                 onblur="blurinput(this); limparMsgObrig('form:descricaoServico', 'descricaoServico');"
                                                 onfocus="focusinput(this);" styleClass="form"
                                                 value="#{PerfilEventoControle.servico.descricaoServico}" /></td>
                                <td valign="middle">
                                    <a4j:commandButton id="consultaDadosServicos"
                                                       focus="valorConsultaServico"
                                                       title="#{CElabels['entidade.servicos.consulta']}"
                                                       reRender="formConsultaServico"
                                                       oncomplete="Richfaces.showModalPanel('panelConsultaServico'), setFocus(formConsultaServico,'formConsultaServico:valorConsultaServico');"
                                                       image="/imagens/botoesCE/adicionar_produto.png"
                                                       rendered="#{!PerfilEventoControle.edicaoPerfilEventoServico}" /></td>

                            </tr></table>
                            <div id="divObg-descricaoServico" class="mensagemObrigatorio"></div>
                        </h:panelGroup>

                        <h:panelGroup>
                            <h:outputText value="#{CElabels['entidade.valor']}:" />
                        </h:panelGroup>
                        <h:panelGroup>
                            <h:outputText value="#{PerfilEventoControle.servico.valorMonetario}" />

                        </h:panelGroup>

                        <h:panelGroup>
                            <%--<%@include file="../includes/include_obrigatorio.jsp" %>--%>
                            <h:outputText styleClass="tituloCampos" value="#{CElabels['entidade.fornecedor']}" />
                        </h:panelGroup>
                        <h:panelGroup>
                            <h:selectOneMenu onblur="blurinput(this); limparMsgObrig('form:fornecedor', 'fornecedor');"
                                             onfocus="focusinput(this);" styleClass="form" id="fornecedor"
                                             valueChangeListener="#{PerfilEventoControle.fornecedorListener}"
                                             value="#{PerfilEventoControle.servico.codigoFornecedor}">
                                <f:selectItem itemValue="0" itemLabel="--Selecione--"/>
                                <f:selectItems value="#{PerfilEventoControle.listaFornecedor}" />
                            </h:selectOneMenu>
                            <div id="divObg-fornecedor" class="mensagemObrigatorio"></div>
                        </h:panelGroup>

                        <h:outputText styleClass="tituloCampos"
                                      value="#{CElabels['entidade.textoLivre']}:" />
                        <rich:editor id="textoLivreServico" useSeamText="false"
                                     viewMode="visual" width="400" height="200"
                                     value="#{PerfilEventoControle.servico.textoLivre}"
                                     readonly="#{PerfilEventoControle.edicaoPerfilEventoServico and PerfilEventoControle.servico.codigo gt 0}" />



                        <h:outputText styleClass="tituloCampos"
                                      value="#{CElabels['entidade.obrigatorio']}:" />
                        <h:selectBooleanCheckbox id="obrigPerfilEventoServico"
                                                 styleClass="campos"
                                                 value="#{PerfilEventoControle.servico.obrigatorio}"
                                                 disabled="#{PerfilEventoControle.temServicoAnterior
													|| (PerfilEventoControle.edicaoPerfilEventoServico and PerfilEventoControle.servico.codigo gt 0)}" />


                    </h:panelGrid>

                    <a4j:commandButton
                            rendered="#{!PerfilEventoControle.edicaoPerfilEventoServico}"
                            action="#{PerfilEventoControle.adicionarPerfilEventoServico}"
                            reRender="panelServicos, panelMensagem"
                            value="#{CElabels['operacoes.adicionar']}"
                            image="/imagens/botaoAdicionar.png" styleClass="botoes"
                            onclick="if(!validarServico()){return false;};" />

                    <a4j:commandButton
                            rendered="#{PerfilEventoControle.edicaoPerfilEventoServico}"
                            action="#{PerfilEventoControle.confirmarAlteracaoPerfilEventoServico}"
                            reRender="panelServicos, panelMensagem"
                            value="#{CElabels['operacoes.editar.confirmar']}"
                            image="/imagens/bt_editar.png" styleClass="botoes"
                            onclick="if(!validarServico()){return false;};" />

                    <h:panelGrid columns="1" width="100%"
                                 styleClass="tabFormSubordinada">
                        <h:dataTable id="tablePerfilEventoServico" width="100%"
                                     headerClass="subordinado" styleClass="tabFormSubordinada"
                                     rowClasses="linhaImpar, linhaPar"
                                     columnClasses="colunaAlinhamento"
                                     value="#{PerfilEventoControle.perfilEventoTO.perfilEventoServicoTOs}"
                                     var="perfilEventoServico">

                            <h:column>
                                <f:facet name="header">
                                    <h:outputText value="#{CElabels['entidade.servico']}" />
                                </f:facet>
                                <h:outputText value="#{perfilEventoServico.descricaoServico}" />
                            </h:column>
                            <h:column>
                                <f:facet name="header">
                                    <h:outputText value="#{CElabels['entidade.fornecedor']}" />
                                </f:facet>
                                <h:outputText value="#{perfilEventoServico.descricaoFornecedor}" />
                            </h:column>
                            <h:column>
                                <f:facet name="header">
                                    <h:outputText value="#{CElabels['entidade.valor']}" />
                                </f:facet>
                                <h:outputText value="#{perfilEventoServico.valorMonetario}" />
                            </h:column>

                            <h:column>
                                <f:facet name="header">
                                    <h:outputText value="#{CElabels['operacoes.opcoes']}" />
                                </f:facet>
                                <a4j:commandButton id="editarPerfilEventoServico"
                                                   reRender="panelServicos" ajaxSingle="true" immediate="true"
                                                   action="#{PerfilEventoControle.editarPerfilEventoServico}"
                                                   value="#{CElabels['operacoes.editar']}"
                                                   image="/imagens/bt_editar.png" styleClass="botoes"/>&nbsp;&nbsp;
                                <a4j:commandButton id="removerPerfilEventoServico"
                                                   reRender="panelServicos" ajaxSingle="true" immediate="true"
                                                   action="#{PerfilEventoControle.removerPerfilEventoServico}"
                                                   value="#{CElabels['operacoes.excluir']}"
                                                   image="/imagens/bt_excluir.png" styleClass="botoes"
                                                   rendered="#{!(perfilEventoServico.codigo gt 0)}"/>
                            </h:column>

                        </h:dataTable>
                    </h:panelGrid>

                </h:panelGrid>
            </rich:tab>


            <!-- INICIO - BENS DE CONSUMO -->
            <rich:tab id="bensConsumo"
                      label="#{CElabels['entidade.bensConsumo']}">
                <h:panelGrid id="panelBensConsumo" columns="1" width="100%"
                             headerClass="subordinado" columnClasses="colunaCentralizada">
                    <f:facet name="header">
                        <h:outputText value="#{CElabels['entidade.bensConsumo']}" />
                    </f:facet>

                    <h:outputText styleClass="tituloCampos"
                                  value="#{CElabels['entidade.perfilEvento.permiteOutrosBens']}" />
                    <h:selectBooleanCheckbox id="permiteOutrosBensConsumo"
                                             styleClass="campos"
                                             value="#{PerfilEventoControle.perfilEventoTO.permiteOutrosBensConsumo}" />

                    <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar"
                                 columnClasses="classEsquerda, classDireita" width="100%">

                        <h:panelGroup>
                            <%@include file="../includes/include_obrigatorio.jsp"%>
                            <h:outputText value="#{CElabels['entidade.bemConsumo']}:" />
                        </h:panelGroup>
                        <h:panelGroup>
                            <table>
                                <tr>
                                    <td valign="middle"><h:inputText readonly="true" id="descricaoBemConsumo" size="50"
                                                                     maxlength="50"
                                                                     onblur="blurinput(this); limparMsgObrig('form:descricaoBemConsumo', 'descricaoBemConsumo');"
                                                                     onfocus="focusinput(this);" styleClass="form"
                                                                     value="#{PerfilEventoControle.bemConsumo.descricaoProdutoLocacao}" />
                                    </td>
                                    <td valign="middle"><a4j:commandButton id="consultaDadosBensConsumo"
                                                                           focus="valorConsultaBemConsumo"
                                                                           title="#{CElabels['entidade.bensConsumo.consulta']}"
                                                                           reRender="formConsultaBemConsumo"
                                                                           oncomplete="Richfaces.showModalPanel('panelConsultaBemConsumo'), setFocus(formConsultaBemConsumo,'formConsultaBemConsumo:valorConsultaBemConsumo');"
                                                                           image="/imagens/botoesCE/adicionar_produto.png"
                                                                           rendered="#{!PerfilEventoControle.edicaoPerfilEventoBemConsumo}" />
                                    </td>
                                </tr>
                            </table>
                            <div id="divObg-descricaoBemConsumo"
                                 class="mensagemObrigatorio"></div>
                        </h:panelGroup>

                        <h:panelGroup>
                            <h:outputText value="#{CElabels['entidade.valor']}:" />
                        </h:panelGroup>
                        <h:panelGroup>
                            <h:outputText value="#{PerfilEventoControle.bemConsumo.valorMonetario}" />

                        </h:panelGroup>

                        <h:outputText styleClass="tituloCampos"
                                      value="#{CElabels['entidade.textoLivre']}:" />
                        <rich:editor id="textoLivreBemConsumo" useSeamText="false"
                                     viewMode="visual" width="400" height="200"
                                     value="#{PerfilEventoControle.bemConsumo.textoLivre}"/>

                        <h:outputText styleClass="tituloCampos"
                                      value="#{CElabels['entidade.obrigatorio']}:" />
                        <h:selectBooleanCheckbox id="obrigPerfilEventoBemConsumo"
                                                 styleClass="campos"
                                                 value="#{PerfilEventoControle.bemConsumo.obrigatorio}"/>

                    </h:panelGrid>

                    <a4j:commandButton
                            rendered="#{!PerfilEventoControle.edicaoPerfilEventoBemConsumo}"
                            action="#{PerfilEventoControle.adicionarPerfilEventoBemConsumo}"
                            reRender="panelBensConsumo, panelMensagem"
                            value="#{CElabels['operacoes.adicionar']}"
                            image="/imagens/botaoAdicionar.png" styleClass="botoes"
                            onclick="if(!validarBemConsumo()){return false;};" />

                    <a4j:commandButton
                            rendered="#{PerfilEventoControle.edicaoPerfilEventoBemConsumo}"
                            action="#{PerfilEventoControle.confirmarAlteracaoPerfilEventoBemConsumo}"
                            reRender="panelBensConsumo, panelMensagem"
                            value="#{CElabels['operacoes.editar.confirmar']}"
                            image="/imagens/bt_editar.png" styleClass="botoes"
                            onclick="if(!validarBemConsumo()){return false;};" />

                    <h:panelGrid columns="1" width="100%"
                                 styleClass="tabFormSubordinada">
                        <h:dataTable id="tablePerfilEventoBemConsumo" width="100%"
                                     headerClass="subordinado" styleClass="tabFormSubordinada"
                                     rowClasses="linhaImpar, linhaPar"
                                     columnClasses="colunaAlinhamento"
                                     value="#{PerfilEventoControle.perfilEventoTO.perfilEventoBensConsumo}"
                                     var="perfilEventoBemConsumo">

                            <h:column>
                                <f:facet name="header">
                                    <h:outputText value="#{CElabels['entidade.bemConsumo']}" />
                                </f:facet>
                                <h:outputText
                                        value="#{perfilEventoBemConsumo.descricaoProdutoLocacao}" />
                            </h:column>
                            <h:column>
                                <f:facet name="header">
                                    <h:outputText value="#{CElabels['entidade.valor']}" />
                                </f:facet>
                                <h:outputText value="#{perfilEventoBemConsumo.valorMonetario}" />
                            </h:column>
                            <h:column>
                                <f:facet name="header">
                                    <h:outputText value="#{CElabels['entidade.obrigatorio']}" />
                                </f:facet>
                                <h:outputText
                                        value="#{perfilEventoBemConsumo.obrigatorioFormatado}" />
                            </h:column>
                            <h:column>
                                <f:facet name="header">
                                    <h:outputText value="#{CElabels['operacoes.opcoes']}" />
                                </f:facet>
                                <a4j:commandButton id="editarPerfilEventoBemConsumo"
                                                   reRender="panelBensConsumo" ajaxSingle="true" immediate="true"
                                                   action="#{PerfilEventoControle.editarPerfilEventoBemConsumo}"
                                                   value="#{CElabels['operacoes.editar']}"
                                                   image="/imagens/bt_editar.png" styleClass="botoes"/>&nbsp;&nbsp;
                                <a4j:commandButton id="removerPerfilEventoBemConsumo"
                                                   reRender="form" ajaxSingle="true" immediate="true"
                                                   action="#{PerfilEventoControle.removerPerfilEventoBemConsumo}"
                                                   value="#{CElabels['operacoes.excluir']}"
                                                   image="/imagens/bt_excluir.png" styleClass="botoes"
                                                   rendered="#{!(perfilEventoBemConsumo.codigo gt 0)}" />
                            </h:column>

                        </h:dataTable>
                    </h:panelGrid>

                </h:panelGrid>
            </rich:tab>
            <!-- FIM - BENS DE CONSUMO -->

            <rich:tab id="utensilios"
                      label="#{CElabels['entidade.utensilios']}">
                <h:panelGrid id="panelUtensilios" columns="1" width="100%"
                             headerClass="subordinado" columnClasses="colunaCentralizada">
                    <f:facet name="header">
                        <h:outputText value="#{CElabels['entidade.utensilios']}" />
                    </f:facet>

                    <h:outputText styleClass="tituloCampos"
                                  value="#{CElabels['entidade.perfilEvento.permiteOutrosUten']}" />
                    <h:selectBooleanCheckbox id="permiteOutrosUtensilios"
                                             styleClass="campos"
                                             value="#{PerfilEventoControle.perfilEventoTO.permiteOutrosUtensilios}" />

                    <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar"
                                 columnClasses="classEsquerda, classDireita" width="100%">

                        <h:panelGroup>
                            <%@include file="../includes/include_obrigatorio.jsp"%>
                            <h:outputText value="#{CElabels['entidade.utensilio']}:" />
                        </h:panelGroup>
                        <h:panelGroup>
                            <table>
                                <tr>
                                    <td valign="middle"><h:inputText readonly="true" id="descricaoUtensilio" size="50"
                                                                     maxlength="50"
                                                                     onblur="blurinput(this); limparMsgObrig('form:descricaoUtensilio', 'descricaoUtensilio');"
                                                                     onfocus="focusinput(this);" styleClass="form"
                                                                     value="#{PerfilEventoControle.utensilio.descricaoProdutoLocacao}" />
                                    </td>
                                    <td valign="middle"><a4j:commandButton id="consultaDadosUtensilios"
                                                                           focus="valorConsultaUtensilio"
                                                                           title="#{CElabels['entidade.utensilios.consulta']}"
                                                                           reRender="formConsultaUtensilio"
                                                                           oncomplete="Richfaces.showModalPanel('panelConsultaUtensilio'), setFocus(formConsultaUtensilio,'formConsultaUtensilio:valorConsultaUtensilio');"
                                                                           image="/imagens/botoesCE/adicionar_produto.png"
                                                                           rendered="#{!PerfilEventoControle.edicaoPerfilEventoUtensilio}" />
                                    </td>
                                </tr>
                            </table>
                            <div id="divObg-descricaoUtensilio" class="mensagemObrigatorio"></div>
                        </h:panelGroup>

                        <h:panelGroup>
                            <h:outputText value="#{CElabels['entidade.valor']}:" />
                        </h:panelGroup>
                        <h:panelGroup>
                            <h:outputText value="#{PerfilEventoControle.utensilio.valorMonetario}" />

                        </h:panelGroup>


                        <h:outputText styleClass="tituloCampos"
                                      value="#{CElabels['entidade.textoLivre']}:" />
                        <rich:editor id="textoLivreUtensilio" useSeamText="false"
                                     viewMode="visual" width="400" height="200"
                                     value="#{PerfilEventoControle.utensilio.textoLivre}"/>

                        <h:outputText styleClass="tituloCampos"
                                      value="#{CElabels['entidade.obrigatorio']}:" />
                        <h:selectBooleanCheckbox id="obrigPerfilEventoUtensilio"
                                                 styleClass="campos"
                                                 value="#{PerfilEventoControle.utensilio.obrigatorio}"/>

                    </h:panelGrid>

                    <a4j:commandButton
                            rendered="#{!PerfilEventoControle.edicaoPerfilEventoUtensilio}"
                            action="#{PerfilEventoControle.adicionarPerfilEventoUtensilio}"
                            reRender="panelUtensilios, panelMensagem"
                            value="#{CElabels['operacoes.adicionar']}"
                            image="/imagens/botaoAdicionar.png" styleClass="botoes"
                            onclick="if(!validarUtensilio()){return false;};" />

                    <a4j:commandButton
                            rendered="#{PerfilEventoControle.edicaoPerfilEventoUtensilio}"
                            action="#{PerfilEventoControle.confirmarAlteracaoPerfilEventoUtensilio}"
                            reRender="panelUtensilios, panelMensagem"
                            value="#{CElabels['operacoes.editar.confirmar']}"
                            image="/imagens/bt_editar.png" styleClass="botoes"
                            onclick="if(!validarUtensilio()){return false;};" />

                    <h:panelGrid columns="1" width="100%"
                                 styleClass="tabFormSubordinada">
                        <h:dataTable id="tablePerfilEventoUtensilio" width="100%"
                                     headerClass="subordinado" styleClass="tabFormSubordinada"
                                     rowClasses="linhaImpar, linhaPar"
                                     columnClasses="colunaAlinhamento"
                                     value="#{PerfilEventoControle.perfilEventoTO.perfilEventoUtensilios}"
                                     var="perfilEventoUtensilio">

                            <h:column>
                                <f:facet name="header">
                                    <h:outputText value="#{CElabels['entidade.utensilio']}" />
                                </f:facet>
                                <h:outputText
                                        value="#{perfilEventoUtensilio.descricaoProdutoLocacao}" />
                            </h:column>
                            <h:column>
                                <f:facet name="header">
                                    <h:outputText value="#{CElabels['entidade.valor']}" />
                                </f:facet>
                                <h:outputText value="#{perfilEventoUtensilio.valorMonetario}" />
                            </h:column>

                            <h:column>
                                <f:facet name="header">
                                    <h:outputText value="#{CElabels['entidade.obrigatorio']}" />
                                </f:facet>
                                <h:outputText
                                        value="#{perfilEventoUtensilio.obrigatorioFormatado}" />
                            </h:column>
                            <h:column>
                                <f:facet name="header">
                                    <h:outputText value="#{CElabels['operacoes.opcoes']}" />
                                </f:facet>
                                <a4j:commandButton id="editarPerfilEventoUtensilio"
                                                   reRender="panelUtensilios" ajaxSingle="true" immediate="true"
                                                   action="#{PerfilEventoControle.editarPerfilEventoUtensilio}"
                                                   value="#{CElabels['operacoes.editar']}"
                                                   image="/imagens/bt_editar.png" styleClass="botoes"/>&nbsp;&nbsp;
                                <a4j:commandButton id="removerPerfilEventoUtensilio"
                                                   reRender="panelUtensilios" ajaxSingle="true" immediate="true"
                                                   action="#{PerfilEventoControle.removerPerfilEventoUtensilio}"
                                                   value="#{CElabels['operacoes.excluir']}"
                                                   image="/imagens/bt_excluir.png" styleClass="botoes"
                                                   rendered="#{!(perfilEventoUtensilio.codigo gt 0)}" />
                            </h:column>

                        </h:dataTable>
                    </h:panelGrid>
                </h:panelGrid>
            </rich:tab>

            <rich:tab id="brinquedos"
                      label="#{CElabels['entidade.brinquedos']}">
                <h:panelGrid id="panelBrinquedos" columns="1" width="100%"
                             headerClass="subordinado" columnClasses="colunaCentralizada">
                    <f:facet name="header">
                        <h:outputText value="#{CElabels['entidade.brinquedos']}" />
                    </f:facet>

                    <h:outputText styleClass="tituloCampos"
                                  value="#{CElabels['entidade.perfilEvento.permiteOutrosBrinq']}" />
                    <h:selectBooleanCheckbox id="permiteOutrosBrinquedos"
                                             styleClass="campos"
                                             value="#{PerfilEventoControle.perfilEventoTO.permiteOutrosBrinquedos}" />

                    <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar"
                                 columnClasses="classEsquerda, classDireita" width="100%">

                        <h:panelGroup>
                            <%@include file="../includes/include_obrigatorio.jsp"%>
                            <h:outputText value="#{CElabels['entidade.brinquedo']}:" />
                        </h:panelGroup>
                        <h:panelGroup style="vertical-align: top;" >
                            <table>
                                <tr>
                                    <td valign="middle"><h:inputText readonly="true" id="descricaoBrinquedo" size="50"
                                                                     maxlength="50"
                                                                     onblur="blurinput(this); limparMsgObrig('form:descricaoBrinquedo', 'descricaoBrinquedo');"
                                                                     onfocus="focusinput(this);" styleClass="form"
                                                                     value="#{PerfilEventoControle.brinquedo.descricaoProdutoLocacao}" /></td>
                                    <td valign="middle"><a4j:commandButton id="consultaDadosBrinquedos"
                                                                           focus="valorConsultaBrinquedo"
                                                                           title="#{CElabels['entidade.brinquedos.consulta']}"
                                                                           reRender="formConsultaBrinquedo"
                                                                           oncomplete="Richfaces.showModalPanel('panelConsultaBrinquedo'), setFocus(formConsultaBrinquedo,'formConsultaBrinquedo:valorConsultaBrinquedo');"
                                                                           image="/imagens/botoesCE/adicionar_produto.png"
                                                                           rendered="#{!PerfilEventoControle.edicaoPerfilEventoBrinquedo}" /></td>
                                </tr>
                            </table>


                            <div id="divObg-descricaoBrinquedo" class="mensagemObrigatorio"></div>
                        </h:panelGroup>

                        <h:panelGroup>
                            <h:outputText value="#{CElabels['entidade.valor']}:" />
                        </h:panelGroup>
                        <h:panelGroup>
                            <h:outputText value="#{PerfilEventoControle.brinquedo.valorMonetario}" />

                        </h:panelGroup>


                        <h:outputText styleClass="tituloCampos"
                                      value="#{CElabels['entidade.textoLivre']}:" />
                        <rich:editor id="textoLivreBrinquedo" useSeamText="false"
                                     viewMode="visual" width="400" height="200"
                                     value="#{PerfilEventoControle.brinquedo.textoLivre}"/>

                        <h:outputText styleClass="tituloCampos"
                                      value="#{CElabels['entidade.obrigatorio']}:" />
                        <h:selectBooleanCheckbox id="obrigPerfilEventoBrinquedo"
                                                 styleClass="campos"
                                                 value="#{PerfilEventoControle.brinquedo.obrigatorio}"/>

                    </h:panelGrid>

                    <a4j:commandButton
                            rendered="#{!PerfilEventoControle.edicaoPerfilEventoBrinquedo}"
                            action="#{PerfilEventoControle.adicionarPerfilEventoBrinquedo}"
                            reRender="panelBrinquedos, panelMensagem"
                            value="#{CElabels['operacoes.adicionar']}"
                            image="/imagens/botaoAdicionar.png" styleClass="botoes"
                            onclick="if(!validarBrinquedo()){return false;};" />

                    <a4j:commandButton
                            rendered="#{PerfilEventoControle.edicaoPerfilEventoBrinquedo}"
                            action="#{PerfilEventoControle.confirmarAlteracaoPerfilEventoBrinquedo}"
                            reRender="panelBrinquedos, panelMensagem"
                            value="#{CElabels['operacoes.editar.confirmar']}"
                            image="/imagens/bt_editar.png" styleClass="botoes"
                            onclick="if(!validarBrinquedo()){return false;};" />

                    <h:panelGrid columns="1" width="100%"
                                 styleClass="tabFormSubordinada">
                        <h:dataTable id="tablePerfilEventoBrinquedo" width="100%"
                                     headerClass="subordinado" styleClass="tabFormSubordinada"
                                     rowClasses="linhaImpar, linhaPar"
                                     columnClasses="colunaAlinhamento"
                                     value="#{PerfilEventoControle.perfilEventoTO.perfilEventoBrinquedos}"
                                     var="perfilEventoBrinquedo">

                            <h:column>
                                <f:facet name="header">
                                    <h:outputText value="#{CElabels['entidade.brinquedo']}" />
                                </f:facet>
                                <h:outputText
                                        value="#{perfilEventoBrinquedo.descricaoProdutoLocacao}" />
                            </h:column>
                            <h:column>
                                <f:facet name="header">
                                    <h:outputText value="#{CElabels['entidade.valor']}" />
                                </f:facet>
                                <h:outputText value="#{perfilEventoBrinquedo.valorMonetario}" />
                            </h:column>

                            <h:column>
                                <f:facet name="header">
                                    <h:outputText value="#{CElabels['entidade.obrigatorio']}" />
                                </f:facet>
                                <h:outputText
                                        value="#{perfilEventoBrinquedo.obrigatorioFormatado}" />
                            </h:column>
                            <h:column>
                                <f:facet name="header">
                                    <h:outputText value="#{CElabels['operacoes.opcoes']}" />
                                </f:facet>
                                <a4j:commandButton id="editarPerfilEventoBrinquedo"
                                                   reRender="panelBrinquedos" ajaxSingle="true" immediate="true"
                                                   action="#{PerfilEventoControle.editarPerfilEventoBrinquedo}"
                                                   value="#{CElabels['operacoes.editar']}"
                                                   image="/imagens/bt_editar.png" styleClass="botoes"/>&nbsp;&nbsp;
                                <a4j:commandButton id="removerPerfilEventoBrinquedo"
                                                   reRender="panelBrinquedos" ajaxSingle="true" immediate="true"
                                                   action="#{PerfilEventoControle.removerPerfilEventoBrinquedo}"
                                                   value="#{CElabels['operacoes.excluir']}"
                                                   image="/imagens/bt_excluir.png" styleClass="botoes"
                                                   rendered="#{!(perfilEventoBrinquedo.codigo gt 0)}" />
                            </h:column>

                        </h:dataTable>
                    </h:panelGrid>
                </h:panelGrid>
            </rich:tab>

            <rich:tab id="modeloContrato"
                      label="#{CElabels['entidade.contrato.modelos']}">
                <h:panelGrid id="panelModeloContrato" columns="1" width="100%"
                             headerClass="subordinado" columnClasses="colunaCentralizada">
                    <rich:simpleTogglePanel switchType="client"
                                            opened="#{!PerfilEventoControle.inclusaoImagemModelo}"
                                            onexpand="return false;" oncollapse="return false;">
                        <f:facet name="header">
                            <h:outputText value="#{CElabels['entidade.contrato.modelos']}" />
                        </f:facet>
                        <rich:simpleTogglePanel switchType="client" opened="false"
                                                width="99,5%" height="450px">
                            <f:facet name="header">
                                <h:outputText value="#{CElabels['entidade.contrato.tags']}" />
                            </f:facet>
                            <h:outputText
                                    value="#{CElabels['entidade.contrato.tags.ambiente']}" />
                            <br />
                            <h:outputText
                                    value="#{CElabels['entidade.contrato.tags.bairro']}" />
                            <br />
                            <h:outputText
                                    value="#{CElabels['entidade.contrato.tags.bensConsumolistahorizontal']}" />
                            <br />
                            <h:outputText
                                    value="#{CElabels['entidade.contrato.tags.bensConsumolistavertical']}" />
                            <br />
                            <h:outputText
                                    value="#{CElabels['entidade.contrato.tags.brinquedoslistahorizontal']}" />
                            <br />
                            <h:outputText
                                    value="#{CElabels['entidade.contrato.tags.brinquedoslistavertical']}" />
                            <br />
                            <h:outputText
                                    value="#{CElabels['entidade.contrato.tags.cheque.cheque']}" />
                            <br />
                            <h:outputText
                                    value="#{CElabels['entidade.contrato.tags.cidade']}" />
                            <br />
                            <h:outputText
                                    value="#{CElabels['entidade.contrato.tags.condPag']}" />
                            <br />
                            <h:outputText value="#{CElabels['entidade.contrato.tags.cpf']}" />
                            <br />
                            <h:outputText
                                    value="#{CElabels['entidade.contrato.tags.data']}" />
                            <br />
                            <h:outputText
                                    value="#{CElabels['entidade.contrato.tags.dataCompensacao']}" />
                            <br />
                            <h:outputText
                                    value="#{CElabels['entidade.contrato.tags.datasParcelas']}" />
                            <br />
                            <h:outputText
                                    value="#{CElabels['entidade.contrato.tags.duracaoEvento']}" />
                            <br />
                            <h:outputText
                                    value="#{CElabels['entidade.contrato.tags.endereco']}" />
                            <br />
                            <h:outputText
                                    value="#{CElabels['entidade.contrato.tags.eventohorainicial']}" />
                            <br />
                            <h:outputText
                                    value="#{CElabels['entidade.contrato.tags.eventohorafinal']}" />
                            <br />
                            <h:outputText
                                    value="#{CElabels['entidade.contrato.tags.formaPagamento']}" />
                            <br />
                            <h:outputText
                                    value="#{CElabels['entidade.contrato.tags.horario']}" />
                            <br />
                            <h:outputText
                                    value="#{CElabels['entidade.contrato.tags.layout']}" />
                            <br />
                            <h:outputText
                                    value="#{CElabels['entidade.contrato.tags.nomeCliente']}" />
                            <br />
                            <h:outputText
                                    value="#{CElabels['entidade.contrato.tags.nomeEvento']}" />
                            <br />
                            <h:outputText
                                    value="#{CElabels['entidade.contrato.tags.observacao']}" />
                            <br />
                            <h:outputText
                                    value="#{CElabels['entidade.contrato.tags.observacaoAmbiente']}" />
                            <br />
                            <h:outputText
                                    value="#{CElabels['entidade.contrato.tags.parcelas']}" />
                            <br />
                            <h:outputText
                                    value="#{CElabels['entidade.contrato.tags.qtdConvidados']}" />
                            <br />
                            <h:outputText value="#{CElabels['entidade.contrato.tags.rg']}" />
                            <br />
                            <h:outputText
                                    value="#{CElabels['entidade.contrato.tags.servicoslistahorizontal']}" />
                            <br />
                            <h:outputText
                                    value="#{CElabels['entidade.contrato.tags.servicoslistavertical']}" />
                            <br />
                            <h:outputText
                                    value="#{CElabels['entidade.contrato.tags.telefones']}" />
                            <br />
                            <h:outputText
                                    value="#{CElabels['entidade.contrato.tags.texto']}" />
                            <br />
                            <h:outputText
                                    value="#{CElabels['entidade.contrato.tags.total']}" />
                            <br />
                            <h:outputText
                                    value="#{CElabels['entidade.contrato.tags.totalDesc']}" />
                            <br />
                            <h:outputText
                                    value="#{CElabels['entidade.contrato.tags.utensilioslistahorizontal']}" />
                            <br />
                            <h:outputText
                                    value="#{CElabels['entidade.contrato.tags.utensilioslistavertical']}" />
                            <br />
                            <h:outputText
                                    value="#{CElabels['entidade.contrato.tags.valorFinal']}" />
                            <br />
                            <h:outputText
                                    value="#{CElabels['entidade.contrato.tags.valorParcela']}" />

                            <br />
                            <h:outputText
                                    value="#valor_ambientes - Soma dos valores de todos os ambientes do evento" />

                            <br />
                            <h:outputText
                                    value="#nr_segurancas - Número de serviços do tipo Segurança inseridos no evento" />
                            <br />
                            <h:outputText
                                    value="#nr_auxiliarLimpeza - Número de serviços do tipo Auxiliar de Limpeza inseridos no evento" />
                            <br />
                            <h:outputText
                                    value="#valor_individual_seguranca - Valor unitário do serviço do tipo Segurança inserido no evento" />
                            <br />
                            <h:outputText
                                    value="#valor_individual_limpeza - Valor unitário do serviço do tipo Auxiliar de Limpeza inserido no evento" />
                            <br />
                            <h:outputText
                                    value="#valor_total_servicos - Valor total dos serviços do tipo Auxiliar de Limpeza e Segurança inseridos no evento" />
                            <br />
                            <h:outputText
                                    value="#valor_credito - Valor total de créditos incluídos no orçamento" />
                            <br />
                            <h:outputText
                                    value="#valor_caucao - Valor total de cheque caução incluídos no orçamento" />
                            <br />
                            <h:outputText
                                    value="#hoje - Data atual" />
                        </rich:simpleTogglePanel>
                        <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar"
                                     columnClasses="classEsquerda, classDireita" width="100%">

                            <h:panelGroup>
                                <%@include file="../includes/include_obrigatorio.jsp"%>
                                <h:outputText value="#{CElabels['entidade.arquivo']}:" />
                            </h:panelGroup>
                            <h:panelGroup>
                                <rich:fileUpload
                                        fileUploadListener="#{PerfilEventoControle.uploadModeloListener}"
                                        maxFilesQuantity="#{PerfilEventoControle.numeroArquivos}"
                                        immediateUpload="false" acceptedTypes="htm, html"
                                        allowFlash="false" listHeight="58px" id="modeloUpload"
                                        onfileuploadcomplete="document.getElementById('form:arquivoCarregado').value = 'true';"
                                        cancelEntryControlLabel="#{CElabels['operacoes.cancelar']}"
                                        addControlLabel="#{CElabels['operacoes.adicionar']}"
                                        clearControlLabel="#{CElabels['operacoes.remover']}"
                                        clearAllControlLabel="#{CElabels['operacoes.remover.todos']}"
                                        doneLabel="#{CElabels['operacoes.concluido']}"
                                        sizeErrorLabel="#{Mensagens['operacoes.arquivo.upload.tamanhoLimiteExcedido']}"
                                        uploadControlLabel="#{CElabels['operacoes.carregar']}"
                                        transferErrorLabel="#{Mensagens['operacoes.arquivo.upload.erroTransferencia']}"
                                        stopControlLabel="#{CElabels['operacoes.parar']}"
                                        stopEntryControlLabel="#{CElabels['operacoes.parar']}"
                                        progressLabel="#{Mensagens['operacoes.arquivo.upload.carregando']}">
                                    <a4j:support event="onclear"
                                                 action="#{PerfilEventoControle.removerArquivoModeloContrato}"
                                                 reRender="modeloUpload, arquivoCarregado" />
                                    <a4j:support event="onadd"
                                                 action="#{PerfilEventoControle.decrementarNumeroArquivos}" />
                                </rich:fileUpload>
                                <div id="divObg-arquivoModeloContrato"
                                     class="mensagemObrigatorio"></div>
                            </h:panelGroup>


                            <h:panelGroup>
                                <%@include file="../includes/include_obrigatorio.jsp"%>
                                <h:outputText value="#{CElabels['entidade.descricao']}:" />
                            </h:panelGroup>
                            <h:panelGroup>
                                <rich:editor id="descricaoModeloContrato" useSeamText="false"
                                             viewMode="visual" width="400" height="200"
                                             value="#{PerfilEventoControle.modeloContrato.descricao}"
                                             onchange="limparMsgObrigTextoPadrao();" />
                                <div id="divObg-descricaoModeloContrato"
                                     class="mensagemObrigatorio"></div>
                            </h:panelGroup>

                        </h:panelGrid>

                        <center><a4j:commandButton
                                action="#{PerfilEventoControle.adicionarModelo}"
                                reRender="panelModeloContrato, arquivoCarregado, panelMensagem"
                                onclick="if(!validarModeloContrato()){return false;};"
                                oncomplete="limparMsgObrigModeloContrato();"
                                value="#{CElabels['operacoes.adicionar']}"
                                image="/imagens/botaoAdicionar.png" styleClass="botoes" /></center>

                        <h:panelGrid columns="1" width="100%"
                                     styleClass="tabFormSubordinada">
                            <h:dataTable id="tableModeloContrato" width="100%"
                                         headerClass="subordinado" styleClass="tabFormSubordinada"
                                         rowClasses="linhaImpar, linhaPar"
                                         columnClasses="colunaAlinhamento"
                                         value="#{PerfilEventoControle.perfilEventoTO.modelosContrato}"
                                         var="modeloContrato">

                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText value="#{CElabels['entidade.nome']}" />
                                    </f:facet>
                                    <h:outputText value="#{modeloContrato.nomeArquivo}" />
                                </h:column>

                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText value="#{CElabels['entidade.descricao']}" />
                                    </f:facet>
                                    <h:outputText value="#{modeloContrato.descricao}"
                                                  escape="false" />
                                </h:column>

                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText value="#{CElabels['operacoes.opcoes']}" />
                                    </f:facet>
                                    <center><a4j:commandLink
                                            action="#{PerfilEventoControle.removerModeloContrato}"
                                            reRender="tableModeloContrato"
                                            rendered="#{!modeloContrato.possuiImpressao}">
                                        <h:graphicImage value="/imagens/bt_excluir.png"
                                                        style="border: 0px;"
                                                        alt="#{CElabels['operacoes.contrato.remover']}" />
                                    </a4j:commandLink>&nbsp;&nbsp;
                                        <a4j:commandLink action="#{PerfilEventoControle.abrirAdicionarImagens}"
                                                         reRender="panelModeloContrato, panelModeloContratoImagem, arquivoCarregado"
                                                         onmouseover="toolTip('#{CElabels['entidade.contrato.imagens.adicionar']}' , 120 , 'gray')"
                                                         onmouseout="hideToolTip();">
                                            <h:graphicImage value="/imagens/botoesCE/adicionar_imagem.png" style="border: 0px;"/>
                                        </a4j:commandLink>

                                    </center>
                                </h:column>

                            </h:dataTable>
                        </h:panelGrid>

                    </rich:simpleTogglePanel>
                </h:panelGrid>

                <h:panelGrid id="panelModeloContratoImagem" columns="1"
                             width="100%" headerClass="subordinado"
                             columnClasses="colunaCentralizada">
                    <rich:simpleTogglePanel switchType="client"
                                            opened="#{PerfilEventoControle.inclusaoImagemModelo}"
                                            onexpand="return false;" oncollapse="return false;">
                        <f:facet name="header">
                            <h:outputText value="#{CElabels['entidade.imagens']}" />
                        </f:facet>

                        <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar"
                                     columnClasses="classEsquerda, classDireita" width="100%">

                            <h:panelGroup>
                                <%@include file="../includes/include_obrigatorio.jsp"%>
                                <h:outputText value="#{CElabels['entidade.arquivo']}:" />
                            </h:panelGroup>
                            <h:panelGroup>
                                <rich:fileUpload
                                        fileUploadListener="#{PerfilEventoControle.uploadImagemListener}"
                                        maxFilesQuantity="#{PerfilEventoControle.numeroArquivos}"
                                        immediateUpload="false" id="imagemModeloUpload"
                                        acceptedTypes="jpg, jpeg, gif, png, bmp, doc, docx" allowFlash="false"
                                        listHeight="58px"
                                        onfileuploadcomplete="document.getElementById('form:arquivoCarregado').value = 'true';"
                                        cancelEntryControlLabel="#{CElabels['operacoes.cancelar']}"
                                        addControlLabel="#{CElabels['operacoes.adicionar']}"
                                        clearControlLabel="#{CElabels['operacoes.remover']}"
                                        clearAllControlLabel="#{CElabels['operacoes.remover.todos']}"
                                        doneLabel="#{CElabels['operacoes.concluido']}"
                                        sizeErrorLabel="#{Mensagens['operacoes.arquivo.upload.tamanhoLimiteExcedido']}"
                                        uploadControlLabel="#{CElabels['operacoes.carregar']}"
                                        transferErrorLabel="#{Mensagens['operacoes.arquivo.upload.erroTransferencia']}"
                                        stopControlLabel="#{CElabels['operacoes.parar']}"
                                        stopEntryControlLabel="#{CElabels['operacoes.parar']}"
                                        progressLabel="#{Mensagens['operacoes.arquivo.upload.carregando']}">
                                    <a4j:support event="onclear"
                                                 action="#{PerfilEventoControle.removerArquivoImagem}"
                                                 reRender="imagemModeloUpload, arquivoCarregado" />
                                    <a4j:support event="onadd"
                                                 action="#{PerfilEventoControle.decrementarNumeroArquivos}" />
                                </rich:fileUpload>
                                <div id="divObg-arquivoModeloContratoImagem"
                                     class="mensagemObrigatorio"></div>
                            </h:panelGroup>

                        </h:panelGrid>

                        <center><a4j:commandButton
                                action="#{PerfilEventoControle.adicionarImagem}"
                                reRender="panelModeloContratoImagem, arquivoCarregado, panelMensagem"
                                onclick="if(!validarModeloContratoImagem()){return false;};"
                                value="#{CElabels['operacoes.adicionar']}"
                                image="/imagens/botaoAdicionar.png" styleClass="botoes" /></center>

                        <h:panelGrid columns="1" width="100%"
                                     styleClass="tabFormSubordinada">
                            <h:dataTable id="tableImagem" width="100%"
                                         headerClass="subordinado" styleClass="tabFormSubordinada"
                                         rowClasses="linhaImpar, linhaPar"
                                         columnClasses="colunaAlinhamento"
                                         value="#{PerfilEventoControle.modeloAddImagens.imagens}"
                                         var="imagem">

                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText value="#{CElabels['entidade.nome']}" />
                                    </f:facet>
                                    <h:outputText value="#{imagem.nomeArquivo}" />
                                </h:column>

                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText value="#{CElabels['operacoes.opcoes']}" />
                                    </f:facet>
                                    <center><a4j:commandLink
                                            action="#{PerfilEventoControle.removerImagem}"
                                            reRender="tableImagem">
                                        <h:graphicImage value="/imagens/bt_excluir.png"
                                                        style="border: 0px;"
                                                        alt="#{CElabels['operacoes.contrato.imagens.remover']}" />
                                    </a4j:commandLink></center>
                                </h:column>

                            </h:dataTable>
                        </h:panelGrid>
                        <center>
                            <a4j:commandButton
                                    value="#{CElabels['entidade.contrato.visualizarModelos']}"
                                    action="#{PerfilEventoControle.visualizarModelos}"
                                    reRender="panelModeloContrato, panelModeloContratoImagem, arquivoCarregado"
                                    image="/imagens/botoesCE/visualizar_modelos_contratos.png"/>
                        </center>
                    </rich:simpleTogglePanel>
                </h:panelGrid>

                <h:inputHidden id="arquivoCarregado"
                               value="#{PerfilEventoControle.arquivoCarregado}" />
            </rich:tab>

            <rich:tab id="modeloOrcamento"
                      label="#{CElabels['entidade.orcamento.modelos']}">
                <h:panelGrid id="panelModeloOrcamento" columns="1" width="100%"
                             headerClass="subordinado" columnClasses="colunaCentralizada">
                    <rich:simpleTogglePanel switchType="client"
                                            opened="#{!PerfilEventoControle.inclusaoImagemModeloOrcamento}"
                                            onexpand="return false;" oncollapse="return false;">
                        <f:facet name="header">
                            <h:outputText value="#{CElabels['entidade.orcamento.modelos']}" />
                        </f:facet>

                        <rich:simpleTogglePanel switchType="client" opened="false"
                                                width="99,5%" height="450px">
                            <f:facet name="header">
                                <h:outputText value="#{CElabels['entidade.contrato.tags']}" />
                            </f:facet>
                            <h:outputText
                                    value="#{CElabels['entidade.contrato.tags.ambiente']}" />
                            <br />
                            <h:outputText
                                    value="#{CElabels['entidade.contrato.tags.bairro']}" />
                            <br />
                            <h:outputText
                                    value="#{CElabels['entidade.contrato.tags.bensConsumolistahorizontal']}" />
                            <br />
                            <h:outputText
                                    value="#{CElabels['entidade.contrato.tags.bensConsumolistavertical']}" />
                            <br />
                            <h:outputText
                                    value="#{CElabels['entidade.contrato.tags.brinquedoslistahorizontal']}" />
                            <br />
                            <h:outputText
                                    value="#{CElabels['entidade.contrato.tags.brinquedoslistavertical']}" />
                            <br />
                            <h:outputText
                                    value="#{CElabels['entidade.contrato.tags.cheque.cheque']}" />
                            <br />
                            <h:outputText
                                    value="#{CElabels['entidade.contrato.tags.cidade']}" />
                            <br />
                            <h:outputText
                                    value="#{CElabels['entidade.contrato.tags.condPag']}" />
                            <br />
                            <h:outputText value="#{CElabels['entidade.contrato.tags.cpf']}" />
                            <br />
                            <h:outputText
                                    value="#{CElabels['entidade.contrato.tags.data']}" />
                            <br />
                            <h:outputText
                                    value="#{CElabels['entidade.contrato.tags.dataCompensacao']}" />
                            <br />
                            <h:outputText
                                    value="#{CElabels['entidade.contrato.tags.datasParcelas']}" />
                            <br />
                            <h:outputText
                                    value="#{CElabels['entidade.contrato.tags.duracaoEvento']}" />
                            <br />
                            <h:outputText
                                    value="#{CElabels['entidade.contrato.tags.endereco']}" />
                            <br />
                            <h:outputText
                                    value="#{CElabels['entidade.contrato.tags.eventohorainicial']}" />
                            <br />
                            <h:outputText
                                    value="#{CElabels['entidade.contrato.tags.eventohorafinal']}" />
                            <br />
                            <h:outputText
                                    value="#{CElabels['entidade.contrato.tags.formaPagamento']}" />
                            <br />
                            <h:outputText
                                    value="#{CElabels['entidade.contrato.tags.horario']}" />
                            <br />
                            <h:outputText
                                    value="#{CElabels['entidade.contrato.tags.layout']}" />
                            <br />
                            <h:outputText
                                    value="#{CElabels['entidade.contrato.tags.nomeCliente']}" />
                            <br />
                            <h:outputText
                                    value="#{CElabels['entidade.contrato.tags.nomeEvento']}" />
                            <br />
                            <h:outputText
                                    value="#{CElabels['entidade.contrato.tags.observacao']}" />
                            <br />
                            <h:outputText
                                    value="#{CElabels['entidade.contrato.tags.observacaoAmbiente']}" />
                            <br />
                            <h:outputText
                                    value="#{CElabels['entidade.contrato.tags.parcelas']}" />
                            <br />
                            <h:outputText
                                    value="#{CElabels['entidade.contrato.tags.qtdConvidados']}" />
                            <br />
                            <h:outputText value="#{CElabels['entidade.contrato.tags.rg']}" />
                            <br />
                            <h:outputText
                                    value="#{CElabels['entidade.contrato.tags.servicoslistahorizontal']}" />
                            <br />
                            <h:outputText
                                    value="#{CElabels['entidade.contrato.tags.servicoslistavertical']}" />
                            <br />
                            <h:outputText
                                    value="#{CElabels['entidade.contrato.tags.telefones']}" />
                            <br />
                            <h:outputText
                                    value="#{CElabels['entidade.contrato.tags.texto']}" />
                            <br />
                            <h:outputText
                                    value="#{CElabels['entidade.contrato.tags.total']}" />
                            <br />
                            <h:outputText
                                    value="#{CElabels['entidade.contrato.tags.totalDesc']}" />
                            <br />
                            <h:outputText
                                    value="#{CElabels['entidade.contrato.tags.utensilioslistahorizontal']}" />
                            <br />
                            <h:outputText
                                    value="#{CElabels['entidade.contrato.tags.utensilioslistavertical']}" />
                            <br />
                            <h:outputText
                                    value="#{CElabels['entidade.contrato.tags.valorFinal']}" />
                            <br />
                            <h:outputText
                                    value="#{CElabels['entidade.contrato.tags.valorParcela']}" />
                            <br />
                            <h:outputText
                                    value="#valor_ambientes - Soma dos valores de todos os ambientes do evento" />

                            <br />
                            <h:outputText
                                    value="#nr_segurancas - Número de serviços do tipo Segurança inseridos no evento" />
                            <br />
                            <h:outputText
                                    value="#nr_auxiliarLimpeza - Número de serviços do tipo Auxiliar de Limpeza inseridos no evento" />
                            <br />
                            <h:outputText
                                    value="#valor_individual_seguranca - Valor unitário do serviço do tipo Segurança inserido no evento" />
                            <br />
                            <h:outputText
                                    value="#valor_individual_limpeza - Valor unitário do serviço do tipo Auxiliar de Limpeza inserido no evento" />
                            <br />
                            <h:outputText
                                    value="#valor_total_servicos - Valor total dos serviços do tipo Auxiliar de Limpeza e Segurança inseridos no evento" />
                            <br />
                            <h:outputText
                                    value="#valor_credito - Valor total de créditos incluídos no orçamento" />
                            <br />
                            <h:outputText
                                    value="#valor_caucao - Valor total de cheque caução incluídos no orçamento" />
                            <br />
                            <h:outputText
                                    value="#hoje - Data atual" />
                        </rich:simpleTogglePanel>

                        <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar"
                                     columnClasses="classEsquerda, classDireita" width="100%">

                            <h:panelGroup>
                                <%@include file="../includes/include_obrigatorio.jsp"%>
                                <h:outputText value="#{CElabels['entidade.arquivo']}:" />
                            </h:panelGroup>
                            <h:panelGroup>
                                <rich:fileUpload
                                        fileUploadListener="#{PerfilEventoControle.uploadModeloOrcamentoListener}"
                                        maxFilesQuantity="#{PerfilEventoControle.numeroArquivosOrcamento}"
                                        immediateUpload="false" acceptedTypes="htm, html"
                                        allowFlash="false" listHeight="58px"
                                        id="modeloOrcamentoUpload"
                                        onfileuploadcomplete="document.getElementById('form:arquivoOrcamentoCarregado').value = 'true';"
                                        cancelEntryControlLabel="#{CElabels['operacoes.cancelar']}"
                                        addControlLabel="#{CElabels['operacoes.adicionar']}"
                                        clearControlLabel="#{CElabels['operacoes.remover']}"
                                        clearAllControlLabel="#{CElabels['operacoes.remover.todos']}"
                                        doneLabel="#{CElabels['operacoes.concluido']}"
                                        sizeErrorLabel="#{Mensagens['operacoes.arquivo.upload.tamanhoLimiteExcedido']}"
                                        uploadControlLabel="#{CElabels['operacoes.carregar']}"
                                        transferErrorLabel="#{Mensagens['operacoes.arquivo.upload.erroTransferencia']}"
                                        stopControlLabel="#{CElabels['operacoes.parar']}"
                                        stopEntryControlLabel="#{CElabels['operacoes.parar']}"
                                        progressLabel="#{Mensagens['operacoes.arquivo.upload.carregando']}">
                                    <a4j:support event="onclear"
                                                 action="#{PerfilEventoControle.removerArquivoModeloOrcamento}"
                                                 reRender="modeloOrcamentoUpload, arquivoOrcamentoCarregado" />
                                    <a4j:support event="onadd"
                                                 action="#{PerfilEventoControle.decrementarNumeroArquivosOrcamento}" />
                                </rich:fileUpload>
                                <div id="divObg-arquivoModeloOrcamento"
                                     class="mensagemObrigatorio"></div>
                            </h:panelGroup>


                            <h:panelGroup>
                                <%@include file="../includes/include_obrigatorio.jsp"%>
                                <h:outputText value="#{CElabels['entidade.descricao']}:" />
                            </h:panelGroup>
                            <h:panelGroup>
                                <rich:editor id="descricaoModeloOrcamento" useSeamText="false"
                                             viewMode="visual" width="400" height="200"
                                             value="#{PerfilEventoControle.modeloOrcamento.descricao}"
                                             onchange="limparMsgObrigTextoPadrao();" />
                                <div id="divObg-descricaoModeloOrcamento"
                                     class="mensagemObrigatorio"></div>
                            </h:panelGroup>

                        </h:panelGrid>

                        <center><a4j:commandButton
                                action="#{PerfilEventoControle.adicionarModeloOrcamento}"
                                reRender="panelModeloOrcamento, arquivoOrcamentoCarregado, panelMensagem"
                                onclick="if(!validarModeloOrcamento()){return false;};"
                                value="#{CElabels['operacoes.adicionar']}"
                                image="/imagens/botaoAdicionar.png" styleClass="botoes" /></center>

                        <h:panelGrid columns="1" width="100%"
                                     styleClass="tabFormSubordinada">
                            <h:dataTable id="tableModeloOrcamento" width="100%"
                                         headerClass="subordinado" styleClass="tabFormSubordinada"
                                         rowClasses="linhaImpar, linhaPar"
                                         columnClasses="colunaAlinhamento"
                                         value="#{PerfilEventoControle.perfilEventoTO.modelosOrcamento}"
                                         var="modeloOrcamento">

                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText value="#{CElabels['entidade.nome']}" />
                                    </f:facet>
                                    <h:outputText value="#{modeloOrcamento.nomeArquivo}" />
                                </h:column>

                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText value="#{CElabels['entidade.descricao']}" />
                                    </f:facet>
                                    <h:outputText value="#{modeloOrcamento.descricao}"
                                                  escape="false" />
                                </h:column>

                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText value="#{CElabels['operacoes.opcoes']}" />
                                    </f:facet>
                                    <center><a4j:commandLink
                                            rendered="#{!modeloOrcamento.possuiImpressao}"
                                            action="#{PerfilEventoControle.removerModeloOrcamento}"
                                            reRender="tableModeloOrcamento">
                                        <h:graphicImage value="/imagens/bt_excluir.png"
                                                        style="border: 0px;"
                                                        alt="#{CElabels['operacoes.contrato.imagens.remover']}" />
                                    </a4j:commandLink>&nbsp;&nbsp; <a4j:commandLink
                                            action="#{PerfilEventoControle.abrirAdicionarImagensModeloOrcamento}"
                                            reRender="panelModeloOrcamento, panelModeloOrcamentoImagem, arquivoOrcamentoCarregado"
                                            onmouseover="toolTip('#{CElabels['entidade.contrato.imagens.adicionar']}' , 120 , 'gray')"
                                            onmouseout="hideToolTip();">
                                        <h:graphicImage value="/imagens/botoesCE/adicionar_imagem.png" style="border: 0px;"/>
                                    </a4j:commandLink>

                                    </center>
                                </h:column>

                            </h:dataTable>
                        </h:panelGrid>
                    </rich:simpleTogglePanel>
                </h:panelGrid>

                <h:panelGrid id="panelModeloOrcamentoImagem" columns="1"
                             width="100%" headerClass="subordinado"
                             columnClasses="colunaCentralizada">
                    <rich:simpleTogglePanel switchType="client"
                                            opened="#{PerfilEventoControle.inclusaoImagemModeloOrcamento}"
                                            onexpand="return false;" oncollapse="return false;">
                        <f:facet name="header">
                            <h:outputText value="#{CElabels['entidade.imagens']}" />
                        </f:facet>

                        <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar"
                                     columnClasses="classEsquerda, classDireita" width="100%">

                            <h:panelGroup>
                                <%@include file="../includes/include_obrigatorio.jsp"%>
                                <h:outputText value="#{CElabels['entidade.arquivo']}:" />
                            </h:panelGroup>
                            <h:panelGroup>
                                <rich:fileUpload
                                        fileUploadListener="#{PerfilEventoControle.uploadImagemModeloOrcamentoListener}"
                                        maxFilesQuantity="#{PerfilEventoControle.numeroArquivosOrcamento}"
                                        immediateUpload="false" id="imagemModeloOrcamentoUpload"
                                        acceptedTypes="jpg, gif, png, bmp, doc, docx" allowFlash="false"
                                        listHeight="58px"
                                        onfileuploadcomplete="document.getElementById('form:arquivoOrcamentoCarregado').value = 'true';"
                                        cancelEntryControlLabel="#{CElabels['operacoes.cancelar']}"
                                        addControlLabel="#{CElabels['operacoes.adicionar']}"
                                        clearControlLabel="#{CElabels['operacoes.remover']}"
                                        clearAllControlLabel="#{CElabels['operacoes.remover.todos']}"
                                        doneLabel="#{CElabels['operacoes.concluido']}"
                                        sizeErrorLabel="#{Mensagens['operacoes.arquivo.upload.tamanhoLimiteExcedido']}"
                                        uploadControlLabel="#{CElabels['operacoes.carregar']}"
                                        transferErrorLabel="#{Mensagens['operacoes.arquivo.upload.erroTransferencia']}"
                                        stopControlLabel="#{CElabels['operacoes.parar']}"
                                        stopEntryControlLabel="#{CElabels['operacoes.parar']}"
                                        progressLabel="#{Mensagens['operacoes.arquivo.upload.carregando']}">
                                    <a4j:support event="onclear"
                                                 action="#{PerfilEventoControle.removerArquivoImagemModeloOrcamento}"
                                                 reRender="imagemModeloOrcamentoUpload, arquivoOrcamentoCarregado" />
                                    <a4j:support event="onadd"
                                                 action="#{PerfilEventoControle.decrementarNumeroArquivosOrcamento}" />
                                </rich:fileUpload>
                                <div id="divObg-arquivoModeloOrcamentoImagem"
                                     class="mensagemObrigatorio"></div>
                            </h:panelGroup>

                        </h:panelGrid>

                        <center><a4j:commandButton
                                action="#{PerfilEventoControle.adicionarImagemModeloOrcamento}"
                                reRender="panelModeloOrcamentoImagem, arquivoOrcamentoCarregado, panelMensagem"
                                onclick="if(!validarModeloOrcamentoImagem()){return false;};"
                                value="#{CElabels['operacoes.adicionar']}"
                                image="/imagens/botaoAdicionar.png" styleClass="botoes" /></center>

                        <h:panelGrid columns="1" width="100%"
                                     styleClass="tabFormSubordinada">
                            <h:dataTable id="tableImagemOrcamento" width="100%"
                                         headerClass="subordinado" styleClass="tabFormSubordinada"
                                         rowClasses="linhaImpar, linhaPar"
                                         columnClasses="colunaAlinhamento"
                                         value="#{PerfilEventoControle.modeloOrcamentoAddImagens.imagens}"
                                         var="imagemModeloOrcamento">

                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText value="#{CElabels['entidade.nome']}" />
                                    </f:facet>
                                    <h:outputText value="#{imagemModeloOrcamento.nomeArquivo}" />
                                </h:column>

                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText value="#{CElabels['operacoes.opcoes']}" />
                                    </f:facet>
                                    <center><a4j:commandLink
                                            action="#{PerfilEventoControle.removerImagemModeloOrcamento}"
                                            reRender="tableImagemOrcamento">
                                        <h:graphicImage value="/imagens/bt_excluir.png"
                                                        style="border: 0px;"
                                                        alt="#{CElabels['operacoes.contrato.imagens.remover']}" />
                                    </a4j:commandLink></center>
                                </h:column>

                            </h:dataTable>
                        </h:panelGrid>

                        <a4j:commandLink
                                value="#{CElabels['entidade.orcamento.visualizarModelos']}"
                                action="#{PerfilEventoControle.visualizarModelosOrcamento}"
                                reRender="panelModeloOrcamento, panelModeloOrcamentoImagem, arquivoOrcamentoCarregado" />

                    </rich:simpleTogglePanel>
                </h:panelGrid>

                <h:inputHidden id="arquivoOrcamentoCarregado"
                               value="#{PerfilEventoControle.arquivoOrcamentoCarregado}" />
            </rich:tab>
        </rich:tabPanel>

        <!-- PAINEL DE EXIBIÇÃO DE MENSAGENS E AÇÕES -->
        <h:panelGrid id="mensagens" columns="1" width="100%"
                     styleClass="tabMensagens">



            <h:panelGrid columns="3" width="100%" styleClass="tabMensagens">
                <h:panelGrid columns="1" width="100%">

                    <h:outputText value=" "/>

                </h:panelGrid>

                <!-- MENSAGENS -->
                <h:commandButton rendered="#{PerfilEventoControle.sucesso}"
                                 image="/imagens/bt_sucesso.png"/>
                <h:commandButton rendered="#{PerfilEventoControle.erro}"
                                 image="/imagens/erro.png"/>
                <h:panelGrid columns="1" width="100%">
                    <h:outputText styleClass="mensagem"
                                  value="#{PerfilEventoControle.mensagem}" escape="false"/>
                    <h:outputText styleClass="mensagemDetalhada"
                                  value="#{PerfilEventoControle.mensagemDetalhada}" escape="false"/>
                </h:panelGrid>
            </h:panelGrid>

            <!-- AÇÕES -->
            <h:panelGrid columns="1" width="100%"
                         columnClasses="colunaCentralizada">
                <h:panelGroup>
                        <!-- BOTÃO QUE SALVA UM NOVO OBJETO -->
                        <h:commandButton id="salvar" action="#{PerfilEventoControle.salvar}"
                                         value="#{CElabels['operacoes.gravar']}"
                                         onclick="if(!validar()){return false;};"
                                         alt="#{CElabels['operacoes.gravar.dados']}" styleClass="botoes nvoBt"
                                         actionListener="#{PerfilEventoControle.autorizacao}">
                            <!-- Entidade.FORNECEDOR -->
                            <f:attribute name="entidade" value="104"/>
                            <!-- Operacao.GRAVAR -->
                            <f:attribute name="operacao" value="G"/>
                        </h:commandButton>
                        <h:outputText value="    "/>
                        <h:panelGroup
                                rendered="#{PerfilEventoControle.perfilEventoTO.codigo gt 0}">
                                <a4j:commandButton id="excluir"
                                                 action="#{PerfilEventoControle.confirmarExcluir}"
                                                 oncomplete="#{PerfilEventoControle.msgAlert}"
                                                 styleClass="botoes nvoBt btSec btPerigo" reRender="form"
                                                 actionListener="#{PerfilEventoControle.autorizacao}"
                                                 value="#{msg_bt.btn_excluir}" title="#{msg.msg_excluir_dados}" accesskey="3" />
                            <h:outputText value="    "/>
                            <!-- Entidade.fornecedor -->
                            <f:attribute name="entidade" value="104"/>
                            <!-- Operacao.consultar -->
                            <f:attribute name="operacao" value="E"/>
                        </h:panelGroup>
                        <!-- BOTÃO QUE CHAMA O MÉTODO DE CONSULTA -->
                        <h:commandButton id="consultar" immediate="true"
                                         action="#{NavegacaoControle.abrirPerfilEvento}"
                                         value="#{CElabels['operacoes.consulta.consultar']}"
                                         alt="#{CElabels['operacoes.consulta.consultarDados']}"
                                         styleClass="botoes nvoBt btSec"
                                         actionListener="#{PerfilEventoControle.autorizacao}">
                            <!-- Entidade.fornecedor -->
                            <f:attribute name="entidade" value="104"/>
                            <!-- Operacao.consultar -->
                            <f:attribute name="operacao" value="C"/>
                        </h:commandButton>

                </h:panelGroup>

            </h:panelGrid>

        </h:panelGrid>


    </h:form>
    </body>
    </html>

    <rich:modalPanel id="panelConsultaProduto" autosized="true"
                     shadowOpacity="true" width="450" height="250">
        <f:facet name="header">

            <jsp:include page="../includes/topoReduzido.jsp" />

        </f:facet>
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="#{CElabels['entidade.produtos.consulta']}"></h:outputText>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:graphicImage value="/imagens/close.png" style="cursor:pointer"
                                id="hidelinkProduto" />
                <rich:componentControl for="panelConsultaProduto"
                                       attachTo="hidelinkProduto" operation="hide" event="onclick" />
            </h:panelGroup>
        </f:facet>

        <a4j:form id="formConsultaProduto" ajaxSubmit="true">
            <h:panelGrid columns="1" width="100%"
                         columnClasses="colunaCentralizada">
                <h:panelGrid columns="3" footerClass="colunaCentralizada"
                             width="100%">
                    <h:outputText value="#{CElabels['entidade.nome']}:" />
                    <h:inputText id="valorConsultaProduto" size="50"
                                 onblur="blurinput(this);" onfocus="focusinput(this);"
                                 styleClass="form"
                                 value="#{PerfilEventoControle.filtroProdutos.descricao}" />
                    <a4j:commandButton id="btnConsultarProduto"
                                       reRender="formConsultaProduto"
                                       action="#{PerfilEventoControle.consultarProdutos}"
                                       styleClass="botoes"
                                       value="#{CElabels['operacoes.consulta.consultar']}"
                                       image="/imagens/botoesCE/buscar.png"
                                       title="#{CElabels['operacoes.consulta.consultarDados']}" />
                </h:panelGrid>
                <rich:dataTable id="resultadoConsultaProduto" width="100%"
                                headerClass="consulta" rowClasses="linhaImpar, linhaPar"
                                columnClasses="colunaAlinhamento"
                                value="#{PerfilEventoControle.produtos}" rows="5" var="produto">
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="#{CElabels['entidade.nome']}" />
                        </f:facet>
                        <h:panelGroup>
                            <h:outputText value="#{produto.descricao}" />
                        </h:panelGroup>
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="#{CElabels['operacoes.opcoes']}" />
                        </f:facet>
                        <a4j:commandButton
                                action="#{PerfilEventoControle.selecionarProduto}"
                                reRender="form"
                                oncomplete="Richfaces.hideModalPanel('panelConsultaProduto')"
                                value="#{CElabels['operacoes.selecionar']}"
                                image="/imagens/bt_editar.png"
                                title="#{CElabels['operacoes.selecionar.selecionarDado']}"
                                styleClass="botoes" />
                    </rich:column>
                </rich:dataTable>
                <rich:datascroller align="center"
                                   for="formConsultaProduto:resultadoConsultaProduto" maxPages="10"
                                   id="scResultadoConsultaProduto" />
                <h:panelGrid id="mensagemConsultaProduto" columns="1" width="100%"
                             styleClass="tabMensagens">
                    <h:panelGrid columns="1" width="100%">
                        <h:outputText styleClass="mensagem"
                                      value="#{PerfilEventoControle.mensagem}" />
                        <h:outputText styleClass="mensagemDetalhada"
                                      value="#{PerfilEventoControle.mensagemDetalhada}" />
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
        </a4j:form>
    </rich:modalPanel>

    <rich:modalPanel id="panelConsultaServico" autosized="true"
                     shadowOpacity="true" width="450" height="250">
        <f:facet name="header">

            <jsp:include page="../includes/topoReduzido.jsp" />

        </f:facet>
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="#{CElabels['entidade.servicos.consulta']}"></h:outputText>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:graphicImage value="/imagens/close.png" style="cursor:pointer"
                                id="hidelinkServico" />
                <rich:componentControl for="panelConsultaServico"
                                       attachTo="hidelinkServico" operation="hide" event="onclick" />
            </h:panelGroup>
        </f:facet>

        <a4j:form id="formConsultaServico" ajaxSubmit="true">
            <h:panelGrid columns="1" width="100%"
                         columnClasses="colunaCentralizada">
                <h:panelGrid columns="3" footerClass="colunaCentralizada"
                             width="100%">
                    <h:outputText value="#{CElabels['entidade.nome']}:" />
                    <h:inputText id="valorConsultaServico" size="50"
                                 onblur="blurinput(this);" onfocus="focusinput(this);"
                                 styleClass="form"
                                 value="#{PerfilEventoControle.filtroServicos.descricao}" />
                    <a4j:commandButton id="btnConsultarServico"
                                       reRender="formConsultaServico"
                                       action="#{PerfilEventoControle.consultarServicos}"
                                       styleClass="botoes"
                                       value="#{CElabels['operacoes.consulta.consultar']}"
                                       image="/imagens/botoesCE/buscar.png"
                                       title="#{CElabels['operacoes.consulta.consultarDados']}" />
                </h:panelGrid>
                <rich:dataTable id="resultadoConsultaServico" width="100%"
                                headerClass="consulta" rowClasses="linhaImpar, linhaPar"
                                columnClasses="colunaAlinhamento"
                                value="#{PerfilEventoControle.servicos}" rows="5" var="servico">
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="#{CElabels['entidade.nome']}" />
                        </f:facet>
                        <h:panelGroup>
                            <h:outputText value="#{servico.descricao}" />
                        </h:panelGroup>
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="#{CElabels['operacoes.opcoes']}" />
                        </f:facet>
                        <a4j:commandButton
                                action="#{PerfilEventoControle.selecionarServico}"
                                focus="valorPerfilEventoServico" reRender="form"
                                oncomplete="Richfaces.hideModalPanel('panelConsultaServico')"
                                value="#{CElabels['operacoes.selecionar']}"
                                image="/imagens/bt_editar.png"
                                title="#{CElabels['operacoes.selecionar.selecionarDado']}"
                                styleClass="botoes" />
                    </rich:column>
                </rich:dataTable>
                <rich:datascroller align="center"
                                   for="formConsultaServico:resultadoConsultaServico" maxPages="10"
                                   id="scResultadoConsultaServico" />
                <h:panelGrid id="mensagemConsultaServico" columns="1" width="100%"
                             styleClass="tabMensagens">
                    <h:panelGrid columns="1" width="100%">
                        <h:outputText styleClass="mensagem"
                                      value="#{PerfilEventoControle.mensagem}" />
                        <h:outputText styleClass="mensagemDetalhada"
                                      value="#{PerfilEventoControle.mensagemDetalhada}" />
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
        </a4j:form>
    </rich:modalPanel>

    <rich:modalPanel id="panelConsultaBemConsumo" autosized="true"
                     shadowOpacity="true" width="450" height="250">
        <f:facet name="header">

            <jsp:include page="../includes/topoReduzido.jsp" />

        </f:facet>
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="#{CElabels['entidade.bensConsumo.consulta']}"></h:outputText>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:graphicImage value="/imagens/close.png" style="cursor:pointer"
                                id="hidelinkBemConsumo" />
                <rich:componentControl for="panelConsultaBemConsumo"
                                       attachTo="hidelinkBemConsumo" operation="hide" event="onclick" />
            </h:panelGroup>
        </f:facet>

        <a4j:form id="formConsultaBemConsumo" ajaxSubmit="true">
            <h:panelGrid columns="1" width="100%"
                         columnClasses="colunaCentralizada">
                <h:panelGrid columns="3" footerClass="colunaCentralizada"
                             width="100%">
                    <h:outputText value="#{CElabels['entidade.nome']}:" />
                    <h:inputText id="valorConsultaBemConsumo" size="50"
                                 onblur="blurinput(this);" onfocus="focusinput(this);"
                                 styleClass="form"
                                 value="#{PerfilEventoControle.filtroBensConsumo.descricao}" />
                    <a4j:commandButton id="btnConsultarBemConsumo"
                                       reRender="formConsultaBemConsumo"
                                       action="#{PerfilEventoControle.consultarBensConsumo}"
                                       styleClass="botoes"
                                       value="#{CElabels['operacoes.consulta.consultar']}"
                                       image="/imagens/botoesCE/buscar.png"
                                       title="#{CElabels['operacoes.consulta.consultarDados']}" />
                </h:panelGrid>
                <rich:dataTable id="resultadoConsultaBemConsumo" width="100%"
                                headerClass="consulta" rowClasses="linhaImpar, linhaPar"
                                columnClasses="colunaAlinhamento"
                                value="#{PerfilEventoControle.bensConsumo}" rows="5"
                                var="bemConsumo">
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="#{CElabels['entidade.nome']}" />
                        </f:facet>
                        <h:panelGroup>
                            <h:outputText value="#{bemConsumo.descricao}" />
                        </h:panelGroup>
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="#{CElabels['entidade.valor']}" />
                        </f:facet>
                        <h:panelGroup>
                            <h:outputText value="#{bemConsumo.valorMonetario}" />
                        </h:panelGroup>
                    </rich:column>

                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="#{CElabels['operacoes.opcoes']}" />
                        </f:facet>
                        <a4j:commandButton
                                action="#{PerfilEventoControle.selecionarBemConsumo}"
                                focus="valorPerfilEventoBemConsumo" reRender="form"
                                oncomplete="Richfaces.hideModalPanel('panelConsultaBemConsumo')"
                                value="#{CElabels['operacoes.selecionar']}"
                                image="/imagens/bt_editar.png"
                                title="#{CElabels['operacoes.selecionar.selecionarDado']}"
                                styleClass="botoes" />
                    </rich:column>
                </rich:dataTable>
                <rich:datascroller align="center"
                                   for="formConsultaBemConsumo:resultadoConsultaBemConsumo"
                                   maxPages="10" id="scResultadoConsultaBemConsumo" />
                <h:panelGrid id="mensagemConsultaBemConsumo" columns="1"
                             width="100%" styleClass="tabMensagens">
                    <h:panelGrid columns="1" width="100%">
                        <h:outputText styleClass="mensagem"
                                      value="#{PerfilEventoControle.mensagem}" />
                        <h:outputText styleClass="mensagemDetalhada"
                                      value="#{PerfilEventoControle.mensagemDetalhada}" />
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
        </a4j:form>
    </rich:modalPanel>

    <rich:modalPanel id="panelConsultaUtensilio" autosized="true"
                     shadowOpacity="true" width="450" height="250">
        <f:facet name="header">

            <jsp:include page="../includes/topoReduzido.jsp" />

        </f:facet>
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="#{CElabels['entidade.utensilios.consulta']}"></h:outputText>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:graphicImage value="/imagens/close.png" style="cursor:pointer"
                                id="hidelinkUtensilio" />
                <rich:componentControl for="panelConsultaUtensilio"
                                       attachTo="hidelinkUtensilio" operation="hide" event="onclick" />
            </h:panelGroup>
        </f:facet>

        <a4j:form id="formConsultaUtensilio" ajaxSubmit="true">
            <h:panelGrid columns="1" width="100%"
                         columnClasses="colunaCentralizada">
                <h:panelGrid columns="3" footerClass="colunaCentralizada"
                             width="100%">
                    <h:outputText value="#{CElabels['entidade.nome']}:" />
                    <h:inputText id="valorConsultaUtensilio" size="50"
                                 onblur="blurinput(this);" onfocus="focusinput(this);"
                                 styleClass="form"
                                 value="#{PerfilEventoControle.filtroUtensilios.descricao}" />
                    <a4j:commandButton id="btnConsultarUtensilio"
                                       reRender="formConsultaUtensilio"
                                       action="#{PerfilEventoControle.consultarUtensilios}"
                                       styleClass="botoes"
                                       value="#{CElabels['operacoes.consulta.consultar']}"
                                       image="/imagens/botoesCE/buscar.png"
                                       title="#{CElabels['operacoes.consulta.consultarDados']}" />
                </h:panelGrid>
                <rich:dataTable id="resultadoConsultaUtensilio" width="100%"
                                headerClass="consulta" rowClasses="linhaImpar, linhaPar"
                                columnClasses="colunaAlinhamento"
                                value="#{PerfilEventoControle.utensilios}" rows="5" var="utensilio">
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="#{CElabels['entidade.nome']}" />
                        </f:facet>
                        <h:panelGroup>
                            <h:outputText value="#{utensilio.descricao}" />
                        </h:panelGroup>
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="#{CElabels['operacoes.opcoes']}" />
                        </f:facet>
                        <a4j:commandButton
                                action="#{PerfilEventoControle.selecionarUtensilio}"
                                focus="valorPerfilEventoUtensilio" reRender="form"
                                oncomplete="Richfaces.hideModalPanel('panelConsultaUtensilio')"
                                value="#{CElabels['operacoes.selecionar']}"
                                image="/imagens/bt_editar.png"
                                title="#{CElabels['operacoes.selecionar.selecionarDado']}"
                                styleClass="botoes" />
                    </rich:column>
                </rich:dataTable>
                <rich:datascroller align="center"
                                   for="formConsultaUtensilio:resultadoConsultaUtensilio"
                                   maxPages="10" id="scResultadoConsultaUtensilio" />
                <h:panelGrid id="mensagemConsultaUtensilio" columns="1" width="100%"
                             styleClass="tabMensagens">
                    <h:panelGrid columns="1" width="100%">
                        <h:outputText styleClass="mensagem"
                                      value="#{PerfilEventoControle.mensagem}" />
                        <h:outputText styleClass="mensagemDetalhada"
                                      value="#{PerfilEventoControle.mensagemDetalhada}" />
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
        </a4j:form>
    </rich:modalPanel>

    <rich:modalPanel id="panelConsultaBrinquedo" autosized="true"
                     shadowOpacity="true" width="450" height="250">
        <f:facet name="header">

            <jsp:include page="../includes/topoReduzido.jsp" />

        </f:facet>
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="#{CElabels['entidade.brinquedos.consulta']}"></h:outputText>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:graphicImage value="/imagens/close.png" style="cursor:pointer"
                                id="hidelinkBrinquedo" />
                <rich:componentControl for="panelConsultaBrinquedo"
                                       attachTo="hidelinkBrinquedo" operation="hide" event="onclick" />
            </h:panelGroup>
        </f:facet>

        <a4j:form id="formConsultaBrinquedo" ajaxSubmit="true">
            <h:panelGrid columns="1" width="100%"
                         columnClasses="colunaCentralizada">
                <h:panelGrid columns="3" footerClass="colunaCentralizada"
                             width="100%">
                    <h:outputText value="#{CElabels['entidade.nome']}:" />
                    <h:inputText id="valorConsultaBrinquedo" size="50"
                                 onblur="blurinput(this);" onfocus="focusinput(this);"
                                 styleClass="form"
                                 value="#{PerfilEventoControle.filtroBrinquedos.descricao}" />
                    <a4j:commandButton id="btnConsultarBrinquedo"
                                       reRender="formConsultaBrinquedo"
                                       action="#{PerfilEventoControle.consultarBrinquedos}"
                                       styleClass="botoes"
                                       value="#{CElabels['operacoes.consulta.consultar']}"
                                       image="/imagens/botoesCE/buscar.png"
                                       title="#{CElabels['operacoes.consulta.consultarDados']}" />
                </h:panelGrid>
                <rich:dataTable id="resultadoConsultaBrinquedo" width="100%"
                                headerClass="consulta" rowClasses="linhaImpar, linhaPar"
                                columnClasses="colunaAlinhamento"
                                value="#{PerfilEventoControle.brinquedos}" rows="5" var="brinquedo">
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="#{CElabels['entidade.nome']}" />
                        </f:facet>
                        <h:panelGroup>
                            <h:outputText value="#{brinquedo.descricao}" />
                        </h:panelGroup>
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="#{CElabels['operacoes.opcoes']}" />
                        </f:facet>
                        <a4j:commandButton
                                action="#{PerfilEventoControle.selecionarBrinquedo}"
                                focus="valorPerfilEventoBrinquedo" reRender="form"
                                oncomplete="Richfaces.hideModalPanel('panelConsultaBrinquedo')"
                                value="#{CElabels['operacoes.selecionar']}"
                                image="/imagens/bt_editar.png"
                                title="#{CElabels['operacoes.selecionar.selecionarDado']}"
                                styleClass="botoes" />
                    </rich:column>
                </rich:dataTable>
                <rich:datascroller align="center"
                                   for="formConsultaBrinquedo:resultadoConsultaBrinquedo"
                                   maxPages="10" id="scResultadoConsultaBrinquedo" />
                <h:panelGrid id="mensagemConsultaBrinquedo" columns="1" width="100%"
                             styleClass="tabMensagens">
                    <h:panelGrid columns="1" width="100%">
                        <h:outputText styleClass="mensagem"
                                      value="#{PerfilEventoControle.mensagem}" />
                        <h:outputText styleClass="mensagemDetalhada"
                                      value="#{PerfilEventoControle.mensagemDetalhada}" />
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
        </a4j:form>
    </rich:modalPanel>
    <%@include file="../../../includes/include_modal_mensagem_generica.jsp"%>
</f:view>
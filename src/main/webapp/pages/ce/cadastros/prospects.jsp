<%@page contentType="text/html" pageEncoding="ISO-8859-1" %>
<%@include file="../includes/include_imports.jsp" %>


<f:view>
    <jsp:include page="/includes/include_carregando_ripple.jsp" flush="true"/>
    <h:form id="form">
    	<html>
    			
    	<input type="hidden" value="centralEventos" name="modulo"/>
		<!-- Inclui o elemento HEAD da página -->
		<%@include file="/pages/ce/includes/include_head.jsp" %>
		
		<script>
		function alternarFiltro() {
			limparMensagensFiltros();
			var itens = document.getElementsByName('form:tipoFiltroPeriodo');
			for (var i = 0; i < itens.length; i++) {
				if (itens[i].checked) {
					var divFiltroData = document.getElementById('filtroData');
					var divFiltroIntervalo = document.getElementById('filtroIntervalo');
					switch (itens[i].value) {
					case '1':
						divFiltroData.style.visibility='visible';
						divFiltroIntervalo.style.visibility='hidden';
						break;
					case '2':
						divFiltroData.style.visibility='hidden';
						divFiltroIntervalo.style.visibility='visible';
						break;
					}
				}
			}
		}
		
		function validarFiltros() {
			limparMensagensFiltros();
			var itens = document.getElementsByName('form:tipoFiltroPeriodo');
			var data = document.getElementById('form:dataInputDate');
			var dataInicio = document.getElementById('form:dataInicioInputDate');
			var dataFim = document.getElementById('form:dataFimInputDate');
			
			var validade=true;
			
			for (var i = 0; i < itens.length; i++) {
				if (itens[i].checked) {
					switch (itens[i].value) {
					case '1':
						if (data == null || data.value == null || data.value == "") {
							exibirMensagem('<h:outputText value="#{Mensagens[\'campoObrigatorio.data\']}"/>', 'filtroData');
							validade = false;
						}	
						break;
					case '2':
						if ((dataInicio == null || dataInicio.value == null || dataInicio.value == "")||(dataFim == null || dataFim.value == null || dataFim.value == "")) {
							exibirMensagem('<h:outputText value="#{Mensagens[\'campoObrigatorio.intervalo\']}"/>', 'filtroIntervalo');
							validade = false;
						}
						break;
					
					}
				}
			}
			return validade;
		}
		
		function limparMensagensFiltros() {
			limparMensagem('filtroData');
			limparMensagem('filtroIntervalo');
		}
		
		function limparFiltros(){
			document.getElementById('form:cliente').value = '';
			document.getElementById('form:evento').value = '';
			document.getElementById('form:dataInputDate').value = '';
			document.getElementById('form:dataInicioInputDate').value = '';	
			document.getElementById('form:dataFimInputDate').value = '';	
		}

		function ativarAjuda(id) {
			if (typeof id == "string") {
				var hint = document.getElementById('hint-' + id);
				hint.style.visibility = hint.style.visibility == 'hidden' ? 'visible' : 'hidden';
			} else {
				for (var i = 0; i < id.length; i++) {
					var hint = document.getElementById('hint-' + id[i]);
					hint.style.visibility = hint.style.visibility == 'hidden' ? 'visible' : 'hidden';
				}
			}
		}
</script>
            
		
		<body>

             <a4j:keepAlive beanName="ProspectsControle" />
            <h:panelGroup layout="block" style="display: inline-table;width: 100%;" styleClass="fundoCinza">
                <h:panelGroup layout="block" styleClass="bgtop topoZW" rendered="#{MenuControle.apresentarTopo}">
                    <jsp:include page="../../../include_topo_2.0.jsp" flush="true"/>
                    <jsp:include page="../../../include_menu_ce_flat.jsp" flush="true"/>
                </h:panelGroup>

                <h:panelGroup layout="block" styleClass="caixaCorpo">
                    <h:panelGroup layout="block" style="height: 80%;width: 100%">
                        <h:panelGroup layout="block" styleClass="caixaMenuLatel">
                            <h:panelGroup layout="block" styleClass="container-imagem">
                                <h:panelGroup layout="block" styleClass="container-box container-conteudo-central">
                                    <h:panelGroup styleClass="container-box-header" layout="block">
                                        <h:panelGroup layout="block" styleClass="margin-box">
                                            <h:outputText styleClass="container-header-titulo" value="#{CElabels['menu.operacoesCE.listaProspects']}"/>
                                            <h:outputLink styleClass="linkWiki"
                                                          value="#{SuperControle.urlWikiCE}Consultas:ListaProspects"
                                                          title="Clique e saiba mais: Lista de Prospects"
                                                          target="_blank">
                                                <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                                            </h:outputLink>
                                        </h:panelGroup>
                                    </h:panelGroup>
                                    <h:panelGroup layout="block" styleClass="margin-box">
                                        <%@include file="../includes/include_prospects.jsp" %>
                                    </h:panelGroup>
                                </h:panelGroup>
                            </h:panelGroup>
                            <%@include file="../includes/include_box_menulateral.jsp" %>
                        </h:panelGroup>
                    </h:panelGroup>
                </h:panelGroup>

                <jsp:include page="../../../include_rodape_flat.jsp" flush="true" />
                <rich:jQuery id="mskData" selector=".rich-calendar-input" timing="onload" query="mask('99/99/9999')" />
            </h:panelGroup>
        <%@include file="/pages/ce/includes/include_focus.jsp" %>
        </body>
        </html>
    </h:form>   
</f:view>

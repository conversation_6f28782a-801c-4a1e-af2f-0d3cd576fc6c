<%@page contentType="text/html" pageEncoding="ISO-8859-1" %>
<%@include file="../includes/include_imports.jsp" %>

<f:view>
    <jsp:include page="/includes/include_carregando_ripple.jsp" flush="true"/>
    <h:form id="form">
        <html>
        <!-- <PERSON><PERSON><PERSON> o elemento HEAD da página -->
        <head>
            <%@include file="../includes/include_head_ce.jsp" %>
        </head>
        <body class="ce">
        <h:panelGroup layout="block" style="display: inline-table;width: 100%;" styleClass="fundoCinza">

            <h:panelGroup layout="block" styleClass="bgtop topoZW" rendered="#{MenuControle.apresentarTopo}">
                <jsp:include page="../../../include_topo_2.0.jsp" flush="true"/>
                <jsp:include page="../../../include_menu_ce_flat.jsp" flush="true"/>
                <rich:jQuery selector=".item2" query="addClass('menuItemAtual')"/>
            </h:panelGroup>

            <h:panelGroup layout="block" styleClass="caixaCorpo">
                <h:panelGroup layout="block" style="height: 80%;width: 100%">
                    <h:panelGroup layout="block" styleClass="caixaMenuLatel">
                        <h:panelGroup layout="block" styleClass="container-imagem container-conteudo-central">
                            <div style="clear:both;">
                                <table width="98%" border="0" align="left" cellpadding="0" cellspacing="0" class="text" style="margin-right:30px;margin-bottom:20px;">
                                    <tr>
                                        <td width="19" height="50" align="left" valign="top"><img src="${contexto}/images/box_centro_top_left.gif" width="19" height="50"></td>
                                        <td align="left" valign="top" background="${contexto}/images/box_centro_top.gif" class="tituloboxcentro" style="padding:11px 0 0 0;">Produtos / Perfis de Eventos</td>
                                        <td width="19" align="left" valign="top"><img src="${contexto}/images/box_centro_top_right.gif" width="19" height="50"></td>
                                    </tr>
                                    <tr>
                                        <td align="left" valign="top" background="${contexto}/images/box_centro_left.gif"><img src="${contexto}/images/shim.gif"></td>
                                        <td align="left" valign="top" bgcolor="#ffffff" style="padding:15px 15px 5px 15px;" class="headerIndex">
                                            <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0" >
                                                <h:panelGrid columnClasses="w33,w33,w33"  columns="3" width="100%" cellpadding="0" cellspacing="0">


                                                    <h:panelGrid columns="1" style="height:100%;" width="100%" cellpadding="5" cellspacing="5" >
                                                        <h:panelGroup>
                                                            <rich:spacer height="5px"/>
                                                            <div>
                                                                <h:outputLink styleClass="linkWiki"
                                                                              value="#{SuperControle.urlWikiCE}Cadastros:PP:Produto"
                                                                              title="Clique e saiba mais: Produto"
                                                                              target="_blank" >
                                                                    <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                                                                </h:outputLink>
                                                                <a class="titulo3" id="produtosLocacao" onclick="abrirPopup('${contexto}/faces/pages/ce/cadastros/produtoLocacao.jsp?modulo=centralEventos','', 900, 595);" href="#">
                                                                    <h:outputText  value="#{CElabels['menu.cadastros.produtos.produtosLocacao']}"/>
                                                                </a>
                                                            </div>
                                                            <rich:spacer height="25px"/>
                                                            <rich:spacer width="10px"/>
                                                            <h:outputText styleClass="text" value="Cadastre todos os produtos que podem ser oferecidos nos seus eventos, definindo o valor e o seu tipo podendo ser Bem de consumo, Utensílios ou Brinquedos. Produtos como brinquedos podem ser rastreados, basta inserir um número de patrimônio para cada, e poderá saber em qual evento cada brinquedo está sendo utilizado."/>
                                                        </h:panelGroup>
                                                        <h:panelGroup>
                                                            <rich:spacer height="25px"/>
                                                            <div>
                                                                <h:outputLink styleClass="linkWiki"
                                                                              value="#{SuperControle.urlWikiCE}Cadastros:PP:Fornecedor"
                                                                              title="Clique e saiba mais: Fornecedor"
                                                                              target="_blank" >
                                                                    <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                                                                </h:outputLink>
                                                                <a4j:commandLink value="Fornecedor"
                                                                                 oncomplete="abrirPopup('../cadastros/fornecedor.jsp?modulo=centralEventos','', 900, 595);"/>
                                                            </div>
                                                            <rich:spacer height="25px"/>
                                                            <rich:spacer width="10px"/>
                                                            <h:outputText styleClass="text" value="Aqui você poderá cadastrar todos os seus fornecedores de serviços para a realização do evento, podendo nos seus orçamentos inserir os terceirizados que serão necessários e seus valores não influenciaram para o cliente, ficará somente como informação para realizar o evento. Ao realizar o check-list essas informações serão dispostas."/>
                                                        </h:panelGroup>
                                                        <h:panelGroup>
                                                            <rich:spacer height="25px"/>
                                                            <div>
                                                                <h:outputLink styleClass="linkWiki"
                                                                              value="#{SuperControle.urlWikiCE}Cadastros:PP:Serviço"
                                                                              title="Clique e saiba mais: Serviço"
                                                                              target="_blank" >
                                                                    <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                                                                </h:outputLink>
                                                                <a class="titulo3" id="servico" onclick="abrirPopup('${contexto}/faces/pages/ce/cadastros/servico.jsp?modulo=centralEventos','', 900, 595);" href="#">
                                                                    <h:outputText value="#{CElabels['menu.cadastros.produtos.servicos']}"/>
                                                                </a>
                                                            </div>
                                                            <rich:spacer height="25px"/>
                                                            <rich:spacer width="10px"/>
                                                            <h:outputText styleClass="text" value="Cadastre todos os serviços que podem ser oferecidos nos seus eventos, definindo o fornecedor. Podendo ser a própria empresa ou uma empresa terceirizada já cadastrada."/>
                                                        </h:panelGroup>
                                                    </h:panelGrid>
                                                    <h:panelGrid style="height:100;%" columns="1" width="100%" cellpadding="5" cellspacing="5" >
                                                        <h:panelGroup>
                                                            <div>
                                                                <h:outputLink styleClass="linkWiki"
                                                                              value="#{SuperControle.urlWikiCE}Cadastros:PP:Ambiente"
                                                                              title="Clique e saiba mais: Ambiente"
                                                                              target="_blank" >
                                                                    <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                                                                </h:outputLink>
                                                                <a class="titulo3" id="ambiente" onclick="abrirPopup('${contexto}/faces/ambienteCons.jsp?modulo=centralEventos', 'Ambiente', 900, 595);" href="#">
                                                                    <h:outputText  value="#{CElabels['menu.cadastros.perfisEventos.ambiente']}"/>
                                                                </a>
                                                            </div>
                                                            <rich:spacer height="25px"/>
                                                            <rich:spacer width="10px"/>
                                                            <h:outputText styleClass="text" value="Cadastre todos os ambientes disponíveis para realização de eventos, definindo o seu tipo e a quantidade máxima de convidados suportada. Exemplo: Nome do ambiente: Salão Rosa, tipo: Salão, capacidade: 150 convidados."/>
                                                        </h:panelGroup>
                                                        <h:panelGroup>
                                                            <rich:spacer height="25px"/>
                                                            <div>
                                                                <h:outputLink styleClass="linkWiki"
                                                                              value="#{SuperControle.urlWikiCE}Cadastros:PP:Tipo_Ambiente"
                                                                              title="Clique e saiba mais: Tipo de Ambiente"
                                                                              target="_blank" >
                                                                    <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                                                                </h:outputLink>
                                                                <a class="titulo3" id="tipoAmbiente" onclick="abrirPopup('${contexto}/faces/pages/ce/cadastros/tipoAmbiente	.jsp?modulo=centralEventos', 'Tipo Ambiente', 900, 595);" href="#">
                                                                    <h:outputText value="#{CElabels['menu.cadastros.perfisEventos.tipoAmbiente']}"/>
                                                                </a>
                                                            </div>
                                                            <rich:spacer height="25px"/>
                                                            <rich:spacer width="10px"/>
                                                            <h:outputText styleClass="text" value="Cadastre os tipos de ambientes disponíveis, definindo a duração mínima em horas, quantas reservas por dia podem ocorrer, quanto tempo adicional permitido e o horário inicial e final que o ambiente fica disponível para reservas. Exemplo: Tipo de ambiente: Salão, duração mínima: 6 horas, reservas máximas por dia: 4, tempo adicional: 1 hora permitido e horário disponível para reservas: 08:00 às 05:00 horas (madrugada)."/>
                                                        </h:panelGroup>



                                                    </h:panelGrid>
                                                    <h:panelGrid columns="1" width="100%" style="height:100%;" cellpadding="5" cellspacing="5" >
                                                        <h:panelGroup>
                                                            <div><img src="${contexto}/imagens/cadastros.png"></div>
                                                        </h:panelGroup>

                                                        <h:panelGroup>
                                                            <rich:spacer height="25px"/>
                                                            <div>
                                                                <h:outputLink styleClass="linkWiki"
                                                                              value="#{SuperControle.urlWikiCE}Cadastros:PP:Tabela_Pre%C3%A7o_Perfil_Evento"
                                                                              title="Clique e saiba mais: Tabela de Preços/Perfil Evento"
                                                                              target="_blank" >
                                                                    <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                                                                </h:outputLink>
                                                                <a class="titulo3" id="tabelaPrecos" onclick="abrirPopup('${contexto}/faces/pages/ce/cadastros/perfilEvento.jsp?modulo=centralEventos','aa', 900, 595);" href="#">
                                                                    <h:outputText  value="#{CElabels['menu.cadastros.perfisEventos.tabelaPrecos']}"/>
                                                                </a>
                                                            </div>
                                                            <rich:spacer height="25px"/>
                                                            <rich:spacer width="10px"/>
                                                            <h:outputText styleClass="text" value="Cadastre os perfis dos seus eventos, tornando mais fácil e ágil o atendimento ao cliente na hora de preencher orçamentos. Perfis de Eventos seriam pacotes contendo os ambientes disponíveis, produtos e serviços que podem ser oferecidos e os documentos de contrato e or~çamento necessários conforme o tipo de evento. Quando o cliente entrar em contato você terá o perfil pré-cadastrado que atende a necessidade dele. Exemplo: Perfil ANIVERSÁRIO, onde temos as bebidas, o Buffet, a decoração, a animação conforme este tipo de evento."/>
                                                        </h:panelGroup>
                                                    </h:panelGrid>

                                                </h:panelGrid>
                                            </h:panelGrid>
                                        </td>
                                        <td align="left" valign="top" background="${contexto}/images/box_centro_right.gif"><img src="${contexto}/images/shim.gif"></td>
                                    </tr>
                                    <tr>
                                        <td height="20" align="left" valign="top"><img src="${contexto}/images/box_centro_bottom_left.gif" width="19" height="20"></td>
                                        <td align="left" valign="top" background="${contexto}/images/box_centro_bottom.gif"><img src="${contexto}/images/shim.gif"></td>
                                        <td align="left" valign="top"><img src="${contexto}/images/box_centro_bottom_right.gif" width="19" height="20"></td>
                                    </tr>
                                </table>
                            </div>
                        </h:panelGroup>
                        <jsp:include page="../includes/include_box_menulatcadastrosprodutosperfis.jsp" flush="true"/>
                    </h:panelGroup>
                </h:panelGroup>
            </h:panelGroup>

            <jsp:include page="../../../include_rodape_flat.jsp" flush="true"/>
            <%@include file="../includes/include_focus.jsp" %>
        </h:panelGroup>
        </body>
        </html>
     </h:form>
</f:view>

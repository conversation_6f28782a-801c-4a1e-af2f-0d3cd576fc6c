<%@include file="includes/include_imports.jsp" %>

<c:set var="moduloSession" value="1" scope="session" />

<f:view>
	<jsp:include page="/includes/include_carregando_ripple.jsp" flush="true"/>
    <h:form id="form">
        <html>
        	<!-- Inclui o elemento HEAD da p�gina -->
            <%@include file="includes/include_head.jsp" %>
            <script>
function alternarConsulta() {
                
				
				var itens = document.getElementsByName('form:tipoPesquisa');
				for (var i = 0; i < itens.length; i++) {
					if (itens[i].checked) {
						
						var pesquisa = document.getElementById('pesquisaNomeEvento');
						switch (itens[i].value) {
						case '1':
							pesquisa.style.display='none';
							
							
							break;
						case '2':

							pesquisa.style.display='block';
							
							break;
						}
					}
				}
			}       
		            </script>
            <body>
			<a4j:keepAlive beanName="PesquisaGeralControle" />
			<h:panelGroup layout="block" style="display: inline-table;width: 100%;" styleClass="fundoCinza">
				<h:panelGroup layout="block" styleClass="bgtop topoZW" rendered="#{MenuControle.apresentarTopo}">
					<jsp:include page="../../include_topo_2.0.jsp" flush="true"/>
					<jsp:include page="../../include_menu_ce_flat.jsp" flush="true"/>
				</h:panelGroup>

				<h:panelGroup layout="block" styleClass="caixaCorpo">
					<h:panelGroup layout="block" style="height: 80%;width: 100%">
						<h:panelGroup layout="block" styleClass="caixaMenuLatel">
							<h:panelGroup layout="block" styleClass="container-imagem container-conteudo-central">
								<h:panelGroup layout="block" styleClass="container-box zw_ui especial ">
									<h:panelGroup styleClass="container-box-header" layout="block">
										<h:panelGroup layout="block" styleClass="margin-box">
											<h:outputText styleClass="container-header-titulo" value="#{CElabels['menu.operacoesCE.pesquisa']}"/>
											<h:outputLink styleClass="linkWiki"
														  value="#{SuperControle.urlWikiCE}Consultas:Pesquisa_Geral"
														  title="Clique e saiba mais: Pesquisa Geral"
														  target="_blank">
												<i class="fa-icon-question-sign" style="font-size: 18px"></i>
											</h:outputLink>
										</h:panelGroup>
									</h:panelGroup>
									<h:panelGroup layout="block" styleClass="margin-box">
										<h:panelGrid id="mensagens" columns="1" width="100%" styleClass="tabMensagens">
											<h:panelGrid columns="3" width="100%" styleClass="tabMensagens">
												<h:panelGrid columns="1" width="100%">

													<h:outputText value=" "/>

												</h:panelGrid>
												<h:commandButton rendered="#{PesquisaGeralControle.sucesso}" image="/imagens/bt_sucesso.png"/>
												<h:commandButton rendered="#{PesquisaGeralControle.erro}" image="/imagens/erro.png"/>
												<h:panelGrid columns="1" width="100%">
													<h:outputText styleClass="mensagem" value="#{PesquisaGeralControle.mensagem}"/>
													<h:outputText styleClass="mensagemDetalhada" value="#{PesquisaGeralControle.mensagemDetalhada}"/>
												</h:panelGrid>
											</h:panelGrid>
										</h:panelGrid>
										&nbsp;
										<rich:simpleTogglePanel id="Consulta" switchType="client" opened="false" height="220px">
											<f:facet name="header">
												<h:outputText value="#{CElabels['entidade.filtroPesquisa']}:" />
											</f:facet>
											<h:panelGrid columns="2" rowClasses="linhaPar" columnClasses="classEsquerda, classDireita" width="100%">
												<h:panelGroup>
													<h:selectOneRadio  onclick="alternarConsulta();" id="tipoPesquisa" layout="pageDirection" styleClass="tituloCampos"
																	   value="#{PesquisaGeralControle.tipoConsulta}" style="width: 100%; height: 50px;">
														<f:selectItems value="#{PesquisaGeralControle.tiposPesquisa}" />
													</h:selectOneRadio>
												</h:panelGroup>
												<h:panelGroup></h:panelGroup>
											</h:panelGrid>
											<div style="display: block; height:56px;" id="pesquisaNomeEvento">
												<h:panelGrid columns="2" rowClasses="linhaImpar,linhaPar" columnClasses="classEsquerda, classDireita" width="100%">
													<h:outputText styleClass="tituloCampos" value="#{CElabels['entidade.cliente.nome']}:"/>
													<h:inputText maxlength="100" id="nomeCliente" onblur="blurinput(this);"
																 onfocus="focusinput(this);" styleClass="form" value="#{PesquisaGeralControle.evento.interessado.nomeCliente}"/>

													<h:outputText styleClass="tituloCampos" value="#{CElabels['entidade.evento.nome']}:"/>
													<h:inputText maxlength="100" id="nomeEvento" onblur="blurinput(this);"
																 onfocus="focusinput(this);" styleClass="form" value="#{PesquisaGeralControle.evento.nomeEvento}"/>
												</h:panelGrid>
											</div>

											<h:panelGrid columns="2" rowClasses="linhaImpar,linhaPar" columnClasses="classEsquerda, classDireita" width="100%">
												<h:outputText styleClass="tituloCampos" value="#{CElabels['entidade.data']}:"/>
												<h:panelGroup>
													<h:outputText styleClass="tituloCampos" value="#{CElabels['entidade.data.de']}"/>&nbsp;
													<rich:calendar id="dataInicio" styleClass="form" value="#{PesquisaGeralControle.evento.dataInicio}"
																   datePattern="dd/MM/yyyy" inputSize="10" inputClass="form" oninputblur="blurinput(this);" oninputfocus="focusinput(this);"
																   oninputchange="if(!validar_Data(this.id)) {this.value = ''};" oninputkeypress="return mascara(this, '99/99/9999', event);"
																   enableManualInput="true" zindex="2" showWeeksBar="false" />&nbsp;
													<h:outputText styleClass="tituloCampos" value="#{CElabels['entidade.dataAte']}"/>&nbsp;
													<rich:calendar id="dataFim" styleClass="form" value="#{PesquisaGeralControle.evento.dataFim}"
																   datePattern="dd/MM/yyyy" inputSize="10" inputClass="form" oninputblur="blurinput(this);" oninputfocus="focusinput(this);"
																   oninputchange="if(!validar_Data(this.id)) {this.value = ''};" oninputkeypress="return mascara(this, '99/99/9999', event);"
																   enableManualInput="true" zindex="2" showWeeksBar="false" />
												</h:panelGroup>

												<h:outputText styleClass="tituloCampos" value="#{CElabels['entidade.categoriaAmbiente']}: " />
												<!-- Combo de Tipos de Ambiente -->
												<h:selectOneMenu onblur="blurinput(this);" onfocus="focusinput(this);" id="tiposAmbiente"
																 value="#{PesquisaGeralControle.evento.ambienteInteresse.tipoAmbiente}" >
													<f:selectItem itemValue="" itemLabel="#{CElabels['operacoes.todos']}"/>
													<f:selectItem itemValue="0" itemLabel="#{CElabels['entidade.agruparTipo']}"/>
													<f:selectItems value="#{PesquisaGeralControle.tiposAmbiente}" />
													<a4j:support event="onchange" reRender="ambientes"></a4j:support>
												</h:selectOneMenu>

												<h:outputText styleClass="tituloCampos" value="#{CElabels['entidade.ambientes']}: " />
												<!-- Combo de Ambientes -->
												<h:selectOneMenu onblur="blurinput(this);" onfocus="focusinput(this);" id="ambientes"
																 value="#{PesquisaGeralControle.evento.ambienteInteresse.codigo}" >
													<f:selectItem itemValue="" itemLabel="#{CElabels['operacoes.todos']}"/>
													<f:selectItem itemValue="0" itemLabel="#{CElabels['entidade.agruparAmbiente']}"/>
													<f:selectItems value="#{PesquisaGeralControle.ambientes}" />
												</h:selectOneMenu>

												<h:outputText styleClass="tituloCampos" value="#{CElabels['entidade.evento.situacao']}: " />
												<!-- Combo Situa��o -->
												<h:selectOneMenu onblur="blurinput(this);" onfocus="focusinput(this);" id="situacao"
																 value="#{PesquisaGeralControle.codigoSituacao}">
													<f:selectItem itemValue="" itemLabel="#{CElabels['operacoes.todos']}"/>
													<f:selectItems value="#{PesquisaGeralControle.situacoes}" />
												</h:selectOneMenu>

											</h:panelGrid>
										</rich:simpleTogglePanel>
										<br />
										<table width="100%" border="0" align="center" cellpadding="0" cellspacing="0">
											<tr>
												<td width="10"><img src="${contexto}/imagens/agenda_imgs/menu_esq.jpg" width="10" height="69"></td>
												<td background="${contexto}/imagens/agenda_imgs/menu_fundo.jpg"><table width="100%" border="0" cellspacing="0" cellpadding="0">
													<tr>

														<td align="center">
															<a4j:commandLink id="consultarEspecifico" reRender="eventos, mensagens"
																			 title="#{CElabels['operacoes.consulta.consultar']}"
																			 action="#{PesquisaGeralControle.consulta}">
																<img src="${contexto}/imagens/botoesCE/buscar.png" border="0" title="Buscar">
															</a4j:commandLink>
														</td>

													</tr>
												</table></td>
												<td width="10"><img src="${contexto}/imagens/agenda_imgs/menu_dir.jpg" width="10" height="69"></td>
											</tr>
										</table>
										<br />
										<rich:extendedDataTable value="#{PesquisaGeralControle.listaEventos}" sortMode="" var="evento" id="eventos" height="400px">

											<rich:column sortable="true" sortBy="#{evento.nomeCliente}" width="20%"
														 filterBy="#{evento.nomeCliente}" filterEvent="onkeyup" label="#{CElabels['entidade.cliente']}">
												<f:facet name="header">
													<h:outputText value="#{CElabels['entidade.cliente']}"/>
												</f:facet>
												<h:outputText value="#{evento.nomeCliente}"/>
											</rich:column>

											<rich:column sortable="true" sortBy="#{evento.nome}" width="20%"
														 filterBy="#{evento.nome}" filterEvent="onkeyup" label="#{CElabels['entidade.evento']}">
												<f:facet name="header">
													<h:outputText value="#{CElabels['entidade.evento']}"/>
												</f:facet>
												<h:outputText value="#{evento.nome}"/>
											</rich:column>
											<rich:column sortable="true" sortBy="#{evento.ambiente}" width="20%"
														 filterBy="#{evento.ambiente}" filterEvent="onkeyup" label="#{CElabels['entidade.ambiente']}">
												<f:facet name="header">
													<h:outputText value="#{CElabels['entidade.ambiente']}"/>
												</f:facet>
												<h:outputText value="#{evento.ambiente}"/>
											</rich:column>


											<rich:column sortable="true" sortBy="#{evento.valorFinal}" width="10%" label="#{CElabels['entidade.data']}">
												<f:facet name="header">
													<h:outputText value="#{CElabels['entidade.valor']}"/>
												</f:facet>
												<h:outputText value="#{evento.valorFinalMonetario}"/>
											</rich:column>

											<c:choose>
												<c:when test="${PesquisaGeralControle.tipoConsulta eq 2}">

													<rich:column sortable="true" sortBy="#{evento.dataInteresse}" width="10%" label="#{CElabels['entidade.data']}">
														<f:facet name="header">
															<h:outputText value="#{CElabels['entidade.data']}"/>
														</f:facet>
														<h:outputText value="#{evento.dataInteresseFormatada}"/>
													</rich:column>
												</c:when>
												<c:otherwise>
													<rich:column sortable="true" sortBy="#{evento.nrEventos}" width="10%" label="#{CElabels['entidade.cliente']}">
														<f:facet name="header">
															<h:outputText value="#{CElabels['entidade.nrEventos']}"/>
														</f:facet>
														<h:outputText value="#{evento.nrEventos}"/>
													</rich:column>
												</c:otherwise>
											</c:choose>

											<rich:column sortable="true" sortBy="#{evento.situacao.descricao}" width="20%"
														 filterBy="#{evento.situacao.descricao}" filterEvent="onkeyup" label="#{CElabels['entidade.situacao']}">
												<f:facet name="header">
													<h:outputText value="#{CElabels['entidade.situacao']}"/>
												</f:facet>
												<h:outputText value="#{evento.situacao.descricao}"/>
											</rich:column>
										</rich:extendedDataTable>


									</h:panelGroup>
								</h:panelGroup>
							</h:panelGroup>
							<%@include file="includes/include_box_menulateral.jsp" %>
						</h:panelGroup>
					</h:panelGroup>
				</h:panelGroup>

				<jsp:include page="../../include_rodape_flat.jsp" flush="true" />
				<rich:jQuery id="mskData" selector=".rich-calendar-input" timing="onload" query="mask('99/99/9999')" />
			</h:panelGroup>
            	<%@include file="includes/include_focus.jsp" %>
            </body>
			
        </html>
    </h:form>
</f:view>


<%@include file="../includes/include_imports.jsp" %>
<rich:modalPanel id="panelAlterarValores" autosized="true" shadowOpacity="true" width="500" height="380"
	showWhenRendered="#{OrcamentoDetalhadoControle.alterarValores}">
	        
	        <f:facet name="header">
	            <h:panelGroup>
	                <h:outputText value="#{CElabels['menu.operacoesCE.Alterar']}"></h:outputText>
	            </h:panelGroup>
	        </f:facet>
	        <f:facet name="controls">
	            <h:panelGroup>
	                <h:graphicImage value="/imagens/close.png" style="cursor:pointer" id="hidelinkAlterarValores"/>
	                <rich:componentControl for="panelAlterarValores" attachTo="hidelinkAlterarValores" operation="hide" event="onclick"/>
	            </h:panelGroup>
	        </f:facet>
	
	        <h:form id="formAlterarValores">
	        			<div style="width: 500px; height: 380px;">
	            		 <h:panelGrid columns="1" style="height: 380px; width: 500px;" styleClass="tabMensagens">
            
            
            
            
            
            <table width="100%" height="100%" id="pagina1" cellpadding="5" style="display: block;">
            <tr><td valign="top" width="100%" height="85%">
                
                    <h:outputText  value="#{CElabels['menu.operacoesCE.Alterar']}" styleClass="tituloCampos" style="font-weight: bold;"  />
                    <br/>
                    Realize a altera��o da negocia��o, quando houver corre��o de valores no or�amento. 
					<br/>
					Abaixo os valores atuais da negocia��o, para altera��o.<br/>
                    <br/>
                    
                    <h:panelGrid width="70%" columns="2">
                        <h:outputText value="#{CElabels['entidade.valorAtualEvento']}: " styleClass="tituloCampos" style="font-weight: bold;"/>
                        <h:outputText value="#{OrcamentoDetalhadoControle.negociacaoEvento.valorFinalMonetario}" styleClass="tituloCampos" />
                        <h:outputText value="#{CElabels['entidade.valorInicialEvento']}: " styleClass="tituloCampos" style="font-weight: bold;"/>
                        <h:outputText value="#{OrcamentoDetalhadoControle.alteraNegociacao.valorInicialMonetario}" styleClass="tituloCampos" />
                        
                        <h:outputText value="#{CElabels['entidade.valorPago']}: " 
                                      rendered="#{OrcamentoDetalhadoControle.alteraNegociacao.valorPago > OrcamentoDetalhadoControle.alteraNegociacao.valorInicial}"
                                      styleClass="tituloCampos" style="font-weight: bold;"/>
                        <h:outputText value="#{OrcamentoDetalhadoControle.alteraNegociacao.valorPagoMonetario}"
                                      rendered="#{OrcamentoDetalhadoControle.alteraNegociacao.valorPago > OrcamentoDetalhadoControle.alteraNegociacao.valorInicial}"
                                      styleClass="tituloCampos" />
                        
                         <h:outputText value="#{CElabels['entidade.diferenca']}: " styleClass="tituloCampos" style="font-weight: bold;"/>
                        <h:outputText value="#{OrcamentoDetalhadoControle.alteraNegociacao.diferencaMonetario}" styleClass="tituloCampos" />

                         <h:outputText value="#{OrcamentoDetalhadoControle.alteraNegociacao.sobraCreditoMonetario}"
                                       rendered="#{OrcamentoDetalhadoControle.alteraNegociacao.usarCredito}"
                                       styleClass="tituloCampos" />

                        </h:panelGrid>
                    
                
                </td>
                </tr>
               <tr>
                <td valign="top" width="100%" height="15%">
				<h:panelGrid  columns="2"  width="100%" columnClasses="colunaLeft,colunaRight">
               				
                            <h:column>
                            <h:panelGroup>
                             <!-- botao cancelar  -->
                             
         					<a4j:commandButton action="#{OrcamentoDetalhadoControle.cancelarAlteracao}" 
         						reRender="panelAlterarValores"
         						image="../../../imagens/botoesCE/cancelar.png">
         					</a4j:commandButton>               
                          
                          	</h:panelGroup>
                          	</h:column>
                          	<!-- botao proximo  -->
                          	<h:column>
                          	<h:panelGroup>
                          	
                                    <a4j:commandButton onclick="navegarProximo('pagina1','pagina2');if(true){return false;};"
                            		 image="../../../imagens/botoesCE/proximo.png"
                                         rendered="#{!OrcamentoDetalhadoControle.alteraNegociacao.usarCredito}"/>

                                    <a4j:commandButton onclick="navegarProximo('pagina1','credito2');if(true){return false;};"
                            		 image="../../../imagens/botoesCE/proximo.png"
                                         rendered="#{OrcamentoDetalhadoControle.alteraNegociacao.usarCredito}"/>

                            </h:panelGroup>
                        </h:column>
        		
        		</h:panelGrid>
        		</td></tr>
            </table>
        
        
        
   <!-- INICIO PAGINA 2  -->

           <div id="pagina2"  style="vertical-align: top; width: 490px; height: 370px; display: none;">
        <table  cellpadding="5" style="align: top; width: 100%; height: 100%;">

        <tr>
        <td valign="top" style="width: 100%; height: 15%;">
       			<img src="${contexto}/images/arrow2.gif" width="16" height="16" style="vertical-align:middle;margin-right:6px;">
						${CElabels['operacoes.parcelasRelacionadas']}:
						<div class="sep" style="margin:4px 0 5px 0;"><img src="${contexto}/images/shim.gif"></div>
				<div style="width: 100%; height: 270px;  overflow-x:hidden; overflow-y:auto; ">
				<table style="width: 100%">
					<tr>
						<td>
						<rich:dataTable var="movParcelaVO" value="#{OrcamentoDetalhadoControle.alteraNegociacao.parcelas}" width="100%">

										<rich:column>
										<f:facet name="header">
											<h:outputText value="#{CElabels['entidade.descricao']}" />
												</f:facet>
												<center>
												<h:outputText
													value="#{movParcelaVO.descricao}" />
													</center>
											</rich:column>


											<rich:column>
											<f:facet name="header">
											<h:outputText value="#{CElabels['entidade.dataRegistro']}" />
												</f:facet>
												<center>
												<h:outputText
													value="#{movParcelaVO.dataRegistro_Apresentar}" />
													</center>
											</rich:column>

											<rich:column>
											<f:facet name="header">
											<h:outputText value="#{CElabels['entidade.vencimento']}" />
												</f:facet>
												<center>
												<h:outputText
													value="#{movParcelaVO.dataVencimento_Apresentar}" />
													</center>
											</rich:column>

											<rich:column>
												<f:facet name="header">
											<h:outputText value="#{CElabels['entidade.valor']}" />
												</f:facet>
												<center>
												<h:outputText
													value="#{movParcelaVO.valorParcelaNumerico}">

												</h:outputText>
												</center>
											</rich:column>
											<rich:column>
												<f:facet name="header">
											<h:outputText value="#{CElabels['entidade.situacao']}" />
												</f:facet>
												<center>
												<h:outputText
													value="#{movParcelaVO.situacao_Apresentar}">
												</h:outputText>
												</center>
											</rich:column>
										</rich:dataTable>
							</td>
					</tr>
				</table>
				</div>



               </td>
               </tr>
               <tr>
               <td valign="top" width="100%" height="85%">
               <h:panelGrid  columns="3"  width="100%" columnClasses="colunaLeft,colunaCenter,colunaRight">
               				<h:column>
                            <!-- botao voltar  -->
                            <h:panelGroup>

                            <input onclick="navegarProximo('pagina2','pagina1');if(true){return false;};"
                            		type="image" src="../../../imagens/botoesCE/anterior.png"/>
                            </h:panelGroup>
                            </h:column>
                            <h:column>
                            <h:panelGroup>
                             <!-- botao cancelar  -->

         					<a4j:commandButton action="#{OrcamentoDetalhadoControle.cancelarAlteracao}"
         						reRender="panelAlterarValores"
         						image="../../../imagens/botoesCE/cancelar.png"/>

                          	</h:panelGroup>
                          	</h:column>
                          	<!-- botao proximo  -->
                          	  <h:column>
                          	<h:panelGroup>

                            <input onclick="navegarProximo('pagina2','pagina3');if(true){return false;};"
                            		type="image" src="../../../imagens/botoesCE/proximo.png"/>
                            </h:panelGroup>
                        </h:column>

        		</h:panelGrid>
        		</td></tr>
               </table>
               </div>

        <!-- INICIO PAGINA 2 COM CREDITO  -->

        <div id="credito2"  style="vertical-align: top; width: 490px; height: 370px; display: none;">
        <table  cellpadding="5" style="align: top; width: 100%; height: 100%;">
        
        <tr>
        <td valign="top" style="width: 100%; height: 15%;">
       			<img src="${contexto}/images/arrow2.gif" width="16" height="16" style="vertical-align:middle;margin-right:6px;">
						Usar cr�dito:
						<div class="sep" style="margin:4px 0 5px 0;"><img src="${contexto}/images/shim.gif"></div>
				<div style="width: 100%; height: 270px;  overflow-x:hidden; overflow-y:auto; ">
                                    <table style="width: 100%">
					<tr>
                                            <td>
                                                <h:panelGrid width="100%" columns="2">
                                                    <h:outputText value="#{CElabels['entidade.credito']}: " styleClass="tituloCampos" style="font-weight: bold;"/>
                                                    <h:outputText value="#{OrcamentoDetalhadoControle.alteraNegociacao.valorCreditoMonetario}" styleClass="tituloCampos" />

                                                    <h:outputText rendered="#{OrcamentoDetalhadoControle.alteraNegociacao.usarCredito
                                                                   && OrcamentoDetalhadoControle.alteraNegociacao.sobraCredito >= 0}"
                                                                   value="Cr�dito restante: " styleClass="tituloCampos" style="font-weight: bold;"/>

                                                     <h:outputText rendered="#{OrcamentoDetalhadoControle.alteraNegociacao.usarCredito
                                                                   && OrcamentoDetalhadoControle.alteraNegociacao.sobraCredito < 0}"
                                                                   value="Diferen�a restante: " styleClass="tituloCampos" style="font-weight: bold;"/>


                                                     <h:outputText value="#{OrcamentoDetalhadoControle.alteraNegociacao.sobraCreditoMonetario}"
                                                                   rendered="#{OrcamentoDetalhadoControle.alteraNegociacao.usarCredito}"
                                                                   styleClass="tituloCampos" />
                                                </h:panelGrid>

                                                <h:outputText rendered="#{OrcamentoDetalhadoControle.alteraNegociacao.usarCredito
                                                                   && OrcamentoDetalhadoControle.alteraNegociacao.sobraCredito > 0}"
                                                                   value="Deseja devolver o cr�dito restante ? " styleClass="tituloCampos" style="font-weight: bold;"/>

                                                <h:outputText rendered="#{OrcamentoDetalhadoControle.alteraNegociacao.usarCredito
                                                                   && OrcamentoDetalhadoControle.alteraNegociacao.sobraCredito == 0}"
                                                                   value="O cr�dito foi usado totalmente." styleClass="tituloCampos" style="font-weight: bold;"/>

                                                <h:outputText rendered="#{OrcamentoDetalhadoControle.alteraNegociacao.usarCredito
                                                                   && OrcamentoDetalhadoControle.alteraNegociacao.sobraCredito < 0}"
                                                                   value="O valor da diferen�a � maior do que o cr�dito. Voc� deve alterar as parcelas existentes ou gerar novas parcelas para o valor restante. " styleClass="tituloCampos" style="font-weight: bold;"/>


                                                <h:selectOneRadio value="#{OrcamentoDetalhadoControle.escolhaDevolucaoCredito}"
                                                                  rendered="#{OrcamentoDetalhadoControle.alteraNegociacao.usarCredito
                                                                   && OrcamentoDetalhadoControle.alteraNegociacao.sobraCredito > 0}"
                                                                   styleClass="tituloCampos">
                                                    <f:selectItem itemLabel="Sim" itemValue="1" />
                                                    <f:selectItem itemLabel="N�o, o cr�dito ainda pode ser usado" itemValue="2"/>
                                                </h:selectOneRadio>

                                                
                                            </td>
                                        </tr>
                                    </table>
				</div>
               </td>
               </tr>
               <tr>
               <td valign="top" width="100%" height="85%">
               <h:panelGrid  columns="3"  width="100%" columnClasses="colunaLeft,colunaCenter,colunaRight">
               				<h:column>
                            <!-- botao voltar  --> 
                            <h:panelGroup>
                            
                            <input onclick="navegarProximo('credito2','pagina1');if(true){return false;};"
                            		type="image" src="../../../imagens/botoesCE/anterior.png"/>
                            </h:panelGroup>
                            </h:column>
                            <h:column>
                            <h:panelGroup>
                             <!-- botao cancelar  -->
                             
         					<a4j:commandButton action="#{OrcamentoDetalhadoControle.cancelarAlteracao}" 
         						reRender="panelAlterarValores"
         						image="../../../imagens/botoesCE/cancelar.png"/>               
                          
                          	</h:panelGroup>
                          	</h:column>
                          	<!-- botao proximo  -->
                          	  <h:column>
                          	<h:panelGroup>

                                    <h:commandButton rendered="#{OrcamentoDetalhadoControle.alteraNegociacao.sobraCredito < 0}"
                                                     image="../../../imagens/botoesCE/proximo.png"
                                                     onclick="navegarProximo('credito2','pagina3');if(true){return false;};">

                                    </h:commandButton>

                                    <h:commandButton rendered="#{OrcamentoDetalhadoControle.alteraNegociacao.usarCredito
                                                                   && OrcamentoDetalhadoControle.alteraNegociacao.sobraCredito >= 0}"
                                                     image="../../../imagens/botoesCE/confirmar.png"
                                                     action="#{OrcamentoDetalhadoControle.confirmarAlteracoes}">
                                        <f:attribute name="codigoEventoInteresse"
                                                     value="#{OrcamentoDetalhadoControle.negociacaoEvento.codigoEventoInteresse}"/>
                                    </h:commandButton>
                            </h:panelGroup>
                        </h:column>
        		
        		</h:panelGrid>
        		</td></tr>
               </table>
               </div>
               
               
         <!-- INICIO PAGINA 3  -->
         <div id="pagina3"  style="vertical-align: top; width: 490px; height: 370px; display: none;">
        <table>
        <tr><td>
        
        <h:panelGrid columns="2" style="vertical-align:middle; height:250px; width: 490px;">
        <h:panelGroup style="width:40%">
         <a4j:commandButton id="botaoParcelas" reRender="editarParcelas,sobra,sobraFixa, sobraHiden, nrParcelas, nrParcelasPagas" 
         					action="#{OrcamentoDetalhadoControle.montarParcelas}" style="visibility: hidden;"/>
        
         
         				
        <h:selectOneRadio  id="tipoAlterar" layout="pageDirection" styleClass="tituloCampos"
                					value="#{OrcamentoDetalhadoControle.alteraNegociacao.operacao}"
                					onclick="alternarOperacao();">
                					<f:selectItem itemLabel="#{CElabels['operacoes.alterarNegociacao.alterar']}" itemValue="2" itemDisabled="#{OrcamentoDetalhadoControle.todasParcelasPagas}"/>
                					<h:outputText value="Todas as parcelas atuais est�o pagas, voc� dever� gerar novas parcelas." 
         			   							  styleClass="mensagemObrigatorio"
         			   							  rendered="#{OrcamentoDetalhadoControle.todasParcelasPagas}"/><br/>
									<f:selectItem itemLabel="#{CElabels['operacoes.alterarNegociacao.gerar']}" itemValue="1"/>
									
									</h:selectOneRadio>
						
                        <h:outputText value="#{CElabels['operacoes.alterarNegociacao.qtdInicialParcelas']}: #{OrcamentoDetalhadoControle.alteraNegociacao.nrParcelas}" styleClass="tituloCampos" style="font-weight: bold;"/>
                        
        </h:panelGroup>
        <h:panelGroup style="width:40%">
        <rich:spacer height="80px"></rich:spacer>
        <table id="gerarNovas" style="width: 250px; ${OrcamentoDetalhadoControle.displayTabelaNovasParcelas}">
                        <tr><td>
                        <h:outputText value="#{CElabels['operacoes.alterarNegociacao.qtdAtualParcelas']}: " styleClass="tituloCampos" style="font-weight: bold;"/>
                        
                        <h:inputText id="entradaQtdParcelas" value="#{OrcamentoDetalhadoControle.alteraNegociacao.nrParcelasNovo}" maxlength="3" size="3" onkeypress="return mascara(this, '999', event);"
                        				onblur="alterarData('formAlterarValores:entradaQtdParcelas', 'saidaQtdParcelas'); limparMensagem('entradaQtdParcelas');"/>
                        <div id="divObg-entradaQtdParcelas" class="mensagemObrigatorio"></div>
                        
                        </td></tr>
                        <tr><td>
                        <h:outputText value="#{CElabels['operacoes.alterarNegociacao.dataPrimeira']}: " styleClass="tituloCampos" style="font-weight: bold;"/>
                        <br/>
                       
                        <rich:calendar id="entradaData" styleClass="form" value="#{OrcamentoDetalhadoControle.alteraNegociacao.dataPrimeiraParcela}"
									   datePattern="dd/MM/yyyy" inputSize="10" inputClass="form" 
									   oninputblur="blurinput(this); alterarData('formAlterarValores:entradaDataInputDate', 'saidaData'); limparMensagem('entradaData');" oninputfocus="focusinput(this);"
									   onchanged="alterarData('formAlterarValores:entradaDataInputDate', 'saidaData'); limparMensagem('entradaData');"
									   oninputchange="if(!validar_Data(this.id)) {this.value = ''};" oninputkeypress="return mascara(this, '99/99/9999', event);"
									   enableManualInput="true" zindex="2" showWeeksBar="false" />
						<div id="divObg-entradaData" class="mensagemObrigatorio"></div>
                       </td></tr>
               </table> 
        
        </h:panelGroup>
        </h:panelGrid>
        <rich:spacer height="60px"></rich:spacer>
              
              <table>  
         	<tr>
         	<td>
			<h:panelGrid  columns="3" style="width: 490px;" columnClasses="colunaLeft,colunaCenter,colunaRight">
               				<h:column>
                            <!-- botao voltar  --> 
                            <h:panelGroup>
                            
                            <input onclick="navegarProximo('pagina3','pagina2');if(true){return false;};" 
                            		type="image" src="../../../imagens/botoesCE/anterior.png"/>
                            </h:panelGroup>
                            </h:column>
                            <h:column>
                            <h:panelGroup>
                             <!-- botao cancelar  -->
                             
         					<a4j:commandButton action="#{OrcamentoDetalhadoControle.cancelarAlteracao}" 
         						reRender="panelAlterarValores"
         						image="../../../imagens/botoesCE/cancelar.png"/>               
                          
                          	</h:panelGroup>
                          	</h:column>
                          	<!-- botao proximo  -->
                          	  <h:column>
                          	<h:panelGroup>
                          	
                            <input onclick="if(!validarNovasParcelas()){return false;}; navegarProximo('pagina3','pagina4');chamarHiddenBotao();if(true){return false;};" 
                            		type="image" src="../../../imagens/botoesCE/proximo.png"/>
                            </h:panelGroup>
                        </h:column>
        		
        		</h:panelGrid>


          </td></tr>     
       </table>
       </td></tr>
        </table>
       </div>
       
       
       <!-- INICIO PAGINA 4  -->
        <div id="pagina4"  style="vertical-align: top; width: 490px; height: 370px; display: none;">
        <table  width="100%" height="100%" cellpadding="5">
        <tr><td height="100%" width="100%">
        <h:panelGroup id="valoresDaAlteracao">
        <table width="100%">
        <tr>
        <!--  COLUNA 1 -->
        <td  width="50%">
        <h:outputText value="#{CElabels['menu.consulta.relatorios.parcelasPagas']}: " styleClass="tituloCampos" style="font-weight: bold;"/>
                        <h:outputText value="#{OrcamentoDetalhadoControle.alteraNegociacao.valorPagoMonetario}" styleClass="tituloCampos" /> <br/>
                        
                        <h:outputText value="#{CElabels['menu.consulta.relatorios.parcelasAberto']}: " styleClass="tituloCampos" style="font-weight: bold;"/>
                        <h:outputText value="#{OrcamentoDetalhadoControle.alteraNegociacao.valorAbertoMonetario}" styleClass="tituloCampos" /><br/>
                        <div id="divObg-sobra" class="mensagemObrigatorio"></div>
                        
        </td>
        <!--  COLUNA 2 -->
        <td width="50%">
        
                        
                        <h:outputText value="#{CElabels['entidade.diferenca']}: " styleClass="tituloCampos" style="font-weight: bold;"/>
                        <h:outputText value="#{OrcamentoDetalhadoControle.alteraNegociacao.diferencaMonetario}" styleClass="tituloCampos" /><br/>
                       
                       	<h:outputText value="#{CElabels['entidade.diferencaValores']}: " styleClass="tituloCampos" style="font-weight: bold;"/>
                        <div id="divsobra">
                       	<h:outputText styleClass="#{OrcamentoDetalhadoControle.alteraNegociacao.style}" id="sobra" value="#{OrcamentoDetalhadoControle.alteraNegociacao.sobraMonetario}"/>
                        </div>
       					
       					<h:inputHidden id="sobraHiden" value="#{OrcamentoDetalhadoControle.alteraNegociacao.sobra}" />
       					<h:inputHidden id="nrParcelasPagas" value="#{OrcamentoDetalhadoControle.alteraNegociacao.nrParcelasPagas}" />
       					<h:inputHidden id="nrParcelas" value="#{OrcamentoDetalhadoControle.alteraNegociacao.nrParcelas}" />
       					<h:inputHidden id="sobraFixa" value="#{OrcamentoDetalhadoControle.alteraNegociacao.sobra}" />
       					<h:outputText styleClass="mensagemDetalhadaGrande" value="#{OrcamentoDetalhadoControle.alteraNegociacao.mensagem}" />
        </td>
        </tr>
        
        </table>
        </h:panelGroup>
        
        <table width="100%" height="100%">
        <tr>
       <td width="100%" height="75%">
       			<div style="width: 100%; height: 200px;  overflow-x:hidden; overflow-y:auto; ">
        		<table id="tableParcelas" style="width: 100%;"><tr><td>
       			
       			
       			<rich:dataTable id="editarParcelas" var="movParcelaVO" value="#{OrcamentoDetalhadoControle.alteraNegociacao.parcelasNovas}" width="100%">
										
										<h:column>
										<f:facet name="header">
											<h:outputText value="#{CElabels['entidade.descricao']}" />
												</f:facet>
												<center>
												<h:outputText
													value="#{movParcelaVO.descricao}" />
													</center>
											</h:column>


											<h:column>
											<f:facet name="header">
											<h:outputText value="#{CElabels['entidade.dataRegistro']}" />
												</f:facet>
												<center>
												<h:outputText
													value="#{movParcelaVO.dataRegistro_Apresentar}" />
													</center>
											</h:column>

											<h:column>
											<f:facet name="header">
											<h:outputText value="#{CElabels['entidade.vencimento']}" />
												</f:facet>
												<center>
												
												<h:outputText rendered="#{!movParcelaVO.parcelaEscolhida}"
													value="#{movParcelaVO.dataVencimento_Apresentar}" />
												
												
												<rich:calendar id="dataParcelaGerar"
													rendered="#{movParcelaVO.parcelaEscolhida}"
													styleClass="form" value="#{movParcelaVO.dataVencimento}"
									   				datePattern="dd/MM/yyyy" 
									   				inputSize="10" inputClass="form" 
									   				oninputblur="blurinput(this);" 
									   				oninputfocus="focusinput(this);"
									   				onchanged="alterarData('formAlterarValores:entradaDataInputDate', 'saidaData'); limparMensagem('entradaData');"
									   				oninputchange="if(!validar_Data(this.id)) {this.value = ''};" 
									   				oninputkeypress="return mascara(this, '99/99/9999', event);"
									   				enableManualInput="true" zindex="2" showWeeksBar="false" />
						
												</center>
													
											</h:column>
											
											
											<h:column>
												<f:facet name="header">
											<h:outputText value="#{CElabels['entidade.valor']}" />
												</f:facet>
												<center>
												<h:inputText onkeypress="return(currencyFormat(this,'.',',',event));" 
															 id="valorparcela"
															 rendered="#{movParcelaVO.parcelaEscolhida}"
												 			 maxlength="8" size="8" 
												 			 value="#{movParcelaVO.valorParcelaFormatado}">
													<a4j:support event="onblur" 
																 status="false"
																 action="#{OrcamentoDetalhadoControle.calcularSobra}" 
																 reRender="valoresDaAlteracao"
																 oncomplete="focar(this.id)"></a4j:support>
												</h:inputText>
												<h:outputText rendered="#{!movParcelaVO.parcelaEscolhida}"
													value="#{OrcamentoDetalhadoControle.alteraNegociacao.valorUnicoPagoMonetario}"></h:outputText>
												</center>
											</h:column>
											
											<h:column>
												<f:facet name="header">
											<h:outputText value="#{CElabels['entidade.situacao']}" />
												</f:facet>
												<center>
												<h:outputText
													value="#{movParcelaVO.situacao_Apresentar}">
												</h:outputText>
												</center>
											</h:column>
								</rich:dataTable>
								</td></tr></table>
								</div>
								
       		</td>	
       		</tr>
       		<tr><td width="100%" height="25%">
             
			<h:panelGrid  columns="3"  width="100%" columnClasses="colunaLeft,colunaCenter,colunaRight">
               				<h:column>
                            <!-- botao voltar  --> 
                            <h:panelGroup>
                            
                            <input onclick="navegarProximo('pagina4','pagina3');if(true){return false;};" 
                            		type="image" src="../../../imagens/botoesCE/anterior.png"/>
                            </h:panelGroup>
                            </h:column>
                            <h:column>
                            <h:panelGroup>
                             <!-- botao cancelar  -->
                             
         					<a4j:commandButton action="#{OrcamentoDetalhadoControle.cancelarAlteracao}" 
         						reRender="panelAlterarValores"
         						image="../../../imagens/botoesCE/cancelar.png"/>               
                          
                          	</h:panelGroup>
                          	</h:column>
                          	<!-- botao proximo  -->
                          	<h:column>
	                          	<h:panelGroup>                          	
	                            <a4j:commandButton
	                            	image="../../../imagens/botoesCE/confirmar.png"
									reRender="mdlMensagemGenerica"
	                            	action="#{OrcamentoDetalhadoControle.abrirModalConfirmarAlteracoes}"
									oncomplete="#{OrcamentoDetalhadoControle.msgAlert}">
									<f:attribute name="codigoEventoInteresse" 
									value="#{OrcamentoDetalhadoControle.negociacaoEvento.codigoEventoInteresse}"/>				
								</a4j:commandButton>
	                            </h:panelGroup>
                        	</h:column>
        		
        		</h:panelGrid>
        
        </td></tr>
       </table> 
       </td></tr>
       </table> 
       </div>
       </h:panelGrid>     
               
        
        </div>			
	        </h:form>
	        <script>

			function calcularSobra(){
				var sobra = document.getElementById('formAlterarValores:sobraHiden');
				var sobraFixa = document.getElementById('formAlterarValores:sobraFixa');
				var pagas = document.getElementById('formAlterarValores:nrParcelasPagas');
				var parcelas = document.getElementById('formAlterarValores:nrParcelas');
				if(pagas.value == '')
					pagas.value = 0;
				sobra.value = sobraFixa.value;
				for(var i = pagas.value; i < parcelas.value; i++){
					
					var id = 'formAlterarValores:editarParcelas:'+i+':valorparcela';
					
					var campo = document.getElementById(id).value;
					if(campo == '')
						campo = '0';
					campo = campo.replace('.', '');
					campo = campo.replace(',', '.');
					var valor = parseFloat(campo);
					sobra.value = sobra.value - valor;
				}
				var divsobra = document.getElementById('divsobra');
				divsobra.innerHTML = "";
				var exibeSobra = sobra.value;
				exibeSobra = exibeSobra.replace('-','');
				exibeSobra = truncaNumero(parseFloat(exibeSobra));
				if(sobra.value <0)
					divsobra.innerHTML = '<h:outputText styleClass="sobraNegativa" value=" R$ '+exibeSobra+'"/>';
					else
						divsobra.innerHTML = '<h:outputText styleClass="sobraPositiva" value=" R$ '+exibeSobra+'"/>';
										
			}	
	        function alterarData(entrada, saida){
	        	var saidaData = document.getElementById(saida);
	        	var entradaData = document.getElementById(entrada);
	        	
	        	saidaData.innerHTML = '<h:outputText value="'+entradaData.value+'"/>';
	        }

	        function chamarHiddenBotao(){
	        	var botao = document.getElementById('formAlterarValores:botaoParcelas');
	        	botao.click();
	        	
	        }


	        function focar(idCampo){
		        idCampo = idCampo.replace('formAlterarValores:editarParcelas:','');
	        	nrId = parseInt(idCampo.replace(':valorparcela',''))+1;
	        	var campo = 'formAlterarValores:editarParcelas:'+nrId+':dataParcelaGerarInputDate';
	        	document.getElementById(campo).focus();
	        }
		       

	        function alternarOperacao() {
	            
	        	
	        	var itens = document.getElementsByName('formAlterarValores:tipoAlterar');
	        	for (var i = 0; i < itens.length; i++) {
	        		if (itens[i].checked) {
	        			
	        			var pesquisa = document.getElementById('gerarNovas');
	        			switch (itens[i].value) {
	        			case '1':
	        				pesquisa.style.display='block';
	        				
	        				
	        				break;
	        			case '2':

	        				pesquisa.style.display='none';
	        				
	        				break;
	        			}
	        		}
	        	}
	        }       
	        function validarNovasParcelas(){
	        	
	        	
	        	var itens = document.getElementsByName('formAlterarValores:tipoAlterar');
	        	var data = document.getElementById('formAlterarValores:entradaDataInputDate');
	        	var qtd = document.getElementById('formAlterarValores:entradaQtdParcelas');
	        	
	        	
	        	
	        	var validade = true;
	        	for (var i = 0; i < itens.length; i++) {
	        	if (itens[i].checked) {
	        	switch (itens[i].value) {
	        	case '1':
	        		if (data == null || data.value == null || data.value == "") {
	        			
	        			exibirMensagem('<h:outputText value="Informe a data inicial das novas parcelas."/>','entradaData');
	        			validade = false;
	        		}
	        		if (qtd == null || qtd.value == null || qtd.value == "") {
	        			exibirMensagem('<h:outputText value="Informe a nova quantidade de parcelas."/>', 'entradaQtdParcelas');
	        			validade = false;
	        		}
	        		
	        		
	        		break;
	        	}
	        	
	        	return validade;
	        	
	        }
	        }
	        }
			function navegarProximo(atual, proxima){
	        		var proximapag = document.getElementById(proxima);
	        		var atualpag = document.getElementById(atual);
	        		proximapag.style.display = "block";
	        		atualpag.style.display = "none";
	        	}
	        	
	        	function validarSobra(){
	        		var sobra = document.getElementById('formAlterarValores:sobraHiden').value;
	        		var validade = true; 
	        		if (sobra != '0' && sobra != '0.0') {
	        			exibirMensagem('<h:outputText value="O valor das parcelas deve ser igual ao valor do evento."/>', 'sobra');
	        			validade = false;
	        		}
	        		return validade;
	        		
	        	}
	        	function truncaNumero(rnum) {
						return Math.round(rnum*Math.pow(10,2))/Math.pow(10,2);
				}
	        	
		</script>
	    </rich:modalPanel>
		
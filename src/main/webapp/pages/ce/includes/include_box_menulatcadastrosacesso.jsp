<%@page contentType="text/html"%>
<%@page pageEncoding="ISO-8859-1"%>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<f:loadBundle var="msg_menu" basename="propriedades.Menu"/>
<!-- inicio box -->

<h:panelGroup layout="block" styleClass="menuLateral">

    <h:panelGroup layout="block" styleClass="grupoMenuLateral"> 
        <h:panelGroup layout="block" styleClass="grupoMenuTitulo">
            <i class="fa-icon-list"></i> Acesso ao Sistema
        </h:panelGroup>
            
        <h:panelGroup layout="block" styleClass="grupoMenuItem">
            <a4j:commandLink  value="#{CElabels['menu.cadastros.acessoSistema.empresa']}"
                              styleClass="titulo3 linkFuncionalidade"
                              action="#{FuncionalidadeControle.abrirComCasoNavegacao}"
                              actionListener="#{FuncionalidadeControle.prepararFuncionalidade}"
                              oncomplete="#{FuncionalidadeControle.abrirPopUp}">
                <f:attribute name="funcionalidade" value="EMPRESA_CE" />
            </a4j:commandLink>
            <h:outputLink styleClass="linkWiki"
                          value="#{SuperControle.urlBaseConhecimento}configurando-os-dados-da-empresa-para-emissao-de-notas-fiscais/"
                          title="Clique e saiba mais: Empresa"
                          target="_blank">
                <i class="fa-icon-question-sign" style="font-size: 18px"></i>
            </h:outputLink>
        </h:panelGroup>
        <h:panelGroup layout="block" styleClass="grupoMenuItem">
            <a4j:commandLink  value="#{CElabels['menu.cadastros.acessoSistema.perfisAcesso']}"
                              styleClass="titulo3 linkFuncionalidade"
                              action="#{FuncionalidadeControle.abrirComCasoNavegacao}"
                              actionListener="#{FuncionalidadeControle.prepararFuncionalidade}"
                              oncomplete="#{FuncionalidadeControle.abrirPopUp}">
                <f:attribute name="funcionalidade" value="PERFIL_ACESSO_CE" />
            </a4j:commandLink>
            <h:outputLink styleClass="linkWiki"
                          value="#{SuperControle.urlBaseConhecimento}quais-sao-as-permissoes-do-perfil-de-acesso-do-modulo-adm/"
                          title="Clique e saiba mais: Perfis de Acesso"
                          target="_blank">
                <i class="fa-icon-question-sign" style="font-size: 18px"></i>
            </h:outputLink>
        </h:panelGroup> 
        <h:panelGroup layout="block" styleClass="grupoMenuItem">
            <a4j:commandLink  value="#{CElabels['menu.cadastros.acessoSistema.usuario']}"
                              styleClass="titulo3 linkFuncionalidade"
                              action="#{FuncionalidadeControle.abrirComCasoNavegacao}"
                              actionListener="#{FuncionalidadeControle.prepararFuncionalidade}"
                              oncomplete="#{FuncionalidadeControle.abrirPopUp}">
                <f:attribute name="funcionalidade" value="USUARIO_CE" />
            </a4j:commandLink>
            <h:outputLink styleClass="linkWiki"
                          value="#{SuperControle.urlBaseConhecimento}como-cadastro-um-novo-usuario-no-sistema/"
                          title="Clique e saiba mais: Usuário"
                          target="_blank">
                <i class="fa-icon-question-sign" style="font-size: 18px"></i>
            </h:outputLink>
        </h:panelGroup>       
        <h:panelGroup layout="block" styleClass="grupoMenuItem">
            <a4j:commandLink  value="#{CElabels['menu.cadastros.acessoSistema.controleLog']}"
                              styleClass="titulo3 linkFuncionalidade"
                              action="#{FuncionalidadeControle.abrirComCasoNavegacao}"
                              actionListener="#{FuncionalidadeControle.prepararFuncionalidade}"
                              oncomplete="#{FuncionalidadeControle.abrirPopUp}">
                <f:attribute name="funcionalidade" value="CONTROLE_LOG_CE" />
            </a4j:commandLink>
            <h:outputLink styleClass="linkWiki"
                          value="#{SuperControle.urlBaseConhecimento}como-utilizar-o-controle-de-log/"
                          title="Clique e saiba mais: Log do Sistema"
                          target="_blank">
                <i class="fa-icon-question-sign" style="font-size: 18px"></i>
            </h:outputLink>
        </h:panelGroup>     

    </h:panelGroup>
</h:panelGroup>
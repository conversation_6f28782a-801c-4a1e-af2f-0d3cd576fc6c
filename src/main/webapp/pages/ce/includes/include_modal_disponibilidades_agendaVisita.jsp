<rich:modalPanel id="panelEventos" autosized="true" shadowOpacity="true" width="450" height="250">
    	
	<f:facet name="header">
		<h:panelGroup>
			<h:outputText
				value="Disponibilidades"></h:outputText>
		</h:panelGroup>
	</f:facet>
	<f:facet name="controls">
		<h:panelGroup>
			<h:graphicImage value="/imagens/close.png" style="cursor:pointer"
				id="hidelinkDisponibilidades" />
			<rich:componentControl for="panelEventos"
				attachTo="hidelinkDisponibilidades" operation="hide"
				event="onclick" />
		</h:panelGroup>
	</f:facet>
        
        <h:form id="formDisponibilidades">
               
        <%@include file="/pages/ce/includes/topoReduzido.jsp"%>
                    
        <!-- Caixa com bot�es -->
            <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
			<h:panelGroup>
				<h:outputText value="#{CElabels['entidade.ambiente']}:" />&nbsp;
				<h:outputText value='#{ConsultaEventosControle.resultadoConsulta[ConsultaEventosControle.indexAmbiente]["0"]["1"]}' />
			</h:panelGroup>
			
			<h:panelGroup>
				<h:outputText value="#{CElabels['entidade.data']}:" />&nbsp;
				<h:outputText value="#{AgendaVisitaControle.agendaVisita.dataVisitaString}" />
			</h:panelGroup>
			
			<h:panelGroup>
				<h:outputText value="#{CElabels['entidade.horario']}:" />&nbsp;
				<h:outputText id="horarioSelecionado"/>
			</h:panelGroup>
			
	        <h:panelGroup>
	        <rich:dataTable width="100%" headerClass="consulta" rowClasses="linhaImpar, linhaPar"
					columnClasses="colunaAlinhamento" value="#{ConsultaEventosControle.disponibilidadeTO.reservas}" var="reserva"
					reRender="panelBotoes" style="cursor: pointer;"
					onRowMouseOver="backgroundAntigo = this.style.background; this.style.background = '#D7D7D7';"
					onRowMouseOut="this.style.background = backgroundAntigo;"
					onRowClick="if('#{reserva.situacao.descricao}' == ''){document.getElementById('formDisponibilidades:horarioSelecionado').innerHTML = '#{reserva.horarioInicialFormatado} - #{reserva.horarioFinalExibicaoFormatado}';
						preencherHiddenChamarBotao('formDisponibilidades:mudarBotoes','formDisponibilidades:codigoReserva','#{reserva.horarioInicialFormatado}');}return false;">
	        	
	        	<rich:column>
					<f:facet name="header">
						<h:outputText value="#{CElabels['entidade.horario']}"/>
					</f:facet>
					<h:outputText value="#{reserva.horarioInicialFormatado} - #{reserva.horarioFinalExibicaoFormatado}" />
				</rich:column>
				<rich:column>
					<f:facet name="header">
						<h:outputText value="#{CElabels['entidade.reserva.responsavel']}"/>
					</f:facet>
					<h:outputText value="#{reserva.responsavel}" />
				</rich:column>
				
				<rich:column>
					<f:facet name="header">
						<h:outputText value="#{CElabels['entidade.nome']}"/>
					</f:facet>
					<h:outputText value="#{reserva.nome}" />
				</rich:column>
				
				<rich:column>
					<f:facet name="header">
						<h:outputText value="#{CElabels['entidade.tipo']}"/>
					</f:facet>
					<h:outputText value="#{reserva.tipo.descricao}" />
				</rich:column>
				
						<rich:column>
					<f:facet name="header">
						<h:outputText value="#{CElabels['entidade.convidados']}"/>
					</f:facet>
					<h:outputText value="#{reserva.nrConvidados}" />
				</rich:column>
				
				<rich:column>
					<f:facet name="header">
						<h:outputText value="#{CElabels['entidade.situacao']}"/>
					</f:facet>
					<h:outputText value="#{reserva.situacao.descricao}" />
				</rich:column>
				
	        </rich:dataTable>
	        </h:panelGroup>
	        <a4j:commandButton id="mudarBotoes" style="display: none;" reRender="panelDadosAgendaVisita"
							action="#{AgendaVisitaControle.escolheHorarioMarcado}" oncomplete="Richfaces.hideModalPanel('panelEventos')"/>
			<h:inputHidden id="codigoReserva" value="#{AgendaVisitaControle.horaMarcada}" />
		</h:panelGrid>
	</h:form>
    </rich:modalPanel>
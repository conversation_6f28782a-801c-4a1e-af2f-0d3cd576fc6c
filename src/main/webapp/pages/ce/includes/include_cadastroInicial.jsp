<%@include file="../includes/include_imports.jsp" %>
<%@include file="../../../includes/include_modal_mensagem_generica.jsp"%>
	<h:panelGrid columns="2" id="panelMensagens" width="100%">
	<h:panelGroup>	
			<h:panelGrid   width="100%" columns="2" styleClass="tabMensagens">
			<h:panelGroup>
			<h:commandButton  image="/imagens/bt_sucesso.png" rendered="#{CadastroInicialControle.pessoa.codigo gt 0}"/>
			<h:outputText styleClass="mensagem" value="#{CadastroInicialControle.mensagem}" />&nbsp;&nbsp;&nbsp;
			<h:outputText styleClass="mensagemDetalhada" value="#{CadastroInicialControle.mensagemDetalhada}" />
			</h:panelGroup>
			</h:panelGrid>
		</h:panelGroup>	
	</h:panelGrid>
	<table width="100%">
	<tr>
	<td width="100%">
	<table width="100%" border="0" align="center" cellpadding="0"
		cellspacing="0">
		<tr>
			<td width="10"><img
				src="${contexto}/imagens/agenda_imgs/menu_esq.jpg" width="10"
				height="69"></td>
			<td background="${contexto}/imagens/agenda_imgs/menu_fundo.jpg">
			<table width="100%" border="0" cellspacing="0" cellpadding="0">
				<tr>

				<td align="center">
				<h:commandLink id="salvarOrcado" action="#{CadastroInicialControle.salvarEventoJaNegociado}"
				rendered="#{CadastroInicialControle.orcado}" onclick="if(!validar()){return false;};"
					actionListener="#{CadastroInicialControle.autorizacao}">
				<f:attribute name="funcao" value="106" />
				<h:graphicImage value="/imagens/bt_salvar.jpg" style="border: 0px;"/>
				</h:commandLink></td>
				<td align="center">
				<h:commandLink id="salvar" action="#{CadastroInicialControle.salvar}"
				rendered="#{!CadastroInicialControle.orcado}" onclick="if(!validar()){return false;};"
					actionListener="#{CadastroInicialControle.autorizacao}">
				<!-- Funcionalidade.cadastroinicial -->
				<f:attribute name="funcao" value="106" />
				<h:graphicImage value="/imagens/bt_salvar.jpg" style="border: 0px;"/>
				</h:commandLink></td>
				<td align="center">
				<h:commandLink id="salvarParcial" action="#{CadastroInicialControle.salvarParcial}"
				rendered="#{!CadastroInicialControle.orcado}" onclick="if(!validarCadastroParcial()){return false;};"
					actionListener="#{CadastroInicialControle.autorizacao}">
				<!-- Funcionalidade.cadastroinicial -->
				<f:attribute name="funcao" value="106" />
				<h:graphicImage value="/imagens/bt_salvarparcialmente.jpg" style="border: 0px;"/>
				</h:commandLink></td>
				<td align="center">
				<h:commandLink id="simularOrcamento" action="#{OrcamentoDetalhadoControle.abrirOrcamentoFicticio}"
				rendered="#{!CadastroInicialControle.orcado}"
				actionListener="#{OrcamentoDetalhadoControle.selOrigemDetalhamentoEvento}">
				<h:graphicImage value="/imagens/bt_simularorcamento.jpg" style="border: 0px;"/>
				</h:commandLink></td>
				<td align="center">
				<h:graphicImage value="/imagens/bt_ajuda.jpg" style="border: 0px;" onclick="ativarAjuda(['telefone', 'verificar', 'nomeEvento', 'ambienteInteresse', 'nomeCliente', 'qeclienteEvento']);"/>
				</td></tr>
			</table>
			</td>
			<td width="10"><img
				src="${contexto}/imagens/agenda_imgs/menu_dir.jpg" width="10"
				height="69"></td>
		</tr>
	</table>
	</td>
	</tr>
	<tr><td width="100%">
<h:panelGrid  id="evt"  width="100%">
		<c:if test="${not empty CadastroInicialControle.eventos}">
		<rich:dataTable width="100%" headerClass="consulta" 
					rowClasses="linhaImpar, linhaPar" columnClasses="colunaAlinhamento"
					value="#{CadastroInicialControle.eventos}"
				    var="evento">

					<rich:column width="130px">
						<f:facet name="header">
							<h:outputText value="#{CElabels['entidade.descricao']}" />
						</f:facet>
						<h:outputText value="#{evento.nomeEvento}"/>
					</rich:column>
					<rich:column width="130px">
						<f:facet name="header">
							<h:outputText value="#{CElabels['entidade.data']}" />
						</f:facet>
						<h:outputText value="#{evento.dataEventoFormatada}"/>
					</rich:column>
					<rich:column width="130px">
						<f:facet name="header">
							<h:outputText value="#{CElabels['entidade.situacao']}" />
						</f:facet>
						<h:outputText value="#{evento.situacao}"/>
					</rich:column>
					<rich:column width="130px">
						<f:facet name="header">
							<h:outputText value="#{CElabels['entidade.perfilEvento.detalharEvento']}" />
						</f:facet>
						<a4j:commandLink
									id="detalhamentoButton"
									action="#{CadastroInicialControle.confirmarDetalhamento}"
									actionListener="#{CadastroInicialControle.selEventoConversaListener}"
									oncomplete="#{CadastroInicialControle.msgAlert}">
									<f:attribute name="codigoEvento" value="#{evento.codigo}"/>
		 							<h:graphicImage value="/imagens/bt_detalhes.png" style="border: 0px;" alt="#{CElabels['operacoes.editar.editarDados']}"/>
		 							</a4j:commandLink>
					</rich:column>

				</rich:dataTable>
			</c:if>
	</h:panelGrid>
	<h:panelGrid id="panelDadosBasicos" columns="1" width="100%"
	 columnClasses="colunaCentralizada">
	
		<h:inputHidden value="#{CadastroInicialControle.pessoa.codigo}" id="codigoPessoa" />
		<h:panelGrid rowClasses="linhaImpar, linhaPar"
			columnClasses="classEsquerda, classDireita"
			width="100%" columns="2">
			<h:panelGroup>
				<a onmouseover="if (document.getElementById('hint-verificar').style.visibility != 'hidden') {toolTip('<h:outputText value="#{CElabels['entidade.cadastroInicial.hint.dataInteresse']}"/>' , 300 , 'blue')}"
						onmouseout="hideToolTip();"><img src="${contexto}/pages/ce/img/hint.gif" width="15px"
						id="hint-verificar" style="visibility: hidden;" height="15px" border="0px;" /></a>&nbsp;&nbsp;&nbsp;<%@include file="../includes/include_obrigatorio.jsp" %>
				<h:outputText styleClass="tituloCampos"
					value="#{CElabels['entidade.dataInteresse']}" />
			</h:panelGroup>
			<h:panelGroup>
				<table><tr>
				<td valign="middle">
				<rich:calendar id="dataInicio" styleClass="form"
					value="#{CadastroInicialControle.cadastro.dataInicial}"
					datePattern="dd/MM/yyyy" inputSize="10" inputClass="form"
					oninputblur="blurinput(this); limparMsgObrig('form:dataInicioInputDate', 'dataInicio');" oninputfocus="focusinput(this);"
					oninputkeypress="return mascara(this, '99/99/9999', event); limparMsgObrig('form:dataInicioInputDate', 'dataInicio');"
					enableManualInput="true" zindex="2" showWeeksBar="false"
					tabindex="1"> 
					<a4j:support event="oninputchange" action="#{CadastroInicialControle.setarPerfisNaData}" reRender="perfis"></a4j:support>
					<a4j:support event="onchanged" action="#{CadastroInicialControle.setarPerfisNaData}" reRender="perfis"></a4j:support>
				</rich:calendar>
				</td>
				<td valign="middle">
				<a4j:commandLink id="detalharEventos"
					onclick="if(!validarDisponibilidade()){return false;};"
				    reRender="panelEventos"
					action="#{ConsultaEventosControle.consultarDispAmbCadastroInicial}"
					oncomplete="Richfaces.showModalPanel('panelEventos');">
					<h:outputText/>
					<h:graphicImage value="/imagens/botoesCE/Verificar_disponibilidade.png" style="border: 0px;" alt="#{CElabels['operacoes.disponibilidade.verificar']}"/>
					</a4j:commandLink>
				<div id="divObg-dataInicio" class="mensagemObrigatorio"></div>
				</td>
				</tr></table>
			</h:panelGroup>
			<h:panelGroup>
				<a onmouseover="if (document.getElementById('hint-nomeEvento').style.visibility != 'hidden') {toolTip('<h:outputText value="#{CElabels['entidade.cadastroInicial.hint.nomeEvento']}"/>' , 300 , 'blue')}"
						onmouseout="hideToolTip();"><img src="${contexto}/pages/ce/img/hint.gif" width="15px"
						id="hint-nomeEvento" style="visibility: hidden;" height="15px" border="0px;" /></a>&nbsp;&nbsp;&nbsp;<%@include file="../includes/include_obrigatorio.jsp" %>
				<h:outputText styleClass="tituloCampos" value="Nome do Evento" />
			</h:panelGroup>
			<h:panelGroup>
				<h:inputText onblur="blurinput(this); limparMsgObrig('form:nomeEvento', 'nomeEvento');" onfocus="focusinput(this);"
					styleClass="form" size="30" maxlength="50"
					value="#{CadastroInicialControle.cadastro.nomeEvento}"
					tabindex="2" id="nomeEvento" onkeyup="return convertParaUpperCase(event, this);"></h:inputText>
				<div id="divObg-nomeEvento" class="mensagemObrigatorio"></div>
			</h:panelGroup>
			
			<!-- ------------------ COMBO DE PERFIS ------------------------------ -->

<h:panelGroup>
				<h:outputText styleClass="tituloCampos" value="#{CElabels['entidade.perfilEventoInteresse']}" />
			</h:panelGroup>
			<h:panelGroup id="perfis">
				<!-- Combo de PERFIS -->
				
				<h:selectOneMenu
					onfocus="focusinput(this);" styleClass="form" id="listaPerfis"
					value="#{CadastroInicialControle.cadastro.codigoPerfilEvento}"
					tabindex="3">
					<f:selectItem itemValue="0" itemLabel="#{CElabels['operacoes.selecione']}"/>
					<f:selectItems value="#{CadastroInicialControle.itensPerfisEvento}" />
				</h:selectOneMenu>
			</h:panelGroup>


<!-- ------------------ FIM - COMBO DE PERFIS ------------------------------ -->
			
			<h:panelGroup>
				<a onmouseover="if (document.getElementById('hint-ambienteInteresse').style.visibility != 'hidden') {toolTip('<h:outputText value="#{CElabels['entidade.cadastroInicial.hint.ambienteInteresse']}"/>' , 300 , 'blue')}"
						onmouseout="hideToolTip();"><img src="${contexto}/pages/ce/img/hint.gif" width="15px"
						id="hint-ambienteInteresse" style="visibility: hidden;" height="15px" border="0px;" /></a>&nbsp;&nbsp;&nbsp;<%@include file="../includes/include_obrigatorio.jsp" %>
				<h:outputText styleClass="tituloCampos" value="Ambiente de Interesse" />
			</h:panelGroup>
			<h:panelGroup>
				<!-- Combo de Ambientes -->
				
				<h:selectOneMenu onblur="blurinput(this); limparMsgObrig('form:ambienteInteresse', 'ambienteInteresse');"
					onfocus="focusinput(this);" styleClass="form" id="ambienteInteresse"
					value="#{CadastroInicialControle.cadastro.codigoAmbienteInteresse}"
					tabindex="4">
					<f:selectItem itemValue="0" itemLabel="#{CElabels['operacoes.selecione']}"/>
					<f:selectItems value="#{CadastroInicialControle.listaAmbiente}" />
				</h:selectOneMenu>
				<div id="divObg-ambienteInteresse" class="mensagemObrigatorio"></div>
			</h:panelGroup>

											
			<h:panelGroup>
				<a onmouseover="if (document.getElementById('hint-nomeCliente').style.visibility != 'hidden') {toolTip('<h:outputText value="#{CElabels['entidade.cadastroInicial.hint.nomeCliente']}"/>' , 300 , 'blue')}"
						onmouseout="hideToolTip();"><img src="${contexto}/pages/ce/img/hint.gif" width="15px"
						id="hint-nomeCliente" style="visibility: hidden;" height="15px" border="0px;" /></a>&nbsp;&nbsp;&nbsp;<%@include file="../includes/include_obrigatorio.jsp" %>
				<h:outputText styleClass="tituloCampos" value="Nome do Cliente" />
			</h:panelGroup>
			<h:panelGroup>
			<table cellpadding="0" cellspacing="0"><tr><td valign="top">
			
				<h:outputText value="#{CadastroInicialControle.pessoa.nomeCompleto}" rendered="#{CadastroInicialControle.desabilitaCampos}"/>
					
					
				<h:inputText rendered="#{!CadastroInicialControle.desabilitaCampos}" 
				    onblur="blurinput(this); limparMsgObrig('form:nomeCliente', 'nomeCliente'); document.getElementById('form:nomeClienteOut').value = this.value;"  
				    onfocus="focusinput(this);"
					styleClass="form" size="70" maxlength="150"
					value="#{CadastroInicialControle.pessoa.nomeCompleto}"
					id="nomeCliente"
					tabindex="5" onkeyup="return convertParaUpperCase(event, this);"></h:inputText>
					<h:inputHidden value="#{CadastroInicialControle.pessoa.nomeCompleto}" id="nomeClienteOut" />
			&nbsp;
			
			</td>
			<td valign="top">
			<a4j:commandButton value="#{CElabels['menu.operacoesCE.alterarNomeInteressado']}" action="#{CadastroInicialControle.alterarNomeInteressado}"
								reRender="panelCadastroSimplificadoCliente"
								image="/imagens/botoesCE/alterar_nome.png">
								</a4j:commandButton>
			</td>
			</tr></table>
				
				
			<div id="divObg-nomeCliente" class="mensagemObrigatorio"></div>	
			</h:panelGroup>
			<h:panelGroup>
				<a onmouseover="if (document.getElementById('hint-qeclienteEvento').style.visibility != 'hidden') {toolTip('<h:outputText value="#{CElabels['entidade.cadastroInicial.hint.qeclienteEvento']}"/>' , 300 , 'blue')}"
						onmouseout="hideToolTip();"><img src="${contexto}/pages/ce/img/hint.gif" width="15px"
						id="hint-qeclienteEvento" style="visibility: hidden;" height="15px" border="0px;" /></a>&nbsp;&nbsp;&nbsp;<h:outputText styleClass="tituloCampos"
					value="Quem � o cliente no evento" />
			</h:panelGroup>
			<h:panelGroup>
				<h:inputText onblur="blurinput(this);" onfocus="focusinput(this);"
					styleClass="form" size="21" maxlength="20"
					value="#{CadastroInicialControle.cadastro.quemE}" 
					tabindex="6" id="quemE" onkeyup="return convertParaUpperCase(event, this);"></h:inputText>
				<div id="divObg-quemE" class="mensagemObrigatorio"></div>
			</h:panelGroup>
			<h:panelGroup>
				<a onmouseover="if (document.getElementById('hint-telefone').style.visibility != 'hidden') {toolTip('<h:outputText value="#{CElabels['entidade.hint.telefone']}"/>' , 300 , 'blue')}"
						onmouseout="hideToolTip();"><img src="${contexto}/pages/ce/img/hint.gif" width="15px"
						id="hint-telefone" style="visibility: hidden;" height="15px" border="0px;" />&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</a><%@include file="../includes/include_obrigatorio.jsp" %>
				<h:outputText  styleClass="tituloCampos" value="Telefone Celular" />
			</h:panelGroup>
			<h:panelGroup>
				<h:inputText size="13"
                             maxlength="13"
                             onchange="return validar_Telefone(this.id);"
                             onblur="blurinput(this); limparMsgObrig('form:telefoneCelular', 'telefoneCelular');"
                             onkeypress="return mascara(this.form, this.id , '(99)999999999', event);"
                             onfocus="focusinput(this);"
                             styleClass="form"
					value="#{CadastroInicialControle.pessoa.telefoneCelular}"
					id="telefoneCelular"
					tabindex="7"/>
				<div id="divObg-telefoneCelular" class="mensagemObrigatorio"></div>
			</h:panelGroup>
			<h:panelGroup>
				<%@include file="../includes/include_obrigatorio.jsp" %>
				<h:outputText styleClass="tituloCampos" value="Telefone Comercial" />
			</h:panelGroup>
			<h:panelGroup>
				<h:inputText onblur="blurinput(this); limparMsgObrig('form:telefoneComercial', 'telefoneComercial');" onfocus="focusinput(this);"
					styleClass="form" size="12" maxlength="15"
					onkeypress="return mascara(this.form, this.id , '(99)999999999', event);"
					value="#{CadastroInicialControle.pessoa.telefoneComercial}"
					id="telefoneComercial"
					tabindex="8"></h:inputText>
				<div id="divObg-telefoneComercial" class="mensagemObrigatorio"></div>
			</h:panelGroup>
			<h:panelGroup>
				<%@include file="../includes/include_obrigatorio.jsp" %>
				<h:outputText styleClass="tituloCampos" value="Telefone Fixo" />
			</h:panelGroup>
			<h:panelGroup>
				<h:inputText  onblur="blurinput(this); limparMsgObrig('form:telefoneFixo', 'telefoneFixo');" onfocus="focusinput(this);"
					styleClass="form" size="12" maxlength="15"
					onkeypress="return mascara(this.form, this.id , '(99)999999999', event);"
					value="#{CadastroInicialControle.pessoa.telefoneFixo}"
					id="telefoneFixo"
					tabindex="9" ></h:inputText>
				<div id="divObg-telefoneFixo" class="mensagemObrigatorio"></div>
			</h:panelGroup>
			<h:panelGroup>
				<h:outputText styleClass="tituloCampos" value="Email" />
			</h:panelGroup>
			<h:panelGroup>
				<h:inputText onblur="blurinput(this); " onfocus="focusinput(this);"
					styleClass="form" size="20" maxlength="50"
					value="#{CadastroInicialControle.pessoa.email}" id="email"
					tabindex="10"></h:inputText>
				<div id="divObg-email" class="mensagemObrigatorio"></div>
			</h:panelGroup>
			<h:panelGroup>
				
				<h:outputText styleClass="tituloCampos" value="N�mero de Convidados" />
			</h:panelGroup>
			<h:panelGroup>
				<rich:inputNumberSpinner
					value="#{CadastroInicialControle.cadastro.nrConvidados}"
					cycled="false" minValue="0" step="50" maxValue="10000" 
					id= "nrconvidados" onchange="zerarConvidados();"
					onblur="zerarConvidados();"
					tabindex="11"/>
			</h:panelGroup>
			<h:panelGroup>
				<h:outputText styleClass="tituloCampos"
					value="Como nos conheceu?" />
			</h:panelGroup>
			<h:panelGroup>
				<h:selectOneMenu onblur="blurinput(this);"
					onfocus="focusinput(this);" styleClass="form" id="ComoConheceu"
					value="#{CadastroInicialControle.cadastro.comoConheceu}"
					tabindex="12">
					<f:selectItem itemValue="" itemLabel="#{CElabels['operacoes.selecione']}"/>
					<f:selectItems value="#{CadastroInicialControle.comoConheceu}" />
				</h:selectOneMenu>
				<div id="divObg-comoConheceu" class="mensagemObrigatorio"></div>
			</h:panelGroup>
			<h:panelGroup>
				<h:outputText styleClass="tituloCampos" value="Quem indicou?" />
			</h:panelGroup>
			<h:panelGroup>
				<h:inputText onblur="blurinput(this);" onfocus="focusinput(this);"
					styleClass="form" size="20" maxlength="100"
					value="#{CadastroInicialControle.cadastro.quemIndicou}"
					id="quemIndicou"
					tabindex="13" onkeyup="return convertParaUpperCase(event, this);"></h:inputText>
				<div id="divObg-quemIndicou" class="mensagemObrigatorio"></div>
			</h:panelGroup>
			<h:panelGroup>
				<%@include file="../includes/include_obrigatorio.jsp" %>
				<h:outputText styleClass="tituloCampos"
					value="Qual a forma de contato?" />
			</h:panelGroup>
			<h:panelGroup>

				<h:selectOneMenu onblur="blurinput(this); limparMsgObrig('form:formaContato', 'formaContato');"
					onfocus="focusinput(this);" styleClass="form" id="formaContato"
					value="#{CadastroInicialControle.cadastro.formaContato}"
					tabindex="14">
					<f:selectItem itemValue="" itemLabel="#{CElabels['operacoes.selecione']}"/>
					<f:selectItems value="#{CadastroInicialControle.formasContato}" />
				</h:selectOneMenu>
				<div id="divObg-formaContato" class="mensagemObrigatorio"></div>
			</h:panelGroup>
			<h:panelGroup>
				<h:outputText styleClass="tituloCampos" value="Observa��es:" />
			</h:panelGroup>
			<h:panelGroup>
				<rich:editor id="textoPadrao" useSeamText="false" viewMode="visual" tabindex="15"
						width="400" height="200" value="#{CadastroInicialControle.cadastro.observacoes}"
						onchange="limparMsgObrigTextoPadrao();" />
			</h:panelGroup>
		</h:panelGrid>
</h:panelGrid></td></tr>
<tr>
	<td width="100%">
	<table width="100%" border="0" align="center" cellpadding="0"
		cellspacing="0">
		<tr>
			<td width="10"><img
				src="${contexto}/imagens/agenda_imgs/menu_esq.jpg" width="10"
				height="69"></td>
			<td background="${contexto}/imagens/agenda_imgs/menu_fundo.jpg">
			<table width="100%" border="0" cellspacing="0" cellpadding="0">
				<tr>

				<td align="center">
				<h:commandLink id="salvarOrcado2" action="#{CadastroInicialControle.salvarEventoJaNegociado}"
				rendered="#{CadastroInicialControle.orcado}" onclick="if(!validar()){return false;};"
					actionListener="#{CadastroInicialControle.autorizacao}">
				<!-- Funcionalidade.cadastroinicial -->
				<f:attribute name="funcao" value="106" />
				<h:graphicImage value="/imagens/bt_salvar.jpg" style="border: 0px;"/>
				</h:commandLink></td>
				<td align="center">
				<h:commandLink id="salvar2" action="#{CadastroInicialControle.salvar}"
				rendered="#{!CadastroInicialControle.orcado}" onclick="if(!validar()){return false;};"
					actionListener="#{CadastroInicialControle.autorizacao}">
				<!-- Funcionalidade.cadastroinicial -->
				<f:attribute name="funcao" value="106" />
				
				<h:graphicImage value="/imagens/bt_salvar.jpg" style="border: 0px;"/>
				</h:commandLink></td>
				<td align="center">
				<h:commandLink id="salvarParcial2" action="#{CadastroInicialControle.salvarParcial}"
				rendered="#{!CadastroInicialControle.orcado}" onclick="if(!validarCadastroParcial()){return false;};"
					actionListener="#{CadastroInicialControle.autorizacao}">
				<!-- Funcionalidade.cadastroinicial -->
				<f:attribute name="funcao" value="106" />
				<h:graphicImage value="/imagens/bt_salvarparcialmente.jpg" style="border: 0px;"/>
				</h:commandLink></td>
				<td align="center">
				<h:commandLink id="simularOrcamento2" action="#{OrcamentoDetalhadoControle.abrirOrcamentoFicticio}"
				rendered="#{!CadastroInicialControle.orcado}"
				actionListener="#{OrcamentoDetalhadoControle.selOrigemDetalhamentoEvento}">
				<h:graphicImage value="/imagens/bt_simularorcamento.jpg" style="border: 0px;"/>
				</h:commandLink></td>
				<td align="center">
				<a4j:commandLink title="Ajuda" oncomplete="ativarAjuda(['telefone', 'verificar', 'nomeEvento', 'ambienteInteresse', 'nomeCliente', 'qeclienteEvento']);" >
													<h:graphicImage value="/imagens/bt_ajuda.jpg" style="border: 0px;"/>
													</a4j:commandLink>
				</td></tr>
			</table>
			</td>
			<td width="10"><img
				src="${contexto}/imagens/agenda_imgs/menu_dir.jpg" width="10"
				height="69"></td>
		</tr>
	</table>
	</td>
	</tr>
</table>
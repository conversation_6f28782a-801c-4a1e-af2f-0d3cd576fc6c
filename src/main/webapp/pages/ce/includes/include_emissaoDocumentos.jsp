<h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
<h:inputHidden id="emailEnvio" value="#{EmissaoDocumentosControle.email}" />
					
	<h:panelGroup>
	<table width="100%" border="0" cellspacing="0" cellpadding="0">
		  <tr>
		    <td>
		    <table width="100%" border="0" cellspacing="0" cellpadding="0">
		      <tr valign="top">
		        <td bgcolor="#FFFFFF" style="padding:8px">
					<h:panelGrid columns="8">
						<a4j:commandLink id="cancelarEvento" action="#{NavegacaoControle.abrirTelaExibeDetalhamentoEvento}"
									 styleClass="botoes">
								<h:graphicImage value="/imagens/botoesCE/voltar_sem_fundo.png" alt="#{CElabels['operacoes.voltar']}" style="border: 0px;"/>
							</a4j:commandLink>		
							
								<h:outputText value="    " />
							
							<a4j:commandButton id="exibirChecklist" onclick="showChecklist(this,'checklist','abrir', 'checklistPopup');"
									image="/imagens/botoesCE/checklist.png" alt="#{CElabels['entidade.checklist']}">
							</a4j:commandButton>
							
								<h:outputText value="    " />
							
							<a4j:commandButton id="exibirVersoChecklist" 
									 image="/imagens/botoesCE/verso_checklist.png"
									 onclick="showChecklist(this,'versoChecklist','abrir', 'versoChecklistPopup');"
									 styleClass="botoes" value="Verso Check-List" >
							</a4j:commandButton>
							
								<h:outputText value="    " />
							
							<a4j:commandButton id="exibirModelo" action="#{EmissaoDocumentosControle.imprimirModelo}" 
									 value="Contrato Modelo" image="/imagens/botoesCE/contrato_modelo.png"
									 oncomplete="document.getElementById('imprimirContrato').click();"
									 styleClass="botoes">
							</a4j:commandButton>
							<br>		
					</h:panelGrid>	
					
					<!-- ------------------------------- INICIO - DOWNLOAD DOS LAYOUTS --------------- -->
					<c:if test="${not empty EmissaoDocumentosControle.layouts}">
					<br/>&nbsp;	
					<h:panelGroup>
						<img src="${contexto}/images/arrow2.gif" width="16" height="16"
							style="vertical-align: middle; margin-right: 6px;">
						<h:outputText value="#{CElabels['entidade.layouts']}:" />
						<div class="sep" style="margin: 4px 0 5px 0;"><img
							src="${contexto}/images/shim.gif"></div>
					</h:panelGroup>	
					
					<rich:dataTable id="layouts" width="50%" headerClass="consulta"
							rowClasses="linhaImpar, linhaPar" 
							value="#{EmissaoDocumentosControle.layouts}"
							var="layout">
					<rich:column>
					<f:facet name="header">
									<h:outputText value="#{CElabels['entidade.ambiente']}" />
								</f:facet>
					<h:outputText value="#{layout.nomeAmbiente}" />
					</rich:column>
					
					<rich:column>
					<f:facet name="header">
												<h:outputText value="#{CElabels['menu.operacoesCE.downloadLayout']}" />
											</f:facet>
					<!-- Download da imagem do Layout -->
					<center>
											<h:commandLink
												style="margin-top:5px;"
												action="#{EmissaoDocumentosControle.downloadLayout}" immediate="true" >												
												<h:graphicImage value="/imagens/botoesCE/Download_Layout_Download.png" alt="#{CElabels['menu.operacoesCE.downloadLayout']}" style="border: 0px;"/>
											</h:commandLink>
					</center>
					</rich:column>
							
					</rich:dataTable>
							
					</c:if>
						
					<br/>&nbsp;		
					<!-- ------------------------------- FIM - DOWNLOAD DOS LAYOUTS --------------- -->
					
					
					
					<h:panelGroup>
						<img src="${contexto}/images/arrow2.gif" width="16" height="16"
							style="vertical-align: middle; margin-right: 6px;">
						<h:outputText value="#{CElabels['entidade.orcamentos']}:" />
						<div class="sep" style="margin: 4px 0 5px 0;"><img
							src="${contexto}/images/shim.gif"></div>
					</h:panelGroup>
							<!-- Lista de Or�amentos -->
						<rich:dataTable id="itens2" width="98%" headerClass="consulta"
							rowClasses="linhaImpar, linhaPar" 
							value="#{EmissaoDocumentosControle.orcamentos}"
							rendered="#{!empty EmissaoDocumentosControle.orcamentos}"
							rows="#{ProspectsControle.nrPagina}" var="dgOrcamento">
							<rich:column>
								<f:facet name="header">
									<h:outputText value="Arquivo" />
								</f:facet>
								<center>
									<h:graphicImage value="/imagens/Word_Icon.png"
										style="border: 0px; width: 25;"/>
								</center>
							</rich:column>		
							<rich:column>
								<f:facet name="header">
									<h:outputText value="Nome Arquivo" />
								</f:facet>
								<center>
									<h:outputText value="#{dgOrcamento.nomeArquivo}" />
								</center>
							</rich:column>	
							<rich:column>
								<f:facet name="header">
									<h:outputText value="Descri��o" />
								</f:facet>
								<center>
									<h:outputText escape="false" value="#{dgOrcamento.descricao}"/>
								</center>
							</rich:column>			
							<rich:column>
								<f:facet name="header">
									<h:outputText value="Data de Envio" />
								</f:facet>
								<center>
									<h:outputText escape="false" value="#{dgOrcamento.dataEnvioFormatada}"/>
								</center>
							</rich:column>		
							<rich:column>
								<f:facet name="header">
									<h:outputText value="Data de Impress�o" />
								</f:facet>
								<center>
									<h:outputText escape="false" value="#{dgOrcamento.dataImpressaoFormatada}"/>
								</center>
							</rich:column>		
							<rich:column>
								<f:facet name="header">
									<h:outputText value="Op��es" />
								</f:facet>
								<center>
									<table><tr><td>
									<h:commandLink
										onclick="preencherHiddenChamarBotao('form:botaoImpressaoOrcamento','form:codigoOrcamento','#{dgOrcamento.codigo}');return false;">
										<h:graphicImage value="/imagens/botoesCE/imprimir.png" alt="#{CElabels['operacoes.imprimir']}" style="border: 0px;"/>
									</h:commandLink>
									</td><td>
									<h:commandButton value="Enviar" style="size: 200"
										image="/imagens/botoesCE/enviar.png"
										onclick="preencherHiddenChamarBotao('form:botaoEnvioOrcamento','form:codigoOrcamento','#{dgOrcamento.codigo}');return false;">
									</h:commandButton>
									</td></tr></table>
								</center>
							</rich:column>							
						</rich:dataTable>
							<br>
					<h:panelGroup>
						<img src="${contexto}/images/arrow2.gif" width="16" height="16"
							style="vertical-align: middle; margin-right: 6px;">
						<h:outputText value="#{CElabels['entidade.contratos']}:" />
						<div class="sep" style="margin: 4px 0 5px 0;"><img
							src="${contexto}/images/shim.gif"></div>
					</h:panelGroup>
						<!-- Lista de Contratos -->
						<rich:dataTable id="itens" width="98%" headerClass="consulta"
							rowClasses="linhaImpar, linhaPar" 
							value="#{EmissaoDocumentosControle.contratos}"
							rendered="#{!empty EmissaoDocumentosControle.contratos}"
							rows="#{ProspectsControle.nrPagina}" var="dgContrato">
							<rich:column>
								<f:facet name="header">
									<h:outputText value="Arquivo" />
								</f:facet>
								<center>
									<h:graphicImage value="/imagens/Word_Icon.png"
										style="border: 0px; width: 25;"/>
								</center>
							</rich:column>		
							<rich:column>
								<f:facet name="header">
									<h:outputText value="Nome Arquivo" />
								</f:facet>
								<center>
									<h:outputText value="#{dgContrato.nomeArquivo}" />
								</center>
							</rich:column>	
							<rich:column>
								<f:facet name="header">
									<h:outputText value="Descri��o" />
								</f:facet>
								<center>
									<h:outputText escape="false" value="#{dgContrato.descricao}"/>
								</center>
							</rich:column>			
							<rich:column>
								<f:facet name="header">
									<h:outputText value="Data de Envio" />
								</f:facet>
								<center>
									<h:outputText escape="false" value="#{dgContrato.dataEnvioFormatada}"/>
								</center>
							</rich:column>		
							<rich:column>
								<f:facet name="header">
									<h:outputText value="Data de Impress�o" />
								</f:facet>
								<center>
									<h:outputText escape="false" value="#{dgContrato.dataImpressaoFormatada}"/>
								</center>
							</rich:column>		
							<rich:column>
								<f:facet name="header">
									<h:outputText value="Op��es" />
								</f:facet>
								<center>
									<table><tr><td>
									<h:commandLink
										onclick="preencherHiddenChamarBotao('form:botaoImpressaoContrato','form:codigoContrato','#{dgContrato.codigo}');return false;">
										<h:graphicImage value="/imagens/botoesCE/imprimir.png" alt="#{CElabels['operacoes.imprimir']}" style="border: 0px;"/>
									</h:commandLink>
									</td><td>
									<h:commandButton value="Enviar" style="size: 200"
										image="/imagens/botoesCE/enviar.png"
										onclick="preencherHiddenChamarBotao('form:botaoEnvioContrato','form:codigoContrato','#{dgContrato.codigo}');return false;">
									</h:commandButton>
									</td></tr></table>
								</center>
							</rich:column>							
						</rich:dataTable>
					<!-- Controles que s�o chamados dentro do data table, bot�o 
							para executar a��o, hidden para guardar o valor do ID do registro na lista -->
					<br/>		
					<h:panelGroup rendered="#{!empty EmissaoDocumentosControle.recibos}">
						<img src="${contexto}/images/arrow2.gif" width="16" height="16"
							style="vertical-align: middle; margin-right: 6px;">
						<h:outputText value="Recibos:" />
						<div class="sep" style="margin: 4px 0 5px 0;"><img
							src="${contexto}/images/shim.gif"></div>
					</h:panelGroup>
					<rich:dataTable id="recibos" width="98%" headerClass="consulta"
							rowClasses="linhaImpar, linhaPar" columnClasses="centralizado"
							value="#{EmissaoDocumentosControle.recibos}"
							rendered="#{!empty EmissaoDocumentosControle.recibos}"
							rows="#{ProspectsControle.nrPagina}" var="recibo">
							<rich:column>
								<f:facet name="header">
									<h:outputText value="C�digo" />
								</f:facet>
								
								<h:outputText escape="false" value="#{recibo.codigoRecibo}"/>
							</rich:column>		
							
							<rich:column>
								<f:facet name="header">
									<h:outputText value="Nome Pagador" />
								</f:facet>
								
								<h:outputText escape="false" value="#{recibo.nomePessoaPagadora}"/>
							</rich:column>
							
							<rich:column>
								<f:facet name="header">
									<h:outputText value="Data" />
								</f:facet>
								
								<h:outputText escape="false" value="#{recibo.data_Apresentar}"/>
							</rich:column>
							
							<rich:column>
								<f:facet name="header">
									<h:outputText value="Valor" />
								</f:facet>
								
								<h:outputText escape="false" value="#{recibo.valor_Apresentar}"/>
							</rich:column>
							
							<rich:column>
								<f:facet name="header">
									<h:outputText value="Op��es" />
								</f:facet>
								
								<a4j:commandButton id="imprimir"
                                                       value="#{msg_bt.btn_ImprimirReciboPagamento}" 
                                                       image="/imagens/botoesCE/imprimir.png"
                                                       actionListener="#{ReciboControle.prepareRecibo}"
                                                       action="#{ReciboControle.imprimirReciboPDFCE}"
                                                       oncomplete="abrirPopup('../../../relatorio/#{ReciboControle.nomeArquivoRelatorioGeradoAgora}','', 780, 595);">
                                        <f:attribute name="reciboPagamentoVO" value="#{recibo.recibo}" />
                                        <f:attribute name="codigoEvento" value="#{recibo.codigoEvento}" />
                                    </a4j:commandButton>
                                    &nbsp;
                                    <a4j:commandButton id="enviarReciboC"
                                                       value="Enviar"
                                                       image="/imagens/botoesCE/enviar.png"
                                                       actionListener="#{ReciboControle.prepareReciboEmailCE}"
                                                       action="#{ReciboControle.confirmarExcluir}"
                                                       oncomplete="#{ReciboControle.msgAlert}">
                                        <f:attribute name="reciboPagamentoVO" value="#{recibo.recibo}" />
                                        <f:attribute name="codigoEvento" value="#{recibo.codigoEvento}" />
                                    </a4j:commandButton>
                            </rich:column>	
							
							
					</rich:dataTable>
					
					
							
					<a4j:commandButton id="botaoImpressaoContrato" action="#{EmissaoDocumentosControle.incluirRegistroImpressaoContrato}" style="visibility: hidden;"
						reRender="itens"
					   	oncomplete="document.getElementById('imprimirContrato').click();"/>
					   	<a4j:commandButton id="botaoEnvioContrato" 
					   	reRender="itens"
					   	action="#{EmissaoDocumentosControle.incluirEnvioContrato}" style="visibility: hidden;" oncomplete="email('form:emailEnvio');"/>
					
					<input type="button" id="imprimirContrato" style="display: none;" onclick="abrirPopup('${contexto}/faces/pages/ce/eventos/contrato?q=1','', 500, 500);"/>
					<h:inputHidden id="codigoContrato" value="#{EmissaoDocumentosControle.codigoModeloContrato}" />	
					
					
					 <a4j:commandButton id="botaoImpressaoOrcamento" 
										reRender="itens2"
										action="#{EmissaoDocumentosControle.incluirRegistroImpressaoOrcamento}" style="visibility: hidden;"
					    	oncomplete="document.getElementById('imprimirOrcamento').click();"/>	
					   	
					<a4j:commandButton id="botaoEnvioOrcamento" 
										reRender="itens2"
										action="#{EmissaoDocumentosControle.incluirEnvioOrcamento}" style="visibility: hidden;" oncomplete="email('form:emailEnvio');"/>	
					   
					<input type="button" id="imprimirOrcamento" style="display: none;" onclick="abrirPopup('${contexto}/faces/pages/ce/eventos/contrato?q=2','', 500, 500);"/>
					<h:inputHidden id="codigoOrcamento" value="#{EmissaoDocumentosControle.codigoModeloOrcamento}" />	
		        	
		        </td>
		      </tr>
		    </table></td>
		  </tr>
	</table>
	</h:panelGroup>	
</h:panelGrid>

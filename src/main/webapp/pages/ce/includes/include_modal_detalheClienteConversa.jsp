<rich:modalPanel id="panelDetalheCliente" autosized="true" shadowOpacity="true" width="450" height="250"
				 showWhenRendered="#{ConversaControle.exibir['detalhamentoCliente']}">
    	
	<f:facet name="header">
		<h:panelGroup>
			<h:outputText value="Detalhamento do Cliente"></h:outputText>
		</h:panelGroup>
	</f:facet>
	<f:facet name="controls">
		<h:panelGroup>
			<h:graphicImage value="/imagens/close.png" style="cursor:pointer"
				id="hidelinkDetalheCliente" />
			<rich:componentControl for="panelDetalheCliente"
				attachTo="hidelinkDetalheCliente" operation="hide"
				event="onclick" />
		</h:panelGroup>
	</f:facet>

        
<h:form id="formDetalheCliente">
               
 <%@include file="/pages/ce/includes/topoReduzido.jsp"%>
    
        
 <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
		<f:attribute name="codigoCliente"
                                                                 value="#{ClienteControle.clienteVO.codigo}" />
                                                    <h:panelGrid width="100%" cellspacing="5"
                                                                 styleClass="colunaCentralizada">
                                                        <h:panelGroup>
                                                            <table width="100%" border="0" align="left" cellpadding="0"
                                                                   cellspacing="0" bgcolor="#e6e6e6" style="padding: 10px;">
                                                                <tr>
                                                                    <td align="left" valign="top" style="padding-bottom: 5px;">
                                                                        <div style="clear: both;" class="text">
                                                                            <p style="margin-bottom: 6px;"><img
                                                                                    src="${contexto}/images/arrow2.gif" width="16" height="16"
                                                                                    style="vertical-align: middle; margin-right: 6px;">
                                                                                <h:outputText style="font-weight: bold"
                                                                                              value="Mensagens / Avisos" /></p>
                                                                            <div class="sep"><img src="${contexto}/images/shim.gif"></div>
                                                                        </div>
                                                                    </td>
                                                                </tr>
                                                                <tr>
                                                                    <td align="left" valign="top"><h:dataTable
                                                                            id="avisosCliente" width="100%" columnClasses="textsmall"
                                                                            value="#{ClienteControle.clienteVO.listaMensagemAvisoCliente}"
                                                                            styleClass="tabFormSubordinada" var="avisoCliente">
                                                                            <h:column>
                                                                                <%-- <img style="vertical-align: middle; margin-right: 4px;"
                                                                                      src="images/arrow.gif">--%>
                                                                                <img style="vertical-align: middle; margin-right: 4px;" src="${contexto}/imagens/ico_ani_telefone.gif">
                                                                                <%--<rich:spacer width="10px" />--%>
                                                                                <h:panelGroup
                                                                                    rendered="#{avisoCliente.navegacaoFrame}">
                                                                                    <a4j:commandLink styleClass="link_red"
                                                                                                     action="#{ClienteControle.abreTela}">
                                                                                        <h:outputText value="#{avisoCliente.mensagem}" />
                                                                                    </a4j:commandLink>
                                                                                </h:panelGroup>
                                                                                <h:panelGroup
                                                                                    rendered="#{avisoCliente.navegacaoPopUp}">
                                                                                    <a4j:commandLink styleClass="link_red"
                                                                                                     action="#{ClienteControle.abreTela}"
                                                                                                     oncomplete="abrirPopup('#{avisoCliente.tipomensagem.navegacao}', '#{avisoCliente.tipomensagem}', 780, 595);">
                                                                                        <h:outputText value="#{avisoCliente.mensagem}" />
                                                                                    </a4j:commandLink>
                                                                                </h:panelGroup>

                                                                            </h:column>
                                                                        </h:dataTable>
                                                                    </td>
                                                                </tr>
                                                            </table>
                                                        </h:panelGroup>
                                                    </h:panelGrid>

                                                    <%--div style="clear:both;"--%>
                                                    <h:panelGrid columns="2" width="100%"
                                                                 columnClasses="colunaCentralizada" cellspacing="5">
                                                        <h:panelGroup>
                                                            <table width="100%" border="0" align="left" cellpadding="0"
                                                                   cellspacing="0" bgcolor="#e6e6e6" style="padding: 10px;">
                                                                <tr>
                                                                    <td align="left" valign="top" style="padding-bottom: 5px;">
                                                                        <div style="clear: both;" class="text">
                                                                            <p style="margin-bottom: 6px;"><img
                                                                                    src="${contexto}/images/arrow2.gif" width="16" height="16"
                                                                                    style="vertical-align: middle; margin-right: 6px;"><h:outputText
                                                                                    style="font-weight: bold" value="Dados Pessoais" /></p>
                                                                            <div class="sep" style="margin: 4px 0 5px 0;"><img
                                                                                    src="${contexto}/images/shim.gif"></div>
                                                                        </div>
                                                                    </td>
                                                                </tr>
                                                                <tr>
                                                                    <td align="left" valign="top"><h:panelGrid
                                                                            id="pendencias" columns="1" columnClasses="left, right"
                                                                            width="100%" border="0" cellspacing="0" cellpadding="0"
                                                                            styleClass="textsmall">
                                                                            <h:panelGroup>
                                                                                <h:outputText style="font-weight: bold"
                                                                                              value="Data Cadastramento:" />
                                                                                
                                                                                    <h:outputText value="        " />
                                                                                
                                                                                <h:outputText style="font-weight: bold"
                                                                                              styleClass="blue"
                                                                                              value="#{ClienteControle.clienteVO.pessoa.dataCadastro_Apresentar}" />
                                                                            </h:panelGroup>
                                                                            <h:panelGroup>
                                                                                <h:outputText style="font-weight: bold"
                                                                                              value="Categoria:" />
                                                                                <f:verbatim>
                                                                                    <h:outputText value="        " />
                                                                                </f:verbatim>
                                                                                <h:outputText style="font-weight: bold"
                                                                                              styleClass="blue"
                                                                                              value="#{ClienteControle.clienteVO.categoria.nome}" />
                                                                            </h:panelGroup>
                                                                            <h:panelGroup>
                                                                                <h:outputText style="font-weight: bold"
                                                                                              value="Data Matr�cula: " />
                                                                                <f:verbatim>
                                                                                    <h:outputText style="font-weight: bold" styleClass="blue"
                                                                                                  value="#{ClienteControle.clienteVO.situacaoClienteSinteticoVO.dataMatricula}">
                                                                                        <f:convertDateTime type="date" dateStyle="short"
                                                                                                           locale="pt" timeZone="America/Sao_Paulo"
                                                                                                           pattern="dd/MM/yyyy" />
                                                                                    </h:outputText>
                                                                                    -
                                                                                    <h:outputText
                                                                                        style="font-weight: bold" styleClass="blue"
                                                                                        value="#{ClienteControle.clienteVO.situacaoClienteSinteticoVO.nomeDiaSemanaDataMatricula}" />
                                                                                    <h:outputText
                                                                                        style="font-weight: bold" styleClass="blue"
                                                                                        value="#{ClienteControle.clienteVO.situacaoClienteSinteticoVO.dataMatriculaAteHojePorExtenso}" />
                                                                                </f:verbatim>
                                                                                <h:outputText style="font-weight: bold"
                                                                                              styleClass="blue" />
                                                                            </h:panelGroup>
                                                                            <h:panelGroup>
                                                                                <h:outputText style="font-weight: bold"
                                                                                              value="Data �ltima Rematr�cula: " />
                                                                                <f:verbatim>
                                                                                    <h:outputText style="font-weight: bold" styleClass="blue"
                                                                                                  value="#{ClienteControle.clienteVO.situacaoClienteSinteticoVO.dataUltimaRematricula}">
                                                                                        <f:convertDateTime type="date" dateStyle="short"
                                                                                                           locale="pt" timeZone="America/Sao_Paulo"
                                                                                                           pattern="dd/MM/yyyy" />
                                                                                    </h:outputText>
                                                                                    -
                                                                                    <h:outputText
                                                                                        style="font-weight: bold" styleClass="blue"
                                                                                        value="#{ClienteControle.clienteVO.situacaoClienteSinteticoVO.nomeDiaSemanaDataUltimaRematricula}" />
                                                                                    <h:outputText
                                                                                        style="font-weight: bold" styleClass="blue"
                                                                                        value="#{ClienteControle.clienteVO.situacaoClienteSinteticoVO.dataRematriculaAteHojePorExtenso}" />
                                                                                </f:verbatim>
                                                                                <h:outputText style="font-weight: bold"
                                                                                              styleClass="blue" />
                                                                            </h:panelGroup>
                                                                            <h:panelGroup>
                                                                                <h:outputText style="font-weight: bold"
                                                                                              value="Situa��o:" />
                                                                                <f:verbatim>
                                                                                    <h:outputText value="        " />
                                                                                </f:verbatim>
                                                                                <h:outputText style="font-weight: bold"
                                                                                              styleClass="blue"
                                                                                              value="#{ClienteControle.clienteVO.situacao_Apresentar}" />
                                                                            </h:panelGroup>


                                                                            <h:panelGroup>
                                                                                <f:verbatim>
                                                                                    <h:outputText value="        " />
                                                                                </f:verbatim>
                                                                                <h:dataTable id="emailVO" width="100%"
                                                                                             rowClasses="textsmall"
                                                                                             columnClasses="centralizado,centralizado"
                                                                                             value="#{ClienteControle.pessoaVO.emailVOs}"
                                                                                             var="email">
                                                                                    <h:column>
                                                                                        <f:facet name="header">
                                                                                            <h:outputText style="font-weight: bold"
                                                                                                          styleClass="textsmall" value="Email:" />
                                                                                        </f:facet>
                                                                                        <h:outputText styleClass="blue" value="#{email.email}" />
                                                                                    </h:column>
                                                                                    <h:column>
                                                                                        <f:facet name="header">
                                                                                            <h:outputText style="font-weight: bold"
                                                                                                          styleClass="textsmall"
                                                                                                          value="Email para Correspond�ncia:" />
                                                                                        </f:facet>
                                                                                        <h:outputText styleClass="blue"
                                                                                                      value="#{email.emailCorrespondencia_Apresentar}" />
                                                                                    </h:column>
                                                                                </h:dataTable>
                                                                            </h:panelGroup>
                                                                        </h:panelGrid></td>
                                                                </tr>
                                                            </table>
                                                        </h:panelGroup>
                                                        <h:panelGroup>
                                                            <table width="100%" border="0" align="left" cellpadding="0"
                                                                   cellspacing="0" bgcolor="#e6e6e6"
                                                                   style="margin-right: 10px; margin-bottom: 10px; padding: 10px;">
                                                                <tr>
                                                                    <td align="left" valign="top" style="padding-bottom: 5px;">
                                                                        <div style="clear: both;" class="text">
                                                                            <p style="margin-bottom: 6px;"><img
                                                                                    src="${contexto}/images/arrow2.gif" width="16" height="16"
                                                                                    style="vertical-align: middle; margin-right: 6px;"><h:outputText
                                                                                    style="font-weight: bold" value="Dados do Aluno" /></p>
                                                                            <div class="sep" style="margin: 4px 0 5px 0;"><img
                                                                                    src="${contexto}/images/shim.gif"></div>
                                                                        </div>
                                                                    </td>
                                                                </tr>
                                                                <tr>
                                                                    <td align="left" valign="top"><h:panelGrid
                                                                            id="dadosAluno" columns="1" columnClasses="left, right"
                                                                            width="100%" border="0" cellspacing="0" cellpadding="0"
                                                                            styleClass="textsmall">
                                                                            <h:panelGroup>
                                                                                <h:outputText style="font-weight: bold"
                                                                                              value="Saldo Conta Academia: " />
                                                                                <f:verbatim>
                                                                                    <h:outputText value="        " />
                                                                                </f:verbatim>
                                                                                <h:outputText style="font-weight: bold" styleClass="red"
                                                                                              value="R$ " />
                                                                                <a4j:commandLink style="font-weight: bold"
                                                                                                 styleClass="link_red"
                                                                                                 action="#{MovimentoContaCorrenteClienteControle.novo}"
                                                                                                 oncomplete="abrirPopup('faces/movimentoContaCorrenteClienteCons.jsp', 'MovimentoContaCorrenteCliente', 800, 595);">
                                                                                    <h:outputText
                                                                                        value="#{ClienteControle.clienteVO.saldoContaCorrente}">
                                                                                        <f:converter converterId="FormatadorNumerico" />
                                                                                    </h:outputText>
                                                                                </a4j:commandLink>
                                                                            </h:panelGroup>
                                                                            <h:panelGroup>
                                                                                <h:outputText style="font-weight: bold" value="Idade:" />
                                                                                <f:verbatim>
                                                                                    <h:outputText value="        " />
                                                                                </f:verbatim>
                                                                                <h:outputText style="font-weight: bold"
                                                                                              styleClass="blue"
                                                                                              value="#{ClienteControle.idadeCliente}" />
                                                                            </h:panelGroup>
                                                                            <h:panelGroup>
                                                                                <h:outputText style="font-weight: bold" value="Sexo biol�gico:" />
                                                                                <f:verbatim>
                                                                                    <h:outputText value="        " />
                                                                                </f:verbatim>
                                                                                <h:outputText style="font-weight: bold"
                                                                                              styleClass="blue"
                                                                                              value="#{ClienteControle.clienteVO.pessoa.sexo_Apresentar}" />
                                                                            </h:panelGroup>
                                                                            <h:panelGroup>
                                                                                <h:dataTable id="vinculos" width="100%"
                                                                                             rowClasses="textsmall" columnClasses="colunaEsquerda"
                                                                                             value="#{ClienteControle.clienteVO.vinculoVOs}"
                                                                                             var="vinculo">
                                                                                    <h:column>
                                                                                        <h:outputText style="font-weight: bold"
                                                                                                      value="#{vinculo.tipoVinculo_Apresentar}" />
                                                                                    </h:column>
                                                                                    <h:column>
                                                                                        <h:outputText style="font-weight: bold"
                                                                                                      styleClass="blue"
                                                                                                      value="#{vinculo.colaborador.pessoa.nome}" />
                                                                                    </h:column>
                                                                                </h:dataTable>
                                                                            </h:panelGroup>
                                                                        </h:panelGrid></td>
                                                                </tr>
                                                            </table>
                                                        </h:panelGroup>
                                                    </h:panelGrid>
                                                    <%--/div--%>

                                                    <%--div style="clear:both;"--%>
                                                    <h:panelGrid width="100%" cellspacing="5">
                                                        <table width="100%" border="0" align="left" cellpadding="0"
                                                               cellspacing="0" bgcolor="#e6e6e6" style="padding: 10px;">
                                                            <h:panelGrid width="100%">
                                                                <h:panelGrid columns="2" width="100%">
                                                                    <h:panelGroup>
                                                                        <img src="${contexto}/images/arrow2.gif" width="16" height="16"
                                                                             style="vertical-align: middle; margin-right: 6px;" />
                                                                        <h:outputText style="font-weight: bold" styleClass="text"
                                                                                      value="Contrato" />
                                                                    </h:panelGroup>
                                                                    <h:panelGroup>
                                                                        <a4j:commandButton reRender="listaContrato"
                                                                                           id="listarTodoContratos"
                                                                                           image="/imagens/botaoListarTodosContratos.png"
                                                                                           alt="Listar Todos Contratos"
                                                                                           action="#{ClienteControle.listarTodoContratos}" />
                                                                    </h:panelGroup>
                                                                </h:panelGrid>
                                                                <div class="sep" style="margin: 4px 0 5px 0;"><img
                                                                        src="${contexto}/images/shim.gif"></div>
                                                                </h:panelGrid>
                                                            <tr>
                                                                <td align="left" valign="top"><rich:dataTable
                                                                        id="listaContrato" width="100%" border="0"
                                                                        rows="#{ClienteControle.tamanhoListaContrato}"
                                                                        cellspacing="0" cellpadding="10" styleClass="textsmall"
                                                                        columnClasses="centralizado, centralizado, centralizado, centralizado, centralizado, colunaDireita"
                                                                        rendered="#{!empty ClienteControle.listaContratos}"
                                                                        value="#{ClienteControle.listaContratos}"
                                                                        var="contrato">
                                                                        <rich:column>
                                                                            <f:facet name="header">
                                                                                <h:outputText style="font-weight: bold" value="Contrato" />
                                                                            </f:facet>
                                                                            <h:outputText style="font-weight: bold" styleClass="blue"
                                                                                          value="#{contrato.codigo}" />
                                                                        </rich:column>
                                                                        <rich:column>
                                                                            <f:facet name="header">
                                                                                <h:outputText style="font-weight: bold"
                                                                                              value="Data Lan�amento" />
                                                                            </f:facet>
                                                                            <h:outputText style="font-weight: bold" styleClass="blue"
                                                                                          value="#{contrato.dataLancamento_Apresentar}" />
                                                                        </rich:column>
                                                                        <rich:column>
                                                                            <f:facet name="header">
                                                                                <h:outputText style="font-weight: bold"
                                                                                              value="Data In�cio" />
                                                                            </f:facet>
                                                                            <h:outputText style="font-weight: bold" styleClass="blue"
                                                                                          value="#{contrato.vigenciaDe}">
                                                                                <f:convertDateTime pattern="dd/MM/yyyy" />
                                                                            </h:outputText>
                                                                        </rich:column>
                                                                        <rich:column>
                                                                            <f:facet name="header">
                                                                                <h:outputText style="font-weight: bold"
                                                                                              value="Data T�rmino" />
                                                                            </f:facet>

                                                                            <h:outputText style="font-weight: bold" styleClass="red"
                                                                                          value="#{contrato.vigenciaAteAjustada}">
                                                                                <f:convertDateTime pattern="dd/MM/yyyy" />
                                                                            </h:outputText>

                                                                        </rich:column>
                                                                        <rich:column>
                                                                            <f:facet name="header">
                                                                                <h:outputText style="font-weight: bold"
                                                                                              value="Respons�vel" />
                                                                            </f:facet>
                                                                            <h:outputText style="font-weight: bold" styleClass="blue"
                                                                                          value="#{contrato.responsavelContrato.nomeAbreviado}" />
                                                                        </rich:column>
                                                                        <%--
                                <rich:column>
                                    <f:facet name="header">
                                        <h:outputText  style="font-weight: bold" value="Dura��o"/>
                                    </f:facet>
                                    <h:outputText style="font-weight: bold" styleClass="blue" value="#{contrato.contratoDuracao.numeroMeses}"/>
                                </rich:column>--%>
                                                                        <rich:column>
                                                                            <f:facet name="header">
                                                                                <h:outputText style="font-weight: bold" value="Op��es" />
                                                                            </f:facet>
                                                                            <a4j:commandButton id="renovarContrato"
                                                                                               rendered="#{contrato.renovarContrato}"
                                                                                               alt="Renovar Contrato"
                                                                                               image="/imagens/botaoRenovar.png"
                                                                                               oncomplete="#{ClienteControle.mensagemNotificar}"
                                                                                               action="#{ClienteControle.gerarOutroContratoApartirDoContratoMatricula}" />
                                                                            <a4j:commandButton id="rematricularContrato"
                                                                                               rendered="#{contrato.rematricularContrato}"
                                                                                               alt="Rematricular Contrato"
                                                                                               image="/imagens/botaoRematricular.png"
                                                                                               oncomplete="#{ClienteControle.mensagemNotificar}"
                                                                                               action="#{ClienteControle.gerarOutroContratoApartirDoContratoMatricula}" />
                                                                            <rich:spacer width="5px" />
                                                                            <a4j:commandButton id="imprimir"
                                                                                               value="Imprimir Contrato" image="/imagens/imprimir.png"
                                                                                               action="#{ClienteControle.imprimirContrato}"
                                                                                               oncomplete="abrirPopup('faces/VisualizarContrato', 'RelatorioContrato', 730, 545);" />
                                                                            <rich:spacer width="5px" />
                                                                            <h:commandButton id="visualisarDadosCompletosContrato"
                                                                                             image="/imagens/botaoVisualizar.png"
                                                                                             alt="Ver Detalhes do Contrato"
                                                                                             action="#{ClienteControle.selecionarDadosContrato}">
                                                                                <a4j:support event="oncomplete" ajaxSingle="true"
                                                                                             reRender="panelOperacoesContrato,richPanel" />
                                                                            </h:commandButton>
                                                                            <rich:spacer width="5px" />

                                                                            <h:graphicImage value="/imagens/botaoAtivo.png"
                                                                                            rendered="#{contrato.contratoAtivo}" width="25"
                                                                                            height="24" />
                                                                            <h:graphicImage value="imagens/botaoCancelamento.png"
                                                                                            rendered="#{contrato.contratoCancelado}" width="25"
                                                                                            height="24" />
                                                                            <h:graphicImage value="/imagens/botaoTrancamento.png"
                                                                                            rendered="#{contrato.contratoTrancado}" width="25"
                                                                                            height="24" />
                                                                            <h:graphicImage value="/imagens/botaoInativo.png"
                                                                                            rendered="#{contrato.contratoInativo}" width="25"
                                                                                            height="24" />
                                                                            <h:graphicImage value="/imagens/botaoRenovado.png"
                                                                                            rendered="#{contrato.apresentarBotaoRenovarContrato}"
                                                                                            width="25" height="24" />

                                                                        </rich:column>
                                                                    </rich:dataTable></td>
                                                            </tr>

                                                        </table>
                                                    </h:panelGrid>
                                                    <%--/div--%>

                                                    <%--div style="clear:both;"--%>
                                                    <h:panelGrid columns="2" width="100%"
                                                                 columnClasses="colunaCentralizada" cellspacing="5">
                                                        <h:panelGroup>
                                                            <table width="100%" border="0" align="left" cellpadding="0"
                                                                   cellspacing="0" bgcolor="#e6e6e6" style="padding: 10px;">
                                                                <tr>
                                                                    <td align="left" valign="top" style="padding-bottom: 5px;">
                                                                        <div style="clear: both;" class="text">
                                                                            <p style="margin-bottom: 6px;"><img
                                                                                    src="${contexto}/images/arrow2.gif" width="16" height="16"
                                                                                    style="vertical-align: middle; margin-right: 6px;">
                                                                                <h:outputText style="font-weight: bold" value="Acesso" /></p>
                                                                            <div class="sep" style="margin: 4px 0 5px 0;"><img
                                                                                    src="${contexto}/images/shim.gif"></div>
                                                                        </div>
                                                                    </td>
                                                                </tr>
                                                                <tr>
                                                                    <td align="left" valign="top" style="padding-bottom: 5px;">
                                                                        <rich:dataTable
                                                                            id="dadosUltimoAcesso" width="100%" border="0"
                                                                            cellspacing="0" cellpadding="10" styleClass="textsmall"
                                                                            columnClasses="centralizado, centralizado, centralizado, centralizado, centralizado, colunaDireita"
                                                                            rendered="#{!empty ClienteControle.clienteVO.situacaoClienteSinteticoVO}"
                                                                            value="#{ClienteControle.clienteVO.situacaoClienteSinteticoVO}"
                                                                            var="situacao">
                                                                            <rich:column rendered="#{!empty ClienteControle.clienteVO.situacaoClienteSinteticoVO.dataUltimoAcesso}">
                                                                                <f:facet name="header">
                                                                                    <h:outputText style="font-weight: bold" value="�ltimo Acesso" />
                                                                                </f:facet>
                                                                                <f:verbatim>
                                                                                    <h:outputText style="font-weight: bold" styleClass="blue"
                                                                                                  value="#{situacao.dataUltimoAcesso}">
                                                                                        <f:convertDateTime type="date" dateStyle="short"
                                                                                                           locale="pt"
                                                                                                           pattern="dd/MM/yyyy HH:mm" />
                                                                                    </h:outputText>
                                                                                    -
                                                                                    <h:outputText
                                                                                        style="font-weight: bold" styleClass="blue"
                                                                                        value="#{ClienteControle.clienteVO.situacaoClienteSinteticoVO.nomeDiaSemanaDataUltimoAcesso}" />
                                                                                </f:verbatim>
                                                                            </rich:column>

                                                                            <rich:column rendered="#{!empty ClienteControle.clienteVO.situacaoClienteSinteticoVO.diasAcessoSemanaPassada}">
                                                                                <f:facet name="header">
                                                                                    <h:outputText style="font-weight: bold" value="Q.A 1" />
                                                                                </f:facet>
                                                                                <f:verbatim>
                                                                                    <h:outputText style="font-weight: bold" styleClass="blue"
                                                                                                  value="#{ClienteControle.clienteVO.situacaoClienteSinteticoVO.diasAcessoSemanaPassada}">

                                                                                    </h:outputText>
                                                                                </f:verbatim>
                                                                            </rich:column>
                                                                            <rich:column rendered="#{!empty ClienteControle.clienteVO.situacaoClienteSinteticoVO.diasAcessoSemana2}">
                                                                                <f:facet name="header">
                                                                                    <h:outputText style="font-weight: bold" value="Q.A 2" />
                                                                                </f:facet>
                                                                                <f:verbatim>
                                                                                    <h:outputText style="font-weight: bold" styleClass="blue"
                                                                                                  value="#{ClienteControle.clienteVO.situacaoClienteSinteticoVO.diasAcessoSemana2}">

                                                                                    </h:outputText>
                                                                                </f:verbatim>
                                                                            </rich:column>
                                                                            <rich:column rendered="#{!empty ClienteControle.clienteVO.situacaoClienteSinteticoVO.diasAcessoSemana3}">
                                                                                <f:facet name="header">
                                                                                    <h:outputText style="font-weight: bold" value="Q.A 3" />
                                                                                </f:facet>
                                                                                <f:verbatim>
                                                                                    <h:outputText style="font-weight: bold" styleClass="blue"
                                                                                                  value="#{ClienteControle.clienteVO.situacaoClienteSinteticoVO.diasAcessoSemana3}">

                                                                                    </h:outputText>
                                                                                </f:verbatim>
                                                                            </rich:column>
                                                                            <rich:column rendered="#{!empty ClienteControle.clienteVO.situacaoClienteSinteticoVO.diasAcessoSemana4}">
                                                                                <f:facet name="header">
                                                                                    <h:outputText style="font-weight: bold" value="Q.A 4" />
                                                                                </f:facet>
                                                                                <f:verbatim>
                                                                                    <h:outputText style="font-weight: bold" styleClass="blue"
                                                                                                  value="#{ClienteControle.clienteVO.situacaoClienteSinteticoVO.diasAcessoSemana4}">

                                                                                    </h:outputText>
                                                                                </f:verbatim>
                                                                            </rich:column>
                                                                        </rich:dataTable>
                                                                        <h:outputLink value="#{SuperControle.urlWiki}Zillyon:Siglas#QA"
                                                                                      title="Clique e saiba mais: Quantidade Acessos" target="_blank">
                                                                            <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                                                                            <h:outputText value="Q.A = Quantidade Acessos: 1�, 2�, 3� e 4� �ltimas semanas"/>
                                                                        </h:outputLink>
                                                                    </td>
                                                                </tr>
                                                            </table>
                                                        </h:panelGroup>
                                                        <h:panelGroup>
                                                            <table width="100%" border="0" align="left" cellpadding="0"
                                                                   cellspacing="0" bgcolor="#e6e6e6" style="padding: 10px;">
                                                                <tr>
                                                                    <td align="left" valign="top" style="padding-bottom: 5px;">
                                                                        <div style="clear: both;" class="text">
                                                                            <p style="margin-bottom: 6px;"><img
                                                                                    src="${contexto}/images/arrow2.gif" width="16" height="16"
                                                                                    style="vertical-align: middle; margin-right: 6px;">
                                                                                <h:outputText style="font-weight: bold"
                                                                                              value="Produtos com Validade" /></p>
                                                                            <div class="sep" style="margin: 4px 0 5px 0;"><img
                                                                                    src="${contexto}/images/shim.gif"></div>
                                                                        </div>
                                                                    </td>
                                                                </tr>
                                                                <tr>
                                                                    <td align="left" valign="top"><rich:dataTable
                                                                            id="listaProdutoComValidade" width="100%" border="0"
                                                                            rows="#{ClienteControle.nrPaginaMovProdutoValidade}"
                                                                            cellspacing="0" cellpadding="10" styleClass="textsmall"
                                                                            columnClasses="centralizado, centralizado, centralizado "
                                                                            value="#{ClienteControle.clienteVO.listaProdutosComValidade}"
                                                                            var="movProduto">
                                                                            <rich:column>
                                                                                <f:facet name="header">
                                                                                    <h:outputText style="font-weight: bold" value="Produto" />
                                                                                </f:facet>
                                                                                <h:outputText style="font-weight: bold"
                                                                                              styleClass="blue"
                                                                                              value="#{movProduto.produto.descricao}" />
                                                                            </rich:column>
                                                                            <rich:column>
                                                                                <f:facet name="header">
                                                                                    <h:outputText style="font-weight: bold"
                                                                                                  value="Data Compra" />
                                                                                </f:facet>
                                                                                <h:outputText style="font-weight: bold"
                                                                                              styleClass="blue"
                                                                                              value="#{movProduto.dataLancamento_Apresentar}" />
                                                                            </rich:column>
                                                                            <rich:column>
                                                                                <f:facet name="header">
                                                                                    <h:outputText style="font-weight: bold"
                                                                                                  value="Data Final Vig�ncia" />
                                                                                </f:facet>
                                                                                <h:outputText style="font-weight: bold" styleClass="red"
                                                                                              value="#{movProduto.dataFinalVigencia_Apresentar}" />
                                                                            </rich:column>
                                                                        </rich:dataTable> <h:panelGrid columns="1"
                                                                                                       columnClasses="colunaCentralizada" width="100%">
                                                                            <h:panelGroup>
                                                                                <h:panelGrid columns="2">
                                                                                    <rich:datascroller align="center"
                                                                                                       for="listaProdutoComValidade" maxPages="100"
                                                                                                       id="scResultadoListaProdutoComValidade" />
                                                                                    <rich:inputNumberSpinner inputSize="4"
                                                                                                             styleClass="form" enableManualInput="true"
                                                                                                             minValue="1" maxValue="100"
                                                                                                             value="#{ClienteControle.nrPaginaMovProdutoValidade}">
                                                                                        <a4j:support event="onchange"
                                                                                                     focus="scResultadoListaProdutoComValidade"
                                                                                                     reRender="listaProdutoComValidade,scResultadoListaProdutoComValidade" />
                                                                                    </rich:inputNumberSpinner>
                                                                                </h:panelGrid>
                                                                            </h:panelGroup>
                                                                        </h:panelGrid></td>
                                                                </tr>
                                                            </table>
                                                        </h:panelGroup>
                                                    </h:panelGrid>	
	
	
		</h:panelGrid>
	</h:form>
</rich:modalPanel>

<%@include file="include_imports.jsp" %>

<script>
    function update(msg) {
        var mensagem = document.getElementById('form:msg');
        var msgDetalhada = document.getElementById('form:msgDetalhada');
        mensagem.innerHTML = "";
        msgDetalhada.innerHTML = "";

        msgDetalhada.innerHTML = msg;
    }
</script>

<center>
    <h:panelGroup id="mensagens">	

        <h:outputText id="msg" styleClass="mensagem" value="#{CadastroInicialControle.mensagem}" />&nbsp;&nbsp;&nbsp;
        <h:outputText id="msgDetalhada" styleClass="mensagemDetalhada" value="#{CadastroInicialControle.mensagemDetalhada}" />
        &nbsp;&nbsp;

    </h:panelGroup>	
</center>
<table width="100%">

    <tr><td>
            <h:panelGroup id="painelBotoes">
                <table width="100%" border="0" align="center" cellpadding="0"
                       cellspacing="0">
                    <tr>
                        <td width="10"><img
                                src="${root}/imagens/agenda_imgs/menu_esq.jpg" width="10"
                                height="69"></td>
                        <td background="${root}/imagens/agenda_imgs/menu_fundo.jpg">
                            <table width="100%" border="0" cellspacing="0" cellpadding="0">
                                <tr>
                                    <td align="center"><h:commandLink id="orcamentoDetalhado"
                                                   action="#{NavegacaoControle.abrirTelaOrcamentoDetalhado}"
                                                   actionListener="#{OrcamentoDetalhadoControle.selEventoInteresseListener}"
                                                   rendered="#{CadastroInicialControle.eventoCadastrado}">
                                            <h:graphicImage value="/imagens/bt_orcamentodetalhado.jpg"
                                                            style="border: 0px;"
                                                            alt="#{CElabels['menu.operacoesCE.exibicaoOrcamento']}"/>
                                            <f:attribute name="codigoEventoInteresse"
                                                         value="#{CadastroInicialControle.cadastro.codigo}" />
                                            <f:attribute name="dataInteresse"
                                                         value="#{CadastroInicialControle.cadastro.dataInicial}" />
                                            <f:attribute name="ambienteInteresse"
                                                         value="#{CadastroInicialControle.ambiente.codigo}" />
                                            <f:attribute name="perfilInteresse"
                                                         value="#{CadastroInicialControle.cadastro.codigoPerfilEvento}" />
                                        </h:commandLink></td>
                                    <td align="center"><h:commandLink id="terminarCadastro"
                                                   action="#{CadastroInicialControle.terminarCadastro}"
                                                   rendered="#{CadastroInicialControle.eventoPendente}">
                                            <h:graphicImage value="/imagens/bt_terminarcadastro.jpg"
                                                            style="border: 0px;"
                                                            alt="#{CElabels['operacoes.cadastro.terminar']}" />
                                        </h:commandLink>
                                    </td>

                                    <td align="center"><h:commandButton id="editarCadastro"
                                                     image="/imagens/botoesCE/Editar_cadastro.png"
                                                     action="#{CadastroInicialControle.editarCadastro}"
                                                     rendered="#{CadastroInicialControle.eventoCadastrado}"
                                                     value="#{CElabels['operacoes.cadastro.editar']}">							
                                        </h:commandButton>
                                    </td>
                                    <td align="center">
                                        <a4j:commandButton id="encerrarEvento"
                                                           rendered="#{CadastroInicialControle.permiteEncerramento}"
                                                           oncomplete="Richfaces.showModalPanel('panelTextoEncerramento');"
                                                           actionListener="#{OrcamentoDetalhadoControle.selEventoExibeListener}"
                                                           reRender="mensagens"
                                                           image="/imagens/botoesCE/encerrar_evento.png"
                                                           value="#{CElabels['operacoes.cadastro.encerrarEvento']}">							
                                            <f:attribute name="codigoEventoInteresse" value="#{CadastroInicialControle.cadastro.codigo}" />

                                        </a4j:commandButton>
                                    </td>
                                    <td align="center"><h:commandLink id="exibeOrcamento"
                                                   action="#{OrcamentoDetalhadoControle.abrirOrcamentoDetalhado}"
                                                   rendered="#{CadastroInicialControle.eventoOrcado}"
                                                   actionListener="#{OrcamentoDetalhadoControle.selEventoExibeListener}">
                                            <h:graphicImage value="/imagens/bt_orcamentodetalhado.jpg"
                                                            style="border: 0px;"
                                                            alt="#{CElabels['menu.operacoesCE.exibicaoOrcamento']}"/>
                                            <f:attribute name="codigoEventoInteresse"
                                                         value="#{CadastroInicialControle.cadastro.codigo}" />
                                        </h:commandLink></td>
                                    <td align="center">

                                        <a4j:commandLink id="consultarConversa"
                                                         actionListener="#{ConversaControle.selEventoConversaListener}"
                                                         action="#{ConversaControle.abrirConversaComInteressado}">
                                            <h:graphicImage value="/imagens/bt_conversas.jpg" style="border: 0px;" alt="#{CElabels['operacoes.conversas']}"/>
                                            <f:attribute name="codigoEvento"
                                                         value="#{CadastroInicialControle.cadastro.codigo}" />
                                            <f:attribute name="codigoInteressado"
                                                         value="#{CadastroInicialControle.cadastro.codigoInteressado}" />
                                        </a4j:commandLink></td>
                                    <td align="center"><h:commandLink id="emissaoDocumentos"
                                                   action="#{EmissaoDocumentosControle.abrirEmissaoDocumentos}"
                                                   rendered="#{CadastroInicialControle.eventoNegociado}"
                                                   actionListener="#{EmissaoDocumentosControle.selEventoInteresseListener}">
                                            <h:graphicImage value="/imagens/bt_emissaodedocumentos.jpg"
                                                            style="border: 0px;"
                                                            alt="#{CElabels['menu.operacoesCE.emissaoDocumentos']}"/>
                                            <f:attribute name="codigoEventoInteresse"
                                                         value="#{CadastroInicialControle.cadastro.codigo}" />
                                        </h:commandLink></td>
                                    <td align="center">
                                        <h:panelGroup id="conteudo" rendered="#{CadastroInicialControle.eventoReservado}">
                                            <a onmouseover="if (document.getElementById('hint-verificar').style.visibility != 'hidden') {
            toolTip('<h:outputText value="#{CElabels['entidade.detalhamentoEvento.hint.fecharNegociacao']}"/>', 300, 'blue')
        }"
                                               onmouseout="hideToolTip();"><img src="${root}/pages/ce/img/hint.gif" width="15px"
                                                                             id="hint-verificar" style="visibility: hidden;" height="15px" border="0px;" /></a>&nbsp;&nbsp;&nbsp;
                                                <a4j:commandButton id="fechaNegociacao"
                                                                   image="/imagens/botoesCE/fechar_negociacao.png"
                                                                   reRender="panelUsuarioSenha, panelClienteInexistente"
                                                                   value="#{CElabels['operacoes.negociacao.fechar']}"
                                                                   alt="#{CElabels['operacoes.negociacao.fechar']}"
                                                                   styleClass="botoes"
                                                                   actionListener="#{OrcamentoDetalhadoControle.selFecharNegociacao}">
                                                    <f:attribute name="codigoEvento"
                                                                 value="#{CadastroInicialControle.cadastro.codigo}" />
                                                </a4j:commandButton>
                                            </h:panelGroup>
                                    </td>
                                    <c:if test="${CadastroInicialControle.nrBotoes eq 9}">
                                    </tr>
                                </table>
                                <table width="100%" border="0" cellspacing="0" cellpadding="0">
                                    <tr>
                                    </c:if>
                                    <td align="center">
                                        <a4j:commandButton id="agendarContato"
                                                           image="/imagens/botoesCE/agendar_contato.png"
                                                           value="#{CElabels['operacoes.atualizarProxContato']}"
                                                           styleClass="botoes"
                                                           oncomplete="Richfaces.showModalPanel('panelTextoAgendarContato');"
                                                           >
                                        </a4j:commandButton>
                                    </td>


                                    <c:if test="${CadastroInicialControle.nrBotoes gt 5 and CadastroInicialControle.nrBotoes lt 9}">
                                    </tr>
                                </table>
                                <table width="100%" border="0" cellspacing="0" cellpadding="0">
                                    <tr>
                                    </c:if>

                                    <td align="center">
                                        <a4j:commandButton id="remarcarPreReserva"
                                                           image="/imagens/botoesCE/atualizar_pre_reserva.png"
                                                           rendered="#{CadastroInicialControle.eventoReservado}"
                                                           value="#{CElabels['operacoes.atualizarPreReserva']}"
                                                           alt="#{CElabels['operacoes.atualizarPreReserva']}"
                                                           styleClass="botoes"
                                                           oncomplete="Richfaces.showModalPanel('panelTextoRemarcarPreReserva');"
                                                           >
                                        </a4j:commandButton>
                                    </td>

                                    <td align="center"><h:commandLink id="cancelarEvento"
                                                   action="#{NavegacaoControle.abrirTelaCancelamentoEvento}"
                                                   actionListener="#{CancelamentoEventoControle.selEventoListener}">
                                            <h:graphicImage value="/imagens/bt_cancelamentodeevento.jpg"
                                                            style="border: 0px;"
                                                            alt="#{CElabels['operacoes.negociacao.fechar']}"/>
                                            <f:attribute name="codigoEvento"
                                                         value="#{CadastroInicialControle.cadastro.codigo}" />
                                        </h:commandLink></td>

                                    <td align="center">	
                                        <h:commandButton rendered="#{CadastroInicialControle.eventoParcelado}"
                                                         image="/imagens/botoesCE/pagamentos.png"
                                                         action="#{NavegacaoControle.abrirTelaCaixaEmAberto}"
                                                         actionListener="#{ProspectsControle.selEventoInteresseListener}" 
                                                         onmouseover="toolTip('Parcelas' , 120 , 'gray')"
                                                         onmouseout="hideToolTip();"
                                                         value="#{CElabels['menu.operacoesCE.pagamentos']}">							
                                            <f:attribute name="codigoEventoInteresse" value="#{CadastroInicialControle.cadastro.codigo}" />
                                        </h:commandButton>
                                    </td>

                                    <td align="center">
                                        <a4j:commandButton id="autorizarEvento"
                                                           image="/imagens/botoesCE/autorizar_evento.png"
                                                           rendered="#{CadastroInicialControle.eventoConfirmado}"
                                                           value="Autorizar Evento"
                                                           alt="Autorizar Evento"
                                                           styleClass="botoes"
                                                           actionListener="#{OrcamentoDetalhadoControle.selCarregarNegociacaoPorEvento}"
                                                           oncomplete="Richfaces.showModalPanel('panelAutorizarEvento');">

                                            <f:attribute name="codigoEvento" value="#{CadastroInicialControle.cadastro.codigo}" />
                                        </a4j:commandButton>														
                                    </td>

                                    <td align="center">
                                        <h:panelGroup rendered="#{CadastroInicialControle.eventoOrcado}">
                                            <h:graphicImage value="/imagens/bt_ajuda.jpg" style="border: 0px;" onclick="ativarAjuda('verificar');"/>
                                        </h:panelGroup>
                                    </td>	
                                </tr>					
                            </table>
                        </td>
                        <td width="10"><img
                                src="${root}/imagens/agenda_imgs/menu_dir.jpg" width="10"
                                height="69"></td>
                    </tr>
                </table>
            </h:panelGroup>
        </td></tr>
    <tr><td>
            <h:inputHidden value="#{CadastroInicialControle.pessoa.codigo}" id="codigoPessoa" />
            <rich:separator width="100%" lineType="solid"></rich:separator>
        <center>
            <table>
                <tr>
                    <td><h:outputText style="font-size: 18px; font-weight: bold;" value="#{CElabels['entidade.interessado.status.evento']}:"/></td>
                <td><h:outputText style="font-size: 18px;" value="#{CadastroInicialControle.cadastro.situacao.descricao}"/></td>
                <td><h:outputText style="font-size: 20px; font-weight: bold;" value=", "/>&nbsp;&nbsp;&nbsp;</td>
                <td><h:outputText rendered="true" style="font-size: 18px; font-weight: bold;" value="#{CElabels['entidade.ambiente']}:"/></td>
                <td>
                    <h:outputText rendered="true" style="font-size: 18px;" value="#{CadastroInicialControle.descricaoAmbientes}"></h:outputText>					
                    </td>
                <c:if test="${CadastroInicialControle.eventoNegociado}">
                    <td><h:outputText style="font-size: 20px; font-weight: bold;" value=", "/>&nbsp;&nbsp;&nbsp;</td>
                    <td><h:outputText style="font-size: 18px; font-weight: bold;"  value="#{CElabels['entidade.evento.data']}:"/></td>
                    <td><h:outputText styleClass="textoVermelhoGrande" value="#{CadastroInicialControle.cadastro.dataFormatada}"></h:outputText></td>
                </c:if>
                <td></td>
            </tr>
        </table>
    </center>
    <rich:separator width="100%" lineType="solid"></rich:separator>

        <center>
        <h:outputText style="font-size: 18px; font-weight: bold;" value="#{CadastroInicialControle.cadastro.msgDiasParaEvento}"></h:outputText>
        </center>

        <!-- inicio da exibi��o dos detalhes do evento  -->		
        <table width="100%" >
            <tr>
                <td>
                <h:panelGrid headerClass="subordinado" rowClasses="linhaImpar, linhaPar"
                             columnClasses="classEsquerdaCinzaCE, classDireitaCE"
                             width="100%" columns="2" >
                    <f:facet name="header">
                        <h:outputText value="#{CElabels['menu.operacoesCE.cadastroInicial']}" />
                    </f:facet>
                    <h:panelGroup>
                        <h:outputText styleClass="tituloCampos" value="#{CElabels['entidade.codigo']}" />
                    </h:panelGroup>
                    <h:panelGroup>
                        <h:outputText value="#{CadastroInicialControle.cadastro.codigo}"></h:outputText>
                    </h:panelGroup>	
                    <h:panelGroup>
                        <h:outputText styleClass="tituloCampos" value="#{CElabels['entidade.dataInteresse']}" />
                    </h:panelGroup>
                    <h:panelGroup>
                        <h:outputText styleClass="textoAzul" value="#{CadastroInicialControle.cadastro.dataFormatada}"></h:outputText>
                    </h:panelGroup>	


                    <h:panelGroup>
                        <h:outputText styleClass="tituloCampos" value="Nome do Evento" />
                    </h:panelGroup>
                    <h:panelGroup>
                        <h:outputText value="#{CadastroInicialControle.cadastro.nomeEvento}"></h:outputText>
                            <div id="divObg-dataInicio" class="mensagemObrigatorio"></div>
                    </h:panelGroup>	

                    <h:panelGroup>
                        <h:outputText styleClass="tituloCampos" value="#{CElabels['entidade.perfilEventoInteresse']}" />
                    </h:panelGroup>
                    <h:panelGroup>
                        <h:outputText styleClass="textoAzul" value="#{CadastroInicialControle.cadastro.nomePerfil}"></h:outputText>
                    </h:panelGroup>	

                    <h:panelGroup>
                        <h:outputText styleClass="tituloCampos" value="Nome do Cliente" />
                    </h:panelGroup>
                    <h:panelGroup>
                        <h:outputText 
                            value="#{CadastroInicialControle.pessoa.nomeCompleto}"
                            id="nomeCliente"></h:outputText>
                    </h:panelGroup>
                    <h:panelGroup>
                        <h:outputText styleClass="tituloCampos"
                                      value="Quem � o cliente no evento" />
                    </h:panelGroup>
                    <h:panelGroup>
                        <h:outputText 
                            value="#{CadastroInicialControle.cadastro.quemE}" id="quemE"></h:outputText>
                    </h:panelGroup>
                    <h:panelGroup>
                        <h:outputText styleClass="tituloCampos" value="Telefone Celular" />
                    </h:panelGroup>
                    <h:panelGroup>
                        <h:outputText 
                            value="#{CadastroInicialControle.pessoa.telefoneCelular}"
                            id="telefone_celular"></h:outputText>
                    </h:panelGroup>
                    <h:panelGroup>
                        <h:outputText styleClass="tituloCampos" value="Telefone Comercial" />
                    </h:panelGroup>
                    <h:panelGroup>
                        <h:outputText 
                            value="#{CadastroInicialControle.pessoa.telefoneComercial}"
                            id="telefone_comercial"></h:outputText>
                    </h:panelGroup>
                    <h:panelGroup>
                        <h:outputText styleClass="tituloCampos" value="Telefone Fixo" />
                    </h:panelGroup>
                    <h:panelGroup>
                        <h:outputText 
                            value="#{CadastroInicialControle.pessoa.telefoneFixo}"
                            id="telefone_fixo"></h:outputText>
                    </h:panelGroup>
                    <h:panelGroup>
                        <h:outputText styleClass="tituloCampos" value="Email" />
                    </h:panelGroup>
                    <h:panelGroup>
                        <h:outputText 
                            value="#{CadastroInicialControle.pessoa.email}" id="email"></h:outputText>
                    </h:panelGroup>

                    <h:panelGroup>
                        <h:outputText styleClass="tituloCampos" value="N�mero de Convidados" />
                    </h:panelGroup>			
                    <h:panelGroup >
                        <h:outputText styleClass="textoAzul"
                                      value="#{CadastroInicialControle.cadastro.nrConvidados}" />
                    </h:panelGroup>

                    <h:panelGroup>
                        <h:outputText styleClass="tituloCampos"
                                      value="Como conheceu a Ipanema?" />
                    </h:panelGroup>
                    <h:panelGroup>
                        <h:outputText  id="ComoConheceu"
                                       value="#{CadastroInicialControle.cadastro.comoConheceu}" />
                    </h:panelGroup>
                    <h:panelGroup>
                        <h:outputText styleClass="tituloCampos" value="Quem indicou?" />
                    </h:panelGroup>
                    <h:panelGroup>
                        <h:outputText 
                            value="#{CadastroInicialControle.cadastro.quemIndicou}"
                            id="quemIndicou"></h:outputText>
                    </h:panelGroup>
                    <h:panelGroup>
                        <h:outputText styleClass="tituloCampos"
                                      value="Qual a forma de contato?" />
                    </h:panelGroup>
                    <h:panelGroup>
                        <h:outputText  id="formaContato"
                                       value="#{CadastroInicialControle.cadastro.formaContato}" />
                    </h:panelGroup>
                    <h:panelGroup>
                        <h:outputText styleClass="tituloCampos" value="Observa��es:" />
                    </h:panelGroup>
                    <h:panelGroup>
                        <h:outputText 
                            value="#{CadastroInicialControle.cadastro.observacoes}"
                            id="observacoes" escape="false" />
                    </h:panelGroup>
                </h:panelGrid>
            </td>			
            <c:if test="${CadastroInicialControle.eventoNegociado}">
                <td valign="top"><h:panelGrid rowClasses="linhaImpar, linhaPar"  headerClass="subordinado" 
                             columnClasses="classEsquerdaCinzaCE, classDireitaCE" width="100%"
                             columns="2" style="vertical-align: top;">
                        <f:facet name="header">
                            <h:outputText value="#{CElabels['menu.operacoesCE.operacoes.orcamento']}" />
                        </f:facet>

                        <!-- perfil do evento  -->
                        <h:panelGroup>
                            <h:outputText styleClass="tituloCampos" value="#{CElabels['entidade.perfilEvento']}" />
                        </h:panelGroup>
                        <h:panelGroup>
                            <h:outputText styleClass="textoVermelho" value="#{CadastroInicialControle.dadosNegociacao.perfilEventoTO.descricao}" />
                        </h:panelGroup>
                        <h:panelGroup>
                            <h:outputText styleClass="tituloCampos" value="Valor: " />
                        </h:panelGroup>
                        <h:panelGroup>
                            <h:outputText styleClass="textoVermelho" value="#{CadastroInicialControle.dadosNegociacao.valorTotalMonetario}" />
                        </h:panelGroup>
                        <!-- horario -->				
                        <h:panelGroup>
                            <h:outputText styleClass="tituloCampos" value="#{CElabels['entidade.horario']}"  />
                        </h:panelGroup>
                        <h:panelGroup>
                            <h:outputText styleClass="textoVermelho" value="#{CadastroInicialControle.dadosNegociacao.horarioInicialString} - #{CadastroInicialControle.dadosNegociacao.horarioFinalExibicaoString}"/>
                        </h:panelGroup>
                        <!-- numero conv -->

                        <f:facet name="footer">
                            <h:panelGroup >
                                <h:dataTable id="ambientesSelecionados" rowClasses="linhaImpar, linhaPar"  headerClass="subordinado"  columnClasses="classEsquerdaCinzaCE, classDireitaCE" 
                                             width="100%" border="0" value="#{CadastroInicialControle.dadosNegociacao.ambientes}" var="ambiente">

                                    <rich:column>
                                        <h:outputText styleClass="tituloCampos" value="#{CElabels['entidade.nrConv']}" />
                                    </rich:column>

                                    <rich:column>
                                        <h:outputText styleClass="textoVermelho" value="#{ambiente.nrMaximoConvidado} para o ambiente #{ambiente.descricaoAmbiente}" />												
                                    </rich:column>

                                </h:dataTable>
                            </h:panelGroup>
                        </f:facet>



                        <!-- observacao no evento -->				
                        <h:panelGroup>
                            <h:outputText styleClass="tituloCampos" value="#{CElabels['entidade.observacaoOrcamento']}" />
                        </h:panelGroup>
                        <h:panelGroup>
                            <h:outputText escape="false" value="#{CadastroInicialControle.dadosNegociacao.textoLivre}" />
                        </h:panelGroup>
                    </h:panelGrid>
                    <br/>
                    <!-- outros dados -->			
                    <h:panelGrid rowClasses="linhaImpar, linhaPar"  headerClass="subordinado" 
                                 columnClasses="classEsquerdaCinzaCE, classDireitaCE" width="100%"
                                 columns="2" style="vertical-align: top;">
                        <f:facet name="header">
                            <h:outputText value="#{CElabels['menu.outrosDados']}" />
                        </f:facet>
                        <!-- DADOS DO ENCERRAMENTO DO EVENTO -->
                        <!-- atendente do evento-->				
                        <h:panelGroup>
                            <h:outputText styleClass="tituloCampos" value="#{CElabels['entidade.atendente']}" />
                        </h:panelGroup>
                        <h:panelGroup>
                            <h:outputText styleClass="textoVermelho" value="#{CadastroInicialControle.dadosNegociacao.atendente.nome}" />
                        </h:panelGroup>

                        <!-- data do encerramento -->				
                        <h:panelGroup>
                            <h:outputText styleClass="tituloCampos" value="#{CElabels['entidade.data.encerramento']}" />
                        </h:panelGroup>
                        <h:panelGroup>
                            <h:outputText styleClass="textoVermelho" value="#{CadastroInicialControle.cadastro.dataEncerramentoFormatada}" />
                        </h:panelGroup>

                        <!-- atendente do encerramento -->				
                        <h:panelGroup>
                            <h:outputText styleClass="tituloCampos" value="#{CElabels['entidade.responsavel.encerramento']}" />
                        </h:panelGroup>
                        <h:panelGroup>
                            <h:outputText styleClass="textoVermelho" value="#{CadastroInicialControle.cadastro.responsavelEncerramento.nome}" />
                        </h:panelGroup>

                        <!-- observa��es do encerramento -->				
                        <h:panelGroup>
                            <h:outputText styleClass="tituloCampos" value="#{CElabels['entidade.obs.encerramento']}" />
                        </h:panelGroup>
                        <h:panelGroup>
                            <h:outputText styleClass="textoVermelho" value="#{CadastroInicialControle.cadastro.obsEncerramento}" escape="false"/>
                        </h:panelGroup>

                    </h:panelGrid>
                </td>

            </c:if>
        </tr>
    </table>
    <!-- parcelas -->	
    <c:if test="${not empty CadastroInicialControle.parcelas }">
        <h:panelGrid rowClasses="linhaImpar, linhaPar"  headerClass="subordinado" 
                     columnClasses="classEsquerdaCE, classDireitaCE" width="100%"
                     columns="2" style="vertical-align: top;">
            <f:facet name="header">
                <h:outputText value="#{CElabels['entidade.parcelas']}" />
            </f:facet>
            <rich:dataTable var="movParcelaVO" value="#{CadastroInicialControle.parcelas}" width="100%" columnClasses="centralizado">

                <rich:column>
                    <f:facet name="header">
                        <h:outputText value="#{CElabels['entidade.descricao']}" />
                    </f:facet>
                    <center>
                        <h:outputText
                            value="#{movParcelaVO.descricao}" />
                    </center>
                </rich:column>


                <rich:column>
                    <f:facet name="header">
                        <h:outputText value="#{CElabels['entidade.dataRegistro']}" />
                    </f:facet>
                    <center>
                        <h:outputText
                            value="#{movParcelaVO.dataRegistro_Apresentar}" />
                    </center>
                </rich:column>

                <rich:column>
                    <f:facet name="header">
                        <h:outputText value="#{CElabels['entidade.vencimento']}" />
                    </f:facet>
                    <center>
                        <h:outputText
                            value="#{movParcelaVO.dataVencimento_Apresentar}" />
                    </center>
                </rich:column>

                <rich:column>
                    <f:facet name="header">
                        <h:outputText value="Pagamento"/>
                    </f:facet>
                    <h:outputText value="#{movParcelaVO.dataPagamento_Apresentar}"/>
                </rich:column>

                <rich:column>
                    <f:facet name="header">
                        <h:outputText value="#{CElabels['entidade.valor']}" />
                    </f:facet>
                    <center>
                        <h:outputText
                            value="#{movParcelaVO.valorParcelaNumerico}">

                        </h:outputText>
                    </center>
                </rich:column>
                <rich:column>
                    <f:facet name="header">
                        <h:outputText value="#{CElabels['entidade.situacao']}" />
                    </f:facet>
                    <center>
                        <h:outputText
                            value="#{movParcelaVO.situacao_Apresentar}">
                        </h:outputText>
                    </center>
                </rich:column>
            </rich:dataTable>
        </h:panelGrid>									
    </c:if>

    <!-- pagamentos -->	
    <c:if test="${not empty CadastroInicialControle.pagamentos}">
        <h:panelGrid rowClasses="linhaImpar, linhaPar"  headerClass="subordinado" 
                     columnClasses="classEsquerdaCE, classDireitaCE" width="100%"
                     columns="2" style="vertical-align: top;">
            <f:facet name="header">
                <h:outputText value="Pagamentos" />
            </f:facet>
            <rich:dataTable var="historicoPagamentos" value="#{CadastroInicialControle.pagamentos}" width="100%" columnClasses="centralizado">

                <rich:column>
                    <f:facet name="header">
                        <h:outputText style="font-weight: bold"
                                      value="#{msg_aplic.prt_HistoricoComprasCliente_codigo}" />
                    </f:facet>
                    <h:outputText value="#{historicoPagamentos.codigo}" />
                </rich:column>
                <rich:column>
                    <f:facet name="header">
                        <h:outputText style="font-weight: bold"
                                      value="#{msg_aplic.prt_HistoricoComprasCliente_nrRecibo}" />
                    </f:facet>
                    <h:outputText value="#{historicoPagamentos.reciboPagamento.codigo}" />
                </rich:column>
                <rich:column>
                    <f:facet name="header">
                        <h:outputText style="font-weight: bold"
                                      value="#{msg_aplic.prt_HistoricoComprasCliente_nomePagador}" />
                    </f:facet>
                    <h:outputText value="#{historicoPagamentos.nomePagador}" />
                </rich:column>
                <rich:column>
                    <f:facet name="header">
                        <h:outputText style="font-weight: bold"
                                      value="#{msg_aplic.prt_HistoricoComprasCliente_dataLancamento}" />
                    </f:facet>
                    <h:outputText value="#{historicoPagamentos.dataLancamento}">
                        <f:convertDateTime pattern="dd/MM/yyyy" />
                    </h:outputText>
                </rich:column>
                <rich:column>
                    <f:facet name="header">
                        <h:outputText style="font-weight: bold"
                                      value="#{msg_aplic.prt_HistoricoComprasCliente_formaPagamento}" />
                    </f:facet>
                    <h:outputText value="#{historicoPagamentos.formaPagamento.tipoFormaPagamento_Apresentar}" />
                </rich:column>
                <rich:column>
                    <f:facet name="header">
                        <h:outputText style="font-weight: bold"
                                      value="Nr. vezes" />
                    </f:facet>
                    <h:outputText value="#{historicoPagamentos.nrVezes}" />
                </rich:column>
                <rich:column>
                    <f:facet name="header">
                        <h:outputText style="font-weight: bold"
                                      value="#{msg_aplic.prt_HistoricoComprasCliente_valor}" />
                    </f:facet>
                    <h:outputText value="#{historicoPagamentos.valorTotal}">
                        <f:converter converterId="FormatadorNumerico" />
                    </h:outputText>
                </rich:column>
            </rich:dataTable>
        </h:panelGrid>									
    </c:if>

    <!-- fim da exibi��o dos detalhes do evento  -->	
</td></tr>
<tr><td>
        <h:panelGroup rendered="#{!CadastroInicialControle.eventoCancelado}">
            <table width="100%" border="0" align="center" cellpadding="0"
                   cellspacing="0">
                <tr>
                    <td width="10"><img
                            src="${root}/imagens/agenda_imgs/menu_esq.jpg" width="10"
                            height="69"></td>
                    <td background="${root}/imagens/agenda_imgs/menu_fundo.jpg">
                        <table width="100%" border="0" cellspacing="0" cellpadding="0">
                            <tr>
                                <td align="center"><h:commandLink id="orcamentoDetalhado2"
                                               action="#{NavegacaoControle.abrirTelaOrcamentoDetalhado}"
                                               actionListener="#{OrcamentoDetalhadoControle.selEventoInteresseListener}"
                                               rendered="#{CadastroInicialControle.eventoCadastrado}">
                                        <h:graphicImage value="/imagens/bt_orcamentodetalhado.jpg"
                                                        style="border: 0px;"
                                                        alt="#{CElabels['menu.operacoesCE.exibicaoOrcamento']}"/>
                                        <f:attribute name="codigoEventoInteresse"
                                                     value="#{CadastroInicialControle.cadastro.codigo}" />
                                        <f:attribute name="dataInteresse"
                                                     value="#{CadastroInicialControle.cadastro.dataInicial}" />
                                        <f:attribute name="perfilInteresse"
                                                     value="#{CadastroInicialControle.cadastro.codigoPerfilEvento}" />
                                        <f:attribute name="ambienteInteresse"
                                                     value="#{CadastroInicialControle.ambiente.codigo}" />
                                    </h:commandLink></td>
                                <td align="center"><h:commandLink id="terminarCadastro2"
                                               action="#{CadastroInicialControle.terminarCadastro}"
                                               rendered="#{CadastroInicialControle.eventoPendente}">
                                        <h:graphicImage value="/imagens/bt_terminarcadastro.jpg"
                                                        style="border: 0px;"
                                                        alt="#{CElabels['operacoes.cadastro.terminar']}" />
                                    </h:commandLink>
                                </td>							
                                <td align="center"><h:commandButton id="editarCadastro2"
                                                 image="/imagens/botoesCE/Editar_cadastro.png"
                                                 action="#{CadastroInicialControle.editarCadastro}"
                                                 rendered="#{CadastroInicialControle.eventoCadastrado}"
                                                 value="#{CElabels['operacoes.cadastro.editar']}">
                                    </h:commandButton>
                                </td>
                                <td align="center">
                                    <a4j:commandButton id="encerrarEvento2"
                                                       rendered="#{CadastroInicialControle.permiteEncerramento}"
                                                       oncomplete="Richfaces.showModalPanel('panelTextoEncerramento');"
                                                       actionListener="#{OrcamentoDetalhadoControle.selEventoExibeListener}"
                                                       reRender="mensagens"
                                                       image="/imagens/botoesCE/encerrar_evento.png"
                                                       value="#{CElabels['operacoes.cadastro.encerrarEvento']}">							
                                        <f:attribute name="codigoEventoInteresse" value="#{CadastroInicialControle.cadastro.codigo}" />

                                    </a4j:commandButton>
                                </td>
                                <td align="center"><h:commandLink id="exibeOrcamento2"
                                               action="#{OrcamentoDetalhadoControle.abrirOrcamentoDetalhado}"
                                               rendered="#{CadastroInicialControle.eventoOrcado}"
                                               actionListener="#{OrcamentoDetalhadoControle.selEventoExibeListener}">
                                        <h:graphicImage value="/imagens/bt_orcamentodetalhado.jpg"
                                                        style="border: 0px;"
                                                        alt="#{CElabels['menu.operacoesCE.exibicaoOrcamento']}"/>
                                        <f:attribute name="codigoEventoInteresse"
                                                     value="#{CadastroInicialControle.cadastro.codigo}" />
                                    </h:commandLink></td>
                                <td align="center">

                                    <a4j:commandLink id="consultarConversa2"
                                                     actionListener="#{ConversaControle.selEventoConversaListener}"
                                                     action="#{ConversaControle.abrirConversaComInteressado}">
                                        <h:graphicImage value="/imagens/bt_conversas.jpg" style="border: 0px;" alt="#{CElabels['operacoes.conversas']}"/>
                                        <f:attribute name="codigoEvento"
                                                     value="#{CadastroInicialControle.cadastro.codigo}" />
                                        <f:attribute name="codigoInteressado"
                                                     value="#{CadastroInicialControle.cadastro.codigoInteressado}" />
                                    </a4j:commandLink></td>
                                <td align="center"><h:commandLink id="emissaoDocumentos2"
                                               action="#{EmissaoDocumentosControle.abrirEmissaoDocumentos}"
                                               rendered="#{CadastroInicialControle.eventoNegociado}"
                                               actionListener="#{EmissaoDocumentosControle.selEventoInteresseListener}">
                                        <h:graphicImage value="/imagens/bt_emissaodedocumentos.jpg"
                                                        style="border: 0px;"
                                                        alt="#{CElabels['menu.operacoesCE.emissaoDocumentos']}"/>
                                        <f:attribute name="codigoEventoInteresse"
                                                     value="#{CadastroInicialControle.cadastro.codigo}" />
                                    </h:commandLink></td>
                                <td align="center">
                                    <h:panelGroup id="conteudo2" rendered="#{CadastroInicialControle.eventoReservado}">
                                        <a4j:commandButton id="fechaNegociacao2"
                                                           image="/imagens/botoesCE/fechar_negociacao.png"
                                                           reRender="panelUsuarioSenha, panelClienteInexistente"
                                                           value="#{CElabels['operacoes.negociacao.fechar']}"
                                                           alt="#{CElabels['operacoes.negociacao.fechar']}"
                                                           styleClass="botoes"
                                                           actionListener="#{OrcamentoDetalhadoControle.selFecharNegociacao}">
                                            <f:attribute name="codigoEvento"
                                                         value="#{CadastroInicialControle.cadastro.codigo}" />
                                        </a4j:commandButton>
                                    </h:panelGroup>
                                </td>
                                <c:if test="${CadastroInicialControle.nrBotoes eq 9}">
                                </tr>
                            </table>
                            <table width="100%" border="0" cellspacing="0" cellpadding="0">
                                <tr>
                                </c:if>

                                <td align="center">
                                    <a4j:commandButton id="agendarcontato2"
                                                       image="/imagens/botoesCE/agendar_contato.png"
                                                       value="#{CElabels['operacoes.atualizarProxContato']}"
                                                       alt="#{CElabels['operacoes.atualizarPreReserva']}"
                                                       styleClass="botoes"
                                                       oncomplete="Richfaces.showModalPanel('panelTextoAgendarContato');"
                                                       >
                                    </a4j:commandButton></td>

                                <c:if test="${CadastroInicialControle.nrBotoes gt 5 and CadastroInicialControle.nrBotoes lt 9}">
                                </tr>
                            </table>
                            <table width="100%" border="0" cellspacing="0" cellpadding="0">
                                <tr>
                                </c:if>
                                <td align="center">
                                    <a4j:commandButton id="remarcarPreReserva2"
                                                       image="/imagens/botoesCE/atualizar_pre_reserva.png"
                                                       rendered="#{CadastroInicialControle.eventoReservado}"
                                                       value="#{CElabels['operacoes.atualizarPreReserva']}"
                                                       alt="#{CElabels['operacoes.atualizarPreReserva']}"
                                                       styleClass="botoes"
                                                       oncomplete="Richfaces.showModalPanel('panelTextoRemarcarPreReserva');"
                                                       >
                                    </a4j:commandButton>
                                </td>


                                <td align="center"><h:commandLink id="cancelarEvento2"
                                               action="#{NavegacaoControle.abrirTelaCancelamentoEvento}"
                                               actionListener="#{CancelamentoEventoControle.selEventoListener}">
                                        <h:graphicImage value="/imagens/bt_cancelamentodeevento.jpg"
                                                        style="border: 0px;"
                                                        alt="#{CElabels['operacoes.negociacao.fechar']}"/>
                                        <f:attribute name="codigoEvento"
                                                     value="#{CadastroInicialControle.cadastro.codigo}" />
                                    </h:commandLink></td>

                                <td align="center">	
                                    <h:commandButton rendered="#{CadastroInicialControle.eventoParcelado}"
                                                     image="/imagens/botoesCE/pagamentos.png"
                                                     action="#{NavegacaoControle.abrirTelaCaixaEmAberto}"
                                                     actionListener="#{ProspectsControle.selEventoInteresseListener}" 
                                                     onmouseover="toolTip('Parcelas' , 120 , 'gray')"
                                                     onmouseout="hideToolTip();"
                                                     value="#{CElabels['menu.operacoesCE.pagamentos']}">							
                                        <f:attribute name="codigoEventoInteresse" value="#{CadastroInicialControle.cadastro.codigo}" />
                                    </h:commandButton>
                                </td>

                                <td align="center">
                                    <a4j:commandButton id="autorizarEvento2"
                                                       image="/imagens/botoesCE/autorizar_evento.png"
                                                       rendered="#{CadastroInicialControle.eventoConfirmado}"
                                                       value="Autorizar Evento"
                                                       alt="Autorizar Evento"
                                                       styleClass="botoes"
                                                       actionListener="#{OrcamentoDetalhadoControle.selCarregarNegociacaoPorEvento}"
                                                       oncomplete="Richfaces.showModalPanel('panelAutorizarEvento');">

                                        <f:attribute name="codigoEvento" value="#{CadastroInicialControle.cadastro.codigo}" />
                                    </a4j:commandButton>														
                                </td>

                                <td align="center">
                                    <h:panelGroup rendered="#{CadastroInicialControle.eventoOrcado}">
                                        <h:graphicImage value="/imagens/bt_ajuda.jpg" style="border: 0px;" onclick="ativarAjuda('verificar');"/>
                                    </h:panelGroup>
                                </td>

                            </tr>
                        </table>
                    </td>
                    <td width="10"><img
                            src="${root}/imagens/agenda_imgs/menu_dir.jpg" width="10"
                            height="69"></td>
                </tr>
            </table>
        </h:panelGroup>
    </td></tr>


</table>



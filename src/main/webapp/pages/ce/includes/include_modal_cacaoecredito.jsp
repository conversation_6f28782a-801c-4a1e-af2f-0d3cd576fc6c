<rich:modalPanel id="panelCacaoECredito" autosized="true" shadowOpacity="true" width="420" height="220" domElementAttachment="parent">
       <f:facet name="header">
           <h:panelGroup>
               <h:outputText value="#{CElabels['operacoes.cacaoecredito.texto']}"></h:outputText>
           </h:panelGroup>
       </f:facet>
       <f:facet name="controls">
           <h:panelGroup>
               <h:graphicImage value="/imagens/close.png" style="cursor:pointer" id="hidelinkCacaoECredito"/>
               <rich:componentControl for="panelCacaoECredito" attachTo="hidelinkCacaoECredito" operation="hide" event="onclick"/>
           </h:panelGroup>
       </f:facet>
       
	<a4j:form id="formCacaoECredito" ajaxSubmit="true">

	<h:panelGrid rowClasses="linhaImpar, linhaPar" width="100%" columns="1">

             <h:panelGroup>	
			<h:outputText styleClass="text"  value="#{CElabels['entidade.data.atual']}: #{CadastroInicialControle.dataAtual}"/>                        
             </h:panelGroup>
		
		<h:panelGroup>	
			<h:outputText styleClass="text"  value="#{CElabels['entidade.tipo']}: #{OrcamentoDetalhadoControle.caucaoECredito.tipoCaucaoECredito.descricao}"/>                        
             </h:panelGroup>
        
        <h:panelGroup>
        	<h:outputText styleClass="text"  value="#{CElabels['entidade.valor']}: "/>     		
          	<h:inputText id="valorDesconto" onblur="blurinput(this);" onfocus="focusinput(this);"
			onkeypress="return(currencyFormat(this,'.',',',event));"
			styleClass="form" maxlength="14" value="#{OrcamentoDetalhadoControle.caucaoECredito.strValorFormatado}" />
		</h:panelGroup>
			
		<h:panelGroup>
			<h:outputText value="#{CElabels['entidade.observacao']}:" />
			<rich:editor id="textoPadrao" useSeamText="false" viewMode="visual"
				width="300" height="200"
				value="#{OrcamentoDetalhadoControle.caucaoECredito.observacao}"/>
		</h:panelGroup>
		
	</h:panelGrid>
	
	<center>
        <a4j:commandButton id="confirmarCaucaoECredito" reRender="dttaCaucaoCredito, detalhamentoNegociacao" 
        value="Confirmar" image="/imagens/botoesCE/confirmar.png"
        action="#{OrcamentoDetalhadoControle.adicionarCaucaoECredito}"
        oncomplete="Richfaces.hideModalPanel('panelCacaoECredito');"/>
        &nbsp;
        <a4j:commandButton id="cancelarCaucaoECredito" reRender="dttaCaucaoCredito, detalhamentoNegociacao" 
        value="Cancelar" image="/imagens/botoesCE/cancelar.png"
        oncomplete="Richfaces.hideModalPanel('panelCacaoECredito');"/>
      </center> 
       </a4j:form>
   </rich:modalPanel>
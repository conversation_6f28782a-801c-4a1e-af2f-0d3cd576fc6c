<%@include file="../includes/include_imports.jsp" %>
<table width="100%">
<tr><td><rich:simpleTogglePanel 
			switchType="client" opened="true" id="msg"
			rendered ="#{AgendaVisitaControle.msg}">
		<f:facet name="header">
			<h:outputText value="#{CElabels['menu.operacoesCE.mensagens']}" />
		</f:facet>
		<h:panelGrid columns="1" width="100%">
			<h:outputText styleClass="mensagem" value="#{AgendaVisitaControle.mensagem}" />
			<h:outputText styleClass="mensagemDetalhada" value="#{AgendaVisitaControle.mensagemDetalhada}" />
		</h:panelGrid>
	</rich:simpleTogglePanel></td></tr>
<tr><td>
<table width="100%" border="0" align="center" cellpadding="0"
	cellspacing="0">
	<tr>
		<td width="10"><img
			src="${contexto}/imagens/agenda_imgs/menu_esq.jpg" width="10"
			height="69"></td>
		<td background="${contexto}/imagens/agenda_imgs/menu_fundo.jpg">
		<table width="100%" border="0" cellspacing="0" cellpadding="0">
			<tr>
				<td align="center">
				<h:commandLink id="salvar"
				title="#{CElabels['menu.operacoesCE.operacoes.salvar']}"
				action="#{AgendaVisitaControle.salvar}"
				onclick="if(!validar()){return false;};"
				actionListener="#{AgendaVisitaControle.autorizacao}">
				<!-- Funcionalidade.visita -->
				<f:attribute name="funcao" value="107" />
				<h:graphicImage value="/imagens/bt_salvar.jpg" style="border: 0px;"/>
				</h:commandLink>
				</td>
				<td align="center">
				<h:commandLink id="simularOrcamento" 
				action="#{OrcamentoDetalhadoControle.abrirOrcamentoFicticio}"
				title="#{CElabels['menu.operacoesCE.operacoes.simularOrcamento']}">
				<h:graphicImage value="/imagens/bt_simularorcamento.jpg" style="border: 0px;"/>
				</h:commandLink>
				</td>
				<td align="center">
				<a4j:commandLink title="Ajuda" oncomplete="ativarAjuda(['dataVisita'	, 'ambiente', 'tipoAmbiente', 'nomeCliente', 'horarioFinal', 'horarioInicial']);" >
					<h:graphicImage value="/imagens/bt_ajuda.jpg" style="border: 0px;"/>
					</a4j:commandLink>
				</td>

			</tr>
		</table>
		</td>
		<td width="10"><img
			src="${contexto}/imagens/agenda_imgs/menu_dir.jpg" width="10"
			height="69"></td>
	</tr>
</table>
</td></tr>
<tr><td>
<h:panelGrid id="panelDadosAgendaVisita" 
			rowClasses="linhaImpar, linhaPar"
			columnClasses="classEsquerda, classDireita"
			width="100%" columns="2">
			
			<h:panelGroup>
				<a onmouseover="if (document.getElementById('hint-dataVisita').style.visibility != 'hidden') {toolTip('<h:outputText value="#{CElabels['entidade.hint.verificarDisponibilidade']}"/>' , 300 , 'blue')}"
						onmouseout="hideToolTip();"><img src="${contexto}/pages/ce/img/hint.gif" width="15px"
						id="hint-dataVisita" style="visibility: hidden;" height="15px" border="0px;" /></a>&nbsp;&nbsp;&nbsp;<%@include file="../includes/include_obrigatorio.jsp" %>
				<h:outputText styleClass="tituloCampos"
					value="#{CElabels['entidade.agendaVisita.dataVisita']}"/>
			</h:panelGroup>
			
			<h:panelGroup rendered="#{!AgendaVisitaControle.habilitaCampos}">
			<h:outputText id="dataVisitaString" value="#{AgendaVisitaControle.agendaVisita.dataVisitaString}"/>
			</h:panelGroup>
			
			<h:panelGroup rendered="#{AgendaVisitaControle.habilitaCampos}">
				<rich:calendar id="dataVisita" styleClass="form"
					value="#{AgendaVisitaControle.agendaVisita.dataVisita}"
					datePattern="dd/MM/yyyy" inputSize="12" inputClass="form"
					oninputblur="blurinput(this);limparMsgObrig('form:dataVisitaInputDate', 'dataVisita');" 
					oninputfocus="focusinput(this);"
					onchanged="limparMsgObrig('form:dataVisitaInputDate', 'dataVisita');"
					oninputchange="return validar_Data(this.id);"
					oninputkeypress="return mascara(this, '99/99/9999', event);"
					enableManualInput="true" zindex="2" showWeeksBar="false"
					tabindex="1"> 
				</rich:calendar>&nbsp;
				<a4j:commandLink id="consultarDisponibilidade"
					onclick="if(!validarDisponibilidade()){return false;};"
				    reRender="panelEventos"
					action="#{ConsultaEventosControle.consultarDispAmbAgendaVisita}"
					oncomplete="Richfaces.showModalPanel('panelEventos');">
					<h:graphicImage value="/imagens/botoesCE/Verificar_disponibilidade.png" style="border: 0px;" alt="#{CElabels['operacoes.disponibilidade.verificar']}"/>
						</a4j:commandLink>&nbsp;&nbsp;&nbsp;
				<div id="divObg-dataVisita" class="mensagemObrigatorio"></div>		
			</h:panelGroup>
			
			<h:panelGroup><a onmouseover="if (document.getElementById('hint-ambiente').style.visibility != 'hidden') {toolTip('<h:outputText value="#{CElabels['entidade.hint.ambiente']}"/>' , 300 , 'blue')}"
						onmouseout="hideToolTip();"><img src="${contexto}/pages/ce/img/hint.gif" width="15px"
						id="hint-ambiente" style="visibility: hidden;" height="15px" border="0px;" /></a>&nbsp;&nbsp;&nbsp;<%@include file="../includes/include_obrigatorio.jsp" %>
				<h:outputText styleClass="tituloCampos" value="#{CElabels['entidade.ambiente']}" />
			</h:panelGroup>
			<h:panelGroup >
			<h:outputText rendered="#{!AgendaVisitaControle.habilitaCampos}" value="#{AgendaVisitaControle.descricaoAmbiente}"/>
				<!-- Combo de Ambientes -->
			<h:selectOneMenu 
					rendered="#{AgendaVisitaControle.habilitaCampos}"
					onblur="blurinput(this);limparMsgObrig('form:ambiente', 'ambiente');"
					onfocus="focusinput(this);" styleClass="form" id="ambiente"
					value="#{AgendaVisitaControle.agendaVisita.ambiente}"
					tabindex="2">
					<f:selectItem itemValue="" itemLabel="#{CElabels['operacoes.selecione']}"/>
					<f:selectItems value="#{AgendaVisitaControle.listaAmbiente}" />
				</h:selectOneMenu>
				<div id="divObg-ambiente" class="mensagemObrigatorio"></div>
				
			</h:panelGroup>
			<!-- Combo de Eventos -->
			<h:panelGroup>
				<h:outputText styleClass="tituloCampos"
					value="#{CElabels['entidade.evento']}:" />
			</h:panelGroup>
			<h:selectOneMenu onblur="blurinput(this);"
				disabled="#{!AgendaVisitaControle.temEventos}"
				onfocus="focusinput(this);" styleClass="form" id="eventoInteresse"
				value="#{AgendaVisitaControle.agendaVisita.codigoEvento}"
				tabindex="2">
				<f:selectItem itemValue=""
					itemLabel="#{CElabels['operacoes.selecione']}" />
				<f:selectItems value="#{AgendaVisitaControle.eventosInteresse}" />
			</h:selectOneMenu>

			<h:panelGroup>
				<a onmouseover="if (document.getElementById('hint-tipoAmbiente').style.visibility != 'hidden') {toolTip('<h:outputText value="#{CElabels['entidade.hint.tipoVisita']}"/>' , 300 , 'blue')}"
						onmouseout="hideToolTip();"><img src="${contexto}/pages/ce/img/hint.gif" width="15px"
						id="hint-tipoAmbiente" style="visibility: hidden;" height="15px" border="0px;" /></a>&nbsp;&nbsp;&nbsp;<%@include file="../includes/include_obrigatorio.jsp" %>
				<h:outputText styleClass="tituloCampos" value="#{CElabels['entidade.agendaVisita.tipoVisita']}" />
			</h:panelGroup>
			<h:panelGroup>
				<!-- Combo de Tipos de Visitas -->
			<h:selectOneMenu onfocus="focusinput(this);" styleClass="form" id="tiposVisita"
					onblur="blurinput(this);limparMsgObrig('form:tiposVisita', 'tiposVisita');"
					value="#{AgendaVisitaControle.agendaVisita.tipoVisita}"
					tabindex="3">
					<f:selectItem itemValue="" itemLabel="#{CElabels['operacoes.selecione']}"/>
					<f:selectItems value="#{AgendaVisitaControle.listaTiposVisita}" />
					
				<a4j:support event="onchange" reRender="horarios" action="#{AgendaVisitaControle.calculaHorarioFinal}"/>	
				</h:selectOneMenu>
			<div id="divObg-tiposVisita" class="mensagemObrigatorio"/>	
			</h:panelGroup>
			
			<h:panelGroup>
			<a onmouseover="if (document.getElementById('hint-horarioInicial').style.visibility != 'hidden') {toolTip('<h:outputText value="#{CElabels['entidade.hint.horaInicial']}"/>' , 300 , 'blue')}"
						onmouseout="hideToolTip();"><img src="${contexto}/pages/ce/img/hint.gif" width="15px"
						id="hint-horarioInicial" style="visibility: hidden;" height="15px" border="0px;" /></a>&nbsp;&nbsp;&nbsp;<h:outputText styleClass="tituloCampos" value="#{CElabels['entidade.horario.inicial']}: "/>
			</h:panelGroup>
			<h:panelGroup>
			<h:outputText  value="#{AgendaVisitaControle.agendaVisita.horarioMarcadoString}"/>
			<h:inputHidden value="#{AgendaVisitaControle.agendaVisita.horarioMarcadoString}" id="horaInicial" />
			<div id="divObg-horaInicial" class="mensagemObrigatorio"/>
			</h:panelGroup>
			
			<h:panelGroup>
			<a onmouseover="if (document.getElementById('hint-horarioFinal').style.visibility != 'hidden') {toolTip('<h:outputText value="#{CElabels['entidade.hint.horaFinal']}"/>' , 300 , 'blue')}"
						onmouseout="hideToolTip();"><img src="${contexto}/pages/ce/img/hint.gif" width="15px"
						id="hint-horarioFinal" style="visibility: hidden;" height="15px" border="0px;" /></a>&nbsp;&nbsp;&nbsp;<h:outputText styleClass="tituloCampos" value="#{CElabels['entidade.agendaVisita.horarioFinal']}: "/>
			</h:panelGroup>
			<h:panelGroup id="horarios">
			<h:outputText id="horarioFinalOut" rendered="#{!AgendaVisitaControle.habilitaHorarioFinal}" value="#{AgendaVisitaControle.agendaVisita.horarioFinalString}"/>
			<h:inputText id="horarioFinalIn" onblur="blurinput(this); Mascara_Hora(this.value, this); validarHorarioFinal();"  onfocus="focusinput(this);"
						 rendered="#{AgendaVisitaControle.habilitaHorarioFinal}"
			             styleClass="form" maxlength="7" 
			             value="#{AgendaVisitaControle.agendaVisita.horarioFinalString}"
						 onkeypress="return mascara(this, '99:99', event);" size="7"
						 tabindex="5"/>
			<div id="divObg-horarios" class="mensagemObrigatorio"/>
			<div id="divObg-terminoDiaPosterior" class="mensagemObrigatorio"></div>
			<div id="divObg-validarHora" class="mensagemObrigatorio">			
			</h:panelGroup>
			
			<h:panelGroup>
				<a onmouseover="if (document.getElementById('hint-nomeCliente').style.visibility != 'hidden') {toolTip('<h:outputText value="#{CElabels['entidade.hint.nomeCliente']}"/>' , 300 , 'blue')}"
						onmouseout="hideToolTip();"><img src="${contexto}/pages/ce/img/hint.gif" width="15px"
						id="hint-nomeCliente" style="visibility: hidden;" height="15px" border="0px;" /></a>&nbsp;&nbsp;&nbsp;<h:outputText styleClass="tituloCampos" value="#{CElabels['entidade.cliente.nome']}" />
			</h:panelGroup>
			<h:panelGroup>
				<h:outputText value="#{AgendaVisitaControle.agendaVisita.interessado.nomeCliente}"/>
			</h:panelGroup>
			<h:panelGroup>
				<h:outputText styleClass="tituloCampos" value="#{CElabels['entidade.interessado.telefoneCelular']}" />
			</h:panelGroup>
			<h:panelGroup>
				<h:outputText value="#{AgendaVisitaControle.agendaVisita.interessado.celular}"/>
			</h:panelGroup>
			<h:panelGroup>
				<h:outputText styleClass="tituloCampos" value="#{CElabels['entidade.interessado.telefoneComercial']}" />
			</h:panelGroup>
			<h:panelGroup>
				<h:outputText value="#{AgendaVisitaControle.agendaVisita.interessado.telefoneComercial}"/>
			</h:panelGroup>
			<h:panelGroup>
				<h:outputText styleClass="tituloCampos"  value="#{CElabels['entidade.interessado.telefoneFixo']}" />
			</h:panelGroup>
			<h:panelGroup>
				<h:outputText value="#{AgendaVisitaControle.agendaVisita.interessado.telefone}"/>
			</h:panelGroup>
			<h:panelGroup>
			<h:outputText styleClass="tituloCampos"  value="#{CElabels['entidade.interessado.email']}"/>
			</h:panelGroup>
			<h:panelGroup>
				<h:outputText value="#{AgendaVisitaControle.agendaVisita.interessado.email}"/>
			</h:panelGroup>
			<h:panelGroup>
				<h:outputText styleClass="tituloCampos" value="#{CElabels['entidade.agendaVisita.observacoes']}" />
			</h:panelGroup>
			<h:panelGroup>
				<rich:editor id="observacoes" useSeamText="false" viewMode="visual" tabindex="11"
						width="400" height="200" value="#{AgendaVisitaControle.agendaVisita.observacoes}"
						/>
			</h:panelGroup>
		</h:panelGrid>

</td></tr>

<tr><td>
<table width="100%" border="0" align="center" cellpadding="0"
	cellspacing="0">
	<tr>
		<td width="10"><img
			src="${contexto}/imagens/agenda_imgs/menu_esq.jpg" width="10"
			height="69"></td>
		<td background="${contexto}/imagens/agenda_imgs/menu_fundo.jpg">
		<table width="100%" border="0" cellspacing="0" cellpadding="0">
			<tr>
				<td align="center">
				<h:commandLink id="salvar2"
				title="#{CElabels['menu.operacoesCE.operacoes.salvar']}"
				action="#{AgendaVisitaControle.salvar}"
				onclick="if(!validar()){return false;};"
					actionListener="#{AgendaVisitaControle.autorizacao}">
				<!-- Funcionalidade.visita -->
				<f:attribute name="funcao" value="107" />
				<h:graphicImage value="/imagens/bt_salvar.jpg" style="border: 0px;"/>
				</h:commandLink>
				</td>
				<td align="center">
				<h:commandLink id="simularOrcamento2" 
				action="#{OrcamentoDetalhadoControle.abrirOrcamentoFicticio}"
				title="#{CElabels['menu.operacoesCE.operacoes.simularOrcamento']}">
				<h:graphicImage value="/imagens/bt_simularorcamento.jpg" style="border: 0px;"/>
				</h:commandLink>
				</td>
				<td align="center">
				<a4j:commandLink title="Ajuda" oncomplete="ativarAjuda(['dataVisita'	, 'ambiente', 'tipoAmbiente', 'nomeCliente', 'horarioFinal', 'horarioInicial']);" >
					<h:graphicImage value="/imagens/bt_ajuda.jpg" style="border: 0px;"/>
					</a4j:commandLink>	 
				</td>

			</tr>
		</table>
		</td>
		<td width="10"><img
			src="${contexto}/imagens/agenda_imgs/menu_dir.jpg" width="10"
			height="69"></td>
	</tr>
</table>
</td></tr>
</table>





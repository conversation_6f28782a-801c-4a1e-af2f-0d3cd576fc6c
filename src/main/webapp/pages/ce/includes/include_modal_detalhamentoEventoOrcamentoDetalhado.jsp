<rich:modalPanel id="panelDetalheEvento" autosized="true" shadowOpacity="true" width="450" height="250">
    	
	<f:facet name="header">
		<h:panelGroup>
			<h:outputText value="#{CElabels['menu.operacoesCE.detalheEvento']}"></h:outputText>
		</h:panelGroup>
	</f:facet>
	
	<f:facet name="controls">
		<h:panelGroup>
			<h:graphicImage value="/imagens/close.png" style="cursor:pointer" id="hidelinkDetalheEvento" />
			<rich:componentControl for="panelDetalheEvento"
				attachTo="hidelinkDetalheEvento" operation="hide" event="onclick" />
		</h:panelGroup>
	</f:facet>

    <h:form id="formDetalheEvento">
               
    	<%@include file="/pages/ce/includes/topoReduzido.jsp"%>
        
		<h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
		   	
		   	<rich:simpleTogglePanel switchType="client" opened="true" id="stp2DetalheEvento">
				<f:facet name="header">
					<h:outputText value="#{CElabels['menu.operacoesCE.dadosEvento']}" />
				</f:facet>
		     	
		     	<h:panelGrid rowClasses="linhaImpar, linhaPar" columnClasses="classEsquerda, classDireita"
						style="background-image:url('../../../imagens/backGroundTelaBasico.png'); background-repeat: no-repeat;padding: 8px;"
						width="100%" columns="2">
		     	
	     			<h:outputText value="#{CElabels['entidade.evento.nome']}: "/>
					<h:outputText value="#{OrcamentoDetalhadoControle.evento.nomeEvento}"/>
			
					<h:outputText value="#{CElabels['entidade.interessado.nome']}: "/>
					<h:outputText value="#{OrcamentoDetalhadoControle.evento.interessado.nomeCliente }"/>	
			
					<h:outputText value="#{CElabels['entidade.dataInteresse']}: "/>
					<h:outputText value="#{OrcamentoDetalhadoControle.evento.dataFormatada}"/>
		
					<h:outputText value="#{CElabels['entidade.nrConv']}: "/>
					<h:outputText value="#{OrcamentoDetalhadoControle.evento.numeroConvidados}"/>
		
					<h:outputText value="#{CElabels['entidade.situacao']}: "/>
					<h:outputText value="#{OrcamentoDetalhadoControle.evento.situacao.descricao}"/>
				</h:panelGrid>             
			</rich:simpleTogglePanel>       
		</h:panelGrid>
	</h:form>
</rich:modalPanel>
    
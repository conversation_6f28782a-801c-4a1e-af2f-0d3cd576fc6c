<rich:modalPanel id="panelEventos" autosized="true" shadowOpacity="true" width="750" height="450">
    	
	<f:facet name="header">
		<h:panelGroup>
			<h:outputText
				value="Disponibilidades"></h:outputText>
		</h:panelGroup>
	</f:facet>
	<f:facet name="controls">
		<h:panelGroup>
			<h:graphicImage value="/imagens/close.png" style="cursor:pointer"
				id="hidelinkDisponibilidades" />
			<rich:componentControl for="panelEventos"
				attachTo="hidelinkDisponibilidades" operation="hide"
				event="onclick" />
		</h:panelGroup>
	</f:facet>

        
        <h:form id="formDisponibilidades">
               
        <%@include file="/pages/ce/includes/topoReduzido.jsp"%>
        
        <a4j:keepAlive beanName="AgendaVisitaControle" />
         
        <!-- Caixa com bot�es -->
        
		<h:panelGrid id="panelBotoes" columns="1" width="100%"
				styleClass="tabMensagens">
			<h:outputText styleClass="mensagemDetalhadaGrande" rendered="#{ConsultaEventosControle.disponibilidadeTO.status == 3}"
					value="#{CElabels['entidade.consultaEventos.dia.situacao.cheio']}" />
			<h:outputText rendered="#{!ConsultaEventosControle.disponivelParaVisita && !ConsultaEventosControle.preenchido}"
					value="#{CElabels['operacoes.horario.selecione']}:" />
			<h:outputText styleClass="mensagem"  value="#{ConsultaEventosControle.mensagem}"/>
            <h:outputText styleClass="mensagemDetalhada" value="#{ConsultaEventosControle.mensagemDetalhada}"/>
			<h:panelGrid columns="8" width="50px">
				<h:commandButton rendered="#{ConsultaEventosControle.disponivel}" 
						actionListener="#{CadastroInicialControle.selAmbienteDiaListener}"
						value="Reservar" styleClass="botoes" 
						action="#{CadastroInicialControle.abrirCadastroInicial}"
						image="/imagens/botoesCE/reservar.png">
					<f:attribute name="codigoAmbienteInteresse" value='#{ConsultaEventosControle.resultadoConsulta[ConsultaEventosControle.indexAmbiente]["0"]["2"]}'/>
					<f:attribute name="dataInteresse" value="#{ConsultaEventosControle.dataSelecionada}"/>
				</h:commandButton>
				<h:commandButton rendered="#{ConsultaEventosControle.disponivel}" actionListener="#{OrcamentoDetalhadoControle.selDataInteresseListener}"
						image="/imagens/botoesCE/simular_orcamento.png"
						value="Simular Or�amento" styleClass="botoes" action="#{OrcamentoDetalhadoControle.abrirOrcamentoFicticio}">
					<f:attribute name="dataInteresse" value="#{ConsultaEventosControle.dataSelecionada}"/>
				</h:commandButton>
				<h:commandButton action="#{CadastroInicialControle.abrirDetalhamento}"
						image="/imagens/botoesCE/detalhamento_do_evento.png"
						rendered="#{ConsultaEventosControle.preenchido}" actionListener="#{CadastroInicialControle.selEventoListener}"
			 			value="#{CElabels['menu.operacoesCE.cadastroInicialDetalhado']}" alt="#{CElabels['operacoes.cadastro.terminar']}" styleClass="botoes">
		 			<f:attribute name="codigoReserva1" value="#{ConsultaEventosControle.codigoReserva}"/></h:commandButton>
				<h:commandButton image="/imagens/botoesCE/exibir_orcamento.png"
				        action="#{OrcamentoDetalhadoControle.abrirOrcamentoDetalhado}"
						rendered="#{ConsultaEventosControle.preenchido}" actionListener="#{OrcamentoDetalhadoControle.selNegociacaoEventoListener}"
			 			value="#{CElabels['menu.operacoesCE.exibicaoOrcamento']}" alt="#{CElabels['operacoes.cadastro.terminar']}" styleClass="botoes">
		 			<f:attribute name="codigoReserva" value="#{ConsultaEventosControle.codigoReserva}"/></h:commandButton>
		 		<h:commandButton image="/imagens/botoesCE/agendar_visita.png" action="#{AgendaVisitaControle.abrirAgendaVisita}"
						rendered="#{ConsultaEventosControle.disponivelParaVisita}" actionListener="#{AgendaVisitaControle.selAmbienteDiaListener}"
			 			value="#{CElabels['menu.operacoesCE.agendaVisita']}" alt="#{CElabels['operacoes.cadastro.terminar']}" styleClass="botoes">
		 			<f:attribute name="codigoAmbiente" value='#{ConsultaEventosControle.resultadoConsulta[ConsultaEventosControle.indexAmbiente]["0"]["2"]}'/>
					<f:attribute name="dataVisita" value="#{ConsultaEventosControle.dataSelecionada}"/>
					<f:attribute name="horaMarcada" value="#{ConsultaEventosControle.horaMarcada}"/></h:commandButton>
			    
			    <a4j:commandButton rendered="#{ConsultaEventosControle.visualizarBtnCancelarVisita}" actionListener="#{AgendaVisitaControle.definirReservaVisita}"
			    	image="/imagens/botoesCE/cancelar_visita.png"
		 			value="#{CElabels['menu.operacoesCE.cancelarVisita']}" alt="#{CElabels['operacoes.cadastro.terminar']}" styleClass="botoes"
		 			oncomplete="Richfaces.showModalPanel('panelUsuarioSenha');">
	 			<f:attribute name="codigoReserva" value="#{ConsultaEventosControle.codigoReserva}"/></a4j:commandButton>
	 			
	 			<a4j:commandButton rendered="#{ConsultaEventosControle.visualizarBtnVisitaRealizada}"
	 			image="/imagens/botoesCE/confirmar_visita.png"
	 			reRender="panelBotoes" 
	 			action="#{NavegacaoControle.abrirTelaInicial}"
	 			actionListener="#{AgendaVisitaControle.marcarComoVisitado}"
		 		value="#{CElabels['menu.operacoesCE.confirmarVisita']}" alt="#{CElabels['operacoes.cadastro.terminar']}" styleClass="botoes">
	 			<f:attribute name="codigoReserva" value="#{ConsultaEventosControle.codigoReserva}"/></a4j:commandButton>
	 			
	 			<a4j:commandButton rendered="#{ConsultaEventosControle.visualizarBtnRemarcarVisita}" 
	 				actionListener="#{AgendaVisitaControle.definirReservaVisita}"
	 				image="/imagens/botoesCE/remarcar_visita.png"
		 			value="#{CElabels['menu.operacoesCE.remarcarVisita']}" alt="#{CElabels['operacoes.cadastro.terminar']}" styleClass="botoes"
		 			reRender="panelRemarcarVisita" oncomplete="Richfaces.showModalPanel('panelRemarcarVisita');">
	 			<f:attribute name="codigoReserva" value="#{ConsultaEventosControle.codigoReserva}"/></a4j:commandButton>
	 				 			
			</h:panelGrid>
		</h:panelGrid>
		
		<table border="0" width="98%">
			<tr>
				<td colspan="2" align="center">
						<h:outputText style="font-size: 15px; font-weight: bold;" value="#{CElabels['entidade.data']}: " />
						<h:outputText style="font-size: 15px;" value='#{ConsultaEventosControle.diasConsultados[ConsultaEventosControle.indexDia].diaSemana}, #{ConsultaEventosControle.diasConsultados[ConsultaEventosControle.indexDia].data}' />
					</td>
			</tr>
			<tr>
				<td width="50%" align="center"><h:outputText style="font-size: 15px; font-weight: bold;" value="#{CElabels['entidade.nome']}: " />
						<h:outputText style="font-size: 15px;" id="nomeSelecionado"/></td>
				<td width="50%" align="center">
						<h:outputText style="font-size: 15px; font-weight: bold;" value="#{CElabels['entidade.tipo']}: " />
						<h:outputText style="font-size: 15px;" id="tipoSelecionado" value="#{ConsultaEventosControle.descricaoTipoSelecionado}"/>
						
				</td>
			</tr>
			<tr>
				<td width="50%" align="center"><h:outputText style="font-size: 15px; font-weight: bold;" value="#{CElabels['entidade.ambiente']}: " />
						<h:outputText style="font-size: 15px;" value='#{ConsultaEventosControle.resultadoConsulta[ConsultaEventosControle.indexAmbiente]["0"]["1"]}' />
					</td>
				<td width="50%" align="center"><h:outputText style="font-size: 15px; font-weight: bold;" value="#{CElabels['entidade.horario']}: " />
						<h:outputText style="font-size: 15px;" id="horarioSelecionado"/>
					</td>
			</tr>
		</table>
		<div style="overflow-x:hidden; width:750; height:250; overflow-y:auto;">
		 <rich:dataTable width="100%" headerClass="consulta" rowClasses="linhaImpar, linhaPar"
					columnClasses="colunaAlinhamento" value="#{ConsultaEventosControle.disponibilidadeTO.reservas}" var="reserva"
					reRender="panelBotoes" style="cursor: pointer;"
					onRowMouseOver="backgroundAntigo = this.style.background; this.style.background = '#D7D7D7';"
					onRowMouseOut="this.style.background = backgroundAntigo;"
					onRowClick="document.getElementById('formDisponibilidades:horarioSelecionado').innerHTML = '#{reserva.horarioInicialFormatado} - #{reserva.horarioFinalExibicaoFormatado}';
						document.getElementById('formDisponibilidades:horaMarcada').value = '#{reserva.horarioInicialFormatado}';
						document.getElementById('formDisponibilidades:tipoSelecionado').innerHTML = '#{reserva.tipo.descricao}';
						document.getElementById('formDisponibilidades:nomeSelecionado').innerHTML = '#{reserva.nome}';
						preencherHiddenChamarBotao('formDisponibilidades:mudarBotoes','formDisponibilidades:codigoReserva','#{reserva.codigo}');return false;"
						>
	        	
	        	<rich:column>
					<f:facet name="header">
						<h:outputText value="#{CElabels['entidade.horario']}"/>
					</f:facet>
					<h:outputText value="#{reserva.horarioInicialFormatado} - #{reserva.horarioFinalExibicaoFormatado}" />
				</rich:column>
				
				<rich:column>
					<f:facet name="header">
						<h:outputText value="#{CElabels['entidade.reserva.responsavel']}"/>
					</f:facet>
					<h:outputText value="#{reserva.responsavel}" />
				</rich:column>
				
				<rich:column>
					<f:facet name="header">
						<h:outputText value="#{CElabels['entidade.nome']}"/>
					</f:facet>
					<h:outputText value="#{reserva.nome}" />
				</rich:column>
				
				<rich:column>
					<f:facet name="header">
						<h:outputText value="#{CElabels['entidade.tel']}"/>
					</f:facet>
					<h:outputText value="#{reserva.validaCampoT}" />
				</rich:column>1
				
				<rich:column>
					<f:facet name="header">
						<h:outputText value="#{CElabels['entidade.tipo']}"/>
					</f:facet>
					<h:outputText value="#{reserva.tipo.descricao}" />
				</rich:column>
				
				<rich:column>
					<f:facet name="header">
						<h:outputText value="#{CElabels['entidade.convidados']}"/>
					</f:facet>
					<h:outputText value="#{reserva.nrConvidados}" />
				</rich:column>
				
				<rich:column>
					<f:facet name="header">
						<h:outputText value="#{CElabels['entidade.situacao']}"/>
					</f:facet>
					<h:outputText value="#{reserva.situacao.descricao}" />
				</rich:column>
				
				<rich:column>
					<f:facet name="header">
						<h:outputText value="#{CElabels['menu.cadastros.acessoSistema.usuario']}"/>
					</f:facet>
					<h:outputText value="#{reserva.nomeCliente}" />
				</rich:column>
				
	        </rich:dataTable>
	        </div>
	        <a4j:commandButton id="mudarBotoes" style="display: none;" reRender="panelBotoes, tipoSelecionado" action="#{ConsultaEventosControle.verificaDisponibilidade}"/>
			<h:inputHidden id="codigoReserva" value="#{ConsultaEventosControle.codigoReserva}" />
			<h:inputHidden id="horaMarcada" value="#{ConsultaEventosControle.horaMarcada}" />
	</h:form>
    </rich:modalPanel>
    
    <rich:modalPanel id="panelUsuarioSenha" autosized="true" shadowOpacity="true" width="450" height="250" onshow="document.getElementById('formUsuarioSenha:senha').focus()">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="#{CElabels['menu.operacoesCE.confirmacaocancelarVisita']}"/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:graphicImage value="/imagens/close.png" style="cursor:pointer" id="hidelinkUsuarioSenha"/>
                <rich:componentControl for="panelUsuarioSenha" attachTo="hidelinkUsuarioSenha" operation="hide"  event="onclick">

                </rich:componentControl>
            </h:panelGroup>
        </f:facet>
        <a4j:form id="formUsuarioSenha">
            <h:panelGrid columns="1"  width="100%" columnClasses="colunaCentralizada">
                <h:panelGrid columns="1" style="height:25px; background-image:url('../../../imagens/fundoBarraTopo.png');background-repeat: repeat-x;" columnClasses="colunaCentralizada" width="100%">
                    <h:outputText styleClass="tituloFormulario" value="#{CElabels['menu.operacoesCE.confirmacaocancelarVisita']}"/>
                    
                     
                </h:panelGrid>
                <h:panelGrid id="panelConfimacao" columns="1" width="100%" columnClasses="colunaEsquerda" styleClass="tabForm">
					<h:panelGroup>
						<h:inputText  style="opacity:0;height: 0px;" size="5" maxlength="7" />
						<h:inputSecret  size="14" maxlength="64" style="opacity:0;height: 0px;"/>
					</h:panelGroup>
                    <h:panelGroup >
                        <h:outputText styleClass="text"  value="C�digo:" />
                        <h:inputText id="codigoUsuario" size="3" maxlength="100" style="margin-left:5px" value="#{AgendaVisitaControle.responsavel.codigo}">
                            <a4j:support event="onchange" focus="formUsuarioSenha:senha" action="#{AgendaVisitaControle.consultarResponsavel}" reRender="panelConfimacao, mensagem"/>
                        </h:inputText>
                        <h:inputText id="autoCompleteHidden" size="3" maxlength="10" style="margin-left:6px;opacity:0;" value="#{AgendaVisitaControle.responsavel.username}"/>
                    </h:panelGroup>
                    <h:panelGroup >
                        <h:outputText styleClass="text"  value="Usu�rio:" />
                        <h:outputText styleClass="text" style="margin-left:6px"
                                      value="#{AgendaVisitaControle.responsavel.username}" />
                    </h:panelGroup>
                    <h:panelGroup>
                        <h:outputText styleClass="text"  value="Senha:"/>
                        <h:inputSecret autocomplete="off" id="senha" size="14" maxlength="64" style="margin-left:8px"
                                       value="#{AgendaVisitaControle.responsavel.senha}"/>
                        <rich:hotKey selector="#senha" key="return"
                                     handler="#{rich:element('loginCaixa')}.onclick();return false;"/>
                    </h:panelGroup>
                </h:panelGrid>
                <h:outputText id="mensagem" styleClass="mensagemDetalhada"
                              value="#{AgendaVisitaControle.mensagemDetalhada}"/>
                
                <a4j:commandButton action="#{AgendaVisitaControle.cancelarVisita}" image="/imagens/btn_Confirmar.png"
			 			reRender="msgDetalhada, msgDetalhada2, mensagem" value="#{msg_bt.btn_confirmar}" alt="#{msg.msg_gravar_dados}" styleClass="botoes">
		 		</a4j:commandButton>
		 		              
                
              </h:panelGrid>
        </a4j:form>
    </rich:modalPanel>
    
    <rich:modalPanel id="panelRemarcarVisita" autosized="true" shadowOpacity="true" width="450" height="250">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="#{CElabels['menu.operacoesCE.definirNovaDataVisita']}"/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:graphicImage value="/imagens/close.png" style="cursor:pointer" id="hidelinkRemarcarVisita"/>
                <rich:componentControl for="panelUsuarioSenha" attachTo="hidelinkRemarcarVisita" operation="hide"  event="onclick">

                </rich:componentControl>
            </h:panelGroup>
        </f:facet>
        <a4j:form id="formRemarcarVisita">
            <h:panelGrid columns="1"  width="100%" columnClasses="colunaCentralizada">
                <h:panelGrid columns="1" style="height:25px; background-image:url('../../../imagens/fundoBarraTopo.png');background-repeat: repeat-x;" columnClasses="colunaCentralizada" width="100%">
                    <h:outputText styleClass="tituloFormulario" value="#{CElabels['menu.operacoesCE.definirNovaDataVisita']}"/>                    
                </h:panelGrid>
                
                <h:panelGrid id="panelNovaDataVisita" columns="1" width="100%" columnClasses="colunaEsquerda" styleClass="tabForm">
                  						
					<h:panelGroup>
						<%@include file="../includes/include_obrigatorio.jsp" %>
						<h:outputText styleClass="tituloCampos" value="#{CElabels['entidade.agendaVisita.novaDataVisita']}"/>
						&nbsp;
						<rich:calendar id="dataVisita" styleClass="form"
							value="#{AgendaVisitaControle.agendaVisita.dataVisita}"
							datePattern="dd/MM/yyyy" inputSize="12" inputClass="form"
							oninputblur="blurinput(this);limparMsgObrig('form:dataVisitaInputDate', 'dataVisita');" 
							oninputfocus="focusinput(this);"
							onchanged="limparMsgObrig('form:dataVisitaInputDate', 'dataVisita');"
							oninputchange="return validar_Data(this.id);"
							oninputkeypress="return mascara(this, '99/99/9999', event);"
							enableManualInput="true" zindex="2" showWeeksBar="false"
							tabindex="1"> 
						</rich:calendar>&nbsp;
													
					</h:panelGroup>
			
                </h:panelGrid>
                
                
              </h:panelGrid>
              
              <center>
                <h:outputText id="mensagem3" styleClass="mensagemDetalhada"
                              value="#{AgendaVisitaControle.mensagemDetalhada}"/><br>
                
                <a4j:commandButton action="#{AgendaVisitaControle.remarcarVisita}"
			 			reRender="msgDetalhada, msgDetalhada2, mensagem3" value="Confirmar">
		 		</a4j:commandButton>		 		             
                &nbsp;
        		<a4j:commandButton reRender="msgDetalhada, msgDetalhada2, mensagem3" value="Cancelar"
        		oncomplete="Richfaces.hideModalPanel('panelRemarcarVisita');"/>
        		</center>
        </a4j:form>
    </rich:modalPanel>

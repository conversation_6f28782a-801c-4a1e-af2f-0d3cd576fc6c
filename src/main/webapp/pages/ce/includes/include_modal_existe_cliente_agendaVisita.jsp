<%@include file="../includes/include_imports.jsp" %>
<script type="text/javascript">
<!--
function chamarBotao(idBotao){
	var botao = document.getElementById(idBotao);
	botao.click();
}
//-->
</script>
<rich:modalPanel id="panelCadastroSimplificadoCliente" autosized="true"
	shadowOpacity="true"
	showWhenRendered="#{AgendaVisitaControle.exibir['modal']}"
	width="550" height="250"
	onshow="document.getElementById('formCadastroSimplificadoCliente:nomeCadastroSimplificadoCliente').focus();">

	<f:facet name="header">
		<h:panelGroup>
			<h:outputText
				value="Consultar Cliente #{CadastroInicialControle.exibir['modalCliente']}"/>
		</h:panelGroup>
	</f:facet>
	<f:facet name="controls">
		<h:panelGroup>
			<a4j:commandLink onclick="chamarBotao('formCadastroSimplificadoCliente:botaoNovo');">
				<h:graphicImage value="/imagens/close.png" style="border: 0px;"/>
			</a4j:commandLink>

		</h:panelGroup>
	</f:facet> 

	<a4j:form id="formCadastroSimplificadoCliente" ajaxSubmit="true">

		<h:panelGrid columns="1" width="100%">

			<h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar"
				columnClasses="classEsquerda, classDireita" width="100%">

				<h:outputText value="Nome Completo " />
				<h:panelGroup>
					<h:inputText id="nomeCadastroSimplificadoCliente" size="50"
						maxlength="50" onblur="blurinput(this);"
						onfocus="focusinput(this);" styleClass="form"
						value="#{AgendaVisitaControle.parametro.nomeCliente}" />
				</h:panelGroup>
				
				<h:outputText value="Telefone " />
				<h:panelGroup>
					<h:inputText id="telefoneConsulta" size="50"
						onblur="if(!validarFonePesquisa(this)){this.value='';};"
						maxlength="50"
						onkeypress="return mascara(this, '(99)999999999', event);"
						onfocus="focusinput(this);" styleClass="form"
						value="#{AgendaVisitaControle.parametro.telefone}" />
				</h:panelGroup>


				<h:outputText value="CPF " />
				<h:panelGroup>
					<h:inputText id="cpf" size="15" maxlength="15"
						onblur="blurinput(this);" onfocus="focusinput(this);"
						styleClass="form" value="#{AgendaVisitaControle.parametro.pessoa.cfp}"
						onkeypress="return mascara(this, '999.999.999-99', event);"/>
				</h:panelGroup>
				<div id="mensagemBusca" class="mensagemObrigatorio">
				</div>
			</h:panelGrid>

			<rich:dataTable id="itens" width="100%" headerClass="consulta"
				rowClasses="linhaImpar, linhaPar" columnClasses="colunaAlinhamento"
				value="#{AgendaVisitaControle.listaInteressados}"
				rendered="#{AgendaVisitaControle.apresentarResultadoConsulta}"
				rows="#{AgendaVisitaControle.nrPagina}" var="interessado">
				
				<h:column>
					<f:facet name="header">
						<h:outputText value="#{CElabels['entidade.pessoa.nomeCompleto']}" />
					</f:facet>
					<a4j:commandLink action="#{AgendaVisitaControle.escolhe}"
						reRender="panelCadastroSimplificadoCliente, panelDadosAgendaVisita"
						value="#{interessado.pessoa.nome}" title="#{msg.msg_editar_dados}"
						styleClass="botoes" />
				</h:column>
				
				
				<h:column>
					<f:facet name="header">
						<h:outputText value="#{CElabels['entidade.interessado.status']}" />
					</f:facet>
					<h:outputText value="#{interessado.status}" />
				</h:column>
				
				<h:column>
					<f:facet name="header">
						<h:outputText value="Telefones" />
					</f:facet>
					<h:outputText escape="false" value="#{interessado.telefones}" />
				</h:column>
				
				<h:column>
					<f:facet name="header">
						<h:outputText value="Eventos" />
					</f:facet>
					<h:outputText escape="false" value="#{interessado.nomesEventos}" />
				</h:column>
			</rich:dataTable>
			<h:panelGrid rendered="#{AgendaVisitaControle.apresentarResultadoConsulta}" columns="1"
                     columnClasses="colunaCentralizada" width="100%">
         <h:panelGroup>
            
<h:panelGrid columns="1"
                     columnClasses="colunaCentralizada" width="100%" >


<a4j:outputPanel id="painelPaginacao">
				<h:panelGroup id="painelPaginacaoManual"
					rendered="#{AgendaVisitaControle.confPaginacao.paginarBanco}">

					<a4j:commandLink id="pagiInicial" styleClass="tituloCampos"
						value="  <<  " reRender="itens,paginaatual,painelPaginacaoManual"
						rendered="#{AgendaVisitaControle.confPaginacao.apresentarPrimeiro}"
						actionListener="#{AgendaVisitaControle.consultarPaginadoListener}">
						<f:attribute name="pagNavegacao" value="pagInicial" />
					</a4j:commandLink>
					<a4j:commandLink id="pagiAnterior" styleClass="tituloCampos"
						value="  <  " reRender="itens,painelPaginacaoManual"
						rendered="#{AgendaVisitaControle.confPaginacao.apresentarAnterior}"
						actionListener="#{AgendaVisitaControle.consultarPaginadoListener}">
						<f:attribute name="pagNavegacao" value="pagAnterior" />
					</a4j:commandLink>
					<h:outputText id="paginaAtual" styleClass="tituloCampos"
						value="#{msg_aplic.prt_msg_pagina} #{AgendaVisitaControle.confPaginacao.paginaAtualDeTodas}"
						rendered="true" />
					<a4j:commandLink id="pagiPosterior" styleClass="tituloCampos"
						value="  >  " reRender="itens,painelPaginacaoManual"
						rendered="#{AgendaVisitaControle.confPaginacao.apresentarPosterior}"
						actionListener="#{AgendaVisitaControle.consultarPaginadoListener}">
						<f:attribute name="pagNavegacao" value="pagPosterior" />
					</a4j:commandLink>
					<a4j:commandLink id="pagiFinal" styleClass="tituloCampos"
						value="  >>  " reRender="itens,painelPaginacaoManual"
						rendered="#{AgendaVisitaControle.confPaginacao.apresentarUltimo}"
						actionListener="#{AgendaVisitaControle.consultarPaginadoListener}">
						<f:attribute name="pagNavegacao" value="pagFinal" />
					</a4j:commandLink>

					<h:outputText id="totalItens" styleClass="tituloCampos"
						value=" [#{msg_aplic.prt_msg_itens} #{AgendaVisitaControle.confPaginacao.numeroTotalItens}]"
						rendered="true" />

				</h:panelGroup>

			</a4j:outputPanel>
			</h:panelGrid>
			</h:panelGroup>
			</h:panelGrid>
		
			<h:panelGrid id="panelMensagem" columns="1" width="100%">
				<h:panelGrid columns="2" width="100%">
					<h:outputText id="msg" styleClass="mensagem"
						value="#{AgendaVisitaControle.mensagem}" />
					<h:outputText styleClass="mensagemDetalhada"
						value="#{AgendaVisitaControle.mensagemDetalhada}" />
						</h:panelGrid>
			</h:panelGrid>
		</h:panelGrid>
			<table width="100%" border="0" align="center" cellpadding="0"
				cellspacing="0">
				<tr>
					<td width="10"><img
						src="${contexto}/imagens/agenda_imgs/menu_esq.jpg" width="10"
						height="69"></td>
					<td background="${contexto}/imagens/agenda_imgs/menu_fundo.jpg">
					<table width="100%" border="0" cellspacing="0" cellpadding="0">
						<tr>
						<td align="center">
							<a4j:commandButton
								action="#{AgendaVisitaControle.consultaClienteInteressado}"
								reRender="panelCadastroSimplificadoCliente,panelDadosAgendaVisita"
								value="#{msg_bt.btn_editar}"
								image="/imagens/botoesCE/buscar.png"
								title="#{msg.msg_editar_dados}" styleClass="botoes" />
						</td>
						<td align="center">		
								
								<a4j:commandButton
								id="botaoNovo"
								action="#{CadastroInicialControle.abrirCadastroInicialSemModal}"
								reRender="panelCadastroSimplificadoCliente"
								title="#{CElabels['entidade.cadastroInicial.conversa']}"
								actionListener="#{CadastroInicialControle.selNomeClienteVisita}"
								image="/imagens/botoesCE/btnNovo.png">
								<f:attribute name="nomeCliente"
								value="#{AgendaVisitaControle.parametro.nomeCliente}" />
								<f:attribute name="codigoAmbienteInteresse"
								value="#{AgendaVisitaControle.agendaVisita.ambiente}" />
								<f:attribute name="dataInteresse"
								value="#{AgendaVisitaControle.agendaVisita.dataVisita}" />
								</a4j:commandButton>
						</td>		
						</tr>
					</table>
					</td>
					<td width="10"><img
						src="${contexto}/imagens/agenda_imgs/menu_dir.jpg" width="10"
						height="69"></td>
				</tr>
			</table>
	</a4j:form>
	<script>
	function exibirMensagemBusca(mensagem) {
		var divObgMensagem = document.getElementById('mensagemBusca');
		divObgMensagem.innerHTML = mensagem;
	}
	function validarBusca() {
		
		var nome = document.getElementById('formCadastroSimplificadoCliente:nomeCadastroSimplificadoCliente').value;
		var tel = document.getElementById('formCadastroSimplificadoCliente:telefoneConsulta').value;
		var cpf = document.getElementById('formCadastroSimplificadoCliente:cpf').value;
		var validade = true;
		
		
		if(nome == "" || nome == null ){
			if(tel == "" || tel == null){
				if (cpf == "" || cpf == null){
					exibirMensagemBusca('<h:outputText value="Informe um filtro para a pesquisa"/>');
				validade = false;
			}
			}
		}
		return validade;
	}
	function limparMensagemBusca() {
		var divObgMensagem = document.getElementById('mensagemBusca');
		divObgMensagem.innerHTML = "";
	}
	function validarFonePesquisa(telefone)
    {
		limparMensagemBusca();
        if (telefone.value.match('^\\(?(\\d{2})\\)[- ]?$')){
	         return true;
        }
        if (telefone.value.match('^\\(?(\\d{2})\\)?[- ]?(\\d{4})$')){
       	 return true;
        }
        if (telefone.value.match('^\\(?(\\d{2})\\)?[- ]?(\\d{4})[- ]?(\\d{4})$')){
       	 return true;
        }
        if (telefone.value.match('^\\(?(\\d{2})\\)?[- ]?(\\d{8})$')){
            return true;
        }
        if (telefone.value.match('^\\(?(\\d{2})\\)?[- ]?(\\d{9})$')){
            return true;
        }
        exibirMensagemBusca('<h:outputText value="Formato do telefone deve ser (XX), (XX)XXXX, (XX)XXXXXXXX ou (XX)XXXXXXXXX."/>');
        return false;
    }
	
	</script>
</rich:modalPanel>

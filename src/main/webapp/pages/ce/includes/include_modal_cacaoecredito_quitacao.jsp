<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>
<%@taglib prefix="jsfChart" uri="http://sourceforge.net/projects/jsf-comp" %>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>

<script>
function validarCaucaoECredito(){
	limparMensagem(['QuitacaoCaucaoECredito']);

	var itens = document.getElementsByName('formCacaoECreditoQuitacao:tipoCaucaoECreditoSelecionado');
	var selecionado = false;
	
	for (var i = 0; i < itens.length; i++) {
		if (itens[i].checked) {
			selecionado = true;
		}
	}
	if (!selecionado){		
		exibirMensagem(montarMsgObrigatoriedadeSelecao('<h:outputText value="#{CElabels[\'operacoes.negociacao.quitar.credito.caucao\']}"/>'), ['QuitacaoCaucaoECredito']);
		return false;
	}

	return true;
}
</script>

<rich:modalPanel id="panelCacaoECreditoQuitacao" autosized="true" shadowOpacity="true" width="420" height="220" domElementAttachment="parent">
       
       <f:facet name="controls">
           <h:panelGroup>
               <h:graphicImage value="/imagens/close.png" style="cursor:pointer" id="hidelinkCacaoECreditoQuitacao"/>
               <rich:componentControl for="panelCacaoECreditoQuitacao" attachTo="hidelinkCacaoECreditoQuitacao" operation="hide" event="onclick"/>
           </h:panelGroup>
       </f:facet>
       
	<a4j:form id="formCacaoECreditoQuitacao" ajaxSubmit="true">

	<a4j:keepAlive beanName="OrcamentoDetalhadoControle" />

	<h:panelGrid rowClasses="linhaImpar, linhaPar" width="100%" columns="1">

             <h:panelGroup>	
			<h:outputText styleClass="text" style="font-weight: bold;" value="#{CElabels['entidade.data.atual']}: "/>
			<h:outputText styleClass="text" value="#{CadastroInicialControle.dataAtual}"/>
             </h:panelGroup>
            <h:panelGroup>
                <h:outputText styleClass="text" style="font-weight: bold;" value="Valor a ser quitado: "/>
                        <h:outputText styleClass="text" value="#{OrcamentoDetalhadoControle.caucaoECredito.valorApresentar}"/>
             </h:panelGroup>
		
		<h:panelGroup>
			<c:if test="${OrcamentoDetalhadoControle.caucaoECredito.tipoCaucaoECredito.codigo eq 1}">
			<h:selectOneRadio id="tipoCaucaoECreditoSelecionado" value="#{OrcamentoDetalhadoControle.caucaoECredito.tipoQuitacao}" layout="pageDirection" styleClass="tituloCamposLeft">
				<f:selectItems value="#{OrcamentoDetalhadoControle.tiposQuitacaoCaucao}" />
			</h:selectOneRadio>
			</c:if>
			<c:if test="${OrcamentoDetalhadoControle.caucaoECredito.tipoCaucaoECredito.codigo eq 2}">
				<h:selectOneRadio id="tipoCaucaoECreditoSelecionado" value="#{OrcamentoDetalhadoControle.caucaoECredito.tipoQuitacao}"  layout="pageDirection" styleClass="tituloCamposLeft">
				<f:selectItems value="#{OrcamentoDetalhadoControle.tiposQuitacaoCredito}" />
				</h:selectOneRadio>
			</c:if>
		</h:panelGroup>        		
	</h:panelGrid>
	
	<center>
		<div id="divObg-QuitacaoCaucaoECredito" class="mensagemObrigatorio"></div><br>
        <a4j:commandButton id="confirmarQuitacaoCaucaoECredito" reRender="dttaCaucaoCredito, detalhamentoNegociacao" 
        value="Confirmar" image="/imagens/botoesCE/confirmar.png"
        onclick="if(!validarCaucaoECredito()){return false;};"
        action="#{OrcamentoDetalhadoControle.salvarQuitacaoCredito}"
        oncomplete="#{OrcamentoDetalhadoControle.msgAlert}"/>
        &nbsp;
        <a4j:commandButton id="cancelarQuitacaoCaucaoECredito" reRender="dttaCaucaoCredito, detalhamentoNegociacao" 
        value="Cancelar" image="/imagens/botoesCE/cancelar.png"
        oncomplete="Richfaces.hideModalPanel('panelCacaoECreditoQuitacao');"/>
      </center> 
       </a4j:form>
   </rich:modalPanel>
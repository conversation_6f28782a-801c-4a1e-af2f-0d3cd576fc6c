<!-- inicio box -->

<%@page contentType="text/html"%>
<%@page pageEncoding="ISO-8859-1"%>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<f:loadBundle var="msg_menu" basename="propriedades.Menu"/>
<!-- inicio box -->

<h:panelGroup layout="block" styleClass="menuLateral">

    <h:panelGroup layout="block" styleClass="grupoMenuLateral">
        <h:panelGroup layout="block" styleClass="grupoMenuTitulo">
            <i class="fa-icon-list"></i> Seção
        </h:panelGroup>

        <h:panelGroup layout="block" styleClass="grupoMenuItem">
            <a href="telaCadastroAuxiliares.jsp"
                             class="titulo3 linkFuncionalidade">
                <h:outputText value="#{CElabels['menu.cadastros.cadastrosAuxiliares']}"/>
            </a>

            <h:outputLink styleClass="linkWiki"
                          value="#{SuperControle.urlWikiCE}Cadastros:CadastroAuxiliares"
                          title="Clique e saiba mais: Cadastros Auxiliares"
                          target="_blank">
                <i class="fa-icon-question-sign" style="font-size: 18px"></i>
            </h:outputLink>
        </h:panelGroup>

        <h:panelGroup layout="block" styleClass="grupoMenuItem">
            <a href="telaCadastroProdutosPerfis.jsp"
               class="titulo3 linkFuncionalidade">
                <h:outputText value="#{CElabels['menu.cadastros.produtosPerfis']}"/>
            </a>

            <h:outputLink styleClass="linkWiki"
                          value="#{SuperControle.urlWikiCE}Cadastros:ProdutoPerfisEvento"
                          title="Clique e saiba mais: Produtos/Perfis de Eventos"
                          target="_blank">
                <i class="fa-icon-question-sign" style="font-size: 18px"></i>
            </h:outputLink>
        </h:panelGroup>

        <h:panelGroup layout="block" styleClass="grupoMenuItem">
            <h:commandLink id="financeiro"
               action="#{NavegacaoControle.abrirFinanceiro}"
               styleClass="titulo3 linkFuncionalidade">
                <h:outputText value="#{CElabels['menu.operacoesCE.financeiro']}"/>
            </h:commandLink>

            <h:outputLink styleClass="linkWiki"
                          value="#{SuperControle.urlWikiCE}Cadastros:Financeiro"
                          title="Clique e saiba mais: Financeiro"
                          target="_blank">
                <i class="fa-icon-question-sign" style="font-size: 18px"></i>
            </h:outputLink>
        </h:panelGroup>

    </h:panelGroup>

</h:panelGroup>
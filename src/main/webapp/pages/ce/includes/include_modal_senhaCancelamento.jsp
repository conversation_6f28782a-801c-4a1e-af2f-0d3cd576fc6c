
    <rich:modalPanel id="panelSenhaCancelamento" autosized="true" shadowOpacity="true" width="450" height="250" onshow="document.getElementById('formSenhaCancelamento:senha').focus()">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="#{CElabels['menu.operacoesCE.operacoes.cancelamentoEvento']}"/>
                
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:graphicImage value="/imagens/close.png" style="cursor:pointer" id="hidelinkSenhaCancelamento"/>
                <rich:componentControl for="panelSenhaCancelamento" attachTo="hidelinkSenhaCancelamento" operation="hide"  event="onclick">

                </rich:componentControl>
            </h:panelGroup>
        </f:facet>
        <a4j:form id="formSenhaCancelamento">
            <h:panelGrid columns="1"  width="100%" columnClasses="colunaCentralizada">
                <h:panelGrid columns="1" style="height:25px; background-image:url('../../../imagens/fundoBarraTopo.png');background-repeat: repeat-x;" columnClasses="colunaCentralizada" width="100%">
                    <h:outputText styleClass="tituloFormulario" value="#{CElabels['menu.operacoesCE.operacoes.cancelamentoEvento']}"/>
                </h:panelGrid>
                <h:panelGrid id="panelConfimacao" columns="1" width="100%" columnClasses="colunaEsquerda" styleClass="tabForm">
                    <h:panelGroup>
                        <h:inputText  style="opacity:0;height: 0px;" size="5" maxlength="7" />
                        <h:inputSecret  size="14" maxlength="64" style="opacity:0;height: 0px;"/>
                    </h:panelGroup>
                    <h:panelGroup >
                        <h:outputText styleClass="text"  value="C�digo:" />
                        <h:inputText id="codigoUsuario" size="3" maxlength="100" style="margin-left:5px" value="#{CancelamentoEventoControle.responsavelCancelamento.codigo}">
                            <a4j:support event="onchange" focus="formUsuarioSenha:senha" action="#{CancelamentoEventoControle.consultarResponsavel}" reRender="panelConfimacao, mensagem"/>
                        </h:inputText>
                        <h:inputText id="autoCompleteHidden" size="3" maxlength="10" style="margin-left:6px;opacity:0;" value="#{CancelamentoEventoControle.responsavelCancelamento.username}"/>
                    </h:panelGroup>
                    <h:panelGroup >
                        <h:outputText styleClass="text"  value="Usu�rio:" />
                        <h:outputText styleClass="text" style="margin-left:6px"
                                      value="#{CancelamentoEventoControle.responsavelCancelamento.username}" />
                    </h:panelGroup>
                    <h:panelGroup>
                        <h:outputText styleClass="text"  value="Senha:"/>
                        <h:inputSecret autocomplete="off" id="senha" size="14" maxlength="64" style="margin-left:8px"
                                       value="#{CancelamentoEventoControle.responsavelCancelamento.senha}"/>
                    </h:panelGroup>
                </h:panelGrid>
                <h:outputText id="mensagem" styleClass="mensagemDetalhada"
                              value="#{CancelamentoEventoControle.mensagemDetalhada}"/>
                
                <c:if test="${not CancelamentoEventoControle.estornar}">
                	<a4j:commandButton value="#{msg_bt.btn_confirmar}"
						image="/imagens/botoesCE/confirmar.png" alt="#{msg.msg_gravar_dados}"
						oncomplete="Richfaces.hideModalPanel('panelSenhaCancelamento'); Richfaces.showModalPanel('panelTextoCancelamento');"
						reRender="panelConfimacao,mensagens" />
                </c:if>
                
                
                <c:if test="${CancelamentoEventoControle.estornar}">
				<c:if test="${CancelamentoEventoControle.parcelasPagas}">
					<a4j:commandButton value="#{msg_bt.btn_confirmar}"
						image="/imagens/botoesCE/confirmar.png" alt="#{msg.msg_gravar_dados}"
						onclick="if(!confirm('#{Mensagens['operacoes.cancelamento.confirmarParcelas']}')) {return false;}"
						action="#{CancelamentoEventoControle.cancelamentoEstorno}"
						oncomplete="Richfaces.hideModalPanel('panelSenhaCancelamento')"
						reRender="panelConfimacao,mensagens" 
						actionListener="#{CancelamentoEventoControle.autorizacao}">
						<!-- funcao.estorno -->
									<f:attribute name="funcao" value="103" />
					</a4j:commandButton>
				</c:if>
				<c:if test="${not CancelamentoEventoControle.parcelasPagas }">
					<a4j:commandButton value="#{msg_bt.btn_confirmar}"
						image="/imagens/botoesCE/confirmar.png" alt="#{msg.msg_gravar_dados}"
						action="#{CancelamentoEventoControle.cancelamentoEstorno}"
						oncomplete="Richfaces.hideModalPanel('panelSenhaCancelamento')"
						reRender="panelConfimacao,mensagens"
						actionListener="#{CancelamentoEventoControle.autorizacao}">
						<!-- funcao.estorno -->
									<f:attribute name="funcao" value="103" />
					</a4j:commandButton>
				</c:if>
			</c:if>
            </h:panelGrid>
        </a4j:form>
    </rich:modalPanel>

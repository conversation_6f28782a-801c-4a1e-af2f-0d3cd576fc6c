<!-- inicio box -->

<%@page contentType="text/html"%>
<%@page pageEncoding="ISO-8859-1"%>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<f:loadBundle var="msg_menu" basename="propriedades.Menu"/>
<!-- inicio box -->

<h:panelGroup layout="block" styleClass="menuLateral">

    <h:panelGroup layout="block" styleClass="grupoMenuLateral">
        <h:panelGroup layout="block" styleClass="grupoMenuTitulo">
            <i class="fa-icon-list"></i> Cadastros Auxiliares
        </h:panelGroup>

        <h:panelGroup layout="block" styleClass="grupoMenuItem">
            <a4j:commandLink value="#{CElabels['menu.cadastros.categoriaCliente']}"
                             styleClass="titulo3 linkFuncionalidade"
                             action="#{FuncionalidadeControle.abrirComCasoNavegacao}"
                             actionListener="#{FuncionalidadeControle.prepararFuncionalidade}"
                             oncomplete="#{FuncionalidadeControle.abrirPopUp}">
                <f:attribute name="funcionalidade" value="CATEGORIA_CLIENTE_CE" />
            </a4j:commandLink>

            <h:outputLink styleClass="linkWiki"
                          value="#{SuperControle.urlWiki}Cadastros:Cadastros_Auxiliares:Categoria_de_Clientes"
                          title="Clique e saiba mais: Categoria de cliente"
                          target="_blank">
                <i class="fa-icon-question-sign" style="font-size: 18px"></i>
            </h:outputLink>
        </h:panelGroup>

        <h:panelGroup layout="block" styleClass="grupoMenuItem">
            <a4j:commandLink value="#{CElabels['menu.cadastros.cliente']}"
                             styleClass="titulo3 linkFuncionalidade"
                             action="#{FuncionalidadeControle.abrirComCasoNavegacao}"
                             actionListener="#{FuncionalidadeControle.prepararFuncionalidade}"
                             oncomplete="#{FuncionalidadeControle.abrirPopUp}">
                <f:attribute name="funcionalidade" value="CLIENTE_CE" />
            </a4j:commandLink>

            <h:outputLink styleClass="linkWiki"
                          value="#{SuperControle.urlBaseConhecimento}como-excluir-cadastro-de-pessoas-alunos/"
                          title="Clique e saiba mais: Cliente"
                          target="_blank">
                <i class="fa-icon-question-sign" style="font-size: 18px"></i>
            </h:outputLink>
        </h:panelGroup>

        <h:panelGroup layout="block" styleClass="grupoMenuItem">
            <a4j:commandLink value="#{CElabels['menu.cadastros.colaborador']}"
                             styleClass="titulo3 linkFuncionalidade"
                             action="#{FuncionalidadeControle.abrirComCasoNavegacao}"
                             actionListener="#{FuncionalidadeControle.prepararFuncionalidade}"
                             oncomplete="#{FuncionalidadeControle.abrirPopUp}">
                <f:attribute name="funcionalidade" value="COLABORADOR_CE" />
            </a4j:commandLink>

            <h:outputLink styleClass="linkWiki"
                          value="#{SuperControle.urlBaseConhecimento}como-cadastrar-um-novo-colaborador/"
                          title="Clique e saiba mais: Colaborador"
                          target="_blank" >
                <i class="fa-icon-question-sign" style="font-size: 18px"></i>
            </h:outputLink>
        </h:panelGroup>

        <h:panelGroup layout="block" styleClass="grupoMenuItem">
            <a4j:commandLink value="#{CElabels['menu.cadastros.grauInstrucao']}"
                             styleClass="titulo3 linkFuncionalidade"
                             action="#{FuncionalidadeControle.abrirComCasoNavegacao}"
                             actionListener="#{FuncionalidadeControle.prepararFuncionalidade}"
                             oncomplete="#{FuncionalidadeControle.abrirPopUp}">
                <f:attribute name="funcionalidade" value="GRAU_DE_INSTRUCAO_CE" />
            </a4j:commandLink>

            <h:outputLink styleClass="linkWiki"
                          value="#{SuperControle.urlWiki}Cadastros:Cadastros_Auxiliares:Grau_de_Instrucao"
                          title="Clique e saiba mais: Grau de instrução"
                          target="_blank" >
                <i class="fa-icon-question-sign" style="font-size: 18px"></i>
            </h:outputLink>
        </h:panelGroup>

        <h:panelGroup layout="block" styleClass="grupoMenuItem">
            <a4j:commandLink value="#{CElabels['menu.cadastros.profissao']}"
                             styleClass="titulo3 linkFuncionalidade"
                             action="#{FuncionalidadeControle.abrirComCasoNavegacao}"
                             actionListener="#{FuncionalidadeControle.prepararFuncionalidade}"
                             oncomplete="#{FuncionalidadeControle.abrirPopUp}">
                <f:attribute name="funcionalidade" value="PROFISSAO_CE" />
            </a4j:commandLink>

            <h:outputLink styleClass="linkWiki"
                          value="#{SuperControle.urlWiki}Cadastros:Cadastros_Auxiliares:Profissao"
                          title="Clique e saiba mais: Profissão"
                          target="_blank">
                <i class="fa-icon-question-sign" style="font-size: 18px"></i>
            </h:outputLink>
        </h:panelGroup>

        <h:panelGroup layout="block" styleClass="grupoMenuItem">

            <a4j:commandLink value="#{CElabels['menu.cadastros.pais']}"
                             styleClass="titulo3 linkFuncionalidade"
                             action="#{FuncionalidadeControle.abrirComCasoNavegacao}"
                             actionListener="#{FuncionalidadeControle.prepararFuncionalidade}"
                             oncomplete="#{FuncionalidadeControle.abrirPopUp}">
                <f:attribute name="funcionalidade" value="PAIS_CE" />
            </a4j:commandLink>

            <h:outputLink styleClass="linkWiki"
                          value="#{SuperControle.urlWiki}Cadastros:Cadastros_Auxiliares:Pais"
                          title="Clique e saiba mais: País"
                          target="_blank">
                <i class="fa-icon-question-sign" style="font-size: 18px"></i>
            </h:outputLink>
        </h:panelGroup>

        <h:panelGroup layout="block" styleClass="grupoMenuItem">

            <a4j:commandLink value="#{CElabels['menu.cadastros.cidade']}"
                             styleClass="titulo3 linkFuncionalidade"
                             action="#{FuncionalidadeControle.abrirComCasoNavegacao}"
                             actionListener="#{FuncionalidadeControle.prepararFuncionalidade}"
                             oncomplete="#{FuncionalidadeControle.abrirPopUp}">
                <f:attribute name="funcionalidade" value="CIDADE_CE" />
            </a4j:commandLink>

            <h:outputLink styleClass="linkWiki"
                          value="#{SuperControle.urlWiki}Cadastros:Cadastros_Auxiliares:Cidade"
                          title="Clique e saiba mais: Cidade"
                          target="_blank">
                <i class="fa-icon-question-sign" style="font-size: 18px"></i>
            </h:outputLink>

        </h:panelGroup>

    </h:panelGroup>

</h:panelGroup>
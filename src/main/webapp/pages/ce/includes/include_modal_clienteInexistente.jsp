
    <rich:modalPanel showWhenRendered="#{OrcamentoDetalhadoControle.clienteInexistente}" id="panelClienteInexistente" autosized="true" shadowOpacity="true" width="380" height="200">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="#{CElabels['entidade.preenchaCadastroCliente']}"></h:outputText>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:graphicImage value="/imagens/close.png" style="cursor:pointer" id="hidelinkClienteInexistente"/>
                <rich:componentControl for="panelClienteInexistente" attachTo="hidelinkClienteInexistente" operation="hide"  event="onclick">

                </rich:componentControl>
            </h:panelGroup>
        </f:facet>
        <a4j:form id="formClienteInexistente">
            <h:panelGrid columns="1"  width="100%" columnClasses="colunaCentralizada">
                <h:panelGrid columns="1" style="height:25px; background-image:url('../../../imagens/fundoBarraTopo.png');background-repeat: repeat-x;" columnClasses="colunaCentralizada" width="100%">
                    <h:outputText styleClass="tituloFormulario" value="#{CElabels['entidade.preenchaCadastroCliente']}"/>
                </h:panelGrid>
                <h:panelGrid id="panelConfimacao" columns="1" width="100%">
                     <h:outputText id="mensagem" styleClass="mensagemDetalhada"
                              value="#{OrcamentoDetalhadoControle.mensagem}"/>
                </h:panelGrid>
		</h:panelGrid>
		&nbsp;
		<table width="100%" border="0" align="center" cellpadding="0"
			cellspacing="0">
			<tr>
				<td width="10"><img
					src="${contexto}/imagens/agenda_imgs/menu_esq.jpg" width="10"
					height="69"></td>
				<td background="${contexto}/imagens/agenda_imgs/menu_fundo.jpg">
				<table width="100%" border="0" cellspacing="0" cellpadding="0">
					<tr>
						<td align="center"><a4j:commandLink action="#{OrcamentoDetalhadoControle.fecharModalClienteInexistente}"
							onclick="setDocumentCookie('popupsImportante', '',1);Richfaces.hideModalPanel('panelClienteInexistente');abrirPopup('#{contexto}/faces/faces/clienteForm.jsp?modulo=centralEventos', 'Cliente', 780, 595);">
							<img src="${contexto}/imagens/bt_terminarcadastro.jpg"
								 border="0">
						</a4j:commandLink></td>
					</tr>
				</table>
				</td>
				<td width="30"><img
					src="${contexto}/imagens/agenda_imgs/menu_dir.jpg" width="10"
					height="69"></td>
			</tr>
		</table>
	</a4j:form>
    </rich:modalPanel>
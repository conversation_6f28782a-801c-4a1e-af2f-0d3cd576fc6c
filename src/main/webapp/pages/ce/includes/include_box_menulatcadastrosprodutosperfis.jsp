
<%@page contentType="text/html"%>
<%@page pageEncoding="ISO-8859-1"%>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<f:loadBundle var="msg_menu" basename="propriedades.Menu"/>
<!-- inicio box -->

<h:panelGroup layout="block" styleClass="menuLateral">

    <h:panelGroup layout="block" styleClass="grupoMenuLateral">
        <h:panelGroup layout="block" styleClass="grupoMenuTitulo">
            <i class="fa-icon-list"></i> Produtos
        </h:panelGroup>

        <h:panelGroup layout="block" styleClass="grupoMenuItem">
            <a4j:commandLink  value="#{CElabels['menu.cadastros.produtos.produtosLocacao']}"
                              styleClass="titulo3 linkFuncionalidade"
                              action="#{FuncionalidadeControle.abrirComCasoNavegacao}"
                              actionListener="#{FuncionalidadeControle.prepararFuncionalidade}"
                              oncomplete="#{FuncionalidadeControle.abrirPopUp}">
                <f:attribute name="funcionalidade" value="PRODUTO_LOCACAO" />
            </a4j:commandLink>
            <h:outputLink styleClass="linkWiki"
                          value="#{SuperControle.urlWikiCE}Cadastros:PP:Produto"
                          title="Clique e saiba mais: Produto"
                          target="_blank">
                <i class="fa-icon-question-sign" style="font-size: 18px"></i>
            </h:outputLink>
        </h:panelGroup>

        <h:panelGroup layout="block" styleClass="grupoMenuItem">
            <a4j:commandLink  value="#{CElabels['menu.cadastros.produtos.servicos']}"
                              styleClass="titulo3 linkFuncionalidade"
                              action="#{FuncionalidadeControle.abrirComCasoNavegacao}"
                              actionListener="#{FuncionalidadeControle.prepararFuncionalidade}"
                              oncomplete="#{FuncionalidadeControle.abrirPopUp}">
                <f:attribute name="funcionalidade" value="SERVICOS" />
            </a4j:commandLink>
            <h:outputLink styleClass="linkWiki"
                          value="#{SuperControle.urlWikiCE}Cadastros:PP:Serviço"
                          title="Clique e saiba mais: Serviço"
                          target="_blank">
                <i class="fa-icon-question-sign" style="font-size: 18px"></i>
            </h:outputLink>
        </h:panelGroup>

        <h:panelGroup layout="block" styleClass="grupoMenuItem">
            <a4j:commandLink  value="#{CElabels['menu.cadastros.produtos.fornecedor']}"
                              styleClass="titulo3 linkFuncionalidade"
                              action="#{FuncionalidadeControle.abrirComCasoNavegacao}"
                              actionListener="#{FuncionalidadeControle.prepararFuncionalidade}"
                              oncomplete="#{FuncionalidadeControle.abrirPopUp}">
                <f:attribute name="funcionalidade" value="FORNECEDOR_CE" />
            </a4j:commandLink>
            <h:outputLink styleClass="linkWiki"
                          value="#{SuperControle.urlWikiCE}Cadastros:PP:Fornecedor"
                          title="Clique e saiba mais: Fornecedor"
                          target="_blank">
                <i class="fa-icon-question-sign" style="font-size: 18px"></i>
            </h:outputLink>
        </h:panelGroup>

        <h:panelGroup layout="block" styleClass="grupoMenuItem">
            <a4j:commandLink  value="#{CElabels['menu.cadastros.perfisEventos.tipoAmbiente']}"
                              styleClass="titulo3 linkFuncionalidade"
                              action="#{FuncionalidadeControle.abrirComCasoNavegacao}"
                              actionListener="#{FuncionalidadeControle.prepararFuncionalidade}"
                              oncomplete="#{FuncionalidadeControle.abrirPopUp}">
                <f:attribute name="funcionalidade" value="TIPO_AMBIENTE" />
            </a4j:commandLink>
            <h:outputLink styleClass="linkWiki"
                          value="#{SuperControle.urlWikiCE}Cadastros:PP:Tipo_Ambiente"
                          title="Clique e saiba mais: Tipo de Ambiente"
                          target="_blank">
                <i class="fa-icon-question-sign" style="font-size: 18px"></i>
            </h:outputLink>
        </h:panelGroup>

        <h:panelGroup layout="block" styleClass="grupoMenuItem">
            <a4j:commandLink  value="#{CElabels['menu.cadastros.perfisEventos.ambiente']}"
                              styleClass="titulo3 linkFuncionalidade"
                              action="#{FuncionalidadeControle.abrirComCasoNavegacao}"
                              actionListener="#{FuncionalidadeControle.prepararFuncionalidade}"
                              oncomplete="#{FuncionalidadeControle.abrirPopUp}">
                <f:attribute name="funcionalidade" value="AMBIENTE_CE" />
            </a4j:commandLink>
            <h:outputLink styleClass="linkWiki"
                          value="#{SuperControle.urlWikiCE}Cadastros:PP:Ambiente"
                          title="Clique e saiba mais: Ambiente"
                          target="_blank">
                <i class="fa-icon-question-sign" style="font-size: 18px"></i>
            </h:outputLink>
        </h:panelGroup>

        <h:panelGroup layout="block" styleClass="grupoMenuItem">
            <a4j:commandLink  value="Perfil Evento"
                              styleClass="titulo3 linkFuncionalidade"
                              action="#{FuncionalidadeControle.abrirComCasoNavegacao}"
                              actionListener="#{FuncionalidadeControle.prepararFuncionalidade}"
                              oncomplete="#{FuncionalidadeControle.abrirPopUp}">
                <f:attribute name="funcionalidade" value="PERFIL_EVENTO" />
            </a4j:commandLink>
            <h:outputLink styleClass="linkWiki"
                          value="#{SuperControle.urlWikiCE}Cadastros:PP:Tabela_Pre%C3%A7o_Perfil_Evento"
                          title="Clique e saiba mais: Tabela de Preços/Perfil Evento"
                          target="_blank">
                <i class="fa-icon-question-sign" style="font-size: 18px"></i>
            </h:outputLink>
        </h:panelGroup>
    </h:panelGroup>
</h:panelGroup>
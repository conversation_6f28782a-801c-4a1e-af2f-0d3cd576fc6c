<a4j:commandButton id="voltar" action="#{NavegacaoControle.abrirTelaExibeDetalhamentoEvento}"
							   value="#{CElabels['operacoes.voltar']}" alt="#{CElabels['operacoes.negociacao.fechar']}" styleClass="botoes"/>
<h:panelGrid id="conteudo" columns="2">
		<rich:column width="60%" style="border: 0px;">	
					<h:panelGroup>   
							<img src="${contexto}/images/arrow2.gif" width="16" height="16" style="vertical-align:middle;margin-right:6px;">
							<h:outputText styleClass="tituloCampos" value="#{CElabels['entidade.filtroPesquisa']}:"/>
							<div class="sep" style="margin:4px 0 5px 0;"><img src="${contexto}/images/shim.gif"></div>
					</h:panelGroup>		
				<h:panelGroup>
				<table class="tablelistras" border="0" width="100%" style="margin-bottom:5px; text-align: left; vertical-align: top; padding: 0;">
						<tr>
							<td colspan="2">
								<h:selectBooleanCheckbox  styleClass="campos" value=""/>
								<h:outputText styleClass="text" style="font-weight: bold" value="#{CElabels['operacoes.consulta.consultarTodosHoje']}: "/>
								
								<h:selectBooleanCheckbox  styleClass="campos" value=""/>
								<h:outputText styleClass="text" style="font-weight: bold" value="#{CElabels['operacoes.consulta.consultarTodasSemana']}: "/>
							</td>
						</tr>
						<tr>
							<td background="black">
								<h:outputText styleClass="text" style="font-weight: bold" value="#{CElabels['menu.cadastros.cliente']}: "/>
							</td>
							<td>
								<h:inputText id="horarioFinal" onblur="blurinput(this);" onfocus="focusinput(this);"
									     styleClass="form" maxlength="7" value="" size="50" />
							</td>
						</tr>
						<tr >
							<td style="background-color: #D7D7D7">
								<h:outputText styleClass="text" style="font-weight: bold" value="#{CElabels['entidade.peridoVencimentoParcela']}: "/>
							</td>
							<td style="background-color: #D7D7D7">
								<h:outputText styleClass="text" style="font-weight: bold" value="#{CElabels['entidade.data.de']} "/>
								<rich:calendar id="dataInicial" styleClass="form" value="#{OrcamentoDetalhadoControle.negociacaoEvento.dataEvento}"
			                			datePattern="dd/MM/yyyy" inputSize="10" inputClass="form"
			                			oninputblur="blurinput(this); limparMsgObrig('form:dataValidadeInputDate', ['dataValidade','dataValidade-msgs']);"
			                			oninputfocus="focusinput(this);" oninputchange="if(!validar_Data(this.id)) {this.value = ''};"
			                			oninputkeypress="return mascara(this, '99/99/9999', event);" enableManualInput="true" zindex="2" showWeeksBar="false">
									<a4j:support event="onchanged" reRender="comboPerfilEvento"></a4j:support>
								</rich:calendar>
								<h:outputText styleClass="text" style="font-weight: bold" value="#{CElabels['entidade.dataAte']}: "/>
								<rich:calendar id="d" styleClass="form" value="#{OrcamentoDetalhadoControle.negociacaoEvento.dataEvento}"
			                			datePattern="dd/MM/yyyy" inputSize="10" inputClass="form"
			                			oninputblur="blurinput(this); limparMsgObrig('form:dataValidadeInputDate', ['dataValidade','dataValidade-msgs']);"
			                			oninputfocus="focusinput(this);" oninputchange="if(!validar_Data(this.id)) {this.value = ''};"
			                			oninputkeypress="return mascara(this, '99/99/9999', event);" enableManualInput="true" zindex="2" showWeeksBar="false">
									<a4j:support event="onchanged" reRender="comboPerfilEvento"></a4j:support>
								</rich:calendar>
							</td>
						</tr>
						<tr>
							<td colspan="2">
								<center>
									<h:commandLink id="buscar" onclick="if(!confirm('#{Mensagens['operacoes.cancelamento.confirmar']}')) {return false;}"
								   		   action="#">
										<h:graphicImage value="/imagens/botaoConsultar.png" style="border: 0px; margin-bottom: 5px;"
											   styleClass="imgalpha" title="#{CElabels['operacoes.buscar']}" height="25"/>
					 				</h:commandLink>&nbsp;
					 				<h:commandLink id="limparBusca" onclick="if(!confirm('#{Mensagens['operacoes.cancelamento.confirmar']}')) {return false;}"
								   		   action="#">
										<h:graphicImage value="/images/botaoLimparBusca.png" style="border: 0px; margin-bottom: 5px;"
											   styleClass="imgalpha" title="#{CElabels['operacoes.limparBusca']}" height="25"/>
					 				</h:commandLink>
								</center>
							</td>
						</tr>
					  </table>
				  </h:panelGroup>
		</rich:column>
		<rich:column styleClass="sombrapreview" width="40%" style="border: 0px; text-align: left; vertical-align: top; padding:10px;">
				<h:panelGrid id="detalhamentoNegociacao" columns="1" width="100%" border="0" cellpadding="0" cellspacing="0">
					<rich:column style="border: 0px;">
						<h:panelGrid styleClass="tablepreviewtotal" style="text-align: right;" columns="1" width="100%" border="0" cellpadding="0" cellspacing="0">
							<h:panelGroup>
								<h:outputText styleClass="text" style="font-weight: bold" value="#{CElabels['entidade.pagamento.responsavalPagamento']}:"/>
								<h:selectOneMenu onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form">	
                    			</h:selectOneMenu>
					   		</h:panelGroup>
					   	</h:panelGrid>	
		       	   </rich:column>
				  <rich:column style="border: 0px;">
						<h:panelGrid styleClass="tablepreviewtotal" style="text-align: right;" columns="1" width="100%" border="0" cellpadding="0" cellspacing="0">
							<h:panelGroup>
								<h:outputText value="#{CElabels['entidade.total']}"/> =  <h:outputText styleClass="verde" value="#{OrcamentoDetalhadoControle.negociacaoEvento.valorFinalFormatado}"/>
							</h:panelGroup>
						</h:panelGrid>
				  </rich:column>
				  <rich:column style="border: 0px;">
						<div style="clear: both; margin-top: 10px; text-align: right;">
							<h:commandLink id="receber"  action="#{OrcamentoDetalhadoControle.receber}">
									<h:graphicImage value="/images/btn_receber.gif" style="border: 0px; margin-bottom: 10px;"
										   styleClass="imgalpha" title="#{CElabels['operacoes.receber']}" width="111" height="41"/>
				 			</h:commandLink>						
						</div>
				 </rich:column>
			</h:panelGrid>
		</rich:column>
</h:panelGrid>
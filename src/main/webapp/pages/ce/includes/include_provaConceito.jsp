<h:panelGrid id="panelDadosBasicos" columns="1" width="100%"
	headerClass="subordinado" columnClasses="colunaCentralizada">
	
	<rich:simpleTogglePanel switchType="client" opened="false" id="stp1">
		<f:facet name="header">
			<h:outputText value="A��es da Prova de Conceitos" />
		</f:facet>
		<fieldset style="height:35px;">
		    <h:commandButton id="salvar" action="#{ProvaConceitoDAO.salvar}"
				value="#{CElabels['operacoes.gravar']}" styleClass="botoes" />
			</fieldset>	
	</rich:simpleTogglePanel>
	<rich:simpleTogglePanel switchType="client" opened="true" id="stp2">
		<f:facet name="header">
			<h:outputText value="Dados da Prova de Conceitos" />
		</f:facet>
		<h:panelGrid rowClasses="linhaImpar, linhaPar"
			columnClasses="classEsquerda, classDireita"
			style="background-image:url('./imagens/backGroundTelaBasico.png'); background-repeat: no-repeat;padding: 8px;"
			width="100%" columns="2">
		<h:panelGroup>
				<h:outputText styleClass="obrigatorio" value="*" />
				<h:outputText styleClass="tituloCampos"
					value="Data" />
			</h:panelGroup>
			<h:panelGroup>
				<rich:calendar id="data" styleClass="form"
					value="#{ProvaConceitoDAO.provaconceito.data}"
					datePattern="dd/MM/yyyy" inputSize="10" inputClass="form"
					
					enableManualInput="true" zindex="2" showWeeksBar="false" />
				<div id="divObg-dataInicio" class="mensagemObrigatorio"></div>
			</h:panelGroup>
			<h:panelGroup>
				<h:outputText styleClass="obrigatorio" value="*" />
				<h:outputText styleClass="tituloCampos" value="Nome" />
			</h:panelGroup>
			<h:panelGroup>
				<h:inputText onblur="blurinput(this);" onfocus="focusinput(this);"
					styleClass="form" size="30" maxlength="25"
					style="border: 1px solid #8eb3c3"
					value="#{ProvaConceitoDAO.provaconceito.nome}" id="nome"></h:inputText>
				<div id="divObg-dataInicio" class="mensagemObrigatorio"></div>
			</h:panelGroup>
			<h:panelGroup>
				<h:outputText styleClass="tituloCampos" value="Descri��o" />
			</h:panelGroup>
			<h:panelGroup>
				<h:inputTextarea rows="3" cols="30" onblur="blurinput(this);"
					onfocus="focusinput(this);" style="border: 1px solid #8eb3c3"
					value="#{ProvaConceitoDAO.provaconceito.descricao}"
					id="descricao">
				</h:inputTextarea>
				<div id="divObg-observacoes" class="mensagemObrigatorio"></div>
			</h:panelGroup>
			<h:panelGroup>
				<h:outputText styleClass="tituloCampos" value="Valor" />
			</h:panelGroup>
			<h:panelGroup>
				<h:inputText onblur="blurinput(this);" onfocus="focusinput(this);"
					styleClass="form" size="10" maxlength="5"
					style="border: 1px solid #8eb3c3"
					value="#{ProvaConceitoDAO.provaconceito.valor}" id="valor"></h:inputText>
				<div id="divObg-dataInicio" class="mensagemObrigatorio"></div>
			</h:panelGroup>
			<h:panelGroup>
				<h:outputText styleClass="tituloCampos"
					value="Verdadeiro?" />
			</h:panelGroup>
			<h:panelGroup>
				<h:selectBooleanCheckbox
					value="#{ProvaConceitoDAO.provaconceito.verdadeiro}"
					id="verdadeiro" />
				<div id="divObg-questionario" class="mensagemObrigatorio"></div>
			</h:panelGroup>
			
		
		
		
		
		</h:panelGrid>
	</rich:simpleTogglePanel>
</h:panelGrid>
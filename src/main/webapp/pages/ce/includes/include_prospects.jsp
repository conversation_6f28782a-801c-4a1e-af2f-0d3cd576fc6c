<%@include file="../includes/include_imports.jsp" %>

<h:panelGroup id="parametrosPaginacaoConsulta">
	<h:inputHidden value="#{ProspectsControle.controleConsulta.paginaAtual}" />
	<h:inputHidden value="#{ProspectsControle.controleConsulta.tamanhoConsulta}" />
</h:panelGroup>
<h:panelGrid id="mensagens" columns="1" width="98%"
			styleClass="tabMensagens">
			<h:panelGrid columns="3" width="100%" styleClass="tabMensagens">
				<h:panelGrid columns="1" width="100%">
					
						<h:outputText value=" " />
					
				</h:panelGrid>
				<h:commandButton rendered="#{ProspectsControle.sucesso}"
					image="/imagens/bt_sucesso.png" />
				<h:commandButton rendered="#{ProspectsControle.erro}"
					image="/imagens/erro.png" />
				<h:panelGrid columns="1" width="100%">
					<h:outputText styleClass="mensagem"
						value="#{ProspectsControle.mensagem}" />
					<h:outputText styleClass="mensagemDetalhada"
						value="#{ProspectsControle.mensagemDetalhada}" />
				</h:panelGrid>
			</h:panelGrid>
		</h:panelGrid>
		&nbsp;
		
		
			<table width="98%">
    <tr>
    <td valign="top" width="75%">
    
		<rich:simpleTogglePanel id="Consulta" switchType="client" opened="false" height="220px">
			<f:facet name="header">
				<h:outputText value="#{CElabels['entidade.filtroPesquisa']}:" />
			</f:facet>
			<h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar" columnClasses="classEsquerda, classDireita" width="100%">
				<h:outputText styleClass="tituloCampos" value="#{CElabels['menu.cadastros.cliente']}:" />
						<h:inputText id="cliente" styleClass="form"
							onblur="blurinput(this);" onfocus="focusinput(this);"
							value="#{ProspectsControle.prospects.cliente}" />
						
						<h:outputText styleClass="tituloCampos" value="#{CElabels['entidade.evento']}:" />
						<h:inputText id="evento" styleClass="form"
							onblur="blurinput(this);" onfocus="focusinput(this);"
							value="#{ProspectsControle.prospects.evento}" />

                                                <h:outputText styleClass="tituloCampos" value="Recibo:" />
						<h:inputText id="codigoRecibo" styleClass="form"
                                                        onkeypress="return mascara(this, '99999999', event);"
							onblur="blurinput(this);" onfocus="focusinput(this);"
							value="#{ProspectsControle.codigoRecibo}" />

						<h:selectOneRadio id="tipoFiltroPeriodo" layout="pageDirection" styleClass="tituloCampos"
								  value="#{ProspectsControle.tipoFiltroData}"  onchange="alternarFiltro();" style="width: 100%; height: 50px;">
								<f:selectItems value="#{ProspectsControle.tiposFiltroData}" />
						</h:selectOneRadio>
						
						<h:panelGroup>
							<div style="position: relative; top: 0px;">
								<div id="filtroData" style="visibility: visible; position: absolute; top: -10px;">
									<rich:calendar id="data" styleClass="form" 
										value="#{ProspectsControle.prospects.data}" verticalOffset="-80" horizontalOffset="100"
										datePattern="dd/MM/yyyy" inputSize="10" inputClass="form" 
										oninputblur="blurinput(this);" oninputfocus="focusinput(this);" style="position: absolute; top: -90px;"
										oninputchange="return validar_Data(this.id);" oninputkeypress="return mascara(this, '99/99/9999', event);"
										enableManualInput="true" zindex="2" showWeeksBar="false" />
								</div>
								
								<div id="filtroIntervalo" style="visibility: hidden; position: absolute; top: -10px;">
									<h:outputText styleClass="tituloCampos"
										value="#{CElabels['entidade.data']}: " />
									<h:outputText styleClass="tituloCampos"
										value="#{CElabels['entidade.data.de']} " />
									<rich:calendar id="dataInicio" styleClass="form" 
										value="#{ProspectsControle.prospects.dataInicio}"
										datePattern="dd/MM/yyyy" inputSize="10" inputClass="form" verticalOffset="-80" horizontalOffset="100"
										oninputblur="blurinput(this);" oninputfocus="focusinput(this);"
										oninputchange="return validar_Data(this.id);" oninputkeypress="return mascara(this, '99/99/9999', event);"
										enableManualInput="true" zindex="2" showWeeksBar="false" />&nbsp;
									<h:outputText styleClass="tituloCampos" value="#{CElabels['entidade.dataAte']} " />
									<rich:calendar id="dataFim" styleClass="form"
										value="#{ProspectsControle.prospects.dataFim}"
										datePattern="dd/MM/yyyy" inputSize="10" inputClass="form" verticalOffset="-80" horizontalOffset="100"
										oninputblur="blurinput(this);" oninputfocus="focusinput(this);"
										oninputchange="return validar_Data(this.id);" oninputkeypress="return mascara(this, '99/99/9999', event);"
										enableManualInput="true" zindex="2" showWeeksBar="false" />
								</div>
						</div>
					</h:panelGroup>		
					<!-- Combo de Tipos de Ambiente -->
					<h:outputText styleClass="tituloCampos" value="#{CElabels['entidade.categoriaAmbiente']}: " />
					<h:selectOneMenu onblur="blurinput(this);" onfocus="focusinput(this);" id="tiposAmbiente"
							value="#{ProspectsControle.prospects.ambiente.tipoAmbiente}" >
					<f:selectItem itemValue="" itemLabel="#{CElabels['operacoes.todos']}"/>
					<f:selectItems value="#{ProspectsControle.tiposAmbiente}" />
						<a4j:support event="onchange" reRender="ambientes"></a4j:support>
					</h:selectOneMenu>
					<!-- Combo de Ambientes -->
					<h:outputText styleClass="tituloCampos" value="#{CElabels['entidade.ambiente']}:" />
					<h:selectOneMenu styleClass="form" onblur="blurinput(this);"  
						onfocus="focusinput(this);"  id="ambientes"  
						value="#{ProspectsControle.prospects.ambiente.codigo}">
					<f:selectItem itemValue="" itemLabel="#{CElabels['operacoes.todos']}"/>
					<f:selectItems value="#{ProspectsControle.ambientes}" />
					               </h:selectOneMenu>
					              
					     
					<h:outputText styleClass="tituloCampos" value="#{CElabels['entidade.situacao']}:" />
					<h:selectOneMenu styleClass="form" onblur="blurinput(this);"  
							onfocus="focusinput(this);" id="situacao"  
							value="#{ProspectsControle.prospects.codigoSituacao}">
					<f:selectItem itemValue="" itemLabel="#{CElabels['operacoes.todos']}"/>
					<f:selectItems value="#{ProspectsControle.situacoes}" />
					</h:selectOneMenu>
			</h:panelGrid>				
		</rich:simpleTogglePanel></td>
    <td valign="top" width="25%">
    <rich:simpleTogglePanel id="legenda" switchType="client" opened="false" height="100px">
																	<f:facet name="header">
																		<h:outputText value="#{CElabels['entidade.consultaEventos.legenda']}" />
																	</f:facet>
																	<table>
																	<tr><td style="background: #CC0000; width: 10px;"></td><td class="legenda-descricao"> <h:outputText styleClass="tituloLegenda" value="#{CElabels['entidade.prospect.urgente']}"/></td></tr>
																	<tr><td style="background: #008000; width: 10px;"></td><td class="legenda-descricao"> <h:outputText styleClass="tituloLegenda" value="#{CElabels['entidade.prospect.breve']}"/></td></tr>
																	<tr><td style="background: #FF6600; width: 10px;"></td><td class="legenda-descricao"> <h:outputText styleClass="tituloLegenda" value="#{CElabels['entidade.prospect.dataExpirada']}"/></td></tr>
																	<tr><td style="background: #000000; width: 10px;"></td><td class="legenda-descricao"> <h:outputText styleClass="tituloLegenda" value="#{CElabels['entidade.prospect.semUrgencia']}"/></td></tr>
																	</table>
																	
																</rich:simpleTogglePanel></td>
  </tr>
</table>
														
		  &nbsp;
<table width="98%" border="0"  cellpadding="0"
	cellspacing="0">
	<tr>
		<td width="10"><img
			src="${contexto}/imagens/agenda_imgs/menu_esq.jpg" width="10"
			height="69"></td>
		<td background="${contexto}/imagens/agenda_imgs/menu_fundo.jpg">
		<table width="100%" border="0" cellspacing="0" cellpadding="0">
			<tr>
			<td align="center"></td>
			
			<td align="center">
			<a4j:commandLink id="consultar" reRender="itens, mensagens, painelPaginacaoManual, paginacaoatual" 
	                		actionListener="#{ProspectsControle.consultarPaginadoListener}">
	        <h:graphicImage value="/imagens/botoesCE/buscar.png" style="border: 0px;" title="#{CElabels['entidade.prospect.hint.cadastro']}"/>
	        <f:attribute name="paginaInicial" value="paginaInicial" />
             <f:attribute name="tipo" value="1"/>
	        </a4j:commandLink></td>
	         <td align="center">
	         	<h:panelGroup>
	        		<a onmouseover="if (document.getElementById('hint-verificar').style.visibility != 'hidden') {toolTip('<h:outputText value="Lista em ordem decrescente de prioridade (PRIOR.). Caso n�o exista lista os n�o agendados."/>' , 300 , 'blue')}"
						onmouseout="hideToolTip();"><img src="${contexto}/pages/ce/img/hint.gif" width="15px"
						id="hint-verificar" style="visibility: hidden;" height="15px" border="0px;" /></a>&nbsp;&nbsp;&nbsp;
				</h:panelGroup>       		
	        	<a4j:commandLink id="consultarCadastroEvento" reRender="itens, mensagens, painelPaginacaoManual, paginacaoatual" 
	                		actionListener="#{ProspectsControle.consultarPaginadoListener}">
	          		<h:graphicImage value="/imagens/bt_cadastros.jpg" style="border: 0px;" title="#{CElabels['operacoes.consulta.cadastrosEventos']}"/>
	          		<f:attribute name="paginaInicial" value="paginaInicial" />
             	<f:attribute name="tipo" value="2"/>   
	          	</a4j:commandLink></td>
	        <!-- <td align="center">
	         	<h:panelGroup>
	         		<a onmouseover="if (document.getElementById('hint-verificar1').style.visibility != 'hidden') {toolTip('<h:outputText value="Lista os nomes que tenham alguma conversa cadastrada."/>' , 300 , 'blue')}"
						onmouseout="hideToolTip();"><img src="${contexto}/pages/ce/img/hint.gif" width="15px"
						id="hint-verificar1" style="visibility: hidden;" height="15px" border="0px;" /></a>&nbsp;&nbsp;&nbsp;
				</h:panelGroup>		       	  
	        	<a4j:commandLink id="consultarConversa" reRender="itens, mensagens, painelPaginacaoManual, paginacaoatual" 
	                		actionListener="#{ProspectsControle.consultarPaginadoListener}">
	         		<h:graphicImage value="/imagens/bt_conversas.jpg" style="border: 0px;" alt="#{CElabels['operacoes.conversas']}"/>
	         		<f:attribute name="paginaInicial" value="paginaInicial" />
	         		<f:attribute name="tipo" value="3"/> 
	         	</a4j:commandLink></td>  --> 
	         <td align="center"> 
	        <a4j:commandLink id="limparFiltros" 
	                		action="#{ProspectsControle.limpar}"
	                		reRender="Consulta,itens, mensagens, painelPaginacaoManual, paginacaoatual" >
	         <h:graphicImage value="/imagens/bt_limpar.jpg" style="border: 0px;" alt="#{CElabels['operacoes.limpar']}"/>
	         <f:attribute name="paginaInicial" value="paginaInicial" />
             </a4j:commandLink></td>
	         <td align="center"><h:graphicImage value="/imagens/bt_ajuda.jpg" style="border: 0px;" onclick="ativarAjuda(['verificar', 'verificar1']);"/></td>

			</tr>
		</table>
		</td>
		<td width="10"><img
			src="${contexto}/imagens/agenda_imgs/menu_dir.jpg" width="10"
			height="69"></td>
	</tr>
</table>

              
            <div id="divObg-filtroData" class="mensagemObrigatorio"></div>
			<div id="divObg-filtroIntervalo" class="mensagemObrigatorio"></div>		
				&nbsp;
         <rich:dataTable id="itens" width="98%" headerClass="consulta"
					rowClasses="linhaImpar, linhaPar" 
					value="#{ProspectsControle.listaProspects}"
					rows="100" var="prospects">
					<rich:column>
						<f:facet name="header">
							<h:outputText value="C�d Evento" />
						</f:facet>
						<center>
						
							<h:outputText rendered="#{prospects.codigoInteresse != 0}" value="#{prospects.codigoInteresse}" styleClass="#{prospects.cssColor}"/>
							
						</center>
					</rich:column>
					<rich:column>
						<f:facet name="header">
							<h:outputText value="#{CElabels['menu.cadastros.cliente']}" />
						</f:facet>
						<center>
							<h:outputText value="#{prospects.cliente}" styleClass="#{prospects.cssColor}"/>
						</center>
					</rich:column>
					<rich:column>
						<f:facet name="header">
							<h:outputText value="Ambiente" />
						</f:facet>
						<center>
							<h:outputText value="#{prospects.ambiente.descricao}"  styleClass="#{prospects.cssColor}"/>
						</center>
					</rich:column>
					<rich:column>
						<f:facet name="header">
							<h:outputText value="Tipo de Evento" />
						</f:facet>
						<center>
							<h:outputText value="#{prospects.tipoEvento}"  styleClass="#{prospects.cssColor}"/>
						</center>	
					</rich:column>
					<rich:column>
						<f:facet name="header">
							<h:outputText value="#{CElabels['entidade.evento.data']}" />
						</f:facet>
						<center>
							<h:outputText value="#{prospects.dataFormatada}"  styleClass="#{prospects.cssColor}"/>
						</center>
					</rich:column>
					<rich:column>
						<f:facet name="header">
							<h:outputText value="#{CElabels['entidade.dataProxContato']}" />
						</f:facet>
						<center>
							<h:outputText rendered="#{prospects.dataProximoContatoFormatada eq null}" value="#{CElabels['operacoes.naoAgendado']}" styleClass="#{prospects.cssColor}"/>
							<h:outputText value="#{prospects.dataProximoContatoFormatada}" styleClass="#{prospects.cssColor}"/>
						</center>	
					</rich:column>
					<rich:column>
						<f:facet name="header">
							<h:outputText value="Mensagem" />
						</f:facet>
						<center>
							<h:outputText value="#{prospects.mensagem}"styleClass="#{prospects.cssColor}"/>
						</center>
					</rich:column>
					<rich:column>
						<f:facet name="header">
							<h:outputText value="#{CElabels['entidade.situacao']}" />
						</f:facet>
						<center>
							<h:outputText value="#{prospects.situacao.descricao}" styleClass="#{prospects.cssColor}"/>
						</center>	
					</rich:column>
					<rich:column>
						<f:facet name="header">
							<h:outputText value="PRIOR." />
						</f:facet>
						<center>
							<h:outputText value="#{prospects.prioridade}" styleClass="#{prospects.cssColor}"/>
						</center>	
					</rich:column>
										
					<rich:column width="13%" style="align: right">
						<f:facet name="header">
							<h:outputText value="#{CElabels['operacoes.opcoes']}" />
						</f:facet>
						<h:panelGrid columns="4"> 
						<h:commandLink rendered="#{prospects.situacao eq ProspectsControle.situacaoInteressado}"
								action="#{CadastroInicialControle.abrirCadastroInicialComInteressado}"
								actionListener="#{CadastroInicialControle.selInteressadoListener}"
								onmouseover="toolTip('Cadastro Inicial' , 120 , 'gray')"
								onmouseout="hideToolTip();">
								<h:graphicImage value="/imagens/bt_editar.png" style="border: 0px;" alt="#{CElabels['menu.operacoesCE.cadastroInicial']}"/>
							<f:attribute name="codigoInteressado" value="#{prospects.interessado}" />
						</h:commandLink>
						<h:commandLink rendered="#{prospects.possuiEvento}" onclick="preencherHiddenChamarBotao('form:botaoEdicao','form:codigoPerfilEdicao','#{prospects.codigoInteresse}');return false;"
								onmouseover="toolTip('Detalhamento do Evento' , 120 , 'gray')"
								onmouseout="hideToolTip();">
							<h:graphicImage value="/imagens/bt_detalhes.png" style="border: 0px;" alt="#{CElabels['operacoes.editar.editarDados']}"/>
						</h:commandLink>
						<h:commandLink  actionListener="#{ConversaControle.selEventoConversaListener}" 
								action="#{ConversaControle.abrirConversaComInteressado}"
								onmouseover="toolTip('Conversas' , 120 , 'gray')"
								onmouseout="hideToolTip();">
						<h:graphicImage value="/imagens/bt_conversas.png" style="border: 0px;" alt="#{CElabels['operacoes.conversas']}"/>
						<f:attribute name="codigoEvento"
								value="#{prospects.codigoInteresse}" />
					<f:attribute name="codigoInteressado"
								value="#{prospects.interessado}" />
						</h:commandLink>
						<h:commandLink rendered="#{prospects.situacao.codigo ge ProspectsControle.situacaoConfirmado.codigo && prospects.situacao.codigo != ProspectsControle.situacaoInteressado.codigo }"
								action="#{NavegacaoControle.abrirTelaCaixaEmAberto}"
								actionListener="#{ProspectsControle.selEventoInteresseListener}" 
								onmouseover="toolTip('Parcelas' , 120 , 'gray')"
								onmouseout="hideToolTip();">
							<h:graphicImage value="/imagens/bt_parcelas.png" style="border: 0px;" alt="#{CElabels['menu.operacoesCE.verParcelas']}"/>
							<f:attribute name="codigoEventoInteresse" value="#{prospects.codigoInteresse}" />
						</h:commandLink>
						<h:commandLink action="#{NavegacaoControle.abrirAgendaVisita}"
								actionListener="#{AgendaVisitaControle.selInteressadoListener}"
								onmouseover="toolTip('Marcar Visita' , 100 , 'gray')"
								onmouseout="hideToolTip();">
							<f:attribute name="codigoInteressado" value="#{prospects.interessado}" />
							<f:attribute name="codigoEvento" value="#{prospects.codigoInteresse}" />
							<h:graphicImage value="/imagens/botoesCE/visita.png" style="border: 0px;" alt="#{CElabels['menu.operacoesCE.agendaVisita']}"/>
						</h:commandLink>		
					</h:panelGrid>
					</rich:column>
					
					
		</rich:dataTable>
		<rich:datascroller for="itens" id="painelPaginacaoManual"></rich:datascroller>
				
		<h:commandButton id="botaoEdicao" action="#{CadastroInicialControle.abrirDetalhamento}" style="visibility: hidden;"/>
		<h:inputHidden id="codigoPerfilEdicao" value="#{CadastroInicialControle.codigoEventoInteresse}" />
		
		
				


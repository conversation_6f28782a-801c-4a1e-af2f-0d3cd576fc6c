<%@page pageEncoding="ISO-8859-1"%>
<%@include file="/includes/imports.jsp" %>
<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>
<rich:modalPanel id="panelMasterLog" autosized="true" shadowOpacity="true" width="600" height="350">
    	
	<f:facet name="header">
		<h:panelGroup>
			<h:outputText
				value="Central de Eventos - Log"></h:outputText>
		</h:panelGroup>
	</f:facet>
	<f:facet name="controls">
		<h:panelGroup>
			<h:graphicImage value="/imagens/close.png" style="cursor:pointer"
				id="hidelinkLog" />
			<rich:componentControl for="panelMasterLog"
				attachTo="hidelinkLog" operation="hide"
				event="onclick" />
		</h:panelGroup>
	</f:facet>

       
        <h:form id="formLog">
            <h:panelGrid columns="1"  width="100%" >
                <%@include file="/pages/ce/includes/topoReduzido.jsp"%>
                <c:if test="${LogControle.showLog}">
               
        		<table id="tableParcelas" style="width: 100%;"><tr><td>
                <h:dataTable id="itemsLogCE" width="100%" headerClass="consulta" rowClasses="linhaImpar, linhaPar" columnClasses="colunaAlinhamento"
                             value="#{LogControle.listaConsulta}" rendered="#{LogControle.apresentarResultadoConsulta}" rows="10" var="controleLog">
                    <h:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_Log_entidade}"/>
                        </f:facet>
                        <a4j:commandLink action="#{LogControle.visualizarLog}" reRender="formLog2, formLog" oncomplete="Richfaces.showModalPanel('panelLog')" id="entidade" value="#{controleLog.nomeEntidade}"/>
                    </h:column>
                    <h:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_Log_operacao}"/>
                        </f:facet>
                        <a4j:commandLink action="#{LogControle.visualizarLog}" reRender="formLog2, formLog" oncomplete="Richfaces.showModalPanel('panelLog')" id="operacao" value="#{controleLog.operacao} #{controleLog.nomeEntidade_Apresentar}"/>
                    </h:column>
                    <h:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_Log_nomeCampo}"/>
                        </f:facet>
                        <a4j:commandLink action="#{LogControle.visualizarLog}" reRender="formLog2, formLog" oncomplete="Richfaces.showModalPanel('panelLog')" id="nomeCampo" value="#{controleLog.nomeCampo}"/>
                    </h:column>
                    <h:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_Log_dataHora}"/>
                        </f:facet>
                        <a4j:commandLink action="#{LogControle.visualizarLog}" reRender="formLog2, formLog" oncomplete="Richfaces.showModalPanel('panelLog')" id="dataAlteracao" value="#{controleLog.dataHoraAlteracao_Apresentar}"/>
                    </h:column>
                    <h:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_Log_responsavel}"/>
                        </f:facet>
                        <a4j:commandLink action="#{LogControle.visualizarLog}" reRender="form, formLog" oncomplete="Richfaces.showModalPanel('panelLog')" id="responsavel" value="#{controleLog.responsavelAlteracao}"/>
                    </h:column>
                </h:dataTable>
                </td></tr></table>
                </c:if>
                <c:if test="${not LogControle.showLog}">
                <br/>
                <br/>
                
                </c:if>
                

                <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                    <h:panelGroup id="painelPaginacao" rendered="#{LogControle.confPaginacao.existePaginacao}">
                        <a4j:commandLink id="pagiInicial" styleClass="tituloCampos" value="  <<  " reRender="items, paginaAtual, painelPaginacao" rendered="#{LogControle.confPaginacao.apresentarPrimeiro}" actionListener="#{LogControle.consultarPaginadoListener}">
                            <f:attribute name="pagNavegacao" value="pagInicial" />
                        </a4j:commandLink>
                        <a4j:commandLink id="pagiAnterior" styleClass="tituloCampos" value="  <  " reRender="items, paginaAtual, painelPaginacao" rendered="#{LogControle.confPaginacao.apresentarAnterior}" actionListener="#{LogControle.consultarPaginadoListener}">
                            <f:attribute name="pagNavegacao" value="pagAnterior" />
                        </a4j:commandLink>
                        <h:outputText id="paginaAtual" styleClass="tituloCampos" value="#{msg_aplic.prt_msg_pagina} #{LogControle.confPaginacao.paginaAtualDeTodas}" rendered="true"/>
                        <a4j:commandLink id="pagiPosterior" styleClass="tituloCampos" value="  >  " reRender="items, paginaAtual, painelPaginacao" rendered="#{LogControle.confPaginacao.apresentarPosterior}" actionListener="#{LogControle.consultarPaginadoListener}">
                            <f:attribute name="pagNavegacao" value="pagPosterior" />
                        </a4j:commandLink>
                        <a4j:commandLink id="pagiFinal" styleClass="tituloCampos" value="  >>  " reRender="items, paginaAtual, painelPaginacao" rendered="#{LogControle.confPaginacao.apresentarUltimo}" actionListener="#{LogControle.consultarPaginadoListener}">
                            <f:attribute name="pagNavegacao" value="pagFinal" />
                        </a4j:commandLink>
                        <h:outputText id="totalItens" styleClass="tituloCampos" value=" [#{msg_aplic.prt_msg_itens} #{LogControle.confPaginacao.numeroTotalItens}]" rendered="true"/>
                    </h:panelGroup>
                </h:panelGrid>


                <h:panelGrid id="mensagensLogModal" columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid columns="1" width="100%">
                        <h:outputText styleClass="mensagem"  value="#{LogControle.mensagem}"/>
                        <h:outputText styleClass="mensagemDetalhada" value="#{LogControle.mensagemDetalhada}"/>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
        </h:form>
       <rich:modalPanel id="panelLog" autosized="true" shadowOpacity="false" width="600" height="300">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value=" #{msg_aplic.prt_Log_tituloForm}">
                </h:outputText>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:graphicImage value="/imagens/close.png" style="cursor:pointer" id="hidelink6"/>
                <rich:componentControl for="panelLog" attachTo="hidelink6" operation="hide" event="onclick"/>
            </h:panelGroup>
        </f:facet>
        <a4j:form id="formLog2" ajaxSubmit="true">
            <h:panelGrid columns="2" styleClass="tabForm" width="100%">

                <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Log_operacao}"/>
                <h:inputTextarea id="operacao" rows="2" cols="60" styleClass="campos" readonly="true" value="#{LogControle.logVO.operacao} #{LogControle.logVO.nomeEntidade_Apresentar}" />
                <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Log_nomeCampo}"/>
                <h:inputText id="nomeCampo" size="40" styleClass="campos" readonly="true" value="#{LogControle.logVO.nomeCampo}" />
                <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Log_valorCampoAnterior}"/>
                <h:inputTextarea rows="4" styleClass="campos" readonly="true" cols="70" id="valorCampoAnterior" value="#{LogControle.logVO.valorCampoAnterior}"/>
                <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Log_valorCampoAlterado}"/>
                <h:inputTextarea rows="4" styleClass="campos" readonly="true" cols="70" id="valorCampoAlterado" value="#{LogControle.logVO.valorCampoAlterado}"/>
                <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Log_responsavel}"/>
                <h:outputText id="responsavel" styleClass="tituloCampos" value="#{LogControle.logVO.responsavelAlteracao}"/>
                <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Log_dataHora}"/>
                <h:outputText id="dataAlteracao" styleClass="tituloCampos" value="#{LogControle.logVO.dataAlteracao_Apresentar} ás #{LogControle.logVO.horaAlteracao_Apresentar}"/>

            </h:panelGrid>
        </a4j:form>
    </rich:modalPanel>
    </rich:modalPanel>

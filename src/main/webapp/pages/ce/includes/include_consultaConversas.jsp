<%@include file="../includes/include_imports.jsp" %>
						<h:panelGrid  id="panelDadosBasicos" columns="1" width="100%" columnClasses="colunaCentralizada" >
							
							<h:panelGrid columns="2" columnClasses="classForm, classForm" width="100%">
								
								<h:panelGrid columns="1" headerClass="subordinado" columnClasses="colunaCentralizada" width="100%">
				<rich:separator width="100%" lineType="solid"></rich:separator>
				<h:outputText
							value="#{CElabels['entidade.conversa.registroconversa']}" />
				<rich:separator width="100%" lineType="solid"></rich:separator>
								
									<h:panelGrid id="dados" rowClasses="linhaImpar, linhaPar" columnClasses="classEsquerda, classDireita"
											width="100%" columns="2">
										<h:panelGroup>
										
										<h:inputHidden value="#{ConversaControle.pessoa.codigo}" id="codigoPessoa" />
										<a onmouseover="if (document.getElementById('hint-atendente').style.visibility != 'hidden') {toolTip('<h:outputText value="#{CElabels['entidade.hint.atendente']}"/>' , 300 , 'blue')}"
						onmouseout="hideToolTip();"><img src="${contexto}/pages/ce/img/hint.gif" width="15px"
						id="hint-atendente" style="visibility: hidden;" height="15px" border="0px;" />&nbsp;&nbsp;&nbsp;&nbsp;</a>
										<h:outputText styleClass="tituloCampos" value="#{CElabels['entidade.atendente']}:" />
										</h:panelGroup>
										
										<h:outputText value="#{ConversaControle.usuario.nome}"/>
										
										<h:panelGroup>
										<a onmouseover="if (document.getElementById('hint-evento').style.visibility != 'hidden') {toolTip('<h:outputText value="#{CElabels['entidade.hint.evento']}"/>' , 300 , 'blue')}"
						onmouseout="hideToolTip();"><img src="${contexto}/pages/ce/img/hint.gif" width="15px"
						id="hint-evento" style="visibility: hidden;" height="15px" border="0px;" />&nbsp;&nbsp;&nbsp;&nbsp;</a>
											<h:outputText styleClass="tituloCampos" value="#{CElabels['entidade.evento']}:" />
										</h:panelGroup>
										<!-- Combo de Eventos -->
										<h:selectOneMenu onblur="blurinput(this);"
												disabled="#{!ConversaControle.temEventos}"
												onfocus="focusinput(this);" styleClass="form" id="eventoInteresse"
												value="#{ConversaControle.consultaConversa.codigoEvento}"
												tabindex="2" >
											<f:selectItem itemValue="" itemLabel="#{CElabels['operacoes.selecione']}"/>
											<f:selectItems value="#{ConversaControle.eventosInteresse}" />
										<a4j:support event="onchange" action="#{ConversaControle.consultarHistorico}" reRender="panelDadosBasicos"/>
										</h:selectOneMenu>
										<h:panelGroup>
										<%@include file="../includes/include_obrigatorio.jsp" %>
										<h:outputText styleClass="tituloCampos" value="#{CElabels['entidade.cliente.nome']}:" />
										</h:panelGroup>
										<h:panelGroup>
												<h:inputText readonly="#{ConversaControle.clienteSelecionado}"
														onblur="blurinput(this); limparMsgObrig('form:nomeCliente', 'nomeCliente');" onfocus="focusinput(this);"
														styleClass="form" size="40" maxlength="100"
														value="#{ConversaControle.pessoa.nomeCompleto}" id="nomeCliente" tabindex="3"/>
												<div id="divObg-nomeCliente" class="mensagemObrigatorio"></div>	
										</h:panelGroup>
										
										<h:panelGroup>
										<a onmouseover="if (document.getElementById('hint-telefone').style.visibility != 'hidden') {toolTip('<h:outputText value="#{CElabels['entidade.hint.telefone']}"/>' , 300 , 'blue')}"
						onmouseout="hideToolTip();"><img src="${contexto}/pages/ce/img/hint.gif" width="15px"
						id="hint-telefone" style="visibility: hidden;" height="15px" border="0px;" />&nbsp;&nbsp;&nbsp;&nbsp;</a><%@include file="../includes/include_obrigatorio.jsp" %>
										<h:outputText styleClass="tituloCampos" value="#{CElabels['entidade.cel']}:" />
										</h:panelGroup>
										<h:panelGroup>
										<h:inputText size="13"
                                                     maxlength="13"
                                                     onchange="return validar_Telefone(this.id);"
                                                     onblur="blurinput(this); limparMsgObrig('form:telefoneCelular', 'telefoneCelular');"
                                                     onkeypress="return mascara(this, '(99)999999999', event);"
                                                     onfocus="focusinput(this);"
                                                     styleClass="form"
												value="#{ConversaControle.pessoa.telefoneCelular}" id="telefoneCelular" tabindex="4"/>
										<div id="divObg-telefoneCelular" class="mensagemObrigatorio"></div>
										</h:panelGroup>
										<h:panelGroup>
										<%@include file="../includes/include_obrigatorio.jsp" %>
										<h:outputText styleClass="tituloCampos" value="#{CElabels['entidade.telComercial']}:" />
										</h:panelGroup>
										
										<h:panelGroup>
										<h:inputText onblur="blurinput(this);limparMsgObrig('form:telefoneComercial', 'telefoneComercial');" onfocus="focusinput(this);"
												styleClass="form" size="12" maxlength="20"
												onkeypress="return mascara(this, '(99)99999999', event);"
												value="#{ConversaControle.pessoa.telefoneComercial}" id="telefoneComercial" tabindex="5"/>
										<div id="divObg-telefoneComercial" class="mensagemObrigatorio"></div>
										</h:panelGroup>
										
										<h:panelGroup>
										<%@include file="../includes/include_obrigatorio.jsp" %>
										<h:outputText styleClass="tituloCampos" value="#{CElabels['entidade.telFixo']}:" />
										</h:panelGroup>
										
										<h:panelGroup>
										<h:inputText  onblur="blurinput(this); limparMsgObrig('form:telefoneFixo', 'telefoneFixo');" onfocus="focusinput(this);"
												styleClass="form" size="12" maxlength="20"
												onkeypress="return mascara(this, '(99)99999999', event);"
												value="#{ConversaControle.pessoa.telefoneFixo}" id="telefoneFixo" tabindex="6" />
										<div id="divObg-telefoneFixo" class="mensagemObrigatorio"></div>
										</h:panelGroup>
										
										<h:outputText styleClass="tituloCampos" value="#{CElabels['entidade.pessoa.email']}:" />
										<h:inputText onblur="blurinput(this);" onfocus="focusinput(this);"
												styleClass="form" size="35" maxlength="50"
												value="#{ConversaControle.pessoa.email}" id="email" tabindex="7"/>
										
										<!-- Combo das formas de contato -->
										
										<h:panelGroup>
											<%@include file="../includes/include_obrigatorio.jsp" %>
											<h:outputText styleClass="tituloCampos"
												value="#{CElabels['entidade.formasContato']}:"/>
										</h:panelGroup>
										<h:panelGroup>
											<h:selectOneMenu onblur="blurinput(this); limparMsgObrig('form:formaContato', 'formaContato');"
													onfocus="focusinput(this);" styleClass="form" id="formaContato"
													value="#{ConversaControle.consultaConversa.forma}" tabindex="8">
												<f:selectItem itemValue="" itemLabel="#{CElabels['operacoes.selecione']}"/>
												<f:selectItems value="#{ConversaControle.formasContato}" />
											</h:selectOneMenu>
											<div id="divObg-formaContato" class="mensagemObrigatorio"></div>	
										</h:panelGroup>
										<h:panelGroup>
										<a onmouseover="if (document.getElementById('hint-dataContato').style.visibility != 'hidden') {toolTip('<h:outputText value="#{CElabels['entidade.hint.dataContato']}"/>' , 300 , 'blue')}"
						onmouseout="hideToolTip();"><img src="${contexto}/pages/ce/img/hint.gif" width="15px"
						id="hint-dataContato" style="visibility: hidden;" height="15px" border="0px;" />&nbsp;&nbsp;&nbsp;&nbsp;</a>
										<h:outputText styleClass="tituloCampos" value="#{CElabels['entidade.dataContato']}:"/>
										</h:panelGroup>
										<h:outputText styleClass="tituloCampos" value="#{ConversaControle.consultaConversa.dataConversaFormatadaSemHora}"/>
										
										<h:panelGroup>
											<h:outputText styleClass="tituloCampos"
												value="#{CElabels['entidade.dataProxContato']}:" />
										</h:panelGroup>
										<h:panelGroup>
											<rich:calendar id="dataProximoContato" styleClass="form"
												value="#{ConversaControle.consultaConversa.dataProxConversa}"
												datePattern="dd/MM/yyyy" inputSize="10" inputClass="form"
												oninputblur="blurinput(this);" oninputfocus="focusinput(this);"
												oninputchange="return validar_Data(this.id);"
												oninputkeypress="return mascara(this, '99/99/9999', event);"
												enableManualInput="true" zindex="2" showWeeksBar="false"
												tabindex="9" />
										</h:panelGroup>
									</h:panelGrid>
								</h:panelGrid>
								<table style="background-color: #EEEEEE" >
								<tr><td>
								<table width="100%"><tr><td>
								<h:outputText styleClass="tituloCampos" value="#{CElabels['entidade.descricaoConversa']}:"/>
								</td><td>
								<%@include file="../includes/include_obrigatorio.jsp" %>
								</td></tr></table>			
								</td></tr>
								<tr><td>
								<rich:editor id="textoPadrao" useSeamText="false" viewMode="visual"
							             	width="500" height="200" value="#{ConversaControle.consultaConversa.descricao}"

							             	onchange="limparMsgObrigTextoPadrao();"/>	
										<div id="divObg-textoPadrao" class="mensagemObrigatorio"></div>
								</td></tr>
								<tr><td align="center">
								<h:panelGroup>
								<a onmouseover="if (document.getElementById('hint-gravar').style.visibility != 'hidden') {toolTip('<h:outputText value="#{CElabels['entidade.hint.gravarConversa']}"/>' , 300 , 'blue')}"
						onmouseout="hideToolTip();"><img src="${contexto}/pages/ce/img/hint.gif" width="15px"
						id="hint-gravar" style="visibility: hidden;" height="15px" border="0px;" />&nbsp;&nbsp;&nbsp;&nbsp;</a>
						<a4j:commandButton id="gravar" action="#{ConversaControle.salvar}" value="#{CElabels['operacoes.gravar']}"
													image="/imagens/bt_gravar.png" title="#{CElabels['operacoes.gravar.dados']}" styleClass="botoes"
													onclick="if(!validar()){return false;};"
													reRender="panelDadosBasicos" 
													actionListener="#{ConversaControle.autorizacao}">
									<!-- funcao.conversa -->
									<f:attribute name="funcao" value="108" />
									</a4j:commandButton>
								
								</h:panelGroup>
								</td></tr>
								</table>
							<br/>
							</h:panelGrid>
							<h:panelGrid>
							<h:outputText id="mensagem" styleClass="mensagem" value="#{ConversaControle.mensagem}" />
							<h:outputText id="mensagemDetalhada" styleClass="mensagemDetalhada" value="#{ConversaControle.mensagemDetalhada}" />
							</h:panelGrid>
								<h:panelGrid columns="1" columnClasses="colunaCentralizada" headerClass="subordinado" width="100%">
									<rich:separator width="100%" lineType="solid"></rich:separator>
									<h:outputText
							value="#{CElabels['entidade.dadosauxiliares']}" />
				
				<rich:separator width="100%" lineType="solid"></rich:separator>
									
									<h:panelGroup>
										<a4j:commandButton id="dadosCliente" value="#{CElabels['entidade.cliente']}" 
										onclick="if (!validarDetalheCliente()) { return false; };"
												reRender="panelDetalheCliente" 
												action="#{ConversaControle.mostraDetalhamentoCliente}"
												style="visibility: hidden;"
												/>
										<div id="divObg-pessoa" class="mensagemObrigatorio"></div>
									</h:panelGroup>
									
									<h:panelGrid rowClasses="linhaImpar, linhaPar" columnClasses="classEsquerda, classDireita"
											width="100%" columns="2">
										
										<h:panelGroup>
											
											<h:outputText styleClass="tituloCampos" value="#{CElabels['entidade.data']}" />
										</h:panelGroup>
										<h:panelGroup>
											<rich:calendar styleClass="form" value="#{ConversaControle.dataConsDisp}"
													onchanged="limparMsgObrig('form:dataConsultaDisponInputDate', 'dataConsultaDispon');"
													datePattern="dd/MM/yyyy" inputSize="10" inputClass="form" id="dataConsultaDispon"
													oninputblur="blurinput(this); limparMsgObrig('form:dataConsultaDisponInputDate', 'dataConsultaDispon');" oninputfocus="focusinput(this);"
													oninputchange="return validar_Data(this.id);" oninputkeypress="return mascara(this, '99/99/9999', event);"
													enableManualInput="true" zindex="2" showWeeksBar="false" tabindex="9" />
											<div id="divObg-dataConsultaDispon" class="mensagemObrigatorio"></div>
										</h:panelGroup>
											
										<h:panelGroup>
											
											<h:outputText styleClass="tituloCampos" value="#{CElabels['entidade.ambiente']}:" />
										</h:panelGroup>
										<h:panelGroup>
											<!-- Combo de Ambientes -->
											<h:selectOneMenu onblur="blurinput(this); limparMsgObrig('form:ambientes', 'ambientes');" onfocus="focusinput(this);"
													id="ambientes" value="#{ConversaControle.codigoAmbienteConsDisp}">
												<f:selectItem itemValue="" itemLabel="#{CElabels['operacoes.selecione']}"/>
												<f:selectItems value="#{ConversaControle.listaAmbiente}" />
											</h:selectOneMenu>
											<div id="divObg-ambientes" class="mensagemObrigatorio"></div>
										</h:panelGroup>
									</h:panelGrid>
									
										
								</h:panelGrid >
								<h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada" >
									<h:panelGroup>
									<table width="100%" border="0" align="center" cellpadding="0" cellspacing="0">
										<tr>
											<td width="10">
												<img src="${contexto}/imagens/agenda_imgs/menu_esq.jpg" width="10" height="69">
											</td>
											<td height="10" background="${contexto}/imagens/agenda_imgs/menu_fundo.jpg">
												<table id="botoes" width="100%" border="0" cellspacing="0" cellpadding="0">
													<tr align="center">
														<td align="center">
														<a onmouseover="if (document.getElementById('hint-disponibilidade').style.visibility != 'hidden') {toolTip('<h:outputText value="#{CElabels['entidade.hint.verificarDisponibilidadeData']}"/>' , 300 , 'blue')}"
						onmouseout="hideToolTip();"><img src="${contexto}/pages/ce/img/hint.gif" width="15px"
						id="hint-disponibilidade" style="visibility: hidden;" height="15px" border="0px;" />&nbsp;&nbsp;&nbsp;</a>
															<a4j:commandButton image="/imagens/botoesCE/Verificar_disponibilidade.png"
																			  id="dispo" value="#{CElabels['operacoes.disponibilidade.verificar']}"
																			  onclick="if(!validarDisponibilidade()){return false;};"
																			  reRender="panelEventos" action="#{ConsultaEventosControle.consultarDispAmbCadastroConversa}"
																			  oncomplete="Richfaces.showModalPanel('panelEventos');">
															</a4j:commandButton>							
														</td>
														
														<td align="center">
														<h:panelGroup rendered="#{ConversaControle.temEventos}">
														<a onmouseover="if (document.getElementById('hint-dadosEvento').style.visibility != 'hidden') {toolTip('<h:outputText value="#{CElabels['entidade.hint.dadosEvento']}"/>' , 300 , 'blue')}"
															onmouseout="hideToolTip();"><img src="${contexto}/pages/ce/img/hint.gif" width="15px"
															id="hint-dadosEvento" style="visibility: hidden;" height="15px" border="0px;" />&nbsp;&nbsp;&nbsp;&nbsp;</a>
															<a4j:commandButton image="/imagens/bt_dados_do_evento.jpg"
																			  id="dadosEvento" value="#{CElabels['entidade.evento']}" onclick="if (!validarDetalheEvento()) { return false; };"
																		     reRender="panelDetalheEvento" action="#{ConversaControle.exibirDetalhesEvento}"
																		     oncomplete="Richfaces.showModalPanel('panelDetalheEvento');" >
															</a4j:commandButton>						    
														</h:panelGroup>						
														</td>
														
														<td align="center">
														<a onmouseover="if (document.getElementById('hint-gravar2').style.visibility != 'hidden') {toolTip('<h:outputText value="#{CElabels['entidade.hint.gravarConversa']}"/>' , 300 , 'blue')}"
						onmouseout="hideToolTip();"><img src="${contexto}/pages/ce/img/hint.gif" width="15px"
						id="hint-gravar2" style="visibility: hidden;" height="15px" border="0px;" />&nbsp;&nbsp;&nbsp;&nbsp;</a>
															<a4j:commandButton id="gravar2" action="#{ConversaControle.salvar}" value="#{CElabels['operacoes.gravar']}"
												image="/imagens/bt_gravar.png" title="#{CElabels['operacoes.gravar.dados']}" style="height:30" styleClass="botoes"
												onclick="if(!validar()){return false;};"
												reRender="panelDadosBasicos"
												actionListener="#{ConversaControle.autorizacao}">
												<!-- funcao.conversa -->
												<f:attribute name="funcao" value="108" />
												</a4j:commandButton>						    
																				
														</td>														
														
														<td align="center">
														<h:panelGroup rendered="#{ConversaControle.temEventos}">
														<a4j:commandButton action="#{CadastroInicialControle.confirmarDetalhamento}"
															image="/imagens/botoesCE/detalhamento_do_evento.png" oncomplete="#{CadastroInicialControle.msgAlert}"
															actionListener="#{CadastroInicialControle.selEventoConversaListener}"
			 												value="#{CElabels['menu.operacoesCE.cadastroInicialDetalhado']}" alt="#{CElabels['operacoes.cadastro.terminar']}" styleClass="botoes">
		 													<f:attribute name="codigoEvento" value="#{ConversaControle.consultaConversa.codigoEvento}"/></a4j:commandButton>
														</h:panelGroup>

														</td>
														
														<td align="center">
															<h:panelGroup>
																<h:graphicImage value="/imagens/bt_ajuda.jpg" style="border: 0px;" onclick="ativarAjuda(['telefone', 'disponibilidade', 'atendente','evento','dataContato', 'gravar','gravar2','dadosEvento','detalhamento']);"/>
															</h:panelGroup>
														</td>	
													</tr>
												</table>
											</td>
											<td width="10"><img
												src="${contexto}/imagens/agenda_imgs/menu_dir.jpg" width="10"
												height="69"></td>
										</tr>
									</table>
									</h:panelGroup>
									<div id="divObg-evento" class="mensagemObrigatorio"></div>		
							</h:panelGrid>
							<h:panelGrid id="historicoConversa" columns="1" width="100%"  rendered="#{!empty ConversaControle.listaHistorico}">
									<table width="100%" border="0" cellspacing="0" cellpadding="0" >
										  <tr>
										    <td bgcolor="#F3F3F3"><img src="${contexto}/imagens/titu_historico.jpg" width="311" height="40"></td>
										  </tr>
										  <tr>
										    <td><table width="100%" border="0" cellspacing="0" cellpadding="0">
										      <tr>
										        <td width="56" valign="top" background="${contexto}/imagens/historico_esq.jpg">&nbsp;</td>
										        <td valign="top" background="${contexto}/imagens/historico_fundo.jpg" class="historico_texto">
										        <c:forEach var="conversa" varStatus="index" items="${ConversaControle.listaHistorico}">
										        	<div style="padding-top:24px; padding-right:25px; padding-left:15px">
										        	<strong>${conversa.dataConversaFormatada} - ${conversa.tipoContato.descricao} - ${CElabels['entidade.registradoPor']}  ${conversa.usuario.nome}</strong>
										        	 &nbsp;&nbsp;
										        	 <input onclick="preencherHiddenChamarBotao('form:removerContato','form:contatoSelecionado','${conversa.codigo}');return false;" 
										        	 width="15px" height="15px" type="image" src="../../../imagens/bt_excluir.png"/>
										        	<br/>
										        	<c:if test="${conversa.dataProxConversa ne null}">
										        	Data do pr�ximo contato : ${conversa.proxContatoFormatada} 
										        	</c:if><br/>
										        	${conversa.descricao}
										        	</div>
												  </c:forEach>
										        <br></td>
										        <td width="20" valign="top" bgcolor="#F3F3F3">&nbsp;</td>
										      </tr>
										    </table></td>
										  </tr>
										  <tr>
										    <td bgcolor="#F3F3F3"><img src="${contexto}/imagens/historico_baixo.jpg" width="57" height="21"></td>
										  </tr>
								  </table>
						
						</h:panelGrid>	
							<a4j:commandButton id="removerContato"
														oncomplete="Richfaces.showModalPanel('panelUsuarioSenha');"
														value="#{CElabels['operacoes.excluir']}"
														style="visibility: hidden;" />
							<h:inputHidden id="contatoSelecionado" value="#{ConversaControle.contatoAExcluir}" />	
						</h:panelGrid>

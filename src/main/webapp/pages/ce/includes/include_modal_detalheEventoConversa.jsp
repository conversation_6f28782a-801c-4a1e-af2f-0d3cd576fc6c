<rich:modalPanel id="panelDetalheEvento" autosized="true" shadowOpacity="true" width="450" height="250">
    	
	<f:facet name="header">
		<h:panelGroup>
			<h:outputText value="#{CElabels['menu.operacoesCE.detalheEvento']}"></h:outputText>
		</h:panelGroup>
	</f:facet>
	
	<f:facet name="controls">
		<h:panelGroup>
			<h:graphicImage value="/imagens/close.png" style="cursor:pointer" id="hidelinkDetalheEvento" />
			<rich:componentControl for="panelDetalheEvento"
				attachTo="hidelinkDetalheEvento" operation="hide" event="onclick" />
		</h:panelGroup>
	</f:facet>

    <h:form id="formDetalheEvento">
               
    	<%@include file="/pages/ce/includes/topoReduzido.jsp"%>
        
		<h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
		   	
		   	<rich:simpleTogglePanel switchType="client" opened="true" id="stp2DetalheEvento">
				<f:facet name="header">
					<h:outputText value="#{CElabels['menu.operacoesCE.dadosEvento']}" />
				</f:facet>
		     	
		     	<h:inputHidden value="#{ConversaControle.pessoa.codigo}" id="codigoPessoa" />	
				<table border="0" width="98%">
					<tr>
						<td colspan="2" align="center">
								<h:outputText style="font-size: 15px; color: #006684;" value="#{CElabels['entidade.ambiente']}:" />
								<h:outputText style="font-size: 15px; color: #006684;" value='#{ConsultaEventosControle.resultadoConsulta[ConsultaEventosControle.indexAmbiente]["0"]["1"]}' />
							</td>
					</tr>
					<tr>
						<td width="50%" align="center"><h:outputText style="font-size: 15px; color: #006684;" value="#{CElabels['entidade.interessado.status.evento']}:" />
								<h:outputText style="font-size: 15px; color: #006684;" value="#{ConversaControle.evento.situacao.descricao}"/>
							</td>
						<td width="50%" align="center"><h:outputText style="font-size: 15px; color: #006684;" value="#{CElabels['entidade.evento.data']}:" />
								<h:outputText style="font-size: 15px; color: #006684;" value="#{ConversaControle.evento.dataFormatada}"/>
							</td>
					</tr>
				</table>
		     	<h:panelGrid rowClasses="linhaImpar, linhaPar" columnClasses="classEsquerda, classDireita"
						style="background-image:url('../../../imagens/backGroundTelaBasico.png'); background-repeat: no-repeat;padding: 8px;"
						width="100%" columns="2">
					
					<h:outputText value="#{CElabels['entidade.interessado.nome']}: "/>
					<h:outputText value="#{ConversaControle.pessoa.nomeCompleto }"/>		
		     	
	     			<h:outputText value="#{CElabels['entidade.evento.nome']}: "/>
					<h:outputText value="#{ConversaControle.evento.nomeEvento}"/>
			
					<h:outputText value="#{CElabels['entidade.nrConv']}: "/>
					<h:outputText value="#{ConversaControle.evento.numeroConvidados}"/>
					
				</h:panelGrid>             
			</rich:simpleTogglePanel>       
		</h:panelGrid>
	</h:form>
</rich:modalPanel>
    
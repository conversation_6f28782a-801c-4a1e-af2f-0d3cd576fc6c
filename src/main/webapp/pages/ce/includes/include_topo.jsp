<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@page contentType="text/html"%>
<%@page pageEncoding="ISO-8859-1"%>

<%@include file="include_imports.jsp" %>

<a4j:loadStyle src="/css/toggle_menu.css"/>
<a4j:loadScript src="/script/toggle_menu.js"/>


<tr>
    <td height="77" align="left" valign="top" class="bgtop">
        <table width="100%" height="77"  align="left" border="0" cellpadding="0" cellspacing="0">
            <tr>
                <td width="15%" align="left" valign="top">
                    <%@include file="/pages/ce/includes/desv_links_clean.jsp" %>
                    
                </td>
                <%@include file="/includes/include_feedGestao.jsp" %>
                <%@include file="/includes/include_expiracao.jsp" %>
                <td align="right" valign="top" style="padding-right:13px;">
                    <table border="0" cellspacing="0" cellpadding="0">
                        <tr>
                            <td align="left" valign="top" background="${root}/beta/imagens/bg-topo-menu-15.png" style="padding:0px 6px 0px 6px; border: 1px solid #527180; border-top: none; border-bottom-left-radius: 15px; border-bottom-right-radius: 15px; overflow: hidden;">
                                <div>
                                    <ul class="abatop">
                                        <li>
                                            <%@include file="/includes/include_menu_modulos.jsp" %>
                                        </li>

                                        <li><img src="${root}/images/aba_sep.gif"></li>

                                        <%@include file="/include_top_socialMailing.jsp" %>

                                        <li>
                                            <rich:dropDownMenu>
                                                <f:facet name="label">
                                                    <h:panelGroup>
                                                        <i class="fa-icon-question-sign fa-icon-2x"></i>
                                                    </h:panelGroup>
                                                </f:facet>
                                                <rich:menuItem submitMode="none"
                                                               icon="/faces/beta/imagens/mini-icons-wiki.png">
                                                    <h:outputLink id="linkwiki"
                                                                  style="color:#555;" target="_blank" value='#{SuperControle.urlWikiRaiz}/ZillyonWeb:Modulo_Central_de_Eventos'>
                                                        WikiPacto
                                                    </h:outputLink>
                                                </rich:menuItem>
                                                <rich:menuItem submitMode="none"
                                                        icon="/faces/beta/imagens/mini-icons-suport.png">
                                                    <h:outputLink id="linksolicitacao"
                                                                  style="color:#555;"  target="_blank" value="#{SuporteControle.urlSolicitacao}">
                                                        Suporte
                                                        <a4j:support event="onclick" action="#{SuporteControle.prepareUserService}" oncomplete="#{SuporteControle.loginService}"/>
                                                    </h:outputLink>
                                                </rich:menuItem>

                                            </rich:dropDownMenu>
                                        </li>

                                        <li>
                                            <rich:dropDownMenu itemClass="itemMnuTpo">
                                                <f:facet name="label">
                                                    <h:panelGroup>
                                                        <i class="fa-icon-cog fa-icon-2x"></i>
                                                    </h:panelGroup>
                                                </f:facet>
                                                <%--rich:menuItem submitMode="none"
                                                        icon="/faces/beta/imagens/mini-icons-config.png">
                                                    <a4j:commandLink action="#{ConfiguracaoFinanceiroControle.preparaEdicao}"
                                                                     oncomplete="#{ConfiguracaoFinanceiroControle.msgAlert}"
                                                                     value="Configurações"
                                                                     style="color: #555;"/>
                                                </rich:menuItem--%>
                                                <rich:menuItem rendered="#{!fn:contains(uriPagina, 'pages')}" submitMode="ajax"
                                                               icon="/faces/beta/imagens/mini-icons-calend.png">
                                                    <a4j:commandLink style="color: #555" id="linkGoogleCalendar" action="#{ConfiguracaoSistemaControle.novo}"
                                                                     oncomplete="window.open('#{contexto}/faces/googlecalendar.jsp', 'GoogleCalendar', 820, 620);" value="Google Calendar"/>
                                                </rich:menuItem>
                                                <rich:menuItem rendered="#{!fn:contains(uriPagina, 'pages')}" submitMode="none"
                                                               icon="/faces/beta/imagens/mini-icons-velo.png">
                                                    <a4j:commandLink style="color: #555" id="linkVelocimetro" oncomplete="abrirPopup('#{contexto}/faces/velocimetro.jsp', 'Velocimetro', 500, 300);"  value="Velocímetro"/>
                                                </rich:menuItem>
                                                <rich:menuItem submitMode="ajax"
                                                        icon="/faces/beta/imagens/mini-icons-key.png">
                                                    <a4j:commandLink rendered="#{LoginControle.usuario.permiteAlterarPropriaSenha}" value="Alterar Senha"
                                                                     onclick="abrirPopup('#{contexto}/faces/alterarSenhaClienteForm.jsp', 'AlterarSenha', 410, 350);"
                                                                     title="Alterar senha" styleClass="text2"
                                                                     style="valign:middle;cursor:pointer;color: #555"/>
                                                </rich:menuItem>
                                                <rich:menuSeparator id="menuSeparator11" />

                                                <rich:menuItem submitMode="none" value="Sair"
                                                               icon="/faces/beta/imagens/mini-icons-sair.png"
                                                               onclick="document.location.href='#{LogoutControle.redirectLogout}'" />
                                            </rich:dropDownMenu>
                                        </li>
                                    </ul>
                                </div>
                            </td>
                        </tr>
                        <jsp:include page="/includes/include_top_datahora.jsp" flush="true"/>
                    </table>
                </td>
            </tr>
        </table>
    </td>
</tr>
<tr>
    <td height="48" align="left" valign="top" class="bgmenu">
        <table width="100%"height="48" border="0" cellpadding="0" cellspacing="0">
            <tr>
                <td width="600" align="left" valign="top" class="bgmenuleft" style="padding:9px 0 0 80px;">
                    <div style="float:left;">
                        <ul class="btnazul">
                            <li><p class="btnleft"></p>
                                <p class="btnmiddle"><h:commandLink action="#{NavegacaoControle.abrirTelaInicial}">Inicial</h:commandLink></p>
                                <p class="btnright"></p></li>
                            <li><p class="btnleft"></p>
                                <p class="btnmiddle"><h:commandLink action="#{NavegacaoControle.abrirTelaCadastro}">Cadastros</h:commandLink></p>
                                <p class="btnright"></p></li>
                            <li><p class="btnleft"></p>
                                <p class="btnmiddle"><h:commandLink action="#{BIControle.inicializarRelatoriosCE}">Relatórios</h:commandLink></p>
                                <p class="btnright"></p></li>                                
                        </ul>
                    </div>
                </td>

                <td width="50%" align="right" valign="top" style="padding:9px 13px 4px 80px;">
                    <style type="text/css">
                        .inputPesquisa {
                            background: url("${contexto}/images/nav_logo37.png") bottom !important;
                        }
                    </style>
                    <%@include file="../../../include_pesquisarFuncionalidade.jsp" %>

                    <rich:spacer width="4"/>
                 </td>
            </tr>
        </table>
    </td>
</tr>
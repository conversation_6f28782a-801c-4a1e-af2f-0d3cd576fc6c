
<h:panelGrid columns="1">
	<h:panelGroup>
			<a4j:commandButton styleClass="botoes"  value="#{CElabels['operacoes.voltar']}"/>						
	</h:panelGroup>
		
	<h:panelGroup>
			<h:outputText styleClass="text" value="#{CElabels['entidade.pagamento.responsavalPagamento']}:" />			
	</h:panelGroup>
</h:panelGrid>	
				
			<h:panelGrid  columns="1" styleClass="tablepadding2 textsmall"  width="100%">

	                <h:panelGroup>
						<img src="${contexto}/images/arrow2.gif" width="16" height="16" style="vertical-align:middle;margin-right:6px;">
						<h:outputText value="#{CElabels['menu.cadastros.confFinanceiras.formaPag']}:" />
						<div class="sep" style="margin:4px 0 5px 0;"><img src="${contexto}/images/shim.gif"></div>
					</h:panelGroup>
	                
	               <h:panelGrid columns="1"  styleClass="tablelistras" width="100%"> 
	                
							<h:panelGroup>
								<h:selectBooleanCheckbox  styleClass="campos" value=""/>
								<h:outputText styleClass="text" style="font-weight: bold" value="#{CElabels['operacoes.dinheiro']}: "/>&nbsp;
								<h:inputText styleClass="text"  value=""/>
							</h:panelGroup>	
							
							<h:panelGroup>	
								<h:selectBooleanCheckbox  styleClass="campos" value=""/>
								<h:outputText styleClass="text" style="font-weight: bold" value="#{CElabels['operacoes.cartaoCredito']}: "/>&nbsp;
								<h:inputText styleClass="text"  value=""/>&nbsp;
								<h:outputText styleClass="text" style="font-weight: bold" value="#{CElabels['entidade.operadoraCartao']}: "/>&nbsp;
								<h:selectOneMenu onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form">
									<f:selectItem itemValue="" itemLabel="#{CElabels['operacoes.selecione']}"/>	
								</h:selectOneMenu>&nbsp;
			          			<h:outputText styleClass="text" style="font-weight: bold" value="#{CElabels['entidade.parcelas']}: "/>&nbsp;
								<h:selectOneMenu onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form">
									<f:selectItem itemValue="" itemLabel="#{CElabels['operacoes.selecione']}"/>	
			                	</h:selectOneMenu>&nbsp;	
			                   </h:panelGroup>				
							
							<h:panelGroup>
								<h:selectBooleanCheckbox  styleClass="campos" value=""/>
								<h:outputText styleClass="text" style="font-weight: bold" value="#{CElabels['entidade.cartaoDebito']}: "/>&nbsp;
								<h:inputText styleClass="text"  value=""/>&nbsp;
								<h:outputText styleClass="text" style="font-weight: bold" value="#{CElabels['entidade.operadoraCartao']}: "/>&nbsp;
								<h:selectOneMenu onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form">
									<f:selectItem itemValue="" itemLabel="#{CElabels['operacoes.selecione']}"/>	
			                	</h:selectOneMenu>
							</h:panelGroup>			
											
							<h:panelGroup>
								<h:selectBooleanCheckbox  styleClass="campos" value=""/>
								<h:outputText styleClass="text" style="font-weight: bold" value="#{CElabels['entidade.cheque']}: "/>
							</h:panelGroup>
					</h:panelGrid>	
										
			</h:panelGrid>
			
			
			 <h:dataTable id="items" width="100%" headerClass="consulta" rowClasses="linhaImpar, linhaPar" columnClasses="colunaAlinhamento"
                          var="categoriaProduto">

                            <h:column>
                                <f:facet name="header" >
                                    <h:outputText value="#{CElabels['menu.cadastros.confFinanceiras.banco']}" id="banco"/>
                                </f:facet>
                            </h:column>
                            <h:column>
                                <f:facet name="header">
                                    <h:outputText value="#{CElabels['entidade.agencia']}" id="agencia" />
                                </f:facet>
                            </h:column>

                    	    <h:column>
                        		<f:facet name="header">
                            		<h:outputText value="#{CElabels['entidade.conta']}" id="conta"/>
                        	    </f:facet>
                    		</h:column>
                    		
                    		<h:column>
                        		<f:facet name="header">
                            		<h:outputText value="#{CElabels['entidade.nCheque']}" id="nCheque"/>
                        	    </f:facet>
                    		</h:column>
                    		
                    		<h:column>
                        		<f:facet name="header">
                            		<h:outputText value="#{CElabels['entidade.compensacao']}" id="compensa��o"/>
                        	    </f:facet>
                    		</h:column>
                    		
                    		<h:column>
                        		<f:facet name="header">
                            		<h:outputText value="#{CElabels['entidade.valor']}" id="valor"/>
                        	    </f:facet>
                    		</h:column>
                    		
                    		<h:column>
                        		<f:facet name="header">
                            		<h:outputText value="#{CElabels['operacoes.opcoes']}" id="opcoes"/>
                        	    </f:facet>
                    		</h:column>
            </h:dataTable>
			
			<table align="right">
					<tr>
						<td>
							<a4j:commandButton style="align: left;" styleClass="botoes" action="#{ConsultaEventosControle.consultarDispAmbSemanaAtual}"
				       						   reRender="" value="#{CElabels['operacoes.gerarData']}"/>&nbsp;	
							<a4j:commandButton style="align: left;" styleClass="botoes" action="#{ConsultaEventosControle.consultarDispAmbSemanaAtual}"
				       						   reRender="" value="#{CElabels['operacoes.atualizar']}"/>	 
						</td>
					</tr>
			</table>
				
		
						       							
			<table class="tablepreviewtotal"  width="100%" style="background-color: #C4D6DE; color:#f0f0f0;">
					<tr>
						<td style="color:#f0f0f0; font-weight: bold"  align="left">
							<h:outputText value="#{CElabels['entidade.totalLancado']}:"/>	
						</td >
						
						<td style="color:#f0f0f0; font-weight: bold"  align="right">
							<h:outputText value="#{CElabels['entidade.total']}:"/>		
						</td>
						
					</tr>
			</table>
						  
		  <h:panelGroup>
			<div class="sep" style="margin:4px 0 5px 0;"><img src="${contexto}/images/shim.gif"></div>
				<table class="tablelistras" border="0" width="100%" style="margin-bottom:5px; text-align: left; vertical-align: top; padding: 0;">
					
					<tr>
						<td colspan="1">
							<h:selectBooleanCheckbox  styleClass="campos" value=""/>
							<h:outputText styleClass="text" style="font-weight: bold" value="#{CElabels['entidade.contaCorrenteCliente']}: "/>
							<h:outputText styleClass="text" style="font-weight: bold" value="#{CElabels['entidade.saldoDisponivel']}: "/>
							<h:inputText styleClass="text"  value=""/>
						</td>
						
					</tr>
					
				  </table>
			  <div class="sep" style="margin:4px 0 5px 0;"><img src="${contexto}/images/shim.gif"></div>	  
		</h:panelGroup>
		
		
		<h:panelGroup>
				
				<h:commandLink id="buscar" onclick="if(!confirm('#{Mensagens['operacoes.cancelamento.confirmar']}')) {return false;}"
								   		   action="#">
										<h:graphicImage value="/images/btn_confirmar.gif" style="border: 0px; margin-bottom: 5px;"
											   styleClass="imgalpha" title="#{CElabels['operacoes.buscar']}" height="31"/>
				</h:commandLink>&nbsp;
					 				
				<h:commandLink id="cancela" onclick="if(!confirm('#{Mensagens['operacoes.cancelamento.confirmar']}')) {return false;}"
											action="#{CadastroInicialControle.cancelamentoEstorno}">
												      <h:graphicImage value="/images/btn_cancelar.gif" style="border: 0px; margin-bottom: 5px;"
														              styleClass="imgalpha" title="#{CElabels['operacoes.cancelar']}"  height="30"/>
				</h:commandLink>
		</h:panelGroup>
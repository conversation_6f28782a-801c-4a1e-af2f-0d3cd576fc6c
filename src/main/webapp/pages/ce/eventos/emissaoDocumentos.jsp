<%@page contentType="text/html" pageEncoding="ISO-8859-1" %>
<%@include file="../includes/include_imports.jsp" %>

<f:view>
	<jsp:include page="/includes/include_carregando_ripple.jsp" flush="true"/>
    <html>
    	
		<!-- <PERSON>lui o elemento HEAD da página -->
		<%@include file="/pages/ce/includes/include_head.jsp" %>
		
		<script>

			function showChecklist(action, form, link, windowId) {
				features="height=780,width=595,status=yes,toolbar=no,menubar=no,location=no,scrollbars=yes";
				abrirPopup('',windowId,780,596);			

				document.getElementById(form + ':' + link).click();
			}
		
		</script>
		<style>
					.label{
					font-weight:bold;
					}
					.pbody{
					    color: #006684;
					    background-color: #F3CEDB;
					}
			</style>
		<body>
			
			<h:form id="checklist" target="checklistPopup" style="display:none;">
				<h:commandButton id="abrir" action="#{EmissaoDocumentosControle.exibirChecklist}" />
		    </h:form>
		    <h:form id="versoChecklist" target="versoChecklistPopup" style="display:none;">
				<h:commandButton id="abrir" action="#{EmissaoDocumentosControle.exibirVersoChecklist}" />
		    </h:form>
		

			<h:form id="form">
				<a4j:keepAlive beanName="EmissaoDocumentosControle" />

				<h:panelGroup layout="block" style="display: inline-table;width: 100%;" styleClass="fundoCinza">
					<h:panelGroup layout="block" styleClass="bgtop topoZW" rendered="#{MenuControle.apresentarTopo}">
						<jsp:include page="../../../include_topo_2.0.jsp" flush="true"/>
						<jsp:include page="../../../include_menu_fin_flat.jsp" flush="true"/>
					</h:panelGroup>

					<h:panelGroup layout="block" styleClass="caixaCorpo">
						<h:panelGroup layout="block" style="height: 80%;width: 100%">
							<h:panelGroup layout="block" styleClass="caixaMenuLatel">
								<h:panelGroup layout="block" styleClass="container-imagem container-conteudo-central">
									<h:panelGroup layout="block" styleClass="container-box zw_ui especial ">
										<h:panelGroup styleClass="container-box-header" layout="block">
											<h:panelGroup layout="block" styleClass="margin-box">
												<h:outputText styleClass="container-header-titulo" value="Emissão de documentos"/>
												<h:outputLink styleClass="linkWiki"
															  value="#{SuperControle.urlBaseConhecimento}relatorio-ver-lancamentos/"
															  title="Clique e saiba mais: Lançamentos Financeiros"
															  target="_blank">
													<i class="fa-icon-question-sign" style="font-size: 18px"></i>
												</h:outputLink>
											</h:panelGroup>
										</h:panelGroup>
										<h:panelGroup layout="block" styleClass="margin-box">
												<%@include file="../includes/include_emissaoDocumentos.jsp" %>
										</h:panelGroup>
									</h:panelGroup>
								</h:panelGroup>
								<jsp:include page="../includes/include_box_menulateral.jsp" flush="true"/>
							</h:panelGroup>
						</h:panelGroup>
					</h:panelGroup>

					<jsp:include page="../../../include_rodape_flat.jsp" flush="true" />
				</h:panelGroup>

	        </h:form>
        
        </body>
	</html>
	<%@include file="../../../includes/include_modal_mensagem_generica.jsp"%>
</f:view>

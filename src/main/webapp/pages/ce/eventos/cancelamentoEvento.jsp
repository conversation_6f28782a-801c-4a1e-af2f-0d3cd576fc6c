<%@include file="../includes/include_imports.jsp" %>


<f:view>

	<jsp:include page="/includes/include_carregando_ripple.jsp" flush="true"/>
    	<html>
    			
    	
		<!-- Inclui o elemento HEAD da p�gina -->
		<%@include file="/pages/ce/includes/include_head.jsp" %>
		
		<body>
		<h:form id="form">
			<a4j:keepAlive beanName="CancelamentoEventoControle" />

			<h:panelGroup layout="block" style="display: inline-table;width: 100%;" styleClass="fundoCinza">
				<h:panelGroup layout="block" styleClass="bgtop topoZW" rendered="#{MenuControle.apresentarTopo}">
					<jsp:include page="../../../include_topo_2.0.jsp" flush="true"/>
					<jsp:include page="../../../include_menu_fin_flat.jsp" flush="true"/>
				</h:panelGroup>

				<h:panelGroup layout="block" styleClass="caixaCorpo">
					<h:panelGroup layout="block" style="height: 80%;width: 100%">
						<h:panelGroup layout="block" styleClass="caixaMenuLatel">
							<h:panelGroup layout="block" styleClass="container-imagem container-conteudo-central">
								<h:panelGroup layout="block" styleClass="container-box zw_ui especial ">
									<h:panelGroup styleClass="container-box-header" layout="block">
										<h:panelGroup layout="block" styleClass="margin-box">
											<h:outputText styleClass="container-header-titulo"
														  value="#{CElabels['menu.operacoesCE.operacoes.cancelamentoEvento']}"/>
										</h:panelGroup>
									</h:panelGroup>
									<h:panelGroup layout="block" styleClass="margin-box">
										<%@include file="../includes/include_cancelamentoEvento.jsp" %>
									</h:panelGroup>
								</h:panelGroup>
							</h:panelGroup>
							<jsp:include page="../includes/include_box_menulateral.jsp" flush="true"/>
						</h:panelGroup>
					</h:panelGroup>
				</h:panelGroup>

				<jsp:include page="../../../include_rodape_flat.jsp" flush="true" />
			</h:panelGroup>



        
        </h:form>
        <%@include file="/pages/ce/includes/include_modal_senhaCancelamento.jsp" %>
        <rich:modalPanel id="panelTextoCancelamento" autosized="true" shadowOpacity="true" width="420" height="220"
				onshow="document.getElementById('formTextoCancelamento:TextoCancelamento').focus();" domElementAttachment="parent">
	        <f:facet name="header">
	            <h:panelGroup>
	                <h:outputText value="#{CElabels['operacoes.cancelar.texto']}"></h:outputText>
	            </h:panelGroup>
	        </f:facet>
	        <f:facet name="controls">
	            <h:panelGroup>
	                <h:graphicImage value="/imagens/close.png" style="cursor:pointer" id="hidelinkTextoCancelamento"
	                		/>
	                <rich:componentControl for="panelTextoCancelamento" attachTo="hidelinkTextoCancelamento" operation="hide" event="onclick"/>
	            </h:panelGroup>
	        </f:facet>
			<a4j:form id="formTextoCancelamento" ajaxSubmit="true">
		        <rich:editor id="TextoCancelamento" viewMode="visual" width="400" height="200"
		        		value="#{CancelamentoEventoControle.textoCancelamento}" />
		        <a4j:commandButton value="Salvar" id="fecharEdicaoTextoCancelamento" 
		        action="#{CancelamentoEventoControle.cancelamentoDesistencia}"
		        actionListener="#{CancelamentoEventoControle.autorizacao}">
						<!-- funcao.desistencia -->
									<f:attribute name="funcao" value="104" />
					</a4j:commandButton>
	        </a4j:form>
	    </rich:modalPanel>
        </body>
        </html>

	<%@include file="../../../includes/include_modal_mensagem_generica.jsp"%>
</f:view>

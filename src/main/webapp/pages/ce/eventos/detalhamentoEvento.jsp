<%@include file="../includes/include_imports.jsp" %>

<f:view>
	<jsp:include page="/includes/include_carregando_ripple.jsp" flush="true"/>
    <h:form id="form">
    <input type="hidden" value="centralEventos" name="modulo"/>
    
    	<html>
    	
    	<script>
			function ativarAjuda(id) {
							if (typeof id == "string") {
								var hint = document.getElementById('hint-' + id);
								hint.style.visibility = hint.style.visibility == 'hidden' ? 'visible' : 'hidden';
							} else {
								for (var i = 0; i < id.length; i++) {
									var hint = document.getElementById('hint-' + id[i]);
									hint.style.visibility = hint.style.visibility == 'hidden' ? 'visible' : 'hidden';
								}
							}
						}

			function validarProximoContato(){
				var textoPadrao = document.getElementById('formTextoAgendarContato:textoPadraoTextArea_ifr').contentWindow.document.body.firstChild.innerHTML;
				var data = document.getElementById('formTextoAgendarContato:dataProximoContatoInputDate');
				var validade = true;

				if (data == null || data.value == null || data.value == "") {
					exibirMensagem('<h:outputText value="#{Mensagens[\'campoObrigatorio.data\']}"/>', 'data');
					validade = false;
				}
				if (textoPadrao == null || textoPadrao == '<br mce_bogus="1">' || textoPadrao=='') {
					exibirMensagem('<h:outputText value="#{Mensagens[\'campoObrigatorio.textoPadrao\']}"/>', 'textoPadrao');
					validade = false;
				
				}
				return validade;

				}
			function validarRemarcarReserva(){
				var textoPadrao = document.getElementById('formTextoRemarcarPreReserva:textoPadraoTextArea_ifr').contentWindow.document.body.firstChild.innerHTML;
				
				var validade = true;

				
				if (textoPadrao == null || textoPadrao == '<br mce_bogus="1">' || textoPadrao=='') {
					exibirMensagem('<h:outputText value="#{Mensagens[\'campoObrigatorio.textoPadrao\']}"/>', 'textoPadrao');
					validade = false;
				
				}
				return validade;

				}
		</script>	
    	
		<!-- Inclui o elemento HEAD da p�gina -->
		<%@include file="/pages/ce/includes/include_head.jsp" %>
		
		<body>
		<a4j:keepAlive beanName="OrcamentoDetalhadoControle" />
		<a4j:keepAlive beanName="CadastroInicialControle" />
		<h:panelGroup layout="block" style="display: inline-table;width: 100%;" styleClass="fundoCinza">
			<h:panelGroup layout="block" styleClass="bgtop topoZW" rendered="#{MenuControle.apresentarTopo}">
				<jsp:include page="../../../include_topo_2.0.jsp" flush="true"/>
				<jsp:include page="../../../include_menu_ce_flat.jsp" flush="true"/>
			</h:panelGroup>
			<h:panelGroup layout="block" styleClass="caixaCorpo">
				<h:panelGroup layout="block" style="height: 80%;width: 100%">
					<h:panelGroup layout="block" styleClass="caixaMenuLatel">
						<h:panelGroup layout="block" styleClass="container-imagem container-conteudo-central">
							<h:panelGroup layout="block" styleClass="container-box zw_ui especial ">
								<h:panelGroup styleClass="container-box-header" layout="block">
									<h:panelGroup layout="block" styleClass="margin-box">
										<h:outputText styleClass="container-header-titulo" value="Detalhamento do Evento "/>
									</h:panelGroup>
								</h:panelGroup>
								<h:panelGroup layout="block" styleClass="margin-box">
									<%@include file="../includes/include_detalhamentoEvento.jsp" %>
								</h:panelGroup>
							</h:panelGroup>
						</h:panelGroup>
						<%@include file="../includes/include_box_menulateral.jsp" %>
					</h:panelGroup>
				</h:panelGroup>
			</h:panelGroup>

			<jsp:include page="../../../include_rodape_flat.jsp" flush="true" />
			<rich:jQuery id="mskData" selector=".rich-calendar-input" timing="onload" query="mask('99/99/9999')" />
		</h:panelGroup>
        </body>
        </html>
        	
    </h:form>
   	
   	<%@include file="../includes/include_modal_usuarioSenha.jsp"%>
	<%@include file="../includes/include_modal_clienteInexistente.jsp"%>
	
	
	 <rich:modalPanel id="panelTextoAgendarContato" autosized="true" shadowOpacity="true" width="420" height="220"
				onshow="document.getElementById('formTextoAgendarContato:TextoAgendarContato').focus();" domElementAttachment="parent">
	        <f:facet name="header">
	            <h:panelGroup>
	                <h:outputText value="#{CElabels['operacoes.adicionar.proximoContato']}"></h:outputText>
	            </h:panelGroup>
	        </f:facet>
	        <f:facet name="controls">
	            <h:panelGroup>
	                <h:graphicImage value="/imagens/close.png" style="cursor:pointer" id="hidelinkTextoAgendarContato"
	                		/>
	                <rich:componentControl for="panelTextoAgendarContato" attachTo="hidelinkTextoAgendarContato" operation="hide" event="onclick"/>
	            </h:panelGroup>
	        </f:facet>
			<a4j:form id="formTextoAgendarContato" ajaxSubmit="true">


			<h:panelGrid rowClasses="linhaImpar, linhaPar"
				columnClasses="classEsquerda, classDireita" width="100%" columns="2">
				<h:panelGroup>
					<h:outputText value="#{CElabels['entidade.dataContato']}: " />
				</h:panelGroup>
				<h:panelGroup>
					<h:outputText
						value="#{CadastroInicialControle.proximoContato.dataConversaFormatadaSemHora}" />
				</h:panelGroup>
				<h:panelGroup>
					<h:outputText value="#{CElabels['entidade.dataProxContato']}: " />
				</h:panelGroup>
				<h:panelGroup>
					<rich:calendar id="dataProximoContato" styleClass="form"
						value="#{CadastroInicialControle.proximoContato.dataProxConversa}"
						datePattern="dd/MM/yyyy" inputSize="10" inputClass="form"
						oninputblur="blurinput(this); limparMsgObrig('formTextoAgendarContato:data', 'data');"
						 oninputfocus="focusinput(this);"
						oninputchange="return validar_Data(this.id);" 
						oninputkeypress="return mascara(this, '99/99/9999', event);"
						enableManualInput="true" zindex="2" showWeeksBar="false"
						tabindex="1">
					</rich:calendar>
				<div id="divObg-data" class="mensagemObrigatorio"></div>
				</h:panelGroup>
				
				<h:panelGroup>
					<h:outputText value="#{CElabels['entidade.descricaoContato']}:" />
				</h:panelGroup>
				
				<h:panelGroup>
					<rich:editor id="textoPadrao" useSeamText="false" viewMode="visual"
						width="300" height="200"
						value="#{CadastroInicialControle.proximoContato.descricao}"
						onchange="limparMsgObrigTextoPadrao();"
						/>
				 <div id="divObg-textoPadrao" class="mensagemObrigatorio"></div>
				</h:panelGroup>
				
			</h:panelGrid>
			<center>
		        <a4j:commandButton id="fecharEdicaoTextoAgendarContato" 
		        image="/imagens/botoesCE/gravar.png"
		        reRender="mensagens,formTextoAgendarContato"
		        onclick="if(!validarProximoContato()){return false;};"
		        value="#{CElabels['operacoes.adicionar']}"
		        action="#{CadastroInicialControle.salvarProximoContato}"
		        oncomplete="Richfaces.hideModalPanel('panelTextoAgendarContato');"
		        />
		      </center> 
	        </a4j:form>
	    </rich:modalPanel>
	    
	    
	    <!-- ------------------------- INICIO - MODAL DE EDITAR PRE RESERVA ------------------------- -->
	    
	    <rich:modalPanel id="panelTextoRemarcarPreReserva" autosized="true" shadowOpacity="true" width="420" height="220"
				onshow="document.getElementById('formTextoAgendarContato:TextoRemarcarPreReserva').focus();" domElementAttachment="parent">
	        <f:facet name="header">
	            <h:panelGroup>
	                <h:outputText value="#{CElabels['operacoes.atualizarPreReserva']}"></h:outputText>
	            </h:panelGroup>
	        </f:facet>
	        <f:facet name="controls">
	            <h:panelGroup>
	                <h:graphicImage value="/imagens/close.png" style="cursor:pointer" id="hidelinkTextoRemarcarPreReserva"
	                		/>
	                <rich:componentControl for="panelTextoRemarcarPreReserva" attachTo="hidelinkTextoRemarcarPreReserva" operation="hide" event="onclick"/>
	            </h:panelGroup>
	        </f:facet>
			<a4j:form id="formTextoRemarcarPreReserva" ajaxSubmit="true">


			<h:panelGrid rowClasses="linhaImpar, linhaPar"
				columnClasses="classEsquerda, classDireita" width="100%" columns="2">
				<h:panelGroup>
					<h:outputText value="#{CElabels['entidade.dataContato']}: " />
				</h:panelGroup>
				<h:panelGroup>
					<h:outputText
						value="#{CadastroInicialControle.proximoContato.dataConversaFormatadaSemHora}" />
				</h:panelGroup>
				
				
				<h:panelGroup>
					<h:outputText value="#{CElabels['entidade.descricaoContato']}:" />
				</h:panelGroup>
				
				<h:panelGroup>
					<rich:editor id="textoPadrao" useSeamText="false" viewMode="visual"
						width="300" height="200"
						value="#{CadastroInicialControle.proximoContato.descricao}"
						onchange="limparMsgObrigTextoPadrao();"
						/>
				 <div id="divObg-textoPadrao" class="mensagemObrigatorio"></div>
				</h:panelGroup>
				
			</h:panelGrid>
			<center>
		        <a4j:commandButton id="fecharEdicaoTextoRemarcarPreReserva" 
		        reRender="mensagens,formTextoRemarcarPreReserva"
		        onclick="if(!validarRemarcarReserva()){return false;};"
		        value="#{CElabels['operacoes.atualizarPreReserva']}"
		        action="#{CadastroInicialControle.atualizarPreReserva}"
		        image="/imagens/botoesCE/atualizar_pre_reserva.png"
		        oncomplete="Richfaces.hideModalPanel('panelTextoRemarcarPreReserva');"
		        />
		      </center> 
	        </a4j:form>
	    </rich:modalPanel>
	    
	    <!-- ------------------------- FIM - MODAL DE EDITAR PRE RESERVA ------------------------- -->
	    
	    <rich:modalPanel id="panelTextoEncerramento" autosized="true" shadowOpacity="true" width="420" height="220"
				onshow="document.getElementById('formTextoEncerramento:TextoAgendarContato').focus();" domElementAttachment="parent">
	        <f:facet name="header">
	            <h:panelGroup>
	                <h:outputText value="#{CElabels['operacoes.cancelar.texto']}"></h:outputText>
	            </h:panelGroup>
	        </f:facet>
	        <f:facet name="controls">
	            <h:panelGroup>
	                <h:graphicImage value="/imagens/close.png" style="cursor:pointer" id="hidelinkEncerramento"/>
	                <rich:componentControl for="panelTextoEncerramento" attachTo="hidelinkEncerramento" operation="hide" event="onclick"/>
	            </h:panelGroup>
	        </f:facet>
	        
			<a4j:form id="formTextoEncerramento" ajaxSubmit="true">
			<h:panelGrid rowClasses="linhaImpar, linhaPar" width="100%" columns="1">
								
				<h:panelGroup>
					<h:outputText styleClass="text"  value="Senha:"/>					
                    <h:inputSecret id="senha" size="14" maxlength="64" autocomplete="off" style="margin-left:8px" value="#{OrcamentoDetalhadoControle.responsavelEncerramento.senha}"/>
                </h:panelGroup>
                
                <h:panelGroup>	
					<h:outputText styleClass="text"  value="#{CElabels['entidade.data.atual']}: #{CadastroInicialControle.dataAtual}"/>                        
                </h:panelGroup>
								
				<h:panelGroup>
					<h:outputText value="#{CElabels['entidade.observacao']}:" />
					<rich:editor id="textoPadrao" useSeamText="false" viewMode="visual"
						width="300" height="200"
						value="#{OrcamentoDetalhadoControle.textoEncerramento}"/>
				</h:panelGroup>
				
			</h:panelGrid>
			
			<center>
				<h:outputText id="mensagemEncerrar" styleClass="mensagemDetalhada" 
				value="#{OrcamentoDetalhadoControle.mensagem}"/><br>
		        <a4j:commandButton id="encerrarEvento" 
		        reRender="mensagemEncerrar"
		        value="Salvar"
		        action="#{OrcamentoDetalhadoControle.salvarEncerramento}"
		        actionListener="#{OrcamentoDetalhadoControle.autorizacao}"
		        image="/imagens/botoesCE/Salvar.png">
		        <!-- funcao.encerrarevento -->
		        <f:attribute name="funcao" value="114"/>
		        </a4j:commandButton>
		        &nbsp;
		        <a4j:commandButton id="cancelarEncerramentoEvento" 
		        value="Cancelar"
		        image="/imagens/botoesCE/cancelar.png"
		        oncomplete="Richfaces.hideModalPanel('panelTextoEncerramento');"/>
		      </center> 
	        </a4j:form>
	    </rich:modalPanel>
	    
		<!-- Inicio - panelAutorizarEvento -->
	    <rich:modalPanel id="panelAutorizarEvento" autosized="true" shadowOpacity="true" width="420" height="220" domElementAttachment="parent"
	    onshow="document.getElementById('formAutorizarEvento:senha').focus();">
	        <f:facet name="header">
	            <h:panelGroup>
	                <h:outputText value="#{CElabels['operacoes.titulo.autorizarEvento']}"></h:outputText>
	            </h:panelGroup>
	        </f:facet>
	        <f:facet name="controls">
	            <h:panelGroup>
	                <h:graphicImage value="/imagens/close.png" style="cursor:pointer" id="hidelinkAutorizarEvento"/>
	                <rich:componentControl for="formAutorizarEvento" attachTo="hidelinkAutorizarEvento" operation="hide" event="onclick"/>
	            </h:panelGroup>
	        </f:facet>
	        
			<a4j:form id="formAutorizarEvento">

			<h:panelGrid rowClasses="linhaImpar, linhaPar" width="100%" columns="1">
											
				<h:panelGroup>
					<h:outputText escape="false" value="#{CElabels['operacoes.texto.explicativo.autorizarEvento']}" />
				</h:panelGroup>
				
				<h:panelGroup>
					<h:outputText styleClass="text"  value="Senha:"/>					
                    <h:inputSecret id="senha" size="14" maxlength="64" autocomplete="off" style="margin-left:8px" value="#{OrcamentoDetalhadoControle.responsavelEncerramento.senha}"/>
                </h:panelGroup>
			</h:panelGrid>
			
			<center>
				<h:outputText id="mensagem" styleClass="mensagemDetalhada" value="#{OrcamentoDetalhadoControle.mensagemDetalhada}"/><br>
			
		        <a4j:commandButton id="autorizarEvento"
		        image="/imagens/botoesCE/autorizar.png" 
		        reRender="mensagem" value="Autorizar"
		        action="#{OrcamentoDetalhadoControle.autorizarEvento}"
		        actionListener="#{OrcamentoDetalhadoControle.autorizacao}">
		        
		        <!-- funcao.autorizarevento -->
		        <f:attribute name="funcao" value="113"/>
		        
		        </a4j:commandButton>
		        &nbsp;
		        <a4j:commandButton id="cancelarEncerramentoEvento" value="Cancelar"
		        oncomplete="Richfaces.hideModalPanel('panelAutorizarEvento');"
		        image="/imagens/botoesCE/cancelar.png"/>
		        
		        
		      </center> 
	        </a4j:form>
	    </rich:modalPanel>
	    <!-- Fim - panelAutorizarEvento -->
	    
</f:view>

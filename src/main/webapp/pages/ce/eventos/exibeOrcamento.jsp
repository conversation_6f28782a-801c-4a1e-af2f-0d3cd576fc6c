<%@page contentType="text/html" pageEncoding="ISO-8859-1" %>
<%@include file="../includes/include_imports.jsp" %>

<script>
    function update(msg) {
        var mensagem = document.getElementById('form:msg');
        var mensagem2 = document.getElementById('form:msg2');
        var msgDetalhada = document.getElementById('form:msgDetalhada');
        var msgDetalhada2 = document.getElementById('form:msgDetalhada2');
        mensagem.innerHTML = "";
        mensagem2.innerHTML = "";
        msgDetalhada.innerHTML = "";
        msgDetalhada2.innerHTML = "";

        msgDetalhada.innerHTML = msg;
        msgDetalhada2.innerHTML = msg;
    }
</script>

<f:view>
    <jsp:include page="/includes/include_carregando_ripple.jsp" flush="true"/>
    <html>
        <!-- Inclui o elemento HEAD da página -->
        <%@include file="/pages/ce/includes/include_head.jsp" %>

        <body>

            <h:form id="form">

                <a4j:keepAlive beanName="OrcamentoDetalhadoControle" />

                <h:inputHidden id="ambienteSelec" value="#{OrcamentoDetalhadoControle.ambienteSelecionado}" />
                <h:inputHidden id="condicaoPagamentoSelec" value="#{OrcamentoDetalhadoControle.condicaoPagamentoSelecionada}" />

                <h:panelGroup layout="block" style="display: inline-table;width: 100%;" styleClass="fundoCinza">
                    <h:panelGroup layout="block" styleClass="bgtop topoZW" rendered="#{MenuControle.apresentarTopo}">
                        <jsp:include page="../../../include_topo_2.0.jsp" flush="true"/>
                        <jsp:include page="../../../include_menu_ce_flat.jsp" flush="true"/>
                    </h:panelGroup>

                    <h:panelGroup layout="block" styleClass="caixaCorpo">
                        <h:panelGroup layout="block" style="height: 80%;width: 100%">
                            <h:panelGroup layout="block" styleClass="caixaMenuLatel">
                                <h:panelGroup layout="block" styleClass="container-imagem">
                                    <h:panelGroup layout="block" styleClass="container-box container-conteudo-central">
                                        <h:panelGroup styleClass="container-box-header" layout="block">
                                            <h:panelGroup layout="block" styleClass="margin-box">
                                                <h:outputText styleClass="container-header-titulo" value="#{CElabels['menu.operacoesCE.exibicaoOrcamento']}"/>
                                                <h:outputLink styleClass="linkWiki"
                                                              value="#{SuperControle.urlWikiCE}:Exibi%C3%A7%C3%B5es:Exibi%C3%A7%C3%A3o_Or%C3%A7amento"
                                                              title="Clique e saiba mais: Exibição de Orçamento"
                                                              target="_blank">
                                                    <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                                                </h:outputLink>
                                            </h:panelGroup>
                                        </h:panelGroup>
                                        <h:panelGroup layout="block" styleClass="margin-box">
                                            <h:panelGrid id="conteudo" columns="2" width="100%">
                                                <rich:column width="60%" style="border: 0px; vertical-align: top;">
                                                    <h:panelGrid columns="1" styleClass="tablepadding2 textsmall" width="100%" border="0"
                                                                 style="margin-bottom:5px; text-align: left; vertical-align: top;" cellpadding="0" cellspacing="0">
                                                        <h:commandButton id="voltar"  action="#{CadastroInicialControle.abrirDetalhamento}"
                                                                         image="/imagens/botoesCE/voltar_sem_fundo.png"
                                                                         value="#{CElabels['operacoes.voltar']}"
                                                                         actionListener="#{CadastroInicialControle.selCadastroInicialListener}">
                                                            <f:attribute name="codigoEventoInteresse" value="#{OrcamentoDetalhadoControle.negociacaoEvento.codigoEventoInteresse}"/>
                                                        </h:commandButton>

                                                        <h:panelGroup>
                                                            <img src="${contexto}/images/arrow2.gif" width="16" height="16" style="vertical-align:middle;margin-right:6px;">
                                                            <h:outputText value="#{CElabels['entidade.dataHorario']}:" />
                                                            <div class="sep" style="margin:4px 0 5px 0;"><img src="${contexto}/images/shim.gif"></div>
                                                        </h:panelGroup>

                                                        <h:panelGrid columns="2">
                                                            <h:outputText styleClass="text" style="font-weight: bold" value="Data do Evento : "/>
                                                            <h:outputText styleClass="text" value="#{OrcamentoDetalhadoControle.negociacaoEvento.dataEventoFormatada}"/>

                                                            <h:outputText styleClass="text" style="font-weight: bold" value="Horário Inicial : "/>
                                                            <h:outputText styleClass="text" value="#{OrcamentoDetalhadoControle.negociacaoEvento.horarioInicialString}"/>

                                                            <h:outputText styleClass="text" style="font-weight: bold" value="Horário Final : "/>
                                                            <h:outputText styleClass="text" value="#{OrcamentoDetalhadoControle.negociacaoEvento.horarioFinalExibicaoString}"/>
                                                        </h:panelGrid>
                                                        <div id="divObg-dataValidade" class="mensagemObrigatorio"></div>

                                                        <br />
                                                        <h:panelGroup>
                                                            <img src="${contexto}/images/arrow2.gif" width="16" height="16" style="vertical-align:middle;margin-right:6px;">

                                                            <h:outputText value="#{CElabels['operacoes.negociacao.perfil']}:" />
                                                            <div class="sep" style="margin:4px 0 5px 0;"><img src="${contexto}/images/shim.gif"></div>
                                                        </h:panelGroup>

                                                        <h:panelGrid columns="1" width="100%">
                                                            <h:outputText value="#{OrcamentoDetalhadoControle.negociacaoEvento.perfilEventoTO.descricao}" />
                                                        </h:panelGrid>

                                                        <div id="divObg-perfilEvento" class="mensagemObrigatorio"></div>

                                                        <br />

                                                        <h:panelGroup>
                                                            <img src="${contexto}/images/arrow2.gif" width="16" height="16" style="vertical-align:middle;margin-right:6px;">
                                                            <h:outputText value="#{CElabels['entidade.ambientes']}:" />
                                                            <div class="sep" style="margin:4px 0 5px 0;"><img src="${contexto}/images/shim.gif"></div>
                                                        </h:panelGroup>
                                                        <rich:dataTable id="ambienteSelecionado3" width="100%" rowClasses="linhaImpar" columnClasses="colunaEsquerda"
                                                                        value="#{OrcamentoDetalhadoControle.negociacaoEvento.ambientes}" var="ambiente">
                                                            <rich:column>
                                                                <f:facet name="header">
                                                                    <h:outputText value="#{CElabels['entidade.descricao']}" />
                                                                </f:facet>
                                                                <h:outputText value="#{ambiente.descricaoAmbiente}" />
                                                            </rich:column>
                                                            <rich:column>
                                                                <f:facet name="header">
                                                                    <h:outputText value="#{CElabels['entidade.valor']}" />
                                                                </f:facet>
                                                                <h:outputText value="#{ambiente.valorDescontadoComSazonalidade}" />
                                                            </rich:column>
                                                        </rich:dataTable>

                                                        <br />

                                                        <h:panelGroup>
                                                            <img src="${contexto}/images/arrow2.gif" width="16" height="16" style="vertical-align:middle;margin-right:6px;">
                                                            <h:outputText value="#{CElabels['entidade.tipoLayout']}:" />
                                                            <div class="sep" style="margin:4px 0 5px 0;"><img src="${contexto}/images/shim.gif"></div>
                                                        </h:panelGroup>

                                                        <h:panelGrid styleClass="tablepadding2" columns="1" width="100%">

                                                            <rich:dataTable id="tiposLayout" width="100%" rowClasses="linhaImpar" columnClasses="colunaEsquerda"
                                                                            value="#{OrcamentoDetalhadoControle.tiposLayout}" var="tipoLayout"
                                                                            rendered="#{!empty OrcamentoDetalhadoControle.tiposLayout}">
                                                                <rich:column>
                                                                    <f:facet name="header">
                                                                        <h:outputText value="Tipo de Layout" />
                                                                    </f:facet>
                                                                    <h:outputText value="#{tipoLayout.descricao}" />
                                                                </rich:column>
                                                            </rich:dataTable>

                                                        </h:panelGrid>

                                                        <br />

                                                        <h:panelGroup>
                                                            <img src="${contexto}/images/arrow2.gif" width="16" height="16" style="vertical-align:middle;margin-right:6px;">
                                                            <h:outputText value="#{CElabels['entidade.servicos']}:" />
                                                            <div class="sep" style="margin:4px 0 5px 0;"><img src="${contexto}/images/shim.gif"></div>
                                                        </h:panelGroup>

                                                        <rich:dataTable id="servicos" width="100%" rowClasses="linhaImpar" columnClasses="colunaEsquerda"
                                                                        rendered="#{!empty OrcamentoDetalhadoControle.negociacaoEvento.servicos}"
                                                                        value="#{OrcamentoDetalhadoControle.negociacaoEvento.servicos}" var="servico">

                                                            <rich:column>
                                                                <f:facet name="header">
                                                                    <h:outputText value="#{CElabels['entidade.descricao']}" />
                                                                </f:facet>
                                                                <h:outputText value="#{servico.descricaoServico}" />
                                                            </rich:column>

                                                            <rich:column>
                                                                <f:facet name="header">
                                                                    <h:outputText value="#{CElabels['entidade.fornecedor']}" />
                                                                </f:facet>
                                                                <h:outputText value="#{servico.descricaoFornecedor}" />
                                                            </rich:column>

                                                            <rich:column>
                                                                <a4j:commandLink action="#{OrcamentoDetalhadoControle.exibirTextoLivreServico}"
                                                                                 oncomplete="Richfaces.showModalPanel('panelTextoLivreItem')" reRender="panelTextoLivreItem">
                                                                    <h:graphicImage value="/imagens/botoesCE/texto_livre.png" style="border: 0px;" alt="#{CElabels['entidade.textoLivre']}"/>
                                                                </a4j:commandLink>
                                                            </rich:column>

                                                            <rich:column>
                                                                <f:facet name="header">
                                                                    <h:outputText value="#{CElabels['entidade.quantidade']}" />
                                                                </f:facet>
                                                                <h:outputText value="#{servico.quantidade}" />
                                                            </rich:column>
                                                            <rich:column>
                                                                <f:facet name="header">
                                                                    <h:outputText value="#{CElabels['entidade.valor']}" />
                                                                </f:facet>
                                                                <h:outputText value="#{servico.valorUnitarioMonetario}" />
                                                            </rich:column>

                                                            <rich:column>
                                                                <f:facet name="header">
                                                                    <h:outputText value="#{CElabels['entidade.total']}" />
                                                                </f:facet>
                                                                <h:outputText value="#{servico.valorMonetario} "/>
                                                            </rich:column>
                                                        </rich:dataTable>

                                                        <br />

                                                        <h:panelGroup>
                                                            <img src="${contexto}/images/arrow2.gif" width="16" height="16" style="vertical-align:middle;margin-right:6px;">
                                                            <h:outputText value="#{CElabels['entidade.servicoTercerizado']}:" />
                                                            <div class="sep" style="margin:4px 0 5px 0;"><img src="${contexto}/images/shim.gif"></div>
                                                        </h:panelGroup>

                                                        <rich:dataTable id="servicosTercerizado" width="100%" rowClasses="linhaImpar" columnClasses="colunaEsquerda"
                                                                        value="#{OrcamentoDetalhadoControle.negociacaoEvento.servicosTerceirizados}"
                                                                        rendered="#{!empty OrcamentoDetalhadoControle.negociacaoEvento.servicosTerceirizados}"
                                                                        var="servicoTerceirizado">
                                                            <rich:column>
                                                                <f:facet name="header">
                                                                    <h:outputText value="#{CElabels['entidade.descricao']}" />
                                                                </f:facet>
                                                                <h:outputText value="#{servicoTerceirizado.descricao}" />
                                                            </rich:column>

                                                            <rich:column>
                                                                <f:facet name="header">
                                                                    <h:outputText value="#{CElabels['entidade.fornecedor']}" />
                                                                </f:facet>
                                                                <h:outputText value="#{servicoTerceirizado.fornecedorServico.fornecedor.descricao}" />
                                                            </rich:column>

                                                            <rich:column>
                                                                <a4j:commandLink action="#{OrcamentoDetalhadoControle.exibirTextoLivreServTerceirizado}"
                                                                                 oncomplete="Richfaces.showModalPanel('panelTextoLivreItem')" reRender="panelTextoLivreItem">
                                                                    <h:graphicImage value="/imagens/botoesCE/texto_livre.png" style="border: 0px;" alt="#{CElabels['entidade.textoLivre']}"/>
                                                                </a4j:commandLink>
                                                            </rich:column>

                                                            <rich:column>
                                                                <f:facet name="header">
                                                                    <h:outputText value="#{CElabels['entidade.valor']}" />
                                                                </f:facet>
                                                                <h:outputText value="#{servicoTerceirizado.valorMonetario}" />
                                                            </rich:column>

                                                        </rich:dataTable>
                                                        <br />

                                                        <h:panelGroup>
                                                            <img src="${contexto}/images/arrow2.gif" width="16" height="16" style="vertical-align:middle;margin-right:6px;">
                                                            <h:outputText value="#{CElabels['entidade.bensConsumo']}:" />
                                                            <div class="sep" style="margin:4px 0 5px 0;"><img src="${contexto}/images/shim.gif"></div>
                                                        </h:panelGroup>

                                                        <h:panelGrid styleClass="tablepadding2" columns="1" width="100%" style="text-align: center;">

                                                            <rich:dataTable id="bensConsumo" width="100%" rowClasses="linhaImpar" columnClasses="colunaEsquerda"
                                                                            rendered="#{!empty OrcamentoDetalhadoControle.negociacaoEvento.bensConsumo}"
                                                                            value="#{OrcamentoDetalhadoControle.negociacaoEvento.bensConsumo}" var="bemConsumo">

                                                                <rich:column>
                                                                    <f:facet name="header">
                                                                        <h:outputText value="#{CElabels['entidade.descricao']}" />
                                                                    </f:facet>
                                                                    <h:outputText value="#{bemConsumo.descricaoProdutoLocacao}" />
                                                                </rich:column>

                                                                <rich:column>
                                                                    <a4j:commandLink action="#{OrcamentoDetalhadoControle.exibirTextoLivreBemConsumo}"
                                                                                     oncomplete="Richfaces.showModalPanel('panelTextoLivreItem')" reRender="panelTextoLivreItem">
                                                                        <h:graphicImage value="/imagens/botoesCE/texto_livre.png" style="border: 0px;" alt="#{CElabels['entidade.textoLivre']}"/>
                                                                    </a4j:commandLink>
                                                                </rich:column>

                                                                <rich:column>
                                                                    <f:facet name="header">
                                                                        <h:outputText value="#{CElabels['entidade.quantidade']}" />
                                                                    </f:facet>
                                                                    <h:outputText value="#{bemConsumo.quantidade}" />
                                                                </rich:column>

                                                                <rich:column>
                                                                    <f:facet name="header">
                                                                        <h:outputText value="#{CElabels['entidade.valor']}" />
                                                                    </f:facet>
                                                                    <h:outputText value="#{bemConsumo.valorUnitarioMonetario}" />
                                                                </rich:column>
                                                                <!-- DESCONTO EM PRODUTO -->
                                                                <rich:column>
                                                                    <f:facet name="header">
                                                                        <h:outputText value="#{CElabels['menu.cadastros.perfisEventos.descontos']}" />
                                                                    </f:facet>
                                                                    <h:outputText value="#{bemConsumo.descontoApresentar}  " />
                                                                </rich:column>
                                                                <rich:column>
                                                                    <f:facet name="header">
                                                                        <h:outputText value="#{CElabels['entidade.total']}" />
                                                                    </f:facet>
                                                                    <h:outputText value="#{bemConsumo.valorMonetario}" />
                                                                </rich:column>

                                                            </rich:dataTable>

                                                        </h:panelGrid>

                                                        <br />

                                                        <h:panelGroup>
                                                            <img src="${contexto}/images/arrow2.gif" width="16" height="16" style="vertical-align:middle;margin-right:6px;">
                                                            <h:outputText value="#{CElabels['entidade.utensilios']}:" />
                                                            <div class="sep" style="margin:4px 0 5px 0;"><img src="${contexto}/images/shim.gif"></div>
                                                        </h:panelGroup>

                                                        <h:panelGrid styleClass="tablepadding2" columns="1" width="100%" style="text-align: center;">

                                                            <rich:dataTable id="utensilios" width="100%" rowClasses="linhaImpar" columnClasses="colunaEsquerda"
                                                                            rendered="#{!empty OrcamentoDetalhadoControle.negociacaoEvento.utensilios}"
                                                                            value="#{OrcamentoDetalhadoControle.negociacaoEvento.utensilios}" var="utensilio">

                                                                <rich:column>
                                                                    <f:facet name="header">
                                                                        <h:outputText value="#{CElabels['entidade.descricao']}" />
                                                                    </f:facet>
                                                                    <h:outputText value="#{utensilio.descricaoProdutoLocacao}" />
                                                                </rich:column>

                                                                <rich:column>
                                                                    <a4j:commandLink action="#{OrcamentoDetalhadoControle.exibirTextoLivreUtensilio}"
                                                                                     oncomplete="Richfaces.showModalPanel('panelTextoLivreItem')" reRender="panelTextoLivreItem">
                                                                        <h:graphicImage value="/imagens/botoesCE/texto_livre.png" style="border: 0px;" alt="#{CElabels['entidade.textoLivre']}"/>
                                                                    </a4j:commandLink>
                                                                </rich:column>

                                                                <rich:column>
                                                                    <f:facet name="header">
                                                                        <h:outputText value="#{CElabels['entidade.quantidade']}" />
                                                                    </f:facet>
                                                                    <h:outputText value="#{utensilio.quantidade}" />
                                                                </rich:column>

                                                                <rich:column>
                                                                    <f:facet name="header">
                                                                        <h:outputText value="#{CElabels['entidade.valor']}" />
                                                                    </f:facet>
                                                                    <h:outputText value="#{utensilio.valorUnitarioMonetario}" />
                                                                </rich:column>
                                                                <!-- DESCONTO EM PRODUTO -->
                                                                <rich:column>
                                                                    <f:facet name="header">
                                                                        <h:outputText value="#{CElabels['menu.cadastros.perfisEventos.descontos']}" />
                                                                    </f:facet>
                                                                    <h:outputText value="#{utensilio.descontoApresentar}  " />
                                                                </rich:column>
                                                                <rich:column>
                                                                    <f:facet name="header">
                                                                        <h:outputText value="#{CElabels['entidade.total']}" />
                                                                    </f:facet>
                                                                    <h:outputText value="#{utensilio.valorMonetario}" />
                                                                </rich:column>

                                                            </rich:dataTable>

                                                        </h:panelGrid>

                                                        <br />

                                                        <h:panelGroup>
                                                            <img src="${contexto}/images/arrow2.gif" width="16" height="16" style="vertical-align:middle;margin-right:6px;">
                                                            <h:outputText value="#{CElabels['entidade.brinquedos']}:" />
                                                            <div class="sep" style="margin:4px 0 5px 0;"><img src="${contexto}/images/shim.gif"></div>
                                                        </h:panelGroup>

                                                        <h:panelGrid styleClass="tablepadding2" columns="1" width="100%" style="text-align: center;">

                                                            <rich:dataTable id="brinquedos" width="100%" rowClasses="linhaImpar" columnClasses="colunaEsquerda"
                                                                            rendered="#{!empty OrcamentoDetalhadoControle.negociacaoEvento.brinquedos}"
                                                                            value="#{OrcamentoDetalhadoControle.negociacaoEvento.brinquedos}" var="brinquedo">

                                                                <rich:column>
                                                                    <f:facet name="header">
                                                                        <h:outputText value="#{CElabels['entidade.descricao']}" />
                                                                    </f:facet>
                                                                    <h:outputText value="#{brinquedo.descricaoProdutoLocacao}" />
                                                                </rich:column>

                                                                <rich:column>
                                                                    <a4j:commandLink action="#{OrcamentoDetalhadoControle.exibirTextoLivreBrinquedo}"
                                                                                     oncomplete="Richfaces.showModalPanel('panelTextoLivreItem')" reRender="panelTextoLivreItem">
                                                                        <h:graphicImage value="/imagens/botoesCE/texto_livre.png" style="border: 0px;" alt="#{CElabels['entidade.textoLivre']}"/>
                                                                    </a4j:commandLink>
                                                                </rich:column>

                                                                <rich:column>
                                                                    <f:facet name="header">
                                                                        <h:outputText value="#{CElabels['entidade.quantidade']}" />
                                                                    </f:facet>
                                                                    <h:outputText value="#{brinquedo.quantidade}" />
                                                                </rich:column>

                                                                <rich:column>
                                                                    <f:facet name="header">
                                                                        <h:outputText value="#{CElabels['entidade.valor']}" />
                                                                    </f:facet>
                                                                    <h:outputText value="#{brinquedo.valorUnitarioMonetario}" />
                                                                </rich:column>
                                                                <!-- DESCONTO EM PRODUTO -->
                                                                <rich:column>
                                                                    <f:facet name="header">
                                                                        <h:outputText value="#{CElabels['menu.cadastros.perfisEventos.descontos']}" />
                                                                    </f:facet>
                                                                    <h:outputText value="#{brinquedo.descontoApresentar}  " />
                                                                </rich:column>
                                                                <rich:column>
                                                                    <f:facet name="header">
                                                                        <h:outputText value="#{CElabels['entidade.valor']}" />
                                                                    </f:facet>
                                                                    <h:outputText value="#{brinquedo.valorMonetario}" />
                                                                </rich:column>

                                                            </rich:dataTable>

                                                        </h:panelGrid>

                                                        <br />

                                                        <div class="sep" style="margin:4px 0 5px 0;"><img src="${contexto}/images/shim.gif"></div>
                                                        <h:panelGrid columns="2">
                                                            <h:outputText styleClass="text" style="font-weight: bold" value="#{CElabels['entidade.desconto.total']} :"/>
                                                            <h:outputText styleClass="text" value="#{OrcamentoDetalhadoControle.negociacaoEvento.descontoMonetario}"/>
                                                            <h:outputText styleClass="text" style="font-weight: bold" value="Percentual de descontos :"/>
                                                            <h:outputText styleClass="text" value="#{OrcamentoDetalhadoControle.negociacaoEvento.descontoPercentual}"/>
                                                        </h:panelGrid>

                                                        <br />

                                                        <!-- INICIO - CHEQUE CAUCAO E CREDITO -->
                                                        <h:panelGroup>
                                                            <img src="${contexto}/images/arrow2.gif" width="16" height="16" style="vertical-align:middle;margin-right:6px;">
                                                            <h:outputText value="#{CElabels['entidade.chequeCaucaoECredito']}:" />
                                                            <div class="sep" style="margin:4px 0 5px 0;"><img src="${contexto}/images/shim.gif"></div>
                                                        </h:panelGroup>

                                                        <!-- Fim - Botoes Lancar Caucao e Lancar Credito -->
                                                        <h:panelGrid styleClass="tablepadding2" columns="1" width="100%">

                                                            <rich:dataTable id="dttaCaucaoCredito" width="100%" rowClasses="linhaImpar" columnClasses="colunaLeft"
                                                                            value="#{OrcamentoDetalhadoControle.negociacaoEvento.listaNegEvCaucaoECreditoTO}" styleClass="tablepreviewtotal"
                                                                            var="caucaoCredito">

                                                                <rich:column>
                                                                    <h:outputText styleClass="text" value="Tipo: " />
                                                                    <h:outputText value="#{caucaoCredito.tipoCaucaoECredito.descricao}" />
                                                                    <rich:spacer width="8"/>
                                                                    <h:outputText styleClass="text" value="Valor: " />
                                                                    <h:outputText value="#{caucaoCredito.strValorFormatado}" />
                                                                    <rich:spacer width="8" rendered="#{caucaoCredito.devolvido}"/>
                                                                    <h:outputText rendered="#{caucaoCredito.devolvido}" value="Devolvido ao cliente"/>
                                                                </rich:column>

                                                            </rich:dataTable>

                                                        </h:panelGrid>
                                                        <!-- FIM - CHEQUE CAUCAO E CREDITO -->

                                                        <br />

                                                        <h:panelGroup>
                                                            <img src="${contexto}/images/arrow2.gif" width="16" height="16" style="vertical-align:middle;margin-right:6px;">

                                                            <h:outputText value="#{CElabels['entidade.condicaoPagamento']}:" />
                                                            <div class="sep" style="margin:4px 0 5px 0;"><img src="${contexto}/images/shim.gif"></div>
                                                        </h:panelGroup>

                                                        <rich:dataTable  width="100%" rowClasses="linhaImpar" columnClasses="colunaEsquerda" id="condicoesPagamento" value="#{OrcamentoDetalhadoControle.condicoesPagamento}" var="condicaoPagamento">
                                                            <rich:column rendered="#{condicaoPagamento.selecionado}" style="border: 0px;">
                                                                <h:outputText  value="#{condicaoPagamento.descricaoCondicaoPagamento}" />
                                                            </rich:column>
                                                        </rich:dataTable>

                                                        <br />

                                                        <h:panelGroup>
                                                            <img src="${contexto}/images/arrow2.gif" width="16" height="16" style="vertical-align:middle;margin-right:6px;">
                                                            <h:outputText value="#{CElabels['entidade.textoPredefinido']}:" />
                                                            <div class="sep" style="margin:4px 0 5px 0;"><img src="${contexto}/images/shim.gif"></div>
                                                        </h:panelGroup>

                                                        <h:panelGrid styleClass="tablelistras" columns="1" width="100%">
                                                            <h:outputText escape="false" value="#{OrcamentoDetalhadoControle.negociacaoEvento.textoPredefinido}" />
                                                        </h:panelGrid>

                                                        <br />

                                                        <h:panelGroup>
                                                            <img src="${contexto}/images/arrow2.gif" width="16" height="16" style="vertical-align:middle;margin-right:6px;">

                                                            <h:outputText escape="false" value="#{CElabels['entidade.observacao']}:" />
                                                            <div class="sep" style="margin:4px 0 5px 0;"><img src="${contexto}/images/shim.gif"></div>
                                                        </h:panelGroup>

                                                        <h:panelGrid styleClass="tablelistras" columns="1" width="100%">
                                                            <h:outputText escape="false" value="#{OrcamentoDetalhadoControle.negociacaoEvento.textoLivre}" />

                                                        </h:panelGrid>


                                                    </h:panelGrid>
                                                </rich:column>

                                                <rich:column styleClass="sombrapreview" width="40%" style="border: 0px; text-align: left; vertical-align: top; padding:10px;">
                                                    <h:panelGrid columns="1" width="100%" border="0" cellpadding="0" cellspacing="0">

                                                        <rich:column style="border: 0px; vertical-align: top; text-align: center; margin-bottom:10px;">
                                                            <h:outputText styleClass="tituloAzul" value="#{CElabels['entidade.negociacao.resultado']}"/>
                                                        </rich:column>
                                                        <rich:column style="border: 0px;">
                                                            <c:if test="${not empty OrcamentoDetalhadoControle.mensagem or not empty OrcamentoDetalhadoControle.mensagemDetalhada }">
                                                                <h:panelGrid id="panelMesangem1" styleClass="tablepreviewtotal" style="text-align: right; vertical-align: middle;" columns="1" width="100%" border="0" cellpadding="0" cellspacing="0">
                                                                    <h:panelGroup>
                                                                        <h:outputText id="msg" styleClass="mensagemDetalhadaGrande" value="#{OrcamentoDetalhadoControle.mensagem}" />&nbsp;
                                                                        <h:outputText id="msgDetalhada" styleClass="mensagemDetalhadaGrande" value="#{OrcamentoDetalhadoControle.mensagemDetalhada}" /><br /><br />
                                                                    </h:panelGroup>
                                                                </h:panelGrid>
                                                            </c:if>
                                                        </rich:column>
                                                        <rich:column style="border: 0px;">
                                                            <h:panelGrid styleClass="tablepreviewtotal" style="text-align: right; vertical-align: middle;" columns="1" width="100%" border="0" cellpadding="0" cellspacing="0">
                                                                <h:panelGroup>
                                                                    <h:outputText styleClass="totalNegrito" value="#{CElabels['entidade.total']} = "/>  <h:outputText styleClass="verde" value="#{OrcamentoDetalhadoControle.negociacaoEvento.valorFinalFormatado}"/>
                                                                </h:panelGroup>
                                                            </h:panelGrid>
                                                        </rich:column>

                                                        <rich:column style="border: 0px;">
                                                            <h:panelGrid styleClass="tablepreview" style="vertical-align: middle;" columns="1" width="100%" border="0" cellpadding="0" cellspacing="0">
                                                                <h:panelGroup>
                                                                    <h:inputHidden value="#{CadastroInicialControle.pessoa.codigo}" id="codigoPessoa" />
                                                                    <h:outputText style="font-weight: bold; font-size: 16px" value="Nome do Cliente: " />
                                                                    <h:outputText style="font-size: 16px"
                                                                                  value="#{CadastroInicialControle.pessoa.nomeCompleto}"
                                                                                  id="nomeCliente"></h:outputText>
                                                                </h:panelGroup>
                                                                <h:panelGroup>
                                                                    <h:outputText style="font-weight: bold;" value="#{CElabels['entidade.perfilEvento']}:"/>&nbsp;<h:outputText value="#{OrcamentoDetalhadoControle.negociacaoEvento.perfilEventoTO.descricao}"/>
                                                                </h:panelGroup>

                                                                <h:panelGroup>
                                                                    <h:outputText style="font-weight: bold;" value="#{CElabels['entidade.ambiente']}:"/>
                                                                    <h:dataTable id="ambientesSelecionados" rowClasses="linhaImpar" columnClasses="colunaEsquerda"
                                                                                 rendered="#{!empty OrcamentoDetalhadoControle.negociacaoEvento.ambientes}"
                                                                                 value="#{OrcamentoDetalhadoControle.negociacaoEvento.ambientes}" var="amb">
                                                                        <h:column>
                                                                            <h:outputText value="#{amb.descricaoAmbiente} "/>
                                                                        </h:column>
                                                                        <h:column>
                                                                            <h:outputText value="#{amb.valorDescontadoComSazonalidade} "/>
                                                                        </h:column>
                                                                        <h:column>
                                                                        </h:column>
                                                                    </h:dataTable>
                                                                </h:panelGroup>

                                                                <h:panelGroup>
                                                                    <h:outputText style="font-weight: bold;" value="#{CElabels['entidade.tipoLayout']}:"/>&nbsp;<h:outputText value="#{OrcamentoDetalhadoControle.negociacaoEvento.ambiente.tipoLayout.descricao}"/>
                                                                </h:panelGroup>

                                                                <h:panelGroup>
                                                                    <h:outputText style="font-weight: bold;" value="#{CElabels['entidade.servicos']}:"/>
                                                                    <h:dataTable id="servicosSelecionados" rowClasses="linhaImpar" columnClasses="colunaEsquerda"
                                                                                 rendered="#{!empty OrcamentoDetalhadoControle.negociacaoEvento.servicos}"
                                                                                 value="#{OrcamentoDetalhadoControle.negociacaoEvento.servicos}" var="servico">
                                                                        <h:column>
                                                                            <h:outputText value="#{servico.descricaoServico} "/>
                                                                        </h:column>
                                                                        <h:column>
                                                                            <h:outputText value="#{servico.quantidade} "/>
                                                                        </h:column>
                                                                        <h:column>
                                                                            <h:outputText value="#{servico.valorMonetario} "/>
                                                                        </h:column>
                                                                    </h:dataTable>
                                                                </h:panelGroup>

                                                                <h:panelGroup>
                                                                    <h:outputText style="font-weight: bold;" value="#{CElabels['entidade.bensConsumo']}:"/>
                                                                    <h:dataTable id="bensConsumoSelecionados" rowClasses="linhaImpar" columnClasses="colunaEsquerda"
                                                                                 rendered="#{!empty OrcamentoDetalhadoControle.negociacaoEvento.bensConsumo}"
                                                                                 value="#{OrcamentoDetalhadoControle.negociacaoEvento.bensConsumo}" var="bemConsumo">
                                                                        <h:column>
                                                                            <h:outputText value="#{bemConsumo.descricaoProdutoLocacao} "/>
                                                                        </h:column>
                                                                        <h:column>
                                                                            <h:outputText value="#{bemConsumo.quantidade} "/>
                                                                        </h:column>
                                                                        <h:column>
                                                                            <h:outputText value="#{bemConsumo.valorMonetario} "/>
                                                                        </h:column>
                                                                    </h:dataTable>
                                                                </h:panelGroup>

                                                                <h:panelGroup>
                                                                    <h:outputText style="font-weight: bold;" value="#{CElabels['entidade.utensilios']}:"/>
                                                                    <h:dataTable id="utensiliosSelecionados" rowClasses="linhaImpar" columnClasses="colunaEsquerda"
                                                                                 rendered="#{!empty OrcamentoDetalhadoControle.negociacaoEvento.utensilios}"
                                                                                 value="#{OrcamentoDetalhadoControle.negociacaoEvento.utensilios}" var="utensilio">
                                                                        <h:column>
                                                                            <h:outputText value="#{utensilio.descricaoProdutoLocacao} "/>
                                                                        </h:column>
                                                                        <h:column>
                                                                            <h:outputText value="#{utensilio.quantidade} "/>
                                                                        </h:column>
                                                                        <h:column>
                                                                            <h:outputText value="#{utensilio.valorMonetario} "/>
                                                                        </h:column>
                                                                    </h:dataTable>
                                                                </h:panelGroup>




                                                                <h:panelGroup>
                                                                    <h:outputText style="font-weight: bold;" value="#{CElabels['entidade.brinquedos']}:"/>
                                                                    <h:dataTable id="brinquedosSelecionados" rowClasses="linhaImpar" columnClasses="colunaEsquerda"
                                                                                 rendered="#{!empty OrcamentoDetalhadoControle.negociacaoEvento.brinquedos}"
                                                                                 value="#{OrcamentoDetalhadoControle.negociacaoEvento.brinquedos}" var="brinquedo">
                                                                        <h:column>
                                                                            <h:outputText value="#{brinquedo.descricaoProdutoLocacao} "/>
                                                                        </h:column>
                                                                        <h:column>
                                                                            <h:outputText value="#{brinquedo.quantidade} "/>
                                                                        </h:column>
                                                                        <h:column>
                                                                            <h:outputText value="#{brinquedo.valorMonetario} "/>
                                                                        </h:column>
                                                                    </h:dataTable>
                                                                </h:panelGroup>

                                                                <h:panelGrid columns="2" columnClasses="tituloCamposNegrito, tituloCampos, tituloCampos, tituloCampos">
                                                                    <h:outputText value="#{CElabels['entidade.negociacao.credito.total']}:"/>
                                                                    <h:outputText value="#{OrcamentoDetalhadoControle.negociacaoEvento.creditoMonetario}"/>

                                                                    <h:outputText value="#{CElabels['entidade.negociacao.total']}:"/>
                                                                    <h:outputText value="#{OrcamentoDetalhadoControle.negociacaoEvento.valorTotalMonetario}"/>

                                                                    <h:outputText value="#{CElabels['entidade.desconto.total']}:"/>
                                                                    <h:outputText value="#{OrcamentoDetalhadoControle.negociacaoEvento.descontoMonetario}">
                                                                    </h:outputText>

                                                                    <h:outputText value="#{CElabels['entidade.subTotal']}:"/>
                                                                    <h:outputText styleClass="blue" value="#{OrcamentoDetalhadoControle.negociacaoEvento.valorFinalMonetario}"/>

                                                                    <h:outputText value="#{CElabels['entidade.valorMensal']}:"/>
                                                                    <h:outputText value="#{OrcamentoDetalhadoControle.negociacaoEvento.valorMensalMonetario}"/>

                                                                    <h:outputText value="#{CElabels['entidade.desconto.total']}:"/>
                                                                    <h:outputText value="#{OrcamentoDetalhadoControle.negociacaoEvento.descontoPercentual}"/>
                                                                </h:panelGrid>
                                                            </h:panelGrid>
                                                        </rich:column>

                                                        <rich:column style="border: 0px;">
                                                            <h:panelGrid styleClass="tablepreviewtotal" style="text-align: right;" columns="1" width="100%" border="0" cellpadding="0" cellspacing="0">
                                                                <h:panelGroup>
                                                                    <h:outputText styleClass="totalNegrito" value="#{CElabels['entidade.total']} = "/><h:outputText styleClass="verde" value="#{OrcamentoDetalhadoControle.negociacaoEvento.valorFinalFormatado}"/>
                                                                </h:panelGroup>
                                                            </h:panelGrid>
                                                            <c:if test="${not OrcamentoDetalhadoControle.parcelasExistentes}">
                                                                <h:panelGrid id="parcelas" columns="1" columnClasses="colunaDireita" width="100%">
                                                                    <h:outputText  styleClass="tituloAzul" value="Parcela(s)" style="clear:both;text-align:right;margin-bottom:5px;" />
                                                                    <h:panelGroup rendered="#{OrcamentoDetalhadoControle.negociacaoEvento.valorFinal > 0}">
                                                                        <h:outputText style="font-weight: bold" value=" 1 x " />
                                                                        <h:outputText id="valorCondicaoPagamento" style="font-weight: bold" value="#{OrcamentoDetalhadoControle.negociacaoEvento.valorMensalMonetario}"/>
                                                                    </h:panelGroup>
                                                                    <h:panelGroup rendered="#{OrcamentoDetalhadoControle.negociacaoEvento.condicaoPagamento.nrParcelas > 1}">
                                                                        <h:outputText style="font-weight: bold" value="#{OrcamentoDetalhadoControle.negociacaoEvento.condicaoPagamento.nrParcelas - 1} x " />
                                                                        <h:outputText style="font-weight: bold" value="#{OrcamentoDetalhadoControle.negociacaoEvento.valorMensalMonetario}"/>
                                                                    </h:panelGroup>
                                                                </h:panelGrid>
                                                            </c:if>


                                                            <c:if test="${ OrcamentoDetalhadoControle.parcelasExistentes}">
                                                                <h:panelGrid id="parcelasGrid" columns="1" columnClasses="colunaRight" width="100%">
                                                                    <h:panelGroup>
                                                                        <h:outputText  styleClass="tituloAzul" value="Parcela(s)" style="clear:both;text-align:right;margin-bottom:5px;" />
                                                                        <br/>
                                                                        <c:forEach var="parcela" varStatus="index" items="${OrcamentoDetalhadoControle.parcelas}">
                                                                            <br/>
                                                                            <h:panelGroup>
                                                                                <strong><span>
                                                                                                    <c:out value="${parcela.descricao} - ${parcela.valorParcelaNumerico}  "></c:out>
                                                                                                    </span></strong>
                                                                            </h:panelGroup>
                                                                        </c:forEach>

                                                                    </h:panelGroup>
                                                                </h:panelGrid>
                                                            </c:if>
                                                            <div style="clear: both; text-align: right;">
                                                                <table width="100%" border="0" cellspacing="0" cellpadding="0">
                                                                    <tr>
                                                                        <td>
                                                                            <c:if test="${not empty OrcamentoDetalhadoControle.mensagem or not empty OrcamentoDetalhadoControle.mensagemDetalhada }">
                                                                                <h:panelGrid styleClass="tablepreviewtotal" id="panelMesangem" style="text-align: right;" columns="1" width="100%" border="0" cellpadding="0" cellspacing="0">
                                                                                    <h:panelGroup>
                                                                                        <h:outputText id="msg2" styleClass="mensagemDetalhadaGrande" value="#{OrcamentoDetalhadoControle.mensagem}" />&nbsp;
                                                                                        <h:outputText id="msgDetalhada2" styleClass="mensagemDetalhadaGrande" value="#{OrcamentoDetalhadoControle.mensagemDetalhada}" /><br /><br />
                                                                                    </h:panelGroup>
                                                                                </h:panelGrid>
                                                                            </c:if>
                                                                        </td>
                                                                    </tr>
                                                                </table>
                                                            </div>

                                                            <h:panelGrid columns="5">
                                                                <a4j:commandButton value="#{CElabels['operacoes.negociacao.salvarReserva']}"
                                                                                   title="#{CElabels['operacoes.negociacao.salvar']}"
                                                                                   action="#{OrcamentoDetalhadoControle.salvarReserva}"
                                                                                   rendered="#{OrcamentoDetalhadoControle.btnSalvarReserva}"
                                                                                   oncomplete="Richfaces.showModalPanel('panelMsgSucesso');"
                                                                                   image="/imagens/botoesCE/salvar_com_reserva.png"/>

                                                                <a4j:commandButton value="#{CElabels['operacoes.negociacao.salvarSemReserva']}" title="#{CElabels['operacoes.negociacao.salvar']}"
                                                                                   action="#{OrcamentoDetalhadoControle.salvarSemReserva}"
                                                                                   rendered="#{OrcamentoDetalhadoControle.btnSalvarSemReserva}"
                                                                                   oncomplete="Richfaces.showModalPanel('panelMsgSucesso');"
                                                                                   image="/imagens/botoesCE/salvar_sem_reserva.png"/>

                                                                <h:commandButton value="#{CElabels['operacoes.editar.editarDados']}"
                                                                                 title="#{CElabels['operacoes.editar.editarDados']}"
                                                                                 action="#{OrcamentoDetalhadoControle.editarDados}"
                                                                                 rendered="#{OrcamentoDetalhadoControle.btnEditar}"
                                                                                 image="/imagens/botoesCE/editar_orcamento.png"/>
                                                                <h:commandButton id="cancela"
                                                                                 actionListener="#{CancelamentoEventoControle.selEventoListener}"
                                                                                 action="#{NavegacaoControle.abrirTelaCancelamentoEvento}"
                                                                                 image="/imagens/botoesCE/cancelar.png">
                                                                    <f:attribute name="codigoEvento"
                                                                                 value="#{OrcamentoDetalhadoControle.negociacaoEvento.codigoEventoInteresse}" />
                                                                </h:commandButton>
                                                            </h:panelGrid>

                                                        </rich:column>

                                                    </h:panelGrid>
                                                </rich:column>

                                            </h:panelGrid>
                                        </h:panelGroup>
                                    </h:panelGroup>
                                </h:panelGroup>
                                <%@include file="../includes/include_box_menulateral.jsp" %>
                            </h:panelGroup>
                        </h:panelGroup>
                    </h:panelGroup>

                    <jsp:include page="../../../include_rodape_flat.jsp" flush="true" />
                    <rich:jQuery id="mskData" selector=".rich-calendar-input" timing="onload" query="mask('99/99/9999')" />
                </h:panelGroup>
            </h:form>

            <rich:modalPanel id="panelTextoLivreItem" autosized="true" shadowOpacity="true" width="420" height="220">
                <f:facet name="header">
                    <h:panelGroup>
                        <h:outputText value="#{CElabels['entidade.textoLivre']}"></h:outputText>
                    </h:panelGroup>
                </f:facet>
                <f:facet name="controls">
                    <h:panelGroup>
                        <h:graphicImage value="/imagens/close.png" style="cursor:pointer" id="hidelinkTextoLivreItem"/>
                        <rich:componentControl for="panelTextoLivreItem" attachTo="hidelinkTextoLivreItem" operation="hide" event="onclick"/>
                    </h:panelGroup>
                </f:facet>
                <h:outputText id="textoLivreItem" escape="false" value="#{OrcamentoDetalhadoControle.textoLivre}" />
            </rich:modalPanel>

            <rich:modalPanel id="panelMsgSucesso" autosized="true" shadowOpacity="true" width="150" height="70">
                <f:facet name="controls">
                    <h:panelGroup>
                        <h:graphicImage value="/imagens/close.png" style="cursor:pointer" id="hidelinkMsgSucesso"/>
                        <rich:componentControl for="panelMsgSucesso" attachTo="hidelinkMsgSucesso" operation="hide" event="onclick"/>
                    </h:panelGroup>
                </f:facet>
                <h:panelGrid columns="1" width="100%" style="text-align: center;">
                    <h:outputText styleClass="titulo3" value="#{Mensagens['operacoes.salvar.sucesso']}"/>
                    <br />
                    <h:form><h:commandButton action="#{CadastroInicialControle.abrirDetalhamento}" value="OK"
                                             image="/imagens/botoesCE/OK.png"
                                             actionListener="#{CadastroInicialControle.selCadastroInicialListener}">
                            <f:attribute name="codigoEventoInteresse" value="#{OrcamentoDetalhadoControle.negociacaoEvento.codigoEventoInteresse}"/>
                        </h:commandButton>
                    </h:form>
                </h:panelGrid>
            </rich:modalPanel>

        </body>
    </html>
    <%@include file="../includes/include_modal_usuarioSenha.jsp"%>
    <%@include file="../includes/include_modal_clienteInexistente.jsp"%>
</f:view>

<%@include file="../includes/include_imports.jsp" %>
<html>
<head>
<link rel="shortcut icon" href="${contexto}/favicon.ico" >    
<title>Central de Eventos - Pacto Solu&ccedil;&otilde;es</title>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<meta name="title" content="Zillyon Web - Pacto Solu&ccedil;&otilde;es" />
<meta name="description" content="Zillyon Web - Pacto Solu&ccedil;&otilde;es" />
<meta name="keywords" content="Zillyon Web - Pacto Solu&ccedil;&otilde;es"/>
<meta name="geo.region" content="BR" />
<meta name="author" content="Zillyon Web - Pacto Solu&ccedil;&otilde;es" />
<meta name="language" content="pt-br" />
<link href="${contexto}/css/checklist.css" rel="stylesheet" type="text/css">

<script>
	window.onload = function() {
		var numeroLinhas = 13;
		var numeroLinhasFornecedor = 8;
		var tamanhoLista = document.getElementById('tamanhoLista').value;
		var tamanhoListaFornecedor = document.getElementById('tamanhoListaFornecedor').value;
		
		if (tamanhoLista < numeroLinhas) {
			var linhaEmBranco = '<tr>';
			for (var i = 0; i < 10; i++) {
				linhaEmBranco += '<td class="camposTabela">&nbsp;</td>';
			}
			linhaEmBranco += '</tr>';

			var linhaTotal = '<tr>';
			linhaTotal += '<td colspan="9" align="right" class="camposTabela">TOTAL</td>';
			linhaTotal += '<td class="camposTabela">&nbsp;</td>';
			linhaTotal += '</tr>';

			var numBens = numeroLinhas - tamanhoLista;
			var tabela = document.getElementById('tabelaBens');

			for (var i = 0; i < numBens; i++) {
				tabela.innerHTML += linhaEmBranco;
			}
			tabela.innerHTML += linhaTotal;
		}
		if (tamanhoListaFornecedor < numeroLinhasFornecedor) {
			var linhaEmBranco = '<tr>';
			for (var i = 0; i < 4; i++) {
				linhaEmBranco += '<td class="camposTabela">&nbsp;</td>';
			}
			linhaEmBranco += '</tr>';


			var numBens = numeroLinhasFornecedor - tamanhoListaFornecedor;
			var tabela = document.getElementById('tabelaFornecedor');

			for (var i = 0; i < numBens; i++) {
				tabela.innerHTML += linhaEmBranco;
			}
			
		}
	};
</script>

</head>

<body>

<f:view>
  <jsp:include page="/includes/include_carregando_ripple.jsp" flush="true"/>
<h:form>
<!-- TABELA CABE�ALHO-->
<table width="1000px">
<!-- BOTAO IMPRIMIR -->
<tr><td>
<h:commandLink
		onclick="window.print();return false;">
		<h:graphicImage value="/imagens/botoesCE/imprimir.png" alt="#{CElabels['operacoes.imprimir']}" style="border: 0px; width: 65;"/>
</h:commandLink>

</td></tr>
<!-- TITULO -->
<tr><td>
<center>
<c:if test="${EmissaoDocumentosControle.nrAmbientes gt 1}">
    <label class="checkTituloGeral"><strong>Evento <h:outputText value="#{EmissaoDocumentosControle.eventoInteresse.situacao.descricao}"/> para os ambientes <h:outputText value="#{EmissaoDocumentosControle.negociacaoEvento.descricaoAmbientes}"/></strong></label>
    </c:if>
    <c:if test="${EmissaoDocumentosControle.nrAmbientes eq 1}">
    <label class="checkTituloGeral"><strong>Evento <h:outputText value="#{EmissaoDocumentosControle.eventoInteresse.situacao.descricao}"/> para o ambiente <h:outputText value="#{EmissaoDocumentosControle.negociacaoEvento.descricaoAmbientes}"/></strong></label>
    </c:if>
</center>
</td></tr>

</table>


<!-- TABELA GERAL -->
<table width="1000px" height="400px" border="0" cellpadding="2" cellspacing="0">

<!-- LINHA DE CIMA-->
<tr>

<!-- COLUNA ESQUERDA-->
<td align="left" valign="top">
<table width="100%" id="tabelaBens"  border="1" cellpadding="2" cellspacing="0" class="tabela">
      <tr>
        <th nowrap="nowrap" bgcolor="#CCCCCC" class="campos">Controle de Bebidas</th>
        <th nowrap="nowrap" bgcolor="#CCCCCC" class="campos">Inicial</th>
        <th nowrap="nowrap" bgcolor="#CCCCCC" class="campos">Confer�ncia</th>
        <th nowrap="nowrap" bgcolor="#CCCCCC" class="campos">Abastec.</th>
        <th nowrap="nowrap" bgcolor="#CCCCCC" class="campos">R�brica</th>
        <th nowrap="nowrap" bgcolor="#CCCCCC" class="campos">Total</th>
        <th nowrap="nowrap" bgcolor="#CCCCCC" class="campos">Final</th>
        <th nowrap="nowrap" bgcolor="#CCCCCC" class="campos">Cons.</th>
        <th nowrap="nowrap" bgcolor="#CCCCCC" class="campos">R$ </th>
        <th nowrap="nowrap" bgcolor="#CCCCCC" class="campos">R$ Total</th>
      </tr>
      
      <c:forEach var="bemConsumo" varStatus="index" items="${EmissaoDocumentosControle.negociacaoEvento.bensConsumo}">
	      <tr>
	        <td class="camposTabela">${bemConsumo.descricaoProdutoLocacao}</td>
	        <td class="camposTabela">${bemConsumo.quantidade}</td>
	        <td class="camposTabela">&nbsp;</td>
	        <td class="camposTabela">&nbsp;</td>
	        <td class="camposTabela">&nbsp;</td>
	        <td class="camposTabela">&nbsp;</td>
	        <td class="camposTabela">&nbsp;</td>
	        <td class="camposTabela">&nbsp;</td>
	        <td class="camposTabela">${bemConsumo.valorUnitarioMonetario}</td>
	        <td class="camposTabela">&nbsp;</td>
	      </tr>
	      <c:if test="${index.last}">
	      	<c:set var="tamanhoLista" value="${index.index + 1}" />
	      </c:if>
	  </c:forEach>
	  
    </table>
</td>


<!-- COLUNA DIREITA-->
<td align="left" valign="top">
  <!-- DATA EVENTO -->
    <table width="100%" border="1" cellpadding="2" cellspacing="0" class="tabela">
      <tr>
        <th class="campos">DATA EVENTO: <h:outputText value="#{EmissaoDocumentosControle.negociacaoEvento.dataEventoFormatada}" /></th>
      </tr>
      <tr>
        <td class="camposTabela"><span class="camposTabela"><h:outputText value="#{EmissaoDocumentosControle.negociacaoEvento.diaSemanaEvento}"/></span></td>
      </tr>
      <tr>
        <td><span class="camposTabela">Respons�vel: <h:outputText value="#{EmissaoDocumentosControle.eventoInteresse.interessado.nomeCliente}" /></span></td>
      </tr>
      <tr>
        <td><span class="camposTabela">Fone: <h:outputText value="#{EmissaoDocumentosControle.telefonesInteressado}" /></span></td>
      </tr>
      <tr>
        <td><span class="camposTabela">Nome do Evento: <h:outputText value="#{EmissaoDocumentosControle.eventoInteresse.nomeEvento}" /></span></td>
      </tr>
    </table>
     <!-- HORARIO EVENTO -->
    <table width="100%" border="1" cellpadding="2" cellspacing="0" class="tabela">
      <tr>
        <td><span class="camposTabela">Hor�rio Inicial: <h:outputText value="#{EmissaoDocumentosControle.negociacaoEvento.horarioInicialString}" /></span></td>
        <td><span class="camposTabela">Hor�rio Final: <h:outputText value="#{EmissaoDocumentosControle.negociacaoEvento.horarioFinalExibicaoString}" /></span></td>
      </tr>
      <tr>
        <td colspan="2"><span class="camposTabela">Hor�rio Livre: ( ) Sim ( ) N�o</span></td>
      </tr>
      <tr>
        <td colspan="2"><span class="camposTabela">Excedente Sal�o de Festas :      </span></td>
      </tr>
      <tr>
        <td colspan="2"><span class="camposTabela">N� Convidados: <h:outputText value="#{EmissaoDocumentosControle.totalConvidados}" /> p.</span></td>
      </tr>
      <tr>
        <td><span class="camposTabela">N� Seguran�as: <h:outputText value="#{EmissaoDocumentosControle.numeroSegurancas}"></h:outputText> </span></td>
        <td><span class="camposTabela">N� Star Clean: <h:outputText value="#{EmissaoDocumentosControle.numeroLimpeza}"></h:outputText>  </span></td>
      </tr>
      <tr>
        <td colspan="2"><span class="camposTabela">Excedente Seguran�a(cada):    </span></td>
      </tr>
    </table>
    <table width="100%" cellpadding="0" cellspacing="0" >
    <tr><td align="left" valign="top">
    <table width="100%" border="1" cellpadding="2" cellspacing="0" class="tabela">
      <tr>
        <td class="camposTabela" scope="col">Cr�dito Bebidas R$ <h:outputText value="#{EmissaoDocumentosControle.totalCreditoFormatado}"/></td>
      </tr>
      <tr>
        <td scope="row"><p class="camposTabela">em nome de: </p></td>
      </tr>
      <tr>
        <td scope="row"><div style="height: 32px;">
        <span class="camposTabela">Cheque cau��o R$ <h:outputText value="#{EmissaoDocumentosControle.totalCaucaoFormatado}"/> </span></div></td>
      </tr>
    </table>
    </td>
	<td align="left" valign="top">
	  
      <!-- NUMERO DE CARROS -->
    <table width="100%" height="100%" border="1" cellpadding="2" cellspacing="0" class="tabela">
      <tr>
        <th class="campos" scope="col">N� CARROS</th>
      </tr>
      <tr>
        <td scope="row"><div style="height: 52px;"></div></td>
      </tr>
    </table>
    
    </td></tr>
    
    </table>
</td>
</tr>

<!-- LINHA DE BAIXO-->
<tr>

<!-- COLUNA ESQUERDA-->
<td align="left" valign="top">
<table width="100%" cellspacing="0" cellpadding="0">
<tr>
<td align="left" valign="top">
<!-- CLIENTE RESPONSAVEL PELO PEDIDO -->
          <table width="100%" border="1" cellpadding="2" cellspacing="0" class="tabela">
            <tr>
              <td align="center" scope="col"><p class="camposTabela"><span class="camposTabela"><h:outputText value="#{EmissaoDocumentosControle.eventoInteresse.interessado.nomeCliente}" /></span></p>
                <p class="camposTabela"><span class="camposTabela">Cliente Respons�vel pelo pedido de Bebidas</span></p></td>
              </tr>
            <tr>
              <td align="center" scope="row"><p><span class="camposTabela">${EmissaoDocumentosControle.dataHoje}</span></p>
                <p><span class="camposTabela">Data defini��o check-list pelo cliente</span></p></td>
              </tr>
          </table>
          
          <!-- SERVI�OS TERCEIRIZADOS -->
          <table id="tabelaFornecedor" width="100%" height="100%" border="1" cellpadding="2" cellspacing="0" class="tabela">
            <tr class="campos">
              <th scope="col">Terceiros</th>
              <th scope="col">Empresa</th>
              <th scope="col">Contato</th>
              <th scope="col">Fone</th>
              </tr>
      <!-- -------------------------SERVICOS TERCEIRIZADOS ------------------------------------------------ -->       
      <c:forEach var="servico" varStatus="index" items="${EmissaoDocumentosControle.negociacaoEvento.servicosTerceirizados}">
            <tr>
              <th scope="row"><span class="camposTabela">${servico.descricao}</span></th>
              <td class="camposTabela">${servico.fornecedorServico.fornecedor.descricao}</td>
              <td>${servico.fornecedorServico.fornecedor.contato}</td>
              <td>${servico.fornecedorServico.fornecedor.telefone}</td>
            </tr>
            <c:if test="${index.last}">
	      	<c:set var="tamanhoListaFornecedor" value="${index.index + 1}" />
	      </c:if>
      </c:forEach>
          </table></td>
<td align="left" valign="top">   <!-- ABERTURA EVENTO -->
    <table width="100%" width="100%" height="100%" border="1" cellpadding="2" cellspacing="0" class="tabela">
      <tr>
        <th  scope="col"><span class="camposTabela"><span class="campos"><span class="checkTituloGeral"><span class="campos">ABERTURA EVENTO</span></span></span></span></th>
        </tr>
      <tr>
        <th scope="row"> <p>&nbsp;</p>
          <p><span class="camposTabela">_______________________________          </span></p>
          <p><span class="camposTabela">Assinatura Funcion�rio Ipanema Sports</span></p></th>
        </tr>
      <tr>
        <th scope="row"><p>&nbsp;</p>
          <p><span class="camposTabela">_______________________________          </span></p>
          <p><span class="camposTabela">Assinatura Cliente Respons�vel***</span></p></th>
        </tr>
      <tr>
        <th scope="row"><p><span class="camposTabela">***Eu,__________________________ </span></p>
          <p><span class="camposTabela">autorizo__________________________ </span></p>
          <p><span class="camposTabela">a fazer a bertura e/ou reposi��o de bebidas.</span></p></th>
        </tr>
    </table></td>

</tr>


</table>
</td>




<!-- COLUNA DIREITA-->
<td align="left" valign="top">
<table width="100%" cellpadding="0" cellspacing="0">
<tr>
<td align="left" valign="top"> <!-- SALDO A COBRAR -->
          <table width="100%" border="1" cellpadding="2" cellspacing="0" class="tabela">
            <tr>
              <th colspan="2" class="campos" scope="col">SALDO A COBRAR (R$)</th>
              </tr>
            <tr>
              <td width="66%" scope="row"><span class="camposTabela">Sal�o</span></td>
              <td width="34%"><span class="camposTabela"> </span></td>
              </tr>
            <tr>
              <td scope="row"><span class="camposTabela">Seguran�a</span></td>
              <td><span class="camposTabela"> </span></td>
              </tr>
            <tr>
              <td scope="row"><span class="camposTabela">Quadra</span></td>
              <td><span class="camposTabela"> </span></td>
              </tr>
            <tr>
              <td scope="row"><span class="camposTabela">Monitor</span></td>
              <td><span class="camposTabela"> </span></td>
              </tr>
            <tr>
              <td scope="row"><span class="camposTabela">Bebidas</span></td>
              <td><span class="camposTabela"> </span></td>
              </tr>
              <tr>
              <td scope="row"><span class="camposTabela">Bebidas</span></td>
              <td><span class="camposTabela"> </span></td>
              </tr>
            <tr>
              <td scope="row"><span class="camposTabela">Cr�d. Bebibas</span></td>
              <td><span class="camposTabela"> </span></td>
              </tr>
            <tr>
              <td scope="row">&nbsp;</td>
              <td><span class="camposTabela"> </span></td>
              </tr>
            <tr>
              <td scope="row">&nbsp;</td>
              <td><span class="camposTabela"> </span></td>
              </tr>
            <tr>
              <td scope="row">&nbsp;</td>
              <td><span class="camposTabela"> </span></td>
              </tr>
            <tr>
              <td scope="row"><span class="camposTabela">Total</span></td>
              <td><span class="camposTabela"> </span></td>
              </tr>
            <tr>
              <td scope="row"><span class="camposTabela">Sub-Total</span></td>
              <td><div style="height: 25px;"><span class="camposTabela"> </span></div></td>
              </tr>
          </table></td>
<td align="left" valign="top">
          <!-- EXCEDENTES EVENTO -->
          <table width="100%" border="1" cellpadding="2" cellspacing="0" class="tabela">
            <tr>
              <th colspan="2" class="campos" scope="col">EXCEDENTES EVENTO(R$)</th>
              </tr>
            <tr>
              <td width="53%" scope="row"><span class="camposTabela">Sal�o</span></td>
              <td width="47%"><span class="camposTabela"> </span></td>
              </tr>
            <tr>
              <th colspan="2" scope="row">&nbsp;</th>
              </tr>
            <tr>
              <th scope="row"><span class="camposTabela">Seguran�a</span></th>
              <td><span class="camposTabela"> </span></td>
              </tr>
            <tr>
              <th colspan="2" scope="row">&nbsp;</th>
              </tr>
            <tr>
              <th scope="row"><span class="camposTabela">Gelo</span></th>
              <td><span class="camposTabela"> </span></td>
              </tr>
            <tr>
              <th colspan="2" scope="row">&nbsp;</th>
              </tr>
            <tr>
              <th scope="row"><span class="camposTabela">Quebras</span></th>
              <td><span class="camposTabela"> </span></td>
              </tr>
            <tr>
              <th colspan="2" scope="row">&nbsp;</th>
              </tr>
            <tr>
              <th scope="row">&nbsp;</th>
              <td>&nbsp;</td>
              </tr>
            <tr>
              <th colspan="2" scope="row">&nbsp;</th>
              </tr>
              <tr>
              <th colspan="2" scope="row"><div style="height: 30px;"><span class="camposTabela"> </span></div></th>
              </tr>
          </table></td>
</tr>
</table>
</td>
</tr>


</table>



<input type="hidden" id="tamanhoLista" value="${tamanhoLista}" />
<input type="hidden" id="tamanhoListaFornecedor" value="${tamanhoListaFornecedor}" />
</h:form>
</f:view>
</body>
</html>

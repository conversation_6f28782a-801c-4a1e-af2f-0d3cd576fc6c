<%--
    Document   : robo
    Created on : 07/12/2009, 08:28:49
    Author     : pedro
--%>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"
    "http://www.w3.org/TR/html4/loose.dtd">
<%@page contentType="text/html"%>
<%@page pageEncoding="ISO-8859-1"%>
<head><script type="text/javascript" language="javascript" src="./script/script.js"></script></head>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<link href="./css_pacto.css" rel="stylesheet" type="text/css">
<script type="text/javascript" language="javascript" src="hoverform.js"></script>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText value="Robô"/>
    </title>
    <rich:modalPanel id="panelConfirmacao" autosized="true" shadowOpacity="true" width="300" height="150" onshow="document.getElementById('formConfirmacao:userName').focus()">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Confirmação"></h:outputText>
            </h:panelGroup>
        </f:facet>
        <a4j:form id="formConfirmacao">
            <h:panelGrid id="panelResponsavel" columns="1" width="100%" columnClasses="colunaCentralizada">
                <h:panelGrid columns="1" style="height:25px; background-image:url('./imagens/fundoBarraTopo.png');background-repeat: repeat-x;" columnClasses="colunaCentralizada" width="100%">
                    <h:outputText styleClass="tituloFormulario" value="Confirmação"/>
                </h:panelGrid>

                <h:outputLabel rendered="#{!LoginControle.ativarPoolProcessamento}" value="Confirma execução desatualizada?"/>

                <h:panelGrid columns="2" width="100%" columnClasses="colunaCentralizado">
                    <a4j:commandButton id="loginConfirmacao" rendered="#{!LoginControle.apresentarProcessamento}" value="Sim" alt="Processar Rotina" action="#{LoginControle.habilitarProcessarRobo}" reRender="formConfirmacao"/>
                    <a4j:commandButton id="fecha"  rendered="#{!LoginControle.apresentarProcessamento}" value="Não" alt="Fechar Janela" oncomplete="Richfaces.hideModalPanel('panelConfirmacao')" reRender="form"/>
                </h:panelGrid>
                <h:panelGrid rendered="#{!LoginControle.apresentarProcessamento}" id="mensagem1">
                    <h:outputText styleClass="mensagem" value="#{LoginControle.mensagem}"/>
                    <h:outputText styleClass="mensagemDetalhada" value="#{LoginControle.mensagemDetalhada}"/>
                </h:panelGrid>
                <h:panelGrid  id="panelProcessamento" rendered="#{LoginControle.apresentarProcessamento}" columns="1"  width="100%"  columnClasses="colunaCentralizada" style="height:300;">
                    <a4j:poll interval="500"  enabled="#{LoginControle.ativarPool}" action="#{LoginControle.processarRoboDesatualizado}"  reRender="panelProcessamento" />
                    <a4j:poll interval="700" enabled="#{LoginControle.ativarPoolProcessamento}"  reRender="dataProcessada" />
                    <h:panelGroup id="panelGroupData" style="height:300;">
                        <rich:spacer width="20px"/>
                        <h:panelGroup id="dataProcessada">
                            <c:if test="${LoginControle.ativarPoolProcessamento}">
                                <h:outputText  value="Processando dia - #{LoginControle.dataProcessamentoRobo_Apresentar}"/>
                            </c:if>
                            <c:if test="${!LoginControle.ativarPoolProcessamento}">
                                <h:outputText  value="Processamento concluído com sucesso!"/>
                            </c:if>
                        </h:panelGroup>
                    </h:panelGroup>
                    <h:panelGrid rendered="#{!LoginControle.ativarPoolProcessamento}" id="mensagem2">
                        <h:outputText styleClass="mensagem" value="#{LoginControle.mensagem}"/>
                        <h:outputText styleClass="mensagemDetalhada" value="#{LoginControle.mensagemDetalhada}"/>
                    </h:panelGrid>
                    <a4j:commandButton id="fecha1" action="#{LoginControle.voltar}"
                                       rendered="#{!LoginControle.ativarPoolProcessamento}"
                                       value="#{msg_bt.btn_fechar}" alt="Fechar Janela"
                                       oncomplete="Richfaces.hideModalPanel('panelConfirmacao')"
                                       reRender="form"/>
                </h:panelGrid>
            </h:panelGrid>
        </a4j:form>
    </rich:modalPanel>

    <h:form id="form">
        <h:panelGrid id="panelGeral" width="100%">
            <table border="0" cellpadding="0" cellspacing="0" align="center" style="position:relative; top:150px; left:0px;">
                <tr>
                    <td align="center" valign="middle" >
                        <div id="login">
                            <div class="top"></div>
                            <div class="middle">

                                <div style="text-align:center;"><img src="images/logo_zw.png" alt="ZillyonWeb" width="130" height="52"></div>
                                <table width="477" border="0" cellpadding="0" cellspacing="0" style="height:200px">
                                    <tr>
                                        <td>
                                            <div align="center" style="font: 10pt 'Trebuchet MS', verdana, arial, helvetica, sans-serif;">
                                                <h:panelGrid columns="1">
                                                    <h:outputText styleClass="titulo7" value="Atenção!"/>
                                                    <h:outputText styleClass="mensagem" value="#{LoginControle.mensagemRobo}"/>
                                                    <h:panelGroup>
                                                        <a4j:commandButton rendered="#{LoginControle.apresentarBotoesRobo}"
                                                                           action="#{LoginControle.limparMensagem}"
                                                                           value="Iniciar processamento"
                                                                           oncomplete="Richfaces.showModalPanel('panelConfirmacao')"
                                                                           reRender="formConfirmacao"/>
                                                        <rich:spacer width="5px"/>
                                                        <h:commandButton  value="Voltar" action="#{LoginControle.voltar}"/>

                                                    </h:panelGroup>
                                                </h:panelGrid>
                                            </div>
                                        </td>
                                    </tr>
                                </table>
                            </div>
                            <div class="bottom"></div>
                        </div>
                    </td>
                </tr>
            </table>
        </h:panelGrid>

    </h:form>

</f:view>

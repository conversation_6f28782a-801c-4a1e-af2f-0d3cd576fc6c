<%-- 
    Document   : relatorioComissao
    Created on : Jul 31, 2012, 4:54:59 PM
    Author     : <PERSON> GeoInova <PERSON>
--%>

<%@include file="pages/estudio/includes/include_imports.jsp" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/functions" prefix="fn"%>
<c:set var="moduloSession" value="1" scope="session" />


<html>
    <head>
        <script type="text/javascript" src="${contexto}/script/basico.js"></script>
        <link href="${contexto}/css/otimize.css" rel="stylesheet" type="text/css">
        <link href="${contexto}/css/estudio.css" rel="stylesheet" type="text/css">
        <link href="${contexto}/css/otimizeCRM.css" rel="stylesheet" type="text/css">
        <jsp:include page="pages/estudio/includes/include_head.jsp" />

    </head>
    <body>
        <f:view>
            <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
        <title>
            <h:outputText value="Gest�o de Comiss�o"/>
        </title>
        <a4j:loadScript src="/script/jquery.maskedinput-1.2.2.js" />
        <h:form id="agendaGeral" prependId="false">
            <h:panelGroup layout="block" style="display: inline-table;width: 100%;" styleClass="fundoCinza">
                <h:panelGroup layout="block" styleClass="bgtop topoZW" rendered="#{MenuControle.apresentarTopo}">
                    <jsp:include page="include_topo_novo.jsp" flush="true"/>
                    <jsp:include page="include_menu_zw_flat.jsp" flush="true"/>
                    <rich:jQuery selector=".item3" query="addClass('menuItemAtual')"/>
                </h:panelGroup>

                <h:panelGroup layout="block" styleClass="caixaCorpo">
                    <h:panelGroup layout="block" style="height: 80%;width: 100%">
                        <h:panelGroup layout="block" styleClass="caixaMenuLatel">
                            <h:panelGroup layout="block" styleClass="container-imagem container-conteudo-central">
                                <h:panelGroup layout="block" styleClass="container-box zw_ui especial ">
                                    <h:panelGroup styleClass="container-box-header" layout="block">
                                        <h:panelGroup layout="block" styleClass="margin-box">
                                            <h:outputText value="Gest�o Comiss�o" styleClass="container-header-titulo"/>
                                            <h:outputLink styleClass="linkWiki"
                                                          value="#{SuperControle.urlBaseConhecimento}relatorio-gestao-de-comissao-do-modulo-agenda-studio/"
                                                          title="Clique e saiba mais: Gest�o de Comiss�o"
                                                          target="_blank" >
                                                <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                                            </h:outputLink>
                                        </h:panelGroup>
                                    </h:panelGroup>
                                    <h:panelGroup layout="block" styleClass="margin-box">
                                        <h:panelGrid columns="2" style="width:100%" columnClasses="column1, column1 ">

                                            <rich:column width="100%" style="vertical-align: top; border-bottom: 0; border-right: 0;">
                                                <h:panelGrid columns="1" columnClasses="column1" style="width:100%;">
                                                    <h:panelGrid columns="10" style="padding-bottom: 10px;" >
                                                        <h:outputText value="Filtrar por:" styleClass="texto_disponibilidade"/>
                                                        <a4j:commandButton
                                                                id="btnBuscarAmbiente"
                                                                image="imagens/estudio/ambiente.png"
                                                                value="Ambiente" styleClass="texto_disponibilidade"
                                                                title="Filtro(s) selecionado(s): #{RelatorioComissaoControle.listaNomeAmbiente}"
                                                                oncomplete="#{rich:component('panelFiltroAmbiente')}.show()"
                                                                action="#{RelatorioComissaoControle.acaoBuscarAmbiente}"
                                                                reRender="formPanelFiltroAmbiente">
                                                        </a4j:commandButton>
                                                        <rich:spacer width="20px;"/>
                                                        <a4j:commandButton
                                                                id="btnBuscarPessoa"
                                                                image="imagens/estudio/profissional.png"
                                                                value="Profissional" styleClass="texto_disponibilidade"
                                                                title="Filtro(s) selecionado(s): #{RelatorioComissaoControle.listaNomeColaborador}"
                                                                action="#{RelatorioComissaoControle.acaoBuscarPessoa}"
                                                                oncomplete="#{rich:component('panelFiltroProfissional')}.show()"
                                                                reRender="formPanelFiltroProfissional">
                                                        </a4j:commandButton>
                                                        <rich:spacer width="20px;"/>
                                                        <a4j:commandButton
                                                                id="btnBuscarServico"
                                                                image="imagens/estudio/servico.png"
                                                                value="Servi�o" styleClass="texto_disponibilidade"
                                                                title="Filtro(s) selecionado(s): #{RelatorioComissaoControle.listaNomeServico}"
                                                                oncomplete="focusAt('panelFiltroServico-servico-codigo');"
                                                                reRender="relatorioComissao-listaServico"
                                                                onclick="#{rich:component('panelFiltroServico')}.show()"/>
                                                        <rich:spacer width="20px;"/>
                                                        <a4j:commandButton
                                                                id="btnBuscarTipoHorario"
                                                                image="imagens/estudio/tipo_horario.png"
                                                                value="Tipos de Hor�rios" styleClass="texto_disponibilidade"
                                                                action="#{RelatorioComissaoControle.acaoBuscarTipoHorario}"
                                                                title="Filtro(s) selecionado(s): #{RelatorioComissaoControle.listaNomeTipoHorario}"
                                                                oncomplete="#{rich:component('panelFiltroTipoHorario')}.show()"
                                                                reRender="formPanelFiltroTipoHorario"/>
                                                        <rich:spacer width="20px;"/>
                                                        <a4j:commandButton
                                                                id="btnBuscarStatus"
                                                                image="imagens/estudio/status_sessao.png"
                                                                value="Status da Sess�o" styleClass="texto_disponibilidade"
                                                                title="Filtro(s) selecionado(s): #{RelatorioComissaoControle.listaNomeStatus}"
                                                                oncomplete="#{rich:component('panelFiltroStatus')}.show()"
                                                                reRender="formPanelFiltroStatus"/>
                                                    </h:panelGrid>
                                                    <h:panelGrid columns="10" id="periodos">
                                                        <h:outputLabel
                                                                value="Per�odo:" styleClass="texto_disponibilidade" />
                                                        <rich:calendar
                                                                locale="pt_BR"
                                                                inputSize="10"
                                                                inputClass="form"
                                                                oninputblur="blurinput(this);"
                                                                oninputfocus="focusinput(this);"
                                                                oninputchange="return validar_Data(this.id);"
                                                                datePattern="dd/MM/yyyy"
                                                                enableManualInput="true"
                                                                zindex="2"
                                                                showWeeksBar="false"
                                                                value="#{RelatorioComissaoControle.periodoInicial}"
                                                                id="relatorioComissao-periodoInicial"
                                                                popup="true"  styleClass="texto_disponibilidade">
                                                        </rich:calendar>

                                                        <h:outputLabel
                                                                value=" a " styleClass="texto_disponibilidade"/>
                                                        <rich:calendar
                                                                locale="pt_BR"
                                                                inputSize="10"
                                                                inputClass="form"
                                                                oninputblur="blurinput(this);"
                                                                oninputfocus="focusinput(this);"
                                                                oninputchange="return validar_Data(this.id);"
                                                                datePattern="dd/MM/yyyy"
                                                                enableManualInput="true"
                                                                zindex="2"
                                                                showWeeksBar="false"
                                                                value="#{RelatorioComissaoControle.periodoFinal}"
                                                                id="relatorioComissao-periodoFinal"
                                                                popup="true" styleClass="texto_disponibilidade" >
                                                        </rich:calendar>

                                                        <rich:spacer width="10px"/>
                                                        <h:outputLabel
                                                                value="Intervalo:" styleClass="texto_disponibilidade"/>
                                                        <h:selectOneMenu
                                                                id="relatorioComissao-listPeriodo"
                                                                value="#{RelatorioComissaoControle.periodoEnum}"
                                                                onblur="blurinput(this);"
                                                                onfocus="focusinput(this);"
                                                                styleClass="form">
                                                            <f:selectItems value="#{RelatorioComissaoControle.periodoSelect}" />
                                                            <a4j:support  event="onchange"
                                                                          action="#{RelatorioComissaoControle.acaoPeriodoIntervalo}"
                                                                          reRender="relatorioComissao-periodoInicial,relatorioComissao-periodoFinal" />
                                                        </h:selectOneMenu>

                                                        <rich:spacer width="10px"/>
                                                        <h:outputLabel
                                                                value="Situa��o:" styleClass="texto_disponibilidade"/>
                                                        <h:selectOneMenu
                                                                id="relatorioComissao-listSituacao"
                                                                value="#{RelatorioComissaoControle.situacaoEnum}"
                                                                onblur="blurinput(this);"
                                                                onfocus="focusinput(this);"
                                                                styleClass="form">
                                                            <f:selectItems value="#{RelatorioComissaoControle.situacaoSelect}" />
                                                            <a4j:support  event="onchange"
                                                                          action="#{RelatorioComissaoControle.acaoPeriodoIntervalo}"
                                                                          reRender="relatorioComissao-periodoInicial,relatorioComissao-periodoFinal" />
                                                        </h:selectOneMenu>
                                                        <rich:jQuery id="mskData" selector=".rich-calendar-input" timing="onload" query="mask('99/99/9999')" />
                                                    </h:panelGrid>

                                                    <h:panelGrid columns="2" style="width:100%;">
                                                        <h:panelGrid columns="6">
                                                            <h:outputLabel
                                                                    value="Hor�rios:" styleClass="texto_disponibilidade"/>
                                                            <h:selectOneMenu
                                                                    id="relatorioComissao-listaHorarioInicial"
                                                                    value="#{RelatorioComissaoControle.horaInicial}"
                                                                    onblur="blurinput(this);"
                                                                    onfocus="focusinput(this);"
                                                                    converter="timeConverter2"
                                                                    styleClass="form">
                                                                <f:selectItem itemLabel="" itemValue=""/>
                                                                <f:selectItems value="#{RelatorioComissaoControle.selectHorario}" />
                                                            </h:selectOneMenu>
                                                            <h:selectOneMenu
                                                                    id="relatorioComissao-listaHorarioFinal"
                                                                    value="#{RelatorioComissaoControle.horaFinal}"
                                                                    onblur="blurinput(this);"
                                                                    onfocus="focusinput(this);"
                                                                    converter="timeConverter2"
                                                                    styleClass="form">
                                                                <f:selectItem itemLabel="" itemValue=""/>
                                                                <f:selectItems value="#{RelatorioComissaoControle.selectHorario}" />
                                                            </h:selectOneMenu>

                                                            <rich:spacer width="20px"/>
                                                            <a4j:commandButton
                                                                    image="imagens/estudio/limpar_filtros.png"
                                                                    reRender="mensagens,agendaGeral,relatorioComissaoControle-listaServico"
                                                                    title="Limpar Filtros"
                                                                    action="#{RelatorioComissaoControle.limparDados}"/>

                                                            <a4j:commandButton
                                                                    style="float:right;"
                                                                    action="#{RelatorioComissaoControle.acaoPesquisar}"
                                                                    image="imagens/estudio/pesquisar.png"
                                                                    title="Pesquisa Inteligente"
                                                                    reRender="relatorioComissao-listaRelatorioComissao, periodos, modalPanelErro, panelRegistro,mensagens"/>
                                                        </h:panelGrid>
                                                        <h:panelGrid columns="3" style="float:right;">
                                                            <a4j:commandButton
                                                                    id="imprimirPDF" ajaxSingle="false"
                                                                    action="#{RelatorioComissaoControle.acaoImprimir}"
                                                                    value="Imprimir"
                                                                    image="imagens/estudio/imprimir.png"
                                                                    style="float:right;"
                                                                    oncomplete="abrirPopupPDFImpressao('relatorio/#{RelatorioComissaoControle.nomeArquivoRelatorioGeradoAgora}','', 780, 595);"
                                                                    accesskey="2" styleClass="botoes"/>
                                                            <a4j:commandLink
                                                                    action="#{RelatorioComissaoControle.acaoImprimirExcel}"
                                                                    oncomplete="location.href='DownloadSV?mimeType=application/vnd.ms-excel&relatorio=comissaoEstudioPactoExcel.xlsx'"
                                                                    style="float:right;">
                                                                <h:graphicImage url="imagens/estudio/excel.png" style="border:none;" title="Imprimir Lista(Excel)" />
                                                            </a4j:commandLink>
                                                        </h:panelGrid>
                                                    </h:panelGrid>
                                                    <h:panelGroup id="mensagens">
                                                        <h:outputText styleClass="mensagemDetalhada" value="#{RelatorioComissaoControle.mensagemDetalhada}"/>
                                                    </h:panelGroup>
                                                    <h:panelGrid columns="5" id="panelRegistro" style="float:right;">
                                                        <h:outputText
                                                                id="registro"
                                                                value="#{fn:length(RelatorioComissaoControle.listaRelatorioComissao)} sess�es no valor total de R$ #{RelatorioComissaoControle.somaUnitario}
                                                                        - o valor em comiss�o: R$ #{RelatorioComissaoControle.somaComissao}"
                                                                style="font-size:10;"
                                                                rendered="#{fn:length(RelatorioComissaoControle.listaRelatorioComissao) > 0}" />
                                                    </h:panelGrid>
                                                </h:panelGrid>

                                                <rich:extendedDataTable
                                                        id="relatorioComissao-listaRelatorioComissao"
                                                        width="100%"
                                                        height="500px"
                                                        headerClass="consulta"
                                                        columnClasses="colunaEsquerda,colunaEsquerda,colunaEsquerda,colunaEsquerda,colunaEsquerda,colunaEsquerda,colunaEsquerda,colunaEsquerda,colunaEsquerda, colunaDireita, colunaDireita"
                                                        rowClasses="linhaImpar, linhaPar"
                                                        styleClass="textsmall"
                                                        value="#{RelatorioComissaoControle.listaRelatorioComissao}"
                                                        var="item" >

                                                    <rich:column label="Profissional"
                                                                 id="item-descColaborador"
                                                                 width="7%"
                                                                 sortable="true"
                                                                 selfSorted="true"
                                                                 sortBy="#{item.descColaborador}"
                                                                 filterEvent="onkeyup">
                                                        <f:facet name="header">
                                                            <h:outputText value="Profissional"/>
                                                        </f:facet>
                                                        <a4j:commandLink action="#{RelatorioComissaoControle.editarAgendamento}"
                                                                         reRender="toolTipAgenda">
                                                            <h:outputText value="#{item.descColaborador}"/>
                                                        </a4j:commandLink>
                                                    </rich:column>
                                                    <rich:column label="Servi�o"
                                                                 id="item-descProduto"
                                                                 width="8%"
                                                                 sortable="true"
                                                                 selfSorted="true"
                                                                 sortBy="#{item.descProduto}"
                                                                 filterEvent="onkeyup">
                                                        <f:facet name="header">
                                                            <h:outputText value="Servi�o"/>
                                                        </f:facet>
                                                        <a4j:commandLink action="#{RelatorioComissaoControle.editarAgendamento}"
                                                                         reRender="toolTipAgenda" title="#{item.descProduto}" >
                                                            <h:outputText value="#{item.descProduto}"/>
                                                        </a4j:commandLink>
                                                    </rich:column>

                                                    <rich:column label="%"
                                                                 id="item-proc"
                                                                 width="3%"
                                                                 sortable="true"
                                                                 selfSorted="true"
                                                                 sortBy="#{item.porcentagem}"
                                                                 filterEvent="onkeyup">
                                                        <f:facet name="header">
                                                            <h:outputText value="%"/>
                                                        </f:facet>
                                                        <a4j:commandLink action="#{RelatorioComissaoControle.editarAgendamento}"
                                                                         reRender="toolTipAgenda" title="#{item.porcentagem}" >
                                                            <h:outputText value="#{item.porcentagem}"/>
                                                        </a4j:commandLink>
                                                    </rich:column>

                                                    <rich:column label="Matr�cula"
                                                                 id="item-codgMatricula"
                                                                 sortable="true"
                                                                 width="6%"
                                                                 selfSorted="true"
                                                                 sortBy="#{item.codgMatricula}"
                                                                 filterEvent="onkeyup">
                                                        <f:facet name="header">
                                                            <h:outputText value="Matr�cula"/>
                                                        </f:facet>
                                                        <a4j:commandLink action="#{RelatorioComissaoControle.editarAgendamento}"
                                                                         reRender="toolTipAgenda" >
                                                            <h:outputText value="#{item.codgMatricula}"/>
                                                        </a4j:commandLink>
                                                    </rich:column>


                                                    <rich:column label="Cliente"
                                                                 id="item-descCliente"
                                                                 width="9%"
                                                                 sortable="true"
                                                                 selfSorted="true"
                                                                 sortBy="#{item.descCliente}"
                                                                 filterEvent="onkeyup">
                                                        <f:facet name="header">
                                                            <h:outputText value="Cliente"/>
                                                        </f:facet>
                                                        <a4j:commandLink action="#{RelatorioComissaoControle.editarAgendamento}"
                                                                         reRender="toolTipAgenda" >
                                                            <h:outputText value="#{item.descCliente}"/>
                                                        </a4j:commandLink>
                                                    </rich:column>
                                                    <rich:column label="Data Aula"
                                                                 id="item-dataAula"
                                                                 width="6%"
                                                                 sortable="true"
                                                                 selfSorted="true"
                                                                 sortBy="#{item.dataAula}"
                                                                 filterEvent="onkeyup">
                                                        <f:facet name="header">
                                                            <h:outputText value="Data Aula"/>
                                                        </f:facet>
                                                        <a4j:commandLink action="#{RelatorioComissaoControle.editarAgendamento}"
                                                                         reRender="toolTipAgenda" >
                                                            <h:outputText value="#{item.dataAula}" converter="dataConverter"/>
                                                        </a4j:commandLink>
                                                    </rich:column>
                                                    <rich:column label="Hor�rio"
                                                                 id="item-horaInicio"
                                                                 sortable="true"
                                                                 width="5%"
                                                                 selfSorted="true"
                                                                 sortBy="#{item.horaAula}"
                                                                 filterEvent="onkeyup">
                                                        <f:facet name="header">
                                                            <h:outputText value="Hor�rio"/>
                                                        </f:facet>
                                                        <a4j:commandLink action="#{RelatorioComissaoControle.editarAgendamento}"
                                                                         reRender="toolTipAgenda" >
                                                            <h:outputText value="#{item.horaInicio}" converter="timeConverter"/>
                                                            <h:outputText rendered="#{item.horaTermino!=null}" value="-" escape="false"/>
                                                            <h:outputText rendered="#{item.horaTermino!=null}" value="#{item.horaTermino}" converter="timeConverter"/>
                                                        </a4j:commandLink>
                                                    </rich:column>
                                                    <rich:column label="Ambiente"
                                                                 id="item-descAmbiente"
                                                                 width="7%"
                                                                 sortable="true"
                                                                 selfSorted="true"
                                                                 sortBy="#{item.descAmbiente}"
                                                                 filterEvent="onkeyup">
                                                        <f:facet name="header">
                                                            <h:outputText value="Ambiente"/>
                                                        </f:facet>
                                                        <a4j:commandLink action="#{RelatorioComissaoControle.editarAgendamento}"
                                                                         reRender="toolTipAgenda" >
                                                            <h:outputText value="#{item.descAmbiente}"/>
                                                        </a4j:commandLink>
                                                    </rich:column>
                                                    <rich:column label="Status"
                                                                 id="item-status"
                                                                 sortable="true"
                                                                 selfSorted="true"
                                                                 width="5%"
                                                                 sortBy="#{item.descStatus}"
                                                                 filterEvent="onkeyup">
                                                        <f:facet name="header">
                                                            <h:outputText value="Status"/>
                                                        </f:facet>
                                                        <a4j:commandLink action="#{RelatorioComissaoControle.editarAgendamento}"
                                                                         reRender="toolTipAgenda" >
                                                            <h:outputText value="#{item.descStatus}"/>
                                                        </a4j:commandLink>
                                                    </rich:column>
                                                    <rich:column label="TH"
                                                                 id="item-tipoHorarioVO"
                                                                 sortable="true"
                                                                 width="04%"
                                                                 selfSorted="true"
                                                                 sortBy="#{item.siglaTipoAula}"
                                                                 filterEvent="onkeyup">
                                                        <f:facet name="header">
                                                            <h:outputText value="TH"/>
                                                        </f:facet>
                                                        <a4j:commandLink action="#{RelatorioComissaoControle.editarAgendamento}"
                                                                         reRender="toolTipAgenda" >
                                                            <h:outputText value="#{item.siglaTipoAula}"/>
                                                        </a4j:commandLink>
                                                    </rich:column>
                                                    <rich:column label="Vl. Unit."
                                                                 id="item-valorUnitario"
                                                                 sortable="true"
                                                                 width="5%"
                                                                 selfSorted="true"
                                                                 sortBy="#{item.valorUnitario}"
                                                                 filterEvent="onkeyup">
                                                        <f:facet name="header">
                                                            <h:outputText value="Vl. Unit."/>
                                                        </f:facet>
                                                        <a4j:commandLink action="#{RelatorioComissaoControle.editarAgendamento}"
                                                                         reRender="toolTipAgenda" >
                                                            <h:outputText value="#{item.valorUnitario}">
                                                                <f:convertNumber pattern="#,###0.00"  />
                                                            </h:outputText>
                                                        </a4j:commandLink>
                                                    </rich:column>
                                                    <rich:column label="Vl. Comiss�o"
                                                                 id="item-valorComissao"
                                                                 width="6%"
                                                                 sortable="true"
                                                                 selfSorted="true"
                                                                 sortBy="#{item.valorComissao}"
                                                                 filterEvent="onkeyup">
                                                        <f:facet name="header">
                                                            <h:outputText value="Vl. Comiss�o"/>
                                                        </f:facet>
                                                        <a4j:commandLink action="#{RelatorioComissaoControle.editarAgendamento}"
                                                                         reRender="toolTipAgenda" >
                                                            <h:outputText value="#{item.valorComissao}">
                                                                <f:convertNumber pattern="#,###0.00"  />
                                                            </h:outputText>
                                                        </a4j:commandLink>
                                                    </rich:column>

                                                    <rich:column label="Profissional Indica��o"
                                                                 id="item-descColaboradorIndicacao"
                                                                 width="9%"
                                                                 sortable="true"
                                                                 selfSorted="true"
                                                                 sortBy="#{item.descColaboradorIndicacao}"
                                                                 filterEvent="onkeyup">
                                                        <f:facet name="header">
                                                            <h:outputText value="Profissional Indica��o"/>
                                                        </f:facet>
                                                        <a4j:commandLink action="#{RelatorioComissaoControle.editarAgendamento}"
                                                                         reRender="toolTipAgenda">
                                                            <h:outputText value="#{item.descColaboradorIndicacao}"/>
                                                        </a4j:commandLink>
                                                    </rich:column>


                                                    <rich:column label="Vl. Comiss�o Indica��o"
                                                                 id="item-valorComissaoIndicacao"
                                                                 width="9%"
                                                                 sortable="true"
                                                                 selfSorted="true"
                                                                 sortBy="#{item.valorComissaoIndicacao}"
                                                                 filterEvent="onkeyup">
                                                        <f:facet name="header">
                                                            <h:outputText value="Vl. Comiss�o Indica��o"/>
                                                        </f:facet>
                                                        <a4j:commandLink action="#{RelatorioComissaoControle.editarAgendamento}"
                                                                         reRender="toolTipAgenda" >
                                                            <h:outputText value="#{item.valorComissaoIndicacao}">
                                                                <f:convertNumber pattern="#,###0.00"  />
                                                            </h:outputText>
                                                        </a4j:commandLink>
                                                    </rich:column>

                                                    <rich:column label="Situa��o"
                                                                 id="item-situacaoParcela"
                                                                 sortable="true"
                                                                 width="9%"
                                                                 selfSorted="true"
                                                                 sortBy="#{item.situacaoParcela.descricao}"
                                                                 filterEvent="onkeyup">
                                                        <f:facet name="header">
                                                            <h:outputText value="Situa��o"/>
                                                        </f:facet>
                                                        <a4j:commandLink action="#{RelatorioComissaoControle.editarAgendamento}"
                                                                         reRender="toolTipAgenda" >
                                                            <h:outputText value="#{item.situacaoParcela.descricao}"/>
                                                        </a4j:commandLink>
                                                    </rich:column>
                                                </rich:extendedDataTable>
                                            </rich:column>
                                        </h:panelGrid>
                                    </h:panelGroup>
                                    <a4j:commandButton style="visibility: hidden;" reRender="panelExpiracaoSenha"
                                                       id="btnAtualizaPagina">
                                    </a4j:commandButton>
                                </h:panelGroup>

                            </h:panelGroup>
                            <jsp:include page="menuRelatorio.jsp" flush="true"/>
                        </h:panelGroup>
                    </h:panelGroup>
                </h:panelGroup>
                <jsp:include page="include_rodape_flat.jsp" flush="true" />
            </h:panelGroup>
        </h:form>

        <!-- MODAL FILTRO SERVI�O -->
        <rich:modalPanel id="panelFiltroServico" autosized="true" shadowOpacity="true"
                         showWhenRendered="false" width="50" onshow="focusAt('panelFiltroServico-servico-codigo'); #{rich:component('mykeyservico')}.enable();"
                         height="240">
            <f:facet name="header">
                <h:panelGroup>
                    <h:outputText value="Servi�o" styleClass="texto_disponibilidade"/>
                </h:panelGroup>
            </f:facet>
            <f:facet name="controls">
                <h:panelGroup>
                    <h:graphicImage value="imagens/close.png" style="cursor:pointer" id="hidelinkPanelFiltroServico" />
                    <rich:componentControl for="panelFiltroServico" attachTo="hidelinkPanelFiltroServico"
                                           operation="hide" event="onclick" />
                </h:panelGroup>
            </f:facet>

            <a4j:form id="formPanelFiltroServico" prependId="false" ajaxSingle="true">
                <h:panelGrid columns="3" id="dadosServico">
                    <h:outputLabel
                        value="C�digo" styleClass="texto_disponibilidade"/>
                    <rich:spacer width="20px"/>
                    <h:outputLabel
                        value="Descri��o" styleClass="texto_disponibilidade" />

                    <h:inputText
                        maxlength="4"
                        autocomplete="off"
                        onkeydown="return tabOnEnter(event, 'panelFiltroServico-servico-descricao');"
                        size="3"
                        onblur="blurinput(this);" onfocus="focusinput(this); getById('panelFiltroServico-servico-codigo').select();"
                        styleClass="form"
                        value="#{RelatorioComissaoControle.produtoVO.codigo}"
                        id="panelFiltroServico-servico-codigo">
                        <a4j:support event="onchange" oncomplete="focusAt('add');" action="#{RelatorioComissaoControle.acaoProcurarServico}"
                                     reRender="panelFiltroServico-servico-descricao,modalServicoSuggestion,modalPanelErro"/>
                    </h:inputText>
                    <rich:spacer width="20px"/>
                    <h:panelGrid columns="4">
                        <h:inputText
                            maxlength="50"
                            autocomplete="off"
                            style="width: 290px;"
                            onblur="blurinput(this);" onfocus="focusinput(this); getById('panelFiltroServico-servico-descricao').select();"
                            onkeydown="return tabOnEnter(event, 'add');"
                            styleClass="form"
                            value="#{RelatorioComissaoControle.produtoVO.descricao}"
                            id="panelFiltroServico-servico-descricao">
                        </h:inputText>
                        <rich:suggestionbox
                            id="modalServicoSuggestion"
                            width="290"
                            status="none"
                            immediate="true"
                            for="panelFiltroServico-servico-descricao"
                            suggestionAction="#{RelatorioComissaoControle.listarServico}"
                            minChars="1" rowClasses="30"
                            fetchValue="#{item.descricao}"
                            var="item"
                            nothingLabel="Nenhum dado encontrado" >
                            <h:column>
                                <h:outputText value="#{item.descricao}"/>
                            </h:column>
                            <a4j:support event="onselect" reRender="panelFiltroServico-servico-codigo" focus="add">
                                <f:setPropertyActionListener
                                    target="#{RelatorioComissaoControle.produtoVO}"
                                    value="#{item}" />
                            </a4j:support>
                        </rich:suggestionbox>
                        <rich:spacer width="10px"/>
                        <a4j:commandButton
                            image="imagens/estudio/adicionar.png"
                            value="Adicionar"
                            id="add"
                            focus="panelFiltroServico-servico-codigo"
                            reRender="relatorioComissaoControle-listaServico,dadosServico,modalServicoSuggestion,modalPanelErro"
                            action="#{RelatorioComissaoControle.acaoAdicionarServico}"
                            title="Adiciona o servi�o informado a tabela."/>
                    </h:panelGrid>
                </h:panelGrid>

                <rich:spacer height="5" />
                <h:panelGrid columns="2">
                    <rich:scrollableDataTable
                        id="relatorioComissaoControle-listaServico"
                        onRowClick="getById('relatorioComissaoControle-listaServico:'+ this.rowIndex +':filtro').onclick();"
                        var="item"
                        height="132px"
                        width="472px"
                        frozenColCount="1"
                        value="#{RelatorioComissaoControle.listaServicoTabela}">

                        <rich:column sortable="false" width="38px">
                            <f:facet name="header">
                                <h:selectBooleanCheckbox
                                    id="selecionarTodosProduto"
                                    value="#{RelatorioComissaoControle.selecionarTodosItemServico}">
                                    <a4j:support
                                        status="none"
                                        action="#{RelatorioComissaoControle.acaoSelecionarTodosServico}"
                                        event="onclick"
                                        reRender="itemSolicitacao-selecionado-servico">
                                    </a4j:support>
                                </h:selectBooleanCheckbox>
                            </f:facet>
                            <h:selectBooleanCheckbox
                                id="itemSolicitacao-selecionado-servico"
                                style="margin-left: 14px;"
                                value="#{item.selecionado}">
                                <a4j:support
                                    status="none"
                                    action="#{RelatorioComissaoControle.acaoSelecionarUmServico}"
                                    event="onclick"
                                    reRender="selecionarTodosProduto">
                                </a4j:support>
                            </h:selectBooleanCheckbox>
                        </rich:column>

                        <rich:column
                            width="70px">
                            <f:facet name="header">
                                <h:outputText value="C�digo" styleClass="texto_disponibilidade" />
                            </f:facet>
                            <h:outputText
                                value="#{item.codigo}"
                                style="float:right; margin-right: 5px;"/>
                        </rich:column>
                        <rich:column
                            width="321px"
                            sortBy="#{item.descricao}"
                            filterBy="#{item.descricao}"
                            filterEvent="onchange">
                            <f:facet name="header">
                                <h:outputText value="Descri��o" styleClass="texto_disponibilidade" />
                            </f:facet>
                            <h:outputText
                                value="#{item.descricao}"
                                style="margin-left: 5px; position:relative;"/>
                        </rich:column>
                        <rich:column width="20px">
                            <a4j:commandLink action="#{RelatorioComissaoControle.acaoRemoverServico}" reRender="relatorioComissaoControle-listaServico">
                                <h:graphicImage value="imagens/estudio/icon_delete.png" style="cursor:pointer" id="rmvItem" />
                                <f:setPropertyActionListener target="#{RelatorioComissaoControle.produtoVO}" value="#{item}"/>
                            </a4j:commandLink>
                        </rich:column>
                    </rich:scrollableDataTable>
                </h:panelGrid>

                <rich:spacer height="10px"/>
                <h:panelGrid columns="2">
                    <rich:spacer width="385px"/>
                    <a4j:commandButton
                        image="imagens/estudio/fechar.png"
                        value="Fechar"
                        action="#{RelatorioComissaoControle.acaoFecharServico}"
                        reRender="btnBuscarServico"
                        title="Fecha pop-up de servi�o."
                        id="fecharServico"
                        onclick="#{rich:component('panelFiltroServico')}.hide();#{rich:component('mykeyservico')}.disable();" />
                    <rich:hotKey
                        id="mykeyservico"
                        key="esc"
                        handler="#{rich:component('panelFiltroServico')}.hide();"/>
                </h:panelGrid>
            </a4j:form>
        </rich:modalPanel>

        <!-- MODAL FILTRO AMBIENTE -->
        <rich:modalPanel id="panelFiltroAmbiente" autosized="true" shadowOpacity="true"
                         showWhenRendered="false" width="550" onshow="#{rich:component('mykeyambiente')}.enable();"
                         height="280">
            <f:facet name="header">
                <h:panelGroup>
                    <h:outputText value="Ambiente" styleClass="texto_disponibilidade"/>
                </h:panelGroup>
            </f:facet>
            <f:facet name="controls">
                <h:panelGroup>
                    <h:graphicImage value="imagens/close.png" style="cursor:pointer" id="hidelinkPanelFiltroAmbiente" />
                    <rich:componentControl for="panelFiltroAmbiente" attachTo="hidelinkPanelFiltroAmbiente"
                                           operation="hide" event="onclick" />
                </h:panelGroup>
            </f:facet>

            <a4j:form id="formPanelFiltroAmbiente" prependId="false">
                <h:outputText value="Selecione o ambiente que deseja pesquisar:" styleClass="texto_disponibilidade"/>
                <div style="overflow-y: auto; overflow-x: hidden; height: 180px; padding-top: 05px;" >
                    <h:selectManyCheckbox
                        id="ambienteSelect"
                        style="text-align: left;"
                        styleClass="texto_disponibilidade"
                        layout="pageDirection"
                        value="#{RelatorioComissaoControle.listaAmbienteSelecionado}">
                        <f:selectItems value="#{RelatorioComissaoControle.listaAmbienteSelect}" />
                        <a4j:support event="onchange" reRender="formPanelFiltroAmbiente"/>
                    </h:selectManyCheckbox>
                </div>
                <rich:spacer height="05px"/>
                <h:panelGrid style="position: relative; margin-left:500px">
                    <a4j:commandButton
                        image="imagens/estudio/fechar.png"
                        value="Fechar"
                        action="#{RelatorioComissaoControle.acaoFecharAmbiente}"
                        reRender="btnBuscarAmbiente"
                        title="Fecha pop-up de ambiente."
                        onclick="#{rich:component('panelFiltroAmbiente')}.hide();#{rich:component('mykeyambiente')}.disable();" />
                    <rich:hotKey
                        id="mykeyambiente"
                        key="esc"
                        handler="#{rich:component('panelFiltroAmbiente')}.hide();"/>
                </h:panelGrid>
            </a4j:form>

        </rich:modalPanel>

        <!-- MODAL FILTRO PROFISSIONAL -->
        <rich:modalPanel id="panelFiltroProfissional" autosized="true" shadowOpacity="true"
                         showWhenRendered="false" width="700" onshow="#{rich:component('mykeyprofissional')}.enable();"
                         height="280">
            <f:facet name="header">
                <h:panelGroup>
                    <h:outputText value="Profissional" styleClass="texto_disponibilidade"/>
                </h:panelGroup>
            </f:facet>

            <a4j:form id="formPanelFiltroProfissional" >
                <h:outputText value="Selecione o profissional que deseja pesquisar:" styleClass="texto_disponibilidade"/>
                <div style="overflow-y: auto; overflow-x: hidden; height: 180px; padding-top: 05px;" >
                    <h:panelGrid id="panelProfessores" width="100%" columns="2">
                        <h:selectManyCheckbox id="profissionalSelect1"
                                              style="text-align: left;"
                                              styleClass="texto_disponibilidade"
                                              layout="pageDirection"
                                              value="#{RelatorioComissaoControle.listaUmColaboradorSelecionado}">
                            <f:selectItems value="#{RelatorioComissaoControle.listaUmPessoaSelect}"/>
                            <a4j:support event="onchange" reRender="formPanelFiltroProfissional"/>
                        </h:selectManyCheckbox>

                        <h:selectManyCheckbox id="profissionalSelect2"
                                              style="text-align: left;"
                                              styleClass="texto_disponibilidade"
                                              layout="pageDirection"
                                              value="#{RelatorioComissaoControle.listaDoisColaboradorSelecionado}">
                            <f:selectItems value="#{RelatorioComissaoControle.listaDoisPessoaSelect}"/>
                            <a4j:support event="onchange" reRender="formPanelFiltroProfissional"/>
                        </h:selectManyCheckbox>
                    </h:panelGrid>
                </div>
                <rich:spacer height="05px"/>
                <h:panelGrid style="float:right;">
                    <a4j:commandButton
                        image="imagens/estudio/fechar.png"
                        value="Fechar"
                        action="#{RelatorioComissaoControle.acaoFecharPessoa}"
                        reRender="btnBuscarPessoa"
                        title="Fecha pop-up de profissional."
                        onclick="#{rich:component('panelFiltroProfissional')}.hide();#{rich:component('mykeyprofissional')}.disable();" />
                    <rich:hotKey
                        id="mykeyprofissional"
                        key="esc"
                        handler="#{rich:component('panelFiltroProfissional')}.hide();"/>
                </h:panelGrid>
            </a4j:form>
        </rich:modalPanel>

        <!-- MODAL TIPO HOR�RIO -->
        <rich:modalPanel id="panelFiltroTipoHorario" autosized="true" shadowOpacity="true"
                         showWhenRendered="false" width="550" onshow="#{rich:component('mykeytipohorario')}.enable();"
                         height="280">
            <f:facet name="header">
                <h:panelGroup>
                    <h:outputText value="Tipo Hor�rio" styleClass="texto_disponibilidade"/>
                </h:panelGroup>
            </f:facet>
            <f:facet name="controls">
                <h:panelGroup>
                    <h:graphicImage value="imagens/close.png" style="cursor:pointer" id="hidelinkPanelFiltroTipoHorario" />
                    <rich:componentControl for="panelFiltroTipoHorario" attachTo="hidelinkPanelFiltroTipoHorario"
                                           operation="hide" event="onclick" />
                </h:panelGroup>
            </f:facet>

            <a4j:form id="formPanelFiltroTipoHorario" prependId="false">
                <h:outputText value="Selecione o tipo de hor�rio que deseja pesquisar:" styleClass="texto_disponibilidade"/>
                <div style="overflow-y: auto; overflow-x: hidden; height: 180px; padding-top: 05px;" >
                    <h:selectManyCheckbox
                        id="tipoHorarioSelect"
                        style="text-align: left;"
                        styleClass="texto_disponibilidade"
                        layout="pageDirection"
                        value="#{RelatorioComissaoControle.listaTipoHorarioSelecionado}">
                        <f:selectItems value="#{RelatorioComissaoControle.listaTipoHorarioSelect}" />
                        <a4j:support event="onchange" reRender="formPanelFiltroTipoHorario"/>
                    </h:selectManyCheckbox>
                </div>
                <rich:spacer height="05px"/>
                <h:panelGrid style="position: relative; margin-left:500px">
                    <a4j:commandButton
                        image="imagens/estudio/fechar.png"
                        value="Fechar"
                        action="#{RelatorioComissaoControle.acaoFecharTipoHorario}"
                        reRender="btnBuscarTipoHorario"
                        title="Fecha pop-up de tipo de hor�rio."
                        onclick="#{rich:component('panelFiltroTipoHorario')}.hide();#{rich:component('mykeytipohorario')}.disable();" />
                    <rich:hotKey
                        id="mykeytipohorario"
                        key="esc"
                        handler="#{rich:component('panelFiltroTipoHorario')}.hide();"/>
                </h:panelGrid>
            </a4j:form>
        </rich:modalPanel>

        <!-- MODAL STATUS -->
        <rich:modalPanel id="panelFiltroStatus" autosized="true" shadowOpacity="true"
                         showWhenRendered="false" width="550" onshow="#{rich:component('mykeystatus')}.enable();"
                         height="280">
            <f:facet name="header">
                <h:panelGroup>
                    <h:outputText value="Status da Sess�o" styleClass="texto_disponibilidade"/>
                </h:panelGroup>
            </f:facet>
            <f:facet name="controls">
                <h:panelGroup>
                    <h:graphicImage value="imagens/close.png" style="cursor:pointer" id="hidelinkPanelFiltroStatus" />
                    <rich:componentControl for="panelFiltroStatus" attachTo="hidelinkPanelFiltroStatus"
                                           operation="hide" event="onclick" />
                </h:panelGroup>
            </f:facet>

            <a4j:form id="formPanelFiltroStatus"  prependId="false">
                <h:outputText value="Selecione o status da sess�o que deseja pesquisar:" styleClass="texto_disponibilidade"/>
                <div style="overflow-y: auto; overflow-x: hidden; height: 180px; padding-top: 05px;" >
                    <h:selectManyCheckbox
                        id="statusSelect"
                        style="text-align: left;"
                        styleClass="texto_disponibilidade"
                        layout="pageDirection"
                        value="#{RelatorioComissaoControle.listaStatusSelecionado}">
                        <f:selectItems value="#{RelatorioComissaoControle.listaStatusSelect}" />
                        <a4j:support event="onchange" reRender="formPanelFiltroStatus"/>
                    </h:selectManyCheckbox>
                </div>
                <rich:spacer height="05px"/>
                <h:panelGrid style="position: relative; margin-left:500px">
                    <a4j:commandButton
                        image="imagens/estudio/fechar.png"
                        value="Fechar"
                        action="#{RelatorioComissaoControle.acaoFecharStatus}"
                        reRender="btnBuscarStatus"
                        title="Fecha pop-up de status da sess�o."
                        onclick="#{rich:component('panelFiltroStatus')}.hide();#{rich:component('mykeystatus')}.disable();" />
                    <rich:hotKey
                        id="mykeystatus"
                        key="esc"
                        handler="#{rich:component('panelFiltroStatus')}.hide();"/>
                </h:panelGrid>
            </a4j:form>
        </rich:modalPanel>

        <%@include  file="pages/estudio/includes/include_modal_dados_aluno.jsp" %>
        <%@include  file="pages/estudio/includes/include_modal_agenda_aluno.jsp" %>
          <%@include file="pages/estudio/includes/include_modal_erro.jsp" %>
        <%@include file="pages/estudio/includes/include_modal_sucesso.jsp" %>
    </f:view>
</body>
</html>

::-webkit-scrollbar{width:8px;height:6px;margin-right:2px;}
::-webkit-scrollbar-button:start:decrement,::-webkit-scrollbar-button:end:increment{display:block;height:0px;}
::-webkit-scrollbar-button:vertical:end:increment{background:#FCF9F9;border-right:1px solid #ccc;border-left:1px solid #ccc;border-top:1px solid #ccc;}
::-webkit-scrollbar-button:vertical:increment{background-color:#FCF9F9;border-right:1px solid #ccc;border-left:1px solid #ccc;border-bottom:1px solid #ccc;border-top:1px solid #ccc;}
::-webkit-scrollbar-track-piece{background:#FCF9F9;border-right:1px solid #ccc;border-left:1px solid #ccc;border-top:1px solid #ccc;}
::-webkit-scrollbar-thumb:vertical{background-color:#ccc;}
::-webkit-scrollbar-thumb:vertical:hover{background-color:#666;}
::-webkit-scrollbar-thumb:vertical:active{background-color:#333;}
body {margin:0px;background-color:#e6e6e6;font-family: Arial !important;}
html, body {
    font-size: 14px;
}
body.finanBI {
    /* Fix BI financeiro, decidir se aplicar geral ou manter especifico */
    min-width: 1366px;
}
p, tr, td, div {
    margin:0
}
/*--- Login ---*/
#login{
    width:562px;
}
#login .top{
    height:22px;
    background-image:url(images/login_top.jpg);
    background-repeat:no-repeat;
    background-position:top;
}
.linkTabela:hover{
    text-decoration: underline;
    cursor: pointer;
}
#login .topNFe{
    height: 80px;
    background: url("images/nfe/tela_login_topo.png") no-repeat top;
}
#login .middle{
    border-left:solid 10px #c4d6de;
    border-right:solid 10px #c4d6de;
    background-color:#fff;
    text-align:left;
    background-image:url(images/login_middle.jpg);
    background-repeat:no-repeat;
    background-position:bottom;
    padding:0 32px;
}

#login .middleNFe{
    height: 298px;
    background: url("images/nfe/tela_login_meio.png") no-repeat bottom;
}

#login .bottom{
    height:20px;
    background-image:url(images/login_bottom.jpg);
    background-repeat:no-repeat;
    background-position:bottom;
}

#login .bottomNFe{
    height: 80px;
    background: url("images/nfe/tela_login_rodape.png") no-repeat bottom;
}

#login .tabletext{
    padding-bottom:15px;
}
.sep{
    background-image:url(images/sep.gif);
    background-repeat:repeat-x;
    clear:both;
    height:1px;
    clear:both;}
.sepmenu{
    background-image:url(images/sep.gif);
    background-repeat:repeat-x;
    clear:both;
    height:1px;
    margin:6px 0;}

/*--- inside ---*/
.bgtop{
    background-color:#fff;
    /*background-image:url(images/bg_top.jpg);*/
    background-repeat:no-repeat;
    background-position:top right;
}

.bgmenu{
    background-image:url(images/bg_menu.gif);
    background-repeat:repeat-x;
}
.bgmenuleft{
    background-image:url(images/bg_menu_left.jpg);
    background-repeat:repeat-x;
    background-repeat:no-repeat;
    background-position:left;
}
.bgrodape{
    background-image:url(images/bg_rodape.gif);
    background-repeat:repeat-x;
}
.bgrodaperight{
    background-image:url(images/bg_rodape_right.jpg);
    background-repeat:no-repeat;
    background-position:bottom right;
}
.bgrodapeleft{
    background-image:url(images/bg_rodape_left.jpg);
    background-repeat:no-repeat;
    background-position:bottom left;
}
.bgrodapeleft2{
    background-image:url(images/bg_rodape_leftRelatorio.jpg);
    background-repeat:no-repeat;
    background-position:bottom left;
}
.logo{
    width:150px;
    height:78px;
    vertical-align: middle;
    margin-left: 15px;
    background-image:url(images/logo_zw.png);
    background-repeat:no-repeat;
    background-position:center center ;
}

.logoNFe {
    width: 180px;
    height: 78px;
    vertical-align: middle;
    margin-left: 15px;
    background-image: url(beta/imagens/pacto-nfe-topo.png);
    background-repeat: no-repeat;
    background-position: center center;
}

.logoSmartBox{
    width:320px;
    height:78px;
    vertical-align: middle;
    margin-left: 15px;
    background-image:url(images/logo_top_smartbox.png);
    background-repeat:no-repeat;
    background-position:center center ;
}

.logoNatal{
    width:190px;
    height:78px;
    vertical-align: middle;
    margin-left: 15px;
    background-image:url(images/logo_top_natal.png);
    background-repeat:no-repeat;
    background-position:center center ;
}

.logoAnoNovo{
    width:200px;
    height:78px;
    vertical-align: middle;
    margin-left: 15px;
    background-image:url(images/logo_top_ano_novo.png);
    background-repeat:no-repeat;
    background-position:center center ;
}

.logoFIN{
    width:142px;
    height:78px;
    vertical-align: middle;
    margin-left: 15px;
    background-image:url(images/logo_financeiro_web.png);
    background-repeat:no-repeat;
    background-position:center center ;
}

.logoEstudio{
    width:152px;
    height:78px;
    vertical-align: middle;
    margin-left: 15px;
    background-image:url(images/logo_estudio.png);
    background-repeat:no-repeat;
    background-position:center center ;
}

.logoCRM{
    width:148px;
    height:78px;
    vertical-align: middle;
    margin-left: 15px;
    background: url(images/logo_CRM.png) no-repeat center center;
}

/*Logos de Datas Comemorativas*/
.logoCopa{
    width:150px;
    height:78px;
    vertical-align: middle;
    margin-left: 15px;
    background: url(images/logo_zw_copa.png) no-repeat center center;
}


.logoCopaCRM{
    width:150px;
    height:78px;
    vertical-align: middle;
    margin-left: 15px;
    background: url(images/logo_CRM_copa.png) no-repeat center center;
}

.logoCopaFIN{
    width:150px;
    height:78px;
    vertical-align: middle;
    margin-left: 15px;
    background: url(images/logo_financeiro_web_copa.png) no-repeat center center;
}

.logoCopaEST{
    width:150px;
    height:78px;
    vertical-align: middle;
    margin-left: 15px;
    background: url(images/logo_estudio_copa.png) no-repeat center center;
}

.bglateral{
    /*background-image:url(images/bg_lateral.gif);*/
    /*background-repeat:repeat-y;*/
    /*background-position:left;*/
    /*padding-left: 0px; padding-right: 0px;*/
}

.bglateral_aumentada{
    background-image:url(images/bg_lateral_aumentada.gif);
    background-repeat:repeat-y;
    background-position:left;
}

.bglateraltop{
    /*background-image:url(images/bg_lateral_top.jpg);*/
    /*background-repeat:no-repeat;*/
    /*background-position:top left;*/
}

.bglateraltop_aumentada{
    background-image:url(images/bg_lateral_top_aumentado.jpg);
    background-repeat:no-repeat;
    background-position:top left;
}

.box2{
    width:230px;
}
.box2 .boxtop{
    height:5px;clear:both;margin:0;
}
.box2 .boxmiddle{
    background-color:#fff;clear:both;margin:0;text-align:left;padding:0 10px;
}
.box2 .boxbottom{
    height:10px;clear:both;margin:0 0 20px 0;
}
.box{
    width:172px;
}
.boxarmario{
    width:172px;
    border: 1px solid #ffffff;
    border-radius: 10px;
    margin-bottom: 20px;
    margin-top: 20px;
    padding-bottom: 20px;
    padding-top: 20px;
    background-color: #ffffff;
    padding-left: 10px;
}
.box .boxtop{
    height:5px;clear:both;margin:0;
}

.box .boxmiddle{
    background-color:#fff;clear:both;margin:0;text-align:left;padding:0 10px;
}
.box .boxbottom{
    height:10px;clear:both;margin:0 0 20px 0;
}

.text {
    font-family:Arial, Helvetica, sans-serif;
    font-size: 12px;
    text-decoration: none;
    line-height: normal;
    font-weight: normal;
    text-transform: none;
    color: #333;
    line-height:150%;
}
.text .gray{
    color: #999;
}
.textblack {
    font-family:Arial, Helvetica, sans-serif;
    font-size: 11px;
    text-decoration: none;
    line-height: normal;
    font-weight: normal;
    text-transform: none;
    color: #111;
    line-height:100%;
}
.text2 {
    font-family:Arial, Helvetica, sans-serif;
    font-size: 12px;
    text-decoration: none;
    line-height: normal;
    font-weight: normal;
    text-transform: none;
    color: #333;
    line-height:100%;
}


.textsmall {
    font-family:Arial, Helvetica, sans-serif;
    font-size:11px;
    text-decoration:none;
    color:#333;
    line-height:125%;
}

.textverysmall {
    font-family:Arial, Helvetica, sans-serif;
    font-size:9px;
    text-decoration:none;
    color:#333;
    line-height:125%;
}

.textrodape {
    font-family:Arial, Helvetica, sans-serif;
    font-size:11px;
    text-decoration:none;
    color:#8a8989;
    line-height:100%;
    font-weight:normal;
}
.textrodape .bluerodape {
    color:#6ca4bd;
    text-decoration:none;
}
.textrodape .bluerodape:hover {
    color:#6ca4bd;
    text-decoration:underline;
}

.hierarquia {
    font-family:Arial, Helvetica, sans-serif;
    font-size:10px;
    font-weight:normal;
    text-decoration:none;
    color:#999;
}
.hierarquialink {
    font-family:Arial, Helvetica, sans-serif;
    font-size:10px;
    font-weight:normal;
    text-decoration:none;
    color:#333;
}
.hierarquialink:hover {
    text-decoration:underline;
}
.titulo {
    font-family:Arial, Helvetica, sans-serif;
    font-size: 11px;
    text-decoration: none;
    line-height: normal;
    font-weight: bold;
    text-transform: none;
    color: #005bab;
}
.titulo:hover {
    text-decoration:none;
}
.titulo2 {
    font-family:Arial, Helvetica, sans-serif;
    font-size: 12px;
    text-decoration: none;
    line-height: normal;
    font-weight: bold;
    text-transform: none;
    color: #0f4c6b;
}
.titulo2:hover{text-decoration: underline;}
.titulo3 {
    font-family:Arial, Helvetica, sans-serif;
    font-size: 11px;
    text-decoration: none;
    line-height: normal;
    font-weight: normal;
    text-transform: none;
    color: #0f4c6b;
}

.tituloPre {
    font-family:Arial, Helvetica, sans-serif;
    font-size: 11px;
    text-decoration: none;
    line-height: normal;
    font-weight: normal;
    text-transform: none;
    color: #0f4c6b;
}

.titulo3_branco{
    font-family:Arial, Helvetica, sans-serif;
    font-size: 11px;
    text-decoration: none;
    line-height: normal;
    font-weight: normal;
    text-transform: none;
    color: #ffffff;
}

.titulo3:hover{text-decoration: none;}
.titulo4 {
    font-family:Arial, Helvetica, sans-serif;
    font-size: 11px;
    text-decoration: none;
    line-height: normal;
    font-weight: normal;
    text-transform: none;
    color: #297d92;
}
.titulo4a {
    font-family:Arial, Helvetica, sans-serif;
    font-size: 11px;
    text-decoration: none;
    font-weight:normal;
    line-height: normal;
    text-transform: none;
    color: #297d92;
}
.titulo4b {
    font-family:Arial, Helvetica, sans-serif;
    font-size: 11px;
    text-decoration: none;
    font-weight:bold;
    line-height: normal;
    text-transform: none;
    color: #FF0000;
}
.titulo5 {
    font-family:Arial, Helvetica, sans-serif;
    font-size: 11px;
    text-decoration: none;
    line-height: normal;
    font-weight: normal;
    text-transform: none;
    color: #FF0000;
}

.titulo6 {
    font-family:Arial, Helvetica, sans-serif;
    font-size: 12px;
    text-decoration: none;
    line-height: normal;
    font-weight: normal;
    text-transform: none;
    color: #4F4F4F;
}
.titulo29 {
    font-family:Arial, Helvetica, sans-serif;
    font-size: 12px;
    text-decoration: none;
    line-height: normal;
    font-weight: bold;
    text-transform: none;
    color: #005bab;
}

.titulo30 {
    font-family:Arial, Helvetica, sans-serif;
    font-size: 12px;
    text-decoration: none;
    line-height: normal;
    font-weight: bold;
    text-transform: none;
}



.titulo7 {
    font-family:Arial, Helvetica, sans-serif;
    font-size: 15px;
    text-decoration: none;
    line-height: normal;
    font-weight: bold;
    text-transform: none;
    color: #0f4c6b;
}

.tituloboxcentro{
    font-family:Arial, Helvetica, sans-serif;
    font-size:16px;
    font-weight:bold;
    text-decoration:none;
    color:#0f4c6b;
    background:url('./images/box_centro_top.gif');
}

.tituloboxcentrocnz{
    font-family:Arial, Helvetica, sans-serif;
    font-size:16px;
    font-weight:bold;
    text-decoration:none;
    color:#0f4c6b;
    background:url('./images/cnz_box_centro_top.gif');
}

.tituloboxcentro2{
    background:url('./images/box_centro_top.gif');
    text-align:right;
    padding:0 0 0 0;
}

.tablelistras td{
    background-color:#f6f6f6;
    border-top:solid 2px #fff;
    border-bottom:solid 2px #fff;
    padding:4px 3px;
}
.tablelistrasContrato td{
    background-color:#d4ecf6;
    border-top:solid 2px #fff;
    border-bottom:solid 2px #fff;
    padding:4px 3px;
}
.tablelistras .par{
    background-color:#e6e6e6;
}
.tablelistras .prata{
    background-image:url(images/btn_prata_middle.png);
}
.tablepadding {
    border:solid 1px #e6e6e6;
}
.tablepadding td{
    padding:4px 3px;
    border-top:solid 2px #fff;
    border-bottom:solid 2px #fff;
}
.tablepadding2 {
    font-family:Arial, Helvetica, sans-serif;
    font-size:12px;
    font-weight:normal;
    text-decoration:none;
    color:#000;
}
.tablepadding2 td{
    padding:4px 3px;
}
.tablepreview {
    font-family:Arial, Helvetica, sans-serif;
    font-size:12px;
    font-weight:normal;
    text-decoration:none;
    color:#000;
    /*border-left:solid 1px #e6e6e6;
    border-right:solid 1px #e6e6e6;*/
}
.tablepreview td {
    background-color:#d4ecf6;
    border-bottom:dotted 0px #fff;
    padding:7px 5px;
}
.tablepreviewtotal {
    font-family:Arial, Helvetica, sans-serif;
    font-size:12px;
    text-decoration:none;
    color:#fff;
    font-weight:bold;
    /*border:solid 1px #e6e6e6;*/
}
.tablepreviewtotal .verde{
    font-size:20px;
    color:#677e9b;
}
.tablepreviewtotal td {
    /*background-image:url(images/aba_top_middle.png);*/
    background-color:#c4d6de;
    padding:12px;
    border-bottom:dotted 1px #fff;
}
.spinner {
    font-family:Arial, Helvetica, sans-serif;
    font-size:12px;
    text-decoration:none;
    color:#fff;
    font-weight:bold;
}
.spinner td{
    background-color:#c4d6de;
    border-bottom:none;
    padding: 1px;
}
.sombrapreview{
    /*background-image:url(images/sombra_box_preview.jpg);
    background-repeat:repeat-y;
    background-position:left;*/
    background-color:#d4ecf6;}
.btnregistro{
    font-family:Arial, Helvetica, sans-serif;
    font-size:9px;
    padding:2px 1px;
    /*background-color:#c4d6de;
    color:#0f4c6b;*/
    font-weight:bold;
}
/*--- Forms ---*/
.form{
    font-family:Arial, Helvetica, sans-serif;
    font-size:11px;
    color:#515151;
    border:solid #8eb3c3 1px;
    padding-left:4px;
    padding-top:4px;
    height:23px;
    vertical-align: middle;
}
.form2{
    font-family:Arial, Helvetica, sans-serif;
    font-size:11px;
    color:#515151;
    border:solid #8eb3c3 1px;
    padding:2px 2px 2px 4px;
    height:23px;
    vertical-align: middle;
    margin: 0 10px 0 10px;
}
.form3{
    font-family:Arial, Helvetica, sans-serif;
    font-size:11px;
    color:#515151;
    border:solid #8eb3c3 1px;
    padding:2px 2px 2px 4px;
    vertical-align: middle;
}
.red{
    color:#da2128;
    font-weight:bold;
}
.orange{
    color:#ffa500;
    font-weight:bold;
}
.green{
    color:#009900;
    font-weight:bold;
}
.blue{
    color:#1668a2;
    font-weight:bold;
}
.blueGrande{
    color:#1668a2;
    font-weight:bold;
    font-size: 14px;
}

.gray{
    color:#999;
    font-weight:bold;
}

.white{
    color:#fff;
    font-weight:bold;
}
.verde{
    font-size:22px;
    color:#677e9b;
}
/* ----- FOTO ----- */

.fotoleft{
    float:left;
    text-align:left;
    margin:0px 0px 15px 0px;
}

.fotoright{
    float:right;
    text-align:right;
    margin:0px 0px 15px 0px;
}

.fotoinferior{
    margin:0px 15px 15px 0px;
    float:left;
}

.containerfotoleft{
    padding:0px 15px 0px 0px;
    float:left;
}

.containerfotoright{
    padding:0px 0px 0px 15px;
    float:right;
}

.containerfotoinferior{
    margin:15px 0px 0px 0px;
    text-align:justify;
}

.fotolegenda{
    font-family:Arial, Helvetica, sans-serif;
    font-size:10px;
    margin-top:5px;
    margin-bottom:15px;
    text-align:center;
}

.fotocreditos{
    font-family:Arial, Helvetica, sans-serif;
    font-size:9px;
    margin-bottom:3px;
    text-align:left;
}
.foto {
    padding:1px;
    background-color: #ffffff;
    border: 1px solid #cccccc;
}
.colunaDireita {
    vertical-align: top;
    text-align: right;
}

.colunaEsquerda {
    vertical-align: top;
    text-align: left;
}
.opacityit img{
    filter:progid:DXImageTransform.Microsoft.Alpha(opacity=80);
    -moz-opacity: 0.8;
}

.opacityit:hover img{
    filter:progid:DXImageTransform.Microsoft.Alpha(opacity=100);
    -moz-opacity: 1;
}
.imgalpha {
    filter:progid:DXImageTransform.Microsoft.Alpha(opacity=100);
    -moz-opacity: 1.0;
}
.imgalpha:hover {
    filter:progid:DXImageTransform.Microsoft.Alpha(opacity=80);
    -moz-opacity: 0.8;
}

.nomargin {margin:0;}
.floatleft {float:left;}
.floatright {float:right;}

/* ----- Formul�rio ----- */
/* TR FOrm */
.trform {
    padding:5px;
    font-family:Arial, Helvetica, sans-serif;
    font-weight:normal;
    font-size:11px;
    color:#585858;
    width:100%;
    overflow:auto;
    margin-bottom:2px;
}
.trform ul {
    margin:0px;
    padding:2px 0px;
    list-style:none;
    clear:both;
    float:left;
    width:100%;
    overflow:auto;
}
.trform li {
    float: left;
}
.tdleft{
    width:25%;
    text-align:right;
    padding-top:3px;
    padding-right:5px;
}
.tdright{
    width:70%;
    text-align:left;
}
/*--- Bot�es --->*/

/*--- Bot�o amarelo --->*/
ul.btnamarelo{
    list-style:none;
    margin:0;
    padding:0;
    overflow:auto;
    float:left;
    display: inline;
    font-family:Arial, Helvetica, sans-serif;
    font-size: 12px;
    text-decoration: none;
    line-height: normal;
    font-weight: normal;
    text-transform: none;
    color: #333;
    line-height:125%;
}
ul.btnamarelo li .btnleft{
    background-image:url(images/btn_amarelo_left.png);
    background-repeat:no-repeat;
    background-position:left;
    width:4px;
    margin:0;
    height:24px;
    float:left;}
ul.btnamarelo li .btnmiddle {
    background-image:url(images/btn_amarelo_middle.png);
    background-repeat:repeat-x;
    margin:0;
    display: inline;
    text-align:center;
    padding:5px 7px 0 7px;
    height:24px;
    float:left;}
ul.btnamarelo li .btnright{
    background-image:url(images/btn_amarelo_right.png);
    background-repeat:no-repeat;
    background-position:right;
    margin:0;
    width:4px;
    height:24px;
    float:left;}
ul.btnamarelo li {
    float:left;
    display:inline;
    clear:none;
    margin-right:5px;}
ul.btnamarelo li a {
    color:#000;
    text-decoration:none;
    padding-right:0}
ul.btnamarelo li a:hover {
    color:#c68b2f;
    text-decoration:none;}

/*--- Bot�o prata --->*/
ul.btnprata{
    list-style:none;
    margin:0;
    padding:0;
    overflow:auto;
    float:left;
    display: inline;
    font-family:Arial, Helvetica, sans-serif;
    font-size: 12px;
    text-decoration: none;
    line-height: normal;
    font-weight: normal;
    text-transform: none;
    color: #333;
    line-height:125%;
}
ul.btnprata li .btnleft{
    background-image:url(images/btn_prata_left.png);
    background-repeat:no-repeat;
    background-position:left;
    width:6px;
    margin:0;
    height:27px;
    float:left;}
ul.btnprata li .btnmiddle {
    background-image:url(images/btn_prata_middle.png);
    background-repeat:repeat-x;
    margin:0;
    display: inline;
    text-align:center;
    padding:6px 5px 0 5px;
    height:27px;
    float:left;}
ul.btnprata li .btnright{
    background-image:url(images/btn_prata_right.png);
    background-repeat:no-repeat;
    background-position:right;
    margin:0;
    width:4px;
    height:27px;
    float:left;}
ul.btnprata li {
    float:left;
    display:inline;
    clear:none;
    margin-right:5px;}
ul.btnprata li a {
    color:#000;
    text-decoration:none;
    padding-right:0}
ul.btnprata li a:hover {
    color:#999;
    text-decoration:none;}

/*--- Bot�o Azul --->*/
ul.btnazul{
    list-style:none;
    margin:0;
    padding:0;
    overflow:auto;
    float:left;
    display: inline;
    font-family:Arial, Helvetica, sans-serif;
    font-size: 12px;
    text-decoration: none;
    line-height: normal;
    font-weight: normal;
    text-transform: none;
    color: #0f4c6b;
    line-height:125%;
}
ul.btnazul li .btnleft{
    background-image:url(images/btn_azul_left.png);
    background-repeat:no-repeat;
    background-position:left;
    width:4px;
    margin:0;
    height:25px;
    float:left;}
ul.btnazul li .btnmiddle {
    background-image:url(images/btn_azul_middle.png);
    background-repeat:repeat-x;
    margin:0;
    display: inline;
    text-align:center;
    padding:5px 5px 0 5px;
    height:25px;
    float:left;}
ul.btnazul li .btnright{
    background-image:url(images/btn_azul_right.png);
    background-repeat:no-repeat;
    background-position:right;
    margin:0;
    width:4px;
    height:25px;
    float:left;}
ul.btnazul li {
    float:left;
    display:inline;
    clear:none;
    margin-right:5px;}
ul.btnazul li a {
    color:#0f4c6b;
    text-decoration:none;
    padding-right:0}
ul.btnazul li a:hover {
    color:#759aab;
    text-decoration:none;}

/*--- Bot�o Azul Escuro --->*/
ul.btnazulescuro{
    list-style:none;
    margin:0;
    padding:0;
    overflow:auto;
    float:left;
    display: inline;
    font-family:Arial, Helvetica, sans-serif;
    font-size: 12px;
    text-decoration: none;
    line-height: normal;
    font-weight: normal;
    text-transform: none;
    color: #fff;
    line-height:125%;
}
ul.btnazulescuro li .btnleft{
    background-image:url(images/btn_azulescuro_left.png);
    background-repeat:no-repeat;
    background-position:left;
    width:4px;
    margin:0;
    height:25px;
    float:left;}
ul.btnazulescuro li .btnmiddle {
    background-image:url(images/btn_azulescuro_middle.png);
    background-repeat:repeat-x;
    margin:0;
    display: inline;
    text-align:center;
    padding:5px 5px 0 5px;
    height:25px;
    float:left;}
ul.btnazulescuro li .btnright{
    background-image:url(images/btn_azulescuro_right.png);
    background-repeat:no-repeat;
    background-position:right;
    margin:0;
    width:4px;
    height:25px;
    float:left;}
ul.btnazulescuro li {
    float:left;
    display:inline;
    clear:none;
    margin-right:5px;}
ul.btnazulescuro li a {
    color:#fff;
    text-decoration:none;
    padding-right:0}
ul.btnazulescuro li a:hover {
    color:#fff;
    text-decoration:none;}

/*--- aba top --->*/
ul.abatop{
    list-style:none;
    margin:0;
    padding:0;
    overflow:hidden;
    max-height: 38px;
    float:left;
    display: inline;
    font-family:Arial, Helvetica, sans-serif;
    font-size: 11px;
    text-decoration: none;
    line-height: normal;
    font-weight: normal;
    text-transform: none;
    color: #fff;
}
ul.abatop li {
    float:left;
    display:inline;
    clear:none;
    padding:0 5px;}
ul.abatop li a {
    color:#fff;
    text-decoration:none;
    padding-right:0}
ul.abatop li a:hover {
    /*color:#aed5f0;*/
    text-decoration:none;}

.linkWiki {
    /*-moz-background-clip:border;
    -moz-background-inline-policy:continuous;
    -moz-background-origin:padding;*/
    /*background:transparent url(http://i79.photobucket.com/albums/j152/maujor/site/iconlink.gif) no-repeat scroll right center;*/
    /*background:transparent url(./imagens/wiki_link2.gif) no-repeat scroll right center;*/
    /*background:transparent url(imagens/wiki_link2.gif) no-repeat scroll right center;*/
    padding-bottom:4px;
    padding-right:2px;
    padding-top:3px;
    border:none;
}

.linkWikiMenu {
    /*-moz-background-clip:border;
    -moz-background-inline-policy:continuous;
    -moz-background-origin:padding;*/
    /*background:transparent url(http://i79.photobucket.com/albums/j152/maujor/site/iconlink.gif) no-repeat scroll right center;*/
    /*background:transparent url(./imagens/wiki_link2.gif) no-repeat scroll right center;*/
    /*background:transparent url(imagens/wiki_link2.gif) no-repeat scroll right center;*/
    border:none;
}

.marginForMenuWiki{
    margin-top:-10px;
    margin-left:9px;
}
div.menufixo img {
    bottom:0;
    position:fixed;
    right:0;
}
/*--- Central de Eventos ---*/
.logoCE{
    width:350px;
    height:77px;
    background-image:url(images/logo_ce.png);
    background-repeat:no-repeat;
    background-position:top left;
}

.ds {
    border-bottom: solid 1px #E7E7E7;
    border-right: solid 1px #E7E7E7;
    MARGIN: 3px 0px 4px 4px;
    margin: 3px 0 4px;
    margin-left: 4px;
    DISPLAY: inline-block;
    display: -moz-inline-box;
    BORDER-RIGHT: #e7e7e7 1px solid
}


.lsbb {
    background: #EEE;
    border: solid 1px;
    height: 30px;
    display: block;
    border-color: #CCC #999 #999 #CCC;
}


.lsb {
    background: url(/images/nav_logo37.png) bottom;
    border: none;
    color: black;
    cursor: pointer;
    height: 30px;
    margin: 0;
    outline: 0;
    font: 15px arial,sans-serif;
    vertical-align: top;
}

.lsb:active {
    BACKGROUND: #ccc
}

.lst{
    background: white;
    border: 1px solid #CCC;
    border-bottom-color: #999;
    border-right-color: #999;
    color: black;
    font: 14px arial,sans-serif bold;
    margin: 0;
    padding: 5px 8px 0 6px;
    padding-right: 38px;
    vertical-align: top;
}
.tituloBold {
    font-family:Arial, Helvetica, sans-serif;
    font-size: 12px;
    text-decoration: none;
    line-height: normal;
    text-transform: none;
    font-weight: bold;
    color: #000000;
}
.tituloBoldBranco {
    font-family:Arial, Helvetica, sans-serif;
    font-size: 12px;
    text-decoration: none;
    line-height: normal;
    text-transform: none;
    font-weight: bold;
    color: #FFFFFF;
}
.tituloBoldPequeno {
    font-family:Arial, Helvetica, sans-serif;
    font-size: 11px;
    text-decoration: none;
    line-height: normal;
    text-transform: none;
    font-weight: bold;
    color: #000000;
}

.tituloSimples {
    font-family:Arial, Helvetica, sans-serif;
    font-size: 11px;
    text-decoration: none;
    line-height: normal;
    text-transform: none;
    font-weight: bold;
    color: #000000;
    padding:4px;
}

.titulotexto {
    font-family:Arial, Helvetica, sans-serif;
    font-size: 11px;
    text-decoration: none;
    line-height: normal;
    font-weight: bold;
    text-transform: none;
    color: #003300;
    padding:2px;
    text-align:center;
}
.texto {
    font-family:Arial, Helvetica, sans-serif;
    font-size: 11px;
    text-decoration: none;
    line-height: normal;
    text-transform: none;
    color: #333333;
    padding:2px;
    text-align:center;
}
.previa {
    font-family:Arial, Helvetica, sans-serif;
    font-size: 10px;
    color: #012B01;
    text-align: right;
}
.previavermelha {
    font-family:Arial, Helvetica, sans-serif;
    font-size: 10px;
    color: #CC0000;
    text-align: right;
}
.tituloDemonstrativo {
    font-family:"Trebuchet MS", Verdana, Arial, Helvetica, sans-serif;
    font-size: 13px;
    text-decoration: none;
    line-height: normal;
    text-transform: none;
    color: #000000;
}
.linkTabela{
    font-size: 13px;
}
.rich-table {
    empty-cells: show;
    border-collapse: separate;
}
.rich-table-subheadercell {
    border-right: solid 1px silver;
    padding: 4px 4px 4px 4px;
    text-align: center;
    font-size: 11px;
    color: #474747;
    font-family: Arial,Verdana,sans-serif;
    white-space: nowrap;
}
.tituloRateio {
    font-family:Arial, Helvetica, sans-serif;
    font-size: 11px;
    text-decoration: none;
    line-height: normal;
    font-weight: bold;
    text-transform: none;
    color: #339933;
    padding:2px;
}

.divScrollable {
    left: 0;
    top: 0;
    bottom: 0;
    overflow: auto;
    border:none;
    background-color:transparent;
}

ul.abatop {
    height: 36px;
    overflow: hidden;
    margin-top: -2px;
}
ul.abatop li a.zwicon,
ul.abatop li a.crmicon,
ul.abatop li a.notasicon,
ul.abatop li a.canalicon,
ul.abatop li a.ceicon,
ul.abatop li a.finanicon,
ul.abatop li a.ceicond,
ul.abatop li a.finanicond,
ul.abatop li a.nfeicon,
ul.abatop li a.nfeicond {
    padding-left: 5px;
    padding-right: 5px;
    display: inline-block;
    height: 30px;
    padding-top: 6px;
}
ul.abatop li:not(:first-child){
    padding-top: 7px;
}
ul.abatop li:not(:first-child):not(:nth-child(2)){
    height: 30px;
}
ul.abatop li:not(:first-child):not(:nth-child(2)):hover{
    background-color: rgba(0,0,0,0.3) !important;
    height: 31px;
}
ul.abatop li:nth-child(2){
    padding-top: 0px;
    margin-top: 9px;
}
ul.abatop li a[class*="icon"]:hover,
body.zw ul.abatop li a.zwicon,
body.crm ul.abatop li a.crmicon,
body.crm ul.abatop li a.notasicon,
body.crm ul.abatop li a.canalicon,
body.finan ul.abatop li a.finanicon,
body.ce ul.abatop li a.ceicon {
    background-position: center center;
    background-repeat: no-repeat;
}

ul.abatop li a.zwicon:hover, body.zw ul.abatop li a.zwicon {
    background-color: #28486f;
    background-image: url("./beta/imagens/oldbutnew-topo-zw-hover.png");
}

ul.abatop li a.crmicon:hover, body.crm ul.abatop li a.crmicon {
    background-color: #e0b436;
    background-image: url("./beta/imagens/oldbutnew-topo-crm-hover.png");
}

ul.abatop li a.ceicon:hover, body.ce ul.abatop li a.ceicon {
    background-color: #ad894b;
    background-image: url("./beta/imagens/oldbutnew-topo-ce-hover.png");
}

ul.abatop li a.finanicon:hover, body.finan ul.abatop li a.finanicon {
    background-color: #b2cd52;
    background-image: url("./beta/imagens/oldbutnew-topo-finan-hover.png");
}

ul.abatop li a.nfeicon:hover, body.nfe ul.abatop li a.nfeicon {
    background-color: #2a7565;
    background-image: url("./beta/imagens/oldbutnew-topo-nfe-hover.png");
}

ul.abatop li a.notasicon:hover, body.nfe ul.abatop li a.notasicon {
    background-color: #2a7565;
    background-image: url("./beta/imagens/oldbutnew-topo-nfe-hover.png");
}

ul.abatop li a.canalicon:hover, body.canal ul.abatop li a.canalicon {
    background-color: #82878e;
    background-image: url("./beta/imagens/oldbutnew-topo-nfe-hover.png");
}

ul.abatop li a[class*="icon"]:hover img,
body.zw ul.abatop li a.zwicon img,
body.crm ul.abatop li a.crmicon img,
body.crm ul.abatop li a.notasicon img,
body.finan ul.abatop li a.finanicon img,
body.ce ul.abatop li a.ceicon img,
body.nfe ul.abatop li a.nfeicon img {
    opacity: 0;
}
ul.abatop li a[class*="icond"]:hover img {
    opacity: 1;
}
/** Overrides para css do faces para menu topo **/
.abatop .rich-menu-list-border {
    border: #89888c !important;
    background-color: transparent;
}
.abatop .rich-menu-list-bg {
    margin-top: 7px;
    background-image: none !important;
    border-color: #89888c !important;
    border-bottom: 1px solid;
    padding: 10px 15px;
    background-color: white;
}
.abatop .rich-ddmenu-label-select{
    border: transparent 0px !important;
    background-color: transparent !important;
    /*color: white;*/
}
.abatop .rich-menu-item-hover, .abatop .rich-menu-item-hover {
    border-color: transparent;
    background-color: #EEEEEE;
    background-image:  none;
}
.abatop span[class*="rich-menu-item"], .abatop span[class*="rich-menu-item"] a {
    border-color: transparent;
    font-size: 12px;
    font-weight: bold;
}
.abatop .rich-menu-separator, .abatop .rich-menu-item {
    margin: 4.5px 0px;
}
.abatop .rich-menu-separator{
    border-top-color: #555;
}
.rich-ddmenu-label, .rich-ddmenu-label-disabled {
    padding: 0px !important;
}
/* CRM Migrado */

/* OUTRAS MIGRAÇÕES */
.crm {
    font-size: 14px;
}
.crm form {
    margin: 0px;
}
.crm .tituloboxcentro2{
    background:url('./images/box_centro_top.gif');
    text-align:right;
    padding:0 0 0 0;
}

.crm .logo{
    width:148px;
    height:78px;
    vertical-align: middle;
    margin-left: 15px;
    background: url(images/logo_CRM.png) no-repeat center center;
}

.crm .metaNaoCalculada {
    font-family:Arial, Helvetica, sans-serif;
    font-size: 11px;
    text-decoration: none;
    line-height: normal;
    font-weight: normal;
    text-transform: none;
    color: #2067bb;
}
.crm .titulo29 {
    font-family:Arial, Helvetica, sans-serif;
    font-size: 12px;
    text-decoration: none;
    line-height: normal;
    font-weight: normal;
    text-transform: none;
    color: #0f4c6b;
}

.crm .textsmall {
    font-family:Arial, Helvetica, sans-serif;
    font-size:11px;
    text-decoration:none;
    color:#333;
    line-height:125%;
}

.crm .textverysmall {
    font-family:Arial, Helvetica, sans-serif;
    font-size:9px;
    text-decoration:none;
    color:#333;
    line-height:125%;
}

.crm .tituloboxcentro{
    font-family:Arial, Helvetica, sans-serif;
    font-size:16px;
    font-weight:bold;
    text-decoration:none;
    color:#FFFFFF;}

/* Novo Menu Superior CRM */
ul.btnazul .rich-label-text-decor {
    font-weight: 400 !important;
}
ul.btnazul .rich-ddmenu-label {
    height: 23px;
    line-height: 23px;
    vertical-align: middle;
    font-family: sans-serif;
    font-weight: 400 !important;
    text-transform: none !important;
    font-size: 11px;
    color: rgb(15, 76, 107);
    display: block;

    border: 1px solid #759aab;
    border-radius: 3px;
    padding: 0 8px !important;

    background-image: linear-gradient(bottom, rgb(218,226,230) 33%, rgb(254,254,254) 90%);
    background-image: -o-linear-gradient(bottom, rgb(218,226,230) 33%, rgb(254,254,254) 90%);
    background-image: -moz-linear-gradient(bottom, rgb(218,226,230) 33%, rgb(254,254,254) 90%);
    background-image: -webkit-linear-gradient(bottom, rgb(218,226,230) 33%, rgb(254,254,254) 90%);
    background-image: -ms-linear-gradient(bottom, rgb(218,226,230) 33%, rgb(254,254,254) 90%);

    background-image: -webkit-gradient(
        linear,
        left bottom,
        left top,
        color-stop(0.33, rgb(218,226,230)),
        color-stop(0.9, rgb(254,254,254))
    );

}
.btn-menu-crm .rich-ddmenu-label {
    height: 23px;
    line-height: 23px;
    vertical-align: middle;
    font-family: sans-serif;
    font-weight: 400 !important;
    text-transform: none !important;
    font-size: 11px;
    color: rgb(15, 76, 107);
    display: block;

    border: 1px solid #759aab;
    border-radius: 3px;
    padding: 0 8px !important;

    background-image: linear-gradient(bottom, rgb(218,226,230) 33%, rgb(254,254,254) 90%);
    background-image: -o-linear-gradient(bottom, rgb(218,226,230) 33%, rgb(254,254,254) 90%);
    background-image: -moz-linear-gradient(bottom, rgb(218,226,230) 33%, rgb(254,254,254) 90%);
    background-image: -webkit-linear-gradient(bottom, rgb(218,226,230) 33%, rgb(254,254,254) 90%);
    background-image: -ms-linear-gradient(bottom, rgb(218,226,230) 33%, rgb(254,254,254) 90%);

    background-image: -webkit-gradient(
            linear,
            left bottom,
            left top,
            color-stop(0.33, rgb(218,226,230)),
            color-stop(0.9, rgb(254,254,254))
    );

}
.btn-menu-crm .rich-label-text-decor:hover {
    color: rgb(117, 154, 171);
}
.btn-menu-crm  .rich-menu-list-border {
    border: #89888c !important;
    background-color: transparent;
    top: -7px !important;
}
ul.btnazul .rich-label-text-decor:hover {
    color: rgb(117, 154, 171);
}
ul.btnazul .rich-menu-list-border {
    border: #89888c !important;
    background-color: transparent;
    top: -7px !important;
}
.btn-menu-crm .rich-menu-list-bg {
    margin-top: 7px;
    background-image: none !important;
    border-color: #89888c !important;
    border-bottom: 1px solid;
    padding: 0px;
    background-color: white;
}
ul.btnazul .rich-menu-list-bg {
    margin-top: 7px;
    background-image: none !important;
    border-color: #89888c !important;
    border-bottom: 1px solid;
    padding: 0px;
    background-color: white;
}
.rich-ddmenu-label, .rich-ddmenu-label-disabled {
    padding: 0px !important;
}
ul.btnazul .rich-menu-item-hover, ul.btnazul .rich-menu-item-hover,.btn-menu-crm .rich-menu-item-hover{
    border-color: transparent;
    background-color: #EEEEEE;
    background-image:  none;
}
ul.btnazul span[class*="rich-menu-item"], ul.btnazul span[class*="rich-menu-item"] a {
    border-color: transparent;
    font-size: 12px;
    font-weight: bold;
}
ul.btnazul .rich-menu-separator, ul.btnazul .rich-menu-item {
    margin: 4.5px 0px;
}
ul.btnazul .rich-menu-separator{
    border-top-color: #555;
}
ul.btnazul .rich-menu-list-strut {
    display: none;
}
.btn-menu-crm .rich-menu-list-strut {
    display: none;
}
/* Modal Dicas e Novidades */
.modalDicNovos .rich-mpnl-mask-div {
    background-color: rgba(0,0,0,0.7);
}
.modalDicNovos .rich-mpnl-shadow {
    display: none;
}
.modalDicNovos .rich-mpnl-body,
.modalDicNovos .rich-mpnl-content {
    background: transparent;
    color: white;
    border: none;
}
.modalDicNovos p[class*=stepy-] {
    margin-top: 10px;
}
.modalDicNovos .step,
.modalDicNovos .step h4 {
    font-size: 18px !important;
}
.modalDicNovos h1 {
    font-size: 48px;
    margin-bottom: 0px;
    margin-top: 0px;
}
.modalDicNovos p.descr {
    margin-bottom: 10px;
}
.modalDicNovos .fecharModal {
    color: white;
    font-size: 32px !important;
    margin-right: -35px;
}
/* Modal Assistente Pacto */
.modalDicNovos.assistente .rich-mpnl-text.rich-mpnl-controls {
    z-index: 10 !important;
}
.modalDicNovos.assistente .fecharModal {
    margin-right: 5px;
    opacity: 0.5;
    font-size: 24px !important;
    position: relative;
}
.modalDicNovos.assistente .fecharModal:hover {
    opacity: 0.8;
}
.modalDicNovos.assistente .blocoFeed {
    position: relative;
    float: left;
    background-color: #00456C;
    top: 0px;
    border-radius: 10px;
    background-image: url("./feed_gestao/assistente/balao-menor.png"), url("./feed_gestao/assistente/balaocinza.png"),url("./feed_gestao/assistente/carinha-home.png");
    background-position: 520px 200px,520px 50px, 780px 230px;
    background-repeat: no-repeat;
    width: 940px;
    height: 460px;
    padding: 10px;
}
.modalDicNovos.assistente .blocoDica {
    position: absolute;
    float: left;
    top: 0px;
}

.modalDicNovos.assistente .blocoFeed.indicador {
    background: none;
    
}
.modalDicNovos.assistente .blocoDica {
    opacity: 0;
    z-index: 1;
    font-size: 12px;
    -webkit-transition: opacity 1s;
    -moz-transition: opacity 1s;
    -ms-transition: opacity 1s;
    -o-transition: opacity 1s;
    transition: opacity 1s;
    display: none;
}
.modalDicNovos.assistente .blocoDica.ativo {
    opacity: 1;
    z-index: 2;
    display: block;
}
.modalDicNovos.assistente .blocoFeed a[class*='btn-pr'] {
    opacity: 0.5;
    text-decoration: none !important;
}
.modalDicNovos.assistente .blocoFeed a[class*='btn-pr']:hover {
    opacity: 0.8;
}
.modalDicNovos.assistente .blocoDica .btn-prev {
    color: white;
    position: absolute;
    left: 203px;
    top: 430px;
    
}

.modalDicNovos.assistente .blocoDica .btn-prox {
    color: white;
    position: absolute;
    left: 270px;
    top: 430px;
}
/* Gest�o de Remessas */
.remessasOpcoesFix:first-child > td:not(:first-child) {
    padding-top: 28px;
}
.remessasRPPagina > tbody > tr > td:first-child {
    border: none;
}

/* CRM e Carteiras, mudar para arquivo separado talvez */
.crm .pure-button {
    font-size: 14px;
}
.crm .pure-form label {
    font-size: 1em;
}
.crm hr {
    border: 0px;
    border-top: 1px solid #888888;
}
.crm .blocoCarteiras {
    /*background-color: #c4d6de;*/
    /*border: 1px solid transparent;*/
    /*border-radius: 10px;*/
    /*padding: 0px;*/
    /*margin-bottom: 23px;*/
    /*font-size: 14px !important;*/
    margin: 20px;
    padding: 10px;
    height: auto;
    position: relative;
    -webkit-box-shadow: 0px 2px 10px 0px rgba(0,0,0,0.35);
    -moz-box-shadow: 0px 2px 10px 0px rgba(0,0,0,0.35);
    box-shadow: 0px 2px 10px 0px rgba(0,0,0,0.35);
    background-color: #ffffff;
    transition: transform .1s ease-out;
    -moz-transition: -moz-transform .1s ease-out;
    -o-transition: -o-transform .1s ease-out;
    -webkit-transition: -webkit-transform .1s ease-out;
}
.crm .blocoCarteiras h3 {
    color: #0f4c6b;
    font-size: 17px;
    margin: 10px 13px 0 13px;
}
.crm .blocoCarteiras.parabens {
    background-color: #c4d6b5;
}
.crm .blocoCarteiras.parabens h3 {
    color: #0f4c36;
}
.crm .blocoCarteiras .blocoCarteirasInterno {
    background-color: #fff;
    border: 1px solid transparent;
    border-radius: 3px;
    margin: 10px;
    padding: 5px;
    color: #888;
    position: relative;
}
.crm .blocoCarteiras .blocoCarteirasInterno:not(.listado) {
    background-color: #fff;
    padding: 15px;
}
.crm .blocoCarteiras .blocoCarteirasInterno fieldset {
    margin-top: 20px;
    color: black;
}
.crm .blocoCarteiras .blocoCarteirasInterno.listado {
    min-height: 35px;
    line-height: 35px;
    vertical-align: middle;
    color: #000;
}
.crm .blocoCarteiras .blocoCarteirasInterno.listado b {
    /*text-transform: capitalize;*/
}
.crm .blocoMenuLateral {
    padding: 10px;
    color: black;
    position: absolute;
    top: 4.35em;
    bottom: 15px;
}
.crm .blocoMenuLateral h4, .rich-mpnl-body h4 {
    margin: 0px;
    font-size: 14px;
}
.crm .blocoMenuLateral > .pure-button{
    color: white;
    text-align: left;
    width: 100%;
    padding: 0px;
    position: relative;
    height: 36px;
    margin-top: 10px;
}
.crm .blocoMenuLateral > .pure-button:not([class*="primary"]) {
    background-color: white;
    color: black;
}
.crm .blocoMenuLateral .pure-button span:nth-child(2) {
    position: absolute;
    top: 9px;
    left: 10px;
}
.crm .blocoMenuLateral .pure-button span:first-child,
.crm .blocoMenuLateral .pure-button span:last-child {
    position: absolute;
    top: 10px;
    right: 10px;
}
.crm .blocoMenuLateral .pure-button span:last-child {
    right: 30px;
    top: 9px;
    letter-spacing: normal;
}
.crm .blocoPrincipal {
    min-height: 500px;
    margin-left: 28.6%;
    width: 70%;
    width: calc(75% - 25px);
    margin-left: calc(25% + 25px);
}
.crm .blocoPrincipal .topoPrincipal {
    background-color: #e7e7e7;
    padding: 10px;
    width: calc(100% - 20px);
}
.crm .blocoPrincipal .topoPrincipal h4 {
    font-weight: normal;
    margin: 0px;
    color: black;
    font-size: 14px;
}
.crm .blocoPrincipal .topoPrincipal span {
    font-size: 12px;
}
.crm .rich-mpnl-ovf-hd {
    overflow: visible;
}
.crm .rich-mpnl-shadow {
    height: 100% !important;
}
.crm .rich-mpnl-content {
    height: auto !important;
}
.crm .calendario-carteiras .rich-calendar-input {
    width: 20%;
}
.crm .calendario-carteiras .rich-calendar-button {
    opacity: 0;
    width: 0px;
}
.crm i[id*="Trigger"] {
    cursor: pointer;
    margin-left: 6px;
}
.crm .dataTable > thead > tr {
    cursor: pointer;
}
/* Modais */
/* PanelBar (Sanfona) */
.crm .rich-panelbar-content-exterior {
    height: 30px !important;
}
.crm .headerSanfona {
    background-color: white;
    background-image: none;
    font-weight: normal;
}
.crm .headerSanfona:hover {
    background-color: #f0f0f0;
}
.crm .headerSelecionado {
    background-color: #eaeaea;
}
.crm .headerSanfona i {
    color: #a9a9a9;
}
.crm .rich-mpnl-content * {
    border-color: #888888;
}
.crm .rich-mpnl-content .rich-stglpanel {
    border-width: 0px;
}
.crm .rich-mpnl-content .rich-stglpanel-marker {
    float: none;
    display: inline-block;
}
.crm .rich-stglpanel-header span:last-child {
    display: inline-block;
    float: right;
    margin-top: -14px;
}
@media screen and (-webkit-min-device-pixel-ratio:0) {
    .crm .rich-stglpanel-header span:last-child {
        margin-top: 0px;
    }
}
.crm .rich-mpnl-content .rich-stglpanel-body {
    padding: 0px;
}
.crm .rich-mpnl-content .rich-stglpanel-body table,
.crm .rich-mpnl-content .rich-stglpanel-body table tr td{
    border: none;
}
.crm .rich-mpnl-content .rich-stglpanel-body table tr {
    border-bottom: 1px solid #888;
    cursor: pointer;
}
.crm .rich-mpnl-content .rich-stglpanel-body table tr:nth-child(odd) {
    background-color: #fff;
}
.crm .rich-mpnl-content .rich-stglpanel-body table tr:nth-child(even) {
    background-color: #e7e7e7;
}
.crm .rich-mpnl-content .rich-stglpanel-body table tr:hover {
    background-color: #dfdfdf;
}
.crm .rich-mpnl-content .rich-stglpanel-body table tr:last-child {
    border-bottom: none;
}
.crm .rich-mpnl-content .rich-stglpanel-body table tr.selecionado {
    background-color: #0079eb;
}
.crm .modalCarteiras .rich-mpnl-content .rich-table-cell {
    padding: 0px;
}
.crm .rich-mpnl-content .rich-stglpanel-body table tr td {
    padding: 0px !important;
}
.crm .rich-mpnl-content .rich-stglpanel-body table tr td:last-child {
    text-align: right;
}
.crm .rich-mpnl-content .rich-stglpanel-body table tr td a {
    display: block;
    color: black;
    text-decoration: none;
    padding: 10px;
}
.crm .linhaSelec, .crm .linhaSelec a {
    background-color: #0079eb !important;
    color: white !important;
}

    .comparativo{
        font-family: Arial, Helvetica, sans-serif;
        font-size: 11px;
        text-decoration: none;
        line-height: normal;
        text-transform: none;
        color: #104E8B;
    }

    .notificacaoAtividades{
        -webkit-background-clip: padding-box;
        -webkit-box-shadow: rgba(0, 0, 0, 0.701961) 0px 1px 1px 0px;
        -webkit-font-smoothing: subpixel-antialiased;
        background-clip: padding-box;
        background-color: rgb(220, 13, 23) !important;
        background-image: -webkit-linear-gradient(top, rgb(250, 60, 69), rgb(220, 13, 23));
        border-bottom-left-radius: 2px;
        border-bottom-right-radius: 2px;
        border-top-left-radius: 2px;
        border-top-right-radius: 2px;
        box-shadow: rgba(0, 0, 0, 0.701961) 0px 1px 1px 0px;
        color: rgb(255, 255, 255) !important;
        cursor: pointer;
        
        
        font-family: 'Helvetica Neue', Helvetica, sans-serif;
        font-size: 10px !important;
        height: 13px !important;
        line-height: normal;
        list-style-type: none;
        padding: 1px 3px !important;
        text-align: center;
        text-shadow: rgba(0, 0, 0, 0.4) 0px -1px 0px;
        zoom: 1;
        
        
        
        
    }

.menu_general_zyllion {
    height: 20px;
    color: #ffffff;
    line-height: 20px;
    padding-top: 10px;
    padding-bottom: 10px;
    width: 126px;
    border-bottom: 1px dotted #E6E6E6;}

.menu_general_zyllion_2linhas {
    height: 35px;
    color: #ffffff;
    line-height: 16px;
    padding-top: 10px;
    padding-bottom: 10px;
    width: 126px;
    border-bottom: 1px dotted #E6E6E6;}

    .menu_general_engloba_zyllion {
    padding-bottom: 20px;
    padding-top: 10px;
    padding-left: 5px;
    padding-right: 10px;}

.menu_general_engloba_zyllion_azul {
    padding-bottom: 20px;
    padding-top: 10px;
    padding-left: 37px;
    padding-right: 10px;

}

    .menu_general_engloba_zyllion_vermelho {
    padding-bottom: 20px;
    padding-top: 10px;
    padding-left: 37px;
    padding-right: 10px;}

.select_empresa select {
    width: 150px;
    padding:3px;
    -webkit-border-radius:4px;
    -moz-border-radius:4px;
    border-radius:4px;
    -webkit-box-shadow: 0 3px 0 #ccc, 0 -1px #77B5B2 inset;
    -moz-box-shadow: 0 3px 0 #ccc, 0 -1px #77B5B2 inset;
    box-shadow: 0 3px 0 #ccc, 0 -1px #77B5B2 inset;
    background: #C4D6DE;
    color: #002F53;
    border:none;
    outline:none;
    display: inline-block;
    -webkit-appearance:none;
    -moz-appearance:none;
    appearance:none;
    cursor:pointer;
    margin: 0 0 10px;
}

/* Targetting Webkit browsers only. FF will show the dropdown arrow with so much padding. */
@media screen and (-webkit-min-device-pixel-ratio:0) {
    .select_empresa select {padding-right:18px}
}

.select_empresa label {position:relative}
.select_empresa label:after {
    content:'<>';
    font:11px "Consolas", monospace;
    color:#002F53;
    -webkit-transform:rotate(90deg);
    -moz-transform:rotate(90deg);
    -ms-transform:rotate(90deg);
    transform:rotate(90deg);
    right:8px; top:2px;
    padding:0 0 2px;
    border-bottom:1px solid #ffffff;
    position:absolute;
    pointer-events:none;
}
.select_empresa label:before {
    content:'';
    right:6px; top:0px;
    width:20px; height:20px;
    background:#C4D6DE;
    position:absolute;
    pointer-events:none;
    display:block;
}
.titulos_menus_geral {
    padding: 10px 0px;
}

.crm .blocoNewCRM {
    background-color: #c4d6de;
    border: 1px solid transparent;
    border-radius: 10px;
    padding: 0px;
    font-size: 14px !important;
}

.crm .blocoNewCRM .blocoNewCRMInterno {
    background-color: #fff;
    border: 1px solid transparent;
    border-radius: 3px;
    margin: 10px;
    padding: 10px;
    color: #888;
    position: relative;
    height: 94%;
}

.crm .bgmenuSemLateral{
    background-image:url(imagensCRM/topo_menu_lateral.png), url(imagensCRM/topo_menu_textura.png);
    background-repeat:no-repeat, no-repeat;
    background-position: top left, right top;
    background-size: auto, 95% 50px;
}

.crm .bgmenuleftSemLateral{
    background-image:url(imagensCRM/bg_menu_left.png);
    background-repeat:repeat-x;
    background-repeat:no-repeat;
    background-position:left;
}
.crm .bgrodapeSemLateral{
    background-image:url(imagensCRM/bg_rodape.png);
    background-repeat:repeat-x;
}
.crm .bgrodaperightSemLateral{
    background-image:url(imagensCRM/bg_rodape_right.png);
    background-repeat:no-repeat;
    background-position:bottom right;
}
.crm .bgrodapeleftSemLateral{
    background-image: none;
    background-repeat:no-repeat;
    background-position:bottom left;
}
.crm .bgrodapeleft2SemLateral{
    background-image:url(images/CRM/bg_rodape_leftRelatorio.jpg);
    background-repeat:no-repeat;
    background-position:bottom left;
}

.crm .bglateralSemLateral{
    background-image:url(imagensCRM/bg_lateral.gif);
    background-repeat:repeat-y;
    background-position:left;
}

.crm .bglateraltopSemLateral{
    background-image:url(imagensCRM/bg_lateral_top.png);
    background-repeat:no-repeat;
    background-position:bottom;
}

.grupoMenuItemContainer {
    display: flex;
    align-items: center;
}

.grupoMenuItemContainer:hover .menuItemLinkFixedHidden{
    visibility: visible;
}

.grupoMenuItemContainer:hover .menuItemLinkWikiHidden{
    visibility: visible;
    margin-left: 0px !important;
}

.grupoMenuItemNomeContainer {
    flex-grow: 1;
    display: flex;
    justify-content: flex-start;
    align-items: center;
}

.grupoMenuItemLinksContainer {
    flex-flow: 0;
}

.icone-grupo-menu {
    margin-right: 10px;
}

.linkFuncionalidadeAcessoRapido {
    margin-left: 2px;
    margin-right: 2px;
}

.menuItemLinkFixedHidden {
    visibility: hidden;
    margin-right: 10px;
}

.menuItemLinkWikiHidden {
    visibility: hidden;
}

.icon-color-grey {
    color: #9e9e9e;
}

.bi-bg-box-verde {
    background-color: #ADDAB8;
}

<%@page contentType="text/html"%>
<%@page pageEncoding="ISO-8859-1"%>
<head><%@include file="/includes/include_import_minifiles.jsp" %></head>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<link href="./beta/css/font-awesome_2.0.min.css" rel="stylesheet" type="text/css">
<script type="text/javascript" language="javascript" src="hoverform.js"></script>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>
<%@taglib prefix="jsfChart" uri="http://sourceforge.net/projects/jsf-comp" %>
<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<f:view>
    <%@include file="includes/include_carregando_ripple.jsp" %>
    <title>
        <h:outputText value="#{RelContratosRecorrenciaControle.tituloDetalhamento}"/>
    </title>
    <html>        
        <h:form id="form">
            <c:set var="titulo" scope="session" value="${RelContratosRecorrenciaControle.tituloDetalhamento} - ${fn:length(RelContratosRecorrenciaControle.listaApresentarRecorrenciaCliente)} registros"/>
            <h:panelGroup layout="block" styleClass="pure-g-r">
                <f:facet name="header">
                    <jsp:include page="topo_reduzido_popUp.jsp"/>
                </f:facet>
            </h:panelGroup>
            <a4j:commandButton id="botaoAtualizarPagina" reRender="form:panelGroup, form:item" style="display:none"/>
            <h:panelGroup layout="block" style="display: inline-table;width: 100%;height: 100vh" styleClass="fundoBranco">

                <h:panelGroup layout="block" styleClass="caixaCorpo">
                    <h:panelGroup layout="block" style="height: 80%;width: 100%">
                        <h:panelGroup layout="block" styleClass="caixaMenuLatel">
                            <h:panelGroup layout="block" >
                                <h:panelGroup layout="block" styleClass="margin-box">
                                    <table width="100%" align="center" height="100%" border="0" cellpadding="0" cellspacing="0">
                                        <tr>
                                            <td align="left" valign="top" width="100%"  style="padding:7px 20px 0 20px;">
                                                <h:panelGroup id="panelGroup">
                                                    <table width="100%" height="100%" border="0" align="left" cellpadding="0" cellspacing="0" class="text" style="margin-bottom:20px;">
                                                        <tr>
                                                            <td align="left" valign="top" bgcolor="#ffffff" style="padding:5px 15px 5px 15px;">
                                                                <h:panelGrid id="panelCliente" width="100%">
                                                                    <h:outputText styleClass="mensagem"  value="#{RelContratosRecorrenciaControle.mensagem}"/>
                                                                    <h:outputText styleClass="mensagemDetalhada" value="#{RelContratosRecorrenciaControle.mensagemDetalhada}"/>

                                                                    <h:panelGroup>
                                                                        <h:selectBooleanCheckbox id="mostrarPaginacao" value="#{RelContratosRecorrenciaControle.mostrarPaginacao}">
                                                                            <a4j:support event="onclick" action="#{DataScrollerControle.resetDatascroller}" reRender="panelCliente,item,panelGroup"/>
                                                                        </h:selectBooleanCheckbox>
                                                                        <rich:spacer width="10px"/>
                                                                        <h:outputText value="Mostrar Paginação" styleClass="texto-font texto-size-14-real texto-cor-cinza" style="font-weight: bold;"/>
                                                                    </h:panelGroup>
                                                                    <h:panelGrid width="100%" style="text-align: right">
                                                                        <h:panelGroup layout="block" >
                                                                            <a4j:commandLink id="exportarExcel"
                                                                                               style="margin-left: 8px;"
                                                                                               actionListener="#{ExportadorListaControle.exportar}"
                                                                                               rendered="#{not empty RelContratosRecorrenciaControle.listaApresentarRecorrenciaCliente}"
                                                                                             oncomplete="#{ExportadorListaControle.mensagemNotificar}#{ExportadorListaControle.operacaoOnComplete}#{ExportadorListaControle.msgAlert}"
                                                                                               accesskey="2" styleClass="linkPadrao">
                                                                                <f:attribute name="lista" value="#{RelContratosRecorrenciaControle.listaApresentarRecorrenciaCliente}"/>
                                                                                <f:attribute name="tipo" value="xls"/>
                                                                                <f:attribute name="itemExportacao" value="#{RelContratosRecorrenciaControle.getItemExportacaoSelecionado()}"/>
                                                                                <f:attribute name="atributos" value="matricula=Matricula,nomeCliente=Nome,situacaoCliente=Status,codigoParcela=Parcela,descricaoParcela=Descrição,valor=Valor,descricaoConvenio=Convênio de Cobrança,descricaoFormaPagamento=Forma de Pagamento,empresa=Empresa"/>
                                                                                <f:attribute name="prefixo" value="ContratosRegimeRecorrenciaClientesAdimplentes"/>
                                                                                <h:outputText title="Exportar para o formato Excel" styleClass="btn-print-2 excel"/>
                                                                            </a4j:commandLink>
                                                                            <%--BOTÃO PDF--%>
                                                                            <a4j:commandLink id="exportarPdf"
                                                                                               style="margin-left: 8px;"
                                                                                               actionListener="#{ExportadorListaControle.exportar}"
                                                                                               rendered="#{not empty RelContratosRecorrenciaControle.listaApresentarRecorrenciaCliente}"
                                                                                               oncomplete="#{ExportadorListaControle.mensagemNotificar}#{ExportadorListaControle.operacaoOnComplete}#{ExportadorListaControle.msgAlert}"
                                                                                               accesskey="2" styleClass="linkPadrao">
                                                                                <f:attribute name="lista" value="#{RelContratosRecorrenciaControle.listaApresentarRecorrenciaCliente}"/>
                                                                                <f:attribute name="tipo" value="pdf"/>
                                                                                <f:attribute name="itemExportacao" value="#{RelContratosRecorrenciaControle.getItemExportacaoSelecionado()}"/>
                                                                                <f:attribute name="atributos" value="matricula=Matricula,nomeCliente=Nome,situacaoCliente=Status,codigoParcela=Parcela,descricaoParcela=Descrição,valor=Valor,descricaoConvenio=Convênio de Cobrança,descricaoFormaPagamento=Forma de Pagamento,empresa=Empresa"/>
                                                                                <f:attribute name="prefixo" value="ContratosRegimeRecorrenciaClientesAdimplentes"/>
                                                                                <h:outputText title="Exportar para o formato PDF" styleClass="btn-print-2 pdf"/>
                                                                            </a4j:commandLink>
                                                                        </h:panelGroup>
                                                                    </h:panelGrid>
                                                                    <rich:dataTable id="item" width="100%" styleClass="tabelaSimplesCustom"
                                                                                    value="#{RelContratosRecorrenciaControle.listaApresentarRecorrenciaCliente}"
                                                                                    rows="#{RelContratosRecorrenciaControle.numeroPaginacao}"
                                                                                    var="item" rowKeyVar="status">

                                                                        <%@include file="/pages/ce/includes/include_contador_richtable.jsp" %>

                                                                        <rich:column sortBy="#{item.matricula}" styleClass="col-text-align-left" headerClass="col-text-align-left"
                                                                                     filterEvent="onkeyup">
                                                                            <f:facet name="header">
                                                                                <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza texto-bold" value="Matricula"/>
                                                                            </f:facet>
                                                                            <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza"  value="#{item.matricula}"/>
                                                                        </rich:column>

                                                                        <rich:column sortBy="#{item.nomeCliente}"  styleClass="col-text-align-left" headerClass="col-text-align-left"
                                                                                     filterEvent="onkeyup">
                                                                            <f:facet name="header">
                                                                                <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza texto-bold" value="Nome"/>
                                                                            </f:facet>
                                                                            <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza"  value="#{item.nomeCliente}"/>
                                                                        </rich:column>

                                                                        <rich:column sortBy="#{item.situacaoCliente}"  styleClass="col-text-align-center" headerClass="col-text-align-left"
                                                                                     filterEvent="onkeyup">
                                                                            <f:facet name="header">
                                                                                <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza texto-bold" value="Status"/>
                                                                            </f:facet>
                                                                            <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza" style="text-align: center" value="#{item.situacaoCliente}"/>
                                                                        </rich:column>

                                                                        <rich:column sortBy="#{item.codigoParcela}"  styleClass="col-text-align-left" headerClass="col-text-align-left"
                                                                                     filterEvent="onkeyup">
                                                                            <f:facet name="header">
                                                                                <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza texto-bold" value="Parcela"/>
                                                                            </f:facet>
                                                                            <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza"  value="#{item.codigoParcela}"/>
                                                                        </rich:column>

                                                                        <rich:column sortBy="#{item.descricaoParcela}"  styleClass="col-text-align-left" headerClass="col-text-align-left"
                                                                                     filterEvent="onkeyup">
                                                                            <f:facet name="header">
                                                                                <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza texto-bold" value="Descrição"/>
                                                                            </f:facet>
                                                                            <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza"  value="#{item.descricaoParcela}"/>
                                                                        </rich:column>

                                                                        <rich:column sortBy="#{item.valor}"  styleClass="col-text-align-left" headerClass="col-text-align-left"
                                                                                     filterEvent="onkeyup">
                                                                            <f:facet name="header">
                                                                                <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza texto-bold" value="Valor"/>
                                                                            </f:facet>
                                                                            <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza"  value="#{item.valor_Apresentar}"/>
                                                                        </rich:column>

                                                                        <rich:column sortBy="#{item.codigoParcela}"  styleClass="col-text-align-left" headerClass="col-text-align-left"
                                                                                     filterEvent="onkeyup">
                                                                            <f:facet name="header">
                                                                                <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza texto-bold" value="Convênio de Cobrança"/>
                                                                            </f:facet>
                                                                            <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza"  value="#{item.descricaoConvenio}"/>
                                                                        </rich:column>

                                                                        <rich:column sortBy="#{item.descricaoFormaPagamento}"  styleClass="col-text-align-left" headerClass="col-text-align-left"
                                                                                     filterEvent="onkeyup">
                                                                            <f:facet name="header">
                                                                                <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza texto-bold" value="Forma de Pagamento"/>
                                                                            </f:facet>
                                                                            <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza"  value="#{item.descricaoFormaPagamento}"/>
                                                                        </rich:column>

                                                                        <rich:column sortBy="#{item.empresa}"  styleClass="col-text-align-left" headerClass="col-text-align-left"
                                                                                     filterEvent="onkeyup">
                                                                            <f:facet name="header">
                                                                                <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza texto-bold" value="Empresa"/>
                                                                            </f:facet>
                                                                            <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza"  value="#{item.empresa}"/>
                                                                        </rich:column>

                                                                        <rich:column styleClass="col-text-align-center" width="5%">
                                                                            <a4j:commandLink id="visualizarAluno"
                                                                                               styleClass="linkPadrao texto-size-16-real"
                                                                                               action="#{RelContratosRecorrenciaControle.irParaTelaCliente}"
                                                                                               oncomplete="abrirPopup('clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);">
                                                                                <f:param name="state" value="AC"/>
                                                                                <i class="fa-icon-search"></i>
                                                                            </a4j:commandLink>
                                                                        </rich:column>
                                                                    </rich:dataTable>

                                                                    <rich:datascroller styleClass="scrollPureCustom" binding="#{DataScrollerControle.dataScroller}"
                                                                                       renderIfSinglePage="false"
                                                                                       rendered="#{RelContratosRecorrenciaControle.mostrarPaginacao}"
                                                                                       align="center" for="form:item" maxPages="20" id="scitems" />

                                                                </h:panelGrid>

                                                            </td>
                                                        </tr>
                                                    </table>
                                                </h:panelGroup>
                                            </td>
                                        </tr>
                                    </table>
                                </h:panelGroup>
                            </h:panelGroup>
                        </h:panelGroup>

                    </h:panelGroup>
                </h:panelGroup>
            </h:panelGroup>
        </h:form>
    </body>
</html>
</f:view>

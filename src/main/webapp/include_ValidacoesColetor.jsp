<%--
    Author             : <PERSON>lisses
    Data               : 19/01/2011
    Objetivo da Tela   : Cadastrar as Valida��es de acesso do Coletor
    Onde a tela � usada: include_CadColetor.jsp
--%>

<%@include file="includes/imports.jsp" %>

    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">

                <h:panelGrid  id="panelListaValidacoes" columns="1" style="height:25px; background-image:url('./imagens/fundoBarraTopo.png');background-repeat: repeat-x;" columnClasses="colunaCentralizada" width="100%">
                    <f:facet name="header">
                        <h:outputText styleClass="tituloFormulario" value="#{msg_aplic.prt_ColetorVal_titulo}"/>
                    </f:facet>
                     <h:outputText value="#{msg_aplic.prt_ColetorVal_Info}"/>

                    <h:panelGrid columns="2" width="100%" columnClasses="colunaEsquerda" styleClass="tabMensagens">
                        <a4j:commandButton id="btnAdicValidacao"
                                           action="#{LocalAcessoControle.adicionarValidacao}"
                                           title="#{msg_bt.btn_adicionarValidacao}"
                                           value="Novo"
                                           ajaxSingle="true"
                                           process="panelEdicaoValidacao"
                                           reRender="panelEdicaoValidacao, dataTableValidacao, panelListaValidacoes"
                                           styleClass="botoes nvoBt btSec"/>

                         <h:panelGroup  id="validacaoSelecionada" layout="block">
                            <h:panelGroup rendered="#{LocalAcessoControle.mostrarEsconderFormValidacao}">
                               <h:outputText value="Valida��o Selecionada: "/>
                               <h:outputText value="#{LocalAcessoControle.validacao.codigo}"/>
                            </h:panelGroup>
                        </h:panelGroup>

                    </h:panelGrid>

                        <h:dataTable id="dataTableValidacao" width="100%" headerClass="subordinado"
                                     rowClasses="linhaImpar, linhaPar" columnClasses="colunaAlinhamento"
                                     value="#{LocalAcessoControle.coletor.permissoes}"
                                     var="validacao">
                            <h:column>
                                <f:facet name="header">
                                    <h:outputText  value="#{msg_aplic.prt_ModeloMensagem_codigo}" />
                                </f:facet>
                                <h:outputText  value="#{validacao.codigo}" />
                            </h:column>

                            <h:column>
                                <f:facet name="header">
                                    <h:outputText  value="#{msg_aplic.prt_LocalAcesso_empresa}" />
                                </f:facet>
                                <h:outputText  value="#{validacao.empresa.nome}" />
                            </h:column>
                            <h:column>
                                <f:facet name="header">
                                    <h:outputText  value="#{msg_aplic.prt_ColetorVal_TipoVal}" />
                                </f:facet>
                                <h:outputText  value="#{validacao.tipoValidacao_Apresentar}" />
                            </h:column>
                            <h:column>
                                <f:facet name="header">
                                    <h:outputText  value="#{msg_aplic.prt_ColetorVal_TipoHor}" />
                                </f:facet>
                                <h:outputText  value="#{validacao.tipoHorario.descricao}" />
                            </h:column>

                            <h:column>
                                <f:facet name="header">
                                    <h:outputText  value="#{msg_bt.btn_opcoes}" />
                                </f:facet>
                                <h:panelGroup>
                                    <a4j:commandButton id="editarItemValidacao"
                                                     action="#{LocalAcessoControle.editarValidacao}"
                                                     value="#{msg_bt.btn_editar}"
                                                     title="#{msg_bt.btn_editar}"
                                                     image="./imagens/botaoEditar.png"
                                                     process="panelEdicaoValidacao"
                                                     reRender="panelEdicaoValidacao, validacaoSelecionada, panelListaValidacoes"
                                                     accesskey="6" styleClass="botoes"/>
                                    <f:verbatim>
                                        <h:outputText value="    "/>
                                    </f:verbatim>
                                    <a4j:commandButton id="removerItemValidacao"
                                                       action="#{LocalAcessoControle.removerValidacao}"
                                                       value="#{msg_bt.btn_excluir}"
                                                       title="#{msg_bt.btn_excluir}"
                                                       image="./imagens/botaoRemover.png"
                                                       styleClass="botoes"
                                                       ajaxSingle="true"
                                                       process="panelEdicaoValidacao"
                                                       reRender="panelEdicaoValidacao, dataTableValidacao, validacaoSelecionada, panelListaValidacoes"
                                                       />

                                </h:panelGroup>
                            </h:column>
                        </h:dataTable>
                    </h:panelGrid>

                <h:panelGroup id="panelEdicaoValidacao" layout="block">
                <h:panelGrid  id="EdicaoValidacao" rendered="#{LocalAcessoControle.mostrarEsconderFormValidacao}" columns="1" width="100%" headerClass="subordinado" columnClasses="colunaCentralizada">
                    <h:panelGrid  columns="2" rowClasses="linhaImpar, linhaPar" columnClasses="classEsquerda, classDireita" width="100%">
                        <h:outputText    value="#{msg_aplic.prt_LocalAcesso_empresa}" styleClass="tituloCampos"/>
                        <h:panelGroup>
                            <h:selectOneMenu id="empresaValidacao"  onblur="blurinput(this);"
                                             onfocus="focusinput(this);"
                                             style="color:black;"
                                             styleClass="form"
                                             value="#{LocalAcessoControle.validacao.empresa.codigo}" >
                                 <a4j:support event="onchange"
                                              focus="selectTipoValidacaoEscolhida"
                                              action="#{LocalAcessoControle.montarListaModalidadeOuProduto}"
                                              reRender="panelEdicaoValidacao"/>
                                <f:selectItems  value="#{LocalAcessoControle.listaSelectItemEmpresa}" />
                            </h:selectOneMenu>
                            <a4j:commandButton rendered="#{LocalAcessoControle.usuarioLogado.administrador}" id="atualizar_empresaVali" action="#{LocalAcessoControle.montarListaSelectItemEmpresa}" image="imagens/atualizar.png" immediate="true" ajaxSingle="true" reRender="form:empresa"/>
                        </h:panelGroup>
                        <h:outputText    value="#{msg_aplic.prt_ColetorVal_TipoVal}" styleClass="tituloCampos"/>
                        <h:selectOneMenu  id="tipoValidacao" styleClass="camposObrigatorios" value="#{LocalAcessoControle.codigoTipoValidacao}" >
                            <a4j:support event="onchange"
                                         focus="selectTipoValidacaoEscolhida"
                                         action="#{LocalAcessoControle.montarListaModalidadeOuProduto}"
                                         reRender="panelEdicaoValidacao"/>
                            <f:selectItems  value="#{LocalAcessoControle.listaSelectItemTipoValidacao}" />
                        </h:selectOneMenu>

                        <h:outputText  id="DescTipoValidacao"
                                       rendered="#{((LocalAcessoControle.codigoTipoValidacao == 2) || (LocalAcessoControle.codigoTipoValidacao == 1) || (LocalAcessoControle.codigoTipoValidacao == 4))}"
                                       value="#{LocalAcessoControle.nomeTipoValidacaoEscolhida}"
                                       styleClass="tituloCampos"/>

                        <h:panelGroup rendered="#{((LocalAcessoControle.codigoTipoValidacao == 2) || (LocalAcessoControle.codigoTipoValidacao == 1) || (LocalAcessoControle.codigoTipoValidacao == 4))}" >

                            <h:selectOneMenu id="selectTipoValidacaoEscolhida2" rendered="#{(LocalAcessoControle.codigoTipoValidacao == 4)}"  styleClass="camposObrigatorios" value="#{LocalAcessoControle.validacao.chave}" >
                                <a4j:support event="onchange"
                                             action="#{LocalAcessoControle.montarDescricaoRegra}"
                                             reRender="panelEdicaoValidacao"/>
                                <f:selectItems  value="#{LocalAcessoControle.listaSelectItemProdutoGymPass}" />
                            </h:selectOneMenu>

                            <%-- Mostrar comboBox de Modalidades, � visualizado quando o combo de tipo Valida��o escolhido � modalidade. --%>
                            <h:selectOneMenu id="selectTipoValidacaoEscolhida" rendered="#{(LocalAcessoControle.codigoTipoValidacao == 1)}"  styleClass="camposObrigatorios" value="#{LocalAcessoControle.validacao.chave}" >
                                <a4j:support event="onchange"
                                             action="#{LocalAcessoControle.montarDescricaoRegra}"
                                             reRender="panelEdicaoValidacao"/>
                                <f:selectItems  value="#{LocalAcessoControle.listaSelectItemModalidade}" />
                            </h:selectOneMenu>
                            <%-- Mostrar pesquisa de Produtos, � visualizado quando o combo de tipo Valida��o escolhido � produto. --%>
                            <h:panelGroup rendered="#{(LocalAcessoControle.codigoTipoValidacao == 2)}">
                                <%@include file="include_SuggestionBoxProduto.jsp" %>
                            </h:panelGroup>

                            <a4j:commandButton id="btnInformacaoPesqProd"
                                               rendered="#{(LocalAcessoControle.codigoTipoValidacao == 2)}"
                                               image="./imagens/informacao.gif"
                                               title="A partir da 1� letra digitada, a pesquisa � realizada automaticamente."
                                               styleClass="botoes"/>
                        </h:panelGroup>

                        <h:outputText value="#{msg_aplic.prt_ColetorVal_TipoHor}"
                                      rendered="#{(LocalAcessoControle.codigoTipoValidacao >0) and (LocalAcessoControle.codigoTipoValidacao != 4)}"
                                      styleClass="tituloCampos"/>
                        <h:selectOneMenu  id="tipoHorario" styleClass="camposObrigatorios" value="#{LocalAcessoControle.codigoTipoHorario}"
                                          rendered="#{(LocalAcessoControle.codigoTipoValidacao >0) and (LocalAcessoControle.codigoTipoValidacao != 4)}">
                            <a4j:support event="onchange" focus="horarioEscolher"
                                         action="#{LocalAcessoControle.montarListaHorario}" reRender="panelEdicaoValidacao"/>
                            <f:selectItems  value="#{LocalAcessoControle.listaSelectItemTipoHorario}" />
                        </h:selectOneMenu>

                        <h:outputText value="#{msg_aplic.prt_PlanoHorario_horario}"
                                      rendered="#{((LocalAcessoControle.codigoTipoHorario == 3) && (LocalAcessoControle.codigoTipoValidacao >0)) || LocalAcessoControle.codigoTipoValidacao == 4}"
                                      styleClass="tituloCampos"/>
                        <h:selectOneMenu  id="horarioEscolher"  rendered="#{((LocalAcessoControle.codigoTipoHorario == 3) && (LocalAcessoControle.codigoTipoValidacao >0))  || LocalAcessoControle.codigoTipoValidacao == 4}"
                                          styleClass="camposObrigatorios" value="#{LocalAcessoControle.validacao.horario.codigo}" >
                            <a4j:support event="onchange"
                                         action="#{LocalAcessoControle.montarDescricaoRegra}"
                                         reRender="panelEdicaoValidacao"/>
                            <f:selectItems  value="#{LocalAcessoControle.listaSelectItemHorario}" />
                        </h:selectOneMenu>
                    </h:panelGrid>
                    <h:outputText id="explicaoRegra" styleClass="tituloCamposVerde" value="#{LocalAcessoControle.explicacaoRegra}"> </h:outputText>
                </h:panelGrid>
                </h:panelGroup>

    </h:panelGrid>

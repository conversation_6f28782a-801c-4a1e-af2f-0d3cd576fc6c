<%@page contentType="text/html;charset=UTF-8" %> 
<head><%@include file="/includes/include_import_minifiles.jsp" %></head>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<link href="beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<link href="${contexto}/css/otimize.css" rel="stylesheet" type="text/css">
<link href="${contexto}/css/ce.css" rel="stylesheet" type="text/css">
<script type="text/javascript" language="javascript">var contexto = '${contexto}';</script>
<script type="text/javascript" language="javascript" src="${contexto}/script/ajuda.js"></script>
<script type="text/javascript" language="javascript" src="hoverform.js"></script>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>
<f:loadBundle var="CElabels" basename="propriedades.CElabels"/>

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText value="#{msg_aplic.prt_TamanhoArmario_tituloForm}"/>
    </title>
    <%-- INICIO HEADER --%>
    <c:set var="titulo" scope="session" value="${msg_aplic.prt_TamanhoArmario_tituloForm}"/>
    <c:set var="urlWiki" scope="session" value="${SuperControle.urlWiki}Cadastros:PPT:Tamanho_Armario"/>
    <f:facet name="header">
        <jsp:include page="topoReduzido_material.jsp"/>
    </f:facet>
    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <h:form id="form">
            <hr style="border-color: #e6e6e6;"/>
            <h:panelGrid columns="1"  width="100%">
                <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar" columnClasses="classEsquerda, classDireita" width="100%">

                    <h:outputText   value="#{msg_aplic.prt_CategoriaProduto_codigo}" />
                    <h:panelGroup>
                        <h:inputText  id="codigo"  size="10" maxlength="10" readonly="true" onblur="blurinput(this);"  onfocus="focusinput(this);" 
                                      styleClass="camposSomenteLeitura"  value="#{TamanhoArmarioControle.tamanhoArmarioVO.codigo}" />
                        <h:message for="codigo" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>

                    <h:outputText   value="#{msg_aplic.prt_CategoriaProduto_descricao}" />
                    <h:panelGroup>
                        <h:inputText  id="descricao"  size="45" maxlength="45" onblur="blurinput(this);"  
                                      onfocus="focusinput(this);" styleClass="form"  
                                      value="#{TamanhoArmarioControle.tamanhoArmarioVO.descricao}" />
                        <h:message for="descricao" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>
                </h:panelGrid>

                <h:panelGrid columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid columns="3" width="100%" styleClass="tabMensagens">
                        <h:panelGrid columns="1" width="100%">
                            <f:verbatim>
                                <h:outputText value=" "/>
                            </f:verbatim>
                        </h:panelGrid>
                        <h:commandButton id="icCategoriaSuc" rendered="#{TamanhoArmarioControle.sucesso}" image="./imagens/sucesso.png"/>
                        <h:commandButton  id="icCategoriaFal" rendered="#{TamanhoArmarioControle.erro}" image="./imagens/erro.png"/>
                        <h:panelGrid columns="1" width="100%">
                            <h:outputText id="msgCategoria" styleClass="mensagem"  value="#{TamanhoArmarioControle.mensagem}"/>
                            <h:outputText id="msgCategoriaDet" styleClass="mensagemDetalhada" value="#{TamanhoArmarioControle.mensagemDetalhada}"/>
                        </h:panelGrid>
                    </h:panelGrid>
                    <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                        <h:panelGroup>
                            <a4j:commandButton id="novo" immediate="true" action="#{TamanhoArmarioControle.novo}" value="#{msg_bt.btn_novo}"  alt="#{msg.msg_novo_dados}" accesskey="1" styleClass="botoes nvoBt btSec"/>
                            <f:verbatim>
                                <h:outputText value="    "/> 
                            </f:verbatim>
                            <a4j:commandButton id="salvar" action="#{TamanhoArmarioControle.gravar}" value="#{msg_bt.btn_gravar}" alt="#{msg.msg_gravar_dados}" accesskey="2" styleClass="botoes nvoBt"/>
                            <f:verbatim>
                                <h:outputText value="    "/> 
                            </f:verbatim>

                            <h:panelGroup id="grupoBtnExcluir">
                                <a4j:commandButton id="excluir"  reRender="mdlMensagemGenerica"
                                                 oncomplete="#{TamanhoArmarioControle.msgAlert}" action="#{TamanhoArmarioControle.confirmarExcluir}"
                                                 value="#{msg_bt.btn_excluir}" title="#{msg.msg_excluir_dados}" accesskey="3" styleClass="botoes nvoBt btSec btPerigo"/>
                            </h:panelGroup>
                            <f:verbatim>
                                <h:outputText value="    "/> 
                            </f:verbatim>
                            <a4j:commandButton id="consultar" immediate="true" action="#{TamanhoArmarioControle.inicializarConsultar}" value="#{msg_bt.btn_voltar_lista}" alt="#{msg.msg_consultar_dados}" accesskey="4" styleClass="botoes nvoBt btSec"/>
                            <f:verbatim>
                                <h:outputText value="    "/> 
                            </f:verbatim>
                            <a4j:commandLink action="#{TamanhoArmarioControle.realizarConsultaLogObjetoSelecionado}"
                                               reRender="form"
                                               oncomplete="abrirPopup('visualizadorLogForm.jsp', 'VisualizadorLog', 785, 595);fireElementFromAnyParent('form:btnAtualizaTempo')"
                                               title="Visualizar Log" styleClass="botoes nvoBt btSec" style="display: inline-block;padding-bottom: 10px;">
                                <i class="fa-icon-list"></i>
                            </a4j:commandLink>
                        </h:panelGroup>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
        </h:form>        
    </h:panelGrid>
    <%@include file="includes/include_modal_mensagem_generica.jsp"%>
</f:view>
<script>
    document.getElementById("form:descricao").focus();
</script>
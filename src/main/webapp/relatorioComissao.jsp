<%-- 
    Document   : relatorioDF
    Created on : 04/10/2011, 17:19:24
    Author     : <PERSON><PERSON><PERSON>
--%>

<%@page contentType="text/html" pageEncoding="ISO-8859-1"%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@include file="include_imports.jsp" %>
<%@taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>

<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<link href="css/jquery.treeTable.css" rel="stylesheet" type="text/css">

<%@include file="/includes/imports.jsp" %>
<script type="text/javascript" src="script/jquery.js"></script>
<script type="text/javascript" src="script/jquery.treeTable.js"></script>

<script type="text/javascript" src="script/cursor.js"></script>

<head>
    <jsp:include page="include_head_imprimir.jsp" flush="true" />
</head>

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
<script>
function inicializarTreeViewComissao() {
    jQuery(document).ready(function($) {

        $(".comissao").treeTable({
            initialState: "expanded"
        });
    });

    jQuery(document).ready(function($){
    	$(".expandir").click(function() {
       	 $(".comissao").expandirTudo();
       	});

    	$(".expandirUm").click(function() {
         	 $(".comissao").expandirUmNivel("dnd-comissao");
       });

    	$(".retrairUm").click(function() {
        	 $(".comissao").retrairUmNivel("dnd-comissao");
      });

    	$(".retrair").click(function() {
          	 $(".comissao").retrairTudo();
        });

           });
}
</script>

<body onload="inicializarTreeViewComissao();" style="background-color:#FFFFFF;">
    <h:form id="form">
        <table border="0" width="100%" >
            <tr>
                <td width="300px">
                    <h:panelGrid columns="2">
                      <h:panelGroup layout="block" style="border: groove">
                        <h:graphicImage value="/fotos/logoPadraoRelatorio.jpg"/>
                      </h:panelGroup>
                    <h:panelGrid columns="1">
                        <h:outputText style="font-size:10px;font-family: sans-serif;font-weight: bold" value="#{GestaoComissaoControle.empresaLogado.nome}"/>
                        <h:outputText style="font-size:10px;font-family: sans-serif;font-weight: bold" value="#{GestaoComissaoControle.empresaLogado.endereco}"/>
                        <h:outputText style="font-size:10px;font-family: sans-serif;font-weight: bold" value="#{GestaoComissaoControle.empresaLogado.cidade_Apresentar}"/>
                    </h:panelGrid>
                    </h:panelGrid>
                </td>
               <td style="text-align: right">

                       <h:panelGrid columns="1" style="text-align: right">
                            <h:outputText style="font-size: 9px;text-align: right;font-family: 'Microsoft Sans Serif' , Arial, Helvetica, Verdana"
                                          value="Zillyon - Sistema Administrativo para Academias Desenvolvido por PACTO Soluções Tecnológicas Ltda."/>

                           <h:outputText style=";font-weight: bold;font-size: 10px;font-family: arial, helvetica, sans-serif" value="#{GestaoComissaoControle.dataAtual}"/> </td>
                     </h:panelGrid>
                </td>

            </tr>

        </table>
        <h:panelGroup layout="block" style="margin-left: 6px;width: 100%;border: groove;text-align: center">

                    <h1 style="font-family: Trebuchet MS">Relatório Gestão de Comissões</h1>

        </h:panelGroup>
        <table style="padding-top:20px; font-family: Trebuchet MS; font-size: 15;">
            <tr>
                <td style="font-weight: bold"> <h:outputText  value="Parâmetros:" ></h:outputText></td>
                <td> <h:outputText  value="" ></h:outputText></td>
            </tr>
            <tr>
                <td style="padding-left:20px"> <h:outputText value="  Consulta:" ></h:outputText> </td>
                <td>  <h:outputText value="#{GestaoComissaoControle.tipoConsulta}" ></h:outputText></td>

            </tr>
            <h:panelGroup  rendered="#{not empty GestaoComissaoControle.somenteAlunosTurma}">
              <tr>
                <td style="padding-left:20px"> <h:outputText value="  Observação:" ></h:outputText> </td>
                <td>  <h:outputText value="#{GestaoComissaoControle.somenteAlunosTurma}" ></h:outputText></td>

            </tr>
            </h:panelGroup>
            <tr>
                <td style="padding-left:20px"> <h:outputText value="  Período:" ></h:outputText> </td>
                <td>  <h:outputText value="#{GestaoComissaoControle.dataInicioApresentar} até  #{GestaoComissaoControle.dataFimApresentar}"></h:outputText></td>

            </tr>
            <h:panelGroup  rendered="#{not empty GestaoComissaoControle.colaboradoresSel}">
           		 <table>
                	<tr>
                   	 <td> <h:outputText style="padding-left:20px" value="  Professores:" ></h:outputText> </td>
                   	 <td> <h:outputText  value="#{GestaoComissaoControle.colaboradoresSel}" ></h:outputText> </td>
               		</tr>
            	</table>
        	</h:panelGroup>
          </table>

		<%@include file="include_gestaoComissao_Sintetico.jsp" %>

        

        <h:panelGrid columns="1" columnClasses="colunaCentralizada" width="100%" style="background-color:#FFFFFF;">
            <h:commandLink
                onclick="window.print();return false;">
                <h:graphicImage value="/imagens/botoesCE/imprimir.png"  style="border: 0px; width: 65;"/>
            </h:commandLink>
        </h:panelGrid>

    </h:form>
 </body>   
</f:view>

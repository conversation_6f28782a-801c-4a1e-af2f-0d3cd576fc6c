<%@page contentType="text/html"%>
<%@page pageEncoding="ISO-8859-1"%>
<head><%@include file="/includes/include_import_minifiles.jsp" %></head>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<script type="text/javascript" language="javascript" src="hoverform.js"></script>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText value="Aula Avulsa "/>
    </title>
    <a4j:loadScript src="/script/jquery.maskedinput-1.2.2.js" />

    <rich:modalPanel id="panelSenhaDesconto" autosized="true" shadowOpacity="true" width="450" height="250" onshow="document.getElementById('formSenhaDesconto:senha').focus()">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="#{msg_aplic.prt_AulaAvulsa_autorizar}"></h:outputText>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:graphicImage value="/imagens/close.png" style="cursor:pointer" id="hidelinkSenhaDesconto"/>
                <rich:componentControl for="panelSenhaDesconto" attachTo="hidelinkSenhaDesconto" operation="hide"  event="onclick">
                    <a4j:support oncomplete="#{AulaAvulsaDiariaControle.fechaPanelSenhaDesconto}"/>
                </rich:componentControl>
            </h:panelGroup>
        </f:facet>
        <a4j:form id="formSenhaDesconto">
            <h:panelGrid columns="1"  width="100%" columnClasses="colunaCentralizada">
                <h:panelGrid columns="1" style="height:25px; background-image:url('./imagens/fundoBarraTopo.png');background-repeat: repeat-x;" columnClasses="colunaCentralizada" width="100%">
                    <h:outputText styleClass="tituloFormulario" value="Confirmação"/>
                </h:panelGrid>
                <h:panelGrid id="panelConfimacaoSenhaDesconto" columns="1" width="100%" columnClasses="colunaEsquerda" styleClass="tabForm">
                    <h:panelGroup>
                        <h:inputText  style="opacity:0;height: 0px;" size="5" maxlength="7" />
                        <h:inputSecret  size="14" maxlength="64" style="opacity:0;height: 0px;"/>
                    </h:panelGroup>
                    <h:panelGroup >
                        <h:outputText styleClass="text"  value="Código:" />
                        <h:inputText id="codigoUsuario" size="5" maxlength="7" style="margin-left:5px" value="#{AulaAvulsaDiariaControle.aulaAvulsaDiariaVO.responsavel.codigo}">
                            <a4j:support event="onchange" focus="formSenhaDesconto:senha" action="#{AulaAvulsaDiariaControle.consultarResponsavel}"
                                         reRender="panelConfimacaoSenhaDesconto, mensagem"/>
                        </h:inputText>
                        <h:inputText id="autoCompleteHidden" size="3" maxlength="10" style="margin-left:6px;opacity:0;" value="#{AulaAvulsaDiariaControle.aulaAvulsaDiariaVO.responsavel.username}"/>
                    </h:panelGroup>
                    <h:panelGroup >
                        <h:outputText styleClass="text"  value="Usuário:" />
                        <h:outputText styleClass="text" style="margin-left:6px"  value="#{AulaAvulsaDiariaControle.aulaAvulsaDiariaVO.responsavel.username}" />
                    </h:panelGroup>
                    <h:panelGroup>
                        <h:outputText  styleClass="text" value="Senha:"/>
                        <h:inputSecret autocomplete="off" id="senha" size="14" maxlength="64" style="margin-left:8px"
                                       value="#{AulaAvulsaDiariaControle.aulaAvulsaDiariaVO.responsavel.senha}"/>
                        <rich:hotKey selector="#senha" key="return"
                                     handler="#{rich:element('autorizarSD')}.onclick();return false;"/>
                    </h:panelGroup>
                    <h:outputText id="obs" styleClass="text" value="#{msg_aplic.prt_AulaAvulsa_obsDesconto}"/>
                </h:panelGrid>
                <h:outputText id="mensagem" styleClass="mensagemDetalhada" value="#{AulaAvulsaDiariaControle.mensagemDetalhada}"/>
                <h:panelGroup>
                    <a4j:commandButton id="autorizarSD" value="#{msg_bt.btn_gravar}" reRender="mensagem, panelMesangem, form:panelItemVenda, panelGroupDesconto"
                                       oncomplete="#{AulaAvulsaDiariaControle.fechaPanelSenhaDesconto}"
                                       image="./imagens/botaoGravar.png" alt="#{msg.msg_gravar_dados}"
                                       action="#{AulaAvulsaDiariaControle.validarPermissaoDescontoAulaAvulsa}"/>
                </h:panelGroup>
            </h:panelGrid>
        </a4j:form>
    </rich:modalPanel>

    <rich:modalPanel id="panelCliente" autosized="true" shadowOpacity="true" width="550" height="300" onshow="document.getElementById('formCliente:consultarCliente').focus();">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="#{msg_aplic.prt_AulaAvulsa_consultarCliente}"/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:graphicImage value="/imagens/close.png" style="cursor:pointer" id="hiperlinkCliente"/>
                <rich:componentControl for="panelCliente" attachTo="hiperlinkCliente" operation="hide" event="onclick"/>
            </h:panelGroup>
        </f:facet>
        <a4j:form id="formCliente" >
            <h:panelGrid columns="1" width="100%">
                <h:panelGrid columns="4" width="100%">
                    <h:outputText styleClass="tituloCampos" value="#{msg.msg_consultar_por}"/>
                    <h:selectOneMenu styleClass="campos" id="consultarCliente" value="#{AulaAvulsaDiariaControle.campoConsultarCliente}">
                        <f:selectItems value="#{AulaAvulsaDiariaControle.tipoConsultarComboCliente}"/>
                    </h:selectOneMenu>
                    <h:inputText id="valorConsultarCliente" styleClass="campos" value="#{AulaAvulsaDiariaControle.valorConsultarCliente}"/>
                    <a4j:commandButton id="btnConsultarCliente" reRender="formCliente:mensagemConsultarCliente, formCliente:resultadoConsultaCliente , formCliente:scResultadoCliente , formCliente" action="#{AulaAvulsaDiariaControle.consultarCliente}" styleClass="botoes" value="#{msg_bt.btn_consultar}" image="./imagens/botaoConsultar.png"/>
                </h:panelGrid>
                <rich:dataTable id="resultadoConsultaCliente" width="100%" headerClass="consulta" rowClasses="linhaImpar, linhaPar" columnClasses="colunaAlinhamento" value="#{AulaAvulsaDiariaControle.listaConsultarCliente}" rows="10" var="cliente">
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_Cliente_codigo}"/>
                        </f:facet>
                        <h:outputText value="#{cliente.codigo}"/>
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_Cliente_pessoa}"/>
                        </f:facet>
                        <h:outputText value="#{cliente.pessoa.nome}"/>
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_Cliente_situacao}"/>
                        </f:facet>
                        <h:outputText value="#{cliente.situacao_Apresentar}"/>
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_Cliente_matricula}"/>
                        </f:facet>
                        <h:outputText value="#{cliente.matricula}"/>
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_bt.btn_opcoes}"/>
                        </f:facet>
                        <a4j:commandButton id="selecionarCliente" action="#{AulaAvulsaDiariaControle.selecionarCliente}" focus="cliente" reRender="form:panelGeral, formCliente, form:mensagem" oncomplete="Richfaces.hideModalPanel('panelCliente')" value="#{msg_bt.btn_selecionar}" styleClass="botoes" image="./imagens/botaoEditar.png"/>
                    </rich:column>
                </rich:dataTable>
                <rich:datascroller align="center" for="formCliente:resultadoConsultaCliente" maxPages="10" id="scResultadoCliente"/>
                <h:panelGrid id="mensagemConsultaCliente" columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid columns="1" width="100%">
                        <h:outputText styleClass="mensagem" value="#{AulaAvulsaDiariaControle.mensagem}"/>
                        <h:outputText styleClass="mensagemDetalhada" value="#{AulaAvulsaDiariaControle.mensagemDetalhada}"/>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
        </a4j:form>
    </rich:modalPanel>

    <%--rich:modalPanel id="panelColaborador" autosized="true" shadowOpacity="true" width="550" height="300" onshow="document.getElementById('formColaborador:consultarColaborador').focus();">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="#{msg_aplic.prt_AulaAvulsa_consultarColaborador}"/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:graphicImage value="/imagens/close.png" style="cursor:pointer" id="hiperlinkColaborador"/>
                <rich:componentControl for="panelColaborador" attachTo="hiperlinkColaborador" operation="hide" event="onclick"/>
            </h:panelGroup>
        </f:facet>
        <a4j:form id="formColaborador" ajaxSubmit="true" >
            <h:panelGrid columns="1" width="100%">
                <h:panelGrid columns="4" width="100%">
                    <h:outputText styleClass="tituloCampos" value="#{msg.msg_consultar_por}"/>
                    <h:selectOneMenu styleClass="campos" id="consultarColaborador" value="#{AulaAvulsaDiariaControle.campoConsultarColaborador}">
                        <f:selectItems value="#{AulaAvulsaDiariaControle.tipoConsultarComboColaborador}"/>
                    </h:selectOneMenu>
                    <h:inputText id="valorConsultarColaborador"styleClass="campos" value="#{AulaAvulsaDiariaControle.valorConsultarColaborador}"/>
                    <a4j:commandButton id="btnConsultarColaborador" reRender="formColaborador:mensagemConsultarColaborador, formColaborador:resultadoConsultaColaborador , formColaborador:scResultadoColaborador , formColaborador, form:mensagem" action="#{AulaAvulsaDiariaControle.consultarColaborador}" styleClass="botoes" value="#{msg_bt.btn_consultar}" image="./imagens/botaoConsultar.png"/>
                </h:panelGrid>
                <rich:dataTable id="resultadoConsultaColaborador" width="100%" headerClass="consulta" rowClasses="linhaImpar, linhaPar" columnClasses="colunaAlinhamento" value="#{AulaAvulsaDiariaControle.listaConsultarColaborador}" rows="10" var="colaborador">
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_Colaborador_codigo}"/>
                        </f:facet>
                        <h:outputText value="#{colaborador.codigo}"/>
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_Colaborador_pessoa}"/>
                        </f:facet>
                        <h:outputText value="#{colaborador.pessoa.nome}"/>
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_bt.btn_opcoes}"/>
                        </f:facet>
                        <a4j:commandButton action="#{AulaAvulsaDiariaControle.selecionarColaborador}" focus="colaborador" reRender="form:panelGeral, formColaborador, mensagem" oncomplete="Richfaces.hideModalPanel('panelColaborador')" value="#{msg_bt.btn_selecionar}" styleClass="botoes" image="./imagens/botaoEditar.png"/>
                    </rich:column>
                </rich:dataTable>
                <rich:datascroller align="center" for="formColaborador:resultadoConsultaColaborador" maxPages="10" id="scResultadoColaborador"/>
                <h:panelGrid id="mensagemConsultaColaborador" columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid columns="1" width="100%">
                        <h:outputText styleClass="mensagem" value="#{AulaAvulsaDiariaControle.mensagem}"/>
                        <h:outputText styleClass="mensagemDetalhada" value="#{AulaAvulsaDiariaControle.mensagemDetalhada}"/>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
        </a4j:form>
    </rich:modalPanel--%>

    <rich:modalPanel id="panelModalidade" shadowOpacity="true" width="550" height="350" onshow="document.getElementById('formModalidade:consultarModalidade').focus();">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="#{msg_aplic.prt_AulaAvulsa_consultarModalidade}"/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:graphicImage value="/imagens/close.png" style="cursor:pointer" id="hiperlinkModalidade"/>
                <rich:componentControl for="panelModalidade" attachTo="hiperlinkModalidade" operation="hide" event="onclick"/>
            </h:panelGroup>
        </f:facet>
        <a4j:form id="formModalidade" ajaxSubmit="true" >
            <h:panelGrid columns="1" width="100%">
                <h:panelGrid columns="4" width="100%">
                    <h:outputText styleClass="tituloCampos" value="#{msg.msg_consultar_por}"/>
                    <h:selectOneMenu styleClass="campos" id="consultarModalidade" value="#{AulaAvulsaDiariaControle.campoConsultarModalidade}">
                        <f:selectItems value="#{AulaAvulsaDiariaControle.tipoConsultarComboModalidade}"/>
                    </h:selectOneMenu>
                    <h:inputText id="valorConsultarModalidade" styleClass="campos" value="#{AulaAvulsaDiariaControle.valorConsultarModalidade}"/>
                    <a4j:commandButton id="btnConsultarModalidade" reRender="formModalidade:mensagemConsultarModalidade, formModalidade:resultadoConsultaModalidade , formModalidade:scResultadoModalidade , formModalidade" action="#{AulaAvulsaDiariaControle.consultarModalidade}" styleClass="botoes" value="#{msg_bt.btn_consultar}" image="./imagens/botaoConsultar.png"/>
                </h:panelGrid>
                <rich:dataTable id="resultadoConsultaModalidade" width="100%" headerClass="consulta" rowClasses="linhaImpar, linhaPar" columnClasses="colunaAlinhamento" value="#{AulaAvulsaDiariaControle.listaConsultarModalidade}" rows="5" var="modalidade">
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_Modalidade_codigo}"/>
                        </f:facet>
                        <h:outputText value="#{modalidade.codigo}"/>
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_AulaAvulsa_consultarModalidade_modalidade}"/>
                        </f:facet>
                        <h:outputText value="#{modalidade.nome}"/>
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_bt.btn_opcoes}"/>
                        </f:facet>
                        <a4j:commandButton id="selecionarModalidade" action="#{AulaAvulsaDiariaControle.selecionarModalidade}" focus="modalidade" reRender="form:panelGeral, formModalidade, mensagem" oncomplete="Richfaces.hideModalPanel('panelModalidade')" value="#{msg_bt.btn_selecionar}" styleClass="botoes" image="./imagens/botaoEditar.png"/>
                    </rich:column>
                </rich:dataTable>
                <rich:datascroller align="center" for="formModalidade:resultadoConsultaModalidade" maxPages="10" id="scResultadoModalidade"/>
                <h:panelGrid id="mensagemConsultaModalidade" columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid columns="1" width="100%">
                        <h:outputText styleClass="mensagem" value="#{AulaAvulsaDiariaControle.mensagem}"/>
                        <h:outputText styleClass="mensagemDetalhada" value="#{AulaAvulsaDiariaControle.mensagemDetalhada}"/>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
        </a4j:form>
    </rich:modalPanel>

    <h:form id="form" >
        <html>
            <jsp:include page="include_head.jsp" flush="true" />
            <body>
                <table width="100%" height="100%" border="0" cellpadding="0" cellspacing="0">
                    <c:if test="${MenuControle.apresentarTopo}">
                        <tr>
                            <td height="77" align="left" valign="top" class="bgtop"><jsp:include page="include_top.jsp" flush="true" />	</td>
                        </tr>

                        <tr>
                            <td height="48" align="left" valign="top" class="bgmenu"><jsp:include page="include_menu.jsp" flush="true" /></td>
                        </tr>
                    </c:if>
                    <tr>
                        <td align="left" valign="top" class="bglateral">
                            <table width="100%" height="100%" border="0" cellpadding="0" cellspacing="0">
                                <tr>
                                    <td width="206" align="center" valign="top" class="bglateraltop" style="padding-top:6px;"><jsp:include page="include_box_menulateral.jsp" flush="true" /><jsp:include page="include_box_descricao.jsp" flush="true" />
                                    </td>
                                    <td align="left" valign="top" style="padding:7px 20px 0 20px;">

                                        <table width="100%" height="100%" border="0" align="left" cellpadding="0" cellspacing="0" class="text" style="margin-bottom:20px;">
                                            <tr>
                                                <td height="28" colspan="3" align="left" valign="top" style="padding-left:17px;"><a class="hierarquialink" href="tela1.jsp"><img border="0" style="margin-right:4px;vertical-align:middle;" src="images/arrow_back.gif" alt="Inicial">Inicial</a> <span class="hierarquia">> Aula Avulsa</span></td>
                                            </tr>
                                            <tr>
                                                <td width="19" height="50" align="left" valign="top"><img src="images/box_centro_top_left.gif" width="19" height="50"></td>
                                                <td align="left" valign="top" background="images/box_centro_top.gif" class="tituloboxcentro" style="padding:11px 0 0 0;">
                                                    Aula Avulsa
                                                    <h:outputLink value="#{SuperControle.urlWiki}Inicial:Aula_Avulsa"
                                                                  title="Clique e saiba mais: Aula Avulsa" target="_blank">
                                                        <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                                                    </h:outputLink>
                                                </td>
                                                <td width="19" align="left" valign="top"><img src="images/box_centro_top_right.gif" width="19" height="50"></td>
                                            </tr>
                                            <tr>
                                                <td align="left" valign="top" background="images/box_centro_left.gif"><img src="images/shim.gif"></td>
                                                <td align="left" valign="top" bgcolor="#ffffff" style="padding:5px 15px 5px 15px;">
                                                    <p style="margin-bottom:6px;">
                                                        <img src="images/arrow2.gif" width="16" height="16" style="vertical-align:middle;margin-right:6px;">Aula Avulsa
                                                    </p>
                                                    <div class="sep" style="margin:4px 0 10px 0;"><img src="images/shim.gif"></div>
                                                        <%--h:panelGrid id="mensagem2" columns="2" width="100%" >
                                                            <h:panelGrid columns="1" width="100%">
                                                                <h:outputText styleClass="mensagem"  value="#{AulaAvulsaDiariaControle.mensagem}"/>
                                                                <h:outputText styleClass="mensagemDetalhada" value="#{AulaAvulsaDiariaControle.mensagemDetalhada}"/>
                                                            </h:panelGrid>
                                                        </h:panelGrid--%>
                                                        <h:panelGrid id="panelGeral" columns="1"  width="100%" rowClasses="linhaImpar, linhaPar">
                                                            <h:panelGroup rendered="#{AulaAvulsaDiariaControle.aulaAvulsaDiariaVO.apresentarEmpresa}">
                                                                <h:outputText  styleClass="tituloCampos"  value="#{msg_aplic.prt_AulaAvulsa_empresa}" />
                                                                <rich:spacer width="70"/>
                                                                <h:selectOneMenu  id="empresa" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" value="#{AulaAvulsaDiariaControle.aulaAvulsaDiariaVO.empresa.codigo}" >
                                                                    <f:selectItems value="#{AulaAvulsaDiariaControle.listaSelectItemEmpresa}" />
                                                                </h:selectOneMenu>
                                                                <a4j:commandButton id="atualizar_empresa" action="#{AulaAvulsaDiariaControle.montarListaSelectItemEmpresa}" image="imagens/atualizar.png" immediate="true" ajaxSingle="true" reRender="form:empresa"/>
                                                            </h:panelGroup>
                                                            <%--h:panelGrid columns="2">
                                                                <h:selectOneRadio id="tipoComprador" styleClass="tituloCampos" value="#{AulaAvulsaDiariaControle.aulaAvulsaDiariaVO.tipoComprador}">
                                                                    <f:selectItems   value="#{AulaAvulsaDiariaControle.listaSelectItemTipoComprador}"/>
                                                                    <a4j:support event="onclick" action="#{AulaAvulsaDiariaControle.aulaAvulsaDiariaVO.apresentarComprador}" reRender="form:panelGroupComprador,mensagem"/>
                                                                </h:selectOneRadio>

                                                        </h:panelGrid--%>
                                                            <h:panelGroup id="panelGroupComprador">
                                                                <h:outputText  styleClass="tituloCampos"  value="#{msg_aplic.prt_AulaAvulsa_nomeComprador}" />
                                                                <rich:spacer width="20"/>
                                                                <h:inputText  id="nomeComprador" size="50" readonly="true" maxlength="50" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" value="#{AulaAvulsaDiariaControle.aulaAvulsaDiariaVO.nomeComprador}" />
                                                                <rich:spacer width="5" />
                                                                <a4j:commandButton id="consultarCliente"  oncomplete="Richfaces.showModalPanel('panelCliente')" image="imagens/informacao.gif" alt="#{msg_aplic.prt_AulaAvulsa_consultarCliente}"/>
                                                                <a4j:commandButton  id="limparCliente" immediate="true" action="#{AulaAvulsaDiariaControle.limparCliente}" image="images/limpar.gif" alt="#{msg_aplic.prt_limparCampo}" reRender="nomeComprador"/>
                                                            </h:panelGroup>
                                                            <h:panelGroup id="panelGroupProduto">
                                                                <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_AulaAvulsa_produto}" />
                                                                <rich:spacer width="75"/>
                                                                <h:selectOneMenu  id="produto" onblur="blurinput(this);"  onfocus="focusinput(this);" value="#{AulaAvulsaDiariaControle.aulaAvulsaDiariaVO.produto.codigo}"  styleClass="form"  >
                                                                    <f:selectItems  value="#{AulaAvulsaDiariaControle.listaSelectItemProdutoAulaAvulsa}" />
                                                                    <a4j:support event="onchange" action="#{AulaAvulsaDiariaControle.obterValorProduto}" reRender="totalLancado,form:mensagem"/>
                                                                </h:selectOneMenu>
                                                                <rich:spacer width="3"/>
                                                                <a4j:commandButton id="atualizar_produto" action="#{AulaAvulsaDiariaControle.montarListaSelectItemProdutoAulaAvulsa}" image="imagens/atualizar.png" immediate="true" ajaxSingle="true" reRender="form:produto"/>
                                                            </h:panelGroup>
                                                            <h:panelGroup id="panelGroupModalidade">
                                                                <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_AulaAvulsa_modalidade}" />
                                                                <rich:spacer width="55"/>
                                                                <h:inputText  id="modalidade" size="50" maxlength="50" onblur="blurinput(this);" onfocus="focusinput(this);" readonly="true" styleClass="form" value="#{AulaAvulsaDiariaControle.aulaAvulsaDiariaVO.modalidade.nome}" />
                                                                <rich:spacer width="5" />
                                                                <a4j:commandButton id="consultarModalidade"  oncomplete="Richfaces.showModalPanel('panelModalidade')" image="imagens/informacao.gif" />
                                                                <a4j:commandButton  id="limparModalidade" immediate="true" reRender="modalidade" action="#{AulaAvulsaDiariaControle.limparModalidade}"  image="images/limpar.gif" alt="#{msg_aplic.prt_limparCampo}" />
                                                            </h:panelGroup>
                                                            <h:panelGroup id="panelGroupDesconto">
                                                                <h:outputText styleClass="tituloCampos"
                                                                              value="Desconto"/>
                                                                <rich:spacer width="82"/>
                                                                <h:inputText readonly="#{!AulaAvulsaDiariaControle.aulaAvulsaDiariaVO.descontoManual}" size="10" maxlength="50"
                                                                             onblur="blurinput(this);"
                                                                             id="valorDescontoManual"
                                                                             onfocus="focusinput(this);"
                                                                             styleClass="form"
                                                                             value="#{AulaAvulsaDiariaControle.aulaAvulsaDiariaVO.valorDescontoManual}">
                                                                    <f:converter converterId="FormatadorNumerico" />
                                                                </h:inputText>
                                                                <rich:spacer width="5"/>
                                                                <a4j:commandButton id="btnAplicarDesconto"
                                                                                   image="./images/tick.png" title="Aplicar Novo Valor"
                                                                                   rendered="#{AulaAvulsaDiariaControle.aplicarValor}"
                                                                                   action="#{AulaAvulsaDiariaControle.editarDescontoProduto}" reRender="form"
                                                                                   oncomplete="#{AulaAvulsaDiariaControle.onCompleteDesconto}"/>
                                                                <a4j:commandButton id="btnChave"
                                                                                   image="./images/icon_chave.png"
                                                                                   rendered="#{!AulaAvulsaDiariaControle.aplicarValor}"
                                                                                   action="#{AulaAvulsaDiariaControle.editarCampoDescontoProduto}"
                                                                                   oncomplete="#{AulaAvulsaDiariaControle.onCompleteDesconto}"
                                                                                   title="Informar Valor do Desconto"
                                                                                   reRender="panelAutorizacaoFuncionalidade"/>
                                                                <rich:spacer width="5"/>
                                                                <a4j:commandButton id="limparTabelaDesconto"
                                                                                   image="images/limpar.gif"
                                                                                   action="#{AulaAvulsaDiariaControle.limparDescontoManual}"
                                                                                   alt="Limpar Desconto"
                                                                                   reRender="form,panelGeral, panelGroupDesconto, totalLancado"/>
                                                            </h:panelGroup>
                                                            <h:panelGroup id="panelGroupDataInicio">
                                                                <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_AulaAvulsa_datainicio}" />
                                                                <rich:spacer width="56"/>
                                                                <rich:calendar id="dataInicio"
                                                                               value="#{AulaAvulsaDiariaControle.aulaAvulsaDiariaVO.dataInicio}"
                                                                               inputSize="10"
                                                                               inputClass="form"
                                                                               oninputblur="blurinput(this);"
                                                                               oninputfocus="focusinput(this);"
                                                                               oninputchange="return validar_Data(this.id);"
                                                                               datePattern="dd/MM/yyyy"
                                                                               enableManualInput="true"
                                                                               zindex="2"
                                                                               showWeeksBar="false"/>
                                                                <rich:jQuery id="mskData" selector=".rich-calendar-input" timing="onload" query="mask('99/99/9999')" />
                                                                <rich:spacer width="3"/>
                                                            </h:panelGroup>
                                                        </h:panelGrid>
                                                        <h:panelGrid id="mensagem" columns="2" width="100%" >
                                                            <h:panelGrid columns="3" width="100%" styleClass="tabMensagens">
                                                                <h:panelGrid columns="1" width="100%">
                                                                    <f:verbatim>
                                                                        <h:outputText value=" "/>
                                                                    </f:verbatim>
                                                                </h:panelGrid>
                                                                <h:commandButton  rendered="#{AulaAvulsaDiariaControle.sucesso}" image="./imagens/sucesso.png"/>
                                                                <h:commandButton rendered="#{AulaAvulsaDiariaControle.erro}" image="./imagens/erro.png"/>
                                                                <h:panelGrid columns="1" width="100%">
                                                                    <h:outputText id="msgAulaAvulsa" styleClass="mensagem" value="#{AulaAvulsaDiariaControle.mensagem}"/>
                                                                    <h:outputText id="msgAulaAvulsaDet" styleClass="mensagemDetalhada" value="#{AulaAvulsaDiariaControle.mensagemDetalhada}"/>
                                                                </h:panelGrid>
                                                            </h:panelGrid>
                                                        </h:panelGrid>

                                                    <table width="100%" border="0" cellspacing="0" cellpadding="0" class="tablepadding2" >
                                                        <tr>
                                                            <td align="left" valign="top">
                                                                <table width="100%" border="0" cellspacing="0" cellpadding="0" class="tablepreviewtotal">
                                                                    <tr>
                                                                        <td width="50%" align="left" valign="middle">Total Lan&ccedil;ado = <span class="verde">R$<h:outputText  id="totalLancado" value="#{AulaAvulsaDiariaControle.aulaAvulsaDiariaVO.valor}"><f:converter converterId="FormatadorNumerico"/></h:outputText></span></td>
                                                                    </tr>
                                                                </table>
                                                            </td>
                                                        </tr>
                                                    </table>

                                                    <!-- inicio botões -->
                                                    <h:panelGrid id="panelBotoesControle"  style="margin-right:0px" columns="1">
                                                        <h:panelGrid   style="margin-right:0px" columns="3">
                                                            <%--<a4j:commandLink id="botaoConfirmarcao" action="#{AulaAvulsaDiariaControle.validarDadosAulaAvulsa}" reRender="form:panelGeral, form:totalLancado,mensagem" oncomplete="#{AulaAvulsaDiariaControle.abrirRichConfimacao}">
                                                                <img src="images/btn_confirmar.gif" width="123" height="41" border="0" class="imgalpha" title="Confirmar">
                                                            </a4j:commandLink>--%>
                                                            <a4j:commandLink id="botaoPagar"
                                                                             action="#{AulaAvulsaDiariaControle.validarDadosAulaAvulsa}"
                                                                             reRender="form:panelGeral, form:totalLancado,form:mensagem, panelAutorizacaoFuncionalidade">
                                                                <img id="botaoPagarImg" src="images/btn_receberFundoBranco.png" width="120" height="40" border="0" class="imgalpha" title="Receber">
                                                            </a4j:commandLink>

                                                            <a href="tela1.jsp">
                                                                <img id="botaoCancelar" src="images/btn_cancelar2.gif" width="111" height="41" border="0" class="imgalpha" title="Cancelar">
                                                            </a>
                                                        </h:panelGrid>
                                                    </h:panelGrid>
                                                    <!-- fim botões -->

                                                </td>
                                                <td align="left" valign="top" background="images/box_centro_right.gif"><img src="images/shim.gif"></td>
                                            </tr>
                                            <tr>
                                                <td height="20" align="left" valign="top"><img src="images/box_centro_bottom_left.gif" width="19" height="20"></td>
                                                <td align="left" valign="top" background="images/box_centro_bottom.gif"><img src="images/shim.gif"></td>
                                                <td align="left" valign="top"><img src="images/box_centro_bottom_right.gif" width="19" height="20"></td>
                                            </tr>
                                        </table>
                                    </td>
                                </tr>
                            </table>
                        </td>
                    </tr>
                    <tr>
                        <td height="93" align="left" valign="top" class="bgrodape"><jsp:include page="include_rodape.jsp" flush="true" /></td>
                    </tr>
                </table>
            </body>
        </html>
    </h:form>

    <jsp:include page="includes/autorizacao/include_autorizacao_funcionalidade_nova.jsp" flush="true"/>
</f:view>
<script>
    document.getElementById("form:tipoComprador").focus();
</script>

<%-- 
    Document   : cancelamentoListaChequeForm
    Created on : 29/06/2009, 18:50:39
    Author     : <PERSON>
--%>

<%@page contentType="text/html"%>
<%@page pageEncoding="ISO-8859-1"%>
<head><%@include file="/includes/include_import_minifiles.jsp" %></head>
<link href="beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/pure-buttons.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/pure-ext.css" type="text/css" rel="stylesheet"/>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<script type="text/javascript" language="javascript" src="hoverform.js"></script>
<script type="text/javascript" language="javascript">
     setTimeout(setInterval(function() { getDocumentCookie('popupsImportante') == 'close' ? this.close() : ''; }, 500), 500);
</script>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>
<style type="text/css">
    body {
        background-color: transparent!important;
    }

    .pure-button{
        font-family: sans-serif;
        font-size:100%;
        margin:0;
        vertical-align:baseline;
        *vertical-align:middle
    }
    table.tabelaSimplesCustom:not(.showCellEmpty) tr td:empty {
        display: table-cell !important;
    }
    .cabecalho-tabela{
        background-color: #FFFFFF;
    }

    .tabela-rich-faces-sem-borda .rich-table-cell{
        border: none !important;
    }
</style>
<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <c:set var="urlWiki" scope="session" value="${SuperControle.urlBaseConhecimento}como-faco-para-cancelar-um-plano-com-devolucao-de-valores/"/>
        <c:set var="titulo" scope="session" value="Cancelamento"/>
    </title>

    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <f:facet name="header">
            <jsp:include page="topoReduzido_material.jsp"/>
        </f:facet>
    </h:panelGrid>

    <rich:modalPanel id="panel" width="350" height="100" showWhenRendered="#{CancelamentoContratoControle.cancelamentoContratoVO.mensagemErro}">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Atenção!"/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:graphicImage value="/imagens/close.png" style="cursor:pointer" id="hidelink"/>
                <rich:componentControl for="panel" attachTo="hidelink" operation="hide" event="onclick"/>
            </h:panelGroup>
        </f:facet>
        <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
            <h:panelGroup>
                <h:graphicImage value="/imagens/erro.png"/>
                <rich:spacer width="10" />
                <h:outputText id="msgCancelamentoDet" styleClass="mensagemDetalhada" value="#{CancelamentoContratoControle.mensagemDetalhada}"/>
            </h:panelGroup>
        </h:panelGrid>
    </rich:modalPanel>
    <div styleClass="col-md-12"><hr class="dividerFundoClaro"/></div>
    <h:form id="form">
        <a4j:keepAlive beanName="RetiradaAutomaticaControle"/>
        <a4j:keepAlive beanName="CaixaControle"/>
        <h:panelGrid columns="2" width="100%">
            <h:panelGrid columns="1" width="100%" cellpadding="2" style="margin-left: auto; margin-right: auto; padding-left: 70px; padding-right: 70px;">
                <h:panelGrid columns="2">
                    <h:outputText value="NOME DO CLIENTE: " styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold"  />
                    <h:panelGroup>
                        <rich:spacer width="12"/>
                        <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{ClienteControle.clienteVO.pessoa.nome}"/>
                    </h:panelGroup>

                    <c:if test="${CancelamentoContratoControle.contratoVO.cancelamentoAntecipado}">
                    <h:outputText value="VALOR CANCELAMENTO " styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold" />
                    <h:panelGroup>
                        <rich:spacer width="12"/>
                        <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{MovPagamentoControle.empresaLogado.moeda} " />
                        <h:outputText id="valorTotalCancelamentoAntecipado" value="#{CancelamentoContratoControle.cancelamentoContratoVO.valorTotalCancelamentoAntecipado}" styleClass="texto-size-14-real texto-cor-cinza texto-font">
                            <f:converter converterId="FormatadorNumerico" />
                        </h:outputText>
                    </h:panelGroup>
                    </c:if>

                    <c:if test="${!CancelamentoContratoControle.contratoVO.cancelamentoAntecipado}">
                    <h:outputText value="VALOR PAGO PELO CLIENTE " styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold" />
                    <h:panelGroup>
                        <rich:spacer width="12"/>
                        <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{MovPagamentoControle.empresaLogado.moeda}  " />
                        <h:outputText id="valorPago" value="#{CancelamentoContratoControle.cancelamentoContratoVO.somaValorPagoPeloClienteComCheque}" styleClass="texto-size-14-real texto-cor-cinza texto-font">
                            <f:converter converterId="FormatadorNumerico" />
                        </h:outputText>
                    </h:panelGroup>

                    <h:outputText value="VALOR TRANSFERêNCIA DE SALDO "
                                  rendered="#{CancelamentoContratoControle.devolverTransferenciaSaldoCredito}"
                                  styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold"
                                  title="Valor da transferência de saldo do contrato renovado antecipadamente."/>

                    <h:panelGroup rendered="#{CancelamentoContratoControle.devolverTransferenciaSaldoCredito}">
                        <rich:spacer width="12"/>
                        <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{MovPagamentoControle.empresaLogado.moeda}  " />
                        <h:outputText id="valorPagoTransfSaldo" value="#{CancelamentoContratoControle.contratoDuracaoCreditoTreinoCancelar.totalTransferenciaSaldo}" styleClass="texto-size-14-real texto-cor-cinza texto-font">
                            <f:converter converterId="FormatadorNumerico" />
                        </h:outputText>
                    </h:panelGroup>


                    <h:outputText value="VALOR DO CANCELAMENTO DO CLIENTE " styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold" />
                    <h:panelGroup>
                        <rich:spacer width="12"/>
                        <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{MovPagamentoControle.empresaLogado.moeda}  " />
                        <h:outputText id="valorCobrado" value="#{CancelamentoContratoControle.cancelamentoContratoVO.valorASerPagoPeloCliente}" styleClass="texto-size-14-real texto-cor-cinza texto-font">
                            <f:converter converterId="FormatadorNumerico" />
                        </h:outputText>
                    </h:panelGroup>
                    <h:panelGroup id="labelValor">
                        <h:outputText value="VALOR A SER DEVOLVIDO EM DINHEIRO AO CLIENTE " styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold" rendered="#{!CancelamentoContratoControle.contratoVO.empresa.marcarAutoRecebiveisCartaoChequeCancelamento and
                        CancelamentoContratoControle.cancelamentoContratoVO.valorASerDevolvidoBaseCalculo >= 0}"/>
                        <h:outputText value="VALOR A SER DEVOLVIDO AO CLIENTE " styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold" rendered="#{CancelamentoContratoControle.contratoVO.empresa.marcarAutoRecebiveisCartaoChequeCancelamento and
                        CancelamentoContratoControle.cancelamentoContratoVO.valorASerDevolvidoBaseCalculo >= 0}"/>
                        <h:outputText value="VALOR A SER COBRADO DO CLIENTE " styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold" rendered="#{CancelamentoContratoControle.cancelamentoContratoVO.valorASerDevolvidoBaseCalculo < 0}"/>
                    </h:panelGroup>
                    <h:panelGroup>
                        <rich:spacer width="12"/>
                        <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{MovPagamentoControle.empresaLogado.moeda}  " />
                        <h:outputText id="valorDevolvido" value="#{CancelamentoContratoControle.cancelamentoContratoVO.valorASerDevolvidoBaseCalculo_Apresentar}" styleClass="#{CancelamentoContratoControle.cancelamentoContratoVO.definirCorCampoBase} texto-size-14-real texto-cor-cinza texto-font" >
                            <f:converter converterId="FormatadorNumerico" />
                        </h:outputText>
                    </h:panelGroup>
                
                    <h:panelGroup>
                        <h:selectBooleanCheckbox value="#{CancelamentoContratoControle.simular}">
                            <a4j:support event="onclick" 
                                         action="#{CancelamentoContratoControle.marcarSimular}"
                                         reRender="form"></a4j:support>
                        </h:selectBooleanCheckbox>
                        <h:outputText value="SIMULAR" styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold" ></h:outputText>
                    </h:panelGroup>
                    <h:panelGroup>
                        <rich:spacer width="12"/>
                        <h:outputText value="EM SIMULAÇÃO" styleClass="texto-size-14-real texto-font "
                                      style="color: #1668a2;"
                                      rendered="#{CancelamentoContratoControle.simular}"></h:outputText>
                    </h:panelGroup>
                    </c:if>
                </h:panelGrid>
                <h:panelGrid id="panelListaCheque" rendered="#{CancelamentoContratoControle.cancelamentoContratoVO.apresentaListaCheque && !CancelamentoContratoControle.contratoVO.cancelamentoAntecipado}" width="100%">
                    <h:outputText value="LISTA DE CHEQUES DO ÚLTIMO PAGAMENTO DO CLIENTE" styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold" />
                    <rich:dataTable id="listaCheque" width="100%" style="border: none;" rowClasses="tabela-rich-faces-sem-borda"
                                    value="#{CancelamentoContratoControle.cancelamentoContratoVO.listaPagamentos}" var="movPagamento">
                        <rich:column rendered="#{movPagamento.movPagamentoEscolhida and movPagamento.formaPagamento.tipoFormaPagamento == 'CH'}">
                            <rich:dataTable id="MovPagamentoCheque" headerClass=""
                                         styleClass="tabelaSimplesCustom"
                                            style="border: none;"
                            			 rowClasses="tablelistras textsmall" width="100%"
                            			 columnClasses="centralizado, centralizado,colunaEsquerda, colunaDireita, colunaDireita,colunaDireita, centralizado, colunaDireita"
                                         value="#{movPagamento.chequeVOs}" var="cheque">
                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText value="COMPENSAR" styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold" />
                                    </f:facet>
                                    <h:selectBooleanCheckbox id="compensarCheque" 
                                    disabled="#{CancelamentoContratoControle.cancelamentoContratoVO.quitacaoCancelamento}" 
                                    rendered="#{cheque.apresentarCheque  || CancelamentoContratoControle.simular}"  value="#{cheque.chequeCompensar}">
                                        <a4j:support event="onclick" action="#{CancelamentoContratoControle.compensarCheque}" 
                                                     reRender="valorDevolvido,panelListaCheque,panelLancaProdutoLiberacao,valorPago,labelValor,devolverCheque"/>
                                    </h:selectBooleanCheckbox>
                                </h:column>
                                <h:column >
                                    <f:facet name="header">
                                        <h:outputText value="DEVOLVER" styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold"/>
                                    </f:facet>
                                    <h:selectBooleanCheckbox id="devolverCheque" 
                                    			disabled="#{CancelamentoContratoControle.cancelamentoContratoVO.quitacaoCancelamento}" 
                                    			rendered="#{(cheque.apresentarCheque
                                                                    && (!cheque.temLote
                                                                                || CancelamentoContratoControle.retirarAutomatico
                                                                                || !ConfiguracaoFinanceiroControle.confFinanceiro.usarMovimentacaoContas)
                                                                    && !cheque.usadoPagarContaFinanceiro
                                                                    && !cheque.pagaOutroContrato) || CancelamentoContratoControle.simular}"  value="#{cheque.chequeEscolhido}">
                                        <a4j:support event="onclick" action="#{CancelamentoContratoControle.devolverCheque}" 
                                                     reRender="valorDevolvido,panelListaCheque,valorPago,labelValor,compensarCheque,panelLancaProdutoLiberacao,devolverCheque, panel"/>
                                    </h:selectBooleanCheckbox>

                                    <h:graphicImage id ="imgAdvertencia" rendered="#{cheque.avisoVinculos != '' && ((cheque.apresentarCheque
                                                                    && (!cheque.temLote
                                                                                || CancelamentoContratoControle.retirarAutomatico
                                                                                || !ConfiguracaoFinanceiroControle.confFinanceiro.usarMovimentacaoContas)
                                                                    && !cheque.usadoPagarContaFinanceiro
                                                                    && !cheque.pagaOutroContrato) || CancelamentoContratoControle.simular)}"
                                             url="imagens/atencaoRisco.gif" style="width:15px;height:15px;"
                                                            title="#{cheque.avisoVinculos}"/>
                                    
                                    <h:outputLink value="#{SuperControle.urlBaseConhecimento}como-faco-para-cancelar-um-plano-com-devolucao-de-valores/"
                                                  title="Clique e saiba mais: "
                                                  target="_blank"
                                                  rendered="#{cheque.apresentarCheque
                                                             && (cheque.temLote
                                                             && ConfiguracaoFinanceiroControle.confFinanceiro.usarMovimentacaoContas
                                                             && !CancelamentoContratoControle.retirarAutomatico)
                                                             && !cheque.usadoPagarContaFinanceiro
                                                             && !cheque.pagaOutroContrato
                                                             && !CancelamentoContratoControle.simular}">
                                        <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                                    </h:outputLink>
			          				 
                                    <h:graphicImage rendered="#{(cheque.situacao == 'CA' || cheque.situacao == 'DV') && !CancelamentoContratoControle.simular}" styleClass="linkWiki" url="/imagensCRM/informacao.gif"
                                                    title="Este cheque já foi devolvido"/>
                                    
                                    <h:graphicImage rendered="#{cheque.pagaOutroContrato && cheque.apresentarCheque
                                                               && !CancelamentoContratoControle.simular}" styleClass="linkWiki" url="/imagensCRM/informacao.gif"
                                                    title="Este cheque paga outro contrato."/>

                                    <h:outputLink value="#{SuperControle.urlWiki}Informa%E7%F5es_do_Cliente:Contrato:Opera%E7%F5es_Contrato:Afastamento:Cancelamento#Devolu.C3.A7.C3.A3o"
                                                  title="Clique e saiba mais: "
                                                  target="_blank"
                                                  rendered="#{cheque.apresentarCheque
                                                             && cheque.usadoPagarContaFinanceiro
                                                  && !CancelamentoContratoControle.simular}">
                                        <h:graphicImage styleClass="linkWiki" url="/imagensCRM/informacao.gif"
                                                        title="Este cheque não pode ser devolvido pois foi usado para pagar uma conta no financeiro. O código do lançamento é #{cheque.pagaContaFinanceiro}. Clique para mais informações."/>
                                    </h:outputLink>
                                </h:column>
                              
                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText value="BANCO" styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold"  />
                                    </f:facet>
                                    <h:outputText id="bancoPreenchido" value="#{cheque.banco.nome}" styleClass="texto-size-14-real texto-cor-cinza texto-font"/>
                                </h:column>
                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText value="AGÊNCIA" styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold"/>
                                    </f:facet>
                                    <h:outputText id="agencia" value="#{cheque.agencia}" styleClass="texto-size-14-real texto-cor-cinza texto-font" />
                                </h:column>
                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText value="CONTA" styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold"/>
                                    </f:facet>
                                    <h:outputText id="conta" value="#{cheque.conta}" styleClass="texto-size-14-real texto-cor-cinza texto-font" />
                                </h:column>
                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText value="N° CHEQUE" styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold"/>
                                    </f:facet>
                                    <h:outputText id="nDoc" value="#{cheque.numero}" styleClass="texto-size-14-real texto-cor-cinza texto-font"/>
                                </h:column>
                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText value="COMPENSAÇÃO" styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold"/>
                                    </f:facet>
                                    <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{cheque.dataCompensacao}">
                                        <f:convertDateTime pattern="dd/MM/yyyy" />
                                    </h:outputText>
                                </h:column>
                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText value="VALOR" styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold"/>
                                    </f:facet>
                                    <h:outputText id="valor" value="#{cheque.valor}" styleClass="texto-size-14-real texto-cor-cinza texto-font" >
                                        <f:converter converterId="FormatadorNumerico"/>
                                    </h:outputText>
                                </h:column>
                            </rich:dataTable>
                        </rich:column>
                    </rich:dataTable>
                </h:panelGrid>
                <h:panelGrid id="panelListaCartao" rendered="#{CancelamentoContratoControle.cancelamentoContratoVO.apresentaListaCartao && !CancelamentoContratoControle.contratoVO.cancelamentoAntecipado}">
                    <h:outputText value="Lista de Cartões do Último Pagamento do Cliente" styleClass="tituloCampos"/>
                    <rich:dataTable id="listaCartaoMovimento" width="100%" rowClasses="linhaImpar" columnClasses="colunaEsquerda"
                                    value="#{CancelamentoContratoControle.cancelamentoContratoVO.listaPagamentos}" var="movPagamento">
                        
                        <rich:column rendered="#{movPagamento.movPagamentoEscolhida and movPagamento.formaPagamento.tipoFormaPagamento == 'CA'}">
                            <h:dataTable id="MovPagamentoCartao" headerClass="consulta" rowClasses="tablelistras textsmall" width="100%"
                                         columnClasses="centralizado, centralizado,colunaEsquerda, colunaEsquerda, centralizado, colunaDireita"
                                         value="#{movPagamento.cartaoCreditoVOs}" var="cartao">
                                    <h:column>
                                        <f:facet name="header">
                                            <h:outputText value="Compensar"/>
                                        </f:facet>
                                        <h:selectBooleanCheckbox id="compensarCartao"
                                                                 disabled="#{CancelamentoContratoControle.cancelamentoContratoVO.quitacaoCancelamento}"
                                                                 rendered="#{(cartao.apresentarCartao || CancelamentoContratoControle.simular) && !cartao.devolverParcial}"  value="#{cartao.cartaoCompensar}">
                                            <a4j:support event="onclick" action="#{CancelamentoContratoControle.compensarCartao}"
                                                         reRender="valorDevolvido,panelListaCartao,panelLancaProdutoLiberacao,valorPago,labelValor,devolverCartao"/>
                                        </h:selectBooleanCheckbox>
                                    </h:column>
                                    <h:column>
                                        <f:facet name="header">
                                            <h:outputText value="Parcial*"/>
                                        </f:facet>
                                        <a4j:commandLink id="DevolverParcial"
                                                         action="#{CancelamentoContratoControle.selecionarCartaoEstornoParcial}"
                                               title="Parcial"
                                               rendered="#{((cartao.apresentarCartao
                                                                             && (!cartao.temLote
                                                                                        || !ConfiguracaoFinanceiroControle.confFinanceiro.usarMovimentacaoContas
                                                                                        || CancelamentoContratoControle.retirarAutomatico)
                                                                             && !cartao.pagaOutroContrato)|| CancelamentoContratoControle.simular) && !cartao.devolverParcial}"
                                               styleClass="linkAzul texto-size-14"
                                               style="margin-right: 20px;"
                                               reRender="mdlEstornoParcial"
                                               oncomplete="#{CancelamentoContratoControle.msgAlert}">
                                                Parcial
                                        </a4j:commandLink>
                                        <a4j:commandLink id="AlterarDevolucao"
                                              action="#{CancelamentoContratoControle.selecionarCartaoEstornoParcial}"
                                               title="Editar Estorno Parcial"
                                               rendered="#{((cartao.apresentarCartao
                                                                             && (!cartao.temLote
                                                                                        || !ConfiguracaoFinanceiroControle.confFinanceiro.usarMovimentacaoContas
                                                                                        || CancelamentoContratoControle.retirarAutomatico)
                                                                             && !cartao.pagaOutroContrato)|| CancelamentoContratoControle.simular) && cartao.devolverParcial}"
                                               styleClass="linkAzul texto-size-14"
                                               reRender="mdlEstornoParcial"
                                               oncomplete="#{CancelamentoContratoControle.msgAlert}">
                                            <i class="fa-icon-edit"></i>
                                            <h:outputText value="Editar"/>
                                               
                                        </a4j:commandLink>
                                                
                                        
                                    </h:column>
                                    <h:column>
                                        <f:facet name="header">
                                            <h:outputText value="Estornar*"/>
                                        </f:facet>
                                        <h:selectBooleanCheckbox id="devolverCartao" 
                                                                 disabled="#{CancelamentoContratoControle.cancelamentoContratoVO.quitacaoCancelamento}"
                                                                 rendered="#{((cartao.apresentarCartao
                                                                             && (!cartao.temLote
                                                                                        || !ConfiguracaoFinanceiroControle.confFinanceiro.usarMovimentacaoContas
                                                                                        || CancelamentoContratoControle.retirarAutomatico)
                                                                             && !cartao.pagaOutroContrato)|| CancelamentoContratoControle.simular) && !cartao.devolverParcial}"
                                                                 value="#{cartao.cartaoEscolhido}">
                                            <a4j:support event="onclick" action="#{CancelamentoContratoControle.devolverCartao}" oncomplete="#{CancelamentoContratoControle.mensagemNotificar}"
                                                         reRender="valorDevolvido,panelListaCartao,valorPago,labelValor,compensarCartao,panelLancaProdutoLiberacao"/>
                                        </h:selectBooleanCheckbox>

                                        <h:graphicImage id ="imgAdvertencia" rendered="#{cartao.avisoVinculos != '' &&( !cartao.apresentarCartao || ((cartao.apresentarCartao
                                                                             && (!cartao.temLote
                                                                                        || !ConfiguracaoFinanceiroControle.confFinanceiro.usarMovimentacaoContas
                                                                                        || CancelamentoContratoControle.retirarAutomatico)
                                                                             && !cartao.pagaOutroContrato)|| CancelamentoContratoControle.simular))}"
                                             url="imagens/atencaoRisco.gif" style="width:15px;height:15px;"
                                                            title="#{cartao.avisoVinculos}"/>

                                        
                                        <h:outputLink value="#{SuperControle.urlWiki}Informa%E7%F5es_do_Cliente:Contrato:Opera%E7%F5es_Contrato:Afastamento:Cancelamento#Devolu.C3.A7.C3.A3o"
                                                      title="Clique e saiba mais: "
                                                      target="_blank"
                                                      rendered="#{cartao.apresentarCartao
                                                                  && (cartao.temLote
							          && ConfiguracaoFinanceiroControle.confFinanceiro.usarMovimentacaoContas
                                                                  && !CancelamentoContratoControle.retirarAutomatico)
                                                                  && !cartao.pagaOutroContrato
                                                                  && !CancelamentoContratoControle.simular}">
                                            <h:graphicImage styleClass="linkWiki" url="/imagensCRM/informacao.gif"
                                                            title="Este cartão não pode ser editado ou excluído pois já está no lote #{cartao.lote.codigo}. Clique para mais informações."/>
                                        </h:outputLink>
                                        <h:graphicImage rendered="#{cartao.situacao == 'CA' &&  !CancelamentoContratoControle.simular}" styleClass="linkWiki" url="/imagensCRM/informacao.gif"
                                                        title="Esta parcela do cartão já foi estornada"/>
                                        <h:graphicImage rendered="#{cartao.pagaOutroContrato && cartao.apresentarCartao && !CancelamentoContratoControle.simular}" styleClass="linkWiki" url="/imagensCRM/informacao.gif"
                                                        title="Esta parcela do cartão paga outro contrato."/>
                                    </h:column>
                                     
                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText value="#{msg_aplic.prt_Finan_GestaoRecebiveis_nome}" />
                                    </f:facet>
                                    <h:outputText value="#{movPagamento.nomePagador}"/>
                                </h:column>
                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText value="#{msg_aplic.prt_OperadoraCartao_tituloForm}" />
                                    </f:facet>
                                    <h:outputText value="#{cartao.operadora.descricao}" />
                                </h:column>
                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText value="#{msg_aplic.prt_Finan_GestaoRecebiveis_dataCompensacao}" />
                                    </f:facet>
                                    <h:outputText value="#{cartao.dataCompensacao}">
                                        <f:convertDateTime pattern="dd/MM/yyyy" />
                                    </h:outputText>
                                </h:column>
                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText value="#{msg_aplic.prt_Finan_GestaoRecebiveis_Autorizacao}" />
                                    </f:facet>
                                    <h:outputText value="#{movPagamento.autorizacaoCartao}"/>
                                </h:column>
                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText value="#{msg_aplic.prt_Cheque_valor}" />
                                    </f:facet>
                                    <h:outputText value="#{cartao.valor}">
                                        <f:converter converterId="FormatadorNumerico" />
                                    </h:outputText>
                                </h:column>
                                <h:column rendered="cartao.devolverParcial">
                                    <f:facet name="header">
                                        <h:outputText value="Valor Estornado" />
                                    </f:facet>
                                    <h:outputText value="#{cartao.valorParcialDevolver}">
                                        <f:converter converterId="FormatadorNumerico" />
                                    </h:outputText>
                                </h:column>
                                <h:column rendered="cartao.devolverParcial">
                                    <f:facet name="header">
                                        <h:outputText value="#{msg_aplic.prt_Cheque_valor}" />
                                    </f:facet>
                                    <h:outputText value="#{cartao.valor}">
                                        <f:converter converterId="FormatadorNumerico" />
                                    </h:outputText>
                                </h:column>
                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText value="Valor Estornar" />
                                    </f:facet>
                                    <h:outputText rendered="#{cartao.devolverParcial}" style="color:red" value="#{cartao.valorParcialDevolver}">
                                        <f:converter converterId="FormatadorNumerico" />
                                    </h:outputText>
                                </h:column>
                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText value="Valor Final" />
                                    </f:facet>
                                    <h:outputText rendered="#{cartao.devolverParcial}" style="color: green;" value="#{cartao.valorFinalParcial}">
                                        <f:converter converterId="FormatadorNumerico" />
                                    </h:outputText>
                                </h:column>
                                <f:facet  name="footer">
                                    <h:panelGrid columns="3">
                                        <h:panelGroup>
                                        <h:outputText value="valor pago por esse contrato: "/>
                                        <h:outputText style="color: green;" value="#{movPagamento.valorPagaContrato}">
                                            <f:converter converterId="FormatadorNumerico" />
                                        </h:outputText>
                                        </h:panelGroup>
                                        <rich:spacer width="5"/>
                                        <h:panelGroup>
                                        <h:outputText value="valor selecionado para estorno: "/>
                                        <h:outputText style="color: red;" value="#{movPagamento.valorEstornado}">
                                            <f:converter converterId="FormatadorNumerico" />
                                        </h:outputText>
                                        </h:panelGroup>
                                    </h:panelGrid>    
                                </f:facet>            
                            </h:dataTable>
                        </rich:column>
                    </rich:dataTable>
                </h:panelGrid>
                 <h:panelGrid id="panelListaChequeMovimento" rendered="#{CancelamentoContratoControle.cancelamentoContratoVO.apresentaListaChequeMovimento}" width="100%">
                    <h:outputText value="Lista de Cheques vinculados a conta corrente" styleClass="tituloCampos" />
                    <rich:dataTable id="listaChequeMovimento" width="100%" rowClasses="linhaImpar" columnClasses="colunaEsquerda"
                                    value="#{CancelamentoContratoControle.cancelamentoContratoVO.listaPagamentosMovimento}" var="movPagamento">
                        <rich:column rendered="#{movPagamento.movPagamentoEscolhida and movPagamento.formaPagamento.tipoFormaPagamento == 'CH'}">
                            <h:dataTable id="MovPagamentoChequeMovimento" headerClass="consulta"
                            			 rowClasses="tablelistras textsmall" width="100%"
                            			 columnClasses="centralizado, centralizado,colunaEsquerda, colunaDireita, colunaDireita,colunaDireita, centralizado, colunaDireita"
                                         value="#{movPagamento.chequeVOs}" var="cheque">
                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText value="Compensar" />
                                    </f:facet>
                                    <h:selectBooleanCheckbox id="compensarCheque"
                                    disabled="#{CancelamentoContratoControle.cancelamentoContratoVO.quitacaoCancelamento}"
                                    rendered="#{cheque.apresentarCheque  || CancelamentoContratoControle.simular}"  value="#{cheque.chequeCompensar}">
                                        <a4j:support event="onclick" action="#{CancelamentoContratoControle.compensarCheque}"
                                                     reRender="valorDevolvido,panelListaChequeMovimento,panelLancaProdutoLiberacao,valorPago,labelValor"/>
                                    </h:selectBooleanCheckbox>
                                </h:column>
                                <h:column >
                                    <f:facet name="header">
                                        <h:outputText value="Devolver" />
                                    </f:facet>
                                    <h:selectBooleanCheckbox id="devolverCheque"
                                    			disabled="#{CancelamentoContratoControle.cancelamentoContratoVO.quitacaoCancelamento}"
                                    			rendered="#{(cheque.apresentarCheque
                                                                    && (!cheque.temLote
                                                                                || CancelamentoContratoControle.retirarAutomatico
                                                                                || !ConfiguracaoFinanceiroControle.confFinanceiro.usarMovimentacaoContas)
                                                                    && !cheque.usadoPagarContaFinanceiro
                                                                    && !cheque.pagaOutroContrato
                                                                    && !cheque.temComposicao) || CancelamentoContratoControle.simular}"  value="#{cheque.chequeEscolhido}">
                                        <a4j:support event="onclick" action="#{CancelamentoContratoControle.devolverCheque}"
                                                     reRender="valorDevolvido,panelListaChequeMovimento,valorPago,labelValor,panelLancaProdutoLiberacao"/>
                                    </h:selectBooleanCheckbox>

                                    <h:outputLink value="#{SuperControle.urlWiki}Informa%E7%F5es_do_Cliente:Contrato:Opera%E7%F5es_Contrato:Afastamento:Cancelamento#Devolu.C3.A7.C3.A3o"
                                                  title="Clique e saiba mais: "
                                                  target="_blank"
                                                  rendered="#{cheque.apresentarCheque
                                                             && (cheque.temLote
                                                             && ConfiguracaoFinanceiroControle.confFinanceiro.usarMovimentacaoContas
                                                             && !CancelamentoContratoControle.retirarAutomatico)
                                                             && !cheque.usadoPagarContaFinanceiro
                                                             && !cheque.pagaOutroContrato
                                                             && !CancelamentoContratoControle.simular}">
                                        <h:graphicImage styleClass="linkWiki" url="/imagensCRM/informacao.gif"
                                                        title="Este cheque não pode ser devolvido pois já está no lote #{cheque.loteVO.codigo}. Clique para mais informações."/>
                                    </h:outputLink>

                                    <h:graphicImage rendered="#{cheque.situacao == 'CA' && !CancelamentoContratoControle.simular}" styleClass="linkWiki" url="/imagensCRM/informacao.gif"
                                                    title="Este cheque já foi devolvido"/>
                                    <h:graphicImage rendered="#{cheque.situacao != 'CA' && !cheque.pagaOutroContrato
                                                                && cheque.temComposicao
                                                                && !cheque.temLote
                                                                && !CancelamentoContratoControle.simular}" styleClass="linkWiki" url="/imagensCRM/informacao.gif"
                                                    title="Este cheque não pode ser devolvido pois está na conta corrente do cliente."/>

                                    <h:graphicImage rendered="#{cheque.pagaOutroContrato && cheque.apresentarCheque
                                                               && !CancelamentoContratoControle.simular}" styleClass="linkWiki" url="/imagensCRM/informacao.gif"
                                                    title="Este cheque paga outro contrato."/>

                                    <h:outputLink value="#{SuperControle.urlWiki}Informa%E7%F5es_do_Cliente:Contrato:Opera%E7%F5es_Contrato:Afastamento:Cancelamento#Devolu.C3.A7.C3.A3o"
                                                  title="Clique e saiba mais: "
                                                  target="_blank"
                                                  rendered="#{cheque.apresentarCheque
                                                             && cheque.usadoPagarContaFinanceiro
                                                  && !CancelamentoContratoControle.simular}">
                                        <h:graphicImage styleClass="linkWiki" url="/imagensCRM/informacao.gif"
                                                        title="Este cheque não pode ser devolvido pois foi usado para pagar uma conta no financeiro. O código do lançamento é #{cheque.pagaContaFinanceiro}. Clique para mais informações."/>
                                    </h:outputLink>
                                </h:column>

                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText value="Banco"  />
                                    </f:facet>
                                    <h:outputText id="bancoPreenchido" value="#{cheque.banco.nome}" styleClass="form"/>
                                </h:column>
                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText value="Agência" />
                                    </f:facet>
                                    <h:outputText id="agencia" value="#{cheque.agencia}" styleClass="form" />
                                </h:column>
                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText value="Conta"/>
                                    </f:facet>
                                    <h:outputText id="conta" value="#{cheque.conta}" styleClass="form" />
                                </h:column>
                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText value="N° Cheque"/>
                                    </f:facet>
                                    <h:outputText id="nDoc" value="#{cheque.numero}" styleClass="form"/>
                                </h:column>
                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText value="Compensação"/>
                                    </f:facet>
                                    <h:outputText value="#{cheque.dataCompensacao}">
                                        <f:convertDateTime pattern="dd/MM/yyyy" />
                                    </h:outputText>
                                </h:column>
                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText value="Valor"/>
                                    </f:facet>
                                    <h:outputText id="valor" value="#{cheque.valor}" styleClass="form" >
                                        <f:converter converterId="FormatadorNumerico"/>
                                    </h:outputText>
                                </h:column>
                            </h:dataTable>
                        </rich:column>
                    </rich:dataTable>
                </h:panelGrid>
                <h:panelGrid id="panelListaCartaoMovimento" rendered="#{CancelamentoContratoControle.cancelamentoContratoVO.apresentaListaCartaoMovimento}">
                    <h:outputText value="Lista de Cartões vinculadas a conta corrente do aluno" styleClass="tituloCampos"/>
                    <rich:dataTable id="listaCartao" width="100%" rowClasses="linhaImpar" columnClasses="colunaEsquerda"
                                    value="#{CancelamentoContratoControle.cancelamentoContratoVO.listaPagamentosMovimento}" var="movPagamento">
                        <rich:column rendered="#{movPagamento.movPagamentoEscolhida and movPagamento.formaPagamento.tipoFormaPagamento == 'CA'}">
                            <h:dataTable id="MovPagamentoCartaoMovimento" headerClass="consulta" rowClasses="tablelistras textsmall" width="100%"
                                         columnClasses="centralizado, centralizado,colunaEsquerda, colunaEsquerda, centralizado, colunaDireita"
                                         value="#{movPagamento.cartaoCreditoVOs}" var="cartao">
                                    <h:column>
                                        <f:facet name="header">
                                            <h:outputText value="Compensar"/>
                                        </f:facet>
                                        <h:selectBooleanCheckbox id="compensarCartao"
                                                                 disabled="#{CancelamentoContratoControle.cancelamentoContratoVO.quitacaoCancelamento}"
                                                                 rendered="#{cartao.apresentarCartao || CancelamentoContratoControle.simular}"  value="#{cartao.cartaoCompensar}">
                                            <a4j:support event="onclick" action="#{CancelamentoContratoControle.compensarCartao}"
                                                         reRender="valorDevolvido,panelListaCartaoMovimento,panelLancaProdutoLiberacao,valorPago,labelValor,devolverCartao"/>
                                        </h:selectBooleanCheckbox>
                                    </h:column>
                                    <h:column>
                                        <f:facet name="header">
                                            <h:outputText value="Estornar*"/>
                                        </f:facet>
                                        <h:selectBooleanCheckbox id="devolverCartao"
                                                                 disabled="#{CancelamentoContratoControle.cancelamentoContratoVO.quitacaoCancelamento}"
                                                                 rendered="#{(cartao.apresentarCartao
                                                                             && (!cartao.temLote
                                                                                        || !ConfiguracaoFinanceiroControle.confFinanceiro.usarMovimentacaoContas
                                                                                        || CancelamentoContratoControle.retirarAutomatico)
                                                                             && !cartao.pagaOutroContrato
                                                                             && !cartao.temComposicao)|| CancelamentoContratoControle.simular}"
                                                                 value="#{cartao.cartaoEscolhido}">
                                            <a4j:support event="onclick" action="#{CancelamentoContratoControle.devolverCartao}"
                                                         reRender="valorDevolvido,panelListaCartaoMovimento,valorPago,labelValor,compensarCartao,panelLancaProdutoLiberacao"/>
                                        </h:selectBooleanCheckbox>

                                        <h:outputLink value="#{SuperControle.urlWiki}Informa%E7%F5es_do_Cliente:Contrato:Opera%E7%F5es_Contrato:Afastamento:Cancelamento#Devolu.C3.A7.C3.A3o"
                                                      title="Clique e saiba mais: "
                                                      target="_blank"
                                                      rendered="#{cartao.apresentarCartao
                                                                  && (cartao.temLote
							          && ConfiguracaoFinanceiroControle.confFinanceiro.usarMovimentacaoContas
                                                                  && !CancelamentoContratoControle.retirarAutomatico)
                                                                  && !cartao.pagaOutroContrato
                                                                  && !CancelamentoContratoControle.simular}">
                                            <h:graphicImage styleClass="linkWiki" url="/imagensCRM/informacao.gif"
                                                            title="Este cartão não pode ser editado ou excluído pois já está no lote #{cartao.lote.codigo}. Clique para mais informações."/>
                                        </h:outputLink>
                                        <h:graphicImage rendered="#{cartao.situacao == 'CA' &&  !CancelamentoContratoControle.simular}" styleClass="linkWiki" url="/imagensCRM/informacao.gif"
                                                        title="Esta parcela do cartão já foi estornada"/>
                                        <h:graphicImage rendered="#{cartao.pagaOutroContrato && cartao.apresentarCartao && !CancelamentoContratoControle.simular}" styleClass="linkWiki" url="/imagensCRM/informacao.gif"
                                                        title="Esta parcela do cartão paga outro contrato."/>
                                        <h:graphicImage rendered="#{cartao.situacao != 'CA' && !cartao.pagaOutroContrato
                                                                    && cartao.temComposicao && !CancelamentoContratoControle.simular}" styleClass="linkWiki" url="/imagensCRM/informacao.gif"
                                                    title="Esta parcela do cartão não pode ser devolvida pois está na conta corrente do cliente."/>
                                    </h:column>
                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText value="#{msg_aplic.prt_Finan_GestaoRecebiveis_nome}" />
                                    </f:facet>
                                    <h:outputText value="#{movPagamento.nomePagador}"/>
                                </h:column>
                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText value="#{msg_aplic.prt_OperadoraCartao_tituloForm}" />
                                    </f:facet>
                                    <h:outputText value="#{cartao.operadora.descricao}" />
                                </h:column>
                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText value="#{msg_aplic.prt_Finan_GestaoRecebiveis_dataCompensacao}" />
                                    </f:facet>
                                    <h:outputText value="#{cartao.dataCompensacao}">
                                        <f:convertDateTime pattern="dd/MM/yyyy" />
                                    </h:outputText>
                                </h:column>
                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText value="#{msg_aplic.prt_Finan_GestaoRecebiveis_Autorizacao}" />
                                    </f:facet>
                                    <h:outputText value="#{movPagamento.autorizacaoCartao}"/>
                                </h:column>
                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText value="#{msg_aplic.prt_Cheque_valor}" />
                                    </f:facet>
                                    <h:outputText value="#{cartao.valor}">
                                        <f:converter converterId="FormatadorNumerico" />
                                    </h:outputText>
                                </h:column>
                                <h:column rendered="#{cartao.devolverParcial}">
                                    <f:facet name="header">
                                        <h:outputText value="Valor Estornar" />
                                    </f:facet>
                                    <h:outputText style="color: #FF5555;" value="#{cartao.valorParcialDevolver}">
                                        <f:converter converterId="FormatadorNumerico" />
                                    </h:outputText>
                                </h:column>
                                <h:column rendered="#{cartao.devolverParcial}">
                                    <f:facet name="header">
                                        <h:outputText value="Valor Final" />
                                    </f:facet>
                                    <h:outputText  style="color: #FF5555;" value="#{cartao.valorFinalParcial}">
                                        <f:converter converterId="FormatadorNumerico" />
                                    </h:outputText>
                                </h:column>     
                            </h:dataTable>
                        </rich:column>
                    </rich:dataTable>
                </h:panelGrid>
                <h:panelGrid id="gridMensagemEstornoCartao" rendered="#{(CancelamentoContratoControle.cancelamentoContratoVO.apresentaListaCartao || CancelamentoContratoControle.cancelamentoContratoVO.apresentaListaCartaoMovimento) && !CancelamentoContratoControle.contratoVO.cancelamentoAntecipado}">
                    <h:outputText id="msgEstornoCartao" value="* Se for necessário estornar algum pagamento com cartão de crédito tenha em mãos o CÓDIGO DE AUTORIZAÇÃO da operação.
                                  Em seguida, ENTRE EM CONTATO com a operadora de cartão de crédito para estornar as parcelas que ainda não entraram na conta da empresa." styleClass="texto-size-14-real texto-cor-cinza texto-font red"/>
                </h:panelGrid>
                <h:panelGrid id="panelLancaProdutoLiberacao" columns="1" width="100%"
                             rendered="#{!CancelamentoContratoControle.contratoVO.cancelamentoAntecipado}">

                    <h:panelGroup rendered="#{CancelamentoContratoControle.permiteAlterarCancelamento and !CancelamentoContratoControle.cancelamentoContratoVO.configuracaoSesc}">
                        <h:selectBooleanCheckbox id="alterarTipoCancelamento" value="#{CancelamentoContratoControle.alterarCancelamento}">
                            <a4j:support event="onclick" reRender="panelAutorizacaoFuncionalidade, panelLancaProdutoLiberacao"
                                         action="#{CancelamentoContratoControle.alterarTipoCancelamento}"/>
                        </h:selectBooleanCheckbox>
                        <rich:spacer width="10px"/>
                        <h:outputText value="DEVOLVER AO CLIENTE"
                                      rendered="#{CancelamentoContratoControle.cancelamentoContratoVO.apresentarPanelLancaProdutoLiberacao}"
                                      styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold"/>
                        <h:panelGroup layout="block" style="padding: 0px 30px 0px;"
                                        rendered="#{CancelamentoContratoControle.cancelamentoContratoVO.apresentarPanelLancaProdutoLiberacao}">
                              <h:outputText
                                      value="A indicação do sistema é que seja gerada uma cobrança para quitação do cancelamento. Caso não concorde, esta opção permite que seja feita uma devolução em dinheiro, ao invés da cobrança."
                                      styleClass="classInfCadUsuario"/>
                        </h:panelGroup>
                        <h:outputText value="COBRAR DO CLIENTE"
                                      rendered="#{!CancelamentoContratoControle.cancelamentoContratoVO.apresentarPanelLancaProdutoLiberacao}"
                                      styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold"/>
                        <h:panelGroup layout="block" style="padding: 0px 30px 0px;"
                                        rendered="#{!CancelamentoContratoControle.cancelamentoContratoVO.apresentarPanelLancaProdutoLiberacao}">
                              <h:outputText
                                      value="A indicação do sistema é que seja realizada uma devolução em dinheiro. Caso não concorde, esta opção permitirá que uma cobrança de valores seja gerada, ao invés da devolução."
                                      styleClass="classInfCadUsuario"/>
                        </h:panelGroup>
                    </h:panelGroup>

                    <h:panelGroup rendered="#{!CancelamentoContratoControle.alterarCancelamento}">
                        <h:panelGroup rendered="#{!CancelamentoContratoControle.cancelamentoContratoVO.apresentarPanelLancaProdutoLiberacao}">
                            <h:selectBooleanCheckbox id="liberacaoDevolucao" value="#{CancelamentoContratoControle.cancelamentoContratoVO.liberacaoDevolucao}">
                                <a4j:support event="onclick" reRender="panelAutorizacaoFuncionalidade, panelLancaProdutoLiberacao"
                                             action="#{CancelamentoContratoControle.obterLiberacaoDevolucaoCancelamento}"/>
                            </h:selectBooleanCheckbox>
                            <rich:spacer width="10px"/>
                            <h:outputText value="NÃO DEVOLVER AO CLIENTE" styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold"/>
                            <h:panelGroup layout="block" style="padding: 0px 30px 0px;">
                                <h:outputText
                                      value="Esta opção permite que o sistema não realize a devolução em dinheiro do valor sugerido, retendo-o na academia, e não realizando lançamentos de saída."
                                      styleClass="classInfCadUsuario"/>
                            </h:panelGroup>
                        </h:panelGroup>
                    </h:panelGroup>

                    <h:panelGroup rendered="#{!CancelamentoContratoControle.alterarCancelamento and !CancelamentoContratoControle.cancelamentoContratoVO.configuracaoSesc}">
                        <h:panelGroup rendered="#{CancelamentoContratoControle.cancelamentoContratoVO.liberacaoDevolucao}" styleClass="tablepreviewtotal">
                            <h:outputText value="VALOR A SER RETIDO: " styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold"/>
                            <rich:spacer width="10px"/>
                            <h:outputText id="msgRetiradaDinheiro" value="#{CancelamentoContratoControle.cancelamentoContratoVO.valorASerDevolvidoBaseCalculoMonetario}" styleClass="texto-size-14-real texto-cor-cinza texto-font"/>
                        </h:panelGroup>
                    </h:panelGroup>

                    <h:panelGroup rendered="#{!CancelamentoContratoControle.alterarCancelamento and !CancelamentoContratoControle.cancelamentoContratoVO.configuracaoSesc}">
                        <h:panelGroup rendered="#{!CancelamentoContratoControle.cancelamentoContratoVO.apresentarPanelLancaProdutoLiberacao}">
                            <h:selectBooleanCheckbox id="devolucaoManual" value="#{CancelamentoContratoControle.cancelamentoContratoVO.devolucaoManualCancelamento}">
                                <a4j:support event="onclick" reRender="panelAutorizacaoFuncionalidade, panelLancaProdutoLiberacao"
                                             action="#{CancelamentoContratoControle.obterDevolucaoManualCancelamento}"/>
                            </h:selectBooleanCheckbox>
                            <rich:spacer width="10px"/>
                            <h:outputText value="DEVOLUÇÃO MANUAL DO CANCELAMENTO" styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold" />
                            <h:panelGroup layout="block" style="padding: 0px 30px 0px;">
                                <h:outputText
                                      value="Esta opção permite que seja informado um valor diferente do valor calculado para a devolução em dinheiro."
                                      styleClass="classInfCadUsuario"/>
                            </h:panelGroup>
                        </h:panelGroup>
                    </h:panelGroup>

                    <h:panelGroup rendered="#{CancelamentoContratoControle.cancelamentoContratoVO.devolucaoManualCancelamento}" styleClass="tablepreviewtotal">
                        <h:outputText value="INFORME O VALOR A SER DEVOLVIDO EM DINHEIRO: " styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold"/>
                        <rich:spacer width="10px"/>
                        <h:inputText id="valorDevolucao" value="#{CancelamentoContratoControle.cancelamentoContratoVO.valorDevolucaoCancelamento}"
                                     size="8" onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="inputTextClean">
                            <f:converter converterId="FormatadorNumerico"/>
                        </h:inputText>
                    </h:panelGroup>

                    <h:panelGrid columns="1" width="100%" rendered="#{CancelamentoContratoControle.cancelamentoContratoVO.apresentarPanelLancaProdutoLiberacao}">
                        <h:panelGroup rendered="#{!CancelamentoContratoControle.alterarCancelamento}">
                            <h:selectBooleanCheckbox id="liberacaoCancelamento" value="#{CancelamentoContratoControle.cancelamentoContratoVO.liberacaoCancelamento}">
                                    <a4j:support event="onclick"
                                                 action="#{CancelamentoContratoControle.obterLiberacaoCancelamento}"
                                                 reRender="panelAutorizacaoFuncionalidade, quitacaoCancelamento, quitacaoManual, groupProduto, groupProduto1, panelListaCheque"/>
                                </h:selectBooleanCheckbox>
                                <rich:spacer width="10px"/>
                                <h:outputText value="NÃO COBRAR DO CLIENTE" styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold" />
                                <h:panelGroup layout="block" style="padding: 0px 30px 0px;">
                                    <h:outputText
                                      value="Marcando esta opção, o aluno será isento de cobrança da quitação do cancelamento."
                                      styleClass="classInfCadUsuario"/>
                                </h:panelGroup>
                        </h:panelGroup>
                        <h:panelGroup rendered="#{!CancelamentoContratoControle.alterarCancelamento}">
                                <h:selectBooleanCheckbox id="quitacaoCancelamento" value="#{CancelamentoContratoControle.cancelamentoContratoVO.quitacaoCancelamento}">
                                    <a4j:support event="onclick" action="#{CancelamentoContratoControle.obterQuitacaoCancelamento}"
                                                 reRender="groupProduto, groupProduto1,panelListaCheque,liberacaoCancelamento, quitacaoManual"/>
                                </h:selectBooleanCheckbox>
                                <rich:spacer width="10px"/>
                                <h:outputText value="QUITAÇÃO DO CANCELAMENTO" styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold"/>
                                <h:panelGroup layout="block" style="padding: 0px 30px 0px;">
                                <h:outputText
                                      value="Marque esta opção caso concorde com o valor calculado pelo sistema para a realização da cobrança"
                                      styleClass="classInfCadUsuario"/>
                                </h:panelGroup>
                                
                                <rich:spacer width="20px"/>
                                
                        </h:panelGroup>

                        <h:panelGroup rendered="#{!CancelamentoContratoControle.alterarCancelamento}">
                            <h:panelGroup>
                                <h:selectBooleanCheckbox id="quitacaoManual" value="#{CancelamentoContratoControle.cancelamentoContratoVO.quitacaoManualCancelamento}">
                                    <a4j:support event="onclick" reRender="panelAutorizacaoFuncionalidade, quitacaoCancelamento, liberacaoCancelamento, groupProduto, groupProduto1, panelListaCheque"
                                                 action="#{CancelamentoContratoControle.obterQuitacaoManualCancelamento}"/>
                                </h:selectBooleanCheckbox>
                                <rich:spacer width="10px"/>
                                <h:outputText value="QUITAÇÃO MANUAL DO CANCELAMENTO" styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold" />
                                <h:panelGroup layout="block" style="padding: 0px 30px 0px;">
                                    <h:outputText
                                      value="Esta opção permite que seja informado um valor diferente do valor calculado para a quitação do cancelamento."  
                                      styleClass="classInfCadUsuario"/>
                                </h:panelGroup>
                            </h:panelGroup>
                        </h:panelGroup>

                        <f:verbatim>
                            <h:outputText value="   " styleClass="tituloCampos" style="font-weight: bold;"/>
                            <h:outputText value="   " styleClass="tituloCampos" style="font-weight: bold;"/>
                            <h:outputText value="   " styleClass="tituloCampos" style="font-weight: bold;"/>
                        </f:verbatim>
                        <h:panelGroup id="groupProduto">
                            <h:panelGroup layout="block" rendered="#{CancelamentoContratoControle.cancelamentoContratoVO.quitacaoCancelamento}" styleClass="tablepreviewtotal" style="margin-top: 12px">
                                <h:outputText value="VALOR A SER COBRADO DO CLIENTE: " styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold"/>
                                <rich:spacer width="10px"/>
                                <h:outputText id="valorQuitacaoVermelho"
                                              value="#{CancelamentoContratoControle.cancelamentoContratoVO.valorQuitacaoCancelamento_Apresentar}"
                                              styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold" style="color: red;">
                                </h:outputText>
                            </h:panelGroup>
                            <br/>
                            <hr style="margin-top: 10px"/>
                            <h:panelGroup layout="block" rendered="#{CancelamentoContratoControle.apresentarMensagemTipoParcelaCancelamento}" styleClass="tablepreviewtotal" style="margin-top: 12px" >
                                <h:outputText value="VALOR ZERADO DEVIDO AS PARCELAS QUE FICARÃO EM ABERTO SER MAIOR QUE O VALOR DA QUITAÇÃO" styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold"/>
                            </h:panelGroup>
                            <h:panelGroup layout="block" rendered="#{CancelamentoContratoControle.apresentarMensagemZerarValorCancelamento}" styleClass="tablepreviewtotal" style="margin-top: 12px" >
                                <h:outputText value="VALOR DA QUITAÇÃO ZERADO DEVIDO A CONFIGURAÇÃO DE CANCELAMENTO DA EMPRESA" styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold"/>
                            </h:panelGroup>
                        </h:panelGroup>
                    </h:panelGrid>

                    <h:panelGrid columns="1" width="100%">
                        <h:panelGroup id="groupProduto1">
                            <h:panelGrid columns="2"
                                    rendered="#{CancelamentoContratoControle.cancelamentoContratoVO.quitacaoManualCancelamento}"
                                    styleClass="tablepreviewtotal">
                                <h:outputText value="INFORME O VALOR A SER COBRADO:" styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold"/>
                                <h:inputText id="valorQuitacao"
                                             value="#{CancelamentoContratoControle.cancelamentoContratoVO.valorQuitacaoCancelamento}"
                                             size="8" onblur="blurinput(this);" onfocus="focusinput(this);"
                                             styleClass="inputTextClean"
                                             disabled="#{CancelamentoContratoControle.cancelamentoContratoVO.quitacaoCancelamento}">
                                    <f:converter converterId="FormatadorNumerico"/>
                                </h:inputText>
                            </h:panelGrid>
                        </h:panelGroup>

                    </h:panelGrid>
                </h:panelGrid>
                <h:panelGrid width="550px" columns="2">
                    <h:panelGrid width="350px"/>
                    <h:panelGrid width="150px">
                        <h:panelGroup>
                            <h:commandLink id="voltar" title="Voltar Passo"
                                             action="#{CancelamentoContratoControle.voltarTelaCancelamento}"
                                             styleClass="pure-button">
                                <i class="fa-icon-arrow-left"></i>
                            </h:commandLink>
                            <rich:spacer width="7"/>
                            <a4j:commandLink id="proximo"
                                               action="#{CancelamentoContratoControle.finalizarCancelamentoComCheque}"
                                               title="Próximo Passo"
                                               styleClass="pure-button pure-button-primary"
                                               reRender="panel, panelAutorizacaoFuncionalidade, formModalRetirada"
                                               oncomplete="#{CancelamentoContratoControle.mensagemNotificar}#{CancelamentoContratoControle.onCompleteQuitacao}"
                                               rendered="#{!CancelamentoContratoControle.simular}">
                                <i class="fa-icon-arrow-right"></i>
                            </a4j:commandLink>
                            <h:commandButton id="proximoHide" action="finalizarCancelamento" style="display:none;">
                            </h:commandButton>
                        </h:panelGroup>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>

        </h:panelGrid>
    </h:form>


<%@include file="/includes/include_modal_retiradaAutomatica.jsp" %>
<%@include file="/pages/finan/includes/include_modal_abrirCaixa.jsp" %>
<%@include file="includes/autorizacao/include_autorizacao_funcionalidade.jsp" %>
<rich:modalPanel id="mdlEstornoParcial" autosized="true" shadowOpacity="true" width="550" height="120"
                 styleClass="novaModal"
                 onshow="document.getElementById('formDeposito:descricaoDeposito').focus();">

    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="Estorno Parcial"/>
        </h:panelGroup>
    </f:facet>

    <a4j:form id="formEstorParcial" ajaxSubmit="true" >

        <div style="margin:10px 0 0 0;"></div>

        <rich:spacer width="63"/>
        <h:panelGroup rendered="#{CancelamentoContratoControle.apresentarDadosEstornoParcial}"> 
            <h:panelGrid id="gridDadosEstorno"  columns="2" width="100%" columnClasses="centralizado" style="font-size: 12px">
                <h:outputText 
                              styleClass="tituloCampos" 
                              value="Parcela:"/>
                <h:outputText  styleClass="tituloCampos" style="font-weight: normal;"
                    value="#{CancelamentoContratoControle.cartaoDevolucao.nrParcela}/#{CancelamentoContratoControle.movPagamentoVO.nrVezes}"/>
                <h:outputText  styleClass="tituloCampos" value="Data Compensação:"/>
                <h:outputText styleClass="tituloCampos" style="font-weight: normal;"  value="#{CancelamentoContratoControle.cartaoDevolucao.dataCompensacao}">
                    <f:convertDateTime pattern="dd/MM/yyyy" />
                </h:outputText>
                <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Finan_GestaoRecebiveis_Autorizacao}" />
                <h:outputText styleClass="tituloCampos" style="font-weight: normal;" value="#{CancelamentoContratoControle.movPagamentoVO.autorizacaoCartao}"/>
                <h:outputText styleClass="tituloCampos"  value="Valor:"/>
                <h:outputText  styleClass="tituloCampos" style="font-weight: normal;"  value="#{CancelamentoContratoControle.cartaoDevolucao.valor}">
                    <f:converter converterId="FormatadorNumerico" />
                </h:outputText>
                <h:outputText  styleClass="tituloCampos"  value="Valor estornar:"/>
                <h:inputText id="valorEstornar" style="width: 90px;" maxlength="40" onfocus="focusinput(this);"
                             styleClass="form" value="#{CancelamentoContratoControle.cartaoDevolucao.valorParcialDevolverCalculo}"
                            title="Valor a ser estornado da parcela.">
                    <a4j:support event="onchange"
                            focus="movPagamentoEscolhido"
                            action="#{CancelamentoContratoControle.atualizarValorParcial}"
                            reRender="formEstorParcial" />
                   <f:converter converterId="FormatadorNumerico"/>
                </h:inputText>
                <h:outputText  styleClass="tituloCampos"  value="Novo Valor:"/>
                <h:inputText id="valorNovo" style="width: 90px;" maxlength="40" onfocus="focusinput(this);"
                            readonly="true"
                            styleClass="form" value="#{CancelamentoContratoControle.cartaoDevolucao.valorFinalParcialCalculo}"
                            title="Novo Valor após a conclusão da operação">
                   <f:converter converterId="FormatadorNumerico"/>
               </h:inputText>
            </h:panelGrid>
            <rich:spacer height="10"/> 
            <h:panelGrid columns="1" width="100%" columnClasses="centralizado">
                <h:panelGroup>
                    <a4j:commandLink action="#{CancelamentoContratoControle.apresentarOpcoesPropagacao}"
                                       reRender="form,formEstorParcial"
                                       id="btnConfirmaOperacao"
                                       oncomplete="#{CancelamentoContratoControle.msgAlert}"
                                       styleClass="pure-button pure-button-primary">
                        OK
                    </a4j:commandLink>
                    <rich:spacer width="10"/>    
                    <a4j:commandLink action="#{CancelamentoContratoControle.apresentarOpcoesPropagacaoCancelando}"
                                       reRender="form,formEstorParcial"
                                       rendered="#{CancelamentoContratoControle.cartaoDevolucao.devolverParcial}"
                                       id="btnCancelarDevolucao"
                                       oncomplete="#{CancelamentoContratoControle.msgAlert}"
                                       styleClass="pure-button">
                        Cancelar Estorno
                    </a4j:commandLink>    
                    <rich:spacer width="10"/>
                    <a4j:commandLink 
                            action="#{CancelamentoContratoControle.cancelarAlteracaoParcial}"
                                     styleClass="pure-button"
                                     oncomplete="#{CancelamentoContratoControle.msgAlert}">
                        Fechar
                    </a4j:commandLink>
                </h:panelGroup>
            </h:panelGrid>
        </h:panelGroup>
        <h:panelGroup rendered="#{!CancelamentoContratoControle.apresentarDadosEstornoParcial}"> 
            <h:panelGrid cellpadding="20" id="gridDadosEstornoDemais"  columns="1" width="100%" columnClasses="centralizado"  style="font-size: 12px">
                <h:outputText styleClass="tituloCampos" value="Aplicar alterações para as próximas parcelas?" />
            </h:panelGrid>
            <h:panelGrid columns="1" width="100%" columnClasses="centralizado">
                <h:panelGroup>
                    <a4j:commandLink action="#{CancelamentoContratoControle.aplicarEstornoParcialDemaisParcelas}"
                                       reRender="form,formEstorParcial"
                                       id="btnConfirmaDemais"
                                       oncomplete="#{CancelamentoContratoControle.msgAlert}"
                                       styleClass="pure-button pure-button-primary">
                        Sim
                    </a4j:commandLink>
                    <rich:spacer width="10"/>
                    <a4j:commandLink  action="#{CancelamentoContratoControle.aplicarEstornoParcialParcelaSelecionada}"
                                       reRender="form,formEstorParcial"
                                     styleClass="pure-button"
                                      oncomplete="#{CancelamentoContratoControle.msgAlert}">
                        Apenas Selecionada
                    </a4j:commandLink>
                     <rich:spacer height="10"/>
                  <a4j:commandLink  action="#{CancelamentoContratoControle.cancelarAlteracaoParcial}"
                                       reRender="form,formEstorParcial"
                                     styleClass="pure-button"
                                      oncomplete="#{CancelamentoContratoControle.msgAlert}">
                    Cancelar
                </a4j:commandLink>    
                </h:panelGroup>
                             
            </h:panelGrid>
        </h:panelGroup>    
            
        <h:panelGrid id="panelMensagemParcial" columns="1" width="100%" >
            <h:panelGrid columns="2" width="100%" >
                <h:panelGrid columns="1" width="100%">

                    <h:outputText value=" "/>

                </h:panelGrid>
                <h:panelGrid columns="1" width="100%">
                    <h:outputText styleClass="mensagem" value="#{CancelamentoContratoControle.mensagem}"/>
                    <h:outputText styleClass="mensagemDetalhada" value="#{CancelamentoContratoControle.mensagemDetalhada}"/>
                </h:panelGrid>
            </h:panelGrid>
        </h:panelGrid>
    </a4j:form>
</rich:modalPanel>
</f:view>

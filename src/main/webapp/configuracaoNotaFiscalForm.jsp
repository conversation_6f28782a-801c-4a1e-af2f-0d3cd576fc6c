<%@page contentType="text/html" %>
<%@page pageEncoding="ISO-8859-1" %>
<head>
    <%@include file="/includes/include_import_minifiles.jsp" %>
</head>
<script src="script/packJQueryPlugins.min.js" type="text/javascript"></script>
<link href="./css/pacto_flat_2.16.min.css" rel="stylesheet" type="text/css">
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<link href="beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/pure-buttons.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/pure-ext.css" type="text/css" rel="stylesheet"/>
<script type="text/javascript" language="javascript" src="hoverform.js"></script>
<script type="text/javascript" src="script/tooltipster/jquery.tooltipster.min.js"></script>

<%@taglib prefix="f" uri="http://java.sun.com/jsf/core" %>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax" %>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich" %>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title>${msg_aplic.prt_ConfigNotaFiscal_tituloForm}</title>
    <c:set var="titulo" scope="session" value="${msg_aplic.prt_ConfigNotaFiscal_tituloForm}"/>
    <c:set var="urlWiki" scope="session"
           value="${SuperControle.urlBaseConhecimento}config-nota-fiscal-explicacao-dos-itens/"/>

    <a4j:loadScript src="/script/jquery.maskedinput-1.2.2.js"/>

    <f:facet name="header">
        <jsp:include page="topoReduzido_material.jsp"/>
    </f:facet>

    <style>
        .subordinado {
            padding: 5px !important;
        }
    </style>

    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <h:form id="form">

            <h:commandLink action="#{ConfiguracaoNotaFiscalControle.liberarBackingBeanMemoria}"
                           id="idLiberarBackingBeanMemoria"
                           style="display: none"/>

            <h:panelGrid columns="1" width="100%">

                <rich:tabPanel id="panelsEmpresa" width="100%" activeTabClass="true" headerAlignment="rigth"
                               switchType="ajax">

                    <rich:tab id="tabGeral" label="Geral">

                        <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar"
                                     id="panelGridGeral"
                                     columnClasses="classEsquerda, classDireita"
                                     headerClass="subordinado"
                                     width="100%">

                            <f:facet name="header">
                                <h:outputText
                                        value="Geral"/>
                            </f:facet>

                            <h:outputText value="Código:"/>
                            <h:outputText id="codigo" styleClass="form"
                                          value="#{ConfiguracaoNotaFiscalControle.configuracaoNotaFiscalVO.codigo}"/>

                            <h:outputText rendered="#{LoginControle.usuarioLogado.usuarioPactoSolucoes}"
                                          value="Empresa:"/>
                            <h:selectOneMenu id="empresa" onblur="blurinput(this);" onfocus="focusinput(this);"
                                             rendered="#{LoginControle.usuarioLogado.usuarioPactoSolucoes}"
                                             styleClass="form"
                                             value="#{ConfiguracaoNotaFiscalControle.configuracaoNotaFiscalVO.empresaVO.codigo}">
                                <f:selectItems value="#{ConfiguracaoNotaFiscalControle.listaEmpresas}"/>
                                <a4j:support event="onchange" reRender="form"
                                             action="#{ConfiguracaoNotaFiscalControle.selecionarEmpresa}"/>
                            </h:selectOneMenu>

                            <h:outputText value="Descrição:"/>
                            <h:inputText id="descricao" size="50" maxlength="120" onblur="blurinput(this);"
                                         onfocus="focusinput(this);" styleClass="form"
                                         value="#{ConfiguracaoNotaFiscalControle.configuracaoNotaFiscalVO.descricao}"/>

                            <h:outputText value="Tipo Nota Fiscal:"/>
                            <h:selectOneMenu id="tipoNota" onblur="blurinput(this);" onfocus="focusinput(this);"
                                             styleClass="form"
                                             rendered="#{ConfiguracaoNotaFiscalControle.configuracaoNotaFiscalVO.codigo == 0}"
                                             value="#{ConfiguracaoNotaFiscalControle.configuracaoNotaFiscalVO.tipoNotaFiscal}">
                                <f:selectItems value="#{ConfiguracaoNotaFiscalControle.listaTipoNota}"/>
                                <a4j:support event="onchange" reRender="form"/>
                            </h:selectOneMenu>
                            <h:outputText id="tipoNotaDescricao"
                                          rendered="#{ConfiguracaoNotaFiscalControle.configuracaoNotaFiscalVO.codigo > 0}"
                                          value="#{ConfiguracaoNotaFiscalControle.configuracaoNotaFiscalVO.tipoNotaFiscal.descricao}"/>

                            <h:outputText value="Ativo:"/>
                            <h:selectBooleanCheckbox id="ativo"
                                                     value="#{ConfiguracaoNotaFiscalControle.configuracaoNotaFiscalVO.ativo}"/>
                        </h:panelGrid>
                    </rich:tab>

                    <rich:tab id="tabEmpresa" label="Empresa">
                        <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar"
                                     id="panelGridEmpresa"
                                     columnClasses="classEsquerda, classDireita"
                                     headerClass="subordinado"
                                     width="100%">

                            <f:facet name="header">
                                <h:outputText value="Empresa"/>
                            </f:facet>

                            <h:outputText
                                    rendered="#{ConfiguracaoNotaFiscalControle.configuracaoNotaFiscalVO.novoObj || LoginControle.usuarioLogado.usuarioPactoSolucoes}"
                                    value="Empresa:"/>
                            <a4j:commandLink
                                    rendered="#{ConfiguracaoNotaFiscalControle.configuracaoNotaFiscalVO.novoObj || LoginControle.usuarioLogado.usuarioPactoSolucoes}"
                                    action="#{ConfiguracaoNotaFiscalControle.obterInformacoesEmpresa}"
                                    oncomplete="#{ConfiguracaoNotaFiscalControle.mensagemNotificar}"
                                    reRender="form"
                                    value="Carregar informações da empresa"/>

                            <h:outputText value="Nome Fantasia:"/>
                            <h:inputText id="nomeFantasia" size="50" maxlength="250"
                                         onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form"
                                         value="#{ConfiguracaoNotaFiscalControle.configuracaoNotaFiscalVO.nomeFantasia}"/>

                            <h:outputText value="Razão Social:"/>
                            <h:inputText id="razaoSocial" size="50" maxlength="250"
                                         onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form"
                                         value="#{ConfiguracaoNotaFiscalControle.configuracaoNotaFiscalVO.razaoSocial}"/>

                            <h:outputText value="CNPJ:"/>
                            <h:inputText id="cnpj" size="15" maxlength="18"
                                         onkeypress="return mascara(this.form, this.id, '99.999.999/9999-99', event);"
                                         onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form"
                                         value="#{ConfiguracaoNotaFiscalControle.configuracaoNotaFiscalVO.cnpj}"/>

                            <h:outputText value="Inscrição Municipal:"/>
                            <h:inputText size="15" maxlength="255" id="inscricaoMunicipal" onblur="blurinput(this);"
                                         onfocus="focusinput(this);" styleClass="form"
                                         value="#{ConfiguracaoNotaFiscalControle.configuracaoNotaFiscalVO.inscricaoMunicipal}"/>

                            <h:outputText value="Inscrição Estadual:"/>
                            <h:inputText size="15" maxlength="255" id="inscricaoEstadual" onblur="blurinput(this);"
                                         onfocus="focusinput(this);" styleClass="form"
                                         value="#{ConfiguracaoNotaFiscalControle.configuracaoNotaFiscalVO.inscricaoEstadual}"/>

                            <h:outputText value="Telefone Comercial:"/>
                            <h:inputText id="telefoneComercial" size="15" maxlength="15"
                                         onfocus="focusinput(this);" styleClass="form"
                                         onchange="return validar_Telefone(this.id);"
                                         onblur="blurinput(this);"
                                         onkeypress="return mascara(this.form, this.id , '(99)999999999', event);"
                                         value="#{ConfiguracaoNotaFiscalControle.configuracaoNotaFiscalVO.telefoneComercial}"/>

                            <h:outputText value="Email:"/>
                            <h:inputText id="email" size="50" maxlength="250"
                                         onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form"
                                         value="#{ConfiguracaoNotaFiscalControle.configuracaoNotaFiscalVO.email}"/>

                            <h:outputText value="CEP:"/>
                            <h:panelGroup layout="block">
                                <h:inputText id="cep" size="10" maxlength="10"
                                             onkeypress="return mascara(this.form, this.id, '99.999-999', event);"
                                             styleClass="form"
                                             value="#{ConfiguracaoNotaFiscalControle.configuracaoNotaFiscalVO.cep}">
                                </h:inputText>
                                <a4j:commandButton id="consultarCEP"
                                                   styleClass="tooltipster"
                                                   action="#{ConfiguracaoNotaFiscalControle.consultarCEP}"
                                                   image="imagens/atualizar.png"
                                                   title="Consultar CEP"
                                                   reRender="form:logradouro,form:complemento,form:bairro,form:panelEstado, form:panelPais, form:panelCidade"/>

                            </h:panelGroup>

                            <h:outputText value="Logradouro:"/>
                            <h:inputText id="logradouro" size="50" maxlength="250"
                                         onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form"
                                         value="#{ConfiguracaoNotaFiscalControle.configuracaoNotaFiscalVO.logradouro}"/>

                            <h:outputText value="Número:"/>
                            <h:inputText id="numero" size="5" maxlength="10"
                                         onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form"
                                         value="#{ConfiguracaoNotaFiscalControle.configuracaoNotaFiscalVO.numero}"/>

                            <h:outputText value="Complemento:"/>
                            <h:inputText id="complemento" size="50" maxlength="250"
                                         onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form"
                                         value="#{ConfiguracaoNotaFiscalControle.configuracaoNotaFiscalVO.complemento}"/>

                            <h:outputText value="Bairro:"/>
                            <h:inputText id="bairro" size="50" maxlength="250"
                                         onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form"
                                         value="#{ConfiguracaoNotaFiscalControle.configuracaoNotaFiscalVO.bairro}"/>

                            <h:outputText value="#{msg_aplic.prt_Pessoa_pais}"/>
                            <h:panelGroup layout="block" id="panelPais">
                                <h:selectOneMenu id="pais" onblur="blurinput(this);" onfocus="focusinput(this);"
                                                 styleClass="form"
                                                 value="#{ConfiguracaoNotaFiscalControle.configuracaoNotaFiscalVO.paisVO.codigo}">
                                    <f:selectItems value="#{ConfiguracaoNotaFiscalControle.listaSelectItemPais}"/>
                                    <a4j:support event="onchange" reRender="form" focus="pais"
                                                 action="#{ConfiguracaoNotaFiscalControle.montarListaEstado}"/>
                                </h:selectOneMenu>
                                <a4j:commandButton id="montarListaPais"
                                                   action="#{ConfiguracaoNotaFiscalControle.montarListaPais}"
                                                   image="imagens/atualizar.png"
                                                   reRender="form:panelEstado, form:panelPais, form:panelCidade"/>
                            </h:panelGroup>

                            <h:outputText value="#{msg_aplic.prt_Pessoa_estado}"/>
                            <h:panelGroup id="panelEstado">
                                <h:selectOneMenu id="estado" onblur="blurinput(this);" onfocus="focusinput(this);"
                                                 styleClass="form"
                                                 value="#{ConfiguracaoNotaFiscalControle.configuracaoNotaFiscalVO.estadoVO.codigo}">
                                    <f:selectItems value="#{ConfiguracaoNotaFiscalControle.listaSelectItemEstado}"/>
                                    <a4j:support event="onchange" reRender="cidade"
                                                 action="#{ConfiguracaoNotaFiscalControle.montarListaCidade}"/>
                                </h:selectOneMenu>
                                <a4j:commandButton id="montarListaEstado"
                                                   action="#{ConfiguracaoNotaFiscalControle.montarListaEstado}"
                                                   image="imagens/atualizar.png" ajaxSingle="true"
                                                   reRender="form:panelEstado, form:panelCidade"/>
                            </h:panelGroup>

                            <h:outputText value="#{msg_aplic.prt_Pessoa_cidade}"/>
                            <h:panelGroup id="panelCidade">
                                <h:selectOneMenu id="cidade" onblur="blurinput(this);" onfocus="focusinput(this);"
                                                 styleClass="form"
                                                 value="#{ConfiguracaoNotaFiscalControle.configuracaoNotaFiscalVO.cidadeVO.codigo}">
                                    <f:selectItems value="#{ConfiguracaoNotaFiscalControle.listaSelectItemCidade}"/>
                                </h:selectOneMenu>
                                <a4j:commandButton id="montarListaCidade"
                                                   action="#{ConfiguracaoNotaFiscalControle.montarListaCidade}"
                                                   image="imagens/atualizar.png" immediate="true" ajaxSingle="true"
                                                   reRender="form:panelCidade"/>
                            </h:panelGroup>
                        </h:panelGrid>
                    </rich:tab>

                    <rich:tab id="tabNotaFiscal" label="Nota Fiscal"
                              rendered="#{ConfiguracaoNotaFiscalControle.configuracaoNotaFiscalVO.NFSe || ConfiguracaoNotaFiscalControle.configuracaoNotaFiscalVO.NFCe || ConfiguracaoNotaFiscalControle.configuracaoNotaFiscalVO.NFe}">
                        <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar"
                                     columnClasses="classEsquerda, classDireita"
                                     headerClass="subordinado"
                                     width="100%">

                            <f:facet name="header">
                                <h:outputText
                                        value="Configurações Nota Fiscal"/>
                            </f:facet>

                            <%-- CONFIGURAÇÃO PARA NFSe --%>
                            <c:if test="${ConfiguracaoNotaFiscalControle.configuracaoNotaFiscalVO.NFSe || ConfiguracaoNotaFiscalControle.configuracaoNotaFiscalVO.NFe}">

                                <c:if test="${!ConfiguracaoNotaFiscalControle.configuracaoNotaFiscalVO.enotas}">
                                    <h:outputText value="Série RPS:"/>
                                    <h:inputText id="serie"
                                                 value="#{ConfiguracaoNotaFiscalControle.configuracaoNotaFiscalVO.serie}"
                                                 styleClass="form"/>
                                </c:if>

                                <h:outputText value="ISS Retido:"/>
                                <h:selectBooleanCheckbox
                                        value="#{ConfiguracaoNotaFiscalControle.configuracaoNotaFiscalVO.issRetido}"/>

                                <h:outputText value="ISS(%):"/>
                                <h:inputText id="iss" size="12"
                                             value="#{ConfiguracaoNotaFiscalControle.configuracaoNotaFiscalVO.iss}"
                                             onblur="blurinput(this);"
                                             onfocus="focusinput(this);" styleClass="form">
                                    <f:convertNumber pattern="#0.#########"/>
                                </h:inputText>

                                <h:outputText value="PIS(%):"/>
                                <h:inputText id="pis" size="10"
                                             value="#{ConfiguracaoNotaFiscalControle.configuracaoNotaFiscalVO.pis}"
                                             onkeypress="return formatar_moeda(this,'.',',',event);"
                                             onblur="blurinput(this);"
                                             onfocus="focusinput(this);" styleClass="form">
                                    <f:converter converterId="FormatadorNumerico"/>
                                </h:inputText>

                                <h:outputText value="COFINS(%):"/>
                                <h:inputText id="cofins" size="10"
                                             value="#{ConfiguracaoNotaFiscalControle.configuracaoNotaFiscalVO.cofins}"
                                             onkeypress="return formatar_moeda(this,'.',',',event);"
                                             onblur="blurinput(this);"
                                             onfocus="focusinput(this);" styleClass="form">
                                    <f:converter converterId="FormatadorNumerico"/>
                                </h:inputText>

                                <h:outputText value="IRRF(%):"/>
                                <h:inputText id="irrf" size="10"
                                             value="#{ConfiguracaoNotaFiscalControle.configuracaoNotaFiscalVO.irrf}"
                                             onkeypress="return formatar_moeda(this,'.',',',event);"
                                             onblur="blurinput(this);"
                                             onfocus="focusinput(this);" styleClass="form">
                                    <f:converter converterId="FormatadorNumerico"/>
                                </h:inputText>

                                <h:outputText value="CSLL(%):"/>
                                <h:inputText id="csll" size="10"
                                             value="#{ConfiguracaoNotaFiscalControle.configuracaoNotaFiscalVO.csll}"
                                             onkeypress="return formatar_moeda(this,'.',',',event);"
                                             onblur="blurinput(this);"
                                             onfocus="focusinput(this);" styleClass="form">
                                    <f:converter converterId="FormatadorNumerico"/>
                                </h:inputText>

                                <h:outputText value="Código Lista Serviço:"/>
                                <h:panelGroup layout="block">
                                    <h:inputText id="codListaServico"
                                                 value="#{ConfiguracaoNotaFiscalControle.configuracaoNotaFiscalVO.codListaServico}"
                                                 onblur="blurinput(this);" onfocus="focusinput(this);"
                                                 styleClass="form"/>

                                    <a4j:commandLink action="#{ConfiguracaoNotaFiscalControle.obterInformacoesServico}"
                                                     rendered="#{ConfiguracaoNotaFiscalControle.configuracaoNotaFiscalVO.enotas}"
                                                     value="Consultar informações do serviço - eNotas"
                                                     style="padding-left: 10px"
                                                     reRender="panelInfoServico"
                                                     oncomplete="#{ConfiguracaoNotaFiscalControle.onCompleteEnotas};#{ConfiguracaoNotaFiscalControle.mensagemNotificar}"/>
                                </h:panelGroup>

                                <h:outputText value="Código Tributação Município:"/>
                                <h:inputText id="codTributacaoMunicipal"
                                             value="#{ConfiguracaoNotaFiscalControle.configuracaoNotaFiscalVO.codTributacaoMunicipal}"
                                             onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form"/>

                                <h:outputText value="Exigibilidade ISS:"/>
                                <h:selectOneMenu id="exigibilidadeISS"
                                                 value="#{ConfiguracaoNotaFiscalControle.configuracaoNotaFiscalVO.idTipoExigibilidadeISSEnum}">
                                    <f:selectItems value="#{ConfiguracaoNotaFiscalControle.listaExigibilidadeISS}"/>
                                </h:selectOneMenu>
                            </c:if>

                            <h:outputText value="Optante Simples Nacional:"/>
                            <h:selectBooleanCheckbox id="optanteSimplesNacional"
                                                     value="#{ConfiguracaoNotaFiscalControle.configuracaoNotaFiscalVO.optanteSimplesNacional}"/>

                            <h:outputText value="Incentivador Cultural:"/>
                            <h:selectBooleanCheckbox id="incentivadorCultural"
                                                     value="#{ConfiguracaoNotaFiscalControle.configuracaoNotaFiscalVO.incentivadorCultural}"/>

                            <h:outputText style="font-weight: bold" value="CNAE:"/>
                            <h:inputText id="cnae" size="20"
                                         value="#{ConfiguracaoNotaFiscalControle.configuracaoNotaFiscalVO.cnae}"
                                         onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form"/>

                            <h:outputText value="Regime Especial Tributação:"/>
                            <h:inputText id="regimeEspecialTributacao" size="5" onblur="blurinput(this);"
                                         onfocus="focusinput(this);" styleClass="form"
                                         value="#{ConfiguracaoNotaFiscalControle.configuracaoNotaFiscalVO.regimeEspecialTributacao}"/>

                            <h:outputText value="Descrição Serviço:"/>
                            <h:inputText id="descricaoServico"
                                         size="50" maxlength="255"
                                         value="#{ConfiguracaoNotaFiscalControle.configuracaoNotaFiscalVO.descricaoServico}"/>

                            <h:outputText value="Natureza da Operação:"/>
                            <h:inputText size="50" maxlength="255" id="naturezaOperacao" onblur="blurinput(this);"
                                         onfocus="focusinput(this);" styleClass="form"
                                         value="#{ConfiguracaoNotaFiscalControle.configuracaoNotaFiscalVO.naturezaOperacao}"/>

                            <h:outputText style="font-weight: bold" value="Observação:"
                                          styleClass="tooltipster"
                                          title="#{ConfiguracaoNotaFiscalControle.configuracaoNotaFiscalVO.titleObservacao}"/>
                            <h:inputTextarea id="observacao"
                                             styleClass="tooltipster"
                                             value="#{ConfiguracaoNotaFiscalControle.configuracaoNotaFiscalVO.observacao}"
                                             cols="51"
                                             rows="3"
                                             title="#{ConfiguracaoNotaFiscalControle.configuracaoNotaFiscalVO.titleObservacao}"/>

                            <c:if test="${ConfiguracaoNotaFiscalControle.configuracaoNotaFiscalVO.NFSe || ConfiguracaoNotaFiscalControle.configuracaoNotaFiscalVO.NFe}">
                                <h:outputText value="Percentual Federal:"/>
                                <h:inputText id="percentualFederal" size="10"
                                             value="#{ConfiguracaoNotaFiscalControle.configuracaoNotaFiscalVO.percentualFederal}"
                                             onkeypress="return formatar_moeda(this,'.',',',event);"
                                             onblur="blurinput(this);"
                                             onfocus="focusinput(this);" styleClass="form">
                                    <f:converter converterId="FormatadorNumerico"/>
                                </h:inputText>
                                <h:outputText value="Percentual Estadual:"/>
                                <h:inputText id="percentualEstadual" size="10"
                                             value="#{ConfiguracaoNotaFiscalControle.configuracaoNotaFiscalVO.percentualEstadual}"
                                             onkeypress="return formatar_moeda(this,'.',',',event);"
                                             onblur="blurinput(this);"
                                             onfocus="focusinput(this);" styleClass="form">
                                    <f:converter converterId="FormatadorNumerico"/>
                                </h:inputText>
                                <h:outputText value="Percentual Municipal:"/>
                                <h:inputText id="percentualMunicipal" size="10"
                                             value="#{ConfiguracaoNotaFiscalControle.configuracaoNotaFiscalVO.percentualMunicipal}"
                                             onkeypress="return formatar_moeda(this,'.',',',event);"
                                             onblur="blurinput(this);"
                                             onfocus="focusinput(this);" styleClass="form">
                                    <f:converter converterId="FormatadorNumerico"/>
                                </h:inputText>
                                <h:outputText value="Valor Aproximado de Tributos(%):"/>
                                <h:inputText id="percentualAproximadoTributos" size="10"
                                             value="#{ConfiguracaoNotaFiscalControle.configuracaoNotaFiscalVO.percentualAproximadoTributos}"
                                             onkeypress="return formatar_moeda(this,'.',',',event);"
                                             onblur="blurinput(this);"
                                             onfocus="focusinput(this);" styleClass="form">
                                    <f:converter converterId="FormatadorNumerico"/>
                                </h:inputText>
                            </c:if>
                        </h:panelGrid>

                        <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar"
                                     columnClasses="classEsquerda, classDireita"
                                     headerClass="subordinado"
                                     width="100%">

                            <f:facet name="header">
                                <h:outputText
                                        value="Outras Configurações"/>
                            </f:facet>

                            <%-- CONFIGURAÇÃO PARA NFCe --%>
                            <c:if test="${ConfiguracaoNotaFiscalControle.configuracaoNotaFiscalVO.NFCe}">
                                <h:outputText value="CPF obrigatório:"/>
                                <h:selectBooleanCheckbox id="cpfObrigatorio"
                                                         value="#{ConfiguracaoNotaFiscalControle.configuracaoNotaFiscalVO.cpfObrigatorio}"/>

                                <h:outputText value="Usar descrição da forma de pagamento:"/>
                                <h:selectBooleanCheckbox id="usarDescricaoFormaPagamento"
                                                         value="#{ConfiguracaoNotaFiscalControle.configuracaoNotaFiscalVO.usarDescricaoFormaPagamento}"/>
                            </c:if>

                            <%-- CONFIGURAÇÃO PARA NFSe e NFe --%>
                            <c:if test="${ConfiguracaoNotaFiscalControle.configuracaoNotaFiscalVO.NFSe || ConfiguracaoNotaFiscalControle.configuracaoNotaFiscalVO.NFe}">

                                <h:outputText value="Valor Mínimo para Cobrar IRRF:"/>
                                <h:inputText id="valorMinimoIRRF" size="10"
                                             value="#{ConfiguracaoNotaFiscalControle.configuracaoNotaFiscalVO.valorMinimoIRRF}"
                                             onkeypress="return formatar_moeda(this,'.',',',event);"
                                             onblur="blurinput(this);"
                                             onfocus="focusinput(this);" styleClass="form">
                                    <f:converter converterId="FormatadorNumerico"/>
                                </h:inputText>

                                <h:outputText value="Enviar observação na descrição do Serviço/produto:" styleClass="tooltipster" title="#{ConfiguracaoNotaFiscalControle.configuracaoNotaFiscalVO.titleEnviarObservacao}"/>
                                <h:selectBooleanCheckbox id="enviarObservacaoNaDescricao" styleClass="tooltipster" title="#{ConfiguracaoNotaFiscalControle.configuracaoNotaFiscalVO.titleEnviarObservacao}"
                                                         value="#{ConfiguracaoNotaFiscalControle.configuracaoNotaFiscalVO.enviarObservacaoNaDescricao}"/>

                                <h:outputText value="Enviar nota com a cidade da empresa:"/>
                                <h:selectBooleanCheckbox id="enviarNotaCidadeEmpresa"
                                                         value="#{ConfiguracaoNotaFiscalControle.configuracaoNotaFiscalVO.enviarNotaCidadeEmpresa}"/>

                                <h:outputText value="Email obrigatório:"/>
                                <h:selectBooleanCheckbox id="emailObrigatorio"
                                                         value="#{ConfiguracaoNotaFiscalControle.configuracaoNotaFiscalVO.emailObrigatorio}"/>

                                <h:outputText value="Apresentar Duração do Plano na descrição da nota:"/>
                                <h:selectBooleanCheckbox id="apresentarDuracaoPlano"
                                                         value="#{ConfiguracaoNotaFiscalControle.configuracaoNotaFiscalVO.apresentarDuracaoPlano}"/>

                                <h:outputText value="Usar a descrição da Parcela na nota:"/>
                                <h:selectBooleanCheckbox id="apresentarDescricaoParcela"
                                                         value="#{ConfiguracaoNotaFiscalControle.configuracaoNotaFiscalVO.apresentarDescricaoParcela}"/>

                                <c:if test="${ConfiguracaoNotaFiscalControle.configuracaoNotaFiscalVO.apresentarSelecaoMostrarCompetencia}">
                                    <h:outputText value="Apresentar a competência na descrição da nota:"/>
                                    <h:selectBooleanCheckbox id="apresentarCompetencia"
                                                             value="#{ConfiguracaoNotaFiscalControle.configuracaoNotaFiscalVO.apresentarCompetencia}"/>
                                </c:if>
                            </c:if>

                            <h:outputText value="Endereço obrigatório:"/>
                            <h:selectBooleanCheckbox id="enderecoObrigatorio"
                                                     value="#{ConfiguracaoNotaFiscalControle.configuracaoNotaFiscalVO.enderecoObrigatorio}"/>

                            <h:outputText value="Limitar tamanho do complemento do endereço do aluno no envio:"
                                          styleClass="tooltipster"
                                          title="Algumas prefeituras possuem limite de caracteres no complemento do endereço da nota e caso for enviado mais que o limite a nota será recusada. Esta configuração é justamente pra quando o complemento do endereço for superior ao número informado aqui, o sistema irá enviar o complemento até o limite de caracteres informado.
                                         <br/>Informe 0 para desativar o recurso."/>
                            <h:inputText id="limiteComplementoEndereco" size="3" maxlength="3"
                                         onkeypress="return mascara(this.form, this.id, '9999', event);"
                                         onblur="blurinput(this);"
                                         onfocus="focusinput(this);"
                                         styleClass="tooltipster"
                                         title="Algumas prefeituras possuem limite de caracteres no complemento do endereço da nota e caso for enviado mais que o limite a nota será recusada. Esta configuração é justamente pra quando o complemento do endereço for superior ao número informado aqui, o sistema irá enviar o complemento até o limite de caracteres informado.
                                         <br/>Informe 0 para desativar o recurso."
                                         value="#{ConfiguracaoNotaFiscalControle.configuracaoNotaFiscalVO.limiteComplementoEndereco}"/>

                            <h:outputText value="Limitar tamanho da descrição da nota:"
                                          styleClass="tooltipster"
                                          title="Algumas prefeituras possuem limite de caracteres na descrição da nota e caso for enviado mais que o limite a nota será recusada. Esta configuração é justamente pra quando a descrição seja superior ao número informado aqui, o sistema irá enviar a descrição até a quantidade de caracteres informado.
                                          <br/>Informe 0 para desativar o recurso (Não limitar)."/>
                            <h:inputText id="limiteDescricao" size="3" maxlength="3"
                                         onkeypress="return mascara(this.form, this.id, '9999', event);"
                                         onblur="blurinput(this);"
                                         onfocus="focusinput(this);"
                                         styleClass="tooltipster"
                                         title="Algumas prefeituras possuem limite de caracteres na descrição da nota e caso for enviado mais que o limite a nota será recusada. Esta configuração é justamente pra quando a descrição seja superior ao número informado aqui, o sistema irá enviar a descrição até a quantidade de caracteres informado.
                                         <br/>Informe 0 para desativar o recurso (Não limitar)."
                                         value="#{ConfiguracaoNotaFiscalControle.configuracaoNotaFiscalVO.limiteDescricao}"/>

                            <h:outputText value="Limitar tamanho da observação da nota:"
                                          styleClass="tooltipster"
                                          title="Caso queira limitar a quantidade de caracteres a ser enviada na observação da nota para a prefeitura, utilize esta configuração para limitar.
                                          <br/>Informe 0 para desativar o recurso (Não limitar)."/>
                            <h:inputText id="limiteObservacao" size="3" maxlength="3"
                                         onkeypress="return mascara(this.form, this.id, '9999', event);"
                                         onblur="blurinput(this);"
                                         onfocus="focusinput(this);"
                                         styleClass="tooltipster"
                                         title="Caso queira limitar a quantidade de caracteres a ser enviada na observação da nota para a prefeitura, utilize esta configuração para limitar.
                                         <br/>Informe 0 para desativar o recurso (Não limitar)."
                                         value="#{ConfiguracaoNotaFiscalControle.configuracaoNotaFiscalVO.limiteObservacao}"/>

                            <h:outputText value="Enviar email cliente:"/>
                            <h:selectBooleanCheckbox id="enviarEmailCliente"
                                                     value="#{ConfiguracaoNotaFiscalControle.configuracaoNotaFiscalVO.enviarEmailCliente}"/>

                            <c:if test="${ConfiguracaoNotaFiscalControle.configuracaoNotaFiscalVO.NFCe}">
                                <h:outputText value="Apresentar tipo de integração do pagamento:"/>
                                <h:selectBooleanCheckbox id="apresentarTipoIntegracaoPagamento"
                                                         value="#{ConfiguracaoNotaFiscalControle.configuracaoNotaFiscalVO.apresentarTipoIntegracaoPagamento}"/>
                            </c:if>
                        </h:panelGrid>
                    </rich:tab>

                    <rich:tab id="tabENotas" label="Integração eNotas"
                              rendered="#{ConfiguracaoNotaFiscalControle.usuarioLogado.usuarioPactoSolucoes || LoginControle.permissaoAcessoMenuVO.permiteAlterarRPS}">

                        <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar"
                                     id="panelGridEnotas"
                                     columnClasses="classEsquerda, classDireita"
                                     headerClass="subordinado"
                                     width="100%">

                            <f:facet name="header">
                                <h:outputText value="eNotas"/>
                            </f:facet>

                            <h:outputText value="Integração eNotas:"/>
                            <h:selectBooleanCheckbox id="eNotas"
                                                     disabled="#{!(ConfiguracaoNotaFiscalControle.usuarioLogado.usuarioPactoSolucoes)}"
                                                     value="#{ConfiguracaoNotaFiscalControle.configuracaoNotaFiscalVO.enotas}">
                                <a4j:support event="onchange" reRender="form"/>
                            </h:selectBooleanCheckbox>

                            <c:if test="${ConfiguracaoNotaFiscalControle.configuracaoNotaFiscalVO.enotas}">

                                <h:outputText
                                        rendered="#{ConfiguracaoNotaFiscalControle.usuarioLogado.usuarioPactoSolucoes}"
                                        value="Obter informações módulo notas:"/>
                                <a4j:commandLink action="#{ConfiguracaoNotaFiscalControle.obterInformacoesModuloNotas}"
                                                 oncomplete="#{ConfiguracaoNotaFiscalControle.mensagemNotificar}"
                                                 value="Consultar informações"
                                                 rendered="#{ConfiguracaoNotaFiscalControle.usuarioLogado.usuarioPactoSolucoes}"
                                                 reRender="form"/>

                                <h:outputText
                                        rendered="#{ConfiguracaoNotaFiscalControle.usuarioLogado.usuarioPactoSolucoes}"
                                        value="Obter informações cidade:"/>
                                <a4j:commandLink action="#{ConfiguracaoNotaFiscalControle.obterInformacoesCidade}"
                                                 value="Consultar informações da cidade"
                                                 reRender="panelInfoCidade"
                                                 rendered="#{ConfiguracaoNotaFiscalControle.usuarioLogado.usuarioPactoSolucoes}"
                                                 oncomplete="#{ConfiguracaoNotaFiscalControle.onCompleteEnotas};#{ConfiguracaoNotaFiscalControle.mensagemNotificar}"/>

                                <h:outputText
                                        rendered="#{ConfiguracaoNotaFiscalControle.usuarioLogado.usuarioPactoSolucoes}"
                                        value="Processar código IBGE:"/>
                                <a4j:commandLink action="#{ConfiguracaoNotaFiscalControle.preencherCodigoIBGE}"
                                                 oncomplete="#{ConfiguracaoNotaFiscalControle.mensagemNotificar}"
                                                 rendered="#{ConfiguracaoNotaFiscalControle.usuarioLogado.usuarioPactoSolucoes}"
                                                 value="Processar IBGE"/>

                                <h:outputText value="URL Download Certificado:"
                                              rendered="#{not empty ConfiguracaoNotaFiscalControle.configuracaoNotaFiscalVO.urlDownloadCertificado &&
                                              ConfiguracaoNotaFiscalControle.usuarioLogado.usuarioPactoSolucoes}"/>
                                <h:outputLink
                                        value="#{ConfiguracaoNotaFiscalControle.configuracaoNotaFiscalVO.urlDownloadCertificado}"
                                        target="_blank"
                                        rendered="#{not empty ConfiguracaoNotaFiscalControle.configuracaoNotaFiscalVO.urlDownloadCertificado &&
                                              ConfiguracaoNotaFiscalControle.usuarioLogado.usuarioPactoSolucoes}">
                                    <h:outputText value="Clique para fazer o download do certificado"/>
                                </h:outputLink>

                                <h:outputText value="URL Download Logotipo:"
                                              rendered="#{not empty ConfiguracaoNotaFiscalControle.configuracaoNotaFiscalVO.urlDownloadLogotipo}"/>
                                <h:outputLink
                                        value="#{ConfiguracaoNotaFiscalControle.configuracaoNotaFiscalVO.urlDownloadLogotipo}"
                                        target="_blank"
                                        rendered="#{not empty ConfiguracaoNotaFiscalControle.configuracaoNotaFiscalVO.urlDownloadLogotipo &&
                                              ConfiguracaoNotaFiscalControle.usuarioLogado.usuarioPactoSolucoes}">
                                    <h:outputText value="Clique para fazer o download do logotipo"/>
                                </h:outputLink>

                                <h:outputText
                                        rendered="#{ConfiguracaoNotaFiscalControle.usuarioLogado.usuarioPactoSolucoes}"
                                        value="ID eNotas:"/>
                                <h:outputText id="idEnotasOut"
                                              rendered="#{!LoginControle.usuarioLogado.usuarioPactoSolucoes &&
                                              ConfiguracaoNotaFiscalControle.usuarioLogado.usuarioPactoSolucoes}"
                                              value="#{ConfiguracaoNotaFiscalControle.configuracaoNotaFiscalVO.idEnotas}"/>
                                <h:inputText id="idEnotas" size="50" maxlength="300" onblur="blurinput(this);"
                                             rendered="#{LoginControle.usuarioLogado.usuarioPactoSolucoes &&
                                              ConfiguracaoNotaFiscalControle.usuarioLogado.usuarioPactoSolucoes}"
                                             onfocus="focusinput(this);" styleClass="form"
                                             value="#{ConfiguracaoNotaFiscalControle.configuracaoNotaFiscalVO.idEnotas}"/>

                                <h:outputText value=""
                                              rendered="#{not empty ConfiguracaoNotaFiscalControle.configuracaoNotaFiscalVO.idEnotas &&
                                              ConfiguracaoNotaFiscalControle.usuarioLogado.usuarioPactoSolucoes}"/>
                                <a4j:commandLink
                                        action="#{ConfiguracaoNotaFiscalControle.consultarSituacaoEmpresaEnotas}"
                                        rendered="#{not empty ConfiguracaoNotaFiscalControle.configuracaoNotaFiscalVO.idEnotas &&
                                              ConfiguracaoNotaFiscalControle.usuarioLogado.usuarioPactoSolucoes}"
                                        value="Consultar situação empresa eNotas"
                                        reRender="panelInfoEmpresaEnotas"
                                        oncomplete="#{ConfiguracaoNotaFiscalControle.onCompleteEnotas};#{ConfiguracaoNotaFiscalControle.mensagemNotificar}"/>

                                <h:outputText
                                        rendered="#{ConfiguracaoNotaFiscalControle.usuarioLogado.usuarioPactoSolucoes}"
                                        value="Ambiente Emissão:"/>
                                <h:selectOneMenu id="ambienteEmissao" onblur="blurinput(this);"
                                                 onfocus="focusinput(this);"
                                                 styleClass="form"
                                                 rendered="#{ConfiguracaoNotaFiscalControle.usuarioLogado.usuarioPactoSolucoes}"
                                                 value="#{ConfiguracaoNotaFiscalControle.configuracaoNotaFiscalVO.ambienteEmissao}">
                                    <f:selectItems value="#{ConfiguracaoNotaFiscalControle.listaAmbienteEmissao}"/>
                                    <a4j:support event="onchange" reRender="form"/>
                                </h:selectOneMenu>

                                <h:outputText
                                        rendered="#{ConfiguracaoNotaFiscalControle.apresentaSetupSAT}"
                                        value=""/>
                                <a4j:commandLink id="obterLinkDownloadSetupSAT"
                                                 value="Download Setup SAT"
                                                 rendered="#{ConfiguracaoNotaFiscalControle.apresentaSetupSAT}"
                                                 action="#{ConfiguracaoNotaFiscalControle.obterLinkDownloadSetupSAT}"
                                                 oncomplete="#{ConfiguracaoNotaFiscalControle.onCompleteEnotas};#{ConfiguracaoNotaFiscalControle.mensagemNotificar}"/>
                            </c:if>

                            <h:outputText value="Link Portal Prefeitura:"/>
                            <h:inputText id="linkPrefeitura" styleClass="form"
                                         size="60"
                                         value="#{ConfiguracaoNotaFiscalControle.configuracaoNotaFiscalVO.linkPrefeitura}"/>
                        </h:panelGrid>

                        <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar"
                                     rendered="#{ConfiguracaoNotaFiscalControle.configuracaoNotaFiscalVO.enotas}"
                                     columnClasses="classEsquerda, classDireita"
                                     headerClass="subordinado"
                                     width="100%">

                            <f:facet name="header">
                                <h:outputText
                                        value="Configurações Ambiente Produção"/>
                            </f:facet>

                            <h:outputText
                                    rendered="#{ConfiguracaoNotaFiscalControle.usuarioLogado.usuarioPactoSolucoes}"
                                    value="Código Config:"/>
                            <h:outputText
                                    rendered="#{ConfiguracaoNotaFiscalControle.usuarioLogado.usuarioPactoSolucoes}"
                                    id="codigoProd"
                                    value="#{ConfiguracaoNotaFiscalControle.configuracaoNotaFiscalVO.configProducaoVO.codigo}"/>

                            <h:outputText
                                    rendered="#{ConfiguracaoNotaFiscalControle.usuarioLogado.usuarioPactoSolucoes}"
                                    value="Série:"/>
                            <h:inputText rendered="#{ConfiguracaoNotaFiscalControle.usuarioLogado.usuarioPactoSolucoes}"
                                         id="serieProd" styleClass="form"
                                         value="#{ConfiguracaoNotaFiscalControle.configuracaoNotaFiscalVO.configProducaoVO.serieNFe}"/>

                            <h:outputText value="Sequêncial Lote:"/>
                            <h:inputText id="seqLoteProd" styleClass="form"
                                         value="#{ConfiguracaoNotaFiscalControle.configuracaoNotaFiscalVO.configProducaoVO.sequencialLoteNFe}"/>

                            <h:outputText value="Sequêncial Nota:"/>
                            <h:inputText id="seqProd" styleClass="form"
                                         value="#{ConfiguracaoNotaFiscalControle.configuracaoNotaFiscalVO.configProducaoVO.sequencialNFe}"/>

                            <h:outputText
                                    rendered="#{ConfiguracaoNotaFiscalControle.usuarioLogado.usuarioPactoSolucoes}"
                                    value="Usuário Acesso:"/>
                            <h:inputText rendered="#{ConfiguracaoNotaFiscalControle.usuarioLogado.usuarioPactoSolucoes}"
                                         id="usuarioProd" styleClass="form"
                                         value="#{ConfiguracaoNotaFiscalControle.configuracaoNotaFiscalVO.configProducaoVO.usuarioAcessoProvedor}"/>

                            <h:outputText
                                    rendered="#{ConfiguracaoNotaFiscalControle.usuarioLogado.usuarioPactoSolucoes}"
                                    value="Senha Acesso:"/>
                            <h:inputText rendered="#{ConfiguracaoNotaFiscalControle.usuarioLogado.usuarioPactoSolucoes}"
                                         id="passWAcesso" styleClass="form"
                                         value="#{ConfiguracaoNotaFiscalControle.configuracaoNotaFiscalVO.configProducaoVO.senhaAcessoProvedor}"/>

                            <h:outputText
                                    rendered="#{ConfiguracaoNotaFiscalControle.usuarioLogado.usuarioPactoSolucoes}"
                                    value="Token Acesso:"/>
                            <h:inputText rendered="#{ConfiguracaoNotaFiscalControle.usuarioLogado.usuarioPactoSolucoes}"
                                         id="tokenProd" styleClass="form"
                                         value="#{ConfiguracaoNotaFiscalControle.configuracaoNotaFiscalVO.configProducaoVO.tokenAcessoProvedor}"/>

                            <h:outputText value="ID CSC:"
                                          rendered="#{(ConfiguracaoNotaFiscalControle.configuracaoNotaFiscalVO.NFCe || ConfiguracaoNotaFiscalControle.configuracaoNotaFiscalVO.NFe) &&
                                          ConfiguracaoNotaFiscalControle.usuarioLogado.usuarioPactoSolucoes}"/>
                            <h:inputText id="idCSCProd" styleClass="form"
                                         rendered="#{(ConfiguracaoNotaFiscalControle.configuracaoNotaFiscalVO.NFCe || ConfiguracaoNotaFiscalControle.configuracaoNotaFiscalVO.NFe) &&
                                          ConfiguracaoNotaFiscalControle.usuarioLogado.usuarioPactoSolucoes}"
                                         value="#{ConfiguracaoNotaFiscalControle.configuracaoNotaFiscalVO.configProducaoVO.idCSC}"/>

                            <h:outputText value="CSC:"
                                          rendered="#{ConfiguracaoNotaFiscalControle.configuracaoNotaFiscalVO.NFCe || ConfiguracaoNotaFiscalControle.configuracaoNotaFiscalVO.NFe &&
                                          ConfiguracaoNotaFiscalControle.usuarioLogado.usuarioPactoSolucoes}"/>
                            <h:inputText id="cscProd" styleClass="form"
                                         rendered="#{ConfiguracaoNotaFiscalControle.configuracaoNotaFiscalVO.NFCe || ConfiguracaoNotaFiscalControle.configuracaoNotaFiscalVO.NFe &&
                                          ConfiguracaoNotaFiscalControle.usuarioLogado.usuarioPactoSolucoes}"
                                         value="#{ConfiguracaoNotaFiscalControle.configuracaoNotaFiscalVO.configProducaoVO.csc}"/>

                        </h:panelGrid>

                        <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar"
                                     rendered="#{ConfiguracaoNotaFiscalControle.configuracaoNotaFiscalVO.enotas &&
                                          ConfiguracaoNotaFiscalControle.usuarioLogado.usuarioPactoSolucoes}"
                                     columnClasses="classEsquerda, classDireita"
                                     headerClass="subordinado"
                                     width="100%">

                            <f:facet name="header">
                                <h:outputText
                                        value="Configurações Ambiente Homologação"/>
                            </f:facet>

                            <h:outputText
                                    rendered="#{ConfiguracaoNotaFiscalControle.usuarioLogado.usuarioPactoSolucoes}"
                                    value="Código Config:"/>
                            <h:outputText id="codigoHomo"
                                          rendered="#{ConfiguracaoNotaFiscalControle.usuarioLogado.usuarioPactoSolucoes}"
                                          value="#{ConfiguracaoNotaFiscalControle.configuracaoNotaFiscalVO.configHomologacaoVO.codigo}"/>

                            <h:outputText
                                    rendered="#{ConfiguracaoNotaFiscalControle.usuarioLogado.usuarioPactoSolucoes}"
                                    value="Série:"/>
                            <h:inputText rendered="#{ConfiguracaoNotaFiscalControle.usuarioLogado.usuarioPactoSolucoes}"
                                         id="serieHomo" styleClass="form"
                                         value="#{ConfiguracaoNotaFiscalControle.configuracaoNotaFiscalVO.configHomologacaoVO.serieNFe}"/>

                            <h:outputText
                                    rendered="#{ConfiguracaoNotaFiscalControle.usuarioLogado.usuarioPactoSolucoes}"
                                    value="Sequêncial Lote:"/>
                            <h:inputText rendered="#{ConfiguracaoNotaFiscalControle.usuarioLogado.usuarioPactoSolucoes}"
                                         id="seqLoteHomo" styleClass="form"
                                         value="#{ConfiguracaoNotaFiscalControle.configuracaoNotaFiscalVO.configHomologacaoVO.sequencialLoteNFe}"/>

                            <h:outputText
                                    rendered="#{ConfiguracaoNotaFiscalControle.usuarioLogado.usuarioPactoSolucoes}"
                                    value="Sequêncial Nota:"/>
                            <h:inputText rendered="#{ConfiguracaoNotaFiscalControle.usuarioLogado.usuarioPactoSolucoes}"
                                         id="seqHomo" styleClass="form"
                                         value="#{ConfiguracaoNotaFiscalControle.configuracaoNotaFiscalVO.configHomologacaoVO.sequencialNFe}"/>

                            <h:outputText
                                    rendered="#{ConfiguracaoNotaFiscalControle.usuarioLogado.usuarioPactoSolucoes}"
                                    value="Usuário Acesso:"/>
                            <h:inputText id="usuarioHomo" styleClass="form"
                                         rendered="#{ConfiguracaoNotaFiscalControle.usuarioLogado.usuarioPactoSolucoes}"
                                         value="#{ConfiguracaoNotaFiscalControle.configuracaoNotaFiscalVO.configHomologacaoVO.usuarioAcessoProvedor}"/>

                            <h:outputText value="Senha Acesso:"
                                          rendered="#{ConfiguracaoNotaFiscalControle.usuarioLogado.usuarioPactoSolucoes}"/>
                            <h:inputText id="passWHomo" styleClass="form"
                                         rendered="#{ConfiguracaoNotaFiscalControle.usuarioLogado.usuarioPactoSolucoes}"
                                         value="#{ConfiguracaoNotaFiscalControle.configuracaoNotaFiscalVO.configHomologacaoVO.senhaAcessoProvedor}"/>

                            <h:outputText value="Token Acesso:"
                                          rendered="#{ConfiguracaoNotaFiscalControle.usuarioLogado.usuarioPactoSolucoes}"/>
                            <h:inputText id="tokenHomo" styleClass="form"
                                         rendered="#{ConfiguracaoNotaFiscalControle.usuarioLogado.usuarioPactoSolucoes}"
                                         value="#{ConfiguracaoNotaFiscalControle.configuracaoNotaFiscalVO.configHomologacaoVO.tokenAcessoProvedor}"/>

                            <h:outputText value="ID CSC:"
                                          rendered="#{ConfiguracaoNotaFiscalControle.configuracaoNotaFiscalVO.NFCe && ConfiguracaoNotaFiscalControle.usuarioLogado.usuarioPactoSolucoes}"/>
                            <h:inputText id="idCSCHomo" styleClass="form"
                                         rendered="#{ConfiguracaoNotaFiscalControle.configuracaoNotaFiscalVO.NFCe && ConfiguracaoNotaFiscalControle.usuarioLogado.usuarioPactoSolucoes}"
                                         value="#{ConfiguracaoNotaFiscalControle.configuracaoNotaFiscalVO.configHomologacaoVO.idCSC}"/>

                            <h:outputText value="CSC:"
                                          rendered="#{ConfiguracaoNotaFiscalControle.configuracaoNotaFiscalVO.NFCe && ConfiguracaoNotaFiscalControle.usuarioLogado.usuarioPactoSolucoes}"/>
                            <h:inputText id="cscHomo" styleClass="form"
                                         rendered="#{ConfiguracaoNotaFiscalControle.configuracaoNotaFiscalVO.NFCe && ConfiguracaoNotaFiscalControle.usuarioLogado.usuarioPactoSolucoes}"
                                         value="#{ConfiguracaoNotaFiscalControle.configuracaoNotaFiscalVO.configHomologacaoVO.csc}"/>

                        </h:panelGrid>

                        <h:panelGroup layout="block" style="text-align: center; padding: 15px;"
                                      rendered="#{ConfiguracaoNotaFiscalControle.configuracaoNotaFiscalVO.enotas && (LoginControle.usuarioLogado.usuarioPactoSolucoes || LoginControle.permissaoAcessoMenuVO.permiteAlterarRPS)}">
                            <a4j:commandLink id="enviarEmpresaComConfiguracoesAmbiente"
                                             action="#{ConfiguracaoNotaFiscalControle.enviarEmpresaComConfiguracoesAmbiente}"
                                             oncomplete="#{ConfiguracaoNotaFiscalControle.mensagemNotificar}"
                                             value="Enviar Empresa com Configurações de Ambiente"
                                             title="Enviar Empresa com Configurações de Ambiente"
                                             reRender="form"
                                             styleClass="botoes nvoBt btSec tooltipster"/>
                        </h:panelGroup>
                    </rich:tab>

                    <rich:tab id="tabCertificado" label="Certificado A1"
                              rendered="#{ConfiguracaoNotaFiscalControle.configuracaoNotaFiscalVO.enotas}">

                        <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar"
                                     columnClasses="classEsquerda, classDireita"
                                     headerClass="subordinado"
                                     width="100%">

                            <f:facet name="header">
                                <h:outputText
                                        value="Certificado Tipo A1"/>
                            </f:facet>

                            <h:outputText value="Data validade:"
                                          rendered="#{not empty ConfiguracaoNotaFiscalControle.configuracaoNotaFiscalVO.dataCertificado_Apresentar}"/>
                            <h:outputText id="dataCertificado"
                                          rendered="#{not empty ConfiguracaoNotaFiscalControle.configuracaoNotaFiscalVO.dataCertificado_Apresentar}"
                                          value="#{ConfiguracaoNotaFiscalControle.configuracaoNotaFiscalVO.dataCertificado_Apresentar}"/>

                            <h:outputText value="Senha certificado digital:"/>
                            <h:inputText id="passWCertificado" size="20" maxlength="50"
                                         onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form"
                                         value="#{ConfiguracaoNotaFiscalControle.configuracaoNotaFiscalVO.senhaCertificado}"/>


                            <h:outputText value="Certificado digital:<br/>Formato (.P12, .PFX)" escape="false"/>
                            <h:panelGroup style="vertical-align: middle;"
                                          layout="block" id="panelArquivo">
                                <rich:fileUpload id="upload"
                                                 rendered="#{empty ConfiguracaoNotaFiscalControle.configuracaoNotaFiscalVO.chaveCertificado}"
                                                 listHeight="60"
                                                 listWidth="565"
                                                 noDuplicate="false"
                                                 fileUploadListener="#{ConfiguracaoNotaFiscalControle.upload}"
                                                 maxFilesQuantity="1"
                                                 allowFlash="false"
                                                 immediateUpload="false"
                                                 acceptedTypes="P12, PFX, p12, pfx"
                                                 addControlLabel="Adicionar"
                                                 cancelEntryControlLabel="Cancelar"
                                                 doneLabel="Pronto"
                                                 sizeErrorLabel="Arquivo não Enviado. Excedeu o tamanho permitido."
                                                 progressLabel="Enviando"
                                                 clearControlLabel="Limpar"
                                                 clearAllControlLabel="Limpar todos"
                                                 stopControlLabel="Parar"
                                                 uploadControlLabel="Enviar"
                                                 transferErrorLabel="Falha de Transmissão"
                                                 stopEntryControlLabel="Parar">
                                    <a4j:support event="onerror"
                                                 oncomplete="#{ConfiguracaoNotaFiscalControle.onCompleteArquivo}"/>
                                    <a4j:support event="onupload"
                                                 oncomplete="#{ConfiguracaoNotaFiscalControle.onCompleteArquivo}"/>
                                    <a4j:support event="onuploadcomplete"
                                                 oncomplete="#{ConfiguracaoNotaFiscalControle.onCompleteArquivo}"/>
                                    <a4j:support event="onclear" reRender="panelArquivo"
                                                 action="#{ConfiguracaoNotaFiscalControle.limparArquivo}"/>
                                </rich:fileUpload>


                                <a4j:commandLink action="#{ConfiguracaoNotaFiscalControle.limparArquivo}"
                                                 oncomplete="#{ConfiguracaoNotaFiscalControle.onCompleteArquivo}"
                                                 rendered="#{not empty ConfiguracaoNotaFiscalControle.configuracaoNotaFiscalVO.chaveCertificado}"
                                                 value="Limpar certificado"
                                                 reRender="panelArquivo"/>
                            </h:panelGroup>
                        </h:panelGrid>

                        <h:panelGroup layout="block" style="text-align: center; padding: 15px;">
                            <a4j:commandLink id="enviarCertificadoEnotas"
                                             action="#{ConfiguracaoNotaFiscalControle.enviarCertificadoEnotas}"
                                             oncomplete="#{ConfiguracaoNotaFiscalControle.mensagemNotificar}"
                                             value="Enviar Certificado eNotas"
                                             title="Enviar certificado para o eNotas"
                                             reRender="form"
                                             styleClass="botoes nvoBt btSec tooltipster"/>
                        </h:panelGroup>
                    </rich:tab>

                    <rich:tab id="tabLogotipo" label="Logotipo"
                              rendered="#{ConfiguracaoNotaFiscalControle.configuracaoNotaFiscalVO.enotas}">

                        <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar"  id="gridLogotipo"
                                     columnClasses="classEsquerda, classDireita"
                                     rendered="#{ConfiguracaoNotaFiscalControle.configuracaoNotaFiscalVO.enotas}"
                                     headerClass="subordinado"
                                     width="100%">

                            <f:facet name="header">
                                <h:outputText
                                        value="Logotipo"/>
                            </f:facet>

                            <h:outputText value="Logotipo:<br/>Formato (.JPG, .PNG)" escape="false"/>
                            <h:panelGroup style="vertical-align: middle;"
                                          layout="block" id="panelLogotipo">
                                <rich:fileUpload id="uploadLogo"
                                                 rendered="#{empty ConfiguracaoNotaFiscalControle.configuracaoNotaFiscalVO.chaveLogotipo}"
                                                 listHeight="60"
                                                 listWidth="565"
                                                 noDuplicate="false"
                                                 fileUploadListener="#{ConfiguracaoNotaFiscalControle.uploadLogo}"
                                                 maxFilesQuantity="1"
                                                 allowFlash="false"
                                                 immediateUpload="false"
                                                 acceptedTypes="JPG, PNG, png, jpg"
                                                 addControlLabel="Adicionar"
                                                 cancelEntryControlLabel="Cancelar"
                                                 doneLabel="Pronto"
                                                 sizeErrorLabel="Arquivo não Enviado. Excedeu o tamanho permitido."
                                                 progressLabel="Enviando"
                                                 clearControlLabel="Limpar"
                                                 clearAllControlLabel="Limpar todos"
                                                 stopControlLabel="Parar"
                                                 uploadControlLabel="Enviar"
                                                 transferErrorLabel="Falha de Transmissão"
                                                 stopEntryControlLabel="Parar">
                                    <a4j:support event="onerror"
                                                 oncomplete="#{ConfiguracaoNotaFiscalControle.onCompleteArquivoLogo}"/>
                                    <a4j:support event="onupload" reRender="gridLogotipo"
                                                 oncomplete="#{ConfiguracaoNotaFiscalControle.onCompleteArquivoLogo}"/>
                                    <a4j:support event="onuploadcomplete"  reRender="gridLogotipo"
                                                 oncomplete="#{ConfiguracaoNotaFiscalControle.onCompleteArquivoLogo}"/>
                                    <a4j:support event="onclear" reRender="gridLogotipo"
                                                 action="#{ConfiguracaoNotaFiscalControle.limparArquivoLogo}"/>
                                </rich:fileUpload>

                                <h:panelGrid columns="2">
                                    <h:graphicImage id="imagemLogotipo"
                                                    styleClass="imagemBanner"
                                                    rendered="#{not empty ConfiguracaoNotaFiscalControle.configuracaoNotaFiscalVO.chaveLogotipo}"
                                                    style="width: 160px; height: 130px;"
                                                    url="#{ConfiguracaoNotaFiscalControle.paintFotoDaNuvem}">
                                    </h:graphicImage>

                                    <a4j:commandLink action="#{ConfiguracaoNotaFiscalControle.limparArquivoLogo}"
                                                     oncomplete="#{ConfiguracaoNotaFiscalControle.onCompleteArquivoLogo}"
                                                     rendered="#{not empty ConfiguracaoNotaFiscalControle.configuracaoNotaFiscalVO.chaveLogotipo}"
                                                     value="Limpar logotipo"
                                                     reRender="panelLogotipo"/>

                                </h:panelGrid>
                            </h:panelGroup>
                            <h:outputText value="Tamanho  Padrão do Arquivo: " escape="false"/>
                            <h:panelGroup>
                                <h:outputText value=" 160 x 130px" escape="false"/>
                                <rich:spacer width="20px"/>
                                <h:outputText styleClass="classInfCadUsuario" value="    *Especificação por parte do eNotas."/>
                            </h:panelGroup>
                            <h:outputText rendered="#{ConfiguracaoNotaFiscalControle.apresentarTamanhoLogo}" styleClass="tituloCampos"
                                          value="Tamanho do Arquivo Adicionado: " />
                            <h:panelGroup  rendered="#{ConfiguracaoNotaFiscalControle.apresentarTamanhoLogo}" >
                                <h:outputText
                                              value="#{ConfiguracaoNotaFiscalControle.larguraLogo}" />
                                <h:outputText  value=" x " />
                                <h:outputText
                                              value="#{ConfiguracaoNotaFiscalControle.alturaLogo}" />
                            </h:panelGroup>
                        </h:panelGrid>


                        <h:panelGroup layout="block" style="text-align: center; padding: 15px;">
                            <a4j:commandLink id="enviarLogotipoEnotas"
                                             action="#{ConfiguracaoNotaFiscalControle.enviarLogotipoEnotas}"
                                             oncomplete="#{ConfiguracaoNotaFiscalControle.mensagemNotificar}"
                                             value="Enviar Logotipo eNotas"
                                             title="Enviar logotipo para o eNotas"
                                             reRender="form"
                                             styleClass="botoes nvoBt btSec tooltipster"/>
                        </h:panelGroup>
                    </rich:tab>

                </rich:tabPanel>

                <h:panelGrid columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid columns="3" width="100%" styleClass="tabMensagens" id="panelMensagens">
                        <h:panelGrid columns="1" width="100%">

                            <h:outputText value=" "/>

                        </h:panelGrid>
                        <h:commandButton rendered="#{ConfiguracaoNotaFiscalControle.sucesso}"
                                         image="./imagens/sucesso.png"/>
                        <h:commandButton rendered="#{ConfiguracaoNotaFiscalControle.erro}" image="./imagens/erro.png"/>
                        <h:panelGrid columns="1" width="100%">
                            <h:outputText id="msg" styleClass="mensagem"
                                          escape="false"
                                          value="#{ConfiguracaoNotaFiscalControle.mensagem}"/>
                            <h:outputText id="msgDet" styleClass="mensagemDetalhada"
                                          escape="false"
                                          value="#{ConfiguracaoNotaFiscalControle.mensagemDetalhada}"/>
                        </h:panelGrid>
                    </h:panelGrid>
                    <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                        <h:panelGroup>
                            <a4j:commandButton id="novo" immediate="true"
                                               action="#{ConfiguracaoNotaFiscalControle.novo}"
                                               value="#{msg_bt.btn_novo}" alt="#{msg.msg_novo_dados}" accesskey="1"
                                               styleClass="botoes nvoBt btSec"/>
                            <h:outputText value="    "/>

                            <a4j:commandButton id="salvar" action="#{ConfiguracaoNotaFiscalControle.gravar}"
                                               oncomplete="#{ConfiguracaoNotaFiscalControle.mensagemNotificar}"
                                               value="#{msg_bt.btn_gravar}"
                                               reRender="form, mdlAviso, panelMensagem"
                                               alt="#{msg.msg_gravar_dados}" accesskey="2" styleClass="botoes nvoBt"/>
                            <h:outputText value="    "/>

                            <%--                            <a4j:commandButton id="excluir"--%>
                            <%--                                               rendered="#{ConfiguracaoNotaFiscalControle.configuracaoNotaFiscalVO.codigo > 0}"--%>
                            <%--                                               onclick="fireElementFromAnyParent('form:btnAtualizaTempo');if(!confirm('Confirma exclusão dos dados?')){return true;}"--%>
                            <%--                                               oncomplete="#{ConfiguracaoNotaFiscalControle.mensagemNotificar}"--%>
                            <%--                                               action="#{ConfiguracaoNotaFiscalControle.excluir}"--%>
                            <%--                                               value="#{msg_bt.btn_excluir}"--%>
                            <%--                                               alt="#{msg.msg_excluir_dados}"--%>
                            <%--                                               reRender="form, mdlAviso, panelMensagem"--%>
                            <%--                                               accesskey="3" styleClass="botoes nvoBt btSec btPerigo"/>--%>
                            <%--                            <h:outputText value="    "/>--%>

                            <a4j:commandButton id="clonar"
                                               rendered="#{ConfiguracaoNotaFiscalControle.configuracaoNotaFiscalVO.codigo > 0}"
                                               oncomplete="#{ConfiguracaoNotaFiscalControle.mensagemNotificar};fireElementFromAnyParent('form:btnAtualizaTempo')"
                                               action="#{ConfiguracaoNotaFiscalControle.clonar}"
                                               value="#{msg_bt.btn_Clonar}"
                                               reRender="form, mdlAviso, panelMensagem"
                                               alt="#{msg.msg_clonar_dados}" accesskey="4"
                                               styleClass="botoes nvoBt btSec"/>

                            <h:outputText value="    "/>

                            <a4j:commandButton id="consultar" immediate="true"
                                               action="#{ConfiguracaoNotaFiscalControle.consultar}"
                                               value="Voltar" alt="#{msg.msg_consultar_dados}"
                                               accesskey="4" styleClass="botoes nvoBt btSec"/>
                            <h:outputText value="    "/>

                            <a4j:commandLink
                                    action="#{ConfiguracaoNotaFiscalControle.realizarConsultaLogObjetoSelecionado}"
                                    reRender="form"
                                    oncomplete="abrirPopup('visualizadorLogForm.jsp', 'VisualizadorLog', 785, 595);fireElementFromAnyParent('form:btnAtualizaTempo')"
                                    title="Visualizar Log" styleClass="botoes nvoBt btSec"
                                    style="display: inline-block;padding-bottom: 10px;">
                                <i class="fa-icon-list"></i>
                            </a4j:commandLink>
                        </h:panelGroup>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
        </h:form>
    </h:panelGrid>

    <rich:modalPanel id="modalEnotas" autosized="true"
                     shadowOpacity="true" width="900" height="400"
                     styleClass="novaModal" onbeforeshow="formatJSON()">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Informações"/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText
                        styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                        id="hidelinkEnotas"/>
                <rich:componentControl for="modalEnotas"
                                       attachTo="hidelinkEnotas" operation="hide"
                                       event="onclick"/>
            </h:panelGroup>
        </f:facet>
        <a4j:form id="formEnotas" ajaxSubmit="true">
            <pre id="json" style="white-space: pre-wrap"></pre>
        </a4j:form>
    </rich:modalPanel>

    <rich:modalPanel id="modalInfoServico" autosized="true"
                     shadowOpacity="true" width="850" styleClass="novaModal">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Informações Serviço"/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText
                        styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                        id="hidelinkModalInfoServico"/>
                <rich:componentControl for="modalInfoServico"
                                       attachTo="hidelinkModalInfoServico" operation="hide"
                                       event="onclick"/>
            </h:panelGroup>
        </f:facet>
        <a4j:form>
            <h:panelGroup id="panelInfoServico">
                <h:panelGroup layout="block" style="text-align: center;">

                    <rich:dataTable styleClass="tabelaDados semZebra"
                                    id="tblInfoServico"
                                    value="#{ConfiguracaoNotaFiscalControle.listaInfoServicoTO}"
                                    var="serv" rows="20" rowKeyVar="status">

                        <rich:column>
                            <f:facet name="header">
                                <h:outputText value="COD."/>
                            </f:facet>
                            <h:outputText title="#{serv.descricao}"
                                          styleClass="tooltipster"
                                          value="#{serv.codigo}"/>
                        </rich:column>

                        <rich:column>
                            <f:facet name="header">
                                <h:outputText value="TRIBUTAÇÃO MUNICÍPIO"/>
                            </f:facet>
                            <h:outputText value="#{serv.codTributacaoMunicipio}"/>
                        </rich:column>

                        <rich:column>
                            <f:facet name="header">
                                <h:outputText value="LISTA SERVIÇO"/>
                            </f:facet>
                            <h:outputText value="#{serv.codListaServico}"/>
                        </rich:column>

                        <rich:column>
                            <f:facet name="header">
                                <h:outputText value="COD. IBGE CIDADE"/>
                            </f:facet>
                            <h:outputText value="#{serv.codigoIBGECidade}"/>
                        </rich:column>

                        <rich:column>
                            <f:facet name="header">
                                <h:outputText value="ALÍQUOTA SUGERIDA"/>
                            </f:facet>
                            <h:outputText value="#{serv.aliquotaSugerida}"/>
                        </rich:column>
                    </rich:dataTable>
                </h:panelGroup>
            </h:panelGroup>
        </a4j:form>
    </rich:modalPanel>

    <rich:modalPanel id="modalInfoCidade" autosized="true"
                     shadowOpacity="true" width="800" height="400" styleClass="novaModal">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Informações Cidade"/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText
                        styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                        id="hidelinkModalInfoCidade"/>
                <rich:componentControl for="modalInfoCidade"
                                       attachTo="hidelinkModalInfoCidade" operation="hide"
                                       event="onclick"/>
            </h:panelGroup>
        </f:facet>
        <a4j:form>
            <h:panelGroup layout="block" id="panelInfoCidade" style="overflow-y: auto; height: 400px;">
                <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar"
                             columnClasses="classEsquerda, classDireita" width="100%"
                             headerClass="subordinado">

                    <h:outputText value="Tipo Autenticação:"/>
                    <h:outputText
                            value="#{ConfiguracaoNotaFiscalControle.infoCidadeEnotasTO.tipoAutenticacaoApresentar}"/>

                    <h:outputText value="Help Tipo Autenticação:"
                                  rendered="#{not empty ConfiguracaoNotaFiscalControle.infoCidadeEnotasTO.helpTipoAutenticacaoTO.certificadoDigital ||
                                  not empty ConfiguracaoNotaFiscalControle.infoCidadeEnotasTO.helpTipoAutenticacaoTO.usuario ||
                                  not empty ConfiguracaoNotaFiscalControle.infoCidadeEnotasTO.helpTipoAutenticacaoTO.senha ||
                                  not empty ConfiguracaoNotaFiscalControle.infoCidadeEnotasTO.helpTipoAutenticacaoTO.token ||
                                  not empty ConfiguracaoNotaFiscalControle.infoCidadeEnotasTO.helpTipoAutenticacaoTO.fraseSecreta}"/>
                    <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar"
                                 rendered="#{not empty ConfiguracaoNotaFiscalControle.infoCidadeEnotasTO.helpTipoAutenticacaoTO.certificadoDigital ||
                                  not empty ConfiguracaoNotaFiscalControle.infoCidadeEnotasTO.helpTipoAutenticacaoTO.usuario ||
                                  not empty ConfiguracaoNotaFiscalControle.infoCidadeEnotasTO.helpTipoAutenticacaoTO.senha ||
                                  not empty ConfiguracaoNotaFiscalControle.infoCidadeEnotasTO.helpTipoAutenticacaoTO.token ||
                                  not empty ConfiguracaoNotaFiscalControle.infoCidadeEnotasTO.helpTipoAutenticacaoTO.fraseSecreta}"
                                 columnClasses="classEsquerda, classDireita" width="100%"
                                 headerClass="subordinado">

                        <h:outputText value="Certificado Digital:"
                                      rendered="#{not empty ConfiguracaoNotaFiscalControle.infoCidadeEnotasTO.helpTipoAutenticacaoTO.certificadoDigital}"/>
                        <h:outputText
                                rendered="#{not empty ConfiguracaoNotaFiscalControle.infoCidadeEnotasTO.helpTipoAutenticacaoTO.certificadoDigital}"
                                value="#{ConfiguracaoNotaFiscalControle.infoCidadeEnotasTO.helpTipoAutenticacaoTO.certificadoDigital}"/>

                        <h:outputText value="Usuário:"
                                      rendered="#{not empty ConfiguracaoNotaFiscalControle.infoCidadeEnotasTO.helpTipoAutenticacaoTO.usuario}"/>
                        <h:outputText
                                rendered="#{not empty ConfiguracaoNotaFiscalControle.infoCidadeEnotasTO.helpTipoAutenticacaoTO.usuario}"
                                value="#{ConfiguracaoNotaFiscalControle.infoCidadeEnotasTO.helpTipoAutenticacaoTO.usuario}"/>

                        <h:outputText value="Senha:"
                                      rendered="#{not empty ConfiguracaoNotaFiscalControle.infoCidadeEnotasTO.helpTipoAutenticacaoTO.senha}"/>
                        <h:outputText
                                rendered="#{not empty ConfiguracaoNotaFiscalControle.infoCidadeEnotasTO.helpTipoAutenticacaoTO.senha}"
                                value="#{ConfiguracaoNotaFiscalControle.infoCidadeEnotasTO.helpTipoAutenticacaoTO.senha}"/>

                        <h:outputText value="Token:"
                                      rendered="#{not empty ConfiguracaoNotaFiscalControle.infoCidadeEnotasTO.helpTipoAutenticacaoTO.token}"/>
                        <h:outputText
                                rendered="#{not empty ConfiguracaoNotaFiscalControle.infoCidadeEnotasTO.helpTipoAutenticacaoTO.token}"
                                value="#{ConfiguracaoNotaFiscalControle.infoCidadeEnotasTO.helpTipoAutenticacaoTO.token}"/>

                        <h:outputText value="Frase Secreta:"
                                      rendered="#{not empty ConfiguracaoNotaFiscalControle.infoCidadeEnotasTO.helpTipoAutenticacaoTO.fraseSecreta}"/>
                        <h:outputText
                                rendered="#{not empty ConfiguracaoNotaFiscalControle.infoCidadeEnotasTO.helpTipoAutenticacaoTO.fraseSecreta}"
                                value="#{ConfiguracaoNotaFiscalControle.infoCidadeEnotasTO.helpTipoAutenticacaoTO.fraseSecreta}"/>

                    </h:panelGrid>

                    <h:outputText value="Assinatura Digital:"/>
                    <h:outputText
                            value="#{ConfiguracaoNotaFiscalControle.infoCidadeEnotasTO.assinaturaDigitalApresentar}"/>

                    <h:outputText value="Usa AEDF:"/>
                    <h:outputText
                            value="#{ConfiguracaoNotaFiscalControle.infoCidadeEnotasTO.usaAEDFApresentar}"/>

                    <%--                    <h:outputText value="Campo Login Provedor:"/>--%>
                    <%--                    <h:outputText value="#{ConfiguracaoNotaFiscalControle.infoCidadeEnotasTO.campoLoginProvedor}"/>--%>

                    <h:outputText value="Suporta Cancelamento:"/>
                    <h:outputText
                            value="#{ConfiguracaoNotaFiscalControle.infoCidadeEnotasTO.suportaCancelamentoApresentar}"/>

                    <h:outputText value="Usa Regime Especial Tributação:"/>
                    <h:outputText
                            value="#{ConfiguracaoNotaFiscalControle.infoCidadeEnotasTO.usaRegimeEspecialTributacaoApresentar}"/>

                    <h:outputText value="Help Regime Especial Tributação:"
                                  rendered="#{not empty ConfiguracaoNotaFiscalControle.infoCidadeEnotasTO.helpRegimeEspecialTributacao}"/>
                    <h:outputText
                            rendered="#{not empty ConfiguracaoNotaFiscalControle.infoCidadeEnotasTO.helpRegimeEspecialTributacao}"
                            value="#{ConfiguracaoNotaFiscalControle.infoCidadeEnotasTO.helpRegimeEspecialTributacao}"/>

                    <h:outputText value="Regime Especial Tributação:"/>
                    <h:dataTable width="100%" headerClass="subordinado"
                                 styleClass="tabFormSubordinada"
                                 rowClasses="linhaImpar, linhaPar"
                                 columnClasses="colunaAlinhamento, colunaAlinhamento"
                                 value="#{ConfiguracaoNotaFiscalControle.infoCidadeEnotasTO.listaRegimesEspecialTributacao}"
                                 var="reg">

                        <h:column>
                            <f:facet name="header">
                                <h:outputText value="Código"/>
                            </f:facet>
                            <h:outputText value="#{reg.codigo}"/>
                        </h:column>

                        <h:column>
                            <f:facet name="header">
                                <h:outputText value="Descrição"/>
                            </f:facet>
                            <h:outputText value="#{reg.nome}"/>
                        </h:column>
                    </h:dataTable>

                    <h:outputText value="Usa Código Serviço Municipal:"/>
                    <h:outputText
                            value="#{ConfiguracaoNotaFiscalControle.infoCidadeEnotasTO.usaCodigoServicoMunicipalApresentar}"/>

                    <h:outputText value="Help Código Serviço Municipal:"
                                  rendered="#{not empty ConfiguracaoNotaFiscalControle.infoCidadeEnotasTO.helpCodigoServicoMunicipal}"/>
                    <h:outputText
                            rendered="#{not empty ConfiguracaoNotaFiscalControle.infoCidadeEnotasTO.helpCodigoServicoMunicipal}"
                            value="#{ConfiguracaoNotaFiscalControle.infoCidadeEnotasTO.helpCodigoServicoMunicipal}"/>

                    <h:outputText value="Usa Descrição Serviço:"/>
                    <h:outputText
                            value="#{ConfiguracaoNotaFiscalControle.infoCidadeEnotasTO.usaDescricaoServicoApresentar}"/>

                    <h:outputText value="Help Descrição Serviço:"
                                  rendered="#{not empty ConfiguracaoNotaFiscalControle.infoCidadeEnotasTO.helpDescricaoServico}"/>
                    <h:outputText
                            rendered="#{not empty ConfiguracaoNotaFiscalControle.infoCidadeEnotasTO.helpDescricaoServico}"
                            value="#{ConfiguracaoNotaFiscalControle.infoCidadeEnotasTO.helpDescricaoServico}"/>

                    <h:outputText value="Usa CNAE:"/>
                    <h:outputText value="#{ConfiguracaoNotaFiscalControle.infoCidadeEnotasTO.usaCNAEApresentar}"/>

                    <h:outputText value="Help CNAE:"
                                  rendered="#{not empty ConfiguracaoNotaFiscalControle.infoCidadeEnotasTO.helpCNAE}"/>
                    <h:outputText rendered="#{not empty ConfiguracaoNotaFiscalControle.infoCidadeEnotasTO.helpCNAE}"
                                  value="#{ConfiguracaoNotaFiscalControle.infoCidadeEnotasTO.helpCNAE}"/>

                    <h:outputText value="Usa Item Lista Serviço:"/>
                    <h:outputText
                            value="#{ConfiguracaoNotaFiscalControle.infoCidadeEnotasTO.usaItemListaServicoApresentar}"/>

                    <h:outputText value="Help Item Lista Serviço:"
                                  rendered="#{not empty ConfiguracaoNotaFiscalControle.infoCidadeEnotasTO.helpItemListaServico}"/>
                    <h:outputText
                            rendered="#{not empty ConfiguracaoNotaFiscalControle.infoCidadeEnotasTO.helpItemListaServico}"
                            value="#{ConfiguracaoNotaFiscalControle.infoCidadeEnotasTO.helpItemListaServico}"/>

                    <h:outputText value="Help Inscrição Municipal:"
                                  rendered="#{not empty ConfiguracaoNotaFiscalControle.infoCidadeEnotasTO.helpInscricaoMunicipal}"/>
                    <h:outputText
                            rendered="#{not empty ConfiguracaoNotaFiscalControle.infoCidadeEnotasTO.helpInscricaoMunicipal}"
                            value="#{ConfiguracaoNotaFiscalControle.infoCidadeEnotasTO.helpInscricaoMunicipal}"/>

                    <%--                    <h:outputText value="Suporta Emissão NFe sem Cliente:"/>--%>
                    <%--                    <h:outputText--%>
                    <%--                            value="#{ConfiguracaoNotaFiscalControle.infoCidadeEnotasTO.suportaEmissaoNFeSemClienteApresentar}"/>--%>

                    <h:outputText value="Suporta Emissão NFe Cliente sem CPF:"/>
                    <h:outputText
                            value="#{ConfiguracaoNotaFiscalControle.infoCidadeEnotasTO.suportaEmissaoNFeClienteSemCpfApresentar}"/>

                    <h:outputText value="Suporta Emissão NFe Cliente sem Endereço:"/>
                    <h:outputText
                            value="#{ConfiguracaoNotaFiscalControle.infoCidadeEnotasTO.suportaEmissaoNFeClienteSemEnderecoApresentar}"/>

                    <%--                    <h:outputText value="Suporta Cancelamento NFe sem Cliente:"/>--%>
                    <%--                    <h:outputText--%>
                    <%--                            value="#{ConfiguracaoNotaFiscalControle.infoCidadeEnotasTO.suportaCancelamentoNFeSemClienteApresentar}"/>--%>

                    <h:outputText value="Suporta Cancelamento NFe Cliente sem CPF:"/>
                    <h:outputText
                            value="#{ConfiguracaoNotaFiscalControle.infoCidadeEnotasTO.suportaCancelamentoNFeClienteSemCpfApresentar}"/>
                </h:panelGrid>
            </h:panelGroup>
        </a4j:form>
    </rich:modalPanel>

    <rich:modalPanel id="modalInfoEmpresa" autosized="true"
                     shadowOpacity="true" width="800" styleClass="novaModal">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Informações Empresa eNotas"/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText
                        styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                        id="hidelinkModalInfoEmpresa"/>
                <rich:componentControl for="modalInfoEmpresa"
                                       attachTo="hidelinkModalInfoEmpresa" operation="hide"
                                       event="onclick"/>
            </h:panelGroup>
        </f:facet>
        <a4j:form>
            <h:panelGroup layout="block" id="panelInfoEmpresaEnotas" style="padding: 10px;">
                <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar"
                             columnClasses="classEsquerda, classDireita" width="100%"
                             headerClass="subordinado">

                    <h:outputText value="ID eNotas:"/>
                    <h:outputText value="#{ConfiguracaoNotaFiscalControle.infoEmpresaEnotasTO.id}"/>

                    <h:outputText value="Status:"/>
                    <h:outputText value="#{ConfiguracaoNotaFiscalControle.infoEmpresaEnotasTO.status}"/>

                    <h:outputText value="Dados obrigatórios preenchidos:"/>
                    <h:outputText
                            value="#{ConfiguracaoNotaFiscalControle.infoEmpresaEnotasTO.dadosObrigatoriosPreenchidosApresentar}"/>

                    <h:outputText value="UF:"/>
                    <h:outputText value="#{ConfiguracaoNotaFiscalControle.infoEmpresaEnotasTO.uf}"/>

                    <h:outputText value="Cidade:"/>
                    <h:outputText value="#{ConfiguracaoNotaFiscalControle.infoEmpresaEnotasTO.cidade}"/>

                    <c:if test="${ConfiguracaoNotaFiscalControle.infoEmpresaEnotasTO.prazo > 0}">
                        <h:outputText value="Prazo:"/>
                        <h:outputText value="#{ConfiguracaoNotaFiscalControle.infoEmpresaEnotasTO.prazo}"
                                      styleClass="tooltipster"
                                      title="Indica quando a empresa será liberada pra emissão de notas fiscais (Válido somente pra prefeituras não integradas)"/>
                        <h:outputText value=""/>
                        <h:outputText
                                value="Indica quando a empresa será liberada pra emissão de notas fiscais (Válido somente pra prefeituras não integradas)"/>
                    </c:if>

                </h:panelGrid>
            </h:panelGroup>
        </a4j:form>
    </rich:modalPanel>

    <jsp:include page="includes/include_modal_mensagem_generica.jsp"/>

    <h:inputHidden id="jsonEnotas" value="#{ConfiguracaoNotaFiscalControle.infoEnotas}"/>
    <h:inputHidden id="jsonEnotasJSON" value="#{ConfiguracaoNotaFiscalControle.infoEnotasJSON}"/>

    <script>
        function formatJSON() {
            document.getElementById("json").innerHTML = JSON.stringify(JSON.parse(document.getElementById("jsonEnotasJSON").value), null, 3);
        }
    </script>
</f:view>
<script>
    try {
        document.getElementById("form:descricao").focus();
    } catch (ex) {
    }

    jQuery('.tooltipster').tooltipster({
        theme: 'tooltipster-light',
        position: 'bottom',
        animation: 'grow',
        contentAsHTML: true
    });
</script>

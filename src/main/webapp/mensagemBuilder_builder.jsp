<%@include file="includes/imports.jsp" %>
<h:panelGroup rendered="#{MensagemBuilderControle.building and not MensagemBuilderControle.configEnvio}">

    <h:inputHidden id="htmlid" value="#{MensagemBuilderControle.html}"/>
    <h:inputHidden id="configsid" value="#{MensagemBuilderControle.configs}"/>
    <h:inputHidden id="rowsid" value="#{MensagemBuilderControle.rows}"/>
    <a4j:jsFunction name="abrirModalGravar"
                    reRender="modalsave"
                    oncomplete="Richfaces.showModalPanel('modalsave')"
                    action="#{MensagemBuilderControle.initGravar}"></a4j:jsFunction>

    <a4j:jsFunction name="prosseguirEnvio"
                    reRender="mensagembuilder"
                    action="#{MensagemBuilderControle.prosseguirEnvio}"></a4j:jsFunction>

    <c:if test="${not MensagemBuilderControle.meioSms and not MensagemBuilderControle.meioApp and not MensagemBuilderControle.meioBotConversa and not MensagemBuilderControle.meioGymbotPro }">
        <div style="margin-top: 20px; color: #000000;" class="tags">Adicione as tags:
            <b class="tooltipster" title="Clique para copiar" onclick="copiar(this)">TAG_NOME</b> (nome completo),
            <b class="tooltipster" title="Clique para copiar" onclick="copiar(this)">TAG_PNOME</b> (primeiro nome),
            <b class="tooltipster" title="Clique para copiar" onclick="copiar(this)">TAG_PESQUISA</b> (pesquisa),
            <b class="tooltipster" title="Clique para copiar" onclick="copiar(this)">TAG_BOLETO</b> (boleto),
            <c:if test="${MalaDiretaControle.existeConvenioCobrancaPix}">
                <b class="tooltipster" title="Clique para copiar </br> <b>Importante: </b> Escolhendo a Tag Pix, o sistema ir� enviar o modelo padr�o do sistema e ignorar o modelo criado pelo usu�rio."
                   onclick="copiar(this)">TAG_PIX</b> (PIX),
            </c:if>
            <b class="tooltipster" title="Clique para copiar" onclick="copiar(this)">TAG_PAGONLINE</b> (link de
            pagamento),
            <b class="tooltipster" title="Clique para copiar" onclick="copiar(this)">TAG_CADCARTAO</b> (link para
            cadastro do cart�o para cobran�a na recorr�ncia).
            Clique <a onclick="Richfaces.showModalPanel('maisTags')" style="font-size: 12px;">aqui</a> para tags da
            empresa,
            <a onclick="Richfaces.showModalPanel('maisTagsRemessa')" style="font-size: 12px;">aqui</a> para tags de
            remessa ou
            <a onclick="Richfaces.showModalPanel('maisTagsBoasVindas')" style="font-size: 12px;">aqui</a> para tags de
            email de contrato do aluno.
        </div>
    </c:if>

    <c:if test="${MensagemBuilderControle.meioSms or MensagemBuilderControle.meioApp }">
        <diV style="margin-top: 20px; color: #000000;" class="tags">Adicione as tags:
            <b class="tooltipster" title="Clique para copiar" onclick="copiar(this)">TAG_NOME</b> (nome completo),
            <b class="tooltipster" title="Clique para copiar" onclick="copiar(this)">TAG_PNOME</b> (primeiro nome),
            <b class="tooltipster" title="Clique para copiar" onclick="copiar(this)">TAG_PESQUISA</b> (pesquisa),
            <c:if test="${MensagemBuilderControle.meioSms}">
                <b class="tooltipster" title="Clique para copiar" onclick="copiar(this)">TAG_PAGONLINE</b> (link de pagamento),
            </c:if>

            Clique <a onclick="Richfaces.showModalPanel('maisTags')" style="font-size: 12px;">aqui</a> para tags da
            empresa

        </diV>
        <c:if test="${MensagemBuilderControle.meioSms }">
                <div style="margin-top: 20px; color: #000000;" class="tags">"<strong>Observa��o:</strong>Limite de 140 caracteres para SMS ! Lembre-se que a tag de pagamento (TAG_PAGONLINE) ocupa 95 caracteres.
                    Se o template de sms criado ultrapassar o limite, ser� enviado um SMS padr�o  : 'Ol�, segue seu link de pagamento: TAG_PAGONLINE'.
                    Cuidado ao usar m�ltiplas tags, como TAG_NOME e TAG_PNOME.."
                </div>
        </c:if>
        <c:if test="${MensagemBuilderControle.meioApp }">
            <div style="margin-top: 20px; color: #000000;" class="tags">"<strong>Observa��o:</strong>Limite de 255 caracteres para APP !  Cuidado ao usar m�ltiplas tags, como TAG_NOME e TAG_PNOME..."
            </div>
        </c:if>
    </c:if>

    <c:if test="${not MensagemBuilderControle.meioEmail}">

        <h:panelGrid columns="1" style="margin-top: 40px"
                     columnClasses="colunaCentralizada" width="100%" >

            <h:inputTextarea rendered="#{MensagemBuilderControle.meioApp}"
                    onkeypress="somaApp(this.value);" onkeyup="somaApp(this.value);"
                    style="align:center;" id="comentarioTextAreaapp" cols="100" rows="2"
                    value="#{MensagemBuilderControle.texto}"/>


             <h:panelGrid  rendered="#{MensagemBuilderControle.meioBotConversa}" columns="2" style="margin-top: 40px"
                     width="100%"  >

                 <div class="col">
                 <div class="preview-email" style="border: 1px solid #d5d2d2; margin: 10px; border-radius: 10px; overflow: hidden;">

                 <table  width="95%" border="0" align="center" cellpadding="5" cellspacing="5" bgcolor="#fff"
                 style="padding: 30px; ">
                 <tr   style="padding-bottom: 5px;">

                 <td align="left" valign="top" style="padding-top: 11px;padding-bottom: 5px;width:200px;">
                     GymBot Fluxo:
                 </td>
                     <td  >
                         <div class="linha" style="display: flex">
                         <h:selectOneMenu value="#{MensagemBuilderControle.webHookFluxo}"
                                          rendered="#{MensagemBuilderControle.meioBotConversa}"
                                          id="selectFluxo"
                                          style="margin-top: 8px;width: 50%; font-size: 12px">
                             <f:selectItems value="#{MensagemBuilderControle.getListaFluxo()}"/>

                         </h:selectOneMenu>
                     </td>
                 </tr>
                 </table>
                 </div>
                 </div>
            </h:panelGrid>

            <h:panelGrid  rendered="#{MensagemBuilderControle.meioGymbotPro}" columns="3" style="margin-top: 40px"
                          width="100%"  >

                <div class="col">
                    <div class="preview-email" style="border: 1px solid #d5d2d2; margin: 10px; border-radius: 10px; overflow: hidden;">

                        <table  width="95%" border="0" align="center" cellpadding="5" cellspacing="5" bgcolor="#fff"
                                style="padding: 30px; ">
                            <tr   style="padding-bottom: 5px;">

                                <td align="left" valign="top" style="padding-top: 11px;padding-bottom: 5px;width:200px;">
                                    GymBot Pro Chatbot de Automa��o:
                                </td>
                                <td  >
                                    <div class="linha" style="display: flex">
                                        <h:selectOneMenu value="#{MensagemBuilderControle.idConfiguracaoGymbotPro}"
                                                         rendered="#{MensagemBuilderControle.meioGymbotPro}"
                                                         id="selectFluxo1"
                                                         style="margin-top: 8px;width: 50%; font-size: 12px">
                                            <f:selectItems value="#{MensagemBuilderControle.getListaFluxoGymbotPro()}"/>

                                        </h:selectOneMenu>
                                </td>
                            </tr>
                        </table>
                    </div>
                </div>
            </h:panelGrid>


            <h:inputTextarea rendered="#{MensagemBuilderControle.meioSms}"
                    onkeypress="soma(this.value);" onkeyup="soma(this.value);"
                    style="align:center;" id="comentarioTextArea" cols="100" rows="2"
                    value="#{MensagemBuilderControle.texto}"/>

            <rich:toolTip for="comentarioTextArea" followMouse="true"
                          rendered="#{MalaDiretaControle.toolTipSMS and MensagemBuilderControle.meioSms}"
                          direction="top-right"  style="width:300px; height:#{MalaDiretaControle.tamanhoToolTipSMS}; " showDelay="200">
                <h:outputText styleClass="tituloCampos" escape="false"
                              value="#{MalaDiretaControle.termosFiscalizados}" />
            </rich:toolTip>



            <h:panelGroup id="panelTamanhoRestante" style="align:center;" layout="block"
                          rendered="#{MensagemBuilderControle.meioSms || MensagemBuilderControle.meioApp}">
                <h:inputText style="align:center;" disabled="true" size="3" id="tamanhoRestante"/>
            </h:panelGroup>

        </h:panelGrid>

        <script>
            function init(body) {}

            function prosseguir() {
                prosseguirEnvio();
            }

            function setdados() {
                abrirModalGravar();
            }

        </script>
    </c:if>

    <c:if test="${MensagemBuilderControle.meioEmail}">
        <div id="editor-container" style="height: 700px;margin-top: 20px;"></div>
        <script>
            var editor;
            var body = ${empty MensagemBuilderControle.configs ? '{}' : MensagemBuilderControle.body}

                function init(body) {
                    editor = unlayer.createEditor({
                        id: 'editor-container',
                        locale: 'pt-BR',
                        tools: {
                            html: {
                                enabled: false
                            }
                        },
                        displayMode: 'email'
                    });
                    editor.setAppearance({
                        theme: 'light',
                        panels: {
                            tools: {
                                dock: 'left'
                            }
                        }
                    });
                    if (body) {
                        editor.loadDesign(body.design);
                    } else {
                        editor.loadDesign({
                            body: {
                                rows: [
                                    {
                                        cells: [
                                            1
                                        ],
                                        columns: [
                                            {
                                                contents: []
                                            }
                                        ],
                                        values: {
                                            backgroundColor: 'rgba(255,255,255,0)',
                                            backgroundImage: {
                                                url: '',
                                                fullWidth: true,
                                                repeat: false,
                                                center: false,
                                                cover: false
                                            },
                                            padding: '10px',
                                            columnsBackgroundColor: 'rgba(255,255,255,0)',
                                            selectable: true,
                                            draggable: true,
                                            deletable: true
                                        }
                                    }
                                ],
                                values: {
                                    backgroundColor: '#ffffff',
                                    backgroundImage: {
                                        url: '',
                                        fullWidth: true,
                                        repeat: false,
                                        center: true,
                                        cover: false
                                    },
                                    contentWidth: '600px',
                                    fontFamily: {
                                        label: 'Montserrat',
                                        value: '\'Montserrat\',sans-serif',
                                        type: 'google',
                                        weights: '400,700'
                                    }
                                }
                            }
                        });
                    }
                };

            function setdados() {
                editor.exportHtml(function (data) {
                    // debugger
                    var hiddenHtml = document.getElementById('form:htmlid');
                    var hiddenConfigs = document.getElementById('form:configsid');
                    var hiddenRows = document.getElementById('form:rowsid');
                    hiddenConfigs.value = JSON.stringify(data);
                    hiddenHtml.value = data.html;
                    hiddenRows.value = JSON.stringify(data.design.body.rows);

                    console.log(hiddenRows.value);

                    abrirModalGravar();
                })
            }

            function prosseguir() {
                editor.exportHtml(function (data) {
                    var hiddenHtml = document.getElementById('form:htmlid');
                    var hiddenConfigs = document.getElementById('form:configsid');
                    var hiddenRows = document.getElementById('form:rowsid');
                    hiddenConfigs.value = JSON.stringify(data);
                    hiddenRows.value = JSON.stringify(data.design.body.rows);
                    hiddenHtml.value = data.html;
                    prosseguirEnvio();
                })
            }


        </script>
    </c:if>

</h:panelGroup>




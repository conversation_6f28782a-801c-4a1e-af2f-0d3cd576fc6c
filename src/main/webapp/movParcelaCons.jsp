<%@page contentType="text/html;charset=UTF-8" %>
<head>
    <%@include file="/includes/include_import_minifiles.jsp" %>
</head>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<script type="text/javascript" language="javascript" src="hoverform.js"></script>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax" %>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich" %>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core" %>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<meta name="viewport" content="width=device-width, initial-scale=1.0">
<link href="beta/css/pure-min.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/bootstrap-tabs.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/pure-ext.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<script src="beta/js/jquery.js" type="text/javascript"></script>
<script src="beta/js/jquery.dataTables.min.js" type="text/javascript"></script>
<script src="beta/js/DT_bootstrap.js" type="text/javascript"></script>
<script src="beta/js/bootstrap-tab.js" type="text/javascript"></script>
<script>
    jQuery.noConflict();
</script>
<c:set var="contexto" value="${pageContext.request.contextPath}" scope="request" />
<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title>${msg_aplic.prt_MovParcela_tituloForm}</title>

    <%-- INICIO HEADER --%>
    <h:panelGroup layout="block" styleClass="pure-g-r">
        <f:facet name="header">
            <jsp:include page="topoReduzido.jsp"/>
        </f:facet>

        <h:panelGrid columns="1"
                     style="height:25px; background-image:url('./imagens/fundoBarraTopo.png');background-repeat: repeat-x;"
                     columnClasses="colunaCentralizada" width="100%">
            <h:outputText styleClass="tituloFormulario" value=" #{msg_aplic.prt_MovParcela_tituloForm}">
                <h:outputLink value="#{SuperControle.urlWiki}Cadastros:Config._Financeiras:Movimento_da_Parcela"
                              title="Clique e saiba mais: Movimento da Parcela" target="_blank"
                              rendered="#{LoginControle.permissaoAcessoMenuVO.movParcela}">
                    <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                </h:outputLink>
            </h:outputText>
        </h:panelGrid>
    </h:panelGroup>
    <%-- FIM HEADER --%>

    <h:panelGroup layout="block" styleClass="pure-g-r">
        <h:form id="form" styleClass="pure-form pure-u-1">
            <a4j:keepAlive beanName="ExportadorListaControle"/>
            <input id="chavePrimaria" type="hidden" value="" name="chavePrimaria"/>

            <%-- INICIO CONTENT --%>
            <h:panelGroup>


                <%-- INICIO COMANDOS --%>
                <h:panelGroup layout="block" styleClass="pure-g-r pure-u-11-12 margin-0-auto margin-v-10">
                    <h:panelGroup layout="block" styleClass="pure-form pure-form-aligned pure-u-2-3">
                        <a4j:commandLink id="btnExcel"
                                         styleClass="pure-button pure-button-small"
                                         actionListener="#{MovParcelaControle.exportar}"
                                         oncomplete="abrirPopup('UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/vnd.ms-excel','Transacoes', 800,200);#{ExportadorListaControle.msgAlert}"
                                         accesskey="3">
                            <f:attribute name="tipo" value="xls"/>
                            <f:attribute name="atributos"
                                         value="codigo=Código,contratoPessoa_Apresentar=Cliente,responsavel_Apresentar=Responsável,dataRegistro_Apresentar=Registro,dataVencimento_Apresentar=Vencimento,empresa_Apresentar=Empresa,situacao_Apresentar=Situação,valorParcela_Apresentar=Valor"/>
                            <f:attribute name="prefixo" value="MovimentoParcela"/>
                            <a4j:actionparam noEscape="true" name="paramsTableFiltrada" value="retornaDadosDT()"/>
                            <i class="fa-icon-excel"></i> &nbsp Excel
                        </a4j:commandLink>

                        <a4j:commandLink id="btnPDF"
                                         styleClass="pure-button pure-button-small margin-h-10"
                                         actionListener="#{MovParcelaControle.exportar}"
                                         oncomplete="abrirPopup('UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/pdf','Transacoes', 800,200);#{ExportadorListaControle.msgAlert}"
                                         accesskey="4">
                            <f:attribute name="tipo" value="pdf"/>
                            <f:attribute name="atributos"
                                         value="codigo=Código,contratoPessoa_Apresentar=Cliente,responsavel_Apresentar=Responsável,dataRegistro_Apresentar=Registro,dataVencimento_Apresentar=Vencimento,empresa_Apresentar=Empresa,situacao_Apresentar=Situação,valorParcela_Apresentar=Valor"/>
                            <f:attribute name="prefixo" value="MovimentoParcela"/>
                            <f:attribute name="titulo" value="Movimento da Parcela"/>
                            <a4j:actionparam noEscape="true" name="paramsTableFiltrada" value="retornaDadosDT()"/>
                            <i class="fa-icon-pdf"></i> &nbsp PDF
                        </a4j:commandLink>
                    </h:panelGroup>
                </h:panelGroup>
                <%-- FIM COMANDOS --%>

                <table id="tblMovParcela" class="tabelaMovParcela pure-g-r pure-u-11-12 margin-0-auto">
                    <thead>
                    <th>${msg_aplic.prt_MovParcela_label_codigo}</th>
                    <th>${msg_aplic.prt_MovParcela_label_cliente}</th>
                    <th>${msg_aplic.prt_MovParcela_label_responsavel}</th>
                    <th>${msg_aplic.prt_MovParcela_label_dataRegistro}</th>
                    <th>${msg_aplic.prt_MovParcela_label_dataVencimento}</th>
                    <th>${msg_aplic.prt_MovParcela_label_empresa}</th>
                    <th>${msg_aplic.prt_MovParcela_label_situacao}</th>
                    <th>${msg_aplic.prt_MovParcela_label_valorParcela}</th>
                    </thead>
                    <tbody></tbody>
                </table>

                <a4j:jsFunction name="jsEditar" action="#{MovParcelaControle.editar}" reRender="mensagem"/>

            </h:panelGroup>
            <%-- FIM CONTENT --%>

            <%-- INICIO FOOTER --%>
            <h:panelGroup id="mensagem" layout="block" styleClass="pure-u-11-12 margin-0-auto">
                <h:graphicImage id="iconSucesso" rendered="#{MovParcelaControle.sucesso}"
                                value="./imagens/sucesso.png"/>
                <h:graphicImage id="iconErro" rendered="#{MovParcelaControle.erro}" value="./imagens/erro.png"/>

                <h:outputText styleClass="mensagem" rendered="#{not empty MovParcelaControle.mensagem}"
                              value=" #{MovParcelaControle.mensagem}"/>
                <h:outputText styleClass="mensagemDetalhada"
                              rendered="#{not empty MovParcelaControle.mensagemDetalhada}"
                              value=" #{MovParcelaControle.mensagemDetalhada}"/>
            </h:panelGroup>
            <%-- FIM FOOTER --%>
        </h:form>

        <rich:modalPanel id="panelStatus" autosized="true">
            <h:panelGrid columns="2" styleClass="titulo3" columnClasses="titulo3">
                <h:graphicImage url="./imagens/carregando.gif" style="border:none"/>
                <h:outputText styleClass="titulo3" value="Carregando..."/>
            </h:panelGrid>
        </rich:modalPanel>
        <a4j:status onstart="Richfaces.showModalPanel('panelStatus');"
                    onstop="Richfaces.hideModalPanel('panelStatus');">
        </a4j:status>
    </h:panelGroup>

    <script src="beta/js/ext-funcs.js" type="text/javascript"></script>


    <script src="beta/js/dt-server.js" type="text/javascript"></script>
    <script>
        jQuery(window).on("load", function () {
            iniTblServer("tabelaMovParcela", "${contexto}/prest/financeiro/movParcela");
        });
    </script>
</f:view>
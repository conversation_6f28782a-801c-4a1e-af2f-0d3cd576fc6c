<%-- 
    Document   : listasRelatoriosAlunosAtestado
    Created on : 27/06/2013, 16:03:21
    Author     : <PERSON><PERSON><PERSON><PERSON>
--%>

<%@page contentType="text/html" pageEncoding="ISO-8859-1" %>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%--<script type="text/javascript" src="script/jquery.js"></script>--%>
<script type="text/javascript" language="javascript" src="hoverform.js"></script>
<head>
    <%@include file="includes/include_import_minifiles.jsp"%>
</head>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core" %>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax" %>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich" %>

<link href="beta/css/font-awesome_2.0.min.css" rel="stylesheet" type="text/css">
<jsp:include page="include_head.jsp" flush="true"/>

<style type="text/css">
    .rich-stglpanel-header {
        color: #0f4c6b;
    }

    .rich-stglpanel-header {
        background-color: #ACBECE;
        border-color: #ACBECE;
        font-size: 12px;
    }
</style>

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <a4j:loadScript src="/script/jquery.maskedinput-1.2.2.js" />
    <h:form id="form">
        <h:panelGroup layout="block" style="display: inline-table;width: 100%;" styleClass="fundoCinza">
            <h:panelGroup layout="block" styleClass="bgtop topoZW" rendered="#{MenuControle.apresentarTopo}">
                <jsp:include page="include_topo_novo.jsp" flush="true"/>
                <jsp:include page="include_menu_zw_flat.jsp" flush="true"/>
                <rich:jQuery selector=".itemRelatorios" query="addClass('menuItemAtual')"/>
            </h:panelGroup>

            <h:panelGroup layout="block" styleClass="caixaCorpo">
                <h:panelGroup layout="block" style="height: 80%;width: 100%">
                    <h:panelGroup layout="block" styleClass="caixaMenuLatel">
                        <h:panelGroup layout="block" styleClass="container-imagem container-conteudo-central">
                            <h:panelGroup layout="block" styleClass="container-box zw_ui especial ">
                                <h:panelGroup styleClass="container-box-header" layout="block">
                                    <h:panelGroup layout="block" styleClass="margin-box">
                                        <h:outputText value="Clientes com Atestado" styleClass="container-header-titulo"/>
                                        <h:outputLink styleClass="linkWiki"
                                                      value="#{SuperControle.urlBaseConhecimento}como-ter-uma-relacao-dos-alunos-que-possuem-atestado-medico-lancado/"
                                                      title="Clique e saiba mais: Relatório Clientes Atestado"
                                                      target="_blank">
                                            <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                                        </h:outputLink>
                                    </h:panelGroup>
                                </h:panelGroup>
                                <h:panelGroup  layout="block" styleClass="margin-box">

                                    <h:panelGroup rendered="#{LRAlunosAtestadoControle.permissaoConsultaTodasEmpresas}"
                                                  style="padding-left: 10px;padding-top: 15px;padding-bottom: 15px;border: 1px solid #ACBECE;"
                                                  layout="block">
                                        <h:outputText style="margin-left: 7px" styleClass="text" value="Empresas: "/>
                                        <h:selectOneMenu value="#{LRAlunosAtestadoControle.filtroEmpresa}">
                                            <f:selectItems  value="#{LRAlunosAtestadoControle.listaEmpresas}" />
                                            <a4j:support event="onchange"
                                                         action="#{LRAlunosAtestadoControle.recarregarTela}"
                                                         reRender="consultores, professores"/>
                                        </h:selectOneMenu>
                                    </h:panelGroup>

                                    <rich:simpleTogglePanel switchType="client" label="Dados do atestado"
                                                            style="margin-top: 8px" opened="true"
                                                            onexpand="false">
                                        <h:panelGrid columns="8">
                                            <h:panelGroup id="dataAtestado">
                                                <%--intervalo de datas de alunos com atestado--%>
                                                <h:outputText styleClass="text"
                                                              value="Data atestado: "></h:outputText>
                                                <rich:calendar id="inicioAtestado"
                                                               value="#{LRAlunosAtestadoControle.inicioAtestado}"
                                                               inputSize="6"
                                                               inputClass="form"
                                                               oninputblur="blurinput(this);"
                                                               oninputfocus="focusinput(this);"
                                                               oninputchange="return validar_Data(this.id);"
                                                               datePattern="dd/MM/yyyy"
                                                               enableManualInput="true"
                                                               zindex="2"
                                                               showWeeksBar="false"/>

                                                <rich:spacer width="5px"/>
                                                <h:outputText styleClass="text"
                                                              value=" até "></h:outputText>
                                                <rich:calendar id="fimAtestado"
                                                               value="#{LRAlunosAtestadoControle.fimAtestado}"
                                                               inputSize="6"
                                                               inputClass="form"
                                                               oninputblur="blurinput(this);"
                                                               oninputfocus="focusinput(this);"
                                                               oninputchange="return validar_Data(this.id);"
                                                               datePattern="dd/MM/yyyy"
                                                               enableManualInput="true"
                                                               zindex="2"
                                                               showWeeksBar="false"/>
                                                <rich:spacer width="5px"/>
                                                <rich:jQuery id="mskData" selector=".rich-calendar-input" timing="onload" query="mask('99/99/9999')" />
                                                <a4j:commandButton id="limparData"
                                                                   action="#{LRAlunosAtestadoControle.limparData}"
                                                                   image="/images/limpar.gif"
                                                                   title="Limpar período de atestado."
                                                                   reRender="inicioAtestado, fimAtestado"/>
                                            </h:panelGroup>
                                        </h:panelGrid>
                                    </rich:simpleTogglePanel>
                                    <%-- ------------------------------- ABA CONSULTORES   ------------------------------------ --%>
                                    <rich:simpleTogglePanel switchType="client" label="Consultores"
                                                            opened="false" onexpand="true">
                                        <h:panelGroup id="consultores">
                                            <rich:dataGrid id="grupoConsultoresRelAtestado"
                                                    value="#{LRAlunosAtestadoControle.listaConsultores}"
                                                    var="consultor" width="100%" columns="4">
                                                <h:selectBooleanCheckbox id="checkBoxConsultorRelAtestado"
                                                        value="#{consultor.colaboradorEscolhido}">
                                                </h:selectBooleanCheckbox>

                                                <h:outputText value="#{consultor.pessoa.nome}">
                                                </h:outputText>
                                            </rich:dataGrid>
                                        </h:panelGroup>
                                    </rich:simpleTogglePanel>
                                    <%-- ------------------------------- ABA PROFESSORES   ------------------------------------ --%>
                                    <rich:simpleTogglePanel switchType="client" label="Professores"
                                                            opened="false"
                                                            onexpand="true">
                                        <h:panelGroup id="professores">
                                            <rich:dataGrid id="grupoProfessorRelAtestado"
                                                    value="#{LRAlunosAtestadoControle.listaProfessores}"
                                                    var="professor" width="100%" columns="4">
                                                <h:selectBooleanCheckbox id="checkBoxProfessorRelAtestado"
                                                        value="#{professor.colaboradorEscolhido}">
                                                </h:selectBooleanCheckbox>

                                                <h:outputText value="#{professor.pessoa.nome} ">
                                                </h:outputText>
                                            </rich:dataGrid>
                                        </h:panelGroup>
                                    </rich:simpleTogglePanel>

                                    <h:panelGroup layout="block" styleClass="container-botoes colunaEsquerda">
                                        <a4j:commandLink styleClass="botoes nvoBt" id="btnConsultarRelAtestado"
                                                         style="margin-left: 0px"
                                                         oncomplete="#{LRAlunosAtestadoControle.mensagemNotificar}"
                                                         action="#{LRAlunosAtestadoControle.consultarAlunosAtestado}"
                                                         reRender="groupResultados, imprimir">
                                            Consultar&nbsp<i class="fa-icon-search"></i>
                                        </a4j:commandLink>

                                        <a4j:commandLink style="margin-left:5px;margin-top: 8px; padding:5px 7px;" id="btnLimparFiltroRelAtestado"
                                                         action="#{LRAlunosAtestadoControle.limparFiltros}"
                                                         styleClass="botoes nvoBt btSec"
                                                         reRender="form">
                                            Limpar filtros&nbsp <i class="fa-icon-eraser"></i>
                                        </a4j:commandLink>
                                    </h:panelGroup>
                                    <h:panelGroup id="imprimir">
                                        <a4j:commandLink id="exportarExcel" style="margin-left:5px;"
                                                           actionListener="#{ExportadorListaControle.exportar}"
                                                           rendered="#{not empty LRAlunosAtestadoControle.resultado}"
                                                         oncomplete="#{ExportadorListaControle.mensagemNotificar}#{ExportadorListaControle.operacaoOnComplete}#{ExportadorListaControle.msgAlert}"
                                                           accesskey="2" styleClass="botoes linkPadrao">
                                            <f:attribute name="lista"
                                                         value="#{LRAlunosAtestadoControle.resultado}"/>
                                            <f:attribute name="tipo" value="xls"/>
                                            <f:attribute name="itemExportacao" value="relAtestados"/>
                                            <f:attribute name="atributos" value="matricula=Matrícula,nome=Nome,telefone=Telefones,email=E-mail,dataNascimentoApresentar=Nascimento
                                                                     ,situacaoCliente=Situação,sexo=Sexo Biológico,dataMatriculaApresentar=Data Matrícula,dataInicioPlanoApresentar=Início Plano
                                                                     ,dataVencimentoPlanoApresentar=Venc. Plano,plano=Plano,dataCadastroApresentar=Data Cadastro
                                                                     ,consultor=Consultor/Professor,estadoCivil=Estado Civil,profissao=Profissão,dataOperacaoApresentar=Lançamento Operação,dataAtestadoApresentar=Início Atestado,dataFimOperacaoApresentar=Fim Atestado,dataUltimoAcessoApresentar=Últ.Acesso,nomeEmpresa=Empresa"/>
                                            <f:attribute name="prefixo" value="ClientesAtestado"/>
                                            <h:outputText title="Exportar para o formato Excel" styleClass="btn-print-2 excel"/>
                                        </a4j:commandLink>
                                        <a4j:commandLink style="margin-left:5px;margin-top: 8px;"
                                                         styleClass="linkPadrao texto-cor-azul"
                                                           rendered="#{not empty LRAlunosAtestadoControle.resultado}"
                                                           reRender="relatorioImprimir"
                                                           action="#{LRAlunosAtestadoControle.prepararImpr}"
                                                           oncomplete="Richfaces.showModalPanel('relatorioImprimir');">
                                            <h:outputText title="Exportar para o formato PDF" styleClass="btn-print-2 pdf"/>
                                        </a4j:commandLink>
                                    </h:panelGroup>
                                    <%---------------------------------TABELA DO RELATORIO---------------------------------------%>
                                    <h:panelGroup id="groupResultados">
                                        <rich:dataTable width="100%"
                                                        value="#{LRAlunosAtestadoControle.resultado}"
                                                        id="resultados"
                                                        var="item"
                                                        rows="30"
                                                        style="margin-top: 8px"
                                                        rendered="#{not empty LRAlunosAtestadoControle.resultado}">

                                            <rich:column id="matricula" sortBy="#{item.matricula}">
                                                <f:facet name="header">
                                                    <h:outputText value="Matricula"></h:outputText>
                                                </f:facet>
                                                <a4j:commandLink
                                                        value="#{item.matricula}"
                                                        actionListener="#{LRAlunosAtestadoControle.prepareEditar}"
                                                        action="#{LRAlunosAtestadoControle.irParaTelaCliente}"
                                                        oncomplete="abrirPopup('clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);"/>
                                            </rich:column>

                                            <rich:column id="nome" sortBy="#{item.nome}">
                                                <f:facet name="header">
                                                    <h:outputText value="Nome"></h:outputText>
                                                </f:facet>
                                                <a4j:commandLink
                                                        value="#{item.nome}"
                                                        actionListener="#{LRAlunosAtestadoControle.prepareEditar}"
                                                        action="#{LRAlunosAtestadoControle.irParaTelaCliente}"
                                                        oncomplete="abrirPopup('clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);"/>
                                            </rich:column>
                                            <rich:column id="dataNascimento"
                                                         sortBy="#{item.dataNascimento}">
                                                <f:facet name="header">
                                                    <h:outputText value="Nascimento"></h:outputText>
                                                </f:facet>
                                                <a4j:commandLink
                                                        value="#{item.dataNascimentoApresentar}"
                                                        actionListener="#{LRAlunosAtestadoControle.prepareEditar}"
                                                        action="#{LRAlunosAtestadoControle.irParaTelaCliente}"
                                                        oncomplete="abrirPopup('clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);"/>
                                            </rich:column>
                                             <rich:column id="dataCadastro" sortBy="#{item.dataCadastro}">
                                                <f:facet name="header">
                                                    <h:outputText title="Data de Cadastro no aluno" value="Cadastro"></h:outputText>
                                                </f:facet>
                                                <a4j:commandLink
                                                        value="#{item.dataCadastroApresentar}"
                                                        actionListener="#{LRAlunosAtestadoControle.prepareEditar}"
                                                        action="#{LRAlunosAtestadoControle.irParaTelaCliente}"
                                                        oncomplete="abrirPopup('clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);"/>
                                            </rich:column>

                                            <rich:column id="telefone" sortBy="#{item.telefone}">
                                                <f:facet name="header">
                                                    <h:outputText value="Telefone"></h:outputText>
                                                </f:facet>
                                                <a4j:commandLink
                                                        value="#{item.telefone}"
                                                        actionListener="#{LRAlunosAtestadoControle.prepareEditar}"
                                                        action="#{LRAlunosAtestadoControle.irParaTelaCliente}"
                                                        oncomplete="abrirPopup('clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);"/>
                                            </rich:column>

                                            <rich:column id="email" sortBy="#{item.email}">
                                                <f:facet name="header">
                                                    <h:outputText value="E-mail"></h:outputText>
                                                </f:facet>
                                                <a4j:commandLink
                                                        value="#{item.email}"
                                                        actionListener="#{LRAlunosAtestadoControle.prepareEditar}"
                                                        action="#{LRAlunosAtestadoControle.irParaTelaCliente}"
                                                        oncomplete="abrirPopup('clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);"/>
                                            </rich:column>
                                            <rich:column id="lancamentoAtestado" sortBy="#{item.dataOperacao}">
                                                <f:facet name="header">
                                                    <h:outputText value="Lançamento Operação"></h:outputText>
                                                </f:facet>
                                                <a4j:commandLink
                                                        value="#{item.dataOperacaoApresentar}"
                                                        actionListener="#{LRAlunosAtestadoControle.prepareEditar}"
                                                        action="#{LRAlunosAtestadoControle.irParaTelaCliente}"
                                                        oncomplete="abrirPopup('clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);"/>
                                            </rich:column>

                                            <rich:column id="inicioAtestado" sortBy="#{item.dataAtestado}">
                                                <f:facet name="header">
                                                    <h:outputText value="Início Atestado"></h:outputText>
                                                </f:facet>
                                                <a4j:commandLink
                                                        value="#{item.dataAtestadoApresentar}"
                                                        actionListener="#{LRAlunosAtestadoControle.prepareEditar}"
                                                        action="#{LRAlunosAtestadoControle.irParaTelaCliente}"
                                                        oncomplete="abrirPopup('clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);"/>
                                            </rich:column>
                                            <rich:column id="fimAtestado" sortBy="#{item.dataFimOperacao}">
                                                <f:facet name="header">
                                                    <h:outputText value="Fim Atestado"></h:outputText>
                                                </f:facet>
                                                <a4j:commandLink
                                                        value="#{item.dataFimOperacaoApresentar}"
                                                        actionListener="#{LRAlunosAtestadoControle.prepareEditar}"
                                                        action="#{LRAlunosAtestadoControle.irParaTelaCliente}"
                                                        oncomplete="abrirPopup('clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);"/>
                                            </rich:column>
                                            <rich:column id="dataUltimoAcesso"
                                                         sortBy="#{item.dataUltimoAcesso}">
                                                <f:facet name="header">
                                                    <h:outputText value="Último Acesso"></h:outputText>
                                                </f:facet>
                                                <a4j:commandLink
                                                        value="#{item.dataUltimoAcessoApresentar}"
                                                        actionListener="#{LRAlunosAtestadoControle.prepareEditar}"
                                                        action="#{LRAlunosAtestadoControle.irParaTelaCliente}"
                                                        oncomplete="abrirPopup('clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);"/>
                                            </rich:column>
                                            <rich:column id="empresa"
                                                         sortBy="#{item.nomeEmpresa}">
                                                <f:facet name="header">
                                                    <h:outputText value="Empresa"></h:outputText>
                                                </f:facet>
                                                <a4j:commandLink
                                                        value="#{item.nomeEmpresa}"
                                                        actionListener="#{LRAlunosAtestadoControle.prepareEditar}"
                                                        action="#{LRAlunosAtestadoControle.irParaTelaCliente}"
                                                        oncomplete="abrirPopup('clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);"/>
                                            </rich:column>
                                        </rich:dataTable>

                                        <table align="right" border="0" cellspacing="0" cellpadding="0">
                                            <tr>
                                                <td align="center" valign="middle">
                                                    <h5>
                                                        <h:outputText
                                                                rendered="#{not empty LRAlunosAtestadoControle.resultado}"
                                                                value=" [Itens:#{LRAlunosAtestadoControle.totalItens}]"> </h:outputText>
                                                    </h5>
                                                </td>
                                            </tr>
                                        </table>
                                        <rich:datascroller for="resultados"
                                                           rendered="#{not empty LRAlunosAtestadoControle.resultado}"
                                                           id="scrollResultados"/>
                                    </h:panelGroup>
                                </h:panelGroup>

                            </h:panelGroup>

                        </h:panelGroup>
                        <jsp:include page="menuRelatorio.jsp" flush="true"/>
                    </h:panelGroup>
                </h:panelGroup>
            </h:panelGroup>

            <jsp:include page="include_rodape_flat.jsp" flush="true" />

        </h:panelGroup>
    </h:form>

    <%------------------------- MODAL 'IMPRIMIR' ----------------------------------------%>
    <rich:modalPanel id="relatorioImprimir" autosized="true" shadowOpacity="true" width="350">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Dados da impressão"/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:graphicImage value="/imagens/close.png" style="cursor:pointer" id="imprimir"/>
                <rich:componentControl for="relatorioImprimir" attachTo="imprimir" operation="hide" event="onclick"/>
            </h:panelGroup>
        </f:facet>
        <a4j:form>

            <h:outputText styleClass="text" style="font-weight: bold"
                          value="Por Favor, selecione as informações que deseja imprimir:"/><br/><br/>
            <h:selectBooleanCheckbox value="#{LRAlunosAtestadoControle.apresentarLinha1}"/>
            <h:outputText styleClass="text" value="Dados Cadastrais"/><br/>
            <h:selectBooleanCheckbox value="#{LRAlunosAtestadoControle.apresentarLinha3}"/>
            <h:outputText styleClass="text" value="Informações de Plano"/><br/><br/>
            <center>
                <a4j:commandButton id="imprimirPDF" ajaxSingle="false"
                                   action="#{LRAlunosAtestadoControle.imprimirListaRelatorio}"
                                   value="Imprimir"
                                   reRender="mensagem"
                                   image="../imagens/imprimirContrato.png"
                                   oncomplete="#{LRAlunosAtestadoControle.mensagemNotificar}#{LRAlunosAtestadoControle.msgAlert}"
                                   accesskey="2" styleClass="botoes"/>
            </center>
        </a4j:form>
    </rich:modalPanel>

    <%------------------------- MODAL 'CONSULTAR' ----------------------------------------%>
    <rich:modalPanel id="listasRelatorios" autosized="true" shadowOpacity="true">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Relátorio de Alunos com Atestado"/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:graphicImage value="/imagens/close.png" style="cursor:pointer" id="relatorios"/>
                <rich:componentControl for="listasRelatorios" attachTo="relatorios" operation="hide" event="onclick"/>
            </h:panelGroup>
        </f:facet>
        <jsp:include page="topoReduzido.jsp"/>
        <br/>
        <a4j:form>
            <h:panelGrid columns="2" columnClasses="colunaDireita, colunaEsquerda">
                <h:outputText styleClass="text" style="font-weight: bold"
                              rendered="#{not empty LRAlunosAtestadoControle.itemRelatorio.nome}"
                              value="Nome: "></h:outputText>
                <h:outputText styleClass="text" rendered="#{not empty LRAlunosAtestadoControle.itemRelatorio.nome}"
                              value="#{LRAlunosAtestadoControle.itemRelatorio.nome}"></h:outputText>

                <h:outputText styleClass="text" style="font-weight: bold"
                              rendered="#{not empty LRAlunosAtestadoControle.itemRelatorio.sexo}"
                              value="Sexo biológico: "></h:outputText>
                <h:outputText styleClass="text" rendered="#{not empty LRAlunosAtestadoControle.itemRelatorio.sexo}"
                              value="#{LRAlunosAtestadoControle.itemRelatorio.sexo}"></h:outputText>

                <h:outputText styleClass="text" style="font-weight: bold"
                              rendered="#{not empty LRAlunosAtestadoControle.itemRelatorio.dataMatriculaApresentar}"
                              value="Data Matrícula: "></h:outputText>
                <h:outputText styleClass="text"
                              rendered="#{not empty LRAlunosAtestadoControle.itemRelatorio.dataMatriculaApresentar}"
                              value="#{LRAlunosAtestadoControle.itemRelatorio.dataMatriculaApresentar}"></h:outputText>

                <h:outputText styleClass="text" style="font-weight: bold"
                              rendered="#{not empty LRAlunosAtestadoControle.itemRelatorio.dataInicioPlanoApresentar}"
                              value="Inicio Plano: "></h:outputText>
                <h:outputText styleClass="text"
                              rendered="#{not empty LRAlunosAtestadoControle.itemRelatorio.dataInicioPlanoApresentar}"
                              value="#{LRAlunosAtestadoControle.itemRelatorio.dataInicioPlanoApresentar}"></h:outputText>

                <h:outputText styleClass="text" style="font-weight: bold"
                              rendered="#{not empty LRAlunosAtestadoControle.itemRelatorio.dataVencimentoPlanoApresentar}"
                              value="Vencimento Plano: "></h:outputText>
                <h:outputText styleClass="text"
                              rendered="#{not empty LRAlunosAtestadoControle.itemRelatorio.dataVencimentoPlanoApresentar}"
                              value="#{LRAlunosAtestadoControle.itemRelatorio.dataVencimentoPlanoApresentar}"></h:outputText>

                <h:outputText styleClass="text" style="font-weight: bold"
                              rendered="#{not empty LRAlunosAtestadoControle.itemRelatorio.plano}"
                              value="Plano: "></h:outputText>
                <h:outputText styleClass="text" rendered="#{not empty LRAlunosAtestadoControle.itemRelatorio.plano}"
                              value="#{LRAlunosAtestadoControle.itemRelatorio.plano}"></h:outputText>

                <h:outputText styleClass="text" style="font-weight: bold;"
                              rendered="#{not empty LRAlunosAtestadoControle.itemRelatorio.consultores}"
                              value="Consultor: "/>
                <h:panelGroup>
                    <c:forEach items="${LRAlunosAtestadoControle.itemRelatorio.consultores}" var="consultor">
                        <span rendered="${not empty LRAlunosAtestadoControle.itemRelatorio.consultores}"
                              class="text">${consultor.pessoa.nome}</span><br/>
                    </c:forEach>
                </h:panelGroup>

                <h:outputText styleClass="text" style="font-weight: bold;"
                              rendered="#{not empty LRAlunosAtestadoControle.itemRelatorio.professores}"
                              value="Professor: "/>
                <h:panelGroup>
                    <c:forEach items="${LRAlunosAtestadoControle.itemRelatorio.professores}" var="professor">
                        <span rendered="${not empty LRAlunosAtestadoControle.itemRelatorio.professores}"
                              class="text">${professor.pessoa.nome}</span><br/>
                    </c:forEach>
                </h:panelGroup>
            </h:panelGrid>
        </a4j:form>
    </rich:modalPanel>
</f:view>


<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>

<h:panelGrid id="indRet" columns="1" rendered="#{LoginControle.permissaoAcessoMenuVO.indicadorRetencao}" width="100%" style="clear:both;" cellpadding="0" cellspacing="0">
    <table id="indRet1" width="100%" border="0" align="left" cellpadding="0" cellspacing="0" class="text" >
        <tr>
            <td width="100%">
                <table id="indRet3" width="95%" border="0" align="left" cellpadding="0" cellspacing="0" class="text" style="margin-right: 0px; margin-bottom: 0px;">
                    <tr>
                        <td><h:panelGrid id="panelIndicadorDeRetencao" width="100%" cellpadding="0" cellspacing="0" columns="3" columnClasses="alinhamentoSuperior,tituloboxcentro2, tituloboxcentro2" style="margin-right:10px;margin-bottom:10px;">
                                <table width="100%" height="420" border="0" align="left" cellpadding="0" cellspacing="0">
                                    <tr>
                                        <td width="19" height="50" align="left" valign="top"><img src="images/box_centro_top_left.gif" width="19" height="50" /></td>
                                        <td width="100%" align="left" valign="top"
                                            background="images/box_centro_top.gif"
                                            class="tituloboxcentro"
                                            style="padding: 11px 0 0 0;">
                                            Relacionamento de Fideliza��o

                                            <rich:calendar id="dataBasePanelIndicadorDeRetencao" disabled="false" 
                                                           value="#{AberturaMetaControle.aberturaMetaVO.dia}" 
                                                           showInput="false" direction="bottom-left" zindex="1000" 
                                                           showWeeksBar="false">
                                                <a4j:support event="onchanged" 
                                                             action="#{AberturaMetaControle.atualizarMetaDiaPorColaboradorRetencao}" 
                                                             ajaxSingle="true" reRender="panelGridIndicadorVendas, panelIndicadorDeRetencao" />
                                            </rich:calendar>

                                        </td>
                                        
                                        <td width="19" align="left" valign="top"><img src="images/box_centro_top_right.gif" width="19" height="50" /></td>
                                    </tr>
                                    <tr>
                                        <td align="left" valign="top" background="images/box_centro_left.gif"><img src="images/shim.gif" /></td>
                                        <td align="left" colspan="3" valign="top" bgcolor="#ffffff">
                                            <table width="100%" border="0" cellspacing="0" cellpadding="0" style="margin-bottom: 10px;">
                                                <tr>
                                                    <td><h:panelGrid id="panelGridIndicadorRetencao" columnClasses="colunaCentralizada" columns="1" style="position:relative; top:0px;" width="100%">
                                                            <h:panelGrid columnClasses="colunaCentralizada" columns="1" width="100%">
                                                                <h:panelGroup layout="block" id="panelDataSelecionadaRotatividade" style="text-align:right;">
                                                                    <h:outputText styleClass="textverysmall" value="Data base: " style="text-align:right;color:#0f4c6b;" />
                                                                    <h:outputText styleClass="textverysmall" value="#{AberturaMetaControle.aberturaMetaVO.dia}" style="text-align:right;color:#0f4c6b;">
                                                                        <f:convertDateTime type="date" dateStyle="short" locale="pt" timeZone="America/Sao_Paulo" pattern="dd/MM/yyyy" />
                                                                    </h:outputText>
                                                                </h:panelGroup>
                                                            </h:panelGrid>
                                                            <rich:dataTable id="itemsFecharMeta" width="100%" headerClass="consulta" 
                                                            				columnClasses="w5,w40,w10,w30,w10,w5" 
                                                            				styleClass="semBorda" style="border:none;" 
                                                            				rendered="#{AberturaMetaControle.aberturaMetaVO.existeMetaParaParticipante}" 
                                                            				value="#{AberturaMetaControle.aberturaMetaVO.fecharMetaVosRetencao}" var="fecharMeta">
                                                                <rich:column style="border:none;" rendered="#{fecharMeta.isIndicadorDeRetencao}">
                                                                    <h:graphicImage url="imagensCRM/pos_vendas.png" title="#{AberturaMetaControle.configuracaoSistemaCRMVO.baterMetaTodasAcoes  == true ? 'Realizar qualquer contato' : fecharMeta.fase.objetivo}" rendered="#{fecharMeta.apresentarImagemPosVenda}"/>
                                                                    <h:graphicImage url="imagensCRM/grupo_de_risco.png" title="#{AberturaMetaControle.configuracaoSistemaCRMVO.baterMetaTodasAcoes  == true ? 'Realizar qualquer contato' : fecharMeta.fase.objetivo}" rendered="#{fecharMeta.apresentarImagemRisco}" />
                                                                    <h:graphicImage url="imagensCRM/aniversariantes.png" title="#{AberturaMetaControle.configuracaoSistemaCRMVO.baterMetaTodasAcoes  == true ? 'Realizar qualquer contato' : fecharMeta.fase.objetivo}" rendered="#{fecharMeta.apresentarImagemAniversariantes}"/>
                                                                    <h:graphicImage url="imagensCRM/faltantes.png" title="#{AberturaMetaControle.configuracaoSistemaCRMVO.baterMetaTodasAcoes  == true ? 'Realizar qualquer contato' : fecharMeta.fase.objetivo}" rendered="#{fecharMeta.apresentarImagemFaltosos}"/>
                                                                    <h:graphicImage url="imagensCRM/vencidos.png" title="#{AberturaMetaControle.configuracaoSistemaCRMVO.baterMetaTodasAcoes  == true ? 'Realizar qualquer contato' : fecharMeta.fase.objetivo}" rendered="#{fecharMeta.apresentarImagemVencidos}"/>
                                                                </rich:column>
                                                                <rich:column  style="border:none;">
                                                                    <h:outputLink value="#{SuperControle.urlBaseConhecimento}fases-da-meta-diaria-do-crm/"
                                                                                  rendered="#{fecharMeta.apresentarImagemPosVenda}"
                                                                                  title="Clique e saiba mais:P�s Venda" target="_blank">
                                                                        <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                                                                    </h:outputLink>
                                                                    <a4j:commandLink id="linkPosVenda" style="font-weight: bold;"
                                                                                     styleClass="camposIndicadorVendasCRM"
                                                                                     rendered="#{fecharMeta.apresentarImagemPosVenda}"
                                                                                     value="#{fecharMeta.identificadorMeta_Apresentar}"
                                                                                     action="#{AberturaMetaControle.consultarFecharMetaDetalhadoRetencao}"
                                                                                     oncomplete="#{AberturaMetaControle.validarAbrirPopUpPosVenda}"/>

                                                                    <h:outputLink value="#{SuperControle.urlBaseConhecimento}fases-da-meta-diaria-do-crm/"
                                                                                  rendered="#{fecharMeta.apresentarImagemRisco}"
                                                                                  title="Clique e saiba mais: Grupo de Risco" target="_blank">
                                                                        <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                                                                    </h:outputLink>
                                                                    <a4j:commandLink id="linkGrupoDeRisco" style="font-weight: bold;" styleClass="camposIndicadorVendasCRM" title="Grupo de Risco"
                                                                                     rendered="#{fecharMeta.apresentarImagemRisco}"
                                                                                     value="#{fecharMeta.identificadorMeta_Apresentar}"
                                                                                     action="#{AberturaMetaControle.consultarFecharMetaDetalhadoRetencao}"
                                                                                     oncomplete="#{AberturaMetaControle.validarAbrirPopUpGrupoRisco}" />
                                                                    <h:outputLink value="#{SuperControle.urlBaseConhecimento}fases-da-meta-diaria-do-crm/"
                                                                                  rendered="#{fecharMeta.apresentarImagemAniversariantes}"
                                                                                  title="Clique e saiba mais: Aniversariantes" target="_blank">
                                                                        <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                                                                    </h:outputLink>
                                                                    <a4j:commandLink id="linkAniversariantes" style="font-weight: bold;" styleClass="camposIndicadorVendasCRM" title="Aniversariantes"
                                                                                     rendered="#{fecharMeta.apresentarImagemAniversariantes}"
                                                                                     value="#{fecharMeta.identificadorMeta_Apresentar}"
                                                                                     action="#{AberturaMetaControle.consultarFecharMetaDetalhadoRetencao}"
                                                                                     oncomplete="#{AberturaMetaControle.validarAbrirPopUpAniversariante}" />
                                                                    <h:outputLink value="#{SuperControle.urlBaseConhecimento}fases-da-meta-diaria-do-crm/"
                                                                                  rendered="#{fecharMeta.apresentarImagemFaltosos}"
                                                                                  title="Clique e saiba mais: Faltosos" target="_blank">
                                                                        <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                                                                    </h:outputLink>
                                                                    <a4j:commandLink id="linkFaltosos" style="font-weight: bold;" styleClass="camposIndicadorVendasCRM" title="Faltosos"
                                                                                     rendered="#{fecharMeta.apresentarImagemFaltosos}"
                                                                                     value="#{fecharMeta.identificadorMeta_Apresentar}"
                                                                                     action="#{AberturaMetaControle.consultarFecharMetaDetalhadoRetencao}"
                                                                                     oncomplete="#{AberturaMetaControle.validarAbrirPopUpFaltas}" />
                                                                                     
                                                                   <h:outputLink value="#{SuperControle.urlBaseConhecimento}fases-da-meta-diaria-do-crm/"
                                                                                  rendered="#{fecharMeta.apresentarImagemVencidos}"
                                                                                  title="Clique e saiba mais: Vencidos" target="_blank">
                                                                       <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                                                                    </h:outputLink>
                                                                    <a4j:commandLink id="linkVencidos" style="font-weight: bold;" styleClass="camposIndicadorVendasCRM"
                                                                                     title="Vencidos" rendered="#{fecharMeta.apresentarImagemVencidos}"
                                                                                     value="#{fecharMeta.identificadorMeta_Apresentar}"
                                                                                     action="#{AberturaMetaControle.consultarFecharMetaDetalhadoRetencao}"
                                                                                     oncomplete="#{AberturaMetaControle.validarAbrirPopUpVencidos}" />

                                                                </rich:column>
                                                                <rich:column style="border:none;" rendered="#{fecharMeta.isIndicadorDeRetencao}">
                                                                    <h:outputText id="metaTotal" styleClass="camposIndicadorVendasMetaCRM" value="#{fecharMeta.meta}">
                                                                        <f:convertNumber pattern="0" />
                                                                    </h:outputText>
                                                                </rich:column>
                                                                <rich:column style="border:none;" rendered="#{fecharMeta.isIndicadorDeRetencao}">
                                                                    <h:outputText styleClass="camposIndicadorVendasCRM" value="#{msg_aplic.prt_AberturaMeta_resultado}" />
                                                                    <rich:spacer width="5px" />
                                                                    <h:outputText id="metaAtingidaBaixa"  style="color: red;"  rendered="#{fecharMeta.resultadoPercentualMetaAtingido <=40}" title="Meta Atingida" styleClass="camposIndicadorVendasMetaAtingidaCRM" value="#{fecharMeta.metaAtingida}">
                                                                        <f:convertNumber pattern="0" />
                                                                    </h:outputText>
                                                                    <h:outputText id="metaAtingidaMedia" style="color: #0078D0;" rendered="#{fecharMeta.resultadoPercentualMetaAtingido >= 41 && fecharMeta.resultadoPercentualMetaAtingido <= 79 }" title="Meta Atingida" styleClass="camposIndicadorVendasMetaAtingidaCRM" value="#{fecharMeta.metaAtingida}">
                                                                        <f:convertNumber pattern="0" />
                                                                    </h:outputText>
                                                                    <h:outputText id="metaAtingidaAtingida" style="color:#006633;" rendered="#{fecharMeta.resultadoPercentualMetaAtingido >= 80}" title="Meta Atingida" styleClass="camposIndicadorVendasMetaAtingidaCRM" value="#{fecharMeta.metaAtingida}">
                                                                        <f:convertNumber pattern="0" />
                                                                    </h:outputText>
                                                                    <h:outputText rendered="#{fecharMeta.repescagem > 0}" styleClass="camposIndicadorVendasMetaAtingidaCRM" value=" - "/>
                                                                    <h:outputText title="Repescagem" rendered="#{fecharMeta.repescagem > 0}" id="metaRepescagem" styleClass="camposIndicadorVendasMetaAtingidaCRM" value="#{fecharMeta.repescagem}">
                                                                        <f:convertNumber pattern="0" />
                                                                    </h:outputText>
                                                                </rich:column>
                                                                <rich:column style="border:none;" rendered="#{fecharMeta.isIndicadorDeRetencao}">
                                                                    <h:outputText  title="Meta atingida + Repescagem"  style="color: red;font-weight: bold;"  rendered="#{fecharMeta.resultadoPercentualMetaAtingido <=40}"  styleClass="camposIndicadorVendasMetaPorcentagemCRM" value="#{fecharMeta.porcentagem}">
                                                                        <f:converter converterId="FormatadorNumerico" />
                                                                    </h:outputText>
                                                                    <h:outputText  title="Meta atingida + Repescagem" style="color: #0078D0;font-weight: bold;" rendered="#{fecharMeta.resultadoPercentualMetaAtingido >= 41 && fecharMeta.resultadoPercentualMetaAtingido <= 79 }"  styleClass="camposIndicadorVendasMetaPorcentagemCRM" value="#{fecharMeta.porcentagem}">
                                                                        <f:converter converterId="FormatadorNumerico" />
                                                                    </h:outputText>
                                                                    <h:outputText title="Meta atingida + Repescagem" style="color:#006633;font-weight: bold;" rendered="#{fecharMeta.resultadoPercentualMetaAtingido >= 80}"  styleClass="camposIndicadorVendasMetaPorcentagemCRM" value="#{fecharMeta.porcentagem}">
                                                                        <f:converter converterId="FormatadorNumerico" />
                                                                    </h:outputText>
                                                                </rich:column>
                                                                <rich:column  style="border:none;">
                                                                    <h:outputText styleClass="camposIndicadorVendasCRM" value="#{msg_aplic.prt_AberturaMeta_metaPorcentagem}" />
                                                                </rich:column>
                                                                <!-- --------------------------------------- DETALHAMENTOS ------------------------------------- -->
                                                                <!-- --------------------------------------- APRESENTAR MENSAGEM RETROATIVO ------------------------------------- -->
                                                                <rich:column style="border:none;" colspan="6" breakBefore="true" rendered="#{fecharMeta.apresentarMensagemRetroativo && fecharMeta.metaCalculada}">
                                                                    <rich:spacer width="35px;" />

                                                                    <h:outputText value="N�o calculada para abertura retroativa." styleClass="titulo5" />
                                                                    <div class="sep" style=""><img src="images/shim.gif"></div>
                                                                    </rich:column>

                                                                <rich:column rendered="#{!fecharMeta.apresentarMensagemRetroativo && fecharMeta.metaCalculada}" style="border:none;" colspan="6" breakBefore="true">
                                                                    <div class="sep" style=""><img src="images/shim.gif"></div>
                                                                </rich:column>
                                                                
                                                                <rich:column id="gridMetaNaoCalculada" style="border:none;" colspan="6" breakBefore="true" rendered="#{!fecharMeta.metaCalculada}">

                                                                <rich:spacer width="2px;" />
                                                                <h:outputText value="#{msg.msg_meta_nao_calculada}" styleClass="metaNaoCalculada" />
                                                                <div class="sep" style=""><img src="images/shim.gif"></div>
                                                                
                                                                <rich:toolTip for="gridMetaNaoCalculada" followMouse="true" direction="top-right" style="width:300px; height:90px; " showDelay="200">
                                        							<h:outputText styleClass="tituloCampos"
                                                      							  value="#{msg.msg_tip_meta_nao_calculada}" />
                                    							</rich:toolTip>	
                                                                </rich:column>
                                                                </rich:dataTable>

                                                            <h:panelGrid columnClasses="colunaEsquerda" columns="1" 
                                                            rendered="#{!empty AberturaMetaControle.aberturaMetaVO.grupoColaboradorListaRetencao && AberturaMetaControle.mostrarGruposColaboradoresRetencao}" width="100%">
                                                                <rich:dataTable id="items" width="100%" headerClass="consulta" 
                                                                columnClasses="colunaEsquerda" styleClass="semBorda" 
                                                                value="#{AberturaMetaControle.aberturaMetaVO.grupoColaboradorListaRetencao}"
                                                                var="grupoColaborador">
                                                                    <f:facet name="header">
                                                                        <h:panelGroup layout="block">
                                                                            <h:panelGroup layout="block" style="float: left">
                                                                                <h:panelGroup rendered="#{!AberturaMetaControle.mostrarGruposRetencao}" layout="block">
                                                                                    <i class="fa-icon-angle-right"></i>
                                                                                </h:panelGroup>
                                                                                <h:panelGroup rendered="#{AberturaMetaControle.mostrarGruposRetencao}" layout="block">
                                                                                    <i class="fa-icon-angle-down"></i>
                                                                                </h:panelGroup>
                                                                                <a4j:commandLink styleClass="titulo3"
                                                                                                 action="#{AberturaMetaControle.toggleMostrarGruposRetencao}"
                                                                                                 reRender="indRet">
                                                                                    <h:outputText styleClass="tituloCamposAberturaMeta"
                                                                                            value="#{msg_aplic.prt_AberturaMeta_descricaoGrupos}"/>
                                                                                    <h:outputText styleClass="tituloCamposAberturaMeta"
                                                                                            value="#{AberturaMetaControle.aberturaMetaVO.nomeGrupoColaboradorRetencao}"/>
                                                                                </a4j:commandLink>
                                                                            </h:panelGroup>
                                                                            <h:panelGroup style="float: right"
                                                                                          layout="block">
                                                                                <h:selectBooleanCheckbox
                                                                                        id="selectUsuarioLogadoRetencao"
                                                                                        rendered="#{AberturaMetaControle.mostrarCheckboxRetencao}"
                                                                                        value="#{AberturaMetaControle.marcarUsuarioRetencao}">
                                                                                    <a4j:support event="onclick"
                                                                                                 action="#{AberturaMetaControle.atualizarMetaDiaPorColaboradorRetencao}"
                                                                                                 reRender="indRet"/>
                                                                                </h:selectBooleanCheckbox>
                                                                                <h:outputText styleClass="titulo3"
                                                                                              value="#{AberturaMetaControle.usuarioLogado.nomeAbreviado}"/>
                                                                            </h:panelGroup>
                                                                        </h:panelGroup>
                                                                    </f:facet>
                                                                    <rich:column rendered="#{AberturaMetaControle.mostrarGruposRetencao}">
                                                                        <a4j:commandButton styleClass="botoes" action="#{AberturaMetaControle.selecionarGrupoColaboradorParticipanteRetencao}" image="./imagensCRM/botaoAdicionarGrupos.png" 
                                                                        reRender="panelIndicadorDeRetencao" />
                                                                        <rich:spacer width="5px;" />
                                                                        <a4j:commandLink styleClass="botoes" title="Visualizar Participantes do Grupo" reRender="panelGridIndicadorVendas, panelIndicadorDeRetencao" action="#{AberturaMetaControle.selecionarGrupoColaboradorParticipanteRetencao}">
                                                                            <h:outputText styleClass="tituloCamposAberturaMeta" value="#{grupoColaborador.descricao}" />
                                                                        </a4j:commandLink>
                                                                        <rich:dataGrid value="#{grupoColaborador.grupoColaboradorParticipanteVOs}" 
                                                                        var="grupoColaboradorParticipante" width="100%" 
                                                                        columns="3" columnClasses="semBorda" styleClass="semBorda" rendered="#{!empty grupoColaborador.grupoColaboradorParticipanteVOs}" cellpadding="0" cellspacing="0">
                                                                            <h:panelGrid columns="2" width="100%" cellpadding="0" cellspacing="0">
                                                                                <h:selectBooleanCheckbox value="#{grupoColaboradorParticipante.grupoColaboradorParticipanteEscolhido}" disabled="#{grupoColaboradorParticipante.colaboradorIndisponivel}" title="#{grupoColaboradorParticipante.colaboradorIndisponivel  == true ? 'Colaborador desabilitado por estar indispon�vel!' : ''}">
                                                                                    <a4j:support event="onclick" action="#{AberturaMetaControle.selecionarMetaColaboradorRetencao}" reRender="panelIndicadorDeRetencao" />
                                                                                </h:selectBooleanCheckbox>
                                                                                <h:outputText styleClass="titulo3" value="#{grupoColaboradorParticipante.usuarioParticipante.primeiroNomeConcatenado}" title="#{grupoColaboradorParticipante.colaboradorIndisponivel  == true ? 'Colaborador desabilitado por estar indispon�vel!' : ''}" />
                                                                            </h:panelGrid>
                                                                        </rich:dataGrid>
                                                                    </rich:column>
                                                                </rich:dataTable>
                                                            </h:panelGrid>
                                                            <h:panelGrid id="panelAtualizarListaRetencao" columnClasses="colunaCentralizada" columns="1" width="100%" rendered="#{!empty AberturaMetaControle.aberturaMetaVO.fecharMetaVosRetencao}">
                                                                <a4j:commandButton styleClass="botoes" action="#{AberturaMetaControle.atualizarMetaDiaPorColaboradorRetencao}" image="./imagensCRM/atualizar.png"
                                                                					id="atualizarBIRetencao" reRender="panelGridIndicadorVendas, panelIndicadorDeRetencao">
                                                                    <rich:toolTip followMouse="true" direction="top-right" style="width:350px; height:80px; " showDelay="500">
                                                                        <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_AberturaMeta_descricaoBotaoAtualizar}" />
                                                                    </rich:toolTip>
                                                                </a4j:commandButton>
                                                            </h:panelGrid>
                                                            <h:panelGrid columns="1" width="100%" rendered="#{!empty AberturaMetaControle.aberturaMetaVO.fecharMetaVosRetencao}">
                                                                <h:panelGroup>
                                                                    <h:outputText styleClass="tituloCampos" value="Colaborador Respons�vel: " />
                                                                    <rich:spacer width="10px" />
                                                                    <h:outputText styleClass="tituloCampos" value="#{AberturaMetaControle.aberturaMetaVO.colaboradorResponsavel.nome}" />
                                                                </h:panelGroup>
                                                                <h:panelGroup>
                                                                    <h:outputText styleClass="tituloCampos" value="Respons�vel Cadastro:" />
                                                                    <rich:spacer width="10px" />
                                                                    <h:outputText styleClass="tituloCampos" value="#{AberturaMetaControle.aberturaMetaVO.responsavelCadastro.nome}" />
                                                                </h:panelGroup>
                                                            </h:panelGrid>
                                                            <h:panelGrid id="panelMensagemVendaRetencao" columns="1" width="100%">
                                                                <h:outputText styleClass="mensagem" value="#{AberturaMetaControle.mensagem}" />
                                                                <h:outputText styleClass="mensagemDetalhada" value="#{AberturaMetaControle.mensagemDetalhada}" />
                                                            </h:panelGrid>


                                                        </h:panelGrid></td>
                                                </tr>
                                            </table>

                                        </td>

                                        <td align="left" valign="top" background="images/box_centro_right.gif"><img src="images/shim.gif" /></td>
                                    </tr>
                                    <tr>
                                        <td height="20" align="left" valign="top"><img src="images/box_centro_bottom_left.gif" width="19" height="20"></td>
                                        <td align="left" colspan="3" valign="top" background="images/box_centro_bottom.gif"><img src="images/shim.gif" /></td>
                                        <td align="left" valign="top"><img src="images/box_centro_bottom_right.gif" width="19" height="20" /></td>
                                    </tr>
                                </table>
                            </h:panelGrid></td>
                    </tr>
                </table>
            </td>
        </tr>
    </table>
</h:panelGrid>

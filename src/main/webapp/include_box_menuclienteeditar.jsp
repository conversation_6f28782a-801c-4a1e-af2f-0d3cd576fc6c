<%@page contentType="text/html"%>
<%@page pageEncoding="UTF-8"%>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>

<!-- inicio box -->
<div class="box">
    <div class="boxtop"><img src="./images/box_top.png"></div>
    <div class="boxmiddle">
        <table width="146" border="0" cellspacing="0" cellpadding="0" style="padding-bottom:6px;">
            <tr>
                <td colspan="2" align="left" valign="top"><a class="titulo2" style="font-size:14px"href="javascript:;">Opções</a></td>
            </tr>
        </table>
        <table width="50" align="left" border="0" cellspacing="0" cellpadding="0" >
            <rich:dropDownMenu styleClass="titulo6" value="Cadastro"
                               submitMode="none" direction="bottom-right" jointPoint="tr" >
                <rich:menuItem >
                    <a4j:commandLink actionListener="#{ClienteControle.abrirAba}" ajaxSingle="true" oncomplete="abrirPopup('clienteForm.jsp', 'ClienteCadastro', 880, 622);"  >
                        <h:outputText value="Vincular a uma Carteira"/>
                        <f:attribute name="aba" value="abaVinculo"/>
                    </a4j:commandLink>
                </rich:menuItem>
                <rich:menuItem >
                    <a4j:commandLink actionListener="#{ClienteControle.abrirAba}" ajaxSingle="true" oncomplete="abrirPopup('clienteForm.jsp', 'ClienteCadastro', 880, 622);" >
                        <h:outputText value="Adicionar classificação"/>
                        <f:attribute name="aba" value="abaGrupo"/>
                    </a4j:commandLink>
                </rich:menuItem>
                <rich:menuItem >
                    <a4j:commandLink actionListener="#{ClienteControle.abrirAba}" ajaxSingle="true" oncomplete="abrirPopup('clienteForm.jsp', 'ClienteCadastro', 880, 622);"  >
                        <h:outputText value="Associar a Grupos"/>
                        <f:attribute name="aba" value="abaGrupo"/>
                    </a4j:commandLink>
                </rich:menuItem>
            </rich:dropDownMenu>

            <!-- fim item-->
            <%-- <!-- inicio item-->
             <h:panelGrid rendered="#{!ClienteControle.menuContrato}">
                 <div><h:commandLink   styleClass="titulo3"action="#{ClienteControle.renovarContrato}"> Renovar Contrato</h:commandLink></div>
                 <div class="sepmenu"><img src="./images/shim.gif"></div>
             </h:panelGrid>
     <!-- fim item-->--%>
        </table>
        <!-- fim item-->
        <div class="sepmenu"><img src="./images/shim.gif"></div>
        <!-- inicio item-->
        <table width="50" border="0" cellspacing="0" cellpadding="0" >
            <rich:dropDownMenu styleClass="titulo6" value="CRM"
                               direction="bottom-right" jointPoint="tr" >
                <rich:menuItem value="Ver o ultimo boletim de visita" />
                <rich:menuItem value="Ver Histórico de Boletins" />
                <rich:menuItem value="Lançar mensagem para catraca" />
                <rich:menuItem value="Lançar aviso ao atendente" />
                <rich:menuItem value="Lançar aviso médico" />
                <rich:menuItem value="Lançar objetivo do aluno academia" />
                <rich:menuItem value="Lança Observação" />
            </rich:dropDownMenu>
        </table>
        <div class="sepmenu"><img src="./images/shim.gif"></div>
        <!-- inicio item-->
        <table width="50" border="0" cellspacing="0" cellpadding="0" >
            <rich:dropDownMenu styleClass="titulo6" value="Venda Avulsa"
                               submitMode="none" direction="bottom-right" jointPoint="tr" >
                <rich:menuItem>
                    <h:commandLink actionListener="#{VendaAvulsaControle.prepare}" action="#{VendaAvulsaControle.novo}"  styleClass="titulo2" >Produto ou Serviço</h:commandLink>
                </rich:menuItem>
                <rich:menuItem>
                    <h:commandLink action="#{AulaAvulsaDiariaControle.novoDiariaTela}"  styleClass="titulo2" >Diária</h:commandLink>
                </rich:menuItem>
                <rich:menuItem>
                    <h:commandLink action="#{AulaAvulsaDiariaControle.novoAulaAvulsa}"  styleClass="titulo2" >Aula</h:commandLink>
                </rich:menuItem>
            </rich:dropDownMenu>
        </table>



    </div>
    <div class="boxbottom"><img src="images/box_bottom.png"></div>
</div>
<!-- fim box -->
<div class="box">
    <div class="boxtop"><img src="./images/box_top.png"></div>
    <div class="boxmiddle">
        <table width="146" border="0" cellspacing="0" cellpadding="0" style="padding-bottom:10px;">

            <tr>
                <td width="2" align="left" valign="top"><img src="./images/shim.gif"></td>
                <!-- inicio item-->
                <td align="left" valign="top">
                    <!-- inicio item-->
                    <h:panelGrid columns="2">
                        <h:column>
<%--<!--                            <h:commandLink styleClass="titulo2" id="A" action="#{ClienteControle.acao}" actionListener="#{ClienteControle.consultarPorLetraListener}">-->--%>
<%--<!--                                <h:outputText value="   "/>-->--%>
<%--<!--                                <h:outputText value="Cliente"/>-->--%>
<%--<!--                            </h:commandLink>-->--%>
							<a4j:commandLink styleClass="titulo2" action="#{ClienteControle.acao}" actionListener="#{ClienteControle.consultarPaginadoListener}">
	                        	Clientes
	                        	<f:attribute name="paginaInicial" value="paginaInicial" />
	                        	<f:attribute name="letraConsultPag" value="A"/>
	                        	<f:attribute name="tipoConsulta" value="letra"/>
	                        </a4j:commandLink>
                        </h:column>
                    </h:panelGrid>
                    <div class="sepmenu"><img src="./images/shim.gif"></div>

                    <!-- fim item-->

                    <%--      <!-- inicio item-->
                 <div><h:commandLink styleClass="titulo3" action="#{ClienteControle.novo}">Cadastro de pessoa simplificado</h:commandLink>
                 <div class="sepmenu"><img src="./images/shim.gif"></div>
                 <!-- fim item-->

                    <!-- inicio item-->
                    <h:panelGroup rendered="#{ClienteControle.menuContrato}">
                        <h:commandLink styleClass="titulo3" action="#{ClienteControle.validarQuestionario}">Novo Contrato</h:commandLink>
                        <div class="sepmenu"><img src="./images/shim.gif"></div>

                    </h:panelGroup>--%>

                    <!-- inicio item-->
                    <h:panelGrid columns="2">
                        <a4j:commandLink styleClass="titulo2" action="#{ClienteControle.realizarConsultaLogObjetoSelecionado}" oncomplete="abrirPopup('visualizadorLogForm.jsp', 'VisualizadorLog', 785, 595);">
                            <h:outputText value="   "/>
                            <h:outputText value="Visualizar LOG"/>
                        </a4j:commandLink>						
                    </h:panelGrid>
                    <!-- fim item-->
                    <div class="sepmenu"><img src="./images/shim.gif"></div>
                    <!-- inicio item-->
                    <table width="146" border="0" cellspacing="0" cellpadding="0" style="padding-bottom:6px;">
                        <tr>

                            <td colspan="2" align="left" valign="top">
                                <h:panelGrid columns="2">
                                    <h:commandLink styleClass="titulo2" action="#{MovParcelaControle.novo}">
                                        <h:outputText value="   "/>
                                        <h:outputText value="Caixa"/>
                                    </h:commandLink>
                                </h:panelGrid>

                        </tr>
                    </table>
                    <!-- fim item-->

                    <!-- fim item-->
                    <div class="sepmenu"><img src="./images/shim.gif"></div>
                    <!-- inicio item-->
                    <h:panelGrid columns="2">
                        <h:commandLink styleClass="titulo2">
                            <h:outputText value="   "/>
                            <h:outputText value="Novo Contrato"/>
                        </h:commandLink>
                    </h:panelGrid>
                    <!-- fim item-->
                </td>
            </tr>
        </table><%--
        <table width="146" border="0" cellspacing="0" cellpadding="0" style="padding-bottom:6px;">
            <tr>
                <td colspan="2" align="left" valign="top"><img style="vertical-align:middle;margin-right:4px;" src="./images/icon_boletim.gif" width="16" height="16"><a class="titulo2" href="javascript:;">Boletim de Visita</a></td>
            </tr>
        </table>

            <table width="146" border="0" cellspacing="0" cellpadding="0" style="padding-bottom:10px;">

                <tr>
                    <td width="20" align="left" valign="top"><img src="./images/shim.gif"></td>
                    <td align="left" valign="top">

                        <!-- inicio item-->
                        <div><a class="titulo3" href="tela4.jsp">Question&aacute;rio</a></div>
                        <div class="sepmenu"><img src="./images/shim.gif"></div>
                        <!-- fim item-->

                 <!-- inicio item-->
                        <div><a class="titulo3" href="javascript:;">Boletim de Visita N&ordm; 02</a></div>
                        <div class="sepmenu"><img src="images/shim.gif"></div>
                        <!-- fim item-->
                    </td>
                </tr>
        </table>--%>


        <table width="146" border="0" cellspacing="0" cellpadding="0" style="padding-bottom:6px;">
            <tr>
                <td colspan="2" align="left" valign="top"><img style="vertical-align:middle;margin-right:7px;" src="images/icon_sair.gif" width="14" height="15"><a class="titulo2" href="login.jsp">Sair</a></td>
            </tr>
        </table>
    </div>
    <div class="boxbottom"><img src="images/box_bottom.png"></div>
</div>


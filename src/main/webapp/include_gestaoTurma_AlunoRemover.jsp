<%@include file="/includes/imports.jsp" %>
<%--
  Created by IntelliJ IDEA.
  User: <PERSON><PERSON>
  Date: 10/04/2019
  Time: 13:27
  To change this template use File | Settings | File Templates.
--%>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<h:panelGroup layout="block" styleClass="margin-box"
              rendered="#{GestaoTurmaControle.manutencaoAlunos}">

    <h:panelGrid columns="1" id="panelGeralAluno" style="width: 100%">

        <%--        <h:outputText styleClass="texto-size-16 texto-cor-cinza texto-font texto-bold"--%>
        <%--                      value="TURMA ORIGEM"/>--%>

        <h:panelGroup layout="block" id="panelFiltrosAlunos"
                      style="display: inline-flex; padding-top: 10px; width: 100%">

            <h:panelGroup layout="block">
                <%--                <h:outputText styleClass="texto-size-14 texto-cor-cinza texto-font texto-bold"--%>
                <%--                              value="MODALIDADE"/>--%>
                <h:selectOneMenu id="modalidadeTransferencia" onblur="blurinput(this);"
                                 styleClass="inputTextClean"
                                 onfocus="focusinput(this);"
                                 value="#{GestaoTurmaControle.modalidadeTransferencia}">
                    <f:selectItems value="#{GestaoTurmaControle.selectItensModalidade}"/>
                    <a4j:support event="onchange"
                                 action="#{GestaoTurmaControle.montarTurmas}"
                                 reRender="panelGeralAluno"/>
                </h:selectOneMenu>
            </h:panelGroup>

            <h:panelGroup layout="block" style="padding-left: 15px">
                <h:selectOneMenu id="turmaOrigem" onblur="blurinput(this);"
                                 styleClass="inputTextClean"
                                 onfocus="focusinput(this);"
                                 value="#{GestaoTurmaControle.turmaOrigem}">
                    <f:selectItems value="#{GestaoTurmaControle.listaTurmaOrigem}"/>
                    <a4j:support event="onchange"
                                 action="#{GestaoTurmaControle.montarHorarioTurmaOrigem}"
                                 reRender="panelGeralAluno"/>
                </h:selectOneMenu>
            </h:panelGroup>

            <h:panelGroup layout="block" style="padding-left: 15px">
                <h:selectOneMenu id="horarioOrigem" onblur="blurinput(this);"
                                 styleClass="inputTextClean"
                                 onfocus="focusinput(this);"
                                 value="#{GestaoTurmaControle.horarioOrigem}">
                    <f:selectItems value="#{GestaoTurmaControle.listaHorarioTurmaOrigem}"/>
                    <a4j:support event="onchange"
                                 oncomplete="#{GestaoTurmaControle.mensagemNotificar}"
                                 action="#{GestaoTurmaControle.consultarAlunos}"
                                 reRender="panelGeralAluno"/>
                </h:selectOneMenu>
            </h:panelGroup>
        </h:panelGroup>

        <h:panelGroup layout="block" id="panelBtnAcoes" style="padding-top: 30px">
            <a4j:commandLink styleClass="botaoPrimario texto-size-12"
                             value="Consultar"
                             action="#{GestaoTurmaControle.consultarAlunos}"
                             oncomplete="#{GestaoTurmaControle.mensagemNotificar}"
                             reRender="panelGeralAluno">
            </a4j:commandLink>

            <a4j:commandLink styleClass="botaoPrimario texto-size-12"
                             value="Remover"
                             style="float: right; background-color: darkred !important;"
                             rendered="#{not empty GestaoTurmaControle.alunosTurma}"
                             action="#{GestaoTurmaControle.removerAlunos}"
                             oncomplete="#{GestaoTurmaControle.mensagemNotificar}"
                             reRender="panelGeralAluno">
            </a4j:commandLink>
        </h:panelGroup>


        <h:panelGroup layout="block" id="panelListaAlunos" style="padding-top: 20px">

            <rich:dataTable width="100%" headerClass="consulta" rowClasses="linhaImpar, linhaPar"
                            columnClasses="colunaAlinhamento" id="tabela"
                            rendered="#{not empty GestaoTurmaControle.alunosTurma}"
                            value="#{GestaoTurmaControle.alunosTurma}" rows="50" var="aluno" rowKeyVar="status">
                <%@include file="pages/ce/includes/include_contador_richtable.jsp" %>

                <rich:column sortBy="#{aluno.selecionado}" filterEvent="onkeyup">
                    <f:facet name="header">
                        <h:selectBooleanCheckbox value="#{GestaoTurmaControle.selecionarTodos}">
                            <a4j:support event="onchange"
                                         action="#{GestaoTurmaControle.acaoSelecionarTodos}"
                                         reRender="panelListaAlunos"/>
                        </h:selectBooleanCheckbox>
                    </f:facet>
                    <h:selectBooleanCheckbox value="#{aluno.selecionado}"/>
                </rich:column>
                <rich:column>
                    <f:facet name="header">
                        <h:outputText value="Foto"/>
                    </f:facet>
                    <h:graphicImage style="width:30px;height:30px; border-radius: 50%;"
                                    styleClass="tooltipsterright"
                                    url="#{aluno.clienteVO.pessoa.urlFoto}">
                    </h:graphicImage>
                </rich:column>
                <rich:column sortBy="#{aluno.clienteVO.matricula}" filterEvent="onkeyup">
                    <f:facet name="header">
                        <h:outputText value="Matrícula"/>
                    </f:facet>
                    <h:outputText value="#{aluno.clienteVO.matricula}"/>
                </rich:column>
                <rich:column sortBy="#{aluno.clienteVO.pessoa.nome}" filterEvent="onkeyup">
                    <f:facet name="header">
                        <h:outputText value="Aluno"/>
                    </f:facet>
                    <h:outputText value="#{aluno.clienteVO.pessoa.nome}"/>
                </rich:column>
                <rich:column sortBy="#{aluno.matriculaAlunoHorarioTurmaVO.codigo == 0 ? 'Sim' : 'Não'}"
                             filterEvent="onkeyup">
                    <f:facet name="header">
                        <h:outputText value="Reposição"/>
                    </f:facet>
                    <h:outputText value="#{aluno.matriculaAlunoHorarioTurmaVO.codigo == 0 ? 'Sim' : 'Não'}"/>
                </rich:column>
                <rich:column sortBy="#{aluno.matriculaAlunoHorarioTurmaVO.dataInicio}" filterEvent="onkeyup">
                    <f:facet name="header">
                        <h:outputText value="Data Início Matrícula"/>
                    </f:facet>
                    <h:outputText value="#{aluno.matriculaAlunoHorarioTurmaVO.dataInicio}">
                        <f:convertDateTime pattern="dd/MM/yyyy"/>
                    </h:outputText>
                </rich:column>
                <rich:column sortBy="#{aluno.matriculaAlunoHorarioTurmaVO.dataFim}" filterEvent="onkeyup">
                    <f:facet name="header">
                        <h:outputText value="Data Fim Matrícula"/>
                    </f:facet>
                    <h:outputText value="#{aluno.matriculaAlunoHorarioTurmaVO.dataFim}">
                        <f:convertDateTime pattern="dd/MM/yyyy"/>
                    </h:outputText>
                </rich:column>
                <rich:column sortBy="#{aluno.matriculaAlunoHorarioTurmaVO.horarioTurma.professor.pessoa_Apresentar}"
                             filterEvent="onkeyup">
                    <f:facet name="header">
                        <h:outputText value="Professor"/>
                    </f:facet>
                    <h:outputText
                            value="#{aluno.matriculaAlunoHorarioTurmaVO.horarioTurma.professor.pessoa_Apresentar}"/>
                </rich:column>
                <rich:column sortBy="#{aluno.matriculaAlunoHorarioTurmaVO.horarioTurma.ambiente.descricao}"
                             filterEvent="onkeyup">
                    <f:facet name="header">
                        <h:outputText value="Ambiente"/>
                    </f:facet>
                    <h:outputText value="#{aluno.matriculaAlunoHorarioTurmaVO.horarioTurma.ambiente.descricao}"/>
                </rich:column>
                <rich:column sortBy="#{aluno.matriculaAlunoHorarioTurmaVO.horarioTurma.diaSemana_Apresentar}"
                             filterEvent="onkeyup">
                    <f:facet name="header">
                        <h:outputText value="Dia da Semana"/>
                    </f:facet>
                    <h:outputText value="#{aluno.matriculaAlunoHorarioTurmaVO.horarioTurma.diaSemana_Apresentar}"/>
                </rich:column>
                <rich:column sortBy="#{aluno.matriculaAlunoHorarioTurmaVO.horarioTurma.horaInicial}"
                             filterEvent="onkeyup">
                    <f:facet name="header">
                        <h:outputText value="Hora Início"/>
                    </f:facet>
                    <h:outputText value="#{aluno.matriculaAlunoHorarioTurmaVO.horarioTurma.horaInicial}"/>
                </rich:column>
                <rich:column sortBy="#{aluno.matriculaAlunoHorarioTurmaVO.horarioTurma.horaFinal}"
                             filterEvent="onkeyup">
                    <f:facet name="header">
                        <h:outputText value="Hora Fim"/>
                    </f:facet>
                    <h:outputText value="#{aluno.matriculaAlunoHorarioTurmaVO.horarioTurma.horaFinal}"/>
                </rich:column>
            </rich:dataTable>
            <rich:datascroller rendered="#{not empty GestaoTurmaControle.alunosTurma}"
                               align="center" for="form:tabela" maxPages="10" id="sctabela"/>

        </h:panelGroup>


    </h:panelGrid>
</h:panelGroup>

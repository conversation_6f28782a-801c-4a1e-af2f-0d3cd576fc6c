<%-- 
    Document   : clientesAcessoRelBI
    Created on : 23/02/2018, 11:22:08
    Author     : arthur
--%>

<%@page contentType="text/html"%>
<%@page pageEncoding="ISO-8859-1"%>
<head>
    <%@include file="includes/include_import_minifiles.jsp"%>
</head>
<script type="text/javascript" language="javascript" src="../hoverform.js"></script>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<link href="./beta/css/font-awesome_2.0.min.css" rel="stylesheet" type="text/css">

<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText value="Cliente(s) na Academia"/>
    </title>
    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <html>
            <body onload="fireElement('form:botaoAtualizarPagina')"/>
            <h:form id="form" >
                <a4j:commandButton id="botaoAtualizarPagina" reRender="form" style="display:none"/>
                <c:set var="titulo" scope="session" value="Acesso(s) Liberados Total:${fn:length(GestaoAcessoRelControle.listaAcessoLiberado)} "/>
                <c:set var="urlWiki" scope="session" value="${SuperControle.urlBaseConhecimento}bi-gestao-de-acessos-adm/"/>
                <h:panelGroup layout="block" styleClass="pure-g-r">
                    <f:facet name="header">
                        <jsp:include page="topo_reduzido_popUp.jsp"/>
                    </f:facet>
                </h:panelGroup>
                <h:panelGrid columns="1" width="100%" >
                    <h:panelGrid width="100%" style="text-align: right">
                        <h:panelGroup layout="block">
                            <a4j:commandLink id="exportarExcel"
                                               style="margin-left: 8px;"
                                               actionListener="#{ExportadorListaControle.exportar}"
                                               rendered="#{not empty GestaoAcessoRelControle.listaAcessoLiberado}"
                                             oncomplete="#{ExportadorListaControle.mensagemNotificar}#{ExportadorListaControle.operacaoOnComplete}#{ExportadorListaControle.msgAlert}"
                                               accesskey="2" styleClass="linkPadrao">
                                <f:attribute name="lista" value="#{GestaoAcessoRelControle.listaAcessoLiberado}"/>
                                <f:attribute name="tipo" value="xls"/>
                                <f:attribute name="itemExportacao" value="biAcessoLiberados"/>
                                <f:attribute name="atributos" value="usuarioLiberouApresentar=Usuario,pessoaApresentar=Cliente,tipoLiberacaoApresentar=Tipo Liberação,dataHoraApresentar=Data Liberação,justificativaApresentar=Justificativa"/>
                                <f:attribute name="prefixo" value="AcessosLiberados"/>
                                <h:outputText title="Exportar para o formato Excel" styleClass="btn-print-2 excel"/>
                            </a4j:commandLink>
                            <%--BOTÃO PDF--%>
                            <a4j:commandLink id="exportarPdf"
                                               style="margin-left: 8px;"
                                               actionListener="#{ExportadorListaControle.exportar}"
                                               rendered="#{not empty GestaoAcessoRelControle.listaAcessoLiberado}"
                                             oncomplete="#{ExportadorListaControle.mensagemNotificar}#{ExportadorListaControle.operacaoOnComplete}#{ExportadorListaControle.msgAlert}"
                                               accesskey="2" styleClass="linkPadrao">
                                <f:attribute name="lista" value="#{GestaoAcessoRelControle.listaAcessoLiberado}"/>
                                <f:attribute name="tipo" value="pdf"/>
                                <f:attribute name="itemExportacao" value="biAcessoLiberados"/>
                                <f:attribute name="atributos" value="usuarioLiberouApresentar=Usuario,pessoaApresentar=Cliente,tipoLiberacaoApresentar=Tipo Liberação,dataHoraApresentar=Data Liberação,justificativaApresentar=Justificativa"/>
                                <f:attribute name="prefixo" value="AcessosLiberados"/>
                                <h:outputText title="Exportar para o formato PDF" styleClass="btn-print-2 pdf"/>
                            </a4j:commandLink>
                        </h:panelGroup>
                    </h:panelGrid>
                    <rich:dataTable width="100%" styleClass="tabelaSimplesCustom" id="tabelaRes"
                                    value="#{GestaoAcessoRelControle.listaAcessoLiberado}" rows="50" var="liberacao" rowKeyVar="status">
                        <%@include file="/pages/ce/includes/include_contador_richtable.jsp" %>
                        <rich:column sortBy="#{liberacao.usuario.nome}" styleClass="col-text-align-left" headerClass="col-text-align-left">
                            <f:facet name="header">
                                <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font"  value="Usuario" />
                            </f:facet>
                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font"  value="#{liberacao.usuario.nome}" />
                        </rich:column>
                        
                        <rich:column sortBy="#{liberacao.pessoaApresentar}" styleClass="col-text-align-left" headerClass="col-text-align-left">
                            <f:facet name="header">
                                <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font"  value="Cliente" />
                            </f:facet>
                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font"  value="#{liberacao.pessoaApresentar}" />
                        </rich:column>
                        
                        <rich:column sortBy="#{liberacao.tipoLiberacaoApresentar}" styleClass="col-text-align-left" headerClass="col-text-align-left">
                            <f:facet name="header">
                                <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font"  value="Tipo Liberação" />
                            </f:facet>
                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font"  value="#{liberacao.tipoLiberacaoApresentar}" />
                        </rich:column>
                        
                        <rich:column sortBy="#{liberacao.dataHora}" styleClass="col-text-align-left" headerClass="col-text-align-left">
                            <f:facet name="header">
                                <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font"  value="Data Liberação" />
                            </f:facet>
                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font"  value="#{liberacao.dataHoraApresentar}" />
                        </rich:column>
                        
                        <rich:column sortBy="#{liberacao.justificativaApresentar}" styleClass="col-text-align-left" headerClass="col-text-align-left">
                            <f:facet name="header">
                                <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font"  value="Justificativa" />
                            </f:facet>
                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font"  value="#{liberacao.justificativaApresentar}" />
                        </rich:column>
                        
                    </rich:dataTable>
                    <rich:datascroller styleClass="scrollPureCustom" renderIfSinglePage="false" align="center" for="form:tabelaRes" maxPages="10" id="sctabelaRes" />
                </h:panelGrid>
            </h:form>
        </body>
    </html>
</h:panelGrid>
</f:view>

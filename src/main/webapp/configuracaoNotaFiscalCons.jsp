<%@page contentType="text/html;charset=UTF-8" %>
<head>
    <%@include file="/includes/include_import_minifiles.jsp" %>
</head>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<script type="text/javascript" language="javascript" src="hoverform.js"></script>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax" %>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich" %>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core" %>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<meta name="viewport" content="width=device-width, initial-scale=1.0">
<link href="beta/css/pure-min.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/bootstrap-tabs.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/pure-ext.css" type="text/css" rel="stylesheet"/>
<script src="beta/js/jquery.js" type="text/javascript"></script>
<script src="beta/js/jquery.dataTables.min.js" type="text/javascript"></script>
<script src="beta/js/DT_bootstrap.js" type="text/javascript"></script>
<script src="beta/js/bootstrap-tab.js" type="text/javascript"></script>
<script>
    jQuery.noConflict();
</script>

<c:set var="contexto" value="${pageContext.request.contextPath}" scope="request"/>

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title>${msg_aplic.prt_ConfigNotaFiscal_tituloForm}</title>
    <c:set var="titulo" scope="session" value="${msg_aplic.prt_ConfigNotaFiscal_tituloForm}"/>
    <c:set var="urlWiki" scope="session"
           value="${SuperControle.urlBaseConhecimento}/config-nota-fiscal-explicacao-dos-itens/"/>
    <f:facet name="header">
        <jsp:include page="topoReduzido_material.jsp"/>
    </f:facet>

    <h:panelGroup layout="block" styleClass="pure-g-r">
        <h:form id="form" styleClass="pure-form pure-u-1">
            <a4j:keepAlive beanName="ExportadorListaControle"/>
            <input id="chavePrimaria" type="hidden" value="" name="chavePrimaria"/>

            <%-- INICIO CONTENT --%>
            <h:panelGroup>

                <%-- INICIO COMANDOS --%>
                <h:panelGroup layout="block" styleClass="pure-g-r pure-u-12-12 margin-0-auto titulo-topo">
                    <h:panelGroup layout="block" styleClass="pure-form pure-form-aligned pure-u-2-3"></h:panelGroup>
                    <h:panelGroup layout="block" styleClass="pure-u-1-3 text-right">
                        <h:panelGroup layout="block" styleClass="controles">

                            <h:outputText styleClass="texto-font texto-size-14 texto-cor-cinza" value="Situação: "/>
                            <h:panelGroup layout="block" styleClass="cb-container" style="margin-right: 10px;">
                                <h:selectOneMenu id="tipoSituacao" styleClass="exportadores" style="margin-right: 10px;"
                                                 value="#{ConfiguracaoNotaFiscalControle.tipo}">
                                    <f:selectItems value="#{ConfiguracaoNotaFiscalControle.listaTipo}"/>
                                    <a4j:support event="onchange" oncomplete="recarregarTabelaConfigNotas()"/>
                                </h:selectOneMenu>
                            </h:panelGroup>

                            <a4j:commandLink id="btnExcel"
                                             styleClass="exportadores"
                                             actionListener="#{ConfiguracaoNotaFiscalControle.exportar}"
                                             oncomplete="abrirPopup('UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/vnd.ms-excel','Transacoes', 800,200);#{ExportadorListaControle.msgAlert}"
                                             accesskey="3">
                                <f:attribute name="tipo" value="xls"/>
                                <f:attribute name="atributos" value="codigo=Código,descricao=Descrição,cnae=CNAE"/>
                                <f:attribute name="prefixo" value="CNAE"/>
                                <h:outputText title="Exportar para o formato Excel" styleClass="btn-print-2 excel"/>
                            </a4j:commandLink>

                            <a4j:commandLink id="btnPDF"
                                             styleClass="exportadores margin-h-10"
                                             actionListener="#{ConfiguracaoNotaFiscalControle.exportar}"
                                             oncomplete="abrirPopup('UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/pdf','Transacoes', 800,200);#{ExportadorListaControle.msgAlert}"
                                             accesskey="4">
                                <f:attribute name="tipo" value="pdf"/>
                                <f:attribute name="atributos" value="codigo=Código,descricao=Descrição,cnae=CNAE"/>
                                <f:attribute name="prefixo" value="CNAE"/>
                                <h:outputText title="Exportar para o formato PDF" styleClass="btn-print-2 pdf"/>
                            </a4j:commandLink>
                            <a4j:commandLink id="btnLog"
                                             styleClass="exportadores margin-h-10"
                                             action="#{ConfiguracaoNotaFiscalControle.realizarConsultaLogObjetoGeral}"
                                             oncomplete="abrirPopup('visualizadorLogForm.jsp', 'VisualizadorLog', 785, 595);">
                                <h:outputText title="visualizar log geral da entidade" styleClass="btn-print-2 log"/>
                            </a4j:commandLink>

                            <a4j:commandLink id="btnNovo"
                                             styleClass="pure-button pure-button-primary"
                                             action="#{ConfiguracaoNotaFiscalControle.novo}"
                                             accesskey="1">
                                &nbsp ${msg_bt.btn_cadastrar_novo}
                            </a4j:commandLink>
                        </h:panelGroup>
                    </h:panelGroup>
                </h:panelGroup>
                <%-- FIM COMANDOS --%>

                <table id="tblConfig" class="tabelaConfig pure-g-r pure-u-11-12 margin-0-auto">
                    <thead>
                    <th>CÓDIGO</th>
                    <th>DESCRIÇÃO</th>
                    <th>CIDADE</th>
                    <th>CNAE</th>
                    <th>CNPJ</th>
                    <th>TIPO</th>
                    <th>SITUAÇÃO</th>
                    <th>AMBIENTE</th>
                    <th>EMPRESA</th>
                    <th>CERTIFICADO</th>
                    </thead>
                    <tbody></tbody>
                </table>

                <a4j:jsFunction name="jsEditar" action="#{ConfiguracaoNotaFiscalControle.editar}" reRender="mensagem"/>

            </h:panelGroup>
            <%-- FIM CONTENT --%>

            <%-- INICIO FOOTER --%>
            <h:panelGroup id="mensagem" layout="block" styleClass="pure-u-11-12 margin-0-auto">
                <h:graphicImage id="iconSucesso" rendered="#{ConfiguracaoNotaFiscalControle.sucesso}"
                                value="./imagens/sucesso.png"/>
                <h:graphicImage id="iconErro" rendered="#{ConfiguracaoNotaFiscalControle.erro}"
                                value="./imagens/erro.png"/>

                <h:outputText styleClass="mensagem" rendered="#{not empty ConfiguracaoNotaFiscalControle.mensagem}"
                              value=" #{ConfiguracaoNotaFiscalControle.mensagem}"/>
                <h:outputText styleClass="mensagemDetalhada"
                              rendered="#{not empty ConfiguracaoNotaFiscalControle.mensagemDetalhada}"
                              value=" #{ConfiguracaoNotaFiscalControle.mensagemDetalhada}"/>
            </h:panelGroup>
            <%-- FIM FOOTER --%>
        </h:form>


    </h:panelGroup>

    <script src="beta/js/ext-funcs.js" type="text/javascript"></script>

    <script>
        function recarregarTabelaConfigNotas() {
            var tipoSituacao = document.getElementById("form:tipoSituacao").value;
            tabelaAtual.dataTable().fnDestroy(0);
            iniciarTabela("tabelaConfig", "${contexto}/prest/basico/configuracaoNotaFiscal?situacao="+tipoSituacao, 1, "asc", "", true);
        }

        jQuery(window).on("load", function () {
            iniciarTabela("tabelaConfig", "${contexto}/prest/basico/configuracaoNotaFiscal?situacao=${ConfiguracaoNotaFiscalControle.tipo}", 1, "asc", "", true);
        });
    </script>

</f:view>

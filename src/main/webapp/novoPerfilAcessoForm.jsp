<%--
  Created by IntelliJ IDEA.
  User: Rafael
  Date: 14/05/2016
  Time: 10:22
  To change this template use File | Settings | File Templates.
--%>
<%@page contentType="text/html;charset=UTF-8" %>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>
<f:loadBundle var="CElabels" basename="propriedades.CElabels"/>
<head>
    <%@include file="/includes/include_import_minifiles.jsp" %>

</head>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<link href="beta/css/pure-min.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/bootstrap-tabs.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/pure-ext.css" type="text/css" rel="stylesheet"/>
<script type="text/javascript" language="javascript" src="script/negociacaoContrato_1.0.min.js"></script>
<script src="beta/js/jquery.js" type="text/javascript"></script>
<script src="beta/js/jquery.dataTables.min.js" type="text/javascript"></script>
<script src="beta/js/DT_bootstrap.js" type="text/javascript"></script>
<script src="beta/js/bootstrap-tab.js" type="text/javascript"></script>


<link href="./css/tooltipster/tooltipster.css" rel="stylesheet" type="text/css"/>
<link href="./css/tooltipster/tooltipster-light.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" language="javascript" src="hoverform.js"></script>
<link href="beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<script type="text/javascript" src="script/tooltipster/jquery.tooltipster.min.js"></script>
<script>
    jQuery.noConflict();
</script>
<style type="text/css">
    .rich-tabpanel-content {
        border-right: none;
        border-left: none;
        border-bottom: none;
    }

    .button, input, select, textarea {
        font-size: 11px;
    }
</style>

<%@include file="includes/verificaModulo.jsp" %>
<c:set var="contexto" value="${pageContext.request.contextPath}" scope="request" />

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText value="#{msg_aplic.prt_PerfilAcesso_tituloForm}"/>
    </title>
    <c:set var="titulo" scope="session" value="${msg_aplic.prt_PerfilAcesso_tituloForm}"/>

    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <f:facet name="header">
            <jsp:include page="topoReduzido_material.jsp"/>
        </f:facet>


        <h:form id="form" styleClass="font-size-Em-max-max">
            <input type="hidden" value="${modulo}" name="modulo"/>

            <h:panelGrid columns="1" width="100%" >
                <h:panelGroup layout="block" styleClass="container-botoes fundoCinza">
                    <c:if test="${modulo eq 'zillyonWeb'}">
                        <h:panelGroup rendered="#{PerfilAcessoControle.perfilAcessoVO.codigo == 0}">
                            <a4j:commandLink id="novo2" immediate="true"
                                             action="#{PerfilAcessoControle.novo}"
                                             value="#{msg_bt.btn_novo}" title="#{msg.msg_novo_dados}"
                                             accesskey="1"
                                             styleClass="botaoSecundario texto-size-14"/>
                        </h:panelGroup>

                        <h:outputText value="    "/>

                        <a4j:commandLink id="salvar2" action="#{PerfilAcessoControle.gravar}"
                                         value="#{msg_bt.btn_gravar}" title="#{msg.msg_gravar_dados}"
                                         accesskey="2"
                                         oncomplete="#{PerfilAcessoControle.mensagemNotificar}"
                                         styleClass="botaoPrimario texto-size-14"/>

                        <h:outputText value="    "/>

                        <h:panelGroup id="grupoMensagem" rendered="#{PerfilAcessoControle.perfilAcessoVO.codigo gt 0}">

                            <a4j:commandButton id="excluir2" reRender="mdlMensagemGenerica"
                                               oncomplete="#{PerfilAcessoControle.msgAlert}" action="#{PerfilAcessoControle.confirmarExcluir}" value="#{msg_bt.btn_excluir}"
                                               alt="#{msg.msg_excluir_dados}" accesskey="3" styleClass="botaoSecundario texto-size-14" style="border: 0px" />


                        </h:panelGroup>

                        <h:outputText value="    "/>

                        <a4j:commandLink id="consultar2" immediate="true"
                                         action="#{PerfilAcessoControle.inicializarConsultar}"
                                         value="#{msg_bt.btn_voltar_lista}"
                                         title="#{msg.msg_consultar_dados}"
                                         accesskey="4" styleClass="botaoSecundario texto-size-14"/>

                        <h:outputText value="    "/>

                        <a4j:commandLink id="visualizarLog2"
                                         immediate="true"
                                         action="#{PerfilAcessoControle.realizarConsultaLogObjetoSelecionado}"
                                         style="display: inline-block;padding: 8px 15px;"
                                         accesskey="5"
                                         styleClass="botaoSecundario texto-size-16"
                                         oncomplete="abrirPopup('visualizadorLogForm.jsp', 'VisualizadorLog', 785, 595);">
                            <h:outputText styleClass="fa-icon-list texto-cor-cinza texto-size-14" />
                        </a4j:commandLink>

                    </c:if>

                    <c:if test="${modulo eq 'centralEventos'}">
                        <h:panelGroup rendered="#{PerfilAcessoControle.perfilAcessoVO.codigo == 0}">
                            <h:commandLink id="novo2" immediate="true"
                                           action="#{PerfilAcessoControle.novo}"
                                           value="#{msg_bt.btn_novo}"
                                           title="#{msg.msg_novo_dados}"
                                           accesskey="1" styleClass="botaoSecundario texto-size-14"/>
                        </h:panelGroup>

                        <h:outputText value="    "/>

                        <a4j:commandLink id="salvar2" action="#{PerfilAcessoControle.gravarCE}"
                                       value="#{msg_bt.btn_gravar}"
                                       title="#{msg.msg_gravar_dados}"
                                       oncomplete="#{PerfilAcessoControle.mensagemNotificar}"
                                       accesskey="2" styleClass="botaoPrimario texto-size-14"
                                       actionListener="#{PerfilAcessoControle.autorizacao}">
                            <!-- Entidade.PERFIL ACESSO -->
                            <f:attribute name="entidade" value="120"/>
                            <!-- operacao.GRAVAR -->
                            <f:attribute name="operacao" value="G"/>
                        </a4j:commandLink>


                        <h:outputText value="    "/>

                        <h:panelGroup rendered="#{PerfilAcessoControle.perfilAcessoVO.codigo gt 0}">
                            <a4j:commandLink id="excluir2"
                                             onclick="if(!confirm('Confirma exclusão dos dados?')){return true;}"
                                           action="#{PerfilAcessoControle.excluirCE}"
                                           value="#{msg_bt.btn_excluir}"
                                           oncomplete="#{PerfilAcessoControle.mensagemNotificar}"
                                           accesskey="3" styleClass="botaoSecundario texto-size-14"
                                           actionListener="#{PerfilAcessoControle.autorizacao}">
                                <!-- Entidade.PERFIL ACESSO -->
                                <f:attribute name="entidade" value="120"/>
                                <!-- operacao.EXCLUIR -->
                                <f:attribute name="operacao" value="E"/>
                            </a4j:commandLink>
                        </h:panelGroup>


                        <h:outputText value="    "/>

                        <a4j:commandLink id="consultar2" immediate="true"
                                       action="#{PerfilAcessoControle.inicializarConsultar}"
                                       value="#{msg_bt.btn_voltar_lista}"
                                       title="#{msg.msg_consultar_dados}" accesskey="4"
                                       styleClass="botaoSecundario texto-size-16"
                                       oncomplete="#{PerfilAcessoControle.mensagemNotificar}"
                                       actionListener="#{PerfilAcessoControle.autorizacao}">
                            <h:outputText styleClass="fa-icon-search texto-cor-cinza texto-size-14" />
                            <!-- Entidade.PERFIL ACESSO -->
                            <f:attribute name="entidade" value="120"/>
                            <!-- operacao.CONSULTAR -->
                            <f:attribute name="operacao" value="C"/>
                        </a4j:commandLink>


                    </c:if>
                </h:panelGroup>
                <h:panelGroup layout="block" style="margin: 10px;width: calc(100% - 20px)" styleClass="font-size-Em-max">
                    <h:panelGrid columns="2" styleClass="font-size-Em-max">
                        <h:panelGroup layout="block" >
                            <h:outputText styleClass="rotuloCampos" value="#{msg_aplic.prt_PerfilAcesso_tipo} "
                                          id="outputTipos"  style="display: block;padding-bottom: 5px"/>
                            <h:panelGroup layout="block" styleClass="cb-container" style="width: 100%">
                                <h:selectOneMenu
                                        value="#{PerfilAcessoControle.perfilAcessoVO.tipo}"
                                        styleClass="tooltipster"
                                        title="O tipo do Perfil de Acesso faz parte do novo recurso de <b>Assistente de Gestão</b>. <br/>O <b>Assistente de Gestão</b> do ZillyonWeb exibirá dicas direcionadas a cada tipo específico de usuário. <br/> Um gestor, por exemplo, receberá dicas de como melhorar seus indicadores de gestão, enquanto um consultor verá dicas relacionadas a vendas, CRM, etc. Lembre-se que ao alterar este tipo, você altera de todos os usuários que tem este Perfil de Acesso."
                                        id="selectTipos">
                                    <f:selectItem itemValue="" itemLabel=""/>
                                    <f:selectItems value="#{PerfilAcessoControle.tipos}"/>
                                </h:selectOneMenu>
                            </h:panelGroup>
                        </h:panelGroup>
                        <h:panelGroup layout="block" style="margin: 10px">
                            <h:outputText styleClass="rotuloCampos"
                                          style="display: block;padding-bottom: 5px"
                                          value="#{msg_aplic.prt_PerfilAcesso_label_descricao} "/>
                            <h:inputText id="nome" size="50" maxlength="50" styleClass="inputTextClean"
                                         value="#{PerfilAcessoControle.perfilAcessoVO.nome}"/>
                        </h:panelGroup>
                        <h:panelGroup layout="block" >
                            <h:panelGroup layout="block">
                                <a4j:commandLink styleClass="linkPadrao texto-size-16" action="#{PerfilAcessoControle.aplicarPermissoesPadraoTipo}"
                                                 reRender="containerPerfilAcesso" >
                                    <h:outputText  styleClass="tooltipster texto-size-14 texto-cor-azul" title="Aplica as permissões sugeridas para o tipo de perfil selecionado"
                                                   value="Aplicar permissões padrão  " />
                                    <h:outputText  styleClass="fa-icon-plus-sign texto-size-12 texto-cor-azul"/>
                                </a4j:commandLink>
                            </h:panelGroup>
                        </h:panelGroup>
                      </h:panelGrid>
                    <h:panelGroup layout="block" style="margin: 30px 0px 0px 0px;">
                        <h:outputText style="font-size: 14px;" styleClass="texto-font texto-bold texto-cor-cinza" value="PESQUISAR AS PERMISSÕES"/>
                    </h:panelGroup>
                    <h:panelGroup layout="block" style="margin:10px 0px 10px 0px;line-height: 60px;">
                        <h:inputText styleClass="inputTextClean inputBuscaPerfil"
                                     value="Pesquisar"
                                     id="perfilAcesso"
                                     onfocus="if(this.value == 'Pesquisar'){this.value=''}"
                                     onblur="if(this.value==''){this.value='Pesquisar';}" onkeyup="buscarPermissao();"/>
                        <a4j:commandLink styleClass="pull-right linkPadrao texto-font texto-size-14"
                                         action="#{PerfilAcessoControle.abrirModalCopiarPerfil}"
                                         value="#{msg_aplic.prt_PerfilAcesso_copiarPermissoes}"
                                         reRender="formConsultaPerfilAcesso"
                                         oncomplete="Richfaces.showModalPanel('panelConsultaPerfilAcesso')">
                        </a4j:commandLink>
                    </h:panelGroup>
                    <h:message for="nome" styleClass="mensagemDetalhada"/>
                </h:panelGroup>

                <rich:tabPanel width="100%" activeTabClass="true" headerAlignment="rigth" styleClass="navBarCustom" switchType="ajax">
                    <rich:tab id="abaPerfilAcesso" label="Perfil de Acesso" switchType="client">
                        <h:panelGroup  id="containerPerfilAcesso" style="width: 100%" layout="block">
                            <a4j:repeat id="corpo" var="permissoes"
                                        value="#{PerfilAcessoControle.listaPermissoes}">
                                <rich:simpleTogglePanel id="modulo" switchType="client"
                                                        styleClass="togglePanelCustom bordaCinza"
                                                        opened="#{permissoes.expandir}"
                                                        width="100%">
                                    <f:facet name="header">
                                        <h:panelGroup id="headerPanel">
                                            <h:outputText styleClass="texto-font texto-size-16 texto-cor-cinza"
                                                          value="#{permissoes.modulo} "/>
                                            <h:panelGroup layout="block" styleClass="pull-right col-text-align-left" style="min-width: 15%">
                                                <h:outputText styleClass="texto-font texto-size-20 texto-cor-cinza"
                                                              value=" #{permissoes.countEntidade} Cadastros"/>
                                            </h:panelGroup>
                                            <h:panelGroup layout="block" styleClass="pull-right col-text-align-center" style="min-width:15%">

                                                <h:outputText styleClass="texto-font texto-size-20 texto-cor-cinza"
                                                              value=" #{permissoes.countAcao} Ações"/>
                                            </h:panelGroup>
                                        </h:panelGroup>
                                    </f:facet>
                                    <h:panelGroup layout="block" styleClass="headerEntidade"
                                                  rendered="#{not empty permissoes.entidades}"
                                                  style="height: 50px;text-align: left;line-height: 50px;">
                                        <h:panelGroup layout="block" styleClass="chk-fa-container"
                                                      style="display: inline-block">
                                            <h:selectBooleanCheckbox id="marcarTodos"
                                                                     value="#{permissoes.selecionarTodoEntidade}">
                                                <a4j:support event="onclick" reRender="entidades,headerPanel"
                                                             action="#{PerfilAcessoControle.selecionarTudoEntidade}"/>
                                            </h:selectBooleanCheckbox>
                                            <span></span>
                                        </h:panelGroup>
                                        <h:outputText style="margin-left: 7px;display: inline-block;width: auto"
                                                styleClass="text-font texto-size-16 texto-cor-cinza texto-bold"
                                                value="Cadastros"/>
                                    </h:panelGroup>
                                    <rich:dataTable id="entidades" styleClass="tabelaSimplesCustom" width="100%"
                                                    var="permissao"
                                                    value="#{permissoes.entidades}">
                                        <rich:column width="30" >
                                            <h:panelGroup layout="block" styleClass="chk-fa-container" style="display: table;width: 30px;text-align: right;">
                                                <h:selectBooleanCheckbox id="permissao"

                                                                         value="#{permissao.selecionado}">
                                                    <a4j:support event="onclick" action="#{PerfilAcessoControle.selecionarPermissao}" oncomplete="buscarPermissao();"
                                                                 reRender="entidades,headerPanel"/>
                                                </h:selectBooleanCheckbox>
                                                <span style="margin-left: 0px;right: 0px;left:inherit"></span>
                                            </h:panelGroup>
                                        </rich:column>
                                        <rich:column width="70%">
                                            <h:outputText style="font-size: 16px;" styleClass="texto-font texto-cor-cinza"
                                                         value=" #{permissao.apresentarPermissao}" />
                                        </rich:column>
                                        <rich:column styleClass="col-text-align-left">
                                            <h:outputText style="font-size: 14px;"  styleClass="texto-font texto-cor-complementar"
                                                          value="#{permissao.hint}" />
                                        </rich:column>
                                        <rich:column  styleClass="col-text-align-right" >
                                            <h:panelGroup id="containerComboBox" layout="block" styleClass="cb-container"   rendered="#{permissao.selecionado}">
                                                <h:selectOneMenu
                                                        style="font-size:16px;"

                                                        onblur="blurinput(this);"
                                                        onfocus="focusinput(this);"
                                                        styleClass="form"
                                                        value="#{permissao.permissoes}">
                                                    <f:selectItems
                                                            value="#{PerfilAcessoControle.listaSelectItemPermissoesPermissao}"/>
                                                </h:selectOneMenu>
                                            </h:panelGroup>
                                            <h:panelGroup layout="block" style="width: 170px;height: calc(2.4em + 1px)"  rendered="#{!permissao.selecionado}">
                                            </h:panelGroup>
                                        </rich:column>
                                    </rich:dataTable>
                                    <h:panelGroup layout="block" rendered="#{not empty permissoes.acoes}"
                                                  styleClass="headerEntidade"
                                                  style="height: 50px;text-align: left;line-height: 50px;">
                                        <h:panelGroup layout="block" styleClass="chk-fa-container"
                                                      style="display: inline-block">
                                            <h:selectBooleanCheckbox value="#{permissoes.selecionarTodoAcao}">
                                                <a4j:support event="onclick" reRender="acoes,headerPanel"
                                                             action="#{PerfilAcessoControle.selecionarTudoFuncionalidade}"/>
                                            </h:selectBooleanCheckbox>
                                            <span></span>
                                        </h:panelGroup>
                                        <h:outputText style="margin-left: 4px;display: inline-block;width: auto"
                                                styleClass="text-font texto-size-16 texto-cor-cinza texto-bold"
                                                value="Ações"/>
                                    </h:panelGroup>
                                    <rich:dataTable id="acoes" styleClass="tabelaSimplesCustom" width="100%"
                                                    var="permissao"
                                                    value="#{permissoes.acoes}">
                                        <rich:column width="1%" >
                                            <h:panelGroup layout="block" styleClass="chk-fa-container" style="display: table;width: 30px;height: 2.4em;text-align: right;">
                                                <h:selectBooleanCheckbox id="permissao"
                                                                         value="#{permissao.selecionado}">
                                                    <a4j:support event="onclick"  action="#{PerfilAcessoControle.selecionarPermissao}"
                                                                 reRender="headerPanel"/>
                                                </h:selectBooleanCheckbox>
                                                <span  style="margin-left: 0px;right: 0px;left:inherit"></span>
                                            </h:panelGroup>
                                        </rich:column>
                                        <rich:column style="text-align:left;" width="70%">
                                            <h:outputText style="font-size: 16px;" styleClass="texto-font texto-cor-cinza"
                                                          value=" #{permissao.apresentarPermissao}" />
                                        </rich:column>
                                        <rich:column  styleClass="col-text-align-left">
                                            <h:outputText style="font-size: 14px;" styleClass="texto-font texto-cor-complementar"
                                                          value=" #{permissao.hint}" />
                                        </rich:column>
                                    </rich:dataTable>
                                </rich:simpleTogglePanel>
                            </a4j:repeat>
                        </h:panelGroup>
                        <c:if test="${modulo eq 'centralEventos'}">
                                <!-- -------------------------------------- ENTIDADES -------------------------------------- -->
                                <table id="entidadesCE" style="width: 100%;">
                                    <tr>
                                        <td>

                                            <rich:simpleTogglePanel id="centralEventosCadastro"
                                                                    opened="#{PerfilAcessoControle.expandirRetrairEntidades}"
                                                                     styleClass="togglePanelCustom bordaCinza"
                                                                    switchType="client" width="100%">
                                                <f:facet name="header">
                                                    <h:panelGroup>
                                                        <h:outputText styleClass="texto-font texto-size-16 texto-cor-cinza"
                                                                      value="#{CElabels['entidade.centralEventos.cadastro']}"/>
                                                    </h:panelGroup>
                                                </f:facet>
                                                <h:selectBooleanCheckbox
                                                        value="#{PerfilAcessoControle.marcarTudoOperacoesCE}">
                                                    <a4j:support event="onclick" reRender="centralEventosCadastro"
                                                                 action="#{PerfilAcessoControle.selecionarTudoOperacoesCE}"/>
                                                </h:selectBooleanCheckbox><h:outputText styleClass="text"
                                                                                        value=" #{msg_aplic.prt_PerfilAcesso_marcarTodos}"/>

                                                <h:panelGrid cellpadding="0" cellspacing="0" style="width: 100%;"
                                                             columns="2"
                                                             columnClasses="classColunaEsquerda"
                                                             rowClasses="linhaImparAcesso, linhaParAcesso">
                                                    <c:forEach var="entidade"
                                                               items="${PerfilAcessoControle.entidadesAutorizacoes}">


                                                        <h:panelGroup styleClass="text">&nbsp;<c:out
                                                                value='${entidade.descricao}'/>
                                                        </h:panelGroup>
                                                        <h:panelGrid style="width: 100%;" columns="8"
                                                                     columnClasses="text">
                                                            <c:forEach var="operacao"
                                                                       items="${PerfilAcessoControle.operacoes}">
                                                                <h:panelGroup>

                                                                    <c:choose>
                                                                        <c:when test="${fn:containsIgnoreCase(entidade.operacao,operacao.codigo)}">
                                                                            <input type="checkbox"
                                                                                   onclick="adicionarOperacao('${entidade.entidade}','${operacao.codigo}');"
                                                                                   checked="checked"/>
                                                                        </c:when>
                                                                        <c:otherwise>
                                                                            <input type="checkbox"
                                                                                   onclick="adicionarOperacao('${entidade.entidade}','${operacao.codigo}');"/>
                                                                        </c:otherwise>
                                                                    </c:choose>
                                                                    <c:out value='${operacao.descricao}'/>
                                                                </h:panelGroup>

                                                            </c:forEach>
                                                        </h:panelGrid>

                                                    </c:forEach>
                                                </h:panelGrid>

                                                <h:inputHidden id="operacaoConcat"
                                                               value="#{PerfilAcessoControle.operacoesTela}"/>


                                            </rich:simpleTogglePanel>

                                        </td>
                                    </tr>
                                    <tr>
                                        <td>

                                            <!-- -------------------------------------- FUNCIONALIDADES-------------------------------------- -->

                                            <rich:simpleTogglePanel id="centralEventosFuncionalidades"
                                                                    opened="#{PerfilAcessoControle.expandirRetrairEntidades}" styleClass="togglePanelCustom bordaCinza"
                                                                    switchType="client" width="100%">
                                                <f:facet name="header">
                                                    <h:panelGroup>
                                                        <h:outputText styleClass="texto-font texto-size-16 texto-cor-cinza"
                                                                      value="#{CElabels['entidade.centralEventos.funcoes']}"/>
                                                    </h:panelGroup>
                                                </f:facet>
                                                <h:selectBooleanCheckbox
                                                        value="#{PerfilAcessoControle.marcarTudoFuncionalidadesCE}">
                                                    <a4j:support event="onclick" reRender="funcionalidades"
                                                                 action="#{PerfilAcessoControle.selecionarTudoFuncionalidadesCE}"/>
                                                </h:selectBooleanCheckbox><h:outputText styleClass="text"
                                                                                        value=" #{msg_aplic.prt_PerfilAcesso_marcarTodos}"/>
                                                <rich:dataTable id="funcionalidades" rowClasses="linhaImpar, linhaPar"
                                                                width="100%"
                                                                var="funcionalidade"
                                                                value="#{PerfilAcessoControle.funcionalidades}">
                                                    <rich:column>

                                                        <table width="100%">
                                                            <tr>
                                                                <td width="10%"></td>
                                                                <td width="90%"><h:selectBooleanCheckbox
                                                                        value="#{funcionalidade.selecionado}"></h:selectBooleanCheckbox>
                                                                    <h:outputText styleClass="text"
                                                                                  value="#{funcionalidade.descricao}"></h:outputText></td>

                                                            </tr>
                                                        </table>


                                                    </rich:column>
                                                </rich:dataTable>

                                            </rich:simpleTogglePanel>


                                        </td>
                                    </tr>
                                </table>
                            </c:if>
                    </rich:tab>

                    <rich:tab id="abaUsuariosPerfilAcesso" label="Usuários com este perfil" switchType="client">
                        <h:form id="form">
                            <a4j:keepAlive beanName="ExportadorListaControle" />
                            <input id="chavePrimaria" type="hidden" value="" name="chavePrimaria"/>
                            <h:panelGrid id="teste" columns="1" width="98%" columnClasses="colunaCentralizada"
                                         style="background: gainsboro;margin: 1%;">
                                <h:panelGroup layout="block" id="titulousuario" style="margin: 5px;">
                                    <h:outputText value="Usuários que tem o perfil de acesso "/>
                                    <h:outputText style="font-weight: bold;"
                                                  value="#{PerfilAcessoControle.perfilAcessoVO.nome}"/>
                                </h:panelGroup>
                            </h:panelGrid>

                            <h:panelGroup>
                                <h:panelGroup layout="block" styleClass="pull-right" style="padding-top: 4px">
                                    <h:panelGroup layout="block" styleClass="controles">
                                        <a4j:commandLink id="btnExcel"
                                                         styleClass="exportadores margin-h-10"
                                                         actionListener="#{PerfilAcessoControle.exportarUsuariosPerfil}"
                                                         oncomplete="abrirPopup('UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/vnd.ms-excel','Transacoes', 800,200);#{ExportadorListaControle.msgAlert}"
                                                         accesskey="3">
                                            <f:attribute name="tipo" value="xls"/>
                                            <f:attribute name="atributos" value="codigo=Código Usuário,userName=Usuário,nome=Nome,perfilAcesso=Perfil Acesso,nomeEmpresa=Empresa"/>
                                            <f:attribute name="prefixo" value="PerfilAcesso"/>
                                            <a4j:actionparam noEscape="true" name="paramsTableFiltrada" value="retornaDadosDT()"/>
                                            <h:outputText title="Exportar para o formato Excel" styleClass="btn-print-2 excel"/>
                                        </a4j:commandLink>

                                        <a4j:commandLink id="btnPDF"
                                                         styleClass="exportadores margin-h-10"
                                                         actionListener="#{PerfilAcessoControle.exportarUsuariosPerfil}"
                                                         oncomplete="abrirPopup('UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/pdf','Transacoes', 800,200);#{ExportadorListaControle.msgAlert}"
                                                         accesskey="4">
                                            <f:attribute name="tipo" value="pdf"/>
                                            <f:attribute name="atributos" value="codigo=Código Usuário,userName=Usuário,nome=Nome,perfilAcesso=Perfil Acesso,nomeEmpresa=Empresa"/>
                                            <f:attribute name="prefixo" value="PerfilAcesso"/>
                                            <f:attribute name="titulo" value="Perfil de Acesso"/>
                                            <a4j:actionparam noEscape="true" name="paramsTableFiltrada" value="retornaDadosDT()"/>
                                            <h:outputText title="Exportar para o formato PDF" styleClass="btn-print-2 pdf"/>
                                        </a4j:commandLink>

                                        <h:outputText styleClass="texto-font texto-size-18 texto-cor-cinza" value="Situação "/>
                                        <h:panelGroup  styleClass="cb-container" style="margin-right: 10px;">
                                            <h:selectOneMenu id="situacao" styleClass="exportadores" value="#{PerfilAcessoControle.situacaoFiltro}">
                                                <f:selectItems value="#{PerfilAcessoControle.listaSelectItemSituacao}"/>
                                                <a4j:support event="onchange" oncomplete="recarregarTabelaUsuarioPerfil()"/>
                                            </h:selectOneMenu>
                                        </h:panelGroup>
                                    </h:panelGroup>
                                </h:panelGroup>
                                <table id="tblUsuarioPerfilAcesso"
                                       class="tabelaUsuarioPerfilAcesso pure-g-r pure-u-11-12 margin-0-auto">
                                    <thead>
                                    <th>${"Código Usuário"}</th>
                                    <th>${"Usuário"}</th>
                                    <th>${"Nome"}</th>
                                    <th>${"Perfil Acesso"}</th>
                                    <th>${"Empresa"}</th>
                                    </thead>
                                    <tbody></tbody>
                                </table>

                                <a4j:jsFunction name="jsEditar"
                                                rendered="#{LoginControle.permissaoAcessoMenuVO.usuario}"
                                                action="#{PerfilAcessoControle.abrirUsuario}"
                                                oncomplete="#{PerfilAcessoControle.onComplete}"
                                                reRender="mensagem"/>
                            </h:panelGroup>

                            <h:panelGroup id="mensagem" layout="block" styleClass="pure-u-11-12 margin-0-auto">
                                <h:graphicImage id="iconSucesso" rendered="#{PerfilAcessoControle.sucesso}"
                                                value="./imagens/sucesso.png"/>
                                <h:graphicImage id="iconErro" rendered="#{PerfilAcessoControle.erro}"
                                                value="./imagens/erro.png"/>

                                <h:outputText styleClass="mensagem"
                                              rendered="#{not empty PerfilAcessoControle.mensagem}"
                                              value=" #{PerfilAcessoControle.mensagem}"/>
                                <h:outputText styleClass="mensagemDetalhada"
                                              rendered="#{not empty PerfilAcessoControle.mensagemDetalhada}"
                                              value=" #{PerfilAcessoControle.mensagemDetalhada}"/>
                            </h:panelGroup>
                        </h:form>
                    </rich:tab>

                    <c:if test="${PerfilAcessoControle.exibirReplicarRedeEmpresa}">
                        <rich:tab id="abaReplicarEmpresaPerfilAcesso" label="Replicar Empresa" switchType="client">
                            <a4j:keepAlive beanName="ExportadorListaControle"/>
                            <input id="chavePrimaria" type="hidden" value="" name="chavePrimaria"/>
                            <h:panelGrid columns="1" width="98%" columnClasses="colunaCentralizada"
                                         style="background: gainsboro;margin: 1%;">
                                <h:panelGroup layout="block" id="titulousuario" style="margin: 5px;">
                                    <h:outputText value="Replicar perfil de acesso "/>
                                    <h:outputText style="font-weight: bold;"
                                                  value="#{PerfilAcessoControle.perfilAcessoVO.nome}"/>
                                    <h:outputText value=" rede empresa."/>
                                </h:panelGroup>
                            </h:panelGrid>

                            <h:panelGrid columns="1" width="100%">
                                <h:panelGrid columns="3" style="border-style: solid;" id="contadorReplicaPlano"
                                             columnClasses="colunaCentralizada, colunaCentralizada, colunaCentralizada"
                                             width="100%">
                                    <h:outputText value="Unidades" styleClass="botoes nvoBt"/>
                                    <h:outputText value="Replicadas" styleClass="botoes nvoBt"/>
                                    <h:outputText value="Não Replicadas" styleClass="botoes nvoBt"/>
                                    <h:outputText value="#{PerfilAcessoControle.listaPerfisAcessoRedeEmpresaSize}"
                                                  style="font-size: 20pt; font-weight: bold;"/>
                                    <h:outputText value="#{PerfilAcessoControle.listaPerfisAcessoRedeEmpresaSincronizado}"
                                                  style="color: #0f4c36; font-size: 20pt; font-weight: bold;"/>
                                    <h:outputText
                                            value="#{PerfilAcessoControle.listaPerfisAcessoRedeEmpresaSize - PerfilAcessoControle.listaPerfisAcessoRedeEmpresaSincronizado}"
                                            style="color: #8b0000; font-size: 20pt; font-weight: bold;"/>
                                </h:panelGrid>
                                <h:panelGrid columns="1" id="contadorReplicaPlano2"
                                             columnClasses="colunaDireita"
                                             width="100%"
                                             style="margin-top: 20px; margin-bottom: 1px">
                                    <h:panelGroup layout="block">
                                        <a4j:commandButton value="Replicar Todas" styleClass="botoes nvoBt"
                                                           action="#{PerfilAcessoControle.replicarTodas}"
                                                           reRender="listaEmpresasReplicar, contadorReplicaPlano"
                                                           ajaxSingle="true" immediate="true"/>
                                        <a4j:commandButton value="Replicar Selecionadas" styleClass="botoes nvoBt btSec"
                                                           action="#{PerfilAcessoControle.replicarSelecionadas}"
                                                           reRender="listaEmpresasReplicar, contadorReplicaPlano"
                                                           ajaxSingle="true" immediate="true"/>
                                        <a4j:commandButton value="Limpar Selecionadas" styleClass="botoes nvoBt btSec"
                                                           action="#{PerfilAcessoControle.limparReplicar}"
                                                           reRender="listaEmpresasReplicar, contadorReplicaPlano"
                                                           ajaxSingle="true" immediate="true"/>
                                    </h:panelGroup>
                                </h:panelGrid>
                                <h:panelGrid columns="1" rowClasses="linhaImpar, linhaPar"
                                             columnClasses="colunaCentralizada" width="100%">
                                    <h:panelGrid columns="1" width="100%" styleClass="tabMensagens">
                                    </h:panelGrid>
                                    </br>
                                    </br>
                                    <h:dataTable id="listaEmpresasReplicar" width="100%" headerClass="subordinado"
                                                 styleClass="tabFormSubordinada"
                                                 rowClasses="linhaImpar, linhaPar"
                                                 columnClasses="colunaEsquerda, colunaEsquerda, colunaCentralizada, colunaEsquerda"
                                                 style="text-align: center;"
                                                 value="#{PerfilAcessoControle.listaPerfisAcessoRedeEmpresa}"
                                                 var="perfilAcessoRedeEmpresaReplicacao">
                                        <h:column>
                                            <f:facet name="header">
                                                <h:outputText value=""/>
                                            </f:facet>
                                            <h:selectBooleanCheckbox id="check" styleClass="form"
                                                                     rendered="#{!perfilAcessoRedeEmpresaReplicacao.dataAtualizacaoInformada}"
                                                                     value="#{perfilAcessoRedeEmpresaReplicacao.selecionado}">
                                                <a4j:support event="onchange" reRender="listaEmpresasReplicar"/>
                                            </h:selectBooleanCheckbox>
                                        </h:column>
                                        <h:column>
                                            <f:facet name="header">
                                                <h:outputText value="#{msg_aplic.prt_PlanoRedeEmpresa_nomeUnidade}"/>
                                            </f:facet>
                                            <h:outputText value="#{perfilAcessoRedeEmpresaReplicacao.nomeUnidade}"/>
                                        </h:column>
                                        <h:column>
                                            <f:facet name="header">
                                                <h:outputText value="#{msg_aplic.prt_PlanoRedeEmpresa_chave}"/>
                                            </f:facet>
                                            <h:outputText value="#{perfilAcessoRedeEmpresaReplicacao.chaveDestino}"/>
                                        </h:column>
                                        <h:column>
                                            <f:facet name="header">
                                                <h:outputText value=""/>
                                            </f:facet>
                                            <h:panelGroup>
                                                <a4j:commandButton id="replicarPlano"
                                                                   reRender="listaEmpresasReplicar, contadorReplicaPlano"
                                                                   ajaxSingle="true" immediate="true"
                                                                   rendered="#{!perfilAcessoRedeEmpresaReplicacao.dataAtualizacaoInformada}"
                                                                   action="#{PerfilAcessoControle.replicarUsuarioRedeEmpresaGeral}"
                                                                   value="Replicar"/>
                                                <h:graphicImage url="./images/check.png"
                                                                rendered="#{perfilAcessoRedeEmpresaReplicacao.dataAtualizacaoInformada}"/>
                                            </h:panelGroup>
                                        </h:column>
                                        <h:column>
                                            <f:facet name="header">
                                                <h:outputText
                                                        value="#{msg_aplic.prt_PlanoRedeEmpresa_mensagemSituacao}"/>
                                            </f:facet>
                                            <h:outputText
                                                    value="#{perfilAcessoRedeEmpresaReplicacao.mensagemSituacao}"/>
                                        </h:column>
                                        <h:column>
                                            <f:facet name="header">
                                                <h:outputText
                                                        value="Vínculo"/>
                                            </f:facet>
                                            <h:panelGroup>
                                                <a4j:commandButton
                                                        rendered="#{perfilAcessoRedeEmpresaReplicacao.dataAtualizacaoInformada}"
                                                        reRender="listaEmpresasReplicar, contadorReplicaPlano"
                                                        ajaxSingle="true" immediate="true"
                                                        action="#{PerfilAcessoControle.retirarVinculoReplicacao}"
                                                        value="Retirar"/>
                                            </h:panelGroup>
                                        </h:column>
                                    </h:dataTable>
                                </h:panelGrid>
                            </h:panelGrid>
                        </rich:tab>
                    </c:if>
                    <rich:tab id="abaDecontosPerfilAcesso" label="Descontos" switchType="client">

                        <h:panelGrid columns="1"  width="100%" columnClasses="colunaCentralizada">
                            <h:panelGrid id="panelFiltros" columns="1" columnClasses="colunaTopCentralizada" width="100%">
                                <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar" columnClasses="classEsquerda, classDireita" width="100%"
                                         style="padding-left:0px;">

                                    <!-- desconto negociação de contratos -->
                                    <h:outputText  styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold" value="#{msg_aplic.prt_PerfilAcesso_Desconto_Contrato}"/>
                                    <h:panelGroup styleClass="font-size-em-max" >
                                        <h:inputText id="valorDescontoPercentualContrato"
                                                     styleClass="inputTextClean noborderleft"
                                                     maxlength="5"
                                                     value="#{PerfilAcessoControle.perfilAcessoVO.porcetagemDescontoContrato}"
                                                     onkeypress="return formatar_percentuallimite(this,'.',',',event,5); "
                                                     onblur="blurinput(this);"
                                                     onfocus="focusinput(this);"
                                                     style="width: calc(70% - 3px);vertical-align: middle;margin-left: 3px">
                                            <f:converter converterId="FormatarPercentual"/>
                                        </h:inputText>
                                        <rich:toolTip
                                                value="Se a empresa estiver configurada para limitar os descontos por perfil de acesso, o lançamento de contratos passará a respeitar essa configuração. O desconto manual será limitado para que a soma do desconto por convênio e o desconto manual, respeite essa configuração "
                                                for="valorDescontoPercentualContrato" />
                                    </h:panelGroup>
                                </h:panelGrid>
                            </h:panelGrid>
                        </h:panelGrid>
                    </rich:tab>
                </rich:tabPanel>



                <h:panelGrid columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGroup layout="block" styleClass="container-botoes fundoCinza">

                            <c:if test="${modulo eq 'zillyonWeb'}">
                                <h:panelGroup rendered="#{PerfilAcessoControle.perfilAcessoVO.codigo == 0}">
                                    <a4j:commandLink id="novo" immediate="true"
                                                     action="#{PerfilAcessoControle.novo}"
                                                     value="#{msg_bt.btn_novo}" title="#{msg.msg_novo_dados}"
                                                     accesskey="1"
                                                     styleClass="botaoSecundario texto-size-14"/>
                                </h:panelGroup>

                                <h:outputText value="    "/>

                                    <a4j:commandButton id="salvar" action="#{PerfilAcessoControle.gravar}"
                                                 value="#{msg_bt.btn_gravar}" title="#{msg.msg_gravar_dados}"
                                                 accesskey="2"
                                                 oncomplete="#{PerfilAcessoControle.mensagemNotificar}"
                                                 styleClass="botaoPrimario texto-size-14"/>

                                <h:outputText value="    "/>

                                <h:panelGroup rendered="#{PerfilAcessoControle.perfilAcessoVO.codigo gt 0}">

                                    <a4j:commandButton id="excluir" reRender="mdlMensagemGenerica"
                                                       oncomplete="#{PerfilAcessoControle.msgAlert}" action="#{PerfilAcessoControle.confirmarExcluir}" value="#{msg_bt.btn_excluir}"
                                                       alt="#{msg.msg_excluir_dados}" accesskey="3" styleClass="botaoSecundario texto-size-14" style="border: 0px" />


                                </h:panelGroup>

                                <h:outputText value="    "/>

                                <a4j:commandLink id="consultar" immediate="true"
                                                 action="#{PerfilAcessoControle.inicializarConsultar}"
                                                 value="#{msg_bt.btn_voltar_lista}"
                                                 title="#{msg.msg_consultar_dados}"
                                                 accesskey="4" styleClass="botaoSecundario texto-size-14"/>

                                <h:outputText value="    "/>

                                <a4j:commandLink id="visualizarLog"
                                               immediate="true"
                                               action="#{PerfilAcessoControle.realizarConsultaLogObjetoSelecionado}"                   
                                               accesskey="5"
                                               styleClass="botaoSecundario texto-size-16"
                                               oncomplete="abrirPopup('visualizadorLogForm.jsp', 'VisualizadorLog', 785, 595);">
                                    <h:outputText styleClass="fa-icon-list texto-cor-cinza texto-size-14" />
                                </a4j:commandLink>

                            </c:if>

                            <c:if test="${modulo eq 'centralEventos'}">
                                <h:panelGroup rendered="#{PerfilAcessoControle.perfilAcessoVO.codigo == 0}">
                                        <h:commandLink id="novo" immediate="true"
                                                     action="#{PerfilAcessoControle.novo}"
                                                     value="#{msg_bt.btn_novo}"
                                                     title="#{msg.msg_novo_dados}"
                                                     accesskey="1" styleClass="botaoSecundario texto-size-14"/>
                                </h:panelGroup>

                                <h:outputText value="    "/>

                                <a4j:commandLink id="salvar" action="#{PerfilAcessoControle.gravarCE}"
                                                 value="#{msg_bt.btn_gravar}"
                                                 title="#{msg.msg_gravar_dados}"
                                                 oncomplete="#{PerfilAcessoControle.mensagemNotificar}"
                                                 accesskey="2" styleClass="botaoPrimario texto-size-14"
                                                 actionListener="#{PerfilAcessoControle.autorizacao}">
                                    <!-- Entidade.PERFIL ACESSO -->
                                    <f:attribute name="entidade" value="120"/>
                                    <!-- operacao.GRAVAR -->
                                    <f:attribute name="operacao" value="G"/>
                                </a4j:commandLink>


                                <h:outputText value="    "/>

                                <h:panelGroup rendered="#{PerfilAcessoControle.perfilAcessoVO.codigo gt 0}">
                                    <a4j:commandLink id="excluir"
                                                     onclick="if(!confirm('Confirma exclusão dos dados?')){return true;}"
                                                     action="#{PerfilAcessoControle.excluirCE}"
                                                     value="#{msg_bt.btn_excluir}"
                                                     oncomplete="#{PerfilAcessoControle.mensagemNotificar}"
                                                     accesskey="3" styleClass="botaoSecundario texto-size-14"
                                                     actionListener="#{PerfilAcessoControle.autorizacao}">
                                        <!-- Entidade.PERFIL ACESSO -->
                                        <f:attribute name="entidade" value="120"/>
                                        <!-- operacao.EXCLUIR -->
                                        <f:attribute name="operacao" value="E"/>
                                    </a4j:commandLink>
                                </h:panelGroup>


                                <h:outputText value="    "/>

                                <a4j:commandLink id="consultar" immediate="true"
                                                 action="#{PerfilAcessoControle.inicializarConsultar}"
                                                 value="#{msg_bt.btn_voltar_lista}"
                                                 title="#{msg.msg_consultar_dados}" accesskey="4"
                                                 styleClass="botaoSecundario texto-size-16"
                                                 actionListener="#{PerfilAcessoControle.autorizacao}">
                                    <h:outputText styleClass="fa-icon-list texto-cor-cinza texto-size-14" />
                                    <!-- Entidade.PERFIL ACESSO -->
                                    <f:attribute name="entidade" value="120"/>
                                    <!-- operacao.CONSULTAR -->
                                    <f:attribute name="operacao" value="C"/>
                                </a4j:commandLink>


                            </c:if>
                    </h:panelGroup>
                </h:panelGrid>
            </h:panelGrid>
        </h:form>
    </h:panelGrid>
    <%--MODAIS--%>
    <rich:modalPanel id="panelConsultaPerfilAcesso" styleClass="novaModal" autosized="true" shadowOpacity="true" width="450" height="250">
       <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="#{msg_aplic.prt_PerfilAcesso_tituloForm}"></h:outputText>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText
                        styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                        id="hidelinkServico"/>
                <rich:componentControl for="panelConsultaPerfilAcesso" attachTo="hidelinkServico" operation="hide"
                                       event="onclick"/>
            </h:panelGroup>
        </f:facet>

        <a4j:form id="formConsultaPerfilAcesso" ajaxSubmit="true">
            <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                <h:panelGrid columns="2" footerClass="colunaCentralizada" width="100%" styleClass="font-size-Em-max">
                    <h:outputText styleClass="rotuloCampos" style="display: block" value="#{msg_aplic.prt_PerfilAcesso_tituloForm}"/>
                    <h:panelGroup/>
                    <h:inputText id="valorConsultaServico" size="50" onblur="blurinput(this);"
                                 onfocus="focusinput(this);"
                                 styleClass="inputTextClean" value="#{PerfilAcessoControle.controleConsulta.valorConsulta}"/>

                    <c:if test="${modulo eq 'zillyonWeb'}">
                            <a4j:commandLink id="btnConsultarServico" reRender="formConsultaPerfilAcesso"
                                           action="#{PerfilAcessoControle.consultarPerfilCopiar}" styleClass="botaoPrimario texto-size-16"
                                           value="#{msg_bt.btn_consultar}"
                                           title="#{msg_bt.btn_consultar}"/>
                    </c:if>
                    <c:if test="${modulo eq 'centralEventos'}">
                            <a4j:commandLink id="btnConsultarServico" reRender="formConsultaPerfilAcesso"
                                           action="#{PerfilAcessoControle.consultarPerfilCopiar}" styleClass="botaoPrimario texto-size-16"
                                           value="#{msg_bt.btn_consultar}"
                                           title="#{msg_bt.btn_consultar}"/>
                    </c:if>
                </h:panelGrid>
                <rich:dataTable id="resultadoConsultaPerfilAcesso" width="100%" styleClass="tabelaSimplesCustom"  rendered="#{not empty PerfilAcessoControle.perfis}" value="#{PerfilAcessoControle.perfis}" rows="5"
                                var="perfil">
                    <rich:column styleClass="col-text-align-left" headerClass="col-text-align-left">
                        <f:facet name="header">
                            <h:outputText styleClass="rotuloCampos" value="#{CElabels['entidade.nome']}"/>
                        </f:facet>
                        <h:panelGroup>
                            <h:outputText styleClass="texto-font texto-size-16 texto-cor-cinza" value="#{perfil.nome}"/>
                        </h:panelGroup>
                    </rich:column>
                    <rich:column styleClass="col-text-align-right" headerClass="col-text-align-center">
                        <f:facet name="header">
                            <h:outputText styleClass="rotuloCampos"  value="#{CElabels['operacoes.opcoes']}"/>
                        </f:facet>
                            <a4j:commandLink  action="#{PerfilAcessoControle.selecionarPerfilCopiar}"
                                           reRender="containerPerfilAcesso"
                                              styleClass="linkPadrao"
                                           oncomplete="Richfaces.hideModalPanel('panelConsultaPerfilAcesso')"
                                           value="#{CElabels['operacoes.selecionar']}"
                                           title="#{CElabels['operacoes.selecionar.selecionarDado']}">
                                <h:outputText styleClass="fa-icon-arrow-right texto-font texto-size-16 texto-cor-azul"/>
                            </a4j:commandLink>
                    </rich:column>
                </rich:dataTable>
                <rich:datascroller align="center" rendered="#{not empty PerfilAcessoControle.perfis}"  for="formConsultaPerfilAcesso:resultadoConsultaPerfilAcesso"
                                   styleClass="scrollPureCustom"
                                   maxPages="10" id="scresultadoConsultaPerfilAcesso"/>
                <h:panelGrid id="mensagemConsultaPerfilAcesso" columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid columns="1" width="100%">
                        <h:outputText styleClass="mensagem" value="#{PerfilAcessoControle.mensagem}"/>
                        <h:outputText styleClass="mensagemDetalhada" value="#{PerfilAcessoControle.mensagemDetalhada}"/>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
        </a4j:form>
    </rich:modalPanel>

    <%@include file="includes/include_modal_mensagem_generica.jsp" %>
    <%@include file="includes/autorizacao/include_autorizacao_funcionalidade.jsp" %>
</f:view>
<script>
    document.getElementById("form:nome").focus();
    function adicionarOperacao(entidade, operacao) {

        var operacaoConcat = document.getElementById('form:operacaoConcat');
        var opEntidade = entidade + operacao;
        // verificar se ainda não foi selecionada
        if (operacaoConcat.value.indexOf(opEntidade) < 0) {
            operacaoConcat.value = operacaoConcat.value + opEntidade;
        } else {
            operacaoConcat.value = operacaoConcat.value.replace(opEntidade, '');
        }
    }
    jQuery(document).ready(function(){
        montarTips();
    });
    function montarTips() {
        jQuery('.tooltipster').tooltipster({
            theme: 'tooltipster-light',
            position: 'bottom',
            animation: 'grow',
            contentAsHTML: true
        });
    }
    var textoBuscar = '';
    var emBusca = false;
    function  buscarPermissao() {
        textoBuscar = jQuery('.inputBuscaPerfil').val();
       if(textoBuscar == 'Pesquisar'){
           return;
       }
        emBusca = false;
        if(!emBusca){
          buscarComParametro();
        }
        emBusca = true;
    }
    function buscarComParametro() {
      filtrarPermissoes(textoBuscar);
    }
    function filtrarPermissoes(filtro) {

        jQuery('.rich-stglpanel').each(function (e) {
            var algumSelecionadoPanel = false;
            jQuery(this).find('.tabelaSimplesCustom').each(function (el) {
                var algumSelecionadoTipo = false;
                jQuery(this).find('tbody tr td:nth-child(2)').each(function (ei) {
                    if (jQuery(this).find('span').text().toUpperCase().indexOf(filtro.toUpperCase()) < 0) {
                        jQuery(this).parent('tr').addClass('hidden');
                    } else {
                        jQuery(this).parent('tr').removeClass('hidden');
                        algumSelecionadoPanel = true;
                        algumSelecionadoTipo = true;
                    }
                });
                if(algumSelecionadoTipo){
                    jQuery(this).removeClass('hidden');
                    jQuery(this).parent().find('.headerEntidade').removeClass('hidden');
                } else {
                    jQuery(this).addClass('hidden');
                    jQuery(this).parent().find('.headerEntidade').addClass('hidden');
                }
            });
            if(!algumSelecionadoPanel) {
                jQuery(this).addClass('hidden');
            }else{
                jQuery(this).removeClass('hidden');
            }

      });
        emBusca = false;
    }

</script>

<script src="beta/js/dt-server.js" type="text/javascript"></script>

<script>
    function recarregarTabelaUsuarioPerfil() {
        var situacao = document.getElementById("form:form:situacao").value;
        var codigoPerfilAcesso = ${PerfilAcessoControle.perfilAcessoVO.codigo};
        tabelaAtual.dataTable().fnDestroy(0);
        iniTblServer("tabelaUsuarioPerfilAcesso",
            "${contexto}/prest/arquitetura/usuarioPerfilAcesso?codigoPerfilAcesso="+codigoPerfilAcesso+"&situacao="+situacao,
            null, 2, "asc", "true");
    }

    jQuery(window).on("load", function () {
        recarregarTabelaUsuarioPerfil();
    });
</script>

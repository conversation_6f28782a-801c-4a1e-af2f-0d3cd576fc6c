<%@include file="includes/imports.jsp" %>


<rich:modalPanel id="modalconfigs" styleClass="novaModal noMargin" shadowOpacity="true"
                 width="650" top="55" autosized="true">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="Configurações"/>
        </h:panelGroup>
    </f:facet>
    <f:facet name="controls">
        <h:panelGroup>
            <h:outputText
                    styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign "
                    id="hidelinkmodalconfig"/>
            <rich:componentControl for="modalconfigs" attachTo="hidelinkmodalconfig" operation="hide"
                                   event="onclick"/>
        </h:panelGroup>
    </f:facet>
    <a4j:form ajaxSubmit="true" id="modalconfigform" style="padding: 20px">

        <h:panelGrid width="100%" columns="2" columnClasses="w20, w80" cellpadding="10" style="margin-top: 20px">
            <h:outputText styleClass="texto-size-14 cinza" value="Limite diário: "></h:outputText>
            <h:inputText value="#{MensagemBuilderControle.limite}"
                         onblur="blurinput(this);" style="width: 50%;"
                         onfocus="focusinput(this);" styleClass="form"/>
        </h:panelGrid>


        <h:panelGroup layout="block" styleClass="container-botoes">
            <a4j:commandLink action="#{MensagemBuilderControle.gravarConfig}"
                             oncomplete="#{MensagemBuilderControle.msgAlert}"
                             style="margin-right: 10px"
                             reRender="mensagembuilder"
                             styleClass="botaoPrimario texto-size-14-real">
                <i class="fa-icon-save"></i>
                <h:outputText value="Gravar configurações"/>
            </a4j:commandLink>


        </h:panelGroup>

    </a4j:form>
</rich:modalPanel>

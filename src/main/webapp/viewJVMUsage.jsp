<%@ page import="java.lang.management.*" %>
<%@ page import="java.util.*" %>

<title>JVM Memory Monitor</title>
<%
            Iterator<MemoryPoolMXBean> iterator = ManagementFactory.getMemoryPoolMXBeans().iterator();
            while (iterator.hasNext()) {
                MemoryPoolMXBean item = (MemoryPoolMXBean) iterator.next();
%>

<table border="0" width="100%">
    <tbody><tr>
            <td colspan="2" align="center">
                <h3>Memory MXBean</h3>
            </td>
        </tr>
        <tr>
            <td width="200">Heap Memory Usage</td>
            <td>
                <%= ManagementFactory.getMemoryMXBean().getHeapMemoryUsage()%>
            </td>
        </tr>
        <tr>
            <td>Non-Heap Memory Usage</td>
            <td>
                <%= ManagementFactory.getMemoryMXBean().getNonHeapMemoryUsage()%>
            </td>
        </tr>
        <% }%>
        <tr>
            <td colspan="2"> </td>
        </tr>
        <tr>
            <td colspan="2" align="center">
                <h3>Memory Pool MXBeans</h3>
            </td>
        </tr>
        <%
                    Iterator<MemoryPoolMXBean> iter1 = ManagementFactory.getMemoryPoolMXBeans().iterator();
                    while (iter1.hasNext()) {
                        MemoryPoolMXBean item = (MemoryPoolMXBean) iter1.next();
        %>
        <tr>
            <td colspan="2">
                <table style="border: 1px solid rgb(152, 170, 177);" border="0" width="100%">
                    <tbody><tr>
                            <td colspan="2" align="center">
                                <b><%= item.getName()%></b>
                            </td>
                        </tr>
                        <tr>
                            <td width="200">Type</td>
                            <td>
                                <%= item.getType()%>
                            </td>
                        </tr>
                        <tr>
                            <td>Usage</td>
                            <td><%= item.getUsage()%></td>
                        </tr>
                        <tr>
                            <td>Peak Usage</td>
                            <td><%= item.getPeakUsage()%></td>
                        </tr>
                        <tr>
                            <td>Collection Usage</td>
                            <td><%= item.getCollectionUsage()%></td>
                        </tr>
                    </tbody></table>
            </td>
        </tr>
        <tr>
            <td colspan="2"> </td>
        </tr>
        <% }%>
    </tbody></table>
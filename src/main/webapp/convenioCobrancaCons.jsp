<%@page contentType="text/html;charset=UTF-8" %>
<head><%@include file="/includes/include_import_minifiles.jsp" %></head>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<script type="text/javascript" language="javascript" src="hoverform.js"></script>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax" %>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich" %>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core" %>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<meta name="viewport" content="width=device-width, initial-scale=1.0">
<link href="beta/css/pure-min.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/bootstrap-tabs.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/pure-ext.css" type="text/css" rel="stylesheet"/>
<script src="beta/js/jquery.js" type="text/javascript"></script>
<script src="beta/js/jquery.dataTables.min.js" type="text/javascript"></script>
<script src="beta/js/DT_bootstrap.js" type="text/javascript"></script>
<script src="beta/js/bootstrap-tab.js" type="text/javascript"></script>
<script>
    jQuery.noConflict();
</script>

<%@include file="/includes/verificaModulo.jsp" %>
<c:set var="contexto" value="${pageContext.request.contextPath}" scope="request"/>

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title>${msg_aplic.prt_ConvenioCobranca_tituloForm}</title>

    <%-- INICIO HEADER --%>
    <c:set var="titulo" scope="session" value="${msg_aplic.prt_ConvenioCobranca_tituloForm}"/>
    <c:set var="urlWiki" scope="session"
           value="${SuperControle.urlBaseConhecimento}convenios-de-cobranca-homologados-com-o-sistema-pacto/"/>
    <f:facet name="header">
        <jsp:include page="topoReduzido_material.jsp"/>
    </f:facet>
    <%-- FIM HEADER --%>

    <h:panelGroup layout="block" styleClass="pure-g-r">
        <h:form id="form" styleClass="pure-form pure-u-1">
            <a4j:keepAlive beanName="ExportadorListaControle"/>
            <input id="chavePrimaria" type="hidden" value="" name="chavePrimaria"/>

            <%-- INICIO CONTENT --%>
            <h:panelGroup>

                <%-- INICIO COMANDOS --%>
                <h:panelGroup layout="block" styleClass="pure-g-r pure-u-12-12 margin-0-auto titulo-topo">
                    <h:panelGroup layout="block" styleClass="pure-form pure-form-aligned pure-u-1-3"></h:panelGroup>
                    <h:panelGroup layout="block" styleClass="pure-u-2-3 text-right">
                        <h:panelGroup layout="block" style="line-height: 44px;">

                            <h:outputText styleClass="texto-font texto-size-13 texto-cor-cinza" value="Tipo: "/>
                            <h:panelGroup layout="block" styleClass="cb-container" style="margin-right: 5px;">
                                <h:selectOneMenu id="tipoConvenio" styleClass="exportadores" style="margin-right: 5px; padding-right: 10px;"
                                                 value="#{ConvenioCobrancaControle.situacaoFiltroTipo}">
                                    <f:selectItems value="#{ConvenioCobrancaControle.listaSelectItemTipoFiltro}"/>
                                    <a4j:support event="onchange" oncomplete="recarregarTabelaConvenios()"/>
                                </h:selectOneMenu>
                            </h:panelGroup>


                            <h:outputText styleClass="texto-font texto-size-13 texto-cor-cinza" value="Situação: "/>
                            <h:panelGroup layout="block" styleClass="cb-container" style="margin-right: 10px;">
                                <h:selectOneMenu id="situacao" styleClass="exportadores"
                                                 value="#{ConvenioCobrancaControle.situacaoFiltro}">
                                    <f:selectItems value="#{ConvenioCobrancaControle.listaSelectItemSituacao}"/>
                                    <a4j:support event="onchange" oncomplete="recarregarTabelaConvenios()"/>
                                </h:selectOneMenu>
                            </h:panelGroup>

                            <a4j:commandLink id="btnNovo"
                                             styleClass="pure-button pure-button-primary "
                                             action="#{ConvenioCobrancaControle.novo}"
                                             style="margin-right: 10px;"
                                             accesskey="1">
                                <i class="fa-icon-plus"></i> &nbsp ${msg_bt.btn_cadastrar_novo}
                            </a4j:commandLink>
                            <div style="margin-top: -3px;">
                            <a4j:commandLink id="btnExcel"
                                             styleClass="linkPadrao"
                                             actionListener="#{ConvenioCobrancaControle.exportar}"
                                             oncomplete="abrirPopup('UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/vnd.ms-excel','Transacoes', 800,200);#{ExportadorListaControle.msgAlert}"
                                             accesskey="3">
                                <f:attribute name="tipo" value="xls"/>
                                <f:attribute name="atributos"
                                             value="codigo=Código,descricao=Descrição,empresa_Apresentar=Empresa,banco_Apresentar=Banco,tipo_Apresentar=Tipo"/>
                                <f:attribute name="prefixo" value="ConvenioCobranca"/>
                                <a4j:actionparam noEscape="true" name="paramsTableFiltrada" value="retornaDadosDT()"/>
                                <h:outputText title="Exportar para o formato Excel" styleClass="btn-print-2 excel"/>
                            </a4j:commandLink>

                            <a4j:commandLink id="btnPDF"
                                             styleClass="linkPadrao"
                                             actionListener="#{ConvenioCobrancaControle.exportar}"
                                             style="margin-left: 10px;"
                                             oncomplete="abrirPopup('UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/pdf','Transacoes', 800,200);#{ExportadorListaControle.msgAlert}"
                                             accesskey="4">
                                <f:attribute name="tipo" value="pdf"/>
                                <f:attribute name="atributos"
                                             value="codigo=Código,descricao=Descrição,empresa_Apresentar=Empresa,banco_Apresentar=Banco,tipo_Apresentar=Tipo"/>
                                <f:attribute name="prefixo" value="ConvenioCobranca"/>
                                <a4j:actionparam noEscape="true" name="paramsTableFiltrada" value="retornaDadosDT()"/>
                                <h:outputText title="Exportar para o formato PDF" styleClass="btn-print-2 pdf"/>
                            </a4j:commandLink>
                            <a4j:commandLink id="btnLog"
                                                 styleClass="exportadores margin-h-10"
                                                 action="#{ConvenioCobrancaControle.realizarConsultaLogObjetoGeral}"
                                                        oncomplete="abrirPopup('visualizadorLogForm.jsp', 'VisualizadorLog', 785, 595);">
                                    <h:outputText title="visualizar log geral da entidade" styleClass="btn-print-2 log"/>
                            </a4j:commandLink>
                            </div>
                        </h:panelGroup>
                    </h:panelGroup>
                    <%-- FIM COMANDOS --%>
                </h:panelGroup>
                <table id="tblConvenioCobranca" class="tabelaConvenioCobranca pure-g-r pure-u-11-12 margin-0-auto">
                    <thead>
                    <th onclick="ordenarLista()">${msg_aplic.prt_Cadastro_label_codigo_maiusculo}</th>
                    <th onclick="ordenarLista()">${msg_aplic.prt_Cadastro_label_descricao_maiusculo}</th>
                    <th>${msg_aplic.prt_Cadastro_label_empresa_maiusculo}</th>
                    <th onclick="ordenarLista()">${msg_aplic.prt_Cadastro_label_banco_maiusculo}</th>
                    <th onclick="ordenarLista()">${msg_aplic.prt_Cadastro_label_tipo_maiusculo}</th>
                    <th onclick="ordenarLista()">AMBIENTE</th>
                    <th onclick="ordenarLista()">COBRANÇA AUTOMÁTICA</th>
                    <th onclick="ordenarLista()">${msg_aplic.prt_Cadastro_label_situacao_maiusculo}</th>
                    </thead>
                    <tbody></tbody>
                </table>

                <a4j:jsFunction name="jsEditar" action="#{ConvenioCobrancaControle.editar}" reRender="mensagem"/>

            </h:panelGroup>
            <%-- FIM CONTENT --%>

            <%-- INICIO FOOTER --%>
            <h:panelGroup id="mensagem" layout="block" styleClass="pure-u-11-12 margin-0-auto">
                <h:graphicImage id="iconSucesso" rendered="#{ConvenioCobrancaControle.sucesso}"
                                value="./imagens/sucesso.png"/>
                <h:graphicImage id="iconErro" rendered="#{ConvenioCobrancaControle.erro}" value="./imagens/erro.png"/>

                <h:outputText styleClass="mensagem" rendered="#{not empty ConvenioCobrancaControle.mensagem}"
                              value=" #{ConvenioCobrancaControle.mensagem}"/>
                <h:outputText styleClass="mensagemDetalhada"
                              rendered="#{not empty ConvenioCobrancaControle.mensagemDetalhada}"
                              value=" #{ConvenioCobrancaControle.mensagemDetalhada}"/>
            </h:panelGroup>
            <%-- FIM FOOTER --%>
        </h:form>


    </h:panelGroup>
</f:view>
<script src="beta/js/dt-server.js" type="text/javascript"></script>

<script>
    jQuery(window).on("load", function () {
        iniTblServer("tabelaConvenioCobranca", "${contexto}/prest/financeiro/convenioCobranca?situacao=${ConvenioCobrancaControle.situacaoFiltro}"+"&tipo=${ConvenioCobrancaControle.situacaoFiltroTipo}", null, 1, "asc", "true");
    });

    function recarregarTabelaConvenios() {
        var situacao = document.getElementById("form:situacao").value;
        var tipoConvenio = document.getElementById("form:tipoConvenio").value;
        tabelaAtual.dataTable().fnDestroy(0);
        iniTblServer("tabelaConvenioCobranca", "${contexto}/prest/financeiro/convenioCobranca?situacao="+situacao+"&tipo="+tipoConvenio, null, 1, "asc", "true");
    }

    function ordenarLista() {
        var elementoOrdenar = document.querySelectorAll('[class*="sorting_"]')
        var tipoOrdenacao = elementoOrdenar[0].classList[elementoOrdenar[0].classList.length - 1]
        var nomeOrdenacao = elementoOrdenar[0].innerText
        var elementoClicado = event.currentTarget
        var tipoClicado = elementoClicado.innerText
        var sentidoOrdenacao = "asc"
        var campoOrdenacao = "descricao"
        var numeroColunaOrdenar = 1;

        debugger

        if (nomeOrdenacao === tipoClicado && tipoOrdenacao !== undefined) {
            if (tipoOrdenacao === "sorting_asc") {
                sentidoOrdenacao = "desc"
            } else if (tipoOrdenacao === "sorting_desc") {
                sentidoOrdenacao = "asc"
            }
        }

        //Passar aqui a coluna que deve ordenar no Banco de Dados
        switch (tipoClicado) {
            case "COD":
                campoOrdenacao = "codigo"
                numeroColunaOrdenar = 0
                break
            case "DESCRIÇÃO":
                campoOrdenacao = "descricao"
                numeroColunaOrdenar = 1
                break
            case "BANCO":
                campoOrdenacao = "banco"
                numeroColunaOrdenar = 3
                break
            case "TIPO":
                campoOrdenacao = "tipoconvenio"
                numeroColunaOrdenar = 4
                break
            case "AMBIENTE":
                campoOrdenacao = "ambiente"
                numeroColunaOrdenar = 5
                break
            case "COBRANÇA AUTOMÁTICA":
                campoOrdenacao = "bloquearcobrancaautomatica"
                numeroColunaOrdenar = 6
                break
            case "SITUAÇÃO":
                campoOrdenacao = "situacao"
                numeroColunaOrdenar = 7
                break
        }

        var situacao = document.getElementById("form:situacao").value;
        var tipoConvenio = document.getElementById("form:tipoConvenio").value;
        tabelaAtual.dataTable().fnDestroy(0);
        iniTblServer(
            "tabelaConvenioCobranca",
            "${contexto}/prest/financeiro/convenioCobranca?situacao="+situacao+"&tipo="+tipoConvenio+"&ordenar="+campoOrdenacao+"&sentidoOrdenacao="+sentidoOrdenacao,
            null,
            numeroColunaOrdenar,
            sentidoOrdenacao,
            "true"
        );
    }
</script>

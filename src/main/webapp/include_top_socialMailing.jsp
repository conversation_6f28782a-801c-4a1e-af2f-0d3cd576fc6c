									<li>
                                        <h:panelGrid cellpadding="0" cellspacing="0" columns="2" id="nrMsgLidas">
                                            
                                                    <a4j:commandLink action="#{LoginControle.abrirSocialMailing}" reRender="nrMsgLidas"
                                                             oncomplete="#{LoginControle.msgAlert}">
                                                <%--h:graphicImage url="/images/socialmail/novamsg.png"
                                                                style="border: 0px;"
                                                                title="Social Mailing"
                                                                ></h:graphicImage--%>
                                                <i class="fa-icon-comment fa-icon-2x"></i>
                                            </a4j:commandLink>
                                                <h:panelGroup id="nrlida" rendered="#{UsuarioControle.usuario.nrMensagensNaoLidas > 0}"
                                                              layout="block">
                                                    <div class="notificacaoAtividades">
                                                    <h:outputText  value="#{UsuarioControle.usuario.nrMensagensNaoLidas}"></h:outputText>
                                                   </div>
                                            </h:panelGroup>
                                        </h:panelGrid>
									</li>
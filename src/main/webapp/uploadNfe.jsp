<%@page contentType="text/html;charset=UTF-8" %>
<head>
    <%@include file="/includes/include_import_minifiles.jsp" %>
</head>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<link href="beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<script type="text/javascript" language="javascript" src="hoverform.js"></script>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax" %>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich" %>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core" %>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<f:view>

    <title>Cadastro de produtos via xml NFE</title>

    <c:set var="titulo" scope="session" value="Cadastrar Produtos via xml NFE"/>
    <f:facet name="header">
        <jsp:include page="topoReduzido_material.jsp"/>
    </f:facet>

    <h:panelGroup layout="block" styleClass="pure-g-r">
        <h:form id="formUploadNfe">
            <h:panelGroup layout="block" style="display: flex; justify-content: center; margin-top: 20px;">
                <rich:fileUpload
                        id="uploadArquivoXml"
                        fileUploadListener="#{ProdutoControle.uploadNfe}"
                        immediateUpload="true"
                        maxFilesQuantity="1"
                        allowFlash="false"
                        acceptedTypes="xml"
                        addControlLabel="Importar XML NFE"
                        uploadControlLabel="Enviar"
                        clearControlLabel="Limpar"
                        listHeight="50px"
                        listWidth="400px">
                    <a4j:support event="onuploadcomplete"
                                 oncomplete="#{ProdutoControle.msgAlert}"
                                 reRender="uploadArquivoXml, tbItensParaCadastroOuVinculo"/>
                </rich:fileUpload>
            </h:panelGroup>

            <h:panelGroup layout="block" style="text-align: center; margin-top: 20px;">
            <a4j:commandButton value="#{msg_bt.btn_voltar_lista}"
                               action="#{ProdutoControle.voltarParaTelaAnterior}"
                               styleClass="pure-button pure-button-secondary"
                               style="margin-top: 15px;"/>
        </h:panelGroup>
    </h:form>

    <h:panelGroup id="mensagem" layout="block" styleClass="pure-u-11-12 margin-0-auto">
        <h:graphicImage id="iconSucesso" rendered="#{ProdutoControle.sucesso}"
                        value="./imagens/sucesso.png"/>
        <h:graphicImage id="iconErro" rendered="#{ProdutoControle.erro}" value="./imagens/erro.png"/>

        <h:outputText styleClass="mensagem" rendered="#{not empty ProdutoControle.mensagem}"
                      value=" #{ProdutoControle.mensagem}"/>
        <h:outputText styleClass="mensagemDetalhada"
                      rendered="#{not empty ProdutoControle.mensagemDetalhada}"
                      value=" #{ProdutoControle.mensagemDetalhada}"/>
    </h:panelGroup>
    </h:panelGroup>

    <rich:modalPanel id="modalProdutosParaCadastroOuVinculo"
                     style="overflow: auto; max-height: 600px;"
                     shadowOpacity="true" width="700" height="600"
                     onshow="chamarMontarListasProdutosCategoria()">

        <a4j:jsFunction name="chamarMontarListasProdutosCategoria"
                        action="#{ProdutoControle.montarListaSelectItemProdutoSemCodigoDeBarras}"
                        oncomplete="chamarMontarListaCategoria()"
                        ajaxSingle="true" reRender="selectProdutoEstoque" />

        <a4j:jsFunction name="chamarMontarListaCategoria"
                        action="#{ProdutoControle.montarListaSelectItemCategoriaProduto}"
                        ajaxSingle="true" reRender="selectCategoriaNovosProdutosNfe" />

        <f:facet name="header">
            <h:outputText value="Produtos"
                          styleClass="centralizar-titulo-header" />
        </f:facet>

        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                              id="hidemodalProdutosParaCadastroOuVinculo"/>
                <rich:componentControl for="modalProdutosParaCadastroOuVinculo"
                                       attachTo="hidemodalProdutosParaCadastroOuVinculo"
                                       operation="hide" event="onclick"/>
            </h:panelGroup>
        </f:facet>

        <a4j:form id="formModalXML" ajaxSubmit="true" >
            <h:panelGrid columns="1" width="100%" styleClass="tabFormSubordinada">
                <h:panelGroup style="overflow: auto; max-height: 300px; width: 100%;" layout="block">
                    <h:dataTable id="tbItensParaCadastroOuVinculo" width="100%" headerClass="subordinado"
                                 rowClasses="linhaImpar, linhaPar" columnClasses="esquerda, centralizado, direita,direita,direita,centralizado"
                                 value="#{ProdutoControle.produtosParaCadastrarOuVincular}" var="item">

                        <h:column>
                            <f:facet name="header">
                                <h:outputText value="Produto para cadastro ou vinculo" />
                            </f:facet>
                            <h:outputText value="#{item.produto.descricao}" />
                        </h:column>

                        <h:column>
                            <f:facet name="header">
                                <h:outputText value="Vincular ao estoque" />
                            </f:facet>
                            <h:selectOneMenu id="selectProdutoEstoque" styleClass="form"
                                             style="vertical-align: middle;"
                                             value="#{item.produtoParaVincularNoEstoque}">
                                <f:selectItems value="#{ProdutoControle.listaSelectItemProduto}" />
                            </h:selectOneMenu>
                        </h:column>

                        <h:column>
                            <f:facet name="header">
                                <h:outputText value="Cadastrar como novo" />
                            </f:facet>
                            <h:selectBooleanCheckbox value="#{item.cadastrarNovo}" immediate="true">
                                <a4j:support event="change" reRender="modalProdutosParaCadastroOuVinculo" />
                            </h:selectBooleanCheckbox>

                            <a4j:commandButton id="updateButton"
                                               actionListener="#{CompraControle.atualizarCheckbox}"
                                               styleClass="hidden"
                                               reRender="modalProdutosParaCadastroOuVinculo"/>

                        </h:column>

                        <h:column>
                            <f:facet name="header">
                                <h:outputText value="Valor de venda" />
                            </f:facet>
                            <h:inputText id="valorDeVenda" style="width: 90px;" maxlength="40" onfocus="focusinput(this);"
                                         onkeypress="return(currencyFormat(this,'.',',',event));"
                                         styleClass="form" value="#{item.valorFinal}">
                                <f:converter converterId="FormatadorNumerico" />
                            </h:inputText>
                        </h:column>

                    </h:dataTable>
                </h:panelGroup>
            </h:panelGrid>

            <h:panelGrid id="selectCategoriaProduto" columns="2" rowClasses="linhaImpar, linhaPar"
                         columnClasses="classEsquerda, classDireita" width="100%"
                         headerClass="subordinado">

                <f:facet name="header">
                    <h:outputText value="Categoria"/>
                </f:facet>

                <h:outputText value="Categoria:"/>
                <h:panelGroup layout="block">
                    <h:selectOneMenu id="categoria" onblur="blurinput(this);"
                                     onfocus="focusinput(this);"
                                     styleClass="form"
                                     value="#{ProdutoControle.categoriaParaNovosProdutosNfe}">
                        <f:selectItems value="#{ProdutoControle.listaSelectItemCategoriaProduto}"/>
                    </h:selectOneMenu>

                </h:panelGroup>
            </h:panelGrid>

            <h:panelGrid id="enviarEstoqueMinimo" columns="2" rowClasses="linhaImpar, linhaPar"
                         columnClasses="classEsquerda, classDireita" width="100%"
                         headerClass="subordinado">

                <f:facet name="header">
                    <h:outputText value="Estoque mínimo"/>
                </f:facet>

                <h:outputText value="Estoque mínimo:"/>
                <h:panelGroup layout="block">
                    <h:inputText id="estoqueMinimo" onblur="blurinput(this);"
                                 onfocus="focusinput(this);" styleClass="form"
                                 size="10" maxlength="50"
                                 value="#{ProdutoControle.estoqueMinimoParaNovosProdutosNfe}"/>
                </h:panelGroup>
            </h:panelGrid>

            <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada"
                         id="gridOperacoesProdutosNaoEncontradosNaNfe" style="padding-bottom: 20px;">
                <h:panelGroup>
                    <h:commandButton id="botaoCancelarProdutosNaoEncontradosNaNfe" immediate="true"
                                     onclick="#{rich:component('modalProdutosParaCadastroOuVinculo')}.hide();"
                                     value="Cancelar" alt="Cancelar"
                                     styleClass="botoes nvoBt btSec"/>

                    <a4j:commandButton id="botaoSalvarProdutosNaoEncontradosNaNfe"
                                       action="#{ProdutoControle.salvarProdutosNaoEncontradosNaNfe}"
                                       oncomplete="#{ProdutoControle.mensagemNotificar}; #{ProdutoControle.produtosParaCadastrarOuVincular.isEmpty()} && Richfaces.hideModalPanel('modalProdutosParaCadastroOuVinculo');"
                                       value="Salvar produtos"
                                       title="Salvar produtos"
                                       reRender="tbItensParaCadastroOuVinculo"
                                       styleClass="botoes nvoBt"/>
                </h:panelGroup>
            </h:panelGrid>

        </a4j:form>
    </rich:modalPanel>
</f:view>
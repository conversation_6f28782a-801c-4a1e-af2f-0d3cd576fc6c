<%@page contentType="text/html" %>
<%@page pageEncoding="UTF-8" %>
<head>
    <%@include file="/includes/include_import_minifiles.jsp" %>
</head>
<link href="./css/pacto_flat_2.16.min.css" rel="stylesheet" type="text/css">
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<link href="beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/pure-buttons.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/pure-ext.css" type="text/css" rel="stylesheet"/>
<script type="text/javascript" language="javascript" src="hoverform.js"></script>
<script type="text/javascript" language="javascript">
     setTimeout(setInterval(function() { getDocumentCookie('popupsImportante') == 'close' ? this.close() : ''; }, 500), 500);
</script>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core" %>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html" %>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax" %>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich" %>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText value="Transferência De Crédito"/>
    </title>

    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <c:set var="titulo" scope="session" value="Transferência Saldo Conta Corrente"/>
        <c:set var="urlWiki" scope="session"
               value="${SuperControle.urlWiki}Inicial:Informações_do_Cliente:Saldo_Conta_Corrente"/>
        <f:facet name="header">
            <jsp:include page="topoReduzido_material.jsp"/>
        </f:facet>
        <div styleClass="col-md-12">
            <hr class="dividerFundoClaro"/>
        </div>
    </h:panelGrid>
    <rich:modalPanel id="panelCliente" autosized="true" shadowOpacity="true" width="550" height="300"
                     styleClass="novaModal" onshow="document.getElementById('formCliente:consultarCliente').focus();">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="#{msg_aplic.prt_TransferenciaContaCorrenteCliente_consultarCliente}"/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:graphicImage value="/imagens/close.png" style="cursor:pointer" id="hiperlinkCliente"/>
                <rich:componentControl for="panelCliente" attachTo="hiperlinkCliente" operation="hide" event="onclick"/>
            </h:panelGroup>
        </f:facet>
        <a4j:form id="formCliente" ajaxSubmit="true">
            <h:panelGrid columns="1" width="100%">
                <h:panelGrid columns="4" width="100%" styleClass="font-size-em-max">
                    <h:outputText styleClass="texto-size-14 texto-cor-cinza texto-font texto-bold tituloCaixaAlta"
                                  value="#{msg.msg_consultar_por}"/>
                    <h:panelGroup>
                        <div class="cb-container margenVertical">
                            <h:selectOneMenu styleClass="campos" id="consultarCliente"
                                             value="#{MovimentoContaCorrenteClienteControle.campoConsultaCliente}">
                                <f:selectItems
                                        value="#{MovimentoContaCorrenteClienteControle.tipoConsultarComboCliente}"/>
                            </h:selectOneMenu>
                        </div>
                    </h:panelGroup>
                    <h:inputText id="valorConsultarCliente" styleClass="campos"
                                 value="#{MovimentoContaCorrenteClienteControle.valorConsultarCliente}"/>
                    <a4j:commandLink id="btnConsultarCliente"
                                     reRender="formCliente:mensagemConsultarCliente, formCliente:resultadoConsultaCliente , formClientescResultadoCliente , formCliente"
                                     action="#{MovimentoContaCorrenteClienteControle.consultarCliente}"
                                     styleClass="botaoPrimario texto-size-14-real "
                                     value="#{msg_bt.btn_consultar}"/>
                </h:panelGrid>
                <rich:dataTable id="resultadoConsultaCliente" width="100%" headerClass="consulta"
                                rowClasses="linhaImpar, linhaPar" columnClasses="colunaAlinhamento"
                                value="#{MovimentoContaCorrenteClienteControle.listaConsultarCliente}" rows="6"
                                var="cliente">
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_Cliente_codigo}"/>
                        </f:facet>
                        <h:outputText value="#{cliente.codigo}"/>
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_Cliente_pessoa}"/>
                        </f:facet>
                        <h:outputText value="#{cliente.pessoa.nome}"/>
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_Cliente_matricula}"/>
                        </f:facet>
                        <h:outputText value="#{cliente.matricula}"/>
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_bt.btn_opcoes}"/>
                        </f:facet>
                        <a4j:commandButton id="linkResultadoBuscaCliente"
                                           action="#{MovimentoContaCorrenteClienteControle.selecionarCliente}"
                                           focus="cliente" reRender="form:panelGeral, form, mensagem"
                                           oncomplete="Richfaces.hideModalPanel('panelCliente')"
                                           value="#{msg_bt.btn_selecionar}" styleClass="botoes"
                                           image="./imagens/botaoEditar.png"/>
                    </rich:column>
                </rich:dataTable>
                <rich:datascroller align="center" for="formCliente:resultadoConsultaCliente" maxPages="10"
                                   id="scResultadoCliente"/>
                <h:panelGrid id="mensagemConsultaCliente" columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid columns="1" width="100%">
                        <h:outputText styleClass="mensagem" value="#{MovimentoContaCorrenteClienteControle.mensagem}"/>
                        <h:outputText styleClass="mensagemDetalhada"
                                      value="#{MovimentoContaCorrenteClienteControle.mensagemDetalhada}"/>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
        </a4j:form>
    </rich:modalPanel>


    <h:form id="form">
        <h:panelGrid columns="1" width="100%" styleClass="font-size-em-max">
            <h:panelGroup>
                <h:outputText value="NOME DO CLIENTE: "
                              styleClass="texto-size-14 texto-cor-cinza texto-font texto-bold"/>
                <h:outputText id="clienteNomeAuto" styleClass="texto-size-14-real texto-cor-cinza texto-font"
                              value="#{ClienteControle.clienteVO.pessoa.nome}"/>
                <div styleClass="col-md-12">
                    <hr class="dividerFundoClaro"/>
                </div>
            </h:panelGroup>
            <h:panelGroup>
                <h:outputText styleClass="texto-size-14 texto-cor-cinza texto-font texto-bold tituloCaixaAlta"
                              value="#{msg_aplic.prt_TransferenciaContaCorrenteCliente_valorMaximo}: "/>
                <h:outputText styleClass="texto-size-14 texto-cor-cinza texto-font " value=" #{MovimentoContaCorrenteClienteControle.empresaLogado.moeda} "/>
                <h:outputText id="valorFinal" styleClass="texto-cor-cinza texto-font texto-bold " style="color: green"
                              value="#{MovimentoContaCorrenteClienteControle.movimentoContaCorrenteClienteVO.saldoAtual}">
                    <f:converter converterId="FormatadorNumerico"/>
                </h:outputText>
                <div styleClass="col-md-12">
                    <hr class="dividerFundoClaro"/>
                </div>
            </h:panelGroup>
            <rich:spacer height="5px"/>
            <h:outputText styleClass="texto-size-14-real texto-cor-cinza-2 texto-font texto-bold"
                          value="DADOS DA TRANSFERÊNCIA"/>
            <rich:spacer height="3px"/>
            <h:panelGrid id="panelGroupCliente" columns="1" styleClass="font-size-em-max">
                <h:outputText styleClass="texto-size-14 texto-cor-cinza texto-font texto-bold tituloCaixaAlta"
                              value="#{msg_aplic.prt_TransferenciaContaCorrenteCliente_nomeCliente}"/>
                <h:panelGroup>
                    <h:inputText id="nomeCliente" size="40" readonly="true" maxlength="50" onblur="blurinput(this);"
                                 onfocus="focusinput(this);" styleClass="inputTextClean"
                                 value="#{MovimentoContaCorrenteClienteControle.movimentoContaCorrenteClienteVO.clienteTransferencia.pessoa.nome}"/>
                    <rich:spacer width="5"/>
                    <a4j:commandLink id="linkPesquisaClienteAuto"
                                     oncomplete="Richfaces.showModalPanel('panelCliente')">
                        <i class="fa-icon-search texto-size-18 linkAzul"></i>
                    </a4j:commandLink>
                    <rich:spacer width="5"/>
                    <a4j:commandLink id="limparCampoCliente" immediate="true"
                                     action="#{MovimentoContaCorrenteClienteControle.limparCampoCliente}"
                                     reRender="nomeCliente">
                        <i class="fa-icon-remove texto-size-18 linkAzul"/>
                    </a4j:commandLink>
                </h:panelGroup>
            </h:panelGrid>

            <h:panelGrid id="panelGroupValorTransferencia" columns="1" styleClass="font-size-em-max">
                <h:outputText styleClass="texto-size-14 texto-cor-cinza texto-font texto-bold tituloCaixaAlta"
                              value="#{msg_aplic.prt_TransferenciaContaCorrenteCliente_valorTransferido}"/>
                <rich:spacer width="5"/>
                <h:inputText id="valorTransferencia" size="7"
                             onkeypress="return formatar_moeda(this,'.',',',event);" maxlength="10"
                             value="#{MovimentoContaCorrenteClienteControle.movimentoContaCorrenteClienteVO.valorTransferencia}"
                             onfocus="focusinput(this);" styleClass="inputTextClean">
                    <f:converter converterId="FormatadorNumerico"/>
                </h:inputText>
            </h:panelGrid>

            <h:panelGrid id="msgTfr" columns="2" width="100%">
                <h:panelGrid columns="1" width="100%">
                    <h:commandButton rendered="#{MovimentoContaCorrenteClienteControle.erro}"
                                     image="./imagens/erro.png"/>
                </h:panelGrid>
                <h:panelGrid columns="1" width="100%">
                    <h:outputText id="msgTransfereciaVerde" styleClass="texto-size-14 texto-cor-cinza texto-font"
                                  value="#{MovimentoContaCorrenteClienteControle.mensagem}"/>
                    <h:outputText id="msgTransferenciaDeta" styleClass="texto-size-14 texto-cor-cinza texto-font"
                                  value="#{MovimentoContaCorrenteClienteControle.mensagemDetalhada}"/>
                </h:panelGrid>
            </h:panelGrid>

            <h:panelGrid id="panelBotoes" width="100%" columns="2" style="align-content: center">
                <h:panelGroup rendered="#{MovimentoContaCorrenteClienteControle.apresentarBoteos}">
                    <a4j:commandLink id="confirmar" reRender="form,panelAutorizacaoFuncionalidade,msgTfr,panelBotoes"
                                     styleClass="pure-button pure-button-primary texto-font"
                                     action="#{MovimentoContaCorrenteClienteControle.richConfirmacaoDadosTransferencia}">
                        <i class="fa-icon-ok"></i>&nbsp; Confirmar
                    </a4j:commandLink>
                </h:panelGroup>
                <h:panelGroup rendered="#{!MovimentoContaCorrenteClienteControle.apresentarBoteos}">
                    <a4j:commandLink id="fechar"
                                     onclick="fecharJanela();"
                                     value="Cancelar"
                                     styleClass="pure-button texto-font">

                    </a4j:commandLink>
                </h:panelGroup>
            </h:panelGrid>
        </h:panelGrid>
    </h:form>
    <%@include file="includes/autorizacao/include_autorizacao_funcionalidade.jsp" %>
</f:view>

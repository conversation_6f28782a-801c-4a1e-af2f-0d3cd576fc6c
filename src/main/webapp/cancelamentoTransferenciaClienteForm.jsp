<%--
    Document   : cancelamenteTransferencia
    Created on : 26/06/2009, 08:23:21
    Author     : pedro
--%>

<%@page contentType="text/html"%>
<%@page pageEncoding="UTF-8"%>
<head><%@include file="/includes/include_import_minifiles.jsp" %></head>
<link href="beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/pure-buttons.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/pure-ext.css" type="text/css" rel="stylesheet"/>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<script type="text/javascript" language="javascript" src="hoverform.js"></script>
<script type="text/javascript" language="javascript">
     setTimeout(setInterval(function() { getDocumentCookie('popupsImportante') == 'close' ? this.close() : ''; }, 500), 500);
</script>

<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>
<style type="text/css">
    .pure-button{
        font-family: sans-serif;
        font-size:100%;
        margin:0;
        vertical-align:baseline;
        *vertical-align:middle
    }
    .to-uper-case{
        text-transform: uppercase;
    }
</style>
<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <c:set var="titulo" scope="session" value="Transferência"/>
    </title>

    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <f:facet name="header">
            <jsp:include page="topoReduzido_material.jsp"/>
        </f:facet>
 </h:panelGrid>
    <rich:modalPanel id="panelCliente" styleClass="novaModal" autosized="true" shadowOpacity="true" width="700" height="250" >
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Consulta Cliente"></h:outputText>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText
                        styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade" id="hiperlinkCliente"/>
                <rich:componentControl for="panelCliente" attachTo="hiperlinkCliente" operation="hide" event="onclick"/>
            </h:panelGroup>
        </f:facet>
        <a4j:form id="formCliente" ajaxSubmit="true" styleClass="font-size-Em-max">
            <h:panelGrid columns="1" width="100%">
                <h:panelGrid columns="4" footerClass="colunaCentralizada" width="100%">
                    <h:outputText styleClass="texto-font texto-size-10-real texto-cor-cinza"
                                  value="#{msg.msg_consultar_por}"/>
                    <h:panelGroup layout="block" styleClass="cb-container">
                        <h:selectOneMenu styleClass="campos" id="consultarCliente"
                                         value="#{CancelamentoContratoControle.campoConsultarCliente}">
                            <f:selectItems value="#{CancelamentoContratoControle.tipoConsultarComboCliente}"/>
                        </h:selectOneMenu>
                    </h:panelGroup>
                    <h:inputText id="valorConsultarCliente" size="25"
                                 value="#{CancelamentoContratoControle.valorConsultarCliente}"/>
                    <a4j:commandLink id="btnConsultarCliente"
                                       reRender="formCliente:mensagemConsultarCliente, formCliente:resultadoConsultaCliente , formClientescResultadoCliente , formCliente"
                                       action="#{CancelamentoContratoControle.consultarCliente}"
                                       styleClass="botaoPrimario texto-size-14-real" value="#{msg_bt.btn_consultar}"
                                       />
                </h:panelGrid>
                <rich:dataTable id="resultadoConsultaCliente" width="100%" styleClass="tabelaSimplesCustom"
                                headerClass="consulta" rowClasses="linhaImpar, linhaPar"
                                columnClasses="colunaAlinhamento"
                                value="#{CancelamentoContratoControle.listaConsultarCliente}" rows="5" var="cliente">
                    <rich:column styleClass="col-text-align-left" headerClass="col-text-align-left">

                            <f:facet name="header">
                                <h:outputText
                                        styleClass="texto-upper texto-font texto-size-14-real texto-cor-cinza texto-bold"
                                        value="#{msg_aplic.prt_Cliente_codigo}"/>
                            </f:facet>

                            <a4j:commandLink
                                    action="#{CancelamentoContratoControle.selecionarCliente}" focus="cliente"
                                    reRender="form:panelGeral, form, mensagem"
                                    oncomplete="Richfaces.hideModalPanel('panelCliente')"
                                    styleClass="linkPadrao texto-size-14-real texto-cor-azul" value="#{cliente.codigo}"/>
                        </rich:column>
                        <rich:column>
                            <f:facet name="header">
                                <h:outputText styleClass="texto-upper texto-font texto-size-14-real texto-cor-cinza texto-bold"
                                              value="#{msg_aplic.prt_Cliente_pessoa}"/>
                            </f:facet>
                            <a4j:commandLink
                                    action="#{CancelamentoContratoControle.selecionarCliente}" focus="cliente"
                                    reRender="form:panelGeral, form, mensagem"
                                    oncomplete="Richfaces.hideModalPanel('panelCliente')"
                                    styleClass="linkPadrao texto-size-14-real texto-cor-azul" value="#{cliente.pessoa.nome}"/>
                        </rich:column>
                        <rich:column>
                            <f:facet name="header">
                                <h:outputText styleClass="texto-upper texto-font texto-size-14-real texto-cor-cinza texto-bold"
                                              value="#{msg_aplic.prt_Cliente_matricula}"/>
                            </f:facet>
                            <a4j:commandLink
                                             action="#{CancelamentoContratoControle.selecionarCliente}" focus="cliente"
                                             reRender="form:panelGeral, form, mensagem"
                                             oncomplete="Richfaces.hideModalPanel('panelCliente')"
                                             styleClass="linkPadrao texto-size-14-real texto-cor-azul" value="#{cliente.matricula}"/>
                        </rich:column>

                        <rich:column>
                            <f:facet name="header">
                                <h:outputText styleClass="texto-upper texto-font texto-size-14-real texto-cor-cinza texto-bold"
                                              value="#{msg_bt.btn_opcoes}"/>
                            </f:facet>

                            <a4j:commandLink id="linkResultadoBuscaCliente"
                                             action="#{CancelamentoContratoControle.selecionarCliente}" focus="cliente"
                                             reRender="form:panelGeral, form, mensagem"
                                             oncomplete="Richfaces.hideModalPanel('panelCliente')"
                                             styleClass="linkPadrao texto-size-14-real texto-cor-azul">
                                <span>Selecionar </span>
                                <i class="fa-icon-arrow-right"></i>
                            </a4j:commandLink>
                        </rich:column>

                </rich:dataTable>
                <rich:datascroller align="center" for="formCliente:resultadoConsultaCliente" maxPages="10"
                                   styleClass="scrollPureCustom" renderIfSinglePage="false"
                                   id="scResultadoOperador" />
                <h:panelGrid id="mensagemConsultaCliente" columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid columns="1" width="100%">
                        <h:outputText styleClass="mensagem" value="#{CancelamentoContratoControle.mensagem}"/>
                        <h:outputText styleClass="mensagemDetalhada" value="#{CancelamentoContratoControle.mensagemDetalhada}"/>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
        </a4j:form>
    </rich:modalPanel>
<%--Consultar contrato--%>
    <rich:modalPanel id="panelContrato" styleClass="novaModal" autosized="true" shadowOpacity="true" width="700" height="250">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="#{msg_aplic.prt_cancelamentoTransferenciaCliente_consultarContrato}"/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText
                styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade" id="hiperlinkContrato"/>
                <rich:componentControl for="panelContrato" attachTo="hiperlinkContrato" operation="hide" event="onclick"/>
            </h:panelGroup>
        </f:facet>
        <a4j:form id="formContrato">
            <h:panelGrid columns="1" width="100%" >
                <h:panelGrid columns="1" width="100%" rendered="#{!empty CancelamentoContratoControle.listaConsultarContrato}">
                    <rich:dataTable id="resultadoConsultaContrato" width="100%" headerClass="consulta" styleClass="tabelaSimplesCustom" rowClasses="linhaImpar, linhaPar" columnClasses="colunaAlinhamento"
                                    value="#{CancelamentoContratoControle.listaConsultarContrato}" rows="10" var="contrato" >
                        <rich:column styleClass="col-text-align-left" headerClass="col-text-align-left" >
                            <f:facet name="header">
                                <h:outputText styleClass="texto-upper texto-font texto-size-14-real texto-cor-cinza texto-bold" value="#{msg_aplic.prt_cancelamentoTransferenciaCliente_consultarContrato_numeroContrato}"/>
                            </f:facet>
                            <a4j:commandLink
                                    styleClass="linkPadrao texto-size-14-real texto-cor-azul"
                                    action="#{CancelamentoContratoControle.selecionarContrato}"
                                    focus="contrato" reRender="form, mensagem"
                                    oncomplete="Richfaces.hideModalPanel('panelContrato')"
                                    title="#{msg.msg_selecionar_dados}" value="#{contrato.codigo}"/>
                        </rich:column>
                        <rich:column>
                            <f:facet name="header">
                                <h:outputText styleClass="texto-upper texto-font texto-size-14-real texto-cor-cinza texto-bold" value="#{msg_aplic.prt_cancelamentoTransferenciaCliente_consultarContrato_plano}"/>
                            </f:facet>
                            <a4j:commandLink
                                    styleClass="linkPadrao texto-size-14-real texto-cor-azul"
                                    action="#{CancelamentoContratoControle.selecionarContrato}"
                                    focus="contrato" reRender="form, mensagem"
                                    oncomplete="Richfaces.hideModalPanel('panelContrato')"
                                    title="#{msg.msg_selecionar_dados}" value="#{contrato.plano.descricao}"/>
                        </rich:column>
                        <rich:column>
                            <f:facet name="header">
                                <h:outputText styleClass="texto-upper texto-font texto-size-14-real texto-cor-cinza texto-bold" value="#{msg_aplic.prt_cancelamentoTransferenciaCliente_consultarContrato_dataInicio}"/>
                            </f:facet>
                            <a4j:commandLink
                                             styleClass="linkPadrao texto-size-14-real texto-cor-azul"
                                             action="#{CancelamentoContratoControle.selecionarContrato}"
                                             focus="contrato" reRender="form, mensagem"
                                             oncomplete="Richfaces.hideModalPanel('panelContrato')"
                                             title="#{msg.msg_selecionar_dados}" value="#{contrato.vigenciaDe_Apresentar}"/>
                        </rich:column>
                        <rich:column>
                            <f:facet name="header">
                                <h:outputText styleClass="texto-upper texto-font texto-size-14-real texto-cor-cinza texto-bold" value="#{msg_aplic.prt_cancelamentoTransferenciaCliente_consultarContrato_dataTermino}"/>
                            </f:facet>
                            <a4j:commandLink
                                             styleClass="linkPadrao texto-size-14-real texto-cor-azul"
                                             action="#{CancelamentoContratoControle.selecionarContrato}"
                                             focus="contrato" reRender="form, mensagem"
                                             oncomplete="Richfaces.hideModalPanel('panelContrato')"
                                             title="#{msg.msg_selecionar_dados}" value="#{contrato.vigenciaAteAjustada_Apresentar}"/>
                        </rich:column>

                        <rich:column>
                            <f:facet name="header">
                                <h:outputText styleClass="texto-upper texto-font texto-size-14-real texto-cor-cinza texto-bold" value="#{msg_bt.btn_opcoes}"/>
                            </f:facet>
                            <a4j:commandLink id="linkResultadoPesquisaContrato"
                                             styleClass="linkPadrao texto-size-14-real texto-cor-azul"
                                             action="#{CancelamentoContratoControle.selecionarContrato}"
                                             focus="contrato" reRender="form, mensagem"
                                             oncomplete="Richfaces.hideModalPanel('panelContrato')"
                                             title="#{msg.msg_selecionar_dados}">
                                <span>Selecionar </span>
                                <i class="fa-icon-arrow-right"></i>
                            </a4j:commandLink>
                        </rich:column>
                    </rich:dataTable>
                    <%--<rich:datascroller align="center" for="formContrato:resultadoConsultaContrato" maxPages="10" id="scResultadoContrato"/>--%>
                <rich:datascroller align="center" for="formContrato:resultadoConsultaContrato" maxPages="10"
                                   styleClass="scrollPureCustom" renderIfSinglePage="false"
                                   id="scResultadoContrato" />
                </h:panelGrid>
                <h:panelGrid columns="1" width="100%" rendered="#{empty CancelamentoContratoControle.listaConsultarContrato}">
                    <h:outputText id="msgSemContrato" styleClass="tituloCamposNegritoMaior" value="O Cliente #{CancelamentoContratoControle.cancelamentoContratoVO.cliente.pessoa.nome} não possui nenhum Contrato para Receber Dias"  />
                </h:panelGrid>
                <h:panelGrid id="mensagemConsultaContrato" columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid columns="1" width="100%">
                        <h:outputText styleClass="mensagem" value="#{CancelamentoContratoControle.mensagem}"/>
                        <h:outputText styleClass="mensagemDetalhada" value="#{CancelamentoContratoControle.mensagemDetalhada}"/>
                    </h:panelGrid>
                </h:panelGrid>
            <h:panelGrid>
                <h:panelGroup>
                <i class="fa-icon-warning-sign texto-cor-vermelho texto-size-20"></i>
            	<h:outputText styleClass="mensagem" value="Atenção! Essa consulta não apresenta contratos de bolsa." />
                </h:panelGroup>
            </h:panelGrid>
            </h:panelGrid>
        </a4j:form>
    </rich:modalPanel>
    <div styleClass="col-md-12"><hr class="dividerFundoClaro"/></div>
    <h:form id="form">
        <h:panelGrid columns="2" width="100%">
            <h:panelGrid columns="1" width="100%" cellpadding="5" style="margin-left: auto; margin-right: auto; padding-left: 70px; padding-right: 70px;">
                <h:outputText value="NOME DO CLIENTE: " styleClass="texto-size-14-real texto-cor-cinza-2 texto-font texto-bold" style="font-weight: bold;"  />
                <h:outputText id="clienteNomeAuto" styleClass="texto-size-14-real texto-cor-cinza-2 texto-font" value="#{ClienteControle.clienteVO.pessoa.nome}"/>
                <br>
                <h:outputText rendered="#{CancelamentoContratoControle.apresentarBotoes}" styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold" value="CLIENTE PARA TRANSFERÊNCIA" />

                <h:panelGroup>
                    <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold" value="VALOR FINAL A SER TRANSFERIDO: "/>
                    <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{MovPagamentoControle.empresaLogado.moeda} "/>
                    <h:outputText id="valorFinal" styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{CancelamentoContratoControle.cancelamentoContratoVO.valorASerDevolvido}">
                        <f:converter converterId="FormatadorNumerico" />
                    </h:outputText>
                </h:panelGroup>

                <h:panelGroup id="panelGroupCliente" rendered="#{CancelamentoContratoControle.apresentarBotoes}">
                    <h:outputText  styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold to-uper-case" value="#{msg_aplic.prt_cancelamentoTransferenciaCliente_nomeCliente}" />
                    <rich:spacer width="10"/>
                    <h:inputText  id="nomeCliente" size="40"  readonly="true" maxlength="50" onblur="blurinput(this);" style="height: 36.05 !important;"
                                  onfocus="focusinput(this);" styleClass="inputTextClean"
                                  value="#{CancelamentoContratoControle.cancelamentoContratoVO.cliente.pessoa.nome}" />

                    <a4j:commandLink id="linkPesquisaClienteAuto" style="margin: 0 5px; font-size: 20px;"
                                       rendered="#{!CancelamentoContratoControle.cancelamentoContratoVO.depositaNaConta}"
                                       oncomplete="Richfaces.showModalPanel('panelCliente')"
                                     title="#{msg_aplic.prt_cancelamentoTransferenciaCliente_consultarCliente}">
                        <i class="fa-icon-search"></i>
                    </a4j:commandLink>

                    <a4j:commandLink rendered="#{!CancelamentoContratoControle.cancelamentoContratoVO.depositaNaConta}"
                                     id="limparCampoCliente" immediate="true"
                                     style="margin: 0 5px; font-size: 20px;"
                                     action="#{CancelamentoContratoControle.limparCampoCliente}"
                                     title="#{msg_aplic.prt_limparCampo}"
                                     reRender="nomeCliente,panelGroupContratoSup,panelContrato">
                        <i class="fa-icon-eraser"></i>
                    </a4j:commandLink>

                </h:panelGroup>

                <c:if test="${CancelamentoContratoControle.cancelamentoContratoVO.cliente.codigo ne 0 && CancelamentoContratoControle.cancelamentoContratoVO.tipoTranferenciaCancelamento eq 'TR' && CancelamentoContratoControle.cancelamentoContratoVO.cliente.situacao ne 'AT'}">
                    <h:panelGroup layout="block">
                        <h:outputText
                                styleClass="texto-size-18-real texto-cor-cinza texto-font"
                                style="color: red"
                                value="Este modelo de cancelamento transferindo o contrato, leva em consideração data futura ou retroativa apenas para ajustar o contrato que está sendo cancelado. O contrato novo é lançado e se inicia na data de lançamento do cancelamento."/>
                    </h:panelGroup>

                    <h:panelGroup layout="block" styleClass="texto-size-14-real texto-cor-cinza texto-font">
                    <h:outputText value="A transferência de dias para um aluno visitante ou inativo irá gerar um contrato com as mesmas configurações do contrato de origem, o calculo de dias será feito no valor do dia do contrato de origem."/>
                    </h:panelGroup>
                </c:if>

                <h:panelGroup id="panelGroupContratoSup">
                <h:panelGroup id="panelGroupContrato"
                              rendered="#{CancelamentoContratoControle.cancelamentoContratoVO.cliente.codigo ne 0 && CancelamentoContratoControle.cancelamentoContratoVO.tipoTranferenciaCancelamento eq 'TR' && CancelamentoContratoControle.cancelamentoContratoVO.cliente.situacao eq 'AT'}">

                    <h:outputText  styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold to-uper-case"
                                   style="margin-right: 10px"
                                   value="#{msg_aplic.prt_cancelamentoTransferenciaCliente_nomeContrato}" />

                    <h:inputText id="selectContrato" size="39" style="height: 36.05 !important;"
                                 readonly="true" maxlength="50"
                                 onblur="blurinput(this);"
                                 onfocus="focusinput(this);"
                                 styleClass="inputTextClean"
                                 value="#{CancelamentoContratoControle.cancelamentoContratoVO.contratoCreditadoNrDias.plano.descricao}" />

                    <a4j:commandLink id="linkPesquisarContratoAuto"
                                     oncomplete="Richfaces.showModalPanel('panelContrato')"
                                     reRender="panelContrato"
                                     action="#{CancelamentoContratoControle.consultarContrato}"
                                     style="margin: 0 5px; font-size: 20px;"
                                     title="#{msg_aplic.prt_cancelamentoTransferenciaCliente_consultarContrato}">
                        <i class="fa-icon-search"></i>
                    </a4j:commandLink>

                    <a4j:commandLink reRender="selectContrato"
                                     id="limparCampoContrato"
                                     style="margin: 0 5px; font-size: 20px;"
                                     immediate="true"
                                     action="#{CancelamentoContratoControle.limparCampoContrato}"
                                     title="#{msg_aplic.prt_limparCampo}">
                        <i class="fa-icon-eraser"></i>
                    </a4j:commandLink>
                </h:panelGroup>
                 </h:panelGroup>
                <h:panelGroup>
                    <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold"
                                  style="margin-bottom: 10px; display: block"
                                  value="OBSERVAÇÃO:"/>

                    <h:inputTextarea value="#{CancelamentoContratoControle.cancelamentoContratoVO.observacao}"
                                     disabled="#{!CancelamentoContratoControle.apresentarBotoes}"
                                     id="descricaoCalculo" styleClass="texto-size-14-real texto-cor-cinza texto-font"  rows="7" cols="90"/>
                </h:panelGroup>
                <h:panelGrid id="msg" columns="2" width="100%" >
                    <h:panelGrid columns="1" width="100%">
                        <h:commandButton rendered="#{CancelamentoContratoControle.erro}" image="./imagens/erro.png"/>
                    </h:panelGrid>
                    <h:panelGrid columns="1" width="100%">
                        <h:outputText id="msgCancelamentoVerde" styleClass="tituloCamposVerde" value="#{CancelamentoContratoControle.mensagem}"/>
                        <h:outputText id="msgCancelamentoDeta" styleClass="tituloCamposNegritoMaiorVermelho" value="#{CancelamentoContratoControle.mensagemDetalhada}"/>
                    </h:panelGrid>
                </h:panelGrid>

                <h:panelGrid id="panelBotoes" columns="10" style="float: right;">
                        <h:panelGroup rendered="#{CancelamentoContratoControle.apresentarBotoes && !CancelamentoContratoControle.processandoOperacao}">
                            <h:commandLink id="voltar" title="Voltar Passo" action="#{CancelamentoContratoControle.voltarTelaTransferencia}" styleClass="pure-button">
                                <i class="fa-icon-arrow-left"></i>
                            </h:commandLink>
                            <rich:spacer width="7"/>
                            <a4j:commandLink id="confirmar"  title="Finalizar" reRender="form,formConfirmacaoCancelamento,msg,panelBotoes,panelAutorizacaoFuncionalidade,panelMensagem"
                                               action="#{CancelamentoContratoControle.validarDadosTransferencia}"  oncomplete="#{CancelamentoContratoControle.mensagemNotificar}"
                                               styleClass="pure-button pure-button-primary">
                                <i class="fa-icon-ok"></i>&nbsp;Confirmar
                            </a4j:commandLink>
                        </h:panelGroup>


                                <h:panelGroup rendered="#{!CancelamentoContratoControle.apresentarBotoes && !CancelamentoContratoControle.processandoOperacao}">
                                    <c:if test="${CancelamentoContratoControle.apresentarBotoesTransferenciaContratoDias}">
                                        <a4j:commandLink id="comprovanteOpCanTrans"
                                                         rendered="#{!CancelamentoContratoControle.apresentarBotoes}"
                                                         title="Imprimir Comprovante da Operação de Cancelamento"
                                                         action="#{CancelamentoContratoControle.imprimirComprovanteOperacao}"
                                                         oncomplete="abrirPopupPDFImpressao('relatorio/#{CancelamentoContratoControle.nomeArquivoComprovanteOperacao}','', 780, 595);"
                                                         styleClass="pure-button">
                                            <i class="fa-icon-print"></i>&nbsp Imprimir Cancelamento
                                        </a4j:commandLink>

                                        <rich:spacer width="7"
                                                     rendered="#{!CancelamentoContratoControle.apresentarBotoes && CancelamentoContratoControle.cancelamentoContratoVO.comprovanteTransDiasVO.codigo != 0}"/>

                                        <a4j:commandLink id="comprovanteOpCanTransSaida"
                                                         rendered="#{!CancelamentoContratoControle.apresentarBotoes  && CancelamentoContratoControle.cancelamentoContratoVO.comprovanteTransDiasVO.codigo != 0}"
                                                         title="Imprimir Comprovante Transferência de Dias Restante"
                                                         action="#{CancelamentoContratoControle.imprimirComprovanteOperacaoSaida}"
                                                         oncomplete="abrirPopupPDFImpressao('relatorio/#{CancelamentoContratoControle.nomeArquivoComprovanteOperacao}','', 780, 595);"
                                                         styleClass="pure-button">
                                            <i class="fa-icon-print"></i>&nbsp Imprimir Trans. de Dias
                                        </a4j:commandLink>

                                        <rich:spacer width="7"
                                                     rendered="#{!CancelamentoContratoControle.apresentarBotoes}"/>
                                    </c:if>
                            <h:commandLink id="fechar" title="Fechar Janela" onclick="fecharJanela();" styleClass="pure-button pure-button-primary">
                                <i class="fa-icon-remove" ></i>&nbsp;Fechar
                            </h:commandLink>
                        </h:panelGroup>


                </h:panelGrid>
                <h:panelGrid rendered="#{CancelamentoContratoControle.processandoOperacao}" columns="1">
                    <h:outputText id="msgProcessando" styleClass="mensagem"  value="Operação já está sendo processada. Dentro de alguns instantes, atualize a página para verificar se operação já foi concluída"/>
                    <rich:spacer height="7"/>
                    <a4j:commandLink id="atualizar" title="Atulaizar" onclick="window.location.reload();fireElementFromParent('form:btnAtualizaCliente');"  styleClass="pure-button pure-button-primary">
                        <i class="fa-icon-refresh"></i>&nbsp;Atualizar
                    </a4j:commandLink>
               </h:panelGrid>                   
            </h:panelGrid>
        </h:panelGrid>
    </h:form>
    <jsp:include page="includes/include_modal_mensagem_generica.jsp" flush="true"/>
    <jsp:include page="includes/autorizacao/include_autorizacao_funcionalidade.jsp" flush="true"/>

</f:view>

var $j = jQuery.noConflict();


$j.fn.infiniteCarousel = function () {

    function repeat(str, num) {
        return new Array( num + 1 ).join( str );
    }
  
    return this.each(function () {
        var $jwrapper = $j('> div', this).css('overflow', 'hidden'),
            $jslider = $jwrapper.find('> ul'),
            $jitems = $jslider.find('> li'),
            $jsingle = $jitems.filter(':first'),
            
            singleWidth = $jsingle.outerWidth(), 
            visible = Math.ceil($jwrapper.innerWidth() / singleWidth), // note: doesn't include padding or border
            currentPage = 1,
            pages = Math.ceil($jitems.length / visible);            


        // 1. Pad so that 'visible' number will always be seen, otherwise create empty items
        if (($jitems.length % visible) != 0) {
            $jslider.append(repeat('<li class="empty" />', visible - ($jitems.length % visible)));
            $jitems = $jslider.find('> li');
        }

        // 2. Top and tail the list with 'visible' number of items, top has the last section, and tail has the first
        $jitems.filter(':first').before($jitems.slice(- visible).clone().addClass('cloned'));
        $jitems.filter(':last').after($jitems.slice(0, visible).clone().addClass('cloned'));
        $jitems = $jslider.find('> li'); // reselect
        
        // 3. Set the left position to the first 'real' item
        $jwrapper.scrollLeft(singleWidth * visible);
        
        // 4. paging function
        function gotoPage(page) {
            var dir = page < currentPage ? -1 : 1,
                n = Math.abs(currentPage - page),
                left = singleWidth * dir * visible * n;
            
            $jwrapper.filter(':not(:animated)').animate({
                scrollLeft : '+=' + left
            }, 500, function () {
                if (page == 0) {
                    $jwrapper.scrollLeft(singleWidth * visible * pages);
                    page = pages;
                    
                } else if (page > pages) {
                    $jwrapper.scrollLeft(singleWidth * visible);
                    // reset back to start position
                    page = 1;
                    document.getElementById('listaFotos').style.marginLeft = '0px';
                } 

                currentPage = page;
            });                
            
            return false;
        }
        
        $jwrapper.after('<a class="arrow back"></a><a class="arrow forward"></a>');
        
        // 5. Bind to the forward and back buttons
        $j('a.back', this).click(function () {
            return gotoPage(currentPage - 1);                
        });
        
        $j('a.forward', this).click(function () {
            return gotoPage(currentPage + 1);
        });
        
        // create a public interface to move to a specific page
        $j(this).bind('goto', function (event, page) {
            gotoPage(page);
        });
    });  
};

$j(document).ready(function () {
  $j('.infiniteCarousel').infiniteCarousel();
});

function preencherValorChamarBotao(idBotao, idClienteHidden, valorClienteHidden){
	var clienteHidden = document.getElementById(idClienteHidden);
	var botao = document.getElementById(idBotao);
	clienteHidden.value = valorClienteHidden;
	botao.click();
	}
	
	function limparValor(str){
		var n = str.lastIndexOf(':');
		var id = str.substring(0, n)+':nrlida';
	    document.getElementById(id).innerHTML='';
	}

	function mostrarCliente(str){
		var clienteHidden = document.getElementById('form:matriculaSelecionada');
		var botao = document.getElementById('form:mostrarAluno');
		clienteHidden.value = str;
		botao.click();
	}
	function atualizar(){
		var botao = document.getElementById('form:atualizarPagina');
		botao.click();
	}
	function atualizarNovo(){
		var botao = document.getElementById('form:atualizarNovo');
		botao.click();
	}
	
	function keyEnterPressed(e){
		if(e.keyCode=='13'){
			var botao = document.getElementById('form:enviarMensagem');
			botao.click();
			}
		}

	function showAddContato(){
		document.getElementById('modalAddContato').style.display = 'block';
	}
	function hideAddContato(){
		document.getElementById('modalAddContato').style.display = 'none';
	}
	
	function showContatos(){
		setTimeout(function () {document.getElementById('thumbs-wrapper').style.overflow = 'visible';}, 2000);
	}
	function hideContatos(){
		document.getElementById('thumbs-wrapper').style.overflow = 'hidden';
	}
	
	function abaixar(){
		var div = document.getElementById('divCaixaMensagens');
		div.scrollTop =  '9999';
    }

	
<%-- 
    Document   : listaAlunosTurma
    Created on : 11/10/2010, 16:58:17
    Author     : carla
--%>

<%@page contentType="text/html"%>
<%@page pageEncoding="ISO-8859-1"%>
<head><script type="text/javascript" language="javascript" src="./script/script.js"></script></head>
<script type="text/javascript" language="javascript" src="../hoverform.js"></script>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">

<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<script>
    window.addEventListener("load", function (event) {
        executePostMessage({loaded: true, message: 'window load'})
    });
</script>

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText value="Lista de Alunos da Turma"/>
    </title>
    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <f:facet name="header">

            <jsp:include page="topoReduzido.jsp"/>

        </f:facet>
        <html>
            <body onload="fireElement('form:botaoAtualizarPagina')"/>
            <h:form id="form" >
                <a4j:commandButton id="botaoAtualizarPagina" reRender="form" style="display:none"/>
                <h:panelGrid columns="1" width="100%" >
                    <h:panelGrid columns="1" style="height:25px; background-image:url('./imagens/fundoBarraTopo.png');background-repeat: repeat-x;" columnClasses="colunaCentralizada" width="100%">
                        <h:outputText styleClass="tituloFormulario" value="Lista de Alunos"/>
                    </h:panelGrid>
                    <h:panelGrid id="panelFiltros" columns="2" columnClasses="colunaCentralizada" width="100%">
                        <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar" columnClasses="classEsquerda, classDireita" width="100%" style="padding-left:0px;">
                            <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_ConsultaAlunoTurma_Turma_codigo}"/>
                            <h:outputText styleClass="tituloCampos" value="#{ConsultarTurmaControle.horarioTurmaConcatenado.turma.codigo}" />
                            <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_ConsultaAlunoTurma_Horario_codigo}"/>
                            <h:outputText styleClass="tituloCampos" value="#{ConsultarTurmaControle.horarioTurma.codigo}" />
                            <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_HorarioTurma_ativo}"/>
                            <h:outputText styleClass="tituloCampos" value="#{ConsultarTurmaControle.horarioTurma.ativo ? 'Sim' : 'Não'}" />
                            <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_ConsultaAlunoTurma_descricao}"/>
                            <h:outputText styleClass="tituloCampos" value="#{ConsultarTurmaControle.horarioTurmaConcatenado.turma.identificador}" />
                            <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_ConsultaAlunoTurma_professor}"/>
                            <h:outputText styleClass="tituloCampos" value="#{ConsultarTurmaControle.horarioTurmaConcatenado.professor}"
                                          rendered="#{!ConsultarTurmaControle.permiteAlterarProfessorConsultaTurma}" />
                            <h:panelGroup rendered="#{ConsultarTurmaControle.permiteAlterarProfessorConsultaTurma}">
                                <h:selectOneMenu  id="HorarioTurma_professor" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form"
                                                  onchange="submit();" onclick="fireElementFromAnyParent('form:consultar')"
                                                  value="#{ConsultarTurmaControle.horarioTurma.professor.codigo}"
                                                  valueChangeListener="#{ConsultarTurmaControle.alterarProfessor}"
                                                  >

                                    <f:selectItems  value="#{ConsultarTurmaControle.listaSelectItemProfessor}" />
                                    <a4j:support reRender="form" />
                                </h:selectOneMenu>
                                <a4j:commandButton id="atualizar_HorarioTurma_professor" action="#{ConsultarTurmaControle.montarListaSelectItemProfessor}"
                                                   image="imagens/atualizar.png" immediate="true" ajaxSingle="true" reRender="formHorarioTurma:HorarioTurma_professor"/>
                                <a4j:commandButton id="cadastroProfessor" action="#{ColaboradorControle.inicializarTipoColaborador}"
                                                   oncomplete="abrirPopup('professor.jsp', 'Colaborador', 680, 595);" image="./images/icon_add.gif" />
                            </h:panelGroup>
                            <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_ConsultaAlunoTurma_nivel}"/>
                            <h:outputText styleClass="tituloCampos" value="#{ConsultarTurmaControle.horarioTurmaConcatenado.nivel}" />
                            <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_ConsultaAlunoTurma_ambiente}"/>
                            <h:outputText styleClass="tituloCampos" value="#{ConsultarTurmaControle.horarioTurmaConcatenado.ambiente}" />
                            <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_ConsultaAlunoTurma_horaInicio}"/>
                            <h:outputText styleClass="tituloCampos" value="#{ConsultarTurmaControle.horarioTurmaConcatenado.horaInicial}" />
                            <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_ConsultaAlunoTurma_horaFinal}"/>
                            <h:outputText styleClass="tituloCampos" value="#{ConsultarTurmaControle.horarioTurmaConcatenado.horaFinal}" />
                            <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_ConsultaAlunoTurma_diaDaSemana}"/>
                            <h:outputText styleClass="tituloCampos" value="#{ConsultarTurmaControle.horarioTurma.diaSemana_Apresentar}" />
                            <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_ConsultaAlunoTurma_nrMaximoAluno}"/>
                            <h:outputText styleClass="tituloCampos" value="#{ConsultarTurmaControle.horarioTurma.nrMaximoAluno}" />
                            <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_ConsultaAlunoTurma_nrAlunoMatriculado}"/>
                            <h:outputText styleClass="tituloCampos" value="#{ConsultarTurmaControle.horarioTurma.nrAlunoMatriculado - ConsultarTurmaControle.horarioTurma.nrAlunoReposicao}" />
                            <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_ConsultaAlunoTurma_nrAlunoReposicao}"/>
                            <h:outputText styleClass="tituloCampos" value="#{ConsultarTurmaControle.horarioTurma.nrAlunoReposicao}" />
                            <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_ConsultaAlunoTurma_nrAlunoMatriculaFutura}"/>
                            <h:outputText styleClass="tituloCampos" value="#{ConsultarTurmaControle.horarioTurma.nrAlunoMatriculadosFuturo}" />
                        </h:panelGrid>
                    </h:panelGrid>
                    <rich:dataTable width="100%" headerClass="consulta" rowClasses="linhaImpar, linhaPar" columnClasses="colunaAlinhamento" id="tabela"
                                    value="#{ConsultarTurmaControle.listaAlunosTurma}" rows="50" var="aluno" rowKeyVar="status">
                        <%@include file="pages/ce/includes/include_contador_richtable.jsp" %>

                        <rich:column>
                            <f:facet name="header">
                                <h:outputText value="Foto" />
                            </f:facet>
                            <a4j:commandLink id="visualizarAluno"
                                             styleClass="linkPadrao texto-size-16-real"
                                             action="#{ConsultarTurmaControle.irParaTelaCliente}"
                                             oncomplete="abrirPopup('clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);">
                                <f:param name="state" value="AC"/>
                                <h:graphicImage style="width:30px;height:30px; border-radius: 50%;"
                                                styleClass="tooltipsterright"
                                                url="#{aluno.clienteVO.pessoa.urlFoto}">
                                </h:graphicImage>
                            </a4j:commandLink>

                        </rich:column>
                        <rich:column >
                            <f:facet name="header">
                                <h:outputText value="Realizar Contato" />
                            </f:facet>
                            <a4j:commandButton id="btnRealizarContato" image="./imagensCRM/icontelefonista.png" title="Realizar Contato"
                                               action="#{ConsultarTurmaControle.montarRealizarContato}" ajaxSingle="true"
                                               oncomplete="#{HistoricoContatoControle.executarAberturaPopupRealizarContato}" />
                        </rich:column>

                        <rich:column sortBy="#{aluno.clienteVO.matricula}"  filterEvent="onkeyup">
                            <f:facet name="header">
                                <h:outputText value="Matrícula" />
                            </f:facet>
                            <h:outputText value="#{aluno.clienteVO.matricula}" />
                        </rich:column>
                        <rich:column sortBy="#{aluno.clienteVO.pessoa.nome}"  filterEvent="onkeyup">
                            <f:facet name="header">
                                <h:outputText value="Nome do Aluno" />
                            </f:facet>
                            <h:outputText value="#{aluno.clienteVO.pessoa.nome}" />
                        </rich:column>
                        <rich:column sortBy="#{aluno.matriculaAlunoHorarioTurmaVO.dataInicio}"  filterEvent="onkeyup">
                            <f:facet name="header">
                                <h:outputText value="Data Início Matrícula" />
                            </f:facet>
                            <h:outputText value="#{aluno.matriculaAlunoHorarioTurmaVO.dataInicio}" >
                                <f:convertDateTime pattern="dd/MM/yyyy"/>
                            </h:outputText>
                        </rich:column>
                        <rich:column sortBy="#{aluno.matriculaAlunoHorarioTurmaVO.dataFim}"  filterEvent="onkeyup">
                            <f:facet name="header">
                                <h:outputText value="Data Fim Matrícula" />
                            </f:facet>
                            <h:outputText value="#{aluno.matriculaAlunoHorarioTurmaVO.dataFim}" >
                                <f:convertDateTime pattern="dd/MM/yyyy"/>
                            </h:outputText>
                        </rich:column>
                        <rich:column sortBy="#{aluno.contratoDuracaoVO.numeroMeses}"  filterEvent="onkeyup">
                            <f:facet name="header">
                                <h:outputText value="Duração Contrato" />
                            </f:facet>
                            <h:outputText value="#{aluno.contratoDuracaoVO.numeroMeses}" />
                        </rich:column>

                        <rich:column sortBy="#{aluno.matriculaAlunoHorarioTurmaVO.codigo == 0 ? 'Sim' : 'Não'}"  filterEvent="onkeyup">
                            <f:facet name="header">
                                <h:outputText value="Reposição" />
                            </f:facet>
                            <h:outputText value="#{aluno.matriculaAlunoHorarioTurmaVO.codigo == 0 ? 'Sim' : 'Não'}" />
                        </rich:column>
                    </rich:dataTable>
                    <rich:datascroller align="center" for="form:tabela" maxPages="10" id="sctabela" />
                    <h:panelGrid columns="1" width="100%" styleClass="tabMensagens">
                        <h:panelGrid columns="2" width="100%" styleClass="tabMensagens">
                            <h:commandButton id="icListaTurmaSuc" rendered="#{ConsultarTurmaControle.sucesso}" image="./imagens/sucesso.png" />
                            <h:commandButton id="icListaTurmaFal" rendered="#{ConsultarTurmaControle.erro}" image="./imagens/erro.png" />
                            <h:panelGrid id="mensagem" columns="1" width="100%" columnClasses="colunaEsquerda">
                                <h:outputText id="msgListaTurma" styleClass="mensagem"  value="#{ConsultarTurmaControle.mensagem}"/>
                                <h:outputText id="msgListaTurmaDet" styleClass="mensagemDetalhada" value="#{ConsultarTurmaControle.mensagemDetalhada}"/>
                            </h:panelGrid>
                        </h:panelGrid>
                    </h:panelGrid>
                </h:panelGrid>

                <h:panelGrid id="total" columns="2" width="100%" columnClasses="colunaDireita">
                    <h:outputText styleClass="tituloCamposDestaqueNegrito " value="Total: #{ConsultarTurmaControle.totalizadorAlunos}"  />
                </h:panelGrid>
            </h:form>
        </body>
    </html>
</h:panelGrid>
</f:view>


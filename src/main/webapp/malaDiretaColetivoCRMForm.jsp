<%@page contentType="text/html" %>
<%@page pageEncoding="UTF-8" %>
<head>
    <%@include file="/includes/include_import_minifiles.jsp" %>
</head>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">

<%@taglib prefix="f" uri="http://java.sun.com/jsf/core" %>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html" %>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax" %>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich" %>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<script type="text/javascript" src="script/scriptSMS.js">

</script>

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title><h:outputText value="#{msg_aplic.prt_MalaDireta_tituloForm}"/></title>

    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <f:facet name="header">
            <jsp:include page="topoReduzidoCRM.jsp"/>
        </f:facet>

        <h:form id="formF">
            <h:commandLink action="#{MetaCRMControle.liberarBackingBeanMemoria}" id="idLiberarBackingBeanMemoria"
                           style="display: none"/>
            <h:panelGrid id="panelGridForm" columns="1" styleClass="tabForm" width="100%">
                <h:panelGrid columns="1"
                             style="height:25px; background-image:url('./imagens/fundoBarraTopo.png');background-repeat: repeat-x;"
                             columnClasses="colunaCentralizada" width="100%">
                    <h:outputText styleClass="tituloFormulario" value="#{msg_aplic.prt_MalaDireta_tituloForm}"/>
                </h:panelGrid>

                <h:panelGrid columns="2">

                    <%--PESSOAS QUE NÃO FORAM ADICIONADAS NA LISTA--%>
                    <h:panelGroup id="pessoasNaoAdicionadas">
                        <h:outputText styleClass="tituloCampos"
                                      style="color: red"
                                      escape="false"
                                      value="#{MetaCRMControle.pessoasSemEmailTelefone}"/>
                    </h:panelGroup>

                    <h:panelGrid columns="1">
                        <h:panelGroup id="totalLista" style="float: right; margin-right: 10px;">
                            <h:outputText styleClass="tituloCampos" style="font-weight: bold;"
                                          value="#{msg_aplic.prt_AberturaMeta_totalizadorListaMailing}:"/>
                            <rich:spacer width="10px;"/>
                            <h:outputText styleClass="tituloCampos" style="font-weight: bold;"
                                          value="#{MetaCRMControle.malaDiretaVO.totalPessoaMalaDireta}"/>
                        </h:panelGroup>
                        <h:panelGroup style="float: right; margin-right: 10px;">
                            <h:outputText styleClass="tituloCampos" style="font-weight: bold;"
                                          value="#{msg_aplic.prt_AberturaMeta_totalizadorEnviados}:"/>
                            <rich:spacer width="10px;"/>
                            <h:outputText styleClass="tituloCampos" style="font-weight: bold;"
                                          value="#{MetaCRMControle.malaDiretaVO.totalPessoaMalaDiretaEnviada}"/>
                        </h:panelGroup>
                    </h:panelGrid>
                </h:panelGrid>
                <rich:tabPanel width="100%" switchType="client">
                    <rich:tab id="dadosEmail" label="Dados Envio" switchType="client">
                        <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar"
                                     columnClasses="classEsquerda, classDireita" width="100%">
                            <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_MalaDireta_meioEnvio}"/>
                            <h:outputText styleClass="tituloCampos"
                                          value="#{MetaCRMControle.malaDiretaVO.meioDeEnvioEnum.descricao}"/>
                            <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_MalaDireta_dataEnvio}"/>
                            <h:inputText id="dataEnvio"
                                         onkeypress="return mascara(this.formF, 'formF:dataEnvio', '99/99/9999', event);"
                                         readonly="true" size="10" maxlength="10" onblur="blurinput(this);"
                                         onfocus="focusinput(this);" styleClass="form"
                                         value="#{MetaCRMControle.malaDiretaVO.dataEnvio}">
                                <f:convertDateTime pattern="dd/MM/yyyy"/>
                            </h:inputText>
                            <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_MalaDireta_remetente}"/>
                            <h:panelGroup id="panelResponsavelGrupo">
                                <h:inputText id="textColaboradorResponsavel" size="50" readonly="true"
                                             onblur="blurinput(this);" onfocus="focusinput(this);"
                                             styleClass="form"
                                             value="#{MetaCRMControle.malaDiretaVO.remetente.nome}"/>
                            </h:panelGroup>
                            <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_MalaDireta_modeloMensagem}"/>
                            <h:panelGroup>
                                <h:inputText id="modeloMensagem" size="70" readonly="true" maxlength="100"
                                             onblur="blurinput(this);"
                                             onfocus="focusinput(this);" styleClass="form"
                                             value="#{MetaCRMControle.malaDiretaVO.modeloMensagem.titulo}"/>
                                <a4j:commandButton oncomplete="Richfaces.showModalPanel('panelModeloMensagem')"
                                                   image="imagensCRM/informacao.gif"
                                                   alt="#{msg_aplic.prt_MalaDireta_consultarModeloMensagem}"/>
                            </h:panelGroup>

                            <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_MalaDireta_titulo}"/>
                            <h:panelGroup>
                                <h:inputText id="titulo" size="70" maxlength="100" onblur="blurinput(this);"
                                             onfocus="focusinput(this);" styleClass="form"
                                             value="#{MetaCRMControle.malaDiretaVO.titulo}"/>
                                <rich:toolTip for="titulo" followMouse="true" direction="top-right"
                                              style="width:300px; height:#{MetaCRMControle.tamanhoToolTip}; "
                                              showDelay="200">
                                    <h:outputText styleClass="tituloCampos" escape="false"
                                                  value="#{msg.msg_tip_tituloMail}#{MetaCRMControle.termosFiscalizados}#{msg.msg_tip_tituloMailPontos}"/>
                                </rich:toolTip>
                            </h:panelGroup>
                            <h:outputText rendered="#{MetaCRMControle.malaDiretaVO.meioDeEnvio==2}"
                                          styleClass="tituloCampos"
                                          value="#{msg_aplic.prt_ModeloMensagem_adicionarTag}"/>
                            <h:panelGroup rendered="#{MetaCRMControle.malaDiretaVO.meioDeEnvio==2}">
                                <a4j:commandButton id="tagNome" reRender="textComentario"
                                                   image="./imagensCRM/botaoTagNome.png" title="Tag Nome"
                                                   action="#{MetaCRMControle.incluirTagNome}"/>
                            </h:panelGroup>
                        </h:panelGrid>

                        <h:panelGrid rendered="#{MetaCRMControle.malaDiretaVO.meioDeEnvio==1}" columns="1"
                                     styleClass="tabForm" columnClasses="colunaCentralizada" width="100%">
                            <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_MalaDireta_mensagem}"/>
                            <rich:editor configuration="editorpropriedades" viewMode="visual" theme="advanced"
                                         id="imputMensagem" height="500" width="700"
                                         value="#{MetaCRMControle.malaDiretaVO.mensagem}"/>
                        </h:panelGrid>
                        <h:panelGrid columns="1" rendered="#{MetaCRMControle.malaDiretaVO.meioDeEnvio==2}"
                                     id="textComentario" styleClass="tabForm"
                                     style="height:25px; background-image:url('./imagens/fundoBarraTopo.png');background-repeat: repeat-x;"
                                     columnClasses="colunaCentralizada" width="100%">
                            <h:outputText styleClass="tituloFormulario" value="#{msg_aplic.prt_Agenda_comentario}"/>
                            <rich:spacer height="20"/>
                            <h:outputLink value="#{SuperControle.urlWikiCRM}Recursos_Extras:Mailing#SMS"
                                          title="Clique e saiba mais: Mailing SMS" target="_blank">
                                <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                                <h:outputText value="Mailing - SMS"></h:outputText>
                            </h:outputLink>
                            <h:inputTextarea
                                    onkeypress="soma(this.value);" onkeyup="soma(this.value);"
                                    style="align:center;" id="comentarioTextArea" cols="100" rows="2"
                                    value="#{MetaCRMControle.malaDiretaVO.mensagem}"/>

                            <rich:toolTip for="comentarioTextArea" followMouse="true"
                                          rendered="#{MetaCRMControle.toolTipSMS}"
                                          direction="top-right"
                                          style="width:300px; height:#{MetaCRMControle.tamanhoToolTipSMS}; "
                                          showDelay="200">
                                <h:outputText styleClass="tituloCampos" escape="false"
                                              value="#{MetaCRMControle.termosFiscalizados}"/>
                            </rich:toolTip>

                            <h:panelGroup id="panelTamanhoRestante" style="align:center;" layout="block"
                                          rendered="#{MetaCRMControle.malaDiretaVO.meioDeEnvio==2}">
                                <h:inputText style="align:center;" disabled="true" size="3" id="tamanhoRestante"/>

                            </h:panelGroup>
                        </h:panelGrid>
                    </rich:tab>

                    <rich:tab id="emailsEnviados" label="Enviar Para:" switchType="client">

                        <rich:dataTable id="malaDiretaEnviadaVO" width="100%" headerClass="subordinado"
                                        styleClass="tabFormSubordinada"
                                        rowClasses="linhaImpar, linhaPar" columnClasses="colunaCentralizada"
                                        value="#{MetaCRMControle.malaDiretaVO.malaDiretaEnviadaVOs}"
                                        var="malaDiretaEnviada">

                            <rich:column>
                                <f:facet name="header">
                                    <h:outputText value="#{msg_aplic.prt_MalaDiretaEnviada_pessoa}"/>
                                </f:facet>
                                <h:outputText value="#{malaDiretaEnviada.clienteVO.pessoa.nome}"
                                              rendered="#{malaDiretaEnviada.apresentarColunaPessoa}"/>
                                <h:outputText value="#{malaDiretaEnviada.passivoVO.nome}"
                                              rendered="#{malaDiretaEnviada.apresentarColunaPassivo}"/>
                                <h:outputText value="#{malaDiretaEnviada.indicadoVO.nomeIndicado}"
                                              rendered="#{malaDiretaEnviada.apresentarColunaIndicado}"/>
                            </rich:column>

                            <rich:column rendered="#{MetaCRMControle.malaDiretaVO.meioDeEnvio==1}">
                                <f:facet name="header">
                                    <h:outputText value="#{msg_aplic.prt_MalaDiretaEnviada_Email}"/>
                                </f:facet>
                                <%--emails--%>
                                <h:outputText rendered="#{malaDiretaEnviada.apresentarColunaPassivo}"
                                              value="#{malaDiretaEnviada.passivoVO.email}"/>
                                <h:outputText rendered="#{malaDiretaEnviada.apresentarColunaIndicado}"
                                              value="#{malaDiretaEnviada.indicadoVO.email}"/>
                                <h:outputText rendered="#{malaDiretaEnviada.apresentarColunaPessoa}"
                                              value="#{malaDiretaEnviada.emails}"/>
                            </rich:column>
                            <rich:column rendered="#{MetaCRMControle.malaDiretaVO.meioDeEnvio==2}">
                                <f:facet name="header">
                                    <h:outputText value="#{msg_aplic.prt_MalaDiretaEnviada_Telefone_Celular}"/>
                                </f:facet>
                                <%--telefones--%>
                                <h:outputText rendered="#{malaDiretaEnviada.apresentarColunaPessoa}"
                                              value="#{malaDiretaEnviada.telefonesCelulares}"/>
                                <h:outputText rendered="#{malaDiretaEnviada.apresentarColunaIndicado}"
                                              value="#{malaDiretaEnviada.indicadoVO.telefones}"/>
                                <h:outputText rendered="#{malaDiretaEnviada.apresentarColunaPassivo}"
                                              value="#{malaDiretaEnviada.passivoVO.telefones}"/>
                            </rich:column>
                        </rich:dataTable>
                    </rich:tab>
                </rich:tabPanel>
                <h:panelGrid columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid id="panelGridMensagens" columns="1" width="100%">
                        <h:outputText styleClass="mensagem" value="#{MetaCRMControle.mensagem}"/>
                        <h:outputText styleClass="mensagemDetalhada" value="#{MetaCRMControle.mensagemDetalhada}"/>
                    </h:panelGrid>
                    <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                        <h:panelGroup>
                            <a4j:commandButton id="enviarEmail"
                                               rendered="#{MetaCRMControle.malaDiretaVO.meioDeEnvio == 1}"
                                               action="#{MetaCRMControle.enviarEmailColetivoCRM}"
                                               reRender="formF"
                                               oncomplete="#{MetaCRMControle.msgAlert}recarregarMetas();"
                                               value="#{msg_bt.btn_gravar}"
                                               image="./imagensCRM/botaoEnviar.png"
                                               title="#{msg.msg_gravar_dados}"
                                               accesskey="2" styleClass="botoes"/>
                            <a4j:commandButton id="enviarSMS"
                                               rendered="#{MetaCRMControle.malaDiretaVO.meioDeEnvio == 2}"
                                               action="#{MetaCRMControle.enviarSMSColetivoCRM}"
                                               reRender="formF"
                                               oncomplete="#{MetaCRMControle.msgAlert}recarregarMetas();"
                                               value="#{msg_bt.btn_gravar}"
                                               image="./imagensCRM/botaoEnviar.png"
                                               title="#{msg.msg_gravar_dados}"
                                               accesskey="2" styleClass="botoes"/>
                        </h:panelGroup>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
        </h:form>
    </h:panelGrid>
</f:view>
<script>
    document.getElementById("formF:dataEnvio").focus();
</script>


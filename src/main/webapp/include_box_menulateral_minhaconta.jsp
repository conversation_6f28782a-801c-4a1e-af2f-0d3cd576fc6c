<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<f:loadBundle var="msg_menu" basename="propriedades.Menu"/>

<!-- inicio box -->
<h:panelGroup layout="block" styleClass="menuLateral tudo">
    <h:panelGroup layout="block" styleClass="grupoMenuLateral">
        <h:panelGroup layout="block" styleClass="grupoMenuTitulo">
            <i class="fa-icon-briefcase"></i> Canal Pacto
        </h:panelGroup>

        <h:panelGroup layout="block" styleClass="grupoMenuItem">
            <a4j:commandLink id="linkMinhaContaHome" styleClass="titulo3 linkFuncionalidade"
                             action="#{CanalPactoControle.abrirTelaMinhaConta}">Minha Conta</a4j:commandLink>
        </h:panelGroup>

        <h:panelGroup layout="block" styleClass="grupoMenuItem">
            <a4j:commandLink id="linkMinhaContaSolAberta" styleClass="titulo3 linkFuncionalidade"
                             action="#{CanalPactoControle.abrirTelaMinhaContaSolicitacoesEmAberto}">Solicita��es em Aberto</a4j:commandLink>
        </h:panelGroup>

        <h:panelGroup layout="block" styleClass="grupoMenuItem">
            <a4j:commandLink id="linkMinhaContaSolConcluida" styleClass="titulo3 linkFuncionalidade"
                             action="#{CanalPactoControle.abrirTelaMinhaContaSolicitacoesConcluidas}"
                             oncomplete="#{CanalPactoControle.mensagemNotificar}">Solicita��es Conclu�das</a4j:commandLink>
        </h:panelGroup>


    </h:panelGroup>


</h:panelGroup>
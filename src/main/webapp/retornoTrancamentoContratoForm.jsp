<%@page contentType="text/html"%>
<%@page pageEncoding="ISO-8859-1"%>
<head><%@include file="/includes/include_import_minifiles.jsp" %></head>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<link href="./css/telaCliente.css" rel="stylesheet" type="text/css">
<script type="text/javascript" language="javascript" src="hoverform.js"></script>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<link href="beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/pure-buttons.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/pure-ext.css" type="text/css" rel="stylesheet"/>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>
<style type="text/css">
    .pure-button{
        font-family: sans-serif;
        font-size:100%;
        margin:0;
        vertical-align:baseline;
        *vertical-align:middle
    }

    td.w48{
        width: 48%;
    }
    .tabelaDados tbody tr:hover, .tblHeaderLeft:not(.contratoSelecionado) tbody tr:hover{
        background-color: white !important;
    }
</style>

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText
            value="#{msg_aplic.prt_retornoTrancamentoContrato_tituloForm}" />
    </title>
    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <c:set var="titulo" scope="session" value="Retorno de Trancamento"/>
        <c:set var="urlWiki" scope="session" value="${SuperControle.urlBaseConhecimento}conhecimento/como-retornar-um-aluno-do-trancamento-nova-tela-do-cliente/"/>
        <f:facet name="header">
            <jsp:include page="topoReduzido_material.jsp"/>
        </f:facet>
    </h:panelGrid>

    <div styleClass="col-md-12"><hr class="dividerFundoClaro"/></div>

    <rich:modalPanel id="panelObservacaoTrancamento" autosized="false"
                     shadowOpacity="true" width="450" height="250"
                     styleClass="novaModal"
                     onshow="document.getElementById('formObservacaoTrancamento:observacao').focus();">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Observação do Trancamento"/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText
                        styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign "
                        id="hidelinkObservacaoTrancamento"/>
                <rich:componentControl for="panelObservacaoTrancamento"
                                       attachTo="hidelinkObservacaoTrancamento" operation="hide" event="onclick"/>
            </h:panelGroup>

        </f:facet>
        <a4j:form id="formObservacaoTrancamento">
            <h:panelGrid columns="1" width="100%"
                         columnClasses="colunaCentralizada">

                <h:panelGrid id="panelObservacaoTrancamento" columns="1"
                             width="100%" columnClasses="colunaEsquerda">
                    <h:panelGrid columns="1">
                        <h:inputTextarea id="observacao" readonly="true"
                                         value="#{RetornoTrancamentoContratoControle.visualizacaoObservacao}"
                                         rows="7" cols="65" />
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
        </a4j:form>
    </rich:modalPanel>

    <h:form id="form">
        <h:panelGrid columns="1" width="100%"  styleClass="font-size-em-max">
            <h:panelGrid id="pnlValores"  columns="1" width="100%" styleClass="font-size-em-max">
                    <h:panelGrid columns="1" styleClass="font-size-em-max">
                        <h:outputText value="NOME DO CLIENTE:" styleClass="texto-size-14 texto-cor-cinza texto-font texto-bold" />
                        <h:outputText id="nomeClienteHr" styleClass="texto-size-14-real texto-cor-cinza texto-font"
                                      value="#{RetornoTrancamentoContratoControle.trancamentoContratoVO.contratoVO.pessoa.nome}"/>
                    </h:panelGrid>

                <br/>
                <h:dataTable id="parcela" width="100%"
                                style="margin: 0;"
                                styleClass="tabelaDados semZebra texto-size-14 texto-cor-cinza texto-font"
                                columnClasses="centralizado"
                                value="#{RetornoTrancamentoContratoControle.listaTrancamentoVOs}"
                                var="trancamento">
                    <h:column>
                        <f:facet name="header">
                            <h:outputText styleClass="texto-bold" value="Contrato" />
                        </f:facet>
                        <h:outputText value="#{trancamento.contratoVO.codigo}" style="float: right; margin-right: 10px;"/>
                    </h:column>
                    <h:column>
                        <f:facet name="header">
                            <h:outputText value="Valor Congelado" />
                        </f:facet>
                        <h:outputText value="#{trancamento.valorCongelado}" style="float: right; margin-right: 10px;">
                            <f:converter converterId="FormatadorNumerico" />
                        </h:outputText>
                    </h:column>
                    <h:column>
                        <f:facet name="header">
                            <h:outputText value="Dias Congelado" />
                        </f:facet>
                        <h:outputText value="#{trancamento.nrDiasCongelado}" style="float: right; margin-right: 10px;"/>
                    </h:column>
                    <h:column>
                        <f:facet name="header">
                            <h:outputText value="Início" />
                        </f:facet>
                        <h:outputText value="#{trancamento.dataTrancamento_Apresentar}" />
                    </h:column>
                    <h:column>
                        <f:facet name="header">
                            <h:outputText value="Retorno" />
                        </f:facet>
                        <h:outputText value="#{trancamento.dataRetorno_Apresentar}" />
                    </h:column>
                    <h:column>
                        <f:facet name="header">
                            <h:outputText value="Responsável Operação" />
                        </f:facet>
                        <h:outputText value="#{trancamento.responsavelOperacao.nome}" />
                    </h:column>
                    <h:column>
                        <f:facet name="header">
                            <h:outputText value="Observação" />
                        </f:facet>
                        <a4j:commandLink reRender="formObservacaoTrancamento"
                                           oncomplete="Richfaces.showModalPanel('panelObservacaoTrancamento'), setFocus(formObservacaoTrancamento,'formObservacaoTrancamento:observacao')"
                                           styleClass="icone linkAzul"
                                           action="#{RetornoTrancamentoContratoControle.obterObservacaoTrancamento}" >
                            <i class="fa-icon-search"/>
                                </a4j:commandLink>
                    </h:column>
                </h:dataTable>

                <h:panelGrid id="panelClienteRetornoNoPrazo" columns="1" style="margin: 20px 0;"
                             width="100%">
                    <h:panelGrid columns="1" columnClasses="colunaCentralizada"
                                 rendered="#{RetornoTrancamentoContratoControle.trancamentoContratoVO.apresentarPanelClienteRetornoNoPrazo}"
                                 width="100%">
                        <h:outputText id="informacaoRetorno" styleClass="texto-size-14 texto-cor-cinza texto-font  texto-bold"
                                        value="O cliente retornou no prazo informado pela OPERAÇÃO DE TRANCAMENTO.
                                      Para reativar o plano, basta confirmar o RETORNO DO TRANCAMENTO." />
                    </h:panelGrid>
                </h:panelGrid>
                <h:panelGrid id="panelClienteRetornoForaPrazo" columns="1"
                             width="100%">
                    <h:panelGrid columns="1"
                                 rendered="#{RetornoTrancamentoContratoControle.trancamentoContratoVO.apresentarPanelClienteRetornoForaPrazo}"
                                 width="100%">
                        <h:outputText styleClass="texto-size-14 texto-cor-cinza texto-font texto-bold"
                                      value="O cliente Retornou depois do prazo informado pela OPERAÇÃO DE TRANCAMENTO."/>
                        <h:panelGroup layout="block" style="margin: 20px 0;">
                            <h:selectBooleanCheckbox id="pagaTaxa"
                                                     value="#{RetornoTrancamentoContratoControle.trancamentoContratoVO.taxaDiasExcedidos}">
                                <a4j:support event="onclick"
                                             action="#{RetornoTrancamentoContratoControle.descarmarDescontoExcedida}"
                                             reRender="panelTaxaExcedida, descontaDias,panelDescontarDias" />
                            </h:selectBooleanCheckbox>
                            <rich:spacer width="10px" />
                            <h:outputText value="Pagar Taxa pelos Dias Excedidos"
                                          styleClass="texto-size-14 texto-cor-cinza texto-font" />
                            <rich:spacer width="20px" />
                            <h:selectBooleanCheckbox id="descontaDias"
                                                     value="#{RetornoTrancamentoContratoControle.trancamentoContratoVO.descontoDiasExcedidos}">
                                <a4j:support event="onclick"
                                             action="#{RetornoTrancamentoContratoControle.descarmarTaxaExcedida}"
                                             reRender="pagaTaxa,panelTaxaExcedida,panelDescontarDias, panelMensagem" />
                            </h:selectBooleanCheckbox>
                            <rich:spacer width="10px" />
                            <h:outputText value="Descontar Dias Excedidos"
                                          styleClass="texto-size-14 texto-cor-cinza texto-font" />
                        </h:panelGroup>
                        <h:panelGrid id="panelTaxaExcedida">
                            <h:panelGrid
                                rendered="#{RetornoTrancamentoContratoControle.trancamentoContratoVO.taxaDiasExcedidos}">

                                <h:panelGroup>
                                    <h:outputText styleClass="texto-size-14 texto-cor-cinza texto-font"
                                                  value="#{msg_aplic.prt_trancamentoContrato_produtoTrancamento}" />
                                    <rich:spacer width="7" />
                                    <h:selectOneMenu id="produtoTrancamento"
                                                     onblur="blurinput(this);" onfocus="focusinput(this);"
                                                     styleClass="form"
                                                     value="#{RetornoTrancamentoContratoControle.trancamentoContratoVO.produtoTrancamento.codigo}">
                                        <f:selectItems
                                            value="#{RetornoTrancamentoContratoControle.listaProdutoTrancamentoVOs}" />
                                        <a4j:support event="onchange"
                                                     action="#{RetornoTrancamentoContratoControle.obterValorProduto}"
                                                     reRender="totalLancado,panelPeriodoTrancamento,panelMensagem" />
                                    </h:selectOneMenu>
                                    <rich:spacer width="3" />
                                    <a4j:commandButton id="atualizar_produtoTrancamento"
                                                       action="#{RetornoTrancamentoContratoControle.montarDadosListaProdutoTrancamentoVOs}"
                                                       image="imagens/atualizar.png" immediate="true"
                                                       ajaxSingle="true" reRender="form:produtoTrancamento" />
                                </h:panelGroup>
                                <h:panelGrid columns="1">
                                    <h:panelGroup>
                                        <h:outputText styleClass="texto-size-14 texto-cor-cinza texto-font"
                                                      value="#{msg_aplic.prt_trancamentoContrato_tipoJustificativa}" />
                                        <rich:spacer width="20" />
                                        <h:selectOneMenu id="justificativa" onblur="blurinput(this);"
                                                         onfocus="focusinput(this);" styleClass="form"
                                                         value="#{RetornoTrancamentoContratoControle.trancamentoContratoVO.tipoJustificativa}">
                                            <f:selectItems
                                                value="#{RetornoTrancamentoContratoControle.listaJustificativaOperacaoVOs}" />
                                            <a4j:support event="onchange"
                                                         action="#{RetornoTrancamentoContratoControle.limparMensagem}"
                                                         reRender="panelMensagem" />
                                        </h:selectOneMenu>
                                        <rich:spacer width="3" />
                                        <a4j:commandButton id="atualizar_justificativaDeOperacao"
                                                           action="#{RetornoTrancamentoContratoControle.montarDadosListaJustificativaOperacaoVOs}"
                                                           image="imagens/atualizar.png" immediate="true"
                                                           ajaxSingle="true" reRender="form:justificativa" />
                                        <rich:spacer width="5" />
                                        <h:commandButton id="adicionarTipoOperacao"
                                                         action="#{JustificativaOperacaoControle.novo}"
                                                         alt="Cadastrar Tipo Justificava Operação "
                                                         onclick="abrirPopup('justificativaOperacaoCons.jsp', 'JustificativaOperacao', 800, 595);"
                                                         image="./images/icon_add.gif" />
                                    </h:panelGroup>
                                </h:panelGrid>
                                <h:panelGrid columns="1" id="panelPeriodoTrancamento">
                                    <h:panelGroup rendered="#{RetornoTrancamentoContratoControle.trancamentoContratoVO.apresentarPeriodoTrancamento}">
                                        <h:panelGrid columns="1">
                                            <h:outputText styleClass="texto-size-14 texto-cor-cinza texto-font"
                                                          value="Período de Trancamento " />
                                            <h:panelGroup>
                                                <h:outputText styleClass="texto-size-14 texto-cor-cinza texto-font" value="Início" />
                                                <rich:spacer width="10" />
                                                <h:outputText id="dataInicioTrancamento"
                                                              styleClass="texto-size-14 texto-cor-cinza texto-font"
                                                              value="#{RetornoTrancamentoContratoControle.trancamentoContratoVO.dataTrancamento_Apresentar}">
                                                    <f:convertDateTime pattern="dd/MM/yyyy" />
                                                </h:outputText>
                                                <rich:spacer width="10" />
                                                <h:outputText styleClass="texto-size-14 texto-cor-cinza texto-font" value=" Até " />
                                                <rich:spacer width="10" />
                                                <h:outputText id="dataFimTrancamento"
                                                              styleClass="texto-size-14 texto-cor-cinza texto-font"
                                                              value="#{RetornoTrancamentoContratoControle.trancamentoContratoVO.dataFimTrancamento_Apresentar}">
                                                    <f:convertDateTime pattern="dd/MM/yyyy" />
                                                </h:outputText>
                                            </h:panelGroup>
                                            <h:panelGroup>
                                                <h:outputText styleClass="texto-size-14 texto-cor-cinza texto-font"
                                                              value="Data do retorno" />
                                                <rich:spacer width="10" />
                                                <h:outputText id="dataRetornoTrancamento"
                                                              styleClass="texto-size-14 texto-cor-cinza texto-font"
                                                              value="#{RetornoTrancamentoContratoControle.trancamentoContratoVO.dataRetorno_Apresentar}">
                                                    <f:convertDateTime pattern="dd/MM/yyyy" />
                                                </h:outputText>
                                            </h:panelGroup>
                                        </h:panelGrid>
                                        <h:panelGrid columns="2">
                                            <h:outputText styleClass="texto-size-14 texto-cor-cinza texto-font"
                                                          value="#{msg_aplic.prt_trancamentoContrato_valorTrancamento} R$ " />
                                            <h:outputText style="font-weight: bold; color: green"
                                                          value="#{RetornoTrancamentoContratoControle.trancamentoContratoVO.valorTrancamento}"
                                                          id="totalLancado">
                                                <f:converter converterId="FormatadorNumerico" />
                                            </h:outputText>
                                        </h:panelGrid>
                                    </h:panelGroup>
                                </h:panelGrid>
                                <h:panelGrid columns="1">
                                    <h:outputText styleClass="texto-size-14 texto-cor-cinza texto-font" value="Observação: " />
                                    <br />
                                    <h:inputTextarea
                                        value="#{RetornoTrancamentoContratoControle.trancamentoContratoVO.observacao}"
                                        rows="7" cols="60" />
                                </h:panelGrid>
                            </h:panelGrid>
                        </h:panelGrid>
                        <h:panelGrid id="panelDescontarDias">
                            <h:panelGrid
                                rendered="#{RetornoTrancamentoContratoControle.trancamentoContratoVO.descontoDiasExcedidos}">
                                <h:panelGroup>
                                    <h:outputText styleClass="texto-size-14 texto-cor-cinza texto-font"
                                                  value="Cliente Deveria ter Retornado no Dia: " />
                                    <rich:spacer width="5" />
                                    <h:outputText id="dataPrevistaRetornoTra"
                                                  styleClass="texto-size-14 texto-cor-cinza texto-font"
                                                  value="#{RetornoTrancamentoContratoControle.trancamentoContratoVO.dataRetorno_Apresentar}">
                                        <f:convertDateTime pattern="dd/MM/yyyy" />
                                    </h:outputText>
                                </h:panelGroup>
                                <h:panelGroup>
                                    <h:outputText styleClass="texto-size-14 texto-cor-cinza texto-font"
                                                  value="Número de Dia(s) a ser Descontados: " />
                                    <rich:spacer width="5" />
                                    <h:outputText id="nDiasDescontados" styleClass="texto-size-14 texto-cor-cinza texto-font"
                                                  value="#{RetornoTrancamentoContratoControle.trancamentoContratoVO.numeroDiasDescontar}" />
                                </h:panelGroup>

                                <h:panelGroup rendered="#{RetornoTrancamentoContratoControle.trancamentoContratoVO.contratoVencido}">
                                    <h:outputText id="msgContratoVencido" styleClass="mensagemDetalhada"
                                                  value="*Esse contrato ficará inativo após o retorno descontando dias, pois o número de dias a descontar é maior que os dias congelados" />
                                </h:panelGroup>

                            </h:panelGrid>
                        </h:panelGrid>
                    </h:panelGrid>
                </h:panelGrid>
                <h:panelGrid id="gridMensagens" columns="3" width="100%">

                    <h:panelGrid columns="1" width="100%">
                        <h:outputText id="msgRetorno" styleClass="mensagem"
                                      value="#{RetornoTrancamentoContratoControle.mensagem}" />
                        <h:outputText id="msgRetornoDetalhada"
                                      styleClass="mensagemDetalhada"
                                      value="#{RetornoTrancamentoContratoControle.mensagemDetalhada}" />
                    </h:panelGrid>
                </h:panelGrid>
                <h:panelGrid id="panelBotoes" width="100%" columns="1" columnClasses="colunaCentralizada">
                    <h:panelGroup
                            rendered="#{RetornoTrancamentoContratoControle.apresentarBotoes && !RetornoTrancamentoContratoControle.processandoOperacao}">
                        <h:panelGroup layout="block" styleClass="container-botoes">
                            <a4j:commandLink id="confirmar" title="Finalizar"
                                             reRender="form,panelAutorizacaoFuncionalidade"
                                             action="#{RetornoTrancamentoContratoControle.validarRetorno}"
                                             oncomplete="#{RetornoTrancamentoContratoControle.mensagemNotificar}"
                                             styleClass="pure-button pure-button-primary">
                                <i class="fa-icon-ok"></i>&nbsp;Confirmar
                            </a4j:commandLink>

                            <h:commandLink id="cancelar" onclick="fecharJanela();executePostMessage({close: true});" styleClass="pure-button">
                                <i class="fa-icon-remove"></i>&nbsp;Cancelar
                            </h:commandLink>

                        </h:panelGroup>
                        <h:panelGroup
                                rendered="#{!RetornoTrancamentoContratoControle.apresentarBotoes && !RetornoTrancamentoContratoControle.processandoOperacao}">
                            <h:panelGroup layout="block" styleClass="container-botoes">
                                <h:commandLink id="fechar" title="Fechar Janela" onclick="fecharJanela();executePostMessage({close: true});"
                                               styleClass="pure-button">
                                    &nbsp;Fechar
                                </h:commandLink>
                            </h:panelGroup>
                        </h:panelGroup>
                    </h:panelGroup>
                </h:panelGrid>
                <h:panelGrid rendered="#{RetornoTrancamentoContratoControle.processandoOperacao}" columns="1">
                    <h:outputText id="msgProcessando" styleClass="mensagem"  value="Operação já está sendo processada. Dentro de alguns instantes, atualize a página para verificar se operação já foi concluída"/>
                    <rich:spacer height="7"/>
                    <a4j:commandLink id="atualizar" title="Atulaizar" onclick="window.location.reload();fireElementFromParent('form:btnAtualizaCliente');"  styleClass="pure-button pure-button-primary">
                        <i class="fa-icon-refresh"></i>&nbsp;Atualizar
                    </a4j:commandLink>
               </h:panelGrid>                           
            </h:panelGrid>
        </h:panelGrid>
        <h:panelGrid columns="1" width="100%">
            <rich:modalPanel id="panel" width="350" height="200"
                             styleClass="novaModal"
                             showWhenRendered="#{RetornoTrancamentoContratoControle.trancamentoContratoVO.mensagemErro}">
                <f:facet name="header">
                    <h:panelGroup>
                        <h:outputText value="Atenção!"/>
                    </h:panelGroup>
                </f:facet>
                <f:facet name="controls">
                    <h:panelGroup>
                        <h:outputText
                                styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign "
                                id="hlomn"/>
                        <rich:componentControl for="panel"
                                               attachTo="hlomn" operation="hide" event="onclick"/>
                    </h:panelGroup>

                </f:facet>
                <h:panelGrid columns="1" width="100%"
                             columnClasses="colunaCentralizada">
                    <h:panelGroup>
                        <h:outputText styleClass="mensagemDetalhada"
                                      value="#{RetornoTrancamentoContratoControle.mensagemDetalhada}" />
                    </h:panelGroup>
                </h:panelGrid>
            </rich:modalPanel>

            <rich:modalPanel id="panelNecessitaManutencao" width="400" height="250"
                             styleClass="novaModal"
                             showWhenRendered="#{RetornoTrancamentoContratoControle.trancamentoContratoVO.mensagemManutencao}">
                <f:facet name="header">
                    <h:panelGroup >
                        <h:outputText value="Atenção!"></h:outputText>
                    </h:panelGroup>
                </f:facet>
                <f:facet name="controls">
                    <h:panelGroup>
                        <h:outputText
                                styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign "
                                id="hlomne"/>
                        <rich:componentControl for="panel"
                                               attachTo="hlomn2" operation="hide" event="onclick"/>
                    </h:panelGroup>

                </f:facet>
                <h:panelGrid columns="1" width="100%" cellpadding="15"
                             columnClasses="colunaCentralizada">
                    <h:panelGroup>
                        <h:outputText styleClass="mensagemDetalhada"
                                      value="Aluno irá voltar em turmas que estão acima do limite ou que estão inativas. Então, ao concluir essa operação, o sistema irá redirecioná-lo para a tela de manutenção de modalidade, para que essa situação seja resolvida." />
                    </h:panelGroup>
                </h:panelGrid>
                <h:panelGroup layout="block" styleClass="container-botoes">
                    <a4j:commandLink id="confirmarRetorno"  title="Finalizar"
                                                 reRender="form,panelAutorizacaoFuncionalidade"
                                                 action="#{RetornoTrancamentoContratoControle.fecharMenssagemManutencao}"
                                                 oncomplete="#{RetornoTrancamentoContratoControle.mensagemNotificar}"
                                                 styleClass="botaoPrimario texto-size-14-real">
                                    <i class="fa-icon-ok"></i>&nbsp;Continuar
                    </a4j:commandLink>
                </h:panelGroup>
            </rich:modalPanel>
        </h:panelGrid>
    </h:form>
    <%@include file="includes/autorizacao/include_autorizacao_funcionalidade.jsp" %>
</f:view>
<script>
    window.addEventListener("load", function (event) {
        executePostMessage({loaded: true, message: 'window load'})
    });
    document.getElementById("form:produtoTrancamento").focus();
</script>

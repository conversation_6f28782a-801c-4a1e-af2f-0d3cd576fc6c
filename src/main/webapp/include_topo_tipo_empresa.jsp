<%@page contentType="text/html" %>
<%@page pageEncoding="ISO-8859-1" %>
<%@include file="/includes/imports.jsp" %>

<style>
    /* Buzz Out */
    @-webkit-keyframes hvr-buzz-out {
        10% {
            -webkit-transform: translateX(3px) rotate(2deg);
            transform: translateX(3px) rotate(2deg);
        }
        20% {
            -webkit-transform: translateX(-3px) rotate(-2deg);
            transform: translateX(-3px) rotate(-2deg);
        }
        30% {
            -webkit-transform: translateX(3px) rotate(2deg);
            transform: translateX(3px) rotate(2deg);
        }
        40% {
            -webkit-transform: translateX(-3px) rotate(-2deg);
            transform: translateX(-3px) rotate(-2deg);
        }
        50% {
            -webkit-transform: translateX(2px) rotate(1deg);
            transform: translateX(2px) rotate(1deg);
        }
        60% {
            -webkit-transform: translateX(-2px) rotate(-1deg);
            transform: translateX(-2px) rotate(-1deg);
        }
        70% {
            -webkit-transform: translateX(2px) rotate(1deg);
            transform: translateX(2px) rotate(1deg);
        }
        80% {
            -webkit-transform: translateX(-2px) rotate(-1deg);
            transform: translateX(-2px) rotate(-1deg);
        }
        90% {
            -webkit-transform: translateX(1px) rotate(0);
            transform: translateX(1px) rotate(0);
        }
        100% {
            -webkit-transform: translateX(-1px) rotate(0);
            transform: translateX(-1px) rotate(0);
        }
    }

    @keyframes hvr-buzz-out {
        10% {
            -webkit-transform: translateX(3px) rotate(2deg);
            transform: translateX(3px) rotate(2deg);
        }
        20% {
            -webkit-transform: translateX(-3px) rotate(-2deg);
            transform: translateX(-3px) rotate(-2deg);
        }
        30% {
            -webkit-transform: translateX(3px) rotate(2deg);
            transform: translateX(3px) rotate(2deg);
        }
        40% {
            -webkit-transform: translateX(-3px) rotate(-2deg);
            transform: translateX(-3px) rotate(-2deg);
        }
        50% {
            -webkit-transform: translateX(2px) rotate(1deg);
            transform: translateX(2px) rotate(1deg);
        }
        60% {
            -webkit-transform: translateX(-2px) rotate(-1deg);
            transform: translateX(-2px) rotate(-1deg);
        }
        70% {
            -webkit-transform: translateX(2px) rotate(1deg);
            transform: translateX(2px) rotate(1deg);
        }
        80% {
            -webkit-transform: translateX(-2px) rotate(-1deg);
            transform: translateX(-2px) rotate(-1deg);
        }
        90% {
            -webkit-transform: translateX(1px) rotate(0);
            transform: translateX(1px) rotate(0);
        }
        100% {
            -webkit-transform: translateX(-1px) rotate(0);
            transform: translateX(-1px) rotate(0);
        }
    }
</style>

<h:panelGroup layout="block" id="panelSuperiorConhecimento"
              styleClass="col-md-4 col-md-offset-4"
              style="z-index: 100; position: absolute; border-bottom-left-radius: 15px; border-bottom-right-radius: 15px;">

    <h:panelGroup layout="block" styleClass="panelEsconderUCP">
        <h:panelGroup layout="block"
                      id="iconeUCP"
                      styleClass="icon-ucp col-md-6"
                      style="text-align: center;
                       background-color: #ff6e1e;
                       border-bottom-left-radius: 15px;
                       border-bottom-right-radius: 15px;
                       height: 40px;
                       width: 386px;
                       padding: 30px 5px 5px;
                       box-shadow: 0 2px 10px 0 rgba(0,0,0,0.25);">

            <h:outputText
                    id="lnkDescricaoEmpresa"
                    style="text-decoration: none; font-size: 40px; font-weight: bold; color: #094771;"
                    escape="false"
                    value="#{SuperControle.mensagemTipoEmpresa}"/>

        </h:panelGroup>
    </h:panelGroup>
</h:panelGroup>

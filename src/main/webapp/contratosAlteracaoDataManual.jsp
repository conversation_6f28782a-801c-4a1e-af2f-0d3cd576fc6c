<%@page contentType="text/html"%>
<%@page pageEncoding="ISO-8859-1"%>
<head>
    <%@include file="includes/include_import_minifiles.jsp"%>
</head>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<script type="text/javascript" language="javascript" src="hoverform.js"></script>
<link href="./beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>

<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>
<%@taglib prefix="jsfChart" uri="http://sourceforge.net/projects/jsf-comp" %>
<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText value="BI - Movimentação de Contratos"/>
    </title>
    <html>        
        <h:form id="form" style="overflow: hidden;">
            <a4j:commandButton id="botaoAtualizarPagina" reRender="form:panelGroup, form:item" style="display:none"/>
            <c:set var="titulo" scope="session" value=" Alteração Manual de Data Base do Contrato - ${fn:length(RelControleOperacoesControle.controleOperacoesRelVO.listaPendenciaResumoPessoaRelVOs)} registros "/>
            <c:set var="urlWiki" scope="session" value="${SuperControle.urlBaseConhecimento}bi-controle-de-operacoes-de-excecoes-adm/"/>
            <h:panelGroup layout="block" styleClass="pure-g-r">
                <f:facet name="header">
                    <jsp:include page="topo_reduzido_popUp.jsp"/>
                </f:facet>
            </h:panelGroup>
            <h:panelGrid columns="1" width="100%" >
                                        <h:panelGrid id="panelCliente" width="100%">
                                            <h:outputText styleClass="mensagem"  value="#{RelControleOperacoesControle.mensagem}"/>
                                            <h:outputText styleClass="mensagemDetalhada" value="#{RelControleOperacoesControle.mensagemDetalhada}"/>

                                            <h:panelGroup>
                                                <h:selectBooleanCheckbox id="mostrarPaginacao" value="#{RelControleOperacoesControle.mostrarPaginacao}">
                                                    <a4j:support event="onclick" action="#{DataScrollerControle.resetDatascroller}" reRender="panelCliente,item,panelGroup"/>
                                                </h:selectBooleanCheckbox>
                                                <rich:spacer width="10px"/>
                                                <h:outputText value="Mostrar Paginação" styleClass="tituloCampos" style="font-weight: bold;"/>
                                            </h:panelGroup>
                                            <h:panelGrid width="100%" style="text-align: right">
                                                <h:panelGroup layout="block">
                            <a4j:commandLink id="exportarExcel"
                                                                       style="margin-left: 8px;"
                                                                       actionListener="#{ExportadorListaControle.exportar}"
                                                                       rendered="#{not empty RelControleOperacoesControle.controleOperacoesRelVO.listaPendenciaResumoPessoaRelVOs}"
                                             oncomplete="#{ExportadorListaControle.mensagemNotificar}#{ExportadorListaControle.operacaoOnComplete}#{ExportadorListaControle.msgAlert}"
                                               accesskey="2" styleClass="linkPadrao">
                                                        <f:attribute name="lista" value="#{RelControleOperacoesControle.controleOperacoesRelVO.listaPendenciaResumoPessoaRelVOs}"/>
                                                        <f:attribute name="tipo" value="xls"/>
                                <f:attribute name="itemExportacao" value="#{RelControleOperacoesControle.controleOperacoesRelVO.itemExportar}"/>
                                                        <f:attribute name="atributos" value="matriculaContrato_Apresentar=Matrícula,nomeContrato_Apresentar=Nome,contrato_Apresentar=Contrato,usuario_Apresentar=Usuário Alteração,dataBase_Apresentar=Alteração Dt. Base,dataLancamento_Apresentar=Dt. Lançamento,dataDe_Apresentar=Dt. Início,dataAte_Apresentar=Dt. Término,duracao_Apresentar=Duração"/>
                                <f:attribute name="prefixo" value="ControleOpExcecao"/>
                                <h:outputText title="Exportar para o formato Excel" styleClass="btn-print-2 excel"/>
                            </a4j:commandLink>
                                                    <%--BOTÃO PDF--%>
                            <a4j:commandLink id="exportarPdf"
                                                                       style="margin-left: 8px;"
                                                                       actionListener="#{ExportadorListaControle.exportar}"
                                                                       rendered="#{not empty RelControleOperacoesControle.controleOperacoesRelVO.listaPendenciaResumoPessoaRelVOs}"
                                             oncomplete="#{ExportadorListaControle.mensagemNotificar}#{ExportadorListaControle.operacaoOnComplete}#{ExportadorListaControle.msgAlert}"
                                               accesskey="2" styleClass="linkPadrao">
                                                        <f:attribute name="lista" value="#{RelControleOperacoesControle.controleOperacoesRelVO.listaPendenciaResumoPessoaRelVOs}"/>
                                                        <f:attribute name="tipo" value="pdf"/>
                                <f:attribute name="itemExportacao" value="#{RelControleOperacoesControle.controleOperacoesRelVO.itemExportar}"/>
                                                        <f:attribute name="atributos" value="matriculaContrato_Apresentar=Matrícula,nomeContrato_Apresentar=Nome,contrato_Apresentar=Contrato,usuario_Apresentar=Usuário Alteração,dataBase_Apresentar=Alteração Dt. Base,dataLancamento_Apresentar=Dt. Lançamento,dataDe_Apresentar=Dt. Início,dataAte_Apresentar=Dt. Término,duracao_Apresentar=Duração"/>
                                <f:attribute name="prefixo" value="ControleOpExcecao"/>
                                <h:outputText title="Exportar para o formato PDF" styleClass="btn-print-2 pdf"/>
                            </a4j:commandLink>
                                                </h:panelGroup>
                                            </h:panelGrid>
                    <rich:dataTable id="item" width="100%" styleClass="tabelaSimplesCustom"
                                    rendered="#{not empty RelControleOperacoesControle.controleOperacoesRelVO.listaPendenciaResumoPessoaRelVOs}"
                                                            value="#{RelControleOperacoesControle.controleOperacoesRelVO.listaPendenciaResumoPessoaRelVOs}"
                                                            rows="#{RelControleOperacoesControle.numeroPaginacao}"
                                                            var="resumoPessoa" rowKeyVar="status">

                                                <%@include file="/pages/ce/includes/include_contador_richtable.jsp" %>


                        <rich:column width="30%" sortBy="#{resumoPessoa.contratoVO.cliente.matricula}"  filterEvent="onkeyup" styleClass="col-text-align-left" headerClass="col-text-align-left">
                                                    <f:facet name="header">
                                <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="Matricula"/>
                                                    </f:facet>
                                                    <h:outputText  value="#{resumoPessoa.contratoVO.cliente.matricula}" />
                                                </rich:column>

                        <rich:column width="30%" sortBy="#{resumoPessoa.contratoVO.pessoa.nome}"  filterEvent="onkeyup" styleClass="col-text-align-left" headerClass="col-text-align-left">
                                                    <f:facet name="header">
                                <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="Nome"/>
                                                    </f:facet>
                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{resumoPessoa.contratoVO.pessoa.nome}" />
                                                </rich:column>

                        <rich:column sortBy="#{resumoPessoa.contratoVO.codigo}"  filterEvent="onkeyup" styleClass="col-text-align-left" headerClass="col-text-align-left">
                                                    <f:facet name="header">
                                <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="Contrato"/>
                                                    </f:facet>
                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{resumoPessoa.contratoVO.codigo}" />
                                                </rich:column>
                        <rich:column sortBy="#{resumoPessoa.contratoVO.responsavelDataBase.nome}"  filterEvent="onkeyup" styleClass="col-text-align-left" headerClass="col-text-align-left">
                                                    <f:facet name="header">
                                <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="Usuário Alteração"/>
                                                    </f:facet>
                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{resumoPessoa.contratoVO.responsavelDataBase.nome}" />
                                                </rich:column>
                        <rich:column sortBy="#{resumoPessoa.contratoVO.vigenciaAteAjustada}"  filterEvent="onkeyup" styleClass="col-text-align-left" headerClass="col-text-align-left">
                                                    <f:facet name="header">
                                <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="Alteração Dt. Base"/>
                                                    </f:facet>
                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{resumoPessoa.contratoVO.dataAlteracaoManual_Apresentar}" />
                                                </rich:column>
                        <rich:column sortBy="#{resumoPessoa.contratoVO.dataLancamento}"  filterEvent="onkeyup" styleClass="col-text-align-left" headerClass="col-text-align-left">
                                                    <f:facet name="header">
                                <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="DT. Lançamento"/>
                                                    </f:facet>
                                                    <h:outputText  value="#{resumoPessoa.contratoVO.dataLancamento_Apresentar}" />
                                                </rich:column>
                        <rich:column sortBy="#{resumoPessoa.contratoVO.vigenciaDe}"  filterEvent="onkeyup" styleClass="col-text-align-left" headerClass="col-text-align-left">
                                                    <f:facet name="header">
                                <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="DT. Início"/>
                                                    </f:facet>
                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{resumoPessoa.contratoVO.vigenciaDe_Apresentar}" />
                                                </rich:column>
                        <rich:column sortBy="#{resumoPessoa.contratoVO.vigenciaAteAjustada}"  filterEvent="onkeyup" styleClass="col-text-align-left" headerClass="col-text-align-left">
                                                    <f:facet name="header">
                                <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="DT. Término"/>
                                                    </f:facet>
                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{resumoPessoa.contratoVO.vigenciaAteAjustada_Apresentar}" />
                                                </rich:column>
                        <rich:column sortBy="#{resumoPessoa.contratoVO.contratoDuracao.numeroMeses}"  filterEvent="onkeyup" styleClass="col-text-align-left" headerClass="col-text-align-left">
                                                    <f:facet name="header">
                                <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="Duração"/>
                                                    </f:facet>
                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{resumoPessoa.contratoVO.contratoDuracao.numeroMeses}" />
                                                </rich:column>
                        <rich:column styleClass="col-text-align-center" headerClass="col-text-align-center">
                            <a4j:commandLink styleClass="linkPadrao texto-cor-azul texto-size-16-real" action="#{RelControleOperacoesControle.irParaTelaCliente}"
                                                                       reRender="form" oncomplete="abrirPopup('clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);">
                                                        <f:param name="state" value="AC"/>
                                <i class="fa-icon-search"></i>
                            </a4j:commandLink>

                                                    <%--<a4j:commandButton id="visualizarCliente"  action="#{RelAlteracaoDataManualControle.irParaTelaCliente}" alt="Ir para tela de Edição do Cliente" image="../imagens/botaoVisualizar.png" oncomplete="abrirPopup('clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);">
                                                        <f:param name="state" value="AC"/>
                                                    </a4j:commandButton>--%>
                                                </rich:column>
                                            </rich:dataTable>

                                            <rich:datascroller binding="#{DataScrollerControle.dataScroller}"
                                       styleClass="scrollPureCustom"
                                       renderIfSinglePage="false"
                                                               rendered="#{RelControleOperacoesControle.mostrarPaginacao}"
                                                               align="center" for="form:item" maxPages="20" id="scitems" />

                                        </h:panelGrid>
                <h:panelGrid id="panelTotalizador" width="100%"    rendered="#{not empty RelControleOperacoesControle.controleOperacoesRelVO.listaPendenciaResumoPessoaRelVOs}">

                                            <table width="100%" border="0" cellspacing="0" cellpadding="0" class="tablepadding2">
                                                <tr>
                                                    <td align="left" valign="top"><div style="clear:both;" class="text">
                                                            <p style="margin-bottom:6px;">
                                    <span  class="texto-size-16-real texto-bold texto-cor-cinza texto-font" >Totalizador</p>

                                                        </div>
                                                        <div class="sep" style="margin:4px 0 10px 0;"><img src="images/shim.gif"></div></td>
                                                    <td>
                                                        <h:panelGrid id="panelTotalizador1" columns="3" width="100%" >
                                                            <h:panelGrid columns="1">
                                                                <h:panelGroup>
                                            <h:outputText  styleClass="texto-size-14-real texto-cor-cinza texto-font" value=" Contratos com alteração manual de data base no período = "/>
                                                                    <h:outputText value="#{fn:length(RelControleOperacoesControle.controleOperacoesRelVO.listaPendenciaResumoPessoaRelVOs)}" />
                                                                </h:panelGroup>
                                                            </h:panelGrid>
                                </h:panelGrid>
                                                    </td>
                                                </tr>
                                            </table>
                                        </h:panelGrid>
            </h:panelGrid>
       

        </h:form>
    </body>
</html>
</f:view>

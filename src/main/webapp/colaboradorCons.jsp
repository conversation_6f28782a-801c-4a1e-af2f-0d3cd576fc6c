<%@page contentType="text/html;charset=UTF-8" %>
<head><%@include file="/includes/include_import_minifiles.jsp" %></head>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<script type="text/javascript" language="javascript" src="hoverform.js"></script>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<meta name="viewport" content="width=device-width, initial-scale=1.0">
<link href="beta/css/pure-min.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/bootstrap-tabs.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/pure-ext.css" type="text/css" rel="stylesheet"/>
<script src="beta/js/jquery.js" type="text/javascript"></script>
<script src="beta/js/jquery.dataTables.min.js" type="text/javascript"></script>
<script src="beta/js/DT_bootstrap.js" type="text/javascript"></script>
<script src="beta/js/bootstrap-tab.js" type="text/javascript"></script>
<script>
    jQuery.noConflict();
</script>

<%@include file="/includes/verificaModulo.jsp" %>
<c:set var="contexto" value="${pageContext.request.contextPath}" scope="request" />

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title>${msg_aplic.prt_Colaborador_tituloForm}</title>

    <%-- INICIO HEADER --%>
    <c:set var="titulo" scope="session" value="${msg_aplic.prt_Colaborador_tituloForm}"/>
    <c:set var="urlWiki" scope="session" value="${SuperControle.urlBaseConhecimento}como-cadastrar-um-novo-colaborador/"/>
    <h:panelGroup layout="block" styleClass="pure-g-r">
        <f:facet name="header">
            <jsp:include page="topoReduzido_material.jsp"/>
        </f:facet>
    </h:panelGroup>
    <%-- FIM HEADER --%>

    <h:panelGroup layout="block" styleClass="pure-g-r">
        <h:form id="form" styleClass="pure-form pure-u-1">

            <a4j:keepAlive beanName="ExportadorListaControle"/>

            <input type="hidden" value="${modulo}" name="modulo"/>
            <input id="chavePrimaria" type="hidden" value="" name="chavePrimaria"/>

            <h:panelGroup layout="block" style="display: flex; background: #FDFDB4; padding: 10px">
                <h:graphicImage value="images/pct-alert-triangle-yellow.svg" style="width: 16px; padding-right: 5px; padding-bottom: 2px;"/>
                <h:outputText style="color: #7D7D03; font-size: 14px"
                              value="Em breve, lançaremos uma nova estrutura para o perfil do colaborador. Fique de olho para não perder as novidades!"></h:outputText>
            </h:panelGroup>

            <%-- INICIO CONTENT --%>
            <h:panelGroup>

                <%-- INICIO COMANDOS --%>
                <h:panelGroup layout="block" styleClass="pure-g-r pure-u-12-12 margin-0-auto titulo-topo">
                    <h:panelGroup layout="block" styleClass="pure-form pure-form-aligned pure-u-1-3"></h:panelGroup>
                    <h:panelGroup layout="block" styleClass="pure-u-2-3 text-right">
                        <h:panelGroup layout="block" style="line-height: 44px;">

                            <h:outputText styleClass="texto-font texto-size-13 texto-cor-cinza" value="Tipo "/>
                            <h:panelGroup layout="block" styleClass="cb-container" style="margin-right: 0px;">
                                <h:selectOneMenu id="tipoColaborador" styleClass="exportadores" style="margin-right: 5px; padding-right: 10px;"
                                                 value="#{ColaboradorControle.situacaoFiltroTipo}">
                                    <f:selectItems value="#{ColaboradorControle.listaSelectItemTipoFiltro}"/>
                                    <a4j:support event="onchange" oncomplete="recarregarTabelaColaborador()"/>
                                </h:selectOneMenu>
                            </h:panelGroup>

                            <h:outputText styleClass="texto-font texto-size-13 texto-cor-cinza" value="Situação "/>
                            <h:panelGroup layout="block" styleClass="cb-container" style="margin-right: 10px;">
                                <h:selectOneMenu id="situacao" styleClass="exportadores" style="margin-right: 10px;"
                                                 value="#{ColaboradorControle.situacaoFiltro}">
                                    <f:selectItems value="#{ColaboradorControle.listaSelectItemSituacao}"/>
                                    <a4j:support event="onchange" oncomplete="recarregarTabelaColaborador()"/>
                                </h:selectOneMenu>
                            </h:panelGroup>

                            <a4j:commandLink id="btnNovo"
                                             styleClass="pure-button pure-button-primary"
                                             style="padding-left: 5px;padding-right: 5px;"
                                             action="#{ColaboradorControle.novo}"
                                             accesskey="1">
                                &nbsp ${msg_bt.btn_cadastrar_novo}
                            </a4j:commandLink>
                        </h:panelGroup>

                        <h:panelGroup layout="block" style="line-height: 44px;">
                            <a4j:commandLink id="btnExcel"
                                             styleClass="exportadores"
                                             actionListener="#{ColaboradorControle.exportar}"
                                             oncomplete="abrirPopup('UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/vnd.ms-excel','Transacoes', 800,200);#{ExportadorListaControle.msgAlert}"
                                             accesskey="3">

                                <f:attribute name="tipo" value="xls"/>
                                <f:attribute name="atributos"
                                             value="codigo=Código,pessoa_Apresentar=Nome,pessoa_DataNascimento=Data de Nascimento,situacao_Apresentar=Situação,departamentoApresentar=Departamento.,tipoColaborador=Tipo,empresa_Apresentar=Empresa"/>
                                <f:attribute name="prefixo" value="Colaborador"/>
                                <a4j:actionparam noEscape="true" name="paramsTableFiltrada" value="retornaDadosDT()"/>
                                <h:outputText title="Exportar para o formato Excel" styleClass="btn-print-2 excel"/>
                            </a4j:commandLink>


                            <a4j:commandLink id="btnPDF"
                                             styleClass="exportadores margin-h-10"
                                             actionListener="#{ColaboradorControle.exportar}"
                                             oncomplete="abrirPopup('UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/pdf','Transacoes', 800,200);#{ExportadorListaControle.msgAlert}"
                                             accesskey="4">

                                <f:attribute name="tipo" value="pdf"/>
                                <f:attribute name="atributos"
                                             value="codigo=Código,pessoa_Apresentar=Nome,pessoa_DataNascimento=Data de Nascimento,situacao_Apresentar=Situação,departamentoApresentar=Departamento,tipoColaborador=Tipo,empresa_Apresentar=Empresa"/>
                                <f:attribute name="prefixo" value="Colaborador"/>
                                <f:attribute name="titulo" value="Colaboradores"/>
                                <a4j:actionparam noEscape="true" name="paramsTableFiltrada" value="retornaDadosDT()"/>
                                <h:outputText title="Exportar para o formato PDF" styleClass="btn-print-2 pdf"/>
                            </a4j:commandLink>

                            <c:if test="${modulo eq 'zillyonWeb'}">
                                <a4j:commandLink id="btnLog"
                                                 styleClass="exportadores margin-h-10"
                                                 action="#{ColaboradorControle.realizarConsultaLogObjetoGeral}"
                                                 oncomplete="abrirPopup('visualizadorLogForm.jsp', 'VisualizadorLog', 785, 595);">
                                    <h:outputText title="visualizar log geral da entidade" styleClass="btn-print-2 log"/>
                                </a4j:commandLink>
                            </c:if>
                            <c:if test="${modulo eq 'centralEventos'}">
                                <a4j:commandLink id="btnLog"
                                                 reRender="formLog"
                                                 styleClass="exportadores margin-h-10"
                                                 actionListener="#{LogControle.entidadeListener}"
                                                 oncomplete="Richfaces.showModalPanel('panelMasterLog');">
                                    <f:attribute name="nomeEntidade" value="COLABORADOR"/>
                                    <f:attribute name="funcao" value="118"/>
                                    <h:outputText title="visualizar log geral da entidade" styleClass="btn-print-2 log"/>
                                </a4j:commandLink>
                            </c:if>
                        </h:panelGroup>
                    </h:panelGroup>
                </h:panelGroup>
                <%-- FIM COMANDOS --%>

                <table id="tblColaborador" class="tabelaColaborador pure-g-r pure-u-11-12 margin-0-auto">
                    <thead>
                    <th>${msg_aplic.prt_Cadastro_label_codigo_maiusculo}</th>
                    <th>${msg_aplic.prt_Cadastro_label_nome_maiusculo}</th>
                    <th>${msg_aplic.prt_Cadastro_label_nascimento_maiusculo}</th>
                    <th>${msg_aplic.prt_Cadastro_label_situacao_maiusculo}</th>
                    <th>${msg_aplic.prt_Cadastro_label_tipo_maiusculo}</th>
                    <th>${msg_aplic.prt_Cadastro_label_empresa_maiusculo}</th>
                    </thead>
                    <tbody></tbody>
                </table>

                <a4j:jsFunction name="jsEditar" action="#{ColaboradorControle.editar}" reRender="mensagem"/>

            </h:panelGroup>
            <%-- FIM CONTENT --%>

            <%-- INICIO FOOTER --%>
            <h:panelGroup id="mensagem" layout="block" styleClass="pure-u-11-12 margin-0-auto">
                <h:graphicImage id="iconSucesso" rendered="#{ColaboradorControle.sucesso}"
                                value="./imagens/sucesso.png"/>
                <h:graphicImage id="iconErro" rendered="#{ColaboradorControle.erro}" value="./imagens/erro.png"/>

                <h:outputText styleClass="mensagem" rendered="#{not empty ColaboradorControle.mensagem}"
                              value=" #{ColaboradorControle.mensagem}"/>
                <h:outputText styleClass="mensagemDetalhada"
                              rendered="#{not empty ColaboradorControle.mensagemDetalhada}"
                              value=" #{ColaboradorControle.mensagemDetalhada}"/>

            </h:panelGroup>
            <%-- FIM FOOTER --%>
        </h:form>



        <%@include file="/pages/ce/includes/include_modal_exibeLogEntidade.jsp" %>
    </h:panelGroup>



</f:view>
<script src="beta/js/dt-server.js" type="text/javascript"></script>

<script>
    function recarregarTabelaColaborador() {
        var situacao = document.getElementById("form:situacao").value;
        var tipoColaborador = document.getElementById("form:tipoColaborador").value;
        tabelaAtual.dataTable().fnDestroy(0);
        iniTblServer("tabelaColaborador", "${contexto}/prest/basico/colaborador?situacao="+situacao+"&tipo="+tipoColaborador, null, 2, "asc", "true");
    }

    jQuery(window).on("load", function () {
        iniTblServer("tabelaColaborador", "${contexto}/prest/basico/colaborador?situacao=${ColaboradorControle.situacaoFiltroPadrao}&tipo=${ColaboradorControle.situacaoFiltroTipo}", null, 2, "asc", "true");
    });
</script>

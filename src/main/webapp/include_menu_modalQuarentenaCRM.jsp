<%@page contentType="text/html" %>
<%@page pageEncoding="ISO-8859-1" %>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax" %>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core" %>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich" %>
<%@taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="H" uri="http://java.sun.com/jsf/html" %>
<%@ taglib prefix="ui" uri="http://richfaces.org/a4j" %>

<rich:modalPanel id="modalIniciaQuarentenaCRM" styleClass="novaModal noMargin" shadowOpacity="true"
                 showWhenRendered="#{FuncionalidadeControle.funcionalidadeNome eq 'CRM_REGISTRO_PARALISACAO'}"
                 width="747" height="#{QuarentenaControle.alturaPadraoModalEncerrar}">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="Registro de Paralisação"/>
        </h:panelGroup>
    </f:facet>
    <f:facet name="controls">
        <h:panelGroup>
            <h:outputText
                    styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign"
                    id="hidelink9"/>
            <rich:componentControl
                    for="modalIniciaQuarentenaCRM"
                    attachTo="hidelink9" operation="hide" event="onclick"/>
        </h:panelGroup>
    </f:facet>
    <a4j:form ajaxSubmit="true" id="formMdlParalisacao">
        <h:panelGrid>
            <h:panelGroup layout="block" style="margin: 10px 20px 10px 20px;">
                <h:panelGrid id="panelIniciarParalisacao" style="margin-top: 2vh;" columns="1"
                             columnClasses="colunaCentralizada" width="100%">
                    <h:outputText
                            styleClass="texto-size-14 texto-bold texto-font texto-cor-cinza"
                            value="Durante o período de paralisação a meta de faltosos e grupo de risco não estarão disponíveis
                             na meta diária. Os cálculos do grupo de risco e faltosos irão ignorar esse período a fim de que,
                             quando retornar da paralisação, a meta diária não seja afetada. Caso um aluno tenha acesso
                            registrado dentro do periodo de quarentena, a mesma não surtira efeito para ele."/>
                    <rich:spacer width="5px"/>
                    <h:panelGrid id="panelMotivo" style="margin-top: 3vh;" columns="1"
                                 columnClasses="colunaCentralizada" width="100%">
                        <h:outputText rendered="#{QuarentenaControle.apresentarIniciarEncerrar}"
                                      styleClass="tituloCampos" value="Data início paralisação:"/>
                        <h:inputText id="dataInicioParalisacao"
                                     rendered="#{QuarentenaControle.apresentarIniciarEncerrar}"
                                     disabled="#{QuarentenaControle.apresentarIniciarEncerrar}"
                                     size="15"
                                     styleClass="form"
                                     value="#{QuarentenaControle.quarentenaVO.inicioQuarentena_Apresentar}"/>
                        <h:outputText rendered="#{QuarentenaControle.apresentarIniciarEncerrar}"
                                      styleClass="tituloCampos" value="Iniciou paralisação:"/>
                        <h:inputText id="iniciouParalisacao"
                                     rendered="#{QuarentenaControle.apresentarIniciarEncerrar}"
                                     disabled="#{QuarentenaControle.apresentarIniciarEncerrar}"
                                     size="40"
                                     styleClass="form"
                                     value="#{QuarentenaControle.quarentenaVO.usuarioIniciou.nome}"/>
                        <h:outputText styleClass="tituloCampos" value="Motivo:"/>
                        <h:inputText id="motivo" size="70" maxlength="100" onblur="blurinput(this);"
                                     onfocus="focusinput(this);"
                                     disabled="#{QuarentenaControle.apresentarIniciarEncerrar}"
                                     styleClass="form" value="#{QuarentenaControle.quarentenaVO.motivo}"/>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGroup>
            <h:panelGroup style="position: absolute; width: 100%; bottom: 6%; text-align: center">
                <a4j:commandLink styleClass="botoes nvoBt"
                                 id="iniciarParalisacao"
                                 action="#{QuarentenaControle.iniciarParalisacao}"
                                 rendered="#{!QuarentenaControle.apresentarIniciarEncerrar}"
                                 oncomplete="#{QuarentenaControle.mensagemNotificar}"
                                 reRender="modalIniciaQuarentenaCRM, mensagens" value="Iniciar Paralisação"/>
                <a4j:commandLink styleClass="botoes nvoBt"
                                 id="encerrarParalisacao"
                                 action="#{QuarentenaControle.encerrarParalisacao}"
                                 rendered="#{QuarentenaControle.apresentarIniciarEncerrar}"
                                 oncomplete="#{QuarentenaControle.mensagemNotificar}"
                                 reRender="modalIniciaQuarentenaCRM, mensagens" value="Encerrar Paralisação"/>
                <a4j:commandLink styleClass="botoes nvoBt btSec"
                                 id="lancarParalisacaoRetro"
                                 rendered="#{!QuarentenaControle.apresentarIniciarEncerrar}"
                                 oncomplete="Richfaces.showModalPanel(modalIniciaQuarentenaCRMRetroativa) && Richfaces.hideModalPanel(modalIniciaQuarentenaCRM)"
                                 reRender="modalIniciaQuarentenaCRM" value="Lançar Paralisação Retroativa"/>
            </h:panelGroup>
        </h:panelGrid>
    </a4j:form>
</rich:modalPanel>

<rich:modalPanel id="modalIniciaQuarentenaCRMRetroativa" styleClass="novaModal noMargin" shadowOpacity="true"
                 width="850" height="580">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="Registro de Paralisação Retroativa"/>
        </h:panelGroup>
    </f:facet>
    <f:facet name="controls">
        <h:panelGroup>
            <h:outputText
                    styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign"
                    id="hidelink10"/>
            <rich:componentControl
                    for="modalIniciaQuarentenaCRMRetroativa"
                    attachTo="hidelink10" operation="hide" event="onclick"/>
        </h:panelGroup>
    </f:facet>
    <a4j:form ajaxSubmit="true" id="formMdlParalisacaoRetroativa">
        <h:panelGrid>
            <h:panelGroup layout="block" style="margin: 10px 20px 10px 20px; height: 250px;">
                <h:panelGrid id="panelIniciarParalisacaoRetroativa" style="margin-top: 2vh;" columns="1"
                             columnClasses="colunaCentralizada" width="100%">
                    <h:outputText
                            styleClass="texto-size-14 texto-bold texto-font texto-cor-cinza"
                            value="Os cálculos do grupo de risco e faltosos irão ignorar esse período de paralisação a fim de que,
                            a meta diária não seja afetada. Sendo assim, quando acabar o período lançado aqui, o sistema continuará
                            calculando exatamente como estava um dia antes do início da paralisação. As metas de faltosos e
                            grupo de risco também serão excluídas no período informado. A data máxima para início da paralisação
                            retroativa é 16/03/2020. Esta operação afeta as fases GRUPO DE RISCO e FALTOSOS, as outras metas continuarão intactas.
                            ATENÇÃO: Esta operação não poderá ser desfeita, as metas excluídas não serão recuperadas,
                            portanto coloque a data exata que a sua academia entrou no período de quarentena, caso um aluno tenha acesso
                            registrado dentro do periodo de quarentena, a mesma não surtira efeito para ele."/>
                    <h:panelGrid id="panelMotivo2" columns="1"
                                 columnClasses="colunaCentralizada" width="100%">
                        <h:panelGroup layout="block" style="margin-top: 2%">
                            <h:outputText styleClass="tituloCampos" style="vertical-align: middle; padding: 10px;"
                                          value="Paralisação de"/>
                            <h:panelGroup styleClass="dateTimeCustom" style="font-size: 11px;">
                                <rich:calendar id="dataInicio"
                                               value="#{QuarentenaControle.quarentenaVO.inicioQuarentena}"
                                               inputSize="10"
                                               buttonIcon="/imagens_flat/calendar-button.svg"
                                               inputClass="form"
                                               oninputblur="blurinput(this);"
                                               oninputfocus="focusinput(this);"
                                               datePattern="dd/MM/yyyy"
                                               oninputchange="return validar_Data(this.id);"
                                               oninputkeypress="return mascara2(this.form, this.id, '99/99/9999', event);"
                                               enableManualInput="true"
                                               zindex="2"
                                               showWeeksBar="false"/>
                            </h:panelGroup>
                            <h:outputText styleClass="tituloCampos" style="vertical-align: middle; padding: 10px;"
                                          value="até"/>
                            <h:panelGroup styleClass="dateTimeCustom" style="font-size: 11px;">
                                <rich:calendar id="dataTerminoC"
                                               value="#{QuarentenaControle.quarentenaVO.fimQuarentena}"
                                               inputSize="10"
                                               inputClass="form"
                                               oninputblur="blurinput(this);"
                                               buttonIcon="/imagens_flat/calendar-button.svg"
                                               oninputfocus="focusinput(this);"
                                               oninputchange="return validar_Data(this.id);"
                                               oninputkeypress="return mascara2(this.form, this.id, '99/99/9999', event);"
                                               datePattern="dd/MM/yyyy"
                                               enableManualInput="true" showWeeksBar="false"
                                               zindex="2"/>
                            </h:panelGroup>
                        </h:panelGroup>

                        <h:panelGroup layout="block" style="margin-top: 6%;">
                            <h:outputText styleClass="tituloCampos" value="Motivo:"/>
                            <h:inputText id="motivo2" size="70" maxlength="100" onblur="blurinput(this);"
                                         onfocus="focusinput(this);" style="width: 100%;"
                                         styleClass="form" value="#{QuarentenaControle.quarentenaVO.motivo}"/>
                        </h:panelGroup>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGroup>

            <h:panelGroup layout="block" style="width: 100%; text-align: center; margin-top: 21%;">
                <a4j:commandLink id="paralisacaoRetroativa"
                                 styleClass="botoes nvoBt"
                                 style="font-size: 15px;"
                                 action="#{QuarentenaControle.paralisacaoRetroativa}"
                                 oncomplete="#{QuarentenaControle.mensagemNotificar}"
                                 reRender="formMdlParalisacaoRetroativa, mensagem"
                                 value="Lançar Paralisação Retroativa"/>
            </h:panelGroup>
        </h:panelGrid>
    </a4j:form>
</rich:modalPanel>

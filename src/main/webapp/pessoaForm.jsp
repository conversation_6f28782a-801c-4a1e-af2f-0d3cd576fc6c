<%@page contentType="text/html"%>
<%@page pageEncoding="ISO-8859-1"%>
<head><%@include file="/includes/include_import_minifiles.jsp" %></head>

<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<link href="beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>

<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText value="#{msg_aplic.prt_Pessoa_tituloForm}"/>
    </title>
    <c:set var="titulo" scope="session" value="${msg_aplic.prt_Pessoa_tituloForm}"/>
    <c:set var="urlWiki" scope="session" value="${SuperControle.urlBaseConhecimento}cadastro-de-pessoa/"/>
    <a4j:loadScript src="/script/jquery.maskedinput-1.2.2.js" />
    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <f:facet name="header">

            <jsp:include page="topoReduzido_material.jsp"/>

        </f:facet>

        <h:form id="form">
            <hr style="border-color: #e6e6e6;"/>
            <h:panelGrid columns="1" styleClass="tabForm" width="100%">
                <rich:tabPanel width="100%" activeTabClass="true" headerAlignment="rigth" >
                    <rich:tab label="Dados Pessoais">  

                        <h:panelGrid columns="2" styleClass="tabForm" width="100%">                
                            <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_Pessoa_codigo}" />
                            <h:panelGroup>
                                <h:inputText  id="codigo" required="true" size="10" maxlength="10" readonly="true" styleClass="camposSomenteLeitura" value="#{PessoaControle.pessoaVO.codigo}" />
                                <h:message for="codigo" styleClass="mensagemDetalhada"/>
                            </h:panelGroup>
                            <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_Pessoa_tipoPessoa}" />
                            <h:panelGroup>
                                <h:selectOneMenu  id="tipoPessoa" styleClass="camposObrigatorios" value="#{PessoaControle.pessoaVO.tipoPessoa}" >
                                    <f:selectItems  value="#{PessoaControle.listaSelectItemTipoPessoaPessoa}" />
                                </h:selectOneMenu>
                                <h:message for="tipoPessoa" styleClass="mensagemDetalhada"/>
                            </h:panelGroup>
                            <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_Pessoa_profissao}" />
                            <h:panelGroup>
                                <h:selectOneMenu  id="profissao" styleClass="campos" value="#{PessoaControle.pessoaVO.profissao.codigo}" >
                                    <f:selectItems  value="#{PessoaControle.listaSelectItemProfissao}" />
                                </h:selectOneMenu>
                                <a4j:commandButton id="atualizar_profissao" action="#{PessoaControle.montarListaSelectItemProfissao}" image="imagens/atualizar.png" immediate="true" ajaxSingle="true" reRender="form:profissao"/>
                                <h:message for="profissao" styleClass="mensagemDetalhada"/>
                            </h:panelGroup>
                            <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_Pessoa_dataCadastro}" />
                            <h:panelGroup>
                                <rich:calendar id="dataCadastro"
                                               value="#{PessoaControle.pessoaVO.dataCadastro}"
                                               inputSize="10"
                                               inputClass="form"
                                               oninputblur="blurinput(this);"
                                               oninputfocus="focusinput(this);"
                                               oninputchange="return validar_Data(this.id);"
                                               datePattern="dd/MM/yyyy"
                                               enableManualInput="true"
                                               zindex="2"
                                               showWeeksBar="false" />
                                <h:message for="dataCadastro"  styleClass="mensagemDetalhada"/>
                            </h:panelGroup>
                            <%--h:inputText  id="dataCadastro" onkeypress="return mascara(this.form, 'form:dataCadastro', '99/99/9999', event);" size="10" maxlength="10" styleClass="campos" value="#{PessoaControle.pessoaVO.dataCadastro}" >
                                <f:convertDateTime pattern="dd/MM/yyyy"/>
                            </h:inputText--%>
                            <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_Pessoa_nome}" />
                            <h:panelGroup>
                                <h:inputText  id="nome" required="true" size="50" maxlength="50" styleClass="camposObrigatorios" value="#{PessoaControle.pessoaVO.nome}" />
                                <h:message for="nome" styleClass="mensagemDetalhada"/>
                            </h:panelGroup>
                            <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_Pessoa_nome_registro}" />
                            <h:panelGroup>
                                <h:inputText  id="nomeRegistro" required="true" size="50" maxlength="50" styleClass="camposObrigatorios" value="#{PessoaControle.pessoaVO.nomeRegistro}" />
                                <h:message for="nomeRegistro" styleClass="mensagemDetalhada"/>
                            </h:panelGroup>
                            <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_Pessoa_dataNasc}" />
                            <h:panelGroup>
                                <rich:calendar id="dataNasc"
                                               value="#{PessoaControle.pessoaVO.dataNasc}"
                                               inputSize="10"
                                               inputClass="form"
                                               oninputblur="blurinput(this);"
                                               oninputfocus="focusinput(this);"
                                               oninputchange="return validar_Data(this.id);"
                                               datePattern="dd/MM/yyyy"
                                               enableManualInput="true"
                                               zindex="2"
                                               showWeeksBar="false" />
                                <h:message for="dataNasc"  styleClass="mensagemDetalhada"/>
                                <rich:jQuery id="mskData" selector=".rich-calendar-input" timing="onload" query="mask('99/99/9999')" />
                            </h:panelGroup>
                            <%--h:inputText  id="dataNasc" onkeypress="return mascara(this.form, 'form:dataNasc', '99/99/9999', event);" size="10" maxlength="10" styleClass="campos" value="#{PessoaControle.pessoaVO.dataNasc}" >
                                <f:convertDateTime pattern="dd/MM/yyyy"/>
                            </h:inputText--%>
                            <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_Pessoa_nomePai}" />
                            <h:inputText  id="nomePai" size="50" maxlength="50" styleClass="campos" value="#{PessoaControle.pessoaVO.nomePai}" />
                            <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_Pessoa_nomeMae}" />
                            <h:inputText  id="nomeMae" size="50" maxlength="50" styleClass="campos" value="#{PessoaControle.pessoaVO.nomeMae}" />
                            <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_Pessoa_cfp}" />
                            <h:panelGroup>
                                <h:inputText  id="cfp" required="true" size="14" maxlength="14" styleClass="camposObrigatorios" value="#{PessoaControle.pessoaVO.cfp}" />
                                <h:message for="cfp" styleClass="mensagemDetalhada"/>
                            </h:panelGroup>
                            <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_Pessoa_rg}" />
                            <h:inputText  id="rg" size="20" maxlength="20" styleClass="campos" value="#{PessoaControle.pessoaVO.rg}" />
                            <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_Pessoa_rgOrgao}" />
                            <h:inputText  id="rgOrgao" size="10" maxlength="10" styleClass="campos" value="#{PessoaControle.pessoaVO.rgOrgao}" />
                            <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_Pessoa_rgUf}" />
                            <h:selectOneMenu  id="rgUf" styleClass="campos" value="#{PessoaControle.pessoaVO.rgUf}" >
                                <f:selectItems  value="#{PessoaControle.listaSelectItemRgUfPessoa}" />
                            </h:selectOneMenu>
                            <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_Pessoa_cidade}" />
                            <h:panelGroup>
                                <h:selectOneMenu  id="cidade" styleClass="camposObrigatorios" value="#{PessoaControle.pessoaVO.cidade.codigo}" >
                                    <f:selectItems  value="#{PessoaControle.listaSelectItemCidade}" />
                                </h:selectOneMenu>
                                <a4j:commandButton id="atualizar_cidade" action="#{PessoaControle.montarListaSelectItemCidade}" image="imagens/atualizar.png" immediate="true" ajaxSingle="true" reRender="form:cidade"/>
                                <h:message for="cidade" styleClass="mensagemDetalhada"/>
                            </h:panelGroup>
                            <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_Pessoa_pais}" />
                            <h:panelGroup>
                                <h:selectOneMenu  id="pais" styleClass="campos" value="#{PessoaControle.pessoaVO.pais.codigo}" >
                                    <f:selectItems  value="#{PessoaControle.listaSelectItemPais}" />
                                </h:selectOneMenu>
                                <a4j:commandButton id="atualizar_pais" action="#{PessoaControle.montarListaSelectItemPais}" image="imagens/atualizar.png" immediate="true" ajaxSingle="true" reRender="form:pais"/>
                                <h:message for="pais" styleClass="mensagemDetalhada"/>
                            </h:panelGroup>
                            <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_Pessoa_estadoCivil}" />
                            <h:selectOneMenu  id="estadoCivil" styleClass="campos" value="#{PessoaControle.pessoaVO.estadoCivil}" >
                                <f:selectItems  value="#{PessoaControle.listaSelectItemEstadoCivilPessoa}" />
                            </h:selectOneMenu>
                            <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_Pessoa_nacionalidade}" />
                            <h:inputText  id="nacionalidade" size="20" maxlength="20" styleClass="campos" value="#{PessoaControle.pessoaVO.nacionalidade}" />
                            <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_Pessoa_naturalidade}" />
                            <h:inputText  id="naturalidade" size="20" maxlength="20" styleClass="campos" value="#{PessoaControle.pessoaVO.naturalidade}" />
                            <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_Pessoa_sexo}" />
                            <h:selectOneMenu  id="sexo" styleClass="campos" value="#{PessoaControle.pessoaVO.sexo}" >
                                <f:selectItems  value="#{PessoaControle.listaSelectItemSexoPessoa}" />
                            </h:selectOneMenu>
                            <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_Pessoa_genero}" />
                            <h:selectOneMenu  id="genero" styleClass="campos" value="#{PessoaControle.pessoaVO.genero}" >
                                <f:selectItems  value="#{PessoaControle.listaSelectItemGeneroPessoa}" />
                            </h:selectOneMenu>
                            <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_Pessoa_email}" />
                            <h:inputText  id="email" size="50" maxlength="50" styleClass="campos" value="#{PessoaControle.pessoaVO.email}" />
                        </h:panelGrid>
                    </rich:tab>                                                 
                    <rich:tab label="Endereço">
                        <h:panelGrid columns="1" width="100%" headerClass="subordinado" columnClasses="colunaCentralizada">
                            <f:facet name="header">
                                <h:outputText value="#{msg_aplic.prt_Endereco_tituloForm}"/>
                            </f:facet>
                            <h:panelGrid columns="2" width="100%" styleClass="tabFormSubordinada" footerClass="colunaCentralizada">
                                <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_Endereco_codigo}" />
                                <h:inputText  size="10" maxlength="10" readonly="true" styleClass="camposSomenteLeitura" value="#{PessoaControle.enderecoVO.codigo}" />
                                <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_Endereco_endereco}" />
                                <h:inputText  size="40" maxlength="40" styleClass="campos" value="#{PessoaControle.enderecoVO.endereco}" />
                                <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_Endereco_complemento}" />
                                <h:inputText  size="40" maxlength="40" styleClass="campos" value="#{PessoaControle.enderecoVO.complemento}" />
                                <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_Endereco_numero}" />
                                <h:inputText  size="10" maxlength="10" styleClass="campos" value="#{PessoaControle.enderecoVO.numero}" />
                                <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_Endereco_bairro}" />
                                <h:inputText  size="35" maxlength="35" styleClass="campos" value="#{PessoaControle.enderecoVO.bairro}" />
                                <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_Endereco_cep}" />
                                <h:inputText  size="10" maxlength="10" styleClass="campos" value="#{PessoaControle.enderecoVO.cep}" />
                                <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_Endereco_tipoEndereco}" />
                                <h:selectOneMenu  id="Endereco_tipoEndereco" styleClass="camposObrigatorios" value="#{PessoaControle.enderecoVO.tipoEndereco}" >
                                    <f:selectItems  value="#{PessoaControle.listaSelectItemTipoEnderecoEndereco}" />
                                </h:selectOneMenu>
                            </h:panelGrid>
                            <h:commandButton action="#{PessoaControle.adicionarEndereco}" value="#{msg_bt.btn_adicionar}" image= "./imagens/botaoAdicionar.png" accesskey="5" styleClass="botoes"/>
                        </h:panelGrid>
                        <h:panelGrid columns="1" width="100%" styleClass="tabFormSubordinada">
                            <h:dataTable id="enderecoVO" width="100%" headerClass="subordinado" styleClass="tabFormSubordinada"
                                         rowClasses="linhaImparSubordinado, linhaParSubordinado" columnClasses="colunaAlinhamento"
                                         value="#{PessoaControle.pessoaVO.enderecoVOs}" var="endereco">
                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText  value="#{msg_aplic.prt_Endereco_codigo}" />
                                    </f:facet>
                                    <h:outputText  value="#{endereco.codigo}" />
                                </h:column>
                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText  value="#{msg_aplic.prt_Endereco_endereco}" />
                                    </f:facet>
                                    <h:outputText  value="#{endereco.endereco}" />
                                </h:column>
                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText  value="#{msg_aplic.prt_Endereco_complemento}" />
                                    </f:facet>
                                    <h:outputText  value="#{endereco.complemento}" />
                                </h:column>
                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText  value="#{msg_aplic.prt_Endereco_numero}" />
                                    </f:facet>
                                    <h:outputText  value="#{endereco.numero}" />
                                </h:column>
                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText  value="#{msg_aplic.prt_Endereco_bairro}" />
                                    </f:facet>
                                    <h:outputText  value="#{endereco.bairro}" />
                                </h:column>
                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText  value="#{msg_aplic.prt_Endereco_cep}" />
                                    </f:facet>
                                    <h:outputText  value="#{endereco.cep}" />
                                </h:column>
                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText  value="#{msg_aplic.prt_Endereco_tipoEndereco}" />
                                    </f:facet>
                                    <h:outputText  value="#{endereco.tipoEndereco_Apresentar}" />
                                </h:column>
                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText  value="#{msg_bt.btn_opcoes}" />
                                    </f:facet>
                                    <h:panelGroup>
                                        <h:commandButton id="editarItemVenda" immediate="true" action="#{PessoaControle.editarEndereco}" value="#{msg_bt.btn_editar}" image="./imagens/botaoEditar.png" accesskey="6" styleClass="botoes"/>

                                        <h:outputText value="    "/>

                                        <h:commandButton id="removerItemVenda" immediate="true" action="#{PessoaControle.removerEndereco}" value="#{msg_bt.btn_excluir}" image="./imagens/botaoRemover.png" accesskey="7" styleClass="botoes"/>
                                    </h:panelGroup>
                                </h:column>
                            </h:dataTable>
                        </h:panelGrid>
                    </rich:tab>                                                 
                    <rich:tab label="Telefone">  
                        <h:panelGrid columns="1" width="100%" headerClass="subordinado" columnClasses="colunaCentralizada">
                            <f:facet name="header">
                                <h:outputText value="#{msg_aplic.prt_Telefone_tituloForm}"/>
                            </f:facet>
                            <h:panelGrid columns="2" width="100%" styleClass="tabFormSubordinada" footerClass="colunaCentralizada">
                                <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_Telefone_numero}" />
                                <h:inputText  size="15" maxlength="15" styleClass="camposObrigatorios" value="#{PessoaControle.telefoneVO.numero}" />
                                <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_Telefone_tipoTelefone}" />
                                <h:selectOneMenu  id="Telefone_tipoTelefone" styleClass="camposObrigatorios" value="#{PessoaControle.telefoneVO.tipoTelefone}" >
                                    <f:selectItems  value="#{PessoaControle.listaSelectItemTipoTelefoneTelefone}" />
                                </h:selectOneMenu>
                            </h:panelGrid>
                            <h:commandButton action="#{PessoaControle.adicionarTelefone}" value="#{msg_bt.btn_adicionar}" image= "./imagens/botaoAdicionar.png" accesskey="6" styleClass="botoes"/>
                        </h:panelGrid>
                        <h:panelGrid columns="1" width="100%" styleClass="tabFormSubordinada">
                            <h:dataTable id="telefoneVO" width="100%" headerClass="subordinado" styleClass="tabFormSubordinada"
                                         rowClasses="linhaImparSubordinado, linhaParSubordinado" columnClasses="colunaAlinhamento"
                                         value="#{PessoaControle.pessoaVO.telefoneVOs}" var="telefone">
                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText  value="#{msg_aplic.prt_Telefone_numero}" />
                                    </f:facet>
                                    <h:outputText  value="#{telefone.numero}" />
                                </h:column>
                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText  value="#{msg_aplic.prt_Telefone_tipoTelefone}" />
                                    </f:facet>
                                    <h:outputText  value="#{telefone.tipoTelefone_Apresentar}" />
                                </h:column>
                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText  value="#{msg_bt.btn_opcoes}" />
                                    </f:facet>
                                    <h:panelGroup>
                                        <h:commandButton id="editarItemVenda" immediate="true" action="#{PessoaControle.editarTelefone}" value="#{msg_bt.btn_editar}" accesskey="6" styleClass="botoes nvoBt"/>

                                        <h:outputText value="    "/>

                                        <h:commandButton id="removerItemVenda" immediate="true" action="#{PessoaControle.removerTelefone}" value="#{msg_bt.btn_excluir}" accesskey="7"  styleClass="botoes nvoBt btSec btPerigo"/>
                                    </h:panelGroup>
                                </h:column>
                            </h:dataTable>
                        </h:panelGrid>
                    </rich:tab>   
                </rich:tabPanel>
                <h:panelGrid columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid columns="1" width="100%">
                        <h:outputText styleClass="mensagem"  value="#{PessoaControle.mensagem}"/>
                        <h:outputText styleClass="mensagemDetalhada" value="#{PessoaControle.mensagemDetalhada}"/>
                    </h:panelGrid>
                    <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                        <h:panelGroup>
                            <a4j:commandButton id="novo" immediate="true" action="#{PessoaControle.novo}" value="#{msg_bt.btn_novo}" image="./imagens/botaoNovo.png" alt="#{msg.msg_novo_dados}" accesskey="1" styleClass="botoes nvoBt btSec"/>

                            <h:outputText value="    "/>

                            <a4j:commandButton id="salvar" action="#{PessoaControle.gravar}" value="#{msg_bt.btn_gravar}" alt="#{msg.msg_gravar_dados}" accesskey="2" styleClass="botoes nvoBt"/>

                            <h:outputText value="    "/>

                            <a4j:commandButton id="excluir" onclick="if(!confirm('Confirma exclusão dos dados?')){return true;}" action="#{PessoaControle.excluir}" value="#{msg_bt.btn_excluir}"  alt="#{msg.msg_excluir_dados}" accesskey="3"  styleClass="botoes nvoBt btSec btPerigo"/>

                            <h:outputText value="    "/>

                            <a4j:commandButton id="consultar" immediate="true" action="#{PessoaControle.inicializarConsultar}" value="#{msg_bt.btn_voltar_lista}" alt="#{msg.msg_consultar_dados}" accesskey="4"  styleClass="botoes nvoBt btSec"/>
                        </h:panelGroup>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
        </h:form>        
    </h:panelGrid>
</f:view>
<script>
    document.getElementById("form:tipoPessoa").focus();
</script>

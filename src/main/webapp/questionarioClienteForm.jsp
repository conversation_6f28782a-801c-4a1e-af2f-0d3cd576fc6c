<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@page contentType="text/html"%>
<%@page pageEncoding="ISO-8859-1"%>
<head>
    <%@include file="/includes/include_import_minifiles.jsp" %>
    <link href="beta/css/font-awesome_2.0.min.css" rel="stylesheet" type="text/css">
    <script type="text/javascript" language="javascript" src="script/negociacaoContrato_1.0.min.js"></script>
</head>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<link href="./css/pacto_flat_2.16.min.css" rel="stylesheet" type="text/css">
<link href="./beta/css/pure-ext.css" type="text/css" rel="stylesheet"/>
<link href="./beta/css/pure-min.css" type="text/css" rel="stylesheet"/>
<script type="text/javascript" language="javascript" src="hoverform.js"></script>
<script type="text/javascript" language="javascript">
     setTimeout(setInterval(function() { getDocumentCookie('popupsImportante') == 'close' ? this.close() : ''; }, 500), 500);
</script>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>
<style>
    .fa-icon-check:before,.fa-icon-check-empty:before {
        padding-right: 1px;
    }
</style>
<f:view> 
    <%@include file="includes/include_carregando_ripple.jsp" %>
    <title>
        <h:outputText value="#{msg_aplic.prt_QuestionarioCliente_tituloForm}"/>
    </title>
    <h:form id="form">
        <jsp:include page="include_head.jsp" flush="true" />
        <h:panelGroup layout="block" style="display: inline-table;width: 100%;height: 100vh" styleClass="fundoBranco">
            <c:set var="titulo" scope="session" value="Questionário"/>
            <c:set var="urlWiki" scope="session" value="${SuperControle.urlBaseConhecimento}tela-do-cliente-modulo-adm/"/>
            <h:panelGroup layout="block" styleClass="pure-g-r">
                <f:facet name="header">
                    <jsp:include page="topo_reduzido_popUp.jsp"/>
                </f:facet>
            </h:panelGroup>
            <h:panelGroup layout="block" styleClass="caixaCorpo">
                <h:panelGroup layout="block" style="height: 80%;width: 100%">
                    <h:panelGroup layout="block" styleClass="caixaMenuLatel">
                        <h:panelGroup layout="block" >
                            <h:panelGroup layout="block" styleClass="margin-box">
                                <h:panelGroup layout="block" styleClass="container-box-header bg-cinza">
                                    <h:panelGroup layout="block" styleClass="margin-container col-text-align-right" style="width: 96%;margin-right: 2%;float: right;">

                                        <a4j:commandLink id="gravarBVTop"
                                                         action="#{QuestionarioClienteControle.validarDadosAlteracao}"
                                                         value="#{msg_bt.btn_gravar}"
                                                         title="#{msg.msg_gravar_dados}"
                                                         reRender="mdlAvisoTrocarConsultor"
                                                         oncomplete="Richfaces.showModalPanel('mdlAvisoTrocarConsultor')"
                                                         accesskey="2" styleClass="botaoPrimario texto-size-16"/>
                                    </h:panelGroup>
                                </h:panelGroup>
                                <table width="100%" align="left" height="100%" border="0" cellpadding="0" cellspacing="0" style="margin:10px 0px;">
                                    <tr>
                                        <td align="center" valign="top" width="40%"  style="padding:10px;" bgcolor="#e6e6e6">
                                            <table width="100%" border="0" cellspacing="0" cellpadding="0">
                                                <tr>
                                                    <td align="left" valign="top">
                                                        <table width="100%" border="0" cellpadding="0" cellspacing="0" class="paginaFontResponsiva">
                                                            <tr>
                                                                <td align="center" valign="top" style="padding-bottom:15px;">
                                                                    <a4j:outputPanel id="panelFoto">
                                                                        <a4j:mediaOutput element="img" id="imagem1"    style="border-radius: 50%;max-width: 160px"  cacheable="false" session="true"
                                                                                         rendered="#{!SuperControle.fotosNaNuvem}"
                                                                                         createContent="#{ClienteControle.paintFoto}"  value="#{ImagemData}" mimeType="image/jpeg" >
                                                                            <f:param value="#{SuperControle.timeStamp}" name="time"/>
                                                                        </a4j:mediaOutput>
                                                                        <h:graphicImage rendered="#{SuperControle.fotosNaNuvem}"
                                                                                        style="border-radius: 50%;max-width: 160px"
                                                                                        url="#{ClienteControle.paintFotoDaNuvem}">
                                                                        </h:graphicImage>
                                                                    </a4j:outputPanel>

                                                                    <h:outputText style="display: block" styleClass="texto-size-16 texto-bold texto-font texto-cor-cinza padding-11" value="#{ClienteControle.pessoaVO.nome}" />

                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <td align="left" valign="top">
                                                                    <table width="100%" border="0" cellspacing="0" cellpadding="2" class="paginaFontResponsiva">
                                                                        <tr>
                                                                            <td align="left" valign="middle">
                                                                                <h:panelGroup layout="block" styleClass="pull-left">
                                                                                    <h:outputText
                                                                                            styleClass="rotuloCampos margenVertical"
                                                                                            style="display: block;"
                                                                                            value="#{msg_aplic.prt_QuestionarioCliente_consultor_maiusculo}"/>
                                                                                    <h:panelGroup id="consultor" layout="block" style="float: left;">

                                                                                        <c:choose>
                                                                                            <c:when test="${QuestionarioClienteControle.usuarioLogado.administrador or QuestionarioClienteControle.apresentarBotaoAlterarConsultorBV}">
                                                                                                <h:panelGroup layout="block" styleClass="cb-container margenVertical" style="width: 100%;">
                                                                                                    <h:selectOneMenu  onblur="blurinput(this);"  onfocus="focusinput(this);"   styleClass="form"  value="#{QuestionarioClienteControle.questionarioClienteVO.consultor.codigo}" >
                                                                                                        <f:selectItems  value="#{QuestionarioClienteControle.listaSelectConsultor}" />
                                                                                                    </h:selectOneMenu>
                                                                                                </h:panelGroup>
                                                                                            </c:when>
                                                                                            <c:otherwise>
                                                                                                <h:outputText style="color:gray;float: left;" value="#{QuestionarioClienteControle.questionarioClienteVO.consultor.pessoa.nome}"/>
                                                                                            </c:otherwise>
                                                                                        </c:choose>

                                                                                    </h:panelGroup>
                                                                                    <a4j:commandLink id="atualizar_consultor"
                                                                                                     rendered="#{QuestionarioClienteControle.apresentarBotaoAtualizar and !QuestionarioClienteControle.apresentarBotaoAlterarConsultorBV}"
                                                                                                     action="#{QuestionarioClienteControle.montarListaSelectItemConsultor}"
                                                                                                     styleClass="linkPadrao col-text-align-right margenVertical"
                                                                                                     style="float: right;margin-left: 4px;margin-top: -1;"
                                                                                                     immediate="true"
                                                                                                     ajaxSingle="true" reRender="form:consultor">
                                                                                        <h:outputText styleClass="fa-icon-refresh texto-cor-azul texto-size-16"/>
                                                                                    </a4j:commandLink>
                                                                                    <a4j:commandLink id="atualizar_consultor1"
                                                                                                     rendered="#{QuestionarioClienteControle.apresentarBotaoAtualizar and QuestionarioClienteControle.apresentarBotaoAlterarConsultorBV}"
                                                                                                     action="#{QuestionarioClienteControle.montarListaSelectItemConsultor}"
                                                                                                     styleClass="linkPadrao col-text-align-right margenVertical"
                                                                                                     immediate="true"
                                                                                                     ajaxSingle="true" reRender="form:consultor">
                                                                                        <h:outputText styleClass="fa-icon-refresh texto-cor-azul texto-size-16" style="margin-top: 12px;"/>
                                                                                    </a4j:commandLink>
                                                                                </h:panelGroup>
                                                                            </td>
                                                                        </tr>
                                                                        <tr>
                                                                            <td align="left" valign="middle">
                                                                                <h:panelGroup layout="block"
                                                                                              styleClass="pull-left">
                                                                                    <h:outputText
                                                                                            styleClass="rotuloCampos margenVertical"
                                                                                            style="display: block;"
                                                                                            value="#{msg_aplic.prt_QuestionarioCliente_data_maiusculo}"/>
                                                                                    <h:outputText
                                                                                            value="#{QuestionarioClienteControle.questionarioClienteVO.data}" styleClass="texto-font texto-size-16 texto-cor-cinza">
                                                                                        <f:convertDateTime
                                                                                                pattern="dd/MM/yyyy"/>
                                                                                    </h:outputText>
                                                                                </h:panelGroup>
                                                                            </td>
                                                                        </tr>
                                                                        <tr>
                                                                            <td align="left" valign="middle">
                                                                                <h:panelGroup layout="block" styleClass="pull-left">
                                                                                    <h:outputText styleClass="rotuloCampos margenVertical" value="Evento:" />
                                                                                    <h:panelGroup
                                                                                            layout="block"
                                                                                            styleClass="cb-container margenVertical" style="width: 100%">
                                                                                        <h:selectOneMenu
                                                                                                id="evento"
                                                                                                onblur="blurinput(this);"
                                                                                                onfocus="focusinput(this);"
                                                                                                styleClass="form"
                                                                                                value="#{QuestionarioClienteControle.questionarioClienteVO.eventoVO.codigo}">
                                                                                            <f:selectItems
                                                                                                    value="#{QuestionarioClienteControle.listaSelectEvento}"/>
                                                                                        </h:selectOneMenu>
                                                                                    </h:panelGroup>
                                                                                </h:panelGroup>
                                                                            </td>
                                                                        </tr>
                                                                        <tr>
                                                                            <td colspan="2" align="left" valign="middle">
                                                                                <h:panelGroup layout="block" style="width: 100%;" styleClass="pull-left">
                                                                                    <h:outputText  style="display: block" styleClass="rotuloCampos margenVertical texto-upper" value="#{msg_aplic.prt_Observacao}" />
                                                                                    <h:inputTextarea  id="textarea" styleClass="inputTextClean noTransitions"
                                                                                                      value="#{QuestionarioClienteControle.questionarioClienteVO.observacao}"
                                                                                                      cols="3" rows="50"
                                                                                                      style="width:100%;height:90px;margin-bottom:15px;"
                                                                                                      onblur="blurinput(this);"
                                                                                                      onfocus="focusinput(this);"/>
                                                                                </h:panelGroup>
                                                                            </td>
                                                                        </tr>
                                                                        <tr>
                                                                            <td colspan="2" align="center" valign="middle">
                                                                                <h:dataTable id="listaQuestionarioRespondidoPeloCliente" styleClass="tabelaSimplesCustom"
                                                                                             value="#{QuestionarioClienteControle.listaSelectQuestionario}" var="questionario"  width="100%">
                                                                                    <rich:column styleClass="col-text-align-left" headerClass="col-text-align-left">
                                                                                        <f:facet name="header">
                                                                                            <h:outputText styleClass="rotuloCampos" style="margin-left: 10px" value="Nome Questionário"/>
                                                                                        </f:facet>
                                                                                        <a4j:commandLink
                                                                                                reRender="nomeQuestionario,questionarioCliente, consultor, data, textarea, listaQuestionarioRespondidoPeloCliente,panelBotoes,tituloBV,evento"
                                                                                                action="#{QuestionarioClienteControle.selecionarQuestionarioSerRespondidoPeloCliente}"
                                                                                                styleClass="texto-font texto-size-16 texto-cor-azul"
                                                                                                id="descricaoQuestionario" value="#{questionario.questionario.nomeInterno}"/>
                                                                                    </rich:column>
                                                                                    <rich:column styleClass="col-text-align-left" headerClass="col-text-align-left">
                                                                                        <f:facet name="header">
                                                                                            <h:outputText styleClass="rotuloCampos" value="Data de Cadastro"/>
                                                                                        </f:facet>
                                                                                        <a4j:commandLink reRender="nomeQuestionario,questionarioCliente, consultor, data, textarea, listaQuestionarioRespondidoPeloCliente,panelBotoes,tituloBV,evento"
                                                                                                         styleClass="texto-font texto-size-16 texto-cor-azul"
                                                                                                         action="#{QuestionarioClienteControle.selecionarQuestionarioSerRespondidoPeloCliente}"
                                                                                                         id="dataQuestionario" value="#{questionario.data_Apresentar}"/>
                                                                                    </rich:column>
                                                                                </h:dataTable>
                                                                            </td>
                                                                        </tr>
                                                                    </table>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>

                                                </tr>
                                            </table>
                                            <!-- fim box -->
                                        </td>
                                         <rich:jQuery query="text();"/>
                                        <td width="60%" align="left" valign="top" style="padding:10px;">
                                            <h:outputText id="nomeQuestionario" styleClass="texto-font texto-bold texto-size-14 texto-cor-cinza" value="#{QuestionarioClienteControle.questionarioClienteVO.questionario.nomeInterno} - #{QuestionarioClienteControle.questionarioClienteVO.data_Apresentar}"></h:outputText>
                                                <rich:dataTable id="questionarioCliente" styleClass="tabelaSimplesCustom noHover semBorda"  value="#{QuestionarioClienteControle.questionarioClienteVO.questionarioPerguntaClienteVOs}" var="pergunta"  width="100%">
                                                    <rich:column>
                                                        <h:panelGroup layout="block" styleClass="margenVertical" style="border-bottom: 1px solid #9E9E9E;height: 26px;line-height: 26px;width: 96%;margin-left: 0px">
                                                            <h:outputText styleClass="rotuloCampos textoImcompleto tooltipster" style="display:inline-block;max-width: 94%;" title="#{pergunta.perguntaCliente.descricao}" value="#{pergunta.perguntaCliente.descricao}" />
                                                        </h:panelGroup>
                                                        <h:dataTable id="tabQuestionario" style="cursor: pointer"  value="#{pergunta.perguntaCliente.respostaPergClienteVOs}" var="repostaPergCliente"  width="90%" >
                                                            <h:column>
                                                                <h:panelGroup layout="block"   rendered="#{pergunta.perguntaCliente.multipla}" style="display: inline-block;" styleClass="chk-fa-container">
                                                                    <div onclick="marcarCheckBox(this,event);" class="font-size-em">
                                                                        <h:outputText id="idPerguntaClienteMultipla" style="font-family: Arial" styleClass="linkPadrao  texto-cor-azul texto-font texto-size-16 #{repostaPergCliente.respostaOpcao ? 'fa-icon-check' : 'fa-icon-check-empty'}" value="#{repostaPergCliente.descricaoRespota}"></h:outputText>
                                                                        <h:selectBooleanCheckbox value="#{repostaPergCliente.respostaOpcao}" style="display: none;"/>
                                                                    </div>
                                                                </h:panelGroup>

                                                                <h:inputTextarea id="idPerguntaClienteTextual" styleClass="inputTextClean noTransitions"
                                                                                 value="#{repostaPergCliente.descricaoRespota}"
                                                                                 rendered="#{pergunta.perguntaCliente.textual}"
                                                                                 rows="3" cols="43"
                                                                                 style="width:100%"
                                                                                 onblur="blurinput(this);"
                                                                                 onfocus="focusinput(this);"/>

                                                                <h:panelGroup layout="block" rendered="#{pergunta.perguntaCliente.simples}" style="display: inline-block" styleClass="chk-fa-container">
                                                                    <div onclick="marcarRadio(this, event);"  class="font-size-em">
                                                                        <h:outputText id="idPerguntaClienteSimples" style="font-family: Arial" styleClass="linkPadrao  texto-cor-azul texto-font texto-size-16 #{repostaPergCliente.respostaOpcao ? 'fa-icon-check' : 'fa-icon-check-empty'}" value="#{repostaPergCliente.descricaoRespota}"></h:outputText>
                                                                        <h:selectBooleanCheckbox value="#{repostaPergCliente.respostaOpcao}" style="display: none;"/>
                                                                    </div>
                                                                </h:panelGroup>

                                                            </h:column>
                                                        </h:dataTable>
                                                    </rich:column>
                                                </rich:dataTable>

                                        </td>
                                    </tr>
                                </table>
                                <h:panelGroup id="panelBotoes" layout="block" styleClass="paginaFontResponsiva">
                                    <h:panelGroup rendered="#{QuestionarioClienteControle.apresentarBotoes}" styleClass="container-botoes bg-cinza" style="text-align: right;" >
                                        <a4j:commandLink id="gravarBV"
                                                         action="#{QuestionarioClienteControle.validarDadosAlteracao}"
                                                         value="#{msg_bt.btn_gravar}"
                                                         style="margin-right: 2%"
                                                         reRender="mdlAvisoTrocarConsultor"
                                                         oncomplete="Richfaces.showModalPanel('mdlAvisoTrocarConsultor')"
                                                         title="#{msg.msg_gravar_dados}"
                                                         accesskey="2" styleClass="botaoPrimario texto-size-16"/>
                                    </h:panelGroup>
                                </h:panelGroup>
                            </h:panelGroup>
                        </h:panelGroup>
                    </h:panelGroup>

                </h:panelGroup>
            </h:panelGroup>
        </h:panelGroup>
    </h:form>
    <rich:modalPanel id="mdlAvisoTrocarConsultor" styleClass="novaModal" autosized="true" shadowOpacity="true" width="450" height="250">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Alteração de consultor do BV"/>
            </h:panelGroup>
        </f:facet>
        <a4j:form id="formAviso">
            <h:panelGrid columns="1" columnClasses="colunaCentralizada" width="100%">
                <h:outputText value="A operação de troca de consultor do BV também irá alterar o consultor dos contratos que estão associados a esse BV, caso o aluno possua algum contrato." style="color: #777;font-size: 16px;font-family: Arial;" rendered="#{QuestionarioClienteControle.apresentarTextoQuestionario}"/>
                <h:outputText value="Deseja Prosseguir ?" style="color: #777;font-size: 16px;font-family: Arial;"/>
                <h:panelGroup layout="block">
                    <a4j:commandLink  action="#{QuestionarioClienteControle.gravarComConfiguracao}" 
                                       id="confirmacaoCadastroQuestionario"
                                       reRender="form,mdlAvisoExcluirBrinde"
                                       accesskey="2"
                                       styleClass="pure-button pure-button-primary"
                                       style="margin-top: 16px;"
                                       oncomplete="#{QuestionarioClienteControle.mensagemNotificar};Richfaces.hideModalPanel('mdlAvisoTrocarConsultor');">
                        Sim
                    </a4j:commandLink>
                    <a4j:commandLink value="Não" status="false" onclick="Richfaces.hideModalPanel('mdlAvisoTrocarConsultor');" reRender="mdlAvisoTrocarConsultor" id="confirmacaoOpercaoNao" styleClass="pure-button"/>
                </h:panelGroup>
            </h:panelGrid>
        </a4j:form>
    </rich:modalPanel>   
</f:view>

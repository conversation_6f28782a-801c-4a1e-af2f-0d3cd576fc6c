<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@page contentType="text/html" %>
<%@page pageEncoding="ISO-8859-1" %>
<head>
    <%@include file="/includes/include_import_minifiles.jsp" %>
</head>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<link href="./beta/css/font-awesome.css" rel="stylesheet" type="text/css">
<script type="text/javascript" language="javascript" src="hoverform.js"></script>
<script type="text/javascript" language="javascript" src="script/gobackblock.js"></script>
<script type="text/javascript" language="javascript" src="script/chart_amcharts.js"></script>
<script src="script/chart_pie.js" type="text/javascript"></script>
<script src="script/chart_light.js" type="text/javascript"></script>
<script src="script/chart_serial.js" type="text/javascript"></script>
<script type="text/javascript" language="javascript" src="bootstrap/jquery.js"></script>
<script type="text/javascript" src="script/tooltipster/jquery.tooltipster.min.js"></script>

<script type="text/javascript">
    setDocumentCookie('popupsImportante', 'close', 1);
</script>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core" %>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax" %>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich" %>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>
<f:loadBundle var="CElabels" basename="propriedades.CElabels"/>
<f:loadBundle var="Mensagens" basename="propriedades.Mensagens"/>
<%
    pageContext.setAttribute("modulo", "zillyonWeb");
%>

<c:set var="root" value="${pageContext.request.contextPath}" scope="request"/>

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText value="Canal do Cliente"/>
    </title>
    <h:form id="form">
        <input type="hidden" value="${modulo}" name="modulo"/>
        <html>
        <jsp:include page="include_head.jsp" flush="true"/>

        <h:panelGroup layout="block" styleClass="fundoCinza">
            <h:panelGroup layout="block" styleClass="bgtop topoZW" rendered="#{MenuControle.apresentarTopo}">
                <jsp:include page="include_topo_novo.jsp" flush="true"/>
                <c:if test="${SuperControle.menuZwUi}">
                    <style>
                        .caixaCorpo{
                            width: calc(100vw - 266px);
                            margin-left: 266px;
                            margin-top: 15px;
                            overflow: auto;
                        }
                        .caixaCorpo.fechado-zwui{
                            width: calc(100vw - 84px);
                            margin-left: 84px;
                        }
                    </style>
                    <jsp:include page="include_box_menulateral.jsp" flush="true">
                        <jsp:param name="menu" value="ADM-INICIO" />
                    </jsp:include>
                </c:if>
                <jsp:include page="include_menu_canal_flat.jsp" flush="true"/>

            </h:panelGroup>

            <h:panelGroup layout="block" id="panelPanalCliente" styleClass="caixaCorpo zw_ui" style="height: 100%">
                <h:panelGroup layout="block" style="height: inherit;width: 100%">
                    <h:inputHidden id="url" value="#{CanalPactoControle.url}"/>
                    <embed src="${CanalPactoControle.urlIncludeCanalCliente}" style="height: inherit; width: 100%; overflow: hidden;"/>
                </h:panelGroup>
            </h:panelGroup>

            <jsp:include page="include_rodape_flat.jsp" flush="true"/>
        </h:panelGroup>
        </body>
        <script>
            jQuery('#form\\:url').val(window.location.origin)
        </script>
        </html>
    </h:form>
    <jsp:include page="includes/include_panelMensagem_goBackBlock.jsp"/>
</f:view>
<c:if test="${SuperControle.menuZwUi}">
    <script>
        fecharMenu();
    </script>
</c:if>

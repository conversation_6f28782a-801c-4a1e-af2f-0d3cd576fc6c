<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@page contentType="text/html" %>
<%@page pageEncoding="ISO-8859-1" %>

<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<link href="./css_bi_1.4.css" rel="stylesheet" type="text/css"/>

<%@include file="/includes/imports.jsp" %>
<head>

    <jsp:include page="include_head.jsp" flush="true"/>
    <script type="text/javascript" language="javascript" src="/script/script.js"></script>
    <link href="../beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
    <link href="../css/telaCliente.css" rel="stylesheet" type="text/css"/>
    <link href="../css/pacto_flat_2.16.min.css" rel="stylesheet" type="text/css"/>
    <link href="../beta/css/pure-min.css" type="text/css" rel="stylesheet"/>
    <link href="../beta/css/bootstrap-tabs.css" type="text/css" rel="stylesheet"/>
    <link href="../beta/css/pure-ext.css" type="text/css" rel="stylesheet"/>

    <script src="script/packJQueryPlugins.min.js" type="text/javascript"></script>
    <link href="css/gestaoRecebiveis1.0.min.css" rel="stylesheet" type="text/css">
</head>

<style type="text/css">
    .textoCarregando {
        margin-left: 0px !important;
    }

    .rich-calendar-exterior {
        left: -120px !important;
    }

    .rich-calendar-exterior:before {
        left: 120px !important;
    }

    .rich-calendar-exterior:after {
        left: 120px !important;
    }

    .gr-totalizador {
        width: calc(100% / 3 - 1px);
        height: 280px;
        font-size: 23px;
    }

    .configs {
        position: absolute;
        top: 15px;
        font-size: 25px;
        right: 15px;
    }

    .rich-color-picker-icon {
        width: 40px !important;
        height: 40px !important;
    }

    .novaModal .rich-color-picker-colors-input {
        font-size: 12px !important;
    }

    .gr-totalizador-text, .gr-totalizador-text:hover, .gr-totalizador-text:visited {
        display: block;
        margin-top: 41px;
        font-size: 4em;
    }

    .dadosvendas {
        display: grid;
        margin: 20px;
    }

    .dadosvendas h4.cinza {
        font-weight: normal;
    }

    .dadosvendas .row {
        margin: 0;
        margin-top: 20px;
    }

    .linkvendas a {
        font-size: 17px;

    }

    .linkvendas {
        width: 80%;
        position: relative;
        padding: 10px;
        border: 1px solid #ddd !important;
        background-color: #fff !important;
        border-radius: 3px;
        color: #b4b4b4 !important;
        margin-bottom: 20px;
    }

    .caixactrlc a {
        margin-top: 10px;
        display: block;
        margin-left: 13px;
    }

    .caixactrlc {
        background-color: #ddd;
        width: 40px;
        height: 40px;
        position: absolute;
        top: 0;
        font-size: 16px;
        right: 0;
    }

    .grid-totalizadores > tbody > tr > td {
        vertical-align: top;
        width: 48% !important;
        padding: 10px;
    }
    .grid-container {
        grid-column-gap: 5px;
        grid-row-gap: 5px;
    }


</style>

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText value="Gestão de Vendas Online"/>
    </title>
    <h:form id="form">
        <a4j:keepAlive beanName="ClubeVantagensControle"/>
        <html>
        <script>
            carregarTooltipster();

            function copiar(copyText) {
                var el = document.createElement('textarea');
                el.value = copyText;
                document.body.appendChild(el);
                el.select();
                document.execCommand('copy');
                document.body.removeChild(el);
                Notifier.info('Link copiado para a área de transferência.');
            }

            var chart = AmCharts.makeChart("chartdiv", {
                "type": "serial",
                "theme": "light",
                "marginRight": 40,
                "dataProvider": ${GestaoVendasOnlineControle.grafico},
                "valueAxes": [{
                    "axisAlpha": 0,
                    "position": "left"
                }],
                "startDuration": 1,
                "graphs": [{
                    "balloonText": "<b>[[description]]: [[value]]</b>",
                    "fillAlphas": 0.9,
                    "lineAlpha": 0.2,
                    "colorField": "cor",
                    "descriptionField": "plano",
                    "type": "column",
                    "valueField": "valor"
                }],
                "chartCursor": {
                    "categoryBalloonEnabled": false,
                    "cursorAlpha": 0,
                    "zoomable": false
                },
                "categoryField": "sigla",
                "categoryAxis": {
                    "gridPosition": "start",
                    "labelRotation": 0
                },
                "export": {
                    "enabled": true
                }

            });

        </script>
        <body>
        <a4j:loadScript src="/script/jquery.maskedinput-1.2.2.js"/>
        <h:panelGroup layout="block" style="display: inline-table;width: 100%;" styleClass="fundoCinza">
            <h:panelGroup layout="block" styleClass="bgtop topoZW" rendered="#{MenuControle.apresentarTopo}">
                <jsp:include page="include_topo_novo.jsp" flush="true"/>
                <jsp:include page="include_menu_zw_flat.jsp" flush="true"/>
                <rich:jQuery selector=".itemClubeVantagens" query="addClass('menuItemAtual')"/>
            </h:panelGroup>
            <h:panelGroup layout="block" styleClass="caixaCorpo">
                <h:panelGroup layout="block" style="height: 80%;width: 100%">
                    <h:panelGroup layout="block" styleClass="caixaMenuLatel">
                        <h:panelGroup layout="block" styleClass="container-imagem container-conteudo-central">
                            <h:panelGroup id="panelConteudo">
                                <h:panelGroup layout="block" styleClass="container-box zw_ui especial ">
                                    <h:panelGroup styleClass="container-box-header" layout="block" style="position: relative">
                                        <h:panelGroup layout="block" styleClass="margin-box">
                                            <h:panelGrid columns="2" id="gridPricipal" style="width: 100%;">
                                                <h:panelGroup style="">
                                                    <h:outputText value="BI Clube de Vantagens"
                                                                  style="float: left; margin-top: 10px"
                                                                  styleClass="container-header-titulo"/>
                                                    <h:outputLink styleClass="linkWiki"
                                                                  style="float: left; margin-top: 10px; margin-left: 10px"
                                                                  value="#{SuperControle.urlBaseConhecimento}como-ver-qual-produto-ou-plano-mais-acumulou-pontos-no-clube-de-vantagens-bi-clube-de-vantagens/"
                                                                  title="Clique e saiba mais: Clube de Vantagens " target="_blank">
                                                        <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                                                    </h:outputLink>
                                                </h:panelGroup>

                                                <h:panelGroup styleClass="tooltipster"
                                                              style="margin-top: 10px ; width: 80% !important; ">

                                                    <h:outputText styleClass="texto-cor-cinza texto-font"
                                                                  style="font-size: 14px;"
                                                                  value="#{msg_aplic.prt_Brinde_empresa}"/>
                                                    <h:selectOneMenu id="empresaSelecionadaCV" tabindex="8"
                                                                     onblur="blurinput(this);"
                                                                     styleClass="inputTextClean texto-cor-cinza texto-font"
                                                                     style="font-size: 12px !important;"
                                                                     onfocus="focusinput(this);"
                                                                     value="#{ClubeVantagensControle.empresa.codigo}">
                                                        <f:selectItems value="#{ClubeVantagensControle.listaEmpresas}"/>
                                                    </h:selectOneMenu>


                                                    <h:outputText
                                                            styleClass="texto-size-14 texto-cor-cinza texto-font texto-bold"
                                                            value=" "/>
                                                    <h:panelGroup styleClass="dateTimeCustom"
                                                                  style="font-size: 11px !important;">
                                                        <rich:calendar value="#{ClubeVantagensControle.dataInicial}"
                                                                       id="dataInicial"
                                                                       inputStyle="width: 73px;"
                                                                       inputSize="10"
                                                                       inputClass="form"
                                                                       oninputblur="blurinput(this);"
                                                                       oninputfocus="focusinput(this);"
                                                                       oninputchange="return validar_Data(this.id);"
                                            datePattern="dd/MM/yyyy"
                                                                       buttonIcon="/imagens_flat/calendar-button.svg"
                                                                       enableManualInput="true"
                                                                       zindex="2"
                                                                       showWeeksBar="false"/>
                                </h:panelGroup>
                                                    <h:outputText styleClass="texto-cor-cinza texto-font" value=" até "
                                                                  style="font-size: 14px !important;"/>
                                                    <h:panelGroup styleClass="dateTimeCustom"
                                                                  style="font-size: 11px !important;">
                                                        <rich:calendar value="#{ClubeVantagensControle.dataFinal}"
                                                                       id="dataFinal"
                                                                       inputSize="10"
                                                                       inputStyle="width: 73px;"
                                                                       buttonIcon="/imagens_flat/calendar-button.svg"
                                                                       inputClass="form"
                                                                       oninputblur="blurinput(this);"
                                                                       oninputfocus="focusinput(this);"
                                                                       oninputchange="return validar_Data(this.id);"
                                            datePattern="dd/MM/yyyy"
                                                                       enableManualInput="true"
                                                                       zindex="2"
                                                                       showWeeksBar="false"/>

                                                        <a4j:commandLink styleClass="tooltipster"
                                                                         title="Atualizar os dados"
                                                                         style="font-size: medium"
                                                                         action="#{ClubeVantagensControle.carregarBI}"
                                                                         reRender="panelConteudo">
                                                            <i class="fa-icon-refresh"></i>
                                                        </a4j:commandLink>
                                                    </h:panelGroup>

                                                </h:panelGroup>

                                            </h:panelGrid>
                                </h:panelGroup>
                                    </h:panelGroup>
                                    <%--//// Totalizadores   //////--%>
                                    <h:panelGrid id="gridTotalizadores" styleClass="grid-totalizadores" columns="2" style="width: 100%" >
                                        <h:panelGroup id="pgTotalpontos" style="width: 50% !important;" >
                                            <span
                                                    class="bi-font-family bi-table-text texto-size-20 texto-cor-cinza" style="padding-left: 30%;">
                                                Totalizador de pontos
                                                <i class="fa-icon-question-sign tooltipster" style="font-size: medium; "
                                                   title="Totalizador de pontos por categoria."></i>
                                            </span>

                                            <rich:dataTable id="listaTotalizadorCategoria"
                                                            headerClass="consulta"
                                                            styleClass="tabelaSimplesCustom showCellEmpty grid-container"
                                                            value="#{ClubeVantagensControle.listaTotalizadorCategoriaPontos}"
                                                            var="categoria">

                                                <rich:column width="30%" headerClass="col-text-align-left">
                                                    <f:facet name="header">
                                                        <h:outputText value="Categoria"
                                                                      styleClass="texto-size-16 texto-cor-cinza text-bold "/>
                                                    </f:facet>
                                                    <h:panelGroup>
                                                        <h:outputText
                                                                styleClass="texto-size-16 linkPadrao texto-cor-cinza"
                                                                value="#{categoria.tipoItemCampanha.descricao}"/>
                                                    </h:panelGroup>
                                                </rich:column>
                                                <rich:column width="30%" headerClass="col-text-align-right"
                                                             style="text-align: right !important">
                                                    <f:facet name="header">
                                                        <h:outputText value="Pontos"
                                                                      styleClass="texto-size-16 texto-cor-cinza text-bold "/>
                                                    </f:facet>
                                                    <h:panelGroup>
                                                        <a4j:commandLink id="abrirobj"
                                                                         styleClass="texto-size-16 linkPadrao texto-cor-azul"
                                                                         value="#{categoria.pontos}"
                                                                         reRender="modalclienteXpontos,modalDescricaoXpontuacao"
                                                                         actionListener="#{ClubeVantagensControle.abrirTotalizadorCategorias}"
                                                                         oncomplete="#{ClubeVantagensControle.oncomplete}">
                                                            <f:attribute name="objCategoriaModal" value="#{categoria}"/>
                                                        </a4j:commandLink>
                                                    </h:panelGroup>
                                                </rich:column>
                                                <rich:column styleClass="col-text-align-right">
                                                    <f:facet name="header">
                                                        <h:outputText value=" % "
                                                                      styleClass="texto-size-16 texto-cor-cinza text-bold "
                                                                      style="text-align: right"/>
                                                    </f:facet>
                                                    <h:outputText styleClass="texto-size-14 texto-cor-cinza texto-font"
                                                                  style="text-align: right"
                                                                  value="#{(categoria.pontos*100)/ClubeVantagensControle.totalPontos}">
                                                        <f:converter converterId="FormatadorNumerico"/>
                                                    </h:outputText>
                                                </rich:column>
                                            </rich:dataTable>
                                        </h:panelGroup  >
                                        <%-- Categorias --%>
                                        <%-- BRINDES --%>
                                        <h:panelGroup  style="width: 50%  !important;max-height: 48% !important;min-height: 48% !important"  >
                                            <span class="bi-font-family bi-table-text texto-size-20 texto-cor-cinza" style="padding-left: 40%;">
                                                Brindes
                                                <i class="fa-icon-question-sign tooltipster" style="font-size: medium;"
                                                   title="Resgate de Brindes realizadas com sucesso no período."></i>
                                            </span>
                                            <rich:dataTable id="listaBridnes"
                                                            headerClass="consulta"
                                                            styleClass="tabelaSimplesCustom showCellEmpty grid-container"
                                                            value="#{ClubeVantagensControle.listaTotalizadorBrindes}"
                                                            var="brinde">

                                                <rich:column style="width:100% !important;" headerClass="col-text-align-left">
                                                    <f:facet name="header">
                                                        <h:outputText value="Brinde"
                                                                      styleClass="texto-size-16 texto-cor-cinza text-bold "/>
                                                    </f:facet>
                                                    <h:panelGroup>
                                                        <h:outputText
                                                                styleClass="texto-size-16 linkPadrao texto-cor-cinza"
                                                                value="#{brinde.nome}"/>
                                                    </h:panelGroup>
                                                </rich:column>
                                                <rich:column width="10% !important;" headerClass="col-text-align-right"
                                                             style="text-align: right !important">
                                                    <f:facet name="header">
                                                        <h:outputText value="Quantidade"
                                                                      styleClass="texto-size-16 texto-cor-cinza text-bold "/>
                                                    </f:facet>
                                                    <h:panelGroup>
                                                        <a4j:commandLink id="abrirobj"
                                                                         styleClass="texto-size-16 linkPadrao texto-cor-azul"
                                                                         value="#{brinde.pontos}"
                                                                         reRender="modalclienteXpontos"
                                                                         actionListener="#{ClubeVantagensControle.abrirTotalizadorBrindes}"
                                                                         oncomplete="#{ClubeVantagensControle.oncomplete}">
                                                            <f:attribute name="objBrindeModal" value="#{brinde}"/>
                                                        </a4j:commandLink>
                                                    </h:panelGroup>
                                                </rich:column>
                                                <rich:column styleClass="col-text-align-right">
                                                    <f:facet name="header">
                                                        <h:outputText value=" % "
                                                                      styleClass="texto-size-16 texto-cor-cinza text-bold "
                                                                      style="text-align: right"/>
                                                    </f:facet>
                                                    <h:outputText styleClass="texto-size-14 texto-cor-cinza texto-font"
                                                                  style="text-align: right"
                                                                  value="#{(brinde.pontos*100)/ClubeVantagensControle.totalBrindes}">
                                                        <f:converter converterId="FormatadorNumerico"/>
                                                    </h:outputText>
                                                </rich:column>
                                            </rich:dataTable>
                                            <h:panelGrid id="gridPaginadorBi" columns="1" width="100%" columnClasses="colunaCentralizada" rendered="#{fn:length(ClubeVantagensControle.listaTotalizadorBrindes) > 0}">
                                                <h:panelGroup layout="block"
                                                              styleClass="paginador-container">
                                                    <h:panelGroup styleClass="pull-left" layout="block">
                                                        <h:outputText
                                                                styleClass="texto-size-14 texto-cor-cinza"
                                                                value="Mostrando #{ClubeVantagensControle.paginadorListaBrindesBi.offset+1} até #{ClubeVantagensControle.paginadorListaBrindesBi.paginaMostrandoAteApresentar} Total #{ClubeVantagensControle.paginadorListaBrindesBi.count} Item(s)"></h:outputText>
                                                    </h:panelGroup>
                                                    <h:panelGroup layout="block"
                                                                  style="align-items: center">
                                                        <a4j:commandLink
                                                                disabled="#{ClubeVantagensControle.paginadorListaBrindesBi.desabilitarAcoes}"
                                                                styleClass="linkPadrao texto-cor-azul texto-size-20-real"
                                                                reRender="listaBridnes, gridPaginadorBi"
                                                                actionListener="#{ClubeVantagensControle.primeiraPagina}">
                                                            <i class="fa-icon-double-angle-left"
                                                               id="primPaginaHistorico"></i>
                                                        </a4j:commandLink>

                                                        <a4j:commandLink
                                                                disabled="#{ClubeVantagensControle.paginadorListaBrindesBi.desabilitarAcoes}"
                                                                styleClass="linkPadrao texto-cor-azul texto-size-20-real"
                                                                reRender="listaBridnes, gridPaginadorBi"
                                                                actionListener="#{ClubeVantagensControle.paginaAnterior}">
                                                            <i class="fa-icon-angle-left" id="pagAntHistorico"></i>
                                                        </a4j:commandLink>
                                                        <h:outputText
                                                                styleClass="texto-font texto-cor-cinza texto-size-16-real"
                                                                value="#{msg_aplic.prt_msg_pagina} #{ClubeVantagensControle.paginadorListaBrindesBi.paginaAtualApresentar}"
                                                                rendered="true"/>
                                                        <a4j:commandLink
                                                                disabled="#{ClubeVantagensControle.paginadorListaBrindesBi.desabilitarAcoes}"
                                                                styleClass="linkPadrao texto-font texto-cor-azul texto-size-20-real"
                                                                reRender="listaBridnes, gridPaginadorBi"
                                                                actionListener="#{ClubeVantagensControle.proximaPagina}">
                                                            <i class="fa-icon-angle-right" id="proxPagHistorico"></i>
                                                        </a4j:commandLink>

                                                        <a4j:commandLink
                                                                disabled="#{ClubeVantagensControle.paginadorListaBrindesBi.desabilitarAcoes}"
                                                                styleClass="linkPadrao texto-cor-azul texto-size-20-real"
                                                                reRender="listaBridnes, gridPaginadorBi"
                                                                actionListener="#{ClubeVantagensControle.ultimaPagina}">
                                                            <i class="fa-icon-double-angle-right"
                                                               id="ultimaPaginaHistorico"></i>
                                    </a4j:commandLink>
                                </h:panelGroup>

                            </h:panelGroup>
                                            </h:panelGrid>
                                        </h:panelGroup>

                                    </h:panelGrid>
                                    <h:panelGrid id="gridTotalizadores2" styleClass="grid-totalizadores"  columns="2" style="width: 100%" >
                                        <h:panelGroup   style=" margin-top: 5%; width: 50% !important;"  id="panelCampanhaAtiva">
                                            <span class="bi-font-family bi-table-text texto-size-20 texto-cor-cinza" style="padding-left: 30%;">
                                                Campanhas Ativas
                                                <i class="fa-icon-question-sign tooltipster" style="font-size: medium;"
                                                   title="Mostra as campanhas ativas no período informado."></i>
                                            </span>

                                            <rich:dataTable id="listaCampana"
                                                            headerClass="consulta"
                                                            styleClass="tabelaSimplesCustom showCellEmpty grid-container"
                                                            value="#{ClubeVantagensControle.listaTotalizadorCampanhas}"
                                                            var="campanha">

                                                <rich:column width="30%" headerClass="col-text-align-left">
                                                    <f:facet name="header">
                                                        <h:outputText value="Campanha"
                                                                      styleClass="texto-size-16 texto-cor-cinza text-bold "/>
                                                    </f:facet>
                                                    <h:panelGroup>
                                                        <h:outputText
                                                                styleClass="texto-size-16 linkPadrao texto-cor-cinza"
                                                                value="#{campanha.nome}"/>
                                                    </h:panelGroup>
                                                </rich:column>
                                                <rich:column width="30%" headerClass="col-text-align-right"
                                                             style="text-align: right !important">
                                                    <f:facet name="header">
                                                        <h:outputText value="Pontos Acumulados"
                                                                      styleClass="texto-size-16 texto-cor-cinza text-bold "/>
                                                    </f:facet>
                                                    <h:panelGroup>
                                                        <a4j:commandLink id="abrirobj"
                                                                         styleClass="texto-size-16 linkPadrao texto-cor-azul"
                                                                         value="#{campanha.multiplicador}"
                                                                         reRender="modalclienteXpontosXTipo"
                                                                         actionListener="#{ClubeVantagensControle.abrirTotalizadorCampanhas}"
                                                                         oncomplete="#{ClubeVantagensControle.oncomplete}">
                                                            <f:attribute name="objCampanhaModal" value="#{campanha}"/>
                                                        </a4j:commandLink>
                                                    </h:panelGroup>
                                                </rich:column>
                                            </rich:dataTable>
                                        </h:panelGroup>
                                        <h:panelGroup style=" margin-top: 5%; width: 50% !important;" >
                                            <span class="bi-font-family bi-table-text texto-size-20 texto-cor-cinza" style="padding-left: 25%;">
                                                Alunos que não resgataram brindes
                                                <i class="fa-icon-question-sign tooltipster" style="font-size: medium;"
                                                   title="Mostra a quantidade de alunos que possuem pontos para trocar em brindes no periodo informado."></i>
                                            </span>
                                            <a4j:commandLink styleClass="gr-totalizador-text bi-font-family" style="margin-left: 15%;padding-left: 30%;"
                                                             id="btnBrindesNaoResgatados2"
                                                             value="#{fn:length(ClubeVantagensControle.listaTotalizadorAlunoSemResgate)}"
                                                             reRender="modalclienteCV"
                                                             actionListener="#{ClubeVantagensControle.abrirTotalizadorAlunos}"
                                                             oncomplete="#{ClubeVantagensControle.oncomplete}"/>
                                        </h:panelGroup>
                                    </h:panelGrid>
                                </h:panelGroup>
                            </h:panelGroup>
                            </h:panelGroup>
                            <jsp:include page="include_box_menulateral.jsp">
                                <jsp:param name="menu" value="ADM-CLUBE_VANTAGENS" />
                            </jsp:include>
                    </h:panelGroup>
                </h:panelGroup>
            </h:panelGroup>
            <jsp:include page="include_rodape_flat.jsp" flush="true"/>
        </h:panelGroup>
        </body>
        </html>
    </h:form>
    <rich:modalPanel id="modalDescricaoXpontuacao" width="700" height="460" styleClass="novaModal">
        <f:facet name="header">
            <h:panelGroup style="text-align: center">
                <h:outputText value="#{ClubeVantagensControle.tituloRel}"/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText
                        styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                        id="hidemodalDescricaoXpontuacao"/>
                <rich:componentControl for="modalDescricaoXpontuacao" attachTo="hidemodalDescricaoXpontuacao"
                                       operation="hide"
                                       event="onclick"/>
            </h:panelGroup>
        </f:facet>

        <a4j:form>
            <h:panelGrid columns="2" width="100%">

               <h:panelGroup layout="block" style="float: right; padding-bottom: 5px">
                    <%--BOTÃO PDF--%>
                    <a4j:commandLink
                            id="exportarPDF"
                            styleClass="exportadores margin-h-10 linkPadrao"
                            style="float: right"
                            actionListener="#{ExportadorListaControle.exportar}"
                            rendered="#{not empty ClubeVantagensControle.listaTotalizadorHistoricoPontos}"
                            oncomplete="abrirPopup('../UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/pdf','Transacoes', 800,200);#{ExportadorListaControle.msgAlert}">
                        <f:attribute name="lista" value="#{ClubeVantagensControle.listaTotalizadorHistoricoPontos}"/>
                        <f:attribute name="tipo" value="pdf"/>
                        <f:attribute name="atributos" value="#{ClubeVantagensControle.atributosRel}"/>
                        <f:attribute name="titulo" value="#{ClubeVantagensControle.tituloRel}"/>
                        <f:attribute name="prefixo" value="#{ClubeVantagensControle.prefixoRel}"/>
                        <h:outputText title="Exportar para o formato PDF" styleClass="btn-print-2 pdf"/>
                    </a4j:commandLink>

                    <rich:spacer width="7" style="float: right"/>

                    <%--BOTÃO EXCEL--%>
                    <a4j:commandLink
                            styleClass="exportadores margin-h-10 linkPadrao"
                            id="exportarExcel"
                            style="float: right"
                            actionListener="#{ExportadorListaControle.exportar}"
                            rendered="#{not empty ClubeVantagensControle.listaTotalizadorHistoricoPontos}"
                            oncomplete="abrirPopup('../UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/vnd.ms-excel','Transacoes', 800,200);#{ExportadorListaControle.msgAlert}">
                        <f:attribute name="lista" value="#{ClubeVantagensControle.listaTotalizadorHistoricoPontos}"/>
                        <f:attribute name="tipo" value="xls"/>
                        <f:attribute name="atributos" value="#{ClubeVantagensControle.atributosRel}"/>
                        <f:attribute name="titulo" value="#{ClubeVantagensControle.tituloRel}"/>
                        <f:attribute name="prefixo" value="#{ClubeVantagensControle.prefixoRel}"/>
                        <h:outputText title="Exportar para o formato XLS" styleClass="btn-print-2 excel"/>
                    </a4j:commandLink>
                </h:panelGroup>
            </h:panelGrid>

            <h:panelGroup layout="block" style="overflow-y: auto; max-height: 300px; ">
                <rich:dataTable id="tblmodalclienteXdata" width="100%" headerClass="subordinado"
                                rowClasses="linhaImpar, linhaPar"
                                value="#{ClubeVantagensControle.listaTotalizadorHistoricoPontos}"
                                var="historico">
                    <c:if test="${historico.cliente.matricula > 0}">
                        <rich:column sortBy="#{historico.cliente.matricula}" style="text-align: right">
                            <f:facet name="header">
                                <h:outputText style="font-size: 16px; font-weight: bold; color: #888888"
                                              value="Código"/>
                            </f:facet>
                            <h:outputText value="#{historico.cliente.matricula}"/>
                        </rich:column>
                    </c:if>

                    <rich:column sortBy="#{historico.cliente.pessoa.nome}">
                        <f:facet name="header">
                            <h:outputText style="font-size: 16px; font-weight: bold; color: #888888"
                                          value="Descrição"/>
                        </f:facet>
                        <h:outputText value="#{historico.cliente.pessoa.nome}"/>
                    </rich:column>

                    <rich:column sortBy="#{historico.pontos}" style="text-align: right">
                        <f:facet name="header">
                            <h:outputText style="font-size: 16px; font-weight: bold; color: #888888"
                                          value="Pontuação"/>
                        </f:facet>
                        <h:outputText value="#{historico.pontos}"/>
                    </rich:column>
                </rich:dataTable>
            </h:panelGroup>
            <h:outputText style="float: left"
                          value="Total registros #{fn:length(ClubeVantagensControle.listaTotalizadorHistoricoPontos)} "/>
        </a4j:form>
    </rich:modalPanel>

    <rich:modalPanel id="modalclienteXpontos" width="700" height="460" styleClass="novaModal">
        <f:facet name="header">
            <h:panelGroup style="text-align: center">
                <h:outputText value="#{ClubeVantagensControle.tituloRel}"/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText
                        styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                        id="hidemodalclienteXpontos"/>
                <rich:componentControl for="modalclienteXpontos" attachTo="hidemodalclienteXpontos"
                                       operation="hide"
                                       event="onclick"/>
            </h:panelGroup>
        </f:facet>

        <a4j:form>
            <h:panelGrid columns="2" width="100%">

                <h:panelGroup layout="block" style="float: right; padding-bottom: 5px">
                    <%--BOTÃO PDF--%>
                    <a4j:commandLink
                            id="exportarPDF"
                            styleClass="exportadores margin-h-10 linkPadrao"
                            style="float: right"
                            actionListener="#{ExportadorListaControle.exportar}"
                            rendered="#{not empty ClubeVantagensControle.listaTotalizadorHistoricoPontos}"
                            oncomplete="abrirPopup('../UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/pdf','Transacoes', 800,200);#{ExportadorListaControle.msgAlert}">
                        <f:attribute name="lista" value="#{ClubeVantagensControle.listaTotalizadorHistoricoPontos}"/>
                        <f:attribute name="tipo" value="pdf"/>
                        <f:attribute name="atributos" value="#{ClubeVantagensControle.atributosRel}"/>
                        <f:attribute name="titulo" value="#{ClubeVantagensControle.tituloRel}"/>
                        <f:attribute name="prefixo" value="#{ClubeVantagensControle.prefixoRel}"/>
                        <h:outputText title="Exportar para o formato PDF" styleClass="btn-print-2 pdf"/>
                    </a4j:commandLink>

                    <rich:spacer width="7" style="float: right"/>

                    <%--BOTÃO EXCEL--%>
                    <a4j:commandLink
                            styleClass="exportadores margin-h-10 linkPadrao"
                            id="exportarExcel"
                            style="float: right"
                            actionListener="#{ExportadorListaControle.exportar}"
                            rendered="#{not empty ClubeVantagensControle.listaTotalizadorHistoricoPontos}"
                            oncomplete="abrirPopup('../UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/vnd.ms-excel','Transacoes', 800,200);#{ExportadorListaControle.msgAlert}">
                        <f:attribute name="lista" value="#{ClubeVantagensControle.listaTotalizadorHistoricoPontos}"/>
                        <f:attribute name="tipo" value="xls"/>
                        <f:attribute name="atributos" value="#{ClubeVantagensControle.atributosRel}"/>
                        <f:attribute name="titulo" value="#{ClubeVantagensControle.tituloRel}"/>
                        <f:attribute name="prefixo" value="#{ClubeVantagensControle.prefixoRel}"/>
                        <h:outputText title="Exportar para o formato PDF" styleClass="btn-print-2 excel"/>
                    </a4j:commandLink>
                </h:panelGroup>
            </h:panelGrid>

            <h:panelGroup layout="block" style="overflow-y: auto; max-height: 300px; ">
                <rich:dataTable id="tblModalClienteXPontos" width="100%" headerClass="subordinado"
                                rowClasses="linhaImpar, linhaPar"
                                value="#{ClubeVantagensControle.listaTotalizadorHistoricoPontos}"
                                var="historico">
                    <rich:column sortBy="#{historico.cliente.matricula}" style="text-align: right; width: 10px !important;">
                        <f:facet name="header">
                            <h:outputText style="font-size: 16px; font-weight: bold; color: #888888"
                                          value="Matrícula"/>
                        </f:facet>
                        <h:outputText value="#{historico.cliente.matricula}"/>
                    </rich:column>

                    <rich:column sortBy="#{historico.cliente.pessoa.nome}">
                        <f:facet name="header">
                            <h:outputText style="font-size: 16px; font-weight: bold; color: #888888"
                                          value="Nome"/>
                        </f:facet>
                        <h:outputText value="#{historico.cliente.pessoa.nome}"/>
                    </rich:column>

                    <rich:column sortBy="#{historico.pontos}" style="text-align: right; width: 10px !important;">
                        <f:facet name="header">
                            <h:outputText style="font-size: 16px; font-weight: bold; color: #888888"
                                          value="Pontuação"/>
                        </f:facet>
                        <h:outputText value="#{historico.pontos}"/>
                    </rich:column>

                    <rich:column style="text-align: center; width: 10px !important;">
                        <f:facet name="header">
                            <h:outputText style="font-size: 16px; font-weight: bold; color: #888888" value="Opções"/>
                        </f:facet>
                        <a4j:commandLink styleClass="exportadores margin-h-10 linkPadrao" id="imprimirComprovante"
                                         title="Imprimir comprovante do brinde" immediate="true"
                                         action="#{ClubeVantagensControle.imprimirComprovanteOperacao}"
                                         oncomplete="abrirPopupPDFImpressao('relatorio/#{ClubeVantagensControle.nomeArquivoComprovanteOperacao}','', 780, 595);">
                            <h:outputText title="Imprimir comprovante do brinde" styleClass="fa-icon-print"/>
                        </a4j:commandLink>

                    </rich:column>
                </rich:dataTable>
            </h:panelGroup>
            <h:outputText style="float: left"
                          value="Total registros #{fn:length(ClubeVantagensControle.listaTotalizadorHistoricoPontos)} "/>
        </a4j:form>
    </rich:modalPanel>

    <rich:modalPanel id="modalclienteXpontosXTipo" width="750" height="460" styleClass="novaModal">
        <f:facet name="header">
            <h:panelGroup style="text-align: center">
                <h:outputText value="#{ClubeVantagensControle.tituloRel}"/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText
                        styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                        id="hidemodalclienteXpontosXTipo"/>
                <rich:componentControl for="modalclienteXpontosXTipo" attachTo="hidemodalclienteXpontosXTipo"
                                       operation="hide"
                                       event="onclick"/>
            </h:panelGroup>
        </f:facet>

        <a4j:form>
            <h:panelGrid columns="2" width="100%">

                <h:panelGroup layout="block" style="float: right; padding-bottom: 5px">
                    <%--BOTÃO PDF--%>
                    <a4j:commandLink
                            id="exportarPDF"
                            styleClass="exportadores margin-h-10 linkPadrao"
                            style="float: right"
                            actionListener="#{ExportadorListaControle.exportar}"
                            rendered="#{not empty ClubeVantagensControle.listaTotalizadorHistoricoPontos}"
                            oncomplete="abrirPopup('../UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/pdf','Transacoes', 800,200);#{ExportadorListaControle.msgAlert}">
                        <f:attribute name="lista" value="#{ClubeVantagensControle.listaTotalizadorHistoricoPontos}"/>
                        <f:attribute name="tipo" value="pdf"/>
                        <f:attribute name="atributos" value="#{ClubeVantagensControle.atributosRel}"/>
                        <f:attribute name="titulo" value="#{ClubeVantagensControle.tituloRel}"/>
                        <f:attribute name="prefixo" value="#{ClubeVantagensControle.prefixoRel}"/>
                        <h:outputText title="Exportar para o formato PDF" styleClass="btn-print-2 pdf"/>
                    </a4j:commandLink>

                    <rich:spacer width="7" style="float: right"/>

                    <%--BOTÃO EXCEL--%>
                    <a4j:commandLink
                            styleClass="exportadores margin-h-10 linkPadrao"
                            id="exportarExcel"
                            style="float: right"
                            actionListener="#{ExportadorListaControle.exportar}"
                            rendered="#{not empty ClubeVantagensControle.listaTotalizadorHistoricoPontos}"
                            oncomplete="abrirPopup('../UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/vnd.ms-excel','Transacoes', 800,200);#{ExportadorListaControle.msgAlert}">
                        <f:attribute name="lista" value="#{ClubeVantagensControle.listaTotalizadorHistoricoPontos}"/>
                        <f:attribute name="tipo" value="xls"/>
                        <f:attribute name="atributos" value="#{ClubeVantagensControle.atributosRel}"/>
                        <f:attribute name="titulo" value="#{ClubeVantagensControle.tituloRel}"/>
                        <f:attribute name="prefixo" value="#{ClubeVantagensControle.prefixoRel}"/>
                        <h:outputText title="Exportar para o formato PDF" styleClass="btn-print-2 excel"/>
                    </a4j:commandLink>
                </h:panelGroup>
            </h:panelGrid>

            <h:panelGroup layout="block" style="overflow-y: auto; max-height: 300px; ">
                <rich:dataTable id="tblmodalclienteXpontosXTipo" width="100%" headerClass="subordinado"
                                rowClasses="linhaImpar, linhaPar"
                                value="#{ClubeVantagensControle.listaTotalizadorHistoricoPontos}"
                                var="historico">
                    <rich:column sortBy="#{historico.cliente.matricula}" style="text-align: right">
                        <f:facet name="header">
                            <h:outputText style="font-size: 16px; font-weight: bold; color: #888888"
                                          value="Matrícula"/>
                        </f:facet>
                        <h:outputText value="#{historico.cliente.matricula}"/>
                    </rich:column>

                    <rich:column sortBy="#{historico.cliente.pessoa.nome}">
                        <f:facet name="header">
                            <h:outputText style="font-size: 16px; font-weight: bold; color: #888888"
                                          value="Nome"/>
                        </f:facet>
                        <h:outputText value="#{historico.cliente.pessoa.nome}"/>
                    </rich:column>

                    <rich:column sortBy="#{historico.pontos}" style="text-align: right">
                        <f:facet name="header">
                            <h:outputText style="font-size: 16px; font-weight: bold; color: #888888"
                                          value="Pontuação"/>
                        </f:facet>
                        <h:outputText value="#{historico.pontos}"/>
                    </rich:column>

                    <rich:column sortBy="#{historico.tipoPonto.descricao}" style="text-align: right">
                        <f:facet name="header">
                            <h:outputText style="font-size: 16px; font-weight: bold; color: #888888"
                                          value="Categoria"/>
                        </f:facet>
                        <h:outputText value="#{historico.tipoPonto.descricao}"/>
                    </rich:column>
                </rich:dataTable>
            </h:panelGroup>
            <h:outputText style="float: left"
                          value="Total registros #{fn:length(ClubeVantagensControle.listaTotalizadorHistoricoPontos)} "/>
        </a4j:form>
    </rich:modalPanel>

    <rich:modalPanel id="modalclienteCV" width="700" height="460" styleClass="novaModal">
        <f:facet name="header">
            <h:panelGroup style="text-align: center">
                <h:outputText value="#{ClubeVantagensControle.tituloRel}"/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText
                        styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                        id="hidemodalclienteCV"/>
                <rich:componentControl for="modalclienteCV" attachTo="hidemodalclienteCV"
                                       operation="hide"
                                       event="onclick"/>
            </h:panelGroup>
        </f:facet>

        <a4j:form>
            <h:panelGrid columns="2" width="100%">

                <h:panelGroup layout="block" style="float: right; padding-bottom: 5px">
                    <%--BOTÃO PDF--%>
                    <a4j:commandLink
                            id="exportarPDF"
                            styleClass="exportadores margin-h-10 linkPadrao"
                            style="float: right"
                            actionListener="#{ExportadorListaControle.exportar}"
                            rendered="#{not empty ClubeVantagensControle.listaTotalizadorAlunoSemResgate}"
                            oncomplete="abrirPopup('../UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/pdf','Transacoes', 800,200);#{ExportadorListaControle.msgAlert}">
                        <f:attribute name="lista" value="#{ClubeVantagensControle.listaTotalizadorAlunoSemResgate}"/>
                        <f:attribute name="tipo" value="pdf"/>
                        <f:attribute name="atributos" value="#{ClubeVantagensControle.atributosRel}"/>
                        <f:attribute name="titulo" value="#{ClubeVantagensControle.tituloRel}"/>
                        <f:attribute name="prefixo" value="#{ClubeVantagensControle.prefixoRel}"/>
                        <h:outputText title="Exportar para o formato PDF" styleClass="btn-print-2 pdf"/>
                    </a4j:commandLink>

                    <rich:spacer width="7" style="float: right"/>

                    <%--BOTÃO EXCEL--%>
                    <a4j:commandLink
                            styleClass="exportadores margin-h-10 linkPadrao"
                            id="exportarExcel"
                            style="float: right"
                            actionListener="#{ExportadorListaControle.exportar}"
                            rendered="#{not empty ClubeVantagensControle.listaTotalizadorAlunoSemResgate}"
                            oncomplete="abrirPopup('../UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/vnd.ms-excel','Transacoes', 800,200);#{ExportadorListaControle.msgAlert}">
                        <f:attribute name="lista" value="#{ClubeVantagensControle.listaTotalizadorAlunoSemResgate}"/>
                        <f:attribute name="tipo" value="xls"/>
                        <f:attribute name="atributos" value="#{ClubeVantagensControle.atributosRel}"/>
                        <f:attribute name="titulo" value="#{ClubeVantagensControle.tituloRel}"/>
                        <f:attribute name="prefixo" value="#{ClubeVantagensControle.prefixoRel}"/>
                        <h:outputText title="Exportar para o formato planilha Excel" styleClass="btn-print-2 excel"/>
                    </a4j:commandLink>
                </h:panelGroup>
            </h:panelGrid>

            <h:panelGroup layout="block" style="overflow-y: auto; max-height: 300px; ">
                <rich:dataTable id="tblmodalclienteCV" width="100%" headerClass="subordinado"
                                rowClasses="linhaImpar, linhaPar"
                                value="#{ClubeVantagensControle.listaTotalizadorAlunoSemResgate}"
                                var="cliente">
                    <rich:column sortBy="#{cliente.matricula}" style="text-align: right">
                        <f:facet name="header">
                            <h:outputText style="font-size: 16px; font-weight: bold; color: #888888"
                                          value="Matrícula"/>
                        </f:facet>
                        <h:outputText value="#{cliente.matricula}"/>
                    </rich:column>

                    <rich:column sortBy="#{cliente.nomeSocial}">
                        <f:facet name="header">
                            <h:outputText style="font-size: 16px; font-weight: bold; color: #888888"
                                          value="Nome"/>
                        </f:facet>
                        <h:outputText value="#{cliente.nomeSocial}"/>
                    </rich:column>

                    <rich:column sortBy="#{cliente.situacao}">
                        <f:facet name="header">
                            <h:outputText style="font-size: 16px; font-weight: bold; color: #888888"
                                          value="Situação"/>
                        </f:facet>
                        <h:outputText value="#{cliente.situacao == 'AT' ? 'ATIVO' : 'INATIVO'}"/>

                    </rich:column>

                    <rich:column sortBy="#{cliente.pontuacao}" style="text-align: right">
                        <f:facet name="header">
                            <h:outputText style="font-size: 16px; font-weight: bold; color: #888888"
                                          value="Pontuação"/>
                        </f:facet>
                        <h:outputText value="#{cliente.pontuacao}"/>
                    </rich:column>
                </rich:dataTable>
            </h:panelGroup>
            <h:outputText style="float: left; font-size: 16px; color: #242424; padding-top: 10px"
                          value="Total: #{fn:length(ClubeVantagensControle.listaTotalizadorAlunoSemResgate)} alunos"/>
        </a4j:form>
    </rich:modalPanel>
</f:view>

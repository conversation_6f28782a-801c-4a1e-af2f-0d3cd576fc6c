<%@page contentType="text/html;charset=UTF-8" %>
<head><%@include file="/includes/include_import_minifiles.jsp" %></head>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<link href="beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<script type="text/javascript" language="javascript" src="bootstrap/jquery.js"></script>
<script type="text/javascript" language="javascript" src="hoverform.js"></script>
<script type="text/javascript" language="javascript" src="checkMarcarTodos.js"></script>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText value="#{msg_aplic.prt_Desconto_tituloForm}"/>
    </title>
    <style>
        .linha-titulo{
            background-color: #CCCCCC;
            border-color: #FFF;
            width: 100%;
            text-align: center;
            font-weight: bold;
            font-size: 8pt;
            font-family: Arial, Helvetica, sans-serif;
            margin-bottom: 5px;
        }
    </style>

    <c:set var="titulo" scope="session" value="${msg_aplic.prt_Desconto_tituloForm}"/>
    <c:set var="urlWiki" scope="session" value="${SuperControle.urlWiki}Cadastros:PPT:Desconto"/>
    <f:facet name="header">
        <jsp:include page="topoReduzido_material.jsp"/>
    </f:facet>
    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <h:form id="form">
            <hr style="border-color: #e6e6e6;"/>
            <h:commandLink action="#{DescontoControle.liberarBackingBeanMemoria}" id="idLiberarBackingBeanMemoria" style="display: none" />
            <h:panelGrid columns="1" width="100%">
                <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar" columnClasses="classEsquerda, classDireita" width="100%">
                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_Desconto_codigo}" />
                    <h:panelGroup>
                        <h:inputText  id="codigo" size="10" maxlength="10" readonly="true" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="camposSomenteLeitura" value="#{DescontoControle.descontoVO.codigo}" />
                        <h:message for="codigo" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>
                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_Desconto_descricao}" />
                    <h:panelGroup>
                        <h:inputText  id="descricao" size="50" maxlength="50" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" value="#{DescontoControle.descontoVO.descricao}" />
                        <h:message for="descricao" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>
                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_Desconto_tipoProduto}" />
                    <h:panelGroup>
                        <h:selectOneMenu id="tipoProduto" onblur="blurinput(this);"  onfocus="focusinput(this);"
                                         styleClass="form" value="#{DescontoControle.descontoVO.tipoProduto}" >
                            <f:selectItems value="#{DescontoControle.listaSelectItemTipoProdutoDesconto}" />
                            <a4j:support event="onchange" reRender="panelDesconto, panelIntervalo"/>
                        </h:selectOneMenu>
                        <h:message for="tipoProduto" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>

                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_Desconto_ativo}" />
                    <h:panelGroup>
                        <h:selectBooleanCheckbox  id="ativo"  onblur="blurinput(this);"  onfocus="focusinput(this);"
                                                  styleClass="form" value="#{DescontoControle.descontoVO.ativo}" />
                        <h:message for="ativo" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>
                </h:panelGrid>

                <h:panelGroup id="panelDesconto">
                    <h:panelGrid columns="1" width="100%" headerClass="subordinado" columnClasses="colunaCentralizada" rendered="#{DescontoControle.mostrarDesconto}">
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_Desconto_tituloForm}"/>
                        </f:facet>
                        <h:panelGrid columns="2" width="100%" rowClasses="linhaImpar, linhaPar" columnClasses="classEsquerda, classDireita">
                            <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Desconto_tipoDesconto}" />
                            <h:panelGroup>
                                <h:selectOneMenu  id="tipoDesconto" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" value="#{DescontoControle.descontoVO.tipoDesconto}" >
                                    <f:selectItems  value="#{DescontoControle.listaSelectItemTipoDescontoDesconto}" />
                                    <a4j:support event="onchange" reRender="panelDesconto"/>
                                </h:selectOneMenu>
                                <h:message for="tipoDesconto" styleClass="mensagemDetalhada"/>
                            </h:panelGroup>
                            <h:outputText rendered="#{DescontoControle.descontoVO.tipoDesconto.codigo == 1}" styleClass="tituloCampos" value="#{msg_aplic.prt_Desconto_percentual}"/>
                            <h:outputText rendered="#{DescontoControle.descontoVO.tipoDesconto.codigo == 2}" styleClass="tituloCampos" value="#{msg_aplic.prt_Desconto_valor}"/>
                            <h:outputText rendered="#{DescontoControle.descontoVO.tipoDesconto.codigo == 3}" styleClass="tituloCampos" value="#{msg_aplic.prt_Desconto_dias}"/>
                            <h:panelGroup rendered="#{DescontoControle.descontoVO.tipoDesconto.codigo != 0}">
                                <h:inputText  id="valor" size="20" maxlength="20" onblur="blurinput(this);" onkeypress="return Tecla(event);" onfocus="focusinput(this);" styleClass="form" value="#{DescontoControle.descontoVO.valor}" >
                                    <f:converter converterId="FormatadorNumerico" />
                                </h:inputText>
                                <h:message for="valor" styleClass="mensagemDetalhada"/>
                            </h:panelGroup>
                        </h:panelGrid>
                    </h:panelGrid>
                </h:panelGroup>

                <h:panelGroup id="panelIntervalo">
                    <h:panelGrid columns="1" width="100%" headerClass="subordinado" columnClasses="colunaCentralizada" rendered="#{DescontoControle.mostrarDescontoRenovacao}">
                        <f:facet name="header">
                            <h:outputText id="tituloDesAntTable" value="#{msg_aplic.prt_Antecipado_tituloForm}"/>
                        </f:facet>
                        <h:panelGrid columns="2" width="100%" rowClasses="linhaImpar, linhaPar" columnClasses="classEsquerda, classDireita">

                            <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Antecipado_tipoIntervalo}" />
                            <h:panelGroup>
                                <h:selectOneMenu id="tipoIntervalo" onblur="blurinput(this);"
                                                 onfocus="focusinput(this);" styleClass="form"
                                                 value="#{DescontoControle.descontoRenovacaoVO.tipoIntervalo}" >
                                    <f:selectItems value="#{DescontoControle.listaSelectItemTipoIntervaloAntecipado}" />
                                </h:selectOneMenu>
                                <h:message for="tipoIntervalo" styleClass="mensagemDetalhada"/>
                            </h:panelGroup>

                            <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Antecipado_intervaloDe}" />
                            <h:inputText id="IntervaloInicio" onkeypress="return Tecla(event);" size="20" maxlength="20"
                                         onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form"
                                         value="#{DescontoControle.descontoRenovacaoVO.intervaloDe}" >
                            </h:inputText>

                            <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Antecipado_intervaloAte}" />
                            <h:inputText id="IntervaloFim" onkeypress="return Tecla(event);" size="20" maxlength="20"
                                         onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form"
                                         value="#{DescontoControle.descontoRenovacaoVO.intervaloAte}" >
                            </h:inputText>

                            <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Desconto_tipoDesconto}" />
                            <h:panelGroup>
                                <h:selectOneMenu id="tipoDescontoAntecipado" onblur="blurinput(this);"
                                                 onfocus="focusinput(this);" styleClass="form"
                                                 value="#{DescontoControle.descontoRenovacaoVO.tipoDesconto}" >
                                    <f:selectItems value="#{DescontoControle.listaSelectItemTipoDescontoAntecipado}" />
                                    <a4j:support event="onchange" reRender="panelIntervalo"/>
                                </h:selectOneMenu>
                                <h:message for="tipoDescontoAntecipado" styleClass="mensagemDetalhada"/>
                            </h:panelGroup>

                            <h:outputText rendered="#{DescontoControle.descontoRenovacaoVO.tipoDesconto.codigo == 1}" styleClass="tituloCampos" value="#{msg_aplic.prt_Desconto_percentual}"/>
                            <h:outputText rendered="#{DescontoControle.descontoRenovacaoVO.tipoDesconto.codigo == 2}" styleClass="tituloCampos" value="#{msg_aplic.prt_Desconto_valor}"/>
                            <h:outputText rendered="#{DescontoControle.descontoRenovacaoVO.tipoDesconto.codigo == 3}" styleClass="tituloCampos" value="#{msg_aplic.prt_Desconto_dias}"/>
                            <h:panelGroup rendered="#{DescontoControle.descontoRenovacaoVO.tipoDesconto.codigo != 0}">
                                <h:inputText id="descontoValorAuto" onkeypress="return Tecla(event);" size="20" maxlength="20"
                                             onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form"
                                             value="#{DescontoControle.descontoRenovacaoVO.valor}" >
                                    <f:converter converterId="FormatadorNumerico" />
                                </h:inputText>
                            </h:panelGroup>

                            <h:outputText rendered="#{DescontoControle.mostrarJustificativa}" styleClass="tituloCampos" value="#{msg_aplic.prt_Antecipado_justificativaBonus}" />
                            <h:panelGroup rendered="#{DescontoControle.mostrarJustificativa}">
                                <h:selectOneMenu id="tipoJustificativa" onblur="blurinput(this);"
                                                 onfocus="focusinput(this);" styleClass="form"
                                                 value="#{DescontoControle.descontoRenovacaoVO.justificativaBonus.codigo}" >
                                    <f:selectItems value="#{DescontoControle.listaSelectItemJustificativaOperacao}" />
                                </h:selectOneMenu>
                                <h:message for="tipoJustificativa" styleClass="mensagemDetalhada"/>
                            </h:panelGroup>
                        </h:panelGrid>
                        <a4j:commandButton id="IntervaloAdicionar" action="#{DescontoControle.adicionarIntervalo}" reRender="panelIntervalo, panelErro" value="#{msg_bt.btn_adicionar}"
                                           image= "./imagens/botaoAdicionar.png" accesskey="5" styleClass="botoes"/>

                        <h:panelGrid columns="1" width="100%" styleClass="tabFormSubordinada">
                            <h:dataTable id="intervalos" width="100%" headerClass="subordinado" styleClass="tabFormSubordinada"
                                         rowClasses="linhaImpar, linhaPar" columnClasses="colunaAlinhamento"
                                         value="#{DescontoControle.descontoVO.listaIntervalos}" var="intervalo">
                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText value="#{msg_aplic.prt_Antecipado_tipoIntervalo}" />
                                    </f:facet>
                                    <h:outputText value="#{intervalo.tipoIntervalo.descricao}" />
                                </h:column>
                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText value="#{msg_aplic.prt_Antecipado_intervaloDe}"/>
                                    </f:facet>
                                    <h:outputText value="#{intervalo.intervaloDe}"/>
                                </h:column>
                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText  value="#{msg_aplic.prt_Antecipado_intervaloAte}" />
                                    </f:facet>
                                    <h:outputText  value="#{intervalo.intervaloAte}" />
                                </h:column>
                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText  value="#{msg_aplic.prt_Antecipado_tipoDesconto}" />
                                    </f:facet>
                                    <h:outputText  value="#{intervalo.tipoDesconto.descricao}" />
                                </h:column>
                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText  value="#{msg_aplic.prt_Antecipado_valor}" />
                                    </f:facet>
                                    <h:outputText  value="#{intervalo.valor}" >
                                        <f:converter converterId="FormatadorNumerico" />
                                    </h:outputText>
                                </h:column>
                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText  value="#{msg_aplic.prt_Antecipado_justificativaBonus}" />
                                    </f:facet>
                                    <h:outputText  value="#{intervalo.justificativaBonus.descricao}" />
                                </h:column>

                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText  value="#{msg_bt.btn_opcoes}" />
                                    </f:facet>
                                    <h:panelGroup>
                                        <a4j:commandButton id="btEditarIntervalo" action="#{DescontoControle.editarIntervalo}" reRender="panelIntervalo, panelErro"
                                                           value="#{msg_bt.btn_editar}" image="./imagens/botaoEditar.png" accesskey="6" styleClass="botoes"/>

                                        <h:outputText value="    "/>

                                        <a4j:commandButton id="btExcluirIntervalo" action="#{DescontoControle.removerIntervalo}" reRender="panelIntervalo, panelErro"
                                                           value="#{msg_bt.btn_excluir}" image="./imagens/botaoRemover.png" accesskey="7" styleClass="botoes"/>
                                    </h:panelGroup>
                                </h:column>
                            </h:dataTable>
                        </h:panelGrid>
                    </h:panelGrid>
                </h:panelGroup>

                <h:panelGroup id="empresas">

                    <h:panelGrid columns="1" width="100%" headerClass="subordinado" columnClasses="colunaCentralizada">
                        <h:panelGrid columns="2" width="100%" rowClasses="linhaImpar, linhaPar" columnClasses="classEsquerda, classDireita">
                            <h:outputText styleClass="tituloCampos" value="Selecionar empresas para aplicar o desconto:" />
                            <h:selectBooleanCheckbox value="#{DescontoControle.descontoVO.aplicarEmpresas}"
                                                     onchange="rerenderEmpresas()" />
                            <a4j:jsFunction name="rerenderEmpresas" reRender="empresas" />
                        </h:panelGrid>
                    </h:panelGrid>

                    <h:panelGroup rendered="#{DescontoControle.descontoVO.aplicarEmpresas}">
                        <div class="linha-titulo">Empresas onde se aplicam o desconto</div>

                        <rich:dataTable value="#{DescontoControle.descontoVO.empresas}"
                                        var="descontoEmpresa"
                                        style="width: 100%;">

                            <rich:column width="5%" style="text-align: center;"
                                         sortBy="#{descontoEmpresa.utilizaDesconto}">
                                <f:facet name="header">
                                    <h:selectBooleanCheckbox id="checkTodasEmpresas"
                                                             value="#{DescontoControle.checkTodasEmpresas}"
                                                             onclick="checkTodasEmpresas('checkTodasEmpresas', 'checkEmpresa')" />
                                </f:facet>
                                <h:selectBooleanCheckbox id="checkEmpresa" value="#{descontoEmpresa.utilizaDesconto}" />
                            </rich:column>

                            <rich:column width="50%" sortBy="#{descontoEmpresa.empresaVO.nome}" filterBy="#{descontoEmpresa.empresaVO.nome}">
                                <f:facet name="header">
                                    <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza texto-bold" value="Empresa"/>
                                </f:facet>
                                <h:outputText  styleClass="texto-cor-cinza texto-size-16 texto-font" value="#{descontoEmpresa.empresaVO.nome}" />
                            </rich:column>

                            <rich:column width="5%" sortBy="#{descontoEmpresa.empresaVO.estadoSigla}" filterBy="#{descontoEmpresa.empresaVO.estadoSigla}">
                                <f:facet name="header">
                                    <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza texto-bold" value="Estado"/>
                                </f:facet>
                                <h:outputText  styleClass="texto-cor-cinza texto-size-16 texto-font" value="#{descontoEmpresa.empresaVO.estadoSigla}" />
                            </rich:column>

                            <rich:column width="10%" sortBy="#{descontoEmpresa.empresaVO.cidadeNome}" filterBy="#{descontoEmpresa.empresaVO.cidadeNome}">
                                <f:facet name="header">
                                    <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza texto-bold" value="Cidade"/>
                                </f:facet>
                                <h:outputText  styleClass="texto-cor-cinza texto-size-16 texto-font" value="#{descontoEmpresa.empresaVO.cidadeNome}" />
                            </rich:column>

                            <rich:column width="30%" sortBy="#{descontoEmpresa.empresaVO.setor}" filterBy="#{descontoEmpresa.empresaVO.setor}">
                                <f:facet name="header">
                                    <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza texto-bold" value="Bairro"/>
                                </f:facet>
                                <h:outputText  styleClass="texto-cor-cinza texto-size-16 texto-font" value="#{descontoEmpresa.empresaVO.setor}" />
                            </rich:column>

                        </rich:dataTable>
                    </h:panelGroup>

                </h:panelGroup>

                <h:panelGrid id="panelErro" columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid columns="3" width="100%" styleClass="tabMensagens">
                        <h:panelGrid columns="1" width="100%">

                            <h:outputText value=" "/>

                        </h:panelGrid>
                        <h:commandButton rendered="#{DescontoControle.sucesso}" image="./imagens/sucesso.png"/>
                        <h:commandButton rendered="#{DescontoControle.erro}" image="./imagens/erro.png"/>
                        <h:panelGrid columns="1" width="100%">
                            <h:outputText id="msgDesconto" styleClass="mensagem" value="#{DescontoControle.mensagem}"/>
                            <h:outputText id="msgDescontoDet" styleClass="mensagemDetalhada" value="#{DescontoControle.mensagemDetalhada}"/>
                        </h:panelGrid>
                    </h:panelGrid>
                    <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                        <h:panelGroup>
                            <a4j:commandButton id="novo" immediate="true" action="#{DescontoControle.novo}"
                                             value="#{msg_bt.btn_novo}"
                                             alt="#{msg.msg_novo_dados}" accesskey="1" styleClass="botoes nvoBt btSec"/>

                            <h:outputText value="    "/>

                            <a4j:commandButton id="salvar" action="#{DescontoControle.gravar}" value="#{msg_bt.btn_gravar}"
                                             alt="#{msg.msg_gravar_dados}" accesskey="2" styleClass="botoes nvoBt"/>

                            <h:outputText value="    "/>

                            <h:panelGroup id="grupoBtnExcluir">
                                <a4j:commandButton id="excluir"  reRender="mdlMensagemGenerica"
                                                 oncomplete="#{DescontoControle.msgAlert}" action="#{DescontoControle.confirmarExcluir}"
                                                 value="#{msg_bt.btn_excluir}" title="#{msg.msg_excluir_dados}" accesskey="3" styleClass="botoes nvoBt btSec btPerigo"/>
                            </h:panelGroup>

                            <h:outputText value="    "/>

                            <a4j:commandButton id="consultar" immediate="true" action="#{DescontoControle.inicializarConsultar}"
                                               value="#{msg_bt.btn_voltar_lista}" alt="#{msg.msg_consultar_dados}"
                                             accesskey="4" styleClass="botoes nvoBt btSec"/>
                            <h:outputText value="    "/>
                            
                            <a4j:commandLink action="#{DescontoControle.realizarConsultaLogObjetoSelecionado}"
                                               reRender="form"
                                               oncomplete="abrirPopup('visualizadorLogForm.jsp', 'VisualizadorLog', 785, 595);fireElementFromAnyParent('form:btnAtualizaTempo')"
                                               title="Visualizar Log" styleClass="botoes nvoBt btSec" style="display: inline-block;padding-bottom: 10px;">
                                <i class="fa-icon-list"></i>
                            </a4j:commandLink>


                        </h:panelGroup>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
        </h:form>
    </h:panelGrid>
    <%@include file="includes/include_modal_mensagem_generica.jsp"%>
</f:view>
<script>
    document.getElementById("form:descricao").focus();
</script>
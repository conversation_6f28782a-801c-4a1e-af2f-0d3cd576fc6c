<%@include file="includes/imports.jsp" %>
<h:panelGroup rendered="#{MensagemBuilderControle.building and MensagemBuilderControle.configEnvio}" layout="block"
              style="min-height: 720px; margin-top: 60px; ">

    <div class="linha" style="display: flex">
        <div class="col-md-6">

            <!-- Painel Remetente -->
            <table width="95%" border="0" align="left" cellpadding="0" cellspacing="0" bgcolor="#fff"
                   style="padding: 10px; ">
                <tr>
                    <td align="left" valign="top" style="padding-bottom: 5px;">
                        <h:panelGrid style="width: 100%" columnClasses="classEsquerda, classDireita" columns="2">

                            <h:outputText rendered="#{MalaDiretaControle.malaDiretaVO.codigo > 0}"
                                          style="text-align: left; display: block; margin-right: 10px" styleClass="text" value="C�digo:"/>
                            <h:outputText style="text-align: left; margin-bottom: 10px; display: block; font-weight: bold" styleClass="text"
                                          rendered="#{MalaDiretaControle.malaDiretaVO.codigo > 0}"
                                          value="#{MalaDiretaControle.malaDiretaVO.codigo}"/>

                            <h:outputText style="text-align: left; display: block; margin-right: 10px" styleClass="text" value="Meio de envio:"/>
                            <h:outputText style="text-align: left; margin-bottom: 10px; display: block;" styleClass="text"
                                          value="#{MensagemBuilderControle.meioEnvio.descricao}"/>

                            <h:outputText style="text-align: left; display: block; margin-right: 10px" styleClass="text" value="#{msg_aplic.prt_MalaDireta_remetente}"/>
                            <h:panelGroup id="panelResponsavelGrupo">
                                <h:inputText id="textColaboradorResponsavel" size="#{MalaDiretaControle.malaDiretaVO.sizeTitulo}"
                                             onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form"
                                             style="width: 100%; font-size: 12px; padding: 15px"
                                             value="#{MalaDiretaControle.malaDiretaVO.remetente.nome}"/>
                                <rich:suggestionbox height="100" width="400"
                                                    for="textColaboradorResponsavel"
                                                    suggestionAction="#{MalaDiretaControle.executarAutocompleteRemetente}"
                                                    minChars="1" rowClasses="20"
                                                    status="statusInComponent"
                                                    nothingLabel="#{msg_aplic.prt_New_Mala_Direta_Form_reponsavel}"
                                                    var="result" fetchValue="#{result.nome}" id="suggestionResponsavel">
                                    <h:column>
                                        <h:outputText value="#{result.nome}"/>
                                    </h:column>
                                    <a4j:support event="onselect" action="#{MalaDiretaControle.setarRemetente}"/>
                                </rich:suggestionbox>
                            </h:panelGroup>

                            <h:outputText style="text-align: left; display: block;" styleClass="text" value="#{msg_aplic.prt_MalaDireta_titulo}"/>
                            <h:panelGroup>
                                <h:inputText id="titulo" size="#{MalaDiretaControle.malaDiretaVO.sizeTitulo}" maxlength="#{MalaDiretaControle.malaDiretaVO.maxLengthTitulo}"
                                             styleClass="form"
                                             disabled="#{MalaDiretaControle.malaDiretaVO.contatoDireto}"
                                             style="margin-top: 10px; width: 100%; font-size: 12px;padding: 15px"
                                             value="#{MalaDiretaControle.malaDiretaVO.titulo}"/>
                                <rich:toolTip for="titulo" followMouse="true" direction="top-right"
                                              style="width:300px; height:#{MalaDiretaControle.tamanhoToolTip}; "
                                              showDelay="200">
                                    <h:outputText styleClass="tituloCampos" escape="false"
                                                  value="#{msg.msg_tip_tituloMail}#{MalaDiretaControle.termosFiscalizados}#{msg.msg_tip_tituloMailPontos}"/>
                                </rich:toolTip>
                            </h:panelGroup>

                        </h:panelGrid>
                    </td>
                </tr>
            </table>

            <!-- Painel Destinat�rio -->
            <table width="95%" border="0" align="left" cellpadding="0" cellspacing="0"
                   style="padding: 10px; ">

                <tr>
                    <td align="left" valign="top" style="padding-bottom: 5px;">
                        <div style="clear: both;" class="text">
                            <h:outputText
                                    rendered="#{!MalaDiretaControle.malaDiretaVO.contatoDireto && !MalaDiretaControle.malaDiretaVO.crmExtra && !MalaDiretaControle.contatoInstantaneo}"
                                    style="font-weight: bold"
                                    value="Selecione o Grupo de Destinat�rios por:"/>
                            <h:outputText
                                    rendered="#{!MalaDiretaControle.malaDiretaVO.contatoDireto && MalaDiretaControle.malaDiretaVO.crmExtra && !MalaDiretaControle.contatoInstantaneo}"
                                    style="font-weight: bold"
                                    value="Selecione o Grupo de Alunos por:"/>
                            <h:outputText
                                    rendered="#{MalaDiretaControle.malaDiretaVO.contatoDireto && !MalaDiretaControle.contatoInstantaneo}"
                                    style="font-weight: bold"
                                    value="Esta mensagem n�o pode ser editada pois foi um contato direto com um aluno ou grupo de alunos."/>
                            <h:outputText rendered="#{MalaDiretaControle.contatoInstantaneo}"
                                          style="font-weight: bold"
                                          value="Esta mensagem n�o pode ser editada pois foi um contato instant�neo."/>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td>
                        <h:panelGrid width="100%" columns="1" columnClasses="classDireitaConfiguracao" cellpadding="0"
                                     rowClasses="linhaTop"
                                     rendered="#{!MalaDiretaControle.malaDiretaVO.contatoDireto && !MalaDiretaControle.contatoInstantaneo}">

                            <h:panelGroup>
                                <h:panelGrid id="pnlEvtPreDef" columnClasses="classeEsquerda,classeDireita"
                                             styleClass="text"
                                             width="100%" columns="2"
                                             style="padding: 0 !important;">

                                    <h:outputText styleClass="text" value="Importar de uma lista"
                                                  rendered="#{MalaDiretaControle.malaDiretaVO.crmExtra}"/>
                                    <h:selectBooleanCheckbox value="#{MalaDiretaControle.malaDiretaVO.importarLista}"
                                                             rendered="#{MalaDiretaControle.malaDiretaVO.crmExtra}"
                                                             disabled="#{MalaDiretaControle.malaDiretaVO.listaImportada}">
                                        <a4j:support event="onchange" reRender="form:mensagembuilder"/>
                                    </h:selectBooleanCheckbox>


                                    <h:outputText styleClass="text"
                                                  value="#{msg_aplic.prt_New_Mala_Direta_Form_pre_definido}"
                                                  rendered="#{!MalaDiretaControle.malaDiretaVO.importarLista}"/>
                                    <h:selectOneMenu value="#{MalaDiretaControle.malaDiretaVO.cfgEvento.eventoCodigo}"
                                                     rendered="#{!MalaDiretaControle.malaDiretaVO.importarLista}"
                                                     id="eventoPreDefLabel"
                                                     style="width: 100%; font-size: 12px">
                                        <f:selectItem itemValue="" itemLabel=""/>
                                        <f:selectItems value="#{MalaDiretaControle.listaSelectItemEventos}"/>
                                        <a4j:support event="onchange" reRender="form:mensagembuilder"
                                                     action="#{MalaDiretaControle.limparDias}"/>
                                    </h:selectOneMenu>

                                    <h:outputText
                                            rendered="#{MalaDiretaControle.malaDiretaVO.cfgEvento.ocorrencia != 5 && !MalaDiretaControle.malaDiretaVO.importarLista}"
                                            styleClass="text"
                                            value="Evento da Academia:"/>
                                    <h:selectOneMenu
                                            rendered="#{MalaDiretaControle.malaDiretaVO.cfgEvento.ocorrencia != 5  && !MalaDiretaControle.malaDiretaVO.importarLista}"
                                            style="width: 100%; font-size: 12px; margin-top: 10px"
                                            value="#{MalaDiretaControle.malaDiretaVO.mailingFiltros.evento}">
                                        <f:selectItems value="#{MalaDiretaControle.listaEventos}"/>
                                    </h:selectOneMenu>
                                </h:panelGrid>
                                <rich:panel
                                        rendered="#{MalaDiretaControle.malaDiretaVO.importarLista && !MalaDiretaControle.malaDiretaVO.listaImportada}"
                                        style="width: 100%; height: 100%; border: none">
                                    <h:outputText styleClass="text" style="text-align: left; font-size: 11px;"
                                                  value="A lista permite que sejam adicionados alunos que j� est�o na base e cadastros que ainda n�o est�o. Registros sem matr�cula, ou cuja matr�cula n�o existir no sistema, ser�o adicionados como receptivos."/>
                                </rich:panel>
                                <rich:panel rendered="#{MalaDiretaControle.malaDiretaVO.listaImportada}"
                                            style="width: 100%; height: 100%; border: none;">
                                    <h:outputText styleClass="text"
                                                  style="text-align: left; font-size: 11px; font-weight: bold"
                                                  value="Lista j� importada. N�o � poss�vel importar outra."/>
                                </rich:panel>
                            </h:panelGroup>


                            <h:panelGrid id="painelCfgImportar" styleClass="text" columns="1" style="width: 100%; margin-top: 20px"
                                         rendered="#{MalaDiretaControle.malaDiretaVO.importarLista}">
                                <rich:fileUpload
                                        fileUploadListener="#{MalaDiretaControle.uploadArquivoListener}"
                                        disabled="#{MalaDiretaControle.malaDiretaVO.listaImportada}"
                                        immediateUpload="true" id="imagemModeloUpload"
                                        acceptedTypes="xls,xlsx" allowFlash="false"
                                        listHeight="58px"
                                        cancelEntryControlLabel="Cancelar"
                                        addControlLabel="Adicionar lista "
                                        clearControlLabel="Remover"
                                        clearAllControlLabel="Remover Todos"
                                        doneLabel="Conclu�do"
                                        sizeErrorLabel="Limite de tamanho atingido"
                                        uploadControlLabel="Carregar"
                                        transferErrorLabel="Erro na transfer�ncia"
                                        stopControlLabel="Parar"
                                        stopEntryControlLabel="Parar"
                                        progressLabel="Carregando"
                                        maxFilesQuantity="1">
                                    <a4j:support event="oncomplete"
                                                 oncomplete="#{MalaDiretaControle.mensagemNotificar}"/>
                                    <a4j:support event="onclear" reRender="painelCfgImportar"
                                                 action="#{MalaDiretaControle.limparLista}"/>
                                </rich:fileUpload>

                                <a href="modelo/importar_meta_exemplo.xlsx">Baixar modelo de arquivo</a>

                            </h:panelGrid>

                            <h:panelGrid id="painelCfgEvento" styleClass="text" columns="1" style="width: 100%; margin-top: 20px"
                                         rendered="#{!MalaDiretaControle.malaDiretaVO.importarLista}">
                                <!--configuracao de evento-->
                                <rich:panel rendered="#{MalaDiretaControle.eventoEscolhido}"
                                            style="width: 100%; height: 100%;">
                                    <h:outputText style="font-weight: bold" value="Periodicidade" />
                                    <h:selectOneRadio rendered="#{!MalaDiretaControle.malaDiretaVO.crmExtra}"
                                                      id="periodicidadeEvento"
                                                      styleClass="text" layout="lineDirection"
                                                      value="#{MalaDiretaControle.malaDiretaVO.cfgEvento.ocorrencia}"
                                                      disabled="#{(MalaDiretaControle.malaDiretaVO.codigo != 0 )}"  >
                                        <f:selectItems
                                                value="#{MalaDiretaControle.malaDiretaVO.cfgEvento.listaOcorrencias}"/>
                                        <a4j:support event="onchange" action="#{MalaDiretaControle.alterarTipo}"
                                                     focus="#{MalaDiretaControle.irPara}"
                                                     reRender="form:mensagembuilder"/>
                                    </h:selectOneRadio>

                                    <!-- Pos Venda -->

                                    <h:panelGroup
                                            rendered="#{MalaDiretaControle.malaDiretaVO.cfgEvento.posVenda && MalaDiretaControle.malaDiretaVO.cfgEvento.ocorrencia != 6}">
                                        <h:panelGrid columns="3">
                                            <h:outputText styleClass="text" title="#{msg_aplic.prt_titlePosVenda}"
                                                          value="#{msg_aplic.prt_New_Mala_Direta_Form_qtd_pos} "/>
                                            <rich:inputNumberSpinner
                                                    id="qtdadeDiasPosVendaCRM"
                                                    value="#{MalaDiretaControle.malaDiretaVO.diasPosVenda}"
                                                    minValue="0" maxValue="999999999"/>
                                            <h:outputText styleClass="text" value="Dia(s)"
                                                          title="#{msg_aplic.prt_titlePosVenda}"/>
                                        </h:panelGrid>
                                        <h:outputText styleClass="text"
                                                      value=" #{msg_aplic.prt_New_Mala_Direta_Form_serao_considerados} "/><h:outputText
                                            style="font-weight: bold;font-size: unset;" value="ATIVOS"/>
                                        <h:outputText styleClass="text"
                                                      value=" #{msg_aplic.prt_New_Mala_Direta_Form_serao_matriculados_filtro_padrao}"/>
                                    </h:panelGroup>

                                    <%-- Quantidade de acesso em um intervalo de dias --%>
                                    <h:panelGroup
                                            rendered="#{MalaDiretaControle.malaDiretaVO.cfgEvento.acessosEmIntervaloDias}">
                                        <h:panelGrid columns="2">
                                            <h:outputText styleClass="text"
                                                          value="#{msg_aplic.prt_New_Mala_Direta_Form_qtd_min} "/>
                                            <rich:inputNumberSpinner
                                                    id="quantidadeMinimaDeAcessoCRM"
                                                    value="#{MalaDiretaControle.malaDiretaVO.quantidadeMinimaAcessos}"
                                                    minValue="0" maxValue="31"/>
                                            <h:outputText styleClass="text"
                                                          value="#{msg_aplic.prt_New_Mala_Direta_Form_qtd_max} "/>
                                            <rich:inputNumberSpinner
                                                    id="quantidadeMaximaDeAcessoCRM"
                                                    value="#{MalaDiretaControle.malaDiretaVO.quantidadeMaximaAcessos}"
                                                    minValue="0" maxValue="31"/>
                                            <h:outputText styleClass="text" value="Intervalo de dias: "/>
                                            <rich:inputNumberSpinner
                                                    id="intervaloDeDiasAcessoCRM"
                                                    value="#{MalaDiretaControle.malaDiretaVO.intervaloDias}"
                                                    minValue="0" maxValue="99999"/>
                                        </h:panelGrid>
                                    </h:panelGroup>

                                    <%-- Op�oesextras do evento "Cancelados" --%>
                                    <h:panelGroup
                                            rendered="#{MalaDiretaControle.malaDiretaVO.cfgEvento.cancelados && MalaDiretaControle.malaDiretaVO.cfgEvento.ocorrencia != 10}">
                                        <h:panelGrid columns="4">
                                            <h:outputText styleClass="text" value="Intervalo de dias: "
                                                          title="Dias ap�s o cancelamento"/>
                                            <rich:inputNumberSpinner
                                                    id="intervaloDiasCancelados"
                                                    value="#{MalaDiretaControle.malaDiretaVO.intervaloDias}"
                                                    minValue="0" maxValue="99999"/>
                                        </h:panelGrid>
                                    </h:panelGroup>

                                    <%-- TIPO CANCELAMENTO --%>
                                    <h:panelGroup
                                            rendered="#{MalaDiretaControle.malaDiretaVO.cfgEvento.cancelados}">
                                        <h:outputText styleClass="text" value="Tipo do cancelamento: " title="Defina o tipo de cancelamento a ser considerado no mailing. TODOS: Ser� considerado todos
                                             os cancelamentos, independente se foi algum usu�rio do sistema que realizou ou se foi autom�tico pela recorr�ncia. MANUAL: Considera somente os cancelamentos
                                             realizados por algum usu�rio do sistema manualmente. AUTOM�TICO: Considera apenas os cancelamentos realizados automaticamente pelo sistema, pelo usu�rio Recorr�ncia."/>
                                        <h:selectOneMenu id="tipoCancelamentoEvento"
                                                         value="#{MalaDiretaControle.malaDiretaVO.tipoCancelamento}">
                                            <f:selectItem itemValue="0" itemLabel="Todos"/>
                                            <f:selectItem itemValue="1" itemLabel="Manual"/>
                                            <f:selectItem itemValue="2" itemLabel="Autom�tico"/>
                                        </h:selectOneMenu>
                                    </h:panelGroup>

                                    <!-- RISCO-->
                                    <rich:dataGrid id="dtgRiscos"
                                                   rendered="#{MalaDiretaControle.malaDiretaVO.cfgEvento.grupoRisco}"
                                                   value="#{MalaDiretaControle.malaDiretaVO.cfgEvento.riscos}"
                                                   width="100%" columns="4" columnClasses="semBorda"
                                                   styleClass="semBorda"
                                                   cellpadding="0" cellspacing="0" var="risco">
                                        <rich:column>
                                            <h:selectBooleanCheckbox value="#{risco.selecionado}" id="grauRiscoCRM">
                                            </h:selectBooleanCheckbox>
                                            <h:outputText value="#{risco.label}"/>
                                        </rich:column>

                                    </rich:dataGrid>
                                    <!-- PENDCIAS-->
                                    <rich:dataGrid id="dtgPendencias"
                                                   rendered="#{MalaDiretaControle.malaDiretaVO.cfgEvento.evtPendencias}"
                                                   value="#{MalaDiretaControle.malaDiretaVO.cfgEvento.pendencias}"
                                                   width="100%" columns="4" columnClasses="semBorda"
                                                   styleClass="semBorda"
                                                   cellpadding="0" cellspacing="0" var="pendencia">
                                        <rich:column>
                                            <h:selectBooleanCheckbox value="#{pendencia.selecionado}"
                                                                     id="selecionandoPendencia">
                                            </h:selectBooleanCheckbox>
                                            <h:outputText value="#{pendencia.label}"/>
                                        </rich:column>

                                    </rich:dataGrid>

                                    <!-- GENERICO-->
                                    <h:panelGroup rendered="#{MalaDiretaControle.malaDiretaVO.cfgEvento.agendamentosProduto
                                                              || MalaDiretaControle.malaDiretaVO.cfgEvento.compra
                                                              || MalaDiretaControle.malaDiretaVO.cfgEvento.evtAmbientes
                                                              || MalaDiretaControle.malaDiretaVO.cfgEvento.produtosVendidos
                                                              || MalaDiretaControle.malaDiretaVO.cfgEvento.agendar
                                                              || MalaDiretaControle.malaDiretaVO.cfgEvento.faturar
                                                              || MalaDiretaControle.malaDiretaVO.cfgEvento.profissional}">
                                        <div style="width: 100%; max-height: 340px; overflow-x: visible; overflow-y: scroll;">
                                            <rich:dataGrid id="dtgAgendamentosProduto"
                                                           value="#{MalaDiretaControle.malaDiretaVO.cfgEvento.listaGenerica}"
                                                           width="100%" columns="3" columnClasses="semBorda"
                                                           styleClass="semBorda"
                                                           cellpadding="0" cellspacing="0" var="gen">
                                                <rich:column>
                                                    <h:selectBooleanCheckbox value="#{gen.selecionado}">
                                                    </h:selectBooleanCheckbox>
                                                    <h:outputText value="#{gen.label}"/>
                                                </rich:column>

                                            </rich:dataGrid>
                                        </div>
                                    </h:panelGroup>

                                    <!--SALDO_PONTOS-->
                                    <h:panelGrid columns="2"
                                                 rendered="#{MalaDiretaControle.malaDiretaVO.cfgEvento.saldoPontos}">
                                        <h:outputText styleClass="text" value="Quantidade de pontos: "/>
                                        <rich:inputNumberSpinner
                                                id="quantidadeDePontosCRM"
                                                value="#{MalaDiretaControle.malaDiretaVO.cfgEvento.nrFaltas}"
                                                minValue="0" maxValue="999999999"/>
                                    </h:panelGrid>

                                    <!--FALTOSOS-->
                                    <h:panelGrid columns="2"
                                                 rendered="#{MalaDiretaControle.malaDiretaVO.cfgEvento.faltosos}">
                                        <h:outputText styleClass="text" value="Nr. dias de falta: "/>
                                        <rich:inputNumberSpinner
                                                id="nDiasDeFaltaCRM"
                                                value="#{MalaDiretaControle.malaDiretaVO.cfgEvento.nrFaltas}"
                                                inputSize="3"/>
                                    </h:panelGrid>
                                    <h:panelGroup rendered="#{MalaDiretaControle.malaDiretaVO.cfgEvento.faltosos}">
                                        <h:selectBooleanCheckbox
                                                value="#{MalaDiretaControle.malaDiretaVO.cfgEvento.nrFaltasMaior}"/>
                                        <h:outputText styleClass="text"
                                                      value="#{msg_aplic.prt_New_Mala_Direta_Form_pesquisar_tambem}"/>
                                    </h:panelGroup>

                                    <!--FALTOSOS-->
                                    <h:panelGrid columns="2"
                                                 rendered="#{MalaDiretaControle.malaDiretaVO.cfgEvento.freepass}">
                                        <h:outputText styleClass="text"
                                                      value="#{msg_aplic.prt_New_Mala_Direta_Form_pesquisar_dias_inicio}"/>
                                        <rich:inputNumberSpinner
                                                value="#{MalaDiretaControle.malaDiretaVO.cfgEvento.nrDiasInicioFreePass}"
                                                inputSize="3" id="inicioFreePassCRM"/>
                                    </h:panelGrid>

                                    <!--CONTRATO_CREDITO_TREINO-->
                                    <h:panelGroup layout="block"
                                                  id="creditoTreinoConfg"
                                                  style="display: inline-flex;"
                                                  rendered="#{MalaDiretaControle.malaDiretaVO.cfgEvento.creditoTreino}">

                                        <h:outputText styleClass="text"
                                                      value="#{msg_aplic.prt_New_Mala_Direta_Form_Qtd_creditos}"/>

                                        <h:outputText styleClass="text" style="margin-left: 15px; margin-right: 5px"
                                                      value="De "/>

                                        <rich:inputNumberSpinner
                                                id="qtdeCreditoInicialCrm"
                                                value="#{MalaDiretaControle.malaDiretaVO.cfgEvento.minValorInt}"
                                                inputSize="3"/>

                                        <h:outputText styleClass="text"
                                                      value=" #{msg_aplic.prt_New_Mala_Direta_Form_ate} "
                                                      style="margin-left: 5px; margin-right: 5px"/>

                                        <rich:inputNumberSpinner
                                                id="qtdeCreditoFinalCrm"
                                                value="#{MalaDiretaControle.malaDiretaVO.cfgEvento.maxValorInt}"
                                                inputSize="3"/>

                                    </h:panelGroup>

                                    <!--INDICADOS-->
                                    <h:panelGroup rendered="#{MalaDiretaControle.malaDiretaVO.cfgEvento.indicados}">
                                        <h:selectBooleanCheckbox
                                                value="#{MalaDiretaControle.malaDiretaVO.cfgEvento.naoClientes}"/>
                                        <h:outputText styleClass="text"
                                                      value="#{msg_aplic.prt_New_Mala_Direta_Form_nao_tornam_alunos}"/>
                                    </h:panelGroup>
                                    <!--DEBITO-->
                                    <h:panelGrid columns="2"
                                                 rendered="#{MalaDiretaControle.malaDiretaVO.cfgEvento.debito}">
                                        <h:outputText styleClass="text" value="Min. de dias em debito: "/>
                                        <rich:inputNumberSpinner
                                                value="#{MalaDiretaControle.malaDiretaVO.cfgEvento.minimoDiasVencido}"
                                                minValue="1"
                                                inputSize="5"
                                                maxValue="99999"/>
                                        <h:outputText styleClass="text" value="Faixa de valor: "/>

                                        <h:panelGrid columns="4" cellspacing="0">
                                            <h:outputText styleClass="text" value="#{msg_aplic.prt_de}"/>
                                            <h:inputText size="6" styleClass="form" id="faixaValorInicial"
                                                         value="#{MalaDiretaControle.malaDiretaVO.cfgEvento.minValor}">
                                                <f:converter converterId="FormatadorNumerico"/>
                                            </h:inputText>
                                            <h:outputText styleClass="text" value="#{msg_aplic.prt_ate}  "/>

                                            <h:inputText size="6" styleClass="form"
                                                         id="faixaFinalInicial"
                                                         value="#{MalaDiretaControle.malaDiretaVO.cfgEvento.maxValor}">
                                                <f:converter converterId="FormatadorNumerico"/>
                                            </h:inputText>

                                        </h:panelGrid>
                                    </h:panelGrid>

                                    <!--PARCELAS VENCIDAS-->
                                    <h:panelGrid columns="2"
                                                 rendered="#{MalaDiretaControle.malaDiretaVO.cfgEvento.parcelasVencidas}">
                                        <h:outputText styleClass="text" value="Qtd. min de parcelas vencidas: "/>
                                        <rich:inputNumberSpinner
                                                value="#{MalaDiretaControle.malaDiretaVO.cfgEvento.qtdMinParcelasVencidas}"
                                                minValue="1" inputSize="3"/>
                                        <h:outputText styleClass="text" value="Vencidas de: "/>
                                        <h:panelGroup layout="block">
                                            <rich:inputNumberSpinner style="float: left;"
                                                                     value="#{MalaDiretaControle.malaDiretaVO.cfgEvento.diasParcelasVencidasInicial}"
                                                                     minValue="0" maxValue="365" inputSize="4"/>
                                            <h:outputText styleClass="text" value="at�"
                                                          style="float: left; margin-left: 6px; margin-right: 6px;"/>
                                            <div style="display: inline;"
                                                 title="Caso n�o seja informado o per�odo final, sera considerado as parcelas vencidas com quantidade de dia maiores que o per�odo inicial">
                                                <rich:inputNumberSpinner style="float: left;"
                                                                         value="#{MalaDiretaControle.malaDiretaVO.cfgEvento.diasParcelasVencidasFinal}"
                                                                         minValue="0" inputSize="4" maxValue="365"/>
                                            </div>
                                            <h:outputText style="margin-left: 6px" styleClass="text" value=" dias"/>
                                        </h:panelGroup>

                                    </h:panelGrid>

                                    <h:panelGrid columns="2"
                                                 rendered="#{MalaDiretaControle.malaDiretaVO.cfgEvento.parcelasRecorrencia}">
                                        <h:outputText styleClass="text"
                                                      value="#{msg_aplic.prt_New_Mala_Direta_Form_Codigo} Retorno: "/>

                                        <h:inputText
                                                value="#{MalaDiretaControle.malaDiretaVO.cfgEvento.codigoErroRemessa}"
                                                size="5" maxlength="5"/>

                                        <h:panelGroup layout="block" style="text-align: right">
                                            <h:outputText styleClass="text"
                                                          value="#{MalaDiretaControle.malaDiretaVO.cfgEvento.labelDatas} >= "/>
                                        </h:panelGroup>
                                        <h:panelGroup layout="block" style="display: inline-flex;">
                                            <rich:inputNumberSpinner
                                                    id="diasRemessa"
                                                    value="#{MalaDiretaControle.malaDiretaVO.cfgEvento.diasRemessa}"
                                                    inputSize="3"/>
                                            <h:outputText styleClass="text"
                                                          style="margin-left: 5px"
                                                          value="dias"/>
                                        </h:panelGroup>
                                    </h:panelGrid>

                                    <!--DATAS-->

                                    <h:panelGrid columns="1">

                                        <h:panelGroup rendered="#{MalaDiretaControle.malaDiretaVO.cfgEvento.produtosVencidos || MalaDiretaControle.malaDiretaVO.cfgEvento.produtosVencendo
                                         && MalaDiretaControle.malaDiretaVO.cfgEvento.ocorrencia != 0 || MalaDiretaControle.malaDiretaVO.cfgEvento.parcelasVecendo}">
                                            <h:outputText styleClass="text" value="Produto: "
                                                          rendered="#{!MalaDiretaControle.malaDiretaVO.cfgEvento.parcelasVecendo}"
                                            />
                                            <h:inputText id="itemProdutos" size="30"
                                                         onblur="blurinput(this);" styleClass="form"
                                                         rendered="#{!MalaDiretaControle.malaDiretaVO.cfgEvento.parcelasVecendo}"
                                                         value="#{MalaDiretaControle.malaDiretaVO.cfgEvento.produtoVO.descricao}"
                                                         onkeydown="bloquearCtrlJ()">
                                            </h:inputText>
                                            <rich:suggestionbox height="200" width="200"
                                                                for="itemProdutos"
                                                                rendered="#{!MalaDiretaControle.malaDiretaVO.cfgEvento.parcelasVecendo}"
                                                                suggestionAction="#{MalaDiretaControle.executarAutocompleteConsultaProduto}"
                                                                minChars="1" rowClasses="20"
                                                                status="statusHora" immediate="true"
                                                                nothingLabel="Nenhum Produto encontrado !"
                                                                var="result" id="suggestionNomeServicos">

                                                <a4j:support event="onselect" focus="itemProdutos" reRender="form:mensagembuilder"
                                                             action="#{MalaDiretaControle.selecionarServicoSuggestionBox}"/>
                                                <h:column>
                                                    <h:outputText value="#{result.descricao}"
                                                                  title="#{result.observacao}"/>
                                                </h:column>
                                            </rich:suggestionbox>
                                            <h:panelGrid columns="1" styleClass="tabFormSubordinada">
                                                <rich:dataTable
                                                        value="#{MalaDiretaControle.malaDiretaVO.cfgEvento.listaProdutosVOs}"
                                                        rendered="#{!empty MalaDiretaControle.malaDiretaVO.cfgEvento.listaProdutosVOs}"
                                                        var="produto" width="100%" id="itensProdutos"
                                                        headerClass="subordinado"
                                                        styleClass="tabFormSubordinada"
                                                        style="position:relative; top:0px; left:50px;"
                                                        rowClasses="linhaImpar, linhaPar"
                                                        columnClasses="colunaAlinhamento">

                                                    <rich:column>
                                                        <f:facet name="header">
                                                            <h:outputText
                                                                    value="#{msg_aplic.prt_New_Mala_Direta_Form_Codigo}"/>
                                                        </f:facet>
                                                        <h:outputText value="#{produto.codigo}"/>
                                                    </rich:column>

                                                    <rich:column>
                                                        <f:facet name="header">
                                                            <h:outputText
                                                                    value="#{msg_aplic.prt_New_Mala_Direta_Form_descricao}"/>
                                                        </f:facet>
                                                        <h:outputText value="#{produto.descricao}"/>
                                                    </rich:column>

                                                    <rich:column>
                                                        <f:facet name="header">
                                                            <h:outputText value="Excluir"/>
                                                        </f:facet>
                                                        <a4j:commandButton id="removerProduto"
                                                                           action="#{MalaDiretaControle.removerProdutoVencidos}"
                                                                           onclick="fireElementFromAnyParent('form:btnAtualizaTempo')"
                                                                           reRender="form:mensagembuilder"
                                                                           value="#{msg_bt.btn_excluir}"
                                                                           image="./imagens/botaoRemover.png"
                                                                           focus="#{MalaDiretaControle.irPara}"
                                                                           styleClass="botoes"/>
                                                    </rich:column>

                                                </rich:dataTable>
                                            </h:panelGrid>


                                        </h:panelGroup>

                                        <h:panelGrid columns="3"
                                                     rendered="#{MalaDiretaControle.malaDiretaVO.cfgEvento.d && !MalaDiretaControle.malaDiretaVO.cfgEvento.parcelasRecorrencia}">
                                            <h:outputText styleClass="text"
                                                          value="#{MalaDiretaControle.malaDiretaVO.cfgEvento.labelDatas} h� "
                                                          rendered="#{MalaDiretaControle.malaDiretaVO.cfgEvento.aprDmais}"/>
                                            <rich:inputNumberSpinner
                                                    id="aprDmais"
                                                    value="#{MalaDiretaControle.malaDiretaVO.cfgEvento.diaMais}"
                                                    rendered="#{MalaDiretaControle.malaDiretaVO.cfgEvento.aprDmais}"
                                                    inputSize="5"
                                                    minValue="0"
                                                    maxValue="99999"/>
                                            <h:outputText styleClass="text" value=" dias"
                                                          rendered="#{MalaDiretaControle.malaDiretaVO.cfgEvento.aprDmais}"/>


                                            <h:outputText styleClass="text"
                                                          value="#{MalaDiretaControle.malaDiretaVO.cfgEvento.labelDatas} daqui a "
                                                          rendered="#{MalaDiretaControle.malaDiretaVO.cfgEvento.aprDMenos}"/>
                                            <rich:inputNumberSpinner
                                                    id="aprDMenos"
                                                    value="#{MalaDiretaControle.malaDiretaVO.cfgEvento.diaMenos}"
                                                    rendered="#{MalaDiretaControle.malaDiretaVO.cfgEvento.aprDMenos}"
                                                    inputSize="5"
                                                    minValue="0"
                                                    maxValue="99999"/>
                                            <h:outputText styleClass="text" value=" dias"
                                                          rendered="#{MalaDiretaControle.malaDiretaVO.cfgEvento.aprDMenos}"/>
                                        </h:panelGrid>


                                        <h:panelGroup
                                                rendered="#{MalaDiretaControle.malaDiretaVO.cfgEvento.apresentarDatas}">
                                            <h:outputText styleClass="text"
                                                          style="padding-right: 6px"
                                                          value="*#{MalaDiretaControle.malaDiretaVO.cfgEvento.labelDatas} entre:"/>
                                            <rich:calendar id="dataInicioC"
                                                           value="#{MalaDiretaControle.malaDiretaVO.cfgEvento.inicio}"
                                                           inputSize="10"
                                                           inputClass="form"
                                                           oninputblur="blurinput(this);"
                                                           oninputfocus="focusinput(this);"
                                                           datePattern="dd/MM/yyyy"
                                                           oninputchange="return validar_Data(this.id);"
                                                           enableManualInput="true"
                                                           zindex="2"
                                                           showWeeksBar="false"/>
                                            <h:outputText styleClass="text"
                                                          style="position:relative; top:0px; left:10px; padding-right: 6px"
                                                          value="e"/>
                                            <rich:spacer width="12px"/>
                                            <rich:calendar id="dataTerminoC"
                                                           value="#{MalaDiretaControle.malaDiretaVO.cfgEvento.fim}"
                                                           inputSize="10"
                                                           inputClass="form"
                                                           oninputblur="blurinput(this);"
                                                           oninputfocus="focusinput(this);"
                                                           oninputchange="return validar_Data(this.id);"
                                                           datePattern="dd/MM/yyyy"
                                                           enableManualInput="true" showWeeksBar="false"
                                                           zindex="2"/>
                                            <rich:spacer width="5px"/>
                                            <a4j:commandButton id="limparPeriodo"
                                                               onclick="fireElementFromAnyParent('form:btnAtualizaTempo');document.getElementById('form:dataInicioCInputDate').value='';document.getElementById('form:dataTerminoCInputDate').value='';"
                                                               image="/images/limpar.gif" title="Limpar per�odo."
                                                               status="false"/>
                                        </h:panelGroup>
                                    </h:panelGrid>

                                    <!--PARCELAS VENCENDO-->
                                    <h:panelGroup layout="block" id="pnlParcelasVencendo"
                                                  rendered="#{(MalaDiretaControle.malaDiretaVO.cfgEvento.parcelasVecendo || MalaDiretaControle.malaDiretaVO.cfgEvento.parcelasVencidas) && MalaDiretaControle.malaDiretaVO.email}">
                                        <h:selectBooleanCheckbox id="chkBoletoParcelasVencendo" styleClass="tooltipster"
                                                                 title="Essa configura��o � utilizada para enviar boletos para o alunos, por�m para que ela funcione <br> � necess�rio que na tela anterior tenha sido configurado a TAG_BOLETO."
                                                                 value="#{MalaDiretaControle.malaDiretaVO.cfgEvento.boletoParcelasVencendo}">
                                            <a4j:support event="onclick" reRender="panelTags,pnlParcelasVencendo"/>
                                        </h:selectBooleanCheckbox>
                                        <h:outputText id="txtBoleto" styleClass="text tooltipster"
                                                      title="Essa configura��o � utilizada para enviar boletos para o alunos, por�m para que ela funcione <br> � necess�rio que na tela anterior tenha sido configurado a TAG_BOLETO."
                                                      value="Pagamentos por conv�nio (Boleto)"/>

                                        <c:if test="${MalaDiretaControle.malaDiretaVO.cfgEvento.boletoParcelasVencendo}">
                                            <br/>
                                            <h:selectBooleanCheckbox id="modeloPadraoBoleto" styleClass="tooltipster"
                                                                     title="Ao marcar essa configura��o, o email que o aluno ir� receber ter� um corpo padr�o Pacto.<br> Neste email padr�o ter� a linha digit�vel e tamb�m ter� o boleto anexado caso o aluno queira baixar. <br> Agora com a configura��o desmarcada, n�o ter� boleto anexado e ser� enviado somente o link para o aluno fazer o download do boleto."
                                                                     value="#{MalaDiretaControle.malaDiretaVO.cfgEvento.modeloPadraoBoleto}">
                                                <a4j:support event="onclick"
                                                             reRender="form:inputMensagem,form:panelGridMaladireta"/>
                                            </h:selectBooleanCheckbox>
                                            <h:outputText styleClass="text tooltipster"
                                                          title="Ao marcar essa configura��o, o email que o aluno ir� receber ter� um corpo padr�o Pacto.<br> Neste email padr�o ter� a linha digit�vel e tamb�m ter� o boleto anexado caso o aluno queira baixar. <br> Agora com a configura��o desmarcada, n�o ter� boleto anexado e ser� enviado somente o link para o aluno fazer o download do boleto."
                                                          value="Utilizar modelo padr�o de email para boleto"/>
                                        </c:if>
                                    </h:panelGroup>

                                    <%--INFORMACOES CART�ES VENCENDO--%>
                                    <h:panelGrid columns="1"
                                                 rendered="#{MalaDiretaControle.malaDiretaVO.cfgEvento.cartoesVencendo}">
                                        <h:outputText styleClass="text" style="font-style: italic;"
                                                      value="Aten��o: Nas datas independente do dia selecionado ser� considerado o m�s completo"
                                                      rendered="#{MalaDiretaControle.malaDiretaVO.cfgEvento.ocorrencia == 1}"/>
                                        <h:outputText styleClass="text" style="font-style: italic;"
                                                      value="Aten��o: Este filtro ir� apresentar a cada m�s os alunos do m�s subsequente"
                                                      rendered="#{MalaDiretaControle.malaDiretaVO.cfgEvento.ocorrencia == 4}"/>
                                    </h:panelGrid>

                                    <%-- CART�ES VENCIDOS--%>
                                    <h:panelGroup
                                            rendered="#{MalaDiretaControle.malaDiretaVO.cfgEvento.cartoesVencidos}">
                                        <h:panelGrid columns="1">
                                            <h:outputText styleClass="text" style="font-style: italic"
                                                          value="Considera os cart�es vencidos dos alunos, se o cart�o do aluno vence este m�s, n�o ser� considerado aqui, somente os vencidos. Caso queira filtrar uma situa��o espec�fica do aluno, utilize o filtro de 'Situa��o' aqui do mailing"/>
                                        </h:panelGrid>
                                    </h:panelGroup>

                                    <%--INFORMACOES PGTOS. N�O APROVADOS DE REMESSAS--%>
                                    <h:panelGroup layout="block"
                                                  rendered="#{MalaDiretaControle.malaDiretaVO.cfgEvento.itensNaoAprovados}">
                                        <h:outputText styleClass="texto-cor-vermelho texto-bold texto-size-16"
                                                      style="font-style: italic;"
                                                      value="Aten��o:"/>
                                        <h:outputText styleClass="texto-cor-vermelho texto-size-16"
                                                      style="padding-left: 5px"
                                                      value="Esse filtro � compat�vel somente para remessas da CIELO."/>
                                    </h:panelGroup>

                                </rich:panel>
                            </h:panelGrid>

                            <h:panelGrid id="painelCfgConvenio" styleClass="text" columns="1" style="width: 100%; margin-top: 20px"
                                         rendered="#{MalaDiretaControle.malaDiretaVO.cfgEvento.itensConvenio}">
                                <rich:panel rendered="#{MalaDiretaControle.eventoEscolhido}"
                                            style="width: 100%; height: 100%;">
                                    <h:outputText style="font-weight: bold" value="Conv�nio de Cobran�a" />
                                    <rich:dataGrid id="dtgConvenio" value="#{MalaDiretaControle.convenioCobrancaVO}"
                                                   width="100%" columns="4" columnClasses="semBorda" styleClass="semBorda"
                                                   cellpadding="0"
                                                   cellspacing="0" var="convenio">
                                        <h:panelGroup>
                                            <h:selectBooleanCheckbox id="slctConvenio" value="#{convenio.selecionado}"/>
                                            <rich:spacer width="5"/>
                                            <h:outputText styleClass="titulo3" value="#{convenio.descricao}"/>
                                        </h:panelGroup>
                                    </rich:dataGrid>
                                </rich:panel>
                            </h:panelGrid>

                        </h:panelGrid>
                    </td>
                </tr>

                <tr>
                    <td>
                        <h:panelGrid columnClasses="colunaEsquerda" columns="1" width="100%" styleClass="crm"
                                     rendered="#{MalaDiretaControle.exibirFiltros}">
                            <h:panelGroup>
                                <a4j:commandLink value="Retrair tudo" action="#{MalaDiretaControle.fecharTodos}"
                                                 onclick="fireElementFromAnyParent('form:btnAtualizaTempo')"
                                                 reRender="form:mensagembuilder" status="false"/>
                                <rich:spacer width="10"/>
                                <a4j:commandLink value="Limpar todos os filtros"
                                                 action="#{MalaDiretaControle.limparTodos}"
                                                 onclick="fireElementFromAnyParent('form:btnAtualizaTempo')"
                                                 reRender="form:mensagembuilder"/>
                            </h:panelGroup>

                            <%-- -----------CATEGORIA------------- --%>
                            <rich:simpleTogglePanel id="categoria" width="100%" switchType="client" label="Categoria"
                                                    opened="#{MalaDiretaControle.categoria}"
                                                    headerClass="headerSanfona">
                                <f:facet name="header">
                                    <h:panelGroup>
                                        <h:outputText styleClass="nomeCol" value="Categoria"/>
                                        <h:outputText styleClass="vinc" value=""/>
                                    </h:panelGroup>
                                </f:facet>
                                <f:facet name="openMarker">
                                    <h:panelGroup>
                                        <i class="fa-icon-chevron-right"></i>
                                    </h:panelGroup>
                                </f:facet>
                                <f:facet name="closeMarker">
                                    <h:panelGroup>
                                        <i class="fa-icon-chevron-down"></i>
                                    </h:panelGroup>
                                </f:facet>
                                <rich:dataGrid id="dtgCategorias" value="#{MalaDiretaControle.categoriaVOs}"
                                               width="100%" columns="4" columnClasses="semBorda" styleClass="semBorda"
                                               cellpadding="0"
                                               cellspacing="0" var="categoria">
                                    <h:panelGroup>
                                        <h:selectBooleanCheckbox id="slctCategoria" value="#{categoria.selecionado}"/>
                                        <rich:spacer width="5"/>
                                        <h:outputText styleClass="titulo3" value="#{categoria.nome}"/>
                                    </h:panelGroup>
                                </rich:dataGrid>
                            </rich:simpleTogglePanel>
                            <%-- -----------COLABORADORES------------- --%>
                            <rich:simpleTogglePanel id="colaboradores" width="100%" headerClass="headerSanfona"
                                                    switchType="client" label="Colaboradores"
                                                    opened="#{MalaDiretaControle.colaboradores}">
                                <f:facet name="header">
                                    <h:panelGroup>
                                        <h:outputText styleClass="nomeCol" value="Colaboradores"/>
                                        <h:outputText styleClass="vinc" value=""/>
                                    </h:panelGroup>
                                </f:facet>
                                <f:facet name="openMarker">
                                    <h:panelGroup>
                                        <i class="fa-icon-chevron-right"></i>
                                    </h:panelGroup>
                                </f:facet>
                                <f:facet name="closeMarker">
                                    <h:panelGroup>
                                        <i class="fa-icon-chevron-down"></i>
                                    </h:panelGroup>
                                </f:facet>

                                <h:panelGroup layout="block">
                                    <a4j:commandButton styleClass="botoes" image="./imagensCRM/botaoAdicionarGrupos.png"
                                                       onclick="fireElementFromAnyParent('form:btnAtualizaTempo')"
                                                       action="#{MalaDiretaControle.toggleConsultores}"
                                                       reRender="colaboradores"/>
                                    <rich:spacer width="5px;"/>
                                    <a4j:commandLink id="lnkConsultores" styleClass="botoes"
                                                     onclick="fireElementFromAnyParent('form:btnAtualizaTempo')"
                                                     action="#{MalaDiretaControle.toggleConsultores}"
                                                     reRender="colaboradores">
                                        <h:outputText styleClass="tituloCamposAberturaMeta" value="Consultores"/>
                                    </a4j:commandLink>

                                    <rich:dataGrid id="dtgConsultores" value="#{MalaDiretaControle.consultoresVOs}"
                                                   rendered="#{MalaDiretaControle.mostrarConsultores}" width="100%"
                                                   columns="4"
                                                   columnClasses="semBorda" styleClass="semBorda" cellpadding="0"
                                                   cellspacing="0"
                                                   var="consultor">
                                        <h:panelGroup>
                                            <h:selectBooleanCheckbox id="slctSituacao"
                                                                     value="#{consultor.selecionado}"/>
                                            <rich:spacer width="5"/>
                                            <h:outputText styleClass="titulo3" value="#{consultor.pessoa_Apresentar}"/>
                                        </h:panelGroup>
                                    </rich:dataGrid>
                                </h:panelGroup>
                                <br/>
                                <h:panelGroup layout="block">
                                    <a4j:commandButton styleClass="botoes" image="./imagensCRM/botaoAdicionarGrupos.png"
                                                       onclick="fireElementFromAnyParent('form:btnAtualizaTempo')"
                                                       action="#{MalaDiretaControle.toggleProfessores}"
                                                       reRender="colaboradores"/>
                                    <rich:spacer width="5px;"/>
                                    <a4j:commandLink id="lnkProfessores"
                                                     action="#{MalaDiretaControle.toggleProfessores}"
                                                     onclick="fireElementFromAnyParent('form:btnAtualizaTempo')"
                                                     styleClass="botoes" reRender="colaboradores">
                                        <h:outputText styleClass="tituloCamposAberturaMeta" value="Professores"/>
                                    </a4j:commandLink>
                                    <rich:dataGrid id="dtgProfessores" value="#{MalaDiretaControle.professoresVOs}"
                                                   onclick="fireElementFromAnyParent('form:btnAtualizaTempo')"
                                                   rendered="#{MalaDiretaControle.mostrarProfessores}" width="100%"
                                                   columns="5"
                                                   columnClasses="semBorda" styleClass="semBorda" cellpadding="0"
                                                   cellspacing="0"
                                                   var="professor">
                                        <h:panelGroup>
                                            <h:selectBooleanCheckbox id="slctSituacao"
                                                                     value="#{professor.selecionado}"/>
                                            <rich:spacer width="5"/>
                                            <h:outputText styleClass="titulo3" value="#{professor.pessoa_Apresentar}"/>
                                        </h:panelGroup>
                                    </rich:dataGrid>
                                </h:panelGroup>
                            </rich:simpleTogglePanel>

                            <%-- -----------DADOS CADASTRAIS------------- --%>
                            <rich:simpleTogglePanel headerClass="headerSanfona" id="dadoscadastrais" width="100%"
                                                    switchType="client" label="Dados Cadastrais"
                                                    opened="#{MalaDiretaControle.dadosCadastrais}">

                                <f:facet name="header">
                                    <h:panelGroup>
                                        <h:outputText styleClass="nomeCol" value="Dados Cadastrais"/>
                                        <h:outputText styleClass="vinc" value=""/>
                                    </h:panelGroup>
                                </f:facet>
                                <f:facet name="openMarker">
                                    <h:panelGroup>
                                        <i class="fa-icon-chevron-right"></i>
                                    </h:panelGroup>
                                </f:facet>
                                <f:facet name="closeMarker">
                                    <h:panelGroup>
                                        <i class="fa-icon-chevron-down"></i>
                                    </h:panelGroup>
                                </f:facet>
                                <h:panelGrid columns="4" columnClasses="colunaEsquerda">
                                    <h:outputText value="Idade: " styleClass="text" style="font-weight: bold"/>

                                    <h:panelGrid columns="4">
                                        <h:outputText value=" #{msg_aplic.prt_New_Mala_Direta_Form_pre_min} "
                                                      styleClass="text"/>
                                        <rich:inputNumberSpinner
                                                value="#{MalaDiretaControle.malaDiretaVO.mailingFiltros.idadeMinStr}"/>
                                        <h:outputText value=" #{msg_aplic.prt_New_Mala_Direta_Form_pre_max} "
                                                      styleClass="text"/>

                                        <rich:inputNumberSpinner
                                                value="#{MalaDiretaControle.malaDiretaVO.mailingFiltros.idadeMaxStr}"
                                                maxValue="150"/>

                                    </h:panelGrid>

                                    <h:outputText value="Sexo biol�gico:" styleClass="text" style="font-weight: bold"/>
                                    <h:panelGroup>
                                        <h:selectBooleanCheckbox
                                                value="#{MalaDiretaControle.malaDiretaVO.mailingFiltros.masculino}">
                                        </h:selectBooleanCheckbox>
                                        <h:outputText value="M" styleClass="text"/>

                                        <h:selectBooleanCheckbox
                                                value="#{MalaDiretaControle.malaDiretaVO.mailingFiltros.feminino}">
                                        </h:selectBooleanCheckbox>
                                        <h:outputText value="F" styleClass="text"/>
                                    </h:panelGroup>
                                    <h:outputText value="Data de Cadastro:" styleClass="text"
                                                  style="font-weight: bold"/>
                                    <h:panelGrid columns="4">

                                        <h:outputText value=" #{msg_aplic.prt_New_Mala_Direta_Form_pre_min} "
                                                      styleClass="text"/>
                                        <rich:calendar
                                                value="#{MalaDiretaControle.malaDiretaVO.mailingFiltros.dataCadastroMin}"
                                                inputSize="10"
                                                inputClass="form"
                                                oninputblur="blurinput(this);"
                                                oninputfocus="focusinput(this);"
                                                oninputchange="return validar_Data(this.id);"
                                                datePattern="dd/MM/yyyy"
                                                enableManualInput="true"
                                                zindex="2"
                                                showWeeksBar="false"/>
                                        <h:outputText value=" #{msg_aplic.prt_New_Mala_Direta_Form_pre_max} "
                                                      styleClass="text"/>
                                        <rich:calendar
                                                value="#{MalaDiretaControle.malaDiretaVO.mailingFiltros.dataCadastroMax}"
                                                inputSize="10"
                                                inputClass="form"
                                                oninputblur="blurinput(this);"
                                                oninputfocus="focusinput(this);"
                                                oninputchange="return validar_Data(this.id);"
                                                datePattern="dd/MM/yyyy"
                                                enableManualInput="true"
                                                zindex="2"
                                                showWeeksBar="false"/>
                                    </h:panelGrid>
                                </h:panelGrid>
                            </rich:simpleTogglePanel>

                            <%-- -----------DADOS PLANOS------------- --%>
                            <rich:simpleTogglePanel headerClass="headerSanfona" id="dadosplanos" width="100%"
                                                    switchType="client" label="Dados Planos"
                                                    opened="#{MalaDiretaControle.dadosPlanos}">

                                <f:facet name="header">
                                    <h:panelGroup>
                                        <h:outputText styleClass="nomeCol" value="Dados de Planos"/>
                                        <h:outputText styleClass="vinc" value=""/>
                                    </h:panelGroup>
                                </f:facet>
                                <f:facet name="openMarker">
                                    <h:panelGroup>
                                        <i class="fa-icon-chevron-right"></i>
                                    </h:panelGroup>
                                </f:facet>
                                <f:facet name="closeMarker">
                                    <h:panelGroup>
                                        <i class="fa-icon-chevron-down"></i>
                                    </h:panelGroup>
                                </f:facet>
                                <h:outputText value="#{msg_aplic.prt_New_Mala_Direta_Form_recorrencia}"
                                              styleClass="text"
                                              style="font-weight: bold"/>
                                <h:selectBooleanCheckbox value="#{MalaDiretaControle.somenteRecorrencia}">
                                    <a4j:support action="#{MalaDiretaControle.marcaDesmarcaRecorrencia}"
                                                 reRender="dtgPlanos" event="onclick"/>
                                </h:selectBooleanCheckbox>
                                <br/>
                                <h:outputText value="Planos:" styleClass="text" style="font-weight: bold"/>
                                <rich:dataGrid id="dtgPlanos" value="#{MalaDiretaControle.planoVOs}"
                                               width="100%" columns="3" columnClasses="semBorda" styleClass="semBorda"
                                               cellpadding="0" cellspacing="0" var="plano">
                                    <h:panelGroup>
                                        <h:selectBooleanCheckbox id="slctPlano" value="#{plano.selecionado}"/>
                                        <rich:spacer width="5"/>
                                        <h:outputText styleClass="titulo3" value="#{plano.descricao}"/>
                                    </h:panelGroup>
                                </rich:dataGrid>
                                <br/>
                                <h:outputText value="Dura��o dos planos:" styleClass="text" style="font-weight: bold"/>
                                <rich:dataGrid id="dtgDuracao" value="#{MalaDiretaControle.contratoDuracaoVOs}"
                                               width="100%" columns="4" columnClasses="semBorda" styleClass="semBorda"
                                               cellpadding="0" cellspacing="0" var="contratoDuracao">
                                    <h:panelGroup>
                                        <h:selectBooleanCheckbox id="slctModalide"
                                                                 value="#{contratoDuracao.selecionado}"/>
                                        <rich:spacer width="5"/>
                                        <h:outputText styleClass="titulo3" value="#{contratoDuracao.numeroMeses}"/>
                                    </h:panelGroup>
                                </rich:dataGrid>

                            </rich:simpleTogglePanel>
                            <%-- -----------MODALIDADES------------- --%>
                            <rich:simpleTogglePanel headerClass="headerSanfona" id="modalidades" width="100%"
                                                    switchType="client" label="Modalidades"
                                                    opened="#{MalaDiretaControle.modalidades}">

                                <f:facet name="header">
                                    <h:panelGroup>
                                        <h:outputText styleClass="nomeCol" value="Modalidades"/>
                                        <h:outputText styleClass="vinc" value=""/>
                                    </h:panelGroup>
                                </f:facet>
                                <f:facet name="openMarker">
                                    <h:panelGroup>
                                        <i class="fa-icon-chevron-right"></i>
                                    </h:panelGroup>
                                </f:facet>
                                <f:facet name="closeMarker">
                                    <h:panelGroup>
                                        <i class="fa-icon-chevron-down"></i>
                                    </h:panelGroup>
                                </f:facet>
                                <rich:dataGrid id="dtgModalidades" value="#{MalaDiretaControle.modalidadeVOs}"
                                               width="100%" columns="4" columnClasses="semBorda" styleClass="semBorda"
                                               cellpadding="0" cellspacing="0" var="modalidade">
                                    <h:panelGroup>
                                        <h:selectBooleanCheckbox id="slctModalide" value="#{modalidade.selecionado}"/>
                                        <rich:spacer width="5"/>
                                        <h:outputText styleClass="titulo3" value="#{modalidade.nome}"/>
                                    </h:panelGroup>
                                </rich:dataGrid>

                            </rich:simpleTogglePanel>


                            <%-- -----------SITUACAO------------- --%>

                            <rich:simpleTogglePanel headerClass="headerSanfona" id="situacao" width="100%"
                                                    switchType="client"
                                                    label="#{msg_aplic.prt_New_Mala_Direta_Form_situacao}"
                                                    rendered="#{!MalaDiretaControle.malaDiretaVO.cfgEvento.posVenda}"
                                                    opened="#{MalaDiretaControle.situacao}">

                                <f:facet name="header">
                                    <h:panelGroup>
                                        <h:outputText styleClass="nomeCol"
                                                      value="#{msg_aplic.prt_New_Mala_Direta_Form_situacao}"/>
                                        <h:outputText styleClass="vinc" value=""/>
                                    </h:panelGroup>
                                </f:facet>
                                <f:facet name="openMarker">
                                    <h:panelGroup>
                                        <i class="fa-icon-chevron-right"></i>
                                    </h:panelGroup>
                                </f:facet>
                                <f:facet name="closeMarker">
                                    <h:panelGroup>
                                        <i class="fa-icon-chevron-down"></i>
                                    </h:panelGroup>
                                </f:facet>
                                <rich:dataGrid id="dtgSituacoes" value="#{MalaDiretaControle.situacaoClienteTOs}"
                                               width="100%" columns="4" columnClasses="semBorda" styleClass="semBorda"
                                               cellpadding="0" cellspacing="0" var="situacao">
                                    <h:panelGroup>
                                        <h:selectBooleanCheckbox id="slctSituacao" value="#{situacao.selecionado}"/>
                                        <rich:spacer width="5"/>
                                        <h:outputText styleClass="titulo3"
                                                      value="#{situacao.situacaoClienteEnum.descricao}"/>
                                    </h:panelGroup>
                                </rich:dataGrid>

                            </rich:simpleTogglePanel>

                            <%-- PESQUISA --%>
                            <rich:simpleTogglePanel headerClass="headerSanfona" id="pesquisa" width="100%"
                                                    rendered="#{MalaDiretaControle.apresentarTags}"
                                                    switchType="client" label="Pesquisa"
                                                    opened="#{MalaDiretaControle.pesquisa}">

                                <f:facet name="header">
                                    <h:panelGroup>
                                        <h:outputText styleClass="nomeCol" value="Pesquisa"/>
                                        <h:outputText styleClass="vinc" value=""/>
                                    </h:panelGroup>
                                </f:facet>
                                <f:facet name="openMarker">
                                    <h:panelGroup>
                                        <i class="fa-icon-chevron-right"></i>
                                    </h:panelGroup>
                                </f:facet>
                                <f:facet name="closeMarker">
                                    <h:panelGroup>
                                        <i class="fa-icon-chevron-down"></i>
                                    </h:panelGroup>
                                </f:facet>

                                <h:selectOneMenu id="listaDePesquisa"
                                                 value="#{MalaDiretaControle.malaDiretaVO.questionario}">
                                    <f:selectItems value="#{MalaDiretaControle.listaSelectItemPesquisa}"/>
                                </h:selectOneMenu>

                            </rich:simpleTogglePanel>
                            <%--CRM Extra--%>
                            <rich:simpleTogglePanel id="extra" width="100%" switchType="client" label="Extra"
                                                    rendered="#{MalaDiretaControle.malaDiretaVO.crmExtra}"
                                                    opened="#{MalaDiretaControle.extra}"
                                                    headerClass="headerSanfona">
                                <f:facet name="header">
                                    <h:panelGroup>
                                        <h:outputText styleClass="nomeCol" value="Meta Extra"/>
                                        <h:outputText styleClass="vinc" value=""/>
                                    </h:panelGroup>
                                </f:facet>
                                <f:facet name="openMarker">
                                    <h:panelGroup>
                                        <i class="fa-icon-chevron-right"></i>
                                    </h:panelGroup>
                                </f:facet>
                                <f:facet name="closeMarker">
                                    <h:panelGroup>
                                        <i class="fa-icon-chevron-down"></i>
                                    </h:panelGroup>
                                </f:facet>

                                <h:panelGrid cellspacing="5px" cellpadding="5px">
                                    <h:panelGroup id="dataVencimento">
                                        <h:outputText styleClass="text" value="Vencimento do Contrato: "/>
                                        <rich:calendar id="inicioVencimento"
                                                       value="#{MalaDiretaControle.inicioVencimento}"
                                                       inputSize="6"
                                                       inputClass="form"
                                                       oninputblur="blurinput(this);"
                                                       oninputfocus="focusinput(this);"
                                                       oninputchange="return validar_Data(this.id);"
                                                       datePattern="dd/MM/yyyy"
                                                       enableManualInput="true"
                                                       zindex="2"
                                                       showWeeksBar="false"/>

                                        <rich:spacer width="5px"/>
                                        <h:outputText styleClass="text"
                                                      value=" #{msg_aplic.prt_New_Mala_Direta_Form_ate} "/>
                                        <rich:calendar id="fimVencimento"
                                                       value="#{MalaDiretaControle.fimVencimento}"
                                                       inputSize="6"
                                                       inputClass="form"
                                                       oninputblur="blurinput(this);"
                                                       oninputfocus="focusinput(this);"
                                                       oninputchange="return validar_Data(this.id);"
                                                       datePattern="dd/MM/yyyy"
                                                       enableManualInput="true"
                                                       zindex="2"
                                                       showWeeksBar="false"/>
                                        <a4j:commandButton id="limparVencimento"
                                                           style="margin-left: 5px; vertical-align: middle"
                                                           action="#{MalaDiretaControle.limparVencimento}"
                                                           image="/images/limpar.gif"
                                                           title="Limpar per�odo de vencimento."
                                                           reRender="inicioVencimento, fimVencimento"/>
                                    </h:panelGroup>
                                </h:panelGrid>
                                <h:panelGrid cellspacing="5px" cellpadding="5px">
                                    <h:panelGroup id="diasSemComp">
                                        <h:outputText styleClass="text" value="Dias sem Comparecer: "/>
                                        <h:inputText id="inicioDias" size="5"
                                                     value="#{MalaDiretaControle.inicioDiasSemAparecer}"/>
                                        <h:outputText styleClass="text"
                                                      value="#{msg_aplic.prt_New_Mala_Direta_Form_ate}: "/>
                                        <h:inputText id="fimDias" size="5"
                                                     value="#{MalaDiretaControle.fimDiasSemAparecer}"/>
                                    </h:panelGroup>
                                </h:panelGrid>


                            </rich:simpleTogglePanel>
                        </h:panelGrid>

                        <h:panelGrid columnClasses="centralizado" width="100%">
                            <h:panelGroup id="botoes">
                                <a4j:commandButton id="verAmostra"
                                                   rendered="#{MalaDiretaControle.exibirBotaoAmostra}"
                                                   action="#{MalaDiretaControle.consultarClientes}"
                                                   reRender="panelAmostraClientes"
                                                   oncomplete="#{MalaDiretaControle.msgAlert}"
                                                   value="Ver amostra de destinat�rios"
                                                   accesskey="2" styleClass="botoes nvoBt"/>

                                <a4j:commandButton id="verHistorico"
                                                   rendered="#{!MalaDiretaControle.malaDiretaVO.novoObj && !MalaDiretaControle.malaDiretaVO.crmExtra}"
                                                   oncomplete="Richfaces.showModalPanel('panelHistorico')"
                                                   reRender="panelHistorico"
                                                   value="Hist�rico de execu��es"
                                                   accesskey="2" styleClass="botoes nvoBt btSec"/>

                                <a4j:commandLink rendered="#{!MalaDiretaControle.malaDiretaVO.novoObj && !MalaDiretaControle.malaDiretaVO.crmExtra}"
                                                 action="#{MalaDiretaControle.realizarConsultaLogObjetoSelecionado}"
                                                 oncomplete="abrirPopup('visualizadorLogForm.jsp', 'VisualizadorLog', 785, 595);"
                                                 title="Visualizar Log"
                                                 style="display: inline-block; padding: 8px 15px;"
                                                 styleClass="botoes nvoBt btSec">
                                    <i style="text-decoration: none" class="fa-icon-list"/>
                                </a4j:commandLink>

                                <%--VER AMOSTRA DA META EXTRA--%>
                                <a4j:commandButton id="verAmostraCRMExtra"
                                                   rendered="#{MalaDiretaControle.malaDiretaVO.crmExtra}"
                                                   action="#{MalaDiretaControle.consultarClientes}"
                                                   reRender="panelAmostraClientes, mdlMensagemGenerica"
                                                   oncomplete="#{MalaDiretaControle.modalMensagemGenerica}"
                                                   value="Ver amostra de clientes"
                                                   accesskey="2" styleClass="botoes nvoBt"/>
                            </h:panelGroup>
                        </h:panelGrid>


                            <%--USUARIOS DA META EXTRA--%>
                        <h:panelGrid id="usuarioMetaExtra" columnClasses="colunaEsquerda" columns="1" width="100%"
                                     styleClass="crm" rendered="#{MalaDiretaControle.malaDiretaVO.crmExtra}">
                            <rich:simpleTogglePanel headerClass="headerSanfona" width="100%"
                                                    rendered="#{!MalaDiretaControle.malaDiretaVO.metaExtraIndividual}"
                                                    switchType="client"
                                                    label="#{msg_aplic.prt_New_Mala_Direta_Form_usuarios}"
                                                    opened="true">

                                <f:facet name="header">
                                    <h:panelGroup>
                                        <h:outputText styleClass="nomeCol" value="Usu�rios Participantes da Meta"/>
                                        <h:outputText styleClass="vinc" value=""/>
                                    </h:panelGroup>
                                </f:facet>
                                <f:facet name="openMarker">
                                    <h:panelGroup>
                                        <i class="fa-icon-chevron-right"></i>
                                    </h:panelGroup>
                                </f:facet>
                                <f:facet name="closeMarker">
                                    <h:panelGroup>
                                        <i class="fa-icon-chevron-down"></i>
                                    </h:panelGroup>
                                </f:facet>

                                <rich:dataGrid value="#{MalaDiretaControle.listaUsuariosMetaCRMExtra}"
                                               id="caixaDeSelecaoUsuario"
                                               width="100%" columns="4" columnClasses="semBorda" styleClass="semBorda"
                                               cellpadding="0" cellspacing="0" var="usuario">
                                    <h:panelGroup>
                                        <h:selectBooleanCheckbox value="#{usuario.usuarioEscolhido}"
                                                                 id="selecionarUsuarioME"/>
                                        <rich:spacer width="5"/>
                                        <h:outputText styleClass="titulo3"
                                                      value="#{usuario.nome}"/>
                                    </h:panelGroup>
                                </rich:dataGrid>
                            </rich:simpleTogglePanel>
                        </h:panelGrid>

                    </td>
                </tr>

            </table>

            <h:panelGroup id="painelPeriodicidade"
                          rendered="#{((MalaDiretaControle.usarAgendamento || !MalaDiretaControle.eventoEscolhido) && !MalaDiretaControle.malaDiretaVO.contatoDireto) && !MalaDiretaControle.malaDiretaVO.crmExtra && !MalaDiretaControle.contatoInstantaneo}">
                <!-- Painel Periodicidade -->
                <div style="z-index: 100;">
                <table width="100%" border="0" align="left" cellpadding="0" cellspacing="0" bgcolor="#fff"
                       style="padding: 10px; display: block; >
                    <tr>
                        <td align="left" valign="top" style="padding-bottom: 5px;">
                            <div style="clear: both;" class="text">
                                <h:outputText style="font-weight: bold"
                                              value="Periodicidade" rendered="#{!MalaDiretaControle.eventoEscolhido}"/>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td align="left" valign="top">
                            <h:panelGroup id="agendamentos">
                                <%-- Aqui inicio Loop agendamentos --%>
                                <h:panelGrid id="idpanelAgendamento" width="100%" columns="3"
                                             columnClasses="colEsquerda, colDireita corrTopo, colDireita corrTopo"
                                             styleClass="panelAgendamento">

                                    <h:panelGroup rendered="#{!MalaDiretaControle.eventoEscolhido}">
                                        <h:selectOneRadio styleClass="text" layout="pageDirection"
                                                          value="#{MalaDiretaControle.ocorrenciaSelecionada}">
                                            <f:selectItems value="#{MalaDiretaControle.listaSelectItemOcorrencia}"/>
                                            <a4j:support event="onchange" action="#{MalaDiretaControle.alterarTipo}"
                                                         reRender="form:mensagembuilder, idpanelAgendamento, agendamentos"/>
                                        </h:selectOneRadio>
                                    </h:panelGroup>
                                    <%-- Meses ou Dias da Semana --%>
                                    <h:panelGroup id="grupoAgendamento"
                                                  rendered="#{MalaDiretaControle.usarAgendamento}">
                                        <h:panelGrid columns="2" columnClasses="colEsquerda" width="100%">
                                            <h:outputText styleClass="text" rendered="#{MalaDiretaControle.mensalmente}"
                                                          value="#{msg_aplic.prt_mailing_agendamento_meses}:"/>
                                            <h:panelGrid columns="1">
                                                <h:panelGroup rendered="#{MalaDiretaControle.mensalmente}">
                                                    <h:selectBooleanCheckbox
                                                            value="#{MalaDiretaControle.malaDiretaVO.agendamento.todosMeses}">
                                                        <a4j:support reRender="idpanelAgendamento" event="onclick"/>
                                                    </h:selectBooleanCheckbox>
                                                    <h:outputText styleClass="text"
                                                                  value="#{msg_aplic.prt_mailing_agendamento_todosMeses}"/>
                                                </h:panelGroup>
                                                <rich:dataGrid style="border: none;"
                                                               value="#{MalaDiretaControle.malaDiretaVO.agendamento.mes}"
                                                               var="mes" columns="4"
                                                               rendered="#{MalaDiretaControle.mensalmente && !MalaDiretaControle.malaDiretaVO.agendamento.todosMeses}">
                                                    <h:selectBooleanCheckbox value="#{mes.selecionado}"/>
                                                    <h:outputText value="#{mes.label}"/>
                                                </rich:dataGrid>

                                            </h:panelGrid>
                                        </h:panelGrid>

                                        <h:selectBooleanCheckbox rendered="#{MalaDiretaControle.semanalmente}"
                                                                 value="#{MalaDiretaControle.malaDiretaVO.agendamento.todosDiasSemana}">
                                            <a4j:support reRender="idpanelAgendamento" event="onclick"/>
                                        </h:selectBooleanCheckbox>
                                        <h:outputText styleClass="text" rendered="#{MalaDiretaControle.semanalmente}"
                                                      value="#{msg_aplic.prt_mailing_agendamento_todosDiasSemana}"/>
                                        <rich:dataGrid style="border: none;"
                                                       value="#{MalaDiretaControle.malaDiretaVO.agendamento.diasSemana}"
                                                       var="dia" columns="4"
                                                       rendered="#{MalaDiretaControle.semanalmente && !MalaDiretaControle.malaDiretaVO.agendamento.todosDiasSemana}">
                                            <h:selectBooleanCheckbox value="#{dia.selecionado}"/>
                                            <h:outputText value="#{dia.label}"/>
                                        </rich:dataGrid>
                                    </h:panelGroup>
                                    <%-- Dias do Mes --%>
                                    <h:panelGroup>
                                        <h:panelGrid columns="2" columnClasses="colEsquerda" width="100%">
                                            <h:outputText styleClass="text" rendered="#{MalaDiretaControle.mensalmente}"
                                                          value="#{msg_aplic.prt_mailing_agendamento_dia}:"/>

                                            <h:panelGrid columns="1">
                                                <h:panelGroup rendered="#{MalaDiretaControle.mensalmente}">
                                                    <h:selectBooleanCheckbox
                                                            value="#{MalaDiretaControle.malaDiretaVO.agendamento.todosDias}">
                                                        <a4j:support reRender="idpanelAgendamento" event="onclick"/>
                                                    </h:selectBooleanCheckbox>
                                                    <h:outputText styleClass="text"
                                                                  value="#{msg_aplic.prt_mailing_agendamento_todosDias}"/>
                                                </h:panelGroup>
                                                <rich:dataGrid style="border: none;"
                                                               value="#{MalaDiretaControle.malaDiretaVO.agendamento.diasMes}"
                                                               var="diaM" columns="8"
                                                               rendered="#{MalaDiretaControle.mensalmente && !MalaDiretaControle.malaDiretaVO.agendamento.todosDias}">
                                                    <h:selectBooleanCheckbox value="#{diaM.selecionado}"/>
                                                    <h:outputText value="#{diaM.label}"/>
                                                </rich:dataGrid>
                                            </h:panelGrid>
                                        </h:panelGrid>
                                    </h:panelGroup>
                                </h:panelGrid>
                                <%-- AQUI Ok! --%>
                                <h:panelGrid rendered="#{MalaDiretaControle.usarAgendamento}"
                                             columns="4"
                                             style="position: absolute; z-index:100"
                                             columnClasses="classeEsquerda, classeDireita, classeEsquerda, classeDireita"
                                             styleClass="text">
                                    <h:outputText
                                            style="font-weight: bold; text-align: left; display: block; width: 100%;"
                                            value="Periodo de execu��o"/>
                                    <h:panelGroup/>
                                    <h:panelGroup/>
                                    <h:panelGroup/>

                                    <h:panelGroup id="periodoExecucaoForm" style="display: block; min-width: 235px;">
                                        <rich:calendar value="#{MalaDiretaControle.malaDiretaVO.dataEnvio}"
                                                       datePattern="dd/MM/yyyy"
                                                       inputSize="8"
                                                       oninputchange="return validar_Data(this.id);"
                                                       enableManualInput="true" zindex="2" showWeeksBar="false"/>

                                        <h:outputText rendered="#{MalaDiretaControle.malaDiretaVO.usarAgendamento}"
                                                      styleClass="tituloCampos"
                                                      value="#{msg_aplic.prt_New_Mala_Direta_Form_ate}"/>

                                        <rich:calendar value="#{MalaDiretaControle.malaDiretaVO.vigenteAte}"
                                                       datePattern="dd/MM/yyyy"
                                                       rendered="#{MalaDiretaControle.malaDiretaVO.usarAgendamento}"
                                                       inputSize="8"
                                                       oninputchange="return validar_Data(this.id);"
                                                       enableManualInput="true" zindex="2" showWeeksBar="false"/>
                                        <a4j:commandButton id="limparPeriodoCriacao"
                                                           onclick="fireElementFromAnyParent('form:btnAtualizaTempo')"
                                                           action="#{MalaDiretaControle.limparPeriodoExecucaoForm}"
                                                           image="imagensCRM/limpar.gif"
                                                           alt="#{msg_aplic.prt_limparCampo}"
                                                           reRender="periodoExecucaoForm"/>
                                    </h:panelGroup>

                                    <h:panelGroup rendered="#{MalaDiretaControle.usarHorarioEnvio}">
                                        <h:outputText styleClass="text" value="Hor�rio de envio:"
                                                      style="display: block; min-width: 120px;"/>
                                    </h:panelGroup>
                                    <h:panelGroup rendered="#{MalaDiretaControle.usarHorarioEnvio}">
                                        <h:panelGroup style="display: block; min-width: 235px; z-index: 100">
                                            <rich:inputNumberSpinner id="idhoraInicio" style="float: left"
                                                                     value="#{MalaDiretaControle.malaDiretaVO.agendamento.horaInicio}"
                                                                     rendered="#{MalaDiretaControle.malaDiretaVO.usarAgendamento}"
                                                                     inputSize="1" maxValue="23" minValue="0"/>
                                            <h:outputText styleClass="text"
                                                          value="#{msg_aplic.prt_New_Mala_Direta_Form_ate}"
                                                          style="float: left"/>
                                            <rich:inputNumberSpinner id="idhoraFim" style="float: left"
                                                                     value="#{MalaDiretaControle.malaDiretaVO.agendamento.horaFim}"
                                                                     rendered="#{MalaDiretaControle.malaDiretaVO.usarAgendamento}"
                                                                     inputSize="1" maxValue="23" minValue="0"/>
                                            <h:outputText styleClass="text" value="horas"/>
                                        </h:panelGroup>
                                    </h:panelGroup>
                                </h:panelGrid>
                            </h:panelGroup>
                        </td>
                    </tr>
                </table>
                </div>
            </h:panelGroup>
        </div>
        <div class="col-md-6" style=" height: 600px;">
            <div class="preview-email" style="border: 1px solid #d5d2d2; margin: 10px; border-radius: 10px; overflow: hidden;${MensagemBuilderControle.meioEmail ? '' : '    min-height: 50px; padding: 30px 10px;'}">
                    ${MensagemBuilderControle.meioEmail ? MensagemBuilderControle.html : MensagemBuilderControle.texto}
            </div>

        </div>
    </div>


</h:panelGroup>

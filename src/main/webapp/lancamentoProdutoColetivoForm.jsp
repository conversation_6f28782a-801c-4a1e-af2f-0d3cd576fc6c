<%@page contentType="text/html;charset=UTF-8" %>
<head><%@include file="/includes/include_import_minifiles.jsp" %></head>

<link href="beta/css/bootstrap-tabs.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/pure-ext.css" type="text/css" rel="stylesheet"/>
<link href="${contexto}/css/otimize.css" rel="stylesheet" type="text/css">
<link href="${contexto}/css/ce.css" rel="stylesheet" type="text/css">
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<script type="text/javascript" language="javascript" src="hoverform.js"></script>
<script type="text/javascript" language="javascript">var contexto = '${contexto}';</script>
<script type="text/javascript" language="javascript" src="${contexto}/script/ajuda.js"></script>
<script src="${root}/script/packJQueryPlugins.min.js" type="text/javascript"></script>

<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>
<%@taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>
<f:loadBundle var="CElabels" basename="propriedades.CElabels"/>
<style>
    .form{
        font-family: Arial, Helvetica, sans-serif;
        font-size: 11px;
        color: #515151;
        border: solid #8eb3c3 1px;
        padding-left: 4px;
        padding-top: 4px;
        height: 23px;
        vertical-align: middle;
    }
</style>
<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText value="#{msg_aplic.prt_Lancamento_produto_coletivo_tituloForm}"/>
    </title>


    <a4j:loadScript src="/script/jquery.maskedinput-1.2.2.js" />
    <c:set var="titulo" scope="session" value="${msg_aplic.prt_Lancamento_produto_coletivo_tituloForm}"/>
    <c:set var="urlWiki" scope="session" value="${SuperControle.urlBaseConhecimento}para-que-serve-o-lancamento-de-produto-coletivo/"/>
    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <f:facet name="header">
            <jsp:include page="topoReduzido_material.jsp"/>
        </f:facet>

        <h:form id="form">
            <hr style="border-color: #e6e6e6;"/>
            <h:panelGrid columns="1"  width="100%">
                <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar" columnClasses="classEsquerda, classDireita" width="100%"
                             id="dadosLancamento">

                    <h:outputText value="* Empresa:"
                                  rendered="#{LancamentoProdutoColetivoControle.usuarioLogado.administrador}"/>
                    <h:selectOneMenu rendered="#{LancamentoProdutoColetivoControle.usuarioLogado.administrador}"
                                     value="#{LancamentoProdutoColetivoControle.lancamento.empresa.codigo}">
                        <f:selectItems value="#{LancamentoProdutoColetivoControle.empresas}"/>
                    </h:selectOneMenu>

                    <h:outputText value="Responsável:"
                                  rendered="#{LancamentoProdutoColetivoControle.lancamento.codigo != null && LancamentoProdutoColetivoControle.lancamento.codigo > 0}"/>
                    <h:outputText value="#{LancamentoProdutoColetivoControle.lancamento.usuario.nome}"
                                  rendered="#{LancamentoProdutoColetivoControle.lancamento.codigo != null && LancamentoProdutoColetivoControle.lancamento.codigo > 0}"/>

                    <h:outputText value="#{msg_aplic.prt_Produto_descricao}" />
                    <h:panelGroup>
                        <h:inputText id="descricao" onblur="blurinput(this);"  onfocus="focusinput(this);"
                                     styleClass="form"  size="50" maxlength="50"
                                     value="#{LancamentoProdutoColetivoControle.lancamento.descricao}" />
                    </h:panelGroup>


                    <h:outputText value="* Produto:" />
                    <h:panelGroup>
                        <h:inputText id="produto" size="50" maxlength="50"
                                     onfocus="focusinput(this);" styleClass="form"
                                     value="#{LancamentoProdutoColetivoControle.lancamento.produtoVO.descricao}">
                           
                        </h:inputText>
                        <a4j:commandButton  title="Limpar Produto"
                                            action="#{LancamentoProdutoColetivoControle.limparProduto}"
                                       image="images/limpar.gif" alt="Limpar Produto"
                                       reRender="form"/>
                        <rich:suggestionbox height="200" width="400"
                                            for="produto"
                                            fetchValue="#{result}"
                                            suggestionAction="#{LancamentoProdutoColetivoControle.executarAutocompleteProduto}"
                                            minChars="1" rowClasses="20"
                                            status="true"
                                            nothingLabel="Nenhum produto encontrado!"
                                            var="result" id="suggestionProduto">
                            <a4j:support event="onselect" ignoreDupResponses="true"
                                         action="#{LancamentoProdutoColetivoControle.selecionarProdutoSuggestionBox}"
                                         focus="produto" reRender="form"/>

                            <h:column>
                                <f:facet name="header">
                                    <h:outputText styleClass="textverysmall" value="Descrição"/>
                                </f:facet>
                                <h:outputText value="#{result.descricao}"/>
                            </h:column>
                        </rich:suggestionbox>
                    </h:panelGroup>

                    <h:outputText value="Valor:"/>
                    <h:inputText id="valorAnuidade"  size="10" maxlength="10"
                                 onkeypress="return formatar_moeda(this,'.',',',event);" onfocus="focusinput(this);"
                                 styleClass="form" value="#{LancamentoProdutoColetivoControle.lancamento.valor}">
                        <f:converter converterId="FormatadorNumerico" />
                        <a4j:support event="onblur" reRender="painelMensagens"/>
                    </h:inputText>

                    <h:outputText value=""/>
                    <h:selectOneRadio value="#{LancamentoProdutoColetivoControle.lancamento.tipoLancamento}">
                        <f:selectItems value="#{LancamentoProdutoColetivoControle.tipos}"/>
                        <a4j:support event="onchange" reRender="dadosLancamento, painelMensagens" status="false"/>
                    </h:selectOneRadio>

                    <h:outputText value="Data de vencimento do produto:" rendered="#{LancamentoProdutoColetivoControle.lancamento.tipoData}"/>
                    <rich:calendar id="data"  oninputchange="validar_Data(this.id);atualizarPainelMensagens();"
                                   onchanged="atualizarPainelMensagens();"
                                   value="#{LancamentoProdutoColetivoControle.lancamento.dataEspecifica}"
                                   rendered="#{LancamentoProdutoColetivoControle.lancamento.tipoData}"
                                   enableManualInput="true" popup="true"
                                   inputSize="10" datePattern="dd/MM/yyyy"
                                   showApplyButton="false">
                    </rich:calendar>
                    

                    <h:outputText value="Mês de vencimento do produto:" rendered="#{LancamentoProdutoColetivoControle.lancamento.tipoMes}"/>
                    <h:selectOneMenu value="#{LancamentoProdutoColetivoControle.lancamento.mes}"
                                     rendered="#{LancamentoProdutoColetivoControle.lancamento.tipoMes}">
                        <f:selectItems value="#{LancamentoProdutoColetivoControle.meses}"/>
                        <a4j:support event="onchange" reRender="painelMensagens" status="false"/>
                    </h:selectOneMenu>

                    <h:outputText value="Parcela:"
                                  rendered="#{LancamentoProdutoColetivoControle.lancamento.tipoParcela}"/>
                    <h:selectOneMenu
                        rendered="#{LancamentoProdutoColetivoControle.lancamento.tipoParcela}"
                        value="#{LancamentoProdutoColetivoControle.lancamento.parcela}">
                        <f:selectItems value="#{LancamentoProdutoColetivoControle.parcelas}"/>
                        <a4j:support event="onchange" reRender="painelMensagens" status="false"/>
                    </h:selectOneMenu>

                    <h:outputText value="Plano:" />
                    <h:panelGroup>
                        <h:inputText id="plano" size="50" maxlength="50"
                                     onfocus="focusinput(this);" styleClass="form"
                                     value="#{LancamentoProdutoColetivoControle.lancamento.planoVO.descricao}">
                            
                        </h:inputText>
                        <a4j:commandButton  title="Limpar Plano"
                                            action="#{LancamentoProdutoColetivoControle.limparPlano}"
                                       image="images/limpar.gif" alt="Limpar Plano"
                                       reRender="form"/>
                        <rich:suggestionbox height="200" width="400"
                                            for="plano"
                                            fetchValue="#{result}"
                                            suggestionAction="#{LancamentoProdutoColetivoControle.executarAutocompletePlano}"
                                            minChars="1" rowClasses="20"
                                            status="true"
                                            nothingLabel="Nenhum produto encontrado!"
                                            var="result" id="suggestionPlano">
                            <a4j:support event="onselect" ignoreDupResponses="true"
                                         action="#{LancamentoProdutoColetivoControle.selecionarPlanoSuggestionBox}"
                                         focus="plano" reRender="form, painelMensagens"/>

                            <h:column>
                                <f:facet name="header">
                                    <h:outputText styleClass="textverysmall" value="Descrição"/>
                                </f:facet>
                                <h:outputText value="#{result.descricao}"/>
                            </h:column>
                        </rich:suggestionbox>
                    </h:panelGroup>
                    <h:outputText value="Contrato Vigência:" />
                    <h:panelGroup>
                        <rich:calendar id="dtInicio" oninputchange="validar_Data(this.id);atualizarPainelMensagens();"
                                       onchanged="atualizarPainelMensagens();"
                                       value="#{LancamentoProdutoColetivoControle.lancamento.vigenciaContratoInicio}"
                                       enableManualInput="true" popup="true"
                                       inputSize="10" datePattern="dd/MM/yyyy"
                                       showApplyButton="false">
                        </rich:calendar>
                        <rich:spacer width="5"/>
                        <h:outputText value="Até"  styleClass="classEsquerda"/>
                        <rich:spacer width="5"/>
                        <rich:calendar id="dtFim" oninputchange="validar_Data(this.id);atualizarPainelMensagens();"
                                       onchanged="atualizarPainelMensagens();"
                                       value="#{LancamentoProdutoColetivoControle.lancamento.vigenciaContratoFim}"
                                       enableManualInput="true" popup="true"
                                       inputSize="10" datePattern="dd/MM/yyyy"
                                       showApplyButton="false">
                        </rich:calendar>
                        <a4j:jsFunction name="atualizarPainelMensagens" reRender="painelMensagens" status="false"/>
                    </h:panelGroup>
                    <h:outputText value="Modalidade:" />
                    <h:panelGroup>
                        <h:inputText id="modalidade" size="50" maxlength="50"
                                     onfocus="focusinput(this);" styleClass="form"
                                     value="#{LancamentoProdutoColetivoControle.lancamento.modalidadeVO.nome}">
                            
                        </h:inputText>
                        <a4j:commandButton  title="Limpar Modalidade"
                                            action="#{LancamentoProdutoColetivoControle.limparModalidade}"
                                       image="images/limpar.gif" alt="Limpar Modalidade"
                                       reRender="form"/>
                        <rich:suggestionbox height="200" width="400"
                                            for="modalidade"
                                            fetchValue="#{result}"
                                            suggestionAction="#{LancamentoProdutoColetivoControle.executarAutocompleteModalidade}"
                                            minChars="1" rowClasses="20"
                                            status="true"
                                            nothingLabel="Nenhum produto encontrado!"
                                            var="result" id="suggestionModalidade">
                            <a4j:support event="onselect" ignoreDupResponses="true"
                                         action="#{LancamentoProdutoColetivoControle.selecionarModalidadeSuggestionBox}"
                                         focus="modalidade" reRender="form"/>

                            <h:column>
                                <f:facet name="header">
                                    <h:outputText styleClass="textverysmall" value="Descrição"/>
                                </f:facet>
                                <h:outputText value="#{result.nome}"/>

                            </h:column>
                        </rich:suggestionbox>
                    </h:panelGroup>

                    <h:outputText value="Parcelar em :"/>
                    <h:panelGroup>
                        <h:selectOneMenu value="#{LancamentoProdutoColetivoControle.lancamento.nrVezesParcelar}">
                            <f:selectItems value="#{LancamentoProdutoColetivoControle.nrParcelas}"/>
                        </h:selectOneMenu>
                        <h:outputText value="vezes"/>
                    </h:panelGroup>

                    <h:outputText value="Válido até:" rendered="#{!LancamentoProdutoColetivoControle.lancamento.tipoData}"/>
                    <rich:calendar id="fim"  oninputchange="validar_Data(this.id);atualizarPainelMensagens();"
                                   onchanged="atualizarPainelMensagens();"
                                   value="#{LancamentoProdutoColetivoControle.lancamento.dataFim}"
                                   rendered="#{!LancamentoProdutoColetivoControle.lancamento.tipoData}"
                                   enableManualInput="true" popup="true"
                                   inputSize="10"
                                   datePattern="dd/MM/yyyy"
                                   showApplyButton="false">
                    </rich:calendar>

                    <h:outputText value="Matrículas específicas:" styleClass="tooltipster"
                                 title="Ao informar uma ou mais matrículas (separadas por vírgula) o sistema vai manter os outros filtros, porém se limitando a estes alunos"/>
                    <h:inputText value="#{LancamentoProdutoColetivoControle.lancamento.matriculas}"
                                 title="Ao informar uma ou mais matrículas (separadas por vírgula) o sistema vai manter os outros filtros, porém se limitando a estes alunos"
                                 styleClass="form tooltipster"  size="80" />

                    <h:outputText value="Lançar produtos ao gravar:"/>
                    <h:panelGroup>
                        <h:selectBooleanCheckbox value="#{LancamentoProdutoColetivoControle.lancarAoGravar}"
                                                 styleClass="tooltipster"
                                                 title="Lançar o produto selecionado assim que acionar o botão 'Gravar'">
                            <a4j:support reRender="labelIgnorar, checkIgnorar, painelMensagens" event="onclick"/>
                        </h:selectBooleanCheckbox>
                        <h:outputText value="(ESTE LANÇAMENTO JÁ GEROU PRODUTOS)"
                                      rendered="#{LancamentoProdutoColetivoControle.lancamento.jaFoiLancado}"/>
                    </h:panelGroup>
                    <h:panelGroup id="labelIgnorar">
                        <h:outputText value="Ignorar já lançados:" rendered="#{LancamentoProdutoColetivoControle.lancarAoGravar}"/>
                    </h:panelGroup>

                    <h:panelGroup id="checkIgnorar">
                        <h:selectBooleanCheckbox value="#{LancamentoProdutoColetivoControle.ignorarJaLancados}"
                                                 rendered="#{LancamentoProdutoColetivoControle.lancarAoGravar}"
                                                 title="Ao lançar, ignorar alunos que já tem produto gerado por este lançamento">
                            <a4j:support reRender="painelMensagens" event="onclick"/>
                        </h:selectBooleanCheckbox>
                    </h:panelGroup>

                </h:panelGrid>

                <h:panelGrid columns="1" width="100%" styleClass="tabMensagens" id="painelMensagens">
                    <h:outputText styleClass="mensagem"  value="#{LancamentoProdutoColetivoControle.descricaoFiltros}"/>

                    <h:panelGrid columns="3" width="100%" styleClass="tabMensagens">
                        <h:panelGrid columns="1" width="100%">
                            <h:outputText value=" "/>
                        </h:panelGrid>
                        <h:commandButton id="icProdutoSuc" rendered="#{LancamentoProdutoColetivoControle.sucesso}" image="./imagens/sucesso.png"/>
                        <h:commandButton id="icProdutoFal" rendered="#{LancamentoProdutoColetivoControle.erro}" image="./imagens/erro.png"/>
                        <h:panelGrid columns="1" width="100%">
                            <h:outputText id="msgProduto" styleClass="mensagem"  value="#{LancamentoProdutoColetivoControle.mensagem}"/>
                            <h:outputText id="msgProdutoDet" styleClass="mensagemDetalhada" value="#{LancamentoProdutoColetivoControle.mensagemDetalhada}"/>
                        </h:panelGrid>
                    </h:panelGrid>

                    <h:panelGrid columnClasses="centralizado" width="100%">

                        <h:panelGroup>
                            <a4j:commandButton id="verAmostra"
                                               action="#{LancamentoProdutoColetivoControle.consultarAmostraClientes}"
                                               reRender="panelAmostraClientes, painelMensagens"
                                               oncomplete="#{LancamentoProdutoColetivoControle.msgAlert}"
                                               value="Ver amostra de clientes"
                                               accesskey="2" styleClass="botoes nvoBt btSec"/>

                            <a4j:commandButton id="verAmostraAlcançados"
                                               action="#{LancamentoProdutoColetivoControle.consultarJaAlcancados}"
                                               reRender="panelAmostraClientes, painelMensagens"
                                               rendered="#{LancamentoProdutoColetivoControle.lancamento.jaFoiLancado}"
                                               oncomplete="#{LancamentoProdutoColetivoControle.msgAlert}"
                                               value="Ver clientes já alcançados"
                                               accesskey="2" styleClass="botoes nvoBt btSec"/>
                        </h:panelGroup>
                    </h:panelGrid>


                    <h:panelGrid columns="1" width="100%" 
                                 columnClasses="colunaCentralizada"
                                 style="alinhar">
                        <h:panelGroup>

                            <h:commandButton id="novo" immediate="true"
                                             action="#{LancamentoProdutoColetivoControle.novo}"
                                             value="#{msg_bt.btn_novo}"
                                             styleClass="botoes nvoBt btSec"
                                             title="#{msg.msg_novo_dados}"
                                             accesskey="1"/>

                            <a4j:commandButton id="salvar" action="#{LancamentoProdutoColetivoControle.gravar}"
                                               value="Gravar"
                                               reRender="form, panelAutorizacaoFuncionalidade"
                                               oncomplete="#{LancamentoProdutoColetivoControle.msgAlert}"
                                               title="#{msg.msg_gravar_dados}" accesskey="2" styleClass="botoes nvoBt"/>

                            <a4j:commandButton id="estornar" action="#{LancamentoProdutoColetivoControle.estornar}"
                                               value="Estornar produtos em aberto"
                                               rendered="#{LancamentoProdutoColetivoControle.lancamento.codigo > 0}"
                                               reRender="form, panelAutorizacaoFuncionalidade"
                                               oncomplete="#{LancamentoProdutoColetivoControle.msgAlert}"
                                               title="#{msg.msg_gravar_dados}" accesskey="2" styleClass="botoes nvoBt btSec"/>


                            <h:panelGroup id="grupoBtnExcluir">
                                <a4j:commandButton id="excluir"  reRender="mdlMensagemGenerica"
                                                   oncomplete="#{LancamentoProdutoColetivoControle.msgAlert}" action="#{LancamentoProdutoColetivoControle.confirmarExcluir}"
                                                   value="#{msg_bt.btn_excluir}" title="#{msg.msg_excluir_dados}" accesskey="3" styleClass="botoes nvoBt btSec btPerigo"/>
                            </h:panelGroup>

                            <a4j:commandButton id="consultar" immediate="true"
                                             action="#{LancamentoProdutoColetivoControle.inicializarConsultar}"
                                             value="#{msg_bt.btn_voltar_lista}"
                                             title="#{msg.msg_consultar_dados}" accesskey="4"
                                             styleClass="botoes nvoBt btSec"/>

                            <a4j:commandLink action="#{LancamentoProdutoColetivoControle.realizarConsultaLogObjetoSelecionado}"
                                             reRender="form" oncomplete="abrirPopup('visualizadorLogForm.jsp', 'VisualizadorLog', 785, 595);"
                                             title="Visualizar Log"
                                             style="display: inline-block; padding: 8px 15px;"
                                             styleClass="botoes nvoBt btSec">
                                <i class="fa-icon-list" ></i>
                            </a4j:commandLink>
                        </h:panelGroup>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
            <rich:jQuery id="mskData" selector=".rich-calendar-input" timing="onload" query="mask('99/99/9999')" />
        </h:form>
    </h:panelGrid>


    <rich:modalPanel id="panelAmostraClientes" styleClass="novaModal" autosized="true" shadowOpacity="true" width="570"
                     height="330">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="#{fn:length(LancamentoProdutoColetivoControle.listaAmostra)} alunos"/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText
                        styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                        id="hiperlinkAmostraClientes"/>
                <rich:componentControl for="panelAmostraClientes" attachTo="hiperlinkAmostraClientes" operation="hide"
                                       event="onclick"/>
            </h:panelGroup>
        </f:facet>
        <a4j:form id="formAmostraClientes" style="overflow:hidden">

            <rich:dataTable value="#{LancamentoProdutoColetivoControle.listaAmostra}" var="amostra" width="100%" id="itensAmostra"
                            styleClass="tabelaSimplesCustom"
                            rows="10">
                <rich:column sortBy="#{amostra.matricula}" headerClass="col-text-align-left" styleClass="col-text-align-left">
                    <f:facet name="header">
                        <h:outputText styleClass="texto-font texto-cor-cinza" value="#{msg_aplic.prt_Cliente_label_matricula_maiusculo}"/>
                    </f:facet>
                    <h:outputText styleClass="texto-font texto-cor-cinza"  value="#{amostra.matricula}"/>
                </rich:column>

                <rich:column sortBy="#{amostra.nome}" headerClass="col-text-align-left" styleClass="col-text-align-left" >
                    <f:facet name="header">
                        <h:outputText styleClass="texto-font texto-cor-cinza"  value="#{msg_aplic.prt_Cliente_label_nome_maiusculo}"/>
                    </f:facet>
                    <a4j:commandLink styleClass="linkPadrao texto-font texto-cor-cinza" action="#{MalaDiretaControle.irParaTelaCliente}"
                                     value="#{amostra.nome}" 
                                     oncomplete="#{MalaDiretaControle.msgAlert}"/>
                </rich:column>

            </rich:dataTable>
            <rich:datascroller styleClass="scrollPureCustom" for="itensAmostra" renderIfSinglePage="false" status="false" />
        </a4j:form>
    </rich:modalPanel>
    <%@include file="includes/autorizacao/include_autorizacao_funcionalidade.jsp" %>
    <%@include file="includes/include_modal_mensagem_generica.jsp"%>
</f:view>
<script>
    document.getElementById("form:descricao").focus();
</script>
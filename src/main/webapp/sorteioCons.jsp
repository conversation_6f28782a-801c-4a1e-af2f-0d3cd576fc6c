<%@page contentType="text/html;charset=UTF-8" %>
<head>
    <%@include file="/includes/include_import_minifiles.jsp" %>
</head>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<script type="text/javascript" language="javascript" src="hoverform.js"></script>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax" %>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich" %>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core" %>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<meta name="viewport" content="width=device-width, initial-scale=1.0">
<link href="beta/css/pure-min.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/bootstrap-tabs.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/pure-ext.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<script src="beta/js/jquery.js" type="text/javascript"></script>
<script src="beta/js/jquery.dataTables.min.js" type="text/javascript"></script>
<script src="beta/js/DT_bootstrap.js" type="text/javascript"></script>
<script src="beta/js/bootstrap-tab.js" type="text/javascript"></script>
<script>
    jQuery.noConflict();
</script>

<c:set var="contexto" value="${pageContext.request.contextPath}" scope="request"/>

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title>${msg_aplic.prt_sorteio_ultimosSorteios}</title>

    <%-- INICIO HEADER --%>
    <c:set var="titulo" scope="session" value=" ${msg_aplic.prt_sorteio_ultimosSorteios}"/>
    <c:set var="urlWiki" scope="session"  value="semWiki"/>
    <h:panelGroup layout="block" styleClass="pure-g-r">
        <jsp:include page="topoReduzido_material.jsp"/>
    </h:panelGroup>
    <%-- FIM HEADER --%>

    <h:panelGroup layout="block" styleClass="pure-g-r">
        <h:form id="form" styleClass="pure-form pure-u-1">
            <input id="chavePrimaria" type="hidden" value="" name="chavePrimaria"/>
            <%-- INICIO CONTENT --%>
            <h:panelGroup>
                <%-- INICIO COMANDOS --%>
                <h:panelGroup layout="block" styleClass="pure-g-r pure-u-11-12 margin-0-auto margin-v-10">

                    <h:panelGroup layout="block"  styleClass="pure-u-3-3 text-right pull-right">
                        <a4j:commandLink id="btnConfig"
                                         styleClass="pure-button "
                                         action="#{SorteioControle.prepararConfiguracoes}"
                                         accesskey="1">
                            <i class="fa-icon-cog"></i> &nbsp ${msg_bt.btn_configuracoes}
                        </a4j:commandLink>

                        <a4j:commandLink id="btnNovo"
                                         styleClass="pure-button pure-button-primary  margin-h-10"
                                         style="margin-right: 0 !important;"
                                         action="#{SorteioControle.sortearCliente}"
                                         accesskey="1">
                            <i class="fa-icon-random"></i> &nbsp ${msg_bt.btn_sortear}
                        </a4j:commandLink>
                    </h:panelGroup>
                </h:panelGroup>
                <%-- FIM COMANDOS --%>

                <table id="tblAutorizacao" class="tabelaSorteios pure-g-r pure-u-11-12 margin-0-auto">
                    <thead>
                    <tr>
                        <th>Código</th>
                        <th>Cliente</th>
                        <th>Data do Sorteio</th>
                        <th>Observações</th>
                        <th>Usuário</th>
                    </tr>
                    </thead>
                    <tbody></tbody>
                </table>

                <a4j:jsFunction name="jsEditar" action="#{AutorizacaoAcessoGrupoEmpresarialControle.editar}"
                                reRender="mensagem"/>

            </h:panelGroup>
            <%-- FIM CONTENT --%>

            <%-- INICIO FOOTER --%>
            <h:panelGroup id="mensagem" layout="block" styleClass="pure-u-11-12 margin-0-auto">
                <h:graphicImage id="iconSucesso" rendered="#{SorteioControle.sucesso}" value="./imagens/sucesso.png"/>
                <h:graphicImage id="iconErro" rendered="#{SorteioControle.erro}" value="./imagens/erro.png"/>

                <h:outputText styleClass="mensagem" rendered="#{not empty SorteioControle.mensagem}"
                              value=" #{SorteioControle.mensagem}"/>
                <h:outputText styleClass="mensagemDetalhada" rendered="#{not empty SorteioControle.mensagemDetalhada}"
                              value=" #{SorteioControle.mensagemDetalhada}"/>
            </h:panelGroup>
            <%-- FIM FOOTER --%>
        </h:form>

        <rich:modalPanel id="panelStatus" autosized="true">
            <h:panelGrid columns="2" styleClass="titulo3" columnClasses="titulo3">
                <h:graphicImage url="./imagens/carregando.gif" style="border:none"/>
                <h:outputText styleClass="titulo3" value="Carregando..."/>
            </h:panelGrid>
        </rich:modalPanel>
        <a4j:status onstart="Richfaces.showModalPanel('panelStatus');"
                    onstop="Richfaces.hideModalPanel('panelStatus');"/>
    </h:panelGroup>

    <script src="beta/js/ext-funcs.js" type="text/javascript"></script>

    <script>
        jQuery(window).on("load", function () {
            iniciarTabela("tabelaSorteios", "${contexto}/prest/basico/sorteio", 0, "desc", "", true);
        });
    </script>

</f:view>

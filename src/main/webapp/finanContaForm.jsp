<%@page contentType="text/html;charset=UTF-8" %> 
<head><%@include file="/includes/include_import_minifiles.jsp" %></head>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<script type="text/javascript" language="javascript" src="hoverform.js"></script>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>
<link href="beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/pure-ext.css" type="text/css" rel="stylesheet"/>
<f:loadBundle var="msg_bt" basename="propriedades.Botoes" />
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao" />
<f:loadBundle var="msg" basename="propriedades.Mensagens" />
<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title><h:outputText value="#{msg_aplic.prt_Finan_Conta_tituloForm}" /></title>
    <c:set var="titulo" scope="session" value="${msg_aplic.prt_Finan_Conta_tituloForm}"/>
    <c:set var="urlWiki" scope="session" value="${SuperControle.urlBaseConhecimento}como-cadastrar-ou-editar-uma-conta-para-movimentacoes-financeiras/"/>
    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <f:facet name="header">
            <jsp:include page="topoReduzido_material.jsp" />

        </f:facet>
        <h:form id="form">
            <hr style="border-color: #e6e6e6;"/>
            <h:panelGrid columns="1" width="100%">
                <!-- O FORMULARIO -->
                <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar"
                             columnClasses="classEsquerda, classDireita" width="100%">

                    <!-- Empresa -->
                    <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Finan_Conta_empresa}" />
                    <h:selectOneMenu onblur="blurinput(this);"
                                     onfocus="focusinput(this);" styleClass="form" id="empresaCombo"
                                     value="#{ContaControle.contaVO.empresa.codigo}">
                        <f:selectItems value="#{ContaControle.empresaCombo}" />
                    </h:selectOneMenu>
                    <!-- Tipo de Conta -->
                    <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Finan_Conta_tipoConta}" />
                    <h:selectOneMenu onblur="blurinput(this);"
                                     onfocus="focusinput(this);" styleClass="form" id="tipoContaCombo"
                                     value="#{ContaControle.contaVO.tipoConta.codigo}">
                        <f:selectItems value="#{ContaControle.tipoContaCombo}" />
                    </h:selectOneMenu>
                    <!-- Descrição -->
                    <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Finan_Conta_descricaoConta}" />
                    <h:inputText onblur="blurinput(this);" onfocus="focusinput(this);"
                                 styleClass="form" size="80" maxlength="100"
                                 value="#{ContaControle.contaVO.descricao}"
                                 id="descricaoRichEditor" />

                    <!-- Descrição curta -->
                    <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_descricao_curta}" />
                    <h:inputText onblur="blurinput(this);" onfocus="focusinput(this);"
                                 styleClass="form" size="10" maxlength="10"
                                 value="#{ContaControle.contaVO.descricaoCurta}"
                                 id="descricaoCurtaRichEditor" />
                    <!-- Banco -->
                    <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Finan_Conta_banco}" />
                    <h:selectOneMenu onblur="blurinput(this);"
                                     onfocus="focusinput(this);" styleClass="form" id="bancoCombo"
                                     value="#{ContaControle.contaVO.banco.codigo}">
                        <f:selectItems value="#{ContaControle.bancoCombo}" />
                    </h:selectOneMenu>
                    <!-- Agência -->
                    <!-- Agência DV -->
                    <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Finan_Conta_agencia}" />
                    <h:panelGrid columns="2" width="30%">
                        <h:inputText onblur="blurinput(this);" onfocus="focusinput(this);"
                                     styleClass="form" size="15" maxlength="16"
                                     value="#{ContaControle.contaVO.agencia}" id="agencia" />
                        <h:inputText onblur="blurinput(this);" onfocus="focusinput(this);"
                                     styleClass="form" size="3" maxlength="2"
                                     value="#{ContaControle.contaVO.agenciaDV}" id="agenciaDV" />
                    </h:panelGrid>
                    <!-- Número -->
                    <!-- Número DV -->
                    <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Finan_Conta_numeroConta}" />
                    <h:panelGrid columns="2" width="30%">
                        <h:inputText onblur="blurinput(this);" onfocus="focusinput(this);"
                                     styleClass="form" size="15" maxlength="16"
                                     value="#{ContaControle.contaVO.numero}" id="numero" />
                        <h:inputText onblur="blurinput(this);" onfocus="focusinput(this);"
                                     styleClass="form" size="2" maxlength="1"
                                     value="#{ContaControle.contaVO.numeroDV}" id="numeroDV" />
                    </h:panelGrid>
                    <!-- Desativada -->
                    <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Finan_Conta_contaAtiva}" />
                    <h:panelGrid columns="2" width="10%">
                        <h:selectBooleanCheckbox styleClass="campos" id="desativada"
                                                 value="#{ContaControle.contaVO.ativa}">
                            <a4j:support event="onchange" reRender="form"/>
                        </h:selectBooleanCheckbox>
                    </h:panelGrid>
                    <!-- Mostrar no DRE e Demonstrativo -->
                    <h:outputText rendered="#{!ContaControle.contaVO.ativa}" id="textMostrarDREDemonstrativo" styleClass="tituloCampos" value="Mostrar conta no filtro do DRE e Demonstrativo Financeiro?" />
                    <h:panelGrid columns="2" width="10%" rendered="#{!ContaControle.contaVO.ativa}">
                        <h:selectBooleanCheckbox styleClass="campos" id="mostrarDREDemonstrativo"
                                                 value="#{ContaControle.contaVO.mostrarDREDemonstrativo}" />
                    </h:panelGrid>
                    <!-- Mostrar no BI -->
                    <h:outputText styleClass="tituloCampos"
                                  value="#{msg_aplic.prt_Finan_Conta_contaTotalizadaBI}" />
                    <h:panelGrid columns="2" width="10%">
                        <h:selectBooleanCheckbox styleClass="campos" id="mostrarBi"
                                                 value="#{ContaControle.contaVO.mostrarBi}" />
                    </h:panelGrid>
                    <!-- Observação -->
                    <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Finan_Conta_observacao}" />
                    <rich:editor id="observacaoRichEditor" useSeamText="false"
                                 viewMode="visual" width="400" height="25"
                                 value="#{ContaControle.contaVO.observacao}" />

                </h:panelGrid>
                <!-- FIM DO FORMULÁRIO -->
                <h:panelGrid id="panelMensagem" columns="1" width="100%" styleClass="tabMensagens">
                    <!-- mensagens -->
                    <h:panelGrid columns="3" width="100%" styleClass="tabMensagens">
                        <h:panelGrid columns="1" width="100%">
                            <h:outputText value=" " />
                        </h:panelGrid>
                        <h:commandButton rendered="#{ContaControle.sucesso}"
                                         image="./imagens/sucesso.png" />
                        <h:commandButton rendered="#{ContaControle.erro}"
                                         image="./imagens/erro.png" />
                        <h:panelGrid columns="1" width="100%">
                            <h:outputText styleClass="mensagem"
                                          value="#{ContaControle.mensagem}" />
                            <h:outputText styleClass="mensagemDetalhada"
                                          value="#{ContaControle.mensagemDetalhada}" />
                        </h:panelGrid>
                    </h:panelGrid>
                    <!-- fim mensagens -->
                    <!-- botões -->
                    <%--<h:panelGrid columns="1" width="100%"--%>
                                 <%--columnClasses="colunaCentralizada">--%>
                        <%--<h:panelGroup>--%>
                            <%--<h:outputText value="    " />--%>
                            <%--<h:commandButton id="excluir"--%>
                                             <%--rendered="#{!empty ContaControle.contaVO.codigo}"--%>
                                             <%--onclick="return confirm('Confirma exclusão desta conta?');"--%>
                                             <%--action="#{ContaControle.excluir}" value="#{msg_bt.btn_excluir}"--%>
                                             <%--image="./imagens/botaoExcluir.png"--%>
                                             <%--alt="#{msg.msg_excluir_dados}" accesskey="3"--%>
                                             <%--styleClass="botaoExcluir" />--%>
                            <%--<h:outputText value="    " />--%>
                            <%--<h:commandButton id="consultar" immediate="true"--%>
                                             <%--action="#{ContaControle.consultarFiltroPadrao}"--%>
                                             <%--value="#{msg_bt.btn_consultar}"--%>
                                             <%--image="./imagens/botaoConsultar.png"--%>
                                             <%--alt="#{msg.msg_consultar_dados}" accesskey="4"--%>
                                             <%--styleClass="botoes" />--%>
                        <%--</h:panelGroup>--%>
                    <%--</h:panelGrid>--%>
                    <h:panelGrid columns="1" width="100%"
                                 columnClasses="colunaCentralizada">
                        <h:panelGroup>

                            <h:commandButton immediate="true"
                                             action="#{ContaControle.novo}"
                                             value="#{msg_bt.btn_novo}"
                                             styleClass="botoes nvoBt btSec"
                                             title="#{msg.msg_novo_dados}"
                                             accesskey="1"/>

                            <a4j:commandButton action="#{ContaControle.gravar}"
                                               value="Gravar"
                                               reRender="panelMensagem"
                                               oncomplete="#{ContaControle.msgAlert}"
                                               alt="#{msg.msg_gravar_dados}" accesskey="2" styleClass="botoes nvoBt"/>

                            <h:commandButton rendered="#{!empty ContaControle.contaVO.codigo}"
                                             onclick="return confirm('Confirma exclusão desta conta?');"
                                             action="#{ContaControle.excluir}"
                                             value="#{msg_bt.btn_excluir}"
                                             title="#{msg.msg_excluir_dados}"
                                             accesskey="3"
                                             styleClass="botoes nvoBt btSec btPerigo"/>

                            <h:commandButton immediate="true"
                                             action="#{ContaControle.consultarFiltroPadrao}"
                                             value="#{msg_bt.btn_voltar_lista}"
                                             title="#{msg.msg_consultar_dados}" accesskey="4"
                                             styleClass="botoes nvoBt btSec"/>

                            <a4j:commandLink id="visualizarLog"
                                             immediate="true"
                                             action="#{ContaControle.realizarConsultaLogObjetoSelecionado}"
                                             accesskey="5"
                                             style="display: inline-block; padding: 9px 13px;"
                                             styleClass="botoes nvoBt btSec"
                                             onclick="fireElementFromAnyParent('form:btnAtualizaTempo');"
                                             oncomplete="abrirPopup('visualizadorLogForm.jsp', 'VisualizadorLog', 785, 595);">
                                <i style="text-decoration: none" class="fa-icon-list"/>
                            </a4j:commandLink>

                        </h:panelGroup>
                    </h:panelGrid>
                    <!-- fim botoes -->
                </h:panelGrid>
            </h:panelGrid>
        </h:form>
    </h:panelGrid>
</f:view>

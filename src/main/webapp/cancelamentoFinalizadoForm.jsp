<%@page contentType="text/html"%>
<%@page pageEncoding="ISO-8859-1"%>
<head><%@include file="/includes/include_import_minifiles.jsp" %></head>

<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<script type="text/javascript" language="javascript" src="hoverform.js"></script>
<script type="text/javascript" language="javascript">
     setTimeout(setInterval(function() { getDocumentCookie('popupsImportante') == 'close' ? this.close() : ''; }, 500), 500);
</script>
<link href="beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/pure-buttons.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/pure-ext.css" type="text/css" rel="stylesheet"/>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>
<style type="text/css">
    .pure-button{
        font-family: sans-serif;
        font-size:100%;
        margin:0;
        vertical-align:baseline;
        *vertical-align:middle
    }
</style>
<f:view>
    <%@include file="includes/include_carregando_ripple.jsp" %>
    <title>
        <c:set var="urlWiki" scope="session" value="${SuperControle.urlBaseConhecimento}como-faco-para-cancelar-um-plano-com-devolucao-de-valores/"/>
        <c:set var="titulo" scope="session" value="Cancelamento"/>
    </title>    

    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <f:facet name="header">
            <jsp:include page="topoReduzido_material.jsp"/>
        </f:facet>
    </h:panelGrid>
    <div styleClass="col-md-12"><hr class="dividerFundoClaro"/></div>
    <!-- FRED -->
    <h:form id="form">
        <h:panelGrid columns="2" >
            <h:panelGrid columns="1" width="100%" cellpadding="10" style="margin-left: auto; margin-right: auto; padding-left: 70px; padding-right: 70px;">
                <h:panelGroup>
                    <h:outputText value="NOME DO CLIENTE: " styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold"/>
                    <rich:spacer width="12"/>
                    <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{ClienteControle.clienteVO.pessoa.nome}"/>
                </h:panelGroup>

                <h:panelGrid columns="2" columnClasses="colunaEsquerda, colunaDireita" rendered="#{CancelamentoContratoControle.contratoVO.cancelamentoAntecipado}">
                    <h:outputText value="VALOR CANCELAMENTO " styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold"/>
                    <h:panelGroup>
                        <rich:spacer width="12"/>
                        <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{MovPagamentoControle.empresaLogado.moeda} " />
                        <h:outputText id="valorTotalCancelamentoAntecipado" value="#{CancelamentoContratoControle.cancelamentoContratoVO.valorTotalCancelamentoAntecipado}" styleClass="texto-size-14-real texto-cor-cinza texto-font">
                            <f:converter converterId="FormatadorNumerico" />
                        </h:outputText>
                    </h:panelGroup>
                </h:panelGrid>

                <h:panelGrid columns="2" columnClasses="colunaEsquerda, colunaDireita" rendered="#{!CancelamentoContratoControle.contratoVO.cancelamentoAntecipado}">
                    <h:outputText value="VALOR PAGO PELO CLIENTE NO CONTRATO" styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold"/>
                    <h:panelGroup>
                        <rich:spacer width="12"/>
                        <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{MovPagamentoControle.empresaLogado.moeda} " />
                        <h:outputText id="valorPago" value="#{CancelamentoContratoControle.cancelamentoContratoVO.somaValorPagoPeloClienteComCheque}" styleClass="texto-size-14-real texto-cor-cinza texto-font">
                            <f:converter converterId="FormatadorNumerico" />
                        </h:outputText>
                    </h:panelGroup>
                    <h:outputText value="VALOR DO CANCELAMENTO DO CLIENTE" styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold"/>
                    <h:panelGroup>
                        <rich:spacer width="12"/>
                        <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{MovPagamentoControle.empresaLogado.moeda} " />
                        <h:outputText id="valorCobrado" value="#{CancelamentoContratoControle.cancelamentoContratoVO.valorASerPagoPeloCliente}" styleClass="texto-size-14-real texto-cor-cinza texto-font">
                            <f:converter converterId="FormatadorNumerico" />
                        </h:outputText>
                    </h:panelGroup>
                    <h:panelGroup rendered="#{CancelamentoContratoControle.cancelamentoContratoVO.liberacaoCancelamento or CancelamentoContratoControle.cancelamentoContratoVO.valorASerDevolvidoBaseCalculo < 0}">
                        <h:panelGroup rendered="#{!CancelamentoContratoControle.cancelamentoContratoVO.liberacaoCancelamento}">
                            <h:outputText value="VALOR COBRADO DO CLIENTE " rendered="#{!CancelamentoContratoControle.alterarCancelamento}" styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold"/>
                            <h:outputText value="VALOR QUE DEVERIA SER COBRADO DO CLIENTE " rendered="#{CancelamentoContratoControle.alterarCancelamento}" styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold"/>
                        </h:panelGroup>
                        <h:panelGroup rendered="#{CancelamentoContratoControle.cancelamentoContratoVO.liberacaoCancelamento}">
                            <h:outputText value="VALOR DA MULTA QUE FOI ISENTADO AO CLIENTE " rendered="#{!CancelamentoContratoControle.alterarCancelamento}" styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold"/>
                            <h:outputText value="VALOR QUE DEVERIA SER LIBERADO PARA O CLIENTE " rendered="#{CancelamentoContratoControle.alterarCancelamento}" styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold"/>
                        </h:panelGroup>
                    </h:panelGroup>

                    <h:panelGroup rendered="#{CancelamentoContratoControle.alterarCancelamento and CancelamentoContratoControle.cancelamentoContratoVO.valorASerDevolvidoBaseCalculo > 0}">
                        <h:panelGroup rendered="#{CancelamentoContratoControle.cancelamentoContratoVO.liberacaoCancelamento}">
                            <h:outputText value="VALOR COBRADO DO CLIENTE " rendered="#{!CancelamentoContratoControle.alterarCancelamento}" styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold"/>
                            <h:outputText value="VALOR QUE DEVERIA SER COBRANDO DO CLIENTE" rendered="#{CancelamentoContratoControle.alterarCancelamento}" styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold"/>
                        </h:panelGroup>
                        <h:panelGroup rendered="#{!CancelamentoContratoControle.cancelamentoContratoVO.liberacaoCancelamento}">
                            <h:outputText value="VALOR LIBERADO PARA O CLIENTE" rendered="#{!CancelamentoContratoControle.alterarCancelamento}" styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold"/>
                            <h:outputText value="VALOR QUE DEVERIA SER LIBERADO PARA O CLIENTE" rendered="#{CancelamentoContratoControle.alterarCancelamento}" styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold"/>
                        </h:panelGroup>
                    </h:panelGroup>

                    <h:panelGroup rendered="#{CancelamentoContratoControle.cancelamentoContratoVO.quitacaoManualCancelamento or CancelamentoContratoControle.cancelamentoContratoVO.valorASerDevolvidoBaseCalculo < 0}">
                        <rich:spacer width="12"/>
                        <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{MovPagamentoControle.empresaLogado.moeda} " />
                        <h:panelGroup rendered="#{!CancelamentoContratoControle.cancelamentoContratoVO.quitacaoManualCancelamento or CancelamentoContratoControle.alterarCancelamento}">
                            <h:outputText id="valorDevolvido" styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{CancelamentoContratoControle.cancelamentoContratoVO.valorASerDevolvidoBaseCalculo_Apresentar}">
                                <f:converter converterId="FormatadorNumerico" />
                            </h:outputText>
                        </h:panelGroup>
                        <h:panelGroup rendered="#{CancelamentoContratoControle.cancelamentoContratoVO.quitacaoManualCancelamento and !CancelamentoContratoControle.alterarCancelamento}">
                            <h:outputText id="valorManual" styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold" value="#{CancelamentoContratoControle.cancelamentoContratoVO.valorQuitacaoCancelamento}">
                                <f:converter converterId="FormatadorNumerico" />
                            </h:outputText>
                        </h:panelGroup>
                    </h:panelGroup>

                    <%-- INICIO - Quando houver alteração do Cancelamento --%>
                    <h:panelGroup rendered="#{CancelamentoContratoControle.cancelamentoContratoVO.quitacaoManualCancelamento and CancelamentoContratoControle.alterarCancelamento}">
                        <h:panelGroup>
                            <h:outputText value="VALOR COBRADO DO CLIENTE" styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold"/>
                        </h:panelGroup>
                    </h:panelGroup>

                    <h:panelGroup rendered="#{CancelamentoContratoControle.cancelamentoContratoVO.quitacaoManualCancelamento and CancelamentoContratoControle.alterarCancelamento}">
                        <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{MovPagamentoControle.empresaLogado.moeda} " />
                        <h:outputText id="valorManual_alterado" styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{CancelamentoContratoControle.cancelamentoContratoVO.valorQuitacaoCancelamento}">
                            <f:converter converterId="FormatadorNumerico" />
                        </h:outputText>
                    </h:panelGroup>

                    <h:panelGroup rendered="#{CancelamentoContratoControle.cancelamentoContratoVO.devolucaoManualCancelamento and CancelamentoContratoControle.alterarCancelamento}">
                        <h:panelGroup>
                            <h:outputText value="VALOR LIBERADO PARA O CLIENTE" styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold"/>
                        </h:panelGroup>
                    </h:panelGroup>

                    <h:panelGroup rendered="#{CancelamentoContratoControle.cancelamentoContratoVO.devolucaoManualCancelamento and CancelamentoContratoControle.alterarCancelamento}">
                        <h:outputText id="valorDevolvido_alterado" styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{CancelamentoContratoControle.cancelamentoContratoVO.valorDevolvidoManualApresentar}"/>
                    </h:panelGroup>
                    <%-- FINAL - Quando houver alteração do Cancelamento --%>

                    <h:panelGroup rendered="#{CancelamentoContratoControle.cancelamentoContratoVO.valorRecebiveis > 0}">

                        <h:outputLink styleClass="linkWiki"
                                      value="#{SuperControle.urlWiki}Informa%C3%A7%C3%B5es_do_Cliente:Contrato:Opera%C3%A7%C3%B5es_Contrato:Afastamento:Cancelamento#Valor_devolvido_em_Receb.C3.ADveis"
                                      title="Clique e saiba mais: Devolução de Recebíveis"
                                      target="_blank">
                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold"
                                    value="VALOR DEVOLVIDO EM RECEBÍVEIS"/>
                        </h:outputLink>
                        <h:outputLink styleClass="linkWiki"
                                      value="#{SuperControle.urlBaseConhecimento}como-faco-para-cancelar-um-plano-com-devolucao-de-valores/"
                                      title="Clique e saiba mais: Devolução de Recebíveis"
                                      target="_blank">
                            <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                        </h:outputLink>
                    </h:panelGroup>
                    <h:panelGroup rendered="#{CancelamentoContratoControle.cancelamentoContratoVO.valorRecebiveis > 0}">
                        <h:outputLink styleClass="linkWiki"
                                      value="#{SuperControle.urlWiki}Informa%C3%A7%C3%B5es_do_Cliente:Contrato:Opera%C3%A7%C3%B5es_Contrato:Afastamento:Cancelamento#Valor_devolvido_em_Receb.C3.ADveis"
                                      title="Clique e saiba mais: Devolução de Recebíveis"
                                      target="_blank">
                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{MovPagamentoControle.empresaLogado.moeda} "/>
                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font"
                                          value="#{CancelamentoContratoControle.cancelamentoContratoVO.valorRecebiveis}">
                                <f:converter converterId="FormatadorNumerico"/>
                            </h:outputText>
                        </h:outputLink>
                    </h:panelGroup>
                    
                    <h:panelGroup rendered="#{!CancelamentoContratoControle.cancelamentoContratoVO.apresentarMensagemCobranca}">
                        <h:outputText value="VALOR DEVOLVIDO EM DINHEIRO" styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold"
                        			rendered="#{CancelamentoContratoControle.cancelamentoContratoVO.valorFinal > 0
                        			  			&& !CancelamentoContratoControle.cancelamentoContratoVO.liberacaoDevolucao}"/>
                       <h:outputText value="VALOR FINAL RETIDO" styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold"
                        			  rendered="#{CancelamentoContratoControle.cancelamentoContratoVO.valorFinal > 0
                        			  			 && CancelamentoContratoControle.cancelamentoContratoVO.liberacaoDevolucao}"/>

                    </h:panelGroup>

                    <h:panelGroup rendered="#{CancelamentoContratoControle.cancelamentoContratoVO.apresentarMensagemCobranca}">
                        <h:outputText value="VALOR COBRADO DO CLIENTE " styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold"
                                      rendered="#{CancelamentoContratoControle.cancelamentoContratoVO.valorFinal > 0
                        			  			&& !CancelamentoContratoControle.cancelamentoContratoVO.liberacaoDevolucao}"/>
                    </h:panelGroup>

                    <h:panelGroup>
                        <rich:spacer width="12"/>
                        <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{MovPagamentoControle.empresaLogado.moeda} "
                        			  rendered="#{CancelamentoContratoControle.cancelamentoContratoVO.valorFinal > 0}"/>
                        <h:outputText id="valorFinalDev" value="#{CancelamentoContratoControle.cancelamentoContratoVO.valorFinal}"
                                      styleClass="texto-size-14-real texto-cor-cinza texto-font"
                                      rendered="#{CancelamentoContratoControle.cancelamentoContratoVO.valorFinal > 0
                                      && !CancelamentoContratoControle.cancelamentoContratoVO.devolucaoManualCancelamento}">
                            <f:converter converterId="FormatadorNumerico" />
                        </h:outputText>
                            <h:outputText id="valorManualDev" styleClass="texto-size-14-real texto-cor-cinza texto-font"
                                          value="#{CancelamentoContratoControle.cancelamentoContratoVO.valorDevolucaoCancelamento}"
                                          rendered="#{CancelamentoContratoControle.cancelamentoContratoVO.valorFinal > 0
                                      		&& CancelamentoContratoControle.cancelamentoContratoVO.devolucaoManualCancelamento}">
                                <f:converter converterId="FormatadorNumerico" />
                            </h:outputText>
                    </h:panelGroup>
                </h:panelGrid>
                
                <h:panelGroup rendered="#{CancelamentoContratoControle.apresentarBotoes
                							&& CancelamentoContratoControle.cancelamentoContratoVO.devolucao  
                							&& !(CancelamentoContratoControle.cancelamentoContratoVO.quitacaoCancelamento
                							|| CancelamentoContratoControle.cancelamentoContratoVO.quitacaoManualCancelamento
                							|| CancelamentoContratoControle.cancelamentoContratoVO.liberacaoCancelamento
                							|| CancelamentoContratoControle.cancelamentoContratoVO.liberacaoDevolucao)
                                                                        && ((CancelamentoContratoControle.cancelamentoContratoVO.valorASerDevolvidoBaseCalculo_Apresentar > 0 && !CancelamentoContratoControle.cancelamentoContratoVO.devolucaoManualCancelamento)
                                                                        ||(CancelamentoContratoControle.cancelamentoContratoVO.valorDevolucaoCancelamento > 0 && CancelamentoContratoControle.cancelamentoContratoVO.devolucaoManualCancelamento))}">
                    <i class="fa-icon-warning-sign " style="color: #FF5555;"/>
	                <h:outputText id="msgDevolucao" value="#{msg.msg_devolucao}"
	                			  styleClass="texto-size-14-real texto-font texto-cor-vermelho" >
	                			  </h:outputText>
	                <h:outputText id="msgDevManual" value="#{CancelamentoContratoControle.cancelamentoContratoVO.valorDevolvidoManualApresentar}"
	                			  rendered="#{CancelamentoContratoControle.cancelamentoContratoVO.devolucaoManualCancelamento}"
	                			  styleClass="texto-size-14-real texto-cor-vermelho texto-font">
	                </h:outputText>
                    <rich:spacer width="5px;"/>
	                <h:outputText value="#{CancelamentoContratoControle.cancelamentoContratoVO.valorDevolvidoEmDinheiroApresentar}"
	                			  rendered="#{!(CancelamentoContratoControle.cancelamentoContratoVO.devolucaoManualCancelamento)}"
	                			  styleClass="texto-size-14-real texto-cor-vermelho texto-font">
	                			  </h:outputText>
	                <h:outputText value="."
	                			  styleClass="texto-size-14-real texto-cor-vermelho texto-font">
	                </h:outputText>
                </h:panelGroup>
                
                <h:panelGroup rendered="#{CancelamentoContratoControle.apresentarBotoes
                							&& (CancelamentoContratoControle.cancelamentoContratoVO.quitacaoCancelamento
                							|| CancelamentoContratoControle.cancelamentoContratoVO.quitacaoManualCancelamento)
                							&& CancelamentoContratoControle.cancelamentoContratoVO.valorQuitacaoCancelamento > 0}">
                    <i class="fa-icon-warning-sign "/>
	                <h:outputText id="msgQuitacao" value="#{msg.msg_quitacaoCancelamento}"
	                			  rendered="#{!CancelamentoContratoControle.cancelamentoContratoVO.devolucaoManualCancelamento}"
	                			  styleClass="texto-size-14-real texto-cor-vermelho texto-font"/>
	                <h:outputText value=" (#{CancelamentoContratoControle.cancelamentoContratoVO.valorQuitacaoCancelamento_Apresentar})."
	                			  styleClass="texto-size-14-real texto-cor-vermelho texto-font">
	                </h:outputText>
                </h:panelGroup>

                <h:panelGrid>
                    <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold" value="OBSERVAÇÃO:"/>
                    <h:inputTextarea value="#{CancelamentoContratoControle.cancelamentoContratoVO.observacao}" id="descricaoCalculo" styleClass=""  rows="7" cols="80"/>
                </h:panelGrid>

                <h:panelGrid id="panelMensagem" columns="2" width="100%">
                    <%--<h:outputText styleClass="mensagem"  value="#{CancelamentoContratoControle.mensagem}"/>--%>
                    <h:commandButton  rendered="#{CancelamentoContratoControle.sucesso}" image="./imagens/sucesso.png"/>
                    <h:commandButton rendered="#{CancelamentoContratoControle.erro}" image="./imagens/erro.png"/>
                    <h:outputText id="msgCancelamentoVerde" styleClass="tituloCamposVerde" value="#{CancelamentoContratoControle.mensagem}"/>
                    <h:outputText id="msgCancelamentoErro" styleClass="tituloCamposNegritoMaiorVermelho" value="#{CancelamentoContratoControle.mensagemDetalhada}"/>
                </h:panelGrid>


                <h:panelGrid id="panelMensagemAntecipado" columns="1" width="100%" rendered="#{CancelamentoContratoControle.contratoVO.cancelamentoAntecipado}">
                    <h:outputText id="mensagemEmailCancelamentoAntecipado" styleClass="#{CancelamentoContratoControle.mensagemEmailCancelamentoAntecipadoSucesso ? 'tituloCamposVerde' : 'tituloCamposNegritoMaiorVermelho'}" value="#{CancelamentoContratoControle.mensagemEmailCancelamentoAntecipado}"/>
                </h:panelGrid>

			<h:panelGrid width="100%" columns="6" columnClasses="colunaDireita">

                            <a4j:commandLink id="enviarEmailCancelamento"
                                             value="Enviar Email"
                                             rendered="#{!CancelamentoContratoControle.apresentarBotoes && CancelamentoContratoControle.contratoVO.empresa.enviarEmailCancelamento && !CancelamentoContratoControle.processandoOperacao}"
                                             title="Enviar email de cancelamento do contrato"
                                             reRender="mdlMensagemGenerica"
                                             action="#{CancelamentoContratoControle.enviarEmailCancelamentoContrato}"
                                             oncomplete="#{CancelamentoContratoControle.modalMensagemGenerica}"
                                             styleClass="pure-button"/>

                            <a4j:commandLink id="comprovanteOpCan"
                                             rendered="#{!CancelamentoContratoControle.apresentarBotoes && !CancelamentoContratoControle.processandoOperacao}"
                                             title="Imprimir Comprovante da Operação de Cancelamento"
                                             action="#{CancelamentoContratoControle.imprimirComprovanteOperacao}"
                                             oncomplete="abrirPopupPDFImpressao('relatorio/#{CancelamentoContratoControle.nomeArquivoComprovanteOperacao}','', 780, 595);"
                                             styleClass="pure-button">
                                <i class="fa-icon-print"></i>&nbsp Imprimir Comprovante
                            </a4j:commandLink>

                            <h:commandLink id="voltar"
                            				 title="Voltar Passo"
                                             styleClass="pure-button"
                            				 rendered="#{CancelamentoContratoControle.apresentarBotoes && !CancelamentoContratoControle.processandoOperacao}"
                            				 action="#{CancelamentoContratoControle.voltarTelaCancelamento}">
                                              <i class="fa-icon-arrow-left" ></i>
                            </h:commandLink>
                           

                            <a4j:commandLink id="confirmar"
                                             rendered="#{CancelamentoContratoControle.apresentarBotoes && !CancelamentoContratoControle.processandoOperacao}"
                                             title="Finalizar" reRender="form,panelMensagem,panelBotoes,panelAutorizacaoFuncionalidade"
                                             action="#{CancelamentoContratoControle.validarAutorizacao}"
                                             styleClass="pure-button pure-button-primary">
                                <i class="fa-icon-ok" ></i>&nbsp Finalizar
                            </a4j:commandLink>
                        	
                        	<a4j:commandLink id="imprimir" rendered="#{!CancelamentoContratoControle.apresentarBotoes
                        									&& CancelamentoContratoControle.mostrarBotaoRecibo && !CancelamentoContratoControle.processandoOperacao}"
                   					           action="#{CancelamentoContratoControle.imprimirReciboDevolucao}"
                                               styleClass="pure-button"
                    				           oncomplete="abrirPopupPDFImpressao('relatorio/#{CancelamentoContratoControle.nomeArquivoRelatorioGeradoAgora}','', 780, 595);">
                                <i class="fa-icon-print" ></i>&nbsp Recibo Devolução
                            </a4j:commandLink>

                            <h:commandLink id="fechar" title="Fechar Janela" onclick="fecharJanela();executePostMessage({reloadContractPage: true});fireElementFromParent('form:btnAtualizaCliente');"
                                           rendered="#{!CancelamentoContratoControle.apresentarBotoes && !CancelamentoContratoControle.processandoOperacao}"
                                           styleClass="pure-button pure-button-primary">
                                <i class="fa-icon-remove" ></i>&nbsp Fechar
                            </h:commandLink>
                    
			</h:panelGrid>
                         <h:panelGrid rendered="#{CancelamentoContratoControle.processandoOperacao}" columns="1">
                            <h:outputText id="msgProcessando" styleClass="mensagem"  value="Operação já está sendo processada. Dentro de alguns instantes, atualize a página para verificar se operação já foi concluída"/>
                            <rich:spacer height="7"/>
                            <a4j:commandLink id="atualizar" title="Atulaizar" onclick="window.location.reload();fireElementFromParent('form:btnAtualizaCliente');"  styleClass="pure-button pure-button-primary">
                                <i class="fa-icon-refresh"></i>&nbsp;Atualizar
                            </a4j:commandLink>
                         </h:panelGrid>               
				
			</h:panelGrid>
		</h:panelGrid>
    </h:form>
    <%@include file="includes/include_modal_mensagem_generica.jsp" %>
    <%@include file="includes/autorizacao/include_autorizacao_funcionalidade.jsp"%>
    <%@include file="./includes/imports.jsp" %>
</f:view>

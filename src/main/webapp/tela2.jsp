<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<head><%@include file="/includes/include_import_minifiles.jsp" %></head>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<script type="text/javascript" language="javascript" src="hoverform.js"></script>
<script type="text/javascript" language="javascript" src="script/Notifier.js"></script>
<%@page pageEncoding="ISO-8859-1"%>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes" />
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao" />
<f:loadBundle var="msg" basename="propriedades.Mensagens" />

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <head>
        <link rel="shortcut icon" href="./favicon.ico">
        <title><h:outputText value="#{msg_aplic.prt_Cliente_tituloForm}" /></title>
    </head>
    <jsp:include page="include_head.jsp" flush="true" />
    <h:form id="form">
        <h:panelGroup layout="block" style="display: inline-table;width: 100%;" styleClass="fundoCinza">
            <h:panelGroup layout="block" styleClass="bgtop topoZW" rendered="#{MenuControle.apresentarTopo}">
                <jsp:include page="include_topo_novo.jsp" flush="true"/>
                <jsp:include page="include_menu_zw_flat.jsp" flush="true"/>
                <rich:jQuery selector=".item5" query="addClass('menuItemAtual')"/>
            </h:panelGroup>

            <h:panelGroup layout="block" styleClass="caixaCorpo paginaFontResponsiva">
                <h:panelGroup layout="block" style="height: 80%;width: 100%">
                    <h:panelGroup layout="block" styleClass="caixaMenuLatel">
                        <h:panelGroup layout="block" styleClass="container-imagem container-conteudo-central" style="position:relative;">
                            <h:panelGroup layout="block" styleClass="container-box zw_ui especial ">
                                <h:panelGroup styleClass="container-box-header" layout="block">
                                    <h:panelGroup layout="block" styleClass="margin-box">
                                        <h:outputText styleClass="container-header-titulo" value="Cadastrar cliente "/>
                                        <h:outputLink styleClass="linkWiki"
                                                      value="#{SuperControle.urlBaseConhecimento}como-cadastrar-um-cliente-visitante-aluno/"
                                                      title="Clique e saiba mais: Informações do Cliente"
                                                      target="_blank">
                                            <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                                        </h:outputLink>
                                    </h:panelGroup>
                                </h:panelGroup>
                                <h:panelGroup layout="block" styleClass="margin-box formNovo">

                                    <!-- inicio item -->
                                    <h:panelGroup id="botesSuperior" styleClass="container-botoes bg-cinza-2" layout="block">
                                            <a4j:commandLink id="salvar"
                                                             value="Confirmar"
                                                             styleClass="botaoPrimario texto-size-16 linkPadrao"
                                                             action="#{ClienteControle.novoQuestionario}"
                                                             rendered="#{!ClienteControle.cadastroDadosObrigatoriosCliente}"
                                                             oncomplete="#{ClienteControle.mensagemNotificar}"
                                                             title="#{msg.msg_gravar_dados}" accesskey="2"
                                                             reRender="panelExistePessoa">
                                            </a4j:commandLink>
                                            <a4j:commandLink id="cancelar"
                                                             value="Cancelar"
                                                             styleClass="botaoSecundario texto-size-16 linkPadrao"
                                                             action="preCadastro"
                                                             style="margin-left: 8px"
                                                             rendered="#{!ClienteControle.cadastroDadosObrigatoriosCliente}"
                                                             title="Cancelar inclusão de um Cliente">
                                            </a4j:commandLink>
                                    </h:panelGroup>
                                    <jsp:include page="include_form_novo_cliente.jsp"/>

                                    <%-- BOTÕES DE CONTROLE --%>
                                    <c:if test="${ClienteControle.cadastroDadosObrigatoriosCliente}">
                                        <jsp:include page="includes/menus/include_menubotoesfixos_tela2.jsp"
                                                     flush="true"/>
                                    </c:if>
                                    <!-- fim item -->
                                    <h:panelGroup id="botesInferior" styleClass="container-botoes " layout="block">
                                            <a4j:commandLink id="salvar1"
                                                             value="Confirmar"
                                                             styleClass="botaoPrimario texto-size-16 linkPadrao"
                                                             action="#{ClienteControle.novoQuestionario}"
                                                             rendered="#{!ClienteControle.cadastroDadosObrigatoriosCliente}"
                                                             oncomplete="#{ClienteControle.mensagemNotificar}"
                                                             title="#{msg.msg_gravar_dados}" accesskey="2"
                                                             reRender="panelExistePessoa">
                                            </a4j:commandLink>
                                            <a4j:commandLink id="cancelar1"
                                                             value="Cancelar"
                                                             styleClass="botaoSecundario texto-size-16 linkPadrao"
                                                             action="preCadastro"
                                                             style="margin-left: 8px"
                                                             rendered="#{!ClienteControle.cadastroDadosObrigatoriosCliente}"
                                                             title="Cancelar inclusão de um Cliente">
                                            </a4j:commandLink>
                                    </h:panelGroup>
                                </h:panelGroup>

                            </h:panelGroup>
                        </h:panelGroup>
                        <jsp:include page="include_box_menulateral.jsp" flush="true"/>
                    </h:panelGroup>
                </h:panelGroup>
            </h:panelGroup>

            <jsp:include page="include_rodape_flat.jsp" flush="true"/>
        </h:panelGroup>

    </h:form>
    <jsp:include  page="include_modais_tela_cadastro_cliente.jsp" flush="true" />
    
    <jsp:include  page="includes/include_modal_validacaoCpf.jsp" flush="true" />
</f:view>

<%@include file="includes/imports.jsp" %>
<h:panelGroup rendered="#{not empty MensagemBuilderControle.templates and MensagemBuilderControle.meioEmail and not MensagemBuilderControle.building  and not MensagemBuilderControle.visaoAgendados}"
              layout="block"
              style="min-height: 700px; margin-top: 20px; ">

    <h:panelGroup style="display: inline-flex; padding-bottom: 10px; border-bottom:#E5E5E5 1px solid; width: 100%; position: relative; height: 40px; margin-top: 25px"
                  layout="block">
        <h:outputText value="Templates de E-mail" style="color: #333; font-weight: bold; font-size: 14px;"/>
    </h:panelGroup>

    <h:panelGroup layout="block" styleClass="modelosEmail">


        <h:inputHidden id="codigoTemplateSelecionado" value="#{MensagemBuilderControle.codigoTemplateSelecionado}"/>
        <a4j:jsFunction reRender="mensagembuilder" name="utilizarModelo"
                        oncomplete="init(body)"
                        action="#{MensagemBuilderControle.novoComTemplate}"></a4j:jsFunction>

        <script>
            function utilizar(codigo) {
                var codigoTemplateSelecionado = document.getElementById('form:codigoTemplateSelecionado');
                codigoTemplateSelecionado.value = codigo;
                utilizarModelo();
            }
        </script>

        <c:forEach items="#{MensagemBuilderControle.templates}" var="template" varStatus="varTem">
            <div class="caixa-build add">
                <div class="thumb-html">
                        ${template.html}
                </div>


                <div class="hover-layer">

                    <a class="btn-builder" onclick="utilizar(${template.codigo})"
                       style="margin-top: 133px;">
                        <i class="fa-icon-paper-plane"></i>Utilizar
                    </a>
                </div>

                <div class="rodape-build">
                        ${template.nome}
                </div>
            </div>
        </c:forEach>
    </h:panelGroup>
</h:panelGroup>


<%@page contentType="text/html"%>
<%@page pageEncoding="ISO-8859-1"%>
<head><%@include file="/includes/include_import_minifiles.jsp" %></head>
<link href="./css/pacto_flat_2.16.css" rel="stylesheet" type="text/css">
<link href="./css/otimize.css" rel="stylesheet" type="text/css">

<link href="beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/pure-buttons.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/pure-ext.css" type="text/css" rel="stylesheet"/>
<script type="text/javascript" language="javascript" src="hoverform.js"></script>
<script type="text/javascript" language="javascript" src="script/Notifier.js"></script>
<script type="text/javascript" language="javascript">
     setTimeout(setInterval(function() { getDocumentCookie('popupsImportante') == 'close' ? this.close() : ''; }, 500), 500);
     window.addEventListener("load", function (event) {
         executePostMessage({loaded: true})
     });
</script>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes" />
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao" />
<f:loadBundle var="msg" basename="propriedades.Mensagens" />
<style type="text/css">
    .pure-button{
        font-family: sans-serif;
        font-size:100%;
        margin:0;
        vertical-align:baseline;
        *vertical-align:middle
    }

    td.w48{
        width: 48%;
    }
</style>

<f:view>
    <%@include file="includes/include_carregando_ripple.jsp" %>
    <title><h:outputText
            value="#{msg_aplic.prt_EstornoContrato_tituloForm}" /></title>

    <rich:modalPanel id="panelMensagemOutroContrato" autosized="true"
                     shadowOpacity="true" width="450"
                     styleClass="novaModal"
                     onshow="document.getElementById('formMensagemOutroContrato:fechar').focus();">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Atenção"/>
            </h:panelGroup>
        </f:facet>
        <a4j:form id="formMensagemOutroContrato">
            <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                <h:panelGroup styleClass="margin-box">
                    <h:panelGroup layout="block" styleClass="texto-size-16-real texto-font texto-cor-cinza"
                                  style="text-align: left">
                        <h:outputText
                                value="Este estorno afetará também os contratos:"/>
                    </h:panelGroup>

                    <rich:dataTable id="listaPessoa" width="100%"
                                    style="width: 100%; margin: 15px 0 0 0;"
                                    styleClass="tabelaDados semZebra"
                                    value="#{EstornoContratoControle.listaPessoaVOs}"
                                    var="contrato">
                        <rich:column>
                            <f:facet name="header">
                                <h:outputText value="Contrato"/>
                            </f:facet>
                            <h:outputText value="#{contrato.codigo}"/>
                        </rich:column>

                        <rich:column>
                            <f:facet name="header">
                                <h:outputText value="Aluno"/>
                            </f:facet>
                            <h:outputText value="#{contrato.pessoa.nome}"/>
                        </rich:column>
                    </rich:dataTable>
                </h:panelGroup>
                <h:panelGrid columns="2" width="100%">
                    <h:panelGroup layout="block" styleClass="container-botoes">
                        <a4j:commandLink value="#{msg_bt.btn_fechar}"
                                         rendered="#{EstornoContratoControle.apresentarBotaoEstorno}"
                                         action="#{EstornoContratoControle.validarPermissaoEstornarContrato}"
                                         oncomplete="Richfaces.hideModalPanel('panelMensagemOutroContrato');"
                                         reRender="form,panelMensagem,panelAutorizacaoFuncionalidade"
                                         styleClass="botaoSecundario texto-size-14-real"/>
                    </h:panelGroup>
                </h:panelGrid>
            </h:panelGrid>
        </a4j:form>
    </rich:modalPanel>

    <rich:modalPanel id="modalConfirmacaoEstornarComNota" autosized="true" shadowOpacity="true"
                     width="450" height="250"
                     styleClass="novaModal">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Confirmação do Estorno do Contrato com Nota Fiscal"/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText
                        styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                        id="hidelinkmodalConfirmacaoEstornarComNota"/>
                <rich:componentControl for="modalConfirmacaoEstornarComNota"
                                       attachTo="hidelinkmodalConfirmacaoEstornarComNota"
                                       operation="hide" event="onclick">
                </rich:componentControl>
            </h:panelGroup>
        </f:facet>
        <a4j:form id="formModalConfirmacaoEstornarComNota">
            <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                <h:panelGroup styleClass="margin-box">
                    <h:panelGroup layout="block" styleClass="texto-size-16-real texto-font texto-cor-cinza"
                                  style="text-align: left">
                        <h:outputText value="Já existe nota fiscal emitida para recibo(s) deste contrato, que não são canceladas automaticamente."/>
                        <br/>
                        <h:outputText value="Deseja estornar mesmo assim?"/>
                    </h:panelGroup>
                </h:panelGroup>
                <h:panelGrid columns="2" width="100%">
                    <h:panelGroup layout="block" styleClass="container-botoes">
                        <a4j:commandLink value="Sim"
                                         action="#{EstornoContratoControle.estornoContratoExcluindoNFSe}"
                                         oncomplete="Richfaces.hideModalPanel('modalConfirmacaoEstornarComNota');"
                                         reRender="form,msg,panelMensagem, mdlMensagemGenerica,panelAutorizacaoFuncionalidade"
                                         styleClass="botaoPrimario texto-size-14-real"/>
                        <a4j:commandLink value="Não"
                                         style="margin-left: 50px"
                                         oncomplete="Richfaces.hideModalPanel('modalConfirmacaoEstornarComNota');Richfaces.hideModalPanel('panelAutorizacaoFuncionalidade');"
                                         reRender="form,msg,panelMensagem, mdlMensagemGenerica,panelAutorizacaoFuncionalidade"
                                         styleClass="botaoSecundario texto-size-14-real"/>
                    </h:panelGroup>
                </h:panelGrid>
            </h:panelGrid>
        </a4j:form>
    </rich:modalPanel>

    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0"
                 columnClasses="colunaEsquerda">
            <c:set var="titulo" scope="session" value="${msg_aplic.prt_EstornoContrato_tituloForm}"/>
            <c:set var="urlWiki" scope="session" value="${SuperControle.urlBaseConhecimento}como-excluir-estornar-o-contrato-de-um-aluno-que-esta-errado/"/>
            <f:facet name="header">
                <jsp:include page="topoReduzido_material.jsp"/>
            </f:facet>
        <div styleClass="col-md-12"><hr class="dividerFundoClaro"/></div>
        <h:form id="form">
            <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">

                <h:panelGrid columns="1" columnClasses="colunaCentralizada" width="100%">

                    <h:panelGrid columns="3" headerClass="subordinado" columnClasses="cinza texto-size-14 w48,w4 semBorda, cinza texto-size-14 w48"  styleClass="tituloCampos col-text-align-left" width="100%">
                        <h:panelGroup>
                            <h:outputText  styleClass=" texto-size-14 texto-cor-cinza texto-font texto-bold" value="NOME ALUNO: "/>
                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font"
                                          value="#{EstornoContratoControle.clienteVO.pessoa.nome}" />
                            <div styleClass="col-md-12"><hr class="dividerFundoClaro"/></div>
                        </h:panelGroup>
                        <h:panelGroup> </h:panelGroup>
                        <h:panelGroup styleClass="col-text-align-left">
                            <h:outputText styleClass=" texto-size-14 texto-cor-cinza texto-font texto-bold"
                                          value="MATRÍCULA: " />
                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font "
                                          value="#{EstornoContratoControle.clienteVO.matricula}" />
                            <div styleClass="col-md-12"><hr class="dividerFundoClaro"/></div>
                        </h:panelGroup>

                        <h:panelGroup>
                            <h:outputText styleClass=" texto-size-14 texto-cor-cinza texto-font texto-bold"
                                          value="EMPRESA: " />
                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font"
                                          value="#{EstornoContratoControle.contratoVO.empresa.nome}" />
                            <div styleClass="col-md-12"><hr class="dividerFundoClaro"/></div>
                        </h:panelGroup>
                        <h:panelGroup> </h:panelGroup>
                        <h:panelGroup styleClass="col-text-align-left">
                            <h:outputText styleClass=" texto-size-14 texto-cor-cinza texto-font texto-bold"
                                          value="PLANO: " />
                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font "
                                          value="#{EstornoContratoControle.contratoVO.plano.descricao}" />
                            <div styleClass="col-md-12"><hr class="dividerFundoClaro"/></div>
                        </h:panelGroup>
                        <h:panelGroup>
                            <h:outputText styleClass=" texto-size-14 texto-cor-cinza texto-font texto-bold"
                                          value="DATA INÍCIO: " />
                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font"
                                          value="#{EstornoContratoControle.contratoVO.vigenciaDe_Apresentar}" />
                            <div styleClass="col-md-12"><hr class="dividerFundoClaro"/></div>
                        </h:panelGroup>
                        <h:panelGroup> </h:panelGroup>
                        <h:panelGroup styleClass="col-text-align-left">
                            <h:outputText styleClass=" texto-size-14 texto-cor-cinza texto-font texto-bold"
                                          value="DATA TÉRMINO ORIGINAL: " />
                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font"
                                          value="#{EstornoContratoControle.contratoVO.vigenciaAte_Apresentar}" />
                            <div styleClass="col-md-12"><hr class="dividerFundoClaro"/></div>
                        </h:panelGroup>

                        <h:panelGroup>
                            <h:outputText styleClass=" texto-size-14 texto-cor-cinza texto-font texto-bold"
                                          value="DURAÇÃO: " />
                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font"
                                          value="#{EstornoContratoControle.contratoVO.contratoDuracao.numeroMeses} Mês" />
                            <div styleClass="col-md-12"><hr class="dividerFundoClaro"/></div>
                        </h:panelGroup>
                        <h:panelGroup> </h:panelGroup>
                        <h:panelGroup>
                            <h:outputText styleClass=" texto-size-14 texto-cor-cinza texto-font texto-bold"
                                          style="font-weight: bold" value="VALOR CONTRATO: " />
                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="R$ " />
                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font"
                                          value="#{EstornoContratoControle.contratoVO.valorFinal}">
                                <f:converter converterId="FormatadorNumerico" />
                            </h:outputText>
                            <div styleClass="col-md-12"><hr class="dividerFundoClaro"/></div>
                        </h:panelGroup>
                        <h:panelGroup styleClass="col-text-align-left">
                            <h:outputText styleClass=" texto-size-14 texto-cor-cinza texto-font texto-bold" value="RESP. CONTRATO: " />
                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{EstornoContratoControle.contratoVO.responsavelContrato.nome}" />
                            <div styleClass="col-md-12"><hr class="dividerFundoClaro"/></div>
                        </h:panelGroup>

                        <h:panelGroup> </h:panelGroup>
                        <h:panelGroup rendered="#{EstornoContratoControle.exiteOutroContratoPagouMinhaParcela}" >
                            <h:outputText id="msgOutroContrato" styleClass="mensagemDetalhada"
                                          value="#{msg.msg_EstornoContrato_ExisteOutroContrato}" />
                            <div styleClass="col-md-12"><hr class="dividerFundoClaro"/></div>
                        </h:panelGroup>

                        <h:panelGroup rendered="#{EstornoContratoControle.existeTransacoesJaProcessadas}">
                            <h:outputText styleClass="mensagemDetalhada"
                                          value="#{msg.msg_Estorno_ExisteTransacoesJaProcessadas}" />
                        </h:panelGroup>
                        <h:panelGroup> </h:panelGroup>
                        <h:panelGroup rendered="#{EstornoContratoControle.existeTransacoesQuePodemSerCanceladas}">
                            <h:outputText styleClass="mensagemDetalhada"
                                          value="#{msg.msg_Estorno_ExisteTransacoesQuePodemSerCanceladas}" />
                        </h:panelGroup>
                    </h:panelGrid>

                    <h:panelGrid columns="1" width="100%" style="margin-top: 20px;">
                        <h:outputText  styleClass="texto-size-14-real texto-cor-cinza-2 texto-font texto-bold col-text-align-left"
                                       value="MODALIDADES INCLUSAS" />
                        <rich:dataTable id="modalidade" border="0" style="margin: 0; width: 100%;"
                                        cellspacing="0" cellpadding="0" styleClass="tabelaDados semZebra"
                                        value="#{EstornoContratoControle.contratoVO.contratoModalidadeVOs}"
                                        var="contratoModalidade">
                            <rich:column>
                                <f:facet name="header">
                                    <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold"  value="MODALIDADE" />
                                </f:facet>
                                <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font"
                                              value="#{contratoModalidade.modalidade.nome}" />
                            </rich:column>
                            <rich:column>
                                <f:facet name="header">
                                    <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold"  value="VEZES POR SEMANA" />
                                </f:facet>
                                <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font"
                                              value="#{contratoModalidade.nrVezesSemana}" />
                            </rich:column>
                        </rich:dataTable>
                    </h:panelGrid>

                    <h:panelGrid columns="1" width="100%" style="margin-top: 20px;"
                                 rendered="#{!EstornoContratoControle.apresentarListaPagamento}"
                                 columnClasses="colunaCentralizada">
                        <h:outputText  styleClass="texto-size-14-real texto-cor-cinza-2 texto-font texto-bold col-text-align-left"
                                      value="PARCELAS DO CONTRATO" />

                        <rich:dataTable id="movParcelaDiretaContrato" style="margin: 0; width: 100%;"
                                        cellspacing="0" cellpadding="0" styleClass="tabelaDados semZebra"
                                        value="#{EstornoContratoControle.contratoVO.movParcelaVOs}"
                                        var="historicoParcela">
                            <rich:column>
                                <f:facet name="header">
                                    <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold"
                                                  value="#{msg_aplic.prt_HistoricoParcelaCliente_codigo}" />
                                </f:facet>
                                <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font"
                                              value="#{historicoParcela.contrato.codigo}" />
                            </rich:column>
                            <rich:column>
                                <f:facet name="header">
                                    <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold"
                                                  value="#{msg_aplic.prt_HistoricoParcelaCliente_nomeAluno}" />
                                </f:facet>
                                <h:panelGroup
                                    rendered="#{EstornoContratoControle.desenharColunaNomeContrato}">
                                    <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font"
                                                  value="#{historicoParcela.contrato.pessoa.nome}" />
                                </h:panelGroup>

                                <h:panelGroup
                                    rendered="#{EstornoContratoControle.desenharColunaNomeVendaAvulsa}">
                                    <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font"
                                                  value="#{historicoParcela.vendaAvulsaVO.nomeComprador}" />
                                </h:panelGroup>

                                <h:panelGroup
                                    rendered="#{EstornoContratoControle.desenharColunaNomeAulaAvusa}">
                                    <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font"
                                                  value="#{historicoParcela.aulaAvulsaDiariaVO.nomeComprador}" />
                                </h:panelGroup>
                            </rich:column>


                            <rich:column>
                                <f:facet name="header">
                                    <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold"
                                                  value="#{msg_aplic.prt_HistoricoParcelaCliente_descricao}" />
                                </f:facet>
                                <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font"
                                              value="#{historicoParcela.descricao}" />
                            </rich:column>

                            <rich:column>
                                <f:facet name="header">
                                    <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold"
                                                  value="#{msg_aplic.prt_HistoricoParcelaCliente_DataVencimento}" />
                                </f:facet>
                                <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font"
                                              value="#{historicoParcela.dataVencimento_Apresentar}">
                                </h:outputText>
                            </rich:column>
                            <rich:column>
                                <f:facet name="header">
                                    <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold"
                                                  value="#{msg_aplic.prt_HistoricoParcelaCliente_valor}" />
                                </f:facet>
                                <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font"
                                              value="#{historicoParcela.valorParcela}">
                                    <f:converter converterId="FormatadorNumerico" />
                                </h:outputText>
                            </rich:column>
                            <rich:column>
                                <f:facet name="header">
                                    <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold"
                                                  value="#{msg_aplic.prt_HistoricoParcelaCliente_situacao}" />
                                </f:facet>
                                <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font"
                                              value="#{historicoParcela.situacao_Apresentar}" />
                            </rich:column>
                        </rich:dataTable>
                    </h:panelGrid>

                    <h:panelGrid columns="1" width="100%" style="margin-top: 20px;"
                                 rendered="#{EstornoContratoControle.apresentarListaPagamento}"
                                 styleClass="tabFormSubordinada" columnClasses="colunaCentralizada">
                        <h:outputText  styleClass="texto-size-14-real texto-cor-cinza-2 texto-font texto-bold col-text-align-left"
                                      value="PARCELAS PAGAS PELO RECIBO #{estornoRecibo.reciboPagamentoVO.codigo}" />
                        <h:dataTable id="movParcela" width="100%"

                                        cellspacing="0" cellpadding="0"
                                        value="#{EstornoContratoControle.contratoVO.listaEstornoRecibo}"
                                        var="estornoRecibo">
                            <h:column>
                                <rich:dataTable id="tabelaDeParcelasAuto" style="margin: 0; width: 100%;" border="0" cellspacing="0"
                                                cellpadding="0"  styleClass="tabelaDados semZebra"
                                                value="#{estornoRecibo.listaMovParcela}" var="historicoParcela">
                                    <rich:column>
                                        <f:facet name="header">
                                            <h:outputText style="font-weight: bold"
                                                          value="#{msg_aplic.prt_HistoricoParcelaCliente_codigo}" />
                                        </f:facet>
                                        <h:outputText  styleClass="texto-size-14-real texto-cor-cinza texto-font"
                                                      value="#{historicoParcela.contrato.codigo}" />
                                    </rich:column>
                                    <rich:column>
                                        <f:facet name="header">
                                            <h:outputText style="font-weight: bold"
                                                          value="#{msg_aplic.prt_HistoricoParcelaCliente_nomeAluno}" />
                                        </f:facet>
                                        <h:panelGroup
                                            rendered="#{EstornoContratoControle.desenharColunaNomeContrato}">
                                            <h:outputText  styleClass="texto-size-14-real texto-cor-cinza texto-font"
                                                          value="#{historicoParcela.contrato.pessoa.nome}" />
                                        </h:panelGroup>

                                        <h:panelGroup
                                            rendered="#{EstornoContratoControle.desenharColunaNomeVendaAvulsa}">
                                            <h:outputText style="font-weight: bold" styleClass="cinza"
                                                          value="#{historicoParcela.vendaAvulsaVO.nomeComprador}" />
                                        </h:panelGroup>

                                        <h:panelGroup
                                            rendered="#{EstornoContratoControle.desenharColunaNomeAulaAvusa}">
                                            <h:outputText style="font-weight: bold" styleClass="cinza"
                                                          value="#{historicoParcela.aulaAvulsaDiariaVO.nomeComprador}" />
                                        </h:panelGroup>
                                    </rich:column>


                                    <rich:column>
                                        <f:facet name="header">
                                            <h:outputText style="font-weight: bold"
                                                          value="#{msg_aplic.prt_HistoricoParcelaCliente_descricao}" />
                                        </f:facet>
                                        <h:outputText  styleClass="texto-size-14-real texto-cor-cinza texto-font"
                                                      value="#{historicoParcela.descricao}" />
                                    </rich:column>

                                    <rich:column>
                                        <f:facet name="header">
                                            <h:outputText style="font-weight: bold"
                                                          value="#{msg_aplic.prt_HistoricoParcelaCliente_DataVencimento}" />
                                        </f:facet>
                                        <h:outputText  styleClass="texto-size-14-real texto-cor-cinza texto-font"
                                                      value="#{historicoParcela.dataVencimento_Apresentar}">

                                        </h:outputText>
                                    </rich:column>
                                    <rich:column>
                                        <f:facet name="header">
                                            <h:outputText style="font-weight: bold"
                                                          value="#{msg_aplic.prt_HistoricoParcelaCliente_valor}" />
                                        </f:facet>
                                        <h:outputText  styleClass="texto-size-14-real texto-cor-cinza texto-font"
                                                      value="#{historicoParcela.valorParcela}">
                                            <f:converter converterId="FormatadorNumerico" />
                                        </h:outputText>
                                    </rich:column>
                                    <rich:column>
                                        <f:facet name="header">
                                            <h:outputText style="font-weight: bold"
                                                          value="#{msg_aplic.prt_HistoricoParcelaCliente_situacao}" />
                                        </f:facet>
                                        <h:outputText  styleClass="texto-size-14-real texto-cor-cinza texto-font"
                                                      value="#{historicoParcela.situacao_Apresentar}" />
                                    </rich:column>
                                </rich:dataTable>
                            </h:column>
                        </h:dataTable>
                    </h:panelGrid>

                    <h:panelGrid columns="1" columnClasses="colunaCentralizada"
                                 width="100%" style="margin-top: 20px;"
                                 rendered="#{EstornoContratoControle.apresentarListaPagamento}">
                        <h:outputText  styleClass="texto-size-14-real texto-cor-cinza-2 texto-font texto-bold col-text-align-left"
                                      value="PAGAMENTOS DO RECIBO #{estornoRecibo.reciboPagamentoVO.codigo}" />
                        <h:dataTable id="movPagamento" width="100%" styleClass="col-text-align-left"
                                        style="border:0;background-color: transparent;"
                                        cellspacing="0" cellpadding="0" columnClasses="centralizado"
                                        value="#{EstornoContratoControle.contratoVO.listaEstornoRecibo}"
                                        var="estornoRecibo">
                            <h:column>
                                <rich:dataTable id="tabelaPagamentosDoReciboAuto" border="0" cellspacing="0"
                                                cellpadding="0"  styleClass="tabelaDados semZebra"
                                                style="margin: 0; width: 100%;"
                                                value="#{estornoRecibo.listaMovPagamento}"
                                                var="historicoPagamentos">
                                    <rich:column>
                                        <f:facet name="header">
                                            <h:outputText style="font-weight: bold"
                                                          value="#{msg_aplic.prt_HistoricoComprasCliente_nomePagador}" />
                                        </f:facet>
                                        <h:outputText  styleClass="texto-size-14-real texto-cor-cinza texto-font"
                                                      value="#{historicoPagamentos.nomePagador}" />
                                    </rich:column>
                                    <rich:column>
                                        <f:facet name="header">
                                            <h:outputText style="font-weight: bold"
                                                          value="#{msg_aplic.prt_HistoricoComprasCliente_formaPagamento}" />
                                        </f:facet>
                                        <h:outputText  styleClass="texto-size-14-real texto-cor-cinza texto-font"
                                                      value="#{historicoPagamentos.formaPagamento.tipoFormaPagamento_Apresentar}" />
                                    </rich:column>
                                    <rich:column>
                                        <f:facet name="header">
                                            <h:outputText style="font-weight: bold"
                                                          value="#{msg_aplic.prt_HistoricoComprasCliente_valor}" />
                                        </f:facet>
                                        <h:outputText  styleClass="texto-size-14-real texto-cor-cinza texto-font"
                                                      value="#{historicoPagamentos.valorTotal}">
                                            <f:converter converterId="FormatadorNumerico" />
                                        </h:outputText>
                                    </rich:column>
                                    <rich:column>
                                        <f:facet name="header">
                                            <h:outputText style="font-weight: bold"
                                                          value="#{msg_aplic.prt_HistoricoComprasCliente_dataLancamento}" />
                                        </f:facet>
                                        <h:outputText  styleClass="texto-size-14-real texto-cor-cinza texto-font"
                                                      value="#{historicoPagamentos.dataLancamento}">
                                            <f:convertDateTime pattern="dd/MM/yyyy" />
                                        </h:outputText>
                                    </rich:column>
                                </rich:dataTable>
                            </h:column>
                        </h:dataTable>
                    </h:panelGrid>

                    <h:panelGrid columns="1" width="100%"
                                 rendered="#{!empty EstornoContratoControle.listaTransacoes}"
                                 styleClass="tabFormSubordinada,tituloCamposNegritoMaior" columnClasses="colunaCentralizada">

                        <%@include file="includes/transacoes/include_table_transacoes.jsp" %>
                    </h:panelGrid>

                    <h:panelGrid columns="1" width="100%" id="panelSelectEstornarTransacoes"
                                  rendered="#{!empty EstornoContratoControle.listaTransacoes }">
                        <h:panelGroup layout="block" style="padding: 10px 0;display: flex;">
                            <h:outputText style="padding-top: 15px; padding-right: 5px;"
                                    styleClass="texto-size-14-real texto-cor-cinza-2 texto-font texto-bold"
                                    value="CANCELAR TRANSAÇÕES"/>
                            <h:panelGroup layout="block" styleClass="cb-container pl20">
                                <h:selectOneMenu id="estornarTransacoes" styleClass="form" onblur="blurinput(this);"
                                                 onfocus="focusinput(this);"
                                                 value="#{EstornoContratoControle.estornarTransacoes}">

                                        <f:selectItems value="#{EstornoContratoControle.listaSelectItemEstornarTransacoes}" />

                                </h:selectOneMenu>
                            </h:panelGroup>
                        </h:panelGroup>
                    </h:panelGrid>

                    <h:panelGrid columns="1" width="100%"
                                 rendered="#{!empty GestaoRemessasControle.itensRemessaEstorno}"
                                 styleClass="tabFormSubordinada,tituloCamposNegritoMaior" columnClasses="colunaCentralizada">

                        <%@include file="includes/remessas/include_itens_remessaestorno.jsp" %>
                    </h:panelGrid>

                    <h:panelGrid columns="1">
                        <h:outputText  styleClass="texto-size-14-real texto-cor-cinza-2 texto-font texto-bold col-text-align-left"
                        value="MOTIVO DO ESTORNO" />
                        <h:inputTextarea styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{EstornoContratoControle.justificativaEstorno}"  cols="110" rows="3"/>
                    </h:panelGrid>

                    <h:panelGrid id="panelMensagem" columns="3" width="100%" columnClasses="colunaEsquerda" styleClass="tabMensagens">
                        <h:panelGrid columns="1" width="100%">
                            <h:outputText value=" " />
                        </h:panelGrid>
                        <h:commandButton rendered="#{EstornoContratoControle.sucesso}" image="./imagens/sucesso.png" />
                        <h:commandButton rendered="#{EstornoContratoControle.erro}" image="./imagens/erro.png" />
                        <h:panelGrid columns="1" width="100%">
                            <h:outputText id="msgEstorno" styleClass="mensagem"
                                          value="#{EstornoContratoControle.mensagem}"/>
                            <h:outputText id="msgEstornoDet" styleClass="mensagemDetalhada"
                                          value="#{EstornoContratoControle.mensagemDetalhada}"/>
                        </h:panelGrid>
                    
                        <rich:spacer rendered="#{EstornoContratoControle.temChequeComLote}" width="20px"/>
                        <rich:spacer rendered="#{EstornoContratoControle.temChequeComLote}" width="20px"/>
                        <h:panelGroup>
                            <h:outputText styleClass="mensagemDetalhada"
                                          rendered="#{EstornoContratoControle.temChequeComLote}"
                                          value="#{msg.msg_contrato_nao_pode_estornar}"/>
                            
                            
                            <h:outputLink styleClass="linkWiki"
                                  value="#{SuperControle.urlBaseConhecimento}como-excluir-estornar-o-contrato-de-um-aluno-que-esta-errado/"
                                  rendered="#{EstornoContratoControle.temChequeComLote}" 
                                  title="Clique e saiba mais: RecebivelLote"
                                  target="_blank" >
                        			<h:outputText styleClass="mensagemDetalhada" value=" Clique Aqui."/>
                    		</h:outputLink>
                    
                        </h:panelGroup>    
                        </h:panelGrid>
                    <h:panelGroup>
                        <h:commandLink id="fechar" title="Fechar Janela"
                                         onclick="fecharJanela();fecharPostMessage();" styleClass="pure-button">
                            <i class="fa-icon-remove" ></i>&nbsp;Fechar
                        </h:commandLink>
                        <rich:spacer width="20px"/>
                        <a4j:commandLink id="estornar" action="#{EstornoContratoControle.validarCaixaAbrirModal}"
                                           rendered="#{EstornoContratoControle.apresentarBotaoEstorno && !EstornoContratoControle.temChequeComLote}"
                                           oncomplete="#{EstornoContratoControle.msgAlert};#{EstornoContratoControle.mensagemNotificar}"
                                           styleClass="pure-button pure-button-primary" reRender="modalAbrirCaixa,panelAutorizacaoFuncionalidade" >
                            <i class="fa-icon-money" ></i>&nbsp;Estornar
                        </a4j:commandLink>
                    </h:panelGroup>

                </h:panelGrid>
            </h:panelGrid>
        </h:form>

    </h:panelGrid>

    <%@include file="includes/include_modal_mensagem_generica.jsp" %>
    <%@include file="includes/transacoes/include_paramspanel_transacao.jsp" %>
    <%@include file="include_modal_trocarCartaoRecorrencia.jsp" %>
    <%@include file="/pages/finan/includes/include_modal_abrirCaixa.jsp" %>
    <%@include file="includes/autorizacao/include_autorizacao_funcionalidade.jsp" %>

</f:view>

<script>
    document.getElementById("form:valorConsulta").focus();

    function fecharPostMessage() {
        executePostMessage({close: true});
    }
</script>

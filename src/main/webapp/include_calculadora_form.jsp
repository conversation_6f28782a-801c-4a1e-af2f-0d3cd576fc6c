<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>

<%@include file="./includes/imports.jsp" %>
<link href="./css/css_bi.css" rel="stylesheet" type="text/css"/>
<script src="${root}/script/all_script_3.4.min.js" type="text/javascript" ></script>
<style>
    .rich-sort-icon {
        visibility:hidden;
    }
</style>
<h:panelGroup layout="block" styleClass="container-box zw_ui especial " id="painelGeral">
    <h:panelGroup styleClass="container-box-header" layout="block">
        <h:panelGroup layout="block" styleClass="margin-box">
            <h:outputText value="Descubra seu Fator ZW" styleClass="container-header-titulo"/>
            <h:outputLink styleClass="linkWiki"
                          value="#{SuperControle.urlWiki}Inicial:Descubra_seu_Fator_ZW"
                          title="Clique e saiba mais: Descubra seu Fator ZW" target="_blank">
                <i class="fa-icon-question-sign" style="font-size: 18px"></i>
            </h:outputLink>
        </h:panelGroup>
    </h:panelGroup>
    <h:panelGroup layout="block" style="margin-right: 170px; margin-left: 170px;"
                  rendered="#{CalculadoraControle.informandoTaxas}">
        <h:panelGroup layout="block" styleClass="margin-box" style="margin-top: 20px; text-align: center;">
            <h:panelGroup layout="block" styleClass="margin-box">
                <h:outputText
                        styleClass="texto-cor-cinza texto-size-40 texto-bold"
                        value="Calcule seu Fator ZW"/>
            </h:panelGroup>
            <h:panelGroup layout="block" styleClass="margin-box" style="margin-top: -20px;">
                <h:outputText
                        styleClass="texto-cor-cinza texto-size-16"
                        value="Informe as taxas para opera��o de cr�dito/d�bito em sua academia atualmente."/>
            </h:panelGroup>
            <h:panelGroup layout="block">
                <h:outputText value="Escolha um per�odo: " styleClass="texto-cor-cinza texto-size-20"/>
            </h:panelGroup>
            <h:panelGroup id="filtroPeriodos" layout="block" styleClass="cb-container">
                <h:selectOneMenu id="filtroDatasss"
                                 styleClass="form"
                                 value="#{CalculadoraControle.filtroSelecionado}">
                    <f:selectItems value="#{CalculadoraControle.listaSelectItemPeriodo}"/>
                    <a4j:support event="onchange" action="#{CalculadoraControle.carregarFaturamento}"
                                 reRender="painelGeral"/>
                </h:selectOneMenu>
            </h:panelGroup>
        </h:panelGroup>

        <rich:dataTable id="tableTaxas" width="100%" styleClass="tabelaSimplesCustom" style="margin-left: -25px;"
                        value="#{CalculadoraControle.detalhesFaturamento}" var="itemFaturamento" >
            <rich:column sortBy="#{itemFaturamento.ordem}" sortOrder="ASCENDING" sortIcon="null" selfSorted="false">
                <f:facet name="header">
                    <h:outputText value="Forma de Pagamento" style="float: left;"
                                  styleClass="texto-font texto-cor-cinza texto-font texto-bold texto-size-20"/>
                </f:facet>
                <h:outputText style="display: inline-block"
                              styleClass="texto-font texto-font texto-size-16 texto-cor-cinza textoImcompleto tooltipster"
                              title="#{itemFaturamento.formaPagamento}"
                              value="#{itemFaturamento.formaPagamento}"/>
            </rich:column>

            <rich:column style="text-align: right;width: 30px">
                <f:facet name="header">
                    <h:outputText value="Taxa (%)"
                                  styleClass="texto-font texto-cor-cinza texto-font texto-bold texto-size-20"/>
                </f:facet>

                <h:outputText rendered="#{itemFaturamento.possuiTaxa}"
                              style="display: inline-block; text-align: right;padding-right: 10px;color:#777!important;"
                              styleClass="texto-font texto-font texto-size-16 texto-cor-cinza textoImcompleto tooltipster"
                              title="#{itemFaturamento.taxaPorcentagem}"
                              value="#{itemFaturamento.taxaPorcentagem}">
                    <f:converter converterId="FormatadorNumerico"/>
                </h:outputText>

                <h:inputText rendered="#{!itemFaturamento.possuiTaxa}"
                             style="display: inline-block; text-align: right;padding-right: 10px;color:#777!important;"
                             styleClass="inputTextClean"
                             onkeypress="if((event.which <= 47 || event.which >= 58) && event.which != 44) return false;"
                             title="#{itemFaturamento.taxaPorcentagem}"
                             value="#{itemFaturamento.taxaPorcentagem}">
                    <f:converter converterId="FormatadorNumerico"/>
                </h:inputText>
            </rich:column>
        </rich:dataTable>

        <h:panelGroup layout="block" styleClass="margin-box" style="text-align: center;">
            <a4j:commandLink styleClass="botaoPrimario texto-cor-branco texto-size-16"
                             action="#{CalculadoraControle.proximoPasso}"
                             title="Verificar faturamento"
                             value="Pr�ximo"
                             oncomplete="#{CalculadoraControle.mensagemNotificar}"
                             reRender="painelGeral">
                <a4j:support ajaxSingle="false" event="onclick"/>
            </a4j:commandLink>
        </h:panelGroup>
    </h:panelGroup>

    <h:panelGroup layout="block" style="margin-right: 170px; margin-left: 170px;"
                  rendered="#{CalculadoraControle.informandoMovimentacao}">
        <h:panelGroup layout="block" styleClass="margin-box" style="margin-top: 20px; text-align: center;">
            <h:panelGroup layout="block" styleClass="margin-box">
                <h:outputText
                        styleClass="texto-cor-cinza texto-size-40 texto-bold"
                        value="Movimenta��o Financeira"/>
            </h:panelGroup>
            <h:panelGroup layout="block" styleClass="margin-box" style="margin-top: -25px;">
                <h:outputText styleClass="texto-cor-cinza texto-size-20"
                              value="Per�odo: #{CalculadoraControle.descricaoFiltro}"/>
            </h:panelGroup>
        </h:panelGroup>

        <rich:dataTable id="tableMovimentacao" width="100%" styleClass="tabelaSimplesCustom"  style="margin-top: -30px; margin-left: -25px;"
                        value="#{CalculadoraControle.detalhesFaturamento}" var="itemFaturamento">
            <rich:column sortBy="#{itemFaturamento.ordem}" sortOrder="ASCENDING" sortIcon="null" selfSorted="false">
                <f:facet name="header">
                    <h:outputText value="Forma de Pagamento" style="float: left;"
                                  styleClass="texto-font texto-cor-cinza texto-font texto-bold texto-size-20"/>
                </f:facet>
                <h:outputText style="display: inline-block"
                              styleClass="texto-font texto-font texto-size-16 texto-cor-cinza textoImcompleto tooltipster"
                              title="#{itemFaturamento.formaPagamento}"
                              value="#{itemFaturamento.formaPagamento}"/>
            </rich:column>

            <rich:column style="text-align: right;">
                <f:facet name="header">
                    <h:outputText value="Custo Atual" style="float: right;"
                                  styleClass="texto-font texto-cor-cinza texto-font texto-bold texto-size-20"/>
                </f:facet>
                <h:outputText style="display: inline-block"
                              styleClass="texto-font texto-font texto-size-16 texto-cor-cinza textoImcompleto tooltipster"
                              title="#{itemFaturamento.taxaPorcentagem}%"
                              value="#{itemFaturamento.taxaPorcentagem}">
                    <f:converter converterId="FormatadorNumerico"/>
                </h:outputText>
                <h:outputText style="display: inline-block"
                              styleClass="texto-font texto-font texto-size-16 texto-cor-cinza textoImcompleto tooltipster"
                              title="#{itemFaturamento.taxaPorcentagem}%"
                              value=" %"/>
            </rich:column>

            <rich:column style="text-align: right;">
                <f:facet name="header">
                    <h:outputText value="Faturamento"
                                  styleClass="texto-font texto-cor-cinza texto-font texto-bold texto-size-20"/>
                </f:facet>
                <h:outputText style="display: inline-block"
                              styleClass="texto-font texto-font texto-size-16 texto-cor-cinza textoImcompleto tooltipster"
                              title="#{itemFaturamento.valor}%"
                              value="R$ "/>

                <h:inputText rendered="#{!itemFaturamento.valorInformado}"
                             style="display: inline-block; text-align: right;padding-right: 10px;color:#777!important;"
                             styleClass="inputTextClean"
                             onkeypress="if((event.which <= 47 || event.which >= 58) && event.which != 44) return false;"
                             title="#{itemFaturamento.valor}"
                             value="#{itemFaturamento.valor}">
                    <f:converter converterId="FormatadorNumerico"/>
                </h:inputText>

                <h:outputText rendered="#{itemFaturamento.valorInformado}"
                              style="display: inline-block; text-align: right;padding-right: 10px;color:#777!important;"
                              styleClass="texto-font texto-font texto-size-16 texto-cor-cinza textoImcompleto tooltipster"
                              title="#{itemFaturamento.valor}"
                              value="#{itemFaturamento.valor}">
                    <f:converter converterId="FormatadorNumerico"/>
                </h:outputText>
            </rich:column>
        </rich:dataTable>

        <h:panelGroup layout="block" styleClass="margin-box" style="text-align: center;">
            <a4j:commandLink styleClass="botaoPrimario texto-cor-branco texto-size-16"
                             style="margin-right: 10px"
                             action="#{CalculadoraControle.passoAnterior}"
                             value="Anterior"
                             title="Visualizar taxas"
                             reRender="painelGeral"/>
            <a4j:commandLink styleClass="botaoPrimario texto-cor-branco texto-size-16"
                             action="#{CalculadoraControle.proximoPasso}"
                             oncomplete="#{CalculadoraControle.mensagemNotificar}"
                             title="Exibir Resultado da Economia"
                             value="Pr�ximo"
                             reRender="painelGeral"/>
        </h:panelGroup>
    </h:panelGroup>

    <h:panelGroup layout="block" style="margin-right: 80px; margin-left: 100px;"
                  rendered="#{CalculadoraControle.exibindoResultado}">
        <h:panelGroup layout="block" styleClass="margin-box" style="margin-top: 10px; text-align: center;">
            <h:panelGroup layout="block" styleClass="margin-box">
                <h:outputText styleClass="texto-cor-cinza texto-size-25 texto-bold"
                              value="Veja como o Fator ZW influencia no resultado da sua academia "/>
            </h:panelGroup>
            <h:panelGroup layout="block" styleClass="margin-box" style="margin-top: -25px;">
                <h:outputText styleClass="texto-cor-cinza texto-size-20"
                              value="Per�odo: #{CalculadoraControle.descricaoFiltro}"/>
            </h:panelGroup>
        </h:panelGroup>
        <rich:dataTable id="tableResultado" width="100%" styleClass="tabelaSimplesCustom"  style="margin-top: -30px; margin-left: -25px;"
                        value="#{CalculadoraControle.detalhesFaturamento}" var="itemFaturamento">
            <rich:column style="width: auto;"  sortBy="#{itemFaturamento.ordem}" sortOrder="ASCENDING" sortIcon="null" selfSorted="false">
                <f:facet name="header">
                    <h:outputText value="Forma de Pagamento" style="float: left;"
                                  styleClass="texto-font texto-cor-cinza texto-font texto-bold texto-size-20"/>
                </f:facet>
                <h:outputText style="display: inline-block"
                              styleClass="texto-font texto-font texto-size-16 texto-cor-cinza textoImcompleto tooltipster"
                              title="#{itemFaturamento.formaPagamento}"
                              value="#{itemFaturamento.formaPagamento}"/>
            </rich:column>

            <rich:column style="text-align: right;">
                <f:facet name="header">
                    <h:outputText value="Faturamento" style="float: right;"
                                  styleClass="texto-font texto-cor-cinza texto-font texto-bold texto-size-20"/>
                </f:facet>
                <h:outputText style="display: inline-block; margin-right: 5px"
                              styleClass="texto-font texto-font texto-size-12 texto-cor-cinza textoImcompleto tooltipster"
                              value="R$"/>
                <h:outputText style="display: inline-block"
                              styleClass="texto-font texto-font texto-size-16 texto-cor-cinza textoImcompleto tooltipster"
                              title="#{itemFaturamento.valor}"
                              value="#{itemFaturamento.valor}">
                    <f:converter converterId="FormatadorNumerico"/>
                </h:outputText>
            </rich:column>

            <rich:column style="text-align: right;width: 200px;">
                <f:facet name="header">
                    <h:panelGroup layout="block" styleClass="margin-box">
                        <h:outputText value="Custo Atual" style="float: right;"
                                      styleClass="texto-font texto-cor-cinza texto-bold texto-size-20"/><br>
                        <h:outputText value="Valor pago � operadora" style="float: right;"
                                      styleClass="texto-font texto-cor-cinza texto-size-11"/><br>
                        <h:outputText value="com sua taxa atual" style="float: right;"
                                      styleClass="texto-font texto-cor-cinza texto-size-11"/>
                    </h:panelGroup>
                </f:facet>
                <h:outputText style="display: inline-block; margin-right: 5px"
                              styleClass="texto-font texto-size-12 texto-cor-cinza textoImcompleto tooltipster"
                              title="R$ #{itemFaturamento.taxaReais}"
                              value="R$ "/>
                <h:outputText style="display: inline-block; margin-right: 5px"
                              styleClass="texto-font texto-size-16 texto-cor-cinza textoImcompleto tooltipster"
                              title="R$ #{itemFaturamento.taxaReais}"
                              value="#{itemFaturamento.taxaReais}">
                    <f:converter converterId="FormatadorNumerico"/>
                </h:outputText>
                <h:outputText style="display: inline-block"
                              styleClass="texto-font texto-size-14 texto-cor-cinza textoImcompleto tooltipster"
                              title="#{itemFaturamento.taxaPorcentagem}%"
                              value="#{itemFaturamento.taxaPorcentagem}">
                    <f:converter converterId="FormatadorNumerico"/>
                </h:outputText>
                <h:outputText style="display: inline-block"
                              styleClass="texto-font texto-size-14 texto-cor-cinza textoImcompleto tooltipster"
                              title="#{itemFaturamento.taxaPorcentagem}%"
                              value="%"/>
            </rich:column>

            <rich:column style="text-align: right;">
                <f:facet name="header">
                    <h:outputText value="Taxa PACTO" style="float: right;"
                                  styleClass="texto-font texto-cor-cinza texto-font texto-bold texto-size-20"/>
                </f:facet>
                <h:outputText style="display: inline-block; margin-right: 5px"
                              styleClass="texto-font texto-font texto-size-12 texto-cor-cinza textoImcompleto tooltipster"
                              value="R$ "/>
                <h:outputText style="display: inline-block; margin-right: 5px"
                              styleClass="texto-font texto-font texto-size-16 texto-cor-cinza textoImcompleto tooltipster"
                              value="#{itemFaturamento.taxaReaisSTONE}">
                    <f:converter converterId="FormatadorNumerico"/>
                </h:outputText>
                <h:outputText style="display: inline-block"
                              styleClass="texto-font texto-font texto-size-14 texto-cor-cinza textoImcompleto tooltipster"
                              value="#{itemFaturamento.taxaSTONE} ">
                    <f:converter converterId="FormatadorNumerico"/>
                </h:outputText>
                <h:outputText style="display: inline-block"
                              styleClass="texto-font texto-font texto-size-14 texto-cor-cinza textoImcompleto tooltipster"
                              value="%"/>
            </rich:column>
            <rich:column style="text-align: right;width: 200px;">
                <f:facet name="header">
                    <h:panelGroup layout="block" styleClass="margin-box">
                            <h:outputText value="Fator ZW" style="float: right;"
                                          styleClass="texto-font texto-cor-verde texto-bold texto-size-20"/><br>
                        <h:outputText value="Valor que o Fator ZW" style="float: right;"
                                      styleClass="texto-font texto-cor-cinza texto-size-11"/><br>
                        <h:outputText value="economiza para sua academia" style="float: right;"
                                      styleClass="texto-font texto-cor-cinza texto-size-11"/>
                    </h:panelGroup>
                </f:facet>
                <h:outputText style="display: inline-block"
                              styleClass="texto-font texto-font texto-size-16 texto-cor-cinza textoImcompleto tooltipster"
                              value="R$"/>
                <h:outputText style="display: inline-block"
                              styleClass="texto-font texto-font texto-size-16 texto-bold texto-cor-cinza textoImcompleto tooltipster"
                              value="#{itemFaturamento.economia}">
                    <f:converter converterId="FormatadorNumerico"/>
                </h:outputText>
            </rich:column>
        </rich:dataTable>
        <h:panelGroup layout="block" styleClass="margin-box" style="margin-top: 30px; text-align: center;">
            <h:panelGroup layout="block" styleClass="margin-box">
                <h:outputText
                        style="margin-right: 20px;"
                        styleClass="texto-font texto-size-40 texto-cor-verde texto-bold"
                        value="Economia do Fator ZW"></h:outputText>
            </h:panelGroup>

            <h:panelGroup layout="block" styleClass="gr-container-totalizador" style="margin-top: -35px;">
                <h:panelGroup layout="block" styleClass="gr-totalizador" style="height: auto;vertical-align: top;">
                    <h:panelGroup layout="block" styleClass="gr-totalizador-item-size-40">
                        <h:outputText style="display: block;" styleClass="bi-font-family texto-size-25 bi-cor-cinza texto-bold"
                                      value="MENSAL"/>
                    </h:panelGroup>

                    <h:panelGroup layout="block" styleClass="gr-totalizador-item-size-40-middle">
                        <h:outputText styleClass="texto-font texto-size-40 texto-cor-azul"
                                      value="R$ "></h:outputText>
                        <h:outputText value="#{CalculadoraControle.economiaMensal}"
                                      styleClass="bi-font-family texto-size-40 bi-cor-azul texto-bold">
                            <f:converter converterId="FormatadorNumerico"/>
                        </h:outputText>
                    </h:panelGroup>
                </h:panelGroup>
                <h:panelGroup layout="block" styleClass="gr-totalizador" style="height: auto;vertical-align: top;">
                    <h:panelGroup layout="block" styleClass="gr-totalizador-item-size-40">
                        <h:outputText style="display: block;" styleClass="bi-font-family texto-size-25 bi-cor-cinza texto-bold"
                                      value="ANUAL"/>
                    </h:panelGroup>

                    <h:panelGroup layout="block" styleClass="gr-totalizador-item-size-40-middle">
                        <h:outputText styleClass="texto-font texto-size-40 texto-cor-azul"
                                      value="R$ "></h:outputText>
                        <h:outputText value="#{CalculadoraControle.economiaAnual}"
                                      styleClass="bi-font-family texto-size-40 bi-cor-azul texto-bold">
                            <f:converter converterId="FormatadorNumerico"/>
                        </h:outputText>
                    </h:panelGroup>
                </h:panelGroup>
            </h:panelGroup>

            <h:panelGroup layout="block" styleClass="margin-box" style="text-align: center;">
                <a4j:commandLink styleClass="botaoPrimario texto-cor-branco texto-size-16"
                                 style="margin-right: 10px"
                                 action="#{CalculadoraControle.passoAnterior}"
                                 title="Visualizar Faturamento"
                                 value="Anterior"
                                 reRender="painelGeral"/>
                <a4j:commandLink styleClass="botaoPrimario texto-cor-branco texto-size-16"
                                 action="#{CalculadoraControle.novaSimulacao}"
                                 title="Limpa as taxas inseridos manualmente"
                                 value="Executar Novamente"
                                 reRender="painelGeral"/>
            </h:panelGroup>
        </h:panelGroup>
    </h:panelGroup>
</h:panelGroup>

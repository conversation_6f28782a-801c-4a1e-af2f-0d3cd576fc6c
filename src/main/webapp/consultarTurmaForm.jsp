<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@page contentType="text/html;charset=UTF-8" %>
<head><%@include file="/includes/include_import_minifiles.jsp" %></head>
<link href="./beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<link href="beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<script type="text/javascript" language="javascript" src="hoverform.js"></script>
<script type="text/javascript" language="javascript" src="script/script.js"></script>
<script src="script/packJQueryPlugins.min.js" type="text/javascript" ></script>

<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>

<%
    String tipo = request.getParameter("tipo");
    if (tipo == null) {
        tipo = "consulta";
    }
    pageContext.setAttribute("tipo", tipo);
%>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>
<f:loadBundle var="CElabels" basename="propriedades.CElabels"/>

<style type="text/css">
    .label,
    .badge {
        display: inline-block;
        padding: 2px 4px;        
        margin: 1px 1px;
        font-weight: bold;
        min-width: 10px;
        line-height: 8px;
        font-size: 9px;
        vertical-align:top;
        color: #ffffff;
        vertical-align: baseline;
        white-space: nowrap;
        text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.25);
        /*background-color: #807579;        */
        background: #c5deea; /* Old browsers */
        /* IE9 SVG, needs conditional override of 'filter' to 'none' */
        background: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiA/Pgo8c3ZnIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgd2lkdGg9IjEwMCUiIGhlaWdodD0iMTAwJSIgdmlld0JveD0iMCAwIDEgMSIgcHJlc2VydmVBc3BlY3RSYXRpbz0ibm9uZSI+CiAgPGxpbmVhckdyYWRpZW50IGlkPSJncmFkLXVjZ2ctZ2VuZXJhdGVkIiBncmFkaWVudFVuaXRzPSJ1c2VyU3BhY2VPblVzZSIgeDE9IjAlIiB5MT0iMCUiIHgyPSIxMDAlIiB5Mj0iMCUiPgogICAgPHN0b3Agb2Zmc2V0PSIwJSIgc3RvcC1jb2xvcj0iI2M1ZGVlYSIgc3RvcC1vcGFjaXR5PSIxIi8+CiAgICA8c3RvcCBvZmZzZXQ9IjMxJSIgc3RvcC1jb2xvcj0iIzhhYmJkNyIgc3RvcC1vcGFjaXR5PSIxIi8+CiAgICA8c3RvcCBvZmZzZXQ9IjEwMCUiIHN0b3AtY29sb3I9IiMwNjZkYWIiIHN0b3Atb3BhY2l0eT0iMSIvPgogIDwvbGluZWFyR3JhZGllbnQ+CiAgPHJlY3QgeD0iMCIgeT0iMCIgd2lkdGg9IjEiIGhlaWdodD0iMSIgZmlsbD0idXJsKCNncmFkLXVjZ2ctZ2VuZXJhdGVkKSIgLz4KPC9zdmc+);
        background: -moz-linear-gradient(left,  #c5deea 0%, #8abbd7 31%, #066dab 100%); /* FF3.6+ */
        background: -webkit-gradient(linear, left top, right top, color-stop(0%,#c5deea), color-stop(31%,#8abbd7), color-stop(100%,#066dab)); /* Chrome,Safari4+ */
        background: -webkit-linear-gradient(left,  #c5deea 0%,#8abbd7 31%,#066dab 100%); /* Chrome10+,Safari5.1+ */
        background: -o-linear-gradient(left,  #c5deea 0%,#8abbd7 31%,#066dab 100%); /* Opera 11.10+ */
        background: -ms-linear-gradient(left,  #c5deea 0%,#8abbd7 31%,#066dab 100%); /* IE10+ */
        background: linear-gradient(to right,  #c5deea 0%,#8abbd7 31%,#066dab 100%); /* W3C */
        filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#c5deea', endColorstr='#066dab',GradientType=1 ); /* IE6-8 */


        border-right: 1px solid #ccc;
        border-left: 1px solid #ccc;
        border-top: 1px solid #ccc;

    }
    .label {
        -webkit-border-radius: 3px;
        -moz-border-radius: 3px;
        border-radius: 3px;
    }
    .badge {
        padding-left: 9px;
        padding-right: 9px;
        -webkit-border-radius: 9px;
        -moz-border-radius: 9px;
        border-radius: 9px;
    }
    .label:empty,
    .badge:empty {
        display: none;
    }
    a.label:hover,
    a.label:focus,
    a.badge:hover,
    a.badge:focus {
        color: #ffffff;
        text-decoration: none;
        cursor: pointer;
    }
    .btn .label,
    .btn .badge {
        position: relative;
        top: -1px;
    }
    .btn-mini .label,
    .btn-mini .badge {
        top: 0;
    }
    .colMapaTitulo{
        width: 14% !important;
        vertical-align: top;
        padding: 0;
        background-color: #EEEEEE;
    }
    .colMapa{
        width: 14% !important;
        vertical-align: top;
    }

    .grid{
        border-top: 0 !important;
    }
    .semBorda{
        border: 0 !important;
    }
    .semCor{
        background-color: none;
    }
</style>


<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <a4j:loadScript src="/script/jquery.maskedinput-1.2.2.js" />
    <script>
        jQuery( document ).ready(function() {
            renderizacomponentesConsultaTurma();
        });
    </script>
    <title>
        <c:if test="${tipo eq 'mapa'}">
            <c:set var="titulo" scope="session" value="${msg_aplic.prt_ConsultarTurma_mapaTurma}"/>
            <c:set var="urlWiki" scope="session" value="${SuperControle.urlBaseConhecimento}relatorio-mapa-de-turmas/"/>
            <c:set var="titulo" scope="session" value="Mapa de Turmas"/>
            <c:set var="urlWiki" scope="session" value="${SuperControle.urlBaseConhecimento}relatorio-mapa-de-turmas/"/>
        </c:if>
        <c:if test="${tipo eq 'consulta'}">
            <c:set var="titulo" scope="session" value="${msg_aplic.prt_ConsultarTurma_TituloForm}"/>
            <c:set var="urlWiki" scope="session" value="${SuperControle.urlBaseConhecimento}o-que-e-consulta-de-turma/"/>
            <c:set var="titulo" scope="session" value="${msg_aplic.prt_ConsultarTurma_TituloForm}"/>
            <c:set var="urlWiki" scope="session" value="${SuperControle.urlBaseConhecimento}o-que-e-consulta-de-turma/"/>
        </c:if>

    </title>
    <jsp:include page="topoReduzido_material.jsp"/>
    <h:form id="form">
        <hr style="border-color: #e6e6e6;"/>
    <%-- <a4j:poll id="pollConsulta" interval="3000" status="none" enabled="#{ConsultarTurmaControle.pollEnabled}"
              reRender="botoes, botoesmapa, panelHorarioTurmaConcatenado, panelStatusRealizandoConsulta"/> --%>

         <a4j:jsFunction name="renderizacomponentesConsultaTurma" status="none" reRender="botoes, botoesmapa, panelHorarioTurmaConcatenado, panelStatusRealizandoConsulta"/>

        <input type="hidden" value="${tipo}" name="tipo"/>

        <h:panelGrid columns="1"  width="100%" columnClasses="colunaCentralizada">            
            <h:panelGrid id="panelFiltros" columns="1" columnClasses="colunaTopCentralizada" width="100%">
                <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar" columnClasses="classEsquerda, classDireita" width="100%" 
                             style="padding-left:0px;">

                    <!-- empresa -->
                    <h:outputText rendered="#{ConsultarTurmaControle.mostrarEmpresa}" styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold" value="#{msg_aplic.prt_ConsultarTurma_empresa}"/>
                    <h:panelGroup styleClass="font-size-em-max" rendered="#{ConsultarTurmaControle.mostrarEmpresa}">
                        <div class="cb-container margenVertical">
                            <h:selectOneMenu id="empresa"  onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form texto-size-12-real texto-cor-cinza texto-font" value="#{ConsultarTurmaControle.consultarTurma.empresa.codigo}">
                                <f:selectItems value="#{ConsultarTurmaControle.consultarTurma.listaEmpresa}"/>
                                <a4j:support event="onchange" action="#{ConsultarTurmaControle.montarListaEmpresa}" reRender="form:panelFiltros" oncomplete="carregarMaskInput();"/>
                            </h:selectOneMenu>
                       </div>     
                    </h:panelGroup>

                    <!-- modalidade -->
                    <h:outputText  styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold" value="#{msg_aplic.prt_ConsultarTurma_modalidade}"/>
                    <h:panelGroup styleClass="font-size-em-max">
                        <div class="cb-container margenVertical">
                            <h:selectOneMenu id="modalidade" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form texto-size-12-real texto-cor-cinza texto-font" value="#{ConsultarTurmaControle.consultarTurma.modalidade.codigo}">
                                <f:selectItems value="#{ConsultarTurmaControle.consultarTurma.listaModalidade}"/>
                                <a4j:support event="onchange" action="#{ConsultarTurmaControle.montarListaTurma}" reRender="form:panelFiltros, form:botoesmapa" oncomplete="carregarMaskInput();"/>
                            </h:selectOneMenu>
                        </div>    
                    </h:panelGroup>

                    <!-- turma -->
                    <h:outputText  styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold" value="#{msg_aplic.prt_ConsultarTurma_turma}"/>
                    <h:panelGroup styleClass="font-size-em-max">
                        <div class="cb-container margenVertical">
                            <h:selectOneMenu id="turma" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form texto-size-12-real texto-cor-cinza texto-font" value="#{ConsultarTurmaControle.consultarTurma.turma.codigo}">
                                <f:selectItems value="#{ConsultarTurmaControle.consultarTurma.listaTurma}"/>
                                <a4j:support event="onchange" action="#{ConsultarTurmaControle.montarListaProfessor}" reRender="form:panelFiltros" oncomplete="carregarMaskInput();"/>
                            </h:selectOneMenu>
                        </div>    
                    </h:panelGroup>

                    <!-- professor -->
                    <h:outputText  styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold" value="#{msg_aplic.prt_ConsultarTurma_professor}"/>
                    <h:panelGroup styleClass="font-size-em-max">
                        <div class="cb-container margenVertical">
                            <h:selectOneMenu id="professor" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form texto-size-12-real texto-cor-cinza texto-font" value="#{ConsultarTurmaControle.consultarTurma.professor.codigo}">
                                <f:selectItems value="#{ConsultarTurmaControle.consultarTurma.listaProfessor}"/>
                                <a4j:support event="onchange" reRender="form:panelFiltros" oncomplete="carregarMaskInput();"/>
                            </h:selectOneMenu>
                        </div>    
                    </h:panelGroup>

                    <c:if test="${tipo eq 'mapa'}">
                        <!-- nivel -->
                        <h:outputText  styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold" value="#{msg_aplic.prt_ConsultarTurma_nivel}"/>
                        <h:panelGroup styleClass="font-size-em-max">
                            <div class="cb-container margenVertical">
                                <h:selectOneMenu id="nivel" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form texto-size-12-real texto-cor-cinza texto-font"
                                                 value="#{ConsultarTurmaControle.consultarTurma.nivel.codigo}">
                                    <f:selectItems value="#{ConsultarTurmaControle.consultarTurma.listaNivel}"/>
                                </h:selectOneMenu>
                            </div>    
                        </h:panelGroup>

                        <!-- ambiente -->
                        <h:outputText  styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold" value="#{msg_aplic.prt_ConsultarTurma_ambiente}"/>
                        <h:panelGroup styleClass="font-size-em-max">
                            <div class="cb-container margenVertical">
                                <h:selectOneMenu id="ambiente" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form texto-size-12-real texto-cor-cinza texto-font"
                                                 value="#{ConsultarTurmaControle.consultarTurma.ambiente.codigo}">
                                    <f:selectItems value="#{ConsultarTurmaControle.consultarTurma.listaAmbiente}"/>
                                </h:selectOneMenu>
                            </div>    
                        </h:panelGroup>
                         <h:outputText  styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold" value="#{msg_aplic.prt_ConsultarTurma_DataReferencia}"/>
                         <h:panelGrid id="pnDataReferencia" columns="1">
                            <h:panelGroup  styleClass="dateTimeCustom" layout="block" style="height: 40px;" >
                                <rich:calendar id="dataReferencia"
                                               value="#{ConsultarTurmaControle.dataReferenciaMapa}"
                                               inputSize="10"
                                               inputClass="form"
                                               oninputblur="blurinput(this);"
                                               oninputfocus="focusinput(this);"
                                               onchanged="validar_Data(this.id);gerarPeriodoConsulta(#{rich:component('dataReferencia')}.getCurrentDate());return validar_Data(this.id);"
                                               oninputchange="validar_Data(this.id);gerarPeriodoConsulta(#{rich:component('dataReferencia')}.getCurrentDate());return validar_Data(this.id);"
                                               datePattern="dd/MM/yyyy"
                                               enableManualInput="true"
                                               buttonIcon="/imagens_flat/calendar-button.svg"
                                               zindex="2"
                                               showWeeksBar="false" >
                                </rich:calendar>
                                <a4j:jsFunction name="gerarPeriodoConsulta" action="#{ConsultarTurmaControle.gerarPeriodoConsultaMapa}"
                                                reRender="pnDataReferencia" >
                                    <a4j:actionparam name="dtreferencia" assignTo="#{ConsultarTurmaControle.dataReferenciaMapa}"/>
                                </a4j:jsFunction>
                            </h:panelGroup>
                            <h:outputText rendered="#{ConsultarTurmaControle.dataReferenciaMapa != null}" styleClass="text" value="Consulta de #{ConsultarTurmaControle.filtroDatas.inicio_Apresentar} até #{ConsultarTurmaControle.filtroDatas.fim_Apresentar}"/>
                         </h:panelGrid>   

                        <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold" value="CONSIDERAR DESMARCAÇÕES:"/>
                        <h:panelGroup layout="block"  styleClass="checkbox-fa semEventoClasse">
                            <h:panelGrid columns="1" width="100%" style="margin-top: 5px;">
                                <div onclick="checkBoxClick(this,event);">
                                     <a4j:commandLink reRender="form:panelFiltros" styleClass="linkPadrao" id="considerarDesmarcacoes">
                                            <f:setPropertyActionListener  value="#{!ConsultarTurmaControle.considerarDesmarcoes}" target="#{ConsultarTurmaControle.considerarDesmarcoes}"/>
                                            <h:outputText style="font-family: Arial" styleClass="#{ConsultarTurmaControle.considerarDesmarcoes ? 'fa-icon-check' : 'fa-icon-check-empty'} texto-cor-cinza texto-font texto-size-16" value=""/>
                                     </a4j:commandLink>
                                </div>
                            </h:panelGrid>
                        </h:panelGroup>
                       
                        <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold" value="CONSULTAR TURMAS INATIVAS:"/>
                        <h:panelGroup layout="block" style="text-align: left;" styleClass="checkbox-fa semEventoClasse">
                            <h:panelGrid columns="1" width="100%" style="margin-top: 5px;">
                                <div onclick="checkBoxClick(this,event);">
                                    <a4j:commandLink reRender="form:panelFiltros" styleClass="linkPadrao" id="consultarTurmasInativas">
                                            <f:setPropertyActionListener  value="#{!ConsultarTurmaControle.todasTurmas}" target="#{ConsultarTurmaControle.todasTurmas}"/>
                                            <h:outputText style="font-family: Arial" styleClass="#{ConsultarTurmaControle.todasTurmas ? 'fa-icon-check' : 'fa-icon-check-empty'} texto-cor-cinza texto-font texto-size-16" value=""/>
                                     </a4j:commandLink>
                                </div>
                           </h:panelGrid> 
                        </h:panelGroup>    
                        <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold" value="ORDENAR POR:"/>
                        <h:selectOneRadio styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold" style="vertical-align: top;" value="#{ConsultarTurmaControle.ordenacaoSelecionada}">
                            <f:selectItems  value="#{ConsultarTurmaControle.itensOrdenacao}" />
                        </h:selectOneRadio>

                        <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold" value="NOMES DOS ALUNOS:"/>
                        <h:selectOneRadio styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold" style="vertical-align: top;"
                                          value="#{ConsultarTurmaControle.nomesAlunos}">
                            <f:selectItems  value="#{ConsultarTurmaControle.itensNomes}" />
                        </h:selectOneRadio>

                    </c:if>
                    <c:if test="${tipo eq 'consulta'}">
                        <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold" value="#{msg_aplic.prt_ConsultarTurma_exibirReposicoes}"/>
                        <h:panelGroup layout="block" style="text-align: left;" styleClass="checkbox-fa semEventoClasse">
                            <h:panelGrid columns="1" width="100%" style="margin-top: 5px;">
                                <div onclick="checkBoxClick(this,event);">
                                    <a4j:commandLink reRender="form:panelFiltros" styleClass="linkPadrao">
                                            <f:setPropertyActionListener  value="#{!ConsultarTurmaControle.consultarTurma.exibirReposicoes}" target="#{ConsultarTurmaControle.consultarTurma.exibirReposicoes}"/>
                                            <h:outputText style="font-family: Arial" styleClass="#{ConsultarTurmaControle.consultarTurma.exibirReposicoes ? 'fa-icon-check' : 'fa-icon-check-empty'} texto-cor-cinza texto-font texto-size-16" value=""/>
                                     </a4j:commandLink>
                                </div>
                            </h:panelGrid>
                        </h:panelGroup>  
                    </c:if>
                </h:panelGrid>

                 <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar" columnClasses="classEsquerda, classDireita" width="100%" 
                             style="padding-left:0px;">
                    <h:outputText  styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold" value="#{msg_aplic.prt_ConsultarTurma_diaSemana}"/>
                    <h:panelGroup>
                        <h:panelGrid id="panelCheckBoxDia" columns="7" width="100%" style="margin-top: 5px;">
                            <h:panelGroup layout="block" style="text-align: left;" styleClass="checkbox-fa semEventoClasse">
                                <div onclick="checkBoxClick(this,event);">
                                    <a4j:commandLink reRender="form:panelCheckBoxDia" styleClass="linkPadrao" id="diaSemanaDomFiltro">
                                            <f:setPropertyActionListener  value="#{!ConsultarTurmaControle.consultarTurma.domingo}" target="#{ConsultarTurmaControle.consultarTurma.domingo}"/>
                                            <h:outputText style="font-family: Arial" styleClass="#{ConsultarTurmaControle.consultarTurma.domingo ? 'fa-icon-check' : 'fa-icon-check-empty'} texto-cor-cinza texto-font texto-size-16" value="Dom"/>
                                     </a4j:commandLink>
                                </div>
                            </h:panelGroup>  
                            <h:panelGroup layout="block" style="text-align: left;" styleClass="checkbox-fa semEventoClasse">
                                <div onclick="checkBoxClick(this,event);">
                                    <a4j:commandLink reRender="form:panelCheckBoxDia" styleClass="linkPadrao" id="diaSemanaSegFiltro">
                                            <f:setPropertyActionListener  value="#{!ConsultarTurmaControle.consultarTurma.segunda}" target="#{ConsultarTurmaControle.consultarTurma.segunda}"/>
                                            <h:outputText style="font-family: Arial" styleClass="#{ConsultarTurmaControle.consultarTurma.segunda ? 'fa-icon-check' : 'fa-icon-check-empty'} texto-cor-cinza texto-font texto-size-16" value="Seg"/>
                                     </a4j:commandLink>
                                </div>
                            </h:panelGroup> 
                             <h:panelGroup layout="block" style="text-align: left;" styleClass="checkbox-fa semEventoClasse">
                                <div onclick="checkBoxClick(this,event);">
                                    <a4j:commandLink reRender="form:panelCheckBoxDia" styleClass="linkPadrao" id="diaSemanaTerFiltro">
                                            <f:setPropertyActionListener  value="#{!ConsultarTurmaControle.consultarTurma.terca}" target="#{ConsultarTurmaControle.consultarTurma.terca}"/>
                                            <h:outputText style="font-family: Arial" styleClass="#{ConsultarTurmaControle.consultarTurma.terca ? 'fa-icon-check' : 'fa-icon-check-empty'} texto-cor-cinza texto-font texto-size-16" value="Ter"/>
                                     </a4j:commandLink>
                                </div>
                            </h:panelGroup>
                           <h:panelGroup layout="block" style="text-align: left;" styleClass="checkbox-fa semEventoClasse">
                                <div onclick="checkBoxClick(this,event);">
                                    <a4j:commandLink reRender="form:panelCheckBoxDia" styleClass="linkPadrao" id="diaSemanaQuaFiltro">
                                            <f:setPropertyActionListener  value="#{!ConsultarTurmaControle.consultarTurma.quarta}" target="#{ConsultarTurmaControle.consultarTurma.quarta}"/>
                                            <h:outputText style="font-family: Arial" styleClass="#{ConsultarTurmaControle.consultarTurma.quarta ? 'fa-icon-check' : 'fa-icon-check-empty'} texto-cor-cinza texto-font texto-size-16" value="Qua"/>
                                     </a4j:commandLink>
                                </div>
                            </h:panelGroup>
                           <h:panelGroup layout="block" style="text-align: left;" styleClass="checkbox-fa semEventoClasse">
                                <div onclick="checkBoxClick(this,event);">
                                    <a4j:commandLink reRender="form:panelCheckBoxDia" styleClass="linkPadrao" id="diaSemanaQuiFiltro">
                                            <f:setPropertyActionListener  value="#{!ConsultarTurmaControle.consultarTurma.quinta}" target="#{ConsultarTurmaControle.consultarTurma.quinta}"/>
                                            <h:outputText style="font-family: Arial" styleClass="#{ConsultarTurmaControle.consultarTurma.quinta ? 'fa-icon-check' : 'fa-icon-check-empty'} texto-cor-cinza texto-font texto-size-16" value="Qui"/>
                                     </a4j:commandLink>
                                </div>
                            </h:panelGroup>
                            <h:panelGroup layout="block" style="text-align: left;" styleClass="checkbox-fa semEventoClasse">
                                <div onclick="checkBoxClick(this,event);">
                                    <a4j:commandLink reRender="form:panelCheckBoxDia" styleClass="linkPadrao" id="diaSemanaSexFiltro">
                                            <f:setPropertyActionListener  value="#{!ConsultarTurmaControle.consultarTurma.sexta}" target="#{ConsultarTurmaControle.consultarTurma.sexta}"/>
                                            <h:outputText style="font-family: Arial" styleClass="#{ConsultarTurmaControle.consultarTurma.sexta ? 'fa-icon-check' : 'fa-icon-check-empty'} texto-cor-cinza texto-font texto-size-16" value="Sex"/>
                                     </a4j:commandLink>
                                </div>
                            </h:panelGroup>
                          
                            <h:panelGroup layout="block" style="text-align: left;" styleClass="checkbox-fa semEventoClasse">
                                <div onclick="checkBoxClick(this,event);">
                                    <a4j:commandLink reRender="form:panelCheckBoxDia" styleClass="linkPadrao" id="diaSemanaSabFiltro">
                                            <f:setPropertyActionListener  value="#{!ConsultarTurmaControle.consultarTurma.sabado}" target="#{ConsultarTurmaControle.consultarTurma.sabado}"/>
                                            <h:outputText style="font-family: Arial" styleClass="#{ConsultarTurmaControle.consultarTurma.sabado ? 'fa-icon-check' : 'fa-icon-check-empty'} texto-cor-cinza texto-font texto-size-16" value="Sab"/>
                                     </a4j:commandLink>
                                </div>
                            </h:panelGroup>
                       </h:panelGrid> 
                    </h:panelGroup>
                    <h:outputText  styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold" value="#{msg_aplic.prt_ConsultarTurma_horarios}"/>
                    <h:panelGroup id="panelCheckBoxHorarioFaixa1">
                        <h:panelGrid columns="4" width="100%" style="margin-top: 5px;">
                            <h:panelGroup layout="block" style="text-align: left;" styleClass="checkbox-fa semEventoClasse">
                                <div onclick="checkBoxClick(this,event);">
                                    <a4j:commandLink reRender="form:panelCheckBoxHorarioFaixa1" styleClass="linkPadrao" id="hora0000-0159Filtro">
                                            <f:setPropertyActionListener  value="#{!ConsultarTurmaControle.consultarTurma.h0001as0200}" target="#{ConsultarTurmaControle.consultarTurma.h0001as0200}"/>
                                            <h:outputText style="font-family: Arial" styleClass="#{ConsultarTurmaControle.consultarTurma.h0001as0200 ? 'fa-icon-check' : 'fa-icon-check-empty'} texto-cor-cinza texto-font texto-size-16" value="00:00 - 01:59"/>
                                     </a4j:commandLink>
                                </div>
                            </h:panelGroup>
                           <h:panelGroup layout="block" style="text-align: left;" styleClass="checkbox-fa semEventoClasse">
                                <div onclick="checkBoxClick(this,event);">
                                    <a4j:commandLink reRender="form:panelCheckBoxHorarioFaixa1" styleClass="linkPadrao" id="hora0200-0359Filtro">
                                            <f:setPropertyActionListener  value="#{!ConsultarTurmaControle.consultarTurma.h0201as0400}" target="#{ConsultarTurmaControle.consultarTurma.h0201as0400}"/>
                                            <h:outputText style="font-family: Arial" styleClass="#{ConsultarTurmaControle.consultarTurma.h0201as0400 ? 'fa-icon-check' : 'fa-icon-check-empty'} texto-cor-cinza texto-font texto-size-16" value="02:00 - 03:59"/>
                                     </a4j:commandLink>
                                </div>
                            </h:panelGroup>
                            <h:panelGroup layout="block" style="text-align: left;" styleClass="checkbox-fa semEventoClasse">
                                <div onclick="checkBoxClick(this,event);">
                                    <a4j:commandLink reRender="form:panelCheckBoxHorarioFaixa1" styleClass="linkPadrao" id="hora0400-0559Filtro">
                                            <f:setPropertyActionListener  value="#{!ConsultarTurmaControle.consultarTurma.h0401as0600}" target="#{ConsultarTurmaControle.consultarTurma.h0401as0600}"/>
                                            <h:outputText style="font-family: Arial" styleClass="#{ConsultarTurmaControle.consultarTurma.h0401as0600 ? 'fa-icon-check' : 'fa-icon-check-empty'} texto-cor-cinza texto-font texto-size-16" value="04:00 - 05:59"/>
                                     </a4j:commandLink>
                                </div>
                            </h:panelGroup>
                        </h:panelGrid>
                    </h:panelGroup>
                    <h:outputText value=""/>
                    <h:panelGroup id="panelCheckBoxHorarioFaixa2">
                        <h:panelGrid columns="4" width="100%" style="margin-top: 5px;">
                            <h:panelGroup layout="block" style="text-align: left;" styleClass="checkbox-fa semEventoClasse">
                                <div onclick="checkBoxClick(this,event);">
                                    <a4j:commandLink reRender="form:panelCheckBoxHorarioFaixa2" styleClass="linkPadrao" id="hora0600-0759Filtro">
                                            <f:setPropertyActionListener  value="#{!ConsultarTurmaControle.consultarTurma.h0601as0800}" target="#{ConsultarTurmaControle.consultarTurma.h0601as0800}"/>
                                            <h:outputText style="font-family: Arial" styleClass="#{ConsultarTurmaControle.consultarTurma.h0601as0800 ? 'fa-icon-check' : 'fa-icon-check-empty'} texto-cor-cinza texto-font texto-size-16" value="06:00 - 07:59"/>
                                     </a4j:commandLink>
                                </div>
                            </h:panelGroup>
                            <h:panelGroup layout="block" style="text-align: left;" styleClass="checkbox-fa semEventoClasse">
                                <div onclick="checkBoxClick(this,event);">
                                    <a4j:commandLink reRender="form:panelCheckBoxHorarioFaixa2" styleClass="linkPadrao" id="hora0800-0959Filtro">
                                            <f:setPropertyActionListener  value="#{!ConsultarTurmaControle.consultarTurma.h0801as1000}" target="#{ConsultarTurmaControle.consultarTurma.h0801as1000}"/>
                                            <h:outputText style="font-family: Arial" styleClass="#{ConsultarTurmaControle.consultarTurma.h0801as1000 ? 'fa-icon-check' : 'fa-icon-check-empty'} texto-cor-cinza texto-font texto-size-16" value="08:00 - 09:59"/>
                                     </a4j:commandLink>
                                </div>
                            </h:panelGroup>
                            <h:panelGroup layout="block" style="text-align: left;" styleClass="checkbox-fa semEventoClasse">
                                <div onclick="checkBoxClick(this,event);">
                                    <a4j:commandLink reRender="form:panelCheckBoxHorarioFaixa2" styleClass="linkPadrao" id="hora1000-1159Filtro">
                                            <f:setPropertyActionListener  value="#{!ConsultarTurmaControle.consultarTurma.h1001as1200}" target="#{ConsultarTurmaControle.consultarTurma.h1001as1200}"/>
                                            <h:outputText style="font-family: Arial" styleClass="#{ConsultarTurmaControle.consultarTurma.h1001as1200 ? 'fa-icon-check' : 'fa-icon-check-empty'} texto-cor-cinza texto-font texto-size-16" value="10:00 - 11:59"/>
                                     </a4j:commandLink>
                                </div>
                            </h:panelGroup>
                        </h:panelGrid>
                    </h:panelGroup>
                    <h:outputText value=""/>
                    <h:panelGroup id="panelCheckBoxHorarioFaixa3">
                        <h:panelGrid columns="4" width="100%" style="margin-top: 5px;">
                            <h:panelGroup layout="block" style="text-align: left;" styleClass="checkbox-fa semEventoClasse">
                                <div onclick="checkBoxClick(this,event);">
                                    <a4j:commandLink reRender="form:panelCheckBoxHorarioFaixa3" styleClass="linkPadrao" id="hora1200-1359Filtro">
                                            <f:setPropertyActionListener  value="#{!ConsultarTurmaControle.consultarTurma.h1201as1400}" target="#{ConsultarTurmaControle.consultarTurma.h1201as1400}"/>
                                            <h:outputText style="font-family: Arial" styleClass="#{ConsultarTurmaControle.consultarTurma.h1201as1400 ? 'fa-icon-check' : 'fa-icon-check-empty'} texto-cor-cinza texto-font texto-size-16" value="12:00 - 13:59"/>
                                     </a4j:commandLink>
                                </div>
                            </h:panelGroup>
                            <h:panelGroup layout="block" style="text-align: left;" styleClass="checkbox-fa semEventoClasse">
                                <div onclick="checkBoxClick(this,event);">
                                    <a4j:commandLink reRender="form:panelCheckBoxHorarioFaixa3" styleClass="linkPadrao" id="hora1400-1559Filtro">
                                            <f:setPropertyActionListener  value="#{!ConsultarTurmaControle.consultarTurma.h1401as1600}" target="#{ConsultarTurmaControle.consultarTurma.h1401as1600}"/>
                                            <h:outputText style="font-family: Arial" styleClass="#{ConsultarTurmaControle.consultarTurma.h1401as1600 ? 'fa-icon-check' : 'fa-icon-check-empty'} texto-cor-cinza texto-font texto-size-16" value="14:00 - 15:59"/>
                                     </a4j:commandLink>
                                </div>
                            </h:panelGroup>
                            <h:panelGroup layout="block" style="text-align: left;" styleClass="checkbox-fa semEventoClasse">
                                <div onclick="checkBoxClick(this,event);">
                                    <a4j:commandLink reRender="form:panelCheckBoxHorarioFaixa3" styleClass="linkPadrao" id="hora1600-1759Filtro">
                                            <f:setPropertyActionListener  value="#{!ConsultarTurmaControle.consultarTurma.h1601as1800}" target="#{ConsultarTurmaControle.consultarTurma.h1601as1800}"/>
                                            <h:outputText style="font-family: Arial" styleClass="#{ConsultarTurmaControle.consultarTurma.h1601as1800 ? 'fa-icon-check' : 'fa-icon-check-empty'} texto-cor-cinza texto-font texto-size-16" value="16:00 - 17:59"/>
                                     </a4j:commandLink>
                                </div>
                            </h:panelGroup> 
                       </h:panelGrid>
                    </h:panelGroup>
                    <h:outputText value=""/>
                    <h:panelGroup id="panelCheckBoxHorarioFaixa4">
                        <h:panelGrid columns="4" width="100%" style="margin-top: 5px;">
                            <h:panelGroup layout="block" style="text-align: left;" styleClass="checkbox-fa semEventoClasse">
                                <div onclick="checkBoxClick(this,event);">
                                    <a4j:commandLink reRender="form:panelCheckBoxHorarioFaixa4" styleClass="linkPadrao" id="hora1800-1959Filtro">
                                            <f:setPropertyActionListener  value="#{!ConsultarTurmaControle.consultarTurma.h1801as2000}" target="#{ConsultarTurmaControle.consultarTurma.h1801as2000}"/>
                                            <h:outputText style="font-family: Arial" styleClass="#{ConsultarTurmaControle.consultarTurma.h1801as2000 ? 'fa-icon-check' : 'fa-icon-check-empty'} texto-cor-cinza texto-font texto-size-16" value="18:00 - 19:59"/>
                                     </a4j:commandLink>
                                </div>
                            </h:panelGroup>
                            <h:panelGroup layout="block" style="text-align: left;" styleClass="checkbox-fa semEventoClasse">
                                <div onclick="checkBoxClick(this,event);">
                                    <a4j:commandLink reRender="form:panelCheckBoxHorarioFaixa4" styleClass="linkPadrao" id="hora2000-2159Filtro">
                                            <f:setPropertyActionListener  value="#{!ConsultarTurmaControle.consultarTurma.h2001as2200}" target="#{ConsultarTurmaControle.consultarTurma.h2001as2200}"/>
                                            <h:outputText style="font-family: Arial" styleClass="#{ConsultarTurmaControle.consultarTurma.h2001as2200 ? 'fa-icon-check' : 'fa-icon-check-empty'} texto-cor-cinza texto-font texto-size-16" value="20:00 - 21:59"/>
                                     </a4j:commandLink>
                                </div>
                            </h:panelGroup>
                            <h:panelGroup layout="block" style="text-align: left;" styleClass="checkbox-fa semEventoClasse">
                                <div onclick="checkBoxClick(this,event);">
                                    <a4j:commandLink reRender="form:panelCheckBoxHorarioFaixa4" styleClass="linkPadrao" id="hora2200-2359Filtro">
                                            <f:setPropertyActionListener  value="#{!ConsultarTurmaControle.consultarTurma.h2201as0000}" target="#{ConsultarTurmaControle.consultarTurma.h2201as0000}"/>
                                            <h:outputText style="font-family: Arial" styleClass="#{ConsultarTurmaControle.consultarTurma.h2201as0000 ? 'fa-icon-check' : 'fa-icon-check-empty'} texto-cor-cinza texto-font texto-size-16" value="22:00 - 23:59"/>
                                     </a4j:commandLink>
                                </div>
                            </h:panelGroup>
                        </h:panelGrid>
                    </h:panelGroup>
                </h:panelGrid>


            </h:panelGrid>
            <h:panelGrid width="100%" columnClasses="colunaCentralizada">
                <h:panelGrid columns="1" width="100%" styleClass="tabMensagens" style="padding:10px;">
                    <h:outputText styleClass="mensagem" value="#{ConsultarTurmaControle.mensagem}"/>
                    <h:outputText styleClass="mensagemDetalhada" value="#{ConsultarTurmaControle.mensagemDetalhada}"/>
                </h:panelGrid>
                <h:panelGroup id="botoesmapa" styleClass="container-botoes" layout="block">
                    <c:if test="${tipo eq 'mapa'}">
                        <a4j:commandLink id="consultarMapaSemMod"
                                           value=""
                                           title="#{msg.msg_consultar_dados}"
                                           style="line-height: 15px;"
                                           oncomplete="Richfaces.showModalPanel('panelConfirmacao');"
                                           rendered="#{ConsultarTurmaControle.consultarTurma.modalidade.codigo == 0}"
                                           accesskey="4" styleClass="botoes nvoBt">
                            Consultar&nbsp<i class="fa-icon-search"></i>
                                    </a4j:commandLink>

                        <a4j:commandLink id="consultarMapaComMod"
                                           action="#{ConsultarTurmaControle.consultarTurmasMapa}"
                                           reRender="form,botoesmapa" 
                                           style="line-height: 15px;"
                                           rendered="#{ConsultarTurmaControle.consultarTurma.modalidade.codigo != 0}"
                                           title="#{msg.msg_consultar_dados}"
                                           accesskey="2"  styleClass="botoes nvoBt">
                            Consultar&nbsp<i class="fa-icon-search"></i>
                                    </a4j:commandLink>
                        &nbsp;
                        <a4j:commandLink id="imprimirPDF" ajaxSingle="false"
                                           action="#{ConsultarTurmaControle.imprimirMapaPDF}"
                                           style="margin-left: 8px; font-size: 16px;"
                                           reRender="mensagem"
                                           oncomplete="#{ConsultarTurmaControle.mensagemNotificar}#{ConsultarTurmaControle.msgAlert}"
                                           accesskey="2" styleClass="linkPadrao">
                            <h:outputText title="Exportar Mapa para o formato PDF" styleClass="btn-print-2 pdf"/>
                        </a4j:commandLink>

                        <a4j:commandLink id="imprimirExcel" ajaxSingle="false"
                                         action="#{ConsultarTurmaControle.imprimirMapaExcel}"
                                         style="margin-left: 8px; font-size: 16px;"
                                         reRender="mensagem"
                                         oncomplete="#{ConsultarTurmaControle.mensagemNotificar}#{ConsultarTurmaControle.msgAlert}"
                                         accesskey="2" styleClass="linkPadrao">
                            <h:outputText title="Exportar Mapa para o formato PDF" styleClass="btn-print-2 excel"/>
                        </a4j:commandLink>
                    </c:if>
                    <c:if test="${tipo eq 'consulta'}">
                        <a4j:commandLink id="consultar" action="#{ConsultarTurmaControle.consultarTurmas}"
                                           reRender="form, botoes, modalidadeTurma" 
                                           title="#{msg.msg_consultar_dados}"
                                           style="line-height: 15px;"
                                           accesskey="4" styleClass="botoes nvoBt">
                            Consultar&nbsp<i class="fa-icon-search"></i>
                                    </a4j:commandLink>
                           <a4j:commandLink id="exportarExcel"
                                       style="margin-left: 8px; font-size: 16px;"
                                       actionListener="#{ExportadorListaControle.exportar}"
                                       rendered="#{not empty ConsultarTurmaControle.listaHorarioTurmaConcatenado}"
                                            oncomplete="#{ExportadorListaControle.mensagemNotificar}#{ExportadorListaControle.operacaoOnComplete}#{ExportadorListaControle.msgAlert}"
                                       accesskey="2" styleClass="linkPadrao">
                            <f:attribute name="lista" value="#{ConsultarTurmaControle.listaHorarioTurmaConcatenado}"/>
                            <f:attribute name="tipo" value="xls"/>
                               <f:attribute name="itemExportacao" value="consultaTurma"/>
                            <f:attribute name="atributos"
                                         value="turma_Apresentar=ID da Turma,professor=Professor,nivel=Nível,ambiente=Ambiente,horaInicial=Hora Início,horaFinal=Hora Fim,quantidadeVagasDom=Dom,quantidadeVagasSeg=Seg,quantidadeVagasTer=Ter,quantidadeVagasQua=Qua,quantidadeVagasQui=Qui,quantidadeVagasSex=Sex,quantidadeVagasSab=Sáb"/>
                            <f:attribute name="prefixo" value="ConsultaTurma"/>
                            <h:outputText title="Exportar para o formato Excel" styleClass="btn-print-2 excel"/>
                        </a4j:commandLink>
                        <%--BOTÃO PDF--%>
                        <a4j:commandLink id="exportarPdf"
                                           style="margin-left: 8px; font-size: 16px;"
                                           actionListener="#{ExportadorListaControle.exportar}"
                                           rendered="#{not empty ConsultarTurmaControle.listaHorarioTurmaConcatenado}"
                                         oncomplete="#{ExportadorListaControle.mensagemNotificar}#{ExportadorListaControle.operacaoOnComplete}#{ExportadorListaControle.msgAlert}"
                                           accesskey="2" styleClass="linkPadrao">
                            <f:attribute name="lista" value="#{ConsultarTurmaControle.listaHorarioTurmaConcatenado}"/>
                            <f:attribute name="tipo" value="pdf"/>
                            <f:attribute name="atributos"
                                         value="turma_Apresentar=ID da Turma,professor=Professor,nivel=Nível,ambiente=Ambiente,horaInicial=Hora Início,horaFinal=Hora Fim,quantidadeVagasDom=Dom,quantidadeVagasSeg=Seg,quantidadeVagasTer=Ter,quantidadeVagasQua=Qua,quantidadeVagasQui=Qui,quantidadeVagasSex=Sex,quantidadeVagasSab=Sáb"/>
                            <f:attribute name="prefixo" value="ConsultaTurma"/>
                            <f:attribute name="itemExportacao" value="consultaTurma"/>
                            <h:outputText title="Exportar para o formato PDF" styleClass="btn-print-2 pdf"/>
                        </a4j:commandLink>   
                    </c:if>
                  
                </h:panelGroup>
            </h:panelGrid>

            <h:panelGrid  columns="1" width="25%"  style="margin: 0 auto;position: relative">
                <h:panelGrid columns="5" style="text-align:center" width="25%">
                    <h:panelGroup layout="block" style="width:110px;height: 15px;text-align: center; padding: 10px; background-color: #A6FFA6">Baixa Ocup.</h:panelGroup>
                    <h:panelGroup layout="block" style="width:110px;height: 15px;text-align: center; padding: 10px;background-color: #A6FFFF">&nbsp;</h:panelGroup>
                    <h:panelGroup layout="block" style="width:110px;height: 15px;text-align: center; padding: 10px;background-color: #FFFFA6">&nbsp;</h:panelGroup>
                    <h:panelGroup layout="block" style="width:110px;height: 15px;text-align: center; padding: 10px;background-color: #FEE3BB">&nbsp;</h:panelGroup>
                    <h:panelGroup layout="block" style="width:110px;height: 15px;text-align: center; padding: 10px;background-color: #FFA6A6">Alta Ocup.</h:panelGroup>
                </h:panelGrid>
            </h:panelGrid>


            <h:panelGrid id="panelHorarioTurmaConcatenado" columns="1" width="100%" >
                <c:if test="${tipo eq 'consulta'}">
                    <rich:dataTable id="modalidadeTurma" width="100%" rowClasses="linhaImpar, linhaPar" columnClasses="colunaCentralizada"
                                    value="#{ConsultarTurmaControle.listaHorarioTurmaConcatenado}" var="horarioTurmaConcatenado" rowKeyVar="status">
                        <%@include file="/pages/ce/includes/include_contador_richtable.jsp" %>
                        <rich:column sortBy="#{horarioTurmaConcatenado.turma.identificador}" filterEvent="onkeyup">
                            <f:facet name="header">
                                <h:outputText value="ID da Turma"/>
                            </f:facet>
                            <h:outputText  value="#{horarioTurmaConcatenado.turma_Apresentar}"/>
                        </rich:column>
                        <rich:column sortBy="#{horarioTurmaConcatenado.professor}" filterEvent="onkeyup" >
                            <f:facet name="header">
                                <h:outputText value="Professor"/>
                            </f:facet>
                            <h:outputText  value="#{horarioTurmaConcatenado.professor}"/>
                        </rich:column>
                        <rich:column sortBy="#{horarioTurmaConcatenado.nivel}" filterEvent="onkeyup">
                            <f:facet name="header">
                                <h:outputText value="Nível"/>
                            </f:facet>
                            <h:outputText  value="#{horarioTurmaConcatenado.nivel}"/>
                        </rich:column>
                        <rich:column sortBy="#{horarioTurmaConcatenado.ambiente}" filterEvent="onkeyup">
                            <f:facet name="header">
                                <h:outputText value="Ambiente"/>
                            </f:facet>
                            <h:outputText  value="#{horarioTurmaConcatenado.ambiente}"/>
                        </rich:column>
                        <rich:column sortBy="#{horarioTurmaConcatenado.mediaOcupacao}" filterEvent="onkeyup">
                            <f:facet name="header">
                                <h:outputText value="Média ocupação"/>
                            </f:facet>
                            <h:outputText  value="#{horarioTurmaConcatenado.mediaOcupacao}"><f:converter converterId="FormatarPercentual" /></h:outputText>
                        </rich:column>
                        <rich:column sortBy="#{horarioTurmaConcatenado.horaInicial}" filterEvent="onkeyup">
                            <f:facet name="header">
                                <h:outputText value="Hora Início"/>
                            </f:facet>
                            <h:outputText  value="#{horarioTurmaConcatenado.horaInicial}"/>
                        </rich:column>
                        <rich:column  sortBy="#{horarioTurmaConcatenado.horaFinal}" filterEvent="onkeyup">
                            <f:facet name="header">
                                <h:outputText value="Hora Fim"/>
                            </f:facet>
                            <h:outputText value="#{horarioTurmaConcatenado.horaFinal}"/>
                        </rich:column>
                        <rich:column title="#{horarioTurmaConcatenado.tooltipDescDom}" styleClass="tooltipster-top"
                                style="background-color: #{horarioTurmaConcatenado.resultadoOcupacaoDom}">
                            <f:facet name="header">
                                <h:outputText value="Dom"/>
                            </f:facet>
                            <h:panelGroup rendered="#{horarioTurmaConcatenado.horarioTurmaDom.codigo > 0}">
                                <a4j:commandLink id="consultaTurmaResultDom"
                                                 styleClass="text2"
                                                 reRender="panelDadosReposicao"
                                                 rendered="#{horarioTurmaConcatenado.horarioTurmaDom.quantidadeVagas > 0 and horarioTurmaConcatenado.horarioTurmaDom.ativo}"
                                                 value="#{horarioTurmaConcatenado.ocupacaoDom}"
                                                 actionListener="#{ConsultarTurmaControle.mostrarDadosTurma}"
                                                 oncomplete="#{ConsultarTurmaControle.consultarTurma.onComplete}">
                                    <f:attribute name="diaSemana" value="DM"/>
                                    <f:attribute name="htc" value="#{horarioTurmaConcatenado}"/>
                                </a4j:commandLink>
                                <c:if test="${!horarioTurmaConcatenado.horarioTurmaDom.ativo and !ConsultarTurmaControle.consultarTurma.modoReposicao}">
                                    <a4j:commandLink style="color: #777;"
                                                     reRender="panelDadosReposicao"
                                                     rendered="#{!horarioTurmaConcatenado.horarioTurmaDom.ativo}"
                                                     value="#{horarioTurmaConcatenado.ocupacaoDom}"
                                                     actionListener="#{ConsultarTurmaControle.mostrarDadosTurma}"
                                                     oncomplete="#{ConsultarTurmaControle.consultarTurma.onComplete}"
                                                     title="Horário Inativo">
                                        <f:attribute name="diaSemana" value="DM"/>
                                        <f:attribute name="htc" value="#{horarioTurmaConcatenado}"/>
                                    </a4j:commandLink>
                                </c:if>
                                <c:if test="${!horarioTurmaConcatenado.horarioTurmaDom.ativo and ConsultarTurmaControle.consultarTurma.modoReposicao}">
                                    <a4j:commandLink style="color: #777;"
                                                     reRender="panelDadosReposicao"
                                                     rendered="#{!horarioTurmaConcatenado.horarioTurmaDom.ativo}"
                                                     disabled="true"
                                                     value="#{horarioTurmaConcatenado.ocupacaoDom}"
                                                     actionListener="#{ConsultarTurmaControle.mostrarDadosTurma}"
                                                     oncomplete="#{ConsultarTurmaControle.consultarTurma.onComplete}"
                                                     title="Horário Inativo">
                                        <f:attribute name="diaSemana" value="DM"/>
                                        <f:attribute name="htc" value="#{horarioTurmaConcatenado}"/>
                                    </a4j:commandLink>
                                </c:if>

                                <a4j:commandLink styleClass="link_red"
                                                 reRender="panelDadosReposicao"
                                                 rendered="#{horarioTurmaConcatenado.horarioTurmaDom.quantidadeVagas <= 0}"
                                                 value="#{horarioTurmaConcatenado.horarioTurmaDom.nrMaximoAluno}"
                                                 actionListener="#{ConsultarTurmaControle.mostrarDadosTurma}"
                                                 oncomplete="#{ConsultarTurmaControle.consultarTurma.onComplete}">
                                    <f:attribute name="diaSemana" value="DM"/>
                                    <f:attribute name="htc" value="#{horarioTurmaConcatenado}"/>
                                </a4j:commandLink>

                                <h:panelGroup rendered="#{ConsultarTurmaControle.consultarTurma.exibirReposicoes}">
                                    <a4j:commandLink rendered="#{horarioTurmaConcatenado.horarioTurmaDom.nrAlunoSairamPorReposicao != 0}"
                                                     actionListener="#{ReposicaoControle.exibirReposicoes}"
                                                     title="#{msg_aplic.prt_ConsultarTurma_xAlunosDesmarcaram}"
                                                     reRender="modalReposicoes"
                                                     oncomplete="#{ReposicaoControle.onComplete}"
                                                     style="color:black"
                                                     value=" +#{horarioTurmaConcatenado.horarioTurmaDom.nrAlunoSairamPorReposicao}">
                                        <f:attribute name="horarioTurmaAtual" value="#{horarioTurmaConcatenado.horarioTurmaDom}"/>
                                        <f:attribute name="sairam" value="true"/>
                                    </a4j:commandLink>

                                    <a4j:commandLink rendered="#{horarioTurmaConcatenado.horarioTurmaDom.nrAlunoEntraramPorReposicao != 0}"
                                                     actionListener="#{ReposicaoControle.exibirReposicoes}"
                                                     style="color:red"
                                                     reRender="modalReposicoes"
                                                     title="#{msg_aplic.prt_ConsultarTurma_xAlunosMarcaram}"
                                                     oncomplete="#{ReposicaoControle.onComplete}"
                                                     value=" -#{horarioTurmaConcatenado.horarioTurmaDom.nrAlunoEntraramPorReposicao}">
                                        <f:attribute name="horarioTurmaAtual" value="#{horarioTurmaConcatenado.horarioTurmaDom}"/>
                                        <f:attribute name="sairam" value="false"/>
                                    </a4j:commandLink>

                                    <a4j:commandLink id="consultaTotalTurmaResultDom"
                                                     styleClass="text2"
                                                     reRender="panelDadosReposicao"
                                                     rendered="#{horarioTurmaConcatenado.horarioTurmaDom.quantidadeVagas > 0 and horarioTurmaConcatenado.horarioTurmaDom.ativo}"
                                                     value="/#{horarioTurmaConcatenado.horarioTurmaDom.nrMaximoAluno}"
                                                     actionListener="#{ConsultarTurmaControle.mostrarDadosTurma}"
                                                     oncomplete="#{ConsultarTurmaControle.consultarTurma.onComplete}">
                                        <f:attribute name="diaSemana" value="DM"/>
                                        <f:attribute name="htc" value="#{horarioTurmaConcatenado}"/>
                                    </a4j:commandLink>

                                    <a4j:commandLink styleClass="link_red"
                                                     reRender="panelDadosReposicao"
                                                     rendered="#{horarioTurmaConcatenado.horarioTurmaDom.quantidadeVagas <= 0}"
                                                     value="/#{horarioTurmaConcatenado.horarioTurmaDom.nrMaximoAluno}"
                                                     actionListener="#{ConsultarTurmaControle.mostrarDadosTurma}"
                                                     oncomplete="#{ConsultarTurmaControle.consultarTurma.onComplete}">
                                        <f:attribute name="diaSemana" value="DM"/>
                                        <f:attribute name="htc" value="#{horarioTurmaConcatenado}"/>
                                    </a4j:commandLink>
                                </h:panelGroup>

                            </h:panelGroup>
                        </rich:column>
                        <rich:column title="#{horarioTurmaConcatenado.tooltipDescSeg}" styleClass="tooltipster-top"
                                      style="background-color: #{horarioTurmaConcatenado.resultadoOcupacaoSeg}">
                            <f:facet name="header">
                                <h:outputText value="Seg"/>
                            </f:facet>
                            <h:panelGroup rendered="#{horarioTurmaConcatenado.horarioTurmaSeg.codigo > 0}">
                                <a4j:commandLink id="consultaTurmaResultSeg"
                                                 styleClass="text2"
                                                 reRender="panelDadosReposicao"
                                                 rendered="#{horarioTurmaConcatenado.horarioTurmaSeg.quantidadeVagas > 0 and horarioTurmaConcatenado.horarioTurmaSeg.ativo}"
                                                 value="#{horarioTurmaConcatenado.ocupacaoSeg}"
                                                 actionListener="#{ConsultarTurmaControle.mostrarDadosTurma}"
                                                 oncomplete="#{ConsultarTurmaControle.consultarTurma.onComplete}">
                                    <f:attribute name="diaSemana" value="SG"/>
                                    <f:attribute name="htc" value="#{horarioTurmaConcatenado}"/>
                                </a4j:commandLink>
                                <c:if test="${!horarioTurmaConcatenado.horarioTurmaSeg.ativo and !ConsultarTurmaControle.consultarTurma.modoReposicao}">
                                    <a4j:commandLink style="color: #777;"
                                                     reRender="panelDadosReposicao"
                                                     rendered="#{!horarioTurmaConcatenado.horarioTurmaSeg.ativo}"
                                                     value="#{horarioTurmaConcatenado.ocupacaoSeg}"
                                                     actionListener="#{ConsultarTurmaControle.mostrarDadosTurma}"
                                                     oncomplete="#{ConsultarTurmaControle.consultarTurma.onComplete}"
                                                     title="Horário Inativo">
                                        <f:attribute name="diaSemana" value="SG"/>
                                        <f:attribute name="htc" value="#{horarioTurmaConcatenado}"/>
                                    </a4j:commandLink>
                                </c:if>
                                <c:if test="${!horarioTurmaConcatenado.horarioTurmaSeg.ativo and ConsultarTurmaControle.consultarTurma.modoReposicao}">
                                    <a4j:commandLink style="color: #777;"
                                                     reRender="panelDadosReposicao"
                                                     rendered="#{!horarioTurmaConcatenado.horarioTurmaSeg.ativo}"
                                                     disabled="true"
                                                     value="#{horarioTurmaConcatenado.ocupacaoSeg}"
                                                     actionListener="#{ConsultarTurmaControle.mostrarDadosTurma}"
                                                     oncomplete="#{ConsultarTurmaControle.consultarTurma.onComplete}"
                                                     title="Horário Inativo">
                                        <f:attribute name="diaSemana" value="SG"/>
                                        <f:attribute name="htc" value="#{horarioTurmaConcatenado}"/>
                                    </a4j:commandLink>
                                </c:if>
                                <a4j:commandLink styleClass="link_red"
                                                 reRender="panelDadosReposicao"
                                                 rendered="#{horarioTurmaConcatenado.horarioTurmaSeg.quantidadeVagas <= 0}"
                                                 value="#{horarioTurmaConcatenado.horarioTurmaSeg.nrMaximoAluno}"
                                                 actionListener="#{ConsultarTurmaControle.mostrarDadosTurma}"
                                                 oncomplete="#{ConsultarTurmaControle.consultarTurma.onComplete}">
                                    <f:attribute name="diaSemana" value="SG"/>
                                    <f:attribute name="htc" value="#{horarioTurmaConcatenado}"/>
                                </a4j:commandLink>

                                <h:panelGroup rendered="#{ConsultarTurmaControle.consultarTurma.exibirReposicoes}">
                                    <a4j:commandLink rendered="#{horarioTurmaConcatenado.horarioTurmaSeg.nrAlunoSairamPorReposicao != 0}"
                                                     actionListener="#{ReposicaoControle.exibirReposicoes}"
                                                     reRender="modalReposicoes"
                                                     style="color:black"
                                                     title="#{msg_aplic.prt_ConsultarTurma_xAlunosDesmarcaram}"
                                                     oncomplete="#{ReposicaoControle.onComplete}"
                                                     value=" +#{horarioTurmaConcatenado.horarioTurmaSeg.nrAlunoSairamPorReposicao}">
                                        <f:attribute name="horarioTurmaAtual" value="#{horarioTurmaConcatenado.horarioTurmaSeg}"/>
                                        <f:attribute name="sairam" value="true"/>
                                    </a4j:commandLink>

                                    <a4j:commandLink rendered="#{horarioTurmaConcatenado.horarioTurmaSeg.nrAlunoEntraramPorReposicao != 0}"
                                                     style="color:red"
                                                     actionListener="#{ReposicaoControle.exibirReposicoes}"
                                                     reRender="modalReposicoes"
                                                     title="#{msg_aplic.prt_ConsultarTurma_xAlunosMarcaram}"
                                                     oncomplete="#{ReposicaoControle.onComplete}"
                                                     value=" -#{horarioTurmaConcatenado.horarioTurmaSeg.nrAlunoEntraramPorReposicao}">
                                        <f:attribute name="horarioTurmaAtual" value="#{horarioTurmaConcatenado.horarioTurmaSeg}"/>
                                        <f:attribute name="sairam" value="false"/>
                                    </a4j:commandLink>

                                    <a4j:commandLink id="consultaTotalTurmaResultSeg"
                                                     styleClass="text2"
                                                     reRender="panelDadosReposicao"
                                                     rendered="#{horarioTurmaConcatenado.horarioTurmaSeg.quantidadeVagas > 0 and horarioTurmaConcatenado.horarioTurmaSeg.ativo}"
                                                     value="/#{horarioTurmaConcatenado.horarioTurmaSeg.nrMaximoAluno}"
                                                     actionListener="#{ConsultarTurmaControle.mostrarDadosTurma}"
                                                     oncomplete="#{ConsultarTurmaControle.consultarTurma.onComplete}">
                                        <f:attribute name="diaSemana" value="SG"/>
                                        <f:attribute name="htc" value="#{horarioTurmaConcatenado}"/>
                                    </a4j:commandLink>

                                    <a4j:commandLink styleClass="link_red"
                                                     reRender="panelDadosReposicao"
                                                     rendered="#{horarioTurmaConcatenado.horarioTurmaSeg.quantidadeVagas <= 0}"
                                                     value="/#{horarioTurmaConcatenado.horarioTurmaSeg.nrMaximoAluno}"
                                                     actionListener="#{ConsultarTurmaControle.mostrarDadosTurma}"
                                                     oncomplete="#{ConsultarTurmaControle.consultarTurma.onComplete}">
                                        <f:attribute name="diaSemana" value="SG"/>
                                        <f:attribute name="htc" value="#{horarioTurmaConcatenado}"/>
                                    </a4j:commandLink>
                                </h:panelGroup>

                                <h:panelGroup rendered="#{null != horarioTurmaConcatenado.nivelDescontoTurmaHorarioSeg}"
                                              style="display: inline-block; width: 6px; float: right; background-color: #{horarioTurmaConcatenado.nivelDescontoTurmaHorarioSeg.color}" >&nbsp;</h:panelGroup>
                            </h:panelGroup>
                        </rich:column>
                        <rich:column title="#{horarioTurmaConcatenado.tooltipDescTer}" styleClass="tooltipster-top"
                                     style="background-color: #{horarioTurmaConcatenado.resultadoOcupacaoTer}">
                            <f:facet name="header">
                                <h:outputText value="Ter"/>
                            </f:facet>
                            <h:panelGroup rendered="#{horarioTurmaConcatenado.horarioTurmaTer.codigo > 0}">
                                <a4j:commandLink id="consultaTurmaResultTer"
                                                 styleClass="text2"
                                                 reRender="panelDadosReposicao"
                                                 rendered="#{horarioTurmaConcatenado.horarioTurmaTer.quantidadeVagas > 0  && horarioTurmaConcatenado.horarioTurmaTer.ativo}"
                                                 value="#{horarioTurmaConcatenado.ocupacaoTer}"
                                                 actionListener="#{ConsultarTurmaControle.mostrarDadosTurma}"
                                                 oncomplete="#{ConsultarTurmaControle.consultarTurma.onComplete}">
                                    <f:attribute name="diaSemana" value="TR"/>
                                    <f:attribute name="htc" value="#{horarioTurmaConcatenado}"/>
                                </a4j:commandLink>

                                <c:if test="${!horarioTurmaConcatenado.horarioTurmaTer.ativo and !ConsultarTurmaControle.consultarTurma.modoReposicao}">
                                    <a4j:commandLink style="color: #777;"
                                                     reRender="panelDadosReposicao"
                                                     rendered="#{!horarioTurmaConcatenado.horarioTurmaTer.ativo}"
                                                     value="#{horarioTurmaConcatenado.ocupacaoTer}"
                                                     actionListener="#{ConsultarTurmaControle.mostrarDadosTurma}"
                                                     oncomplete="#{ConsultarTurmaControle.consultarTurma.onComplete}"
                                                     title="Horário Inativo">
                                        <f:attribute name="diaSemana" value="TR"/>
                                        <f:attribute name="htc" value="#{horarioTurmaConcatenado}"/>
                                    </a4j:commandLink>
                                </c:if>
                                <c:if test="${!horarioTurmaConcatenado.horarioTurmaTer.ativo and ConsultarTurmaControle.consultarTurma.modoReposicao}">
                                    <a4j:commandLink style="color: #777;"
                                                     reRender="panelDadosReposicao"
                                                     rendered="#{!horarioTurmaConcatenado.horarioTurmaTer.ativo}"
                                                     disabled="true"
                                                     value="#{horarioTurmaConcatenado.ocupacaoTer}"
                                                     actionListener="#{ConsultarTurmaControle.mostrarDadosTurma}"
                                                     oncomplete="#{ConsultarTurmaControle.consultarTurma.onComplete}"
                                                     title="Horário Inativo">
                                        <f:attribute name="diaSemana" value="TR"/>
                                        <f:attribute name="htc" value="#{horarioTurmaConcatenado}"/>
                                    </a4j:commandLink>
                                </c:if>
                                <a4j:commandLink styleClass="link_red"
                                                 reRender="panelDadosReposicao"
                                                 rendered="#{horarioTurmaConcatenado.horarioTurmaTer.quantidadeVagas <= 0}"
                                                 value="#{horarioTurmaConcatenado.horarioTurmaTer.nrMaximoAluno}"
                                                 actionListener="#{ConsultarTurmaControle.mostrarDadosTurma}"
                                                 oncomplete="#{ConsultarTurmaControle.consultarTurma.onComplete}">
                                    <f:attribute name="diaSemana" value="TR"/>
                                    <f:attribute name="htc" value="#{horarioTurmaConcatenado}"/>
                                </a4j:commandLink>

                                <h:panelGroup rendered="#{ConsultarTurmaControle.consultarTurma.exibirReposicoes}">
                                    <a4j:commandLink rendered="#{horarioTurmaConcatenado.horarioTurmaTer.nrAlunoSairamPorReposicao != 0}"
                                                     actionListener="#{ReposicaoControle.exibirReposicoes}"
                                                     title="#{msg_aplic.prt_ConsultarTurma_xAlunosDesmarcaram}"
                                                     reRender="modalReposicoes"
                                                     style="color:black"
                                                     oncomplete="#{ReposicaoControle.onComplete}"
                                                     value=" +#{horarioTurmaConcatenado.horarioTurmaTer.nrAlunoSairamPorReposicao}">
                                        <f:attribute name="horarioTurmaAtual" value="#{horarioTurmaConcatenado.horarioTurmaTer}"/>
                                        <f:attribute name="sairam" value="true"/>
                                    </a4j:commandLink>

                                    <a4j:commandLink rendered="#{horarioTurmaConcatenado.horarioTurmaTer.nrAlunoEntraramPorReposicao != 0}"
                                                     style="color:red"
                                                     actionListener="#{ReposicaoControle.exibirReposicoes}"
                                                     title="#{msg_aplic.prt_ConsultarTurma_xAlunosMarcaram}"
                                                     reRender="modalReposicoes"
                                                     oncomplete="#{ReposicaoControle.onComplete}"
                                                     value=" -#{horarioTurmaConcatenado.horarioTurmaTer.nrAlunoEntraramPorReposicao}">
                                        <f:attribute name="horarioTurmaAtual" value="#{horarioTurmaConcatenado.horarioTurmaTer}"/>
                                        <f:attribute name="sairam" value="false"/>
                                    </a4j:commandLink>

                                    <a4j:commandLink id="consultaTotalTurmaResultTer"
                                                     styleClass="text2"
                                                     reRender="panelDadosReposicao"
                                                     rendered="#{horarioTurmaConcatenado.horarioTurmaTer.quantidadeVagas > 0  && horarioTurmaConcatenado.horarioTurmaTer.ativo}"
                                                     value="/#{horarioTurmaConcatenado.horarioTurmaTer.nrMaximoAluno}"
                                                     actionListener="#{ConsultarTurmaControle.mostrarDadosTurma}"
                                                     oncomplete="#{ConsultarTurmaControle.consultarTurma.onComplete}">
                                        <f:attribute name="diaSemana" value="TR"/>
                                        <f:attribute name="htc" value="#{horarioTurmaConcatenado}"/>
                                    </a4j:commandLink>

                                    <a4j:commandLink styleClass="link_red"
                                                     reRender="panelDadosReposicao"
                                                     rendered="#{horarioTurmaConcatenado.horarioTurmaTer.quantidadeVagas <= 0}"
                                                     value="/#{horarioTurmaConcatenado.horarioTurmaTer.nrMaximoAluno}"
                                                     actionListener="#{ConsultarTurmaControle.mostrarDadosTurma}"
                                                     oncomplete="#{ConsultarTurmaControle.consultarTurma.onComplete}">
                                        <f:attribute name="diaSemana" value="TR"/>
                                        <f:attribute name="htc" value="#{horarioTurmaConcatenado}"/>
                                    </a4j:commandLink>
                                </h:panelGroup>

                            </h:panelGroup>
                        </rich:column>
                        <rich:column title="#{horarioTurmaConcatenado.tooltipDescQua}" styleClass="tooltipster-top"
                                     style="background-color: #{horarioTurmaConcatenado.resultadoOcupacaoQua}">
                            <f:facet name="header">
                                <h:outputText value="Qua"/>
                            </f:facet>
                            <h:panelGroup rendered="#{horarioTurmaConcatenado.horarioTurmaQua.codigo > 0}">
                                <a4j:commandLink id="consultaTurmaResultQua"
                                                 styleClass="text2"
                                                 reRender="panelDadosReposicao"
                                                 rendered="#{horarioTurmaConcatenado.horarioTurmaQua.quantidadeVagas > 0 and horarioTurmaConcatenado.horarioTurmaQua.ativo}"
                                                 value="#{horarioTurmaConcatenado.ocupacaoQua}"
                                                 actionListener="#{ConsultarTurmaControle.mostrarDadosTurma}"
                                                 oncomplete="#{ConsultarTurmaControle.consultarTurma.onComplete}">
                                    <f:attribute name="diaSemana" value="QA"/>
                                    <f:attribute name="htc" value="#{horarioTurmaConcatenado}"/>
                                </a4j:commandLink>

                                <c:if test="${!horarioTurmaConcatenado.horarioTurmaQua.ativo and !ConsultarTurmaControle.consultarTurma.modoReposicao}">
                                    <a4j:commandLink style="color: #777;"
                                                     reRender="panelDadosReposicao"
                                                     rendered="#{!horarioTurmaConcatenado.horarioTurmaQua.ativo}"
                                                     value="#{horarioTurmaConcatenado.ocupacaoQua}"
                                                     actionListener="#{ConsultarTurmaControle.mostrarDadosTurma}"
                                                     oncomplete="#{ConsultarTurmaControle.consultarTurma.onComplete}"
                                                     title="Horário Inativo">
                                        <f:attribute name="diaSemana" value="QA"/>
                                        <f:attribute name="htc" value="#{horarioTurmaConcatenado}"/>
                                    </a4j:commandLink>
                                </c:if>
                                <c:if test="${!horarioTurmaConcatenado.horarioTurmaQua.ativo and ConsultarTurmaControle.consultarTurma.modoReposicao}">
                                    <a4j:commandLink style="color: #777;"
                                                     reRender="panelDadosReposicao"
                                                     rendered="#{!horarioTurmaConcatenado.horarioTurmaQua.ativo}"
                                                     disabled="true"
                                                     value="#{horarioTurmaConcatenado.ocupacaoQua}"
                                                     actionListener="#{ConsultarTurmaControle.mostrarDadosTurma}"
                                                     oncomplete="#{ConsultarTurmaControle.consultarTurma.onComplete}"
                                                     title="Horário Inativo">
                                        <f:attribute name="diaSemana" value="QA"/>
                                        <f:attribute name="htc" value="#{horarioTurmaConcatenado}"/>
                                    </a4j:commandLink>
                                </c:if>

                                <a4j:commandLink styleClass="link_red"
                                                 reRender="panelDadosReposicao"
                                                 rendered="#{horarioTurmaConcatenado.horarioTurmaQua.quantidadeVagas <= 0}"
                                                 value="#{horarioTurmaConcatenado.horarioTurmaQua.nrMaximoAluno}"
                                                 actionListener="#{ConsultarTurmaControle.mostrarDadosTurma}"
                                                 oncomplete="#{ConsultarTurmaControle.consultarTurma.onComplete}">
                                    <f:attribute name="diaSemana" value="QA"/>
                                    <f:attribute name="htc" value="#{horarioTurmaConcatenado}"/>
                                </a4j:commandLink>

                                <h:panelGroup rendered="#{ConsultarTurmaControle.consultarTurma.exibirReposicoes}">
                                    <a4j:commandLink rendered="#{horarioTurmaConcatenado.horarioTurmaQua.nrAlunoSairamPorReposicao != 0}"
                                                     actionListener="#{ReposicaoControle.exibirReposicoes}"
                                                     title="#{msg_aplic.prt_ConsultarTurma_xAlunosDesmarcaram}"
                                                     reRender="modalReposicoes"
                                                     style="color:black"
                                                     oncomplete="#{ReposicaoControle.onComplete}"
                                                     value=" +#{horarioTurmaConcatenado.horarioTurmaQua.nrAlunoSairamPorReposicao}">
                                        <f:attribute name="horarioTurmaAtual" value="#{horarioTurmaConcatenado.horarioTurmaQua}"/>
                                        <f:attribute name="sairam" value="true"/>
                                    </a4j:commandLink>

                                    <a4j:commandLink rendered="#{horarioTurmaConcatenado.horarioTurmaQua.nrAlunoEntraramPorReposicao != 0}"
                                                     style="color:red"
                                                     actionListener="#{ReposicaoControle.exibirReposicoes}"
                                                     title="#{msg_aplic.prt_ConsultarTurma_xAlunosMarcaram}"
                                                     reRender="modalReposicoes"
                                                     oncomplete="#{ReposicaoControle.onComplete}"
                                                     value=" -#{horarioTurmaConcatenado.horarioTurmaQua.nrAlunoEntraramPorReposicao}">
                                        <f:attribute name="horarioTurmaAtual" value="#{horarioTurmaConcatenado.horarioTurmaQua}"/>
                                        <f:attribute name="sairam" value="false"/>
                                    </a4j:commandLink>

                                    <a4j:commandLink id="consultaTotalTurmaResultQua"
                                                     styleClass="text2"
                                                     reRender="panelDadosReposicao"
                                                     rendered="#{horarioTurmaConcatenado.horarioTurmaQua.quantidadeVagas > 0 and horarioTurmaConcatenado.horarioTurmaQua.ativo}"
                                                     value="/#{horarioTurmaConcatenado.horarioTurmaQua.nrMaximoAluno}"
                                                     actionListener="#{ConsultarTurmaControle.mostrarDadosTurma}"
                                                     oncomplete="#{ConsultarTurmaControle.consultarTurma.onComplete}">
                                        <f:attribute name="diaSemana" value="QA"/>
                                        <f:attribute name="htc" value="#{horarioTurmaConcatenado}"/>
                                    </a4j:commandLink>

                                    <a4j:commandLink styleClass="link_red"
                                                     reRender="panelDadosReposicao"
                                                     rendered="#{horarioTurmaConcatenado.horarioTurmaQua.quantidadeVagas <= 0}"
                                                     value="/#{horarioTurmaConcatenado.horarioTurmaQua.nrMaximoAluno}"
                                                     actionListener="#{ConsultarTurmaControle.mostrarDadosTurma}"
                                                     oncomplete="#{ConsultarTurmaControle.consultarTurma.onComplete}">
                                        <f:attribute name="diaSemana" value="QA"/>
                                        <f:attribute name="htc" value="#{horarioTurmaConcatenado}"/>
                                    </a4j:commandLink>
                                </h:panelGroup>

                            </h:panelGroup>
                        </rich:column>
                        <rich:column title="#{horarioTurmaConcatenado.tooltipDescQui}" styleClass="tooltipster-top"
                                     style="background-color: #{horarioTurmaConcatenado.resultadoOcupacaoQui}">
                            <f:facet name="header">
                                <h:outputText value="Qui"/>
                            </f:facet>
                            <h:panelGroup rendered="#{horarioTurmaConcatenado.horarioTurmaQui.codigo > 0}">
                                <a4j:commandLink id="consultaTurmaResultQui"
                                                 styleClass="text2"
                                                 reRender="panelDadosReposicao"
                                                 rendered="#{horarioTurmaConcatenado.horarioTurmaQui.quantidadeVagas > 0 and horarioTurmaConcatenado.horarioTurmaQui.ativo}"
                                                 value="#{horarioTurmaConcatenado.ocupacaoQui}"
                                                 actionListener="#{ConsultarTurmaControle.mostrarDadosTurma}"
                                                 oncomplete="#{ConsultarTurmaControle.consultarTurma.onComplete}">
                                    <f:attribute name="diaSemana" value="QI"/>
                                    <f:attribute name="htc" value="#{horarioTurmaConcatenado}"/>
                                </a4j:commandLink>

                                <c:if test="${!horarioTurmaConcatenado.horarioTurmaQui.ativo && !ConsultarTurmaControle.consultarTurma.modoReposicao}">
                                    <a4j:commandLink style="color: #777;"
                                                     reRender="panelDadosReposicao"
                                                     rendered="#{!horarioTurmaConcatenado.horarioTurmaQui.ativo}"
                                                     value="#{horarioTurmaConcatenado.ocupacaoQui}"
                                                     actionListener="#{ConsultarTurmaControle.mostrarDadosTurma}"
                                                     oncomplete="#{ConsultarTurmaControle.consultarTurma.onComplete}"
                                                     title="Horário Inativo">
                                        <f:attribute name="diaSemana" value="QI"/>
                                        <f:attribute name="htc" value="#{horarioTurmaConcatenado}"/>
                                    </a4j:commandLink>
                                </c:if>
                                <c:if test="${!horarioTurmaConcatenado.horarioTurmaQui.ativo && ConsultarTurmaControle.consultarTurma.modoReposicao}">
                                    <a4j:commandLink style="color: #777;"
                                                     reRender="panelDadosReposicao"
                                                     value="#{horarioTurmaConcatenado.ocupacaoQui}"
                                                     rendered="#{!horarioTurmaConcatenado.horarioTurmaQui.ativo}"
                                                     disabled="true"
                                                     actionListener="#{ConsultarTurmaControle.mostrarDadosTurma}"
                                                     oncomplete="#{ConsultarTurmaControle.consultarTurma.onComplete}"
                                                     title="Horário Inativo">
                                        <f:attribute name="diaSemana" value="QI"/>
                                        <f:attribute name="htc" value="#{horarioTurmaConcatenado}"/>
                                    </a4j:commandLink>
                                </c:if>


                                <a4j:commandLink styleClass="link_red"
                                                 reRender="panelDadosReposicao"
                                                 rendered="#{horarioTurmaConcatenado.horarioTurmaQui.quantidadeVagas <= 0}"
                                                 value="#{horarioTurmaConcatenado.horarioTurmaQui.nrMaximoAluno}"
                                                 actionListener="#{ConsultarTurmaControle.mostrarDadosTurma}"
                                                 oncomplete="#{ConsultarTurmaControle.consultarTurma.onComplete}">
                                    <f:attribute name="diaSemana" value="QI"/>
                                    <f:attribute name="htc" value="#{horarioTurmaConcatenado}"/>
                                </a4j:commandLink>

                                <h:panelGroup rendered="#{ConsultarTurmaControle.consultarTurma.exibirReposicoes}">
                                    <a4j:commandLink rendered="#{horarioTurmaConcatenado.horarioTurmaQui.nrAlunoSairamPorReposicao != 0}"
                                                     actionListener="#{ReposicaoControle.exibirReposicoes}"
                                                     title="#{msg_aplic.prt_ConsultarTurma_xAlunosDesmarcaram}"
                                                     reRender="modalReposicoes"
                                                     style="color:black"
                                                     oncomplete="#{ReposicaoControle.onComplete}"
                                                     value=" +#{horarioTurmaConcatenado.horarioTurmaQui.nrAlunoSairamPorReposicao}">
                                        <f:attribute name="horarioTurmaAtual" value="#{horarioTurmaConcatenado.horarioTurmaQui}"/>
                                        <f:attribute name="sairam" value="true"/>
                                    </a4j:commandLink>

                                    <a4j:commandLink rendered="#{horarioTurmaConcatenado.horarioTurmaQui.nrAlunoEntraramPorReposicao != 0}"
                                                     style="color:red"
                                                     actionListener="#{ReposicaoControle.exibirReposicoes}"
                                                     title="#{msg_aplic.prt_ConsultarTurma_xAlunosMarcaram}"
                                                     reRender="modalReposicoes"
                                                     oncomplete="#{ReposicaoControle.onComplete}"
                                                     value=" -#{horarioTurmaConcatenado.horarioTurmaQui.nrAlunoEntraramPorReposicao}">
                                        <f:attribute name="horarioTurmaAtual" value="#{horarioTurmaConcatenado.horarioTurmaQui}"/>
                                        <f:attribute name="sairam" value="false"/>
                                    </a4j:commandLink>

                                    <a4j:commandLink id="consultaTotalTurmaResultQui"
                                                     styleClass="text2"
                                                     reRender="panelDadosReposicao"
                                                     rendered="#{horarioTurmaConcatenado.horarioTurmaQui.quantidadeVagas > 0 and horarioTurmaConcatenado.horarioTurmaQui.ativo}"
                                                     value="/#{horarioTurmaConcatenado.horarioTurmaQui.nrMaximoAluno}"
                                                     actionListener="#{ConsultarTurmaControle.mostrarDadosTurma}"
                                                     oncomplete="#{ConsultarTurmaControle.consultarTurma.onComplete}">
                                        <f:attribute name="diaSemana" value="QI"/>
                                        <f:attribute name="htc" value="#{horarioTurmaConcatenado}"/>
                                    </a4j:commandLink>

                                    <a4j:commandLink styleClass="link_red"
                                                     reRender="panelDadosReposicao"
                                                     rendered="#{horarioTurmaConcatenado.horarioTurmaQui.quantidadeVagas <= 0}"
                                                     value="/#{horarioTurmaConcatenado.horarioTurmaQui.nrMaximoAluno}"
                                                     actionListener="#{ConsultarTurmaControle.mostrarDadosTurma}"
                                                     oncomplete="#{ConsultarTurmaControle.consultarTurma.onComplete}">
                                        <f:attribute name="diaSemana" value="QI"/>
                                        <f:attribute name="htc" value="#{horarioTurmaConcatenado}"/>
                                    </a4j:commandLink>
                                </h:panelGroup>

                            </h:panelGroup>
                        </rich:column>
                        <rich:column title="#{horarioTurmaConcatenado.tooltipDescSex}" styleClass="tooltipster-top"
                                     style="background-color: #{horarioTurmaConcatenado.resultadoOcupacaoSex}">
                            <f:facet name="header">
                                <h:outputText value="Sex"/>
                            </f:facet>
                            <h:panelGroup rendered="#{horarioTurmaConcatenado.horarioTurmaSex.codigo > 0}">
                                <a4j:commandLink  id="consultaTurmaResultSex"
                                                  styleClass="text2"
                                                  reRender="panelDadosReposicao"
                                                  rendered="#{horarioTurmaConcatenado.horarioTurmaSex.quantidadeVagas > 0 and horarioTurmaConcatenado.horarioTurmaSex.ativo}"
                                                  value="#{horarioTurmaConcatenado.ocupacaoSex}"
                                                  actionListener="#{ConsultarTurmaControle.mostrarDadosTurma}"
                                                  oncomplete="#{ConsultarTurmaControle.consultarTurma.onComplete}">
                                    <f:attribute name="diaSemana" value="SX"/>
                                    <f:attribute name="htc" value="#{horarioTurmaConcatenado}"/>
                                </a4j:commandLink>

                                <c:if test="${!horarioTurmaConcatenado.horarioTurmaSex.ativo and !ConsultarTurmaControle.consultarTurma.modoReposicao}">

                                    <a4j:commandLink style="color: #777;"
                                                     reRender="panelDadosReposicao"
                                                     rendered="#{!horarioTurmaConcatenado.horarioTurmaSex.ativo}"
                                                     value="#{horarioTurmaConcatenado.ocupacaoSex}"
                                                     actionListener="#{ConsultarTurmaControle.mostrarDadosTurma}"
                                                     oncomplete="#{ConsultarTurmaControle.consultarTurma.onComplete}"
                                                     title="Horário Inativo">
                                        <f:attribute name="diaSemana" value="SX"/>
                                        <f:attribute name="htc" value="#{horarioTurmaConcatenado}"/>
                                    </a4j:commandLink>
                                </c:if>

                                <c:if test="${!horarioTurmaConcatenado.horarioTurmaSex.ativo and ConsultarTurmaControle.consultarTurma.modoReposicao}">

                                    <a4j:commandLink style="color: #777;"
                                                     reRender="panelDadosReposicao"
                                                     rendered="#{!horarioTurmaConcatenado.horarioTurmaSex.ativo}"
                                                     disabled="true"
                                                     value="#{horarioTurmaConcatenado.ocupacaoSex}"
                                                     actionListener="#{ConsultarTurmaControle.mostrarDadosTurma}"
                                                     oncomplete="#{ConsultarTurmaControle.consultarTurma.onComplete}"
                                                     title="Horário Inativo">
                                        <f:attribute name="diaSemana" value="SX"/>
                                        <f:attribute name="htc" value="#{horarioTurmaConcatenado}"/>
                                    </a4j:commandLink>
                                </c:if>

                                <a4j:commandLink  styleClass="link_red"
                                                  reRender="panelDadosReposicao"
                                                  rendered="#{horarioTurmaConcatenado.horarioTurmaSex.quantidadeVagas <= 0}"
                                                  value="#{horarioTurmaConcatenado.horarioTurmaSex.nrMaximoAluno}"
                                                  actionListener="#{ConsultarTurmaControle.mostrarDadosTurma}"
                                                  oncomplete="#{ConsultarTurmaControle.consultarTurma.onComplete}">
                                    <f:attribute name="diaSemana" value="SX"/>
                                    <f:attribute name="htc" value="#{horarioTurmaConcatenado}"/>
                                </a4j:commandLink>

                                <h:panelGroup rendered="#{ConsultarTurmaControle.consultarTurma.exibirReposicoes}">
                                    <a4j:commandLink rendered="#{horarioTurmaConcatenado.horarioTurmaSex.nrAlunoSairamPorReposicao != 0}"
                                                     actionListener="#{ReposicaoControle.exibirReposicoes}"
                                                     title="#{msg_aplic.prt_ConsultarTurma_xAlunosDesmarcaram}"
                                                     reRender="modalReposicoes"
                                                     style="color:black"
                                                     oncomplete="#{ReposicaoControle.onComplete}"
                                                     value=" +#{horarioTurmaConcatenado.horarioTurmaSex.nrAlunoSairamPorReposicao}">
                                        <f:attribute name="horarioTurmaAtual" value="#{horarioTurmaConcatenado.horarioTurmaSex}"/>
                                        <f:attribute name="sairam" value="true"/>
                                    </a4j:commandLink>

                                    <a4j:commandLink rendered="#{horarioTurmaConcatenado.horarioTurmaSex.nrAlunoEntraramPorReposicao != 0}"
                                                     style="color:red"
                                                     actionListener="#{ReposicaoControle.exibirReposicoes}"
                                                     title="#{msg_aplic.prt_ConsultarTurma_xAlunosMarcaram}"
                                                     reRender="modalReposicoes"
                                                     oncomplete="#{ReposicaoControle.onComplete}"
                                                     value=" -#{horarioTurmaConcatenado.horarioTurmaSex.nrAlunoEntraramPorReposicao}">
                                        <f:attribute name="horarioTurmaAtual" value="#{horarioTurmaConcatenado.horarioTurmaSex}"/>
                                        <f:attribute name="sairam" value="false"/>
                                    </a4j:commandLink>

                                    <a4j:commandLink  id="consultaTotalTurmaResultSex"
                                                      styleClass="text2"
                                                      reRender="panelDadosReposicao"
                                                      rendered="#{horarioTurmaConcatenado.horarioTurmaSex.quantidadeVagas > 0 and horarioTurmaConcatenado.horarioTurmaSex.ativo}"
                                                      value="/#{horarioTurmaConcatenado.horarioTurmaSex.nrMaximoAluno}"
                                                      actionListener="#{ConsultarTurmaControle.mostrarDadosTurma}"
                                                      oncomplete="#{ConsultarTurmaControle.consultarTurma.onComplete}">
                                        <f:attribute name="diaSemana" value="SX"/>
                                        <f:attribute name="htc" value="#{horarioTurmaConcatenado}"/>
                                    </a4j:commandLink>

                                    <a4j:commandLink  styleClass="link_red"
                                                      reRender="panelDadosReposicao"
                                                      rendered="#{horarioTurmaConcatenado.horarioTurmaSex.quantidadeVagas <= 0}"
                                                      value="/#{horarioTurmaConcatenado.horarioTurmaSex.nrMaximoAluno}"
                                                      actionListener="#{ConsultarTurmaControle.mostrarDadosTurma}"
                                                      oncomplete="#{ConsultarTurmaControle.consultarTurma.onComplete}">
                                        <f:attribute name="diaSemana" value="SX"/>
                                        <f:attribute name="htc" value="#{horarioTurmaConcatenado}"/>
                                    </a4j:commandLink>
                                </h:panelGroup>

                            </h:panelGroup>
                        </rich:column>
                        <rich:column title="#{horarioTurmaConcatenado.tooltipDescSab}" styleClass="tooltipster-top"
                                     style="background-color: #{horarioTurmaConcatenado.resultadoOcupacaoSab}">
                            <f:facet name="header">
                                <h:outputText value="Sáb"/>
                            </f:facet>
                            <h:panelGroup rendered="#{horarioTurmaConcatenado.horarioTurmaSab.codigo > 0}">
                                <a4j:commandLink id="consultaTurmaResultSab"
                                                 styleClass="text2"
                                                 reRender="panelDadosReposicao"
                                                 rendered="#{horarioTurmaConcatenado.horarioTurmaSab.quantidadeVagas > 0 and horarioTurmaConcatenado.horarioTurmaSab.ativo}"
                                                 value="#{horarioTurmaConcatenado.ocupacaoSab}"
                                                 actionListener="#{ConsultarTurmaControle.mostrarDadosTurma}"
                                                 oncomplete="#{ConsultarTurmaControle.consultarTurma.onComplete}">
                                    <f:attribute name="diaSemana" value="SB"/>
                                    <f:attribute name="htc" value="#{horarioTurmaConcatenado}"/>
                                </a4j:commandLink>
                                <c:if test="${!horarioTurmaConcatenado.horarioTurmaSab.ativo and !ConsultarTurmaControle.consultarTurma.modoReposicao}">
                                    <a4j:commandLink style="color: #777;"
                                                     reRender="panelDadosReposicao"
                                                     rendered="#{!horarioTurmaConcatenado.horarioTurmaSab.ativo}"
                                                     actionListener="#{ConsultarTurmaControle.mostrarDadosTurma}"
                                                     oncomplete="#{ConsultarTurmaControle.consultarTurma.onComplete}"
                                                     title="Horário Inativo">
                                        <f:attribute name="diaSemana" value="SB"/>
                                        <f:attribute name="htc" value="#{horarioTurmaConcatenado}"/>
                                    </a4j:commandLink>
                                </c:if>
                                <c:if test="${!horarioTurmaConcatenado.horarioTurmaSab.ativo and ConsultarTurmaControle.consultarTurma.modoReposicao}">
                                    <a4j:commandLink style="color: #777;"
                                                     reRender="panelDadosReposicao"
                                                     value="#{horarioTurmaConcatenado.ocupacaoSab}"
                                                     rendered="#{!horarioTurmaConcatenado.horarioTurmaSab.ativo}"
                                                     disabled="true"
                                                     actionListener="#{ConsultarTurmaControle.mostrarDadosTurma}"
                                                     oncomplete="#{ConsultarTurmaControle.consultarTurma.onComplete}"
                                                     title="Horário Inativo">
                                        <f:attribute name="diaSemana" value="SB"/>
                                        <f:attribute name="htc" value="#{horarioTurmaConcatenado}"/>
                                    </a4j:commandLink>
                                </c:if>
                                <a4j:commandLink styleClass="link_red"
                                                 reRender="panelDadosReposicao"
                                                 rendered="#{horarioTurmaConcatenado.horarioTurmaSab.quantidadeVagas <= 0}"
                                                 value="#{horarioTurmaConcatenado.horarioTurmaSab.nrMaximoAluno}"
                                                 actionListener="#{ConsultarTurmaControle.mostrarDadosTurma}"
                                                 oncomplete="#{ConsultarTurmaControle.consultarTurma.onComplete}">
                                    <f:attribute name="diaSemana" value="SB"/>
                                    <f:attribute name="htc" value="#{horarioTurmaConcatenado}"/>
                                </a4j:commandLink>

                                <h:panelGroup rendered="#{ConsultarTurmaControle.consultarTurma.exibirReposicoes}">
                                    <a4j:commandLink rendered="#{horarioTurmaConcatenado.horarioTurmaSab.nrAlunoSairamPorReposicao != 0}"
                                                     actionListener="#{ReposicaoControle.exibirReposicoes}"
                                                     title="#{msg_aplic.prt_ConsultarTurma_xAlunosDesmarcaram}"
                                                     reRender="modalReposicoes"
                                                     style="color:black"
                                                     oncomplete="#{ReposicaoControle.onComplete}"
                                                     value=" +#{horarioTurmaConcatenado.horarioTurmaSab.nrAlunoSairamPorReposicao}">
                                        <f:attribute name="horarioTurmaAtual" value="#{horarioTurmaConcatenado.horarioTurmaSab}"/>
                                        <f:attribute name="sairam" value="true"/>
                                    </a4j:commandLink>

                                    <a4j:commandLink rendered="#{horarioTurmaConcatenado.horarioTurmaSab.nrAlunoEntraramPorReposicao != 0}"
                                                     style="color:red"
                                                     actionListener="#{ReposicaoControle.exibirReposicoes}"
                                                     title="#{msg_aplic.prt_ConsultarTurma_xAlunosMarcaram}"
                                                     reRender="modalReposicoes"
                                                     oncomplete="#{ReposicaoControle.onComplete}"
                                                     value=" -#{horarioTurmaConcatenado.horarioTurmaSab.nrAlunoEntraramPorReposicao}">
                                        <f:attribute name="horarioTurmaAtual" value="#{horarioTurmaConcatenado.horarioTurmaSab}"/>
                                        <f:attribute name="sairam" value="false"/>
                                    </a4j:commandLink>

                                    <a4j:commandLink id="consultaTotalTurmaResultSab"
                                                     styleClass="text2"
                                                     reRender="panelDadosReposicao"
                                                     rendered="#{horarioTurmaConcatenado.horarioTurmaSab.quantidadeVagas > 0 and horarioTurmaConcatenado.horarioTurmaSab.ativo}"
                                                     value="/#{horarioTurmaConcatenado.horarioTurmaSab.nrMaximoAluno}"
                                                     actionListener="#{ConsultarTurmaControle.mostrarDadosTurma}"
                                                     oncomplete="#{ConsultarTurmaControle.consultarTurma.onComplete}">
                                        <f:attribute name="diaSemana" value="SB"/>
                                        <f:attribute name="htc" value="#{horarioTurmaConcatenado}"/>
                                    </a4j:commandLink>

                                    <a4j:commandLink styleClass="link_red"
                                                     reRender="panelDadosReposicao"
                                                     rendered="#{horarioTurmaConcatenado.horarioTurmaSab.quantidadeVagas <= 0}"
                                                     value="/#{horarioTurmaConcatenado.horarioTurmaSab.nrMaximoAluno}"
                                                     actionListener="#{ConsultarTurmaControle.mostrarDadosTurma}"
                                                     oncomplete="#{ConsultarTurmaControle.consultarTurma.onComplete}">
                                        <f:attribute name="diaSemana" value="SB"/>
                                        <f:attribute name="htc" value="#{horarioTurmaConcatenado}"/>
                                    </a4j:commandLink>
                                </h:panelGroup>

                            </h:panelGroup>
                        </rich:column>
                        <rich:column>
                            <f:facet name="header">
                                <h:outputText value="Op"/>
                            </f:facet>
                            <a4j:commandButton action="#{ConsultarTurmaControle.prepararListaChamada}"
                                               image="./imagensCRM/iconeVerBv.png" title="Realizar chamada"/>
                        </rich:column>
                    </rich:dataTable>
                </c:if>
                <c:if test="${tipo eq 'mapa'}">
                    <h:panelGroup layout="block" id="containerMapa">
                    <h:panelGrid width="100%" columnClasses="direita">
                        <h:outputText styleClass="texto-size-14-real texto-cor-cinza-2 texto-font texto-bold" value="L = limite / V = vagas"/>

                    </h:panelGrid>
                    <rich:dataGrid columns="7" value="#{ConsultarTurmaControle.diasSemanaLabels}"
                                   var="labelDiaSemana" width="100%" columnClasses="colMapaTitulo centralizado"
                                   cellpadding="0" cellspacing="0">
                        <h:outputText styleClass="texto-size-14-real texto-cor-cinza-2 texto-font texto-bold" value="#{labelDiaSemana}"/>
                    </rich:dataGrid>
                    <a4j:repeat value="#{ConsultarTurmaControle.mapaTurmas}" var="hora">

                        <h:outputText styleClass="texto-size-14-real texto-cor-cinza-2 texto-font texto-bold" value="#{hora.horario}"/>
                        <rich:separator height="2" lineType="dashed"/>
                        <rich:dataGrid columns="7" value="#{hora.mapa.lista}"
                                       var="diaSemana" width="100%" columnClasses="colMapa semBorda" styleClass="semBorda"
                                       cellpadding="0" cellspacing="0">

                            <rich:dataTable rowKeyVar="index" value="#{diaSemana.itens}" var="item"
                                            styleClass="semBorda" columnClasses="semBorda" width="100%" cellpadding="0" cellspacing="0"
                                            rowClasses="linhaPar, linhaImpar">

                                <h:column>
                                    <h:outputText styleClass="texto-size-14-real texto-cor-cinza-2 texto-font texto-bold" style="font-size: 8pt;font-weight:bold;" value="Prof. #{item.nomeProfessor}"/><br/>
                                    <h:outputText styleClass="texto-size-14-real texto-cor-cinza-2 texto-font texto-bold" style="font-size: 8pt;font-weight:bold;" value="Mod. #{item.modalidade}"/><br/>
                                    <h:outputText styleClass="texto-size-14-real texto-cor-cinza-2 texto-font texto-bold" style="font-size: 8pt;font-weight:bold;" value="#{item.nomeTurma} L#{item.nrMaxApresentar}/V#{item.vagasApresentar} #{item.horarioAtivo? '' : '- Inativo'}" /><br/>
                                    <h:outputText styleClass="texto-size-14-real texto-cor-cinza-2 texto-font texto-bold" style="font-size: 8pt;font-weight:bold;" value="#{item.ambiente}"/><br/>
                                    <h:outputText styleClass="texto-size-14-real texto-cor-cinza-2 texto-font texto-bold" style="text-decoration: underline; font-weight:bold;"  value="#{item.nivel}"/>

                                    <h:dataTable styleClass="semBorda" columnClasses="semBorda"
                                                 value="#{item.alunos}" var="aluno" width="100%" rowClasses="linhaPar" cellpadding="0" >
                                        <h:column>
                                            <h:outputText value="#{aluno.codigo}.- #{aluno.matricula} - #{aluno.nome}"/>
                                        </h:column>
                                    </h:dataTable>

                                    <h:panelGrid width="100%" rendered="#{item.nrMatriculas == 0}" columnClasses="centralizado">
                                        <h:outputText styleClass="texto-size-14-real texto-cor-cinza-2 texto-font texto-bold" style="font-size: 8pt; font-style: italic;" value="Nenhum aluno neste horário."/>
                                    </h:panelGrid>

                                </h:column>
                            </rich:dataTable>

                        </rich:dataGrid>
                    </a4j:repeat>
                    </h:panelGroup>
                </c:if>

            </h:panelGrid>
        </h:panelGrid>
    </h:form>
    <rich:modalPanel id="panelConfirmacao" styleClass="novaModal" width="400">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Confimação"></h:outputText>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText
                        styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                        id="panelConfirmacaoHidelink"/>
                <rich:componentControl for="panelConfirmacao" attachTo="panelConfirmacaoHidelink" operation="hide"
                                       event="onclick"/>
            </h:panelGroup>
        </f:facet>
        <a4j:form id="formPanelConfirmacao">
            <h:panelGroup layout="block">
                <h:panelGroup layout="block" styleClass="container-botoes">
                    <h:outputText styleClass="texto-cor-cinza texto-bold texto-font texto-size-14-real" value="A consulta pode demorar"></h:outputText>
                </h:panelGroup>
                <h:panelGroup layout="block" styleClass="container-botoes">
                    <a4j:commandLink  action="#{ConsultarTurmaControle.consultarTurmasMapa}"
                                      value="Continuar"
                                      oncomplete="Richfaces.hideModalPanel('panelConfirmacao');location.reload();"
                                      styleClass="botaoPrimario texto-size-12-real"
                                      id="confirmacao-continuar"/>
                    <a4j:commandLink  value="Cancelar"
                                      style="margin-left: 10px;"
                                      onclick="Richfaces.hideModalPanel('panelConfirmacao');"
                                      styleClass="botaoSecundario texto-size-12-real"
                                      id="confirmacao-cancelar"/>
                </h:panelGroup>
            </h:panelGroup>
        </a4j:form>
    </rich:modalPanel>

    <rich:modalPanel  id="panelDadosReposicao" width="500"
                      autosized="true"
                      shadowOpacity="true">

        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Dados da Reposição" />
            </h:panelGroup>
        </f:facet>

        <f:facet name="controls">
            <h:panelGroup>
                <a4j:commandButton image="imagens/close.png" style="cursor:pointer"
                                   onclick="#{rich:component('panelDadosReposicao')}.hide();"/>
            </h:panelGroup>
        </f:facet>
        <h:panelGroup id="self">
            <a4j:form>
                <h:panelGrid columnClasses="text,text" columns="2">
                    <h:outputText style="font-weight: bold;" value="Cliente: "/>
                    <h:outputText value="#{ConsultarTurmaControle.consultarTurma.reposicao.cliente.matricula} -
                                  #{ConsultarTurmaControle.consultarTurma.reposicao.cliente.pessoa.nome}"/>

                    <h:outputText style="font-weight: bold;" value="Contrato: "/>
                    <h:outputText value="#{ConsultarTurmaControle.consultarTurma.reposicao.contrato.codigo}"/>

                    <h:outputText style="font-weight: bold;" value="Data Lançamento: "/>
                    <h:outputText value="#{ConsultarTurmaControle.consultarTurma.reposicao.dataLancamento}">
                        <f:convertDateTime pattern="dd/MM/yyyy"/>
                    </h:outputText>
                </h:panelGrid>
                <h:panelGrid columns="3" columnClasses="text,text,text">                   
                    <h:outputText style="font-weight: bold;" value="Aula Desmarcada:"/>
                    <a4j:commandLink actionListener="#{ConsultarTurmaControle.mostrarDadosTurma}"
                                     reRender="self"
                                     style="padding: 5 5 5 5;font-weight:bold;"
                                     title="Clique para visualizar alunos matriculados nesta turma e nesta data"
                                     oncomplete="abrirPopup('./listaAlunosTurma.jsp', 'ConsultarTurmaControle', 780, 595);">
                        <h:outputText value="#{ConsultarTurmaControle.consultarTurma.reposicao.horarioTurmaOrigem.diaSemana_Apresentar} -
                                      #{ConsultarTurmaControle.consultarTurma.reposicao.horarioTurmaOrigem.horaInicial} às #{ConsultarTurmaControle.consultarTurma.reposicao.horarioTurmaOrigem.horaFinal}"/>
                        <f:attribute name="diaSemana" value="#{ConsultarTurmaControle.consultarTurma.reposicao.horarioTurmaOrigem.diaSemana}"/>
                        <f:attribute name="dataBase" value="#{ConsultarTurmaControle.consultarTurma.reposicao.dataOrigem}"/>
                        <f:attribute name="ht" value="#{ConsultarTurmaControle.consultarTurma.reposicao.horarioTurmaOrigem}"/>
                    </a4j:commandLink>
                    <rich:calendar value="#{ConsultarTurmaControle.consultarTurma.reposicao.dataOrigem}"
                                   inputSize="10"
                                   inputClass="form"
                                   oninputblur="blurinput(this);"
                                   oninputfocus="focusinput(this);"
                                   oninputchange="return validar_Data(this.id);"
                                   datePattern="dd/MM/yyyy"
                                   enableManualInput="true"
                                   zindex="2"
                                   showWeeksBar="false" />

                    <h:outputText style="font-weight: bold;" value="Aula Reposição:"/>
                    <a4j:commandLink actionListener="#{ConsultarTurmaControle.mostrarDadosTurma}"
                                     reRender="self"
                                     style="padding: 5 5 5 5;font-weight:bold;"
                                     title="Clique para visualizar alunos matriculados nesta turma e nesta data"
                                     oncomplete="abrirPopup('./listaAlunosTurma.jsp', 'ConsultarTurmaControle', 780, 595);">
                        <h:outputText value="#{ConsultarTurmaControle.consultarTurma.reposicao.horarioTurma.diaSemana_Apresentar} -
                                      #{ConsultarTurmaControle.consultarTurma.reposicao.horarioTurma.horaInicial} às #{ConsultarTurmaControle.consultarTurma.reposicao.horarioTurma.horaFinal}"/>
                        <f:attribute name="diaSemana" value="#{ConsultarTurmaControle.consultarTurma.reposicao.horarioTurma.diaSemana}"/>
                        <f:attribute name="dataBase" value="#{ConsultarTurmaControle.consultarTurma.reposicao.dataReposicao}"/>
                        <f:attribute name="ht" value="#{ConsultarTurmaControle.consultarTurma.reposicao.horarioTurma}"/>
                    </a4j:commandLink>

                    <h:panelGroup>
                        <rich:calendar value="#{ConsultarTurmaControle.consultarTurma.reposicao.dataReposicao}"
                                       inputSize="10"
                                       inputClass="form"
                                       oninputblur="blurinput(this);"
                                       oninputfocus="focusinput(this);"
                                       oninputchange="return validar_Data(this.id);"
                                       datePattern="dd/MM/yyyy"
                                       enableManualInput="true"
                                       zindex="2"
                                       showWeeksBar="false" />
                    </h:panelGroup>



                </h:panelGrid>

                <h:panelGrid columns="4" columnClasses="text,text,text,text">
                    <h:outputText style="font-weight: bold;" value="Turma: "/>
                    <h:outputText value="#{ConsultarTurmaControle.horarioTurma.identificadorTurma}"/>
                    <h:outputText style="font-weight: bold;" value="Professor: "/>
                    <h:outputText value="#{ConsultarTurmaControle.horarioTurma.professor.pessoa.nome}"/>
                </h:panelGrid>

                <a4j:commandButton rendered="#{ConsultarTurmaControle.consultarTurma.reposicao.codigo == 0}" id="btnConsultaTurmaGravarReposicao"
                                   value="Gravar" action="#{ConsultarTurmaControle.doValidarReposicao}"
                                   reRender="form,panelAutorizacaoFuncionalidade,mdlMensagemGenerica"
                                   oncomplete="#{ConsultarTurmaControle.msgAlert};fireElementFromParent('form:btnAtualizaCliente');"/>
            </a4j:form>

            <h:panelGrid id="panelMensagemReposicao" columns="2">
                <h:outputText styleClass="mensagemDetalhada" value="#{ConsultarTurmaControle.mensagemDetalhada}"/>
            </h:panelGrid>

        </h:panelGroup>
        <h:panelGroup layout="block" id="containerFuncMaskData">
            <script>
                carregarMaskInput();
            </script>
        </h:panelGroup>
    </rich:modalPanel>

    <%@include file="includes/cliente/include_modal_reposicoes.jsp" %>
    <%@include file="includes/autorizacao/include_autorizacao_funcionalidade.jsp" %>
    <jsp:include page="includes/include_modal_mensagem_generica.jsp"/>
</f:view>

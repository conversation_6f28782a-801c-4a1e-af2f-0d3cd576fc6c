<%@page contentType="text/html;charset=UTF-8" %>
<head>
    <%@include file="/includes/include_import_minifiles.jsp" %>
</head>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<script type="text/javascript" language="javascript" src="hoverform.js"></script>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax" %>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich" %>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core" %>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<meta name="viewport" content="width=device-width, initial-scale=1.0">
<link href="beta/css/pure-min.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/bootstrap-tabs.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/pure-ext.css" type="text/css" rel="stylesheet"/>
<script src="beta/js/jquery.js" type="text/javascript"></script>
<script src="beta/js/jquery.dataTables.min.js" type="text/javascript"></script>
<script src="beta/js/DT_bootstrap.js" type="text/javascript"></script>
<script src="beta/js/bootstrap-tab.js" type="text/javascript"></script>
<script>
    jQuery.noConflict();
</script>
<style>
    #negrito th {
        font-weight: bold;
        text-align: center;
    }

    #tblPessoa tbody tr td:nth-child(3), #tblPessoa tbody tr td:nth-child(4) {
        text-align: center;
    }
</style>

<%@include file="/includes/verificaModulo.jsp" %>
<c:set var="contexto" value="${pageContext.request.contextPath}" scope="request"/>

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title><h:outputText value="#{msg_aplic.prt_Pessoa_tituloForm}"/></title>

    <%-- INICIO HEADER --%>
    <%--Título  e wiki do header deve ser preenchido antes do include do topo--%>
    <c:set var="titulo" scope="session" value="${msg_aplic.prt_Pessoa_tituloForm}"/>
    <c:set var="urlWiki" scope="session" value="${SuperControle.urlBaseConhecimento}cadastro-de-pessoa/"/>
    <h:panelGroup layout="block" styleClass="pure-g-r">
        <f:facet name="header">
            <c:choose>
                <c:when test="${modulo eq 'centralEventos'}">
                    <jsp:include page="/pages/ce/includes/topoReduzido.jsp"/>
                </c:when>
                <c:otherwise>
                    <jsp:include page="topoReduzido_material.jsp"/>
                </c:otherwise>
            </c:choose>
        </f:facet>
    </h:panelGroup>
    <%-- FIM HEADER --%>

    <h:panelGroup layout="block" styleClass="pure-g-r">
        <h:form id="form" styleClass="pure-form pure-u-1">
            <a4j:keepAlive beanName="ExportadorListaControle"/>
            <input type="hidden" value="${modulo}" name="modulo"/>
            <input id="chavePrimaria" type="hidden" value="" name="chavePrimaria"/>

            <%-- INICIO CONTENT --%>
            <h:panelGroup>

                <%-- INICIO COMANDOS --%>
                <h:panelGroup layout="block" styleClass="pure-g-r pure-u-12-12 margin-0-auto titulo-topo">
                    <h:panelGroup layout="block" styleClass="pure-form pure-form-aligned pure-u-2-3"></h:panelGroup>
                    <h:panelGroup layout="block" styleClass="pure-u-1-3 text-right">
                        <h:panelGroup layout="block" styleClass="controles">
                            <a4j:commandLink id="btnExcel"
                                             styleClass="exportadores"
                                             actionListener="#{PessoaControle.exportar}"
                                             oncomplete="abrirPopup('UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/vnd.ms-excel','Transacoes', 800,200);#{ExportadorListaControle.msgAlert}"
                                             accesskey="3">

                                <f:attribute name="tipo" value="xls"/>
                                <f:attribute name="atributos"
                                             value="codigo=Código,nome=Nome,dataNasc_Apresentar=Data de Nascimento,cfp=CPF,cidade_Apresentar=Cidade"/>
                                <f:attribute name="prefixo" value="Pessoa"/>
                                <a4j:actionparam noEscape="true" name="paramsTableFiltrada" value="retornaDadosDT()"/>
                                <h:outputText title="Exportar para o formato Excel" styleClass="btn-print-2 excel"/>
                            </a4j:commandLink>

                            <a4j:commandLink id="btnPDF"
                                             styleClass="exportadores margin-h-10"
                                             actionListener="#{PessoaControle.exportar}"
                                             oncomplete="abrirPopup('UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/pdf','Transacoes', 800,200);#{ExportadorListaControle.msgAlert}"
                                             accesskey="4">

                                <f:attribute name="tipo" value="pdf"/>
                                <f:attribute name="atributos"
                                             value="codigo=Código,nome=Nome,dataNasc_Apresentar=Data de Nascimento,cfp=CPF,cidade_Apresentar=Cidade"/>
                                <f:attribute name="prefixo" value="Pessoa"/>
                                <f:attribute name="titulo" value="Pessoas"/>
                                <a4j:actionparam noEscape="true" name="paramsTableFiltrada" value="retornaDadosDT()"/>
                                <h:outputText title="Exportar para o formato PDF" styleClass="btn-print-2 pdf"/>
                            </a4j:commandLink>

                            <c:if test="${modulo eq 'centralEventos'}">
                                <a4j:commandLink id="btnLog"
                                                 styleClass="pure-button pure-button-small"
                                                 reRender="formLog"
                                                 actionListener="#{LogControle.entidadeListener}"
                                                 oncomplete="Richfaces.showModalPanel('panelMasterLog');">
                                    <f:attribute name="nomeEntidade" value="PESSOA"/>
                                    <f:attribute name="funcao" value="118"/>

                                    <i class="fa-icon-log"></i> &nbsp Log
                                </a4j:commandLink>
                            </c:if>

                            <a4j:commandLink id="btnNovo"
                                             styleClass="pure-button pure-button-primary"
                                             action="#{PessoaSimplificadoControle.novoPessoaSimplificado}"
                                             accesskey="1">
                                &nbsp ${msg_bt.btn_cadastrar_novo}
                            </a4j:commandLink>
                        </h:panelGroup>
                    </h:panelGroup>
                </h:panelGroup>
                <%-- FIM COMANDOS --%>

                <table id="tblPessoa" class="tabelaPessoa pure-g-r pure-u-11-12 margin-0-auto">
                    <thead id="negrito">
                    <th>${msg_aplic.prt_Cadastro_label_codigo_maiusculo}</th>
                    <th>${msg_aplic.prt_Cadastro_label_nome_maiusculo}</th>
                    <th>${msg_aplic.prt_Cadastro_label_nascimento_maiusculo}</th>
                    <th>${ClienteControle.displayIdentificadorFront[0]}</th>
                    <th>${msg_aplic.prt_Cadastro_label_cidade_maiusculo}</th>
                    </thead>
                    <tbody></tbody>
                </table>

                <a4j:jsFunction name="jsEditar" action="#{PessoaSimplificadoControle.editar}" reRender="mensagem"/>

            </h:panelGroup>
            <%-- FIM CONTENT --%>

            <%-- INICIO FOOTER --%>
            <h:panelGroup id="mensagem" layout="block" styleClass="pure-u-11-12 margin-0-auto">
                <h:graphicImage id="iconSucesso" rendered="#{PessoaControle.sucesso}" value="./imagens/sucesso.png"/>
                <h:graphicImage id="iconErro" rendered="#{PessoaControle.erro}" value="./imagens/erro.png"/>

                <h:outputText styleClass="mensagem" rendered="#{not empty PessoaControle.mensagem}"
                              value=" #{PessoaControle.mensagem}"/>
                <h:outputText styleClass="mensagemDetalhada" rendered="#{not empty PessoaControle.mensagemDetalhada}"
                              value=" #{PessoaControle.mensagemDetalhada}"/>
            </h:panelGroup>
            <%-- FIM FOOTER --%>
        </h:form>

        <rich:modalPanel id="panelStatus" autosized="true">
            <h:panelGrid columns="2" styleClass="titulo3" columnClasses="titulo3">
                <h:graphicImage url="./imagens/carregando.gif" style="border:none"/>
                <h:outputText styleClass="titulo3" value="Carregando..."/>
            </h:panelGrid>
        </rich:modalPanel>


        <%@include file="/pages/ce/includes/include_modal_exibeLogEntidade.jsp" %>
    </h:panelGroup>

    <script src="beta/js/ext-funcs.js" type="text/javascript"></script>

    <script>
        jQuery(window).on("load", function () {
            iniciarTabela("tabelaPessoa", "${contexto}/prest/basico/pessoa", 1, "asc", 2, true);
        });
    </script>
</f:view>

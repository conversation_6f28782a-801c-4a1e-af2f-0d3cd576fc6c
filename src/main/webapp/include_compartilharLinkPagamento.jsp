<%@page pageEncoding="ISO-8859-1" %>
<%@include file="/includes/imports.jsp" %>

    <rich:modalPanel id="modalCompartilharPagamento"  styleClass="novaModal noMargin" shadowOpacity="true"
                     width="650" autosized="true" showWhenRendered="#{TelaClienteControle.compartilharLink}">
        <f:facet name="header">
            <h:panelGroup layout="block" id="panelSupeCompartilharPagamento">
                <h:outputText rendered="#{TelaClienteControle.cadastrarCartaoOnline}" value="Compartilhando link de cadastar cartão"/>
                <h:outputText rendered="#{!TelaClienteControle.cadastrarCartaoOnline}" value="Compartilhando link de pagamento"/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText
                        styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign "
                        id="hdmodalCompartilharPagamento"/>
                <rich:componentControl for="modalCompartilharPagamento" attachTo="hdmodalCompartilharPagamento" operation="hide"
                                       event="onclick"/>
            </h:panelGroup>
        </f:facet>
        <a4j:form ajaxSubmit="true">

            <style>
                .btncompartilhar a i{
                    font-size: 65px;
                    color: #0090FF;
                }
                .btncompartilhar:hover{
                    background-color: #dddddd;
                }
                .btncompartilhar{
                    display: inline-block;
                    width: 32%;
                    padding: 20px 0;
                    text-align: center;
                    vertical-align: top;
                }
                .btncompartilhar a:hover{
                    text-decoration: none;
                }
                .textolink{
                    font-size: 14px;
                    margin-top: 20px;
                    color: #474747;
                }

            </style>

            <h:panelGroup id="painelCompartilhar" layout="block" style="margin: 20px 10px; text-align: center">
                <h2>Como deseja compartilhar o link?</h2>

                <h:panelGroup layout="block" styleClass="btncompartilhar" rendered="#{not empty TelaClienteControle.cliente.pessoa.telefoneVOs}">
                    <a4j:commandLink styleClass="toolpister" title="Clique para enviar o link via Whatsapp"
                                     action="#{TelaClienteControle.linkCompartilharWhatsapp}"
                                     oncomplete="#{TelaClienteControle.msgAlert}">
                        <img src="imagens/whatsapp-logo.png"
                    </a4j:commandLink>

                    <div class="textolink">WhatsApp</div>
                </h:panelGroup>

                
                <div class="btncompartilhar">
                    <a4j:commandLink styleClass="toolpister" title="Clique para copiar para a área de trabalho"
                                     action="#{TelaClienteControle.linkCompartilhar}"
                                     oncomplete="#{TelaClienteControle.msgAlert}">
                        <i class="fa-icon-copy"></i>
                    </a4j:commandLink>
                    <div class="textolink">Copiar</div>
                </div>

                <h:panelGroup layout="block" style="margin-top: 20px" styleClass="container-botoes">
                    <a4j:commandLink id="consultar"
                                     status="false"
                                     oncomplete="Richfaces.hideModalPanel('modalCompartilharPagamento')"
                                     value="Ok"
                                     styleClass="botaoPrimario texto-size-14-real"/>
                </h:panelGroup>

            </h:panelGroup>
        </a4j:form>
    </rich:modalPanel>

<script>

    function copiar(copyText) {
        var el = document.createElement('textarea');
        el.value = copyText;
        document.body.appendChild(el);
        el.select();
        document.execCommand('copy');
        document.body.removeChild(el);
        Notifier.info('Link gerado com sucesso e copiado para a área de transferência! O link expira em 5 dias.');
    }
</script>

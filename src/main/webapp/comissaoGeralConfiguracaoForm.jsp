<%@page contentType="text/html" pageEncoding="UTF-8" %>
<head>
    <%@include file="/includes/include_import_minifiles.jsp" %>
</head>
<%@include file="include_imports.jsp" %>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<link href="${root}/css/otimize.css" rel="stylesheet" type="text/css">
<link href="${root}/css/ce.css" rel="stylesheet" type="text/css">
<link href="${root}/beta/css/font-awesome_2.0.min.css" rel="stylesheet" type="text/css">
<script type="text/javascript" language="javascript" src="hoverform.js"></script>
<script type="text/javascript" language="javascript">var contexto = '${root}';</script>
<script type="text/javascript" language="javascript" src="${root}/script/ajuda.js"></script>
<%@ taglib prefix="f" uri="http://java.sun.com/jsf/core" %>
<%@ taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich" %>
<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>
<f:loadBundle var="CElabels" basename="propriedades.CElabels"/>

<%@include file="includes/verificaModulo.jsp" %>
<c:set var="contexto" value="${pageContext.request.contextPath}" scope="request"/>

<f:view locale="#{SuperControle.idioma}">

    <title>
        <h:outputText value="Taxas de Comissão"/>
    </title>

    <%--&lt;%&ndash; INICIO HEADER &ndash;%&gt;--%>
    <%--<c:set var="titulo" scope="session" value="${msg_aplic.prt_TaxaComissao_tituloForm}"/>--%>
    <%--<c:set var="urlWiki" scope="session" value="${SuperControle.urlWiki}Clique e saiba mais:Taxas_Comissao"/>--%>
    <f:facet name="header">
        <jsp:include page="topoReduzido_material.jsp"/>
    </f:facet>
    <%-- FIM HEADER --%>

    <a4j:loadScript src="/script/jquery.maskedinput-1.2.2.js" />
    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <h:form id="form">
            <hr style="border-color: #e6e6e6;"/>
            <input type="hidden" value="${modulo}" name="modulo"/>

            <h:panelGrid columns="1" width="100%">
                <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar" columnClasses="classEsquerda, classDireita"
                             width="100%">

                    <h:outputText value="Empresa"/>
                    <h:selectOneMenu id="empresa" onfocus="focusinput(this);" styleClass="form"
                                     value="#{ComissaoGeralConfiguracaoControle.comissaoVO.empresa.codigo}">
                        <f:selectItems value="#{ComissaoGeralConfiguracaoControle.listaEmpresas}"/>

                        <a4j:support event="onchange" reRender="form" action="#{ComissaoGeralConfiguracaoControle.verificarComissaoMeta}"/>
                    </h:selectOneMenu>

                    <h:outputText value="Situação"/>
                    <h:selectOneMenu id="situacao" onfocus="focusinput(this);" styleClass="form"
                                     value="#{ComissaoGeralConfiguracaoControle.comissaoVO.situacao}"
                                     rendered="#{!ComissaoGeralConfiguracaoControle.comissaoVO.novoObj}">
                        <f:selectItem itemValue="" itemLabel="--Selecione--"/>
                        <f:selectItem itemValue="MA" itemLabel="Matrícula"/>
                        <f:selectItem itemValue="RE" itemLabel="Rematrícula"/>
                        <f:selectItem itemValue="RN" itemLabel="Renovação"/>
                    </h:selectOneMenu>

                    <h:selectManyCheckbox id="situacaoCadastrar" onfocus="focusinput(this);"
                                          styleClass="tituloCampos"
                                          value="#{ComissaoGeralConfiguracaoControle.situacaoCadastrar}"
                                          rendered="#{ComissaoGeralConfiguracaoControle.comissaoVO.novoObj}">
                        <f:selectItem itemValue="MA" itemLabel="Matrícula"/>
                        <f:selectItem itemValue="RE" itemLabel="#{msg_aplic.prt_TaxaComissao_rematricula}"/>
                        <f:selectItem itemValue="RN" itemLabel="Renovação"/>
                    </h:selectManyCheckbox>

                    <h:outputText value="Vigência Inicial"/>
                    <h:panelGroup>
                        <rich:calendar id="vigenciainicio"
                                       value="#{ComissaoGeralConfiguracaoControle.comissaoVO.vigenciaInicio}"
                                       enableManualInput="true"
                                       popup="true"
                                       inputSize="7"
                                       datePattern="MM/yyyy"
                                       showApplyButton="false"
                                       style="width:200px"
                                       inputClass="MMyyyy"
                                       showFooter="false"/>
                        <h:message for="vigenciainicio" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>

                    <h:outputText value="Vigência Final"/>
                    <h:panelGroup>
                        <rich:calendar id="vigenciafinal"
                                       value="#{ComissaoGeralConfiguracaoControle.comissaoVO.vigenciaFinal}"
                                       enableManualInput="true"
                                       popup="true"
                                       inputSize="7"
                                       datePattern="MM/yyyy"
                                       showApplyButton="false"
                                       style="width:200px"
                                       inputClass="MMyyyy"
                                       showFooter="false"/>
                        <h:message for="vigenciafinal" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>
                    <h:outputText value="Duração"/>
                    <h:panelGroup id="pnlDuracao">
                        <h:inputText id="duracao" size="2" maxlength="2" onblur="blurinput(this);"
                                     onfocus="focusinput(this);" styleClass="form"
                                     rendered="#{!ComissaoGeralConfiguracaoControle.comissaoVO.novoObj}"
                                     value="#{ComissaoGeralConfiguracaoControle.comissaoVO.duracao}"/>

                        <rich:dataGrid id="dtgDuracao" value="#{ComissaoGeralConfiguracaoControle.duracoesPossiveis}"
                                       style="background: none" width="100%" columns="5" columnClasses="semBorda"
                                       styleClass="semBorda" cellpadding="0" cellspacing="0" var="contratoDuracao"
                                       rendered="#{ComissaoGeralConfiguracaoControle.comissaoVO.novoObj and !ComissaoGeralConfiguracaoControle.outraDuracao}">
                            <h:panelGroup>
                                <h:selectBooleanCheckbox id="slctModalide" value="#{contratoDuracao.selecionado}"/>
                                <rich:spacer width="5"/>
                                <h:outputText styleClass="titulo3" value="#{contratoDuracao.numeroMeses}"/>
                            </h:panelGroup>
                        </rich:dataGrid>

                        <h:message for="duracao" styleClass="mensagemDetalhada"/>

                        <h:panelGroup style="margin-left: 4px;" rendered="#{ComissaoGeralConfiguracaoControle.comissaoVO.novoObj}">
                            <h:selectBooleanCheckbox style="margin-top: 6px" id="slcOutraDuracao" value="#{ComissaoGeralConfiguracaoControle.outraDuracao}">
                                <a4j:support event="onchange" reRender="pnlDuracao"/>
                            </h:selectBooleanCheckbox>
                            <rich:spacer width="5"/>

                            <h:outputText styleClass="titulo3" value="#{msg_aplic.prt_TaxaComissao_outraDuracao}"/>
                        </h:panelGroup>

                        <h:inputText id="outraDuracao" size="2" maxlength="2" onblur="blurinput(this);"
                                     onfocus="focusinput(this);" styleClass="form" style="margin-left: 5px"
                                     rendered="#{ComissaoGeralConfiguracaoControle.outraDuracao and ComissaoGeralConfiguracaoControle.comissaoVO.novoObj}"
                                     value="#{ComissaoGeralConfiguracaoControle.comissaoVO.duracao}"/>
                    </h:panelGroup>
                    <c:if test="${!ComissaoGeralConfiguracaoControle.mostrarComissaoPorMeta}">
                      <h:outputText  value="Valor Fixo Espontâneo"/>
                    </c:if>

                    <c:if test="${!ComissaoGeralConfiguracaoControle.mostrarComissaoPorMeta}">
                        <h:panelGroup>
                            <h:inputText id="fixoEsp" size="5" maxlength="5" onblur="blurinput(this);"
                                         onfocus="focusinput(this);" styleClass="form"
                                         onkeypress="return formatar_moeda(this,'.',',',event);"
                                         value="#{ComissaoGeralConfiguracaoControle.comissaoVO.valorFixoEspontaneo}">
                                <f:converter converterId="FormatadorNumerico" />
                            </h:inputText>
                            <h:message for="fixoEsp" styleClass="mensagemDetalhada"/>
                        </h:panelGroup>
                    </c:if>

                    <c:if test="${!ComissaoGeralConfiguracaoControle.mostrarComissaoPorMeta}">
                      <h:outputText value="Valor Fixo Agendado"/>
                    </c:if>
                    <c:if test="${!ComissaoGeralConfiguracaoControle.mostrarComissaoPorMeta}">
                        <h:panelGroup>
                            <h:inputText id="fixoAg" size="5" maxlength="5" onblur="blurinput(this);"
                                         onfocus="focusinput(this);" styleClass="form"
                                         onkeypress="return formatar_moeda(this,'.',',',event);"
                                         value="#{ComissaoGeralConfiguracaoControle.comissaoVO.valorFixoAgendado}">
                                <f:converter converterId="FormatadorNumerico" />
                            </h:inputText>
                            <h:message for="fixoAg" styleClass="mensagemDetalhada"/>
                        </h:panelGroup>
                    </c:if>
                    <c:if test="${!ComissaoGeralConfiguracaoControle.mostrarComissaoPorMeta}">
                      <h:outputText value="Porcentagem Espontâneo"/>
                    </c:if>
                    <c:if test="${!ComissaoGeralConfiguracaoControle.mostrarComissaoPorMeta}">
                        <h:panelGroup>
                            <h:inputText id="percEsp" size="5" maxlength="5" onblur="blurinput(this);"
                                         onfocus="focusinput(this);" styleClass="form"
                                         onkeypress="return formatar_moeda(this,'.',',',event);"
                                         value="#{ComissaoGeralConfiguracaoControle.comissaoVO.porcentagemEspontaneo}">
                                   <f:converter converterId="FormatadorNumerico" />
                            </h:inputText>
                            <h:message for="percEsp" styleClass="mensagemDetalhada"/>
                        </h:panelGroup>
                    </c:if>
                    <c:if test="${!ComissaoGeralConfiguracaoControle.mostrarComissaoPorMeta}">
                      <h:outputText value="Porcentagem Agendado"/>
                    </c:if>
                    <c:if test="${!ComissaoGeralConfiguracaoControle.mostrarComissaoPorMeta}">
                        <h:panelGroup>
                            <h:inputText id="percAg" size="5" maxlength="5" onblur="blurinput(this);"
                                         onfocus="focusinput(this);" styleClass="form"
                                         onkeypress="return formatar_moeda(this,'.',',',event);"
                                         value="#{ComissaoGeralConfiguracaoControle.comissaoVO.porcentagemAgendado}">
                                <f:converter converterId="FormatadorNumerico" />
                            </h:inputText>
                            <h:message for="percAg" styleClass="mensagemDetalhada"/>
                        </h:panelGroup>
                    </c:if>
                </h:panelGrid>

                <h:panelGroup id="pgComissaoTaxa">
                    <h:panelGrid rendered="#{ComissaoGeralConfiguracaoControle.mostrarComissaoPorMeta}"  width="100%" headerClass="subordinado" columnClasses="colunaCentralizada" style="margin-right:15px;background-color:#B5B5B5;" >
                        <h:panelGroup>
                            <h:outputText style="font-size:12px;font-weight:bold;margin-right:5px;" value="Configuração de comissão por atingimento de meta"/>
                        </h:panelGroup>
                    </h:panelGrid>
                    <rich:dataGrid value="#{ComissaoGeralConfiguracaoControle.comissaoVO.listaComissaoMeta}"
                                   rendered="#{ComissaoGeralConfiguracaoControle.mostrarComissaoPorMeta}"
                                   var="comissaoMeta"
                                   styleClass="semBorda"
                                   columnClasses="semBorda"
                                   style="background: #e6e6e6;"
                                   width="100%"
                                   cellpadding="0"
                                   cellspacing="0">

                         <h:panelGrid columns="5" rowClasses="linhaImpar, linhaPar"  width="100%">
                            <h:outputText value="#{comissaoMeta.nomeMeta}"/>
                            <h:panelGroup>
                                <h:outputText value="R$ Espontâneo:"/>
                                <h:inputText size="5" maxlength="5" onblur="blurinput(this);"
                                             onkeypress="return formatar_moeda(this,'.',',',event);"
                                             onfocus="focusinput(this);" styleClass="form"
                                             value="#{comissaoMeta.valorEspontaneo}">
                                    <f:converter converterId="FormatadorNumerico" />
                                </h:inputText>
                            </h:panelGroup>
                            <h:panelGroup>
                                <h:outputText value="R$ Agendado:"/>
                                <h:inputText size="5" maxlength="5" onblur="blurinput(this);"
                                             onkeypress="return formatar_moeda(this,'.',',',event);"
                                             onfocus="focusinput(this);" styleClass="form"
                                             value="#{comissaoMeta.valorAgendado}">
                                    <f:converter converterId="FormatadorNumerico" />
                                </h:inputText>

                            </h:panelGroup>
                             <h:panelGroup>
                                 <h:outputText value="% Espontâneo:"/>
                                 <h:inputText size="5" maxlength="5" onblur="blurinput(this);"
                                              onkeypress="return formatar_moeda(this,'.',',',event);"
                                              onfocus="focusinput(this);" styleClass="form"
                                              value="#{comissaoMeta.porcentagemEspontaneo}">
                                     <f:converter converterId="FormatadorNumerico" />
                                 </h:inputText>
                             </h:panelGroup>

                            <h:panelGroup>
                                <h:outputText value="% Agendado:"/>
                                <h:inputText size="5" maxlength="5" onblur="blurinput(this);"
                                             onkeypress="return formatar_moeda(this,'.',',',event);"
                                             onfocus="focusinput(this);" styleClass="form"
                                             value="#{comissaoMeta.porcentagemAgendado}">
                                    <f:converter converterId="FormatadorNumerico" />
                                </h:inputText>
                            </h:panelGroup>
                             </h:panelGrid>
                    </rich:dataGrid>

                </h:panelGroup>

                <h:panelGrid columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid id="panelMensagemErro" columns="3" width="100%" styleClass="tabMensagens">
                        <h:panelGrid columns="1" width="100%">
                            <f:verbatim>
                                <h:outputText value=" "/>
                            </f:verbatim>
                        </h:panelGrid>
                        <a4j:commandButton id="icSuc" rendered="#{ComissaoGeralConfiguracaoControle.sucesso}"
                                         image="./imagens/sucesso.png"/>
                        <a4j:commandButton id="icFal" rendered="#{ComissaoGeralConfiguracaoControle.erro}"
                                         image="./imagens/erro.png"/>
                        <h:panelGrid columns="1" width="100%">
                            <h:outputText id="msg" styleClass="mensagem"
                                          value="#{ComissaoGeralConfiguracaoControle.mensagem}"/>
                            <h:outputText id="msgDetalhada" styleClass="mensagemDetalhada"
                                          value="#{ComissaoGeralConfiguracaoControle.mensagemDetalhada}"/>
                        </h:panelGrid>
                    </h:panelGrid>
                    <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                        <h:panelGroup>
                            <a4j:commandButton id="novo" immediate="true"
                                             action="#{ComissaoGeralConfiguracaoControle.novo}"
                                             value="#{msg_bt.btn_novo}"
                                             alt="#{msg.msg_novo_dados}" accesskey="1" styleClass="botoes nvoBt btSec"/>
                            <f:verbatim>
                                <h:outputText value="    "/>
                            </f:verbatim>
                            <a4j:commandButton id="salvar" action="#{ComissaoGeralConfiguracaoControle.gravar}"
                                             value="#{msg_bt.btn_gravar}"
                                             alt="#{msg.msg_gravar_dados}" accesskey="2" styleClass="botoes nvoBt"/>

                            <f:verbatim>
                                <h:outputText value="    "/>
                            </f:verbatim>
                            <h:panelGroup id="grupoBtnExcluir">
                                <a4j:commandButton id="excluir"  reRender="mdlMensagemGenerica"
                                                   oncomplete="#{ComissaoGeralConfiguracaoControle.msgAlert}" action="#{ComissaoGeralConfiguracaoControle.confirmarExcluir}"
                                                   value="#{msg_bt.btn_excluir}" title="#{msg.msg_excluir_dados}" accesskey="3" styleClass="botoes nvoBt btSec btPerigo"/>
                            </h:panelGroup>

                            <f:verbatim>
                                <h:outputText value="    "/>
                            </f:verbatim>
                            <a4j:commandButton id="consultar" immediate="true" action="consultar"
                                             value="#{msg_bt.btn_voltar_lista}"
                                             alt="#{msg.msg_consultar_dados}" accesskey="4" styleClass="botoes nvoBt btSec"/>
                            <f:verbatim>
                                <h:outputText value="    "/>
                            </f:verbatim>
                             <a4j:commandLink action="#{ComissaoGeralConfiguracaoControle.realizarConsultaLogObjetoSelecionado}"
                                               reRender="form"
                                               oncomplete="abrirPopup('visualizadorLogForm.jsp', 'VisualizadorLog', 785, 595);fireElementFromAnyParent('form:btnAtualizaTempo')"
                                               title="Visualizar Log" styleClass="botoes nvoBt btSec" style="display: inline-block;padding-bottom: 10px;">
                                <i class="fa-icon-list"></i>
                            </a4j:commandLink>
                        </h:panelGroup>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
            <rich:jQuery id="mskData" selector=".rich-calendar-input:not(.MMyyyy)" timing="onload" query="mask('99/99/9999')" />
            <rich:jQuery selector=".rich-calendar-input.MMyyyy" timing="onload" query="mask('99/9999')" />
        </h:form>
    </h:panelGrid>
    <jsp:include page="includes/include_carregando_ripple.jsp"/>
    <%@include file="includes/include_modal_mensagem_generica.jsp"%>
</f:view>

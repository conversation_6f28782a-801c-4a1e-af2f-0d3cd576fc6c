<%--
    Document   : indiceConversaoVenda
    Author     : <PERSON><PERSON><PERSON>
--%>
<%@page contentType="text/html"%>
<%@page pageEncoding="ISO-8859-1"%>
<head><%@include file="/includes/include_import_minifiles.jsp" %></head>
<script type="text/javascript" language="javascript" src="../hoverform.js"></script>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<link href="./beta/css/font-awesome_2.0.min.css" rel="stylesheet" type="text/css">
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText value="Resumo de Clientes (Lista Risco Churn)"/>
    </title>
    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <html>
            <body onload="fireElement('form:botaoAtualizarPagina')"/>
            <h:form id="form" >
                <c:set var="titulo" scope="session" value="Grupo de risco : Itens ${IndiceConversaoVendaRelControle.qtdeLista}"/>
                <c:set var="urlWiki" scope="session" value="${SuperControle.urlBaseConhecimento}bi-conversao-de-vendas-adm/"/>
                <h:panelGroup layout="block" styleClass="pure-g-r">
                    <f:facet name="header">
                        <jsp:include page="topo_reduzido_popUp.jsp"/>
                    </f:facet>
                </h:panelGroup>
                <a4j:commandButton id="botaoAtualizarPagina" reRender="form" style="display:none"/>
                <h:panelGroup layout="block" style="display: inline-table;width: 100%;height: 100vh" styleClass="fundoBranco">

                    <h:panelGroup layout="block" styleClass="caixaCorpo">
                        <h:panelGroup layout="block" style="height: 80%;width: 100%">
                            <h:panelGroup layout="block" styleClass="caixaMenuLatel">
                                <h:panelGroup layout="block" >
                                    <h:panelGroup layout="block" styleClass="margin-box">
                                        <h:panelGrid columns="1" width="100%" rendered="#{IndiceConversaoVendaRelControle.mostraLista}">

                                            <h:panelGrid width="100%" style="text-align: right">
                                                <h:panelGroup layout="block">
                                                    <a4j:commandLink id="exportarExcel"
                                                                       style="margin-left: 8px;"
                                                                       actionListener="#{ExportadorListaControle.exportar}"
                                                                       rendered="#{not empty IndiceConversaoVendaRelControle.listaQuestionarios}"
                                                                       oncomplete="abrirPopup('UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/vnd.ms-excel','Transacoes', 800,200);#{ExportadorListaControle.msgAlert}"
                                                                       accesskey="2" styleClass="linkPadrao">
                                                        <f:attribute name="lista" value="#{IndiceConversaoVendaRelControle.listaQuestionarios}"/>
                                                        <f:attribute name="tipo" value="xls"/>
                                                        <f:attribute name="atributos" value="matriculaApresentar=Matrícula,nomeApresentar=Nome,situacaoApresentar=Situação,data_Apresentar=Data
                                             ,bvApresentar=BV,tipoBvApresentar=Tipo BV,consultorApresentar=Consultor"/>
                                                        <f:attribute name="prefixo" value="IndiceConversaoVendas"/>
                                                        <h:outputText title="Exportar para o formato Excel" styleClass="btn-print-2 excel"/>
                                                    </a4j:commandLink>
                                                    <%--BOTÃO PDF--%>
                                                    <a4j:commandLink id="exportarPdf"
                                                                       style="margin-left: 8px;"
                                                                       actionListener="#{ExportadorListaControle.exportar}"
                                                                       rendered="#{not empty IndiceConversaoVendaRelControle.listaQuestionarios}"
                                                                       oncomplete="abrirPopup('UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/pdf','Transacoes', 800,200);#{ExportadorListaControle.msgAlert}"
                                                                       accesskey="2" styleClass="linkPadrao">
                                                        <f:attribute name="lista" value="#{IndiceConversaoVendaRelControle.listaQuestionarios}"/>
                                                        <f:attribute name="tipo" value="pdf"/>
                                                        <f:attribute name="atributos" value="matriculaApresentar=Matrícula,nomeApresentar=Nome,situacaoApresentar=Situação,data_Apresentar=Data
                                             ,bvApresentar=BV,tipoBvApresentar=Tipo BV,consultorApresentar=Consultor"/>
                                                        <f:attribute name="prefixo" value="IndiceConversaoVendas"/>
                                                        <h:outputText title="Exportar para o formato PDF" styleClass="btn-print-2 pdf"/>
                                                    </a4j:commandLink>
                                                </h:panelGroup>
                                            </h:panelGrid>
                                            <rich:dataTable width="100%" styleClass="tabelaSimplesCustom" id="tabelaQuestionario"
                                                            value="#{IndiceConversaoVendaRelControle.listaQuestionarios}" var="resumoPessoa" rowKeyVar="status">
                                                <%@include file="/pages/ce/includes/include_contador_richtable.jsp" %>
                                                <rich:column sortBy="#{resumoPessoa.cliente.matricula}" filterEvent="onkeyup" headerClass="col-text-align-left" styleClass="col-text-align-left">
                                                    <f:facet name="header">
                                                        <h:outputText  styleClass="texto-upper texto-font texto-size-14-real texto-cor-cinza texto-bold"  value="Matrícula" />
                                                    </f:facet>
                                                    <h:outputText  styleClass="texto-font texto-size-14-real texto-cor-cinza"  value="#{resumoPessoa.cliente.matricula}" />
                                                </rich:column>
                                                <rich:column sortBy="#{resumoPessoa.cliente.pessoa.nome}" filterEvent="onkeyup" headerClass="col-text-align-left" styleClass="col-text-align-left">
                                                    <f:facet name="header">
                                                        <h:outputText  styleClass="texto-upper texto-font texto-size-14-real texto-cor-cinza texto-bold"  value="Nome" />
                                                    </f:facet>
                                                    <h:outputText  styleClass="texto-font texto-size-14-real texto-cor-cinza"  value="#{resumoPessoa.cliente.pessoa.nome}" />
                                                </rich:column>
                                                <rich:column sortBy="#{resumoPessoa.cliente.situacao}" headerClass="col-text-align-left" styleClass="col-text-align-left" filterEvent="onkeyup">
                                                    <f:facet name="header">
                                                        <h:outputText  styleClass="texto-upper texto-font texto-size-14-real texto-cor-cinza texto-bold"  value="Situação"/>
                                                    </f:facet>
                                                    <h:outputText  styleClass="texto-font texto-size-14-real texto-cor-cinza"  value="#{resumoPessoa.cliente.situacao_Apresentar}" />
                                                </rich:column>
                                                <rich:column sortBy="#{resumoPessoa.data_Apresentar}" headerClass="col-text-align-left" styleClass="col-text-align-left" filterEvent="onkeyup">
                                                    <f:facet name="header">
                                                        <h:outputText styleClass="texto-upper texto-font texto-size-14-real texto-cor-cinza texto-bold"   value="Data"/>
                                                    </f:facet>
                                                    <h:outputText  styleClass="texto-font texto-size-14-real texto-cor-cinza"  value="#{resumoPessoa.data_Apresentar}" />
                                                </rich:column>
                                                <rich:column sortBy="#{resumoPessoa.questionario.nomeInterno}" headerClass="col-text-align-left" styleClass="col-text-align-left" filterEvent="onkeyup">
                                                    <f:facet name="header">
                                                        <h:outputText styleClass="texto-upper texto-font texto-size-14-real texto-cor-cinza texto-bold"   value="BV"/>
                                                    </f:facet>
                                                    <h:outputText  styleClass="texto-font texto-size-14-real texto-cor-cinza"  value="#{resumoPessoa.questionario.nomeInterno}" />
                                                </rich:column>
                                                <rich:column sortBy="#{resumoPessoa.tipoBV.descricao}" headerClass="col-text-align-left" styleClass="col-text-align-left" filterEvent="onkeyup">
                                                    <f:facet name="header">
                                                        <h:outputText  styleClass="texto-upper texto-font texto-size-14-real texto-cor-cinza texto-bold"  value="Tipo BV"/>
                                                    </f:facet>
                                                    <h:outputText  styleClass="texto-font texto-size-14-real texto-cor-cinza"  value="#{resumoPessoa.tipoBV.descricao}" />
                                                </rich:column>
                                                <rich:column sortBy="#{resumoPessoa.consultor.pessoa.nome}" headerClass="col-text-align-left" styleClass="col-text-align-left" filterEvent="onkeyup">
                                                    <f:facet name="header">
                                                        <h:outputText  styleClass="texto-upper texto-font texto-size-14-real texto-cor-cinza texto-bold"  value="Consultor"/>
                                                    </f:facet>
                                                    <h:outputText  styleClass="texto-font texto-size-14-real texto-cor-cinza"  value="#{resumoPessoa.consultor.pessoa.nome}" />
                                                </rich:column>
                                                <rich:column  headerClass="col-text-align-center" styleClass="col-text-align-center">
                                                    <a4j:commandLink styleClass="linkPadrao  texto-size-14-real "
                                                                     action="#{IndiceConversaoVendaRelControle.irParaTelaCliente}"
                                                                     oncomplete="abrirPopup('clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);">
                                                        <f:param name="state" value="AC"/>
                                                        <i class="fa-icon-search"></i>
                                                    </a4j:commandLink>
                                                </rich:column>
                                            </rich:dataTable>
                                        </h:panelGrid>

                                        <h:panelGrid columns="1" width="100%" rendered="#{!IndiceConversaoVendaRelControle.mostraLista}">
                                           <h:panelGrid width="100%" style="text-align: right">
                                                <h:panelGroup layout="block">
                                                    <a4j:commandLink id="exportarExcell"
                                                                       style="margin-left: 8px;"
                                                                       actionListener="#{ExportadorListaControle.exportar}"
                                                                       rendered="#{not empty IndiceConversaoVendaRelControle.listaClientes}"
                                                                       oncomplete="abrirPopup('UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/vnd.ms-excel','Transacoes', 800,200);#{ExportadorListaControle.msgAlert}"
                                                                       accesskey="2" styleClass="linkPadrao">
                                                        <f:attribute name="lista" value="#{IndiceConversaoVendaRelControle.listaClientes}"/>
                                                        <f:attribute name="tipo" value="xls"/>
                                                        <f:attribute name="atributos" value="matriculaApresentar=Matrícula,nomeApresentar=Nome,situacaoApresentar=Situação,contrato=Tipo do Contrato
                                             ,duracao=Duração do Plano,quantidadeDiasParaRenovar=Dias Vencido,consultorApresentar=Consultor"/>
                                                        <f:attribute name="prefixo" value="IndiceConversaoVendas"/>
                                                        <h:outputText title="Exportar para o formato Excel" styleClass="btn-print-2 excel"/>
                                                    </a4j:commandLink>
                                                    <%--BOTÃO PDF--%>
                                                    <a4j:commandLink id="exportarPdff"
                                                                       style="margin-left: 8px;"
                                                                       actionListener="#{ExportadorListaControle.exportar}"
                                                                       rendered="#{not empty IndiceConversaoVendaRelControle.listaClientes}"
                                                                       oncomplete="abrirPopup('UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/pdf','Transacoes', 800,200);#{ExportadorListaControle.msgAlert}"
                                                                       accesskey="2" styleClass="linkPadrao">
                                                        <f:attribute name="lista" value="#{IndiceConversaoVendaRelControle.listaClientes}"/>
                                                        <f:attribute name="tipo" value="pdf"/>
                                                        <f:attribute name="atributos" value="matriculaApresentar=Matrícula,nomeApresentar=Nome,situacaoApresentar=Situação,contrato=Tipo do Contrato
                                             ,duracao=Duração do Plano,quantidadeDiasParaRenovar=Dias Vencido,consultorApresentar=Consultor"/>
                                                        <f:attribute name="prefixo" value="IndiceConversaoVendas"/>
                                                        <h:outputText title="Exportar para o formato PDF" styleClass="btn-print-2 pdf"/>
                                                    </a4j:commandLink>
                                                </h:panelGroup>
                                            </h:panelGrid>
                                            <rich:dataTable width="100%" styleClass="tabelaSimplesCustom" id="tabelaCliente"
                                                            value="#{IndiceConversaoVendaRelControle.listaClientes}" var="resumoPessoa" rowKeyVar="status">
                                                <%@include file="/pages/ce/includes/include_contador_richtable.jsp" %>
                                                <rich:column sortBy="#{resumoPessoa.cliente.matricula}" headerClass="col-text-align-left" styleClass="col-text-align-left" filterEvent="onkeyup">
                                                    <f:facet name="header">
                                                        <h:outputText styleClass="texto-upper texto-font texto-size-14-real texto-cor-cinza texto-bold" value="Matrícula" />
                                                    </f:facet>
                                                    <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza" value="#{resumoPessoa.cliente.matricula}" />
                                                </rich:column>
                                                <rich:column sortBy="#{resumoPessoa.cliente.pessoa.nome}" headerClass="col-text-align-left" styleClass="col-text-align-left" filterEvent="onkeyup">
                                                    <f:facet name="header">
                                                        <h:outputText styleClass="texto-upper texto-font texto-size-14-real texto-cor-cinza texto-bold" value="Nome" />
                                                    </f:facet>
                                                    <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza" value="#{resumoPessoa.cliente.pessoa.nome}" />
                                                </rich:column>
                                                <rich:column sortBy="#{resumoPessoa.cliente.situacao}" headerClass="col-text-align-left" styleClass="col-text-align-left" filterEvent="onkeyup">
                                                    <f:facet name="header">
                                                        <h:outputText styleClass="texto-upper texto-font texto-size-14-real texto-cor-cinza texto-bold" value="Situação"/>
                                                    </f:facet>
                                                    <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza" value="#{resumoPessoa.cliente.situacao_Apresentar}" />
                                                </rich:column>
                                                <rich:column sortBy="#{resumoPessoa.tipoContrato.descricao}" headerClass="col-text-align-left" styleClass="col-text-align-left" filterEvent="onkeyup">
                                                    <f:facet name="header">
                                                        <h:outputText styleClass="texto-upper texto-font texto-size-14-real texto-cor-cinza texto-bold" value="Tipo do Contrato"/>
                                                    </f:facet>
                                                    <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza" value="#{resumoPessoa.tipoContrato.descricao}" />
                                                </rich:column>
                                                <rich:column sortBy="#{resumoPessoa.dataLancamento}" headerClass="col-text-align-left" styleClass="col-text-align-left" filterEvent="onkeyup">
                                                    <f:facet name="header">
                                                        <h:outputText styleClass="texto-upper texto-font texto-size-14-real texto-cor-cinza texto-bold" value="DT. Lançamento"/>
                                                    </f:facet>
                                                    <h:outputText  styleClass="texto-font texto-size-14-real texto-cor-cinza" value="#{resumoPessoa.dataLancamento_Apresentar}" />
                                                </rich:column>
                                                <rich:column sortBy="#{resumoPessoa.inicioContrato}" headerClass="col-text-align-left" styleClass="col-text-align-left" filterEvent="onkeyup">
                                                    <f:facet name="header">
                                                        <h:outputText styleClass="texto-upper texto-font texto-size-14-real texto-cor-cinza texto-bold" value="DT. Início"/>
                                                    </f:facet>
                                                    <h:outputText  styleClass="texto-font texto-size-14-real texto-cor-cinza" value="#{resumoPessoa.inicioContrato_Apresentar}" />
                                                </rich:column>
                                                <rich:column sortBy="#{resumoPessoa.duracao}" headerClass="col-text-align-left" styleClass="col-text-align-left" filterEvent="onkeyup">
                                                    <f:facet name="header">
                                                        <h:outputText styleClass="texto-upper texto-font texto-size-14-real texto-cor-cinza texto-bold" value="Duração do Plano"/>
                                                    </f:facet>
                                                    <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza" value="#{resumoPessoa.duracao}" />
                                                </rich:column>
                                                <rich:column sortBy="#{resumoPessoa.quantidadeDiasParaRenovar}" headerClass="col-text-align-left" styleClass="col-text-align-left" filterEvent="onkeyup">
                                                    <f:facet name="header">
                                                        <h:outputText styleClass="texto-upper texto-font texto-size-14-real texto-cor-cinza texto-bold" value="Dias Vencido"/>
                                                    </f:facet>
                                                    <h:outputText  styleClass="texto-font texto-size-14-real texto-cor-cinza" value="#{resumoPessoa.quantidadeDiasParaRenovar}" />
                                                </rich:column>
                                                <rich:column sortBy="#{resumoPessoa.consultor.pessoa.nome}" headerClass="col-text-align-left" styleClass="col-text-align-left"  filterEvent="onkeyup">
                                                    <f:facet name="header">
                                                        <h:outputText styleClass="texto-upper texto-font texto-size-14-real texto-cor-cinza texto-bold"  value="Consultor"/>
                                                    </f:facet>
                                                    <h:outputText  styleClass="texto-font texto-size-14-real texto-cor-cinza" value="#{resumoPessoa.consultor.pessoa.nome}" />
                                                </rich:column>
                                                <rich:column>
                                                    <a4j:commandLink styleClass="linkPadrao texto-cor-azul texto-size-14-real"
                                                                     action="#{IndiceConversaoVendaRelControle.irParaTelaCliente}"
                                                                     oncomplete="abrirPopup('clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);">
                                                        <f:param name="state" value="AC"/>
                                                        <i class="fa-icon-search"></i>
                                                    </a4j:commandLink>
                                                </rich:column>
                                            </rich:dataTable>
                                        </h:panelGrid>
                                    </h:panelGroup>
                                </h:panelGroup>
                            </h:panelGroup>

                        </h:panelGroup>
                    </h:panelGroup>
                </h:panelGroup>

            </h:form>
        </body>
    </html>
</h:panelGrid>
</f:view>


<%@page contentType="text/html"%>
<%@page pageEncoding="ISO-8859-1"%>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<link href="beta/css/font-awesome_2.0.min.css" rel="stylesheet" type="text/css">
<jsp:include page="include_head.jsp" flush="true" />
<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <h:form id="form">
        <head><%@include file="/includes/include_import_minifiles.jsp" %></head>


        <h:panelGroup layout="block" style="display: inline-table;width: 100%;" styleClass="fundoCinza">
            <h:panelGroup layout="block" styleClass="bgtop topoZW" rendered="#{MenuControle.apresentarTopo}">
                <jsp:include page="include_topo_novo.jsp" flush="true"/>
                <jsp:include page="include_menu_zw_flat.jsp" flush="true"/>
                <rich:jQuery selector=".item2" query="addClass('menuItemAtual')"/>
            </h:panelGroup>

            <h:panelGroup layout="block" styleClass="caixaCorpo">
                <h:panelGroup layout="block" style="height: 80%;width: 100%">
                    <h:panelGroup layout="block" styleClass="caixaMenuLatel">
                        <h:panelGroup layout="block" styleClass="container-imagem container-conteudo-central">
                            <h:panelGroup layout="block" styleClass="container-box form-flat">
                                <h:panelGroup styleClass="container-box-header" layout="block">
                                    <h:panelGroup layout="block" styleClass="margin-box">
                                        <h:outputText value="Cadastros Auxiliares" styleClass="container-header-titulo"/>
                                    </h:panelGroup>
                                </h:panelGroup>
                                <h:panelGroup id="panelCaixaAberto"  layout="block" styleClass="margin-box">
                                    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
                                        <h:panelGrid columnClasses="w33,w33,w33" columns="3" width="100%" cellpadding="0" cellspacing="0">


                                            <h:panelGrid columns="1" style="height:100%;" width="100%" cellpadding="5" cellspacing="5" >
                                                <h:panelGroup>
                                                    <div>
                                                        <h:outputText styleClass="fa-icon-pushpin textoCorAzul"/>
                                                        <rich:spacer width="5px"/>
                                                        <a4j:commandLink id="colaboradorMenuDescritivo"
                                                                         styleClass="tituloCampos"
                                                                         actionListener="#{MenuAcessoFacilControle.addHistorinoFuncionalidade}"
                                                                         rendered="#{LoginControle.permissaoAcessoMenuVO.colaborador}"
                                                                         action="#{ColaboradorControle.novo}"
                                                                         oncomplete="abrirPopup('colaboradorCons.jsp', 'Colaborador', 1090, 595);reRenderMenuLateral();"
                                                                         value="Colaborador">
                                                            <f:attribute name="funcionalidade" value="COLABORADOR"/>
                                                        </a4j:commandLink>
                                                        <h:outputText styleClass="tituloCampos" id="colaboradorMenuDescritivoText" rendered="#{!LoginControle.permissaoAcessoMenuVO.colaborador}"  value="Colaborador"/>
                                                    </div>
                                                    <rich:spacer height="10px"/>
                                                    <h:outputText styleClass="text" value="Deverão ser cadastrados, no campo cadastro de colaborador, todos os funcionários da academia.Dentre eles podem estar: os consultores, os professores, os personais, os gerentes, os responsáveis pela limpeza, dentre outros."/>
                                                    </br>
                                                    <h:outputText styleClass="text" value="Apesar de serem inclusos em uma mesma tela, eles se diferenciarão pelo seu TIPO, que deverá ser definido na hora do cadastro."/>
                                                </h:panelGroup>
                                                <h:panelGroup>
                                                    <h:outputText styleClass="text quote-text" value="Se cadastrarmos um colaborador e este for definido como TIPO CONSULTOR, o sistema tratará este colaborador como um Consultor de Vendas para todos os procedimentos e operações realizadas."/>
                                                </h:panelGroup>

                                                <c:if test="${LoginControle.apresentarLinkZW}">
                                                    <h:panelGroup>
                                                        <rich:spacer height="25px"/>
                                                        <div>
                                                            <h:outputText styleClass="fa-icon-pushpin textoCorAzul"/>
                                                            <rich:spacer width="5px"/>
                                                            <a4j:commandLink id="novoGrupoDescontoMenuDescritivo"
                                                                             styleClass="tituloCampos"
                                                                             rendered="#{LoginControle.permissaoAcessoMenuVO.grupo}"
                                                                             oncomplete="#{FuncionalidadeControle.abrirPopUp}"
                                                                             actionListener="#{MenuAcessoFacilControle.addHistoricoPreparaFuncionalidade}"
                                                                             value="Grupo com Desconto">
                                                                <f:attribute name="funcionalidade" value="GRUPO_DESCONTO"/>
                                                            </a4j:commandLink>
                                                            <h:outputText styleClass="tituloCampos" id="grupoDescontoMenuDescritivoText" rendered="#{!LoginControle.permissaoAcessoMenuVO.grupo}"  value="Grupo com Desconto"/>
                                                        </div>
                                                        <rich:spacer height="10px"/>
                                                        <h:outputText styleClass="text" value="Este campo será utilizado quando algum o colaborador quiser definir um desconto que será aplicado a um determinado grupo de pessoas, sem que este desconto seja caracterizado como Convênio de Desconto, já que este tipo de convênio é fechado com algum estabelecimento."/>
                                                    </h:panelGroup>
                                                    <h:panelGroup>
                                                        <h:outputText styleClass="text quote-text" value="Uma família composta por quatro pessoas resolveu praticar alguma atividade na mesma academia. O colaborador poderá encaixar as quatro pessoas em um grupo com desconto." />
                                                    </h:panelGroup>
                                                    <h:panelGroup>
                                                        <h:outputText styleClass="text" value="O grupo com desconto só poderá ser utilizado juntamente com algum plano." />
                                                    </h:panelGroup>
                                                </c:if>

                                                <h:panelGroup>
                                                    <rich:spacer height="25px"/>
                                                    <div>
                                                        <h:outputText styleClass="fa-icon-pushpin textoCorAzul"/>
                                                        <rich:spacer width="5px"/>
                                                        <a4j:commandLink id="novoClassificacaoMenuDescritivo"
                                                                         styleClass="tituloCampos"
                                                                         rendered="#{LoginControle.permissaoAcessoMenuVO.classificacao}"
                                                                         oncomplete="#{FuncionalidadeControle.abrirPopUp}"
                                                                         actionListener="#{MenuAcessoFacilControle.addHistoricoPreparaFuncionalidade}"
                                                                         value="Classificação">
                                                            <f:attribute name="funcionalidade" value="CLASSIFICACAO"/>
                                                        </a4j:commandLink>
                                                        <h:outputText styleClass="tituloCampos" id="classificacaoMenuDescritivoText" rendered="#{!LoginControle.permissaoAcessoMenuVO.classificacao}"  value="Classificação"/>
                                                    </div>
                                                    <rich:spacer height="10px"/>
                                                    <h:outputText styleClass="text" value="Através da classificação você poderá identificar e agrupar os alunos com características específicas. Dessa maneira, será mais fácil organizar as informações e identificar oportunidades de negócios para os alunos de uma mesma classificação. Você poderá criar diversas classificações, conforme as suas necessidades, sendo que uma única pessoa poderá ter mais de uma Classificação."/>
                                                </h:panelGroup>
                                                <h:panelGroup>
                                                    <h:outputText styleClass="text quote-text" value="Você poderá agrupar todos os seus alunos com idade acima de 60 anos, classificando-os como alunos de TERCEIRA IDADE. Você também poderá identificar seus atletas profissionais classificando-os como PROFISSIONAL."/>
                                                </h:panelGroup>

                                                <h:panelGroup>
                                                    <div>
                                                        <h:outputText styleClass="fa-icon-pushpin textoCorAzul"/>
                                                        <rich:spacer width="5px"/>
                                                        <a4j:commandLink id="novoCidadeDescritivo"
                                                                         styleClass="tituloCampos"
                                                                         rendered="#{LoginControle.permissaoAcessoMenuVO.cidade}"
                                                                         oncomplete="#{FuncionalidadeControle.abrirPopUp}"
                                                                         actionListener="#{MenuAcessoFacilControle.addHistoricoPreparaFuncionalidade}"
                                                                         value="Cidade">
                                                            <f:attribute name="funcionalidade"
                                                                         value="CIDADE"/>
                                                        </a4j:commandLink>
                                                        <h:outputText styleClass="tituloCampos" id="cidadeMenuDescritivoText"  rendered="#{!LoginControle.permissaoAcessoMenuVO.cidade}"  value="Cidade"/>
                                                    </div>
                                                    <rich:spacer height="25px"/>
                                                    <rich:spacer width="10px"/>
                                                    <h:outputText styleClass="text" value="Esta opção será utilizada para efetuar o CADASTRO DE CIDADES. Fazendo isso, você facilitará o cadastro de clientes. Esta opção será muito útil quando o cliente não souber o seu próprio CEP."/>

                                                </h:panelGroup>
                                            </h:panelGrid>
                                            <h:panelGrid style="height:100;%" columns="1" width="100%" cellpadding="5" cellspacing="5" >
                                                <h:panelGroup>
                                                    <div>
                                                        <h:outputText styleClass="fa-icon-pushpin textoCorAzul"/>
                                                        <rich:spacer width="5px"/>
                                                        <a4j:commandLink id="clienteMenuDescritivo"
                                                                         styleClass="tituloCampos"
                                                                         rendered="#{LoginControle.permissaoAcessoMenuVO.cliente}"
                                                                         oncomplete="reRenderMenuLateral();"
                                                                         actionListener="#{MenuAcessoFacilControle.addHistorinoFuncionalidade}"
                                                                         onclick="abrirPopup('clienteCons.jsp', 'Cliente', 800, 595);"
                                                                         value="Cliente">
                                                            <f:attribute name="funcionalidade" value="CLIENTE"/>
                                                        </a4j:commandLink>
                                                        <h:outputText styleClass="tituloCampos" id="clienteMenuDescritivoText" rendered="#{!LoginControle.permissaoAcessoMenuVO.cliente}"  value="Cliente"/>
                                                    </div>
                                                    <rich:spacer height="10px"/>
                                                    <h:outputText styleClass="text" value="No campo de cadastro do cliente você poderá consultar todos os dados dos alunos da academia. É importante lembrar que a qualidade de seus relatórios depende do quão completo estão os dados do Cliente, por isso, ao realizar o cadastro dos alunos é importante que sejam preenchidos, se não todos os campos, a maioria deles dos campos apresentados."/>
                                                    </br>
                                                    <h:outputText styleClass="text" value="Você também poderá definir alguns campos (que julgar mais importantes para suas pesquisas) como obrigatórios, assim você garantirá que os mesmos serão sempre preenchidos na hora do cadastro, facilitando suas pesquisas e relatórios."/>
                                                </h:panelGroup>

                                                <h:panelGroup>
                                                    <rich:spacer height="25px"/>
                                                    <div>
                                                        <h:outputText styleClass="fa-icon-pushpin textoCorAzul"/>
                                                        <rich:spacer width="5px"/>
                                                        <a4j:commandLink id="questionarioMenuDescritivo"
                                                                         rendered="#{LoginControle.permissaoAcessoMenuVO.questionario}"
                                                                         oncomplete="#{FuncionalidadeControle.abrirPopUp}"
                                                                         actionListener="#{MenuAcessoFacilControle.addHistoricoPreparaFuncionalidade}"
                                                                         styleClass="tituloCampos"
                                                                         value="Questionário">
                                                            <f:attribute name="funcionalidade" value="QUESTIONARIO"/>
                                                        </a4j:commandLink>

                                                        <h:outputText styleClass="tituloCampos" id="questionarioMenuDescritivoText" rendered="#{!LoginControle.permissaoAcessoMenuVO.questionario}"  value="Questionário"/>
                                                    </div>
                                                    <rich:spacer height="10px"/>
                                                    <h:outputText styleClass="text" value="No campo de cadastro de QUESTIONÁRIOS poderão ser criados vários Boletins de Visitas diferentes. O colaborador poderá criar o questionário de acordo com as necessidades da academia."/>
                                                    </br>
                                                    <h:outputText styleClass="text" value="De forma simplificada o colaborador conseguirá definir um nome e escolher as perguntas que farão parte do seu boletim e pronto."/>
                                                </h:panelGroup>

                                                <h:panelGroup>
                                                    <div>
                                                        <h:outputText styleClass="fa-icon-pushpin textoCorAzul"/>
                                                        <rich:spacer width="5px"/>
                                                        <a4j:commandLink id="novoProfissaoMenuDescritivo"
                                                                         styleClass="tituloCampos"
                                                                         rendered="#{LoginControle.permissaoAcessoMenuVO.profissao}"
                                                                         oncomplete="#{FuncionalidadeControle.abrirPopUp}"
                                                                         actionListener="#{MenuAcessoFacilControle.addHistoricoPreparaFuncionalidade}"
                                                                         value="Profissão">
                                                            <f:attribute name="funcionalidade" value="PROFISSAO"/>
                                                        </a4j:commandLink>
                                                        <h:outputText styleClass="tituloCampos" id="profissaoMenuDescritivoText" rendered="#{!LoginControle.permissaoAcessoMenuVO.profissao}"  value="Profissão"/>
                                                    </div>
                                                    <rich:spacer height="10px"/>
                                                    <h:outputText styleClass="text" value="Neste campo você cadastrará a profissão do aluno. É importante que o colaborador sempre mantenha seu CADASTRO DE PROFISSÃO completo ao cadastrar novos alunos ou funcionários. Lembre-se, também, que você só conseguirá retirar um relatório com as profissões dos alunos se as mesmas estiverem corretamente cadastradas e lançadas."/>
                                                </h:panelGroup>

                                                <c:if test="${LoginControle.apresentarLinkZW}">
                                                    <h:panelGroup>
                                                        <rich:spacer height="25px"/>
                                                        <div>
                                                            <h:outputText styleClass="fa-icon-pushpin textoCorAzul"/>
                                                            <rich:spacer width="5px"/>
                                                            <a4j:commandLink id="novoParentescoMenuDescritivo"
                                                                             styleClass="tituloCampos"
                                                                             rendered="#{LoginControle.permissaoAcessoMenuVO.parentesco}"
                                                                             oncomplete="#{FuncionalidadeControle.abrirPopUp}"
                                                                             actionListener="#{MenuAcessoFacilControle.addHistoricoPreparaFuncionalidade}"
                                                                             value="Parentesco">
                                                                <f:attribute name="funcionalidade" value="PARENTESCO"/>
                                                            </a4j:commandLink>
                                                            <h:outputText styleClass="tituloCampos" id="parentescoMenuDescritivoText" rendered="#{!LoginControle.permissaoAcessoMenuVO.parentesco}"  value="Parentesco"/>
                                                        </div>
                                                        <rich:spacer height="25px"/>
                                                        <rich:spacer width="10px"/>
                                                        <h:outputText styleClass="text" value="Esta opção, geralmente, é muito utilizada em clubes. O campo PARENTESCO existe para identificar qual o laço consanguíneo existe entre as pessoas cadastradas em um mesmo Grupo."/>
                                                    </h:panelGroup>
                                                    <h:panelGroup>
                                                        <h:outputText styleClass="text quote-text" value="Ao realizar um cadastro de alguma família o colaborador poderá classificar o grau de parentesco dos dependentes com o titular, como mãe, pai, irmão."/>
                                                    </h:panelGroup>
                                                </c:if>
                                            </h:panelGrid>

                                            <h:panelGrid columns="1" width="100%" style="height:100%;" cellpadding="5" cellspacing="5">


                                                <h:panelGroup>
                                                    <rich:spacer height="5px"/>
                                                    <div>
                                                        <h:outputText styleClass="fa-icon-pushpin textoCorAzul"/>
                                                        <rich:spacer width="5px"/>
                                                        <a4j:commandLink id="novoCategoriaClientesMenuDescritivo"
                                                                         styleClass="tituloCampos"
                                                                         rendered="#{LoginControle.permissaoAcessoMenuVO.categoria}"
                                                                         oncomplete="#{FuncionalidadeControle.abrirPopUp}"
                                                                         actionListener="#{MenuAcessoFacilControle.addHistoricoPreparaFuncionalidade}"
                                                                         value="Categoria de Clientes">
                                                            <f:attribute name="funcionalidade" value="CATEGORIA_CLIENTES"/>
                                                        </a4j:commandLink>
                                                        <h:outputText styleClass="tituloCampos" id="categoraClientesMenuDescritivoText" rendered="#{!LoginControle.permissaoAcessoMenuVO.categoria}"  value="Categoria de Clientes"/>
                                                    </div>
                                                    <rich:spacer height="10px"/>
                                                    <h:outputText styleClass="text" value="Se o seu negócio é Fitness, você poderá utilizar a CATEGORIA DE CLIENTES para diferenciar os diversos tipos de alunos que tem acesso a sua academia. Dessa forma você conseguirá agrupar todos aqueles que possuem características semelhantes, facilitando o controle e, principalmente, organizando os dados do seu sistema."/>
                                                </h:panelGroup>
                                                <h:panelGroup>
                                                    <h:outputText styleClass="text quote-text" value="Com a categoria de clientes você poderia criar a categoria ALUNO PERSONAL, do tipo ALUNO, para diferenciar os alunos comuns dos alunos que possuem seu próprio personal" />
                                                </h:panelGroup>
                                                <h:panelGroup>
                                                    <rich:spacer width="10px"/>
                                                    <h:outputText styleClass="text" value="Mas, caso você trabalhe com clubes, utilize sempre os tipos, SÓCIO e NÃO SÓCIO ao cadastrar suas categorias. Além disso, você poderá definir a quantidade máxima de convites permitida para cada categoria." />
                                                </h:panelGroup>
                                                <h:panelGroup>
                                                    <h:outputText styleClass="text" value="Você também conseguirá criar categorias de SÓCIO EFETIVO e SÓCIO CONTRIBUINTE, para diferenciar os dois tipos de sócios." />
                                                </h:panelGroup>

                                                <c:if test="${LoginControle.apresentarLinkZW}">
                                                    <h:panelGroup>
                                                        <rich:spacer height="25px"/>
                                                        <div>
                                                            <h:outputText styleClass="fa-icon-pushpin textoCorAzul"/>
                                                            <rich:spacer width="5px"/>
                                                            <a4j:commandLink id="novoPerguntaMenuDescritivo"
                                                                             styleClass="tituloCampos"
                                                                             rendered="#{LoginControle.permissaoAcessoMenuVO.pergunta}"
                                                                             oncomplete="#{FuncionalidadeControle.abrirPopUp}"
                                                                             actionListener="#{MenuAcessoFacilControle.addHistoricoPreparaFuncionalidade}"
                                                                             value="Pergunta">
                                                                <f:attribute name="funcionalidade" value="PERGUNTA"/>
                                                            </a4j:commandLink>
                                                            <h:outputText styleClass="tituloCampos" id="perguntaMenuDescritivoText" rendered="#{!LoginControle.permissaoAcessoMenuVO.pergunta}"  value="Pergunta"/>
                                                        </div>

                                                        <rich:spacer height="10px"/>
                                                        <h:outputText styleClass="text" value="Aqui você poderá cadastrar todas as perguntas de sua Anamnese (entrevista) que posteriormente poderão compor seu Boletim de Visita. As perguntas cadastradas aqui, poderão ser escolhidas, individualmente, no momento em que você criar o seu Questionário"/>
                                                    </h:panelGroup>
                                                </c:if>

                                                <h:panelGroup>
                                                    <rich:spacer height="25px"/>
                                                    <div>
                                                        <h:outputText styleClass="fa-icon-pushpin textoCorAzul"/>
                                                        <rich:spacer width="5px"/>
                                                        <a4j:commandLink id="novoPaisMenuDescritivo"
                                                                         styleClass="tituloCampos"
                                                                         rendered="#{LoginControle.permissaoAcessoMenuVO.pais}"
                                                                         oncomplete="#{FuncionalidadeControle.abrirPopUp}"
                                                                         actionListener="#{MenuAcessoFacilControle.addHistoricoPreparaFuncionalidade}"
                                                                         value="País">
                                                            <f:attribute name="funcionalidade" value="PAIS"/>
                                                        </a4j:commandLink>
                                                        <h:outputText styleClass="tituloCampos" id="paisMenuDescritivoText" rendered="#{!LoginControle.permissaoAcessoMenuVO.pais}"  value="País"/>
                                                    </div>
                                                    <rich:spacer height="10px"/>
                                                    <h:outputText styleClass="text" value="Neste campo de cadastro o colaborador deverá preencher com o PAÍS de origem do aluno. Se necessário, você poderá cadastrar novos países para manter seu cadastro mais completo possível."/>
                                                </h:panelGroup>

                                                <h:panelGroup>
                                                    <rich:spacer height="25px"/>
                                                    <div>
                                                        <h:outputText styleClass="fa-icon-pushpin textoCorAzul"/>
                                                        <rich:spacer width="5px"/>
                                                        <a4j:commandLink id="novoBrindeMenuDescritivo"
                                                                         styleClass="tituloCampos"
                                                                         rendered="#{LoginControle.permissaoAcessoMenuVO.brinde}"
                                                                         oncomplete="#{FuncionalidadeControle.abrirPopUp}"
                                                                         actionListener="#{MenuAcessoFacilControle.addHistorinoFuncionalidade}"
                                                                         value="Brinde">
                                                            <f:attribute name="funcionalidade" value="BRINDE"/>
                                                        </a4j:commandLink>
                                                        <h:outputText styleClass="tituloCampos"
                                                                      id="brindeMenuDescritivoText"
                                                                      rendered="#{!LoginControle.permissaoAcessoMenuVO.brinde}"
                                                                      value="Brinde"/>
                                                    </div>
                                                    <rich:spacer width="10px"/>
                                                    <h:outputText styleClass="text" value="Esta opção será utilizada para efetuar o CADASTRO DE BRINDES. Sera usado na utilização da pontuação realizada."/>

                                                </h:panelGroup>
                                                <%--
                                                <h:panelGroup>
                                                    <div>
                                                        <h:outputText styleClass="fa-icon-pushpin textoCorAzul"/>
                                                        <rich:spacer width="5px"/>
                                                        <a4j:commandLink id="conviteAulaExpMenuDescritivo" styleClass="tituloCampos" rendered="#{LoginControle.permissaoAcessoMenuVO.conviteAulaExperimental}" onclick="abrirPopup('tipoConviteAulaExperimentalCons.jsp', 'ConviteAulaExperimental', 800, 595);" value="Convite Aula Experimental"/>
                                                        <h:outputText styleClass="tituloCampos" id="conviteAulaExpMenuDescritivoText"  rendered="#{!LoginControle.permissaoAcessoMenuVO.conviteAulaExperimental}"  value="Convite Aula Experimental"/>
                                                    </div>
                                                    <rich:spacer height="10px"/>
                                                    <h:outputText styleClass="text" value="Esta opção será utilizada para efetuar o CADASTRO DE CONVITE PARA AULA EXPERIMENTAL. Após efetuar o cadastro e o mesmo já estiver vigente, o colaborador ou o próprio aluno poderão enviar convites para que outras pessoas façam algum tipo de aula experimental."/>

                                                </h:panelGroup>
                                                --%>
                                            </h:panelGrid>


                                        </h:panelGrid>
                                    </h:panelGrid>
                                </h:panelGroup>

                            </h:panelGroup>

                        </h:panelGroup>

                        <jsp:include page="include_box_menulateral.jsp">
                            <jsp:param name="menu" value="ADM-CADASTROS_AUXILIARES" />
                        </jsp:include>

                    </h:panelGroup>
                </h:panelGroup>
            </h:panelGroup>

            <jsp:include page="include_rodape_flat.jsp" flush="true" />

        </h:panelGroup>
    </h:form>
</f:view>

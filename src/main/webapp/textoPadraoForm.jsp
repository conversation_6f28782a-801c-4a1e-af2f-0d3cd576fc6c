<%@page contentType="text/html" %>
<%@page pageEncoding="ISO-8859-1" %>
<head>
    <%@include file="/includes/include_import_minifiles.jsp" %>
</head>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">

<%@taglib prefix="f" uri="http://java.sun.com/jsf/core" %>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html" %>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax" %>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich" %>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>
<script type="text/javascript" src="script/scriptSMS.js"></script>
<style type="text/css">
    body {
        margin: 0;
        padding: 0;
    }
</style>

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title>Script</title>

    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">

        <c:set var="titulo" scope="session" value="Script"/>
        <c:set var="urlWiki" scope="session" value="${SuperControle.urlWiki}"/>

        <h:panelGroup layout="block" styleClass="pure-g-r">
            <f:facet name="header">
                <jsp:include page="topoReduzido_material_crm.jsp"/>
            </f:facet>
        </h:panelGroup>

        <h:form id="form">
            <h:panelGrid id="panelGridForm" columns="1" styleClass="tabForm" width="100%">
                <h:panelGrid columns="1" style="height:25px; background: url('./imagens/fundoBarraTopo.png') repeat-x;"
                             columnClasses="colunaCentralizada" width="100%">
                    <h:outputText styleClass="tituloFormulario" value="Script">
                        <h:outputLink value="#{SuperControle.urlWikiCRM}Cadastros:TextoPadrao"
                                      title="Clique e saiba mais: Script" target="_blank">
                            <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                        </h:outputLink>
                    </h:outputText>
                </h:panelGrid>

                <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar" columnClasses="classEsquerda, classDireita"
                             styleClass="tabForm" width="100%">

                    <h:outputText styleClass="tituloCampos" value="Descrição:"/>
                    <h:inputText id="descricao" size="50" maxlength="50" onblur="blurinput(this);"
                                 onfocus="focusinput(this);" styleClass="form"
                                 value="#{TextoPadraoControle.textoPadraoVO.descricao}"/>

                    <h:outputText styleClass="tituloCampos" value="Fase(CRM):"/>
                    <h:selectOneMenu id="opcoesFaseCRM" onblur="blurinput(this);" onfocus="focusinput(this);"
                                     styleClass="form" style="border: none;"
                                     valueChangeListener="#{TextoPadraoControle.alterouFase}"
                                     value="#{TextoPadraoControle.textoPadraoVO.faseCRM.codigo}">
                        <f:selectItems value="#{TextoPadraoControle.listaSelectItemFaseCRM}"/>
                        <a4j:support event="onchange" reRender="opcoesFaseCRM"/>
                    </h:selectOneMenu>

                    <h:outputText styleClass="tituloCampos" value="Tipo de Contato:"/>
                    <h:selectOneMenu id="tipoContato"
                                     styleClass="form" style="border: none;"
                                     value="#{TextoPadraoControle.textoPadraoVO.tipoContato}">
                        <a4j:support event="onchange" actionListener="#{TextoPadraoControle.alterouTipoContato}" reRender="form" oncomplete="checarCriacaoQtdCaracteres();"/>
                        <f:selectItems value="#{TextoPadraoControle.listaSelectItemTipoContato}"/>
                    </h:selectOneMenu>

                    <h:outputText styleClass="tituloCampos" value="Link(Google Docs):"/>
                    <h:inputText id="linkDocs" size="50" onblur="blurinput(this);"
                                 onfocus="focusinput(this);" styleClass="form"
                                 value="#{TextoPadraoControle.textoPadraoVO.linkDocs}"/>

                    <h:outputText styleClass="tituloCampos" value="Script:"/>
                    <rich:editor rendered="#{TextoPadraoControle.renderizarTextoFormatado}" style="align:center;" id="mensagemPadrao" width="530"
                             value="#{TextoPadraoControle.textoPadraoVO.mensagemPadrao}"/>
                    <h:inputTextarea rendered="#{TextoPadraoControle.tipoContatoAplicativo}" value="#{TextoPadraoControle.textoPadraoVO.mensagemPadrao}"
                    id="textAreaMensagemPadraoAplicativo" rows="5" cols="100"></h:inputTextarea>
                    <h:inputTextarea rendered="#{TextoPadraoControle.tipoContatoSMS}" value="#{TextoPadraoControle.textoPadraoVO.mensagemPadrao}"
                                     id="textAreaMensagemPadrao" rows="5" cols="100" onkeyup="somaCRMCustom('form:textAreaMensagemPadrao','countMensagemPadrao', 140, true)"></h:inputTextarea>

                </h:panelGrid>

                <h:panelGrid columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid id="panelGridMensagens" columns="1" width="100%">
                        <h:outputText id="msgObjecao" styleClass="mensagem" value="#{TextoPadraoControle.mensagem}"/>
                        <h:outputText id="msgObjecaoDet" styleClass="mensagemDetalhada"
                                      value="#{TextoPadraoControle.mensagemDetalhada}"/>
                    </h:panelGrid>
                    <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                        <h:panelGroup>
                            <h:commandButton id="novo" immediate="true" action="#{TextoPadraoControle.novo}"
                                             onclick="fireElementFromAnyParent('form:btnAtualizaTempo')"
                                             value="#{msg_bt.btn_novo}"
                                             title="#{msg.msg_novo_dados}" accesskey="1" styleClass="botoes nvoBt btSec"/>
                            <rich:spacer width="10"/>
                            <a4j:commandButton id="salvar" action="#{TextoPadraoControle.gravar}"
                                               onclick="fireElementFromAnyParent('form:btnAtualizaTempo')"
                                               value="#{msg_bt.btn_gravar}" reRender="form"
                                               oncomplete="#{TextoPadraoControle.msgAlert}"
                                               title="#{msg.msg_gravar_dados}"
                                               accesskey="2" styleClass="botoes nvoBt"/>
                            <rich:spacer width="10"/>

                            <h:panelGroup id="grupoBtnExcluir">
                                <a4j:commandButton id="excluir"  reRender="mdlMensagemGenerica"
                                                   oncomplete="#{TextoPadraoControle.msgAlert}" action="#{TextoPadraoControle.confirmarExcluir}"
                                                   value="#{msg_bt.btn_excluir}" title="#{msg.msg_excluir_dados}" accesskey="3" styleClass="botoes nvoBt btSec btPerigo"/>
                            </h:panelGroup>

                            <rich:spacer width="10"/>
                            <h:commandButton id="consultar" immediate="true" action="consultar"
                                             onclick="fireElementFromAnyParent('form:btnAtualizaTempo')"
                                             value="#{msg_bt.btn_voltar_lista}"
                                             title="#{msg.msg_consultar_dados}" accesskey="4" styleClass="botoes nvoBt btSec"/>
                        </h:panelGroup>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
        </h:form>
    </h:panelGrid>
    <%@include file="includes/include_modal_mensagem_generica.jsp"%>
</f:view>
<script>
    document.getElementById("form:descricao").focus();
    function checarCriacaoQtdCaracteres(){
        if(!document.getElementById('countMensagemPadrao')){
            document.getElementById('form:textAreaMensagemPadrao').parentElement.innerHTML += '(<label id="countMensagemPadrao"></label>)'
        }
        somaCRMCustom('form:textAreaMensagemPadrao','countMensagemPadrao', 140, true);
    }
    checarCriacaoQtdCaracteres();
</script>
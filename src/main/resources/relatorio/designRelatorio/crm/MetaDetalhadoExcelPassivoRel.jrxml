<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="ParcelaEmAbertoRel" pageWidth="1417" pageHeight="842" columnWidth="1389" leftMargin="14" rightMargin="14" topMargin="14" bottomMargin="14" whenResourceMissingType="Empty">
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.encoding" value="ISO-8859-1"/>
	<property name="ireport.zoom" value="1.6105100000000017"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="java.util.*"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<subDataset name="Teste"/>
	<parameter name="logoPadraoRelatorio" class="java.io.InputStream" isForPrompting="false"/>
	<parameter name="tituloRelatorio" class="java.lang.String" isForPrompting="false"/>
	<parameter name="nomeEmpresa" class="java.lang.String" isForPrompting="false"/>
	<parameter name="versaoSoftware" class="java.lang.String" isForPrompting="false"/>
	<parameter name="usuario" class="java.lang.String" isForPrompting="false"/>
	<parameter name="filtros" class="java.lang.String" isForPrompting="false"/>
	<parameter name="SUBREPORT_DIR" class="java.lang.String">
		<defaultValueExpression><![CDATA["E:\\desenvolvimentosv\\Projeto Pacto Solucoes\\ZillyonWeb\\src\\java\\relatorio\\designRelatorio\\financeiro\\"]]></defaultValueExpression>
	</parameter>
	<parameter name="SUBREPORT_DIR1" class="java.lang.String">
		<defaultValueExpression><![CDATA["E:\\desenvolvimentosv\\Projeto Pacto Solucoes\\ZillyonWeb\\src\\java\\relatorio\\designRelatorio\\financeiro\\"]]></defaultValueExpression>
	</parameter>
	<parameter name="dataIni" class="java.lang.String" isForPrompting="false"/>
	<parameter name="dataFim" class="java.lang.String" isForPrompting="false"/>
	<parameter name="qtdAV" class="java.lang.String" isForPrompting="false"/>
	<parameter name="qtdCA" class="java.lang.String" isForPrompting="false"/>
	<parameter name="qtdChequeAV" class="java.lang.String" isForPrompting="false"/>
	<parameter name="qtdChequePR" class="java.lang.String" isForPrompting="false"/>
	<parameter name="qtdOutro" class="java.lang.String" isForPrompting="false"/>
	<parameter name="valorAV" class="java.lang.Double" isForPrompting="false"/>
	<parameter name="valorCA" class="java.lang.Double" isForPrompting="false"/>
	<parameter name="valorChequeAV" class="java.lang.Double" isForPrompting="false"/>
	<parameter name="valorChequePR" class="java.lang.Double" isForPrompting="false"/>
	<parameter name="valorOutro" class="java.lang.Double" isForPrompting="false"/>
	<parameter name="parametro1" class="java.lang.String" isForPrompting="false"/>
	<parameter name="parametro2" class="java.lang.String" isForPrompting="false"/>
	<parameter name="parametro3" class="java.lang.String" isForPrompting="false"/>
	<parameter name="parametro4" class="java.lang.String" isForPrompting="false"/>
	<parameter name="parametro5" class="java.lang.String" isForPrompting="false"/>
	<parameter name="parametro6" class="java.lang.String" isForPrompting="false"/>
	<field name="passivo.nome" class="java.lang.String"/>
	<field name="passivo.dia_Apresentar" class="java.lang.String"/>
	<field name="passivo.telefoneResidencial" class="java.lang.String"/>
	<field name="passivo.telefoneCelular" class="java.lang.String"/>
	<field name="passivo.telefoneTrabalho" class="java.lang.String"/>
	<field name="passivo.email" class="java.lang.String"/>
	<field name="passivo.evento.descricao" class="java.lang.String"/>
	<field name="passivo.colaboradorResponsavel.nome" class="java.lang.String"/>
	<field name="historicoContatoVO.resultado" class="java.lang.String"/>
	<field name="passivo.dia_ApresentarRel" class="java.lang.String"/>
	<field name="passivo.colaboradorResponsavel.nomeAbreviado" class="java.lang.String"/>
	<field name="dataUltimoContato_Apresentar" class="java.lang.String"/>
	<group name="nomeCliente">
		<groupHeader>
			<band height="15" splitType="Stretch">
				<staticText>
					<reportElement key="staticText-19" x="-1" y="0" width="204" height="15"/>
					<textElement>
						<font size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<text><![CDATA[Nome]]></text>
				</staticText>
				<staticText>
					<reportElement key="staticText-19" x="203" y="0" width="161" height="15"/>
					<textElement>
						<font size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<text><![CDATA[Data Lançamento]]></text>
				</staticText>
				<staticText>
					<reportElement key="staticText-19" x="505" y="0" width="109" height="15"/>
					<textElement>
						<font size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<text><![CDATA[Tel. Residencial]]></text>
				</staticText>
				<staticText>
					<reportElement key="staticText-19" x="614" y="0" width="91" height="15"/>
					<textElement>
						<font size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<text><![CDATA[Tel. Celular]]></text>
				</staticText>
				<staticText>
					<reportElement key="staticText-19" x="1095" y="0" width="155" height="15"/>
					<textElement>
						<font size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<text><![CDATA[Colaborador Responsável]]></text>
				</staticText>
				<staticText>
					<reportElement key="staticText-19" x="1250" y="0" width="138" height="15"/>
					<textElement>
						<font size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<text><![CDATA[Histórico Resultado]]></text>
				</staticText>
				<staticText>
					<reportElement key="staticText-19" x="796" y="0" width="180" height="15"/>
					<textElement>
						<font size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<text><![CDATA[E-mail]]></text>
				</staticText>
				<staticText>
					<reportElement key="staticText-19" x="976" y="0" width="119" height="15"/>
					<textElement>
						<font size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<text><![CDATA[Evento]]></text>
				</staticText>
				<staticText>
					<reportElement key="staticText-19" x="364" y="0" width="141" height="15"/>
					<textElement>
						<font size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<text><![CDATA[Último Contato]]></text>
				</staticText>
				<staticText>
					<reportElement key="staticText-19" x="705" y="0" width="91" height="15"/>
					<textElement>
						<font size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<text><![CDATA[Tel. Trabalho]]></text>
				</staticText>
			</band>
		</groupHeader>
		<groupFooter>
			<band splitType="Stretch"/>
		</groupFooter>
	</group>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band height="1" splitType="Stretch"/>
	</title>
	<pageHeader>
		<band splitType="Stretch"/>
	</pageHeader>
	<columnHeader>
		<band splitType="Stretch"/>
	</columnHeader>
	<detail>
		<band height="13" splitType="Stretch">
			<textField>
				<reportElement x="0" y="0" width="203" height="13"/>
				<textElement>
					<font size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{passivo.nome}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="505" y="0" width="109" height="13"/>
				<textElement>
					<font size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{passivo.telefoneResidencial}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="614" y="0" width="91" height="13"/>
				<textElement>
					<font size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{passivo.telefoneCelular}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="796" y="0" width="180" height="13"/>
				<textElement>
					<font size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{passivo.email}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="976" y="0" width="119" height="13"/>
				<textElement>
					<font size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{passivo.evento.descricao}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="1250" y="0" width="138" height="13"/>
				<textElement>
					<font size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{historicoContatoVO.resultado}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="1095" y="0" width="155" height="13"/>
				<textElement>
					<font size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{passivo.colaboradorResponsavel.nomeAbreviado}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="203" y="0" width="161" height="13"/>
				<textElement>
					<font size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{passivo.dia_ApresentarRel}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="364" y="0" width="141" height="13"/>
				<textElement/>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{dataUltimoContato_Apresentar}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="705" y="0" width="91" height="13"/>
				<textElement>
					<font size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{passivo.telefoneTrabalho}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<columnFooter>
		<band splitType="Stretch"/>
	</columnFooter>
	<pageFooter>
		<band splitType="Stretch"/>
	</pageFooter>
	<lastPageFooter>
		<band height="24" splitType="Stretch"/>
	</lastPageFooter>
	<summary>
		<band splitType="Stretch"/>
	</summary>
</jasperReport>

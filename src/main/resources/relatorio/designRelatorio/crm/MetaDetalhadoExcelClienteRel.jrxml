<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="ParcelaEmAbertoRel" pageWidth="1559" pageHeight="842" columnWidth="1531" leftMargin="14" rightMargin="14" topMargin="14" bottomMargin="14" whenResourceMissingType="Empty">
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.encoding" value="ISO-8859-1"/>
	<property name="ireport.zoom" value="1.7715610000000042"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="java.util.*"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<subDataset name="Teste"/>
	<parameter name="logoPadraoRelatorio" class="java.io.InputStream" isForPrompting="false"/>
	<parameter name="tituloRelatorio" class="java.lang.String" isForPrompting="false"/>
	<parameter name="nomeEmpresa" class="java.lang.String" isForPrompting="false"/>
	<parameter name="versaoSoftware" class="java.lang.String" isForPrompting="false"/>
	<parameter name="usuario" class="java.lang.String" isForPrompting="false"/>
	<parameter name="filtros" class="java.lang.String" isForPrompting="false"/>
	<parameter name="SUBREPORT_DIR" class="java.lang.String">
		<defaultValueExpression><![CDATA["E:\\desenvolvimentosv\\Projeto Pacto Solucoes\\ZillyonWeb\\src\\java\\relatorio\\designRelatorio\\financeiro\\"]]></defaultValueExpression>
	</parameter>
	<parameter name="SUBREPORT_DIR1" class="java.lang.String">
		<defaultValueExpression><![CDATA["E:\\desenvolvimentosv\\Projeto Pacto Solucoes\\ZillyonWeb\\src\\java\\relatorio\\designRelatorio\\financeiro\\"]]></defaultValueExpression>
	</parameter>
	<parameter name="dataIni" class="java.lang.String" isForPrompting="false"/>
	<parameter name="dataFim" class="java.lang.String" isForPrompting="false"/>
	<parameter name="qtdAV" class="java.lang.String" isForPrompting="false"/>
	<parameter name="qtdCA" class="java.lang.String" isForPrompting="false"/>
	<parameter name="qtdChequeAV" class="java.lang.String" isForPrompting="false"/>
	<parameter name="qtdChequePR" class="java.lang.String" isForPrompting="false"/>
	<parameter name="qtdOutro" class="java.lang.String" isForPrompting="false"/>
	<parameter name="valorAV" class="java.lang.Double" isForPrompting="false"/>
	<parameter name="valorCA" class="java.lang.Double" isForPrompting="false"/>
	<parameter name="valorChequeAV" class="java.lang.Double" isForPrompting="false"/>
	<parameter name="valorChequePR" class="java.lang.Double" isForPrompting="false"/>
	<parameter name="valorOutro" class="java.lang.Double" isForPrompting="false"/>
	<parameter name="parametro1" class="java.lang.String" isForPrompting="false"/>
	<parameter name="parametro2" class="java.lang.String" isForPrompting="false"/>
	<parameter name="parametro3" class="java.lang.String" isForPrompting="false"/>
	<parameter name="parametro4" class="java.lang.String" isForPrompting="false"/>
	<parameter name="parametro5" class="java.lang.String" isForPrompting="false"/>
	<parameter name="parametro6" class="java.lang.String" isForPrompting="false"/>
	<field name="cliente.pessoa.nome" class="java.lang.String"/>
	<field name="fecharMeta.aberturaMetaVO.colaboradorResponsavel.primeiroNomeConcatenado" class="java.lang.String"/>
	<field name="cliente.matricula" class="java.lang.String"/>
	<field name="historicoContatoVO.resultado" class="java.lang.String"/>
	<field name="fecharMeta.dataRegistro_Apresentar" class="java.lang.String"/>
	<field name="dataUltimoContato_Apresentar" class="java.lang.String"/>
	<field name="cliente.clienteSituacaoVO_Apresentar.situacaoAtualCliente_Apresentar" class="java.lang.String"/>
	<field name="fecharMeta.identificadorMeta" class="java.lang.String"/>
	<field name="configuracaoDiasPosVendaVO.descricao" class="java.lang.String"/>
	<field name="contratoVO.valorFinal" class="java.lang.Double"/>
	<group name="nomeCliente">
		<groupHeader>
			<band height="15" splitType="Stretch">
				<staticText>
					<reportElement key="staticText-19" x="78" y="0" width="256" height="15"/>
					<textElement>
						<font size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<text><![CDATA[Nome]]></text>
				</staticText>
				<staticText>
					<reportElement key="staticText-19" x="1222" y="0" width="160" height="15"/>
					<textElement>
						<font size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<text><![CDATA[Resultado Histórico]]></text>
				</staticText>
				<staticText>
					<reportElement key="staticText-19" x="991" y="0" width="231" height="15"/>
					<textElement>
						<font size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<text><![CDATA[Colaborador Responsável]]></text>
				</staticText>
				<staticText>
					<reportElement key="staticText-19" x="0" y="0" width="78" height="15"/>
					<textElement>
						<font size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<text><![CDATA[Matrícula]]></text>
				</staticText>
				<staticText>
					<reportElement key="staticText-19" x="694" y="0" width="130" height="15"/>
					<textElement>
						<font size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<text><![CDATA[Data Lançamento]]></text>
				</staticText>
				<staticText>
					<reportElement key="staticText-19" x="824" y="0" width="167" height="15"/>
					<textElement>
						<font size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<text><![CDATA[Último Contato]]></text>
				</staticText>
				<staticText>
					<reportElement key="staticText-19" x="334" y="0" width="61" height="15"/>
					<textElement>
						<font size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<text><![CDATA[Situação]]></text>
				</staticText>
				<staticText>
					<reportElement key="staticText-19" x="395" y="0" width="299" height="15" isRemoveLineWhenBlank="true">
						<printWhenExpression><![CDATA[$F{fecharMeta.identificadorMeta}.equals( "PV" )]]></printWhenExpression>
					</reportElement>
					<textElement>
						<font size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<text><![CDATA[Motivo entrar Contato]]></text>
				</staticText>
				<staticText>
					<reportElement key="staticText-19" x="1382" y="0" width="149" height="15" isRemoveLineWhenBlank="true">
						<printWhenExpression><![CDATA[$F{fecharMeta.identificadorMeta}.equals( "MF" )]]></printWhenExpression>
					</reportElement>
					<textElement>
						<font size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<text><![CDATA[Valor Contrato]]></text>
				</staticText>
			</band>
		</groupHeader>
		<groupFooter>
			<band splitType="Stretch"/>
		</groupFooter>
	</group>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band splitType="Stretch"/>
	</title>
	<pageHeader>
		<band height="3" splitType="Stretch"/>
	</pageHeader>
	<columnHeader>
		<band splitType="Stretch"/>
	</columnHeader>
	<detail>
		<band height="13" splitType="Stretch">
			<textField>
				<reportElement x="78" y="0" width="256" height="13"/>
				<textElement>
					<font size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{cliente.pessoa.nome}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="0" y="0" width="78" height="13"/>
				<textElement>
					<font size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{cliente.matricula}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="1222" y="0" width="160" height="13"/>
				<textElement>
					<font size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{historicoContatoVO.resultado}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="991" y="0" width="231" height="13"/>
				<textElement>
					<font size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{fecharMeta.aberturaMetaVO.colaboradorResponsavel.primeiroNomeConcatenado}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="694" y="0" width="130" height="13"/>
				<textElement>
					<font size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{fecharMeta.dataRegistro_Apresentar}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="824" y="0" width="167" height="13"/>
				<textElement>
					<font size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{dataUltimoContato_Apresentar}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="334" y="0" width="61" height="13"/>
				<textElement>
					<font size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{cliente.clienteSituacaoVO_Apresentar.situacaoAtualCliente_Apresentar}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="395" y="0" width="299" height="13" isRemoveLineWhenBlank="true">
					<printWhenExpression><![CDATA[$F{fecharMeta.identificadorMeta}.equals( "PV" )]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{configuracaoDiasPosVendaVO.descricao}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="1382" y="0" width="149" height="13" isRemoveLineWhenBlank="true">
					<printWhenExpression><![CDATA[$F{fecharMeta.identificadorMeta}.equals( "MF" )]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.Double"><![CDATA[$F{contratoVO.valorFinal}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<columnFooter>
		<band splitType="Stretch"/>
	</columnFooter>
	<pageFooter>
		<band splitType="Stretch"/>
	</pageFooter>
	<lastPageFooter>
		<band height="20" splitType="Stretch"/>
	</lastPageFooter>
	<summary>
		<band splitType="Stretch"/>
	</summary>
</jasperReport>

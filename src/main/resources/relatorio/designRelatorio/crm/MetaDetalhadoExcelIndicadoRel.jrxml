<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="ParcelaEmAbertoRel" pageWidth="1332" pageHeight="842" columnWidth="1304" leftMargin="14" rightMargin="14" topMargin="14" bottomMargin="14" whenResourceMissingType="Empty">
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.encoding" value="ISO-8859-1"/>
	<property name="ireport.zoom" value="1.4641000000000017"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="java.util.*"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<subDataset name="Teste"/>
	<parameter name="logoPadraoRelatorio" class="java.io.InputStream" isForPrompting="false"/>
	<parameter name="tituloRelatorio" class="java.lang.String" isForPrompting="false"/>
	<parameter name="nomeEmpresa" class="java.lang.String" isForPrompting="false"/>
	<parameter name="versaoSoftware" class="java.lang.String" isForPrompting="false"/>
	<parameter name="usuario" class="java.lang.String" isForPrompting="false"/>
	<parameter name="filtros" class="java.lang.String" isForPrompting="false"/>
	<parameter name="SUBREPORT_DIR" class="java.lang.String">
		<defaultValueExpression><![CDATA["E:\\desenvolvimentosv\\Projeto Pacto Solucoes\\ZillyonWeb\\src\\java\\relatorio\\designRelatorio\\financeiro\\"]]></defaultValueExpression>
	</parameter>
	<parameter name="SUBREPORT_DIR1" class="java.lang.String">
		<defaultValueExpression><![CDATA["E:\\desenvolvimentosv\\Projeto Pacto Solucoes\\ZillyonWeb\\src\\java\\relatorio\\designRelatorio\\financeiro\\"]]></defaultValueExpression>
	</parameter>
	<parameter name="dataIni" class="java.lang.String" isForPrompting="false"/>
	<parameter name="dataFim" class="java.lang.String" isForPrompting="false"/>
	<parameter name="qtdAV" class="java.lang.String" isForPrompting="false"/>
	<parameter name="qtdCA" class="java.lang.String" isForPrompting="false"/>
	<parameter name="qtdChequeAV" class="java.lang.String" isForPrompting="false"/>
	<parameter name="qtdChequePR" class="java.lang.String" isForPrompting="false"/>
	<parameter name="qtdOutro" class="java.lang.String" isForPrompting="false"/>
	<parameter name="valorAV" class="java.lang.Double" isForPrompting="false"/>
	<parameter name="valorCA" class="java.lang.Double" isForPrompting="false"/>
	<parameter name="valorChequeAV" class="java.lang.Double" isForPrompting="false"/>
	<parameter name="valorChequePR" class="java.lang.Double" isForPrompting="false"/>
	<parameter name="valorOutro" class="java.lang.Double" isForPrompting="false"/>
	<parameter name="parametro1" class="java.lang.String" isForPrompting="false"/>
	<parameter name="parametro2" class="java.lang.String" isForPrompting="false"/>
	<parameter name="parametro3" class="java.lang.String" isForPrompting="false"/>
	<parameter name="parametro4" class="java.lang.String" isForPrompting="false"/>
	<parameter name="parametro5" class="java.lang.String" isForPrompting="false"/>
	<parameter name="parametro6" class="java.lang.String" isForPrompting="false"/>
	<field name="indicado.nomeIndicado" class="java.lang.String"/>
	<field name="indicado.telefoneIndicado" class="java.lang.String"/>
	<field name="indicado.telefone" class="java.lang.String"/>
	<field name="indicado.email" class="java.lang.String"/>
	<field name="indicado.indicacaoVO.evento.descricao" class="java.lang.String"/>
	<field name="historicoContatoVO.resultado" class="java.lang.String"/>
	<field name="indicado.indicacaoVO.dia_ApresentarRel" class="java.lang.String"/>
	<field name="indicado.indicacaoVO.colaboradorResponsavel.nomeAbreviado" class="java.lang.String"/>
	<field name="dataUltimoContato_Apresentar" class="java.lang.String"/>
	<group name="nomeCliente">
		<groupHeader>
			<band height="15" splitType="Stretch">
				<staticText>
					<reportElement key="staticText-19" x="-1" y="0" width="240" height="15"/>
					<textElement>
						<font size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<text><![CDATA[Nome]]></text>
				</staticText>
				<staticText>
					<reportElement key="staticText-19" x="239" y="0" width="145" height="15"/>
					<textElement>
						<font size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<text><![CDATA[Data Lançamento]]></text>
				</staticText>
				<staticText>
					<reportElement key="staticText-19" x="529" y="0" width="87" height="15"/>
					<textElement>
						<font size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<text><![CDATA[Tel. Indicado]]></text>
				</staticText>
				<staticText>
					<reportElement key="staticText-19" x="616" y="0" width="87" height="15"/>
					<textElement>
						<font size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<text><![CDATA[Telefone]]></text>
				</staticText>
				<staticText>
					<reportElement key="staticText-19" x="961" y="0" width="204" height="15"/>
					<textElement>
						<font size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<text><![CDATA[Colaborador Responsável]]></text>
				</staticText>
				<staticText>
					<reportElement key="staticText-19" x="1165" y="0" width="139" height="15"/>
					<textElement>
						<font size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<text><![CDATA[Histórico Resultado]]></text>
				</staticText>
				<staticText>
					<reportElement key="staticText-19" x="852" y="0" width="109" height="15"/>
					<textElement>
						<font size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<text><![CDATA[Evento]]></text>
				</staticText>
				<staticText>
					<reportElement key="staticText-19" x="703" y="0" width="149" height="15"/>
					<textElement>
						<font size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<text><![CDATA[E-mail]]></text>
				</staticText>
				<staticText>
					<reportElement key="staticText-19" x="384" y="0" width="145" height="15"/>
					<textElement>
						<font size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<text><![CDATA[Último Contato]]></text>
				</staticText>
			</band>
		</groupHeader>
		<groupFooter>
			<band splitType="Stretch"/>
		</groupFooter>
	</group>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band splitType="Stretch"/>
	</title>
	<pageHeader>
		<band height="1" splitType="Stretch"/>
	</pageHeader>
	<columnHeader>
		<band splitType="Stretch"/>
	</columnHeader>
	<detail>
		<band height="13" splitType="Stretch">
			<textField>
				<reportElement x="239" y="0" width="145" height="13"/>
				<textElement>
					<font size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{indicado.indicacaoVO.dia_ApresentarRel}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="1" y="0" width="238" height="13"/>
				<textElement>
					<font size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{indicado.nomeIndicado}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="961" y="0" width="204" height="13"/>
				<textElement>
					<font size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{indicado.indicacaoVO.colaboradorResponsavel.nomeAbreviado}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="1165" y="0" width="139" height="13"/>
				<textElement>
					<font size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{historicoContatoVO.resultado}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="852" y="0" width="109" height="13"/>
				<textElement>
					<font size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{indicado.indicacaoVO.evento.descricao}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="529" y="0" width="87" height="13"/>
				<textElement>
					<font size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{indicado.telefoneIndicado}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="616" y="0" width="87" height="13"/>
				<textElement>
					<font size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{indicado.telefone}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="703" y="0" width="149" height="13"/>
				<textElement>
					<font size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{indicado.email}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="384" y="0" width="145" height="13"/>
				<textElement>
					<font size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{dataUltimoContato_Apresentar}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<columnFooter>
		<band splitType="Stretch"/>
	</columnFooter>
	<pageFooter>
		<band splitType="Stretch"/>
	</pageFooter>
	<lastPageFooter>
		<band height="19" splitType="Stretch"/>
	</lastPageFooter>
	<summary>
		<band splitType="Stretch"/>
	</summary>
</jasperReport>

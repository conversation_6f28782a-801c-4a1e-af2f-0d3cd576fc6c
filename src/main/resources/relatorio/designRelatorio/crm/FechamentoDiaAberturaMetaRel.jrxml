<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="ParcelaEmAbertoRel" pageWidth="595" pageHeight="842" columnWidth="567" leftMargin="14" rightMargin="14" topMargin="14" bottomMargin="14" whenResourceMissingType="Empty">
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.encoding" value="ISO-8859-1"/>
	<property name="ireport.zoom" value="1.4641000000000017"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="java.util.*"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<subDataset name="Teste"/>
	<parameter name="logoPadraoRelatorio" class="java.io.InputStream" isForPrompting="false"/>
	<parameter name="tituloRelatorio" class="java.lang.String" isForPrompting="false"/>
	<parameter name="nomeEmpresa" class="java.lang.String" isForPrompting="false"/>
	<parameter name="versaoSoftware" class="java.lang.String" isForPrompting="false"/>
	<parameter name="usuario" class="java.lang.String" isForPrompting="false"/>
	<parameter name="filtros" class="java.lang.String" isForPrompting="false"/>
	<parameter name="SUBREPORT_DIR" class="java.lang.String">
		<defaultValueExpression><![CDATA["E:\\desenvolvimentosv\\Projeto Pacto Solucoes\\ZillyonWeb\\src\\java\\relatorio\\designRelatorio\\financeiro\\"]]></defaultValueExpression>
	</parameter>
	<parameter name="SUBREPORT_DIR1" class="java.lang.String">
		<defaultValueExpression><![CDATA["E:\\desenvolvimentosv\\Projeto Pacto Solucoes\\ZillyonWeb\\src\\java\\relatorio\\designRelatorio\\financeiro\\"]]></defaultValueExpression>
	</parameter>
	<parameter name="dataIni" class="java.lang.String" isForPrompting="false"/>
	<parameter name="dataFim" class="java.lang.String" isForPrompting="false"/>
	<parameter name="qtdAV" class="java.lang.String" isForPrompting="false"/>
	<parameter name="qtdCA" class="java.lang.String" isForPrompting="false"/>
	<parameter name="qtdChequeAV" class="java.lang.String" isForPrompting="false"/>
	<parameter name="qtdChequePR" class="java.lang.String" isForPrompting="false"/>
	<parameter name="qtdOutro" class="java.lang.String" isForPrompting="false"/>
	<parameter name="valorAV" class="java.lang.Double" isForPrompting="false"/>
	<parameter name="valorCA" class="java.lang.Double" isForPrompting="false"/>
	<parameter name="valorChequeAV" class="java.lang.Double" isForPrompting="false"/>
	<parameter name="valorChequePR" class="java.lang.Double" isForPrompting="false"/>
	<parameter name="valorOutro" class="java.lang.Double" isForPrompting="false"/>
	<parameter name="parametro1" class="java.lang.String" isForPrompting="false"/>
	<parameter name="parametro2" class="java.lang.String" isForPrompting="false"/>
	<parameter name="parametro3" class="java.lang.String" isForPrompting="false"/>
	<parameter name="parametro4" class="java.lang.String" isForPrompting="false"/>
	<parameter name="parametro5" class="java.lang.String" isForPrompting="false"/>
	<parameter name="parametro6" class="java.lang.String" isForPrompting="false"/>
	<parameter name="totalPessoas" class="java.lang.Double"/>
	<field name="identificadorMeta_Apresentar" class="java.lang.String"/>
	<field name="meta" class="java.lang.Double"/>
	<field name="metaAtingida" class="java.lang.Double"/>
	<field name="porcentagemApresentar" class="java.lang.String"/>
	<field name="justificativa" class="java.lang.String"/>
	<field name="aberturaMetaVO.colaboradorResponsavel.nome" class="java.lang.String"/>
	<field name="aberturaMetaVO.responsavelCadastro.nome" class="java.lang.String"/>
	<field name="aberturaMetaVO.dia" class="java.util.Date"/>
	<field name="apresentarImagemFaturamento" class="java.lang.Boolean"/>
	<field name="aberturaMetaVO.justificativa" class="java.lang.String"/>
	<group name="nomeCliente">
		<groupHeader>
			<band height="17" splitType="Stretch">
				<staticText>
					<reportElement key="staticText-19" x="1" y="0" width="202" height="17"/>
					<textElement>
						<font size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<text><![CDATA[Identificador Meta]]></text>
				</staticText>
				<staticText>
					<reportElement key="staticText-19" x="203" y="0" width="114" height="17"/>
					<textElement>
						<font size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<text><![CDATA[Meta]]></text>
				</staticText>
				<staticText>
					<reportElement key="staticText-19" x="317" y="0" width="126" height="17"/>
					<textElement>
						<font size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<text><![CDATA[Resultado]]></text>
				</staticText>
				<staticText>
					<reportElement key="staticText-19" x="443" y="0" width="124" height="17"/>
					<textElement>
						<font size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<text><![CDATA[Percentual %]]></text>
				</staticText>
			</band>
		</groupHeader>
		<groupFooter>
			<band height="99" splitType="Stretch">
				<textField>
					<reportElement x="98" y="8" width="458" height="27"/>
					<textElement verticalAlignment="Middle">
						<font size="8"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$F{aberturaMetaVO.justificativa}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement key="staticText-19" x="257" y="56" width="163" height="19"/>
					<textElement>
						<font size="12" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<text><![CDATA[Percentual de Meta Diária:]]></text>
				</staticText>
				<textField pattern="###0.00">
					<reportElement x="420" y="56" width="60" height="19"/>
					<textElement>
						<font size="12"/>
					</textElement>
					<textFieldExpression class="java.lang.Double"><![CDATA[$P{totalPessoas}]]></textFieldExpression>
				</textField>
				<line>
					<reportElement key="line-1" positionType="Float" x="154" y="90" width="289" height="1"/>
				</line>
				<staticText>
					<reportElement key="staticText-19" x="12" y="79" width="137" height="17"/>
					<textElement>
						<font size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<text><![CDATA[Ass. Responsável Cadastro:]]></text>
				</staticText>
				<staticText>
					<reportElement key="staticText-19" x="12" y="8" width="86" height="27"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<text><![CDATA[Justificativa:]]></text>
				</staticText>
			</band>
		</groupFooter>
	</group>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band splitType="Stretch"/>
	</title>
	<pageHeader>
		<band height="110" splitType="Stretch">
			<image isUsingCache="true" onErrorType="Blank" evaluationTime="Page">
				<reportElement key="image-1" x="1" y="1" width="82" height="36" isPrintWhenDetailOverflows="true"/>
				<box>
					<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<imageExpression class="java.io.InputStream"><![CDATA[$P{logoPadraoRelatorio}]]></imageExpression>
			</image>
			<textField pattern="dd/MM/yyyy HH.mm.ss" isBlankWhenNull="false">
				<reportElement key="dataRel-1" mode="Opaque" x="443" y="1" width="124" height="19" backcolor="#FFFFFF"/>
				<box bottomPadding="2">
					<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Verdana" size="8" isItalic="false" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression class="java.util.Date"><![CDATA[new Date()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-2" x="83" y="1" width="360" height="36"/>
				<box>
					<pen lineWidth="0.5" lineStyle="Solid"/>
					<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid"/>
				</box>
				<textElement textAlignment="Center">
					<font fontName="Arial" size="14" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{tituloRelatorio}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-25" x="443" y="20" width="73" height="17"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right">
					<font fontName="Arial"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA["Pág: " + $V{PAGE_NUMBER} + " de "]]></textFieldExpression>
			</textField>
			<textField evaluationTime="Report" isBlankWhenNull="false">
				<reportElement key="textField-26" x="516" y="20" width="51" height="17"/>
				<box bottomPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement>
					<font fontName="Arial"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[" " + $V{PAGE_NUMBER} + ""]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement key="staticText-19" x="0" y="47" width="98" height="17"/>
				<textElement>
					<font size="7" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Colaborador Responsável:]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-19" x="0" y="64" width="98" height="17"/>
				<textElement>
					<font size="7" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Responsável Cadastro:]]></text>
			</staticText>
			<textField>
				<reportElement x="98" y="47" width="159" height="17"/>
				<textElement>
					<font size="6"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{aberturaMetaVO.colaboradorResponsavel.nome}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="98" y="64" width="159" height="17"/>
				<textElement>
					<font size="6"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{aberturaMetaVO.responsavelCadastro.nome}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement key="staticText-19" x="0" y="81" width="98" height="16"/>
				<textElement>
					<font size="7" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Dia Abertura Meta:]]></text>
			</staticText>
			<textField pattern="dd/MM/yyyy">
				<reportElement x="98" y="81" width="159" height="16"/>
				<textElement>
					<font size="6"/>
				</textElement>
				<textFieldExpression class="java.util.Date"><![CDATA[$F{aberturaMetaVO.dia}]]></textFieldExpression>
			</textField>
		</band>
	</pageHeader>
	<columnHeader>
		<band splitType="Stretch"/>
	</columnHeader>
	<detail>
		<band height="22" splitType="Stretch">
			<textField>
				<reportElement positionType="Float" x="0" y="0" width="203" height="13" isRemoveLineWhenBlank="true" isPrintWhenDetailOverflows="true"/>
				<textElement>
					<font size="10" isBold="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{identificadorMeta_Apresentar}]]></textFieldExpression>
			</textField>
			<textField pattern="###0">
				<reportElement positionType="Float" x="203" y="0" width="114" height="13" isRemoveLineWhenBlank="true" isPrintWhenDetailOverflows="true">
					<printWhenExpression><![CDATA[!$F{apresentarImagemFaturamento}]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.Double"><![CDATA[$F{meta}]]></textFieldExpression>
			</textField>
			<textField pattern="###0">
				<reportElement positionType="Float" x="317" y="0" width="126" height="13" isRemoveLineWhenBlank="true" isPrintWhenDetailOverflows="true">
					<printWhenExpression><![CDATA[!$F{apresentarImagemFaturamento}]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.Double"><![CDATA[$F{metaAtingida}]]></textFieldExpression>
			</textField>
			<textField pattern="###0.00">
				<reportElement x="443" y="0" width="124" height="13" isRemoveLineWhenBlank="true" isPrintWhenDetailOverflows="true"/>
				<textElement>
					<font size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{porcentagemApresentar}]]></textFieldExpression>
			</textField>
			<line>
				<reportElement key="line-1" positionType="Float" x="0" y="18" width="567" height="1" isRemoveLineWhenBlank="true" isPrintWhenDetailOverflows="true"/>
			</line>
		</band>
	</detail>
	<columnFooter>
		<band splitType="Stretch"/>
	</columnFooter>
	<pageFooter>
		<band splitType="Stretch"/>
	</pageFooter>
	<lastPageFooter>
		<band height="20" splitType="Stretch">
			<textField pattern="" isBlankWhenNull="false">
				<reportElement key="textField-207" x="2" y="3" width="565" height="13"/>
				<box>
					<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="8" isItalic="true" pdfFontName="Helvetica-Oblique"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[" "+" Usuário:" + $P{usuario}]]></textFieldExpression>
			</textField>
		</band>
	</lastPageFooter>
	<summary>
		<band splitType="Stretch"/>
	</summary>
</jasperReport>

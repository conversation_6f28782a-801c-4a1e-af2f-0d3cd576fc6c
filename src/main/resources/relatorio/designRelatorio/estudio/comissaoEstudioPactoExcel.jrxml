<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="comissaoEstudioPactoExcel" pageWidth="1213" pageHeight="50000" columnWidth="1213" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0">
	<property name="ireport.zoom" value="1.2396694214876047"/>
	<property name="ireport.x" value="487"/>
	<property name="ireport.y" value="0"/>
	<queryString>
		<![CDATA[]]>
	</queryString>
	<field name="descColaborador" class="java.lang.String"/>
	<field name="descCliente" class="java.lang.String"/>
	<field name="descAmbiente" class="java.lang.String"/>
	<field name="descProduto" class="java.lang.String"/>
	<field name="descStatus" class="java.lang.String"/>
	<field name="siglaTipoAula" class="java.lang.String"/>
	<field name="valorComissao" class="java.math.BigDecimal"/>
	<field name="valorUnitario" class="java.math.BigDecimal"/>
	<field name="somaComissao" class="java.math.BigDecimal"/>
	<field name="somaUnitario" class="java.math.BigDecimal"/>
	<field name="codgMatricula" class="java.lang.Integer"/>
	<field name="dataAula" class="java.util.Date"/>
	<field name="horaInicio" class="java.sql.Time"/>
	<field name="valorUnitarioString" class="java.lang.String"/>
	<field name="valorComissaoString" class="java.lang.String"/>
	<field name="porcentagem" class="java.lang.String"/>
	<field name="valorComissaoIndicacaoString" class="java.lang.String"/>
	<field name="descColaboradorIndicacao" class="java.lang.String"/>
	<variable name="somaProduto" class="java.math.BigDecimal" calculation="Count">
		<variableExpression><![CDATA[$F{valorUnitario}]]></variableExpression>
	</variable>
	<variable name="somaUnitarioProduto" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{valorUnitario}]]></variableExpression>
	</variable>
	<variable name="somaComissaoProduto" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{valorComissao}]]></variableExpression>
		<initialValueExpression><![CDATA[]]></initialValueExpression>
	</variable>
	<variable name="somaUnitarioProfissional" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{valorUnitario}]]></variableExpression>
	</variable>
	<variable name="somaComissaoProfissional" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{valorComissao}]]></variableExpression>
		<initialValueExpression><![CDATA[]]></initialValueExpression>
	</variable>
	<variable name="somaProdutoProfissional" class="java.math.BigDecimal" calculation="Count">
		<variableExpression><![CDATA[$F{valorUnitario}]]></variableExpression>
	</variable>
	<variable name="totalSessoes" class="java.math.BigDecimal" calculation="Count">
		<variableExpression><![CDATA[$F{valorUnitario}]]></variableExpression>
	</variable>
	<columnHeader>
		<band height="13" splitType="Stretch">
			<staticText>
				<reportElement key="staticText-1" positionType="Float" x="909" y="0" width="52" height="13"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="SansSerif" size="9" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Vl. Unit.]]></text>
			</staticText>
			<staticText>
				<reportElement key="" positionType="Float" x="97" y="0" width="189" height="13"/>
				<textElement verticalAlignment="Middle">
					<font fontName="SansSerif" size="9" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Produto]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-1" positionType="Float" x="961" y="0" width="55" height="13"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="SansSerif" size="9" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Comissão]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-1" positionType="Float" x="876" y="0" width="33" height="13"/>
				<textElement verticalAlignment="Middle">
					<font fontName="SansSerif" size="9" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[T.H.]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-1" positionType="Float" x="641" y="0" width="130" height="13"/>
				<textElement verticalAlignment="Middle">
					<font fontName="SansSerif" size="9" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Ambiente]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-1" positionType="Float" x="588" y="0" width="53" height="13"/>
				<textElement verticalAlignment="Middle">
					<font fontName="SansSerif" size="9" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Hora]]></text>
			</staticText>
			<staticText>
				<reportElement key="" positionType="Float" x="341" y="0" width="170" height="13"/>
				<textElement verticalAlignment="Middle">
					<font fontName="SansSerif" size="9" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Cliente]]></text>
			</staticText>
			<staticText>
				<reportElement key="" positionType="Float" x="286" y="0" width="55" height="13"/>
				<textElement verticalAlignment="Middle">
					<font fontName="SansSerif" size="9" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Matrícula]]></text>
			</staticText>
			<staticText>
				<reportElement key="" positionType="Float" x="0" y="0" width="97" height="13"/>
				<textElement verticalAlignment="Middle">
					<font fontName="SansSerif" size="9" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Profissional]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-1" positionType="Float" x="771" y="0" width="105" height="13"/>
				<textElement verticalAlignment="Middle">
					<font fontName="SansSerif" size="9" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Status]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-1" positionType="Float" x="511" y="0" width="77" height="13"/>
				<textElement verticalAlignment="Middle">
					<font fontName="SansSerif" size="9" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Data Aula]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-1" positionType="Float" x="1016" y="0" width="107" height="13"/>
				<textElement verticalAlignment="Middle">
					<font fontName="SansSerif" size="9" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Profissional Indicação]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-1" positionType="Float" x="1123" y="0" width="90" height="13"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="SansSerif" size="9" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Comissão Indicado]]></text>
			</staticText>
		</band>
	</columnHeader>
	<detail>
		<band height="13">
			<textField pattern="" isBlankWhenNull="true">
				<reportElement x="909" y="0" width="52" height="13"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="SansSerif" size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{valorUnitarioString}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="0" y="0" width="97" height="13"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="SansSerif" size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{descColaborador}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement x="961" y="0" width="55" height="13"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="SansSerif" size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{valorComissaoString}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="341" y="0" width="170" height="13"/>
				<textElement verticalAlignment="Middle">
					<font fontName="SansSerif" size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{descCliente}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="771" y="0" width="105" height="13"/>
				<textElement verticalAlignment="Middle">
					<font fontName="SansSerif" size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{descStatus}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="641" y="0" width="130" height="13"/>
				<textElement verticalAlignment="Middle">
					<font fontName="SansSerif" size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{descAmbiente}]]></textFieldExpression>
			</textField>
			<textField pattern="dd/MM/yyyy">
				<reportElement x="511" y="0" width="77" height="13"/>
				<textElement verticalAlignment="Middle">
					<font fontName="SansSerif" size="8"/>
				</textElement>
				<textFieldExpression class="java.util.Date"><![CDATA[$F{dataAula}]]></textFieldExpression>
			</textField>
			<textField pattern="HH:mm">
				<reportElement x="588" y="0" width="53" height="13"/>
				<textElement verticalAlignment="Middle">
					<font fontName="SansSerif" size="8"/>
				</textElement>
				<textFieldExpression class="java.sql.Time"><![CDATA[$F{horaInicio}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="286" y="0" width="55" height="13"/>
				<textElement verticalAlignment="Middle">
					<font fontName="SansSerif" size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.Integer"><![CDATA[$F{codgMatricula}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="97" y="0" width="189" height="13"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="SansSerif" size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{descProduto} + " - "+$F{porcentagem}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="1016" y="0" width="107" height="13"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="SansSerif" size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{descColaboradorIndicacao}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement x="1123" y="0" width="90" height="13"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="SansSerif" size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{valorComissaoIndicacaoString}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="876" y="0" width="33" height="13"/>
				<textElement verticalAlignment="Middle">
					<font fontName="SansSerif" size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{siglaTipoAula}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
</jasperReport>

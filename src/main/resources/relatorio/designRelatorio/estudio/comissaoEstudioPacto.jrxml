<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="comissaoPacto" pageWidth="595" pageHeight="842" columnWidth="567" leftMargin="14" rightMargin="14" topMargin="14" bottomMargin="14">
	<property name="ireport.zoom" value="2.4200000000000017"/>
	<property name="ireport.x" value="441"/>
	<property name="ireport.y" value="8"/>
	<parameter name="versaoSoftware" class="java.lang.String" isForPrompting="false"/>
	<parameter name="usuario" class="java.lang.String" isForPrompting="false"/>
	<parameter name="filtros" class="java.lang.String" isForPrompting="false"/>
	<parameter name="nomeEmpresa" class="java.lang.String"/>
	<parameter name="enderecoEmpresa" class="java.lang.String"/>
	<parameter name="cidadeEmpresa" class="java.lang.String"/>
	<parameter name="logoPadraoRelatorio" class="java.io.InputStream" isForPrompting="false"/>
	<parameter name="totalComissao" class="java.math.BigDecimal" isForPrompting="false"/>
	<parameter name="totalUnitario" class="java.math.BigDecimal" isForPrompting="false"/>
	<parameter name="totalCliente" class="java.lang.Integer"/>
	<field name="descColaborador" class="java.lang.String"/>
	<field name="descCliente" class="java.lang.String"/>
	<field name="descAmbiente" class="java.lang.String"/>
	<field name="descProduto" class="java.lang.String"/>
	<field name="descStatus" class="java.lang.String"/>
	<field name="siglaTipoAula" class="java.lang.String"/>
	<field name="valorComissao" class="java.math.BigDecimal"/>
	<field name="valorUnitario" class="java.math.BigDecimal"/>
	<field name="somaComissao" class="java.math.BigDecimal"/>
	<field name="somaUnitario" class="java.math.BigDecimal"/>
	<field name="codgMatricula" class="java.lang.Integer"/>
	<field name="dataAula" class="java.util.Date"/>
	<field name="horaInicio" class="java.sql.Time"/>
	<field name="horaTermino" class="java.sql.Time"/>
	<field name="porcentagem" class="java.lang.String"/>
	<variable name="somaProduto" class="java.math.BigDecimal" resetType="Group" resetGroup="Produto" calculation="Count">
		<variableExpression><![CDATA[$F{valorUnitario}]]></variableExpression>
	</variable>
	<variable name="somaUnitarioProduto" class="java.math.BigDecimal" resetType="Group" resetGroup="Produto" calculation="Sum">
		<variableExpression><![CDATA[$F{valorUnitario}]]></variableExpression>
	</variable>
	<variable name="somaComissaoProduto" class="java.math.BigDecimal" resetType="Group" resetGroup="Produto" calculation="Sum">
		<variableExpression><![CDATA[$F{valorComissao}]]></variableExpression>
		<initialValueExpression><![CDATA[]]></initialValueExpression>
	</variable>
	<variable name="somaUnitarioProfissional" class="java.math.BigDecimal" resetType="Group" resetGroup="Profissionais" calculation="Sum">
		<variableExpression><![CDATA[$F{valorUnitario}]]></variableExpression>
	</variable>
	<variable name="somaComissaoProfissional" class="java.math.BigDecimal" resetType="Group" resetGroup="Profissionais" calculation="Sum">
		<variableExpression><![CDATA[$F{valorComissao}]]></variableExpression>
		<initialValueExpression><![CDATA[]]></initialValueExpression>
	</variable>
	<variable name="somaProdutoProfissional" class="java.math.BigDecimal" resetType="Group" resetGroup="Profissionais" calculation="Count">
		<variableExpression><![CDATA[$F{valorUnitario}]]></variableExpression>
	</variable>
	<variable name="totalSessoes" class="java.math.BigDecimal" calculation="Count">
		<variableExpression><![CDATA[$F{valorUnitario}]]></variableExpression>
	</variable>
	<group name="Profissionais">
		<groupExpression><![CDATA[$F{descColaborador}]]></groupExpression>
		<groupHeader>
			<band height="14">
				<rectangle>
					<reportElement mode="Opaque" x="0" y="0" width="567" height="14" forecolor="#DBD3F6" backcolor="#DBD3F6"/>
				</rectangle>
				<textField>
					<reportElement x="5" y="3" width="189" height="11"/>
					<textElement>
						<font fontName="Arial" size="8" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$F{descColaborador}]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
		<groupFooter>
			<band height="20">
				<textField pattern="###0.00;-###0.00" isBlankWhenNull="true">
					<reportElement x="482" y="3" width="36" height="10"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<textFieldExpression class="java.math.BigDecimal"><![CDATA[$V{somaUnitarioProfissional}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement x="184" y="3" width="204" height="10"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<text><![CDATA[TOTAL POR PROFISSIONAL:]]></text>
				</staticText>
				<textField pattern="###0.00;-###0.00" isBlankWhenNull="true">
					<reportElement x="533" y="3" width="34" height="10"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<textFieldExpression class="java.math.BigDecimal"><![CDATA[$V{somaComissaoProfissional}]]></textFieldExpression>
				</textField>
				<textField pattern="">
					<reportElement x="402" y="3" width="24" height="10"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<textFieldExpression class="java.math.BigDecimal"><![CDATA[$V{somaProdutoProfissional}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="430" y="3" width="45" height="10"/>
					<textElement textAlignment="Left" markup="none">
						<font fontName="Arial" size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$V{somaProdutoProfissional}.compareTo(new BigDecimal(1)) == 1 ? "SESSÕES" : "SESSÃO"]]></textFieldExpression>
				</textField>
			</band>
		</groupFooter>
	</group>
	<group name="Produto">
		<groupExpression><![CDATA[$F{descProduto}]]></groupExpression>
		<groupHeader>
			<band height="15">
				<textField isStretchWithOverflow="true">
					<reportElement stretchType="RelativeToTallestObject" x="13" y="4" width="554" height="11"/>
					<textElement>
						<font fontName="Arial" size="8" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$F{descProduto}+" - "+$F{porcentagem}]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
		<groupFooter>
			<band height="15">
				<textField pattern="###0.00;-###0.00" isBlankWhenNull="true">
					<reportElement x="483" y="3" width="36" height="10"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<textFieldExpression class="java.math.BigDecimal"><![CDATA[$V{somaUnitarioProduto}]]></textFieldExpression>
				</textField>
				<textField pattern="###0.00;-###0.00" isBlankWhenNull="true">
					<reportElement x="533" y="3" width="34" height="10"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<textFieldExpression class="java.math.BigDecimal"><![CDATA[$V{somaComissaoProduto}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement x="286" y="3" width="102" height="10"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<text><![CDATA[TOTAL POR PRODUTO:]]></text>
				</staticText>
				<textField>
					<reportElement x="430" y="3" width="45" height="10"/>
					<textElement textAlignment="Left" markup="none">
						<font fontName="Arial" size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$V{somaProduto}.compareTo(new BigDecimal(1)) == 1 ? "SESSÕES" : "SESSÃO"]]></textFieldExpression>
				</textField>
				<textField pattern="">
					<reportElement x="402" y="3" width="24" height="10"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<textFieldExpression class="java.math.BigDecimal"><![CDATA[$V{somaProduto}]]></textFieldExpression>
				</textField>
			</band>
		</groupFooter>
	</group>
	<title>
		<band height="105" splitType="Stretch">
			<textField evaluationTime="Report" isBlankWhenNull="false">
				<reportElement key="textField-212" x="536" y="35" width="31" height="17"/>
				<box bottomPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement>
					<font fontName="Arial" size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[" " + $V{PAGE_NUMBER} + ""]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-208" x="87" y="1" width="113" height="14"/>
				<textElement>
					<font fontName="Arial" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{nomeEmpresa}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-209" x="87" y="15" width="113" height="14"/>
				<textElement>
					<font fontName="Arial" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{enderecoEmpresa}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement key="staticText-14" mode="Opaque" x="305" y="1" width="262" height="23"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Top">
					<font fontName="Arial" size="9" isBold="true" isItalic="true" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica-BoldOblique"/>
				</textElement>
				<text><![CDATA[Zillyon - Sistema Administrativo para Academias Desenvolvido por PACTO Soluções Tecnológicas Ltda.]]></text>
			</staticText>
			<image vAlign="Middle" isUsingCache="true" onErrorType="Blank" evaluationTime="Page">
				<reportElement key="image-1" x="1" y="1" width="82" height="46" isPrintWhenDetailOverflows="true"/>
				<box>
					<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<imageExpression class="java.io.InputStream"><![CDATA[$P{logoPadraoRelatorio}]]></imageExpression>
			</image>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-211" x="454" y="35" width="75" height="17"/>
				<box bottomPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right">
					<font fontName="Arial" size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA["Página: " + $V{PAGE_NUMBER} + " de "]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement key="staticText-15" mode="Opaque" x="456" y="23" width="111" height="12"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Top">
					<font fontName="Arial" size="9" isBold="true" isItalic="true" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica-BoldOblique"/>
				</textElement>
				<text><![CDATA[(0xx62) 3251-5820]]></text>
			</staticText>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-210" x="87" y="31" width="113" height="14"/>
				<textElement>
					<font fontName="Arial" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{cidadeEmpresa}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement key="staticText-13" x="2" y="57" width="555" height="23"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="18" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Gestão da Comissão]]></text>
			</staticText>
			<textField isStretchWithOverflow="true" isBlankWhenNull="false">
				<reportElement key="textField-214" x="2" y="80" width="553" height="14"/>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="html">
					<font fontName="Arial" size="7" isBold="true" isItalic="true" pdfFontName="Helvetica-BoldOblique"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{filtros}]]></textFieldExpression>
			</textField>
		</band>
	</title>
	<columnHeader>
		<band height="15" splitType="Stretch">
			<line>
				<reportElement x="0" y="14" width="567" height="1"/>
			</line>
			<staticText>
				<reportElement key="" positionType="Float" x="26" y="0" width="41" height="14"/>
				<textElement>
					<font fontName="Arial" size="9" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Matrícula]]></text>
			</staticText>
			<staticText>
				<reportElement key="" positionType="Float" x="67" y="0" width="127" height="14"/>
				<textElement>
					<font fontName="Arial" size="9" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Cliente]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-1" positionType="Float" x="195" y="0" width="47" height="14"/>
				<textElement>
					<font fontName="Arial" size="9" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Data Aula]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-1" positionType="Float" x="242" y="0" width="50" height="14"/>
				<textElement textAlignment="Center">
					<font fontName="Arial" size="9" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Horário]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-1" positionType="Float" x="292" y="0" width="104" height="14"/>
				<textElement textAlignment="Center">
					<font fontName="Arial" size="9" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Ambiente]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-1" positionType="Float" x="396" y="0" width="78" height="14"/>
				<textElement textAlignment="Center">
					<font fontName="Arial" size="9" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Status]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-1" positionType="Float" x="474" y="0" width="23" height="14"/>
				<textElement>
					<font fontName="Arial" size="9" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[T.H.]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-1" positionType="Float" x="533" y="0" width="34" height="14"/>
				<textElement textAlignment="Right">
					<font fontName="Arial" size="9" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Comis.]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-1" positionType="Float" x="497" y="0" width="36" height="14"/>
				<textElement textAlignment="Right">
					<font fontName="Arial" size="9" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Vl. Unit.]]></text>
			</staticText>
		</band>
	</columnHeader>
	<detail>
		<band height="11" splitType="Stretch">
			<textField pattern="dd/MM/yyyy">
				<reportElement x="194" y="0" width="48" height="11"/>
				<textElement>
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression class="java.util.Date"><![CDATA[$F{dataAula}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="26" y="0" width="41" height="11"/>
				<textElement>
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.Integer"><![CDATA[$F{codgMatricula}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="67" y="0" width="127" height="11"/>
				<textElement>
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{descCliente}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="292" y="0" width="104" height="11"/>
				<textElement>
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{descAmbiente}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="396" y="0" width="81" height="11"/>
				<textElement>
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{descStatus}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="477" y="0" width="23" height="11"/>
				<textElement>
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{siglaTipoAula}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00;-#,##0.00" isBlankWhenNull="true">
				<reportElement x="533" y="0" width="34" height="11"/>
				<textElement textAlignment="Right">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{valorComissao}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00;-#,##0.00" isBlankWhenNull="true">
				<reportElement x="500" y="0" width="33" height="11"/>
				<textElement textAlignment="Right">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{valorUnitario}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="HH.mm" isBlankWhenNull="false">
				<reportElement x="242" y="0" width="22" height="11"/>
				<textElement>
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression class="java.sql.Time"><![CDATA[$F{horaInicio}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="HH.mm">
				<reportElement x="269" y="0" width="22" height="11">
					<printWhenExpression><![CDATA[$F{horaTermino} !=null]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression class="java.sql.Time"><![CDATA[$F{horaTermino}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement key="" positionType="Float" x="264" y="0" width="5" height="11">
					<printWhenExpression><![CDATA[$F{horaTermino} != null]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Center">
					<font fontName="Arial" size="8" isBold="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[-]]></text>
			</staticText>
		</band>
	</detail>
	<lastPageFooter>
		<band height="15">
			<rectangle>
				<reportElement mode="Transparent" x="0" y="0" width="567" height="15"/>
				<graphicElement>
					<pen lineWidth="1.0"/>
				</graphicElement>
			</rectangle>
			<textField pattern="dd/MM/yyyy HH:mm:ss" isBlankWhenNull="true">
				<reportElement key="dataRel-1" mode="Opaque" x="265" y="1" width="113" height="13" backcolor="#FFFFFF"/>
				<box bottomPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="false" isItalic="true" isUnderline="false" pdfFontName="Helvetica-Oblique" isPdfEmbedded="false"/>
				</textElement>
				<textFieldExpression class="java.util.Date"><![CDATA[new Date()]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="2" y="0" width="248" height="15"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="8" isItalic="true" pdfFontName="Helvetica-Oblique"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[" "+" Usuário: " + $P{usuario}]]></textFieldExpression>
			</textField>
		</band>
	</lastPageFooter>
	<summary>
		<band height="26" splitType="Stretch">
			<staticText>
				<reportElement key="staticText-2" positionType="Float" x="219" y="15" width="96" height="11"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[TOTAL GERAL:]]></text>
			</staticText>
			<textField isStretchWithOverflow="true" pattern="###0.00;-###0.00" isBlankWhenNull="true">
				<reportElement key="" x="483" y="15" width="36" height="11"/>
				<textElement textAlignment="Right" verticalAlignment="Middle" markup="html">
					<font fontName="Arial" size="8" isBold="true" isStrikeThrough="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.math.BigDecimal"><![CDATA[$P{totalUnitario}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="###0.00;-###0.00" isBlankWhenNull="true">
				<reportElement key="" x="533" y="15" width="34" height="11"/>
				<textElement textAlignment="Right" verticalAlignment="Middle" markup="html">
					<font fontName="Arial" size="8" isBold="true" isStrikeThrough="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.math.BigDecimal"><![CDATA[$P{totalComissao}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="430" y="15" width="45" height="10"/>
				<textElement textAlignment="Left" markup="none">
					<font fontName="Arial" size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$V{totalSessoes}.compareTo(new BigDecimal(1)) == 1 ? "SESSÕES" : "SESSÃO"]]></textFieldExpression>
			</textField>
			<textField pattern="">
				<reportElement x="402" y="15" width="24" height="10"/>
				<textElement textAlignment="Right">
					<font fontName="Arial" size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.math.BigDecimal"><![CDATA[$V{totalSessoes}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="355" y="15" width="45" height="10"/>
				<textElement textAlignment="Left" markup="none">
					<font fontName="Arial" size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{totalCliente} > 1 ? "CLIENTES" : "CLIENTE"]]></textFieldExpression>
			</textField>
			<textField pattern="">
				<reportElement x="327" y="15" width="21" height="10"/>
				<textElement textAlignment="Right">
					<font fontName="Arial" size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.Integer"><![CDATA[$P{totalCliente}]]></textFieldExpression>
			</textField>
		</band>
	</summary>
</jasperReport>

<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="geralAgendamentosEstudioPactoExcel" pageWidth="1280" pageHeight="50000" columnWidth="1280" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0">
	<property name="ireport.zoom" value="3.215383215000002"/>
	<property name="ireport.x" value="731"/>
	<property name="ireport.y" value="0"/>
	<queryString>
		<![CDATA[]]>
	</queryString>
	<field name="codgMatricula" class="java.lang.Integer"/>
	<field name="descCliente" class="java.lang.String"/>
	<field name="dataAula" class="java.util.Date"/>
	<field name="horaInicio" class="java.sql.Time"/>
	<field name="horaTermino" class="java.sql.Time"/>
	<field name="descProduto" class="java.lang.String"/>
	<field name="descColaborador" class="java.lang.String"/>
	<field name="descAmbiente" class="java.lang.String"/>
	<field name="descStatus.descricao" class="java.lang.String"/>
	<field name="descTipoHorario.sigla" class="java.lang.String"/>
	<field name="situacaoParcela.descricao" class="java.lang.String"/>
	<field name="qtdVendida" class="java.lang.Integer"/>
	<field name="qtdUtilizada" class="java.lang.Integer"/>
	<field name="qtdAgendar" class="java.lang.Integer"/>
	<field name="qtdRestante" class="java.lang.Integer"/>
	<columnHeader>
		<band height="13" splitType="Stretch">
			<staticText>
				<reportElement key="" positionType="Float" x="0" y="0" width="70" height="13"/>
				<textElement verticalAlignment="Middle">
					<font fontName="SansSerif" size="9" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Matrícula]]></text>
			</staticText>
			<staticText>
				<reportElement key="" positionType="Float" x="70" y="0" width="160" height="13"/>
				<textElement verticalAlignment="Middle">
					<font fontName="SansSerif" size="9" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Cliente]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-1" positionType="Float" x="230" y="0" width="70" height="13"/>
				<textElement verticalAlignment="Middle">
					<font fontName="SansSerif" size="9" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Data Aula]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-1" positionType="Float" x="300" y="0" width="70" height="13"/>
				<textElement verticalAlignment="Middle">
					<font fontName="SansSerif" size="9" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Horário]]></text>
			</staticText>
			<staticText>
				<reportElement key="" positionType="Float" x="370" y="0" width="120" height="13"/>
				<textElement verticalAlignment="Middle">
					<font fontName="SansSerif" size="9" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Serviço]]></text>
			</staticText>
			<staticText>
				<reportElement key="" positionType="Float" x="490" y="0" width="110" height="13"/>
				<textElement verticalAlignment="Middle">
					<font fontName="SansSerif" size="9" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Profissional]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-1" positionType="Float" x="600" y="0" width="100" height="13"/>
				<textElement verticalAlignment="Middle">
					<font fontName="SansSerif" size="9" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Ambiente]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-1" positionType="Float" x="700" y="0" width="80" height="13"/>
				<textElement verticalAlignment="Middle">
					<font fontName="SansSerif" size="9" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Status]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="780" y="0" width="60" height="13"/>
				<textElement verticalAlignment="Middle">
					<font size="9" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Tipo Horário]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="840" y="0" width="120" height="13"/>
				<textElement verticalAlignment="Middle">
					<font size="9" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Situação]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="960" y="0" width="80" height="13"/>
				<textElement verticalAlignment="Middle">
					<font size="9" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Qtd. Vendida]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="1040" y="0" width="80" height="13"/>
				<textElement verticalAlignment="Middle">
					<font size="9" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Qtd. Utilizada]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="1120" y="0" width="80" height="13"/>
				<textElement verticalAlignment="Middle">
					<font size="9" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Qtd. A Agendar]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="1200" y="0" width="80" height="13"/>
				<textElement verticalAlignment="Middle">
					<font size="9" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Qtd. Restante]]></text>
			</staticText>
		</band>
	</columnHeader>
	<detail>
		<band height="13">
			<textField>
				<reportElement positionType="Float" x="490" y="0" width="110" height="13"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="SansSerif" size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{descColaborador}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement positionType="Float" x="70" y="0" width="160" height="13"/>
				<textElement verticalAlignment="Middle">
					<font fontName="SansSerif" size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{descCliente}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement positionType="Float" x="700" y="0" width="80" height="13"/>
				<textElement verticalAlignment="Middle">
					<font fontName="SansSerif" size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{descStatus.descricao}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement positionType="Float" x="600" y="0" width="100" height="13"/>
				<textElement verticalAlignment="Middle">
					<font fontName="SansSerif" size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{descAmbiente}]]></textFieldExpression>
			</textField>
			<textField pattern="dd/MM/yyyy">
				<reportElement positionType="Float" x="230" y="0" width="70" height="13"/>
				<textElement verticalAlignment="Middle">
					<font fontName="SansSerif" size="8"/>
				</textElement>
				<textFieldExpression class="java.util.Date"><![CDATA[$F{dataAula}]]></textFieldExpression>
			</textField>
			<textField pattern="HH:mm">
				<reportElement positionType="Float" x="300" y="0" width="35" height="13"/>
				<textElement verticalAlignment="Middle">
					<font fontName="SansSerif" size="8"/>
				</textElement>
				<textFieldExpression class="java.sql.Time"><![CDATA[$F{horaInicio}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement positionType="Float" x="0" y="0" width="70" height="13"/>
				<textElement verticalAlignment="Middle">
					<font fontName="SansSerif" size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.Integer"><![CDATA[$F{codgMatricula}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement positionType="Float" x="370" y="0" width="120" height="13"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="SansSerif" size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{descProduto}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement positionType="Float" x="780" y="0" width="60" height="13"/>
				<textElement verticalAlignment="Middle">
					<font size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{descTipoHorario.sigla}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement positionType="Float" x="840" y="0" width="120" height="13"/>
				<textElement verticalAlignment="Middle">
					<font size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{situacaoParcela.descricao}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement positionType="Float" x="960" y="0" width="80" height="13"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.Integer"><![CDATA[$F{qtdVendida}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement positionType="Float" x="1040" y="0" width="80" height="13"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.Integer"><![CDATA[$F{qtdUtilizada}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement positionType="Float" x="1120" y="0" width="80" height="13"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.Integer"><![CDATA[$F{qtdAgendar}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement positionType="Float" x="1200" y="0" width="80" height="13"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.Integer"><![CDATA[$F{qtdRestante}]]></textFieldExpression>
			</textField>
			<textField pattern="HH.mm">
				<reportElement x="335" y="0" width="35" height="13">
					<printWhenExpression><![CDATA[$F{horaTermino} != null]]></printWhenExpression>
				</reportElement>
				<textElement verticalAlignment="Middle">
					<font size="8"/>
				</textElement>
				<textFieldExpression class="java.sql.Time"><![CDATA[$F{horaTermino}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
</jasperReport>

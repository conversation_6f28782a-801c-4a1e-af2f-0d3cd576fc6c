<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="geralAgendamentosEstudioPacto" pageWidth="842" pageHeight="595" orientation="Landscape" whenNoDataType="AllSectionsNoDetail" columnWidth="802" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" whenResourceMissingType="Empty">
	<property name="ireport.zoom" value="7.581705427489504"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="1194"/>
	<style name="table">
		<box>
			<pen lineWidth="1.0" lineColor="#000000"/>
		</box>
	</style>
	<style name="table_TH" mode="Opaque" backcolor="#C3CFD9">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="table_CH" mode="Opaque" backcolor="#E6F3FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="table_TD" mode="Opaque" backcolor="#FFFFFF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
		<conditionalStyle>
			<conditionExpression><![CDATA[new Boolean($V{REPORT_COUNT}.intValue()%2==0)]]></conditionExpression>
			<style backcolor="#F8FCFF"/>
		</conditionalStyle>
	</style>
	<style name="Crosstab Data Text" hAlign="Center"/>
	<parameter name="versaoSoftware" class="java.lang.String" isForPrompting="false"/>
	<parameter name="usuario" class="java.lang.String" isForPrompting="false"/>
	<parameter name="filtros" class="java.lang.String" isForPrompting="false"/>
	<parameter name="nomeEmpresa" class="java.lang.String"/>
	<parameter name="enderecoEmpresa" class="java.lang.String"/>
	<parameter name="cidadeEmpresa" class="java.lang.String"/>
	<parameter name="logoPadraoRelatorio" class="java.io.InputStream" isForPrompting="false"/>
	<parameter name="SUBREPORT_DIR" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA["D:\\PactoJ\\Desenvolvimento\\Sistemas\\ZillyonWeb\\ZillyonWeb-Work\\src\\java\\relatorio\\designRelatorio\\estudio\\"]]></defaultValueExpression>
	</parameter>
	<parameter name="listaStatus" class="java.lang.Object" isForPrompting="false"/>
	<parameter name="listaTipoHorario" class="java.lang.Object" isForPrompting="false"/>
	<parameter name="listaSituacao" class="java.lang.Object" isForPrompting="false"/>
	<field name="codgMatricula" class="java.lang.Integer"/>
	<field name="descCliente" class="java.lang.String"/>
	<field name="dataAula" class="java.util.Date"/>
	<field name="horaInicio" class="java.sql.Time"/>
	<field name="horaTermino" class="java.sql.Time"/>
	<field name="descTipoHorario.descricao" class="java.lang.String"/>
	<field name="descColaborador" class="java.lang.String"/>
	<field name="descStatus.descricao" class="java.lang.String"/>
	<field name="descProduto" class="java.lang.String"/>
	<field name="descAmbiente" class="java.lang.String"/>
	<field name="situacaoParcela.descricao" class="java.lang.String"/>
	<field name="qtdVendida" class="java.lang.Integer"/>
	<field name="qtdUtilizada" class="java.lang.Integer"/>
	<field name="qtdAgendar" class="java.lang.Integer"/>
	<field name="qtdRestante" class="java.lang.Integer"/>
	<title>
		<band height="93" splitType="Stretch">
			<image hAlign="Center" vAlign="Middle" isUsingCache="true" onErrorType="Blank" evaluationTime="Page">
				<reportElement key="image-1" x="2" y="0" width="82" height="46" isPrintWhenDetailOverflows="true" forecolor="#FFFFFF"/>
				<box>
					<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<imageExpression class="java.io.InputStream"><![CDATA[$P{logoPadraoRelatorio}]]></imageExpression>
			</image>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-208" x="88" y="2" width="113" height="14"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{nomeEmpresa}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-209" x="88" y="16" width="113" height="14"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{enderecoEmpresa}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-210" x="88" y="30" width="113" height="14"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{cidadeEmpresa}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement key="staticText-14" mode="Opaque" x="540" y="0" width="262" height="23"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="9" isBold="true" isItalic="true" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica-BoldOblique"/>
				</textElement>
				<text><![CDATA[Zillyon - Sistema Administrativo para Academias Desenvolvido por PACTO Soluções Tecnológicas Ltda.]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-15" mode="Opaque" x="691" y="22" width="111" height="12"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="9" isBold="true" isItalic="true" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica-BoldOblique"/>
				</textElement>
				<text><![CDATA[(0xx62) 3251-5820]]></text>
			</staticText>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-211" x="689" y="34" width="75" height="17"/>
				<box bottomPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA["Página: " + $V{PAGE_NUMBER} + " de "]]></textFieldExpression>
			</textField>
			<textField evaluationTime="Report" isBlankWhenNull="false">
				<reportElement key="textField-212" x="771" y="34" width="31" height="17"/>
				<box bottomPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[" " + $V{PAGE_NUMBER} + ""]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement key="staticText-13" x="201" y="28" width="388" height="25"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="18" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Gestão de Agendamentos]]></text>
			</staticText>
			<textField isStretchWithOverflow="true" isBlankWhenNull="false">
				<reportElement key="textField-214" x="2" y="60" width="800" height="14"/>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="html">
					<font fontName="Arial" size="7" isBold="true" isItalic="true" pdfFontName="Helvetica-BoldOblique"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{filtros}]]></textFieldExpression>
			</textField>
		</band>
	</title>
	<columnHeader>
		<band height="41" splitType="Stretch">
			<staticText>
				<reportElement x="124" y="22" width="50" height="18"/>
				<textElement verticalAlignment="Middle">
					<font size="10"/>
				</textElement>
				<text><![CDATA[Início]]></text>
			</staticText>
			<staticText>
				<reportElement x="264" y="0" width="180" height="22"/>
				<textElement verticalAlignment="Middle">
					<font size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[Profissional]]></text>
			</staticText>
			<staticText>
				<reportElement x="662" y="0" width="140" height="22"/>
				<textElement verticalAlignment="Middle">
					<font size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[Situação]]></text>
			</staticText>
			<staticText>
				<reportElement x="767" y="22" width="35" height="18"/>
				<textElement verticalAlignment="Middle"/>
				<text><![CDATA[Q. R.]]></text>
			</staticText>
			<staticText>
				<reportElement x="732" y="22" width="35" height="18"/>
				<textElement verticalAlignment="Middle"/>
				<text><![CDATA[Q. A.]]></text>
			</staticText>
			<staticText>
				<reportElement x="697" y="22" width="35" height="18"/>
				<textElement verticalAlignment="Middle"/>
				<text><![CDATA[Q. U.]]></text>
			</staticText>
			<staticText>
				<reportElement x="662" y="22" width="35" height="18"/>
				<textElement verticalAlignment="Middle"/>
				<text><![CDATA[Q. V.]]></text>
			</staticText>
			<staticText>
				<reportElement x="444" y="0" width="218" height="22"/>
				<textElement verticalAlignment="Middle">
					<font size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[Serviço]]></text>
			</staticText>
			<staticText>
				<reportElement x="3" y="0" width="37" height="22"/>
				<textElement verticalAlignment="Middle">
					<font size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[Mt.]]></text>
			</staticText>
			<staticText>
				<reportElement x="40" y="0" width="224" height="22"/>
				<textElement verticalAlignment="Middle">
					<font size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[Nome]]></text>
			</staticText>
			<staticText>
				<reportElement x="40" y="22" width="84" height="18"/>
				<textElement verticalAlignment="Middle">
					<font size="10"/>
				</textElement>
				<text><![CDATA[Data]]></text>
			</staticText>
			<staticText>
				<reportElement x="264" y="22" width="180" height="18"/>
				<textElement verticalAlignment="Middle"/>
				<text><![CDATA[Status]]></text>
			</staticText>
			<staticText>
				<reportElement x="174" y="22" width="50" height="18"/>
				<textElement verticalAlignment="Middle">
					<font size="10"/>
				</textElement>
				<text><![CDATA[Fim]]></text>
			</staticText>
			<staticText>
				<reportElement x="224" y="22" width="40" height="18"/>
				<textElement verticalAlignment="Middle">
					<font size="10"/>
				</textElement>
				<text><![CDATA[T. H.]]></text>
			</staticText>
			<staticText>
				<reportElement x="444" y="22" width="218" height="18"/>
				<textElement verticalAlignment="Middle"/>
				<text><![CDATA[Ambiente]]></text>
			</staticText>
			<line>
				<reportElement x="0" y="39" width="802" height="1"/>
			</line>
		</band>
	</columnHeader>
	<detail>
		<band height="40" splitType="Stretch">
			<rectangle>
				<reportElement key="rectangle-1" x="0" y="0" width="802" height="40" forecolor="#FFFFFF" backcolor="#E1E1E1">
					<printWhenExpression><![CDATA[new Boolean(($V{COLUMN_COUNT}.intValue()%2)==0)]]></printWhenExpression>
				</reportElement>
			</rectangle>
			<textField isBlankWhenNull="true">
				<reportElement x="672" y="0" width="130" height="22"/>
				<textElement verticalAlignment="Middle">
					<font size="11"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{situacaoParcela.descricao}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="264" y="0" width="180" height="22"/>
				<textElement verticalAlignment="Middle">
					<font size="11"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{descColaborador}]]></textFieldExpression>
			</textField>
			<textField pattern="HH:mm" isBlankWhenNull="true">
				<reportElement x="124" y="22" width="50" height="18"/>
				<textElement verticalAlignment="Middle">
					<font size="9"/>
				</textElement>
				<textFieldExpression class="java.sql.Time"><![CDATA[$F{horaInicio}]]></textFieldExpression>
			</textField>
			<textField pattern="dd/MM/yyyy" isBlankWhenNull="true">
				<reportElement x="40" y="22" width="84" height="18"/>
				<textElement verticalAlignment="Middle">
					<font size="9"/>
				</textElement>
				<textFieldExpression class="java.util.Date"><![CDATA[$F{dataAula}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="3" y="0" width="37" height="22"/>
				<textElement verticalAlignment="Middle">
					<font size="11"/>
				</textElement>
				<textFieldExpression class="java.lang.Integer"><![CDATA[$F{codgMatricula}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="40" y="0" width="224" height="22"/>
				<textElement verticalAlignment="Middle">
					<font size="11"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{descCliente}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="662" y="22" width="35" height="18"/>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="SansSerif" isBold="true"/>
				</textElement>
				<textFieldExpression class="java.lang.Integer"><![CDATA[$F{qtdVendida}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="697" y="22" width="35" height="18"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="SansSerif" size="10" isBold="true"/>
				</textElement>
				<textFieldExpression class="java.lang.Integer"><![CDATA[$F{qtdUtilizada}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="732" y="22" width="35" height="18"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="SansSerif" isBold="true"/>
				</textElement>
				<textFieldExpression class="java.lang.Integer"><![CDATA[$F{qtdAgendar}]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement x="767" y="22" width="35" height="18"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="SansSerif" size="10" isBold="true"/>
				</textElement>
				<textFieldExpression class="java.lang.Integer"><![CDATA[$F{qtdRestante}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="264" y="22" width="180" height="18"/>
				<textElement verticalAlignment="Middle">
					<font size="9"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{descStatus.descricao}]]></textFieldExpression>
			</textField>
			<textField pattern="HH:mm" isBlankWhenNull="true">
				<reportElement x="174" y="22" width="50" height="18"/>
				<textElement verticalAlignment="Middle">
					<font size="9"/>
				</textElement>
				<textFieldExpression class="java.sql.Time"><![CDATA[$F{horaTermino}]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement x="224" y="22" width="40" height="18"/>
				<textElement verticalAlignment="Middle">
					<font size="9"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{descTipoHorario.descricao}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="444" y="0" width="218" height="22"/>
				<textElement verticalAlignment="Middle">
					<font size="11"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{descProduto}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="444" y="22" width="218" height="18"/>
				<textElement verticalAlignment="Middle">
					<font size="9"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{descAmbiente}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<lastPageFooter>
		<band height="200">
			<textField pattern="dd/MM/yyyy HH:mm:ss" isBlankWhenNull="true">
				<reportElement key="dataRel-1" positionType="FixRelativeToBottom" mode="Opaque" x="472" y="185" width="329" height="15" backcolor="#FFFFFF"/>
				<box bottomPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="false" isItalic="true" isUnderline="false" pdfFontName="Helvetica-Oblique" isPdfEmbedded="false"/>
				</textElement>
				<textFieldExpression class="java.util.Date"><![CDATA[new Date()]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement positionType="FixRelativeToBottom" x="0" y="185" width="462" height="15" backcolor="#DCDCDC"/>
				<textElement textAlignment="Right" verticalAlignment="Middle"/>
				<textFieldExpression class="java.lang.String"><![CDATA[" Usuário: " + $P{usuario}]]></textFieldExpression>
			</textField>
			<subreport>
				<reportElement positionType="Float" stretchType="RelativeToTallestObject" x="72" y="51" width="190" height="60"/>
				<dataSourceExpression><![CDATA[$P{listaStatus}]]></dataSourceExpression>
				<subreportExpression class="java.lang.String"><![CDATA[$P{SUBREPORT_DIR} + "totalizadorStatus.jasper"]]></subreportExpression>
			</subreport>
			<subreport>
				<reportElement positionType="Float" stretchType="RelativeToTallestObject" x="272" y="51" width="190" height="60"/>
				<dataSourceExpression><![CDATA[$P{listaTipoHorario}]]></dataSourceExpression>
				<subreportExpression class="java.lang.String"><![CDATA[$P{SUBREPORT_DIR} + "totalizadorTipoHorario.jasper"]]></subreportExpression>
			</subreport>
			<subreport>
				<reportElement positionType="Float" stretchType="RelativeToTallestObject" x="472" y="51" width="190" height="60"/>
				<dataSourceExpression><![CDATA[$P{listaSituacao}]]></dataSourceExpression>
				<subreportExpression class="java.lang.String"><![CDATA[$P{SUBREPORT_DIR} + "totalizadorSituacao.jasper"]]></subreportExpression>
			</subreport>
		</band>
	</lastPageFooter>
</jasperReport>

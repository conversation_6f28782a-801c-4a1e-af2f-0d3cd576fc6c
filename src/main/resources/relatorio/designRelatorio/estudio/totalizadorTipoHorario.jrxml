<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="totalizadorStatus" pageWidth="190" pageHeight="5000" columnWidth="190" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0">
	<property name="ireport.zoom" value="2.3579476910000046"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<subDataset name="dataset1"/>
	<field name="descTipoHorario_Apresentar" class="java.lang.String"/>
	<field name="totalizador" class="java.lang.Integer"/>
	<columnHeader>
		<band height="15" splitType="Stretch">
			<staticText>
				<reportElement x="0" y="0" width="150" height="15"/>
				<textElement verticalAlignment="Middle">
					<font size="10" isBold="true"/>
				</textElement>
				<text><![CDATA[ Tipo Horário]]></text>
			</staticText>
			<staticText>
				<reportElement x="150" y="0" width="38" height="15"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="10" isBold="true"/>
				</textElement>
				<text><![CDATA[Qtd.]]></text>
			</staticText>
			<line>
				<reportElement x="0" y="14" width="190" height="1"/>
			</line>
		</band>
	</columnHeader>
	<detail>
		<band height="15" splitType="Stretch">
			<rectangle>
				<reportElement x="0" y="0" width="150" height="15" forecolor="#FFFFFF" backcolor="#E1E1E1">
					<printWhenExpression><![CDATA[new Boolean(($V{COLUMN_COUNT}.intValue()%2)==0)]]></printWhenExpression>
				</reportElement>
				<graphicElement>
					<pen lineWidth="0.0" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="150" y="0" width="40" height="15" forecolor="#FFFFFF" backcolor="#E1E1E1">
					<printWhenExpression><![CDATA[new Boolean(($V{COLUMN_COUNT}.intValue()%2)==0)]]></printWhenExpression>
				</reportElement>
				<graphicElement>
					<pen lineWidth="0.0" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<textField>
				<reportElement x="0" y="0" width="150" height="15"/>
				<textElement verticalAlignment="Middle"/>
				<textFieldExpression class="java.lang.String"><![CDATA[ $F{descTipoHorario_Apresentar}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="150" y="0" width="38" height="15"/>
				<textElement textAlignment="Right" verticalAlignment="Middle"/>
				<textFieldExpression class="java.lang.Integer"><![CDATA[$F{totalizador}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<pageFooter>
		<band height="1">
			<line>
				<reportElement x="0" y="0" width="190" height="1"/>
			</line>
		</band>
	</pageFooter>
</jasperReport>

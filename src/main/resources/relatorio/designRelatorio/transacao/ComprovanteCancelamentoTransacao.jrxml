<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="ComprovanteCancelamentoTransacao" pageWidth="595" pageHeight="880" whenNoDataType="AllSectionsNoDetail" columnWidth="591" leftMargin="2" rightMargin="2" topMargin="2" bottomMargin="2" whenResourceMissingType="Empty">
	<property name="ireport.zoom" value="1.5"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="275"/>
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.encoding" value="ISO-8859-1"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="java.util.*"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<parameter name="SUBREPORT_DIR" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA["C:\\PactoJ\\Desenvolvimento\\Sistemas\\ZillyonWeb\\ZW_Tronco\\src\\main\\resources\\relatorio\\designRelatorio\\nfse\\"]]></defaultValueExpression>
	</parameter>
	<queryString>
		<![CDATA[]]>
	</queryString>
	<field name="empresaNome" class="java.lang.String"/>
	<field name="empresaEndereco1" class="java.lang.String"/>
	<field name="numeroCartao" class="java.lang.String"/>
	<field name="dataVenda" class="java.lang.String"/>
	<field name="valorVenda" class="java.lang.String"/>
	<field name="valorCancelado" class="java.lang.String"/>
	<field name="codigoAutorizacao" class="java.lang.String"/>
	<field name="codigoExterno" class="java.lang.String"/>
	<field name="empresaEndereco2" class="java.lang.String"/>
	<field name="empresaEndereco3" class="java.lang.String"/>
	<field name="tid" class="java.lang.String"/>
	<field name="tipoTransacao" class="java.lang.String"/>
	<field name="logomarca" class="java.lang.String"/>
	<field name="mensagemInformacao" class="java.lang.String"/>
	<field name="dataCancelamento" class="java.lang.String"/>
	<field name="nsu" class="java.lang.String"/>
	<field name="mensagemInfoEstorno" class="java.lang.String"/>
	<detail>
		<band height="568" splitType="Prevent">
			<image>
				<reportElement x="9" y="23" width="157" height="49">
					<printWhenExpression><![CDATA[!$F{logomarca}.isEmpty()]]></printWhenExpression>
				</reportElement>
				<imageExpression class="java.lang.String"><![CDATA[$P{SUBREPORT_DIR} + $F{logomarca}]]></imageExpression>
			</image>
			<staticText>
				<reportElement key="staticText-16" x="9" y="23" width="566" height="66">
					<printWhenExpression><![CDATA[$F{logomarca}.isEmpty()]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font size="20" isBold="true" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Comprovante de Cancelamento de Transação]]></text>
			</staticText>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-212" x="9" y="100" width="566" height="14"/>
				<textElement>
					<font isBold="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{empresaNome}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-212" x="9" y="115" width="566" height="14"/>
				<textElement>
					<font isBold="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{empresaEndereco1}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-212" x="9" y="130" width="566" height="14"/>
				<textElement>
					<font isBold="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{empresaEndereco2}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement key="staticText-16" x="9" y="194" width="352" height="14"/>
				<textElement>
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Ref.: Cancelamento de Venda]]></text>
			</staticText>
			<rectangle>
				<reportElement x="11" y="221" width="181" height="14"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="11" y="249" width="181" height="14"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="11" y="263" width="181" height="14"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="11" y="277" width="181" height="14"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="11" y="291" width="181" height="14"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="192" y="221" width="385" height="14"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="192" y="235" width="385" height="14"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="192" y="249" width="385" height="14"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="192" y="263" width="385" height="14"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="192" y="277" width="385" height="14"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="192" y="291" width="385" height="14"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="11" y="235" width="181" height="14"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<staticText>
				<reportElement key="staticText-16" positionType="Float" x="14" y="221" width="178" height="14"/>
				<textElement>
					<font isBold="true" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[TID:]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-16" positionType="Float" x="14" y="235" width="178" height="14"/>
				<textElement>
					<font isBold="true" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Nº DO CARTÃO:]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-16" positionType="Float" x="14" y="249" width="178" height="14"/>
				<textElement>
					<font isBold="true" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[DATA DA VENDA:]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-16" positionType="Float" x="14" y="263" width="178" height="14"/>
				<textElement>
					<font isBold="true" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[VALOR DA VENDA:]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-16" positionType="Float" x="14" y="277" width="178" height="14"/>
				<textElement>
					<font isBold="true" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[VALOR CANCELADO:]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-16" positionType="Float" x="14" y="291" width="178" height="14"/>
				<textElement>
					<font isBold="true" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[CÓDIGO DE AUTORIZAÇÃO:]]></text>
			</staticText>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-212" x="195" y="221" width="380" height="14"/>
				<textElement>
					<font isBold="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{tid}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-212" x="195" y="235" width="380" height="14"/>
				<textElement>
					<font isBold="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{numeroCartao}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-212" x="195" y="249" width="380" height="14"/>
				<textElement>
					<font isBold="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{dataVenda}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-212" x="195" y="263" width="380" height="14"/>
				<textElement>
					<font isBold="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{valorVenda}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-212" x="195" y="277" width="380" height="14"/>
				<textElement>
					<font isBold="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{valorCancelado}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-212" x="195" y="291" width="380" height="14"/>
				<textElement>
					<font isBold="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{codigoAutorizacao}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-212" x="9" y="318" width="561" height="28"/>
				<textElement>
					<font isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{mensagemInfoEstorno}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-212" x="9" y="356" width="566" height="90"/>
				<textElement>
					<font isBold="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{mensagemInformacao}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
</jasperReport>

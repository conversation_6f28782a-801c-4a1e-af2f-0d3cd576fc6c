<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="SituacaoRenovacaoAnaliticoDWRel" pageWidth="842" pageHeight="595" orientation="Landscape" columnWidth="802" leftMargin="20" rightMargin="20" topMargin="10" bottomMargin="10" whenResourceMissingType="Empty">
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.encoding" value="ISO-8859-1"/>
	<property name="ireport.zoom" value="1.7715610000000008"/>
	<property name="ireport.x" value="3"/>
	<property name="ireport.y" value="0"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="java.util.*"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<subDataset name="Teste"/>
	<parameter name="logoPadraoRelatorio" class="java.io.InputStream" isForPrompting="false"/>
	<parameter name="tituloRelatorio" class="java.lang.String" isForPrompting="false"/>
	<parameter name="nomeEmpresa" class="java.lang.String" isForPrompting="false"/>
	<parameter name="versaoSoftware" class="java.lang.String" isForPrompting="false"/>
	<parameter name="usuario" class="java.lang.String" isForPrompting="false"/>
	<parameter name="filtros" class="java.lang.String" isForPrompting="false"/>
	<parameter name="enderecoEmpresa" class="java.lang.String" isForPrompting="false"/>
	<parameter name="cidadeEmpresa" class="java.lang.String" isForPrompting="false"/>
	<field name="cliente.matricula" class="java.lang.String">
		<property name="matricula" value="situacaoContratoAnaliticoDW.cliente.matricula"/>
	</field>
	<field name="cliente.nome" class="java.lang.String"/>
	<field name="contrato.dataPrevistaRenovar_Apresentar" class="java.lang.String"/>
	<field name="contrato.contratoDuracao.numeroMeses" class="java.lang.Integer"/>
	<field name="consultor.pessoa.nome" class="java.lang.String"/>
	<field name="situacaoRenovacao" class="java.lang.String"/>
	<field name="foneCliente" class="java.lang.String"/>
	<field name="contrato.dataRenovarRealizada_Apresentar" class="java.lang.String"/>
	<field name="contrato.duracaoRenovacao" class="java.lang.Integer"/>
	<field name="valorBaseCalculoApresentar" class="java.lang.String"/>
	<variable name="qtd_RenovacaoAtrasada" class="java.lang.Integer" calculation="Sum">
		<variableExpression><![CDATA[(($F{situacaoRenovacao}.equals("RT")) ? new Integer(1) : new Integer(0) )]]></variableExpression>
	</variable>
	<variable name="qtd_RenovacaoDia" class="java.lang.Integer" calculation="Sum">
		<variableExpression><![CDATA[( ($F{situacaoRenovacao}.equals("ND")) ? new Integer(1) : new Integer(0) )]]></variableExpression>
	</variable>
	<variable name="qtd_RenovacaoAntecipadas" class="java.lang.Integer" calculation="Sum">
		<variableExpression><![CDATA[(( $F{situacaoRenovacao}.equals("AN")) ? new Integer(1) : new Integer(0) )]]></variableExpression>
	</variable>
	<variable name="qtd_NRenovadosAVencer" class="java.lang.Integer" calculation="Sum">
		<variableExpression><![CDATA[( ($F{situacaoRenovacao}.equals("RA"))? new Integer(1) : new Integer(0) )]]></variableExpression>
	</variable>
	<variable name="qtd_NRenovadosDesistentes" class="java.lang.Integer" calculation="Sum">
		<variableExpression><![CDATA[( ($F{situacaoRenovacao}.equals("RD")) ? new Integer(1) : new Integer(0) )]]></variableExpression>
	</variable>
	<variable name="qtd_NRenovadosVencidos" class="java.lang.Integer" calculation="Sum">
		<variableExpression><![CDATA[( ($F{situacaoRenovacao}.equals("RV")) ? new Integer(1) : new Integer(0) )]]></variableExpression>
	</variable>
	<variable name="totalNRenovados" class="java.lang.Integer" calculation="Sum">
		<variableExpression><![CDATA[( ($F{situacaoRenovacao}.equals("RA")||
  $F{situacaoRenovacao}.equals("RD")||
  $F{situacaoRenovacao}.equals("RV")) ? new Integer(1) : new Integer(0) )]]></variableExpression>
	</variable>
	<variable name="totalRenovacoes" class="java.lang.Integer" calculation="Sum">
		<variableExpression><![CDATA[( ($F{situacaoRenovacao}.equals("RT")||
  $F{situacaoRenovacao}.equals("ND")||
  $F{situacaoRenovacao}.equals("AN")) ? new Integer(1) : new Integer(0) )]]></variableExpression>
	</variable>
	<group name="renovacaoAnalitico">
		<groupExpression><![CDATA[]]></groupExpression>
		<groupHeader>
			<band splitType="Stretch"/>
		</groupHeader>
		<groupFooter>
			<band height="84" splitType="Stretch">
				<line>
					<reportElement key="line-1" x="0" y="10" width="800" height="1"/>
				</line>
				<staticText>
					<reportElement key="staticText-16" x="493" y="16" width="90" height="17"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="8" isBold="false" pdfFontName="Helvetica"/>
					</textElement>
					<text><![CDATA[Renovação Atrasada]]></text>
				</staticText>
				<staticText>
					<reportElement key="staticText-17" x="493" y="33" width="90" height="17"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="8" isBold="false" pdfFontName="Helvetica"/>
					</textElement>
					<text><![CDATA[Renovação do Dia]]></text>
				</staticText>
				<staticText>
					<reportElement key="staticText-18" x="493" y="50" width="90" height="17"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="8" isBold="false" pdfFontName="Helvetica"/>
					</textElement>
					<text><![CDATA[Renovação Antecipada]]></text>
				</staticText>
				<staticText>
					<reportElement key="staticText-19" x="493" y="67" width="90" height="17"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="8" isBold="false" pdfFontName="Helvetica"/>
					</textElement>
					<text><![CDATA[Total de Renovações]]></text>
				</staticText>
				<staticText>
					<reportElement key="staticText-20" x="650" y="16" width="106" height="17"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="8" isBold="false" pdfFontName="Helvetica"/>
					</textElement>
					<text><![CDATA[Não Renovados a Vencer]]></text>
				</staticText>
				<staticText>
					<reportElement key="staticText-21" x="650" y="33" width="106" height="17"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="8" isBold="false" pdfFontName="Helvetica"/>
					</textElement>
					<text><![CDATA[Não Renovados Desistentes]]></text>
				</staticText>
				<staticText>
					<reportElement key="staticText-22" x="650" y="50" width="106" height="17"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="8" isBold="false" pdfFontName="Helvetica"/>
					</textElement>
					<text><![CDATA[Não Renovados Vencidos]]></text>
				</staticText>
				<staticText>
					<reportElement key="staticText-23" x="650" y="67" width="106" height="17"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="8" isBold="false" pdfFontName="Helvetica"/>
					</textElement>
					<text><![CDATA[Total de Não Renovados]]></text>
				</staticText>
				<textField isBlankWhenNull="false">
					<reportElement key="textField" x="585" y="16" width="41" height="17"/>
					<textElement textAlignment="Right" verticalAlignment="Middle"/>
					<textFieldExpression class="java.lang.Integer"><![CDATA[$V{qtd_RenovacaoAtrasada}]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="false">
					<reportElement key="textField" x="585" y="33" width="41" height="17"/>
					<textElement textAlignment="Right" verticalAlignment="Middle"/>
					<textFieldExpression class="java.lang.Integer"><![CDATA[$V{qtd_RenovacaoDia}]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="false">
					<reportElement key="textField" x="585" y="50" width="41" height="17"/>
					<textElement textAlignment="Right" verticalAlignment="Middle"/>
					<textFieldExpression class="java.lang.Integer"><![CDATA[$V{qtd_RenovacaoAntecipadas}]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="false">
					<reportElement key="textField" x="756" y="16" width="44" height="17"/>
					<textElement textAlignment="Right" verticalAlignment="Middle"/>
					<textFieldExpression class="java.lang.Integer"><![CDATA[$V{qtd_NRenovadosAVencer}]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="false">
					<reportElement key="textField" x="756" y="33" width="44" height="17"/>
					<textElement textAlignment="Right" verticalAlignment="Middle"/>
					<textFieldExpression class="java.lang.Integer"><![CDATA[$V{qtd_NRenovadosDesistentes}]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="false">
					<reportElement key="textField" x="756" y="50" width="44" height="17"/>
					<textElement textAlignment="Right" verticalAlignment="Middle"/>
					<textFieldExpression class="java.lang.Integer"><![CDATA[$V{qtd_NRenovadosVencidos}]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="false">
					<reportElement key="textField" x="756" y="67" width="44" height="17"/>
					<textElement textAlignment="Right" verticalAlignment="Middle"/>
					<textFieldExpression class="java.lang.Integer"><![CDATA[$V{totalNRenovados}]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="false">
					<reportElement key="textField" x="585" y="67" width="41" height="17"/>
					<textElement textAlignment="Right" verticalAlignment="Middle"/>
					<textFieldExpression class="java.lang.Integer"><![CDATA[$V{totalRenovacoes}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement x="0" y="16" width="140" height="14"/>
					<textElement>
						<font size="9"/>
					</textElement>
					<text><![CDATA[AN - Renovações Antecipadas]]></text>
				</staticText>
				<staticText>
					<reportElement x="0" y="33" width="139" height="14"/>
					<textElement>
						<font size="9"/>
					</textElement>
					<text><![CDATA[RT - Renovações Atrasadas]]></text>
				</staticText>
				<staticText>
					<reportElement x="0" y="50" width="140" height="14"/>
					<textElement>
						<font size="9"/>
					</textElement>
					<text><![CDATA[ND - Renovações Dia]]></text>
				</staticText>
				<staticText>
					<reportElement x="155" y="16" width="140" height="14"/>
					<textElement>
						<font size="9"/>
					</textElement>
					<text><![CDATA[RD - Desistentes]]></text>
				</staticText>
				<staticText>
					<reportElement x="155" y="33" width="140" height="14"/>
					<textElement>
						<font size="9"/>
					</textElement>
					<text><![CDATA[RA - A Vencer]]></text>
				</staticText>
				<staticText>
					<reportElement x="155" y="50" width="140" height="14"/>
					<textElement>
						<font size="9"/>
					</textElement>
					<text><![CDATA[RV - Vencidos]]></text>
				</staticText>
				<staticText>
					<reportElement x="155" y="67" width="140" height="14"/>
					<textElement>
						<font size="9"/>
					</textElement>
					<text><![CDATA[CA - Cancelados]]></text>
				</staticText>
				<staticText>
					<reportElement x="0" y="67" width="140" height="14"/>
					<textElement>
						<font size="9"/>
					</textElement>
					<text><![CDATA[TR - Trancados]]></text>
				</staticText>
			</band>
		</groupFooter>
	</group>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band splitType="Stretch"/>
	</title>
	<pageHeader>
		<band height="108" splitType="Stretch">
			<staticText>
				<reportElement key="staticText-3" x="65" y="92" width="149" height="14"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Nome]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-4" x="239" y="92" width="60" height="14"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Previsão]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-5" x="309" y="92" width="42" height="14"/>
				<textElement verticalAlignment="Middle">
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Duração]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-6" x="493" y="92" width="70" height="14"/>
				<textElement verticalAlignment="Middle">
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Duração Ren.]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-7" x="563" y="92" width="118" height="14"/>
				<textElement verticalAlignment="Middle">
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Consultor]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-8" x="681" y="92" width="118" height="14"/>
				<textElement verticalAlignment="Middle">
					<font size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Telefone]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-12" x="0" y="92" width="60" height="14"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Matrícula]]></text>
			</staticText>
			<line>
				<reportElement key="line-2" x="0" y="106" width="800" height="1"/>
			</line>
			<staticText>
				<reportElement key="staticText-25" x="214" y="92" width="25" height="14"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Sit.]]></text>
			</staticText>
			<image isUsingCache="true" onErrorType="Blank" evaluationTime="Page">
				<reportElement key="image-1" x="1" y="2" width="82" height="46" isPrintWhenDetailOverflows="true"/>
				<box>
					<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<imageExpression class="java.io.InputStream"><![CDATA[$P{logoPadraoRelatorio}]]></imageExpression>
			</image>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-208" x="86" y="1" width="175" height="14"/>
				<textElement>
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{nomeEmpresa}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-209" x="86" y="17" width="175" height="14"/>
				<textElement>
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{enderecoEmpresa}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-210" x="86" y="33" width="175" height="14"/>
				<textElement>
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{cidadeEmpresa}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement key="staticText-13" stretchType="RelativeToBandHeight" x="261" y="17" width="290" height="27"/>
				<textElement textAlignment="Center">
					<font size="18" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Previsão de Renovação]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-14" mode="Opaque" x="538" y="1" width="262" height="23"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Top" lineSpacing="Single">
					<font fontName="Microsoft Sans Serif" size="9" isBold="true" isItalic="true" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica-BoldOblique"/>
				</textElement>
				<text><![CDATA[Zillyon - Sistema Administrativo para Academias Desenvolvido por PACTO Soluções Tecnológicas Ltda.]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-15" mode="Opaque" x="689" y="24" width="111" height="12"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Top" lineSpacing="Single">
					<font fontName="Microsoft Sans Serif" size="9" isBold="true" isItalic="true" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica-BoldOblique"/>
				</textElement>
				<text><![CDATA[(0xx62) 3251-5820]]></text>
			</staticText>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-211" x="695" y="37" width="75" height="17"/>
				<box bottomPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right">
					<font fontName="Arial" size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA["Página: " + $V{PAGE_NUMBER} + " de "]]></textFieldExpression>
			</textField>
			<textField evaluationTime="Report" isBlankWhenNull="false">
				<reportElement key="textField-212" x="770" y="37" width="31" height="17"/>
				<box bottomPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement>
					<font fontName="Arial" size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[" " + $V{PAGE_NUMBER} + ""]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-214" x="1" y="57" width="800" height="30"/>
				<box>
					<topPen lineWidth="0.5" lineStyle="Solid"/>
					<leftPen lineWidth="0.5" lineStyle="Solid"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.5" lineStyle="Solid"/>
				</box>
				<textElement textAlignment="Center">
					<font fontName="Arial" size="10" isBold="true" isItalic="true" pdfFontName="Helvetica-BoldOblique"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{filtros}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement key="staticText-4" x="433" y="92" width="60" height="14"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Renovado]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-5" x="362" y="92" width="71" height="14"/>
				<textElement verticalAlignment="Middle">
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Valor Contrato]]></text>
			</staticText>
		</band>
	</pageHeader>
	<columnHeader>
		<band splitType="Stretch"/>
	</columnHeader>
	<detail>
		<band height="16" splitType="Stretch">
			<rectangle>
				<reportElement key="rectangle-1" x="1" y="1" width="799" height="14" forecolor="#B4CDCD" backcolor="#B4CDCD">
					<printWhenExpression><![CDATA[new Boolean(($V{COLUMN_COUNT}.intValue()%2)==0)]]></printWhenExpression>
				</reportElement>
			</rectangle>
			<textField isBlankWhenNull="false">
				<reportElement key="textField" x="0" y="0" width="60" height="14"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="9"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{cliente.matricula}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField" x="65" y="0" width="149" height="14"/>
				<textElement verticalAlignment="Middle">
					<font size="9"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{cliente.nome}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField" x="239" y="0" width="60" height="14"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="9"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{contrato.dataPrevistaRenovar_Apresentar}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField" x="563" y="1" width="118" height="14"/>
				<textElement verticalAlignment="Middle">
					<font size="9"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{consultor.pessoa.nome}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-213" x="493" y="0" width="70" height="14">
					<printWhenExpression><![CDATA[$F{contrato.duracaoRenovacao} > 0]]></printWhenExpression>
				</reportElement>
				<textElement verticalAlignment="Middle">
					<font size="9"/>
				</textElement>
				<textFieldExpression class="java.lang.Integer"><![CDATA[$F{contrato.duracaoRenovacao}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField" x="681" y="0" width="116" height="14"/>
				<textElement verticalAlignment="Middle">
					<font size="9"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{foneCliente}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField" x="433" y="0" width="60" height="14"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="9"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{contrato.dataRenovarRealizada_Apresentar}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField" x="214" y="1" width="25" height="14"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="9"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{situacaoRenovacao}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField" x="309" y="0" width="42" height="14"/>
				<textElement verticalAlignment="Middle">
					<font size="9"/>
				</textElement>
				<textFieldExpression class="java.lang.Integer"><![CDATA[$F{contrato.contratoDuracao.numeroMeses}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField" x="362" y="0" width="71" height="14"/>
				<textElement verticalAlignment="Middle">
					<font size="9"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{valorBaseCalculoApresentar}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<columnFooter>
		<band splitType="Stretch"/>
	</columnFooter>
	<pageFooter>
		<band height="34" splitType="Stretch">
			<textField pattern="" isBlankWhenNull="false">
				<reportElement key="textField-207" x="1" y="5" width="800" height="19"/>
				<box>
					<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="8" isItalic="true" pdfFontName="Helvetica-Oblique"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[" "+" Usuário:" + $P{usuario}]]></textFieldExpression>
			</textField>
			<textField pattern="dd/MM/yyyy HH.mm.ss" isBlankWhenNull="true">
				<reportElement key="dataRel-1" mode="Opaque" x="671" y="8" width="126" height="13" backcolor="#FFFFFF"/>
				<box bottomPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle" lineSpacing="Single">
					<font fontName="Arial" size="8" isBold="false" isItalic="true" isUnderline="false" pdfFontName="Helvetica-Oblique"/>
				</textElement>
				<textFieldExpression class="java.util.Date"><![CDATA[new Date()]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement key="staticText-3" x="649" y="7" width="23" height="13"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="false" isItalic="true" pdfFontName="Helvetica-Oblique"/>
				</textElement>
				<text><![CDATA[Data:]]></text>
			</staticText>
		</band>
	</pageFooter>
	<summary>
		<band splitType="Stretch"/>
	</summary>
</jasperReport>

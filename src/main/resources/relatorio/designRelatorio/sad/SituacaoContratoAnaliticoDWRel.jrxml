<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="SituacaoContratoAnaliticoDWRel" pageWidth="842" pageHeight="595" orientation="Landscape" columnWidth="838" leftMargin="2" rightMargin="2" topMargin="2" bottomMargin="2" whenResourceMissingType="Empty">
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.encoding" value="ISO-8859-1"/>
	<property name="ireport.zoom" value="1.0"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="java.util.*"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<subDataset name="Teste"/>
	<parameter name="logoPadraoRelatorio" class="java.io.InputStream" isForPrompting="false"/>
	<parameter name="tituloRelatorio" class="java.lang.String" isForPrompting="false"/>
	<parameter name="nomeEmpresa" class="java.lang.String" isForPrompting="false"/>
	<parameter name="versaoSoftware" class="java.lang.String" isForPrompting="false"/>
	<parameter name="usuario" class="java.lang.String" isForPrompting="false"/>
	<parameter name="filtros" class="java.lang.String" isForPrompting="false"/>
	<parameter name="enderecoEmpresa" class="java.lang.String" isForPrompting="false"/>
	<parameter name="cidadeEmpresa" class="java.lang.String" isForPrompting="false"/>
	<field name="cliente.matricula" class="java.lang.String">
		<property name="matricula" value="situacaoContratoAnaliticoDW.cliente.matricula"/>
	</field>
	<field name="cliente.pessoa.nome" class="java.lang.String"/>
	<field name="foneCliente" class="java.lang.String"/>
	<field name="emailCliente" class="java.lang.String"/>
	<field name="cliente.situacao" class="java.lang.String"/>
	<field name="contrato.situacaoContrato_Apresentar" class="java.lang.String"/>
	<field name="contrato.contratoDuracao.numeroMeses" class="java.lang.Integer"/>
	<field name="modalidadeCliente" class="java.lang.String"/>
	<field name="plano.descricao" class="java.lang.String"/>
	<field name="contrato.vigenciaAteAjustada_Apresentar" class="java.lang.String"/>
	<field name="situacao" class="java.lang.String"/>
	<variable name="ativo" class="java.lang.Integer" calculation="Sum">
		<variableExpression><![CDATA[( ($F{situacao}.equals("NO") ||
  $F{situacao}.equals("AT") ||
  $F{situacao}.equals("TV") ||
  $F{situacao}.equals("AV") ||
  $F{situacao}.equals("VE") ||
  $F{situacao}.equals("TR"))
   ? new Integer(1) : new Integer(0) )]]></variableExpression>
	</variable>
	<variable name="visitante" class="java.lang.Integer" calculation="Sum">
		<variableExpression><![CDATA[( ($F{situacao}.equals("VI") ||
   $F{situacao}.equals("PL")||
   $F{situacao}.equals("AA")||
   $F{situacao}.equals("DI")
     ) ? new Integer(1) : new Integer(0) )]]></variableExpression>
	</variable>
	<variable name="intativo" class="java.lang.Integer" calculation="Sum">
		<variableExpression><![CDATA[( ($F{situacao}.equals("IN") ||
   $F{situacao}.equals("CA")||
   $F{situacao}.equals("DE")) ? new Integer(1) : new Integer(0) )]]></variableExpression>
	</variable>
	<variable name="total" class="java.lang.Integer" calculation="Count">
		<variableExpression><![CDATA[$F{situacao}]]></variableExpression>
	</variable>
	<group name="clienteAnalitico">
		<groupExpression><![CDATA[]]></groupExpression>
		<groupHeader>
			<band height="24" splitType="Stretch">
				<staticText>
					<reportElement key="staticText-3" x="67" y="2" width="50" height="17"/>
					<textElement textAlignment="Left">
						<font size="11" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<text><![CDATA[ Nome]]></text>
				</staticText>
				<staticText>
					<reportElement key="staticText-4" x="267" y="2" width="63" height="17"/>
					<textElement textAlignment="Left">
						<font size="11" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<text><![CDATA[ Telefone]]></text>
				</staticText>
				<staticText>
					<reportElement key="staticText-5" x="410" y="2" width="123" height="17"/>
					<textElement textAlignment="Left">
						<font size="11" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<text><![CDATA[ E-Mail]]></text>
				</staticText>
				<staticText>
					<reportElement key="staticText-7" x="549" y="2" width="75" height="17"/>
					<textElement textAlignment="Left">
						<font size="11" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<text><![CDATA[Tipo Contrato Contrat o]]></text>
				</staticText>
				<staticText>
					<reportElement key="staticText-8" x="624" y="2" width="26" height="17"/>
					<textElement textAlignment="Left">
						<font size="11" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<text><![CDATA[ Dur.]]></text>
				</staticText>
				<staticText>
					<reportElement key="staticText-9" x="654" y="2" width="54" height="17"/>
					<textElement textAlignment="Left">
						<font size="11" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<text><![CDATA[ Modali.]]></text>
				</staticText>
				<staticText>
					<reportElement key="staticText-10" x="708" y="2" width="51" height="17"/>
					<textElement textAlignment="Left">
						<font size="11" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<text><![CDATA[ Plano]]></text>
				</staticText>
				<staticText>
					<reportElement key="staticText-11" x="762" y="2" width="61" height="17"/>
					<textElement textAlignment="Left">
						<font size="11" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<text><![CDATA[ Dt. Vencimento]]></text>
				</staticText>
				<staticText>
					<reportElement key="staticText-12" x="1" y="2" width="52" height="17"/>
					<textElement textAlignment="Left">
						<font size="11" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<text><![CDATA[ Mat]]></text>
				</staticText>
				<line>
					<reportElement key="line-4" x="0" y="20" width="821" height="1"/>
				</line>
				<staticText>
					<reportElement key="staticText-20" x="243" y="2" width="21" height="17"/>
					<textElement textAlignment="Left">
						<font size="11" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<text><![CDATA[Sit.]]></text>
				</staticText>
			</band>
		</groupHeader>
		<groupFooter>
			<band splitType="Stretch"/>
		</groupFooter>
	</group>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band splitType="Stretch"/>
	</title>
	<pageHeader>
		<band height="124" splitType="Stretch">
			<image isUsingCache="true" onErrorType="Blank" evaluationTime="Page">
				<reportElement key="image-1" x="1" y="6" width="82" height="52" isPrintWhenDetailOverflows="true"/>
				<box>
					<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<imageExpression class="java.io.InputStream"><![CDATA[$P{logoPadraoRelatorio}]]></imageExpression>
			</image>
			<staticText>
				<reportElement key="staticText-17" mode="Opaque" x="574" y="2" width="262" height="23"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Top" lineSpacing="Single">
					<font fontName="Microsoft Sans Serif" size="9" isBold="true" isItalic="true" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica-BoldOblique"/>
				</textElement>
				<text><![CDATA[Zillyon - Sistema Administrativo para Academias Desenvolvido por PACTO Soluções Tecnológicas Ltda.]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-18" mode="Opaque" x="725" y="28" width="111" height="12"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Top" lineSpacing="Single">
					<font fontName="Microsoft Sans Serif" size="9" isBold="true" isItalic="true" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica-BoldOblique"/>
				</textElement>
				<text><![CDATA[(0xx62) 3251-5820]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-19" x="300" y="22" width="165" height="27"/>
				<textElement textAlignment="Left">
					<font size="20" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Geral de Clientes]]></text>
			</staticText>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-212" x="95" y="4" width="200" height="14"/>
				<textElement>
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{nomeEmpresa}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-213" x="95" y="20" width="200" height="14"/>
				<textElement>
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{enderecoEmpresa}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-214" x="95" y="36" width="200" height="14"/>
				<textElement>
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{cidadeEmpresa}]]></textFieldExpression>
			</textField>
			<textField evaluationTime="Report" isBlankWhenNull="false">
				<reportElement key="textField-215" x="806" y="49" width="31" height="17"/>
				<box bottomPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement>
					<font fontName="Arial" size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[" " + $V{PAGE_NUMBER} + ""]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-216" x="724" y="49" width="75" height="17"/>
				<box bottomPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right">
					<font fontName="Arial" size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA["Página: " + $V{PAGE_NUMBER} + " de "]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-218" x="6" y="67" width="821" height="45"/>
				<box>
					<topPen lineWidth="0.5" lineStyle="Solid"/>
					<leftPen lineWidth="0.5" lineStyle="Solid"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.5" lineStyle="Solid"/>
				</box>
				<textElement textAlignment="Center">
					<font fontName="Arial" size="10" isBold="true" isItalic="true" pdfFontName="Helvetica-BoldOblique"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{filtros}]]></textFieldExpression>
			</textField>
		</band>
	</pageHeader>
	<columnHeader>
		<band splitType="Stretch"/>
	</columnHeader>
	<detail>
		<band height="20" splitType="Stretch">
			<rectangle>
				<reportElement key="rectangle-1" mode="Opaque" x="0" y="0" width="822" height="18" backcolor="#B4CDCD">
					<printWhenExpression><![CDATA[new Boolean(($V{COLUMN_COUNT}.intValue()%2)==0)]]></printWhenExpression>
				</reportElement>
				<graphicElement>
					<pen lineWidth="0.0" lineStyle="Solid"/>
				</graphicElement>
			</rectangle>
			<textField isBlankWhenNull="false">
				<reportElement key="textField" x="3" y="0" width="50" height="18"/>
				<textElement/>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{cliente.matricula}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField" x="66" y="0" width="175" height="18"/>
				<textElement/>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{cliente.pessoa.nome}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField" x="266" y="0" width="144" height="18"/>
				<textElement/>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{foneCliente}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField" x="412" y="0" width="139" height="18"/>
				<textElement/>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{emailCliente}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField" x="551" y="0" width="75" height="18"/>
				<textElement/>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{contrato.situacaoContrato_Apresentar}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField" x="629" y="0" width="23" height="18"/>
				<textElement/>
				<textFieldExpression class="java.lang.Integer"><![CDATA[$F{contrato.contratoDuracao.numeroMeses}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField" x="653" y="0" width="55" height="18"/>
				<textElement/>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{modalidadeCliente}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField" x="708" y="0" width="51" height="18"/>
				<textElement/>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{plano.descricao}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField" x="767" y="0" width="56" height="18"/>
				<textElement/>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{contrato.vigenciaAteAjustada_Apresentar}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-217" x="242" y="0" width="23" height="18"/>
				<textElement/>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{cliente.situacao}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<columnFooter>
		<band splitType="Stretch"/>
	</columnFooter>
	<pageFooter>
		<band splitType="Stretch"/>
	</pageFooter>
	<lastPageFooter>
		<band height="133" splitType="Stretch">
			<staticText>
				<reportElement key="staticText-2" x="713" y="4" width="63" height="17"/>
				<textElement textAlignment="Center">
					<font size="8" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Visitantes]]></text>
			</staticText>
			<textField pattern="" isBlankWhenNull="false">
				<reportElement key="textField-207" x="1" y="116" width="822" height="13"/>
				<box>
					<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="8" isItalic="true" pdfFontName="Helvetica-Oblique"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[" "+" Usuário:" + $P{usuario}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-208" x="778" y="21" width="41" height="18"/>
				<textElement>
					<font size="8" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression class="java.lang.Integer"><![CDATA[$V{ativo}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement key="staticText-14" x="717" y="21" width="43" height="17"/>
				<textElement textAlignment="Center">
					<font size="8" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Ativos]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-15" x="708" y="38" width="63" height="17"/>
				<textElement textAlignment="Center">
					<font size="8" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Inativos]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-16" x="713" y="55" width="56" height="17"/>
				<textElement textAlignment="Center">
					<font size="8" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Total de Clientes]]></text>
			</staticText>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-209" x="778" y="4" width="41" height="16"/>
				<textElement>
					<font size="8" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression class="java.lang.Integer"><![CDATA[$V{visitante}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-210" x="778" y="39" width="41" height="15"/>
				<textElement>
					<font size="8" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression class="java.lang.Integer"><![CDATA[$V{intativo}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-211" x="778" y="55" width="41" height="17"/>
				<textElement>
					<font size="8" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression class="java.lang.Integer"><![CDATA[$V{total}]]></textFieldExpression>
			</textField>
			<line>
				<reportElement key="line-3" x="1" y="1" width="821" height="1"/>
			</line>
		</band>
	</lastPageFooter>
	<summary>
		<band splitType="Stretch"/>
	</summary>
</jasperReport>

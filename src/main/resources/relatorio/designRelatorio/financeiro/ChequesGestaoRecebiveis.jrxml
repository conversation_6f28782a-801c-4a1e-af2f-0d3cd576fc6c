<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="ChequesRecebiveis" pageWidth="625" pageHeight="100" columnWidth="625" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" whenResourceMissingType="Empty">
	<property name="ireport.zoom" value="2.4157650000000253"/>
	<property name="ireport.x" value="372"/>
	<property name="ireport.y" value="0"/>
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.encoding" value="ISO-8859-1"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="java.util.*"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<parameter name="tituloRelatorio" class="java.lang.String" isForPrompting="false"/>
	<parameter name="nomeEmpresa" class="java.lang.String" isForPrompting="false"/>
	<parameter name="versaoSoftware" class="java.lang.String" isForPrompting="false"/>
	<parameter name="usuario" class="java.lang.String" isForPrompting="false"/>
	<parameter name="filtros" class="java.lang.String" isForPrompting="false"/>
	<parameter name="SUBREPORT_DIR" class="java.lang.String">
		<defaultValueExpression><![CDATA["E:\\desenvolvimentosv\\Projeto Pacto Solucoes\\ZillyonWeb\\src\\java\\relatorio\\designRelatorio\\financeiro\\"]]></defaultValueExpression>
	</parameter>
	<parameter name="SUBREPORT_DIR1" class="java.lang.String">
		<defaultValueExpression><![CDATA["E:\\desenvolvimentosv\\Projeto Pacto Solucoes\\ZillyonWeb\\src\\java\\relatorio\\designRelatorio\\financeiro\\"]]></defaultValueExpression>
	</parameter>
	<parameter name="logoPadraoRelatorio" class="java.io.InputStream" isForPrompting="false"/>
	<parameter name="SUBREPORT_DIR2" class="java.lang.String">
		<defaultValueExpression><![CDATA["E:\\desenvolvimentosv\\Projeto Pacto Solucoes\\ZillyonWeb\\src\\java\\relatorio\\designRelatorio\\financeiro\\"]]></defaultValueExpression>
	</parameter>
	<parameter name="dataFim" class="java.lang.String"/>
	<parameter name="dataIni" class="java.lang.String"/>
	<parameter name="considerarCompensacaoOriginal" class="java.lang.Boolean"/>
	<parameter name="moeda" class="java.lang.String">
		<defaultValueExpression><![CDATA["R$"]]></defaultValueExpression>
	</parameter>
	<field name="nomePagador" class="java.lang.String"/>
	<field name="numero" class="java.lang.String"/>
	<field name="agencia" class="java.lang.String"/>
	<field name="numeroBanco" class="java.lang.String"/>
	<field name="conta" class="java.lang.String"/>
	<field name="numeroLoteApresentar" class="java.lang.String"/>
	<field name="valorApresentar" class="java.lang.String"/>
	<field name="dataCompensacaoApresentar" class="java.lang.String"/>
	<field name="dataLancamentoApresentar" class="java.lang.String"/>
	<field name="contaContido" class="java.lang.String"/>
	<field name="matricula" class="java.lang.String"/>
	<field name="dataOriginalApresentar" class="java.lang.String"/>
	<columnHeader>
		<band height="13">
			<staticText>
				<reportElement x="35" y="0" width="114" height="13"/>
				<textElement>
					<font size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Nome Pagador]]></text>
			</staticText>
			<staticText>
				<reportElement x="207" y="0" width="45" height="13"/>
				<textElement>
					<font size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Agência]]></text>
			</staticText>
			<staticText>
				<reportElement x="252" y="0" width="60" height="13"/>
				<textElement>
					<font size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Conta]]></text>
			</staticText>
			<staticText>
				<reportElement x="314" y="0" width="43" height="13"/>
				<textElement>
					<font size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Número]]></text>
			</staticText>
			<staticText>
				<reportElement x="362" y="0" width="40" height="13"/>
				<textElement>
					<font size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Lanç.]]></text>
			</staticText>
			<staticText>
				<reportElement x="412" y="0" width="40" height="13"/>
				<textElement>
					<font size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Comp.]]></text>
			</staticText>
			<staticText>
				<reportElement x="482" y="0" width="39" height="13"/>
				<textElement textAlignment="Right">
					<font size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Valor]]></text>
			</staticText>
			<staticText>
				<reportElement x="525" y="0" width="71" height="13"/>
				<textElement>
					<font size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Conta Financeiro]]></text>
			</staticText>
			<staticText>
				<reportElement x="600" y="0" width="25" height="13"/>
				<textElement>
					<font size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Lote]]></text>
			</staticText>
			<staticText>
				<reportElement x="175" y="0" width="27" height="13"/>
				<textElement>
					<font size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Banco]]></text>
			</staticText>
			<line>
				<reportElement x="0" y="12" width="625" height="1"/>
			</line>
			<staticText>
				<reportElement x="2" y="1" width="30" height="11"/>
				<textElement>
					<font size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Mat.]]></text>
			</staticText>
			<textField>
				<reportElement mode="Transparent" x="463" y="0" width="19" height="13" forecolor="#000000" backcolor="#FFFFFF"/>
				<textElement textAlignment="Right" verticalAlignment="Top" rotation="None" lineSpacing="Single" markup="none">
					<font fontName="SansSerif" size="8" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica-Bold" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{moeda}]]></textFieldExpression>
			</textField>
		</band>
	</columnHeader>
	<detail>
		<band height="13">
			<textField isStretchWithOverflow="true">
				<reportElement positionType="Float" stretchType="RelativeToBandHeight" x="175" y="0" width="29" height="10"/>
				<textElement>
					<font size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{numeroBanco}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement positionType="Float" stretchType="RelativeToBandHeight" x="207" y="0" width="45" height="10"/>
				<textElement>
					<font size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{agencia}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement positionType="Float" stretchType="RelativeToBandHeight" x="252" y="0" width="60" height="10"/>
				<textElement>
					<font size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{conta}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement positionType="Float" stretchType="RelativeToBandHeight" x="314" y="0" width="43" height="10"/>
				<textElement>
					<font size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{numero}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement positionType="Float" stretchType="RelativeToBandHeight" x="362" y="0" width="49" height="10"/>
				<textElement>
					<font size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{dataLancamentoApresentar}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement positionType="Float" stretchType="RelativeToBandHeight" x="412" y="0" width="55" height="10"/>
				<textElement>
					<font size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{considerarCompensacaoOriginal} ? $F{dataOriginalApresentar} : $F{dataCompensacaoApresentar}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement positionType="Float" stretchType="RelativeToBandHeight" x="468" y="0" width="53" height="10"/>
				<textElement textAlignment="Right">
					<font size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{valorApresentar}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement positionType="Float" stretchType="RelativeToBandHeight" x="525" y="0" width="71" height="10"/>
				<textElement>
					<font size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{contaContido}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" stretchType="RelativeToBandHeight" x="596" y="0" width="29" height="10"/>
				<textElement textAlignment="Center">
					<font size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{numeroLoteApresentar}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement positionType="Float" stretchType="RelativeToBandHeight" x="34" y="0" width="142" height="10"/>
				<textElement>
					<font size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{nomePagador} != null ? $F{nomePagador}.toUpperCase() : ""]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="1" y="0" width="32" height="10"/>
				<textElement>
					<font size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{matricula}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
</jasperReport>

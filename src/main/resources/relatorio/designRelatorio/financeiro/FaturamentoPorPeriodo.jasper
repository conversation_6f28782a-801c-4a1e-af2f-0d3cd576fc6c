¬í sr (net.sf.jasperreports.engine.JasperReport      'Ø L compileDatat Ljava/io/Serializable;L compileNameSuffixt Ljava/lang/String;L 
compilerClassq ~ xr -net.sf.jasperreports.engine.base.JRBaseReport      'Ø ,I PSEUDO_SERIAL_VERSION_UIDI bottomMarginI columnCountI 
columnSpacingI columnWidthZ ignorePaginationZ isFloatColumnFooterZ isSummaryNewPageZ  isSummaryWithPageHeaderAndFooterZ isTitleNewPageI 
leftMarginB orientationI 
pageHeightI 	pageWidthB 
printOrderI rightMarginI 	topMarginB whenNoDataTypeL 
backgroundt $Lnet/sf/jasperreports/engine/JRBand;L columnDirectiont 3Lnet/sf/jasperreports/engine/type/RunDirectionEnum;L columnFooterq ~ L columnHeaderq ~ [ datasetst ([Lnet/sf/jasperreports/engine/JRDataset;L defaultFontt *Lnet/sf/jasperreports/engine/JRReportFont;L defaultStylet %Lnet/sf/jasperreports/engine/JRStyle;L detailq ~ L 
detailSectiont 'Lnet/sf/jasperreports/engine/JRSection;[ fontst +[Lnet/sf/jasperreports/engine/JRReportFont;L formatFactoryClassq ~ L 
importsSett Ljava/util/Set;L languageq ~ L lastPageFooterq ~ L mainDatasett 'Lnet/sf/jasperreports/engine/JRDataset;L nameq ~ L noDataq ~ L orientationValuet 2Lnet/sf/jasperreports/engine/type/OrientationEnum;L 
pageFooterq ~ L 
pageHeaderq ~ L printOrderValuet 1Lnet/sf/jasperreports/engine/type/PrintOrderEnum;[ stylest &[Lnet/sf/jasperreports/engine/JRStyle;L summaryq ~ [ 	templatest /[Lnet/sf/jasperreports/engine/JRReportTemplate;L titleq ~ L whenNoDataTypeValuet 5Lnet/sf/jasperreports/engine/type/WhenNoDataTypeEnum;xp  wñ            +           J  8        sr +net.sf.jasperreports.engine.base.JRBaseBand      'Ø I PSEUDO_SERIAL_VERSION_UIDI heightZ isSplitAllowedL printWhenExpressiont *Lnet/sf/jasperreports/engine/JRExpression;L 	splitTypet Ljava/lang/Byte;L splitTypeValuet 0Lnet/sf/jasperreports/engine/type/SplitTypeEnum;xr 3net.sf.jasperreports.engine.base.JRBaseElementGroup      'Ø L childrent Ljava/util/List;L elementGroupt ,Lnet/sf/jasperreports/engine/JRElementGroup;xpsr java.util.ArrayListxÒÇa I sizexp    w    xp  wñ    pp~r .net.sf.jasperreports.engine.type.SplitTypeEnum          xr java.lang.Enum          xpt STRETCH~r 1net.sf.jasperreports.engine.type.RunDirectionEnum          xq ~ t LTRppur ([Lnet.sf.jasperreports.engine.JRDataset;L6Í¬D  xp   sr .net.sf.jasperreports.engine.base.JRBaseDataset      'Ø I PSEUDO_SERIAL_VERSION_UIDZ isMainB whenResourceMissingType[ fieldst &[Lnet/sf/jasperreports/engine/JRField;L filterExpressionq ~ [ groupst &[Lnet/sf/jasperreports/engine/JRGroup;L nameq ~ [ 
parameterst *[Lnet/sf/jasperreports/engine/JRParameter;L 
propertiesMapt -Lnet/sf/jasperreports/engine/JRPropertiesMap;L queryt %Lnet/sf/jasperreports/engine/JRQuery;L resourceBundleq ~ L scriptletClassq ~ [ 
scriptletst *[Lnet/sf/jasperreports/engine/JRScriptlet;[ 
sortFieldst *[Lnet/sf/jasperreports/engine/JRSortField;[ 	variablest )[Lnet/sf/jasperreports/engine/JRVariable;L whenResourceMissingTypeValuet >Lnet/sf/jasperreports/engine/type/WhenResourceMissingTypeEnum;xp  wñ  pppt dataset1ur *[Lnet.sf.jasperreports.engine.JRParameter;" *Ã`!  xp   sr 0net.sf.jasperreports.engine.base.JRBaseParameter      'Ø 	Z isForPromptingZ isSystemDefinedL defaultValueExpressionq ~ L descriptionq ~ L nameq ~ L nestedTypeNameq ~ L 
propertiesMapq ~ *L valueClassNameq ~ L valueClassRealNameq ~ xpppt REPORT_PARAMETERS_MAPpsr +net.sf.jasperreports.engine.JRPropertiesMap      'Ø L baseq ~ *L propertiesListq ~ L 
propertiesMapt Ljava/util/Map;xppppt 
java.util.Mappsq ~ 4ppt 
JASPER_REPORTpsq ~ 7pppt (net.sf.jasperreports.engine.JasperReportpsq ~ 4ppt REPORT_CONNECTIONpsq ~ 7pppt java.sql.Connectionpsq ~ 4ppt REPORT_MAX_COUNTpsq ~ 7pppt java.lang.Integerpsq ~ 4ppt REPORT_DATA_SOURCEpsq ~ 7pppt (net.sf.jasperreports.engine.JRDataSourcepsq ~ 4ppt REPORT_SCRIPTLETpsq ~ 7pppt /net.sf.jasperreports.engine.JRAbstractScriptletpsq ~ 4ppt 
REPORT_LOCALEpsq ~ 7pppt java.util.Localepsq ~ 4ppt REPORT_RESOURCE_BUNDLEpsq ~ 7pppt java.util.ResourceBundlepsq ~ 4ppt REPORT_TIME_ZONEpsq ~ 7pppt java.util.TimeZonepsq ~ 4ppt REPORT_FORMAT_FACTORYpsq ~ 7pppt .net.sf.jasperreports.engine.util.FormatFactorypsq ~ 4ppt REPORT_CLASS_LOADERpsq ~ 7pppt java.lang.ClassLoaderpsq ~ 4ppt REPORT_URL_HANDLER_FACTORYpsq ~ 7pppt  java.net.URLStreamHandlerFactorypsq ~ 4ppt REPORT_FILE_RESOLVERpsq ~ 7pppt -net.sf.jasperreports.engine.util.FileResolverpsq ~ 4ppt REPORT_TEMPLATESpsq ~ 7pppt java.util.Collectionpsq ~ 4ppt SORT_FIELDSpsq ~ 7pppt java.util.Listpsq ~ 7ppppppppur )[Lnet.sf.jasperreports.engine.JRVariable;bæ|,·D  xp   sr /net.sf.jasperreports.engine.base.JRBaseVariable      'Ø I PSEUDO_SERIAL_VERSION_UIDB calculationB 
incrementTypeZ isSystemDefinedB 	resetTypeL calculationValuet 2Lnet/sf/jasperreports/engine/type/CalculationEnum;L 
expressionq ~ L incrementGroupt %Lnet/sf/jasperreports/engine/JRGroup;L incrementTypeValuet 4Lnet/sf/jasperreports/engine/type/IncrementTypeEnum;L incrementerFactoryClassNameq ~ L incrementerFactoryClassRealNameq ~ L initialValueExpressionq ~ L nameq ~ L 
resetGroupq ~ xL resetTypeValuet 0Lnet/sf/jasperreports/engine/type/ResetTypeEnum;L valueClassNameq ~ L valueClassRealNameq ~ xp  wî   ~r 0net.sf.jasperreports.engine.type.CalculationEnum          xq ~ t SYSTEMpp~r 2net.sf.jasperreports.engine.type.IncrementTypeEnum          xq ~ t NONEppsr 1net.sf.jasperreports.engine.base.JRBaseExpression      'Ø I id[ chunkst 0[Lnet/sf/jasperreports/engine/JRExpressionChunk;L valueClassNameq ~ L valueClassRealNameq ~ xp    ur 0[Lnet.sf.jasperreports.engine.JRExpressionChunk;mYÏÞiK£U  xp   sr 6net.sf.jasperreports.engine.base.JRBaseExpressionChunk      'Ø B typeL textq ~ xpt new java.lang.Integer(1)q ~ Fpt PAGE_NUMBERp~r .net.sf.jasperreports.engine.type.ResetTypeEnum          xq ~ t REPORTq ~ Fpsq ~ v  wî   q ~ }ppq ~ ppsq ~    uq ~    sq ~ t new java.lang.Integer(1)q ~ Fpt 
COLUMN_NUMBERp~q ~ t PAGEq ~ Fpsq ~ v  wî   ~q ~ |t COUNTsq ~    uq ~    sq ~ t new java.lang.Integer(1)q ~ Fppq ~ ppsq ~    uq ~    sq ~ t new java.lang.Integer(0)q ~ Fpt REPORT_COUNTpq ~ q ~ Fpsq ~ v  wî   q ~ sq ~    uq ~    sq ~ t new java.lang.Integer(1)q ~ Fppq ~ ppsq ~    uq ~    sq ~ t new java.lang.Integer(0)q ~ Fpt 
PAGE_COUNTpq ~ q ~ Fpsq ~ v  wî   q ~ sq ~    uq ~    sq ~ t new java.lang.Integer(1)q ~ Fppq ~ ppsq ~    uq ~    sq ~ t new java.lang.Integer(0)q ~ Fpt COLUMN_COUNTp~q ~ t COLUMNq ~ Fp~r <net.sf.jasperreports.engine.type.WhenResourceMissingTypeEnum          xq ~ t NULLsq ~ &  wñ  pppt Table Dataset 1uq ~ 2   sq ~ 4ppq ~ 6psq ~ 7pppq ~ :psq ~ 4ppq ~ <psq ~ 7pppq ~ >psq ~ 4ppq ~ @psq ~ 7pppq ~ Bpsq ~ 4ppq ~ Dpsq ~ 7pppq ~ Fpsq ~ 4ppq ~ Hpsq ~ 7pppq ~ Jpsq ~ 4ppq ~ Lpsq ~ 7pppq ~ Npsq ~ 4ppq ~ Ppsq ~ 7pppq ~ Rpsq ~ 4ppq ~ Tpsq ~ 7pppq ~ Vpsq ~ 4ppq ~ Xpsq ~ 7pppq ~ Zpsq ~ 4ppq ~ \psq ~ 7pppq ~ ^psq ~ 4ppq ~ `psq ~ 7pppq ~ bpsq ~ 4ppq ~ dpsq ~ 7pppq ~ fpsq ~ 4ppq ~ hpsq ~ 7pppq ~ jpsq ~ 4ppq ~ lpsq ~ 7pppq ~ npsq ~ 4ppq ~ ppsq ~ 7pppq ~ rpsq ~ 7ppppppppuq ~ t   sq ~ v  wî   q ~ }ppq ~ ppsq ~     uq ~    sq ~ t new java.lang.Integer(1)q ~ Fpq ~ pq ~ q ~ Fpsq ~ v  wî   q ~ }ppq ~ ppsq ~    uq ~    sq ~ t new java.lang.Integer(1)q ~ Fpq ~ pq ~ q ~ Fpsq ~ v  wî   q ~ sq ~    uq ~    sq ~ t new java.lang.Integer(1)q ~ Fppq ~ ppsq ~    uq ~    sq ~ t new java.lang.Integer(0)q ~ Fpq ~ ¡pq ~ q ~ Fpsq ~ v  wî   q ~ sq ~    uq ~    sq ~ t new java.lang.Integer(1)q ~ Fppq ~ ppsq ~    uq ~    sq ~ t new java.lang.Integer(0)q ~ Fpq ~ «pq ~ q ~ Fpsq ~ v  wî   q ~ sq ~    uq ~    sq ~ t new java.lang.Integer(1)q ~ Fppq ~ ppsq ~    uq ~    sq ~ t new java.lang.Integer(0)q ~ Fpq ~ µpq ~ ¶q ~ Fpq ~ ¹pppsr .net.sf.jasperreports.engine.base.JRBaseSection      'Ø [ bandst %[Lnet/sf/jasperreports/engine/JRBand;xpur %[Lnet.sf.jasperreports.engine.JRBand;Ý~ìÊ5  xp   sq ~ sq ~    w   sr 0net.sf.jasperreports.engine.base.JRBaseTextField      'Ø I PSEUDO_SERIAL_VERSION_UIDI 
bookmarkLevelB evaluationTimeB hyperlinkTargetB 
hyperlinkTypeZ isStretchWithOverflowL anchorNameExpressionq ~ L evaluationGroupq ~ xL evaluationTimeValuet 5Lnet/sf/jasperreports/engine/type/EvaluationTimeEnum;L 
expressionq ~ L hyperlinkAnchorExpressionq ~ L hyperlinkPageExpressionq ~ [ hyperlinkParameterst 3[Lnet/sf/jasperreports/engine/JRHyperlinkParameter;L hyperlinkReferenceExpressionq ~ L hyperlinkTooltipExpressionq ~ L isBlankWhenNullt Ljava/lang/Boolean;L 
linkTargetq ~ L linkTypeq ~ L patternq ~ xr 2net.sf.jasperreports.engine.base.JRBaseTextElement      'Ø %I PSEUDO_SERIAL_VERSION_UIDL borderq ~ L borderColort Ljava/awt/Color;L bottomBorderq ~ L bottomBorderColorq ~L 
bottomPaddingt Ljava/lang/Integer;L fontNameq ~ L fontSizeq ~L horizontalAlignmentq ~ L horizontalAlignmentValuet 6Lnet/sf/jasperreports/engine/type/HorizontalAlignEnum;L isBoldq ~
L isItalicq ~
L 
isPdfEmbeddedq ~
L isStrikeThroughq ~
L isStyledTextq ~
L isUnderlineq ~
L 
leftBorderq ~ L leftBorderColorq ~L leftPaddingq ~L lineBoxt 'Lnet/sf/jasperreports/engine/JRLineBox;L lineSpacingq ~ L lineSpacingValuet 2Lnet/sf/jasperreports/engine/type/LineSpacingEnum;L markupq ~ L paddingq ~L pdfEncodingq ~ L pdfFontNameq ~ L 
reportFontq ~ L rightBorderq ~ L rightBorderColorq ~L rightPaddingq ~L rotationq ~ L 
rotationValuet /Lnet/sf/jasperreports/engine/type/RotationEnum;L 	topBorderq ~ L topBorderColorq ~L 
topPaddingq ~L verticalAlignmentq ~ L verticalAlignmentValuet 4Lnet/sf/jasperreports/engine/type/VerticalAlignEnum;xr .net.sf.jasperreports.engine.base.JRBaseElement      'Ø I PSEUDO_SERIAL_VERSION_UIDI heightZ isPrintInFirstWholeBandZ isPrintRepeatedValuesZ isPrintWhenDetailOverflowsZ isRemoveLineWhenBlankB positionTypeB stretchTypeI widthI xI yL 	backcolorq ~L defaultStyleProvidert 4Lnet/sf/jasperreports/engine/JRDefaultStyleProvider;L elementGroupq ~ L 	forecolorq ~L keyq ~ L modeq ~ L 	modeValuet +Lnet/sf/jasperreports/engine/type/ModeEnum;L parentStyleq ~ L parentStyleNameReferenceq ~ L positionTypeValuet 3Lnet/sf/jasperreports/engine/type/PositionTypeEnum;L printWhenExpressionq ~ L printWhenGroupChangesq ~ xL 
propertiesMapq ~ *[ propertyExpressionst 3[Lnet/sf/jasperreports/engine/JRPropertyExpression;L stretchTypeValuet 2Lnet/sf/jasperreports/engine/type/StretchTypeEnum;xp  wñ   
        P      pq ~ q ~ppp~r )net.sf.jasperreports.engine.type.ModeEnum          xq ~ t TRANSPARENTpp~r 1net.sf.jasperreports.engine.type.PositionTypeEnum          xq ~ t FIX_RELATIVE_TO_TOPpppp~r 0net.sf.jasperreports.engine.type.StretchTypeEnum          xq ~ t 
NO_STRETCH  wñppppppsr java.lang.Integerâ ¤÷8 I valuexr java.lang.Number¬à  xp   pppppppppppsr .net.sf.jasperreports.engine.base.JRBaseLineBox      'Ø L 
bottomPaddingq ~L 	bottomPent +Lnet/sf/jasperreports/engine/base/JRBoxPen;L boxContainert ,Lnet/sf/jasperreports/engine/JRBoxContainer;L leftPaddingq ~L leftPenq ~*L paddingq ~L penq ~*L rightPaddingq ~L rightPenq ~*L 
topPaddingq ~L topPenq ~*xppsr 3net.sf.jasperreports.engine.base.JRBaseBoxBottomPen      'Ø  xr -net.sf.jasperreports.engine.base.JRBaseBoxPen      'Ø L lineBoxq ~xr *net.sf.jasperreports.engine.base.JRBasePen      'Ø I PSEUDO_SERIAL_VERSION_UIDL 	lineColorq ~L 	lineStyleq ~ L lineStyleValuet 0Lnet/sf/jasperreports/engine/type/LineStyleEnum;L 	lineWidtht Ljava/lang/Float;L penContainert ,Lnet/sf/jasperreports/engine/JRPenContainer;xp  wñppppq ~,q ~,q ~psr 1net.sf.jasperreports.engine.base.JRBaseBoxLeftPen      'Ø  xq ~.  wñppppq ~,q ~,psq ~.  wñppppq ~,q ~,psr 2net.sf.jasperreports.engine.base.JRBaseBoxRightPen      'Ø  xq ~.  wñppppq ~,q ~,psr 0net.sf.jasperreports.engine.base.JRBaseBoxTopPen      'Ø  xq ~.  wñppppq ~,q ~,pppppppppppppppp~r 2net.sf.jasperreports.engine.type.VerticalAlignEnum          xq ~ t MIDDLE  wñ       pp~r 3net.sf.jasperreports.engine.type.EvaluationTimeEnum          xq ~ t NOWsq ~    uq ~    sq ~ t descricaoProdutot java.lang.Stringppppppppppsq ~
  wñ   
        7  2   pq ~ q ~ppppppq ~!ppppq ~$  wñppppppsq ~&   p~r 4net.sf.jasperreports.engine.type.HorizontalAlignEnum          xq ~ t LEFTpppppppppsq ~)psq ~-  wñppppq ~Kq ~Kq ~Fpsq ~4  wñppppq ~Kq ~Kpsq ~.  wñppppq ~Kq ~Kpsq ~7  wñppppq ~Kq ~Kpsq ~9  wñppppq ~Kq ~Kppppppppppppppppq ~<  wñ        ppq ~?sq ~    uq ~    sq ~ t dataCadastroClientet java.util.Dateppppppsr java.lang.BooleanÍ rÕúî Z valuexpppt 
dd/MM/yyyysq ~
  wñ   
        (   Q   pq ~ q ~ppppppq ~!ppppq ~$  wñppppppq ~(pq ~Ipppppppppsq ~)psq ~-  wñppppq ~Zq ~Zq ~Ypsq ~4  wñppppq ~Zq ~Zpsq ~.  wñppppq ~Zq ~Zpsq ~7  wñppppq ~Zq ~Zpsq ~9  wñppppq ~Zq ~Zppppppppppppppppq ~<  wñ       ppq ~?sq ~    uq ~    sq ~ t matriculaClientet java.lang.Stringpppppppppt  sq ~
  wñ   
        2  i   pq ~ q ~ppppppq ~!ppppq ~$  wñppppppq ~Gpq ~Ipppppppppsq ~)psq ~-  wñppppq ~gq ~gq ~fpsq ~4  wñppppq ~gq ~gpsq ~.  wñppppq ~gq ~gpsq ~7  wñppppq ~gq ~gpsq ~9  wñppppq ~gq ~gppppppppppppppppq ~<  wñ       ppq ~?sq ~    uq ~    sq ~ t codContratot java.lang.Integerppppppq ~Wppt ###0sq ~
  wñ   
        _   y   pq ~ q ~ppppppq ~!ppppq ~$  wñppppppq ~(pppppppppppsq ~)psq ~-  wñppppq ~tq ~tq ~spsq ~4  wñppppq ~tq ~tpsq ~.  wñppppq ~tq ~tpsq ~7  wñppppq ~tq ~tpsq ~9  wñppppq ~tq ~tppppppppppppppppq ~<  wñ       ppq ~?sq ~    uq ~    sq ~ t nomeClientet java.lang.Stringppppppppppsq ~
  wñ   
        Z   Ø   pq ~ q ~ppppppq ~!ppppq ~$  wñppppppq ~(pppppppppppsq ~)psq ~-  wñppppq ~q ~q ~psq ~4  wñppppq ~q ~psq ~.  wñppppq ~q ~psq ~7  wñppppq ~q ~psq ~9  wñppppq ~q ~ppppppppppppppppq ~<  wñ       ppq ~?sq ~    uq ~    sq ~ t nomeResponsavelLancamentot java.lang.Stringppppppppppsq ~
  wñ   
        -     pq ~ q ~ppppppq ~!ppppq ~$  wñppppppq ~Gpq ~Ipppppppppsq ~)psq ~-  wñppppq ~q ~q ~psq ~4  wñppppq ~q ~psq ~.  wñppppq ~q ~psq ~7  wñppppq ~q ~psq ~9  wñppppq ~q ~ppppppppppppppppq ~<  wñ        ppq ~?sq ~    uq ~    sq ~ t 
dataIniciot java.lang.Stringppppppq ~Wppt #,##0.00sq ~
  wñ   
          ô   pq ~ q ~ppppppq ~!ppppq ~$  wñppppppq ~Gpq ~Ipppppppppsq ~)psq ~-  wñppppq ~q ~q ~psq ~4  wñppppq ~q ~psq ~.  wñppppq ~q ~psq ~7  wñppppq ~q ~psq ~9  wñppppq ~q ~ppppppppppppppppq ~<  wñ       ppq ~?sq ~    uq ~    sq ~ t duracaoPlanot java.lang.Integerppppppq ~Wppt 
###0;-###0sq ~
  wñ   
        6     pq ~ q ~ppppppq ~!ppppq ~$  wñppppppq ~Gpq ~Ipppppppppsq ~)psq ~-  wñppppq ~¦q ~¦q ~¥psq ~4  wñppppq ~¦q ~¦psq ~.  wñppppq ~¦q ~¦psq ~7  wñppppq ~¦q ~¦psq ~9  wñppppq ~¦q ~¦ppppppppppppppppq ~<  wñ       ppq ~?sq ~    uq ~    sq ~ t modalidadest java.lang.Stringppppppq ~Wppt #,##0.00sq ~
  wñ   
        <     pq ~ q ~ppppppq ~!ppppq ~$  wñppppppq ~Gpq ~Ipppppppppsq ~)psq ~-  wñppppq ~³q ~³q ~²psq ~4  wñppppq ~³q ~³psq ~.  wñppppq ~³q ~³psq ~7  wñppppq ~³q ~³psq ~9  wñppppq ~³q ~³ppppppppppppppppq ~<  wñ       ppq ~?sq ~    uq ~    sq ~ t 	nomePlanot java.lang.Stringppppppq ~Wppt #,##0.00sq ~
  wñ   
        I  È   pq ~ q ~ppppppq ~!ppppq ~$  wñppppppq ~Gpq ~Ipppppppppsq ~)psq ~-  wñppppq ~Àq ~Àq ~¿psq ~4  wñppppq ~Àq ~Àpsq ~.  wñppppq ~Àq ~Àpsq ~7  wñppppq ~Àq ~Àpsq ~9  wñppppq ~Àq ~Àppppppppppppppppq ~<  wñ       ppq ~?sq ~    uq ~    sq ~ t situacaoContratot java.lang.Stringppppppq ~Wppt #,##0.00sq ~
  wñ   
        4     pq ~ q ~ppppppq ~!ppppq ~$  wñppppppq ~Gpq ~Ipppppppppsq ~)psq ~-  wñppppq ~Íq ~Íq ~Ìpsq ~4  wñppppq ~Íq ~Ípsq ~.  wñppppq ~Íq ~Ípsq ~7  wñppppq ~Íq ~Ípsq ~9  wñppppq ~Íq ~Íppppppppppppppppq ~<  wñ        ppq ~?sq ~    uq ~    sq ~ t dataLancamentoProdutot java.util.Dateppppppq ~Wppt 
dd/MM/yyyysq ~
  wñ   
        7  ½   pq ~ q ~ppppppq ~!ppppq ~$  wñppppppq ~Gpq ~Ipppppppppsq ~)psq ~-  wñppppq ~Úq ~Úq ~Ùpsq ~4  wñppppq ~Úq ~Úpsq ~.  wñppppq ~Úq ~Úpsq ~7  wñppppq ~Úq ~Úpsq ~9  wñppppq ~Úq ~Úppppppppppppppppq ~<  wñ        ppq ~?sq ~    uq ~    sq ~ t dataTerminot java.lang.Stringppppppq ~Wppt #,##0.00sr +net.sf.jasperreports.engine.base.JRBaseLine      'Ø I PSEUDO_SERIAL_VERSION_UIDB 	directionL directionValuet 4Lnet/sf/jasperreports/engine/type/LineDirectionEnum;xr 5net.sf.jasperreports.engine.base.JRBaseGraphicElement      'Ø I PSEUDO_SERIAL_VERSION_UIDL fillq ~ L 	fillValuet +Lnet/sf/jasperreports/engine/type/FillEnum;L linePent #Lnet/sf/jasperreports/engine/JRPen;L penq ~ xq ~  wñ                 pq ~ q ~ppppppq ~!ppppq ~$  wîppsq ~/  wñppppq ~ëp  wñ ~r 2net.sf.jasperreports.engine.type.LineDirectionEnum          xq ~ t TOP_DOWNsq ~
  wñ   
        W     pq ~ q ~ppppppq ~!ppppq ~$  wñppppppq ~Gpppppppppppsq ~)psq ~-  wñppppq ~ñq ~ñq ~ðpsq ~4  wñppppq ~ñq ~ñpsq ~.  wñppppq ~ñq ~ñpsq ~7  wñppppq ~ñq ~ñpsq ~9  wñppppq ~ñq ~ñppppppppppppppppq ~<  wñ       ppq ~?sq ~    uq ~    sq ~ t condicaoPagamentot java.lang.Stringppppppq ~Wpppsq ~
  wñ   
        >  E   pq ~ q ~ppppppq ~!ppppq ~$  wñppppppq ~Gpppppppppppsq ~)psq ~-  wñppppq ~ýq ~ýq ~üpsq ~4  wñppppq ~ýq ~ýpsq ~.  wñppppq ~ýq ~ýpsq ~7  wñppppq ~ýq ~ýpsq ~9  wñppppq ~ýq ~ýppppppppppppppppq ~<  wñ       ppq ~?sq ~     uq ~    sq ~ t formaPagApresentart java.lang.Stringppppppq ~Wpppsq ~
  wñ   
        4  Ú   pq ~ q ~ppppppq ~!ppppq ~$  wñppppppq ~Gp~q ~Ht RIGHTpppppppppsq ~)psq ~-  wñppppq ~q ~q ~psq ~4  wñppppq ~q ~psq ~.  wñppppq ~q ~psq ~7  wñppppq ~q ~psq ~9  wñppppq ~q ~ppppppppppppppppq ~<  wñ       ppq ~?sq ~    !uq ~    sq ~ t valorCompetenciat java.lang.Doubleppppppq ~Wppt R$ #,##0.00sq ~
  wñ   
           H   pq ~ q ~ppppppq ~!ppppq ~$  wñppppppq ~Gpq ~Ipppppppppsq ~)psq ~-  wñppppq ~q ~q ~psq ~4  wñppppq ~q ~psq ~.  wñppppq ~q ~psq ~7  wñppppq ~q ~psq ~9  wñppppq ~q ~ppppppppppppppppq ~<  wñ       ppq ~?sq ~    "uq ~    sq ~ t turmaq ~°ppppppq ~Wppq ~±sq ~
  wñ   
        $  h   pq ~ q ~ppppppq ~!ppppq ~$  wñppppppq ~Gpq ~Ipppppppppsq ~)psq ~-  wñppppq ~#q ~#q ~"psq ~4  wñppppq ~#q ~#psq ~.  wñppppq ~#q ~#psq ~7  wñppppq ~#q ~#psq ~9  wñppppq ~#q ~#ppppppppppppppppq ~<  wñ       ppq ~?sq ~    #uq ~    sq ~ t 	categoriaq ~°ppppppq ~Wppq ~±xp  wñ   ppq ~ pppt javapsq ~ &  wñ ur &[Lnet.sf.jasperreports.engine.JRField;<ßÇN*òp  xp   sr ,net.sf.jasperreports.engine.base.JRBaseField      'Ø L descriptionq ~ L nameq ~ L 
propertiesMapq ~ *L valueClassNameq ~ L valueClassRealNameq ~ xppt descricaoProdutosq ~ 7pppt java.lang.Stringpsq ~1pt matriculaClientesq ~ 7pppt java.lang.Stringpsq ~1pt nomeClientesq ~ 7pppt java.lang.Stringpsq ~1pt nomeResponsavelLancamentosq ~ 7pppt java.lang.Stringpsq ~1pt dataCadastroClientesq ~ 7pppt java.util.Datepsq ~1pt codContratosq ~ 7pppt java.lang.Integerpsq ~1pt 
dataIniciosq ~ 7pppt java.lang.Stringpsq ~1pt dataTerminosq ~ 7pppt java.lang.Stringpsq ~1pt duracaoPlanosq ~ 7pppt java.lang.Integerpsq ~1pt modalidadessq ~ 7pppt java.lang.Stringpsq ~1pt 	nomePlanosq ~ 7pppt java.lang.Stringpsq ~1pt situacaoContratosq ~ 7pppt java.lang.Stringpsq ~1pt condicaoPagamentosq ~ 7pppt java.lang.Stringpsq ~1pt dataLancamentoProdutosq ~ 7pppt java.util.Datepsq ~1pt formaPagApresentarsq ~ 7pppt java.lang.Stringpsq ~1pt valorCompetenciasq ~ 7pppt java.lang.Doublepsq ~1pt turmasq ~ 7pppt java.lang.Stringpsq ~1pt 	categoriasq ~ 7pppq ~upppt RelatorioEstoqueProdutouq ~ 2   )sq ~ 4ppq ~ 6psq ~ 7pppq ~ :psq ~ 4ppq ~ <psq ~ 7pppq ~ >psq ~ 4ppq ~ @psq ~ 7pppq ~ Bpsq ~ 4ppq ~ Dpsq ~ 7pppq ~ Fpsq ~ 4ppq ~ Hpsq ~ 7pppq ~ Jpsq ~ 4ppq ~ Lpsq ~ 7pppq ~ Npsq ~ 4ppq ~ Ppsq ~ 7pppq ~ Rpsq ~ 4ppq ~ Tpsq ~ 7pppq ~ Vpsq ~ 4ppq ~ Xpsq ~ 7pppq ~ Zpsq ~ 4ppq ~ \psq ~ 7pppq ~ ^psq ~ 4ppq ~ `psq ~ 7pppq ~ bpsq ~ 4ppq ~ dpsq ~ 7pppq ~ fpsq ~ 4ppq ~ hpsq ~ 7pppq ~ jpsq ~ 4ppq ~ lpsq ~ 7pppq ~ npsq ~ 4ppq ~ ppsq ~ 7pppq ~ rpsq ~ 4ppt REPORT_VIRTUALIZERpsq ~ 7pppt )net.sf.jasperreports.engine.JRVirtualizerpsq ~ 4ppt IS_IGNORE_PAGINATIONpsq ~ 7pppt java.lang.Booleanpsq ~ 4ppt XML_DATA_DOCUMENTpsq ~ 7pppt org.w3c.dom.Documentpsq ~ 4ppt XML_DATE_PATTERNpsq ~ 7pppq ~upsq ~ 4ppt XML_NUMBER_PATTERNpsq ~ 7pppq ~upsq ~ 4ppt 
XML_LOCALEpsq ~ 7pppq ~ Rpsq ~ 4ppt 
XML_TIME_ZONEpsq ~ 7pppq ~ Zpsq ~ 4  ppt logoPadraoRelatoriopsq ~ 7pppt java.io.InputStreampsq ~ 4 ppt tipoVisualizacaopsq ~ 7pppt java.lang.Integerpsq ~ 4 ppt 
statusProdutopsq ~ 7pppt java.lang.Stringpsq ~ 4  ppt 	ordenacaopsq ~ 7pppt java.lang.Stringpsq ~ 4  ppt 
valorImpressopsq ~ 7pppt java.lang.Stringpsq ~ 4 ppt 
totalizadorespsq ~ 7pppt java.lang.Objectpsq ~ 4  sq ~     uq ~    sq ~ t }"C:\\PactoJ\\Desenvolvimento\\Sistemas\\ZillyonWeb\\tronco-novo\\src\\main\\resources\\relatorio\\designRelatorio\\estoque\\"t java.lang.Stringppt 
SUBREPORT_DIRpsq ~ 7pppq ~Îpsq ~ 4 ppt nomeEmpresapsq ~ 7pppt java.lang.Stringpsq ~ 4 ppt enderecoEmpresapsq ~ 7pppt java.lang.Stringpsq ~ 4 ppt 
nomeCidadepsq ~ 7pppt java.lang.Stringpsq ~ 4 sq ~    uq ~    sq ~ t "Faturamento por PerÃ­odo"t java.lang.Stringppt titulopsq ~ 7pppq ~âpsq ~ 4 ppt emAbertoQtdpsq ~ 7pppt java.lang.Integerpsq ~ 4 ppt periodopsq ~ 7pppt java.lang.Stringpsq ~ 4 ppt periodoValorpsq ~ 7pppt java.lang.Stringpsq ~ 4 ppt competenciapsq ~ 7pppt java.lang.Booleanpsq ~ 4 ppt especiepsq ~ 7pppt java.lang.Stringpsq ~ 4 ppt 
especieQtdpsq ~ 7pppt java.lang.Stringpsq ~ 4 ppt 
especieVlrpsq ~ 7pppt java.lang.Stringpsq ~ 4 ppt 	totalizarpsq ~ 7pppt java.lang.Booleanpsq ~ 7psq ~    w   t ireport.zoomt 	ireport.xt 	ireport.yxsr java.util.HashMapÚÁÃ`Ñ F 
loadFactorI 	thresholdxp?@     w      q ~t 2.8531167061100033q ~t 1263q ~	t 127xsr ,net.sf.jasperreports.engine.base.JRBaseQuery      'Ø [ chunkst +[Lnet/sf/jasperreports/engine/JRQueryChunk;L languageq ~ xppt xPathppppuq ~ t   sq ~ v  wî   q ~ }ppq ~ ppsq ~    uq ~    sq ~ t new java.lang.Integer(1)q ~ Fpq ~ pq ~ q ~ Fpsq ~ v  wî   q ~ }ppq ~ ppsq ~    uq ~    sq ~ t new java.lang.Integer(1)q ~ Fpq ~ pq ~ q ~ Fpsq ~ v  wî   q ~ sq ~    uq ~    sq ~ t new java.lang.Integer(1)q ~ Fppq ~ ppsq ~    uq ~    sq ~ t new java.lang.Integer(0)q ~ Fpq ~ ¡pq ~ q ~ Fpsq ~ v  wî   q ~ sq ~    uq ~    sq ~ t new java.lang.Integer(1)q ~ Fppq ~ ppsq ~    uq ~    sq ~ t new java.lang.Integer(0)q ~ Fpq ~ «pq ~ q ~ Fpsq ~ v  wî   q ~ sq ~    uq ~    sq ~ t new java.lang.Integer(1)q ~ Fppq ~ ppsq ~    	uq ~    sq ~ t new java.lang.Integer(0)q ~ Fpq ~ µpq ~ ¶q ~ Fpsq ~ v  wî    ~q ~ |t SUMsq ~    
uq ~    sq ~ t valorCompetenciat java.lang.Doubleppq ~ ppsq ~    uq ~    sq ~ t 
new Double(0)q ~@pt 
valorTotalpq ~ q ~@pq ~ ¹q ~ysq ~ sq ~    w   sr 1net.sf.jasperreports.engine.base.JRBaseStaticText      'Ø L textq ~ xq ~  wñ          
      
pq ~ q ~Fppppppq ~!ppppq ~$  wñppppppsq ~&   p~q ~Ht CENTERq ~Wppppppppsq ~)psq ~-  wñppppq ~Mq ~Mq ~Ipsq ~4  wñppppq ~Mq ~Mpsq ~.  wñppppq ~Mq ~Mpsq ~7  wñppppq ~Mq ~Mpsq ~9  wñppppq ~Mq ~Mpppppppppppppppppt INÃ£o hÃ¡ dados para serem exibidos ! verifique os parÃ¢metros informados.xp  wñ   3ppp~r 0net.sf.jasperreports.engine.type.OrientationEnum          xq ~ t PORTRAITsq ~ sq ~    w   sq ~
  wñ           K  ­    pq ~ q ~Wpt 
textField-211ppppq ~!ppppq ~$  wñpppppt Arialsq ~&   pq ~	q ~Wppppppppsq ~)sq ~&   sq ~-  wñsr java.awt.Color¥3u F falphaI valueL cst Ljava/awt/color/ColorSpace;[ 	frgbvaluet [F[ fvalueq ~bxp    ÿfffpppp~r .net.sf.jasperreports.engine.type.LineStyleEnum          xq ~ t SOLIDsr java.lang.FloatÚíÉ¢Û<ðì F valuexq ~'    q ~]q ~]q ~Ypsq ~4  wñsq ~`    ÿfffppppq ~esq ~g    q ~]q ~]psq ~.  wñppppq ~]q ~]psq ~7  wñsq ~`    ÿ   ppppq ~esq ~g    q ~]q ~]psq ~9  wñsq ~`    ÿ   ppppq ~esq ~g    q ~]q ~]pppppt Helvetica-Boldppppppppppp  wñ        ppq ~?sq ~    $uq ~    sq ~ t "PÃ¡gina: " + sq ~ t PAGE_NUMBERsq ~ t 	 + " de "t java.lang.Stringppppppsq ~V pppsq ~
  wñ             ø    pq ~ q ~Wpt 
textField-212ppppq ~!ppppq ~$  wñpppppt Arialq ~\ppq ~Wppppppppsq ~)q ~^sq ~-  wñsq ~`    ÿfffppppq ~esq ~g    q ~q ~q ~~psq ~4  wñsq ~`    ÿfffppppq ~esq ~g    q ~q ~psq ~.  wñppppq ~q ~psq ~7  wñsq ~`    ÿfffppppq ~esq ~g    q ~q ~psq ~9  wñsq ~`    ÿ   ppppq ~esq ~g    q ~q ~pppppt Helvetica-Boldppppppppppp  wñ        pp~q ~>t REPORTsq ~    %uq ~    sq ~ t " " + sq ~ t PAGE_NUMBERsq ~ t  + ""t java.lang.Stringppppppq ~}pppsq ~H  wñ           N      )pq ~ q ~Wppppppq ~!ppppq ~$  wñppppppq ~\ppq ~Wppppppppsq ~)psq ~-  wñppppq ~q ~q ~psq ~4  wñppppq ~q ~psq ~.  wñppppq ~q ~psq ~7  wñppppq ~q ~psq ~9  wñppppq ~q ~pppppppppppppppppt Total de registros atÃ© aqui:sq ~
  wñ               N   )pq ~ q ~Wppppppq ~!ppppq ~$  wñppppppq ~\pppppppppppsq ~)psq ~-  wñppppq ~¤q ~¤q ~£psq ~4  wñppppq ~¤q ~¤psq ~.  wñppppq ~¤q ~¤psq ~7  wñppppq ~¤q ~¤psq ~9  wñppppq ~¤q ~¤ppppppppppppppppp  wñ        ppq ~?sq ~    &uq ~    sq ~ t REPORT_COUNTt java.lang.Integerppppppppppxp  wñ   3pppsq ~ sq ~    w   sq ~H  wñ           P      `sq ~`    ÿÌÌÌpppq ~ q ~¯ppp~q ~t OPAQUEppq ~!ppppq ~$  wñppppppq ~(ppq ~Wppppppppsq ~)psq ~-  wñppppq ~µq ~µq ~±psq ~4  wñppppq ~µq ~µpsq ~.  wñppppq ~µq ~µpsq ~7  wñppppq ~µq ~µpsq ~9  wñppppq ~µq ~µppppppppppppppppq ~<t Produtosq ~H  wñ           (   Q   `sq ~`    ÿÌÌÌpppq ~ q ~¯pppq ~³ppq ~!ppppq ~$  wñppppppq ~(ppq ~Wppppppppsq ~)psq ~-  wñppppq ~¾q ~¾q ~¼psq ~4  wñppppq ~¾q ~¾psq ~.  wñppppq ~¾q ~¾psq ~7  wñppppq ~¾q ~¾psq ~9  wñppppq ~¾q ~¾ppppppppppppppppq ~<t 
MatrÃ­culasq ~H  wñ           _   y   `sq ~`    ÿÌÌÌpppq ~ q ~¯pppq ~³ppq ~!ppppq ~$  wñppppppq ~(pq ~Iq ~Wppppppppsq ~)psq ~-  wñppppq ~Çq ~Çq ~Åpsq ~4  wñppppq ~Çq ~Çpsq ~.  wñppppq ~Çq ~Çpsq ~7  wñppppq ~Çq ~Çpsq ~9  wñppppq ~Çq ~Çppppppppppppppppq ~<t Nomesq ~H  wñ           Z   Ø   `sq ~`    ÿÌÌÌpppq ~ q ~¯pppq ~³ppq ~!ppppq ~$  wñppppppq ~(pq ~Iq ~Wppppppppsq ~)psq ~-  wñppppq ~Ðq ~Ðq ~Îpsq ~4  wñppppq ~Ðq ~Ðpsq ~.  wñppppq ~Ðq ~Ðpsq ~7  wñppppq ~Ðq ~Ðpsq ~9  wñppppq ~Ðq ~Ðppppppppppppppppq ~<t ResponsÃ¡velsq ~H  wñ           7  2   `sq ~`    ÿÌÌÌpppq ~ q ~¯pppq ~³ppq ~!ppppq ~$  wñppppppq ~(pq ~Iq ~Wppppppppsq ~)psq ~-  wñppppq ~Ùq ~Ùq ~×psq ~4  wñppppq ~Ùq ~Ùpsq ~.  wñppppq ~Ùq ~Ùpsq ~7  wñppppq ~Ùq ~Ùpsq ~9  wñppppq ~Ùq ~Ùppppppppppppppppq ~<t Dt. Cadastrosq ~H  wñ           '  i   `sq ~`    ÿÌÌÌpppq ~ q ~¯pppq ~³ppq ~!ppppq ~$  wñppppppq ~(pq ~Iq ~Wppppppppsq ~)psq ~-  wñppppq ~âq ~âq ~àpsq ~4  wñppppq ~âq ~âpsq ~.  wñppppq ~âq ~âpsq ~7  wñppppq ~âq ~âpsq ~9  wñppppq ~âq ~âppppppppppppppppq ~<t NÂº Contratosq ~H  wñ           -     `sq ~`    ÿÌÌÌpppq ~ q ~¯pppq ~³ppq ~!ppppq ~$  wñppppppq ~(pq ~Iq ~Wppppppppsq ~)psq ~-  wñppppq ~ëq ~ëq ~épsq ~4  wñppppq ~ëq ~ëpsq ~.  wñppppq ~ëq ~ëpsq ~7  wñppppq ~ëq ~ëpsq ~9  wñppppq ~ëq ~ëppppppppppppppppq ~<t Dt. InÃ­ciosq ~H  wñ           7  ½   `sq ~`    ÿÌÌÌpppq ~ q ~¯pppq ~³ppq ~!ppppq ~$  wñppppppq ~(pq ~Iq ~Wppppppppsq ~)psq ~-  wñppppq ~ôq ~ôq ~òpsq ~4  wñppppq ~ôq ~ôpsq ~.  wñppppq ~ôq ~ôpsq ~7  wñppppq ~ôq ~ôpsq ~9  wñppppq ~ôq ~ôppppppppppppppppq ~<t Dt. TÃ©rminosq ~H  wñ             ô   `sq ~`    ÿÌÌÌpppq ~ q ~¯pppq ~³ppq ~!ppppq ~$  wñppppppq ~(pq ~Iq ~Wppppppppsq ~)psq ~-  wñppppq ~ýq ~ýq ~ûpsq ~4  wñppppq ~ýq ~ýpsq ~.  wñppppq ~ýq ~ýpsq ~7  wñppppq ~ýq ~ýpsq ~9  wñppppq ~ýq ~ýppppppppppppppppq ~<t 	DuraÃ§Ã£osq ~H  wñ           8     `sq ~`    ÿÌÌÌpppq ~ q ~¯pppq ~³ppq ~!ppppq ~$  wñppppppq ~(pq ~Iq ~Wppppppppsq ~)psq ~-  wñppppq ~q ~q ~psq ~4  wñppppq ~q ~psq ~.  wñppppq ~q ~psq ~7  wñppppq ~q ~psq ~9  wñppppq ~q ~ppppppppppppppppq ~<t Modalidadessq ~H  wñ           <     `sq ~`    ÿÌÌÌpppq ~ q ~¯pppq ~³ppq ~!ppppq ~$  wñppppppq ~(pq ~Iq ~Wppppppppsq ~)psq ~-  wñppppq ~q ~q ~
psq ~4  wñppppq ~q ~psq ~.  wñppppq ~q ~psq ~7  wñppppq ~q ~psq ~9  wñppppq ~q ~ppppppppppppppppq ~<t Planosq ~H  wñ           F  È   `sq ~`    ÿÌÌÌpppq ~ q ~¯pppq ~³ppq ~!ppppq ~$  wñppppppq ~(pq ~Iq ~Wppppppppsq ~)psq ~-  wñppppq ~q ~q ~psq ~4  wñppppq ~q ~psq ~.  wñppppq ~q ~psq ~7  wñppppq ~q ~psq ~9  wñppppq ~q ~ppppppppppppppppq ~<t SituaÃ§Ã£o Contratosq ~H  wñ           7     `sq ~`    ÿÌÌÌpppq ~ q ~¯pppq ~³ppq ~!ppppq ~$  wñppppppq ~(pq ~Iq ~Wppppppppsq ~)psq ~-  wñppppq ~!q ~!q ~psq ~4  wñppppq ~!q ~!psq ~.  wñppppq ~!q ~!psq ~7  wñppppq ~!q ~!psq ~9  wñppppq ~!q ~!ppppppppppppppppq ~<t 
Dt. LanÃ§.sq ~H  wñ           $  h   `sq ~`    ÿÌÌÌpppq ~ q ~¯pppq ~³ppq ~!ppppq ~$  wñppppppq ~(pq ~Iq ~Wppppppppsq ~)psq ~-  wñppppq ~*q ~*q ~(psq ~4  wñppppq ~*q ~*psq ~.  wñppppq ~*q ~*psq ~7  wñppppq ~*q ~*psq ~9  wñppppq ~*q ~*ppppppppppppppppq ~<t 	Categoriasq ~H  wñ           W     `sq ~`    ÿÌÌÌpppq ~ q ~¯pppq ~³ppq ~!ppppq ~$  wñppppppq ~(pq ~Iq ~Wppppppppsq ~)psq ~-  wñppppq ~3q ~3q ~1psq ~4  wñppppq ~3q ~3psq ~.  wñppppq ~3q ~3psq ~7  wñppppq ~3q ~3psq ~9  wñppppq ~3q ~3ppppppppppppppppq ~<t CondiÃ§Ã£o Pagamentosq ~H  wñ           4  Ú   `sq ~`    ÿÌÌÌpppq ~ q ~¯pppq ~³ppq ~!ppppq ~$  wñppppppq ~(pq ~Iq ~Wppppppppsq ~)psq ~-  wñppppq ~<q ~<q ~:psq ~4  wñppppq ~<q ~<psq ~.  wñppppq ~<q ~<psq ~7  wñppppq ~<q ~<psq ~9  wñppppq ~<q ~<ppppppppppppppppq ~<t Valorsq ~H  wñ               pq ~ q ~¯pt 
staticText-14pq ~³ppq ~!ppppq ~$  wñpppppt Microsoft Sans Serifsq ~&   	pq ~	q ~Wq ~Wpq ~}pq ~}pppsq ~)psq ~-  wñsq ~`    ÿfffppppq ~esq ~g    q ~Gq ~Gq ~Cpsq ~4  wñsq ~`    ÿfffppppq ~esq ~g    q ~Gq ~Gpsq ~.  wñppppq ~Gq ~Gpsq ~7  wñsq ~`    ÿfffppppq ~esq ~g    q ~Gq ~Gpsq ~9  wñsq ~`    ÿfffppppq ~esq ~g    q ~Gq ~Gp~r 0net.sf.jasperreports.engine.type.LineSpacingEnum          xq ~ t SINGLEpppt Helvetica-BoldObliquepppppppppp~q ~;t TOPt gZillyonweb- Sistema Administrativo para Academias Desenvolvido por PACTO SoluÃ§Ãµes TecnolÃ³gicas Ltda.sq ~H  wñ           `  ®   pq ~ q ~¯pt 
staticText-15pq ~³ppq ~!ppppq ~$  wñpppppt Microsoft Sans Serifq ~\pq ~	q ~Wq ~Wpq ~}pq ~}pppsq ~)psq ~-  wñsq ~`    ÿfffppppq ~esq ~g    q ~_q ~_q ~\psq ~4  wñsq ~`    ÿfffppppq ~esq ~g    q ~_q ~_psq ~.  wñppppq ~_q ~_psq ~7  wñsq ~`    ÿfffppppq ~esq ~g    q ~_q ~_psq ~9  wñsq ~`    ÿfffppppq ~esq ~g    q ~_q ~_pq ~Vpppt Helvetica-BoldObliqueppppppppppq ~Yt (0xx62) 3251-5820sq ~
  wñ           !  í   !sq ~`    ÿÿÿÿpppq ~ q ~¯pt 	dataRel-1pq ~ppq ~!ppppq ~$  wñpppppt Arialq ~\pq ~	q ~Wq ~}pq ~}pppppsq ~)sq ~&    sq ~-  wñsq ~`    ÿÿÿÿppppq ~esq ~g?   q ~sq ~sq ~opsq ~4  wñsq ~`    ÿÿÿÿppppq ~esq ~g?   q ~sq ~spsq ~.  wñsq ~`    ÿÿÿÿppppppq ~sq ~spsq ~7  wñsq ~`    ÿÿÿÿppppq ~esq ~g?   q ~sq ~spsq ~9  wñsq ~`    ÿÿÿÿppppq ~esq ~g?   q ~sq ~spppppt Helvetica-Boldppppppppppq ~<  wñ        ppq ~?sq ~    uq ~    sq ~ t 
new Date()t java.util.Dateppppppq ~}ppt dd/MM/yyyy HH.mm.sssr ,net.sf.jasperreports.engine.base.JRBaseImage      'Ø *I PSEUDO_SERIAL_VERSION_UIDI 
bookmarkLevelB evaluationTimeB hyperlinkTargetB 
hyperlinkTypeZ isLazyB onErrorTypeL anchorNameExpressionq ~ L borderq ~ L borderColorq ~L bottomBorderq ~ L bottomBorderColorq ~L 
bottomPaddingq ~L evaluationGroupq ~ xL evaluationTimeValueq ~L 
expressionq ~ L horizontalAlignmentq ~ L horizontalAlignmentValueq ~L hyperlinkAnchorExpressionq ~ L hyperlinkPageExpressionq ~ [ hyperlinkParametersq ~L hyperlinkReferenceExpressionq ~ L hyperlinkTooltipExpressionq ~ L isUsingCacheq ~
L 
leftBorderq ~ L leftBorderColorq ~L leftPaddingq ~L lineBoxq ~L 
linkTargetq ~ L linkTypeq ~ L onErrorTypeValuet 2Lnet/sf/jasperreports/engine/type/OnErrorTypeEnum;L paddingq ~L rightBorderq ~ L rightBorderColorq ~L rightPaddingq ~L 
scaleImageq ~ L scaleImageValuet 1Lnet/sf/jasperreports/engine/type/ScaleImageEnum;L 	topBorderq ~ L topBorderColorq ~L 
topPaddingq ~L verticalAlignmentq ~ L verticalAlignmentValueq ~xq ~è  wñ   0       y       pq ~ q ~¯sq ~`    ÿÿÿÿpppt image-1pppp~q ~ t FLOATpppp~q ~#t RELATIVE_TO_BAND_HEIGHT  wîppsq ~/  wñsq ~`    ÿÿÿÿppppppq ~p  wñ         ppppppp~q ~>t PAGEsq ~    
uq ~    sq ~ t logoPadraoRelatoriot java.io.InputStreamppppppppq ~Wpppsq ~)psq ~-  wñppppq ~q ~q ~psq ~4  wñppppq ~q ~psq ~.  wñppppq ~q ~psq ~7  wñppppq ~q ~psq ~9  wñppppq ~q ~pp~r 0net.sf.jasperreports.engine.type.OnErrorTypeEnum          xq ~ t BLANKpppppppppppsq ~H  wñ           9  ´   !pq ~ q ~¯ppppppq ~!ppppq ~$  wñppppppq ~\ppq ~Wppppppppsq ~)psq ~-  wñppppq ~§q ~§q ~¦psq ~4  wñppppq ~§q ~§psq ~.  wñppppq ~§q ~§psq ~7  wñppppq ~§q ~§psq ~9  wñppppq ~§q ~§ppppppppppppppppq ~<t Data da Impressao:sq ~
  wñ           ¬   z   pq ~ q ~¯ppppppq ~!ppppq ~$  wñppppppppppppppppppsq ~)psq ~-  wñppppq ~¯q ~¯q ~®psq ~4  wñppppq ~¯q ~¯psq ~.  wñppppq ~¯q ~¯psq ~7  wñppppq ~¯q ~¯psq ~9  wñppppq ~¯q ~¯ppppppppppppppppq ~<  wñ       ppq ~?sq ~    uq ~    sq ~ t nomeEmpresat java.lang.Stringppppppq ~Wpppsq ~
  wñ           ¬   z   pq ~ q ~¯pppq ~ppq ~!pppp~q ~#t RELATIVE_TO_TALLEST_OBJECT  wñppppppppppppppppppsq ~)psq ~-  wñppppq ~½q ~½q ~ºpsq ~4  wñppppq ~½q ~½psq ~.  wñppppq ~½q ~½psq ~7  wñppppq ~½q ~½psq ~9  wñppppq ~½q ~½ppppppppppppppppq ~<  wñ       ppq ~?sq ~    uq ~    sq ~ t enderecoEmpresat java.lang.Stringppppppq ~Wpppsq ~
  wñ           ¬   z   $pq ~ q ~¯pppq ~ppq ~!ppppq ~»  wñppppppppppppppppppsq ~)psq ~-  wñppppq ~Éq ~Éq ~Èpsq ~4  wñppppq ~Éq ~Épsq ~.  wñppppq ~Éq ~Épsq ~7  wñppppq ~Éq ~Épsq ~9  wñppppq ~Éq ~Éppppppppppppppppq ~<  wñ       ppq ~?sq ~    uq ~    sq ~ t 
nomeCidadet java.lang.Stringppppppq ~Wpppsq ~
  wñ          
      @pq ~ q ~¯ppppppq ~!ppppq ~$  wñppppppsq ~&   pq ~Kq ~Wppppppppsq ~)psq ~-  wñppppq ~Öq ~Öq ~Ôpsq ~4  wñppppq ~Öq ~Öpsq ~.  wñppppq ~Öq ~Öpsq ~7  wñppppq ~Öq ~Öpsq ~9  wñppppq ~Öq ~Öppppppppppppppppq ~<  wñ        ppq ~?sq ~    uq ~    sq ~ t titulot java.lang.Stringppppppppppsq ~H  wñ           >  E   `sq ~`    ÿÌÌÌpppq ~ q ~¯pppq ~³ppq ~!ppppq ~$  wñppppppq ~(pq ~Iq ~Wppppppppsq ~)psq ~-  wñppppq ~ãq ~ãq ~ápsq ~4  wñppppq ~ãq ~ãpsq ~.  wñppppq ~ãq ~ãpsq ~7  wñppppq ~ãq ~ãpsq ~9  wñppppq ~ãq ~ãppppppppppppppppq ~<t Forma Pagamentosq ~H  wñ             J   `sq ~`    ÿÌÌÌpppq ~ q ~¯pppq ~³ppq ~!ppppq ~$  wñppppppq ~(pq ~Iq ~Wppppppppsq ~)psq ~-  wñppppq ~ìq ~ìq ~êpsq ~4  wñppppq ~ìq ~ìpsq ~.  wñppppq ~ìq ~ìpsq ~7  wñppppq ~ìq ~ìpsq ~9  wñppppq ~ìq ~ìppppppppppppppppq ~<t Turmasxp  wñ   lppp~r /net.sf.jasperreports.engine.type.PrintOrderEnum          xq ~ t VERTICALpsq ~ sq ~    w   sq ~H  wñ           F  V   pq ~ q ~öppppppq ~!ppppq ~$  wñppppppq ~(pq ~	q ~Wppppppppsq ~)psq ~-  wñppppq ~ùq ~ùq ~øpsq ~4  wñppppq ~ùq ~ùpsq ~.  wñppppq ~ùq ~ùpsq ~7  wñppppq ~ùq ~ùpsq ~9  wñppppq ~ùq ~ùppppppppppppppppq ~<t Valor Total:sq ~
  wñ           q     pq ~ q ~öppppppq ~!ppppq ~$  wñppppppq ~Gpq ~	pppppppppsq ~)psq ~-  wñppppq ~q ~q ~ psq ~4  wñppppq ~q ~psq ~.  wñppppq ~q ~psq ~7  wñppppq ~q ~psq ~9  wñppppq ~q ~ppppppppppppppppq ~<  wñ       ppq ~?sq ~    'uq ~    sq ~ t 
valorTotalt java.lang.Doubleppppppq ~Wppt R$ #,##0.00sq ~
  wñ           +  +   pq ~ q ~öppppppq ~!ppppq ~$  wñppppppq ~Gpq ~	pppppppppsq ~)psq ~-  wñppppq ~q ~q ~
psq ~4  wñppppq ~q ~psq ~.  wñppppq ~q ~psq ~7  wñppppq ~q ~psq ~9  wñppppq ~q ~ppppppppppppppppp  wñ       ppq ~?sq ~    (uq ~    sq ~ t emAbertoQtdt java.lang.Integerppppppq ~Wpppsq ~H  wñ           A  ê   pq ~ q ~öppppppq ~!ppppq ~$  wñppppppq ~(pq ~	q ~Wppppq ~}pppsq ~)psq ~-  wñppppq ~q ~q ~psq ~4  wñppppq ~q ~psq ~.  wñppppq ~q ~psq ~7  wñppppq ~q ~psq ~9  wñppppq ~q ~ppppppppppppppppq ~<t Quantidade:sq ~H  wñ           b      pq ~ q ~öppppppq ~!sq ~    )uq ~    sq ~ t competenciasq ~ t  == trueq ~ ppppq ~$  wñppppppq ~(ppq ~Wppppq ~}pppsq ~)psq ~-  wñppppq ~(q ~(q ~!psq ~4  wñppppq ~(q ~(psq ~.  wñppppq ~(q ~(psq ~7  wñppppq ~(q ~(psq ~9  wñppppq ~(q ~(ppppppppppppppppq ~<t PRODUTOS PAGOSsq ~H  wñ           b       pq ~ q ~öppppppq ~!sq ~    *uq ~    sq ~ t competenciasq ~ t  == trueq ~ ppppq ~$  wñppppppq ~(ppq ~Wppppq ~}pppsq ~)psq ~-  wñppppq ~6q ~6q ~/psq ~4  wñppppq ~6q ~6psq ~.  wñppppq ~6q ~6psq ~7  wñppppq ~6q ~6psq ~9  wñppppq ~6q ~6ppppppppppppppppq ~<t MÃS /ANO DE VENCIMENTOsq ~H  wñ           N   c    pq ~ q ~öppppppq ~!sq ~    +uq ~    sq ~ t competenciasq ~ t  == trueq ~ ppppq ~$  wñppppppq ~(pq ~	q ~Wppppq ~}pppsq ~)psq ~-  wñppppq ~Dq ~Dq ~=psq ~4  wñppppq ~Dq ~Dpsq ~.  wñppppq ~Dq ~Dpsq ~7  wñppppq ~Dq ~Dpsq ~9  wñppppq ~Dq ~Dppppppppppppppppq ~<t VALORsq ~
  wñ           Y   c   ,pq ~ q ~öppppppq ~!sq ~    ,uq ~    sq ~ t competenciasq ~ t  == trueq ~ ppppq ~  wñppppppq ~(pq ~	pppppppppsq ~)psq ~-  wñppppq ~Rq ~Rq ~Kpsq ~4  wñppppq ~Rq ~Rpsq ~.  wñppppq ~Rq ~Rpsq ~7  wñppppq ~Rq ~Rpsq ~9  wñppppq ~Rq ~Rppppppppppppppppp  wñ       ppq ~?sq ~    -uq ~    sq ~ t periodoValort java.lang.Stringppppppq ~Wpppsq ~æ  wñ                pq ~ q ~öppppppq ~!ppppq ~$  wîppsq ~/  wñppppq ~]p  wñ q ~îsq ~H  wñ           4  û   pq ~ q ~öppppppq ~!sq ~    .uq ~    sq ~ t 	totalizarsq ~ t  == trueq ~ ppppq ~$  wñppppppq ~(pq ~Kq ~Wppppq ~}pppsq ~)psq ~-  wñppppq ~fq ~fq ~_psq ~4  wñppppq ~fq ~fpsq ~.  wñppppq ~fq ~fpsq ~7  wñppppq ~fq ~fpsq ~9  wñppppq ~fq ~fppppppppppppppppq ~<t 
QUANTIDADEsq ~H  wñ           V  /   pq ~ q ~öppppppq ~!sq ~    /uq ~    sq ~ t 	totalizarsq ~ t  == trueq ~ ppppq ~$  wñppppppq ~(pq ~	q ~Wppppq ~}pppsq ~)psq ~-  wñppppq ~tq ~tq ~mpsq ~4  wñppppq ~tq ~tpsq ~.  wñppppq ~tq ~tpsq ~7  wñppppq ~tq ~tpsq ~9  wñppppq ~tq ~tppppppppppppppppq ~<t VALORsq ~
  wñ           k      pq ~ q ~öppppppq ~!sq ~    0uq ~    sq ~ t 	totalizarsq ~ t  == trueq ~ ppppq ~  wñppppppq ~(pppppppppppsq ~)psq ~-  wñppppq ~q ~q ~{psq ~4  wñppppq ~q ~psq ~.  wñppppq ~q ~psq ~7  wñppppq ~q ~psq ~9  wñppppq ~q ~ppppppppppppppppp  wñ       ppq ~?sq ~    1uq ~    sq ~ t especiet java.lang.Stringppppppq ~Wpppsq ~
  wñ           )  û    pq ~ q ~öppppppq ~!sq ~    2uq ~    sq ~ t 	totalizarsq ~ t  == trueq ~ ppppq ~  wñppppppq ~(pq ~	pppppppppsq ~)psq ~-  wñppppq ~q ~q ~psq ~4  wñppppq ~q ~psq ~.  wñppppq ~q ~psq ~7  wñppppq ~q ~psq ~9  wñppppq ~q ~ppppppppppppppppp  wñ       ppq ~?sq ~    3uq ~    sq ~ t 
especieQtdt java.lang.Stringppppppq ~Wpppsq ~H  wñ           k     pq ~ q ~öppppppq ~!sq ~    4uq ~    sq ~ t 	totalizarsq ~ t  == trueq ~ ppppq ~$  wñppppppq ~(ppq ~Wppppq ~}pppsq ~)psq ~-  wñppppq ~¦q ~¦q ~psq ~4  wñppppq ~¦q ~¦psq ~.  wñppppq ~¦q ~¦psq ~7  wñppppq ~¦q ~¦psq ~9  wñppppq ~¦q ~¦ppppppppppppppppq ~<t ESPÃCIEsq ~
  wñ           a  /    pq ~ q ~öppppppq ~!sq ~    5uq ~    sq ~ t 	totalizarsq ~ t  == trueq ~ ppppq ~  wñppppppq ~(pq ~	pppppppppsq ~)psq ~-  wñppppq ~´q ~´q ~­psq ~4  wñppppq ~´q ~´psq ~.  wñppppq ~´q ~´psq ~7  wñppppq ~´q ~´psq ~9  wñppppq ~´q ~´ppppppppppppppppp  wñ       ppq ~?sq ~    6uq ~    sq ~ t 
especieVlrt java.lang.Stringppppppq ~Wpppsq ~
  wñ           b      ,pq ~ q ~öppppppq ~!sq ~    7uq ~    sq ~ t competenciasq ~ t  == trueq ~ ppppq ~  wñppppppq ~(pppppppppppsq ~)psq ~-  wñppppq ~Æq ~Æq ~¿psq ~4  wñppppq ~Æq ~Æpsq ~.  wñppppq ~Æq ~Æpsq ~7  wñppppq ~Æq ~Æpsq ~9  wñppppq ~Æq ~Æppppppppppppppppp  wñ       ppq ~?sq ~    8uq ~    sq ~ t periodot java.lang.Stringppppppq ~Wpppxp  wñ   @ppq ~ pp~r 3net.sf.jasperreports.engine.type.WhenNoDataTypeEnum          xq ~ t NO_DATA_SECTIONsr 6net.sf.jasperreports.engine.design.JRReportCompileData      'Ø L crosstabCompileDataq ~ 8L datasetCompileDataq ~ 8L mainDatasetCompileDataq ~ xpsq ~
?@     w       xsq ~
?@     w      q ~ ¼ur [B¬óøTà  xp  äÊþº¾   .  >RelatorioEstoqueProduto_Table32Dataset321_1745613812136_763936  ,net/sf/jasperreports/engine/fill/JREvaluator  parameter_REPORT_LOCALE 2Lnet/sf/jasperreports/engine/fill/JRFillParameter; parameter_JASPER_REPORT parameter_REPORT_TIME_ZONE parameter_SORT_FIELDS parameter_REPORT_FILE_RESOLVER parameter_REPORT_SCRIPTLET parameter_REPORT_PARAMETERS_MAP parameter_REPORT_CONNECTION parameter_REPORT_CLASS_LOADER parameter_REPORT_DATA_SOURCE $parameter_REPORT_URL_HANDLER_FACTORY parameter_REPORT_FORMAT_FACTORY parameter_REPORT_MAX_COUNT parameter_REPORT_TEMPLATES  parameter_REPORT_RESOURCE_BUNDLE variable_PAGE_NUMBER 1Lnet/sf/jasperreports/engine/fill/JRFillVariable; variable_COLUMN_NUMBER variable_REPORT_COUNT variable_PAGE_COUNT variable_COLUMN_COUNT <init> ()V Code  
    	     	  "  	  $ 	 	  & 
 	  (  	  *  	  , 
 	  .  	  0  	  2  	  4  	  6  	  8  	  :  	  <  	  >  	  @  	  B  	  D  	  F LineNumberTable customizedInit 0(Ljava/util/Map;Ljava/util/Map;Ljava/util/Map;)V 
initParams (Ljava/util/Map;)V K L
  M 
initFields O L
  P initVars R L
  S 
REPORT_LOCALE U 
java/util/Map W get &(Ljava/lang/Object;)Ljava/lang/Object; Y Z X [ 0net/sf/jasperreports/engine/fill/JRFillParameter ] 
JASPER_REPORT _ REPORT_TIME_ZONE a SORT_FIELDS c REPORT_FILE_RESOLVER e REPORT_SCRIPTLET g REPORT_PARAMETERS_MAP i REPORT_CONNECTION k REPORT_CLASS_LOADER m REPORT_DATA_SOURCE o REPORT_URL_HANDLER_FACTORY q REPORT_FORMAT_FACTORY s REPORT_MAX_COUNT u REPORT_TEMPLATES w REPORT_RESOURCE_BUNDLE y PAGE_NUMBER { /net/sf/jasperreports/engine/fill/JRFillVariable } 
COLUMN_NUMBER  REPORT_COUNT  
PAGE_COUNT  COLUMN_COUNT  evaluate (I)Ljava/lang/Object; 
Exceptions java/lang/Throwable  java/lang/Integer  (I)V  
   evaluateOld evaluateEstimated 
SourceFile !                      	     
               
                                                                        Õ     i*· *µ !*µ #*µ %*µ '*µ )*µ +*µ -*µ /*µ 1*µ 3*µ 5*µ 7*µ 9*µ ;*µ =*µ ?*µ A*µ C*µ E*µ G±    H   Z       	          "  '   , ! 1 " 6 # ; $ @ % E & J ' O ( T ) Y * ^ + c , h   I J     4     *+· N*,· Q*-· T±    H       8  9 
 :  ;  K L    c    *+V¹ \ À ^À ^µ !*+`¹ \ À ^À ^µ #*+b¹ \ À ^À ^µ %*+d¹ \ À ^À ^µ '*+f¹ \ À ^À ^µ )*+h¹ \ À ^À ^µ +*+j¹ \ À ^À ^µ -*+l¹ \ À ^À ^µ /*+n¹ \ À ^À ^µ 1*+p¹ \ À ^À ^µ 3*+r¹ \ À ^À ^µ 5*+t¹ \ À ^À ^µ 7*+v¹ \ À ^À ^µ 9*+x¹ \ À ^À ^µ ;*+z¹ \ À ^À ^µ =±    H   B    C  D $ E 6 F H G Z H l I ~ J  K ¢ L ´ M Æ N Ø O ê P ü Q R  O L           ±    H       Z  R L          [*+|¹ \ À ~À ~µ ?*+¹ \ À ~À ~µ A*+¹ \ À ~À ~µ C*+¹ \ À ~À ~µ E*+¹ \ À ~À ~µ G±    H       b  c $ d 6 e H f Z g              ë     Mª             -   9   E   Q   ]   i   u   » Y· M§ T» Y· M§ H» Y· M§ <» Y· M§ 0» Y· M§ $» Y· M§ » Y· M§ » Y· M,°    H   J    o  q 0 u 9 v < z E { H  Q  T  ]  `  i  l  u  x                      ë     Mª             -   9   E   Q   ]   i   u   » Y· M§ T» Y· M§ H» Y· M§ <» Y· M§ 0» Y· M§ $» Y· M§ » Y· M§ » Y· M,°    H   J    ©  « 0 ¯ 9 ° < ´ E µ H ¹ Q º T ¾ ] ¿ ` Ã i Ä l È u É x Í  Î  Ò  Ú              ë     Mª             -   9   E   Q   ]   i   u   » Y· M§ T» Y· M§ H» Y· M§ <» Y· M§ 0» Y· M§ $» Y· M§ » Y· M§ » Y· M,°    H   J    ã  å 0 é 9 ê < î E ï H ó Q ô T ø ] ù ` ý i þ l u x         q ~ 1uq ~Ø  ÛÊþº¾   .  5RelatorioEstoqueProduto_dataset1_1745613812136_763936  ,net/sf/jasperreports/engine/fill/JREvaluator  parameter_REPORT_LOCALE 2Lnet/sf/jasperreports/engine/fill/JRFillParameter; parameter_JASPER_REPORT parameter_REPORT_TIME_ZONE parameter_SORT_FIELDS parameter_REPORT_FILE_RESOLVER parameter_REPORT_SCRIPTLET parameter_REPORT_PARAMETERS_MAP parameter_REPORT_CONNECTION parameter_REPORT_CLASS_LOADER parameter_REPORT_DATA_SOURCE $parameter_REPORT_URL_HANDLER_FACTORY parameter_REPORT_FORMAT_FACTORY parameter_REPORT_MAX_COUNT parameter_REPORT_TEMPLATES  parameter_REPORT_RESOURCE_BUNDLE variable_PAGE_NUMBER 1Lnet/sf/jasperreports/engine/fill/JRFillVariable; variable_COLUMN_NUMBER variable_REPORT_COUNT variable_PAGE_COUNT variable_COLUMN_COUNT <init> ()V Code  
    	     	  "  	  $ 	 	  & 
 	  (  	  *  	  , 
 	  .  	  0  	  2  	  4  	  6  	  8  	  :  	  <  	  >  	  @  	  B  	  D  	  F LineNumberTable customizedInit 0(Ljava/util/Map;Ljava/util/Map;Ljava/util/Map;)V 
initParams (Ljava/util/Map;)V K L
  M 
initFields O L
  P initVars R L
  S 
REPORT_LOCALE U 
java/util/Map W get &(Ljava/lang/Object;)Ljava/lang/Object; Y Z X [ 0net/sf/jasperreports/engine/fill/JRFillParameter ] 
JASPER_REPORT _ REPORT_TIME_ZONE a SORT_FIELDS c REPORT_FILE_RESOLVER e REPORT_SCRIPTLET g REPORT_PARAMETERS_MAP i REPORT_CONNECTION k REPORT_CLASS_LOADER m REPORT_DATA_SOURCE o REPORT_URL_HANDLER_FACTORY q REPORT_FORMAT_FACTORY s REPORT_MAX_COUNT u REPORT_TEMPLATES w REPORT_RESOURCE_BUNDLE y PAGE_NUMBER { /net/sf/jasperreports/engine/fill/JRFillVariable } 
COLUMN_NUMBER  REPORT_COUNT  
PAGE_COUNT  COLUMN_COUNT  evaluate (I)Ljava/lang/Object; 
Exceptions java/lang/Throwable  java/lang/Integer  (I)V  
   evaluateOld evaluateEstimated 
SourceFile !                      	     
               
                                                                        Õ     i*· *µ !*µ #*µ %*µ '*µ )*µ +*µ -*µ /*µ 1*µ 3*µ 5*µ 7*µ 9*µ ;*µ =*µ ?*µ A*µ C*µ E*µ G±    H   Z       	          "  '   , ! 1 " 6 # ; $ @ % E & J ' O ( T ) Y * ^ + c , h   I J     4     *+· N*,· Q*-· T±    H       8  9 
 :  ;  K L    c    *+V¹ \ À ^À ^µ !*+`¹ \ À ^À ^µ #*+b¹ \ À ^À ^µ %*+d¹ \ À ^À ^µ '*+f¹ \ À ^À ^µ )*+h¹ \ À ^À ^µ +*+j¹ \ À ^À ^µ -*+l¹ \ À ^À ^µ /*+n¹ \ À ^À ^µ 1*+p¹ \ À ^À ^µ 3*+r¹ \ À ^À ^µ 5*+t¹ \ À ^À ^µ 7*+v¹ \ À ^À ^µ 9*+x¹ \ À ^À ^µ ;*+z¹ \ À ^À ^µ =±    H   B    C  D $ E 6 F H G Z H l I ~ J  K ¢ L ´ M Æ N Ø O ê P ü Q R  O L           ±    H       Z  R L          [*+|¹ \ À ~À ~µ ?*+¹ \ À ~À ~µ A*+¹ \ À ~À ~µ C*+¹ \ À ~À ~µ E*+¹ \ À ~À ~µ G±    H       b  c $ d 6 e H f Z g              ë     Mª             -   9   E   Q   ]   i   u   » Y· M§ T» Y· M§ H» Y· M§ <» Y· M§ 0» Y· M§ $» Y· M§ » Y· M§ » Y· M,°    H   J    o  q 0 u 9 v < z E { H  Q  T  ]  `  i  l  u  x                      ë     Mª             -   9   E   Q   ]   i   u   » Y· M§ T» Y· M§ H» Y· M§ <» Y· M§ 0» Y· M§ $» Y· M§ » Y· M§ » Y· M,°    H   J    ©  « 0 ¯ 9 ° < ´ E µ H ¹ Q º T ¾ ] ¿ ` Ã i Ä l È u É x Í  Î  Ò  Ú              ë     Mª             -   9   E   Q   ]   i   u   » Y· M§ T» Y· M§ H» Y· M§ <» Y· M§ 0» Y· M§ $» Y· M§ » Y· M§ » Y· M,°    H   J    ã  å 0 é 9 ê < î E ï H ó Q ô T ø ] ù ` ý i þ l u x         xuq ~Ø  3Êþº¾   .µ ,RelatorioEstoqueProduto_1745613812136_763936  ,net/sf/jasperreports/engine/fill/JREvaluator  parameter_enderecoEmpresa 2Lnet/sf/jasperreports/engine/fill/JRFillParameter; parameter_nomeCidade parameter_JASPER_REPORT parameter_ordenacao parameter_REPORT_TIME_ZONE parameter_periodoValor parameter_REPORT_FILE_RESOLVER parameter_REPORT_PARAMETERS_MAP parameter_especieVlr parameter_REPORT_CLASS_LOADER parameter_competencia $parameter_REPORT_URL_HANDLER_FACTORY parameter_REPORT_DATA_SOURCE parameter_totalizar parameter_IS_IGNORE_PAGINATION parameter_XML_DATE_PATTERN parameter_XML_DATA_DOCUMENT parameter_REPORT_MAX_COUNT parameter_XML_LOCALE parameter_REPORT_TEMPLATES parameter_XML_NUMBER_PATTERN parameter_periodo parameter_REPORT_LOCALE parameter_REPORT_VIRTUALIZER parameter_especieQtd parameter_SORT_FIELDS parameter_logoPadraoRelatorio parameter_REPORT_SCRIPTLET parameter_emAbertoQtd parameter_totalizadores parameter_REPORT_CONNECTION parameter_tipoVisualizacao parameter_valorImpresso parameter_SUBREPORT_DIR parameter_titulo parameter_REPORT_FORMAT_FACTORY parameter_nomeEmpresa parameter_statusProduto  parameter_REPORT_RESOURCE_BUNDLE parameter_XML_TIME_ZONE parameter_especie field_dataLancamentoProduto .Lnet/sf/jasperreports/engine/fill/JRFillField; field_dataTermino field_formaPagApresentar field_matriculaCliente field_valorCompetencia field_dataCadastroCliente field_duracaoPlano field_condicaoPagamento field_turma field_descricaoProduto field_categoria field_nomePlano field_nomeResponsavelLancamento field_dataInicio field_situacaoContrato field_modalidades field_codContrato field_nomeCliente variable_PAGE_NUMBER 1Lnet/sf/jasperreports/engine/fill/JRFillVariable; variable_COLUMN_NUMBER variable_REPORT_COUNT variable_PAGE_COUNT variable_COLUMN_COUNT variable_valorTotal <init> ()V Code I J
  L  	  N  	  P  	  R 	 	  T 
 	  V  	  X  	  Z 
 	  \  	  ^  	  `  	  b  	  d  	  f  	  h  	  j  	  l  	  n  	  p  	  r  	  t  	  v  	  x  	  z  	  |  	  ~  	     	   ! 	   " 	   # 	   $ 	   % 	   & 	   ' 	   ( 	   ) 	   * 	   + 	   , 	   - 	   . 	   / 0	    1 0	  ¢ 2 0	  ¤ 3 0	  ¦ 4 0	  ¨ 5 0	  ª 6 0	  ¬ 7 0	  ® 8 0	  ° 9 0	  ² : 0	  ´ ; 0	  ¶ < 0	  ¸ = 0	  º > 0	  ¼ ? 0	  ¾ @ 0	  À A 0	  Â B C	  Ä D C	  Æ E C	  È F C	  Ê G C	  Ì H C	  Î LineNumberTable customizedInit 0(Ljava/util/Map;Ljava/util/Map;Ljava/util/Map;)V 
initParams (Ljava/util/Map;)V Ó Ô
  Õ 
initFields × Ô
  Ø initVars Ú Ô
  Û enderecoEmpresa Ý 
java/util/Map ß get &(Ljava/lang/Object;)Ljava/lang/Object; á â à ã 0net/sf/jasperreports/engine/fill/JRFillParameter å 
nomeCidade ç 
JASPER_REPORT é 	ordenacao ë REPORT_TIME_ZONE í periodoValor ï REPORT_FILE_RESOLVER ñ REPORT_PARAMETERS_MAP ó 
especieVlr õ REPORT_CLASS_LOADER ÷ competencia ù REPORT_URL_HANDLER_FACTORY û REPORT_DATA_SOURCE ý 	totalizar ÿ IS_IGNORE_PAGINATION XML_DATE_PATTERN XML_DATA_DOCUMENT REPORT_MAX_COUNT 
XML_LOCALE	 REPORT_TEMPLATES XML_NUMBER_PATTERN
 periodo 
REPORT_LOCALE REPORT_VIRTUALIZER 
especieQtd SORT_FIELDS logoPadraoRelatorio REPORT_SCRIPTLET emAbertoQtd 
totalizadores REPORT_CONNECTION! tipoVisualizacao# 
valorImpresso% 
SUBREPORT_DIR' titulo) REPORT_FORMAT_FACTORY+ nomeEmpresa- 
statusProduto/ REPORT_RESOURCE_BUNDLE1 
XML_TIME_ZONE3 especie5 dataLancamentoProduto7 ,net/sf/jasperreports/engine/fill/JRFillField9 dataTermino; formaPagApresentar= matriculaCliente? valorCompetenciaA dataCadastroClienteC duracaoPlanoE condicaoPagamentoG turmaI descricaoProdutoK 	categoriaM 	nomePlanoO nomeResponsavelLancamentoQ 
dataInicioS situacaoContratoU modalidadesW codContratoY nomeCliente[ PAGE_NUMBER] /net/sf/jasperreports/engine/fill/JRFillVariable_ 
COLUMN_NUMBERa REPORT_COUNTc 
PAGE_COUNTe COLUMN_COUNTg 
valorTotali evaluate (I)Ljava/lang/Object; 
Exceptions java/lang/Throwablen oC:\PactoJ\Desenvolvimento\Sistemas\ZillyonWeb\tronco-novo\src\main\resources\relatorio\designRelatorio\estoque\p Faturamento por PerÃ­odor java/lang/Integert (I)V Iv
uw getValue ()Ljava/lang/Object;yz
:{ java/lang/Double} (D)V I
~ java/util/Date
 L
 æ{ java/io/InputStream java/lang/String java/lang/StringBuffer 	PÃ¡gina:  (Ljava/lang/String;)V I

`{ append ,(Ljava/lang/Object;)Ljava/lang/StringBuffer;
  de  ,(Ljava/lang/String;)Ljava/lang/StringBuffer;
 toString ()Ljava/lang/String;
   java/lang/Boolean¡ booleanValue ()Z£¤
¢¥ valueOf (Z)Ljava/lang/Boolean;§¨
¢© evaluateOld getOldValue¬z
:­
`­ evaluateEstimated getEstimatedValue±z
`² 
SourceFile !     A                 	     
               
                                                                                                     !     "     #     $     %     &     '     (     )     *     +     ,     -     .     / 0    1 0    2 0    3 0    4 0    5 0    6 0    7 0    8 0    9 0    : 0    ; 0    < 0    = 0    > 0    ? 0    @ 0    A 0    B C    D C    E C    F C    G C    H C     I J  K  j    J*· M*µ O*µ Q*µ S*µ U*µ W*µ Y*µ [*µ ]*µ _*µ a*µ c*µ e*µ g*µ i*µ k*µ m*µ o*µ q*µ s*µ u*µ w*µ y*µ {*µ }*µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ ¡*µ £*µ ¥*µ §*µ ©*µ «*µ ­*µ ¯*µ ±*µ ³*µ µ*µ ·*µ ¹*µ »*µ ½*µ ¿*µ Á*µ Ã*µ Å*µ Ç*µ É*µ Ë*µ Í*µ Ï±    Ð   C      	          "  '   , ! 1 " 6 # ; $ @ % E & J ' O ( T ) Y * ^ + c , h - m . r / w 0 | 1  2  3  4  5  6  7  8 ¤ 9 © : ® ; ³ < ¸ = ½ > Â ? Ç @ Ì A Ñ B Ö C Û D à E å F ê G ï H ô I ù J þ K L M
 N O P Q! R& S+ T0 U5 V: W? XD YI   Ñ Ò  K   4     *+· Ö*,· Ù*-· Ü±    Ð       e  f 
 g  h  Ó Ô  K  »    ÿ*+Þ¹ ä À æÀ æµ O*+è¹ ä À æÀ æµ Q*+ê¹ ä À æÀ æµ S*+ì¹ ä À æÀ æµ U*+î¹ ä À æÀ æµ W*+ð¹ ä À æÀ æµ Y*+ò¹ ä À æÀ æµ [*+ô¹ ä À æÀ æµ ]*+ö¹ ä À æÀ æµ _*+ø¹ ä À æÀ æµ a*+ú¹ ä À æÀ æµ c*+ü¹ ä À æÀ æµ e*+þ¹ ä À æÀ æµ g*+ ¹ ä À æÀ æµ i*+¹ ä À æÀ æµ k*+¹ ä À æÀ æµ m*+¹ ä À æÀ æµ o*+¹ ä À æÀ æµ q*+
¹ ä À æÀ æµ s*+¹ ä À æÀ æµ u*+¹ ä À æÀ æµ w*+¹ ä À æÀ æµ y*+¹ ä À æÀ æµ {*+¹ ä À æÀ æµ }*+¹ ä À æÀ æµ *+¹ ä À æÀ æµ *+¹ ä À æÀ æµ *+¹ ä À æÀ æµ *+¹ ä À æÀ æµ *+ ¹ ä À æÀ æµ *+"¹ ä À æÀ æµ *+$¹ ä À æÀ æµ *+&¹ ä À æÀ æµ *+(¹ ä À æÀ æµ *+*¹ ä À æÀ æµ *+,¹ ä À æÀ æµ *+.¹ ä À æÀ æµ *+0¹ ä À æÀ æµ *+2¹ ä À æÀ æµ *+4¹ ä À æÀ æµ *+6¹ ä À æÀ æµ ±    Ð   ª *   p  q $ r 6 s H t Z u l v ~ w  x ¢ y ´ z Æ { Ø | ê } ý ~ # 6 I \ o   ¨ » Î á ô   - @ S f y   ² Å Ø ë þ   × Ô  K  ·    W*+8¹ ä À:À:µ ¡*+<¹ ä À:À:µ £*+>¹ ä À:À:µ ¥*+@¹ ä À:À:µ §*+B¹ ä À:À:µ ©*+D¹ ä À:À:µ «*+F¹ ä À:À:µ ­*+H¹ ä À:À:µ ¯*+J¹ ä À:À:µ ±*+L¹ ä À:À:µ ³*+N¹ ä À:À:µ µ*+P¹ ä À:À:µ ·*+R¹ ä À:À:µ ¹*+T¹ ä À:À:µ »*+V¹ ä À:À:µ ½*+X¹ ä À:À:µ ¿*+Z¹ ä À:À:µ Á*+\¹ ä À:À:µ Ã±    Ð   N    ¡  ¢ & £ 9 ¤ L ¥ _ ¦ r §  ¨  © « ª ¾ « Ñ ¬ ä ­ ÷ ®
 ¯ °0 ±C ²V ³  Ú Ô  K   £     s*+^¹ ä À`À`µ Å*+b¹ ä À`À`µ Ç*+d¹ ä À`À`µ É*+f¹ ä À`À`µ Ë*+h¹ ä À`À`µ Í*+j¹ ä À`À`µ Ï±    Ð       »  ¼ & ½ 9 ¾ L ¿ _ À r Á kl m    o K      ®Mª  ©       8   ñ   ø   ÿ      #  /  ;  G  S  _  m  y         ®  ¼  Ê  Ø  æ  ô        ,  :  H  V  d  r        ª  ¸  Æ  ê      $  2  N  j    ¢  °  Ì  è      .  <  X  t    qM§´sM§­»uY·xM§¡»uY·xM§»uY·xM§»uY·xM§}»uY·xM§q»uY·xM§e»uY·xM§Y»uY·xM§M*´ ©¶|À~M§?»~Y·M§3»Y·M§(*´ ¶ÀM§*´ ¶ÀM§*´ O¶ÀM§þ*´ Q¶ÀM§ð*´ ¶ÀM§â*´ ³¶|ÀM§Ô*´ «¶|ÀM§Æ*´ §¶|ÀM§¸*´ Á¶|ÀuM§ª*´ Ã¶|ÀM§*´ ¹¶|ÀM§*´ »¶|ÀM§*´ ­¶|ÀuM§r*´ ¿¶|ÀM§d*´ ·¶|ÀM§V*´ ½¶|ÀM§H*´ ¡¶|ÀM§:*´ £¶|ÀM§,*´ ¯¶|ÀM§*´ ¥¶|ÀM§*´ ©¶|À~M§*´ ±¶|ÀM§ô*´ µ¶|ÀM§æ»Y·*´ Å¶Àu¶¶¶M§Â»Y ·*´ Å¶Àu¶¶M§¤*´ É¶ÀuM§*´ Ï¶À~M§*´ ¶ÀuM§z*´ c¶À¢¶¦ § ¸ªM§^*´ c¶À¢¶¦ § ¸ªM§B*´ c¶À¢¶¦ § ¸ªM§&*´ c¶À¢¶¦ § ¸ªM§
*´ Y¶ÀM§ ü*´ i¶À¢¶¦ § ¸ªM§ à*´ i¶À¢¶¦ § ¸ªM§ Ä*´ i¶À¢¶¦ § ¸ªM§ ¨*´ ¶ÀM§ *´ i¶À¢¶¦ § ¸ªM§ ~*´ ¶ÀM§ p*´ i¶À¢¶¦ § ¸ªM§ T*´ i¶À¢¶¦ § ¸ªM§ 8*´ _¶ÀM§ **´ c¶À¢¶¦ § ¸ªM§ *´ y¶ÀM,°    Ð  Ò t   É  Ë ô Ï ø Ð û Ô ÿ Õ Ù Ú Þ ß ã# ä& è/ é2 í; î> òG óJ ÷S øV ü_ ýbmpy| £®±¼ ¿$Ê%Í)Ø*Û.æ/é3ô4÷89=>BC!G,H/L:M=QHRKVVWY[d\g`rauefjkoptªu­y¸z»~ÆÉêí$'25NQ¡j¢m¦§«¢¬¥°°±³µÌ¶Ïºè»ë¿ÀÄÅÉ.Ê1Î<Ï?ÓXÔ[ØtÙwÝÞâã¡ç¬ï «l m    o K      ®Mª  ©       8   ñ   ø   ÿ      #  /  ;  G  S  _  m  y         ®  ¼  Ê  Ø  æ  ô        ,  :  H  V  d  r        ª  ¸  Æ  ê      $  2  N  j    ¢  °  Ì  è      .  <  X  t    qM§´sM§­»uY·xM§¡»uY·xM§»uY·xM§»uY·xM§}»uY·xM§q»uY·xM§e»uY·xM§Y»uY·xM§M*´ ©¶®À~M§?»~Y·M§3»Y·M§(*´ ¶ÀM§*´ ¶ÀM§*´ O¶ÀM§þ*´ Q¶ÀM§ð*´ ¶ÀM§â*´ ³¶®ÀM§Ô*´ «¶®ÀM§Æ*´ §¶®ÀM§¸*´ Á¶®ÀuM§ª*´ Ã¶®ÀM§*´ ¹¶®ÀM§*´ »¶®ÀM§*´ ­¶®ÀuM§r*´ ¿¶®ÀM§d*´ ·¶®ÀM§V*´ ½¶®ÀM§H*´ ¡¶®ÀM§:*´ £¶®ÀM§,*´ ¯¶®ÀM§*´ ¥¶®ÀM§*´ ©¶®À~M§*´ ±¶®ÀM§ô*´ µ¶®ÀM§æ»Y·*´ Å¶¯Àu¶¶¶M§Â»Y ·*´ Å¶¯Àu¶¶M§¤*´ É¶¯ÀuM§*´ Ï¶¯À~M§*´ ¶ÀuM§z*´ c¶À¢¶¦ § ¸ªM§^*´ c¶À¢¶¦ § ¸ªM§B*´ c¶À¢¶¦ § ¸ªM§&*´ c¶À¢¶¦ § ¸ªM§
*´ Y¶ÀM§ ü*´ i¶À¢¶¦ § ¸ªM§ à*´ i¶À¢¶¦ § ¸ªM§ Ä*´ i¶À¢¶¦ § ¸ªM§ ¨*´ ¶ÀM§ *´ i¶À¢¶¦ § ¸ªM§ ~*´ ¶ÀM§ p*´ i¶À¢¶¦ § ¸ªM§ T*´ i¶À¢¶¦ § ¸ªM§ 8*´ _¶ÀM§ **´ c¶À¢¶¦ § ¸ªM§ *´ y¶ÀM,°    Ð  Ò t  ø ú ôþ øÿ û ÿ	
#&/2;>!G"J&S'V+_,b0m1p5y6|:;?@D E£I®J±N¼O¿SÊTÍXØYÛ]æ^ébôc÷ghlmqr!v,w/{:|=HKVYdgru£ª¤­¨¸©»­Æ®É²ê³í·¸¼½Á$Â'Æ2Ç5ËNÌQÐjÑmÕÖÚ¢Û¥ß°à³äÌåÏéèêëîïóôø.ù1ý<þ?X[tw
¡¬ °l m    o K      ®Mª  ©       8   ñ   ø   ÿ      #  /  ;  G  S  _  m  y         ®  ¼  Ê  Ø  æ  ô        ,  :  H  V  d  r        ª  ¸  Æ  ê      $  2  N  j    ¢  °  Ì  è      .  <  X  t    qM§´sM§­»uY·xM§¡»uY·xM§»uY·xM§»uY·xM§}»uY·xM§q»uY·xM§e»uY·xM§Y»uY·xM§M*´ ©¶|À~M§?»~Y·M§3»Y·M§(*´ ¶ÀM§*´ ¶ÀM§*´ O¶ÀM§þ*´ Q¶ÀM§ð*´ ¶ÀM§â*´ ³¶|ÀM§Ô*´ «¶|ÀM§Æ*´ §¶|ÀM§¸*´ Á¶|ÀuM§ª*´ Ã¶|ÀM§*´ ¹¶|ÀM§*´ »¶|ÀM§*´ ­¶|ÀuM§r*´ ¿¶|ÀM§d*´ ·¶|ÀM§V*´ ½¶|ÀM§H*´ ¡¶|ÀM§:*´ £¶|ÀM§,*´ ¯¶|ÀM§*´ ¥¶|ÀM§*´ ©¶|À~M§*´ ±¶|ÀM§ô*´ µ¶|ÀM§æ»Y·*´ Å¶³Àu¶¶¶M§Â»Y ·*´ Å¶³Àu¶¶M§¤*´ É¶³ÀuM§*´ Ï¶³À~M§*´ ¶ÀuM§z*´ c¶À¢¶¦ § ¸ªM§^*´ c¶À¢¶¦ § ¸ªM§B*´ c¶À¢¶¦ § ¸ªM§&*´ c¶À¢¶¦ § ¸ªM§
*´ Y¶ÀM§ ü*´ i¶À¢¶¦ § ¸ªM§ à*´ i¶À¢¶¦ § ¸ªM§ Ä*´ i¶À¢¶¦ § ¸ªM§ ¨*´ ¶ÀM§ *´ i¶À¢¶¦ § ¸ªM§ ~*´ ¶ÀM§ p*´ i¶À¢¶¦ § ¸ªM§ T*´ i¶À¢¶¦ § ¸ªM§ 8*´ _¶ÀM§ **´ c¶À¢¶¦ § ¸ªM§ *´ y¶ÀM,°    Ð  Ò t  ' ) ô- ø. û2 ÿ378<=A#B&F/G2K;L>PGQJUSVVZ_[b_m`pdye|ijnos t£x®y±}¼~¿ÊÍØÛæéô÷ ¡!¥,¦/ª:«=¯H°K´VµY¹dºg¾r¿uÃÄÈÉÍÎÒªÓ­×¸Ø»ÜÆÝÉáêâíæçëìð$ñ'õ2ö5úNûQÿj m	¢
¥°³ÌÏèë"#'.(1,<-?1X2[6t7w;<@A¡E¬M ´    t _1745613812136_763936t 2net.sf.jasperreports.engine.design.JRJavacCompiler
<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="CaixaPorOperadorRel" pageWidth="680" pageHeight="878" whenNoDataType="AllSectionsNoDetail" columnWidth="625" leftMargin="20" rightMargin="35" topMargin="2" bottomMargin="2" whenResourceMissingType="Empty">
	<property name="ireport.zoom" value="1.5"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.encoding" value="ISO-8859-1"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="java.util.*"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<subDataset name="Teste"/>
	<parameter name="tituloRelatorio" class="java.lang.String" isForPrompting="false"/>
	<parameter name="nomeEmpresa" class="java.lang.String" isForPrompting="false"/>
	<parameter name="versaoSoftware" class="java.lang.String" isForPrompting="false"/>
	<parameter name="usuario" class="java.lang.String" isForPrompting="false"/>
	<parameter name="filtros" class="java.lang.String" isForPrompting="false"/>
	<parameter name="SUBREPORT_DIR" class="java.lang.String">
		<defaultValueExpression><![CDATA["E:\\desenvolvimentosv\\Projeto Pacto Solucoes\\ZillyonWeb\\src\\java\\relatorio\\designRelatorio\\financeiro\\"]]></defaultValueExpression>
	</parameter>
	<parameter name="SUBREPORT_DIR1" class="java.lang.String">
		<defaultValueExpression><![CDATA["E:\\desenvolvimentosv\\Projeto Pacto Solucoes\\ZillyonWeb\\src\\java\\relatorio\\designRelatorio\\financeiro\\"]]></defaultValueExpression>
	</parameter>
	<parameter name="dataIni" class="java.lang.String" isForPrompting="false"/>
	<parameter name="dataFim" class="java.lang.String" isForPrompting="false"/>
	<parameter name="qtdAV" class="java.lang.String" isForPrompting="false"/>
	<parameter name="qtdCA" class="java.lang.String" isForPrompting="false"/>
	<parameter name="qtdChequeAV" class="java.lang.String" isForPrompting="false"/>
	<parameter name="qtdChequePR" class="java.lang.String" isForPrompting="false"/>
	<parameter name="qtdOutro" class="java.lang.String" isForPrompting="false"/>
	<parameter name="valorAV" class="java.lang.Double" isForPrompting="false"/>
	<parameter name="valorCA" class="java.lang.Double" isForPrompting="false"/>
	<parameter name="valorChequeAV" class="java.lang.Double" isForPrompting="false"/>
	<parameter name="valorChequePR" class="java.lang.Double" isForPrompting="false"/>
	<parameter name="valorOutro" class="java.lang.Double" isForPrompting="false"/>
	<parameter name="logoPadraoRelatorio" class="java.io.InputStream" isForPrompting="false"/>
	<parameter name="qtdCD" class="java.lang.String"/>
	<parameter name="valorCD" class="java.lang.Double"/>
	<parameter name="SUBREPORT_DIR2" class="java.lang.String">
		<defaultValueExpression><![CDATA["E:\\desenvolvimentosv\\Projeto Pacto Solucoes\\ZillyonWeb\\src\\java\\relatorio\\designRelatorio\\financeiro\\"]]></defaultValueExpression>
	</parameter>
	<parameter name="qtdBB" class="java.lang.String"/>
	<parameter name="valorBB" class="java.lang.Double"/>
	<parameter name="qtdDV" class="java.lang.String" isForPrompting="false"/>
	<parameter name="valorDV" class="java.lang.Double" isForPrompting="false"/>
	<parameter name="devolucoes" class="java.lang.Object"/>
	<parameter name="apresentarDados" class="java.lang.Boolean"/>
	<parameter name="qtdDR" class="java.lang.String"/>
	<parameter name="valorDR" class="java.lang.Double"/>
	<parameter name="totalizadores" class="java.lang.Object"/>
	<parameter name="somenteSintetico" class="java.lang.Boolean"/>
	<parameter name="mostrarModalidade" class="java.lang.Boolean"/>
	<parameter name="moeda" class="java.lang.String">
		<defaultValueExpression><![CDATA["R$"]]></defaultValueExpression>
	</parameter>
	<field name="listaMovPagamento" class="java.lang.Object"/>
	<field name="listaMovProduto" class="java.lang.Object"/>
	<field name="reciboPagamentoVO.codigo" class="java.lang.Integer"/>
	<field name="reciboPagamentoVO.data" class="java.util.Date"/>
	<field name="reciboPagamentoVO.valorTotal" class="java.lang.Double"/>
	<field name="reciboPagamentoVO.nomePessoaPagador" class="java.lang.String"/>
	<field name="reciboPagamentoVO.responsavelLancamento.colaboradorVO.pessoa.nome" class="java.lang.String"/>
	<field name="numeroContrato" class="java.lang.String"/>
	<field name="nomeOperador" class="java.lang.String"/>
	<field name="matricula" class="java.lang.String"/>
	<field name="mostrarNumeroContrato" class="java.lang.Boolean"/>
	<field name="listaMovParcela" class="java.lang.Object"/>
	<field name="consultorResponsavel" class="java.lang.String"/>
	<field name="devolucao" class="java.lang.Boolean"/>
	<field name="descricaoDevolucao" class="java.lang.String"/>
	<field name="contrato" class="java.lang.Integer"/>
	<field name="valorDevolvidoMonetario" class="java.lang.String"/>
	<field name="centralEventos" class="java.lang.Boolean"/>
	<field name="movProduto.precoUnitario" class="java.lang.Double"/>
	<field name="descConfiguracaoVO.porcentagemDescontoApresentar" class="java.lang.String"/>
	<field name="movProduto.produto.tipoProduto" class="java.lang.String"/>
	<field name="listaDescontosRecibo" class="java.lang.Object"/>
	<field name="apresentarDescontos" class="java.lang.Boolean"/>
	<field name="modalidades" class="java.lang.String"/>
	<group name="caixaPoOperador" minHeightToStartNewPage="75">
		<groupExpression><![CDATA[$F{reciboPagamentoVO.codigo}]]></groupExpression>
		<groupHeader>
			<band height="43" splitType="Prevent">
				<printWhenExpression><![CDATA[$P{apresentarDados} && !($P{somenteSintetico}.booleanValue())]]></printWhenExpression>
				<textField isBlankWhenNull="false">
					<reportElement key="textField-211" mode="Opaque" x="117" y="5" width="309" height="12"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Microsoft Sans Serif" size="9" isBold="false" pdfFontName="Helvetica"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$F{nomeOperador}]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="false">
					<reportElement key="textField-229" mode="Opaque" x="221" y="29" width="280" height="12"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Microsoft Sans Serif" size="9" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$F{reciboPagamentoVO.nomePessoaPagador}]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="false">
					<reportElement key="textField-231" mode="Opaque" x="143" y="29" width="78" height="12"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Microsoft Sans Serif" size="9" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$F{matricula}]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="false">
					<reportElement key="textField-228" mode="Opaque" x="1" y="29" width="90" height="12"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Microsoft Sans Serif" size="9" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<textFieldExpression class="java.util.Date"><![CDATA[$F{reciboPagamentoVO.data}]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="false">
					<reportElement key="textField-211" mode="Opaque" x="1" y="5" width="116" height="12"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Microsoft Sans Serif" size="9" isBold="false" pdfFontName="Helvetica"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[""+($F{devolucao} ? " " : $R{Resp_Receb} )]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="false">
					<reportElement key="textField-212" mode="Opaque" x="117" y="17" width="175" height="12"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Microsoft Sans Serif" size="9" isBold="false" pdfFontName="Helvetica"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$F{consultorResponsavel}]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="false">
					<reportElement key="textField-211" mode="Opaque" x="1" y="17" width="106" height="12"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Microsoft Sans Serif" size="9" isBold="false" pdfFontName="Helvetica"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[""+($F{devolucao} ? $R{Resp_Devolucao} : $R{Consultor_Resp})]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="false">
					<reportElement key="textField-230" mode="Opaque" x="444" y="29" width="150" height="12"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Microsoft Sans Serif" size="9" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[""+($F{devolucao} ? "Contrato:"+$F{contrato} : "")]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="false">
					<reportElement key="textField-230" mode="Opaque" x="99" y="29" width="56" height="12"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Microsoft Sans Serif" size="9" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[""+($F{devolucao} ? "" : ""+$F{reciboPagamentoVO.codigo})]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="false">
					<reportElement key="textField-231" mode="Opaque" x="254" y="5" width="128" height="12"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Microsoft Sans Serif" size="9" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[""+($F{devolucao} ? $R{Devolucao} : "")]]></textFieldExpression>
				</textField>
				<line>
					<reportElement key="line-1" x="0" y="3" width="625" height="1"/>
				</line>
			</band>
		</groupHeader>
	</group>
	<group name="movParcela">
		<groupHeader>
			<band height="12" splitType="Prevent">
				<printWhenExpression><![CDATA[$P{apresentarDados} && $P{somenteSintetico}.equals( false )]]></printWhenExpression>
				<subreport isUsingCache="true">
					<reportElement key="subreport-2" stretchType="RelativeToBandHeight" x="10" y="0" width="615" height="12"/>
					<subreportParameter name="SUBREPORT_DIR">
						<subreportParameterExpression><![CDATA[$P{SUBREPORT_DIR}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="listaMovProduto">
						<subreportParameterExpression><![CDATA[$F{listaMovProduto}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="REPORT_RESOURCE_BUNDLE">
						<subreportParameterExpression><![CDATA[$P{REPORT_RESOURCE_BUNDLE}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="moeda">
						<subreportParameterExpression><![CDATA[$P{moeda}]]></subreportParameterExpression>
					</subreportParameter>
					<dataSourceExpression><![CDATA[$F{listaMovParcela}]]></dataSourceExpression>
					<subreportExpression class="java.lang.String"><![CDATA[$P{SUBREPORT_DIR2} + "MovParcela.jasper"]]></subreportExpression>
				</subreport>
			</band>
		</groupHeader>
	</group>
	<group name="movProduto">
		<groupExpression><![CDATA[]]></groupExpression>
		<groupHeader>
			<band height="15" splitType="Prevent">
				<printWhenExpression><![CDATA[$P{apresentarDados} && $P{somenteSintetico}.equals( false )]]></printWhenExpression>
				<textField isBlankWhenNull="false">
					<reportElement key="textField-211" mode="Opaque" x="12" y="1" width="320" height="12">
						<printWhenExpression><![CDATA[!$F{centralEventos}]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Microsoft Sans Serif" size="9" isBold="false" isUnderline="true" pdfFontName="Helvetica"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[""+($F{devolucao} ? $R{Calculo_Devolucao} : $R{Produtos_Recibo})]]></textFieldExpression>
				</textField>
			</band>
			<band height="16">
				<printWhenExpression><![CDATA[$P{apresentarDados} && $P{somenteSintetico}.equals( false )]]></printWhenExpression>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement key="textField-211" stretchType="RelativeToBandHeight" mode="Opaque" x="12" y="0" width="613" height="12"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Microsoft Sans Serif" size="9" isBold="false" pdfFontName="Helvetica"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$F{descricaoDevolucao}]]></textFieldExpression>
				</textField>
			</band>
			<band height="16">
				<printWhenExpression><![CDATA[$P{apresentarDados} && $P{somenteSintetico}.equals( false )]]></printWhenExpression>
				<subreport isUsingCache="false">
					<reportElement key="subreport-2" stretchType="RelativeToBandHeight" x="10" y="1" width="615" height="12"/>
					<subreportParameter name="SUBREPORT_DIR">
						<subreportParameterExpression><![CDATA[$P{SUBREPORT_DIR}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="listaMovProduto">
						<subreportParameterExpression><![CDATA[$F{listaMovProduto}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="REPORT_RESOURCE_BUNDLE">
						<subreportParameterExpression><![CDATA[$P{REPORT_RESOURCE_BUNDLE}]]></subreportParameterExpression>
					</subreportParameter>
					<dataSourceExpression><![CDATA[$F{listaMovProduto}]]></dataSourceExpression>
					<subreportExpression class="java.lang.String"><![CDATA[$P{SUBREPORT_DIR1} + "MovProduto.jasper"]]></subreportExpression>
				</subreport>
			</band>
			<band height="17">
				<printWhenExpression><![CDATA[$P{apresentarDados} && $P{somenteSintetico}.equals( false )]]></printWhenExpression>
				<textField isStretchWithOverflow="true">
					<reportElement x="7" y="4" width="613" height="11" isRemoveLineWhenBlank="true">
						<printWhenExpression><![CDATA[$P{apresentarDados} && $P{somenteSintetico}.equals( false )]]></printWhenExpression>
					</reportElement>
					<textElement textAlignment="Center">
						<font size="8"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$F{modalidades}]]></textFieldExpression>
				</textField>
			</band>
			<band height="29">
				<printWhenExpression><![CDATA[$P{apresentarDados} && $P{somenteSintetico}.equals( false )]]></printWhenExpression>
				<textField>
					<reportElement x="2" y="0" width="148" height="12" isRemoveLineWhenBlank="true">
						<printWhenExpression><![CDATA[$F{apresentarDescontos}.equals( true )]]></printWhenExpression>
					</reportElement>
					<textElement markup="none">
						<font fontName="Microsoft Sans Serif" size="9" isUnderline="true"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$R{Desc_Extra_Convenio}]]></textFieldExpression>
				</textField>
				<subreport>
					<reportElement x="0" y="12" width="615" height="13" isRemoveLineWhenBlank="true"/>
					<subreportParameter name="REPORT_RESOURCE_BUNDLE">
						<subreportParameterExpression><![CDATA[$P{REPORT_RESOURCE_BUNDLE}]]></subreportParameterExpression>
					</subreportParameter>
					<dataSourceExpression><![CDATA[$F{listaDescontosRecibo}]]></dataSourceExpression>
					<subreportExpression class="java.lang.String"><![CDATA[$P{SUBREPORT_DIR} + "Descontos.jasper"]]></subreportExpression>
				</subreport>
			</band>
		</groupHeader>
	</group>
	<group name="movPagamento">
		<groupExpression><![CDATA[]]></groupExpression>
		<groupHeader>
			<band height="45" splitType="Prevent">
				<printWhenExpression><![CDATA[$P{apresentarDados} && $P{somenteSintetico}.equals( false )]]></printWhenExpression>
				<subreport isUsingCache="true">
					<reportElement key="subreport-1" stretchType="RelativeToBandHeight" x="10" y="15" width="615" height="24"/>
					<subreportParameter name="SUBREPORT_DIR">
						<subreportParameterExpression><![CDATA[$P{SUBREPORT_DIR}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="REPORT_RESOURCE_BUNDLE">
						<subreportParameterExpression><![CDATA[$P{REPORT_RESOURCE_BUNDLE}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="listaMovPagamento">
						<subreportParameterExpression><![CDATA[$F{listaMovPagamento}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="moeda">
						<subreportParameterExpression><![CDATA[$P{moeda}]]></subreportParameterExpression>
					</subreportParameter>
					<dataSourceExpression><![CDATA[$F{listaMovPagamento}]]></dataSourceExpression>
					<subreportExpression class="java.lang.String"><![CDATA[$P{SUBREPORT_DIR} + "MovPagamento.jasper"]]></subreportExpression>
				</subreport>
				<textField isBlankWhenNull="false">
					<reportElement key="textField-211" mode="Opaque" x="10" y="0" width="202" height="12"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Microsoft Sans Serif" size="9" isBold="false" isUnderline="true" pdfFontName="Helvetica"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[""+($F{devolucao} ? "" : $R{Pag_Recibos})]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="false">
					<reportElement key="textField-231" mode="Opaque" x="212" y="0" width="308" height="12"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Microsoft Sans Serif" size="9" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[""+($F{devolucao} ? $R{Valor_Devolvido} + " " +
    $F{valorDevolvidoMonetario} : "")]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
	</group>
	<pageHeader>
		<band height="120" splitType="Stretch">
			<textField>
				<reportElement key="staticText-85" positionType="Float" mode="Opaque" x="1" y="77" width="90" height="14">
					<printWhenExpression><![CDATA[!($P{somenteSintetico}.booleanValue())]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle" markup="none">
					<font fontName="Microsoft Sans Serif" size="9" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$R{DT_Ent_Caixa}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement key="staticText-82" mode="Opaque" x="306" y="38" width="26" height="17"/>
				<box>
					<pen lineWidth="0.0" lineColor="#666666"/>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineColor="#666666"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="14" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[a]]></text>
			</staticText>
			<textField pattern="dd/MM/yyyy HH.mm.ss" isBlankWhenNull="false">
				<reportElement key="dataRel-1" mode="Transparent" x="520" y="0" width="105" height="12" backcolor="#FFFFFF"/>
				<box bottomPadding="0">
					<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Verdana" size="8" isItalic="false" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression class="java.util.Date"><![CDATA[new Date()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-2" x="83" y="19" width="437" height="19"/>
				<box>
					<pen lineWidth="0.5" lineStyle="Solid"/>
					<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="14" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$R{Titulo}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-215" x="83" y="38" width="223" height="17"/>
				<box>
					<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="14" isBold="true" isItalic="true" pdfFontName="Helvetica-BoldOblique"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{dataIni}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-216" x="332" y="38" width="188" height="17"/>
				<box>
					<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.5" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="14" isBold="true" isItalic="true" pdfFontName="Helvetica-BoldOblique"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{dataFim}]]></textFieldExpression>
			</textField>
			<image vAlign="Top" isUsingCache="true" onErrorType="Blank" evaluationTime="Page">
				<reportElement key="image-1" positionType="Float" x="1" y="0" width="82" height="38" isPrintWhenDetailOverflows="true"/>
				<box>
					<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<imageExpression class="java.io.InputStream"><![CDATA[$P{logoPadraoRelatorio}]]></imageExpression>
			</image>
			<textField>
				<reportElement key="staticText-121" positionType="Float" mode="Opaque" x="91" y="77" width="50" height="14">
					<printWhenExpression><![CDATA[!($P{somenteSintetico})]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Microsoft Sans Serif" size="9" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$R{Recibo}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement key="staticText-86" positionType="Float" mode="Opaque" x="141" y="77" width="80" height="14">
					<printWhenExpression><![CDATA[!($P{somenteSintetico})]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Microsoft Sans Serif" size="9" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$R{Matricula}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement key="staticText-88" positionType="Float" mode="Opaque" x="221" y="77" width="280" height="14">
					<printWhenExpression><![CDATA[!($P{somenteSintetico})]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle" markup="none">
					<font fontName="Microsoft Sans Serif" size="9" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$R{Nome_Resp_Pag}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement key="staticText-131" mode="Opaque" x="526" y="91" width="50" height="14">
					<printWhenExpression><![CDATA[!($P{somenteSintetico})]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Right" verticalAlignment="Middle" markup="none">
					<font fontName="Microsoft Sans Serif" size="9" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$R{Desconto}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement key="staticText-132" mode="Opaque" x="514" y="91" width="18" height="14">
					<printWhenExpression><![CDATA[!($P{somenteSintetico})]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Right" verticalAlignment="Middle" markup="none">
					<font fontName="Microsoft Sans Serif" size="9" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$R{Qtd}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement key="staticText-130" mode="Opaque" x="477" y="91" width="35" height="14">
					<printWhenExpression><![CDATA[!($P{somenteSintetico})]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Right" verticalAlignment="Middle" markup="none">
					<font fontName="Microsoft Sans Serif" size="9" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$R{Unitario}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement key="staticText-133" mode="Opaque" x="575" y="91" width="50" height="14">
					<printWhenExpression><![CDATA[!($P{somenteSintetico})]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Right" verticalAlignment="Middle" markup="none">
					<font fontName="Microsoft Sans Serif" size="9" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$R{Valor_Pago}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement key="staticText-129" mode="Opaque" x="49" y="91" width="205" height="14">
					<printWhenExpression><![CDATA[!($P{somenteSintetico})]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle" markup="none">
					<font fontName="Microsoft Sans Serif" size="9" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$R{Nome_Aluno}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement key="staticText-128" mode="Opaque" x="10" y="91" width="38" height="14">
					<printWhenExpression><![CDATA[!($P{somenteSintetico})]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle" markup="none">
					<font fontName="Microsoft Sans Serif" size="9" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$R{Contrato}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement key="staticText-127" mode="Opaque" x="254" y="91" width="172" height="14">
					<printWhenExpression><![CDATA[!($P{somenteSintetico})]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle" markup="none">
					<font fontName="Microsoft Sans Serif" size="9" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$R{Produto}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement key="staticText-89" mode="Opaque" x="83" y="105" width="72" height="14">
					<printWhenExpression><![CDATA[!($P{somenteSintetico})]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Microsoft Sans Serif" size="9" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$R{Valor_Pago}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement key="staticText-90" mode="Opaque" x="155" y="105" width="97" height="14">
					<printWhenExpression><![CDATA[!($P{somenteSintetico})]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle" markup="none">
					<font fontName="Microsoft Sans Serif" size="9" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$R{Forma_Pg}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement key="staticText-87" mode="Opaque" x="10" y="105" width="72" height="14">
					<printWhenExpression><![CDATA[!($P{somenteSintetico})]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle" markup="none">
					<font fontName="Microsoft Sans Serif" size="9" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$R{Data_Pagamento}]]></textFieldExpression>
			</textField>
			<textField pattern="">
				<reportElement mode="Transparent" x="520" y="12" width="105" height="12"/>
				<box>
					<leftPen lineWidth="0.5" lineStyle="Dotted"/>
					<bottomPen lineWidth="0.5" lineStyle="Dotted"/>
					<rightPen lineWidth="0.5" lineStyle="Dotted"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Verdana" size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA["Usuário:"+ $P{usuario}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="520" y="24" width="70" height="14"/>
				<box>
					<leftPen lineWidth="0.5" lineStyle="Dotted"/>
					<bottomPen lineWidth="0.5" lineStyle="Dotted"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Verdana" size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA["Página "+$V{PAGE_NUMBER}+" de"]]></textFieldExpression>
			</textField>
			<textField evaluationTime="Report">
				<reportElement x="590" y="24" width="35" height="14"/>
				<box leftPadding="4">
					<bottomPen lineWidth="0.5" lineStyle="Dotted"/>
					<rightPen lineWidth="0.5" lineStyle="Dotted"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Verdana" size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.Integer"><![CDATA[$V{PAGE_NUMBER}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="83" y="55" width="437" height="15"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="12"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{filtros}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement key="staticText-130" mode="Opaque" x="426" y="91" width="46" height="14">
					<printWhenExpression><![CDATA[!($P{somenteSintetico})]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle" markup="none">
					<font fontName="Microsoft Sans Serif" size="9" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$R{Pacote}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-2" x="83" y="0" width="437" height="19"/>
				<box>
					<pen lineWidth="0.5" lineStyle="Solid"/>
					<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="14" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{nomeEmpresa}]]></textFieldExpression>
			</textField>
		</band>
	</pageHeader>
	<summary>
		<band height="30" splitType="Immediate">
			<subreport>
				<reportElement stretchType="RelativeToBandHeight" x="2" y="0" width="625" height="21"/>
				<subreportParameter name="valorCD">
					<subreportParameterExpression><![CDATA[$P{valorCD}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="qtdDV">
					<subreportParameterExpression><![CDATA[$P{qtdDV}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="usuario">
					<subreportParameterExpression><![CDATA[$P{usuario}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="valorCA">
					<subreportParameterExpression><![CDATA[$P{valorCA}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="totalizadoresEspecies">
					<subreportParameterExpression><![CDATA[$P{totalizadores}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="qtdCA">
					<subreportParameterExpression><![CDATA[$P{qtdCA}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="SUBREPORT_DIR1">
					<subreportParameterExpression><![CDATA[$P{SUBREPORT_DIR1}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="valorChequeAV">
					<subreportParameterExpression><![CDATA[$P{valorChequeAV}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="qtdChequePR">
					<subreportParameterExpression><![CDATA[$P{qtdChequePR}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="valorChequePR">
					<subreportParameterExpression><![CDATA[$P{valorChequePR}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="SUBREPORT_DIR2">
					<subreportParameterExpression><![CDATA[$P{SUBREPORT_DIR2}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="qtdBB">
					<subreportParameterExpression><![CDATA[$P{qtdBB}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="valorOutro">
					<subreportParameterExpression><![CDATA[$P{valorOutro}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="somenteSintetico">
					<subreportParameterExpression><![CDATA[$P{somenteSintetico}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="qtdDR">
					<subreportParameterExpression><![CDATA[$P{qtdDR}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="qtdAV">
					<subreportParameterExpression><![CDATA[$P{qtdAV}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="dataIni">
					<subreportParameterExpression><![CDATA[$P{dataIni}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="valorBB">
					<subreportParameterExpression><![CDATA[$P{valorBB}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="qtdOutro">
					<subreportParameterExpression><![CDATA[$P{qtdOutro}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="logoPadraoRelatorio">
					<subreportParameterExpression><![CDATA[$P{logoPadraoRelatorio}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="totalizadores">
					<subreportParameterExpression><![CDATA[$P{totalizadores}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="apresentarDados">
					<subreportParameterExpression><![CDATA[$P{apresentarDados}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="SUBREPORT_DIR">
					<subreportParameterExpression><![CDATA[$P{SUBREPORT_DIR}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="qtdCD">
					<subreportParameterExpression><![CDATA[$P{qtdCD}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="dataFim">
					<subreportParameterExpression><![CDATA[$P{dataFim}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="valorDR">
					<subreportParameterExpression><![CDATA[$P{valorDR}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="tituloRelatorio">
					<subreportParameterExpression><![CDATA[$P{tituloRelatorio}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="nomeEmpresa">
					<subreportParameterExpression><![CDATA[$P{nomeEmpresa}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="valorDV">
					<subreportParameterExpression><![CDATA[$P{valorDV}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="qtdChequeAV">
					<subreportParameterExpression><![CDATA[$P{qtdChequeAV}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="valorAV">
					<subreportParameterExpression><![CDATA[$P{valorAV}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="devolucoes">
					<subreportParameterExpression><![CDATA[$P{devolucoes}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="moeda">
					<subreportParameterExpression><![CDATA[$P{moeda}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="REPORT_RESOURCE_BUNDLE">
					<subreportParameterExpression><![CDATA[$P{REPORT_RESOURCE_BUNDLE}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="versaoSoftware">
					<subreportParameterExpression><![CDATA[$P{versaoSoftware}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="filtros">
					<subreportParameterExpression><![CDATA[$P{filtros}]]></subreportParameterExpression>
				</subreportParameter>
				<dataSourceExpression><![CDATA[$P{devolucoes}]]></dataSourceExpression>
				<subreportExpression class="java.lang.String"><![CDATA[$P{SUBREPORT_DIR} + "CaixaReciboDevolucao.jasper"]]></subreportExpression>
			</subreport>
		</band>
	</summary>
</jasperReport>

¬í sr (net.sf.jasperreports.engine.JasperReport      'Ø L compileDatat Ljava/io/Serializable;L compileNameSuffixt Ljava/lang/String;L 
compilerClassq ~ xr -net.sf.jasperreports.engine.base.JRBaseReport      'Ø ,I PSEUDO_SERIAL_VERSION_UIDI bottomMarginI columnCountI 
columnSpacingI columnWidthZ ignorePaginationZ isFloatColumnFooterZ isSummaryNewPageZ  isSummaryWithPageHeaderAndFooterZ isTitleNewPageI 
leftMarginB orientationI 
pageHeightI 	pageWidthB 
printOrderI rightMarginI 	topMarginB whenNoDataTypeL 
backgroundt $Lnet/sf/jasperreports/engine/JRBand;L columnDirectiont 3Lnet/sf/jasperreports/engine/type/RunDirectionEnum;L columnFooterq ~ L columnHeaderq ~ [ datasetst ([Lnet/sf/jasperreports/engine/JRDataset;L defaultFontt *Lnet/sf/jasperreports/engine/JRReportFont;L defaultStylet %Lnet/sf/jasperreports/engine/JRStyle;L detailq ~ L 
detailSectiont 'Lnet/sf/jasperreports/engine/JRSection;[ fontst +[Lnet/sf/jasperreports/engine/JRReportFont;L formatFactoryClassq ~ L 
importsSett Ljava/util/Set;L languageq ~ L lastPageFooterq ~ L mainDatasett 'Lnet/sf/jasperreports/engine/JRDataset;L nameq ~ L noDataq ~ L orientationValuet 2Lnet/sf/jasperreports/engine/type/OrientationEnum;L 
pageFooterq ~ L 
pageHeaderq ~ L printOrderValuet 1Lnet/sf/jasperreports/engine/type/PrintOrderEnum;[ stylest &[Lnet/sf/jasperreports/engine/JRStyle;L summaryq ~ [ 	templatest /[Lnet/sf/jasperreports/engine/JRReportTemplate;L titleq ~ L whenNoDataTypeValuet 5Lnet/sf/jasperreports/engine/type/WhenNoDataTypeEnum;xp  wñ             Â           h   Ì        p~r 1net.sf.jasperreports.engine.type.RunDirectionEnum          xr java.lang.Enum          xpt LTRppppppsr .net.sf.jasperreports.engine.base.JRBaseSection      'Ø [ bandst %[Lnet/sf/jasperreports/engine/JRBand;xpur %[Lnet.sf.jasperreports.engine.JRBand;Ý~ìÊ5  xp   sr +net.sf.jasperreports.engine.base.JRBaseBand      'Ø I PSEUDO_SERIAL_VERSION_UIDI heightZ isSplitAllowedL printWhenExpressiont *Lnet/sf/jasperreports/engine/JRExpression;L 	splitTypet Ljava/lang/Byte;L splitTypeValuet 0Lnet/sf/jasperreports/engine/type/SplitTypeEnum;xr 3net.sf.jasperreports.engine.base.JRBaseElementGroup      'Ø L childrent Ljava/util/List;L elementGroupt ,Lnet/sf/jasperreports/engine/JRElementGroup;xpsr java.util.ArrayListxÒÇa I sizexp   w   
sr 1net.sf.jasperreports.engine.base.JRBaseStaticText      'Ø L textq ~ xr 2net.sf.jasperreports.engine.base.JRBaseTextElement      'Ø %I PSEUDO_SERIAL_VERSION_UIDL borderq ~ L borderColort Ljava/awt/Color;L bottomBorderq ~ L bottomBorderColorq ~ (L 
bottomPaddingt Ljava/lang/Integer;L fontNameq ~ L fontSizeq ~ )L horizontalAlignmentq ~ L horizontalAlignmentValuet 6Lnet/sf/jasperreports/engine/type/HorizontalAlignEnum;L isBoldt Ljava/lang/Boolean;L isItalicq ~ +L 
isPdfEmbeddedq ~ +L isStrikeThroughq ~ +L isStyledTextq ~ +L isUnderlineq ~ +L 
leftBorderq ~ L leftBorderColorq ~ (L leftPaddingq ~ )L lineBoxt 'Lnet/sf/jasperreports/engine/JRLineBox;L lineSpacingq ~ L lineSpacingValuet 2Lnet/sf/jasperreports/engine/type/LineSpacingEnum;L markupq ~ L paddingq ~ )L pdfEncodingq ~ L pdfFontNameq ~ L 
reportFontq ~ L rightBorderq ~ L rightBorderColorq ~ (L rightPaddingq ~ )L rotationq ~ L 
rotationValuet /Lnet/sf/jasperreports/engine/type/RotationEnum;L 	topBorderq ~ L topBorderColorq ~ (L 
topPaddingq ~ )L verticalAlignmentq ~ L verticalAlignmentValuet 4Lnet/sf/jasperreports/engine/type/VerticalAlignEnum;xr .net.sf.jasperreports.engine.base.JRBaseElement      'Ø I PSEUDO_SERIAL_VERSION_UIDI heightZ isPrintInFirstWholeBandZ isPrintRepeatedValuesZ isPrintWhenDetailOverflowsZ isRemoveLineWhenBlankB positionTypeB stretchTypeI widthI xI yL 	backcolorq ~ (L defaultStyleProvidert 4Lnet/sf/jasperreports/engine/JRDefaultStyleProvider;L elementGroupq ~ "L 	forecolorq ~ (L keyq ~ L modeq ~ L 	modeValuet +Lnet/sf/jasperreports/engine/type/ModeEnum;L parentStyleq ~ L parentStyleNameReferenceq ~ L positionTypeValuet 3Lnet/sf/jasperreports/engine/type/PositionTypeEnum;L printWhenExpressionq ~ L printWhenGroupChangest %Lnet/sf/jasperreports/engine/JRGroup;L 
propertiesMapt -Lnet/sf/jasperreports/engine/JRPropertiesMap;[ propertyExpressionst 3[Lnet/sf/jasperreports/engine/JRPropertyExpression;L stretchTypeValuet 2Lnet/sf/jasperreports/engine/type/StretchTypeEnum;xp  wñ  `        Â        pq ~ q ~ #pppppp~r 1net.sf.jasperreports.engine.type.PositionTypeEnum          xq ~ t FIX_RELATIVE_TO_TOPpppp~r 0net.sf.jasperreports.engine.type.StretchTypeEnum          xq ~ t 
NO_STRETCH  wñppppppppppppppppppsr .net.sf.jasperreports.engine.base.JRBaseLineBox      'Ø L 
bottomPaddingq ~ )L 	bottomPent +Lnet/sf/jasperreports/engine/base/JRBoxPen;L boxContainert ,Lnet/sf/jasperreports/engine/JRBoxContainer;L leftPaddingq ~ )L leftPenq ~ @L paddingq ~ )L penq ~ @L rightPaddingq ~ )L rightPenq ~ @L 
topPaddingq ~ )L topPenq ~ @xppsr 3net.sf.jasperreports.engine.base.JRBaseBoxBottomPen      'Ø  xr -net.sf.jasperreports.engine.base.JRBaseBoxPen      'Ø L lineBoxq ~ ,xr *net.sf.jasperreports.engine.base.JRBasePen      'Ø I PSEUDO_SERIAL_VERSION_UIDL 	lineColorq ~ (L 	lineStyleq ~ L lineStyleValuet 0Lnet/sf/jasperreports/engine/type/LineStyleEnum;L 	lineWidtht Ljava/lang/Float;L penContainert ,Lnet/sf/jasperreports/engine/JRPenContainer;xp  wñppppq ~ Bq ~ Bq ~ 8psr 1net.sf.jasperreports.engine.base.JRBaseBoxLeftPen      'Ø  xq ~ D  wñppppq ~ Bq ~ Bpsq ~ D  wñppppq ~ Bq ~ Bpsr 2net.sf.jasperreports.engine.base.JRBaseBoxRightPen      'Ø  xq ~ D  wñppppq ~ Bq ~ Bpsr 0net.sf.jasperreports.engine.base.JRBaseBoxTopPen      'Ø  xq ~ D  wñppppq ~ Bq ~ Bppppt Cp1252ppppppppppppt¦Este Ã© um Texte para ImpressÃ£o.

Impressora TÃ©rmica.
ImpressÃ£o em PDF
Verificar o comportamento de Textos neste tipo de impressÃ£o, bem como o espaÃ§o a ser usado, e outras coisas, como qualidade.

Lorem ipsum dolor sit amet, consectetur adipisicing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum

Teste realizado em 21/08/2014xp  wñ  `ppppppt groovypsr .net.sf.jasperreports.engine.base.JRBaseDataset      'Ø I PSEUDO_SERIAL_VERSION_UIDZ isMainB whenResourceMissingType[ fieldst &[Lnet/sf/jasperreports/engine/JRField;L filterExpressionq ~ [ groupst &[Lnet/sf/jasperreports/engine/JRGroup;L nameq ~ [ 
parameterst *[Lnet/sf/jasperreports/engine/JRParameter;L 
propertiesMapq ~ 5L queryt %Lnet/sf/jasperreports/engine/JRQuery;L resourceBundleq ~ L scriptletClassq ~ [ 
scriptletst *[Lnet/sf/jasperreports/engine/JRScriptlet;[ 
sortFieldst *[Lnet/sf/jasperreports/engine/JRSortField;[ 	variablest )[Lnet/sf/jasperreports/engine/JRVariable;L whenResourceMissingTypeValuet >Lnet/sf/jasperreports/engine/type/WhenResourceMissingTypeEnum;xp  wñ pppt 
ReciboTermicour *[Lnet.sf.jasperreports.engine.JRParameter;" *Ã`!  xp   sr 0net.sf.jasperreports.engine.base.JRBaseParameter      'Ø 	Z isForPromptingZ isSystemDefinedL defaultValueExpressionq ~ L descriptionq ~ L nameq ~ L nestedTypeNameq ~ L 
propertiesMapq ~ 5L valueClassNameq ~ L valueClassRealNameq ~ xpppt REPORT_PARAMETERS_MAPpsr +net.sf.jasperreports.engine.JRPropertiesMap      'Ø L baseq ~ 5L propertiesListq ~ !L 
propertiesMapt Ljava/util/Map;xppppt 
java.util.Mappsq ~ appt 
JASPER_REPORTpsq ~ dpppt (net.sf.jasperreports.engine.JasperReportpsq ~ appt REPORT_CONNECTIONpsq ~ dpppt java.sql.Connectionpsq ~ appt REPORT_MAX_COUNTpsq ~ dpppt java.lang.Integerpsq ~ appt REPORT_DATA_SOURCEpsq ~ dpppt (net.sf.jasperreports.engine.JRDataSourcepsq ~ appt REPORT_SCRIPTLETpsq ~ dpppt /net.sf.jasperreports.engine.JRAbstractScriptletpsq ~ appt 
REPORT_LOCALEpsq ~ dpppt java.util.Localepsq ~ appt REPORT_RESOURCE_BUNDLEpsq ~ dpppt java.util.ResourceBundlepsq ~ appt REPORT_TIME_ZONEpsq ~ dpppt java.util.TimeZonepsq ~ appt REPORT_FORMAT_FACTORYpsq ~ dpppt .net.sf.jasperreports.engine.util.FormatFactorypsq ~ appt REPORT_CLASS_LOADERpsq ~ dpppt java.lang.ClassLoaderpsq ~ appt REPORT_URL_HANDLER_FACTORYpsq ~ dpppt  java.net.URLStreamHandlerFactorypsq ~ appt REPORT_FILE_RESOLVERpsq ~ dpppt -net.sf.jasperreports.engine.util.FileResolverpsq ~ appt REPORT_TEMPLATESpsq ~ dpppt java.util.Collectionpsq ~ appt SORT_FIELDSpsq ~ dpppt java.util.Listpsq ~ appt REPORT_VIRTUALIZERpsq ~ dpppt )net.sf.jasperreports.engine.JRVirtualizerpsq ~ appt IS_IGNORE_PAGINATIONpsq ~ dpppt java.lang.Booleanpsq ~ dpsq ~ $   w   
t ireport.zoomt 	ireport.xt 	ireport.yxsr java.util.HashMapÚÁÃ`Ñ F 
loadFactorI 	thresholdxp?@     w      q ~ ªt 1.0q ~ «t 0q ~ ¬t 0xpppppur )[Lnet.sf.jasperreports.engine.JRVariable;bæ|,·D  xp   sr /net.sf.jasperreports.engine.base.JRBaseVariable      'Ø I PSEUDO_SERIAL_VERSION_UIDB calculationB 
incrementTypeZ isSystemDefinedB 	resetTypeL calculationValuet 2Lnet/sf/jasperreports/engine/type/CalculationEnum;L 
expressionq ~ L incrementGroupq ~ 4L incrementTypeValuet 4Lnet/sf/jasperreports/engine/type/IncrementTypeEnum;L incrementerFactoryClassNameq ~ L incrementerFactoryClassRealNameq ~ L initialValueExpressionq ~ L nameq ~ L 
resetGroupq ~ 4L resetTypeValuet 0Lnet/sf/jasperreports/engine/type/ResetTypeEnum;L valueClassNameq ~ L valueClassRealNameq ~ xp  wî   ~r 0net.sf.jasperreports.engine.type.CalculationEnum          xq ~ t SYSTEMpp~r 2net.sf.jasperreports.engine.type.IncrementTypeEnum          xq ~ t NONEppsr 1net.sf.jasperreports.engine.base.JRBaseExpression      'Ø I id[ chunkst 0[Lnet/sf/jasperreports/engine/JRExpressionChunk;L valueClassNameq ~ L valueClassRealNameq ~ xp    ur 0[Lnet.sf.jasperreports.engine.JRExpressionChunk;mYÏÞiK£U  xp   sr 6net.sf.jasperreports.engine.base.JRBaseExpressionChunk      'Ø B typeL textq ~ xpt new java.lang.Integer(1)q ~ spt PAGE_NUMBERp~r .net.sf.jasperreports.engine.type.ResetTypeEnum          xq ~ t REPORTq ~ spsq ~ ´  wî   q ~ ºppq ~ ½ppsq ~ ¿   uq ~ Â   sq ~ Ät new java.lang.Integer(1)q ~ spt 
COLUMN_NUMBERp~q ~ Èt PAGEq ~ spsq ~ ´  wî   ~q ~ ¹t COUNTsq ~ ¿   uq ~ Â   sq ~ Ät new java.lang.Integer(1)q ~ sppq ~ ½ppsq ~ ¿   uq ~ Â   sq ~ Ät new java.lang.Integer(0)q ~ spt REPORT_COUNTpq ~ Éq ~ spsq ~ ´  wî   q ~ Ôsq ~ ¿   uq ~ Â   sq ~ Ät new java.lang.Integer(1)q ~ sppq ~ ½ppsq ~ ¿   uq ~ Â   sq ~ Ät new java.lang.Integer(0)q ~ spt 
PAGE_COUNTpq ~ Ñq ~ spsq ~ ´  wî   q ~ Ôsq ~ ¿   uq ~ Â   sq ~ Ät new java.lang.Integer(1)q ~ sppq ~ ½ppsq ~ ¿   uq ~ Â   sq ~ Ät new java.lang.Integer(0)q ~ spt COLUMN_COUNTp~q ~ Èt COLUMNq ~ sp~r <net.sf.jasperreports.engine.type.WhenResourceMissingTypeEnum          xq ~ t NULLq ~ ^p~r 0net.sf.jasperreports.engine.type.OrientationEnum          xq ~ t PORTRAITpp~r /net.sf.jasperreports.engine.type.PrintOrderEnum          xq ~ t VERTICALpppp~r 3net.sf.jasperreports.engine.type.WhenNoDataTypeEnum          xq ~ t NO_PAGESsr 6net.sf.jasperreports.engine.design.JRReportCompileData      'Ø L crosstabCompileDataq ~ eL datasetCompileDataq ~ eL mainDatasetCompileDataq ~ xpsq ~ ­?@     w       xsq ~ ­?@     w       xur [B¬óøTà  xp  5Êþº¾   / "ReciboTermico_1408619402641_696257  ,net/sf/jasperreports/engine/fill/JREvaluator  groovy/lang/GroovyObject  -calculator_ReciboTermico_1408619402641_696257 parameter_REPORT_LOCALE 2Lnet/sf/jasperreports/engine/fill/JRFillParameter; parameter_JASPER_REPORT parameter_REPORT_VIRTUALIZER parameter_REPORT_TIME_ZONE parameter_SORT_FIELDS parameter_REPORT_FILE_RESOLVER parameter_REPORT_SCRIPTLET parameter_REPORT_PARAMETERS_MAP parameter_REPORT_CONNECTION parameter_REPORT_CLASS_LOADER parameter_REPORT_DATA_SOURCE $parameter_REPORT_URL_HANDLER_FACTORY parameter_IS_IGNORE_PAGINATION parameter_REPORT_FORMAT_FACTORY parameter_REPORT_MAX_COUNT parameter_REPORT_TEMPLATES  parameter_REPORT_RESOURCE_BUNDLE variable_PAGE_NUMBER 1Lnet/sf/jasperreports/engine/fill/JRFillVariable; variable_COLUMN_NUMBER variable_REPORT_COUNT variable_PAGE_COUNT variable_COLUMN_COUNT 	metaClass Lgroovy/lang/MetaClass; __timeStamp Ljava/lang/Long; )__timeStamp__239_neverHappen1408619402688 <init> ()V % &
  ' class$0 Ljava/lang/Class; ) *	  +  class$ %(Ljava/lang/String;)Ljava/lang/Class; . /
  0 class$groovy$lang$MetaClass 2 *	  3 groovy.lang.MetaClass 5 6class$net$sf$jasperreports$engine$fill$JRFillParameter 7 *	  8 0net.sf.jasperreports.engine.fill.JRFillParameter : 1org/codehaus/groovy/runtime/ScriptBytecodeAdapter < 
castToType 7(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object; > ?
 = @ 0net/sf/jasperreports/engine/fill/JRFillParameter B  		  D 
 		  F  		  H  		  J 
 		  L  		  N  		  P  		  R  		  T  		  V  		  X  		  Z  		  \  		  ^  		  `  		  b  		  d 5class$net$sf$jasperreports$engine$fill$JRFillVariable f *	  g /net.sf.jasperreports.engine.fill.JRFillVariable i /net/sf/jasperreports/engine/fill/JRFillVariable k  	  m  	  o  	  q  	  s  	  u 7class$org$codehaus$groovy$runtime$ScriptBytecodeAdapter w *	  x 1org.codehaus.groovy.runtime.ScriptBytecodeAdapter z 
initMetaClass | java/lang/Object ~ invokeStaticMethodN [(Ljava/lang/Class;Ljava/lang/Class;Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/Object;  
 =  groovy/lang/MetaClass    !	   this $LReciboTermico_1408619402641_696257; customizedInit 0(Ljava/util/Map;Ljava/util/Map;Ljava/util/Map;)V class$groovy$lang$GroovyObject  *	   groovy.lang.GroovyObject  
initParams  invokeMethodOnCurrentN d(Ljava/lang/Class;Lgroovy/lang/GroovyObject;Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/Object;  
 =  
initFields  initVars  pm Ljava/util/Map; fm vm (Ljava/util/Map;)V get   
REPORT_LOCALE ¢ 
invokeMethodN \(Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/Object; ¤ ¥
 = ¦ 
JASPER_REPORT ¨ REPORT_VIRTUALIZER ª REPORT_TIME_ZONE ¬ SORT_FIELDS ® REPORT_FILE_RESOLVER ° REPORT_SCRIPTLET ² REPORT_PARAMETERS_MAP ´ REPORT_CONNECTION ¶ REPORT_CLASS_LOADER ¸ REPORT_DATA_SOURCE º REPORT_URL_HANDLER_FACTORY ¼ IS_IGNORE_PAGINATION ¾ REPORT_FORMAT_FACTORY À REPORT_MAX_COUNT Â REPORT_TEMPLATES Ä REPORT_RESOURCE_BUNDLE Æ PAGE_NUMBER È 
COLUMN_NUMBER Ê REPORT_COUNT Ì 
PAGE_COUNT Î COLUMN_COUNT Ð evaluate (I)Ljava/lang/Object; Borg/codehaus/groovy/runtime/typehandling/DefaultTypeTransformation Ô box Ö Ó
 Õ × java/lang/Integer Ù     (I)V % Ü
 Ú Ý compareEqual '(Ljava/lang/Object;Ljava/lang/Object;)Z ß à
 = á class$java$lang$Integer ã *	  ä java.lang.Integer æ    
invokeNewN H(Ljava/lang/Class;Ljava/lang/Class;Ljava/lang/Object;)Ljava/lang/Object; é ê
 = ë                   class$java$lang$Object ó *	  ô java.lang.Object ö id I value Ljava/lang/Object; evaluateOld evaluateEstimated getMetaClass ()Lgroovy/lang/MetaClass; invokeMethod 8(Ljava/lang/String;Ljava/lang/Object;)Ljava/lang/Object;  method Ljava/lang/String; 	arguments getProperty &(Ljava/lang/String;)Ljava/lang/Object; property setProperty '(Ljava/lang/String;Ljava/lang/Object;)V
 <clinit> java/lang/Long  GøBUÀ (J)V %
 " #	          $ #	  setMetaClass (Lgroovy/lang/MetaClass;)V super$2$evaluate >(Lnet/sf/jasperreports/engine/JRExpression;)Ljava/lang/Object; Ò
  super$1$toString ()Ljava/lang/String; toString#"
 $ super$1$notify notify' &
 ( super$1$notifyAll 	notifyAll+ &
 , super$2$evaluateEstimated ý
 / super$2$init n(Ljava/util/Map;Ljava/util/Map;Ljava/util/Map;Lnet/sf/jasperreports/engine/type/WhenResourceMissingTypeEnum;)V init32
 4 super$2$str &(Ljava/lang/String;)Ljava/lang/String; str87
 9 
super$1$clone ()Ljava/lang/Object; clone=<
 > super$2$evaluateOld ü
 A super$1$wait waitD &
 E (JI)VDG
 H super$2$handleMissingResource ;(Ljava/lang/String;Ljava/lang/Exception;)Ljava/lang/String; handleMissingResourceLK
 M super$1$getClass ()Ljava/lang/Class; getClassQP
 R super$2$msg \(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/String; msgVU
 W J(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/String;VY
 Z super$1$finalize finalize] &
 ^ 9(Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/String;V`
 aD
 c 8(Ljava/lang/String;Ljava/lang/Object;)Ljava/lang/String;Ve
 f super$1$equals (Ljava/lang/Object;)Z equalsji
 k super$1$hashCode ()I hashCodeon
 p java/lang/Classr forNamet /
su java/lang/NoClassDefFoundErrorw  java/lang/ClassNotFoundExceptiony 
getMessage{"
z| (Ljava/lang/String;)V %~
x 	Synthetic Code LocalVariableTable LineNumberTable 
SourceFile      !   	    
 	     	     	    
 	     	     	     	     	     	     	     	     	     	     	     	     	                               !   	 " #   	 $ #   f *      2 *      ) *      ó *       *      w *      7 *      ã *      $  % &   ß    Á*· (² ,Ç -¸ 1Y³ ,§ ² ,YLW² 4Ç 6¸ 1Y³ 4§ ² 4YMW² 9Ç ;¸ 1Y³ 9§ ² 9¸ AÀ CY² 9Ç ;¸ 1Y³ 9§ ² 9¸ AÀ C*_µ EW² 9Ç ;¸ 1Y³ 9§ ² 9¸ AÀ CY² 9Ç ;¸ 1Y³ 9§ ² 9¸ AÀ C*_µ GW² 9Ç ;¸ 1Y³ 9§ ² 9¸ AÀ CY² 9Ç ;¸ 1Y³ 9§ ² 9¸ AÀ C*_µ IW² 9Ç ;¸ 1Y³ 9§ ² 9¸ AÀ CY² 9Ç ;¸ 1Y³ 9§ ² 9¸ AÀ C*_µ KW² 9Ç ;¸ 1Y³ 9§ ² 9¸ AÀ CY² 9Ç ;¸ 1Y³ 9§ ² 9¸ AÀ C*_µ MW² 9Ç ;¸ 1Y³ 9§ ² 9¸ AÀ CY² 9Ç ;¸ 1Y³ 9§ ² 9¸ AÀ C*_µ OW² 9Ç ;¸ 1Y³ 9§ ² 9¸ AÀ CY² 9Ç ;¸ 1Y³ 9§ ² 9¸ AÀ C*_µ QW² 9Ç ;¸ 1Y³ 9§ ² 9¸ AÀ CY² 9Ç ;¸ 1Y³ 9§ ² 9¸ AÀ C*_µ SW² 9Ç ;¸ 1Y³ 9§ ² 9¸ AÀ CY² 9Ç ;¸ 1Y³ 9§ ² 9¸ AÀ C*_µ UW² 9Ç ;¸ 1Y³ 9§ ² 9¸ AÀ CY² 9Ç ;¸ 1Y³ 9§ ² 9¸ AÀ C*_µ WW² 9Ç ;¸ 1Y³ 9§ ² 9¸ AÀ CY² 9Ç ;¸ 1Y³ 9§ ² 9¸ AÀ C*_µ YW² 9Ç ;¸ 1Y³ 9§ ² 9¸ AÀ CY² 9Ç ;¸ 1Y³ 9§ ² 9¸ AÀ C*_µ [W² 9Ç ;¸ 1Y³ 9§ ² 9¸ AÀ CY² 9Ç ;¸ 1Y³ 9§ ² 9¸ AÀ C*_µ ]W² 9Ç ;¸ 1Y³ 9§ ² 9¸ AÀ CY² 9Ç ;¸ 1Y³ 9§ ² 9¸ AÀ C*_µ _W² 9Ç ;¸ 1Y³ 9§ ² 9¸ AÀ CY² 9Ç ;¸ 1Y³ 9§ ² 9¸ AÀ C*_µ aW² 9Ç ;¸ 1Y³ 9§ ² 9¸ AÀ CY² 9Ç ;¸ 1Y³ 9§ ² 9¸ AÀ C*_µ cW² 9Ç ;¸ 1Y³ 9§ ² 9¸ AÀ CY² 9Ç ;¸ 1Y³ 9§ ² 9¸ AÀ C*_µ eW² hÇ j¸ 1Y³ h§ ² h¸ AÀ lY² hÇ j¸ 1Y³ h§ ² h¸ AÀ l*_µ nW² hÇ j¸ 1Y³ h§ ² h¸ AÀ lY² hÇ j¸ 1Y³ h§ ² h¸ AÀ l*_µ pW² hÇ j¸ 1Y³ h§ ² h¸ AÀ lY² hÇ j¸ 1Y³ h§ ² h¸ AÀ l*_µ rW² hÇ j¸ 1Y³ h§ ² h¸ AÀ lY² hÇ j¸ 1Y³ h§ ² h¸ AÀ l*_µ tW² hÇ j¸ 1Y³ h§ ² h¸ AÀ lY² hÇ j¸ 1Y³ h§ ² h¸ AÀ l*_µ vW+² yÇ {¸ 1Y³ y§ ² y}½ Y*S¸ ,¸ AÀ Y,¸ AÀ *_µ W±        ¼               ¸² ,Ç -¸ 1Y³ ,§ ² ,Y:W² 4Ç 6¸ 1Y³ 4§ ² 4Y:W*² Ç ¸ 1Y³ § ² ¸ AÀ ½ Y+S¸ W*² Ç ¸ 1Y³ § ² ¸ AÀ ½ Y,S¸ W*² Ç ¸ 1Y³ § ² ¸ AÀ ½ Y-S¸ W±±      *    ·       ·      ·      ·        2 : ^ ;  <      +    ·² ,Ç -¸ 1Y³ ,§ ² ,YMW² 4Ç 6¸ 1Y³ 4§ ² 4YNW,+¡½ Y£S¸ §² 9Ç ;¸ 1Y³ 9§ ² 9¸ AÀ CYÀ C*_µ EW,+¡½ Y©S¸ §² 9Ç ;¸ 1Y³ 9§ ² 9¸ AÀ CYÀ C*_µ GW,+¡½ Y«S¸ §² 9Ç ;¸ 1Y³ 9§ ² 9¸ AÀ CYÀ C*_µ IW,+¡½ Y­S¸ §² 9Ç ;¸ 1Y³ 9§ ² 9¸ AÀ CYÀ C*_µ KW,+¡½ Y¯S¸ §² 9Ç ;¸ 1Y³ 9§ ² 9¸ AÀ CYÀ C*_µ MW,+¡½ Y±S¸ §² 9Ç ;¸ 1Y³ 9§ ² 9¸ AÀ CYÀ C*_µ OW,+¡½ Y³S¸ §² 9Ç ;¸ 1Y³ 9§ ² 9¸ AÀ CYÀ C*_µ QW,+¡½ YµS¸ §² 9Ç ;¸ 1Y³ 9§ ² 9¸ AÀ CYÀ C*_µ SW,+¡½ Y·S¸ §² 9Ç ;¸ 1Y³ 9§ ² 9¸ AÀ CYÀ C*_µ UW,+¡½ Y¹S¸ §² 9Ç ;¸ 1Y³ 9§ ² 9¸ AÀ CYÀ C*_µ WW,+¡½ Y»S¸ §² 9Ç ;¸ 1Y³ 9§ ² 9¸ AÀ CYÀ C*_µ YW,+¡½ Y½S¸ §² 9Ç ;¸ 1Y³ 9§ ² 9¸ AÀ CYÀ C*_µ [W,+¡½ Y¿S¸ §² 9Ç ;¸ 1Y³ 9§ ² 9¸ AÀ CYÀ C*_µ ]W,+¡½ YÁS¸ §² 9Ç ;¸ 1Y³ 9§ ² 9¸ AÀ CYÀ C*_µ _W,+¡½ YÃS¸ §² 9Ç ;¸ 1Y³ 9§ ² 9¸ AÀ CYÀ C*_µ aW,+¡½ YÅS¸ §² 9Ç ;¸ 1Y³ 9§ ² 9¸ AÀ CYÀ C*_µ cW,+¡½ YÇS¸ §² 9Ç ;¸ 1Y³ 9§ ² 9¸ AÀ CYÀ C*_µ eW±±         ¶      ¶      F  0 E e F  G Ï H I9 Jn K£ LØ M
 NB Ow P¬ Qá R SK T U       Z     2² ,Ç -¸ 1Y³ ,§ ² ,YMW² 4Ç 6¸ 1Y³ 4§ ² 4YNW±±          1       1             ;² ,Ç -¸ 1Y³ ,§ ² ,YMW² 4Ç 6¸ 1Y³ 4§ ² 4YNW,+¡½ YÉS¸ §² hÇ j¸ 1Y³ h§ ² h¸ AÀ lYÀ l*_µ nW,+¡½ YËS¸ §² hÇ j¸ 1Y³ h§ ² h¸ AÀ lYÀ l*_µ pW,+¡½ YÍS¸ §² hÇ j¸ 1Y³ h§ ² h¸ AÀ lYÀ l*_µ rW,+¡½ YÏS¸ §² hÇ j¸ 1Y³ h§ ² h¸ AÀ lYÀ l*_µ tW,+¡½ YÑS¸ §² hÇ j¸ 1Y³ h§ ² h¸ AÀ lYÀ l*_µ vW±±         :      :        0 f e g  h Ï i j  Ò Ó       i² ,Ç -¸ 1Y³ ,§ ² ,YMW² 4Ç 6¸ 1Y³ 4§ ² 4YNW:¸ Ø» ÚYÛ· Þ¸ â 3,² åÇ ç¸ 1Y³ å§ ² å½ Y» ÚYè· ÞS¸ ìY:W§Ø¸ Ø» ÚYè· Þ¸ â 3,² åÇ ç¸ 1Y³ å§ ² å½ Y» ÚYè· ÞS¸ ìY:W§¸ Ø» ÚYí· Þ¸ â 3,² åÇ ç¸ 1Y³ å§ ² å½ Y» ÚYè· ÞS¸ ìY:W§R¸ Ø» ÚYî· Þ¸ â 3,² åÇ ç¸ 1Y³ å§ ² å½ Y» ÚYÛ· ÞS¸ ìY:W§¸ Ø» ÚYï· Þ¸ â 3,² åÇ ç¸ 1Y³ å§ ² å½ Y» ÚYè· ÞS¸ ìY:W§ Ì¸ Ø» ÚYð· Þ¸ â 3,² åÇ ç¸ 1Y³ å§ ² å½ Y» ÚYÛ· ÞS¸ ìY:W§ ¸ Ø» ÚYñ· Þ¸ â 3,² åÇ ç¸ 1Y³ å§ ² å½ Y» ÚYè· ÞS¸ ìY:W§ F¸ Ø» ÚYò· Þ¸ â 3,² åÇ ç¸ 1Y³ å§ ² å½ Y» ÚYÛ· ÞS¸ ìY:W§ ² õÇ ÷¸ 1Y³ õ§ ² õ¸ AÀ °          i      i ø ù  36 ú û    j  0 s 3 u F v F w v y  z  { ¹ } Ì ~ Ì  ü   ? R R    Å Ø Ø    K   ü Ó       i² ,Ç -¸ 1Y³ ,§ ² ,YMW² 4Ç 6¸ 1Y³ 4§ ² 4YNW:¸ Ø» ÚYÛ· Þ¸ â 3,² åÇ ç¸ 1Y³ å§ ² å½ Y» ÚYè· ÞS¸ ìY:W§Ø¸ Ø» ÚYè· Þ¸ â 3,² åÇ ç¸ 1Y³ å§ ² å½ Y» ÚYè· ÞS¸ ìY:W§¸ Ø» ÚYí· Þ¸ â 3,² åÇ ç¸ 1Y³ å§ ² å½ Y» ÚYè· ÞS¸ ìY:W§R¸ Ø» ÚYî· Þ¸ â 3,² åÇ ç¸ 1Y³ å§ ² å½ Y» ÚYÛ· ÞS¸ ìY:W§¸ Ø» ÚYï· Þ¸ â 3,² åÇ ç¸ 1Y³ å§ ² å½ Y» ÚYè· ÞS¸ ìY:W§ Ì¸ Ø» ÚYð· Þ¸ â 3,² åÇ ç¸ 1Y³ å§ ² å½ Y» ÚYÛ· ÞS¸ ìY:W§ ¸ Ø» ÚYñ· Þ¸ â 3,² åÇ ç¸ 1Y³ å§ ² å½ Y» ÚYè· ÞS¸ ìY:W§ F¸ Ø» ÚYò· Þ¸ â 3,² åÇ ç¸ 1Y³ å§ ² å½ Y» ÚYÛ· ÞS¸ ìY:W§ ² õÇ ÷¸ 1Y³ õ§ ² õ¸ AÀ °          i      i ø ù  36 ú û    j  0  3 ¡ F ¢ F £ v ¥  ¦  § ¹ © Ì ª Ì « ü ­ ® ¯? ±R ²R ³ µ ¶ ·Å ¹Ø ºØ » ½ ¾ ¿K Â  ý Ó       i² ,Ç -¸ 1Y³ ,§ ² ,YMW² 4Ç 6¸ 1Y³ 4§ ² 4YNW:¸ Ø» ÚYÛ· Þ¸ â 3,² åÇ ç¸ 1Y³ å§ ² å½ Y» ÚYè· ÞS¸ ìY:W§Ø¸ Ø» ÚYè· Þ¸ â 3,² åÇ ç¸ 1Y³ å§ ² å½ Y» ÚYè· ÞS¸ ìY:W§¸ Ø» ÚYí· Þ¸ â 3,² åÇ ç¸ 1Y³ å§ ² å½ Y» ÚYè· ÞS¸ ìY:W§R¸ Ø» ÚYî· Þ¸ â 3,² åÇ ç¸ 1Y³ å§ ² å½ Y» ÚYÛ· ÞS¸ ìY:W§¸ Ø» ÚYï· Þ¸ â 3,² åÇ ç¸ 1Y³ å§ ² å½ Y» ÚYè· ÞS¸ ìY:W§ Ì¸ Ø» ÚYð· Þ¸ â 3,² åÇ ç¸ 1Y³ å§ ² å½ Y» ÚYÛ· ÞS¸ ìY:W§ ¸ Ø» ÚYñ· Þ¸ â 3,² åÇ ç¸ 1Y³ å§ ² å½ Y» ÚYè· ÞS¸ ìY:W§ F¸ Ø» ÚYò· Þ¸ â 3,² åÇ ç¸ 1Y³ å§ ² å½ Y» ÚYÛ· ÞS¸ ìY:W§ ² õÇ ÷¸ 1Y³ õ§ ² õ¸ AÀ °          i      i ø ù  36 ú û    j  0 Ë 3 Í F Î F Ï v Ñ  Ò  Ó ¹ Õ Ì Ö Ì × ü Ù Ú Û? ÝR ÞR ß á â ãÅ åØ æØ ç é ê ëK î  þ ÿ          ² ,Ç -¸ 1Y³ ,§ ² ,YLW² 4Ç 6¸ 1Y³ 4§ ² 4YMW*´ ¸ â >+² yÇ {¸ 1Y³ y§ ² y}½ Y*S¸ ,¸ AÀ Y,¸ AÀ *_µ W§ *´ ,¸ AÀ °                    Ç     ² ,Ç -¸ 1Y³ ,§ ² ,YNW² 4Ç 6¸ 1Y³ 4§ ² 4Y:W*´ ¸ â @-² yÇ {¸ 1Y³ y§ ² y}½ Y*S¸ ¸ AÀ Y¸ AÀ *_µ W§ -*´ ½ Y*SY+SY,S¸ §°                       û      ¶     ² ,Ç -¸ 1Y³ ,§ ² ,YMW² 4Ç 6¸ 1Y³ 4§ ² 4YNW*´ ¸ â >,² yÇ {¸ 1Y³ y§ ² y}½ Y*S¸ -¸ AÀ Y-¸ AÀ *_µ W§ ,*´ ½ Y*SY+S¸ §°                 	  
    É     ² ,Ç -¸ 1Y³ ,§ ² ,YNW² 4Ç 6¸ 1Y³ 4§ ² 4Y:W*´ ¸ â @-² yÇ {¸ 1Y³ y§ ² y}½ Y*S¸ ¸ AÀ Y¸ AÀ *_µ W§ -*´ ½ Y*SY+SY,S¸ §W±±                  	     ú û  
 &    b     V² ,Ç -¸ 1Y³ ,§ ² ,YKW² 4Ç 6¸ 1Y³ 4§ ² 4YLW»Y·YÀ³W»Y·YÀ³W±±         j     B² ,Ç -¸ 1Y³ ,§ ² ,YMW² 4Ç 6¸ 1Y³ 4§ ² 4YNW+Y-¸ AÀ *_µ W±±±          A       A ú !            *+· °      !"         *·%°      & &         *·)±      * &         *·-±      .         *+·0°      12         
*+,-·5±      67         *+·:°      ;<         *·?°      @         *+·B°      C &         *·F±      CG         *·I±      JK         *+,·N°      OP         *·S°      TU         
*+,-·X°      TY         *+,-·[°      \ &         *·_±      T`         *+,·b°      C         *·d±      Te         *+,·g°      hi         *+·l¬      mn         *·q¬     . /    &     *¸v°L»xY+¶}·¿     z           t _1408619402641_696257t /net.sf.jasperreports.compilers.JRGroovyCompiler
<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="MovProduto" pageWidth="195" pageHeight="535" orientation="Landscape" whenNoDataType="AllSectionsNoDetail" columnWidth="195" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0">
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.encoding" value="UTF-8"/>
	<property name="ireport.zoom" value="3.897434200000008"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="java.util.*"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<parameter name="mostrarModalidade" class="java.lang.Boolean"/>
	<parameter name="detalharPeriodoProduto" class="java.lang.Boolean">
		<defaultValueExpression><![CDATA[true]]></defaultValueExpression>
	</parameter>
	<field name="descricao" class="java.lang.String"/>
	<field name="valorDesconto" class="java.lang.Double"/>
	<field name="precoUnitario" class="java.lang.Double"/>
	<field name="quantidade" class="java.lang.Integer"/>
	<field name="valorPagoMovProdutoParcela" class="java.lang.Double"/>
	<field name="contrato.codigo" class="java.lang.Integer"/>
	<field name="pessoa.primeiroNomeConcatenado" class="java.lang.String"/>
	<field name="produto.tipoProduto" class="java.lang.String"/>
	<field name="contrato.vigenciaDe_Apresentar" class="java.lang.String"/>
	<field name="contrato.vigenciaAteAjustada_Apresentar" class="java.lang.String"/>
	<field name="contrato.nomeModalidades" class="java.lang.String"/>
	<field name="contrato.dataAlteracaoManual_Apresentar" class="java.lang.String"/>
	<field name="contrato.responsavelDataBase.nome" class="java.lang.String"/>
	<field name="pacoteVO.titulo" class="java.lang.String"/>
	<field name="dataInicioVigencia_Apresentar" class="java.lang.String"/>
	<field name="dataFinalVigencia_Apresentar" class="java.lang.String"/>
	<detail>
		<band height="11">
			<textField isBlankWhenNull="false">
				<reportElement key="textField-1" stretchType="RelativeToTallestObject" x="38" y="1" width="107" height="10" isRemoveLineWhenBlank="true" isPrintWhenDetailOverflows="true"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="8" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[" "+$F{pessoa.primeiroNomeConcatenado}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-6" positionType="Float" stretchType="RelativeToTallestObject" x="0" y="1" width="38" height="10" isPrintWhenDetailOverflows="true"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="8" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.Integer"><![CDATA[$F{contrato.codigo}]]></textFieldExpression>
			</textField>
		</band>
		<band height="10" splitType="Stretch">
			<textField isStretchWithOverflow="true">
				<reportElement x="0" y="0" width="194" height="10" isRemoveLineWhenBlank="true" isPrintWhenDetailOverflows="true"/>
				<textElement>
					<font fontName="Microsoft Sans Serif" size="7"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{descricao}]]></textFieldExpression>
			</textField>
		</band>
		<band height="10">
			<textField isBlankWhenNull="true">
				<reportElement key="textField-9" x="0" y="0" width="190" height="10" isRemoveLineWhenBlank="true" isPrintWhenDetailOverflows="true" backcolor="#FFFFFF"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="8" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{pacoteVO.titulo}]]></textFieldExpression>
			</textField>
		</band>
		<band height="10">
			<textField pattern="#,##0.00" isBlankWhenNull="false">
				<reportElement key="textField-5" stretchType="RelativeToTallestObject" x="154" y="0" width="40" height="10" isRemoveLineWhenBlank="true" isPrintWhenDetailOverflows="true"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="8" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.Double"><![CDATA[$F{valorPagoMovProdutoParcela}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-3" stretchType="RelativeToTallestObject" x="101" y="0" width="17" height="10" isRemoveLineWhenBlank="true" isPrintWhenDetailOverflows="true">
					<printWhenExpression><![CDATA[!$F{produto.tipoProduto}.equals("PM")]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="8" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.Integer"><![CDATA[$F{quantidade}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00" isBlankWhenNull="false">
				<reportElement key="textField-2" stretchType="RelativeToTallestObject" x="70" y="0" width="31" height="10" isRemoveLineWhenBlank="true" isPrintWhenDetailOverflows="true">
					<printWhenExpression><![CDATA[!$F{produto.tipoProduto}.equals("PM")]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="8" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.Double"><![CDATA[$F{precoUnitario}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00" isBlankWhenNull="false">
				<reportElement key="textField-4" stretchType="RelativeToTallestObject" x="117" y="0" width="38" height="10" isRemoveLineWhenBlank="true" isPrintWhenDetailOverflows="true">
					<printWhenExpression><![CDATA[!$F{produto.tipoProduto}.equals("PM")]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="8" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.Double"><![CDATA[$F{valorDesconto}]]></textFieldExpression>
			</textField>
		</band>
		<band height="10">
			<textField>
				<reportElement x="4" y="0" width="60" height="10" isRemoveLineWhenBlank="true">
					<printWhenExpression><![CDATA[!$F{contrato.dataAlteracaoManual_Apresentar}.equals("")]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Right" verticalAlignment="Middle" markup="none">
					<font size="7" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$R{Dt_alteracao}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-12" x="63" y="0" width="47" height="10" isRemoveLineWhenBlank="true"/>
				<box leftPadding="10">
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="7" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{contrato.dataAlteracaoManual_Apresentar}]]></textFieldExpression>
			</textField>
		</band>
		<band height="10">
			<printWhenExpression><![CDATA[(($F{contrato.codigo}.intValue() != 0) &&     ($F{produto.tipoProduto}.equals("PM")))]]></printWhenExpression>
			<textField>
				<reportElement x="0" y="0" width="62" height="10" isRemoveLineWhenBlank="true">
					<printWhenExpression><![CDATA[!$F{contrato.responsavelDataBase.nome}.equals("")]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Right" verticalAlignment="Middle" markup="none">
					<font size="7" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$R{Responsavel}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-13" x="61" y="0" width="106" height="10" isRemoveLineWhenBlank="true"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="7" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{contrato.responsavelDataBase.nome}]]></textFieldExpression>
			</textField>
		</band>
		<band height="11">
			<printWhenExpression><![CDATA[(($F{contrato.codigo}.intValue() != 0) &&     ($F{produto.tipoProduto}.equals("PM")))]]></printWhenExpression>
			<textField>
				<reportElement x="0" y="0" width="30" height="10" isRemoveLineWhenBlank="true">
					<printWhenExpression><![CDATA[$P{mostrarModalidade}.equals(false)]]></printWhenExpression>
				</reportElement>
				<textElement verticalAlignment="Middle">
					<font size="7" isBold="true" isItalic="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA["("+$F{contrato.nomeModalidades}+")"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="29" y="0" width="28" height="10" isRemoveLineWhenBlank="true">
					<printWhenExpression><![CDATA[$P{detalharPeriodoProduto}]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle" markup="none">
					<font size="7" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$R{Inicio}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="109" y="0" width="40" height="10" isRemoveLineWhenBlank="true">
					<printWhenExpression><![CDATA[$P{detalharPeriodoProduto}]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle" markup="none">
					<font size="7" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$R{Termino}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-10" x="58" y="0" width="52" height="10" isRemoveLineWhenBlank="true">
					<printWhenExpression><![CDATA[$P{detalharPeriodoProduto}]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="7" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{contrato.vigenciaDe_Apresentar}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-11" x="148" y="0" width="42" height="10" isRemoveLineWhenBlank="true">
					<printWhenExpression><![CDATA[$P{detalharPeriodoProduto}]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="7" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{contrato.vigenciaAteAjustada_Apresentar}]]></textFieldExpression>
			</textField>
		</band>
		<band height="11">
			<printWhenExpression><![CDATA[$F{produto.tipoProduto}.equals("DI")]]></printWhenExpression>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-10" x="58" y="0" width="52" height="10" isRemoveLineWhenBlank="true">
					<printWhenExpression><![CDATA[$P{detalharPeriodoProduto}]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="7" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{dataInicioVigencia_Apresentar}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="29" y="0" width="28" height="10" isRemoveLineWhenBlank="true">
					<printWhenExpression><![CDATA[$P{detalharPeriodoProduto}]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle" markup="none">
					<font size="7" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$R{Inicio}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="109" y="0" width="40" height="10" isRemoveLineWhenBlank="true">
					<printWhenExpression><![CDATA[$P{detalharPeriodoProduto}]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle" markup="none">
					<font size="7" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$R{Termino}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-11" x="148" y="0" width="42" height="10" isRemoveLineWhenBlank="true">
					<printWhenExpression><![CDATA[$P{detalharPeriodoProduto}]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="7" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{dataFinalVigencia_Apresentar}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
</jasperReport>

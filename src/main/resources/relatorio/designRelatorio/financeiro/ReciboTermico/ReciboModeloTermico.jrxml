<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="ReciboRel" pageWidth="200" pageHeight="500" whenNoDataType="AllSectionsNoDetail" columnWidth="200" leftMargin="0" rightMargin="0" topMargin="5" bottomMargin="2" whenResourceMissingType="Empty">
	<property name="ireport.zoom" value="2.0"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="264"/>
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.encoding" value="ISO-8859-1"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="java.util.*"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<subDataset name="Teste"/>
	<parameter name="tituloRelatorio" class="java.lang.String" isForPrompting="false"/>
	<parameter name="nomeEmpresa" class="java.lang.String" isForPrompting="false"/>
	<parameter name="versaoSoftware" class="java.lang.String" isForPrompting="false"/>
	<parameter name="usuario" class="java.lang.String" isForPrompting="false"/>
	<parameter name="filtros" class="java.lang.String" isForPrompting="false"/>
	<parameter name="SUBREPORT_DIR" class="java.lang.String">
		<defaultValueExpression><![CDATA["E:\\desenvolvimentosv\\Projeto Pacto Solucoes\\ZillyonWeb\\src\\java\\relatorio\\designRelatorio\\financeiro\\"]]></defaultValueExpression>
	</parameter>
	<parameter name="SUBREPORT_DIR1" class="java.lang.String">
		<defaultValueExpression><![CDATA["E:\\desenvolvimentosv\\Projeto Pacto Solucoes\\ZillyonWeb\\src\\java\\relatorio\\designRelatorio\\financeiro\\"]]></defaultValueExpression>
	</parameter>
	<parameter name="dataIni" class="java.lang.String" isForPrompting="false"/>
	<parameter name="dataFim" class="java.lang.String" isForPrompting="false"/>
	<parameter name="qtdAV" class="java.lang.String" isForPrompting="false"/>
	<parameter name="qtdCA" class="java.lang.String" isForPrompting="false"/>
	<parameter name="qtdChequeAV" class="java.lang.String" isForPrompting="false"/>
	<parameter name="qtdChequePR" class="java.lang.String" isForPrompting="false"/>
	<parameter name="qtdOutro" class="java.lang.String" isForPrompting="false"/>
	<parameter name="valorAV" class="java.lang.Double" isForPrompting="false"/>
	<parameter name="valorCA" class="java.lang.Double" isForPrompting="false"/>
	<parameter name="valorChequeAV" class="java.lang.Double" isForPrompting="false"/>
	<parameter name="valorChequePR" class="java.lang.Double" isForPrompting="false"/>
	<parameter name="valorOutro" class="java.lang.Double" isForPrompting="false"/>
	<parameter name="logoPadraoRelatorio" class="java.io.InputStream" isForPrompting="false"/>
	<parameter name="qtdCD" class="java.lang.String"/>
	<parameter name="valorCD" class="java.lang.Double"/>
	<parameter name="SUBREPORT_DIR2" class="java.lang.String">
		<defaultValueExpression><![CDATA["E:\\desenvolvimentosv\\Projeto Pacto Solucoes\\ZillyonWeb\\src\\java\\relatorio\\designRelatorio\\financeiro\\"]]></defaultValueExpression>
	</parameter>
	<parameter name="codRecibo" class="java.lang.String"/>
	<parameter name="empresaVO.cnpj" class="java.lang.String"/>
	<parameter name="empresaVO.endereco" class="java.lang.String"/>
	<parameter name="empresaVO.site" class="java.lang.String"/>
	<parameter name="empresaVO.fone" class="java.lang.String"/>
	<parameter name="mostrarCnpj" class="java.lang.Boolean">
		<defaultValueExpression><![CDATA[true]]></defaultValueExpression>
	</parameter>
	<parameter name="totalContrato" class="java.lang.String"/>
	<parameter name="mostrarModalidade" class="java.lang.Boolean"/>
	<parameter name="detalharPeriodoProduto" class="java.lang.Boolean"/>
	<parameter name="detalharParcelas" class="java.lang.Boolean"/>
	<parameter name="detalharPagamentos" class="java.lang.Boolean"/>
	<parameter name="detalharDescontos" class="java.lang.Boolean"/>
	<parameter name="apresentarAssinaturas" class="java.lang.Boolean" isForPrompting="false"/>
	<parameter name="apresentarObservacao" class="java.lang.Boolean" isForPrompting="false"/>
	<parameter name="observacaoRecibo" class="java.lang.String"/>
	<parameter name="moeda" class="java.lang.String">
		<defaultValueExpression><![CDATA["R$"]]></defaultValueExpression>
	</parameter>
	<parameter name="identificador" class="java.lang.String">
		<defaultValueExpression><![CDATA[""]]></defaultValueExpression>
	</parameter>
	<field name="listaMovProduto" class="java.lang.Object"/>
	<field name="listaMovParcela" class="java.lang.Object"/>
	<field name="listaMovPagamento" class="java.lang.Object"/>
	<field name="listaDescontosRecibo" class="java.lang.Object"/>
	<field name="reciboPagamentoVO.codigo" class="java.lang.Integer"/>
	<field name="reciboPagamentoVO.data" class="java.util.Date"/>
	<field name="reciboPagamentoVO.valorTotal" class="java.lang.Double"/>
	<field name="reciboPagamentoVO.nomePessoaPagador" class="java.lang.String"/>
	<field name="reciboPagamentoVO.responsavelLancamento.nome" class="java.lang.String"/>
	<field name="reciboPagamentoVO.valorTotalPorExtenso" class="java.lang.String"/>
	<field name="numeroContrato" class="java.lang.String"/>
	<field name="nomeOperador" class="java.lang.String"/>
	<field name="matricula" class="java.lang.String"/>
	<field name="mostrarNumeroContrato" class="java.lang.Boolean"/>
	<field name="consultorResponsavel" class="java.lang.String"/>
	<field name="centralEventos" class="java.lang.Boolean"/>
	<field name="descricaoDevolucao" class="java.lang.String"/>
	<field name="reciboPagamentoVO.pessoaPagador.cfp" class="java.lang.String"/>
	<field name="valorParcelasAberto" class="java.lang.Double"/>
	<field name="contratoVO.valorContrato" class="java.lang.Double"/>
	<field name="movProduto.precoUnitario" class="java.lang.Double"/>
	<field name="modalidades" class="java.lang.String"/>
	<field name="descConfiguracaoVO.porcentagemDescontoApresentar" class="java.lang.String"/>
	<field name="movProduto.produto.tipoProduto" class="java.lang.String"/>
	<field name="convenioDescontoVO.descricao" class="java.lang.String"/>
	<field name="reciboPagamentoVO.contrato.codigo" class="java.lang.Integer"/>
	<field name="movProduto.descricao" class="java.lang.String"/>
	<field name="apresentarDescontos" class="java.lang.Boolean"/>
	<field name="sequencial" class="java.lang.Integer"/>
	<field name="mensagemValoresRodape" class="java.lang.String"/>
	<field name="reciboPagamentoVO.pessoaPagador.cnpj" class="java.lang.String"/>
	<variable name="reciboPagamentoVO.codigo_SUM" class="java.lang.Integer" calculation="Sum">
		<variableExpression><![CDATA[$F{reciboPagamentoVO.codigo}]]></variableExpression>
	</variable>
	<group name="caixaPoOperador" minHeightToStartNewPage="75">
		<groupExpression><![CDATA[$F{reciboPagamentoVO.codigo}]]></groupExpression>
	</group>
	<group name="movProduto">
		<groupExpression><![CDATA[]]></groupExpression>
	</group>
	<group name="movParcela"/>
	<group name="movPagamento">
		<groupExpression><![CDATA[]]></groupExpression>
	</group>
	<group name="sequencial" keepTogether="true">
		<groupHeader>
			<band/>
		</groupHeader>
		<groupFooter>
			<band/>
		</groupFooter>
	</group>
	<detail>
		<band height="97">
			<textField>
				<reportElement key="staticText-85" positionType="Float" mode="Opaque" x="5" y="75" width="45" height="10"/>
				<textElement textAlignment="Left" verticalAlignment="Middle" markup="none">
					<font fontName="SansSerif" size="7" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$R{DT_Ent_Caixa}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement key="staticText-86" positionType="Float" mode="Opaque" x="51" y="75" width="50" height="10"/>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="SansSerif" size="7" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$R{Matricula}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement key="staticText-88" positionType="Float" mode="Opaque" x="103" y="75" width="97" height="10"/>
				<textElement textAlignment="Left" verticalAlignment="Middle" markup="none">
					<font fontName="SansSerif" size="7" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$R{Nome_Responsavel}]]></textFieldExpression>
			</textField>
			<image scaleImage="FillFrame" isUsingCache="true" onErrorType="Blank" evaluationTime="Page">
				<reportElement key="image-1" positionType="Float" stretchType="RelativeToBandHeight" x="0" y="0" width="95" height="39" isPrintWhenDetailOverflows="true" forecolor="#FFFFFF"/>
				<imageExpression class="java.io.InputStream"><![CDATA[$P{logoPadraoRelatorio}]]></imageExpression>
			</image>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-228" mode="Opaque" x="5" y="86" width="70" height="10">
					<printWhenExpression><![CDATA[$F{reciboPagamentoVO.codigo}.intValue() > 0]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="7" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression class="java.util.Date"><![CDATA[$F{reciboPagamentoVO.data}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-229" mode="Opaque" x="102" y="86" width="97" height="10" isRemoveLineWhenBlank="true" isPrintWhenDetailOverflows="true"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="7" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{reciboPagamentoVO.nomePessoaPagador}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-231" mode="Opaque" x="51" y="86" width="50" height="10"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="7" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{matricula}]]></textFieldExpression>
			</textField>
			<line>
				<reportElement x="1" y="85" width="198" height="1"/>
			</line>
			<rectangle radius="10">
				<reportElement key="retDadosEmpresa1" x="0" y="39" width="195" height="36" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineWidth="0.25"/>
				</graphicElement>
			</rectangle>
			<textField>
				<reportElement x="5" y="39" width="189" height="9"/>
				<textElement>
					<font fontName="Microsoft Sans Serif" size="7" isBold="true" isItalic="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{nomeEmpresa}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="5" y="56" width="192" height="9"/>
				<textElement>
					<font fontName="Microsoft Sans Serif" size="7" isBold="true" isItalic="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{empresaVO.endereco}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="5" y="66" width="125" height="9"/>
				<textElement>
					<font fontName="Microsoft Sans Serif" size="7" isBold="true" isItalic="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{empresaVO.site}.toLowerCase()]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="5" y="47" width="72" height="9">
					<printWhenExpression><![CDATA[$P{mostrarCnpj}.equals(true)]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font fontName="Microsoft Sans Serif" size="7" isBold="true" isItalic="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{empresaVO.cnpj}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="nomeRecibo1" x="96" y="0" width="103" height="15" forecolor="#000000">
					<printWhenExpression><![CDATA[$F{reciboPagamentoVO.codigo}.intValue() > 0]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{tituloRelatorio} + " Nº " + $F{reciboPagamentoVO.codigo}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern=" #,##0.00" isBlankWhenNull="false">
				<reportElement key="valorRecibo1" x="131" y="15" width="68" height="24">
					<printWhenExpression><![CDATA[$F{reciboPagamentoVO.valorTotal}.intValue() > 0]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="14" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.Double"><![CDATA[$F{reciboPagamentoVO.valorTotal}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="130" y="66" width="66" height="9"/>
				<textElement>
					<font fontName="Microsoft Sans Serif" size="7" isBold="true" isItalic="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{empresaVO.fone}]]></textFieldExpression>
			</textField>
			<textField pattern=" #,##0.00" isBlankWhenNull="false">
				<reportElement mode="Transparent" x="96" y="15" width="35" height="24" forecolor="#000000" backcolor="#FFFFFF"/>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
					<font fontName="Arial" size="14" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica-Bold" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{moeda}]]></textFieldExpression>
			</textField>
		</band>
		<band height="12">
			<textField isBlankWhenNull="false">
				<reportElement key="textField-228" mode="Opaque" x="0" y="0" width="199" height="12" isRemoveLineWhenBlank="true">
					<printWhenExpression><![CDATA[$F{centralEventos}]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="7" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{descricaoDevolucao}]]></textFieldExpression>
			</textField>
		</band>
		<band height="41" splitType="Stretch">
			<subreport isUsingCache="false">
				<reportElement key="subreport-1" x="0" y="30" width="199" height="11" isRemoveLineWhenBlank="true" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true"/>
				<subreportParameter name="SUBREPORT_DIR">
					<subreportParameterExpression><![CDATA[$P{SUBREPORT_DIR}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="listaMovProduto">
					<subreportParameterExpression><![CDATA[$F{listaMovProduto}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="detalharPeriodoProduto">
					<subreportParameterExpression><![CDATA[$P{detalharPeriodoProduto}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="REPORT_RESOURCE_BUNDLE">
					<subreportParameterExpression><![CDATA[$P{REPORT_RESOURCE_BUNDLE}]]></subreportParameterExpression>
				</subreportParameter>
				<dataSourceExpression><![CDATA[$F{listaMovProduto}]]></dataSourceExpression>
				<subreportExpression class="java.lang.String"><![CDATA[$P{SUBREPORT_DIR1} + "MovProduto.jasper"]]></subreportExpression>
			</subreport>
			<textField>
				<reportElement key="staticText-125" mode="Opaque" x="0" y="0" width="102" height="10">
					<printWhenExpression><![CDATA[!$F{centralEventos}]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle" markup="none">
					<font fontName="SansSerif" size="7" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$R{Prod_do_Recibo}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement key="staticText-131" mode="Opaque" x="117" y="10" width="38" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle" markup="none">
					<font fontName="SansSerif" size="7" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$R{Desc}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement key="staticText-130" mode="Opaque" x="5" y="19" width="31" height="10"/>
				<textElement verticalAlignment="Middle" markup="none">
					<font fontName="SansSerif" size="7" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$R{Pacote}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement key="staticText-130" mode="Opaque" x="70" y="10" width="31" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle" markup="none">
					<font fontName="SansSerif" size="7" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$R{Unitario}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement key="staticText-133" mode="Opaque" x="154" y="10" width="40" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle" markup="none">
					<font fontName="SansSerif" size="7" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$R{Valor_Pago}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement key="staticText-132" mode="Opaque" x="101" y="10" width="17" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle" markup="none">
					<font fontName="SansSerif" size="7" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$R{Qtd}]]></textFieldExpression>
			</textField>
			<line>
				<reportElement x="1" y="29" width="198" height="1"/>
			</line>
		</band>
		<band height="10">
			<textField isStretchWithOverflow="true">
				<reportElement x="0" y="0" width="199" height="10" isRemoveLineWhenBlank="true">
					<printWhenExpression><![CDATA[$P{mostrarModalidade}.equals(true)]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Center">
					<font size="7"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{modalidades}]]></textFieldExpression>
			</textField>
		</band>
		<band height="21">
			<printWhenExpression><![CDATA[$P{detalharDescontos} && $F{movProduto.produto.tipoProduto}.equals("PM")]]></printWhenExpression>
			<subreport isUsingCache="false">
				<reportElement x="0" y="10" width="199" height="11" isRemoveLineWhenBlank="true"/>
				<subreportParameter name="REPORT_RESOURCE_BUNDLE">
					<subreportParameterExpression><![CDATA[$P{REPORT_RESOURCE_BUNDLE}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="moeda">
					<subreportParameterExpression><![CDATA[$P{moeda}]]></subreportParameterExpression>
				</subreportParameter>
				<dataSourceExpression><![CDATA[$F{listaDescontosRecibo}]]></dataSourceExpression>
				<subreportExpression class="java.lang.String"><![CDATA[$P{SUBREPORT_DIR} + "Descontos.jasper"]]></subreportExpression>
			</subreport>
			<textField>
				<reportElement key="staticText-126" mode="Opaque" x="0" y="0" width="131" height="10" isRemoveLineWhenBlank="true">
					<printWhenExpression><![CDATA[$F{apresentarDescontos}.equals( true )]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle" markup="none">
					<font fontName="SansSerif" size="7" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$R{Descontos}]]></textFieldExpression>
			</textField>
		</band>
		<band height="20">
			<printWhenExpression><![CDATA[$P{detalharParcelas}]]></printWhenExpression>
			<subreport isUsingCache="false">
				<reportElement key="subreport-2" stretchType="RelativeToBandHeight" x="0" y="0" width="199" height="20" isRemoveLineWhenBlank="true"/>
				<subreportParameter name="SUBREPORT_DIR">
					<subreportParameterExpression><![CDATA[$P{SUBREPORT_DIR}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="listaMovProduto">
					<subreportParameterExpression><![CDATA[$F{listaMovProduto}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="REPORT_RESOURCE_BUNDLE">
					<subreportParameterExpression><![CDATA[$P{REPORT_RESOURCE_BUNDLE}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="moeda">
					<subreportParameterExpression><![CDATA[$P{moeda}]]></subreportParameterExpression>
				</subreportParameter>
				<dataSourceExpression><![CDATA[$F{listaMovParcela}]]></dataSourceExpression>
				<subreportExpression class="java.lang.String"><![CDATA[$P{SUBREPORT_DIR} + "MovParcela.jasper"]]></subreportExpression>
			</subreport>
		</band>
		<band height="20">
			<textField>
				<reportElement key="staticText-126" mode="Opaque" x="0" y="0" width="131" height="10"/>
				<textElement textAlignment="Left" verticalAlignment="Middle" markup="none">
					<font fontName="SansSerif" size="7" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$R{Pag_Recibos}]]></textFieldExpression>
			</textField>
			<subreport isUsingCache="false">
				<reportElement key="subreport-3" stretchType="RelativeToBandHeight" x="0" y="9" width="199" height="11"/>
				<subreportParameter name="mostrarModalidade">
					<subreportParameterExpression><![CDATA[$P{mostrarModalidade}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="apresentarAssinaturas">
					<subreportParameterExpression><![CDATA[$P{apresentarAssinaturas}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="valorCD">
					<subreportParameterExpression><![CDATA[$P{valorCD}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="valorCA">
					<subreportParameterExpression><![CDATA[$P{valorCA}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="usuario">
					<subreportParameterExpression><![CDATA[$P{usuario}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="qtdCA">
					<subreportParameterExpression><![CDATA[$P{qtdCA}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="SUBREPORT_DIR1">
					<subreportParameterExpression><![CDATA[$P{SUBREPORT_DIR1}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="detalharPeriodoProduto">
					<subreportParameterExpression><![CDATA[$P{detalharPeriodoProduto}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="apresentarObservacao">
					<subreportParameterExpression><![CDATA[$P{apresentarObservacao}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="listaMovPagamento">
					<subreportParameterExpression><![CDATA[$F{listaMovPagamento}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="valorChequeAV">
					<subreportParameterExpression><![CDATA[$P{valorChequeAV}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="qtdChequePR">
					<subreportParameterExpression><![CDATA[$P{qtdChequePR}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="SUBREPORT_DIR2">
					<subreportParameterExpression><![CDATA[$P{SUBREPORT_DIR2}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="valorChequePR">
					<subreportParameterExpression><![CDATA[$P{valorChequePR}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="empresaVO.endereco">
					<subreportParameterExpression><![CDATA[$P{empresaVO.endereco}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="detalharParcelas">
					<subreportParameterExpression><![CDATA[$P{detalharParcelas}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="detalharPagamentos">
					<subreportParameterExpression><![CDATA[$P{detalharPagamentos}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="valorOutro">
					<subreportParameterExpression><![CDATA[$P{valorOutro}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="codRecibo">
					<subreportParameterExpression><![CDATA[$P{codRecibo}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="mostrarCnpj">
					<subreportParameterExpression><![CDATA[$P{mostrarCnpj}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="qtdAV">
					<subreportParameterExpression><![CDATA[$P{qtdAV}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="dataIni">
					<subreportParameterExpression><![CDATA[$P{dataIni}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="qtdOutro">
					<subreportParameterExpression><![CDATA[$P{qtdOutro}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="logoPadraoRelatorio">
					<subreportParameterExpression><![CDATA[$P{logoPadraoRelatorio}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="observacaoRecibo">
					<subreportParameterExpression><![CDATA[$P{observacaoRecibo}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="SUBREPORT_DIR">
					<subreportParameterExpression><![CDATA[$P{SUBREPORT_DIR}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="totalContrato">
					<subreportParameterExpression><![CDATA[$P{totalContrato}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="qtdCD">
					<subreportParameterExpression><![CDATA[$P{qtdCD}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="dataFim">
					<subreportParameterExpression><![CDATA[$P{dataFim}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="detalharDescontos">
					<subreportParameterExpression><![CDATA[$P{detalharDescontos}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="empresaVO.cnpj">
					<subreportParameterExpression><![CDATA[$P{empresaVO.cnpj}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="tituloRelatorio">
					<subreportParameterExpression><![CDATA[$P{tituloRelatorio}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="nomeEmpresa">
					<subreportParameterExpression><![CDATA[$P{nomeEmpresa}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="empresaVO.site">
					<subreportParameterExpression><![CDATA[$P{empresaVO.site}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="qtdChequeAV">
					<subreportParameterExpression><![CDATA[$P{qtdChequeAV}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="valorAV">
					<subreportParameterExpression><![CDATA[$P{valorAV}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="empresaVO.fone">
					<subreportParameterExpression><![CDATA[$P{empresaVO.fone}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="REPORT_RESOURCE_BUNDLE">
					<subreportParameterExpression><![CDATA[$P{REPORT_RESOURCE_BUNDLE}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="moeda">
					<subreportParameterExpression><![CDATA[$P{moeda}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="versaoSoftware">
					<subreportParameterExpression><![CDATA[$P{versaoSoftware}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="filtros">
					<subreportParameterExpression><![CDATA[$P{filtros}]]></subreportParameterExpression>
				</subreportParameter>
				<dataSourceExpression><![CDATA[$F{listaMovPagamento}]]></dataSourceExpression>
				<subreportExpression class="java.lang.String"><![CDATA[$P{SUBREPORT_DIR} + "MovPagamento.jasper"]]></subreportExpression>
			</subreport>
		</band>
		<band height="175">
			<textField isStretchWithOverflow="true">
				<reportElement x="5" y="0" width="190" height="29" isPrintWhenDetailOverflows="true"/>
				<textElement verticalAlignment="Middle">
					<font size="7" isBold="true" isItalic="true" pdfFontName="Helvetica-BoldOblique"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$R{Recebemos_de} + " " +
($F{reciboPagamentoVO.nomePessoaPagador}.isEmpty()? " " : $F{reciboPagamentoVO.nomePessoaPagador}) + " " + $R{A_quantia_de} + " " +
($F{reciboPagamentoVO.valorTotalPorExtenso}.isEmpty() ? " " : $F{reciboPagamentoVO.valorTotalPorExtenso}) + " " + $R{Proveniente_dos} +
$F{mensagemValoresRodape}]]></textFieldExpression>
			</textField>
			<line>
				<reportElement positionType="FixRelativeToBottom" x="0" y="171" width="199" height="1" isRemoveLineWhenBlank="true"/>
				<graphicElement>
					<pen lineWidth="1.5" lineStyle="Dashed"/>
				</graphicElement>
			</line>
			<textField>
				<reportElement positionType="FixRelativeToBottom" x="5" y="153" width="55" height="8" isRemoveLineWhenBlank="true"/>
				<textElement markup="none">
					<font size="6" isBold="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$R{Dt_impressao}]]></textFieldExpression>
			</textField>
			<textField pattern="dd/MM/yyyy HH:mm:ss" isBlankWhenNull="true">
				<reportElement key="dataImpressao1" positionType="FixRelativeToBottom" mode="Transparent" x="60" y="153" width="70" height="8" isRemoveLineWhenBlank="true"/>
				<textElement verticalAlignment="Top">
					<font fontName="SansSerif" size="6" isItalic="false" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression class="java.util.Date"><![CDATA[new Date()]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement positionType="FixRelativeToBottom" x="5" y="161" width="55" height="8" isRemoveLineWhenBlank="true"/>
				<textElement markup="none">
					<font size="6" isBold="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$R{Dt_pagamento}]]></textFieldExpression>
			</textField>
			<textField pattern="dd/MM/yyyy HH:mm:ss">
				<reportElement key="dataPagamento1" positionType="FixRelativeToBottom" x="60" y="161" width="70" height="8" isRemoveLineWhenBlank="true">
					<printWhenExpression><![CDATA[$F{reciboPagamentoVO.codigo}.intValue() > 0]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font size="6"/>
				</textElement>
				<textFieldExpression class="java.util.Date"><![CDATA[$F{reciboPagamentoVO.data}]]></textFieldExpression>
			</textField>
			<elementGroup>
				<line>
					<reportElement positionType="FixRelativeToBottom" x="5" y="77" width="190" height="1" isRemoveLineWhenBlank="true">
						<printWhenExpression><![CDATA[$P{apresentarAssinaturas}]]></printWhenExpression>
					</reportElement>
				</line>
				<textField>
					<reportElement positionType="FixRelativeToBottom" x="5" y="79" width="190" height="12" isRemoveLineWhenBlank="true">
						<printWhenExpression><![CDATA[$P{apresentarAssinaturas}]]></printWhenExpression>
					</reportElement>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="8" isBold="true"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$R{Resp_Recebimento} + " " +
$F{reciboPagamentoVO.responsavelLancamento.nome}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement positionType="FixRelativeToBottom" x="5" y="90" width="190" height="12" isRemoveLineWhenBlank="true">
						<printWhenExpression><![CDATA[$P{apresentarAssinaturas}]]></printWhenExpression>
					</reportElement>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="8" isBold="true"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$R{Cons.Resp} + " " +
$F{consultorResponsavel}]]></textFieldExpression>
				</textField>
				<line>
					<reportElement positionType="FixRelativeToBottom" x="5" y="129" width="190" height="1" isRemoveLineWhenBlank="true">
						<printWhenExpression><![CDATA[$P{apresentarAssinaturas}]]></printWhenExpression>
					</reportElement>
				</line>
				<textField>
					<reportElement positionType="FixRelativeToBottom" x="5" y="131" width="190" height="12" isRemoveLineWhenBlank="true">
						<printWhenExpression><![CDATA[$P{apresentarAssinaturas}]]></printWhenExpression>
					</reportElement>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="8" isBold="true"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$R{Cliente} + " " +
$F{reciboPagamentoVO.nomePessoaPagador}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement positionType="FixRelativeToBottom" x="5" y="141" width="190" height="12" isRemoveLineWhenBlank="true">
						<printWhenExpression><![CDATA[$P{apresentarAssinaturas}]]></printWhenExpression>
					</reportElement>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="8" isBold="true"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[""+($F{reciboPagamentoVO.pessoaPagador.cfp} == null || $F{reciboPagamentoVO.pessoaPagador.cfp}.equals("") ? ($F{reciboPagamentoVO.pessoaPagador.cnpj} == null || $F{reciboPagamentoVO.pessoaPagador.cnpj}.equals("") ? " "
	: "CNPJ: "+$F{reciboPagamentoVO.pessoaPagador.cnpj} ) : $P{identificador} + " " + $F{reciboPagamentoVO.pessoaPagador.cfp})]]></textFieldExpression>
				</textField>
			</elementGroup>
			<textField isStretchWithOverflow="true" isBlankWhenNull="false">
				<reportElement stretchType="RelativeToBandHeight" x="5" y="33" width="190" height="20" isPrintWhenDetailOverflows="true">
					<printWhenExpression><![CDATA[$P{apresentarObservacao}]]></printWhenExpression>
				</reportElement>
				<textElement verticalAlignment="Top">
					<font size="6" isBold="false" isItalic="true" pdfFontName="Helvetica-BoldOblique"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{observacaoRecibo}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
</jasperReport>

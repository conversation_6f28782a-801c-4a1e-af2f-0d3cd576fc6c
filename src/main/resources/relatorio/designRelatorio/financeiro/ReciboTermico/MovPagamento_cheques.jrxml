<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="MovPagamento_cheques" pageWidth="195" pageHeight="802" whenNoDataType="AllSectionsNoDetail" columnWidth="195" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0">
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.encoding" value="UTF-8"/>
	<property name="ireport.zoom" value="1.8627639691774651"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="java.util.*"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<parameter name="moeda" class="java.lang.String">
		<defaultValueExpression><![CDATA["R$"]]></defaultValueExpression>
	</parameter>
	<field name="valor" class="java.lang.Double"/>
	<field name="numero" class="java.lang.String"/>
	<field name="agencia" class="java.lang.String"/>
	<field name="banco.nome" class="java.lang.String"/>
	<field name="conta" class="java.lang.String"/>
	<field name="dataCompensacao_Apresentar" class="java.lang.String"/>
	<field name="cpf" class="java.lang.String"/>
	<field name="cnpj" class="java.lang.String"/>
	<field name="situacao" class="java.lang.String"/>
	<pageHeader>
		<band splitType="Stretch"/>
	</pageHeader>
	<columnHeader>
		<band splitType="Stretch"/>
	</columnHeader>
	<detail>
		<band height="30" splitType="Prevent">
			<textField pattern="#,##0.00" isBlankWhenNull="false">
				<reportElement key="textField-1" mode="Opaque" x="155" y="20" width="40" height="10"/>
				<box>
					<pen lineWidth="0.0" lineColor="#999999"/>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Double" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Double" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="7" isBold="true" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression class="java.lang.Double"><![CDATA[$F{valor}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement key="staticText-7" x="12" y="20" width="17" height="10">
					<printWhenExpression><![CDATA[!$F{situacao}.equals("CA")]]></printWhenExpression>
				</reportElement>
				<textElement verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="7" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Dtc:]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-2" x="94" y="10" width="10" height="10"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="7" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[N:]]></text>
			</staticText>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-4" mode="Opaque" x="137" y="0" width="41" height="10"/>
				<box>
					<pen lineWidth="0.0" lineColor="#999999"/>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Double" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Double" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="7" isBold="true" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{agencia}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement key="staticText-6" x="19" y="10" width="10" height="10"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="7" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[C:]]></text>
			</staticText>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-5" mode="Opaque" x="29" y="10" width="55" height="10"/>
				<box>
					<pen lineWidth="0.0" lineColor="#999999"/>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Double" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Double" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="7" isBold="true" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{conta}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement key="staticText-5" x="125" y="0" width="12" height="10"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="7" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Ag:]]></text>
			</staticText>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-6" mode="Transparent" x="29" y="20" width="45" height="10"/>
				<box>
					<pen lineWidth="0.0" lineColor="#999999"/>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Double" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Double" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="7" isBold="true" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{situacao}.equals("CA")?"":$F{dataCompensacao_Apresentar}.substring( 0, 10 )]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement key="staticText-4" x="0" y="0" width="12" height="10"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="7" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[ B:]]></text>
			</staticText>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-7" mode="Opaque" x="72" y="20" width="65" height="10"/>
				<box>
					<pen lineWidth="0.0" lineColor="#999999"/>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Double" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Double" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="7" isBold="true" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[( $F{cpf}.equals("") ?  $F{cnpj}:$F{cpf} )]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-2" mode="Opaque" x="104" y="10" width="74" height="10"/>
				<box>
					<pen lineWidth="0.0" lineColor="#999999"/>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Double" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Double" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="7" isBold="true" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{numero}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-3" mode="Transparent" x="12" y="0" width="102" height="10"/>
				<box>
					<pen lineWidth="0.0" lineColor="#999999"/>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Double" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Double" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="7" isBold="true" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{banco.nome}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00" isBlankWhenNull="false">
				<reportElement mode="Opaque" x="137" y="20" width="22" height="10" forecolor="#000000" backcolor="#FFFFFF"/>
				<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
					<font fontName="Microsoft Sans Serif" size="7" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{moeda}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<columnFooter>
		<band splitType="Stretch"/>
	</columnFooter>
	<pageFooter>
		<band splitType="Stretch"/>
	</pageFooter>
	<summary>
		<band splitType="Stretch"/>
	</summary>
</jasperReport>

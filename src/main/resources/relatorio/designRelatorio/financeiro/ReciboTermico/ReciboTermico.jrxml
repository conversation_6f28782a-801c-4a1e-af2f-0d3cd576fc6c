<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="ReciboTermico" language="groovy" pageWidth="204" pageHeight="360" columnWidth="194" leftMargin="5" rightMargin="5" topMargin="4" bottomMargin="4">
	<property name="ireport.zoom" value="1.0"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<detail>
		<band height="352">
			<staticText>
				<reportElement x="0" y="0" width="194" height="352"/>
				<textElement>
					<font pdfEncoding="Cp1252"/>
				</textElement>
				<text><![CDATA[Este é um Texte para Impressão.

Impressora Térmica.
Impressão em PDF
Verificar o comportamento de Textos neste tipo de impressão, bem como o espaço a ser usado, e outras coisas, como qualidade.

Lorem ipsum dolor sit amet, consectetur adipisicing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum

Teste realizado em 21/08/2014]]></text>
			</staticText>
		</band>
	</detail>
</jasperReport>

<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="ReciboRel" pageWidth="203" pageHeight="842" whenNoDataType="AllSectionsNoDetail" columnWidth="199" leftMargin="2" rightMargin="2" topMargin="2" bottomMargin="2" whenResourceMissingType="Empty">
	<property name="ireport.zoom" value="3.0"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.encoding" value="ISO-8859-1"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="java.util.*"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<parameter name="tituloRelatorio" class="java.lang.String" isForPrompting="false"/>
	<parameter name="nomeEmpresa" class="java.lang.String" isForPrompting="false"/>
	<parameter name="versaoSoftware" class="java.lang.String" isForPrompting="false"/>
	<parameter name="usuario" class="java.lang.String" isForPrompting="false"/>
	<parameter name="filtros" class="java.lang.String" isForPrompting="false"/>
	<parameter name="SUBREPORT_DIR" class="java.lang.String">
		<defaultValueExpression><![CDATA["E:\\desenvolvimentosv\\Projeto Pacto Solucoes\\ZillyonWeb\\src\\java\\relatorio\\designRelatorio\\financeiro\\"]]></defaultValueExpression>
	</parameter>
	<parameter name="SUBREPORT_DIR1" class="java.lang.String">
		<defaultValueExpression><![CDATA["E:\\desenvolvimentosv\\Projeto Pacto Solucoes\\ZillyonWeb\\src\\java\\relatorio\\designRelatorio\\financeiro\\"]]></defaultValueExpression>
	</parameter>
	<parameter name="dataIni" class="java.lang.String" isForPrompting="false"/>
	<parameter name="dataFim" class="java.lang.String" isForPrompting="false"/>
	<parameter name="detalharPeriodoProduto" class="java.lang.Boolean"/>
	<parameter name="detalharParcelas" class="java.lang.Boolean"/>
	<parameter name="detalharPagamentos" class="java.lang.Boolean"/>
	<parameter name="detalharDescontos" class="java.lang.Boolean"/>
	<parameter name="apresentarAssinaturas" class="java.lang.Boolean" isForPrompting="false"/>
	<parameter name="qtdAV" class="java.lang.String" isForPrompting="false"/>
	<parameter name="qtdCA" class="java.lang.String" isForPrompting="false"/>
	<parameter name="qtdChequeAV" class="java.lang.String" isForPrompting="false"/>
	<parameter name="qtdChequePR" class="java.lang.String" isForPrompting="false"/>
	<parameter name="qtdOutro" class="java.lang.String" isForPrompting="false"/>
	<parameter name="valorAV" class="java.lang.Double" isForPrompting="false"/>
	<parameter name="valorCA" class="java.lang.Double" isForPrompting="false"/>
	<parameter name="valorChequeAV" class="java.lang.Double" isForPrompting="false"/>
	<parameter name="valorChequePR" class="java.lang.Double" isForPrompting="false"/>
	<parameter name="valorOutro" class="java.lang.Double" isForPrompting="false"/>
	<parameter name="logoPadraoRelatorio" class="java.io.InputStream" isForPrompting="false"/>
	<parameter name="qtdCD" class="java.lang.String"/>
	<parameter name="valorCD" class="java.lang.Double"/>
	<parameter name="SUBREPORT_DIR2" class="java.lang.String">
		<defaultValueExpression><![CDATA["E:\\desenvolvimentosv\\Projeto Pacto Solucoes\\ZillyonWeb\\src\\java\\relatorio\\designRelatorio\\financeiro\\"]]></defaultValueExpression>
	</parameter>
	<parameter name="codRecibo" class="java.lang.String"/>
	<parameter name="empresaVO.cnpj" class="java.lang.String"/>
	<parameter name="empresaVO.endereco" class="java.lang.String"/>
	<parameter name="empresaVO.site" class="java.lang.String"/>
	<parameter name="empresaVO.fone" class="java.lang.String"/>
	<parameter name="mostrarCnpj" class="java.lang.Boolean">
		<defaultValueExpression><![CDATA[true]]></defaultValueExpression>
	</parameter>
	<parameter name="totalContrato" class="java.lang.String"/>
	<parameter name="mostrarModalidade" class="java.lang.Boolean"/>
	<parameter name="apresentarObservacao" class="java.lang.Boolean" isForPrompting="false"/>
	<parameter name="observacaoRecibo" class="java.lang.String"/>
	<parameter name="moeda" class="java.lang.String">
		<defaultValueExpression><![CDATA["R$"]]></defaultValueExpression>
	</parameter>
	<parameter name="identificador" class="java.lang.String">
		<defaultValueExpression><![CDATA[""]]></defaultValueExpression>
	</parameter>
	<field name="sequencial" class="java.lang.Integer"/>
	<field name="recibo" class="java.lang.Object"/>
	<field name="reciboDatasource" class="java.lang.Object"/>
	<group name="sequencial">
		<groupExpression><![CDATA[$F{sequencial}]]></groupExpression>
	</group>
	<detail>
		<band height="50">
			<subreport>
				<reportElement x="0" y="0" width="199" height="50"/>
				<subreportParameter name="mostrarModalidade">
					<subreportParameterExpression><![CDATA[$P{mostrarModalidade}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="apresentarAssinaturas">
					<subreportParameterExpression><![CDATA[$P{apresentarAssinaturas}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="valorCD">
					<subreportParameterExpression><![CDATA[$P{valorCD}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="valorCA">
					<subreportParameterExpression><![CDATA[$P{valorCA}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="usuario">
					<subreportParameterExpression><![CDATA[$P{usuario}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="qtdCA">
					<subreportParameterExpression><![CDATA[$P{qtdCA}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="SUBREPORT_DIR1">
					<subreportParameterExpression><![CDATA[$P{SUBREPORT_DIR1}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="detalharPeriodoProduto">
					<subreportParameterExpression><![CDATA[$P{detalharPeriodoProduto}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="apresentarObservacao">
					<subreportParameterExpression><![CDATA[$P{apresentarObservacao}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="valorChequeAV">
					<subreportParameterExpression><![CDATA[$P{valorChequeAV}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="qtdChequePR">
					<subreportParameterExpression><![CDATA[$P{qtdChequePR}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="valorChequePR">
					<subreportParameterExpression><![CDATA[$P{valorChequePR}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="SUBREPORT_DIR2">
					<subreportParameterExpression><![CDATA[$P{SUBREPORT_DIR2}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="empresaVO.endereco">
					<subreportParameterExpression><![CDATA[$P{empresaVO.endereco}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="detalharPagamentos">
					<subreportParameterExpression><![CDATA[$P{detalharPagamentos}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="detalharParcelas">
					<subreportParameterExpression><![CDATA[$P{detalharParcelas}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="valorOutro">
					<subreportParameterExpression><![CDATA[$P{valorOutro}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="codRecibo">
					<subreportParameterExpression><![CDATA[$P{codRecibo}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="mostrarCnpj">
					<subreportParameterExpression><![CDATA[$P{mostrarCnpj}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="qtdAV">
					<subreportParameterExpression><![CDATA[$P{qtdAV}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="dataIni">
					<subreportParameterExpression><![CDATA[$P{dataIni}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="qtdOutro">
					<subreportParameterExpression><![CDATA[$P{qtdOutro}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="logoPadraoRelatorio">
					<subreportParameterExpression><![CDATA[$P{logoPadraoRelatorio}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="observacaoRecibo">
					<subreportParameterExpression><![CDATA[$P{observacaoRecibo}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="SUBREPORT_DIR">
					<subreportParameterExpression><![CDATA[$P{SUBREPORT_DIR}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="totalContrato">
					<subreportParameterExpression><![CDATA[$P{totalContrato}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="qtdCD">
					<subreportParameterExpression><![CDATA[$P{qtdCD}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="dataFim">
					<subreportParameterExpression><![CDATA[$P{dataFim}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="detalharDescontos">
					<subreportParameterExpression><![CDATA[$P{detalharDescontos}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="empresaVO.cnpj">
					<subreportParameterExpression><![CDATA[$P{empresaVO.cnpj}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="tituloRelatorio">
					<subreportParameterExpression><![CDATA[$P{tituloRelatorio}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="empresaVO.site">
					<subreportParameterExpression><![CDATA[$P{empresaVO.site}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="nomeEmpresa">
					<subreportParameterExpression><![CDATA[$P{nomeEmpresa}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="identificador">
					<subreportParameterExpression><![CDATA[$P{identificador}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="qtdChequeAV">
					<subreportParameterExpression><![CDATA[$P{qtdChequeAV}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="valorAV">
					<subreportParameterExpression><![CDATA[$P{valorAV}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="empresaVO.fone">
					<subreportParameterExpression><![CDATA[$P{empresaVO.fone}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="REPORT_RESOURCE_BUNDLE">
					<subreportParameterExpression><![CDATA[$P{REPORT_RESOURCE_BUNDLE}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="moeda">
					<subreportParameterExpression><![CDATA[$P{moeda}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="filtros">
					<subreportParameterExpression><![CDATA[$P{filtros}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="versaoSoftware">
					<subreportParameterExpression><![CDATA[$P{versaoSoftware}]]></subreportParameterExpression>
				</subreportParameter>
				<dataSourceExpression><![CDATA[$F{reciboDatasource}]]></dataSourceExpression>
				<subreportExpression class="java.lang.String"><![CDATA[$P{SUBREPORT_DIR} + "ReciboModeloTermico.jasper"]]></subreportExpression>
			</subreport>
		</band>
	</detail>
</jasperReport>

<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="RelatorioEstoqueProduto" pageWidth="1080" pageHeight="842" whenNoDataType="NoDataSection" columnWidth="555" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20">
	<property name="ireport.zoom" value="2.8531167061100033"/>
	<property name="ireport.x" value="1263"/>
	<property name="ireport.y" value="127"/>
	<subDataset name="dataset1"/>
	<subDataset name="Table Dataset 1"/>
	<parameter name="logoPadraoRelatorio" class="java.io.InputStream" isForPrompting="false"/>
	<parameter name="tipoVisualizacao" class="java.lang.Integer"/>
	<parameter name="statusProduto" class="java.lang.String"/>
	<parameter name="ordenacao" class="java.lang.String" isForPrompting="false"/>
	<parameter name="valorImpresso" class="java.lang.String" isForPrompting="false"/>
	<parameter name="totalizadores" class="java.lang.Object"/>
	<parameter name="SUBREPORT_DIR" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA["C:\\PactoJ\\Desenvolvimento\\Sistemas\\ZillyonWeb\\tronco-novo\\src\\main\\resources\\relatorio\\designRelatorio\\estoque\\"]]></defaultValueExpression>
	</parameter>
	<parameter name="nomeEmpresa" class="java.lang.String"/>
	<parameter name="enderecoEmpresa" class="java.lang.String"/>
	<parameter name="nomeCidade" class="java.lang.String"/>
	<parameter name="titulo" class="java.lang.String">
		<defaultValueExpression><![CDATA["Faturamento por Período"]]></defaultValueExpression>
	</parameter>
	<parameter name="emAbertoQtd" class="java.lang.Integer"/>
	<parameter name="periodo" class="java.lang.String"/>
	<parameter name="periodoValor" class="java.lang.String"/>
	<parameter name="competencia" class="java.lang.Boolean"/>
	<parameter name="especie" class="java.lang.String"/>
	<parameter name="especieQtd" class="java.lang.String"/>
	<parameter name="especieVlr" class="java.lang.String"/>
	<parameter name="totalizar" class="java.lang.Boolean"/>
	<queryString language="xPath">
		<![CDATA[]]>
	</queryString>
	<field name="descricaoProduto" class="java.lang.String"/>
	<field name="matriculaCliente" class="java.lang.String"/>
	<field name="nomeCliente" class="java.lang.String"/>
	<field name="nomeResponsavelLancamento" class="java.lang.String"/>
	<field name="dataCadastroCliente" class="java.util.Date"/>
	<field name="codContrato" class="java.lang.Integer"/>
	<field name="dataInicio" class="java.lang.String"/>
	<field name="dataTermino" class="java.lang.String"/>
	<field name="duracaoPlano" class="java.lang.Integer"/>
	<field name="modalidades" class="java.lang.String"/>
	<field name="nomePlano" class="java.lang.String"/>
	<field name="situacaoContrato" class="java.lang.String"/>
	<field name="condicaoPagamento" class="java.lang.String"/>
	<field name="dataLancamentoProduto" class="java.util.Date"/>
	<field name="formaPagApresentar" class="java.lang.String"/>
	<field name="valorCompetencia" class="java.lang.Double"/>
	<field name="turma" class="java.lang.String"/>
	<field name="categoria" class="java.lang.String"/>
	<variable name="valorTotal" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[$F{valorCompetencia}]]></variableExpression>
		<initialValueExpression><![CDATA[new Double(0)]]></initialValueExpression>
	</variable>
	<background>
		<band splitType="Stretch"/>
	</background>
	<pageHeader>
		<band height="108">
			<staticText>
				<reportElement mode="Opaque" x="1" y="96" width="80" height="12" backcolor="#CCCCCC"/>
				<textElement verticalAlignment="Middle">
					<font size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[Produto]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="81" y="96" width="40" height="12" backcolor="#CCCCCC"/>
				<textElement verticalAlignment="Middle">
					<font size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[Matrícula]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="121" y="96" width="95" height="12" backcolor="#CCCCCC"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[Nome]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="216" y="96" width="90" height="12" backcolor="#CCCCCC"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[Responsável]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="306" y="96" width="55" height="12" backcolor="#CCCCCC"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[Dt. Cadastro]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="361" y="96" width="39" height="12" backcolor="#CCCCCC"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[Nº Contrato]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="400" y="96" width="45" height="12" backcolor="#CCCCCC"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[Dt. Início]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="445" y="96" width="55" height="12" backcolor="#CCCCCC"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[Dt. Término]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="500" y="96" width="30" height="12" backcolor="#CCCCCC"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[Duração]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="530" y="96" width="56" height="12" backcolor="#CCCCCC"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[Modalidades]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="652" y="96" width="60" height="12" backcolor="#CCCCCC"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[Plano]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="712" y="96" width="70" height="12" backcolor="#CCCCCC"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[Situação Contrato]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="782" y="96" width="55" height="12" backcolor="#CCCCCC"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[Dt. Lanç.]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="616" y="96" width="36" height="12" backcolor="#CCCCCC"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[Categoria]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="899" y="96" width="87" height="12" backcolor="#CCCCCC"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[Condição Pagamento]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="986" y="96" width="52" height="12" backcolor="#CCCCCC"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[Valor]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-14" mode="Opaque" x="776" y="4" width="262" height="23"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Top" lineSpacing="Single">
					<font fontName="Microsoft Sans Serif" size="9" isBold="true" isItalic="true" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica-BoldOblique"/>
				</textElement>
				<text><![CDATA[Zillyonweb- Sistema Administrativo para Academias Desenvolvido por PACTO Soluções Tecnológicas Ltda.]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-15" mode="Opaque" x="942" y="25" width="96" height="12"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Top" lineSpacing="Single">
					<font fontName="Microsoft Sans Serif" size="6" isBold="true" isItalic="true" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica-BoldOblique"/>
				</textElement>
				<text><![CDATA[(0xx62) 3251-5820]]></text>
			</staticText>
			<textField pattern="dd/MM/yyyy HH.mm.ss" isBlankWhenNull="false">
				<reportElement key="dataRel-1" mode="Transparent" x="1005" y="33" width="33" height="12" backcolor="#FFFFFF"/>
				<box bottomPadding="0">
					<pen lineColor="#FFFFFF"/>
					<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#FFFFFF"/>
					<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#FFFFFF"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#FFFFFF"/>
					<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#FFFFFF"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="6" isBold="true" isItalic="false" isStrikeThrough="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.util.Date"><![CDATA[new Date()]]></textFieldExpression>
			</textField>
			<image isUsingCache="true" onErrorType="Blank" evaluationTime="Page">
				<reportElement key="image-1" positionType="Float" stretchType="RelativeToBandHeight" x="0" y="4" width="121" height="48" isPrintWhenDetailOverflows="true" forecolor="#FFFFFF"/>
				<graphicElement>
					<pen lineColor="#FFFFFF"/>
				</graphicElement>
				<imageExpression class="java.io.InputStream"><![CDATA[$P{logoPadraoRelatorio}]]></imageExpression>
			</image>
			<staticText>
				<reportElement x="948" y="33" width="57" height="12"/>
				<textElement verticalAlignment="Middle">
					<font size="6" isBold="true"/>
				</textElement>
				<text><![CDATA[Data da Impressao:]]></text>
			</staticText>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="122" y="4" width="172" height="16"/>
				<textElement verticalAlignment="Middle"/>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{nomeEmpresa}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" mode="Transparent" x="122" y="20" width="172" height="16"/>
				<textElement verticalAlignment="Middle"/>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{enderecoEmpresa}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" mode="Transparent" x="122" y="36" width="172" height="16"/>
				<textElement verticalAlignment="Middle"/>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{nomeCidade}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="1" y="64" width="1037" height="31"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="16" isBold="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{titulo}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement mode="Opaque" x="837" y="96" width="62" height="12" backcolor="#CCCCCC"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[Forma Pagamento]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="586" y="96" width="30" height="12" backcolor="#CCCCCC"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[Turmas]]></text>
			</staticText>
		</band>
	</pageHeader>
	<detail>
		<band height="16" splitType="Stretch">
			<textField isStretchWithOverflow="true">
				<reportElement mode="Transparent" x="1" y="3" width="80" height="13"/>
				<textElement verticalAlignment="Middle">
					<font size="7"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{descricaoProduto}]]></textFieldExpression>
			</textField>
			<textField pattern="dd/MM/yyyy" isBlankWhenNull="true">
				<reportElement x="306" y="3" width="55" height="13"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font size="8"/>
				</textElement>
				<textFieldExpression class="java.util.Date"><![CDATA[$F{dataCadastroCliente}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="">
				<reportElement x="81" y="3" width="40" height="13"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font size="7"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{matriculaCliente}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="###0" isBlankWhenNull="true">
				<reportElement x="361" y="3" width="50" height="13"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.Integer"><![CDATA[$F{codContrato}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement x="121" y="3" width="95" height="13"/>
				<textElement verticalAlignment="Middle">
					<font size="7"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{nomeCliente}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement x="216" y="3" width="90" height="13"/>
				<textElement verticalAlignment="Middle">
					<font size="7"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{nomeResponsavelLancamento}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00" isBlankWhenNull="true">
				<reportElement x="400" y="3" width="45" height="13"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{dataInicio}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="###0;-###0" isBlankWhenNull="true">
				<reportElement x="500" y="3" width="30" height="13"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.Integer"><![CDATA[$F{duracaoPlano}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0.00" isBlankWhenNull="true">
				<reportElement x="530" y="3" width="54" height="13"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{modalidades}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0.00" isBlankWhenNull="true">
				<reportElement x="652" y="3" width="60" height="13"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{nomePlano}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0.00" isBlankWhenNull="true">
				<reportElement x="712" y="3" width="73" height="13"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{situacaoContrato}]]></textFieldExpression>
			</textField>
			<textField pattern="dd/MM/yyyy" isBlankWhenNull="true">
				<reportElement x="785" y="3" width="52" height="13"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font size="8"/>
				</textElement>
				<textFieldExpression class="java.util.Date"><![CDATA[$F{dataLancamentoProduto}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00" isBlankWhenNull="true">
				<reportElement x="445" y="3" width="55" height="13"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{dataTermino}]]></textFieldExpression>
			</textField>
			<line>
				<reportElement x="0" y="1" width="1038" height="1"/>
			</line>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="899" y="3" width="87" height="13"/>
				<textElement verticalAlignment="Middle">
					<font size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{condicaoPagamento}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="837" y="3" width="62" height="13"/>
				<textElement verticalAlignment="Middle">
					<font size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{formaPagApresentar}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="R$ #,##0.00" isBlankWhenNull="true">
				<reportElement x="986" y="3" width="52" height="13"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.Double"><![CDATA[$F{valorCompetencia}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0.00" isBlankWhenNull="true">
				<reportElement x="584" y="3" width="32" height="13"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{turma}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0.00" isBlankWhenNull="true">
				<reportElement x="616" y="2" width="36" height="13"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{categoria}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<pageFooter>
		<band height="51">
			<textField isBlankWhenNull="false">
				<reportElement key="textField-211" x="941" y="32" width="75" height="17"/>
				<box bottomPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right">
					<font fontName="Arial" size="6" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA["Página: " + $V{PAGE_NUMBER} + " de "]]></textFieldExpression>
			</textField>
			<textField evaluationTime="Report" isBlankWhenNull="false">
				<reportElement key="textField-212" x="1016" y="32" width="29" height="17"/>
				<box bottomPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement>
					<font fontName="Arial" size="6" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[" " + $V{PAGE_NUMBER} + ""]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="1" y="41" width="78" height="8"/>
				<textElement>
					<font size="6" isBold="true"/>
				</textElement>
				<text><![CDATA[Total de registros até aqui:]]></text>
			</staticText>
			<textField>
				<reportElement isPrintRepeatedValues="false" x="78" y="41" width="30" height="8"/>
				<textElement>
					<font size="6"/>
				</textElement>
				<textFieldExpression class="java.lang.Integer"><![CDATA[$V{REPORT_COUNT}]]></textFieldExpression>
			</textField>
		</band>
	</pageFooter>
	<summary>
		<band height="64" splitType="Stretch">
			<staticText>
				<reportElement x="854" y="8" width="70" height="12"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[Valor Total:]]></text>
			</staticText>
			<textField isStretchWithOverflow="true" pattern="R$ #,##0.00" isBlankWhenNull="true">
				<reportElement x="924" y="8" width="113" height="12"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.Double"><![CDATA[$V{valorTotal}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="811" y="8" width="43" height="12"/>
				<textElement textAlignment="Right">
					<font size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.Integer"><![CDATA[$P{emAbertoQtd}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="746" y="8" width="65" height="12"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="7" isBold="true" isUnderline="false"/>
				</textElement>
				<text><![CDATA[Quantidade:]]></text>
			</staticText>
			<staticText>
				<reportElement x="1" y="20" width="98" height="12">
					<printWhenExpression><![CDATA[$P{competencia} == true]]></printWhenExpression>
				</reportElement>
				<textElement verticalAlignment="Middle">
					<font size="7" isBold="true" isUnderline="false"/>
				</textElement>
				<text><![CDATA[PRODUTOS PAGOS]]></text>
			</staticText>
			<staticText>
				<reportElement x="1" y="32" width="98" height="12">
					<printWhenExpression><![CDATA[$P{competencia} == true]]></printWhenExpression>
				</reportElement>
				<textElement verticalAlignment="Middle">
					<font size="7" isBold="true" isUnderline="false"/>
				</textElement>
				<text><![CDATA[MÊS /ANO DE VENCIMENTO]]></text>
			</staticText>
			<staticText>
				<reportElement x="99" y="32" width="78" height="12">
					<printWhenExpression><![CDATA[$P{competencia} == true]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="7" isBold="true" isUnderline="false"/>
				</textElement>
				<text><![CDATA[VALOR]]></text>
			</staticText>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="99" y="44" width="89" height="12">
					<printWhenExpression><![CDATA[$P{competencia} == true]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Right">
					<font size="7"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{periodoValor}]]></textFieldExpression>
			</textField>
			<line>
				<reportElement x="1" y="4" width="1039" height="1"/>
			</line>
			<staticText>
				<reportElement x="507" y="20" width="52" height="12">
					<printWhenExpression><![CDATA[$P{totalizar} == true]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="7" isBold="true" isUnderline="false"/>
				</textElement>
				<text><![CDATA[QUANTIDADE]]></text>
			</staticText>
			<staticText>
				<reportElement x="559" y="20" width="86" height="12">
					<printWhenExpression><![CDATA[$P{totalizar} == true]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="7" isBold="true" isUnderline="false"/>
				</textElement>
				<text><![CDATA[VALOR]]></text>
			</staticText>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="400" y="32" width="107" height="12">
					<printWhenExpression><![CDATA[$P{totalizar} == true]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font size="7"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{especie}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="507" y="32" width="41" height="12">
					<printWhenExpression><![CDATA[$P{totalizar} == true]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Right">
					<font size="7"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{especieQtd}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="400" y="20" width="107" height="12">
					<printWhenExpression><![CDATA[$P{totalizar} == true]]></printWhenExpression>
				</reportElement>
				<textElement verticalAlignment="Middle">
					<font size="7" isBold="true" isUnderline="false"/>
				</textElement>
				<text><![CDATA[ESPÉCIE]]></text>
			</staticText>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="559" y="32" width="97" height="12">
					<printWhenExpression><![CDATA[$P{totalizar} == true]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Right">
					<font size="7"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{especieVlr}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="1" y="44" width="98" height="12">
					<printWhenExpression><![CDATA[$P{competencia} == true]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font size="7"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{periodo}]]></textFieldExpression>
			</textField>
		</band>
	</summary>
	<noData>
		<band height="51">
			<staticText>
				<reportElement x="1" y="13" width="1037" height="20"/>
				<textElement textAlignment="Center">
					<font size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[Não há dados para serem exibidos ! verifique os parâmetros informados.]]></text>
			</staticText>
		</band>
	</noData>
</jasperReport>

<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="CartoesRecebiveis" pageWidth="625" pageHeight="100" columnWidth="625" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" whenResourceMissingType="Empty">
	<property name="ireport.zoom" value="1.9965000000000244"/>
	<property name="ireport.x" value="101"/>
	<property name="ireport.y" value="0"/>
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.encoding" value="ISO-8859-1"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="java.util.*"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<parameter name="tituloRelatorio" class="java.lang.String" isForPrompting="false"/>
	<parameter name="nomeEmpresa" class="java.lang.String" isForPrompting="false"/>
	<parameter name="versaoSoftware" class="java.lang.String" isForPrompting="false"/>
	<parameter name="usuario" class="java.lang.String" isForPrompting="false"/>
	<parameter name="filtros" class="java.lang.String" isForPrompting="false"/>
	<parameter name="SUBREPORT_DIR" class="java.lang.String">
		<defaultValueExpression><![CDATA["E:\\desenvolvimentosv\\Projeto Pacto Solucoes\\ZillyonWeb\\src\\java\\relatorio\\designRelatorio\\financeiro\\"]]></defaultValueExpression>
	</parameter>
	<parameter name="SUBREPORT_DIR1" class="java.lang.String">
		<defaultValueExpression><![CDATA["E:\\desenvolvimentosv\\Projeto Pacto Solucoes\\ZillyonWeb\\src\\java\\relatorio\\designRelatorio\\financeiro\\"]]></defaultValueExpression>
	</parameter>
	<parameter name="logoPadraoRelatorio" class="java.io.InputStream" isForPrompting="false"/>
	<parameter name="SUBREPORT_DIR2" class="java.lang.String">
		<defaultValueExpression><![CDATA["E:\\desenvolvimentosv\\Projeto Pacto Solucoes\\ZillyonWeb\\src\\java\\relatorio\\designRelatorio\\financeiro\\"]]></defaultValueExpression>
	</parameter>
	<parameter name="dataFim" class="java.lang.String"/>
	<parameter name="dataIni" class="java.lang.String"/>
	<parameter name="moeda" class="java.lang.String">
		<defaultValueExpression><![CDATA["R$"]]></defaultValueExpression>
	</parameter>
	<field name="nomePagador" class="java.lang.String"/>
	<field name="numeroLoteApresentar" class="java.lang.String"/>
	<field name="valorApresentar" class="java.lang.String"/>
	<field name="dataCompensacaoApresentar" class="java.lang.String"/>
	<field name="dataLancamentoApresentar" class="java.lang.String"/>
	<field name="autorizacao" class="java.lang.String"/>
	<field name="operadora" class="java.lang.String"/>
	<field name="matricula" class="java.lang.String"/>
	<field name="nsu" class="java.lang.String"/>
	<columnHeader>
		<band height="13">
			<staticText>
				<reportElement x="45" y="0" width="114" height="13"/>
				<textElement>
					<font size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Nome Pagador]]></text>
			</staticText>
			<staticText>
				<reportElement x="319" y="0" width="61" height="13"/>
				<textElement>
					<font size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Lanç.]]></text>
			</staticText>
			<staticText>
				<reportElement x="386" y="0" width="46" height="13"/>
				<textElement>
					<font size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Comp.]]></text>
			</staticText>
			<staticText>
				<reportElement x="461" y="0" width="34" height="13"/>
				<textElement textAlignment="Right">
					<font size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Valor]]></text>
			</staticText>
			<staticText>
				<reportElement x="565" y="0" width="29" height="13"/>
				<textElement>
					<font size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Lote]]></text>
			</staticText>
			<staticText>
				<reportElement x="216" y="0" width="100" height="13"/>
				<textElement>
					<font size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Operadora ]]></text>
			</staticText>
			<staticText>
				<reportElement x="513" y="0" width="52" height="13"/>
				<textElement>
					<font size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Autorização]]></text>
			</staticText>
			<line>
				<reportElement x="0" y="12" width="625" height="1"/>
			</line>
			<staticText>
				<reportElement x="1" y="0" width="35" height="13"/>
				<textElement>
					<font size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Mat.]]></text>
			</staticText>
			<staticText>
				<reportElement x="593" y="0" width="32" height="13"/>
				<textElement>
					<font size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[NSU]]></text>
			</staticText>
			<textField>
				<reportElement mode="Transparent" x="432" y="-1" width="29" height="13" forecolor="#000000" backcolor="#FFFFFF"/>
				<textElement textAlignment="Right" verticalAlignment="Top" rotation="None" lineSpacing="Single" markup="none">
					<font fontName="SansSerif" size="8" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica-Bold" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{moeda}]]></textFieldExpression>
			</textField>
		</band>
	</columnHeader>
	<detail>
		<band height="13">
			<textField isStretchWithOverflow="true">
				<reportElement stretchType="RelativeToBandHeight" x="319" y="0" width="61" height="10"/>
				<textElement>
					<font size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{dataLancamentoApresentar}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement stretchType="RelativeToBandHeight" x="386" y="0" width="46" height="10"/>
				<textElement>
					<font size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{dataCompensacaoApresentar}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement stretchType="RelativeToBandHeight" x="432" y="0" width="63" height="10"/>
				<textElement textAlignment="Right">
					<font size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{valorApresentar}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement stretchType="RelativeToBandHeight" x="513" y="0" width="52" height="10"/>
				<textElement>
					<font size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{autorizacao}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement stretchType="RelativeToBandHeight" x="45" y="0" width="168" height="10"/>
				<textElement>
					<font size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{nomePagador}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement stretchType="RelativeToBandHeight" x="216" y="0" width="100" height="10"/>
				<textElement>
					<font size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{operadora}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="1" y="0" width="43" height="10"/>
				<textElement>
					<font size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{matricula}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="">
				<reportElement x="565" y="0" width="29" height="10"/>
				<textElement textAlignment="Center">
					<font size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{numeroLoteApresentar}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="false">
				<reportElement x="593" y="0" width="32" height="10"/>
				<textElement textAlignment="Center">
					<font size="8" isStrikeThrough="false" isPdfEmbedded="false"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{nsu}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
</jasperReport>

<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="OutrosRecebiveis" pageWidth="625" pageHeight="100" columnWidth="625" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" whenResourceMissingType="Empty">
	<property name="ireport.zoom" value="3.0"/>
	<property name="ireport.x" value="749"/>
	<property name="ireport.y" value="0"/>
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.encoding" value="ISO-8859-1"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="java.util.*"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<parameter name="tituloRelatorio" class="java.lang.String" isForPrompting="false"/>
	<parameter name="nomeEmpresa" class="java.lang.String" isForPrompting="false"/>
	<parameter name="versaoSoftware" class="java.lang.String" isForPrompting="false"/>
	<parameter name="usuario" class="java.lang.String" isForPrompting="false"/>
	<parameter name="filtros" class="java.lang.String" isForPrompting="false"/>
	<parameter name="SUBREPORT_DIR" class="java.lang.String">
		<defaultValueExpression><![CDATA["E:\\desenvolvimentosv\\Projeto Pacto Solucoes\\ZillyonWeb\\src\\java\\relatorio\\designRelatorio\\financeiro\\"]]></defaultValueExpression>
	</parameter>
	<parameter name="SUBREPORT_DIR1" class="java.lang.String">
		<defaultValueExpression><![CDATA["E:\\desenvolvimentosv\\Projeto Pacto Solucoes\\ZillyonWeb\\src\\java\\relatorio\\designRelatorio\\financeiro\\"]]></defaultValueExpression>
	</parameter>
	<parameter name="logoPadraoRelatorio" class="java.io.InputStream" isForPrompting="false"/>
	<parameter name="SUBREPORT_DIR2" class="java.lang.String">
		<defaultValueExpression><![CDATA["E:\\desenvolvimentosv\\Projeto Pacto Solucoes\\ZillyonWeb\\src\\java\\relatorio\\designRelatorio\\financeiro\\"]]></defaultValueExpression>
	</parameter>
	<parameter name="dataFim" class="java.lang.String"/>
	<parameter name="dataIni" class="java.lang.String"/>
	<parameter name="exibirAutorizacao" class="java.lang.Boolean"/>
	<parameter name="exibirNSU" class="java.lang.Boolean"/>
	<parameter name="moeda" class="java.lang.String">
		<defaultValueExpression><![CDATA["R$"]]></defaultValueExpression>
	</parameter>
	<field name="nomePagador" class="java.lang.String"/>
	<field name="valorApresentar" class="java.lang.String"/>
	<field name="dataPagamentoSemHora_Apresentar" class="java.lang.String"/>
	<field name="dataLancamentoSemHora_Apresentar" class="java.lang.String"/>
	<field name="autorizacaoCartao" class="java.lang.String"/>
	<field name="matriculaPagador" class="java.lang.String"/>
	<field name="nomeOperadorCartaoApresentar" class="java.lang.String"/>
	<field name="nsu" class="java.lang.String"/>
	<columnHeader>
		<band height="13">
			<staticText>
				<reportElement x="53" y="0" width="136" height="13"/>
				<textElement>
					<font fontName="SansSerif" size="8" isBold="true" isItalic="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Nome Pagador]]></text>
			</staticText>
			<staticText>
				<reportElement x="275" y="0" width="70" height="13"/>
				<textElement>
					<font fontName="SansSerif" size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Lançamento]]></text>
			</staticText>
			<staticText>
				<reportElement x="345" y="0" width="70" height="13"/>
				<textElement>
					<font fontName="SansSerif" size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Compensação]]></text>
			</staticText>
			<staticText>
				<reportElement x="435" y="0" width="45" height="13"/>
				<textElement textAlignment="Right">
					<font fontName="SansSerif" size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Valor]]></text>
			</staticText>
			<staticText>
				<reportElement x="490" y="0" width="80" height="13">
					<printWhenExpression><![CDATA[$P{exibirAutorizacao}]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font fontName="SansSerif" size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Autorização]]></text>
			</staticText>
			<line>
				<reportElement x="0" y="12" width="625" height="1"/>
			</line>
			<staticText>
				<reportElement x="3" y="-1" width="35" height="13"/>
				<textElement>
					<font size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Mat.]]></text>
			</staticText>
			<staticText>
				<reportElement x="189" y="0" width="76" height="13"/>
				<textElement>
					<font fontName="SansSerif" size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Op.Cartão]]></text>
			</staticText>
			<staticText>
				<reportElement x="575" y="0" width="45" height="13">
					<printWhenExpression><![CDATA[$P{exibirNSU}]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[NSU]]></text>
			</staticText>
			<textField>
				<reportElement mode="Transparent" x="420" y="0" width="15" height="13" forecolor="#000000" backcolor="#FFFFFF"/>
				<textElement textAlignment="Right" verticalAlignment="Top" rotation="None" lineSpacing="Single" markup="none">
					<font fontName="SansSerif" size="8" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica-Bold" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{moeda}]]></textFieldExpression>
			</textField>
		</band>
	</columnHeader>
	<detail>
		<band height="13">
			<textField isStretchWithOverflow="true">
				<reportElement positionType="Float" stretchType="RelativeToBandHeight" x="275" y="0" width="70" height="10"/>
				<textElement>
					<font size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{dataLancamentoSemHora_Apresentar}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement positionType="Float" stretchType="RelativeToBandHeight" x="345" y="0" width="70" height="10"/>
				<textElement>
					<font size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{dataPagamentoSemHora_Apresentar}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement positionType="Float" stretchType="RelativeToBandHeight" x="420" y="0" width="60" height="10"/>
				<textElement textAlignment="Right">
					<font size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{valorApresentar}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement positionType="Float" stretchType="RelativeToBandHeight" x="490" y="0" width="80" height="10">
					<printWhenExpression><![CDATA[$P{exibirAutorizacao}]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{autorizacaoCartao}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement positionType="Float" stretchType="RelativeToBandHeight" x="52" y="0" width="137" height="10"/>
				<textElement>
					<font size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{nomePagador}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="3" y="0" width="41" height="10"/>
				<textElement>
					<font size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{matriculaPagador}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement positionType="Float" stretchType="RelativeToBandHeight" x="189" y="0" width="76" height="10"/>
				<textElement>
					<font size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{nomeOperadorCartaoApresentar}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="false">
				<reportElement x="575" y="0" width="45" height="10">
					<printWhenExpression><![CDATA[$P{exibirNSU}]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Center">
					<font size="8" isStrikeThrough="false" isPdfEmbedded="false"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{nsu}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
</jasperReport>

<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="SaldoContaCorrenteTodasEmpresasRel" pageWidth="680" pageHeight="878" columnWidth="625" leftMargin="19" rightMargin="36" topMargin="2" bottomMargin="2" whenResourceMissingType="Empty">
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.zoom" value="1.3310000000000004"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="java.util.*"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<subDataset name="Teste"/>
	<parameter name="logoPadraoRelatorio" class="java.io.InputStream" isForPrompting="false"/>
	<parameter name="tituloRelatorio" class="java.lang.String" isForPrompting="false"/>
	<parameter name="nomeEmpresa" class="java.lang.String" isForPrompting="false"/>
	<parameter name="versaoSoftware" class="java.lang.String" isForPrompting="false"/>
	<parameter name="usuario" class="java.lang.String" isForPrompting="false"/>
	<parameter name="filtros" class="java.lang.String" isForPrompting="false"/>
	<parameter name="SUBREPORT_DIR" class="java.lang.String">
		<defaultValueExpression><![CDATA["E:\\desenvolvimentosv\\Projeto Pacto Solucoes\\ZillyonWeb\\src\\java\\relatorio\\designRelatorio\\financeiro\\"]]></defaultValueExpression>
	</parameter>
	<parameter name="SUBREPORT_DIR1" class="java.lang.String">
		<defaultValueExpression><![CDATA["E:\\desenvolvimentosv\\Projeto Pacto Solucoes\\ZillyonWeb\\src\\java\\relatorio\\designRelatorio\\financeiro\\"]]></defaultValueExpression>
	</parameter>
	<parameter name="dataIni" class="java.lang.String" isForPrompting="false"/>
	<parameter name="dataFim" class="java.lang.String" isForPrompting="false"/>
	<parameter name="qtdAV" class="java.lang.String" isForPrompting="false"/>
	<parameter name="qtdCA" class="java.lang.String" isForPrompting="false"/>
	<parameter name="qtdChequeAV" class="java.lang.String" isForPrompting="false"/>
	<parameter name="qtdChequePR" class="java.lang.String" isForPrompting="false"/>
	<parameter name="qtdOutro" class="java.lang.String" isForPrompting="false"/>
	<parameter name="valorAV" class="java.lang.Double" isForPrompting="false"/>
	<parameter name="valorCA" class="java.lang.Double" isForPrompting="false"/>
	<parameter name="valorChequeAV" class="java.lang.Double" isForPrompting="false"/>
	<parameter name="valorChequePR" class="java.lang.Double" isForPrompting="false"/>
	<parameter name="valorOutro" class="java.lang.Double" isForPrompting="false"/>
	<field name="matricula" class="java.lang.String">
		<fieldDescription><![CDATA[SaldoContaCorrenteRel/registros+matricula]]></fieldDescription>
	</field>
	<field name="nome" class="java.lang.String">
		<fieldDescription><![CDATA[SaldoContaCorrenteRel/registros+nome]]></fieldDescription>
	</field>
	<field name="cliente_matricula" class="java.lang.String"/>
	<field name="saldoatual" class="java.lang.String">
		<fieldDescription><![CDATA[SaldoContaCorrenteRel/registros+saldoatual]]></fieldDescription>
	</field>
	<field name="empresa" class="java.lang.String">
		<fieldDescription><![CDATA[SaldoContaCorrenteRel/registros+empresa]]></fieldDescription>
	</field>
	<variable name="totalclientes" class="java.lang.Integer" calculation="Count">
		<variableExpression><![CDATA[$F{matricula}]]></variableExpression>
	</variable>
	<variable name="variavelpositivo" class="java.lang.Double">
		<variableExpression><![CDATA[new Double($F{saldoatual})]]></variableExpression>
	</variable>
	<variable name="positivo" class="java.lang.Integer" calculation="Sum">
		<variableExpression><![CDATA[$V{variavelpositivo}.doubleValue()>(0.0) ?new Integer(1)  : new Integer(0)]]></variableExpression>
	</variable>
	<variable name="negativo" class="java.lang.Integer" calculation="Sum">
		<variableExpression><![CDATA[$V{variavelpositivo}.doubleValue()>(0.0) ? new Integer(0) : new Integer(1)]]></variableExpression>
	</variable>
	<variable name="somapositivo" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[$V{variavelpositivo}.doubleValue()>(0.0) ?($V{variavelpositivo})  : new Double(0)]]></variableExpression>
	</variable>
	<variable name="somanegativo" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[$V{variavelpositivo}.doubleValue()>(0.0) ?  new Double(0) :($V{variavelpositivo})]]></variableExpression>
	</variable>
	<variable name="saldoatual" class="java.lang.Double">
		<variableExpression><![CDATA[new Double($F{saldoatual})]]></variableExpression>
	</variable>
	<group name="nomeCliente">
		<groupExpression><![CDATA[$F{matricula}]]></groupExpression>
		<groupHeader>
			<band height="15" splitType="Stretch">
				<textField isBlankWhenNull="false">
					<reportElement key="textField-223" x="0" y="1" width="88" height="14"/>
					<textElement/>
					<textFieldExpression class="java.lang.String"><![CDATA[$F{matricula}]]></textFieldExpression>
				</textField>
				<textField pattern="#,##0.00" isBlankWhenNull="false">
					<reportElement key="textField" x="495" y="1" width="127" height="14"/>
					<textElement/>
					<textFieldExpression class="java.lang.Double"><![CDATA[new Double ($F{saldoatual})]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="false">
					<reportElement key="textField-224" x="102" y="1" width="183" height="14"/>
					<textElement/>
					<textFieldExpression class="java.lang.String"><![CDATA[$F{nome}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="300" y="0" width="181" height="15"/>
					<textElement/>
					<textFieldExpression class="java.lang.String"><![CDATA[$F{empresa}]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
		<groupFooter>
			<band splitType="Stretch"/>
		</groupFooter>
	</group>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band splitType="Stretch"/>
	</title>
	<pageHeader>
		<band height="69" splitType="Stretch">
			<image isUsingCache="true" onErrorType="Blank" evaluationTime="Page">
				<reportElement key="image-1" x="1" y="1" width="82" height="36" isPrintWhenDetailOverflows="true"/>
				<box>
					<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<imageExpression class="java.io.InputStream"><![CDATA[$P{logoPadraoRelatorio}]]></imageExpression>
			</image>
			<textField pattern="dd/MM/yyyy HH.mm.ss" isBlankWhenNull="false">
				<reportElement key="dataRel-1" mode="Opaque" x="520" y="1" width="105" height="19" backcolor="#FFFFFF"/>
				<box bottomPadding="2">
					<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Verdana" size="8" isItalic="false" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression class="java.util.Date"><![CDATA[new Date()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-2" x="83" y="1" width="437" height="19"/>
				<box>
					<pen lineWidth="0.5" lineStyle="Solid"/>
					<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid"/>
				</box>
				<textElement textAlignment="Center">
					<font fontName="Arial" size="14" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{tituloRelatorio}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-25" x="520" y="20" width="60" height="17"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right">
					<font fontName="Arial"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA["Pág: " + $V{PAGE_NUMBER} + " de "]]></textFieldExpression>
			</textField>
			<textField evaluationTime="Report" isBlankWhenNull="false">
				<reportElement key="textField-26" x="580" y="20" width="45" height="17"/>
				<box bottomPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement>
					<font fontName="Arial"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[" " + $V{PAGE_NUMBER} + ""]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-216" x="82" y="20" width="438" height="37"/>
				<box>
					<leftPen lineWidth="0.5" lineStyle="Solid"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.5" lineStyle="Solid"/>
				</box>
				<textElement textAlignment="Center">
					<font fontName="Arial" size="14" isBold="true" isItalic="true" pdfFontName="Helvetica-BoldOblique"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{filtros}]]></textFieldExpression>
			</textField>
		</band>
	</pageHeader>
	<columnHeader>
		<band height="31" splitType="Stretch">
			<line>
				<reportElement key="line-1" x="1" y="2" width="624" height="1"/>
			</line>
			<staticText>
				<reportElement key="staticText-1" x="102" y="7" width="183" height="14"/>
				<textElement>
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Nome]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-2" x="0" y="7" width="88" height="14"/>
				<textElement>
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Mat. Cliente]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-8" x="495" y="7" width="127" height="14"/>
				<textElement>
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Saldo Atual]]></text>
			</staticText>
			<line>
				<reportElement key="line-7" x="1" y="23" width="624" height="1"/>
			</line>
			<staticText>
				<reportElement key="staticText-1" x="300" y="7" width="181" height="14"/>
				<textElement>
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Empresa]]></text>
			</staticText>
		</band>
	</columnHeader>
	<detail>
		<band splitType="Stretch"/>
	</detail>
	<columnFooter>
		<band splitType="Stretch"/>
	</columnFooter>
	<pageFooter>
		<band splitType="Stretch"/>
	</pageFooter>
	<lastPageFooter>
		<band height="75" splitType="Stretch">
			<staticText>
				<reportElement key="staticText-3" x="0" y="13" width="86" height="14"/>
				<textElement>
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Valor Positivo]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-4" x="0" y="32" width="86" height="14"/>
				<textElement>
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Valor Negativo]]></text>
			</staticText>
			<textField pattern="" isBlankWhenNull="false">
				<reportElement key="textField-207" x="1" y="59" width="621" height="13"/>
				<box>
					<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="8" isItalic="true" pdfFontName="Helvetica-Oblique"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[" "+" Usuário:" + $P{usuario}]]></textFieldExpression>
			</textField>
			<textField pattern="###0.00" isBlankWhenNull="false">
				<reportElement key="textField-226" x="86" y="13" width="88" height="14"/>
				<textElement/>
				<textFieldExpression class="java.lang.Double"><![CDATA[$V{somapositivo}]]></textFieldExpression>
			</textField>
			<textField pattern="###0.00" isBlankWhenNull="false">
				<reportElement key="textField-227" x="86" y="32" width="88" height="14"/>
				<textElement/>
				<textFieldExpression class="java.lang.Double"><![CDATA[$V{somanegativo}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement key="staticText-9" x="285" y="13" width="86" height="14"/>
				<textElement>
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Nº Clientes]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-10" x="285" y="31" width="86" height="14"/>
				<textElement>
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Nº Clientes]]></text>
			</staticText>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-228" x="371" y="13" width="88" height="14"/>
				<textElement/>
				<textFieldExpression class="java.lang.Integer"><![CDATA[$V{positivo}]]></textFieldExpression>
			</textField>
			<line>
				<reportElement key="line-4" x="1" y="11" width="624" height="1"/>
			</line>
			<line>
				<reportElement key="line-5" x="1" y="49" width="624" height="1"/>
			</line>
			<line>
				<reportElement key="line-6" x="1" y="51" width="624" height="1"/>
			</line>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-229" x="371" y="31" width="88" height="14"/>
				<textElement/>
				<textFieldExpression class="java.lang.Integer"><![CDATA[$V{negativo}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement key="staticText-11" x="505" y="13" width="84" height="14"/>
				<textElement>
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Total Clientes]]></text>
			</staticText>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-230" x="590" y="13" width="35" height="14"/>
				<textElement/>
				<textFieldExpression class="java.lang.Integer"><![CDATA[$V{totalclientes}]]></textFieldExpression>
			</textField>
		</band>
	</lastPageFooter>
	<summary>
		<band splitType="Stretch"/>
	</summary>
</jasperReport>

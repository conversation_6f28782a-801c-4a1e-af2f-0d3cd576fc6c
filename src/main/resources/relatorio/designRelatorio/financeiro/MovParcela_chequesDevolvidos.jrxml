<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="MovParcela_chequesDevolvidos" language="groovy" pageWidth="615" pageHeight="555" orientation="Landscape" columnWidth="615" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0">
	<property name="ireport.zoom" value="2.0"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<parameter name="moeda" class="java.lang.String"/>
	<field name="numero" class="java.lang.String"/>
	<field name="agencia" class="java.lang.String"/>
	<field name="nomeNoCheque" class="java.lang.String"/>
	<field name="valor" class="java.lang.Double"/>
	<field name="conta" class="java.lang.String"/>
	<field name="numeroBanco" class="java.lang.String"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<columnHeader>
		<band height="16">
			<textField>
				<reportElement x="18" y="0" width="100" height="14"/>
				<textElement markup="none"/>
				<textFieldExpression class="java.lang.String"><![CDATA[$R{Cheques_Devolvidos}]]></textFieldExpression>
			</textField>
		</band>
	</columnHeader>
	<detail>
		<band height="14" splitType="Stretch">
			<textField pattern="#,##0.00" isBlankWhenNull="false">
				<reportElement key="textField-1" mode="Opaque" x="494" y="0" width="35" height="12"/>
				<box>
					<pen lineWidth="0.0" lineColor="#999999"/>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Double" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Double" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="8" isBold="true" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression class="java.lang.Double"><![CDATA[$F{valor}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-2" mode="Opaque" x="420" y="0" width="56" height="12"/>
				<box>
					<pen lineWidth="0.0" lineColor="#999999"/>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Double" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Double" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="8" isBold="true" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{numero}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement key="staticText-3" x="476" y="0" width="12" height="12"/>
				<textElement verticalAlignment="Middle" markup="none">
					<font fontName="Microsoft Sans Serif" size="8" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{moeda}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement key="staticText-2" x="389" y="0" width="31" height="12"/>
				<textElement verticalAlignment="Middle" markup="none">
					<font fontName="Microsoft Sans Serif" size="8" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$R{Numero}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-5" mode="Opaque" x="335" y="0" width="54" height="12"/>
				<box>
					<pen lineWidth="0.0" lineColor="#999999"/>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Double" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Double" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="8" isBold="true" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{conta}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="36" y="0" width="16" height="12"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="8"/>
				</textElement>
				<text><![CDATA[NT:]]></text>
			</staticText>
			<textField isStretchWithOverflow="true">
				<reportElement x="52" y="0" width="103" height="12" isPrintWhenDetailOverflows="true"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="8" isBold="true" isItalic="false"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{nomeNoCheque}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement key="staticText-5" x="249" y="0" width="32" height="12"/>
				<textElement verticalAlignment="Middle" markup="none">
					<font fontName="Microsoft Sans Serif" size="8" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$R{Agencia}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-4" mode="Opaque" x="281" y="0" width="30" height="12"/>
				<box>
					<pen lineWidth="0.0" lineColor="#999999"/>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Double" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Double" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="8" isBold="true" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{agencia}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement key="staticText-6" x="311" y="0" width="24" height="12"/>
				<textElement verticalAlignment="Middle" markup="none">
					<font fontName="Microsoft Sans Serif" size="8" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$R{Conta}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement key="staticText-5" x="155" y="0" width="11" height="12"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="8" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[B.:]]></text>
			</staticText>
			<textField isStretchWithOverflow="true">
				<reportElement x="166" y="0" width="83" height="12" isPrintWhenDetailOverflows="true"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="8" isBold="true" isItalic="false"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{numeroBanco}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
</jasperReport>

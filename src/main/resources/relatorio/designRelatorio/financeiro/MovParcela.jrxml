<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="MovProduto" pageWidth="615" pageHeight="535" orientation="Landscape" columnWidth="615" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0">
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.encoding" value="UTF-8"/>
	<property name="ireport.zoom" value="2.0"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="java.util.*"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<parameter name="SUBREPORT_DIR" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA["C:\\PactoJ\\Desenvolvimento\\Sistemas\\ZillyonWeb\\tronco-novo\\src\\main\\resources\\relatorio\\designRelatorio\\financeiro\\"]]></defaultValueExpression>
	</parameter>
	<parameter name="moeda" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA["R$"]]></defaultValueExpression>
	</parameter>
	<field name="descricao" class="java.lang.String"/>
	<field name="valorParcela" class="java.lang.Double"/>
	<field name="contrato.codigo" class="java.lang.Integer"/>
	<field name="movParcelaDetalhamentoVO" class="java.lang.Object"/>
	<field name="movParcelaDetalhamentoVO.cheques" class="java.lang.Object"/>
	<field name="dsParcelasRenegociadas" class="java.lang.Object"/>
	<pageHeader>
		<band height="14">
			<textField>
				<reportElement x="2" y="1" width="100" height="12"/>
				<textElement markup="none">
					<font fontName="Microsoft Sans Serif" size="9" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$R{Parcelas_Recibo}]]></textFieldExpression>
			</textField>
		</band>
	</pageHeader>
	<detail>
		<band height="14" splitType="Stretch">
			<textField isBlankWhenNull="false">
				<reportElement key="textField-1" x="40" y="0" width="500" height="14"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="9"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[" "+$F{descricao}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-6" x="0" y="0" width="38" height="14">
					<printWhenExpression><![CDATA[$F{contrato.codigo} > 0]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="9"/>
				</textElement>
				<textFieldExpression class="java.lang.Integer"><![CDATA[$F{contrato.codigo}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00" isBlankWhenNull="false">
				<reportElement key="textField-5" x="565" y="0" width="50" height="14"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="9"/>
				</textElement>
				<textFieldExpression class="java.lang.Double"><![CDATA[$F{valorParcela}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="540" y="0" width="25" height="14"/>
				<textElement>
					<font size="9"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{moeda}]]></textFieldExpression>
			</textField>
		</band>
		<band height="30">
			<subreport>
				<reportElement x="2" y="0" width="613" height="30"/>
				<subreportParameter name="REPORT_RESOURCE_BUNDLE">
					<subreportParameterExpression><![CDATA[$P{REPORT_RESOURCE_BUNDLE}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="moeda2">
					<subreportParameterExpression><![CDATA[$P{moeda}]]></subreportParameterExpression>
				</subreportParameter>
				<dataSourceExpression><![CDATA[$F{dsParcelasRenegociadas}]]></dataSourceExpression>
				<subreportExpression class="java.lang.String"><![CDATA[$P{SUBREPORT_DIR} + "MovParcela_Renegociadas.jasper"]]></subreportExpression>
			</subreport>
		</band>
		<band height="30">
			<printWhenExpression><![CDATA[!$F{movParcelaDetalhamentoVO}.equals( null )]]></printWhenExpression>
			<subreport>
				<reportElement x="2" y="0" width="613" height="30"/>
				<subreportParameter name="REPORT_RESOURCE_BUNDLE">
					<subreportParameterExpression><![CDATA[$P{REPORT_RESOURCE_BUNDLE}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="moeda">
					<subreportParameterExpression><![CDATA[$P{moeda}]]></subreportParameterExpression>
				</subreportParameter>
				<dataSourceExpression><![CDATA[$F{movParcelaDetalhamentoVO.cheques}]]></dataSourceExpression>
				<subreportExpression class="java.lang.String"><![CDATA[$P{SUBREPORT_DIR} + "MovParcela_chequesDevolvidos.jasper"]]></subreportExpression>
			</subreport>
		</band>
	</detail>
</jasperReport>

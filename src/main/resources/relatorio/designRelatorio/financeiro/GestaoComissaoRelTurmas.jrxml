<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="ParcelaEmAbertoRel" pageWidth="576" pageHeight="878" columnWidth="576" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" whenResourceMissingType="Empty">
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.encoding" value="ISO-8859-1"/>
	<property name="ireport.zoom" value="2.593742460100007"/>
	<property name="ireport.x" value="115"/>
	<property name="ireport.y" value="0"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="java.util.*"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<style name="Crosstab Data Text" hAlign="Center"/>
	<style name="table">
		<box>
			<pen lineWidth="1.0" lineColor="#000000"/>
		</box>
	</style>
	<style name="table_TH" mode="Opaque" backcolor="#FFFAFA">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="table_CH" mode="Opaque" backcolor="#FFBFBF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="table_TD" mode="Opaque" backcolor="#FFFFFF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<parameter name="logoPadraoRelatorio" class="java.io.InputStream" isForPrompting="false"/>
	<parameter name="tituloRelatorio" class="java.lang.String" isForPrompting="false"/>
	<parameter name="nomeEmpresa" class="java.lang.String" isForPrompting="false"/>
	<parameter name="versaoSoftware" class="java.lang.String" isForPrompting="false"/>
	<parameter name="usuario" class="java.lang.String" isForPrompting="false"/>
	<parameter name="filtros" class="java.lang.String" isForPrompting="false"/>
	<parameter name="SUBREPORT_DIR" class="java.lang.String">
		<defaultValueExpression><![CDATA["E:\\desenvolvimentosv\\Projeto Pacto Solucoes\\ZillyonWeb\\src\\java\\relatorio\\designRelatorio\\financeiro\\"]]></defaultValueExpression>
	</parameter>
	<parameter name="SUBREPORT_DIR1" class="java.lang.String">
		<defaultValueExpression><![CDATA["E:\\desenvolvimentosv\\Projeto Pacto Solucoes\\ZillyonWeb\\src\\java\\relatorio\\designRelatorio\\financeiro\\"]]></defaultValueExpression>
	</parameter>
	<parameter name="dataIni" class="java.lang.String" isForPrompting="false"/>
	<parameter name="dataFim" class="java.lang.String" isForPrompting="false"/>
	<parameter name="qtdAV" class="java.lang.String" isForPrompting="false"/>
	<parameter name="qtdCA" class="java.lang.String" isForPrompting="false"/>
	<parameter name="qtdChequeAV" class="java.lang.String" isForPrompting="false"/>
	<parameter name="qtdChequePR" class="java.lang.String" isForPrompting="false"/>
	<parameter name="qtdOutro" class="java.lang.String" isForPrompting="false"/>
	<parameter name="valorAV" class="java.lang.Double" isForPrompting="false"/>
	<parameter name="valorCA" class="java.lang.Double" isForPrompting="false"/>
	<parameter name="valorChequeAV" class="java.lang.Double" isForPrompting="false"/>
	<parameter name="valorChequePR" class="java.lang.Double" isForPrompting="false"/>
	<parameter name="valorOutro" class="java.lang.Double" isForPrompting="false"/>
	<parameter name="parametro1" class="java.lang.String" isForPrompting="false"/>
	<parameter name="parametro2" class="java.lang.String" isForPrompting="false"/>
	<parameter name="parametro3" class="java.lang.String" isForPrompting="false"/>
	<parameter name="parametro4" class="java.lang.String" isForPrompting="false"/>
	<parameter name="parametro5" class="java.lang.String" isForPrompting="false"/>
	<parameter name="parametro6" class="java.lang.String" isForPrompting="false"/>
	<field name="contrato.codigo" class="java.lang.Integer"/>
	<field name="horarioTurma.diaSemana" class="java.lang.String"/>
	<field name="horarioTurma.horaInicial" class="java.lang.String"/>
	<field name="horarioTurma.horaFinal" class="java.lang.String"/>
	<field name="horarioTurma.identificadorTurma" class="java.lang.String"/>
	<field name="horarioTurma.professor.pessoa.nome" class="java.lang.String"/>
	<field name="horarioTurma.professor.porcComissao_Apresentar" class="java.lang.String"/>
	<field name="dataInicio_Apresentar" class="java.lang.String"/>
	<field name="dataFim_Apresentar" class="java.lang.String"/>
	<field name="valorComissao_Apresentar" class="java.lang.String"/>
	<columnHeader>
		<band height="19">
			<staticText>
				<reportElement key="staticText-85" positionType="Float" mode="Opaque" x="0" y="0" width="41" height="13"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="SansSerif" size="9" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Contrato]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-85" positionType="Float" mode="Opaque" x="41" y="0" width="23" height="13"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="SansSerif" size="9" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Dia]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-85" positionType="Float" mode="Opaque" x="69" y="1" width="61" height="13"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="SansSerif" size="9" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Horário]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-85" positionType="Float" mode="Opaque" x="150" y="0" width="61" height="13"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="SansSerif" size="9" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Nome Turma]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-85" positionType="Float" mode="Opaque" x="214" y="0" width="61" height="13"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="SansSerif" size="9" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Professor]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-85" positionType="Float" mode="Opaque" x="338" y="0" width="46" height="13"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="SansSerif" size="9" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Comissão]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-85" positionType="Float" mode="Opaque" x="386" y="0" width="48" height="13"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="SansSerif" size="9" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Inic. Mat.]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-85" positionType="Float" mode="Opaque" x="451" y="0" width="61" height="13"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="SansSerif" size="9" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Fim Mat.]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-85" positionType="Float" mode="Opaque" x="514" y="0" width="62" height="13"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="SansSerif" size="9" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Vlr Comissão]]></text>
			</staticText>
			<line>
				<reportElement x="0" y="14" width="576" height="1"/>
			</line>
		</band>
	</columnHeader>
	<detail>
		<band height="23" splitType="Stretch">
			<textField>
				<reportElement x="0" y="0" width="41" height="20"/>
				<textElement/>
				<textFieldExpression class="java.lang.Integer"><![CDATA[$F{contrato.codigo}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="41" y="0" width="28" height="20"/>
				<textElement/>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{horarioTurma.diaSemana}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="69" y="0" width="81" height="20"/>
				<textElement/>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{horarioTurma.horaInicial} +"/"+$F{horarioTurma.horaFinal}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="150" y="0" width="61" height="20"/>
				<textElement/>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{horarioTurma.identificadorTurma}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="212" y="0" width="126" height="20"/>
				<textElement/>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{horarioTurma.professor.pessoa.nome}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="339" y="0" width="46" height="20"/>
				<textElement/>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{horarioTurma.professor.porcComissao_Apresentar}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="386" y="0" width="64" height="20"/>
				<textElement/>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{dataInicio_Apresentar}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="450" y="0" width="61" height="20"/>
				<textElement/>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{dataFim_Apresentar}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="511" y="0" width="65" height="20"/>
				<textElement/>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{valorComissao_Apresentar}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
</jasperReport>

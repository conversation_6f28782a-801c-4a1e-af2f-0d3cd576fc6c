<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="MovPagamento_cartoes" pageWidth="470" pageHeight="802" whenNoDataType="AllSectionsNoDetail" columnWidth="470" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0">
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.encoding" value="UTF-8"/>
	<property name="ireport.zoom" value="3.0"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="java.util.*"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<field name="valor" class="java.lang.Double"/>
	<field name="dataCompensacao_Apresentar" class="java.lang.String"/>
	<field name="movpagamento.nomePagador" class="java.lang.String"/>
	<field name="operadora.descricao" class="java.lang.String"/>
	<pageHeader>
		<band splitType="Stretch"/>
	</pageHeader>
	<columnHeader>
		<band splitType="Stretch"/>
	</columnHeader>
	<detail>
		<band height="12" splitType="Prevent">
			<textField pattern="#,##0.00" isBlankWhenNull="false">
				<reportElement key="textField-1" mode="Opaque" x="435" y="0" width="35" height="12"/>
				<box>
					<pen lineWidth="0.0" lineColor="#999999"/>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Double" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Double" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="8" isBold="true" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression class="java.lang.Double"><![CDATA[$F{valor}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement key="staticText-7" x="302" y="0" width="17" height="12"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="8" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Dtc:]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-3" x="423" y="0" width="12" height="12"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="8" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[R$]]></text>
			</staticText>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-3" mode="Opaque" x="12" y="0" width="158" height="12"/>
				<box>
					<pen lineWidth="0.0" lineColor="#999999"/>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Double" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Double" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="8" isBold="true" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{movpagamento.nomePagador}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-4" mode="Opaque" x="185" y="0" width="100" height="12"/>
				<box>
					<pen lineWidth="0.0" lineColor="#999999"/>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Double" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Double" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="8" isBold="true" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{operadora.descricao}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement key="staticText-5" x="170" y="0" width="15" height="12"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="8" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Op:]]></text>
			</staticText>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-6" mode="Opaque" x="319" y="0" width="104" height="12"/>
				<box>
					<pen lineWidth="0.0" lineColor="#999999"/>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Double" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Double" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="8" isBold="true" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{dataCompensacao_Apresentar}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement key="staticText-4" x="0" y="0" width="12" height="12"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="8" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[P:]]></text>
			</staticText>
		</band>
	</detail>
	<columnFooter>
		<band splitType="Stretch"/>
	</columnFooter>
	<pageFooter>
		<band splitType="Stretch"/>
	</pageFooter>
	<summary>
		<band splitType="Stretch"/>
	</summary>
</jasperReport>

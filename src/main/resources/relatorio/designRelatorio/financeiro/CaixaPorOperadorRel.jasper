¬í sr (net.sf.jasperreports.engine.JasperReport      'Ø L compileDatat Ljava/io/Serializable;L compileNameSuffixt Ljava/lang/String;L 
compilerClassq ~ xr -net.sf.jasperreports.engine.base.JRBaseReport      'Ø +I PSEUDO_SERIAL_VERSION_UIDI bottomMarginI columnCountI 
columnSpacingI columnWidthZ ignorePaginationZ isFloatColumnFooterZ isSummaryNewPageZ  isSummaryWithPageHeaderAndFooterZ isTitleNewPageI 
leftMarginB orientationI 
pageHeightI 	pageWidthB 
printOrderI rightMarginI 	topMarginB whenNoDataTypeL 
backgroundt $Lnet/sf/jasperreports/engine/JRBand;L columnFooterq ~ L columnHeaderq ~ [ datasetst ([Lnet/sf/jasperreports/engine/JRDataset;L defaultFontt *Lnet/sf/jasperreports/engine/JRReportFont;L defaultStylet %Lnet/sf/jasperreports/engine/JRStyle;L detailq ~ L 
detailSectiont 'Lnet/sf/jasperreports/engine/JRSection;[ fontst +[Lnet/sf/jasperreports/engine/JRReportFont;L formatFactoryClassq ~ L 
importsSett Ljava/util/Set;L languageq ~ L lastPageFooterq ~ L mainDatasett 'Lnet/sf/jasperreports/engine/JRDataset;L nameq ~ L noDataq ~ L orientationValuet 2Lnet/sf/jasperreports/engine/type/OrientationEnum;L 
pageFooterq ~ L 
pageHeaderq ~ L printOrderValuet 1Lnet/sf/jasperreports/engine/type/PrintOrderEnum;[ stylest &[Lnet/sf/jasperreports/engine/JRStyle;L summaryq ~ [ 	templatest /[Lnet/sf/jasperreports/engine/JRReportTemplate;L titleq ~ L whenNoDataTypeValuet 5Lnet/sf/jasperreports/engine/type/WhenNoDataTypeEnum;xp  wî            q           n  ¨    #    pppur ([Lnet.sf.jasperreports.engine.JRDataset;L6Í¬D  xp   sr .net.sf.jasperreports.engine.base.JRBaseDataset      'Ø I PSEUDO_SERIAL_VERSION_UIDZ isMainB whenResourceMissingType[ fieldst &[Lnet/sf/jasperreports/engine/JRField;L filterExpressiont *Lnet/sf/jasperreports/engine/JRExpression;[ groupst &[Lnet/sf/jasperreports/engine/JRGroup;L nameq ~ [ 
parameterst *[Lnet/sf/jasperreports/engine/JRParameter;L 
propertiesMapt -Lnet/sf/jasperreports/engine/JRPropertiesMap;L queryt %Lnet/sf/jasperreports/engine/JRQuery;L resourceBundleq ~ L scriptletClassq ~ [ 
scriptletst *[Lnet/sf/jasperreports/engine/JRScriptlet;[ 
sortFieldst *[Lnet/sf/jasperreports/engine/JRSortField;[ 	variablest )[Lnet/sf/jasperreports/engine/JRVariable;L whenResourceMissingTypeValuet >Lnet/sf/jasperreports/engine/type/WhenResourceMissingTypeEnum;xp  wî  pppt Testeur *[Lnet.sf.jasperreports.engine.JRParameter;" *Ã`!  xp   sr 0net.sf.jasperreports.engine.base.JRBaseParameter      'Ø 	Z isForPromptingZ isSystemDefinedL defaultValueExpressionq ~ L descriptionq ~ L nameq ~ L nestedTypeNameq ~ L 
propertiesMapq ~ L valueClassNameq ~ L valueClassRealNameq ~ xpppt REPORT_PARAMETERS_MAPpsr +net.sf.jasperreports.engine.JRPropertiesMap      'Ø L baseq ~ L propertiesListt Ljava/util/List;L 
propertiesMapt Ljava/util/Map;xppppt 
java.util.Mappsq ~ #ppt 
JASPER_REPORTpsq ~ &pppt (net.sf.jasperreports.engine.JasperReportpsq ~ #ppt REPORT_CONNECTIONpsq ~ &pppt java.sql.Connectionpsq ~ #ppt REPORT_MAX_COUNTpsq ~ &pppt java.lang.Integerpsq ~ #ppt REPORT_DATA_SOURCEpsq ~ &pppt (net.sf.jasperreports.engine.JRDataSourcepsq ~ #ppt REPORT_SCRIPTLETpsq ~ &pppt /net.sf.jasperreports.engine.JRAbstractScriptletpsq ~ #ppt 
REPORT_LOCALEpsq ~ &pppt java.util.Localepsq ~ #ppt REPORT_RESOURCE_BUNDLEpsq ~ &pppt java.util.ResourceBundlepsq ~ #ppt REPORT_TIME_ZONEpsq ~ &pppt java.util.TimeZonepsq ~ #ppt REPORT_FORMAT_FACTORYpsq ~ &pppt .net.sf.jasperreports.engine.util.FormatFactorypsq ~ #ppt REPORT_CLASS_LOADERpsq ~ &pppt java.lang.ClassLoaderpsq ~ #ppt REPORT_URL_HANDLER_FACTORYpsq ~ &pppt  java.net.URLStreamHandlerFactorypsq ~ #ppt REPORT_FILE_RESOLVERpsq ~ &pppt -net.sf.jasperreports.engine.util.FileResolverpsq ~ #ppt REPORT_TEMPLATESpsq ~ &pppt java.util.Collectionpsq ~ &ppppppppur )[Lnet.sf.jasperreports.engine.JRVariable;bæ|,·D  xp   sr /net.sf.jasperreports.engine.base.JRBaseVariable      'Ø I PSEUDO_SERIAL_VERSION_UIDB calculationB 
incrementTypeZ isSystemDefinedB 	resetTypeL calculationValuet 2Lnet/sf/jasperreports/engine/type/CalculationEnum;L 
expressionq ~ L incrementGroupt %Lnet/sf/jasperreports/engine/JRGroup;L incrementTypeValuet 4Lnet/sf/jasperreports/engine/type/IncrementTypeEnum;L incrementerFactoryClassNameq ~ L incrementerFactoryClassRealNameq ~ L initialValueExpressionq ~ L nameq ~ L 
resetGroupq ~ dL resetTypeValuet 0Lnet/sf/jasperreports/engine/type/ResetTypeEnum;L valueClassNameq ~ L valueClassRealNameq ~ xp  wî   ~r 0net.sf.jasperreports.engine.type.CalculationEnum          xr java.lang.Enum          xpt SYSTEMpp~r 2net.sf.jasperreports.engine.type.IncrementTypeEnum          xq ~ it NONEppsr 1net.sf.jasperreports.engine.base.JRBaseExpression      'Ø I id[ chunkst 0[Lnet/sf/jasperreports/engine/JRExpressionChunk;L valueClassNameq ~ L valueClassRealNameq ~ xp    ur 0[Lnet.sf.jasperreports.engine.JRExpressionChunk;mYÏÞiK£U  xp   sr 6net.sf.jasperreports.engine.base.JRBaseExpressionChunk      'Ø B typeL textq ~ xpt new java.lang.Integer(1)q ~ 6pt PAGE_NUMBERp~r .net.sf.jasperreports.engine.type.ResetTypeEnum          xq ~ it REPORTq ~ 6psq ~ b  wî   q ~ jppq ~ mppsq ~ o   uq ~ r   sq ~ tt new java.lang.Integer(1)q ~ 6pt 
COLUMN_NUMBERp~q ~ xt PAGEq ~ 6psq ~ b  wî   ~q ~ ht COUNTsq ~ o   uq ~ r   sq ~ tt new java.lang.Integer(1)q ~ 6ppq ~ mppsq ~ o   uq ~ r   sq ~ tt new java.lang.Integer(0)q ~ 6pt REPORT_COUNTpq ~ yq ~ 6psq ~ b  wî   q ~ sq ~ o   uq ~ r   sq ~ tt new java.lang.Integer(1)q ~ 6ppq ~ mppsq ~ o   uq ~ r   sq ~ tt new java.lang.Integer(0)q ~ 6pt 
PAGE_COUNTpq ~ q ~ 6psq ~ b  wî   q ~ sq ~ o   uq ~ r   sq ~ tt new java.lang.Integer(1)q ~ 6ppq ~ mppsq ~ o   uq ~ r   sq ~ tt new java.lang.Integer(0)q ~ 6pt COLUMN_COUNTp~q ~ xt COLUMNq ~ 6p~r <net.sf.jasperreports.engine.type.WhenResourceMissingTypeEnum          xq ~ it NULLpppsr .net.sf.jasperreports.engine.base.JRBaseSection      'Ø [ bandst %[Lnet/sf/jasperreports/engine/JRBand;xppppsr java.util.HashSetºD¸·4  xpw   ?@     t "net.sf.jasperreports.engine.data.*t net.sf.jasperreports.engine.*t java.util.*xt javapsq ~   wî ur &[Lnet.sf.jasperreports.engine.JRField;<ßÇN*òp  xp   sr ,net.sf.jasperreports.engine.base.JRBaseField      'Ø L descriptionq ~ L nameq ~ L 
propertiesMapq ~ L valueClassNameq ~ L valueClassRealNameq ~ xppt listaMovPagamentosq ~ &pppt java.lang.Objectpsq ~ ´pt listaMovProdutosq ~ &pppt java.lang.Objectpsq ~ ´pt reciboPagamentoVO.codigosq ~ &pppt java.lang.Integerpsq ~ ´pt reciboPagamentoVO.datasq ~ &pppt java.util.Datepsq ~ ´pt reciboPagamentoVO.valorTotalsq ~ &pppt java.lang.Doublepsq ~ ´pt #reciboPagamentoVO.nomePessoaPagadorsq ~ &pppt java.lang.Stringpsq ~ ´pt AreciboPagamentoVO.responsavelLancamento.colaboradorVO.pessoa.nomesq ~ &pppt java.lang.Stringpsq ~ ´pt numeroContratosq ~ &pppt java.lang.Stringpsq ~ ´pt nomeOperadorsq ~ &pppt java.lang.Stringpsq ~ ´pt 	matriculasq ~ &pppt java.lang.Stringpsq ~ ´pt mostrarNumeroContratosq ~ &pppt java.lang.Booleanpsq ~ ´pt listaMovParcelasq ~ &pppt java.lang.Objectpsq ~ ´pt consultorResponsavelsq ~ &pppt java.lang.Stringpsq ~ ´pt 	devolucaosq ~ &pppt java.lang.Booleanpsq ~ ´pt descricaoDevolucaosq ~ &pppt java.lang.Stringpsq ~ ´pt contratosq ~ &pppt java.lang.Integerpsq ~ ´pt valorDevolvidoMonetariosq ~ &pppt java.lang.Stringpsq ~ ´pt centralEventossq ~ &pppt java.lang.Booleanpsq ~ ´pt movProduto.precoUnitariosq ~ &pppt java.lang.Doublepsq ~ ´pt 0descConfiguracaoVO.porcentagemDescontoApresentarsq ~ &pppt java.lang.Stringpsq ~ ´pt movProduto.produto.tipoProdutosq ~ &pppt java.lang.Stringpsq ~ ´pt listaDescontosRecibosq ~ &pppt java.lang.Objectpsq ~ ´pt apresentarDescontossq ~ &pppt java.lang.Booleanpsq ~ ´pt modalidadessq ~ &pppt java.lang.Stringppur &[Lnet.sf.jasperreports.engine.JRGroup;@£_zLýxê  xp   sr ,net.sf.jasperreports.engine.base.JRBaseGroup      'Ø I PSEUDO_SERIAL_VERSION_UIDB footerPositionZ isReprintHeaderOnEachPageZ isResetPageNumberZ isStartNewColumnZ isStartNewPageZ keepTogetherI minHeightToStartNewPageL 
countVariablet (Lnet/sf/jasperreports/engine/JRVariable;L 
expressionq ~ L footerPositionValuet 5Lnet/sf/jasperreports/engine/type/FooterPositionEnum;L groupFooterq ~ L groupFooterSectionq ~ L groupHeaderq ~ L groupHeaderSectionq ~ L nameq ~ xp  wî         Ksq ~ b  wî   q ~ sq ~ o   uq ~ r   sq ~ tt new java.lang.Integer(1)q ~ 6ppq ~ mppsq ~ o   
uq ~ r   sq ~ tt new java.lang.Integer(0)q ~ 6pt caixaPoOperador_COUNTq ~~q ~ xt GROUPq ~ 6psq ~ o   uq ~ r   sq ~ tt reciboPagamentoVO.codigot java.lang.Objectp~r 3net.sf.jasperreports.engine.type.FooterPositionEnum          xq ~ it NORMALpsq ~ ¨ppsq ~ ¨ur %[Lnet.sf.jasperreports.engine.JRBand;Ý~ìÊ5  xp   sr +net.sf.jasperreports.engine.base.JRBaseBand      'Ø I PSEUDO_SERIAL_VERSION_UIDI heightZ isSplitAllowedL printWhenExpressionq ~ L 	splitTypet Ljava/lang/Byte;L splitTypeValuet 0Lnet/sf/jasperreports/engine/type/SplitTypeEnum;xr 3net.sf.jasperreports.engine.base.JRBaseElementGroup      'Ø L childrenq ~ 'L elementGroupt ,Lnet/sf/jasperreports/engine/JRElementGroup;xpsr java.util.ArrayListxÒÇa I sizexp   w   sr 0net.sf.jasperreports.engine.base.JRBaseTextField      'Ø I PSEUDO_SERIAL_VERSION_UIDI 
bookmarkLevelB evaluationTimeB hyperlinkTargetB 
hyperlinkTypeZ isStretchWithOverflowL anchorNameExpressionq ~ L evaluationGroupq ~ dL evaluationTimeValuet 5Lnet/sf/jasperreports/engine/type/EvaluationTimeEnum;L 
expressionq ~ L hyperlinkAnchorExpressionq ~ L hyperlinkPageExpressionq ~ [ hyperlinkParameterst 3[Lnet/sf/jasperreports/engine/JRHyperlinkParameter;L hyperlinkReferenceExpressionq ~ L hyperlinkTooltipExpressionq ~ L isBlankWhenNullt Ljava/lang/Boolean;L 
linkTargetq ~ L linkTypeq ~ L patternq ~ xr 2net.sf.jasperreports.engine.base.JRBaseTextElement      'Ø %I PSEUDO_SERIAL_VERSION_UIDL borderq ~4L borderColort Ljava/awt/Color;L bottomBorderq ~4L bottomBorderColorq ~@L 
bottomPaddingt Ljava/lang/Integer;L fontNameq ~ L fontSizeq ~AL horizontalAlignmentq ~4L horizontalAlignmentValuet 6Lnet/sf/jasperreports/engine/type/HorizontalAlignEnum;L isBoldq ~>L isItalicq ~>L 
isPdfEmbeddedq ~>L isStrikeThroughq ~>L isStyledTextq ~>L isUnderlineq ~>L 
leftBorderq ~4L leftBorderColorq ~@L leftPaddingq ~AL lineBoxt 'Lnet/sf/jasperreports/engine/JRLineBox;L lineSpacingq ~4L lineSpacingValuet 2Lnet/sf/jasperreports/engine/type/LineSpacingEnum;L markupq ~ L paddingq ~AL pdfEncodingq ~ L pdfFontNameq ~ L 
reportFontq ~ L rightBorderq ~4L rightBorderColorq ~@L rightPaddingq ~AL rotationq ~4L 
rotationValuet /Lnet/sf/jasperreports/engine/type/RotationEnum;L 	topBorderq ~4L topBorderColorq ~@L 
topPaddingq ~AL verticalAlignmentq ~4L verticalAlignmentValuet 4Lnet/sf/jasperreports/engine/type/VerticalAlignEnum;xr .net.sf.jasperreports.engine.base.JRBaseElement      'Ø I PSEUDO_SERIAL_VERSION_UIDI heightZ isPrintInFirstWholeBandZ isPrintRepeatedValuesZ isPrintWhenDetailOverflowsZ isRemoveLineWhenBlankB positionTypeB stretchTypeI widthI xI yL 	backcolorq ~@L defaultStyleProvidert 4Lnet/sf/jasperreports/engine/JRDefaultStyleProvider;L elementGroupq ~7L 	forecolorq ~@L keyq ~ L modeq ~4L 	modeValuet +Lnet/sf/jasperreports/engine/type/ModeEnum;L parentStyleq ~ L parentStyleNameReferenceq ~ L positionTypeValuet 3Lnet/sf/jasperreports/engine/type/PositionTypeEnum;L printWhenExpressionq ~ L printWhenGroupChangesq ~ dL 
propertiesMapq ~ [ propertyExpressionst 3[Lnet/sf/jasperreports/engine/JRPropertyExpression;L stretchTypeValuet 2Lnet/sf/jasperreports/engine/type/StretchTypeEnum;xp  wî          5   u   pq ~ q ~8pt 
textField-211p~r )net.sf.jasperreports.engine.type.ModeEnum          xq ~ it OPAQUEpp~r 1net.sf.jasperreports.engine.type.PositionTypeEnum          xq ~ it FIX_RELATIVE_TO_TOPpppp~r 0net.sf.jasperreports.engine.type.StretchTypeEnum          xq ~ it 
NO_STRETCH  wîpppppt Microsoft Sans Serifsr java.lang.Integerâ ¤÷8 I valuexr java.lang.Number¬à  xp   	p~r 4net.sf.jasperreports.engine.type.HorizontalAlignEnum          xq ~ it LEFTsr java.lang.BooleanÍ rÕúî Z valuexp ppppppppsr .net.sf.jasperreports.engine.base.JRBaseLineBox      'Ø L 
bottomPaddingq ~AL 	bottomPent +Lnet/sf/jasperreports/engine/base/JRBoxPen;L boxContainert ,Lnet/sf/jasperreports/engine/JRBoxContainer;L leftPaddingq ~AL leftPenq ~bL paddingq ~AL penq ~bL rightPaddingq ~AL rightPenq ~bL 
topPaddingq ~AL topPenq ~bxppsr 3net.sf.jasperreports.engine.base.JRBaseBoxBottomPen      'Ø  xr -net.sf.jasperreports.engine.base.JRBaseBoxPen      'Ø L lineBoxq ~Cxr *net.sf.jasperreports.engine.base.JRBasePen      'Ø I PSEUDO_SERIAL_VERSION_UIDL 	lineColorq ~@L 	lineStyleq ~4L lineStyleValuet 0Lnet/sf/jasperreports/engine/type/LineStyleEnum;L 	lineWidtht Ljava/lang/Float;L penContainert ,Lnet/sf/jasperreports/engine/JRPenContainer;xp  wîsr java.awt.Color¥3u F falphaI valueL cst Ljava/awt/color/ColorSpace;[ 	frgbvaluet [F[ fvalueq ~nxp    ÿfffpppp~r .net.sf.jasperreports.engine.type.LineStyleEnum          xq ~ it SOLIDsr java.lang.FloatÚíÉ¢Û<ðì F valuexq ~Z    q ~dq ~dq ~Mpsr 1net.sf.jasperreports.engine.base.JRBaseBoxLeftPen      'Ø  xq ~f  wîsq ~l    ÿfffppppq ~qsq ~s    q ~dq ~dpsq ~f  wîppppq ~dq ~dpsr 2net.sf.jasperreports.engine.base.JRBaseBoxRightPen      'Ø  xq ~f  wîsq ~l    ÿfffppppq ~qsq ~s    q ~dq ~dpsr 0net.sf.jasperreports.engine.base.JRBaseBoxTopPen      'Ø  xq ~f  wîsq ~l    ÿfffppppq ~qsq ~s    q ~dq ~dpppppt 	Helveticapppppppppp~r 2net.sf.jasperreports.engine.type.VerticalAlignEnum          xq ~ it MIDDLE  wî        pp~r 3net.sf.jasperreports.engine.type.EvaluationTimeEnum          xq ~ it NOWsq ~ o   uq ~ r   sq ~ tt nomeOperadort java.lang.Stringppppppq ~`pppsq ~;  wî             Ý   pq ~ q ~8pt 
textField-229pq ~Pppq ~Sppppq ~V  wîpppppt Microsoft Sans Serifq ~[pq ~]sq ~_ppppppppsq ~apsq ~e  wîsq ~l    ÿfffppppq ~qsq ~s    q ~q ~q ~psq ~u  wîsq ~l    ÿfffppppq ~qsq ~s    q ~q ~psq ~f  wîppppq ~q ~psq ~z  wîsq ~l    ÿfffppppq ~qsq ~s    q ~q ~psq ~~  wîsq ~l    ÿfffppppq ~qsq ~s    q ~q ~pppppt Helvetica-Boldppppppppppq ~  wî        ppq ~sq ~ o   uq ~ r   sq ~ tt #reciboPagamentoVO.nomePessoaPagadort java.lang.Stringppppppq ~`pppsq ~;  wî           N      pq ~ q ~8pt 
textField-231pq ~Pppq ~Sppppq ~V  wîpppppt Microsoft Sans Serifq ~[p~q ~\t CENTERq ~ppppppppsq ~apsq ~e  wîsq ~l    ÿfffppppq ~qsq ~s    q ~«q ~«q ~¦psq ~u  wîsq ~l    ÿfffppppq ~qsq ~s    q ~«q ~«psq ~f  wîppppq ~«q ~«psq ~z  wîsq ~l    ÿfffppppq ~qsq ~s    q ~«q ~«psq ~~  wîsq ~l    ÿfffppppq ~qsq ~s    q ~«q ~«pppppt Helvetica-Boldppppppppppq ~  wî        ppq ~sq ~ o   uq ~ r   sq ~ tt 	matriculat java.lang.Stringppppppq ~`pppsq ~;  wî           Z      pq ~ q ~8pt 
textField-228pq ~Pppq ~Sppppq ~V  wîpppppt Microsoft Sans Serifq ~[pq ~]q ~ppppppppsq ~apsq ~e  wîsq ~l    ÿfffppppq ~qsq ~s    q ~Âq ~Âq ~¿psq ~u  wîsq ~l    ÿfffppppq ~qsq ~s    q ~Âq ~Âpsq ~f  wîppppq ~Âq ~Âpsq ~z  wîsq ~l    ÿfffppppq ~qsq ~s    q ~Âq ~Âpsq ~~  wîsq ~l    ÿfffppppq ~qsq ~s    q ~Âq ~Âpppppt Helvetica-Boldppppppppppq ~  wî        ppq ~sq ~ o   uq ~ r   sq ~ tt reciboPagamentoVO.datat java.util.Dateppppppq ~`pppsq ~;  wî           t      pq ~ q ~8pt 
textField-211pq ~Pppq ~Sppppq ~V  wîpppppt Microsoft Sans Serifq ~[pq ~]q ~`ppppppppsq ~apsq ~e  wîsq ~l    ÿfffppppq ~qsq ~s    q ~Ùq ~Ùq ~Öpsq ~u  wîsq ~l    ÿfffppppq ~qsq ~s    q ~Ùq ~Ùpsq ~f  wîppppq ~Ùq ~Ùpsq ~z  wîsq ~l    ÿfffppppq ~qsq ~s    q ~Ùq ~Ùpsq ~~  wîsq ~l    ÿfffppppq ~qsq ~s    q ~Ùq ~Ùpppppt 	Helveticappppppppppq ~  wî        ppq ~sq ~ o   uq ~ r   sq ~ tt ""+(sq ~ tt 	devolucaosq ~ tt 	 ? " " : sq ~ tt 
Resp_Recebsq ~ tt  )t java.lang.Stringppppppq ~`pppsq ~;  wî           ¯   u   pq ~ q ~8pt 
textField-212pq ~Pppq ~Sppppq ~V  wîpppppt Microsoft Sans Serifq ~[pq ~]q ~`ppppppppsq ~apsq ~e  wîsq ~l    ÿfffppppq ~qsq ~s    q ~øq ~øq ~õpsq ~u  wîsq ~l    ÿfffppppq ~qsq ~s    q ~øq ~øpsq ~f  wîppppq ~øq ~øpsq ~z  wîsq ~l    ÿfffppppq ~qsq ~s    q ~øq ~øpsq ~~  wîsq ~l    ÿfffppppq ~qsq ~s    q ~øq ~øpppppt 	Helveticappppppppppq ~  wî        ppq ~sq ~ o   uq ~ r   sq ~ tt consultorResponsavelt java.lang.Stringppppppq ~`pppsq ~;  wî           j      pq ~ q ~8pt 
textField-211pq ~Pppq ~Sppppq ~V  wîpppppt Microsoft Sans Serifq ~[pq ~]q ~`ppppppppsq ~apsq ~e  wîsq ~l    ÿfffppppq ~qsq ~s    q ~q ~q ~psq ~u  wîsq ~l    ÿfffppppq ~qsq ~s    q ~q ~psq ~f  wîppppq ~q ~psq ~z  wîsq ~l    ÿfffppppq ~qsq ~s    q ~q ~psq ~~  wîsq ~l    ÿfffppppq ~qsq ~s    q ~q ~pppppt 	Helveticappppppppppq ~  wî        ppq ~sq ~ o   uq ~ r   sq ~ tt ""+(sq ~ tt 	devolucaosq ~ tt  ? sq ~ tt Resp_Devolucaosq ~ tt  : sq ~ tt Consultor_Respsq ~ tt )t java.lang.Stringppppppq ~`pppsq ~;  wî             ¼   pq ~ q ~8pt 
textField-230pq ~Pppq ~Sppppq ~V  wîpppppt Microsoft Sans Serifq ~[pq ~©q ~ppppppppsq ~apsq ~e  wîsq ~l    ÿfffppppq ~qsq ~s    q ~2q ~2q ~/psq ~u  wîsq ~l    ÿfffppppq ~qsq ~s    q ~2q ~2psq ~f  wîppppq ~2q ~2psq ~z  wîsq ~l    ÿfffppppq ~qsq ~s    q ~2q ~2psq ~~  wîsq ~l    ÿfffppppq ~qsq ~s    q ~2q ~2pppppt Helvetica-Boldppppppppppq ~  wî        ppq ~sq ~ o   uq ~ r   sq ~ tt ""+(sq ~ tt 	devolucaosq ~ tt  ? "Contrato:"+sq ~ tt contratosq ~ tt  : "")t java.lang.Stringppppppq ~`pppsq ~;  wî           8   c   pq ~ q ~8pt 
textField-230pq ~Pppq ~Sppppq ~V  wîpppppt Microsoft Sans Serifq ~[pq ~©q ~ppppppppsq ~apsq ~e  wîsq ~l    ÿfffppppq ~qsq ~s    q ~Qq ~Qq ~Npsq ~u  wîsq ~l    ÿfffppppq ~qsq ~s    q ~Qq ~Qpsq ~f  wîppppq ~Qq ~Qpsq ~z  wîsq ~l    ÿfffppppq ~qsq ~s    q ~Qq ~Qpsq ~~  wîsq ~l    ÿfffppppq ~qsq ~s    q ~Qq ~Qpppppt Helvetica-Boldppppppppppq ~  wî        ppq ~sq ~ o   uq ~ r   sq ~ tt ""+(sq ~ tt 	devolucaosq ~ tt  ? "" : ""+sq ~ tt reciboPagamentoVO.codigosq ~ tt )t java.lang.Stringppppppq ~`pppsq ~;  wî              þ   pq ~ q ~8pt 
textField-231pq ~Pppq ~Sppppq ~V  wîpppppt Microsoft Sans Serifq ~[pq ~©q ~ppppppppsq ~apsq ~e  wîsq ~l    ÿfffppppq ~qsq ~s    q ~pq ~pq ~mpsq ~u  wîsq ~l    ÿfffppppq ~qsq ~s    q ~pq ~ppsq ~f  wîppppq ~pq ~ppsq ~z  wîsq ~l    ÿfffppppq ~qsq ~s    q ~pq ~ppsq ~~  wîsq ~l    ÿfffppppq ~qsq ~s    q ~pq ~ppppppt Helvetica-Boldppppppppppq ~  wî        ppq ~sq ~ o   uq ~ r   sq ~ tt ""+(sq ~ tt 	devolucaosq ~ tt  ? sq ~ tt 	Devolucaosq ~ tt  : "")t java.lang.Stringppppppq ~`pppsr +net.sf.jasperreports.engine.base.JRBaseLine      'Ø I PSEUDO_SERIAL_VERSION_UIDB 	directionL directionValuet 4Lnet/sf/jasperreports/engine/type/LineDirectionEnum;xr 5net.sf.jasperreports.engine.base.JRBaseGraphicElement      'Ø I PSEUDO_SERIAL_VERSION_UIDL fillq ~4L 	fillValuet +Lnet/sf/jasperreports/engine/type/FillEnum;L linePent #Lnet/sf/jasperreports/engine/JRPen;L penq ~4xq ~G  wî          q       pq ~ q ~8pt line-1ppppq ~Sppppq ~V  wîppsq ~g  wîppppq ~p  wî ~r 2net.sf.jasperreports.engine.type.LineDirectionEnum          xq ~ it TOP_DOWNxp  wî   +sq ~ o   uq ~ r   sq ~ tt apresentarDadossq ~ tt  && !(sq ~ tt somenteSinteticosq ~ tt .booleanValue())t java.lang.Booleanpp~r .net.sf.jasperreports.engine.type.SplitTypeEnum          xq ~ it PREVENTt caixaPoOperadorsq ~  wî          sq ~ b  wî   q ~ sq ~ o   uq ~ r   sq ~ tt new java.lang.Integer(1)q ~ 6ppq ~ mppsq ~ o   uq ~ r   sq ~ tt new java.lang.Integer(0)q ~ 6pt movParcela_COUNTq ~¦q ~%q ~ 6ppq ~-psq ~ ¨ppsq ~ ¨uq ~1   sq ~3sq ~9   w   sr 0net.sf.jasperreports.engine.base.JRBaseSubreport      'Ø L connectionExpressionq ~ L dataSourceExpressionq ~ L 
expressionq ~ L isUsingCacheq ~>[ 
parameterst 3[Lnet/sf/jasperreports/engine/JRSubreportParameter;L parametersMapExpressionq ~ [ returnValuest 5[Lnet/sf/jasperreports/engine/JRSubreportReturnValue;L runToBottomq ~>xq ~G  wî          g   
    pq ~ q ~´pt subreport-2ppppq ~Spppp~q ~Ut RELATIVE_TO_BAND_HEIGHTpsq ~ o   %uq ~ r   sq ~ tt listaMovParcelaq ~ :psq ~ o   &uq ~ r   sq ~ tt SUBREPORT_DIR2sq ~ tt  + "MovParcela.jasper"t java.lang.Stringpq ~ur 3[Lnet.sf.jasperreports.engine.JRSubreportParameter;[£À¾B  xp   sr 9net.sf.jasperreports.engine.base.JRBaseSubreportParameter      'Ø  xr 7net.sf.jasperreports.engine.base.JRBaseDatasetParameter      'Ø L 
expressionq ~ L nameq ~ xpsq ~ o   !uq ~ r   sq ~ tt 
SUBREPORT_DIRq ~+pt 
SUBREPORT_DIRsq ~Êsq ~ o   "uq ~ r   sq ~ tt listaMovProdutoq ~+pt listaMovProdutosq ~Êsq ~ o   #uq ~ r   sq ~ tt REPORT_RESOURCE_BUNDLEq ~+pt REPORT_RESOURCE_BUNDLEsq ~Êsq ~ o   $uq ~ r   sq ~ tt moedaq ~+pt moedapppxp  wî   sq ~ o    uq ~ r   sq ~ tt apresentarDadossq ~ tt  && sq ~ tt somenteSinteticosq ~ tt .equals( false )q ~¡ppq ~£t 
movParcelasq ~  wî          sq ~ b  wî   q ~ sq ~ o   uq ~ r   sq ~ tt new java.lang.Integer(1)q ~ 6ppq ~ mppsq ~ o   uq ~ r   sq ~ tt new java.lang.Integer(0)q ~ 6pt movProduto_COUNTq ~ïq ~%q ~ 6psq ~ o   'pq ~+pq ~-psq ~ ¨ppsq ~ ¨uq ~1   sq ~3sq ~9   w   sq ~;  wî          @      pq ~ q ~þpt 
textField-211pq ~Pppq ~Ssq ~ o   )uq ~ r   sq ~ tt !sq ~ tt centralEventosq ~¡ppppq ~V  wîpppppt Microsoft Sans Serifq ~[pq ~]q ~`ppppq ~pppsq ~apsq ~e  wîsq ~l    ÿfffppppq ~qsq ~s    q ~	q ~	q ~ psq ~u  wîsq ~l    ÿfffppppq ~qsq ~s    q ~	q ~	psq ~f  wîppppq ~	q ~	psq ~z  wîsq ~l    ÿfffppppq ~qsq ~s    q ~	q ~	psq ~~  wîsq ~l    ÿfffppppq ~qsq ~s    q ~	q ~	pppppt 	Helveticappppppppppq ~  wî        ppq ~sq ~ o   *uq ~ r   sq ~ tt ""+(sq ~ tt 	devolucaosq ~ tt  ? sq ~ tt Calculo_Devolucaosq ~ tt  : sq ~ tt Produtos_Recibosq ~ tt )t java.lang.Stringppppppq ~`pppxp  wî   sq ~ o   (uq ~ r   sq ~ tt apresentarDadossq ~ tt  && sq ~ tt somenteSinteticosq ~ tt .equals( false )q ~¡ppq ~£sq ~3sq ~9   w   sq ~;  wî          e       pq ~ q ~3pt 
textField-211pq ~Pppq ~Sppppq ~»  wîpppppt Microsoft Sans Serifq ~[pq ~]q ~`ppppppppsq ~apsq ~e  wîsq ~l    ÿfffppppq ~qsq ~s    q ~8q ~8q ~5psq ~u  wîsq ~l    ÿfffppppq ~qsq ~s    q ~8q ~8psq ~f  wîppppq ~8q ~8psq ~z  wîsq ~l    ÿfffppppq ~qsq ~s    q ~8q ~8psq ~~  wîsq ~l    ÿfffppppq ~qsq ~s    q ~8q ~8pppppt 	Helveticappppppppppq ~  wî       ppq ~sq ~ o   ,uq ~ r   sq ~ tt descricaoDevolucaot java.lang.Stringppppppq ~pppxp  wî   sq ~ o   +uq ~ r   sq ~ tt apresentarDadossq ~ tt  && sq ~ tt somenteSinteticosq ~ tt .equals( false )q ~¡pppsq ~3sq ~9   w   sq ~¶  wî          g   
   pq ~ q ~Vpt subreport-2ppppq ~Sppppq ~»psq ~ o   1uq ~ r   sq ~ tt listaMovProdutoq ~ :psq ~ o   2uq ~ r   sq ~ tt SUBREPORT_DIR1sq ~ tt  + "MovProduto.jasper"t java.lang.Stringpq ~`uq ~È   sq ~Êsq ~ o   .uq ~ r   sq ~ tt 
SUBREPORT_DIRq ~+pt 
SUBREPORT_DIRsq ~Êsq ~ o   /uq ~ r   sq ~ tt listaMovProdutoq ~+pt listaMovProdutosq ~Êsq ~ o   0uq ~ r   sq ~ tt REPORT_RESOURCE_BUNDLEq ~+pt REPORT_RESOURCE_BUNDLEpppxp  wî   sq ~ o   -uq ~ r   sq ~ tt apresentarDadossq ~ tt  && sq ~ tt somenteSinteticosq ~ tt .equals( false )q ~¡pppsq ~3sq ~9   w   sq ~;  wî         e      pq ~ q ~ppppppq ~Ssq ~ o   4uq ~ r   sq ~ tt apresentarDadossq ~ tt  && sq ~ tt somenteSinteticosq ~ tt .equals( false )q ~¡ppppq ~V  wîppppppsq ~Y   pq ~©pppppppppsq ~apsq ~e  wîppppq ~q ~q ~psq ~u  wîppppq ~q ~psq ~f  wîppppq ~q ~psq ~z  wîppppq ~q ~psq ~~  wîppppq ~q ~ppppppppppppppppp  wî       ppq ~sq ~ o   5uq ~ r   sq ~ tt modalidadest java.lang.Stringppppppppppxp  wî   sq ~ o   3uq ~ r   sq ~ tt apresentarDadossq ~ tt  && sq ~ tt somenteSinteticosq ~ tt .equals( false )q ~¡pppsq ~3sq ~9   w   sq ~;  wî                 pq ~ q ~¥ppppppq ~Ssq ~ o   7uq ~ r   sq ~ tt apresentarDescontossq ~ tt .equals( true )q ~¡ppppq ~V  wîpppppt Microsoft Sans Serifq ~[pppppppq ~pppsq ~apsq ~e  wîppppq ~¯q ~¯q ~§psq ~u  wîppppq ~¯q ~¯psq ~f  wîppppq ~¯q ~¯psq ~z  wîppppq ~¯q ~¯psq ~~  wîppppq ~¯q ~¯ppt nonepppppppppppppp  wî        ppq ~sq ~ o   8uq ~ r   sq ~ tt Desc_Extra_Conveniot java.lang.Stringppppppppppsq ~¶  wî   
      g       pq ~ q ~¥ppppppq ~Sppppq ~Vpsq ~ o   :uq ~ r   sq ~ tt listaDescontosReciboq ~ :psq ~ o   ;uq ~ r   sq ~ tt 
SUBREPORT_DIRsq ~ tt  + "Descontos.jasper"t java.lang.Stringppuq ~È   sq ~Êsq ~ o   9uq ~ r   sq ~ tt REPORT_RESOURCE_BUNDLEq ~+pt REPORT_RESOURCE_BUNDLEpppxp  wî   sq ~ o   6uq ~ r   sq ~ tt apresentarDadossq ~ tt  && sq ~ tt somenteSinteticosq ~ tt .equals( false )q ~¡pppt 
movProdutosq ~  wî          sq ~ b  wî   q ~ sq ~ o   uq ~ r   sq ~ tt new java.lang.Integer(1)q ~ 6ppq ~ mppsq ~ o   uq ~ r   sq ~ tt new java.lang.Integer(0)q ~ 6pt movPagamento_COUNTq ~Ùq ~%q ~ 6psq ~ o   <pq ~+pq ~-psq ~ ¨ppsq ~ ¨uq ~1   sq ~3sq ~9   w   sq ~¶  wî          g   
   pq ~ q ~èpt subreport-1ppppq ~Sppppq ~»psq ~ o   Buq ~ r   sq ~ tt listaMovPagamentoq ~ :psq ~ o   Cuq ~ r   sq ~ tt 
SUBREPORT_DIRsq ~ tt  + "MovPagamento.jasper"t java.lang.Stringpq ~uq ~È   sq ~Êsq ~ o   >uq ~ r   sq ~ tt 
SUBREPORT_DIRq ~+pt 
SUBREPORT_DIRsq ~Êsq ~ o   ?uq ~ r   sq ~ tt REPORT_RESOURCE_BUNDLEq ~+pt REPORT_RESOURCE_BUNDLEsq ~Êsq ~ o   @uq ~ r   sq ~ tt listaMovPagamentoq ~+pt listaMovPagamentosq ~Êsq ~ o   Auq ~ r   sq ~ tt moedaq ~+pt moedapppsq ~;  wî           Ê   
    pq ~ q ~èpt 
textField-211pq ~Pppq ~Sppppq ~V  wîpppppt Microsoft Sans Serifq ~[pq ~]q ~`ppppq ~pppsq ~apsq ~e  wîsq ~l    ÿfffppppq ~qsq ~s    q ~q ~q ~psq ~u  wîsq ~l    ÿfffppppq ~qsq ~s    q ~q ~psq ~f  wîppppq ~q ~psq ~z  wîsq ~l    ÿfffppppq ~qsq ~s    q ~q ~psq ~~  wîsq ~l    ÿfffppppq ~qsq ~s    q ~q ~pppppt 	Helveticappppppppppq ~  wî        ppq ~sq ~ o   Duq ~ r   sq ~ tt ""+(sq ~ tt 	devolucaosq ~ tt  ? "" : sq ~ tt Pag_Recibossq ~ tt )t java.lang.Stringppppppq ~`pppsq ~;  wî          4   Ô    pq ~ q ~èpt 
textField-231pq ~Pppq ~Sppppq ~V  wîpppppt Microsoft Sans Serifq ~[pq ~©q ~ppppppppsq ~apsq ~e  wîsq ~l    ÿfffppppq ~qsq ~s    q ~2q ~2q ~/psq ~u  wîsq ~l    ÿfffppppq ~qsq ~s    q ~2q ~2psq ~f  wîppppq ~2q ~2psq ~z  wîsq ~l    ÿfffppppq ~qsq ~s    q ~2q ~2psq ~~  wîsq ~l    ÿfffppppq ~qsq ~s    q ~2q ~2pppppt Helvetica-Boldppppppppppq ~  wî        ppq ~sq ~ o   Euq ~ r   sq ~ tt ""+(sq ~ tt 	devolucaosq ~ tt  ? sq ~ tt Valor_Devolvidosq ~ tt 
 + " " +
    sq ~ tt valorDevolvidoMonetariosq ~ tt  : "")t java.lang.Stringppppppq ~`pppxp  wî   -sq ~ o   =uq ~ r   sq ~ tt apresentarDadossq ~ tt  && sq ~ tt somenteSinteticosq ~ tt .equals( false )q ~¡ppq ~£t movPagamentot CaixaPorOperadorReluq ~ !   3sq ~ #ppq ~ %psq ~ &pppq ~ *psq ~ #ppq ~ ,psq ~ &pppq ~ .psq ~ #ppq ~ 0psq ~ &pppq ~ 2psq ~ #ppq ~ 4psq ~ &pppq ~ 6psq ~ #ppq ~ 8psq ~ &pppq ~ :psq ~ #ppq ~ <psq ~ &pppq ~ >psq ~ #ppq ~ @psq ~ &pppq ~ Bpsq ~ #ppq ~ Dpsq ~ &pppq ~ Fpsq ~ #ppq ~ Hpsq ~ &pppq ~ Jpsq ~ #ppq ~ Lpsq ~ &pppq ~ Npsq ~ #ppq ~ Ppsq ~ &pppq ~ Rpsq ~ #ppq ~ Tpsq ~ &pppq ~ Vpsq ~ #ppq ~ Xpsq ~ &pppq ~ Zpsq ~ #ppq ~ \psq ~ &pppq ~ ^psq ~ #ppt REPORT_VIRTUALIZERpsq ~ &pppt )net.sf.jasperreports.engine.JRVirtualizerpsq ~ #ppt IS_IGNORE_PAGINATIONpsq ~ &pppq ~¡psq ~ #  ppt tituloRelatoriopsq ~ &pppt java.lang.Stringpsq ~ #  ppt nomeEmpresapsq ~ &pppt java.lang.Stringpsq ~ #  ppt versaoSoftwarepsq ~ &pppt java.lang.Stringpsq ~ #  ppt usuariopsq ~ &pppt java.lang.Stringpsq ~ #  ppt filtrospsq ~ &pppt java.lang.Stringpsq ~ # sq ~ o    uq ~ r   sq ~ tt p"E:\\desenvolvimentosv\\Projeto Pacto Solucoes\\ZillyonWeb\\src\\java\\relatorio\\designRelatorio\\financeiro\\"t java.lang.Stringppt 
SUBREPORT_DIRpsq ~ &pppq ~psq ~ # sq ~ o   uq ~ r   sq ~ tt p"E:\\desenvolvimentosv\\Projeto Pacto Solucoes\\ZillyonWeb\\src\\java\\relatorio\\designRelatorio\\financeiro\\"t java.lang.Stringppt SUBREPORT_DIR1psq ~ &pppq ~£psq ~ #  ppt dataInipsq ~ &pppt java.lang.Stringpsq ~ #  ppt dataFimpsq ~ &pppt java.lang.Stringpsq ~ #  ppt qtdAVpsq ~ &pppt java.lang.Stringpsq ~ #  ppt qtdCApsq ~ &pppt java.lang.Stringpsq ~ #  ppt qtdChequeAVpsq ~ &pppt java.lang.Stringpsq ~ #  ppt qtdChequePRpsq ~ &pppt java.lang.Stringpsq ~ #  ppt qtdOutropsq ~ &pppt java.lang.Stringpsq ~ #  ppt valorAVpsq ~ &pppt java.lang.Doublepsq ~ #  ppt valorCApsq ~ &pppt java.lang.Doublepsq ~ #  ppt 
valorChequeAVpsq ~ &pppt java.lang.Doublepsq ~ #  ppt 
valorChequePRpsq ~ &pppt java.lang.Doublepsq ~ #  ppt 
valorOutropsq ~ &pppt java.lang.Doublepsq ~ #  ppt logoPadraoRelatoriopsq ~ &pppt java.io.InputStreampsq ~ # ppt qtdCDpsq ~ &pppt java.lang.Stringpsq ~ # ppt valorCDpsq ~ &pppt java.lang.Doublepsq ~ # sq ~ o   uq ~ r   sq ~ tt p"E:\\desenvolvimentosv\\Projeto Pacto Solucoes\\ZillyonWeb\\src\\java\\relatorio\\designRelatorio\\financeiro\\"t java.lang.Stringppt SUBREPORT_DIR2psq ~ &pppq ~çpsq ~ # ppt qtdBBpsq ~ &pppt java.lang.Stringpsq ~ # ppt valorBBpsq ~ &pppt java.lang.Doublepsq ~ #  ppt qtdDVpsq ~ &pppt java.lang.Stringpsq ~ #  ppt valorDVpsq ~ &pppt java.lang.Doublepsq ~ # ppt 
devolucoespsq ~ &pppt java.lang.Objectpsq ~ # ppt apresentarDadospsq ~ &pppt java.lang.Booleanpsq ~ # ppt qtdDRpsq ~ &pppt java.lang.Stringpsq ~ # ppt valorDRpsq ~ &pppt java.lang.Doublepsq ~ # ppt 
totalizadorespsq ~ &pppt java.lang.Objectpsq ~ # ppt somenteSinteticopsq ~ &pppt java.lang.Booleanpsq ~ # ppt mostrarModalidadepsq ~ &pppt java.lang.Booleanpsq ~ # sq ~ o   uq ~ r   sq ~ tt "R$"t java.lang.Stringppt moedapsq ~ &pppq ~psq ~ &psq ~9   w   t ireport.zoomt 	ireport.xt 	ireport.yt ireport.scriptlethandlingt ireport.encodingxsr java.util.HashMapÚÁÃ`Ñ F 
loadFactorI 	thresholdxp?@     w      q ~ t 1.5q ~$t 
ISO-8859-1q ~!t 0q ~"t 0q ~#t 0xpppppuq ~ `   	sq ~ b  wî   q ~ jppq ~ mppsq ~ o   uq ~ r   sq ~ tt new java.lang.Integer(1)q ~ 6pq ~ wpq ~ yq ~ 6psq ~ b  wî   q ~ jppq ~ mppsq ~ o   uq ~ r   sq ~ tt new java.lang.Integer(1)q ~ 6pq ~ pq ~ q ~ 6psq ~ b  wî   q ~ sq ~ o   uq ~ r   sq ~ tt new java.lang.Integer(1)q ~ 6ppq ~ mppsq ~ o   uq ~ r   sq ~ tt new java.lang.Integer(0)q ~ 6pq ~ pq ~ yq ~ 6psq ~ b  wî   q ~ sq ~ o   uq ~ r   sq ~ tt new java.lang.Integer(1)q ~ 6ppq ~ mppsq ~ o   	uq ~ r   sq ~ tt new java.lang.Integer(0)q ~ 6pq ~ pq ~ q ~ 6psq ~ b  wî   q ~ sq ~ o   
uq ~ r   sq ~ tt new java.lang.Integer(1)q ~ 6ppq ~ mppsq ~ o   uq ~ r   sq ~ tt new java.lang.Integer(0)q ~ 6pq ~ ¢pq ~ £q ~ 6pq ~q ~§q ~ðq ~Ú~q ~ ¥t EMPTYq ~]p~r 0net.sf.jasperreports.engine.type.OrientationEnum          xq ~ it PORTRAITpsq ~3sq ~9   w   sq ~;  wî           Z      Mpq ~ q ~Wpt 
staticText-85pq ~Ppp~q ~Rt FLOATsq ~ o   Fuq ~ r   sq ~ tt !(sq ~ tt somenteSinteticosq ~ tt .booleanValue())q ~¡ppppq ~V  wîpppppt Microsoft Sans Serifq ~[pq ~]q ~`q ~`pq ~`pq ~`pppsq ~apsq ~e  wîppppq ~fq ~fq ~Ypsq ~u  wîppppq ~fq ~fpsq ~f  wîppppq ~fq ~fpsq ~z  wîppppq ~fq ~fpsq ~~  wîppppq ~fq ~fppt noneppt 	Helveticappppppppppq ~  wî        ppq ~sq ~ o   Guq ~ r   sq ~ tt DT_Ent_Caixat java.lang.Stringppppppppppsr 1net.sf.jasperreports.engine.base.JRBaseStaticText      'Ø L textq ~ xq ~?  wî             2   &pq ~ q ~Wpt 
staticText-82pq ~Pppq ~Sppppq ~V  wîpppppt Arialsq ~Y   pq ~©q ~q ~`pq ~`pq ~`pppsq ~apsq ~e  wîsq ~l    ÿfffppppq ~qsq ~s?   q ~xq ~xq ~tpsq ~u  wîsq ~l    ÿfffpppppsq ~s    q ~xq ~xpsq ~f  wîsq ~l    ÿfffpppppsq ~s    q ~xq ~xpsq ~z  wîsq ~l    ÿfffppppq ~qsq ~s    q ~xq ~xpsq ~~  wîsq ~l    ÿfffppppq ~qsq ~s    q ~xq ~xpppppt Helvetica-Boldppppppppppq ~t asq ~;  wî           i      sq ~l    ÿÿÿÿpppq ~ q ~Wpt 	dataRel-1p~q ~Ot TRANSPARENTppq ~Sppppq ~V  wîpppppt Verdanaq ~pq ~©pq ~`pppppppsq ~asq ~Y    sq ~e  wîsq ~l    ÿfffppppq ~qsq ~s?   q ~q ~q ~psq ~u  wîsq ~l    ÿfffppppq ~qsq ~s?   q ~q ~psq ~f  wîppppq ~q ~psq ~z  wîsq ~l    ÿfffppppq ~qsq ~s?   q ~q ~psq ~~  wîsq ~l    ÿfffppppq ~qsq ~s?   q ~q ~pppppt 	Helveticappppppppppq ~  wî        ppq ~sq ~ o   Huq ~ r   sq ~ tt 
new Date()t java.util.Dateppppppq ~`ppt dd/MM/yyyy HH.mm.sssq ~;  wî          µ   S   pq ~ q ~Wpt textField-2ppppq ~Sppppq ~V  wîpppppt Arialq ~wpq ~©q ~ppppppppsq ~apsq ~e  wîsq ~l    ÿfffppppq ~qsq ~s?   q ~©q ~©q ~¦psq ~u  wîppq ~qsq ~s    q ~©q ~©psq ~f  wîppq ~qsq ~s?   q ~©q ~©psq ~z  wîppq ~qsq ~s    q ~©q ~©psq ~~  wîsq ~l    ÿfffppppq ~qsq ~s?   q ~©q ~©pppppt Helvetica-Boldppppppppppq ~  wî        ppq ~sq ~ o   Iuq ~ r   sq ~ tt Titulot java.lang.Stringppppppq ~`pppsq ~;  wî           ß   S   &pq ~ q ~Wpt 
textField-215ppppq ~Sppppq ~V  wîpppppt Arialq ~wp~q ~\t RIGHTq ~q ~pppppppsq ~apsq ~e  wîsq ~l    ÿfffppppq ~qsq ~s?   q ~Áq ~Áq ~¼psq ~u  wîsq ~l    ÿfffppppq ~qsq ~s?   q ~Áq ~Ápsq ~f  wîppppq ~Áq ~Ápsq ~z  wîsq ~l    ÿfffppppq ~qpq ~Áq ~Ápsq ~~  wîppppq ~Áq ~Ápppppt Helvetica-BoldObliqueppppppppppq ~  wî        ppq ~sq ~ o   Juq ~ r   sq ~ tt dataInit java.lang.Stringppppppq ~`pppsq ~;  wî           ¼  L   &pq ~ q ~Wpt 
textField-216ppppq ~Sppppq ~V  wîpppppt Arialq ~wpq ~]q ~q ~pppppppsq ~apsq ~e  wîsq ~l    ÿfffppppq ~qsq ~s?   q ~Õq ~Õq ~Òpsq ~u  wîppppq ~Õq ~Õpsq ~f  wîppppq ~Õq ~Õpsq ~z  wîsq ~l    ÿfffpppppsq ~s?   q ~Õq ~Õpsq ~~  wîppppq ~Õq ~Õpppppt Helvetica-BoldObliqueppppppppppq ~  wî        ppq ~sq ~ o   Kuq ~ r   sq ~ tt dataFimt java.lang.Stringppppppq ~`pppsr ,net.sf.jasperreports.engine.base.JRBaseImage      'Ø *I PSEUDO_SERIAL_VERSION_UIDI 
bookmarkLevelB evaluationTimeB hyperlinkTargetB 
hyperlinkTypeZ isLazyB onErrorTypeL anchorNameExpressionq ~ L borderq ~4L borderColorq ~@L bottomBorderq ~4L bottomBorderColorq ~@L 
bottomPaddingq ~AL evaluationGroupq ~ dL evaluationTimeValueq ~<L 
expressionq ~ L horizontalAlignmentq ~4L horizontalAlignmentValueq ~BL hyperlinkAnchorExpressionq ~ L hyperlinkPageExpressionq ~ [ hyperlinkParametersq ~=L hyperlinkReferenceExpressionq ~ L hyperlinkTooltipExpressionq ~ L isUsingCacheq ~>L 
leftBorderq ~4L leftBorderColorq ~@L leftPaddingq ~AL lineBoxq ~CL 
linkTargetq ~ L linkTypeq ~ L onErrorTypeValuet 2Lnet/sf/jasperreports/engine/type/OnErrorTypeEnum;L paddingq ~AL rightBorderq ~4L rightBorderColorq ~@L rightPaddingq ~AL 
scaleImageq ~4L scaleImageValuet 1Lnet/sf/jasperreports/engine/type/ScaleImageEnum;L 	topBorderq ~4L topBorderColorq ~@L 
topPaddingq ~AL verticalAlignmentq ~4L verticalAlignmentValueq ~Fxq ~  wî   &       R       pq ~ q ~Wpt image-1ppppq ~[ppppq ~V  wîppsq ~g  wîppppq ~èp  wî         ppppppp~q ~t PAGEsq ~ o   Luq ~ r   sq ~ tt logoPadraoRelatoriot java.io.InputStreamppppppppq ~pppsq ~apsq ~e  wîsq ~l    ÿfffppppq ~qsq ~s?   q ~òq ~òq ~èpsq ~u  wîsq ~l    ÿfffppppq ~qsq ~s?   q ~òq ~òpsq ~f  wîppppq ~òq ~òpsq ~z  wîsq ~l    ÿfffppppq ~qsq ~s?   q ~òq ~òpsq ~~  wîsq ~l    ÿfffppppq ~qsq ~s?   q ~òq ~òpp~r 0net.sf.jasperreports.engine.type.OnErrorTypeEnum          xq ~ it BLANKpppppppppp~q ~t TOPsq ~;  wî           2   [   Mpq ~ q ~Wpt staticText-121pq ~Pppq ~[sq ~ o   Muq ~ r   sq ~ tt !(sq ~ tt somenteSinteticosq ~ tt )q ~¡ppppq ~V  wîpppppt Microsoft Sans Serifq ~[pq ~©q ~`q ~`pq ~`pq ~`pppsq ~apsq ~e  wîppppq ~q ~q ~psq ~u  wîppppq ~q ~psq ~f  wîppppq ~q ~psq ~z  wîppppq ~q ~psq ~~  wîppppq ~q ~ppt noneppt 	Helveticappppppppppq ~  wî        ppq ~sq ~ o   Nuq ~ r   sq ~ tt Recibot java.lang.Stringppppppppppsq ~;  wî           P      Mpq ~ q ~Wpt 
staticText-86pq ~Pppq ~[sq ~ o   Ouq ~ r   sq ~ tt !(sq ~ tt somenteSinteticosq ~ tt )q ~¡ppppq ~V  wîpppppt Microsoft Sans Serifq ~[pq ~©q ~`q ~`pq ~`pq ~`pppsq ~apsq ~e  wîppppq ~(q ~(q ~psq ~u  wîppppq ~(q ~(psq ~f  wîppppq ~(q ~(psq ~z  wîppppq ~(q ~(psq ~~  wîppppq ~(q ~(ppt noneppt 	Helveticappppppppppq ~  wî        ppq ~sq ~ o   Puq ~ r   sq ~ tt 	Matriculat java.lang.Stringppppppppppsq ~;  wî             Ý   Mpq ~ q ~Wpt 
staticText-88pq ~Pppq ~[sq ~ o   Quq ~ r   sq ~ tt !(sq ~ tt somenteSinteticosq ~ tt )q ~¡ppppq ~V  wîpppppt Microsoft Sans Serifq ~[pq ~]q ~`q ~`pq ~`pq ~`pppsq ~apsq ~e  wîppppq ~@q ~@q ~5psq ~u  wîppppq ~@q ~@psq ~f  wîppppq ~@q ~@psq ~z  wîppppq ~@q ~@psq ~~  wîppppq ~@q ~@ppt noneppt 	Helveticappppppppppq ~  wî        ppq ~sq ~ o   Ruq ~ r   sq ~ tt 
Nome_Resp_Pagt java.lang.Stringppppppppppsq ~;  wî           2     [pq ~ q ~Wpt staticText-131pq ~Pppq ~Ssq ~ o   Suq ~ r   sq ~ tt !(sq ~ tt somenteSinteticosq ~ tt )q ~¡ppppq ~V  wîpppppt Microsoft Sans Serifq ~[pq ~¿q ~`q ~`pq ~`pq ~`pppsq ~apsq ~e  wîppppq ~Xq ~Xq ~Mpsq ~u  wîppppq ~Xq ~Xpsq ~f  wîppppq ~Xq ~Xpsq ~z  wîppppq ~Xq ~Xpsq ~~  wîppppq ~Xq ~Xppt noneppt 	Helveticappppppppppq ~  wî        ppq ~sq ~ o   Tuq ~ r   sq ~ tt Descontot java.lang.Stringppppppppppsq ~;  wî                [pq ~ q ~Wpt staticText-132pq ~Pppq ~Ssq ~ o   Uuq ~ r   sq ~ tt !(sq ~ tt somenteSinteticosq ~ tt )q ~¡ppppq ~V  wîpppppt Microsoft Sans Serifq ~[pq ~¿q ~`q ~`pq ~`pq ~`pppsq ~apsq ~e  wîppppq ~pq ~pq ~epsq ~u  wîppppq ~pq ~ppsq ~f  wîppppq ~pq ~ppsq ~z  wîppppq ~pq ~ppsq ~~  wîppppq ~pq ~pppt noneppt 	Helveticappppppppppq ~  wî        ppq ~sq ~ o   Vuq ~ r   sq ~ tt Qtdt java.lang.Stringppppppppppsq ~;  wî           #  Ý   [pq ~ q ~Wpt staticText-130pq ~Pppq ~Ssq ~ o   Wuq ~ r   sq ~ tt !(sq ~ tt somenteSinteticosq ~ tt )q ~¡ppppq ~V  wîpppppt Microsoft Sans Serifq ~[pq ~¿q ~`q ~`pq ~`pq ~`pppsq ~apsq ~e  wîppppq ~q ~q ~}psq ~u  wîppppq ~q ~psq ~f  wîppppq ~q ~psq ~z  wîppppq ~q ~psq ~~  wîppppq ~q ~ppt noneppt 	Helveticappppppppppq ~  wî        ppq ~sq ~ o   Xuq ~ r   sq ~ tt Unitariot java.lang.Stringppppppppppsq ~;  wî           2  ?   [pq ~ q ~Wpt staticText-133pq ~Pppq ~Ssq ~ o   Yuq ~ r   sq ~ tt !(sq ~ tt somenteSinteticosq ~ tt )q ~¡ppppq ~V  wîpppppt Microsoft Sans Serifq ~[pq ~¿q ~`q ~`pq ~`pq ~`pppsq ~apsq ~e  wîppppq ~ q ~ q ~psq ~u  wîppppq ~ q ~ psq ~f  wîppppq ~ q ~ psq ~z  wîppppq ~ q ~ psq ~~  wîppppq ~ q ~ ppt noneppt 	Helveticappppppppppq ~  wî        ppq ~sq ~ o   Zuq ~ r   sq ~ tt 
Valor_Pagot java.lang.Stringppppppppppsq ~;  wî           Í   1   [pq ~ q ~Wpt staticText-129pq ~Pppq ~Ssq ~ o   [uq ~ r   sq ~ tt !(sq ~ tt somenteSinteticosq ~ tt )q ~¡ppppq ~V  wîpppppt Microsoft Sans Serifq ~[pq ~]q ~`q ~`pq ~`pq ~`pppsq ~apsq ~e  wîppppq ~¸q ~¸q ~­psq ~u  wîppppq ~¸q ~¸psq ~f  wîppppq ~¸q ~¸psq ~z  wîppppq ~¸q ~¸psq ~~  wîppppq ~¸q ~¸ppt noneppt 	Helveticappppppppppq ~  wî        ppq ~sq ~ o   \uq ~ r   sq ~ tt 
Nome_Alunot java.lang.Stringppppppppppsq ~;  wî           &   
   [pq ~ q ~Wpt staticText-128pq ~Pppq ~Ssq ~ o   ]uq ~ r   sq ~ tt !(sq ~ tt somenteSinteticosq ~ tt )q ~¡ppppq ~V  wîpppppt Microsoft Sans Serifq ~[pq ~]q ~`q ~`pq ~`pq ~`pppsq ~apsq ~e  wîppppq ~Ðq ~Ðq ~Åpsq ~u  wîppppq ~Ðq ~Ðpsq ~f  wîppppq ~Ðq ~Ðpsq ~z  wîppppq ~Ðq ~Ðpsq ~~  wîppppq ~Ðq ~Ðppt noneppt 	Helveticappppppppppq ~  wî        ppq ~sq ~ o   ^uq ~ r   sq ~ tt Contratot java.lang.Stringppppppppppsq ~;  wî           ¬   þ   [pq ~ q ~Wpt staticText-127pq ~Pppq ~Ssq ~ o   _uq ~ r   sq ~ tt !(sq ~ tt somenteSinteticosq ~ tt )q ~¡ppppq ~V  wîpppppt Microsoft Sans Serifq ~[pq ~]q ~`q ~`pq ~`pq ~`pppsq ~apsq ~e  wîppppq ~èq ~èq ~Ýpsq ~u  wîppppq ~èq ~èpsq ~f  wîppppq ~èq ~èpsq ~z  wîppppq ~èq ~èpsq ~~  wîppppq ~èq ~èppt noneppt 	Helveticappppppppppq ~  wî        ppq ~sq ~ o   `uq ~ r   sq ~ tt Produtot java.lang.Stringppppppppppsq ~;  wî           H   S   ipq ~ q ~Wpt 
staticText-89pq ~Pppq ~Ssq ~ o   auq ~ r   sq ~ tt !(sq ~ tt somenteSinteticosq ~ tt )q ~¡ppppq ~V  wîpppppt Microsoft Sans Serifq ~[pq ~©q ~`q ~`pq ~`pq ~`pppsq ~apsq ~e  wîppppq ~ q ~ q ~õpsq ~u  wîppppq ~ q ~ psq ~f  wîppppq ~ q ~ psq ~z  wîppppq ~ q ~ psq ~~  wîppppq ~ q ~ ppt noneppt 	Helveticappppppppppq ~  wî        ppq ~sq ~ o   buq ~ r   sq ~ tt 
Valor_Pagot java.lang.Stringppppppppppsq ~;  wî           a      ipq ~ q ~Wpt 
staticText-90pq ~Pppq ~Ssq ~ o   cuq ~ r   sq ~ tt !(sq ~ tt somenteSinteticosq ~ tt )q ~¡ppppq ~V  wîpppppt Microsoft Sans Serifq ~[pq ~]q ~`q ~`pq ~`pq ~`pppsq ~apsq ~e  wîppppq ~q ~q ~
psq ~u  wîppppq ~q ~psq ~f  wîppppq ~q ~psq ~z  wîppppq ~q ~psq ~~  wîppppq ~q ~ppt noneppt 	Helveticappppppppppq ~  wî        ppq ~sq ~ o   duq ~ r   sq ~ tt Forma_Pgt java.lang.Stringppppppppppsq ~;  wî           H   
   ipq ~ q ~Wpt 
staticText-87pq ~Pppq ~Ssq ~ o   euq ~ r   sq ~ tt !(sq ~ tt somenteSinteticosq ~ tt )q ~¡ppppq ~V  wîpppppt Microsoft Sans Serifq ~[pq ~]q ~`q ~`pq ~`pq ~`pppsq ~apsq ~e  wîppppq ~0q ~0q ~%psq ~u  wîppppq ~0q ~0psq ~f  wîppppq ~0q ~0psq ~z  wîppppq ~0q ~0psq ~~  wîppppq ~0q ~0ppt noneppt 	Helveticappppppppppq ~  wî        ppq ~sq ~ o   fuq ~ r   sq ~ tt Data_Pagamentot java.lang.Stringppppppppppsq ~;  wî           i     pq ~ q ~Wpppq ~ppq ~Sppppq ~V  wîpppppt Verdanaq ~pq ~©pppppppppsq ~apsq ~e  wîpp~q ~pt DOTTEDsq ~s?   q ~?q ~?q ~=psq ~u  wîppq ~Asq ~s?   q ~?q ~?psq ~f  wîppppq ~?q ~?psq ~z  wîppq ~Asq ~s?   q ~?q ~?psq ~~  wîppppq ~?q ~?ppppppppppppppppq ~  wî        ppq ~sq ~ o   guq ~ r   sq ~ tt 
"UsuÃ¡rio:"+ sq ~ tt usuariot java.lang.Stringpppppppppt  sq ~;  wî           F     pq ~ q ~Wppppppq ~Sppppq ~V  wîpppppt Verdanaq ~pq ~¿pppppppppsq ~apsq ~e  wîppq ~Asq ~s?   q ~Tq ~Tq ~Rpsq ~u  wîppq ~Asq ~s?   q ~Tq ~Tpsq ~f  wîppppq ~Tq ~Tpsq ~z  wîppppq ~Tq ~Tpsq ~~  wîppppq ~Tq ~Tppppppppppppppppq ~  wî        ppq ~sq ~ o   huq ~ r   sq ~ tt "PÃ¡gina "+sq ~ tt PAGE_NUMBERsq ~ tt +" de"t java.lang.Stringppppppppppsq ~;  wî           #  N   pq ~ q ~Wppppppq ~Sppppq ~V  wîpppppt Verdanaq ~pppppppppppsq ~apsq ~e  wîppq ~Asq ~s?   q ~gq ~gq ~esq ~Y   sq ~u  wîppppq ~gq ~gpsq ~f  wîppppq ~gq ~gpsq ~z  wîppq ~Asq ~s?   q ~gq ~gpsq ~~  wîppppq ~gq ~gppppppppppppppppq ~  wî        pp~q ~t REPORTsq ~ o   iuq ~ r   sq ~ tt PAGE_NUMBERt java.lang.Integerppppppppppsq ~;  wî          µ   S   7pq ~ q ~Wppppppq ~Sppppq ~V  wîpppppt Arialsq ~Y   pq ~©pppppppppsq ~apsq ~e  wîppppq ~zq ~zq ~wpsq ~u  wîppppq ~zq ~zpsq ~f  wîppppq ~zq ~zpsq ~z  wîppppq ~zq ~zpsq ~~  wîppppq ~zq ~zppppppppppppppppq ~  wî        ppq ~sq ~ o   juq ~ r   sq ~ tt filtrost java.lang.Stringppppppppppsq ~;  wî           .  ª   [pq ~ q ~Wpt staticText-130pq ~Pppq ~Ssq ~ o   kuq ~ r   sq ~ tt !(sq ~ tt somenteSinteticosq ~ tt )q ~¡ppppq ~V  wîpppppt Microsoft Sans Serifq ~[pq ~]q ~`q ~`pq ~`pq ~`pppsq ~apsq ~e  wîppppq ~q ~q ~psq ~u  wîppppq ~q ~psq ~f  wîppppq ~q ~psq ~z  wîppppq ~q ~psq ~~  wîppppq ~q ~ppt noneppt 	Helveticappppppppppq ~  wî        ppq ~sq ~ o   luq ~ r   sq ~ tt Pacotet java.lang.Stringppppppppppsq ~;  wî          µ   S    pq ~ q ~Wpt textField-2ppppq ~Sppppq ~V  wîpppppt Arialq ~wpq ~©q ~ppppppppsq ~apsq ~e  wîsq ~l    ÿfffppppq ~qsq ~s?   q ~ q ~ q ~psq ~u  wîppq ~qsq ~s    q ~ q ~ psq ~f  wîppq ~qsq ~s?   q ~ q ~ psq ~z  wîppq ~qsq ~s    q ~ q ~ psq ~~  wîsq ~l    ÿfffppppq ~qsq ~s?   q ~ q ~ pppppt Helvetica-Boldppppppppppq ~  wî        ppq ~sq ~ o   muq ~ r   sq ~ tt nomeEmpresat java.lang.Stringppppppq ~`pppxp  wî   xpp~q ~¢t STRETCH~r /net.sf.jasperreports.engine.type.PrintOrderEnum          xq ~ it VERTICALpsq ~3sq ~9   w   sq ~¶  wî          q       pq ~ q ~¸ppppppq ~Sppppq ~»psq ~ o   uq ~ r   sq ~ tt 
devolucoesq ~ :psq ~ o   uq ~ r   sq ~ tt 
SUBREPORT_DIRsq ~ tt   + "CaixaReciboDevolucao.jasper"t java.lang.Stringppuq ~È   $sq ~Êsq ~ o   nuq ~ r   sq ~ tt valorCDq ~+pt valorCDsq ~Êsq ~ o   ouq ~ r   sq ~ tt qtdDVq ~+pt qtdDVsq ~Êsq ~ o   puq ~ r   sq ~ tt usuarioq ~+pt usuariosq ~Êsq ~ o   quq ~ r   sq ~ tt valorCAq ~+pt valorCAsq ~Êsq ~ o   ruq ~ r   sq ~ tt 
totalizadoresq ~+pt totalizadoresEspeciessq ~Êsq ~ o   suq ~ r   sq ~ tt qtdCAq ~+pt qtdCAsq ~Êsq ~ o   tuq ~ r   sq ~ tt SUBREPORT_DIR1q ~+pt SUBREPORT_DIR1sq ~Êsq ~ o   uuq ~ r   sq ~ tt 
valorChequeAVq ~+pt 
valorChequeAVsq ~Êsq ~ o   vuq ~ r   sq ~ tt qtdChequePRq ~+pt qtdChequePRsq ~Êsq ~ o   wuq ~ r   sq ~ tt 
valorChequePRq ~+pt 
valorChequePRsq ~Êsq ~ o   xuq ~ r   sq ~ tt SUBREPORT_DIR2q ~+pt SUBREPORT_DIR2sq ~Êsq ~ o   yuq ~ r   sq ~ tt qtdBBq ~+pt qtdBBsq ~Êsq ~ o   zuq ~ r   sq ~ tt 
valorOutroq ~+pt 
valorOutrosq ~Êsq ~ o   {uq ~ r   sq ~ tt somenteSinteticoq ~+pt somenteSinteticosq ~Êsq ~ o   |uq ~ r   sq ~ tt qtdDRq ~+pt qtdDRsq ~Êsq ~ o   }uq ~ r   sq ~ tt qtdAVq ~+pt qtdAVsq ~Êsq ~ o   ~uq ~ r   sq ~ tt dataIniq ~+pt dataInisq ~Êsq ~ o   uq ~ r   sq ~ tt valorBBq ~+pt valorBBsq ~Êsq ~ o   uq ~ r   sq ~ tt qtdOutroq ~+pt qtdOutrosq ~Êsq ~ o   uq ~ r   sq ~ tt logoPadraoRelatorioq ~+pt logoPadraoRelatoriosq ~Êsq ~ o   uq ~ r   sq ~ tt 
totalizadoresq ~+pt 
totalizadoressq ~Êsq ~ o   uq ~ r   sq ~ tt apresentarDadosq ~+pt apresentarDadossq ~Êsq ~ o   uq ~ r   sq ~ tt 
SUBREPORT_DIRq ~+pt 
SUBREPORT_DIRsq ~Êsq ~ o   uq ~ r   sq ~ tt qtdCDq ~+pt qtdCDsq ~Êsq ~ o   uq ~ r   sq ~ tt dataFimq ~+pt dataFimsq ~Êsq ~ o   uq ~ r   sq ~ tt valorDRq ~+pt valorDRsq ~Êsq ~ o   uq ~ r   sq ~ tt tituloRelatorioq ~+pt tituloRelatoriosq ~Êsq ~ o   uq ~ r   sq ~ tt nomeEmpresaq ~+pt nomeEmpresasq ~Êsq ~ o   uq ~ r   sq ~ tt valorDVq ~+pt valorDVsq ~Êsq ~ o   uq ~ r   sq ~ tt qtdChequeAVq ~+pt qtdChequeAVsq ~Êsq ~ o   uq ~ r   sq ~ tt valorAVq ~+pt valorAVsq ~Êsq ~ o   uq ~ r   sq ~ tt 
devolucoesq ~+pt 
devolucoessq ~Êsq ~ o   uq ~ r   sq ~ tt moedaq ~+pt moedasq ~Êsq ~ o   uq ~ r   sq ~ tt REPORT_RESOURCE_BUNDLEq ~+pt REPORT_RESOURCE_BUNDLEsq ~Êsq ~ o   uq ~ r   sq ~ tt versaoSoftwareq ~+pt versaoSoftwaresq ~Êsq ~ o   uq ~ r   sq ~ tt filtrosq ~+pt filtrospppxp  wî   pp~q ~¢t 	IMMEDIATEpp~r 3net.sf.jasperreports.engine.type.WhenNoDataTypeEnum          xq ~ it ALL_SECTIONS_NO_DETAILsr 6net.sf.jasperreports.engine.design.JRReportCompileData      'Ø L crosstabCompileDataq ~ (L datasetCompileDataq ~ (L mainDatasetCompileDataq ~ xpsq ~%?@     w       xsq ~%?@     w      q ~  ur [B¬óøTà  xp  zÊþº¾   .  .CaixaPorOperadorRel_Teste_1575580218623_775808  ,net/sf/jasperreports/engine/fill/JREvaluator  parameter_REPORT_LOCALE 2Lnet/sf/jasperreports/engine/fill/JRFillParameter; parameter_JASPER_REPORT parameter_REPORT_TIME_ZONE parameter_REPORT_FILE_RESOLVER parameter_REPORT_SCRIPTLET parameter_REPORT_PARAMETERS_MAP parameter_REPORT_CONNECTION parameter_REPORT_CLASS_LOADER parameter_REPORT_DATA_SOURCE $parameter_REPORT_URL_HANDLER_FACTORY parameter_REPORT_FORMAT_FACTORY parameter_REPORT_MAX_COUNT parameter_REPORT_TEMPLATES  parameter_REPORT_RESOURCE_BUNDLE variable_PAGE_NUMBER 1Lnet/sf/jasperreports/engine/fill/JRFillVariable; variable_COLUMN_NUMBER variable_REPORT_COUNT variable_PAGE_COUNT variable_COLUMN_COUNT <init> ()V Code  
    	    	  !  	  # 	 	  % 
 	  '  	  )  	  + 
 	  -  	  /  	  1  	  3  	  5  	  7  	  9  	  ;  	  =  	  ?  	  A  	  C LineNumberTable customizedInit 0(Ljava/util/Map;Ljava/util/Map;Ljava/util/Map;)V 
initParams (Ljava/util/Map;)V H I
  J 
initFields L I
  M initVars O I
  P 
REPORT_LOCALE R 
java/util/Map T get &(Ljava/lang/Object;)Ljava/lang/Object; V W U X 0net/sf/jasperreports/engine/fill/JRFillParameter Z 
JASPER_REPORT \ REPORT_TIME_ZONE ^ REPORT_FILE_RESOLVER ` REPORT_SCRIPTLET b REPORT_PARAMETERS_MAP d REPORT_CONNECTION f REPORT_CLASS_LOADER h REPORT_DATA_SOURCE j REPORT_URL_HANDLER_FACTORY l REPORT_FORMAT_FACTORY n REPORT_MAX_COUNT p REPORT_TEMPLATES r REPORT_RESOURCE_BUNDLE t PAGE_NUMBER v /net/sf/jasperreports/engine/fill/JRFillVariable x 
COLUMN_NUMBER z REPORT_COUNT | 
PAGE_COUNT ~ COLUMN_COUNT  evaluate (I)Ljava/lang/Object; 
Exceptions java/lang/Throwable  java/lang/Integer  (I)V  
   evaluateOld evaluateEstimated 
SourceFile !                      	     
               
                                                                   Ì     d*· *µ  *µ "*µ $*µ &*µ (*µ **µ ,*µ .*µ 0*µ 2*µ 4*µ 6*µ 8*µ :*µ <*µ >*µ @*µ B*µ D±    E   V       	          ! " " ' # , $ 1 % 6 & ; ' @ ( E ) J * O + T , Y - ^ . c   F G     4     *+· K*,· N*-· Q±    E       :  ; 
 <  =  H I    M     ý*+S¹ Y À [À [µ  *+]¹ Y À [À [µ "*+_¹ Y À [À [µ $*+a¹ Y À [À [µ &*+c¹ Y À [À [µ (*+e¹ Y À [À [µ **+g¹ Y À [À [µ ,*+i¹ Y À [À [µ .*+k¹ Y À [À [µ 0*+m¹ Y À [À [µ 2*+o¹ Y À [À [µ 4*+q¹ Y À [À [µ 6*+s¹ Y À [À [µ 8*+u¹ Y À [À [µ :±    E   >    E  F $ G 6 H H I Z J l K ~ L  M ¢ N ´ O Æ P Ø Q ê R ü S  L I           ±    E       [  O I          [*+w¹ Y À yÀ yµ <*+{¹ Y À yÀ yµ >*+}¹ Y À yÀ yµ @*+¹ Y À yÀ yµ B*+¹ Y À yÀ yµ D±    E       c  d $ e 6 f H g Z h              ë     Mª             -   9   E   Q   ]   i   u   » Y· M§ T» Y· M§ H» Y· M§ <» Y· M§ 0» Y· M§ $» Y· M§ » Y· M§ » Y· M,°    E   J    p  r 0 v 9 w < { E | H  Q  T  ]  `  i  l  u  x       ¡              ë     Mª             -   9   E   Q   ]   i   u   » Y· M§ T» Y· M§ H» Y· M§ <» Y· M§ 0» Y· M§ $» Y· M§ » Y· M§ » Y· M,°    E   J    ª  ¬ 0 ° 9 ± < µ E ¶ H º Q » T ¿ ] À ` Ä i Å l É u Ê x Î  Ï  Ó  Û              ë     Mª             -   9   E   Q   ]   i   u   » Y· M§ T» Y· M§ H» Y· M§ <» Y· M§ 0» Y· M§ $» Y· M§ » Y· M§ » Y· M,°    E   J    ä  æ 0 ê 9 ë < ï E ð H ô Q õ T ù ] ú ` þ i ÿ l u x 	 
       xuq ~¨  cÊþº¾   .j (CaixaPorOperadorRel_1575580218623_775808  ,net/sf/jasperreports/engine/fill/JREvaluator  parameter_mostrarModalidade 2Lnet/sf/jasperreports/engine/fill/JRFillParameter; parameter_REPORT_TIME_ZONE parameter_REPORT_PARAMETERS_MAP parameter_qtdCA parameter_REPORT_CLASS_LOADER parameter_REPORT_DATA_SOURCE $parameter_REPORT_URL_HANDLER_FACTORY parameter_IS_IGNORE_PAGINATION parameter_valorChequeAV parameter_qtdBB parameter_REPORT_TEMPLATES parameter_valorOutro parameter_somenteSintetico parameter_dataIni parameter_qtdAV parameter_REPORT_VIRTUALIZER parameter_REPORT_SCRIPTLET parameter_totalizadores parameter_valorDR parameter_tituloRelatorio parameter_valorDV parameter_qtdChequeAV  parameter_REPORT_RESOURCE_BUNDLE parameter_filtros parameter_valorCD parameter_JASPER_REPORT parameter_qtdDV parameter_usuario parameter_valorCA parameter_REPORT_FILE_RESOLVER parameter_SUBREPORT_DIR1 parameter_qtdChequePR parameter_valorChequePR parameter_SUBREPORT_DIR2 parameter_REPORT_MAX_COUNT parameter_qtdDR parameter_REPORT_LOCALE parameter_valorBB parameter_qtdOutro parameter_logoPadraoRelatorio parameter_REPORT_CONNECTION parameter_apresentarDados parameter_SUBREPORT_DIR parameter_dataFim parameter_qtdCD parameter_REPORT_FORMAT_FACTORY parameter_nomeEmpresa parameter_valorAV parameter_devolucoes parameter_moeda parameter_versaoSoftware field_listaDescontosRecibo .Lnet/sf/jasperreports/engine/fill/JRFillField; field_reciboPagamentoVO46codigo field_valorDevolvidoMonetario &field_movProduto46produto46tipoProduto field_centralEventos field_listaMovProduto field_mostrarNumeroContrato 7field_descConfiguracaoVO46porcentagemDescontoApresentar field_movProduto46precoUnitario field_listaMovPagamento field_contrato field_matricula field_devolucao Kfield_reciboPagamentoVO46responsavelLancamento46colaboradorVO46pessoa46nome field_reciboPagamentoVO46data #field_reciboPagamentoVO46valorTotal *field_reciboPagamentoVO46nomePessoaPagador field_apresentarDescontos field_listaMovParcela field_consultorResponsavel field_modalidades field_descricaoDevolucao field_numeroContrato field_nomeOperador variable_PAGE_NUMBER 1Lnet/sf/jasperreports/engine/fill/JRFillVariable; variable_COLUMN_NUMBER variable_REPORT_COUNT variable_PAGE_COUNT variable_COLUMN_COUNT variable_caixaPoOperador_COUNT variable_movParcela_COUNT variable_movProduto_COUNT variable_movPagamento_COUNT <init> ()V Code \ ]
  _  	  a  	  c  	  e 	 	  g 
 	  i  	  k  	  m 
 	  o  	  q  	  s  	  u  	  w  	  y  	  {  	  }  	    	    	    	    	    	    	    	    	    	    	     	   ! 	   " 	   # 	   $ 	   % 	   & 	  ¡ ' 	  £ ( 	  ¥ ) 	  § * 	  © + 	  « , 	  ­ - 	  ¯ . 	  ± / 	  ³ 0 	  µ 1 	  · 2 	  ¹ 3 	  » 4 	  ½ 5 	  ¿ 6 	  Á 7 	  Ã 8 	  Å 9 :	  Ç ; :	  É < :	  Ë = :	  Í > :	  Ï ? :	  Ñ @ :	  Ó A :	  Õ B :	  × C :	  Ù D :	  Û E :	  Ý F :	  ß G :	  á H :	  ã I :	  å J :	  ç K :	  é L :	  ë M :	  í N :	  ï O :	  ñ P :	  ó Q :	  õ R S	  ÷ T S	  ù U S	  û V S	  ý W S	  ÿ X S	  Y S	  Z S	  [ S	  LineNumberTable customizedInit 0(Ljava/util/Map;Ljava/util/Map;Ljava/util/Map;)V 
initParams (Ljava/util/Map;)V
  
initFields
  initVars
  mostrarModalidade 
java/util/Map get &(Ljava/lang/Object;)Ljava/lang/Object; 0net/sf/jasperreports/engine/fill/JRFillParameter REPORT_TIME_ZONE  REPORT_PARAMETERS_MAP" qtdCA$ REPORT_CLASS_LOADER& REPORT_DATA_SOURCE( REPORT_URL_HANDLER_FACTORY* IS_IGNORE_PAGINATION, 
valorChequeAV. qtdBB0 REPORT_TEMPLATES2 
valorOutro4 somenteSintetico6 dataIni8 qtdAV: REPORT_VIRTUALIZER< REPORT_SCRIPTLET> 
totalizadores@ valorDRB tituloRelatorioD valorDVF qtdChequeAVH REPORT_RESOURCE_BUNDLEJ filtrosL valorCDN 
JASPER_REPORTP qtdDVR usuarioT valorCAV REPORT_FILE_RESOLVERX SUBREPORT_DIR1Z qtdChequePR\ 
valorChequePR^ SUBREPORT_DIR2` REPORT_MAX_COUNTb qtdDRd 
REPORT_LOCALEf valorBBh qtdOutroj logoPadraoRelatoriol REPORT_CONNECTIONn apresentarDadosp 
SUBREPORT_DIRr dataFimt qtdCDv REPORT_FORMAT_FACTORYx nomeEmpresaz valorAV| 
devolucoes~ moeda versaoSoftware listaDescontosRecibo ,net/sf/jasperreports/engine/fill/JRFillField reciboPagamentoVO.codigo valorDevolvidoMonetario movProduto.produto.tipoProduto centralEventos listaMovProduto mostrarNumeroContrato 0descConfiguracaoVO.porcentagemDescontoApresentar movProduto.precoUnitario listaMovPagamento contrato 	matricula 	devolucao AreciboPagamentoVO.responsavelLancamento.colaboradorVO.pessoa.nome  reciboPagamentoVO.data¢ reciboPagamentoVO.valorTotal¤ #reciboPagamentoVO.nomePessoaPagador¦ apresentarDescontos¨ listaMovParcelaª consultorResponsavel¬ modalidades® descricaoDevolucao° numeroContrato² nomeOperador´ PAGE_NUMBER¶ /net/sf/jasperreports/engine/fill/JRFillVariable¸ 
COLUMN_NUMBERº REPORT_COUNT¼ 
PAGE_COUNT¾ COLUMN_COUNTÀ caixaPoOperador_COUNTÂ movParcela_COUNTÄ movProduto_COUNTÆ movPagamento_COUNTÈ evaluate (I)Ljava/lang/Object; 
Exceptions java/lang/ThrowableÍ eE:\desenvolvimentosv\Projeto Pacto Solucoes\ZillyonWeb\src\java\relatorio\designRelatorio\financeiro\Ï R$Ñ java/lang/IntegerÓ (I)V \Õ
ÔÖ getValue ()Ljava/lang/Object;ØÙ
Ú
Ú java/lang/BooleanÝ booleanValue ()Zßà
Þá valueOf (Z)Ljava/lang/Boolean;ãä
Þå java/lang/Stringç java/util/Dateé java/lang/StringBufferë
ì _  î 
Resp_Recebð str &(Ljava/lang/String;)Ljava/lang/String;òó
 ô append ,(Ljava/lang/String;)Ljava/lang/StringBuffer;ö÷
ìø toString ()Ljava/lang/String;úû
ìü Resp_Devolucaoþ Consultor_Resp  	Contrato: (Ljava/lang/String;)V \
ì ,(Ljava/lang/Object;)Ljava/lang/StringBuffer;ö
ì  
 	Devolucao equals (Ljava/lang/Object;)Z
Þ java/util/ResourceBundle (net/sf/jasperreports/engine/JRDataSource &(Ljava/lang/Object;)Ljava/lang/String;ã
è MovParcela.jasper Calculo_Devolucao Produtos_Recibo MovProduto.jasper Desc_Extra_Convenio! Descontos.jasper# MovPagamento.jasper% Pag_Recibos' Valor_Devolvido) DT_Ent_Caixa+
ê _ Titulo. java/io/InputStream0 Recibo2 	Matricula4 
Nome_Resp_Pag6 Desconto8 Qtd: Unitario< 
Valor_Pago> 
Nome_Aluno@ ContratoB ProdutoD 	evaluate1FË
 G Forma_PgI Data_PagamentoK 	UsuÃ¡rio:M PÃ¡gina O
¹Ú  deR PacoteT java/lang/DoubleV CaixaReciboDevolucao.jasperX evaluateOld getOldValue[Ù
\ evaluateOld1^Ë
 _
¹\ evaluateEstimated evaluateEstimated1cË
 d getEstimatedValuefÙ
¹g 
SourceFile !     T                 	     
               
                                                                                                     !     "     #     $     %     &     '     (     )     *     +     ,     -     .     /     0     1     2     3     4     5     6     7     8     9 :    ; :    < :    = :    > :    ? :    @ :    A :    B :    C :    D :    E :    F :    G :    H :    I :    J :    K :    L :    M :    N :    O :    P :    Q :    R S    T S    U S    V S    W S    X S    Y S    Z S    [ S     \ ]  ^      ©*· `*µ b*µ d*µ f*µ h*µ j*µ l*µ n*µ p*µ r*µ t*µ v*µ x*µ z*µ |*µ ~*µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ  *µ ¢*µ ¤*µ ¦*µ ¨*µ ª*µ ¬*µ ®*µ °*µ ²*µ ´*µ ¶*µ ¸*µ º*µ ¼*µ ¾*µ À*µ Â*µ Ä*µ Æ*µ È*µ Ê*µ Ì*µ Î*µ Ð*µ Ò*µ Ô*µ Ö*µ Ø*µ Ú*µ Ü*µ Þ*µ à*µ â*µ ä*µ æ*µ è*µ ê*µ ì*µ î*µ ð*µ ò*µ ô*µ ö*µ ø*µ ú*µ ü*µ þ*µ *µ*µ*µ*µ±   	  Z V      	          ! " " ' # , $ 1 % 6 & ; ' @ ( E ) J * O + T , Y - ^ . c / h 0 m 1 r 2 w 3 | 4  5  6  7  8  9  :  ; ¤ < © = ® > ³ ? ¸ @ ½ A Â B Ç C Ì D Ñ E Ö F Û G à H å I ê J ï K ô L ù M þ N O P
 Q R S T! U& V+ W0 X5 Y: Z? [D \I ]N ^S _X `] ab bg cl dq ev f{ g h i j k l m n£ o¨  
  ^   4     *+·*,·*-·±   	       {  | 
 }  ~ 
  ^  ®    Ê*+¹ ÀÀµ b*+!¹ ÀÀµ d*+#¹ ÀÀµ f*+%¹ ÀÀµ h*+'¹ ÀÀµ j*+)¹ ÀÀµ l*++¹ ÀÀµ n*+-¹ ÀÀµ p*+/¹ ÀÀµ r*+1¹ ÀÀµ t*+3¹ ÀÀµ v*+5¹ ÀÀµ x*+7¹ ÀÀµ z*+9¹ ÀÀµ |*+;¹ ÀÀµ ~*+=¹ ÀÀµ *+?¹ ÀÀµ *+A¹ ÀÀµ *+C¹ ÀÀµ *+E¹ ÀÀµ *+G¹ ÀÀµ *+I¹ ÀÀµ *+K¹ ÀÀµ *+M¹ ÀÀµ *+O¹ ÀÀµ *+Q¹ ÀÀµ *+S¹ ÀÀµ *+U¹ ÀÀµ *+W¹ ÀÀµ *+Y¹ ÀÀµ *+[¹ ÀÀµ *+]¹ ÀÀµ  *+_¹ ÀÀµ ¢*+a¹ ÀÀµ ¤*+c¹ ÀÀµ ¦*+e¹ ÀÀµ ¨*+g¹ ÀÀµ ª*+i¹ ÀÀµ ¬*+k¹ ÀÀµ ®*+m¹ ÀÀµ °*+o¹ ÀÀµ ²*+q¹ ÀÀµ ´*+s¹ ÀÀµ ¶*+u¹ ÀÀµ ¸*+w¹ ÀÀµ º*+y¹ ÀÀµ ¼*+{¹ ÀÀµ ¾*+}¹ ÀÀµ À*+¹ ÀÀµ Â*+¹ ÀÀµ Ä*+¹ ÀÀµ Æ±   	   Ò 4      &  9  L  _  r      «  ¾  Ñ  ä  ÷ 
  0 C V i |  ¢ µ È Û î   ¡ ¢' £: ¤M ¥` ¦s § ¨ ©¬ ª¿ «Ò ¬å ­ø ® ¯ °1 ±D ²W ³j ´} µ ¶£ ·¶ ¸É ¹ 
  ^  A    É*+¹ ÀÀµ È*+¹ ÀÀµ Ê*+¹ ÀÀµ Ì*+¹ ÀÀµ Î*+¹ ÀÀµ Ð*+¹ ÀÀµ Ò*+¹ ÀÀµ Ô*+¹ ÀÀµ Ö*+¹ ÀÀµ Ø*+¹ ÀÀµ Ú*+¹ ÀÀµ Ü*+¹ ÀÀµ Þ*+¹ ÀÀµ à*+¡¹ ÀÀµ â*+£¹ ÀÀµ ä*+¥¹ ÀÀµ æ*+§¹ ÀÀµ è*+©¹ ÀÀµ ê*+«¹ ÀÀµ ì*+­¹ ÀÀµ î*+¯¹ ÀÀµ ð*+±¹ ÀÀµ ò*+³¹ ÀÀµ ô*+µ¹ ÀÀµ ö±   	   f    Á  Â & Ã 9 Ä L Å _ Æ r Ç  È  É « Ê ¾ Ë Ñ Ì ä Í ÷ Î
 Ï Ð0 ÑC ÒV Ói Ô| Õ Ö¢ ×µ ØÈ Ù 
  ^   è     ¬*+·¹ À¹À¹µ ø*+»¹ À¹À¹µ ú*+½¹ À¹À¹µ ü*+¿¹ À¹À¹µ þ*+Á¹ À¹À¹µ *+Ã¹ À¹À¹µ*+Å¹ À¹À¹µ*+Ç¹ À¹À¹µ*+É¹ À¹À¹µ±   	   * 
   á  â & ã 9 ä L å _ æ r ç  è  é « ê ÊË Ì    Î ^  
{    
/Mª  
$       c    ¤  «  ²  ¹  Å  Ñ  Ý  é  õ    
    %  1  =  I  U  a  m  y    ³  Á  Ï  Ý  ë    '  Y    Ø    6  D  O  ]  k  y      Ï  ë    M  [      ¤  ²  À  á    A  O      ¢  °  ¾  ß  ä    "  0  ;  I  W  x  ¦  ô      &  1  ?  M  [  w      ©  Å  Ð  ì  ÷  	  	  	:  	E  	a  	l  	  	  	¯  	º  	Ö  	á  	ý  
ÐM§ÐM§ÐM§{ÒM§t»ÔY·×M§h»ÔY·×M§\»ÔY·×M§P»ÔY·×M§D»ÔY·×M§8»ÔY·×M§,»ÔY·×M§ »ÔY·×M§»ÔY·×M§»ÔY·×M§ü»ÔY·×M§ð»ÔY·×M§ä»ÔY·×M§Ø»ÔY·×M§Ì»ÔY·×M§À»ÔY·×M§´*´ Ê¶ÛÀÔM§¦*´ ´¶ÜÀÞ¶â *´ z¶ÜÀÞ¶â § ¸æM§z*´ ö¶ÛÀèM§l*´ è¶ÛÀèM§^*´ Þ¶ÛÀèM§P*´ ä¶ÛÀêM§B»ìY·í*´ à¶ÛÀÞ¶â 	ï§ 
*ñ¶õ¶ù¶ýM§*´ î¶ÛÀèM§»ìY·í*´ à¶ÛÀÞ¶â 
*ÿ¶õ§ 
*¶õ¶ù¶ýM§Ô»ìY·í*´ à¶ÛÀÞ¶â  »ìY·*´ Ü¶ÛÀÔ¶	¶ý§ ¶ù¶ýM§»ìY·í*´ à¶ÛÀÞ¶â 	§ »ìY·í*´ Ê¶ÛÀÔ¶	¶ý¶ù¶ýM§U»ìY·í*´ à¶ÛÀÞ¶â 
*
¶õ§ ¶ù¶ýM§'*´ ´¶ÜÀÞ¶â *´ z¶ÜÀÞ¸æ¶ § ¸æM§÷*´ ¶¶ÜÀèM§é*´ Ò¶ÛM§Þ*´ ¶ÜÀM§Ð*´ Ä¶ÜÀèM§Â*´ ì¶ÛÀM§´»ìY*´ ¤¶ÜÀè¸·¶ù¶ýM§M§*´ ´¶ÜÀÞ¶â *´ z¶ÜÀÞ¸æ¶ § ¸æM§^*´ Ð¶ÛÀÞ¶â § ¸æM§B»ìY·í*´ à¶ÛÀÞ¶â 
*¶õ§ 
*¶õ¶ù¶ýM§*´ ´¶ÜÀÞ¶â *´ z¶ÜÀÞ¸æ¶ § ¸æM§à*´ ò¶ÛÀèM§Ò*´ ´¶ÜÀÞ¶â *´ z¶ÜÀÞ¸æ¶ § ¸æM§¢*´ ¶¶ÜÀèM§*´ Ò¶ÛM§*´ ¶ÜÀM§{*´ Ò¶ÛÀM§m»ìY*´ ¶ÜÀè¸· ¶ù¶ýM§L*´ ´¶ÜÀÞ¶â *´ z¶ÜÀÞ¸æ¶ § ¸æM§*´ ´¶ÜÀÞ¶â *´ z¶ÜÀÞ¸æ¶ § ¸æM§ì*´ ð¶ÛÀèM§Þ*´ ´¶ÜÀÞ¶â *´ z¶ÜÀÞ¸æ¶ § ¸æM§®*´ ê¶ÛÀÞ¸æ¶¸æM§*"¶õM§*´ ¶ÜÀM§}*´ È¶ÛÀM§o»ìY*´ ¶¶ÜÀè¸·$¶ù¶ýM§NM§I*´ ´¶ÜÀÞ¶â *´ z¶ÜÀÞ¸æ¶ § ¸æM§*´ ¶¶ÜÀèM§*´ ¶ÜÀM§ý*´ Ú¶ÛM§ò*´ Ä¶ÜÀèM§ä*´ Ú¶ÛÀM§Ö»ìY*´ ¶¶ÜÀè¸·&¶ù¶ýM§µ»ìY·í*´ à¶ÛÀÞ¶â 	§ 
*(¶õ¶ù¶ýM§»ìY·í*´ à¶ÛÀÞ¶â -»ìY**¶õ¸·ï¶ù*´ Ì¶ÛÀè¶ù¶ý§ ¶ù¶ýM§9*´ z¶ÜÀÞ¶â § ¸æM§*,¶õM§»êY·-M§*/¶õM§ü*´ |¶ÜÀèM§î*´ ¸¶ÜÀèM§à*´ °¶ÜÀ1M§Ò*´ z¶ÜÀÞ¶â § ¸æM§¶*3¶õM§«*´ z¶ÜÀÞ¶â § ¸æM§*5¶õM§*´ z¶ÜÀÞ¶â § ¸æM§h*7¶õM§]*´ z¶ÜÀÞ¶â § ¸æM§A*9¶õM§6*´ z¶ÜÀÞ¶â § ¸æM§*;¶õM§*´ z¶ÜÀÞ¶â § ¸æM§ ó*=¶õM§ è*´ z¶ÜÀÞ¶â § ¸æM§ Ì*?¶õM§ Á*´ z¶ÜÀÞ¶â § ¸æM§ ¥*A¶õM§ *´ z¶ÜÀÞ¶â § ¸æM§ ~*C¶õM§ s*´ z¶ÜÀÞ¶â § ¸æM§ W*E¶õM§ L*´ z¶ÜÀÞ¶â § ¸æM§ 0*?¶õM§ %*´ z¶ÜÀÞ¶â § ¸æM§ 	*·HM,°   	  : Î   ò  ô  ø¤ ù§ ý« þ®²µ¹¼Å
ÈÑÔÝàéì õ!ø%&*
+/04%5(91:4>=?@CIDLHUIXMaNdRmSpWyX|\]a³b¶fÁgÄkÏlÒpÝqàuëvîz{'*Y\ØÛ	69DG¢O£R§]¨`¬k­n±y²|¶·»¼¢ÀÏÁÒÅëÆîÊË ÏMÐPÔ[Õ^ÙÚÞßã¤ä§è²éµíÀîÃòáóä÷øüAýDOR¢¥°³¾Áß â$ä%ç)*."/%30438;9>=I>LBWCZGxH{L¦M©Q×RðQôS÷WX\]a&b)f1g4k?lBpMqPu[v^zw{z¡©¬ÅÈÐÓìï÷ú¢	£	§	¨	!¬	:­	=±	E²	H¶	a·	d»	l¼	oÀ	Á	Å	Æ	Ê	¯Ë	²Ï	ºÐ	½Ô	ÖÕ	ÙÙ	áÚ	äÞ	ýß
 ã
ä
è
$é
'í
-ñ FË Ì    Î ^  N    ²Mª  ­   d      Í   Ø   ô   ÿ    A  O  ]  y         ®  ¼  Ê  Õ  ã  ñ  ÿ  
    )  7  E  S  a  o  }      §  ²  À  Î  Ü  ê  ø      "  0  >  I  W  e  s    *J¶õM§Ø*´ z¶ÜÀÞ¶â § ¸æM§¼*L¶õM§±»ìYN·*´ ¶ÜÀè¶ù¶ýM§»ìYP·*´ ø¶QÀÔ¶	S¶ù¶ýM§o*´ ø¶QÀÔM§a*´ ¶ÜÀèM§S*´ z¶ÜÀÞ¶â § ¸æM§7*U¶õM§,*´ ¾¶ÜÀèM§*´ ¶ÜÀWM§*´ ¶ÜÀèM§*´ ¶ÜÀèM§ô*´ ¶ÜÀWM§æ*´ ¶ÜM§Û*´ h¶ÜÀèM§Í*´ ¶ÜÀèM§¿*´ r¶ÜÀWM§±*´  ¶ÜÀèM§£*´ ¢¶ÜÀWM§*´ ¤¶ÜÀèM§*´ t¶ÜÀèM§y*´ x¶ÜÀWM§k*´ z¶ÜÀÞM§]*´ ¨¶ÜÀèM§O*´ ~¶ÜÀèM§A*´ |¶ÜÀèM§3*´ ¬¶ÜÀWM§%*´ ®¶ÜÀèM§*´ °¶ÜÀ1M§	*´ ¶ÜM§ þ*´ ´¶ÜÀÞM§ ð*´ ¶¶ÜÀèM§ â*´ º¶ÜÀèM§ Ô*´ ¸¶ÜÀèM§ Æ*´ ¶ÜÀWM§ ¸*´ ¶ÜÀèM§ ª*´ ¾¶ÜÀèM§ *´ ¶ÜÀWM§ *´ ¶ÜÀèM§ *´ À¶ÜÀWM§ r*´ Â¶ÜM§ g*´ Ä¶ÜÀèM§ Y*´ ¶ÜÀM§ K*´ Æ¶ÜÀèM§ =*´ ¶ÜÀèM§ /*´ Â¶ÜÀM§ !»ìY*´ ¶¶ÜÀè¸·Y¶ù¶ýM,°   	   b  ú ü Ð  Ø Û ô ÷
 ÿ ADOR]`#y$|()-.2 3£7®8±<¼=¿AÊBÍFÕGØKãLæPñQôUÿVZ
[_`d)e,i7j:nEoHsStVxayd}o~r}§ª²µÀÃ Î¡Ñ¥Ü¦ßªê«í¯ø°û´µ	¹º¾"¿%Ã0Ä3È>ÉAÍIÎLÒWÓZ×eØhÜsÝváâæçë°ó ZË Ì    Î ^  
{    
/Mª  
$       c    ¤  «  ²  ¹  Å  Ñ  Ý  é  õ    
    %  1  =  I  U  a  m  y    ³  Á  Ï  Ý  ë    '  Y    Ø    6  D  O  ]  k  y      Ï  ë    M  [      ¤  ²  À  á    A  O      ¢  °  ¾  ß  ä    "  0  ;  I  W  x  ¦  ô      &  1  ?  M  [  w      ©  Å  Ð  ì  ÷  	  	  	:  	E  	a  	l  	  	  	¯  	º  	Ö  	á  	ý  
ÐM§ÐM§ÐM§{ÒM§t»ÔY·×M§h»ÔY·×M§\»ÔY·×M§P»ÔY·×M§D»ÔY·×M§8»ÔY·×M§,»ÔY·×M§ »ÔY·×M§»ÔY·×M§»ÔY·×M§ü»ÔY·×M§ð»ÔY·×M§ä»ÔY·×M§Ø»ÔY·×M§Ì»ÔY·×M§À»ÔY·×M§´*´ Ê¶]ÀÔM§¦*´ ´¶ÜÀÞ¶â *´ z¶ÜÀÞ¶â § ¸æM§z*´ ö¶]ÀèM§l*´ è¶]ÀèM§^*´ Þ¶]ÀèM§P*´ ä¶]ÀêM§B»ìY·í*´ à¶]ÀÞ¶â 	ï§ 
*ñ¶õ¶ù¶ýM§*´ î¶]ÀèM§»ìY·í*´ à¶]ÀÞ¶â 
*ÿ¶õ§ 
*¶õ¶ù¶ýM§Ô»ìY·í*´ à¶]ÀÞ¶â  »ìY·*´ Ü¶]ÀÔ¶	¶ý§ ¶ù¶ýM§»ìY·í*´ à¶]ÀÞ¶â 	§ »ìY·í*´ Ê¶]ÀÔ¶	¶ý¶ù¶ýM§U»ìY·í*´ à¶]ÀÞ¶â 
*
¶õ§ ¶ù¶ýM§'*´ ´¶ÜÀÞ¶â *´ z¶ÜÀÞ¸æ¶ § ¸æM§÷*´ ¶¶ÜÀèM§é*´ Ò¶]M§Þ*´ ¶ÜÀM§Ð*´ Ä¶ÜÀèM§Â*´ ì¶]ÀM§´»ìY*´ ¤¶ÜÀè¸·¶ù¶ýM§M§*´ ´¶ÜÀÞ¶â *´ z¶ÜÀÞ¸æ¶ § ¸æM§^*´ Ð¶]ÀÞ¶â § ¸æM§B»ìY·í*´ à¶]ÀÞ¶â 
*¶õ§ 
*¶õ¶ù¶ýM§*´ ´¶ÜÀÞ¶â *´ z¶ÜÀÞ¸æ¶ § ¸æM§à*´ ò¶]ÀèM§Ò*´ ´¶ÜÀÞ¶â *´ z¶ÜÀÞ¸æ¶ § ¸æM§¢*´ ¶¶ÜÀèM§*´ Ò¶]M§*´ ¶ÜÀM§{*´ Ò¶]ÀM§m»ìY*´ ¶ÜÀè¸· ¶ù¶ýM§L*´ ´¶ÜÀÞ¶â *´ z¶ÜÀÞ¸æ¶ § ¸æM§*´ ´¶ÜÀÞ¶â *´ z¶ÜÀÞ¸æ¶ § ¸æM§ì*´ ð¶]ÀèM§Þ*´ ´¶ÜÀÞ¶â *´ z¶ÜÀÞ¸æ¶ § ¸æM§®*´ ê¶]ÀÞ¸æ¶¸æM§*"¶õM§*´ ¶ÜÀM§}*´ È¶]ÀM§o»ìY*´ ¶¶ÜÀè¸·$¶ù¶ýM§NM§I*´ ´¶ÜÀÞ¶â *´ z¶ÜÀÞ¸æ¶ § ¸æM§*´ ¶¶ÜÀèM§*´ ¶ÜÀM§ý*´ Ú¶]M§ò*´ Ä¶ÜÀèM§ä*´ Ú¶]ÀM§Ö»ìY*´ ¶¶ÜÀè¸·&¶ù¶ýM§µ»ìY·í*´ à¶]ÀÞ¶â 	§ 
*(¶õ¶ù¶ýM§»ìY·í*´ à¶]ÀÞ¶â -»ìY**¶õ¸·ï¶ù*´ Ì¶]Àè¶ù¶ý§ ¶ù¶ýM§9*´ z¶ÜÀÞ¶â § ¸æM§*,¶õM§»êY·-M§*/¶õM§ü*´ |¶ÜÀèM§î*´ ¸¶ÜÀèM§à*´ °¶ÜÀ1M§Ò*´ z¶ÜÀÞ¶â § ¸æM§¶*3¶õM§«*´ z¶ÜÀÞ¶â § ¸æM§*5¶õM§*´ z¶ÜÀÞ¶â § ¸æM§h*7¶õM§]*´ z¶ÜÀÞ¶â § ¸æM§A*9¶õM§6*´ z¶ÜÀÞ¶â § ¸æM§*;¶õM§*´ z¶ÜÀÞ¶â § ¸æM§ ó*=¶õM§ è*´ z¶ÜÀÞ¶â § ¸æM§ Ì*?¶õM§ Á*´ z¶ÜÀÞ¶â § ¸æM§ ¥*A¶õM§ *´ z¶ÜÀÞ¶â § ¸æM§ ~*C¶õM§ s*´ z¶ÜÀÞ¶â § ¸æM§ W*E¶õM§ L*´ z¶ÜÀÞ¶â § ¸æM§ 0*?¶õM§ %*´ z¶ÜÀÞ¶â § ¸æM§ 	*·`M,°   	  : Î  ü þ ¤§«®²
µ¹¼ÅÈÑÔ Ý!à%é&ì*õ+ø/04
59:>%?(C1D4H=I@MINLRUSXWaXd\m]payb|fgk³l¶pÁqÄuÏvÒzÝ{àëî'*Y\ØÛ	¢6£9§D¨G¬O­R±]²`¶k·n»y¼|ÀÁÅÆ¢ÊÏËÒÏëÐîÔÕ ÙMÚPÞ[ß^ãäèéí¤î§ò²óµ÷ÀøÃüáýäADOR¢¥° ³$¾%Á)ß*â.ä/ç348"9%=0>3B;C>GIHLLWMZQxR{V¦W©[×\ð[ô]÷abfgk&l)p1q4u?vBzM{P[^wz¡©¬ÅÈÐÓ¢ì£ï§÷¨ú¬	­	±	²	!¶	:·	=»	E¼	HÀ	aÁ	dÅ	lÆ	oÊ	Ë	Ï	Ð	Ô	¯Õ	²Ù	ºÚ	½Þ	Öß	Ùã	áä	äè	ýé
 í
î
ò
$ó
'÷
-û ^Ë Ì    Î ^  N    ²Mª  ­   d      Í   Ø   ô   ÿ    A  O  ]  y         ®  ¼  Ê  Õ  ã  ñ  ÿ  
    )  7  E  S  a  o  }      §  ²  À  Î  Ü  ê  ø      "  0  >  I  W  e  s    *J¶õM§Ø*´ z¶ÜÀÞ¶â § ¸æM§¼*L¶õM§±»ìYN·*´ ¶ÜÀè¶ù¶ýM§»ìYP·*´ ø¶aÀÔ¶	S¶ù¶ýM§o*´ ø¶aÀÔM§a*´ ¶ÜÀèM§S*´ z¶ÜÀÞ¶â § ¸æM§7*U¶õM§,*´ ¾¶ÜÀèM§*´ ¶ÜÀWM§*´ ¶ÜÀèM§*´ ¶ÜÀèM§ô*´ ¶ÜÀWM§æ*´ ¶ÜM§Û*´ h¶ÜÀèM§Í*´ ¶ÜÀèM§¿*´ r¶ÜÀWM§±*´  ¶ÜÀèM§£*´ ¢¶ÜÀWM§*´ ¤¶ÜÀèM§*´ t¶ÜÀèM§y*´ x¶ÜÀWM§k*´ z¶ÜÀÞM§]*´ ¨¶ÜÀèM§O*´ ~¶ÜÀèM§A*´ |¶ÜÀèM§3*´ ¬¶ÜÀWM§%*´ ®¶ÜÀèM§*´ °¶ÜÀ1M§	*´ ¶ÜM§ þ*´ ´¶ÜÀÞM§ ð*´ ¶¶ÜÀèM§ â*´ º¶ÜÀèM§ Ô*´ ¸¶ÜÀèM§ Æ*´ ¶ÜÀWM§ ¸*´ ¶ÜÀèM§ ª*´ ¾¶ÜÀèM§ *´ ¶ÜÀWM§ *´ ¶ÜÀèM§ *´ À¶ÜÀWM§ r*´ Â¶ÜM§ g*´ Ä¶ÜÀèM§ Y*´ ¶ÜÀM§ K*´ Æ¶ÜÀèM§ =*´ ¶ÜÀèM§ /*´ Â¶ÜÀM§ !»ìY*´ ¶¶ÜÀè¸·Y¶ù¶ýM,°   	   b    Ð
 Ø Û ô ÷ ÿ AD#O$R(])`-y.|2378< =£A®B±F¼G¿KÊLÍPÕQØUãVæZñ[ô_ÿ`d
eijn)o,s7t:xEyH}S~Vador}§ª ²¡µ¥À¦ÃªÎ«Ñ¯Ü°ß´êµí¹øºû¾¿	ÃÄÈ"É%Í0Î3Ò>ÓA×IØLÜWÝZáeâhæsçvëìðñõ°ý bË Ì    Î ^  
{    
/Mª  
$       c    ¤  «  ²  ¹  Å  Ñ  Ý  é  õ    
    %  1  =  I  U  a  m  y    ³  Á  Ï  Ý  ë    '  Y    Ø    6  D  O  ]  k  y      Ï  ë    M  [      ¤  ²  À  á    A  O      ¢  °  ¾  ß  ä    "  0  ;  I  W  x  ¦  ô      &  1  ?  M  [  w      ©  Å  Ð  ì  ÷  	  	  	:  	E  	a  	l  	  	  	¯  	º  	Ö  	á  	ý  
ÐM§ÐM§ÐM§{ÒM§t»ÔY·×M§h»ÔY·×M§\»ÔY·×M§P»ÔY·×M§D»ÔY·×M§8»ÔY·×M§,»ÔY·×M§ »ÔY·×M§»ÔY·×M§»ÔY·×M§ü»ÔY·×M§ð»ÔY·×M§ä»ÔY·×M§Ø»ÔY·×M§Ì»ÔY·×M§À»ÔY·×M§´*´ Ê¶ÛÀÔM§¦*´ ´¶ÜÀÞ¶â *´ z¶ÜÀÞ¶â § ¸æM§z*´ ö¶ÛÀèM§l*´ è¶ÛÀèM§^*´ Þ¶ÛÀèM§P*´ ä¶ÛÀêM§B»ìY·í*´ à¶ÛÀÞ¶â 	ï§ 
*ñ¶õ¶ù¶ýM§*´ î¶ÛÀèM§»ìY·í*´ à¶ÛÀÞ¶â 
*ÿ¶õ§ 
*¶õ¶ù¶ýM§Ô»ìY·í*´ à¶ÛÀÞ¶â  »ìY·*´ Ü¶ÛÀÔ¶	¶ý§ ¶ù¶ýM§»ìY·í*´ à¶ÛÀÞ¶â 	§ »ìY·í*´ Ê¶ÛÀÔ¶	¶ý¶ù¶ýM§U»ìY·í*´ à¶ÛÀÞ¶â 
*
¶õ§ ¶ù¶ýM§'*´ ´¶ÜÀÞ¶â *´ z¶ÜÀÞ¸æ¶ § ¸æM§÷*´ ¶¶ÜÀèM§é*´ Ò¶ÛM§Þ*´ ¶ÜÀM§Ð*´ Ä¶ÜÀèM§Â*´ ì¶ÛÀM§´»ìY*´ ¤¶ÜÀè¸·¶ù¶ýM§M§*´ ´¶ÜÀÞ¶â *´ z¶ÜÀÞ¸æ¶ § ¸æM§^*´ Ð¶ÛÀÞ¶â § ¸æM§B»ìY·í*´ à¶ÛÀÞ¶â 
*¶õ§ 
*¶õ¶ù¶ýM§*´ ´¶ÜÀÞ¶â *´ z¶ÜÀÞ¸æ¶ § ¸æM§à*´ ò¶ÛÀèM§Ò*´ ´¶ÜÀÞ¶â *´ z¶ÜÀÞ¸æ¶ § ¸æM§¢*´ ¶¶ÜÀèM§*´ Ò¶ÛM§*´ ¶ÜÀM§{*´ Ò¶ÛÀM§m»ìY*´ ¶ÜÀè¸· ¶ù¶ýM§L*´ ´¶ÜÀÞ¶â *´ z¶ÜÀÞ¸æ¶ § ¸æM§*´ ´¶ÜÀÞ¶â *´ z¶ÜÀÞ¸æ¶ § ¸æM§ì*´ ð¶ÛÀèM§Þ*´ ´¶ÜÀÞ¶â *´ z¶ÜÀÞ¸æ¶ § ¸æM§®*´ ê¶ÛÀÞ¸æ¶¸æM§*"¶õM§*´ ¶ÜÀM§}*´ È¶ÛÀM§o»ìY*´ ¶¶ÜÀè¸·$¶ù¶ýM§NM§I*´ ´¶ÜÀÞ¶â *´ z¶ÜÀÞ¸æ¶ § ¸æM§*´ ¶¶ÜÀèM§*´ ¶ÜÀM§ý*´ Ú¶ÛM§ò*´ Ä¶ÜÀèM§ä*´ Ú¶ÛÀM§Ö»ìY*´ ¶¶ÜÀè¸·&¶ù¶ýM§µ»ìY·í*´ à¶ÛÀÞ¶â 	§ 
*(¶õ¶ù¶ýM§»ìY·í*´ à¶ÛÀÞ¶â -»ìY**¶õ¸·ï¶ù*´ Ì¶ÛÀè¶ù¶ý§ ¶ù¶ýM§9*´ z¶ÜÀÞ¶â § ¸æM§*,¶õM§»êY·-M§*/¶õM§ü*´ |¶ÜÀèM§î*´ ¸¶ÜÀèM§à*´ °¶ÜÀ1M§Ò*´ z¶ÜÀÞ¶â § ¸æM§¶*3¶õM§«*´ z¶ÜÀÞ¶â § ¸æM§*5¶õM§*´ z¶ÜÀÞ¶â § ¸æM§h*7¶õM§]*´ z¶ÜÀÞ¶â § ¸æM§A*9¶õM§6*´ z¶ÜÀÞ¶â § ¸æM§*;¶õM§*´ z¶ÜÀÞ¶â § ¸æM§ ó*=¶õM§ è*´ z¶ÜÀÞ¶â § ¸æM§ Ì*?¶õM§ Á*´ z¶ÜÀÞ¶â § ¸æM§ ¥*A¶õM§ *´ z¶ÜÀÞ¶â § ¸æM§ ~*C¶õM§ s*´ z¶ÜÀÞ¶â § ¸æM§ W*E¶õM§ L*´ z¶ÜÀÞ¶â § ¸æM§ 0*?¶õM§ %*´ z¶ÜÀÞ¶â § ¸æM§ 	*·eM,°   	  : Î    ¤
§«®²µ¹¼ Å!È%Ñ&Ô*Ý+à/é0ì4õ5ø9:>
?CDH%I(M1N4R=S@WIXL\U]Xaabdfmgpkyl|pqu³v¶zÁ{ÄÏÒÝàëî'*Y\¢Ø£Û§¨	¬6­9±D²G¶O·R»]¼`ÀkÁnÅyÆ|ÊËÏÐ¢ÔÏÕÒÙëÚîÞß ãMäPè[é^íîòó÷¤ø§ü²ýµÀÃáäADOR $¢%¥)°*³.¾/Á3ß4â8ä9ç=>B"C%G0H3L;M>QIRLVWWZ[x\{`¦a©e×fðeôg÷klpqu&v)z1{4?BMP[^wz¡©¬¢Å£È§Ð¨Ó¬ì­ï±÷²ú¶	·	»	¼	!À	:Á	=Å	EÆ	HÊ	aË	dÏ	lÐ	oÔ	Õ	Ù	Ú	Þ	¯ß	²ã	ºä	½è	Öé	Ùí	áî	äò	ýó
 ÷
ø
ü
$ý
'	
-	 cË Ì    Î ^  N    ²Mª  ­   d      Í   Ø   ô   ÿ    A  O  ]  y         ®  ¼  Ê  Õ  ã  ñ  ÿ  
    )  7  E  S  a  o  }      §  ²  À  Î  Ü  ê  ø      "  0  >  I  W  e  s    *J¶õM§Ø*´ z¶ÜÀÞ¶â § ¸æM§¼*L¶õM§±»ìYN·*´ ¶ÜÀè¶ù¶ýM§»ìYP·*´ ø¶hÀÔ¶	S¶ù¶ýM§o*´ ø¶hÀÔM§a*´ ¶ÜÀèM§S*´ z¶ÜÀÞ¶â § ¸æM§7*U¶õM§,*´ ¾¶ÜÀèM§*´ ¶ÜÀWM§*´ ¶ÜÀèM§*´ ¶ÜÀèM§ô*´ ¶ÜÀWM§æ*´ ¶ÜM§Û*´ h¶ÜÀèM§Í*´ ¶ÜÀèM§¿*´ r¶ÜÀWM§±*´  ¶ÜÀèM§£*´ ¢¶ÜÀWM§*´ ¤¶ÜÀèM§*´ t¶ÜÀèM§y*´ x¶ÜÀWM§k*´ z¶ÜÀÞM§]*´ ¨¶ÜÀèM§O*´ ~¶ÜÀèM§A*´ |¶ÜÀèM§3*´ ¬¶ÜÀWM§%*´ ®¶ÜÀèM§*´ °¶ÜÀ1M§	*´ ¶ÜM§ þ*´ ´¶ÜÀÞM§ ð*´ ¶¶ÜÀèM§ â*´ º¶ÜÀèM§ Ô*´ ¸¶ÜÀèM§ Æ*´ ¶ÜÀWM§ ¸*´ ¶ÜÀèM§ ª*´ ¾¶ÜÀèM§ *´ ¶ÜÀWM§ *´ ¶ÜÀèM§ *´ À¶ÜÀWM§ r*´ Â¶ÜM§ g*´ Ä¶ÜÀèM§ Y*´ ¶ÜÀM§ K*´ Æ¶ÜÀèM§ =*´ ¶ÜÀèM§ /*´ Â¶ÜÀM§ !»ìY*´ ¶¶ÜÀè¸·Y¶ù¶ýM,°   	   b  	 	 Ð	 Ø	 Û	 ô	 ÷	 ÿ		#	$ 	(A	)D	-O	.R	2]	3`	7y	8|	<	=	A	B	F 	G£	K®	L±	P¼	Q¿	UÊ	VÍ	ZÕ	[Ø	_ã	`æ	dñ	eô	iÿ	j	n
	o	s	t	x)	y,	}7	~:	E	H	S	V	a	d	o	r	}				 	¡	¥§	¦ª	ª²	«µ	¯À	°Ã	´Î	µÑ	¹Ü	ºß	¾ê	¿í	Ãø	Äû	È	É		Í	Î	Ò"	Ó%	×0	Ø3	Ü>	ÝA	áI	âL	æW	çZ	ëe	ìh	ðs	ñv	õ	ö	ú	û	ÿ°
 i    t _1575580218623_775808t 2net.sf.jasperreports.engine.design.JRJavacCompiler
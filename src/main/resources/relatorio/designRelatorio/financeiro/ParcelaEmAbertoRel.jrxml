<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="ParcelaEmAbertoRel" pageWidth="680" pageHeight="878" columnWidth="625" leftMargin="19" rightMargin="36" topMargin="2" bottomMargin="2" whenResourceMissingType="Empty">
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.encoding" value="ISO-8859-1"/>
	<property name="ireport.zoom" value="1.815000000000003"/>
	<property name="ireport.x" value="218"/>
	<property name="ireport.y" value="0"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="java.util.*"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<subDataset name="Teste"/>
	<parameter name="logoPadraoRelatorio" class="java.io.InputStream" isForPrompting="false"/>
	<parameter name="tituloRelatorio" class="java.lang.String" isForPrompting="false"/>
	<parameter name="nomeEmpresa" class="java.lang.String" isForPrompting="false"/>
	<parameter name="versaoSoftware" class="java.lang.String" isForPrompting="false"/>
	<parameter name="usuario" class="java.lang.String" isForPrompting="false"/>
	<parameter name="filtros" class="java.lang.String" isForPrompting="false"/>
	<parameter name="SUBREPORT_DIR" class="java.lang.String">
		<defaultValueExpression><![CDATA["E:\\desenvolvimentosv\\Projeto Pacto Solucoes\\ZillyonWeb\\src\\java\\relatorio\\designRelatorio\\financeiro\\"]]></defaultValueExpression>
	</parameter>
	<parameter name="SUBREPORT_DIR1" class="java.lang.String">
		<defaultValueExpression><![CDATA["E:\\desenvolvimentosv\\Projeto Pacto Solucoes\\ZillyonWeb\\src\\java\\relatorio\\designRelatorio\\financeiro\\"]]></defaultValueExpression>
	</parameter>
	<parameter name="dataIni" class="java.lang.String" isForPrompting="false"/>
	<parameter name="dataFim" class="java.lang.String" isForPrompting="false"/>
	<parameter name="qtdAV" class="java.lang.String" isForPrompting="false"/>
	<parameter name="qtdCA" class="java.lang.String" isForPrompting="false"/>
	<parameter name="qtdChequeAV" class="java.lang.String" isForPrompting="false"/>
	<parameter name="qtdChequePR" class="java.lang.String" isForPrompting="false"/>
	<parameter name="qtdOutro" class="java.lang.String" isForPrompting="false"/>
	<parameter name="valorAV" class="java.lang.Double" isForPrompting="false"/>
	<parameter name="valorCA" class="java.lang.Double" isForPrompting="false"/>
	<parameter name="valorChequeAV" class="java.lang.Double" isForPrompting="false"/>
	<parameter name="valorChequePR" class="java.lang.Double" isForPrompting="false"/>
	<parameter name="valorOutro" class="java.lang.Double" isForPrompting="false"/>
	<parameter name="parametro1" class="java.lang.String" isForPrompting="false"/>
	<parameter name="parametro2" class="java.lang.String" isForPrompting="false"/>
	<parameter name="parametro3" class="java.lang.String" isForPrompting="false"/>
	<parameter name="parametro4" class="java.lang.String" isForPrompting="false"/>
	<parameter name="parametro5" class="java.lang.String" isForPrompting="false"/>
	<parameter name="parametro6" class="java.lang.String" isForPrompting="false"/>
	<parameter name="mostrarcampo" class="java.lang.Boolean" isForPrompting="false"/>
	<field name="pessoa_nome" class="java.lang.String">
		<fieldDescription><![CDATA[ParcelaEmDemandaRel/registros+pessoa_nome]]></fieldDescription>
	</field>
	<field name="cliente_matricula" class="java.lang.String">
		<fieldDescription><![CDATA[ParcelaEmDemandaRel/registros+cliente_matricula]]></fieldDescription>
	</field>
	<field name="parcela_descricao" class="java.lang.String">
		<fieldDescription><![CDATA[ParcelaEmDemandaRel/registros+parcela_descricao]]></fieldDescription>
	</field>
	<field name="parcela_dataregistro" class="java.lang.String">
		<fieldDescription><![CDATA[ParcelaEmDemandaRel/registros+parcela_dataregistro]]></fieldDescription>
	</field>
	<field name="parcela_datavencimento" class="java.lang.String">
		<fieldDescription><![CDATA[ParcelaEmDemandaRel/registros+parcela_datavencimento]]></fieldDescription>
	</field>
	<field name="parcela_contrato" class="java.lang.String">
		<fieldDescription><![CDATA[ParcelaEmDemandaRel/registros+parcela_contrato]]></fieldDescription>
	</field>
	<field name="parcela_situacao" class="java.lang.String">
		<fieldDescription><![CDATA[ParcelaEmDemandaRel/registros+parcela_situacao]]></fieldDescription>
	</field>
	<field name="parcela_valorparcela" class="java.lang.String">
		<fieldDescription><![CDATA[ParcelaEmDemandaRel/registros+parcela_valorparcela]]></fieldDescription>
	</field>
	<field name="movparcela_codigo" class="java.lang.String">
		<fieldDescription><![CDATA[ParcelaEmDemandaRel/registros+movparcela_codigo]]></fieldDescription>
	</field>
	<field name="regime_recorrencia" class="java.lang.String">
		<fieldDescription><![CDATA[ParcelaEmDemandaRel/registros+regime_recorrencia]]></fieldDescription>
	</field>
	<field name="situacao_cliente" class="java.lang.String">
		<fieldDescription><![CDATA[ParcelaEmDemandaRel/registros+situacao_cliente]]></fieldDescription>
	</field>
	<field name="situacao_contrato" class="java.lang.String">
		<fieldDescription><![CDATA[ParcelaEmDemandaRel/registros+situacao_contrato]]></fieldDescription>
	</field>
	<field name="total" class="java.lang.String">
		<fieldDescription><![CDATA[ParcelaEmDemandaRel/registros+total]]></fieldDescription>
	</field>
	<field name="juros" class="java.lang.String">
		<fieldDescription><![CDATA[ParcelaEmDemandaRel/registros+juros]]></fieldDescription>
	</field>
	<field name="multa" class="java.lang.String">
		<fieldDescription><![CDATA[ParcelaEmDemandaRel/registros+multa]]></fieldDescription>
	</field>
	<variable name="variable1" class="java.lang.String"/>
	<variable name="vartotal" class="java.lang.Double" resetType="Group" resetGroup="nomeCliente" calculation="Sum">
		<variableExpression><![CDATA[new Double($F{parcela_valorparcela})]]></variableExpression>
		<initialValueExpression><![CDATA[0.0]]></initialValueExpression>
	</variable>
	<group name="nomeCliente">
		<groupExpression><![CDATA[$F{cliente_matricula}]]></groupExpression>
		<groupHeader>
			<band height="39" splitType="Stretch">
				<line>
					<reportElement key="line-1" x="1" y="4" width="624" height="1"/>
				</line>
				<staticText>
					<reportElement key="staticText-1" x="215" y="6" width="32" height="14"/>
					<textElement>
						<font isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<text><![CDATA[Nome]]></text>
				</staticText>
				<staticText>
					<reportElement key="staticText-2" x="0" y="6" width="61" height="14"/>
					<textElement>
						<font isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<text><![CDATA[Mat. Cliente]]></text>
				</staticText>
				<textField isBlankWhenNull="false">
					<reportElement key="textField-217" x="247" y="6" width="165" height="14"/>
					<textElement/>
					<textFieldExpression class="java.lang.String"><![CDATA[$F{pessoa_nome}]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="false">
					<reportElement key="textField-218" x="61" y="6" width="154" height="14"/>
					<textElement/>
					<textFieldExpression class="java.lang.String"><![CDATA[$F{cliente_matricula}]]></textFieldExpression>
				</textField>
				<line>
					<reportElement key="line-2" x="1" y="22" width="624" height="1"/>
				</line>
				<staticText>
					<reportElement key="staticText-3" x="44" y="23" width="87" height="14"/>
					<textElement>
						<font isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<text><![CDATA[Desc. Parcela]]></text>
				</staticText>
				<staticText>
					<reportElement key="staticText-4" stretchType="RelativeToTallestObject" x="131" y="23" width="94" height="14" isPrintWhenDetailOverflows="true"/>
					<textElement>
						<font isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<text><![CDATA[Dt. Faturamento]]></text>
				</staticText>
				<staticText>
					<reportElement key="staticText-5" x="225" y="25" width="91" height="14"/>
					<textElement>
						<font isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<text><![CDATA[Dt. Vencimento]]></text>
				</staticText>
				<staticText>
					<reportElement key="staticText-6" x="316" y="25" width="62" height="14"/>
					<textElement>
						<font isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<text><![CDATA[Nº Contrato]]></text>
				</staticText>
				<staticText>
					<reportElement key="staticText-7" x="378" y="25" width="74" height="14"/>
					<textElement>
						<font isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<text><![CDATA[Situação]]></text>
				</staticText>
				<staticText>
					<reportElement key="staticText-8" x="519" y="25" width="34" height="14"/>
					<textElement>
						<font isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<text><![CDATA[Valor]]></text>
				</staticText>
				<staticText>
					<reportElement key="staticText-19" x="0" y="23" width="43" height="14"/>
					<textElement>
						<font isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<text><![CDATA[Cod.]]></text>
				</staticText>
				<staticText>
					<reportElement key="staticText-1" x="452" y="25" width="67" height="14"/>
					<textElement>
						<font isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<text><![CDATA[Recorrência]]></text>
				</staticText>
				<staticText>
					<reportElement key="staticText-1" x="413" y="6" width="48" height="14">
						<printWhenExpression><![CDATA[!$F{situacao_cliente}.isEmpty()]]></printWhenExpression>
					</reportElement>
					<textElement>
						<font isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<text><![CDATA[Situação:]]></text>
				</staticText>
				<textField isBlankWhenNull="true">
					<reportElement key="textField-217" x="462" y="6" width="22" height="14"/>
					<textElement/>
					<textFieldExpression class="java.lang.String"><![CDATA[$F{situacao_cliente}]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement key="textField-217" x="484" y="6" width="138" height="14"/>
					<textElement/>
					<textFieldExpression class="java.lang.String"><![CDATA[$F{situacao_contrato}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement key="staticText-8" x="552" y="25" width="34" height="14">
						<printWhenExpression><![CDATA[$P{mostrarcampo}]]></printWhenExpression>
					</reportElement>
					<textElement>
						<font isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<text><![CDATA[Multa]]></text>
				</staticText>
				<staticText>
					<reportElement key="staticText-8" x="586" y="25" width="34" height="14">
						<printWhenExpression><![CDATA[$P{mostrarcampo}]]></printWhenExpression>
					</reportElement>
					<textElement>
						<font isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<text><![CDATA[Juros]]></text>
				</staticText>
			</band>
		</groupHeader>
		<groupFooter>
			<band height="18" splitType="Stretch">
				<line>
					<reportElement key="line-2" x="1" y="1" width="624" height="1"/>
				</line>
				<staticText>
					<reportElement key="staticText-1" x="520" y="2" width="33" height="14"/>
					<textElement>
						<font isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<text><![CDATA[Total:]]></text>
				</staticText>
				<textField pattern="#,##0.00" isBlankWhenNull="true">
					<reportElement key="textField-217" x="553" y="2" width="69" height="14"/>
					<textElement/>
					<textFieldExpression class="java.lang.Double"><![CDATA[$V{vartotal}]]></textFieldExpression>
				</textField>
			</band>
		</groupFooter>
	</group>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band splitType="Stretch"/>
	</title>
	<pageHeader>
		<band height="62" splitType="Stretch">
			<image isUsingCache="true" onErrorType="Blank" evaluationTime="Page">
				<reportElement key="image-1" x="1" y="1" width="82" height="36" isPrintWhenDetailOverflows="true"/>
				<box>
					<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<imageExpression class="java.io.InputStream"><![CDATA[$P{logoPadraoRelatorio}]]></imageExpression>
			</image>
			<textField pattern="dd/MM/yyyy HH.mm.ss" isBlankWhenNull="false">
				<reportElement key="dataRel-1" mode="Opaque" x="520" y="1" width="105" height="19" backcolor="#FFFFFF"/>
				<box bottomPadding="2">
					<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Verdana" size="8" isItalic="false" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression class="java.util.Date"><![CDATA[new Date()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-2" x="83" y="1" width="437" height="19"/>
				<box>
					<pen lineWidth="0.5" lineStyle="Solid"/>
					<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid"/>
				</box>
				<textElement textAlignment="Center">
					<font fontName="Arial" size="14" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{tituloRelatorio}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-25" x="520" y="20" width="60" height="17"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right">
					<font fontName="Arial"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA["Pág: " + $V{PAGE_NUMBER} + " de "]]></textFieldExpression>
			</textField>
			<textField evaluationTime="Report" isBlankWhenNull="false">
				<reportElement key="textField-26" x="580" y="20" width="45" height="17"/>
				<box bottomPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement>
					<font fontName="Arial"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[" " + $V{PAGE_NUMBER} + ""]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-216" x="83" y="20" width="437" height="37"/>
				<box>
					<leftPen lineWidth="0.5" lineStyle="Solid"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.5" lineStyle="Solid"/>
				</box>
				<textElement textAlignment="Center">
					<font fontName="Arial" size="10" isBold="true" isItalic="true" pdfFontName="Helvetica-BoldOblique"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{filtros}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="135" y="37" width="100" height="20"/>
				<textElement/>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{tituloRelatorio}]]></textFieldExpression>
			</textField>
		</band>
	</pageHeader>
	<columnHeader>
		<band splitType="Stretch"/>
	</columnHeader>
	<detail>
		<band height="15" splitType="Stretch">
			<textField isBlankWhenNull="false">
				<reportElement key="textField-219" x="44" y="0" width="87" height="14"/>
				<textElement/>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{parcela_descricao}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-220" x="131" y="0" width="94" height="14"/>
				<textElement/>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{parcela_dataregistro}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-221" x="225" y="1" width="91" height="14"/>
				<textElement/>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{parcela_datavencimento}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-222" x="317" y="1" width="61" height="14"/>
				<textElement/>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{parcela_contrato}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-223" x="378" y="1" width="73" height="14"/>
				<textElement/>
				<textFieldExpression class="java.lang.String"><![CDATA[( $F{parcela_situacao}.equals("EA") ? "Em Aberto" :
( $F{parcela_situacao}.equals("PG") ? "Pago" : "Cancelado" ))]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00" isBlankWhenNull="false">
				<reportElement key="textField-224" x="519" y="1" width="33" height="14"/>
				<textElement/>
				<textFieldExpression class="java.lang.Double"><![CDATA[new Double($F{parcela_valorparcela})]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-233" x="1" y="1" width="43" height="14"/>
				<textElement/>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{movparcela_codigo}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-219" x="453" y="1" width="67" height="14"/>
				<textElement/>
				<textFieldExpression class="java.lang.String"><![CDATA[($F{regime_recorrencia}.equals("t") ? "Sim":"Não")]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00" isBlankWhenNull="false">
				<reportElement key="textField-224" x="553" y="1" width="33" height="14">
					<printWhenExpression><![CDATA[$P{mostrarcampo}]]></printWhenExpression>
				</reportElement>
				<textElement/>
				<textFieldExpression class="java.lang.Double"><![CDATA[new Double($F{multa})]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00" isBlankWhenNull="false">
				<reportElement key="textField-224" x="586" y="1" width="33" height="14">
					<printWhenExpression><![CDATA[$P{mostrarcampo}]]></printWhenExpression>
				</reportElement>
				<textElement/>
				<textFieldExpression class="java.lang.Double"><![CDATA[new Double($F{juros}  )]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<columnFooter>
		<band splitType="Stretch"/>
	</columnFooter>
	<pageFooter>
		<band splitType="Stretch"/>
	</pageFooter>
	<lastPageFooter>
		<band height="168" splitType="Stretch">
			<textField pattern="" isBlankWhenNull="false">
				<reportElement key="textField-207" x="1" y="147" width="621" height="13"/>
				<box>
					<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="8" isItalic="true" pdfFontName="Helvetica-Oblique"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[" "+" Usuário:" + $P{usuario}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement key="staticText-9" x="107" y="43" width="58" height="14"/>
				<textElement>
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Em Aberto:]]></text>
			</staticText>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-225" x="170" y="43" width="150" height="14"/>
				<textElement/>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{parametro1}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00" isBlankWhenNull="false">
				<reportElement key="textField-226" x="333" y="43" width="200" height="14"/>
				<textElement/>
				<textFieldExpression class="java.lang.Double"><![CDATA[new Double ($P{parametro2})]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement key="staticText-13" x="107" y="61" width="58" height="14"/>
				<textElement>
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Pago:]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-14" x="107" y="79" width="58" height="14"/>
				<textElement>
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Cancelado:]]></text>
			</staticText>
			<textField pattern="#,##0.00" isBlankWhenNull="false">
				<reportElement key="textField-229" x="333" y="61" width="200" height="14"/>
				<textElement/>
				<textFieldExpression class="java.lang.Double"><![CDATA[new Double ($P{parametro4})]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-230" x="170" y="61" width="150" height="14"/>
				<textElement/>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{parametro3}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-231" x="170" y="79" width="150" height="14"/>
				<textElement/>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{parametro5}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement key="staticText-17" x="169" y="21" width="102" height="14"/>
				<textElement>
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Quantidade Parcelas]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-18" x="332" y="21" width="102" height="14"/>
				<textElement>
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Valor Total Parcelas]]></text>
			</staticText>
			<textField pattern="#,##0.00" isBlankWhenNull="false">
				<reportElement key="textField-232" x="333" y="79" width="200" height="14"/>
				<textElement/>
				<textFieldExpression class="java.lang.Double"><![CDATA[new Double ($P{parametro6})]]></textFieldExpression>
			</textField>
		</band>
	</lastPageFooter>
	<summary>
		<band splitType="Stretch"/>
	</summary>
</jasperReport>

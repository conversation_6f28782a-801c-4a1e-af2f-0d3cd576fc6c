<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="SaldosContas" pageWidth="625" pageHeight="100" columnWidth="625" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" whenResourceMissingType="Empty">
	<property name="ireport.zoom" value="1.8150000000000348"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.encoding" value="ISO-8859-1"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="java.util.*"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<parameter name="tituloRelatorio" class="java.lang.String" isForPrompting="false"/>
	<parameter name="nomeEmpresa" class="java.lang.String" isForPrompting="false"/>
	<parameter name="versaoSoftware" class="java.lang.String" isForPrompting="false"/>
	<parameter name="usuario" class="java.lang.String" isForPrompting="false"/>
	<parameter name="filtros" class="java.lang.String" isForPrompting="false"/>
	<parameter name="SUBREPORT_DIR" class="java.lang.String">
		<defaultValueExpression><![CDATA["E:\\desenvolvimentosv\\Projeto Pacto Solucoes\\ZillyonWeb\\src\\java\\relatorio\\designRelatorio\\financeiro\\"]]></defaultValueExpression>
	</parameter>
	<parameter name="SUBREPORT_DIR1" class="java.lang.String">
		<defaultValueExpression><![CDATA["E:\\desenvolvimentosv\\Projeto Pacto Solucoes\\ZillyonWeb\\src\\java\\relatorio\\designRelatorio\\financeiro\\"]]></defaultValueExpression>
	</parameter>
	<parameter name="logoPadraoRelatorio" class="java.io.InputStream" isForPrompting="false"/>
	<parameter name="SUBREPORT_DIR2" class="java.lang.String">
		<defaultValueExpression><![CDATA["E:\\desenvolvimentosv\\Projeto Pacto Solucoes\\ZillyonWeb\\src\\java\\relatorio\\designRelatorio\\financeiro\\"]]></defaultValueExpression>
	</parameter>
	<parameter name="dataFim" class="java.lang.String"/>
	<parameter name="dataIni" class="java.lang.String"/>
	<parameter name="exibirAutorizacao" class="java.lang.Boolean"/>
	<field name="descricao" class="java.lang.String"/>
	<field name="listaMovimentacoesJR" class="java.lang.Object"/>
	<field name="inicialApresentar" class="java.lang.String"/>
	<field name="saldoAtualApresentar" class="java.lang.String"/>
	<field name="temMovimentacoes" class="java.lang.Boolean"/>
	<detail>
		<band height="35">
			<printWhenExpression><![CDATA[$F{temMovimentacoes}]]></printWhenExpression>
			<textField>
				<reportElement x="0" y="11" width="348" height="20"/>
				<textElement>
					<font fontName="Arial" size="12" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{descricao}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="505" y="15" width="120" height="20"/>
				<textElement textAlignment="Right" verticalAlignment="Bottom">
					<font fontName="Arial" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{inicialApresentar}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="405" y="15" width="100" height="20"/>
				<textElement verticalAlignment="Bottom">
					<font fontName="Arial"/>
				</textElement>
				<text><![CDATA[Saldo Inicial (R$)]]></text>
			</staticText>
		</band>
		<band height="37">
			<printWhenExpression><![CDATA[$F{temMovimentacoes}]]></printWhenExpression>
			<subreport>
				<reportElement x="0" y="1" width="625" height="34" isPrintWhenDetailOverflows="true"/>
				<subreportParameter name="tituloRelatorio"/>
				<subreportParameter name="nomeEmpresa"/>
				<subreportParameter name="versaoSoftware"/>
				<subreportParameter name="usuario"/>
				<subreportParameter name="filtros"/>
				<subreportParameter name="SUBREPORT_DIR"/>
				<subreportParameter name="SUBREPORT_DIR1"/>
				<subreportParameter name="logoPadraoRelatorio"/>
				<subreportParameter name="SUBREPORT_DIR2"/>
				<subreportParameter name="dataFim"/>
				<subreportParameter name="dataIni"/>
				<subreportParameter name="exibirAutorizacao"/>
				<dataSourceExpression><![CDATA[$F{listaMovimentacoesJR}]]></dataSourceExpression>
				<subreportExpression class="java.lang.String"><![CDATA[$P{SUBREPORT_DIR} + "SaldosContasDetalhado.jasper"]]></subreportExpression>
			</subreport>
		</band>
		<band height="36">
			<printWhenExpression><![CDATA[$F{temMovimentacoes}]]></printWhenExpression>
			<staticText>
				<reportElement x="405" y="4" width="100" height="20"/>
				<textElement>
					<font fontName="Arial"/>
				</textElement>
				<text><![CDATA[Saldo Final (R$)]]></text>
			</staticText>
			<textField>
				<reportElement x="505" y="4" width="120" height="20"/>
				<textElement textAlignment="Right">
					<font fontName="Arial" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{saldoAtualApresentar}]]></textFieldExpression>
			</textField>
			<line>
				<reportElement positionType="Float" x="0" y="2" width="625" height="1" forecolor="#CCCCCC"/>
			</line>
			<line>
				<reportElement positionType="Float" x="0" y="32" width="625" height="1" forecolor="#E6E4E4"/>
			</line>
		</band>
	</detail>
</jasperReport>

<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="ParcelaEmAbertoRel" pageWidth="878" pageHeight="680" orientation="Landscape" columnWidth="823" leftMargin="19" rightMargin="36" topMargin="2" bottomMargin="2" whenResourceMissingType="Empty">
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.encoding" value="ISO-8859-1"/>
	<property name="ireport.zoom" value="1.5"/>
	<property name="ireport.x" value="426"/>
	<property name="ireport.y" value="0"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="java.util.*"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<parameter name="logoPadraoRelatorio" class="java.io.InputStream" isForPrompting="false"/>
	<parameter name="tituloRelatorio" class="java.lang.String" isForPrompting="false"/>
	<parameter name="nomeEmpresa" class="java.lang.String" isForPrompting="false"/>
	<parameter name="versaoSoftware" class="java.lang.String" isForPrompting="false"/>
	<parameter name="usuario" class="java.lang.String" isForPrompting="false"/>
	<parameter name="filtros" class="java.lang.String" isForPrompting="false"/>
	<parameter name="SUBREPORT_DIR" class="java.lang.String">
		<defaultValueExpression><![CDATA["E:\\desenvolvimentosv\\Projeto Pacto Solucoes\\ZillyonWeb\\src\\java\\relatorio\\designRelatorio\\financeiro\\"]]></defaultValueExpression>
	</parameter>
	<parameter name="SUBREPORT_DIR1" class="java.lang.String">
		<defaultValueExpression><![CDATA["E:\\desenvolvimentosv\\Projeto Pacto Solucoes\\ZillyonWeb\\src\\java\\relatorio\\designRelatorio\\financeiro\\"]]></defaultValueExpression>
	</parameter>
	<parameter name="dataIni" class="java.lang.String" isForPrompting="false"/>
	<parameter name="dataFim" class="java.lang.String" isForPrompting="false"/>
	<parameter name="qtdAV" class="java.lang.String" isForPrompting="false"/>
	<parameter name="qtdCA" class="java.lang.String" isForPrompting="false"/>
	<parameter name="qtdChequeAV" class="java.lang.String" isForPrompting="false"/>
	<parameter name="qtdChequePR" class="java.lang.String" isForPrompting="false"/>
	<parameter name="qtdOutro" class="java.lang.String" isForPrompting="false"/>
	<parameter name="valorAV" class="java.lang.Double" isForPrompting="false"/>
	<parameter name="valorCA" class="java.lang.Double" isForPrompting="false"/>
	<parameter name="valorChequeAV" class="java.lang.Double" isForPrompting="false"/>
	<parameter name="valorChequePR" class="java.lang.Double" isForPrompting="false"/>
	<parameter name="valorOutro" class="java.lang.Double" isForPrompting="false"/>
	<parameter name="parametro1" class="java.lang.String" isForPrompting="false"/>
	<parameter name="parametro2" class="java.lang.String" isForPrompting="false"/>
	<parameter name="parametro3" class="java.lang.String" isForPrompting="false"/>
	<parameter name="parametro4" class="java.lang.String" isForPrompting="false"/>
	<parameter name="parametro5" class="java.lang.String" isForPrompting="false"/>
	<parameter name="parametro6" class="java.lang.String" isForPrompting="false"/>
	<parameter name="parametro7" class="java.lang.String" isForPrompting="false"/>
	<parameter name="mostrarcampo" class="java.lang.Boolean" isForPrompting="false"/>
	<parameter name="totaljuromulta" class="java.lang.String" isForPrompting="false"/>
	<parameter name="apresentarDadosSensiveis" class="java.lang.Boolean" isForPrompting="false"/>
	<field name="pessoa_nome" class="java.lang.String">
		<fieldDescription><![CDATA[ParcelaEmDemandaRel/registros+pessoa_nome]]></fieldDescription>
	</field>
	<field name="cliente_matricula" class="java.lang.String">
		<fieldDescription><![CDATA[ParcelaEmDemandaRel/registros+cliente_matricula]]></fieldDescription>
	</field>
	<field name="parcela_descricao" class="java.lang.String">
		<fieldDescription><![CDATA[ParcelaEmDemandaRel/registros+parcela_descricao]]></fieldDescription>
	</field>
	<field name="parcela_dataregistro" class="java.lang.String">
		<fieldDescription><![CDATA[ParcelaEmDemandaRel/registros+parcela_dataregistro]]></fieldDescription>
	</field>
	<field name="parcela_datavencimento" class="java.lang.String">
		<fieldDescription><![CDATA[ParcelaEmDemandaRel/registros+parcela_datavencimento]]></fieldDescription>
	</field>
	<field name="parcela_contrato" class="java.lang.String">
		<fieldDescription><![CDATA[ParcelaEmDemandaRel/registros+parcela_contrato]]></fieldDescription>
	</field>
	<field name="parcela_situacao" class="java.lang.String">
		<fieldDescription><![CDATA[ParcelaEmDemandaRel/registros+parcela_situacao]]></fieldDescription>
	</field>
	<field name="parcela_valorparcela" class="java.lang.String">
		<fieldDescription><![CDATA[ParcelaEmDemandaRel/registros+parcela_valorparcela]]></fieldDescription>
	</field>
	<field name="movparcela_codigo" class="java.lang.String">
		<fieldDescription><![CDATA[ParcelaEmDemandaRel/registros+movparcela_codigo]]></fieldDescription>
	</field>
	<field name="regime_recorrencia" class="java.lang.String">
		<fieldDescription><![CDATA[ParcelaEmDemandaRel/registros+regime_recorrencia]]></fieldDescription>
	</field>
	<field name="situacao_cliente" class="java.lang.String">
		<fieldDescription><![CDATA[ParcelaEmDemandaRel/registros+situacao_cliente]]></fieldDescription>
	</field>
	<field name="situacao_contrato" class="java.lang.String">
		<fieldDescription><![CDATA[ParcelaEmDemandaRel/registros+situacao_contrato]]></fieldDescription>
	</field>
	<field name="total" class="java.lang.String">
		<fieldDescription><![CDATA[ParcelaEmDemandaRel/registros+total]]></fieldDescription>
	</field>
	<field name="datapagamento" class="java.lang.String">
		<fieldDescription><![CDATA[ParcelaEmDemandaRel/registros+datapagamento]]></fieldDescription>
	</field>
	<field name="multa" class="java.lang.String">
		<fieldDescription><![CDATA[ParcelaEmDemandaRel/registros+multa]]></fieldDescription>
	</field>
	<field name="juros" class="java.lang.String">
		<fieldDescription><![CDATA[ParcelaEmDemandaRel/registros+juros]]></fieldDescription>
	</field>
	<field name="telefone" class="java.lang.String">
		<fieldDescription><![CDATA[ParcelaEmDemandaRel/registros+telefone]]></fieldDescription>
	</field>
	<field name="primeiroTelefone" class="java.lang.String">
		<fieldDescription><![CDATA[ParcelaEmDemandaRel/registros+primeiro_telefone]]></fieldDescription>
	</field>
	<field name="pessoa_cpf" class="java.lang.String">
		<fieldDescription><![CDATA[ParcelaEmDemandaRel/registros+pessoa_cpf]]></fieldDescription>
	</field>
	<field name="formas_pagamento" class="java.lang.String">
		<fieldDescription><![CDATA[ParcelaEmDemandaRel/registros+formas_pagamento]]></fieldDescription>
	</field>
	<field name="modalidades" class="java.lang.String">
		<fieldDescription><![CDATA[ParcelaEmDemandaRel/registros+modalidades]]></fieldDescription>
	</field>
	<field name="endereco" class="java.lang.String">
		<fieldDescription><![CDATA[ParcelaEmDemandaRel/registros+endereco]]></fieldDescription>
	</field>
	<variable name="variable1" class="java.lang.String"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band splitType="Stretch"/>
	</title>
	<pageHeader>
		<band height="62" splitType="Stretch">
			<image isUsingCache="true" onErrorType="Blank" evaluationTime="Page">
				<reportElement key="image-1" x="1" y="1" width="82" height="45" isPrintWhenDetailOverflows="true"/>
				<box>
					<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<imageExpression class="java.io.InputStream"><![CDATA[$P{logoPadraoRelatorio}]]></imageExpression>
			</image>
			<textField pattern="dd/MM/yyyy HH.mm.ss" isBlankWhenNull="false">
				<reportElement key="dataRel-1" mode="Opaque" x="718" y="1" width="105" height="19" backcolor="#FFFFFF"/>
				<box bottomPadding="2">
					<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Verdana" size="8" isItalic="false" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression class="java.util.Date"><![CDATA[new Date()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-2" x="83" y="1" width="635" height="19"/>
				<box>
					<pen lineWidth="0.5" lineStyle="Solid"/>
					<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid"/>
				</box>
				<textElement textAlignment="Center">
					<font fontName="Arial" size="14" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{tituloRelatorio}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-25" x="718" y="20" width="60" height="26"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right">
					<font fontName="Arial"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA["Pág: " + $V{PAGE_NUMBER} + " de "]]></textFieldExpression>
			</textField>
			<textField evaluationTime="Report" isBlankWhenNull="false">
				<reportElement key="textField-26" x="778" y="20" width="45" height="26"/>
				<box bottomPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement>
					<font fontName="Arial"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[" " + $V{PAGE_NUMBER} + ""]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-216" x="83" y="20" width="635" height="26"/>
				<box>
					<leftPen lineWidth="0.5" lineStyle="Solid"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.5" lineStyle="Solid"/>
				</box>
				<textElement textAlignment="Center">
					<font fontName="Arial" size="9" isBold="true" isItalic="true" pdfFontName="Helvetica-BoldOblique"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{filtros}]]></textFieldExpression>
			</textField>
		</band>
	</pageHeader>
	<columnHeader>
		<band height="19" splitType="Stretch">
			<line>
				<reportElement key="line-1" x="1" y="0" width="822" height="1"/>
			</line>
			<staticText>
				<reportElement key="staticText-1" x="42" y="1" width="198" height="14"/>
				<textElement>
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Nome]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-2" x="0" y="1" width="42" height="14"/>
				<textElement>
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Mat.]]></text>
			</staticText>
			<line>
				<reportElement key="line-2" x="1" y="18" width="822" height="1"/>
			</line>
			<staticText>
				<reportElement key="staticText-1" x="675" y="1" width="32" height="14"/>
				<textElement>
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Recor.]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-3" x="242" y="1" width="84" height="14"/>
				<textElement>
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Celular]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-6" x="326" y="0" width="49" height="14"/>
				<textElement>
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Contrato]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-3" x="375" y="0" width="98" height="14"/>
				<textElement>
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Parcela]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-7" x="473" y="0" width="40" height="14"/>
				<textElement>
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Sit.]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-8" x="513" y="1" width="34" height="14"/>
				<textElement textAlignment="Right">
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Valor]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-8" x="605" y="1" width="34" height="14"/>
				<textElement textAlignment="Right">
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Multa]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-8" x="639" y="1" width="34" height="14"/>
				<textElement textAlignment="Right">
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Juros]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-4" stretchType="RelativeToTallestObject" x="707" y="1" width="58" height="14" isPrintWhenDetailOverflows="true"/>
				<textElement textAlignment="Center">
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Dt. Fatur.]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-5" x="765" y="1" width="58" height="14"/>
				<textElement textAlignment="Center">
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Dt. Venc.]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-5" x="547" y="1" width="58" height="14"/>
				<textElement textAlignment="Center">
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Dt. Pgto.]]></text>
			</staticText>
		</band>
	</columnHeader>
	<detail>
		<band height="15" splitType="Stretch">
			<textField isBlankWhenNull="false">
				<reportElement key="textField-218" x="1" y="1" width="41" height="14"/>
				<textElement/>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{cliente_matricula}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-217" x="44" y="1" width="196" height="14"/>
				<textElement/>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{pessoa_nome}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-219" x="675" y="1" width="32" height="14"/>
				<textElement textAlignment="Center"/>
				<textFieldExpression class="java.lang.String"><![CDATA[($F{regime_recorrencia}.equals("t") ? "Sim":"Não")]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-217" x="242" y="1" width="84" height="14"/>
				<textElement/>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{primeiroTelefone}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-222" x="326" y="1" width="49" height="14"/>
				<textElement/>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{parcela_contrato}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-219" x="375" y="1" width="98" height="14"/>
				<textElement/>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{parcela_descricao}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-223" x="473" y="1" width="40" height="14"/>
				<textElement/>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{parcela_situacao}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00" isBlankWhenNull="false">
				<reportElement key="textField-224" x="513" y="1" width="34" height="14"/>
				<textElement textAlignment="Right"/>
				<textFieldExpression class="java.lang.Double"><![CDATA[new Double($F{parcela_valorparcela})]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00" isBlankWhenNull="false">
				<reportElement key="textField-224" x="605" y="1" width="34" height="14"/>
				<textElement textAlignment="Right"/>
				<textFieldExpression class="java.lang.Double"><![CDATA[new Double($F{multa})]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00" isBlankWhenNull="false">
				<reportElement key="textField-224" x="639" y="1" width="34" height="14"/>
				<textElement textAlignment="Right"/>
				<textFieldExpression class="java.lang.Double"><![CDATA[new Double($F{juros}  )]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-220" x="707" y="1" width="58" height="14"/>
				<textElement textAlignment="Center"/>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{parcela_dataregistro}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-221" x="765" y="1" width="58" height="14"/>
				<textElement textAlignment="Center"/>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{parcela_datavencimento}]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="false">
				<reportElement key="textField-221" x="547" y="1" width="58" height="14"/>
				<textElement textAlignment="Center"/>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{datapagamento}]]></textFieldExpression>
			</textField>
		</band>
		<band height="15" splitType="Stretch">
			<printWhenExpression><![CDATA[$P{apresentarDadosSensiveis}.equals( true )]]></printWhenExpression>
			<staticText>
				<reportElement key="staticText-2" x="343" y="1" width="40" height="14"/>
				<textElement>
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Formas:]]></text>
			</staticText>
			<textField isStretchWithOverflow="true" isBlankWhenNull="false">
				<reportElement key="textField-217" x="382" y="1" width="146" height="14"/>
				<textElement/>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{formas_pagamento}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement key="staticText-2" x="3" y="1" width="24" height="14"/>
				<textElement>
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[CPF:]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-2" x="104" y="1" width="71" height="14"/>
				<textElement>
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Modalidade(s):]]></text>
			</staticText>
			<textField isStretchWithOverflow="true" isBlankWhenNull="false">
				<reportElement key="textField-217" x="174" y="1" width="165" height="14"/>
				<textElement/>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{modalidades}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-217" x="27" y="1" width="72" height="14"/>
				<textElement/>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{pessoa_cpf}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement key="staticText-2" x="538" y="1" width="26" height="13"/>
				<textElement>
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[End.:]]></text>
			</staticText>
			<textField isStretchWithOverflow="true" isBlankWhenNull="false">
				<reportElement key="textField-217" x="563" y="1" width="260" height="14"/>
				<textElement/>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{endereco}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<lastPageFooter>
		<band height="202" splitType="Prevent">
			<textField pattern="" isBlankWhenNull="false">
				<reportElement key="textField-207" x="1" y="177" width="822" height="13"/>
				<box>
					<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="8" isItalic="true" pdfFontName="Helvetica-Oblique"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[" "+" Usuário:" + $P{usuario}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement key="staticText-9" x="169" y="73" width="70" height="14"/>
				<textElement>
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Em Aberto:]]></text>
			</staticText>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-225" x="242" y="73" width="150" height="14"/>
				<textElement/>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{parametro1}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00" isBlankWhenNull="false">
				<reportElement key="textField-226" x="405" y="73" width="200" height="14"/>
				<textElement/>
				<textFieldExpression class="java.lang.Double"><![CDATA[new Double ($P{parametro2})]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement key="staticText-13" x="169" y="91" width="70" height="14"/>
				<textElement>
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Pago:]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-14" x="169" y="109" width="70" height="14"/>
				<textElement>
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Cancelado:]]></text>
			</staticText>
			<textField pattern="#,##0.00" isBlankWhenNull="false">
				<reportElement key="textField-229" x="405" y="91" width="200" height="14"/>
				<textElement/>
				<textFieldExpression class="java.lang.Double"><![CDATA[new Double ($P{parametro4})]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-230" x="242" y="91" width="150" height="14"/>
				<textElement/>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{parametro3}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-231" x="242" y="109" width="150" height="14"/>
				<textElement/>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{parametro5}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement key="staticText-17" x="241" y="55" width="102" height="14"/>
				<textElement>
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Quantidade Parcelas]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-18" x="405" y="55" width="102" height="14"/>
				<textElement>
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Valor Total Parcelas]]></text>
			</staticText>
			<textField pattern="#,##0.00" isBlankWhenNull="false">
				<reportElement key="textField-232" x="405" y="109" width="200" height="14"/>
				<textElement/>
				<textFieldExpression class="java.lang.Double"><![CDATA[new Double ($P{parametro6})]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-225" x="242" y="25" width="150" height="14"/>
				<textElement/>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{parametro7}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement key="staticText-9" x="169" y="25" width="70" height="14"/>
				<textElement>
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Qtd. Alunos:]]></text>
			</staticText>
			<textField pattern="#,##0.00" isBlankWhenNull="false">
				<reportElement key="textField-232" x="405" y="155" width="200" height="14"/>
				<textElement/>
				<textFieldExpression class="java.lang.Double"><![CDATA[new Double (String.valueOf(Double.parseDouble($P{parametro6}) +
Double.parseDouble($P{parametro4}) +
Double.parseDouble($P{parametro2}) +
Double.parseDouble($P{totaljuromulta})))]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement key="staticText-14" x="169" y="155" width="70" height="14"/>
				<textElement>
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Total]]></text>
			</staticText>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-231" x="242" y="155" width="150" height="14"/>
				<textElement/>
				<textFieldExpression class="java.lang.String"><![CDATA[String.valueOf(Integer.parseInt($P{parametro5}) +
Integer.parseInt($P{parametro3}) +
Integer.parseInt($P{parametro1}))]]></textFieldExpression>
			</textField>
			<line>
				<reportElement x="169" y="148" width="436" height="1"/>
			</line>
			<staticText>
				<reportElement key="staticText-14" x="169" y="134" width="95" height="14">
					<printWhenExpression><![CDATA[$P{mostrarcampo}]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Juros + Multa:]]></text>
			</staticText>
			<textField pattern="#,##0.00" isBlankWhenNull="false">
				<reportElement key="textField-232" x="405" y="134" width="200" height="14">
					<printWhenExpression><![CDATA[$P{mostrarcampo}]]></printWhenExpression>
				</reportElement>
				<textElement/>
				<textFieldExpression class="java.lang.Double"><![CDATA[new Double ($P{totaljuromulta})]]></textFieldExpression>
			</textField>
		</band>
	</lastPageFooter>
	<summary>
		<band splitType="Stretch"/>
	</summary>
</jasperReport>

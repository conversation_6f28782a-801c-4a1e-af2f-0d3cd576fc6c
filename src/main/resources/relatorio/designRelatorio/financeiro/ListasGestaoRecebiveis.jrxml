<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="ChequesRecebiveis" pageWidth="625" pageHeight="300" columnWidth="625" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" whenResourceMissingType="Empty">
	<property name="ireport.zoom" value="2.1961500000000145"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.encoding" value="ISO-8859-1"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="java.util.*"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<parameter name="tituloRelatorio" class="java.lang.String" isForPrompting="false"/>
	<parameter name="nomeEmpresa" class="java.lang.String" isForPrompting="false"/>
	<parameter name="versaoSoftware" class="java.lang.String" isForPrompting="false"/>
	<parameter name="usuario" class="java.lang.String" isForPrompting="false"/>
	<parameter name="filtros" class="java.lang.String" isForPrompting="false"/>
	<parameter name="SUBREPORT_DIR" class="java.lang.String">
		<defaultValueExpression><![CDATA["E:\\desenvolvimentosv\\Projeto Pacto Solucoes\\ZillyonWeb\\src\\java\\relatorio\\designRelatorio\\financeiro\\"]]></defaultValueExpression>
	</parameter>
	<parameter name="SUBREPORT_DIR1" class="java.lang.String">
		<defaultValueExpression><![CDATA["E:\\desenvolvimentosv\\Projeto Pacto Solucoes\\ZillyonWeb\\src\\java\\relatorio\\designRelatorio\\financeiro\\"]]></defaultValueExpression>
	</parameter>
	<parameter name="logoPadraoRelatorio" class="java.io.InputStream" isForPrompting="false"/>
	<parameter name="SUBREPORT_DIR2" class="java.lang.String">
		<defaultValueExpression><![CDATA["E:\\desenvolvimentosv\\Projeto Pacto Solucoes\\ZillyonWeb\\src\\java\\relatorio\\designRelatorio\\financeiro\\"]]></defaultValueExpression>
	</parameter>
	<parameter name="dataFim" class="java.lang.String"/>
	<parameter name="dataIni" class="java.lang.String"/>
	<parameter name="considerarCompensacaoOriginal" class="java.lang.Boolean">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="moeda" class="java.lang.String">
		<defaultValueExpression><![CDATA["R$"]]></defaultValueExpression>
	</parameter>
	<field name="cheque" class="java.lang.Boolean"/>
	<field name="cartao" class="java.lang.Boolean"/>
	<field name="formaPagamento" class="java.lang.String"/>
	<field name="valorApresentar" class="java.lang.String"/>
	<field name="chequesJR" class="java.lang.Object"/>
	<field name="cartoesJR" class="java.lang.Object"/>
	<field name="pagamentosJR" class="java.lang.Object"/>
	<field name="outro" class="java.lang.Boolean"/>
	<field name="exibirAutorizacao" class="java.lang.Boolean"/>
	<field name="imprimir" class="java.lang.Boolean"/>
	<field name="devolucao" class="java.lang.Boolean"/>
	<field name="devolucoesJR" class="java.lang.Object"/>
	<field name="exibirNSU" class="java.lang.Boolean"/>
	<detail>
		<band height="20">
			<printWhenExpression><![CDATA[$F{imprimir}]]></printWhenExpression>
			<rectangle>
				<reportElement x="0" y="0" width="625" height="14" forecolor="#FFFFFF" backcolor="#CCCCCC"/>
			</rectangle>
			<textField isStretchWithOverflow="true">
				<reportElement stretchType="RelativeToBandHeight" x="3" y="0" width="302" height="20"/>
				<textElement>
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{formaPagamento}]]></textFieldExpression>
			</textField>
		</band>
		<band height="24">
			<printWhenExpression><![CDATA[$F{cartao} && $F{imprimir}]]></printWhenExpression>
			<subreport isUsingCache="false">
				<reportElement positionType="Float" stretchType="RelativeToBandHeight" x="0" y="0" width="625" height="22"/>
				<subreportParameter name="dataIni"/>
				<subreportParameter name="SUBREPORT_DIR"/>
				<subreportParameter name="dataFim"/>
				<subreportParameter name="SUBREPORT_DIR2"/>
				<subreportParameter name="tituloRelatorio"/>
				<subreportParameter name="usuario"/>
				<subreportParameter name="nomeEmpresa"/>
				<subreportParameter name="logoPadraoRelatorio"/>
				<subreportParameter name="SUBREPORT_DIR1"/>
				<subreportParameter name="moeda">
					<subreportParameterExpression><![CDATA[$P{moeda}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="filtros"/>
				<subreportParameter name="versaoSoftware"/>
				<dataSourceExpression><![CDATA[$F{cartoesJR}]]></dataSourceExpression>
				<subreportExpression class="java.lang.String"><![CDATA[$P{SUBREPORT_DIR} + "CartoesGestaoRecebiveis.jasper"]]></subreportExpression>
			</subreport>
		</band>
		<band height="21">
			<printWhenExpression><![CDATA[$F{cheque} && $F{imprimir}]]></printWhenExpression>
			<subreport isUsingCache="false">
				<reportElement positionType="Float" stretchType="RelativeToBandHeight" x="0" y="0" width="625" height="20"/>
				<subreportParameter name="SUBREPORT_DIR"/>
				<subreportParameter name="dataIni"/>
				<subreportParameter name="dataFim"/>
				<subreportParameter name="SUBREPORT_DIR2"/>
				<subreportParameter name="considerarCompensacaoOriginal">
					<subreportParameterExpression><![CDATA[$P{considerarCompensacaoOriginal}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="tituloRelatorio"/>
				<subreportParameter name="usuario"/>
				<subreportParameter name="nomeEmpresa"/>
				<subreportParameter name="logoPadraoRelatorio"/>
				<subreportParameter name="SUBREPORT_DIR1"/>
				<subreportParameter name="moeda">
					<subreportParameterExpression><![CDATA[$P{moeda}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="versaoSoftware"/>
				<subreportParameter name="filtros"/>
				<dataSourceExpression><![CDATA[$F{chequesJR}]]></dataSourceExpression>
				<subreportExpression class="java.lang.String"><![CDATA[$P{SUBREPORT_DIR} + "ChequesGestaoRecebiveis.jasper"]]></subreportExpression>
			</subreport>
		</band>
		<band height="24">
			<printWhenExpression><![CDATA[$F{outro} && $F{imprimir}]]></printWhenExpression>
			<subreport isUsingCache="false">
				<reportElement positionType="Float" stretchType="RelativeToBandHeight" x="0" y="0" width="625" height="24"/>
				<subreportParameter name="dataIni"/>
				<subreportParameter name="usuario"/>
				<subreportParameter name="logoPadraoRelatorio"/>
				<subreportParameter name="exibirAutorizacao">
					<subreportParameterExpression><![CDATA[$F{exibirAutorizacao}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="SUBREPORT_DIR1"/>
				<subreportParameter name="SUBREPORT_DIR"/>
				<subreportParameter name="dataFim"/>
				<subreportParameter name="SUBREPORT_DIR2"/>
				<subreportParameter name="tituloRelatorio"/>
				<subreportParameter name="nomeEmpresa"/>
				<subreportParameter name="moeda">
					<subreportParameterExpression><![CDATA[$P{moeda}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="filtros"/>
				<subreportParameter name="exibirNSU">
					<subreportParameterExpression><![CDATA[$F{exibirNSU}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="versaoSoftware"/>
				<dataSourceExpression><![CDATA[$F{pagamentosJR}]]></dataSourceExpression>
				<subreportExpression class="java.lang.String"><![CDATA[$P{SUBREPORT_DIR} + "OutrosGestaoRecebiveis.jasper"]]></subreportExpression>
			</subreport>
		</band>
		<band height="30">
			<printWhenExpression><![CDATA[$F{devolucao} && $F{imprimir}]]></printWhenExpression>
			<subreport isUsingCache="false">
				<reportElement positionType="Float" stretchType="RelativeToBandHeight" x="0" y="0" width="625" height="24"/>
				<subreportParameter name="dataIni"/>
				<subreportParameter name="SUBREPORT_DIR"/>
				<subreportParameter name="dataFim"/>
				<subreportParameter name="SUBREPORT_DIR2"/>
				<subreportParameter name="tituloRelatorio"/>
				<subreportParameter name="usuario"/>
				<subreportParameter name="nomeEmpresa"/>
				<subreportParameter name="logoPadraoRelatorio"/>
				<subreportParameter name="exibirAutorizacao">
					<subreportParameterExpression><![CDATA[$F{exibirAutorizacao}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="SUBREPORT_DIR1"/>
				<subreportParameter name="moeda">
					<subreportParameterExpression><![CDATA[$P{moeda}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="filtros"/>
				<subreportParameter name="versaoSoftware"/>
				<dataSourceExpression><![CDATA[$F{devolucoesJR}]]></dataSourceExpression>
				<subreportExpression class="java.lang.String"><![CDATA[$P{SUBREPORT_DIR} + "DevolucoesGestaoRecebiveis.jasper"]]></subreportExpression>
			</subreport>
		</band>
		<band height="24">
			<printWhenExpression><![CDATA[$F{imprimir}]]></printWhenExpression>
			<line>
				<reportElement x="196" y="11" width="100" height="1" forecolor="#FFFFFF"/>
			</line>
			<textField isStretchWithOverflow="true">
				<reportElement stretchType="RelativeToBandHeight" x="465" y="0" width="160" height="20"/>
				<textElement textAlignment="Right">
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[" Total: "+$F{valorApresentar}]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="false">
				<reportElement mode="Transparent" x="405" y="0" width="60" height="20" forecolor="#000000" backcolor="#FFFFFF"/>
				<textElement textAlignment="Right" verticalAlignment="Top" rotation="None" lineSpacing="Single" markup="none">
					<font fontName="SansSerif" size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica-Bold" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{moeda}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
</jasperReport>

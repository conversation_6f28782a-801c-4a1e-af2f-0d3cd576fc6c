<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="RelatorioRepasse" pageWidth="842" pageHeight="595" orientation="Landscape" columnWidth="787" leftMargin="19" rightMargin="36" topMargin="2" bottomMargin="2" whenResourceMissingType="Empty">
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.encoding" value="ISO-8859-1"/>
	<property name="ireport.zoom" value="1.399522140629202"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="java.util.*"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<parameter name="logoPadraoRelatorio" class="java.io.InputStream" isForPrompting="false"/>
	<parameter name="tituloRelatorio" class="java.lang.String" isForPrompting="false"/>
	<parameter name="nomeEmpresa" class="java.lang.String" isForPrompting="false"/>
	<parameter name="versaoSoftware" class="java.lang.String" isForPrompting="false"/>
	<parameter name="usuario" class="java.lang.String" isForPrompting="false"/>
	<parameter name="filtros" class="java.lang.String" isForPrompting="false"/>
	<parameter name="SUBREPORT_DIR" class="java.lang.String">
		<defaultValueExpression><![CDATA["E:\\desenvolvimentosv\\Projeto Pacto Solucoes\\ZillyonWeb\\src\\java\\relatorio\\designRelatorio\\financeiro\\"]]></defaultValueExpression>
	</parameter>
	<parameter name="SUBREPORT_DIR1" class="java.lang.String">
		<defaultValueExpression><![CDATA["E:\\desenvolvimentosv\\Projeto Pacto Solucoes\\ZillyonWeb\\src\\java\\relatorio\\designRelatorio\\financeiro\\"]]></defaultValueExpression>
	</parameter>
	<parameter name="dataIni" class="java.lang.String" isForPrompting="false"/>
	<parameter name="dataFim" class="java.lang.String" isForPrompting="false"/>
	<parameter name="qtdAV" class="java.lang.String" isForPrompting="false"/>
	<parameter name="qtdCA" class="java.lang.String" isForPrompting="false"/>
	<parameter name="qtdChequeAV" class="java.lang.String" isForPrompting="false"/>
	<parameter name="qtdChequePR" class="java.lang.String" isForPrompting="false"/>
	<parameter name="qtdOutro" class="java.lang.String" isForPrompting="false"/>
	<parameter name="valorAV" class="java.lang.Double" isForPrompting="false"/>
	<parameter name="valorCA" class="java.lang.Double" isForPrompting="false"/>
	<parameter name="valorChequeAV" class="java.lang.Double" isForPrompting="false"/>
	<parameter name="valorChequePR" class="java.lang.Double" isForPrompting="false"/>
	<parameter name="valorOutro" class="java.lang.Double" isForPrompting="false"/>
	<parameter name="totalValorPago" class="java.lang.String" isForPrompting="false"/>
	<parameter name="totalDescontado" class="java.lang.String" isForPrompting="false"/>
	<parameter name="totalCompensado" class="java.lang.String" isForPrompting="false"/>
	<parameter name="totalRepasse" class="java.lang.String" isForPrompting="false"/>
	<parameter name="periodo" class="java.lang.String"/>
	<parameter name="totalNr" class="java.lang.String"/>
	<field name="nomeAluno" class="java.lang.String"/>
	<field name="matricula" class="java.lang.String"/>
	<field name="plano" class="java.lang.String"/>
	<field name="valor_Apresentar" class="java.lang.String"/>
	<field name="dataCompensacao_Apresentar" class="java.lang.String"/>
	<field name="valorCompensado_Apresentar" class="java.lang.String"/>
	<field name="totalDescontar_Apresentar" class="java.lang.String"/>
	<field name="valorRepasse_Apresentar" class="java.lang.String"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band splitType="Stretch"/>
	</title>
	<pageHeader>
		<band height="93" splitType="Stretch">
			<textField>
				<reportElement key="textField-2" x="0" y="7" width="382" height="40"/>
				<textElement markup="none">
					<font fontName="Arial" size="14" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{tituloRelatorio}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement key="textField-216" stretchType="RelativeToBandHeight" x="1" y="47" width="786" height="19"/>
				<textElement markup="none">
					<font fontName="Arial" size="10" isBold="true" isItalic="true" pdfFontName="Helvetica-BoldOblique"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{filtros}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement key="" positionType="Float" mode="Opaque" x="1" y="77" width="54" height="13"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="SansSerif" size="8" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Matrícula]]></text>
			</staticText>
			<staticText>
				<reportElement key="" positionType="Float" mode="Opaque" x="55" y="77" width="82" height="13"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="SansSerif" size="8" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Nome do Aluno]]></text>
			</staticText>
			<line>
				<reportElement key="" positionType="Float" x="1" y="91" width="786" height="1"/>
			</line>
			<staticText>
				<reportElement key="" positionType="Float" mode="Opaque" x="726" y="77" width="61" height="13"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="SansSerif" size="8" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Valor repasse]]></text>
			</staticText>
			<staticText>
				<reportElement key="" positionType="Float" mode="Opaque" x="299" y="77" width="214" height="13"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="SansSerif" size="8" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Plano]]></text>
			</staticText>
			<staticText>
				<reportElement key="" positionType="Float" mode="Opaque" x="525" y="77" width="87" height="13"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="SansSerif" size="8" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Data]]></text>
			</staticText>
			<staticText>
				<reportElement key="" positionType="Float" mode="Opaque" x="652" y="77" width="49" height="13"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="SansSerif" size="8" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Valor]]></text>
			</staticText>
			<textField>
				<reportElement key="textField-216" stretchType="RelativeToBandHeight" x="513" y="7" width="273" height="19"/>
				<textElement textAlignment="Right" markup="none">
					<font fontName="Arial" size="10" isBold="true" isItalic="true" pdfFontName="Helvetica-BoldOblique"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{periodo}]]></textFieldExpression>
			</textField>
			<line>
				<reportElement key="" positionType="Float" x="0" y="68" width="786" height="1"/>
			</line>
		</band>
	</pageHeader>
	<columnHeader>
		<band splitType="Stretch"/>
	</columnHeader>
	<detail>
		<band height="26" splitType="Stretch">
			<textField isStretchWithOverflow="true">
				<reportElement stretchType="RelativeToBandHeight" x="1" y="0" width="54" height="20"/>
				<textElement>
					<font size="10"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{matricula}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement stretchType="RelativeToBandHeight" x="55" y="0" width="243" height="19"/>
				<textElement>
					<font size="9"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{nomeAluno}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement stretchType="RelativeToBandHeight" x="528" y="0" width="84" height="20"/>
				<textElement textAlignment="Center"/>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{dataCompensacao_Apresentar}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement stretchType="RelativeToBandHeight" x="298" y="0" width="215" height="20"/>
				<textElement>
					<font size="9"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{plano}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement stretchType="RelativeToBandHeight" x="625" y="0" width="76" height="20"/>
				<textElement textAlignment="Right"/>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{valorCompensado_Apresentar}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement stretchType="RelativeToBandHeight" x="716" y="0" width="71" height="20"/>
				<textElement textAlignment="Right"/>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{valorRepasse_Apresentar}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<columnFooter>
		<band splitType="Stretch"/>
	</columnFooter>
	<pageFooter>
		<band height="22" splitType="Stretch">
			<textField>
				<reportElement key="textField-25" x="660" y="0" width="70" height="17"/>
				<textElement textAlignment="Right" markup="none">
					<font fontName="Arial"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA["Pág:  "  + $V{PAGE_NUMBER} + " de "]]></textFieldExpression>
			</textField>
			<textField evaluationTime="Report">
				<reportElement key="textField-26" x="730" y="0" width="57" height="17"/>
				<textElement markup="none">
					<font fontName="Arial"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[" " + $V{PAGE_NUMBER}]]></textFieldExpression>
			</textField>
		</band>
	</pageFooter>
	<summary>
		<band height="77" splitType="Immediate">
			<line>
				<reportElement key="" positionType="Float" x="0" y="3" width="786" height="1"/>
			</line>
			<staticText>
				<reportElement key="staticText-85" positionType="Float" mode="Opaque" x="12" y="11" width="184" height="21"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Top">
					<font fontName="SansSerif" size="12" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Total Geral de Lançamentos:]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-85" positionType="Float" mode="Opaque" x="12" y="32" width="184" height="22"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Top">
					<font fontName="SansSerif" size="12" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Total:]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-85" positionType="Float" mode="Opaque" x="12" y="54" width="184" height="22"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Top">
					<font fontName="SansSerif" size="12" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Total Repasse:]]></text>
			</staticText>
			<textField>
				<reportElement x="196" y="10" width="142" height="22"/>
				<textElement>
					<font size="12"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{totalNr}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="196" y="32" width="142" height="22"/>
				<textElement>
					<font size="12"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{totalCompensado}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="196" y="54" width="142" height="22"/>
				<textElement>
					<font size="12"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{totalRepasse}]]></textFieldExpression>
			</textField>
		</band>
	</summary>
</jasperReport>

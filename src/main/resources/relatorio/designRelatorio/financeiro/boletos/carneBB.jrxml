<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="bradesco" pageWidth="595" pageHeight="842" columnWidth="575" leftMargin="10" rightMargin="10" topMargin="0" bottomMargin="0">
	<property name="ireport.zoom" value="1.948717100000002"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<parameter name="cnpjEmpresa" class="java.lang.String"/>
	<parameter name="enderecoEmpresa" class="java.lang.String"/>
	<field name="banco.linhaDigitavel" class="java.lang.String">
		<fieldDescription><![CDATA[banco.linhaDigitavel]]></fieldDescription>
	</field>
	<field name="boleto.dataVencimento" class="java.lang.String">
		<fieldDescription><![CDATA[boleto.dataVencimento]]></fieldDescription>
	</field>
	<field name="boleto.nossoNumero" class="java.lang.String">
		<fieldDescription><![CDATA[boleto.nossoNumero]]></fieldDescription>
	</field>
	<field name="boleto.valorBoleto" class="java.lang.String">
		<fieldDescription><![CDATA[boleto.valorBoleto]]></fieldDescription>
	</field>
	<field name="boleto.cedente" class="java.lang.String">
		<fieldDescription><![CDATA[boleto.cedente]]></fieldDescription>
	</field>
	<field name="boleto.especieDocumento" class="java.lang.String">
		<fieldDescription><![CDATA[boleto.especieDocumento]]></fieldDescription>
	</field>
	<field name="boleto.carteira" class="java.lang.String">
		<fieldDescription><![CDATA[boleto.carteira]]></fieldDescription>
	</field>
	<field name="boleto.nomeSacado" class="java.lang.String">
		<fieldDescription><![CDATA[boleto.nomeSacado]]></fieldDescription>
	</field>
	<field name="boleto.enderecoSacado" class="java.lang.String">
		<fieldDescription><![CDATA[boleto.enderecoSacado]]></fieldDescription>
	</field>
	<field name="banco.codigoBarras" class="java.lang.String">
		<fieldDescription><![CDATA[banco.codigoBarras]]></fieldDescription>
	</field>
	<field name="boleto.instrucao1" class="java.lang.String">
		<fieldDescription><![CDATA[boleto.instrucao1]]></fieldDescription>
	</field>
	<field name="banco.banco" class="java.lang.String">
		<fieldDescription><![CDATA[banco.banco]]></fieldDescription>
	</field>
	<field name="banco" class="java.lang.Object"/>
	<field name="banco.numeroFormatted" class="java.lang.String">
		<fieldDescription><![CDATA[banco.numeroFormatted]]></fieldDescription>
	</field>
	<field name="boleto.cidadeSacado" class="java.lang.String">
		<fieldDescription><![CDATA[boleto.cidadeSacado]]></fieldDescription>
	</field>
	<field name="boleto.cepSacado" class="java.lang.String">
		<fieldDescription><![CDATA[boleto.cepSacado]]></fieldDescription>
	</field>
	<field name="boleto.ufSacado" class="java.lang.String">
		<fieldDescription><![CDATA[boleto.ufSacado]]></fieldDescription>
	</field>
	<field name="boleto.cpfSacado" class="java.lang.String">
		<fieldDescription><![CDATA[boleto.cpfSacado]]></fieldDescription>
	</field>
	<field name="boleto.dataDocumento" class="java.lang.String">
		<fieldDescription><![CDATA[boleto.dataDocumento]]></fieldDescription>
	</field>
	<field name="boleto.localPagamento" class="java.lang.String">
		<fieldDescription><![CDATA[boleto.localPagamento]]></fieldDescription>
	</field>
	<field name="boleto.agencia" class="java.lang.String">
		<fieldDescription><![CDATA[boleto.agencia]]></fieldDescription>
	</field>
	<field name="boleto.dvAgencia" class="java.lang.String">
		<fieldDescription><![CDATA[boleto.dvAgencia]]></fieldDescription>
	</field>
	<field name="boleto.contaCorrente" class="java.lang.String">
		<fieldDescription><![CDATA[boleto.contaCorrente]]></fieldDescription>
	</field>
	<field name="boleto.dvContaCorrente" class="java.lang.String">
		<fieldDescription><![CDATA[boleto.dvContaCorrente]]></fieldDescription>
	</field>
	<field name="boleto.numConvenio" class="java.lang.String"/>
	<field name="boleto.dvNossoNumero" class="java.lang.String"/>
	<field name="banco.nossoNumeroFormatted" class="java.lang.String"/>
	<field name="banco.agenciaCodCedenteFormatted" class="java.lang.String"/>
	<field name="boleto.dataProcessamento" class="java.lang.String"/>
	<field name="boleto.noDocumento" class="java.lang.String"/>
	<field name="boleto.aceite" class="java.lang.String"/>
	<field name="boleto.responsavel" class="java.lang.String">
		<fieldDescription><![CDATA[boleto.responsavel]]></fieldDescription>
	</field>
	<background>
		<band splitType="Stretch"/>
	</background>
	<detail>
		<band height="257" splitType="Stretch">
			<line>
				<reportElement x="0" y="18" width="108" height="1"/>
				<graphicElement>
					<pen lineWidth="2.0"/>
				</graphicElement>
			</line>
			<line>
				<reportElement x="115" y="18" width="460" height="1"/>
				<graphicElement>
					<pen lineWidth="1.2"/>
				</graphicElement>
			</line>
			<line>
				<reportElement x="81" y="3" width="1" height="15"/>
				<graphicElement>
					<pen lineWidth="2.0"/>
				</graphicElement>
			</line>
			<line>
				<reportElement x="216" y="3" width="1" height="15"/>
				<graphicElement>
					<pen lineWidth="2.0"/>
				</graphicElement>
			</line>
			<line>
				<reportElement x="116" y="38" width="459" height="1"/>
				<graphicElement>
					<pen lineWidth="1.0"/>
				</graphicElement>
			</line>
			<line>
				<reportElement x="445" y="18" width="1" height="178"/>
				<graphicElement>
					<pen lineWidth="1.25"/>
				</graphicElement>
			</line>
			<staticText>
				<reportElement x="118" y="20" width="323" height="7"/>
				<textElement>
					<font size="5" isBold="false"/>
				</textElement>
				<text><![CDATA[LOCAL DO PAGAMENTO]]></text>
			</staticText>
			<staticText>
				<reportElement x="448" y="20" width="123" height="8"/>
				<textElement>
					<font size="5"/>
				</textElement>
				<text><![CDATA[VENCIMENTO]]></text>
			</staticText>
			<line>
				<reportElement x="115" y="58" width="460" height="1"/>
				<graphicElement>
					<pen lineWidth="1.0"/>
				</graphicElement>
			</line>
			<staticText>
				<reportElement x="118" y="40" width="323" height="8"/>
				<textElement>
					<font size="5"/>
				</textElement>
				<text><![CDATA[NOME DO BENEFICIÁRIO/CPF/CNPJ/ENDEREÇO]]></text>
			</staticText>
			<staticText>
				<reportElement x="448" y="40" width="123" height="8"/>
				<textElement>
					<font size="5"/>
				</textElement>
				<text><![CDATA[AGÊNCIA / CÓDIGO DO BENEFICIÁRIO]]></text>
			</staticText>
			<line>
				<reportElement x="115" y="78" width="460" height="1"/>
				<graphicElement>
					<pen lineWidth="1.25"/>
				</graphicElement>
			</line>
			<staticText>
				<reportElement x="118" y="60" width="64" height="8"/>
				<textElement>
					<font size="5"/>
				</textElement>
				<text><![CDATA[DATA DO DOCUMENTO]]></text>
			</staticText>
			<staticText>
				<reportElement x="184" y="60" width="65" height="8"/>
				<textElement>
					<font size="5"/>
				</textElement>
				<text><![CDATA[N. DO DOCUMENTO]]></text>
			</staticText>
			<staticText>
				<reportElement x="251" y="60" width="36" height="8"/>
				<textElement>
					<font size="5"/>
				</textElement>
				<text><![CDATA[ESPÉCIE]]></text>
			</staticText>
			<staticText>
				<reportElement x="290" y="60" width="39" height="8"/>
				<textElement>
					<font size="5"/>
				</textElement>
				<text><![CDATA[ACEITE]]></text>
			</staticText>
			<staticText>
				<reportElement x="331" y="60" width="110" height="8"/>
				<textElement>
					<font size="5"/>
				</textElement>
				<text><![CDATA[DATA PROCESSAMENTO]]></text>
			</staticText>
			<line>
				<reportElement x="115" y="99" width="460" height="1"/>
				<graphicElement>
					<pen lineWidth="1.25"/>
				</graphicElement>
			</line>
			<staticText>
				<reportElement x="117" y="80" width="41" height="8"/>
				<textElement>
					<font size="5"/>
				</textElement>
				<text><![CDATA[USO DO BANCO]]></text>
			</staticText>
			<staticText>
				<reportElement x="184" y="80" width="29" height="8"/>
				<textElement>
					<font size="5"/>
				</textElement>
				<text><![CDATA[CARTEIRA]]></text>
			</staticText>
			<staticText>
				<reportElement x="219" y="80" width="24" height="8"/>
				<textElement>
					<font size="5"/>
				</textElement>
				<text><![CDATA[MOEDA]]></text>
			</staticText>
			<staticText>
				<reportElement x="251" y="80" width="78" height="8"/>
				<textElement>
					<font size="5"/>
				</textElement>
				<text><![CDATA[QUANTIDADE]]></text>
			</staticText>
			<staticText>
				<reportElement x="331" y="81" width="110" height="8"/>
				<textElement>
					<font size="5"/>
				</textElement>
				<text><![CDATA[VALOR]]></text>
			</staticText>
			<line>
				<reportElement x="115" y="197" width="460" height="1"/>
				<graphicElement>
					<pen lineWidth="1.0"/>
				</graphicElement>
			</line>
			<staticText>
				<reportElement x="118" y="101" width="213" height="8"/>
				<textElement>
					<font size="5"/>
				</textElement>
				<text><![CDATA[INFORMAÇÕES DE RESPONSABILIDADE DO BENEFICIÁRIO]]></text>
			</staticText>
			<staticText>
				<reportElement x="448" y="60" width="123" height="8"/>
				<textElement>
					<font size="5"/>
				</textElement>
				<text><![CDATA[NOSSO NÚMERO]]></text>
			</staticText>
			<staticText>
				<reportElement x="448" y="80" width="123" height="8"/>
				<textElement>
					<font size="5"/>
				</textElement>
				<text><![CDATA[(=) VALOR DO DOCUMENTO]]></text>
			</staticText>
			<staticText>
				<reportElement x="118" y="159" width="44" height="8"/>
				<textElement>
					<font size="5"/>
				</textElement>
				<text><![CDATA[PAGADOR]]></text>
			</staticText>
			<line>
				<reportElement x="257" y="3" width="1" height="15"/>
				<graphicElement>
					<pen lineWidth="2.0"/>
				</graphicElement>
			</line>
			<line>
				<reportElement x="115" y="18" width="1" height="179"/>
			</line>
			<line>
				<reportElement x="575" y="18" width="1" height="179"/>
			</line>
			<line>
				<reportElement x="446" y="118" width="129" height="1"/>
			</line>
			<line>
				<reportElement x="446" y="177" width="129" height="1"/>
			</line>
			<line>
				<reportElement x="446" y="138" width="129" height="1"/>
			</line>
			<staticText>
				<reportElement x="448" y="101" width="123" height="8"/>
				<textElement>
					<font size="5"/>
				</textElement>
				<text><![CDATA[(-) DESCONTO/ABATIMENTO]]></text>
			</staticText>
			<staticText>
				<reportElement x="448" y="140" width="123" height="8"/>
				<textElement>
					<font size="5"/>
				</textElement>
				<text><![CDATA[(+) JUROS/MULTA]]></text>
			</staticText>
			<staticText>
				<reportElement x="448" y="160" width="123" height="8"/>
				<textElement>
					<font size="5"/>
				</textElement>
				<text><![CDATA[(+) OUTROS ACRÉSCIMOS]]></text>
			</staticText>
			<staticText>
				<reportElement x="448" y="178" width="123" height="8"/>
				<textElement>
					<font size="5"/>
				</textElement>
				<text><![CDATA[(=) VALOR COBRADO]]></text>
			</staticText>
			<line>
				<reportElement x="108" y="4" width="1" height="248"/>
			</line>
			<line>
				<reportElement x="0" y="18" width="1" height="233"/>
			</line>
			<line>
				<reportElement x="0" y="251" width="109" height="1"/>
			</line>
			<line>
				<reportElement x="111" y="0" width="1" height="257"/>
				<graphicElement>
					<pen lineWidth="0.5" lineStyle="Dashed"/>
				</graphicElement>
			</line>
			<line>
				<reportElement x="1" y="38" width="107" height="1"/>
			</line>
			<line>
				<reportElement x="1" y="57" width="107" height="1"/>
			</line>
			<line>
				<reportElement x="0" y="117" width="108" height="1"/>
			</line>
			<line>
				<reportElement x="1" y="136" width="107" height="1"/>
			</line>
			<line>
				<reportElement x="1" y="157" width="107" height="1"/>
			</line>
			<line>
				<reportElement x="1" y="176" width="107" height="1"/>
			</line>
			<line>
				<reportElement x="1" y="197" width="107" height="1"/>
			</line>
			<line>
				<reportElement x="1" y="216" width="107" height="1"/>
			</line>
			<line>
				<reportElement x="1" y="77" width="107" height="1"/>
			</line>
			<line>
				<reportElement x="1" y="97" width="107" height="1"/>
			</line>
			<staticText>
				<reportElement x="3" y="20" width="104" height="7"/>
				<textElement>
					<font size="5"/>
				</textElement>
				<text><![CDATA[VENCIMENTO]]></text>
			</staticText>
			<staticText>
				<reportElement x="2" y="60" width="105" height="8"/>
				<textElement>
					<font size="5"/>
				</textElement>
				<text><![CDATA[NOSSO NÚMERO]]></text>
			</staticText>
			<staticText>
				<reportElement x="2" y="40" width="105" height="8"/>
				<textElement>
					<font size="5"/>
				</textElement>
				<text><![CDATA[AGÊNCIA / CÓDIGO DO BENEFICIÁRIO]]></text>
			</staticText>
			<staticText>
				<reportElement x="2" y="160" width="105" height="8"/>
				<textElement>
					<font size="5"/>
				</textElement>
				<text><![CDATA[(+) OUTROS ACRÉSCIMOS]]></text>
			</staticText>
			<staticText>
				<reportElement x="2" y="80" width="105" height="8"/>
				<textElement>
					<font size="5"/>
				</textElement>
				<text><![CDATA[(=) VALOR DO DOCUMENTO]]></text>
			</staticText>
			<staticText>
				<reportElement x="2" y="178" width="105" height="8"/>
				<textElement>
					<font size="5"/>
				</textElement>
				<text><![CDATA[(=) VALOR COBRADO]]></text>
			</staticText>
			<staticText>
				<reportElement x="2" y="140" width="105" height="8"/>
				<textElement>
					<font size="5"/>
				</textElement>
				<text><![CDATA[(+) JUROS/MULTA]]></text>
			</staticText>
			<staticText>
				<reportElement x="2" y="101" width="105" height="8"/>
				<textElement>
					<font size="5"/>
				</textElement>
				<text><![CDATA[(-) DESCONTO/ABATIMENTO]]></text>
			</staticText>
			<staticText>
				<reportElement x="2" y="199" width="105" height="8"/>
				<textElement>
					<font size="5"/>
				</textElement>
				<text><![CDATA[NÚMERO DO DOC.]]></text>
			</staticText>
			<staticText>
				<reportElement x="2" y="219" width="105" height="8"/>
				<textElement>
					<font size="5"/>
				</textElement>
				<text><![CDATA[SACADO]]></text>
			</staticText>
			<line>
				<reportElement x="182" y="59" width="1" height="19"/>
			</line>
			<line>
				<reportElement x="249" y="59" width="1" height="40"/>
			</line>
			<line>
				<reportElement x="288" y="59" width="1" height="19"/>
			</line>
			<line>
				<reportElement x="329" y="59" width="1" height="40"/>
			</line>
			<line>
				<reportElement x="182" y="79" width="1" height="20"/>
			</line>
			<line>
				<reportElement x="217" y="79" width="1" height="21"/>
			</line>
			<line>
				<reportElement x="0" y="256" width="575" height="1"/>
				<graphicElement>
					<pen lineWidth="0.5" lineStyle="Dashed"/>
				</graphicElement>
			</line>
			<textField>
				<reportElement x="262" y="4" width="313" height="15" isPrintWhenDetailOverflows="true"/>
				<textElement verticalAlignment="Middle">
					<font size="8" isBold="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{banco.linhaDigitavel}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="448" y="68" width="123" height="10"/>
				<textElement textAlignment="Right">
					<font size="6"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{banco.nossoNumeroFormatted}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="2" y="68" width="105" height="10"/>
				<textElement>
					<font size="6"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{banco.nossoNumeroFormatted}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="118" y="49" width="323" height="10"/>
				<textElement>
					<font size="6"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{boleto.cedente} + " / " + $P{cnpjEmpresa} + " / " + $P{enderecoEmpresa}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="448" y="89" width="123" height="10"/>
				<textElement textAlignment="Right">
					<font size="6"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{boleto.valorBoleto}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="183" y="89" width="32" height="10"/>
				<textElement textAlignment="Center">
					<font size="6"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{boleto.carteira}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="331" y="89" width="110" height="10"/>
				<textElement textAlignment="Right">
					<font size="6"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{boleto.valorBoleto}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="118" y="167" width="323" height="10"/>
				<textElement>
					<font size="6"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[(($F{boleto.responsavel} != null &&  !$F{boleto.responsavel}.isEmpty()) ? $F{boleto.responsavel} :  $F{boleto.nomeSacado}) + " CPF/CNPJ: " + $F{boleto.cpfSacado}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="118" y="176" width="323" height="10"/>
				<textElement>
					<font size="6"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{boleto.enderecoSacado}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="250" y="69" width="36" height="10"/>
				<textElement textAlignment="Center">
					<font size="6"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{boleto.especieDocumento}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="448" y="201" width="123" height="8"/>
				<textElement>
					<font size="6" isBold="true"/>
				</textElement>
				<text><![CDATA[Autenticação Mecânica-Ficha de ]]></text>
			</staticText>
			<textField>
				<reportElement x="2" y="88" width="105" height="10"/>
				<textElement>
					<font size="6"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{boleto.valorBoleto}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="2" y="226" width="105" height="23"/>
				<textElement>
					<font size="6"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{boleto.nomeSacado}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="448" y="29" width="123" height="10"/>
				<textElement textAlignment="Right">
					<font size="6"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{boleto.dataVencimento}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="2" y="29" width="105" height="10"/>
				<textElement>
					<font size="6"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{boleto.dataVencimento}]]></textFieldExpression>
			</textField>
			<line>
				<reportElement x="116" y="158" width="460" height="1"/>
			</line>
			<textField>
				<reportElement x="219" y="6" width="36" height="13"/>
				<textElement textAlignment="Center"/>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{banco.numeroFormatted}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="82" y="5" width="26" height="14"/>
				<textElement textAlignment="Center"/>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{banco.numeroFormatted}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="118" y="109" width="323" height="48"/>
				<textElement>
					<font size="6"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{boleto.instrucao1}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="2" y="5" width="79" height="14"/>
				<textElement/>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{banco.banco}]]></textFieldExpression>
			</textField>
			<image scaleImage="FillFrame" hAlign="Left" vAlign="Top">
				<reportElement key="barcode-1" mode="Opaque" x="118" y="214" width="319" height="35" forecolor="#000000" backcolor="#FFFFFF"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<graphicElement fill="Solid">
					<pen lineWidth="0.0" lineStyle="Solid"/>
				</graphicElement>
				<imageExpression class="java.awt.Image"><![CDATA[it.businesslogic.ireport.barcode.BcImage.getBarcodeImage(13,$F{banco.codigoBarras},false,false,null,0,0)]]></imageExpression>
			</image>
			<textField>
				<reportElement x="118" y="6" width="95" height="13"/>
				<textElement/>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{banco.banco}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="158" y="186" width="283" height="10"/>
				<textElement>
					<font size="6"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[($F{boleto.cidadeSacado}==null?"":$F{boleto.cidadeSacado}.trim() + "  " ) + ( $F{boleto.ufSacado}==null? "" : ( $F{boleto.ufSacado}))]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="118" y="186" width="38" height="10"/>
				<textElement>
					<font size="6"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{boleto.cepSacado}.length() != 0 ? ($F{boleto.cepSacado}.length()<=5?$F{boleto.cepSacado}+"-000":$F{boleto.cepSacado}.substring(0,5)+"-"+$F{boleto.cepSacado}.substring(5)):""]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="118" y="70" width="62" height="10"/>
				<textElement>
					<font size="6"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{boleto.dataDocumento}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="118" y="29" width="323" height="10"/>
				<textElement>
					<font size="6"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{boleto.localPagamento}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="2" y="48" width="105" height="10"/>
				<textElement>
					<font size="6"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{banco.agenciaCodCedenteFormatted}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="448" y="48" width="123" height="10"/>
				<textElement textAlignment="Right">
					<font size="6"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{banco.agenciaCodCedenteFormatted}]]></textFieldExpression>
			</textField>
			<line>
				<reportElement x="158" y="79" width="1" height="20"/>
			</line>
			<staticText>
				<reportElement x="161" y="80" width="18" height="8"/>
				<textElement>
					<font size="5"/>
				</textElement>
				<text><![CDATA[CIP]]></text>
			</staticText>
			<staticText>
				<reportElement x="478" y="211" width="44" height="8"/>
				<textElement>
					<font size="6" isBold="true"/>
				</textElement>
				<text><![CDATA[Compensação]]></text>
			</staticText>
			<staticText>
				<reportElement x="219" y="90" width="24" height="8"/>
				<textElement textAlignment="Center">
					<font size="5"/>
				</textElement>
				<text><![CDATA[R$]]></text>
			</staticText>
			<textField>
				<reportElement x="331" y="68" width="106" height="10"/>
				<textElement textAlignment="Center">
					<font size="6"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{boleto.dataProcessamento}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="5" y="207" width="87" height="10"/>
				<textElement>
					<font size="6"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{boleto.noDocumento}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="185" y="68" width="61" height="10"/>
				<textElement>
					<font size="6"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{boleto.noDocumento}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="290" y="68" width="36" height="10"/>
				<textElement textAlignment="Center">
					<font size="6"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{boleto.aceite}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
</jasperReport>

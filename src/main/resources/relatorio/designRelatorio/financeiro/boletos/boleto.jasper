¬í sr (net.sf.jasperreports.engine.JasperReport      'Ø L compileDatat Ljava/io/Serializable;L compileNameSuffixt Ljava/lang/String;L 
compilerClassq ~ xr -net.sf.jasperreports.engine.base.JRBaseReport      'Ø +I PSEUDO_SERIAL_VERSION_UIDI bottomMarginI columnCountI 
columnSpacingI columnWidthZ ignorePaginationZ isFloatColumnFooterZ isSummaryNewPageZ  isSummaryWithPageHeaderAndFooterZ isTitleNewPageI 
leftMarginB orientationI 
pageHeightI 	pageWidthB 
printOrderI rightMarginI 	topMarginB whenNoDataTypeL 
backgroundt $Lnet/sf/jasperreports/engine/JRBand;L columnFooterq ~ L columnHeaderq ~ [ datasetst ([Lnet/sf/jasperreports/engine/JRDataset;L defaultFontt *Lnet/sf/jasperreports/engine/JRReportFont;L defaultStylet %Lnet/sf/jasperreports/engine/JRStyle;L detailq ~ L 
detailSectiont 'Lnet/sf/jasperreports/engine/JRSection;[ fontst +[Lnet/sf/jasperreports/engine/JRReportFont;L formatFactoryClassq ~ L 
importsSett Ljava/util/Set;L languageq ~ L lastPageFooterq ~ L mainDatasett 'Lnet/sf/jasperreports/engine/JRDataset;L nameq ~ L noDataq ~ L orientationValuet 2Lnet/sf/jasperreports/engine/type/OrientationEnum;L 
pageFooterq ~ L 
pageHeaderq ~ L printOrderValuet 1Lnet/sf/jasperreports/engine/type/PrintOrderEnum;[ stylest &[Lnet/sf/jasperreports/engine/JRStyle;L summaryq ~ [ 	templatest /[Lnet/sf/jasperreports/engine/JRReportTemplate;L titleq ~ L whenNoDataTypeValuet 5Lnet/sf/jasperreports/engine/type/WhenNoDataTypeEnum;xp  wî             N           J  S          pppppppsr .net.sf.jasperreports.engine.base.JRBaseSection      'Ø [ bandst %[Lnet/sf/jasperreports/engine/JRBand;xpur %[Lnet.sf.jasperreports.engine.JRBand;Ý~ìÊ5  xp   sr +net.sf.jasperreports.engine.base.JRBaseBand      'Ø I PSEUDO_SERIAL_VERSION_UIDI heightZ isSplitAllowedL printWhenExpressiont *Lnet/sf/jasperreports/engine/JRExpression;L 	splitTypet Ljava/lang/Byte;L splitTypeValuet 0Lnet/sf/jasperreports/engine/type/SplitTypeEnum;xr 3net.sf.jasperreports.engine.base.JRBaseElementGroup      'Ø L childrent Ljava/util/List;L elementGroupt ,Lnet/sf/jasperreports/engine/JRElementGroup;xpsr java.util.ArrayListxÒÇa I sizexp   w   sr +net.sf.jasperreports.engine.base.JRBaseLine      'Ø I PSEUDO_SERIAL_VERSION_UIDB 	directionL directionValuet 4Lnet/sf/jasperreports/engine/type/LineDirectionEnum;xr 5net.sf.jasperreports.engine.base.JRBaseGraphicElement      'Ø I PSEUDO_SERIAL_VERSION_UIDL fillq ~ L 	fillValuet +Lnet/sf/jasperreports/engine/type/FillEnum;L linePent #Lnet/sf/jasperreports/engine/JRPen;L penq ~ xr .net.sf.jasperreports.engine.base.JRBaseElement      'Ø I PSEUDO_SERIAL_VERSION_UIDI heightZ isPrintInFirstWholeBandZ isPrintRepeatedValuesZ isPrintWhenDetailOverflowsZ isRemoveLineWhenBlankB positionTypeB stretchTypeI widthI xI yL 	backcolort Ljava/awt/Color;L defaultStyleProvidert 4Lnet/sf/jasperreports/engine/JRDefaultStyleProvider;L elementGroupq ~ L 	forecolorq ~ 'L keyq ~ L modeq ~ L 	modeValuet +Lnet/sf/jasperreports/engine/type/ModeEnum;L parentStyleq ~ L parentStyleNameReferenceq ~ L positionTypeValuet 3Lnet/sf/jasperreports/engine/type/PositionTypeEnum;L printWhenExpressionq ~ L printWhenGroupChangest %Lnet/sf/jasperreports/engine/JRGroup;L 
propertiesMapt -Lnet/sf/jasperreports/engine/JRPropertiesMap;[ propertyExpressionst 3[Lnet/sf/jasperreports/engine/JRPropertyExpression;L stretchTypeValuet 2Lnet/sf/jasperreports/engine/type/StretchTypeEnum;xp  wî                 spq ~ q ~ pt line-20pppp~r 1net.sf.jasperreports.engine.type.PositionTypeEnum          xr java.lang.Enum          xpt FIX_RELATIVE_TO_TOPpppp~r 0net.sf.jasperreports.engine.type.StretchTypeEnum          xq ~ 2t 
NO_STRETCH  wîppsr *net.sf.jasperreports.engine.base.JRBasePen      'Ø I PSEUDO_SERIAL_VERSION_UIDL 	lineColorq ~ 'L 	lineStyleq ~ L lineStyleValuet 0Lnet/sf/jasperreports/engine/type/LineStyleEnum;L 	lineWidtht Ljava/lang/Float;L penContainert ,Lnet/sf/jasperreports/engine/JRPenContainer;xp  wîpp~r .net.sf.jasperreports.engine.type.LineStyleEnum          xq ~ 2t DASHEDsr java.lang.FloatÚíÉ¢Û<ðì F valuexr java.lang.Number¬à  xp?  q ~ /p  wî ~r 2net.sf.jasperreports.engine.type.LineDirectionEnum          xq ~ 2t TOP_DOWNsq ~ !  wî          
      pq ~ q ~ pt line-1ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîpp~q ~ =t SOLIDsq ~ @?  q ~ Fp  wî q ~ Dsq ~ !  wî              x   vpq ~ q ~ pt line-2ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ Isq ~ @?  q ~ Lp  wî q ~ Dsq ~ !  wî                 vpq ~ q ~ pt line-3ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ Isq ~ @?  q ~ Pp  wî q ~ Dsq ~ !  wî                 pq ~ q ~ pt line-4ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ Isq ~ @?  q ~ Tp  wî q ~ Dsq ~ !  wî                 ©pq ~ q ~ pt line-5ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ Isq ~ @?  q ~ Xp  wî q ~ Dsq ~ !  wî                 ºpq ~ q ~ pt line-6ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ Isq ~ @?  q ~ \p  wî q ~ Dsq ~ !  wî                 Ëpq ~ q ~ pt line-7ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ Isq ~ @?  q ~ `p  wî q ~ Dsq ~ !  wî             y   pq ~ q ~ pt line-8ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ Isq ~ @?  q ~ dp  wî q ~ Dsq ~ !  wî                 pq ~ q ~ pt line-9ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ Isq ~ @?  q ~ hp  wî q ~ Dsq ~ !  wî             z   Üpq ~ q ~ pt line-10ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ Isq ~ @?  q ~ lp  wî q ~ Dsq ~ !  wî             z   ípq ~ q ~ pt line-11ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ Isq ~ @?  q ~ pp  wî q ~ Dsq ~ !  wî             z   þpq ~ q ~ pt line-12ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ Isq ~ @?  q ~ tp  wî q ~ Dsq ~ !  wî             y  pq ~ q ~ pt line-13ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ Isq ~ @?  q ~ xp  wî q ~ Dsq ~ !  wî   "           L   ©pq ~ q ~ pt line-14ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ Isq ~ @?  q ~ |p  wî q ~ Dsq ~ !  wî   "              ©pq ~ q ~ pt line-15ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ Isq ~ @?  q ~ p  wî q ~ Dsq ~ !  wî              |   ºpq ~ q ~ pt line-16ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ Isq ~ @?  q ~ p  wî q ~ Dsq ~ !  wî   "           í   ©pq ~ q ~ pt line-17ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ Isq ~ @?  q ~ p  wî q ~ Dsq ~ !  wî              Ñ   ©pq ~ q ~ pt line-18ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ Isq ~ @?  q ~ p  wî q ~ Dsq ~ !  wî                Kpq ~ q ~ pt line-19ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ Isq ~ @?  q ~ p  wî q ~ Dsr 1net.sf.jasperreports.engine.base.JRBaseStaticText      'Ø L textq ~ xr 2net.sf.jasperreports.engine.base.JRBaseTextElement      'Ø %I PSEUDO_SERIAL_VERSION_UIDL borderq ~ L borderColorq ~ 'L bottomBorderq ~ L bottomBorderColorq ~ 'L 
bottomPaddingt Ljava/lang/Integer;L fontNameq ~ L fontSizeq ~ L horizontalAlignmentq ~ L horizontalAlignmentValuet 6Lnet/sf/jasperreports/engine/type/HorizontalAlignEnum;L isBoldt Ljava/lang/Boolean;L isItalicq ~ L 
isPdfEmbeddedq ~ L isStrikeThroughq ~ L isStyledTextq ~ L isUnderlineq ~ L 
leftBorderq ~ L leftBorderColorq ~ 'L leftPaddingq ~ L lineBoxt 'Lnet/sf/jasperreports/engine/JRLineBox;L lineSpacingq ~ L lineSpacingValuet 2Lnet/sf/jasperreports/engine/type/LineSpacingEnum;L markupq ~ L paddingq ~ L pdfEncodingq ~ L pdfFontNameq ~ L 
reportFontq ~ L rightBorderq ~ L rightBorderColorq ~ 'L rightPaddingq ~ L rotationq ~ L 
rotationValuet /Lnet/sf/jasperreports/engine/type/RotationEnum;L 	topBorderq ~ L topBorderColorq ~ 'L 
topPaddingq ~ L verticalAlignmentq ~ L verticalAlignmentValuet 4Lnet/sf/jasperreports/engine/type/VerticalAlignEnum;xq ~ &  wî           B      pq ~ q ~ pt staticText-1ppppq ~ 3ppppq ~ 6  wîpppppt Arialsr java.lang.Integerâ ¤÷8 I valuexq ~ A   ppsr java.lang.BooleanÍ rÕúî Z valuexp ppppppppsr .net.sf.jasperreports.engine.base.JRBaseLineBox      'Ø L 
bottomPaddingq ~ L 	bottomPent +Lnet/sf/jasperreports/engine/base/JRBoxPen;L boxContainert ,Lnet/sf/jasperreports/engine/JRBoxContainer;L leftPaddingq ~ L leftPenq ~ ¥L paddingq ~ L penq ~ ¥L rightPaddingq ~ L rightPenq ~ ¥L 
topPaddingq ~ L topPenq ~ ¥xppsr 3net.sf.jasperreports.engine.base.JRBaseBoxBottomPen      'Ø  xr -net.sf.jasperreports.engine.base.JRBaseBoxPen      'Ø L lineBoxq ~ xq ~ 8  wîsr java.awt.Color¥3u F falphaI valueL cst Ljava/awt/color/ColorSpace;[ 	frgbvaluet [F[ fvalueq ~ ­xp    ÿ   ppppq ~ Isq ~ @    q ~ §q ~ §q ~ psr 1net.sf.jasperreports.engine.base.JRBaseBoxLeftPen      'Ø  xq ~ ©  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~ §q ~ §psq ~ ©  wîppppq ~ §q ~ §psr 2net.sf.jasperreports.engine.base.JRBaseBoxRightPen      'Ø  xq ~ ©  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~ §q ~ §psr 0net.sf.jasperreports.engine.base.JRBaseBoxTopPen      'Ø  xq ~ ©  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~ §q ~ §pppppt 	Helveticapppppppppp~r 2net.sf.jasperreports.engine.type.VerticalAlignEnum          xq ~ 2t MIDDLEt Local de pagamentosq ~   wî           B      pq ~ q ~ pt staticText-2ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~ Åq ~ Åq ~ Âpsq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~ Åq ~ Åpsq ~ ©  wîppppq ~ Åq ~ Åpsq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~ Åq ~ Åpsq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~ Åq ~ Åpppppt 	Helveticappppppppppq ~ ¿t 
BeneficiÃ¡riosq ~   wî           B      ©pq ~ q ~ pt staticText-3ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~ Øq ~ Øq ~ Õpsq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~ Øq ~ Øpsq ~ ©  wîppppq ~ Øq ~ Øpsq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~ Øq ~ Øpsq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~ Øq ~ Øpppppt 	Helveticappppppppppq ~ ¿t Data do Documentosq ~   wî           K      ºpq ~ q ~ pt staticText-4ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~ ëq ~ ëq ~ èpsq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~ ëq ~ ëpsq ~ ©  wîppppq ~ ëq ~ ëpsq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~ ëq ~ ëpsq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~ ëq ~ ëpppppt 	Helveticappppppppppq ~ ¿t NÂº da Conta / Respons.sq ~   wî           K      Ípq ~ q ~ pt staticText-5ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~ þq ~ þq ~ ûpsq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~ þq ~ þpsq ~ ©  wîppppq ~ þq ~ þpsq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~ þq ~ þpsq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~ þq ~ þpppppt 	Helveticappppppppppq ~ ¿t InstruÃ§Ãµes :sq ~   wî           B   O   ©pq ~ q ~ pt staticText-6ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~q ~psq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~psq ~ ©  wîppppq ~q ~psq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~psq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~pppppt 	Helveticappppppppppq ~ ¿t NÂº do Documentosq ~   wî           *      ©pq ~ q ~ pt staticText-7ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~$q ~$q ~!psq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~$q ~$psq ~ ©  wîppppq ~$q ~$psq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~$q ~$psq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~$q ~$pppppt 	Helveticappppppppppq ~ ¿t 
EspÃ©cie Doc.sq ~   wî              Ô   ©pq ~ q ~ pt staticText-8ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~7q ~7q ~4psq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~7q ~7psq ~ ©  wîppppq ~7q ~7psq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~7q ~7psq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~7q ~7pppppt 	Helveticappppppppppq ~ ¿t Aceitesq ~   wî              ð   ©pq ~ q ~ pt staticText-9ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~Jq ~Jq ~Gpsq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~Jq ~Jpsq ~ ©  wîppppq ~Jq ~Jpsq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~Jq ~Jpsq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~Jq ~Jpppppt 	Helveticappppppppppq ~ ¿t Data do Processamentosq ~   wî           #   O   ºpq ~ q ~ pt 
staticText-10ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~]q ~]q ~Zpsq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~]q ~]psq ~ ©  wîppppq ~]q ~]psq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~]q ~]psq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~]q ~]pppppt 	Helveticappppppppppq ~ ¿t Carteirasq ~   wî              ~   ºpq ~ q ~ pt 
staticText-11ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~pq ~pq ~mpsq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~pq ~ppsq ~ ©  wîppppq ~pq ~ppsq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~pq ~ppsq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~pq ~ppppppt 	Helveticappppppppppq ~ ¿t EspÃ©ciesq ~   wî           B      ºpq ~ q ~ pt 
staticText-12ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~q ~psq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~psq ~ ©  wîppppq ~q ~psq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~psq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~pppppt 	Helveticappppppppppq ~ ¿t 
Quantidadesq ~   wî           B   ð   ºpq ~ q ~ pt 
staticText-13ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~q ~psq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~psq ~ ©  wîppppq ~q ~psq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~psq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~pppppt 	Helveticappppppppppq ~ ¿t Valorsq ~   wî           d  |   pq ~ q ~ pt 
staticText-14ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~©q ~©q ~¦psq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~©q ~©psq ~ ©  wîppppq ~©q ~©psq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~©q ~©psq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~©q ~©pppppt 	Helveticappppppppppq ~ ¿t 
Vencimentosq ~   wî           d  |   pq ~ q ~ pt 
staticText-15ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~¼q ~¼q ~¹psq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~¼q ~¼psq ~ ©  wîppppq ~¼q ~¼psq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~¼q ~¼psq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~¼q ~¼pppppt 	Helveticappppppppppq ~ ¿t  AgÃªncia / CÃ³digo BeneficiÃ¡riosq ~   wî           d  |  pq ~ q ~ pt 
staticText-16ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~Ïq ~Ïq ~Ìpsq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~Ïq ~Ïpsq ~ ©  wîppppq ~Ïq ~Ïpsq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~Ïq ~Ïpsq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~Ïq ~Ïpppppt 	Helveticappppppppppq ~ ¿t (=) Valor cobradosq ~   wî           d  |   þpq ~ q ~ pt 
staticText-17ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~âq ~âq ~ßpsq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~âq ~âpsq ~ ©  wîppppq ~âq ~âpsq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~âq ~âpsq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~âq ~âpppppt 	Helveticappppppppppq ~ ¿t (=) Outros acrÃ©scimossq ~   wî           d  |   ípq ~ q ~ pt 
staticText-18ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~õq ~õq ~òpsq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~õq ~õpsq ~ ©  wîppppq ~õq ~õpsq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~õq ~õpsq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~õq ~õpppppt 	Helveticappppppppppq ~ ¿t (+) Juros / Multasq ~   wî           d  |   Üpq ~ q ~ pt 
staticText-19ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~q ~psq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~psq ~ ©  wîppppq ~q ~psq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~psq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~pppppt 	Helveticappppppppppq ~ ¿t (-) Outros deduÃ§Ãµessq ~   wî           d  |   Ëpq ~ q ~ pt 
staticText-20ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~q ~psq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~psq ~ ©  wîppppq ~q ~psq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~psq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~pppppt 	Helveticappppppppppq ~ ¿t (-) Desconto / Abatimentosq ~   wî           d  |   ºpq ~ q ~ pt 
staticText-21ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~.q ~.q ~+psq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~.q ~.psq ~ ©  wîppppq ~.q ~.psq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~.q ~.psq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~.q ~.pppppt 	Helveticappppppppppq ~ ¿t (=) Valor do Documentosq ~   wî           d  |   ©pq ~ q ~ pt 
staticText-22ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~Aq ~Aq ~>psq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~Aq ~Apsq ~ ©  wîppppq ~Aq ~Apsq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~Aq ~Apsq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~Aq ~Apppppt 	Helveticappppppppppq ~ ¿t 
Nosso NÃºmerosq ~   wî           !     "pq ~ q ~ pt 
staticText-23ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~Tq ~Tq ~Qpsq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~Tq ~Tpsq ~ ©  wîppppq ~Tq ~Tpsq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~Tq ~Tpsq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~Tq ~Tpppppt 	Helveticappppppppppq ~ ¿t Pagador:sq ~   wî           A     @pq ~ q ~ pt 
staticText-24ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~gq ~gq ~dpsq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~gq ~gpsq ~ ©  wîppppq ~gq ~gpsq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~gq ~gpsq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~gq ~gpppppt 	Helveticappppppppppq ~ ¿t Pagador / Avalista :sq ~   wî           9  e  @pq ~ q ~ pt 
staticText-25ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~zq ~zq ~wpsq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~zq ~zpsq ~ ©  wîppppq ~zq ~zpsq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~zq ~zpsq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~zq ~zpppppt 	Helveticappppppppppq ~ ¿t CÃ³digo de baixasq ~   wî           D  L  Mpq ~ q ~ pt 
staticText-26ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~q ~psq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~psq ~ ©  wîppppq ~q ~psq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~psq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~pppppt 	Helveticappppppppppq ~ ¿t AutenticaÃ§Ã£o mecÃ¢nicasq ~   wî           j    Mpq ~ q ~ pt 
staticText-27ppppq ~ 3ppppq ~ 6  wîpppppt Arialsq ~     	ppsq ~ ¢ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~¢q ~¢q ~psq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~¢q ~¢psq ~ ©  wîppppq ~¢q ~¢psq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~¢q ~¢psq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~¢q ~¢pppppt Helvetica-Boldppppppppppq ~ ¿t Ficha de CompensaÃ§Ã£osq ~   wî              ê   Àpq ~ q ~ pt 
staticText-28p~r )net.sf.jasperreports.engine.type.ModeEnum          xq ~ 2t OPAQUEppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡p~r 4net.sf.jasperreports.engine.type.HorizontalAlignEnum          xq ~ 2t CENTERq ~¡ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~»q ~»q ~²psq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~»q ~»psq ~ ©  wîppppq ~»q ~»psq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~»q ~»psq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~»q ~»pppppt Helvetica-Boldppppppppppq ~ ¿t Xsr ,net.sf.jasperreports.engine.base.JRBaseImage      'Ø *I PSEUDO_SERIAL_VERSION_UIDI 
bookmarkLevelB evaluationTimeB hyperlinkTargetB 
hyperlinkTypeZ isLazyB onErrorTypeL anchorNameExpressionq ~ L borderq ~ L borderColorq ~ 'L bottomBorderq ~ L bottomBorderColorq ~ 'L 
bottomPaddingq ~ L evaluationGroupq ~ +L evaluationTimeValuet 5Lnet/sf/jasperreports/engine/type/EvaluationTimeEnum;L 
expressionq ~ L horizontalAlignmentq ~ L horizontalAlignmentValueq ~ L hyperlinkAnchorExpressionq ~ L hyperlinkPageExpressionq ~ [ hyperlinkParameterst 3[Lnet/sf/jasperreports/engine/JRHyperlinkParameter;L hyperlinkReferenceExpressionq ~ L hyperlinkTooltipExpressionq ~ L isUsingCacheq ~ L 
leftBorderq ~ L leftBorderColorq ~ 'L leftPaddingq ~ L lineBoxq ~ L 
linkTargetq ~ L linkTypeq ~ L onErrorTypeValuet 2Lnet/sf/jasperreports/engine/type/OnErrorTypeEnum;L paddingq ~ L rightBorderq ~ L rightBorderColorq ~ 'L rightPaddingq ~ L 
scaleImageq ~ L scaleImageValuet 1Lnet/sf/jasperreports/engine/type/ScaleImageEnum;L 	topBorderq ~ L topBorderColorq ~ 'L 
topPaddingq ~ L verticalAlignmentq ~ L verticalAlignmentValueq ~ xq ~ #  wî   #       ?     Msq ~ «    ÿÿÿÿpppq ~ q ~ sq ~ «    ÿ   pppt 	barcode-1pq ~µppq ~ 3ppppq ~ 6  wîp~r )net.sf.jasperreports.engine.type.FillEnum          xq ~ 2t SOLIDsq ~ 8  wîppq ~ Isq ~ @    q ~Ðp  wî         ppppppp~r 3net.sf.jasperreports.engine.type.EvaluationTimeEnum          xq ~ 2t NOWsr 1net.sf.jasperreports.engine.base.JRBaseExpression      'Ø I id[ chunkst 0[Lnet/sf/jasperreports/engine/JRExpressionChunk;L valueClassNameq ~ L valueClassRealNameq ~ xp   ur 0[Lnet.sf.jasperreports.engine.JRExpressionChunk;mYÏÞiK£U  xp   sr 6net.sf.jasperreports.engine.base.JRBaseExpressionChunk      'Ø B typeL textq ~ xpt <it.businesslogic.ireport.barcode.BcImage.getBarcodeImage(13,sq ~át banco.codigoBarrassq ~át ,false,false,null,0,0)t java.awt.Imagepp~q ~¸t LEFTpppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~ëq ~ëq ~Ðpsq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~ëq ~ëpsq ~ ©  wîppppq ~ëq ~ëpsq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~ëq ~ëpsq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~ëq ~ëpp~r 0net.sf.jasperreports.engine.type.OnErrorTypeEnum          xq ~ 2t ERRORppppp~r /net.sf.jasperreports.engine.type.ScaleImageEnum          xq ~ 2t 
FILL_FRAMEpppp~q ~ ¾t TOPsq ~ !  wî                 pq ~ q ~ pt line-22ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ >sq ~ @?  q ~p  wî q ~ Dsq ~ !  wî   m             pq ~ q ~ pt line-23ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ >sq ~ @?  q ~p  wî q ~ Dsq ~ !  wî                 pq ~ q ~ pt line-24ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ Isq ~ @?  q ~	p  wî q ~ Dsq ~ !  wî                 pq ~ q ~ pt line-25ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ Isq ~ @?  q ~
p  wî q ~ Dsq ~ !  wî              y   pq ~ q ~ pt line-26ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ Isq ~ @?  q ~p  wî q ~ Dsq ~   wî           d      pq ~ q ~ pt 
staticText-29ppppq ~ 3ppppq ~ 6  wîppppppq ~ pq ~¹q ~¡ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~q ~psq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~psq ~ ©  wîppppq ~q ~psq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~psq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~pppppt Helvetica-Boldppppppppppq ~ÿt Recibo do Pagadorsq ~ !  wî                pq ~ q ~ pt line-27ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ Isq ~ @?  q ~'p  wî q ~ Dsq ~ !  wî             
   pq ~ q ~ pt line-28ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ Isq ~ @?  q ~+p  wî q ~ Dsq ~ !  wî             £   pq ~ q ~ pt line-29ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ Isq ~ @?  q ~/p  wî q ~ Dsq ~   wî           d  ¥   pq ~ q ~ pt 
staticText-30ppppq ~ 3ppppq ~ 6  wîppppppq ~ pq ~¹q ~¡ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~5q ~5q ~3psq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~5q ~5psq ~ ©  wîppppq ~5q ~5psq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~5q ~5psq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~5q ~5pppppt Helvetica-Boldppppppppppq ~ÿt Recibo de Entregasr 0net.sf.jasperreports.engine.base.JRBaseTextField      'Ø I PSEUDO_SERIAL_VERSION_UIDI 
bookmarkLevelB evaluationTimeB hyperlinkTargetB 
hyperlinkTypeZ isStretchWithOverflowL anchorNameExpressionq ~ L evaluationGroupq ~ +L evaluationTimeValueq ~ÌL 
expressionq ~ L hyperlinkAnchorExpressionq ~ L hyperlinkPageExpressionq ~ [ hyperlinkParametersq ~ÍL hyperlinkReferenceExpressionq ~ L hyperlinkTooltipExpressionq ~ L isBlankWhenNullq ~ L 
linkTargetq ~ L linkTypeq ~ L patternq ~ xq ~   wî             {   pq ~ q ~ pt textField-35ppppq ~ 3ppppq ~ 6  wîpppppppp~q ~¸t RIGHTq ~¡ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~Jq ~Jq ~Fpsq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~Jq ~Jpsq ~ ©  wîppppq ~Jq ~Jpsq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~Jq ~Jpsq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~Jq ~Jpppppt Helvetica-Boldppppppppppq ~ÿ  wî       ppq ~Úsq ~Ü   	uq ~ß   sq ~át banco.numeroFormattedt java.lang.Stringppppppq ~¡pppsq ~ !  wî                 pq ~ q ~ pt line-30ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ Isq ~ @?  q ~^p  wî q ~ Dsq ~ !  wî              2   pq ~ q ~ pt line-31ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ Isq ~ @?  q ~bp  wî q ~ Dsq ~ !  wî                 'pq ~ q ~ pt line-32ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ Isq ~ @?  q ~fp  wî q ~ Dsq ~ !  wî              Ï   pq ~ q ~ pt line-33ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ Isq ~ @?  q ~jp  wî q ~ Dsq ~   wî           .      pq ~ q ~ pt 
staticText-31ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~qq ~qq ~npsq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~qq ~qpsq ~ ©  wîppppq ~qq ~qpsq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~qq ~qpsq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~qq ~qpppppt 	Helveticappppppppppq ~ ¿t 
Vencimentosq ~   wî           d   5   pq ~ q ~ pt 
staticText-32ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~q ~psq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~psq ~ ©  wîppppq ~q ~psq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~psq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~pppppt 	Helveticappppppppppq ~ ¿t  AgÃªncia / CÃ³digo BeneficiÃ¡riosq ~   wî                 pq ~ q ~ pt 
staticText-33ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~q ~psq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~psq ~ ©  wîppppq ~q ~psq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~psq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~pppppt 	Helveticappppppppppq ~ ¿t EspÃ©ciesq ~   wî           /   Ò   pq ~ q ~ pt 
staticText-34ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~ªq ~ªq ~§psq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~ªq ~ªpsq ~ ©  wîppppq ~ªq ~ªpsq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~ªq ~ªpsq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~ªq ~ªpppppt 	Helveticappppppppppq ~ ¿t 
Quantidadesq ~ !  wî             
   pq ~ q ~ pt line-34ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ Isq ~ @?  q ~ºp  wî q ~ Dsq ~ !  wî             £   pq ~ q ~ pt line-35ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ Isq ~ @?  q ~¾p  wî q ~ Dsq ~ !  wî             :   pq ~ q ~ pt line-36ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ Isq ~ @?  q ~Âp  wî q ~ Dsq ~ !  wî             
   'pq ~ q ~ pt line-37ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ Isq ~ @?  q ~Æp  wî q ~ Dsq ~ !  wî             ×   pq ~ q ~ pt line-38ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ Isq ~ @?  q ~Êp  wî q ~ Dsq ~   wî           .  
   pq ~ q ~ pt 
staticText-35ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~Ñq ~Ñq ~Îpsq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~Ñq ~Ñpsq ~ ©  wîppppq ~Ñq ~Ñpsq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~Ñq ~Ñpsq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~Ñq ~Ñpppppt 	Helveticappppppppppq ~ ¿t 
Vencimentosq ~   wî           d  =   pq ~ q ~ pt 
staticText-36ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~äq ~äq ~ápsq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~äq ~äpsq ~ ©  wîppppq ~äq ~äpsq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~äq ~äpsq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~äq ~äpppppt 	Helveticappppppppppq ~ ¿t  AgÃªncia / CÃ³digo BeneficiÃ¡riosq ~   wî             ¦   pq ~ q ~ pt 
staticText-37ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~÷q ~÷q ~ôpsq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~÷q ~÷psq ~ ©  wîppppq ~÷q ~÷psq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~÷q ~÷psq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~÷q ~÷pppppt 	Helveticappppppppppq ~ ¿t EspÃ©ciesq ~   wî           /  Ú   pq ~ q ~ pt 
staticText-38ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~
q ~
q ~psq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~
q ~
psq ~ ©  wîppppq ~
q ~
psq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~
q ~
psq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~
q ~
pppppt 	Helveticappppppppppq ~ ¿t 
Quantidadesq ~ !  wî                 8pq ~ q ~ pt line-39ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ Isq ~ @?  q ~p  wî q ~ Dsq ~ !  wî              W   'pq ~ q ~ pt line-40ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ Isq ~ @?  q ~p  wî q ~ Dsq ~ !  wî              ¬   'pq ~ q ~ pt line-41ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ Isq ~ @?  q ~"p  wî q ~ Dsq ~   wî           R      'pq ~ q ~ pt 
staticText-39ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~)q ~)q ~&psq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~)q ~)psq ~ ©  wîppppq ~)q ~)psq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~)q ~)psq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~)q ~)pppppt 	Helveticappppppppppq ~ ¿t (=) Valor do Documentosq ~   wî           P   Z   'pq ~ q ~ pt 
staticText-40ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~<q ~<q ~9psq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~<q ~<psq ~ ©  wîppppq ~<q ~<psq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~<q ~<psq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~<q ~<pppppt 	Helveticappppppppppq ~ ¿t (-) Desconto / Abatimentosq ~   wî           R   ¯   'pq ~ q ~ pt 
staticText-41ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~Oq ~Oq ~Lpsq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~Oq ~Opsq ~ ©  wîppppq ~Oq ~Opsq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~Oq ~Opsq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~Oq ~Opppppt 	Helveticappppppppppq ~ ¿t (+) Juros / Multasq ~ !  wî              W   8pq ~ q ~ pt line-42ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ Isq ~ @?  q ~_p  wî q ~ Dsq ~ !  wî                 Ipq ~ q ~ pt line-43ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ Isq ~ @?  q ~cp  wî q ~ Dsq ~ !  wî              ¬   8pq ~ q ~ pt line-44ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ Isq ~ @?  q ~gp  wî q ~ Dsq ~   wî           R      8pq ~ q ~ pt 
staticText-42ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~nq ~nq ~kpsq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~nq ~npsq ~ ©  wîppppq ~nq ~npsq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~nq ~npsq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~nq ~npppppt 	Helveticappppppppppq ~ ¿t (=) Valor cobradosq ~   wî           P   Z   8pq ~ q ~ pt 
staticText-43ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~q ~~psq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~psq ~ ©  wîppppq ~q ~psq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~psq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~pppppt 	Helveticappppppppppq ~ ¿t 
Nosso NÃºmerosq ~   wî           B   ¯   8pq ~ q ~ pt 
staticText-44ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~q ~psq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~psq ~ ©  wîppppq ~q ~psq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~psq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~pppppt 	Helveticappppppppppq ~ ¿t NÂº do Documentosq ~   wî           R  
   'pq ~ q ~ pt 
staticText-45ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~§q ~§q ~¤psq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~§q ~§psq ~ ©  wîppppq ~§q ~§psq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~§q ~§psq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~§q ~§pppppt 	Helveticappppppppppq ~ ¿t (=) Valor do Documentosq ~ !  wî             _   'pq ~ q ~ pt line-45ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ Isq ~ @?  q ~·p  wî q ~ Dsq ~ !  wî             
   8pq ~ q ~ pt line-46ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ Isq ~ @?  q ~»p  wî q ~ Dsq ~ !  wî                 Upq ~ q ~ pt line-47ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ Isq ~ @?  q ~¿p  wî q ~ Dsq ~   wî           X      Ipq ~ q ~ pt 
staticText-46ppppq ~ 3ppppq ~ 6  wîpppppt Arialsq ~     ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~Çq ~Çq ~Ãpsq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~Çq ~Çpsq ~ ©  wîppppq ~Çq ~Çpsq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~Çq ~Çpsq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~Çq ~Çpppppt 	Helveticappppppppppq ~ ¿t  BeneficiÃ¡rio/CPF/CNPJ/EndereÃ§osq ~ !  wî             
   Ipq ~ q ~ pt line-48ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ Isq ~ @?  q ~×p  wî q ~ Dsq ~   wî           !  
   8pq ~ q ~ pt 
staticText-47ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~Þq ~Þq ~Ûpsq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~Þq ~Þpsq ~ ©  wîppppq ~Þq ~Þpsq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~Þq ~Þpsq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~Þq ~Þpppppt 	Helveticappppppppppq ~ ¿t Pagadorsq ~ !  wî             
   cpq ~ q ~ pt line-49ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ Isq ~ @?  q ~îp  wî q ~ Dsq ~   wî           Q  
   Ipq ~ q ~ pt 
staticText-48ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~õq ~õq ~òpsq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~õq ~õpsq ~ ©  wîppppq ~õq ~õpsq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~õq ~õpsq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~õq ~õpppppt 	Helveticappppppppppq ~ ¿t Assinatura do Recebedorsq ~   wî           D   `   dpq ~ q ~ pt 
staticText-49ppppq ~ 3ppppq ~ 6  wîpppppt 	SansSerifq ~Æpq ~¹q ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~q ~psq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~psq ~ ©  wîppppq ~q ~psq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~psq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~pppppt 	Helveticappppppppppq ~ÿt AutenticaÃ§Ã£o mecÃ¢nicasq ~   wî           P  k   'pq ~ q ~ pt 
staticText-50ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~q ~psq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~psq ~ ©  wîppppq ~q ~psq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~psq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~pppppt 	Helveticappppppppppq ~ ¿t 
Nosso NÃºmerosq ~ !  wî                pq ~ q ~ pt line-50ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ >sq ~ @?  q ~+p  wî q ~ Dsq ~ !  wî                 cpq ~ q ~ pt line-51ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ Isq ~ @?  q ~/p  wî q ~ Dsq ~   wî           !      Vpq ~ q ~ pt 
staticText-51ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~Æppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~6q ~6q ~3psq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~6q ~6psq ~ ©  wîppppq ~6q ~6psq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~6q ~6psq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~6q ~6pppppt 	Helveticappppppppppq ~ ¿t Pagadorsq ~E  wî                pq ~ q ~ pt textField-35ppppq ~ 3ppppq ~ 6  wîppppppppq ~Hq ~¡ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~Hq ~Hq ~Fpsq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~Hq ~Hpsq ~ ©  wîppppq ~Hq ~Hpsq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~Hq ~Hpsq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~Hq ~Hpppppt Helvetica-Boldppppppppppq ~ÿ  wî        ppq ~Úsq ~Ü   
uq ~ß   sq ~át banco.numeroFormattedt java.lang.Stringppppppq ~¡pppsq ~E  wî             {   vpq ~ q ~ pt textField-35ppppq ~ 3ppppq ~ 6  wîppppppppq ~Hq ~¡ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~^q ~^q ~\psq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~^q ~^psq ~ ©  wîppppq ~^q ~^psq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~^q ~^psq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~^q ~^pppppt Helvetica-Boldppppppppppq ~ÿ  wî       ppq ~Úsq ~Ü   uq ~ß   sq ~át banco.numeroFormattedt java.lang.Stringppppppq ~¡pppsq ~E  wî          p      vpq ~ q ~ ppppppq ~ 3ppppq ~ 6  wîppppppppq ~¹q ~¡ppppppppsq ~ ¤psq ~ ¨  wîppppq ~sq ~sq ~rpsq ~ °  wîppppq ~sq ~spsq ~ ©  wîppppq ~sq ~spsq ~ µ  wîppppq ~sq ~spsq ~ ¹  wîppppq ~sq ~spppppt Helvetica-Boldppppppppppp  wî        ppq ~Úsq ~Ü   uq ~ß   sq ~át banco.linhaDigitavelt java.lang.Stringppppppppppsq ~E  wî   	             Lpq ~ q ~ ppppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡pppppppppppsq ~ ¤psq ~ ¨  wîppppq ~q ~q ~psq ~ °  wîppppq ~q ~psq ~ ©  wîppppq ~q ~psq ~ µ  wîppppq ~q ~psq ~ ¹  wîppppq ~q ~pppppppppppppppp~q ~ ¾t BOTTOM  wî        ppq ~Úsq ~Ü   
uq ~ß   sq ~át boleto.cedentesq ~át  + " / " + sq ~át cnpjEmpresasq ~át  + " / " + sq ~át enderecoEmpresat java.lang.Stringppppppppppsq ~E  wî   	       t       pq ~ q ~ ppppppq ~ 3ppppq ~ 6  wîpppppt Arialsq ~     pppppppppppsq ~ ¤psq ~ ¨  wîppppq ~q ~q ~psq ~ °  wîppppq ~q ~psq ~ ©  wîppppq ~q ~psq ~ µ  wîppppq ~q ~psq ~ ¹  wîppppq ~q ~ppppppppppppppppq ~  wî        ppq ~Úsq ~Ü   uq ~ß   sq ~át boleto.cedentesq ~át  + " / " + sq ~át cnpjEmpresasq ~át  + " / " + sq ~át enderecoEmpresat java.lang.Stringppppppppppsq ~E  wî   	       t      pq ~ q ~ ppppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~pppppppppppsq ~ ¤psq ~ ¨  wîppppq ~®q ~®q ~¬psq ~ °  wîppppq ~®q ~®psq ~ ©  wîppppq ~®q ~®psq ~ µ  wîppppq ~®q ~®psq ~ ¹  wîppppq ~®q ~®ppppppppppppppppq ~  wî        ppq ~Úsq ~Ü   uq ~ß   sq ~át boleto.localPagamentot java.lang.Stringppppppq ~¡pppsq ~E  wî   	        B      ±pq ~ q ~ ppppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~pppppppppppsq ~ ¤psq ~ ¨  wîppppq ~»q ~»q ~¹psq ~ °  wîppppq ~»q ~»psq ~ ©  wîppppq ~»q ~»psq ~ µ  wîppppq ~»q ~»psq ~ ¹  wîppppq ~»q ~»ppppppppppppppppq ~  wî        ppq ~Úsq ~Ü   uq ~ß   sq ~át boleto.dataDocumentot java.lang.Stringppppppq ~¡ppt  sq ~E  wî   	        3      ±pq ~ q ~ ppppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~pq ~¹pppppppppsq ~ ¤psq ~ ¨  wîppppq ~Éq ~Éq ~Çpsq ~ °  wîppppq ~Éq ~Épsq ~ ©  wîppppq ~Éq ~Épsq ~ µ  wîppppq ~Éq ~Épsq ~ ¹  wîppppq ~Éq ~Éppppppppppppppppq ~  wî        ppq ~Úsq ~Ü   uq ~ß   sq ~át boleto.especieDocumentot java.lang.Stringppppppppppsq ~E  wî   	           Ó   ±pq ~ q ~ ppppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~pq ~¹pppppppppsq ~ ¤psq ~ ¨  wîppppq ~Öq ~Öq ~Ôpsq ~ °  wîppppq ~Öq ~Öpsq ~ ©  wîppppq ~Öq ~Öpsq ~ µ  wîppppq ~Öq ~Öpsq ~ ¹  wîppppq ~Öq ~Öppppppppppppppppq ~  wî        ppq ~Úsq ~Ü   uq ~ß   sq ~át 
boleto.aceitet java.lang.Stringppppppppppsq ~E  wî   	           ð   ±pq ~ q ~ ppppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~pppppppppppsq ~ ¤psq ~ ¨  wîppppq ~ãq ~ãq ~ápsq ~ °  wîppppq ~ãq ~ãpsq ~ ©  wîppppq ~ãq ~ãpsq ~ µ  wîppppq ~ãq ~ãpsq ~ ¹  wîppppq ~ãq ~ãppppppppppppppppq ~  wî        ppq ~Úsq ~Ü   uq ~ß   sq ~át boleto.dataProcessamentot java.lang.Stringppppppppppsq ~E  wî   	        (   O   Âpq ~ q ~ ppppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~pq ~¹pppppppppsq ~ ¤psq ~ ¨  wîppppq ~ðq ~ðq ~îpsq ~ °  wîppppq ~ðq ~ðpsq ~ ©  wîppppq ~ðq ~ðpsq ~ µ  wîppppq ~ðq ~ðpsq ~ ¹  wîppppq ~ðq ~ðppppppppppppppppq ~  wî        ppq ~Úsq ~Ü   uq ~ß   sq ~át boleto.carteirat java.lang.Stringppppppppppsq ~E  wî   	        P  ²   pq ~ q ~ ppppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~pq ~Hpppppppppsq ~ ¤psq ~ ¨  wîppppq ~ýq ~ýq ~ûpsq ~ °  wîppppq ~ýq ~ýpsq ~ ©  wîppppq ~ýq ~ýpsq ~ µ  wîppppq ~ýq ~ýpsq ~ ¹  wîppppq ~ýq ~ýppppppppppppppppq ~  wî        ppq ~Úsq ~Ü   uq ~ß   sq ~át boleto.dataVencimentot java.lang.Stringppppppppppsq ~E  wî   	        P  ²   Âpq ~ q ~ ppppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~pq ~Hpppppppppsq ~ ¤psq ~ ¨  wîppppq ~
q ~
q ~psq ~ °  wîppppq ~
q ~
psq ~ ©  wîppppq ~
q ~
psq ~ µ  wîppppq ~
q ~
psq ~ ¹  wîppppq ~
q ~
ppppppppppppppppq ~  wî        ppq ~Úsq ~Ü   uq ~ß   sq ~át boleto.valorBoletot java.lang.Stringppppppppppsq ~E  wî   	       Ý   %  !pq ~ q ~ ppppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~pppppppppppsq ~ ¤psq ~ ¨  wîppppq ~q ~q ~psq ~ °  wîppppq ~q ~psq ~ ©  wîppppq ~q ~psq ~ µ  wîppppq ~q ~psq ~ ¹  wîppppq ~q ~ppppppppppppppppq ~  wî        ppq ~Úsq ~Ü   uq ~ß   sq ~át ((sq ~át boleto.responsavelsq ~át  != null &&  !sq ~át boleto.responsavelsq ~át .isEmpty()) ? sq ~át boleto.responsavelsq ~át  :  sq ~át boleto.nomeSacadosq ~át 
) + " / " + (sq ~át boleto.cpfSacadosq ~át A.replace(".","").replace("-","").length()<12?"CPF: ":"CNPJ: ") + sq ~át boleto.cpfSacadot java.lang.Stringppppppppppsq ~E  wî   	        .      pq ~ q ~ ppppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~pq ~épppppppppsq ~ ¤psq ~ ¨  wîppppq ~:q ~:q ~8psq ~ °  wîppppq ~:q ~:psq ~ ©  wîppppq ~:q ~:psq ~ µ  wîppppq ~:q ~:psq ~ ¹  wîppppq ~:q ~:ppppppppppppppppq ~  wî        ppq ~Úsq ~Ü   uq ~ß   sq ~át boleto.dataVencimentot java.lang.Stringppppppppppsq ~E  wî   	        .  
   pq ~ q ~ ppppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~pq ~épppppppppsq ~ ¤psq ~ ¨  wîppppq ~Gq ~Gq ~Epsq ~ °  wîppppq ~Gq ~Gpsq ~ ©  wîppppq ~Gq ~Gpsq ~ µ  wîppppq ~Gq ~Gpsq ~ ¹  wîppppq ~Gq ~Gppppppppppppppppq ~  wî        ppq ~Úsq ~Ü   uq ~ß   sq ~át boleto.dataVencimentot java.lang.Stringppppppppppsq ~E  wî   	        J      /pq ~ q ~ ppppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~pq ~Hpppppppppsq ~ ¤psq ~ ¨  wîppppq ~Tq ~Tq ~Rpsq ~ °  wîppppq ~Tq ~Tpsq ~ ©  wîppppq ~Tq ~Tpsq ~ µ  wîppppq ~Tq ~Tpsq ~ ¹  wîppppq ~Tq ~Tppppppppppppppppq ~  wî        ppq ~Úsq ~Ü   uq ~ß   sq ~át boleto.valorBoletot java.lang.Stringppppppppppsq ~E  wî   	        J  
   /pq ~ q ~ ppppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~pq ~Hpppppppppsq ~ ¤psq ~ ¨  wîppppq ~aq ~aq ~_psq ~ °  wîppppq ~aq ~apsq ~ ©  wîppppq ~aq ~apsq ~ µ  wîppppq ~aq ~apsq ~ ¹  wîppppq ~aq ~appppppppppppppppq ~  wî        ppq ~Úsq ~Ü   uq ~ß   sq ~át boleto.valorBoletot java.lang.Stringppppppppppsq ~E  wî   	          
   @pq ~ q ~ ppppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~pppppppppppsq ~ ¤psq ~ ¨  wîppppq ~nq ~nq ~lpsq ~ °  wîppppq ~nq ~npsq ~ ©  wîppppq ~nq ~npsq ~ µ  wîppppq ~nq ~npsq ~ ¹  wîppppq ~nq ~nppppppppppppppppq ~  wî        ppq ~Úsq ~Ü   uq ~ß   sq ~át boleto.nomeSacadot java.lang.Stringppppppppppsq ~E  wî   	        O   Z   ?pq ~ q ~ pt textField-50ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~pq ~¹pppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~|q ~|q ~ypsq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~|q ~|psq ~ ©  wîppppq ~|q ~|psq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~|q ~|psq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~|q ~|ppppppppppppppppq ~  wî        ppq ~Úsq ~Ü   uq ~ß   sq ~át banco.nossoNumeroFormattedt java.lang.Stringppppppq ~¡ppq ~Æsq ~E  wî   	          k   -pq ~ q ~ pt textField-51ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~pq ~¹pppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~q ~psq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~psq ~ ©  wîppppq ~q ~psq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~psq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~ppppppppppppppppq ~  wî        ppq ~Úsq ~Ü   uq ~ß   sq ~át banco.nossoNumeroFormattedt java.lang.Stringppppppq ~¡ppq ~Æsq ~E  wî   	             °pq ~ q ~ pt textField-49ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~pq ~Hpppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~¨q ~¨q ~¥psq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~¨q ~¨psq ~ ©  wîppppq ~¨q ~¨psq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~¨q ~¨psq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~¨q ~¨ppppppppppppppppq ~  wî        ppq ~Úsq ~Ü   uq ~ß   sq ~át boleto.nossoNumerosq ~át +(sq ~át boleto.dvNossoNumerosq ~át .isEmpty() ? "" : ("-" + sq ~át boleto.dvNossoNumerosq ~át ))t java.lang.Stringppppppq ~¡ppq ~Æsq ~E  wî   	       Ý   %  *pq ~ q ~ ppppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~pppppppppppsq ~ ¤psq ~ ¨  wîppppq ~Çq ~Çq ~Åpsq ~ °  wîppppq ~Çq ~Çpsq ~ ©  wîppppq ~Çq ~Çpsq ~ µ  wîppppq ~Çq ~Çpsq ~ ¹  wîppppq ~Çq ~Çppppppppppppppppq ~  wî        ppq ~Úsq ~Ü    uq ~ß   sq ~át boleto.enderecoSacadot java.lang.Stringppppppppppsq ~E  wî   	       Ý   %  3pq ~ q ~ ppppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~pppppppppppsq ~ ¤psq ~ ¨  wîppppq ~Ôq ~Ôq ~Òpsq ~ °  wîppppq ~Ôq ~Ôpsq ~ ©  wîppppq ~Ôq ~Ôpsq ~ µ  wîppppq ~Ôq ~Ôpsq ~ ¹  wîppppq ~Ôq ~Ôppppppppppppppppq ~  wî        ppq ~Úsq ~Ü   !uq ~ß   sq ~át (sq ~át boleto.bairroSacadosq ~át 
==null?"":sq ~át boleto.bairroSacadosq ~át .trim() + "  " ) +
(sq ~át boleto.cidadeSacadosq ~át 
==null?"":sq ~át boleto.cidadeSacadosq ~át .trim() + "  " ) +
(sq ~át boleto.ufSacadosq ~át ==null? "" : ( sq ~át boleto.ufSacadosq ~át 
)) + 
" " + (sq ~át boleto.cepSacadosq ~át .trim() != null ? "CEP: " + sq ~át boleto.cepSacadosq ~át  : "")t java.lang.Stringppppppq ~¡pppsq ~E  wî   	        P  ²    pq ~ q ~ ppppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~pq ~Hpppppppppsq ~ ¤psq ~ ¨  wîppppq ~q ~q ~ÿpsq ~ °  wîppppq ~q ~psq ~ ©  wîppppq ~q ~psq ~ µ  wîppppq ~q ~psq ~ ¹  wîppppq ~q ~ppppppppppppppppq ~  wî        ppq ~Úsq ~Ü   "uq ~ß   sq ~át  banco.agenciaCodCedenteFormattedt java.lang.Stringppppppppppsq ~E  wî                 [pq ~ q ~ ppppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡pppppppppppsq ~ ¤psq ~ ¨  wîppppq ~q ~q ~psq ~ °  wîppppq ~q ~psq ~ ©  wîppppq ~q ~psq ~ µ  wîppppq ~q ~psq ~ ¹  wîppppq ~q ~ppppppppppppppppq ~  wî        ppq ~Úsq ~Ü   #uq ~ß   sq ~át boleto.nomeSacadot java.lang.Stringppppppppppsq ~E  wî   F       p      Õpq ~ q ~ pt textField-24ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~pq ~épppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~q ~psq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~psq ~ ©  wîppppq ~q ~psq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~psq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~ppppppppppppppppq ~ÿ  wî        ppq ~Úsq ~Ü   $uq ~ß   sq ~át boleto.instrucao1t java.lang.Stringppppppq ~¡pppsq ~E  wî           w      vpq ~ q ~ ppppppq ~ 3ppppq ~ 6  wîpppppt Arialppppppppppppsq ~ ¤psq ~ ¨  wîppppq ~1q ~1q ~/psq ~ °  wîppppq ~1q ~1psq ~ ©  wîppppq ~1q ~1psq ~ µ  wîppppq ~1q ~1psq ~ ¹  wîppppq ~1q ~1ppppppppppppppppq ~ÿ  wî        ppq ~Úsq ~Ü   %uq ~ß   sq ~át banco.bancot java.lang.Stringppppppppppsq ~E  wî           w      pq ~ q ~ ppppppq ~ 3ppppq ~ 6  wîpppppt Arialppppppppppppsq ~ ¤psq ~ ¨  wîppppq ~>q ~>q ~<psq ~ °  wîppppq ~>q ~>psq ~ ©  wîppppq ~>q ~>psq ~ µ  wîppppq ~>q ~>psq ~ ¹  wîppppq ~>q ~>ppppppppppppppppq ~ÿ  wî        ppq ~Úsq ~Ü   &uq ~ß   sq ~át banco.bancot java.lang.Stringppppppppppsq ~E  wî           w  
   pq ~ q ~ ppppppq ~ 3ppppq ~ 6  wîpppppt Arialppppppppppppsq ~ ¤psq ~ ¨  wîppppq ~Kq ~Kq ~Ipsq ~ °  wîppppq ~Kq ~Kpsq ~ ©  wîppppq ~Kq ~Kpsq ~ µ  wîppppq ~Kq ~Kpsq ~ ¹  wîppppq ~Kq ~Kppppppppppppppppq ~ÿ  wî        ppq ~Úsq ~Ü   'uq ~ß   sq ~át banco.bancot java.lang.Stringppppppppppsq ~E  wî   	              Âpq ~ q ~ ppppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~pq ~¹pppppppppsq ~ ¤psq ~ ¨  wîppppq ~Xq ~Xq ~Vpsq ~ °  wîppppq ~Xq ~Xpsq ~ ©  wîppppq ~Xq ~Xpsq ~ µ  wîppppq ~Xq ~Xpsq ~ ¹  wîppppq ~Xq ~Xppppppppppppppppq ~  wî        ppq ~Úsq ~Ü   (uq ~ß   sq ~át "R$"t java.lang.Stringppppppppppsq ~E  wî   	        C   N   ±pq ~ q ~ ppppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~pq ~¹pppppppppsq ~ ¤psq ~ ¨  wîppppq ~eq ~eq ~cpsq ~ °  wîppppq ~eq ~epsq ~ ©  wîppppq ~eq ~epsq ~ µ  wîppppq ~eq ~epsq ~ ¹  wîppppq ~eq ~eppppppppppppppppq ~  wî        ppq ~Úsq ~Ü   )uq ~ß   sq ~át boleto.noDocumentot java.lang.Stringppppppppppsq ~E  wî   	        Z  =   pq ~ q ~ ppppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~pq ~Hpppppppppsq ~ ¤psq ~ ¨  wîppppq ~rq ~rq ~ppsq ~ °  wîppppq ~rq ~rpsq ~ ©  wîppppq ~rq ~rpsq ~ µ  wîppppq ~rq ~rpsq ~ ¹  wîppppq ~rq ~rppppppppppppppppq ~  wî        ppq ~Úsq ~Ü   *uq ~ß   sq ~át  banco.agenciaCodCedenteFormattedt java.lang.Stringppppppppppsq ~E  wî   	        Z   5   pq ~ q ~ ppppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~pq ~Hpppppppppsq ~ ¤psq ~ ¨  wîppppq ~q ~q ~}psq ~ °  wîppppq ~q ~psq ~ ©  wîppppq ~q ~psq ~ µ  wîppppq ~q ~psq ~ ¹  wîppppq ~q ~ppppppppppppppppq ~  wî        ppq ~Úsq ~Ü   +uq ~ß   sq ~át  banco.agenciaCodCedenteFormattedt java.lang.Stringppppppppppxp  wî  pp~r .net.sf.jasperreports.engine.type.SplitTypeEnum          xq ~ 2t STRETCHppsr java.util.HashSetºD¸·4  xpw   ?@     t "net.sf.jasperreports.engine.data.*t net.sf.jasperreports.engine.*t java.util.*xt javapsr .net.sf.jasperreports.engine.base.JRBaseDataset      'Ø I PSEUDO_SERIAL_VERSION_UIDZ isMainB whenResourceMissingType[ fieldst &[Lnet/sf/jasperreports/engine/JRField;L filterExpressionq ~ [ groupst &[Lnet/sf/jasperreports/engine/JRGroup;L nameq ~ [ 
parameterst *[Lnet/sf/jasperreports/engine/JRParameter;L 
propertiesMapq ~ ,L queryt %Lnet/sf/jasperreports/engine/JRQuery;L resourceBundleq ~ L scriptletClassq ~ [ 
scriptletst *[Lnet/sf/jasperreports/engine/JRScriptlet;[ 
sortFieldst *[Lnet/sf/jasperreports/engine/JRSortField;[ 	variablest )[Lnet/sf/jasperreports/engine/JRVariable;L whenResourceMissingTypeValuet >Lnet/sf/jasperreports/engine/type/WhenResourceMissingTypeEnum;xp  wî ur &[Lnet.sf.jasperreports.engine.JRField;<ßÇN*òp  xp   sr ,net.sf.jasperreports.engine.base.JRBaseField      'Ø L descriptionq ~ L nameq ~ L 
propertiesMapq ~ ,L valueClassNameq ~ L valueClassRealNameq ~ xppt bancosr +net.sf.jasperreports.engine.JRPropertiesMap      'Ø L baseq ~ ,L propertiesListq ~ L 
propertiesMapt Ljava/util/Map;xppppt java.lang.Objectpsq ~pt banco.numeroFormattedsq ~¢pppt java.lang.Stringpsq ~pt banco.linhaDigitavelsq ~¢pppt java.lang.Stringpsq ~pt banco.codigoBarrassq ~¢pppt java.lang.Stringpsq ~pt boleto.cedentesq ~¢pppt java.lang.Stringpsq ~pt boleto.localPagamentosq ~¢pppt java.lang.Stringpsq ~pt boleto.dataDocumentosq ~¢pppt java.lang.Stringpsq ~pt boleto.especieDocumentosq ~¢pppt java.lang.Stringpsq ~pt 
boleto.aceitesq ~¢pppt java.lang.Stringpsq ~pt boleto.dataProcessamentosq ~¢pppt java.lang.Stringpsq ~pt boleto.carteirasq ~¢pppt java.lang.Stringpsq ~pt boleto.dataVencimentosq ~¢pppt java.lang.Stringpsq ~pt boleto.valorBoletosq ~¢pppt java.lang.Stringpsq ~pt boleto.nomeSacadosq ~¢pppt java.lang.Stringpsq ~pt boleto.cpfSacadosq ~¢pppt java.lang.Stringpsq ~pt boleto.nossoNumerosq ~¢pppt java.lang.Stringpsq ~pt boleto.enderecoSacadosq ~¢pppt java.lang.Stringpsq ~pt boleto.cepSacadosq ~¢pppt java.lang.Stringpsq ~pt boleto.cidadeSacadosq ~¢pppt java.lang.Stringpsq ~pt boleto.ufSacadosq ~¢pppt java.lang.Stringpsq ~pt  banco.agenciaCodCedenteFormattedsq ~¢pppt java.lang.Stringpsq ~pt boleto.instrucao1sq ~¢pppt java.lang.Stringpsq ~pt banco.bancosq ~¢pppt java.lang.Stringpsq ~pt banco.nossoNumeroFormattedsq ~¢pppt java.lang.Stringpsq ~pt boleto.dvNossoNumerosq ~¢pppt java.lang.Stringpsq ~pt boleto.noDocumentosq ~¢pppt java.lang.Stringpsq ~t boleto.responsavelt boleto.responsavelsq ~¢pppt java.lang.Stringpsq ~pt boleto.bairroSacadosq ~¢pppt java.lang.Stringpppt boletour *[Lnet.sf.jasperreports.engine.JRParameter;" *Ã`!  xp   sr 0net.sf.jasperreports.engine.base.JRBaseParameter      'Ø 	Z isForPromptingZ isSystemDefinedL defaultValueExpressionq ~ L descriptionq ~ L nameq ~ L nestedTypeNameq ~ L 
propertiesMapq ~ ,L valueClassNameq ~ L valueClassRealNameq ~ xpppt REPORT_PARAMETERS_MAPpsq ~¢pppt 
java.util.Mappsq ~ppt 
JASPER_REPORTpsq ~¢pppt (net.sf.jasperreports.engine.JasperReportpsq ~ppt REPORT_CONNECTIONpsq ~¢pppt java.sql.Connectionpsq ~ppt REPORT_MAX_COUNTpsq ~¢pppt java.lang.Integerpsq ~ppt REPORT_DATA_SOURCEpsq ~¢pppt (net.sf.jasperreports.engine.JRDataSourcepsq ~ppt REPORT_SCRIPTLETpsq ~¢pppt /net.sf.jasperreports.engine.JRAbstractScriptletpsq ~ppt 
REPORT_LOCALEpsq ~¢pppt java.util.Localepsq ~ppt REPORT_RESOURCE_BUNDLEpsq ~¢pppt java.util.ResourceBundlepsq ~ppt REPORT_TIME_ZONEpsq ~¢pppt java.util.TimeZonepsq ~ppt REPORT_FORMAT_FACTORYpsq ~¢pppt .net.sf.jasperreports.engine.util.FormatFactorypsq ~ppt REPORT_CLASS_LOADERpsq ~¢pppt java.lang.ClassLoaderpsq ~ppt REPORT_URL_HANDLER_FACTORYpsq ~¢pppt  java.net.URLStreamHandlerFactorypsq ~ppt REPORT_FILE_RESOLVERpsq ~¢pppt -net.sf.jasperreports.engine.util.FileResolverpsq ~ppt REPORT_TEMPLATESpsq ~¢pppt java.util.Collectionpsq ~ppt REPORT_VIRTUALIZERpsq ~¢pppt )net.sf.jasperreports.engine.JRVirtualizerpsq ~ppt IS_IGNORE_PAGINATIONpsq ~¢pppt java.lang.Booleanpsq ~ ppt cnpjEmpresapsq ~¢pppt java.lang.Stringpsq ~ ppt enderecoEmpresapsq ~¢pppt java.lang.Stringpsq ~¢psq ~    w   t ireport.scriptlethandlingt ireport.encodingt ireport.zoomt 	ireport.xt 	ireport.yxsr java.util.HashMapÚÁÃ`Ñ F 
loadFactorI 	thresholdxp?@     w      q ~ct 1.5q ~bt UTF-8q ~dt 0q ~et 53q ~at 0xsr ,net.sf.jasperreports.engine.base.JRBaseQuery      'Ø [ chunkst +[Lnet/sf/jasperreports/engine/JRQueryChunk;L languageq ~ xppt sqlppppur )[Lnet.sf.jasperreports.engine.JRVariable;bæ|,·D  xp   sr /net.sf.jasperreports.engine.base.JRBaseVariable      'Ø I PSEUDO_SERIAL_VERSION_UIDB calculationB 
incrementTypeZ isSystemDefinedB 	resetTypeL calculationValuet 2Lnet/sf/jasperreports/engine/type/CalculationEnum;L 
expressionq ~ L incrementGroupq ~ +L incrementTypeValuet 4Lnet/sf/jasperreports/engine/type/IncrementTypeEnum;L incrementerFactoryClassNameq ~ L incrementerFactoryClassRealNameq ~ L initialValueExpressionq ~ L nameq ~ L 
resetGroupq ~ +L resetTypeValuet 0Lnet/sf/jasperreports/engine/type/ResetTypeEnum;L valueClassNameq ~ L valueClassRealNameq ~ xp  wî   ~r 0net.sf.jasperreports.engine.type.CalculationEnum          xq ~ 2t SYSTEMpp~r 2net.sf.jasperreports.engine.type.IncrementTypeEnum          xq ~ 2t NONEppsq ~Ü    uq ~ß   sq ~át new java.lang.Integer(1)q ~&pt PAGE_NUMBERp~r .net.sf.jasperreports.engine.type.ResetTypeEnum          xq ~ 2t REPORTq ~&psq ~s  wî   q ~yppq ~|ppsq ~Ü   uq ~ß   sq ~át new java.lang.Integer(1)q ~&pt 
COLUMN_NUMBERp~q ~t PAGEq ~&psq ~s  wî   ~q ~xt COUNTsq ~Ü   uq ~ß   sq ~át new java.lang.Integer(1)q ~&ppq ~|ppsq ~Ü   uq ~ß   sq ~át new java.lang.Integer(0)q ~&pt REPORT_COUNTpq ~q ~&psq ~s  wî   q ~sq ~Ü   uq ~ß   sq ~át new java.lang.Integer(1)q ~&ppq ~|ppsq ~Ü   uq ~ß   sq ~át new java.lang.Integer(0)q ~&pt 
PAGE_COUNTpq ~q ~&psq ~s  wî   q ~sq ~Ü   uq ~ß   sq ~át new java.lang.Integer(1)q ~&ppq ~|ppsq ~Ü   uq ~ß   sq ~át new java.lang.Integer(0)q ~&pt COLUMN_COUNTp~q ~t COLUMNq ~&p~r <net.sf.jasperreports.engine.type.WhenResourceMissingTypeEnum          xq ~ 2t NULLq ~p~r 0net.sf.jasperreports.engine.type.OrientationEnum          xq ~ 2t PORTRAITpp~r /net.sf.jasperreports.engine.type.PrintOrderEnum          xq ~ 2t VERTICALpppp~r 3net.sf.jasperreports.engine.type.WhenNoDataTypeEnum          xq ~ 2t NO_PAGESsr 6net.sf.jasperreports.engine.design.JRReportCompileData      'Ø L crosstabCompileDataq ~£L datasetCompileDataq ~£L mainDatasetCompileDataq ~ xpsq ~f?@     w       xsq ~f?@     w       xur [B¬óøTà  xp  /BÊþº¾   .v boleto_1565112175312_285059  ,net/sf/jasperreports/engine/fill/JREvaluator  parameter_enderecoEmpresa 2Lnet/sf/jasperreports/engine/fill/JRFillParameter; parameter_REPORT_LOCALE parameter_JASPER_REPORT parameter_REPORT_VIRTUALIZER parameter_REPORT_TIME_ZONE parameter_REPORT_FILE_RESOLVER parameter_REPORT_SCRIPTLET parameter_REPORT_PARAMETERS_MAP parameter_REPORT_CONNECTION parameter_REPORT_CLASS_LOADER parameter_REPORT_DATA_SOURCE $parameter_REPORT_URL_HANDLER_FACTORY parameter_IS_IGNORE_PAGINATION parameter_REPORT_FORMAT_FACTORY parameter_REPORT_MAX_COUNT parameter_REPORT_TEMPLATES parameter_cnpjEmpresa  parameter_REPORT_RESOURCE_BUNDLE field_boleto46cedente .Lnet/sf/jasperreports/engine/fill/JRFillField; !field_banco46nossoNumeroFormatted field_boleto46enderecoSacado field_boleto46dvNossoNumero field_banco46codigoBarras 'field_banco46agenciaCodCedenteFormatted field_boleto46nomeSacado field_boleto46aceite field_banco46banco field_boleto46valorBoleto field_boleto46especieDocumento field_banco46numeroFormatted field_banco field_boleto46dataVencimento field_boleto46dataProcessamento field_boleto46ufSacado field_boleto46bairroSacado field_boleto46cepSacado field_boleto46dataDocumento field_banco46linhaDigitavel field_boleto46nossoNumero field_boleto46responsavel field_boleto46noDocumento field_boleto46localPagamento field_boleto46cpfSacado field_boleto46cidadeSacado field_boleto46carteira field_boleto46instrucao1 variable_PAGE_NUMBER 1Lnet/sf/jasperreports/engine/fill/JRFillVariable; variable_COLUMN_NUMBER variable_REPORT_COUNT variable_PAGE_COUNT variable_COLUMN_COUNT <init> ()V Code ; <
  >  	  @  	  B  	  D 	 	  F 
 	  H  	  J  	  L 
 	  N  	  P  	  R  	  T  	  V  	  X  	  Z  	  \  	  ^  	  `  	  b  	  d  	  f  	  h  	  j  	  l  	  n  	  p   	  r ! 	  t " 	  v # 	  x $ 	  z % 	  | & 	  ~ ' 	   ( 	   ) 	   * 	   + 	   , 	   - 	   . 	   / 	   0 	   1 	   2 	   3 	   4 	   5 6	   7 6	   8 6	    9 6	  ¢ : 6	  ¤ LineNumberTable customizedInit 0(Ljava/util/Map;Ljava/util/Map;Ljava/util/Map;)V 
initParams (Ljava/util/Map;)V © ª
  « 
initFields ­ ª
  ® initVars ° ª
  ± enderecoEmpresa ³ 
java/util/Map µ get &(Ljava/lang/Object;)Ljava/lang/Object; · ¸ ¶ ¹ 0net/sf/jasperreports/engine/fill/JRFillParameter » 
REPORT_LOCALE ½ 
JASPER_REPORT ¿ REPORT_VIRTUALIZER Á REPORT_TIME_ZONE Ã REPORT_FILE_RESOLVER Å REPORT_SCRIPTLET Ç REPORT_PARAMETERS_MAP É REPORT_CONNECTION Ë REPORT_CLASS_LOADER Í REPORT_DATA_SOURCE Ï REPORT_URL_HANDLER_FACTORY Ñ IS_IGNORE_PAGINATION Ó REPORT_FORMAT_FACTORY Õ REPORT_MAX_COUNT × REPORT_TEMPLATES Ù cnpjEmpresa Û REPORT_RESOURCE_BUNDLE Ý boleto.cedente ß ,net/sf/jasperreports/engine/fill/JRFillField á banco.nossoNumeroFormatted ã boleto.enderecoSacado å boleto.dvNossoNumero ç banco.codigoBarras é  banco.agenciaCodCedenteFormatted ë boleto.nomeSacado í 
boleto.aceite ï banco.banco ñ boleto.valorBoleto ó boleto.especieDocumento õ banco.numeroFormatted ÷ banco ù boleto.dataVencimento û boleto.dataProcessamento ý boleto.ufSacado ÿ boleto.bairroSacado boleto.cepSacado boleto.dataDocumento banco.linhaDigitavel boleto.nossoNumero	 boleto.responsavel boleto.noDocumento
 boleto.localPagamento boleto.cpfSacado boleto.cidadeSacado boleto.carteira boleto.instrucao1 PAGE_NUMBER /net/sf/jasperreports/engine/fill/JRFillVariable 
COLUMN_NUMBER REPORT_COUNT 
PAGE_COUNT! COLUMN_COUNT# evaluate (I)Ljava/lang/Object; 
Exceptions java/lang/Throwable( java/lang/Integer* (I)V ;,
+- getValue ()Ljava/lang/Object;/0
 â1 java/lang/String3 (it/businesslogic/ireport/barcode/BcImage5 getBarcodeImage I(ILjava/lang/Object;ZZLjava/lang/String;II)Ljava/awt/image/BufferedImage;78
69 java/lang/StringBuffer; valueOf &(Ljava/lang/Object;)Ljava/lang/String;=>
4? (Ljava/lang/String;)V ;A
<B  / D append ,(Ljava/lang/String;)Ljava/lang/StringBuffer;FG
<H
 ¼1 toString ()Ljava/lang/String;KL
<M isEmpty ()ZOP
4Q .S  U replace D(Ljava/lang/CharSequence;Ljava/lang/CharSequence;)Ljava/lang/String;WX
4Y -[ length ()I]^
4_ CPF: a CNPJ: c trimeL
4f   h  j CEP: l R$n evaluateOld getOldValueq0
 âr evaluateEstimated 
SourceFile !     3                 	     
               
                                                                                                !     "     #     $     %     &     '     (     )     *     +     ,     -     .     /     0     1     2     3     4     5 6    7 6    8 6    9 6    : 6     ; <  =  ì    *· ?*µ A*µ C*µ E*µ G*µ I*µ K*µ M*µ O*µ Q*µ S*µ U*µ W*µ Y*µ [*µ ]*µ _*µ a*µ c*µ e*µ g*µ i*µ k*µ m*µ o*µ q*µ s*µ u*µ w*µ y*µ {*µ }*µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ ¡*µ £*µ ¥±    ¦   Ö 5      	          ! " " ' # , $ 1 % 6 & ; ' @ ( E ) J * O + T , Y - ^ . c / h 0 m 1 r 2 w 3 | 4  5  6  7  8  9  :  ; ¤ < © = ® > ³ ? ¸ @ ½ A Â B Ç C Ì D Ñ E Ö F Û G à H å I ê J ï K ô L ù M þ N   § ¨  =   4     *+· ¬*,· ¯*-· ²±    ¦       Z  [ 
 \  ]  © ª  =  ¥    E*+´¹ º À ¼À ¼µ A*+¾¹ º À ¼À ¼µ C*+À¹ º À ¼À ¼µ E*+Â¹ º À ¼À ¼µ G*+Ä¹ º À ¼À ¼µ I*+Æ¹ º À ¼À ¼µ K*+È¹ º À ¼À ¼µ M*+Ê¹ º À ¼À ¼µ O*+Ì¹ º À ¼À ¼µ Q*+Î¹ º À ¼À ¼µ S*+Ð¹ º À ¼À ¼µ U*+Ò¹ º À ¼À ¼µ W*+Ô¹ º À ¼À ¼µ Y*+Ö¹ º À ¼À ¼µ [*+Ø¹ º À ¼À ¼µ ]*+Ú¹ º À ¼À ¼µ _*+Ü¹ º À ¼À ¼µ a*+Þ¹ º À ¼À ¼µ c±    ¦   N    e  f $ g 6 h H i Z j l k ~ l  m ¢ n ´ o Æ p Ø q ê r ü s t  u2 vD w  ­ ª  =      *+à¹ º À âÀ âµ e*+ä¹ º À âÀ âµ g*+æ¹ º À âÀ âµ i*+è¹ º À âÀ âµ k*+ê¹ º À âÀ âµ m*+ì¹ º À âÀ âµ o*+î¹ º À âÀ âµ q*+ð¹ º À âÀ âµ s*+ò¹ º À âÀ âµ u*+ô¹ º À âÀ âµ w*+ö¹ º À âÀ âµ y*+ø¹ º À âÀ âµ {*+ú¹ º À âÀ âµ }*+ü¹ º À âÀ âµ *+þ¹ º À âÀ âµ *+ ¹ º À âÀ âµ *+¹ º À âÀ âµ *+¹ º À âÀ âµ *+¹ º À âÀ âµ *+¹ º À âÀ âµ *+
¹ º À âÀ âµ *+¹ º À âÀ âµ *+¹ º À âÀ âµ *+¹ º À âÀ âµ *+¹ º À âÀ âµ *+¹ º À âÀ âµ *+¹ º À âÀ âµ *+¹ º À âÀ âµ ±    ¦   v       $  6  H  Z  l  ~    ¢  ´  Æ  Ø  ê  ü  ! 4 G Z m   ¦ ¹ Ì ß ò    ° ª  =        `*+¹ º ÀÀµ *+¹ º ÀÀµ *+ ¹ º ÀÀµ ¡*+"¹ º ÀÀµ £*+$¹ º ÀÀµ ¥±    ¦       £  ¤ & ¥ 9 ¦ L § _ ¨ %& '    ) =      ÿMª  ú       +   ½   É   Õ   á   í   ù        5  C  Q  _  m  ®  ï  ý      '  5  C  Q  _  ç  õ        -  ;  I    ¥  x      ¢  °  ¾  Ì  Ó  á  ï»+Y·.M§4»+Y·.M§(»+Y·.M§»+Y·.M§»+Y·.M§»+Y·.M§ø»+Y·.M§ì»+Y·.M§à
*´ m¶2À4¸:M§È*´ {¶2À4M§º*´ {¶2À4M§¬*´ {¶2À4M§*´ ¶2À4M§»<Y*´ e¶2À4¸@·CE¶I*´ a¶JÀ4¶IE¶I*´ A¶JÀ4¶I¶NM§O»<Y*´ e¶2À4¸@·CE¶I*´ a¶JÀ4¶IE¶I*´ A¶JÀ4¶I¶NM§*´ ¶2À4M§ *´ ¶2À4M§ò*´ y¶2À4M§ä*´ s¶2À4M§Ö*´ ¶2À4M§È*´ ¶2À4M§º*´ ¶2À4M§¬*´ w¶2À4M§»<Y*´ ¶2À4Æ  *´ ¶2À4¶R *´ ¶2À4§ 
*´ q¶2À4¸@·CE¶I*´ ¶2À4TV¶Z\V¶Z¶`¢ 	b§ d¶I*´ ¶2À4¶I¶NM§*´ ¶2À4M§*´ ¶2À4M§ú*´ w¶2À4M§ì*´ w¶2À4M§Þ*´ q¶2À4M§Ð*´ g¶2À4M§Â*´ g¶2À4M§´»<Y*´ ¶2À4¸@·C*´ k¶2À4¶R 	V§ »<Y\·C*´ k¶2À4¶I¶N¶I¶NM§f*´ i¶2À4M§X»<Y*´ ¶2À4Ç 	V§ #»<Y*´ ¶2À4¶g¸@·Ci¶I¶N¸@·C*´ ¶2À4Ç 	V§ #»<Y*´ ¶2À4¶g¸@·Ci¶I¶N¶I*´ ¶2À4Ç 	V§ 
*´ ¶2À4¶Ik¶I*´ ¶2À4¶gÆ  »<Ym·C*´ ¶2À4¶I¶N§ V¶I¶NM§ *´ o¶2À4M§ w*´ q¶2À4M§ i*´ ¶2À4M§ [*´ u¶2À4M§ M*´ u¶2À4M§ ?*´ u¶2À4M§ 1oM§ **´ ¶2À4M§ *´ o¶2À4M§ *´ o¶2À4M,°    ¦  z ^   °  ² À ¶ É · Ì » Õ ¼ Ø À á Á ä Å í Æ ð Ê ù Ë ü Ï Ð Ô Õ Ù Ú  Þ5 ß8 ãC äF èQ éT í_ îb òm óp ÷® ø± üï ýòý '*58CFQ T$_%b)ç*ê.õ/ø3489=>"B-C0G;H>LIMLQRV¥W¨[å\];^t[x_{cdhim¢n¥r°s³w¾xÁ|Ì}ÏÓÖáäïòý p& '    ) =      ÿMª  ú       +   ½   É   Õ   á   í   ù        5  C  Q  _  m  ®  ï  ý      '  5  C  Q  _  ç  õ        -  ;  I    ¥  x      ¢  °  ¾  Ì  Ó  á  ï»+Y·.M§4»+Y·.M§(»+Y·.M§»+Y·.M§»+Y·.M§»+Y·.M§ø»+Y·.M§ì»+Y·.M§à
*´ m¶sÀ4¸:M§È*´ {¶sÀ4M§º*´ {¶sÀ4M§¬*´ {¶sÀ4M§*´ ¶sÀ4M§»<Y*´ e¶sÀ4¸@·CE¶I*´ a¶JÀ4¶IE¶I*´ A¶JÀ4¶I¶NM§O»<Y*´ e¶sÀ4¸@·CE¶I*´ a¶JÀ4¶IE¶I*´ A¶JÀ4¶I¶NM§*´ ¶sÀ4M§ *´ ¶sÀ4M§ò*´ y¶sÀ4M§ä*´ s¶sÀ4M§Ö*´ ¶sÀ4M§È*´ ¶sÀ4M§º*´ ¶sÀ4M§¬*´ w¶sÀ4M§»<Y*´ ¶sÀ4Æ  *´ ¶sÀ4¶R *´ ¶sÀ4§ 
*´ q¶sÀ4¸@·CE¶I*´ ¶sÀ4TV¶Z\V¶Z¶`¢ 	b§ d¶I*´ ¶sÀ4¶I¶NM§*´ ¶sÀ4M§*´ ¶sÀ4M§ú*´ w¶sÀ4M§ì*´ w¶sÀ4M§Þ*´ q¶sÀ4M§Ð*´ g¶sÀ4M§Â*´ g¶sÀ4M§´»<Y*´ ¶sÀ4¸@·C*´ k¶sÀ4¶R 	V§ »<Y\·C*´ k¶sÀ4¶I¶N¶I¶NM§f*´ i¶sÀ4M§X»<Y*´ ¶sÀ4Ç 	V§ #»<Y*´ ¶sÀ4¶g¸@·Ci¶I¶N¸@·C*´ ¶sÀ4Ç 	V§ #»<Y*´ ¶sÀ4¶g¸@·Ci¶I¶N¶I*´ ¶sÀ4Ç 	V§ 
*´ ¶sÀ4¶Ik¶I*´ ¶sÀ4¶gÆ  »<Ym·C*´ ¶sÀ4¶I¶N§ V¶I¶NM§ *´ o¶sÀ4M§ w*´ q¶sÀ4M§ i*´ ¶sÀ4M§ [*´ u¶sÀ4M§ M*´ u¶sÀ4M§ ?*´ u¶sÀ4M§ 1oM§ **´ ¶sÀ4M§ *´ o¶sÀ4M§ *´ o¶sÀ4M,°    ¦  z ^  ¡ £ À§ É¨ Ì¬ Õ­ Ø± á² ä¶ í· ð» ù¼ üÀÁÅÆÊË Ï5Ð8ÔCÕFÙQÚTÞ_ßbãmäpè®é±íïîòòýó ÷øüý'*58CFQT_bçêõ ø$%)*./"3-408;9>=I>LBCG¥H¨LåMN;OtLxP{TUYZ^¢_¥c°d³h¾iÁmÌnÏrÓsÖwáxä|ï}òý t& '    ) =      ÿMª  ú       +   ½   É   Õ   á   í   ù        5  C  Q  _  m  ®  ï  ý      '  5  C  Q  _  ç  õ        -  ;  I    ¥  x      ¢  °  ¾  Ì  Ó  á  ï»+Y·.M§4»+Y·.M§(»+Y·.M§»+Y·.M§»+Y·.M§»+Y·.M§ø»+Y·.M§ì»+Y·.M§à
*´ m¶2À4¸:M§È*´ {¶2À4M§º*´ {¶2À4M§¬*´ {¶2À4M§*´ ¶2À4M§»<Y*´ e¶2À4¸@·CE¶I*´ a¶JÀ4¶IE¶I*´ A¶JÀ4¶I¶NM§O»<Y*´ e¶2À4¸@·CE¶I*´ a¶JÀ4¶IE¶I*´ A¶JÀ4¶I¶NM§*´ ¶2À4M§ *´ ¶2À4M§ò*´ y¶2À4M§ä*´ s¶2À4M§Ö*´ ¶2À4M§È*´ ¶2À4M§º*´ ¶2À4M§¬*´ w¶2À4M§»<Y*´ ¶2À4Æ  *´ ¶2À4¶R *´ ¶2À4§ 
*´ q¶2À4¸@·CE¶I*´ ¶2À4TV¶Z\V¶Z¶`¢ 	b§ d¶I*´ ¶2À4¶I¶NM§*´ ¶2À4M§*´ ¶2À4M§ú*´ w¶2À4M§ì*´ w¶2À4M§Þ*´ q¶2À4M§Ð*´ g¶2À4M§Â*´ g¶2À4M§´»<Y*´ ¶2À4¸@·C*´ k¶2À4¶R 	V§ »<Y\·C*´ k¶2À4¶I¶N¶I¶NM§f*´ i¶2À4M§X»<Y*´ ¶2À4Ç 	V§ #»<Y*´ ¶2À4¶g¸@·Ci¶I¶N¸@·C*´ ¶2À4Ç 	V§ #»<Y*´ ¶2À4¶g¸@·Ci¶I¶N¶I*´ ¶2À4Ç 	V§ 
*´ ¶2À4¶Ik¶I*´ ¶2À4¶gÆ  »<Ym·C*´ ¶2À4¶I¶N§ V¶I¶NM§ *´ o¶2À4M§ w*´ q¶2À4M§ i*´ ¶2À4M§ [*´ u¶2À4M§ M*´ u¶2À4M§ ?*´ u¶2À4M§ 1oM§ **´ ¶2À4M§ *´ o¶2À4M§ *´ o¶2À4M,°    ¦  z ^    À É Ì Õ Ø¢ á£ ä§ í¨ ð¬ ù­ ü±²¶·»¼ À5Á8ÅCÆFÊQËTÏ_ÐbÔmÕpÙ®Ú±Þïßòãýä èéíîò'ó*÷5ø8üCýFQT_bçêõø "$-%0);*>.I/L348¥9¨=å>?;@t=xA{EFJKO¢P¥T°U³Y¾ZÁ^Ì_ÏcÓdÖháiämïnòrýz u    t _1565112175312_285059t 2net.sf.jasperreports.engine.design.JRJavacCompiler
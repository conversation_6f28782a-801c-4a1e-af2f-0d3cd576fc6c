¬í sr (net.sf.jasperreports.engine.JasperReport      'Ø L compileDatat Ljava/io/Serializable;L compileNameSuffixt Ljava/lang/String;L 
compilerClassq ~ xr -net.sf.jasperreports.engine.base.JRBaseReport      'Ø +I PSEUDO_SERIAL_VERSION_UIDI bottomMarginI columnCountI 
columnSpacingI columnWidthZ ignorePaginationZ isFloatColumnFooterZ isSummaryNewPageZ  isSummaryWithPageHeaderAndFooterZ isTitleNewPageI 
leftMarginB orientationI 
pageHeightI 	pageWidthB 
printOrderI rightMarginI 	topMarginB whenNoDataTypeL 
backgroundt $Lnet/sf/jasperreports/engine/JRBand;L columnFooterq ~ L columnHeaderq ~ [ datasetst ([Lnet/sf/jasperreports/engine/JRDataset;L defaultFontt *Lnet/sf/jasperreports/engine/JRReportFont;L defaultStylet %Lnet/sf/jasperreports/engine/JRStyle;L detailq ~ L 
detailSectiont 'Lnet/sf/jasperreports/engine/JRSection;[ fontst +[Lnet/sf/jasperreports/engine/JRReportFont;L formatFactoryClassq ~ L 
importsSett Ljava/util/Set;L languageq ~ L lastPageFooterq ~ L mainDatasett 'Lnet/sf/jasperreports/engine/JRDataset;L nameq ~ L noDataq ~ L orientationValuet 2Lnet/sf/jasperreports/engine/type/OrientationEnum;L 
pageFooterq ~ L 
pageHeaderq ~ L printOrderValuet 1Lnet/sf/jasperreports/engine/type/PrintOrderEnum;[ stylest &[Lnet/sf/jasperreports/engine/JRStyle;L summaryq ~ [ 	templatest /[Lnet/sf/jasperreports/engine/JRReportTemplate;L titleq ~ L whenNoDataTypeValuet 5Lnet/sf/jasperreports/engine/type/WhenNoDataTypeEnum;xp  wî                                    pppppppsr .net.sf.jasperreports.engine.base.JRBaseSection      'Ø [ bandst %[Lnet/sf/jasperreports/engine/JRBand;xpur %[Lnet.sf.jasperreports.engine.JRBand;Ý~ìÊ5  xp   sr +net.sf.jasperreports.engine.base.JRBaseBand      'Ø I PSEUDO_SERIAL_VERSION_UIDI heightZ isSplitAllowedL printWhenExpressiont *Lnet/sf/jasperreports/engine/JRExpression;L 	splitTypet Ljava/lang/Byte;L splitTypeValuet 0Lnet/sf/jasperreports/engine/type/SplitTypeEnum;xr 3net.sf.jasperreports.engine.base.JRBaseElementGroup      'Ø L childrent Ljava/util/List;L elementGroupt ,Lnet/sf/jasperreports/engine/JRElementGroup;xpsr java.util.ArrayListxÒÇa I sizexp   w   sr +net.sf.jasperreports.engine.base.JRBaseLine      'Ø I PSEUDO_SERIAL_VERSION_UIDB 	directionL directionValuet 4Lnet/sf/jasperreports/engine/type/LineDirectionEnum;xr 5net.sf.jasperreports.engine.base.JRBaseGraphicElement      'Ø I PSEUDO_SERIAL_VERSION_UIDL fillq ~ L 	fillValuet +Lnet/sf/jasperreports/engine/type/FillEnum;L linePent #Lnet/sf/jasperreports/engine/JRPen;L penq ~ xr .net.sf.jasperreports.engine.base.JRBaseElement      'Ø I PSEUDO_SERIAL_VERSION_UIDI heightZ isPrintInFirstWholeBandZ isPrintRepeatedValuesZ isPrintWhenDetailOverflowsZ isRemoveLineWhenBlankB positionTypeB stretchTypeI widthI xI yL 	backcolort Ljava/awt/Color;L defaultStyleProvidert 4Lnet/sf/jasperreports/engine/JRDefaultStyleProvider;L elementGroupq ~ L 	forecolorq ~ 'L keyq ~ L modeq ~ L 	modeValuet +Lnet/sf/jasperreports/engine/type/ModeEnum;L parentStyleq ~ L parentStyleNameReferenceq ~ L positionTypeValuet 3Lnet/sf/jasperreports/engine/type/PositionTypeEnum;L printWhenExpressionq ~ L printWhenGroupChangest %Lnet/sf/jasperreports/engine/JRGroup;L 
propertiesMapt -Lnet/sf/jasperreports/engine/JRPropertiesMap;[ propertyExpressionst 3[Lnet/sf/jasperreports/engine/JRPropertyExpression;L stretchTypeValuet 2Lnet/sf/jasperreports/engine/type/StretchTypeEnum;xp  wî                 pq ~ q ~ pt line-20pppp~r 1net.sf.jasperreports.engine.type.PositionTypeEnum          xr java.lang.Enum          xpt FIX_RELATIVE_TO_TOPpppp~r 0net.sf.jasperreports.engine.type.StretchTypeEnum          xq ~ 2t 
NO_STRETCH  wîppsr *net.sf.jasperreports.engine.base.JRBasePen      'Ø I PSEUDO_SERIAL_VERSION_UIDL 	lineColorq ~ 'L 	lineStyleq ~ L lineStyleValuet 0Lnet/sf/jasperreports/engine/type/LineStyleEnum;L 	lineWidtht Ljava/lang/Float;L penContainert ,Lnet/sf/jasperreports/engine/JRPenContainer;xp  wîpp~r .net.sf.jasperreports.engine.type.LineStyleEnum          xq ~ 2t DASHEDsr java.lang.FloatÚíÉ¢Û<ðì F valuexr java.lang.Number¬à  xp?  q ~ /p  wî ~r 2net.sf.jasperreports.engine.type.LineDirectionEnum          xq ~ 2t TOP_DOWNsq ~ !  wî          	      ¢pq ~ q ~ pt line-1ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîpp~q ~ =t SOLIDsq ~ @?  q ~ Fp  wî q ~ Dsq ~ !  wî              s   pq ~ q ~ pt line-2ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ Isq ~ @?  q ~ Lp  wî q ~ Dsq ~ !  wî                 pq ~ q ~ pt line-3ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ Isq ~ @?  q ~ Pp  wî q ~ Dsq ~ !  wî          	      ³pq ~ q ~ pt line-4ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ Isq ~ @?  q ~ Tp  wî q ~ Dsq ~ !  wî          	      Äpq ~ q ~ pt line-5ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ Isq ~ @?  q ~ Xp  wî q ~ Dsq ~ !  wî          	      Õpq ~ q ~ pt line-6ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ Isq ~ @?  q ~ \p  wî q ~ Dsq ~ !  wî          	      æpq ~ q ~ pt line-7ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ Isq ~ @?  q ~ `p  wî q ~ Dsq ~ !  wî             b   ¢pq ~ q ~ pt line-8ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ Isq ~ @?  q ~ dp  wî q ~ Dsq ~ !  wî          	     ;pq ~ q ~ pt line-9ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ Isq ~ @?  q ~ hp  wî q ~ Dsq ~ !  wî           ª  b   ÷pq ~ q ~ pt line-10ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ Isq ~ @?  q ~ lp  wî q ~ Dsq ~ !  wî           ª  b  pq ~ q ~ pt line-11ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ Isq ~ @?  q ~ pp  wî q ~ Dsq ~ !  wî           ª  b  pq ~ q ~ pt line-12ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ Isq ~ @?  q ~ tp  wî q ~ Dsq ~ !  wî           ª  b  *pq ~ q ~ pt line-13ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ Isq ~ @?  q ~ xp  wî q ~ Dsq ~ !  wî   "           L   Äpq ~ q ~ pt line-14ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ Isq ~ @?  q ~ |p  wî q ~ Dsq ~ !  wî   "              Äpq ~ q ~ pt line-15ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ Isq ~ @?  q ~ p  wî q ~ Dsq ~ !  wî              |   Õpq ~ q ~ pt line-16ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ Isq ~ @?  q ~ p  wî q ~ Dsq ~ !  wî   "           í   Äpq ~ q ~ pt line-17ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ Isq ~ @?  q ~ p  wî q ~ Dsq ~ !  wî              Ñ   Äpq ~ q ~ pt line-18ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ Isq ~ @?  q ~ p  wî q ~ Dsq ~ !  wî          	     `pq ~ q ~ pt line-19ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ Isq ~ @?  q ~ p  wî q ~ Dsr 1net.sf.jasperreports.engine.base.JRBaseStaticText      'Ø L textq ~ xr 2net.sf.jasperreports.engine.base.JRBaseTextElement      'Ø %I PSEUDO_SERIAL_VERSION_UIDL borderq ~ L borderColorq ~ 'L bottomBorderq ~ L bottomBorderColorq ~ 'L 
bottomPaddingt Ljava/lang/Integer;L fontNameq ~ L fontSizeq ~ L horizontalAlignmentq ~ L horizontalAlignmentValuet 6Lnet/sf/jasperreports/engine/type/HorizontalAlignEnum;L isBoldt Ljava/lang/Boolean;L isItalicq ~ L 
isPdfEmbeddedq ~ L isStrikeThroughq ~ L isStyledTextq ~ L isUnderlineq ~ L 
leftBorderq ~ L leftBorderColorq ~ 'L leftPaddingq ~ L lineBoxt 'Lnet/sf/jasperreports/engine/JRLineBox;L lineSpacingq ~ L lineSpacingValuet 2Lnet/sf/jasperreports/engine/type/LineSpacingEnum;L markupq ~ L paddingq ~ L pdfEncodingq ~ L pdfFontNameq ~ L 
reportFontq ~ L rightBorderq ~ L rightBorderColorq ~ 'L rightPaddingq ~ L rotationq ~ L 
rotationValuet /Lnet/sf/jasperreports/engine/type/RotationEnum;L 	topBorderq ~ L topBorderColorq ~ 'L 
topPaddingq ~ L verticalAlignmentq ~ L verticalAlignmentValuet 4Lnet/sf/jasperreports/engine/type/VerticalAlignEnum;xq ~ &  wî           B      ¢pq ~ q ~ pt staticText-1ppppq ~ 3ppppq ~ 6  wîpppppt Arialsr java.lang.Integerâ ¤÷8 I valuexq ~ A   ppsr java.lang.BooleanÍ rÕúî Z valuexp ppppppppsr .net.sf.jasperreports.engine.base.JRBaseLineBox      'Ø L 
bottomPaddingq ~ L 	bottomPent +Lnet/sf/jasperreports/engine/base/JRBoxPen;L boxContainert ,Lnet/sf/jasperreports/engine/JRBoxContainer;L leftPaddingq ~ L leftPenq ~ ¥L paddingq ~ L penq ~ ¥L rightPaddingq ~ L rightPenq ~ ¥L 
topPaddingq ~ L topPenq ~ ¥xppsr 3net.sf.jasperreports.engine.base.JRBaseBoxBottomPen      'Ø  xr -net.sf.jasperreports.engine.base.JRBaseBoxPen      'Ø L lineBoxq ~ xq ~ 8  wîsr java.awt.Color¥3u F falphaI valueL cst Ljava/awt/color/ColorSpace;[ 	frgbvaluet [F[ fvalueq ~ ­xp    ÿ   ppppq ~ Isq ~ @    q ~ §q ~ §q ~ psr 1net.sf.jasperreports.engine.base.JRBaseBoxLeftPen      'Ø  xq ~ ©  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~ §q ~ §psq ~ ©  wîppppq ~ §q ~ §psr 2net.sf.jasperreports.engine.base.JRBaseBoxRightPen      'Ø  xq ~ ©  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~ §q ~ §psr 0net.sf.jasperreports.engine.base.JRBaseBoxTopPen      'Ø  xq ~ ©  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~ §q ~ §pppppt 	Helveticapppppppppp~r 2net.sf.jasperreports.engine.type.VerticalAlignEnum          xq ~ 2t MIDDLEt Local de pagamentosq ~   wî           B      ³pq ~ q ~ pt staticText-2ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~ Åq ~ Åq ~ Âpsq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~ Åq ~ Åpsq ~ ©  wîppppq ~ Åq ~ Åpsq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~ Åq ~ Åpsq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~ Åq ~ Åpppppt 	Helveticappppppppppq ~ ¿t 
BeneficiÃ¡riosq ~   wî           B      Äpq ~ q ~ pt staticText-3ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~ Øq ~ Øq ~ Õpsq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~ Øq ~ Øpsq ~ ©  wîppppq ~ Øq ~ Øpsq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~ Øq ~ Øpsq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~ Øq ~ Øpppppt 	Helveticappppppppppq ~ ¿t Data do Documentosq ~   wî           K      Õpq ~ q ~ pt staticText-4ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~ ëq ~ ëq ~ èpsq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~ ëq ~ ëpsq ~ ©  wîppppq ~ ëq ~ ëpsq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~ ëq ~ ëpsq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~ ëq ~ ëpppppt 	Helveticappppppppppq ~ ¿t Uso do Bancosq ~   wî           K      èpq ~ q ~ pt staticText-5ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~ þq ~ þq ~ ûpsq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~ þq ~ þpsq ~ ©  wîppppq ~ þq ~ þpsq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~ þq ~ þpsq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~ þq ~ þpppppt 	Helveticappppppppppq ~ ¿t InstruÃ§Ãµes :sq ~   wî           B   O   Äpq ~ q ~ pt staticText-6ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~q ~psq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~psq ~ ©  wîppppq ~q ~psq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~psq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~pppppt 	Helveticappppppppppq ~ ¿t NÂº do Documentosq ~   wî           *      Äpq ~ q ~ pt staticText-7ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~$q ~$q ~!psq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~$q ~$psq ~ ©  wîppppq ~$q ~$psq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~$q ~$psq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~$q ~$pppppt 	Helveticappppppppppq ~ ¿t 
EspÃ©cie Doc.sq ~   wî              Ô   Äpq ~ q ~ pt staticText-8ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~7q ~7q ~4psq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~7q ~7psq ~ ©  wîppppq ~7q ~7psq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~7q ~7psq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~7q ~7pppppt 	Helveticappppppppppq ~ ¿t Aceitesq ~   wî           X   ð   Äpq ~ q ~ pt staticText-9ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~Jq ~Jq ~Gpsq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~Jq ~Jpsq ~ ©  wîppppq ~Jq ~Jpsq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~Jq ~Jpsq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~Jq ~Jpppppt 	Helveticappppppppppq ~ ¿t Data do Processamentosq ~   wî           #   O   Õpq ~ q ~ pt 
staticText-10ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~]q ~]q ~Zpsq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~]q ~]psq ~ ©  wîppppq ~]q ~]psq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~]q ~]psq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~]q ~]pppppt 	Helveticappppppppppq ~ ¿t Carteirasq ~   wî              ~   Õpq ~ q ~ pt 
staticText-11ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~pq ~pq ~mpsq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~pq ~ppsq ~ ©  wîppppq ~pq ~ppsq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~pq ~ppsq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~pq ~ppppppt 	Helveticappppppppppq ~ ¿t EspÃ©ciesq ~   wî           B      Õpq ~ q ~ pt 
staticText-12ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~q ~psq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~psq ~ ©  wîppppq ~q ~psq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~psq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~pppppt 	Helveticappppppppppq ~ ¿t 
Quantidadesq ~   wî           B   ð   Õpq ~ q ~ pt 
staticText-13ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~q ~psq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~psq ~ ©  wîppppq ~q ~psq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~psq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~pppppt 	Helveticappppppppppq ~ ¿t Valorsq ~   wî           d  e   ¢pq ~ q ~ pt 
staticText-14ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~©q ~©q ~¦psq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~©q ~©psq ~ ©  wîppppq ~©q ~©psq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~©q ~©psq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~©q ~©pppppt 	Helveticappppppppppq ~ ¿t 
Vencimentosq ~   wî           d  e   ³pq ~ q ~ pt 
staticText-15ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~¼q ~¼q ~¹psq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~¼q ~¼psq ~ ©  wîppppq ~¼q ~¼psq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~¼q ~¼psq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~¼q ~¼pppppt 	Helveticappppppppppq ~ ¿t AgÃªncia / CÃ³digo Cedentesq ~   wî           d  e  *pq ~ q ~ pt 
staticText-16ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~Ïq ~Ïq ~Ìpsq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~Ïq ~Ïpsq ~ ©  wîppppq ~Ïq ~Ïpsq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~Ïq ~Ïpsq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~Ïq ~Ïpppppt 	Helveticappppppppppq ~ ¿t (=) Valor cobradosq ~   wî           d  e  pq ~ q ~ pt 
staticText-18ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~âq ~âq ~ßpsq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~âq ~âpsq ~ ©  wîppppq ~âq ~âpsq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~âq ~âpsq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~âq ~âpppppt 	Helveticappppppppppq ~ ¿t (+) Mora / Multasq ~   wî           d  e   æpq ~ q ~ pt 
staticText-20ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~õq ~õq ~òpsq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~õq ~õpsq ~ ©  wîppppq ~õq ~õpsq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~õq ~õpsq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~õq ~õpppppt 	Helveticappppppppppq ~ ¿t (-) Desconto / Abatimentosq ~   wî           d  e   Õpq ~ q ~ pt 
staticText-21ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~q ~psq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~psq ~ ©  wîppppq ~q ~psq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~psq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~pppppt 	Helveticappppppppppq ~ ¿t (=) Valor do Documentosq ~   wî           d  e   Äpq ~ q ~ pt 
staticText-22ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~q ~psq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~psq ~ ©  wîppppq ~q ~psq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~psq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~pppppt 	Helveticappppppppppq ~ ¿t 
Nosso NÃºmerosq ~   wî   	        !     <pq ~ q ~ pt 
staticText-23ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~.q ~.q ~+psq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~.q ~.psq ~ ©  wîppppq ~.q ~.psq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~.q ~.psq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~.q ~.pppppt 	Helveticappppppppppq ~ ¿t 	Pagador :sq ~   wî   	        A     Vpq ~ q ~ pt 
staticText-24ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~Aq ~Aq ~>psq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~Aq ~Apsq ~ ©  wîppppq ~Aq ~Apsq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~Aq ~Apsq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~Aq ~Apppppt 	Helveticappppppppppq ~ ¿t Sacador / Avalista :sq ~   wî   	        9  e  Vpq ~ q ~ pt 
staticText-25ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~Tq ~Tq ~Qpsq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~Tq ~Tpsq ~ ©  wîppppq ~Tq ~Tpsq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~Tq ~Tpsq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~Tq ~Tpppppt 	Helveticappppppppppq ~ ¿t CÃ³digo de baixa:sq ~   wî           ½  L  bpq ~ q ~ pt 
staticText-26ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡p~r 4net.sf.jasperreports.engine.type.HorizontalAlignEnum          xq ~ 2t CENTERq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~jq ~jq ~dpsq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~jq ~jpsq ~ ©  wîppppq ~jq ~jpsq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~jq ~jpsq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~jq ~jpppppt 	Helveticappppppppppq ~ ¿t 1AutenticaÃ§Ã£o mecÃ¢nica / Ficha de CompensaÃ§Ã£osr ,net.sf.jasperreports.engine.base.JRBaseImage      'Ø *I PSEUDO_SERIAL_VERSION_UIDI 
bookmarkLevelB evaluationTimeB hyperlinkTargetB 
hyperlinkTypeZ isLazyB onErrorTypeL anchorNameExpressionq ~ L borderq ~ L borderColorq ~ 'L bottomBorderq ~ L bottomBorderColorq ~ 'L 
bottomPaddingq ~ L evaluationGroupq ~ +L evaluationTimeValuet 5Lnet/sf/jasperreports/engine/type/EvaluationTimeEnum;L 
expressionq ~ L horizontalAlignmentq ~ L horizontalAlignmentValueq ~ L hyperlinkAnchorExpressionq ~ L hyperlinkPageExpressionq ~ [ hyperlinkParameterst 3[Lnet/sf/jasperreports/engine/JRHyperlinkParameter;L hyperlinkReferenceExpressionq ~ L hyperlinkTooltipExpressionq ~ L isUsingCacheq ~ L 
leftBorderq ~ L leftBorderColorq ~ 'L leftPaddingq ~ L lineBoxq ~ L 
linkTargetq ~ L linkTypeq ~ L onErrorTypeValuet 2Lnet/sf/jasperreports/engine/type/OnErrorTypeEnum;L paddingq ~ L rightBorderq ~ L rightBorderColorq ~ 'L rightPaddingq ~ L 
scaleImageq ~ L scaleImageValuet 1Lnet/sf/jasperreports/engine/type/ScaleImageEnum;L 	topBorderq ~ L topBorderColorq ~ 'L 
topPaddingq ~ L verticalAlignmentq ~ L verticalAlignmentValueq ~ xq ~ #  wî   #       ?     esq ~ «    ÿÿÿÿpppq ~ q ~ sq ~ «    ÿ   pppt 	barcode-1p~r )net.sf.jasperreports.engine.type.ModeEnum          xq ~ 2t OPAQUEppq ~ 3ppppq ~ 6  wîp~r )net.sf.jasperreports.engine.type.FillEnum          xq ~ 2t SOLIDsq ~ 8  wîppq ~ Isq ~ @    q ~p  wî         ppppppp~r 3net.sf.jasperreports.engine.type.EvaluationTimeEnum          xq ~ 2t NOWsr 1net.sf.jasperreports.engine.base.JRBaseExpression      'Ø I id[ chunkst 0[Lnet/sf/jasperreports/engine/JRExpressionChunk;L valueClassNameq ~ L valueClassRealNameq ~ xp   	ur 0[Lnet.sf.jasperreports.engine.JRExpressionChunk;mYÏÞiK£U  xp   sr 6net.sf.jasperreports.engine.base.JRBaseExpressionChunk      'Ø B typeL textq ~ xpt <it.businesslogic.ireport.barcode.BcImage.getBarcodeImage(13,sq ~t banco.codigoBarrassq ~t ,false,false,null,0,0)t java.awt.Imagepp~q ~gt LEFTpppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~q ~psq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~psq ~ ©  wîppppq ~q ~psq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~psq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~pp~r 0net.sf.jasperreports.engine.type.OnErrorTypeEnum          xq ~ 2t ERRORppppp~r /net.sf.jasperreports.engine.type.ScaleImageEnum          xq ~ 2t 
FILL_FRAMEpppp~q ~ ¾t TOPsq ~ !  wî                 pq ~ q ~ pt line-22ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ >sq ~ @?  q ~³p  wî q ~ Dsq ~   wî           d  ¨   pq ~ q ~ pt 
staticText-29ppppq ~ 3ppppq ~ 6  wîppppppsq ~     	pq ~hsq ~ ¢ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~»q ~»q ~·psq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~»q ~»psq ~ ©  wîppppq ~»q ~»psq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~»q ~»psq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~»q ~»pppppt Helvetica-Boldppppppppppq ~ ¿t Recibo do Pagadorsq ~ !  wî          	     pq ~ q ~ pt line-50ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ >sq ~ @?  q ~Ëp  wî q ~ Dsr 0net.sf.jasperreports.engine.base.JRBaseTextField      'Ø I PSEUDO_SERIAL_VERSION_UIDI 
bookmarkLevelB evaluationTimeB hyperlinkTargetB 
hyperlinkTypeZ isStretchWithOverflowL anchorNameExpressionq ~ L evaluationGroupq ~ +L evaluationTimeValueq ~{L 
expressionq ~ L hyperlinkAnchorExpressionq ~ L hyperlinkPageExpressionq ~ [ hyperlinkParametersq ~|L hyperlinkReferenceExpressionq ~ L hyperlinkTooltipExpressionq ~ L isBlankWhenNullq ~ L 
linkTargetq ~ L linkTypeq ~ L patternq ~ xq ~   wî           $   t   pq ~ q ~ pt textField-35ppppq ~ 3ppppq ~ 6  wîppppppppq ~hq ~ºppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~Òq ~Òq ~Ðpsq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~Òq ~Òpsq ~ ©  wîppppq ~Òq ~Òpsq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~Òq ~Òpsq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~Òq ~Òpppppt Helvetica-Boldppppppppppq ~ ¿  wî        ppq ~sq ~   
uq ~   sq ~t "707"t java.lang.Stringppppppq ~ºpppsq ~Ï  wî          p      pq ~ q ~ ppppppq ~ 3ppppq ~ 6  wîppppppppq ~hq ~ºppppppppsq ~ ¤psq ~ ¨  wîppppq ~çq ~çq ~æpsq ~ °  wîppppq ~çq ~çpsq ~ ©  wîppppq ~çq ~çpsq ~ µ  wîppppq ~çq ~çpsq ~ ¹  wîppppq ~çq ~çpppppt Helvetica-Boldppppppppppp  wî        ppq ~sq ~   uq ~   sq ~t banco.linhaDigitavelt java.lang.Stringppppppppppsq ~Ï  wî   	             »pq ~ q ~ ppppppq ~ 3ppppq ~ 6  wîpppppt Arialsq ~     pppppppppppsq ~ ¤psq ~ ¨  wîppppq ~öq ~öq ~ópsq ~ °  wîppppq ~öq ~öpsq ~ ©  wîppppq ~öq ~öpsq ~ µ  wîppppq ~öq ~öpsq ~ ¹  wîppppq ~öq ~öpppppppppppppppp~q ~ ¾t BOTTOM  wî        ppq ~sq ~   uq ~   sq ~t boleto.cedentet java.lang.Stringppppppppppsq ~Ï  wî   	       [      ªpq ~ q ~ ppppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~õpppppppppppsq ~ ¤psq ~ ¨  wîppppq ~q ~q ~psq ~ °  wîppppq ~q ~psq ~ ©  wîppppq ~q ~psq ~ µ  wîppppq ~q ~psq ~ ¹  wîppppq ~q ~ppppppppppppppppq ~ü  wî        ppq ~sq ~   
uq ~   sq ~t boleto.localPagamentot java.lang.Stringppppppq ~ºpppsq ~Ï  wî   	        B      Ìpq ~ q ~ ppppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~õpppppppppppsq ~ ¤psq ~ ¨  wîppppq ~q ~q ~psq ~ °  wîppppq ~q ~psq ~ ©  wîppppq ~q ~psq ~ µ  wîppppq ~q ~psq ~ ¹  wîppppq ~q ~ppppppppppppppppq ~ü  wî        ppq ~sq ~   uq ~   sq ~t boleto.dataDocumentot java.lang.Stringppppppq ~ºppt  sq ~Ï  wî   	        3      Ìpq ~ q ~ ppppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~õpq ~hpppppppppsq ~ ¤psq ~ ¨  wîppppq ~ q ~ q ~psq ~ °  wîppppq ~ q ~ psq ~ ©  wîppppq ~ q ~ psq ~ µ  wîppppq ~ q ~ psq ~ ¹  wîppppq ~ q ~ ppppppppppppppppq ~ü  wî        ppq ~sq ~   uq ~   sq ~t boleto.especieDocumentot java.lang.Stringppppppppppsq ~Ï  wî   	           Ó   Ìpq ~ q ~ ppppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~õpq ~hpppppppppsq ~ ¤psq ~ ¨  wîppppq ~-q ~-q ~+psq ~ °  wîppppq ~-q ~-psq ~ ©  wîppppq ~-q ~-psq ~ µ  wîppppq ~-q ~-psq ~ ¹  wîppppq ~-q ~-ppppppppppppppppq ~ü  wî        ppq ~sq ~   uq ~   sq ~t 
boleto.aceitet java.lang.Stringppppppppppsq ~Ï  wî   	        (   O   Ýpq ~ q ~ ppppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~õpq ~hpppppppppsq ~ ¤psq ~ ¨  wîppppq ~:q ~:q ~8psq ~ °  wîppppq ~:q ~:psq ~ ©  wîppppq ~:q ~:psq ~ µ  wîppppq ~:q ~:psq ~ ¹  wîppppq ~:q ~:ppppppppppppppppq ~ü  wî        ppq ~sq ~   uq ~   sq ~t boleto.carteirat java.lang.Stringppppppppppsq ~Ï  wî   	        \     ªpq ~ q ~ ppppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~õpq ~hpppppppppsq ~ ¤psq ~ ¨  wîppppq ~Gq ~Gq ~Epsq ~ °  wîppppq ~Gq ~Gpsq ~ ©  wîppppq ~Gq ~Gpsq ~ µ  wîppppq ~Gq ~Gpsq ~ ¹  wîppppq ~Gq ~Gppppppppppppppppq ~ü  wî        ppq ~sq ~   uq ~   sq ~t boleto.dataVencimentot java.lang.Stringppppppppppsq ~Ï  wî   	        \     Ýpq ~ q ~ ppppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~õp~q ~gt RIGHTpppppppppsq ~ ¤psq ~ ¨  wîppppq ~Vq ~Vq ~Rpsq ~ °  wîppppq ~Vq ~Vpsq ~ ©  wîppppq ~Vq ~Vpsq ~ µ  wîppppq ~Vq ~Vpsq ~ ¹  wîppppq ~Vq ~Vppppppppppppppppq ~ü  wî        ppq ~sq ~   uq ~   sq ~t boleto.valorBoletot java.lang.Stringppppppppppsq ~Ï  wî   	        é   %  <pq ~ q ~ ppppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~õpppppppppppsq ~ ¤psq ~ ¨  wîppppq ~cq ~cq ~apsq ~ °  wîppppq ~cq ~cpsq ~ ©  wîppppq ~cq ~cpsq ~ µ  wîppppq ~cq ~cpsq ~ ¹  wîppppq ~cq ~cppppppppppppppppq ~ü  wî        ppq ~sq ~   uq ~   	sq ~t ((sq ~t boleto.responsavelsq ~t  != null &&  !sq ~t boleto.responsavelsq ~t .isEmpty()) ? sq ~t boleto.responsavelsq ~t  :  sq ~t boleto.nomeSacadosq ~t )t java.lang.Stringppppppppppsq ~Ï  wî   	          k   Ëpq ~ q ~ pt textField-49ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~õpq ~Tpppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~q ~~psq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~psq ~ ©  wîppppq ~q ~psq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~psq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~ppppppppppppppppq ~ü  wî        ppq ~sq ~   uq ~   sq ~t boleto.carteirasq ~t 	 + "/" + sq ~t boleto.nossoNumerosq ~t +"-"+sq ~t boleto.dvNossoNumerot java.lang.Stringppppppq ~ºppq ~sq ~Ï  wî   	           %  Ipq ~ q ~ ppppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~õpppppppppppsq ~ ¤psq ~ ¨  wîppppq ~q ~q ~psq ~ °  wîppppq ~q ~psq ~ ©  wîppppq ~q ~psq ~ µ  wîppppq ~q ~psq ~ ¹  wîppppq ~q ~ppppppppppppppppq ~ü  wî        ppq ~sq ~   uq ~   sq ~t boleto.enderecoSacadot java.lang.Stringppppppppppsq ~Ï  wî   	        I   Å  Ipq ~ q ~ ppppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~õpppppppppppsq ~ ¤psq ~ ¨  wîppppq ~«q ~«q ~©psq ~ °  wîppppq ~«q ~«psq ~ ©  wîppppq ~«q ~«psq ~ µ  wîppppq ~«q ~«psq ~ ¹  wîppppq ~«q ~«ppppppppppppppppq ~ü  wî        ppq ~sq ~   uq ~   	sq ~t (sq ~t boleto.cepSacadosq ~t 
.length()<=5?sq ~t boleto.cepSacadosq ~t +"-000":sq ~t boleto.cepSacadosq ~t .substring(0,5)+"-"+sq ~t boleto.cepSacadosq ~t .substring(5))t java.lang.Stringppppppq ~ºpppsq ~Ï  wî   	        ²  W  Ipq ~ q ~ ppppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~õpppppppppppsq ~ ¤psq ~ ¨  wîppppq ~Èq ~Èq ~Æpsq ~ °  wîppppq ~Èq ~Èpsq ~ ©  wîppppq ~Èq ~Èpsq ~ µ  wîppppq ~Èq ~Èpsq ~ ¹  wîppppq ~Èq ~Èppppppppppppppppq ~ü  wî        ppq ~sq ~   uq ~   	sq ~t (sq ~t boleto.cidadeSacadosq ~t 
==null?"":sq ~t boleto.cidadeSacadosq ~t .trim() + "  " ) + ( sq ~t boleto.ufSacadosq ~t ==null? "" : ( sq ~t boleto.ufSacadosq ~t ))t java.lang.Stringppppppq ~ºpppsq ~Ï  wî   	        \     »pq ~ q ~ ppppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~õpq ~Tpppppppppsq ~ ¤psq ~ ¨  wîppppq ~åq ~åq ~ãpsq ~ °  wîppppq ~åq ~åpsq ~ ©  wîppppq ~åq ~åpsq ~ µ  wîppppq ~åq ~åpsq ~ ¹  wîppppq ~åq ~åppppppppppppppppq ~ü  wî        ppq ~sq ~   uq ~   sq ~t  banco.agenciaCodCedenteFormattedt java.lang.Stringppppppppppsq ~Ï  wî   F       T      ðpq ~ q ~ pt textField-24ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~õpq ~pppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~óq ~óq ~ðpsq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~óq ~ópsq ~ ©  wîppppq ~óq ~ópsq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~óq ~ópsq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~óq ~óppppppppppppppppq ~±  wî        ppq ~sq ~   uq ~   sq ~t boleto.instrucao1t java.lang.Stringppppppq ~ºpppsq ~Ï  wî           $   t   pq ~ q ~ pt textField-35ppppq ~ 3ppppq ~ 6  wîppppppppq ~hq ~ºppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~q ~psq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~psq ~ ©  wîppppq ~q ~psq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~psq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~pppppt Helvetica-Boldppppppppppq ~ ¿  wî        ppq ~sq ~   uq ~   sq ~t "707"t java.lang.Stringppppppq ~ºpppsq ~ !  wî                 pq ~ q ~ pt line-3ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ Isq ~ @?  q ~p  wî q ~ Dsq ~ !  wî              s   pq ~ q ~ pt line-2ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ Isq ~ @?  q ~ p  wî q ~ Dsq ~Ï  wî          p      pq ~ q ~ ppppppq ~ 3ppppq ~ 6  wîppppppppq ~hq ~ºppppppppsq ~ ¤psq ~ ¨  wîppppq ~%q ~%q ~$psq ~ °  wîppppq ~%q ~%psq ~ ©  wîppppq ~%q ~%psq ~ µ  wîppppq ~%q ~%psq ~ ¹  wîppppq ~%q ~%pppppt Helvetica-Boldppppppppppp  wî        ppq ~sq ~   uq ~   sq ~t banco.linhaDigitavelt java.lang.Stringppppppppppsq ~ !  wî          	      "pq ~ q ~ pt line-1ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ Isq ~ @?  q ~1p  wî q ~ Dsq ~   wî           B      #pq ~ q ~ pt staticText-1ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~8q ~8q ~5psq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~8q ~8psq ~ ©  wîppppq ~8q ~8psq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~8q ~8psq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~8q ~8pppppt 	Helveticappppppppppq ~ ¿t 
BeneficiÃ¡riosq ~ !  wî          	      4pq ~ q ~ pt line-1ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ Isq ~ @?  q ~Hp  wî q ~ Dsq ~Ï  wî   	        Ä      +pq ~ q ~ ppppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~õpppppppppppsq ~ ¤psq ~ ¨  wîppppq ~Nq ~Nq ~Lpsq ~ °  wîppppq ~Nq ~Npsq ~ ©  wîppppq ~Nq ~Npsq ~ µ  wîppppq ~Nq ~Npsq ~ ¹  wîppppq ~Nq ~Nppppppppppppppppq ~ü  wî        ppq ~sq ~   uq ~   sq ~t boleto.cedentet java.lang.Stringppppppppppsq ~ !  wî              Ä   #pq ~ q ~ pt line-3ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ Isq ~ @?  q ~Yp  wî q ~ Dsq ~   wî           %   Æ   #pq ~ q ~ pt staticText-1ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~`q ~`q ~]psq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~`q ~`psq ~ ©  wîppppq ~`q ~`psq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~`q ~`psq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~`q ~`pppppt 	Helveticappppppppppq ~ ¿t CNPJ/CPFsq ~ !  wî             
   #pq ~ q ~ pt line-3ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ Isq ~ @?  q ~pp  wî q ~ Dsq ~   wî           c  ¨   #pq ~ q ~ pt 
staticText-14ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~wq ~wq ~tpsq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~wq ~wpsq ~ ©  wîppppq ~wq ~wpsq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~wq ~wpsq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~wq ~wpppppt 	Helveticappppppppppq ~ ¿t 
Vencimentosq ~Ï  wî   	        c  ¨   +pq ~ q ~ ppppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~õpq ~hpppppppppsq ~ ¤psq ~ ¨  wîppppq ~q ~q ~psq ~ °  wîppppq ~q ~psq ~ ©  wîppppq ~q ~psq ~ µ  wîppppq ~q ~psq ~ ¹  wîppppq ~q ~ppppppppppppppppq ~ü  wî        ppq ~sq ~   uq ~   sq ~t boleto.dataVencimentot java.lang.Stringppppppppppsq ~ !  wî             ¦   #pq ~ q ~ pt line-3ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ Isq ~ @?  q ~p  wî q ~ Dsq ~   wî           =     #pq ~ q ~ pt staticText-1ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~q ~psq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~psq ~ ©  wîppppq ~q ~psq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~psq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~pppppt 	Helveticappppppppppq ~ ¿t Sacador Avalistasq ~ !  wî          	      Fpq ~ q ~ pt line-1ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ Isq ~ @?  q ~«p  wî q ~ Dsq ~   wî           y      5pq ~ q ~ pt staticText-1ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~²q ~²q ~¯psq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~²q ~²psq ~ ©  wîppppq ~²q ~²psq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~²q ~²psq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~²q ~²pppppt 	Helveticappppppppppq ~ ¿t *EndereÃ§o BeneficiÃ¡rio / Sacador Avalistasq ~ !  wî          	      Xpq ~ q ~ pt line-1ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ Isq ~ @?  q ~Âp  wî q ~ Dsq ~ !  wî          	      jpq ~ q ~ pt line-1ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ Isq ~ @?  q ~Æp  wî q ~ Dsq ~ !  wî   $           U   Fpq ~ q ~ pt line-3ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ Isq ~ @?  q ~Êp  wî q ~ Dsq ~ !  wî   $           ª   Fpq ~ q ~ pt line-3ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ Isq ~ @?  q ~Îp  wî q ~ Dsq ~ !  wî              Ü   Gpq ~ q ~ pt line-3ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ Isq ~ @?  q ~Òp  wî q ~ Dsq ~ !  wî              ÿ   Ypq ~ q ~ pt line-3ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ Isq ~ @?  q ~Öp  wî q ~ Dsq ~ !  wî             1   Ypq ~ q ~ pt line-3ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ Isq ~ @?  q ~Úp  wî q ~ Dsq ~ !  wî                Ypq ~ q ~ pt line-3ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ Isq ~ @?  q ~Þp  wî q ~ Dsq ~ !  wî             ¤   Gpq ~ q ~ pt line-3ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ Isq ~ @?  q ~âp  wî q ~ Dsq ~ !  wî             @   Gpq ~ q ~ pt line-3ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ Isq ~ @?  q ~æp  wî q ~ Dsq ~   wî           I      Gpq ~ q ~ pt staticText-1ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~íq ~íq ~êpsq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~íq ~ípsq ~ ©  wîppppq ~íq ~ípsq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~íq ~ípsq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~íq ~ípppppt 	Helveticappppppppppq ~ ¿t 
Nosso NÃºmerosq ~   wî           F   V   Gpq ~ q ~ pt staticText-1ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~ q ~ q ~ýpsq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~ q ~ psq ~ ©  wîppppq ~ q ~ psq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~ q ~ psq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~ q ~ pppppt 	Helveticappppppppppq ~ ¿t Carteirasq ~   wî              «   Gpq ~ q ~ pt staticText-1ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~q ~psq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~psq ~ ©  wîppppq ~q ~psq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~psq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~pppppt 	Helveticappppppppppq ~ ¿t EspÃ©ciesq ~   wî           %   Þ   Gpq ~ q ~ pt staticText-1ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~&q ~&q ~#psq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~&q ~&psq ~ ©  wîppppq ~&q ~&psq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~&q ~&psq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~&q ~&pppppt 	Helveticappppppppppq ~ ¿t 
Quantidadesq ~   wî           $  B   Gpq ~ q ~ pt staticText-1ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~9q ~9q ~6psq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~9q ~9psq ~ ©  wîppppq ~9q ~9psq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~9q ~9psq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~9q ~9pppppt 	Helveticappppppppppq ~ ¿t Valorsq ~   wî           e  ¦   Gpq ~ q ~ pt staticText-1ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~Lq ~Lq ~Ipsq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~Lq ~Lpsq ~ ©  wîppppq ~Lq ~Lpsq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~Lq ~Lpsq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~Lq ~Lpppppt 	Helveticappppppppppq ~ ¿t !AgÃªncia/CÃ³digo do BeneficiÃ¡riosq ~   wî           B     Ypq ~ q ~ pt staticText-1ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~_q ~_q ~\psq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~_q ~_psq ~ ©  wîppppq ~_q ~_psq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~_q ~_psq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~_q ~_pppppt 	Helveticappppppppppq ~ ¿t Valor do Documentosq ~   wî           L  2   Ypq ~ q ~ pt staticText-1ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~rq ~rq ~opsq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~rq ~rpsq ~ ©  wîppppq ~rq ~rpsq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~rq ~rpsq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~rq ~rpppppt 	Helveticappppppppppq ~ ¿t Data de Processamentosq ~   wî           "      Ypq ~ q ~ pt staticText-1ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~q ~psq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~psq ~ ©  wîppppq ~q ~psq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~psq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~pppppt 	Helveticappppppppppq ~ ¿t Aceitesq ~   wî           E   ¬   Ypq ~ q ~ pt staticText-1ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~q ~psq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~psq ~ ©  wîppppq ~q ~psq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~psq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~pppppt 	Helveticappppppppppq ~ ¿t EspÃ©cie do Documentosq ~   wî           E   W   Ypq ~ q ~ pt staticText-1ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~«q ~«q ~¨psq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~«q ~«psq ~ ©  wîppppq ~«q ~«psq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~«q ~«psq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~«q ~«pppppt 	Helveticappppppppppq ~ ¿t NÃºmero do Documentosq ~   wî           E      Ypq ~ q ~ pt staticText-1ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~¾q ~¾q ~»psq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~¾q ~¾psq ~ ©  wîppppq ~¾q ~¾psq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~¾q ~¾psq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~¾q ~¾pppppt 	Helveticappppppppppq ~ ¿t Data do Documentosq ~ !  wî             
   mpq ~ q ~ pt line-3ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ Isq ~ @?  q ~Îp  wî q ~ Dsq ~ !  wî             3   mpq ~ q ~ pt line-3ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ Isq ~ @?  q ~Òp  wî q ~ Dsq ~ !  wî           ×  3   mpq ~ q ~ pt line-1ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ Isq ~ @?  q ~Öp  wî q ~ Dsq ~   wî           Ö  4   npq ~ q ~ pt staticText-1ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡pq ~hq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~Ýq ~Ýq ~Úpsq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~Ýq ~Ýpsq ~ ©  wîppppq ~Ýq ~Ýpsq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~Ýq ~Ýpsq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~Ýq ~Ýpppppt 	Helveticappppppppppq ~ ¿t AutenticaÃ§Ã£o MecÃ¢nicasq ~Ï  wî   	        Q      Opq ~ q ~ pt textField-49ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~õpq ~hpppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~ðq ~ðq ~ípsq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~ðq ~ðpsq ~ ©  wîppppq ~ðq ~ðpsq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~ðq ~ðpsq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~ðq ~ðppppppppppppppppq ~ü  wî        ppq ~sq ~   uq ~   sq ~t boleto.nossoNumerot java.lang.Stringppppppq ~ºppq ~sq ~Ï  wî   	        R   W   Opq ~ q ~ ppppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~õpq ~hpppppppppsq ~ ¤psq ~ ¨  wîppppq ~q ~q ~psq ~ °  wîppppq ~q ~psq ~ ©  wîppppq ~q ~psq ~ µ  wîppppq ~q ~psq ~ ¹  wîppppq ~q ~ppppppppppppppppq ~ü  wî        ppq ~sq ~    uq ~   sq ~t boleto.carteirat java.lang.Stringppppppppppsq ~Ï  wî   	        v     apq ~ q ~ ppppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~õpq ~hpppppppppsq ~ ¤psq ~ ¨  wîppppq ~q ~q ~psq ~ °  wîppppq ~q ~psq ~ ©  wîppppq ~q ~psq ~ µ  wîppppq ~q ~psq ~ ¹  wîppppq ~q ~ppppppppppppppppq ~ü  wî        ppq ~sq ~   !uq ~   sq ~t boleto.valorBoletot java.lang.Stringppppppppppsq ~   wî           %     ´pq ~ q ~ pt staticText-1ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~ q ~ q ~psq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~ q ~ psq ~ ©  wîppppq ~ q ~ psq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~ q ~ psq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~ q ~ pppppt 	Helveticappppppppppq ~ ¿t CNPJ/CPFsq ~ !  wî                ³pq ~ q ~ pt line-3ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ Isq ~ @?  q ~0p  wî q ~ Dsq ~Ï  wî   	        0   ¬   Opq ~ q ~ ppppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~õpq ~hpppppppppsq ~ ¤psq ~ ¨  wîppppq ~6q ~6q ~4psq ~ °  wîppppq ~6q ~6psq ~ ©  wîppppq ~6q ~6psq ~ µ  wîppppq ~6q ~6psq ~ ¹  wîppppq ~6q ~6ppppppppppppppppq ~ü  wî        ppq ~sq ~   "uq ~   sq ~t boleto.especieDocumentot java.lang.Stringppppppppppsq ~Ï  wî   	           }   Ýpq ~ q ~ ppppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~õpq ~hpppppppppsq ~ ¤psq ~ ¨  wîppppq ~Cq ~Cq ~Apsq ~ °  wîppppq ~Cq ~Cpsq ~ ©  wîppppq ~Cq ~Cpsq ~ µ  wîppppq ~Cq ~Cpsq ~ ¹  wîppppq ~Cq ~Cppppppppppppppppq ~ü  wî        ppq ~sq ~   #uq ~   sq ~t "R$"t java.lang.Stringppppppppppsq ~   wî   	        #    <pq ~ q ~ pt 
staticText-24ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~Qq ~Qq ~Npsq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~Qq ~Qpsq ~ ©  wîppppq ~Qq ~Qpsq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~Qq ~Qpsq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~Qq ~Qpppppt 	Helveticappppppppppq ~ ¿t 	CNPJ/CPF:sq ~   wî   	        !     Ipq ~ q ~ pt 
staticText-23ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~dq ~dq ~apsq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~dq ~dpsq ~ ©  wîppppq ~dq ~dpsq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~dq ~dpsq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~dq ~dpppppt 	Helveticappppppppppq ~ ¿t 	EndereÃ§osq ~   wî   	        #    Vpq ~ q ~ pt 
staticText-24ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~wq ~wq ~tpsq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~wq ~wpsq ~ ©  wîppppq ~wq ~wpsq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~wq ~wpsq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~wq ~wpppppt 	Helveticappppppppppq ~ ¿t CNPJ:sq ~Ï  wî   	       
      =pq ~ q ~ ppppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~õpppppppppppsq ~ ¤psq ~ ¨  wîppppq ~q ~q ~psq ~ °  wîppppq ~q ~psq ~ ©  wîppppq ~q ~psq ~ µ  wîppppq ~q ~psq ~ ¹  wîppppq ~q ~ppppppppppppppppq ~ü  wî        ppq ~sq ~   $uq ~   sq ~t enderecoEmpresat java.lang.Stringppppppppppsq ~z  wî           l      pq ~ q ~ ppppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppppq ~p  wî         pppppppq ~sq ~   %uq ~   sq ~t 
SUBREPORT_DIRsq ~t   + "logos//logo-daycoval.gif"t java.lang.Stringppppppppppppsq ~ ¤psq ~ ¨  wîppppq ~q ~q ~psq ~ °  wîppppq ~q ~psq ~ ©  wîppppq ~q ~psq ~ µ  wîppppq ~q ~psq ~ ¹  wîppppq ~q ~ppq ~¬ppppp~q ~®t 	REAL_SIZEpppppsq ~z  wî           l      pq ~ q ~ ppppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppppq ~¥p  wî         pppppppq ~sq ~   &uq ~   sq ~t 
SUBREPORT_DIRsq ~t   + "logos//logo-daycoval.gif"t java.lang.Stringppppppppppppsq ~ ¤psq ~ ¨  wîppppq ~®q ~®q ~¥psq ~ °  wîppppq ~®q ~®psq ~ ©  wîppppq ~®q ~®psq ~ µ  wîppppq ~®q ~®psq ~ ¹  wîppppq ~®q ~®ppq ~¬pppppq ~£pppppsq ~Ï  wî   	        E   Ç   +pq ~ q ~ ppppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~õpppppppppppsq ~ ¤psq ~ ¨  wîppppq ~¶q ~¶q ~´psq ~ °  wîppppq ~¶q ~¶psq ~ ©  wîppppq ~¶q ~¶psq ~ µ  wîppppq ~¶q ~¶psq ~ ¹  wîppppq ~¶q ~¶ppppppppppppppppq ~ü  wî        ppq ~sq ~   'uq ~   sq ~t cnpjEmpresat java.lang.Stringppppppppppsq ~Ï  wî   	        D     »pq ~ q ~ ppppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~õpppppppppppsq ~ ¤psq ~ ¨  wîppppq ~Ãq ~Ãq ~Ápsq ~ °  wîppppq ~Ãq ~Ãpsq ~ ©  wîppppq ~Ãq ~Ãpsq ~ µ  wîppppq ~Ãq ~Ãpsq ~ ¹  wîppppq ~Ãq ~Ãppppppppppppppppq ~ü  wî        ppq ~sq ~   (uq ~   sq ~t cnpjEmpresat java.lang.Stringppppppppppsq ~Ï  wî   	        P      apq ~ q ~ ppppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~õpq ~hpppppppppsq ~ ¤psq ~ ¨  wîppppq ~Ðq ~Ðq ~Îpsq ~ °  wîppppq ~Ðq ~Ðpsq ~ ©  wîppppq ~Ðq ~Ðpsq ~ µ  wîppppq ~Ðq ~Ðpsq ~ ¹  wîppppq ~Ðq ~Ðppppppppppppppppq ~ü  wî        ppq ~sq ~   )uq ~   sq ~t boleto.dataDocumentot java.lang.Stringppppppq ~ºppq ~sq ~Ï  wî   	        d  §   Opq ~ q ~ ppppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~õpq ~hpppppppppsq ~ ¤psq ~ ¨  wîppppq ~Ýq ~Ýq ~Ûpsq ~ °  wîppppq ~Ýq ~Ýpsq ~ ©  wîppppq ~Ýq ~Ýpsq ~ µ  wîppppq ~Ýq ~Ýpsq ~ ¹  wîppppq ~Ýq ~Ýppppppppppppppppq ~ü  wî        ppq ~sq ~   *uq ~   sq ~t  banco.agenciaCodCedenteFormattedt java.lang.Stringppppppppppsq ~Ï  wî   	        G    Ipq ~ q ~ ppppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~õpppppppppppsq ~ ¤psq ~ ¨  wîppppq ~êq ~êq ~èpsq ~ °  wîppppq ~êq ~êpsq ~ ©  wîppppq ~êq ~êpsq ~ µ  wîppppq ~êq ~êpsq ~ ¹  wîppppq ~êq ~êppppppppppppppppq ~ü  wî        ppq ~sq ~   +uq ~   sq ~t boleto.bairroSacadot java.lang.Stringppppppq ~ºpppsq ~Ï  wî   	        F  2  ;pq ~ q ~ ppppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~õpppppppppppsq ~ ¤psq ~ ¨  wîppppq ~÷q ~÷q ~õpsq ~ °  wîppppq ~÷q ~÷psq ~ ©  wîppppq ~÷q ~÷psq ~ µ  wîppppq ~÷q ~÷psq ~ ¹  wîppppq ~÷q ~÷ppppppppppppppppq ~ ¿  wî        ppq ~sq ~   ,uq ~   sq ~t boleto.cpfSacadot java.lang.Stringppppppppppsq ~Ï  wî   	        h   ñ   Ìpq ~ q ~ ppppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~õpppppppppppsq ~ ¤psq ~ ¨  wîppppq ~q ~q ~psq ~ °  wîppppq ~q ~psq ~ ©  wîppppq ~q ~psq ~ µ  wîppppq ~q ~psq ~ ¹  wîppppq ~q ~ppppppppppppppppq ~ü  wî        ppq ~sq ~   -uq ~   sq ~t boleto.dataProcessamentot java.lang.Stringppppppppppsq ~Ï  wî   	        ^  2   apq ~ q ~ ppppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~õpppppppppppsq ~ ¤psq ~ ¨  wîppppq ~q ~q ~psq ~ °  wîppppq ~q ~psq ~ ©  wîppppq ~q ~psq ~ µ  wîppppq ~q ~psq ~ ¹  wîppppq ~q ~ppppppppppppppppq ~ü  wî        ppq ~sq ~   .uq ~   sq ~t boleto.dataProcessamentot java.lang.Stringppppppppppsq ~Ï  wî   	        Q   W   apq ~ q ~ pt textField-49ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~õpq ~hpppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~q ~psq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~psq ~ ©  wîppppq ~q ~psq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~psq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~ppppppppppppppppq ~ü  wî        ppq ~sq ~   /uq ~   sq ~t boleto.noDocumentot java.lang.Stringppppppq ~ºppq ~sq ~Ï  wî   	        B   O   Ìpq ~ q ~ pt textField-49ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~õpq ~hpppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~5q ~5q ~2psq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~5q ~5psq ~ ©  wîppppq ~5q ~5psq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~5q ~5psq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~5q ~5ppppppppppppppppq ~ü  wî        ppq ~sq ~   0uq ~   sq ~t boleto.noDocumentot java.lang.Stringppppppq ~ºppq ~xp  wî  pp~r .net.sf.jasperreports.engine.type.SplitTypeEnum          xq ~ 2t STRETCHppsr java.util.HashSetºD¸·4  xpw   ?@     t "net.sf.jasperreports.engine.data.*t net.sf.jasperreports.engine.*t java.util.*xt javapsr .net.sf.jasperreports.engine.base.JRBaseDataset      'Ø I PSEUDO_SERIAL_VERSION_UIDZ isMainB whenResourceMissingType[ fieldst &[Lnet/sf/jasperreports/engine/JRField;L filterExpressionq ~ [ groupst &[Lnet/sf/jasperreports/engine/JRGroup;L nameq ~ [ 
parameterst *[Lnet/sf/jasperreports/engine/JRParameter;L 
propertiesMapq ~ ,L queryt %Lnet/sf/jasperreports/engine/JRQuery;L resourceBundleq ~ L scriptletClassq ~ [ 
scriptletst *[Lnet/sf/jasperreports/engine/JRScriptlet;[ 
sortFieldst *[Lnet/sf/jasperreports/engine/JRSortField;[ 	variablest )[Lnet/sf/jasperreports/engine/JRVariable;L whenResourceMissingTypeValuet >Lnet/sf/jasperreports/engine/type/WhenResourceMissingTypeEnum;xp  wî ur &[Lnet.sf.jasperreports.engine.JRField;<ßÇN*òp  xp   sr ,net.sf.jasperreports.engine.base.JRBaseField      'Ø L descriptionq ~ L nameq ~ L 
propertiesMapq ~ ,L valueClassNameq ~ L valueClassRealNameq ~ xppt bancosr +net.sf.jasperreports.engine.JRPropertiesMap      'Ø L baseq ~ ,L propertiesListq ~ L 
propertiesMapt Ljava/util/Map;xppppt java.lang.Objectpsq ~]pt banco.numeroFormattedsq ~`pppt java.lang.Stringpsq ~]pt banco.linhaDigitavelsq ~`pppt java.lang.Stringpsq ~]pt banco.codigoBarrassq ~`pppt java.lang.Stringpsq ~]pt boleto.cedentesq ~`pppt java.lang.Stringpsq ~]pt boleto.localPagamentosq ~`pppt java.lang.Stringpsq ~]pt boleto.dataDocumentosq ~`pppt java.lang.Stringpsq ~]pt boleto.especieDocumentosq ~`pppt java.lang.Stringpsq ~]pt 
boleto.aceitesq ~`pppt java.lang.Stringpsq ~]pt boleto.dataProcessamentosq ~`pppt java.lang.Stringpsq ~]pt boleto.carteirasq ~`pppt java.lang.Stringpsq ~]pt boleto.dataVencimentosq ~`pppt java.lang.Stringpsq ~]pt boleto.nossoNumerosq ~`pppt java.lang.Stringpsq ~]pt boleto.valorBoletosq ~`pppt java.lang.Stringpsq ~]pt boleto.nomeSacadosq ~`pppt java.lang.Stringpsq ~]pt boleto.enderecoSacadosq ~`pppt java.lang.Stringpsq ~]pt boleto.cepSacadosq ~`pppt java.lang.Stringpsq ~]pt boleto.cidadeSacadosq ~`pppt java.lang.Stringpsq ~]pt boleto.ufSacadosq ~`pppt java.lang.Stringpsq ~]pt  banco.agenciaCodCedenteFormattedsq ~`pppt java.lang.Stringpsq ~]pt boleto.instrucao1sq ~`pppt java.lang.Stringpsq ~]pt banco.bancosq ~`pppt java.lang.Stringpsq ~]pt boleto.dvNossoNumerosq ~`pppt java.lang.Stringpsq ~]pt boleto.bairroSacadosq ~`pppt java.lang.Stringpsq ~]t boleto.responsavelt boleto.responsavelsq ~`pppt java.lang.Stringpsq ~]pt boleto.cpfSacadosq ~`pppt java.lang.Stringpsq ~]pt boleto.noDocumentosq ~`pppt java.lang.Stringpppt boletour *[Lnet.sf.jasperreports.engine.JRParameter;" *Ã`!  xp   sr 0net.sf.jasperreports.engine.base.JRBaseParameter      'Ø 	Z isForPromptingZ isSystemDefinedL defaultValueExpressionq ~ L descriptionq ~ L nameq ~ L nestedTypeNameq ~ L 
propertiesMapq ~ ,L valueClassNameq ~ L valueClassRealNameq ~ xpppt REPORT_PARAMETERS_MAPpsq ~`pppt 
java.util.Mappsq ~Ðppt 
JASPER_REPORTpsq ~`pppt (net.sf.jasperreports.engine.JasperReportpsq ~Ðppt REPORT_CONNECTIONpsq ~`pppt java.sql.Connectionpsq ~Ðppt REPORT_MAX_COUNTpsq ~`pppt java.lang.Integerpsq ~Ðppt REPORT_DATA_SOURCEpsq ~`pppt (net.sf.jasperreports.engine.JRDataSourcepsq ~Ðppt REPORT_SCRIPTLETpsq ~`pppt /net.sf.jasperreports.engine.JRAbstractScriptletpsq ~Ðppt 
REPORT_LOCALEpsq ~`pppt java.util.Localepsq ~Ðppt REPORT_RESOURCE_BUNDLEpsq ~`pppt java.util.ResourceBundlepsq ~Ðppt REPORT_TIME_ZONEpsq ~`pppt java.util.TimeZonepsq ~Ðppt REPORT_FORMAT_FACTORYpsq ~`pppt .net.sf.jasperreports.engine.util.FormatFactorypsq ~Ðppt REPORT_CLASS_LOADERpsq ~`pppt java.lang.ClassLoaderpsq ~Ðppt REPORT_URL_HANDLER_FACTORYpsq ~`pppt  java.net.URLStreamHandlerFactorypsq ~Ðppt REPORT_FILE_RESOLVERpsq ~`pppt -net.sf.jasperreports.engine.util.FileResolverpsq ~Ðppt REPORT_TEMPLATESpsq ~`pppt java.util.Collectionpsq ~Ðppt REPORT_VIRTUALIZERpsq ~`pppt )net.sf.jasperreports.engine.JRVirtualizerpsq ~Ðppt IS_IGNORE_PAGINATIONpsq ~`pppt java.lang.Booleanpsq ~Ð  ppt enderecoEmpresapsq ~`pppt java.lang.Stringpsq ~Ð sq ~    pt java.lang.Stringppt 
SUBREPORT_DIRpsq ~`pppq ~psq ~Ð ppt cnpjEmpresapsq ~`pppt java.lang.Stringpsq ~`psq ~    w   t ireport.scriptlethandlingt ireport.encodingt ireport.zoomt 	ireport.xt 	ireport.yxsr java.util.HashMapÚÁÃ`Ñ F 
loadFactorI 	thresholdxp?@     w      q ~"t 2.0q ~!t UTF-8q ~#t 0q ~$t 0q ~ t 0xsr ,net.sf.jasperreports.engine.base.JRBaseQuery      'Ø [ chunkst +[Lnet/sf/jasperreports/engine/JRQueryChunk;L languageq ~ xppt sqlppppur )[Lnet.sf.jasperreports.engine.JRVariable;bæ|,·D  xp   sr /net.sf.jasperreports.engine.base.JRBaseVariable      'Ø I PSEUDO_SERIAL_VERSION_UIDB calculationB 
incrementTypeZ isSystemDefinedB 	resetTypeL calculationValuet 2Lnet/sf/jasperreports/engine/type/CalculationEnum;L 
expressionq ~ L incrementGroupq ~ +L incrementTypeValuet 4Lnet/sf/jasperreports/engine/type/IncrementTypeEnum;L incrementerFactoryClassNameq ~ L incrementerFactoryClassRealNameq ~ L initialValueExpressionq ~ L nameq ~ L 
resetGroupq ~ +L resetTypeValuet 0Lnet/sf/jasperreports/engine/type/ResetTypeEnum;L valueClassNameq ~ L valueClassRealNameq ~ xp  wî   ~r 0net.sf.jasperreports.engine.type.CalculationEnum          xq ~ 2t SYSTEMpp~r 2net.sf.jasperreports.engine.type.IncrementTypeEnum          xq ~ 2t NONEppsq ~   uq ~   sq ~t new java.lang.Integer(1)q ~àpt PAGE_NUMBERp~r .net.sf.jasperreports.engine.type.ResetTypeEnum          xq ~ 2t REPORTq ~àpsq ~2  wî   q ~8ppq ~;ppsq ~   uq ~   sq ~t new java.lang.Integer(1)q ~àpt 
COLUMN_NUMBERp~q ~Bt PAGEq ~àpsq ~2  wî   ~q ~7t COUNTsq ~   uq ~   sq ~t new java.lang.Integer(1)q ~àppq ~;ppsq ~   uq ~   sq ~t new java.lang.Integer(0)q ~àpt REPORT_COUNTpq ~Cq ~àpsq ~2  wî   q ~Nsq ~   uq ~   sq ~t new java.lang.Integer(1)q ~àppq ~;ppsq ~   uq ~   sq ~t new java.lang.Integer(0)q ~àpt 
PAGE_COUNTpq ~Kq ~àpsq ~2  wî   q ~Nsq ~   uq ~   sq ~t new java.lang.Integer(1)q ~àppq ~;ppsq ~   uq ~   sq ~t new java.lang.Integer(0)q ~àpt COLUMN_COUNTp~q ~Bt COLUMNq ~àp~r <net.sf.jasperreports.engine.type.WhenResourceMissingTypeEnum          xq ~ 2t NULLq ~Íp~r 0net.sf.jasperreports.engine.type.OrientationEnum          xq ~ 2t PORTRAITpp~r /net.sf.jasperreports.engine.type.PrintOrderEnum          xq ~ 2t VERTICALpppp~r 3net.sf.jasperreports.engine.type.WhenNoDataTypeEnum          xq ~ 2t NO_PAGESsr 6net.sf.jasperreports.engine.design.JRReportCompileData      'Ø L crosstabCompileDataq ~aL datasetCompileDataq ~aL mainDatasetCompileDataq ~ xpsq ~%?@     w       xsq ~%?@     w       xur [B¬óøTà  xp  .Êþº¾   .u boleto_1545788388223_198831  ,net/sf/jasperreports/engine/fill/JREvaluator  parameter_enderecoEmpresa 2Lnet/sf/jasperreports/engine/fill/JRFillParameter; parameter_REPORT_LOCALE parameter_JASPER_REPORT parameter_REPORT_VIRTUALIZER parameter_REPORT_TIME_ZONE parameter_REPORT_FILE_RESOLVER parameter_REPORT_SCRIPTLET parameter_REPORT_PARAMETERS_MAP parameter_REPORT_CONNECTION parameter_REPORT_CLASS_LOADER parameter_REPORT_DATA_SOURCE $parameter_REPORT_URL_HANDLER_FACTORY parameter_IS_IGNORE_PAGINATION parameter_SUBREPORT_DIR parameter_REPORT_FORMAT_FACTORY parameter_REPORT_MAX_COUNT parameter_REPORT_TEMPLATES parameter_cnpjEmpresa  parameter_REPORT_RESOURCE_BUNDLE field_boleto46cedente .Lnet/sf/jasperreports/engine/fill/JRFillField; field_boleto46enderecoSacado field_boleto46dvNossoNumero field_banco46codigoBarras 'field_banco46agenciaCodCedenteFormatted field_boleto46nomeSacado field_boleto46aceite field_banco46banco field_boleto46valorBoleto field_boleto46especieDocumento field_banco46numeroFormatted field_banco field_boleto46dataVencimento field_boleto46dataProcessamento field_boleto46ufSacado field_boleto46bairroSacado field_boleto46cepSacado field_boleto46dataDocumento field_banco46linhaDigitavel field_boleto46nossoNumero field_boleto46noDocumento field_boleto46responsavel field_boleto46cpfSacado field_boleto46localPagamento field_boleto46cidadeSacado field_boleto46carteira field_boleto46instrucao1 variable_PAGE_NUMBER 1Lnet/sf/jasperreports/engine/fill/JRFillVariable; variable_COLUMN_NUMBER variable_REPORT_COUNT variable_PAGE_COUNT variable_COLUMN_COUNT <init> ()V Code ; <
  >  	  @  	  B  	  D 	 	  F 
 	  H  	  J  	  L 
 	  N  	  P  	  R  	  T  	  V  	  X  	  Z  	  \  	  ^  	  `  	  b  	  d  	  f  	  h  	  j  	  l  	  n  	  p   	  r ! 	  t " 	  v # 	  x $ 	  z % 	  | & 	  ~ ' 	   ( 	   ) 	   * 	   + 	   , 	   - 	   . 	   / 	   0 	   1 	   2 	   3 	   4 	   5 6	   7 6	   8 6	    9 6	  ¢ : 6	  ¤ LineNumberTable customizedInit 0(Ljava/util/Map;Ljava/util/Map;Ljava/util/Map;)V 
initParams (Ljava/util/Map;)V © ª
  « 
initFields ­ ª
  ® initVars ° ª
  ± enderecoEmpresa ³ 
java/util/Map µ get &(Ljava/lang/Object;)Ljava/lang/Object; · ¸ ¶ ¹ 0net/sf/jasperreports/engine/fill/JRFillParameter » 
REPORT_LOCALE ½ 
JASPER_REPORT ¿ REPORT_VIRTUALIZER Á REPORT_TIME_ZONE Ã REPORT_FILE_RESOLVER Å REPORT_SCRIPTLET Ç REPORT_PARAMETERS_MAP É REPORT_CONNECTION Ë REPORT_CLASS_LOADER Í REPORT_DATA_SOURCE Ï REPORT_URL_HANDLER_FACTORY Ñ IS_IGNORE_PAGINATION Ó 
SUBREPORT_DIR Õ REPORT_FORMAT_FACTORY × REPORT_MAX_COUNT Ù REPORT_TEMPLATES Û cnpjEmpresa Ý REPORT_RESOURCE_BUNDLE ß boleto.cedente á ,net/sf/jasperreports/engine/fill/JRFillField ã boleto.enderecoSacado å boleto.dvNossoNumero ç banco.codigoBarras é  banco.agenciaCodCedenteFormatted ë boleto.nomeSacado í 
boleto.aceite ï banco.banco ñ boleto.valorBoleto ó boleto.especieDocumento õ banco.numeroFormatted ÷ banco ù boleto.dataVencimento û boleto.dataProcessamento ý boleto.ufSacado ÿ boleto.bairroSacado boleto.cepSacado boleto.dataDocumento banco.linhaDigitavel boleto.nossoNumero	 boleto.noDocumento boleto.responsavel
 boleto.cpfSacado boleto.localPagamento boleto.cidadeSacado boleto.carteira boleto.instrucao1 PAGE_NUMBER /net/sf/jasperreports/engine/fill/JRFillVariable 
COLUMN_NUMBER REPORT_COUNT 
PAGE_COUNT! COLUMN_COUNT# evaluate (I)Ljava/lang/Object; 
Exceptions java/lang/Throwable( java/lang/Integer* (I)V ;,
+- getValue ()Ljava/lang/Object;/0
 ä1 java/lang/String3 (it/businesslogic/ireport/barcode/BcImage5 getBarcodeImage I(ILjava/lang/Object;ZZLjava/lang/String;II)Ljava/awt/image/BufferedImage;78
69 707; isEmpty ()Z=>
4? java/lang/StringBufferA valueOf &(Ljava/lang/Object;)Ljava/lang/String;CD
4E (Ljava/lang/String;)V ;G
BH /J append ,(Ljava/lang/String;)Ljava/lang/StringBuffer;LM
BN -P toString ()Ljava/lang/String;RS
BT length ()IVW
4X -000Z 	substring (II)Ljava/lang/String;\]
4^ (I)Ljava/lang/String;\`
4a  c trimeS
4f   h R$j
 ¼1 logos//logo-daycoval.gifm evaluateOld getOldValuep0
 äq evaluateEstimated 
SourceFile !     3                 	     
               
                                                                                                !     "     #     $     %     &     '     (     )     *     +     ,     -     .     /     0     1     2     3     4     5 6    7 6    8 6    9 6    : 6     ; <  =  ì    *· ?*µ A*µ C*µ E*µ G*µ I*µ K*µ M*µ O*µ Q*µ S*µ U*µ W*µ Y*µ [*µ ]*µ _*µ a*µ c*µ e*µ g*µ i*µ k*µ m*µ o*µ q*µ s*µ u*µ w*µ y*µ {*µ }*µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ ¡*µ £*µ ¥±    ¦   Ö 5      	          ! " " ' # , $ 1 % 6 & ; ' @ ( E ) J * O + T , Y - ^ . c / h 0 m 1 r 2 w 3 | 4  5  6  7  8  9  :  ; ¤ < © = ® > ³ ? ¸ @ ½ A Â B Ç C Ì D Ñ E Ö F Û G à H å I ê J ï K ô L ù M þ N   § ¨  =   4     *+· ¬*,· ¯*-· ²±    ¦       Z  [ 
 \  ]  © ª  =  »    W*+´¹ º À ¼À ¼µ A*+¾¹ º À ¼À ¼µ C*+À¹ º À ¼À ¼µ E*+Â¹ º À ¼À ¼µ G*+Ä¹ º À ¼À ¼µ I*+Æ¹ º À ¼À ¼µ K*+È¹ º À ¼À ¼µ M*+Ê¹ º À ¼À ¼µ O*+Ì¹ º À ¼À ¼µ Q*+Î¹ º À ¼À ¼µ S*+Ð¹ º À ¼À ¼µ U*+Ò¹ º À ¼À ¼µ W*+Ô¹ º À ¼À ¼µ Y*+Ö¹ º À ¼À ¼µ [*+Ø¹ º À ¼À ¼µ ]*+Ú¹ º À ¼À ¼µ _*+Ü¹ º À ¼À ¼µ a*+Þ¹ º À ¼À ¼µ c*+à¹ º À ¼À ¼µ e±    ¦   R    e  f $ g 6 h H i Z j l k ~ l  m ¢ n ´ o Æ p Ø q ê r ü s t  u2 vD wV x  ­ ª  =  x    ô*+â¹ º À äÀ äµ g*+æ¹ º À äÀ äµ i*+è¹ º À äÀ äµ k*+ê¹ º À äÀ äµ m*+ì¹ º À äÀ äµ o*+î¹ º À äÀ äµ q*+ð¹ º À äÀ äµ s*+ò¹ º À äÀ äµ u*+ô¹ º À äÀ äµ w*+ö¹ º À äÀ äµ y*+ø¹ º À äÀ äµ {*+ú¹ º À äÀ äµ }*+ü¹ º À äÀ äµ *+þ¹ º À äÀ äµ *+ ¹ º À äÀ äµ *+¹ º À äÀ äµ *+¹ º À äÀ äµ *+¹ º À äÀ äµ *+¹ º À äÀ äµ *+
¹ º À äÀ äµ *+¹ º À äÀ äµ *+¹ º À äÀ äµ *+¹ º À äÀ äµ *+¹ º À äÀ äµ *+¹ º À äÀ äµ *+¹ º À äÀ äµ *+¹ º À äÀ äµ ±    ¦   r       $  6  H  Z  l  ~    ¢  ´  Æ  Ø  ê  ü  " 5 H [ n   § º Í à ó   ° ª  =        `*+¹ º ÀÀµ *+¹ º ÀÀµ *+ ¹ º ÀÀµ ¡*+"¹ º ÀÀµ £*+$¹ º ÀÀµ ¥±    ¦       £  ¤ & ¥ 9 ¦ L § _ ¨ %& '    ) =  4    Mª         0   Ñ   Ö   â   î   ú        *  6  N  U  c  q        ©  ·  Å  Ó    L  Z  Â  &  4  B  I  W  e  s        «  ²  À  á        ,  :  H  V  d  r  M§¸»+Y·.M§¬»+Y·.M§ »+Y·.M§»+Y·.M§»+Y·.M§|»+Y·.M§p»+Y·.M§d»+Y·.M§X
*´ m¶2À4¸:M§@<M§9*´ ¶2À4M§+*´ g¶2À4M§*´ ¶2À4M§*´ ¶2À4M§*´ y¶2À4M§ó*´ s¶2À4M§å*´ ¶2À4M§×*´ ¶2À4M§É*´ w¶2À4M§»*´ ¶2À4Æ  *´ ¶2À4¶@ *´ ¶2À4§ 
*´ q¶2À4M§»BY*´ ¶2À4¸F·IK¶O*´ ¶2À4¶OQ¶O*´ k¶2À4¶O¶UM§B*´ i¶2À4M§4*´ ¶2À4¶Y£ #»BY*´ ¶2À4¸F·I[¶O¶U§ 6»BY*´ ¶2À4¶_¸F·IQ¶O*´ ¶2À4¶b¶O¶UM§Ì»BY*´ ¶2À4Ç 	d§ #»BY*´ ¶2À4¶g¸F·Ii¶O¶U¸F·I*´ ¶2À4Ç 	d§ 
*´ ¶2À4¶O¶UM§h*´ o¶2À4M§Z*´ ¶2À4M§L<M§E*´ ¶2À4M§7*´ g¶2À4M§)*´ ¶2À4M§*´ ¶2À4M§
*´ ¶2À4M§ ÿ*´ w¶2À4M§ ñ*´ y¶2À4M§ ãkM§ Ü*´ A¶lÀ4M§ Î»BY*´ [¶lÀ4¸F·In¶O¶UM§ ­»BY*´ [¶lÀ4¸F·In¶O¶UM§ *´ c¶lÀ4M§ ~*´ c¶lÀ4M§ p*´ ¶2À4M§ b*´ o¶2À4M§ T*´ ¶2À4M§ F*´ ¶2À4M§ 8*´ ¶2À4M§ **´ ¶2À4M§ *´ ¶2À4M§ *´ ¶2À4M,°    ¦   d   °  ² Ô ¶ Ö · Ù » â ¼ å À î Á ñ Å ú Æ ý Ê Ë	 Ï Ð Ô Õ! Ù* Ú- Þ6 ß9 ãN äQ èU éX íc îf òq ót ÷ ø ü ý©¬·ºÅÈÓÖL O$Z%])Â*Å.&/)34478B9E=I>LBWCZGeHhLsMvQRVW[\ `«a®e²fµjÀkÃoápätuyz~!,/:=HKVYdgru¡¢¦® o& '    ) =  4    Mª         0   Ñ   Ö   â   î   ú        *  6  N  U  c  q        ©  ·  Å  Ó    L  Z  Â  &  4  B  I  W  e  s        «  ²  À  á        ,  :  H  V  d  r  M§¸»+Y·.M§¬»+Y·.M§ »+Y·.M§»+Y·.M§»+Y·.M§|»+Y·.M§p»+Y·.M§d»+Y·.M§X
*´ m¶rÀ4¸:M§@<M§9*´ ¶rÀ4M§+*´ g¶rÀ4M§*´ ¶rÀ4M§*´ ¶rÀ4M§*´ y¶rÀ4M§ó*´ s¶rÀ4M§å*´ ¶rÀ4M§×*´ ¶rÀ4M§É*´ w¶rÀ4M§»*´ ¶rÀ4Æ  *´ ¶rÀ4¶@ *´ ¶rÀ4§ 
*´ q¶rÀ4M§»BY*´ ¶rÀ4¸F·IK¶O*´ ¶rÀ4¶OQ¶O*´ k¶rÀ4¶O¶UM§B*´ i¶rÀ4M§4*´ ¶rÀ4¶Y£ #»BY*´ ¶rÀ4¸F·I[¶O¶U§ 6»BY*´ ¶rÀ4¶_¸F·IQ¶O*´ ¶rÀ4¶b¶O¶UM§Ì»BY*´ ¶rÀ4Ç 	d§ #»BY*´ ¶rÀ4¶g¸F·Ii¶O¶U¸F·I*´ ¶rÀ4Ç 	d§ 
*´ ¶rÀ4¶O¶UM§h*´ o¶rÀ4M§Z*´ ¶rÀ4M§L<M§E*´ ¶rÀ4M§7*´ g¶rÀ4M§)*´ ¶rÀ4M§*´ ¶rÀ4M§
*´ ¶rÀ4M§ ÿ*´ w¶rÀ4M§ ñ*´ y¶rÀ4M§ ãkM§ Ü*´ A¶lÀ4M§ Î»BY*´ [¶lÀ4¸F·In¶O¶UM§ ­»BY*´ [¶lÀ4¸F·In¶O¶UM§ *´ c¶lÀ4M§ ~*´ c¶lÀ4M§ p*´ ¶rÀ4M§ b*´ o¶rÀ4M§ T*´ ¶rÀ4M§ F*´ ¶rÀ4M§ 8*´ ¶rÀ4M§ **´ ¶rÀ4M§ *´ ¶rÀ4M§ *´ ¶rÀ4M,°    ¦   d  · ¹ Ô½ Ö¾ ÙÂ âÃ åÇ îÈ ñÌ úÍ ýÑÒ	Ö×ÛÜ!à*á-å6æ9êNëQïUðXôcõfùqútþÿ	
©¬·ºÅÈÓÖ!"&L'O+Z,]0Â1Å5&6):4;7?B@EDIELIWJZNeOhSsTvXY]^bc g«h®l²mµqÀrÃváwä{|!,/:=HKVYdg£r¤u¨©­µ s& '    ) =  4    Mª         0   Ñ   Ö   â   î   ú        *  6  N  U  c  q        ©  ·  Å  Ó    L  Z  Â  &  4  B  I  W  e  s        «  ²  À  á        ,  :  H  V  d  r  M§¸»+Y·.M§¬»+Y·.M§ »+Y·.M§»+Y·.M§»+Y·.M§|»+Y·.M§p»+Y·.M§d»+Y·.M§X
*´ m¶2À4¸:M§@<M§9*´ ¶2À4M§+*´ g¶2À4M§*´ ¶2À4M§*´ ¶2À4M§*´ y¶2À4M§ó*´ s¶2À4M§å*´ ¶2À4M§×*´ ¶2À4M§É*´ w¶2À4M§»*´ ¶2À4Æ  *´ ¶2À4¶@ *´ ¶2À4§ 
*´ q¶2À4M§»BY*´ ¶2À4¸F·IK¶O*´ ¶2À4¶OQ¶O*´ k¶2À4¶O¶UM§B*´ i¶2À4M§4*´ ¶2À4¶Y£ #»BY*´ ¶2À4¸F·I[¶O¶U§ 6»BY*´ ¶2À4¶_¸F·IQ¶O*´ ¶2À4¶b¶O¶UM§Ì»BY*´ ¶2À4Ç 	d§ #»BY*´ ¶2À4¶g¸F·Ii¶O¶U¸F·I*´ ¶2À4Ç 	d§ 
*´ ¶2À4¶O¶UM§h*´ o¶2À4M§Z*´ ¶2À4M§L<M§E*´ ¶2À4M§7*´ g¶2À4M§)*´ ¶2À4M§*´ ¶2À4M§
*´ ¶2À4M§ ÿ*´ w¶2À4M§ ñ*´ y¶2À4M§ ãkM§ Ü*´ A¶lÀ4M§ Î»BY*´ [¶lÀ4¸F·In¶O¶UM§ ­»BY*´ [¶lÀ4¸F·In¶O¶UM§ *´ c¶lÀ4M§ ~*´ c¶lÀ4M§ p*´ ¶2À4M§ b*´ o¶2À4M§ T*´ ¶2À4M§ F*´ ¶2À4M§ 8*´ ¶2À4M§ **´ ¶2À4M§ *´ ¶2À4M§ *´ ¶2À4M,°    ¦   d  ¾ À ÔÄ ÖÅ ÙÉ âÊ åÎ îÏ ñÓ úÔ ýØÙ	ÝÞâã!ç*è-ì6í9ñNòQöU÷Xûcüf qt
©¬·ºÅÈ#Ó$Ö()-L.O2Z3]7Â8Å<&=)A4B7FBGEKILLPWQZUeVhZs[v_`deij n«o®s²tµxÀyÃ}á~ä!,/:=HK V¡Y¥d¦gªr«u¯°´¼ t    t _1545788388223_198831t 2net.sf.jasperreports.engine.design.JRJavacCompiler
¬í sr (net.sf.jasperreports.engine.JasperReport      'Ø L compileDatat Ljava/io/Serializable;L compileNameSuffixt Ljava/lang/String;L 
compilerClassq ~ xr -net.sf.jasperreports.engine.base.JRBaseReport      'Ø ,I PSEUDO_SERIAL_VERSION_UIDI bottomMarginI columnCountI 
columnSpacingI columnWidthZ ignorePaginationZ isFloatColumnFooterZ isSummaryNewPageZ  isSummaryWithPageHeaderAndFooterZ isTitleNewPageI 
leftMarginB orientationI 
pageHeightI 	pageWidthB 
printOrderI rightMarginI 	topMarginB whenNoDataTypeL 
backgroundt $Lnet/sf/jasperreports/engine/JRBand;L columnDirectiont 3Lnet/sf/jasperreports/engine/type/RunDirectionEnum;L columnFooterq ~ L columnHeaderq ~ [ datasetst ([Lnet/sf/jasperreports/engine/JRDataset;L defaultFontt *Lnet/sf/jasperreports/engine/JRReportFont;L defaultStylet %Lnet/sf/jasperreports/engine/JRStyle;L detailq ~ L 
detailSectiont 'Lnet/sf/jasperreports/engine/JRSection;[ fontst +[Lnet/sf/jasperreports/engine/JRReportFont;L formatFactoryClassq ~ L 
importsSett Ljava/util/Set;L languageq ~ L lastPageFooterq ~ L mainDatasett 'Lnet/sf/jasperreports/engine/JRDataset;L nameq ~ L noDataq ~ L orientationValuet 2Lnet/sf/jasperreports/engine/type/OrientationEnum;L 
pageFooterq ~ L 
pageHeaderq ~ L printOrderValuet 1Lnet/sf/jasperreports/engine/type/PrintOrderEnum;[ stylest &[Lnet/sf/jasperreports/engine/JRStyle;L summaryq ~ [ 	templatest /[Lnet/sf/jasperreports/engine/JRReportTemplate;L titleq ~ L whenNoDataTypeValuet 5Lnet/sf/jasperreports/engine/type/WhenNoDataTypeEnum;xp  wñ                                    p~r 1net.sf.jasperreports.engine.type.RunDirectionEnum          xr java.lang.Enum          xpt LTRppppppsr .net.sf.jasperreports.engine.base.JRBaseSection      'Ø [ bandst %[Lnet/sf/jasperreports/engine/JRBand;xpur %[Lnet.sf.jasperreports.engine.JRBand;Ý~ìÊ5  xp   sr +net.sf.jasperreports.engine.base.JRBaseBand      'Ø I PSEUDO_SERIAL_VERSION_UIDI heightZ isSplitAllowedL printWhenExpressiont *Lnet/sf/jasperreports/engine/JRExpression;L 	splitTypet Ljava/lang/Byte;L splitTypeValuet 0Lnet/sf/jasperreports/engine/type/SplitTypeEnum;xr 3net.sf.jasperreports.engine.base.JRBaseElementGroup      'Ø L childrent Ljava/util/List;L elementGroupt ,Lnet/sf/jasperreports/engine/JRElementGroup;xpsr java.util.ArrayListxÒÇa I sizexp   aw   sr +net.sf.jasperreports.engine.base.JRBaseLine      'Ø I PSEUDO_SERIAL_VERSION_UIDB 	directionL directionValuet 4Lnet/sf/jasperreports/engine/type/LineDirectionEnum;xr 5net.sf.jasperreports.engine.base.JRBaseGraphicElement      'Ø I PSEUDO_SERIAL_VERSION_UIDL fillq ~ L 	fillValuet +Lnet/sf/jasperreports/engine/type/FillEnum;L linePent #Lnet/sf/jasperreports/engine/JRPen;L penq ~ xr .net.sf.jasperreports.engine.base.JRBaseElement      'Ø I PSEUDO_SERIAL_VERSION_UIDI heightZ isPrintInFirstWholeBandZ isPrintRepeatedValuesZ isPrintWhenDetailOverflowsZ isRemoveLineWhenBlankB positionTypeB stretchTypeI widthI xI yL 	backcolort Ljava/awt/Color;L defaultStyleProvidert 4Lnet/sf/jasperreports/engine/JRDefaultStyleProvider;L elementGroupq ~ "L 	forecolorq ~ ,L keyq ~ L modeq ~ L 	modeValuet +Lnet/sf/jasperreports/engine/type/ModeEnum;L parentStyleq ~ L parentStyleNameReferenceq ~ L positionTypeValuet 3Lnet/sf/jasperreports/engine/type/PositionTypeEnum;L printWhenExpressionq ~ L printWhenGroupChangest %Lnet/sf/jasperreports/engine/JRGroup;L 
propertiesMapt -Lnet/sf/jasperreports/engine/JRPropertiesMap;[ propertyExpressionst 3[Lnet/sf/jasperreports/engine/JRPropertyExpression;L stretchTypeValuet 2Lnet/sf/jasperreports/engine/type/StretchTypeEnum;xp  wñ                 pq ~ q ~ #pt line-20pppp~r 1net.sf.jasperreports.engine.type.PositionTypeEnum          xq ~ t FIX_RELATIVE_TO_TOPpppp~r 0net.sf.jasperreports.engine.type.StretchTypeEnum          xq ~ t 
NO_STRETCH  wîppsr *net.sf.jasperreports.engine.base.JRBasePen      'Ø I PSEUDO_SERIAL_VERSION_UIDL 	lineColorq ~ ,L 	lineStyleq ~ L lineStyleValuet 0Lnet/sf/jasperreports/engine/type/LineStyleEnum;L 	lineWidtht Ljava/lang/Float;L penContainert ,Lnet/sf/jasperreports/engine/JRPenContainer;xp  wñpp~r .net.sf.jasperreports.engine.type.LineStyleEnum          xq ~ t DASHEDsr java.lang.FloatÚíÉ¢Û<ðì F valuexr java.lang.Number¬à  xp?  q ~ 4p  wñ ~r 2net.sf.jasperreports.engine.type.LineDirectionEnum          xq ~ t TOP_DOWNsq ~ &  wñ          	      °pq ~ q ~ #pt line-1ppppq ~ 7ppppq ~ :  wîppsq ~ <  wñpp~q ~ At SOLIDsq ~ D?  q ~ Jp  wñ q ~ Hsq ~ &  wñ          	      Ápq ~ q ~ #pt line-4ppppq ~ 7ppppq ~ :  wîppsq ~ <  wñppq ~ Msq ~ D?  q ~ Pp  wñ q ~ Hsq ~ &  wñ          	      Òpq ~ q ~ #pt line-5ppppq ~ 7ppppq ~ :  wîppsq ~ <  wñppq ~ Msq ~ D?  q ~ Tp  wñ q ~ Hsq ~ &  wñ          	      ãpq ~ q ~ #pt line-6ppppq ~ 7ppppq ~ :  wîppsq ~ <  wñppq ~ Msq ~ D?  q ~ Xp  wñ q ~ Hsq ~ &  wñ          	      ôpq ~ q ~ #pt line-7ppppq ~ 7ppppq ~ :  wîppsq ~ <  wñppq ~ Msq ~ D?  q ~ \p  wñ q ~ Hsq ~ &  wñ             b   °pq ~ q ~ #pt line-8ppppq ~ 7ppppq ~ :  wîppsq ~ <  wñppq ~ Msq ~ D?  q ~ `p  wñ q ~ Hsq ~ &  wñ          	     Ipq ~ q ~ #pt line-9ppppq ~ 7ppppq ~ :  wîppsq ~ <  wñppq ~ Msq ~ D?  q ~ dp  wñ q ~ Hsq ~ &  wñ           ª  b  pq ~ q ~ #pt line-10ppppq ~ 7ppppq ~ :  wîppsq ~ <  wñppq ~ Msq ~ D?  q ~ hp  wñ q ~ Hsq ~ &  wñ           ª  b  pq ~ q ~ #pt line-11ppppq ~ 7ppppq ~ :  wîppsq ~ <  wñppq ~ Msq ~ D?  q ~ lp  wñ q ~ Hsq ~ &  wñ           ª  b  'pq ~ q ~ #pt line-12ppppq ~ 7ppppq ~ :  wîppsq ~ <  wñppq ~ Msq ~ D?  q ~ pp  wñ q ~ Hsq ~ &  wñ           ª  b  8pq ~ q ~ #pt line-13ppppq ~ 7ppppq ~ :  wîppsq ~ <  wñppq ~ Msq ~ D?  q ~ tp  wñ q ~ Hsq ~ &  wñ   "           L   Òpq ~ q ~ #pt line-14ppppq ~ 7ppppq ~ :  wîppsq ~ <  wñppq ~ Msq ~ D?  q ~ xp  wñ q ~ Hsq ~ &  wñ   "              Òpq ~ q ~ #pt line-15ppppq ~ 7ppppq ~ :  wîppsq ~ <  wñppq ~ Msq ~ D?  q ~ |p  wñ q ~ Hsq ~ &  wñ              |   ãpq ~ q ~ #pt line-16ppppq ~ 7ppppq ~ :  wîppsq ~ <  wñppq ~ Msq ~ D?  q ~ p  wñ q ~ Hsq ~ &  wñ              í   Òpq ~ q ~ #pt line-17ppppq ~ 7ppppq ~ :  wîppsq ~ <  wñppq ~ Msq ~ D?  q ~ p  wñ q ~ Hsq ~ &  wñ              Ñ   Òpq ~ q ~ #pt line-18ppppq ~ 7ppppq ~ :  wîppsq ~ <  wñppq ~ Msq ~ D?  q ~ p  wñ q ~ Hsq ~ &  wñ          	     npq ~ q ~ #pt line-19ppppq ~ 7ppppq ~ :  wîppsq ~ <  wñppq ~ Msq ~ D?  q ~ p  wñ q ~ Hsr 1net.sf.jasperreports.engine.base.JRBaseStaticText      'Ø L textq ~ xr 2net.sf.jasperreports.engine.base.JRBaseTextElement      'Ø %I PSEUDO_SERIAL_VERSION_UIDL borderq ~ L borderColorq ~ ,L bottomBorderq ~ L bottomBorderColorq ~ ,L 
bottomPaddingt Ljava/lang/Integer;L fontNameq ~ L fontSizeq ~ L horizontalAlignmentq ~ L horizontalAlignmentValuet 6Lnet/sf/jasperreports/engine/type/HorizontalAlignEnum;L isBoldt Ljava/lang/Boolean;L isItalicq ~ L 
isPdfEmbeddedq ~ L isStrikeThroughq ~ L isStyledTextq ~ L isUnderlineq ~ L 
leftBorderq ~ L leftBorderColorq ~ ,L leftPaddingq ~ L lineBoxt 'Lnet/sf/jasperreports/engine/JRLineBox;L lineSpacingq ~ L lineSpacingValuet 2Lnet/sf/jasperreports/engine/type/LineSpacingEnum;L markupq ~ L paddingq ~ L pdfEncodingq ~ L pdfFontNameq ~ L 
reportFontq ~ L rightBorderq ~ L rightBorderColorq ~ ,L rightPaddingq ~ L rotationq ~ L 
rotationValuet /Lnet/sf/jasperreports/engine/type/RotationEnum;L 	topBorderq ~ L topBorderColorq ~ ,L 
topPaddingq ~ L verticalAlignmentq ~ L verticalAlignmentValuet 4Lnet/sf/jasperreports/engine/type/VerticalAlignEnum;xq ~ +  wñ           B      °pq ~ q ~ #pt staticText-1ppppq ~ 7ppppq ~ :  wñpppppt Arialsr java.lang.Integerâ ¤÷8 I valuexq ~ E   ppsr java.lang.BooleanÍ rÕúî Z valuexp ppppppppsr .net.sf.jasperreports.engine.base.JRBaseLineBox      'Ø L 
bottomPaddingq ~ L 	bottomPent +Lnet/sf/jasperreports/engine/base/JRBoxPen;L boxContainert ,Lnet/sf/jasperreports/engine/JRBoxContainer;L leftPaddingq ~ L leftPenq ~ ¡L paddingq ~ L penq ~ ¡L rightPaddingq ~ L rightPenq ~ ¡L 
topPaddingq ~ L topPenq ~ ¡xppsr 3net.sf.jasperreports.engine.base.JRBaseBoxBottomPen      'Ø  xr -net.sf.jasperreports.engine.base.JRBaseBoxPen      'Ø L lineBoxq ~ xq ~ <  wñsr java.awt.Color¥3u F falphaI valueL cst Ljava/awt/color/ColorSpace;[ 	frgbvaluet [F[ fvalueq ~ ©xp    ÿ   ppppq ~ Msq ~ D    q ~ £q ~ £q ~ psr 1net.sf.jasperreports.engine.base.JRBaseBoxLeftPen      'Ø  xq ~ ¥  wñsq ~ §    ÿ   ppppq ~ Msq ~ D    q ~ £q ~ £psq ~ ¥  wñppppq ~ £q ~ £psr 2net.sf.jasperreports.engine.base.JRBaseBoxRightPen      'Ø  xq ~ ¥  wñsq ~ §    ÿ   ppppq ~ Msq ~ D    q ~ £q ~ £psr 0net.sf.jasperreports.engine.base.JRBaseBoxTopPen      'Ø  xq ~ ¥  wñsq ~ §    ÿ   ppppq ~ Msq ~ D    q ~ £q ~ £pppppt 	Helveticapppppppppp~r 2net.sf.jasperreports.engine.type.VerticalAlignEnum          xq ~ t MIDDLEt Local de pagamentosq ~   wñ          `      Ápq ~ q ~ #pt staticText-2ppppq ~ 7ppppq ~ :  wñpppppt Arialq ~ ppq ~ ppppppppsq ~  psq ~ ¤  wñsq ~ §    ÿ   ppppq ~ Msq ~ D    q ~ Áq ~ Áq ~ ¾psq ~ ¬  wñsq ~ §    ÿ   ppppq ~ Msq ~ D    q ~ Áq ~ Ápsq ~ ¥  wñppppq ~ Áq ~ Ápsq ~ ±  wñsq ~ §    ÿ   ppppq ~ Msq ~ D    q ~ Áq ~ Ápsq ~ µ  wñsq ~ §    ÿ   ppppq ~ Msq ~ D    q ~ Áq ~ Ápppppt 	Helveticappppppppppq ~ »t "Nome do BeneficiÃ¡rio / CPF / CNPJsq ~   wñ           B      Òpq ~ q ~ #pt staticText-3ppppq ~ 7ppppq ~ :  wñpppppt Arialq ~ ppq ~ ppppppppsq ~  psq ~ ¤  wñsq ~ §    ÿ   ppppq ~ Msq ~ D    q ~ Ôq ~ Ôq ~ Ñpsq ~ ¬  wñsq ~ §    ÿ   ppppq ~ Msq ~ D    q ~ Ôq ~ Ôpsq ~ ¥  wñppppq ~ Ôq ~ Ôpsq ~ ±  wñsq ~ §    ÿ   ppppq ~ Msq ~ D    q ~ Ôq ~ Ôpsq ~ µ  wñsq ~ §    ÿ   ppppq ~ Msq ~ D    q ~ Ôq ~ Ôpppppt 	Helveticappppppppppq ~ »t Data do Documentosq ~   wñ           K      ãpq ~ q ~ #pt staticText-4ppppq ~ 7ppppq ~ :  wñpppppt Arialq ~ ppq ~ ppppppppsq ~  psq ~ ¤  wñsq ~ §    ÿ   ppppq ~ Msq ~ D    q ~ çq ~ çq ~ äpsq ~ ¬  wñsq ~ §    ÿ   ppppq ~ Msq ~ D    q ~ çq ~ çpsq ~ ¥  wñppppq ~ çq ~ çpsq ~ ±  wñsq ~ §    ÿ   ppppq ~ Msq ~ D    q ~ çq ~ çpsq ~ µ  wñsq ~ §    ÿ   ppppq ~ Msq ~ D    q ~ çq ~ çpppppt 	Helveticappppppppppq ~ »t Uso do Bancosq ~   wñ           K      öpq ~ q ~ #pt staticText-5ppppq ~ 7ppppq ~ :  wñpppppt Arialq ~ ppq ~ ppppppppsq ~  psq ~ ¤  wñsq ~ §    ÿ   ppppq ~ Msq ~ D    q ~ úq ~ úq ~ ÷psq ~ ¬  wñsq ~ §    ÿ   ppppq ~ Msq ~ D    q ~ úq ~ úpsq ~ ¥  wñppppq ~ úq ~ úpsq ~ ±  wñsq ~ §    ÿ   ppppq ~ Msq ~ D    q ~ úq ~ úpsq ~ µ  wñsq ~ §    ÿ   ppppq ~ Msq ~ D    q ~ úq ~ úpppppt 	Helveticappppppppppq ~ »t InstruÃ§Ãµes :sq ~   wñ           B   O   Òpq ~ q ~ #pt staticText-6ppppq ~ 7ppppq ~ :  wñpppppt Arialq ~ ppq ~ ppppppppsq ~  psq ~ ¤  wñsq ~ §    ÿ   ppppq ~ Msq ~ D    q ~
q ~
q ~
psq ~ ¬  wñsq ~ §    ÿ   ppppq ~ Msq ~ D    q ~
q ~
psq ~ ¥  wñppppq ~
q ~
psq ~ ±  wñsq ~ §    ÿ   ppppq ~ Msq ~ D    q ~
q ~
psq ~ µ  wñsq ~ §    ÿ   ppppq ~ Msq ~ D    q ~
q ~
pppppt 	Helveticappppppppppq ~ »t NÂº do Documentosq ~   wñ           *      Òpq ~ q ~ #pt staticText-7ppppq ~ 7ppppq ~ :  wñpppppt Arialq ~ ppq ~ ppppppppsq ~  psq ~ ¤  wñsq ~ §    ÿ   ppppq ~ Msq ~ D    q ~ q ~ q ~psq ~ ¬  wñsq ~ §    ÿ   ppppq ~ Msq ~ D    q ~ q ~ psq ~ ¥  wñppppq ~ q ~ psq ~ ±  wñsq ~ §    ÿ   ppppq ~ Msq ~ D    q ~ q ~ psq ~ µ  wñsq ~ §    ÿ   ppppq ~ Msq ~ D    q ~ q ~ pppppt 	Helveticappppppppppq ~ »t 
EspÃ©cie Doc.sq ~   wñ              Ô   Òpq ~ q ~ #pt staticText-8ppppq ~ 7ppppq ~ :  wñpppppt Arialq ~ ppq ~ ppppppppsq ~  psq ~ ¤  wñsq ~ §    ÿ   ppppq ~ Msq ~ D    q ~3q ~3q ~0psq ~ ¬  wñsq ~ §    ÿ   ppppq ~ Msq ~ D    q ~3q ~3psq ~ ¥  wñppppq ~3q ~3psq ~ ±  wñsq ~ §    ÿ   ppppq ~ Msq ~ D    q ~3q ~3psq ~ µ  wñsq ~ §    ÿ   ppppq ~ Msq ~ D    q ~3q ~3pppppt 	Helveticappppppppppq ~ »t Aceitesq ~   wñ           X   ð   Òpq ~ q ~ #pt staticText-9ppppq ~ 7ppppq ~ :  wñpppppt Arialq ~ ppq ~ ppppppppsq ~  psq ~ ¤  wñsq ~ §    ÿ   ppppq ~ Msq ~ D    q ~Fq ~Fq ~Cpsq ~ ¬  wñsq ~ §    ÿ   ppppq ~ Msq ~ D    q ~Fq ~Fpsq ~ ¥  wñppppq ~Fq ~Fpsq ~ ±  wñsq ~ §    ÿ   ppppq ~ Msq ~ D    q ~Fq ~Fpsq ~ µ  wñsq ~ §    ÿ   ppppq ~ Msq ~ D    q ~Fq ~Fpppppt 	Helveticappppppppppq ~ »t Data do Processamentosq ~   wñ           #   O   ãpq ~ q ~ #pt 
staticText-10ppppq ~ 7ppppq ~ :  wñpppppt Arialq ~ ppq ~ ppppppppsq ~  psq ~ ¤  wñsq ~ §    ÿ   ppppq ~ Msq ~ D    q ~Yq ~Yq ~Vpsq ~ ¬  wñsq ~ §    ÿ   ppppq ~ Msq ~ D    q ~Yq ~Ypsq ~ ¥  wñppppq ~Yq ~Ypsq ~ ±  wñsq ~ §    ÿ   ppppq ~ Msq ~ D    q ~Yq ~Ypsq ~ µ  wñsq ~ §    ÿ   ppppq ~ Msq ~ D    q ~Yq ~Ypppppt 	Helveticappppppppppq ~ »t Carteirasq ~   wñ              ~   ãpq ~ q ~ #pt 
staticText-11ppppq ~ 7ppppq ~ :  wñpppppt Arialq ~ ppq ~ ppppppppsq ~  psq ~ ¤  wñsq ~ §    ÿ   ppppq ~ Msq ~ D    q ~lq ~lq ~ipsq ~ ¬  wñsq ~ §    ÿ   ppppq ~ Msq ~ D    q ~lq ~lpsq ~ ¥  wñppppq ~lq ~lpsq ~ ±  wñsq ~ §    ÿ   ppppq ~ Msq ~ D    q ~lq ~lpsq ~ µ  wñsq ~ §    ÿ   ppppq ~ Msq ~ D    q ~lq ~lpppppt 	Helveticappppppppppq ~ »t EspÃ©ciesq ~   wñ           B      ãpq ~ q ~ #pt 
staticText-12ppppq ~ 7ppppq ~ :  wñpppppt Arialq ~ ppq ~ ppppppppsq ~  psq ~ ¤  wñsq ~ §    ÿ   ppppq ~ Msq ~ D    q ~q ~q ~|psq ~ ¬  wñsq ~ §    ÿ   ppppq ~ Msq ~ D    q ~q ~psq ~ ¥  wñppppq ~q ~psq ~ ±  wñsq ~ §    ÿ   ppppq ~ Msq ~ D    q ~q ~psq ~ µ  wñsq ~ §    ÿ   ppppq ~ Msq ~ D    q ~q ~pppppt 	Helveticappppppppppq ~ »t 
Quantidadesq ~   wñ           B   ð   ãpq ~ q ~ #pt 
staticText-13ppppq ~ 7ppppq ~ :  wñpppppt Arialq ~ ppq ~ ppppppppsq ~  psq ~ ¤  wñsq ~ §    ÿ   ppppq ~ Msq ~ D    q ~q ~q ~psq ~ ¬  wñsq ~ §    ÿ   ppppq ~ Msq ~ D    q ~q ~psq ~ ¥  wñppppq ~q ~psq ~ ±  wñsq ~ §    ÿ   ppppq ~ Msq ~ D    q ~q ~psq ~ µ  wñsq ~ §    ÿ   ppppq ~ Msq ~ D    q ~q ~pppppt 	Helveticappppppppppq ~ »t Valorsq ~   wñ           d  e   °pq ~ q ~ #pt 
staticText-14ppppq ~ 7ppppq ~ :  wñpppppt Arialq ~ ppq ~ ppppppppsq ~  psq ~ ¤  wñsq ~ §    ÿ   ppppq ~ Msq ~ D    q ~¥q ~¥q ~¢psq ~ ¬  wñsq ~ §    ÿ   ppppq ~ Msq ~ D    q ~¥q ~¥psq ~ ¥  wñppppq ~¥q ~¥psq ~ ±  wñsq ~ §    ÿ   ppppq ~ Msq ~ D    q ~¥q ~¥psq ~ µ  wñsq ~ §    ÿ   ppppq ~ Msq ~ D    q ~¥q ~¥pppppt 	Helveticappppppppppq ~ »t 
Vencimentosq ~   wñ           d  e   Ápq ~ q ~ #pt 
staticText-15ppppq ~ 7ppppq ~ :  wñpppppt Arialq ~ ppq ~ ppppppppsq ~  psq ~ ¤  wñsq ~ §    ÿ   ppppq ~ Msq ~ D    q ~¸q ~¸q ~µpsq ~ ¬  wñsq ~ §    ÿ   ppppq ~ Msq ~ D    q ~¸q ~¸psq ~ ¥  wñppppq ~¸q ~¸psq ~ ±  wñsq ~ §    ÿ   ppppq ~ Msq ~ D    q ~¸q ~¸psq ~ µ  wñsq ~ §    ÿ   ppppq ~ Msq ~ D    q ~¸q ~¸pppppt 	Helveticappppppppppq ~ »t #AgÃªncia / CÃ³digo do BeneficiÃ¡riosq ~   wñ           d  e  8pq ~ q ~ #pt 
staticText-16ppppq ~ 7ppppq ~ :  wñpppppt Arialq ~ ppq ~ ppppppppsq ~  psq ~ ¤  wñsq ~ §    ÿ   ppppq ~ Msq ~ D    q ~Ëq ~Ëq ~Èpsq ~ ¬  wñsq ~ §    ÿ   ppppq ~ Msq ~ D    q ~Ëq ~Ëpsq ~ ¥  wñppppq ~Ëq ~Ëpsq ~ ±  wñsq ~ §    ÿ   ppppq ~ Msq ~ D    q ~Ëq ~Ëpsq ~ µ  wñsq ~ §    ÿ   ppppq ~ Msq ~ D    q ~Ëq ~Ëpppppt 	Helveticappppppppppq ~ »t (=) Valor cobradosq ~   wñ           d  e  pq ~ q ~ #pt 
staticText-18ppppq ~ 7ppppq ~ :  wñpppppt Arialq ~ ppq ~ ppppppppsq ~  psq ~ ¤  wñsq ~ §    ÿ   ppppq ~ Msq ~ D    q ~Þq ~Þq ~Ûpsq ~ ¬  wñsq ~ §    ÿ   ppppq ~ Msq ~ D    q ~Þq ~Þpsq ~ ¥  wñppppq ~Þq ~Þpsq ~ ±  wñsq ~ §    ÿ   ppppq ~ Msq ~ D    q ~Þq ~Þpsq ~ µ  wñsq ~ §    ÿ   ppppq ~ Msq ~ D    q ~Þq ~Þpppppt 	Helveticappppppppppq ~ »t (+) Mora / Multasq ~   wñ           d  e   ôpq ~ q ~ #pt 
staticText-20ppppq ~ 7ppppq ~ :  wñpppppt Arialq ~ ppq ~ ppppppppsq ~  psq ~ ¤  wñsq ~ §    ÿ   ppppq ~ Msq ~ D    q ~ñq ~ñq ~îpsq ~ ¬  wñsq ~ §    ÿ   ppppq ~ Msq ~ D    q ~ñq ~ñpsq ~ ¥  wñppppq ~ñq ~ñpsq ~ ±  wñsq ~ §    ÿ   ppppq ~ Msq ~ D    q ~ñq ~ñpsq ~ µ  wñsq ~ §    ÿ   ppppq ~ Msq ~ D    q ~ñq ~ñpppppt 	Helveticappppppppppq ~ »t (-) Desconto / Abatimentosq ~   wñ           d  e   ãpq ~ q ~ #pt 
staticText-21ppppq ~ 7ppppq ~ :  wñpppppt Arialq ~ ppq ~ ppppppppsq ~  psq ~ ¤  wñsq ~ §    ÿ   ppppq ~ Msq ~ D    q ~q ~q ~psq ~ ¬  wñsq ~ §    ÿ   ppppq ~ Msq ~ D    q ~q ~psq ~ ¥  wñppppq ~q ~psq ~ ±  wñsq ~ §    ÿ   ppppq ~ Msq ~ D    q ~q ~psq ~ µ  wñsq ~ §    ÿ   ppppq ~ Msq ~ D    q ~q ~pppppt 	Helveticappppppppppq ~ »t (=) Valor do Documentosq ~   wñ           d  e   Òpq ~ q ~ #pt 
staticText-22ppppq ~ 7ppppq ~ :  wñpppppt Arialq ~ ppq ~ ppppppppsq ~  psq ~ ¤  wñsq ~ §    ÿ   ppppq ~ Msq ~ D    q ~q ~q ~psq ~ ¬  wñsq ~ §    ÿ   ppppq ~ Msq ~ D    q ~q ~psq ~ ¥  wñppppq ~q ~psq ~ ±  wñsq ~ §    ÿ   ppppq ~ Msq ~ D    q ~q ~psq ~ µ  wñsq ~ §    ÿ   ppppq ~ Msq ~ D    q ~q ~pppppt 	Helveticappppppppppq ~ »t 
Nosso NÃºmerosq ~   wñ   	       
     Jpq ~ q ~ #pt 
staticText-23ppppq ~ 7ppppq ~ :  wñpppppt Arialq ~ ppq ~ ppppppppsq ~  psq ~ ¤  wñsq ~ §    ÿ   ppppq ~ Msq ~ D    q ~*q ~*q ~'psq ~ ¬  wñsq ~ §    ÿ   ppppq ~ Msq ~ D    q ~*q ~*psq ~ ¥  wñppppq ~*q ~*psq ~ ±  wñsq ~ §    ÿ   ppppq ~ Msq ~ D    q ~*q ~*psq ~ µ  wñsq ~ §    ÿ   ppppq ~ Msq ~ D    q ~*q ~*pppppt 	Helveticappppppppppq ~ »t <Nome do pagador / CPF / CNPJ / EndereÃ§o / Cidade / UF / CEPsq ~   wñ   	        A     dpq ~ q ~ #pt 
staticText-24ppppq ~ 7ppppq ~ :  wñpppppt Arialq ~ ppq ~ ppppppppsq ~  psq ~ ¤  wñsq ~ §    ÿ   ppppq ~ Msq ~ D    q ~=q ~=q ~:psq ~ ¬  wñsq ~ §    ÿ   ppppq ~ Msq ~ D    q ~=q ~=psq ~ ¥  wñppppq ~=q ~=psq ~ ±  wñsq ~ §    ÿ   ppppq ~ Msq ~ D    q ~=q ~=psq ~ µ  wñsq ~ §    ÿ   ppppq ~ Msq ~ D    q ~=q ~=pppppt 	Helveticappppppppppq ~ »t Sacador / Avalista :sq ~   wñ           ½  L  ppq ~ q ~ #pt 
staticText-26ppppq ~ 7ppppq ~ :  wñpppppt Arialsq ~    p~r 4net.sf.jasperreports.engine.type.HorizontalAlignEnum          xq ~ t CENTERq ~ ppppppppsq ~  psq ~ ¤  wñsq ~ §    ÿ   ppppq ~ Msq ~ D    q ~Tq ~Tq ~Mpsq ~ ¬  wñsq ~ §    ÿ   ppppq ~ Msq ~ D    q ~Tq ~Tpsq ~ ¥  wñppppq ~Tq ~Tpsq ~ ±  wñsq ~ §    ÿ   ppppq ~ Msq ~ D    q ~Tq ~Tpsq ~ µ  wñsq ~ §    ÿ   ppppq ~ Msq ~ D    q ~Tq ~Tpppppt 	Helveticappppppppppq ~ »t FICHA DE COMPENSAÃÃOsr ,net.sf.jasperreports.engine.base.JRBaseImage      'Ø *I PSEUDO_SERIAL_VERSION_UIDI 
bookmarkLevelB evaluationTimeB hyperlinkTargetB 
hyperlinkTypeZ isLazyB onErrorTypeL anchorNameExpressionq ~ L borderq ~ L borderColorq ~ ,L bottomBorderq ~ L bottomBorderColorq ~ ,L 
bottomPaddingq ~ L evaluationGroupq ~ 0L evaluationTimeValuet 5Lnet/sf/jasperreports/engine/type/EvaluationTimeEnum;L 
expressionq ~ L horizontalAlignmentq ~ L horizontalAlignmentValueq ~ L hyperlinkAnchorExpressionq ~ L hyperlinkPageExpressionq ~ [ hyperlinkParameterst 3[Lnet/sf/jasperreports/engine/JRHyperlinkParameter;L hyperlinkReferenceExpressionq ~ L hyperlinkTooltipExpressionq ~ L isUsingCacheq ~ L 
leftBorderq ~ L leftBorderColorq ~ ,L leftPaddingq ~ L lineBoxq ~ L 
linkTargetq ~ L linkTypeq ~ L onErrorTypeValuet 2Lnet/sf/jasperreports/engine/type/OnErrorTypeEnum;L paddingq ~ L rightBorderq ~ L rightBorderColorq ~ ,L rightPaddingq ~ L 
scaleImageq ~ L scaleImageValuet 1Lnet/sf/jasperreports/engine/type/ScaleImageEnum;L 	topBorderq ~ L topBorderColorq ~ ,L 
topPaddingq ~ L verticalAlignmentq ~ L verticalAlignmentValueq ~ xq ~ (  wñ   #       ?     ssq ~ §    ÿÿÿÿpppq ~ q ~ #sq ~ §    ÿ   pppt 	barcode-1p~r )net.sf.jasperreports.engine.type.ModeEnum          xq ~ t OPAQUEppq ~ 7ppppq ~ :  wîp~r )net.sf.jasperreports.engine.type.FillEnum          xq ~ t SOLIDsq ~ <  wñppq ~ Msq ~ D    q ~ip  wñ         ppppppp~r 3net.sf.jasperreports.engine.type.EvaluationTimeEnum          xq ~ t NOWsr 1net.sf.jasperreports.engine.base.JRBaseExpression      'Ø I id[ chunkst 0[Lnet/sf/jasperreports/engine/JRExpressionChunk;L valueClassNameq ~ L valueClassRealNameq ~ xp   	ur 0[Lnet.sf.jasperreports.engine.JRExpressionChunk;mYÏÞiK£U  xp   sr 6net.sf.jasperreports.engine.base.JRBaseExpressionChunk      'Ø B typeL textq ~ xpt <it.businesslogic.ireport.barcode.BcImage.getBarcodeImage(13,sq ~}t banco.codigoBarrassq ~}t ,false,false,null,0,0)t java.awt.Imagepp~q ~Qt LEFTpppppppppsq ~  psq ~ ¤  wñsq ~ §    ÿ   ppppq ~ Msq ~ D    q ~q ~q ~ipsq ~ ¬  wñsq ~ §    ÿ   ppppq ~ Msq ~ D    q ~q ~psq ~ ¥  wñppppq ~q ~psq ~ ±  wñsq ~ §    ÿ   ppppq ~ Msq ~ D    q ~q ~psq ~ µ  wñsq ~ §    ÿ   ppppq ~ Msq ~ D    q ~q ~pp~r 0net.sf.jasperreports.engine.type.OnErrorTypeEnum          xq ~ t ERRORppppp~r /net.sf.jasperreports.engine.type.ScaleImageEnum          xq ~ t 
FILL_FRAMEpppp~q ~ ºt TOPsq ~ &  wñ                 pq ~ q ~ #pt line-22ppppq ~ 7ppppq ~ :  wîppsq ~ <  wñppq ~ Bsq ~ D?  q ~p  wñ q ~ Hsq ~   wñ           d  ¨   pq ~ q ~ #pt 
staticText-29ppppq ~ 7ppppq ~ :  wñppppppsq ~    	pq ~Rsq ~ ppppppppsq ~  psq ~ ¤  wñsq ~ §    ÿ   ppppq ~ Msq ~ D    q ~¥q ~¥q ~¡psq ~ ¬  wñsq ~ §    ÿ   ppppq ~ Msq ~ D    q ~¥q ~¥psq ~ ¥  wñppppq ~¥q ~¥psq ~ ±  wñsq ~ §    ÿ   ppppq ~ Msq ~ D    q ~¥q ~¥psq ~ µ  wñsq ~ §    ÿ   ppppq ~ Msq ~ D    q ~¥q ~¥pppppt Helvetica-Boldppppppppppq ~ »t Recibo do Pagadorsq ~ &  wñ          	     pq ~ q ~ #pt line-50ppppq ~ 7ppppq ~ :  wîppsq ~ <  wñppq ~ Bsq ~ D?  q ~µp  wñ q ~ Hsr 0net.sf.jasperreports.engine.base.JRBaseTextField      'Ø I PSEUDO_SERIAL_VERSION_UIDI 
bookmarkLevelB evaluationTimeB hyperlinkTargetB 
hyperlinkTypeZ isStretchWithOverflowL anchorNameExpressionq ~ L evaluationGroupq ~ 0L evaluationTimeValueq ~eL 
expressionq ~ L hyperlinkAnchorExpressionq ~ L hyperlinkPageExpressionq ~ [ hyperlinkParametersq ~fL hyperlinkReferenceExpressionq ~ L hyperlinkTooltipExpressionq ~ L isBlankWhenNullq ~ L 
linkTargetq ~ L linkTypeq ~ L patternq ~ xq ~   wñ   #       a   «   pq ~ q ~ #ppppppq ~ 7ppppq ~ :  wñppppppppq ~Rq ~¤ppppppppsq ~  psq ~ ¤  wñppppq ~»q ~»q ~ºpsq ~ ¬  wñppppq ~»q ~»psq ~ ¥  wñppppq ~»q ~»psq ~ ±  wñppppq ~»q ~»psq ~ µ  wñppppq ~»q ~»pppppt Helvetica-Boldppppppppppq ~ »  wñ        ppq ~vsq ~x   
uq ~{   sq ~}t banco.linhaDigitavelt java.lang.Stringppppppppppsq ~¹  wñ   	       \      Épq ~ q ~ #ppppppq ~ 7ppppq ~ :  wñpppppt Arialq ~Ppppppppppppsq ~  psq ~ ¤  wñppppq ~Éq ~Éq ~Çpsq ~ ¬  wñppppq ~Éq ~Épsq ~ ¥  wñppppq ~Éq ~Épsq ~ ±  wñppppq ~Éq ~Épsq ~ µ  wñppppq ~Éq ~Épppppppppppppppp~q ~ ºt BOTTOM  wñ        ppq ~vsq ~x   uq ~{   sq ~}t boleto.cedentesq ~}t  + " / " + sq ~}t cnpjEmpresasq ~}t  + " / " + sq ~}t enderecoEmpresat java.lang.Stringppppppppppsq ~¹  wñ   	        B      Úpq ~ q ~ #ppppppq ~ 7ppppq ~ :  wñpppppt Arialq ~Ppppppppppppsq ~  psq ~ ¤  wñppppq ~àq ~àq ~Þpsq ~ ¬  wñppppq ~àq ~àpsq ~ ¥  wñppppq ~àq ~àpsq ~ ±  wñppppq ~àq ~àpsq ~ µ  wñppppq ~àq ~àppppppppppppppppq ~Ï  wñ        ppq ~vsq ~x   uq ~{   sq ~}t boleto.dataDocumentot java.lang.Stringppppppq ~¤ppt  sq ~¹  wñ   	        3      Úpq ~ q ~ #ppppppq ~ 7ppppq ~ :  wñpppppt Arialq ~Ppq ~Rpppppppppsq ~  psq ~ ¤  wñppppq ~îq ~îq ~ìpsq ~ ¬  wñppppq ~îq ~îpsq ~ ¥  wñppppq ~îq ~îpsq ~ ±  wñppppq ~îq ~îpsq ~ µ  wñppppq ~îq ~îppppppppppppppppq ~Ï  wñ        ppq ~vsq ~x   
uq ~{   sq ~}t "DS"t java.lang.Stringppppppppppsq ~¹  wñ   	           Ó   Úpq ~ q ~ #ppppppq ~ 7ppppq ~ :  wñpppppt Arialq ~Ppq ~Rpppppppppsq ~  psq ~ ¤  wñppppq ~ûq ~ûq ~ùpsq ~ ¬  wñppppq ~ûq ~ûpsq ~ ¥  wñppppq ~ûq ~ûpsq ~ ±  wñppppq ~ûq ~ûpsq ~ µ  wñppppq ~ûq ~ûppppppppppppppppq ~Ï  wñ        ppq ~vsq ~x   uq ~{   sq ~}t "N"t java.lang.Stringppppppppppsq ~¹  wñ   	        (   O   ëpq ~ q ~ #ppppppq ~ 7ppppq ~ :  wñpppppt Arialq ~Ppq ~Rpppppppppsq ~  psq ~ ¤  wñppppq ~q ~q ~psq ~ ¬  wñppppq ~q ~psq ~ ¥  wñppppq ~q ~psq ~ ±  wñppppq ~q ~psq ~ µ  wñppppq ~q ~ppppppppppppppppq ~Ï  wñ        ppq ~vsq ~x   uq ~{   sq ~}t "COB"t java.lang.Stringppppppppppsq ~¹  wñ   	        k     ¸pq ~ q ~ #ppppppq ~ 7ppppq ~ :  wñpppppt Arialq ~Pp~q ~Qt RIGHTpppppppppsq ~  psq ~ ¤  wñppppq ~q ~q ~psq ~ ¬  wñppppq ~q ~psq ~ ¥  wñppppq ~q ~psq ~ ±  wñppppq ~q ~psq ~ µ  wñppppq ~q ~ppppppppppppppppq ~Ï  wñ        ppq ~vsq ~x   uq ~{   sq ~}t boleto.dataVencimentot java.lang.Stringppppppppppsq ~¹  wñ   	        k     ëpq ~ q ~ #ppppppq ~ 7ppppq ~ :  wñpppppt Arialq ~Ppq ~pppppppppsq ~  psq ~ ¤  wñppppq ~$q ~$q ~"psq ~ ¬  wñppppq ~$q ~$psq ~ ¥  wñppppq ~$q ~$psq ~ ±  wñppppq ~$q ~$psq ~ µ  wñppppq ~$q ~$ppppppppppppppppq ~Ï  wñ        ppq ~vsq ~x   uq ~{   sq ~}t boleto.valorBoletot java.lang.Stringppppppppppsq ~¹  wñ   	          k   Ùpq ~ q ~ #pt textField-49ppppq ~ 7ppppq ~ :  wñpppppt Arialq ~Ppq ~pppppppppsq ~  psq ~ ¤  wñsq ~ §    ÿ   ppppq ~ Msq ~ D    q ~2q ~2q ~/psq ~ ¬  wñsq ~ §    ÿ   ppppq ~ Msq ~ D    q ~2q ~2psq ~ ¥  wñppppq ~2q ~2psq ~ ±  wñsq ~ §    ÿ   ppppq ~ Msq ~ D    q ~2q ~2psq ~ µ  wñsq ~ §    ÿ   ppppq ~ Msq ~ D    q ~2q ~2ppppppppppppppppq ~Ï  wñ        ppq ~vsq ~x   uq ~{   sq ~}t boleto.nossoNumerot java.lang.Stringppppppq ~¤ppq ~ësq ~¹  wñ   F       T      þpq ~ q ~ #pt textField-24ppppq ~ 7ppppq ~ :  wñpppppt Arialq ~Ppq ~pppppppppsq ~  psq ~ ¤  wñsq ~ §    ÿ   ppppq ~ Msq ~ D    q ~Hq ~Hq ~Epsq ~ ¬  wñsq ~ §    ÿ   ppppq ~ Msq ~ D    q ~Hq ~Hpsq ~ ¥  wñppppq ~Hq ~Hpsq ~ ±  wñsq ~ §    ÿ   ppppq ~ Msq ~ D    q ~Hq ~Hpsq ~ µ  wñsq ~ §    ÿ   ppppq ~ Msq ~ D    q ~Hq ~Hppppppppppppppppq ~  wñ        ppq ~vsq ~x   uq ~{   sq ~}t boleto.instrucao1t java.lang.Stringppppppq ~¤pppsq ~¹  wñ   #        3   u   pq ~ q ~ #pt textField-35ppppq ~ 7ppppq ~ :  wñppppppsq ~    pq ~Rq ~¤ppppppppsq ~  psq ~ ¤  wñsq ~ §    ÿ   ppppq ~ Msq ~ D    q ~^q ~^q ~[psq ~ ¬  wñsq ~ §    ÿ   ppppq ~ Msq ~ D    q ~^q ~^psq ~ ¥  wñppppq ~^q ~^psq ~ ±  wñsq ~ §    ÿ   ppppq ~ Msq ~ D    q ~^q ~^psq ~ µ  wñsq ~ §    ÿ   ppppq ~ Msq ~ D    q ~^q ~^pppppt Helvetica-Boldppppppppppq ~ »  wñ        ppq ~vsq ~x   uq ~{   sq ~}t "070-1"t java.lang.Stringppppppq ~¤pppsq ~ &  wñ   #           ©   pq ~ q ~ #pt line-3ppppq ~ 7ppppq ~ :  wîppsq ~ <  wñppq ~ Msq ~ D?  q ~rp  wñ q ~ Hsq ~ &  wñ   #           s   pq ~ q ~ #pt line-2ppppq ~ 7ppppq ~ :  wîppsq ~ <  wñppq ~ Msq ~ D?  q ~vp  wñ q ~ Hsq ~¹  wñ          a   «   pq ~ q ~ #ppppppq ~ 7ppppq ~ :  wñppppppsq ~    
pq ~Rq ~¤ppppppppsq ~  psq ~ ¤  wñppppq ~|q ~|q ~zpsq ~ ¬  wñppppq ~|q ~|psq ~ ¥  wñppppq ~|q ~|psq ~ ±  wñppppq ~|q ~|psq ~ µ  wñppppq ~|q ~|pppppt Helvetica-Boldppppppppppp  wñ        ppq ~vsq ~x   uq ~{   sq ~}t banco.linhaDigitavelt java.lang.Stringppppppppppsq ~ &  wñ          	      *pq ~ q ~ #pt line-1ppppq ~ 7ppppq ~ :  wîppsq ~ <  wñppq ~ Msq ~ D?  q ~p  wñ q ~ Hsq ~   wñ           B      +pq ~ q ~ #pt staticText-1ppppq ~ 7ppppq ~ :  wñpppppt Arialq ~ ppq ~ ppppppppsq ~  psq ~ ¤  wñsq ~ §    ÿ   ppppq ~ Msq ~ D    q ~q ~q ~psq ~ ¬  wñsq ~ §    ÿ   ppppq ~ Msq ~ D    q ~q ~psq ~ ¥  wñppppq ~q ~psq ~ ±  wñsq ~ §    ÿ   ppppq ~ Msq ~ D    q ~q ~psq ~ µ  wñsq ~ §    ÿ   ppppq ~ Msq ~ D    q ~q ~pppppt 	Helveticappppppppppq ~ »t <Nome do pagador / CPF / CNPJ / EndereÃ§o / Cidade / UF / CEPsq ~ &  wñ          	      Npq ~ q ~ #pt line-1ppppq ~ 7ppppq ~ :  wîppsq ~ <  wñppq ~ Msq ~ D?  q ~p  wñ q ~ Hsq ~ &  wñ          	      `pq ~ q ~ #pt line-1ppppq ~ 7ppppq ~ :  wîppsq ~ <  wñppq ~ Msq ~ D?  q ~£p  wñ q ~ Hsq ~ &  wñ          	      rpq ~ q ~ #pt line-1ppppq ~ 7ppppq ~ :  wîppsq ~ <  wñppq ~ Msq ~ D?  q ~§p  wñ q ~ Hsq ~   wñ           Ù  1   spq ~ q ~ #pt staticText-1ppppq ~ 7ppppq ~ :  wñpppppt Arialq ~ pq ~q ~ ppppppppsq ~  psq ~ ¤  wñsq ~ §    ÿ   ppppq ~ Msq ~ D    q ~®q ~®q ~«psq ~ ¬  wñsq ~ §    ÿ   ppppq ~ Msq ~ D    q ~®q ~®psq ~ ¥  wñppppq ~®q ~®psq ~ ±  wñsq ~ §    ÿ   ppppq ~ Msq ~ D    q ~®q ~®psq ~ µ  wñsq ~ §    ÿ   ppppq ~ Msq ~ D    q ~®q ~®pppppt 	Helveticappppppppppq ~ »t AutenticaÃ§Ã£o MecÃ¢nicasq ~¹  wñ   	           }   ëpq ~ q ~ #ppppppq ~ 7ppppq ~ :  wñpppppt Arialq ~Ppq ~Rpppppppppsq ~  psq ~ ¤  wñppppq ~Àq ~Àq ~¾psq ~ ¬  wñppppq ~Àq ~Àpsq ~ ¥  wñppppq ~Àq ~Àpsq ~ ±  wñppppq ~Àq ~Àpsq ~ µ  wñppppq ~Àq ~Àppppppppppppppppq ~Ï  wñ        ppq ~vsq ~x   uq ~{   sq ~}t "R$"t java.lang.Stringppppppppppsq ~d  wñ   #        l      pq ~ q ~ #ppppppq ~ 7ppppq ~ :  wîppsq ~ <  wñppppq ~Ëp  wñ         pppppppq ~vsq ~x   uq ~{   sq ~}t 
SUBREPORT_DIRsq ~}t   + "logos/logo-brb.png"t java.lang.Stringppppppppppppsq ~  psq ~ ¤  wñppppq ~Ôq ~Ôq ~Ëpsq ~ ¬  wñppppq ~Ôq ~Ôpsq ~ ¥  wñppppq ~Ôq ~Ôpsq ~ ±  wñppppq ~Ôq ~Ôpsq ~ µ  wñppppq ~Ôq ~Ôppq ~ppppp~q ~t 	REAL_SIZEpppppsq ~d  wñ   #        l      pq ~ q ~ #ppppppq ~ 7ppppq ~ :  wîppsq ~ <  wñppppq ~Üp  wñ         pppppppq ~vsq ~x   uq ~{   sq ~}t 
SUBREPORT_DIRsq ~}t  + "logos/logo-brb.png"t java.lang.Stringppppppppppppsq ~  psq ~ ¤  wñppppq ~åq ~åq ~Üpsq ~ ¬  wñppppq ~åq ~åpsq ~ ¥  wñppppq ~åq ~åpsq ~ ±  wñppppq ~åq ~åpsq ~ µ  wñppppq ~åq ~åppq ~pppppq ~Úpppppsq ~   wñ           s      spq ~ q ~ #pt staticText-1ppppq ~ 7ppppq ~ :  wñpppppt Arialq ~ ppq ~ ppppppppsq ~  psq ~ ¤  wñsq ~ §    ÿ   ppppq ~ Msq ~ D    q ~îq ~îq ~ëpsq ~ ¬  wñsq ~ §    ÿ   ppppq ~ Msq ~ D    q ~îq ~îpsq ~ ¥  wñppppq ~îq ~îpsq ~ ±  wñsq ~ §    ÿ   ppppq ~ Msq ~ D    q ~îq ~îpsq ~ µ  wñsq ~ §    ÿ   ppppq ~ Msq ~ D    q ~îq ~îpppppt 	Helveticappppppppppq ~ »t #AgÃªncia / CÃ³digo do BeneficiÃ¡riosq ~   wñ           s      apq ~ q ~ #pt staticText-1ppppq ~ 7ppppq ~ :  wñpppppt Arialq ~ ppq ~ ppppppppsq ~  psq ~ ¤  wñsq ~ §    ÿ   ppppq ~ Msq ~ D    q ~q ~q ~þpsq ~ ¬  wñsq ~ §    ÿ   ppppq ~ Msq ~ D    q ~q ~psq ~ ¥  wñppppq ~q ~psq ~ ±  wñsq ~ §    ÿ   ppppq ~ Msq ~ D    q ~q ~psq ~ µ  wñsq ~ §    ÿ   ppppq ~ Msq ~ D    q ~q ~pppppt 	Helveticappppppppppq ~ »t &BeneficiÃ¡rio / CPF / CNPJ / EndereÃ§osq ~   wñ           f      Opq ~ q ~ #pt staticText-1ppppq ~ 7ppppq ~ :  wñpppppt Arialq ~ ppq ~ ppppppppsq ~  psq ~ ¤  wñsq ~ §    ÿ   ppppq ~ Msq ~ D    q ~q ~q ~psq ~ ¬  wñsq ~ §    ÿ   ppppq ~ Msq ~ D    q ~q ~psq ~ ¥  wñppppq ~q ~psq ~ ±  wñsq ~ §    ÿ   ppppq ~ Msq ~ D    q ~q ~psq ~ µ  wñsq ~ §    ÿ   ppppq ~ Msq ~ D    q ~q ~pppppt 	Helveticappppppppppq ~ »t 
Nosso NÃºmerosq ~ &  wñ              h   Opq ~ q ~ #ppppppq ~ 7ppppq ~ :  wîppsq ~ <  wñppppq ~$p  wñ q ~ Hsq ~   wñ           e   j   Opq ~ q ~ #pt staticText-1ppppq ~ 7ppppq ~ :  wñpppppt Arialq ~ ppq ~ ppppppppsq ~  psq ~ ¤  wñsq ~ §    ÿ   ppppq ~ Msq ~ D    q ~)q ~)q ~&psq ~ ¬  wñsq ~ §    ÿ   ppppq ~ Msq ~ D    q ~)q ~)psq ~ ¥  wñppppq ~)q ~)psq ~ ±  wñsq ~ §    ÿ   ppppq ~ Msq ~ D    q ~)q ~)psq ~ µ  wñsq ~ §    ÿ   ppppq ~ Msq ~ D    q ~)q ~)pppppt 	Helveticappppppppppq ~ »t NÃºmero do documentosq ~ &  wñ              Ð   Opq ~ q ~ #ppppppq ~ 7ppppq ~ :  wîppsq ~ <  wñppppq ~9p  wñ q ~ Hsq ~   wñ           f   Ò   Opq ~ q ~ #pt staticText-1ppppq ~ 7ppppq ~ :  wñpppppt Arialq ~ ppq ~ ppppppppsq ~  psq ~ ¤  wñsq ~ §    ÿ   ppppq ~ Msq ~ D    q ~>q ~>q ~;psq ~ ¬  wñsq ~ §    ÿ   ppppq ~ Msq ~ D    q ~>q ~>psq ~ ¥  wñppppq ~>q ~>psq ~ ±  wñsq ~ §    ÿ   ppppq ~ Msq ~ D    q ~>q ~>psq ~ µ  wñsq ~ §    ÿ   ppppq ~ Msq ~ D    q ~>q ~>pppppt 	Helveticappppppppppq ~ »t 
Vencimentosq ~ &  wñ             9   Opq ~ q ~ #ppppppq ~ 7ppppq ~ :  wîppsq ~ <  wñppppq ~Np  wñ q ~ Hsq ~   wñ           f  ;   Opq ~ q ~ #pt staticText-1ppppq ~ 7ppppq ~ :  wñpppppt Arialq ~ ppq ~ ppppppppsq ~  psq ~ ¤  wñsq ~ §    ÿ   ppppq ~ Msq ~ D    q ~Sq ~Sq ~Ppsq ~ ¬  wñsq ~ §    ÿ   ppppq ~ Msq ~ D    q ~Sq ~Spsq ~ ¥  wñppppq ~Sq ~Spsq ~ ±  wñsq ~ §    ÿ   ppppq ~ Msq ~ D    q ~Sq ~Spsq ~ µ  wñsq ~ §    ÿ   ppppq ~ Msq ~ D    q ~Sq ~Spppppt 	Helveticappppppppppq ~ »t (=) Valor do documentosq ~ &  wñ             ¢   Opq ~ q ~ #ppppppq ~ 7ppppq ~ :  wîppsq ~ <  wñppppq ~cp  wñ q ~ Hsq ~   wñ           f  ¤   Opq ~ q ~ #pt staticText-1ppppq ~ 7ppppq ~ :  wñpppppt Arialq ~ ppq ~ ppppppppsq ~  psq ~ ¤  wñsq ~ §    ÿ   ppppq ~ Msq ~ D    q ~hq ~hq ~epsq ~ ¬  wñsq ~ §    ÿ   ppppq ~ Msq ~ D    q ~hq ~hpsq ~ ¥  wñppppq ~hq ~hpsq ~ ±  wñsq ~ §    ÿ   ppppq ~ Msq ~ D    q ~hq ~hpsq ~ µ  wñsq ~ §    ÿ   ppppq ~ Msq ~ D    q ~hq ~hpppppt 	Helveticappppppppppq ~ »t (=) Valor Cobradosq ~¹  wñ   #        3   u   pq ~ q ~ #pt textField-35ppppq ~ 7ppppq ~ :  wñppppppq ~]pq ~Rq ~¤ppppppppsq ~  psq ~ ¤  wñsq ~ §    ÿ   ppppq ~ Msq ~ D    q ~zq ~zq ~xpsq ~ ¬  wñsq ~ §    ÿ   ppppq ~ Msq ~ D    q ~zq ~zpsq ~ ¥  wñppppq ~zq ~zpsq ~ ±  wñsq ~ §    ÿ   ppppq ~ Msq ~ D    q ~zq ~zpsq ~ µ  wñsq ~ §    ÿ   ppppq ~ Msq ~ D    q ~zq ~zpppppt Helvetica-Boldppppppppppq ~ »  wñ        ppq ~vsq ~x   uq ~{   sq ~}t "070-1"t java.lang.Stringppppppq ~¤pppsq ~ &  wñ   #           s   pq ~ q ~ #pt line-3ppppq ~ 7ppppq ~ :  wîppsq ~ <  wñppq ~ Msq ~ D?  q ~p  wñ q ~ Hsq ~ &  wñ   #           ©   pq ~ q ~ #pt line-3ppppq ~ 7ppppq ~ :  wîppsq ~ <  wñppq ~ Msq ~ D?  q ~p  wñ q ~ Hsq ~   wñ   	       `      ¸pq ~ q ~ #ppppppq ~ 7ppppq ~ :  wñpppppt Arialq ~Pppq ~¤ppppppppsq ~  psq ~ ¤  wñppppq ~q ~q ~psq ~ ¬  wñppppq ~q ~psq ~ ¥  wñppppq ~q ~psq ~ ±  wñppppq ~q ~psq ~ µ  wñppppq ~q ~pppppppppppppppppt (PAGAR PREFERENCIALMENTE BRB CONVENIENCIAsq ~   wñ           ½  L  pq ~ q ~ #pt 
staticText-26ppppq ~ 7ppppq ~ :  wñpppppt Arialq ~ pq ~Rq ~ ppppppppsq ~  psq ~ ¤  wñsq ~ §    ÿ   ppppq ~ Msq ~ D    q ~¢q ~¢q ~psq ~ ¬  wñsq ~ §    ÿ   ppppq ~ Msq ~ D    q ~¢q ~¢psq ~ ¥  wñppppq ~¢q ~¢psq ~ ±  wñsq ~ §    ÿ   ppppq ~ Msq ~ D    q ~¢q ~¢psq ~ µ  wñsq ~ §    ÿ   ppppq ~ Msq ~ D    q ~¢q ~¢pppppt 	Helveticappppppppppq ~ »t !AutenticaÃ§Ã£o MecÃ¢nica no versosq ~   wñ   	        A      Epq ~ q ~ #pt 
staticText-24ppppq ~ 7ppppq ~ :  wñpppppt Arialq ~ ppq ~ ppppppppsq ~  psq ~ ¤  wñsq ~ §    ÿ   ppppq ~ Msq ~ D    q ~µq ~µq ~²psq ~ ¬  wñsq ~ §    ÿ   ppppq ~ Msq ~ D    q ~µq ~µpsq ~ ¥  wñppppq ~µq ~µpsq ~ ±  wñsq ~ §    ÿ   ppppq ~ Msq ~ D    q ~µq ~µpsq ~ µ  wñsq ~ §    ÿ   ppppq ~ Msq ~ D    q ~µq ~µpppppt 	Helveticappppppppppq ~ »t Sacador / Avalista :sq ~¹  wñ   	        _  B   Wpq ~ q ~ #ppppppq ~ 7ppppq ~ :  wñpppppt Arialq ~Ppq ~pppppppppsq ~  psq ~ ¤  wñppppq ~Çq ~Çq ~Åpsq ~ ¬  wñppppq ~Çq ~Çpsq ~ ¥  wñppppq ~Çq ~Çpsq ~ ±  wñppppq ~Çq ~Çpsq ~ µ  wñppppq ~Çq ~Çppppppppppppppppq ~Ï  wñ        ppq ~vsq ~x   uq ~{   sq ~}t boleto.valorBoletot java.lang.Stringppppppppppsq ~¹  wñ   	        a   Ô   Wpq ~ q ~ #ppppppq ~ 7ppppq ~ :  wñpppppt Arialq ~Ppq ~pppppppppsq ~  psq ~ ¤  wñppppq ~Ôq ~Ôq ~Òpsq ~ ¬  wñppppq ~Ôq ~Ôpsq ~ ¥  wñppppq ~Ôq ~Ôpsq ~ ±  wñppppq ~Ôq ~Ôpsq ~ µ  wñppppq ~Ôq ~Ôppppppppppppppppq ~Ï  wñ        ppq ~vsq ~x   uq ~{   sq ~}t boleto.dataVencimentot java.lang.Stringppppppppppsq ~   wñ   	           ë   ëpq ~ q ~ #ppppppq ~ 7ppppq ~ :  wñpppppt Arialq ~Ppq ~Rpppppppppsq ~  psq ~ ¤  wñppppq ~áq ~áq ~ßpsq ~ ¬  wñppppq ~áq ~ápsq ~ ¥  wñppppq ~áq ~ápsq ~ ±  wñppppq ~áq ~ápsq ~ µ  wñppppq ~áq ~áppppppppppppppppq ~ »t xsq ~¹  wñ   	        a      Wpq ~ q ~ #pt textField-49ppppq ~ 7ppppq ~ :  wñpppppt Arialq ~Ppq ~pppppppppsq ~  psq ~ ¤  wñsq ~ §    ÿ   ppppq ~ Msq ~ D    q ~ëq ~ëq ~èpsq ~ ¬  wñsq ~ §    ÿ   ppppq ~ Msq ~ D    q ~ëq ~ëpsq ~ ¥  wñppppq ~ëq ~ëpsq ~ ±  wñsq ~ §    ÿ   ppppq ~ Msq ~ D    q ~ëq ~ëpsq ~ µ  wñsq ~ §    ÿ   ppppq ~ Msq ~ D    q ~ëq ~ëppppppppppppppppq ~Ï  wñ        ppq ~vsq ~x   uq ~{   sq ~}t boleto.nossoNumerot java.lang.Stringppppppq ~¤ppq ~ësq ~¹  wñ               Spq ~ q ~ #ppppppq ~ 7ppppq ~ :  wñpppppt Arialq ~Ppppppppppppsq ~  psq ~ ¤  wñppppq ~ q ~ q ~þpsq ~ ¬  wñppppq ~ q ~ psq ~ ¥  wñppppq ~ q ~ psq ~ ±  wñppppq ~ q ~ psq ~ µ  wñppppq ~ q ~ ppppppppppppppppp  wñ        ppq ~vsq ~x   uq ~{   sq ~}t boleto.nomeSacadosq ~}t 
+ (sq ~}t boleto.cpfSacadosq ~}t .length() == 0 ? "" : " / " + sq ~}t boleto.cpfSacadosq ~}t )
+ (sq ~}t boleto.enderecoSacadosq ~}t .length() == 0 ? "" : " / " + sq ~}t boleto.enderecoSacadosq ~}t )
+ (sq ~}t boleto.cidadeSacadosq ~}t .length() == 0 ? "" : " / " + sq ~}t boleto.cidadeSacadosq ~}t )
+ (sq ~}t boleto.ufSacadosq ~}t .length() == 0 ? "" : " - " + sq ~}t boleto.ufSacadosq ~}t )
+ (sq ~}t boleto.cepSacadosq ~}t .length() == 0 ? "" : " - " + sq ~}t boleto.cepSacadosq ~}t )t java.lang.Stringppppppppppsq ~¹  wñ                3pq ~ q ~ #ppppppq ~ 7ppppq ~ :  wñpppppt Arialq ~Ppppppppppppsq ~  psq ~ ¤  wñppppq ~7q ~7q ~5psq ~ ¬  wñppppq ~7q ~7psq ~ ¥  wñppppq ~7q ~7psq ~ ±  wñppppq ~7q ~7psq ~ µ  wñppppq ~7q ~7ppppppppppppppppp  wñ        ppq ~vsq ~x   uq ~{   sq ~}t boleto.nomeSacadosq ~}t 
+ (sq ~}t boleto.cpfSacadosq ~}t .length() == 0 ? "" : " / " + sq ~}t boleto.cpfSacadosq ~}t )
+ (sq ~}t boleto.enderecoSacadosq ~}t .length() == 0 ? "" : " / " + sq ~}t boleto.enderecoSacadosq ~}t )
+ (sq ~}t boleto.cidadeSacadosq ~}t .length() == 0 ? "" : " / " + sq ~}t boleto.cidadeSacadosq ~}t )
+ (sq ~}t boleto.ufSacadosq ~}t .length() == 0 ? "" : " - " + sq ~}t boleto.ufSacadosq ~}t )
+ (sq ~}t boleto.cepSacadosq ~}t .length() == 0 ? "" : " - " + sq ~}t boleto.cepSacadosq ~}t )t java.lang.Stringppppppppppsq ~¹  wñ   	          k   Êpq ~ q ~ #ppppppq ~ 7ppppq ~ :  wñpppppt Arialq ~Ppq ~pppppppppsq ~  psq ~ ¤  wñppppq ~nq ~nq ~lpsq ~ ¬  wñppppq ~nq ~npsq ~ ¥  wñppppq ~nq ~npsq ~ ±  wñppppq ~nq ~npsq ~ µ  wñppppq ~nq ~nppppppppppppppppq ~Ï  wñ        ppq ~vsq ~x   uq ~{   sq ~}t  banco.agenciaCodCedenteFormattedt java.lang.Stringppppppppppsq ~¹  wñ   	              {pq ~ q ~ #ppppppq ~ 7ppppq ~ :  wñpppppt Arialq ~Ppq ~Rpppppppppsq ~  psq ~ ¤  wñppppq ~{q ~{q ~ypsq ~ ¬  wñppppq ~{q ~{psq ~ ¥  wñppppq ~{q ~{psq ~ ±  wñppppq ~{q ~{psq ~ µ  wñppppq ~{q ~{ppppppppppppppppq ~Ï  wñ        ppq ~vsq ~x    uq ~{   sq ~}t  banco.agenciaCodCedenteFormattedt java.lang.Stringppppppppppsq ~¹  wñ   	             ipq ~ q ~ #ppppppq ~ 7ppppq ~ :  wñpppppt Arialq ~Ppppppppppppsq ~  psq ~ ¤  wñppppq ~q ~q ~psq ~ ¬  wñppppq ~q ~psq ~ ¥  wñppppq ~q ~psq ~ ±  wñppppq ~q ~psq ~ µ  wñppppq ~q ~ppppppppppppppppq ~Ï  wñ        ppq ~vsq ~x   !uq ~{   sq ~}t boleto.cedentesq ~}t  + " / " + sq ~}t cnpjEmpresasq ~}t  + " / " + sq ~}t enderecoEmpresat java.lang.Stringppppppppppsq ~ &  wñ              î   rpq ~ q ~ #ppppppq ~ 7ppppq ~ :  wîppsq ~ <  wñppppq ~p  wñ q ~ Hsq ~¹  wñ   	        a   n   Wpq ~ q ~ #pt textField-49ppppq ~ 7ppppq ~ :  wñpppppt Arialq ~Ppq ~pppppppppsq ~  psq ~ ¤  wñsq ~ §    ÿ   ppppq ~ Msq ~ D    q ~ q ~ q ~psq ~ ¬  wñsq ~ §    ÿ   ppppq ~ Msq ~ D    q ~ q ~ psq ~ ¥  wñppppq ~ q ~ psq ~ ±  wñsq ~ §    ÿ   ppppq ~ Msq ~ D    q ~ q ~ psq ~ µ  wñsq ~ §    ÿ   ppppq ~ Msq ~ D    q ~ q ~ ppppppppppppppppq ~Ï  wñ        ppq ~vsq ~x   "uq ~{   sq ~}t boleto.noDocumentot java.lang.Stringppppppq ~¤ppq ~ësq ~¹  wñ   	        I   N   Úpq ~ q ~ #pt textField-49ppppq ~ 7ppppq ~ :  wñpppppt Arialq ~Ppq ~pppppppppsq ~  psq ~ ¤  wñsq ~ §    ÿ   ppppq ~ Msq ~ D    q ~¶q ~¶q ~³psq ~ ¬  wñsq ~ §    ÿ   ppppq ~ Msq ~ D    q ~¶q ~¶psq ~ ¥  wñppppq ~¶q ~¶psq ~ ±  wñsq ~ §    ÿ   ppppq ~ Msq ~ D    q ~¶q ~¶psq ~ µ  wñsq ~ §    ÿ   ppppq ~ Msq ~ D    q ~¶q ~¶ppppppppppppppppq ~Ï  wñ        ppq ~vsq ~x   #uq ~{   sq ~}t boleto.noDocumentot java.lang.Stringppppppq ~¤ppq ~ëxp  wñ  pp~r .net.sf.jasperreports.engine.type.SplitTypeEnum          xq ~ t STRETCHppsr java.util.HashSetºD¸·4  xpw   ?@     t net.sf.jasperreports.engine.*t java.util.*t "net.sf.jasperreports.engine.data.*xt javapsr .net.sf.jasperreports.engine.base.JRBaseDataset      'Ø I PSEUDO_SERIAL_VERSION_UIDZ isMainB whenResourceMissingType[ fieldst &[Lnet/sf/jasperreports/engine/JRField;L filterExpressionq ~ [ groupst &[Lnet/sf/jasperreports/engine/JRGroup;L nameq ~ [ 
parameterst *[Lnet/sf/jasperreports/engine/JRParameter;L 
propertiesMapq ~ 1L queryt %Lnet/sf/jasperreports/engine/JRQuery;L resourceBundleq ~ L scriptletClassq ~ [ 
scriptletst *[Lnet/sf/jasperreports/engine/JRScriptlet;[ 
sortFieldst *[Lnet/sf/jasperreports/engine/JRSortField;[ 	variablest )[Lnet/sf/jasperreports/engine/JRVariable;L whenResourceMissingTypeValuet >Lnet/sf/jasperreports/engine/type/WhenResourceMissingTypeEnum;xp  wñ ur &[Lnet.sf.jasperreports.engine.JRField;<ßÇN*òp  xp   sr ,net.sf.jasperreports.engine.base.JRBaseField      'Ø L descriptionq ~ L nameq ~ L 
propertiesMapq ~ 1L valueClassNameq ~ L valueClassRealNameq ~ xppt bancosr +net.sf.jasperreports.engine.JRPropertiesMap      'Ø L baseq ~ 1L propertiesListq ~ !L 
propertiesMapt Ljava/util/Map;xppppt java.lang.Objectpsq ~Þpt banco.numeroFormattedsq ~ápppt java.lang.Stringpsq ~Þpt banco.linhaDigitavelsq ~ápppt java.lang.Stringpsq ~Þpt banco.codigoBarrassq ~ápppt java.lang.Stringpsq ~Þpt boleto.cedentesq ~ápppt java.lang.Stringpsq ~Þpt boleto.localPagamentosq ~ápppt java.lang.Stringpsq ~Þpt boleto.dataDocumentosq ~ápppt java.lang.Stringpsq ~Þpt boleto.especieDocumentosq ~ápppt java.lang.Stringpsq ~Þpt 
boleto.aceitesq ~ápppt java.lang.Stringpsq ~Þpt boleto.dataProcessamentosq ~ápppt java.lang.Stringpsq ~Þpt boleto.carteirasq ~ápppt java.lang.Stringpsq ~Þpt boleto.dataVencimentosq ~ápppt java.lang.Stringpsq ~Þpt boleto.nossoNumerosq ~ápppt java.lang.Stringpsq ~Þpt boleto.valorBoletosq ~ápppt java.lang.Stringpsq ~Þpt boleto.nomeSacadosq ~ápppt java.lang.Stringpsq ~Þpt boleto.enderecoSacadosq ~ápppt java.lang.Stringpsq ~Þpt boleto.cepSacadosq ~ápppt java.lang.Stringpsq ~Þpt boleto.cidadeSacadosq ~ápppt java.lang.Stringpsq ~Þpt boleto.ufSacadosq ~ápppt java.lang.Stringpsq ~Þpt  banco.agenciaCodCedenteFormattedsq ~ápppt java.lang.Stringpsq ~Þpt boleto.instrucao1sq ~ápppt java.lang.Stringpsq ~Þpt banco.bancosq ~ápppt java.lang.Stringpsq ~Þpt boleto.dvNossoNumerosq ~ápppt java.lang.Stringpsq ~Þpt boleto.bairroSacadosq ~ápppt java.lang.Stringpsq ~Þpt boleto.agenciasq ~ápppt java.lang.Stringpsq ~Þpt boleto.dvAgenciasq ~ápppt java.lang.Stringpsq ~Þpt boleto.contaCorrentesq ~ápppt java.lang.Stringpsq ~Þpt boleto.dvContaCorrentesq ~ápppt java.lang.Stringpsq ~Þpt boleto.noDocumentosq ~ápppt java.lang.Stringpsq ~Þpt boleto.cpfSacadosq ~ápppt java.lang.Stringpppt boletour *[Lnet.sf.jasperreports.engine.JRParameter;" *Ã`!  xp   sr 0net.sf.jasperreports.engine.base.JRBaseParameter      'Ø 	Z isForPromptingZ isSystemDefinedL defaultValueExpressionq ~ L descriptionq ~ L nameq ~ L nestedTypeNameq ~ L 
propertiesMapq ~ 1L valueClassNameq ~ L valueClassRealNameq ~ xpppt REPORT_PARAMETERS_MAPpsq ~ápppt 
java.util.Mappsq ~\ppt 
JASPER_REPORTpsq ~ápppt (net.sf.jasperreports.engine.JasperReportpsq ~\ppt REPORT_CONNECTIONpsq ~ápppt java.sql.Connectionpsq ~\ppt REPORT_MAX_COUNTpsq ~ápppt java.lang.Integerpsq ~\ppt REPORT_DATA_SOURCEpsq ~ápppt (net.sf.jasperreports.engine.JRDataSourcepsq ~\ppt REPORT_SCRIPTLETpsq ~ápppt /net.sf.jasperreports.engine.JRAbstractScriptletpsq ~\ppt 
REPORT_LOCALEpsq ~ápppt java.util.Localepsq ~\ppt REPORT_RESOURCE_BUNDLEpsq ~ápppt java.util.ResourceBundlepsq ~\ppt REPORT_TIME_ZONEpsq ~ápppt java.util.TimeZonepsq ~\ppt REPORT_FORMAT_FACTORYpsq ~ápppt .net.sf.jasperreports.engine.util.FormatFactorypsq ~\ppt REPORT_CLASS_LOADERpsq ~ápppt java.lang.ClassLoaderpsq ~\ppt REPORT_URL_HANDLER_FACTORYpsq ~ápppt  java.net.URLStreamHandlerFactorypsq ~\ppt REPORT_FILE_RESOLVERpsq ~ápppt -net.sf.jasperreports.engine.util.FileResolverpsq ~\ppt REPORT_TEMPLATESpsq ~ápppt java.util.Collectionpsq ~\ppt SORT_FIELDSpsq ~ápppt java.util.Listpsq ~\ppt REPORT_VIRTUALIZERpsq ~ápppt )net.sf.jasperreports.engine.JRVirtualizerpsq ~\ppt IS_IGNORE_PAGINATIONpsq ~ápppt java.lang.Booleanpsq ~\  ppt enderecoEmpresapsq ~ápppt java.lang.Stringpsq ~\ sq ~x    pt java.lang.Stringppt 
SUBREPORT_DIRpsq ~ápppq ~§psq ~\ ppt cnpjEmpresapsq ~ápppt java.lang.Stringpsq ~ápsq ~ $   w   
t ireport.scriptlethandlingt ireport.encodingt ireport.zoomt 	ireport.xt 	ireport.yxsr java.util.HashMapÚÁÃ`Ñ F 
loadFactorI 	thresholdxp?@     w      q ~³t 40q ~²t 2.0q ~´t 340q ~°t 0q ~±t UTF-8xsr ,net.sf.jasperreports.engine.base.JRBaseQuery      'Ø [ chunkst +[Lnet/sf/jasperreports/engine/JRQueryChunk;L languageq ~ xppt sqlppppur )[Lnet.sf.jasperreports.engine.JRVariable;bæ|,·D  xp   sr /net.sf.jasperreports.engine.base.JRBaseVariable      'Ø I PSEUDO_SERIAL_VERSION_UIDB calculationB 
incrementTypeZ isSystemDefinedB 	resetTypeL calculationValuet 2Lnet/sf/jasperreports/engine/type/CalculationEnum;L 
expressionq ~ L incrementGroupq ~ 0L incrementTypeValuet 4Lnet/sf/jasperreports/engine/type/IncrementTypeEnum;L incrementerFactoryClassNameq ~ L incrementerFactoryClassRealNameq ~ L initialValueExpressionq ~ L nameq ~ L 
resetGroupq ~ 0L resetTypeValuet 0Lnet/sf/jasperreports/engine/type/ResetTypeEnum;L valueClassNameq ~ L valueClassRealNameq ~ xp  wî   ~r 0net.sf.jasperreports.engine.type.CalculationEnum          xq ~ t SYSTEMpp~r 2net.sf.jasperreports.engine.type.IncrementTypeEnum          xq ~ t NONEppsq ~x   uq ~{   sq ~}t new java.lang.Integer(1)q ~lpt PAGE_NUMBERp~r .net.sf.jasperreports.engine.type.ResetTypeEnum          xq ~ t REPORTq ~lpsq ~Â  wî   q ~Èppq ~Ëppsq ~x   uq ~{   sq ~}t new java.lang.Integer(1)q ~lpt 
COLUMN_NUMBERp~q ~Òt PAGEq ~lpsq ~Â  wî   ~q ~Çt COUNTsq ~x   uq ~{   sq ~}t new java.lang.Integer(1)q ~lppq ~Ëppsq ~x   uq ~{   sq ~}t new java.lang.Integer(0)q ~lpt REPORT_COUNTpq ~Óq ~lpsq ~Â  wî   q ~Þsq ~x   uq ~{   sq ~}t new java.lang.Integer(1)q ~lppq ~Ëppsq ~x   uq ~{   sq ~}t new java.lang.Integer(0)q ~lpt 
PAGE_COUNTpq ~Ûq ~lpsq ~Â  wî   q ~Þsq ~x   uq ~{   sq ~}t new java.lang.Integer(1)q ~lppq ~Ëppsq ~x   uq ~{   sq ~}t new java.lang.Integer(0)q ~lpt COLUMN_COUNTp~q ~Òt COLUMNq ~lp~r <net.sf.jasperreports.engine.type.WhenResourceMissingTypeEnum          xq ~ t NULLq ~Yp~r 0net.sf.jasperreports.engine.type.OrientationEnum          xq ~ t PORTRAITpp~r /net.sf.jasperreports.engine.type.PrintOrderEnum          xq ~ t VERTICALpppp~r 3net.sf.jasperreports.engine.type.WhenNoDataTypeEnum          xq ~ t NO_PAGESsr 6net.sf.jasperreports.engine.design.JRReportCompileData      'Ø L crosstabCompileDataq ~âL datasetCompileDataq ~âL mainDatasetCompileDataq ~ xpsq ~µ?@     w       xsq ~µ?@     w       xur [B¬óøTà  xp  /åÊþº¾   .} boleto_1485968483236_95729  ,net/sf/jasperreports/engine/fill/JREvaluator   parameter_REPORT_RESOURCE_BUNDLE 2Lnet/sf/jasperreports/engine/fill/JRFillParameter; parameter_JASPER_REPORT parameter_REPORT_CLASS_LOADER parameter_REPORT_FORMAT_FACTORY parameter_REPORT_TIME_ZONE parameter_SORT_FIELDS parameter_REPORT_DATA_SOURCE parameter_REPORT_LOCALE parameter_REPORT_FILE_RESOLVER parameter_cnpjEmpresa $parameter_REPORT_URL_HANDLER_FACTORY parameter_REPORT_PARAMETERS_MAP parameter_REPORT_CONNECTION parameter_IS_IGNORE_PAGINATION parameter_REPORT_TEMPLATES parameter_enderecoEmpresa parameter_SUBREPORT_DIR parameter_REPORT_VIRTUALIZER parameter_REPORT_SCRIPTLET parameter_REPORT_MAX_COUNT field_boleto46dataProcessamento .Lnet/sf/jasperreports/engine/fill/JRFillField; field_boleto46localPagamento field_boleto46carteira field_banco46codigoBarras field_banco46banco field_boleto46dvContaCorrente field_boleto46cedente field_banco field_boleto46bairroSacado field_boleto46cepSacado field_boleto46nossoNumero field_banco46numeroFormatted field_boleto46enderecoSacado field_boleto46cpfSacado field_boleto46contaCorrente field_boleto46instrucao1 field_boleto46dataVencimento field_boleto46dvNossoNumero field_boleto46cidadeSacado field_boleto46especieDocumento field_boleto46valorBoleto field_boleto46noDocumento field_boleto46dvAgencia 'field_banco46agenciaCodCedenteFormatted field_boleto46nomeSacado field_boleto46ufSacado field_boleto46dataDocumento field_boleto46aceite field_boleto46agencia field_banco46linhaDigitavel variable_PAGE_NUMBER 1Lnet/sf/jasperreports/engine/fill/JRFillVariable; variable_COLUMN_NUMBER variable_REPORT_COUNT variable_PAGE_COUNT variable_COLUMN_COUNT <init> ()V Code ? @
  B  	  D  	  F  	  H 	 	  J 
 	  L  	  N  	  P 
 	  R  	  T  	  V  	  X  	  Z  	  \  	  ^  	  `  	  b  	  d  	  f  	  h  	  j  	  l  	  n  	  p  	  r  	  t   	  v ! 	  x " 	  z # 	  | $ 	  ~ % 	   & 	   ' 	   ( 	   ) 	   * 	   + 	   , 	   - 	   . 	   / 	   0 	   1 	   2 	   3 	   4 	   5 	    6 	  ¢ 7 	  ¤ 8 	  ¦ 9 :	  ¨ ; :	  ª < :	  ¬ = :	  ® > :	  ° LineNumberTable customizedInit 0(Ljava/util/Map;Ljava/util/Map;Ljava/util/Map;)V 
initParams (Ljava/util/Map;)V µ ¶
  · 
initFields ¹ ¶
  º initVars ¼ ¶
  ½ REPORT_RESOURCE_BUNDLE ¿ 
java/util/Map Á get &(Ljava/lang/Object;)Ljava/lang/Object; Ã Ä Â Å 0net/sf/jasperreports/engine/fill/JRFillParameter Ç 
JASPER_REPORT É REPORT_CLASS_LOADER Ë REPORT_FORMAT_FACTORY Í REPORT_TIME_ZONE Ï SORT_FIELDS Ñ REPORT_DATA_SOURCE Ó 
REPORT_LOCALE Õ REPORT_FILE_RESOLVER × cnpjEmpresa Ù REPORT_URL_HANDLER_FACTORY Û REPORT_PARAMETERS_MAP Ý REPORT_CONNECTION ß IS_IGNORE_PAGINATION á REPORT_TEMPLATES ã enderecoEmpresa å 
SUBREPORT_DIR ç REPORT_VIRTUALIZER é REPORT_SCRIPTLET ë REPORT_MAX_COUNT í boleto.dataProcessamento ï ,net/sf/jasperreports/engine/fill/JRFillField ñ boleto.localPagamento ó boleto.carteira õ banco.codigoBarras ÷ banco.banco ù boleto.dvContaCorrente û boleto.cedente ý banco ÿ boleto.bairroSacado boleto.cepSacado boleto.nossoNumero banco.numeroFormatted boleto.enderecoSacado	 boleto.cpfSacado boleto.contaCorrente
 boleto.instrucao1 boleto.dataVencimento boleto.dvNossoNumero boleto.cidadeSacado boleto.especieDocumento boleto.valorBoleto boleto.noDocumento boleto.dvAgencia  banco.agenciaCodCedenteFormatted boleto.nomeSacado! boleto.ufSacado# boleto.dataDocumento% 
boleto.aceite' boleto.agencia) banco.linhaDigitavel+ PAGE_NUMBER- /net/sf/jasperreports/engine/fill/JRFillVariable/ 
COLUMN_NUMBER1 REPORT_COUNT3 
PAGE_COUNT5 COLUMN_COUNT7 evaluate (I)Ljava/lang/Object; 
Exceptions java/lang/Throwable< java/lang/Integer> (I)V ?@
?A getValue ()Ljava/lang/Object;CD
 òE java/lang/StringG (it/businesslogic/ireport/barcode/BcImageI getBarcodeImage I(ILjava/lang/Object;ZZLjava/lang/String;II)Ljava/awt/image/BufferedImage;KL
JM java/lang/StringBufferO valueOf &(Ljava/lang/Object;)Ljava/lang/String;QR
HS (Ljava/lang/String;)V ?U
PV  / X append ,(Ljava/lang/String;)Ljava/lang/StringBuffer;Z[
P\
 ÈE toString ()Ljava/lang/String;_`
Pa DSc Ne COBg 070-1i R$k logos/logo-brb.pngm length ()Iop
Hq  s  - u evaluateOld getOldValuexD
 òy evaluateEstimated 
SourceFile !     7                 	     
               
                                                                                                !     "     #     $     %     &     '     (     )     *     +     ,     -     .     /     0     1     2     3     4     5     6     7     8     9 :    ; :    < :    = :    > :     ? @  A      *· C*µ E*µ G*µ I*µ K*µ M*µ O*µ Q*µ S*µ U*µ W*µ Y*µ [*µ ]*µ _*µ a*µ c*µ e*µ g*µ i*µ k*µ m*µ o*µ q*µ s*µ u*µ w*µ y*µ {*µ }*µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ ¡*µ £*µ ¥*µ §*µ ©*µ «*µ ­*µ ¯*µ ±±    ²   æ 9      	          ! " " ' # , $ 1 % 6 & ; ' @ ( E ) J * O + T , Y - ^ . c / h 0 m 1 r 2 w 3 | 4  5  6  7  8  9  :  ; ¤ < © = ® > ³ ? ¸ @ ½ A Â B Ç C Ì D Ñ E Ö F Û G à H å I ê J ï K ô L ù M þ N O P
 Q R   ³ ´  A   4     *+· ¸*,· »*-· ¾±    ²       ^  _ 
 `  a  µ ¶  A  Ñ    i*+À¹ Æ À ÈÀ Èµ E*+Ê¹ Æ À ÈÀ Èµ G*+Ì¹ Æ À ÈÀ Èµ I*+Î¹ Æ À ÈÀ Èµ K*+Ð¹ Æ À ÈÀ Èµ M*+Ò¹ Æ À ÈÀ Èµ O*+Ô¹ Æ À ÈÀ Èµ Q*+Ö¹ Æ À ÈÀ Èµ S*+Ø¹ Æ À ÈÀ Èµ U*+Ú¹ Æ À ÈÀ Èµ W*+Ü¹ Æ À ÈÀ Èµ Y*+Þ¹ Æ À ÈÀ Èµ [*+à¹ Æ À ÈÀ Èµ ]*+â¹ Æ À ÈÀ Èµ _*+ä¹ Æ À ÈÀ Èµ a*+æ¹ Æ À ÈÀ Èµ c*+è¹ Æ À ÈÀ Èµ e*+ê¹ Æ À ÈÀ Èµ g*+ì¹ Æ À ÈÀ Èµ i*+î¹ Æ À ÈÀ Èµ k±    ²   V    i  j $ k 6 l H m Z n l o ~ p  q ¢ r ´ s Æ t Ø u ê v ü w x  y2 zD {V |h }  ¹ ¶  A  Ä    4*+ð¹ Æ À òÀ òµ m*+ô¹ Æ À òÀ òµ o*+ö¹ Æ À òÀ òµ q*+ø¹ Æ À òÀ òµ s*+ú¹ Æ À òÀ òµ u*+ü¹ Æ À òÀ òµ w*+þ¹ Æ À òÀ òµ y*+ ¹ Æ À òÀ òµ {*+¹ Æ À òÀ òµ }*+¹ Æ À òÀ òµ *+¹ Æ À òÀ òµ *+¹ Æ À òÀ òµ *+
¹ Æ À òÀ òµ *+¹ Æ À òÀ òµ *+¹ Æ À òÀ òµ *+¹ Æ À òÀ òµ *+¹ Æ À òÀ òµ *+¹ Æ À òÀ òµ *+¹ Æ À òÀ òµ *+¹ Æ À òÀ òµ *+¹ Æ À òÀ òµ *+¹ Æ À òÀ òµ *+¹ Æ À òÀ òµ *+ ¹ Æ À òÀ òµ *+"¹ Æ À òÀ òµ *+$¹ Æ À òÀ òµ *+&¹ Æ À òÀ òµ ¡*+(¹ Æ À òÀ òµ £*+*¹ Æ À òÀ òµ ¥*+,¹ Æ À òÀ òµ §±    ²   ~       $  6  H  Z  l  ~    ¤  ·  Ê  Ý  ð   ) < O b u   ® Á Ô ç ú  
 ¡  ¢3 £  ¼ ¶  A        `*+.¹ Æ À0À0µ ©*+2¹ Æ À0À0µ «*+4¹ Æ À0À0µ ­*+6¹ Æ À0À0µ ¯*+8¹ Æ À0À0µ ±±    ²       «  ¬ & ­ 9 ® L ¯ _ ° 9: ;    = A  n    Mª  ý       #      ¢   ®   º   Æ   Ò   Þ   ê   ö      (  i  w  ~        ¨  ¶  Ä  Ë  Ù  à    "  )  7  E  S  m      £  ä  òM§^»?Y·BM§R»?Y·BM§F»?Y·BM§:»?Y·BM§.»?Y·BM§"»?Y·BM§»?Y·BM§
»?Y·BM§þ
*´ s¶FÀH¸NM§æ*´ §¶FÀHM§Ø»PY*´ y¶FÀH¸T·WY¶]*´ W¶^ÀH¶]Y¶]*´ c¶^ÀH¶]¶bM§*´ ¡¶FÀHM§dM§fM§{hM§t*´ ¶FÀHM§f*´ ¶FÀHM§X*´ ¶FÀHM§J*´ ¶FÀHM§<jM§5*´ §¶FÀHM§'lM§ »PY*´ e¶^ÀH¸T·Wn¶]¶bM§ÿ»PY*´ e¶^ÀH¸T·Wn¶]¶bM§ÞjM§×*´ ¶FÀHM§É*´ ¶FÀHM§»*´ ¶FÀHM§­»PY*´ ¶FÀH¸T·W*´ ¶FÀH¶r 	t§ »PYY·W*´ ¶FÀH¶]¶b¶]*´ ¶FÀH¶r 	t§ »PYY·W*´ ¶FÀH¶]¶b¶]*´ ¶FÀH¶r 	t§ »PYY·W*´ ¶FÀH¶]¶b¶]*´ ¶FÀH¶r 	t§ »PYv·W*´ ¶FÀH¶]¶b¶]*´ ¶FÀH¶r 	t§ »PYv·W*´ ¶FÀH¶]¶b¶]¶bM§»PY*´ ¶FÀH¸T·W*´ ¶FÀH¶r 	t§ »PYY·W*´ ¶FÀH¶]¶b¶]*´ ¶FÀH¶r 	t§ »PYY·W*´ ¶FÀH¶]¶b¶]*´ ¶FÀH¶r 	t§ »PYY·W*´ ¶FÀH¶]¶b¶]*´ ¶FÀH¶r 	t§ »PYv·W*´ ¶FÀH¶]¶b¶]*´ ¶FÀH¶r 	t§ »PYv·W*´ ¶FÀH¶]¶b¶]¶bM§ y*´ ¶FÀHM§ k*´ ¶FÀHM§ ]»PY*´ y¶FÀH¸T·WY¶]*´ W¶^ÀH¶]Y¶]*´ c¶^ÀH¶]¶bM§ *´ ¶FÀHM§ *´ ¶FÀHM,°    ²  Z V   ¸  º   ¾ ¢ ¿ ¥ Ã ® Ä ± È º É ½ Í Æ Î É Ò Ò Ó Õ × Þ Ø á Ü ê Ý í á ö â ù æ ç ë ì ð( ñ+ õi öl úw ûz ÿ~ 	
¨«¶¹ÄÇ"Ë#Î'Ù(Ü,à-ã126"7%;)<,@7A:EEFHJSKVOjPQÐRS6TiOmUpYZ·[ê\]P^Y_cdh£i¦mänçròsõw  w: ;    = A  n    Mª  ý       #      ¢   ®   º   Æ   Ò   Þ   ê   ö      (  i  w  ~        ¨  ¶  Ä  Ë  Ù  à    "  )  7  E  S  m      £  ä  òM§^»?Y·BM§R»?Y·BM§F»?Y·BM§:»?Y·BM§.»?Y·BM§"»?Y·BM§»?Y·BM§
»?Y·BM§þ
*´ s¶zÀH¸NM§æ*´ §¶zÀHM§Ø»PY*´ y¶zÀH¸T·WY¶]*´ W¶^ÀH¶]Y¶]*´ c¶^ÀH¶]¶bM§*´ ¡¶zÀHM§dM§fM§{hM§t*´ ¶zÀHM§f*´ ¶zÀHM§X*´ ¶zÀHM§J*´ ¶zÀHM§<jM§5*´ §¶zÀHM§'lM§ »PY*´ e¶^ÀH¸T·Wn¶]¶bM§ÿ»PY*´ e¶^ÀH¸T·Wn¶]¶bM§ÞjM§×*´ ¶zÀHM§É*´ ¶zÀHM§»*´ ¶zÀHM§­»PY*´ ¶zÀH¸T·W*´ ¶zÀH¶r 	t§ »PYY·W*´ ¶zÀH¶]¶b¶]*´ ¶zÀH¶r 	t§ »PYY·W*´ ¶zÀH¶]¶b¶]*´ ¶zÀH¶r 	t§ »PYY·W*´ ¶zÀH¶]¶b¶]*´ ¶zÀH¶r 	t§ »PYv·W*´ ¶zÀH¶]¶b¶]*´ ¶zÀH¶r 	t§ »PYv·W*´ ¶zÀH¶]¶b¶]¶bM§»PY*´ ¶zÀH¸T·W*´ ¶zÀH¶r 	t§ »PYY·W*´ ¶zÀH¶]¶b¶]*´ ¶zÀH¶r 	t§ »PYY·W*´ ¶zÀH¶]¶b¶]*´ ¶zÀH¶r 	t§ »PYY·W*´ ¶zÀH¶]¶b¶]*´ ¶zÀH¶r 	t§ »PYv·W*´ ¶zÀH¶]¶b¶]*´ ¶zÀH¶r 	t§ »PYv·W*´ ¶zÀH¶]¶b¶]¶bM§ y*´ ¶zÀHM§ k*´ ¶zÀHM§ ]»PY*´ y¶zÀH¸T·WY¶]*´ W¶^ÀH¶]Y¶]*´ c¶^ÀH¶]¶bM§ *´ ¶zÀHM§ *´ ¶zÀHM,°    ²  Z V      ¢ ¥ ® ± º ½ Æ É¢ Ò£ Õ§ Þ¨ á¬ ê­ í± ö² ù¶·»¼À(Á+ÅiÆlÊwËzÏ~ÐÔÕÙÚÞßã¨ä«è¶é¹íÄîÇòËóÎ÷ÙøÜüàýã"%),7:EHSVj !Ð"#6$im%p)*·+ê,-P.)/348£9¦=ä>çBòCõG O {: ;    = A  n    Mª  ý       #      ¢   ®   º   Æ   Ò   Þ   ê   ö      (  i  w  ~        ¨  ¶  Ä  Ë  Ù  à    "  )  7  E  S  m      £  ä  òM§^»?Y·BM§R»?Y·BM§F»?Y·BM§:»?Y·BM§.»?Y·BM§"»?Y·BM§»?Y·BM§
»?Y·BM§þ
*´ s¶FÀH¸NM§æ*´ §¶FÀHM§Ø»PY*´ y¶FÀH¸T·WY¶]*´ W¶^ÀH¶]Y¶]*´ c¶^ÀH¶]¶bM§*´ ¡¶FÀHM§dM§fM§{hM§t*´ ¶FÀHM§f*´ ¶FÀHM§X*´ ¶FÀHM§J*´ ¶FÀHM§<jM§5*´ §¶FÀHM§'lM§ »PY*´ e¶^ÀH¸T·Wn¶]¶bM§ÿ»PY*´ e¶^ÀH¸T·Wn¶]¶bM§ÞjM§×*´ ¶FÀHM§É*´ ¶FÀHM§»*´ ¶FÀHM§­»PY*´ ¶FÀH¸T·W*´ ¶FÀH¶r 	t§ »PYY·W*´ ¶FÀH¶]¶b¶]*´ ¶FÀH¶r 	t§ »PYY·W*´ ¶FÀH¶]¶b¶]*´ ¶FÀH¶r 	t§ »PYY·W*´ ¶FÀH¶]¶b¶]*´ ¶FÀH¶r 	t§ »PYv·W*´ ¶FÀH¶]¶b¶]*´ ¶FÀH¶r 	t§ »PYv·W*´ ¶FÀH¶]¶b¶]¶bM§»PY*´ ¶FÀH¸T·W*´ ¶FÀH¶r 	t§ »PYY·W*´ ¶FÀH¶]¶b¶]*´ ¶FÀH¶r 	t§ »PYY·W*´ ¶FÀH¶]¶b¶]*´ ¶FÀH¶r 	t§ »PYY·W*´ ¶FÀH¶]¶b¶]*´ ¶FÀH¶r 	t§ »PYv·W*´ ¶FÀH¶]¶b¶]*´ ¶FÀH¶r 	t§ »PYv·W*´ ¶FÀH¶]¶b¶]¶bM§ y*´ ¶FÀHM§ k*´ ¶FÀHM§ ]»PY*´ y¶FÀH¸T·WY¶]*´ W¶^ÀH¶]Y¶]*´ c¶^ÀH¶]¶bM§ *´ ¶FÀHM§ *´ ¶FÀHM,°    ²  Z V  X Z  ^ ¢_ ¥c ®d ±h ºi ½m Æn Ér Òs Õw Þx á| ê} í ö ù(+ilwz~ ¤¥©ª®¯³¨´«¸¶¹¹½Ä¾ÇÂËÃÎÇÙÈÜÌàÍãÑÒÖ"×%Û)Ü,à7á:åEæHêSëVïjðñÐòó6ôiïmõpùú·ûêüýPþùÿ£	¦
äçòõ  |    t _1485968483236_95729t 2net.sf.jasperreports.engine.design.JRJavacCompiler
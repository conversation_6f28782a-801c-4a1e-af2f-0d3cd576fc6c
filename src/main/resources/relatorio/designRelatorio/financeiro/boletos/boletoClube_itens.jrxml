<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="boletoClube_itens" language="groovy" pageWidth="260" pageHeight="20" columnWidth="260" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0">
	<property name="ireport.zoom" value="4.0"/>
	<property name="ireport.x" value="21"/>
	<property name="ireport.y" value="0"/>
	<field name="valor" class="java.lang.String"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<detail>
		<band height="12" splitType="Stretch">
			<textField>
				<reportElement x="0" y="0" width="260" height="12"/>
				<textElement>
					<font fontName="Arial" size="7"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{valor}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
</jasperReport>

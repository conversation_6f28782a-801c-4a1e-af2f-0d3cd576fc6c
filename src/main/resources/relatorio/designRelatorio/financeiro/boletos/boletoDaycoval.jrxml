<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="boleto" pageWidth="539" pageHeight="400" columnWidth="534" leftMargin="5" rightMargin="0" topMargin="0" bottomMargin="0">
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.encoding" value="UTF-8"/>
	<property name="ireport.zoom" value="2.0"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="java.util.*"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<parameter name="enderecoEmpresa" class="java.lang.String" isForPrompting="false"/>
	<parameter name="SUBREPORT_DIR" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="cnpjEmpresa" class="java.lang.String"/>
	<queryString>
		<![CDATA[]]>
	</queryString>
	<field name="banco" class="java.lang.Object"/>
	<field name="banco.numeroFormatted" class="java.lang.String"/>
	<field name="banco.linhaDigitavel" class="java.lang.String"/>
	<field name="banco.codigoBarras" class="java.lang.String"/>
	<field name="boleto.cedente" class="java.lang.String"/>
	<field name="boleto.localPagamento" class="java.lang.String"/>
	<field name="boleto.dataDocumento" class="java.lang.String"/>
	<field name="boleto.especieDocumento" class="java.lang.String"/>
	<field name="boleto.aceite" class="java.lang.String"/>
	<field name="boleto.dataProcessamento" class="java.lang.String"/>
	<field name="boleto.carteira" class="java.lang.String"/>
	<field name="boleto.dataVencimento" class="java.lang.String"/>
	<field name="boleto.nossoNumero" class="java.lang.String"/>
	<field name="boleto.valorBoleto" class="java.lang.String"/>
	<field name="boleto.nomeSacado" class="java.lang.String"/>
	<field name="boleto.enderecoSacado" class="java.lang.String"/>
	<field name="boleto.cepSacado" class="java.lang.String"/>
	<field name="boleto.cidadeSacado" class="java.lang.String"/>
	<field name="boleto.ufSacado" class="java.lang.String"/>
	<field name="banco.agenciaCodCedenteFormatted" class="java.lang.String"/>
	<field name="boleto.instrucao1" class="java.lang.String"/>
	<field name="banco.banco" class="java.lang.String"/>
	<field name="boleto.dvNossoNumero" class="java.lang.String"/>
	<field name="boleto.bairroSacado" class="java.lang.String"/>
	<field name="boleto.responsavel" class="java.lang.String">
		<fieldDescription><![CDATA[boleto.responsavel]]></fieldDescription>
	</field>
	<field name="boleto.cpfSacado" class="java.lang.String"/>
	<field name="boleto.noDocumento" class="java.lang.String"/>
	<detail>
		<band height="400" splitType="Stretch">
			<line>
				<reportElement key="line-20" x="0" y="127" width="524" height="1"/>
				<graphicElement>
					<pen lineWidth="1.0" lineStyle="Dashed"/>
				</graphicElement>
			</line>
			<line>
				<reportElement key="line-1" x="2" y="162" width="521" height="1"/>
				<graphicElement>
					<pen lineWidth="1.0" lineStyle="Solid"/>
				</graphicElement>
			</line>
			<line>
				<reportElement key="line-2" x="115" y="145" width="1" height="17"/>
				<graphicElement>
					<pen lineWidth="1.0" lineStyle="Solid"/>
				</graphicElement>
			</line>
			<line>
				<reportElement key="line-3" x="154" y="145" width="1" height="17"/>
				<graphicElement>
					<pen lineWidth="1.0" lineStyle="Solid"/>
				</graphicElement>
			</line>
			<line>
				<reportElement key="line-4" x="2" y="179" width="521" height="1"/>
				<graphicElement>
					<pen lineWidth="1.0" lineStyle="Solid"/>
				</graphicElement>
			</line>
			<line>
				<reportElement key="line-5" x="2" y="196" width="521" height="1"/>
				<graphicElement>
					<pen lineWidth="1.0" lineStyle="Solid"/>
				</graphicElement>
			</line>
			<line>
				<reportElement key="line-6" x="2" y="213" width="521" height="1"/>
				<graphicElement>
					<pen lineWidth="1.0" lineStyle="Solid"/>
				</graphicElement>
			</line>
			<line>
				<reportElement key="line-7" x="2" y="230" width="521" height="1"/>
				<graphicElement>
					<pen lineWidth="1.0" lineStyle="Solid"/>
				</graphicElement>
			</line>
			<line>
				<reportElement key="line-8" x="354" y="162" width="1" height="153"/>
				<graphicElement>
					<pen lineWidth="1.0" lineStyle="Solid"/>
				</graphicElement>
			</line>
			<line>
				<reportElement key="line-9" x="2" y="315" width="521" height="1"/>
				<graphicElement>
					<pen lineWidth="1.0" lineStyle="Solid"/>
				</graphicElement>
			</line>
			<line>
				<reportElement key="line-10" x="354" y="247" width="170" height="1"/>
				<graphicElement>
					<pen lineWidth="1.0" lineStyle="Solid"/>
				</graphicElement>
			</line>
			<line>
				<reportElement key="line-11" x="354" y="264" width="170" height="1"/>
				<graphicElement>
					<pen lineWidth="1.0" lineStyle="Solid"/>
				</graphicElement>
			</line>
			<line>
				<reportElement key="line-12" x="354" y="281" width="170" height="1"/>
				<graphicElement>
					<pen lineWidth="1.0" lineStyle="Solid"/>
				</graphicElement>
			</line>
			<line>
				<reportElement key="line-13" x="354" y="298" width="170" height="1"/>
				<graphicElement>
					<pen lineWidth="1.0" lineStyle="Solid"/>
				</graphicElement>
			</line>
			<line>
				<reportElement key="line-14" x="76" y="196" width="1" height="34"/>
				<graphicElement>
					<pen lineWidth="1.0" lineStyle="Solid"/>
				</graphicElement>
			</line>
			<line>
				<reportElement key="line-15" x="152" y="196" width="1" height="34"/>
				<graphicElement>
					<pen lineWidth="1.0" lineStyle="Solid"/>
				</graphicElement>
			</line>
			<line>
				<reportElement key="line-16" x="124" y="213" width="1" height="17"/>
				<graphicElement>
					<pen lineWidth="1.0" lineStyle="Solid"/>
				</graphicElement>
			</line>
			<line>
				<reportElement key="line-17" x="237" y="196" width="1" height="34"/>
				<graphicElement>
					<pen lineWidth="1.0" lineStyle="Solid"/>
				</graphicElement>
			</line>
			<line>
				<reportElement key="line-18" x="209" y="196" width="1" height="17"/>
				<graphicElement>
					<pen lineWidth="1.0" lineStyle="Solid"/>
				</graphicElement>
			</line>
			<line>
				<reportElement key="line-19" x="2" y="352" width="521" height="1"/>
				<graphicElement>
					<pen lineWidth="1.0" lineStyle="Solid"/>
				</graphicElement>
			</line>
			<staticText>
				<reportElement key="staticText-1" x="1" y="162" width="66" height="8"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="6" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Local de pagamento]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-2" x="1" y="179" width="66" height="8"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="6" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Beneficiário]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-3" x="1" y="196" width="66" height="8"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="6" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Data do Documento]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-4" x="1" y="213" width="75" height="8"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="6" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Uso do Banco]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-5" x="1" y="232" width="75" height="8"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="6" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Instruções :]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-6" x="79" y="196" width="66" height="8"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="6" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Nº do Documento]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-7" x="155" y="196" width="42" height="8"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="6" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Espécie Doc.]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-8" x="212" y="196" width="22" height="8"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="6" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Aceite]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-9" x="240" y="196" width="88" height="8"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="6" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Data do Processamento]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-10" x="79" y="213" width="35" height="8"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="6" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Carteira]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-11" x="126" y="213" width="24" height="8"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="6" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Espécie]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-12" x="155" y="213" width="66" height="8"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="6" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Quantidade]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-13" x="240" y="213" width="66" height="8"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="6" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Valor]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-14" x="357" y="162" width="100" height="8"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="6" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Vencimento]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-15" x="357" y="179" width="100" height="8"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="6" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Agência / Código Cedente]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-16" x="357" y="298" width="100" height="8"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="6" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[(=) Valor cobrado]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-18" x="357" y="264" width="100" height="8"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="6" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[(+) Mora / Multa]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-20" x="357" y="230" width="100" height="8"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="6" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[(-) Desconto / Abatimento]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-21" x="357" y="213" width="100" height="8"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="6" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[(=) Valor do Documento]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-22" x="357" y="196" width="100" height="8"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="6" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Nosso Número]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-23" x="1" y="316" width="33" height="9"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="6" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Pagador :]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-24" x="1" y="342" width="65" height="9"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="6" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Sacador / Avalista :]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-25" x="357" y="342" width="57" height="9"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="6" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Código de baixa:]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-26" x="332" y="354" width="189" height="8"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="6" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Autenticação mecânica / Ficha de Compensação]]></text>
			</staticText>
			<image scaleImage="FillFrame" hAlign="Left" vAlign="Top">
				<reportElement key="barcode-1" mode="Opaque" x="3" y="357" width="319" height="35" forecolor="#000000" backcolor="#FFFFFF"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<graphicElement fill="Solid">
					<pen lineWidth="0.0" lineStyle="Solid"/>
				</graphicElement>
				<imageExpression class="java.awt.Image"><![CDATA[it.businesslogic.ireport.barcode.BcImage.getBarcodeImage(13,$F{banco.codigoBarras},false,false,null,0,0)]]></imageExpression>
			</image>
			<line>
				<reportElement key="line-22" x="0" y="1" width="524" height="1"/>
				<graphicElement>
					<pen lineWidth="1.0" lineStyle="Dashed"/>
				</graphicElement>
			</line>
			<staticText>
				<reportElement key="staticText-29" x="424" y="2" width="100" height="15"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="9" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Recibo do Pagador]]></text>
			</staticText>
			<line>
				<reportElement key="line-50" x="1" y="396" width="521" height="1"/>
				<graphicElement>
					<pen lineWidth="1.0" lineStyle="Dashed"/>
				</graphicElement>
			</line>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-35" x="116" y="145" width="36" height="15"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA["707"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="156" y="145" width="368" height="15"/>
				<textElement textAlignment="Center">
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{banco.linhaDigitavel}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="5" y="187" width="276" height="9"/>
				<textElement verticalAlignment="Bottom">
					<font fontName="Arial" size="7"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{boleto.cedente}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="5" y="170" width="347" height="9"/>
				<textElement verticalAlignment="Bottom">
					<font fontName="Arial" size="7"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{boleto.localPagamento}]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement x="5" y="204" width="66" height="9"/>
				<textElement verticalAlignment="Bottom">
					<font fontName="Arial" size="7"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{boleto.dataDocumento}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="155" y="204" width="51" height="9"/>
				<textElement textAlignment="Center" verticalAlignment="Bottom">
					<font fontName="Arial" size="7"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{boleto.especieDocumento}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="211" y="204" width="24" height="9"/>
				<textElement textAlignment="Center" verticalAlignment="Bottom">
					<font fontName="Arial" size="7"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{boleto.aceite}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="79" y="221" width="40" height="9"/>
				<textElement textAlignment="Center" verticalAlignment="Bottom">
					<font fontName="Arial" size="7"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{boleto.carteira}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="411" y="170" width="92" height="9"/>
				<textElement textAlignment="Center" verticalAlignment="Bottom">
					<font fontName="Arial" size="7"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{boleto.dataVencimento}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="411" y="221" width="92" height="9"/>
				<textElement textAlignment="Right" verticalAlignment="Bottom">
					<font fontName="Arial" size="7"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{boleto.valorBoleto}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="37" y="316" width="233" height="9"/>
				<textElement verticalAlignment="Bottom">
					<font fontName="Arial" size="7"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[(($F{boleto.responsavel} != null &&  !$F{boleto.responsavel}.isEmpty()) ? $F{boleto.responsavel} :  $F{boleto.nomeSacado})]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement key="textField-49" x="363" y="203" width="140" height="9"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Bottom">
					<font fontName="Arial" size="7"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{boleto.carteira} + "/" + $F{boleto.nossoNumero}+"-"+$F{boleto.dvNossoNumero}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="37" y="329" width="159" height="9"/>
				<textElement verticalAlignment="Bottom">
					<font fontName="Arial" size="7"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{boleto.enderecoSacado}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="197" y="329" width="73" height="9"/>
				<textElement verticalAlignment="Bottom">
					<font fontName="Arial" size="7"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[($F{boleto.cepSacado}.length()<=5?$F{boleto.cepSacado}+"-000":$F{boleto.cepSacado}.substring(0,5)+"-"+$F{boleto.cepSacado}.substring(5))]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="343" y="329" width="178" height="9"/>
				<textElement verticalAlignment="Bottom">
					<font fontName="Arial" size="7"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[($F{boleto.cidadeSacado}==null?"":$F{boleto.cidadeSacado}.trim() + "  " ) + ( $F{boleto.ufSacado}==null? "" : ( $F{boleto.ufSacado}))]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="411" y="187" width="92" height="9"/>
				<textElement textAlignment="Right" verticalAlignment="Bottom">
					<font fontName="Arial" size="7"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{banco.agenciaCodCedenteFormatted}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-24" x="5" y="240" width="340" height="70"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Top">
					<font fontName="Arial" size="7"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{boleto.instrucao1}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-35" x="116" y="17" width="36" height="15"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA["707"]]></textFieldExpression>
			</textField>
			<line>
				<reportElement key="line-3" x="154" y="17" width="1" height="17"/>
				<graphicElement>
					<pen lineWidth="1.0" lineStyle="Solid"/>
				</graphicElement>
			</line>
			<line>
				<reportElement key="line-2" x="115" y="17" width="1" height="17"/>
				<graphicElement>
					<pen lineWidth="1.0" lineStyle="Solid"/>
				</graphicElement>
			</line>
			<textField>
				<reportElement x="156" y="17" width="368" height="15"/>
				<textElement textAlignment="Center">
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{banco.linhaDigitavel}]]></textFieldExpression>
			</textField>
			<line>
				<reportElement key="line-1" x="2" y="34" width="521" height="1"/>
				<graphicElement>
					<pen lineWidth="1.0" lineStyle="Solid"/>
				</graphicElement>
			</line>
			<staticText>
				<reportElement key="staticText-1" x="1" y="35" width="66" height="8"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="6" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Beneficiário]]></text>
			</staticText>
			<line>
				<reportElement key="line-1" x="1" y="52" width="521" height="1"/>
				<graphicElement>
					<pen lineWidth="1.0" lineStyle="Solid"/>
				</graphicElement>
			</line>
			<textField>
				<reportElement x="1" y="43" width="196" height="9"/>
				<textElement verticalAlignment="Bottom">
					<font fontName="Arial" size="7"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{boleto.cedente}]]></textFieldExpression>
			</textField>
			<line>
				<reportElement key="line-3" x="196" y="35" width="1" height="17"/>
				<graphicElement>
					<pen lineWidth="1.0" lineStyle="Solid"/>
				</graphicElement>
			</line>
			<staticText>
				<reportElement key="staticText-1" x="198" y="35" width="37" height="8"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="6" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[CNPJ/CPF]]></text>
			</staticText>
			<line>
				<reportElement key="line-3" x="269" y="35" width="1" height="17"/>
				<graphicElement>
					<pen lineWidth="1.0" lineStyle="Solid"/>
				</graphicElement>
			</line>
			<staticText>
				<reportElement key="staticText-14" x="424" y="35" width="99" height="8"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="6" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Vencimento]]></text>
			</staticText>
			<textField>
				<reportElement x="424" y="43" width="99" height="9"/>
				<textElement textAlignment="Center" verticalAlignment="Bottom">
					<font fontName="Arial" size="7"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{boleto.dataVencimento}]]></textFieldExpression>
			</textField>
			<line>
				<reportElement key="line-3" x="422" y="35" width="1" height="17"/>
				<graphicElement>
					<pen lineWidth="1.0" lineStyle="Solid"/>
				</graphicElement>
			</line>
			<staticText>
				<reportElement key="staticText-1" x="271" y="35" width="61" height="8"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="6" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Sacador Avalista]]></text>
			</staticText>
			<line>
				<reportElement key="line-1" x="1" y="70" width="521" height="1"/>
				<graphicElement>
					<pen lineWidth="1.0" lineStyle="Solid"/>
				</graphicElement>
			</line>
			<staticText>
				<reportElement key="staticText-1" x="1" y="53" width="121" height="8"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="6" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Endereço Beneficiário / Sacador Avalista]]></text>
			</staticText>
			<line>
				<reportElement key="line-1" x="1" y="88" width="521" height="1"/>
				<graphicElement>
					<pen lineWidth="1.0" lineStyle="Solid"/>
				</graphicElement>
			</line>
			<line>
				<reportElement key="line-1" x="1" y="106" width="521" height="1"/>
				<graphicElement>
					<pen lineWidth="1.0" lineStyle="Solid"/>
				</graphicElement>
			</line>
			<line>
				<reportElement key="line-3" x="85" y="70" width="1" height="36"/>
				<graphicElement>
					<pen lineWidth="1.0" lineStyle="Solid"/>
				</graphicElement>
			</line>
			<line>
				<reportElement key="line-3" x="170" y="70" width="1" height="36"/>
				<graphicElement>
					<pen lineWidth="1.0" lineStyle="Solid"/>
				</graphicElement>
			</line>
			<line>
				<reportElement key="line-3" x="220" y="71" width="1" height="17"/>
				<graphicElement>
					<pen lineWidth="1.0" lineStyle="Solid"/>
				</graphicElement>
			</line>
			<line>
				<reportElement key="line-3" x="255" y="89" width="1" height="17"/>
				<graphicElement>
					<pen lineWidth="1.0" lineStyle="Solid"/>
				</graphicElement>
			</line>
			<line>
				<reportElement key="line-3" x="305" y="89" width="1" height="17"/>
				<graphicElement>
					<pen lineWidth="1.0" lineStyle="Solid"/>
				</graphicElement>
			</line>
			<line>
				<reportElement key="line-3" x="400" y="89" width="1" height="17"/>
				<graphicElement>
					<pen lineWidth="1.0" lineStyle="Solid"/>
				</graphicElement>
			</line>
			<line>
				<reportElement key="line-3" x="420" y="71" width="1" height="17"/>
				<graphicElement>
					<pen lineWidth="1.0" lineStyle="Solid"/>
				</graphicElement>
			</line>
			<line>
				<reportElement key="line-3" x="320" y="71" width="1" height="17"/>
				<graphicElement>
					<pen lineWidth="1.0" lineStyle="Solid"/>
				</graphicElement>
			</line>
			<staticText>
				<reportElement key="staticText-1" x="1" y="71" width="73" height="8"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="6" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Nosso Número]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-1" x="86" y="71" width="70" height="8"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="6" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Carteira]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-1" x="171" y="71" width="27" height="8"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="6" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Espécie]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-1" x="222" y="71" width="37" height="8"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="6" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Quantidade]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-1" x="322" y="71" width="36" height="8"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="6" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Valor]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-1" x="422" y="71" width="101" height="8"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="6" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Agência/Código do Beneficiário]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-1" x="402" y="89" width="66" height="8"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="6" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Valor do Documento]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-1" x="306" y="89" width="76" height="8"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="6" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Data de Processamento]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-1" x="256" y="89" width="34" height="8"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="6" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Aceite]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-1" x="172" y="89" width="69" height="8"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="6" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Espécie do Documento]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-1" x="87" y="89" width="69" height="8"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="6" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Número do Documento]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-1" x="1" y="89" width="69" height="8"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="6" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Data do Documento]]></text>
			</staticText>
			<line>
				<reportElement key="line-3" x="522" y="109" width="1" height="17"/>
				<graphicElement>
					<pen lineWidth="1.0" lineStyle="Solid"/>
				</graphicElement>
			</line>
			<line>
				<reportElement key="line-3" x="307" y="109" width="1" height="17"/>
				<graphicElement>
					<pen lineWidth="1.0" lineStyle="Solid"/>
				</graphicElement>
			</line>
			<line>
				<reportElement key="line-1" x="307" y="109" width="215" height="1"/>
				<graphicElement>
					<pen lineWidth="1.0" lineStyle="Solid"/>
				</graphicElement>
			</line>
			<staticText>
				<reportElement key="staticText-1" x="308" y="110" width="214" height="8"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="6" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Autenticação Mecânica]]></text>
			</staticText>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement key="textField-49" x="2" y="79" width="81" height="9"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Bottom">
					<font fontName="Arial" size="7"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{boleto.nossoNumero}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="87" y="79" width="82" height="9"/>
				<textElement textAlignment="Center" verticalAlignment="Bottom">
					<font fontName="Arial" size="7"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{boleto.carteira}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="403" y="97" width="118" height="9"/>
				<textElement textAlignment="Center" verticalAlignment="Bottom">
					<font fontName="Arial" size="7"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{boleto.valorBoleto}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement key="staticText-1" x="283" y="180" width="37" height="8"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="6" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[CNPJ/CPF]]></text>
			</staticText>
			<line>
				<reportElement key="line-3" x="281" y="179" width="1" height="17"/>
				<graphicElement>
					<pen lineWidth="1.0" lineStyle="Solid"/>
				</graphicElement>
			</line>
			<textField>
				<reportElement x="172" y="79" width="48" height="9"/>
				<textElement textAlignment="Center" verticalAlignment="Bottom">
					<font fontName="Arial" size="7"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{boleto.especieDocumento}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="125" y="221" width="27" height="9"/>
				<textElement textAlignment="Center" verticalAlignment="Bottom">
					<font fontName="Arial" size="7"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA["R$"]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement key="staticText-24" x="270" y="316" width="35" height="9"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="6" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[CNPJ/CPF:]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-23" x="1" y="329" width="33" height="9"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="6" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Endereço]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-24" x="271" y="342" width="35" height="9"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="6" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[CNPJ:]]></text>
			</staticText>
			<textField>
				<reportElement x="1" y="61" width="522" height="9"/>
				<textElement verticalAlignment="Bottom">
					<font fontName="Arial" size="7"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{enderecoEmpresa}]]></textFieldExpression>
			</textField>
			<image scaleImage="RealSize">
				<reportElement x="6" y="134" width="108" height="25"/>
				<imageExpression class="java.lang.String"><![CDATA[$P{SUBREPORT_DIR}  + "logos//logo-daycoval.gif"]]></imageExpression>
			</image>
			<image scaleImage="RealSize">
				<reportElement x="6" y="7" width="108" height="25"/>
				<imageExpression class="java.lang.String"><![CDATA[$P{SUBREPORT_DIR}  + "logos//logo-daycoval.gif"]]></imageExpression>
			</image>
			<textField>
				<reportElement x="199" y="43" width="69" height="9"/>
				<textElement verticalAlignment="Bottom">
					<font fontName="Arial" size="7"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{cnpjEmpresa}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="285" y="187" width="68" height="9"/>
				<textElement verticalAlignment="Bottom">
					<font fontName="Arial" size="7"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{cnpjEmpresa}]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement x="3" y="97" width="80" height="9"/>
				<textElement textAlignment="Center" verticalAlignment="Bottom">
					<font fontName="Arial" size="7"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{boleto.dataDocumento}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="423" y="79" width="100" height="9"/>
				<textElement textAlignment="Center" verticalAlignment="Bottom">
					<font fontName="Arial" size="7"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{banco.agenciaCodCedenteFormatted}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="271" y="329" width="71" height="9"/>
				<textElement verticalAlignment="Bottom">
					<font fontName="Arial" size="7"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{boleto.bairroSacado}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="306" y="315" width="70" height="9"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="7"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{boleto.cpfSacado}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="241" y="204" width="104" height="9"/>
				<textElement verticalAlignment="Bottom">
					<font fontName="Arial" size="7"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{boleto.dataProcessamento}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="306" y="97" width="94" height="9"/>
				<textElement verticalAlignment="Bottom">
					<font fontName="Arial" size="7"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{boleto.dataProcessamento}]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement key="textField-49" x="87" y="97" width="81" height="9"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Bottom">
					<font fontName="Arial" size="7"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{boleto.noDocumento}]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement key="textField-49" x="79" y="204" width="66" height="9"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Bottom">
					<font fontName="Arial" size="7"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{boleto.noDocumento}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
</jasperReport>

¬í sr (net.sf.jasperreports.engine.JasperReport      'Ø L compileDatat Ljava/io/Serializable;L compileNameSuffixt Ljava/lang/String;L 
compilerClassq ~ xr -net.sf.jasperreports.engine.base.JRBaseReport      'Ø +I PSEUDO_SERIAL_VERSION_UIDI bottomMarginI columnCountI 
columnSpacingI columnWidthZ ignorePaginationZ isFloatColumnFooterZ isSummaryNewPageZ  isSummaryWithPageHeaderAndFooterZ isTitleNewPageI 
leftMarginB orientationI 
pageHeightI 	pageWidthB 
printOrderI rightMarginI 	topMarginB whenNoDataTypeL 
backgroundt $Lnet/sf/jasperreports/engine/JRBand;L columnFooterq ~ L columnHeaderq ~ [ datasetst ([Lnet/sf/jasperreports/engine/JRDataset;L defaultFontt *Lnet/sf/jasperreports/engine/JRReportFont;L defaultStylet %Lnet/sf/jasperreports/engine/JRStyle;L detailq ~ L 
detailSectiont 'Lnet/sf/jasperreports/engine/JRSection;[ fontst +[Lnet/sf/jasperreports/engine/JRReportFont;L formatFactoryClassq ~ L 
importsSett Ljava/util/Set;L languageq ~ L lastPageFooterq ~ L mainDatasett 'Lnet/sf/jasperreports/engine/JRDataset;L nameq ~ L noDataq ~ L orientationValuet 2Lnet/sf/jasperreports/engine/type/OrientationEnum;L 
pageFooterq ~ L 
pageHeaderq ~ L printOrderValuet 1Lnet/sf/jasperreports/engine/type/PrintOrderEnum;[ stylest &[Lnet/sf/jasperreports/engine/JRStyle;L summaryq ~ [ 	templatest /[Lnet/sf/jasperreports/engine/JRReportTemplate;L titleq ~ L whenNoDataTypeValuet 5Lnet/sf/jasperreports/engine/type/WhenNoDataTypeEnum;xp  wî             ?        
   J  S    
     sr +net.sf.jasperreports.engine.base.JRBaseBand      'Ø I PSEUDO_SERIAL_VERSION_UIDI heightZ isSplitAllowedL printWhenExpressiont *Lnet/sf/jasperreports/engine/JRExpression;L 	splitTypet Ljava/lang/Byte;L splitTypeValuet 0Lnet/sf/jasperreports/engine/type/SplitTypeEnum;xr 3net.sf.jasperreports.engine.base.JRBaseElementGroup      'Ø L childrent Ljava/util/List;L elementGroupt ,Lnet/sf/jasperreports/engine/JRElementGroup;xpsr java.util.ArrayListxÒÇa I sizexp    w    xp  wî    pp~r .net.sf.jasperreports.engine.type.SplitTypeEnum          xr java.lang.Enum          xpt STRETCHppppppsr .net.sf.jasperreports.engine.base.JRBaseSection      'Ø [ bandst %[Lnet/sf/jasperreports/engine/JRBand;xpur %[Lnet.sf.jasperreports.engine.JRBand;Ý~ìÊ5  xp   sq ~ sq ~    gw   gsr +net.sf.jasperreports.engine.base.JRBaseLine      'Ø I PSEUDO_SERIAL_VERSION_UIDB 	directionL directionValuet 4Lnet/sf/jasperreports/engine/type/LineDirectionEnum;xr 5net.sf.jasperreports.engine.base.JRBaseGraphicElement      'Ø I PSEUDO_SERIAL_VERSION_UIDL fillq ~ L 	fillValuet +Lnet/sf/jasperreports/engine/type/FillEnum;L linePent #Lnet/sf/jasperreports/engine/JRPen;L penq ~ xr .net.sf.jasperreports.engine.base.JRBaseElement      'Ø I PSEUDO_SERIAL_VERSION_UIDI heightZ isPrintInFirstWholeBandZ isPrintRepeatedValuesZ isPrintWhenDetailOverflowsZ isRemoveLineWhenBlankB positionTypeB stretchTypeI widthI xI yL 	backcolort Ljava/awt/Color;L defaultStyleProvidert 4Lnet/sf/jasperreports/engine/JRDefaultStyleProvider;L elementGroupq ~ L 	forecolorq ~ -L keyq ~ L modeq ~ L 	modeValuet +Lnet/sf/jasperreports/engine/type/ModeEnum;L parentStyleq ~ L parentStyleNameReferenceq ~ L positionTypeValuet 3Lnet/sf/jasperreports/engine/type/PositionTypeEnum;L printWhenExpressionq ~ L printWhenGroupChangest %Lnet/sf/jasperreports/engine/JRGroup;L 
propertiesMapt -Lnet/sf/jasperreports/engine/JRPropertiesMap;[ propertyExpressionst 3[Lnet/sf/jasperreports/engine/JRPropertyExpression;L stretchTypeValuet 2Lnet/sf/jasperreports/engine/type/StretchTypeEnum;xp  wî           l       pq ~ q ~ %pppppp~r 1net.sf.jasperreports.engine.type.PositionTypeEnum          xq ~ t FIX_RELATIVE_TO_TOPpppp~r 0net.sf.jasperreports.engine.type.StretchTypeEnum          xq ~ t 
NO_STRETCH  wîppsr *net.sf.jasperreports.engine.base.JRBasePen      'Ø I PSEUDO_SERIAL_VERSION_UIDL 	lineColorq ~ -L 	lineStyleq ~ L lineStyleValuet 0Lnet/sf/jasperreports/engine/type/LineStyleEnum;L 	lineWidtht Ljava/lang/Float;L penContainert ,Lnet/sf/jasperreports/engine/JRPenContainer;xp  wîpppsr java.lang.FloatÚíÉ¢Û<ðì F valuexr java.lang.Number¬à  xp@   q ~ 5p  wî ~r 2net.sf.jasperreports.engine.type.LineDirectionEnum          xq ~ t TOP_DOWNsq ~ '  wî          Ì   s   pq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppsq ~ <  wîpppsq ~ A?q ~ Gp  wî q ~ Esq ~ '  wî              7   pq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppsq ~ <  wîpppsq ~ A@   q ~ Jp  wî q ~ Esq ~ '  wî              Ø   pq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppsq ~ <  wîpppsq ~ A@   q ~ Mp  wî q ~ Esq ~ '  wî          Ë   t   &pq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppsq ~ <  wîpppsq ~ A?  q ~ Pp  wî q ~ Esq ~ '  wî   ²          ½   pq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppsq ~ <  wîpppsq ~ A?   q ~ Sp  wî q ~ Esr 1net.sf.jasperreports.engine.base.JRBaseStaticText      'Ø L textq ~ xr 2net.sf.jasperreports.engine.base.JRBaseTextElement      'Ø %I PSEUDO_SERIAL_VERSION_UIDL borderq ~ L borderColorq ~ -L bottomBorderq ~ L bottomBorderColorq ~ -L 
bottomPaddingt Ljava/lang/Integer;L fontNameq ~ L fontSizeq ~ XL horizontalAlignmentq ~ L horizontalAlignmentValuet 6Lnet/sf/jasperreports/engine/type/HorizontalAlignEnum;L isBoldt Ljava/lang/Boolean;L isItalicq ~ ZL 
isPdfEmbeddedq ~ ZL isStrikeThroughq ~ ZL isStyledTextq ~ ZL isUnderlineq ~ ZL 
leftBorderq ~ L leftBorderColorq ~ -L leftPaddingq ~ XL lineBoxt 'Lnet/sf/jasperreports/engine/JRLineBox;L lineSpacingq ~ L lineSpacingValuet 2Lnet/sf/jasperreports/engine/type/LineSpacingEnum;L markupq ~ L paddingq ~ XL pdfEncodingq ~ L pdfFontNameq ~ L 
reportFontq ~ L rightBorderq ~ L rightBorderColorq ~ -L rightPaddingq ~ XL rotationq ~ L 
rotationValuet /Lnet/sf/jasperreports/engine/type/RotationEnum;L 	topBorderq ~ L topBorderColorq ~ -L 
topPaddingq ~ XL verticalAlignmentq ~ L verticalAlignmentValuet 4Lnet/sf/jasperreports/engine/type/VerticalAlignEnum;xq ~ ,  wî          C   v   pq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppppppsr java.lang.Integerâ ¤÷8 I valuexq ~ B   ppsr java.lang.BooleanÍ rÕúî Z valuexp ppppppppsr .net.sf.jasperreports.engine.base.JRBaseLineBox      'Ø L 
bottomPaddingq ~ XL 	bottomPent +Lnet/sf/jasperreports/engine/base/JRBoxPen;L boxContainert ,Lnet/sf/jasperreports/engine/JRBoxContainer;L leftPaddingq ~ XL leftPenq ~ eL paddingq ~ XL penq ~ eL rightPaddingq ~ XL rightPenq ~ eL 
topPaddingq ~ XL topPenq ~ exppsr 3net.sf.jasperreports.engine.base.JRBaseBoxBottomPen      'Ø  xr -net.sf.jasperreports.engine.base.JRBaseBoxPen      'Ø L lineBoxq ~ [xq ~ <  wîppppq ~ gq ~ gq ~ _psr 1net.sf.jasperreports.engine.base.JRBaseBoxLeftPen      'Ø  xq ~ i  wîppppq ~ gq ~ gpsq ~ i  wîppppq ~ gq ~ gpsr 2net.sf.jasperreports.engine.base.JRBaseBoxRightPen      'Ø  xq ~ i  wîppppq ~ gq ~ gpsr 0net.sf.jasperreports.engine.base.JRBaseBoxTopPen      'Ø  xq ~ i  wîppppq ~ gq ~ gpppppppppppppppppt LOCAL DO PAGAMENTOsq ~ V  wî           {  À   pq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppppppq ~ apppppppppppsq ~ dpsq ~ h  wîppppq ~ tq ~ tq ~ spsq ~ k  wîppppq ~ tq ~ tpsq ~ i  wîppppq ~ tq ~ tpsq ~ n  wîppppq ~ tq ~ tpsq ~ p  wîppppq ~ tq ~ tpppppppppppppppppt 
VENCIMENTOsq ~ '  wî          Ì   s   :pq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppsq ~ <  wîpppsq ~ A?  q ~ {p  wî q ~ Esq ~ V  wî          C   v   (pq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppppppq ~ apppppppppppsq ~ dpsq ~ h  wîppppq ~ q ~ q ~ ~psq ~ k  wîppppq ~ q ~ psq ~ i  wîppppq ~ q ~ psq ~ n  wîppppq ~ q ~ psq ~ p  wîppppq ~ q ~ pppppppppppppppppt (NOME DO BENEFICIÃRIO/CPF/CNPJ/ENDEREÃOsq ~ V  wî           {  À   (pq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppppppq ~ apppppppppppsq ~ dpsq ~ h  wîppppq ~ q ~ q ~ psq ~ k  wîppppq ~ q ~ psq ~ i  wîppppq ~ q ~ psq ~ n  wîppppq ~ q ~ psq ~ p  wîppppq ~ q ~ pppppppppppppppppt #AGÃNCIA / CÃDIGO DO BENEFICIÃRIOsq ~ '  wî          Ì   s   Npq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppsq ~ <  wîpppsq ~ A?   q ~ p  wî q ~ Esq ~ V  wî           @   v   <pq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppppppq ~ apppppppppppsq ~ dpsq ~ h  wîppppq ~ q ~ q ~ psq ~ k  wîppppq ~ q ~ psq ~ i  wîppppq ~ q ~ psq ~ n  wîppppq ~ q ~ psq ~ p  wîppppq ~ q ~ pppppppppppppppppt DATA DO DOCUMENTOsq ~ V  wî           A   ¸   <pq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppppppq ~ apppppppppppsq ~ dpsq ~ h  wîppppq ~ q ~ q ~ psq ~ k  wîppppq ~ q ~ psq ~ i  wîppppq ~ q ~ psq ~ n  wîppppq ~ q ~ psq ~ p  wîppppq ~ q ~ pppppppppppppppppt N. DO DOCUMENTOsq ~ V  wî           $   û   <pq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppppppq ~ apppppppppppsq ~ dpsq ~ h  wîppppq ~ ¢q ~ ¢q ~ ¡psq ~ k  wîppppq ~ ¢q ~ ¢psq ~ i  wîppppq ~ ¢q ~ ¢psq ~ n  wîppppq ~ ¢q ~ ¢psq ~ p  wîppppq ~ ¢q ~ ¢pppppppppppppppppt ESPÃCIEsq ~ V  wî           '  "   <pq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppppppq ~ apppppppppppsq ~ dpsq ~ h  wîppppq ~ ªq ~ ªq ~ ©psq ~ k  wîppppq ~ ªq ~ ªpsq ~ i  wîppppq ~ ªq ~ ªpsq ~ n  wîppppq ~ ªq ~ ªpsq ~ p  wîppppq ~ ªq ~ ªpppppppppppppppppt ACEITEsq ~ V  wî           n  K   <pq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppppppq ~ apppppppppppsq ~ dpsq ~ h  wîppppq ~ ²q ~ ²q ~ ±psq ~ k  wîppppq ~ ²q ~ ²psq ~ i  wîppppq ~ ²q ~ ²psq ~ n  wîppppq ~ ²q ~ ²psq ~ p  wîppppq ~ ²q ~ ²pppppppppppppppppt DATA PROCESSAMENTOsq ~ '  wî          Ì   s   cpq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppsq ~ <  wîpppsq ~ A?   q ~ ¹p  wî q ~ Esq ~ V  wî           )   u   Ppq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppppppq ~ apppppppppppsq ~ dpsq ~ h  wîppppq ~ ½q ~ ½q ~ ¼psq ~ k  wîppppq ~ ½q ~ ½psq ~ i  wîppppq ~ ½q ~ ½psq ~ n  wîppppq ~ ½q ~ ½psq ~ p  wîppppq ~ ½q ~ ½pppppppppppppppppt USO DO BANCOsq ~ V  wî              ¸   Ppq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppppppq ~ apppppppppppsq ~ dpsq ~ h  wîppppq ~ Åq ~ Åq ~ Äpsq ~ k  wîppppq ~ Åq ~ Åpsq ~ i  wîppppq ~ Åq ~ Åpsq ~ n  wîppppq ~ Åq ~ Åpsq ~ p  wîppppq ~ Åq ~ Åpppppppppppppppppt CARTEIRAsq ~ V  wî              Û   Ppq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppppppq ~ apppppppppppsq ~ dpsq ~ h  wîppppq ~ Íq ~ Íq ~ Ìpsq ~ k  wîppppq ~ Íq ~ Ípsq ~ i  wîppppq ~ Íq ~ Ípsq ~ n  wîppppq ~ Íq ~ Ípsq ~ p  wîppppq ~ Íq ~ Ípppppppppppppppppt MOEDAsq ~ V  wî           N   û   Ppq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppppppq ~ apppppppppppsq ~ dpsq ~ h  wîppppq ~ Õq ~ Õq ~ Ôpsq ~ k  wîppppq ~ Õq ~ Õpsq ~ i  wîppppq ~ Õq ~ Õpsq ~ n  wîppppq ~ Õq ~ Õpsq ~ p  wîppppq ~ Õq ~ Õpppppppppppppppppt 
QUANTIDADEsq ~ V  wî           n  K   Qpq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppppppq ~ apppppppppppsq ~ dpsq ~ h  wîppppq ~ Ýq ~ Ýq ~ Üpsq ~ k  wîppppq ~ Ýq ~ Ýpsq ~ i  wîppppq ~ Ýq ~ Ýpsq ~ n  wîppppq ~ Ýq ~ Ýpsq ~ p  wîppppq ~ Ýq ~ Ýpppppppppppppppppt VALORsq ~ '  wî          Ì   s   Åpq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppsq ~ <  wîpppsq ~ A?  q ~ äp  wî q ~ Esq ~ V  wî           Õ   v   epq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppppppq ~ apppppppppppsq ~ dpsq ~ h  wîppppq ~ èq ~ èq ~ çpsq ~ k  wîppppq ~ èq ~ èpsq ~ i  wîppppq ~ èq ~ èpsq ~ n  wîppppq ~ èq ~ èpsq ~ p  wîppppq ~ èq ~ èpppppppppppppppppt 2INFORMAÃÃES DE RESPONSABILIDADE DO BENEFICIÃRIOsq ~ V  wî           {  À   <pq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppppppq ~ apppppppppppsq ~ dpsq ~ h  wîppppq ~ ðq ~ ðq ~ ïpsq ~ k  wîppppq ~ ðq ~ ðpsq ~ i  wîppppq ~ ðq ~ ðpsq ~ n  wîppppq ~ ðq ~ ðpsq ~ p  wîppppq ~ ðq ~ ðpppppppppppppppppt 
NOSSO NÃMEROsq ~ V  wî           {  À   Ppq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppppppq ~ apppppppppppsq ~ dpsq ~ h  wîppppq ~ øq ~ øq ~ ÷psq ~ k  wîppppq ~ øq ~ øpsq ~ i  wîppppq ~ øq ~ øpsq ~ n  wîppppq ~ øq ~ øpsq ~ p  wîppppq ~ øq ~ øpppppppppppppppppt (=) VALOR DO DOCUMENTOsq ~ V  wî           ,   v   pq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppppppq ~ apppppppppppsq ~ dpsq ~ h  wîppppq ~ q ~ q ~ ÿpsq ~ k  wîppppq ~ q ~ psq ~ i  wîppppq ~ q ~ psq ~ n  wîppppq ~ q ~ psq ~ p  wîppppq ~ q ~ pppppppppppppppppt PAGADORsq ~ '  wî                pq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppsq ~ <  wîpppsq ~ A@   q ~p  wî q ~ Esq ~ '  wî   ³           s   pq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppsq ~ <  wîppppq ~
p  wî q ~ Esq ~ '  wî   ³          ?   pq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppsq ~ <  wîppppq ~p  wî q ~ Esq ~ '  wî             ¾   vpq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppsq ~ <  wîppppq ~p  wî q ~ Esq ~ '  wî             ¾   ±pq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppsq ~ <  wîppppq ~p  wî q ~ Esq ~ '  wî             ¾   pq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppsq ~ <  wîppppq ~p  wî q ~ Esq ~ V  wî           {  À   epq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppppppq ~ apppppppppppsq ~ dpsq ~ h  wîppppq ~q ~q ~psq ~ k  wîppppq ~q ~psq ~ i  wîppppq ~q ~psq ~ n  wîppppq ~q ~psq ~ p  wîppppq ~q ~pppppppppppppppppt (-) DESCONTO/ABATIMENTOsq ~ V  wî           {  À   pq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppppppq ~ apppppppppppsq ~ dpsq ~ h  wîppppq ~q ~q ~psq ~ k  wîppppq ~q ~psq ~ i  wîppppq ~q ~psq ~ n  wîppppq ~q ~psq ~ p  wîppppq ~q ~pppppppppppppppppt (+) JUROS/MULTAsq ~ V  wî           {  À    pq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppppppq ~ apppppppppppsq ~ dpsq ~ h  wîppppq ~%q ~%q ~$psq ~ k  wîppppq ~%q ~%psq ~ i  wîppppq ~%q ~%psq ~ n  wîppppq ~%q ~%psq ~ p  wîppppq ~%q ~%pppppppppppppppppt (+) OUTROS ACRÃSCIMOSsq ~ V  wî           {  À   ²pq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppppppq ~ apppppppppppsq ~ dpsq ~ h  wîppppq ~-q ~-q ~,psq ~ k  wîppppq ~-q ~-psq ~ i  wîppppq ~-q ~-psq ~ n  wîppppq ~-q ~-psq ~ p  wîppppq ~-q ~-pppppppppppppppppt (=) VALOR COBRADOsq ~ '  wî   ø           l   pq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppsq ~ <  wîppppq ~4p  wî q ~ Esq ~ '  wî   é               pq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppsq ~ <  wîppppq ~6p  wî q ~ Esq ~ '  wî           m       ûpq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppsq ~ <  wîppppq ~8p  wî q ~ Esq ~ '  wî             o    pq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppsq ~ <  wîpp~r .net.sf.jasperreports.engine.type.LineStyleEnum          xq ~ t DASHEDsq ~ A?   q ~:p  wî q ~ Esq ~ '  wî           k      &pq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppsq ~ <  wîppppq ~@p  wî q ~ Esq ~ '  wî           k      9pq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppsq ~ <  wîppppq ~Bp  wî q ~ Esq ~ '  wî           l       upq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppsq ~ <  wîppppq ~Dp  wî q ~ Esq ~ '  wî           k      pq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppsq ~ <  wîppppq ~Fp  wî q ~ Esq ~ '  wî           k      pq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppsq ~ <  wîppppq ~Hp  wî q ~ Esq ~ '  wî           k      °pq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppsq ~ <  wîppppq ~Jp  wî q ~ Esq ~ '  wî           k      Åpq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppsq ~ <  wîppppq ~Lp  wî q ~ Esq ~ '  wî           k      Øpq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppsq ~ <  wîppppq ~Np  wî q ~ Esq ~ '  wî           k      Mpq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppsq ~ <  wîppppq ~Pp  wî q ~ Esq ~ '  wî           k      apq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppsq ~ <  wîppppq ~Rp  wî q ~ Esq ~ V  wî           h      pq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppppppq ~ apppppppppppsq ~ dpsq ~ h  wîppppq ~Uq ~Uq ~Tpsq ~ k  wîppppq ~Uq ~Upsq ~ i  wîppppq ~Uq ~Upsq ~ n  wîppppq ~Uq ~Upsq ~ p  wîppppq ~Uq ~Upppppppppppppppppt 
VENCIMENTOsq ~ V  wî           i      <pq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppppppq ~ apppppppppppsq ~ dpsq ~ h  wîppppq ~]q ~]q ~\psq ~ k  wîppppq ~]q ~]psq ~ i  wîppppq ~]q ~]psq ~ n  wîppppq ~]q ~]psq ~ p  wîppppq ~]q ~]pppppppppppppppppt 
NOSSO NÃMEROsq ~ V  wî           i      (pq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppppppq ~ apppppppppppsq ~ dpsq ~ h  wîppppq ~eq ~eq ~dpsq ~ k  wîppppq ~eq ~epsq ~ i  wîppppq ~eq ~epsq ~ n  wîppppq ~eq ~epsq ~ p  wîppppq ~eq ~epppppppppppppppppt #AGÃNCIA / CÃDIGO DO BENEFICIÃRIOsq ~ V  wî           i       pq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppppppq ~ apppppppppppsq ~ dpsq ~ h  wîppppq ~mq ~mq ~lpsq ~ k  wîppppq ~mq ~mpsq ~ i  wîppppq ~mq ~mpsq ~ n  wîppppq ~mq ~mpsq ~ p  wîppppq ~mq ~mpppppppppppppppppt (+) OUTROS ACRÃSCIMOSsq ~ V  wî           i      Ppq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppppppq ~ apppppppppppsq ~ dpsq ~ h  wîppppq ~uq ~uq ~tpsq ~ k  wîppppq ~uq ~upsq ~ i  wîppppq ~uq ~upsq ~ n  wîppppq ~uq ~upsq ~ p  wîppppq ~uq ~upppppppppppppppppt (=) VALOR DO DOCUMENTOsq ~ V  wî           i      ²pq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppppppq ~ apppppppppppsq ~ dpsq ~ h  wîppppq ~}q ~}q ~|psq ~ k  wîppppq ~}q ~}psq ~ i  wîppppq ~}q ~}psq ~ n  wîppppq ~}q ~}psq ~ p  wîppppq ~}q ~}pppppppppppppppppt (=) VALOR COBRADOsq ~ V  wî           i      pq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppppppq ~ apppppppppppsq ~ dpsq ~ h  wîppppq ~q ~q ~psq ~ k  wîppppq ~q ~psq ~ i  wîppppq ~q ~psq ~ n  wîppppq ~q ~psq ~ p  wîppppq ~q ~pppppppppppppppppt (+) JUROS/MULTAsq ~ V  wî           i      epq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppppppq ~ apppppppppppsq ~ dpsq ~ h  wîppppq ~q ~q ~psq ~ k  wîppppq ~q ~psq ~ i  wîppppq ~q ~psq ~ n  wîppppq ~q ~psq ~ p  wîppppq ~q ~pppppppppppppppppt (-) DESCONTO/ABATIMENTOsq ~ V  wî           i      Çpq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppppppq ~ apppppppppppsq ~ dpsq ~ h  wîppppq ~q ~q ~psq ~ k  wîppppq ~q ~psq ~ i  wîppppq ~q ~psq ~ n  wîppppq ~q ~psq ~ p  wîppppq ~q ~pppppppppppppppppt NÃMERO DO DOC.sq ~ V  wî           i      Ûpq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppppppq ~ apppppppppppsq ~ dpsq ~ h  wîppppq ~q ~q ~psq ~ k  wîppppq ~q ~psq ~ i  wîppppq ~q ~psq ~ n  wîppppq ~q ~psq ~ p  wîppppq ~q ~pppppppppppppppppt PAGADORsq ~ '  wî              ¶   ;pq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppsq ~ <  wîppppq ~¤p  wî q ~ Esq ~ '  wî   (           ù   ;pq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppsq ~ <  wîppppq ~¦p  wî q ~ Esq ~ '  wî                 ;pq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppsq ~ <  wîppppq ~¨p  wî q ~ Esq ~ '  wî   (          I   ;pq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppsq ~ <  wîppppq ~ªp  wî q ~ Esq ~ '  wî              ¶   Opq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppsq ~ <  wîppppq ~¬p  wî q ~ Esq ~ '  wî              Ù   Opq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppsq ~ <  wîppppq ~®p  wî q ~ Esq ~ '  wî          ?       pq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppsq ~ <  wîppq ~=sq ~ A?   q ~°p  wî q ~ Esr 0net.sf.jasperreports.engine.base.JRBaseTextField      'Ø I PSEUDO_SERIAL_VERSION_UIDI 
bookmarkLevelB evaluationTimeB hyperlinkTargetB 
hyperlinkTypeZ isStretchWithOverflowL anchorNameExpressionq ~ L evaluationGroupq ~ 1L evaluationTimeValuet 5Lnet/sf/jasperreports/engine/type/EvaluationTimeEnum;L 
expressionq ~ L hyperlinkAnchorExpressionq ~ L hyperlinkPageExpressionq ~ [ hyperlinkParameterst 3[Lnet/sf/jasperreports/engine/JRHyperlinkParameter;L hyperlinkReferenceExpressionq ~ L hyperlinkTooltipExpressionq ~ L isBlankWhenNullq ~ ZL 
linkTargetq ~ L linkTypeq ~ L patternq ~ xq ~ W  wî         9     pq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppppppsq ~ `   ppsq ~ bppppppppsq ~ dpsq ~ h  wîppppq ~¹q ~¹q ~¶psq ~ k  wîppppq ~¹q ~¹psq ~ i  wîppppq ~¹q ~¹psq ~ n  wîppppq ~¹q ~¹psq ~ p  wîppppq ~¹q ~¹pppppppppppppppp~r 2net.sf.jasperreports.engine.type.VerticalAlignEnum          xq ~ t MIDDLE  wî        pp~r 3net.sf.jasperreports.engine.type.EvaluationTimeEnum          xq ~ t NOWsr 1net.sf.jasperreports.engine.base.JRBaseExpression      'Ø I id[ chunkst 0[Lnet/sf/jasperreports/engine/JRExpressionChunk;L valueClassNameq ~ L valueClassRealNameq ~ xp   ur 0[Lnet.sf.jasperreports.engine.JRExpressionChunk;mYÏÞiK£U  xp   sr 6net.sf.jasperreports.engine.base.JRBaseExpressionChunk      'Ø B typeL textq ~ xpt banco.linhaDigitavelt java.lang.Stringppppppppppsq ~³  wî   
        {  À   Dpq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppppppsq ~ `   p~r 4net.sf.jasperreports.engine.type.HorizontalAlignEnum          xq ~ t RIGHTpppppppppsq ~ dpsq ~ h  wîppppq ~Óq ~Óq ~Îpsq ~ k  wîppppq ~Óq ~Ópsq ~ i  wîppppq ~Óq ~Ópsq ~ n  wîppppq ~Óq ~Ópsq ~ p  wîppppq ~Óq ~Óppppppppppppppppp  wî        ppq ~Ãsq ~Å   	uq ~È   sq ~Êt banco.nossoNumeroFormattedt java.lang.Stringppppppppppsq ~³  wî   
        i      Dpq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppppppq ~Ïpppppppppppsq ~ dpsq ~ h  wîppppq ~ßq ~ßq ~Þpsq ~ k  wîppppq ~ßq ~ßpsq ~ i  wîppppq ~ßq ~ßpsq ~ n  wîppppq ~ßq ~ßpsq ~ p  wîppppq ~ßq ~ßppppppppppppppppp  wî        ppq ~Ãsq ~Å   
uq ~È   sq ~Êt banco.nossoNumeroFormattedt java.lang.Stringppppppppppsq ~³  wî   
       C   v   1pq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppppppq ~Ïpppppppppppsq ~ dpsq ~ h  wîppppq ~ëq ~ëq ~êpsq ~ k  wîppppq ~ëq ~ëpsq ~ i  wîppppq ~ëq ~ëpsq ~ n  wîppppq ~ëq ~ëpsq ~ p  wîppppq ~ëq ~ëppppppppppppppppp  wî        ppq ~Ãsq ~Å   uq ~È   sq ~Êt boleto.cedentet java.lang.Stringppppppppppsq ~³  wî   
        {  À   Ypq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppppppq ~Ïpq ~Ñpppppppppsq ~ dpsq ~ h  wîppppq ~÷q ~÷q ~öpsq ~ k  wîppppq ~÷q ~÷psq ~ i  wîppppq ~÷q ~÷psq ~ n  wîppppq ~÷q ~÷psq ~ p  wîppppq ~÷q ~÷ppppppppppppppppp  wî        ppq ~Ãsq ~Å   uq ~È   sq ~Êt boleto.valorBoletot java.lang.Stringppppppppppsq ~³  wî   
            ·   Ypq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppppppq ~Ïp~q ~Ðt CENTERpppppppppsq ~ dpsq ~ h  wîppppq ~q ~q ~psq ~ k  wîppppq ~q ~psq ~ i  wîppppq ~q ~psq ~ n  wîppppq ~q ~psq ~ p  wîppppq ~q ~ppppppppppppppppp  wî        ppq ~Ãsq ~Å   
uq ~È   sq ~Êt boleto.carteirat java.lang.Stringppppppppppsq ~³  wî   
        n  K   Ypq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppppppq ~Ïpq ~Ñpppppppppsq ~ dpsq ~ h  wîppppq ~q ~q ~psq ~ k  wîppppq ~q ~psq ~ i  wîppppq ~q ~psq ~ n  wîppppq ~q ~psq ~ p  wîppppq ~q ~ppppppppppppppppp  wî        ppq ~Ãsq ~Å   uq ~È   sq ~Êt boleto.valorBoletot java.lang.Stringppppppppppsq ~³  wî   
       C   v   §pq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppppppq ~Ïpppppppppppsq ~ dpsq ~ h  wîppppq ~q ~q ~psq ~ k  wîppppq ~q ~psq ~ i  wîppppq ~q ~psq ~ n  wîppppq ~q ~psq ~ p  wîppppq ~q ~ppppppppppppppppp  wî        ppq ~Ãsq ~Å   uq ~È   
sq ~Êt ((sq ~Êt boleto.responsavelsq ~Êt  != null &&  !sq ~Êt boleto.responsavelsq ~Êt .isEmpty()) ? sq ~Êt boleto.responsavelsq ~Êt  :  sq ~Êt boleto.nomeSacadosq ~Êt ) + " CPF/CNPJ: " + sq ~Êt boleto.cpfSacadot java.lang.Stringppppppppppsq ~³  wî   
       C   v   °pq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppppppq ~Ïpppppppppppsq ~ dpsq ~ h  wîppppq ~;q ~;q ~:psq ~ k  wîppppq ~;q ~;psq ~ i  wîppppq ~;q ~;psq ~ n  wîppppq ~;q ~;psq ~ p  wîppppq ~;q ~;ppppppppppppppppp  wî        ppq ~Ãsq ~Å   uq ~È   sq ~Êt boleto.enderecoSacadot java.lang.Stringppppppppppsq ~³  wî   
        $   ú   Epq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppppppq ~Ïpq ~pppppppppsq ~ dpsq ~ h  wîppppq ~Gq ~Gq ~Fpsq ~ k  wîppppq ~Gq ~Gpsq ~ i  wîppppq ~Gq ~Gpsq ~ n  wîppppq ~Gq ~Gpsq ~ p  wîppppq ~Gq ~Gppppppppppppppppp  wî        ppq ~Ãsq ~Å   uq ~È   sq ~Êt boleto.especieDocumentot java.lang.Stringppppppppppsq ~ V  wî           {  À   Épq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppppppq ~Ïppq ~¸ppppppppsq ~ dpsq ~ h  wîppppq ~Sq ~Sq ~Rpsq ~ k  wîppppq ~Sq ~Spsq ~ i  wîppppq ~Sq ~Spsq ~ n  wîppppq ~Sq ~Spsq ~ p  wîppppq ~Sq ~Spppppppppppppppppt "AutenticaÃ§Ã£o MecÃ¢nica-Ficha de sq ~³  wî   
        i      Xpq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppppppq ~Ïpppppppppppsq ~ dpsq ~ h  wîppppq ~[q ~[q ~Zpsq ~ k  wîppppq ~[q ~[psq ~ i  wîppppq ~[q ~[psq ~ n  wîppppq ~[q ~[psq ~ p  wîppppq ~[q ~[ppppppppppppppppp  wî        ppq ~Ãsq ~Å   uq ~È   sq ~Êt boleto.valorBoletot java.lang.Stringppppppppppsq ~³  wî           i      âpq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppppppq ~Ïpppppppppppsq ~ dpsq ~ h  wîppppq ~gq ~gq ~fpsq ~ k  wîppppq ~gq ~gpsq ~ i  wîppppq ~gq ~gpsq ~ n  wîppppq ~gq ~gpsq ~ p  wîppppq ~gq ~gppppppppppppppppp  wî        ppq ~Ãsq ~Å   uq ~È   sq ~Êt (sq ~Êt boleto.responsavelsq ~Êt  != null &&  !sq ~Êt boleto.responsavelsq ~Êt .isEmpty()) ? sq ~Êt boleto.responsavelsq ~Êt  :  sq ~Êt boleto.nomeSacadot java.lang.Stringppppppppppsq ~³  wî   
        {  À   pq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppppppq ~Ïpq ~Ñpppppppppsq ~ dpsq ~ h  wîppppq ~q ~q ~psq ~ k  wîppppq ~q ~psq ~ i  wîppppq ~q ~psq ~ n  wîppppq ~q ~psq ~ p  wîppppq ~q ~ppppppppppppppppp  wî        ppq ~Ãsq ~Å   uq ~È   sq ~Êt boleto.dataVencimentot java.lang.Stringppppppppppsq ~³  wî   
        i      pq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppppppq ~Ïpppppppppppsq ~ dpsq ~ h  wîppppq ~q ~q ~psq ~ k  wîppppq ~q ~psq ~ i  wîppppq ~q ~psq ~ n  wîppppq ~q ~psq ~ p  wîppppq ~q ~ppppppppppppppppp  wî        ppq ~Ãsq ~Å   uq ~È   sq ~Êt boleto.dataVencimentot java.lang.Stringppppppppppsq ~ '  wî          Ì   t   pq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppsq ~ <  wîppppq ~p  wî q ~ Esq ~³  wî   
        $   Û   pq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppppppppppppppppppsq ~ dpsq ~ h  wîppppq ~q ~q ~psq ~ k  wîppppq ~q ~psq ~ i  wîppppq ~q ~psq ~ n  wîppppq ~q ~psq ~ p  wîppppq ~q ~ppppppppppppppppp  wî        ppq ~Ãsq ~Å   uq ~È   sq ~Êt banco.numeroFormattedt java.lang.Stringppppppppppsq ~³  wî           1   ;   pq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppppppppppppppppppsq ~ dpsq ~ h  wîppppq ~§q ~§q ~¦psq ~ k  wîppppq ~§q ~§psq ~ i  wîppppq ~§q ~§psq ~ n  wîppppq ~§q ~§psq ~ p  wîppppq ~§q ~§ppppppppppppppppp  wî        ppq ~Ãsq ~Å   uq ~È   sq ~Êt banco.numeroFormattedt java.lang.Stringppppppppppsq ~³  wî   0       C   v   mpq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppppppq ~Ïpppppppppppsq ~ dpsq ~ h  wîppppq ~³q ~³q ~²psq ~ k  wîppppq ~³q ~³psq ~ i  wîppppq ~³q ~³psq ~ n  wîppppq ~³q ~³psq ~ p  wîppppq ~³q ~³ppppppppppppppppp  wî        ppq ~Ãsq ~Å   uq ~È   sq ~Êt boleto.instrucao1t java.lang.Stringppppppppppsq ~³  wî           5      pq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppppppppppppppppppsq ~ dpsq ~ h  wîppppq ~¿q ~¿q ~¾psq ~ k  wîppppq ~¿q ~¿psq ~ i  wîppppq ~¿q ~¿psq ~ n  wîppppq ~¿q ~¿psq ~ p  wîppppq ~¿q ~¿ppppppppppppppppp  wî        ppq ~Ãsq ~Å   uq ~È   sq ~Êt banco.bancot java.lang.Stringppppppppppsr ,net.sf.jasperreports.engine.base.JRBaseImage      'Ø *I PSEUDO_SERIAL_VERSION_UIDI 
bookmarkLevelB evaluationTimeB hyperlinkTargetB 
hyperlinkTypeZ isLazyB onErrorTypeL anchorNameExpressionq ~ L borderq ~ L borderColorq ~ -L bottomBorderq ~ L bottomBorderColorq ~ -L 
bottomPaddingq ~ XL evaluationGroupq ~ 1L evaluationTimeValueq ~´L 
expressionq ~ L horizontalAlignmentq ~ L horizontalAlignmentValueq ~ YL hyperlinkAnchorExpressionq ~ L hyperlinkPageExpressionq ~ [ hyperlinkParametersq ~µL hyperlinkReferenceExpressionq ~ L hyperlinkTooltipExpressionq ~ L isUsingCacheq ~ ZL 
leftBorderq ~ L leftBorderColorq ~ -L leftPaddingq ~ XL lineBoxq ~ [L 
linkTargetq ~ L linkTypeq ~ L onErrorTypeValuet 2Lnet/sf/jasperreports/engine/type/OnErrorTypeEnum;L paddingq ~ XL rightBorderq ~ L rightBorderColorq ~ -L rightPaddingq ~ XL 
scaleImageq ~ L scaleImageValuet 1Lnet/sf/jasperreports/engine/type/ScaleImageEnum;L 	topBorderq ~ L topBorderColorq ~ -L 
topPaddingq ~ XL verticalAlignmentq ~ L verticalAlignmentValueq ~ ^xq ~ )  wî   #       ?   v   Ösr java.awt.Color¥3u F falphaI valueL cst Ljava/awt/color/ColorSpace;[ 	frgbvaluet [F[ fvalueq ~Ðxp    ÿÿÿÿpppq ~ q ~ %sq ~Î    ÿ   pppt 	barcode-1p~r )net.sf.jasperreports.engine.type.ModeEnum          xq ~ t OPAQUEppq ~ 7ppppq ~ :  wîp~r )net.sf.jasperreports.engine.type.FillEnum          xq ~ t SOLIDsq ~ <  wîpp~q ~<t SOLIDsq ~ A    q ~Íp  wî         pppppppq ~Ãsq ~Å   uq ~È   sq ~Êt <it.businesslogic.ireport.barcode.BcImage.getBarcodeImage(13,sq ~Êt banco.codigoBarrassq ~Êt ,false,false,null,0,0)t java.awt.Imagepp~q ~Ðt LEFTpppppppppsq ~ dpsq ~ h  wîsq ~Î    ÿ   ppppq ~Ûsq ~ A    q ~éq ~éq ~Ípsq ~ k  wîsq ~Î    ÿ   ppppq ~Ûsq ~ A    q ~éq ~épsq ~ i  wîppppq ~éq ~épsq ~ n  wîsq ~Î    ÿ   ppppq ~Ûsq ~ A    q ~éq ~épsq ~ p  wîsq ~Î    ÿ   ppppq ~Ûsq ~ A    q ~éq ~épp~r 0net.sf.jasperreports.engine.type.OnErrorTypeEnum          xq ~ t ERRORppppp~r /net.sf.jasperreports.engine.type.ScaleImageEnum          xq ~ t 
FILL_FRAMEpppp~q ~¿t TOPsq ~³  wî   
        _   v   pq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppppppppppppppppppsq ~ dpsq ~ h  wîppppq ~ q ~ q ~ÿpsq ~ k  wîppppq ~ q ~ psq ~ i  wîppppq ~ q ~ psq ~ n  wîppppq ~ q ~ psq ~ p  wîppppq ~ q ~ ppppppppppppppppp  wî        ppq ~Ãsq ~Å   uq ~È   sq ~Êt banco.bancot java.lang.Stringppppppppppsq ~³  wî   
             ºpq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppppppq ~Ïpppppppppppsq ~ dpsq ~ h  wîppppq ~q ~q ~psq ~ k  wîppppq ~q ~psq ~ i  wîppppq ~q ~psq ~ n  wîppppq ~q ~psq ~ p  wîppppq ~q ~ppppppppppppppppp  wî        ppq ~Ãsq ~Å   uq ~È   sq ~Êt (sq ~Êt boleto.cidadeSacadosq ~Êt 
==null?"":sq ~Êt boleto.cidadeSacadosq ~Êt .trim() + "  " ) + ( sq ~Êt boleto.ufSacadosq ~Êt ==null? "" : ( sq ~Êt boleto.ufSacadosq ~Êt ))
+ " - " +(((sq ~Êt boleto.responsavelsq ~Êt  != null &&  !sq ~Êt boleto.responsavelsq ~Êt .isEmpty()) ? sq ~Êt boleto.nomeSacadosq ~Êt  : ""))t java.lang.Stringppppppppppsq ~³  wî   
        &   v   ºpq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppppppq ~Ïpppppppppppsq ~ dpsq ~ h  wîppppq ~4q ~4q ~3psq ~ k  wîppppq ~4q ~4psq ~ i  wîppppq ~4q ~4psq ~ n  wîppppq ~4q ~4psq ~ p  wîppppq ~4q ~4ppppppppppppppppp  wî        ppq ~Ãsq ~Å   uq ~È   
sq ~Êt boleto.cepSacadosq ~Êt .length() != 0 ? (sq ~Êt boleto.cepSacadosq ~Êt 
.length()<=5?sq ~Êt boleto.cepSacadosq ~Êt +"-000":sq ~Êt boleto.cepSacadosq ~Êt .substring(0,5)+"-"+sq ~Êt boleto.cepSacadosq ~Êt .substring(5)):""t java.lang.Stringppppppppppsq ~³  wî   
        >   v   Fpq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppppppq ~Ïpppppppppppsq ~ dpsq ~ h  wîppppq ~Rq ~Rq ~Qpsq ~ k  wîppppq ~Rq ~Rpsq ~ i  wîppppq ~Rq ~Rpsq ~ n  wîppppq ~Rq ~Rpsq ~ p  wîppppq ~Rq ~Rppppppppppppppppp  wî        ppq ~Ãsq ~Å   uq ~È   sq ~Êt boleto.dataDocumentot java.lang.Stringppppppppppsq ~³  wî   
       C   v   pq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppppppq ~Ïpppppppppppsq ~ dpsq ~ h  wîppppq ~^q ~^q ~]psq ~ k  wîppppq ~^q ~^psq ~ i  wîppppq ~^q ~^psq ~ n  wîppppq ~^q ~^psq ~ p  wîppppq ~^q ~^ppppppppppppppppp  wî        ppq ~Ãsq ~Å   uq ~È   sq ~Êt boleto.localPagamentot java.lang.Stringppppppppppsq ~³  wî   
        i      0pq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppppppq ~Ïpppppppppppsq ~ dpsq ~ h  wîppppq ~jq ~jq ~ipsq ~ k  wîppppq ~jq ~jpsq ~ i  wîppppq ~jq ~jpsq ~ n  wîppppq ~jq ~jpsq ~ p  wîppppq ~jq ~jppppppppppppppppp  wî        ppq ~Ãsq ~Å    uq ~È   sq ~Êt  banco.agenciaCodCedenteFormattedt java.lang.Stringppppppppppsq ~³  wî   
        {  À   0pq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppppppq ~Ïpq ~Ñpppppppppsq ~ dpsq ~ h  wîppppq ~vq ~vq ~upsq ~ k  wîppppq ~vq ~vpsq ~ i  wîppppq ~vq ~vpsq ~ n  wîppppq ~vq ~vpsq ~ p  wîppppq ~vq ~vppppppppppppppppp  wî        ppq ~Ãsq ~Å   !uq ~È   sq ~Êt  banco.agenciaCodCedenteFormattedt java.lang.Stringppppppppppsq ~ '  wî                 Opq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppsq ~ <  wîppppq ~p  wî q ~ Esq ~ V  wî              ¡   Ppq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppppppq ~ apppppppppppsq ~ dpsq ~ h  wîppppq ~q ~q ~psq ~ k  wîppppq ~q ~psq ~ i  wîppppq ~q ~psq ~ n  wîppppq ~q ~psq ~ p  wîppppq ~q ~pppppppppppppppppt CIPsq ~ V  wî           ,  Þ   Ópq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppppppq ~Ïppq ~¸ppppppppsq ~ dpsq ~ h  wîppppq ~q ~q ~psq ~ k  wîppppq ~q ~psq ~ i  wîppppq ~q ~psq ~ n  wîppppq ~q ~psq ~ p  wîppppq ~q ~pppppppppppppppppt 
CompensaÃ§Ã£osq ~ V  wî              Û   Zpq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppppppq ~ apq ~pppppppppsq ~ dpsq ~ h  wîppppq ~q ~q ~psq ~ k  wîppppq ~q ~psq ~ i  wîppppq ~q ~psq ~ n  wîppppq ~q ~psq ~ p  wîppppq ~q ~pppppppppppppppppt R$sq ~³  wî   
        j  K   Dpq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppppppq ~Ïpq ~pppppppppsq ~ dpsq ~ h  wîppppq ~q ~q ~psq ~ k  wîppppq ~q ~psq ~ i  wîppppq ~q ~psq ~ n  wîppppq ~q ~psq ~ p  wîppppq ~q ~ppppppppppppppppp  wî        ppq ~Ãsq ~Å   "uq ~È   sq ~Êt boleto.dataProcessamentot java.lang.Stringppppppppppsq ~ V  wî           '  "   Epq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppppppq ~ apq ~pppppppppsq ~ dpsq ~ h  wîppppq ~¨q ~¨q ~§psq ~ k  wîppppq ~¨q ~¨psq ~ i  wîppppq ~¨q ~¨psq ~ n  wîppppq ~¨q ~¨psq ~ p  wîppppq ~¨q ~¨pppppppppppppppppt Nxp  wî  ppq ~ pppt javapsr .net.sf.jasperreports.engine.base.JRBaseDataset      'Ø I PSEUDO_SERIAL_VERSION_UIDZ isMainB whenResourceMissingType[ fieldst &[Lnet/sf/jasperreports/engine/JRField;L filterExpressionq ~ [ groupst &[Lnet/sf/jasperreports/engine/JRGroup;L nameq ~ [ 
parameterst *[Lnet/sf/jasperreports/engine/JRParameter;L 
propertiesMapq ~ 2L queryt %Lnet/sf/jasperreports/engine/JRQuery;L resourceBundleq ~ L scriptletClassq ~ [ 
scriptletst *[Lnet/sf/jasperreports/engine/JRScriptlet;[ 
sortFieldst *[Lnet/sf/jasperreports/engine/JRSortField;[ 	variablest )[Lnet/sf/jasperreports/engine/JRVariable;L whenResourceMissingTypeValuet >Lnet/sf/jasperreports/engine/type/WhenResourceMissingTypeEnum;xp  wî ur &[Lnet.sf.jasperreports.engine.JRField;<ßÇN*òp  xp   sr ,net.sf.jasperreports.engine.base.JRBaseField      'Ø L descriptionq ~ L nameq ~ L 
propertiesMapq ~ 2L valueClassNameq ~ L valueClassRealNameq ~ xpt banco.linhaDigitavelt banco.linhaDigitavelsr +net.sf.jasperreports.engine.JRPropertiesMap      'Ø L baseq ~ 2L propertiesListq ~ L 
propertiesMapt Ljava/util/Map;xppppt java.lang.Stringpsq ~¼t boleto.dataVencimentot boleto.dataVencimentosq ~Àpppt java.lang.Stringpsq ~¼t boleto.nossoNumerot boleto.nossoNumerosq ~Àpppt java.lang.Stringpsq ~¼t boleto.valorBoletot boleto.valorBoletosq ~Àpppt java.lang.Stringpsq ~¼t boleto.cedentet boleto.cedentesq ~Àpppt java.lang.Stringpsq ~¼t boleto.especieDocumentot boleto.especieDocumentosq ~Àpppt java.lang.Stringpsq ~¼t boleto.carteirat boleto.carteirasq ~Àpppt java.lang.Stringpsq ~¼t boleto.nomeSacadot boleto.nomeSacadosq ~Àpppt java.lang.Stringpsq ~¼t boleto.enderecoSacadot boleto.enderecoSacadosq ~Àpppt java.lang.Stringpsq ~¼t banco.codigoBarrast banco.codigoBarrassq ~Àpppt java.lang.Stringpsq ~¼t boleto.instrucao1t boleto.instrucao1sq ~Àpppt java.lang.Stringpsq ~¼t banco.bancot banco.bancosq ~Àpppt java.lang.Stringpsq ~¼pt bancosq ~Àpppt java.lang.Objectpsq ~¼t banco.numeroFormattedt banco.numeroFormattedsq ~Àpppt java.lang.Stringpsq ~¼t boleto.cidadeSacadot boleto.cidadeSacadosq ~Àpppt java.lang.Stringpsq ~¼t boleto.cepSacadot boleto.cepSacadosq ~Àpppt java.lang.Stringpsq ~¼t boleto.ufSacadot boleto.ufSacadosq ~Àpppt java.lang.Stringpsq ~¼t boleto.cpfSacadot boleto.cpfSacadosq ~Àpppt java.lang.Stringpsq ~¼t boleto.dataDocumentot boleto.dataDocumentosq ~Àpppt java.lang.Stringpsq ~¼t boleto.localPagamentot boleto.localPagamentosq ~Àpppt java.lang.Stringpsq ~¼t boleto.agenciat boleto.agenciasq ~Àpppt java.lang.Stringpsq ~¼t boleto.dvAgenciat boleto.dvAgenciasq ~Àpppt java.lang.Stringpsq ~¼t boleto.contaCorrentet boleto.contaCorrentesq ~Àpppt java.lang.Stringpsq ~¼t boleto.dvContaCorrentet boleto.dvContaCorrentesq ~Àpppt java.lang.Stringpsq ~¼pt boleto.numConveniosq ~Àpppt java.lang.Stringpsq ~¼pt boleto.dvNossoNumerosq ~Àpppt java.lang.Stringpsq ~¼pt banco.nossoNumeroFormattedsq ~Àpppt java.lang.Stringpsq ~¼pt  banco.agenciaCodCedenteFormattedsq ~Àpppt java.lang.Stringpsq ~¼pt boleto.dataProcessamentosq ~Àpppt java.lang.Stringpsq ~¼t boleto.responsavelt boleto.responsavelsq ~Àpppt java.lang.Stringpppt bradescour *[Lnet.sf.jasperreports.engine.JRParameter;" *Ã`!  xp   sr 0net.sf.jasperreports.engine.base.JRBaseParameter      'Ø 	Z isForPromptingZ isSystemDefinedL defaultValueExpressionq ~ L descriptionq ~ L nameq ~ L nestedTypeNameq ~ L 
propertiesMapq ~ 2L valueClassNameq ~ L valueClassRealNameq ~ xpppt REPORT_PARAMETERS_MAPpsq ~Àpppt 
java.util.Mappsq ~Rppt 
JASPER_REPORTpsq ~Àpppt (net.sf.jasperreports.engine.JasperReportpsq ~Rppt REPORT_CONNECTIONpsq ~Àpppt java.sql.Connectionpsq ~Rppt REPORT_MAX_COUNTpsq ~Àpppt java.lang.Integerpsq ~Rppt REPORT_DATA_SOURCEpsq ~Àpppt (net.sf.jasperreports.engine.JRDataSourcepsq ~Rppt REPORT_SCRIPTLETpsq ~Àpppt /net.sf.jasperreports.engine.JRAbstractScriptletpsq ~Rppt 
REPORT_LOCALEpsq ~Àpppt java.util.Localepsq ~Rppt REPORT_RESOURCE_BUNDLEpsq ~Àpppt java.util.ResourceBundlepsq ~Rppt REPORT_TIME_ZONEpsq ~Àpppt java.util.TimeZonepsq ~Rppt REPORT_FORMAT_FACTORYpsq ~Àpppt .net.sf.jasperreports.engine.util.FormatFactorypsq ~Rppt REPORT_CLASS_LOADERpsq ~Àpppt java.lang.ClassLoaderpsq ~Rppt REPORT_URL_HANDLER_FACTORYpsq ~Àpppt  java.net.URLStreamHandlerFactorypsq ~Rppt REPORT_FILE_RESOLVERpsq ~Àpppt -net.sf.jasperreports.engine.util.FileResolverpsq ~Rppt REPORT_TEMPLATESpsq ~Àpppt java.util.Collectionpsq ~Rppt REPORT_VIRTUALIZERpsq ~Àpppt )net.sf.jasperreports.engine.JRVirtualizerpsq ~Rppt IS_IGNORE_PAGINATIONpsq ~Àpppt java.lang.Booleanpsq ~Àpsq ~    w   t ireport.zoomt 	ireport.xt 	ireport.yxsr java.util.HashMapÚÁÃ`Ñ F 
loadFactorI 	thresholdxp?@     w      q ~t 1.948717100000002q ~t 0q ~t 0xpppppur )[Lnet.sf.jasperreports.engine.JRVariable;bæ|,·D  xp   sr /net.sf.jasperreports.engine.base.JRBaseVariable      'Ø I PSEUDO_SERIAL_VERSION_UIDB calculationB 
incrementTypeZ isSystemDefinedB 	resetTypeL calculationValuet 2Lnet/sf/jasperreports/engine/type/CalculationEnum;L 
expressionq ~ L incrementGroupq ~ 1L incrementTypeValuet 4Lnet/sf/jasperreports/engine/type/IncrementTypeEnum;L incrementerFactoryClassNameq ~ L incrementerFactoryClassRealNameq ~ L initialValueExpressionq ~ L nameq ~ L 
resetGroupq ~ 1L resetTypeValuet 0Lnet/sf/jasperreports/engine/type/ResetTypeEnum;L valueClassNameq ~ L valueClassRealNameq ~ xp  wî   ~r 0net.sf.jasperreports.engine.type.CalculationEnum          xq ~ t SYSTEMpp~r 2net.sf.jasperreports.engine.type.IncrementTypeEnum          xq ~ t NONEppsq ~Å    uq ~È   sq ~Êt new java.lang.Integer(1)q ~bpt PAGE_NUMBERp~r .net.sf.jasperreports.engine.type.ResetTypeEnum          xq ~ t REPORTq ~bpsq ~  wî   q ~¥ppq ~¨ppsq ~Å   uq ~È   sq ~Êt new java.lang.Integer(1)q ~bpt 
COLUMN_NUMBERp~q ~¯t PAGEq ~bpsq ~  wî   ~q ~¤t COUNTsq ~Å   uq ~È   sq ~Êt new java.lang.Integer(1)q ~bppq ~¨ppsq ~Å   uq ~È   sq ~Êt new java.lang.Integer(0)q ~bpt REPORT_COUNTpq ~°q ~bpsq ~  wî   q ~»sq ~Å   uq ~È   sq ~Êt new java.lang.Integer(1)q ~bppq ~¨ppsq ~Å   uq ~È   sq ~Êt new java.lang.Integer(0)q ~bpt 
PAGE_COUNTpq ~¸q ~bpsq ~  wî   q ~»sq ~Å   uq ~È   sq ~Êt new java.lang.Integer(1)q ~bppq ~¨ppsq ~Å   uq ~È   sq ~Êt new java.lang.Integer(0)q ~bpt COLUMN_COUNTp~q ~¯t COLUMNq ~bp~r <net.sf.jasperreports.engine.type.WhenResourceMissingTypeEnum          xq ~ t NULLq ~Op~r 0net.sf.jasperreports.engine.type.OrientationEnum          xq ~ t PORTRAITpp~r /net.sf.jasperreports.engine.type.PrintOrderEnum          xq ~ t VERTICALpppp~r 3net.sf.jasperreports.engine.type.WhenNoDataTypeEnum          xq ~ t NO_PAGESsr 6net.sf.jasperreports.engine.design.JRReportCompileData      'Ø L crosstabCompileDataq ~ÁL datasetCompileDataq ~ÁL mainDatasetCompileDataq ~ xpsq ~?@     w       xsq ~?@     w       xur [B¬óøTà  xp  +Êþº¾   .p bradesco_1565125453888_869760  ,net/sf/jasperreports/engine/fill/JREvaluator  parameter_REPORT_LOCALE 2Lnet/sf/jasperreports/engine/fill/JRFillParameter; parameter_JASPER_REPORT parameter_REPORT_VIRTUALIZER parameter_REPORT_TIME_ZONE parameter_REPORT_FILE_RESOLVER parameter_REPORT_SCRIPTLET parameter_REPORT_PARAMETERS_MAP parameter_REPORT_CONNECTION parameter_REPORT_CLASS_LOADER parameter_REPORT_DATA_SOURCE $parameter_REPORT_URL_HANDLER_FACTORY parameter_IS_IGNORE_PAGINATION parameter_REPORT_FORMAT_FACTORY parameter_REPORT_MAX_COUNT parameter_REPORT_TEMPLATES  parameter_REPORT_RESOURCE_BUNDLE !field_banco46nossoNumeroFormatted .Lnet/sf/jasperreports/engine/fill/JRFillField; field_boleto46cedente field_boleto46enderecoSacado field_boleto46agencia field_boleto46dvNossoNumero field_boleto46dvAgencia 'field_banco46agenciaCodCedenteFormatted field_banco46codigoBarras field_boleto46nomeSacado field_banco46banco field_boleto46valorBoleto field_boleto46especieDocumento field_banco46numeroFormatted field_banco field_boleto46dataVencimento field_boleto46dataProcessamento field_boleto46numConvenio field_boleto46ufSacado field_boleto46cepSacado field_boleto46dvContaCorrente field_boleto46dataDocumento field_banco46linhaDigitavel field_boleto46nossoNumero field_boleto46contaCorrente field_boleto46responsavel field_boleto46cpfSacado field_boleto46localPagamento field_boleto46cidadeSacado field_boleto46carteira field_boleto46instrucao1 variable_PAGE_NUMBER 1Lnet/sf/jasperreports/engine/fill/JRFillVariable; variable_COLUMN_NUMBER variable_REPORT_COUNT variable_PAGE_COUNT variable_COLUMN_COUNT <init> ()V Code ; <
  >  	  @  	  B  	  D 	 	  F 
 	  H  	  J  	  L 
 	  N  	  P  	  R  	  T  	  V  	  X  	  Z  	  \  	  ^  	  `  	  b  	  d  	  f  	  h  	  j  	  l  	  n  	  p   	  r ! 	  t " 	  v # 	  x $ 	  z % 	  | & 	  ~ ' 	   ( 	   ) 	   * 	   + 	   , 	   - 	   . 	   / 	   0 	   1 	   2 	   3 	   4 	   5 6	   7 6	   8 6	    9 6	  ¢ : 6	  ¤ LineNumberTable customizedInit 0(Ljava/util/Map;Ljava/util/Map;Ljava/util/Map;)V 
initParams (Ljava/util/Map;)V © ª
  « 
initFields ­ ª
  ® initVars ° ª
  ± 
REPORT_LOCALE ³ 
java/util/Map µ get &(Ljava/lang/Object;)Ljava/lang/Object; · ¸ ¶ ¹ 0net/sf/jasperreports/engine/fill/JRFillParameter » 
JASPER_REPORT ½ REPORT_VIRTUALIZER ¿ REPORT_TIME_ZONE Á REPORT_FILE_RESOLVER Ã REPORT_SCRIPTLET Å REPORT_PARAMETERS_MAP Ç REPORT_CONNECTION É REPORT_CLASS_LOADER Ë REPORT_DATA_SOURCE Í REPORT_URL_HANDLER_FACTORY Ï IS_IGNORE_PAGINATION Ñ REPORT_FORMAT_FACTORY Ó REPORT_MAX_COUNT Õ REPORT_TEMPLATES × REPORT_RESOURCE_BUNDLE Ù banco.nossoNumeroFormatted Û ,net/sf/jasperreports/engine/fill/JRFillField Ý boleto.cedente ß boleto.enderecoSacado á boleto.agencia ã boleto.dvNossoNumero å boleto.dvAgencia ç  banco.agenciaCodCedenteFormatted é banco.codigoBarras ë boleto.nomeSacado í banco.banco ï boleto.valorBoleto ñ boleto.especieDocumento ó banco.numeroFormatted õ banco ÷ boleto.dataVencimento ù boleto.dataProcessamento û boleto.numConvenio ý boleto.ufSacado ÿ boleto.cepSacado boleto.dvContaCorrente boleto.dataDocumento banco.linhaDigitavel boleto.nossoNumero	 boleto.contaCorrente boleto.responsavel
 boleto.cpfSacado boleto.localPagamento boleto.cidadeSacado boleto.carteira boleto.instrucao1 PAGE_NUMBER /net/sf/jasperreports/engine/fill/JRFillVariable 
COLUMN_NUMBER REPORT_COUNT 
PAGE_COUNT! COLUMN_COUNT# evaluate (I)Ljava/lang/Object; 
Exceptions java/lang/Throwable( java/lang/Integer* (I)V ;,
+- getValue ()Ljava/lang/Object;/0
 Þ1 java/lang/String3 java/lang/StringBuffer5 isEmpty ()Z78
49 valueOf &(Ljava/lang/Object;)Ljava/lang/String;;<
4= (Ljava/lang/String;)V ;?
6@  CPF/CNPJ: B append ,(Ljava/lang/String;)Ljava/lang/StringBuffer;DE
6F toString ()Ljava/lang/String;HI
6J (it/businesslogic/ireport/barcode/BcImageL getBarcodeImage I(ILjava/lang/Object;ZZLjava/lang/String;II)Ljava/awt/image/BufferedImage;NO
MP  R trimTI
4U   W  - Y length ()I[\
4] -000_ 	substring (II)Ljava/lang/String;ab
4c -e (I)Ljava/lang/String;ag
4h evaluateOld getOldValuek0
 Þl evaluateEstimated 
SourceFile !     3                 	     
               
                                                                                                !     "     #     $     %     &     '     (     )     *     +     ,     -     .     /     0     1     2     3     4     5 6    7 6    8 6    9 6    : 6     ; <  =  ì    *· ?*µ A*µ C*µ E*µ G*µ I*µ K*µ M*µ O*µ Q*µ S*µ U*µ W*µ Y*µ [*µ ]*µ _*µ a*µ c*µ e*µ g*µ i*µ k*µ m*µ o*µ q*µ s*µ u*µ w*µ y*µ {*µ }*µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ ¡*µ £*µ ¥±    ¦   Ö 5      	          "  '   , ! 1 " 6 # ; $ @ % E & J ' O ( T ) Y * ^ + c , h - m . r / w 0 | 1  2  3  4  5  6  7  8 ¤ 9 © : ® ; ³ < ¸ = ½ > Â ? Ç @ Ì A Ñ B Ö C Û D à E å F ê G ï H ô I ù J þ K   § ¨  =   4     *+· ¬*,· ¯*-· ²±    ¦       W  X 
 Y  Z  © ª  =  y    !*+´¹ º À ¼À ¼µ A*+¾¹ º À ¼À ¼µ C*+À¹ º À ¼À ¼µ E*+Â¹ º À ¼À ¼µ G*+Ä¹ º À ¼À ¼µ I*+Æ¹ º À ¼À ¼µ K*+È¹ º À ¼À ¼µ M*+Ê¹ º À ¼À ¼µ O*+Ì¹ º À ¼À ¼µ Q*+Î¹ º À ¼À ¼µ S*+Ð¹ º À ¼À ¼µ U*+Ò¹ º À ¼À ¼µ W*+Ô¹ º À ¼À ¼µ Y*+Ö¹ º À ¼À ¼µ [*+Ø¹ º À ¼À ¼µ ]*+Ú¹ º À ¼À ¼µ _±    ¦   F    b  c $ d 6 e H f Z g l h ~ i  j ¢ k ´ l Æ m Ø n ê o ü p q  r  ­ ª  =  º    **+Ü¹ º À ÞÀ Þµ a*+à¹ º À ÞÀ Þµ c*+â¹ º À ÞÀ Þµ e*+ä¹ º À ÞÀ Þµ g*+æ¹ º À ÞÀ Þµ i*+è¹ º À ÞÀ Þµ k*+ê¹ º À ÞÀ Þµ m*+ì¹ º À ÞÀ Þµ o*+î¹ º À ÞÀ Þµ q*+ð¹ º À ÞÀ Þµ s*+ò¹ º À ÞÀ Þµ u*+ô¹ º À ÞÀ Þµ w*+ö¹ º À ÞÀ Þµ y*+ø¹ º À ÞÀ Þµ {*+ú¹ º À ÞÀ Þµ }*+ü¹ º À ÞÀ Þµ *+þ¹ º À ÞÀ Þµ *+ ¹ º À ÞÀ Þµ *+¹ º À ÞÀ Þµ *+¹ º À ÞÀ Þµ *+¹ º À ÞÀ Þµ *+¹ º À ÞÀ Þµ *+
¹ º À ÞÀ Þµ *+¹ º À ÞÀ Þµ *+¹ º À ÞÀ Þµ *+¹ º À ÞÀ Þµ *+¹ º À ÞÀ Þµ *+¹ º À ÞÀ Þµ *+¹ º À ÞÀ Þµ *+¹ º À ÞÀ Þµ ±    ¦   ~    z  { $ | 6 } H ~ Z  l  ~    ¢  ´  Æ  Ø  ê  ü    2 E X k ~  ¤ · Ê Ý ð   )   ° ª  =        `*+¹ º ÀÀµ *+¹ º ÀÀµ *+ ¹ º ÀÀµ ¡*+"¹ º ÀÀµ £*+$¹ º ÀÀµ ¥±    ¦          ¡ & ¢ 9 £ L ¤ _ ¥ %& '    ) =  +    ïMª  ê       "      ¥   ±   ½   É   Õ   á   í   ù      #  1  ?  M  [  ³  Á  Ï  Ý    #  1  ?  M  [  i      )  §  µ  Ã  Ñ  ß»+Y·.M§H»+Y·.M§<»+Y·.M§0»+Y·.M§$»+Y·.M§»+Y·.M§»+Y·.M§ »+Y·.M§ô*´ ¶2À4M§æ*´ a¶2À4M§Ø*´ a¶2À4M§Ê*´ c¶2À4M§¼*´ u¶2À4M§®*´ ¶2À4M§ *´ u¶2À4M§»6Y*´ ¶2À4Æ  *´ ¶2À4¶: *´ ¶2À4§ 
*´ q¶2À4¸>·AC¶G*´ ¶2À4¶G¶KM§:*´ e¶2À4M§,*´ w¶2À4M§*´ u¶2À4M§*´ ¶2À4Æ  *´ ¶2À4¶: *´ ¶2À4§ 
*´ q¶2À4M§Ø*´ }¶2À4M§Ê*´ }¶2À4M§¼*´ y¶2À4M§®*´ y¶2À4M§ *´ ¶2À4M§*´ s¶2À4M§
*´ o¶2À4¸QM§l*´ s¶2À4M§^»6Y*´ ¶2À4Ç 	S§ #»6Y*´ ¶2À4¶V¸>·AX¶G¶K¸>·A*´ ¶2À4Ç 	S§ 
*´ ¶2À4¶GZ¶G*´ ¶2À4Æ  *´ ¶2À4¶: *´ q¶2À4§ S¶G¶KM§ Ä*´ ¶2À4¶^ j*´ ¶2À4¶^£ #»6Y*´ ¶2À4¸>·A`¶G¶K§ <»6Y*´ ¶2À4¶d¸>·Af¶G*´ ¶2À4¶i¶G¶K§ SM§ F*´ ¶2À4M§ 8*´ ¶2À4M§ **´ m¶2À4M§ *´ m¶2À4M§ *´ ¶2À4M,°    ¦  * J   ­  ¯  ³ ¥ ´ ¨ ¸ ± ¹ ´ ½ ½ ¾ À Â É Ã Ì Ç Õ È Ø Ì á Í ä Ñ í Ò ð Ö ù × ü Û Ü
 à á å# æ& ê1 ë4 ï? ðB ôM õP ù[ ú^ þ³ ÿ¶ÁÄÏ	Ò
Ýà#&14!?"B&M'P+[,^0i1l56:;?ï@%?)A,E§FªJµK¸OÃPÆTÑUÔYßZâ^íf j& '    ) =  +    ïMª  ê       "      ¥   ±   ½   É   Õ   á   í   ù      #  1  ?  M  [  ³  Á  Ï  Ý    #  1  ?  M  [  i      )  §  µ  Ã  Ñ  ß»+Y·.M§H»+Y·.M§<»+Y·.M§0»+Y·.M§$»+Y·.M§»+Y·.M§»+Y·.M§ »+Y·.M§ô*´ ¶mÀ4M§æ*´ a¶mÀ4M§Ø*´ a¶mÀ4M§Ê*´ c¶mÀ4M§¼*´ u¶mÀ4M§®*´ ¶mÀ4M§ *´ u¶mÀ4M§»6Y*´ ¶mÀ4Æ  *´ ¶mÀ4¶: *´ ¶mÀ4§ 
*´ q¶mÀ4¸>·AC¶G*´ ¶mÀ4¶G¶KM§:*´ e¶mÀ4M§,*´ w¶mÀ4M§*´ u¶mÀ4M§*´ ¶mÀ4Æ  *´ ¶mÀ4¶: *´ ¶mÀ4§ 
*´ q¶mÀ4M§Ø*´ }¶mÀ4M§Ê*´ }¶mÀ4M§¼*´ y¶mÀ4M§®*´ y¶mÀ4M§ *´ ¶mÀ4M§*´ s¶mÀ4M§
*´ o¶mÀ4¸QM§l*´ s¶mÀ4M§^»6Y*´ ¶mÀ4Ç 	S§ #»6Y*´ ¶mÀ4¶V¸>·AX¶G¶K¸>·A*´ ¶mÀ4Ç 	S§ 
*´ ¶mÀ4¶GZ¶G*´ ¶mÀ4Æ  *´ ¶mÀ4¶: *´ q¶mÀ4§ S¶G¶KM§ Ä*´ ¶mÀ4¶^ j*´ ¶mÀ4¶^£ #»6Y*´ ¶mÀ4¸>·A`¶G¶K§ <»6Y*´ ¶mÀ4¶d¸>·Af¶G*´ ¶mÀ4¶i¶G¶K§ SM§ F*´ ¶mÀ4M§ 8*´ ¶mÀ4M§ **´ m¶mÀ4M§ *´ m¶mÀ4M§ *´ ¶mÀ4M,°    ¦  * J  o q u ¥v ¨z ±{ ´ ½ À É Ì Õ Ø á ä í ð ù ü
¢£§#¨&¬1­4±?²B¶M·P»[¼^À³Á¶ÅÁÆÄÊÏËÒÏÝÐàÔÕÙ#Ú&Þ1ß4ã?äBèMéPí[î^òiól÷øüýï%),§ªµ
¸ÃÆÑÔßâ í( n& '    ) =  +    ïMª  ê       "      ¥   ±   ½   É   Õ   á   í   ù      #  1  ?  M  [  ³  Á  Ï  Ý    #  1  ?  M  [  i      )  §  µ  Ã  Ñ  ß»+Y·.M§H»+Y·.M§<»+Y·.M§0»+Y·.M§$»+Y·.M§»+Y·.M§»+Y·.M§ »+Y·.M§ô*´ ¶2À4M§æ*´ a¶2À4M§Ø*´ a¶2À4M§Ê*´ c¶2À4M§¼*´ u¶2À4M§®*´ ¶2À4M§ *´ u¶2À4M§»6Y*´ ¶2À4Æ  *´ ¶2À4¶: *´ ¶2À4§ 
*´ q¶2À4¸>·AC¶G*´ ¶2À4¶G¶KM§:*´ e¶2À4M§,*´ w¶2À4M§*´ u¶2À4M§*´ ¶2À4Æ  *´ ¶2À4¶: *´ ¶2À4§ 
*´ q¶2À4M§Ø*´ }¶2À4M§Ê*´ }¶2À4M§¼*´ y¶2À4M§®*´ y¶2À4M§ *´ ¶2À4M§*´ s¶2À4M§
*´ o¶2À4¸QM§l*´ s¶2À4M§^»6Y*´ ¶2À4Ç 	S§ #»6Y*´ ¶2À4¶V¸>·AX¶G¶K¸>·A*´ ¶2À4Ç 	S§ 
*´ ¶2À4¶GZ¶G*´ ¶2À4Æ  *´ ¶2À4¶: *´ q¶2À4§ S¶G¶KM§ Ä*´ ¶2À4¶^ j*´ ¶2À4¶^£ #»6Y*´ ¶2À4¸>·A`¶G¶K§ <»6Y*´ ¶2À4¶d¸>·Af¶G*´ ¶2À4¶i¶G¶K§ SM§ F*´ ¶2À4M§ 8*´ ¶2À4M§ **´ m¶2À4M§ *´ m¶2À4M§ *´ ¶2À4M,°    ¦  * J  1 3 7 ¥8 ¨< ±= ´A ½B ÀF ÉG ÌK ÕL ØP áQ äU íV ðZ ù[ ü_`
dei#j&n1o4s?tBxMyP}[~^³¶ÁÄÏÒÝà#& 1¡4¥?¦BªM«P¯[°^´iµl¹º¾¿ÃïÄ%Ã)Å,É§ÊªÎµÏ¸ÓÃÔÆØÑÙÔÝßÞââíê o    t _1565125453888_869760t 2net.sf.jasperreports.engine.design.JRJavacCompiler
<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="ReciboRel" pageWidth="580" pageHeight="850" whenNoDataType="AllSectionsNoDetail" columnWidth="524" leftMargin="28" rightMargin="28" topMargin="28" bottomMargin="28" whenResourceMissingType="Empty">
	<property name="ireport.zoom" value="1.0"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.encoding" value="ISO-8859-1"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="java.util.*"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<parameter name="nomeEmpresa" class="java.lang.String"/>
	<parameter name="empresaVO.endereco" class="java.lang.String"/>
	<parameter name="empresaVO.fone" class="java.lang.String"/>
	<parameter name="logoPadraoRelatorio" class="java.io.InputStream"/>
	<parameter name="SUBREPORT_DIR" class="java.lang.String">
		<defaultValueExpression><![CDATA["E:\\desenvolvimentosv\\Projeto Pacto Solucoes\\ZillyonWeb\\src\\java\\relatorio\\designRelatorio\\financeiro\\"]]></defaultValueExpression>
	</parameter>
	<parameter name="propaganda" class="java.io.InputStream"/>
	<field name="boleto" class="java.lang.Object"/>
	<field name="boletoDatasource" class="java.lang.Object"/>
	<detail>
		<band height="700">
			<subreport>
				<reportElement x="0" y="0" width="524" height="700"/>
				<subreportParameter name="SUBREPORT_DIR">
					<subreportParameterExpression><![CDATA[$P{SUBREPORT_DIR}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="empresaVO.endereco">
					<subreportParameterExpression><![CDATA[$P{empresaVO.endereco}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="nomeEmpresa">
					<subreportParameterExpression><![CDATA[$P{nomeEmpresa}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="logoPadraoRelatorio">
					<subreportParameterExpression><![CDATA[$P{logoPadraoRelatorio}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="empresaVO.fone">
					<subreportParameterExpression><![CDATA[$P{empresaVO.fone}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="propaganda">
					<subreportParameterExpression><![CDATA[$P{propaganda}]]></subreportParameterExpression>
				</subreportParameter>
				<dataSourceExpression><![CDATA[$F{boletoDatasource}]]></dataSourceExpression>
				<subreportExpression class="java.lang.String"><![CDATA[$P{SUBREPORT_DIR} + "boletoClube.jasper"]]></subreportExpression>
			</subreport>
		</band>
	</detail>
</jasperReport>

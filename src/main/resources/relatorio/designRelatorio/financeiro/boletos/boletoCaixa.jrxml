<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="boleto" pageWidth="595" pageHeight="842" columnWidth="590" leftMargin="5" rightMargin="0" topMargin="0" bottomMargin="0">
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.encoding" value="UTF-8"/>
	<property name="ireport.zoom" value="1.8150000000000024"/>
	<property name="ireport.x" value="12"/>
	<property name="ireport.y" value="384"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="java.util.*"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<parameter name="cnpjEmpresa" class="java.lang.String"/>
	<parameter name="enderecoEmpresa" class="java.lang.String"/>
	<parameter name="SUBREPORT_DIR" class="java.lang.String"/>
	<queryString>
		<![CDATA[]]>
	</queryString>
	<field name="banco" class="java.lang.Object"/>
	<field name="banco.numeroFormatted" class="java.lang.String"/>
	<field name="banco.linhaDigitavel" class="java.lang.String"/>
	<field name="banco.codigoBarras" class="java.lang.String"/>
	<field name="boleto.cedente" class="java.lang.String"/>
	<field name="boleto.localPagamento" class="java.lang.String"/>
	<field name="boleto.dataDocumento" class="java.lang.String"/>
	<field name="boleto.especieDocumento" class="java.lang.String"/>
	<field name="boleto.aceite" class="java.lang.String"/>
	<field name="boleto.dataProcessamento" class="java.lang.String"/>
	<field name="boleto.carteira" class="java.lang.String"/>
	<field name="boleto.dataVencimento" class="java.lang.String"/>
	<field name="boleto.valorBoleto" class="java.lang.String"/>
	<field name="boleto.nomeSacado" class="java.lang.String"/>
	<field name="boleto.cpfSacado" class="java.lang.String"/>
	<field name="boleto.nossoNumero" class="java.lang.String"/>
	<field name="boleto.enderecoSacado" class="java.lang.String"/>
	<field name="boleto.cepSacado" class="java.lang.String"/>
	<field name="boleto.cidadeSacado" class="java.lang.String"/>
	<field name="boleto.ufSacado" class="java.lang.String"/>
	<field name="banco.agenciaCodCedenteFormatted" class="java.lang.String"/>
	<field name="boleto.instrucao1" class="java.lang.String"/>
	<field name="banco.banco" class="java.lang.String"/>
	<field name="banco.nossoNumeroFormatted" class="java.lang.String"/>
	<field name="boleto.dvNossoNumero" class="java.lang.String"/>
	<field name="boleto.noDocumento" class="java.lang.String"/>
	<field name="boleto.responsavel" class="java.lang.String">
		<fieldDescription><![CDATA[boleto.responsavel]]></fieldDescription>
	</field>
	<detail>
		<band height="450" splitType="Stretch">
			<line>
				<reportElement key="line-20" x="0" y="164" width="524" height="1"/>
				<graphicElement>
					<pen lineWidth="1.0" lineStyle="Dashed"/>
				</graphicElement>
			</line>
			<line>
				<reportElement key="line-1" x="1" y="184" width="522" height="1"/>
				<graphicElement>
					<pen lineWidth="1.0" lineStyle="Solid"/>
				</graphicElement>
			</line>
			<line>
				<reportElement key="line-2" x="120" y="167" width="1" height="17"/>
				<graphicElement>
					<pen lineWidth="1.0" lineStyle="Solid"/>
				</graphicElement>
			</line>
			<line>
				<reportElement key="line-3" x="154" y="167" width="1" height="17"/>
				<graphicElement>
					<pen lineWidth="1.0" lineStyle="Solid"/>
				</graphicElement>
			</line>
			<line>
				<reportElement key="line-4" x="0" y="201" width="524" height="1"/>
				<graphicElement>
					<pen lineWidth="1.0" lineStyle="Solid"/>
				</graphicElement>
			</line>
			<line>
				<reportElement key="line-5" x="0" y="218" width="524" height="1"/>
				<graphicElement>
					<pen lineWidth="1.0" lineStyle="Solid"/>
				</graphicElement>
			</line>
			<line>
				<reportElement key="line-6" x="0" y="235" width="524" height="1"/>
				<graphicElement>
					<pen lineWidth="1.0" lineStyle="Solid"/>
				</graphicElement>
			</line>
			<line>
				<reportElement key="line-7" x="0" y="252" width="524" height="1"/>
				<graphicElement>
					<pen lineWidth="1.0" lineStyle="Solid"/>
				</graphicElement>
			</line>
			<line>
				<reportElement key="line-8" x="377" y="184" width="1" height="153"/>
				<graphicElement>
					<pen lineWidth="1.0" lineStyle="Solid"/>
				</graphicElement>
			</line>
			<line>
				<reportElement key="line-9" x="0" y="337" width="524" height="1"/>
				<graphicElement>
					<pen lineWidth="1.0" lineStyle="Solid"/>
				</graphicElement>
			</line>
			<line>
				<reportElement key="line-10" x="378" y="269" width="146" height="1"/>
				<graphicElement>
					<pen lineWidth="1.0" lineStyle="Solid"/>
				</graphicElement>
			</line>
			<line>
				<reportElement key="line-11" x="378" y="286" width="146" height="1"/>
				<graphicElement>
					<pen lineWidth="1.0" lineStyle="Solid"/>
				</graphicElement>
			</line>
			<line>
				<reportElement key="line-12" x="378" y="303" width="146" height="1"/>
				<graphicElement>
					<pen lineWidth="1.0" lineStyle="Solid"/>
				</graphicElement>
			</line>
			<line>
				<reportElement key="line-13" x="377" y="320" width="147" height="1"/>
				<graphicElement>
					<pen lineWidth="1.0" lineStyle="Solid"/>
				</graphicElement>
			</line>
			<line>
				<reportElement key="line-14" x="76" y="218" width="1" height="34"/>
				<graphicElement>
					<pen lineWidth="1.0" lineStyle="Solid"/>
				</graphicElement>
			</line>
			<line>
				<reportElement key="line-15" x="152" y="218" width="1" height="17"/>
				<graphicElement>
					<pen lineWidth="1.0" lineStyle="Solid"/>
				</graphicElement>
			</line>
			<line>
				<reportElement key="line-16" x="124" y="235" width="1" height="17"/>
				<graphicElement>
					<pen lineWidth="1.0" lineStyle="Solid"/>
				</graphicElement>
			</line>
			<line>
				<reportElement key="line-17" x="237" y="218" width="1" height="34"/>
				<graphicElement>
					<pen lineWidth="1.0" lineStyle="Solid"/>
				</graphicElement>
			</line>
			<line>
				<reportElement key="line-18" x="209" y="218" width="1" height="17"/>
				<graphicElement>
					<pen lineWidth="1.0" lineStyle="Solid"/>
				</graphicElement>
			</line>
			<line>
				<reportElement key="line-19" x="0" y="380" width="524" height="1"/>
				<graphicElement>
					<pen lineWidth="1.0" lineStyle="Solid"/>
				</graphicElement>
			</line>
			<staticText>
				<reportElement key="staticText-1" x="1" y="184" width="66" height="8"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="6" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Local de pagamento]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-2" x="1" y="201" width="66" height="8"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="6" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Beneficiário]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-3" x="1" y="218" width="66" height="8"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="6" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Data do Documento]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-4" x="1" y="235" width="75" height="8"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="6" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Nº da Conta / Respons.]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-5" x="1" y="254" width="75" height="8"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="6" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Instruções :]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-6" x="79" y="218" width="66" height="8"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="6" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Nº do Documento]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-7" x="155" y="218" width="42" height="8"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="6" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Espécie Doc.]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-8" x="212" y="218" width="22" height="8"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="6" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Aceite]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-9" x="240" y="218" width="137" height="8"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="6" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Data do Processamento]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-10" x="79" y="235" width="35" height="8"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="6" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Carteira]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-11" x="126" y="235" width="43" height="8"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="6" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Espécie Moeda]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-12" x="173" y="235" width="48" height="8"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="6" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Qtde moeda]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-13" x="240" y="235" width="66" height="8"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="6" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Valor]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-14" x="380" y="184" width="100" height="8"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="6" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Vencimento]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-15" x="380" y="201" width="100" height="8"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="6" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Agência / Código Beneficiário]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-16" x="380" y="320" width="100" height="8"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="6" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[(=) Valor cobrado]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-17" x="380" y="303" width="100" height="8"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="6" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[(=) Outros acréscimos]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-18" x="380" y="286" width="100" height="8"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="6" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[(+) Mora / Juros / Multa]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-19" x="380" y="269" width="100" height="8"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="6" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[(-) Outros deduções]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-20" x="380" y="252" width="100" height="8"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="6" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[(-) Desconto / Abatimento]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-21" x="380" y="235" width="100" height="8"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="6" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[(=) Valor do Documento]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-22" x="380" y="218" width="100" height="8"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="6" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Nosso Número]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-23" x="1" y="339" width="33" height="8"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="6" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Pagador:]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-24" x="1" y="369" width="65" height="8"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="6" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Sacador / Avalista :]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-25" x="357" y="369" width="57" height="8"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="6" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Código de baixa]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-26" x="332" y="382" width="68" height="8"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="6" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Autenticação mecânica]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-27" x="410" y="382" width="106" height="11"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="9" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Ficha de Compensação]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-28" mode="Opaque" x="234" y="241" width="7" height="8"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="6" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[X]]></text>
			</staticText>
			<image scaleImage="FillFrame" hAlign="Left" vAlign="Top">
				<reportElement key="barcode-1" mode="Opaque" x="1" y="382" width="319" height="35" forecolor="#000000" backcolor="#FFFFFF"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<graphicElement fill="Solid">
					<pen lineWidth="0.0" lineStyle="Solid"/>
				</graphicElement>
				<imageExpression class="java.awt.Image"><![CDATA[it.businesslogic.ireport.barcode.BcImage.getBarcodeImage(13,$F{banco.codigoBarras},false,false,null,0,0)]]></imageExpression>
			</image>
			<line>
				<reportElement key="line-22" x="1" y="1" width="524" height="1"/>
				<graphicElement>
					<pen lineWidth="1.0" lineStyle="Dashed"/>
				</graphicElement>
			</line>
			<line>
				<reportElement key="line-50" x="0" y="435" width="524" height="1"/>
				<graphicElement>
					<pen lineWidth="1.0" lineStyle="Dashed"/>
				</graphicElement>
			</line>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement key="textField-35" x="123" y="167" width="30" height="15" isPrintWhenDetailOverflows="true"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Top">
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{banco.numeroFormatted}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="156" y="167" width="368" height="15"/>
				<textElement textAlignment="Center">
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{banco.linhaDigitavel}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="5" y="209" width="372" height="9"/>
				<textElement verticalAlignment="Bottom">
					<font fontName="Arial" size="7"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{boleto.cedente}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="5" y="192" width="372" height="9"/>
				<textElement verticalAlignment="Bottom">
					<font fontName="Arial" size="7"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{boleto.localPagamento}]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement x="5" y="226" width="66" height="9"/>
				<textElement verticalAlignment="Bottom">
					<font fontName="Arial" size="7"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{boleto.dataDocumento}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="155" y="226" width="51" height="9"/>
				<textElement textAlignment="Center" verticalAlignment="Bottom">
					<font fontName="Arial" size="7"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{boleto.especieDocumento}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="211" y="226" width="24" height="9"/>
				<textElement textAlignment="Center" verticalAlignment="Bottom">
					<font fontName="Arial" size="7"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{boleto.aceite}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="240" y="226" width="137" height="9"/>
				<textElement verticalAlignment="Bottom">
					<font fontName="Arial" size="7"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{boleto.dataProcessamento}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="79" y="243" width="40" height="9"/>
				<textElement textAlignment="Center" verticalAlignment="Bottom">
					<font fontName="Arial" size="7"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{boleto.carteira}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="434" y="192" width="80" height="9"/>
				<textElement textAlignment="Right" verticalAlignment="Bottom">
					<font fontName="Arial" size="7"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{boleto.dataVencimento}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="434" y="243" width="80" height="9"/>
				<textElement textAlignment="Right" verticalAlignment="Bottom">
					<font fontName="Arial" size="7"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{boleto.valorBoleto}]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement key="textField-49" x="386" y="227" width="128" height="9"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Bottom">
					<font fontName="Arial" size="7"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{boleto.nossoNumero}+($F{boleto.dvNossoNumero}.isEmpty() ? "" : ("-" + $F{boleto.dvNossoNumero}))]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="37" y="356" width="477" height="9"/>
				<textElement verticalAlignment="Bottom">
					<font fontName="Arial" size="7"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[($F{boleto.cidadeSacado}==null?"":$F{boleto.cidadeSacado}.trim() + "  " ) + ( $F{boleto.ufSacado}==null? "" : ( $F{boleto.ufSacado})+" "+($F{boleto.cepSacado}.trim() != null ? "CEP: "+$F{boleto.cepSacado}:""))]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="434" y="209" width="80" height="9"/>
				<textElement textAlignment="Right" verticalAlignment="Bottom">
					<font fontName="Arial" size="7"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{banco.agenciaCodCedenteFormatted}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-24" x="5" y="262" width="368" height="70"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Top">
					<font fontName="Arial" size="7"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{boleto.instrucao1}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="130" y="243" width="15" height="9"/>
				<textElement textAlignment="Center" verticalAlignment="Bottom">
					<font fontName="Arial" size="7"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA["R$"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="78" y="226" width="67" height="9"/>
				<textElement textAlignment="Center" verticalAlignment="Bottom">
					<font fontName="Arial" size="7"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{boleto.noDocumento}]]></textFieldExpression>
			</textField>
			<line>
				<reportElement key="line-15" x="171" y="235" width="1" height="17"/>
				<graphicElement>
					<pen lineWidth="1.0" lineStyle="Solid"/>
				</graphicElement>
			</line>
			<image scaleImage="FillFrame">
				<reportElement x="2" y="166" width="74" height="17"/>
				<imageExpression class="java.lang.String"><![CDATA[$P{SUBREPORT_DIR}  + "logos/logo-caixa.png"]]></imageExpression>
			</image>
			<textField>
				<reportElement x="156" y="2" width="368" height="15"/>
				<textElement textAlignment="Center">
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{banco.linhaDigitavel}]]></textFieldExpression>
			</textField>
			<line>
				<reportElement key="line-3" x="154" y="2" width="1" height="17"/>
				<graphicElement>
					<pen lineWidth="1.0" lineStyle="Solid"/>
				</graphicElement>
			</line>
			<image scaleImage="FillFrame">
				<reportElement x="2" y="1" width="74" height="17"/>
				<imageExpression class="java.lang.String"><![CDATA[$P{SUBREPORT_DIR}  + "logos/logo-caixa.png"]]></imageExpression>
			</image>
			<line>
				<reportElement key="line-2" x="120" y="2" width="1" height="17"/>
				<graphicElement>
					<pen lineWidth="1.0" lineStyle="Solid"/>
				</graphicElement>
			</line>
			<line>
				<reportElement key="line-1" x="1" y="19" width="526" height="1"/>
				<graphicElement>
					<pen lineWidth="1.0" lineStyle="Solid"/>
				</graphicElement>
			</line>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement key="textField-35" x="123" y="2" width="30" height="15" isPrintWhenDetailOverflows="true"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Top">
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{banco.numeroFormatted}]]></textFieldExpression>
			</textField>
			<line>
				<reportElement key="line-4" x="3" y="39" width="524" height="1"/>
				<graphicElement>
					<pen lineWidth="1.0" lineStyle="Solid"/>
				</graphicElement>
			</line>
			<staticText>
				<reportElement key="staticText-1" x="2" y="21" width="66" height="8"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="6" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Pagador]]></text>
			</staticText>
			<textField>
				<reportElement x="6" y="29" width="367" height="9"/>
				<textElement verticalAlignment="Bottom">
					<font fontName="Arial" size="7"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[(($F{boleto.responsavel} != null &&  !$F{boleto.responsavel}.isEmpty()) ? $F{boleto.responsavel} :  $F{boleto.nomeSacado})]]></textFieldExpression>
			</textField>
			<line>
				<reportElement key="line-8" x="378" y="19" width="1" height="60"/>
				<graphicElement>
					<pen lineWidth="1.0" lineStyle="Solid"/>
				</graphicElement>
			</line>
			<staticText>
				<reportElement key="staticText-7" x="221" y="41" width="48" height="8"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="6" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Vencimento]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-9" x="296" y="41" width="80" height="8"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="6" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Valor do Documento]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-3" x="2" y="41" width="66" height="8"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="6" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Nosso Número]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-6" x="116" y="41" width="66" height="8"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="6" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Nr. Documento]]></text>
			</staticText>
			<line>
				<reportElement key="line-15" x="217" y="41" width="1" height="17"/>
				<graphicElement>
					<pen lineWidth="1.0" lineStyle="Solid"/>
				</graphicElement>
			</line>
			<line>
				<reportElement key="line-18" x="293" y="41" width="1" height="17"/>
				<graphicElement>
					<pen lineWidth="1.0" lineStyle="Solid"/>
				</graphicElement>
			</line>
			<line>
				<reportElement key="line-4" x="3" y="59" width="524" height="1"/>
				<graphicElement>
					<pen lineWidth="1.0" lineStyle="Solid"/>
				</graphicElement>
			</line>
			<staticText>
				<reportElement key="staticText-3" x="380" y="21" width="66" height="8"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="6" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[CPF/CNPJ do Pagador]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-3" x="380" y="41" width="66" height="8"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="6" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Valor Cobrado]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-3" x="380" y="60" width="143" height="8"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="6" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[CPF/CNPJ do Beneficiário]]></text>
			</staticText>
			<line>
				<reportElement key="line-4" x="3" y="79" width="524" height="1"/>
				<graphicElement>
					<pen lineWidth="1.0" lineStyle="Solid"/>
				</graphicElement>
			</line>
			<line>
				<reportElement key="line-4" x="3" y="99" width="524" height="1"/>
				<graphicElement>
					<pen lineWidth="1.0" lineStyle="Solid"/>
				</graphicElement>
			</line>
			<staticText>
				<reportElement key="staticText-3" x="2" y="60" width="66" height="8"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="6" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Beneficiário]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-3" x="3" y="82" width="101" height="8"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="6" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Endereço do Beneficiário]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-3" x="3" y="100" width="101" height="8"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="6" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Agência/Código do Beneficiário]]></text>
			</staticText>
			<line>
				<reportElement key="line-8" x="332" y="100" width="1" height="59"/>
				<graphicElement>
					<pen lineWidth="1.0" lineStyle="Solid"/>
				</graphicElement>
			</line>
			<line>
				<reportElement key="line-4" x="3" y="119" width="329" height="1"/>
				<graphicElement>
					<pen lineWidth="1.0" lineStyle="Solid"/>
				</graphicElement>
			</line>
			<staticText>
				<reportElement key="staticText-26" x="334" y="102" width="190" height="8"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="6" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Autenticação mecânica - Recibo do Pagador]]></text>
			</staticText>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement key="textField-49" x="5" y="49" width="99" height="9"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Bottom">
					<font fontName="Arial" size="7"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{boleto.nossoNumero}+($F{boleto.dvNossoNumero}.isEmpty() ? "" : ("-" + $F{boleto.dvNossoNumero}))]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="296" y="49" width="80" height="9"/>
				<textElement textAlignment="Right" verticalAlignment="Bottom">
					<font fontName="Arial" size="7"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{boleto.valorBoleto}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="221" y="49" width="69" height="9"/>
				<textElement textAlignment="Right" verticalAlignment="Bottom">
					<font fontName="Arial" size="7"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{boleto.dataVencimento}]]></textFieldExpression>
			</textField>
			<line>
				<reportElement key="line-15" x="112" y="41" width="1" height="17"/>
				<graphicElement>
					<pen lineWidth="1.0" lineStyle="Solid"/>
				</graphicElement>
			</line>
			<textField>
				<reportElement x="118" y="49" width="95" height="9"/>
				<textElement textAlignment="Center" verticalAlignment="Bottom">
					<font fontName="Arial" size="7"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{boleto.noDocumento}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="7" y="69" width="366" height="9"/>
				<textElement verticalAlignment="Bottom">
					<font fontName="Arial" size="7"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{boleto.cedente}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="7" y="109" width="325" height="9"/>
				<textElement verticalAlignment="Bottom">
					<font fontName="Arial" size="7"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{banco.agenciaCodCedenteFormatted}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement key="staticText-26" x="5" y="122" width="61" height="8"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="6" isBold="true" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[SAC CAIXA:]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-26" x="68" y="122" width="254" height="8"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="6" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[0800 726 0101 (informações, reclamações, sugestões e elogios)]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-26" x="5" y="131" width="164" height="8"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="6" isBold="true" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Para pessoas com deficiência auditiva ou de fala:]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-26" x="171" y="131" width="151" height="8"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="6" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[0800 726 2492]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-26" x="5" y="140" width="164" height="8"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="6" isBold="true" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Ouvidoria:]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-26" x="171" y="140" width="151" height="8"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="6" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[0800 725 7474]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-26" x="5" y="149" width="317" height="8"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="6" isBold="true" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[caixa.gov.br]]></text>
			</staticText>
			<textField>
				<reportElement x="386" y="69" width="100" height="9"/>
				<textElement>
					<font fontName="Arial" size="7"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{cnpjEmpresa}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="386" y="29" width="141" height="9"/>
				<textElement verticalAlignment="Bottom">
					<font fontName="Arial" size="7"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{boleto.cpfSacado}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="7" y="91" width="479" height="9"/>
				<textElement>
					<font fontName="Arial" size="7"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{enderecoEmpresa}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="37" y="337" width="477" height="9"/>
				<textElement verticalAlignment="Bottom">
					<font fontName="Arial" size="7"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[(($F{boleto.responsavel} != null &&  !$F{boleto.responsavel}.isEmpty()) ? $F{boleto.responsavel} :  $F{boleto.nomeSacado}) + " / " + ($F{boleto.cpfSacado}.replace(".","").replace("-","").length()<12?"CPF: ":"CNPJ: ") + $F{boleto.cpfSacado}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="37" y="347" width="477" height="9"/>
				<textElement verticalAlignment="Bottom">
					<font fontName="Arial" size="7"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{boleto.enderecoSacado}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
</jasperReport>

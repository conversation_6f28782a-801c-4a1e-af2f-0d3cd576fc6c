<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="SaldosContas" pageWidth="625" pageHeight="100" columnWidth="625" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" whenResourceMissingType="Empty">
	<property name="ireport.zoom" value="2.415765000000045"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.encoding" value="ISO-8859-1"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="java.util.*"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<parameter name="tituloRelatorio" class="java.lang.String" isForPrompting="false"/>
	<parameter name="nomeEmpresa" class="java.lang.String" isForPrompting="false"/>
	<parameter name="versaoSoftware" class="java.lang.String" isForPrompting="false"/>
	<parameter name="usuario" class="java.lang.String" isForPrompting="false"/>
	<parameter name="filtros" class="java.lang.String" isForPrompting="false"/>
	<parameter name="SUBREPORT_DIR" class="java.lang.String">
		<defaultValueExpression><![CDATA["E:\\desenvolvimentosv\\Projeto Pacto Solucoes\\ZillyonWeb\\src\\java\\relatorio\\designRelatorio\\financeiro\\"]]></defaultValueExpression>
	</parameter>
	<parameter name="SUBREPORT_DIR1" class="java.lang.String">
		<defaultValueExpression><![CDATA["E:\\desenvolvimentosv\\Projeto Pacto Solucoes\\ZillyonWeb\\src\\java\\relatorio\\designRelatorio\\financeiro\\"]]></defaultValueExpression>
	</parameter>
	<parameter name="logoPadraoRelatorio" class="java.io.InputStream" isForPrompting="false"/>
	<parameter name="SUBREPORT_DIR2" class="java.lang.String">
		<defaultValueExpression><![CDATA["E:\\desenvolvimentosv\\Projeto Pacto Solucoes\\ZillyonWeb\\src\\java\\relatorio\\designRelatorio\\financeiro\\"]]></defaultValueExpression>
	</parameter>
	<parameter name="dataFim" class="java.lang.String"/>
	<parameter name="dataIni" class="java.lang.String"/>
	<parameter name="exibirAutorizacao" class="java.lang.Boolean"/>
	<field name="diaApresentar" class="java.lang.String"/>
	<field name="descricao" class="java.lang.String"/>
	<field name="movimentacao" class="java.lang.String"/>
	<field name="favorecido" class="java.lang.String"/>
	<field name="valorApresentar" class="java.lang.String"/>
	<columnHeader>
		<band height="20">
			<rectangle>
				<reportElement x="0" y="0" width="625" height="20" backcolor="#999999"/>
				<graphicElement>
					<pen lineWidth="0.0"/>
				</graphicElement>
			</rectangle>
			<staticText>
				<reportElement x="5" y="0" width="72" height="20" forecolor="#FFFFFF"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Dia | Hora]]></text>
			</staticText>
			<staticText>
				<reportElement x="77" y="0" width="164" height="20" forecolor="#FFFFFF"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Favorecido]]></text>
			</staticText>
			<staticText>
				<reportElement x="241" y="0" width="46" height="20" forecolor="#FFFFFF"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Mov.]]></text>
			</staticText>
			<staticText>
				<reportElement x="287" y="0" width="254" height="20" forecolor="#FFFFFF"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Descrição]]></text>
			</staticText>
			<staticText>
				<reportElement x="543" y="0" width="77" height="20" forecolor="#FFFFFF"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[E/S]]></text>
			</staticText>
		</band>
	</columnHeader>
	<detail>
		<band height="22">
			<textField>
				<reportElement x="5" y="4" width="72" height="12"/>
				<textElement>
					<font fontName="Arial"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{diaApresentar}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="287" y="4" width="254" height="12"/>
				<textElement>
					<font fontName="Arial"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{descricao}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="241" y="4" width="46" height="12"/>
				<textElement>
					<font fontName="Arial"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{movimentacao}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="77" y="4" width="164" height="12"/>
				<textElement>
					<font fontName="Arial"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{favorecido}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="543" y="4" width="77" height="12"/>
				<textElement textAlignment="Right">
					<font fontName="Arial"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{valorApresentar}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
</jasperReport>

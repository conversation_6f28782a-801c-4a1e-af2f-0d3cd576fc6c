<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="ParcelaEmAbertoRel" pageWidth="576" pageHeight="878" columnWidth="576" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" whenResourceMissingType="Empty">
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.encoding" value="ISO-8859-1"/>
	<property name="ireport.zoom" value="2.593742460100007"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="java.util.*"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<style name="Crosstab Data Text" hAlign="Center"/>
	<style name="table">
		<box>
			<pen lineWidth="1.0" lineColor="#000000"/>
		</box>
	</style>
	<style name="table_TH" mode="Opaque" backcolor="#FFFAFA">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="table_CH" mode="Opaque" backcolor="#FFBFBF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="table_TD" mode="Opaque" backcolor="#FFFFFF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<parameter name="logoPadraoRelatorio" class="java.io.InputStream" isForPrompting="false"/>
	<parameter name="tituloRelatorio" class="java.lang.String" isForPrompting="false"/>
	<parameter name="nomeEmpresa" class="java.lang.String" isForPrompting="false"/>
	<parameter name="versaoSoftware" class="java.lang.String" isForPrompting="false"/>
	<parameter name="usuario" class="java.lang.String" isForPrompting="false"/>
	<parameter name="filtros" class="java.lang.String" isForPrompting="false"/>
	<parameter name="SUBREPORT_DIR" class="java.lang.String">
		<defaultValueExpression><![CDATA["E:\\desenvolvimentosv\\Projeto Pacto Solucoes\\ZillyonWeb\\src\\java\\relatorio\\designRelatorio\\financeiro\\"]]></defaultValueExpression>
	</parameter>
	<parameter name="SUBREPORT_DIR1" class="java.lang.String">
		<defaultValueExpression><![CDATA["E:\\desenvolvimentosv\\Projeto Pacto Solucoes\\ZillyonWeb\\src\\java\\relatorio\\designRelatorio\\financeiro\\"]]></defaultValueExpression>
	</parameter>
	<parameter name="dataIni" class="java.lang.String" isForPrompting="false"/>
	<parameter name="dataFim" class="java.lang.String" isForPrompting="false"/>
	<parameter name="qtdAV" class="java.lang.String" isForPrompting="false"/>
	<parameter name="qtdCA" class="java.lang.String" isForPrompting="false"/>
	<parameter name="qtdChequeAV" class="java.lang.String" isForPrompting="false"/>
	<parameter name="qtdChequePR" class="java.lang.String" isForPrompting="false"/>
	<parameter name="qtdOutro" class="java.lang.String" isForPrompting="false"/>
	<parameter name="valorAV" class="java.lang.Double" isForPrompting="false"/>
	<parameter name="valorCA" class="java.lang.Double" isForPrompting="false"/>
	<parameter name="valorChequeAV" class="java.lang.Double" isForPrompting="false"/>
	<parameter name="valorChequePR" class="java.lang.Double" isForPrompting="false"/>
	<parameter name="valorOutro" class="java.lang.Double" isForPrompting="false"/>
	<parameter name="parametro1" class="java.lang.String" isForPrompting="false"/>
	<parameter name="parametro2" class="java.lang.String" isForPrompting="false"/>
	<parameter name="parametro3" class="java.lang.String" isForPrompting="false"/>
	<parameter name="parametro4" class="java.lang.String" isForPrompting="false"/>
	<parameter name="parametro5" class="java.lang.String" isForPrompting="false"/>
	<parameter name="parametro6" class="java.lang.String" isForPrompting="false"/>
	<field name="nomeModalidade" class="java.lang.String"/>
	<field name="percentagem_Apresentar" class="java.lang.String"/>
	<field name="valorPago_Apresentar" class="java.lang.String"/>
	<field name="dsTurmas" class="java.lang.Object"/>
	<field name="temTurma" class="java.lang.Boolean"/>
	<columnHeader>
		<band height="15">
			<staticText>
				<reportElement key="staticText-85" positionType="Float" mode="Opaque" x="1" y="0" width="64" height="13"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="9" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Modalidade]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-85" positionType="Float" mode="Opaque" x="123" y="0" width="60" height="13"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="9" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Fração Pg. %]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-85" positionType="Float" mode="Opaque" x="212" y="1" width="61" height="13"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="9" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Fração Pg. R$]]></text>
			</staticText>
		</band>
	</columnHeader>
	<detail>
		<band height="22" splitType="Stretch">
			<textField>
				<reportElement x="0" y="1" width="91" height="20"/>
				<textElement/>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{nomeModalidade}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="123" y="1" width="60" height="20"/>
				<textElement/>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{percentagem_Apresentar}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="212" y="1" width="61" height="20"/>
				<textElement/>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{valorPago_Apresentar}]]></textFieldExpression>
			</textField>
		</band>
		<band height="24">
			<printWhenExpression><![CDATA[$F{temTurma}]]></printWhenExpression>
			<subreport>
				<reportElement x="1" y="12" width="575" height="11">
					<printWhenExpression><![CDATA[$F{temTurma}]]></printWhenExpression>
				</reportElement>
				<subreportParameter name="logoPadraoRelatorio"/>
				<subreportParameter name="tituloRelatorio"/>
				<subreportParameter name="nomeEmpresa"/>
				<subreportParameter name="versaoSoftware"/>
				<subreportParameter name="usuario"/>
				<subreportParameter name="filtros"/>
				<subreportParameter name="SUBREPORT_DIR"/>
				<subreportParameter name="SUBREPORT_DIR1"/>
				<subreportParameter name="dataIni"/>
				<subreportParameter name="dataFim"/>
				<subreportParameter name="qtdAV"/>
				<subreportParameter name="qtdCA"/>
				<subreportParameter name="qtdChequeAV"/>
				<subreportParameter name="qtdChequePR"/>
				<subreportParameter name="qtdOutro"/>
				<subreportParameter name="valorAV"/>
				<subreportParameter name="valorCA"/>
				<subreportParameter name="valorChequeAV"/>
				<subreportParameter name="valorChequePR"/>
				<subreportParameter name="valorOutro"/>
				<subreportParameter name="parametro1"/>
				<subreportParameter name="parametro2"/>
				<subreportParameter name="parametro3"/>
				<subreportParameter name="parametro4"/>
				<subreportParameter name="parametro5"/>
				<subreportParameter name="parametro6"/>
				<dataSourceExpression><![CDATA[$F{dsTurmas}]]></dataSourceExpression>
				<subreportExpression class="java.lang.String"><![CDATA[$P{SUBREPORT_DIR} + "GestaoComissaoRelTurmas.jasper"]]></subreportExpression>
			</subreport>
			<staticText>
				<reportElement key="staticText-85" positionType="Float" mode="Opaque" x="1" y="0" width="122" height="13">
					<printWhenExpression><![CDATA[$F{temTurma}]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="9" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Turmas:]]></text>
			</staticText>
		</band>
	</detail>
</jasperReport>

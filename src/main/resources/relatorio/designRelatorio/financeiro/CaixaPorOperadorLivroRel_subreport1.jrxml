<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="CaixaPorOperadorLivroRel_subreport1" language="groovy" pageWidth="555" pageHeight="802" columnWidth="555" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0">
	<property name="ireport.zoom" value="1.0"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<field name="codFormaPagamento" class="java.lang.Integer"/>
	<field name="formaPagamento" class="java.lang.String"/>
	<field name="valorTotal" class="java.lang.Double"/>
	<columnHeader>
		<band height="23" splitType="Prevent">
			<rectangle>
				<reportElement x="0" y="0" width="380" height="23"/>
			</rectangle>
			<staticText>
				<reportElement key="staticText-85" positionType="Float" mode="Opaque" x="6" y="3" width="369" height="18"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="14" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[DETALHE DO SALDO]]></text>
			</staticText>
		</band>
	</columnHeader>
	<detail>
		<band height="24" splitType="Stretch">
			<rectangle>
				<reportElement x="0" y="0" width="380" height="24"/>
			</rectangle>
			<line>
				<reportElement x="238" y="0" width="1" height="24"/>
			</line>
			<textField>
				<reportElement key="staticText-85" positionType="Float" mode="Opaque" x="4" y="2" width="198" height="20"/>
				<textElement verticalAlignment="Middle" markup="none">
					<font fontName="Arial" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{formaPagamento}]]></textFieldExpression>
			</textField>
			<textField pattern="¤ #,##0.00">
				<reportElement key="staticText-85" positionType="Float" mode="Opaque" x="252" y="2" width="125" height="20" forecolor="#339900"/>
				<textElement textAlignment="Right" verticalAlignment="Middle" markup="none">
					<font fontName="Arial" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression class="java.lang.Double"><![CDATA[$F{valorTotal}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
</jasperReport>

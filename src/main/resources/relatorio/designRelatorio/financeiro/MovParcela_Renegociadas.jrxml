<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="ParcelasRenegociadas" pageWidth="615" pageHeight="535" orientation="Landscape" columnWidth="615" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0">
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.encoding" value="UTF-8"/>
	<property name="ireport.zoom" value="2.0"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="java.util.*"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<parameter name="SUBREPORT_DIR" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA["C:\\PactoJ\\Desenvolvimento\\Sistemas\\ZillyonWeb\\tronco-novo\\src\\main\\resources\\relatorio\\designRelatorio\\financeiro\\"]]></defaultValueExpression>
	</parameter>
	<parameter name="moeda" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA["R$"]]></defaultValueExpression>
	</parameter>
	<field name="descricao" class="java.lang.String"/>
	<field name="codigo" class="java.lang.Integer"/>
	<field name="valorParcela" class="java.lang.Double"/>
	<columnHeader>
		<band height="25">
			<textField>
				<reportElement x="9" y="5" width="176" height="20"/>
				<textElement>
					<font isBold="false" isItalic="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$R{Parcelas_Renegociadas_Recibo}]]></textFieldExpression>
			</textField>
		</band>
	</columnHeader>
	<detail>
		<band height="22" splitType="Stretch">
			<textField isBlankWhenNull="false">
				<reportElement key="textField-1" x="49" y="3" width="467" height="14"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="9"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[" "+$F{descricao}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-6" x="0" y="3" width="38" height="14">
					<printWhenExpression><![CDATA[$F{codigo} > 0]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="9"/>
				</textElement>
				<textFieldExpression class="java.lang.Integer"><![CDATA[$F{codigo}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00" isBlankWhenNull="false">
				<reportElement key="textField-1" x="552" y="3" width="61" height="14"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="9"/>
				</textElement>
				<textFieldExpression class="java.lang.Double"><![CDATA[$F{valorParcela}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="525" y="3" width="25" height="14"/>
				<textElement textAlignment="Right">
					<font size="9"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{moeda}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
</jasperReport>

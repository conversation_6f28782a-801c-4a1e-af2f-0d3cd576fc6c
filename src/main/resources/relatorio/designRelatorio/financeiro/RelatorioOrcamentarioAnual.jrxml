<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="RelatorioOrcamentarioSemestral" language="groovy" pageWidth="842" pageHeight="595" orientation="Landscape" columnWidth="802" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20">
	<property name="ireport.zoom" value="6.65500000000038"/>
	<property name="ireport.x" value="2181"/>
	<property name="ireport.y" value="527"/>
	<style name="corFundoRetangulo" mode="Opaque" forecolor="#00FFFF" backcolor="#00FFFF">
		<conditionalStyle>
			<conditionExpression><![CDATA[$F{nivelArvore}.intValue() == 1]]></conditionExpression>
			<style mode="Opaque" forecolor="#C0C0C0" backcolor="#C0C0C0">
				<pen lineColor="#C0C0C0"/>
			</style>
		</conditionalStyle>
		<conditionalStyle>
			<conditionExpression><![CDATA[$F{nivelArvore}.intValue() == 2]]></conditionExpression>
			<style mode="Opaque" forecolor="#CCFFCC" backcolor="#CCFFCC">
				<pen lineColor="#98FB98"/>
			</style>
		</conditionalStyle>
		<conditionalStyle>
			<conditionExpression><![CDATA[$F{nivelArvore}.intValue() == 3]]></conditionExpression>
			<style mode="Opaque" forecolor="#FFFFFF" backcolor="#FFFFFF">
				<pen lineColor="#FFFFFF"/>
			</style>
		</conditionalStyle>
		<conditionalStyle>
			<conditionExpression><![CDATA[$F{nivelArvore}.intValue() == 4]]></conditionExpression>
			<style mode="Opaque" forecolor="#ADD8E6" backcolor="#ADD8E6">
				<pen lineColor="#ADD8E6"/>
			</style>
		</conditionalStyle>
		<conditionalStyle>
			<conditionExpression><![CDATA[$F{nivelArvore}.intValue() == 5]]></conditionExpression>
			<style mode="Opaque" forecolor="#F5DEB3" backcolor="#F5DEB3">
				<pen lineColor="#F5DEB3"/>
			</style>
		</conditionalStyle>
	</style>
	<parameter name="logoPadraoRelatorio" class="java.io.InputStream"/>
	<parameter name="tituloRelatorio" class="java.lang.String"/>
	<parameter name="data1" class="java.lang.String"/>
	<parameter name="data2" class="java.lang.String"/>
	<parameter name="data3" class="java.lang.String"/>
	<parameter name="data4" class="java.lang.String"/>
	<parameter name="data5" class="java.lang.String"/>
	<parameter name="data6" class="java.lang.String"/>
	<parameter name="data7" class="java.lang.String"/>
	<parameter name="data8" class="java.lang.String"/>
	<parameter name="data9" class="java.lang.String"/>
	<parameter name="data10" class="java.lang.String"/>
	<parameter name="data11" class="java.lang.String"/>
	<parameter name="data12" class="java.lang.String"/>
	<parameter name="mes1TotalFimPaginaReceitasPrevisto" class="java.lang.String"/>
	<parameter name="mes1TotalFimPaginaReceitasRealizado" class="java.lang.String"/>
	<parameter name="mes1TotalFimPaginaDespesasPrevisto" class="java.lang.String"/>
	<parameter name="mes1TotalFimPaginaDespesasRealizado" class="java.lang.String"/>
	<parameter name="mes1TotalFimPaginaInvestimentosPrevisto" class="java.lang.String"/>
	<parameter name="mes1TotalFimPaginaInvestimentosRealizado" class="java.lang.String"/>
	<parameter name="mes1TotalFimPaginaInvestimentosDespesasPrevisto" class="java.lang.String"/>
	<parameter name="mes1TotalFimPaginaInvestimentosDespesasRealizado" class="java.lang.String"/>
	<parameter name="mes1TotalFimPaginaSemInvestimentosPrevisto" class="java.lang.String"/>
	<parameter name="mes1TotalFimPaginaSemInvestimentosRealizado" class="java.lang.String"/>
	<parameter name="mes1TotalFimPaginaComInvestimentosPrevisto" class="java.lang.String"/>
	<parameter name="mes1TotalFimPaginaComInvestimentosRealizado" class="java.lang.String"/>
	<parameter name="mes2TotalFimPaginaReceitasPrevisto" class="java.lang.String"/>
	<parameter name="mes2TotalFimPaginaReceitasRealizado" class="java.lang.String"/>
	<parameter name="mes2TotalFimPaginaDespesasPrevisto" class="java.lang.String"/>
	<parameter name="mes2TotalFimPaginaDespesasRealizado" class="java.lang.String"/>
	<parameter name="mes2TotalFimPaginaInvestimentosPrevisto" class="java.lang.String"/>
	<parameter name="mes2TotalFimPaginaInvestimentosRealizado" class="java.lang.String"/>
	<parameter name="mes2TotalFimPaginaInvestimentosDespesasPrevisto" class="java.lang.String"/>
	<parameter name="mes2TotalFimPaginaInvestimentosDespesasRealizado" class="java.lang.String"/>
	<parameter name="mes2TotalFimPaginaSemInvestimentosPrevisto" class="java.lang.String"/>
	<parameter name="mes2TotalFimPaginaSemInvestimentosRealizado" class="java.lang.String"/>
	<parameter name="mes2TotalFimPaginaComInvestimentosPrevisto" class="java.lang.String"/>
	<parameter name="mes2TotalFimPaginaComInvestimentosRealizado" class="java.lang.String"/>
	<parameter name="mes3TotalFimPaginaReceitasPrevisto" class="java.lang.String"/>
	<parameter name="mes3TotalFimPaginaReceitasRealizado" class="java.lang.String"/>
	<parameter name="mes3TotalFimPaginaDespesasPrevisto" class="java.lang.String"/>
	<parameter name="mes3TotalFimPaginaDespesasRealizado" class="java.lang.String"/>
	<parameter name="mes3TotalFimPaginaInvestimentosPrevisto" class="java.lang.String"/>
	<parameter name="mes3TotalFimPaginaInvestimentosRealizado" class="java.lang.String"/>
	<parameter name="mes3TotalFimPaginaInvestimentosDespesasPrevisto" class="java.lang.String"/>
	<parameter name="mes3TotalFimPaginaInvestimentosDespesasRealizado" class="java.lang.String"/>
	<parameter name="mes3TotalFimPaginaSemInvestimentosPrevisto" class="java.lang.String"/>
	<parameter name="mes3TotalFimPaginaSemInvestimentosRealizado" class="java.lang.String"/>
	<parameter name="mes3TotalFimPaginaComInvestimentosPrevisto" class="java.lang.String"/>
	<parameter name="mes3TotalFimPaginaComInvestimentosRealizado" class="java.lang.String"/>
	<parameter name="mes4TotalFimPaginaReceitasPrevisto" class="java.lang.String"/>
	<parameter name="mes4TotalFimPaginaReceitasRealizado" class="java.lang.String"/>
	<parameter name="mes4TotalFimPaginaDespesasPrevisto" class="java.lang.String"/>
	<parameter name="mes4TotalFimPaginaDespesasRealizado" class="java.lang.String"/>
	<parameter name="mes4TotalFimPaginaInvestimentosPrevisto" class="java.lang.String"/>
	<parameter name="mes4TotalFimPaginaInvestimentosRealizado" class="java.lang.String"/>
	<parameter name="mes4TotalFimPaginaInvestimentosDespesasPrevisto" class="java.lang.String"/>
	<parameter name="mes4TotalFimPaginaInvestimentosDespesasRealizado" class="java.lang.String"/>
	<parameter name="mes4TotalFimPaginaSemInvestimentosPrevisto" class="java.lang.String"/>
	<parameter name="mes4TotalFimPaginaSemInvestimentosRealizado" class="java.lang.String"/>
	<parameter name="mes4TotalFimPaginaComInvestimentosPrevisto" class="java.lang.String"/>
	<parameter name="mes4TotalFimPaginaComInvestimentosRealizado" class="java.lang.String"/>
	<parameter name="mes5TotalFimPaginaReceitasPrevisto" class="java.lang.String"/>
	<parameter name="mes5TotalFimPaginaReceitasRealizado" class="java.lang.String"/>
	<parameter name="mes5TotalFimPaginaDespesasPrevisto" class="java.lang.String"/>
	<parameter name="mes5TotalFimPaginaDespesasRealizado" class="java.lang.String"/>
	<parameter name="mes5TotalFimPaginaInvestimentosPrevisto" class="java.lang.String"/>
	<parameter name="mes5TotalFimPaginaInvestimentosRealizado" class="java.lang.String"/>
	<parameter name="mes5TotalFimPaginaInvestimentosDespesasPrevisto" class="java.lang.String"/>
	<parameter name="mes5TotalFimPaginaInvestimentosDespesasRealizado" class="java.lang.String"/>
	<parameter name="mes5TotalFimPaginaSemInvestimentosPrevisto" class="java.lang.String"/>
	<parameter name="mes5TotalFimPaginaSemInvestimentosRealizado" class="java.lang.String"/>
	<parameter name="mes5TotalFimPaginaComInvestimentosPrevisto" class="java.lang.String"/>
	<parameter name="mes5TotalFimPaginaComInvestimentosRealizado" class="java.lang.String"/>
	<parameter name="mes6TotalFimPaginaReceitasPrevisto" class="java.lang.String"/>
	<parameter name="mes6TotalFimPaginaReceitasRealizado" class="java.lang.String"/>
	<parameter name="mes6TotalFimPaginaDespesasPrevisto" class="java.lang.String"/>
	<parameter name="mes6TotalFimPaginaDespesasRealizado" class="java.lang.String"/>
	<parameter name="mes6TotalFimPaginaInvestimentosPrevisto" class="java.lang.String"/>
	<parameter name="mes6TotalFimPaginaInvestimentosRealizado" class="java.lang.String"/>
	<parameter name="mes6TotalFimPaginaInvestimentosDespesasPrevisto" class="java.lang.String"/>
	<parameter name="mes6TotalFimPaginaInvestimentosDespesasRealizado" class="java.lang.String"/>
	<parameter name="mes6TotalFimPaginaSemInvestimentosPrevisto" class="java.lang.String"/>
	<parameter name="mes6TotalFimPaginaSemInvestimentosRealizado" class="java.lang.String"/>
	<parameter name="mes6TotalFimPaginaComInvestimentosPrevisto" class="java.lang.String"/>
	<parameter name="mes6TotalFimPaginaComInvestimentosRealizado" class="java.lang.String"/>
	<parameter name="mes7TotalFimPaginaReceitasPrevisto" class="java.lang.String"/>
	<parameter name="mes7TotalFimPaginaReceitasRealizado" class="java.lang.String"/>
	<parameter name="mes7TotalFimPaginaDespesasPrevisto" class="java.lang.String"/>
	<parameter name="mes7TotalFimPaginaDespesasRealizado" class="java.lang.String"/>
	<parameter name="mes7TotalFimPaginaInvestimentosPrevisto" class="java.lang.String"/>
	<parameter name="mes7TotalFimPaginaInvestimentosRealizado" class="java.lang.String"/>
	<parameter name="mes7TotalFimPaginaInvestimentosDespesasPrevisto" class="java.lang.String"/>
	<parameter name="mes7TotalFimPaginaInvestimentosDespesasRealizado" class="java.lang.String"/>
	<parameter name="mes7TotalFimPaginaSemInvestimentosPrevisto" class="java.lang.String"/>
	<parameter name="mes7TotalFimPaginaSemInvestimentosRealizado" class="java.lang.String"/>
	<parameter name="mes7TotalFimPaginaComInvestimentosPrevisto" class="java.lang.String"/>
	<parameter name="mes7TotalFimPaginaComInvestimentosRealizado" class="java.lang.String"/>
	<parameter name="mes8TotalFimPaginaReceitasPrevisto" class="java.lang.String"/>
	<parameter name="mes8TotalFimPaginaReceitasRealizado" class="java.lang.String"/>
	<parameter name="mes8TotalFimPaginaDespesasPrevisto" class="java.lang.String"/>
	<parameter name="mes8TotalFimPaginaDespesasRealizado" class="java.lang.String"/>
	<parameter name="mes8TotalFimPaginaInvestimentosPrevisto" class="java.lang.String"/>
	<parameter name="mes8TotalFimPaginaInvestimentosRealizado" class="java.lang.String"/>
	<parameter name="mes8TotalFimPaginaInvestimentosDespesasPrevisto" class="java.lang.String"/>
	<parameter name="mes8TotalFimPaginaInvestimentosDespesasRealizado" class="java.lang.String"/>
	<parameter name="mes8TotalFimPaginaSemInvestimentosPrevisto" class="java.lang.String"/>
	<parameter name="mes8TotalFimPaginaSemInvestimentosRealizado" class="java.lang.String"/>
	<parameter name="mes8TotalFimPaginaComInvestimentosPrevisto" class="java.lang.String"/>
	<parameter name="mes8TotalFimPaginaComInvestimentosRealizado" class="java.lang.String"/>
	<parameter name="mes9TotalFimPaginaReceitasPrevisto" class="java.lang.String"/>
	<parameter name="mes9TotalFimPaginaReceitasRealizado" class="java.lang.String"/>
	<parameter name="mes9TotalFimPaginaDespesasPrevisto" class="java.lang.String"/>
	<parameter name="mes9TotalFimPaginaDespesasRealizado" class="java.lang.String"/>
	<parameter name="mes9TotalFimPaginaInvestimentosPrevisto" class="java.lang.String"/>
	<parameter name="mes9TotalFimPaginaInvestimentosRealizado" class="java.lang.String"/>
	<parameter name="mes9TotalFimPaginaInvestimentosDespesasPrevisto" class="java.lang.String"/>
	<parameter name="mes9TotalFimPaginaInvestimentosDespesasRealizado" class="java.lang.String"/>
	<parameter name="mes9TotalFimPaginaSemInvestimentosPrevisto" class="java.lang.String"/>
	<parameter name="mes9TotalFimPaginaSemInvestimentosRealizado" class="java.lang.String"/>
	<parameter name="mes9TotalFimPaginaComInvestimentosPrevisto" class="java.lang.String"/>
	<parameter name="mes9TotalFimPaginaComInvestimentosRealizado" class="java.lang.String"/>
	<parameter name="mes10TotalFimPaginaReceitasPrevisto" class="java.lang.String"/>
	<parameter name="mes10TotalFimPaginaReceitasRealizado" class="java.lang.String"/>
	<parameter name="mes10TotalFimPaginaDespesasPrevisto" class="java.lang.String"/>
	<parameter name="mes10TotalFimPaginaDespesasRealizado" class="java.lang.String"/>
	<parameter name="mes10TotalFimPaginaInvestimentosPrevisto" class="java.lang.String"/>
	<parameter name="mes10TotalFimPaginaInvestimentosRealizado" class="java.lang.String"/>
	<parameter name="mes10TotalFimPaginaInvestimentosDespesasPrevisto" class="java.lang.String"/>
	<parameter name="mes10TotalFimPaginaInvestimentosDespesasRealizado" class="java.lang.String"/>
	<parameter name="mes10TotalFimPaginaSemInvestimentosPrevisto" class="java.lang.String"/>
	<parameter name="mes10TotalFimPaginaSemInvestimentosRealizado" class="java.lang.String"/>
	<parameter name="mes10TotalFimPaginaComInvestimentosPrevisto" class="java.lang.String"/>
	<parameter name="mes10TotalFimPaginaComInvestimentosRealizado" class="java.lang.String"/>
	<parameter name="mes11TotalFimPaginaReceitasPrevisto" class="java.lang.String"/>
	<parameter name="mes11TotalFimPaginaReceitasRealizado" class="java.lang.String"/>
	<parameter name="mes11TotalFimPaginaDespesasPrevisto" class="java.lang.String"/>
	<parameter name="mes11TotalFimPaginaDespesasRealizado" class="java.lang.String"/>
	<parameter name="mes11TotalFimPaginaInvestimentosPrevisto" class="java.lang.String"/>
	<parameter name="mes11TotalFimPaginaInvestimentosRealizado" class="java.lang.String"/>
	<parameter name="mes11TotalFimPaginaInvestimentosDespesasPrevisto" class="java.lang.String"/>
	<parameter name="mes11TotalFimPaginaInvestimentosDespesasRealizado" class="java.lang.String"/>
	<parameter name="mes11TotalFimPaginaSemInvestimentosPrevisto" class="java.lang.String"/>
	<parameter name="mes11TotalFimPaginaSemInvestimentosRealizado" class="java.lang.String"/>
	<parameter name="mes11TotalFimPaginaComInvestimentosPrevisto" class="java.lang.String"/>
	<parameter name="mes11TotalFimPaginaComInvestimentosRealizado" class="java.lang.String"/>
	<parameter name="mes12TotalFimPaginaReceitasPrevisto" class="java.lang.String"/>
	<parameter name="mes12TotalFimPaginaReceitasRealizado" class="java.lang.String"/>
	<parameter name="mes12TotalFimPaginaDespesasPrevisto" class="java.lang.String"/>
	<parameter name="mes12TotalFimPaginaDespesasRealizado" class="java.lang.String"/>
	<parameter name="mes12TotalFimPaginaInvestimentosPrevisto" class="java.lang.String"/>
	<parameter name="mes12TotalFimPaginaInvestimentosRealizado" class="java.lang.String"/>
	<parameter name="mes12TotalFimPaginaInvestimentosDespesasPrevisto" class="java.lang.String"/>
	<parameter name="mes12TotalFimPaginaInvestimentosDespesasRealizado" class="java.lang.String"/>
	<parameter name="mes12TotalFimPaginaSemInvestimentosPrevisto" class="java.lang.String"/>
	<parameter name="mes12TotalFimPaginaSemInvestimentosRealizado" class="java.lang.String"/>
	<parameter name="mes12TotalFimPaginaComInvestimentosPrevisto" class="java.lang.String"/>
	<parameter name="mes12TotalFimPaginaComInvestimentosRealizado" class="java.lang.String"/>
	<parameter name="totalPrevistoRealizadoFimPaginaReceitasPrevisto" class="java.lang.String"/>
	<parameter name="totalPrevistoRealizadoFimPaginaReceitasRealizado" class="java.lang.String"/>
	<parameter name="totalPrevistoRealizadoFimPaginaDespesasPrevisto" class="java.lang.String"/>
	<parameter name="totalPrevistoRealizadoFimPaginaDespesasRealizado" class="java.lang.String"/>
	<parameter name="totalPrevistoRealizadoFimPaginaInvestimentosPrevisto" class="java.lang.String"/>
	<parameter name="totalPrevistoRealizadoFimPaginaInvestimentosRealizado" class="java.lang.String"/>
	<parameter name="totalPrevistoRealizadoFimPaginaInvestimentosDespesasPrevisto" class="java.lang.String"/>
	<parameter name="totalPrevistoRealizadoFimPaginaInvestimentosDespesasRealizado" class="java.lang.String"/>
	<parameter name="totalPrevistoRealizadoFimPaginaSemInvestimentosPrevisto" class="java.lang.String"/>
	<parameter name="totalPrevistoRealizadoFimPaginaSemInvestimentosRealizado" class="java.lang.String"/>
	<parameter name="totalPrevistoRealizadoFimPaginaComInvestimentosPrevisto" class="java.lang.String"/>
	<parameter name="totalPrevistoRealizadoFimPaginaComInvestimentosRealizado" class="java.lang.String"/>
	<parameter name="saldoFimPaginaReceitas" class="java.lang.String"/>
	<parameter name="saldoFimPaginaDespesas" class="java.lang.String"/>
	<parameter name="saldoFimPaginaInvestimentos" class="java.lang.String"/>
	<parameter name="saldoFimPaginaInvestimentosDespesas" class="java.lang.String"/>
	<parameter name="saldoFimPaginaSemInvestimentos" class="java.lang.String"/>
	<parameter name="saldoFimPaginaComInvestimentos" class="java.lang.String"/>
	<parameter name="variacaoFimPaginaReceitas" class="java.lang.String"/>
	<parameter name="variacaoFimPaginaDespesas" class="java.lang.String"/>
	<parameter name="variacaoFimPaginaInvestimentos" class="java.lang.String"/>
	<parameter name="variacaoFimPaginaInvestimentosDespesas" class="java.lang.String"/>
	<parameter name="variacaoFimPaginaSemInvestimentos" class="java.lang.String"/>
	<parameter name="variacaoFimPaginaComInvestimentos" class="java.lang.String"/>
	<field name="codigoAgrupador" class="java.lang.String"/>
	<field name="nomeAgrupador" class="java.lang.String"/>
	<field name="mes1.totalPrevistoMesString" class="java.lang.String"/>
	<field name="mes1.totalRealizadoMesString" class="java.lang.String"/>
	<field name="totalPrevistoString" class="java.lang.String"/>
	<field name="totalRealizadoString" class="java.lang.String"/>
	<field name="saldoFinalString" class="java.lang.String"/>
	<field name="percPretendidoString" class="java.lang.String"/>
	<field name="mes2.totalPrevistoMesString" class="java.lang.String"/>
	<field name="mes2.totalRealizadoMesString" class="java.lang.String"/>
	<field name="mes3.totalPrevistoMesString" class="java.lang.String"/>
	<field name="mes3.totalRealizadoMesString" class="java.lang.String"/>
	<field name="mes4.totalPrevistoMesString" class="java.lang.String"/>
	<field name="mes4.totalRealizadoMesString" class="java.lang.String"/>
	<field name="mes5.totalPrevistoMesString" class="java.lang.String"/>
	<field name="mes5.totalRealizadoMesString" class="java.lang.String"/>
	<field name="mes6.totalPrevistoMesString" class="java.lang.String"/>
	<field name="mes6.totalRealizadoMesString" class="java.lang.String"/>
	<field name="mes7.totalPrevistoMesString" class="java.lang.String"/>
	<field name="mes7.totalRealizadoMesString" class="java.lang.String"/>
	<field name="mes8.totalPrevistoMesString" class="java.lang.String"/>
	<field name="mes8.totalRealizadoMesString" class="java.lang.String"/>
	<field name="mes9.totalPrevistoMesString" class="java.lang.String"/>
	<field name="mes9.totalRealizadoMesString" class="java.lang.String"/>
	<field name="mes10.totalPrevistoMesString" class="java.lang.String"/>
	<field name="mes10.totalRealizadoMesString" class="java.lang.String"/>
	<field name="mes11.totalPrevistoMesString" class="java.lang.String"/>
	<field name="mes11.totalRealizadoMesString" class="java.lang.String"/>
	<field name="mes12.totalPrevistoMesString" class="java.lang.String"/>
	<field name="mes12.totalRealizadoMesString" class="java.lang.String"/>
	<field name="nivelArvore" class="java.lang.Integer"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<pageHeader>
		<band height="76" splitType="Stretch">
			<image isUsingCache="true" onErrorType="Blank" evaluationTime="Page">
				<reportElement x="2" y="1" width="82" height="36"/>
				<imageExpression class="java.io.InputStream"><![CDATA[$P{logoPadraoRelatorio}]]></imageExpression>
			</image>
			<textField>
				<reportElement x="84" y="1" width="718" height="36"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="14" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{tituloRelatorio}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="63" y="37" width="40" height="15"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="7" isBold="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{data1}]]></textFieldExpression>
			</textField>
			<rectangle>
				<reportElement x="724" y="51" width="28" height="25" forecolor="#999999" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<textField>
				<reportElement x="118" y="37" width="40" height="15"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="7" isBold="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{data2}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="174" y="37" width="40" height="15"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="7" isBold="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{data3}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="224" y="37" width="50" height="15"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="7" isBold="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{data4}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="285" y="37" width="40" height="15"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="7" isBold="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{data5}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="336" y="37" width="50" height="15"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="7" isBold="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{data6}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="391" y="37" width="50" height="15"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="7" isBold="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{data7}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="448" y="37" width="50" height="15"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="7" isBold="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{data8}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="501" y="37" width="50" height="15"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="7" isBold="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{data9}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="558" y="37" width="50" height="15"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="7" isBold="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{data10}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="614" y="37" width="50" height="15"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="7" isBold="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{data11}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="674" y="37" width="44" height="15"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="7" isBold="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{data12}]]></textFieldExpression>
			</textField>
			<rectangle>
				<reportElement x="52" y="51" width="28" height="25" forecolor="#999999" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="0" y="51" width="52" height="25" forecolor="#999999" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="80" y="51" width="28" height="25" forecolor="#999999" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<staticText>
				<reportElement x="53" y="54" width="26" height="20"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="5" isBold="true"/>
				</textElement>
				<text><![CDATA[Previsto]]></text>
			</staticText>
			<staticText>
				<reportElement x="81" y="54" width="26" height="20"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="5" isBold="true"/>
				</textElement>
				<text><![CDATA[Realizado]]></text>
			</staticText>
			<staticText>
				<reportElement x="4" y="54" width="44" height="20"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="6" isBold="true"/>
				</textElement>
				<text><![CDATA[Plano de Contas]]></text>
			</staticText>
			<staticText>
				<reportElement x="137" y="54" width="26" height="20"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="5" isBold="true"/>
				</textElement>
				<text><![CDATA[Realizado]]></text>
			</staticText>
			<rectangle>
				<reportElement x="136" y="51" width="28" height="25" forecolor="#999999" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="108" y="51" width="28" height="25" forecolor="#999999" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<staticText>
				<reportElement x="109" y="54" width="26" height="20"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="5" isBold="true"/>
				</textElement>
				<text><![CDATA[Previsto]]></text>
			</staticText>
			<staticText>
				<reportElement x="137" y="54" width="26" height="20"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="5" isBold="true"/>
				</textElement>
				<text><![CDATA[Realizado]]></text>
			</staticText>
			<staticText>
				<reportElement x="193" y="54" width="26" height="20"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="5" isBold="true"/>
				</textElement>
				<text><![CDATA[Realizado]]></text>
			</staticText>
			<rectangle>
				<reportElement x="192" y="51" width="28" height="25" forecolor="#999999" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="248" y="51" width="28" height="25" forecolor="#999999" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="220" y="51" width="28" height="25" forecolor="#999999" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<staticText>
				<reportElement x="249" y="54" width="26" height="20"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="5" isBold="true"/>
				</textElement>
				<text><![CDATA[Realizado]]></text>
			</staticText>
			<rectangle>
				<reportElement x="164" y="51" width="28" height="25" forecolor="#999999" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<staticText>
				<reportElement x="249" y="54" width="26" height="20"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="5" isBold="true"/>
				</textElement>
				<text><![CDATA[Realizado]]></text>
			</staticText>
			<staticText>
				<reportElement x="221" y="54" width="26" height="20"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="5" isBold="true"/>
				</textElement>
				<text><![CDATA[Previsto]]></text>
			</staticText>
			<staticText>
				<reportElement x="165" y="54" width="26" height="20"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="5" isBold="true"/>
				</textElement>
				<text><![CDATA[Previsto]]></text>
			</staticText>
			<staticText>
				<reportElement x="193" y="54" width="26" height="20"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="5" isBold="true"/>
				</textElement>
				<text><![CDATA[Realizado]]></text>
			</staticText>
			<staticText>
				<reportElement x="473" y="54" width="26" height="20"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="5" isBold="true"/>
				</textElement>
				<text><![CDATA[Realizado]]></text>
			</staticText>
			<staticText>
				<reportElement x="305" y="54" width="26" height="20"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="5" isBold="true"/>
				</textElement>
				<text><![CDATA[Realizado]]></text>
			</staticText>
			<rectangle>
				<reportElement x="304" y="51" width="28" height="25" forecolor="#999999" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<staticText>
				<reportElement x="445" y="54" width="26" height="20"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="5" isBold="true"/>
				</textElement>
				<text><![CDATA[Previsto]]></text>
			</staticText>
			<rectangle>
				<reportElement x="360" y="51" width="28" height="25" forecolor="#999999" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<staticText>
				<reportElement x="473" y="54" width="26" height="20"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="5" isBold="true"/>
				</textElement>
				<text><![CDATA[Realizado]]></text>
			</staticText>
			<staticText>
				<reportElement x="417" y="54" width="26" height="20"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="5" isBold="true"/>
				</textElement>
				<text><![CDATA[Realizado]]></text>
			</staticText>
			<rectangle>
				<reportElement x="472" y="51" width="28" height="25" forecolor="#999999" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="332" y="51" width="28" height="25" forecolor="#999999" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<staticText>
				<reportElement x="361" y="54" width="26" height="20"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="5" isBold="true"/>
				</textElement>
				<text><![CDATA[Realizado]]></text>
			</staticText>
			<staticText>
				<reportElement x="417" y="54" width="26" height="20"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="5" isBold="true"/>
				</textElement>
				<text><![CDATA[Realizado]]></text>
			</staticText>
			<staticText>
				<reportElement x="389" y="54" width="26" height="20"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="5" isBold="true"/>
				</textElement>
				<text><![CDATA[Previsto]]></text>
			</staticText>
			<rectangle>
				<reportElement x="276" y="51" width="28" height="25" forecolor="#999999" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="388" y="51" width="28" height="25" forecolor="#999999" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<staticText>
				<reportElement x="361" y="54" width="26" height="20"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="5" isBold="true"/>
				</textElement>
				<text><![CDATA[Realizado]]></text>
			</staticText>
			<staticText>
				<reportElement x="333" y="54" width="26" height="20"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="5" isBold="true"/>
				</textElement>
				<text><![CDATA[Previsto]]></text>
			</staticText>
			<rectangle>
				<reportElement x="416" y="51" width="28" height="25" forecolor="#999999" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<staticText>
				<reportElement x="277" y="54" width="26" height="20"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="5" isBold="true"/>
				</textElement>
				<text><![CDATA[Previsto]]></text>
			</staticText>
			<rectangle>
				<reportElement x="444" y="51" width="28" height="25" forecolor="#999999" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<staticText>
				<reportElement x="697" y="54" width="26" height="20"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="5" isBold="true"/>
				</textElement>
				<text><![CDATA[Realizado]]></text>
			</staticText>
			<staticText>
				<reportElement x="529" y="54" width="26" height="20"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="5" isBold="true"/>
				</textElement>
				<text><![CDATA[Realizado]]></text>
			</staticText>
			<rectangle>
				<reportElement x="528" y="51" width="28" height="25" forecolor="#999999" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<staticText>
				<reportElement x="669" y="54" width="26" height="20"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="5" isBold="true"/>
				</textElement>
				<text><![CDATA[Previsto]]></text>
			</staticText>
			<rectangle>
				<reportElement x="584" y="51" width="28" height="25" forecolor="#999999" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<staticText>
				<reportElement x="697" y="54" width="26" height="20"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="5" isBold="true"/>
				</textElement>
				<text><![CDATA[Realizado]]></text>
			</staticText>
			<staticText>
				<reportElement x="641" y="54" width="26" height="20"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="5" isBold="true"/>
				</textElement>
				<text><![CDATA[Realizado]]></text>
			</staticText>
			<rectangle>
				<reportElement x="696" y="51" width="28" height="25" forecolor="#999999" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="556" y="51" width="28" height="25" forecolor="#999999" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<staticText>
				<reportElement x="641" y="54" width="26" height="20"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="5" isBold="true"/>
				</textElement>
				<text><![CDATA[Realizado]]></text>
			</staticText>
			<staticText>
				<reportElement x="613" y="54" width="26" height="20"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="5" isBold="true"/>
				</textElement>
				<text><![CDATA[Previsto]]></text>
			</staticText>
			<rectangle>
				<reportElement x="500" y="51" width="28" height="25" forecolor="#999999" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="612" y="51" width="28" height="25" forecolor="#999999" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="640" y="51" width="28" height="25" forecolor="#999999" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="668" y="51" width="28" height="25" forecolor="#999999" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<staticText>
				<reportElement x="305" y="54" width="26" height="20"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="5" isBold="true"/>
				</textElement>
				<text><![CDATA[Realizado]]></text>
			</staticText>
			<staticText>
				<reportElement x="389" y="54" width="26" height="20"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="5" isBold="true"/>
				</textElement>
				<text><![CDATA[Previsto]]></text>
			</staticText>
			<staticText>
				<reportElement x="417" y="54" width="26" height="20"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="5" isBold="true"/>
				</textElement>
				<text><![CDATA[Realizado]]></text>
			</staticText>
			<staticText>
				<reportElement x="445" y="54" width="26" height="20"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="5" isBold="true"/>
				</textElement>
				<text><![CDATA[Previsto]]></text>
			</staticText>
			<staticText>
				<reportElement x="473" y="54" width="26" height="20"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="5" isBold="true"/>
				</textElement>
				<text><![CDATA[Realizado]]></text>
			</staticText>
			<staticText>
				<reportElement x="501" y="54" width="26" height="20"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="5" isBold="true"/>
				</textElement>
				<text><![CDATA[Previsto]]></text>
			</staticText>
			<staticText>
				<reportElement x="529" y="54" width="26" height="20"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="5" isBold="true"/>
				</textElement>
				<text><![CDATA[Realizado]]></text>
			</staticText>
			<staticText>
				<reportElement x="557" y="54" width="26" height="20"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="5" isBold="true"/>
				</textElement>
				<text><![CDATA[Previsto]]></text>
			</staticText>
			<staticText>
				<reportElement x="585" y="54" width="26" height="20"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="5" isBold="true"/>
				</textElement>
				<text><![CDATA[Realizado]]></text>
			</staticText>
			<staticText>
				<reportElement x="613" y="54" width="26" height="20"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="5" isBold="true"/>
				</textElement>
				<text><![CDATA[Previsto]]></text>
			</staticText>
			<staticText>
				<reportElement x="641" y="54" width="26" height="20"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="5" isBold="true"/>
				</textElement>
				<text><![CDATA[Realizado]]></text>
			</staticText>
			<staticText>
				<reportElement x="669" y="54" width="26" height="20"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="5" isBold="true"/>
				</textElement>
				<text><![CDATA[Previsto]]></text>
			</staticText>
			<staticText>
				<reportElement x="697" y="54" width="26" height="20"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="5" isBold="true"/>
				</textElement>
				<text><![CDATA[Realizado]]></text>
			</staticText>
			<staticText>
				<reportElement x="737" y="37" width="26" height="14"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[Total]]></text>
			</staticText>
			<rectangle>
				<reportElement x="776" y="51" width="26" height="25" forecolor="#999999" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<staticText>
				<reportElement x="725" y="54" width="26" height="20"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="5" isBold="true"/>
				</textElement>
				<text><![CDATA[Previsto]]></text>
			</staticText>
			<rectangle>
				<reportElement x="752" y="51" width="24" height="25" forecolor="#999999" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<staticText>
				<reportElement x="777" y="54" width="24" height="20"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="5" isBold="true"/>
				</textElement>
				<text><![CDATA[Saldo]]></text>
			</staticText>
			<staticText>
				<reportElement x="752" y="54" width="23" height="20"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="4" isBold="true"/>
				</textElement>
				<text><![CDATA[Realizado]]></text>
			</staticText>
		</band>
	</pageHeader>
	<detail>
		<band height="12" splitType="Stretch">
			<textField>
				<reportElement style="corFundoRetangulo" mode="Opaque" x="1" y="1" width="800" height="11"/>
				<textElement>
					<font size="5"/>
				</textElement>
				<textFieldExpression class="java.lang.Integer"><![CDATA[$F{nivelArvore}]]></textFieldExpression>
			</textField>
			<rectangle>
				<reportElement mode="Transparent" x="52" y="0" width="28" height="12" forecolor="#999999" backcolor="#999999"/>
				<graphicElement>
					<pen lineStyle="Solid" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<textField>
				<reportElement x="109" y="1" width="26" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{mes2.totalPrevistoMesString}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="137" y="1" width="26" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{mes2.totalRealizadoMesString}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="165" y="1" width="26" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{mes3.totalPrevistoMesString}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="194" y="1" width="26" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{mes3.totalRealizadoMesString}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="222" y="1" width="26" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{mes4.totalPrevistoMesString}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="250" y="1" width="26" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{mes4.totalRealizadoMesString}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="277" y="1" width="26" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{mes5.totalPrevistoMesString}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="305" y="1" width="26" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{mes5.totalRealizadoMesString}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="333" y="1" width="26" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{mes6.totalPrevistoMesString}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="389" y="1" width="26" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{mes7.totalPrevistoMesString}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="417" y="1" width="26" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{mes7.totalRealizadoMesString}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="445" y="1" width="26" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{mes8.totalPrevistoMesString}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="473" y="1" width="26" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{mes8.totalRealizadoMesString}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="501" y="1" width="26" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{mes9.totalPrevistoMesString}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="529" y="1" width="26" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{mes9.totalRealizadoMesString}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="557" y="1" width="26" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{mes10.totalPrevistoMesString}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="584" y="1" width="26" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{mes10.totalRealizadoMesString}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="613" y="1" width="26" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{mes11.totalPrevistoMesString}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="641" y="1" width="26" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{mes11.totalRealizadoMesString}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="669" y="1" width="26" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{mes12.totalPrevistoMesString}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="697" y="1" width="26" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{mes12.totalRealizadoMesString}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="725" y="1" width="26" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{totalPrevistoString}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="753" y="1" width="22" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{totalRealizadoString}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="777" y="1" width="24" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{saldoFinalString}]]></textFieldExpression>
			</textField>
			<rectangle>
				<reportElement mode="Transparent" x="80" y="0" width="28" height="12" forecolor="#999999" backcolor="#999999"/>
				<graphicElement>
					<pen lineStyle="Solid" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement mode="Transparent" x="108" y="0" width="28" height="12" forecolor="#999999" backcolor="#999999"/>
				<graphicElement>
					<pen lineStyle="Solid" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement mode="Transparent" x="136" y="0" width="28" height="12" forecolor="#999999" backcolor="#999999"/>
				<graphicElement>
					<pen lineStyle="Solid" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement mode="Transparent" x="164" y="0" width="28" height="12" forecolor="#999999" backcolor="#999999"/>
				<graphicElement>
					<pen lineStyle="Solid" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement mode="Transparent" x="248" y="0" width="28" height="12" forecolor="#999999" backcolor="#999999"/>
				<graphicElement>
					<pen lineStyle="Solid" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement mode="Transparent" x="192" y="0" width="28" height="12" forecolor="#999999" backcolor="#999999"/>
				<graphicElement>
					<pen lineStyle="Solid" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement mode="Transparent" x="220" y="0" width="28" height="12" forecolor="#999999" backcolor="#999999"/>
				<graphicElement>
					<pen lineStyle="Solid" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<textField>
				<reportElement x="81" y="1" width="26" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{mes1.totalRealizadoMesString}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="53" y="1" width="26" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{mes1.totalPrevistoMesString}]]></textFieldExpression>
			</textField>
			<rectangle>
				<reportElement mode="Transparent" x="304" y="0" width="28" height="12" forecolor="#999999" backcolor="#999999"/>
				<graphicElement>
					<pen lineStyle="Solid" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement mode="Transparent" x="332" y="0" width="28" height="12" forecolor="#999999" backcolor="#999999"/>
				<graphicElement>
					<pen lineStyle="Solid" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement mode="Transparent" x="444" y="0" width="28" height="12" forecolor="#999999" backcolor="#999999"/>
				<graphicElement>
					<pen lineStyle="Solid" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement mode="Transparent" x="416" y="0" width="28" height="12" forecolor="#999999" backcolor="#999999"/>
				<graphicElement>
					<pen lineStyle="Solid" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement mode="Transparent" x="472" y="0" width="28" height="12" forecolor="#999999" backcolor="#999999"/>
				<graphicElement>
					<pen lineStyle="Solid" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement mode="Transparent" x="500" y="0" width="28" height="12" forecolor="#999999" backcolor="#999999"/>
				<graphicElement>
					<pen lineStyle="Solid" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement mode="Transparent" x="696" y="0" width="28" height="12" forecolor="#999999" backcolor="#999999"/>
				<graphicElement>
					<pen lineStyle="Solid" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement mode="Transparent" x="668" y="0" width="28" height="12" forecolor="#999999" backcolor="#999999"/>
				<graphicElement>
					<pen lineStyle="Solid" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement mode="Transparent" x="640" y="0" width="28" height="12" forecolor="#999999" backcolor="#999999"/>
				<graphicElement>
					<pen lineStyle="Solid" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement mode="Transparent" x="612" y="0" width="28" height="12" forecolor="#999999" backcolor="#999999"/>
				<graphicElement>
					<pen lineStyle="Solid" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement mode="Transparent" x="584" y="0" width="28" height="12" forecolor="#999999" backcolor="#999999"/>
				<graphicElement>
					<pen lineStyle="Solid" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement mode="Transparent" x="556" y="0" width="28" height="12" forecolor="#999999" backcolor="#999999"/>
				<graphicElement>
					<pen lineStyle="Solid" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement mode="Transparent" x="528" y="0" width="28" height="12" forecolor="#999999" backcolor="#999999"/>
				<graphicElement>
					<pen lineStyle="Solid" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement mode="Transparent" x="752" y="0" width="24" height="12" forecolor="#999999" backcolor="#999999"/>
				<graphicElement>
					<pen lineStyle="Solid" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement mode="Transparent" x="724" y="0" width="28" height="12" forecolor="#999999" backcolor="#999999"/>
				<graphicElement>
					<pen lineStyle="Solid" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement mode="Transparent" x="776" y="0" width="26" height="12" forecolor="#999999" backcolor="#999999"/>
				<graphicElement>
					<pen lineStyle="Solid" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement mode="Transparent" x="276" y="0" width="28" height="12" forecolor="#999999" backcolor="#999999"/>
				<graphicElement>
					<pen lineStyle="Solid" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement mode="Transparent" x="388" y="0" width="28" height="12" forecolor="#999999" backcolor="#999999"/>
				<graphicElement>
					<pen lineStyle="Solid" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement mode="Transparent" x="0" y="0" width="52" height="12" forecolor="#999999" backcolor="#999999"/>
				<graphicElement>
					<pen lineStyle="Solid" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<textField>
				<reportElement x="1" y="1" width="50" height="10"/>
				<textElement verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{nomeAgrupador}]]></textFieldExpression>
			</textField>
			<rectangle>
				<reportElement mode="Transparent" x="360" y="0" width="28" height="12" forecolor="#999999" backcolor="#999999"/>
				<graphicElement>
					<pen lineStyle="Solid" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<textField>
				<reportElement x="361" y="1" width="26" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{mes6.totalRealizadoMesString}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<summary>
		<band height="119">
			<rectangle>
				<reportElement x="264" y="24" width="28" height="12" forecolor="#999999" backcolor="#CCCCCC"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="180" y="24" width="28" height="12" forecolor="#999999" backcolor="#CCCCCC"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="712" y="24" width="28" height="12" forecolor="#999999" backcolor="#CCCCCC"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="432" y="24" width="28" height="12" forecolor="#999999" backcolor="#CCCCCC"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="488" y="24" width="28" height="12" forecolor="#999999" backcolor="#CCCCCC"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="292" y="24" width="28" height="12" forecolor="#999999" backcolor="#CCCCCC"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="96" y="24" width="28" height="12" forecolor="#999999" backcolor="#CCCCCC"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="68" y="24" width="28" height="12" forecolor="#999999" backcolor="#CCCCCC"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="236" y="24" width="28" height="12" forecolor="#999999" backcolor="#CCCCCC"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="208" y="24" width="28" height="12" forecolor="#999999" backcolor="#CCCCCC"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="320" y="24" width="28" height="12" forecolor="#999999" backcolor="#CCCCCC"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="124" y="24" width="28" height="12" forecolor="#999999" backcolor="#CCCCCC"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="740" y="24" width="28" height="12" forecolor="#999999" backcolor="#CCCCCC"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="0" y="24" width="40" height="12" forecolor="#999999" backcolor="#CCCCCC"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="348" y="24" width="28" height="12" forecolor="#999999" backcolor="#CCCCCC"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="152" y="24" width="28" height="12" forecolor="#999999" backcolor="#CCCCCC"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<staticText>
				<reportElement x="70" y="25" width="25" height="10"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="5" isBold="true"/>
				</textElement>
				<text><![CDATA[Realizado]]></text>
			</staticText>
			<staticText>
				<reportElement x="293" y="25" width="28" height="10"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="5" isBold="true"/>
				</textElement>
				<text><![CDATA[Realizado]]></text>
			</staticText>
			<staticText>
				<reportElement x="41" y="25" width="26" height="10"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="5" isBold="true"/>
				</textElement>
				<text><![CDATA[Previsto]]></text>
			</staticText>
			<staticText>
				<reportElement x="728" y="8" width="25" height="15"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[Total]]></text>
			</staticText>
			<textField>
				<reportElement x="51" y="8" width="40" height="15"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="7" isBold="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{data1}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="321" y="8" width="52" height="15"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="7" isBold="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{data6}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="777" y="8" width="20" height="15"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[Saldo]]></text>
			</staticText>
			<staticText>
				<reportElement x="321" y="25" width="28" height="10"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="5" isBold="true"/>
				</textElement>
				<text><![CDATA[Previsto]]></text>
			</staticText>
			<staticText>
				<reportElement x="153" y="25" width="28" height="10"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="5" isBold="true"/>
				</textElement>
				<text><![CDATA[Previsto]]></text>
			</staticText>
			<textField>
				<reportElement x="217" y="8" width="40" height="15"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="7" isBold="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{data4}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="713" y="25" width="26" height="10"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="5" isBold="true"/>
				</textElement>
				<text><![CDATA[Previsto]]></text>
			</staticText>
			<staticText>
				<reportElement x="265" y="25" width="28" height="10"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="5" isBold="true"/>
				</textElement>
				<text><![CDATA[Previsto]]></text>
			</staticText>
			<textField>
				<reportElement x="106" y="8" width="40" height="15"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="7" isBold="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{data2}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="237" y="25" width="28" height="10"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="5" isBold="true"/>
				</textElement>
				<text><![CDATA[Realizado]]></text>
			</staticText>
			<staticText>
				<reportElement x="125" y="25" width="28" height="10"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="5" isBold="true"/>
				</textElement>
				<text><![CDATA[Realizado]]></text>
			</staticText>
			<staticText>
				<reportElement x="97" y="25" width="28" height="10"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="5" isBold="true"/>
				</textElement>
				<text><![CDATA[Previsto]]></text>
			</staticText>
			<staticText>
				<reportElement x="181" y="25" width="28" height="10"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="5" isBold="true"/>
				</textElement>
				<text><![CDATA[Realizado]]></text>
			</staticText>
			<textField>
				<reportElement x="160" y="8" width="40" height="15"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="7" isBold="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{data3}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="348" y="25" width="28" height="10"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="5" isBold="true"/>
				</textElement>
				<text><![CDATA[Realizado]]></text>
			</staticText>
			<staticText>
				<reportElement x="1" y="25" width="38" height="10"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="5" isBold="true"/>
				</textElement>
				<text><![CDATA[Resumo Geral]]></text>
			</staticText>
			<textField>
				<reportElement x="272" y="8" width="40" height="15"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="7" isBold="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{data5}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="209" y="25" width="28" height="10"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="5" isBold="true"/>
				</textElement>
				<text><![CDATA[Previsto]]></text>
			</staticText>
			<staticText>
				<reportElement x="741" y="25" width="26" height="10"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="5" isBold="true"/>
				</textElement>
				<text><![CDATA[Realizado]]></text>
			</staticText>
			<rectangle>
				<reportElement x="768" y="36" width="33" height="11" forecolor="#999999" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="180" y="36" width="28" height="12" forecolor="#999999" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="40" y="36" width="28" height="12" forecolor="#999999" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="712" y="36" width="28" height="12" forecolor="#999999" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="292" y="36" width="28" height="12" forecolor="#999999" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="264" y="36" width="28" height="12" forecolor="#999999" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="96" y="36" width="28" height="12" forecolor="#999999" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="68" y="36" width="28" height="12" forecolor="#999999" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="236" y="36" width="28" height="12" forecolor="#999999" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="208" y="36" width="28" height="12" forecolor="#999999" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="320" y="36" width="28" height="12" forecolor="#999999" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="124" y="36" width="28" height="12" forecolor="#999999" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="740" y="36" width="28" height="12" forecolor="#999999" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="0" y="36" width="40" height="12" forecolor="#999999" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="348" y="36" width="28" height="12" forecolor="#999999" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="152" y="36" width="28" height="12" forecolor="#999999" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="712" y="47" width="28" height="12" forecolor="#999999" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="236" y="47" width="28" height="12" forecolor="#999999" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="180" y="47" width="28" height="12" forecolor="#999999" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="152" y="47" width="28" height="12" forecolor="#999999" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="264" y="47" width="28" height="12" forecolor="#999999" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="320" y="47" width="28" height="12" forecolor="#999999" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="208" y="47" width="28" height="12" forecolor="#999999" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="68" y="47" width="28" height="12" forecolor="#999999" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="96" y="47" width="28" height="12" forecolor="#999999" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="292" y="47" width="28" height="12" forecolor="#999999" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="740" y="47" width="28" height="12" forecolor="#999999" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="40" y="47" width="28" height="12" forecolor="#999999" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="348" y="47" width="28" height="12" forecolor="#999999" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="124" y="47" width="28" height="12" forecolor="#999999" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="0" y="47" width="40" height="12" forecolor="#999999" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="768" y="47" width="33" height="12" forecolor="#999999" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="712" y="59" width="28" height="12" forecolor="#999999" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="236" y="59" width="28" height="12" forecolor="#999999" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="180" y="59" width="28" height="12" forecolor="#999999" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="152" y="59" width="28" height="12" forecolor="#999999" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="264" y="59" width="28" height="12" forecolor="#999999" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="320" y="59" width="28" height="12" forecolor="#999999" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="208" y="59" width="28" height="12" forecolor="#999999" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="68" y="59" width="28" height="12" forecolor="#999999" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="96" y="59" width="28" height="12" forecolor="#999999" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="292" y="59" width="28" height="12" forecolor="#999999" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="740" y="59" width="28" height="12" forecolor="#999999" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="40" y="59" width="28" height="12" forecolor="#999999" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="348" y="59" width="28" height="12" forecolor="#999999" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="124" y="59" width="28" height="12" forecolor="#999999" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="0" y="59" width="40" height="12" forecolor="#999999" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="768" y="59" width="33" height="12" forecolor="#999999" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="712" y="71" width="28" height="12" forecolor="#999999" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="236" y="71" width="28" height="12" forecolor="#999999" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="180" y="71" width="28" height="12" forecolor="#999999" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="152" y="71" width="28" height="12" forecolor="#999999" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="264" y="71" width="28" height="12" forecolor="#999999" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="320" y="71" width="28" height="12" forecolor="#999999" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="208" y="71" width="28" height="12" forecolor="#999999" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="68" y="71" width="28" height="12" forecolor="#999999" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="96" y="71" width="28" height="12" forecolor="#999999" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="292" y="71" width="28" height="12" forecolor="#999999" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="740" y="71" width="28" height="12" forecolor="#999999" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="40" y="71" width="28" height="12" forecolor="#999999" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="348" y="71" width="28" height="12" forecolor="#999999" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="124" y="71" width="28" height="12" forecolor="#999999" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="0" y="71" width="40" height="12" forecolor="#999999" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="768" y="71" width="33" height="12" forecolor="#999999" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="712" y="83" width="28" height="12" forecolor="#999999" backcolor="#CCFFCC"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="236" y="83" width="28" height="12" forecolor="#999999" backcolor="#CCFFCC"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="180" y="83" width="28" height="12" forecolor="#999999" backcolor="#CCFFCC"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="152" y="83" width="28" height="12" forecolor="#999999" backcolor="#CCFFCC"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="264" y="83" width="28" height="12" forecolor="#999999" backcolor="#CCFFCC"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="320" y="83" width="28" height="12" forecolor="#999999" backcolor="#CCFFCC"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="208" y="83" width="28" height="12" forecolor="#999999" backcolor="#CCFFCC"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="68" y="83" width="28" height="12" forecolor="#999999" backcolor="#CCFFCC"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="96" y="83" width="28" height="12" forecolor="#999999" backcolor="#CCFFCC"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="292" y="83" width="28" height="12" forecolor="#999999" backcolor="#CCFFCC"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="740" y="83" width="28" height="12" forecolor="#999999" backcolor="#CCFFCC"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="40" y="83" width="28" height="12" forecolor="#999999" backcolor="#CCFFCC"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="348" y="83" width="28" height="12" forecolor="#999999" backcolor="#CCFFCC"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="124" y="83" width="28" height="12" forecolor="#999999" backcolor="#CCFFCC"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="0" y="83" width="40" height="12" forecolor="#999999" backcolor="#CCFFCC"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="768" y="83" width="33" height="12" forecolor="#999999" backcolor="#CCFFCC"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="180" y="95" width="28" height="12" forecolor="#999999" backcolor="#CCFFCC"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="320" y="95" width="28" height="12" forecolor="#999999" backcolor="#CCFFCC"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="40" y="95" width="28" height="12" forecolor="#999999" backcolor="#CCFFCC"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="768" y="95" width="33" height="12" forecolor="#999999" backcolor="#CCFFCC"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="292" y="95" width="28" height="12" forecolor="#999999" backcolor="#CCFFCC"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="264" y="95" width="28" height="12" forecolor="#999999" backcolor="#CCFFCC"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="152" y="95" width="28" height="12" forecolor="#999999" backcolor="#CCFFCC"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="712" y="95" width="28" height="12" forecolor="#999999" backcolor="#CCFFCC"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="348" y="95" width="28" height="12" forecolor="#999999" backcolor="#CCFFCC"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="124" y="95" width="28" height="12" forecolor="#999999" backcolor="#CCFFCC"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="740" y="95" width="28" height="12" forecolor="#999999" backcolor="#CCFFCC"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="68" y="95" width="28" height="12" forecolor="#999999" backcolor="#CCFFCC"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="236" y="95" width="28" height="12" forecolor="#999999" backcolor="#CCFFCC"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="96" y="95" width="28" height="12" forecolor="#999999" backcolor="#CCFFCC"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="208" y="95" width="28" height="12" forecolor="#999999" backcolor="#CCFFCC"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="0" y="95" width="40" height="12" forecolor="#999999" backcolor="#CCFFCC"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="180" y="107" width="28" height="12" forecolor="#999999" backcolor="#CCFFCC"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="320" y="107" width="28" height="12" forecolor="#999999" backcolor="#CCFFCC"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="40" y="107" width="28" height="12" forecolor="#999999" backcolor="#CCFFCC"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="768" y="107" width="33" height="12" forecolor="#999999" backcolor="#CCFFCC"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="292" y="107" width="28" height="12" forecolor="#999999" backcolor="#CCFFCC"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="264" y="107" width="28" height="12" forecolor="#999999" backcolor="#CCFFCC"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="152" y="107" width="28" height="12" forecolor="#999999" backcolor="#CCFFCC"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="712" y="107" width="28" height="12" forecolor="#999999" backcolor="#CCFFCC"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="348" y="107" width="28" height="12" forecolor="#999999" backcolor="#CCFFCC"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="124" y="107" width="28" height="12" forecolor="#999999" backcolor="#CCFFCC"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="740" y="107" width="28" height="12" forecolor="#999999" backcolor="#CCFFCC"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="68" y="107" width="28" height="12" forecolor="#999999" backcolor="#CCFFCC"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="236" y="107" width="28" height="12" forecolor="#999999" backcolor="#CCFFCC"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="96" y="107" width="28" height="12" forecolor="#999999" backcolor="#CCFFCC"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="208" y="107" width="28" height="12" forecolor="#999999" backcolor="#CCFFCC"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="0" y="107" width="40" height="12" forecolor="#999999" backcolor="#CCFFCC"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<staticText>
				<reportElement x="1" y="84" width="38" height="9"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="5" isBold="true"/>
				</textElement>
				<text><![CDATA[Resultado EconÃ´mico]]></text>
			</staticText>
			<textField>
				<reportElement x="69" y="37" width="26" height="9"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes1TotalFimPaginaReceitasRealizado}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="41" y="37" width="26" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes1TotalFimPaginaReceitasPrevisto}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="1" y="37" width="38" height="10"/>
				<textElement verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<text><![CDATA[Receitas]]></text>
			</staticText>
			<staticText>
				<reportElement x="1" y="48" width="38" height="10"/>
				<textElement verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<text><![CDATA[Despesas]]></text>
			</staticText>
			<staticText>
				<reportElement x="1" y="60" width="38" height="10"/>
				<textElement verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<text><![CDATA[Investimentos]]></text>
			</staticText>
			<staticText>
				<reportElement x="1" y="72" width="38" height="10"/>
				<textElement verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<text><![CDATA[Inv + Despesas]]></text>
			</staticText>
			<staticText>
				<reportElement x="1" y="96" width="38" height="10"/>
				<textElement verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<text><![CDATA[Sem Investim.]]></text>
			</staticText>
			<staticText>
				<reportElement x="1" y="108" width="38" height="10"/>
				<textElement verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<text><![CDATA[Com Investim.]]></text>
			</staticText>
			<textField>
				<reportElement x="41" y="48" width="26" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes1TotalFimPaginaDespesasPrevisto}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="69" y="48" width="26" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes1TotalFimPaginaDespesasRealizado}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="41" y="60" width="26" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes1TotalFimPaginaInvestimentosPrevisto}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="69" y="60" width="26" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes1TotalFimPaginaInvestimentosRealizado}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="41" y="72" width="26" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes1TotalFimPaginaInvestimentosDespesasPrevisto}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="69" y="72" width="26" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes1TotalFimPaginaInvestimentosDespesasRealizado}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="41" y="96" width="26" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes1TotalFimPaginaSemInvestimentosPrevisto}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="69" y="96" width="26" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes1TotalFimPaginaSemInvestimentosRealizado}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="41" y="108" width="26" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes1TotalFimPaginaComInvestimentosPrevisto}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="69" y="108" width="26" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes1TotalFimPaginaComInvestimentosRealizado}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="97" y="37" width="26" height="9"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes2TotalFimPaginaReceitasPrevisto}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="125" y="37" width="26" height="9"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes2TotalFimPaginaReceitasRealizado}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="97" y="48" width="26" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes2TotalFimPaginaDespesasPrevisto}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="125" y="48" width="26" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes2TotalFimPaginaDespesasRealizado}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="97" y="60" width="26" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes2TotalFimPaginaInvestimentosPrevisto}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="125" y="60" width="26" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes2TotalFimPaginaInvestimentosRealizado}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="97" y="72" width="26" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes2TotalFimPaginaInvestimentosDespesasPrevisto}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="125" y="72" width="26" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes2TotalFimPaginaInvestimentosDespesasRealizado}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="97" y="96" width="26" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes2TotalFimPaginaSemInvestimentosPrevisto}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="125" y="96" width="26" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes2TotalFimPaginaSemInvestimentosRealizado}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="97" y="108" width="26" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes2TotalFimPaginaComInvestimentosPrevisto}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="125" y="108" width="26" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes2TotalFimPaginaComInvestimentosRealizado}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="153" y="37" width="26" height="9"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes3TotalFimPaginaReceitasPrevisto}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="181" y="37" width="26" height="9"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes3TotalFimPaginaReceitasRealizado}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="153" y="48" width="26" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes3TotalFimPaginaDespesasPrevisto}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="181" y="48" width="26" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes3TotalFimPaginaDespesasRealizado}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="153" y="60" width="26" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes3TotalFimPaginaInvestimentosPrevisto}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="181" y="60" width="26" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes3TotalFimPaginaInvestimentosRealizado}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="153" y="72" width="26" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes3TotalFimPaginaInvestimentosDespesasPrevisto}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="181" y="72" width="26" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes3TotalFimPaginaInvestimentosDespesasRealizado}]]></textFieldExpression>
			</textField>
			<rectangle>
				<reportElement x="404" y="59" width="28" height="12" forecolor="#999999" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<textField>
				<reportElement x="153" y="96" width="26" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes3TotalFimPaginaSemInvestimentosPrevisto}]]></textFieldExpression>
			</textField>
			<rectangle>
				<reportElement x="404" y="71" width="28" height="12" forecolor="#999999" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<textField>
				<reportElement x="181" y="96" width="26" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes3TotalFimPaginaSemInvestimentosRealizado}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="153" y="108" width="26" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes3TotalFimPaginaComInvestimentosPrevisto}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="181" y="108" width="26" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes3TotalFimPaginaComInvestimentosRealizado}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="209" y="37" width="26" height="9"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes4TotalFimPaginaReceitasPrevisto}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="237" y="37" width="26" height="9"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes4TotalFimPaginaReceitasRealizado}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="209" y="48" width="26" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes4TotalFimPaginaDespesasPrevisto}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="237" y="48" width="26" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes4TotalFimPaginaDespesasRealizado}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="209" y="60" width="26" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes4TotalFimPaginaInvestimentosPrevisto}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="237" y="60" width="26" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes4TotalFimPaginaInvestimentosRealizado}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="209" y="72" width="26" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes4TotalFimPaginaInvestimentosDespesasPrevisto}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="237" y="72" width="26" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes4TotalFimPaginaInvestimentosDespesasRealizado}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="209" y="96" width="26" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes4TotalFimPaginaSemInvestimentosPrevisto}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="237" y="96" width="26" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes4TotalFimPaginaSemInvestimentosRealizado}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="209" y="108" width="26" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes4TotalFimPaginaComInvestimentosPrevisto}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="237" y="108" width="26" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes4TotalFimPaginaComInvestimentosRealizado}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="265" y="37" width="26" height="9"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes5TotalFimPaginaReceitasPrevisto}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="293" y="37" width="26" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes5TotalFimPaginaReceitasRealizado}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="265" y="48" width="26" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes5TotalFimPaginaDespesasPrevisto}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="293" y="48" width="26" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes5TotalFimPaginaDespesasRealizado}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="265" y="60" width="26" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes5TotalFimPaginaInvestimentosPrevisto}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="293" y="60" width="26" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes5TotalFimPaginaInvestimentosRealizado}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="265" y="72" width="26" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes5TotalFimPaginaInvestimentosDespesasPrevisto}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="293" y="72" width="26" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes5TotalFimPaginaInvestimentosDespesasRealizado}]]></textFieldExpression>
			</textField>
			<rectangle>
				<reportElement x="376" y="71" width="28" height="12" forecolor="#999999" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<textField>
				<reportElement x="265" y="96" width="26" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes5TotalFimPaginaSemInvestimentosPrevisto}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="293" y="96" width="26" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes5TotalFimPaginaSemInvestimentosRealizado}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="265" y="108" width="26" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes5TotalFimPaginaComInvestimentosPrevisto}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="293" y="108" width="26" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes5TotalFimPaginaComInvestimentosRealizado}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="321" y="37" width="26" height="9"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes6TotalFimPaginaReceitasPrevisto}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="349" y="37" width="26" height="9"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes6TotalFimPaginaReceitasRealizado}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="321" y="48" width="26" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes6TotalFimPaginaDespesasPrevisto}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="349" y="48" width="26" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes6TotalFimPaginaDespesasRealizado}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="321" y="60" width="26" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes6TotalFimPaginaInvestimentosPrevisto}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="349" y="60" width="26" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes6TotalFimPaginaInvestimentosRealizado}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="321" y="72" width="26" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes6TotalFimPaginaInvestimentosDespesasPrevisto}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="349" y="72" width="26" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes6TotalFimPaginaInvestimentosDespesasRealizado}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="321" y="96" width="26" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes6TotalFimPaginaSemInvestimentosPrevisto}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="349" y="96" width="26" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes6TotalFimPaginaSemInvestimentosRealizado}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="321" y="108" width="26" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes6TotalFimPaginaComInvestimentosPrevisto}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="349" y="108" width="26" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes6TotalFimPaginaComInvestimentosRealizado}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="713" y="37" width="26" height="9"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{totalPrevistoRealizadoFimPaginaReceitasPrevisto}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="741" y="37" width="26" height="9"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{totalPrevistoRealizadoFimPaginaReceitasRealizado}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="713" y="48" width="26" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{totalPrevistoRealizadoFimPaginaDespesasPrevisto}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="741" y="48" width="26" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{totalPrevistoRealizadoFimPaginaDespesasRealizado}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="713" y="60" width="26" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{totalPrevistoRealizadoFimPaginaInvestimentosPrevisto}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="741" y="60" width="26" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{totalPrevistoRealizadoFimPaginaInvestimentosRealizado}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="713" y="72" width="26" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{totalPrevistoRealizadoFimPaginaInvestimentosDespesasPrevisto}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="741" y="72" width="26" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{totalPrevistoRealizadoFimPaginaInvestimentosDespesasRealizado}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="713" y="96" width="26" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{totalPrevistoRealizadoFimPaginaSemInvestimentosPrevisto}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="741" y="96" width="26" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{totalPrevistoRealizadoFimPaginaSemInvestimentosRealizado}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="713" y="108" width="26" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{totalPrevistoRealizadoFimPaginaComInvestimentosPrevisto}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="741" y="108" width="26" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{totalPrevistoRealizadoFimPaginaComInvestimentosRealizado}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="769" y="37" width="31" height="9"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{saldoFimPaginaReceitas}]]></textFieldExpression>
			</textField>
			<rectangle>
				<reportElement x="432" y="59" width="28" height="12" forecolor="#999999" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<textField>
				<reportElement x="769" y="48" width="31" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{saldoFimPaginaDespesas}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="769" y="60" width="31" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{saldoFimPaginaInvestimentos}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="769" y="72" width="31" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{saldoFimPaginaInvestimentosDespesas}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="769" y="96" width="31" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{saldoFimPaginaSemInvestimentos}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="769" y="108" width="31" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{saldoFimPaginaComInvestimentos}]]></textFieldExpression>
			</textField>
			<rectangle>
				<reportElement x="404" y="47" width="28" height="12" forecolor="#999999" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<textField>
				<reportElement x="405" y="48" width="26" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes7TotalFimPaginaDespesasRealizado}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="405" y="72" width="26" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes7TotalFimPaginaInvestimentosDespesasRealizado}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="405" y="60" width="26" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes7TotalFimPaginaInvestimentosRealizado}]]></textFieldExpression>
			</textField>
			<rectangle>
				<reportElement x="404" y="95" width="28" height="12" forecolor="#999999" backcolor="#CCFFCC"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<textField>
				<reportElement x="405" y="96" width="26" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes7TotalFimPaginaSemInvestimentosRealizado}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="379" y="8" width="50" height="15"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="7" isBold="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{data7}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="377" y="72" width="26" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes7TotalFimPaginaInvestimentosDespesasPrevisto}]]></textFieldExpression>
			</textField>
			<rectangle>
				<reportElement x="404" y="83" width="28" height="12" forecolor="#999999" backcolor="#CCFFCC"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="404" y="24" width="28" height="12" forecolor="#999999" backcolor="#CCCCCC"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="376" y="107" width="28" height="12" forecolor="#999999" backcolor="#CCFFCC"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="376" y="95" width="28" height="12" forecolor="#999999" backcolor="#CCFFCC"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="404" y="107" width="28" height="12" forecolor="#999999" backcolor="#CCFFCC"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="376" y="24" width="28" height="12" forecolor="#999999" backcolor="#CCCCCC"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="404" y="36" width="28" height="11" forecolor="#999999" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<textField>
				<reportElement x="377" y="96" width="26" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes7TotalFimPaginaSemInvestimentosPrevisto}]]></textFieldExpression>
			</textField>
			<rectangle>
				<reportElement x="376" y="36" width="28" height="11" forecolor="#999999" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<textField>
				<reportElement x="377" y="36" width="26" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes7TotalFimPaginaReceitasPrevisto}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="405" y="108" width="26" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes7TotalFimPaginaComInvestimentosRealizado}]]></textFieldExpression>
			</textField>
			<rectangle>
				<reportElement x="376" y="59" width="28" height="12" forecolor="#999999" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<textField>
				<reportElement x="377" y="60" width="26" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes7TotalFimPaginaInvestimentosPrevisto}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="377" y="48" width="26" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes7TotalFimPaginaDespesasPrevisto}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="405" y="37" width="26" height="9"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes7TotalFimPaginaReceitasRealizado}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="377" y="108" width="26" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes7TotalFimPaginaComInvestimentosPrevisto}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="377" y="25" width="28" height="10"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="5" isBold="true"/>
				</textElement>
				<text><![CDATA[Previsto]]></text>
			</staticText>
			<rectangle>
				<reportElement x="376" y="83" width="28" height="12" forecolor="#999999" backcolor="#CCFFCC"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="768" y="24" width="33" height="12" forecolor="#999999" backcolor="#CCCCCC"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<staticText>
				<reportElement x="404" y="25" width="28" height="10"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="5" isBold="true"/>
				</textElement>
				<text><![CDATA[Realizado]]></text>
			</staticText>
			<textField>
				<reportElement x="461" y="60" width="26" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes8TotalFimPaginaInvestimentosRealizado}]]></textFieldExpression>
			</textField>
			<rectangle>
				<reportElement x="460" y="95" width="28" height="12" forecolor="#999999" backcolor="#CCFFCC"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="432" y="83" width="28" height="12" forecolor="#999999" backcolor="#CCFFCC"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="460" y="71" width="28" height="12" forecolor="#999999" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="432" y="107" width="28" height="12" forecolor="#999999" backcolor="#CCFFCC"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<textField>
				<reportElement x="461" y="96" width="26" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes8TotalFimPaginaSemInvestimentosRealizado}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="433" y="48" width="26" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes8TotalFimPaginaDespesasPrevisto}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="433" y="25" width="28" height="10"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="5" isBold="true"/>
				</textElement>
				<text><![CDATA[Previsto]]></text>
			</staticText>
			<textField>
				<reportElement x="433" y="60" width="26" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes8TotalFimPaginaInvestimentosPrevisto}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="461" y="72" width="26" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes8TotalFimPaginaInvestimentosDespesasRealizado}]]></textFieldExpression>
			</textField>
			<rectangle>
				<reportElement x="432" y="36" width="28" height="11" forecolor="#999999" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="460" y="24" width="28" height="12" forecolor="#999999" backcolor="#CCCCCC"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="488" y="95" width="28" height="12" forecolor="#999999" backcolor="#CCFFCC"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="40" y="24" width="28" height="12" forecolor="#999999" backcolor="#CCCCCC"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<textField>
				<reportElement x="433" y="108" width="26" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes8TotalFimPaginaComInvestimentosPrevisto}]]></textFieldExpression>
			</textField>
			<rectangle>
				<reportElement x="488" y="107" width="28" height="12" forecolor="#999999" backcolor="#CCFFCC"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="460" y="107" width="28" height="12" forecolor="#999999" backcolor="#CCFFCC"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="460" y="36" width="28" height="12" forecolor="#999999" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<textField>
				<reportElement x="433" y="72" width="26" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes8TotalFimPaginaInvestimentosDespesasPrevisto}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="461" y="108" width="26" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes8TotalFimPaginaComInvestimentosRealizado}]]></textFieldExpression>
			</textField>
			<rectangle>
				<reportElement x="460" y="47" width="28" height="12" forecolor="#999999" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="432" y="95" width="28" height="12" forecolor="#999999" backcolor="#CCFFCC"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<textField>
				<reportElement x="433" y="37" width="26" height="9"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes8TotalFimPaginaReceitasPrevisto}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="433" y="96" width="26" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes8TotalFimPaginaSemInvestimentosPrevisto}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="435" y="8" width="50" height="15"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="7" isBold="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{data8}]]></textFieldExpression>
			</textField>
			<rectangle>
				<reportElement x="460" y="83" width="28" height="12" forecolor="#999999" backcolor="#CCFFCC"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<staticText>
				<reportElement x="460" y="25" width="28" height="10"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="5" isBold="true"/>
				</textElement>
				<text><![CDATA[Realizado]]></text>
			</staticText>
			<textField>
				<reportElement x="461" y="48" width="26" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes8TotalFimPaginaDespesasRealizado}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="461" y="37" width="26" height="9"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes8TotalFimPaginaReceitasRealizado}]]></textFieldExpression>
			</textField>
			<rectangle>
				<reportElement x="488" y="36" width="28" height="11" forecolor="#999999" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<textField>
				<reportElement x="491" y="8" width="50" height="15"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="7" isBold="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{data9}]]></textFieldExpression>
			</textField>
			<rectangle>
				<reportElement x="516" y="36" width="28" height="12" forecolor="#999999" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<staticText>
				<reportElement x="489" y="25" width="28" height="10"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="5" isBold="true"/>
				</textElement>
				<text><![CDATA[Previsto]]></text>
			</staticText>
			<textField>
				<reportElement x="489" y="37" width="26" height="9"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes9TotalFimPaginaReceitasPrevisto}]]></textFieldExpression>
			</textField>
			<rectangle>
				<reportElement x="488" y="47" width="28" height="12" forecolor="#999999" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<textField>
				<reportElement x="489" y="96" width="26" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes9TotalFimPaginaSemInvestimentosPrevisto}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="489" y="108" width="26" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes9TotalFimPaginaComInvestimentosPrevisto}]]></textFieldExpression>
			</textField>
			<rectangle>
				<reportElement x="488" y="71" width="28" height="12" forecolor="#999999" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="516" y="24" width="28" height="12" forecolor="#999999" backcolor="#CCCCCC"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<staticText>
				<reportElement x="516" y="25" width="28" height="10"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="5" isBold="true"/>
				</textElement>
				<text><![CDATA[Realizado]]></text>
			</staticText>
			<rectangle>
				<reportElement x="516" y="83" width="28" height="12" forecolor="#999999" backcolor="#CCFFCC"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<textField>
				<reportElement x="489" y="48" width="26" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes9TotalFimPaginaDespesasPrevisto}]]></textFieldExpression>
			</textField>
			<rectangle>
				<reportElement x="516" y="107" width="28" height="12" forecolor="#999999" backcolor="#CCFFCC"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<textField>
				<reportElement x="517" y="60" width="26" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes9TotalFimPaginaInvestimentosRealizado}]]></textFieldExpression>
			</textField>
			<rectangle>
				<reportElement x="516" y="95" width="28" height="12" forecolor="#999999" backcolor="#CCFFCC"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="516" y="71" width="28" height="12" forecolor="#999999" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<textField>
				<reportElement x="517" y="96" width="26" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes9TotalFimPaginaSemInvestimentosRealizado}]]></textFieldExpression>
			</textField>
			<rectangle>
				<reportElement x="516" y="47" width="28" height="12" forecolor="#999999" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<textField>
				<reportElement x="517" y="37" width="26" height="9"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes9TotalFimPaginaReceitasRealizado}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="517" y="108" width="26" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes9TotalFimPaginaComInvestimentosRealizado}]]></textFieldExpression>
			</textField>
			<rectangle>
				<reportElement x="488" y="83" width="28" height="12" forecolor="#999999" backcolor="#CCFFCC"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="488" y="59" width="28" height="12" forecolor="#999999" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<textField>
				<reportElement x="489" y="60" width="26" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes9TotalFimPaginaInvestimentosPrevisto}]]></textFieldExpression>
			</textField>
			<rectangle>
				<reportElement x="628" y="47" width="28" height="12" forecolor="#999999" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<textField>
				<reportElement x="489" y="72" width="26" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes9TotalFimPaginaInvestimentosDespesasPrevisto}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="517" y="72" width="26" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes9TotalFimPaginaInvestimentosDespesasRealizado}]]></textFieldExpression>
			</textField>
			<rectangle>
				<reportElement x="544" y="47" width="28" height="12" forecolor="#999999" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<textField>
				<reportElement x="517" y="48" width="26" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes9TotalFimPaginaDespesasRealizado}]]></textFieldExpression>
			</textField>
			<rectangle>
				<reportElement x="572" y="107" width="28" height="12" forecolor="#999999" backcolor="#CCFFCC"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<textField>
				<reportElement x="573" y="108" width="26" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes10TotalFimPaginaComInvestimentosRealizado}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="545" y="48" width="26" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes10TotalFimPaginaDespesasPrevisto}]]></textFieldExpression>
			</textField>
			<rectangle>
				<reportElement x="572" y="36" width="28" height="12" forecolor="#999999" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="572" y="95" width="28" height="12" forecolor="#999999" backcolor="#CCFFCC"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<textField>
				<reportElement x="573" y="96" width="26" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes10TotalFimPaginaSemInvestimentosRealizado}]]></textFieldExpression>
			</textField>
			<rectangle>
				<reportElement x="544" y="24" width="28" height="12" forecolor="#999999" backcolor="#CCCCCC"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="572" y="24" width="28" height="12" forecolor="#999999" backcolor="#CCCCCC"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="544" y="107" width="28" height="12" forecolor="#999999" backcolor="#CCFFCC"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<staticText>
				<reportElement x="572" y="25" width="28" height="10"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="5" isBold="true"/>
				</textElement>
				<text><![CDATA[Realizado]]></text>
			</staticText>
			<rectangle>
				<reportElement x="544" y="36" width="28" height="11" forecolor="#999999" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<textField>
				<reportElement x="545" y="37" width="26" height="9"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes10TotalFimPaginaReceitasPrevisto}]]></textFieldExpression>
			</textField>
			<rectangle>
				<reportElement x="572" y="59" width="28" height="12" forecolor="#999999" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<textField>
				<reportElement x="573" y="60" width="26" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes10TotalFimPaginaInvestimentosRealizado}]]></textFieldExpression>
			</textField>
			<rectangle>
				<reportElement x="572" y="71" width="28" height="12" forecolor="#999999" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<textField>
				<reportElement x="573" y="72" width="26" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes10TotalFimPaginaInvestimentosDespesasRealizado}]]></textFieldExpression>
			</textField>
			<rectangle>
				<reportElement x="572" y="47" width="28" height="12" forecolor="#999999" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<textField>
				<reportElement x="573" y="48" width="26" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes10TotalFimPaginaDespesasRealizado}]]></textFieldExpression>
			</textField>
			<rectangle>
				<reportElement x="544" y="95" width="28" height="12" forecolor="#999999" backcolor="#CCFFCC"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="572" y="83" width="28" height="12" forecolor="#999999" backcolor="#CCFFCC"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<textField>
				<reportElement x="545" y="108" width="26" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes10TotalFimPaginaComInvestimentosPrevisto}]]></textFieldExpression>
			</textField>
			<rectangle>
				<reportElement x="544" y="59" width="28" height="12" forecolor="#999999" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<textField>
				<reportElement x="545" y="60" width="26" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes10TotalFimPaginaInvestimentosPrevisto}]]></textFieldExpression>
			</textField>
			<rectangle>
				<reportElement x="544" y="83" width="28" height="12" forecolor="#999999" backcolor="#CCFFCC"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<textField>
				<reportElement x="573" y="37" width="26" height="9"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes10TotalFimPaginaReceitasRealizado}]]></textFieldExpression>
			</textField>
			<rectangle>
				<reportElement x="544" y="71" width="28" height="12" forecolor="#999999" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<textField>
				<reportElement x="545" y="72" width="26" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes10TotalFimPaginaInvestimentosDespesasPrevisto}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="545" y="96" width="26" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes10TotalFimPaginaSemInvestimentosPrevisto}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="545" y="25" width="28" height="10"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="5" isBold="true"/>
				</textElement>
				<text><![CDATA[Previsto]]></text>
			</staticText>
			<textField>
				<reportElement x="547" y="8" width="50" height="15"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="7" isBold="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{data10}]]></textFieldExpression>
			</textField>
			<rectangle>
				<reportElement x="600" y="59" width="28" height="12" forecolor="#999999" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<textField>
				<reportElement x="601" y="60" width="26" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes11TotalFimPaginaInvestimentosPrevisto}]]></textFieldExpression>
			</textField>
			<rectangle>
				<reportElement x="600" y="36" width="28" height="11" forecolor="#999999" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="628" y="83" width="28" height="12" forecolor="#999999" backcolor="#CCFFCC"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="628" y="36" width="28" height="11" forecolor="#999999" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="628" y="24" width="28" height="12" forecolor="#999999" backcolor="#CCCCCC"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<staticText>
				<reportElement x="628" y="25" width="28" height="10"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="5" isBold="true"/>
				</textElement>
				<text><![CDATA[Realizado]]></text>
			</staticText>
			<rectangle>
				<reportElement x="628" y="95" width="28" height="12" forecolor="#999999" backcolor="#CCFFCC"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<textField>
				<reportElement x="629" y="96" width="26" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes11TotalFimPaginaSemInvestimentosRealizado}]]></textFieldExpression>
			</textField>
			<rectangle>
				<reportElement x="600" y="47" width="28" height="12" forecolor="#999999" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<textField>
				<reportElement x="601" y="48" width="26" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes11TotalFimPaginaDespesasPrevisto}]]></textFieldExpression>
			</textField>
			<rectangle>
				<reportElement x="628" y="59" width="28" height="12" forecolor="#999999" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="600" y="83" width="28" height="12" forecolor="#999999" backcolor="#CCFFCC"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<textField>
				<reportElement x="603" y="8" width="50" height="15"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="7" isBold="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{data11}]]></textFieldExpression>
			</textField>
			<rectangle>
				<reportElement x="600" y="107" width="28" height="12" forecolor="#999999" backcolor="#CCFFCC"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="600" y="24" width="28" height="12" forecolor="#999999" backcolor="#CCCCCC"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<staticText>
				<reportElement x="601" y="25" width="28" height="10"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="5" isBold="true"/>
				</textElement>
				<text><![CDATA[Previsto]]></text>
			</staticText>
			<textField>
				<reportElement x="629" y="60" width="26" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes11TotalFimPaginaInvestimentosRealizado}]]></textFieldExpression>
			</textField>
			<rectangle>
				<reportElement x="600" y="95" width="28" height="12" forecolor="#999999" backcolor="#CCFFCC"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="628" y="107" width="28" height="12" forecolor="#999999" backcolor="#CCFFCC"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="628" y="71" width="28" height="12" forecolor="#999999" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="600" y="71" width="28" height="12" forecolor="#999999" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<textField>
				<reportElement x="601" y="96" width="26" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes11TotalFimPaginaSemInvestimentosPrevisto}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="629" y="48" width="26" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes11TotalFimPaginaDespesasRealizado}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="601" y="37" width="26" height="9"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes11TotalFimPaginaReceitasPrevisto}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="601" y="108" width="26" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes11TotalFimPaginaComInvestimentosPrevisto}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="629" y="108" width="26" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes11TotalFimPaginaComInvestimentosRealizado}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="629" y="37" width="26" height="9"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes11TotalFimPaginaReceitasRealizado}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="601" y="72" width="26" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes11TotalFimPaginaInvestimentosDespesasPrevisto}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="629" y="72" width="26" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes11TotalFimPaginaInvestimentosDespesasRealizado}]]></textFieldExpression>
			</textField>
			<rectangle>
				<reportElement x="684" y="71" width="28" height="12" forecolor="#999999" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<textField>
				<reportElement x="685" y="72" width="26" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes12TotalFimPaginaInvestimentosDespesasRealizado}]]></textFieldExpression>
			</textField>
			<rectangle>
				<reportElement x="656" y="59" width="28" height="12" forecolor="#999999" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="684" y="59" width="28" height="12" forecolor="#999999" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="684" y="107" width="28" height="12" forecolor="#999999" backcolor="#CCFFCC"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="656" y="107" width="28" height="12" forecolor="#999999" backcolor="#CCFFCC"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<textField>
				<reportElement x="685" y="108" width="26" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes12TotalFimPaginaComInvestimentosRealizado}]]></textFieldExpression>
			</textField>
			<rectangle>
				<reportElement x="684" y="47" width="28" height="12" forecolor="#999999" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<textField>
				<reportElement x="685" y="48" width="26" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes12TotalFimPaginaDespesasRealizado}]]></textFieldExpression>
			</textField>
			<rectangle>
				<reportElement x="684" y="24" width="28" height="12" forecolor="#999999" backcolor="#CCCCCC"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="656" y="36" width="28" height="11" forecolor="#999999" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<textField>
				<reportElement x="657" y="108" width="26" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes12TotalFimPaginaComInvestimentosPrevisto}]]></textFieldExpression>
			</textField>
			<rectangle>
				<reportElement x="656" y="71" width="28" height="12" forecolor="#999999" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="656" y="95" width="28" height="12" forecolor="#999999" backcolor="#CCFFCC"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<textField>
				<reportElement x="657" y="60" width="26" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes12TotalFimPaginaInvestimentosPrevisto}]]></textFieldExpression>
			</textField>
			<rectangle>
				<reportElement x="656" y="24" width="28" height="12" forecolor="#999999" backcolor="#CCCCCC"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<textField>
				<reportElement x="657" y="37" width="26" height="9"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes12TotalFimPaginaReceitasPrevisto}]]></textFieldExpression>
			</textField>
			<rectangle>
				<reportElement x="656" y="47" width="28" height="12" forecolor="#999999" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<textField>
				<reportElement x="657" y="48" width="26" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes12TotalFimPaginaDespesasPrevisto}]]></textFieldExpression>
			</textField>
			<rectangle>
				<reportElement x="684" y="36" width="28" height="11" forecolor="#999999" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="684" y="95" width="28" height="12" forecolor="#999999" backcolor="#CCFFCC"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<textField>
				<reportElement x="685" y="96" width="26" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes12TotalFimPaginaSemInvestimentosRealizado}]]></textFieldExpression>
			</textField>
			<rectangle>
				<reportElement x="684" y="83" width="28" height="12" forecolor="#999999" backcolor="#CCFFCC"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<textField>
				<reportElement x="657" y="96" width="26" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes12TotalFimPaginaSemInvestimentosPrevisto}]]></textFieldExpression>
			</textField>
			<rectangle>
				<reportElement x="656" y="83" width="28" height="12" forecolor="#999999" backcolor="#CCFFCC"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<textField>
				<reportElement x="685" y="37" width="26" height="9"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes12TotalFimPaginaReceitasRealizado}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="657" y="25" width="28" height="10"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="5" isBold="true"/>
				</textElement>
				<text><![CDATA[Previsto]]></text>
			</staticText>
			<textField>
				<reportElement x="657" y="72" width="26" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes12TotalFimPaginaInvestimentosDespesasPrevisto}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="684" y="25" width="28" height="10"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="5" isBold="true"/>
				</textElement>
				<text><![CDATA[Realizado]]></text>
			</staticText>
			<textField>
				<reportElement x="685" y="60" width="26" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes12TotalFimPaginaInvestimentosRealizado}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="659" y="8" width="50" height="15"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="7" isBold="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{data12}]]></textFieldExpression>
			</textField>
		</band>
	</summary>
</jasperReport>

<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="ReciboDevolucao" pageWidth="680" pageHeight="842" columnWidth="625" leftMargin="20" rightMargin="35" topMargin="2" bottomMargin="2" whenResourceMissingType="Empty">
	<property name="ireport.zoom" value="1.6934217901613537"/>
	<property name="ireport.x" value="1"/>
	<property name="ireport.y" value="0"/>
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.encoding" value="ISO-8859-1"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="java.util.*"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<parameter name="tituloRelatorio" class="java.lang.String" isForPrompting="false"/>
	<parameter name="nomeEmpresa" class="java.lang.String" isForPrompting="false"/>
	<parameter name="versaoSoftware" class="java.lang.String" isForPrompting="false"/>
	<parameter name="usuario" class="java.lang.String" isForPrompting="false"/>
	<parameter name="filtros" class="java.lang.String" isForPrompting="false"/>
	<parameter name="SUBREPORT_DIR" class="java.lang.String">
		<defaultValueExpression><![CDATA["E:\\desenvolvimentosv\\Projeto Pacto Solucoes\\ZillyonWeb\\src\\java\\relatorio\\designRelatorio\\financeiro\\"]]></defaultValueExpression>
	</parameter>
	<parameter name="SUBREPORT_DIR1" class="java.lang.String">
		<defaultValueExpression><![CDATA["E:\\desenvolvimentosv\\Projeto Pacto Solucoes\\ZillyonWeb\\src\\java\\relatorio\\designRelatorio\\financeiro\\"]]></defaultValueExpression>
	</parameter>
	<parameter name="dataIni" class="java.lang.String" isForPrompting="false"/>
	<parameter name="dataFim" class="java.lang.String" isForPrompting="false"/>
	<parameter name="qtdAV" class="java.lang.String" isForPrompting="false"/>
	<parameter name="qtdCA" class="java.lang.String" isForPrompting="false"/>
	<parameter name="qtdChequeAV" class="java.lang.String" isForPrompting="false"/>
	<parameter name="qtdChequePR" class="java.lang.String" isForPrompting="false"/>
	<parameter name="qtdOutro" class="java.lang.String" isForPrompting="false"/>
	<parameter name="valorAV" class="java.lang.Double" isForPrompting="false"/>
	<parameter name="valorCA" class="java.lang.Double" isForPrompting="false"/>
	<parameter name="valorChequeAV" class="java.lang.Double" isForPrompting="false"/>
	<parameter name="valorChequePR" class="java.lang.Double" isForPrompting="false"/>
	<parameter name="valorOutro" class="java.lang.Double" isForPrompting="false"/>
	<parameter name="logoPadraoRelatorio" class="java.io.InputStream" isForPrompting="false"/>
	<parameter name="qtdCD" class="java.lang.String"/>
	<parameter name="valorCD" class="java.lang.Double"/>
	<parameter name="SUBREPORT_DIR2" class="java.lang.String">
		<defaultValueExpression><![CDATA["E:\\desenvolvimentosv\\Projeto Pacto Solucoes\\ZillyonWeb\\src\\java\\relatorio\\designRelatorio\\financeiro\\"]]></defaultValueExpression>
	</parameter>
	<parameter name="codRecibo" class="java.lang.String"/>
	<parameter name="empresaVO.cnpj" class="java.lang.String"/>
	<parameter name="empresaVO.endereco" class="java.lang.String"/>
	<parameter name="empresaVO.site" class="java.lang.String"/>
	<parameter name="empresaVO.fone" class="java.lang.String"/>
	<parameter name="responsavelDevolucao" class="java.lang.String"/>
	<parameter name="pessoa" class="java.lang.String"/>
	<parameter name="contrato" class="java.lang.Integer"/>
	<parameter name="dataDevolucao" class="java.util.Date"/>
	<parameter name="valorDevolucao" class="java.lang.Double"/>
	<parameter name="valorPorExtenso" class="java.lang.String"/>
	<parameter name="descricao" class="java.lang.String"/>
	<parameter name="produto" class="java.lang.Integer"/>
	<field name="descricao" class="java.lang.String"/>
	<field name="dataDevolucao" class="java.util.Date"/>
	<field name="responsavelDevolucao.colaboradorVO.pessoa.nome" class="java.lang.String"/>
	<field name="contrato.codigo" class="java.lang.Integer"/>
	<field name="valorPorExtenso" class="java.lang.String"/>
	<field name="contrato.pessoa.nome" class="java.lang.String"/>
	<field name="valorDevolucao" class="java.lang.Double"/>
	<field name="valorMonetario" class="java.lang.String"/>
	<field name="dataFormatada" class="java.lang.String"/>
	<field name="listaCheque" class="java.lang.Object"/>
	<field name="listaCartoes" class="java.lang.Object"/>
	<field name="apresentarCartoes" class="java.lang.Boolean"/>
	<field name="apresentarCheques" class="java.lang.Boolean"/>
	<field name="listaCheque2" class="java.lang.Object"/>
	<field name="listaCartoes2" class="java.lang.Object"/>
	<field name="valorSaldoCCApresentar" class="java.lang.String"/>
	<field name="valorContratoApresentar" class="java.lang.String"/>
	<field name="informacoes" class="java.lang.String"/>
	<field name="produtoVO.codigo" class="java.lang.Integer"/>
	<field name="produtoVO.pessoa.nome" class="java.lang.String"/>
	<detail>
		<band height="101">
			<staticText>
				<reportElement key="staticText-85" positionType="Float" mode="Opaque" x="4" y="44" width="61" height="13"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="SansSerif" size="9" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[DT Devolução]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-88" positionType="Float" mode="Opaque" x="164" y="44" width="280" height="13"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="SansSerif" size="9" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[ Nome do Responsável Devolução]]></text>
			</staticText>
			<image isUsingCache="true" onErrorType="Blank" evaluationTime="Page">
				<reportElement key="image-1" x="2" y="3" width="121" height="39" isPrintWhenDetailOverflows="true" forecolor="#FFFFFF"/>
				<imageExpression class="java.io.InputStream"><![CDATA[$P{logoPadraoRelatorio}]]></imageExpression>
			</image>
			<rectangle radius="10">
				<reportElement key="retDadosEmpresa1" x="126" y="3" width="334" height="39" backcolor="#F0F0F0"/>
				<graphicElement>
					<pen lineWidth="0.25"/>
				</graphicElement>
			</rectangle>
			<textField>
				<reportElement x="136" y="5" width="220" height="13"/>
				<textElement>
					<font fontName="Microsoft Sans Serif" isBold="true" isItalic="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{nomeEmpresa}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="136" y="17" width="218" height="13"/>
				<textElement>
					<font fontName="Microsoft Sans Serif" isBold="true" isItalic="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{empresaVO.endereco}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="136" y="29" width="319" height="13"/>
				<textElement>
					<font fontName="Microsoft Sans Serif" isBold="true" isItalic="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{empresaVO.site}.toLowerCase()]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="354" y="5" width="101" height="13"/>
				<textElement>
					<font fontName="Microsoft Sans Serif" isBold="true" isItalic="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{empresaVO.cnpj}]]></textFieldExpression>
			</textField>
			<rectangle radius="10">
				<reportElement key="retDadosRecibo1" x="482" y="3" width="136" height="39" backcolor="#F0F0F0"/>
				<graphicElement>
					<pen lineWidth="0.25"/>
				</graphicElement>
			</rectangle>
			<textField>
				<reportElement x="354" y="17" width="90" height="13"/>
				<textElement>
					<font fontName="Microsoft Sans Serif" isBold="true" isItalic="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{empresaVO.fone}]]></textFieldExpression>
			</textField>
			<line>
				<reportElement x="1" y="60" width="624" height="1"/>
			</line>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-229" mode="Opaque" x="167" y="65" width="197" height="12"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="9" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{responsavelDevolucao.colaboradorVO.pessoa.nome}]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="false">
				<reportElement key="textField-229" mode="Opaque" x="8" y="64" width="146" height="12"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="9" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{dataFormatada}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-229" mode="Opaque" x="513" y="65" width="69" height="12"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="9" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.Integer"><![CDATA[($F{contrato.codigo}.intValue() > 0 ? $F{contrato.codigo} : $F{produtoVO.codigo})]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-229" mode="Opaque" x="499" y="16" width="116" height="13" backcolor="#F0F0F0"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="11" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{valorMonetario}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement stretchType="RelativeToBandHeight" x="8" y="85" width="280" height="13"/>
				<textElement textAlignment="Justified"/>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{informacoes}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-229" mode="Opaque" x="511" y="44" width="66" height="13"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="SansSerif" size="9" isBold="true" isItalic="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[($F{contrato.codigo}.intValue() > 0 ? "Cod Contrato" : "Cod Produto")]]></textFieldExpression>
			</textField>
		</band>
		<band height="36">
			<textField isStretchWithOverflow="true">
				<reportElement stretchType="RelativeToBandHeight" x="5" y="17" width="280" height="17"/>
				<textElement textAlignment="Justified"/>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{descricao}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement key="staticText-85" mode="Opaque" x="6" y="3" width="214" height="13"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="SansSerif" size="9" isBold="true" isItalic="false" isUnderline="true" isStrikeThrough="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Valores do Cancelamento]]></text>
			</staticText>
		</band>
		<band height="54">
			<printWhenExpression><![CDATA[$F{apresentarCheques}]]></printWhenExpression>
			<subreport isUsingCache="true">
				<reportElement key="subreport-1" x="7" y="16" width="455" height="34" isRemoveLineWhenBlank="true">
					<printWhenExpression><![CDATA[$F{apresentarCheques}]]></printWhenExpression>
				</reportElement>
				<subreportParameter name="SUBREPORT_DIR">
					<subreportParameterExpression><![CDATA[$P{SUBREPORT_DIR}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="ListaCheque">
					<subreportParameterExpression><![CDATA[$F{listaCheque}]]></subreportParameterExpression>
				</subreportParameter>
				<dataSourceExpression><![CDATA[$F{listaCheque}]]></dataSourceExpression>
				<subreportExpression class="java.lang.String"><![CDATA[$P{SUBREPORT_DIR} + "MovPagamento_cheques.jasper"]]></subreportExpression>
			</subreport>
			<staticText>
				<reportElement key="staticText-85" mode="Opaque" x="7" y="3" width="214" height="13">
					<printWhenExpression><![CDATA[$F{apresentarCheques}]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="SansSerif" size="9" isBold="true" isItalic="false" isUnderline="true" isStrikeThrough="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Cheques devolvidos]]></text>
			</staticText>
		</band>
		<band height="51">
			<printWhenExpression><![CDATA[$F{apresentarCartoes}]]></printWhenExpression>
			<staticText>
				<reportElement key="staticText-85" mode="Opaque" x="7" y="4" width="214" height="13">
					<printWhenExpression><![CDATA[$F{apresentarCartoes}]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="SansSerif" size="9" isBold="true" isItalic="false" isUnderline="true" isStrikeThrough="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Cartões estornados]]></text>
			</staticText>
			<subreport>
				<reportElement key="subreport-2" x="7" y="18" width="454" height="21">
					<printWhenExpression><![CDATA[$F{apresentarCartoes}]]></printWhenExpression>
				</reportElement>
				<dataSourceExpression><![CDATA[$F{listaCartoes}]]></dataSourceExpression>
				<subreportExpression class="java.lang.String"><![CDATA[$P{SUBREPORT_DIR} + "MovPagamento_cartaocredito.jasper"]]></subreportExpression>
			</subreport>
		</band>
		<band height="103">
			<line>
				<reportElement x="7" y="43" width="259" height="1"/>
			</line>
			<line>
				<reportElement x="353" y="43" width="262" height="1"/>
			</line>
			<textField>
				<reportElement x="353" y="44" width="260" height="12"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="8" isBold="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA["Cliente: " + ($F{contrato.codigo}.intValue() > 0 ? $F{contrato.pessoa.nome} : $F{produtoVO.pessoa.nome})]]></textFieldExpression>
			</textField>
			<textField pattern="dd/MM/yyyy HH:mm:ss" isBlankWhenNull="false">
				<reportElement key="dataImpressao1" mode="Transparent" x="53" y="75" width="60" height="8"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="SansSerif" size="6" isItalic="false" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression class="java.util.Date"><![CDATA[new Date()]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="498" y="75" width="49" height="8"/>
				<textElement textAlignment="Right">
					<font size="6" isBold="true"/>
				</textElement>
				<text><![CDATA[Data pagamento:]]></text>
			</staticText>
			<textField>
				<reportElement x="7" y="44" width="259" height="12"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="8" isBold="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA["Resp. Devolução: " + $F{responsavelDevolucao.colaboradorVO.pessoa.nome}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="7" y="3" width="612" height="34"/>
				<textElement verticalAlignment="Middle">
					<font size="9" isBold="true" isItalic="true" pdfFontName="Helvetica-BoldOblique"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA["* Devolvemos a " + ($F{contrato.codigo}.intValue() > 0 ? $F{contrato.pessoa.nome} : $F{produtoVO.pessoa.nome}) +", a quantia de " +$F{valorPorExtenso}+", proveniente de devolução de cancelamento."]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="false">
				<reportElement key="textField-229" mode="Opaque" x="552" y="75" width="72" height="8"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="6" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{dataFormatada}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="4" y="76" width="49" height="8"/>
				<textElement>
					<font size="6" isBold="true"/>
				</textElement>
				<text><![CDATA[Data impressão:]]></text>
			</staticText>
			<line>
				<reportElement x="2" y="95" width="624" height="1"/>
				<graphicElement>
					<pen lineWidth="1.5" lineStyle="Dashed"/>
				</graphicElement>
			</line>
		</band>
		<band height="105">
			<textField>
				<reportElement x="138" y="15" width="218" height="13"/>
				<textElement>
					<font fontName="Microsoft Sans Serif" isBold="true" isItalic="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{empresaVO.endereco}]]></textFieldExpression>
			</textField>
			<rectangle radius="10">
				<reportElement key="retDadosEmpresa1" x="128" y="2" width="334" height="39" backcolor="#F0F0F0"/>
				<graphicElement>
					<pen lineWidth="0.25"/>
				</graphicElement>
			</rectangle>
			<textField>
				<reportElement x="138" y="27" width="319" height="13"/>
				<textElement>
					<font fontName="Microsoft Sans Serif" isBold="true" isItalic="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{empresaVO.site}.toLowerCase()]]></textFieldExpression>
			</textField>
			<rectangle radius="10">
				<reportElement key="retDadosRecibo1" x="484" y="2" width="136" height="39" backcolor="#F0F0F0"/>
				<graphicElement>
					<pen lineWidth="0.25"/>
				</graphicElement>
			</rectangle>
			<textField>
				<reportElement x="138" y="3" width="220" height="13"/>
				<textElement>
					<font fontName="Microsoft Sans Serif" isBold="true" isItalic="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{nomeEmpresa}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="356" y="3" width="101" height="13"/>
				<textElement>
					<font fontName="Microsoft Sans Serif" isBold="true" isItalic="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{empresaVO.cnpj}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-229" mode="Opaque" x="501" y="14" width="116" height="13" backcolor="#F0F0F0"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="11" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{valorMonetario}]]></textFieldExpression>
			</textField>
			<image isUsingCache="true" onErrorType="Blank" evaluationTime="Page">
				<reportElement key="image-1" x="4" y="2" width="121" height="39" isPrintWhenDetailOverflows="true" forecolor="#FFFFFF"/>
				<imageExpression class="java.io.InputStream"><![CDATA[$P{logoPadraoRelatorio}]]></imageExpression>
			</image>
			<line>
				<reportElement x="1" y="63" width="624" height="1"/>
			</line>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-229" mode="Opaque" x="167" y="68" width="197" height="12"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="9" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{responsavelDevolucao.colaboradorVO.pessoa.nome}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement stretchType="RelativeToBandHeight" x="8" y="88" width="280" height="13"/>
				<textElement textAlignment="Justified"/>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{informacoes}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement key="staticText-88" positionType="Float" mode="Opaque" x="164" y="46" width="280" height="13"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="SansSerif" size="9" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[ Nome do Responsável Devolução]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-85" positionType="Float" mode="Opaque" x="4" y="46" width="61" height="13"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="SansSerif" size="9" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[DT Devolução]]></text>
			</staticText>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-229" mode="Opaque" x="513" y="68" width="69" height="12"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="9" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.Integer"><![CDATA[($F{contrato.codigo}.intValue() > 0 ? $F{contrato.codigo} : $F{produtoVO.codigo})]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="false">
				<reportElement key="textField-229" mode="Opaque" x="8" y="67" width="146" height="12"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="9" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{dataFormatada}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-229" mode="Opaque" x="511" y="46" width="66" height="13"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="SansSerif" size="9" isBold="true" isItalic="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[($F{contrato.codigo}.intValue() > 0 ? "Cod Contrato" : "Cod Produto")]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="138" y="15" width="218" height="13"/>
				<textElement>
					<font fontName="Microsoft Sans Serif" isBold="true" isItalic="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{empresaVO.endereco}]]></textFieldExpression>
			</textField>
		</band>
		<band height="44">
			<textField isStretchWithOverflow="true">
				<reportElement stretchType="RelativeToBandHeight" x="6" y="22" width="280" height="17"/>
				<textElement textAlignment="Justified"/>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{descricao}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement key="staticText-85" positionType="Float" mode="Opaque" x="7" y="4" width="214" height="13"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="SansSerif" size="9" isBold="true" isItalic="false" isUnderline="true" isStrikeThrough="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Cálculo do Cancelamento]]></text>
			</staticText>
		</band>
		<band height="54">
			<printWhenExpression><![CDATA[$F{apresentarCheques}]]></printWhenExpression>
			<subreport isUsingCache="true">
				<reportElement key="subreport-3" x="6" y="16" width="455" height="34" isRemoveLineWhenBlank="true">
					<printWhenExpression><![CDATA[$F{apresentarCheques}]]></printWhenExpression>
				</reportElement>
				<subreportParameter name="SUBREPORT_DIR">
					<subreportParameterExpression><![CDATA[$P{SUBREPORT_DIR}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="ListaCheque">
					<subreportParameterExpression><![CDATA[$F{listaCheque}]]></subreportParameterExpression>
				</subreportParameter>
				<dataSourceExpression><![CDATA[$F{listaCheque2}]]></dataSourceExpression>
				<subreportExpression class="java.lang.String"><![CDATA[$P{SUBREPORT_DIR} + "MovPagamento_cheques.jasper"]]></subreportExpression>
			</subreport>
			<staticText>
				<reportElement key="staticText-85" mode="Opaque" x="6" y="3" width="214" height="13">
					<printWhenExpression><![CDATA[$F{apresentarCheques}]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="SansSerif" size="9" isBold="true" isItalic="false" isUnderline="true" isStrikeThrough="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Cheques devolvidos]]></text>
			</staticText>
		</band>
		<band height="50">
			<printWhenExpression><![CDATA[$F{apresentarCartoes}]]></printWhenExpression>
			<staticText>
				<reportElement key="staticText-85" mode="Opaque" x="6" y="8" width="214" height="13">
					<printWhenExpression><![CDATA[$F{apresentarCartoes}]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="SansSerif" size="9" isBold="true" isItalic="false" isUnderline="true" isStrikeThrough="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Cartões estornados]]></text>
			</staticText>
			<subreport>
				<reportElement key="subreport-4" x="7" y="23" width="455" height="27" isPrintWhenDetailOverflows="true"/>
				<dataSourceExpression><![CDATA[$F{listaCartoes2}]]></dataSourceExpression>
				<subreportExpression class="java.lang.String"><![CDATA[$P{SUBREPORT_DIR} + "MovPagamento_cartaocredito.jasper"]]></subreportExpression>
			</subreport>
		</band>
		<band height="103">
			<textField>
				<reportElement x="8" y="46" width="259" height="12"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="8" isBold="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA["Resp. Devolução: " + $F{responsavelDevolucao.colaboradorVO.pessoa.nome}]]></textFieldExpression>
			</textField>
			<line>
				<reportElement x="8" y="45" width="259" height="1"/>
			</line>
			<textField pattern="" isBlankWhenNull="false">
				<reportElement key="textField-229" mode="Opaque" x="553" y="77" width="72" height="8"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="6" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{dataFormatada}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="354" y="46" width="260" height="12"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="8" isBold="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA["Cliente: " +($F{contrato.codigo}.intValue() > 0 ? $F{contrato.pessoa.nome} : $F{produtoVO.pessoa.nome})]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="499" y="77" width="49" height="8"/>
				<textElement textAlignment="Right">
					<font size="6" isBold="true"/>
				</textElement>
				<text><![CDATA[Data pagamento:]]></text>
			</staticText>
			<staticText>
				<reportElement x="5" y="78" width="49" height="8"/>
				<textElement>
					<font size="6" isBold="true"/>
				</textElement>
				<text><![CDATA[Data impressão:]]></text>
			</staticText>
			<line>
				<reportElement x="3" y="97" width="624" height="1"/>
				<graphicElement>
					<pen lineWidth="1.5" lineStyle="Dashed"/>
				</graphicElement>
			</line>
			<textField>
				<reportElement x="8" y="5" width="612" height="34"/>
				<textElement verticalAlignment="Middle">
					<font size="9" isBold="true" isItalic="true" pdfFontName="Helvetica-BoldOblique"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA["* Devolvemos a " + ($F{contrato.codigo}.intValue() > 0 ? $F{contrato.pessoa.nome} : $F{produtoVO.pessoa.nome})  +", a quantia de " +$F{valorPorExtenso}+", proveniente de devolução de cancelamento."]]></textFieldExpression>
			</textField>
			<line>
				<reportElement x="354" y="45" width="262" height="1"/>
			</line>
			<textField pattern="dd/MM/yyyy HH:mm:ss" isBlankWhenNull="false">
				<reportElement key="dataImpressao1" mode="Transparent" x="54" y="77" width="60" height="8"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="SansSerif" size="6" isItalic="false" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression class="java.util.Date"><![CDATA[new Date()]]></textFieldExpression>
			</textField>
		</band>
	</detail>
</jasperReport>

<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="ReciboDevolucao" pageWidth="680" pageHeight="878" whenNoDataType="NoDataSection" columnWidth="680" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" whenResourceMissingType="Empty">
	<property name="ireport.zoom" value="1.6934217901613562"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.encoding" value="ISO-8859-1"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="java.util.*"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<parameter name="tituloRelatorio" class="java.lang.String" isForPrompting="false"/>
	<parameter name="nomeEmpresa" class="java.lang.String" isForPrompting="false"/>
	<parameter name="versaoSoftware" class="java.lang.String" isForPrompting="false"/>
	<parameter name="usuario" class="java.lang.String" isForPrompting="false"/>
	<parameter name="filtros" class="java.lang.String" isForPrompting="false"/>
	<parameter name="SUBREPORT_DIR" class="java.lang.String">
		<defaultValueExpression><![CDATA["E:\\desenvolvimentosv\\Projeto Pacto Solucoes\\ZillyonWeb\\src\\java\\relatorio\\designRelatorio\\financeiro\\"]]></defaultValueExpression>
	</parameter>
	<parameter name="SUBREPORT_DIR1" class="java.lang.String">
		<defaultValueExpression><![CDATA["E:\\desenvolvimentosv\\Projeto Pacto Solucoes\\ZillyonWeb\\src\\java\\relatorio\\designRelatorio\\financeiro\\"]]></defaultValueExpression>
	</parameter>
	<parameter name="dataIni" class="java.lang.String" isForPrompting="false"/>
	<parameter name="dataFim" class="java.lang.String" isForPrompting="false"/>
	<parameter name="qtdAV" class="java.lang.String" isForPrompting="false"/>
	<parameter name="qtdCA" class="java.lang.String" isForPrompting="false"/>
	<parameter name="qtdChequeAV" class="java.lang.String" isForPrompting="false"/>
	<parameter name="qtdChequePR" class="java.lang.String" isForPrompting="false"/>
	<parameter name="qtdOutro" class="java.lang.String" isForPrompting="false"/>
	<parameter name="valorAV" class="java.lang.Double" isForPrompting="false"/>
	<parameter name="valorCA" class="java.lang.Double" isForPrompting="false"/>
	<parameter name="valorChequeAV" class="java.lang.Double" isForPrompting="false"/>
	<parameter name="valorChequePR" class="java.lang.Double" isForPrompting="false"/>
	<parameter name="valorOutro" class="java.lang.Double" isForPrompting="false"/>
	<parameter name="logoPadraoRelatorio" class="java.io.InputStream" isForPrompting="false"/>
	<parameter name="qtdCD" class="java.lang.String"/>
	<parameter name="valorCD" class="java.lang.Double"/>
	<parameter name="SUBREPORT_DIR2" class="java.lang.String">
		<defaultValueExpression><![CDATA["E:\\desenvolvimentosv\\Projeto Pacto Solucoes\\ZillyonWeb\\src\\java\\relatorio\\designRelatorio\\financeiro\\"]]></defaultValueExpression>
	</parameter>
	<parameter name="codRecibo" class="java.lang.String"/>
	<parameter name="empresaVO.cnpj" class="java.lang.String"/>
	<parameter name="empresaVO.endereco" class="java.lang.String"/>
	<parameter name="empresaVO.site" class="java.lang.String"/>
	<parameter name="empresaVO.fone" class="java.lang.String"/>
	<parameter name="responsavelDevolucao" class="java.lang.String"/>
	<parameter name="pessoa" class="java.lang.String"/>
	<parameter name="contrato" class="java.lang.Integer"/>
	<parameter name="dataDevolucao" class="java.util.Date"/>
	<parameter name="valorDevolucao" class="java.lang.Double"/>
	<parameter name="descricao" class="java.lang.String"/>
	<parameter name="totalizadores" class="java.lang.Object"/>
	<parameter name="qtdDV" class="java.lang.String"/>
	<parameter name="valorDV" class="java.lang.Double"/>
	<parameter name="somenteSintetico" class="java.lang.Boolean"/>
	<parameter name="moeda" class="java.lang.String"/>
	<field name="descricao" class="java.lang.String"/>
	<field name="dataDevolucao" class="java.util.Date"/>
	<field name="responsavelDevolucao.colaboradorVO.pessoa.nome" class="java.lang.String"/>
	<field name="contrato.codigo" class="java.lang.Integer"/>
	<field name="contrato.pessoa.nome" class="java.lang.String"/>
	<field name="valorDevolucao" class="java.lang.Double"/>
	<field name="valorMonetario" class="java.lang.String"/>
	<field name="dataFormatada" class="java.lang.String"/>
	<field name="apresentarCheques" class="java.lang.Boolean"/>
	<field name="listaCheque2" class="java.lang.Object"/>
	<field name="listaCartoes2" class="java.lang.Object"/>
	<field name="apresentarCartoes" class="java.lang.Boolean"/>
	<field name="produtoVO.codigo" class="java.lang.Integer"/>
	<field name="produtoVO.pessoa.nome" class="java.lang.String"/>
	<field name="reciboEditado" class="java.lang.Integer"/>
	<pageHeader>
		<band height="42">
			<staticText>
				<reportElement key="staticText-85" positionType="Float" mode="Opaque" x="261" y="0" width="75" height="14"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="SansSerif" size="11" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Devoluções]]></text>
			</staticText>
			<textField>
				<reportElement key="staticText-85" positionType="Float" mode="Opaque" x="19" y="20" width="61" height="13"/>
				<textElement textAlignment="Left" verticalAlignment="Middle" markup="none">
					<font fontName="SansSerif" size="9" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$R{DT_Devolucoes}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement key="staticText-85" positionType="Float" mode="Opaque" x="145" y="20" width="146" height="13"/>
				<textElement textAlignment="Left" verticalAlignment="Middle" markup="none">
					<font fontName="SansSerif" size="9" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$R{Resp_Devolucao}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement key="staticText-85" positionType="Float" mode="Opaque" x="366" y="20" width="56" height="13"/>
				<textElement textAlignment="Left" verticalAlignment="Middle" markup="none">
					<font fontName="SansSerif" size="9" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$R{Cliente}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement key="staticText-85" positionType="Float" mode="Opaque" x="530" y="20" width="56" height="13"/>
				<textElement textAlignment="Left" verticalAlignment="Middle" markup="none">
					<font fontName="SansSerif" size="9" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$R{Contrato}]]></textFieldExpression>
			</textField>
			<line>
				<reportElement x="1" y="38" width="624" height="1"/>
			</line>
		</band>
	</pageHeader>
	<detail>
		<band height="58">
			<textField isStretchWithOverflow="true">
				<reportElement x="1" y="36" width="624" height="19"/>
				<textElement/>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{descricao}]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="false">
				<reportElement key="textField-229" mode="Opaque" x="1" y="0" width="109" height="12"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="9" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{dataFormatada}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-229" mode="Opaque" x="498" y="0" width="109" height="12"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="9" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[""+($F{contrato.codigo}.intValue() > 0 ? $F{contrato.codigo} : $F{contrato.codigo}.intValue() > 0 ? $R{Produto} + " " +
    $F{produtoVO.codigo} : $R{Recibo} + " " +
    $F{reciboEditado})]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-229" mode="Opaque" x="322" y="0" width="176" height="12"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="9" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[($F{contrato.codigo}.intValue() > 0 ? $F{contrato.pessoa.nome} : $F{produtoVO.pessoa.nome})]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-229" mode="Opaque" x="128" y="0" width="176" height="12"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="9" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{responsavelDevolucao.colaboradorVO.pessoa.nome}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-229" mode="Opaque" x="5" y="24" width="299" height="12"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="9" isBold="true" isUnderline="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[""+($F{reciboEditado}.intValue() > 0 ? $R{Dev_Cheques_Edicao_PG} : $R{Calculo_Cancelamento})]]></textFieldExpression>
			</textField>
		</band>
		<band height="52">
			<printWhenExpression><![CDATA[$F{apresentarCheques}]]></printWhenExpression>
			<subreport isUsingCache="true">
				<reportElement key="subreport-3" x="10" y="15" width="455" height="34" isRemoveLineWhenBlank="true">
					<printWhenExpression><![CDATA[$F{apresentarCheques}]]></printWhenExpression>
				</reportElement>
				<subreportParameter name="SUBREPORT_DIR">
					<subreportParameterExpression><![CDATA[$P{SUBREPORT_DIR}]]></subreportParameterExpression>
				</subreportParameter>
				<dataSourceExpression><![CDATA[$F{listaCheque2}]]></dataSourceExpression>
				<subreportExpression class="java.lang.String"><![CDATA[$P{SUBREPORT_DIR} + "MovPagamento_cheques.jasper"]]></subreportExpression>
			</subreport>
			<textField>
				<reportElement key="staticText-85" mode="Opaque" x="10" y="2" width="214" height="13">
					<printWhenExpression><![CDATA[$F{apresentarCheques}]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle" markup="none">
					<font fontName="SansSerif" size="9" isBold="true" isItalic="false" isUnderline="true" isStrikeThrough="false" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$R{Cheques_Devolvidos}]]></textFieldExpression>
			</textField>
		</band>
		<band height="48">
			<printWhenExpression><![CDATA[$F{apresentarCartoes}]]></printWhenExpression>
			<textField>
				<reportElement key="staticText-85" mode="Opaque" x="10" y="2" width="214" height="13">
					<printWhenExpression><![CDATA[$F{apresentarCartoes}]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle" markup="none">
					<font fontName="SansSerif" size="9" isBold="true" isItalic="false" isUnderline="true" isStrikeThrough="false" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$R{Cartoes_Estornados}]]></textFieldExpression>
			</textField>
			<subreport>
				<reportElement key="subreport-4" x="11" y="17" width="455" height="27" isPrintWhenDetailOverflows="true"/>
				<subreportParameter name="moeda">
					<subreportParameterExpression><![CDATA[$P{moeda}]]></subreportParameterExpression>
				</subreportParameter>
				<dataSourceExpression><![CDATA[$F{listaCartoes2}]]></dataSourceExpression>
				<subreportExpression class="java.lang.String"><![CDATA[$P{SUBREPORT_DIR} + "MovPagamento_cartaocredito.jasper"]]></subreportExpression>
			</subreport>
		</band>
		<band height="26">
			<line>
				<reportElement positionType="Float" x="0" y="23" width="624" height="1"/>
			</line>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-229" positionType="Float" mode="Transparent" x="337" y="3" width="237" height="13" backcolor="#F0F0F0"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="11" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$R{Valor_Devolvido}+ ": " +
$F{valorMonetario}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<summary>
		<band height="50">
			<subreport>
				<reportElement positionType="Float" stretchType="RelativeToBandHeight" x="5" y="4" width="380" height="30" isRemoveLineWhenBlank="true" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true"/>
				<subreportParameter name="qtdDV">
					<subreportParameterExpression><![CDATA[$P{qtdDV}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="valorDV">
					<subreportParameterExpression><![CDATA[$P{valorDV}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="moeda">
					<subreportParameterExpression><![CDATA[$P{moeda}]]></subreportParameterExpression>
				</subreportParameter>
				<dataSourceExpression><![CDATA[$P{totalizadores}]]></dataSourceExpression>
				<subreportExpression class="java.lang.String"><![CDATA[$P{SUBREPORT_DIR} + "TotalizadoresCaixaPorOperador.jasper"]]></subreportExpression>
			</subreport>
		</band>
	</summary>
	<noData>
		<band height="50">
			<subreport>
				<reportElement positionType="Float" stretchType="RelativeToBandHeight" x="5" y="9" width="380" height="30" isRemoveLineWhenBlank="true" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true"/>
				<subreportParameter name="qtdDV">
					<subreportParameterExpression><![CDATA[$P{qtdDV}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="valorDV">
					<subreportParameterExpression><![CDATA[$P{valorDV}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="moeda">
					<subreportParameterExpression><![CDATA[$P{moeda}]]></subreportParameterExpression>
				</subreportParameter>
				<dataSourceExpression><![CDATA[$P{totalizadores}]]></dataSourceExpression>
				<subreportExpression class="java.lang.String"><![CDATA[$P{SUBREPORT_DIR} + "TotalizadoresCaixaPorOperador.jasper"]]></subreportExpression>
			</subreport>
			<line>
				<reportElement positionType="Float" x="2" y="4" width="624" height="1"/>
			</line>
		</band>
	</noData>
</jasperReport>

¬í sr (net.sf.jasperreports.engine.JasperReport      'Ø L compileDatat Ljava/io/Serializable;L compileNameSuffixt Ljava/lang/String;L 
compilerClassq ~ xr -net.sf.jasperreports.engine.base.JRBaseReport      'Ø ,I PSEUDO_SERIAL_VERSION_UIDI bottomMarginI columnCountI 
columnSpacingI columnWidthZ ignorePaginationZ isFloatColumnFooterZ isSummaryNewPageZ  isSummaryWithPageHeaderAndFooterZ isTitleNewPageI 
leftMarginB orientationI 
pageHeightI 	pageWidthB 
printOrderI rightMarginI 	topMarginB whenNoDataTypeL 
backgroundt $Lnet/sf/jasperreports/engine/JRBand;L columnDirectiont 3Lnet/sf/jasperreports/engine/type/RunDirectionEnum;L columnFooterq ~ L columnHeaderq ~ [ datasetst ([Lnet/sf/jasperreports/engine/JRDataset;L defaultFontt *Lnet/sf/jasperreports/engine/JRReportFont;L defaultStylet %Lnet/sf/jasperreports/engine/JRStyle;L detailq ~ L 
detailSectiont 'Lnet/sf/jasperreports/engine/JRSection;[ fontst +[Lnet/sf/jasperreports/engine/JRReportFont;L formatFactoryClassq ~ L 
importsSett Ljava/util/Set;L languageq ~ L lastPageFooterq ~ L mainDatasett 'Lnet/sf/jasperreports/engine/JRDataset;L nameq ~ L noDataq ~ L orientationValuet 2Lnet/sf/jasperreports/engine/type/OrientationEnum;L 
pageFooterq ~ L 
pageHeaderq ~ L printOrderValuet 1Lnet/sf/jasperreports/engine/type/PrintOrderEnum;[ stylest &[Lnet/sf/jasperreports/engine/JRStyle;L summaryq ~ [ 	templatest /[Lnet/sf/jasperreports/engine/JRReportTemplate;L titleq ~ L whenNoDataTypeValuet 5Lnet/sf/jasperreports/engine/type/WhenNoDataTypeEnum;xp  wñ            q           n  ¨    #    p~r 1net.sf.jasperreports.engine.type.RunDirectionEnum          xr java.lang.Enum          xpt LTRppppppsr .net.sf.jasperreports.engine.base.JRBaseSection      'Ø [ bandst %[Lnet/sf/jasperreports/engine/JRBand;xpur %[Lnet.sf.jasperreports.engine.JRBand;Ý~ìÊ5  xp   sr +net.sf.jasperreports.engine.base.JRBaseBand      'Ø I PSEUDO_SERIAL_VERSION_UIDI heightZ isSplitAllowedL printWhenExpressiont *Lnet/sf/jasperreports/engine/JRExpression;L 	splitTypet Ljava/lang/Byte;L splitTypeValuet 0Lnet/sf/jasperreports/engine/type/SplitTypeEnum;xr 3net.sf.jasperreports.engine.base.JRBaseElementGroup      'Ø L childrent Ljava/util/List;L elementGroupt ,Lnet/sf/jasperreports/engine/JRElementGroup;xpsr java.util.ArrayListxÒÇa I sizexp   w   sr 0net.sf.jasperreports.engine.base.JRBaseSubreport      'Ø L connectionExpressionq ~ L dataSourceExpressionq ~ L 
expressionq ~ L isUsingCachet Ljava/lang/Boolean;[ 
parameterst 3[Lnet/sf/jasperreports/engine/JRSubreportParameter;L parametersMapExpressionq ~ [ returnValuest 5[Lnet/sf/jasperreports/engine/JRSubreportReturnValue;L runToBottomq ~ 'xr .net.sf.jasperreports.engine.base.JRBaseElement      'Ø I PSEUDO_SERIAL_VERSION_UIDI heightZ isPrintInFirstWholeBandZ isPrintRepeatedValuesZ isPrintWhenDetailOverflowsZ isRemoveLineWhenBlankB positionTypeB stretchTypeI widthI xI yL 	backcolort Ljava/awt/Color;L defaultStyleProvidert 4Lnet/sf/jasperreports/engine/JRDefaultStyleProvider;L elementGroupq ~ "L 	forecolorq ~ +L keyq ~ L modeq ~ L 	modeValuet +Lnet/sf/jasperreports/engine/type/ModeEnum;L parentStyleq ~ L parentStyleNameReferenceq ~ L positionTypeValuet 3Lnet/sf/jasperreports/engine/type/PositionTypeEnum;L printWhenExpressionq ~ L printWhenGroupChangest %Lnet/sf/jasperreports/engine/JRGroup;L 
propertiesMapt -Lnet/sf/jasperreports/engine/JRPropertiesMap;[ propertyExpressionst 3[Lnet/sf/jasperreports/engine/JRPropertyExpression;L stretchTypeValuet 2Lnet/sf/jasperreports/engine/type/StretchTypeEnum;xp  wñ                pq ~ q ~ #pppppp~r 1net.sf.jasperreports.engine.type.PositionTypeEnum          xq ~ t FIX_RELATIVE_TO_TOPpppp~r 0net.sf.jasperreports.engine.type.StretchTypeEnum          xq ~ t 
NO_STRETCHpsr 1net.sf.jasperreports.engine.base.JRBaseExpression      'Ø I id[ chunkst 0[Lnet/sf/jasperreports/engine/JRExpressionChunk;L valueClassNameq ~ L valueClassRealNameq ~ xp   ur 0[Lnet.sf.jasperreports.engine.JRExpressionChunk;mYÏÞiK£U  xp   sr 6net.sf.jasperreports.engine.base.JRBaseExpressionChunk      'Ø B typeL textq ~ xpt formast (net.sf.jasperreports.engine.JRDataSourcepsq ~ :   uq ~ =   sq ~ ?t 
SUBREPORT_DIRsq ~ ?t " + "GestaoRecebiveisResumo.jasper"t java.lang.Stringppur 3[Lnet.sf.jasperreports.engine.JRSubreportParameter;[£À¾B  xp   sr 9net.sf.jasperreports.engine.base.JRBaseSubreportParameter      'Ø  xr 7net.sf.jasperreports.engine.base.JRBaseDatasetParameter      'Ø L 
expressionq ~ L nameq ~ xppt 
SUBREPORT_DIRsq ~ Lpt dataInisq ~ Lpt dataFimsq ~ Lpt SUBREPORT_DIR2sq ~ Lpt tituloRelatoriosq ~ Lpt usuariosq ~ Lpt nomeEmpresasq ~ Lpt logoPadraoRelatoriosq ~ Lpt SUBREPORT_DIR1sq ~ Lsq ~ :   uq ~ =   sq ~ ?t moedat java.lang.Objectpt moedasq ~ Lpt versaoSoftwaresq ~ Lpt filtrospppsr +net.sf.jasperreports.engine.base.JRBaseLine      'Ø I PSEUDO_SERIAL_VERSION_UIDB 	directionL directionValuet 4Lnet/sf/jasperreports/engine/type/LineDirectionEnum;xr 5net.sf.jasperreports.engine.base.JRBaseGraphicElement      'Ø I PSEUDO_SERIAL_VERSION_UIDL fillq ~ L 	fillValuet +Lnet/sf/jasperreports/engine/type/FillEnum;L linePent #Lnet/sf/jasperreports/engine/JRPen;L penq ~ xq ~ *  wñ          q       pq ~ q ~ #pt  pppp~q ~ 4t FLOATppppq ~ 8  wîppsr *net.sf.jasperreports.engine.base.JRBasePen      'Ø I PSEUDO_SERIAL_VERSION_UIDL 	lineColorq ~ +L 	lineStyleq ~ L lineStyleValuet 0Lnet/sf/jasperreports/engine/type/LineStyleEnum;L 	lineWidtht Ljava/lang/Float;L penContainert ,Lnet/sf/jasperreports/engine/JRPenContainer;xp  wñppppq ~ pp  wñ ~r 2net.sf.jasperreports.engine.type.LineDirectionEnum          xq ~ t TOP_DOWNsr 0net.sf.jasperreports.engine.base.JRBaseTextField      'Ø I PSEUDO_SERIAL_VERSION_UIDI 
bookmarkLevelB evaluationTimeB hyperlinkTargetB 
hyperlinkTypeZ isStretchWithOverflowL anchorNameExpressionq ~ L evaluationGroupq ~ /L evaluationTimeValuet 5Lnet/sf/jasperreports/engine/type/EvaluationTimeEnum;L 
expressionq ~ L hyperlinkAnchorExpressionq ~ L hyperlinkPageExpressionq ~ [ hyperlinkParameterst 3[Lnet/sf/jasperreports/engine/JRHyperlinkParameter;L hyperlinkReferenceExpressionq ~ L hyperlinkTooltipExpressionq ~ L isBlankWhenNullq ~ 'L 
linkTargetq ~ L linkTypeq ~ L patternq ~ xr 2net.sf.jasperreports.engine.base.JRBaseTextElement      'Ø %I PSEUDO_SERIAL_VERSION_UIDL borderq ~ L borderColorq ~ +L bottomBorderq ~ L bottomBorderColorq ~ +L 
bottomPaddingt Ljava/lang/Integer;L fontNameq ~ L fontSizeq ~ L horizontalAlignmentq ~ L horizontalAlignmentValuet 6Lnet/sf/jasperreports/engine/type/HorizontalAlignEnum;L isBoldq ~ 'L isItalicq ~ 'L 
isPdfEmbeddedq ~ 'L isStrikeThroughq ~ 'L isStyledTextq ~ 'L isUnderlineq ~ 'L 
leftBorderq ~ L leftBorderColorq ~ +L leftPaddingq ~ L lineBoxt 'Lnet/sf/jasperreports/engine/JRLineBox;L lineSpacingq ~ L lineSpacingValuet 2Lnet/sf/jasperreports/engine/type/LineSpacingEnum;L markupq ~ L paddingq ~ L pdfEncodingq ~ L pdfFontNameq ~ L 
reportFontq ~ L rightBorderq ~ L rightBorderColorq ~ +L rightPaddingq ~ L rotationq ~ L 
rotationValuet /Lnet/sf/jasperreports/engine/type/RotationEnum;L 	topBorderq ~ L topBorderColorq ~ +L 
topPaddingq ~ L verticalAlignmentq ~ L verticalAlignmentValuet 4Lnet/sf/jasperreports/engine/type/VerticalAlignEnum;xq ~ *  wñ                 pq ~ q ~ #pt textField-7ppppq ~ 5sq ~ :   uq ~ =   sq ~ ?t faturamentot java.lang.Booleanppppq ~ 8  wñpppppt Microsoft Sans Serifsr java.lang.Integerâ ¤÷8 I valuexr java.lang.Number¬à  xp   	p~r 4net.sf.jasperreports.engine.type.HorizontalAlignEnum          xq ~ t LEFTsr java.lang.BooleanÍ rÕúî Z valuexpppppppppsr .net.sf.jasperreports.engine.base.JRBaseLineBox      'Ø L 
bottomPaddingq ~ L 	bottomPent +Lnet/sf/jasperreports/engine/base/JRBoxPen;L boxContainert ,Lnet/sf/jasperreports/engine/JRBoxContainer;L leftPaddingq ~ L leftPenq ~ L paddingq ~ L penq ~ L rightPaddingq ~ L rightPenq ~ L 
topPaddingq ~ L topPenq ~ xppsr 3net.sf.jasperreports.engine.base.JRBaseBoxBottomPen      'Ø  xr -net.sf.jasperreports.engine.base.JRBaseBoxPen      'Ø L lineBoxq ~ xq ~ t  wñppppq ~ q ~ q ~ psr 1net.sf.jasperreports.engine.base.JRBaseBoxLeftPen      'Ø  xq ~   wñppppq ~ q ~ psq ~   wñppppq ~ q ~ psr 2net.sf.jasperreports.engine.base.JRBaseBoxRightPen      'Ø  xq ~   wñppppq ~ q ~ psr 0net.sf.jasperreports.engine.base.JRBaseBoxTopPen      'Ø  xq ~   wñppppq ~ q ~ pppppt Helvetica-Boldpppppppppp~r 2net.sf.jasperreports.engine.type.VerticalAlignEnum          xq ~ t MIDDLE  wñ       pp~r 3net.sf.jasperreports.engine.type.EvaluationTimeEnum          xq ~ t NOWsq ~ :   uq ~ =   sq ~ ?t "Consulta por faturamento :"t java.lang.Stringppppppq ~ pppsq ~ |  wñ         K      pq ~ q ~ #pt textField-7ppppq ~ 5sq ~ :   uq ~ =   sq ~ ?t faturamentoq ~ ppppq ~ 8  wñpppppt Microsoft Sans Serifq ~ pq ~ q ~ ppppppppsq ~ psq ~   wñppppq ~ ·q ~ ·q ~ °psq ~   wñppppq ~ ·q ~ ·psq ~   wñppppq ~ ·q ~ ·psq ~    wñppppq ~ ·q ~ ·psq ~ ¢  wñppppq ~ ·q ~ ·pppppt 	Helveticappppppppppq ~ ¦  wñ       ppq ~ ©sq ~ :   uq ~ =   sq ~ ?t "De "+ sq ~ ?t dataInicioFaturamentoApresentarsq ~ ?t  +" atÃ© "+ sq ~ ?t dataFimFaturamentoApresentart java.lang.Stringppppppq ~ pppsq ~ |  wñ          d   È   apq ~ q ~ #pq ~ qppppq ~ rppppq ~ 8  wñpppppt Microsoft Sans Serifsq ~    p~q ~ t RIGHTpppppppppsq ~ psq ~   wñppppq ~ Îq ~ Îq ~ Épsq ~   wñppppq ~ Îq ~ Îpsq ~   wñppppq ~ Îq ~ Îpsq ~    wñppppq ~ Îq ~ Îpsq ~ ¢  wñppppq ~ Îq ~ Îppppppppppppppppq ~ ¦  wñ        ppq ~ ©sq ~ :   uq ~ =   sq ~ ?t valorApresentart java.lang.Stringppppppq ~ pppsq ~ |  wñ                apq ~ q ~ #pq ~ qppppq ~ rppppq ~ 8  wñpppppt Microsoft Sans Serifq ~ Ëpq ~ pppppppppsq ~ psq ~   wñppppq ~ Ûq ~ Ûq ~ Ùpsq ~   wñppppq ~ Ûq ~ Ûpsq ~   wñppppq ~ Ûq ~ Ûpsq ~    wñppppq ~ Ûq ~ Ûpsq ~ ¢  wñppppq ~ Ûq ~ Ûppppppppppppppppq ~ ¦  wñ        ppq ~ ©sq ~ :   uq ~ =   sq ~ ?t "Total:"t java.lang.Stringppppppq ~ pppsq ~ |  wñ                 pq ~ q ~ #pt textField-7ppppq ~ 5sq ~ :   uq ~ =   sq ~ ?t compensacaoq ~ ppppq ~ 8  wñpppppt Microsoft Sans Serifq ~ pq ~ q ~ ppppppppsq ~ psq ~   wñppppq ~ íq ~ íq ~ æpsq ~   wñppppq ~ íq ~ ípsq ~   wñppppq ~ íq ~ ípsq ~    wñppppq ~ íq ~ ípsq ~ ¢  wñppppq ~ íq ~ ípppppt Helvetica-Boldppppppppppq ~ ¦  wñ       ppq ~ ©sq ~ :   uq ~ =   sq ~ ?t "Consulta por CompensaÃ§Ã£o :"t java.lang.Stringppppppq ~ pppsq ~ |  wñ         l      pq ~ q ~ #pt textField-7ppppq ~ 5sq ~ :   uq ~ =   sq ~ ?t compensacaoq ~ ppppq ~ 8  wñpppppt Microsoft Sans Serifq ~ pq ~ q ~ ppppppppsq ~ psq ~   wñppppq ~ q ~ q ~ ùpsq ~   wñppppq ~ q ~ psq ~   wñppppq ~ q ~ psq ~    wñppppq ~ q ~ psq ~ ¢  wñppppq ~ q ~ ppppppppppppppppq ~ ¦  wñ       ppq ~ ©sq ~ :   uq ~ =   sq ~ ?t "De "+ sq ~ ?t dataInicioCompensacaoApresentarsq ~ ?t  +" atÃ© "+sq ~ ?t dataFimCompensacaoApresentart java.lang.Stringppppppq ~ pppsq ~ k  wñ           V   Ô   `pq ~ q ~ #ppppppq ~ rppppq ~ 8  wîppsq ~ t  wñppppq ~p  wñ q ~ zsq ~ |  wñ                0pq ~ q ~ #pq ~ qppppq ~ rppppq ~ 8  wñpppppt Microsoft Sans Serifq ~ Ëpq ~ pppppppppsq ~ psq ~   wñppppq ~q ~q ~psq ~   wñppppq ~q ~psq ~   wñppppq ~q ~psq ~    wñppppq ~q ~psq ~ ¢  wñppppq ~q ~ppppppppppppppppq ~ ¦  wñ        ppq ~ ©sq ~ :    uq ~ =   sq ~ ?t 	"Totais:"t java.lang.Stringppppppq ~ pppsq ~ |  wñ                <pq ~ q ~ #pq ~ qppppq ~ rppppq ~ 8  wñpppppt Microsoft Sans Serifq ~ Ëpq ~ pppppppppsq ~ psq ~   wñppppq ~"q ~"q ~ psq ~   wñppppq ~"q ~"psq ~   wñppppq ~"q ~"psq ~    wñppppq ~"q ~"psq ~ ¢  wñppppq ~"q ~"ppppppppppppppppq ~ ¦  wñ        ppq ~ ©sq ~ :   !uq ~ =   sq ~ ?t "EspÃ©cie:"t java.lang.Stringppppppq ~ pppsq ~ |  wñ                Hpq ~ q ~ #pq ~ qppppq ~ rppppq ~ 8  wñpppppt Microsoft Sans Serifq ~ Ëpq ~ pppppppppsq ~ psq ~   wñppppq ~/q ~/q ~-psq ~   wñppppq ~/q ~/psq ~   wñppppq ~/q ~/psq ~    wñppppq ~/q ~/psq ~ ¢  wñppppq ~/q ~/ppppppppppppppppq ~ ¦  wñ        ppq ~ ©sq ~ :   "uq ~ =   sq ~ ?t 	"Boleto:"t java.lang.Stringppppppq ~ pppsq ~ |  wñ                Tpq ~ q ~ #pq ~ qppppq ~ rppppq ~ 8  wñpppppt Microsoft Sans Serifq ~ Ëpq ~ pppppppppsq ~ psq ~   wñppppq ~<q ~<q ~:psq ~   wñppppq ~<q ~<psq ~   wñppppq ~<q ~<psq ~    wñppppq ~<q ~<psq ~ ¢  wñppppq ~<q ~<ppppppppppppppppq ~ ¦  wñ        ppq ~ ©sq ~ :   #uq ~ =   sq ~ ?t "DevoluÃ§Ãµes:"t java.lang.Stringppppppq ~ pppsq ~ |  wñ          d   È   <pq ~ q ~ #pq ~ qppppq ~ rppppq ~ 8  wñpppppt Microsoft Sans Serifq ~ Ëpq ~ Ìpppppppppsq ~ psq ~   wñppppq ~Iq ~Iq ~Gpsq ~   wñppppq ~Iq ~Ipsq ~   wñppppq ~Iq ~Ipsq ~    wñppppq ~Iq ~Ipsq ~ ¢  wñppppq ~Iq ~Ippppppppppppppppq ~ ¦  wñ        ppq ~ ©sq ~ :   $uq ~ =   sq ~ ?t valorEspecieApresentart java.lang.Stringppppppq ~ pppsq ~ |  wñ          d   È   Hpq ~ q ~ #pq ~ qppppq ~ rppppq ~ 8  wñpppppt Microsoft Sans Serifq ~ Ëpq ~ Ìpppppppppsq ~ psq ~   wñppppq ~Vq ~Vq ~Tpsq ~   wñppppq ~Vq ~Vpsq ~   wñppppq ~Vq ~Vpsq ~    wñppppq ~Vq ~Vpsq ~ ¢  wñppppq ~Vq ~Vppppppppppppppppq ~ ¦  wñ        ppq ~ ©sq ~ :   %uq ~ =   sq ~ ?t valorBoletoApresentart java.lang.Stringppppppq ~ pppsq ~ |  wñ          d   È   Tpq ~ q ~ #pq ~ qppppq ~ rppppq ~ 8  wñpppppt Microsoft Sans Serifq ~ Ëpq ~ Ìpppppppppsq ~ psq ~   wñppppq ~cq ~cq ~apsq ~   wñppppq ~cq ~cpsq ~   wñppppq ~cq ~cpsq ~    wñppppq ~cq ~cpsq ~ ¢  wñppppq ~cq ~cppppppppppppppppq ~ ¦  wñ        ppq ~ ©sq ~ :   &uq ~ =   sq ~ ?t "-"+sq ~ ?t valorDevolucoesApresentart java.lang.Stringppppppq ~ pppsq ~ |  wñ                mpq ~ q ~ #pq ~ qppppq ~ rppppq ~ 8  wñpppppt Microsoft Sans Serifq ~ Ëpq ~ pppppppppsq ~ psq ~   wñppppq ~rq ~rq ~ppsq ~   wñppppq ~rq ~rpsq ~   wñppppq ~rq ~rpsq ~    wñppppq ~rq ~rpsq ~ ¢  wñppppq ~rq ~rppppppppppppppppq ~ ¦  wñ        ppq ~ ©sq ~ :   'uq ~ =   sq ~ ?t "Conta Corrente Cliente:"t java.lang.Stringppppppq ~ pppsq ~ |  wñ          d   È   mpq ~ q ~ #pq ~ qppppq ~ rppppq ~ 8  wñpppppt Microsoft Sans Serifq ~ Ëpq ~ Ìpppppppppsq ~ psq ~   wñppppq ~q ~q ~}psq ~   wñppppq ~q ~psq ~   wñppppq ~q ~psq ~    wñppppq ~q ~psq ~ ¢  wñppppq ~q ~ppppppppppppppppq ~ ¦  wñ        ppq ~ ©sq ~ :   (uq ~ =   sq ~ ?t valorContaCorrenteApresentart java.lang.Stringppppppq ~ pppsq ~ |  wñ           *      <sr java.awt.Color¥3u F falphaI valueL cst Ljava/awt/color/ColorSpace;[ 	frgbvaluet [F[ fvalueq ~xp    ÿÿÿÿpppq ~ q ~ #sq ~    ÿ   ppppp~r )net.sf.jasperreports.engine.type.ModeEnum          xq ~ t TRANSPARENTppq ~ 5ppppq ~ 8  wñpppppt Microsoft Sans Serifq ~ Ëpq ~ Ìsq ~  q ~q ~q ~pq ~pppsq ~ psq ~   wñppppq ~q ~q ~psq ~   wñppppq ~q ~psq ~   wñppppq ~q ~psq ~    wñppppq ~q ~psq ~ ¢  wñppppq ~q ~p~r 0net.sf.jasperreports.engine.type.LineSpacingEnum          xq ~ t SINGLEt nonept Cp1252t 	Helveticappppp~r -net.sf.jasperreports.engine.type.RotationEnum          xq ~ t NONEppppq ~ ¦  wñ        ppq ~ ©sq ~ :   )uq ~ =   sq ~ ?t moedat java.lang.Stringppppppq ~ ppq ~ qsq ~ |  wñ           *      Hsq ~    ÿÿÿÿpppq ~ q ~ #sq ~    ÿ   pppppq ~ppq ~ 5ppppq ~ 8  wñpppppt Microsoft Sans Serifq ~ Ëpq ~ Ìq ~q ~q ~q ~pq ~pppsq ~ psq ~   wñppppq ~­q ~­q ~©psq ~   wñppppq ~­q ~­psq ~   wñppppq ~­q ~­psq ~    wñppppq ~­q ~­psq ~ ¢  wñppppq ~­q ~­pq ~t nonept Cp1252t 	Helveticapppppq ~¢ppppq ~ ¦  wñ        ppq ~ ©sq ~ :   *uq ~ =   sq ~ ?t moedat java.lang.Stringppppppq ~ ppq ~ qsq ~ |  wñ           *      Tsq ~    ÿÿÿÿpppq ~ q ~ #sq ~    ÿ   pppppq ~ppq ~ 5ppppq ~ 8  wñpppppt Microsoft Sans Serifq ~ Ëpq ~ Ìq ~q ~q ~q ~pq ~pppsq ~ psq ~   wñppppq ~¿q ~¿q ~»psq ~   wñppppq ~¿q ~¿psq ~   wñppppq ~¿q ~¿psq ~    wñppppq ~¿q ~¿psq ~ ¢  wñppppq ~¿q ~¿pq ~t nonept Cp1252t 	Helveticapppppq ~¢ppppq ~ ¦  wñ        ppq ~ ©sq ~ :   +uq ~ =   sq ~ ?t moedat java.lang.Stringppppppq ~ ppq ~ qsq ~ |  wñ           *      asq ~    ÿÿÿÿpppq ~ q ~ #sq ~    ÿ   pppppq ~ppq ~ 5ppppq ~ 8  wñpppppt Microsoft Sans Serifq ~ Ëpq ~ Ìq ~q ~q ~q ~pq ~pppsq ~ psq ~   wñppppq ~Ñq ~Ñq ~Ípsq ~   wñppppq ~Ñq ~Ñpsq ~   wñppppq ~Ñq ~Ñpsq ~    wñppppq ~Ñq ~Ñpsq ~ ¢  wñppppq ~Ñq ~Ñpq ~t nonept Cp1252t 	Helveticapppppq ~¢ppppq ~ ¦  wñ        ppq ~ ©sq ~ :   ,uq ~ =   sq ~ ?t moedat java.lang.Stringppppppq ~ ppq ~ qsq ~ |  wñ           *      msq ~    ÿÿÿÿpppq ~ q ~ #sq ~    ÿ   pppppq ~ppq ~ 5ppppq ~ 8  wñpppppt Microsoft Sans Serifq ~ Ëpq ~ Ìq ~q ~q ~q ~pq ~pppsq ~ psq ~   wñppppq ~ãq ~ãq ~ßpsq ~   wñppppq ~ãq ~ãpsq ~   wñppppq ~ãq ~ãpsq ~    wñppppq ~ãq ~ãpsq ~ ¢  wñppppq ~ãq ~ãpq ~t nonept Cp1252t 	Helveticapppppq ~¢ppppq ~ ¦  wñ        ppq ~ ©sq ~ :   -uq ~ =   sq ~ ?t moedat java.lang.Stringppppppq ~ ppq ~ qxp  wñ   pppsq ~ sq ~ $   w   sq ~ &  wñ   +     V      pq ~ q ~ñppppppq ~ rpppp~q ~ 7t RELATIVE_TO_BAND_HEIGHTpsq ~ :   2uq ~ =   sq ~ ?t formas2q ~ Bpsq ~ :   3uq ~ =   sq ~ ?t 
SUBREPORT_DIRsq ~ ?t " + "ListasGestaoRecebiveis.jasper"t java.lang.Stringpq ~uq ~ J   
sq ~ Lsq ~ :   /uq ~ =   sq ~ ?t 
SUBREPORT_DIRq ~ ept 
SUBREPORT_DIRsq ~ Lpt dataInisq ~ Lpt dataFimsq ~ Lpt SUBREPORT_DIR2sq ~ Lsq ~ :   0uq ~ =   sq ~ ?t considerarCompensacaoOriginalq ~ ept considerarCompensacaoOriginalsq ~ Lpt tituloRelatoriosq ~ Lpt usuariosq ~ Lpt nomeEmpresasq ~ Lpt logoPadraoRelatoriosq ~ Lpt SUBREPORT_DIR1sq ~ Lsq ~ :   1uq ~ =   sq ~ ?t moedaq ~ ept moedasq ~ Lpt filtrossq ~ Lpt versaoSoftwarepppxp  wñ   Esq ~ :   .uq ~ =   sq ~ ?t detalharq ~ pppppsr java.util.HashSetºD¸·4  xpw   ?@     t "net.sf.jasperreports.engine.data.*t net.sf.jasperreports.engine.*t java.util.*xt javapsr .net.sf.jasperreports.engine.base.JRBaseDataset      'Ø I PSEUDO_SERIAL_VERSION_UIDZ isMainB whenResourceMissingType[ fieldst &[Lnet/sf/jasperreports/engine/JRField;L filterExpressionq ~ [ groupst &[Lnet/sf/jasperreports/engine/JRGroup;L nameq ~ [ 
parameterst *[Lnet/sf/jasperreports/engine/JRParameter;L 
propertiesMapq ~ 0L queryt %Lnet/sf/jasperreports/engine/JRQuery;L resourceBundleq ~ L scriptletClassq ~ [ 
scriptletst *[Lnet/sf/jasperreports/engine/JRScriptlet;[ 
sortFieldst *[Lnet/sf/jasperreports/engine/JRSortField;[ 	variablest )[Lnet/sf/jasperreports/engine/JRVariable;L whenResourceMissingTypeValuet >Lnet/sf/jasperreports/engine/type/WhenResourceMissingTypeEnum;xp  wñ ur &[Lnet.sf.jasperreports.engine.JRField;<ßÇN*òp  xp   sr ,net.sf.jasperreports.engine.base.JRBaseField      'Ø L descriptionq ~ L nameq ~ L 
propertiesMapq ~ 0L valueClassNameq ~ L valueClassRealNameq ~ xppt formassr +net.sf.jasperreports.engine.JRPropertiesMap      'Ø L baseq ~ 0L propertiesListq ~ !L 
propertiesMapt Ljava/util/Map;xppppt java.lang.Objectpsq ~>pt dataInicioFaturamentoApresentarsq ~Apppt java.lang.Stringpsq ~>pt dataFimFaturamentoApresentarsq ~Apppt java.lang.Stringpsq ~>pt dataFimCompensacaoApresentarsq ~Apppt java.lang.Stringpsq ~>pt dataInicioCompensacaoApresentarsq ~Apppt java.lang.Stringpsq ~>pt valorApresentarsq ~Apppt java.lang.Stringpsq ~>pt faturamentosq ~Apppt java.lang.Booleanpsq ~>pt compensacaosq ~Apppt java.lang.Booleanpsq ~>pt detalharsq ~Apppt java.lang.Booleanpsq ~>pt formas2sq ~Apppt java.lang.Objectpsq ~>pt formasOutrossq ~Apppt java.lang.Objectpsq ~>pt valorEspecieApresentarsq ~Apppt java.lang.Stringpsq ~>pt valorBoletoApresentarsq ~Apppt java.lang.Stringpsq ~>pt valorContaCorrenteApresentarsq ~Apppt java.lang.Stringpsq ~>pt valorDevolucoesApresentarsq ~Apppt java.lang.Stringpppt GestaoRecebiveisur *[Lnet.sf.jasperreports.engine.JRParameter;" *Ã`!  xp   +sr 0net.sf.jasperreports.engine.base.JRBaseParameter      'Ø 	Z isForPromptingZ isSystemDefinedL defaultValueExpressionq ~ L descriptionq ~ L nameq ~ L nestedTypeNameq ~ L 
propertiesMapq ~ 0L valueClassNameq ~ L valueClassRealNameq ~ xpppt REPORT_PARAMETERS_MAPpsq ~Apppt 
java.util.Mappsq ~ppt 
JASPER_REPORTpsq ~Apppt (net.sf.jasperreports.engine.JasperReportpsq ~ppt REPORT_CONNECTIONpsq ~Apppt java.sql.Connectionpsq ~ppt REPORT_MAX_COUNTpsq ~Apppt java.lang.Integerpsq ~ppt REPORT_DATA_SOURCEpsq ~Apppq ~ Bpsq ~ppt REPORT_SCRIPTLETpsq ~Apppt /net.sf.jasperreports.engine.JRAbstractScriptletpsq ~ppt 
REPORT_LOCALEpsq ~Apppt java.util.Localepsq ~ppt REPORT_RESOURCE_BUNDLEpsq ~Apppt java.util.ResourceBundlepsq ~ppt REPORT_TIME_ZONEpsq ~Apppt java.util.TimeZonepsq ~ppt REPORT_FORMAT_FACTORYpsq ~Apppt .net.sf.jasperreports.engine.util.FormatFactorypsq ~ppt REPORT_CLASS_LOADERpsq ~Apppt java.lang.ClassLoaderpsq ~ppt REPORT_URL_HANDLER_FACTORYpsq ~Apppt  java.net.URLStreamHandlerFactorypsq ~ppt REPORT_FILE_RESOLVERpsq ~Apppt -net.sf.jasperreports.engine.util.FileResolverpsq ~ppt REPORT_TEMPLATESpsq ~Apppt java.util.Collectionpsq ~ppt SORT_FIELDSpsq ~Apppt java.util.Listpsq ~ppt REPORT_VIRTUALIZERpsq ~Apppt )net.sf.jasperreports.engine.JRVirtualizerpsq ~ppt IS_IGNORE_PAGINATIONpsq ~Apppq ~ psq ~  ppt tituloRelatoriopsq ~Apppt java.lang.Stringpsq ~  ppt nomeEmpresapsq ~Apppt java.lang.Stringpsq ~  ppt versaoSoftwarepsq ~Apppt java.lang.Stringpsq ~  ppt usuariopsq ~Apppt java.lang.Stringpsq ~  ppt filtrospsq ~Apppt java.lang.Stringpsq ~ sq ~ :    uq ~ =   sq ~ ?t p"E:\\desenvolvimentosv\\Projeto Pacto Solucoes\\ZillyonWeb\\src\\java\\relatorio\\designRelatorio\\financeiro\\"t java.lang.Stringppt 
SUBREPORT_DIRpsq ~Apppq ~Üpsq ~ sq ~ :   uq ~ =   sq ~ ?t p"E:\\desenvolvimentosv\\Projeto Pacto Solucoes\\ZillyonWeb\\src\\java\\relatorio\\designRelatorio\\financeiro\\"t java.lang.Stringppt SUBREPORT_DIR1psq ~Apppq ~äpsq ~  ppt logoPadraoRelatoriopsq ~Apppt java.io.InputStreampsq ~ sq ~ :   uq ~ =   sq ~ ?t p"E:\\desenvolvimentosv\\Projeto Pacto Solucoes\\ZillyonWeb\\src\\java\\relatorio\\designRelatorio\\financeiro\\"t java.lang.Stringppt SUBREPORT_DIR2psq ~Apppq ~ðpsq ~ ppt dataFimpsq ~Apppt java.lang.Stringpsq ~ ppt dataInipsq ~Apppt java.lang.Stringpsq ~ ppt inicioFaturamentopsq ~Apppt java.lang.Stringpsq ~ ppt fimFaturamentopsq ~Apppt java.lang.Stringpsq ~ ppt inicioCompensacaopsq ~Apppt java.lang.Stringpsq ~ ppt fimCompensacaopsq ~Apppt java.lang.Stringpsq ~ ppt formaspsq ~Apppt java.lang.Objectpsq ~ ppt diariopsq ~Apppt java.lang.Booleanpsq ~ ppt faturamentopsq ~Apppt java.lang.Booleanpsq ~ ppt compensacaopsq ~Apppt java.lang.Booleanpsq ~ ppt valorTotalGRpsq ~Apppt java.lang.Stringpsq ~ ppt 
valorBoletoGRpsq ~Apppt java.lang.Stringpsq ~ ppt valorDevolucoesGRpsq ~Apppt java.lang.Stringpsq ~ ppt valorEspecieGRpsq ~Apppt java.lang.Stringpsq ~ ppt valorContaCorrenteGRpsq ~Apppt java.lang.Stringpsq ~ ppt considerarCompensacaoOriginalpsq ~Apppt java.lang.Booleanpsq ~ sq ~ :   uq ~ =   sq ~ ?t "R$"t java.lang.Stringppt moedapsq ~Apppq ~8psq ~Apsq ~ $   w   t ireport.zoomt 	ireport.xt 	ireport.yt ireport.scriptlethandlingt ireport.encodingxsr java.util.HashMapÚÁÃ`Ñ F 
loadFactorI 	thresholdxp?@     w      q ~=t 1.8150000000000117q ~At 
ISO-8859-1q ~>t 0q ~?t 118q ~@t 0xpppppur )[Lnet.sf.jasperreports.engine.JRVariable;bæ|,·D  xp   sr /net.sf.jasperreports.engine.base.JRBaseVariable      'Ø I PSEUDO_SERIAL_VERSION_UIDB calculationB 
incrementTypeZ isSystemDefinedB 	resetTypeL calculationValuet 2Lnet/sf/jasperreports/engine/type/CalculationEnum;L 
expressionq ~ L incrementGroupq ~ /L incrementTypeValuet 4Lnet/sf/jasperreports/engine/type/IncrementTypeEnum;L incrementerFactoryClassNameq ~ L incrementerFactoryClassRealNameq ~ L initialValueExpressionq ~ L nameq ~ L 
resetGroupq ~ /L resetTypeValuet 0Lnet/sf/jasperreports/engine/type/ResetTypeEnum;L valueClassNameq ~ L valueClassRealNameq ~ xp  wî   ~r 0net.sf.jasperreports.engine.type.CalculationEnum          xq ~ t SYSTEMpp~r 2net.sf.jasperreports.engine.type.IncrementTypeEnum          xq ~ t NONEppsq ~ :   uq ~ =   sq ~ ?t new java.lang.Integer(1)q ~pt PAGE_NUMBERp~r .net.sf.jasperreports.engine.type.ResetTypeEnum          xq ~ t REPORTq ~psq ~K  wî   q ~Qppq ~Tppsq ~ :   uq ~ =   sq ~ ?t new java.lang.Integer(1)q ~pt 
COLUMN_NUMBERp~q ~[t PAGEq ~psq ~K  wî   ~q ~Pt COUNTsq ~ :   uq ~ =   sq ~ ?t new java.lang.Integer(1)q ~ppq ~Tppsq ~ :   uq ~ =   sq ~ ?t new java.lang.Integer(0)q ~pt REPORT_COUNTpq ~\q ~psq ~K  wî   q ~gsq ~ :   uq ~ =   sq ~ ?t new java.lang.Integer(1)q ~ppq ~Tppsq ~ :   	uq ~ =   sq ~ ?t new java.lang.Integer(0)q ~pt 
PAGE_COUNTpq ~dq ~psq ~K  wî   q ~gsq ~ :   
uq ~ =   sq ~ ?t new java.lang.Integer(1)q ~ppq ~Tppsq ~ :   uq ~ =   sq ~ ?t new java.lang.Integer(0)q ~pt COLUMN_COUNTp~q ~[t COLUMNq ~p~r <net.sf.jasperreports.engine.type.WhenResourceMissingTypeEnum          xq ~ t EMPTYq ~}p~r 0net.sf.jasperreports.engine.type.OrientationEnum          xq ~ t PORTRAITpsq ~ sq ~ $   w   sq ~ |  wñ           i     sq ~    ÿÿÿÿpppq ~ q ~pt 	dataRel-1pq ~ppq ~ 5ppppq ~ 8  wñpppppt Verdanaq ~ Ëp~q ~ t CENTERpq ~pppppppsq ~ sq ~     sq ~   wñsq ~    ÿfffpppp~r .net.sf.jasperreports.engine.type.LineStyleEnum          xq ~ t SOLIDsr java.lang.FloatÚíÉ¢Û<ðì F valuexq ~ ?   q ~q ~q ~psq ~   wñsq ~    ÿfffppppq ~sq ~?   q ~q ~psq ~   wñppppq ~q ~psq ~    wñsq ~    ÿfffppppq ~sq ~?   q ~q ~psq ~ ¢  wñsq ~    ÿfffppppq ~sq ~?   q ~q ~pppppt 	Helveticappppppppppq ~ ¦  wñ        ppq ~ ©sq ~ :   uq ~ =   sq ~ ?t 
new Date()t java.util.Dateppppppq ~ppt dd/MM/yyyy HH.mm.sssq ~ |  wñ   $       µ   S   pq ~ q ~pt textField-2ppppq ~ 5ppppq ~ 8  wñpppppt Arialsq ~    pq ~q ~ ppppppppsq ~ psq ~   wñsq ~    ÿfffppppq ~sq ~?   q ~´q ~´q ~°psq ~   wñppq ~sq ~    q ~´q ~´psq ~   wñppq ~sq ~?   q ~´q ~´psq ~    wñppq ~sq ~    q ~´q ~´psq ~ ¢  wñsq ~    ÿfffppppq ~sq ~?   q ~´q ~´pppppt Helvetica-Boldppppppppppq ~ ¦  wñ        ppq ~ ©sq ~ :   
uq ~ =   sq ~ ?t tituloRelatoriot java.lang.Stringppppppq ~pppsr ,net.sf.jasperreports.engine.base.JRBaseImage      'Ø *I PSEUDO_SERIAL_VERSION_UIDI 
bookmarkLevelB evaluationTimeB hyperlinkTargetB 
hyperlinkTypeZ isLazyB onErrorTypeL anchorNameExpressionq ~ L borderq ~ L borderColorq ~ +L bottomBorderq ~ L bottomBorderColorq ~ +L 
bottomPaddingq ~ L evaluationGroupq ~ /L evaluationTimeValueq ~ }L 
expressionq ~ L horizontalAlignmentq ~ L horizontalAlignmentValueq ~ L hyperlinkAnchorExpressionq ~ L hyperlinkPageExpressionq ~ [ hyperlinkParametersq ~ ~L hyperlinkReferenceExpressionq ~ L hyperlinkTooltipExpressionq ~ L isUsingCacheq ~ 'L 
leftBorderq ~ L leftBorderColorq ~ +L leftPaddingq ~ L lineBoxq ~ L 
linkTargetq ~ L linkTypeq ~ L onErrorTypeValuet 2Lnet/sf/jasperreports/engine/type/OnErrorTypeEnum;L paddingq ~ L rightBorderq ~ L rightBorderColorq ~ +L rightPaddingq ~ L 
scaleImageq ~ L scaleImageValuet 1Lnet/sf/jasperreports/engine/type/ScaleImageEnum;L 	topBorderq ~ L topBorderColorq ~ +L 
topPaddingq ~ L verticalAlignmentq ~ L verticalAlignmentValueq ~ xq ~ m  wñ   $       R       pq ~ q ~pt image-1ppppq ~ rppppq ~ 8  wîppsq ~ t  wñppppq ~Êp  wñ         ppppppp~q ~ ¨t PAGEsq ~ :   uq ~ =   sq ~ ?t logoPadraoRelatoriot java.io.InputStreamppppppppq ~ pppsq ~ psq ~   wñsq ~    ÿfffppppq ~sq ~?   q ~Ôq ~Ôq ~Êpsq ~   wñsq ~    ÿfffppppq ~sq ~?   q ~Ôq ~Ôpsq ~   wñppppq ~Ôq ~Ôpsq ~    wñsq ~    ÿfffppppq ~sq ~?   q ~Ôq ~Ôpsq ~ ¢  wñsq ~    ÿfffppppq ~sq ~?   q ~Ôq ~Ôpp~r 0net.sf.jasperreports.engine.type.OnErrorTypeEnum          xq ~ t BLANKpppppppppppsq ~ |  wñ           i     
pq ~ q ~pppq ~ppq ~ 5ppppq ~ 8  wñpppppt Verdanaq ~ Ëpq ~pppppppppsq ~ psq ~   wñpp~q ~t DOTTEDsq ~?   q ~çq ~çq ~åpsq ~   wñppq ~ésq ~?   q ~çq ~çpsq ~   wñppppq ~çq ~çpsq ~    wñppq ~ésq ~?   q ~çq ~çpsq ~ ¢  wñppppq ~çq ~çppppppppppppppppq ~ ¦  wñ        ppq ~ ©sq ~ :   uq ~ =   sq ~ ?t 
"UsuÃ¡rio:"+ sq ~ ?t usuariot java.lang.Stringpppppppppq ~ qsq ~ |  wñ           F     pq ~ q ~ppppppq ~ 5ppppq ~ 8  wñpppppt Verdanaq ~ Ëpq ~ Ìpppppppppsq ~ psq ~   wñppq ~ésq ~?   q ~ûq ~ûq ~ùpsq ~   wñppq ~ésq ~?   q ~ûq ~ûpsq ~   wñppppq ~ûq ~ûpsq ~    wñppppq ~ûq ~ûpsq ~ ¢  wñppppq ~ûq ~ûppppppppppppppppq ~ ¦  wñ        ppq ~ ©sq ~ :   uq ~ =   sq ~ ?t "PÃ¡gina "+sq ~ ?t PAGE_NUMBERsq ~ ?t +" de"t java.lang.Stringppppppppppsq ~ |  wñ           #  N   pq ~ q ~ppppppq ~ 5ppppq ~ 8  wñpppppt Verdanaq ~ Ëpppppppppppsq ~ psq ~   wñppq ~ésq ~?   q ~q ~q ~sq ~    sq ~   wñppppq ~q ~psq ~   wñppppq ~q ~psq ~    wñppq ~ésq ~?   q ~q ~psq ~ ¢  wñppppq ~q ~ppppppppppppppppq ~ ¦  wñ        pp~q ~ ¨t REPORTsq ~ :   uq ~ =   sq ~ ?t PAGE_NUMBERt java.lang.Integerppppppppppsq ~ |  wñ          q       %pq ~ q ~pt 
textField-216ppppq ~ 5ppppq ~ô  wñpppppt Arialsq ~    
pq ~q ~ q ~ pppppppsq ~ psq ~   wñsq ~    ÿfffppppq ~sq ~?   q ~"q ~"q ~psq ~   wñppq ~sq ~?   q ~"q ~"psq ~   wñppppq ~"q ~"psq ~    wñppq ~sq ~?   q ~"q ~"psq ~ ¢  wñppppq ~"q ~"pppppt Helvetica-BoldObliqueppppppppppp  wñ       ppq ~ ©sq ~ :   uq ~ =   sq ~ ?t filtrost java.lang.Stringppppppq ~pppxp  wñ   9pp~r .net.sf.jasperreports.engine.type.SplitTypeEnum          xq ~ t STRETCH~r /net.sf.jasperreports.engine.type.PrintOrderEnum          xq ~ t VERTICALpsq ~ sq ~ $   w   sq ~ |  wñ         l      pq ~ q ~8pt textField-7ppppq ~ 5sq ~ :   5uq ~ =   sq ~ ?t faturamentoq ~ ppppq ~ 8  wñpppppt Microsoft Sans Serifq ~ pq ~ q ~ ppppppppsq ~ psq ~   wñppppq ~Aq ~Aq ~:psq ~   wñppppq ~Aq ~Apsq ~   wñppppq ~Aq ~Apsq ~    wñppppq ~Aq ~Apsq ~ ¢  wñppppq ~Aq ~Apppppt 	Helveticappppppppppq ~ ¦  wñ       ppq ~ ©sq ~ :   6uq ~ =   sq ~ ?t "De "+ sq ~ ?t inicioFaturamentosq ~ ?t  +" atÃ© "+ sq ~ ?t fimFaturamentot java.lang.Stringppppppq ~ pppsq ~ |  wñ         ²      &pq ~ q ~8pt textField-7ppppq ~ 5sq ~ :   7uq ~ =   sq ~ ?t compensacaoq ~ ppppq ~ 8  wñpppppt Microsoft Sans Serifq ~ pq ~ q ~ ppppppppsq ~ psq ~   wñppppq ~Zq ~Zq ~Spsq ~   wñppppq ~Zq ~Zpsq ~   wñppppq ~Zq ~Zpsq ~    wñppppq ~Zq ~Zpsq ~ ¢  wñppppq ~Zq ~Zpppppt 	Helveticappppppppppq ~ ¦  wñ       ppq ~ ©sq ~ :   8uq ~ =   sq ~ ?t "De "+ sq ~ ?t inicioCompensacaosq ~ ?t  +" atÃ© "+sq ~ ?t fimCompensacaot java.lang.Stringppppppq ~ pppsq ~ |  wñ                 &pq ~ q ~8pt textField-7ppppq ~ 5sq ~ :   9uq ~ =   sq ~ ?t compensacaoq ~ ppppq ~ 8  wñpppppt Microsoft Sans Serifq ~ pq ~ q ~ ppppppppsq ~ psq ~   wñppppq ~sq ~sq ~lpsq ~   wñppppq ~sq ~spsq ~   wñppppq ~sq ~spsq ~    wñppppq ~sq ~spsq ~ ¢  wñppppq ~sq ~spppppt Helvetica-Boldppppppppppq ~ ¦  wñ       ppq ~ ©sq ~ :   :uq ~ =   sq ~ ?t $"PerÃ­odo total por CompensaÃ§Ã£o :"t java.lang.Stringppppppq ~ pppsr 1net.sf.jasperreports.engine.base.JRBaseStaticText      'Ø L textq ~ xq ~   wñ           d       	pq ~ q ~8ppppppq ~ 5ppppq ~ 8  wñpppppppppq ~ ppq ~pppppsq ~ psq ~   wñppppq ~q ~q ~psq ~   wñppppq ~q ~psq ~   wñppppq ~q ~psq ~    wñppppq ~q ~psq ~ ¢  wñppppq ~q ~pppppt Helvetica-Boldpppppppppppt Totais:sq ~ |  wñ                 pq ~ q ~8pt textField-7ppppq ~ 5sq ~ :   ;uq ~ =   sq ~ ?t faturamentoq ~ ppppq ~ 8  wñpppppt Microsoft Sans Serifq ~ pq ~ q ~ ppppppppsq ~ psq ~   wñppppq ~q ~q ~psq ~   wñppppq ~q ~psq ~   wñppppq ~q ~psq ~    wñppppq ~q ~psq ~ ¢  wñppppq ~q ~pppppt Helvetica-Boldppppppppppq ~ ¦  wñ       ppq ~ ©sq ~ :   <uq ~ =   sq ~ ?t ""PerÃ­odo total por faturamento :"t java.lang.Stringppppppq ~ pppsq ~ &  wñ                2pq ~ q ~8ppppppq ~ 5ppppq ~ 8psq ~ :   >uq ~ =   sq ~ ?t formasq ~ Bpsq ~ :   ?uq ~ =   sq ~ ?t 
SUBREPORT_DIRsq ~ ?t " + "GestaoRecebiveisResumo.jasper"t java.lang.Stringppuq ~ J   sq ~ Lpt 
SUBREPORT_DIRsq ~ Lpt dataInisq ~ Lpt dataFimsq ~ Lpt SUBREPORT_DIR2sq ~ Lpt tituloRelatoriosq ~ Lpt usuariosq ~ Lpt nomeEmpresasq ~ Lpt logoPadraoRelatoriosq ~ Lpt SUBREPORT_DIR1sq ~ Lsq ~ :   =uq ~ =   sq ~ ?t moedaq ~ ept moedasq ~ Lpt versaoSoftwaresq ~ Lpt filtrospppsq ~ |  wñ          d   È   ypq ~ q ~8pq ~ qppppq ~ rppppq ~ 8  wñpppppt Microsoft Sans Serifq ~ Ëpq ~ Ìpppppppppsq ~ psq ~   wñppppq ~Çq ~Çq ~Åpsq ~   wñppppq ~Çq ~Çpsq ~   wñppppq ~Çq ~Çpsq ~    wñppppq ~Çq ~Çpsq ~ ¢  wñppppq ~Çq ~Çppppppppppppppppq ~ ¦  wñ        ppq ~ ©sq ~ :   @uq ~ =   sq ~ ?t valorTotalGRt java.lang.Stringppppppq ~ pppsq ~ |  wñ                ypq ~ q ~8pq ~ qppppq ~ rppppq ~ 8  wñpppppt Microsoft Sans Serifq ~ Ëpq ~ pppppppppsq ~ psq ~   wñppppq ~Ôq ~Ôq ~Òpsq ~   wñppppq ~Ôq ~Ôpsq ~   wñppppq ~Ôq ~Ôpsq ~    wñppppq ~Ôq ~Ôpsq ~ ¢  wñppppq ~Ôq ~Ôppppppppppppppppq ~ ¦  wñ        ppq ~ ©sq ~ :   Auq ~ =   sq ~ ?t "Total:"t java.lang.Stringppppppq ~ pppsq ~ k  wñ           V   Ö   wpq ~ q ~8ppppppq ~ rppppq ~ 8  wîppsq ~ t  wñppppq ~ßp  wñ q ~ zsq ~ |  wñ                Hpq ~ q ~8pq ~ qppppq ~ rppppq ~ 8  wñpppppt Microsoft Sans Serifq ~ Ëpq ~ pppppppppsq ~ psq ~   wñppppq ~ãq ~ãq ~ápsq ~   wñppppq ~ãq ~ãpsq ~   wñppppq ~ãq ~ãpsq ~    wñppppq ~ãq ~ãpsq ~ ¢  wñppppq ~ãq ~ãppppppppppppppppq ~ ¦  wñ        ppq ~ ©sq ~ :   Buq ~ =   sq ~ ?t 	"Totais:"t java.lang.Stringppppppq ~ pppsq ~ |  wñ                Tpq ~ q ~8pq ~ qppppq ~ rppppq ~ 8  wñpppppt Microsoft Sans Serifq ~ Ëpq ~ pppppppppsq ~ psq ~   wñppppq ~ðq ~ðq ~îpsq ~   wñppppq ~ðq ~ðpsq ~   wñppppq ~ðq ~ðpsq ~    wñppppq ~ðq ~ðpsq ~ ¢  wñppppq ~ðq ~ðppppppppppppppppq ~ ¦  wñ        ppq ~ ©sq ~ :   Cuq ~ =   sq ~ ?t "EspÃ©cie:"t java.lang.Stringppppppq ~ pppsq ~ |  wñ                `pq ~ q ~8pq ~ qppppq ~ rppppq ~ 8  wñpppppt Microsoft Sans Serifq ~ Ëpq ~ pppppppppsq ~ psq ~   wñppppq ~ýq ~ýq ~ûpsq ~   wñppppq ~ýq ~ýpsq ~   wñppppq ~ýq ~ýpsq ~    wñppppq ~ýq ~ýpsq ~ ¢  wñppppq ~ýq ~ýppppppppppppppppq ~ ¦  wñ        ppq ~ ©sq ~ :   Duq ~ =   sq ~ ?t 	"Boleto:"t java.lang.Stringppppppq ~ pppsq ~ |  wñ                lpq ~ q ~8pq ~ qppppq ~ rppppq ~ 8  wñpppppt Microsoft Sans Serifq ~ Ëpq ~ pppppppppsq ~ psq ~   wñppppq ~
q ~
q ~psq ~   wñppppq ~
q ~
psq ~   wñppppq ~
q ~
psq ~    wñppppq ~
q ~
psq ~ ¢  wñppppq ~
q ~
ppppppppppppppppq ~ ¦  wñ        ppq ~ ©sq ~ :   Euq ~ =   sq ~ ?t "DevoluÃ§Ãµes:"t java.lang.Stringppppppq ~ pppsq ~ |  wñ          d   È   Tpq ~ q ~8pq ~ qppppq ~ rppppq ~ 8  wñpppppt Microsoft Sans Serifq ~ Ëpq ~ Ìpppppppppsq ~ psq ~   wñppppq ~q ~q ~psq ~   wñppppq ~q ~psq ~   wñppppq ~q ~psq ~    wñppppq ~q ~psq ~ ¢  wñppppq ~q ~ppppppppppppppppq ~ ¦  wñ        ppq ~ ©sq ~ :   Fuq ~ =   sq ~ ?t valorEspecieGRt java.lang.Stringppppppq ~ pppsq ~ |  wñ          d   È   `pq ~ q ~8pq ~ qppppq ~ rppppq ~ 8  wñpppppt Microsoft Sans Serifq ~ Ëpq ~ Ìpppppppppsq ~ psq ~   wñppppq ~$q ~$q ~"psq ~   wñppppq ~$q ~$psq ~   wñppppq ~$q ~$psq ~    wñppppq ~$q ~$psq ~ ¢  wñppppq ~$q ~$ppppppppppppppppq ~ ¦  wñ        ppq ~ ©sq ~ :   Guq ~ =   sq ~ ?t 
valorBoletoGRt java.lang.Stringppppppq ~ pppsq ~ |  wñ          b   È   mpq ~ q ~8pq ~ qppppq ~ rppppq ~ 8  wñpppppt Microsoft Sans Serifq ~ Ëpq ~ Ìpppppppppsq ~ psq ~   wñppppq ~1q ~1q ~/psq ~   wñppppq ~1q ~1psq ~   wñppppq ~1q ~1psq ~    wñppppq ~1q ~1psq ~ ¢  wñppppq ~1q ~1ppppppppppppppppq ~ ¦  wñ        ppq ~ ©sq ~ :   Huq ~ =   sq ~ ?t "-"+sq ~ ?t valorDevolucoesGRt java.lang.Stringppppppq ~ pppsq ~ |  wñ                pq ~ q ~8pq ~ qppppq ~ rppppq ~ 8  wñpppppt Microsoft Sans Serifq ~ Ëpq ~ pppppppppsq ~ psq ~   wñppppq ~@q ~@q ~>psq ~   wñppppq ~@q ~@psq ~   wñppppq ~@q ~@psq ~    wñppppq ~@q ~@psq ~ ¢  wñppppq ~@q ~@ppppppppppppppppq ~ ¦  wñ        ppq ~ ©sq ~ :   Iuq ~ =   sq ~ ?t "Conta Corrente Cliente:"t java.lang.Stringppppppq ~ pppsq ~ |  wñ          d   È   pq ~ q ~8pq ~ qppppq ~ rppppq ~ 8  wñpppppt Microsoft Sans Serifq ~ Ëpq ~ Ìpppppppppsq ~ psq ~   wñppppq ~Mq ~Mq ~Kpsq ~   wñppppq ~Mq ~Mpsq ~   wñppppq ~Mq ~Mpsq ~    wñppppq ~Mq ~Mpsq ~ ¢  wñppppq ~Mq ~Mppppppppppppppppq ~ ¦  wñ        ppq ~ ©sq ~ :   Juq ~ =   sq ~ ?t valorContaCorrenteGRt java.lang.Stringppppppq ~ pppsq ~ |  wñ           *      Tsq ~    ÿÿÿÿpppq ~ q ~8sq ~    ÿ   pppppq ~ppq ~ 5ppppq ~ 8  wñpppppt Microsoft Sans Serifq ~ Ëpq ~ Ìq ~q ~q ~q ~pq ~pppsq ~ psq ~   wñppppq ~\q ~\q ~Xpsq ~   wñppppq ~\q ~\psq ~   wñppppq ~\q ~\psq ~    wñppppq ~\q ~\psq ~ ¢  wñppppq ~\q ~\pq ~t nonept Cp1252t 	Helveticapppppq ~¢ppppq ~ ¦  wñ        ppq ~ ©sq ~ :   Kuq ~ =   sq ~ ?t moedat java.lang.Stringppppppq ~ ppq ~ qsq ~ |  wñ           *      `sq ~    ÿÿÿÿpppq ~ q ~8sq ~    ÿ   pppppq ~ppq ~ 5ppppq ~ 8  wñpppppt Microsoft Sans Serifq ~ Ëpq ~ Ìq ~q ~q ~q ~pq ~pppsq ~ psq ~   wñppppq ~nq ~nq ~jpsq ~   wñppppq ~nq ~npsq ~   wñppppq ~nq ~npsq ~    wñppppq ~nq ~npsq ~ ¢  wñppppq ~nq ~npq ~t nonept Cp1252t 	Helveticapppppq ~¢ppppq ~ ¦  wñ        ppq ~ ©sq ~ :   Luq ~ =   sq ~ ?t moedat java.lang.Stringppppppq ~ ppq ~ qsq ~ |  wñ           '      ksq ~    ÿÿÿÿpppq ~ q ~8sq ~    ÿ   pppppq ~ppq ~ 5ppppq ~ 8  wñpppppt Microsoft Sans Serifq ~ Ëpq ~ Ìq ~q ~q ~q ~pq ~pppsq ~ psq ~   wñppppq ~q ~q ~|psq ~   wñppppq ~q ~psq ~   wñppppq ~q ~psq ~    wñppppq ~q ~psq ~ ¢  wñppppq ~q ~pq ~t nonept Cp1252t 	Helveticapppppq ~¢ppppq ~ ¦  wñ        ppq ~ ©sq ~ :   Muq ~ =   sq ~ ?t moedat java.lang.Stringppppppq ~ ppq ~ qsq ~ |  wñ           *      wsq ~    ÿÿÿÿpppq ~ q ~8sq ~    ÿ   pppppq ~ppq ~ 5ppppq ~ 8  wñpppppt Microsoft Sans Serifq ~ Ëpq ~ Ìq ~q ~q ~q ~pq ~pppsq ~ psq ~   wñppppq ~q ~q ~psq ~   wñppppq ~q ~psq ~   wñppppq ~q ~psq ~    wñppppq ~q ~psq ~ ¢  wñppppq ~q ~pq ~t nonept Cp1252t 	Helveticapppppq ~¢ppppq ~ ¦  wñ        ppq ~ ©sq ~ :   Nuq ~ =   sq ~ ?t moedat java.lang.Stringppppppq ~ ppq ~ qsq ~ |  wñ           *      sq ~    ÿÿÿÿpppq ~ q ~8sq ~    ÿ   pppppq ~ppq ~ 5ppppq ~ 8  wñpppppt Microsoft Sans Serifq ~ Ëpq ~ Ìq ~q ~q ~q ~pq ~pppsq ~ psq ~   wñppppq ~¤q ~¤q ~ psq ~   wñppppq ~¤q ~¤psq ~   wñppppq ~¤q ~¤psq ~    wñppppq ~¤q ~¤psq ~ ¢  wñppppq ~¤q ~¤pq ~t nonept Cp1252t 	Helveticapppppq ~¢ppppq ~ ¦  wñ        ppq ~ ©sq ~ :   Ouq ~ =   sq ~ ?t moedat java.lang.Stringppppppq ~ ppq ~ qxp  wñ   sq ~ :   4uq ~ =   sq ~ ?t diarioq ~ ppppp~r 3net.sf.jasperreports.engine.type.WhenNoDataTypeEnum          xq ~ t ALL_SECTIONS_NO_DETAILsr 6net.sf.jasperreports.engine.design.JRReportCompileData      'Ø L crosstabCompileDataq ~BL datasetCompileDataq ~BL mainDatasetCompileDataq ~ xpsq ~B?@     w       xsq ~B?@     w       xur [B¬óøTà  xp  :ñÊþº¾   .¼ %GestaoRecebiveis_1576530200235_683987  ,net/sf/jasperreports/engine/fill/JREvaluator  parameter_fimFaturamento 2Lnet/sf/jasperreports/engine/fill/JRFillParameter; parameter_valorEspecieGR parameter_JASPER_REPORT parameter_REPORT_TIME_ZONE parameter_usuario parameter_REPORT_FILE_RESOLVER parameter_compensacao parameter_REPORT_PARAMETERS_MAP parameter_inicioFaturamento parameter_faturamento parameter_diario parameter_SUBREPORT_DIR1 parameter_REPORT_CLASS_LOADER parameter_inicioCompensacao $parameter_REPORT_URL_HANDLER_FACTORY parameter_REPORT_DATA_SOURCE parameter_valorTotalGR parameter_IS_IGNORE_PAGINATION parameter_SUBREPORT_DIR2 parameter_REPORT_MAX_COUNT parameter_valorDevolucoesGR parameter_fimCompensacao parameter_REPORT_TEMPLATES parameter_dataIni parameter_REPORT_LOCALE 'parameter_considerarCompensacaoOriginal parameter_REPORT_VIRTUALIZER parameter_SORT_FIELDS parameter_logoPadraoRelatorio parameter_REPORT_SCRIPTLET parameter_formas parameter_REPORT_CONNECTION parameter_SUBREPORT_DIR parameter_dataFim parameter_REPORT_FORMAT_FACTORY parameter_tituloRelatorio parameter_nomeEmpresa parameter_valorBoletoGR parameter_moeda parameter_valorContaCorrenteGR  parameter_REPORT_RESOURCE_BUNDLE parameter_versaoSoftware parameter_filtros field_detalhar .Lnet/sf/jasperreports/engine/fill/JRFillField; field_valorEspecieApresentar "field_dataFimCompensacaoApresentar field_compensacao field_formas "field_valorContaCorrenteApresentar "field_dataFimFaturamentoApresentar field_faturamento field_formasOutros field_valorBoletoApresentar 
field_formas2 field_valorDevolucoesApresentar %field_dataInicioFaturamentoApresentar field_valorApresentar %field_dataInicioCompensacaoApresentar variable_PAGE_NUMBER 1Lnet/sf/jasperreports/engine/fill/JRFillVariable; variable_COLUMN_NUMBER variable_REPORT_COUNT variable_PAGE_COUNT variable_COLUMN_COUNT <init> ()V Code G H
  J  	  L  	  N  	  P 	 	  R 
 	  T  	  V  	  X 
 	  Z  	  \  	  ^  	  `  	  b  	  d  	  f  	  h  	  j  	  l  	  n  	  p  	  r  	  t  	  v  	  x  	  z  	  |  	  ~   	   ! 	   " 	   # 	   $ 	   % 	   & 	   ' 	   ( 	   ) 	   * 	   + 	   , 	   - 	   . 	   / 	   0 	    1 2	  ¢ 3 2	  ¤ 4 2	  ¦ 5 2	  ¨ 6 2	  ª 7 2	  ¬ 8 2	  ® 9 2	  ° : 2	  ² ; 2	  ´ < 2	  ¶ = 2	  ¸ > 2	  º ? 2	  ¼ @ 2	  ¾ A B	  À C B	  Â D B	  Ä E B	  Æ F B	  È LineNumberTable customizedInit 0(Ljava/util/Map;Ljava/util/Map;Ljava/util/Map;)V 
initParams (Ljava/util/Map;)V Í Î
  Ï 
initFields Ñ Î
  Ò initVars Ô Î
  Õ fimFaturamento × 
java/util/Map Ù get &(Ljava/lang/Object;)Ljava/lang/Object; Û Ü Ú Ý 0net/sf/jasperreports/engine/fill/JRFillParameter ß valorEspecieGR á 
JASPER_REPORT ã REPORT_TIME_ZONE å usuario ç REPORT_FILE_RESOLVER é compensacao ë REPORT_PARAMETERS_MAP í inicioFaturamento ï faturamento ñ diario ó SUBREPORT_DIR1 õ REPORT_CLASS_LOADER ÷ inicioCompensacao ù REPORT_URL_HANDLER_FACTORY û REPORT_DATA_SOURCE ý valorTotalGR ÿ IS_IGNORE_PAGINATION SUBREPORT_DIR2 REPORT_MAX_COUNT valorDevolucoesGR fimCompensacao	 REPORT_TEMPLATES dataIni
 
REPORT_LOCALE considerarCompensacaoOriginal REPORT_VIRTUALIZER SORT_FIELDS logoPadraoRelatorio REPORT_SCRIPTLET formas REPORT_CONNECTION 
SUBREPORT_DIR dataFim! REPORT_FORMAT_FACTORY# tituloRelatorio% nomeEmpresa' 
valorBoletoGR) moeda+ valorContaCorrenteGR- REPORT_RESOURCE_BUNDLE/ versaoSoftware1 filtros3 detalhar5 ,net/sf/jasperreports/engine/fill/JRFillField7 valorEspecieApresentar9 dataFimCompensacaoApresentar; valorContaCorrenteApresentar= dataFimFaturamentoApresentar? formasOutrosA valorBoletoApresentarC formas2E valorDevolucoesApresentarG dataInicioFaturamentoApresentarI valorApresentarK dataInicioCompensacaoApresentarM PAGE_NUMBERO /net/sf/jasperreports/engine/fill/JRFillVariableQ 
COLUMN_NUMBERS REPORT_COUNTU 
PAGE_COUNTW COLUMN_COUNTY evaluate (I)Ljava/lang/Object; 
Exceptions java/lang/Throwable^ eE:\desenvolvimentosv\Projeto Pacto Solucoes\ZillyonWeb\src\java\relatorio\designRelatorio\financeiro\` R$b java/lang/Integerd (I)V Gf
eg java/util/Datei
j J getValue ()Ljava/lang/Object;lm
 àn java/lang/Stringp java/io/InputStreamr java/lang/StringBuffert 	UsuÃ¡rio:v (Ljava/lang/String;)V Gx
uy append ,(Ljava/lang/String;)Ljava/lang/StringBuffer;{|
u} toString ()Ljava/lang/String;
u PÃ¡gina 
Rn ,(Ljava/lang/Object;)Ljava/lang/StringBuffer;{
u  de
8n (net/sf/jasperreports/engine/JRDataSource valueOf &(Ljava/lang/Object;)Ljava/lang/String;
q GestaoRecebiveisResumo.jasper java/lang/Boolean Consulta por faturamento : De   atÃ©  Total: Consulta por CompensaÃ§Ã£o : Totais:  	EspÃ©cie:¢ Boleto:¤ 
DevoluÃ§Ãµes:¦ -¨ Conta Corrente Cliente:ª ListasGestaoRecebiveis.jasper¬ "PerÃ­odo total por CompensaÃ§Ã£o :®  PerÃ­odo total por faturamento :° evaluateOld getOldValue³m
R´
8´ evaluateEstimated getEstimatedValue¸m
R¹ 
SourceFile !     ?                 	     
               
                                                                                                     !     "     #     $     %     &     '     (     )     *     +     ,     -     .     /     0     1 2    3 2    4 2    5 2    6 2    7 2    8 2    9 2    : 2    ; 2    < 2    = 2    > 2    ? 2    @ 2    A B    C B    D B    E B    F B     G H  I  X    @*· K*µ M*µ O*µ Q*µ S*µ U*µ W*µ Y*µ [*µ ]*µ _*µ a*µ c*µ e*µ g*µ i*µ k*µ m*µ o*µ q*µ s*µ u*µ w*µ y*µ {*µ }*µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ ¡*µ £*µ ¥*µ §*µ ©*µ «*µ ­*µ ¯*µ ±*µ ³*µ µ*µ ·*µ ¹*µ »*µ ½*µ ¿*µ Á*µ Ã*µ Å*µ Ç*µ É±    Ê   A      	          ! " " ' # , $ 1 % 6 & ; ' @ ( E ) J * O + T , Y - ^ . c / h 0 m 1 r 2 w 3 | 4  5  6  7  8  9  :  ; ¤ < © = ® > ³ ? ¸ @ ½ A Â B Ç C Ì D Ñ E Ö F Û G à H å I ê J ï K ô L ù M þ N O P
 Q R S T! U& V+ W0 X5 Y: Z?   Ë Ì  I   4     *+· Ð*,· Ó*-· Ö±    Ê       f  g 
 h  i  Í Î  I  æ    "*+Ø¹ Þ À àÀ àµ M*+â¹ Þ À àÀ àµ O*+ä¹ Þ À àÀ àµ Q*+æ¹ Þ À àÀ àµ S*+è¹ Þ À àÀ àµ U*+ê¹ Þ À àÀ àµ W*+ì¹ Þ À àÀ àµ Y*+î¹ Þ À àÀ àµ [*+ð¹ Þ À àÀ àµ ]*+ò¹ Þ À àÀ àµ _*+ô¹ Þ À àÀ àµ a*+ö¹ Þ À àÀ àµ c*+ø¹ Þ À àÀ àµ e*+ú¹ Þ À àÀ àµ g*+ü¹ Þ À àÀ àµ i*+þ¹ Þ À àÀ àµ k*+ ¹ Þ À àÀ àµ m*+¹ Þ À àÀ àµ o*+¹ Þ À àÀ àµ q*+¹ Þ À àÀ àµ s*+¹ Þ À àÀ àµ u*+
¹ Þ À àÀ àµ w*+¹ Þ À àÀ àµ y*+¹ Þ À àÀ àµ {*+¹ Þ À àÀ àµ }*+¹ Þ À àÀ àµ *+¹ Þ À àÀ àµ *+¹ Þ À àÀ àµ *+¹ Þ À àÀ àµ *+¹ Þ À àÀ àµ *+¹ Þ À àÀ àµ *+¹ Þ À àÀ àµ *+ ¹ Þ À àÀ àµ *+"¹ Þ À àÀ àµ *+$¹ Þ À àÀ àµ *+&¹ Þ À àÀ àµ *+(¹ Þ À àÀ àµ *+*¹ Þ À àÀ àµ *+,¹ Þ À àÀ àµ *+.¹ Þ À àÀ àµ *+0¹ Þ À àÀ àµ *+2¹ Þ À àÀ àµ *+4¹ Þ À àÀ àµ ¡±    Ê   ² ,   q  r $ s 6 t H u Z v l w ~ x  y ¢ z ´ { Æ | Ø } ê ~ ü    3 F Y l   ¥ ¸ Ë Þ ñ   * = P c v   ¯ Â Õ è û  !   Ñ Î  I  p    *+6¹ Þ À8À8µ £*+:¹ Þ À8À8µ ¥*+<¹ Þ À8À8µ §*+ì¹ Þ À8À8µ ©*+¹ Þ À8À8µ «*+>¹ Þ À8À8µ ­*+@¹ Þ À8À8µ ¯*+ò¹ Þ À8À8µ ±*+B¹ Þ À8À8µ ³*+D¹ Þ À8À8µ µ*+F¹ Þ À8À8µ ·*+H¹ Þ À8À8µ ¹*+J¹ Þ À8À8µ »*+L¹ Þ À8À8µ ½*+N¹ Þ À8À8µ ¿±    Ê   B    ¤  ¥ & ¦ 9 § K ¨ ^ © q ª  «  ¬ © ­ ¼ ® Ï ¯ â ° õ ± ² ³  Ô Î  I        `*+P¹ Þ ÀRÀRµ Á*+T¹ Þ ÀRÀRµ Ã*+V¹ Þ ÀRÀRµ Å*+X¹ Þ ÀRÀRµ Ç*+Z¹ Þ ÀRÀRµ É±    Ê       »  ¼ & ½ 9 ¾ L ¿ _ À [\ ]    _ I  ·    Mª         O  M  T  [  b  i  u        ¥  ±  ½  É  Ô  â  ð    2  @  N  \  j         ®  ß  í  ô    	    H  O  V  ]  d  r      ¥  ³  Á  Ï  Ý  ë  ù      #  1  ?  `  n  |  ­  »  ì  ú        $  2  S  a  h  o  v  }         ¾  Å  Ó  á  ï  ý  aM§ÅaM§¾aM§·cM§°»eY·hM§¤»eY·hM§»eY·hM§»eY·hM§»eY·hM§t»eY·hM§h»eY·hM§\»eY·hM§P»jY·kM§E*´ ¶oÀqM§7*´ ¶oÀsM§)»uYw·z*´ U¶oÀq¶~¶M§»uY·z*´ Á¶Àe¶¶~¶M§ç*´ Á¶ÀeM§Ù*´ ¡¶oÀqM§Ë*´ ¶oÀqM§½*´ «¶ÀM§¯»uY*´ ¶oÀq¸·z¶~¶M§*´ ±¶ÀM§M§y*´ ±¶ÀM§k»uY·z*´ »¶Àq¶~¶~*´ ¯¶Àq¶~¶M§:*´ ½¶ÀqM§,M§%*´ ©¶ÀM§M§*´ ©¶ÀM§»uY·z*´ ¿¶Àq¶~¶~*´ §¶Àq¶~¶M§Ñ¡M§Ê£M§Ã¥M§¼§M§µ*´ ¥¶ÀqM§§*´ µ¶ÀqM§»uY©·z*´ ¹¶Àq¶~¶M§{«M§t*´ ­¶ÀqM§f*´ ¶oÀqM§X*´ ¶oÀqM§J*´ ¶oÀqM§<*´ ¶oÀqM§.*´ ¶oÀqM§ *´ £¶ÀM§*´ ¶oÀqM§*´ ¶oÀM§ö*´ ¶oÀqM§è*´ ·¶ÀM§Ú»uY*´ ¶oÀq¸·z­¶~¶M§¹*´ a¶oÀM§«*´ _¶oÀM§»uY·z*´ ]¶oÀq¶~¶~*´ M¶oÀq¶~¶M§l*´ Y¶oÀM§^»uY·z*´ g¶oÀq¶~¶~*´ w¶oÀq¶~¶M§-*´ Y¶oÀM§¯M§*´ _¶oÀM§
±M§*´ ¶oÀqM§ õ*´ ¶oÀM§ ç»uY*´ ¶oÀq¸·z¶~¶M§ Æ*´ m¶oÀqM§ ¸M§ ±¡M§ ª£M§ £¥M§ §M§ *´ O¶oÀqM§ *´ ¶oÀqM§ y»uY©·z*´ u¶oÀq¶~¶M§ [«M§ T*´ ¶oÀqM§ F*´ ¶oÀqM§ 8*´ ¶oÀqM§ **´ ¶oÀqM§ *´ ¶oÀqM§ *´ ¶oÀqM,°    Ê   ¢   È  ÊP ÎT ÏW Ó[ Ô^ Øb Ùe Ýi Þl âu ãx ç è ì í ñ ò ö¥ ÷¨ û± ü´ ½ÀÉÌ
Ô×âåðó25#@$C(N)Q-\._2j3m78<=A B£F®G±KßLâPíQðUôV÷Z[_	`deiHjKnOoRsVtYx]y`}d~gru¡¥¨³¶ÁÄ Ï¡Ò¥Ý¦àªë«î¯ù°ü´µ
¹º¾#¿&Ã1Ä4È?ÉBÍ`ÎcÒnÓq×|ØÜ­Ý°á»â¾æìçïëúìýðñõöúûÿ$ '25	S
Vadhkorvy"}#'(,-1 2£6¾7Á;Å<È@ÓAÖEáFäJïKòOýP TUYa ²\ ]    _ I  ·    Mª         O  M  T  [  b  i  u        ¥  ±  ½  É  Ô  â  ð    2  @  N  \  j         ®  ß  í  ô    	    H  O  V  ]  d  r      ¥  ³  Á  Ï  Ý  ë  ù      #  1  ?  `  n  |  ­  »  ì  ú        $  2  S  a  h  o  v  }         ¾  Å  Ó  á  ï  ý  aM§ÅaM§¾aM§·cM§°»eY·hM§¤»eY·hM§»eY·hM§»eY·hM§»eY·hM§t»eY·hM§h»eY·hM§\»eY·hM§P»jY·kM§E*´ ¶oÀqM§7*´ ¶oÀsM§)»uYw·z*´ U¶oÀq¶~¶M§»uY·z*´ Á¶µÀe¶¶~¶M§ç*´ Á¶µÀeM§Ù*´ ¡¶oÀqM§Ë*´ ¶oÀqM§½*´ «¶¶ÀM§¯»uY*´ ¶oÀq¸·z¶~¶M§*´ ±¶¶ÀM§M§y*´ ±¶¶ÀM§k»uY·z*´ »¶¶Àq¶~¶~*´ ¯¶¶Àq¶~¶M§:*´ ½¶¶ÀqM§,M§%*´ ©¶¶ÀM§M§*´ ©¶¶ÀM§»uY·z*´ ¿¶¶Àq¶~¶~*´ §¶¶Àq¶~¶M§Ñ¡M§Ê£M§Ã¥M§¼§M§µ*´ ¥¶¶ÀqM§§*´ µ¶¶ÀqM§»uY©·z*´ ¹¶¶Àq¶~¶M§{«M§t*´ ­¶¶ÀqM§f*´ ¶oÀqM§X*´ ¶oÀqM§J*´ ¶oÀqM§<*´ ¶oÀqM§.*´ ¶oÀqM§ *´ £¶¶ÀM§*´ ¶oÀqM§*´ ¶oÀM§ö*´ ¶oÀqM§è*´ ·¶¶ÀM§Ú»uY*´ ¶oÀq¸·z­¶~¶M§¹*´ a¶oÀM§«*´ _¶oÀM§»uY·z*´ ]¶oÀq¶~¶~*´ M¶oÀq¶~¶M§l*´ Y¶oÀM§^»uY·z*´ g¶oÀq¶~¶~*´ w¶oÀq¶~¶M§-*´ Y¶oÀM§¯M§*´ _¶oÀM§
±M§*´ ¶oÀqM§ õ*´ ¶oÀM§ ç»uY*´ ¶oÀq¸·z¶~¶M§ Æ*´ m¶oÀqM§ ¸M§ ±¡M§ ª£M§ £¥M§ §M§ *´ O¶oÀqM§ *´ ¶oÀqM§ y»uY©·z*´ u¶oÀq¶~¶M§ [«M§ T*´ ¶oÀqM§ F*´ ¶oÀqM§ 8*´ ¶oÀqM§ **´ ¶oÀqM§ *´ ¶oÀqM§ *´ ¶oÀqM,°    Ê   ¢  j lPpTqWu[v^zb{eilux¥¨±´¢½£À§É¨Ì¬Ô­×±â²å¶ð·ó»¼À2Á5Å@ÆCÊNËQÏ\Ð_ÔjÕmÙÚÞßã ä£è®é±íßîâòíóð÷ôø÷üý	HKORVY]`d g$r%u)*./¡3¥4¨8³9¶=Á>ÄBÏCÒGÝHàLëMîQùRüVW
[\`#a&e1f4j?kBo`pctnuqy|z~­°»¾ìïúý¡$¢'¦2§5«S¬V°a±dµh¶kºo»r¿vÀyÄ}ÅÉÊÎÏÓ Ô£Ø¾ÙÁÝÅÞÈâÓãÖçáèäìïíòñýò ö÷û ·\ ]    _ I  ·    Mª         O  M  T  [  b  i  u        ¥  ±  ½  É  Ô  â  ð    2  @  N  \  j         ®  ß  í  ô    	    H  O  V  ]  d  r      ¥  ³  Á  Ï  Ý  ë  ù      #  1  ?  `  n  |  ­  »  ì  ú        $  2  S  a  h  o  v  }         ¾  Å  Ó  á  ï  ý  aM§ÅaM§¾aM§·cM§°»eY·hM§¤»eY·hM§»eY·hM§»eY·hM§»eY·hM§t»eY·hM§h»eY·hM§\»eY·hM§P»jY·kM§E*´ ¶oÀqM§7*´ ¶oÀsM§)»uYw·z*´ U¶oÀq¶~¶M§»uY·z*´ Á¶ºÀe¶¶~¶M§ç*´ Á¶ºÀeM§Ù*´ ¡¶oÀqM§Ë*´ ¶oÀqM§½*´ «¶ÀM§¯»uY*´ ¶oÀq¸·z¶~¶M§*´ ±¶ÀM§M§y*´ ±¶ÀM§k»uY·z*´ »¶Àq¶~¶~*´ ¯¶Àq¶~¶M§:*´ ½¶ÀqM§,M§%*´ ©¶ÀM§M§*´ ©¶ÀM§»uY·z*´ ¿¶Àq¶~¶~*´ §¶Àq¶~¶M§Ñ¡M§Ê£M§Ã¥M§¼§M§µ*´ ¥¶ÀqM§§*´ µ¶ÀqM§»uY©·z*´ ¹¶Àq¶~¶M§{«M§t*´ ­¶ÀqM§f*´ ¶oÀqM§X*´ ¶oÀqM§J*´ ¶oÀqM§<*´ ¶oÀqM§.*´ ¶oÀqM§ *´ £¶ÀM§*´ ¶oÀqM§*´ ¶oÀM§ö*´ ¶oÀqM§è*´ ·¶ÀM§Ú»uY*´ ¶oÀq¸·z­¶~¶M§¹*´ a¶oÀM§«*´ _¶oÀM§»uY·z*´ ]¶oÀq¶~¶~*´ M¶oÀq¶~¶M§l*´ Y¶oÀM§^»uY·z*´ g¶oÀq¶~¶~*´ w¶oÀq¶~¶M§-*´ Y¶oÀM§¯M§*´ _¶oÀM§
±M§*´ ¶oÀqM§ õ*´ ¶oÀM§ ç»uY*´ ¶oÀq¸·z¶~¶M§ Æ*´ m¶oÀqM§ ¸M§ ±¡M§ ª£M§ £¥M§ §M§ *´ O¶oÀqM§ *´ ¶oÀqM§ y»uY©·z*´ u¶oÀq¶~¶M§ [«M§ T*´ ¶oÀqM§ F*´ ¶oÀqM§ 8*´ ¶oÀqM§ **´ ¶oÀqM§ *´ ¶oÀqM§ *´ ¶oÀqM,°    Ê   ¢   PTW[^be!i"l&u'x+,0156:¥;¨?±@´D½EÀIÉJÌNÔO×SâTåXðYó]^b2c5g@hClNmQq\r_vjwm{| £®±ßâíðô÷£	¤¨©­H®K²O³R·V¸Y¼]½`ÁdÂgÆrÇuËÌÐÑ¡Õ¥Ö¨Ú³Û¶ßÁàÄäÏåÒéÝêàîëïîóùôüøù
ýþ#&14?
B`cnq| ­!°%»&¾*ì+ï/ú0ý459:>?C$D'H2I5MSNVRaSdWhXk\o]ravbyf}gklpqu v£z¾{ÁÅÈÓÖáäïòý ¥ »    t _1576530200235_683987t 2net.sf.jasperreports.engine.design.JRJavacCompiler
<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="GestaoRecebiveis" pageWidth="680" pageHeight="878" whenNoDataType="AllSectionsNoDetail" columnWidth="625" leftMargin="20" rightMargin="35" topMargin="2" bottomMargin="2" whenResourceMissingType="Empty">
	<property name="ireport.zoom" value="1.8150000000000117"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="118"/>
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.encoding" value="ISO-8859-1"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="java.util.*"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<parameter name="tituloRelatorio" class="java.lang.String" isForPrompting="false"/>
	<parameter name="nomeEmpresa" class="java.lang.String" isForPrompting="false"/>
	<parameter name="versaoSoftware" class="java.lang.String" isForPrompting="false"/>
	<parameter name="usuario" class="java.lang.String" isForPrompting="false"/>
	<parameter name="filtros" class="java.lang.String" isForPrompting="false"/>
	<parameter name="SUBREPORT_DIR" class="java.lang.String">
		<defaultValueExpression><![CDATA["E:\\desenvolvimentosv\\Projeto Pacto Solucoes\\ZillyonWeb\\src\\java\\relatorio\\designRelatorio\\financeiro\\"]]></defaultValueExpression>
	</parameter>
	<parameter name="SUBREPORT_DIR1" class="java.lang.String">
		<defaultValueExpression><![CDATA["E:\\desenvolvimentosv\\Projeto Pacto Solucoes\\ZillyonWeb\\src\\java\\relatorio\\designRelatorio\\financeiro\\"]]></defaultValueExpression>
	</parameter>
	<parameter name="logoPadraoRelatorio" class="java.io.InputStream" isForPrompting="false"/>
	<parameter name="SUBREPORT_DIR2" class="java.lang.String">
		<defaultValueExpression><![CDATA["E:\\desenvolvimentosv\\Projeto Pacto Solucoes\\ZillyonWeb\\src\\java\\relatorio\\designRelatorio\\financeiro\\"]]></defaultValueExpression>
	</parameter>
	<parameter name="dataFim" class="java.lang.String"/>
	<parameter name="dataIni" class="java.lang.String"/>
	<parameter name="inicioFaturamento" class="java.lang.String"/>
	<parameter name="fimFaturamento" class="java.lang.String"/>
	<parameter name="inicioCompensacao" class="java.lang.String"/>
	<parameter name="fimCompensacao" class="java.lang.String"/>
	<parameter name="formas" class="java.lang.Object"/>
	<parameter name="diario" class="java.lang.Boolean"/>
	<parameter name="faturamento" class="java.lang.Boolean"/>
	<parameter name="compensacao" class="java.lang.Boolean"/>
	<parameter name="valorTotalGR" class="java.lang.String"/>
	<parameter name="valorBoletoGR" class="java.lang.String"/>
	<parameter name="valorDevolucoesGR" class="java.lang.String"/>
	<parameter name="valorEspecieGR" class="java.lang.String"/>
	<parameter name="valorContaCorrenteGR" class="java.lang.String"/>
	<parameter name="considerarCompensacaoOriginal" class="java.lang.Boolean"/>
	<parameter name="moeda" class="java.lang.String">
		<defaultValueExpression><![CDATA["R$"]]></defaultValueExpression>
	</parameter>
	<field name="formas" class="java.lang.Object"/>
	<field name="dataInicioFaturamentoApresentar" class="java.lang.String"/>
	<field name="dataFimFaturamentoApresentar" class="java.lang.String"/>
	<field name="dataFimCompensacaoApresentar" class="java.lang.String"/>
	<field name="dataInicioCompensacaoApresentar" class="java.lang.String"/>
	<field name="valorApresentar" class="java.lang.String"/>
	<field name="faturamento" class="java.lang.Boolean"/>
	<field name="compensacao" class="java.lang.Boolean"/>
	<field name="detalhar" class="java.lang.Boolean"/>
	<field name="formas2" class="java.lang.Object"/>
	<field name="formasOutros" class="java.lang.Object"/>
	<field name="valorEspecieApresentar" class="java.lang.String"/>
	<field name="valorBoletoApresentar" class="java.lang.String"/>
	<field name="valorContaCorrenteApresentar" class="java.lang.String"/>
	<field name="valorDevolucoesApresentar" class="java.lang.String"/>
	<pageHeader>
		<band height="57" splitType="Stretch">
			<textField pattern="dd/MM/yyyy HH.mm.ss" isBlankWhenNull="false">
				<reportElement key="dataRel-1" mode="Transparent" x="520" y="1" width="105" height="12" backcolor="#FFFFFF"/>
				<box bottomPadding="0">
					<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Verdana" size="8" isItalic="false" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression class="java.util.Date"><![CDATA[new Date()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-2" x="83" y="1" width="437" height="36"/>
				<box>
					<pen lineWidth="0.5" lineStyle="Solid"/>
					<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="14" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{tituloRelatorio}]]></textFieldExpression>
			</textField>
			<image isUsingCache="true" onErrorType="Blank" evaluationTime="Page">
				<reportElement key="image-1" positionType="Float" x="0" y="1" width="82" height="36" isPrintWhenDetailOverflows="true"/>
				<box>
					<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<imageExpression class="java.io.InputStream"><![CDATA[$P{logoPadraoRelatorio}]]></imageExpression>
			</image>
			<textField pattern="">
				<reportElement mode="Transparent" x="520" y="13" width="105" height="12"/>
				<box>
					<leftPen lineWidth="0.5" lineStyle="Dotted"/>
					<bottomPen lineWidth="0.5" lineStyle="Dotted"/>
					<rightPen lineWidth="0.5" lineStyle="Dotted"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Verdana" size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA["Usuário:"+ $P{usuario}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="520" y="25" width="70" height="12"/>
				<box>
					<leftPen lineWidth="0.5" lineStyle="Dotted"/>
					<bottomPen lineWidth="0.5" lineStyle="Dotted"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Verdana" size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA["Página "+$V{PAGE_NUMBER}+" de"]]></textFieldExpression>
			</textField>
			<textField evaluationTime="Report">
				<reportElement x="590" y="25" width="35" height="12"/>
				<box leftPadding="4">
					<bottomPen lineWidth="0.5" lineStyle="Dotted"/>
					<rightPen lineWidth="0.5" lineStyle="Dotted"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Verdana" size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.Integer"><![CDATA[$V{PAGE_NUMBER}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="false">
				<reportElement key="textField-216" stretchType="RelativeToBandHeight" x="0" y="37" width="625" height="20"/>
				<box>
					<leftPen lineWidth="0.5" lineStyle="Solid"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.5" lineStyle="Solid"/>
				</box>
				<textElement textAlignment="Center">
					<font fontName="Arial" size="10" isBold="true" isItalic="true" pdfFontName="Helvetica-BoldOblique"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{filtros}]]></textFieldExpression>
			</textField>
		</band>
	</pageHeader>
	<detail>
		<band height="130">
			<subreport>
				<reportElement x="14" y="28" width="286" height="20"/>
				<subreportParameter name="SUBREPORT_DIR"/>
				<subreportParameter name="dataIni"/>
				<subreportParameter name="dataFim"/>
				<subreportParameter name="SUBREPORT_DIR2"/>
				<subreportParameter name="tituloRelatorio"/>
				<subreportParameter name="usuario"/>
				<subreportParameter name="nomeEmpresa"/>
				<subreportParameter name="logoPadraoRelatorio"/>
				<subreportParameter name="SUBREPORT_DIR1"/>
				<subreportParameter name="moeda">
					<subreportParameterExpression><![CDATA[$P{moeda}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="versaoSoftware"/>
				<subreportParameter name="filtros"/>
				<dataSourceExpression><![CDATA[$F{formas}]]></dataSourceExpression>
				<subreportExpression class="java.lang.String"><![CDATA[$P{SUBREPORT_DIR} + "GestaoRecebiveisResumo.jasper"]]></subreportExpression>
			</subreport>
			<line>
				<reportElement key="" positionType="Float" x="0" y="129" width="625" height="1"/>
			</line>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement key="textField-7" x="0" y="3" width="133" height="12" isRemoveLineWhenBlank="true">
					<printWhenExpression><![CDATA[$F{faturamento}]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="9" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA["Consulta por faturamento :"]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement key="textField-7" x="133" y="3" width="331" height="12" isRemoveLineWhenBlank="true">
					<printWhenExpression><![CDATA[$F{faturamento}]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="9" isBold="true" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA["De "+ $F{dataInicioFaturamentoApresentar} +" até "+ $F{dataFimFaturamentoApresentar}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="" positionType="Float" x="200" y="97" width="100" height="12" isRemoveLineWhenBlank="true"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{valorApresentar}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="" positionType="Float" x="3" y="97" width="154" height="12" isRemoveLineWhenBlank="true"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA["Total:"]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement key="textField-7" x="0" y="15" width="133" height="12" isRemoveLineWhenBlank="true">
					<printWhenExpression><![CDATA[$F{compensacao}]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="9" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA["Consulta por Compensação :"]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement key="textField-7" x="133" y="15" width="364" height="12" isRemoveLineWhenBlank="true">
					<printWhenExpression><![CDATA[$F{compensacao}]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="9" isBold="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA["De "+ $F{dataInicioCompensacaoApresentar} +" até "+$F{dataFimCompensacaoApresentar}]]></textFieldExpression>
			</textField>
			<line>
				<reportElement positionType="Float" x="212" y="96" width="86" height="1"/>
			</line>
			<textField isBlankWhenNull="true">
				<reportElement key="" positionType="Float" x="2" y="48" width="154" height="12" isRemoveLineWhenBlank="true"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA["Totais:"]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="" positionType="Float" x="14" y="60" width="142" height="12" isRemoveLineWhenBlank="true"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA["Espécie:"]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="" positionType="Float" x="14" y="72" width="142" height="12" isRemoveLineWhenBlank="true"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA["Boleto:"]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="" positionType="Float" x="14" y="84" width="142" height="12" isRemoveLineWhenBlank="true"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA["Devoluções:"]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="" positionType="Float" x="200" y="60" width="100" height="12" isRemoveLineWhenBlank="true"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{valorEspecieApresentar}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="" positionType="Float" x="200" y="72" width="100" height="12" isRemoveLineWhenBlank="true"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{valorBoletoApresentar}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="" positionType="Float" x="200" y="84" width="100" height="12" isRemoveLineWhenBlank="true"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA["-"+$F{valorDevolucoesApresentar}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="" positionType="Float" x="15" y="109" width="142" height="12" isRemoveLineWhenBlank="true"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA["Conta Corrente Cliente:"]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="" positionType="Float" x="200" y="109" width="100" height="12" isRemoveLineWhenBlank="true"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{valorContaCorrenteApresentar}]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement mode="Transparent" x="155" y="60" width="42" height="12" forecolor="#000000" backcolor="#FFFFFF"/>
				<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
					<font fontName="Microsoft Sans Serif" size="8" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{moeda}]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement mode="Transparent" x="155" y="72" width="42" height="12" forecolor="#000000" backcolor="#FFFFFF"/>
				<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
					<font fontName="Microsoft Sans Serif" size="8" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{moeda}]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement mode="Transparent" x="155" y="84" width="42" height="12" forecolor="#000000" backcolor="#FFFFFF"/>
				<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
					<font fontName="Microsoft Sans Serif" size="8" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{moeda}]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement mode="Transparent" x="155" y="97" width="42" height="12" forecolor="#000000" backcolor="#FFFFFF"/>
				<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
					<font fontName="Microsoft Sans Serif" size="8" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{moeda}]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement mode="Transparent" x="156" y="109" width="42" height="12" forecolor="#000000" backcolor="#FFFFFF"/>
				<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
					<font fontName="Microsoft Sans Serif" size="8" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{moeda}]]></textFieldExpression>
			</textField>
		</band>
		<band height="69">
			<printWhenExpression><![CDATA[$F{detalhar}]]></printWhenExpression>
			<subreport isUsingCache="false">
				<reportElement positionType="Float" stretchType="RelativeToBandHeight" isPrintRepeatedValues="false" x="14" y="7" width="598" height="43" isRemoveLineWhenBlank="true" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true"/>
				<subreportParameter name="SUBREPORT_DIR">
					<subreportParameterExpression><![CDATA[$P{SUBREPORT_DIR}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="dataIni"/>
				<subreportParameter name="dataFim"/>
				<subreportParameter name="SUBREPORT_DIR2"/>
				<subreportParameter name="considerarCompensacaoOriginal">
					<subreportParameterExpression><![CDATA[$P{considerarCompensacaoOriginal}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="tituloRelatorio"/>
				<subreportParameter name="usuario"/>
				<subreportParameter name="nomeEmpresa"/>
				<subreportParameter name="logoPadraoRelatorio"/>
				<subreportParameter name="SUBREPORT_DIR1"/>
				<subreportParameter name="moeda">
					<subreportParameterExpression><![CDATA[$P{moeda}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="filtros"/>
				<subreportParameter name="versaoSoftware"/>
				<dataSourceExpression><![CDATA[$F{formas2}]]></dataSourceExpression>
				<subreportExpression class="java.lang.String"><![CDATA[$P{SUBREPORT_DIR} + "ListasGestaoRecebiveis.jasper"]]></subreportExpression>
			</subreport>
		</band>
	</detail>
	<summary>
		<band height="154">
			<printWhenExpression><![CDATA[$P{diario}]]></printWhenExpression>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement key="textField-7" x="155" y="26" width="364" height="12" isRemoveLineWhenBlank="true">
					<printWhenExpression><![CDATA[$P{faturamento}]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="9" isBold="true" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA["De "+ $P{inicioFaturamento} +" até "+ $P{fimFaturamento}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement key="textField-7" x="155" y="38" width="434" height="12" isRemoveLineWhenBlank="true">
					<printWhenExpression><![CDATA[$P{compensacao}]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="9" isBold="true" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA["De "+ $P{inicioCompensacao} +" até "+$P{fimCompensacao}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement key="textField-7" x="0" y="38" width="155" height="12" isRemoveLineWhenBlank="true">
					<printWhenExpression><![CDATA[$P{compensacao}]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="9" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA["Período total por Compensação :"]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="0" y="9" width="100" height="14"/>
				<textElement>
					<font isBold="true" isStrikeThrough="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Totais:]]></text>
			</staticText>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement key="textField-7" x="0" y="26" width="155" height="12" isRemoveLineWhenBlank="true">
					<printWhenExpression><![CDATA[$P{faturamento}]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="9" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA["Período total por faturamento :"]]></textFieldExpression>
			</textField>
			<subreport>
				<reportElement x="14" y="50" width="286" height="20"/>
				<subreportParameter name="SUBREPORT_DIR"/>
				<subreportParameter name="dataIni"/>
				<subreportParameter name="dataFim"/>
				<subreportParameter name="SUBREPORT_DIR2"/>
				<subreportParameter name="tituloRelatorio"/>
				<subreportParameter name="usuario"/>
				<subreportParameter name="nomeEmpresa"/>
				<subreportParameter name="logoPadraoRelatorio"/>
				<subreportParameter name="SUBREPORT_DIR1"/>
				<subreportParameter name="moeda">
					<subreportParameterExpression><![CDATA[$P{moeda}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="versaoSoftware"/>
				<subreportParameter name="filtros"/>
				<dataSourceExpression><![CDATA[$P{formas}]]></dataSourceExpression>
				<subreportExpression class="java.lang.String"><![CDATA[$P{SUBREPORT_DIR} + "GestaoRecebiveisResumo.jasper"]]></subreportExpression>
			</subreport>
			<textField isBlankWhenNull="true">
				<reportElement key="" positionType="Float" x="200" y="121" width="100" height="12" isRemoveLineWhenBlank="true"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{valorTotalGR}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="" positionType="Float" x="3" y="121" width="154" height="12" isRemoveLineWhenBlank="true"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA["Total:"]]></textFieldExpression>
			</textField>
			<line>
				<reportElement positionType="Float" x="214" y="119" width="86" height="1"/>
			</line>
			<textField isBlankWhenNull="true">
				<reportElement key="" positionType="Float" x="3" y="72" width="154" height="12" isRemoveLineWhenBlank="true"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA["Totais:"]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="" positionType="Float" x="14" y="84" width="142" height="12" isRemoveLineWhenBlank="true"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA["Espécie:"]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="" positionType="Float" x="14" y="96" width="142" height="12" isRemoveLineWhenBlank="true"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA["Boleto:"]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="" positionType="Float" x="14" y="108" width="142" height="12" isRemoveLineWhenBlank="true"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA["Devoluções:"]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="" positionType="Float" x="200" y="84" width="100" height="12" isRemoveLineWhenBlank="true"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{valorEspecieGR}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="" positionType="Float" x="200" y="96" width="100" height="12" isRemoveLineWhenBlank="true"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{valorBoletoGR}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="" positionType="Float" x="200" y="109" width="98" height="12" isRemoveLineWhenBlank="true"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA["-"+$P{valorDevolucoesGR}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="" positionType="Float" x="14" y="133" width="142" height="12" isRemoveLineWhenBlank="true"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA["Conta Corrente Cliente:"]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="" positionType="Float" x="200" y="133" width="100" height="12" isRemoveLineWhenBlank="true"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{valorContaCorrenteGR}]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement mode="Transparent" x="158" y="84" width="42" height="12" forecolor="#000000" backcolor="#FFFFFF"/>
				<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
					<font fontName="Microsoft Sans Serif" size="8" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{moeda}]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement mode="Transparent" x="157" y="96" width="42" height="12" forecolor="#000000" backcolor="#FFFFFF"/>
				<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
					<font fontName="Microsoft Sans Serif" size="8" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{moeda}]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement mode="Transparent" x="158" y="107" width="39" height="12" forecolor="#000000" backcolor="#FFFFFF"/>
				<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
					<font fontName="Microsoft Sans Serif" size="8" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{moeda}]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement mode="Transparent" x="158" y="119" width="42" height="12" forecolor="#000000" backcolor="#FFFFFF"/>
				<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
					<font fontName="Microsoft Sans Serif" size="8" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{moeda}]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement mode="Transparent" x="155" y="133" width="42" height="12" forecolor="#000000" backcolor="#FFFFFF"/>
				<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
					<font fontName="Microsoft Sans Serif" size="8" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{moeda}]]></textFieldExpression>
			</textField>
		</band>
	</summary>
</jasperReport>

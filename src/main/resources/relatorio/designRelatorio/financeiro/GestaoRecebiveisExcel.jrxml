<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="GestaoRecebiveisExcel" pageWidth="680" pageHeight="878" columnWidth="640" leftMargin="20" rightMargin="20" topMargin="2" bottomMargin="2" whenResourceMissingType="Empty">
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.encoding" value="ISO-8859-1"/>
	<property name="ireport.zoom" value="1.9965000000000006"/>
	<property name="ireport.x" value="211"/>
	<property name="ireport.y" value="0"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="java.util.*"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<parameter name="tipoRelatorioDF" class="java.lang.Integer"/>
	<parameter name="moeda" class="java.lang.String">
		<defaultValueExpression><![CDATA["R$"]]></defaultValueExpression>
	</parameter>
	<queryString>
		<![CDATA[]]>
	</queryString>
	<field name="formaPagamento" class="java.lang.String"/>
	<field name="ano" class="java.lang.String"/>
	<field name="mes" class="java.lang.String"/>
	<field name="diaMes" class="java.lang.String"/>
	<field name="valorExcel" class="java.lang.String"/>
	<variable name="tipoDescricao" class="java.lang.String">
		<variableExpression><![CDATA[]]></variableExpression>
	</variable>
	<title>
		<band height="23">
			<staticText>
				<reportElement key="staticText-2" x="2" y="2" width="173" height="20"/>
				<textElement>
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Forma de Pagamento]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-1" stretchType="RelativeToTallestObject" x="175" y="2" width="80" height="20"/>
				<textElement>
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Ano]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-8" x="255" y="2" width="69" height="20"/>
				<textElement>
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Mês]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-8" x="420" y="2" width="149" height="20"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Valor(R$)]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-8" x="324" y="2" width="96" height="20"/>
				<textElement verticalAlignment="Middle">
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Dia]]></text>
			</staticText>
		</band>
	</title>
	<detail>
		<band height="25">
			<textField isBlankWhenNull="true">
				<reportElement key="textField-223" x="175" y="3" width="80" height="20"/>
				<textElement verticalAlignment="Middle"/>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{ano}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-224" x="2" y="3" width="173" height="20"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font size="10" isBold="false"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{formaPagamento}]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement key="textField" positionType="Float" x="503" y="3" width="66" height="20"/>
				<textElement textAlignment="Right" verticalAlignment="Middle"/>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{valorExcel}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-223" x="255" y="3" width="69" height="20"/>
				<textElement verticalAlignment="Middle"/>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{mes}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-223" x="324" y="3" width="96" height="20"/>
				<textElement verticalAlignment="Middle"/>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{diaMes}]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement mode="Transparent" x="420" y="3" width="70" height="20" forecolor="#000000" backcolor="#FFFFFF"/>
				<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
					<font fontName="SansSerif" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{moeda}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
</jasperReport>

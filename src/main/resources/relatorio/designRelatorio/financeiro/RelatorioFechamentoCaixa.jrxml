<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="RelatorioFechamentoCaixa" pageWidth="680" pageHeight="878" whenNoDataType="AllSectionsNoDetail" columnWidth="625" leftMargin="20" rightMargin="35" topMargin="2" bottomMargin="2" whenResourceMissingType="Empty">
	<property name="ireport.zoom" value="2.1961500000000305"/>
	<property name="ireport.x" value="368"/>
	<property name="ireport.y" value="0"/>
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.encoding" value="ISO-8859-1"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="java.util.*"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<parameter name="tituloRelatorio" class="java.lang.String" isForPrompting="false"/>
	<parameter name="nomeEmpresa" class="java.lang.String" isForPrompting="false"/>
	<parameter name="versaoSoftware" class="java.lang.String" isForPrompting="false"/>
	<parameter name="usuario" class="java.lang.String" isForPrompting="false"/>
	<parameter name="filtros" class="java.lang.String" isForPrompting="false"/>
	<parameter name="SUBREPORT_DIR" class="java.lang.String">
		<defaultValueExpression><![CDATA["E:\\desenvolvimentosv\\Projeto Pacto Solucoes\\ZillyonWeb\\src\\java\\relatorio\\designRelatorio\\financeiro\\"]]></defaultValueExpression>
	</parameter>
	<parameter name="SUBREPORT_DIR1" class="java.lang.String">
		<defaultValueExpression><![CDATA["E:\\desenvolvimentosv\\Projeto Pacto Solucoes\\ZillyonWeb\\src\\java\\relatorio\\designRelatorio\\financeiro\\"]]></defaultValueExpression>
	</parameter>
	<parameter name="logoPadraoRelatorio" class="java.io.InputStream" isForPrompting="false"/>
	<parameter name="SUBREPORT_DIR2" class="java.lang.String">
		<defaultValueExpression><![CDATA["E:\\desenvolvimentosv\\Projeto Pacto Solucoes\\ZillyonWeb\\src\\java\\relatorio\\designRelatorio\\financeiro\\"]]></defaultValueExpression>
	</parameter>
	<parameter name="dataFim" class="java.lang.String"/>
	<parameter name="dataIni" class="java.lang.String"/>
	<parameter name="dataAberturaApresentar" class="java.lang.String"/>
	<parameter name="dataFechamentoApresentar" class="java.lang.String"/>
	<parameter name="totalRecebiveis" class="java.lang.String"/>
	<parameter name="totalRecebidos" class="java.lang.String"/>
	<parameter name="inicioFat" class="java.lang.String"/>
	<parameter name="fimFat" class="java.lang.String"/>
	<parameter name="dataImpressao" class="java.lang.String"/>
	<parameter name="usuarioImpressao" class="java.lang.String"/>
	<parameter name="contasSelecionadas" class="java.lang.String"/>
	<parameter name="moeda" class="java.lang.String">
		<defaultValueExpression><![CDATA["R$"]]></defaultValueExpression>
	</parameter>
	<field name="formasJR" class="java.lang.Object"/>
	<field name="totaisRecebidosJR" class="java.lang.Object"/>
	<field name="contasJR" class="java.lang.Object"/>
	<field name="totaisFormaJR" class="java.lang.Object"/>
	<field name="contasJR2" class="java.lang.Object"/>
	<field name="totalContas.inicialApresentar" class="java.lang.String"/>
	<field name="totalContas.entradaApresentar" class="java.lang.String"/>
	<field name="totalContas.saldoAtualApresentar" class="java.lang.String"/>
	<field name="totalContas.saidaApresentar" class="java.lang.String"/>
	<field name="totalFP.entradaApresentar" class="java.lang.String"/>
	<field name="totalFP.saidaApresentar" class="java.lang.String"/>
	<field name="totalFP.saldoApresentar" class="java.lang.String"/>
	<field name="caixasJR" class="java.lang.Object"/>
	<pageHeader>
		<band height="73" splitType="Stretch">
			<rectangle>
				<reportElement x="0" y="36" width="625" height="35" backcolor="#F0EFEF"/>
				<graphicElement>
					<pen lineWidth="0.0"/>
				</graphicElement>
			</rectangle>
			<staticText>
				<reportElement x="2" y="16" width="390" height="20"/>
				<textElement>
					<font fontName="Arial" size="12" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Relatório Movimentações Financeiras - Fechamento Diário]]></text>
			</staticText>
			<staticText>
				<reportElement x="4" y="38" width="33" height="13"/>
				<textElement>
					<font fontName="Arial" isBold="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Início:]]></text>
			</staticText>
			<staticText>
				<reportElement x="4" y="53" width="33" height="13"/>
				<textElement>
					<font fontName="Arial" isBold="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Fim:]]></text>
			</staticText>
			<image isUsingCache="true" onErrorType="Blank" evaluationTime="Page">
				<reportElement key="image-1" positionType="Float" x="512" y="0" width="113" height="36" isPrintWhenDetailOverflows="true"/>
				<box>
					<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<imageExpression class="java.io.InputStream"><![CDATA[$P{logoPadraoRelatorio}]]></imageExpression>
			</image>
			<textField>
				<reportElement x="37" y="38" width="248" height="13"/>
				<textElement>
					<font fontName="Arial" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{dataAberturaApresentar}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="37" y="53" width="248" height="13"/>
				<textElement>
					<font fontName="Arial" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{dataFechamentoApresentar}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement x="327" y="38" width="296" height="31"/>
				<textElement>
					<font fontName="Arial" size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{contasSelecionadas}]]></textFieldExpression>
			</textField>
		</band>
	</pageHeader>
	<detail>
		<band height="63">
			<subreport>
				<reportElement x="0" y="41" width="298" height="20"/>
				<subreportParameter name="tituloRelatorio"/>
				<subreportParameter name="nomeEmpresa"/>
				<subreportParameter name="versaoSoftware"/>
				<subreportParameter name="usuario"/>
				<subreportParameter name="filtros"/>
				<subreportParameter name="SUBREPORT_DIR"/>
				<subreportParameter name="SUBREPORT_DIR1"/>
				<subreportParameter name="logoPadraoRelatorio"/>
				<subreportParameter name="SUBREPORT_DIR2"/>
				<subreportParameter name="dataFim"/>
				<subreportParameter name="dataIni"/>
				<dataSourceExpression><![CDATA[$F{formasJR}]]></dataSourceExpression>
				<subreportExpression class="java.lang.String"><![CDATA[$P{SUBREPORT_DIR} + "GestaoRecebiveisResumo_MovimentacaoContas.jasper"]]></subreportExpression>
			</subreport>
			<staticText>
				<reportElement x="2" y="5" width="184" height="15"/>
				<textElement>
					<font fontName="Arial" size="12" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Totais de Gestão de Recebíveis]]></text>
			</staticText>
			<staticText>
				<reportElement x="327" y="5" width="189" height="15"/>
				<textElement>
					<font fontName="Arial" size="12" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Recebido do Caixa ADM]]></text>
			</staticText>
			<subreport>
				<reportElement x="327" y="41" width="298" height="20"/>
				<subreportParameter name="tituloRelatorio"/>
				<subreportParameter name="nomeEmpresa"/>
				<subreportParameter name="versaoSoftware"/>
				<subreportParameter name="usuario"/>
				<subreportParameter name="filtros"/>
				<subreportParameter name="SUBREPORT_DIR"/>
				<subreportParameter name="SUBREPORT_DIR1"/>
				<subreportParameter name="logoPadraoRelatorio"/>
				<subreportParameter name="SUBREPORT_DIR2"/>
				<subreportParameter name="dataFim"/>
				<subreportParameter name="dataIni"/>
				<dataSourceExpression><![CDATA[$F{totaisRecebidosJR}]]></dataSourceExpression>
				<subreportExpression class="java.lang.String"><![CDATA[$P{SUBREPORT_DIR} + "RecebidosCaixa.jasper"]]></subreportExpression>
			</subreport>
			<rectangle>
				<reportElement x="0" y="21" width="298" height="20" backcolor="#999999"/>
				<graphicElement>
					<pen lineWidth="0.0"/>
				</graphicElement>
			</rectangle>
			<staticText>
				<reportElement x="215" y="21" width="34" height="20" forecolor="#FFFFFF"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Saldo ]]></text>
			</staticText>
			<rectangle>
				<reportElement x="327" y="21" width="298" height="20" backcolor="#999999"/>
				<graphicElement>
					<pen lineWidth="0.0"/>
				</graphicElement>
			</rectangle>
			<staticText>
				<reportElement x="516" y="21" width="53" height="20" forecolor="#FFFFFF"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Saldo]]></text>
			</staticText>
			<staticText>
				<reportElement x="330" y="21" width="62" height="20" forecolor="#FFFFFF"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Lançamento]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement key="" positionType="Float" x="4" y="21" width="211" height="20" isRemoveLineWhenBlank="true" forecolor="#FFFFFF"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA["Faturamento de "+$P{inicioFat}+" até "+$P{fimFat}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement mode="Transparent" x="249" y="21" width="29" height="20" forecolor="#FFFFFF" backcolor="#FFFFFF"/>
				<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
					<font fontName="Arial" size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica-Bold" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA["(" + $P{moeda} + ")"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement mode="Transparent" x="569" y="21" width="30" height="20" forecolor="#FFFFFF" backcolor="#FFFFFF"/>
				<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
					<font fontName="Arial" size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica-Bold" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA["(" + $P{moeda} + ")"]]></textFieldExpression>
			</textField>
		</band>
		<band height="25">
			<textField isBlankWhenNull="true">
				<reportElement key="" positionType="Float" x="2" y="10" width="154" height="12" isRemoveLineWhenBlank="true"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA["Total:"]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="" positionType="Float" x="327" y="10" width="154" height="12" isRemoveLineWhenBlank="true"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA["Total:"]]></textFieldExpression>
			</textField>
			<line>
				<reportElement positionType="Float" x="0" y="6" width="298" height="1" forecolor="#CCCCCC"/>
			</line>
			<line>
				<reportElement positionType="Float" x="327" y="6" width="298" height="1" forecolor="#CCCCCC"/>
			</line>
			<textField isBlankWhenNull="true">
				<reportElement key="" positionType="Float" x="156" y="10" width="142" height="12" isRemoveLineWhenBlank="true"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{totalRecebiveis}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="" positionType="Float" x="481" y="10" width="144" height="12" isRemoveLineWhenBlank="true"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{totalRecebidos}]]></textFieldExpression>
			</textField>
		</band>
		<band height="68">
			<staticText>
				<reportElement x="0" y="0" width="351" height="20"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="12" isBold="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Saldo das Contas]]></text>
			</staticText>
			<subreport>
				<reportElement x="0" y="20" width="623" height="37"/>
				<subreportParameter name="dataIni"/>
				<subreportParameter name="usuario"/>
				<subreportParameter name="logoPadraoRelatorio"/>
				<subreportParameter name="exibirAutorizacao"/>
				<subreportParameter name="SUBREPORT_DIR1"/>
				<subreportParameter name="SUBREPORT_DIR"/>
				<subreportParameter name="dataFim"/>
				<subreportParameter name="SUBREPORT_DIR2"/>
				<subreportParameter name="totalFinal">
					<subreportParameterExpression><![CDATA[$F{totalContas.saldoAtualApresentar}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="tituloRelatorio"/>
				<subreportParameter name="nomeEmpresa"/>
				<subreportParameter name="totalSaida">
					<subreportParameterExpression><![CDATA[$F{totalContas.saidaApresentar}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="totalEntrada">
					<subreportParameterExpression><![CDATA[$F{totalContas.entradaApresentar}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="totalInicial">
					<subreportParameterExpression><![CDATA[$F{totalContas.inicialApresentar}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="versaoSoftware"/>
				<subreportParameter name="filtros"/>
				<dataSourceExpression><![CDATA[$F{contasJR}]]></dataSourceExpression>
				<subreportExpression class="java.lang.String"><![CDATA[$P{SUBREPORT_DIR} + "SaldosContas.jasper"]]></subreportExpression>
			</subreport>
		</band>
		<band height="59">
			<staticText>
				<reportElement x="0" y="1" width="351" height="20"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="12" isBold="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Totais por Forma de Pagamento]]></text>
			</staticText>
			<subreport>
				<reportElement x="0" y="21" width="623" height="37"/>
				<subreportParameter name="dataIni"/>
				<subreportParameter name="usuario"/>
				<subreportParameter name="logoPadraoRelatorio"/>
				<subreportParameter name="exibirAutorizacao"/>
				<subreportParameter name="SUBREPORT_DIR1"/>
				<subreportParameter name="SUBREPORT_DIR"/>
				<subreportParameter name="dataFim"/>
				<subreportParameter name="SUBREPORT_DIR2"/>
				<subreportParameter name="totalFinal">
					<subreportParameterExpression><![CDATA[$F{totalFP.saldoApresentar}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="tituloRelatorio"/>
				<subreportParameter name="nomeEmpresa"/>
				<subreportParameter name="totalSaida">
					<subreportParameterExpression><![CDATA[$F{totalFP.saidaApresentar}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="totalEntrada">
					<subreportParameterExpression><![CDATA[$F{totalFP.entradaApresentar}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="versaoSoftware"/>
				<subreportParameter name="filtros"/>
				<dataSourceExpression><![CDATA[$F{totaisFormaJR}]]></dataSourceExpression>
				<subreportExpression class="java.lang.String"><![CDATA[$P{SUBREPORT_DIR} + "TotaisFormaPagamento.jasper"]]></subreportExpression>
			</subreport>
		</band>
		<band height="64">
			<subreport>
				<reportElement x="0" y="7" width="623" height="50"/>
				<subreportParameter name="tituloRelatorio"/>
				<subreportParameter name="nomeEmpresa"/>
				<subreportParameter name="versaoSoftware"/>
				<subreportParameter name="usuario"/>
				<subreportParameter name="filtros"/>
				<subreportParameter name="SUBREPORT_DIR">
					<subreportParameterExpression><![CDATA[$P{SUBREPORT_DIR}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="SUBREPORT_DIR1"/>
				<subreportParameter name="logoPadraoRelatorio"/>
				<subreportParameter name="SUBREPORT_DIR2"/>
				<subreportParameter name="dataFim"/>
				<subreportParameter name="dataIni"/>
				<subreportParameter name="exibirAutorizacao"/>
				<dataSourceExpression><![CDATA[$F{contasJR2}]]></dataSourceExpression>
				<subreportExpression class="java.lang.String"><![CDATA[$P{SUBREPORT_DIR} + "ContasDetalhado.jasper"]]></subreportExpression>
			</subreport>
			<break>
				<reportElement x="0" y="4" width="625" height="1"/>
			</break>
		</band>
		<band height="97">
			<subreport>
				<reportElement x="0" y="2" width="623" height="50"/>
				<subreportParameter name="tituloRelatorio"/>
				<subreportParameter name="nomeEmpresa"/>
				<subreportParameter name="versaoSoftware"/>
				<subreportParameter name="usuario"/>
				<subreportParameter name="filtros"/>
				<subreportParameter name="SUBREPORT_DIR">
					<subreportParameterExpression><![CDATA[$P{SUBREPORT_DIR}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="SUBREPORT_DIR1"/>
				<subreportParameter name="logoPadraoRelatorio"/>
				<subreportParameter name="SUBREPORT_DIR2"/>
				<subreportParameter name="dataFim"/>
				<subreportParameter name="dataIni"/>
				<subreportParameter name="exibirAutorizacao"/>
				<dataSourceExpression><![CDATA[$F{caixasJR}]]></dataSourceExpression>
				<subreportExpression class="java.lang.String"><![CDATA[$P{SUBREPORT_DIR} + "CaixasDetalhado.jasper"]]></subreportExpression>
			</subreport>
		</band>
	</detail>
	<columnFooter>
		<band height="37">
			<printWhenExpression><![CDATA[$V{PAGE_NUMBER} == 1]]></printWhenExpression>
			<line>
				<reportElement positionType="Float" x="327" y="11" width="298" height="1" forecolor="#000000"/>
			</line>
			<line>
				<reportElement positionType="Float" x="0" y="11" width="298" height="1" forecolor="#000000"/>
			</line>
			<staticText>
				<reportElement x="0" y="12" width="298" height="20"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" isBold="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Responsável pelo Caixa ]]></text>
			</staticText>
			<staticText>
				<reportElement x="327" y="11" width="298" height="20"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" isBold="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Gerente]]></text>
			</staticText>
		</band>
	</columnFooter>
	<pageFooter>
		<band height="20">
			<textField isBlankWhenNull="true">
				<reportElement key="" positionType="Float" x="50" y="0" width="518" height="12" isRemoveLineWhenBlank="true"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA["Impresso em: "+$P{dataImpressao}+" pelo usuário "+$P{usuarioImpressao}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="" positionType="Float" x="588" y="0" width="37" height="12" isRemoveLineWhenBlank="true"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA["Pág. "+$V{PAGE_NUMBER}]]></textFieldExpression>
			</textField>
		</band>
	</pageFooter>
</jasperReport>

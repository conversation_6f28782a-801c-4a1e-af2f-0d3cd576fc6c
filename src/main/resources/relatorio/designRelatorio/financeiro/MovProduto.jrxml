<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="MovProduto" pageWidth="615" pageHeight="842" orientation="Landscape" whenNoDataType="AllSectionsNoDetail" columnWidth="615" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0">
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.encoding" value="UTF-8"/>
	<property name="ireport.zoom" value="1.652892561983473"/>
	<property name="ireport.x" value="8"/>
	<property name="ireport.y" value="0"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="java.util.*"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<parameter name="mostrarModalidade" class="java.lang.Boolean"/>
	<parameter name="detalharPeriodoProduto" class="java.lang.Boolean">
		<defaultValueExpression><![CDATA[true]]></defaultValueExpression>
	</parameter>
	<field name="descricao" class="java.lang.String"/>
	<field name="valorDesconto" class="java.lang.Double"/>
	<field name="precoUnitario" class="java.lang.Double"/>
	<field name="quantidade" class="java.lang.Integer"/>
	<field name="valorPagoMovProdutoParcela" class="java.lang.Double"/>
	<field name="contrato.codigo" class="java.lang.Integer"/>
	<field name="pessoa.primeiroNomeConcatenado" class="java.lang.String"/>
	<field name="produto.tipoProduto" class="java.lang.String"/>
	<field name="contrato.vigenciaDe_Apresentar" class="java.lang.String"/>
	<field name="contrato.vigenciaAteAjustada_Apresentar" class="java.lang.String"/>
	<field name="contrato.nomeModalidades" class="java.lang.String"/>
	<field name="contrato.dataAlteracaoManual_Apresentar" class="java.lang.String"/>
	<field name="contrato.responsavelDataBase.nome" class="java.lang.String"/>
	<field name="pacoteVO.titulo" class="java.lang.String"/>
	<field name="nomeAlunoPersonal" class="java.lang.String"/>
	<field name="dataInicioVigencia_Apresentar" class="java.lang.String"/>
	<field name="dataFinalVigencia_Apresentar" class="java.lang.String"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band splitType="Stretch"/>
	</title>
	<pageHeader>
		<band splitType="Stretch"/>
	</pageHeader>
	<columnHeader>
		<band splitType="Stretch"/>
	</columnHeader>
	<detail>
		<band height="14" splitType="Stretch">
			<textField isBlankWhenNull="false">
				<reportElement key="textField-7" stretchType="RelativeToTallestObject" x="38" y="0" width="107" height="14" isPrintWhenDetailOverflows="true"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="9"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[" "+$F{pessoa.primeiroNomeConcatenado}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00" isBlankWhenNull="false">
				<reportElement key="textField-2" stretchType="RelativeToTallestObject" x="485" y="0" width="32" height="14" isPrintWhenDetailOverflows="true">
					<printWhenExpression><![CDATA[!$F{produto.tipoProduto}.equals("PM")]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="9"/>
				</textElement>
				<textFieldExpression class="java.lang.Double"><![CDATA[$F{precoUnitario}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-3" stretchType="RelativeToTallestObject" x="516" y="0" width="18" height="14" isPrintWhenDetailOverflows="true">
					<printWhenExpression><![CDATA[!$F{produto.tipoProduto}.equals("PM")]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="9"/>
				</textElement>
				<textFieldExpression class="java.lang.Integer"><![CDATA[$F{quantidade}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00" isBlankWhenNull="false">
				<reportElement key="textField-4" stretchType="RelativeToTallestObject" x="533" y="0" width="39" height="14" isPrintWhenDetailOverflows="true">
					<printWhenExpression><![CDATA[!$F{produto.tipoProduto}.equals("PM")]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="9"/>
				</textElement>
				<textFieldExpression class="java.lang.Double"><![CDATA[$F{valorDesconto}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00" isBlankWhenNull="false">
				<reportElement key="textField-5" stretchType="RelativeToTallestObject" x="571" y="0" width="44" height="14" isPrintWhenDetailOverflows="true"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="9"/>
				</textElement>
				<textFieldExpression class="java.lang.Double"><![CDATA[$F{valorPagoMovProdutoParcela}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-6" stretchType="RelativeToTallestObject" x="0" y="0" width="38" height="14" isPrintWhenDetailOverflows="true"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="9"/>
				</textElement>
				<textFieldExpression class="java.lang.Integer"><![CDATA[$F{contrato.codigo}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="false">
				<reportElement key="textField-1" stretchType="RelativeToTallestObject" x="145" y="0" width="340" height="14" isPrintWhenDetailOverflows="true"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="9"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[" "+  $F{descricao}]]></textFieldExpression>
			</textField>
		</band>
		<band height="12">
			<printWhenExpression><![CDATA[(($F{contrato.codigo}.intValue() != 0) &&
    ($F{produto.tipoProduto}.equals("PM")))]]></printWhenExpression>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-1" x="349" y="0" width="60" height="12" isRemoveLineWhenBlank="true">
					<printWhenExpression><![CDATA[$P{detalharPeriodoProduto}]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{contrato.vigenciaDe_Apresentar}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="319" y="0" width="30" height="12" isRemoveLineWhenBlank="true">
					<printWhenExpression><![CDATA[$P{detalharPeriodoProduto}]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle" markup="none">
					<font size="8" isBold="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$R{Inicio}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="409" y="0" width="44" height="12" isRemoveLineWhenBlank="true">
					<printWhenExpression><![CDATA[$P{detalharPeriodoProduto}]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle" markup="none">
					<font size="8" isBold="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$R{Termino}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-1" x="453" y="0" width="51" height="12" isRemoveLineWhenBlank="true">
					<printWhenExpression><![CDATA[$P{detalharPeriodoProduto}]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{contrato.vigenciaAteAjustada_Apresentar}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="286" y="0" width="33" height="12" isRemoveLineWhenBlank="true">
					<printWhenExpression><![CDATA[$P{mostrarModalidade}.equals(false)]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font size="8" isBold="true" isItalic="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA["("+$F{contrato.nomeModalidades}+")"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="8" y="0" width="60" height="12" isRemoveLineWhenBlank="true">
					<printWhenExpression><![CDATA[!$F{contrato.dataAlteracaoManual_Apresentar}.equals("")]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle" markup="none">
					<font size="8" isBold="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$R{Dt_alteracao}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-7" x="69" y="0" width="47" height="12" isRemoveLineWhenBlank="true"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{contrato.dataAlteracaoManual_Apresentar}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="118" y="0" width="62" height="12" isRemoveLineWhenBlank="true">
					<printWhenExpression><![CDATA[!$F{contrato.responsavelDataBase.nome}.equals("")]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font size="8" isBold="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$R{Responsavel}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-7" x="180" y="0" width="106" height="12" isRemoveLineWhenBlank="true"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{contrato.responsavelDataBase.nome}]]></textFieldExpression>
			</textField>
		</band>
		<band height="13">
			<printWhenExpression><![CDATA[$F{produto.tipoProduto}.equals("DI")]]></printWhenExpression>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-1" x="453" y="0" width="51" height="12" isRemoveLineWhenBlank="true">
					<printWhenExpression><![CDATA[$P{detalharPeriodoProduto}]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{dataFinalVigencia_Apresentar}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="409" y="0" width="44" height="12" isRemoveLineWhenBlank="true">
					<printWhenExpression><![CDATA[$P{detalharPeriodoProduto}]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle" markup="none">
					<font size="8" isBold="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$R{Termino}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-1" x="349" y="0" width="60" height="12" isRemoveLineWhenBlank="true">
					<printWhenExpression><![CDATA[$P{detalharPeriodoProduto}]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{dataInicioVigencia_Apresentar}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="319" y="0" width="30" height="12" isRemoveLineWhenBlank="true">
					<printWhenExpression><![CDATA[$P{detalharPeriodoProduto}]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle" markup="none">
					<font size="8" isBold="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$R{Inicio}]]></textFieldExpression>
			</textField>
		</band>
		<band height="12">
			<printWhenExpression><![CDATA[$F{produto.tipoProduto}.equals("TP") | !$F{pacoteVO.titulo}.equals("")]]></printWhenExpression>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-7" stretchType="RelativeToTallestObject" x="60" y="0" width="169" height="12" isPrintWhenDetailOverflows="true"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="9"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{produto.tipoProduto}.equals("TP") ? $F{nomeAlunoPersonal} : $F{pacoteVO.titulo}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<columnFooter>
		<band splitType="Stretch"/>
	</columnFooter>
	<pageFooter>
		<band splitType="Stretch"/>
	</pageFooter>
	<summary>
		<band splitType="Stretch"/>
	</summary>
</jasperReport>

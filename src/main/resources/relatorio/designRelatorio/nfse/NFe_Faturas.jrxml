<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="NFe_Faturas" language="groovy" columnCount="15" printOrder="Horizontal" pageWidth="581" pageHeight="30" columnWidth="38" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0">
	<property name="ireport.zoom" value="4.0"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<field name="valor" class="java.lang.String"/>
	<field name="dataVencimento" class="java.lang.String"/>
	<field name="numero" class="java.lang.String"/>
	<field name="ordem" class="java.lang.Integer"/>
	<detail>
		<band height="30" splitType="Stretch">
			<rectangle>
				<reportElement x="0" y="0" width="38" height="30"/>
			</rectangle>
			<textField isStretchWithOverflow="true">
				<reportElement x="1" y="0" width="37" height="9" isPrintWhenDetailOverflows="true"/>
				<textElement textAlignment="Left" verticalAlignment="Middle" markup="none">
					<font size="5" isBold="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA["Num. " + $F{numero}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement x="1" y="9" width="37" height="9" isPrintWhenDetailOverflows="true"/>
				<textElement textAlignment="Left" verticalAlignment="Middle" markup="none">
					<font size="5" isBold="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA["Venc. " + $F{dataVencimento}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement x="1" y="19" width="37" height="9" isPrintWhenDetailOverflows="true"/>
				<textElement textAlignment="Left" verticalAlignment="Middle" markup="none">
					<font size="5" isBold="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{valor}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
</jasperReport>

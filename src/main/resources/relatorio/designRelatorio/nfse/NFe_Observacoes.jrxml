<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="NFe_Produtos" language="groovy" pageWidth="315" pageHeight="14" columnWidth="315" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0">
	<property name="ireport.zoom" value="4.0"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<field name="campo" class="java.lang.String"/>
	<field name="texto" class="java.lang.String"/>
	<detail>
		<band height="13" splitType="Stretch">
			<textField isStretchWithOverflow="true">
				<reportElement x="0" y="0" width="315" height="13" isPrintWhenDetailOverflows="true"/>
				<textElement textAlignment="Left" verticalAlignment="Middle" markup="none">
					<font size="8" isBold="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{campo} + ": " + $F{texto}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
</jasperReport>

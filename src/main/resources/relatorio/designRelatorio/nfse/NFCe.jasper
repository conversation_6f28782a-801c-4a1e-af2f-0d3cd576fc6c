¬í sr (net.sf.jasperreports.engine.JasperReport      'Ø L compileDatat Ljava/io/Serializable;L compileNameSuffixt Ljava/lang/String;L 
compilerClassq ~ xr -net.sf.jasperreports.engine.base.JRBaseReport      'Ø ,I PSEUDO_SERIAL_VERSION_UIDI bottomMarginI columnCountI 
columnSpacingI columnWidthZ ignorePaginationZ isFloatColumnFooterZ isSummaryNewPageZ  isSummaryWithPageHeaderAndFooterZ isTitleNewPageI 
leftMarginB orientationI 
pageHeightI 	pageWidthB 
printOrderI rightMarginI 	topMarginB whenNoDataTypeL 
backgroundt $Lnet/sf/jasperreports/engine/JRBand;L columnDirectiont 3Lnet/sf/jasperreports/engine/type/RunDirectionEnum;L columnFooterq ~ L columnHeaderq ~ [ datasetst ([Lnet/sf/jasperreports/engine/JRDataset;L defaultFontt *Lnet/sf/jasperreports/engine/JRReportFont;L defaultStylet %Lnet/sf/jasperreports/engine/JRStyle;L detailq ~ L 
detailSectiont 'Lnet/sf/jasperreports/engine/JRSection;[ fontst +[Lnet/sf/jasperreports/engine/JRReportFont;L formatFactoryClassq ~ L 
importsSett Ljava/util/Set;L languageq ~ L lastPageFooterq ~ L mainDatasett 'Lnet/sf/jasperreports/engine/JRDataset;L nameq ~ L noDataq ~ L orientationValuet 2Lnet/sf/jasperreports/engine/type/OrientationEnum;L 
pageFooterq ~ L 
pageHeaderq ~ L printOrderValuet 1Lnet/sf/jasperreports/engine/type/PrintOrderEnum;[ stylest &[Lnet/sf/jasperreports/engine/JRStyle;L summaryq ~ [ 	templatest /[Lnet/sf/jasperreports/engine/JRReportTemplate;L titleq ~ L whenNoDataTypeValuet 5Lnet/sf/jasperreports/engine/type/WhenNoDataTypeEnum;xp  wñ            O           p  S        p~r 1net.sf.jasperreports.engine.type.RunDirectionEnum          xr java.lang.Enum          xpt LTRppppppsr .net.sf.jasperreports.engine.base.JRBaseSection      'Ø [ bandst %[Lnet/sf/jasperreports/engine/JRBand;xpur %[Lnet.sf.jasperreports.engine.JRBand;Ý~ìÊ5  xp   sr +net.sf.jasperreports.engine.base.JRBaseBand      'Ø I PSEUDO_SERIAL_VERSION_UIDI heightZ isSplitAllowedL printWhenExpressiont *Lnet/sf/jasperreports/engine/JRExpression;L 	splitTypet Ljava/lang/Byte;L splitTypeValuet 0Lnet/sf/jasperreports/engine/type/SplitTypeEnum;xr 3net.sf.jasperreports.engine.base.JRBaseElementGroup      'Ø L childrent Ljava/util/List;L elementGroupt ,Lnet/sf/jasperreports/engine/JRElementGroup;xpsr java.util.ArrayListxÒÇa I sizexp    w    sr 0net.sf.jasperreports.engine.base.JRBaseRectangle      'Ø L radiust Ljava/lang/Integer;xr 5net.sf.jasperreports.engine.base.JRBaseGraphicElement      'Ø I PSEUDO_SERIAL_VERSION_UIDL fillq ~ L 	fillValuet +Lnet/sf/jasperreports/engine/type/FillEnum;L linePent #Lnet/sf/jasperreports/engine/JRPen;L penq ~ xr .net.sf.jasperreports.engine.base.JRBaseElement      'Ø I PSEUDO_SERIAL_VERSION_UIDI heightZ isPrintInFirstWholeBandZ isPrintRepeatedValuesZ isPrintWhenDetailOverflowsZ isRemoveLineWhenBlankB positionTypeB stretchTypeI widthI xI yL 	backcolort Ljava/awt/Color;L defaultStyleProvidert 4Lnet/sf/jasperreports/engine/JRDefaultStyleProvider;L elementGroupq ~ "L 	forecolorq ~ ,L keyq ~ L modeq ~ L 	modeValuet +Lnet/sf/jasperreports/engine/type/ModeEnum;L parentStyleq ~ L parentStyleNameReferenceq ~ L positionTypeValuet 3Lnet/sf/jasperreports/engine/type/PositionTypeEnum;L printWhenExpressionq ~ L printWhenGroupChangest %Lnet/sf/jasperreports/engine/JRGroup;L 
propertiesMapt -Lnet/sf/jasperreports/engine/JRPropertiesMap;[ propertyExpressionst 3[Lnet/sf/jasperreports/engine/JRPropertyExpression;L stretchTypeValuet 2Lnet/sf/jasperreports/engine/type/StretchTypeEnum;xp  wñ   T       E       pq ~ q ~ #pppppp~r 1net.sf.jasperreports.engine.type.PositionTypeEnum          xq ~ t FIX_RELATIVE_TO_TOPpppp~r 0net.sf.jasperreports.engine.type.StretchTypeEnum          xq ~ t 
NO_STRETCH  wîppsr *net.sf.jasperreports.engine.base.JRBasePen      'Ø I PSEUDO_SERIAL_VERSION_UIDL 	lineColorq ~ ,L 	lineStyleq ~ L lineStyleValuet 0Lnet/sf/jasperreports/engine/type/LineStyleEnum;L 	lineWidtht Ljava/lang/Float;L penContainert ,Lnet/sf/jasperreports/engine/JRPenContainer;xp  wñpppsr java.lang.FloatÚíÉ¢Û<ðì F valuexr java.lang.Number¬à  xp?   q ~ 4ppsq ~ &  wñ          E      Tpq ~ q ~ #ppp~r )net.sf.jasperreports.engine.type.ModeEnum          xq ~ t OPAQUEppq ~ 6ppppq ~ 9  wîppsq ~ ;  wñpppsq ~ @?   q ~ Cppsq ~ &  wñ          E      rpq ~ q ~ #ppppppq ~ 6ppppq ~ 9  wîppsq ~ ;  wñpppsq ~ @?   q ~ Ippsq ~ &  wñ           !      rpq ~ q ~ #ppppppq ~ 6ppppq ~ 9  wîppsq ~ ;  wñpppsq ~ @?   q ~ Lppsq ~ &  wñ           U  õ   rpq ~ q ~ #ppppppq ~ 6ppppq ~ 9  wîppsq ~ ;  wñpppsq ~ @?   q ~ Oppsq ~ &  wñ           ;     rpq ~ q ~ #ppppppq ~ 6ppppq ~ 9  wîppsq ~ ;  wñpppsq ~ @?   q ~ Rppsq ~ &  wñ           #   Ò   rpq ~ q ~ #ppppppq ~ 6ppppq ~ 9  wîppsq ~ ;  wñpppsq ~ @?   q ~ Uppsq ~ &  wñ              õ   rpq ~ q ~ #ppppppq ~ 6ppppq ~ 9  wîppsq ~ ;  wñpppsq ~ @?   q ~ Xppsr 0net.sf.jasperreports.engine.base.JRBaseTextField      'Ø I PSEUDO_SERIAL_VERSION_UIDI 
bookmarkLevelB evaluationTimeB hyperlinkTargetB 
hyperlinkTypeZ isStretchWithOverflowL anchorNameExpressionq ~ L evaluationGroupq ~ 0L evaluationTimeValuet 5Lnet/sf/jasperreports/engine/type/EvaluationTimeEnum;L 
expressionq ~ L hyperlinkAnchorExpressionq ~ L hyperlinkPageExpressionq ~ [ hyperlinkParameterst 3[Lnet/sf/jasperreports/engine/JRHyperlinkParameter;L hyperlinkReferenceExpressionq ~ L hyperlinkTooltipExpressionq ~ L isBlankWhenNullt Ljava/lang/Boolean;L 
linkTargetq ~ L linkTypeq ~ L patternq ~ xr 2net.sf.jasperreports.engine.base.JRBaseTextElement      'Ø %I PSEUDO_SERIAL_VERSION_UIDL borderq ~ L borderColorq ~ ,L bottomBorderq ~ L bottomBorderColorq ~ ,L 
bottomPaddingq ~ 'L fontNameq ~ L fontSizeq ~ 'L horizontalAlignmentq ~ L horizontalAlignmentValuet 6Lnet/sf/jasperreports/engine/type/HorizontalAlignEnum;L isBoldq ~ ^L isItalicq ~ ^L 
isPdfEmbeddedq ~ ^L isStrikeThroughq ~ ^L isStyledTextq ~ ^L isUnderlineq ~ ^L 
leftBorderq ~ L leftBorderColorq ~ ,L leftPaddingq ~ 'L lineBoxt 'Lnet/sf/jasperreports/engine/JRLineBox;L lineSpacingq ~ L lineSpacingValuet 2Lnet/sf/jasperreports/engine/type/LineSpacingEnum;L markupq ~ L paddingq ~ 'L pdfEncodingq ~ L pdfFontNameq ~ L 
reportFontq ~ L rightBorderq ~ L rightBorderColorq ~ ,L rightPaddingq ~ 'L rotationq ~ L 
rotationValuet /Lnet/sf/jasperreports/engine/type/RotationEnum;L 	topBorderq ~ L topBorderColorq ~ ,L 
topPaddingq ~ 'L verticalAlignmentq ~ L verticalAlignmentValuet 4Lnet/sf/jasperreports/engine/type/VerticalAlignEnum;xq ~ +  wñ          E      pq ~ q ~ #pt 
textField-212ppppq ~ 6ppppq ~ 9  wñpppppppp~r 4net.sf.jasperreports.engine.type.HorizontalAlignEnum          xq ~ t CENTERsr java.lang.BooleanÍ rÕúî Z valuexpppppppppsr .net.sf.jasperreports.engine.base.JRBaseLineBox      'Ø L 
bottomPaddingq ~ 'L 	bottomPent +Lnet/sf/jasperreports/engine/base/JRBoxPen;L boxContainert ,Lnet/sf/jasperreports/engine/JRBoxContainer;L leftPaddingq ~ 'L leftPenq ~ mL paddingq ~ 'L penq ~ mL rightPaddingq ~ 'L rightPenq ~ mL 
topPaddingq ~ 'L topPenq ~ mxppsr 3net.sf.jasperreports.engine.base.JRBaseBoxBottomPen      'Ø  xr -net.sf.jasperreports.engine.base.JRBaseBoxPen      'Ø L lineBoxq ~ axq ~ ;  wñppppq ~ oq ~ oq ~ epsr 1net.sf.jasperreports.engine.base.JRBaseBoxLeftPen      'Ø  xq ~ q  wñppppq ~ oq ~ opsq ~ q  wñppppq ~ oq ~ opsr 2net.sf.jasperreports.engine.base.JRBaseBoxRightPen      'Ø  xq ~ q  wñppppq ~ oq ~ opsr 0net.sf.jasperreports.engine.base.JRBaseBoxTopPen      'Ø  xq ~ q  wñppppq ~ oq ~ opppppt Helvetica-Boldppppppppppp  wñ        pp~r 3net.sf.jasperreports.engine.type.EvaluationTimeEnum          xq ~ t NOWsr 1net.sf.jasperreports.engine.base.JRBaseExpression      'Ø I id[ chunkst 0[Lnet/sf/jasperreports/engine/JRExpressionChunk;L valueClassNameq ~ L valueClassRealNameq ~ xp   	ur 0[Lnet.sf.jasperreports.engine.JRExpressionChunk;mYÏÞiK£U  xp   sr 6net.sf.jasperreports.engine.base.JRBaseExpressionChunk      'Ø B typeL textq ~ xpt nomeFantasiat java.lang.Stringppppppsq ~ j pppsr 1net.sf.jasperreports.engine.base.JRBaseStaticText      'Ø L textq ~ xq ~ _  wñ          E      Upq ~ q ~ #pt 
staticText-16ppppq ~ 6ppppq ~ 9  wñppppppppq ~ hq ~ kppppppppsq ~ lpsq ~ p  wñppppq ~ q ~ q ~ psq ~ s  wñppppq ~ q ~ psq ~ q  wñppppq ~ q ~ psq ~ v  wñppppq ~ q ~ psq ~ x  wñppppq ~ q ~ pppppt 	Helveticapppppppppppt IDANFE NFC-e - Documento Auxiliar da Nota Fiscal de Consumidor EletrÃ´nicasq ~ [  wñ          E      pq ~ q ~ #pt 
textField-212ppppq ~ 6ppppq ~ 9  wñppppppppq ~ hq ~ ppppppppsq ~ lpsq ~ p  wñppppq ~ q ~ q ~ psq ~ s  wñppppq ~ q ~ psq ~ q  wñppppq ~ q ~ psq ~ v  wñppppq ~ q ~ psq ~ x  wñppppq ~ q ~ pppppt Helvetica-Boldppppppppppp  wñ        ppq ~ |sq ~ ~   
uq ~    sq ~ t razaoSocialPrestadort java.lang.Stringppppppq ~ pppsq ~ [  wñ          E      $pq ~ q ~ #pt 
textField-212ppppq ~ 6ppppq ~ 9  wñppppppppq ~ hq ~ ppppppppsq ~ lpsq ~ p  wñppppq ~ £q ~ £q ~ ¡psq ~ s  wñppppq ~ £q ~ £psq ~ q  wñppppq ~ £q ~ £psq ~ v  wñppppq ~ £q ~ £psq ~ x  wñppppq ~ £q ~ £pppppt Helvetica-Boldppppppppppp  wñ        ppq ~ |sq ~ ~   uq ~    sq ~ t 
infoPrestadort java.lang.Stringppppppq ~ pppsq ~   wñ           !      rpq ~ q ~ #pt 
staticText-16ppppq ~ 6ppppq ~ 9  wñppppppsr java.lang.Integerâ ¤÷8 I valuexq ~ A   pq ~ hq ~ ppppppppsq ~ lpsq ~ p  wñppppq ~ ³q ~ ³q ~ ¯psq ~ s  wñppppq ~ ³q ~ ³psq ~ q  wñppppq ~ ³q ~ ³psq ~ v  wñppppq ~ ³q ~ ³psq ~ x  wñppppq ~ ³q ~ ³pppppt 	Helveticapppppppppp~r 2net.sf.jasperreports.engine.type.VerticalAlignEnum          xq ~ t MIDDLEt CÃ³digosq ~   wñ           #   Ò   rpq ~ q ~ #pt 
staticText-16ppppq ~ 6ppppq ~ 9  wñppppppq ~ ²pq ~ hq ~ ppppppppsq ~ lpsq ~ p  wñppppq ~ Àq ~ Àq ~ ¾psq ~ s  wñppppq ~ Àq ~ Àpsq ~ q  wñppppq ~ Àq ~ Àpsq ~ v  wñppppq ~ Àq ~ Àpsq ~ x  wñppppq ~ Àq ~ Àpppppt 	Helveticappppppppppq ~ »t Qtdesq ~   wñ           ?     rpq ~ q ~ #pt 
staticText-16ppppq ~ 6ppppq ~ 9  wñppppppq ~ ²pq ~ hq ~ ppppppppsq ~ lpsq ~ p  wñppppq ~ Êq ~ Êq ~ Èpsq ~ s  wñppppq ~ Êq ~ Êpsq ~ q  wñppppq ~ Êq ~ Êpsq ~ v  wñppppq ~ Êq ~ Êpsq ~ x  wñppppq ~ Êq ~ Êpppppt 	Helveticappppppppppq ~ »t Vlr. UnitÃ¡riosq ~   wñ           9     rpq ~ q ~ #pt 
staticText-16ppppq ~ 6ppppq ~ 9  wñppppppq ~ ²p~q ~ gt RIGHTq ~ ppppppppsq ~ lpsq ~ p  wñppppq ~ Öq ~ Öq ~ Òpsq ~ s  wñppppq ~ Öq ~ Öpsq ~ q  wñppppq ~ Öq ~ Öpsq ~ v  wñppppq ~ Öq ~ Öpsq ~ x  wñppppq ~ Öq ~ Öpppppt 	Helveticappppppppppq ~ »t Descontosq ~   wñ           ª   (   rpq ~ q ~ #pt 
staticText-16ppppq ~ 6ppppq ~ 9  wñppppppq ~ ²ppq ~ ppppppppsq ~ lpsq ~ p  wñppppq ~ àq ~ àq ~ Þpsq ~ s  wñppppq ~ àq ~ àpsq ~ q  wñppppq ~ àq ~ àpsq ~ v  wñppppq ~ àq ~ àpsq ~ x  wñppppq ~ àq ~ àpppppt 	Helveticappppppppppq ~ »t DescriÃ§Ã£o do Produtosq ~   wñ           S  õ   rpq ~ q ~ #pt 
staticText-16ppppq ~ 6ppppq ~ 9  wñppppppq ~ ²pq ~ Ôq ~ ppppppppsq ~ lpsq ~ p  wñppppq ~ êq ~ êq ~ èpsq ~ s  wñppppq ~ êq ~ êpsq ~ q  wñppppq ~ êq ~ êpsq ~ v  wñppppq ~ êq ~ êpsq ~ x  wñppppq ~ êq ~ êpppppt 	Helveticappppppppppq ~ »t Valor Totalsq ~   wñ           7  ½   rpq ~ q ~ #pt 
staticText-16ppppq ~ 6ppppq ~ 9  wñppppppq ~ ²pq ~ Ôq ~ ppppppppsq ~ lpsq ~ p  wñppppq ~ ôq ~ ôq ~ òpsq ~ s  wñppppq ~ ôq ~ ôpsq ~ q  wñppppq ~ ôq ~ ôpsq ~ v  wñppppq ~ ôq ~ ôpsq ~ x  wñppppq ~ ôq ~ ôpppppt 	Helveticappppppppppq ~ »t 
AcrÃ©scimosq ~ [  wñ          E      4pq ~ q ~ #pt 
textField-212ppppq ~ 6ppppq ~ 9  wñppppppppq ~ hq ~ ppppppppsq ~ lpsq ~ p  wñppppq ~ þq ~ þq ~ üpsq ~ s  wñppppq ~ þq ~ þpsq ~ q  wñppppq ~ þq ~ þpsq ~ v  wñppppq ~ þq ~ þpsq ~ x  wñppppq ~ þq ~ þpppppt Helvetica-Boldppppppppppp  wñ        ppq ~ |sq ~ ~   uq ~    sq ~ t enderecoPrestadort java.lang.Stringppppppq ~ pppsq ~ [  wñ          E      Cpq ~ q ~ #pt 
textField-212ppppq ~ 6ppppq ~ 9  wñppppppppq ~ hq ~ ppppppppsq ~ lpsq ~ p  wñppppq ~q ~q ~
psq ~ s  wñppppq ~q ~psq ~ q  wñppppq ~q ~psq ~ v  wñppppq ~q ~psq ~ x  wñppppq ~q ~pppppt Helvetica-Boldppppppppppp  wñ        ppq ~ |sq ~ ~   
uq ~    sq ~ t telefonePrestadort java.lang.Stringppppppq ~ pppsq ~   wñ          E      dpq ~ q ~ #pt 
staticText-16ppppq ~ 6ppppq ~ 9  wñppppppppq ~ hq ~ ppppppppsq ~ lpsq ~ p  wñppppq ~q ~q ~psq ~ s  wñppppq ~q ~psq ~ q  wñppppq ~q ~psq ~ v  wñppppq ~q ~psq ~ x  wñppppq ~q ~pppppt 	Helveticapppppppppppt /NÃ£o permite aproveitamento de crÃ©dito de ICMSsq ~   wñ              õ   rpq ~ q ~ #pt 
staticText-16ppppq ~ 6ppppq ~ 9  wñppppppq ~ ²pq ~ hq ~ ppppppppsq ~ lpsq ~ p  wñppppq ~$q ~$q ~"psq ~ s  wñppppq ~$q ~$psq ~ q  wñppppq ~$q ~$psq ~ v  wñppppq ~$q ~$psq ~ x  wñppppq ~$q ~$pppppt 	Helveticappppppppppq ~ »t Unsr 0net.sf.jasperreports.engine.base.JRBaseSubreport      'Ø L connectionExpressionq ~ L dataSourceExpressionq ~ L 
expressionq ~ L isUsingCacheq ~ ^[ 
parameterst 3[Lnet/sf/jasperreports/engine/JRSubreportParameter;L parametersMapExpressionq ~ [ returnValuest 5[Lnet/sf/jasperreports/engine/JRSubreportReturnValue;L runToBottomq ~ ^xq ~ +  wñ          E      pq ~ q ~ #pppppp~q ~ 5t FLOATppppq ~ 9psq ~ ~   uq ~    sq ~ t 
produtosJrt (net.sf.jasperreports.engine.JRDataSourcepsq ~ ~   uq ~    sq ~ t 
SUBREPORT_DIRsq ~ t  + "NFCe_Produtos.jasper"t java.lang.Stringpq ~ ppppsr ,net.sf.jasperreports.engine.base.JRBaseFrame      'Ø L borderq ~ L borderColorq ~ ,L bottomBorderq ~ L bottomBorderColorq ~ ,L 
bottomPaddingq ~ 'L childrenq ~ !L 
leftBorderq ~ L leftBorderColorq ~ ,L leftPaddingq ~ 'L lineBoxq ~ aL paddingq ~ 'L rightBorderq ~ L rightBorderColorq ~ ,L rightPaddingq ~ 'L 	topBorderq ~ L topBorderColorq ~ ,L 
topPaddingq ~ 'xq ~ +  wñ   )       H      pq ~ q ~ #ppppppq ~0ppppq ~ 9pppppsq ~ $   w   sq ~ &  wñ   '       E      pq ~ q ~?ppppppq ~ 6ppppq ~ 9  wîppsq ~ ;  wñpppsq ~ @?   q ~Appsq ~   wñ           e     pq ~ q ~?pt 
staticText-16ppppq ~ 6ppppq ~ 9  wñppppppppq ~ Ôq ~ kppppppppsq ~ lpsq ~ p  wñppppq ~Fq ~Fq ~Dpsq ~ s  wñppppq ~Fq ~Fpsq ~ q  wñppppq ~Fq ~Fpsq ~ v  wñppppq ~Fq ~Fpsq ~ x  wñppppq ~Fq ~Fpppppt 	Helveticappppppppppq ~ »t VALOR TOTAL:sq ~ [  wñ           R  ï   pq ~ q ~?pt 
staticText-16ppppq ~ 6ppppq ~ 9  wñppppppppq ~ Ôq ~ kppppppppsq ~ lpsq ~ p  wñppppq ~Pq ~Pq ~Npsq ~ s  wñppppq ~Pq ~Ppsq ~ q  wñppppq ~Pq ~Ppsq ~ v  wñppppq ~Pq ~Ppsq ~ x  wñppppq ~Pq ~Pppt noneppt 	Helveticappppppppppq ~ »  wñ        ppq ~ |sq ~ ~   uq ~    sq ~ t 
valorTotalt java.lang.Stringppppppppppsq ~   wñ           }  q   pq ~ q ~?pt 
staticText-16ppppq ~ 6ppppq ~ 9  wñppppppppq ~ Ôq ~ kppppq ~ pppsq ~ lpsq ~ p  wñppppq ~_q ~_q ~]psq ~ s  wñppppq ~_q ~_psq ~ q  wñppppq ~_q ~_psq ~ v  wñppppq ~_q ~_psq ~ x  wñppppq ~_q ~_pppppt 	Helveticappppppppppq ~ »t Forma de Pagamentosq ~   wñ           R  ï   pq ~ q ~?pt 
staticText-16ppppq ~ 6ppppq ~ 9  wñppppppppq ~ Ôq ~ kppppq ~ pppsq ~ lpsq ~ p  wñppppq ~iq ~iq ~gpsq ~ s  wñppppq ~iq ~ipsq ~ q  wñppppq ~iq ~ipsq ~ v  wñppppq ~iq ~ipsq ~ x  wñppppq ~iq ~ipppppt 	Helveticappppppppppq ~ »t 
Valor Pagosq ~ [  wñ           Ç   	    pq ~ q ~?pt 
staticText-16ppppq ~ 6ppppq ~ 9  wñpppppppppq ~ ppppppppsq ~ lpsq ~ p  wñppppq ~sq ~sq ~qpsq ~ s  wñppppq ~sq ~spsq ~ q  wñppppq ~sq ~spsq ~ v  wñppppq ~sq ~spsq ~ x  wñppppq ~sq ~sppt noneppt 	Helveticappppppppppq ~ »  wñ        ppq ~ |sq ~ ~   uq ~    sq ~ t  "Quantidade Total de Itens: " + sq ~ t qtdProdutost java.lang.Stringppppppppppxpppsq ~ lpsq ~ p  wñppppq ~q ~q ~?psq ~ s  wñppppq ~q ~psq ~ q  wñppppq ~q ~psq ~ v  wñppppq ~q ~psq ~ x  wñppppq ~q ~pppppppsq ~,  wñ          E      ¹pq ~ q ~ #ppppppq ~0ppppq ~ 9psq ~ ~   uq ~    sq ~ t pagamentosJrq ~6psq ~ ~   uq ~    sq ~ t 
SUBREPORT_DIRsq ~ t  + "NFCe_Pagamentos.jasper"t java.lang.Stringpq ~ ppppsq ~>  wñ   W       H      ¾pq ~ q ~ #ppppppq ~0ppppq ~ 9pppppsq ~ $   w   sq ~ &  wñ   T       E      pq ~ q ~ppppppq ~0ppppq ~ 9  wîppsq ~ ;  wñpppsq ~ @?   q ~ppsq ~   wñ          E      pq ~ q ~pt 
staticText-16ppppq ~0ppppq ~ 9  wñppppppppq ~ hq ~ kppppppppsq ~ lpsq ~ p  wñppppq ~q ~q ~psq ~ s  wñppppq ~q ~psq ~ q  wñppppq ~q ~psq ~ v  wñppppq ~q ~psq ~ x  wñppppq ~q ~pppppt 	Helveticapppppppppppt ÃREA DE MENSAGEM FISCALsq ~ [  wñ          E      pq ~ q ~pt 
staticText-16ppppq ~0ppppq ~ 9  wñppppppppq ~ hq ~ ppppppppsq ~ lpsq ~ p  wñppppq ~¥q ~¥q ~£psq ~ s  wñppppq ~¥q ~¥psq ~ q  wñppppq ~¥q ~¥psq ~ v  wñppppq ~¥q ~¥psq ~ x  wñppppq ~¥q ~¥ppt noneppt 	Helveticappppppppppp  wñ        ppq ~ |sq ~ ~   uq ~    sq ~ t informacoesNotat java.lang.Stringppppppppppsq ~   wñ          E      #pq ~ q ~pt 
staticText-16ppppq ~0ppppq ~ 9  wñppppppppq ~ hq ~ ppppppppsq ~ lpsq ~ p  wñppppq ~´q ~´q ~²psq ~ s  wñppppq ~´q ~´psq ~ q  wñppppq ~´q ~´psq ~ v  wñppppq ~´q ~´psq ~ x  wñppppq ~´q ~´pppppt 	Helveticapppppppppppt BConsulte pela Chave de Acesso em http://dec.fazenda.df.gov.br/nfcesq ~   wñ          E      3pq ~ q ~pt 
staticText-16ppppq ~0ppppq ~ 9  wñppppppppq ~ hq ~ kppppppppsq ~ lpsq ~ p  wñppppq ~¾q ~¾q ~¼psq ~ s  wñppppq ~¾q ~¾psq ~ q  wñppppq ~¾q ~¾psq ~ v  wñppppq ~¾q ~¾psq ~ x  wñppppq ~¾q ~¾pppppt 	Helveticapppppppppppt CHAVE DE ACESSOsq ~ [  wñ          E      Bpq ~ q ~pt 
staticText-16ppppq ~0ppppq ~ 9  wñppppppppq ~ hq ~ ppppppppsq ~ lpsq ~ p  wñppppq ~Èq ~Èq ~Æpsq ~ s  wñppppq ~Èq ~Èpsq ~ q  wñppppq ~Èq ~Èpsq ~ v  wñppppq ~Èq ~Èpsq ~ x  wñppppq ~Èq ~Èppt noneppt 	Helveticappppppppppp  wñ        ppq ~ |sq ~ ~   uq ~    sq ~ t chaveAcessot java.lang.Stringppppppppppxpppsq ~ lpsq ~ p  wñppppq ~Õq ~Õq ~psq ~ s  wñppppq ~Õq ~Õpsq ~ q  wñppppq ~Õq ~Õpsq ~ v  wñppppq ~Õq ~Õpsq ~ x  wñppppq ~Õq ~Õpppppppsq ~>  wñ   >       H     pq ~ q ~ #ppppppq ~0ppppq ~ 9pppppsq ~ $   w   sq ~ &  wñ   =       E      pq ~ q ~Ûppppppq ~0ppppq ~ 9  wîppsq ~ ;  wñpppsq ~ @?   q ~Ýppsq ~   wñ          E      pq ~ q ~Ûpt 
staticText-16ppppq ~0ppppq ~ 9  wñppppppppq ~ hq ~ kppppppppsq ~ lpsq ~ p  wñppppq ~âq ~âq ~àpsq ~ s  wñppppq ~âq ~âpsq ~ q  wñppppq ~âq ~âpsq ~ v  wñppppq ~âq ~âpsq ~ x  wñppppq ~âq ~âpppppt 	Helveticapppppppppppt 
OBSERVAÃÃESsq ~ [  wñ   *       E      pq ~ q ~Ûpt 
staticText-16ppppq ~0ppppq ~ 9  wñpppppppp~q ~ gt LEFTq ~ ppppppppsq ~ lpsq ~ p  wñppppq ~îq ~îq ~êpsq ~ s  wñppppq ~îq ~îpsq ~ q  wñppppq ~îq ~îpsq ~ v  wñppppq ~îq ~îpsq ~ x  wñppppq ~îq ~îppt noneppt 	Helveticappppppppppp  wñ        ppq ~ |sq ~ ~   uq ~    sq ~ t observacoest java.lang.Stringppppppppppxpppsq ~ lpsq ~ p  wñppppq ~ûq ~ûq ~Ûpsq ~ s  wñppppq ~ûq ~ûpsq ~ q  wñppppq ~ûq ~ûpsq ~ v  wñppppq ~ûq ~ûpsq ~ x  wñppppq ~ûq ~ûpppppppsq ~>  wñ   I       H     Mpq ~ q ~ #ppppppq ~0pppp~q ~ 8t RELATIVE_TO_TALLEST_OBJECTpppppsq ~ $   w   sq ~ &  wñ   G       E      pq ~ q ~ppppppq ~ 6ppppq ~ 9  wîppsq ~ ;  wñpppsq ~ @?   q ~ppsq ~   wñ          E      pq ~ q ~pt 
staticText-16ppppq ~ 6ppppq ~ 9  wñppppppppq ~ hq ~ kppppppppsq ~ lpsq ~ p  wñppppq ~
q ~
q ~psq ~ s  wñppppq ~
q ~
psq ~ q  wñppppq ~
q ~
psq ~ v  wñppppq ~
q ~
psq ~ x  wñppppq ~
q ~
pppppt 	Helveticappppppppppq ~ »t 
CONSUMIDORsq ~ [  wñ          E      6pq ~ q ~pt 
staticText-16ppppq ~ 6ppppq ~ 9  wñppppppppq ~ hq ~ ppppppppsq ~ lpsq ~ p  wñppppq ~q ~q ~psq ~ s  wñppppq ~q ~psq ~ q  wñppppq ~q ~psq ~ v  wñppppq ~q ~psq ~ x  wñppppq ~q ~ppt noneppt 	Helveticappppppppppq ~ »  wñ        ppq ~ |sq ~ ~   uq ~    sq ~ t enderecoConsumidort java.lang.Stringppppppppppsq ~ [  wñ          E      %pq ~ q ~pt 
staticText-16ppppq ~ 6ppppq ~ 9  wñppppppppq ~ hq ~ ppppppppsq ~ lpsq ~ p  wñppppq ~#q ~#q ~!psq ~ s  wñppppq ~#q ~#psq ~ q  wñppppq ~#q ~#psq ~ v  wñppppq ~#q ~#psq ~ x  wñppppq ~#q ~#ppt noneppt 	Helveticappppppppppq ~ »  wñ        ppq ~ |sq ~ ~   uq ~    sq ~ t nomeConsumidort java.lang.Stringppppppppppsq ~ [  wñ          E      pq ~ q ~pt 
staticText-16ppppq ~ 6ppppq ~ 9  wñppppppppq ~ hq ~ ppppppppsq ~ lpsq ~ p  wñppppq ~2q ~2q ~0psq ~ s  wñppppq ~2q ~2psq ~ q  wñppppq ~2q ~2psq ~ v  wñppppq ~2q ~2psq ~ x  wñppppq ~2q ~2ppt noneppt 	Helveticappppppppppq ~ »  wñ        ppq ~ |sq ~ ~   uq ~    sq ~ t 
cpfConsumidort java.lang.Stringppppppppppxpppsq ~ lpsq ~ p  wñppppq ~?q ~?q ~psq ~ s  wñppppq ~?q ~?psq ~ q  wñppppq ~?q ~?psq ~ v  wñppppq ~?q ~?psq ~ x  wñppppq ~?q ~?pppppppsq ~>  wñ  
       H     pq ~ q ~ #ppppppq ~0ppppq ~ 9pppppsq ~ $   w   sq ~ &  wñ   é       E      pq ~ q ~Eppppppq ~0ppppq ~ 9  wîppsq ~ ;  wñpppsq ~ @?   q ~Gppsq ~   wñ          E      
pq ~ q ~Ept 
staticText-16ppppq ~ 6ppppq ~ 9  wñppppppppq ~ hq ~ ppppppppsq ~ lpsq ~ p  wñppppq ~Lq ~Lq ~Jpsq ~ s  wñppppq ~Lq ~Lpsq ~ q  wñppppq ~Lq ~Lpsq ~ v  wñppppq ~Lq ~Lpsq ~ x  wñppppq ~Lq ~Lpppppt 	Helveticappppppppppq ~ »t Consulta via leitor de QR Codesr ,net.sf.jasperreports.engine.base.JRBaseImage      'Ø *I PSEUDO_SERIAL_VERSION_UIDI 
bookmarkLevelB evaluationTimeB hyperlinkTargetB 
hyperlinkTypeZ isLazyB onErrorTypeL anchorNameExpressionq ~ L borderq ~ L borderColorq ~ ,L bottomBorderq ~ L bottomBorderColorq ~ ,L 
bottomPaddingq ~ 'L evaluationGroupq ~ 0L evaluationTimeValueq ~ \L 
expressionq ~ L horizontalAlignmentq ~ L horizontalAlignmentValueq ~ `L hyperlinkAnchorExpressionq ~ L hyperlinkPageExpressionq ~ [ hyperlinkParametersq ~ ]L hyperlinkReferenceExpressionq ~ L hyperlinkTooltipExpressionq ~ L isUsingCacheq ~ ^L 
leftBorderq ~ L leftBorderColorq ~ ,L leftPaddingq ~ 'L lineBoxq ~ aL 
linkTargetq ~ L linkTypeq ~ L onErrorTypeValuet 2Lnet/sf/jasperreports/engine/type/OnErrorTypeEnum;L paddingq ~ 'L rightBorderq ~ L rightBorderColorq ~ ,L rightPaddingq ~ 'L 
scaleImageq ~ L scaleImageValuet 1Lnet/sf/jasperreports/engine/type/ScaleImageEnum;L 	topBorderq ~ L topBorderColorq ~ ,L 
topPaddingq ~ 'L verticalAlignmentq ~ L verticalAlignmentValueq ~ dxq ~ (  wñ   ¸        ¶   Î   pq ~ q ~Ept image-1ppppq ~ 6ppppq ~ 9  wîppsq ~ ;  wñppppq ~Wp  wñ         ppppppp~q ~ {t REPORTsq ~ ~   uq ~    sq ~ t qrCodet java.io.InputStreamppppppppq ~ kpppsq ~ lpsq ~ p  wñsr java.awt.Color¥3u F falphaI valueL cst Ljava/awt/color/ColorSpace;[ 	frgbvaluet [F[ fvalueq ~exp    ÿfffpppp~r .net.sf.jasperreports.engine.type.LineStyleEnum          xq ~ t SOLIDsq ~ @?   q ~aq ~aq ~Wpsq ~ s  wñsq ~c    ÿfffppppq ~hsq ~ @?   q ~aq ~apsq ~ q  wñppppq ~aq ~apsq ~ v  wñsq ~c    ÿfffppppq ~hsq ~ @?   q ~aq ~apsq ~ x  wñsq ~c    ÿfffppppq ~hsq ~ @?   q ~aq ~app~r 0net.sf.jasperreports.engine.type.OnErrorTypeEnum          xq ~ t BLANKppppp~r /net.sf.jasperreports.engine.type.ScaleImageEnum          xq ~ t 	REAL_SIZEpppppsq ~ [  wñ          E      Úpq ~ q ~Ept 
staticText-16ppppq ~ 6ppppq ~ 9  wñppppppppq ~ hq ~ ppppppppsq ~ lpsq ~ p  wñppppq ~}q ~}q ~{psq ~ s  wñppppq ~}q ~}psq ~ q  wñppppq ~}q ~}psq ~ v  wñppppq ~}q ~}psq ~ x  wñppppq ~}q ~}ppt noneppt 	Helveticappppppppppq ~ »  wñ        ppq ~ |sq ~ ~   uq ~    sq ~ t 	protocolot java.lang.Stringppppppppppsq ~   wñ   
              ípq ~ q ~Ept 
staticText-16ppppq ~ 6ppppq ~ 9  wñppppppsq ~ ±   	ppq ~ ppppppppsq ~ lpsq ~ p  wñppppq ~q ~q ~psq ~ s  wñppppq ~q ~psq ~ q  wñppppq ~q ~psq ~ v  wñppppq ~q ~psq ~ x  wñppppq ~q ~pppppt 	Helveticappppppppppq ~ »t DATA E HORA DA IMPRESSÃO:sq ~ [  wñ   
              ísq ~c    ÿÿÿÿpppq ~ q ~Ept 	dataRel-1p~q ~ Dt TRANSPARENTppq ~ 6ppppq ~ 9  wñpppppt Arialq ~ppq ~ q ~ kq ~ ppq ~ pppsq ~ lsq ~ ±   sq ~ p  wñsq ~c    ÿfffppppq ~hsq ~ @    q ~q ~q ~psq ~ s  wñsq ~c    ÿfffppppq ~hsq ~ @    q ~q ~psq ~ q  wñppppq ~q ~psq ~ v  wñsq ~c    ÿfffppppq ~hsq ~ @    q ~q ~psq ~ x  wñsq ~c    ÿfffppppq ~hsq ~ @    q ~q ~pppppt Helvetica-Obliquepppppppppp~q ~ ºt BOTTOM  wñ        ppq ~ |sq ~ ~   uq ~    sq ~ t 
new Date()t java.util.Dateppppppq ~ kppt dd/MM/yyyy HH:mm:ssxpppsq ~ lpsq ~ p  wñppppq ~³q ~³q ~Epsq ~ s  wñppppq ~³q ~³psq ~ q  wñppppq ~³q ~³psq ~ v  wñppppq ~³q ~³psq ~ x  wñppppq ~³q ~³pppppppsq ~ &  wñ           7  J   rpq ~ q ~ #ppppppq ~ 6ppppq ~ 9  wîppsq ~ ;  wñpppsq ~ @?   q ~¹ppsq ~   wñ           7  J   rpq ~ q ~ #pt 
staticText-16ppppq ~ 6ppppq ~ 9  wñppppppq ~ ²pq ~ hq ~ ppppppppsq ~ lpsq ~ p  wñppppq ~¾q ~¾q ~¼psq ~ s  wñppppq ~¾q ~¾psq ~ q  wñppppq ~¾q ~¾psq ~ v  wñppppq ~¾q ~¾psq ~ x  wñppppq ~¾q ~¾pppppt 	Helveticappppppppppq ~ »t COFINSxp  wñ  ¼pp~r .net.sf.jasperreports.engine.type.SplitTypeEnum          xq ~ t PREVENTppsr java.util.HashSetºD¸·4  xpw   ?@     t "net.sf.jasperreports.engine.data.*t net.sf.jasperreports.engine.*t java.util.*xt javapsr .net.sf.jasperreports.engine.base.JRBaseDataset      'Ø I PSEUDO_SERIAL_VERSION_UIDZ isMainB whenResourceMissingType[ fieldst &[Lnet/sf/jasperreports/engine/JRField;L filterExpressionq ~ [ groupst &[Lnet/sf/jasperreports/engine/JRGroup;L nameq ~ [ 
parameterst *[Lnet/sf/jasperreports/engine/JRParameter;L 
propertiesMapq ~ 1L queryt %Lnet/sf/jasperreports/engine/JRQuery;L resourceBundleq ~ L scriptletClassq ~ [ 
scriptletst *[Lnet/sf/jasperreports/engine/JRScriptlet;[ 
sortFieldst *[Lnet/sf/jasperreports/engine/JRSortField;[ 	variablest )[Lnet/sf/jasperreports/engine/JRVariable;L whenResourceMissingTypeValuet >Lnet/sf/jasperreports/engine/type/WhenResourceMissingTypeEnum;xp  wñ ur &[Lnet.sf.jasperreports.engine.JRField;<ßÇN*òp  xp   sr ,net.sf.jasperreports.engine.base.JRBaseField      'Ø L descriptionq ~ L nameq ~ L 
propertiesMapq ~ 1L valueClassNameq ~ L valueClassRealNameq ~ xppt razaoSocialPrestadorsr +net.sf.jasperreports.engine.JRPropertiesMap      'Ø L baseq ~ 1L propertiesListq ~ !L 
propertiesMapt Ljava/util/Map;xppppt java.lang.Stringpsq ~Ûpt 
infoPrestadorsq ~Þpppt java.lang.Stringpsq ~Ûpt enderecoPrestadorsq ~Þpppt java.lang.Stringpsq ~Ûpt informacoesNotasq ~Þpppt java.lang.Stringpsq ~Ûpt chaveAcessosq ~Þpppt java.lang.Stringpsq ~Ûpt 
cpfConsumidorsq ~Þpppt java.lang.Stringpsq ~Ûpt nomeConsumidorsq ~Þpppt java.lang.Stringpsq ~Ûpt enderecoConsumidorsq ~Þpppt java.lang.Stringpsq ~Ûpt 	protocolosq ~Þpppt java.lang.Stringpsq ~Ûpt qtdProdutossq ~Þpppt java.lang.Stringpsq ~Ûpt subTotalsq ~Þpppt java.lang.Stringpsq ~Ûpt 
valorTotalsq ~Þpppt java.lang.Stringpsq ~Ûpt 
cnpjPrestadorsq ~Þpppt java.lang.Stringpsq ~Ûpt 
produtosJrsq ~Þpppt java.lang.Objectpsq ~Ûpt pagamentosJrsq ~Þpppt java.lang.Objectpsq ~Ûpt nomeFantasiasq ~Þpppt java.lang.Stringpsq ~Ûpt telefonePrestadorsq ~Þpppt java.lang.Stringpsq ~Ûpt qrCodesq ~Þpppt java.lang.Objectpsq ~Ûpt observacoessq ~Þpppt java.lang.Stringpppt 
NotaManualur *[Lnet.sf.jasperreports.engine.JRParameter;" *Ã`!  xp   sr 0net.sf.jasperreports.engine.base.JRBaseParameter      'Ø 	Z isForPromptingZ isSystemDefinedL defaultValueExpressionq ~ L descriptionq ~ L nameq ~ L nestedTypeNameq ~ L 
propertiesMapq ~ 1L valueClassNameq ~ L valueClassRealNameq ~ xpppt REPORT_PARAMETERS_MAPpsq ~Þpppt 
java.util.Mappsq ~-ppt 
JASPER_REPORTpsq ~Þpppt (net.sf.jasperreports.engine.JasperReportpsq ~-ppt REPORT_CONNECTIONpsq ~Þpppt java.sql.Connectionpsq ~-ppt REPORT_MAX_COUNTpsq ~Þpppt java.lang.Integerpsq ~-ppt REPORT_DATA_SOURCEpsq ~Þpppq ~6psq ~-ppt REPORT_SCRIPTLETpsq ~Þpppt /net.sf.jasperreports.engine.JRAbstractScriptletpsq ~-ppt 
REPORT_LOCALEpsq ~Þpppt java.util.Localepsq ~-ppt REPORT_RESOURCE_BUNDLEpsq ~Þpppt java.util.ResourceBundlepsq ~-ppt REPORT_TIME_ZONEpsq ~Þpppt java.util.TimeZonepsq ~-ppt REPORT_FORMAT_FACTORYpsq ~Þpppt .net.sf.jasperreports.engine.util.FormatFactorypsq ~-ppt REPORT_CLASS_LOADERpsq ~Þpppt java.lang.ClassLoaderpsq ~-ppt REPORT_URL_HANDLER_FACTORYpsq ~Þpppt  java.net.URLStreamHandlerFactorypsq ~-ppt REPORT_FILE_RESOLVERpsq ~Þpppt -net.sf.jasperreports.engine.util.FileResolverpsq ~-ppt REPORT_TEMPLATESpsq ~Þpppt java.util.Collectionpsq ~-ppt SORT_FIELDSpsq ~Þpppt java.util.Listpsq ~-ppt REPORT_VIRTUALIZERpsq ~Þpppt )net.sf.jasperreports.engine.JRVirtualizerpsq ~-ppt IS_IGNORE_PAGINATIONpsq ~Þpppt java.lang.Booleanpsq ~-  sq ~ ~    uq ~    sq ~ t j"/home/<USER>/Repositorios/pacto/ZillyonWeb/trunk-limpo/src/main/resources/relatorio/designRelatorio/nfse/"t java.lang.Stringppt 
SUBREPORT_DIRpsq ~Þpppq ~vpsq ~Þpsq ~ $   w   t ireport.zoomt 	ireport.xt 	ireport.yt ireport.scriptlethandlingt ireport.encodingxsr java.util.HashMapÚÁÃ`Ñ F 
loadFactorI 	thresholdxp?@     w      q ~{t 1.3636363636363653q ~t 
ISO-8859-1q ~|t 0q ~}t 0q ~~t 0xsr ,net.sf.jasperreports.engine.base.JRBaseQuery      'Ø [ chunkst +[Lnet/sf/jasperreports/engine/JRQueryChunk;L languageq ~ xppt sqlppppur )[Lnet.sf.jasperreports.engine.JRVariable;bæ|,·D  xp   sr /net.sf.jasperreports.engine.base.JRBaseVariable      'Ø I PSEUDO_SERIAL_VERSION_UIDB calculationB 
incrementTypeZ isSystemDefinedB 	resetTypeL calculationValuet 2Lnet/sf/jasperreports/engine/type/CalculationEnum;L 
expressionq ~ L incrementGroupq ~ 0L incrementTypeValuet 4Lnet/sf/jasperreports/engine/type/IncrementTypeEnum;L incrementerFactoryClassNameq ~ L incrementerFactoryClassRealNameq ~ L initialValueExpressionq ~ L nameq ~ L 
resetGroupq ~ 0L resetTypeValuet 0Lnet/sf/jasperreports/engine/type/ResetTypeEnum;L valueClassNameq ~ L valueClassRealNameq ~ xp  wî   ~r 0net.sf.jasperreports.engine.type.CalculationEnum          xq ~ t SYSTEMpp~r 2net.sf.jasperreports.engine.type.IncrementTypeEnum          xq ~ t NONEppsq ~ ~   uq ~    sq ~ t new java.lang.Integer(1)q ~=pt PAGE_NUMBERp~r .net.sf.jasperreports.engine.type.ResetTypeEnum          xq ~ t REPORTq ~=psq ~  wî   q ~ppq ~ppsq ~ ~   uq ~    sq ~ t new java.lang.Integer(1)q ~=pt 
COLUMN_NUMBERp~q ~t PAGEq ~=psq ~  wî   ~q ~t COUNTsq ~ ~   uq ~    sq ~ t new java.lang.Integer(1)q ~=ppq ~ppsq ~ ~   uq ~    sq ~ t new java.lang.Integer(0)q ~=pt REPORT_COUNTpq ~q ~=psq ~  wî   q ~©sq ~ ~   uq ~    sq ~ t new java.lang.Integer(1)q ~=ppq ~ppsq ~ ~   uq ~    sq ~ t new java.lang.Integer(0)q ~=pt 
PAGE_COUNTpq ~¦q ~=psq ~  wî   q ~©sq ~ ~   uq ~    sq ~ t new java.lang.Integer(1)q ~=ppq ~ppsq ~ ~   uq ~    sq ~ t new java.lang.Integer(0)q ~=pt COLUMN_COUNTp~q ~t COLUMNq ~=p~r <net.sf.jasperreports.engine.type.WhenResourceMissingTypeEnum          xq ~ t EMPTYq ~*p~r 0net.sf.jasperreports.engine.type.OrientationEnum          xq ~ t PORTRAITpp~r /net.sf.jasperreports.engine.type.PrintOrderEnum          xq ~ t VERTICALpppp~r 3net.sf.jasperreports.engine.type.WhenNoDataTypeEnum          xq ~ t ALL_SECTIONS_NO_DETAILsr 6net.sf.jasperreports.engine.design.JRReportCompileData      'Ø L crosstabCompileDataq ~ßL datasetCompileDataq ~ßL mainDatasetCompileDataq ~ xpsq ~?@     w       xsq ~?@     w       xur [B¬óøTà  xp   NÊþº¾   ./ NotaManual_1540926566420_570380  ,net/sf/jasperreports/engine/fill/JREvaluator  parameter_REPORT_LOCALE 2Lnet/sf/jasperreports/engine/fill/JRFillParameter; parameter_JASPER_REPORT parameter_REPORT_VIRTUALIZER parameter_REPORT_TIME_ZONE parameter_SORT_FIELDS parameter_REPORT_FILE_RESOLVER parameter_REPORT_SCRIPTLET parameter_REPORT_PARAMETERS_MAP parameter_REPORT_CONNECTION parameter_REPORT_CLASS_LOADER parameter_REPORT_DATA_SOURCE $parameter_REPORT_URL_HANDLER_FACTORY parameter_IS_IGNORE_PAGINATION parameter_SUBREPORT_DIR parameter_REPORT_FORMAT_FACTORY parameter_REPORT_MAX_COUNT parameter_REPORT_TEMPLATES  parameter_REPORT_RESOURCE_BUNDLE field_enderecoPrestador .Lnet/sf/jasperreports/engine/fill/JRFillField; field_nomeConsumidor field_qtdProdutos field_cpfConsumidor field_nomeFantasia field_qrCode field_telefonePrestador field_protocolo field_observacoes field_pagamentosJr field_informacoesNota field_enderecoConsumidor field_infoPrestador field_subTotal field_cnpjPrestador field_valorTotal field_chaveAcesso field_produtosJr field_razaoSocialPrestador variable_PAGE_NUMBER 1Lnet/sf/jasperreports/engine/fill/JRFillVariable; variable_COLUMN_NUMBER variable_REPORT_COUNT variable_PAGE_COUNT variable_COLUMN_COUNT <init> ()V Code 2 3
  5  	  7  	  9  	  ; 	 	  = 
 	  ?  	  A  	  C 
 	  E  	  G  	  I  	  K  	  M  	  O  	  Q  	  S  	  U  	  W  	  Y  	  [  	  ]  	  _  	  a  	  c  	  e  	  g   	  i ! 	  k " 	  m # 	  o $ 	  q % 	  s & 	  u ' 	  w ( 	  y ) 	  { * 	  } + 	   , -	   . -	   / -	   0 -	   1 -	   LineNumberTable customizedInit 0(Ljava/util/Map;Ljava/util/Map;Ljava/util/Map;)V 
initParams (Ljava/util/Map;)V  
   
initFields  
   initVars  
   
REPORT_LOCALE  
java/util/Map  get &(Ljava/lang/Object;)Ljava/lang/Object;     0net/sf/jasperreports/engine/fill/JRFillParameter   
JASPER_REPORT ¢ REPORT_VIRTUALIZER ¤ REPORT_TIME_ZONE ¦ SORT_FIELDS ¨ REPORT_FILE_RESOLVER ª REPORT_SCRIPTLET ¬ REPORT_PARAMETERS_MAP ® REPORT_CONNECTION ° REPORT_CLASS_LOADER ² REPORT_DATA_SOURCE ´ REPORT_URL_HANDLER_FACTORY ¶ IS_IGNORE_PAGINATION ¸ 
SUBREPORT_DIR º REPORT_FORMAT_FACTORY ¼ REPORT_MAX_COUNT ¾ REPORT_TEMPLATES À REPORT_RESOURCE_BUNDLE Â enderecoPrestador Ä ,net/sf/jasperreports/engine/fill/JRFillField Æ nomeConsumidor È qtdProdutos Ê 
cpfConsumidor Ì nomeFantasia Î qrCode Ð telefonePrestador Ò 	protocolo Ô observacoes Ö pagamentosJr Ø informacoesNota Ú enderecoConsumidor Ü 
infoPrestador Þ subTotal à 
cnpjPrestador â 
valorTotal ä chaveAcesso æ 
produtosJr è razaoSocialPrestador ê PAGE_NUMBER ì /net/sf/jasperreports/engine/fill/JRFillVariable î 
COLUMN_NUMBER ð REPORT_COUNT ò 
PAGE_COUNT ô COLUMN_COUNT ö evaluate (I)Ljava/lang/Object; 
Exceptions java/lang/Throwable û h/home/<USER>/Repositorios/pacto/ZillyonWeb/trunk-limpo/src/main/resources/relatorio/designRelatorio/nfse/ ý java/lang/Integer ÿ (I)V 2
  getValue ()Ljava/lang/Object;
 Ç java/lang/String (net/sf/jasperreports/engine/JRDataSource
 java/lang/StringBuffer
 ¡ valueOf &(Ljava/lang/Object;)Ljava/lang/String;
	 (Ljava/lang/String;)V 2

 NFCe_Produtos.jasper append ,(Ljava/lang/String;)Ljava/lang/StringBuffer;

 toString ()Ljava/lang/String;

 Quantidade Total de Itens:   NFCe_Pagamentos.jasper" java/io/InputStream$ java/util/Date&
' 5 evaluateOld getOldValue*
 Ç+ evaluateEstimated 
SourceFile !     *                 	     
               
                                                                                                !     "     #     $     %     &     '     (     )     *     +     , -    . -    / -    0 -    1 -     2 3  4       ×*· 6*µ 8*µ :*µ <*µ >*µ @*µ B*µ D*µ F*µ H*µ J*µ L*µ N*µ P*µ R*µ T*µ V*µ X*µ Z*µ \*µ ^*µ `*µ b*µ d*µ f*µ h*µ j*µ l*µ n*µ p*µ r*µ t*µ v*µ x*µ z*µ |*µ ~*µ *µ *µ *µ *µ *µ ±       ² ,      	          ! " " ' # , $ 1 % 6 & ; ' @ ( E ) J * O + T , Y - ^ . c / h 0 m 1 r 2 w 3 | 4  5  6  7  8  9  :  ; ¤ < © = ® > ³ ? ¸ @ ½ A Â B Ç C Ì D Ñ E Ö      4   4     *+· *,· *-· ±           Q  R 
 S  T     4  ¥    E*+¹  À ¡À ¡µ 8*+£¹  À ¡À ¡µ :*+¥¹  À ¡À ¡µ <*+§¹  À ¡À ¡µ >*+©¹  À ¡À ¡µ @*+«¹  À ¡À ¡µ B*+­¹  À ¡À ¡µ D*+¯¹  À ¡À ¡µ F*+±¹  À ¡À ¡µ H*+³¹  À ¡À ¡µ J*+µ¹  À ¡À ¡µ L*+·¹  À ¡À ¡µ N*+¹¹  À ¡À ¡µ P*+»¹  À ¡À ¡µ R*+½¹  À ¡À ¡µ T*+¿¹  À ¡À ¡µ V*+Á¹  À ¡À ¡µ X*+Ã¹  À ¡À ¡µ Z±       N    \  ] $ ^ 6 _ H ` Z a l b ~ c  d ¢ e ´ f Æ g Ø h ê i ü j k  l2 mD n     4  »    W*+Å¹  À ÇÀ Çµ \*+É¹  À ÇÀ Çµ ^*+Ë¹  À ÇÀ Çµ `*+Í¹  À ÇÀ Çµ b*+Ï¹  À ÇÀ Çµ d*+Ñ¹  À ÇÀ Çµ f*+Ó¹  À ÇÀ Çµ h*+Õ¹  À ÇÀ Çµ j*+×¹  À ÇÀ Çµ l*+Ù¹  À ÇÀ Çµ n*+Û¹  À ÇÀ Çµ p*+Ý¹  À ÇÀ Çµ r*+ß¹  À ÇÀ Çµ t*+á¹  À ÇÀ Çµ v*+ã¹  À ÇÀ Çµ x*+å¹  À ÇÀ Çµ z*+ç¹  À ÇÀ Çµ |*+é¹  À ÇÀ Çµ ~*+ë¹  À ÇÀ Çµ ±       R    v  w $ x 6 y H z Z { l | ~ }  ~ ¢  ´  Æ  Ø  ê  ü    2 D V      4        [*+í¹  À ïÀ ïµ *+ñ¹  À ïÀ ïµ *+ó¹  À ïÀ ïµ *+õ¹  À ïÀ ïµ *+÷¹  À ïÀ ïµ ±              $  6  H  Z   ø ù  ú     ü 4  8    4Mª  /                      «   ·   Ã   Ï   Û   ç   õ        -  ;  \  j      ·  Å  Ó  á  ï  ý      'þM§«» Y·M§» Y·M§» Y·M§» Y·M§{» Y·M§o» Y·M§c» Y·M§W» Y·M§K*´ d¶À	M§=*´ ¶À	M§/*´ t¶À	M§!*´ \¶À	M§*´ h¶À	M§*´ ~¶ÀM§ ÷»
Y*´ R¶À	¸·¶¶M§ Ö*´ z¶À	M§ È»
Y!·*´ `¶À	¶¶M§ ª*´ n¶ÀM§ »
Y*´ R¶À	¸·#¶¶M§ {*´ p¶À	M§ m*´ |¶À	M§ _*´ l¶À	M§ Q*´ r¶À	M§ C*´ ^¶À	M§ 5*´ b¶À	M§ '*´ f¶À%M§ *´ j¶À	M§ »'Y·(M,°       ò <        ¤  ¥  ©  ª  ®  ¯ ¢ ³ « ´ ® ¸ · ¹ º ½ Ã ¾ Æ Â Ï Ã Ò Ç Û È Þ Ì ç Í ê Ñ õ Ò ø Ö × Û Ü à á" å- æ0 ê; ë> ï\ ð_ ôj õm ù ú þ ÿ·ºÅ	È
ÓÖáäïòý !"&'+',*028 ) ù  ú     ü 4  8    4Mª  /                      «   ·   Ã   Ï   Û   ç   õ        -  ;  \  j      ·  Å  Ó  á  ï  ý      'þM§«» Y·M§» Y·M§» Y·M§» Y·M§{» Y·M§o» Y·M§c» Y·M§W» Y·M§K*´ d¶,À	M§=*´ ¶,À	M§/*´ t¶,À	M§!*´ \¶,À	M§*´ h¶,À	M§*´ ~¶,ÀM§ ÷»
Y*´ R¶À	¸·¶¶M§ Ö*´ z¶,À	M§ È»
Y!·*´ `¶,À	¶¶M§ ª*´ n¶,ÀM§ »
Y*´ R¶À	¸·#¶¶M§ {*´ p¶,À	M§ m*´ |¶,À	M§ _*´ l¶,À	M§ Q*´ r¶,À	M§ C*´ ^¶,À	M§ 5*´ b¶,À	M§ '*´ f¶,À%M§ *´ j¶,À	M§ »'Y·(M,°       ò <  A C G H L M Q R ¢V «W ®[ ·\ º` Ãa Æe Ïf Òj Ûk Þo çp êt õu øyz~"-0;>\_jm¡¢¦·§º«Å¬È°Ó±Öµá¶äºï»ò¿ýÀ ÄÅÉÊÎ'Ï*Ó2Û - ù  ú     ü 4  8    4Mª  /                      «   ·   Ã   Ï   Û   ç   õ        -  ;  \  j      ·  Å  Ó  á  ï  ý      'þM§«» Y·M§» Y·M§» Y·M§» Y·M§{» Y·M§o» Y·M§c» Y·M§W» Y·M§K*´ d¶À	M§=*´ ¶À	M§/*´ t¶À	M§!*´ \¶À	M§*´ h¶À	M§*´ ~¶ÀM§ ÷»
Y*´ R¶À	¸·¶¶M§ Ö*´ z¶À	M§ È»
Y!·*´ `¶À	¶¶M§ ª*´ n¶ÀM§ »
Y*´ R¶À	¸·#¶¶M§ {*´ p¶À	M§ m*´ |¶À	M§ _*´ l¶À	M§ Q*´ r¶À	M§ C*´ ^¶À	M§ 5*´ b¶À	M§ '*´ f¶À%M§ *´ j¶À	M§ »'Y·(M,°       ò <  ä æ ê ë ï ð ô õ ¢ù «ú ®þ ·ÿ º Ã Æ Ï	 Ò
 Û Þ ç ê õ ø!"&'"+-,00;1>5\6_:j;m?@DEI·JºNÅOÈSÓTÖXáYä]ï^òbýc ghlmq'r*v2~ .    t _1540926566420_570380t 2net.sf.jasperreports.engine.design.JRJavacCompiler
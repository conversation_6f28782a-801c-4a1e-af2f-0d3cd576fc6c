<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="NFCe_Produtos" language="groovy" pageWidth="581" pageHeight="802" columnWidth="581" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0">
	<property name="ireport.zoom" value="4.0"/>
	<property name="ireport.x" value="1253"/>
	<property name="ireport.y" value="0"/>
	<field name="codigo" class="java.lang.String"/>
	<field name="quantidade" class="java.lang.String"/>
	<field name="descricao" class="java.lang.String"/>
	<field name="unidade" class="java.lang.String"/>
	<field name="valorUnitario" class="java.lang.String"/>
	<field name="valorDesconto" class="java.lang.String"/>
	<field name="valorAcrescimo" class="java.lang.String"/>
	<field name="valorTotal" class="java.lang.String"/>
	<field name="cofins" class="java.lang.String"/>
	<detail>
		<band height="13" splitType="Stretch">
			<rectangle>
				<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="33" height="13"/>
				<graphicElement>
					<pen lineWidth="0.5" lineStyle="Solid"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement stretchType="RelativeToBandHeight" x="33" y="0" width="172" height="13"/>
				<graphicElement>
					<pen lineWidth="0.5" lineStyle="Solid"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement stretchType="RelativeToBandHeight" x="205" y="0" width="35" height="13"/>
				<graphicElement>
					<pen lineWidth="0.5" lineStyle="Solid"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement stretchType="RelativeToBandHeight" x="240" y="0" width="23" height="13"/>
				<graphicElement>
					<pen lineWidth="0.5" lineStyle="Solid"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement stretchType="RelativeToBandHeight" x="263" y="0" width="63" height="13"/>
				<graphicElement>
					<pen lineWidth="0.5" lineStyle="Solid"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement stretchType="RelativeToBandHeight" x="325" y="0" width="55" height="13"/>
				<graphicElement>
					<pen lineWidth="0.5" lineStyle="Solid"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement stretchType="RelativeToBandHeight" x="380" y="0" width="59" height="13"/>
				<graphicElement>
					<pen lineWidth="0.5" lineStyle="Solid"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement stretchType="RelativeToBandHeight" x="439" y="0" width="57" height="13"/>
				<graphicElement>
					<pen lineWidth="0.5" lineStyle="Solid"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement stretchType="RelativeToBandHeight" x="496" y="0" width="85" height="13"/>
				<graphicElement>
					<pen lineWidth="0.5" lineStyle="Solid"/>
				</graphicElement>
			</rectangle>
			<textField>
				<reportElement x="0" y="0" width="33" height="13"/>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font size="6" isBold="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{codigo}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="35" y="0" width="170" height="13"/>
				<textElement verticalAlignment="Middle" markup="none">
					<font size="6" isBold="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{descricao}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="205" y="0" width="35" height="13"/>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font size="6" isBold="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{quantidade}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="240" y="0" width="23" height="13"/>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font size="6" isBold="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{unidade}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="263" y="0" width="61" height="13"/>
				<textElement textAlignment="Right" verticalAlignment="Middle" markup="none">
					<font size="6" isBold="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{valorUnitario}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="325" y="0" width="55" height="13"/>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font size="6" isBold="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{cofins}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="380" y="0" width="57" height="13"/>
				<textElement textAlignment="Right" verticalAlignment="Middle" markup="none">
					<font size="6" isBold="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{valorDesconto}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="439" y="0" width="55" height="13"/>
				<textElement textAlignment="Right" verticalAlignment="Middle" markup="none">
					<font size="6" isBold="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{valorAcrescimo}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="496" y="0" width="83" height="13"/>
				<textElement textAlignment="Right" verticalAlignment="Middle" markup="none">
					<font size="6" isBold="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{valorTotal}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
</jasperReport>

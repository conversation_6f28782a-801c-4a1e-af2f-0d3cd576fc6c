<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="NotaManual" pageWidth="595" pageHeight="880" whenNoDataType="AllSectionsNoDetail" columnWidth="591" leftMargin="2" rightMargin="2" topMargin="2" bottomMargin="2" whenResourceMissingType="Empty">
	<property name="ireport.zoom" value="1.5"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="96"/>
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.encoding" value="ISO-8859-1"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="java.util.*"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<parameter name="SUBREPORT_DIR" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA["C:\\PactoJ\\Desenvolvimento\\Sistemas\\ZillyonWeb\\ZW_Tronco\\src\\main\\resources\\relatorio\\designRelatorio\\nfse\\"]]></defaultValueExpression>
	</parameter>
	<queryString>
		<![CDATA[]]>
	</queryString>
	<field name="razaoSocialPrestador" class="java.lang.String"/>
	<field name="infoPrestador" class="java.lang.String"/>
	<field name="enderecoPrestador" class="java.lang.String"/>
	<field name="informacoesNota" class="java.lang.String"/>
	<field name="chaveAcesso" class="java.lang.String"/>
	<field name="cpfConsumidor" class="java.lang.String"/>
	<field name="nomeConsumidor" class="java.lang.String"/>
	<field name="enderecoConsumidor" class="java.lang.String"/>
	<field name="protocolo" class="java.lang.String"/>
	<field name="qtdProdutos" class="java.lang.String"/>
	<field name="subTotal" class="java.lang.String"/>
	<field name="valorTotal" class="java.lang.String"/>
	<field name="cnpjPrestador" class="java.lang.String"/>
	<field name="produtosJr" class="java.lang.Object"/>
	<field name="pagamentosJr" class="java.lang.Object"/>
	<field name="nomeFantasia" class="java.lang.String"/>
	<field name="telefonePrestador" class="java.lang.String"/>
	<field name="qrCode" class="java.lang.Object"/>
	<field name="observacoes" class="java.lang.String"/>
	<detail>
		<band height="700" splitType="Prevent">
			<rectangle>
				<reportElement x="5" y="0" width="581" height="84"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement mode="Opaque" x="5" y="84" width="581" height="30"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="5" y="114" width="581" height="16"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="5" y="114" width="33" height="16"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="501" y="114" width="85" height="16"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="385" y="114" width="59" height="16"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="210" y="114" width="35" height="16"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="245" y="114" width="23" height="16"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-212" x="5" y="4" width="581" height="14"/>
				<textElement textAlignment="Center">
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{nomeFantasia}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement key="staticText-16" x="5" y="85" width="581" height="14"/>
				<textElement textAlignment="Center">
					<font isBold="true" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[DANFE NFC-e - Documento Auxiliar da Nota Fiscal de Consumidor Eletrônica]]></text>
			</staticText>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-212" x="5" y="20" width="581" height="14"/>
				<textElement textAlignment="Center">
					<font isBold="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{razaoSocialPrestador}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-212" x="5" y="36" width="581" height="14"/>
				<textElement textAlignment="Center">
					<font isBold="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{infoPrestador}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement key="staticText-16" x="5" y="114" width="33" height="16"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="8" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Código]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-16" x="210" y="114" width="35" height="16"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="8" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Qtde]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-16" x="267" y="114" width="63" height="16"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="8" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Vlr. Unitário]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-16" x="385" y="114" width="57" height="16"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="8" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Desconto]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-16" x="40" y="114" width="170" height="16"/>
				<textElement verticalAlignment="Middle">
					<font size="8" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Descrição do Produto]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-16" x="501" y="114" width="83" height="16"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="8" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Valor Total]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-16" x="445" y="114" width="55" height="16"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="8" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Acréscimo]]></text>
			</staticText>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-212" x="5" y="52" width="581" height="14"/>
				<textElement textAlignment="Center">
					<font isBold="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{enderecoPrestador}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-212" x="5" y="67" width="581" height="14"/>
				<textElement textAlignment="Center">
					<font isBold="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{telefonePrestador}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement key="staticText-16" x="5" y="100" width="581" height="14"/>
				<textElement textAlignment="Center">
					<font isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Não permite aproveitamento de crédito de ICMS]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-16" x="245" y="114" width="23" height="16"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="8" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Un]]></text>
			</staticText>
			<subreport isUsingCache="false">
				<reportElement positionType="Float" x="5" y="130" width="581" height="15"/>
				<dataSourceExpression><![CDATA[$F{produtosJr}]]></dataSourceExpression>
				<subreportExpression class="java.lang.String"><![CDATA[$P{SUBREPORT_DIR} + "NFCe_Produtos.jasper"]]></subreportExpression>
			</subreport>
			<frame>
				<reportElement positionType="Float" x="4" y="145" width="584" height="41"/>
				<rectangle>
					<reportElement x="1" y="1" width="581" height="39"/>
					<graphicElement>
						<pen lineWidth="0.5"/>
					</graphicElement>
				</rectangle>
				<staticText>
					<reportElement key="staticText-16" x="393" y="3" width="101" height="14"/>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font isBold="true" pdfFontName="Helvetica"/>
					</textElement>
					<text><![CDATA[VALOR TOTAL:]]></text>
				</staticText>
				<textField>
					<reportElement key="staticText-16" x="495" y="3" width="82" height="14"/>
					<textElement textAlignment="Right" verticalAlignment="Middle" markup="none">
						<font isBold="true" pdfFontName="Helvetica"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$F{valorTotal}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement key="staticText-16" x="369" y="20" width="125" height="14"/>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font isBold="true" isUnderline="false" pdfFontName="Helvetica"/>
					</textElement>
					<text><![CDATA[Forma de Pagamento]]></text>
				</staticText>
				<staticText>
					<reportElement key="staticText-16" x="495" y="20" width="82" height="14"/>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font isBold="true" isUnderline="false" pdfFontName="Helvetica"/>
					</textElement>
					<text><![CDATA[Valor Pago]]></text>
				</staticText>
				<textField>
					<reportElement key="staticText-16" x="9" y="0" width="199" height="14"/>
					<textElement verticalAlignment="Middle" markup="none">
						<font isBold="false" pdfFontName="Helvetica"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA["Quantidade Total de Itens: " + $F{qtdProdutos}]]></textFieldExpression>
				</textField>
			</frame>
			<subreport isUsingCache="false">
				<reportElement positionType="Float" x="5" y="185" width="581" height="5"/>
				<dataSourceExpression><![CDATA[$F{pagamentosJr}]]></dataSourceExpression>
				<subreportExpression class="java.lang.String"><![CDATA[$P{SUBREPORT_DIR} + "NFCe_Pagamentos.jasper"]]></subreportExpression>
			</subreport>
			<frame>
				<reportElement positionType="Float" x="4" y="190" width="584" height="87"/>
				<rectangle>
					<reportElement positionType="Float" x="1" y="1" width="581" height="84"/>
					<graphicElement>
						<pen lineWidth="0.5"/>
					</graphicElement>
				</rectangle>
				<staticText>
					<reportElement key="staticText-16" positionType="Float" x="1" y="3" width="581" height="14"/>
					<textElement textAlignment="Center">
						<font isBold="true" pdfFontName="Helvetica"/>
					</textElement>
					<text><![CDATA[ÁREA DE MENSAGEM FISCAL]]></text>
				</staticText>
				<textField>
					<reportElement key="staticText-16" positionType="Float" x="1" y="18" width="581" height="14"/>
					<textElement textAlignment="Center" markup="none">
						<font isBold="false" pdfFontName="Helvetica"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$F{informacoesNota}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement key="staticText-16" positionType="Float" x="1" y="35" width="581" height="14"/>
					<textElement textAlignment="Center">
						<font isBold="false" pdfFontName="Helvetica"/>
					</textElement>
					<text><![CDATA[Consulte pela Chave de Acesso em http://dec.fazenda.df.gov.br/nfce]]></text>
				</staticText>
				<staticText>
					<reportElement key="staticText-16" positionType="Float" x="1" y="51" width="581" height="14"/>
					<textElement textAlignment="Center">
						<font isBold="true" pdfFontName="Helvetica"/>
					</textElement>
					<text><![CDATA[CHAVE DE ACESSO]]></text>
				</staticText>
				<textField>
					<reportElement key="staticText-16" positionType="Float" x="1" y="66" width="581" height="14"/>
					<textElement textAlignment="Center" markup="none">
						<font isBold="false" pdfFontName="Helvetica"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$F{chaveAcesso}]]></textFieldExpression>
				</textField>
			</frame>
			<frame>
				<reportElement positionType="Float" x="4" y="273" width="584" height="62"/>
				<rectangle>
					<reportElement positionType="Float" x="1" y="1" width="581" height="61"/>
					<graphicElement>
						<pen lineWidth="0.5"/>
					</graphicElement>
				</rectangle>
				<staticText>
					<reportElement key="staticText-16" positionType="Float" x="1" y="3" width="581" height="14"/>
					<textElement textAlignment="Center">
						<font isBold="true" pdfFontName="Helvetica"/>
					</textElement>
					<text><![CDATA[OBSERVAÇÕES]]></text>
				</staticText>
				<textField>
					<reportElement key="staticText-16" positionType="Float" x="1" y="18" width="581" height="42"/>
					<textElement textAlignment="Left" markup="none">
						<font isBold="false" pdfFontName="Helvetica"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$F{observacoes}]]></textFieldExpression>
				</textField>
			</frame>
			<frame>
				<reportElement positionType="Float" stretchType="RelativeToTallestObject" x="4" y="333" width="584" height="73"/>
				<rectangle>
					<reportElement x="1" y="2" width="581" height="71"/>
					<graphicElement>
						<pen lineWidth="0.5"/>
					</graphicElement>
				</rectangle>
				<staticText>
					<reportElement key="staticText-16" x="1" y="3" width="581" height="14"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font isBold="true" pdfFontName="Helvetica"/>
					</textElement>
					<text><![CDATA[CONSUMIDOR]]></text>
				</staticText>
				<textField>
					<reportElement key="staticText-16" x="1" y="54" width="581" height="14"/>
					<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
						<font isBold="false" pdfFontName="Helvetica"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$F{enderecoConsumidor}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="staticText-16" x="1" y="37" width="581" height="14"/>
					<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
						<font isBold="false" pdfFontName="Helvetica"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$F{nomeConsumidor}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="staticText-16" x="1" y="20" width="581" height="14"/>
					<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
						<font isBold="false" pdfFontName="Helvetica"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$F{cpfConsumidor}]]></textFieldExpression>
				</textField>
			</frame>
			<frame>
				<reportElement positionType="Float" x="4" y="403" width="584" height="266"/>
				<rectangle>
					<reportElement positionType="Float" x="1" y="3" width="581" height="233"/>
					<graphicElement>
						<pen lineWidth="0.5"/>
					</graphicElement>
				</rectangle>
				<staticText>
					<reportElement key="staticText-16" x="1" y="10" width="581" height="14"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font isBold="false" pdfFontName="Helvetica"/>
					</textElement>
					<text><![CDATA[Consulta via leitor de QR Code]]></text>
				</staticText>
				<image scaleImage="RealSize" isUsingCache="true" onErrorType="Blank" evaluationTime="Report">
					<reportElement key="image-1" isPrintRepeatedValues="false" x="206" y="30" width="182" height="184" isPrintWhenDetailOverflows="true"/>
					<box>
						<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
						<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
						<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
						<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					</box>
					<imageExpression class="java.io.InputStream"><![CDATA[$F{qrCode}]]></imageExpression>
				</image>
				<textField>
					<reportElement key="staticText-16" x="1" y="218" width="581" height="14"/>
					<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
						<font isBold="false" pdfFontName="Helvetica"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$F{protocolo}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement key="staticText-16" x="1" y="237" width="145" height="13"/>
					<textElement verticalAlignment="Middle">
						<font size="9" isBold="false" pdfFontName="Helvetica"/>
					</textElement>
					<text><![CDATA[DATA E HORA DA IMPRESSÃO:]]></text>
				</staticText>
				<textField pattern="dd/MM/yyyy HH:mm:ss" isBlankWhenNull="true">
					<reportElement key="dataRel-1" mode="Transparent" x="146" y="237" width="128" height="13" backcolor="#FFFFFF"/>
					<box bottomPadding="2">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					</box>
					<textElement verticalAlignment="Bottom">
						<font fontName="Arial" size="9" isBold="false" isItalic="true" isUnderline="false" pdfFontName="Helvetica-Oblique" isPdfEmbedded="false"/>
					</textElement>
					<textFieldExpression class="java.util.Date"><![CDATA[new Date()]]></textFieldExpression>
				</textField>
			</frame>
			<rectangle>
				<reportElement x="330" y="114" width="55" height="16"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<staticText>
				<reportElement key="staticText-16" x="330" y="114" width="55" height="16"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="8" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[COFINS]]></text>
			</staticText>
		</band>
	</detail>
</jasperReport>

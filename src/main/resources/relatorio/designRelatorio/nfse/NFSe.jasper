¬í sr (net.sf.jasperreports.engine.JasperReport      'Ø L compileDatat Ljava/io/Serializable;L compileNameSuffixt Ljava/lang/String;L 
compilerClassq ~ xr -net.sf.jasperreports.engine.base.JRBaseReport      'Ø ,I PSEUDO_SERIAL_VERSION_UIDI bottomMarginI columnCountI 
columnSpacingI columnWidthZ ignorePaginationZ isFloatColumnFooterZ isSummaryNewPageZ  isSummaryWithPageHeaderAndFooterZ isTitleNewPageI 
leftMarginB orientationI 
pageHeightI 	pageWidthB 
printOrderI rightMarginI 	topMarginB whenNoDataTypeL 
backgroundt $Lnet/sf/jasperreports/engine/JRBand;L columnDirectiont 3Lnet/sf/jasperreports/engine/type/RunDirectionEnum;L columnFooterq ~ L columnHeaderq ~ [ datasetst ([Lnet/sf/jasperreports/engine/JRDataset;L defaultFontt *Lnet/sf/jasperreports/engine/JRReportFont;L defaultStylet %Lnet/sf/jasperreports/engine/JRStyle;L detailq ~ L 
detailSectiont 'Lnet/sf/jasperreports/engine/JRSection;[ fontst +[Lnet/sf/jasperreports/engine/JRReportFont;L formatFactoryClassq ~ L 
importsSett Ljava/util/Set;L languageq ~ L lastPageFooterq ~ L mainDatasett 'Lnet/sf/jasperreports/engine/JRDataset;L nameq ~ L noDataq ~ L orientationValuet 2Lnet/sf/jasperreports/engine/type/OrientationEnum;L 
pageFooterq ~ L 
pageHeaderq ~ L printOrderValuet 1Lnet/sf/jasperreports/engine/type/PrintOrderEnum;[ stylest &[Lnet/sf/jasperreports/engine/JRStyle;L summaryq ~ [ 	templatest /[Lnet/sf/jasperreports/engine/JRReportTemplate;L titleq ~ L whenNoDataTypeValuet 5Lnet/sf/jasperreports/engine/type/WhenNoDataTypeEnum;xp  wñ            O           J  S        sr +net.sf.jasperreports.engine.base.JRBaseBand      'Ø I PSEUDO_SERIAL_VERSION_UIDI heightZ isSplitAllowedL printWhenExpressiont *Lnet/sf/jasperreports/engine/JRExpression;L 	splitTypet Ljava/lang/Byte;L splitTypeValuet 0Lnet/sf/jasperreports/engine/type/SplitTypeEnum;xr 3net.sf.jasperreports.engine.base.JRBaseElementGroup      'Ø L childrent Ljava/util/List;L elementGroupt ,Lnet/sf/jasperreports/engine/JRElementGroup;xpsr java.util.ArrayListxÒÇa I sizexp    w    xp  wñ    pp~r .net.sf.jasperreports.engine.type.SplitTypeEnum          xr java.lang.Enum          xpt STRETCH~r 1net.sf.jasperreports.engine.type.RunDirectionEnum          xq ~ t LTRsq ~ sq ~     w    xp  wñ    ppq ~ sq ~ sq ~     w    xp  wñ    ppq ~ ppppsr .net.sf.jasperreports.engine.base.JRBaseSection      'Ø [ bandst %[Lnet/sf/jasperreports/engine/JRBand;xppppsr java.util.HashSetºD¸·4  xpw   ?@     t "net.sf.jasperreports.engine.data.*t net.sf.jasperreports.engine.*t java.util.*xt javapsr .net.sf.jasperreports.engine.base.JRBaseDataset      'Ø I PSEUDO_SERIAL_VERSION_UIDZ isMainB whenResourceMissingType[ fieldst &[Lnet/sf/jasperreports/engine/JRField;L filterExpressionq ~ [ groupst &[Lnet/sf/jasperreports/engine/JRGroup;L nameq ~ [ 
parameterst *[Lnet/sf/jasperreports/engine/JRParameter;L 
propertiesMapt -Lnet/sf/jasperreports/engine/JRPropertiesMap;L queryt %Lnet/sf/jasperreports/engine/JRQuery;L resourceBundleq ~ L scriptletClassq ~ [ 
scriptletst *[Lnet/sf/jasperreports/engine/JRScriptlet;[ 
sortFieldst *[Lnet/sf/jasperreports/engine/JRSortField;[ 	variablest )[Lnet/sf/jasperreports/engine/JRVariable;L whenResourceMissingTypeValuet >Lnet/sf/jasperreports/engine/type/WhenResourceMissingTypeEnum;xp  wñ ur &[Lnet.sf.jasperreports.engine.JRField;<ßÇN*òp  xp   8sr ,net.sf.jasperreports.engine.base.JRBaseField      'Ø L descriptionq ~ L nameq ~ L 
propertiesMapq ~ 5L valueClassNameq ~ L valueClassRealNameq ~ xppt razaoSocialPrestadorsr +net.sf.jasperreports.engine.JRPropertiesMap      'Ø L baseq ~ 5L propertiesListq ~ L 
propertiesMapt Ljava/util/Map;xppppt java.lang.Stringpsq ~ >pt 
cnpjPrestadorsq ~ Apppt java.lang.Stringpsq ~ >pt inscricaoMunicipalPrestadorsq ~ Apppt java.lang.Stringpsq ~ >pt inscricaoEstadualPrestadorsq ~ Apppt java.lang.Stringpsq ~ >pt enderecoPrestadorsq ~ Apppt java.lang.Stringpsq ~ >pt complementoPrestadorsq ~ Apppt java.lang.Stringpsq ~ >pt municipioPrestadorsq ~ Apppt java.lang.Stringpsq ~ >pt ufPrestadorsq ~ Apppt java.lang.Stringpsq ~ >pt telefonePrestadorsq ~ Apppt java.lang.Stringpsq ~ >pt emailPrestadorsq ~ Apppt java.lang.Stringpsq ~ >pt logomarcaPrestadorsq ~ Apppt java.io.InputStreampsq ~ >pt razaoSocialTomadorsq ~ Apppt java.lang.Stringpsq ~ >pt cnpjTomadorsq ~ Apppt java.lang.Stringpsq ~ >pt inscricaoMunicipalTomadorsq ~ Apppt java.lang.Stringpsq ~ >pt inscricaoEstadualTomadorsq ~ Apppt java.lang.Stringpsq ~ >pt enderecoTomadorsq ~ Apppt java.lang.Stringpsq ~ >pt complementoTomadorsq ~ Apppt java.lang.Stringpsq ~ >pt municipioTomadorsq ~ Apppt java.lang.Stringpsq ~ >pt 	ufTomadorsq ~ Apppt java.lang.Stringpsq ~ >pt telefoneTomadorsq ~ Apppt java.lang.Stringpsq ~ >pt emailTomadorsq ~ Apppt java.lang.Stringpsq ~ >pt logomarcaPrefeiturasq ~ Apppt java.io.InputStreampsq ~ >pt cidadePrestacaosq ~ Apppt java.lang.Stringpsq ~ >pt 
numeroNotasq ~ Apppt java.lang.Stringpsq ~ >pt dataServicosq ~ Apppt java.lang.Stringpsq ~ >pt dataHoraEmissaosq ~ Apppt java.lang.Stringpsq ~ >pt codigoAutorizacaosq ~ Apppt java.lang.Stringpsq ~ >pt dataCompetenciasq ~ Apppt java.lang.Stringpsq ~ >pt 	numeroRPSsq ~ Apppt java.lang.Stringpsq ~ >pt municipioPrestacaosq ~ Apppt java.lang.Stringpsq ~ >pt servicosDescricaosq ~ Apppt java.lang.Stringpsq ~ >pt 
codigoServicosq ~ Apppt java.lang.Stringpsq ~ >pt valorPISsq ~ Apppt java.lang.Stringpsq ~ >pt valorCOFINSsq ~ Apppt java.lang.Stringpsq ~ >pt valorIRsq ~ Apppt java.lang.Stringpsq ~ >pt 	valorINSSsq ~ Apppt java.lang.Stringpsq ~ >pt 	valorCSLLsq ~ Apppt java.lang.Stringpsq ~ >pt 
valorServicossq ~ Apppt java.lang.Stringpsq ~ >pt descontoIncondicionadosq ~ Apppt java.lang.Stringpsq ~ >pt descontoCondicionadosq ~ Apppt java.lang.Stringpsq ~ >pt retencoesFederaissq ~ Apppt java.lang.Stringpsq ~ >pt outrasRetencoessq ~ Apppt java.lang.Stringpsq ~ >pt issRetidoValorsq ~ Apppt java.lang.Stringpsq ~ >pt valorLiquidosq ~ Apppt java.lang.Stringpsq ~ >pt naturezaOperacaosq ~ Apppt java.lang.Stringpsq ~ >pt regimeEspecialsq ~ Apppt java.lang.Stringpsq ~ >pt simplesNacionalsq ~ Apppt java.lang.Stringpsq ~ >pt incentivadorCulturalsq ~ Apppt java.lang.Stringpsq ~ >pt deducoesPermitidassq ~ Apppt java.lang.Stringpsq ~ >pt descontoIncondicionadoMunicipiosq ~ Apppt java.lang.Stringpsq ~ >pt baseCalculosq ~ Apppt java.lang.Stringpsq ~ >pt aliquotasq ~ Apppt java.lang.Stringpsq ~ >pt reterISSsq ~ Apppt java.lang.Stringpsq ~ >pt valorISSsq ~ Apppt java.lang.Stringpsq ~ >pt valorTotalNotasq ~ Apppt java.lang.Stringpsq ~ >pt outrasInformacoessq ~ Apppt java.lang.Stringpppt 
NotaManualur *[Lnet.sf.jasperreports.engine.JRParameter;" *Ã`!  xp   sr 0net.sf.jasperreports.engine.base.JRBaseParameter      'Ø 	Z isForPromptingZ isSystemDefinedL defaultValueExpressionq ~ L descriptionq ~ L nameq ~ L nestedTypeNameq ~ L 
propertiesMapq ~ 5L valueClassNameq ~ L valueClassRealNameq ~ xpppt REPORT_PARAMETERS_MAPpsq ~ Apppt 
java.util.Mappsq ~$ppt 
JASPER_REPORTpsq ~ Apppt (net.sf.jasperreports.engine.JasperReportpsq ~$ppt REPORT_CONNECTIONpsq ~ Apppt java.sql.Connectionpsq ~$ppt REPORT_MAX_COUNTpsq ~ Apppt java.lang.Integerpsq ~$ppt REPORT_DATA_SOURCEpsq ~ Apppt (net.sf.jasperreports.engine.JRDataSourcepsq ~$ppt REPORT_SCRIPTLETpsq ~ Apppt /net.sf.jasperreports.engine.JRAbstractScriptletpsq ~$ppt 
REPORT_LOCALEpsq ~ Apppt java.util.Localepsq ~$ppt REPORT_RESOURCE_BUNDLEpsq ~ Apppt java.util.ResourceBundlepsq ~$ppt REPORT_TIME_ZONEpsq ~ Apppt java.util.TimeZonepsq ~$ppt REPORT_FORMAT_FACTORYpsq ~ Apppt .net.sf.jasperreports.engine.util.FormatFactorypsq ~$ppt REPORT_CLASS_LOADERpsq ~ Apppt java.lang.ClassLoaderpsq ~$ppt REPORT_URL_HANDLER_FACTORYpsq ~ Apppt  java.net.URLStreamHandlerFactorypsq ~$ppt REPORT_FILE_RESOLVERpsq ~ Apppt -net.sf.jasperreports.engine.util.FileResolverpsq ~$ppt REPORT_TEMPLATESpsq ~ Apppt java.util.Collectionpsq ~$ppt SORT_FIELDSpsq ~ Apppt java.util.Listpsq ~$ppt REPORT_VIRTUALIZERpsq ~ Apppt )net.sf.jasperreports.engine.JRVirtualizerpsq ~$ppt IS_IGNORE_PAGINATIONpsq ~ Apppt java.lang.Booleanpsq ~ Apsq ~    w   t ireport.zoomt 	ireport.xt 	ireport.yt ireport.scriptlethandlingt ireport.encodingxsr java.util.HashMapÚÁÃ`Ñ F 
loadFactorI 	thresholdxp?@     w      q ~kt 1.5q ~ot 
ISO-8859-1q ~lt 0q ~mt 240q ~nt 0xsr ,net.sf.jasperreports.engine.base.JRBaseQuery      'Ø [ chunkst +[Lnet/sf/jasperreports/engine/JRQueryChunk;L languageq ~ xppt sqlppppur )[Lnet.sf.jasperreports.engine.JRVariable;bæ|,·D  xp   sr /net.sf.jasperreports.engine.base.JRBaseVariable      'Ø I PSEUDO_SERIAL_VERSION_UIDB calculationB 
incrementTypeZ isSystemDefinedB 	resetTypeL calculationValuet 2Lnet/sf/jasperreports/engine/type/CalculationEnum;L 
expressionq ~ L incrementGroupt %Lnet/sf/jasperreports/engine/JRGroup;L incrementTypeValuet 4Lnet/sf/jasperreports/engine/type/IncrementTypeEnum;L incrementerFactoryClassNameq ~ L incrementerFactoryClassRealNameq ~ L initialValueExpressionq ~ L nameq ~ L 
resetGroupq ~L resetTypeValuet 0Lnet/sf/jasperreports/engine/type/ResetTypeEnum;L valueClassNameq ~ L valueClassRealNameq ~ xp  wî   ~r 0net.sf.jasperreports.engine.type.CalculationEnum          xq ~ t SYSTEMpp~r 2net.sf.jasperreports.engine.type.IncrementTypeEnum          xq ~ t NONEppsr 1net.sf.jasperreports.engine.base.JRBaseExpression      'Ø I id[ chunkst 0[Lnet/sf/jasperreports/engine/JRExpressionChunk;L valueClassNameq ~ L valueClassRealNameq ~ xp    ur 0[Lnet.sf.jasperreports.engine.JRExpressionChunk;mYÏÞiK£U  xp   sr 6net.sf.jasperreports.engine.base.JRBaseExpressionChunk      'Ø B typeL textq ~ xpt new java.lang.Integer(1)q ~4pt PAGE_NUMBERp~r .net.sf.jasperreports.engine.type.ResetTypeEnum          xq ~ t REPORTq ~4psq ~}  wî   q ~ppq ~ppsq ~   uq ~   sq ~t new java.lang.Integer(1)q ~4pt 
COLUMN_NUMBERp~q ~t PAGEq ~4psq ~}  wî   ~q ~t COUNTsq ~   uq ~   sq ~t new java.lang.Integer(1)q ~4ppq ~ppsq ~   uq ~   sq ~t new java.lang.Integer(0)q ~4pt REPORT_COUNTpq ~q ~4psq ~}  wî   q ~sq ~   uq ~   sq ~t new java.lang.Integer(1)q ~4ppq ~ppsq ~   uq ~   sq ~t new java.lang.Integer(0)q ~4pt 
PAGE_COUNTpq ~q ~4psq ~}  wî   q ~sq ~   uq ~   sq ~t new java.lang.Integer(1)q ~4ppq ~ppsq ~   uq ~   sq ~t new java.lang.Integer(0)q ~4pt COLUMN_COUNTp~q ~t COLUMNq ~4p~r <net.sf.jasperreports.engine.type.WhenResourceMissingTypeEnum          xq ~ t EMPTYq ~!p~r 0net.sf.jasperreports.engine.type.OrientationEnum          xq ~ t PORTRAITsq ~ sq ~    w   sr 0net.sf.jasperreports.engine.base.JRBaseTextField      'Ø I PSEUDO_SERIAL_VERSION_UIDI 
bookmarkLevelB evaluationTimeB hyperlinkTargetB 
hyperlinkTypeZ isStretchWithOverflowL anchorNameExpressionq ~ L evaluationGroupq ~L evaluationTimeValuet 5Lnet/sf/jasperreports/engine/type/EvaluationTimeEnum;L 
expressionq ~ L hyperlinkAnchorExpressionq ~ L hyperlinkPageExpressionq ~ [ hyperlinkParameterst 3[Lnet/sf/jasperreports/engine/JRHyperlinkParameter;L hyperlinkReferenceExpressionq ~ L hyperlinkTooltipExpressionq ~ L isBlankWhenNullt Ljava/lang/Boolean;L 
linkTargetq ~ L linkTypeq ~ L patternq ~ xr 2net.sf.jasperreports.engine.base.JRBaseTextElement      'Ø %I PSEUDO_SERIAL_VERSION_UIDL borderq ~ L borderColort Ljava/awt/Color;L bottomBorderq ~ L bottomBorderColorq ~ÌL 
bottomPaddingt Ljava/lang/Integer;L fontNameq ~ L fontSizeq ~ÍL horizontalAlignmentq ~ L horizontalAlignmentValuet 6Lnet/sf/jasperreports/engine/type/HorizontalAlignEnum;L isBoldq ~ÊL isItalicq ~ÊL 
isPdfEmbeddedq ~ÊL isStrikeThroughq ~ÊL isStyledTextq ~ÊL isUnderlineq ~ÊL 
leftBorderq ~ L leftBorderColorq ~ÌL leftPaddingq ~ÍL lineBoxt 'Lnet/sf/jasperreports/engine/JRLineBox;L lineSpacingq ~ L lineSpacingValuet 2Lnet/sf/jasperreports/engine/type/LineSpacingEnum;L markupq ~ L paddingq ~ÍL pdfEncodingq ~ L pdfFontNameq ~ L 
reportFontq ~ L rightBorderq ~ L rightBorderColorq ~ÌL rightPaddingq ~ÍL rotationq ~ L 
rotationValuet /Lnet/sf/jasperreports/engine/type/RotationEnum;L 	topBorderq ~ L topBorderColorq ~ÌL 
topPaddingq ~ÍL verticalAlignmentq ~ L verticalAlignmentValuet 4Lnet/sf/jasperreports/engine/type/VerticalAlignEnum;xr .net.sf.jasperreports.engine.base.JRBaseElement      'Ø I PSEUDO_SERIAL_VERSION_UIDI heightZ isPrintInFirstWholeBandZ isPrintRepeatedValuesZ isPrintWhenDetailOverflowsZ isRemoveLineWhenBlankB positionTypeB stretchTypeI widthI xI yL 	backcolorq ~ÌL defaultStyleProvidert 4Lnet/sf/jasperreports/engine/JRDefaultStyleProvider;L elementGroupq ~ L 	forecolorq ~ÌL keyq ~ L modeq ~ L 	modeValuet +Lnet/sf/jasperreports/engine/type/ModeEnum;L parentStyleq ~ L parentStyleNameReferenceq ~ L positionTypeValuet 3Lnet/sf/jasperreports/engine/type/PositionTypeEnum;L printWhenExpressionq ~ L printWhenGroupChangesq ~L 
propertiesMapq ~ 5[ propertyExpressionst 3[Lnet/sf/jasperreports/engine/JRPropertyExpression;L stretchTypeValuet 2Lnet/sf/jasperreports/engine/type/StretchTypeEnum;xp  wñ   
               sr java.awt.Color¥3u F falphaI valueL cst Ljava/awt/color/ColorSpace;[ 	frgbvaluet [F[ fvalueq ~Üxp    ÿÿÿÿpppq ~ q ~Åpt 	dataRel-1p~r )net.sf.jasperreports.engine.type.ModeEnum          xq ~ t OPAQUEpp~r 1net.sf.jasperreports.engine.type.PositionTypeEnum          xq ~ t FIX_RELATIVE_TO_TOPpppp~r 0net.sf.jasperreports.engine.type.StretchTypeEnum          xq ~ t 
NO_STRETCH  wñpppppt Arialsr java.lang.Integerâ ¤÷8 I valuexr java.lang.Number¬à  xp   p~r 4net.sf.jasperreports.engine.type.HorizontalAlignEnum          xq ~ t LEFTsr java.lang.BooleanÍ rÕúî Z valuexp sq ~ïq ~ðppq ~ðpppsr .net.sf.jasperreports.engine.base.JRBaseLineBox      'Ø L 
bottomPaddingq ~ÍL 	bottomPent +Lnet/sf/jasperreports/engine/base/JRBoxPen;L boxContainert ,Lnet/sf/jasperreports/engine/JRBoxContainer;L leftPaddingq ~ÍL leftPenq ~óL paddingq ~ÍL penq ~óL rightPaddingq ~ÍL rightPenq ~óL 
topPaddingq ~ÍL topPenq ~óxpsq ~é   sr 3net.sf.jasperreports.engine.base.JRBaseBoxBottomPen      'Ø  xr -net.sf.jasperreports.engine.base.JRBaseBoxPen      'Ø L lineBoxq ~Ïxr *net.sf.jasperreports.engine.base.JRBasePen      'Ø I PSEUDO_SERIAL_VERSION_UIDL 	lineColorq ~ÌL 	lineStyleq ~ L lineStyleValuet 0Lnet/sf/jasperreports/engine/type/LineStyleEnum;L 	lineWidtht Ljava/lang/Float;L penContainert ,Lnet/sf/jasperreports/engine/JRPenContainer;xp  wñsq ~Ú    ÿfffpppp~r .net.sf.jasperreports.engine.type.LineStyleEnum          xq ~ t SOLIDsr java.lang.FloatÚíÉ¢Û<ðì F valuexq ~ê    q ~õq ~õq ~Ùpsr 1net.sf.jasperreports.engine.base.JRBaseBoxLeftPen      'Ø  xq ~ø  wñsq ~Ú    ÿfffppppq ~ sq ~    q ~õq ~õpsq ~ø  wñppppq ~õq ~õpsr 2net.sf.jasperreports.engine.base.JRBaseBoxRightPen      'Ø  xq ~ø  wñsq ~Ú    ÿfffppppq ~ sq ~    q ~õq ~õpsr 0net.sf.jasperreports.engine.base.JRBaseBoxTopPen      'Ø  xq ~ø  wñsq ~Ú    ÿfffppppq ~ sq ~    q ~õq ~õpppppt Helvetica-Obliquepppppppppp~r 2net.sf.jasperreports.engine.type.VerticalAlignEnum          xq ~ t MIDDLE  wñ        pp~r 3net.sf.jasperreports.engine.type.EvaluationTimeEnum          xq ~ t NOWsq ~   ?uq ~   sq ~t 
new Date()t java.util.Dateppppppq ~ñppt dd/MM/yyyy HH:mm:sssr 1net.sf.jasperreports.engine.base.JRBaseStaticText      'Ø L textq ~ xq ~Ë  wñ   
        ~       pq ~ q ~Åpt 
staticText-16ppppq ~ãppppq ~æ  wñppppppq ~ëppq ~ðppppppppsq ~òpsq ~÷  wñppppq ~!q ~!q ~psq ~  wñppppq ~!q ~!psq ~ø  wñppppq ~!q ~!psq ~	  wñppppq ~!q ~!psq ~
  wñppppq ~!q ~!pppppt 	Helveticappppppppppq ~t DATA E HORA DA IMPRESSÃO:xp  wñ   ppq ~ sq ~ sq ~    w   sr 0net.sf.jasperreports.engine.base.JRBaseRectangle      'Ø L radiusq ~Íxr 5net.sf.jasperreports.engine.base.JRBaseGraphicElement      'Ø I PSEUDO_SERIAL_VERSION_UIDL fillq ~ L 	fillValuet +Lnet/sf/jasperreports/engine/type/FillEnum;L linePent #Lnet/sf/jasperreports/engine/JRPen;L penq ~ xq ~Ó  wñ   h       E      pq ~ q ~)ppppppq ~ãppppq ~æ  wîppsq ~ù  wñpppsq ~?   q ~/ppsq ~+  wñ   h        Ï  |   pq ~ q ~)ppppppq ~ãppppq ~æ  wîppsq ~ù  wñpppsq ~?   q ~2ppsq ~+  wñ   #        Ï  |   pq ~ q ~)ppppppq ~ãppppq ~æ  wîppsq ~ù  wñpppsq ~?   q ~5ppsq ~+  wñ   #        Ï  |   Qpq ~ q ~)ppppppq ~ãppppq ~æ  wîppsq ~ù  wñpppsq ~?   q ~8ppsq ~+  wñ   #       E      tpq ~ q ~)ppppppq ~ãppppq ~æ  wîppsq ~ù  wñpppsq ~?   q ~;ppsq ~+  wñ   #        a      tpq ~ q ~)ppppppq ~ãppppq ~æ  wîppsq ~ù  wñpppsq ~?   q ~>ppsq ~  wñ   
        E     pq ~ q ~)pt 
staticText-16ppppq ~ãppppq ~æ  wñppppppq ~ëppq ~ðppppppppsq ~òpsq ~÷  wñppppq ~Cq ~Cq ~Apsq ~  wñppppq ~Cq ~Cpsq ~ø  wñppppq ~Cq ~Cpsq ~	  wñppppq ~Cq ~Cpsq ~
  wñppppq ~Cq ~Cpppppt 	Helveticapppppppppp~q ~t TOPt NÃºmero da Notasq ~  wñ   
        B     pq ~ q ~)pt 
staticText-16ppppq ~ãppppq ~æ  wñppppppq ~ëp~q ~ìt RIGHTq ~ðppppppppsq ~òpsq ~÷  wñppppq ~Qq ~Qq ~Mpsq ~  wñppppq ~Qq ~Qpsq ~ø  wñppppq ~Qq ~Qpsq ~	  wñppppq ~Qq ~Qpsq ~
  wñppppq ~Qq ~Qpppppt 	Helveticappppppppppq ~Jt Data do ServiÃ§osq ~Ç  wñ           í   {   "pq ~ q ~)pt 
textField-212ppppq ~ãppppq ~æ  wñpppppppp~q ~ìt CENTERq ~ñppppppppsq ~òpsq ~÷  wñppppq ~]q ~]q ~Ypsq ~  wñppppq ~]q ~]psq ~ø  wñppppq ~]q ~]psq ~	  wñppppq ~]q ~]psq ~
  wñppppq ~]q ~]pppppt Helvetica-Boldppppppppppq ~  wñ        ppq ~sq ~   uq ~   sq ~t "Prefeitura de " + sq ~t cidadePrestacaot java.lang.Stringppppppq ~ðpppsq ~  wñ   
        i     2pq ~ q ~)pt 
staticText-16ppppq ~ãppppq ~æ  wñppppppq ~ëppq ~ðppppppppsq ~òpsq ~÷  wñppppq ~mq ~mq ~kpsq ~  wñppppq ~mq ~mpsq ~ø  wñppppq ~mq ~mpsq ~	  wñppppq ~mq ~mpsq ~
  wñppppq ~mq ~mpppppt 	Helveticappppppppppq ~Jt Data e Hora da EmissÃ£osq ~  wñ   
        i     Tpq ~ q ~)pt 
staticText-16ppppq ~ãppppq ~æ  wñppppppq ~ëppq ~ðppppppppsq ~òpsq ~÷  wñppppq ~wq ~wq ~upsq ~  wñppppq ~wq ~wpsq ~ø  wñppppq ~wq ~wpsq ~	  wñppppq ~wq ~wpsq ~
  wñppppq ~wq ~wpppppt 	Helveticappppppppppq ~Jt CÃ³digo de VerificaÃ§Ã£osq ~  wñ           [   	   vpq ~ q ~)pt 
staticText-16ppppq ~ãppppq ~æ  wñppppppq ~ëppq ~ðppppppppsq ~òpsq ~÷  wñppppq ~q ~q ~psq ~  wñppppq ~q ~psq ~ø  wñppppq ~q ~psq ~	  wñppppq ~q ~psq ~
  wñppppq ~q ~pppppt 	Helveticappppppppppq ~Jt CompetÃªnciasq ~+  wñ   #        o   g   tpq ~ q ~)ppppppq ~ãppppq ~æ  wîppsq ~ù  wñpppsq ~?   q ~ppsq ~+  wñ   #           Ö   tpq ~ q ~)ppppppq ~ãppppq ~æ  wîppsq ~ù  wñpppsq ~?   q ~ppsq ~  wñ           i   j   vpq ~ q ~)pt 
staticText-16ppppq ~ãppppq ~æ  wñppppppq ~ëppq ~ðppppppppsq ~òpsq ~÷  wñppppq ~q ~q ~psq ~  wñppppq ~q ~psq ~ø  wñppppq ~q ~psq ~	  wñppppq ~q ~psq ~
  wñppppq ~q ~pppppt 	Helveticappppppppppq ~Jt NÃºmero do RPSsq ~  wñ           z   Û   vpq ~ q ~)pt 
staticText-16ppppq ~ãppppq ~æ  wñppppppq ~ëppq ~ðppppppppsq ~òpsq ~÷  wñppppq ~q ~q ~psq ~  wñppppq ~q ~psq ~ø  wñppppq ~q ~psq ~	  wñppppq ~q ~psq ~
  wñppppq ~q ~pppppt 	Helveticappppppppppq ~Jt NÃºmero da NFSe substituidasq ~  wñ           ê  ^   vpq ~ q ~)pt 
staticText-16ppppq ~ãppppq ~æ  wñppppppq ~ëppq ~ðppppppppsq ~òpsq ~÷  wñppppq ~¥q ~¥q ~£psq ~  wñppppq ~¥q ~¥psq ~ø  wñppppq ~¥q ~¥psq ~	  wñppppq ~¥q ~¥psq ~
  wñppppq ~¥q ~¥pppppt 	Helveticappppppppppq ~Jt %MunicÃ­pio de prestaÃ§Ã£o do serviÃ§osq ~+  wñ   _       E      pq ~ q ~)ppppppq ~ãppppq ~æ  wîppsq ~ù  wñpppsq ~?   q ~­ppsr ,net.sf.jasperreports.engine.base.JRBaseImage      'Ø *I PSEUDO_SERIAL_VERSION_UIDI 
bookmarkLevelB evaluationTimeB hyperlinkTargetB 
hyperlinkTypeZ isLazyB onErrorTypeL anchorNameExpressionq ~ L borderq ~ L borderColorq ~ÌL bottomBorderq ~ L bottomBorderColorq ~ÌL 
bottomPaddingq ~ÍL evaluationGroupq ~L evaluationTimeValueq ~ÈL 
expressionq ~ L horizontalAlignmentq ~ L horizontalAlignmentValueq ~ÎL hyperlinkAnchorExpressionq ~ L hyperlinkPageExpressionq ~ [ hyperlinkParametersq ~ÉL hyperlinkReferenceExpressionq ~ L hyperlinkTooltipExpressionq ~ L isUsingCacheq ~ÊL 
leftBorderq ~ L leftBorderColorq ~ÌL leftPaddingq ~ÍL lineBoxq ~ÏL 
linkTargetq ~ L linkTypeq ~ L onErrorTypeValuet 2Lnet/sf/jasperreports/engine/type/OnErrorTypeEnum;L paddingq ~ÍL rightBorderq ~ L rightBorderColorq ~ÌL rightPaddingq ~ÍL 
scaleImageq ~ L scaleImageValuet 1Lnet/sf/jasperreports/engine/type/ScaleImageEnum;L 	topBorderq ~ L topBorderColorq ~ÌL 
topPaddingq ~ÍL verticalAlignmentq ~ L verticalAlignmentValueq ~Òxq ~,  wñ   U       Z   
   pq ~ q ~)pt image-1ppppq ~ãppppq ~æ  wîppsq ~ù  wñppppq ~³p  wñ         ppppppp~q ~t PAGEsq ~   	uq ~   sq ~t logomarcaPrestadort java.io.InputStreamppq ~[pppppq ~ñpppsq ~òpsq ~÷  wñsq ~Ú    ÿfffppppq ~ sq ~?   q ~½q ~½q ~³psq ~  wñsq ~Ú    ÿfffppppq ~ sq ~?   q ~½q ~½psq ~ø  wñppppq ~½q ~½psq ~	  wñsq ~Ú    ÿfffppppq ~ sq ~?   q ~½q ~½psq ~
  wñsq ~Ú    ÿfffppppq ~ sq ~?   q ~½q ~½pp~r 0net.sf.jasperreports.engine.type.OnErrorTypeEnum          xq ~ t BLANKpppppppppppsq ~  wñ           S   m   «pq ~ q ~)pt 
staticText-16ppppq ~ãppppq ~æ  wñppppppq ~ëppq ~ðppppppppsq ~òpsq ~÷  wñppppq ~Ðq ~Ðq ~Îpsq ~  wñppppq ~Ðq ~Ðpsq ~ø  wñppppq ~Ðq ~Ðpsq ~	  wñppppq ~Ðq ~Ðpsq ~
  wñppppq ~Ðq ~Ðpppppt 	Helveticappppppppppq ~t Nome/RazÃ£o Social:sq ~  wñ           *   m   ºpq ~ q ~)pt 
staticText-16ppppq ~ãppppq ~æ  wñppppppq ~ëppq ~ðppppppppsq ~òpsq ~÷  wñppppq ~Úq ~Úq ~Øpsq ~  wñppppq ~Úq ~Úpsq ~ø  wñppppq ~Úq ~Úpsq ~	  wñppppq ~Úq ~Úpsq ~
  wñppppq ~Úq ~Úpppppt 	Helveticappppppppppq ~t 	CPF/CNPJ:sq ~  wñ           *   m   Épq ~ q ~)pt 
staticText-16ppppq ~ãppppq ~æ  wñppppppq ~ëppq ~ðppppppppsq ~òpsq ~÷  wñppppq ~äq ~äq ~âpsq ~  wñppppq ~äq ~äpsq ~ø  wñppppq ~äq ~äpsq ~	  wñppppq ~äq ~äpsq ~
  wñppppq ~äq ~äpppppt 	Helveticappppppppppq ~t 
EndereÃ§o:sq ~  wñ           =   m   ×pq ~ q ~)pt 
staticText-16ppppq ~ãppppq ~æ  wñppppppq ~ëppq ~ðppppppppsq ~òpsq ~÷  wñppppq ~îq ~îq ~ìpsq ~  wñppppq ~îq ~îpsq ~ø  wñppppq ~îq ~îpsq ~	  wñppppq ~îq ~îpsq ~
  wñppppq ~îq ~îpppppt 	Helveticappppppppppq ~t Complemento:sq ~  wñ           ,   m   æpq ~ q ~)pt 
staticText-16ppppq ~ãppppq ~æ  wñppppppq ~ëppq ~ðppppppppsq ~òpsq ~÷  wñppppq ~øq ~øq ~öpsq ~  wñppppq ~øq ~øpsq ~ø  wñppppq ~øq ~øpsq ~	  wñppppq ~øq ~øpsq ~
  wñppppq ~øq ~øpppppt 	Helveticappppppppppq ~t MunicÃ­pio:sq ~  wñ           K  5   ºpq ~ q ~)pt 
staticText-16ppppq ~ãppppq ~æ  wñppppppq ~ëppq ~ðppppppppsq ~òpsq ~÷  wñppppq ~q ~q ~ psq ~  wñppppq ~q ~psq ~ø  wñppppq ~q ~psq ~	  wñppppq ~q ~psq ~
  wñppppq ~q ~pppppt 	Helveticappppppppppq ~t InscriÃ§Ã£o Municipal:sq ~  wñ           '     ×pq ~ q ~)pt 
staticText-16ppppq ~ãppppq ~æ  wñppppppq ~ëppq ~ðppppppppsq ~òpsq ~÷  wñppppq ~q ~q ~
psq ~  wñppppq ~q ~psq ~ø  wñppppq ~q ~psq ~	  wñppppq ~q ~psq ~
  wñppppq ~q ~pppppt 	Helveticappppppppppq ~t 	Telefone:sq ~  wñ                æpq ~ q ~)pt 
staticText-16ppppq ~ãppppq ~æ  wñppppppq ~ëppq ~ðppppppppsq ~òpsq ~÷  wñppppq ~q ~q ~psq ~  wñppppq ~q ~psq ~ø  wñppppq ~q ~psq ~	  wñppppq ~q ~psq ~
  wñppppq ~q ~pppppt 	Helveticappppppppppq ~t UF:sq ~  wñ             6   æpq ~ q ~)pt 
staticText-16ppppq ~ãppppq ~æ  wñppppppq ~ëppq ~ðppppppppsq ~òpsq ~÷  wñppppq ~ q ~ q ~psq ~  wñppppq ~ q ~ psq ~ø  wñppppq ~ q ~ psq ~	  wñppppq ~ q ~ psq ~
  wñppppq ~ q ~ pppppt 	Helveticappppppppppq ~t E-mail:sq ~+  wñ   _       E      öpq ~ q ~)ppppppq ~ãppppq ~æ  wîppsq ~ù  wñpppsq ~?   q ~(ppsq ~  wñ           P   
  
pq ~ q ~)pt 
staticText-16ppppq ~ãppppq ~æ  wñppppppq ~ëppq ~ðppppppppsq ~òpsq ~÷  wñppppq ~-q ~-q ~+psq ~  wñppppq ~-q ~-psq ~ø  wñppppq ~-q ~-psq ~	  wñppppq ~-q ~-psq ~
  wñppppq ~-q ~-pppppt 	Helveticappppppppppq ~t Nome/RazÃ£o Social:sq ~  wñ           *   
  pq ~ q ~)pt 
staticText-16ppppq ~ãppppq ~æ  wñppppppq ~ëppq ~ðppppppppsq ~òpsq ~÷  wñppppq ~7q ~7q ~5psq ~  wñppppq ~7q ~7psq ~ø  wñppppq ~7q ~7psq ~	  wñppppq ~7q ~7psq ~
  wñppppq ~7q ~7pppppt 	Helveticappppppppppq ~t 	CPF/CNPJ:sq ~  wñ           *   
  (pq ~ q ~)pt 
staticText-16ppppq ~ãppppq ~æ  wñppppppq ~ëppq ~ðppppppppsq ~òpsq ~÷  wñppppq ~Aq ~Aq ~?psq ~  wñppppq ~Aq ~Apsq ~ø  wñppppq ~Aq ~Apsq ~	  wñppppq ~Aq ~Apsq ~
  wñppppq ~Aq ~Apppppt 	Helveticappppppppppq ~t 
EndereÃ§o:sq ~  wñ           =   
  7pq ~ q ~)pt 
staticText-16ppppq ~ãppppq ~æ  wñppppppq ~ëppq ~ðppppppppsq ~òpsq ~÷  wñppppq ~Kq ~Kq ~Ipsq ~  wñppppq ~Kq ~Kpsq ~ø  wñppppq ~Kq ~Kpsq ~	  wñppppq ~Kq ~Kpsq ~
  wñppppq ~Kq ~Kpppppt 	Helveticappppppppppq ~t Complemento:sq ~  wñ           *   
  Epq ~ q ~)pt 
staticText-16ppppq ~ãppppq ~æ  wñppppppq ~ëppq ~ðppppppppsq ~òpsq ~÷  wñppppq ~Uq ~Uq ~Spsq ~  wñppppq ~Uq ~Upsq ~ø  wñppppq ~Uq ~Upsq ~	  wñppppq ~Uq ~Upsq ~
  wñppppq ~Uq ~Upppppt 	Helveticappppppppppq ~t MunicÃ­pio:sq ~  wñ           N  >  pq ~ q ~)pt 
staticText-16ppppq ~ãppppq ~æ  wñppppppq ~ëppq ~ðppppppppsq ~òpsq ~÷  wñppppq ~_q ~_q ~]psq ~  wñppppq ~_q ~_psq ~ø  wñppppq ~_q ~_psq ~	  wñppppq ~_q ~_psq ~
  wñppppq ~_q ~_pppppt 	Helveticappppppppppq ~t InscriÃ§Ã£o Municipal:sq ~  wñ           &  >  7pq ~ q ~)pt 
staticText-16ppppq ~ãppppq ~æ  wñppppppq ~ëppq ~ðppppppppsq ~òpsq ~÷  wñppppq ~iq ~iq ~gpsq ~  wñppppq ~iq ~ipsq ~ø  wñppppq ~iq ~ipsq ~	  wñppppq ~iq ~ipsq ~
  wñppppq ~iq ~ipppppt 	Helveticappppppppppq ~t 	Telefone:sq ~  wñ              Ö  Epq ~ q ~)pt 
staticText-16ppppq ~ãppppq ~æ  wñppppppq ~ëppq ~ðppppppppsq ~òpsq ~÷  wñppppq ~sq ~sq ~qpsq ~  wñppppq ~sq ~spsq ~ø  wñppppq ~sq ~spsq ~	  wñppppq ~sq ~spsq ~
  wñppppq ~sq ~spppppt 	Helveticappppppppppq ~t UF:sq ~  wñ                Epq ~ q ~)pt 
staticText-16ppppq ~ãppppq ~æ  wñppppppq ~ëppq ~ðppppppppsq ~òpsq ~÷  wñppppq ~}q ~}q ~{psq ~  wñppppq ~}q ~}psq ~ø  wñppppq ~}q ~}psq ~	  wñppppq ~}q ~}psq ~
  wñppppq ~}q ~}pppppt 	Helveticappppppppppq ~t E-mail:sq ~+  wñ   d       E     Upq ~ q ~)ppppppq ~ãppppq ~æ  wîppsq ~ù  wñpppsq ~?   q ~ppsq ~+  wñ          E     ·pq ~ q ~)ppppppq ~ãppppq ~æ  wîppsq ~ù  wñpppsq ~?   q ~ppsq ~  wñ           M   
  »pq ~ q ~)pt 
staticText-16ppppq ~ãppppq ~æ  wñppppppq ~ëppq ~ðppppppppsq ~òpsq ~÷  wñppppq ~q ~q ~psq ~  wñppppq ~q ~psq ~ø  wñppppq ~q ~psq ~	  wñppppq ~q ~psq ~
  wñppppq ~q ~pppppt 	Helveticappppppppppq ~t CÃ³digo do ServiÃ§o:sq ~+  wñ          E     Ôpq ~ q ~)ppppppq ~ãppppq ~æ  wîppsq ~ù  wñpppsq ~?   q ~ppsq ~+  wñ   "       E     æpq ~ q ~)ppppppq ~ãppppq ~æ  wîppsq ~ù  wñpppsq ~?   q ~ppsq ~+  wñ   q       E     pq ~ q ~)ppppppq ~ãppppq ~æ  wîppsq ~ù  wñpppsq ~?   q ~ppsq ~+  wñ   "        t     æpq ~ q ~)ppppppq ~ãppppq ~æ  wîppsq ~ù  wñpppsq ~?   q ~ppsq ~+  wñ   "        t   z  æpq ~ q ~)ppppppq ~ãppppq ~æ  wîppsq ~ù  wñpppsq ~?   q ~¡ppsq ~+  wñ   "        t   ê  æpq ~ q ~)ppppppq ~ãppppq ~æ  wîppsq ~ù  wñpppsq ~?   q ~¤ppsq ~+  wñ   "        x  Ó  æpq ~ q ~)ppppppq ~ãppppq ~æ  wîppsq ~ù  wñpppsq ~?   q ~§ppsq ~  wñ   
        8   ?  épq ~ q ~)pt 
staticText-16ppppq ~ãppppq ~æ  wñppppppq ~ëpq ~Oq ~ðppppppppsq ~òpsq ~÷  wñppppq ~¬q ~¬q ~ªpsq ~  wñppppq ~¬q ~¬psq ~ø  wñppppq ~¬q ~¬psq ~	  wñppppq ~¬q ~¬psq ~
  wñppppq ~¬q ~¬pppppt 	Helveticappppppppppq ~Jt PIS (R$)sq ~  wñ   
        8   ®  épq ~ q ~)pt 
staticText-16ppppq ~ãppppq ~æ  wñppppppq ~ëpq ~Oq ~ðppppppppsq ~òpsq ~÷  wñppppq ~¶q ~¶q ~´psq ~  wñppppq ~¶q ~¶psq ~ø  wñppppq ~¶q ~¶psq ~	  wñppppq ~¶q ~¶psq ~
  wñppppq ~¶q ~¶pppppt 	Helveticappppppppppq ~Jt COFINS (R$)sq ~  wñ   
        8  #  épq ~ q ~)pt 
staticText-16ppppq ~ãppppq ~æ  wñppppppq ~ëpq ~Oq ~ðppppppppsq ~òpsq ~÷  wñppppq ~Àq ~Àq ~¾psq ~  wñppppq ~Àq ~Àpsq ~ø  wñppppq ~Àq ~Àpsq ~	  wñppppq ~Àq ~Àpsq ~
  wñppppq ~Àq ~Àpppppt 	Helveticappppppppppq ~Jt IR (R$)sq ~  wñ   
        8    épq ~ q ~)pt 
staticText-16ppppq ~ãppppq ~æ  wñppppppq ~ëpq ~Oq ~ðppppppppsq ~òpsq ~÷  wñppppq ~Êq ~Êq ~Èpsq ~  wñppppq ~Êq ~Êpsq ~ø  wñppppq ~Êq ~Êpsq ~	  wñppppq ~Êq ~Êpsq ~
  wñppppq ~Êq ~Êpppppt 	Helveticappppppppppq ~Jt 	INSS (R$)sq ~  wñ   
        8    épq ~ q ~)pt 
staticText-16ppppq ~ãppppq ~æ  wñppppppq ~ëpq ~Oq ~ðppppppppsq ~òpsq ~÷  wñppppq ~Ôq ~Ôq ~Òpsq ~  wñppppq ~Ôq ~Ôpsq ~ø  wñppppq ~Ôq ~Ôpsq ~	  wñppppq ~Ôq ~Ôpsq ~
  wñppppq ~Ôq ~Ôpppppt 	Helveticappppppppppq ~Jt 	CSLL (R$)sq ~  wñ   
        I   
  pq ~ q ~)pt 
staticText-16ppppq ~ãppppq ~æ  wñppppppq ~ëppq ~ðppppppppsq ~òpsq ~÷  wñppppq ~Þq ~Þq ~Üpsq ~  wñppppq ~Þq ~Þpsq ~ø  wñppppq ~Þq ~Þpsq ~	  wñppppq ~Þq ~Þpsq ~
  wñppppq ~Þq ~Þpppppt 	Helveticappppppppppq ~t Valor dos ServiÃ§ossq ~+  wñ   q        Ú  q  pq ~ q ~)ppppppq ~ãppppq ~æ  wîppsq ~ù  wñpppsq ~?   q ~æppsq ~+  wñ   q        ä     pq ~ q ~)ppppppq ~ãppppq ~æ  wîppsq ~ù  wñpppsq ~?   q ~éppsq ~+  wñ   !       E     ypq ~ q ~)ppppppq ~ãppppq ~æ  wîppsq ~ù  wñpppsq ~?   q ~ìppsq ~  wñ   
        r   	  -pq ~ q ~)pt 
staticText-16ppppq ~ãppppq ~æ  wñppppppq ~ëppq ~ðppppppppsq ~òpsq ~÷  wñppppq ~ñq ~ñq ~ïpsq ~  wñppppq ~ñq ~ñpsq ~ø  wñppppq ~ñq ~ñpsq ~	  wñppppq ~ñq ~ñpsq ~
  wñppppq ~ñq ~ñpppppt 	Helveticappppppppppq ~t (-) Desconto Incondicionadosq ~  wñ   
        r   
  <pq ~ q ~)pt 
staticText-16ppppq ~ãppppq ~æ  wñppppppq ~ëppq ~ðppppppppsq ~òpsq ~÷  wñppppq ~ûq ~ûq ~ùpsq ~  wñppppq ~ûq ~ûpsq ~ø  wñppppq ~ûq ~ûpsq ~	  wñppppq ~ûq ~ûpsq ~
  wñppppq ~ûq ~ûpppppt 	Helveticappppppppppq ~t (-) Desconto Condicionadosq ~  wñ   
        r   
  Kpq ~ q ~)pt 
staticText-16ppppq ~ãppppq ~æ  wñppppppq ~ëppq ~ðppppppppsq ~òpsq ~÷  wñppppq ~q ~q ~psq ~  wñppppq ~q ~psq ~ø  wñppppq ~q ~psq ~	  wñppppq ~q ~psq ~
  wñppppq ~q ~pppppt 	Helveticappppppppppq ~t (-) RetenÃ§Ãµes Federaissq ~  wñ   
        r   
  Ypq ~ q ~)pt 
staticText-16ppppq ~ãppppq ~æ  wñppppppq ~ëppq ~ðppppppppsq ~òpsq ~÷  wñppppq ~q ~q ~
psq ~  wñppppq ~q ~psq ~ø  wñppppq ~q ~psq ~	  wñppppq ~q ~psq ~
  wñppppq ~q ~pppppt 	Helveticappppppppppq ~t (-) Outras RetenÃ§Ãµessq ~  wñ   
        r   
  hpq ~ q ~)pt 
staticText-16ppppq ~ãppppq ~æ  wñppppppq ~ëppq ~ðppppppppsq ~òpsq ~÷  wñppppq ~q ~q ~psq ~  wñppppq ~q ~psq ~ø  wñppppq ~q ~psq ~	  wñppppq ~q ~psq ~
  wñppppq ~q ~pppppt 	Helveticappppppppppq ~t (-) ISS Retidosq ~  wñ   
        }   ï   pq ~ q ~)pt 
staticText-16ppppq ~ãppppq ~æ  wñppppppq ~ëpq ~[q ~ðppppppppsq ~òpsq ~÷  wñppppq ~#q ~#q ~!psq ~  wñppppq ~#q ~#psq ~ø  wñppppq ~#q ~#psq ~	  wñppppq ~#q ~#psq ~
  wñppppq ~#q ~#pppppt 	Helveticappppppppppq ~t Natureza da OperaÃ§Ã£osq ~  wñ   
        }   î  Hpq ~ q ~)pt 
staticText-16ppppq ~ãppppq ~æ  wñppppppq ~ëpq ~[q ~ðppppppppsq ~òpsq ~÷  wñppppq ~-q ~-q ~+psq ~  wñppppq ~-q ~-psq ~ø  wñppppq ~-q ~-psq ~	  wñppppq ~-q ~-psq ~
  wñppppq ~-q ~-pppppt 	Helveticappppppppppq ~t Regime Especial de TributaÃ§Ã£osq ~+  wñ   !        ä     ypq ~ q ~)ppppppq ~ãppppq ~æ  wîppsq ~ù  wñpppsq ~?   q ~5ppsq ~+  wñ   !        Ú  q  ypq ~ q ~)ppppppq ~ãppppq ~æ  wîppsq ~ù  wñpppsq ~?   q ~8ppsq ~  wñ   
        a   ì  |pq ~ q ~)pt 
staticText-16ppppq ~ãppppq ~æ  wñppppppq ~ëppq ~ðppppppppsq ~òpsq ~÷  wñppppq ~=q ~=q ~;psq ~  wñppppq ~=q ~=psq ~ø  wñppppq ~=q ~=psq ~	  wñppppq ~=q ~=psq ~
  wñppppq ~=q ~=pppppt 	Helveticappppppppppq ~t OpÃ§Ã£o Simples Nacionalsq ~  wñ   
        \   ì  pq ~ q ~)pt 
staticText-16ppppq ~ãppppq ~æ  wñppppppq ~ëppq ~ðppppppppsq ~òpsq ~÷  wñppppq ~Gq ~Gq ~Epsq ~  wñppppq ~Gq ~Gpsq ~ø  wñppppq ~Gq ~Gpsq ~	  wñppppq ~Gq ~Gpsq ~
  wñppppq ~Gq ~Gpppppt 	Helveticappppppppppq ~t Incentivador Culturalsq ~  wñ   
        r     pq ~ q ~)pt 
staticText-16ppppq ~ãppppq ~æ  wñppppppq ~ëppq ~ñppppppppsq ~òpsq ~÷  wñppppq ~Qq ~Qq ~Opsq ~  wñppppq ~Qq ~Qpsq ~ø  wñppppq ~Qq ~Qpsq ~	  wñppppq ~Qq ~Qpsq ~
  wñppppq ~Qq ~Qpppppt 	Helveticappppppppppq ~t (=) Valor LÃ­quidosq ~  wñ   
        r  w  pq ~ q ~)pt 
staticText-16ppppq ~ãppppq ~æ  wñppppppq ~ëppq ~ñppppppppsq ~òpsq ~÷  wñppppq ~[q ~[q ~Ypsq ~  wñppppq ~[q ~[psq ~ø  wñppppq ~[q ~[psq ~	  wñppppq ~[q ~[psq ~
  wñppppq ~[q ~[pppppt 	Helveticappppppppppq ~t 
(=) Valor ISSsq ~+  wñ          E     pq ~ q ~)ppppppq ~ãppppq ~æ  wîppsq ~ù  wñpppsq ~?   q ~cppsq ~+  wñ   7       E     ´pq ~ q ~)ppppppq ~ãppppq ~æ  wîppsq ~ù  wñpppsq ~?   q ~fppsq ~  wñ   
       Û   m   pq ~ q ~)pt 
staticText-16ppppq ~ãppppq ~æ  wñppppppsq ~é   
pq ~[q ~ñppppppppsq ~òpsq ~÷  wñppppq ~lq ~lq ~ipsq ~  wñppppq ~lq ~lpsq ~ø  wñppppq ~lq ~lpsq ~	  wñppppq ~lq ~lpsq ~
  wñppppq ~lq ~lpppppt 	Helveticappppppppppq ~t PRESTADOR DE SERVIÃOSsq ~  wñ   
       E      ûpq ~ q ~)pt 
staticText-16ppppq ~ãppppq ~æ  wñppppppq ~kpq ~[q ~ñppppppppsq ~òpsq ~÷  wñppppq ~vq ~vq ~tpsq ~  wñppppq ~vq ~vpsq ~ø  wñppppq ~vq ~vpsq ~	  wñppppq ~vq ~vpsq ~
  wñppppq ~vq ~vpppppt 	Helveticappppppppppq ~t TOMADOR DE SERVIÃOSsq ~  wñ   
       E     Öpq ~ q ~)pt 
staticText-16ppppq ~ãppppq ~æ  wñppppppq ~kpq ~[q ~ñppppppppsq ~òpsq ~÷  wñppppq ~q ~q ~~psq ~  wñppppq ~q ~psq ~ø  wñppppq ~q ~psq ~	  wñppppq ~q ~psq ~
  wñppppq ~q ~pppppt 	Helveticappppppppppq ~t TRIBUTOS FEDERAISsq ~  wñ   
           ê  pq ~ q ~)pt 
staticText-16ppppq ~ãppppq ~æ  wñppppppq ~ëpq ~[q ~ñppq ~ðpppppsq ~òpsq ~÷  wñppppq ~q ~q ~psq ~  wñppppq ~q ~psq ~ø  wñppppq ~q ~psq ~	  wñppppq ~q ~psq ~
  wñppppq ~q ~pppppt 	Helveticappppppppppq ~t Outras InformaÃ§Ãµessq ~  wñ   
        ä     pq ~ q ~)pt 
staticText-16ppppq ~ãppppq ~æ  wñppppppq ~ëpq ~[q ~ñppq ~ðpppppsq ~òpsq ~÷  wñppppq ~q ~q ~psq ~  wñppppq ~q ~psq ~ø  wñppppq ~q ~psq ~	  wñppppq ~q ~psq ~
  wñppppq ~q ~pppppt 	Helveticappppppppppq ~t 1Detalhamento de Valores - Prestador dos ServiÃ§ossq ~  wñ   
        Ú  q  pq ~ q ~)pt 
staticText-16ppppq ~ãppppq ~æ  wñppppppq ~ëpq ~[q ~ñppq ~ðpppppsq ~òpsq ~÷  wñppppq ~q ~q ~psq ~  wñppppq ~q ~psq ~ø  wñppppq ~q ~psq ~	  wñppppq ~q ~psq ~
  wñppppq ~q ~pppppt 	Helveticappppppppppq ~t &CÃ¡lculo do ISSQN devido no MunicÃ­piosq ~  wñ   
        r   
  pq ~ q ~)pt 
staticText-16ppppq ~ãppppq ~æ  wñppppppq ~ëppq ~ðppppppppsq ~òpsq ~÷  wñppppq ~¨q ~¨q ~¦psq ~  wñppppq ~¨q ~¨psq ~ø  wñppppq ~¨q ~¨psq ~	  wñppppq ~¨q ~¨psq ~
  wñppppq ~¨q ~¨pppppt 	Helveticappppppppppq ~t Valor dos ServiÃ§ossq ~  wñ   
       E     Zpq ~ q ~)pt 
staticText-16ppppq ~ãppppq ~æ  wñppppppq ~kpq ~[q ~ñppppppppsq ~òpsq ~÷  wñppppq ~²q ~²q ~°psq ~  wñppppq ~²q ~²psq ~ø  wñppppq ~²q ~²psq ~	  wñppppq ~²q ~²psq ~
  wñppppq ~²q ~²pppppt 	Helveticappppppppppq ~t DISCRIMINAÃÃO DOS SERVIÃOSsq ~  wñ             m   Fpq ~ q ~)pt 
staticText-16ppppq ~ãppppq ~æ  wñppppppq ~kpq ~[q ~ñppppppppsq ~òpsq ~÷  wñppppq ~¼q ~¼q ~ºpsq ~  wñppppq ~¼q ~¼psq ~ø  wñppppq ~¼q ~¼psq ~	  wñppppq ~¼q ~¼psq ~
  wñppppq ~¼q ~¼pppppt 	Helveticappppppppppq ~t ,NOTA FISCAL ELETRÃNICA DE SERVIÃOS - NFS-esq ~  wñ   
       E     ·pq ~ q ~)pt 
staticText-16ppppq ~ãppppq ~æ  wñppppppq ~kpq ~[q ~ñppppppppsq ~òpsq ~÷  wñppppq ~Æq ~Æq ~Äpsq ~  wñppppq ~Æq ~Æpsq ~ø  wñppppq ~Æq ~Æpsq ~	  wñppppq ~Æq ~Æpsq ~
  wñppppq ~Æq ~Æpppppt 	Helveticappppppppppq ~t OUTRAS INFORMAÃÃESsq ~Ç  wñ          ;      pq ~ q ~)pt 
textField-212ppppq ~ãppppq ~æ  wñppppppppq ~[q ~ñppppppppsq ~òpsq ~÷  wñppppq ~Ðq ~Ðq ~Îpsq ~  wñppppq ~Ðq ~Ðpsq ~ø  wñppppq ~Ðq ~Ðpsq ~	  wñppppq ~Ðq ~Ðpsq ~
  wñppppq ~Ðq ~Ðpppppt Helvetica-Boldppppppppppq ~  wñ        ppq ~sq ~   
uq ~   sq ~t "VALOR TOTAL DA NOTA = " + sq ~t valorTotalNotat java.lang.Stringppppppq ~ðpppsq ~  wñ   
        r  w  pq ~ q ~)pt 
staticText-16ppppq ~ãppppq ~æ  wñppppppq ~ëppq ~ðppppppppsq ~òpsq ~÷  wñppppq ~àq ~àq ~Þpsq ~  wñppppq ~àq ~àpsq ~ø  wñppppq ~àq ~àpsq ~	  wñppppq ~àq ~àpsq ~
  wñppppq ~àq ~àpppppt 	Helveticappppppppppq ~t Valor dos ServiÃ§ossq ~  wñ   
        r  w  <pq ~ q ~)pt 
staticText-16ppppq ~ãppppq ~æ  wñppppppq ~ëppq ~ðppppppppsq ~òpsq ~÷  wñppppq ~êq ~êq ~èpsq ~  wñppppq ~êq ~êpsq ~ø  wñppppq ~êq ~êpsq ~	  wñppppq ~êq ~êpsq ~
  wñppppq ~êq ~êpppppt 	Helveticappppppppppq ~t (-) DeduÃ§Ãµessq ~  wñ   
        r  w  -pq ~ q ~)pt 
staticText-16ppppq ~ãppppq ~æ  wñppppppq ~ëppq ~ðppppppppsq ~òpsq ~÷  wñppppq ~ôq ~ôq ~òpsq ~  wñppppq ~ôq ~ôpsq ~ø  wñppppq ~ôq ~ôpsq ~	  wñppppq ~ôq ~ôpsq ~
  wñppppq ~ôq ~ôpppppt 	Helveticappppppppppq ~t (-) Desconto Incondicionadosq ~  wñ   
        r  w  Kpq ~ q ~)pt 
staticText-16ppppq ~ãppppq ~æ  wñppppppq ~ëppq ~ðppppppppsq ~òpsq ~÷  wñppppq ~þq ~þq ~üpsq ~  wñppppq ~þq ~þpsq ~ø  wñppppq ~þq ~þpsq ~	  wñppppq ~þq ~þpsq ~
  wñppppq ~þq ~þpppppt 	Helveticappppppppppq ~t (=) Base de CÃ¡lculosq ~  wñ   
        r  w  Ypq ~ q ~)pt 
staticText-16ppppq ~ãppppq ~æ  wñppppppq ~ëppq ~ðppppppppsq ~òpsq ~÷  wñppppq ~q ~q ~psq ~  wñppppq ~q ~psq ~ø  wñppppq ~q ~psq ~	  wñppppq ~q ~psq ~
  wñppppq ~q ~pppppt 	Helveticappppppppppq ~t (x) AlÃ­quota (%) sq ~  wñ   
        r  w  hpq ~ q ~)pt 
staticText-16ppppq ~ãppppq ~æ  wñppppppq ~ëppq ~ðppppppppsq ~òpsq ~÷  wñppppq ~q ~q ~psq ~  wñppppq ~q ~psq ~ø  wñppppq ~q ~psq ~	  wñppppq ~q ~psq ~
  wñppppq ~q ~pppppt 	Helveticappppppppppq ~t ISS a reter:sq ~Ç  wñ           Æ     apq ~ q ~)pt 
textField-212ppppq ~ãppppq ~æ  wñppppppq ~ëpq ~Oq ~ñppppppppsq ~òpsq ~÷  wñppppq ~q ~q ~psq ~  wñppppq ~q ~psq ~ø  wñppppq ~q ~psq ~	  wñppppq ~q ~psq ~
  wñppppq ~q ~pppppt Helvetica-Boldppppppppppq ~  wñ        ppq ~sq ~   uq ~   sq ~t codigoAutorizacaot java.lang.Stringppppppq ~ðpppsq ~Ç  wñ           Æ     ?pq ~ q ~)pt 
textField-212ppppq ~ãppppq ~æ  wñppppppq ~ëpq ~Oq ~ñppppppppsq ~òpsq ~÷  wñppppq ~*q ~*q ~(psq ~  wñppppq ~*q ~*psq ~ø  wñppppq ~*q ~*psq ~	  wñppppq ~*q ~*psq ~
  wñppppq ~*q ~*pppppt Helvetica-Boldppppppppppq ~  wñ        ppq ~sq ~   uq ~   sq ~t dataHoraEmissaot java.lang.Stringppppppq ~ðpppsq ~Ç  wñ           h     pq ~ q ~)pt 
textField-212ppppq ~ãppppq ~æ  wñppppppq ~ëppq ~ñppppppppsq ~òpsq ~÷  wñppppq ~8q ~8q ~6psq ~  wñppppq ~8q ~8psq ~ø  wñppppq ~8q ~8psq ~	  wñppppq ~8q ~8psq ~
  wñppppq ~8q ~8pppppt Helvetica-Boldppppppppppq ~  wñ        ppq ~sq ~   
uq ~   sq ~t 
numeroNotat java.lang.Stringppppppq ~ðpppsq ~Ç  wñ           W  ñ   pq ~ q ~)pt 
textField-212ppppq ~ãppppq ~æ  wñppppppq ~ëpq ~Oq ~ñppppppppsq ~òpsq ~÷  wñppppq ~Fq ~Fq ~Dpsq ~  wñppppq ~Fq ~Fpsq ~ø  wñppppq ~Fq ~Fpsq ~	  wñppppq ~Fq ~Fpsq ~
  wñppppq ~Fq ~Fpppppt Helvetica-Boldppppppppppq ~  wñ        ppq ~sq ~   uq ~   sq ~t dataServicot java.lang.Stringppppppq ~ðpppsq ~Ç  wñ           ê  ^   pq ~ q ~)pt 
textField-212ppppq ~ãppppq ~æ  wñppppppq ~ëppq ~ñppppppppsq ~òpsq ~÷  wñppppq ~Tq ~Tq ~Rpsq ~  wñppppq ~Tq ~Tpsq ~ø  wñppppq ~Tq ~Tpsq ~	  wñppppq ~Tq ~Tpsq ~
  wñppppq ~Tq ~Tpppppt Helvetica-Boldppppppppppq ~  wñ        ppq ~sq ~   uq ~   sq ~t municipioPrestacaot java.lang.Stringppppppq ~ðpppsq ~Ç  wñ           i   j   pq ~ q ~)pt 
textField-212ppppq ~ãppppq ~æ  wñppppppq ~ëppq ~ñppppppppsq ~òpsq ~÷  wñppppq ~bq ~bq ~`psq ~  wñppppq ~bq ~bpsq ~ø  wñppppq ~bq ~bpsq ~	  wñppppq ~bq ~bpsq ~
  wñppppq ~bq ~bpppppt Helvetica-Boldppppppppppq ~  wñ        ppq ~sq ~   uq ~   sq ~t 	numeroRPSt java.lang.Stringppppppq ~ðpppsq ~Ç  wñ           [   	   pq ~ q ~)pt 
textField-212ppppq ~ãppppq ~æ  wñppppppq ~ëppq ~ñppppppppsq ~òpsq ~÷  wñppppq ~pq ~pq ~npsq ~  wñppppq ~pq ~ppsq ~ø  wñppppq ~pq ~ppsq ~	  wñppppq ~pq ~ppsq ~
  wñppppq ~pq ~ppppppt Helvetica-Boldppppppppppq ~  wñ        ppq ~sq ~   uq ~   sq ~t dataCompetenciat java.lang.Stringppppppq ~ðpppsq ~Ç  wñ             À   «pq ~ q ~)pt 
textField-212ppppq ~ãppppq ~æ  wñppppppq ~ëppq ~ñppppppppsq ~òpsq ~÷  wñppppq ~~q ~~q ~|psq ~  wñppppq ~~q ~~psq ~ø  wñppppq ~~q ~~psq ~	  wñppppq ~~q ~~psq ~
  wñppppq ~~q ~~pppppt Helvetica-Boldppppppppppq ~  wñ        ppq ~sq ~   uq ~   sq ~t razaoSocialPrestadort java.lang.Stringppppppq ~ðpppsq ~Ç  wñ                 ºpq ~ q ~)pt 
textField-212ppppq ~ãppppq ~æ  wñppppppq ~ëppq ~ñppppppppsq ~òpsq ~÷  wñppppq ~q ~q ~psq ~  wñppppq ~q ~psq ~ø  wñppppq ~q ~psq ~	  wñppppq ~q ~psq ~
  wñppppq ~q ~pppppt Helvetica-Boldppppppppppq ~  wñ        ppq ~sq ~   uq ~   sq ~t 
cnpjPrestadort java.lang.Stringppppppq ~ðpppsq ~Ç  wñ          ±      Épq ~ q ~)pt 
textField-212ppppq ~ãppppq ~æ  wñppppppq ~ëppq ~ñppppppppsq ~òpsq ~÷  wñppppq ~q ~q ~psq ~  wñppppq ~q ~psq ~ø  wñppppq ~q ~psq ~	  wñppppq ~q ~psq ~
  wñppppq ~q ~pppppt Helvetica-Boldppppppppppq ~  wñ        ppq ~sq ~   uq ~   sq ~t enderecoPrestadort java.lang.Stringppppppq ~ðpppsq ~Ç  wñ           æ   ª   ×pq ~ q ~)pt 
textField-212ppppq ~ãppppq ~æ  wñppppppq ~ëppq ~ñppppppppsq ~òpsq ~÷  wñppppq ~¨q ~¨q ~¦psq ~  wñppppq ~¨q ~¨psq ~ø  wñppppq ~¨q ~¨psq ~	  wñppppq ~¨q ~¨psq ~
  wñppppq ~¨q ~¨pppppt Helvetica-Boldppppppppppq ~  wñ        ppq ~sq ~   uq ~   sq ~t complementoPrestadort java.lang.Stringppppppq ~ðpppsq ~Ç  wñ           È     ºpq ~ q ~)pt 
textField-212ppppq ~ãppppq ~æ  wñppppppq ~ëppq ~ñppppppppsq ~òpsq ~÷  wñppppq ~¶q ~¶q ~´psq ~  wñppppq ~¶q ~¶psq ~ø  wñppppq ~¶q ~¶psq ~	  wñppppq ~¶q ~¶psq ~
  wñppppq ~¶q ~¶pppppt Helvetica-Boldppppppppppq ~  wñ        ppq ~sq ~   uq ~   sq ~t inscricaoMunicipalPrestadort java.lang.Stringppppppq ~ðpppsq ~Ç  wñ             »   ×pq ~ q ~)pt 
textField-212ppppq ~ãppppq ~æ  wñppppppq ~ëppq ~ñppppppppsq ~òpsq ~÷  wñppppq ~Äq ~Äq ~Âpsq ~  wñppppq ~Äq ~Äpsq ~ø  wñppppq ~Äq ~Äpsq ~	  wñppppq ~Äq ~Äpsq ~
  wñppppq ~Äq ~Äpppppt Helvetica-Boldppppppppppq ~  wñ        ppq ~sq ~   uq ~   sq ~t telefonePrestadort java.lang.Stringppppppq ~ðpppsq ~Ç  wñ           õ  S   æpq ~ q ~)pt 
textField-212ppppq ~ãppppq ~æ  wñppppppsq ~é   ppq ~ñppppppppsq ~òpsq ~÷  wñppppq ~Óq ~Óq ~Ðpsq ~  wñppppq ~Óq ~Ópsq ~ø  wñppppq ~Óq ~Ópsq ~	  wñppppq ~Óq ~Ópsq ~
  wñppppq ~Óq ~Ópppppt Helvetica-Boldppppppppppq ~  wñ        ppq ~sq ~   uq ~   sq ~t emailPrestadort java.lang.Stringppppppq ~ðpppsq ~Ç  wñ                æpq ~ q ~)pt 
textField-212ppppq ~ãppppq ~æ  wñppppppq ~Òppq ~ñppppppppsq ~òpsq ~÷  wñppppq ~áq ~áq ~ßpsq ~  wñppppq ~áq ~ápsq ~ø  wñppppq ~áq ~ápsq ~	  wñppppq ~áq ~ápsq ~
  wñppppq ~áq ~ápppppt Helvetica-Boldppppppppppq ~  wñ        ppq ~sq ~   uq ~   sq ~t ufPrestadort java.lang.Stringppppppq ~ðpppsq ~Ç  wñ           v      æpq ~ q ~)pt 
textField-212ppppq ~ãppppq ~æ  wñppppppq ~Òppq ~ñppppppppsq ~òpsq ~÷  wñppppq ~ïq ~ïq ~ípsq ~  wñppppq ~ïq ~ïpsq ~ø  wñppppq ~ïq ~ïpsq ~	  wñppppq ~ïq ~ïpsq ~
  wñppppq ~ïq ~ïpppppt Helvetica-Boldppppppppppq ~  wñ        ppq ~sq ~   uq ~   sq ~t municipioPrestadort java.lang.Stringppppppq ~ðpppsq ~Ç  wñ          î   Z  
pq ~ q ~)pt 
textField-212ppppq ~ãppppq ~æ  wñppppppq ~ëppq ~ñppppppppsq ~òpsq ~÷  wñppppq ~ýq ~ýq ~ûpsq ~  wñppppq ~ýq ~ýpsq ~ø  wñppppq ~ýq ~ýpsq ~	  wñppppq ~ýq ~ýpsq ~
  wñppppq ~ýq ~ýpppppt Helvetica-Boldppppppppppq ~  wñ        ppq ~sq ~   uq ~   sq ~t razaoSocialTomadort java.lang.Stringppppppq ~ðpppsq ~Ç  wñ             6  pq ~ q ~)pt 
textField-212ppppq ~ãppppq ~æ  wñppppppq ~ëppq ~ñppppppppsq ~òpsq ~÷  wñppppq ~q ~q ~	psq ~  wñppppq ~q ~psq ~ø  wñppppq ~q ~psq ~	  wñppppq ~q ~psq ~
  wñppppq ~q ~pppppt Helvetica-Boldppppppppppq ~  wñ        ppq ~sq ~   uq ~   sq ~t cnpjTomadort java.lang.Stringppppppq ~ðpppsq ~Ç  wñ           ¼    pq ~ q ~)pt 
textField-212ppppq ~ãppppq ~æ  wñppppppq ~ëppq ~ñppppppppsq ~òpsq ~÷  wñppppq ~q ~q ~psq ~  wñppppq ~q ~psq ~ø  wñppppq ~q ~psq ~	  wñppppq ~q ~psq ~
  wñppppq ~q ~pppppt Helvetica-Boldppppppppppq ~  wñ        ppq ~sq ~   uq ~   sq ~t inscricaoMunicipalTomadort java.lang.Stringppppppq ~ðpppsq ~Ç  wñ             6  (pq ~ q ~)pt 
textField-212ppppq ~ãppppq ~æ  wñppppppq ~ëppq ~ñppppppppsq ~òpsq ~÷  wñppppq ~'q ~'q ~%psq ~  wñppppq ~'q ~'psq ~ø  wñppppq ~'q ~'psq ~	  wñppppq ~'q ~'psq ~
  wñppppq ~'q ~'pppppt Helvetica-Boldppppppppppq ~  wñ        ppq ~sq ~   uq ~   sq ~t enderecoTomadort java.lang.Stringppppppq ~ðpppsq ~Ç  wñ           ð   G  7pq ~ q ~)pt 
textField-212ppppq ~ãppppq ~æ  wñppppppq ~ëppq ~ñppppppppsq ~òpsq ~÷  wñppppq ~5q ~5q ~3psq ~  wñppppq ~5q ~5psq ~ø  wñppppq ~5q ~5psq ~	  wñppppq ~5q ~5psq ~
  wñppppq ~5q ~5pppppt Helvetica-Boldppppppppppq ~  wñ        ppq ~sq ~   uq ~   sq ~t complementoTomadort java.lang.Stringppppppq ~ðpppsq ~Ç  wñ           ¡   2  Epq ~ q ~)pt 
textField-212ppppq ~ãppppq ~æ  wñppppppq ~ëppq ~ñppppppppsq ~òpsq ~÷  wñppppq ~Cq ~Cq ~Apsq ~  wñppppq ~Cq ~Cpsq ~ø  wñppppq ~Cq ~Cpsq ~	  wñppppq ~Cq ~Cpsq ~
  wñppppq ~Cq ~Cpppppt Helvetica-Boldppppppppppq ~  wñ        ppq ~sq ~    uq ~   sq ~t municipioTomadort java.lang.Stringppppppq ~ðpppsq ~Ç  wñ           (   ç  Epq ~ q ~)pt 
textField-212ppppq ~ãppppq ~æ  wñppppppq ~ëppq ~ñppppppppsq ~òpsq ~÷  wñppppq ~Qq ~Qq ~Opsq ~  wñppppq ~Qq ~Qpsq ~ø  wñppppq ~Qq ~Qpsq ~	  wñppppq ~Qq ~Qpsq ~
  wñppppq ~Qq ~Qpppppt Helvetica-Boldppppppppppq ~  wñ        ppq ~sq ~   !uq ~   sq ~t 	ufTomadort java.lang.Stringppppppq ~ðpppsq ~Ç  wñ            1  Epq ~ q ~)pt 
textField-212ppppq ~ãppppq ~æ  wñppppppq ~ëppq ~ñppppppppsq ~òpsq ~÷  wñppppq ~_q ~_q ~]psq ~  wñppppq ~_q ~_psq ~ø  wñppppq ~_q ~_psq ~	  wñppppq ~_q ~_psq ~
  wñppppq ~_q ~_pppppt Helvetica-Boldppppppppppq ~  wñ        ppq ~sq ~   "uq ~   sq ~t emailTomadort java.lang.Stringppppppq ~ðpppsq ~Ç  wñ           ä  d  7pq ~ q ~)pt 
textField-212ppppq ~ãppppq ~æ  wñppppppq ~ëppq ~ñppppppppsq ~òpsq ~÷  wñppppq ~mq ~mq ~kpsq ~  wñppppq ~mq ~mpsq ~ø  wñppppq ~mq ~mpsq ~	  wñppppq ~mq ~mpsq ~
  wñppppq ~mq ~mpppppt Helvetica-Boldppppppppppq ~  wñ        ppq ~sq ~   #uq ~   sq ~t telefoneTomadort java.lang.Stringppppppq ~ðpppsq ~Ç  wñ   K       <   
  jpq ~ q ~)pt 
textField-212ppppq ~ãppppq ~æ  wñppppppq ~ëppq ~ñppppppppsq ~òpsq ~÷  wñppppq ~{q ~{q ~ypsq ~  wñppppq ~{q ~{psq ~ø  wñppppq ~{q ~{psq ~	  wñppppq ~{q ~{psq ~
  wñppppq ~{q ~{pppppt Helvetica-Boldppppppppppq ~J  wñ        ppq ~sq ~   $uq ~   sq ~t servicosDescricaot java.lang.Stringppppppq ~ðpppsq ~Ç  wñ           k     øpq ~ q ~)pt 
textField-212ppppq ~ãppppq ~æ  wñppppppq ~ëpq ~Oq ~ðppppppppsq ~òpsq ~÷  wñppppq ~q ~q ~psq ~  wñppppq ~q ~psq ~ø  wñppppq ~q ~psq ~	  wñppppq ~q ~psq ~
  wñppppq ~q ~pppppt Helvetica-Boldppppppppppq ~  wñ        ppq ~sq ~   %uq ~   sq ~t valorPISt java.lang.Stringppppppq ~ðpppsq ~Ç  wñ           h   ~  øpq ~ q ~)pt 
textField-212ppppq ~ãppppq ~æ  wñppppppq ~ëpq ~Oq ~ðppppppppsq ~òpsq ~÷  wñppppq ~q ~q ~psq ~  wñppppq ~q ~psq ~ø  wñppppq ~q ~psq ~	  wñppppq ~q ~psq ~
  wñppppq ~q ~pppppt Helvetica-Boldppppppppppq ~  wñ        ppq ~sq ~   &uq ~   sq ~t valorCOFINSt java.lang.Stringppppppq ~ðpppsq ~Ç  wñ           l   ï  øpq ~ q ~)pt 
textField-212ppppq ~ãppppq ~æ  wñppppppq ~ëpq ~Oq ~ðppppppppsq ~òpsq ~÷  wñppppq ~¥q ~¥q ~£psq ~  wñppppq ~¥q ~¥psq ~ø  wñppppq ~¥q ~¥psq ~	  wñppppq ~¥q ~¥psq ~
  wñppppq ~¥q ~¥pppppt Helvetica-Boldppppppppppq ~  wñ        ppq ~sq ~   'uq ~   sq ~t valorIRt java.lang.Stringppppppq ~ðpppsq ~Ç  wñ           k  d  øpq ~ q ~)pt 
textField-212ppppq ~ãppppq ~æ  wñppppppq ~ëpq ~Oq ~ðppppppppsq ~òpsq ~÷  wñppppq ~³q ~³q ~±psq ~  wñppppq ~³q ~³psq ~ø  wñppppq ~³q ~³psq ~	  wñppppq ~³q ~³psq ~
  wñppppq ~³q ~³pppppt Helvetica-Boldppppppppppq ~  wñ        ppq ~sq ~   (uq ~   sq ~t 	valorINSSt java.lang.Stringppppppq ~ðpppsq ~Ç  wñ           j  Ý  øpq ~ q ~)pt 
textField-212ppppq ~ãppppq ~æ  wñppppppq ~ëpq ~Oq ~ðppppppppsq ~òpsq ~÷  wñppppq ~Áq ~Áq ~¿psq ~  wñppppq ~Áq ~Ápsq ~ø  wñppppq ~Áq ~Ápsq ~	  wñppppq ~Áq ~Ápsq ~
  wñppppq ~Áq ~Ápppppt Helvetica-Boldppppppppppq ~  wñ        ppq ~sq ~   )uq ~   sq ~t 	valorCSLLt java.lang.Stringppppppq ~ðpppsq ~Ç  wñ           h   ~  pq ~ q ~)pt 
textField-212ppppq ~ãppppq ~æ  wñppppppq ~ëpq ~Oq ~ðppppppppsq ~òpsq ~÷  wñppppq ~Ïq ~Ïq ~Ípsq ~  wñppppq ~Ïq ~Ïpsq ~ø  wñppppq ~Ïq ~Ïpsq ~	  wñppppq ~Ïq ~Ïpsq ~
  wñppppq ~Ïq ~Ïpppppt Helvetica-Boldppppppppppq ~  wñ        ppq ~sq ~   *uq ~   sq ~t 
valorServicost java.lang.Stringppppppq ~ðpppsq ~Ç  wñ           f     -pq ~ q ~)pt 
textField-212ppppq ~ãppppq ~æ  wñppppppq ~ëpq ~Oq ~ðppppppppsq ~òpsq ~÷  wñppppq ~Ýq ~Ýq ~Ûpsq ~  wñppppq ~Ýq ~Ýpsq ~ø  wñppppq ~Ýq ~Ýpsq ~	  wñppppq ~Ýq ~Ýpsq ~
  wñppppq ~Ýq ~Ýpppppt Helvetica-Boldppppppppppq ~  wñ        ppq ~sq ~   +uq ~   sq ~t descontoIncondicionadot java.lang.Stringppppppq ~ðpppsq ~Ç  wñ           f     =pq ~ q ~)pt 
textField-212ppppq ~ãppppq ~æ  wñppppppq ~ëpq ~Oq ~ðppppppppsq ~òpsq ~÷  wñppppq ~ëq ~ëq ~épsq ~  wñppppq ~ëq ~ëpsq ~ø  wñppppq ~ëq ~ëpsq ~	  wñppppq ~ëq ~ëpsq ~
  wñppppq ~ëq ~ëpppppt Helvetica-Boldppppppppppq ~  wñ        ppq ~sq ~   ,uq ~   sq ~t descontoCondicionadot java.lang.Stringppppppq ~ðpppsq ~Ç  wñ           f     Kpq ~ q ~)pt 
textField-212ppppq ~ãppppq ~æ  wñppppppq ~ëpq ~Oq ~ðppppppppsq ~òpsq ~÷  wñppppq ~ùq ~ùq ~÷psq ~  wñppppq ~ùq ~ùpsq ~ø  wñppppq ~ùq ~ùpsq ~	  wñppppq ~ùq ~ùpsq ~
  wñppppq ~ùq ~ùpppppt Helvetica-Boldppppppppppq ~  wñ        ppq ~sq ~   -uq ~   sq ~t retencoesFederaist java.lang.Stringppppppq ~ðpppsq ~Ç  wñ           f     Zpq ~ q ~)pt 
textField-212ppppq ~ãppppq ~æ  wñppppppq ~ëpq ~Oq ~ðppppppppsq ~òpsq ~÷  wñppppq ~q ~q ~psq ~  wñppppq ~q ~psq ~ø  wñppppq ~q ~psq ~	  wñppppq ~q ~psq ~
  wñppppq ~q ~pppppt Helvetica-Boldppppppppppq ~  wñ        ppq ~sq ~   .uq ~   sq ~t outrasRetencoest java.lang.Stringppppppq ~ðpppsq ~Ç  wñ           f     kpq ~ q ~)pt 
textField-212ppppq ~ãppppq ~æ  wñppppppq ~ëpq ~Oq ~ðppppppppsq ~òpsq ~÷  wñppppq ~q ~q ~psq ~  wñppppq ~q ~psq ~ø  wñppppq ~q ~psq ~	  wñppppq ~q ~psq ~
  wñppppq ~q ~pppppt Helvetica-Boldppppppppppq ~  wñ        ppq ~sq ~   /uq ~   sq ~t issRetidoValort java.lang.Stringppppppq ~ðpppsq ~Ç  wñ           |   ï  2pq ~ q ~)pt 
textField-212ppppq ~ãppppq ~æ  wñppppppq ~ëpq ~[q ~ðppppppppsq ~òpsq ~÷  wñppppq ~#q ~#q ~!psq ~  wñppppq ~#q ~#psq ~ø  wñppppq ~#q ~#psq ~	  wñppppq ~#q ~#psq ~
  wñppppq ~#q ~#pppppt Helvetica-Boldppppppppppq ~  wñ        ppq ~sq ~   0uq ~   sq ~t naturezaOperacaot java.lang.Stringppppppq ~ðpppsq ~Ç  wñ           |   ï  Xpq ~ q ~)pt 
textField-212ppppq ~ãppppq ~æ  wñppppppq ~ëpq ~[q ~ðppppppppsq ~òpsq ~÷  wñppppq ~1q ~1q ~/psq ~  wñppppq ~1q ~1psq ~ø  wñppppq ~1q ~1psq ~	  wñppppq ~1q ~1psq ~
  wñppppq ~1q ~1pppppt Helvetica-Boldppppppppppq ~  wñ        ppq ~sq ~   1uq ~   sq ~t regimeEspecialt java.lang.Stringppppppq ~ðpppsq ~Ç  wñ           W  ñ  pq ~ q ~)pt 
textField-212ppppq ~ãppppq ~æ  wñppppppq ~ëpq ~Oq ~ðppppppppsq ~òpsq ~÷  wñppppq ~?q ~?q ~=psq ~  wñppppq ~?q ~?psq ~ø  wñppppq ~?q ~?psq ~	  wñppppq ~?q ~?psq ~
  wñppppq ~?q ~?pppppt Helvetica-Boldppppppppppq ~  wñ        ppq ~sq ~   2uq ~   sq ~t 
valorServicost java.lang.Stringppppppq ~ðpppsq ~Ç  wñ           W  ñ  <pq ~ q ~)pt 
textField-212ppppq ~ãppppq ~æ  wñppppppq ~ëpq ~Oq ~ðppppppppsq ~òpsq ~÷  wñppppq ~Mq ~Mq ~Kpsq ~  wñppppq ~Mq ~Mpsq ~ø  wñppppq ~Mq ~Mpsq ~	  wñppppq ~Mq ~Mpsq ~
  wñppppq ~Mq ~Mpppppt Helvetica-Boldppppppppppq ~  wñ        ppq ~sq ~   3uq ~   sq ~t deducoesPermitidast java.lang.Stringppppppq ~ðpppsq ~Ç  wñ           W  ñ  -pq ~ q ~)pt 
textField-212ppppq ~ãppppq ~æ  wñppppppq ~ëpq ~Oq ~ðppppppppsq ~òpsq ~÷  wñppppq ~[q ~[q ~Ypsq ~  wñppppq ~[q ~[psq ~ø  wñppppq ~[q ~[psq ~	  wñppppq ~[q ~[psq ~
  wñppppq ~[q ~[pppppt Helvetica-Boldppppppppppq ~  wñ        ppq ~sq ~   4uq ~   sq ~t descontoIncondicionadoMunicipiot java.lang.Stringppppppq ~ðpppsq ~Ç  wñ           W  ñ  Kpq ~ q ~)pt 
textField-212ppppq ~ãppppq ~æ  wñppppppq ~ëpq ~Oq ~ðppppppppsq ~òpsq ~÷  wñppppq ~iq ~iq ~gpsq ~  wñppppq ~iq ~ipsq ~ø  wñppppq ~iq ~ipsq ~	  wñppppq ~iq ~ipsq ~
  wñppppq ~iq ~ipppppt Helvetica-Boldppppppppppq ~  wñ        ppq ~sq ~   5uq ~   sq ~t baseCalculot java.lang.Stringppppppq ~ðpppsq ~Ç  wñ           W  ñ  Ypq ~ q ~)pt 
textField-212ppppq ~ãppppq ~æ  wñppppppq ~ëpq ~Oq ~ðppppppppsq ~òpsq ~÷  wñppppq ~wq ~wq ~upsq ~  wñppppq ~wq ~wpsq ~ø  wñppppq ~wq ~wpsq ~	  wñppppq ~wq ~wpsq ~
  wñppppq ~wq ~wpppppt Helvetica-Boldppppppppppq ~  wñ        ppq ~sq ~   6uq ~   sq ~t aliquotat java.lang.Stringppppppq ~ðpppsq ~Ç  wñ           W  ñ  hpq ~ q ~)pt 
textField-212ppppq ~ãppppq ~æ  wñppppppq ~ëpq ~Oq ~ðppppppppsq ~òpsq ~÷  wñppppq ~q ~q ~psq ~  wñppppq ~q ~psq ~ø  wñppppq ~q ~psq ~	  wñppppq ~q ~psq ~
  wñppppq ~q ~pppppt Helvetica-Boldppppppppppq ~  wñ        ppq ~sq ~   7uq ~   sq ~t reterISSt java.lang.Stringppppppq ~ðpppsq ~Ç  wñ           W  ñ  pq ~ q ~)pt 
textField-212ppppq ~ãppppq ~æ  wñppppppq ~ëpq ~Oq ~ñppppppppsq ~òpsq ~÷  wñppppq ~q ~q ~psq ~  wñppppq ~q ~psq ~ø  wñppppq ~q ~psq ~	  wñppppq ~q ~psq ~
  wñppppq ~q ~pppppt Helvetica-Boldppppppppppq ~  wñ        ppq ~sq ~   8uq ~   sq ~t valorISSt java.lang.Stringppppppq ~ðpppsq ~Ç  wñ   #       <   
  Æpq ~ q ~)pt 
textField-212ppppq ~ãppppq ~æ  wñppppppq ~ëppq ~ñppppppppsq ~òpsq ~÷  wñppppq ~¡q ~¡q ~psq ~  wñppppq ~¡q ~¡psq ~ø  wñppppq ~¡q ~¡psq ~	  wñppppq ~¡q ~¡psq ~
  wñppppq ~¡q ~¡pppppt Helvetica-Boldppppppppppq ~J  wñ        ppq ~sq ~   9uq ~   sq ~t outrasInformacoest java.lang.Stringppppppq ~ðpppsq ~Ç  wñ           a     pq ~ q ~)pt 
textField-212ppppq ~ãppppq ~æ  wñppppppq ~ëpq ~Oq ~ñppppppppsq ~òpsq ~÷  wñppppq ~¯q ~¯q ~­psq ~  wñppppq ~¯q ~¯psq ~ø  wñppppq ~¯q ~¯psq ~	  wñppppq ~¯q ~¯psq ~
  wñppppq ~¯q ~¯pppppt Helvetica-Boldppppppppppq ~  wñ        ppq ~sq ~   :uq ~   sq ~t valorLiquidot java.lang.Stringppppppq ~ðpppsq ~Ç  wñ              M  |pq ~ q ~)pt 
textField-212ppppq ~ãppppq ~æ  wñppppppq ~ëpq ~Oq ~ñppppppppsq ~òpsq ~÷  wñppppq ~½q ~½q ~»psq ~  wñppppq ~½q ~½psq ~ø  wñppppq ~½q ~½psq ~	  wñppppq ~½q ~½psq ~
  wñppppq ~½q ~½pppppt Helvetica-Boldppppppppppq ~  wñ        ppq ~sq ~   ;uq ~   sq ~t simplesNacionalt java.lang.Stringppppppq ~ðpppsq ~Ç  wñ              M  pq ~ q ~)pt 
textField-212ppppq ~ãppppq ~æ  wñppppppq ~ëpq ~Oq ~ñppppppppsq ~òpsq ~÷  wñppppq ~Ëq ~Ëq ~Épsq ~  wñppppq ~Ëq ~Ëpsq ~ø  wñppppq ~Ëq ~Ëpsq ~	  wñppppq ~Ëq ~Ëpsq ~
  wñppppq ~Ëq ~Ëpppppt Helvetica-Boldppppppppppq ~  wñ        ppq ~sq ~   <uq ~   sq ~t incentivadorCulturalt java.lang.Stringppppppq ~ðpppsq ~Ç  wñ          ï   W  »pq ~ q ~)pt 
textField-212ppppq ~ãppppq ~æ  wñppppppq ~ëppq ~ñppppppppsq ~òpsq ~÷  wñppppq ~Ùq ~Ùq ~×psq ~  wñppppq ~Ùq ~Ùpsq ~ø  wñppppq ~Ùq ~Ùpsq ~	  wñppppq ~Ùq ~Ùpsq ~
  wñppppq ~Ùq ~Ùpppppt Helvetica-Boldppppppppppq ~  wñ        ppq ~sq ~   =uq ~   sq ~t 
codigoServicot java.lang.Stringppppppq ~ðpppsq ~°  wñ   U       Z   
   pq ~ q ~)pt image-1ppppq ~ãppppq ~æ  wîppsq ~ù  wñppppq ~åp  wñ         pppppppq ~¶sq ~   >uq ~   sq ~t logomarcaPrefeiturat java.io.InputStreamppq ~[pppppq ~ñpppsq ~òpsq ~÷  wñsq ~Ú    ÿfffppppq ~ sq ~?   q ~íq ~íq ~åpsq ~  wñsq ~Ú    ÿfffppppq ~ sq ~?   q ~íq ~ípsq ~ø  wñppppq ~íq ~ípsq ~	  wñsq ~Ú    ÿfffppppq ~ sq ~?   q ~íq ~ípsq ~
  wñsq ~Ú    ÿfffppppq ~ sq ~?   q ~íq ~íppq ~Ìpppppppppppxp  wñ  ñppq ~ ~r /net.sf.jasperreports.engine.type.PrintOrderEnum          xq ~ t VERTICALpppsq ~ sq ~     w    xp  wñ    ppq ~ ~r 3net.sf.jasperreports.engine.type.WhenNoDataTypeEnum          xq ~ t ALL_SECTIONS_NO_DETAILsr 6net.sf.jasperreports.engine.design.JRReportCompileData      'Ø L crosstabCompileDataq ~ BL datasetCompileDataq ~ BL mainDatasetCompileDataq ~ xpsq ~p?@     w       xsq ~p?@     w       xur [B¬óøTà  xp  7ÐÊþº¾   .Ø NotaManual_1545416662313_99423  ,net/sf/jasperreports/engine/fill/JREvaluator  parameter_REPORT_LOCALE 2Lnet/sf/jasperreports/engine/fill/JRFillParameter; parameter_JASPER_REPORT parameter_REPORT_VIRTUALIZER parameter_REPORT_TIME_ZONE parameter_SORT_FIELDS parameter_REPORT_FILE_RESOLVER parameter_REPORT_SCRIPTLET parameter_REPORT_PARAMETERS_MAP parameter_REPORT_CONNECTION parameter_REPORT_CLASS_LOADER parameter_REPORT_DATA_SOURCE $parameter_REPORT_URL_HANDLER_FACTORY parameter_IS_IGNORE_PAGINATION parameter_REPORT_FORMAT_FACTORY parameter_REPORT_MAX_COUNT parameter_REPORT_TEMPLATES  parameter_REPORT_RESOURCE_BUNDLE field_enderecoPrestador .Lnet/sf/jasperreports/engine/fill/JRFillField; field_municipioPrestacao 
field_valorIR  field_inscricaoEstadualPrestador field_outrasInformacoes field_naturezaOperacao field_cnpjTomador field_valorTotalNota field_reterISS field_codigoServico field_valorCOFINS field_logomarcaPrefeitura field_numeroRPS field_dataHoraEmissao field_numeroNota field_dataServico field_valorCSLL field_cnpjPrestador field_enderecoTomador field_valorINSS field_incentivadorCultural field_deducoesPermitidas field_municipioPrestador field_inscricaoEstadualTomador field_emailPrestador field_simplesNacional field_issRetidoValor field_cidadePrestacao field_ufPrestador %field_descontoIncondicionadoMunicipio field_ufTomador !field_inscricaoMunicipalPrestador field_razaoSocialPrestador field_baseCalculo field_dataCompetencia field_valorPIS field_telefoneTomador field_aliquota field_retencoesFederais field_municipioTomador field_inscricaoMunicipalTomador field_valorISS field_complementoPrestador field_descontoIncondicionado field_valorLiquido field_emailTomador field_codigoAutorizacao field_regimeEspecial field_outrasRetencoes field_telefonePrestador field_descontoCondicionado field_servicosDescricao field_razaoSocialTomador field_logomarcaPrestador field_complementoTomador field_valorServicos variable_PAGE_NUMBER 1Lnet/sf/jasperreports/engine/fill/JRFillVariable; variable_COLUMN_NUMBER variable_REPORT_COUNT variable_PAGE_COUNT variable_COLUMN_COUNT <init> ()V Code V W
  Y  	  [  	  ]  	  _ 	 	  a 
 	  c  	  e  	  g 
 	  i  	  k  	  m  	  o  	  q  	  s  	  u  	  w  	  y  	  {  	  }  	    	    	    	    	    	    	     	   ! 	   " 	   # 	   $ 	   % 	   & 	   ' 	   ( 	   ) 	   * 	  ¡ + 	  £ , 	  ¥ - 	  § . 	  © / 	  « 0 	  ­ 1 	  ¯ 2 	  ± 3 	  ³ 4 	  µ 5 	  · 6 	  ¹ 7 	  » 8 	  ½ 9 	  ¿ : 	  Á ; 	  Ã < 	  Å = 	  Ç > 	  É ? 	  Ë @ 	  Í A 	  Ï B 	  Ñ C 	  Ó D 	  Õ E 	  × F 	  Ù G 	  Û H 	  Ý I 	  ß J 	  á K 	  ã L 	  å M 	  ç N 	  é O 	  ë P Q	  í R Q	  ï S Q	  ñ T Q	  ó U Q	  õ LineNumberTable customizedInit 0(Ljava/util/Map;Ljava/util/Map;Ljava/util/Map;)V 
initParams (Ljava/util/Map;)V ú û
  ü 
initFields þ û
  ÿ initVars û
  
REPORT_LOCALE 
java/util/Map get &(Ljava/lang/Object;)Ljava/lang/Object;	
 0net/sf/jasperreports/engine/fill/JRFillParameter 
JASPER_REPORT REPORT_VIRTUALIZER REPORT_TIME_ZONE SORT_FIELDS REPORT_FILE_RESOLVER REPORT_SCRIPTLET REPORT_PARAMETERS_MAP REPORT_CONNECTION REPORT_CLASS_LOADER REPORT_DATA_SOURCE  REPORT_URL_HANDLER_FACTORY" IS_IGNORE_PAGINATION$ REPORT_FORMAT_FACTORY& REPORT_MAX_COUNT( REPORT_TEMPLATES* REPORT_RESOURCE_BUNDLE, enderecoPrestador. ,net/sf/jasperreports/engine/fill/JRFillField0 municipioPrestacao2 valorIR4 inscricaoEstadualPrestador6 outrasInformacoes8 naturezaOperacao: cnpjTomador< valorTotalNota> reterISS@ 
codigoServicoB valorCOFINSD logomarcaPrefeituraF 	numeroRPSH dataHoraEmissaoJ 
numeroNotaL dataServicoN 	valorCSLLP 
cnpjPrestadorR enderecoTomadorT 	valorINSSV incentivadorCulturalX deducoesPermitidasZ municipioPrestador\ inscricaoEstadualTomador^ emailPrestador` simplesNacionalb issRetidoValord cidadePrestacaof ufPrestadorh descontoIncondicionadoMunicipioj 	ufTomadorl inscricaoMunicipalPrestadorn razaoSocialPrestadorp baseCalculor dataCompetenciat valorPISv telefoneTomadorx aliquotaz retencoesFederais| municipioTomador~ inscricaoMunicipalTomador valorISS complementoPrestador descontoIncondicionado valorLiquido emailTomador codigoAutorizacao regimeEspecial outrasRetencoes telefonePrestador descontoCondicionado servicosDescricao razaoSocialTomador logomarcaPrestador complementoTomador 
valorServicos PAGE_NUMBER  /net/sf/jasperreports/engine/fill/JRFillVariable¢ 
COLUMN_NUMBER¤ REPORT_COUNT¦ 
PAGE_COUNT¨ COLUMN_COUNTª evaluate (I)Ljava/lang/Object; 
Exceptions java/lang/Throwable¯ java/lang/Integer± (I)V V³
²´ java/lang/StringBuffer¶ Prefeitura de ¸ (Ljava/lang/String;)V Vº
·» getValue ()Ljava/lang/Object;½¾
1¿ java/lang/StringÁ append ,(Ljava/lang/String;)Ljava/lang/StringBuffer;ÃÄ
·Å toString ()Ljava/lang/String;ÇÈ
·É java/io/InputStreamË VALOR TOTAL DA NOTA = Í java/util/DateÏ
Ð Y evaluateOld getOldValueÓ¾
1Ô evaluateEstimated 
SourceFile !     N                 	     
               
                                                                                                !     "     #     $     %     &     '     (     )     *     +     ,     -     .     /     0     1     2     3     4     5     6     7     8     9     :     ;     <     =     >     ?     @     A     B     C     D     E     F     G     H     I     J     K     L     M     N     O     P Q    R Q    S Q    T Q    U Q     V W  X  ß    *· Z*µ \*µ ^*µ `*µ b*µ d*µ f*µ h*µ j*µ l*µ n*µ p*µ r*µ t*µ v*µ x*µ z*µ |*µ ~*µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ  *µ ¢*µ ¤*µ ¦*µ ¨*µ ª*µ ¬*µ ®*µ °*µ ²*µ ´*µ ¶*µ ¸*µ º*µ ¼*µ ¾*µ À*µ Â*µ Ä*µ Æ*µ È*µ Ê*µ Ì*µ Î*µ Ð*µ Ò*µ Ô*µ Ö*µ Ø*µ Ú*µ Ü*µ Þ*µ à*µ â*µ ä*µ æ*µ è*µ ê*µ ì*µ î*µ ð*µ ò*µ ô*µ ö±    ÷  B P      	          ! " " ' # , $ 1 % 6 & ; ' @ ( E ) J * O + T , Y - ^ . c / h 0 m 1 r 2 w 3 | 4  5  6  7  8  9  :  ; ¤ < © = ® > ³ ? ¸ @ ½ A Â B Ç C Ì D Ñ E Ö F Û G à H å I ê J ï K ô L ù M þ N O P
 Q R S T! U& V+ W0 X5 Y: Z? [D \I ]N ^S _X `] ab bg cl dq ev f{ g h i   ø ù  X   4     *+· ý*,· *-·±    ÷       u  v 
 w  x  ú û  X       D*+¹ À
À
µ \*+¹ À
À
µ ^*+¹ À
À
µ `*+¹ À
À
µ b*+¹ À
À
µ d*+¹ À
À
µ f*+¹ À
À
µ h*+¹ À
À
µ j*+¹ À
À
µ l*+¹ À
À
µ n*+!¹ À
À
µ p*+#¹ À
À
µ r*+%¹ À
À
µ t*+'¹ À
À
µ v*+)¹ À
À
µ x*++¹ À
À
µ z*+-¹ À
À
µ |±    ÷   J       &  9  L  _  r      «  ¾  Ñ  ä  ÷ 
  0 C   þ û  X  !    )*+/¹ À1À1µ ~*+3¹ À1À1µ *+5¹ À1À1µ *+7¹ À1À1µ *+9¹ À1À1µ *+;¹ À1À1µ *+=¹ À1À1µ *+?¹ À1À1µ *+A¹ À1À1µ *+C¹ À1À1µ *+E¹ À1À1µ *+G¹ À1À1µ *+I¹ À1À1µ *+K¹ À1À1µ *+M¹ À1À1µ *+O¹ À1À1µ *+Q¹ À1À1µ *+S¹ À1À1µ  *+U¹ À1À1µ ¢*+W¹ À1À1µ ¤*+Y¹ À1À1µ ¦*+[¹ À1À1µ ¨*+]¹ À1À1µ ª*+_¹ À1À1µ ¬*+a¹ À1À1µ ®*+c¹ À1À1µ °*+e¹ À1À1µ ²*+g¹ À1À1µ ´*+i¹ À1À1µ ¶*+k¹ À1À1µ ¸*+m¹ À1À1µ º*+o¹ À1À1µ ¼*+q¹ À1À1µ ¾*+s¹ À1À1µ À*+u¹ À1À1µ Â*+w¹ À1À1µ Ä*+y¹ À1À1µ Æ*+{¹ À1À1µ È*+}¹ À1À1µ Ê*+¹ À1À1µ Ì*+¹ À1À1µ Î*+¹ À1À1µ Ð*+¹ À1À1µ Ò*+¹ À1À1µ Ô*+¹ À1À1µ Ö*+¹ À1À1µ Ø*+¹ À1À1µ Ú*+¹ À1À1µ Ü*+¹ À1À1µ Þ*+¹ À1À1µ à*+¹ À1À1µ â*+¹ À1À1µ ä*+¹ À1À1µ æ*+¹ À1À1µ è*+¹ À1À1µ ê*+¹ À1À1µ ì±    ÷   æ 9      &  9  L  _  r      ¡ « ¢ ¾ £ Ñ ¤ ä ¥ ÷ ¦
 § ¨0 ©C ªV «i ¬| ­ ®¢ ¯µ °È ±Û ²î ³ ´ µ' ¶: ·M ¸` ¹s º » ¼¬ ½¿ ¾Ò ¿å Àø Á Â Ã1 ÄD ÅW Æj Ç} È É£ Ê¶ ËÉ ÌÜ Íï Î Ï Ð( Ñ  û  X        `*+¡¹ À£À£µ î*+¥¹ À£À£µ ð*+§¹ À£À£µ ò*+©¹ À£À£µ ô*+«¹ À£À£µ ö±    ÷       Ù  Ú & Û 9 Ü L Ý _ Þ ¬­ ®    ° X  ¸    Mª         ?  
    %  1  =  I  U  a  m      ·  Å  Ó  á  ï  ý      '  5  C  Q  _  m  {      ¥  ³  Á  Ï  Ý  ë  ù      #  1  ?  M  [  i  w      ¡  ¯  ½  Ë  Ù  ç  õ        -  ;  I  W  e  s    »²Y·µM§»²Y·µM§u»²Y·µM§i»²Y·µM§]»²Y·µM§Q»²Y·µM§E»²Y·µM§9»²Y·µM§-»·Y¹·¼*´ ´¶ÀÀÂ¶Æ¶ÊM§*´ è¶ÀÀÌM§»·YÎ·¼*´ ¶ÀÀÂ¶Æ¶ÊM§ã*´ Ú¶ÀÀÂM§Õ*´ ¶ÀÀÂM§Ç*´ ¶ÀÀÂM§¹*´ ¶ÀÀÂM§«*´ ¶ÀÀÂM§*´ ¶ÀÀÂM§*´ Â¶ÀÀÂM§*´ ¾¶ÀÀÂM§s*´  ¶ÀÀÂM§e*´ ~¶ÀÀÂM§W*´ Ò¶ÀÀÂM§I*´ ¼¶ÀÀÂM§;*´ à¶ÀÀÂM§-*´ ®¶ÀÀÂM§*´ ¶¶ÀÀÂM§*´ ª¶ÀÀÂM§*´ æ¶ÀÀÂM§õ*´ ¶ÀÀÂM§ç*´ Î¶ÀÀÂM§Ù*´ ¢¶ÀÀÂM§Ë*´ ê¶ÀÀÂM§½*´ Ì¶ÀÀÂM§¯*´ º¶ÀÀÂM§¡*´ Ø¶ÀÀÂM§*´ Æ¶ÀÀÂM§*´ ä¶ÀÀÂM§w*´ Ä¶ÀÀÂM§i*´ ¶ÀÀÂM§[*´ ¶ÀÀÂM§M*´ ¤¶ÀÀÂM§?*´ ¶ÀÀÂM§1*´ ì¶ÀÀÂM§#*´ Ô¶ÀÀÂM§*´ â¶ÀÀÂM§*´ Ê¶ÀÀÂM§ ù*´ Þ¶ÀÀÂM§ ë*´ ²¶ÀÀÂM§ Ý*´ ¶ÀÀÂM§ Ï*´ Ü¶ÀÀÂM§ Á*´ ì¶ÀÀÂM§ ³*´ ¨¶ÀÀÂM§ ¥*´ ¸¶ÀÀÂM§ *´ À¶ÀÀÂM§ *´ È¶ÀÀÂM§ {*´ ¶ÀÀÂM§ m*´ Ð¶ÀÀÂM§ _*´ ¶ÀÀÂM§ Q*´ Ö¶ÀÀÂM§ C*´ °¶ÀÀÂM§ 5*´ ¦¶ÀÀÂM§ '*´ ¶ÀÀÂM§ *´ ¶ÀÀÌM§ »ÐY·ÑM,°    ÷  
    æ  è ì í ñ% ò( ö1 ÷4 û= ü@ ILUX
admp·º#Å$È(Ó)Ö-á.ä2ï3ò7ý8 <=ABF'G*K5L8PCQFUQVTZ_[b_m`pd{e~ijnos¥t¨x³y¶}Á~ÄÏÒÝàëîùü
 #¡&¥1¦4ª?«B¯M°P´[µ^¹iºl¾w¿zÃÄÈÉÍ¡Î¤Ò¯Ó²×½ØÀÜËÝÎáÙâÜæççêëõìøðñõöúû"ÿ- 0;>	I
LWZehsv"#'/ Ò­ ®    ° X  ¸    Mª         ?  
    %  1  =  I  U  a  m      ·  Å  Ó  á  ï  ý      '  5  C  Q  _  m  {      ¥  ³  Á  Ï  Ý  ë  ù      #  1  ?  M  [  i  w      ¡  ¯  ½  Ë  Ù  ç  õ        -  ;  I  W  e  s    »²Y·µM§»²Y·µM§u»²Y·µM§i»²Y·µM§]»²Y·µM§Q»²Y·µM§E»²Y·µM§9»²Y·µM§-»·Y¹·¼*´ ´¶ÕÀÂ¶Æ¶ÊM§*´ è¶ÕÀÌM§»·YÎ·¼*´ ¶ÕÀÂ¶Æ¶ÊM§ã*´ Ú¶ÕÀÂM§Õ*´ ¶ÕÀÂM§Ç*´ ¶ÕÀÂM§¹*´ ¶ÕÀÂM§«*´ ¶ÕÀÂM§*´ ¶ÕÀÂM§*´ Â¶ÕÀÂM§*´ ¾¶ÕÀÂM§s*´  ¶ÕÀÂM§e*´ ~¶ÕÀÂM§W*´ Ò¶ÕÀÂM§I*´ ¼¶ÕÀÂM§;*´ à¶ÕÀÂM§-*´ ®¶ÕÀÂM§*´ ¶¶ÕÀÂM§*´ ª¶ÕÀÂM§*´ æ¶ÕÀÂM§õ*´ ¶ÕÀÂM§ç*´ Î¶ÕÀÂM§Ù*´ ¢¶ÕÀÂM§Ë*´ ê¶ÕÀÂM§½*´ Ì¶ÕÀÂM§¯*´ º¶ÕÀÂM§¡*´ Ø¶ÕÀÂM§*´ Æ¶ÕÀÂM§*´ ä¶ÕÀÂM§w*´ Ä¶ÕÀÂM§i*´ ¶ÕÀÂM§[*´ ¶ÕÀÂM§M*´ ¤¶ÕÀÂM§?*´ ¶ÕÀÂM§1*´ ì¶ÕÀÂM§#*´ Ô¶ÕÀÂM§*´ â¶ÕÀÂM§*´ Ê¶ÕÀÂM§ ù*´ Þ¶ÕÀÂM§ ë*´ ²¶ÕÀÂM§ Ý*´ ¶ÕÀÂM§ Ï*´ Ü¶ÕÀÂM§ Á*´ ì¶ÕÀÂM§ ³*´ ¨¶ÕÀÂM§ ¥*´ ¸¶ÕÀÂM§ *´ À¶ÕÀÂM§ *´ È¶ÕÀÂM§ {*´ ¶ÕÀÂM§ m*´ Ð¶ÕÀÂM§ _*´ ¶ÕÀÂM§ Q*´ Ö¶ÕÀÂM§ C*´ °¶ÕÀÂM§ 5*´ ¦¶ÕÀÂM§ '*´ ¶ÕÀÂM§ *´ ¶ÕÀÌM§ »ÐY·ÑM,°    ÷  
   8 :>?C%D(H1I4M=N@RISLWUXX\a]dambpfgklp·qºuÅvÈzÓ{Öáäïòý '*58¢C£F§Q¨T¬_­b±m²p¶{·~»¼ÀÁÅ¥Æ¨Ê³Ë¶ÏÁÐÄÔÏÕÒÙÝÚàÞëßîãùäüèé
íîò#ó&÷1ø4ü?ýBMP[^ilwz¡ ¤$¯%²)½*À.Ë/Î3Ù4Ü8ç9ê=õ>øBCGHLM"Q-R0V;W>[I\L`WaZeefhjskvoptuy Ö­ ®    ° X  ¸    Mª         ?  
    %  1  =  I  U  a  m      ·  Å  Ó  á  ï  ý      '  5  C  Q  _  m  {      ¥  ³  Á  Ï  Ý  ë  ù      #  1  ?  M  [  i  w      ¡  ¯  ½  Ë  Ù  ç  õ        -  ;  I  W  e  s    »²Y·µM§»²Y·µM§u»²Y·µM§i»²Y·µM§]»²Y·µM§Q»²Y·µM§E»²Y·µM§9»²Y·µM§-»·Y¹·¼*´ ´¶ÀÀÂ¶Æ¶ÊM§*´ è¶ÀÀÌM§»·YÎ·¼*´ ¶ÀÀÂ¶Æ¶ÊM§ã*´ Ú¶ÀÀÂM§Õ*´ ¶ÀÀÂM§Ç*´ ¶ÀÀÂM§¹*´ ¶ÀÀÂM§«*´ ¶ÀÀÂM§*´ ¶ÀÀÂM§*´ Â¶ÀÀÂM§*´ ¾¶ÀÀÂM§s*´  ¶ÀÀÂM§e*´ ~¶ÀÀÂM§W*´ Ò¶ÀÀÂM§I*´ ¼¶ÀÀÂM§;*´ à¶ÀÀÂM§-*´ ®¶ÀÀÂM§*´ ¶¶ÀÀÂM§*´ ª¶ÀÀÂM§*´ æ¶ÀÀÂM§õ*´ ¶ÀÀÂM§ç*´ Î¶ÀÀÂM§Ù*´ ¢¶ÀÀÂM§Ë*´ ê¶ÀÀÂM§½*´ Ì¶ÀÀÂM§¯*´ º¶ÀÀÂM§¡*´ Ø¶ÀÀÂM§*´ Æ¶ÀÀÂM§*´ ä¶ÀÀÂM§w*´ Ä¶ÀÀÂM§i*´ ¶ÀÀÂM§[*´ ¶ÀÀÂM§M*´ ¤¶ÀÀÂM§?*´ ¶ÀÀÂM§1*´ ì¶ÀÀÂM§#*´ Ô¶ÀÀÂM§*´ â¶ÀÀÂM§*´ Ê¶ÀÀÂM§ ù*´ Þ¶ÀÀÂM§ ë*´ ²¶ÀÀÂM§ Ý*´ ¶ÀÀÂM§ Ï*´ Ü¶ÀÀÂM§ Á*´ ì¶ÀÀÂM§ ³*´ ¨¶ÀÀÂM§ ¥*´ ¸¶ÀÀÂM§ *´ À¶ÀÀÂM§ *´ È¶ÀÀÂM§ {*´ ¶ÀÀÂM§ m*´ Ð¶ÀÀÂM§ _*´ ¶ÀÀÂM§ Q*´ Ö¶ÀÀÂM§ C*´ °¶ÀÀÂM§ 5*´ ¦¶ÀÀÂM§ '*´ ¶ÀÀÂM§ *´ ¶ÀÀÌM§ »ÐY·ÑM,°    ÷  
    %(14= @¤I¥L©UªX®a¯d³m´p¸¹½¾Â·ÃºÇÅÈÈÌÓÍÖÑáÒäÖï×òÛýÜ àáåæê'ë*ï5ð8ôCõFùQúTþ_ÿbmp{	~
¥¨³¶!Á"Ä&Ï'Ò+Ý,à0ë1î5ù6ü:;
?@D#E&I1J4N?OBSMTPX[Y^]i^lbwczghlmq¡r¤v¯w²{½|ÀËÎÙÜçêõø"£-¤0¨;©>­I®L²W³Z·e¸h¼s½vÁÂÆÇËÓ ×    t _1545416662313_99423t 2net.sf.jasperreports.engine.design.JRJavacCompiler
<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="NFCe_Pagamentos" language="groovy" pageWidth="581" pageHeight="14" columnWidth="581" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0">
	<property name="ireport.zoom" value="2.0"/>
	<property name="ireport.x" value="230"/>
	<property name="ireport.y" value="0"/>
	<parameter name="SUBREPORT_DIR" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA["C:\\PactoJ\\Desenvolvimento\\Sistemas\\ZillyonWeb\\ZW_Tronco\\src\\main\\resources\\relatorio\\designRelatorio\\nfse\\"]]></defaultValueExpression>
	</parameter>
	<field name="valor" class="java.lang.String"/>
	<field name="descricao" class="java.lang.String"/>
	<detail>
		<band height="13" splitType="Immediate">
			<textField>
				<reportElement x="0" y="0" width="492" height="13"/>
				<textElement textAlignment="Right" verticalAlignment="Middle" markup="none">
					<font isBold="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{descricao}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="492" y="0" width="86" height="13"/>
				<textElement textAlignment="Right" verticalAlignment="Middle" markup="none">
					<font isBold="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{valor}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
</jasperReport>

<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="NFe" pageWidth="595" pageHeight="880" whenNoDataType="AllSectionsNoDetail" columnWidth="591" leftMargin="2" rightMargin="2" topMargin="2" bottomMargin="2" whenResourceMissingType="Empty">
	<property name="ireport.zoom" value="1.5"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="279"/>
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.encoding" value="ISO-8859-1"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="java.util.*"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<parameter name="SUBREPORT_DIR" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA[""]]></defaultValueExpression>
	</parameter>
	<queryString>
		<![CDATA[]]>
	</queryString>
	<field name="razaoSocialPrestador" class="java.lang.String"/>
	<field name="cnpjPrestador" class="java.lang.String"/>
	<field name="inscricaoMunicipalPrestador" class="java.lang.String"/>
	<field name="inscricaoEstadualPrestador" class="java.lang.String"/>
	<field name="enderecoPrestador" class="java.lang.String"/>
	<field name="complementoPrestador" class="java.lang.String"/>
	<field name="municipioPrestador" class="java.lang.String"/>
	<field name="ufPrestador" class="java.lang.String"/>
	<field name="telefonePrestador" class="java.lang.String"/>
	<field name="emailPrestador" class="java.lang.String"/>
	<field name="logomarcaPrestador" class="java.io.InputStream"/>
	<field name="razaoSocialTomador" class="java.lang.String"/>
	<field name="cnpjTomador" class="java.lang.String"/>
	<field name="inscricaoMunicipalTomador" class="java.lang.String"/>
	<field name="inscricaoEstadualTomador" class="java.lang.String"/>
	<field name="enderecoTomador" class="java.lang.String"/>
	<field name="complementoTomador" class="java.lang.String"/>
	<field name="municipioTomador" class="java.lang.String"/>
	<field name="ufTomador" class="java.lang.String"/>
	<field name="telefoneTomador" class="java.lang.String"/>
	<field name="emailTomador" class="java.lang.String"/>
	<field name="logomarcaPrefeitura" class="java.io.InputStream"/>
	<field name="cidadePrestacao" class="java.lang.String"/>
	<field name="numeroNota" class="java.lang.String"/>
	<field name="dataServico" class="java.lang.String"/>
	<field name="dataHoraEmissao" class="java.lang.String"/>
	<field name="codigoAutorizacao" class="java.lang.String"/>
	<field name="dataCompetencia" class="java.lang.String"/>
	<field name="numeroRPS" class="java.lang.String"/>
	<field name="municipioPrestacao" class="java.lang.String"/>
	<field name="servicosDescricao" class="java.lang.String"/>
	<field name="codigoServico" class="java.lang.String"/>
	<field name="valorPIS" class="java.lang.String"/>
	<field name="valorCOFINS" class="java.lang.String"/>
	<field name="valorIR" class="java.lang.String"/>
	<field name="valorINSS" class="java.lang.String"/>
	<field name="valorCSLL" class="java.lang.String"/>
	<field name="valorServicos" class="java.lang.String"/>
	<field name="descontoIncondicionado" class="java.lang.String"/>
	<field name="descontoCondicionado" class="java.lang.String"/>
	<field name="retencoesFederais" class="java.lang.String"/>
	<field name="outrasRetencoes" class="java.lang.String"/>
	<field name="issRetidoValor" class="java.lang.String"/>
	<field name="valorLiquido" class="java.lang.String"/>
	<field name="naturezaOperacao" class="java.lang.String"/>
	<field name="regimeEspecial" class="java.lang.String"/>
	<field name="simplesNacional" class="java.lang.String"/>
	<field name="incentivadorCultural" class="java.lang.String"/>
	<field name="deducoesPermitidas" class="java.lang.String"/>
	<field name="descontoIncondicionadoMunicipio" class="java.lang.String"/>
	<field name="baseCalculo" class="java.lang.String"/>
	<field name="aliquota" class="java.lang.String"/>
	<field name="reterISS" class="java.lang.String"/>
	<field name="valorISS" class="java.lang.String"/>
	<field name="valorTotalNota" class="java.lang.String"/>
	<field name="outrasInformacoes" class="java.lang.String"/>
	<field name="chaveAcesso" class="java.lang.String"/>
	<field name="protocoloAutorizacao" class="java.lang.String"/>
	<field name="dataSaida" class="java.lang.String"/>
	<field name="bairroTomador" class="java.lang.String"/>
	<field name="cepTomador" class="java.lang.String"/>
	<field name="bairroPrestador" class="java.lang.String"/>
	<field name="cepPrestador" class="java.lang.String"/>
	<field name="serieNota" class="java.lang.String"/>
	<field name="dataEmissao" class="java.lang.String"/>
	<field name="horaSaida" class="java.lang.String"/>
	<field name="baseCalculoICMS" class="java.lang.String"/>
	<field name="valorICMS" class="java.lang.String"/>
	<field name="baseCalculoICMSSubs" class="java.lang.String"/>
	<field name="valorICMSSubs" class="java.lang.String"/>
	<field name="valorTributos" class="java.lang.String"/>
	<field name="valorIPI" class="java.lang.String"/>
	<field name="valorIRRF" class="java.lang.String"/>
	<field name="valorFrete" class="java.lang.String"/>
	<field name="valorSeguro" class="java.lang.String"/>
	<field name="valorDesconto" class="java.lang.String"/>
	<field name="valorISSQN" class="java.lang.String"/>
	<field name="valorTotalProdutos" class="java.lang.String"/>
	<field name="valorOutrasDespesas" class="java.lang.String"/>
	<field name="valorTotalServicos" class="java.lang.String"/>
	<field name="produtosJr" class="java.lang.Object"/>
	<field name="observacoesJr" class="java.lang.Object"/>
	<field name="valorBaseCalculoISSQN" class="java.lang.String"/>
	<field name="infAdicinfAdFisco" class="java.lang.String"/>
	<field name="nomeFormaPagamento" class="java.lang.String"/>
	<field name="faturasJr" class="java.lang.Object"/>
	<field name="apresentarFaturas" class="java.lang.Boolean"/>
	<detail>
		<band height="743">
			<rectangle>
				<reportElement x="5" y="185" width="471" height="30"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="341" y="185" width="135" height="30"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="479" y="245" width="107" height="30"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="5" y="245" width="471" height="30"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="176" y="245" width="150" height="30"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="326" y="245" width="37" height="30"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="5" y="5" width="255" height="108"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="374" y="5" width="212" height="108"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="374" y="5" width="212" height="39"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="374" y="78" width="212" height="35"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="5" y="113" width="581" height="30"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="341" y="113" width="245" height="35"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="5" y="142" width="581" height="30"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="5" y="215" width="471" height="30"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="350" y="46" width="19" height="23"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="169" y="142" width="172" height="30"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="341" y="142" width="245" height="30"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="479" y="185" width="107" height="30"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="479" y="215" width="107" height="30"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="5" y="329" width="581" height="30"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="5" y="329" width="92" height="30"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="97" y="329" width="56" height="30"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="153" y="329" width="92" height="30"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<staticText>
				<reportElement key="staticText-16" x="378" y="46" width="105" height="13"/>
				<textElement verticalAlignment="Top">
					<font size="7" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[CHAVE DE ACESSO]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-16" x="378" y="83" width="205" height="13"/>
				<textElement textAlignment="Center" verticalAlignment="Top">
					<font size="8" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Consulta de autenticidade no portal nacional da NF-e]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-16" x="7" y="114" width="109" height="12"/>
				<textElement verticalAlignment="Top">
					<font size="7" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[NATUREZA DA OPERAÇÃO]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-16" x="345" y="114" width="238" height="12"/>
				<textElement verticalAlignment="Top">
					<font size="7" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[PROTOCOLO DE AUTORIZAÇÃO DE USO]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-16" x="5" y="173" width="581" height="13"/>
				<textElement verticalAlignment="Middle">
					<font size="7" isBold="true" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[DESTINATÁRIO / REMETENTE]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-16" x="260" y="5" width="114" height="17"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="12" isBold="true" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[DANFE]]></text>
			</staticText>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-212" x="374" y="60" width="212" height="14" isPrintWhenDetailOverflows="true"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="6" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{chaveAcesso}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-212" x="345" y="127" width="238" height="14"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{protocoloAutorizacao}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-212" x="5" y="8" width="255" height="14"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="7" isBold="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{razaoSocialPrestador}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-212" x="344" y="156" width="239" height="14"/>
				<textElement verticalAlignment="Middle">
					<font size="8" isBold="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{cnpjPrestador}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-212" x="89" y="23" width="167" height="14"/>
				<textElement verticalAlignment="Middle">
					<font size="7" isBold="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{enderecoPrestador}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-212" x="7" y="199" width="330" height="14"/>
				<textElement verticalAlignment="Middle">
					<font size="8" isBold="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{razaoSocialTomador}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-212" x="344" y="199" width="130" height="14"/>
				<textElement verticalAlignment="Middle">
					<font size="8" isBold="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{cnpjTomador}]]></textFieldExpression>
			</textField>
			<rectangle>
				<reportElement x="5" y="287" width="581" height="30">
					<printWhenExpression><![CDATA[$F{apresentarFaturas}.equals(false)]]></printWhenExpression>
				</reportElement>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-212" x="482" y="229" width="101" height="14"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="8" isBold="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{dataSaida}]]></textFieldExpression>
			</textField>
			<rectangle>
				<reportElement x="245" y="287" width="115" height="30">
					<printWhenExpression><![CDATA[$F{apresentarFaturas}.equals(false)]]></printWhenExpression>
				</reportElement>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-212" x="7" y="229" width="203" height="14"/>
				<textElement verticalAlignment="Middle">
					<font size="8" isBold="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{enderecoTomador}]]></textFieldExpression>
			</textField>
			<rectangle>
				<reportElement x="476" y="287" width="110" height="30">
					<printWhenExpression><![CDATA[$F{apresentarFaturas}.equals(false)]]></printWhenExpression>
				</reportElement>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="5" y="359" width="581" height="30"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-212" x="217" y="229" width="175" height="14"/>
				<textElement verticalAlignment="Middle">
					<font size="8" isBold="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{bairroTomador}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-212" x="398" y="229" width="76" height="14"/>
				<textElement verticalAlignment="Middle">
					<font size="8" isBold="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{cepTomador}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-212" x="178" y="258" width="145" height="14"/>
				<textElement verticalAlignment="Middle">
					<font size="8" isBold="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{telefoneTomador}]]></textFieldExpression>
			</textField>
			<image scaleImage="RealHeight" hAlign="Left" isUsingCache="true" onErrorType="Blank" evaluationTime="Report">
				<reportElement key="image-1" isPrintRepeatedValues="false" x="9" y="27" width="77" height="80"/>
				<box>
					<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<imageExpression class="java.io.InputStream"><![CDATA[$F{logomarcaPrestador}]]></imageExpression>
			</image>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-212" x="89" y="37" width="167" height="14"/>
				<textElement verticalAlignment="Middle">
					<font size="7" isBold="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{complementoPrestador}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement key="staticText-16" x="262" y="21" width="112" height="25"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="8" isBold="true" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Documento Auxiliar da Nota Fiscal Eletrônica]]></text>
			</staticText>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-212" x="89" y="52" width="167" height="14"/>
				<textElement verticalAlignment="Middle">
					<font size="7" isBold="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{bairroPrestador}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-212" x="89" y="67" width="167" height="14"/>
				<textElement verticalAlignment="Middle">
					<font size="7" isBold="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{municipioPrestador}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-212" x="89" y="82" width="167" height="14"/>
				<textElement verticalAlignment="Middle">
					<font size="7" isBold="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{cepPrestador}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement key="staticText-16" x="267" y="45" width="76" height="13"/>
				<textElement verticalAlignment="Middle">
					<font size="7" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[0 - ENTRADA]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-16" x="267" y="57" width="76" height="13"/>
				<textElement verticalAlignment="Middle">
					<font size="7" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[1 - SAÍDA]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-16" x="351" y="48" width="16" height="20"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="11" isBold="true" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[1]]></text>
			</staticText>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-212" x="262" y="69" width="112" height="14"/>
				<textElement verticalAlignment="Middle">
					<font size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA["Nº " + $F{numeroNota}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-212" x="262" y="82" width="112" height="14"/>
				<textElement verticalAlignment="Middle">
					<font size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA["SÉRIE " + $F{serieNota}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement key="staticText-16" x="378" y="97" width="205" height="13"/>
				<textElement textAlignment="Center" verticalAlignment="Top">
					<font size="8" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[www.nfe.fazenda.gov.br/portal ou no site da Sefaz Autorizadora]]></text>
			</staticText>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-212" x="7" y="127" width="329" height="14"/>
				<textElement verticalAlignment="Middle">
					<font size="8" isBold="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{naturezaOperacao}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement key="staticText-16" x="7" y="143" width="109" height="12"/>
				<textElement verticalAlignment="Top">
					<font size="7" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[INSCRIÇÃO ESTADUAL]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-16" x="171" y="143" width="170" height="12"/>
				<textElement verticalAlignment="Top">
					<font size="7" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[INSC. ESTADUAL DO SUBST. TRIBUTÁRIO]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-16" x="344" y="143" width="170" height="12"/>
				<textElement verticalAlignment="Top">
					<font size="7" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[CNPJ]]></text>
			</staticText>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-212" x="7" y="156" width="160" height="14"/>
				<textElement verticalAlignment="Middle">
					<font size="8" isBold="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{inscricaoEstadualPrestador}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement key="staticText-16" x="7" y="186" width="109" height="12"/>
				<textElement verticalAlignment="Top">
					<font size="7" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[NOME / RAZÃO SOCIAL]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-16" x="344" y="186" width="109" height="12"/>
				<textElement verticalAlignment="Top">
					<font size="7" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[CNPJ / CPF]]></text>
			</staticText>
			<rectangle>
				<reportElement x="245" y="329" width="110" height="30"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<staticText>
				<reportElement key="staticText-16" x="482" y="186" width="93" height="12"/>
				<textElement verticalAlignment="Top">
					<font size="7" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[DATA DA EMISSÃO]]></text>
			</staticText>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-212" x="482" y="199" width="101" height="14"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="8" isBold="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{dataEmissao}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement key="staticText-16" x="482" y="216" width="101" height="12"/>
				<textElement verticalAlignment="Top">
					<font size="7" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[DATA DA SAIDA / ENTRADA]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-16" x="7" y="216" width="109" height="12"/>
				<textElement verticalAlignment="Top">
					<font size="7" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[ENDEREÇO]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-16" x="217" y="216" width="109" height="12"/>
				<textElement verticalAlignment="Top">
					<font size="7" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[BAIRRO / DISTRITO]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-16" x="398" y="216" width="58" height="12"/>
				<textElement verticalAlignment="Top">
					<font size="7" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[CEP]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-16" x="7" y="245" width="109" height="12"/>
				<textElement verticalAlignment="Top">
					<font size="7" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[MUNICÍPIO]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-16" x="178" y="245" width="109" height="12"/>
				<textElement verticalAlignment="Top">
					<font size="7" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[FONE / FAX]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-16" x="328" y="245" width="27" height="12"/>
				<textElement verticalAlignment="Top">
					<font size="7" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[UF]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-16" x="365" y="245" width="109" height="12"/>
				<textElement verticalAlignment="Top">
					<font size="7" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[INSCRIÇÃO ESTADUAL]]></text>
			</staticText>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-212" x="7" y="258" width="166" height="14"/>
				<textElement verticalAlignment="Middle">
					<font size="8" isBold="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{municipioTomador}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-212" x="328" y="258" width="33" height="14"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="8" isBold="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{ufTomador}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-212" x="364" y="258" width="110" height="14"/>
				<textElement verticalAlignment="Middle">
					<font size="8" isBold="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{inscricaoEstadualTomador}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement key="staticText-16" x="482" y="245" width="101" height="12"/>
				<textElement verticalAlignment="Top">
					<font size="7" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[HORA DE SAÍDA]]></text>
			</staticText>
			<rectangle>
				<reportElement x="167" y="359" width="58" height="30"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="355" y="329" width="121" height="30"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="97" y="359" width="70" height="30"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="225" y="359" width="114" height="30"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="339" y="359" width="72" height="30"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="411" y="359" width="81" height="30"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-212" x="482" y="258" width="101" height="14"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="8" isBold="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{horaSaida}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement key="staticText-16" x="5" y="275" width="581" height="13"/>
				<textElement verticalAlignment="Middle">
					<font size="7" isBold="true" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[FATURA / DUPLICATAS]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-16" x="5" y="317" width="581" height="13"/>
				<textElement verticalAlignment="Middle">
					<font size="7" isBold="true" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[CÁLCULO DO IMPOSTO]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-16" x="7" y="330" width="90" height="12"/>
				<textElement verticalAlignment="Top">
					<font size="6" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[BASE DE CÁLCULO DO ICMS]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-16" x="99" y="330" width="54" height="12"/>
				<textElement verticalAlignment="Top">
					<font size="6" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[VALOR DO ICMS]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-16" x="155" y="330" width="90" height="12"/>
				<textElement verticalAlignment="Top">
					<font size="6" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[BASE CÁLC. DE ICMS SUBS.]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-16" x="249" y="330" width="106" height="12"/>
				<textElement verticalAlignment="Top">
					<font size="6" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[VALOR DO ICMS SUBSTITUIÇÃO ]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-16" x="358" y="330" width="116" height="12"/>
				<textElement verticalAlignment="Top">
					<font size="6" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[VALOR APROXIMADO DOS TRIBUTOS]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-16" x="478" y="330" width="108" height="12"/>
				<textElement verticalAlignment="Top">
					<font size="6" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[VALOR TOTAL DOS PRODUTOS]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-16" x="7" y="360" width="82" height="12"/>
				<textElement verticalAlignment="Top">
					<font size="6" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[VALOR DO FRETE]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-16" x="99" y="360" width="68" height="12"/>
				<textElement verticalAlignment="Top">
					<font size="6" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[VALOR DO SEGURO ]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-16" x="169" y="360" width="48" height="12"/>
				<textElement verticalAlignment="Top">
					<font size="6" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[DESCONTO]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-16" x="228" y="360" width="111" height="12"/>
				<textElement verticalAlignment="Top">
					<font size="6" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[OUTRAS DESPESAS ACESSÓRIAS]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-16" x="341" y="360" width="68" height="12"/>
				<textElement verticalAlignment="Top">
					<font size="6" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[VALOR DO IPI]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-16" x="414" y="360" width="68" height="12"/>
				<textElement verticalAlignment="Top">
					<font size="6" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[VALOR DO IRRF]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-16" x="495" y="360" width="88" height="12"/>
				<textElement verticalAlignment="Top">
					<font size="6" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[VALOR TOTAL DA NOTA]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-16" x="5" y="389" width="581" height="13"/>
				<textElement verticalAlignment="Middle">
					<font size="7" isBold="true" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[TRANSPORTADOR / VOLUMES TRANSPORTADOS]]></text>
			</staticText>
			<rectangle>
				<reportElement x="5" y="402" width="581" height="25"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="5" y="427" width="581" height="25"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="5" y="452" width="581" height="25"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<staticText>
				<reportElement key="staticText-16" x="5" y="477" width="581" height="13"/>
				<textElement verticalAlignment="Middle">
					<font size="7" isBold="true" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[LOCAL DE ENTREGA]]></text>
			</staticText>
			<rectangle>
				<reportElement x="5" y="489" width="581" height="30"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<staticText>
				<reportElement key="staticText-16" x="5" y="519" width="581" height="13"/>
				<textElement verticalAlignment="Middle">
					<font size="7" isBold="true" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[DADOS DO PRODUTO / SERVIÇOS]]></text>
			</staticText>
			<rectangle>
				<reportElement x="5" y="532" width="581" height="30"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="500" y="532" width="86" height="30"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="500" y="545" width="44" height="17"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="544" y="545" width="42" height="17"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<staticText>
				<reportElement key="staticText-16" x="500" y="533" width="86" height="12"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="6" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[ALíQUOTAS]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-16" x="500" y="547" width="44" height="12"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="6" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[ICMS]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-16" x="544" y="547" width="42" height="12"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="6" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[IPI]]></text>
			</staticText>
			<rectangle>
				<reportElement x="5" y="532" width="25" height="30"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="30" y="532" width="137" height="30"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<staticText>
				<reportElement key="staticText-16" x="7" y="537" width="23" height="21"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="6" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[CÓD. PROD.]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-16" x="30" y="538" width="137" height="17"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="6" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[DESCRIÇÃO DO PRODUTO / SERVIÇOS ]]></text>
			</staticText>
			<rectangle>
				<reportElement x="200" y="532" width="24" height="30"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<staticText>
				<reportElement key="staticText-16" x="167" y="538" width="32" height="17"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="6" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[NCM / SH]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-16" x="200" y="538" width="25" height="17"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="6" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[CST]]></text>
			</staticText>
			<rectangle>
				<reportElement x="224" y="532" width="25" height="30"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<staticText>
				<reportElement key="staticText-16" x="224" y="538" width="26" height="17"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="6" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[CFOP]]></text>
			</staticText>
			<rectangle>
				<reportElement x="249" y="532" width="30" height="30"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<staticText>
				<reportElement key="staticText-16" x="249" y="538" width="30" height="17"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="6" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[UNID.]]></text>
			</staticText>
			<rectangle>
				<reportElement x="279" y="532" width="27" height="30"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<staticText>
				<reportElement key="staticText-16" x="280" y="538" width="25" height="17"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="6" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[QTD.]]></text>
			</staticText>
			<rectangle>
				<reportElement x="305" y="532" width="45" height="30"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<staticText>
				<reportElement key="staticText-16" x="306" y="538" width="45" height="17"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="6" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[V. UNITÁRIO]]></text>
			</staticText>
			<rectangle>
				<reportElement x="349" y="532" width="45" height="30"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<staticText>
				<reportElement key="staticText-16" x="349" y="538" width="45" height="17"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="6" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[V. TOTAL]]></text>
			</staticText>
			<rectangle>
				<reportElement x="394" y="532" width="36" height="30"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<staticText>
				<reportElement key="staticText-16" x="394" y="538" width="35" height="17"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="6" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[BC ICMS]]></text>
			</staticText>
			<rectangle>
				<reportElement x="430" y="532" width="36" height="30"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<staticText>
				<reportElement key="staticText-16" x="430" y="538" width="35" height="17"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="6" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[V. ICMS]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-16" x="466" y="538" width="35" height="17"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="6" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[V. IPI]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-16" x="7" y="403" width="109" height="12"/>
				<textElement verticalAlignment="Top">
					<font size="7" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[NOME / RAZÃO SOCIAL]]></text>
			</staticText>
			<rectangle>
				<reportElement x="248" y="402" width="73" height="25"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="99" y="489" width="188" height="30"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="411" y="489" width="175" height="30"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<staticText>
				<reportElement key="staticText-16" x="7" y="490" width="90" height="12"/>
				<textElement verticalAlignment="Top">
					<font size="7" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[CNPJ / CPF]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-16" x="102" y="490" width="90" height="12"/>
				<textElement verticalAlignment="Top">
					<font size="7" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[ENDEREÇO]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-16" x="290" y="490" width="90" height="12"/>
				<textElement verticalAlignment="Top">
					<font size="7" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[BAIRRO / DISTRITO]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-16" x="414" y="490" width="90" height="12"/>
				<textElement verticalAlignment="Top">
					<font size="7" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[MUNICÍPIO]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-16" x="250" y="403" width="71" height="12"/>
				<textElement verticalAlignment="Top">
					<font size="7" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[FRETE POR CONTA]]></text>
			</staticText>
			<rectangle>
				<reportElement x="509" y="402" width="77" height="25"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<staticText>
				<reportElement key="staticText-16" x="511" y="403" width="71" height="12"/>
				<textElement verticalAlignment="Top">
					<font size="7" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[CNPJ / CPF]]></text>
			</staticText>
			<rectangle>
				<reportElement x="482" y="402" width="27" height="25"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<staticText>
				<reportElement key="staticText-16" x="485" y="403" width="19" height="12"/>
				<textElement verticalAlignment="Top">
					<font size="7" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[UF]]></text>
			</staticText>
			<rectangle>
				<reportElement x="392" y="402" width="90" height="25"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<staticText>
				<reportElement key="staticText-16" x="395" y="403" width="71" height="12"/>
				<textElement verticalAlignment="Top">
					<font size="7" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[PLACA DO VEÍCULO]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-16" x="324" y="403" width="68" height="12"/>
				<textElement verticalAlignment="Top">
					<font size="7" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[CÓDIGO ANTT]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-16" x="7" y="428" width="109" height="12"/>
				<textElement verticalAlignment="Top">
					<font size="7" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[ENDEREÇO]]></text>
			</staticText>
			<rectangle>
				<reportElement x="248" y="427" width="161" height="25"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="409" y="427" width="31" height="25"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="5" y="452" width="92" height="25"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="97" y="452" width="92" height="25"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="188" y="452" width="92" height="25"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="280" y="452" width="92" height="25"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="372" y="452" width="92" height="25"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="214" y="215" width="181" height="30"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="130" y="287" width="115" height="30">
					<printWhenExpression><![CDATA[$F{apresentarFaturas}.equals(false)]]></printWhenExpression>
				</reportElement>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<staticText>
				<reportElement key="staticText-16" x="250" y="428" width="109" height="12"/>
				<textElement verticalAlignment="Top">
					<font size="7" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[MUNICÍPIO]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-16" x="412" y="428" width="19" height="12"/>
				<textElement verticalAlignment="Top">
					<font size="7" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[UF]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-16" x="443" y="428" width="138" height="12"/>
				<textElement verticalAlignment="Top">
					<font size="7" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[INSCRIÇÃO ESTADUAL]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-16" x="7" y="453" width="82" height="12"/>
				<textElement verticalAlignment="Top">
					<font size="7" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[QUANTIDADE]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-16" x="100" y="453" width="82" height="12"/>
				<textElement verticalAlignment="Top">
					<font size="7" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[ESPÉCIE]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-16" x="191" y="453" width="82" height="12"/>
				<textElement verticalAlignment="Top">
					<font size="7" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[MARCA]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-16" x="283" y="453" width="82" height="12"/>
				<textElement verticalAlignment="Top">
					<font size="7" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[NUMERAÇÃO]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-16" x="374" y="453" width="82" height="12"/>
				<textElement verticalAlignment="Top">
					<font size="7" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[PESO BRUTO]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-16" x="467" y="453" width="116" height="12"/>
				<textElement verticalAlignment="Top">
					<font size="7" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[PESO LÍQUIDO]]></text>
			</staticText>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-212" x="7" y="343" width="88" height="14"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="8" isBold="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{baseCalculoICMS}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-212" x="99" y="343" width="52" height="14"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="8" isBold="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{valorICMS}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-212" x="156" y="343" width="87" height="14"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="8" isBold="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{baseCalculoICMSSubs}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-212" x="249" y="343" width="104" height="14"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="8" isBold="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{valorICMSSubs}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-212" x="358" y="343" width="116" height="14"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="8" isBold="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{valorTributos}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-212" x="479" y="343" width="105" height="14"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{valorTotalProdutos}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement key="staticText-16" x="250" y="415" width="71" height="12"/>
				<textElement verticalAlignment="Top">
					<font size="7" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[0-Emitente]]></text>
			</staticText>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-212" x="7" y="374" width="87" height="14"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="8" isBold="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{valorFrete}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-212" x="99" y="374" width="65" height="14"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="8" isBold="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{valorSeguro}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-212" x="169" y="374" width="54" height="14"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="8" isBold="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{valorDesconto}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-212" x="228" y="374" width="109" height="14"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="8" isBold="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{valorOutrasDespesas}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-212" x="341" y="374" width="68" height="14"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="8" isBold="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{valorIPI}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-212" x="414" y="374" width="75" height="14"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="8" isBold="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{valorIRRF}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-212" x="495" y="374" width="88" height="14"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{valorTotalNota}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement key="staticText-16" x="262" y="97" width="112" height="14"/>
				<textElement verticalAlignment="Middle">
					<font size="8" isBold="true" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[FOLHA 01/01]]></text>
			</staticText>
			<subreport isUsingCache="false">
				<reportElement x="4" y="562" width="581" height="14"/>
				<dataSourceExpression><![CDATA[$F{produtosJr}]]></dataSourceExpression>
				<subreportExpression class="java.lang.String"><![CDATA[$P{SUBREPORT_DIR} + "NFe_Produtos.jasper"]]></subreportExpression>
			</subreport>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-212" x="89" y="97" width="167" height="14"/>
				<textElement verticalAlignment="Middle">
					<font size="7" isBold="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA["FONE: " + $F{telefonePrestador}]]></textFieldExpression>
			</textField>
			<componentElement>
				<reportElement x="380" y="13" width="201" height="25"/>
				<jr:barbecue xmlns:jr="http://jasperreports.sourceforge.net/jasperreports/components" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports/components http://jasperreports.sourceforge.net/xsd/components.xsd" type="2of7" drawText="false" checksumRequired="false" barWidth="0" barHeight="0">
					<jr:codeExpression><![CDATA[$F{chaveAcesso}]]></jr:codeExpression>
				</jr:barbecue>
			</componentElement>
			<frame>
				<reportElement positionType="Float" x="5" y="576" width="581" height="43"/>
				<rectangle>
					<reportElement x="0" y="13" width="581" height="30"/>
					<graphicElement>
						<pen lineWidth="0.5"/>
					</graphicElement>
				</rectangle>
				<rectangle>
					<reportElement x="433" y="13" width="148" height="30"/>
					<graphicElement>
						<pen lineWidth="0.5"/>
					</graphicElement>
				</rectangle>
				<rectangle>
					<reportElement x="0" y="13" width="148" height="30"/>
					<graphicElement>
						<pen lineWidth="0.5"/>
					</graphicElement>
				</rectangle>
				<rectangle>
					<reportElement x="285" y="13" width="148" height="30"/>
					<graphicElement>
						<pen lineWidth="0.5"/>
					</graphicElement>
				</rectangle>
				<textField isBlankWhenNull="false">
					<reportElement key="textField-212" x="288" y="27" width="142" height="14"/>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font size="8" isBold="false" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$F{valorBaseCalculoISSQN}]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="false">
					<reportElement key="textField-212" x="436" y="27" width="142" height="14"/>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font size="8" isBold="false" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$F{valorISSQN}]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="false">
					<reportElement key="textField-212" x="3" y="27" width="140" height="14"/>
					<textElement verticalAlignment="Middle">
						<font size="8" isBold="false" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$F{inscricaoEstadualPrestador}]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="false">
					<reportElement key="textField-212" x="151" y="27" width="130" height="14"/>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font size="8" isBold="false" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$F{valorTotalServicos}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement key="staticText-16" x="0" y="0" width="581" height="13"/>
					<textElement verticalAlignment="Middle">
						<font size="7" isBold="true" pdfFontName="Helvetica"/>
					</textElement>
					<text><![CDATA[CÁLCULO DO ISSQN]]></text>
				</staticText>
				<staticText>
					<reportElement key="staticText-16" x="4" y="14" width="135" height="12"/>
					<textElement verticalAlignment="Top">
						<font size="7" isBold="false" pdfFontName="Helvetica"/>
					</textElement>
					<text><![CDATA[INSCRIÇÃO MUNICIPAL]]></text>
				</staticText>
				<staticText>
					<reportElement key="staticText-16" x="153" y="13" width="125" height="12"/>
					<textElement verticalAlignment="Top">
						<font size="7" isBold="false" pdfFontName="Helvetica"/>
					</textElement>
					<text><![CDATA[VALOR TOTAL DOS SERVIÇOS]]></text>
				</staticText>
				<staticText>
					<reportElement key="staticText-16" x="289" y="14" width="125" height="12"/>
					<textElement verticalAlignment="Top">
						<font size="7" isBold="false" pdfFontName="Helvetica"/>
					</textElement>
					<text><![CDATA[BASE DE CÁLCULO DO ISSQN]]></text>
				</staticText>
				<staticText>
					<reportElement key="staticText-16" x="437" y="14" width="125" height="12"/>
					<textElement verticalAlignment="Top">
						<font size="7" isBold="false" pdfFontName="Helvetica"/>
					</textElement>
					<text><![CDATA[VALOR DO ISSQN]]></text>
				</staticText>
			</frame>
			<staticText>
				<reportElement key="staticText-16" positionType="Float" x="6" y="623" width="581" height="13" isPrintWhenDetailOverflows="true"/>
				<textElement verticalAlignment="Middle">
					<font size="7" isBold="true" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[DADOS ADICIONAIS]]></text>
			</staticText>
			<rectangle>
				<reportElement positionType="Float" x="6" y="637" width="322" height="88" isPrintWhenDetailOverflows="true"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement positionType="Float" x="326" y="637" width="261" height="88" isPrintWhenDetailOverflows="true"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<staticText>
				<reportElement key="staticText-16" positionType="Float" x="8" y="637" width="171" height="12" isPrintWhenDetailOverflows="true"/>
				<textElement verticalAlignment="Middle">
					<font size="7" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[INFORMAÇÕES COMPLEMENTARES
]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-16" positionType="Float" x="329" y="637" width="171" height="12" isPrintWhenDetailOverflows="true"/>
				<textElement verticalAlignment="Middle">
					<font size="7" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[RESERVADO AO FISCO]]></text>
			</staticText>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-212" x="332" y="649" width="252" height="14"/>
				<textElement verticalAlignment="Middle">
					<font size="8" isBold="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{infAdicinfAdFisco}]]></textFieldExpression>
			</textField>
			<subreport isUsingCache="false">
				<reportElement positionType="Float" x="9" y="649" width="315" height="14" isPrintWhenDetailOverflows="true"/>
				<dataSourceExpression><![CDATA[$F{observacoesJr}]]></dataSourceExpression>
				<subreportExpression class="java.lang.String"><![CDATA[$P{SUBREPORT_DIR} + "NFe_Observacoes.jasper"]]></subreportExpression>
			</subreport>
			<staticText>
				<reportElement key="staticText-16" positionType="Float" x="6" y="726" width="126" height="13" isPrintWhenDetailOverflows="true"/>
				<textElement verticalAlignment="Middle">
					<font size="8" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[DATA E HORA DA IMPRESSÃO:]]></text>
			</staticText>
			<textField pattern="dd/MM/yyyy HH:mm:ss" isBlankWhenNull="true">
				<reportElement key="dataRel-1" positionType="Float" mode="Transparent" x="132" y="726" width="128" height="13" isPrintWhenDetailOverflows="true" backcolor="#FFFFFF"/>
				<box bottomPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement verticalAlignment="Bottom">
					<font fontName="Arial" size="9" isBold="false" isItalic="true" isUnderline="false" pdfFontName="Helvetica-Oblique" isPdfEmbedded="false"/>
				</textElement>
				<textFieldExpression class="java.util.Date"><![CDATA[new Date()]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="7" y="288" width="109" height="12">
					<printWhenExpression><![CDATA[$F{apresentarFaturas}.equals(false)]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font size="7"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{nomeFormaPagamento}]]></textFieldExpression>
			</textField>
			<subreport isUsingCache="false">
				<reportElement x="5" y="287" width="581" height="29">
					<printWhenExpression><![CDATA[$F{apresentarFaturas}.equals(true)]]></printWhenExpression>
				</reportElement>
				<dataSourceExpression><![CDATA[$F{faturasJr}]]></dataSourceExpression>
				<subreportExpression class="java.lang.String"><![CDATA[$P{SUBREPORT_DIR} + "NFe_Faturas.jasper"]]></subreportExpression>
			</subreport>
		</band>
	</detail>
</jasperReport>

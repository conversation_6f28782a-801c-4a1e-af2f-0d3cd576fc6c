<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="NotaManual" pageWidth="595" pageHeight="842" whenNoDataType="AllSectionsNoDetail" columnWidth="591" leftMargin="2" rightMargin="2" topMargin="2" bottomMargin="2" whenResourceMissingType="Empty">
	<property name="ireport.zoom" value="1.5"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="240"/>
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.encoding" value="ISO-8859-1"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="java.util.*"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<queryString>
		<![CDATA[]]>
	</queryString>
	<field name="razaoSocialPrestador" class="java.lang.String"/>
	<field name="cnpjPrestador" class="java.lang.String"/>
	<field name="inscricaoMunicipalPrestador" class="java.lang.String"/>
	<field name="inscricaoEstadualPrestador" class="java.lang.String"/>
	<field name="enderecoPrestador" class="java.lang.String"/>
	<field name="complementoPrestador" class="java.lang.String"/>
	<field name="municipioPrestador" class="java.lang.String"/>
	<field name="ufPrestador" class="java.lang.String"/>
	<field name="telefonePrestador" class="java.lang.String"/>
	<field name="emailPrestador" class="java.lang.String"/>
	<field name="logomarcaPrestador" class="java.io.InputStream"/>
	<field name="razaoSocialTomador" class="java.lang.String"/>
	<field name="cnpjTomador" class="java.lang.String"/>
	<field name="inscricaoMunicipalTomador" class="java.lang.String"/>
	<field name="inscricaoEstadualTomador" class="java.lang.String"/>
	<field name="enderecoTomador" class="java.lang.String"/>
	<field name="complementoTomador" class="java.lang.String"/>
	<field name="municipioTomador" class="java.lang.String"/>
	<field name="ufTomador" class="java.lang.String"/>
	<field name="telefoneTomador" class="java.lang.String"/>
	<field name="emailTomador" class="java.lang.String"/>
	<field name="logomarcaPrefeitura" class="java.io.InputStream"/>
	<field name="cidadePrestacao" class="java.lang.String"/>
	<field name="numeroNota" class="java.lang.String"/>
	<field name="dataServico" class="java.lang.String"/>
	<field name="dataHoraEmissao" class="java.lang.String"/>
	<field name="codigoAutorizacao" class="java.lang.String"/>
	<field name="dataCompetencia" class="java.lang.String"/>
	<field name="numeroRPS" class="java.lang.String"/>
	<field name="municipioPrestacao" class="java.lang.String"/>
	<field name="servicosDescricao" class="java.lang.String"/>
	<field name="codigoServico" class="java.lang.String"/>
	<field name="valorPIS" class="java.lang.String"/>
	<field name="valorCOFINS" class="java.lang.String"/>
	<field name="valorIR" class="java.lang.String"/>
	<field name="valorINSS" class="java.lang.String"/>
	<field name="valorCSLL" class="java.lang.String"/>
	<field name="valorServicos" class="java.lang.String"/>
	<field name="descontoIncondicionado" class="java.lang.String"/>
	<field name="descontoCondicionado" class="java.lang.String"/>
	<field name="retencoesFederais" class="java.lang.String"/>
	<field name="outrasRetencoes" class="java.lang.String"/>
	<field name="issRetidoValor" class="java.lang.String"/>
	<field name="valorLiquido" class="java.lang.String"/>
	<field name="naturezaOperacao" class="java.lang.String"/>
	<field name="regimeEspecial" class="java.lang.String"/>
	<field name="simplesNacional" class="java.lang.String"/>
	<field name="incentivadorCultural" class="java.lang.String"/>
	<field name="deducoesPermitidas" class="java.lang.String"/>
	<field name="descontoIncondicionadoMunicipio" class="java.lang.String"/>
	<field name="baseCalculo" class="java.lang.String"/>
	<field name="aliquota" class="java.lang.String"/>
	<field name="reterISS" class="java.lang.String"/>
	<field name="valorISS" class="java.lang.String"/>
	<field name="valorTotalNota" class="java.lang.String"/>
	<field name="outrasInformacoes" class="java.lang.String"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band splitType="Stretch"/>
	</title>
	<pageHeader>
		<band height="753" splitType="Stretch">
			<rectangle>
				<reportElement x="6" y="12" width="581" height="104"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="380" y="12" width="207" height="104"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="380" y="12" width="207" height="35"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="380" y="81" width="207" height="35"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="6" y="116" width="581" height="35"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="6" y="116" width="97" height="35"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<staticText>
				<reportElement key="staticText-16" x="384" y="14" width="69" height="13"/>
				<textElement verticalAlignment="Top">
					<font size="8" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Número da Nota]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-16" x="518" y="14" width="66" height="13"/>
				<textElement textAlignment="Right" verticalAlignment="Top">
					<font size="8" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Data do Serviço]]></text>
			</staticText>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-212" x="123" y="34" width="237" height="27"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA["Prefeitura de " + $F{cidadePrestacao}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement key="staticText-16" x="384" y="50" width="105" height="13"/>
				<textElement verticalAlignment="Top">
					<font size="8" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Data e Hora da Emissão]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-16" x="384" y="84" width="105" height="13"/>
				<textElement verticalAlignment="Top">
					<font size="8" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Código de Verificação]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-16" x="9" y="118" width="91" height="14"/>
				<textElement verticalAlignment="Top">
					<font size="8" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Competência]]></text>
			</staticText>
			<rectangle>
				<reportElement x="103" y="116" width="111" height="35"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="214" y="116" width="132" height="35"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<staticText>
				<reportElement key="staticText-16" x="106" y="118" width="105" height="14"/>
				<textElement verticalAlignment="Top">
					<font size="8" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Número do RPS]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-16" x="219" y="118" width="122" height="14"/>
				<textElement verticalAlignment="Top">
					<font size="8" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Número da NFSe substituida]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-16" x="350" y="118" width="234" height="14"/>
				<textElement verticalAlignment="Top">
					<font size="8" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Município de prestação do serviço]]></text>
			</staticText>
			<rectangle>
				<reportElement x="6" y="151" width="581" height="95"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<image hAlign="Center" isUsingCache="true" onErrorType="Blank" evaluationTime="Page">
				<reportElement key="image-1" x="10" y="156" width="90" height="85" isPrintWhenDetailOverflows="true"/>
				<box>
					<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<imageExpression class="java.io.InputStream"><![CDATA[$F{logomarcaPrestador}]]></imageExpression>
			</image>
			<staticText>
				<reportElement key="staticText-16" x="109" y="171" width="83" height="14"/>
				<textElement verticalAlignment="Middle">
					<font size="8" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Nome/Razão Social:]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-16" x="109" y="186" width="42" height="14"/>
				<textElement verticalAlignment="Middle">
					<font size="8" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[CPF/CNPJ:]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-16" x="109" y="201" width="42" height="14"/>
				<textElement verticalAlignment="Middle">
					<font size="8" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Endereço:]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-16" x="109" y="215" width="61" height="14"/>
				<textElement verticalAlignment="Middle">
					<font size="8" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Complemento:]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-16" x="109" y="230" width="44" height="14"/>
				<textElement verticalAlignment="Middle">
					<font size="8" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Município:]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-16" x="309" y="186" width="75" height="14"/>
				<textElement verticalAlignment="Middle">
					<font size="8" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Inscrição Municipal:]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-16" x="404" y="215" width="39" height="14"/>
				<textElement verticalAlignment="Middle">
					<font size="8" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Telefone:]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-16" x="272" y="230" width="15" height="14"/>
				<textElement verticalAlignment="Middle">
					<font size="8" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[UF:]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-16" x="310" y="230" width="29" height="14"/>
				<textElement verticalAlignment="Middle">
					<font size="8" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[E-mail:]]></text>
			</staticText>
			<rectangle>
				<reportElement x="6" y="246" width="581" height="95"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<staticText>
				<reportElement key="staticText-16" x="10" y="266" width="80" height="14"/>
				<textElement verticalAlignment="Middle">
					<font size="8" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Nome/Razão Social:]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-16" x="10" y="281" width="42" height="14"/>
				<textElement verticalAlignment="Middle">
					<font size="8" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[CPF/CNPJ:]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-16" x="10" y="296" width="42" height="14"/>
				<textElement verticalAlignment="Middle">
					<font size="8" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Endereço:]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-16" x="10" y="311" width="61" height="14"/>
				<textElement verticalAlignment="Middle">
					<font size="8" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Complemento:]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-16" x="10" y="325" width="42" height="14"/>
				<textElement verticalAlignment="Middle">
					<font size="8" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Município:]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-16" x="318" y="281" width="78" height="14"/>
				<textElement verticalAlignment="Middle">
					<font size="8" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Inscrição Municipal:]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-16" x="318" y="311" width="38" height="14"/>
				<textElement verticalAlignment="Middle">
					<font size="8" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Telefone:]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-16" x="214" y="325" width="15" height="14"/>
				<textElement verticalAlignment="Middle">
					<font size="8" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[UF:]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-16" x="273" y="325" width="32" height="14"/>
				<textElement verticalAlignment="Middle">
					<font size="8" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[E-mail:]]></text>
			</staticText>
			<rectangle>
				<reportElement x="6" y="341" width="581" height="100"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="6" y="439" width="581" height="30"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<staticText>
				<reportElement key="staticText-16" x="10" y="443" width="77" height="14"/>
				<textElement verticalAlignment="Middle">
					<font size="8" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Código do Serviço:]]></text>
			</staticText>
			<rectangle>
				<reportElement x="6" y="468" width="581" height="20"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="6" y="486" width="581" height="34"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="6" y="520" width="581" height="113"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="6" y="486" width="116" height="34"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="122" y="486" width="116" height="34"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="234" y="486" width="116" height="34"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="467" y="486" width="120" height="34"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<staticText>
				<reportElement key="staticText-16" x="63" y="489" width="56" height="13"/>
				<textElement textAlignment="Right" verticalAlignment="Top">
					<font size="8" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[PIS (R$)]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-16" x="174" y="489" width="56" height="13"/>
				<textElement textAlignment="Right" verticalAlignment="Top">
					<font size="8" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[COFINS (R$)]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-16" x="291" y="489" width="56" height="13"/>
				<textElement textAlignment="Right" verticalAlignment="Top">
					<font size="8" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[IR (R$)]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-16" x="407" y="489" width="56" height="13"/>
				<textElement textAlignment="Right" verticalAlignment="Top">
					<font size="8" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[INSS (R$)]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-16" x="527" y="489" width="56" height="13"/>
				<textElement textAlignment="Right" verticalAlignment="Top">
					<font size="8" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[CSLL (R$)]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-16" x="10" y="542" width="73" height="13"/>
				<textElement verticalAlignment="Middle">
					<font size="8" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Valor dos Serviços]]></text>
			</staticText>
			<rectangle>
				<reportElement x="369" y="520" width="218" height="113"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="6" y="520" width="228" height="113"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="6" y="633" width="581" height="33"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<staticText>
				<reportElement key="staticText-16" x="9" y="557" width="114" height="13"/>
				<textElement verticalAlignment="Middle">
					<font size="8" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[(-) Desconto Incondicionado]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-16" x="10" y="572" width="114" height="13"/>
				<textElement verticalAlignment="Middle">
					<font size="8" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[(-) Desconto Condicionado]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-16" x="10" y="587" width="114" height="13"/>
				<textElement verticalAlignment="Middle">
					<font size="8" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[(-) Retenções Federais]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-16" x="10" y="601" width="114" height="13"/>
				<textElement verticalAlignment="Middle">
					<font size="8" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[(-) Outras Retenções]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-16" x="10" y="616" width="114" height="13"/>
				<textElement verticalAlignment="Middle">
					<font size="8" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[(-) ISS Retido]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-16" x="239" y="544" width="125" height="13"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="8" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Natureza da Operação]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-16" x="238" y="584" width="125" height="13"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="8" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Regime Especial de Tributação]]></text>
			</staticText>
			<rectangle>
				<reportElement x="6" y="633" width="228" height="33"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="369" y="633" width="218" height="33"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<staticText>
				<reportElement key="staticText-16" x="236" y="636" width="97" height="13"/>
				<textElement verticalAlignment="Middle">
					<font size="8" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Opção Simples Nacional]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-16" x="236" y="650" width="92" height="13"/>
				<textElement verticalAlignment="Middle">
					<font size="8" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Incentivador Cultural]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-16" x="12" y="643" width="114" height="13"/>
				<textElement verticalAlignment="Middle">
					<font size="8" isBold="true" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[(=) Valor Líquido]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-16" x="375" y="643" width="114" height="13"/>
				<textElement verticalAlignment="Middle">
					<font size="8" isBold="true" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[(=) Valor ISS]]></text>
			</staticText>
			<rectangle>
				<reportElement x="6" y="666" width="581" height="26"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="6" y="692" width="581" height="55"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<staticText>
				<reportElement key="staticText-16" x="109" y="156" width="475" height="13"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="10" isBold="true" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[PRESTADOR DE SERVIÇOS]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-16" x="6" y="251" width="581" height="13"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="10" isBold="true" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[TOMADOR DE SERVIÇOS]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-16" x="6" y="470" width="581" height="13"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="10" isBold="true" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[TRIBUTOS FEDERAIS]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-16" x="234" y="524" width="135" height="13"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="8" isBold="true" isStrikeThrough="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Outras Informações]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-16" x="6" y="524" width="228" height="13"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="8" isBold="true" isStrikeThrough="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Detalhamento de Valores - Prestador dos Serviços]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-16" x="369" y="524" width="218" height="13"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="8" isBold="true" isStrikeThrough="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Cálculo do ISSQN devido no Município]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-16" x="10" y="542" width="114" height="13"/>
				<textElement verticalAlignment="Middle">
					<font size="8" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Valor dos Serviços]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-16" x="6" y="346" width="581" height="13"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="10" isBold="true" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[DISCRIMINAÇÃO DOS SERVIÇOS]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-16" x="109" y="70" width="260" height="25"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="10" isBold="true" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[NOTA FISCAL ELETRÔNICA DE SERVIÇOS - NFS-e]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-16" x="6" y="695" width="581" height="13"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="10" isBold="true" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[OUTRAS INFORMAÇÕES]]></text>
			</staticText>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-212" x="12" y="672" width="571" height="14"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA["VALOR TOTAL DA NOTA = " + $F{valorTotalNota}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement key="staticText-16" x="375" y="542" width="114" height="13"/>
				<textElement verticalAlignment="Middle">
					<font size="8" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Valor dos Serviços]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-16" x="375" y="572" width="114" height="13"/>
				<textElement verticalAlignment="Middle">
					<font size="8" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[(-) Deduções]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-16" x="375" y="557" width="114" height="13"/>
				<textElement verticalAlignment="Middle">
					<font size="8" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[(-) Desconto Incondicionado]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-16" x="375" y="587" width="114" height="13"/>
				<textElement verticalAlignment="Middle">
					<font size="8" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[(=) Base de Cálculo]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-16" x="375" y="601" width="114" height="13"/>
				<textElement verticalAlignment="Middle">
					<font size="8" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[(x) Alíquota (%) ]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-16" x="375" y="616" width="114" height="13"/>
				<textElement verticalAlignment="Middle">
					<font size="8" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[ISS a reter:]]></text>
			</staticText>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-212" x="384" y="97" width="198" height="14"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{codigoAutorizacao}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-212" x="384" y="63" width="198" height="14"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{dataHoraEmissao}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-212" x="384" y="29" width="104" height="14"/>
				<textElement verticalAlignment="Middle">
					<font size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{numeroNota}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-212" x="497" y="29" width="87" height="14"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{dataServico}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-212" x="350" y="134" width="234" height="14"/>
				<textElement verticalAlignment="Middle">
					<font size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{municipioPrestacao}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-212" x="106" y="134" width="105" height="14"/>
				<textElement verticalAlignment="Middle">
					<font size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{numeroRPS}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-212" x="9" y="134" width="91" height="14"/>
				<textElement verticalAlignment="Middle">
					<font size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{dataCompetencia}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-212" x="192" y="171" width="392" height="14"/>
				<textElement verticalAlignment="Middle">
					<font size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{razaoSocialPrestador}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-212" x="151" y="186" width="149" height="14"/>
				<textElement verticalAlignment="Middle">
					<font size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{cnpjPrestador}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-212" x="151" y="201" width="433" height="14"/>
				<textElement verticalAlignment="Middle">
					<font size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{enderecoPrestador}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-212" x="170" y="215" width="230" height="14"/>
				<textElement verticalAlignment="Middle">
					<font size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{complementoPrestador}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-212" x="384" y="186" width="200" height="14"/>
				<textElement verticalAlignment="Middle">
					<font size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{inscricaoMunicipalPrestador}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-212" x="443" y="215" width="141" height="14"/>
				<textElement verticalAlignment="Middle">
					<font size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{telefonePrestador}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-212" x="339" y="230" width="245" height="14"/>
				<textElement verticalAlignment="Middle">
					<font size="7" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{emailPrestador}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-212" x="287" y="230" width="22" height="14"/>
				<textElement verticalAlignment="Middle">
					<font size="7" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{ufPrestador}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-212" x="153" y="230" width="118" height="14"/>
				<textElement verticalAlignment="Middle">
					<font size="7" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{municipioPrestador}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-212" x="90" y="266" width="494" height="14"/>
				<textElement verticalAlignment="Middle">
					<font size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{razaoSocialTomador}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-212" x="54" y="281" width="260" height="14"/>
				<textElement verticalAlignment="Middle">
					<font size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{cnpjTomador}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-212" x="396" y="281" width="188" height="14"/>
				<textElement verticalAlignment="Middle">
					<font size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{inscricaoMunicipalTomador}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-212" x="54" y="296" width="530" height="14"/>
				<textElement verticalAlignment="Middle">
					<font size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{enderecoTomador}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-212" x="71" y="311" width="240" height="14"/>
				<textElement verticalAlignment="Middle">
					<font size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{complementoTomador}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-212" x="50" y="325" width="161" height="14"/>
				<textElement verticalAlignment="Middle">
					<font size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{municipioTomador}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-212" x="231" y="325" width="40" height="14"/>
				<textElement verticalAlignment="Middle">
					<font size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{ufTomador}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-212" x="305" y="325" width="279" height="14"/>
				<textElement verticalAlignment="Middle">
					<font size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{emailTomador}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-212" x="356" y="311" width="228" height="14"/>
				<textElement verticalAlignment="Middle">
					<font size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{telefoneTomador}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-212" x="10" y="362" width="572" height="75"/>
				<textElement verticalAlignment="Top">
					<font size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{servicosDescricao}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-212" x="12" y="504" width="107" height="14"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="8" isBold="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{valorPIS}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-212" x="126" y="504" width="104" height="14"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="8" isBold="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{valorCOFINS}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-212" x="239" y="504" width="108" height="14"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="8" isBold="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{valorIR}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-212" x="356" y="504" width="107" height="14"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="8" isBold="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{valorINSS}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-212" x="477" y="504" width="106" height="14"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="8" isBold="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{valorCSLL}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-212" x="126" y="541" width="104" height="14"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="8" isBold="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{valorServicos}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-212" x="127" y="557" width="102" height="14"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="8" isBold="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{descontoIncondicionado}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-212" x="127" y="573" width="102" height="14"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="8" isBold="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{descontoCondicionado}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-212" x="127" y="587" width="102" height="14"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="8" isBold="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{retencoesFederais}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-212" x="127" y="602" width="102" height="14"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="8" isBold="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{outrasRetencoes}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-212" x="127" y="619" width="102" height="14"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="8" isBold="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{issRetidoValor}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-212" x="239" y="562" width="124" height="14"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="8" isBold="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{naturezaOperacao}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-212" x="239" y="600" width="124" height="29"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="8" isBold="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{regimeEspecial}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-212" x="497" y="540" width="87" height="14"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="8" isBold="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{valorServicos}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-212" x="497" y="572" width="87" height="14"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="8" isBold="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{deducoesPermitidas}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-212" x="497" y="557" width="87" height="14"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="8" isBold="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{descontoIncondicionadoMunicipio}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-212" x="497" y="587" width="87" height="14"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="8" isBold="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{baseCalculo}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-212" x="497" y="601" width="87" height="14"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="8" isBold="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{aliquota}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-212" x="497" y="616" width="87" height="14"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="8" isBold="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{reterISS}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-212" x="497" y="642" width="87" height="14"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{valorISS}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-212" x="10" y="710" width="572" height="35"/>
				<textElement verticalAlignment="Top">
					<font size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{outrasInformacoes}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-212" x="132" y="643" width="97" height="14"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{valorLiquido}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-212" x="333" y="636" width="32" height="14"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{simplesNacional}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-212" x="333" y="650" width="32" height="14"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{incentivadorCultural}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-212" x="87" y="443" width="495" height="14"/>
				<textElement verticalAlignment="Middle">
					<font size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{codigoServico}]]></textFieldExpression>
			</textField>
			<image hAlign="Center" isUsingCache="true" onErrorType="Blank" evaluationTime="Page">
				<reportElement key="image-1" x="10" y="22" width="90" height="85" isPrintWhenDetailOverflows="true"/>
				<box>
					<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<imageExpression class="java.io.InputStream"><![CDATA[$F{logomarcaPrefeitura}]]></imageExpression>
			</image>
		</band>
	</pageHeader>
	<columnHeader>
		<band splitType="Stretch"/>
	</columnHeader>
	<columnFooter>
		<band splitType="Stretch"/>
	</columnFooter>
	<pageFooter>
		<band height="16" splitType="Stretch">
			<textField pattern="dd/MM/yyyy HH:mm:ss" isBlankWhenNull="true">
				<reportElement key="dataRel-1" mode="Opaque" x="133" y="0" width="128" height="13" backcolor="#FFFFFF"/>
				<box bottomPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="false" isItalic="true" isUnderline="false" pdfFontName="Helvetica-Oblique" isPdfEmbedded="false"/>
				</textElement>
				<textFieldExpression class="java.util.Date"><![CDATA[new Date()]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement key="staticText-16" x="6" y="0" width="126" height="13"/>
				<textElement verticalAlignment="Middle">
					<font size="8" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[DATA E HORA DA IMPRESSÃO:]]></text>
			</staticText>
		</band>
	</pageFooter>
</jasperReport>

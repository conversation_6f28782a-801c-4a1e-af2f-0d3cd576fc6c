¬í sr (net.sf.jasperreports.engine.JasperReport      'Ø L compileDatat Ljava/io/Serializable;L compileNameSuffixt Ljava/lang/String;L 
compilerClassq ~ xr -net.sf.jasperreports.engine.base.JRBaseReport      'Ø ,I PSEUDO_SERIAL_VERSION_UIDI bottomMarginI columnCountI 
columnSpacingI columnWidthZ ignorePaginationZ isFloatColumnFooterZ isSummaryNewPageZ  isSummaryWithPageHeaderAndFooterZ isTitleNewPageI 
leftMarginB orientationI 
pageHeightI 	pageWidthB 
printOrderI rightMarginI 	topMarginB whenNoDataTypeL 
backgroundt $Lnet/sf/jasperreports/engine/JRBand;L columnDirectiont 3Lnet/sf/jasperreports/engine/type/RunDirectionEnum;L columnFooterq ~ L columnHeaderq ~ [ datasetst ([Lnet/sf/jasperreports/engine/JRDataset;L defaultFontt *Lnet/sf/jasperreports/engine/JRReportFont;L defaultStylet %Lnet/sf/jasperreports/engine/JRStyle;L detailq ~ L 
detailSectiont 'Lnet/sf/jasperreports/engine/JRSection;[ fontst +[Lnet/sf/jasperreports/engine/JRReportFont;L formatFactoryClassq ~ L 
importsSett Ljava/util/Set;L languageq ~ L lastPageFooterq ~ L mainDatasett 'Lnet/sf/jasperreports/engine/JRDataset;L nameq ~ L noDataq ~ L orientationValuet 2Lnet/sf/jasperreports/engine/type/OrientationEnum;L 
pageFooterq ~ L 
pageHeaderq ~ L printOrderValuet 1Lnet/sf/jasperreports/engine/type/PrintOrderEnum;[ stylest &[Lnet/sf/jasperreports/engine/JRStyle;L summaryq ~ [ 	templatest /[Lnet/sf/jasperreports/engine/JRReportTemplate;L titleq ~ L whenNoDataTypeValuet 5Lnet/sf/jasperreports/engine/type/WhenNoDataTypeEnum;xp  wñ            )           F  S        p~r 1net.sf.jasperreports.engine.type.RunDirectionEnum          xr java.lang.Enum          xpt LTRsr +net.sf.jasperreports.engine.base.JRBaseBand      'Ø I PSEUDO_SERIAL_VERSION_UIDI heightZ isSplitAllowedL printWhenExpressiont *Lnet/sf/jasperreports/engine/JRExpression;L 	splitTypet Ljava/lang/Byte;L splitTypeValuet 0Lnet/sf/jasperreports/engine/type/SplitTypeEnum;xr 3net.sf.jasperreports.engine.base.JRBaseElementGroup      'Ø L childrent Ljava/util/List;L elementGroupt ,Lnet/sf/jasperreports/engine/JRElementGroup;xpsr java.util.ArrayListxÒÇa I sizexp   w   sr +net.sf.jasperreports.engine.base.JRBaseLine      'Ø I PSEUDO_SERIAL_VERSION_UIDB 	directionL directionValuet 4Lnet/sf/jasperreports/engine/type/LineDirectionEnum;xr 5net.sf.jasperreports.engine.base.JRBaseGraphicElement      'Ø I PSEUDO_SERIAL_VERSION_UIDL fillq ~ L 	fillValuet +Lnet/sf/jasperreports/engine/type/FillEnum;L linePent #Lnet/sf/jasperreports/engine/JRPen;L penq ~ xr .net.sf.jasperreports.engine.base.JRBaseElement      'Ø I PSEUDO_SERIAL_VERSION_UIDI heightZ isPrintInFirstWholeBandZ isPrintRepeatedValuesZ isPrintWhenDetailOverflowsZ isRemoveLineWhenBlankB positionTypeB stretchTypeI widthI xI yL 	backcolort Ljava/awt/Color;L defaultStyleProvidert 4Lnet/sf/jasperreports/engine/JRDefaultStyleProvider;L elementGroupq ~ L 	forecolorq ~ 'L keyq ~ L modeq ~ L 	modeValuet +Lnet/sf/jasperreports/engine/type/ModeEnum;L parentStyleq ~ L parentStyleNameReferenceq ~ L positionTypeValuet 3Lnet/sf/jasperreports/engine/type/PositionTypeEnum;L printWhenExpressionq ~ L printWhenGroupChangest %Lnet/sf/jasperreports/engine/JRGroup;L 
propertiesMapt -Lnet/sf/jasperreports/engine/JRPropertiesMap;[ propertyExpressionst 3[Lnet/sf/jasperreports/engine/JRPropertyExpression;L stretchTypeValuet 2Lnet/sf/jasperreports/engine/type/StretchTypeEnum;xp  wñ                pq ~ q ~ pppppp~r 1net.sf.jasperreports.engine.type.PositionTypeEnum          xq ~ t FIX_RELATIVE_TO_TOPpppp~r 0net.sf.jasperreports.engine.type.StretchTypeEnum          xq ~ t 
NO_STRETCH  wîppsr *net.sf.jasperreports.engine.base.JRBasePen      'Ø I PSEUDO_SERIAL_VERSION_UIDL 	lineColorq ~ 'L 	lineStyleq ~ L lineStyleValuet 0Lnet/sf/jasperreports/engine/type/LineStyleEnum;L 	lineWidtht Ljava/lang/Float;L penContainert ,Lnet/sf/jasperreports/engine/JRPenContainer;xp  wñppppq ~ /p  wñ ~r 2net.sf.jasperreports.engine.type.LineDirectionEnum          xq ~ t TOP_DOWNsq ~ !  wñ               pq ~ q ~ ppppppq ~ 1ppppq ~ 4  wîppsq ~ 6  wñppppq ~ >p  wñ q ~ <sr 0net.sf.jasperreports.engine.base.JRBaseTextField      'Ø I PSEUDO_SERIAL_VERSION_UIDI 
bookmarkLevelB evaluationTimeB hyperlinkTargetB 
hyperlinkTypeZ isStretchWithOverflowL anchorNameExpressionq ~ L evaluationGroupq ~ +L evaluationTimeValuet 5Lnet/sf/jasperreports/engine/type/EvaluationTimeEnum;L 
expressionq ~ L hyperlinkAnchorExpressionq ~ L hyperlinkPageExpressionq ~ [ hyperlinkParameterst 3[Lnet/sf/jasperreports/engine/JRHyperlinkParameter;L hyperlinkReferenceExpressionq ~ L hyperlinkTooltipExpressionq ~ L isBlankWhenNullt Ljava/lang/Boolean;L 
linkTargetq ~ L linkTypeq ~ L patternq ~ xr 2net.sf.jasperreports.engine.base.JRBaseTextElement      'Ø %I PSEUDO_SERIAL_VERSION_UIDL borderq ~ L borderColorq ~ 'L bottomBorderq ~ L bottomBorderColorq ~ 'L 
bottomPaddingt Ljava/lang/Integer;L fontNameq ~ L fontSizeq ~ EL horizontalAlignmentq ~ L horizontalAlignmentValuet 6Lnet/sf/jasperreports/engine/type/HorizontalAlignEnum;L isBoldq ~ CL isItalicq ~ CL 
isPdfEmbeddedq ~ CL isStrikeThroughq ~ CL isStyledTextq ~ CL isUnderlineq ~ CL 
leftBorderq ~ L leftBorderColorq ~ 'L leftPaddingq ~ EL lineBoxt 'Lnet/sf/jasperreports/engine/JRLineBox;L lineSpacingq ~ L lineSpacingValuet 2Lnet/sf/jasperreports/engine/type/LineSpacingEnum;L markupq ~ L paddingq ~ EL pdfEncodingq ~ L pdfFontNameq ~ L 
reportFontq ~ L rightBorderq ~ L rightBorderColorq ~ 'L rightPaddingq ~ EL rotationq ~ L 
rotationValuet /Lnet/sf/jasperreports/engine/type/RotationEnum;L 	topBorderq ~ L topBorderColorq ~ 'L 
topPaddingq ~ EL verticalAlignmentq ~ L verticalAlignmentValuet 4Lnet/sf/jasperreports/engine/type/VerticalAlignEnum;xq ~ &  wñ               pq ~ q ~ ppppppq ~ 1ppppq ~ 4  wñppppppsr java.lang.Integerâ ¤÷8 I valuexr java.lang.Number¬à  xp   p~r 4net.sf.jasperreports.engine.type.HorizontalAlignEnum          xq ~ t CENTERsr java.lang.BooleanÍ rÕúî Z valuexpppppppppsr .net.sf.jasperreports.engine.base.JRBaseLineBox      'Ø L 
bottomPaddingq ~ EL 	bottomPent +Lnet/sf/jasperreports/engine/base/JRBoxPen;L boxContainert ,Lnet/sf/jasperreports/engine/JRBoxContainer;L leftPaddingq ~ EL leftPenq ~ UL paddingq ~ EL penq ~ UL rightPaddingq ~ EL rightPenq ~ UL 
topPaddingq ~ EL topPenq ~ Uxppsr 3net.sf.jasperreports.engine.base.JRBaseBoxBottomPen      'Ø  xr -net.sf.jasperreports.engine.base.JRBaseBoxPen      'Ø L lineBoxq ~ Gxq ~ 6  wñppppq ~ Wq ~ Wq ~ Kpsr 1net.sf.jasperreports.engine.base.JRBaseBoxLeftPen      'Ø  xq ~ Y  wñppppq ~ Wq ~ Wpsq ~ Y  wñppppq ~ Wq ~ Wpsr 2net.sf.jasperreports.engine.base.JRBaseBoxRightPen      'Ø  xq ~ Y  wñppppq ~ Wq ~ Wpsr 0net.sf.jasperreports.engine.base.JRBaseBoxTopPen      'Ø  xq ~ Y  wñppppq ~ Wq ~ Wpppppppppppppppp~r 2net.sf.jasperreports.engine.type.VerticalAlignEnum          xq ~ t MIDDLE  wñ        pp~r 3net.sf.jasperreports.engine.type.EvaluationTimeEnum          xq ~ t NOWsr 1net.sf.jasperreports.engine.base.JRBaseExpression      'Ø I id[ chunkst 0[Lnet/sf/jasperreports/engine/JRExpressionChunk;L valueClassNameq ~ L valueClassRealNameq ~ xp   )ur 0[Lnet.sf.jasperreports.engine.JRExpressionChunk;mYÏÞiK£U  xp   sr 6net.sf.jasperreports.engine.base.JRBaseExpressionChunk      'Ø B typeL textq ~ xpt "Cliente: " + sq ~ mt nomet java.lang.Stringppppppppppsq ~ @  wñ           <  è   =pq ~ q ~ pt dataImpressao1p~r )net.sf.jasperreports.engine.type.ModeEnum          xq ~ t TRANSPARENTppq ~ 1ppppq ~ 4  wñpppppt 	SansSerifsq ~ L   pq ~ Ppsq ~ R pppppppsq ~ Tpsq ~ X  wñppppq ~ {q ~ {q ~ spsq ~ [  wñppppq ~ {q ~ {psq ~ Y  wñppppq ~ {q ~ {psq ~ ^  wñppppq ~ {q ~ {psq ~ `  wñppppq ~ {q ~ {pppppt 	Helveticappppppppppq ~ c  wñ        ppq ~ fsq ~ h   *uq ~ k   sq ~ mt 
new Date()t java.util.Dateppppppq ~ zppt dd/MM/yyyy HH:mm:sssq ~ @  wñ                pq ~ q ~ ppppppq ~ 1ppppq ~ 4  wñppppppq ~ Npq ~ Pq ~ Sppppppppsq ~ Tpsq ~ X  wñppppq ~ q ~ q ~ psq ~ [  wñppppq ~ q ~ psq ~ Y  wñppppq ~ q ~ psq ~ ^  wñppppq ~ q ~ psq ~ `  wñppppq ~ q ~ ppppppppppppppppq ~ c  wñ        ppq ~ fsq ~ h   +uq ~ k   sq ~ mt "Resp. OperaÃ§Ã£o: " + sq ~ mt responsavel.nomet java.lang.Stringppppppppppsr 1net.sf.jasperreports.engine.base.JRBaseStaticText      'Ø L textq ~ xq ~ D  wñ           1  ·   =pq ~ q ~ ppppppq ~ 1ppppq ~ 4  wñppppppq ~ yppq ~ Sppppppppsq ~ Tpsq ~ X  wñppppq ~ q ~ q ~ psq ~ [  wñppppq ~ q ~ psq ~ Y  wñppppq ~ q ~ psq ~ ^  wñppppq ~ q ~ psq ~ `  wñppppq ~ q ~ pppppppppppppppppt Data impressÃ£o:sq ~ !  wñ          &       Epq ~ q ~ ppppppq ~ 1ppppq ~ 4  wîppsq ~ 6  wñpp~r .net.sf.jasperreports.engine.type.LineStyleEnum          xq ~ t DASHEDsr java.lang.FloatÚíÉ¢Û<ðì F valuexq ~ M?À  q ~ p  wñ q ~ <xp  wñ   Hppppppppsr .net.sf.jasperreports.engine.base.JRBaseSection      'Ø [ bandst %[Lnet/sf/jasperreports/engine/JRBand;xpur %[Lnet.sf.jasperreports.engine.JRBand;Ý~ìÊ5  xp   sq ~ sq ~    w   sq ~   wñ   
        v      ,pq ~ q ~ «pt 
staticText-85p~q ~ ut OPAQUEpp~q ~ 0t FLOATppppq ~ 4  wñpppppt 	SansSerifsq ~ L   	p~q ~ Ot LEFTq ~ Sq ~ zpq ~ zpq ~ zpppsq ~ Tpsq ~ X  wñsr java.awt.Color¥3u F falphaI valueL cst Ljava/awt/color/ColorSpace;[ 	frgbvaluet [F[ fvalueq ~ »xp    ÿfffpppp~q ~ ¡t SOLIDsq ~ ¤    q ~ ·q ~ ·q ~ ­psq ~ [  wñsq ~ ¹    ÿfffppppq ~ ½sq ~ ¤    q ~ ·q ~ ·psq ~ Y  wñppppq ~ ·q ~ ·psq ~ ^  wñsq ~ ¹    ÿfffppppq ~ ½sq ~ ¤    q ~ ·q ~ ·psq ~ `  wñsq ~ ¹    ÿfffppppq ~ ½sq ~ ¤    q ~ ·q ~ ·pppppt 	Helveticappppppppppq ~ ct 
OperaÃ§Ã£osq ~   wñ   
        a     ,pq ~ q ~ «pt 
staticText-88pq ~ ¯ppq ~ ±ppppq ~ 4  wñpppppt 	SansSerifq ~ ´pq ~ µq ~ Sq ~ zpq ~ zpq ~ zpppsq ~ Tpsq ~ X  wñsq ~ ¹    ÿfffppppq ~ ½sq ~ ¤    q ~ Ïq ~ Ïq ~ Ìpsq ~ [  wñsq ~ ¹    ÿfffppppq ~ ½sq ~ ¤    q ~ Ïq ~ Ïpsq ~ Y  wñppppq ~ Ïq ~ Ïpsq ~ ^  wñsq ~ ¹    ÿfffppppq ~ ½sq ~ ¤    q ~ Ïq ~ Ïpsq ~ `  wñsq ~ ¹    ÿfffppppq ~ ½sq ~ ¤    q ~ Ïq ~ Ïpppppt 	Helveticappppppppppq ~ ct 
 ResponsÃ¡velsr ,net.sf.jasperreports.engine.base.JRBaseImage      'Ø *I PSEUDO_SERIAL_VERSION_UIDI 
bookmarkLevelB evaluationTimeB hyperlinkTargetB 
hyperlinkTypeZ isLazyB onErrorTypeL anchorNameExpressionq ~ L borderq ~ L borderColorq ~ 'L bottomBorderq ~ L bottomBorderColorq ~ 'L 
bottomPaddingq ~ EL evaluationGroupq ~ +L evaluationTimeValueq ~ AL 
expressionq ~ L horizontalAlignmentq ~ L horizontalAlignmentValueq ~ FL hyperlinkAnchorExpressionq ~ L hyperlinkPageExpressionq ~ [ hyperlinkParametersq ~ BL hyperlinkReferenceExpressionq ~ L hyperlinkTooltipExpressionq ~ L isUsingCacheq ~ CL 
leftBorderq ~ L leftBorderColorq ~ 'L leftPaddingq ~ EL lineBoxq ~ GL 
linkTargetq ~ L linkTypeq ~ L onErrorTypeValuet 2Lnet/sf/jasperreports/engine/type/OnErrorTypeEnum;L paddingq ~ EL rightBorderq ~ L rightBorderColorq ~ 'L rightPaddingq ~ EL 
scaleImageq ~ L scaleImageValuet 1Lnet/sf/jasperreports/engine/type/ScaleImageEnum;L 	topBorderq ~ L topBorderColorq ~ 'L 
topPaddingq ~ EL verticalAlignmentq ~ L verticalAlignmentValueq ~ Jxq ~ #  wñ   '       n      pq ~ q ~ «sq ~ ¹    ÿÿÿÿpppt image-1ppppq ~ 1ppppq ~ 4  wîppsq ~ 6  wñppppq ~ âp  wñ         ppppppp~q ~ et PAGEsq ~ h   uq ~ k   sq ~ mt logoPadraoRelatoriot java.io.InputStreamppppppppq ~ Spppsq ~ Tpsq ~ X  wñppppq ~ íq ~ íq ~ âpsq ~ [  wñppppq ~ íq ~ ípsq ~ Y  wñppppq ~ íq ~ ípsq ~ ^  wñppppq ~ íq ~ ípsq ~ `  wñppppq ~ íq ~ ípp~r 0net.sf.jasperreports.engine.type.OnErrorTypeEnum          xq ~ t BLANKpppppppppppsr 0net.sf.jasperreports.engine.base.JRBaseRectangle      'Ø L radiusq ~ Exq ~ #  wñ   '       >   s   sq ~ ¹    ÿðððpppq ~ q ~ «pt retDadosEmpresa1ppppq ~ 1ppppq ~ 4  wîppsq ~ 6  wñpppsq ~ ¤>  q ~ ÷psq ~ L   
sq ~ @  wñ   
        Ü   w   pq ~ q ~ «ppppppq ~ 1ppppq ~ 4  wñpppppt Microsoft Sans Serifpppq ~ Sq ~ Spppppppsq ~ Tpsq ~ X  wñppppq ~ ÿq ~ ÿq ~ ýpsq ~ [  wñppppq ~ ÿq ~ ÿpsq ~ Y  wñppppq ~ ÿq ~ ÿpsq ~ ^  wñppppq ~ ÿq ~ ÿpsq ~ `  wñppppq ~ ÿq ~ ÿpppppt Helvetica-Boldppppppppppp  wñ        ppq ~ fsq ~ h   uq ~ k   sq ~ mt nomeEmpresat java.lang.Stringppppppppppsq ~ @  wñ   
       :   w   pq ~ q ~ «ppppppq ~ 1ppppq ~ 4  wñpppppt Microsoft Sans Serifpppq ~ Sq ~ Spppppppsq ~ Tpsq ~ X  wñppppq ~
q ~
q ~psq ~ [  wñppppq ~
q ~
psq ~ Y  wñppppq ~
q ~
psq ~ ^  wñppppq ~
q ~
psq ~ `  wñppppq ~
q ~
ppppppppppppppppp  wñ        ppq ~ fsq ~ h   
uq ~ k   sq ~ mt empresaVO.enderecot java.lang.Stringppppppppppsq ~ @  wñ   
       :   w   pq ~ q ~ «ppppppq ~ 1ppppq ~ 4  wñpppppt Microsoft Sans Serifpppq ~ Sq ~ Spppppppsq ~ Tpsq ~ X  wñppppq ~q ~q ~psq ~ [  wñppppq ~q ~psq ~ Y  wñppppq ~q ~psq ~ ^  wñppppq ~q ~psq ~ `  wñppppq ~q ~ppppppppppppppppp  wñ        ppq ~ fsq ~ h   uq ~ k   sq ~ mt empresaVO.sitesq ~ mt .toLowerCase()t java.lang.Stringppppppppppsq ~ @  wñ   
        e  L   pq ~ q ~ «ppppppq ~ 1ppppq ~ 4  wñpppppt Microsoft Sans Serifpppq ~ Sq ~ Spppppppsq ~ Tpsq ~ X  wñppppq ~)q ~)q ~'psq ~ [  wñppppq ~)q ~)psq ~ Y  wñppppq ~)q ~)psq ~ ^  wñppppq ~)q ~)psq ~ `  wñppppq ~)q ~)pppppt Helvetica-Boldppppppppppp  wñ        ppq ~ fsq ~ h   uq ~ k   sq ~ mt empresaVO.cnpjt java.lang.Stringppppppppppsq ~ ö  wñ   '        q  µ   sq ~ ¹    ÿðððpppq ~ q ~ «pt retDadosRecibo1ppppq ~ 1ppppq ~ 4  wîppsq ~ 6  wñpppsq ~ ¤>  q ~5pq ~ üsq ~ @  wñ   
        `  Q   pq ~ q ~ «ppppppq ~ 1ppppq ~ 4  wñpppppt Microsoft Sans Serifpppq ~ Sq ~ Spppppppsq ~ Tpsq ~ X  wñppppq ~<q ~<q ~:psq ~ [  wñppppq ~<q ~<psq ~ Y  wñppppq ~<q ~<psq ~ ^  wñppppq ~<q ~<psq ~ `  wñppppq ~<q ~<ppppppppppppppppp  wñ        ppq ~ fsq ~ h   uq ~ k   sq ~ mt empresaVO.fonet java.lang.Stringppppppppppsq ~ !  wñ          (      =pq ~ q ~ «ppppppq ~ 1ppppq ~ 4  wîppsq ~ 6  wñppppq ~Gp  wñ q ~ <sq ~ @  wñ                >pq ~ q ~ «pt 
textField-229pq ~ ¯ppq ~ 1ppppq ~ 4  wñpppppt Microsoft Sans Serifq ~ ´pq ~ µq ~ Sppppppppsq ~ Tpsq ~ X  wñsq ~ ¹    ÿfffppppq ~ ½sq ~ ¤    q ~Lq ~Lq ~Ipsq ~ [  wñsq ~ ¹    ÿfffppppq ~ ½sq ~ ¤    q ~Lq ~Lpsq ~ Y  wñppppq ~Lq ~Lpsq ~ ^  wñsq ~ ¹    ÿfffppppq ~ ½sq ~ ¤    q ~Lq ~Lpsq ~ `  wñsq ~ ¹    ÿfffppppq ~ ½sq ~ ¤    q ~Lq ~Lpppppt Helvetica-Boldppppppppppq ~ c  wñ        ppq ~ fsq ~ h   uq ~ k   sq ~ mt responsavel.nomet java.lang.Stringppppppq ~ zpppsq ~ @  wñ           P   k   >pq ~ q ~ «pt 
textField-229pq ~ ¯ppq ~ 1ppppq ~ 4  wñpppppt Microsoft Sans Serifq ~ ´pq ~ µq ~ Sppppppppsq ~ Tpsq ~ X  wñsq ~ ¹    ÿfffppppq ~ ½sq ~ ¤    q ~cq ~cq ~`psq ~ [  wñsq ~ ¹    ÿfffppppq ~ ½sq ~ ¤    q ~cq ~cpsq ~ Y  wñppppq ~cq ~cpsq ~ ^  wñsq ~ ¹    ÿfffppppq ~ ½sq ~ ¤    q ~cq ~cpsq ~ `  wñsq ~ ¹    ÿfffppppq ~ ½sq ~ ¤    q ~cq ~cpppppt Helvetica-Boldppppppppppq ~ c  wñ        ppq ~ fsq ~ h   uq ~ k   sq ~ mt contratot java.lang.Integerppppppq ~ zpppsq ~ @  wñ          n  ·   sq ~ ¹    ÿðððpppq ~ q ~ «pt 
textField-229pq ~ ¯ppq ~ 1pppp~q ~ 3t RELATIVE_TO_TALLEST_OBJECT  wñpppppt Microsoft Sans Serifsq ~ L   pq ~ Pq ~ Sppppppppsq ~ Tpsq ~ X  wñsq ~ ¹    ÿfffppppq ~ ½sq ~ ¤    q ~~q ~~q ~wpsq ~ [  wñsq ~ ¹    ÿfffppppq ~ ½sq ~ ¤    q ~~q ~~psq ~ Y  wñppppq ~~q ~~psq ~ ^  wñsq ~ ¹    ÿfffppppq ~ ½sq ~ ¤    q ~~q ~~psq ~ `  wñsq ~ ¹    ÿfffppppq ~ ½sq ~ ¤    q ~~q ~~pppppt Helvetica-Boldppppppppppq ~ c  wñ       ppq ~ fsq ~ h   uq ~ k   sq ~ mt tipoOperacao_Apresentart java.lang.Stringppppppq ~ zpppsq ~   wñ   
        U   »   ,pq ~ q ~ «pt 
staticText-85pq ~ ¯ppq ~ ±ppppq ~ 4  wñpppppt 	SansSerifq ~ ´pq ~ µq ~ Sq ~ zpq ~ zpq ~ zpppsq ~ Tpsq ~ X  wñsq ~ ¹    ÿfffppppq ~ ½sq ~ ¤    q ~q ~q ~psq ~ [  wñsq ~ ¹    ÿfffppppq ~ ½sq ~ ¤    q ~q ~psq ~ Y  wñppppq ~q ~psq ~ ^  wñsq ~ ¹    ÿfffppppq ~ ½sq ~ ¤    q ~q ~psq ~ `  wñsq ~ ¹    ÿfffppppq ~ ½sq ~ ¤    q ~q ~pppppt 	Helveticappppppppppq ~ ct Data LanÃ§amentosq ~   wñ   
        A     ,pq ~ q ~ «pt 
staticText-85pq ~ ¯ppq ~ ±ppppq ~ 4  wñpppppt 	SansSerifq ~ ´pq ~ µq ~ Sq ~ zpq ~ zpq ~ zpppsq ~ Tpsq ~ X  wñsq ~ ¹    ÿfffppppq ~ ½sq ~ ¤    q ~¨q ~¨q ~¥psq ~ [  wñsq ~ ¹    ÿfffppppq ~ ½sq ~ ¤    q ~¨q ~¨psq ~ Y  wñppppq ~¨q ~¨psq ~ ^  wñsq ~ ¹    ÿfffppppq ~ ½sq ~ ¤    q ~¨q ~¨psq ~ `  wñsq ~ ¹    ÿfffppppq ~ ½sq ~ ¤    q ~¨q ~¨pppppt 	Helveticappppppppppq ~ ct Data InÃ­ciosq ~   wñ   
        A  V   ,pq ~ q ~ «pt 
staticText-85pq ~ ¯ppq ~ ±ppppq ~ 4  wñpppppt 	SansSerifq ~ ´pq ~ µq ~ Sq ~ zpq ~ zpq ~ zpppsq ~ Tpsq ~ X  wñsq ~ ¹    ÿfffppppq ~ ½sq ~ ¤    q ~»q ~»q ~¸psq ~ [  wñsq ~ ¹    ÿfffppppq ~ ½sq ~ ¤    q ~»q ~»psq ~ Y  wñppppq ~»q ~»psq ~ ^  wñsq ~ ¹    ÿfffppppq ~ ½sq ~ ¤    q ~»q ~»psq ~ `  wñsq ~ ¹    ÿfffppppq ~ ½sq ~ ¤    q ~»q ~»pppppt 	Helveticappppppppppq ~ ct 
Data Finalsq ~ @  wñ           A  V   >pq ~ q ~ «pt 
textField-229pq ~ ¯ppq ~ 1ppppq ~ 4  wñpppppt Microsoft Sans Serifq ~ ´pq ~ µq ~ Sppppppppsq ~ Tpsq ~ X  wñsq ~ ¹    ÿfffppppq ~ ½sq ~ ¤    q ~Îq ~Îq ~Ëpsq ~ [  wñsq ~ ¹    ÿfffppppq ~ ½sq ~ ¤    q ~Îq ~Îpsq ~ Y  wñppppq ~Îq ~Îpsq ~ ^  wñsq ~ ¹    ÿfffppppq ~ ½sq ~ ¤    q ~Îq ~Îpsq ~ `  wñsq ~ ¹    ÿfffppppq ~ ½sq ~ ¤    q ~Îq ~Îpppppt Helvetica-Boldppppppppppq ~ c  wñ        ppq ~ fsq ~ h   uq ~ k   sq ~ mt $dataFimEfetivacaoOperacao_Apresentart java.lang.Stringppppppq ~ zppt  sq ~ @  wñ           A     >pq ~ q ~ «pt 
textField-229pq ~ ¯ppq ~ 1ppppq ~ 4  wñpppppt Microsoft Sans Serifq ~ ´pq ~ µq ~ Sppppppppsq ~ Tpsq ~ X  wñsq ~ ¹    ÿfffppppq ~ ½sq ~ ¤    q ~æq ~æq ~ãpsq ~ [  wñsq ~ ¹    ÿfffppppq ~ ½sq ~ ¤    q ~æq ~æpsq ~ Y  wñppppq ~æq ~æpsq ~ ^  wñsq ~ ¹    ÿfffppppq ~ ½sq ~ ¤    q ~æq ~æpsq ~ `  wñsq ~ ¹    ÿfffppppq ~ ½sq ~ ¤    q ~æq ~æpppppt Helvetica-Boldppppppppppq ~ c  wñ        ppq ~ fsq ~ h   uq ~ k   sq ~ mt 'dataInicioEfetivacaoOperacao_Apresentart java.lang.Stringppppppq ~ zppq ~âsq ~ @  wñ           U   ¾   >pq ~ q ~ «pt 
textField-229pq ~ ¯ppq ~ 1ppppq ~ 4  wñpppppt Microsoft Sans Serifq ~ ´pq ~ µq ~ Sppppppppsq ~ Tpsq ~ X  wñsq ~ ¹    ÿfffppppq ~ ½sq ~ ¤    q ~ýq ~ýq ~úpsq ~ [  wñsq ~ ¹    ÿfffppppq ~ ½sq ~ ¤    q ~ýq ~ýpsq ~ Y  wñppppq ~ýq ~ýpsq ~ ^  wñsq ~ ¹    ÿfffppppq ~ ½sq ~ ¤    q ~ýq ~ýpsq ~ `  wñsq ~ ¹    ÿfffppppq ~ ½sq ~ ¤    q ~ýq ~ýpppppt Helvetica-Boldppppppppppq ~ c  wñ        ppq ~ fsq ~ h   uq ~ k   sq ~ mt dataOperacao_Apresentart java.lang.Stringppppppq ~ zppq ~âsq ~ @  wñ          `      >pq ~ q ~ «pt 
textField-229pq ~ ¯ppq ~ 1ppppq ~ 4  wñpppppt Microsoft Sans Serifq ~ ´pq ~ µq ~ Sppppppppsq ~ Tpsq ~ X  wñsq ~ ¹    ÿfffppppq ~ ½sq ~ ¤    q ~q ~q ~psq ~ [  wñsq ~ ¹    ÿfffppppq ~ ½sq ~ ¤    q ~q ~psq ~ Y  wñppppq ~q ~psq ~ ^  wñsq ~ ¹    ÿfffppppq ~ ½sq ~ ¤    q ~q ~psq ~ `  wñsq ~ ¹    ÿfffppppq ~ ½sq ~ ¤    q ~q ~pppppt Helvetica-Boldppppppppppq ~ c  wñ       ppq ~ fsq ~ h   uq ~ k   sq ~ mt tipoOperacao_Apresentart java.lang.Stringppppppq ~ zpppsq ~   wñ   
        P   k   ,pq ~ q ~ «pt 
staticText-85pq ~ ¯ppq ~ ±ppppq ~ 4  wñpppppt 	SansSerifq ~ ´pq ~ µq ~ Sq ~ zpq ~ zpq ~ zpppsq ~ Tpsq ~ X  wñsq ~ ¹    ÿfffppppq ~ ½sq ~ ¤    q ~+q ~+q ~(psq ~ [  wñsq ~ ¹    ÿfffppppq ~ ½sq ~ ¤    q ~+q ~+psq ~ Y  wñppppq ~+q ~+psq ~ ^  wñsq ~ ¹    ÿfffppppq ~ ½sq ~ ¤    q ~+q ~+psq ~ `  wñsq ~ ¹    ÿfffppppq ~ ½sq ~ ¤    q ~+q ~+pppppt 	Helveticappppppppppq ~ ct Contratoxp  wñ   Kpppsq ~ sq ~    w   sq ~ @  wñ   
             $pq ~ q ~;ppppppq ~ 1pppp~q ~ 3t RELATIVE_TO_BAND_HEIGHT  wñppppppq ~ Np~q ~ Ot 	JUSTIFIEDpppppppppsq ~ Tpsq ~ X  wñppppq ~Bq ~Bq ~=psq ~ [  wñppppq ~Bq ~Bpsq ~ Y  wñppppq ~Bq ~Bpsq ~ ^  wñppppq ~Bq ~Bpsq ~ `  wñppppq ~Bq ~Bppppppppppppppppp  wñ       ppq ~ fsq ~ h   uq ~ k   sq ~ mt descricaoCalculot java.lang.Stringppppppppppsq ~   wñ   
       Z      pq ~ q ~;pt 
staticText-85pq ~ ¯ppq ~ ±sq ~ h   uq ~ k   sq ~ mt !sq ~ mt justificativaApresentarsq ~ mt 
.equals(null)t java.lang.Booleanppppq ~ 4  wñpppppt 	SansSerifq ~ ´p~q ~ Ot RIGHTq ~ Sq ~ zpq ~ zpq ~ zpppsq ~ Tpsq ~ X  wñsq ~ ¹    ÿfffppppq ~ ½sq ~ ¤    q ~[q ~[q ~Mpsq ~ [  wñsq ~ ¹    ÿfffppppq ~ ½sq ~ ¤    q ~[q ~[psq ~ Y  wñppppq ~[q ~[psq ~ ^  wñsq ~ ¹    ÿfffppppq ~ ½sq ~ ¤    q ~[q ~[psq ~ `  wñsq ~ ¹    ÿfffppppq ~ ½sq ~ ¤    q ~[q ~[pppppt Helvetica-Boldppppppppppq ~ ct Justificativa:sq ~ @  wñ   
      Ä   e   pq ~ q ~;ppppppq ~ 1sq ~ h   uq ~ k   sq ~ mt !sq ~ mt justificativaApresentarsq ~ mt 
.equals(null)q ~Wppppq ~>  wñppppppppq ~@pppppppppsq ~ Tpsq ~ X  wñppppq ~tq ~tq ~kpsq ~ [  wñppppq ~tq ~tpsq ~ Y  wñppppq ~tq ~tpsq ~ ^  wñppppq ~tq ~tpsq ~ `  wñppppq ~tq ~tppppppppppppppppp  wñ       ppq ~ fsq ~ h   uq ~ k   sq ~ mt justificativaApresentart java.lang.Stringppppppq ~ Spppsq ~ @  wñ   
        ò  3   $pq ~ q ~;ppppppq ~ 1ppppq ~>  wñppppppq ~ Npq ~@pppppppppsq ~ Tpsq ~ X  wñppppq ~q ~q ~psq ~ [  wñppppq ~q ~psq ~ Y  wñppppq ~q ~psq ~ ^  wñppppq ~q ~psq ~ `  wñppppq ~q ~ppppppppppppppppp  wñ       ppq ~ fsq ~ h   uq ~ k   sq ~ mt 
observacaot java.lang.Stringppppppppppsq ~   wñ   
        Z     pq ~ q ~;pt 
staticText-85pq ~ ¯ppq ~ 1ppppq ~ 4  wñpppppt 	SansSerifq ~ ´pq ~Yq ~ Sq ~ zpq ~ zpq ~ zpppsq ~ Tpsq ~ X  wñsq ~ ¹    ÿfffppppq ~ ½sq ~ ¤    q ~q ~q ~psq ~ [  wñsq ~ ¹    ÿfffppppq ~ ½sq ~ ¤    q ~q ~psq ~ Y  wñppppq ~q ~psq ~ ^  wñsq ~ ¹    ÿfffppppq ~ ½sq ~ ¤    q ~q ~psq ~ `  wñsq ~ ¹    ÿfffppppq ~ ½sq ~ ¤    q ~q ~pppppt Helvetica-Boldppppppppppq ~ ct ObservaÃ§Ãµes:sq ~   wñ   
        Z      pq ~ q ~;pt 
staticText-85pq ~ ¯ppq ~ ±ppppq ~ 4  wñpppppt 	SansSerifq ~ ´pq ~Yq ~ Sq ~ zpq ~ zpq ~ zpppsq ~ Tpsq ~ X  wñsq ~ ¹    ÿfffppppq ~ ½sq ~ ¤    q ~¡q ~¡q ~psq ~ [  wñsq ~ ¹    ÿfffppppq ~ ½sq ~ ¤    q ~¡q ~¡psq ~ Y  wñppppq ~¡q ~¡psq ~ ^  wñsq ~ ¹    ÿfffppppq ~ ½sq ~ ¤    q ~¡q ~¡psq ~ `  wñsq ~ ¹    ÿfffppppq ~ ½sq ~ ¤    q ~¡q ~¡pppppt Helvetica-Boldppppppppppq ~ ct DescriÃ§Ã£o CÃ¡lculo:xp  wñ   7pppsq ~ sq ~    w   sq ~   wñ   
        l      pq ~ q ~±pt 
staticText-85pq ~ ¯ppq ~ ±sq ~ h   uq ~ k   sq ~ mt !reciboDevolucao.apresentarChequesq ~Wppppq ~ 4  wñpppppt 	SansSerifq ~ ´pq ~ µq ~ Sq ~ zpq ~ zpq ~ Spppsq ~ Tpsq ~ X  wñsq ~ ¹    ÿfffppppq ~ ½sq ~ ¤    q ~ºq ~ºq ~³psq ~ [  wñsq ~ ¹    ÿfffppppq ~ ½sq ~ ¤    q ~ºq ~ºpsq ~ Y  wñppppq ~ºq ~ºpsq ~ ^  wñsq ~ ¹    ÿfffppppq ~ ½sq ~ ¤    q ~ºq ~ºpsq ~ `  wñsq ~ ¹    ÿfffppppq ~ ½sq ~ ¤    q ~ºq ~ºpppppt 	Helveticappppppppppq ~ ct Cheques devolvidossr 0net.sf.jasperreports.engine.base.JRBaseSubreport      'Ø L connectionExpressionq ~ L dataSourceExpressionq ~ L 
expressionq ~ L isUsingCacheq ~ C[ 
parameterst 3[Lnet/sf/jasperreports/engine/JRSubreportParameter;L parametersMapExpressionq ~ [ returnValuest 5[Lnet/sf/jasperreports/engine/JRSubreportReturnValue;L runToBottomq ~ Cxq ~ &  wñ   "            pq ~ q ~±pt subreport-1ppppq ~ 1sq ~ h   uq ~ k   sq ~ mt !reciboDevolucao.apresentarChequesq ~Wppppq ~ 4psq ~ h   "uq ~ k   sq ~ mt reciboDevolucao.listaChequet (net.sf.jasperreports.engine.JRDataSourcepsq ~ h   #uq ~ k   sq ~ mt SUBREPORT_DIR2sq ~ mt   + "MovPagamento_cheques.jasper"t java.lang.Stringpq ~ Sur 3[Lnet.sf.jasperreports.engine.JRSubreportParameter;[£À¾B  xp   sr 9net.sf.jasperreports.engine.base.JRBaseSubreportParameter      'Ø  xr 7net.sf.jasperreports.engine.base.JRBaseDatasetParameter      'Ø L 
expressionq ~ L nameq ~ xpsq ~ h    uq ~ k   sq ~ mt 
SUBREPORT_DIRt java.lang.Objectpt 
SUBREPORT_DIRsq ~ásq ~ h   !uq ~ k   sq ~ mt reciboDevolucao.listaChequeq ~èpt ListaChequepppxp  wñ   6sq ~ h   uq ~ k   sq ~ mt !reciboDevolucao.apresentarChequesq ~Wpppsq ~ sq ~    w   sq ~   wñ   
        `      pq ~ q ~ôpt 
staticText-85pq ~ ¯ppq ~ 1sq ~ h   %uq ~ k   sq ~ mt !reciboDevolucao.apresentarCartoesq ~Wppppq ~ 4  wñpppppt 	SansSerifq ~ ´pq ~ µq ~ Sq ~ zpq ~ zpq ~ Spppsq ~ Tpsq ~ X  wñsq ~ ¹    ÿfffppppq ~ ½sq ~ ¤    q ~ýq ~ýq ~öpsq ~ [  wñsq ~ ¹    ÿfffppppq ~ ½sq ~ ¤    q ~ýq ~ýpsq ~ Y  wñppppq ~ýq ~ýpsq ~ ^  wñsq ~ ¹    ÿfffppppq ~ ½sq ~ ¤    q ~ýq ~ýpsq ~ `  wñsq ~ ¹    ÿfffppppq ~ ½sq ~ ¤    q ~ýq ~ýpppppt 	Helveticappppppppppq ~ ct CartÃµes estornadossq ~Ê  wñ                pq ~ q ~ôpt subreport-2ppppq ~ 1sq ~ h   &uq ~ k   sq ~ mt !reciboDevolucao.apresentarCartoesq ~Wppppq ~ 4psq ~ h   'uq ~ k   sq ~ mt reciboDevolucao.listaCartoesq ~×psq ~ h   (uq ~ k   sq ~ mt SUBREPORT_DIR2sq ~ mt & + "MovPagamento_cartaocredito.jasper"t java.lang.Stringppppppxp  wñ   3sq ~ h   $uq ~ k   sq ~ mt !reciboDevolucao.apresentarCartoesq ~Wpppppsr java.util.HashSetºD¸·4  xpw   ?@     t "net.sf.jasperreports.engine.data.*t net.sf.jasperreports.engine.*t java.util.*xt javapsr .net.sf.jasperreports.engine.base.JRBaseDataset      'Ø I PSEUDO_SERIAL_VERSION_UIDZ isMainB whenResourceMissingType[ fieldst &[Lnet/sf/jasperreports/engine/JRField;L filterExpressionq ~ [ groupst &[Lnet/sf/jasperreports/engine/JRGroup;L nameq ~ [ 
parameterst *[Lnet/sf/jasperreports/engine/JRParameter;L 
propertiesMapq ~ ,L queryt %Lnet/sf/jasperreports/engine/JRQuery;L resourceBundleq ~ L scriptletClassq ~ [ 
scriptletst *[Lnet/sf/jasperreports/engine/JRScriptlet;[ 
sortFieldst *[Lnet/sf/jasperreports/engine/JRSortField;[ 	variablest )[Lnet/sf/jasperreports/engine/JRVariable;L whenResourceMissingTypeValuet >Lnet/sf/jasperreports/engine/type/WhenResourceMissingTypeEnum;xp  wñ ur &[Lnet.sf.jasperreports.engine.JRField;<ßÇN*òp  xp   sr ,net.sf.jasperreports.engine.base.JRBaseField      'Ø L descriptionq ~ L nameq ~ L 
propertiesMapq ~ ,L valueClassNameq ~ L valueClassRealNameq ~ xppt tipoOperacao_Apresentarsr +net.sf.jasperreports.engine.JRPropertiesMap      'Ø L baseq ~ ,L propertiesListq ~ L 
propertiesMapt Ljava/util/Map;xppppt java.lang.Stringpsq ~4pt dataOperacao_Apresentarsq ~7pppt java.lang.Stringpsq ~4pt 'dataInicioEfetivacaoOperacao_Apresentarsq ~7pppt java.lang.Stringpsq ~4pt $dataFimEfetivacaoOperacao_Apresentarsq ~7pppt java.lang.Stringpsq ~4pt responsavel.nomesq ~7pppt java.lang.Stringpsq ~4pt contratosq ~7pppt java.lang.Integerpsq ~4pt justificativaApresentarsq ~7pppt java.lang.Stringpsq ~4pt 
observacaosq ~7pppt java.lang.Stringpsq ~4pt descricaoCalculosq ~7pppt java.lang.Stringpsq ~4pt nomesq ~7pppt java.lang.Stringpsq ~4pt reciboDevolucao.listaChequesq ~7pppt java.lang.Objectpsq ~4pt reciboDevolucao.listaCartoessq ~7pppt java.lang.Objectpsq ~4pt !reciboDevolucao.apresentarCartoessq ~7pppt java.lang.Booleanpsq ~4pt !reciboDevolucao.apresentarChequessq ~7pppt java.lang.Booleanpppt ComprovanteOperacaoContratour *[Lnet.sf.jasperreports.engine.JRParameter;" *Ã`!  xp   'sr 0net.sf.jasperreports.engine.base.JRBaseParameter      'Ø 	Z isForPromptingZ isSystemDefinedL defaultValueExpressionq ~ L descriptionq ~ L nameq ~ L nestedTypeNameq ~ L 
propertiesMapq ~ ,L valueClassNameq ~ L valueClassRealNameq ~ xpppt REPORT_PARAMETERS_MAPpsq ~7pppt 
java.util.Mappsq ~rppt 
JASPER_REPORTpsq ~7pppt (net.sf.jasperreports.engine.JasperReportpsq ~rppt REPORT_CONNECTIONpsq ~7pppt java.sql.Connectionpsq ~rppt REPORT_MAX_COUNTpsq ~7pppt java.lang.Integerpsq ~rppt REPORT_DATA_SOURCEpsq ~7pppq ~×psq ~rppt REPORT_SCRIPTLETpsq ~7pppt /net.sf.jasperreports.engine.JRAbstractScriptletpsq ~rppt 
REPORT_LOCALEpsq ~7pppt java.util.Localepsq ~rppt REPORT_RESOURCE_BUNDLEpsq ~7pppt java.util.ResourceBundlepsq ~rppt REPORT_TIME_ZONEpsq ~7pppt java.util.TimeZonepsq ~rppt REPORT_FORMAT_FACTORYpsq ~7pppt .net.sf.jasperreports.engine.util.FormatFactorypsq ~rppt REPORT_CLASS_LOADERpsq ~7pppt java.lang.ClassLoaderpsq ~rppt REPORT_URL_HANDLER_FACTORYpsq ~7pppt  java.net.URLStreamHandlerFactorypsq ~rppt REPORT_FILE_RESOLVERpsq ~7pppt -net.sf.jasperreports.engine.util.FileResolverpsq ~rppt REPORT_TEMPLATESpsq ~7pppt java.util.Collectionpsq ~rppt SORT_FIELDSpsq ~7pppt java.util.Listpsq ~rppt REPORT_VIRTUALIZERpsq ~7pppt )net.sf.jasperreports.engine.JRVirtualizerpsq ~rppt IS_IGNORE_PAGINATIONpsq ~7pppq ~Wpsq ~r  ppt tituloRelatoriopsq ~7pppt java.lang.Stringpsq ~r  ppt nomeEmpresapsq ~7pppt java.lang.Stringpsq ~r  ppt versaoSoftwarepsq ~7pppt java.lang.Stringpsq ~r  ppt usuariopsq ~7pppt java.lang.Stringpsq ~r sq ~ h    uq ~ k   sq ~ mt ^"C:\\PactoJ\\ZillyonWebTronco\\src\\main\\resources\\relatorio\\designRelatorio\\financeiro\\"t java.lang.Stringppt 
SUBREPORT_DIRpsq ~7pppq ~Êpsq ~r sq ~ h   uq ~ k   sq ~ mt p"E:\\desenvolvimentosv\\Projeto Pacto Solucoes\\ZillyonWeb\\src\\java\\relatorio\\designRelatorio\\financeiro\\"t java.lang.Stringppt SUBREPORT_DIR1psq ~7pppq ~Òpsq ~r sq ~ h   uq ~ k   sq ~ mt z"C:\\PactoJ\\Desenvolvimento\\Sistemas\\ZillyonWeb\\trunk\\src\\main\\resources\\relatorio\\designRelatorio\\financeiro\\"t java.lang.Stringppt SUBREPORT_DIR2psq ~7pppq ~Úpsq ~r ppt empresaVO.cnpjpsq ~7pppt java.lang.Stringpsq ~r ppt empresaVO.enderecopsq ~7pppt java.lang.Stringpsq ~r ppt empresaVO.sitepsq ~7pppt java.lang.Stringpsq ~r ppt empresaVO.fonepsq ~7pppt java.lang.Stringpsq ~r  ppt logoPadraoRelatoriopsq ~7pppt java.io.InputStreampsq ~r  ppt 'dataInicioEfetivacaoOperacao_Apresentarpsq ~7pppt java.lang.Stringpsq ~r  ppt $dataFimEfetivacaoOperacao_Apresentarpsq ~7pppt java.lang.Stringpsq ~r  ppt dataOperacao_Apresentarpsq ~7pppt java.lang.Stringpsq ~r  ppt responsavel.nomepsq ~7pppt java.lang.Stringpsq ~r  ppt tipoOperacao_Apresentarpsq ~7pppt java.lang.Stringpsq ~r  ppt justificativaApresentarpsq ~7pppt java.lang.Stringpsq ~r  ppt 
observacaopsq ~7pppt java.lang.Stringpsq ~r  ppt descricaoCalculopsq ~7pppt java.lang.Stringpsq ~r ppt pessoapsq ~7pppt java.lang.Stringpsq ~r ppt contratopsq ~7pppt java.lang.Integerpsq ~7psq ~    w   t ireport.zoomt 	ireport.xt 	ireport.yt ireport.scriptlethandlingt ireport.encodingxsr java.util.HashMapÚÁÃ`Ñ F 
loadFactorI 	thresholdxp?@     w      q ~t 1.5q ~t 
ISO-8859-1q ~t 0q ~t 0q ~t 0xpppppur )[Lnet.sf.jasperreports.engine.JRVariable;bæ|,·D  xp   sr /net.sf.jasperreports.engine.base.JRBaseVariable      'Ø I PSEUDO_SERIAL_VERSION_UIDB calculationB 
incrementTypeZ isSystemDefinedB 	resetTypeL calculationValuet 2Lnet/sf/jasperreports/engine/type/CalculationEnum;L 
expressionq ~ L incrementGroupq ~ +L incrementTypeValuet 4Lnet/sf/jasperreports/engine/type/IncrementTypeEnum;L incrementerFactoryClassNameq ~ L incrementerFactoryClassRealNameq ~ L initialValueExpressionq ~ L nameq ~ L 
resetGroupq ~ +L resetTypeValuet 0Lnet/sf/jasperreports/engine/type/ResetTypeEnum;L valueClassNameq ~ L valueClassRealNameq ~ xp  wî   ~r 0net.sf.jasperreports.engine.type.CalculationEnum          xq ~ t SYSTEMpp~r 2net.sf.jasperreports.engine.type.IncrementTypeEnum          xq ~ t NONEppsq ~ h   uq ~ k   sq ~ mt new java.lang.Integer(1)q ~pt PAGE_NUMBERp~r .net.sf.jasperreports.engine.type.ResetTypeEnum          xq ~ t REPORTq ~psq ~)  wî   q ~/ppq ~2ppsq ~ h   uq ~ k   sq ~ mt new java.lang.Integer(1)q ~pt 
COLUMN_NUMBERp~q ~9t PAGEq ~psq ~)  wî   ~q ~.t COUNTsq ~ h   uq ~ k   sq ~ mt new java.lang.Integer(1)q ~ppq ~2ppsq ~ h   uq ~ k   sq ~ mt new java.lang.Integer(0)q ~pt REPORT_COUNTpq ~:q ~psq ~)  wî   q ~Esq ~ h   uq ~ k   sq ~ mt new java.lang.Integer(1)q ~ppq ~2ppsq ~ h   uq ~ k   sq ~ mt new java.lang.Integer(0)q ~pt 
PAGE_COUNTpq ~Bq ~psq ~)  wî   q ~Esq ~ h   	uq ~ k   sq ~ mt new java.lang.Integer(1)q ~ppq ~2ppsq ~ h   
uq ~ k   sq ~ mt new java.lang.Integer(0)q ~pt COLUMN_COUNTp~q ~9t COLUMNq ~p~r <net.sf.jasperreports.engine.type.WhenResourceMissingTypeEnum          xq ~ t EMPTYq ~op~r 0net.sf.jasperreports.engine.type.OrientationEnum          xq ~ t PORTRAITpp~r /net.sf.jasperreports.engine.type.PrintOrderEnum          xq ~ t VERTICALpppp~r 3net.sf.jasperreports.engine.type.WhenNoDataTypeEnum          xq ~ t NO_PAGESsr 6net.sf.jasperreports.engine.design.JRReportCompileData      'Ø L crosstabCompileDataq ~8L datasetCompileDataq ~8L mainDatasetCompileDataq ~ xpsq ~ ?@     w       xsq ~ ?@     w       xur [B¬óøTà  xp  -Êþº¾   . 0ComprovanteOperacaoContrato_1548435002278_711566  ,net/sf/jasperreports/engine/fill/JREvaluator  parameter_descricaoCalculo 2Lnet/sf/jasperreports/engine/fill/JRFillParameter; !parameter_justificativaApresentar parameter_JASPER_REPORT parameter_REPORT_TIME_ZONE parameter_usuario parameter_REPORT_FILE_RESOLVER !parameter_tipoOperacao_Apresentar parameter_REPORT_PARAMETERS_MAP parameter_SUBREPORT_DIR1 parameter_REPORT_CLASS_LOADER $parameter_REPORT_URL_HANDLER_FACTORY parameter_REPORT_DATA_SOURCE parameter_contrato parameter_IS_IGNORE_PAGINATION parameter_SUBREPORT_DIR2 parameter_REPORT_MAX_COUNT parameter_empresaVO46endereco parameter_REPORT_TEMPLATES 1parameter_dataInicioEfetivacaoOperacao_Apresentar .parameter_dataFimEfetivacaoOperacao_Apresentar parameter_REPORT_LOCALE parameter_REPORT_VIRTUALIZER parameter_SORT_FIELDS parameter_logoPadraoRelatorio parameter_pessoa parameter_REPORT_SCRIPTLET parameter_REPORT_CONNECTION parameter_responsavel46nome parameter_observacao parameter_SUBREPORT_DIR parameter_empresaVO46cnpj parameter_REPORT_FORMAT_FACTORY parameter_tituloRelatorio parameter_empresaVO46site parameter_nomeEmpresa !parameter_dataOperacao_Apresentar parameter_empresaVO46fone  parameter_REPORT_RESOURCE_BUNDLE parameter_versaoSoftware (field_reciboDevolucao46apresentarCheques .Lnet/sf/jasperreports/engine/fill/JRFillField; field_descricaoCalculo #field_reciboDevolucao46listaCartoes field_justificativaApresentar (field_reciboDevolucao46apresentarCartoes "field_reciboDevolucao46listaCheque -field_dataInicioEfetivacaoOperacao_Apresentar field_tipoOperacao_Apresentar *field_dataFimEfetivacaoOperacao_Apresentar 
field_nome field_responsavel46nome field_dataOperacao_Apresentar field_observacao field_contrato variable_PAGE_NUMBER 1Lnet/sf/jasperreports/engine/fill/JRFillVariable; variable_COLUMN_NUMBER variable_REPORT_COUNT variable_PAGE_COUNT variable_COLUMN_COUNT <init> ()V Code B C
  E  	  G  	  I  	  K 	 	  M 
 	  O  	  Q  	  S 
 	  U  	  W  	  Y  	  [  	  ]  	  _  	  a  	  c  	  e  	  g  	  i  	  k  	  m  	  o  	  q  	  s  	  u  	  w  	  y   	  { ! 	  } " 	   # 	   $ 	   % 	   & 	   ' 	   ( 	   ) 	   * 	   + 	   , 	   - .	   / .	   0 .	   1 .	   2 .	   3 .	   4 .	  ¡ 5 .	  £ 6 .	  ¥ 7 .	  § 8 .	  © 9 .	  « : .	  ­ ; .	  ¯ < =	  ± > =	  ³ ? =	  µ @ =	  · A =	  ¹ LineNumberTable customizedInit 0(Ljava/util/Map;Ljava/util/Map;Ljava/util/Map;)V 
initParams (Ljava/util/Map;)V ¾ ¿
  À 
initFields Â ¿
  Ã initVars Å ¿
  Æ descricaoCalculo È 
java/util/Map Ê get &(Ljava/lang/Object;)Ljava/lang/Object; Ì Í Ë Î 0net/sf/jasperreports/engine/fill/JRFillParameter Ð justificativaApresentar Ò 
JASPER_REPORT Ô REPORT_TIME_ZONE Ö usuario Ø REPORT_FILE_RESOLVER Ú tipoOperacao_Apresentar Ü REPORT_PARAMETERS_MAP Þ SUBREPORT_DIR1 à REPORT_CLASS_LOADER â REPORT_URL_HANDLER_FACTORY ä REPORT_DATA_SOURCE æ contrato è IS_IGNORE_PAGINATION ê SUBREPORT_DIR2 ì REPORT_MAX_COUNT î empresaVO.endereco ð REPORT_TEMPLATES ò 'dataInicioEfetivacaoOperacao_Apresentar ô $dataFimEfetivacaoOperacao_Apresentar ö 
REPORT_LOCALE ø REPORT_VIRTUALIZER ú SORT_FIELDS ü logoPadraoRelatorio þ pessoa  REPORT_SCRIPTLET REPORT_CONNECTION responsavel.nome 
observacao 
SUBREPORT_DIR
 empresaVO.cnpj REPORT_FORMAT_FACTORY tituloRelatorio empresaVO.site nomeEmpresa dataOperacao_Apresentar empresaVO.fone REPORT_RESOURCE_BUNDLE versaoSoftware !reciboDevolucao.apresentarCheques ,net/sf/jasperreports/engine/fill/JRFillField  reciboDevolucao.listaCartoes" !reciboDevolucao.apresentarCartoes$ reciboDevolucao.listaCheque& nome( PAGE_NUMBER* /net/sf/jasperreports/engine/fill/JRFillVariable, 
COLUMN_NUMBER. REPORT_COUNT0 
PAGE_COUNT2 COLUMN_COUNT4 evaluate (I)Ljava/lang/Object; 
Exceptions java/lang/Throwable9 SC:\PactoJ\ZillyonWebTronco\src\main\resources\relatorio\designRelatorio\financeiro\; eE:\desenvolvimentosv\Projeto Pacto Solucoes\ZillyonWeb\src\java\relatorio\designRelatorio\financeiro\= lC:\PactoJ\Desenvolvimento\Sistemas\ZillyonWeb\trunk\src\main\resources\relatorio\designRelatorio\financeiro\? java/lang/IntegerA (I)V BC
BD getValue ()Ljava/lang/Object;FG
 ÑH java/io/InputStreamJ java/lang/StringL toLowerCase ()Ljava/lang/String;NO
MP
!H equals (Ljava/lang/Object;)ZST
MU java/lang/BooleanW valueOf (Z)Ljava/lang/Boolean;YZ
X[ (net/sf/jasperreports/engine/JRDataSource] java/lang/StringBuffer_ &(Ljava/lang/Object;)Ljava/lang/String;Ya
Mb (Ljava/lang/String;)V Bd
`e MovPagamento_cheques.jasperg append ,(Ljava/lang/String;)Ljava/lang/StringBuffer;ij
`k toStringmO
`n !MovPagamento_cartaocredito.jasperp 	Cliente: r java/util/Datet
u E Resp. OperaÃ§Ã£o: w evaluateOld getOldValuezG
!{ evaluateEstimated 
SourceFile !     :                 	     
               
                                                                                                     !     "     #     $     %     &     '     (     )     *     +     ,     - .    / .    0 .    1 .    2 .    3 .    4 .    5 .    6 .    7 .    8 .    9 .    : .    ; .    < =    > =    ? =    @ =    A =     B C  D  +    '*· F*µ H*µ J*µ L*µ N*µ P*µ R*µ T*µ V*µ X*µ Z*µ \*µ ^*µ `*µ b*µ d*µ f*µ h*µ j*µ l*µ n*µ p*µ r*µ t*µ v*µ x*µ z*µ |*µ ~*µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ  *µ ¢*µ ¤*µ ¦*µ ¨*µ ª*µ ¬*µ ®*µ °*µ ²*µ ´*µ ¶*µ ¸*µ º±    »   ò <      	          ! " " ' # , $ 1 % 6 & ; ' @ ( E ) J * O + T , Y - ^ . c / h 0 m 1 r 2 w 3 | 4  5  6  7  8  9  :  ; ¤ < © = ® > ³ ? ¸ @ ½ A Â B Ç C Ì D Ñ E Ö F Û G à H å I ê J ï K ô L ù M þ N O P
 Q R S T! U&   ¼ ½  D   4     *+· Á*,· Ä*-· Ç±    »       a  b 
 c  d  ¾ ¿  D      Î*+É¹ Ï À ÑÀ Ñµ H*+Ó¹ Ï À ÑÀ Ñµ J*+Õ¹ Ï À ÑÀ Ñµ L*+×¹ Ï À ÑÀ Ñµ N*+Ù¹ Ï À ÑÀ Ñµ P*+Û¹ Ï À ÑÀ Ñµ R*+Ý¹ Ï À ÑÀ Ñµ T*+ß¹ Ï À ÑÀ Ñµ V*+á¹ Ï À ÑÀ Ñµ X*+ã¹ Ï À ÑÀ Ñµ Z*+å¹ Ï À ÑÀ Ñµ \*+ç¹ Ï À ÑÀ Ñµ ^*+é¹ Ï À ÑÀ Ñµ `*+ë¹ Ï À ÑÀ Ñµ b*+í¹ Ï À ÑÀ Ñµ d*+ï¹ Ï À ÑÀ Ñµ f*+ñ¹ Ï À ÑÀ Ñµ h*+ó¹ Ï À ÑÀ Ñµ j*+õ¹ Ï À ÑÀ Ñµ l*+÷¹ Ï À ÑÀ Ñµ n*+ù¹ Ï À ÑÀ Ñµ p*+û¹ Ï À ÑÀ Ñµ r*+ý¹ Ï À ÑÀ Ñµ t*+ÿ¹ Ï À ÑÀ Ñµ v*+¹ Ï À ÑÀ Ñµ x*+¹ Ï À ÑÀ Ñµ z*+¹ Ï À ÑÀ Ñµ |*+¹ Ï À ÑÀ Ñµ ~*+	¹ Ï À ÑÀ Ñµ *+¹ Ï À ÑÀ Ñµ *+
¹ Ï À ÑÀ Ñµ *+¹ Ï À ÑÀ Ñµ *+¹ Ï À ÑÀ Ñµ *+¹ Ï À ÑÀ Ñµ *+¹ Ï À ÑÀ Ñµ *+¹ Ï À ÑÀ Ñµ *+¹ Ï À ÑÀ Ñµ *+¹ Ï À ÑÀ Ñµ *+¹ Ï À ÑÀ Ñµ ±    »   ¢ (   l  m $ n 6 o H p Z q l r ~ s  t ¢ u ´ v Æ w Ø x ê y ü z {  |2 }D ~V h z   ° Ã Ö é ü  " 5 H [ n   § º Í   Â ¿  D  U    *+¹ Ï À!À!µ *+É¹ Ï À!À!µ *+#¹ Ï À!À!µ *+Ó¹ Ï À!À!µ *+%¹ Ï À!À!µ *+'¹ Ï À!À!µ  *+õ¹ Ï À!À!µ ¢*+Ý¹ Ï À!À!µ ¤*+÷¹ Ï À!À!µ ¦*+)¹ Ï À!À!µ ¨*+¹ Ï À!À!µ ª*+¹ Ï À!À!µ ¬*+	¹ Ï À!À!µ ®*+é¹ Ï À!À!µ °±    »   >       %  8  J  ]   p ¡  ¢  £ ¦ ¤ ¹ ¥ Ì ¦ ß § ò ¨ ©  Å ¿  D        `*++¹ Ï À-À-µ ²*+/¹ Ï À-À-µ ´*+1¹ Ï À-À-µ ¶*+3¹ Ï À-À-µ ¸*+5¹ Ï À-À-µ º±    »       ±  ² & ³ 9 ´ L µ _ ¶ 67 8    : D  ß    cMª  ^       +   ½   Ä   Ë   Ò   Þ   ê   ö        &  2  @  N  \  m  {      ¥  ³  Á  Ï  Ý  ë  ù    3  A  O  ]  k  y         Á  Ï  Ý  ë  ù    8  C<M§>M§@M§»BY·EM§»BY·EM§w»BY·EM§k»BY·EM§_»BY·EM§S»BY·EM§G»BY·EM§;»BY·EM§/*´ v¶IÀKM§!*´ ¶IÀMM§*´ h¶IÀMM§*´ ¶IÀM¶QM§ô*´ ¶IÀMM§æ*´ ¶IÀMM§Ø*´ ª¶RÀMM§Ê*´ °¶RÀBM§¼*´ ¤¶RÀMM§®*´ ¦¶RÀMM§ *´ ¢¶RÀMM§*´ ¬¶RÀMM§*´ ¤¶RÀMM§v*´ ¶RÀMM§h*´ ¶RÀM¶V § ¸\M§K*´ ¶RÀM¶V § ¸\M§.*´ ¶RÀMM§ *´ ®¶RÀMM§*´ ¶RÀXM§*´ ¶RÀXM§ ö*´ ¶RÀXM§ è*´ ¶IÀMM§ Ú*´  ¶RM§ Ï*´  ¶RÀ^M§ Á»`Y*´ d¶IÀM¸c·fh¶l¶oM§  *´ ¶RÀXM§ *´ ¶RÀXM§ *´ ¶RÀXM§ v*´ ¶RÀ^M§ h»`Y*´ d¶IÀM¸c·fq¶l¶oM§ G»`Ys·f*´ ¨¶RÀM¶l¶oM§ )»uY·vM§ »`Yx·f*´ ª¶RÀM¶l¶oM,°    »  j Z   ¾  À À Ä Ä Å Ç É Ë Ê Î Î Ò Ï Õ Ó Þ Ô á Ø ê Ù í Ý ö Þ ù â ã ç è ì í ñ& ò) ö2 ÷5 û@ üC NQ\_
mp{~¥¨#³$¶(Á)Ä-Ï.Ò2Ý3à7ë8î<ù=üABF3G6KALDPOQRU]V`Zk[n_y`|deijn o£sÁtÄxÏyÒ}Ý~àëîùü8;CFa£ y7 8    : D  ß    cMª  ^       +   ½   Ä   Ë   Ò   Þ   ê   ö        &  2  @  N  \  m  {      ¥  ³  Á  Ï  Ý  ë  ù    3  A  O  ]  k  y         Á  Ï  Ý  ë  ù    8  C<M§>M§@M§»BY·EM§»BY·EM§w»BY·EM§k»BY·EM§_»BY·EM§S»BY·EM§G»BY·EM§;»BY·EM§/*´ v¶IÀKM§!*´ ¶IÀMM§*´ h¶IÀMM§*´ ¶IÀM¶QM§ô*´ ¶IÀMM§æ*´ ¶IÀMM§Ø*´ ª¶|ÀMM§Ê*´ °¶|ÀBM§¼*´ ¤¶|ÀMM§®*´ ¦¶|ÀMM§ *´ ¢¶|ÀMM§*´ ¬¶|ÀMM§*´ ¤¶|ÀMM§v*´ ¶|ÀMM§h*´ ¶|ÀM¶V § ¸\M§K*´ ¶|ÀM¶V § ¸\M§.*´ ¶|ÀMM§ *´ ®¶|ÀMM§*´ ¶|ÀXM§*´ ¶|ÀXM§ ö*´ ¶|ÀXM§ è*´ ¶IÀMM§ Ú*´  ¶|M§ Ï*´  ¶|À^M§ Á»`Y*´ d¶IÀM¸c·fh¶l¶oM§  *´ ¶|ÀXM§ *´ ¶|ÀXM§ *´ ¶|ÀXM§ v*´ ¶|À^M§ h»`Y*´ d¶IÀM¸c·fq¶l¶oM§ G»`Ys·f*´ ¨¶|ÀM¶l¶oM§ )»uY·vM§ »`Yx·f*´ ª¶|ÀM¶l¶oM,°    »  j Z  ¬ ® À² Ä³ Ç· Ë¸ Î¼ Ò½ ÕÁ ÞÂ áÆ êÇ íË öÌ ùÐÑÕÖÚÛß&à)ä2å5é@êCîNïQó\ô_ømùpý{þ~¥
¨³¶ÁÄÏÒ Ý!à%ë&î*ù+ü/043569A:D>O?RC]D`HkInMyN|RSWX\ ]£aÁbÄfÏgÒkÝlàpëqîuùvüz{8;CFa }7 8    : D  ß    cMª  ^       +   ½   Ä   Ë   Ò   Þ   ê   ö        &  2  @  N  \  m  {      ¥  ³  Á  Ï  Ý  ë  ù    3  A  O  ]  k  y         Á  Ï  Ý  ë  ù    8  C<M§>M§@M§»BY·EM§»BY·EM§w»BY·EM§k»BY·EM§_»BY·EM§S»BY·EM§G»BY·EM§;»BY·EM§/*´ v¶IÀKM§!*´ ¶IÀMM§*´ h¶IÀMM§*´ ¶IÀM¶QM§ô*´ ¶IÀMM§æ*´ ¶IÀMM§Ø*´ ª¶RÀMM§Ê*´ °¶RÀBM§¼*´ ¤¶RÀMM§®*´ ¦¶RÀMM§ *´ ¢¶RÀMM§*´ ¬¶RÀMM§*´ ¤¶RÀMM§v*´ ¶RÀMM§h*´ ¶RÀM¶V § ¸\M§K*´ ¶RÀM¶V § ¸\M§.*´ ¶RÀMM§ *´ ®¶RÀMM§*´ ¶RÀXM§*´ ¶RÀXM§ ö*´ ¶RÀXM§ è*´ ¶IÀMM§ Ú*´  ¶RM§ Ï*´  ¶RÀ^M§ Á»`Y*´ d¶IÀM¸c·fh¶l¶oM§  *´ ¶RÀXM§ *´ ¶RÀXM§ *´ ¶RÀXM§ v*´ ¶RÀ^M§ h»`Y*´ d¶IÀM¸c·fq¶l¶oM§ G»`Ys·f*´ ¨¶RÀM¶l¶oM§ )»uY·vM§ »`Yx·f*´ ª¶RÀM¶l¶oM,°    »  j Z    À  Ä¡ Ç¥ Ë¦ Îª Ò« Õ¯ Þ° á´ êµ í¹ öº ù¾¿ÃÄÈÉÍ&Î)Ò2Ó5×@ØCÜNÝQá\â_æmçpë{ì~ðñõöú¥û¨ÿ³ ¶ÁÄ	Ï
ÒÝàëîùü"3#6'A(D,O-R1]2`6k7n;y<|@AEFJ K£OÁPÄTÏUÒYÝZà^ë_îcùdühim8n;rCsFwa ~    t _1548435002278_711566t 2net.sf.jasperreports.engine.design.JRJavacCompiler
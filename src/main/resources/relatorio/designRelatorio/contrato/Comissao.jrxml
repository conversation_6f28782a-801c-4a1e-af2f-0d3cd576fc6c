<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="ComissaoRel" pageWidth="878" pageHeight="680" orientation="Landscape" whenNoDataType="AllSectionsNoDetail" columnWidth="823" leftMargin="20" rightMargin="35" topMargin="2" bottomMargin="2" whenResourceMissingType="Empty">
	<property name="ireport.zoom" value="1.768460768250001"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.encoding" value="ISO-8859-1"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="java.util.*"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<subDataset name="Teste"/>
	<parameter name="tituloRelatorio" class="java.lang.String" isForPrompting="false"/>
	<parameter name="nomeEmpresa" class="java.lang.String" isForPrompting="false"/>
	<parameter name="usuario" class="java.lang.String" isForPrompting="false"/>
	<parameter name="filtros" class="java.lang.String" isForPrompting="false"/>
	<parameter name="SUBREPORT_DIR" class="java.lang.String">
		<defaultValueExpression><![CDATA["D:\\PactoJ\\Desenvolvimento\\Sistemas\\ZillyonWeb\\tronco-novo\\src\\java\\relatorio\\designRelatorio\\contrato\\"]]></defaultValueExpression>
	</parameter>
	<parameter name="SUBREPORT_DIR1" class="java.lang.String">
		<defaultValueExpression><![CDATA["E:\\desenvolvimentosv\\Projeto Pacto Solucoes\\ZillyonWeb\\src\\java\\relatorio\\designRelatorio\\financeiro\\"]]></defaultValueExpression>
	</parameter>
	<parameter name="logoPadraoRelatorio" class="java.io.InputStream" isForPrompting="false"/>
	<parameter name="SUBREPORT_DIR2" class="java.lang.String">
		<defaultValueExpression><![CDATA["E:\\desenvolvimentosv\\Projeto Pacto Solucoes\\ZillyonWeb\\src\\java\\relatorio\\designRelatorio\\financeiro\\"]]></defaultValueExpression>
	</parameter>
	<parameter name="dataFim" class="java.lang.String"/>
	<parameter name="total" class="java.lang.String"/>
	<parameter name="modoVisualizacao" class="java.lang.String"/>
	<parameter name="qtdAlunos" class="java.lang.Integer"/>
	<parameter name="descricaoPeriodo" class="java.lang.String"/>
	<parameter name="comissaoMatriculaRematricula" class="java.lang.Boolean"/>
	<parameter name="pagarComissaoProdutos" class="java.lang.Boolean"/>
	<field name="nome" class="java.lang.String"/>
	<field name="listaConfiguracao" class="java.lang.Object"/>
	<field name="valorTotalComissao" class="java.lang.String"/>
	<field name="valorTotal" class="java.lang.String"/>
	<field name="codigo" class="java.lang.Integer"/>
	<pageHeader>
		<band height="69" splitType="Stretch">
			<textField pattern="dd/MM/yyyy HH.mm.ss" isBlankWhenNull="false">
				<reportElement key="dataRel-1" mode="Transparent" x="703" y="1" width="120" height="12" backcolor="#FFFFFF"/>
				<box bottomPadding="0">
					<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Verdana" size="8" isItalic="false" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression class="java.util.Date"><![CDATA[new Date()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-2" x="83" y="1" width="620" height="19"/>
				<box>
					<pen lineColor="#FFFFFF"/>
					<topPen lineColor="#FFFFFF"/>
					<leftPen lineColor="#FFFFFF"/>
					<bottomPen lineColor="#FFFFFF"/>
					<rightPen lineColor="#FFFFFF"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Bottom">
					<font fontName="Arial" size="14" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{tituloRelatorio}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-216" x="241" y="20" width="297" height="17"/>
				<box>
					<pen lineColor="#FFFFFF"/>
					<topPen lineColor="#FFFFFF"/>
					<leftPen lineColor="#FFFFFF"/>
					<bottomPen lineColor="#FFFFFF"/>
					<rightPen lineColor="#FFFFFF"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Bottom">
					<font fontName="Arial" size="10" isBold="false" isItalic="true" pdfFontName="Helvetica-BoldOblique"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{descricaoPeriodo}]]></textFieldExpression>
			</textField>
			<image scaleImage="FillFrame" isUsingCache="true" onErrorType="Blank" evaluationTime="Page">
				<reportElement key="image-1" positionType="Float" x="0" y="1" width="82" height="67" isPrintWhenDetailOverflows="true"/>
				<box>
					<pen lineColor="#FFFFFF"/>
					<topPen lineColor="#FFFFFF"/>
					<leftPen lineColor="#FFFFFF"/>
					<bottomPen lineColor="#FFFFFF"/>
					<rightPen lineColor="#FFFFFF"/>
				</box>
				<imageExpression class="java.io.InputStream"><![CDATA[$P{logoPadraoRelatorio}]]></imageExpression>
			</image>
			<textField pattern="">
				<reportElement mode="Transparent" x="703" y="13" width="120" height="12"/>
				<box>
					<leftPen lineWidth="0.5" lineStyle="Dotted"/>
					<bottomPen lineWidth="0.5" lineStyle="Dotted"/>
					<rightPen lineWidth="0.5" lineStyle="Dotted"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Verdana" size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA["Usuário:"+ $P{usuario}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="703" y="25" width="75" height="12"/>
				<box>
					<leftPen lineWidth="0.5" lineStyle="Dotted"/>
					<bottomPen lineWidth="0.5" lineStyle="Dotted"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Verdana" size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA["Página "+$V{PAGE_NUMBER}+" de"]]></textFieldExpression>
			</textField>
			<textField evaluationTime="Report">
				<reportElement x="778" y="25" width="45" height="12"/>
				<box leftPadding="4">
					<bottomPen lineWidth="0.5" lineStyle="Dotted"/>
					<rightPen lineWidth="0.5" lineStyle="Dotted"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Verdana" size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.Integer"><![CDATA[$V{PAGE_NUMBER}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="83" y="38" width="620" height="30" isPrintWhenDetailOverflows="true"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="10"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{filtros}]]></textFieldExpression>
			</textField>
		</band>
	</pageHeader>
	<detail>
		<band height="130">
			<subreport>
				<reportElement x="0" y="41" width="823" height="88"/>
				<subreportParameter name="SUBREPORT_DIR">
					<subreportParameterExpression><![CDATA[$P{SUBREPORT_DIR}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="modoVisualizacao">
					<subreportParameterExpression><![CDATA[$P{modoVisualizacao}]]></subreportParameterExpression>
				</subreportParameter>
				<dataSourceExpression><![CDATA[$F{listaConfiguracao}]]></dataSourceExpression>
				<subreportExpression class="java.lang.String"><![CDATA[$P{SUBREPORT_DIR} + "ComissaoConsultor.jasper"]]></subreportExpression>
			</subreport>
			<rectangle>
				<reportElement x="0" y="21" width="823" height="20" forecolor="#FFFFFF" backcolor="#F0EFEF"/>
			</rectangle>
			<textField>
				<reportElement x="30" y="21" width="753" height="20"/>
				<textElement verticalAlignment="Middle"/>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{nome}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="30" y="6" width="40" height="15"/>
				<textElement>
					<font size="8"/>
				</textElement>
				<text><![CDATA[Matrícula]]></text>
			</staticText>
			<staticText>
				<reportElement x="70" y="6" width="140" height="15"/>
				<textElement>
					<font size="8"/>
				</textElement>
				<text><![CDATA[Nome]]></text>
			</staticText>
			<staticText>
				<reportElement x="220" y="6" width="40" height="15"/>
				<textElement>
					<font size="8"/>
				</textElement>
				<text><![CDATA[Recibo]]></text>
			</staticText>
			<staticText>
				<reportElement x="260" y="6" width="20" height="15"/>
				<textElement>
					<font size="8"/>
				</textElement>
				<text><![CDATA[Tipo]]></text>
			</staticText>
			<staticText>
				<reportElement x="538" y="6" width="90" height="15"/>
				<textElement textAlignment="Center">
					<font size="8"/>
				</textElement>
				<text><![CDATA[Forma Pgto.]]></text>
			</staticText>
			<staticText>
				<reportElement x="733" y="6" width="45" height="15"/>
				<textElement textAlignment="Right">
					<font size="8"/>
				</textElement>
				<text><![CDATA[Vl. Compen.]]></text>
			</staticText>
			<staticText>
				<reportElement x="778" y="6" width="45" height="15"/>
				<textElement textAlignment="Right">
					<font size="8"/>
				</textElement>
				<text><![CDATA[Comissão]]></text>
			</staticText>
			<staticText>
				<reportElement x="280" y="6" width="100" height="15"/>
				<textElement>
					<font size="8"/>
				</textElement>
				<text><![CDATA[Plano]]></text>
			</staticText>
			<staticText>
				<reportElement x="380" y="6" width="103" height="15"/>
				<textElement>
					<font size="8"/>
				</textElement>
				<text><![CDATA[Atendente]]></text>
			</staticText>
			<staticText>
				<reportElement x="678" y="6" width="55" height="15"/>
				<textElement>
					<font size="8"/>
				</textElement>
				<text><![CDATA[Dt. Compens.]]></text>
			</staticText>
			<staticText>
				<reportElement x="483" y="6" width="55" height="15"/>
				<textElement>
					<font size="8"/>
				</textElement>
				<text><![CDATA[Ent. Caixa]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Transparent" x="624" y="6" width="64" height="15"/>
				<textElement>
					<font size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Vl. Contrato**]]></text>
			</staticText>
		</band>
		<band height="35">
			<rectangle>
				<reportElement x="448" y="10" width="376" height="20" forecolor="#FFFFFF" backcolor="#F0EFEF"/>
			</rectangle>
			<textField>
				<reportElement x="643" y="10" width="180" height="20"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font isBold="true" isItalic="true" isUnderline="false"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA["Comissão: " + $F{valorTotalComissao}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="448" y="10" width="180" height="20"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font isBold="true" isItalic="true" isUnderline="false"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA["Total pago: " + $F{valorTotal}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<summary>
		<band height="45">
			<rectangle>
				<reportElement x="448" y="1" width="376" height="40" forecolor="#FFFFFF" backcolor="#F0EFEF"/>
			</rectangle>
			<textField>
				<reportElement x="448" y="1" width="375" height="20"/>
				<textElement textAlignment="Right" verticalAlignment="Middle"/>
				<textFieldExpression class="java.lang.String"><![CDATA["Total geral de comissões calculadas: " + $P{total}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="448" y="21" width="375" height="20"/>
				<textElement textAlignment="Right" verticalAlignment="Middle"/>
				<textFieldExpression class="java.lang.String"><![CDATA["Total de Alunos na comissão: " + $P{qtdAlunos}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement isPrintRepeatedValues="false" x="0" y="15" width="424" height="15" isPrintInFirstWholeBand="true"/>
				<textElement>
					<font size="8" isBold="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA["Vl. Total** :" +
($P{comissaoMatriculaRematricula}.equals(true) ?
    " Valor do contrato + Matrícula e Rematrícula." :
    " Valor do Contrato.") +
($P{pagarComissaoProdutos}.equals(true) ?
    " Caso produto, apresentará o valor total do produto." :
    " ")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement isPrintRepeatedValues="false" x="0" y="30" width="424" height="15" isPrintInFirstWholeBand="true"/>
				<textElement>
					<font size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA["Descrição: Nome do Plano." +
($P{pagarComissaoProdutos}.equals(true) ?
    " Caso produto, apresentará a descrição do MovProduto." :
    " ")]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="0" y="-1" width="70" height="16"/>
				<textElement>
					<font isBold="true"/>
				</textElement>
				<text><![CDATA[Legenda:]]></text>
			</staticText>
		</band>
	</summary>
</jasperReport>

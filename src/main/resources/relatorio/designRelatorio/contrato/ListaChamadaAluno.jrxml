<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="ListaChamadaAluno" pageWidth="878" pageHeight="555" orientation="Landscape" whenNoDataType="AllSectionsNoDetail" columnWidth="878" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" whenResourceMissingType="Empty">
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.encoding" value="ISO-8859-1"/>
	<property name="ireport.zoom" value="4.392300000000001"/>
	<property name="ireport.x" value="1504"/>
	<property name="ireport.y" value="0"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="java.util.*"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<field name="pessoa.nome" class="java.lang.String"/>
	<field name="listaChamada1" class="java.lang.Boolean"/>
	<field name="listaChamada2" class="java.lang.Boolean"/>
	<field name="listaChamada3" class="java.lang.Boolean"/>
	<field name="listaChamada4" class="java.lang.Boolean"/>
	<field name="listaChamada5" class="java.lang.Boolean"/>
	<field name="listaChamada6" class="java.lang.Boolean"/>
	<field name="listaChamada7" class="java.lang.Boolean"/>
	<field name="listaChamada8" class="java.lang.Boolean"/>
	<field name="listaChamada9" class="java.lang.Boolean"/>
	<field name="listaChamada10" class="java.lang.Boolean"/>
	<field name="listaChamada11" class="java.lang.Boolean"/>
	<field name="listaChamada12" class="java.lang.Boolean"/>
	<field name="listaChamada13" class="java.lang.Boolean"/>
	<field name="listaChamada14" class="java.lang.Boolean"/>
	<field name="listaChamada15" class="java.lang.Boolean"/>
	<field name="listaChamada16" class="java.lang.Boolean"/>
	<field name="listaChamada17" class="java.lang.Boolean"/>
	<field name="listaChamada18" class="java.lang.Boolean"/>
	<field name="listaChamada19" class="java.lang.Boolean"/>
	<field name="listaChamada20" class="java.lang.Boolean"/>
	<field name="listaChamada21" class="java.lang.Boolean"/>
	<field name="listaChamada22" class="java.lang.Boolean"/>
	<field name="listaChamada23" class="java.lang.Boolean"/>
	<field name="listaChamada24" class="java.lang.Boolean"/>
	<field name="listaChamada25" class="java.lang.Boolean"/>
	<field name="listaChamada26" class="java.lang.Boolean"/>
	<field name="listaChamada27" class="java.lang.Boolean"/>
	<field name="listaChamada28" class="java.lang.Boolean"/>
	<field name="listaChamada29" class="java.lang.Boolean"/>
	<field name="listaChamada30" class="java.lang.Boolean"/>
	<field name="contrato.vigenciaAteAjustada" class="java.util.Date"/>
	<field name="cliente.matricula" class="java.lang.String"/>
	<field name="listaChamada31" class="java.lang.Boolean"/>
	<field name="presenca1" class="java.lang.Boolean"/>
	<field name="presenca2" class="java.lang.Boolean"/>
	<field name="presenca3" class="java.lang.Boolean"/>
	<field name="presenca4" class="java.lang.Boolean"/>
	<field name="presenca5" class="java.lang.Boolean"/>
	<field name="presenca6" class="java.lang.Boolean"/>
	<field name="presenca7" class="java.lang.Boolean"/>
	<field name="presenca8" class="java.lang.Boolean"/>
	<field name="presenca9" class="java.lang.Boolean"/>
	<field name="presenca10" class="java.lang.Boolean"/>
	<field name="presenca11" class="java.lang.Boolean"/>
	<field name="presenca12" class="java.lang.Boolean"/>
	<field name="presenca13" class="java.lang.Boolean"/>
	<field name="presenca14" class="java.lang.Boolean"/>
	<field name="presenca15" class="java.lang.Boolean"/>
	<field name="presenca16" class="java.lang.Boolean"/>
	<field name="presenca17" class="java.lang.Boolean"/>
	<field name="presenca18" class="java.lang.Boolean"/>
	<field name="presenca19" class="java.lang.Boolean"/>
	<field name="presenca20" class="java.lang.Boolean"/>
	<field name="presenca21" class="java.lang.Boolean"/>
	<field name="presenca22" class="java.lang.Boolean"/>
	<field name="presenca23" class="java.lang.Boolean"/>
	<field name="presenca24" class="java.lang.Boolean"/>
	<field name="presenca25" class="java.lang.Boolean"/>
	<field name="presenca26" class="java.lang.Boolean"/>
	<field name="presenca27" class="java.lang.Boolean"/>
	<field name="presenca28" class="java.lang.Boolean"/>
	<field name="presenca29" class="java.lang.Boolean"/>
	<field name="presenca30" class="java.lang.Boolean"/>
	<field name="presenca31" class="java.lang.Boolean"/>
	<field name="contrato.pessoa.dataNasc" class="java.util.Date"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band splitType="Stretch"/>
	</title>
	<pageHeader>
		<band splitType="Stretch"/>
	</pageHeader>
	<columnHeader>
		<band splitType="Stretch"/>
	</columnHeader>
	<detail>
		<band height="13" splitType="Stretch">
			<textField isBlankWhenNull="false">
				<reportElement key="textField-2" x="63" y="0" width="211" height="13"/>
				<box>
					<pen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Left">
					<font fontName="Microsoft Sans Serif" size="10"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{pessoa.nome}]]></textFieldExpression>
			</textField>
			<textField pattern="dd/MM/yyyy" isBlankWhenNull="false">
				<reportElement key="textField-46" x="355" y="0" width="81" height="13"/>
				<box>
					<pen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left">
					<font fontName="Microsoft Sans Serif" size="10"/>
				</textElement>
				<textFieldExpression class="java.util.Date"><![CDATA[   $F{contrato.vigenciaAteAjustada}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-47" x="10" y="0" width="53" height="13"/>
				<box>
					<pen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Left">
					<font fontName="Microsoft Sans Serif" size="10"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[" " + $F{cliente.matricula}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-131" mode="Opaque" x="436" y="0" width="13" height="13" forecolor="#000000" backcolor="#DDD7D7">
					<printWhenExpression><![CDATA[$F{listaChamada1}]]></printWhenExpression>
				</reportElement>
				<box>
					<pen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None">
					<font fontName="Microsoft Sans Serif" size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[($F{presenca1} ? "P" : "")]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-131" mode="Opaque" x="449" y="0" width="13" height="13" forecolor="#000000" backcolor="#DDD7D7">
					<printWhenExpression><![CDATA[$F{listaChamada2}]]></printWhenExpression>
				</reportElement>
				<box>
					<pen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None">
					<font fontName="Microsoft Sans Serif" size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[($F{presenca2} ? "P" : "")]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-131" mode="Opaque" x="462" y="0" width="13" height="13" forecolor="#000000" backcolor="#DDD7D7">
					<printWhenExpression><![CDATA[$F{listaChamada3}]]></printWhenExpression>
				</reportElement>
				<box>
					<pen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None">
					<font fontName="Microsoft Sans Serif" size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[($F{presenca3} ? "P" : "")]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-131" mode="Opaque" x="475" y="0" width="13" height="13" forecolor="#000000" backcolor="#DDD7D7">
					<printWhenExpression><![CDATA[$F{listaChamada4}]]></printWhenExpression>
				</reportElement>
				<box>
					<pen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None">
					<font fontName="Microsoft Sans Serif" size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[($F{presenca4} ? "P" : "")]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-131" mode="Opaque" x="488" y="0" width="13" height="13" forecolor="#000000" backcolor="#DDD7D7">
					<printWhenExpression><![CDATA[$F{listaChamada5}]]></printWhenExpression>
				</reportElement>
				<box>
					<pen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None">
					<font fontName="Microsoft Sans Serif" size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[($F{presenca5} ? "P" : "")]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-131" mode="Opaque" x="501" y="0" width="13" height="13" forecolor="#000000" backcolor="#DDD7D7">
					<printWhenExpression><![CDATA[$F{listaChamada6}]]></printWhenExpression>
				</reportElement>
				<box>
					<pen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None">
					<font fontName="Microsoft Sans Serif" size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[($F{presenca6} ? "P" : "")]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-131" mode="Opaque" x="514" y="0" width="13" height="13" forecolor="#000000" backcolor="#DDD7D7">
					<printWhenExpression><![CDATA[$F{listaChamada7}]]></printWhenExpression>
				</reportElement>
				<box>
					<pen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None">
					<font fontName="Microsoft Sans Serif" size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[($F{presenca7} ? "P" : "")]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-131" mode="Opaque" x="527" y="0" width="13" height="13" forecolor="#000000" backcolor="#DDD7D7">
					<printWhenExpression><![CDATA[$F{listaChamada8}]]></printWhenExpression>
				</reportElement>
				<box>
					<pen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None">
					<font fontName="Microsoft Sans Serif" size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[($F{presenca8} ? "P" : "")]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-131" mode="Opaque" x="540" y="0" width="13" height="13" forecolor="#000000" backcolor="#DDD7D7">
					<printWhenExpression><![CDATA[$F{listaChamada9}]]></printWhenExpression>
				</reportElement>
				<box>
					<pen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None">
					<font fontName="Microsoft Sans Serif" size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[($F{presenca9} ? "P" : "")]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-131" mode="Opaque" x="553" y="0" width="13" height="13" forecolor="#000000" backcolor="#DDD7D7">
					<printWhenExpression><![CDATA[$F{listaChamada10}]]></printWhenExpression>
				</reportElement>
				<box>
					<pen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None">
					<font fontName="Microsoft Sans Serif" size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[($F{presenca10} ? "P" : "")]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-131" mode="Opaque" x="592" y="0" width="13" height="13" forecolor="#000000" backcolor="#DDD7D7">
					<printWhenExpression><![CDATA[$F{listaChamada13}]]></printWhenExpression>
				</reportElement>
				<box>
					<pen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None">
					<font fontName="Microsoft Sans Serif" size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[($F{presenca13} ? "P" : "")]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-131" mode="Opaque" x="657" y="0" width="13" height="13" forecolor="#000000" backcolor="#DDD7D7">
					<printWhenExpression><![CDATA[$F{listaChamada18}]]></printWhenExpression>
				</reportElement>
				<box>
					<pen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None">
					<font fontName="Microsoft Sans Serif" size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[($F{presenca18} ? "P" : "")]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-131" mode="Opaque" x="683" y="0" width="13" height="13" forecolor="#000000" backcolor="#DDD7D7">
					<printWhenExpression><![CDATA[$F{listaChamada20}]]></printWhenExpression>
				</reportElement>
				<box>
					<pen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None">
					<font fontName="Microsoft Sans Serif" size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[($F{presenca20} ? "P" : "")]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-131" mode="Opaque" x="644" y="0" width="13" height="13" forecolor="#000000" backcolor="#DDD7D7">
					<printWhenExpression><![CDATA[$F{listaChamada17}]]></printWhenExpression>
				</reportElement>
				<box>
					<pen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None">
					<font fontName="Microsoft Sans Serif" size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[($F{presenca17} ? "P" : "")]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-131" mode="Opaque" x="670" y="0" width="13" height="13" forecolor="#000000" backcolor="#DDD7D7">
					<printWhenExpression><![CDATA[$F{listaChamada19}]]></printWhenExpression>
				</reportElement>
				<box>
					<pen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None">
					<font fontName="Microsoft Sans Serif" size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[($F{presenca19} ? "P" : "")]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-131" mode="Opaque" x="605" y="0" width="13" height="13" forecolor="#000000" backcolor="#DDD7D7">
					<printWhenExpression><![CDATA[$F{listaChamada14}]]></printWhenExpression>
				</reportElement>
				<box>
					<pen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None">
					<font fontName="Microsoft Sans Serif" size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[($F{presenca14} ? "P" : "")]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-131" mode="Opaque" x="631" y="0" width="13" height="13" forecolor="#000000" backcolor="#DDD7D7">
					<printWhenExpression><![CDATA[$F{listaChamada16}]]></printWhenExpression>
				</reportElement>
				<box>
					<pen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None">
					<font fontName="Microsoft Sans Serif" size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[($F{presenca16} ? "P" : "")]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-131" mode="Opaque" x="579" y="0" width="13" height="13" forecolor="#000000" backcolor="#DDD7D7">
					<printWhenExpression><![CDATA[$F{listaChamada12}]]></printWhenExpression>
				</reportElement>
				<box>
					<pen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None">
					<font fontName="Microsoft Sans Serif" size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[($F{presenca12} ? "P" : "")]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-131" mode="Opaque" x="566" y="0" width="13" height="13" forecolor="#000000" backcolor="#DDD7D7">
					<printWhenExpression><![CDATA[$F{listaChamada11}]]></printWhenExpression>
				</reportElement>
				<box>
					<pen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None">
					<font fontName="Microsoft Sans Serif" size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[($F{presenca11} ? "P" : "")]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-131" mode="Opaque" x="618" y="0" width="13" height="13" forecolor="#000000" backcolor="#DDD7D7">
					<printWhenExpression><![CDATA[$F{listaChamada15}]]></printWhenExpression>
				</reportElement>
				<box>
					<pen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None">
					<font fontName="Microsoft Sans Serif" size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[($F{presenca15} ? "P" : "")]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-131" mode="Opaque" x="709" y="0" width="13" height="13" forecolor="#000000" backcolor="#DDD7D7">
					<printWhenExpression><![CDATA[$F{listaChamada22}]]></printWhenExpression>
				</reportElement>
				<box>
					<pen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None">
					<font fontName="Microsoft Sans Serif" size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[($F{presenca22} ? "P" : "")]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-131" mode="Opaque" x="774" y="0" width="13" height="13" forecolor="#000000" backcolor="#DDD7D7">
					<printWhenExpression><![CDATA[$F{listaChamada27}]]></printWhenExpression>
				</reportElement>
				<box>
					<pen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None">
					<font fontName="Microsoft Sans Serif" size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[($F{presenca27} ? "P" : "")]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-131" mode="Opaque" x="800" y="0" width="13" height="13" forecolor="#000000" backcolor="#DDD7D7">
					<printWhenExpression><![CDATA[$F{listaChamada29}]]></printWhenExpression>
				</reportElement>
				<box>
					<pen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None">
					<font fontName="Microsoft Sans Serif" size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[($F{presenca29} ? "P" : "")]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-131" mode="Opaque" x="761" y="0" width="13" height="13" forecolor="#000000" backcolor="#DDD7D7">
					<printWhenExpression><![CDATA[$F{listaChamada26}]]></printWhenExpression>
				</reportElement>
				<box>
					<pen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None">
					<font fontName="Microsoft Sans Serif" size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[($F{presenca26} ? "P" : "")]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-131" mode="Opaque" x="787" y="0" width="13" height="13" forecolor="#000000" backcolor="#DDD7D7">
					<printWhenExpression><![CDATA[$F{listaChamada28}]]></printWhenExpression>
				</reportElement>
				<box>
					<pen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None">
					<font fontName="Microsoft Sans Serif" size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[($F{presenca28} ? "P" : "")]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-131" mode="Opaque" x="722" y="0" width="13" height="13" forecolor="#000000" backcolor="#DDD7D7">
					<printWhenExpression><![CDATA[$F{listaChamada23}]]></printWhenExpression>
				</reportElement>
				<box>
					<pen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None">
					<font fontName="Microsoft Sans Serif" size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[($F{presenca23} ? "P" : "")]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-131" mode="Opaque" x="748" y="0" width="13" height="13" forecolor="#000000" backcolor="#DDD7D7">
					<printWhenExpression><![CDATA[$F{listaChamada25}]]></printWhenExpression>
				</reportElement>
				<box>
					<pen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None">
					<font fontName="Microsoft Sans Serif" size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[($F{presenca25} ? "P" : "")]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-131" mode="Opaque" x="696" y="0" width="13" height="13" forecolor="#000000" backcolor="#DDD7D7">
					<printWhenExpression><![CDATA[$F{listaChamada21}]]></printWhenExpression>
				</reportElement>
				<box>
					<pen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None">
					<font fontName="Microsoft Sans Serif" size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[($F{presenca21} ? "P" : "")]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-131" mode="Opaque" x="735" y="0" width="13" height="13" forecolor="#000000" backcolor="#DDD7D7">
					<printWhenExpression><![CDATA[$F{listaChamada24}]]></printWhenExpression>
				</reportElement>
				<box>
					<pen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None">
					<font fontName="Microsoft Sans Serif" size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[($F{presenca24} ? "P" : "")]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-131" mode="Opaque" x="813" y="0" width="13" height="13" forecolor="#000000" backcolor="#DDD7D7">
					<printWhenExpression><![CDATA[$F{listaChamada30}]]></printWhenExpression>
				</reportElement>
				<box>
					<pen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None">
					<font fontName="Microsoft Sans Serif" size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[($F{presenca30} ? "P" : "")]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-131" mode="Opaque" x="826" y="0" width="13" height="13" forecolor="#000000" backcolor="#DDD7D7">
					<printWhenExpression><![CDATA[$F{listaChamada31}]]></printWhenExpression>
				</reportElement>
				<box>
					<pen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None">
					<font fontName="Microsoft Sans Serif" size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[($F{presenca31} ? "P" : "")]]></textFieldExpression>
			</textField>
			<textField pattern="dd/MM/yyyy" isBlankWhenNull="false">
				<reportElement key="textField-46" x="274" y="0" width="81" height="13"/>
				<box>
					<pen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid"/>
				</box>
				<textElement textAlignment="Left">
					<font fontName="Microsoft Sans Serif" size="10"/>
				</textElement>
				<textFieldExpression class="java.util.Date"><![CDATA[   $F{contrato.pessoa.dataNasc}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<columnFooter>
		<band splitType="Stretch"/>
	</columnFooter>
	<pageFooter>
		<band splitType="Stretch"/>
	</pageFooter>
	<summary>
		<band splitType="Stretch"/>
	</summary>
</jasperReport>

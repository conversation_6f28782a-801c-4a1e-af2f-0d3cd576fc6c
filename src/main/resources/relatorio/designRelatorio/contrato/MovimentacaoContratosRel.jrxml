<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="MovimentacaoContratosRel" pageWidth="680" pageHeight="878" columnWidth="640" leftMargin="20" rightMargin="20" topMargin="10" bottomMargin="10" whenResourceMissingType="Empty">
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.encoding" value="ISO-8859-1"/>
	<property name="ireport.zoom" value="1.5"/>
	<property name="ireport.x" value="194"/>
	<property name="ireport.y" value="0"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="java.util.*"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<subDataset name="Teste"/>
	<parameter name="logoPadraoRelatorio" class="java.io.InputStream" isForPrompting="false"/>
	<parameter name="tituloRelatorio" class="java.lang.String" isForPrompting="false"/>
	<parameter name="versaoSoftware" class="java.lang.String" isForPrompting="false"/>
	<parameter name="usuario" class="java.lang.String" isForPrompting="false"/>
	<parameter name="filtros" class="java.lang.String" isForPrompting="false"/>
	<parameter name="SUBREPORT_DIR" class="java.lang.String">
		<defaultValueExpression><![CDATA["D:\\PactoJ\\Desenvolvimento\\Sistemas\\ZillyonWeb\\tronco-novo\\src\\java\\relatorio\\designRelatorio\\outros\\"]]></defaultValueExpression>
	</parameter>
	<parameter name="nomeEmpresa" class="java.lang.String"/>
	<parameter name="enderecoEmpresa" class="java.lang.String"/>
	<parameter name="cidadeEmpresa" class="java.lang.String"/>
	<parameter name="dataIni" class="java.lang.String" isForPrompting="false"/>
	<parameter name="dataFim" class="java.lang.String" isForPrompting="false"/>
	<parameter name="qtdMatriculado" class="java.lang.Integer"/>
	<parameter name="qtdRematriculado" class="java.lang.Integer"/>
	<parameter name="qtdCancelado" class="java.lang.Integer"/>
	<parameter name="qtdTrancamento" class="java.lang.Integer"/>
	<parameter name="qtdDesistente" class="java.lang.Integer"/>
	<parameter name="qtdRetornoTrancamento" class="java.lang.Integer"/>
	<parameter name="qtdVencidoMes" class="java.lang.Integer"/>
	<parameter name="qtdTotal" class="java.lang.Integer"/>
	<parameter name="apresentarCancelados" class="java.lang.Boolean"/>
	<field name="cliente.matricula" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="codigo" class="java.lang.Integer"/>
	<field name="pessoa.nome" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="dataLancamento_Apresentar" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="vigenciaDe_Apresentar" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="vigenciaAteAjustada_Apresentar" class="java.lang.String"/>
	<field name="contratoDuracao.numeroMeses" class="java.lang.Integer"/>
	<field name="justificativaCancelamento_Apresentar" class="java.lang.String"/>
	<field name="situacaoCliente" class="java.lang.String"/>
	<variable name="totalRegistros" class="java.lang.Integer" calculation="Count">
		<variableExpression><![CDATA[$F{codigo}]]></variableExpression>
	</variable>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band splitType="Stretch"/>
	</title>
	<pageHeader>
		<band height="69" splitType="Stretch">
			<textField evaluationTime="Report" isBlankWhenNull="false">
				<reportElement key="textField-212" x="600" y="51" width="31" height="17"/>
				<box bottomPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement>
					<font fontName="Arial" size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[" " + $V{PAGE_NUMBER} + ""]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement key="staticText-15" mode="Opaque" x="522" y="35" width="111" height="12"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Top" lineSpacing="Single">
					<font fontName="Microsoft Sans Serif" size="9" isBold="true" isItalic="true" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica-BoldOblique"/>
				</textElement>
				<text><![CDATA[(0xx62) 3251-5820]]></text>
			</staticText>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-211" x="524" y="51" width="75" height="17"/>
				<box bottomPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right">
					<font fontName="Arial" size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA["Página: " + $V{PAGE_NUMBER} + " de "]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement key="staticText-13" x="200" y="42" width="322" height="27"/>
				<textElement textAlignment="Center">
					<font size="20" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Movimentação de Contratos]]></text>
			</staticText>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-209" x="86" y="31" width="113" height="14"/>
				<textElement>
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{enderecoEmpresa}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-208" x="86" y="15" width="262" height="14"/>
				<textElement>
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{nomeEmpresa}]]></textFieldExpression>
			</textField>
			<image isUsingCache="true" onErrorType="Blank" evaluationTime="Page">
				<reportElement key="image-1" x="0" y="16" width="82" height="46" isPrintWhenDetailOverflows="true"/>
				<box>
					<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<imageExpression class="java.io.InputStream"><![CDATA[$P{logoPadraoRelatorio}]]></imageExpression>
			</image>
			<staticText>
				<reportElement key="staticText-14" mode="Opaque" x="371" y="6" width="262" height="23"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Top" lineSpacing="Single">
					<font fontName="Microsoft Sans Serif" size="9" isBold="true" isItalic="true" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica-BoldOblique"/>
				</textElement>
				<text><![CDATA[Zillyon - Sistema Administrativo para Academias Desenvolvido por PACTO Soluções Tecnológicas Ltda.]]></text>
			</staticText>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-210" x="86" y="47" width="113" height="14"/>
				<textElement>
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{cidadeEmpresa}]]></textFieldExpression>
			</textField>
		</band>
	</pageHeader>
	<columnHeader>
		<band height="55" splitType="Stretch">
			<line>
				<reportElement key="line-1" x="0" y="2" width="640" height="1"/>
			</line>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-214" x="0" y="3" width="640" height="30"/>
				<box>
					<topPen lineWidth="0.5" lineStyle="Solid"/>
					<leftPen lineWidth="0.5" lineStyle="Solid"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.5" lineStyle="Solid"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="true" isItalic="true" pdfFontName="Helvetica-BoldOblique"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{filtros}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement key="staticText-2" x="1" y="40" width="61" height="14"/>
				<textElement>
					<font size="9" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Matrícula]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-1" x="62" y="40" width="179" height="14"/>
				<textElement>
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Nome]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-8" x="241" y="41" width="63" height="14"/>
				<textElement>
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Contrato]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-8" x="304" y="41" width="86" height="14"/>
				<textElement>
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Data Lançamento]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-8" x="400" y="40" width="60" height="14"/>
				<textElement>
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Data Início]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-8" x="464" y="41" width="60" height="14"/>
				<textElement>
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Data Final]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-8" x="535" y="41" width="48" height="14"/>
				<textElement>
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Duração]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-8" x="588" y="40" width="48" height="14"/>
				<textElement>
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Situação]]></text>
			</staticText>
			<line>
				<reportElement key="line-1" x="0" y="53" width="640" height="1"/>
			</line>
		</band>
	</columnHeader>
	<detail>
		<band height="33" splitType="Stretch">
			<rectangle>
				<reportElement key="rectangle-1" mode="Opaque" x="1" y="1" width="639" height="32" backcolor="#B4CDCD">
					<printWhenExpression><![CDATA[new Boolean(($V{COLUMN_COUNT}.intValue()%2)==0)]]></printWhenExpression>
				</reportElement>
				<graphicElement>
					<pen lineWidth="0.0" lineStyle="Solid"/>
				</graphicElement>
			</rectangle>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-223" x="404" y="3" width="56" height="16"/>
				<textElement verticalAlignment="Middle">
					<font size="9"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{vigenciaDe_Apresentar}]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement key="textField" positionType="Float" x="469" y="3" width="55" height="16"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font size="9"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{vigenciaAteAjustada_Apresentar}]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement key="textField" x="535" y="1" width="31" height="16"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="9"/>
				</textElement>
				<textFieldExpression class="java.lang.Integer"><![CDATA[$F{contratoDuracao.numeroMeses}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-224" x="62" y="3" width="179" height="14"/>
				<textElement>
					<font size="10" isBold="false"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{pessoa.nome}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-223" x="7" y="3" width="50" height="14"/>
				<textElement/>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{cliente.matricula}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement key="staticText-2" x="1" y="19" width="77" height="14">
					<printWhenExpression><![CDATA[$P{apresentarCancelados}]]></printWhenExpression>
				</reportElement>
				<textElement verticalAlignment="Middle">
					<font size="9" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Justificativa:]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-224" x="78" y="19" width="447" height="14">
					<printWhenExpression><![CDATA[$P{apresentarCancelados}]]></printWhenExpression>
				</reportElement>
				<textElement verticalAlignment="Middle">
					<font size="10" isBold="false"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{justificativaCancelamento_Apresentar}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-224" x="241" y="1" width="63" height="16"/>
				<textElement verticalAlignment="Middle">
					<font size="9" isBold="false"/>
				</textElement>
				<textFieldExpression class="java.lang.Integer"><![CDATA[$F{codigo}]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement key="textField-224" x="304" y="3" width="86" height="16"/>
				<textElement verticalAlignment="Middle">
					<font size="9"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{dataLancamento_Apresentar}]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement key="textField" x="588" y="0" width="43" height="16"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="9"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{situacaoCliente}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<columnFooter>
		<band splitType="Stretch"/>
	</columnFooter>
	<pageFooter>
		<band splitType="Stretch"/>
	</pageFooter>
	<lastPageFooter>
		<band height="124" splitType="Stretch">
			<staticText>
				<reportElement x="5" y="24" width="61" height="15"/>
				<textElement>
					<font size="9" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Matriculados:]]></text>
			</staticText>
			<line>
				<reportElement key="line-4" x="1" y="1" width="640" height="1"/>
			</line>
			<line>
				<reportElement key="line-5" x="1" y="91" width="640" height="1"/>
			</line>
			<line>
				<reportElement key="line-6" x="1" y="89" width="640" height="1"/>
			</line>
			<textField pattern="" isBlankWhenNull="false">
				<reportElement key="textField-207" x="1" y="101" width="640" height="19"/>
				<box>
					<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="8" isItalic="true" pdfFontName="Helvetica-Oblique"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[" "+" Usuário:" + $P{usuario}]]></textFieldExpression>
			</textField>
			<textField pattern="dd/MM/yyyy HH.mm.ss" isBlankWhenNull="true">
				<reportElement key="dataRel-1" mode="Opaque" x="512" y="104" width="126" height="13" backcolor="#FFFFFF"/>
				<box bottomPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle" lineSpacing="Single">
					<font fontName="Arial" size="8" isBold="false" isItalic="true" isUnderline="false" pdfFontName="Helvetica-Oblique"/>
				</textElement>
				<textFieldExpression class="java.util.Date"><![CDATA[new Date()]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="5" y="39" width="73" height="15"/>
				<textElement>
					<font size="9" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Rematriculados:]]></text>
			</staticText>
			<staticText>
				<reportElement x="6" y="54" width="61" height="15"/>
				<textElement>
					<font size="9" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Cancelados:]]></text>
			</staticText>
			<staticText>
				<reportElement x="6" y="70" width="61" height="15"/>
				<textElement>
					<font size="9" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Trancados:]]></text>
			</staticText>
			<staticText>
				<reportElement x="460" y="24" width="61" height="15"/>
				<textElement>
					<font size="9" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Desistências:]]></text>
			</staticText>
			<staticText>
				<reportElement x="460" y="39" width="123" height="15"/>
				<textElement>
					<font size="9" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Retorno de Trancamentos:]]></text>
			</staticText>
			<staticText>
				<reportElement x="460" y="55" width="83" height="15"/>
				<textElement>
					<font size="9" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Vencidos do Mês:]]></text>
			</staticText>
			<staticText>
				<reportElement x="460" y="70" width="123" height="15"/>
				<textElement>
					<font size="9" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Total de Ativos + Vencidos:]]></text>
			</staticText>
			<textField>
				<reportElement x="66" y="25" width="80" height="15"/>
				<textElement>
					<font size="9" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression class="java.lang.Integer"><![CDATA[$P{qtdMatriculado}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="78" y="39" width="68" height="15"/>
				<textElement>
					<font size="9" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression class="java.lang.Integer"><![CDATA[$P{qtdRematriculado}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="67" y="54" width="79" height="15"/>
				<textElement>
					<font size="9" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression class="java.lang.Integer"><![CDATA[$P{qtdCancelado}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="67" y="70" width="79" height="15"/>
				<textElement>
					<font size="9" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression class="java.lang.Integer"><![CDATA[$P{qtdTrancamento}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="520" y="24" width="117" height="15"/>
				<textElement>
					<font size="9" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression class="java.lang.Integer"><![CDATA[$P{qtdDesistente}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="583" y="39" width="53" height="15"/>
				<textElement>
					<font size="9" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression class="java.lang.Integer"><![CDATA[$P{qtdRetornoTrancamento}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="543" y="55" width="93" height="15"/>
				<textElement>
					<font size="9" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression class="java.lang.Integer"><![CDATA[$P{qtdVencidoMes}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="583" y="70" width="53" height="15"/>
				<textElement>
					<font size="9" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression class="java.lang.Integer"><![CDATA[$P{qtdTotal}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="6" y="7" width="93" height="15"/>
				<textElement>
					<font size="9" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Total de registros:]]></text>
			</staticText>
			<textField>
				<reportElement x="99" y="7" width="51" height="15"/>
				<textElement>
					<font size="9" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression class="java.lang.Integer"><![CDATA[$V{totalRegistros}]]></textFieldExpression>
			</textField>
		</band>
	</lastPageFooter>
	<summary>
		<band splitType="Stretch"/>
	</summary>
</jasperReport>

<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="ListaChamada" pageWidth="878" pageHeight="680" orientation="Landscape" columnWidth="841" leftMargin="19" rightMargin="18" topMargin="2" bottomMargin="2" whenResourceMissingType="Empty">
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.encoding" value="ISO-8859-1"/>
	<property name="ireport.zoom" value="1.5"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="4"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="java.util.*"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<subDataset name="Teste"/>
	<parameter name="logoPadraoRelatorio" class="java.io.InputStream" isForPrompting="false"/>
	<parameter name="tituloRelatorio" class="java.lang.String" isForPrompting="false"/>
	<parameter name="nomeEmpresa" class="java.lang.String" isForPrompting="false"/>
	<parameter name="versaoSoftware" class="java.lang.String" isForPrompting="false"/>
	<parameter name="usuario" class="java.lang.String" isForPrompting="false"/>
	<parameter name="filtros" class="java.lang.String" isForPrompting="false"/>
	<parameter name="SUBREPORT_DIR" class="java.lang.String">
		<defaultValueExpression><![CDATA["D:\\PactoJ\\Desenvolvimento\\Sistemas\\ZillyonWeb\\tronco-novo\\src\\java\\relatorio\\designRelatorio\\contrato\\"]]></defaultValueExpression>
	</parameter>
	<parameter name="CONT" class="java.lang.Integer" isForPrompting="false"/>
	<parameter name="dataIni" class="java.lang.String" isForPrompting="false"/>
	<parameter name="dataFim" class="java.lang.String" isForPrompting="false"/>
	<parameter name="enderecoEmpresa" class="java.lang.String" isForPrompting="false"/>
	<parameter name="cidadeEmpresa" class="java.lang.String" isForPrompting="false"/>
	<parameter name="quebrarPagina" class="java.lang.Boolean" isForPrompting="false"/>
	<field name="dataInicioMatricula" class="java.util.Date"/>
	<field name="dataFimMatricula" class="java.util.Date"/>
	<field name="modalidade.nome" class="java.lang.String"/>
	<field name="horarioTurma.horaInicial" class="java.lang.String"/>
	<field name="horarioTurma.horaFinal" class="java.lang.String"/>
	<field name="data1" class="java.lang.String"/>
	<field name="data2" class="java.lang.String"/>
	<field name="data3" class="java.lang.String"/>
	<field name="data4" class="java.lang.String"/>
	<field name="data5" class="java.lang.String"/>
	<field name="data6" class="java.lang.String"/>
	<field name="data7" class="java.lang.String"/>
	<field name="data8" class="java.lang.String"/>
	<field name="data9" class="java.lang.String"/>
	<field name="data10" class="java.lang.String"/>
	<field name="data11" class="java.lang.String"/>
	<field name="data12" class="java.lang.String"/>
	<field name="data13" class="java.lang.String"/>
	<field name="data14" class="java.lang.String"/>
	<field name="data15" class="java.lang.String"/>
	<field name="data16" class="java.lang.String"/>
	<field name="data17" class="java.lang.String"/>
	<field name="data18" class="java.lang.String"/>
	<field name="data19" class="java.lang.String"/>
	<field name="data20" class="java.lang.String"/>
	<field name="data21" class="java.lang.String"/>
	<field name="data22" class="java.lang.String"/>
	<field name="data23" class="java.lang.String"/>
	<field name="data24" class="java.lang.String"/>
	<field name="data25" class="java.lang.String"/>
	<field name="data26" class="java.lang.String"/>
	<field name="data27" class="java.lang.String"/>
	<field name="data28" class="java.lang.String"/>
	<field name="data29" class="java.lang.String"/>
	<field name="data30" class="java.lang.String"/>
	<field name="diaSemana1" class="java.lang.String"/>
	<field name="diaSemana2" class="java.lang.String"/>
	<field name="diaSemana3" class="java.lang.String"/>
	<field name="diaSemana4" class="java.lang.String"/>
	<field name="diaSemana5" class="java.lang.String"/>
	<field name="diaSemana6" class="java.lang.String"/>
	<field name="diaSemana7" class="java.lang.String"/>
	<field name="diaSemana8" class="java.lang.String"/>
	<field name="diaSemana9" class="java.lang.String"/>
	<field name="diaSemana10" class="java.lang.String"/>
	<field name="turma.descricao" class="java.lang.String"/>
	<field name="diaSemana11" class="java.lang.String"/>
	<field name="diaSemana12" class="java.lang.String"/>
	<field name="diaSemana13" class="java.lang.String"/>
	<field name="diaSemana14" class="java.lang.String"/>
	<field name="diaSemana15" class="java.lang.String"/>
	<field name="diaSemana16" class="java.lang.String"/>
	<field name="diaSemana17" class="java.lang.String"/>
	<field name="diaSemana18" class="java.lang.String"/>
	<field name="diaSemana19" class="java.lang.String"/>
	<field name="diaSemana20" class="java.lang.String"/>
	<field name="diaSemana21" class="java.lang.String"/>
	<field name="diaSemana22" class="java.lang.String"/>
	<field name="diaSemana23" class="java.lang.String"/>
	<field name="diaSemana24" class="java.lang.String"/>
	<field name="diaSemana25" class="java.lang.String"/>
	<field name="diaSemana26" class="java.lang.String"/>
	<field name="diaSemana27" class="java.lang.String"/>
	<field name="diaSemana28" class="java.lang.String"/>
	<field name="diaSemana29" class="java.lang.String"/>
	<field name="diaSemana30" class="java.lang.String"/>
	<field name="contrato.vigenciaDe" class="java.util.Date"/>
	<field name="horarioTurma.ambiente.descricao" class="java.lang.String"/>
	<field name="horarioTurma.nivelTurma.descricao" class="java.lang.String"/>
	<field name="listaAlunos" class="java.lang.Object"/>
	<field name="sequencial" class="java.lang.Integer"/>
	<field name="qtdAluno" class="java.lang.Integer"/>
	<field name="horarioTurma.professor.pessoa.nome" class="java.lang.String"/>
	<field name="data31" class="java.lang.String"/>
	<field name="diaSemana31" class="java.lang.String"/>
	<group name="sequencial">
		<groupExpression><![CDATA[$F{sequencial}]]></groupExpression>
		<groupHeader>
			<band height="75" splitType="Stretch">
				<staticText>
					<reportElement key="staticText-95" mode="Opaque" x="355" y="61" width="81" height="14"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Microsoft Sans Serif" size="9" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<text><![CDATA[ Dt Término]]></text>
				</staticText>
				<staticText>
					<reportElement key="staticText-81" mode="Opaque" x="0" y="21" width="87" height="14"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Microsoft Sans Serif" size="12" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<text><![CDATA[Modalidade:]]></text>
				</staticText>
				<staticText>
					<reportElement key="staticText-82" mode="Opaque" x="0" y="2" width="87" height="14"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Microsoft Sans Serif" size="12" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<text><![CDATA[Turma:]]></text>
				</staticText>
				<textField isBlankWhenNull="false">
					<reportElement key="textField-127" mode="Opaque" x="87" y="21" width="252" height="14"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Microsoft Sans Serif" size="12" isBold="false" pdfFontName="Helvetica"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[" "+$F{modalidade.nome}]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="false">
					<reportElement key="textField-129" mode="Opaque" x="87" y="2" width="253" height="14"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Microsoft Sans Serif" size="12" isBold="false" pdfFontName="Helvetica"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[" "+$F{turma.descricao}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement key="staticText-84" mode="Opaque" x="0" y="39" width="59" height="14"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Microsoft Sans Serif" size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<text><![CDATA[Professor:]]></text>
				</staticText>
				<textField isBlankWhenNull="false">
					<reportElement key="textField-130" mode="Opaque" x="60" y="39" width="279" height="14"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Microsoft Sans Serif" size="10" isBold="false" pdfFontName="Helvetica"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$F{horarioTurma.professor.pessoa.nome}]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="false">
					<reportElement key="textField-131" mode="Opaque" x="436" y="60" width="13" height="15" forecolor="#000000" backcolor="#FFFFFF"/>
					<box>
						<pen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" rotation="None">
						<font fontName="Microsoft Sans Serif" size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$F{data1}]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="false">
					<reportElement key="textField-167" mode="Opaque" x="449" y="60" width="13" height="15" forecolor="#000000" backcolor="#FFFFFF"/>
					<box>
						<pen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" rotation="None">
						<font fontName="Microsoft Sans Serif" size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$F{data2}]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="false">
					<reportElement key="textField-168" mode="Opaque" x="462" y="60" width="13" height="15" forecolor="#000000" backcolor="#FFFFFF"/>
					<box>
						<pen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" rotation="None">
						<font fontName="Microsoft Sans Serif" size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$F{data3}]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="false">
					<reportElement key="textField-169" mode="Opaque" x="501" y="60" width="13" height="15" forecolor="#000000" backcolor="#FFFFFF"/>
					<box>
						<pen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" rotation="None">
						<font fontName="Microsoft Sans Serif" size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$F{data6}]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="false">
					<reportElement key="textField-170" mode="Opaque" x="488" y="60" width="13" height="15" forecolor="#000000" backcolor="#FFFFFF"/>
					<box>
						<pen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" rotation="None">
						<font fontName="Microsoft Sans Serif" size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$F{data5}]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="false">
					<reportElement key="textField-171" mode="Opaque" x="475" y="60" width="13" height="15" forecolor="#000000" backcolor="#FFFFFF"/>
					<box>
						<pen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" rotation="None">
						<font fontName="Microsoft Sans Serif" size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$F{data4}]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="false">
					<reportElement key="textField-172" mode="Opaque" x="579" y="60" width="13" height="15" forecolor="#000000" backcolor="#FFFFFF"/>
					<box>
						<pen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" rotation="None">
						<font fontName="Microsoft Sans Serif" size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$F{data12}]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="false">
					<reportElement key="textField-173" mode="Opaque" x="566" y="60" width="13" height="15" forecolor="#000000" backcolor="#FFFFFF"/>
					<box>
						<pen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" rotation="None">
						<font fontName="Microsoft Sans Serif" size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$F{data11}]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="false">
					<reportElement key="textField-174" mode="Opaque" x="553" y="60" width="13" height="15" forecolor="#000000" backcolor="#FFFFFF"/>
					<box>
						<pen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" rotation="None">
						<font fontName="Microsoft Sans Serif" size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$F{data10}]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="false">
					<reportElement key="textField-175" mode="Opaque" x="540" y="60" width="13" height="15" forecolor="#000000" backcolor="#FFFFFF"/>
					<box>
						<pen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" rotation="None">
						<font fontName="Microsoft Sans Serif" size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$F{data9}]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="false">
					<reportElement key="textField-176" mode="Opaque" x="527" y="60" width="13" height="15" forecolor="#000000" backcolor="#FFFFFF"/>
					<box>
						<pen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" rotation="None">
						<font fontName="Microsoft Sans Serif" size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$F{data8}]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="false">
					<reportElement key="textField-177" mode="Opaque" x="514" y="60" width="13" height="15" forecolor="#000000" backcolor="#FFFFFF"/>
					<box>
						<pen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" rotation="None">
						<font fontName="Microsoft Sans Serif" size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$F{data7}]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="false">
					<reportElement key="textField-178" mode="Opaque" x="592" y="60" width="13" height="15" forecolor="#000000" backcolor="#FFFFFF"/>
					<box>
						<pen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" rotation="None">
						<font fontName="Microsoft Sans Serif" size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$F{data13}]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="false">
					<reportElement key="textField-179" mode="Opaque" x="605" y="60" width="13" height="15" forecolor="#000000" backcolor="#FFFFFF"/>
					<box>
						<pen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" rotation="None">
						<font fontName="Microsoft Sans Serif" size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$F{data14}]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="false">
					<reportElement key="textField-180" mode="Opaque" x="618" y="60" width="13" height="15" forecolor="#000000" backcolor="#FFFFFF"/>
					<box>
						<pen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" rotation="None">
						<font fontName="Microsoft Sans Serif" size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$F{data15}]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="false">
					<reportElement key="textField-181" mode="Opaque" x="657" y="60" width="13" height="15" forecolor="#000000" backcolor="#FFFFFF"/>
					<box>
						<pen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" rotation="None">
						<font fontName="Microsoft Sans Serif" size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$F{data18}]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="false">
					<reportElement key="textField-182" mode="Opaque" x="644" y="60" width="13" height="15" forecolor="#000000" backcolor="#FFFFFF"/>
					<box>
						<pen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" rotation="None">
						<font fontName="Microsoft Sans Serif" size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$F{data17}]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="false">
					<reportElement key="textField-183" mode="Opaque" x="631" y="60" width="13" height="15" forecolor="#000000" backcolor="#FFFFFF"/>
					<box>
						<pen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" rotation="None">
						<font fontName="Microsoft Sans Serif" size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$F{data16}]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="false">
					<reportElement key="textField-184" mode="Opaque" x="735" y="60" width="13" height="15" forecolor="#000000" backcolor="#FFFFFF"/>
					<box>
						<pen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" rotation="None">
						<font fontName="Microsoft Sans Serif" size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$F{data24}]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="false">
					<reportElement key="textField-185" mode="Opaque" x="722" y="60" width="13" height="15" forecolor="#000000" backcolor="#FFFFFF"/>
					<box>
						<pen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" rotation="None">
						<font fontName="Microsoft Sans Serif" size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$F{data23}]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="false">
					<reportElement key="textField-186" mode="Opaque" x="709" y="60" width="13" height="15" forecolor="#000000" backcolor="#FFFFFF"/>
					<box>
						<pen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" rotation="None">
						<font fontName="Microsoft Sans Serif" size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$F{data22}]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="false">
					<reportElement key="textField-187" mode="Opaque" x="696" y="60" width="13" height="15" forecolor="#000000" backcolor="#FFFFFF"/>
					<box>
						<pen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" rotation="None">
						<font fontName="Microsoft Sans Serif" size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$F{data21}]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="false">
					<reportElement key="textField-188" mode="Opaque" x="683" y="60" width="13" height="15" forecolor="#000000" backcolor="#FFFFFF"/>
					<box>
						<pen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" rotation="None">
						<font fontName="Microsoft Sans Serif" size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$F{data20}]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="false">
					<reportElement key="textField-189" mode="Opaque" x="670" y="60" width="13" height="15" forecolor="#000000" backcolor="#FFFFFF"/>
					<box>
						<pen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" rotation="None">
						<font fontName="Microsoft Sans Serif" size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$F{data19}]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="false">
					<reportElement key="textField-190" mode="Opaque" x="748" y="60" width="13" height="15" forecolor="#000000" backcolor="#FFFFFF"/>
					<box>
						<pen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" rotation="None">
						<font fontName="Microsoft Sans Serif" size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$F{data25}]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="false">
					<reportElement key="textField-191" mode="Opaque" x="761" y="60" width="13" height="15" forecolor="#000000" backcolor="#FFFFFF"/>
					<box>
						<pen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" rotation="None">
						<font fontName="Microsoft Sans Serif" size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$F{data26}]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="false">
					<reportElement key="textField-192" mode="Opaque" x="774" y="60" width="13" height="15" forecolor="#000000" backcolor="#FFFFFF"/>
					<box>
						<pen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" rotation="None">
						<font fontName="Microsoft Sans Serif" size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$F{data27}]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="false">
					<reportElement key="textField-193" mode="Opaque" x="813" y="60" width="13" height="15" forecolor="#000000" backcolor="#FFFFFF"/>
					<box>
						<pen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" rotation="None">
						<font fontName="Microsoft Sans Serif" size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$F{data30}]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="false">
					<reportElement key="textField-194" mode="Opaque" x="800" y="60" width="13" height="15" forecolor="#000000" backcolor="#FFFFFF"/>
					<box>
						<pen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" rotation="None">
						<font fontName="Microsoft Sans Serif" size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$F{data29}]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="false">
					<reportElement key="textField-195" mode="Opaque" x="787" y="60" width="13" height="15" forecolor="#000000" backcolor="#FFFFFF"/>
					<box>
						<pen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" rotation="None">
						<font fontName="Microsoft Sans Serif" size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$F{data28}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement key="staticText-93" mode="Opaque" x="10" y="61" width="53" height="14"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Microsoft Sans Serif" size="9" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<text><![CDATA[ Mat.]]></text>
				</staticText>
				<staticText>
					<reportElement key="staticText-94" mode="Opaque" x="63" y="61" width="211" height="14"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Microsoft Sans Serif" size="9" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<text><![CDATA[ Nome do Aluno]]></text>
				</staticText>
				<staticText>
					<reportElement key="staticText-96" mode="Opaque" x="342" y="2" width="60" height="14"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Microsoft Sans Serif" size="12" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<text><![CDATA[Horário:]]></text>
				</staticText>
				<textField isBlankWhenNull="false">
					<reportElement key="textField-208" mode="Opaque" x="393" y="2" width="56" height="14"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Microsoft Sans Serif" size="12" isBold="false" pdfFontName="Helvetica"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$F{horarioTurma.horaInicial}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement key="staticText-97" mode="Opaque" x="451" y="2" width="20" height="14"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Microsoft Sans Serif" size="12" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<text><![CDATA[ às ]]></text>
				</staticText>
				<textField isBlankWhenNull="false">
					<reportElement key="textField-209" mode="Opaque" x="475" y="2" width="78" height="14"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Microsoft Sans Serif" size="12" isBold="false" pdfFontName="Helvetica"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$F{horarioTurma.horaFinal}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement key="staticText-98" mode="Opaque" x="566" y="2" width="54" height="14"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Microsoft Sans Serif" size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<text><![CDATA[Ambiente:]]></text>
				</staticText>
				<textField isBlankWhenNull="false">
					<reportElement key="textField-210" mode="Opaque" x="623" y="2" width="213" height="14"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Microsoft Sans Serif" size="10" isBold="false" pdfFontName="Helvetica"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$F{horarioTurma.ambiente.descricao}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement key="staticText-99" mode="Opaque" x="342" y="21" width="30" height="14"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Microsoft Sans Serif" size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<text><![CDATA[Nível:]]></text>
				</staticText>
				<textField isBlankWhenNull="false">
					<reportElement key="textField-211" mode="Opaque" x="373" y="21" width="97" height="14"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Microsoft Sans Serif" size="10" isBold="false" pdfFontName="Helvetica"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$F{horarioTurma.nivelTurma.descricao}]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="false">
					<reportElement key="textField-212" mode="Opaque" x="436" y="44" width="13" height="15"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Microsoft Sans Serif" size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$F{diaSemana1}]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="false">
					<reportElement key="textField-213" mode="Opaque" x="592" y="44" width="13" height="15"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Microsoft Sans Serif" size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$F{diaSemana13}]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="false">
					<reportElement key="textField-214" mode="Opaque" x="605" y="44" width="13" height="15"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Microsoft Sans Serif" size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$F{diaSemana14}]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="false">
					<reportElement key="textField-215" mode="Opaque" x="514" y="44" width="13" height="15"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Microsoft Sans Serif" size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$F{diaSemana7}]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="false">
					<reportElement key="textField-216" mode="Opaque" x="449" y="44" width="13" height="15"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Microsoft Sans Serif" size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$F{diaSemana2}]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="false">
					<reportElement key="textField-217" mode="Opaque" x="462" y="44" width="13" height="15"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Microsoft Sans Serif" size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$F{diaSemana3}]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="false">
					<reportElement key="textField-218" mode="Opaque" x="527" y="44" width="13" height="15"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Microsoft Sans Serif" size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$F{diaSemana8}]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="false">
					<reportElement key="textField-219" mode="Opaque" x="787" y="44" width="13" height="15"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Microsoft Sans Serif" size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$F{diaSemana28}]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="false">
					<reportElement key="textField-220" mode="Opaque" x="774" y="44" width="13" height="15"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Microsoft Sans Serif" size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$F{diaSemana27}]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="false">
					<reportElement key="textField-221" mode="Opaque" x="748" y="44" width="13" height="15"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Microsoft Sans Serif" size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$F{diaSemana25}]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="false">
					<reportElement key="textField-222" mode="Opaque" x="566" y="44" width="13" height="15"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Microsoft Sans Serif" size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$F{diaSemana11}]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="false">
					<reportElement key="textField-223" mode="Opaque" x="618" y="45" width="13" height="14"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Microsoft Sans Serif" size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$F{diaSemana15}]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="false">
					<reportElement key="textField-224" mode="Opaque" x="670" y="44" width="13" height="15"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Microsoft Sans Serif" size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$F{diaSemana19}]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="false">
					<reportElement key="textField-225" mode="Opaque" x="761" y="44" width="13" height="15"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Microsoft Sans Serif" size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$F{diaSemana26}]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="false">
					<reportElement key="textField-226" mode="Opaque" x="722" y="44" width="13" height="15"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Microsoft Sans Serif" size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$F{diaSemana23}]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="false">
					<reportElement key="textField-227" mode="Opaque" x="696" y="44" width="13" height="15"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Microsoft Sans Serif" size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$F{diaSemana21}]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="false">
					<reportElement key="textField-228" mode="Opaque" x="657" y="44" width="13" height="15"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Microsoft Sans Serif" size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$F{diaSemana18}]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="false">
					<reportElement key="textField-229" mode="Opaque" x="735" y="44" width="13" height="15"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Microsoft Sans Serif" size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$F{diaSemana24}]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="false">
					<reportElement key="textField-230" mode="Opaque" x="631" y="44" width="13" height="15"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Microsoft Sans Serif" size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$F{diaSemana16}]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="false">
					<reportElement key="textField-231" mode="Opaque" x="579" y="44" width="13" height="15"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Microsoft Sans Serif" size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$F{diaSemana12}]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="false">
					<reportElement key="textField-232" mode="Opaque" x="501" y="44" width="13" height="15"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Microsoft Sans Serif" size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$F{diaSemana6}]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="false">
					<reportElement key="textField-233" mode="Opaque" x="540" y="44" width="13" height="15"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Microsoft Sans Serif" size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$F{diaSemana9}]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="false">
					<reportElement key="textField-234" mode="Opaque" x="475" y="44" width="13" height="15"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Microsoft Sans Serif" size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$F{diaSemana4}]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="false">
					<reportElement key="textField-235" mode="Opaque" x="488" y="44" width="13" height="15"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Microsoft Sans Serif" size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$F{diaSemana5}]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="false">
					<reportElement key="textField-236" mode="Opaque" x="553" y="44" width="13" height="15"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Microsoft Sans Serif" size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$F{diaSemana10}]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="false">
					<reportElement key="textField-237" mode="Opaque" x="644" y="44" width="13" height="15"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Microsoft Sans Serif" size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$F{diaSemana17}]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="false">
					<reportElement key="textField-238" mode="Opaque" x="683" y="44" width="13" height="15"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Microsoft Sans Serif" size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$F{diaSemana20}]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="false">
					<reportElement key="textField-239" mode="Opaque" x="709" y="44" width="13" height="15"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Microsoft Sans Serif" size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$F{diaSemana22}]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="false">
					<reportElement key="textField-240" mode="Opaque" x="800" y="44" width="13" height="15"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Microsoft Sans Serif" size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$F{diaSemana29}]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="false">
					<reportElement key="textField-241" mode="Opaque" x="813" y="44" width="13" height="15"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Microsoft Sans Serif" size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$F{diaSemana30}]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="false">
					<reportElement key="textField-131" mode="Opaque" x="826" y="60" width="13" height="15" forecolor="#000000" backcolor="#FFFFFF"/>
					<box>
						<pen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" rotation="None">
						<font fontName="Microsoft Sans Serif" size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$F{data31}]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="false">
					<reportElement key="textField-212" mode="Opaque" x="826" y="44" width="13" height="15"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Microsoft Sans Serif" size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$F{diaSemana31}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement key="staticText-95" mode="Opaque" x="274" y="61" width="81" height="14"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Microsoft Sans Serif" size="9" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<text><![CDATA[ Dt Nascimento]]></text>
				</staticText>
			</band>
		</groupHeader>
		<groupFooter>
			<band height="62" splitType="Stretch">
				<rectangle radius="0">
					<reportElement key="rectangle-1" mode="Transparent" x="488" y="0" width="13" height="13" forecolor="#000000" backcolor="#FFFFFF"/>
				</rectangle>
				<rectangle radius="0">
					<reportElement key="rectangle-2" mode="Transparent" x="501" y="0" width="13" height="13" forecolor="#000000" backcolor="#FFFFFF"/>
				</rectangle>
				<rectangle radius="0">
					<reportElement key="rectangle-3" mode="Transparent" x="553" y="0" width="13" height="13" forecolor="#000000" backcolor="#FFFFFF"/>
				</rectangle>
				<rectangle radius="0">
					<reportElement key="rectangle-4" mode="Transparent" x="475" y="0" width="13" height="13" forecolor="#000000" backcolor="#FFFFFF"/>
				</rectangle>
				<rectangle radius="0">
					<reportElement key="rectangle-5" mode="Transparent" x="462" y="0" width="13" height="13" forecolor="#000000" backcolor="#FFFFFF"/>
				</rectangle>
				<rectangle radius="0">
					<reportElement key="rectangle-6" mode="Transparent" x="449" y="0" width="13" height="13" forecolor="#000000" backcolor="#FFFFFF"/>
				</rectangle>
				<rectangle radius="0">
					<reportElement key="rectangle-7" mode="Transparent" x="436" y="0" width="13" height="13" forecolor="#000000" backcolor="#FFFFFF"/>
				</rectangle>
				<rectangle radius="0">
					<reportElement key="rectangle-8" mode="Transparent" x="540" y="0" width="13" height="13" forecolor="#000000" backcolor="#FFFFFF"/>
				</rectangle>
				<rectangle radius="0">
					<reportElement key="rectangle-9" mode="Transparent" x="527" y="0" width="13" height="13" forecolor="#000000" backcolor="#FFFFFF"/>
				</rectangle>
				<rectangle radius="0">
					<reportElement key="rectangle-10" mode="Transparent" x="514" y="0" width="13" height="13" forecolor="#000000" backcolor="#FFFFFF"/>
				</rectangle>
				<rectangle radius="0">
					<reportElement key="rectangle-11" mode="Transparent" x="748" y="0" width="13" height="13" forecolor="#000000" backcolor="#FFFFFF"/>
				</rectangle>
				<rectangle radius="0">
					<reportElement key="rectangle-12" mode="Transparent" x="761" y="0" width="13" height="13" forecolor="#000000" backcolor="#FFFFFF"/>
				</rectangle>
				<rectangle radius="0">
					<reportElement key="rectangle-13" mode="Transparent" x="813" y="0" width="13" height="13" forecolor="#000000" backcolor="#FFFFFF"/>
				</rectangle>
				<rectangle radius="0">
					<reportElement key="rectangle-14" mode="Transparent" x="735" y="0" width="13" height="13" forecolor="#000000" backcolor="#FFFFFF"/>
				</rectangle>
				<rectangle radius="0">
					<reportElement key="rectangle-15" mode="Transparent" x="722" y="0" width="13" height="13" forecolor="#000000" backcolor="#FFFFFF"/>
				</rectangle>
				<rectangle radius="0">
					<reportElement key="rectangle-16" mode="Transparent" x="709" y="0" width="13" height="13" forecolor="#000000" backcolor="#FFFFFF"/>
				</rectangle>
				<rectangle radius="0">
					<reportElement key="rectangle-17" mode="Transparent" x="696" y="0" width="13" height="13" forecolor="#000000" backcolor="#FFFFFF"/>
				</rectangle>
				<rectangle radius="0">
					<reportElement key="rectangle-18" mode="Transparent" x="800" y="0" width="13" height="13" forecolor="#000000" backcolor="#FFFFFF"/>
				</rectangle>
				<rectangle radius="0">
					<reportElement key="rectangle-19" mode="Transparent" x="787" y="0" width="13" height="13" forecolor="#000000" backcolor="#FFFFFF"/>
				</rectangle>
				<rectangle radius="0">
					<reportElement key="rectangle-20" mode="Transparent" x="774" y="0" width="13" height="13" forecolor="#000000" backcolor="#FFFFFF"/>
				</rectangle>
				<rectangle radius="0">
					<reportElement key="rectangle-21" mode="Transparent" x="618" y="0" width="13" height="13" forecolor="#000000" backcolor="#FFFFFF"/>
				</rectangle>
				<rectangle radius="0">
					<reportElement key="rectangle-22" mode="Transparent" x="631" y="0" width="13" height="13" forecolor="#000000" backcolor="#FFFFFF"/>
				</rectangle>
				<rectangle radius="0">
					<reportElement key="rectangle-23" mode="Transparent" x="683" y="0" width="13" height="13" forecolor="#000000" backcolor="#FFFFFF"/>
				</rectangle>
				<rectangle radius="0">
					<reportElement key="rectangle-24" mode="Transparent" x="605" y="0" width="13" height="13" forecolor="#000000" backcolor="#FFFFFF"/>
				</rectangle>
				<rectangle radius="0">
					<reportElement key="rectangle-25" mode="Transparent" x="592" y="0" width="13" height="13" forecolor="#000000" backcolor="#FFFFFF"/>
				</rectangle>
				<rectangle radius="0">
					<reportElement key="rectangle-26" mode="Transparent" x="579" y="0" width="13" height="13" forecolor="#000000" backcolor="#FFFFFF"/>
				</rectangle>
				<rectangle radius="0">
					<reportElement key="rectangle-27" mode="Transparent" x="566" y="0" width="13" height="13" forecolor="#000000" backcolor="#FFFFFF"/>
				</rectangle>
				<rectangle radius="0">
					<reportElement key="rectangle-28" mode="Transparent" x="670" y="0" width="13" height="13" forecolor="#000000" backcolor="#FFFFFF"/>
				</rectangle>
				<rectangle radius="0">
					<reportElement key="rectangle-29" mode="Transparent" x="657" y="0" width="13" height="13" forecolor="#000000" backcolor="#FFFFFF"/>
				</rectangle>
				<rectangle radius="0">
					<reportElement key="rectangle-30" mode="Transparent" x="644" y="0" width="13" height="13" forecolor="#000000" backcolor="#FFFFFF"/>
				</rectangle>
				<rectangle radius="0">
					<reportElement key="rectangle-31" x="10" y="0" width="426" height="13" forecolor="#000000" backcolor="#FFFFFF"/>
				</rectangle>
				<rectangle radius="0">
					<reportElement key="rectangle-32" mode="Transparent" x="488" y="13" width="13" height="13" forecolor="#000000" backcolor="#FFFFFF"/>
				</rectangle>
				<rectangle radius="0">
					<reportElement key="rectangle-33" mode="Transparent" x="501" y="13" width="13" height="13" forecolor="#000000" backcolor="#FFFFFF"/>
				</rectangle>
				<rectangle radius="0">
					<reportElement key="rectangle-34" mode="Transparent" x="553" y="13" width="13" height="13" forecolor="#000000" backcolor="#FFFFFF"/>
				</rectangle>
				<rectangle radius="0">
					<reportElement key="rectangle-35" mode="Transparent" x="475" y="13" width="13" height="13" forecolor="#000000" backcolor="#FFFFFF"/>
				</rectangle>
				<rectangle radius="0">
					<reportElement key="rectangle-36" mode="Transparent" x="462" y="13" width="13" height="13" forecolor="#000000" backcolor="#FFFFFF"/>
				</rectangle>
				<rectangle radius="0">
					<reportElement key="rectangle-37" mode="Transparent" x="449" y="13" width="13" height="13" forecolor="#000000" backcolor="#FFFFFF"/>
				</rectangle>
				<rectangle radius="0">
					<reportElement key="rectangle-38" mode="Transparent" x="436" y="13" width="13" height="13" forecolor="#000000" backcolor="#FFFFFF"/>
				</rectangle>
				<rectangle radius="0">
					<reportElement key="rectangle-39" mode="Transparent" x="540" y="13" width="13" height="13" forecolor="#000000" backcolor="#FFFFFF"/>
				</rectangle>
				<rectangle radius="0">
					<reportElement key="rectangle-40" mode="Transparent" x="527" y="13" width="13" height="13" forecolor="#000000" backcolor="#FFFFFF"/>
				</rectangle>
				<rectangle radius="0">
					<reportElement key="rectangle-41" mode="Transparent" x="514" y="13" width="13" height="13" forecolor="#000000" backcolor="#FFFFFF"/>
				</rectangle>
				<rectangle radius="0">
					<reportElement key="rectangle-42" mode="Transparent" x="748" y="13" width="13" height="13" forecolor="#000000" backcolor="#FFFFFF"/>
				</rectangle>
				<rectangle radius="0">
					<reportElement key="rectangle-43" mode="Transparent" x="761" y="13" width="13" height="13" forecolor="#000000" backcolor="#FFFFFF"/>
				</rectangle>
				<rectangle radius="0">
					<reportElement key="rectangle-44" mode="Transparent" x="813" y="13" width="13" height="13" forecolor="#000000" backcolor="#FFFFFF"/>
				</rectangle>
				<rectangle radius="0">
					<reportElement key="rectangle-45" mode="Transparent" x="735" y="13" width="13" height="13" forecolor="#000000" backcolor="#FFFFFF"/>
				</rectangle>
				<rectangle radius="0">
					<reportElement key="rectangle-46" mode="Transparent" x="722" y="13" width="13" height="13" forecolor="#000000" backcolor="#FFFFFF"/>
				</rectangle>
				<rectangle radius="0">
					<reportElement key="rectangle-47" mode="Transparent" x="709" y="13" width="13" height="13" forecolor="#000000" backcolor="#FFFFFF"/>
				</rectangle>
				<rectangle radius="0">
					<reportElement key="rectangle-48" mode="Transparent" x="696" y="13" width="13" height="13" forecolor="#000000" backcolor="#FFFFFF"/>
				</rectangle>
				<rectangle radius="0">
					<reportElement key="rectangle-49" mode="Transparent" x="800" y="13" width="13" height="13" forecolor="#000000" backcolor="#FFFFFF"/>
				</rectangle>
				<rectangle radius="0">
					<reportElement key="rectangle-50" mode="Transparent" x="787" y="13" width="13" height="13" forecolor="#000000" backcolor="#FFFFFF"/>
				</rectangle>
				<rectangle radius="0">
					<reportElement key="rectangle-51" mode="Transparent" x="774" y="13" width="13" height="13" forecolor="#000000" backcolor="#FFFFFF"/>
				</rectangle>
				<rectangle radius="0">
					<reportElement key="rectangle-52" mode="Transparent" x="618" y="13" width="13" height="13" forecolor="#000000" backcolor="#FFFFFF"/>
				</rectangle>
				<rectangle radius="0">
					<reportElement key="rectangle-53" mode="Transparent" x="631" y="13" width="13" height="13" forecolor="#000000" backcolor="#FFFFFF"/>
				</rectangle>
				<rectangle radius="0">
					<reportElement key="rectangle-54" mode="Transparent" x="683" y="13" width="13" height="13" forecolor="#000000" backcolor="#FFFFFF"/>
				</rectangle>
				<rectangle radius="0">
					<reportElement key="rectangle-55" mode="Transparent" x="605" y="13" width="13" height="13" forecolor="#000000" backcolor="#FFFFFF"/>
				</rectangle>
				<rectangle radius="0">
					<reportElement key="rectangle-56" mode="Transparent" x="592" y="13" width="13" height="13" forecolor="#000000" backcolor="#FFFFFF"/>
				</rectangle>
				<rectangle radius="0">
					<reportElement key="rectangle-57" mode="Transparent" x="579" y="13" width="13" height="13" forecolor="#000000" backcolor="#FFFFFF"/>
				</rectangle>
				<rectangle radius="0">
					<reportElement key="rectangle-58" mode="Transparent" x="566" y="13" width="13" height="13" forecolor="#000000" backcolor="#FFFFFF"/>
				</rectangle>
				<rectangle radius="0">
					<reportElement key="rectangle-59" mode="Transparent" x="670" y="13" width="13" height="13" forecolor="#000000" backcolor="#FFFFFF"/>
				</rectangle>
				<rectangle radius="0">
					<reportElement key="rectangle-60" mode="Transparent" x="657" y="13" width="13" height="13" forecolor="#000000" backcolor="#FFFFFF"/>
				</rectangle>
				<rectangle radius="0">
					<reportElement key="rectangle-61" mode="Transparent" x="644" y="13" width="13" height="13" forecolor="#000000" backcolor="#FFFFFF"/>
				</rectangle>
				<rectangle radius="0">
					<reportElement key="rectangle-62" x="10" y="13" width="426" height="13" forecolor="#000000" backcolor="#FFFFFF"/>
				</rectangle>
				<rectangle radius="0">
					<reportElement key="rectangle-63" mode="Transparent" x="488" y="26" width="13" height="13" forecolor="#000000" backcolor="#FFFFFF"/>
				</rectangle>
				<rectangle radius="0">
					<reportElement key="rectangle-64" mode="Transparent" x="501" y="26" width="13" height="13" forecolor="#000000" backcolor="#FFFFFF"/>
				</rectangle>
				<rectangle radius="0">
					<reportElement key="rectangle-65" mode="Transparent" x="553" y="26" width="13" height="13" forecolor="#000000" backcolor="#FFFFFF"/>
				</rectangle>
				<rectangle radius="0">
					<reportElement key="rectangle-66" mode="Transparent" x="475" y="26" width="13" height="13" forecolor="#000000" backcolor="#FFFFFF"/>
				</rectangle>
				<rectangle radius="0">
					<reportElement key="rectangle-67" mode="Transparent" x="462" y="26" width="13" height="13" forecolor="#000000" backcolor="#FFFFFF"/>
				</rectangle>
				<rectangle radius="0">
					<reportElement key="rectangle-68" mode="Transparent" x="449" y="26" width="13" height="13" forecolor="#000000" backcolor="#FFFFFF"/>
				</rectangle>
				<rectangle radius="0">
					<reportElement key="rectangle-69" mode="Transparent" x="436" y="26" width="13" height="13" forecolor="#000000" backcolor="#FFFFFF"/>
				</rectangle>
				<rectangle radius="0">
					<reportElement key="rectangle-70" mode="Transparent" x="540" y="26" width="13" height="13" forecolor="#000000" backcolor="#FFFFFF"/>
				</rectangle>
				<rectangle radius="0">
					<reportElement key="rectangle-71" mode="Transparent" x="527" y="26" width="13" height="13" forecolor="#000000" backcolor="#FFFFFF"/>
				</rectangle>
				<rectangle radius="0">
					<reportElement key="rectangle-72" mode="Transparent" x="514" y="26" width="13" height="13" forecolor="#000000" backcolor="#FFFFFF"/>
				</rectangle>
				<rectangle radius="0">
					<reportElement key="rectangle-73" mode="Transparent" x="748" y="26" width="13" height="13" forecolor="#000000" backcolor="#FFFFFF"/>
				</rectangle>
				<rectangle radius="0">
					<reportElement key="rectangle-74" mode="Transparent" x="761" y="26" width="13" height="13" forecolor="#000000" backcolor="#FFFFFF"/>
				</rectangle>
				<rectangle radius="0">
					<reportElement key="rectangle-75" mode="Transparent" x="813" y="26" width="13" height="13" forecolor="#000000" backcolor="#FFFFFF"/>
				</rectangle>
				<rectangle radius="0">
					<reportElement key="rectangle-76" mode="Transparent" x="735" y="26" width="13" height="13" forecolor="#000000" backcolor="#FFFFFF"/>
				</rectangle>
				<rectangle radius="0">
					<reportElement key="rectangle-77" mode="Transparent" x="722" y="26" width="13" height="13" forecolor="#000000" backcolor="#FFFFFF"/>
				</rectangle>
				<rectangle radius="0">
					<reportElement key="rectangle-78" mode="Transparent" x="709" y="26" width="13" height="13" forecolor="#000000" backcolor="#FFFFFF"/>
				</rectangle>
				<rectangle radius="0">
					<reportElement key="rectangle-79" mode="Transparent" x="696" y="26" width="13" height="13" forecolor="#000000" backcolor="#FFFFFF"/>
				</rectangle>
				<rectangle radius="0">
					<reportElement key="rectangle-80" mode="Transparent" x="800" y="26" width="13" height="13" forecolor="#000000" backcolor="#FFFFFF"/>
				</rectangle>
				<rectangle radius="0">
					<reportElement key="rectangle-81" mode="Transparent" x="787" y="26" width="13" height="13" forecolor="#000000" backcolor="#FFFFFF"/>
				</rectangle>
				<rectangle radius="0">
					<reportElement key="rectangle-82" mode="Transparent" x="774" y="26" width="13" height="13" forecolor="#000000" backcolor="#FFFFFF"/>
				</rectangle>
				<rectangle radius="0">
					<reportElement key="rectangle-83" mode="Transparent" x="618" y="26" width="13" height="13" forecolor="#000000" backcolor="#FFFFFF"/>
				</rectangle>
				<rectangle radius="0">
					<reportElement key="rectangle-84" mode="Transparent" x="631" y="26" width="13" height="13" forecolor="#000000" backcolor="#FFFFFF"/>
				</rectangle>
				<rectangle radius="0">
					<reportElement key="rectangle-85" mode="Transparent" x="683" y="26" width="13" height="13" forecolor="#000000" backcolor="#FFFFFF"/>
				</rectangle>
				<rectangle radius="0">
					<reportElement key="rectangle-86" mode="Transparent" x="605" y="26" width="13" height="13" forecolor="#000000" backcolor="#FFFFFF"/>
				</rectangle>
				<rectangle radius="0">
					<reportElement key="rectangle-87" mode="Transparent" x="592" y="26" width="13" height="13" forecolor="#000000" backcolor="#FFFFFF"/>
				</rectangle>
				<rectangle radius="0">
					<reportElement key="rectangle-88" mode="Transparent" x="579" y="26" width="13" height="13" forecolor="#000000" backcolor="#FFFFFF"/>
				</rectangle>
				<rectangle radius="0">
					<reportElement key="rectangle-89" mode="Transparent" x="566" y="26" width="13" height="13" forecolor="#000000" backcolor="#FFFFFF"/>
				</rectangle>
				<rectangle radius="0">
					<reportElement key="rectangle-90" mode="Transparent" x="670" y="26" width="13" height="13" forecolor="#000000" backcolor="#FFFFFF"/>
				</rectangle>
				<rectangle radius="0">
					<reportElement key="rectangle-91" mode="Transparent" x="657" y="26" width="13" height="13" forecolor="#000000" backcolor="#FFFFFF"/>
				</rectangle>
				<rectangle radius="0">
					<reportElement key="rectangle-92" mode="Transparent" x="644" y="26" width="13" height="13" forecolor="#000000" backcolor="#FFFFFF"/>
				</rectangle>
				<rectangle radius="0">
					<reportElement key="rectangle-93" x="10" y="26" width="426" height="13" forecolor="#000000" backcolor="#FFFFFF"/>
				</rectangle>
				<staticText>
					<reportElement key="staticText-101" mode="Opaque" x="744" y="43" width="51" height="14"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Microsoft Sans Serif" size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<text><![CDATA[Alunos:]]></text>
				</staticText>
				<textField isBlankWhenNull="false">
					<reportElement key="textField-244" mode="Opaque" x="798" y="43" width="23" height="14"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Microsoft Sans Serif" size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<textFieldExpression class="java.lang.Integer"><![CDATA[$F{qtdAluno}]]></textFieldExpression>
				</textField>
				<rectangle radius="0">
					<reportElement key="rectangle-7" mode="Transparent" x="826" y="0" width="13" height="13" forecolor="#000000" backcolor="#FFFFFF"/>
				</rectangle>
				<rectangle radius="0">
					<reportElement key="rectangle-38" mode="Transparent" x="826" y="13" width="13" height="13" forecolor="#000000" backcolor="#FFFFFF"/>
				</rectangle>
				<rectangle radius="0">
					<reportElement key="rectangle-69" mode="Transparent" x="826" y="26" width="13" height="13" forecolor="#000000" backcolor="#FFFFFF"/>
				</rectangle>
			</band>
		</groupFooter>
	</group>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band splitType="Stretch"/>
	</title>
	<pageHeader>
		<band height="70" splitType="Stretch">
			<image isUsingCache="true" onErrorType="Blank">
				<reportElement key="image-1" stretchType="RelativeToBandHeight" x="1" y="1" width="75" height="65" isPrintWhenDetailOverflows="true"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<imageExpression class="java.io.InputStream"><![CDATA[$P{logoPadraoRelatorio}]]></imageExpression>
			</image>
			<staticText>
				<reportElement key="staticText-91" mode="Opaque" x="577" y="2" width="262" height="23"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Top" lineSpacing="Single">
					<font fontName="Microsoft Sans Serif" size="9" isBold="true" isItalic="true" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica-BoldOblique"/>
				</textElement>
				<text><![CDATA[ZillyonWeb - Sistema Administrativo para Academias Desenvolvido por PACTO Soluções Tecnológicas Ltda.]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-92" mode="Opaque" x="728" y="25" width="111" height="12"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Top" lineSpacing="Single">
					<font fontName="Microsoft Sans Serif" size="9" isBold="true" isItalic="true" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica-BoldOblique"/>
				</textElement>
				<text><![CDATA[(0xx62) 3251-5820]]></text>
			</staticText>
			<line>
				<reportElement key="line-1" mode="Transparent" x="2" y="68" width="837" height="1"/>
				<graphicElement fill="Solid">
					<pen lineWidth="0.5" lineStyle="Solid"/>
				</graphicElement>
			</line>
			<staticText>
				<reportElement key="staticText-100" x="527" y="39" width="312" height="27"/>
				<textElement textAlignment="Right">
					<font size="20" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Lista de Chamada]]></text>
			</staticText>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-245" x="115" y="53" width="74" height="14"/>
				<textElement>
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{dataIni}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-246" x="217" y="53" width="74" height="14"/>
				<textElement>
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{dataFim}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement key="staticText-102" mode="Opaque" x="92" y="53" width="20" height="14"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="12" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[De]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-103" mode="Opaque" x="191" y="53" width="24" height="14"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="12" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[até]]></text>
			</staticText>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-247" x="92" y="5" width="340" height="14"/>
				<textElement>
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{nomeEmpresa}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-248" x="92" y="21" width="340" height="14"/>
				<textElement>
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{enderecoEmpresa}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-249" x="92" y="37" width="340" height="14"/>
				<textElement>
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{cidadeEmpresa}]]></textFieldExpression>
			</textField>
		</band>
	</pageHeader>
	<columnHeader>
		<band splitType="Stretch"/>
	</columnHeader>
	<detail>
		<band height="15" splitType="Stretch">
			<subreport isUsingCache="true">
				<reportElement key="subreport-2" positionType="Float" stretchType="RelativeToTallestObject" x="0" y="2" width="839" height="13"/>
				<subreportParameter name="listaAlunos">
					<subreportParameterExpression><![CDATA[$F{listaAlunos}]]></subreportParameterExpression>
				</subreportParameter>
				<dataSourceExpression><![CDATA[$F{listaAlunos}]]></dataSourceExpression>
				<subreportExpression class="java.lang.String"><![CDATA[$P{SUBREPORT_DIR} + "ListaChamadaAluno.jasper"]]></subreportExpression>
			</subreport>
		</band>
	</detail>
	<pageFooter>
		<band height="30" splitType="Stretch">
			<textField evaluationTime="Report" isBlankWhenNull="false">
				<reportElement key="textField-242" x="793" y="6" width="45" height="17"/>
				<box bottomPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement>
					<font fontName="Arial" size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[" " + $V{PAGE_NUMBER} + ""]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-243" x="710" y="6" width="83" height="17"/>
				<box bottomPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right">
					<font fontName="Arial" size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA["Página: " + $V{PAGE_NUMBER} + " de "]]></textFieldExpression>
			</textField>
			<textField pattern="dd/MM/yyyy HH.mm.ss" isBlankWhenNull="false">
				<reportElement key="dataRel-1" mode="Opaque" x="4" y="6" width="204" height="17" backcolor="#FFFFFF"/>
				<box bottomPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Verdana" size="10" isBold="true" isItalic="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.util.Date"><![CDATA[new Date()]]></textFieldExpression>
			</textField>
			<line>
				<reportElement key="line-2" mode="Transparent" x="2" y="2" width="837" height="1"/>
				<graphicElement fill="Solid"/>
			</line>
		</band>
	</pageFooter>
	<summary>
		<band splitType="Stretch"/>
	</summary>
</jasperReport>

<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="ComprovanteOperacaoContrato" pageWidth="595" pageHeight="838" columnWidth="553" leftMargin="21" rightMargin="21" topMargin="7" bottomMargin="21" whenResourceMissingType="Empty">
	<property name="ireport.zoom" value="2.143588810000002"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.encoding" value="ISO-8859-1"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="java.util.*"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<parameter name="tituloRelatorio" class="java.lang.String" isForPrompting="false"/>
	<parameter name="nomeEmpresa" class="java.lang.String" isForPrompting="false"/>
	<parameter name="versaoSoftware" class="java.lang.String" isForPrompting="false"/>
	<parameter name="usuario" class="java.lang.String" isForPrompting="false"/>
	<parameter name="SUBREPORT_DIR" class="java.lang.String">
		<defaultValueExpression><![CDATA["C:\\PactoJ\\ZillyonWebTronco\\src\\main\\resources\\relatorio\\designRelatorio\\financeiro\\"]]></defaultValueExpression>
	</parameter>
	<parameter name="SUBREPORT_DIR1" class="java.lang.String">
		<defaultValueExpression><![CDATA["E:\\desenvolvimentosv\\Projeto Pacto Solucoes\\ZillyonWeb\\src\\java\\relatorio\\designRelatorio\\financeiro\\"]]></defaultValueExpression>
	</parameter>
	<parameter name="SUBREPORT_DIR2" class="java.lang.String">
		<defaultValueExpression><![CDATA["C:\\PactoJ\\Desenvolvimento\\Sistemas\\ZillyonWeb\\trunk\\src\\main\\resources\\relatorio\\designRelatorio\\financeiro\\"]]></defaultValueExpression>
	</parameter>
	<parameter name="empresaVO.cnpj" class="java.lang.String"/>
	<parameter name="empresaVO.endereco" class="java.lang.String"/>
	<parameter name="empresaVO.site" class="java.lang.String"/>
	<parameter name="empresaVO.fone" class="java.lang.String"/>
	<parameter name="logoPadraoRelatorio" class="java.io.InputStream" isForPrompting="false"/>
	<parameter name="dataInicioEfetivacaoOperacao_Apresentar" class="java.lang.String" isForPrompting="false"/>
	<parameter name="dataFimEfetivacaoOperacao_Apresentar" class="java.lang.String" isForPrompting="false"/>
	<parameter name="dataOperacao_Apresentar" class="java.lang.String" isForPrompting="false"/>
	<parameter name="responsavel.nome" class="java.lang.String" isForPrompting="false"/>
	<parameter name="tipoOperacao_Apresentar" class="java.lang.String" isForPrompting="false"/>
	<parameter name="justificativaApresentar" class="java.lang.String" isForPrompting="false"/>
	<parameter name="observacao" class="java.lang.String" isForPrompting="false"/>
	<parameter name="descricaoCalculo" class="java.lang.String" isForPrompting="false"/>
	<parameter name="pessoa" class="java.lang.String"/>
	<parameter name="contrato" class="java.lang.Integer"/>
	<field name="descricao" class="java.lang.String"/>
	<field name="cliente.nome_Apresentar" class="java.lang.String"/>
	<field name="cliente.pessoa.cfp" class="java.lang.String"/>
	<field name="dataConfirmacao" class="java.util.Date"/>
	<field name="cliente.matricula" class="java.lang.String"/>
	<field name="brinde.nome" class="java.lang.String"/>
	<field name="observacao" class="java.lang.String"/>
	<field name="pontos" class="java.lang.Integer"/>
	<detail>
		<band height="75">
			<staticText>
				<reportElement key="staticText-85" positionType="Float" mode="Opaque" x="5" y="44" width="107" height="13"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="SansSerif" size="9" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Matrícula]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-88" positionType="Float" mode="Opaque" x="439" y="44" width="97" height="13"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="SansSerif" size="9" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[CPF]]></text>
			</staticText>
			<image isUsingCache="true" onErrorType="Blank" evaluationTime="Page">
				<reportElement key="image-1" x="2" y="3" width="110" height="39" isPrintWhenDetailOverflows="true" forecolor="#FFFFFF"/>
				<imageExpression class="java.io.InputStream"><![CDATA[$P{logoPadraoRelatorio}]]></imageExpression>
			</image>
			<rectangle radius="10">
				<reportElement key="retDadosEmpresa1" x="115" y="3" width="318" height="39" backcolor="#F0F0F0"/>
				<graphicElement>
					<pen lineWidth="0.25"/>
				</graphicElement>
			</rectangle>
			<textField>
				<reportElement x="119" y="4" width="220" height="13"/>
				<textElement>
					<font fontName="Microsoft Sans Serif" isBold="true" isItalic="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{nomeEmpresa}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="119" y="16" width="314" height="13"/>
				<textElement>
					<font fontName="Microsoft Sans Serif" isBold="true" isItalic="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{empresaVO.endereco}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="119" y="28" width="314" height="13"/>
				<textElement>
					<font fontName="Microsoft Sans Serif" isBold="true" isItalic="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{empresaVO.site}.toLowerCase()]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="332" y="4" width="101" height="13"/>
				<textElement>
					<font fontName="Microsoft Sans Serif" isBold="true" isItalic="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{empresaVO.cnpj}]]></textFieldExpression>
			</textField>
			<rectangle radius="10">
				<reportElement key="retDadosRecibo1" x="437" y="3" width="113" height="39" backcolor="#F0F0F0"/>
				<graphicElement>
					<pen lineWidth="0.25"/>
				</graphicElement>
			</rectangle>
			<textField>
				<reportElement x="337" y="29" width="96" height="13"/>
				<textElement>
					<font fontName="Microsoft Sans Serif" isBold="true" isItalic="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{empresaVO.fone}]]></textFieldExpression>
			</textField>
			<line>
				<reportElement x="1" y="61" width="552" height="1"/>
			</line>
			<staticText>
				<reportElement key="staticText-85" positionType="Float" mode="Opaque" x="115" y="44" width="318" height="13"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="SansSerif" size="9" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Nome]]></text>
			</staticText>
			<staticText>
				<reportElement stretchType="RelativeToBandHeight" x="439" y="14" width="109" height="18" isPrintWhenDetailOverflows="true"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="11"/>
				</textElement>
				<text><![CDATA[Resgate de brinde]]></text>
			</staticText>
			<textField>
				<reportElement x="5" y="62" width="107" height="13"/>
				<textElement>
					<font size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{cliente.matricula}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="115" y="62" width="318" height="13"/>
				<textElement>
					<font size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{cliente.nome_Apresentar}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="439" y="62" width="107" height="13"/>
				<textElement>
					<font size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{cliente.pessoa.cfp}]]></textFieldExpression>
			</textField>
		</band>
		<band height="55">
			<staticText>
				<reportElement key="staticText-85" positionType="Float" mode="Opaque" x="5" y="6" width="107" height="13"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="SansSerif" size="8" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Brinde resgatado]]></text>
			</staticText>
			<line>
				<reportElement x="1" y="20" width="552" height="1"/>
			</line>
			<textField>
				<reportElement x="5" y="21" width="541" height="13"/>
				<textElement>
					<font size="9"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{brinde.nome}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement key="staticText-85" positionType="Float" mode="Transparent" x="5" y="36" width="541" height="10" isPrintWhenDetailOverflows="true">
					<printWhenExpression><![CDATA[$F{observacao}.length() > 0]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle" markup="none">
					<font fontName="SansSerif" size="7" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA["Observação: " + $F{observacao}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement key="staticText-85" positionType="Float" isPrintRepeatedValues="false" mode="Transparent" x="401" y="6" width="145" height="13" isPrintWhenDetailOverflows="true"/>
				<textElement textAlignment="Right" verticalAlignment="Middle" markup="none">
					<font fontName="SansSerif" size="8" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA["Pontos gastos: " + $F{pontos}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<columnFooter>
		<band height="72">
			<line>
				<reportElement x="5" y="16" width="259" height="1"/>
			</line>
			<line>
				<reportElement x="278" y="16" width="260" height="1"/>
			</line>
			<textField>
				<reportElement x="278" y="17" width="260" height="12"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="8" isBold="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA["Cliente: " + $F{cliente.nome_Apresentar}]]></textFieldExpression>
			</textField>
			<textField pattern="dd/MM/yyyy HH:mm:ss" isBlankWhenNull="false">
				<reportElement key="dataImpressao1" mode="Transparent" x="488" y="61" width="60" height="8"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="SansSerif" size="6" isItalic="false" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression class="java.util.Date"><![CDATA[$F{dataConfirmacao}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="5" y="17" width="259" height="12"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="8" isBold="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA["Impresso por: " + $P{usuario}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="439" y="61" width="49" height="8"/>
				<textElement>
					<font size="6" isBold="true"/>
				</textElement>
				<text><![CDATA[Resgate em:]]></text>
			</staticText>
			<line>
				<reportElement x="0" y="69" width="550" height="1"/>
				<graphicElement>
					<pen lineWidth="1.5" lineStyle="Dashed"/>
				</graphicElement>
			</line>
			<textField>
				<reportElement x="278" y="30" width="260" height="12"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="8" isBold="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA["CPF: " + $F{cliente.pessoa.cfp}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="439" y="52" width="49" height="8"/>
				<textElement>
					<font size="6" isBold="true"/>
				</textElement>
				<text><![CDATA[Data impressão:]]></text>
			</staticText>
			<textField pattern="dd/MM/yyyy HH:mm:ss" isBlankWhenNull="false">
				<reportElement key="dataImpressao1" mode="Transparent" x="488" y="52" width="60" height="8"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="SansSerif" size="6" isItalic="false" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression class="java.util.Date"><![CDATA[new Date()]]></textFieldExpression>
			</textField>
		</band>
	</columnFooter>
</jasperReport>

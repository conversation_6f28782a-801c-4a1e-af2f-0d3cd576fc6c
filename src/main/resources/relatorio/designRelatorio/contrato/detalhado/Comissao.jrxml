<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="ComissaoRel" pageWidth="878" pageHeight="680" orientation="Landscape" whenNoDataType="AllSectionsNoDetail" columnWidth="823" leftMargin="20" rightMargin="35" topMargin="2" bottomMargin="2" whenResourceMissingType="Empty">
	<property name="ireport.zoom" value="1.3286707500000006"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.encoding" value="ISO-8859-1"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="java.util.*"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<parameter name="tituloRelatorio" class="java.lang.String" isForPrompting="false"/>
	<parameter name="nomeEmpresa" class="java.lang.String" isForPrompting="false"/>
	<parameter name="usuario" class="java.lang.String" isForPrompting="false"/>
	<parameter name="filtros" class="java.lang.String" isForPrompting="false"/>
	<parameter name="SUBREPORT_DIR" class="java.lang.String">
		<defaultValueExpression><![CDATA["D:\\PactoJ\\Desenvolvimento\\Sistemas\\ZillyonWeb\\tronco-novo\\src\\java\\relatorio\\designRelatorio\\contrato\\"]]></defaultValueExpression>
	</parameter>
	<parameter name="SUBREPORT_DIR1" class="java.lang.String">
		<defaultValueExpression><![CDATA["E:\\desenvolvimentosv\\Projeto Pacto Solucoes\\ZillyonWeb\\src\\java\\relatorio\\designRelatorio\\financeiro\\"]]></defaultValueExpression>
	</parameter>
	<parameter name="logoPadraoRelatorio" class="java.io.InputStream" isForPrompting="false"/>
	<parameter name="SUBREPORT_DIR2" class="java.lang.String">
		<defaultValueExpression><![CDATA["E:\\desenvolvimentosv\\Projeto Pacto Solucoes\\ZillyonWeb\\src\\java\\relatorio\\designRelatorio\\financeiro\\"]]></defaultValueExpression>
	</parameter>
	<parameter name="total" class="java.lang.String"/>
	<parameter name="modoVisualizacao" class="java.lang.String"/>
	<parameter name="qtdAlunos" class="java.lang.Integer"/>
	<parameter name="totalGeralPago" class="java.lang.String"/>
	<parameter name="comissaoMatriculaRematricula" class="java.lang.Boolean"/>
	<parameter name="descricaoPeriodo" class="java.lang.String"/>
	<parameter name="qtdContratosTotal" class="java.lang.Integer"/>
	<parameter name="pagarComissaoProdutos" class="java.lang.Boolean"/>
	<field name="nome" class="java.lang.String"/>
	<field name="listaConfiguracao" class="java.lang.Object"/>
	<field name="valorTotalComissao" class="java.lang.String"/>
	<field name="valorTotal" class="java.lang.String"/>
	<field name="codigo" class="java.lang.Integer"/>
	<field name="qtdContratos" class="java.lang.Integer"/>
	<field name="qtdProdutos" class="java.lang.Integer"/>
	<field name="valorTotalContratos" class="java.lang.String"/>
	<field name="valorTotalProdutos" class="java.lang.String"/>
	<field name="valorTotalComissaoContratos" class="java.lang.String"/>
	<field name="valorTotalComissaoProdutos" class="java.lang.String"/>
	<variable name="contratos" class="java.lang.Integer" calculation="Sum">
		<variableExpression><![CDATA[$F{qtdContratos}]]></variableExpression>
	</variable>
	<variable name="produtos" class="java.lang.Integer" calculation="Sum">
		<variableExpression><![CDATA[$F{qtdProdutos}]]></variableExpression>
	</variable>
	<pageHeader>
		<band height="69" splitType="Stretch">
			<textField pattern="dd/MM/yyyy HH.mm.ss" isBlankWhenNull="false">
				<reportElement key="dataRel-1" mode="Transparent" x="703" y="1" width="120" height="12" backcolor="#FFFFFF"/>
				<box bottomPadding="0">
					<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Verdana" size="8" isItalic="false" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression class="java.util.Date"><![CDATA[new Date()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-2" x="83" y="1" width="620" height="19"/>
				<box>
					<pen lineColor="#FFFFFF"/>
					<topPen lineColor="#FFFFFF"/>
					<leftPen lineColor="#FFFFFF"/>
					<bottomPen lineColor="#FFFFFF"/>
					<rightPen lineColor="#FFFFFF"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Bottom">
					<font fontName="Arial" size="14" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{tituloRelatorio}]]></textFieldExpression>
			</textField>
			<image scaleImage="FillFrame" isUsingCache="true" onErrorType="Blank" evaluationTime="Page">
				<reportElement key="image-1" positionType="Float" x="0" y="1" width="82" height="67" isPrintWhenDetailOverflows="true"/>
				<box>
					<pen lineColor="#FFFFFF"/>
					<topPen lineColor="#FFFFFF"/>
					<leftPen lineColor="#FFFFFF"/>
					<bottomPen lineColor="#FFFFFF"/>
					<rightPen lineColor="#FFFFFF"/>
				</box>
				<imageExpression class="java.io.InputStream"><![CDATA[$P{logoPadraoRelatorio}]]></imageExpression>
			</image>
			<textField pattern="">
				<reportElement mode="Transparent" x="703" y="13" width="120" height="12"/>
				<box>
					<leftPen lineWidth="0.5" lineStyle="Dotted"/>
					<bottomPen lineWidth="0.5" lineStyle="Dotted"/>
					<rightPen lineWidth="0.5" lineStyle="Dotted"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Verdana" size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA["Usuário:"+ $P{usuario}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="703" y="25" width="75" height="12"/>
				<box>
					<leftPen lineWidth="0.5" lineStyle="Dotted"/>
					<bottomPen lineWidth="0.5" lineStyle="Dotted"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Verdana" size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA["Página "+$V{PAGE_NUMBER}+" de"]]></textFieldExpression>
			</textField>
			<textField evaluationTime="Report">
				<reportElement x="778" y="25" width="45" height="12"/>
				<box leftPadding="4">
					<bottomPen lineWidth="0.5" lineStyle="Dotted"/>
					<rightPen lineWidth="0.5" lineStyle="Dotted"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Verdana" size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.Integer"><![CDATA[$V{PAGE_NUMBER}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="83" y="38" width="620" height="30" isPrintWhenDetailOverflows="true"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="10"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{filtros}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-216" x="246" y="20" width="297" height="17"/>
				<box>
					<pen lineColor="#FFFFFF"/>
					<topPen lineColor="#FFFFFF"/>
					<leftPen lineColor="#FFFFFF"/>
					<bottomPen lineColor="#FFFFFF"/>
					<rightPen lineColor="#FFFFFF"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Bottom">
					<font fontName="Arial" size="10" isBold="false" isItalic="true" pdfFontName="Helvetica-BoldOblique"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{descricaoPeriodo}]]></textFieldExpression>
			</textField>
		</band>
	</pageHeader>
	<detail>
		<band height="130">
			<subreport>
				<reportElement x="0" y="41" width="823" height="88"/>
				<subreportParameter name="SUBREPORT_DIR">
					<subreportParameterExpression><![CDATA[$P{SUBREPORT_DIR}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="modoVisualizacao">
					<subreportParameterExpression><![CDATA[$P{modoVisualizacao}]]></subreportParameterExpression>
				</subreportParameter>
				<dataSourceExpression><![CDATA[$F{listaConfiguracao}]]></dataSourceExpression>
				<subreportExpression class="java.lang.String"><![CDATA[$P{SUBREPORT_DIR} + "ComissaoConsultor.jasper"]]></subreportExpression>
			</subreport>
			<rectangle>
				<reportElement x="0" y="21" width="823" height="20" forecolor="#FFFFFF" backcolor="#F0EFEF"/>
			</rectangle>
			<textField>
				<reportElement x="30" y="21" width="753" height="20"/>
				<textElement verticalAlignment="Middle"/>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{nome}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement isPrintRepeatedValues="false" x="30" y="6" width="40" height="15" isPrintInFirstWholeBand="true"/>
				<textElement>
					<font size="8"/>
				</textElement>
				<text><![CDATA[Matrícula]]></text>
			</staticText>
			<staticText>
				<reportElement isPrintRepeatedValues="false" x="70" y="6" width="140" height="15" isPrintInFirstWholeBand="true"/>
				<textElement>
					<font size="8"/>
				</textElement>
				<text><![CDATA[Nome]]></text>
			</staticText>
			<staticText>
				<reportElement isPrintRepeatedValues="false" x="220" y="6" width="40" height="15" isPrintInFirstWholeBand="true"/>
				<textElement>
					<font size="8"/>
				</textElement>
				<text><![CDATA[Recibo]]></text>
			</staticText>
			<staticText>
				<reportElement isPrintRepeatedValues="false" x="260" y="6" width="20" height="15" isPrintInFirstWholeBand="true"/>
				<textElement>
					<font size="8"/>
				</textElement>
				<text><![CDATA[Tipo]]></text>
			</staticText>
			<staticText>
				<reportElement isPrintRepeatedValues="false" x="538" y="6" width="80" height="15" isPrintInFirstWholeBand="true"/>
				<textElement textAlignment="Center">
					<font size="8"/>
				</textElement>
				<text><![CDATA[Forma Pgto.]]></text>
			</staticText>
			<staticText>
				<reportElement isPrintRepeatedValues="false" x="733" y="6" width="45" height="15" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true"/>
				<textElement textAlignment="Right">
					<font size="8"/>
				</textElement>
				<text><![CDATA[Vl. Compen.]]></text>
			</staticText>
			<staticText>
				<reportElement isPrintRepeatedValues="false" x="778" y="6" width="45" height="15" isPrintInFirstWholeBand="true"/>
				<textElement textAlignment="Right">
					<font size="8"/>
				</textElement>
				<text><![CDATA[Comissão]]></text>
			</staticText>
			<staticText>
				<reportElement isPrintRepeatedValues="false" x="280" y="6" width="100" height="15" isPrintInFirstWholeBand="true"/>
				<textElement>
					<font size="8"/>
				</textElement>
				<text><![CDATA[Descrição]]></text>
			</staticText>
			<staticText>
				<reportElement isPrintRepeatedValues="false" x="678" y="6" width="55" height="15" isPrintInFirstWholeBand="true"/>
				<textElement>
					<font size="8"/>
				</textElement>
				<text><![CDATA[Dt. Compens.]]></text>
			</staticText>
			<staticText>
				<reportElement isPrintRepeatedValues="false" x="483" y="6" width="55" height="15" isPrintInFirstWholeBand="true"/>
				<textElement>
					<font size="8"/>
				</textElement>
				<text><![CDATA[Ent. Caixa]]></text>
			</staticText>
			<staticText>
				<reportElement isPrintRepeatedValues="false" x="380" y="6" width="103" height="15" isPrintInFirstWholeBand="true"/>
				<textElement>
					<font size="8"/>
				</textElement>
				<text><![CDATA[Atendente]]></text>
			</staticText>
			<textField isStretchWithOverflow="true">
				<reportElement isPrintRepeatedValues="false" x="618" y="6" width="60" height="15" isPrintInFirstWholeBand="true"/>
				<textElement>
					<font size="8" isBold="true" isItalic="false"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA["Vl. Total **"]]></textFieldExpression>
			</textField>
		</band>
		<band height="20">
			<rectangle>
				<reportElement x="448" y="0" width="376" height="20" forecolor="#FFFFFF" backcolor="#F0EFEF"/>
			</rectangle>
			<textField>
				<reportElement x="778" y="0" width="45" height="20"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font isBold="true" isItalic="true" isUnderline="false"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{valorTotalComissaoContratos}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="733" y="0" width="45" height="20"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font isBold="true" isItalic="true" isUnderline="false"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{valorTotalContratos}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="543" y="0" width="40" height="20"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font isBold="true" isItalic="true" isUnderline="false"/>
				</textElement>
				<textFieldExpression class="java.lang.Integer"><![CDATA[$F{qtdContratos}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="471" y="0" width="72" height="20"/>
				<textElement textAlignment="Right" verticalAlignment="Middle"/>
				<text><![CDATA[Contratos:]]></text>
			</staticText>
		</band>
		<band height="20">
			<printWhenExpression><![CDATA[$P{pagarComissaoProdutos}.equals( true )]]></printWhenExpression>
			<rectangle>
				<reportElement x="448" y="0" width="376" height="20" forecolor="#FFFFFF" backcolor="#F0EFEF"/>
			</rectangle>
			<textField>
				<reportElement x="733" y="0" width="45" height="20"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font isBold="true" isItalic="true" isUnderline="false"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{valorTotalProdutos}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="778" y="0" width="45" height="20"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font isBold="true" isItalic="true" isUnderline="false"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{valorTotalComissaoProdutos}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="543" y="0" width="40" height="20"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font isBold="true" isItalic="true" isUnderline="false"/>
				</textElement>
				<textFieldExpression class="java.lang.Integer"><![CDATA[$F{qtdProdutos}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="471" y="0" width="72" height="20"/>
				<textElement textAlignment="Right" verticalAlignment="Middle"/>
				<text><![CDATA[Produtos:]]></text>
			</staticText>
		</band>
		<band height="20">
			<printWhenExpression><![CDATA[$P{pagarComissaoProdutos}.equals( true )]]></printWhenExpression>
			<rectangle>
				<reportElement x="448" y="0" width="376" height="20" forecolor="#FFFFFF" backcolor="#F0EFEF"/>
			</rectangle>
			<staticText>
				<reportElement x="471" y="0" width="72" height="20"/>
				<textElement textAlignment="Right" verticalAlignment="Middle"/>
				<text><![CDATA[Total:]]></text>
			</staticText>
			<textField>
				<reportElement x="543" y="0" width="40" height="20"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font isBold="true" isItalic="true" isUnderline="false"/>
				</textElement>
				<textFieldExpression class="java.lang.Integer"><![CDATA[$F{qtdContratos} + $F{qtdProdutos}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="733" y="0" width="45" height="20"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font isBold="true" isItalic="true" isUnderline="false"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{valorTotal}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="778" y="0" width="45" height="20"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font isBold="true" isItalic="true" isUnderline="false"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{valorTotalComissao}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<summary>
		<band height="80">
			<rectangle>
				<reportElement x="448" y="0" width="376" height="75" forecolor="#FFFFFF" backcolor="#F0EFEF"/>
			</rectangle>
			<textField>
				<reportElement x="448" y="50" width="375" height="25"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="12" isBold="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA["Total geral de comissões calculadas: " + $P{total}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="448" y="0" width="375" height="25"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="12" isBold="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA["Total de itens na comissão: " + $P{qtdContratosTotal} + ($P{pagarComissaoProdutos}? " (Contratos: " + $V{contratos}+"; Produtos: "+$V{produtos}+")" : "")]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="448" y="25" width="375" height="25"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="12" isBold="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA["Total de Geral pago: " + $P{totalGeralPago}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="0" y="0" width="70" height="16"/>
				<textElement>
					<font isBold="true"/>
				</textElement>
				<text><![CDATA[Legenda:]]></text>
			</staticText>
			<textField isStretchWithOverflow="true">
				<reportElement isPrintRepeatedValues="false" x="0" y="16" width="424" height="15" isPrintInFirstWholeBand="true"/>
				<textElement>
					<font size="8" isBold="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA["Vl. Total** :" +
($P{comissaoMatriculaRematricula}.equals(true) ?
    " Valor do contrato + Matrícula e Rematrícula." :
    " Valor do Contrato.") +
($P{pagarComissaoProdutos}.equals(true) ?
    " Caso produto, apresentará o valor total do produto." :
    " ")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement isPrintRepeatedValues="false" x="0" y="31" width="424" height="15" isPrintInFirstWholeBand="true"/>
				<textElement>
					<font size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA["Descrição: Nome do Plano." +
($P{pagarComissaoProdutos}.equals(true) ?
    " Caso produto, apresentará a descrição do MovProduto." :
    " ")]]></textFieldExpression>
			</textField>
		</band>
	</summary>
</jasperReport>

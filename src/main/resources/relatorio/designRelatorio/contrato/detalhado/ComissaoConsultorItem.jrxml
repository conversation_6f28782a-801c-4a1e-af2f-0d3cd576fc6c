<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="ComissaoConsultorItem" pageWidth="823" pageHeight="535" orientation="Landscape" whenNoDataType="AllSectionsNoDetail" columnWidth="823" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0">
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.encoding" value="UTF-8"/>
	<property name="ireport.zoom" value="2.0"/>
	<property name="ireport.x" value="730"/>
	<property name="ireport.y" value="0"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="java.util.*"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<parameter name="modoVisualizacao" class="java.lang.String"/>
	<field name="matriculaCliente" class="java.lang.String"/>
	<field name="nomePessoa" class="java.lang.String"/>
	<field name="valorDaComissao_apresentar" class="java.lang.String"/>
	<field name="codigoRecibo" class="java.lang.Integer"/>
	<field name="tipoContratoApresentar" class="java.lang.String"/>
	<field name="dataPagamento" class="java.util.Date"/>
	<field name="dataCompensacao" class="java.util.Date"/>
	<field name="formaPagamento" class="java.lang.String"/>
	<field name="valor_apresentar" class="java.lang.String"/>
	<field name="nomePlano" class="java.lang.String"/>
	<field name="valorContrato_apresentar" class="java.lang.String"/>
	<field name="responsavelLancamento_apresentar" class="java.lang.String"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band splitType="Stretch"/>
	</title>
	<pageHeader>
		<band splitType="Stretch"/>
	</pageHeader>
	<columnHeader>
		<band splitType="Stretch"/>
	</columnHeader>
	<detail>
		<band height="17" splitType="Stretch">
			<textField>
				<reportElement x="30" y="1" width="40" height="15"/>
				<textElement>
					<font size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{matriculaCliente}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="70" y="1" width="140" height="15"/>
				<textElement>
					<font size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{nomePessoa}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="778" y="1" width="45" height="15"/>
				<textElement textAlignment="Right">
					<font size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{valorDaComissao_apresentar}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="220" y="1" width="38" height="15">
					<printWhenExpression><![CDATA[$P{modoVisualizacao}.equals("AP") && (!$F{codigoRecibo}.equals(0))]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.Integer"><![CDATA[$F{codigoRecibo}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="260" y="1" width="20" height="15">
					<printWhenExpression><![CDATA[$P{modoVisualizacao}.equals("AP")]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{tipoContratoApresentar}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="483" y="1" width="55" height="15">
					<printWhenExpression><![CDATA[$P{modoVisualizacao}.equals("AP")]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font size="8"/>
				</textElement>
				<textFieldExpression class="java.util.Date"><![CDATA[$F{dataPagamento}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="678" y="1" width="55" height="15">
					<printWhenExpression><![CDATA[$P{modoVisualizacao}.equals("AP")]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font size="8"/>
				</textElement>
				<textFieldExpression class="java.util.Date"><![CDATA[$F{dataCompensacao}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="538" y="1" width="90" height="15">
					<printWhenExpression><![CDATA[$P{modoVisualizacao}.equals("AP")]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Center">
					<font size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{formaPagamento}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="733" y="1" width="45" height="15"/>
				<textElement textAlignment="Right">
					<font size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{valor_apresentar}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="280" y="1" width="100" height="15">
					<printWhenExpression><![CDATA[$P{modoVisualizacao}.equals("AP")]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{nomePlano}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="628" y="1" width="50" height="15">
					<printWhenExpression><![CDATA[$P{modoVisualizacao}.equals("AP")]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{valorContrato_apresentar}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="380" y="1" width="103" height="15">
					<printWhenExpression><![CDATA[$P{modoVisualizacao}.equals("AP")]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{responsavelLancamento_apresentar}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<columnFooter>
		<band splitType="Stretch"/>
	</columnFooter>
	<pageFooter>
		<band splitType="Stretch"/>
	</pageFooter>
	<summary>
		<band splitType="Stretch"/>
	</summary>
</jasperReport>

<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="ComissaoConsultor" pageWidth="823" pageHeight="535" orientation="Landscape" whenNoDataType="AllSectionsNoDetail" columnWidth="823" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0">
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.encoding" value="UTF-8"/>
	<property name="ireport.zoom" value="0.9330147604194674"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="java.util.*"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<parameter name="modoVisualizacao" class="java.lang.String"/>
	<parameter name="SUBREPORT_DIR" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA["D:\\PactoJ\\Desenvolvimento\\Sistemas\\ZillyonWeb\\tronco-novo\\src\\java\\relatorio\\designRelatorio\\contrato\\"]]></defaultValueExpression>
	</parameter>
	<field name="configuracao_apresentar" class="java.lang.String"/>
	<field name="listaComissoes" class="java.lang.Object"/>
	<field name="valorTotalConfiguracao" class="java.lang.String"/>
	<field name="valorComissaoConfiguracao" class="java.lang.String"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band splitType="Stretch"/>
	</title>
	<pageHeader>
		<band splitType="Stretch"/>
	</pageHeader>
	<detail>
		<band height="55" splitType="Stretch">
			<subreport>
				<reportElement x="0" y="28" width="823" height="25">
					<printWhenExpression><![CDATA[$P{modoVisualizacao}.equals( "A" ) || $P{modoVisualizacao}.equals( "AP" )]]></printWhenExpression>
				</reportElement>
				<subreportParameter name="modoVisualizacao">
					<subreportParameterExpression><![CDATA[$P{modoVisualizacao}]]></subreportParameterExpression>
				</subreportParameter>
				<dataSourceExpression><![CDATA[$F{listaComissoes}]]></dataSourceExpression>
				<subreportExpression class="java.lang.String"><![CDATA[$P{SUBREPORT_DIR} + "ComissaoConsultorItem.jasper"]]></subreportExpression>
			</subreport>
			<textField isStretchWithOverflow="true">
				<reportElement isPrintRepeatedValues="false" x="30" y="3" width="237" height="20" isPrintWhenDetailOverflows="true"/>
				<textElement verticalAlignment="Middle">
					<font isBold="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{configuracao_apresentar}]]></textFieldExpression>
			</textField>
		</band>
		<band height="22">
			<textField>
				<reportElement x="583" y="2" width="240" height="20"/>
				<textElement textAlignment="Right">
					<font isUnderline="false"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA["Comissão por configuração: " + $F{valorComissaoConfiguracao}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="312" y="2" width="260" height="20"/>
				<textElement textAlignment="Right">
					<font isUnderline="false"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA["Total pago por configuração: " + $F{valorTotalConfiguracao}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<pageFooter>
		<band splitType="Stretch"/>
	</pageFooter>
	<summary>
		<band splitType="Stretch"/>
	</summary>
</jasperReport>

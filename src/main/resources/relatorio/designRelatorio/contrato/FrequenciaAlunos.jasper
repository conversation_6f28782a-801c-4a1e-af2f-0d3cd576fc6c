¬í sr (net.sf.jasperreports.engine.JasperReport      'Ø L compileDatat Ljava/io/Serializable;L compileNameSuffixt Ljava/lang/String;L 
compilerClassq ~ xr -net.sf.jasperreports.engine.base.JRBaseReport      'Ø +I PSEUDO_SERIAL_VERSION_UIDI bottomMarginI columnCountI 
columnSpacingI columnWidthZ ignorePaginationZ isFloatColumnFooterZ isSummaryNewPageZ  isSummaryWithPageHeaderAndFooterZ isTitleNewPageI 
leftMarginB orientationI 
pageHeightI 	pageWidthB 
printOrderI rightMarginI 	topMarginB whenNoDataTypeL 
backgroundt $Lnet/sf/jasperreports/engine/JRBand;L columnFooterq ~ L columnHeaderq ~ [ datasetst ([Lnet/sf/jasperreports/engine/JRDataset;L defaultFontt *Lnet/sf/jasperreports/engine/JRReportFont;L defaultStylet %Lnet/sf/jasperreports/engine/JRStyle;L detailq ~ L 
detailSectiont 'Lnet/sf/jasperreports/engine/JRSection;[ fontst +[Lnet/sf/jasperreports/engine/JRReportFont;L formatFactoryClassq ~ L 
importsSett Ljava/util/Set;L languageq ~ L lastPageFooterq ~ L mainDatasett 'Lnet/sf/jasperreports/engine/JRDataset;L nameq ~ L noDataq ~ L orientationValuet 2Lnet/sf/jasperreports/engine/type/OrientationEnum;L 
pageFooterq ~ L 
pageHeaderq ~ L printOrderValuet 1Lnet/sf/jasperreports/engine/type/PrintOrderEnum;[ stylest &[Lnet/sf/jasperreports/engine/JRStyle;L summaryq ~ [ 	templatest /[Lnet/sf/jasperreports/engine/JRReportTemplate;L titleq ~ L whenNoDataTypeValuet 5Lnet/sf/jasperreports/engine/type/WhenNoDataTypeEnum;xp  wî            +           J  S        sr +net.sf.jasperreports.engine.base.JRBaseBand      'Ø I PSEUDO_SERIAL_VERSION_UIDI heightZ isSplitAllowedL printWhenExpressiont *Lnet/sf/jasperreports/engine/JRExpression;L 	splitTypet Ljava/lang/Byte;L splitTypeValuet 0Lnet/sf/jasperreports/engine/type/SplitTypeEnum;xr 3net.sf.jasperreports.engine.base.JRBaseElementGroup      'Ø L childrent Ljava/util/List;L elementGroupt ,Lnet/sf/jasperreports/engine/JRElementGroup;xpsr java.util.ArrayListxÒÇa I sizexp    w   
xp  wî    pp~r .net.sf.jasperreports.engine.type.SplitTypeEnum          xr java.lang.Enum          xpt STRETCHppppppsr .net.sf.jasperreports.engine.base.JRBaseSection      'Ø [ bandst %[Lnet/sf/jasperreports/engine/JRBand;xpur %[Lnet.sf.jasperreports.engine.JRBand;Ý~ìÊ5  xp   sq ~ sq ~    w   
sr 0net.sf.jasperreports.engine.base.JRBaseRectangle      'Ø L radiust Ljava/lang/Integer;xr 5net.sf.jasperreports.engine.base.JRBaseGraphicElement      'Ø I PSEUDO_SERIAL_VERSION_UIDL fillq ~ L 	fillValuet +Lnet/sf/jasperreports/engine/type/FillEnum;L linePent #Lnet/sf/jasperreports/engine/JRPen;L penq ~ xr .net.sf.jasperreports.engine.base.JRBaseElement      'Ø I PSEUDO_SERIAL_VERSION_UIDI heightZ isPrintInFirstWholeBandZ isPrintRepeatedValuesZ isPrintWhenDetailOverflowsZ isRemoveLineWhenBlankB positionTypeB stretchTypeI widthI xI yL 	backcolort Ljava/awt/Color;L defaultStyleProvidert 4Lnet/sf/jasperreports/engine/JRDefaultStyleProvider;L elementGroupq ~ L 	forecolorq ~ -L keyq ~ L modeq ~ L 	modeValuet +Lnet/sf/jasperreports/engine/type/ModeEnum;L parentStyleq ~ L parentStyleNameReferenceq ~ L positionTypeValuet 3Lnet/sf/jasperreports/engine/type/PositionTypeEnum;L printWhenExpressionq ~ L printWhenGroupChangest %Lnet/sf/jasperreports/engine/JRGroup;L 
propertiesMapt -Lnet/sf/jasperreports/engine/JRPropertiesMap;[ propertyExpressionst 3[Lnet/sf/jasperreports/engine/JRPropertyExpression;L stretchTypeValuet 2Lnet/sf/jasperreports/engine/type/StretchTypeEnum;xp  wî          +        sr java.awt.Color¥3u F falphaI valueL cst Ljava/awt/color/ColorSpace;[ 	frgbvaluet [F[ fvalueq ~ 8xp    ÿæææpppq ~ q ~ %pppppp~r 1net.sf.jasperreports.engine.type.PositionTypeEnum          xq ~ t FIX_RELATIVE_TO_TOPsr 1net.sf.jasperreports.engine.base.JRBaseExpression      'Ø I id[ chunkst 0[Lnet/sf/jasperreports/engine/JRExpressionChunk;L valueClassNameq ~ L valueClassRealNameq ~ xp   ur 0[Lnet.sf.jasperreports.engine.JRExpressionChunk;mYÏÞiK£U  xp   sr 6net.sf.jasperreports.engine.base.JRBaseExpressionChunk      'Ø B typeL textq ~ xpt 
new Boolean((sq ~ Bt COLUMN_COUNTsq ~ Bt .intValue()%2)==1)t java.lang.Booleanpppp~r 0net.sf.jasperreports.engine.type.StretchTypeEnum          xq ~ t 
NO_STRETCH  wîppsr *net.sf.jasperreports.engine.base.JRBasePen      'Ø I PSEUDO_SERIAL_VERSION_UIDL 	lineColorq ~ -L 	lineStyleq ~ L lineStyleValuet 0Lnet/sf/jasperreports/engine/type/LineStyleEnum;L 	lineWidtht Ljava/lang/Float;L penContainert ,Lnet/sf/jasperreports/engine/JRPenContainer;xp  wîpppsr java.lang.FloatÚíÉ¢Û<ðì F valuexr java.lang.Number¬à  xp    q ~ 5ppsr 0net.sf.jasperreports.engine.base.JRBaseTextField      'Ø I PSEUDO_SERIAL_VERSION_UIDI 
bookmarkLevelB evaluationTimeB hyperlinkTargetB 
hyperlinkTypeZ isStretchWithOverflowL anchorNameExpressionq ~ L evaluationGroupq ~ 1L evaluationTimeValuet 5Lnet/sf/jasperreports/engine/type/EvaluationTimeEnum;L 
expressionq ~ L hyperlinkAnchorExpressionq ~ L hyperlinkPageExpressionq ~ [ hyperlinkParameterst 3[Lnet/sf/jasperreports/engine/JRHyperlinkParameter;L hyperlinkReferenceExpressionq ~ L hyperlinkTooltipExpressionq ~ L isBlankWhenNullt Ljava/lang/Boolean;L 
linkTargetq ~ L linkTypeq ~ L patternq ~ xr 2net.sf.jasperreports.engine.base.JRBaseTextElement      'Ø %I PSEUDO_SERIAL_VERSION_UIDL borderq ~ L borderColorq ~ -L bottomBorderq ~ L bottomBorderColorq ~ -L 
bottomPaddingq ~ (L fontNameq ~ L fontSizeq ~ (L horizontalAlignmentq ~ L horizontalAlignmentValuet 6Lnet/sf/jasperreports/engine/type/HorizontalAlignEnum;L isBoldq ~ XL isItalicq ~ XL 
isPdfEmbeddedq ~ XL isStrikeThroughq ~ XL isStyledTextq ~ XL isUnderlineq ~ XL 
leftBorderq ~ L leftBorderColorq ~ -L leftPaddingq ~ (L lineBoxt 'Lnet/sf/jasperreports/engine/JRLineBox;L lineSpacingq ~ L lineSpacingValuet 2Lnet/sf/jasperreports/engine/type/LineSpacingEnum;L markupq ~ L paddingq ~ (L pdfEncodingq ~ L pdfFontNameq ~ L 
reportFontq ~ L rightBorderq ~ L rightBorderColorq ~ -L rightPaddingq ~ (L rotationq ~ L 
rotationValuet /Lnet/sf/jasperreports/engine/type/RotationEnum;L 	topBorderq ~ L topBorderColorq ~ -L 
topPaddingq ~ (L verticalAlignmentq ~ L verticalAlignmentValuet 4Lnet/sf/jasperreports/engine/type/VerticalAlignEnum;xq ~ ,  wî           F        pq ~ q ~ %ppppppq ~ ;ppppq ~ K  wîppppppppppppppppppsr .net.sf.jasperreports.engine.base.JRBaseLineBox      'Ø L 
bottomPaddingq ~ (L 	bottomPent +Lnet/sf/jasperreports/engine/base/JRBoxPen;L boxContainert ,Lnet/sf/jasperreports/engine/JRBoxContainer;L leftPaddingq ~ (L leftPenq ~ aL paddingq ~ (L penq ~ aL rightPaddingq ~ (L rightPenq ~ aL 
topPaddingq ~ (L topPenq ~ axppsr 3net.sf.jasperreports.engine.base.JRBaseBoxBottomPen      'Ø  xr -net.sf.jasperreports.engine.base.JRBaseBoxPen      'Ø L lineBoxq ~ [xq ~ M  wîppppq ~ cq ~ cq ~ _psr 1net.sf.jasperreports.engine.base.JRBaseBoxLeftPen      'Ø  xq ~ e  wîppppq ~ cq ~ cpsq ~ e  wîppppq ~ cq ~ cpsr 2net.sf.jasperreports.engine.base.JRBaseBoxRightPen      'Ø  xq ~ e  wîppppq ~ cq ~ cpsr 0net.sf.jasperreports.engine.base.JRBaseBoxTopPen      'Ø  xq ~ e  wîppppq ~ cq ~ cpppppppppppppppp~r 2net.sf.jasperreports.engine.type.VerticalAlignEnum          xq ~ t MIDDLE  wî        pp~r 3net.sf.jasperreports.engine.type.EvaluationTimeEnum          xq ~ t NOWsq ~ =   uq ~ @   sq ~ Bt clienteVO.matriculat java.lang.Stringppppppppppsq ~ U  wî           õ   F    pq ~ q ~ %ppppppq ~ ;ppppq ~ K  wîppppppppppppppppppsq ~ `psq ~ d  wîppppq ~ zq ~ zq ~ ypsq ~ g  wîppppq ~ zq ~ zpsq ~ e  wîppppq ~ zq ~ zpsq ~ j  wîppppq ~ zq ~ zpsq ~ l  wîppppq ~ zq ~ zppppppppppppppppq ~ o  wî        ppq ~ rsq ~ =   uq ~ @   sq ~ Bt clienteVO.pessoa.nomet java.lang.Stringppppppppppsq ~ U  wî           P  ;    pq ~ q ~ %ppppppq ~ ;ppppq ~ K  wîpppppppp~r 4net.sf.jasperreports.engine.type.HorizontalAlignEnum          xq ~ t RIGHTpppppppppsq ~ `psq ~ d  wîppppq ~ q ~ q ~ psq ~ g  wîppppq ~ q ~ psq ~ e  wîppppq ~ q ~ psq ~ j  wîppppq ~ q ~ psq ~ l  wîppppq ~ q ~ ppppppppppppppppq ~ o  wî        ppq ~ rsq ~ =   uq ~ @   sq ~ Bt qtdeAulasPeriodot java.lang.Integerppppppppppsq ~ U  wî           P  Û    pq ~ q ~ %ppppppq ~ ;ppppq ~ K  wîppppppppq ~ pppppppppsq ~ `psq ~ d  wîppppq ~ q ~ q ~ psq ~ g  wîppppq ~ q ~ psq ~ e  wîppppq ~ q ~ psq ~ j  wîppppq ~ q ~ psq ~ l  wîppppq ~ q ~ ppppppppppppppppq ~ o  wî        ppq ~ rsq ~ =   uq ~ @   sq ~ Bt frequenciaAlunosq ~ Bt .toString()+"%"t java.lang.Stringppppppppppsq ~ U  wî           P      pq ~ q ~ %ppppppq ~ ;ppppq ~ K  wîppppppppq ~ pppppppppsq ~ `psq ~ d  wîppppq ~ £q ~ £q ~ ¢psq ~ g  wîppppq ~ £q ~ £psq ~ e  wîppppq ~ £q ~ £psq ~ j  wîppppq ~ £q ~ £psq ~ l  wîppppq ~ £q ~ £ppppppppppppppppq ~ o  wî        ppq ~ rsq ~ =    uq ~ @   sq ~ Bt qtdePresencasPeriodot java.lang.Integerppppppppppxp  wî   ppppppt javapsr .net.sf.jasperreports.engine.base.JRBaseDataset      'Ø I PSEUDO_SERIAL_VERSION_UIDZ isMainB whenResourceMissingType[ fieldst &[Lnet/sf/jasperreports/engine/JRField;L filterExpressionq ~ [ groupst &[Lnet/sf/jasperreports/engine/JRGroup;L nameq ~ [ 
parameterst *[Lnet/sf/jasperreports/engine/JRParameter;L 
propertiesMapq ~ 2L queryt %Lnet/sf/jasperreports/engine/JRQuery;L resourceBundleq ~ L scriptletClassq ~ [ 
scriptletst *[Lnet/sf/jasperreports/engine/JRScriptlet;[ 
sortFieldst *[Lnet/sf/jasperreports/engine/JRSortField;[ 	variablest )[Lnet/sf/jasperreports/engine/JRVariable;L whenResourceMissingTypeValuet >Lnet/sf/jasperreports/engine/type/WhenResourceMissingTypeEnum;xp  wî ur &[Lnet.sf.jasperreports.engine.JRField;<ßÇN*òp  xp   sr ,net.sf.jasperreports.engine.base.JRBaseField      'Ø L descriptionq ~ L nameq ~ L 
propertiesMapq ~ 2L valueClassNameq ~ L valueClassRealNameq ~ xppt clienteVO.matriculasr +net.sf.jasperreports.engine.JRPropertiesMap      'Ø L baseq ~ 2L propertiesListq ~ L 
propertiesMapt Ljava/util/Map;xppppt java.lang.Stringpsq ~ »pt qtdeAulasPeriodosq ~ ¾pppt java.lang.Integerpsq ~ »pt qtdePresencasPeriodosq ~ ¾pppt java.lang.Integerpsq ~ »pt frequenciaAlunosq ~ ¾pppt java.lang.Doublepsq ~ »pt clienteVO.pessoa.nomesq ~ ¾pppt java.lang.Stringppur &[Lnet.sf.jasperreports.engine.JRGroup;@£_zLýxê  xp   sr ,net.sf.jasperreports.engine.base.JRBaseGroup      'Ø I PSEUDO_SERIAL_VERSION_UIDB footerPositionZ isReprintHeaderOnEachPageZ isResetPageNumberZ isStartNewColumnZ isStartNewPageZ keepTogetherI minHeightToStartNewPageL 
countVariablet (Lnet/sf/jasperreports/engine/JRVariable;L 
expressionq ~ L footerPositionValuet 5Lnet/sf/jasperreports/engine/type/FooterPositionEnum;L groupFooterq ~ L groupFooterSectionq ~ L groupHeaderq ~ L groupHeaderSectionq ~ L nameq ~ xp  wî          sr /net.sf.jasperreports.engine.base.JRBaseVariable      'Ø I PSEUDO_SERIAL_VERSION_UIDB calculationB 
incrementTypeZ isSystemDefinedB 	resetTypeL calculationValuet 2Lnet/sf/jasperreports/engine/type/CalculationEnum;L 
expressionq ~ L incrementGroupq ~ 1L incrementTypeValuet 4Lnet/sf/jasperreports/engine/type/IncrementTypeEnum;L incrementerFactoryClassNameq ~ L incrementerFactoryClassRealNameq ~ L initialValueExpressionq ~ L nameq ~ L 
resetGroupq ~ 1L resetTypeValuet 0Lnet/sf/jasperreports/engine/type/ResetTypeEnum;L valueClassNameq ~ L valueClassRealNameq ~ xp  wî   ~r 0net.sf.jasperreports.engine.type.CalculationEnum          xq ~ t COUNTsq ~ =   uq ~ @   sq ~ Bt new java.lang.Integer(1)t java.lang.Integerpp~r 2net.sf.jasperreports.engine.type.IncrementTypeEnum          xq ~ t NONEppsq ~ =   	uq ~ @   sq ~ Bt new java.lang.Integer(0)q ~ äpt alunos_COUNTq ~ ×~r .net.sf.jasperreports.engine.type.ResetTypeEnum          xq ~ t GROUPq ~ äpp~r 3net.sf.jasperreports.engine.type.FooterPositionEnum          xq ~ t NORMALpsq ~  uq ~ #   sq ~ sq ~    w   
sq ~ '  wî          *      q ~ 9q ~ q ~ õppppppq ~ ;ppppq ~ K  wîppsq ~ M  wîpppq ~ Tq ~ ÷ppsq ~ U  wî           P  ;    pq ~ q ~ õppppppq ~ ;ppppq ~ K  wîppppppppq ~ sr java.lang.BooleanÍ rÕúî Z valuexpppppsq ~ ú pppsq ~ `psq ~ d  wîppppq ~ ýq ~ ýq ~ ùpsq ~ g  wîppppq ~ ýq ~ ýpsq ~ e  wîppppq ~ ýq ~ ýpsq ~ j  wîppppq ~ ýq ~ ýpsq ~ l  wîppppq ~ ýq ~ ýppppppppppppppppq ~ o  wî        ppq ~ rsq ~ =   
uq ~ @   sq ~ Bt 	sum_aulast java.lang.Integerppppppppppsq ~ U  wî           P      pq ~ q ~ õppppppq ~ ;ppppq ~ K  wîppppppppq ~ q ~ ûppppppppsq ~ `psq ~ d  wîppppq ~	q ~	q ~psq ~ g  wîppppq ~	q ~	psq ~ e  wîppppq ~	q ~	psq ~ j  wîppppq ~	q ~	psq ~ l  wîppppq ~	q ~	ppppppppppppppppq ~ o  wî        ppq ~ rsq ~ =   uq ~ @   sq ~ Bt 
sum_presencast java.lang.Integerppppppppppsq ~ U  wî           P  Û    pq ~ q ~ õppppppq ~ ;ppppq ~ K  wîppppppppq ~ q ~ ûppppppppsq ~ `psq ~ d  wîppppq ~q ~q ~psq ~ g  wîppppq ~q ~psq ~ e  wîppppq ~q ~psq ~ j  wîppppq ~q ~psq ~ l  wîppppq ~q ~ppppppppppppppppq ~ o  wî        ppq ~ rsq ~ =   uq ~ @   sq ~ Bt avg_freqsq ~ Bt .toString()+"%"t java.lang.Stringppppppppppsr 1net.sf.jasperreports.engine.base.JRBaseStaticText      'Ø L textq ~ xq ~ Y  wî           F        pq ~ q ~ õppppppq ~ ;ppppq ~ K  wîppppppsr java.lang.Integerâ ¤÷8 I valuexq ~ S   ppq ~ ûppppppppsq ~ `psq ~ d  wîppppq ~&q ~&q ~#psq ~ g  wîppppq ~&q ~&psq ~ e  wîppppq ~&q ~&psq ~ j  wîppppq ~&q ~&psq ~ l  wîppppq ~&q ~&ppppppppppppppppq ~ ot Totaisxp  wî   ppppsq ~  pt alunost FrequenciaAlunosur *[Lnet.sf.jasperreports.engine.JRParameter;" *Ã`!  xp   sr 0net.sf.jasperreports.engine.base.JRBaseParameter      'Ø 	Z isForPromptingZ isSystemDefinedL defaultValueExpressionq ~ L descriptionq ~ L nameq ~ L nestedTypeNameq ~ L 
propertiesMapq ~ 2L valueClassNameq ~ L valueClassRealNameq ~ xpppt REPORT_PARAMETERS_MAPpsq ~ ¾pppt 
java.util.Mappsq ~2ppt 
JASPER_REPORTpsq ~ ¾pppt (net.sf.jasperreports.engine.JasperReportpsq ~2ppt REPORT_CONNECTIONpsq ~ ¾pppt java.sql.Connectionpsq ~2ppt REPORT_MAX_COUNTpsq ~ ¾pppq ~ äpsq ~2ppt REPORT_DATA_SOURCEpsq ~ ¾pppt (net.sf.jasperreports.engine.JRDataSourcepsq ~2ppt REPORT_SCRIPTLETpsq ~ ¾pppt /net.sf.jasperreports.engine.JRAbstractScriptletpsq ~2ppt 
REPORT_LOCALEpsq ~ ¾pppt java.util.Localepsq ~2ppt REPORT_RESOURCE_BUNDLEpsq ~ ¾pppt java.util.ResourceBundlepsq ~2ppt REPORT_TIME_ZONEpsq ~ ¾pppt java.util.TimeZonepsq ~2ppt REPORT_FORMAT_FACTORYpsq ~ ¾pppt .net.sf.jasperreports.engine.util.FormatFactorypsq ~2ppt REPORT_CLASS_LOADERpsq ~ ¾pppt java.lang.ClassLoaderpsq ~2ppt REPORT_URL_HANDLER_FACTORYpsq ~ ¾pppt  java.net.URLStreamHandlerFactorypsq ~2ppt REPORT_FILE_RESOLVERpsq ~ ¾pppt -net.sf.jasperreports.engine.util.FileResolverpsq ~2ppt REPORT_TEMPLATESpsq ~ ¾pppt java.util.Collectionpsq ~2ppt REPORT_VIRTUALIZERpsq ~ ¾pppt )net.sf.jasperreports.engine.JRVirtualizerpsq ~2ppt IS_IGNORE_PAGINATIONpsq ~ ¾pppq ~ Ipsq ~2  ppt nomeEmpresapsq ~ ¾pppt java.lang.Stringpsq ~2  ppt logoPadraoRelatoriopsq ~ ¾pppt java.io.InputStreampsq ~2  ppt dataInipsq ~ ¾pppt java.lang.Stringpsq ~2  ppt dataFimpsq ~ ¾pppt java.lang.Stringpsq ~2 ppt enderecoEmpresapsq ~ ¾pppt java.lang.Stringpsq ~2 ppt 
cidadeEmpresapsq ~ ¾pppt java.lang.Stringpsq ~2 ppt turmapsq ~ ¾pppt java.lang.Stringpsq ~2 ppt 	professorpsq ~ ¾pppt java.lang.Stringpsq ~2 ppt ambientepsq ~ ¾pppt java.lang.Stringpsq ~2 ppt 	diaSemanapsq ~ ¾pppt java.lang.Stringpsq ~2 ppt horariopsq ~ ¾pppt java.lang.Stringpsq ~2 ppt 
SUBREPORT_DIRpsq ~ ¾pppt java.lang.Stringpsq ~2 ppt dadosReposicoespsq ~ ¾pppt java.lang.Objectpsq ~ ¾psq ~    w   
t ireport.zoomt 	ireport.xt 	ireport.yxsr java.util.HashMapÚÁÃ`Ñ F 
loadFactorI 	thresholdxp?@     w      q ~§t 1.0q ~¨t 0q ~©t 0xpppppur )[Lnet.sf.jasperreports.engine.JRVariable;bæ|,·D  xp   	sq ~ Ø  wî   ~q ~ Ýt SYSTEMppq ~ æppsq ~ =    uq ~ @   sq ~ Bt new java.lang.Integer(1)q ~ äpt PAGE_NUMBERp~q ~ ít REPORTq ~ äpsq ~ Ø  wî   q ~²ppq ~ æppsq ~ =   uq ~ @   sq ~ Bt new java.lang.Integer(1)q ~ äpt 
COLUMN_NUMBERp~q ~ ít PAGEq ~ äpsq ~ Ø  wî   q ~ Þsq ~ =   uq ~ @   sq ~ Bt new java.lang.Integer(1)q ~ äppq ~ æppsq ~ =   uq ~ @   sq ~ Bt new java.lang.Integer(0)q ~ äpt REPORT_COUNTpq ~¹q ~ äpsq ~ Ø  wî   q ~ Þsq ~ =   uq ~ @   sq ~ Bt new java.lang.Integer(1)q ~ äppq ~ æppsq ~ =   uq ~ @   sq ~ Bt new java.lang.Integer(0)q ~ äpt 
PAGE_COUNTpq ~Áq ~ äpsq ~ Ø  wî   q ~ Þsq ~ =   uq ~ @   sq ~ Bt new java.lang.Integer(1)q ~ äppq ~ æppsq ~ =   uq ~ @   sq ~ Bt new java.lang.Integer(0)q ~ äpt COLUMN_COUNTp~q ~ ít COLUMNq ~ äpq ~ Üsq ~ Ø  wî    ~q ~ Ýt SUMsq ~ =   
uq ~ @   sq ~ Bt qtdeAulasPeriodot java.lang.Integerppq ~ æpppt 	sum_aulaspq ~¹q ~êpsq ~ Ø  wî    q ~äsq ~ =   uq ~ @   sq ~ Bt qtdePresencasPeriodot java.lang.Integerppq ~ æpppt 
sum_presencaspq ~¹q ~ñpsq ~ Ø  wî    ~q ~ Ýt AVERAGEsq ~ =   uq ~ @   sq ~ Bt frequenciaAlunot java.lang.Doubleppq ~ æpppt avg_freqpq ~¹q ~úp~r <net.sf.jasperreports.engine.type.WhenResourceMissingTypeEnum          xq ~ t NULLq ~/p~r 0net.sf.jasperreports.engine.type.OrientationEnum          xq ~ t PORTRAITsq ~ sq ~    w   
sr +net.sf.jasperreports.engine.base.JRBaseLine      'Ø I PSEUDO_SERIAL_VERSION_UIDB 	directionL directionValuet 4Lnet/sf/jasperreports/engine/type/LineDirectionEnum;xq ~ )  wî          +        pq ~ q ~pt line-2p~r )net.sf.jasperreports.engine.type.ModeEnum          xq ~ t TRANSPARENTppq ~ ;ppppq ~ K  wîp~r )net.sf.jasperreports.engine.type.FillEnum          xq ~ t SOLIDsq ~ M  wîppppq ~p  wî ~r 2net.sf.jasperreports.engine.type.LineDirectionEnum          xq ~ t TOP_DOWNsq ~ U  wî           Ì      sq ~ 6    ÿÿÿÿpppq ~ q ~pt 	dataRel-1pq ~	ppq ~ ;ppppq ~ K  wîpppppt Verdanasq ~$   
p~q ~ t LEFTq ~ ûq ~ üpppppppsq ~ `sq ~$   sq ~ d  wîsq ~ 6    ÿfffpppp~r .net.sf.jasperreports.engine.type.LineStyleEnum          xq ~ t SOLIDsq ~ R    q ~q ~q ~psq ~ g  wîsq ~ 6    ÿfffppppq ~sq ~ R    q ~q ~psq ~ e  wîppppq ~q ~psq ~ j  wîsq ~ 6    ÿfffppppq ~sq ~ R    q ~q ~psq ~ l  wîsq ~ 6    ÿfffppppq ~sq ~ R    q ~q ~pppppt Helvetica-Boldppppppppppq ~ o  wî        ppq ~ rsq ~ =   !uq ~ @   sq ~ Bt 
new Date()t java.util.Dateppppppq ~ üppt EEE, d MMM yyyy HH:mm:ss Zsq ~ U  wî           -  þ   pq ~ q ~pt 
textField-242ppppq ~ ;ppppq ~ K  wîpppppt Arialq ~ppq ~ ûppppppppsq ~ `q ~sq ~ d  wîsq ~ 6    ÿfffppppq ~sq ~ R    q ~5q ~5q ~2psq ~ g  wîsq ~ 6    ÿfffppppq ~sq ~ R    q ~5q ~5psq ~ e  wîppppq ~5q ~5psq ~ j  wîsq ~ 6    ÿfffppppq ~sq ~ R    q ~5q ~5psq ~ l  wîsq ~ 6    ÿ   ppppq ~sq ~ R    q ~5q ~5pppppt Helvetica-Boldppppppppppp  wî        pp~q ~ qt REPORTsq ~ =   "uq ~ @   sq ~ Bt " " + sq ~ Bt PAGE_NUMBERsq ~ Bt  + ""t java.lang.Stringppppppq ~ üpppsq ~ U  wî           S  «   pq ~ q ~pt 
textField-243ppppq ~ ;ppppq ~ K  wîpppppt Arialq ~pq ~ q ~ ûppppppppsq ~ `q ~sq ~ d  wîsq ~ 6    ÿfffppppq ~sq ~ R    q ~Rq ~Rq ~Opsq ~ g  wîsq ~ 6    ÿfffppppq ~sq ~ R    q ~Rq ~Rpsq ~ e  wîppppq ~Rq ~Rpsq ~ j  wîsq ~ 6    ÿ   ppppq ~sq ~ R    q ~Rq ~Rpsq ~ l  wîsq ~ 6    ÿ   ppppq ~sq ~ R    q ~Rq ~Rpppppt Helvetica-Boldppppppppppp  wî        ppq ~ rsq ~ =   #uq ~ @   sq ~ Bt "PÃ¡gina: " + sq ~ Bt PAGE_NUMBERsq ~ Bt 	 + " de "t java.lang.Stringppppppq ~ üpppxp  wî   pppsq ~ sq ~    w   &sq ~"  wî           Ç  d   %pq ~ q ~jpt staticText-100ppppq ~ ;ppppq ~ K  wîppppppsq ~$   pq ~ q ~ ûppppppppsq ~ `psq ~ d  wîppppq ~oq ~oq ~lpsq ~ g  wîppppq ~oq ~opsq ~ e  wîppppq ~oq ~opsq ~ j  wîppppq ~oq ~opsq ~ l  wîppppq ~oq ~opppppt Helvetica-Boldpppppppppppt FrequÃªncia de Alunossq ~"  wî           Ç  d    pq ~ q ~jpt 
staticText-91p~q ~t OPAQUEppq ~ ;ppppq ~ K  wîpppppt Microsoft Sans Serifsq ~$   	pq ~ q ~ ûq ~ ûpq ~ üpq ~ üpppsq ~ `psq ~ d  wîsq ~ 6    ÿfffppppq ~sq ~ R    q ~}q ~}q ~wpsq ~ g  wîsq ~ 6    ÿfffppppq ~sq ~ R    q ~}q ~}psq ~ e  wîppppq ~}q ~}psq ~ j  wîsq ~ 6    ÿfffppppq ~sq ~ R    q ~}q ~}psq ~ l  wîsq ~ 6    ÿfffppppq ~sq ~ R    q ~}q ~}p~r 0net.sf.jasperreports.engine.type.LineSpacingEnum          xq ~ t SINGLEpppt Helvetica-BoldObliquepppppppppp~q ~ nt TOPt ?Zillyon - Desenvolvido por PACTO
SoluÃ§Ãµes TecnolÃ³gicas Ltda.sq ~ U  wî           <   ä   *pq ~ q ~jpt 
textField-246ppppq ~ ;ppppq ~ K  wîpppppppppq ~ ûppppppppsq ~ `psq ~ d  wîppppq ~q ~q ~psq ~ g  wîppppq ~q ~psq ~ e  wîppppq ~q ~psq ~ j  wîppppq ~q ~psq ~ l  wîppppq ~q ~pppppt Helvetica-Boldppppppppppp  wî        ppq ~ rsq ~ =   uq ~ @   sq ~ Bt dataFimt java.lang.Stringppppppq ~ üpppsq ~ U  wî           <      *pq ~ q ~jpt 
textField-245ppppq ~ ;ppppq ~ K  wîpppppppppq ~ ûppppppppsq ~ `psq ~ d  wîppppq ~¢q ~¢q ~ psq ~ g  wîppppq ~¢q ~¢psq ~ e  wîppppq ~¢q ~¢psq ~ j  wîppppq ~¢q ~¢psq ~ l  wîppppq ~¢q ~¢pppppt Helvetica-Boldppppppppppp  wî        ppq ~ rsq ~ =   uq ~ @   sq ~ Bt dataInit java.lang.Stringppppppq ~ üpppsq ~"  wî              Ì   *pq ~ q ~jpt staticText-103pq ~yppq ~ ;ppppq ~ K  wîpppppt Microsoft Sans Serifq ~%pq ~q ~ ûq ~ üpq ~ üpq ~ üpppsq ~ `psq ~ d  wîsq ~ 6    ÿfffppppq ~sq ~ R    q ~±q ~±q ~®psq ~ g  wîsq ~ 6    ÿfffppppq ~sq ~ R    q ~±q ~±psq ~ e  wîppppq ~±q ~±psq ~ j  wîsq ~ 6    ÿfffppppq ~sq ~ R    q ~±q ~±psq ~ l  wîsq ~ 6    ÿfffppppq ~sq ~ R    q ~±q ~±pppppt Helvetica-Boldppppppppppq ~ ot atÃ©sq ~ U  wî           Ô      pq ~ q ~jpt 
textField-249ppppq ~ ;ppppq ~ K  wîpppppppppq ~ ûppppppppsq ~ `psq ~ d  wîppppq ~Ãq ~Ãq ~Ápsq ~ g  wîppppq ~Ãq ~Ãpsq ~ e  wîppppq ~Ãq ~Ãpsq ~ j  wîppppq ~Ãq ~Ãpsq ~ l  wîppppq ~Ãq ~Ãpppppt Helvetica-Boldppppppppppp  wî        ppq ~ rsq ~ =   uq ~ @   sq ~ Bt 
cidadeEmpresat java.lang.Stringppppppq ~ üpppsq ~"  wî           :   V   *pq ~ q ~jpt staticText-102pq ~yppq ~ ;ppppq ~ K  wîpppppt Microsoft Sans Serifq ~pq ~q ~ ûq ~ üpq ~ üpq ~ üpppsq ~ `psq ~ d  wîsq ~ 6    ÿfffppppq ~sq ~ R    q ~Òq ~Òq ~Ïpsq ~ g  wîsq ~ 6    ÿfffppppq ~sq ~ R    q ~Òq ~Òpsq ~ e  wîppppq ~Òq ~Òpsq ~ j  wîsq ~ 6    ÿfffppppq ~sq ~ R    q ~Òq ~Òpsq ~ l  wîsq ~ 6    ÿfffppppq ~sq ~ R    q ~Òq ~Òpppppt Helvetica-Boldppppppppppq ~ ot 	PerÃ­odo:sr ,net.sf.jasperreports.engine.base.JRBaseImage      'Ø *I PSEUDO_SERIAL_VERSION_UIDI 
bookmarkLevelB evaluationTimeB hyperlinkTargetB 
hyperlinkTypeZ isLazyB onErrorTypeL anchorNameExpressionq ~ L borderq ~ L borderColorq ~ -L bottomBorderq ~ L bottomBorderColorq ~ -L 
bottomPaddingq ~ (L evaluationGroupq ~ 1L evaluationTimeValueq ~ VL 
expressionq ~ L horizontalAlignmentq ~ L horizontalAlignmentValueq ~ ZL hyperlinkAnchorExpressionq ~ L hyperlinkPageExpressionq ~ [ hyperlinkParametersq ~ WL hyperlinkReferenceExpressionq ~ L hyperlinkTooltipExpressionq ~ L isUsingCacheq ~ XL 
leftBorderq ~ L leftBorderColorq ~ -L leftPaddingq ~ (L lineBoxq ~ [L 
linkTargetq ~ L linkTypeq ~ L onErrorTypeValuet 2Lnet/sf/jasperreports/engine/type/OnErrorTypeEnum;L paddingq ~ (L rightBorderq ~ L rightBorderColorq ~ -L rightPaddingq ~ (L 
scaleImageq ~ L scaleImageValuet 1Lnet/sf/jasperreports/engine/type/ScaleImageEnum;L 	topBorderq ~ L topBorderColorq ~ -L 
topPaddingq ~ (L verticalAlignmentq ~ L verticalAlignmentValueq ~ ^xq ~ )  wî   A       K        pq ~ q ~jpt image-1ppppq ~ ;ppppq ~ K  wîppsq ~ M  wîppppq ~åp  wî         pppppppq ~ rsq ~ =   uq ~ @   sq ~ Bt logoPadraoRelatoriot java.io.InputStreamppppppppq ~ ûpppsq ~ `psq ~ d  wîsq ~ 6    ÿfffppppq ~sq ~ R    q ~íq ~íq ~åpsq ~ g  wîsq ~ 6    ÿfffppppq ~sq ~ R    q ~íq ~ípsq ~ e  wîppppq ~íq ~ípsq ~ j  wîsq ~ 6    ÿfffppppq ~sq ~ R    q ~íq ~ípsq ~ l  wîsq ~ 6    ÿfffppppq ~sq ~ R    q ~íq ~ípp~r 0net.sf.jasperreports.engine.type.OnErrorTypeEnum          xq ~ t BLANKpppppppppppsq ~ U  wî           Ô      pq ~ q ~jpt 
textField-248ppppq ~ ;ppppq ~ K  wîpppppppppq ~ ûppppppppsq ~ `psq ~ d  wîppppq ~ q ~ q ~þpsq ~ g  wîppppq ~ q ~ psq ~ e  wîppppq ~ q ~ psq ~ j  wîppppq ~ q ~ psq ~ l  wîppppq ~ q ~ pppppt Helvetica-Boldppppppppppp  wî        ppq ~ rsq ~ =   uq ~ @   sq ~ Bt enderecoEmpresat java.lang.Stringppppppq ~ üpppsq ~"  wî           o  ¼   pq ~ q ~jpt 
staticText-92pq ~yppq ~ ;ppppq ~ K  wîpppppt Microsoft Sans Serifq ~|pq ~ q ~ ûq ~ ûpq ~ üpq ~ üpppsq ~ `psq ~ d  wîsq ~ 6    ÿfffppppq ~sq ~ R    q ~q ~q ~psq ~ g  wîsq ~ 6    ÿfffppppq ~sq ~ R    q ~q ~psq ~ e  wîppppq ~q ~psq ~ j  wîsq ~ 6    ÿfffppppq ~sq ~ R    q ~q ~psq ~ l  wîsq ~ 6    ÿfffppppq ~sq ~ R    q ~q ~pq ~pppt Helvetica-BoldObliqueppppppppppq ~t (0xx62) 3251-5820sq ~ U  wî           Ô       pq ~ q ~jpt 
textField-247ppppq ~ ;ppppq ~ K  wîpppppppppq ~ ûppppppppsq ~ `psq ~ d  wîppppq ~!q ~!q ~psq ~ g  wîppppq ~!q ~!psq ~ e  wîppppq ~!q ~!psq ~ j  wîppppq ~!q ~!psq ~ l  wîppppq ~!q ~!pppppt Helvetica-Boldppppppppppp  wî        ppq ~ rsq ~ =   uq ~ @   sq ~ Bt nomeEmpresat java.lang.Stringppppppq ~ üpppsq ~  wî          +       pq ~ q ~jpt line-1pq ~	ppq ~ ;ppppq ~ K  wîpq ~sq ~ M  wîppq ~sq ~ R?   q ~-p  wî q ~sq ~"  wî           F       pq ~ q ~jppppppq ~ ;ppppq ~ K  wîppppppq ~%ppq ~ ûppppppppsq ~ `psq ~ d  wîppppq ~2q ~2q ~1psq ~ g  wîppppq ~2q ~2psq ~ e  wîppppq ~2q ~2psq ~ j  wîppppq ~2q ~2psq ~ l  wîppppq ~2q ~2ppppppppppppppppq ~ ot 
MatrÃ­culasq ~"  wî           õ   F   pq ~ q ~jppppppq ~ ;ppppq ~ K  wîppppppq ~%ppq ~ ûppppppppsq ~ `psq ~ d  wîppppq ~:q ~:q ~9psq ~ g  wîppppq ~:q ~:psq ~ e  wîppppq ~:q ~:psq ~ j  wîppppq ~:q ~:psq ~ l  wîppppq ~:q ~:ppppppppppppppppq ~ ot Nomesq ~"  wî           P  ;   pq ~ q ~jppppppq ~ ;ppppq ~ K  wîppppppq ~%pq ~ q ~ ûppppppppsq ~ `psq ~ d  wîppppq ~Bq ~Bq ~Apsq ~ g  wîppppq ~Bq ~Bpsq ~ e  wîppppq ~Bq ~Bpsq ~ j  wîppppq ~Bq ~Bpsq ~ l  wîppppq ~Bq ~Bppppppppppppppppq ~ ot 
Qtde.Aulassq ~"  wî           P     pq ~ q ~jppppppq ~ ;ppppq ~ K  wîppppppq ~%pq ~ q ~ ûppppppppsq ~ `psq ~ d  wîppppq ~Jq ~Jq ~Ipsq ~ g  wîppppq ~Jq ~Jpsq ~ e  wîppppq ~Jq ~Jpsq ~ j  wîppppq ~Jq ~Jpsq ~ l  wîppppq ~Jq ~Jppppppppppppppppq ~ ot 
PresenÃ§assq ~"  wî           P  Û   pq ~ q ~jppppppq ~ ;ppppq ~ K  wîppppppq ~%pq ~ q ~ ûppppppppsq ~ `psq ~ d  wîppppq ~Rq ~Rq ~Qpsq ~ g  wîppppq ~Rq ~Rpsq ~ e  wîppppq ~Rq ~Rpsq ~ j  wîppppq ~Rq ~Rpsq ~ l  wîppppq ~Rq ~Rppppppppppppppppq ~ ot FrequÃªnciasq ~  wî          +       pq ~ q ~jppppppq ~ ;ppppq ~ K  wîppsq ~ M  wîpppsq ~ R?   q ~Yp  wî q ~sq ~"  wî           :   V   pq ~ q ~jpt staticText-102pq ~yppq ~ ;ppppq ~ K  wîpppppt Microsoft Sans Serifq ~pq ~q ~ ûq ~ üpq ~ üpq ~ üpppsq ~ `psq ~ d  wîsq ~ 6    ÿfffppppq ~sq ~ R    q ~_q ~_q ~\psq ~ g  wîsq ~ 6    ÿfffppppq ~sq ~ R    q ~_q ~_psq ~ e  wîppppq ~_q ~_psq ~ j  wîsq ~ 6    ÿfffppppq ~sq ~ R    q ~_q ~_psq ~ l  wîsq ~ 6    ÿfffppppq ~sq ~ R    q ~_q ~_pppppt Helvetica-Boldppppppppppq ~ ot Cidade:sq ~"  wî           :   V   pq ~ q ~jpt staticText-102pq ~yppq ~ ;ppppq ~ K  wîpppppt Microsoft Sans Serifq ~pq ~q ~ ûq ~ üpq ~ üpq ~ üpppsq ~ `psq ~ d  wîsq ~ 6    ÿfffppppq ~sq ~ R    q ~rq ~rq ~opsq ~ g  wîsq ~ 6    ÿfffppppq ~sq ~ R    q ~rq ~rpsq ~ e  wîppppq ~rq ~rpsq ~ j  wîsq ~ 6    ÿfffppppq ~sq ~ R    q ~rq ~rpsq ~ l  wîsq ~ 6    ÿfffppppq ~sq ~ R    q ~rq ~rpppppt Helvetica-Boldppppppppppq ~ ot 
EndereÃ§o:sq ~"  wî           :   V    pq ~ q ~jpt staticText-102pq ~yppq ~ ;ppppq ~ K  wîpppppt Microsoft Sans Serifq ~pq ~q ~ ûq ~ üpq ~ üpq ~ üpppsq ~ `psq ~ d  wîsq ~ 6    ÿfffppppq ~sq ~ R    q ~q ~q ~psq ~ g  wîsq ~ 6    ÿfffppppq ~sq ~ R    q ~q ~psq ~ e  wîppppq ~q ~psq ~ j  wîsq ~ 6    ÿfffppppq ~sq ~ R    q ~q ~psq ~ l  wîsq ~ 6    ÿfffppppq ~sq ~ R    q ~q ~pppppt Helvetica-Boldppppppppppq ~ ot Empresa:sq ~"  wî           <       Kpq ~ q ~jppppppq ~ ;ppppq ~ K  wîpppppt Microsoft Sans Serifpppq ~ ûppppppppsq ~ `psq ~ d  wîppppq ~q ~q ~psq ~ g  wîppppq ~q ~psq ~ e  wîppppq ~q ~psq ~ j  wîppppq ~q ~psq ~ l  wîppppq ~q ~ppppppppppppppppq ~ ot Turma:sq ~"  wî           <       \pq ~ q ~jppppppq ~ ;ppppq ~ K  wîpppppt Microsoft Sans Serifpppq ~ ûppppppppsq ~ `psq ~ d  wîppppq ~ q ~ q ~psq ~ g  wîppppq ~ q ~ psq ~ e  wîppppq ~ q ~ psq ~ j  wîppppq ~ q ~ psq ~ l  wîppppq ~ q ~ ppppppppppppppppq ~ ot 
Professor:sq ~"  wî           F  d   Kpq ~ q ~jppppppq ~ ;ppppq ~ K  wîpppppt Microsoft Sans Serifpppq ~ ûppppppppsq ~ `psq ~ d  wîppppq ~©q ~©q ~§psq ~ g  wîppppq ~©q ~©psq ~ e  wîppppq ~©q ~©psq ~ j  wîppppq ~©q ~©psq ~ l  wîppppq ~©q ~©ppppppppppppppppq ~ ot Dia Semana:sq ~"  wî           <       mpq ~ q ~jppppppq ~ ;ppppq ~ K  wîpppppt Microsoft Sans Serifpppq ~ ûppppppppsq ~ `psq ~ d  wîppppq ~²q ~²q ~°psq ~ g  wîppppq ~²q ~²psq ~ e  wîppppq ~²q ~²psq ~ j  wîppppq ~²q ~²psq ~ l  wîppppq ~²q ~²ppppppppppppppppq ~ ot 	Ambiente:sq ~"  wî           -  d   \pq ~ q ~jppppppq ~ ;ppppq ~ K  wîpppppt Microsoft Sans Serifpppq ~ ûppppppppsq ~ `psq ~ d  wîppppq ~»q ~»q ~¹psq ~ g  wîppppq ~»q ~»psq ~ e  wîppppq ~»q ~»psq ~ j  wîppppq ~»q ~»psq ~ l  wîppppq ~»q ~»ppppppppppppppppq ~ ot 	HorÃ¡rio:sq ~ U  wî           È   <   Kpq ~ q ~jppppppq ~ ;ppppq ~ K  wîppppppppppppppppppsq ~ `psq ~ d  wîppppq ~Ãq ~Ãq ~Âpsq ~ g  wîppppq ~Ãq ~Ãpsq ~ e  wîppppq ~Ãq ~Ãpsq ~ j  wîppppq ~Ãq ~Ãpsq ~ l  wîppppq ~Ãq ~Ãppppppppppppppppq ~ o  wî        ppq ~ rsq ~ =   uq ~ @   sq ~ Bt turmat java.lang.Stringppppppppppsq ~ U  wî           È   <   \pq ~ q ~jppppppq ~ ;ppppq ~ K  wîppppppppppppppppppsq ~ `psq ~ d  wîppppq ~Ïq ~Ïq ~Îpsq ~ g  wîppppq ~Ïq ~Ïpsq ~ e  wîppppq ~Ïq ~Ïpsq ~ j  wîppppq ~Ïq ~Ïpsq ~ l  wîppppq ~Ïq ~Ïppppppppppppppppq ~ o  wî        ppq ~ rsq ~ =   uq ~ @   sq ~ Bt 	professort java.lang.Stringppppppppppsq ~ U  wî           È   <   mpq ~ q ~jppppppq ~ ;ppppq ~ K  wîppppppppppppppppppsq ~ `psq ~ d  wîppppq ~Ûq ~Ûq ~Úpsq ~ g  wîppppq ~Ûq ~Ûpsq ~ e  wîppppq ~Ûq ~Ûpsq ~ j  wîppppq ~Ûq ~Ûpsq ~ l  wîppppq ~Ûq ~Ûppppppppppppppppq ~ o  wî        ppq ~ rsq ~ =   uq ~ @   sq ~ Bt ambientet java.lang.Stringppppppppppsq ~ U  wî           x  ª   Kpq ~ q ~jppppppq ~ ;ppppq ~ K  wîppppppppppppppppppsq ~ `psq ~ d  wîppppq ~çq ~çq ~æpsq ~ g  wîppppq ~çq ~çpsq ~ e  wîppppq ~çq ~çpsq ~ j  wîppppq ~çq ~çpsq ~ l  wîppppq ~çq ~çppppppppppppppppq ~ o  wî        ppq ~ rsq ~ =   uq ~ @   sq ~ Bt 	diaSemanat java.lang.Stringppppppppppsq ~ U  wî           x     \pq ~ q ~jppppppq ~ ;ppppq ~ K  wîppppppppppppppppppsq ~ `psq ~ d  wîppppq ~óq ~óq ~òpsq ~ g  wîppppq ~óq ~ópsq ~ e  wîppppq ~óq ~ópsq ~ j  wîppppq ~óq ~ópsq ~ l  wîppppq ~óq ~óppppppppppppppppq ~ o  wî        ppq ~ rsq ~ =   uq ~ @   sq ~ Bt horariot java.lang.Stringppppppppppxp  wî   ppp~r /net.sf.jasperreports.engine.type.PrintOrderEnum          xq ~ t VERTICALpsq ~ sq ~    w   
sq ~  wî          +       pq ~ q ~pq ~.pq ~	ppq ~ ;ppppq ~ K  wîpq ~sq ~ M  wîppq ~q ~0q ~p  wî q ~sr 0net.sf.jasperreports.engine.base.JRBaseSubreport      'Ø L connectionExpressionq ~ L dataSourceExpressionq ~ L 
expressionq ~ L isUsingCacheq ~ X[ 
parameterst 3[Lnet/sf/jasperreports/engine/JRSubreportParameter;L parametersMapExpressionq ~ [ returnValuest 5[Lnet/sf/jasperreports/engine/JRSubreportReturnValue;L runToBottomq ~ Xxq ~ ,  wî          +      pq ~ q ~pt 
subreport1ppppq ~ ;ppppq ~ Kpsq ~ =   $uq ~ @   sq ~ Bt dadosReposicoesq ~Epsq ~ =   %uq ~ @   sq ~ Bt 
SUBREPORT_DIRsq ~ Bt * + "FrequenciaAlunos_subReposicoes.jasper"t java.lang.Stringpq ~ üppppxp  wî   2ppppp~r 3net.sf.jasperreports.engine.type.WhenNoDataTypeEnum          xq ~ t NO_PAGESsr 6net.sf.jasperreports.engine.design.JRReportCompileData      'Ø L crosstabCompileDataq ~ ¿L datasetCompileDataq ~ ¿L mainDatasetCompileDataq ~ xpsq ~ª?@     w       xsq ~ª?@     w       xur [B¬óøTà  xp  $¿Êþº¾   .J %FrequenciaAlunos_1365434326365_683480  ,net/sf/jasperreports/engine/fill/JREvaluator  parameter_enderecoEmpresa 2Lnet/sf/jasperreports/engine/fill/JRFillParameter; parameter_JASPER_REPORT parameter_REPORT_TIME_ZONE parameter_REPORT_FILE_RESOLVER parameter_diaSemana parameter_REPORT_PARAMETERS_MAP parameter_REPORT_CLASS_LOADER $parameter_REPORT_URL_HANDLER_FACTORY parameter_REPORT_DATA_SOURCE parameter_IS_IGNORE_PAGINATION parameter_REPORT_MAX_COUNT parameter_REPORT_TEMPLATES parameter_REPORT_LOCALE parameter_dataIni parameter_horario parameter_REPORT_VIRTUALIZER parameter_logoPadraoRelatorio parameter_REPORT_SCRIPTLET parameter_ambiente parameter_REPORT_CONNECTION parameter_turma parameter_dadosReposicoes parameter_SUBREPORT_DIR parameter_dataFim parameter_REPORT_FORMAT_FACTORY parameter_nomeEmpresa parameter_professor parameter_cidadeEmpresa  parameter_REPORT_RESOURCE_BUNDLE field_clienteVO46pessoa46nome .Lnet/sf/jasperreports/engine/fill/JRFillField; field_qtdePresencasPeriodo field_qtdeAulasPeriodo field_frequenciaAluno field_clienteVO46matricula variable_PAGE_NUMBER 1Lnet/sf/jasperreports/engine/fill/JRFillVariable; variable_COLUMN_NUMBER variable_REPORT_COUNT variable_PAGE_COUNT variable_COLUMN_COUNT variable_alunos_COUNT variable_sum_aulas variable_sum_presencas variable_avg_freq <init> ()V Code 3 4
  6  	  8  	  :  	  < 	 	  > 
 	  @  	  B  	  D 
 	  F  	  H  	  J  	  L  	  N  	  P  	  R  	  T  	  V  	  X  	  Z  	  \  	  ^  	  `  	  b  	  d  	  f  	  h  	  j   	  l ! 	  n " 	  p # $	  r % $	  t & $	  v ' $	  x ( $	  z ) *	  | + *	  ~ , *	   - *	   . *	   / *	   0 *	   1 *	   2 *	   LineNumberTable customizedInit 0(Ljava/util/Map;Ljava/util/Map;Ljava/util/Map;)V 
initParams (Ljava/util/Map;)V  
   
initFields  
   initVars  
   enderecoEmpresa  
java/util/Map  get &(Ljava/lang/Object;)Ljava/lang/Object;     ¡ 0net/sf/jasperreports/engine/fill/JRFillParameter £ 
JASPER_REPORT ¥ REPORT_TIME_ZONE § REPORT_FILE_RESOLVER © 	diaSemana « REPORT_PARAMETERS_MAP ­ REPORT_CLASS_LOADER ¯ REPORT_URL_HANDLER_FACTORY ± REPORT_DATA_SOURCE ³ IS_IGNORE_PAGINATION µ REPORT_MAX_COUNT · REPORT_TEMPLATES ¹ 
REPORT_LOCALE » dataIni ½ horario ¿ REPORT_VIRTUALIZER Á logoPadraoRelatorio Ã REPORT_SCRIPTLET Å ambiente Ç REPORT_CONNECTION É turma Ë dadosReposicoes Í 
SUBREPORT_DIR Ï dataFim Ñ REPORT_FORMAT_FACTORY Ó nomeEmpresa Õ 	professor × 
cidadeEmpresa Ù REPORT_RESOURCE_BUNDLE Û clienteVO.pessoa.nome Ý ,net/sf/jasperreports/engine/fill/JRFillField ß qtdePresencasPeriodo á qtdeAulasPeriodo ã frequenciaAluno å clienteVO.matricula ç PAGE_NUMBER é /net/sf/jasperreports/engine/fill/JRFillVariable ë 
COLUMN_NUMBER í REPORT_COUNT ï 
PAGE_COUNT ñ COLUMN_COUNT ó alunos_COUNT õ 	sum_aulas ÷ 
sum_presencas ù avg_freq û evaluate (I)Ljava/lang/Object; 
Exceptions java/lang/Throwable  java/lang/Integer (I)V 3
 getValue ()Ljava/lang/Object;
 à	 java/lang/Double
 ì	 java/lang/StringBuffer toString ()Ljava/lang/String;
 java/lang/String valueOf &(Ljava/lang/Object;)Ljava/lang/String;
 (Ljava/lang/String;)V 3
 % append ,(Ljava/lang/String;)Ljava/lang/StringBuffer; 
!

 ¤	 java/io/InputStream% java/lang/Boolean' intValue ()I)*
+ (Z)V 3-
(. java/util/Date0
1 6  3 ,(Ljava/lang/Object;)Ljava/lang/StringBuffer;5
6 	PÃ¡gina: 8  de : (net/sf/jasperreports/engine/JRDataSource< %FrequenciaAlunos_subReposicoes.jasper> evaluateOld getOldValueA
 àB
 ìB evaluateEstimated getEstimatedValueF
 ìG 
SourceFile !     +                 	     
               
                                                                                                     !     "     # $    % $    & $    ' $    ( $    ) *    + *    , *    - *    . *    / *    0 *    1 *    2 *     3 4  5  ¤     Ü*· 7*µ 9*µ ;*µ =*µ ?*µ A*µ C*µ E*µ G*µ I*µ K*µ M*µ O*µ Q*µ S*µ U*µ W*µ Y*µ [*µ ]*µ _*µ a*µ c*µ e*µ g*µ i*µ k*µ m*µ o*µ q*µ s*µ u*µ w*µ y*µ {*µ }*µ *µ *µ *µ *µ *µ *µ *µ ±       ¶ -      	          "  '   , ! 1 " 6 # ; $ @ % E & J ' O ( T ) Y * ^ + c , h - m . r / w 0 | 1  2  3  4  5  6  7  8 ¤ 9 © : ® ; ³ < ¸ = ½ > Â ? Ç @ Ì A Ñ B Ö C Û      5   4     *+· *,· *-· ±           O  P 
 Q  R     5      *+¹ ¢ À ¤À ¤µ 9*+¦¹ ¢ À ¤À ¤µ ;*+¨¹ ¢ À ¤À ¤µ =*+ª¹ ¢ À ¤À ¤µ ?*+¬¹ ¢ À ¤À ¤µ A*+®¹ ¢ À ¤À ¤µ C*+°¹ ¢ À ¤À ¤µ E*+²¹ ¢ À ¤À ¤µ G*+´¹ ¢ À ¤À ¤µ I*+¶¹ ¢ À ¤À ¤µ K*+¸¹ ¢ À ¤À ¤µ M*+º¹ ¢ À ¤À ¤µ O*+¼¹ ¢ À ¤À ¤µ Q*+¾¹ ¢ À ¤À ¤µ S*+À¹ ¢ À ¤À ¤µ U*+Â¹ ¢ À ¤À ¤µ W*+Ä¹ ¢ À ¤À ¤µ Y*+Æ¹ ¢ À ¤À ¤µ [*+È¹ ¢ À ¤À ¤µ ]*+Ê¹ ¢ À ¤À ¤µ _*+Ì¹ ¢ À ¤À ¤µ a*+Î¹ ¢ À ¤À ¤µ c*+Ð¹ ¢ À ¤À ¤µ e*+Ò¹ ¢ À ¤À ¤µ g*+Ô¹ ¢ À ¤À ¤µ i*+Ö¹ ¢ À ¤À ¤µ k*+Ø¹ ¢ À ¤À ¤µ m*+Ú¹ ¢ À ¤À ¤µ o*+Ü¹ ¢ À ¤À ¤µ q±       z    Z  [ $ \ 6 ] H ^ Z _ l ` ~ a  b ¢ c ´ d Æ e Ø f ê g ü h i  j2 kD lV mh nz o p q° rÂ sÔ tæ uø v
 w     5        [*+Þ¹ ¢ À àÀ àµ s*+â¹ ¢ À àÀ àµ u*+ä¹ ¢ À àÀ àµ w*+æ¹ ¢ À àÀ àµ y*+è¹ ¢ À àÀ àµ {±              $  6  H  Z      5   ß     £*+ê¹ ¢ À ìÀ ìµ }*+î¹ ¢ À ìÀ ìµ *+ð¹ ¢ À ìÀ ìµ *+ò¹ ¢ À ìÀ ìµ *+ô¹ ¢ À ìÀ ìµ *+ö¹ ¢ À ìÀ ìµ *+ø¹ ¢ À ìÀ ìµ *+ú¹ ¢ À ìÀ ìµ *+ü¹ ¢ À ìÀ ìµ ±       * 
      $  6  H  Z  l  ~    ¢   ý þ  ÿ     5  j    Mª         %   ¥   ±   ½   É   Õ   á   í   ù        +  9  G  U  c      £  ±  ¿  Í  Û  é  ÷      !  D  R  `  n       «  É  í  û»Y·M§k»Y·M§_»Y·M§S»Y·M§G»Y·M§;»Y·M§/»Y·M§#»Y·M§»Y·M§»Y·M§ÿ*´ w¶
ÀM§ñ*´ u¶
ÀM§ã*´ y¶
ÀM§Õ*´ ¶
ÀM§Ç*´ ¶
ÀM§¹»Y*´ ¶
À¶¸·¶"¶#M§*´ g¶$ÀM§*´ S¶$ÀM§y*´ o¶$ÀM§k*´ Y¶$À&M§]*´ 9¶$ÀM§O*´ k¶$ÀM§A*´ a¶$ÀM§3*´ m¶$ÀM§%*´ ]¶$ÀM§*´ A¶$ÀM§	*´ U¶$ÀM§ û»(Y*´ ¶
À¶,p  § ·/M§ Ø*´ {¶
ÀM§ Ê*´ s¶
ÀM§ ¼*´ w¶
ÀM§ ®»Y*´ y¶
À¶¸·¶"¶#M§ *´ u¶
ÀM§ |»1Y·2M§ q»Y4·*´ }¶
À¶7¶#M§ S»Y9·*´ }¶
À¶7;¶"¶#M§ /*´ c¶$À=M§ !»Y*´ e¶$À¸·?¶"¶#M,°      : N      ¨ £ ± ¤ ´ ¨ ½ © À ­ É ® Ì ² Õ ³ Ø · á ¸ ä ¼ í ½ ð Á ù Â ü Æ Ç Ë Ì Ð Ñ  Õ+ Ö. Ú9 Û< ßG àJ äU åX éc êf î ï ó ô ø£ ù¦ ý± þ´¿ÂÍÐÛ
Þéì÷ú !%!&$*D+G/R0U4`5c9n:q>?C D£H«I®MÉNÌRíSðWûXþ\d @ þ  ÿ     5  j    Mª         %   ¥   ±   ½   É   Õ   á   í   ù        +  9  G  U  c      £  ±  ¿  Í  Û  é  ÷      !  D  R  `  n       «  É  í  û»Y·M§k»Y·M§_»Y·M§S»Y·M§G»Y·M§;»Y·M§/»Y·M§#»Y·M§»Y·M§»Y·M§ÿ*´ w¶CÀM§ñ*´ u¶CÀM§ã*´ y¶CÀM§Õ*´ ¶DÀM§Ç*´ ¶DÀM§¹»Y*´ ¶DÀ¶¸·¶"¶#M§*´ g¶$ÀM§*´ S¶$ÀM§y*´ o¶$ÀM§k*´ Y¶$À&M§]*´ 9¶$ÀM§O*´ k¶$ÀM§A*´ a¶$ÀM§3*´ m¶$ÀM§%*´ ]¶$ÀM§*´ A¶$ÀM§	*´ U¶$ÀM§ û»(Y*´ ¶DÀ¶,p  § ·/M§ Ø*´ {¶CÀM§ Ê*´ s¶CÀM§ ¼*´ w¶CÀM§ ®»Y*´ y¶CÀ¶¸·¶"¶#M§ *´ u¶CÀM§ |»1Y·2M§ q»Y4·*´ }¶DÀ¶7¶#M§ S»Y9·*´ }¶DÀ¶7;¶"¶#M§ /*´ c¶$À=M§ !»Y*´ e¶$À¸·?¶"¶#M,°      : N  m o ¨s ±t ´x ½y À} É~ Ì Õ Ø á ä í ð ù ü ¡ ¥+¦.ª9«<¯G°J´UµX¹cºf¾¿ÃÄÈ£É¦Í±Î´Ò¿ÓÂ×ÍØÐÜÛÝÞáéâìæ÷çúëìðñõ!ö$úDûGÿR U`c	n
q £«®ÉÌ"í#ð'û(þ,4 E þ  ÿ     5  j    Mª         %   ¥   ±   ½   É   Õ   á   í   ù        +  9  G  U  c      £  ±  ¿  Í  Û  é  ÷      !  D  R  `  n       «  É  í  û»Y·M§k»Y·M§_»Y·M§S»Y·M§G»Y·M§;»Y·M§/»Y·M§#»Y·M§»Y·M§»Y·M§ÿ*´ w¶
ÀM§ñ*´ u¶
ÀM§ã*´ y¶
ÀM§Õ*´ ¶HÀM§Ç*´ ¶HÀM§¹»Y*´ ¶HÀ¶¸·¶"¶#M§*´ g¶$ÀM§*´ S¶$ÀM§y*´ o¶$ÀM§k*´ Y¶$À&M§]*´ 9¶$ÀM§O*´ k¶$ÀM§A*´ a¶$ÀM§3*´ m¶$ÀM§%*´ ]¶$ÀM§*´ A¶$ÀM§	*´ U¶$ÀM§ û»(Y*´ ¶HÀ¶,p  § ·/M§ Ø*´ {¶
ÀM§ Ê*´ s¶
ÀM§ ¼*´ w¶
ÀM§ ®»Y*´ y¶
À¶¸·¶"¶#M§ *´ u¶
ÀM§ |»1Y·2M§ q»Y4·*´ }¶HÀ¶7¶#M§ S»Y9·*´ }¶HÀ¶7;¶"¶#M§ /*´ c¶$À=M§ !»Y*´ e¶$À¸·?¶"¶#M,°      : N  = ? ¨C ±D ´H ½I ÀM ÉN ÌR ÕS ØW áX ä\ í] ða ùb üfgklpq u+v.z9{<GJUXcf£¦±´¢¿£Â§Í¨Ð¬Û­Þ±é²ì¶÷·ú»¼ÀÁÅ!Æ$ÊDËGÏRÐUÔ`ÕcÙnÚqÞßã ä£è«é®íÉîÌòíóð÷ûøþü I    t _1365434326365_683480t 2net.sf.jasperreports.engine.design.JRJavacCompiler
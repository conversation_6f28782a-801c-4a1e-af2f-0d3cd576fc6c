<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="ComprovanteOperacaoContrato" pageWidth="680" pageHeight="842" columnWidth="625" leftMargin="20" rightMargin="35" topMargin="2" bottomMargin="2" whenResourceMissingType="Empty">
	<property name="ireport.zoom" value="1.5"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="81"/>
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.encoding" value="ISO-8859-1"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="java.util.*"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<parameter name="tituloRelatorio" class="java.lang.String" isForPrompting="false"/>
	<parameter name="nomeEmpresa" class="java.lang.String" isForPrompting="false"/>
	<parameter name="versaoSoftware" class="java.lang.String" isForPrompting="false"/>
	<parameter name="usuario" class="java.lang.String" isForPrompting="false"/>
	<parameter name="SUBREPORT_DIR" class="java.lang.String">
		<defaultValueExpression><![CDATA["C:\\PactoJ\\ZillyonWebTronco\\src\\main\\resources\\relatorio\\designRelatorio\\financeiro\\"]]></defaultValueExpression>
	</parameter>
	<parameter name="SUBREPORT_DIR1" class="java.lang.String">
		<defaultValueExpression><![CDATA["E:\\desenvolvimentosv\\Projeto Pacto Solucoes\\ZillyonWeb\\src\\java\\relatorio\\designRelatorio\\financeiro\\"]]></defaultValueExpression>
	</parameter>
	<parameter name="SUBREPORT_DIR2" class="java.lang.String">
		<defaultValueExpression><![CDATA["C:\\PactoJ\\ZillyonWebTronco\\src\\main\\resources\\relatorio\\designRelatorio\\financeiro\\"]]></defaultValueExpression>
	</parameter>
	<parameter name="empresaVO.cnpj" class="java.lang.String"/>
	<parameter name="empresaVO.endereco" class="java.lang.String"/>
	<parameter name="empresaVO.site" class="java.lang.String"/>
	<parameter name="empresaVO.fone" class="java.lang.String"/>
	<parameter name="logoPadraoRelatorio" class="java.io.InputStream" isForPrompting="false"/>
	<parameter name="dataInicioEfetivacaoOperacao_Apresentar" class="java.lang.String" isForPrompting="false"/>
	<parameter name="dataFimEfetivacaoOperacao_Apresentar" class="java.lang.String" isForPrompting="false"/>
	<parameter name="dataOperacao_Apresentar" class="java.lang.String" isForPrompting="false"/>
	<parameter name="responsavel.nome" class="java.lang.String" isForPrompting="false"/>
	<parameter name="tipoOperacao_Apresentar" class="java.lang.String" isForPrompting="false"/>
	<parameter name="justificativaApresentar" class="java.lang.String" isForPrompting="false"/>
	<parameter name="observacao" class="java.lang.String" isForPrompting="false"/>
	<parameter name="descricaoCalculo" class="java.lang.String" isForPrompting="false"/>
	<parameter name="pessoa" class="java.lang.String"/>
	<parameter name="contrato" class="java.lang.Integer"/>
	<field name="tipoOperacao_Apresentar" class="java.lang.String"/>
	<field name="dataOperacao_Apresentar" class="java.lang.String"/>
	<field name="dataInicioEfetivacaoOperacao_Apresentar" class="java.lang.String"/>
	<field name="dataFimEfetivacaoOperacao_Apresentar" class="java.lang.String"/>
	<field name="responsavel.nome" class="java.lang.String"/>
	<field name="contrato" class="java.lang.Integer"/>
	<field name="justificativaApresentar" class="java.lang.String"/>
	<field name="observacao" class="java.lang.String"/>
	<field name="descricaoCalculo" class="java.lang.String"/>
	<field name="nome" class="java.lang.String"/>
	<field name="reciboDevolucao.listaCheque" class="java.lang.Object"/>
	<field name="reciboDevolucao.listaCartoes" class="java.lang.Object"/>
	<field name="reciboDevolucao.apresentarCartoes" class="java.lang.Boolean"/>
	<field name="reciboDevolucao.apresentarCheques" class="java.lang.Boolean"/>
	<field name="assinaturaDigitalBiometria" class="java.lang.String"/>
	<detail>
		<band height="75">
			<staticText>
				<reportElement key="staticText-85" positionType="Float" mode="Opaque" x="5" y="44" width="118" height="13"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="SansSerif" size="9" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Operação]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-88" positionType="Float" mode="Opaque" x="444" y="44" width="180" height="13"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="SansSerif" size="9" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[ Responsável]]></text>
			</staticText>
			<image isUsingCache="true" onErrorType="Blank" evaluationTime="Page">
				<reportElement key="image-1" x="2" y="3" width="121" height="39" isPrintWhenDetailOverflows="true" forecolor="#FFFFFF"/>
				<imageExpression class="java.io.InputStream"><![CDATA[$P{logoPadraoRelatorio}]]></imageExpression>
			</image>
			<rectangle radius="10">
				<reportElement key="retDadosEmpresa1" x="126" y="3" width="334" height="39" backcolor="#F0F0F0"/>
				<graphicElement>
					<pen lineWidth="0.25"/>
				</graphicElement>
			</rectangle>
			<textField>
				<reportElement x="136" y="5" width="220" height="13"/>
				<textElement>
					<font fontName="Microsoft Sans Serif" isBold="true" isItalic="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{nomeEmpresa}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="136" y="17" width="319" height="13"/>
				<textElement>
					<font fontName="Microsoft Sans Serif" isBold="true" isItalic="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{empresaVO.endereco}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="136" y="29" width="319" height="13"/>
				<textElement>
					<font fontName="Microsoft Sans Serif" isBold="true" isItalic="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{empresaVO.site}.toLowerCase()]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="354" y="5" width="101" height="13"/>
				<textElement>
					<font fontName="Microsoft Sans Serif" isBold="true" isItalic="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{empresaVO.cnpj}]]></textFieldExpression>
			</textField>
			<rectangle radius="10">
				<reportElement key="retDadosRecibo1" x="470" y="3" width="148" height="39" backcolor="#F0F0F0"/>
				<graphicElement>
					<pen lineWidth="0.25"/>
				</graphicElement>
			</rectangle>
			<textField>
				<reportElement x="354" y="29" width="101" height="13"/>
				<textElement>
					<font fontName="Microsoft Sans Serif" isBold="true" isItalic="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{empresaVO.fone}]]></textFieldExpression>
			</textField>
			<line>
				<reportElement x="1" y="60" width="624" height="1"/>
			</line>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-229" mode="Opaque" x="444" y="62" width="180" height="12"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="9" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{responsavel.nome}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-229" mode="Opaque" x="130" y="62" width="80" height="12"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="9" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.Integer"><![CDATA[$F{contrato}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-229" stretchType="RelativeToTallestObject" mode="Opaque" x="474" y="8" width="140" height="28" isPrintWhenDetailOverflows="true" backcolor="#F0F0F0"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="11" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{tipoOperacao_Apresentar}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement key="staticText-85" positionType="Float" mode="Opaque" x="215" y="44" width="85" height="13"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="SansSerif" size="9" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Data Lançamento]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-85" positionType="Float" mode="Opaque" x="305" y="44" width="65" height="13"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="SansSerif" size="9" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Data Início]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-85" positionType="Float" mode="Opaque" x="375" y="44" width="65" height="13"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="SansSerif" size="9" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Data Final]]></text>
			</staticText>
			<textField pattern="" isBlankWhenNull="false">
				<reportElement key="textField-229" mode="Opaque" x="375" y="62" width="65" height="12"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="9" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{dataFimEfetivacaoOperacao_Apresentar}]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="false">
				<reportElement key="textField-229" mode="Opaque" x="305" y="62" width="65" height="12"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="9" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{dataInicioEfetivacaoOperacao_Apresentar}]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="false">
				<reportElement key="textField-229" mode="Opaque" x="215" y="62" width="85" height="12"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="9" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{dataOperacao_Apresentar}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="false">
				<reportElement key="textField-229" mode="Opaque" x="5" y="62" width="118" height="12" isPrintWhenDetailOverflows="true"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="9" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{tipoOperacao_Apresentar}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement key="staticText-85" positionType="Float" mode="Opaque" x="130" y="44" width="80" height="13"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="SansSerif" size="9" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Contrato]]></text>
			</staticText>
		</band>
		<band height="55" splitType="Prevent">
			<textField isStretchWithOverflow="true">
				<reportElement stretchType="RelativeToBandHeight" x="22" y="36" width="278" height="13"/>
				<textElement textAlignment="Justified">
					<font size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{descricaoCalculo}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement key="staticText-85" positionType="Float" mode="Opaque" x="5" y="4" width="90" height="13" isRemoveLineWhenBlank="true">
					<printWhenExpression><![CDATA[!$F{justificativaApresentar}.equals(null)]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="SansSerif" size="9" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Justificativa:]]></text>
			</staticText>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="101" y="4" width="517" height="13" isRemoveLineWhenBlank="true">
					<printWhenExpression><![CDATA[!$F{justificativaApresentar}.equals(null)]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Justified"/>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{justificativaApresentar}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement stretchType="RelativeToBandHeight" x="323" y="36" width="290" height="13"/>
				<textElement textAlignment="Justified">
					<font size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{observacao}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement key="staticText-85" mode="Opaque" x="285" y="23" width="90" height="13"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="SansSerif" size="9" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Observações:]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-85" positionType="Float" mode="Opaque" x="5" y="23" width="90" height="13"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="SansSerif" size="9" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Descrição Cálculo:]]></text>
			</staticText>
		</band>
		<band height="54">
			<printWhenExpression><![CDATA[$F{reciboDevolucao.apresentarCheques}]]></printWhenExpression>
			<staticText>
				<reportElement key="staticText-85" positionType="Float" mode="Opaque" x="5" y="3" width="108" height="13">
					<printWhenExpression><![CDATA[$F{reciboDevolucao.apresentarCheques}]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="SansSerif" size="9" isBold="true" isItalic="false" isUnderline="true" isStrikeThrough="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Cheques devolvidos]]></text>
			</staticText>
			<subreport isUsingCache="true">
				<reportElement key="subreport-1" x="5" y="16" width="455" height="34" isRemoveLineWhenBlank="true">
					<printWhenExpression><![CDATA[$F{reciboDevolucao.apresentarCheques}]]></printWhenExpression>
				</reportElement>
				<subreportParameter name="SUBREPORT_DIR">
					<subreportParameterExpression><![CDATA[$P{SUBREPORT_DIR}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="ListaCheque">
					<subreportParameterExpression><![CDATA[$F{reciboDevolucao.listaCheque}]]></subreportParameterExpression>
				</subreportParameter>
				<dataSourceExpression><![CDATA[$F{reciboDevolucao.listaCheque}]]></dataSourceExpression>
				<subreportExpression class="java.lang.String"><![CDATA[$P{SUBREPORT_DIR2} + "MovPagamento_cheques.jasper"]]></subreportExpression>
			</subreport>
		</band>
		<band height="51">
			<printWhenExpression><![CDATA[$F{reciboDevolucao.apresentarCartoes}]]></printWhenExpression>
			<staticText>
				<reportElement key="staticText-85" mode="Opaque" x="5" y="4" width="96" height="13">
					<printWhenExpression><![CDATA[$F{reciboDevolucao.apresentarCartoes}]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="SansSerif" size="9" isBold="true" isItalic="false" isUnderline="true" isStrikeThrough="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Cartões estornados]]></text>
			</staticText>
			<subreport>
				<reportElement key="subreport-2" x="5" y="18" width="454" height="21">
					<printWhenExpression><![CDATA[$F{reciboDevolucao.apresentarCartoes}]]></printWhenExpression>
				</reportElement>
				<dataSourceExpression><![CDATA[$F{reciboDevolucao.listaCartoes}]]></dataSourceExpression>
				<subreportExpression class="java.lang.String"><![CDATA[$P{SUBREPORT_DIR2} + "MovPagamento_cartaocredito.jasper"]]></subreportExpression>
			</subreport>
		</band>
	</detail>
	<columnFooter>
		<band height="224">
			<line>
				<reportElement x="36" y="110" width="514" height="1"/>
			</line>
			<textField>
				<reportElement x="136" y="113" width="323" height="12"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="8" isBold="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA["Cliente: " + $F{nome} + ", assinatura digital biométrica"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="5" y="0" width="613" height="96"/>
				<textElement verticalAlignment="Bottom">
					<font size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{assinaturaDigitalBiometria}]]></textFieldExpression>
			</textField>
			<line>
				<reportElement x="177" y="187" width="259" height="1"/>
			</line>
			<textField pattern="dd/MM/yyyy HH:mm:ss" isBlankWhenNull="false">
				<reportElement key="dataImpressao1" mode="Transparent" x="388" y="205" width="60" height="8"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="SansSerif" size="6" isItalic="false" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression class="java.util.Date"><![CDATA[new Date()]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="178" y="189" width="259" height="12"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="8" isBold="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA["Resp. Operação: " + $F{responsavel.nome}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="339" y="205" width="49" height="8"/>
				<textElement>
					<font size="6" isBold="true"/>
				</textElement>
				<text><![CDATA[Data impressão:]]></text>
			</staticText>
			<line>
				<reportElement x="1" y="223" width="624" height="1"/>
				<graphicElement>
					<pen lineWidth="1.5" lineStyle="Dashed"/>
				</graphicElement>
			</line>
		</band>
	</columnFooter>
</jasperReport>

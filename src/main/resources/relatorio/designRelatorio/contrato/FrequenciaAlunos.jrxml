<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="FrequenciaAlunos" pageWidth="595" pageHeight="842" columnWidth="555" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20">
	<property name="ireport.zoom" value="1.0"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<parameter name="nomeEmpresa" class="java.lang.String" isForPrompting="false"/>
	<parameter name="logoPadraoRelatorio" class="java.io.InputStream" isForPrompting="false"/>
	<parameter name="dataIni" class="java.lang.String" isForPrompting="false"/>
	<parameter name="dataFim" class="java.lang.String" isForPrompting="false"/>
	<parameter name="enderecoEmpresa" class="java.lang.String"/>
	<parameter name="cidadeEmpresa" class="java.lang.String"/>
	<parameter name="turma" class="java.lang.String"/>
	<parameter name="professor" class="java.lang.String"/>
	<parameter name="ambiente" class="java.lang.String"/>
	<parameter name="diaSemana" class="java.lang.String"/>
	<parameter name="horario" class="java.lang.String"/>
	<parameter name="SUBREPORT_DIR" class="java.lang.String"/>
	<parameter name="dadosReposicoes" class="java.lang.Object"/>
	<field name="clienteVO.matricula" class="java.lang.String"/>
	<field name="qtdeAulasPeriodo" class="java.lang.Integer"/>
	<field name="qtdePresencasPeriodo" class="java.lang.Integer"/>
	<field name="frequenciaAluno" class="java.lang.Double"/>
	<field name="clienteVO.pessoa.nome" class="java.lang.String"/>
	<variable name="sum_aulas" class="java.lang.Integer" calculation="Sum">
		<variableExpression><![CDATA[$F{qtdeAulasPeriodo}]]></variableExpression>
	</variable>
	<variable name="sum_presencas" class="java.lang.Integer" calculation="Sum">
		<variableExpression><![CDATA[$F{qtdePresencasPeriodo}]]></variableExpression>
	</variable>
	<variable name="avg_freq" class="java.lang.Double" calculation="Average">
		<variableExpression><![CDATA[$F{frequenciaAluno}]]></variableExpression>
	</variable>
	<group name="alunos">
		<groupFooter>
			<band height="17">
				<rectangle>
					<reportElement x="1" y="1" width="554" height="16" backcolor="#E6E6E6"/>
					<graphicElement>
						<pen lineWidth="0.0"/>
					</graphicElement>
				</rectangle>
				<textField>
					<reportElement x="315" y="0" width="80" height="17"/>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font isBold="true" isUnderline="false"/>
					</textElement>
					<textFieldExpression class="java.lang.Integer"><![CDATA[$V{sum_aulas}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="395" y="0" width="80" height="17"/>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font isBold="true"/>
					</textElement>
					<textFieldExpression class="java.lang.Integer"><![CDATA[$V{sum_presencas}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="475" y="0" width="80" height="17"/>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font isBold="true"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$V{avg_freq}.toString()+"%"]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement x="0" y="0" width="70" height="17"/>
					<textElement verticalAlignment="Middle">
						<font size="12" isBold="true"/>
					</textElement>
					<text><![CDATA[Totais]]></text>
				</staticText>
			</band>
		</groupFooter>
	</group>
	<background>
		<band splitType="Stretch"/>
	</background>
	<pageHeader>
		<band height="146">
			<staticText>
				<reportElement key="staticText-100" x="356" y="37" width="199" height="27"/>
				<textElement textAlignment="Right">
					<font size="18" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Frequência de Alunos]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-91" mode="Opaque" x="356" y="0" width="199" height="25"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Top" lineSpacing="Single">
					<font fontName="Microsoft Sans Serif" size="9" isBold="true" isItalic="true" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica-BoldOblique"/>
				</textElement>
				<text><![CDATA[Zillyon - Desenvolvido por PACTO
Soluções Tecnológicas Ltda.]]></text>
			</staticText>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-246" x="228" y="42" width="60" height="14"/>
				<textElement>
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{dataFim}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-245" x="144" y="42" width="60" height="14"/>
				<textElement>
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{dataIni}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement key="staticText-103" mode="Opaque" x="204" y="42" width="24" height="14"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="12" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[até]]></text>
			</staticText>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-249" x="144" y="28" width="212" height="14"/>
				<textElement>
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{cidadeEmpresa}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement key="staticText-102" mode="Opaque" x="86" y="42" width="58" height="14"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Período:]]></text>
			</staticText>
			<image isUsingCache="true" onErrorType="Blank">
				<reportElement key="image-1" x="0" y="0" width="75" height="65" isPrintWhenDetailOverflows="true"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<imageExpression class="java.io.InputStream"><![CDATA[$P{logoPadraoRelatorio}]]></imageExpression>
			</image>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-248" x="144" y="14" width="212" height="14"/>
				<textElement>
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{enderecoEmpresa}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement key="staticText-92" mode="Opaque" x="444" y="25" width="111" height="12"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Top" lineSpacing="Single">
					<font fontName="Microsoft Sans Serif" size="9" isBold="true" isItalic="true" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica-BoldOblique"/>
				</textElement>
				<text><![CDATA[(0xx62) 3251-5820]]></text>
			</staticText>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-247" x="144" y="0" width="212" height="14"/>
				<textElement>
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{nomeEmpresa}]]></textFieldExpression>
			</textField>
			<line>
				<reportElement key="line-1" mode="Transparent" x="0" y="127" width="555" height="1"/>
				<graphicElement fill="Solid">
					<pen lineWidth="0.5" lineStyle="Solid"/>
				</graphicElement>
			</line>
			<staticText>
				<reportElement x="0" y="128" width="70" height="17"/>
				<textElement verticalAlignment="Middle">
					<font size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[Matrícula]]></text>
			</staticText>
			<staticText>
				<reportElement x="70" y="128" width="245" height="17"/>
				<textElement verticalAlignment="Middle">
					<font size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[Nome]]></text>
			</staticText>
			<staticText>
				<reportElement x="315" y="128" width="80" height="17"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[Qtde.Aulas]]></text>
			</staticText>
			<staticText>
				<reportElement x="395" y="128" width="80" height="17"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[Presenças]]></text>
			</staticText>
			<staticText>
				<reportElement x="475" y="128" width="80" height="17"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[Frequência]]></text>
			</staticText>
			<line>
				<reportElement x="0" y="145" width="555" height="1"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</line>
			<staticText>
				<reportElement key="staticText-102" mode="Opaque" x="86" y="28" width="58" height="14"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Cidade:]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-102" mode="Opaque" x="86" y="14" width="58" height="14"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Endereço:]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-102" mode="Opaque" x="86" y="0" width="58" height="14"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Empresa:]]></text>
			</staticText>
			<staticText>
				<reportElement x="0" y="75" width="60" height="17"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" isBold="true"/>
				</textElement>
				<text><![CDATA[Turma:]]></text>
			</staticText>
			<staticText>
				<reportElement x="0" y="92" width="60" height="17"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" isBold="true"/>
				</textElement>
				<text><![CDATA[Professor:]]></text>
			</staticText>
			<staticText>
				<reportElement x="356" y="75" width="70" height="17"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" isBold="true"/>
				</textElement>
				<text><![CDATA[Dia Semana:]]></text>
			</staticText>
			<staticText>
				<reportElement x="0" y="109" width="60" height="17"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" isBold="true"/>
				</textElement>
				<text><![CDATA[Ambiente:]]></text>
			</staticText>
			<staticText>
				<reportElement x="356" y="92" width="45" height="17"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" isBold="true"/>
				</textElement>
				<text><![CDATA[Horário:]]></text>
			</staticText>
			<textField>
				<reportElement x="60" y="75" width="200" height="17"/>
				<textElement verticalAlignment="Middle"/>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{turma}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="60" y="92" width="200" height="17"/>
				<textElement verticalAlignment="Middle"/>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{professor}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="60" y="109" width="200" height="17"/>
				<textElement verticalAlignment="Middle"/>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{ambiente}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="426" y="75" width="120" height="17"/>
				<textElement verticalAlignment="Middle"/>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{diaSemana}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="401" y="92" width="120" height="17"/>
				<textElement verticalAlignment="Middle"/>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{horario}]]></textFieldExpression>
			</textField>
		</band>
	</pageHeader>
	<detail>
		<band height="17">
			<rectangle>
				<reportElement x="0" y="0" width="555" height="17" backcolor="#E6E6E6">
					<printWhenExpression><![CDATA[new Boolean(($V{COLUMN_COUNT}.intValue()%2)==1)]]></printWhenExpression>
				</reportElement>
				<graphicElement>
					<pen lineWidth="0.0"/>
				</graphicElement>
			</rectangle>
			<textField>
				<reportElement x="0" y="0" width="70" height="17"/>
				<textElement verticalAlignment="Middle"/>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{clienteVO.matricula}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="70" y="0" width="245" height="17"/>
				<textElement verticalAlignment="Middle"/>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{clienteVO.pessoa.nome}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="315" y="0" width="80" height="17"/>
				<textElement textAlignment="Right" verticalAlignment="Middle"/>
				<textFieldExpression class="java.lang.Integer"><![CDATA[$F{qtdeAulasPeriodo}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="475" y="0" width="80" height="17"/>
				<textElement textAlignment="Right" verticalAlignment="Middle"/>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{frequenciaAluno}.toString()+"%"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="395" y="0" width="80" height="17"/>
				<textElement textAlignment="Right" verticalAlignment="Middle"/>
				<textFieldExpression class="java.lang.Integer"><![CDATA[$F{qtdePresencasPeriodo}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<pageFooter>
		<band height="18">
			<line>
				<reportElement key="line-2" mode="Transparent" x="0" y="0" width="555" height="1"/>
				<graphicElement fill="Solid"/>
			</line>
			<textField pattern="EEE, d MMM yyyy HH:mm:ss Z" isBlankWhenNull="false">
				<reportElement key="dataRel-1" mode="Transparent" x="1" y="1" width="204" height="17" backcolor="#FFFFFF"/>
				<box bottomPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Verdana" size="10" isBold="true" isItalic="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.util.Date"><![CDATA[new Date()]]></textFieldExpression>
			</textField>
			<textField evaluationTime="Report" isBlankWhenNull="false">
				<reportElement key="textField-242" x="510" y="1" width="45" height="17"/>
				<box bottomPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement>
					<font fontName="Arial" size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[" " + $V{PAGE_NUMBER} + ""]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-243" x="427" y="1" width="83" height="17"/>
				<box bottomPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right">
					<font fontName="Arial" size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA["Página: " + $V{PAGE_NUMBER} + " de "]]></textFieldExpression>
			</textField>
		</band>
	</pageFooter>
	<summary>
		<band height="50">
			<line>
				<reportElement key="line-1" mode="Transparent" x="0" y="1" width="555" height="2"/>
				<graphicElement fill="Solid">
					<pen lineWidth="0.5" lineStyle="Solid"/>
				</graphicElement>
			</line>
			<subreport isUsingCache="false">
				<reportElement key="subreport1" x="1" y="15" width="555" height="30"/>
				<dataSourceExpression><![CDATA[$P{dadosReposicoes}]]></dataSourceExpression>
				<subreportExpression class="java.lang.String"><![CDATA[$P{SUBREPORT_DIR} + "FrequenciaAlunos_subReposicoes.jasper"]]></subreportExpression>
			</subreport>
		</band>
	</summary>
</jasperReport>

<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="ReciboRel" pageWidth="680" pageHeight="842" whenNoDataType="AllSectionsNoDetail" columnWidth="625" leftMargin="20" rightMargin="35" topMargin="2" bottomMargin="2" whenResourceMissingType="Empty">
	<property name="ireport.zoom" value="1.0"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.encoding" value="ISO-8859-1"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="java.util.*"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<parameter name="tituloRelatorio" class="java.lang.String" isForPrompting="false"/>
	<parameter name="nomeEmpresa" class="java.lang.String" isForPrompting="false"/>
	<parameter name="versaoSoftware" class="java.lang.String" isForPrompting="false"/>
	<parameter name="usuario" class="java.lang.String" isForPrompting="false"/>
	<parameter name="filtros" class="java.lang.String" isForPrompting="false"/>
	<parameter name="SUBREPORT_DIR" class="java.lang.String">
		<defaultValueExpression><![CDATA["E:\\desenvolvimentosv\\Projeto Pacto Solucoes\\ZillyonWeb\\src\\java\\relatorio\\designRelatorio\\financeiro\\"]]></defaultValueExpression>
	</parameter>
	<parameter name="SUBREPORT_DIR1" class="java.lang.String">
		<defaultValueExpression><![CDATA["E:\\desenvolvimentosv\\Projeto Pacto Solucoes\\ZillyonWeb\\src\\java\\relatorio\\designRelatorio\\financeiro\\"]]></defaultValueExpression>
	</parameter>
	<parameter name="logoPadraoRelatorio" class="java.io.InputStream" isForPrompting="false"/>
	<parameter name="SUBREPORT_DIR2" class="java.lang.String">
		<defaultValueExpression><![CDATA["E:\\desenvolvimentosv\\Projeto Pacto Solucoes\\ZillyonWeb\\src\\java\\relatorio\\designRelatorio\\financeiro\\"]]></defaultValueExpression>
	</parameter>
	<parameter name="empresaVO.cnpj" class="java.lang.String"/>
	<parameter name="empresaVO.endereco" class="java.lang.String"/>
	<parameter name="empresaVO.site" class="java.lang.String"/>
	<parameter name="empresaVO.fone" class="java.lang.String"/>
	<parameter name="dataInicioEfetivacaoOperacao_Apresentar" class="java.lang.String" isForPrompting="false"/>
	<parameter name="dataFimEfetivacaoOperacao_Apresentar" class="java.lang.String" isForPrompting="false"/>
	<parameter name="dataOperacao_Apresentar" class="java.lang.Boolean" isForPrompting="false"/>
	<parameter name="contrato" class="java.lang.String" isForPrompting="false"/>
	<parameter name="responsavel.nome" class="java.lang.String" isForPrompting="false"/>
	<parameter name="tipoOperacao_Apresentar" class="java.lang.String" isForPrompting="false"/>
	<parameter name="justificativaApresentar" class="java.lang.String" isForPrompting="false"/>
	<parameter name="nome" class="java.lang.String" isForPrompting="false"/>
	<parameter name="descricaoCalculo" class="java.lang.String" isForPrompting="false"/>
	<parameter name="observacao" class="java.lang.String" isForPrompting="false"/>
	<field name="contratoOperacaoDatasource" class="java.lang.Object"/>
	<detail>
		<band height="41" splitType="Stretch">
			<subreport>
				<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="625" height="41"/>
				<subreportParameter name="justificativaApresentar">
					<subreportParameterExpression><![CDATA[$P{justificativaApresentar}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="tipoOperacao_Apresentar">
					<subreportParameterExpression><![CDATA[$P{tipoOperacao_Apresentar}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="logoPadraoRelatorio">
					<subreportParameterExpression><![CDATA[$P{logoPadraoRelatorio}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="SUBREPORT_DIR1">
					<subreportParameterExpression><![CDATA[$P{SUBREPORT_DIR1}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="contrato">
					<subreportParameterExpression><![CDATA[$P{contrato}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="SUBREPORT_DIR">
					<subreportParameterExpression><![CDATA[$P{SUBREPORT_DIR}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="empresaVO.cnpj">
					<subreportParameterExpression><![CDATA[$P{empresaVO.cnpj}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="SUBREPORT_DIR2">
					<subreportParameterExpression><![CDATA[$P{SUBREPORT_DIR2}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="tituloRelatorio">
					<subreportParameterExpression><![CDATA[$P{tituloRelatorio}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="empresaVO.endereco">
					<subreportParameterExpression><![CDATA[$P{empresaVO.endereco}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="nomeEmpresa">
					<subreportParameterExpression><![CDATA[$P{nomeEmpresa}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="empresaVO.site">
					<subreportParameterExpression><![CDATA[$P{empresaVO.site}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="dataInicioEfetivacaoOperacao_Apresentar">
					<subreportParameterExpression><![CDATA[$P{dataInicioEfetivacaoOperacao_Apresentar}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="dataFimEfetivacaoOperacao_Apresentar">
					<subreportParameterExpression><![CDATA[$P{dataFimEfetivacaoOperacao_Apresentar}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="nome">
					<subreportParameterExpression><![CDATA[$P{nome}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="empresaVO.fone">
					<subreportParameterExpression><![CDATA[$P{empresaVO.fone}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="versaoSoftware">
					<subreportParameterExpression><![CDATA[$P{versaoSoftware}]]></subreportParameterExpression>
				</subreportParameter>
				<dataSourceExpression><![CDATA[$F{contratoOperacaoDatasource}]]></dataSourceExpression>
				<subreportExpression class="java.lang.String"><![CDATA[$P{SUBREPORT_DIR} + "ComprovanteOperacaoContrato1.jasper"]]></subreportExpression>
			</subreport>
		</band>
	</detail>
</jasperReport>

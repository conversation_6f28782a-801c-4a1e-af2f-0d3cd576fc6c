<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="frequenciaOcupacaoTurmas" pageWidth="842" pageHeight="595" orientation="Landscape" columnWidth="802" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20">
	<property name="ireport.zoom" value="1.5394743546921212"/>
	<property name="ireport.x" value="361"/>
	<property name="ireport.y" value="0"/>
	<parameter name="nomeEmpresa" class="java.lang.String" isForPrompting="false"/>
	<parameter name="logoPadraoRelatorio" class="java.io.InputStream" isForPrompting="false"/>
	<parameter name="dataIni" class="java.lang.String" isForPrompting="false"/>
	<parameter name="dataFim" class="java.lang.String" isForPrompting="false"/>
	<parameter name="enderecoEmpresa" class="java.lang.String" isForPrompting="false"/>
	<parameter name="cidadeEmpresa" class="java.lang.String" isForPrompting="false"/>
	<parameter name="SUBREPORT_DIR" class="java.lang.String">
		<defaultValueExpression><![CDATA["D:\\PactoJ\\Desenvolvimento\\Sistemas\\ZillyonWeb\\tronco-novo\\src\\java\\relatorio\\designRelatorio\\contrato\\"]]></defaultValueExpression>
	</parameter>
	<parameter name="filtros" class="java.lang.String" isForPrompting="false"/>
	<parameter name="CONT" class="java.lang.Integer" isForPrompting="false"/>
	<parameter name="usuario" class="java.lang.String" isForPrompting="false"/>
	<parameter name="versaoSoftware" class="java.lang.String" isForPrompting="false"/>
	<field name="horario.identificadorTurma" class="java.lang.String"/>
	<field name="horario.professor.pessoa.nome" class="java.lang.String"/>
	<field name="horario.ambiente.descricao" class="java.lang.String"/>
	<field name="horario.diaSemana_Apresentar" class="java.lang.String"/>
	<field name="horario.horaInicial" class="java.lang.String"/>
	<field name="horario.horaFinal" class="java.lang.String"/>
	<field name="horario.nrMaximoAluno" class="java.lang.Integer"/>
	<field name="horario.nrAlunoMatriculado" class="java.lang.Integer"/>
	<field name="horario.txOcupacao" class="java.lang.Double"/>
	<field name="horario.freqMedia" class="java.lang.Double"/>
	<field name="horario.codigo" class="java.lang.Integer"/>
	<field name="horario.nrAlunoSairamPorReposicao" class="java.lang.Integer"/>
	<field name="horario.nrAlunoEntraramPorReposicao" class="java.lang.Integer"/>
	<field name="qtdPrevisto" class="java.lang.Integer"/>
	<field name="qtdPresencas" class="java.lang.Integer"/>
	<field name="qtdAlunos" class="java.lang.Integer"/>
	<variable name="qtdeTurmas" class="java.lang.Integer" calculation="Count">
		<variableExpression><![CDATA[$F{horario.codigo}]]></variableExpression>
	</variable>
	<variable name="totalVagas" class="java.lang.Integer" calculation="Sum">
		<variableExpression><![CDATA[$F{horario.nrMaximoAluno}]]></variableExpression>
		<initialValueExpression><![CDATA[]]></initialValueExpression>
	</variable>
	<variable name="totalOcupadas" class="java.lang.Integer" calculation="Sum">
		<variableExpression><![CDATA[$F{qtdAlunos}]]></variableExpression>
	</variable>
	<variable name="mediaFrequencia" class="java.lang.Double" calculation="Average">
		<variableExpression><![CDATA[$F{horario.freqMedia}/100]]></variableExpression>
	</variable>
	<variable name="totalDemarcadas" class="java.lang.Integer" calculation="Sum">
		<variableExpression><![CDATA[$F{horario.nrAlunoSairamPorReposicao}]]></variableExpression>
	</variable>
	<variable name="totalReposicoes" class="java.lang.Integer" calculation="Sum">
		<variableExpression><![CDATA[$F{horario.nrAlunoEntraramPorReposicao}]]></variableExpression>
	</variable>
	<variable name="totalPrevisto" class="java.lang.Integer" calculation="Sum">
		<variableExpression><![CDATA[$F{qtdPrevisto}]]></variableExpression>
	</variable>
	<variable name="totalPresencas" class="java.lang.Integer" calculation="Sum">
		<variableExpression><![CDATA[$F{qtdPresencas}]]></variableExpression>
	</variable>
	<group name="turmas">
		<groupFooter>
			<band height="28">
				<printWhenExpression><![CDATA[!$P{nomeEmpresa}.isEmpty()]]></printWhenExpression>
				<textField>
					<reportElement x="430" y="8" width="60" height="20"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="10"/>
					</textElement>
					<textFieldExpression class="java.lang.Integer"><![CDATA[$V{qtdeTurmas}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement x="365" y="8" width="65" height="20"/>
					<textElement verticalAlignment="Middle">
						<font size="10"/>
					</textElement>
					<text><![CDATA[Qtde Turmas:]]></text>
				</staticText>
				<textField>
					<reportElement x="490" y="7" width="45" height="21"/>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font size="10"/>
					</textElement>
					<textFieldExpression class="java.lang.Integer"><![CDATA[$V{totalVagas}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="535" y="8" width="33" height="20"/>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font size="10"/>
					</textElement>
					<textFieldExpression class="java.lang.Integer"><![CDATA[$V{totalOcupadas}]]></textFieldExpression>
				</textField>
				<textField pattern="#,##0.00 %">
					<reportElement x="708" y="8" width="50" height="20"/>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font size="10"/>
					</textElement>
					<textFieldExpression class="java.lang.Double"><![CDATA[$V{totalOcupadas}.doubleValue()/$V{totalVagas}.doubleValue()]]></textFieldExpression>
				</textField>
				<textField pattern="#,##0.00 %">
					<reportElement x="758" y="8" width="44" height="20"/>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font size="10"/>
					</textElement>
					<textFieldExpression class="java.lang.Double"><![CDATA[$V{mediaFrequencia}]]></textFieldExpression>
				</textField>
				<line>
					<reportElement x="365" y="7" width="437" height="1"/>
				</line>
				<textField>
					<reportElement x="650" y="8" width="29" height="20"/>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font size="10"/>
					</textElement>
					<textFieldExpression class="java.lang.Integer"><![CDATA[$V{totalDemarcadas}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="679" y="8" width="29" height="20"/>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font size="10"/>
					</textElement>
					<textFieldExpression class="java.lang.Integer"><![CDATA[$V{totalReposicoes}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="568" y="8" width="43" height="20"/>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font size="10"/>
					</textElement>
					<textFieldExpression class="java.lang.Integer"><![CDATA[$V{totalPrevisto}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="611" y="8" width="38" height="20"/>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font size="10"/>
					</textElement>
					<textFieldExpression class="java.lang.Integer"><![CDATA[$V{totalPresencas}]]></textFieldExpression>
				</textField>
			</band>
		</groupFooter>
	</group>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band height="80">
			<printWhenExpression><![CDATA[!$P{nomeEmpresa}.isEmpty()]]></printWhenExpression>
			<image isUsingCache="true" onErrorType="Blank">
				<reportElement key="image-1" x="0" y="0" width="75" height="65" isPrintWhenDetailOverflows="true"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<imageExpression class="java.io.InputStream"><![CDATA[$P{logoPadraoRelatorio}]]></imageExpression>
			</image>
			<staticText>
				<reportElement key="staticText-100" x="430" y="36" width="372" height="27"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="20" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Frequência e Ocupação de Turmas]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-91" mode="Opaque" x="540" y="0" width="262" height="23"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Top" lineSpacing="Single">
					<font fontName="Microsoft Sans Serif" size="9" isBold="true" isItalic="true" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica-BoldOblique"/>
				</textElement>
				<text><![CDATA[Zillyon - Sistema Administrativo para Academias Desenvolvido por PACTO Soluções Tecnológicas Ltda.]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-92" mode="Opaque" x="691" y="24" width="111" height="12"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Top" lineSpacing="Single">
					<font fontName="Microsoft Sans Serif" size="9" isBold="true" isItalic="true" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica-BoldOblique"/>
				</textElement>
				<text><![CDATA[(0xx62) 3251-5820]]></text>
			</staticText>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-246" x="229" y="43" width="60" height="14"/>
				<textElement>
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{dataFim}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-247" x="145" y="1" width="285" height="14"/>
				<textElement>
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{nomeEmpresa}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-245" x="145" y="43" width="60" height="14"/>
				<textElement>
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{dataIni}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement key="staticText-103" mode="Opaque" x="205" y="43" width="24" height="14"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="12" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[até]]></text>
			</staticText>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-249" x="145" y="29" width="285" height="14"/>
				<textElement>
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{cidadeEmpresa}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement key="staticText-102" mode="Opaque" x="87" y="15" width="58" height="14"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Endereço:]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-102" mode="Opaque" x="87" y="43" width="58" height="14"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Período:]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-102" mode="Opaque" x="87" y="1" width="58" height="14"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Empresa:]]></text>
			</staticText>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-248" x="145" y="15" width="285" height="14"/>
				<textElement>
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{enderecoEmpresa}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement key="staticText-102" mode="Opaque" x="87" y="29" width="58" height="14"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Cidade:]]></text>
			</staticText>
		</band>
	</title>
	<columnHeader>
		<band height="17">
			<staticText>
				<reportElement key="staticText-96" mode="Transparent" x="649" y="1" width="29" height="14"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[D.]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-96" mode="Transparent" x="490" y="1" width="45" height="14"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Vagas]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-96" mode="Transparent" x="708" y="1" width="50" height="14"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Tx.Ocup.]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-82" mode="Transparent" x="0" y="1" width="90" height="14"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Turma]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-96" mode="Transparent" x="758" y="1" width="45" height="14"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Freq.]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-96" mode="Transparent" x="678" y="1" width="29" height="14"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[R.]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-96" mode="Transparent" x="535" y="1" width="33" height="14"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Ocup.]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-96" mode="Transparent" x="568" y="1" width="43" height="14"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Prev.]]></text>
			</staticText>
			<line>
				<reportElement key="line-1" mode="Transparent" x="1" y="0" width="801" height="1"/>
				<graphicElement fill="Solid">
					<pen lineWidth="0.5" lineStyle="Solid"/>
				</graphicElement>
			</line>
			<staticText>
				<reportElement key="staticText-96" mode="Transparent" x="611" y="1" width="38" height="14"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Pres.]]></text>
			</staticText>
			<line>
				<reportElement key="line-2" mode="Transparent" x="0" y="15" width="802" height="1"/>
				<graphicElement fill="Solid">
					<pen lineWidth="0.5" lineStyle="Double"/>
				</graphicElement>
			</line>
			<staticText>
				<reportElement key="staticText-84" mode="Transparent" x="90" y="1" width="159" height="14"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Professor]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-96" mode="Transparent" x="413" y="1" width="77" height="14"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Horário]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-98" mode="Transparent" x="303" y="1" width="110" height="14"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Ambiente]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-81" mode="Transparent" x="233" y="1" width="70" height="14"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Dia Semana]]></text>
			</staticText>
		</band>
	</columnHeader>
	<detail>
		<band height="17" splitType="Stretch">
			<rectangle>
				<reportElement x="1" y="0" width="801" height="17" backcolor="#E6E6E6">
					<printWhenExpression><![CDATA[new Boolean(($V{COLUMN_COUNT}.intValue()%2)==1)]]></printWhenExpression>
				</reportElement>
				<graphicElement>
					<pen lineWidth="0.0"/>
				</graphicElement>
			</rectangle>
			<textField>
				<reportElement x="303" y="0" width="110" height="17"/>
				<textElement verticalAlignment="Middle"/>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{horario.ambiente.descricao}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="413" y="0" width="77" height="17"/>
				<textElement textAlignment="Center" verticalAlignment="Middle"/>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{horario.horaInicial} +" - "+ $F{horario.horaFinal}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="490" y="0" width="45" height="17"/>
				<textElement textAlignment="Right" verticalAlignment="Middle"/>
				<textFieldExpression class="java.lang.Integer"><![CDATA[$F{horario.nrMaximoAluno}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="535" y="0" width="33" height="17"/>
				<textElement textAlignment="Right" verticalAlignment="Middle"/>
				<textFieldExpression class="java.lang.Integer"><![CDATA[$F{qtdAlunos}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00 %">
				<reportElement x="708" y="0" width="50" height="17"/>
				<textElement textAlignment="Right" verticalAlignment="Middle"/>
				<textFieldExpression class="java.lang.Double"><![CDATA[$F{horario.txOcupacao}/100]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00 %">
				<reportElement x="758" y="0" width="45" height="17"/>
				<textElement textAlignment="Right" verticalAlignment="Middle"/>
				<textFieldExpression class="java.lang.Double"><![CDATA[$F{horario.freqMedia}/100]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="0" y="0" width="90" height="17"/>
				<textElement verticalAlignment="Middle"/>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{horario.identificadorTurma}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="90" y="0" width="139" height="17"/>
				<textElement verticalAlignment="Middle"/>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{horario.professor.pessoa.nome}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="233" y="0" width="70" height="17"/>
				<textElement verticalAlignment="Middle"/>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{horario.diaSemana_Apresentar}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="678" y="0" width="29" height="17"/>
				<textElement textAlignment="Right" verticalAlignment="Middle"/>
				<textFieldExpression class="java.lang.Integer"><![CDATA[$F{horario.nrAlunoEntraramPorReposicao}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="649" y="0" width="29" height="17"/>
				<textElement textAlignment="Right" verticalAlignment="Middle"/>
				<textFieldExpression class="java.lang.Integer"><![CDATA[$F{horario.nrAlunoSairamPorReposicao}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="568" y="0" width="43" height="17"/>
				<textElement textAlignment="Right" verticalAlignment="Middle"/>
				<textFieldExpression class="java.lang.Integer"><![CDATA[$F{qtdPrevisto}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="611" y="0" width="38" height="17"/>
				<textElement textAlignment="Right" verticalAlignment="Middle"/>
				<textFieldExpression class="java.lang.Integer"><![CDATA[$F{qtdPresencas}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<pageFooter>
		<band height="18">
			<printWhenExpression><![CDATA[!$P{nomeEmpresa}.isEmpty()]]></printWhenExpression>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-243" x="675" y="1" width="83" height="17"/>
				<box bottomPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right">
					<font fontName="Arial" size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA["Página: " + $V{PAGE_NUMBER} + " de "]]></textFieldExpression>
			</textField>
			<line>
				<reportElement key="line-2" mode="Transparent" x="0" y="0" width="802" height="1"/>
				<graphicElement fill="Solid"/>
			</line>
			<textField pattern="EEE, d MMM yyyy HH:mm:ss Z" isBlankWhenNull="false">
				<reportElement key="dataRel-1" mode="Transparent" x="1" y="1" width="204" height="17" backcolor="#FFFFFF"/>
				<box bottomPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Verdana" size="10" isBold="true" isItalic="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.util.Date"><![CDATA[new Date()]]></textFieldExpression>
			</textField>
			<textField evaluationTime="Report" isBlankWhenNull="false">
				<reportElement key="textField-242" x="758" y="1" width="45" height="17"/>
				<box bottomPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement>
					<font fontName="Arial" size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[" " + $V{PAGE_NUMBER} + ""]]></textFieldExpression>
			</textField>
		</band>
	</pageFooter>
</jasperReport>

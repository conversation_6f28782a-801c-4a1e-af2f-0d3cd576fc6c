¬í sr (net.sf.jasperreports.engine.JasperReport      'Ø L compileDatat Ljava/io/Serializable;L compileNameSuffixt Ljava/lang/String;L 
compilerClassq ~ xr -net.sf.jasperreports.engine.base.JRBaseReport      'Ø ,I PSEUDO_SERIAL_VERSION_UIDI bottomMarginI columnCountI 
columnSpacingI columnWidthZ ignorePaginationZ isFloatColumnFooterZ isSummaryNewPageZ  isSummaryWithPageHeaderAndFooterZ isTitleNewPageI 
leftMarginB orientationI 
pageHeightI 	pageWidthB 
printOrderI rightMarginI 	topMarginB whenNoDataTypeL 
backgroundt $Lnet/sf/jasperreports/engine/JRBand;L columnDirectiont 3Lnet/sf/jasperreports/engine/type/RunDirectionEnum;L columnFooterq ~ L columnHeaderq ~ [ datasetst ([Lnet/sf/jasperreports/engine/JRDataset;L defaultFontt *Lnet/sf/jasperreports/engine/JRReportFont;L defaultStylet %Lnet/sf/jasperreports/engine/JRStyle;L detailq ~ L 
detailSectiont 'Lnet/sf/jasperreports/engine/JRSection;[ fontst +[Lnet/sf/jasperreports/engine/JRReportFont;L formatFactoryClassq ~ L 
importsSett Ljava/util/Set;L languageq ~ L lastPageFooterq ~ L mainDatasett 'Lnet/sf/jasperreports/engine/JRDataset;L nameq ~ L noDataq ~ L orientationValuet 2Lnet/sf/jasperreports/engine/type/OrientationEnum;L 
pageFooterq ~ L 
pageHeaderq ~ L printOrderValuet 1Lnet/sf/jasperreports/engine/type/PrintOrderEnum;[ stylest &[Lnet/sf/jasperreports/engine/JRStyle;L summaryq ~ [ 	templatest /[Lnet/sf/jasperreports/engine/JRReportTemplate;L titleq ~ L whenNoDataTypeValuet 5Lnet/sf/jasperreports/engine/type/WhenNoDataTypeEnum;xp  wñ            q           n  ¨    #    p~r 1net.sf.jasperreports.engine.type.RunDirectionEnum          xr java.lang.Enum          xpt LTRppppppsr .net.sf.jasperreports.engine.base.JRBaseSection      'Ø [ bandst %[Lnet/sf/jasperreports/engine/JRBand;xpur %[Lnet.sf.jasperreports.engine.JRBand;Ý~ìÊ5  xp   sr +net.sf.jasperreports.engine.base.JRBaseBand      'Ø I PSEUDO_SERIAL_VERSION_UIDI heightZ isSplitAllowedL printWhenExpressiont *Lnet/sf/jasperreports/engine/JRExpression;L 	splitTypet Ljava/lang/Byte;L splitTypeValuet 0Lnet/sf/jasperreports/engine/type/SplitTypeEnum;xr 3net.sf.jasperreports.engine.base.JRBaseElementGroup      'Ø L childrent Ljava/util/List;L elementGroupt ,Lnet/sf/jasperreports/engine/JRElementGroup;xpsr java.util.ArrayListxÒÇa I sizexp   	w   	sr 0net.sf.jasperreports.engine.base.JRBaseSubreport      'Ø L connectionExpressionq ~ L dataSourceExpressionq ~ L 
expressionq ~ L isUsingCachet Ljava/lang/Boolean;[ 
parameterst 3[Lnet/sf/jasperreports/engine/JRSubreportParameter;L parametersMapExpressionq ~ [ returnValuest 5[Lnet/sf/jasperreports/engine/JRSubreportReturnValue;L runToBottomq ~ 'xr .net.sf.jasperreports.engine.base.JRBaseElement      'Ø I PSEUDO_SERIAL_VERSION_UIDI heightZ isPrintInFirstWholeBandZ isPrintRepeatedValuesZ isPrintWhenDetailOverflowsZ isRemoveLineWhenBlankB positionTypeB stretchTypeI widthI xI yL 	backcolort Ljava/awt/Color;L defaultStyleProvidert 4Lnet/sf/jasperreports/engine/JRDefaultStyleProvider;L elementGroupq ~ "L 	forecolorq ~ +L keyq ~ L modeq ~ L 	modeValuet +Lnet/sf/jasperreports/engine/type/ModeEnum;L parentStyleq ~ L parentStyleNameReferenceq ~ L positionTypeValuet 3Lnet/sf/jasperreports/engine/type/PositionTypeEnum;L printWhenExpressionq ~ L printWhenGroupChangest %Lnet/sf/jasperreports/engine/JRGroup;L 
propertiesMapt -Lnet/sf/jasperreports/engine/JRPropertiesMap;[ propertyExpressionst 3[Lnet/sf/jasperreports/engine/JRPropertyExpression;L stretchTypeValuet 2Lnet/sf/jasperreports/engine/type/StretchTypeEnum;xp  wñ   (       q       )pq ~ q ~ #pppppp~r 1net.sf.jasperreports.engine.type.PositionTypeEnum          xq ~ t FIX_RELATIVE_TO_TOPpppp~r 0net.sf.jasperreports.engine.type.StretchTypeEnum          xq ~ t 
NO_STRETCHpsr 1net.sf.jasperreports.engine.base.JRBaseExpression      'Ø I id[ chunkst 0[Lnet/sf/jasperreports/engine/JRExpressionChunk;L valueClassNameq ~ L valueClassRealNameq ~ xp   ur 0[Lnet.sf.jasperreports.engine.JRExpressionChunk;mYÏÞiK£U  xp   sr 6net.sf.jasperreports.engine.base.JRBaseExpressionChunk      'Ø B typeL textq ~ xpt listaConfiguracaot (net.sf.jasperreports.engine.JRDataSourcepsq ~ :   uq ~ =   sq ~ ?t 
SUBREPORT_DIRsq ~ ?t  + "ComissaoConsultor.jasper"t java.lang.Stringppur 3[Lnet.sf.jasperreports.engine.JRSubreportParameter;[£À¾B  xp   sr 9net.sf.jasperreports.engine.base.JRBaseSubreportParameter      'Ø  xr 7net.sf.jasperreports.engine.base.JRBaseDatasetParameter      'Ø L 
expressionq ~ L nameq ~ xpsq ~ :   uq ~ =   sq ~ ?t 
SUBREPORT_DIRt java.lang.Objectpt 
SUBREPORT_DIRsq ~ Lsq ~ :   uq ~ =   sq ~ ?t modoVisualizacaoq ~ Spt modoVisualizacaopppsr 0net.sf.jasperreports.engine.base.JRBaseRectangle      'Ø L radiust Ljava/lang/Integer;xr 5net.sf.jasperreports.engine.base.JRBaseGraphicElement      'Ø I PSEUDO_SERIAL_VERSION_UIDL fillq ~ L 	fillValuet +Lnet/sf/jasperreports/engine/type/FillEnum;L linePent #Lnet/sf/jasperreports/engine/JRPen;L penq ~ xq ~ *  wñ          q       sr java.awt.Color¥3u F falphaI valueL cst Ljava/awt/color/ColorSpace;[ 	frgbvaluet [F[ fvalueq ~ cxp    ÿðïïpppq ~ q ~ #sq ~ a    ÿÿÿÿppppppppq ~ 5ppppq ~ 8  wîppsr *net.sf.jasperreports.engine.base.JRBasePen      'Ø I PSEUDO_SERIAL_VERSION_UIDL 	lineColorq ~ +L 	lineStyleq ~ L lineStyleValuet 0Lnet/sf/jasperreports/engine/type/LineStyleEnum;L 	lineWidtht Ljava/lang/Float;L penContainert ,Lnet/sf/jasperreports/engine/JRPenContainer;xp  wñppppq ~ `ppsr 0net.sf.jasperreports.engine.base.JRBaseTextField      'Ø I PSEUDO_SERIAL_VERSION_UIDI 
bookmarkLevelB evaluationTimeB hyperlinkTargetB 
hyperlinkTypeZ isStretchWithOverflowL anchorNameExpressionq ~ L evaluationGroupq ~ /L evaluationTimeValuet 5Lnet/sf/jasperreports/engine/type/EvaluationTimeEnum;L 
expressionq ~ L hyperlinkAnchorExpressionq ~ L hyperlinkPageExpressionq ~ [ hyperlinkParameterst 3[Lnet/sf/jasperreports/engine/JRHyperlinkParameter;L hyperlinkReferenceExpressionq ~ L hyperlinkTooltipExpressionq ~ L isBlankWhenNullq ~ 'L 
linkTargetq ~ L linkTypeq ~ L patternq ~ xr 2net.sf.jasperreports.engine.base.JRBaseTextElement      'Ø %I PSEUDO_SERIAL_VERSION_UIDL borderq ~ L borderColorq ~ +L bottomBorderq ~ L bottomBorderColorq ~ +L 
bottomPaddingq ~ \L fontNameq ~ L fontSizeq ~ \L horizontalAlignmentq ~ L horizontalAlignmentValuet 6Lnet/sf/jasperreports/engine/type/HorizontalAlignEnum;L isBoldq ~ 'L isItalicq ~ 'L 
isPdfEmbeddedq ~ 'L isStrikeThroughq ~ 'L isStyledTextq ~ 'L isUnderlineq ~ 'L 
leftBorderq ~ L leftBorderColorq ~ +L leftPaddingq ~ \L lineBoxt 'Lnet/sf/jasperreports/engine/JRLineBox;L lineSpacingq ~ L lineSpacingValuet 2Lnet/sf/jasperreports/engine/type/LineSpacingEnum;L markupq ~ L paddingq ~ \L pdfEncodingq ~ L pdfFontNameq ~ L 
reportFontq ~ L rightBorderq ~ L rightBorderColorq ~ +L rightPaddingq ~ \L rotationq ~ L 
rotationValuet /Lnet/sf/jasperreports/engine/type/RotationEnum;L 	topBorderq ~ L topBorderColorq ~ +L 
topPaddingq ~ \L verticalAlignmentq ~ L verticalAlignmentValuet 4Lnet/sf/jasperreports/engine/type/VerticalAlignEnum;xq ~ *  wñ                pq ~ q ~ #ppppppq ~ 5ppppq ~ 8  wñppppppppppppppppppsr .net.sf.jasperreports.engine.base.JRBaseLineBox      'Ø L 
bottomPaddingq ~ \L 	bottomPent +Lnet/sf/jasperreports/engine/base/JRBoxPen;L boxContainert ,Lnet/sf/jasperreports/engine/JRBoxContainer;L leftPaddingq ~ \L leftPenq ~ vL paddingq ~ \L penq ~ vL rightPaddingq ~ \L rightPenq ~ vL 
topPaddingq ~ \L topPenq ~ vxppsr 3net.sf.jasperreports.engine.base.JRBaseBoxBottomPen      'Ø  xr -net.sf.jasperreports.engine.base.JRBaseBoxPen      'Ø L lineBoxq ~ pxq ~ f  wñppppq ~ xq ~ xq ~ tpsr 1net.sf.jasperreports.engine.base.JRBaseBoxLeftPen      'Ø  xq ~ z  wñppppq ~ xq ~ xpsq ~ z  wñppppq ~ xq ~ xpsr 2net.sf.jasperreports.engine.base.JRBaseBoxRightPen      'Ø  xq ~ z  wñppppq ~ xq ~ xpsr 0net.sf.jasperreports.engine.base.JRBaseBoxTopPen      'Ø  xq ~ z  wñppppq ~ xq ~ xpppppppppppppppp~r 2net.sf.jasperreports.engine.type.VerticalAlignEnum          xq ~ t MIDDLE  wñ        pp~r 3net.sf.jasperreports.engine.type.EvaluationTimeEnum          xq ~ t NOWsq ~ :   uq ~ =   sq ~ ?t nomet java.lang.Stringppppppppppsr 1net.sf.jasperreports.engine.base.JRBaseStaticText      'Ø L textq ~ xq ~ n  wñ           (      pq ~ q ~ #ppppppq ~ 5ppppq ~ 8  wñppppppsr java.lang.Integerâ ¤÷8 I valuexr java.lang.Number¬à  xp   pppppppppppsq ~ upsq ~ y  wñppppq ~ q ~ q ~ psq ~ |  wñppppq ~ q ~ psq ~ z  wñppppq ~ q ~ psq ~   wñppppq ~ q ~ psq ~   wñppppq ~ q ~ pppppppppppppppppt 
MatrÃ­culasq ~   wñ           á   _   pq ~ q ~ #ppppppq ~ 5ppppq ~ 8  wñppppppq ~ pppppppppppsq ~ upsq ~ y  wñppppq ~ q ~ q ~ psq ~ |  wñppppq ~ q ~ psq ~ z  wñppppq ~ q ~ psq ~   wñppppq ~ q ~ psq ~   wñppppq ~ q ~ pppppppppppppppppt Nomesq ~   wñ           (  Y   pq ~ q ~ #ppppppq ~ 5ppppq ~ 8  wñppppppq ~ p~r 4net.sf.jasperreports.engine.type.HorizontalAlignEnum          xq ~ t CENTERpppppppppsq ~ upsq ~ y  wñppppq ~ ¦q ~ ¦q ~ ¢psq ~ |  wñppppq ~ ¦q ~ ¦psq ~ z  wñppppq ~ ¦q ~ ¦psq ~   wñppppq ~ ¦q ~ ¦psq ~   wñppppq ~ ¦q ~ ¦pppppppppppppppppt CÃ³digosq ~   wñ                pq ~ q ~ #ppppppq ~ 5ppppq ~ 8  wñppppppq ~ pq ~ ¤pppppppppsq ~ upsq ~ y  wñppppq ~ ®q ~ ®q ~ ­psq ~ |  wñppppq ~ ®q ~ ®psq ~ z  wñppppq ~ ®q ~ ®psq ~   wñppppq ~ ®q ~ ®psq ~   wñppppq ~ ®q ~ ®pppppppppppppppppt N. Parcsq ~   wñ           :  7   pq ~ q ~ #ppppppq ~ 5ppppq ~ 8  wñppppppq ~ p~q ~ £t RIGHTpppppppppsq ~ upsq ~ y  wñppppq ~ ¸q ~ ¸q ~ µpsq ~ |  wñppppq ~ ¸q ~ ¸psq ~ z  wñppppq ~ ¸q ~ ¸psq ~   wñppppq ~ ¸q ~ ¸psq ~   wñppppq ~ ¸q ~ ¸pppppppppppppppppt 	ComissÃ£osq ~ k  wñ           <  Ñ   pq ~ q ~ #ppppppq ~ 5ppppq ~ 8  wñppppppq ~ ppsr java.lang.BooleanÍ rÕúî Z valuexpppppppppsq ~ upsq ~ y  wñppppq ~ Âq ~ Âq ~ ¿psq ~ |  wñppppq ~ Âq ~ Âpsq ~ z  wñppppq ~ Âq ~ Âpsq ~   wñppppq ~ Âq ~ Âpsq ~   wñppppq ~ Âq ~ Âppppppppppppppppp  wñ        ppq ~ sq ~ :   uq ~ =   sq ~ ?t 
"Vl. Total**"t java.lang.Stringppppppppppxp  wñ   Upppsq ~ sq ~ $   w   sq ~ [  wñ          x   ú    sq ~ a    ÿðïïpppq ~ q ~ Ísq ~ a    ÿÿÿÿppppppppq ~ 5ppppq ~ 8  wîppsq ~ f  wñppppq ~ Ïppsq ~ k  wñ           :  7    pq ~ q ~ Íppppppq ~ 5ppppq ~ 8  wñppppppppq ~ ¶q ~ Áq ~ Ápppsq ~ À pppsq ~ upsq ~ y  wñppppq ~ Õq ~ Õq ~ Ópsq ~ |  wñppppq ~ Õq ~ Õpsq ~ z  wñppppq ~ Õq ~ Õpsq ~   wñppppq ~ Õq ~ Õpsq ~   wñppppq ~ Õq ~ Õppppppppppppppppq ~   wñ        ppq ~ sq ~ :   uq ~ =   sq ~ ?t valorTotalComissaoContratost java.lang.Stringppppppppppsq ~ k  wñ           2  Ñ    pq ~ q ~ Íppppppq ~ 5ppppq ~ 8  wñppppppppq ~ ¶q ~ Áq ~ Ápppq ~ Ôpppsq ~ upsq ~ y  wñppppq ~ áq ~ áq ~ àpsq ~ |  wñppppq ~ áq ~ ápsq ~ z  wñppppq ~ áq ~ ápsq ~   wñppppq ~ áq ~ ápsq ~   wñppppq ~ áq ~ áppppppppppppppppq ~   wñ        ppq ~ sq ~ :   uq ~ =   sq ~ ?t valorTotalContratost java.lang.Stringppppppppppsq ~ k  wñ           (  Y    pq ~ q ~ Íppppppq ~ 5ppppq ~ 8  wñppppppppq ~ ¤q ~ Áq ~ Ápppq ~ Ôpppsq ~ upsq ~ y  wñppppq ~ íq ~ íq ~ ìpsq ~ |  wñppppq ~ íq ~ ípsq ~ z  wñppppq ~ íq ~ ípsq ~   wñppppq ~ íq ~ ípsq ~   wñppppq ~ íq ~ íppppppppppppppppq ~   wñ        ppq ~ sq ~ :   uq ~ =   sq ~ ?t qtdContratost java.lang.Integerppppppppppsq ~   wñ           H      pq ~ q ~ Íppppppq ~ 5ppppq ~ 8  wñppppppppq ~ ¶pppppppppsq ~ upsq ~ y  wñppppq ~ ùq ~ ùq ~ øpsq ~ |  wñppppq ~ ùq ~ ùpsq ~ z  wñppppq ~ ùq ~ ùpsq ~   wñppppq ~ ùq ~ ùpsq ~   wñppppq ~ ùq ~ ùppppppppppppppppq ~ t 
Contratos:xp  wñ   pppsq ~ sq ~ $   w   sq ~ [  wñ          x   ú    sq ~ a    ÿðïïpppq ~ q ~ sq ~ a    ÿÿÿÿppppppppq ~ 5ppppq ~ 8  wîppsq ~ f  wñppppq ~ppsq ~ k  wñ           2  Ñ    pq ~ q ~ ppppppq ~ 5ppppq ~ 8  wñppppppppq ~ ¶q ~ Áq ~ Ápppq ~ Ôpppsq ~ upsq ~ y  wñppppq ~q ~q ~psq ~ |  wñppppq ~q ~psq ~ z  wñppppq ~q ~psq ~   wñppppq ~q ~psq ~   wñppppq ~q ~ppppppppppppppppq ~   wñ        ppq ~ sq ~ :   uq ~ =   sq ~ ?t valorTotalProdutost java.lang.Stringppppppppppsq ~ k  wñ           :  7    pq ~ q ~ ppppppq ~ 5ppppq ~ 8  wñppppppppq ~ ¶q ~ Áq ~ Ápppq ~ Ôpppsq ~ upsq ~ y  wñppppq ~q ~q ~psq ~ |  wñppppq ~q ~psq ~ z  wñppppq ~q ~psq ~   wñppppq ~q ~psq ~   wñppppq ~q ~ppppppppppppppppq ~   wñ        ppq ~ sq ~ :    uq ~ =   sq ~ ?t valorTotalComissaoProdutost java.lang.Stringppppppppppsq ~   wñ           H      pq ~ q ~ ppppppq ~ 5ppppq ~ 8  wñppppppppq ~ ¶pppppppppsq ~ upsq ~ y  wñppppq ~q ~q ~psq ~ |  wñppppq ~q ~psq ~ z  wñppppq ~q ~psq ~   wñppppq ~q ~psq ~   wñppppq ~q ~ppppppppppppppppq ~ t 	Produtos:sq ~ k  wñ           (  Y    pq ~ q ~ ppppppq ~ 5ppppq ~ 8  wñppppppppq ~ ¤q ~ Áq ~ Ápppq ~ Ôpppsq ~ upsq ~ y  wñppppq ~'q ~'q ~&psq ~ |  wñppppq ~'q ~'psq ~ z  wñppppq ~'q ~'psq ~   wñppppq ~'q ~'psq ~   wñppppq ~'q ~'ppppppppppppppppq ~   wñ        ppq ~ sq ~ :   !uq ~ =   sq ~ ?t qtdProdutost java.lang.Integerppppppppppxp  wñ   sq ~ :   uq ~ =   sq ~ ?t pagarComissaoProdutossq ~ ?t .equals( true )t java.lang.Booleanpppsq ~ sq ~ $   w   sq ~ [  wñ          x   ú    sq ~ a    ÿðïïpppq ~ q ~9sq ~ a    ÿÿÿÿppppppppq ~ 5ppppq ~ 8  wîppsq ~ f  wñppppq ~;ppsq ~   wñ           H      pq ~ q ~9ppppppq ~ 5ppppq ~ 8  wñppppppppq ~ ¶pppppppppsq ~ upsq ~ y  wñppppq ~@q ~@q ~?psq ~ |  wñppppq ~@q ~@psq ~ z  wñppppq ~@q ~@psq ~   wñppppq ~@q ~@psq ~   wñppppq ~@q ~@ppppppppppppppppq ~ t Total:sq ~ k  wñ           (  Y    pq ~ q ~9ppppppq ~ 5ppppq ~ 8  wñppppppppq ~ ¤q ~ Áq ~ Ápppq ~ Ôpppsq ~ upsq ~ y  wñppppq ~Hq ~Hq ~Gpsq ~ |  wñppppq ~Hq ~Hpsq ~ z  wñppppq ~Hq ~Hpsq ~   wñppppq ~Hq ~Hpsq ~   wñppppq ~Hq ~Hppppppppppppppppq ~   wñ        ppq ~ sq ~ :   #uq ~ =   sq ~ ?t qtdContratossq ~ ?t  + sq ~ ?t qtdProdutost java.lang.Integerppppppppppsq ~ k  wñ           2  Ñ    pq ~ q ~9ppppppq ~ 5ppppq ~ 8  wñppppppppq ~ ¶q ~ Áq ~ Ápppq ~ Ôpppsq ~ upsq ~ y  wñppppq ~Xq ~Xq ~Wpsq ~ |  wñppppq ~Xq ~Xpsq ~ z  wñppppq ~Xq ~Xpsq ~   wñppppq ~Xq ~Xpsq ~   wñppppq ~Xq ~Xppppppppppppppppq ~   wñ        ppq ~ sq ~ :   $uq ~ =   sq ~ ?t 
valorTotalt java.lang.Stringppppppppppsq ~ k  wñ           :  7    pq ~ q ~9ppppppq ~ 5ppppq ~ 8  wñppppppppq ~ ¶q ~ Áq ~ Ápppq ~ Ôpppsq ~ upsq ~ y  wñppppq ~dq ~dq ~cpsq ~ |  wñppppq ~dq ~dpsq ~ z  wñppppq ~dq ~dpsq ~   wñppppq ~dq ~dpsq ~   wñppppq ~dq ~dppppppppppppppppq ~   wñ        ppq ~ sq ~ :   %uq ~ =   sq ~ ?t valorTotalComissaot java.lang.Stringppppppppppxp  wñ   sq ~ :   "uq ~ =   sq ~ ?t pagarComissaoProdutossq ~ ?t .equals( true )q ~8pppppsr java.util.HashSetºD¸·4  xpw   ?@     t "net.sf.jasperreports.engine.data.*t net.sf.jasperreports.engine.*t java.util.*xt javapsr .net.sf.jasperreports.engine.base.JRBaseDataset      'Ø I PSEUDO_SERIAL_VERSION_UIDZ isMainB whenResourceMissingType[ fieldst &[Lnet/sf/jasperreports/engine/JRField;L filterExpressionq ~ [ groupst &[Lnet/sf/jasperreports/engine/JRGroup;L nameq ~ [ 
parameterst *[Lnet/sf/jasperreports/engine/JRParameter;L 
propertiesMapq ~ 0L queryt %Lnet/sf/jasperreports/engine/JRQuery;L resourceBundleq ~ L scriptletClassq ~ [ 
scriptletst *[Lnet/sf/jasperreports/engine/JRScriptlet;[ 
sortFieldst *[Lnet/sf/jasperreports/engine/JRSortField;[ 	variablest )[Lnet/sf/jasperreports/engine/JRVariable;L whenResourceMissingTypeValuet >Lnet/sf/jasperreports/engine/type/WhenResourceMissingTypeEnum;xp  wñ ur &[Lnet.sf.jasperreports.engine.JRField;<ßÇN*òp  xp   sr ,net.sf.jasperreports.engine.base.JRBaseField      'Ø L descriptionq ~ L nameq ~ L 
propertiesMapq ~ 0L valueClassNameq ~ L valueClassRealNameq ~ xppt nomesr +net.sf.jasperreports.engine.JRPropertiesMap      'Ø L baseq ~ 0L propertiesListq ~ !L 
propertiesMapt Ljava/util/Map;xppppt java.lang.Stringpsq ~pt listaConfiguracaosq ~pppt java.lang.Objectpsq ~pt valorTotalComissaosq ~pppt java.lang.Stringpsq ~pt 
valorTotalsq ~pppt java.lang.Stringpsq ~pt codigosq ~pppt java.lang.Integerpsq ~pt qtdContratossq ~pppt java.lang.Integerpsq ~pt qtdProdutossq ~pppt java.lang.Integerpsq ~pt valorTotalContratossq ~pppt java.lang.Stringpsq ~pt valorTotalProdutossq ~pppt java.lang.Stringpsq ~pt valorTotalComissaoContratossq ~pppt java.lang.Stringpsq ~pt valorTotalComissaoProdutossq ~pppt java.lang.Stringpppt ComissaoRelur *[Lnet.sf.jasperreports.engine.JRParameter;" *Ã`!  xp   !sr 0net.sf.jasperreports.engine.base.JRBaseParameter      'Ø 	Z isForPromptingZ isSystemDefinedL defaultValueExpressionq ~ L descriptionq ~ L nameq ~ L nestedTypeNameq ~ L 
propertiesMapq ~ 0L valueClassNameq ~ L valueClassRealNameq ~ xpppt REPORT_PARAMETERS_MAPpsq ~pppt 
java.util.Mappsq ~¹ppt 
JASPER_REPORTpsq ~pppt (net.sf.jasperreports.engine.JasperReportpsq ~¹ppt REPORT_CONNECTIONpsq ~pppt java.sql.Connectionpsq ~¹ppt REPORT_MAX_COUNTpsq ~pppt java.lang.Integerpsq ~¹ppt REPORT_DATA_SOURCEpsq ~pppq ~ Bpsq ~¹ppt REPORT_SCRIPTLETpsq ~pppt /net.sf.jasperreports.engine.JRAbstractScriptletpsq ~¹ppt 
REPORT_LOCALEpsq ~pppt java.util.Localepsq ~¹ppt REPORT_RESOURCE_BUNDLEpsq ~pppt java.util.ResourceBundlepsq ~¹ppt REPORT_TIME_ZONEpsq ~pppt java.util.TimeZonepsq ~¹ppt REPORT_FORMAT_FACTORYpsq ~pppt .net.sf.jasperreports.engine.util.FormatFactorypsq ~¹ppt REPORT_CLASS_LOADERpsq ~pppt java.lang.ClassLoaderpsq ~¹ppt REPORT_URL_HANDLER_FACTORYpsq ~pppt  java.net.URLStreamHandlerFactorypsq ~¹ppt REPORT_FILE_RESOLVERpsq ~pppt -net.sf.jasperreports.engine.util.FileResolverpsq ~¹ppt REPORT_TEMPLATESpsq ~pppt java.util.Collectionpsq ~¹ppt SORT_FIELDSpsq ~pppt java.util.Listpsq ~¹ppt REPORT_VIRTUALIZERpsq ~pppt )net.sf.jasperreports.engine.JRVirtualizerpsq ~¹ppt IS_IGNORE_PAGINATIONpsq ~pppq ~8psq ~¹  ppt tituloRelatoriopsq ~pppt java.lang.Stringpsq ~¹  ppt nomeEmpresapsq ~pppt java.lang.Stringpsq ~¹  ppt usuariopsq ~pppt java.lang.Stringpsq ~¹  ppt filtrospsq ~pppt java.lang.Stringpsq ~¹ sq ~ :    uq ~ =   sq ~ ?t s"D:\\PactoJ\\Desenvolvimento\\Sistemas\\ZillyonWeb\\tronco-novo\\src\\java\\relatorio\\designRelatorio\\contrato\\"t java.lang.Stringppt 
SUBREPORT_DIRpsq ~pppq ~psq ~¹ sq ~ :   uq ~ =   sq ~ ?t p"E:\\desenvolvimentosv\\Projeto Pacto Solucoes\\ZillyonWeb\\src\\java\\relatorio\\designRelatorio\\financeiro\\"t java.lang.Stringppt SUBREPORT_DIR1psq ~pppq ~psq ~¹  ppt logoPadraoRelatoriopsq ~pppt java.io.InputStreampsq ~¹ sq ~ :   uq ~ =   sq ~ ?t p"E:\\desenvolvimentosv\\Projeto Pacto Solucoes\\ZillyonWeb\\src\\java\\relatorio\\designRelatorio\\financeiro\\"t java.lang.Stringppt SUBREPORT_DIR2psq ~pppq ~%psq ~¹ ppt totalpsq ~pppt java.lang.Stringpsq ~¹ ppt modoVisualizacaopsq ~pppt java.lang.Stringpsq ~¹ ppt 	qtdAlunospsq ~pppt java.lang.Integerpsq ~¹ ppt totalGeralPagopsq ~pppt java.lang.Stringpsq ~¹ ppt comissaoMatriculaRematriculapsq ~pppt java.lang.Booleanpsq ~¹ ppt descricaoPeriodopsq ~pppt java.lang.Stringpsq ~¹ ppt qtdContratosTotalpsq ~pppt java.lang.Integerpsq ~¹ ppt pagarComissaoProdutospsq ~pppt java.lang.Booleanpsq ~psq ~ $   w   t ireport.zoomt 	ireport.xt 	ireport.yt ireport.scriptlethandlingt ireport.encodingxsr java.util.HashMapÚÁÃ`Ñ F 
loadFactorI 	thresholdxp?@     w      q ~Jt 1.5q ~Nt 
ISO-8859-1q ~Kt 0q ~Lt 0q ~Mt 0xpppppur )[Lnet.sf.jasperreports.engine.JRVariable;bæ|,·D  xp   sr /net.sf.jasperreports.engine.base.JRBaseVariable      'Ø I PSEUDO_SERIAL_VERSION_UIDB calculationB 
incrementTypeZ isSystemDefinedB 	resetTypeL calculationValuet 2Lnet/sf/jasperreports/engine/type/CalculationEnum;L 
expressionq ~ L incrementGroupq ~ /L incrementTypeValuet 4Lnet/sf/jasperreports/engine/type/IncrementTypeEnum;L incrementerFactoryClassNameq ~ L incrementerFactoryClassRealNameq ~ L initialValueExpressionq ~ L nameq ~ L 
resetGroupq ~ /L resetTypeValuet 0Lnet/sf/jasperreports/engine/type/ResetTypeEnum;L valueClassNameq ~ L valueClassRealNameq ~ xp  wî   ~r 0net.sf.jasperreports.engine.type.CalculationEnum          xq ~ t SYSTEMpp~r 2net.sf.jasperreports.engine.type.IncrementTypeEnum          xq ~ t NONEppsq ~ :   uq ~ =   sq ~ ?t new java.lang.Integer(1)q ~Épt PAGE_NUMBERp~r .net.sf.jasperreports.engine.type.ResetTypeEnum          xq ~ t REPORTq ~Épsq ~X  wî   q ~^ppq ~appsq ~ :   uq ~ =   sq ~ ?t new java.lang.Integer(1)q ~Épt 
COLUMN_NUMBERp~q ~ht PAGEq ~Épsq ~X  wî   ~q ~]t COUNTsq ~ :   uq ~ =   sq ~ ?t new java.lang.Integer(1)q ~Éppq ~appsq ~ :   uq ~ =   sq ~ ?t new java.lang.Integer(0)q ~Épt REPORT_COUNTpq ~iq ~Épsq ~X  wî   q ~tsq ~ :   uq ~ =   sq ~ ?t new java.lang.Integer(1)q ~Éppq ~appsq ~ :   uq ~ =   sq ~ ?t new java.lang.Integer(0)q ~Épt 
PAGE_COUNTpq ~qq ~Épsq ~X  wî   q ~tsq ~ :   	uq ~ =   sq ~ ?t new java.lang.Integer(1)q ~Éppq ~appsq ~ :   
uq ~ =   sq ~ ?t new java.lang.Integer(0)q ~Épt COLUMN_COUNTp~q ~ht COLUMNq ~Épsq ~X  wî    ~q ~]t SUMsq ~ :   uq ~ =   sq ~ ?t qtdContratost java.lang.Integerppq ~apppt 	contratospq ~iq ~psq ~X  wî    q ~sq ~ :   uq ~ =   sq ~ ?t qtdProdutost java.lang.Integerppq ~apppt produtospq ~iq ~£p~r <net.sf.jasperreports.engine.type.WhenResourceMissingTypeEnum          xq ~ t EMPTYq ~¶p~r 0net.sf.jasperreports.engine.type.OrientationEnum          xq ~ t PORTRAITpsq ~ sq ~ $   w   sq ~ k  wñ           x  ù   sq ~ a    ÿÿÿÿpppq ~ q ~«pt 	dataRel-1p~r )net.sf.jasperreports.engine.type.ModeEnum          xq ~ t TRANSPARENTppq ~ 5ppppq ~ 8  wñpppppt Verdanaq ~ pq ~ ¤pq ~ Ôpppppppsq ~ usq ~     sq ~ y  wñsq ~ a    ÿfffpppp~r .net.sf.jasperreports.engine.type.LineStyleEnum          xq ~ t SOLIDsr java.lang.FloatÚíÉ¢Û<ðì F valuexq ~ ?   q ~´q ~´q ~­psq ~ |  wñsq ~ a    ÿfffppppq ~¹sq ~»?   q ~´q ~´psq ~ z  wñppppq ~´q ~´psq ~   wñsq ~ a    ÿfffppppq ~¹sq ~»?   q ~´q ~´psq ~   wñsq ~ a    ÿfffppppq ~¹sq ~»?   q ~´q ~´pppppt 	Helveticappppppppppq ~   wñ        ppq ~ sq ~ :   
uq ~ =   sq ~ ?t 
new Date()t java.util.Dateppppppq ~ Ôppt dd/MM/yyyy HH.mm.sssq ~ k  wñ          ¦   S   pq ~ q ~«pt textField-2ppppq ~ 5ppppq ~ 8  wñpppppt Arialsq ~    pq ~ ¤q ~ Áppppppppsq ~ upsq ~ y  wñsq ~ a    ÿÿÿÿppppppq ~Òq ~Òq ~Îpsq ~ |  wñsq ~ a    ÿÿÿÿppppppq ~Òq ~Òpsq ~ z  wñsq ~ a    ÿÿÿÿppppppq ~Òq ~Òpsq ~   wñsq ~ a    ÿÿÿÿppppppq ~Òq ~Òpsq ~   wñsq ~ a    ÿÿÿÿppppppq ~Òq ~Òpppppt Helvetica-Boldpppppppppp~q ~ t BOTTOM  wñ        ppq ~ sq ~ :   uq ~ =   sq ~ ?t tituloRelatoriot java.lang.Stringppppppq ~ Ôpppsr ,net.sf.jasperreports.engine.base.JRBaseImage      'Ø *I PSEUDO_SERIAL_VERSION_UIDI 
bookmarkLevelB evaluationTimeB hyperlinkTargetB 
hyperlinkTypeZ isLazyB onErrorTypeL anchorNameExpressionq ~ L borderq ~ L borderColorq ~ +L bottomBorderq ~ L bottomBorderColorq ~ +L 
bottomPaddingq ~ \L evaluationGroupq ~ /L evaluationTimeValueq ~ lL 
expressionq ~ L horizontalAlignmentq ~ L horizontalAlignmentValueq ~ oL hyperlinkAnchorExpressionq ~ L hyperlinkPageExpressionq ~ [ hyperlinkParametersq ~ mL hyperlinkReferenceExpressionq ~ L hyperlinkTooltipExpressionq ~ L isUsingCacheq ~ 'L 
leftBorderq ~ L leftBorderColorq ~ +L leftPaddingq ~ \L lineBoxq ~ pL 
linkTargetq ~ L linkTypeq ~ L onErrorTypeValuet 2Lnet/sf/jasperreports/engine/type/OnErrorTypeEnum;L paddingq ~ \L rightBorderq ~ L rightBorderColorq ~ +L rightPaddingq ~ \L 
scaleImageq ~ L scaleImageValuet 1Lnet/sf/jasperreports/engine/type/ScaleImageEnum;L 	topBorderq ~ L topBorderColorq ~ +L 
topPaddingq ~ \L verticalAlignmentq ~ L verticalAlignmentValueq ~ sxq ~ ]  wñ   C       R       pq ~ q ~«pt image-1pppp~q ~ 4t FLOATppppq ~ 8  wîppsq ~ f  wñppppq ~èp  wñ         ppppppp~q ~ t PAGEsq ~ :   uq ~ =   sq ~ ?t logoPadraoRelatoriot java.io.InputStreamppppppppq ~ Ápppsq ~ upsq ~ y  wñsq ~ a    ÿÿÿÿppppppq ~ôq ~ôq ~èpsq ~ |  wñsq ~ a    ÿÿÿÿppppppq ~ôq ~ôpsq ~ z  wñsq ~ a    ÿÿÿÿppppppq ~ôq ~ôpsq ~   wñsq ~ a    ÿÿÿÿppppppq ~ôq ~ôpsq ~   wñsq ~ a    ÿÿÿÿppppppq ~ôq ~ôpp~r 0net.sf.jasperreports.engine.type.OnErrorTypeEnum          xq ~ t BLANKppppp~r /net.sf.jasperreports.engine.type.ScaleImageEnum          xq ~ t 
FILL_FRAMEpppppsq ~ k  wñ           x  ù   
pq ~ q ~«pppq ~±ppq ~ 5ppppq ~ 8  wñpppppt Verdanaq ~ pq ~ ¤pppppppppsq ~ upsq ~ y  wñpp~q ~¸t DOTTEDsq ~»?   q ~q ~q ~psq ~ |  wñppq ~	sq ~»?   q ~q ~psq ~ z  wñppppq ~q ~psq ~   wñppq ~	sq ~»?   q ~q ~psq ~   wñppppq ~q ~ppppppppppppppppq ~   wñ        ppq ~ sq ~ :   uq ~ =   sq ~ ?t 
"UsuÃ¡rio:"+ sq ~ ?t usuariot java.lang.Stringpppppppppt  sq ~ k  wñ           K  ù   pq ~ q ~«ppppppq ~ 5ppppq ~ 8  wñpppppt Verdanaq ~ pq ~ ¶pppppppppsq ~ upsq ~ y  wñppq ~	sq ~»?   q ~q ~q ~psq ~ |  wñppq ~	sq ~»?   q ~q ~psq ~ z  wñppppq ~q ~psq ~   wñppppq ~q ~psq ~   wñppppq ~q ~ppppppppppppppppq ~   wñ        ppq ~ sq ~ :   uq ~ =   sq ~ ?t "PÃ¡gina "+sq ~ ?t PAGE_NUMBERsq ~ ?t +" de"t java.lang.Stringppppppppppsq ~ k  wñ           -  D   pq ~ q ~«ppppppq ~ 5ppppq ~ 8  wñpppppt Verdanaq ~ pppppppppppsq ~ upsq ~ y  wñppq ~	sq ~»?   q ~/q ~/q ~-sq ~    sq ~ |  wñppppq ~/q ~/psq ~ z  wñppppq ~/q ~/psq ~   wñppq ~	sq ~»?   q ~/q ~/psq ~   wñppppq ~/q ~/ppppppppppppppppq ~   wñ        pp~q ~ t REPORTsq ~ :   uq ~ =   sq ~ ?t PAGE_NUMBERt java.lang.Integerppppppppppsq ~ k  wñ         ¦   S   &pq ~ q ~«ppppppq ~ 5ppppq ~ 8  wñpppppt Arialsq ~    
pq ~ ¤pppppppppsq ~ upsq ~ y  wñppppq ~Bq ~Bq ~?psq ~ |  wñppppq ~Bq ~Bpsq ~ z  wñppppq ~Bq ~Bpsq ~   wñppppq ~Bq ~Bpsq ~   wñppppq ~Bq ~Bppppppppppppppppq ~   wñ        ppq ~ sq ~ :   uq ~ =   sq ~ ?t filtrost java.lang.Stringppppppppppsq ~ k  wñ          )      pq ~ q ~«pt 
textField-216ppppq ~ 5ppppq ~ 8  wñpppppt Arialq ~Apq ~ ¤q ~ Ôq ~ Ápppppppsq ~ upsq ~ y  wñsq ~ a    ÿÿÿÿppppppq ~Pq ~Pq ~Mpsq ~ |  wñsq ~ a    ÿÿÿÿppppppq ~Pq ~Ppsq ~ z  wñsq ~ a    ÿÿÿÿppppppq ~Pq ~Ppsq ~   wñsq ~ a    ÿÿÿÿppppppq ~Pq ~Ppsq ~   wñsq ~ a    ÿÿÿÿppppppq ~Pq ~Ppppppt Helvetica-BoldObliqueppppppppppq ~Þ  wñ        ppq ~ sq ~ :   uq ~ =   sq ~ ?t descricaoPeriodot java.lang.Stringppppppq ~ Ôpppxp  wñ   Epp~r .net.sf.jasperreports.engine.type.SplitTypeEnum          xq ~ t STRETCH~r /net.sf.jasperreports.engine.type.PrintOrderEnum          xq ~ t VERTICALpsq ~ sq ~ $   w   sq ~ [  wñ   K       x   ú   sq ~ a    ÿðïïpppq ~ q ~gsq ~ a    ÿÿÿÿppppppppq ~ 5ppppq ~ 8  wîppsq ~ f  wñppppq ~ippsq ~ k  wñ          w   ú   7pq ~ q ~gppppppq ~ 5ppppq ~ 8  wñppppppsq ~    pq ~ ¶q ~ Áppppppppsq ~ upsq ~ y  wñppppq ~oq ~oq ~mpsq ~ |  wñppppq ~oq ~opsq ~ z  wñppppq ~oq ~opsq ~   wñppppq ~oq ~opsq ~   wñppppq ~oq ~oppppppppppppppppq ~   wñ        ppq ~ sq ~ :   &uq ~ =   sq ~ ?t +"Total geral de comissÃµes calculadas: " + sq ~ ?t totalt java.lang.Stringppppppppppsq ~ k  wñ          w   ú   pq ~ q ~gppppppq ~ 5ppppq ~ 8  wñppppppq ~npq ~ ¶q ~ Áppppppppsq ~ upsq ~ y  wñppppq ~}q ~}q ~|psq ~ |  wñppppq ~}q ~}psq ~ z  wñppppq ~}q ~}psq ~   wñppppq ~}q ~}psq ~   wñppppq ~}q ~}ppppppppppppppppq ~   wñ        ppq ~ sq ~ :   'uq ~ =   	sq ~ ?t ""Total de itens na comissÃ£o: " + sq ~ ?t qtdContratosTotalsq ~ ?t  + (sq ~ ?t pagarComissaoProdutossq ~ ?t ? " (Contratos: " + sq ~ ?t 	contratossq ~ ?t +"; Produtos: "+sq ~ ?t produtossq ~ ?t 
+")" : "")t java.lang.Stringppppppppppsq ~ k  wñ          w   ú   pq ~ q ~gppppppq ~ 5ppppq ~ 8  wñppppppq ~npq ~ ¶q ~ Áppppppppsq ~ upsq ~ y  wñppppq ~q ~q ~psq ~ |  wñppppq ~q ~psq ~ z  wñppppq ~q ~psq ~   wñppppq ~q ~psq ~   wñppppq ~q ~ppppppppppppppppq ~   wñ        ppq ~ sq ~ :   (uq ~ =   sq ~ ?t "Total de Geral pago: " + sq ~ ?t totalGeralPagot java.lang.Stringppppppppppsr +net.sf.jasperreports.engine.base.JRBaseLine      'Ø I PSEUDO_SERIAL_VERSION_UIDB 	directionL directionValuet 4Lnet/sf/jasperreports/engine/type/LineDirectionEnum;xq ~ ]  wñ          q       pq ~ q ~gppppppq ~ 5ppppq ~ 8  wîppsq ~ f  wñppppq ~¨p  wñ ~r 2net.sf.jasperreports.engine.type.LineDirectionEnum          xq ~ t TOP_DOWNsq ~ k  wñ           î       pq ~ q ~gppppppq ~ 5pppp~q ~ 7t RELATIVE_TO_BAND_HEIGHT  wñppppppq ~ ppq ~ Áppppppppsq ~ upsq ~ y  wñppppq ~°q ~°q ~­psq ~ |  wñppppq ~°q ~°psq ~ z  wñppppq ~°q ~°psq ~   wñppppq ~°q ~°psq ~   wñppppq ~°q ~°ppppppppppppppppp  wñ       ppq ~ sq ~ :   )uq ~ =   sq ~ ?t "Vl. Total** :" +
(sq ~ ?t comissaoMatriculaRematriculasq ~ ?t f.equals(true) ?
    " Valor do contrato + MatrÃ­cula e RematrÃ­cula." :
    " Valor do Contrato.") +
(sq ~ ?t pagarComissaoProdutossq ~ ?t V.equals(true) ?
    " Caso produto, apresentarÃ¡ o valor total do produto." :
    " ")t java.lang.Stringppppppppppsq ~   wñ           F       pq ~ q ~gppppppq ~ 5ppppq ~ 8  wñpppppppppq ~ Áppppppppsq ~ upsq ~ y  wñppppq ~Äq ~Äq ~Ãpsq ~ |  wñppppq ~Äq ~Äpsq ~ z  wñppppq ~Äq ~Äpsq ~   wñppppq ~Äq ~Äpsq ~   wñppppq ~Äq ~Äpppppppppppppppppt Legenda:sq ~ k  wñ           î       -pq ~ q ~gppppppq ~ 5ppppq ~®  wñppppppq ~ pppppppppppsq ~ upsq ~ y  wñppppq ~Ìq ~Ìq ~Ëpsq ~ |  wñppppq ~Ìq ~Ìpsq ~ z  wñppppq ~Ìq ~Ìpsq ~   wñppppq ~Ìq ~Ìpsq ~   wñppppq ~Ìq ~Ìppppppppppppppppp  wñ       ppq ~ sq ~ :   *uq ~ =   sq ~ ?t #"CÃ³digo: CÃ³digo do contrato." +
(sq ~ ?t pagarComissaoProdutossq ~ ?t U.equals(true) ?
    " Caso produto, apresentarÃ¡ o cÃ³digo do MovProduto." :
    " ")t java.lang.Stringppppppppppxp  wñ   Pppppp~r 3net.sf.jasperreports.engine.type.WhenNoDataTypeEnum          xq ~ t ALL_SECTIONS_NO_DETAILsr 6net.sf.jasperreports.engine.design.JRReportCompileData      'Ø L crosstabCompileDataq ~L datasetCompileDataq ~L mainDatasetCompileDataq ~ xpsq ~O?@     w       xsq ~O?@     w       xur [B¬óøTà  xp  /Êþº¾   .  ComissaoRel_1573501723554_909732  ,net/sf/jasperreports/engine/fill/JREvaluator  parameter_total 2Lnet/sf/jasperreports/engine/fill/JRFillParameter; parameter_qtdContratosTotal parameter_totalGeralPago parameter_JASPER_REPORT parameter_REPORT_TIME_ZONE parameter_usuario parameter_REPORT_FILE_RESOLVER parameter_modoVisualizacao parameter_REPORT_PARAMETERS_MAP parameter_SUBREPORT_DIR1 parameter_REPORT_CLASS_LOADER $parameter_REPORT_URL_HANDLER_FACTORY parameter_REPORT_DATA_SOURCE parameter_IS_IGNORE_PAGINATION parameter_qtdAlunos parameter_SUBREPORT_DIR2 parameter_REPORT_MAX_COUNT parameter_REPORT_TEMPLATES &parameter_comissaoMatriculaRematricula parameter_REPORT_LOCALE parameter_descricaoPeriodo parameter_REPORT_VIRTUALIZER parameter_SORT_FIELDS parameter_logoPadraoRelatorio parameter_REPORT_SCRIPTLET parameter_pagarComissaoProdutos parameter_REPORT_CONNECTION parameter_SUBREPORT_DIR parameter_REPORT_FORMAT_FACTORY parameter_tituloRelatorio parameter_nomeEmpresa  parameter_REPORT_RESOURCE_BUNDLE parameter_filtros field_codigo .Lnet/sf/jasperreports/engine/fill/JRFillField; field_qtdProdutos field_valorTotalProdutos  field_valorTotalComissaoProdutos field_valorTotal field_listaConfiguracao !field_valorTotalComissaoContratos field_valorTotalComissao 
field_nome field_valorTotalContratos field_qtdContratos variable_PAGE_NUMBER 1Lnet/sf/jasperreports/engine/fill/JRFillVariable; variable_COLUMN_NUMBER variable_REPORT_COUNT variable_PAGE_COUNT variable_COLUMN_COUNT variable_contratos variable_produtos <init> ()V Code ; <
  >  	  @  	  B  	  D 	 	  F 
 	  H  	  J  	  L 
 	  N  	  P  	  R  	  T  	  V  	  X  	  Z  	  \  	  ^  	  `  	  b  	  d  	  f  	  h  	  j  	  l  	  n  	  p  	  r   	  t ! 	  v " 	  x # 	  z $ 	  | % 	  ~ & 	   ' (	   ) (	   * (	   + (	   , (	   - (	   . (	   / (	   0 (	   1 (	   2 (	   3 4	   5 4	   6 4	   7 4	   8 4	    9 4	  ¢ : 4	  ¤ LineNumberTable customizedInit 0(Ljava/util/Map;Ljava/util/Map;Ljava/util/Map;)V 
initParams (Ljava/util/Map;)V © ª
  « 
initFields ­ ª
  ® initVars ° ª
  ± total ³ 
java/util/Map µ get &(Ljava/lang/Object;)Ljava/lang/Object; · ¸ ¶ ¹ 0net/sf/jasperreports/engine/fill/JRFillParameter » qtdContratosTotal ½ totalGeralPago ¿ 
JASPER_REPORT Á REPORT_TIME_ZONE Ã usuario Å REPORT_FILE_RESOLVER Ç modoVisualizacao É REPORT_PARAMETERS_MAP Ë SUBREPORT_DIR1 Í REPORT_CLASS_LOADER Ï REPORT_URL_HANDLER_FACTORY Ñ REPORT_DATA_SOURCE Ó IS_IGNORE_PAGINATION Õ 	qtdAlunos × SUBREPORT_DIR2 Ù REPORT_MAX_COUNT Û REPORT_TEMPLATES Ý comissaoMatriculaRematricula ß 
REPORT_LOCALE á descricaoPeriodo ã REPORT_VIRTUALIZER å SORT_FIELDS ç logoPadraoRelatorio é REPORT_SCRIPTLET ë pagarComissaoProdutos í REPORT_CONNECTION ï 
SUBREPORT_DIR ñ REPORT_FORMAT_FACTORY ó tituloRelatorio õ nomeEmpresa ÷ REPORT_RESOURCE_BUNDLE ù filtros û codigo ý ,net/sf/jasperreports/engine/fill/JRFillField ÿ qtdProdutos valorTotalProdutos valorTotalComissaoProdutos 
valorTotal listaConfiguracao	 valorTotalComissaoContratos valorTotalComissao
 nome valorTotalContratos qtdContratos PAGE_NUMBER /net/sf/jasperreports/engine/fill/JRFillVariable 
COLUMN_NUMBER REPORT_COUNT 
PAGE_COUNT COLUMN_COUNT 	contratos! produtos# evaluate (I)Ljava/lang/Object; 
Exceptions java/lang/Throwable( fD:\PactoJ\Desenvolvimento\Sistemas\ZillyonWeb\tronco-novo\src\java\relatorio\designRelatorio\contrato\* eE:\desenvolvimentosv\Projeto Pacto Solucoes\ZillyonWeb\src\java\relatorio\designRelatorio\financeiro\, java/lang/Integer. (I)V ;0
/1 getValue ()Ljava/lang/Object;34
 5 java/util/Date7
8 >
 ¼5 java/lang/String; java/io/InputStream= java/lang/StringBuffer? 	UsuÃ¡rio:A (Ljava/lang/String;)V ;C
@D append ,(Ljava/lang/String;)Ljava/lang/StringBuffer;FG
@H toString ()Ljava/lang/String;JK
@L PÃ¡gina N
5 ,(Ljava/lang/Object;)Ljava/lang/StringBuffer;FQ
@R  deT (net/sf/jasperreports/engine/JRDataSourceV valueOf &(Ljava/lang/Object;)Ljava/lang/String;XY
<Z ComissaoConsultor.jasper\ Vl. Total**^ java/lang/Boolean` (Z)Ljava/lang/Boolean;Xb
ac equals (Ljava/lang/Object;)Zef
ag intValue ()Iij
/k (I)Ljava/lang/Integer;Xm
/n &Total geral de comissÃµes calculadas: p Total de itens na comissÃ£o: r booleanValue ()Ztu
av 
 (Contratos: x ; Produtos: z )|  ~ Total de Geral pago:  
Vl. Total** : / Valor do contrato + MatrÃ­cula e RematrÃ­cula.  Valor do Contrato. 5 Caso produto, apresentarÃ¡ o valor total do produto.   CÃ³digo: CÃ³digo do contrato. 4 Caso produto, apresentarÃ¡ o cÃ³digo do MovProduto. evaluateOld getOldValue4
 
 evaluateEstimated getEstimatedValue4
 
SourceFile !     3                 	     
               
                                                                                                     !     "     #     $     %     &     ' (    ) (    * (    + (    , (    - (    . (    / (    0 (    1 (    2 (    3 4    5 4    6 4    7 4    8 4    9 4    : 4     ; <  =  ì    *· ?*µ A*µ C*µ E*µ G*µ I*µ K*µ M*µ O*µ Q*µ S*µ U*µ W*µ Y*µ [*µ ]*µ _*µ a*µ c*µ e*µ g*µ i*µ k*µ m*µ o*µ q*µ s*µ u*µ w*µ y*µ {*µ }*µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ ¡*µ £*µ ¥±    ¦   Ö 5      	          ! " " ' # , $ 1 % 6 & ; ' @ ( E ) J * O + T , Y - ^ . c / h 0 m 1 r 2 w 3 | 4  5  6  7  8  9  :  ; ¤ < © = ® > ³ ? ¸ @ ½ A Â B Ç C Ì D Ñ E Ö F Û G à H å I ê J ï K ô L ù M þ N   § ¨  =   4     *+· ¬*,· ¯*-· ²±    ¦       Z  [ 
 \  ]  © ª  =  ï    S*+´¹ º À ¼À ¼µ A*+¾¹ º À ¼À ¼µ C*+À¹ º À ¼À ¼µ E*+Â¹ º À ¼À ¼µ G*+Ä¹ º À ¼À ¼µ I*+Æ¹ º À ¼À ¼µ K*+È¹ º À ¼À ¼µ M*+Ê¹ º À ¼À ¼µ O*+Ì¹ º À ¼À ¼µ Q*+Î¹ º À ¼À ¼µ S*+Ð¹ º À ¼À ¼µ U*+Ò¹ º À ¼À ¼µ W*+Ô¹ º À ¼À ¼µ Y*+Ö¹ º À ¼À ¼µ [*+Ø¹ º À ¼À ¼µ ]*+Ú¹ º À ¼À ¼µ _*+Ü¹ º À ¼À ¼µ a*+Þ¹ º À ¼À ¼µ c*+à¹ º À ¼À ¼µ e*+â¹ º À ¼À ¼µ g*+ä¹ º À ¼À ¼µ i*+æ¹ º À ¼À ¼µ k*+è¹ º À ¼À ¼µ m*+ê¹ º À ¼À ¼µ o*+ì¹ º À ¼À ¼µ q*+î¹ º À ¼À ¼µ s*+ð¹ º À ¼À ¼µ u*+ò¹ º À ¼À ¼µ w*+ô¹ º À ¼À ¼µ y*+ö¹ º À ¼À ¼µ {*+ø¹ º À ¼À ¼µ }*+ú¹ º À ¼À ¼µ *+ü¹ º À ¼À ¼µ ±    ¦    "   e  f $ g 6 h H i Z j l k ~ l  m ¢ n ´ o Æ p Ø q ê r ü s t  u2 vD wV xh yz z { |° }Â ~Ô æ ø 
  . @ R   ­ ª  =       Ñ*+þ¹ º À À µ *+¹ º À À µ *+¹ º À À µ *+¹ º À À µ *+¹ º À À µ *+
¹ º À À µ *+¹ º À À µ *+¹ º À À µ *+¹ º À À µ *+¹ º À À µ *+¹ º À À µ ±    ¦   2       %  8  K  ^  q      ª  ½  Ð   ° ª  =   º     *+¹ º ÀÀµ *+¹ º ÀÀµ *+¹ º ÀÀµ *+¹ º ÀÀµ *+ ¹ º ÀÀµ ¡*+"¹ º ÀÀµ £*+$¹ º ÀÀµ ¥±    ¦   "    ¡  ¢ & £ 9 ¤ L ¥ _ ¦ r §  ¨ %& '    ) =  Õ    )Mª  $       *   ¹   À   Ç   Î   Ú   æ   ò   þ  
    "  .  <  J  U  c  q    ³  Á  Ï  Ý  ë  ù    (  6  =  K  Y  g        ©  Á  ã  ñ  ÿ      ¥  ö+M§g-M§`-M§Y»/Y·2M§M»/Y·2M§A»/Y·2M§5»/Y·2M§)»/Y·2M§»/Y·2M§»/Y·2M§»/Y·2M§ù*´ ¶6À/M§ë*´ ¶6À/M§Ý»8Y·9M§Ò*´ {¶:À<M§Ä*´ o¶:À>M§¶»@YB·E*´ K¶:À<¶I¶MM§»@YO·E*´ ¶PÀ/¶SU¶I¶MM§t*´ ¶PÀ/M§f*´ ¶:À<M§X*´ i¶:À<M§J*´ w¶:À<M§<*´ O¶:À<M§.*´ ¶6ÀWM§ »@Y*´ w¶:À<¸[·E]¶I¶MM§ÿ*´ ¶6À<M§ñ_M§ê*´ ¶6À<M§Ü*´ ¶6À<M§Î*´ ¶6À/M§À*´ s¶:Àa¸d¶h¸dM§¨*´ ¶6À<M§*´ ¶6À<M§*´ ¶6À/M§~*´ s¶:Àa¸d¶h¸dM§f*´ ¶6À/¶l*´ ¶6À/¶l`¸oM§D*´ ¶6À<M§6*´ ¶6À<M§(»@Yq·E*´ A¶:À<¶I¶MM§
»@Ys·E*´ C¶:À/¶S*´ s¶:Àa¶w 9»@Yy·E*´ £¶PÀ/¶S{¶I*´ ¥¶PÀ/¶S}¶I¶M§ ¶I¶MM§  »@Y·E*´ E¶:À<¶I¶MM§ »@Y·E*´ e¶:Àa¸d¶h 	§ ¶I*´ s¶:Àa¸d¶h 	§ ¶I¶MM§ 1»@Y·E*´ s¶:Àa¸d¶h 	§ ¶I¶MM,°    ¦   f   °  ² ¼ ¶ À · Ã » Ç ¼ Ê À Î Á Ñ Å Ú Æ Ý Ê æ Ë é Ï ò Ð õ Ô þ Õ Ù
 Ú
 Þ ß ã" ä% è. é1 í< î? òJ óM ÷U øX üc ýfqt³¶ÁÄÏÒÝàë î$ù%ü)*
.(/+36498=9@=K>NBYC\GgHjLMQRVW[©\¬`ÁaÄeãfæjñkôoÿptu yz~¥¨²ÆÌÏÒæìïòöù #' & '    ) =  Õ    )Mª  $       *   ¹   À   Ç   Î   Ú   æ   ò   þ  
    "  .  <  J  U  c  q    ³  Á  Ï  Ý  ë  ù    (  6  =  K  Y  g        ©  Á  ã  ñ  ÿ      ¥  ö+M§g-M§`-M§Y»/Y·2M§M»/Y·2M§A»/Y·2M§5»/Y·2M§)»/Y·2M§»/Y·2M§»/Y·2M§»/Y·2M§ù*´ ¶À/M§ë*´ ¶À/M§Ý»8Y·9M§Ò*´ {¶:À<M§Ä*´ o¶:À>M§¶»@YB·E*´ K¶:À<¶I¶MM§»@YO·E*´ ¶À/¶SU¶I¶MM§t*´ ¶À/M§f*´ ¶:À<M§X*´ i¶:À<M§J*´ w¶:À<M§<*´ O¶:À<M§.*´ ¶ÀWM§ »@Y*´ w¶:À<¸[·E]¶I¶MM§ÿ*´ ¶À<M§ñ_M§ê*´ ¶À<M§Ü*´ ¶À<M§Î*´ ¶À/M§À*´ s¶:Àa¸d¶h¸dM§¨*´ ¶À<M§*´ ¶À<M§*´ ¶À/M§~*´ s¶:Àa¸d¶h¸dM§f*´ ¶À/¶l*´ ¶À/¶l`¸oM§D*´ ¶À<M§6*´ ¶À<M§(»@Yq·E*´ A¶:À<¶I¶MM§
»@Ys·E*´ C¶:À/¶S*´ s¶:Àa¶w 9»@Yy·E*´ £¶À/¶S{¶I*´ ¥¶À/¶S}¶I¶M§ ¶I¶MM§  »@Y·E*´ E¶:À<¶I¶MM§ »@Y·E*´ e¶:Àa¸d¶h 	§ ¶I*´ s¶:Àa¸d¶h 	§ ¶I¶MM§ 1»@Y·E*´ s¶:Àa¸d¶h 	§ ¶I¶MM,°    ¦   f  ¢ ¤ ¼¨ À© Ã­ Ç® Ê² Î³ Ñ· Ú¸ Ý¼ æ½ éÁ òÂ õÆ þÇË
Ì
ÐÑÕ"Ö%Ú.Û1ß<à?äJåMéUêXîcïfóqôtøùý³þ¶ÁÄÏÒÝ
àëîùü
 (!+%6&9*=+@/K0N4Y5\9g:j>?CDHIM©N¬RÁSÄWãXæ\ñ]ôaÿbfg klp¥q¨u²vÆwÌxÏvÒyæzì{ïyòuö|ù #' & '    ) =  Õ    )Mª  $       *   ¹   À   Ç   Î   Ú   æ   ò   þ  
    "  .  <  J  U  c  q    ³  Á  Ï  Ý  ë  ù    (  6  =  K  Y  g        ©  Á  ã  ñ  ÿ      ¥  ö+M§g-M§`-M§Y»/Y·2M§M»/Y·2M§A»/Y·2M§5»/Y·2M§)»/Y·2M§»/Y·2M§»/Y·2M§»/Y·2M§ù*´ ¶6À/M§ë*´ ¶6À/M§Ý»8Y·9M§Ò*´ {¶:À<M§Ä*´ o¶:À>M§¶»@YB·E*´ K¶:À<¶I¶MM§»@YO·E*´ ¶À/¶SU¶I¶MM§t*´ ¶À/M§f*´ ¶:À<M§X*´ i¶:À<M§J*´ w¶:À<M§<*´ O¶:À<M§.*´ ¶6ÀWM§ »@Y*´ w¶:À<¸[·E]¶I¶MM§ÿ*´ ¶6À<M§ñ_M§ê*´ ¶6À<M§Ü*´ ¶6À<M§Î*´ ¶6À/M§À*´ s¶:Àa¸d¶h¸dM§¨*´ ¶6À<M§*´ ¶6À<M§*´ ¶6À/M§~*´ s¶:Àa¸d¶h¸dM§f*´ ¶6À/¶l*´ ¶6À/¶l`¸oM§D*´ ¶6À<M§6*´ ¶6À<M§(»@Yq·E*´ A¶:À<¶I¶MM§
»@Ys·E*´ C¶:À/¶S*´ s¶:Àa¶w 9»@Yy·E*´ £¶À/¶S{¶I*´ ¥¶À/¶S}¶I¶M§ ¶I¶MM§  »@Y·E*´ E¶:À<¶I¶MM§ »@Y·E*´ e¶:Àa¸d¶h 	§ ¶I*´ s¶:Àa¸d¶h 	§ ¶I¶MM§ 1»@Y·E*´ s¶:Àa¸d¶h 	§ ¶I¶MM,°    ¦   f    ¼ À Ã Ç  Ê¤ Î¥ Ñ© Úª Ý® æ¯ é³ ò´ õ¸ þ¹½
¾
ÂÃÇ"È%Ì.Í1Ñ<Ò?ÖJ×MÛUÜXàcáfåqætêëï³ð¶ôÁõÄùÏúÒþÝÿàëîù	ü

(+69=@!K"N&Y'\+g,j0156:;?©@¬DÁEÄIãJæNñOôSÿTXY ]^b¥c¨g²hÆiÌjÏhÒkælìmïkògönùrstu s#r'}     t _1573501723554_909732t 2net.sf.jasperreports.engine.design.JRJavacCompiler
<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="ComissaoConsultorItem" pageWidth="625" pageHeight="823" whenNoDataType="AllSectionsNoDetail" columnWidth="625" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0">
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.encoding" value="UTF-8"/>
	<property name="ireport.zoom" value="2.0"/>
	<property name="ireport.x" value="368"/>
	<property name="ireport.y" value="0"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="java.util.*"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<parameter name="modoVisualizacao" class="java.lang.String"/>
	<field name="matriculaCliente" class="java.lang.String"/>
	<field name="nomePessoa" class="java.lang.String"/>
	<field name="valorDaComissao_apresentar" class="java.lang.String"/>
	<field name="codigoContrato" class="java.lang.Integer"/>
	<field name="tipoContratoApresentar" class="java.lang.String"/>
	<field name="dataPagamento" class="java.util.Date"/>
	<field name="dataCompensacao" class="java.util.Date"/>
	<field name="formaPagamento" class="java.lang.String"/>
	<field name="valor_apresentar" class="java.lang.String"/>
	<field name="nomePlano" class="java.lang.String"/>
	<field name="valorContrato_apresentar" class="java.lang.String"/>
	<field name="responsavelLancamento_apresentar" class="java.lang.String"/>
	<field name="qtdParcelas" class="java.lang.Integer"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band splitType="Stretch"/>
	</title>
	<pageHeader>
		<band splitType="Stretch"/>
	</pageHeader>
	<columnHeader>
		<band splitType="Stretch"/>
	</columnHeader>
	<detail>
		<band height="17" splitType="Immediate">
			<textField isBlankWhenNull="true">
				<reportElement x="30" y="1" width="40" height="15" isRemoveLineWhenBlank="true"/>
				<textElement>
					<font size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{matriculaCliente}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="95" y="1" width="225" height="15" isRemoveLineWhenBlank="true"/>
				<textElement>
					<font size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{nomePessoa}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="345" y="1" width="40" height="15" isRemoveLineWhenBlank="true"/>
				<textElement textAlignment="Center" verticalAlignment="Top">
					<font size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.Integer"><![CDATA[$F{codigoContrato}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="410" y="1" width="30" height="15" isRemoveLineWhenBlank="true"/>
				<textElement textAlignment="Center">
					<font size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.Integer"><![CDATA[$F{qtdParcelas}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="465" y="1" width="50" height="15" isRemoveLineWhenBlank="true"/>
				<textElement textAlignment="Right">
					<font size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{valor_apresentar}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="567" y="1" width="58" height="15" isRemoveLineWhenBlank="true"/>
				<textElement textAlignment="Right">
					<font size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{valorDaComissao_apresentar}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<columnFooter>
		<band splitType="Stretch"/>
	</columnFooter>
	<pageFooter>
		<band splitType="Stretch"/>
	</pageFooter>
	<summary>
		<band splitType="Stretch"/>
	</summary>
</jasperReport>

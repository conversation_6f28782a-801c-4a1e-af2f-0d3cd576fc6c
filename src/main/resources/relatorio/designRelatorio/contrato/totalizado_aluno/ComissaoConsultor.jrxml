<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="ComissaoConsultor" pageWidth="625" pageHeight="823" whenNoDataType="AllSectionsNoDetail" columnWidth="625" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0">
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.encoding" value="UTF-8"/>
	<property name="ireport.zoom" value="0.9330147604194674"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="java.util.*"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<parameter name="modoVisualizacao" class="java.lang.String"/>
	<parameter name="SUBREPORT_DIR" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA["D:\\PactoJ\\Desenvolvimento\\Sistemas\\ZillyonWeb\\tronco-novo\\src\\java\\relatorio\\designRelatorio\\contrato\\"]]></defaultValueExpression>
	</parameter>
	<field name="configuracao_apresentar" class="java.lang.String"/>
	<field name="listaComissoes" class="java.lang.Object"/>
	<field name="valorTotalConfiguracao" class="java.lang.String"/>
	<field name="valorComissaoConfiguracao" class="java.lang.String"/>
	<field name="qtdComissoes" class="java.lang.Integer"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band splitType="Stretch"/>
	</title>
	<pageHeader>
		<band splitType="Stretch"/>
	</pageHeader>
	<detail>
		<band height="45" splitType="Stretch">
			<subreport>
				<reportElement isPrintRepeatedValues="false" x="0" y="20" width="625" height="25" isRemoveLineWhenBlank="true">
					<printWhenExpression><![CDATA[$P{modoVisualizacao}.equals( "A" ) || $P{modoVisualizacao}.equals( "AP" )]]></printWhenExpression>
				</reportElement>
				<subreportParameter name="modoVisualizacao">
					<subreportParameterExpression><![CDATA[$P{modoVisualizacao}]]></subreportParameterExpression>
				</subreportParameter>
				<dataSourceExpression><![CDATA[$F{listaComissoes}]]></dataSourceExpression>
				<subreportExpression class="java.lang.String"><![CDATA[$P{SUBREPORT_DIR} + "ComissaoConsultorItem.jasper"]]></subreportExpression>
			</subreport>
			<textField isStretchWithOverflow="true">
				<reportElement isPrintRepeatedValues="false" x="30" y="0" width="237" height="20" isRemoveLineWhenBlank="true" isPrintWhenDetailOverflows="true"/>
				<textElement verticalAlignment="Middle">
					<font isBold="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{configuracao_apresentar}]]></textFieldExpression>
			</textField>
		</band>
		<band height="20" splitType="Prevent">
			<printWhenExpression><![CDATA[$F{qtdComissoes} > 1]]></printWhenExpression>
			<textField>
				<reportElement x="555" y="0" width="70" height="20"/>
				<textElement textAlignment="Right">
					<font isUnderline="false"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{valorComissaoConfiguracao}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="445" y="0" width="70" height="20"/>
				<textElement textAlignment="Right">
					<font isUnderline="false"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{valorTotalConfiguracao}]]></textFieldExpression>
			</textField>
			<line>
				<reportElement x="450" y="0" width="65" height="1"/>
				<graphicElement>
					<pen lineWidth="0.7"/>
				</graphicElement>
			</line>
			<line>
				<reportElement x="567" y="0" width="58" height="1"/>
				<graphicElement>
					<pen lineWidth="0.7"/>
				</graphicElement>
			</line>
			<line>
				<reportElement x="345" y="0" width="40" height="1"/>
				<graphicElement>
					<pen lineWidth="0.7" lineStyle="Dashed"/>
				</graphicElement>
			</line>
			<textField>
				<reportElement x="345" y="0" width="40" height="20"/>
				<textElement textAlignment="Center">
					<font isUnderline="false"/>
				</textElement>
				<textFieldExpression class="java.lang.Integer"><![CDATA[$F{qtdComissoes}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<pageFooter>
		<band splitType="Stretch"/>
	</pageFooter>
	<summary>
		<band splitType="Stretch"/>
	</summary>
</jasperReport>

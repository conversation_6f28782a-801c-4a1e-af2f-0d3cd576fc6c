<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="FrequenciaAlunos_subReposicoes" pageWidth="594" pageHeight="425" columnWidth="555" leftMargin="0" rightMargin="39" topMargin="0" bottomMargin="0">
	<property name="ireport.zoom" value="3.0"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="32"/>
	<field name="data_Apresentar" class="java.lang.String"/>
	<field name="tipoComoString" class="java.lang.String"/>
	<field name="cliente.pessoa.nome" class="java.lang.String"/>
	<field name="tipo" class="java.lang.Integer"/>
	<variable name="qtdDesmarcadas" class="java.lang.Integer" calculation="Sum">
		<variableExpression><![CDATA[($F{tipo}.intValue() == 0 ? 1 : 0)]]></variableExpression>
	</variable>
	<variable name="qtdReposicoes" class="java.lang.Integer" calculation="Sum">
		<variableExpression><![CDATA[($F{tipo}.intValue() == 1 ? 1 : 0)]]></variableExpression>
	</variable>
	<background>
		<band splitType="Stretch"/>
	</background>
	<columnHeader>
		<band height="48">
			<staticText>
				<reportElement key="staticText-1" positionType="Float" x="333" y="34" width="119" height="14" isPrintInFirstWholeBand="true"/>
				<textElement verticalAlignment="Middle">
					<font size="10" isBold="true" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Dt.Aula]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-1" positionType="Float" x="1" y="34" width="210" height="14" isPrintInFirstWholeBand="true"/>
				<textElement verticalAlignment="Middle">
					<font size="10" isBold="true" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Aluno]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-1" positionType="Float" x="212" y="34" width="116" height="14" isPrintInFirstWholeBand="true"/>
				<textElement verticalAlignment="Middle">
					<font size="10" isBold="true" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Situação]]></text>
			</staticText>
			<rectangle>
				<reportElement x="0" y="10" width="555" height="17" backcolor="#E6E6E6"/>
				<graphicElement>
					<pen lineWidth="0.0"/>
				</graphicElement>
			</rectangle>
			<staticText>
				<reportElement x="0" y="10" width="206" height="17"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[Alterações por Reposição]]></text>
			</staticText>
			<line>
				<reportElement x="0" y="34" width="555" height="2"/>
			</line>
		</band>
	</columnHeader>
	<detail>
		<band height="17" splitType="Stretch">
			<textField>
				<reportElement x="333" y="1" width="119" height="16" isPrintInFirstWholeBand="true"/>
				<textElement verticalAlignment="Middle">
					<font size="10" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{data_Apresentar}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="1" y="1" width="210" height="16" isPrintInFirstWholeBand="true"/>
				<textElement verticalAlignment="Middle">
					<font size="10" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{cliente.pessoa.nome}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="213" y="1" width="116" height="16" isPrintInFirstWholeBand="true"/>
				<textElement verticalAlignment="Middle">
					<font size="10" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{tipoComoString}]]></textFieldExpression>
			</textField>
			<line>
				<reportElement x="0" y="0" width="555" height="1"/>
			</line>
		</band>
	</detail>
	<columnFooter>
		<band height="41">
			<textField>
				<reportElement x="108" y="2" width="65" height="19" isPrintInFirstWholeBand="true"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font size="10" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression class="java.lang.Integer"><![CDATA[$V{qtdDesmarcadas}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="0" y="2" width="108" height="19"/>
				<textElement verticalAlignment="Middle">
					<font size="10" isBold="true"/>
				</textElement>
				<text><![CDATA[Desmarcações:]]></text>
			</staticText>
			<line>
				<reportElement x="0" y="1" width="555" height="1"/>
			</line>
			<textField>
				<reportElement x="108" y="22" width="65" height="19" isPrintInFirstWholeBand="true"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font size="10" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression class="java.lang.Integer"><![CDATA[$V{qtdReposicoes}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="0" y="22" width="108" height="19"/>
				<textElement verticalAlignment="Middle">
					<font size="10" isBold="true"/>
				</textElement>
				<text><![CDATA[Reposições:]]></text>
			</staticText>
		</band>
	</columnFooter>
</jasperReport>

¬í sr (net.sf.jasperreports.engine.JasperReport      'Ø L compileDatat Ljava/io/Serializable;L compileNameSuffixt Ljava/lang/String;L 
compilerClassq ~ xr -net.sf.jasperreports.engine.base.JRBaseReport      'Ø ,I PSEUDO_SERIAL_VERSION_UIDI bottomMarginI columnCountI 
columnSpacingI columnWidthZ ignorePaginationZ isFloatColumnFooterZ isSummaryNewPageZ  isSummaryWithPageHeaderAndFooterZ isTitleNewPageI 
leftMarginB orientationI 
pageHeightI 	pageWidthB 
printOrderI rightMarginI 	topMarginB whenNoDataTypeL 
backgroundt $Lnet/sf/jasperreports/engine/JRBand;L columnDirectiont 3Lnet/sf/jasperreports/engine/type/RunDirectionEnum;L columnFooterq ~ L columnHeaderq ~ [ datasetst ([Lnet/sf/jasperreports/engine/JRDataset;L defaultFontt *Lnet/sf/jasperreports/engine/JRReportFont;L defaultStylet %Lnet/sf/jasperreports/engine/JRStyle;L detailq ~ L 
detailSectiont 'Lnet/sf/jasperreports/engine/JRSection;[ fontst +[Lnet/sf/jasperreports/engine/JRReportFont;L formatFactoryClassq ~ L 
importsSett Ljava/util/Set;L languageq ~ L lastPageFooterq ~ L mainDatasett 'Lnet/sf/jasperreports/engine/JRDataset;L nameq ~ L noDataq ~ L orientationValuet 2Lnet/sf/jasperreports/engine/type/OrientationEnum;L 
pageFooterq ~ L 
pageHeaderq ~ L printOrderValuet 1Lnet/sf/jasperreports/engine/type/PrintOrderEnum;[ stylest &[Lnet/sf/jasperreports/engine/JRStyle;L summaryq ~ [ 	templatest /[Lnet/sf/jasperreports/engine/JRReportTemplate;L titleq ~ L whenNoDataTypeValuet 5Lnet/sf/jasperreports/engine/type/WhenNoDataTypeEnum;xp  wñ            q           n  ¨    #    p~r 1net.sf.jasperreports.engine.type.RunDirectionEnum          xr java.lang.Enum          xpt LTRppppppsr .net.sf.jasperreports.engine.base.JRBaseSection      'Ø [ bandst %[Lnet/sf/jasperreports/engine/JRBand;xpur %[Lnet.sf.jasperreports.engine.JRBand;Ý~ìÊ5  xp   sr +net.sf.jasperreports.engine.base.JRBaseBand      'Ø I PSEUDO_SERIAL_VERSION_UIDI heightZ isSplitAllowedL printWhenExpressiont *Lnet/sf/jasperreports/engine/JRExpression;L 	splitTypet Ljava/lang/Byte;L splitTypeValuet 0Lnet/sf/jasperreports/engine/type/SplitTypeEnum;xr 3net.sf.jasperreports.engine.base.JRBaseElementGroup      'Ø L childrent Ljava/util/List;L elementGroupt ,Lnet/sf/jasperreports/engine/JRElementGroup;xpsr java.util.ArrayListxÒÇa I sizexp   w   sr 0net.sf.jasperreports.engine.base.JRBaseSubreport      'Ø L connectionExpressionq ~ L dataSourceExpressionq ~ L 
expressionq ~ L isUsingCachet Ljava/lang/Boolean;[ 
parameterst 3[Lnet/sf/jasperreports/engine/JRSubreportParameter;L parametersMapExpressionq ~ [ returnValuest 5[Lnet/sf/jasperreports/engine/JRSubreportReturnValue;L runToBottomq ~ 'xr .net.sf.jasperreports.engine.base.JRBaseElement      'Ø I PSEUDO_SERIAL_VERSION_UIDI heightZ isPrintInFirstWholeBandZ isPrintRepeatedValuesZ isPrintWhenDetailOverflowsZ isRemoveLineWhenBlankB positionTypeB stretchTypeI widthI xI yL 	backcolort Ljava/awt/Color;L defaultStyleProvidert 4Lnet/sf/jasperreports/engine/JRDefaultStyleProvider;L elementGroupq ~ "L 	forecolorq ~ +L keyq ~ L modeq ~ L 	modeValuet +Lnet/sf/jasperreports/engine/type/ModeEnum;L parentStyleq ~ L parentStyleNameReferenceq ~ L positionTypeValuet 3Lnet/sf/jasperreports/engine/type/PositionTypeEnum;L printWhenExpressionq ~ L printWhenGroupChangest %Lnet/sf/jasperreports/engine/JRGroup;L 
propertiesMapt -Lnet/sf/jasperreports/engine/JRPropertiesMap;[ propertyExpressionst 3[Lnet/sf/jasperreports/engine/JRPropertyExpression;L stretchTypeValuet 2Lnet/sf/jasperreports/engine/type/StretchTypeEnum;xp  wñ   (       q       )pq ~ q ~ #pppppp~r 1net.sf.jasperreports.engine.type.PositionTypeEnum          xq ~ t FIX_RELATIVE_TO_TOPpppp~r 0net.sf.jasperreports.engine.type.StretchTypeEnum          xq ~ t 
NO_STRETCHpsr 1net.sf.jasperreports.engine.base.JRBaseExpression      'Ø I id[ chunkst 0[Lnet/sf/jasperreports/engine/JRExpressionChunk;L valueClassNameq ~ L valueClassRealNameq ~ xp   ur 0[Lnet.sf.jasperreports.engine.JRExpressionChunk;mYÏÞiK£U  xp   sr 6net.sf.jasperreports.engine.base.JRBaseExpressionChunk      'Ø B typeL textq ~ xpt listaConfiguracaot (net.sf.jasperreports.engine.JRDataSourcepsq ~ :   uq ~ =   sq ~ ?t 
SUBREPORT_DIRsq ~ ?t  + "ComissaoConsultor.jasper"t java.lang.Stringppur 3[Lnet.sf.jasperreports.engine.JRSubreportParameter;[£À¾B  xp   sr 9net.sf.jasperreports.engine.base.JRBaseSubreportParameter      'Ø  xr 7net.sf.jasperreports.engine.base.JRBaseDatasetParameter      'Ø L 
expressionq ~ L nameq ~ xpsq ~ :   uq ~ =   sq ~ ?t 
SUBREPORT_DIRt java.lang.Objectpt 
SUBREPORT_DIRsq ~ Lsq ~ :   uq ~ =   sq ~ ?t modoVisualizacaoq ~ Spt modoVisualizacaopppsr 0net.sf.jasperreports.engine.base.JRBaseRectangle      'Ø L radiust Ljava/lang/Integer;xr 5net.sf.jasperreports.engine.base.JRBaseGraphicElement      'Ø I PSEUDO_SERIAL_VERSION_UIDL fillq ~ L 	fillValuet +Lnet/sf/jasperreports/engine/type/FillEnum;L linePent #Lnet/sf/jasperreports/engine/JRPen;L penq ~ xq ~ *  wñ          q       sr java.awt.Color¥3u F falphaI valueL cst Ljava/awt/color/ColorSpace;[ 	frgbvaluet [F[ fvalueq ~ cxp    ÿðïïpppq ~ q ~ #sq ~ a    ÿÿÿÿppppppppq ~ 5ppppq ~ 8  wîppsr *net.sf.jasperreports.engine.base.JRBasePen      'Ø I PSEUDO_SERIAL_VERSION_UIDL 	lineColorq ~ +L 	lineStyleq ~ L lineStyleValuet 0Lnet/sf/jasperreports/engine/type/LineStyleEnum;L 	lineWidtht Ljava/lang/Float;L penContainert ,Lnet/sf/jasperreports/engine/JRPenContainer;xp  wñppppq ~ `ppsr 0net.sf.jasperreports.engine.base.JRBaseTextField      'Ø I PSEUDO_SERIAL_VERSION_UIDI 
bookmarkLevelB evaluationTimeB hyperlinkTargetB 
hyperlinkTypeZ isStretchWithOverflowL anchorNameExpressionq ~ L evaluationGroupq ~ /L evaluationTimeValuet 5Lnet/sf/jasperreports/engine/type/EvaluationTimeEnum;L 
expressionq ~ L hyperlinkAnchorExpressionq ~ L hyperlinkPageExpressionq ~ [ hyperlinkParameterst 3[Lnet/sf/jasperreports/engine/JRHyperlinkParameter;L hyperlinkReferenceExpressionq ~ L hyperlinkTooltipExpressionq ~ L isBlankWhenNullq ~ 'L 
linkTargetq ~ L linkTypeq ~ L patternq ~ xr 2net.sf.jasperreports.engine.base.JRBaseTextElement      'Ø %I PSEUDO_SERIAL_VERSION_UIDL borderq ~ L borderColorq ~ +L bottomBorderq ~ L bottomBorderColorq ~ +L 
bottomPaddingq ~ \L fontNameq ~ L fontSizeq ~ \L horizontalAlignmentq ~ L horizontalAlignmentValuet 6Lnet/sf/jasperreports/engine/type/HorizontalAlignEnum;L isBoldq ~ 'L isItalicq ~ 'L 
isPdfEmbeddedq ~ 'L isStrikeThroughq ~ 'L isStyledTextq ~ 'L isUnderlineq ~ 'L 
leftBorderq ~ L leftBorderColorq ~ +L leftPaddingq ~ \L lineBoxt 'Lnet/sf/jasperreports/engine/JRLineBox;L lineSpacingq ~ L lineSpacingValuet 2Lnet/sf/jasperreports/engine/type/LineSpacingEnum;L markupq ~ L paddingq ~ \L pdfEncodingq ~ L pdfFontNameq ~ L 
reportFontq ~ L rightBorderq ~ L rightBorderColorq ~ +L rightPaddingq ~ \L rotationq ~ L 
rotationValuet /Lnet/sf/jasperreports/engine/type/RotationEnum;L 	topBorderq ~ L topBorderColorq ~ +L 
topPaddingq ~ \L verticalAlignmentq ~ L verticalAlignmentValuet 4Lnet/sf/jasperreports/engine/type/VerticalAlignEnum;xq ~ *  wñ                pq ~ q ~ #ppppppq ~ 5ppppq ~ 8  wñppppppppppppppppppsr .net.sf.jasperreports.engine.base.JRBaseLineBox      'Ø L 
bottomPaddingq ~ \L 	bottomPent +Lnet/sf/jasperreports/engine/base/JRBoxPen;L boxContainert ,Lnet/sf/jasperreports/engine/JRBoxContainer;L leftPaddingq ~ \L leftPenq ~ vL paddingq ~ \L penq ~ vL rightPaddingq ~ \L rightPenq ~ vL 
topPaddingq ~ \L topPenq ~ vxppsr 3net.sf.jasperreports.engine.base.JRBaseBoxBottomPen      'Ø  xr -net.sf.jasperreports.engine.base.JRBaseBoxPen      'Ø L lineBoxq ~ pxq ~ f  wñppppq ~ xq ~ xq ~ tpsr 1net.sf.jasperreports.engine.base.JRBaseBoxLeftPen      'Ø  xq ~ z  wñppppq ~ xq ~ xpsq ~ z  wñppppq ~ xq ~ xpsr 2net.sf.jasperreports.engine.base.JRBaseBoxRightPen      'Ø  xq ~ z  wñppppq ~ xq ~ xpsr 0net.sf.jasperreports.engine.base.JRBaseBoxTopPen      'Ø  xq ~ z  wñppppq ~ xq ~ xpppppppppppppppp~r 2net.sf.jasperreports.engine.type.VerticalAlignEnum          xq ~ t MIDDLE  wñ        pp~r 3net.sf.jasperreports.engine.type.EvaluationTimeEnum          xq ~ t NOWsq ~ :   uq ~ =   sq ~ ?t nomet java.lang.Stringppppppppppsr 1net.sf.jasperreports.engine.base.JRBaseStaticText      'Ø L textq ~ xq ~ n  wñ           (      pq ~ q ~ #ppppppq ~ 5ppppq ~ 8  wñppppppsr java.lang.Integerâ ¤÷8 I valuexr java.lang.Number¬à  xp   pppppppppppsq ~ upsq ~ y  wñppppq ~ q ~ q ~ psq ~ |  wñppppq ~ q ~ psq ~ z  wñppppq ~ q ~ psq ~   wñppppq ~ q ~ psq ~   wñppppq ~ q ~ pppppppppppppppppt 
MatrÃ­culasq ~   wñ           á   _   pq ~ q ~ #ppppppq ~ 5ppppq ~ 8  wñppppppq ~ pppppppppppsq ~ upsq ~ y  wñppppq ~ q ~ q ~ psq ~ |  wñppppq ~ q ~ psq ~ z  wñppppq ~ q ~ psq ~   wñppppq ~ q ~ psq ~   wñppppq ~ q ~ pppppppppppppppppt Nomesq ~   wñ           (  Y   pq ~ q ~ #ppppppq ~ 5ppppq ~ 8  wñppppppq ~ p~r 4net.sf.jasperreports.engine.type.HorizontalAlignEnum          xq ~ t CENTERpppppppppsq ~ upsq ~ y  wñppppq ~ ¦q ~ ¦q ~ ¢psq ~ |  wñppppq ~ ¦q ~ ¦psq ~ z  wñppppq ~ ¦q ~ ¦psq ~   wñppppq ~ ¦q ~ ¦psq ~   wñppppq ~ ¦q ~ ¦pppppppppppppppppt CÃ³digosq ~   wñ           :  7   pq ~ q ~ #ppppppq ~ 5ppppq ~ 8  wñppppppq ~ p~q ~ £t RIGHTpppppppppsq ~ upsq ~ y  wñppppq ~ °q ~ °q ~ ­psq ~ |  wñppppq ~ °q ~ °psq ~ z  wñppppq ~ °q ~ °psq ~   wñppppq ~ °q ~ °psq ~   wñppppq ~ °q ~ °pppppppppppppppppt 	ComissÃ£osq ~ k  wñ           <  Ñ   pq ~ q ~ #ppppppq ~ 5ppppq ~ 8  wñppppppq ~ ppsr java.lang.BooleanÍ rÕúî Z valuexpppppppppsq ~ upsq ~ y  wñppppq ~ ºq ~ ºq ~ ·psq ~ |  wñppppq ~ ºq ~ ºpsq ~ z  wñppppq ~ ºq ~ ºpsq ~   wñppppq ~ ºq ~ ºpsq ~   wñppppq ~ ºq ~ ºppppppppppppppppp  wñ        ppq ~ sq ~ :   uq ~ =   sq ~ ?t 
"Vl. Total**"t java.lang.Stringppppppppppxp  wñ   Upppsq ~ sq ~ $   w   sq ~ [  wñ          x   ú    sq ~ a    ÿðïïpppq ~ q ~ Åsq ~ a    ÿÿÿÿppppppppq ~ 5ppppq ~ 8  wîppsq ~ f  wñppppq ~ Çppsq ~ k  wñ           :  7    pq ~ q ~ Åppppppq ~ 5ppppq ~ 8  wñppppppppq ~ ®q ~ ¹q ~ ¹pppsq ~ ¸ pppsq ~ upsq ~ y  wñppppq ~ Íq ~ Íq ~ Ëpsq ~ |  wñppppq ~ Íq ~ Ípsq ~ z  wñppppq ~ Íq ~ Ípsq ~   wñppppq ~ Íq ~ Ípsq ~   wñppppq ~ Íq ~ Íppppppppppppppppq ~   wñ        ppq ~ sq ~ :   uq ~ =   sq ~ ?t valorTotalComissaoContratost java.lang.Stringppppppppppsq ~ k  wñ           2  Ñ    pq ~ q ~ Åppppppq ~ 5ppppq ~ 8  wñppppppppq ~ ®q ~ ¹q ~ ¹pppq ~ Ìpppsq ~ upsq ~ y  wñppppq ~ Ùq ~ Ùq ~ Øpsq ~ |  wñppppq ~ Ùq ~ Ùpsq ~ z  wñppppq ~ Ùq ~ Ùpsq ~   wñppppq ~ Ùq ~ Ùpsq ~   wñppppq ~ Ùq ~ Ùppppppppppppppppq ~   wñ        ppq ~ sq ~ :   uq ~ =   sq ~ ?t valorTotalContratost java.lang.Stringppppppppppsq ~ k  wñ           (  Y    pq ~ q ~ Åppppppq ~ 5ppppq ~ 8  wñppppppppq ~ ¤q ~ ¹q ~ ¹pppq ~ Ìpppsq ~ upsq ~ y  wñppppq ~ åq ~ åq ~ äpsq ~ |  wñppppq ~ åq ~ åpsq ~ z  wñppppq ~ åq ~ åpsq ~   wñppppq ~ åq ~ åpsq ~   wñppppq ~ åq ~ åppppppppppppppppq ~   wñ        ppq ~ sq ~ :   uq ~ =   sq ~ ?t qtdContratost java.lang.Integerppppppppppsq ~   wñ           H      pq ~ q ~ Åppppppq ~ 5ppppq ~ 8  wñppppppppq ~ ®pppppppppsq ~ upsq ~ y  wñppppq ~ ñq ~ ñq ~ ðpsq ~ |  wñppppq ~ ñq ~ ñpsq ~ z  wñppppq ~ ñq ~ ñpsq ~   wñppppq ~ ñq ~ ñpsq ~   wñppppq ~ ñq ~ ñppppppppppppppppq ~ t 
Contratos:xp  wñ   pppsq ~ sq ~ $   w   sq ~ [  wñ          x   ú    sq ~ a    ÿðïïpppq ~ q ~ øsq ~ a    ÿÿÿÿppppppppq ~ 5ppppq ~ 8  wîppsq ~ f  wñppppq ~ úppsq ~ k  wñ           2  Ñ    pq ~ q ~ øppppppq ~ 5ppppq ~ 8  wñppppppppq ~ ®q ~ ¹q ~ ¹pppq ~ Ìpppsq ~ upsq ~ y  wñppppq ~ ÿq ~ ÿq ~ þpsq ~ |  wñppppq ~ ÿq ~ ÿpsq ~ z  wñppppq ~ ÿq ~ ÿpsq ~   wñppppq ~ ÿq ~ ÿpsq ~   wñppppq ~ ÿq ~ ÿppppppppppppppppq ~   wñ        ppq ~ sq ~ :   uq ~ =   sq ~ ?t valorTotalProdutost java.lang.Stringppppppppppsq ~ k  wñ           :  7    pq ~ q ~ øppppppq ~ 5ppppq ~ 8  wñppppppppq ~ ®q ~ ¹q ~ ¹pppq ~ Ìpppsq ~ upsq ~ y  wñppppq ~q ~q ~
psq ~ |  wñppppq ~q ~psq ~ z  wñppppq ~q ~psq ~   wñppppq ~q ~psq ~   wñppppq ~q ~ppppppppppppppppq ~   wñ        ppq ~ sq ~ :   uq ~ =   sq ~ ?t valorTotalComissaoProdutost java.lang.Stringppppppppppsq ~   wñ           H      pq ~ q ~ øppppppq ~ 5ppppq ~ 8  wñppppppppq ~ ®pppppppppsq ~ upsq ~ y  wñppppq ~q ~q ~psq ~ |  wñppppq ~q ~psq ~ z  wñppppq ~q ~psq ~   wñppppq ~q ~psq ~   wñppppq ~q ~ppppppppppppppppq ~ t 	Produtos:sq ~ k  wñ           (  Y    pq ~ q ~ øppppppq ~ 5ppppq ~ 8  wñppppppppq ~ ¤q ~ ¹q ~ ¹pppq ~ Ìpppsq ~ upsq ~ y  wñppppq ~q ~q ~psq ~ |  wñppppq ~q ~psq ~ z  wñppppq ~q ~psq ~   wñppppq ~q ~psq ~   wñppppq ~q ~ppppppppppppppppq ~   wñ        ppq ~ sq ~ :    uq ~ =   sq ~ ?t qtdProdutost java.lang.Integerppppppppppxp  wñ   pppsq ~ sq ~ $   w   sq ~ [  wñ          x   ú    sq ~ a    ÿðïïpppq ~ q ~*sq ~ a    ÿÿÿÿppppppppq ~ 5ppppq ~ 8  wîppsq ~ f  wñppppq ~,ppsq ~   wñ           H      pq ~ q ~*ppppppq ~ 5ppppq ~ 8  wñppppppppq ~ ®pppppppppsq ~ upsq ~ y  wñppppq ~1q ~1q ~0psq ~ |  wñppppq ~1q ~1psq ~ z  wñppppq ~1q ~1psq ~   wñppppq ~1q ~1psq ~   wñppppq ~1q ~1ppppppppppppppppq ~ t Total:sq ~ k  wñ           (  Y    pq ~ q ~*ppppppq ~ 5ppppq ~ 8  wñppppppppq ~ ¤q ~ ¹q ~ ¹pppq ~ Ìpppsq ~ upsq ~ y  wñppppq ~9q ~9q ~8psq ~ |  wñppppq ~9q ~9psq ~ z  wñppppq ~9q ~9psq ~   wñppppq ~9q ~9psq ~   wñppppq ~9q ~9ppppppppppppppppq ~   wñ        ppq ~ sq ~ :   "uq ~ =   sq ~ ?t qtdContratossq ~ ?t  + sq ~ ?t qtdProdutost java.lang.Integerppppppppppsq ~ k  wñ           2  Ñ    pq ~ q ~*ppppppq ~ 5ppppq ~ 8  wñppppppppq ~ ®q ~ ¹q ~ ¹pppq ~ Ìpppsq ~ upsq ~ y  wñppppq ~Iq ~Iq ~Hpsq ~ |  wñppppq ~Iq ~Ipsq ~ z  wñppppq ~Iq ~Ipsq ~   wñppppq ~Iq ~Ipsq ~   wñppppq ~Iq ~Ippppppppppppppppq ~   wñ        ppq ~ sq ~ :   #uq ~ =   sq ~ ?t 
valorTotalt java.lang.Stringppppppppppsq ~ k  wñ           :  7    pq ~ q ~*ppppppq ~ 5ppppq ~ 8  wñppppppppq ~ ®q ~ ¹q ~ ¹pppq ~ Ìpppsq ~ upsq ~ y  wñppppq ~Uq ~Uq ~Tpsq ~ |  wñppppq ~Uq ~Upsq ~ z  wñppppq ~Uq ~Upsq ~   wñppppq ~Uq ~Upsq ~   wñppppq ~Uq ~Uppppppppppppppppq ~   wñ        ppq ~ sq ~ :   $uq ~ =   sq ~ ?t valorTotalComissaot java.lang.Stringppppppppppxp  wñ   sq ~ :   !uq ~ =   sq ~ ?t pagarComissaoProdutossq ~ ?t .equals( true )t java.lang.Booleanpppppsr java.util.HashSetºD¸·4  xpw   ?@     t "net.sf.jasperreports.engine.data.*t net.sf.jasperreports.engine.*t java.util.*xt javapsr .net.sf.jasperreports.engine.base.JRBaseDataset      'Ø I PSEUDO_SERIAL_VERSION_UIDZ isMainB whenResourceMissingType[ fieldst &[Lnet/sf/jasperreports/engine/JRField;L filterExpressionq ~ [ groupst &[Lnet/sf/jasperreports/engine/JRGroup;L nameq ~ [ 
parameterst *[Lnet/sf/jasperreports/engine/JRParameter;L 
propertiesMapq ~ 0L queryt %Lnet/sf/jasperreports/engine/JRQuery;L resourceBundleq ~ L scriptletClassq ~ [ 
scriptletst *[Lnet/sf/jasperreports/engine/JRScriptlet;[ 
sortFieldst *[Lnet/sf/jasperreports/engine/JRSortField;[ 	variablest )[Lnet/sf/jasperreports/engine/JRVariable;L whenResourceMissingTypeValuet >Lnet/sf/jasperreports/engine/type/WhenResourceMissingTypeEnum;xp  wñ ur &[Lnet.sf.jasperreports.engine.JRField;<ßÇN*òp  xp   sr ,net.sf.jasperreports.engine.base.JRBaseField      'Ø L descriptionq ~ L nameq ~ L 
propertiesMapq ~ 0L valueClassNameq ~ L valueClassRealNameq ~ xppt nomesr +net.sf.jasperreports.engine.JRPropertiesMap      'Ø L baseq ~ 0L propertiesListq ~ !L 
propertiesMapt Ljava/util/Map;xppppt java.lang.Stringpsq ~ypt listaConfiguracaosq ~|pppt java.lang.Objectpsq ~ypt valorTotalComissaosq ~|pppt java.lang.Stringpsq ~ypt 
valorTotalsq ~|pppt java.lang.Stringpsq ~ypt codigosq ~|pppt java.lang.Integerpsq ~ypt qtdContratossq ~|pppt java.lang.Integerpsq ~ypt qtdProdutossq ~|pppt java.lang.Integerpsq ~ypt valorTotalContratossq ~|pppt java.lang.Stringpsq ~ypt valorTotalProdutossq ~|pppt java.lang.Stringpsq ~ypt valorTotalComissaoContratossq ~|pppt java.lang.Stringpsq ~ypt valorTotalComissaoProdutossq ~|pppt java.lang.Stringpppt ComissaoRelur *[Lnet.sf.jasperreports.engine.JRParameter;" *Ã`!  xp   !sr 0net.sf.jasperreports.engine.base.JRBaseParameter      'Ø 	Z isForPromptingZ isSystemDefinedL defaultValueExpressionq ~ L descriptionq ~ L nameq ~ L nestedTypeNameq ~ L 
propertiesMapq ~ 0L valueClassNameq ~ L valueClassRealNameq ~ xpppt REPORT_PARAMETERS_MAPpsq ~|pppt 
java.util.Mappsq ~«ppt 
JASPER_REPORTpsq ~|pppt (net.sf.jasperreports.engine.JasperReportpsq ~«ppt REPORT_CONNECTIONpsq ~|pppt java.sql.Connectionpsq ~«ppt REPORT_MAX_COUNTpsq ~|pppt java.lang.Integerpsq ~«ppt REPORT_DATA_SOURCEpsq ~|pppq ~ Bpsq ~«ppt REPORT_SCRIPTLETpsq ~|pppt /net.sf.jasperreports.engine.JRAbstractScriptletpsq ~«ppt 
REPORT_LOCALEpsq ~|pppt java.util.Localepsq ~«ppt REPORT_RESOURCE_BUNDLEpsq ~|pppt java.util.ResourceBundlepsq ~«ppt REPORT_TIME_ZONEpsq ~|pppt java.util.TimeZonepsq ~«ppt REPORT_FORMAT_FACTORYpsq ~|pppt .net.sf.jasperreports.engine.util.FormatFactorypsq ~«ppt REPORT_CLASS_LOADERpsq ~|pppt java.lang.ClassLoaderpsq ~«ppt REPORT_URL_HANDLER_FACTORYpsq ~|pppt  java.net.URLStreamHandlerFactorypsq ~«ppt REPORT_FILE_RESOLVERpsq ~|pppt -net.sf.jasperreports.engine.util.FileResolverpsq ~«ppt REPORT_TEMPLATESpsq ~|pppt java.util.Collectionpsq ~«ppt SORT_FIELDSpsq ~|pppt java.util.Listpsq ~«ppt REPORT_VIRTUALIZERpsq ~|pppt )net.sf.jasperreports.engine.JRVirtualizerpsq ~«ppt IS_IGNORE_PAGINATIONpsq ~|pppq ~fpsq ~«  ppt tituloRelatoriopsq ~|pppt java.lang.Stringpsq ~«  ppt nomeEmpresapsq ~|pppt java.lang.Stringpsq ~«  ppt usuariopsq ~|pppt java.lang.Stringpsq ~«  ppt filtrospsq ~|pppt java.lang.Stringpsq ~« sq ~ :    uq ~ =   sq ~ ?t s"D:\\PactoJ\\Desenvolvimento\\Sistemas\\ZillyonWeb\\tronco-novo\\src\\java\\relatorio\\designRelatorio\\contrato\\"t java.lang.Stringppt 
SUBREPORT_DIRpsq ~|pppq ~psq ~« sq ~ :   uq ~ =   sq ~ ?t p"E:\\desenvolvimentosv\\Projeto Pacto Solucoes\\ZillyonWeb\\src\\java\\relatorio\\designRelatorio\\financeiro\\"t java.lang.Stringppt SUBREPORT_DIR1psq ~|pppq ~psq ~«  ppt logoPadraoRelatoriopsq ~|pppt java.io.InputStreampsq ~« sq ~ :   uq ~ =   sq ~ ?t p"E:\\desenvolvimentosv\\Projeto Pacto Solucoes\\ZillyonWeb\\src\\java\\relatorio\\designRelatorio\\financeiro\\"t java.lang.Stringppt SUBREPORT_DIR2psq ~|pppq ~psq ~« ppt totalpsq ~|pppt java.lang.Stringpsq ~« ppt modoVisualizacaopsq ~|pppt java.lang.Stringpsq ~« ppt 	qtdAlunospsq ~|pppt java.lang.Integerpsq ~« ppt totalGeralPagopsq ~|pppt java.lang.Stringpsq ~« ppt comissaoMatriculaRematriculapsq ~|pppt java.lang.Booleanpsq ~« ppt qtdContratosTotalpsq ~|pppt java.lang.Integerpsq ~« ppt descricaoPeriodopsq ~|pppt java.lang.Stringpsq ~« ppt pagarComissaoProdutospsq ~|pppt java.lang.Booleanpsq ~|psq ~ $   w   t ireport.zoomt 	ireport.xt 	ireport.yt ireport.scriptlethandlingt ireport.encodingxsr java.util.HashMapÚÁÃ`Ñ F 
loadFactorI 	thresholdxp?@     w      q ~<t 1.3286707500000006q ~@t 
ISO-8859-1q ~=t 0q ~>t 0q ~?t 0xpppppur )[Lnet.sf.jasperreports.engine.JRVariable;bæ|,·D  xp   sr /net.sf.jasperreports.engine.base.JRBaseVariable      'Ø I PSEUDO_SERIAL_VERSION_UIDB calculationB 
incrementTypeZ isSystemDefinedB 	resetTypeL calculationValuet 2Lnet/sf/jasperreports/engine/type/CalculationEnum;L 
expressionq ~ L incrementGroupq ~ /L incrementTypeValuet 4Lnet/sf/jasperreports/engine/type/IncrementTypeEnum;L incrementerFactoryClassNameq ~ L incrementerFactoryClassRealNameq ~ L initialValueExpressionq ~ L nameq ~ L 
resetGroupq ~ /L resetTypeValuet 0Lnet/sf/jasperreports/engine/type/ResetTypeEnum;L valueClassNameq ~ L valueClassRealNameq ~ xp  wî   ~r 0net.sf.jasperreports.engine.type.CalculationEnum          xq ~ t SYSTEMpp~r 2net.sf.jasperreports.engine.type.IncrementTypeEnum          xq ~ t NONEppsq ~ :   uq ~ =   sq ~ ?t new java.lang.Integer(1)q ~»pt PAGE_NUMBERp~r .net.sf.jasperreports.engine.type.ResetTypeEnum          xq ~ t REPORTq ~»psq ~J  wî   q ~Pppq ~Sppsq ~ :   uq ~ =   sq ~ ?t new java.lang.Integer(1)q ~»pt 
COLUMN_NUMBERp~q ~Zt PAGEq ~»psq ~J  wî   ~q ~Ot COUNTsq ~ :   uq ~ =   sq ~ ?t new java.lang.Integer(1)q ~»ppq ~Sppsq ~ :   uq ~ =   sq ~ ?t new java.lang.Integer(0)q ~»pt REPORT_COUNTpq ~[q ~»psq ~J  wî   q ~fsq ~ :   uq ~ =   sq ~ ?t new java.lang.Integer(1)q ~»ppq ~Sppsq ~ :   uq ~ =   sq ~ ?t new java.lang.Integer(0)q ~»pt 
PAGE_COUNTpq ~cq ~»psq ~J  wî   q ~fsq ~ :   	uq ~ =   sq ~ ?t new java.lang.Integer(1)q ~»ppq ~Sppsq ~ :   
uq ~ =   sq ~ ?t new java.lang.Integer(0)q ~»pt COLUMN_COUNTp~q ~Zt COLUMNq ~»psq ~J  wî    ~q ~Ot SUMsq ~ :   uq ~ =   sq ~ ?t qtdContratost java.lang.Integerppq ~Spppt 	contratospq ~[q ~psq ~J  wî    q ~sq ~ :   uq ~ =   sq ~ ?t qtdProdutost java.lang.Integerppq ~Spppt produtospq ~[q ~p~r <net.sf.jasperreports.engine.type.WhenResourceMissingTypeEnum          xq ~ t EMPTYq ~¨p~r 0net.sf.jasperreports.engine.type.OrientationEnum          xq ~ t PORTRAITpsq ~ sq ~ $   w   sq ~ k  wñ           x  ù   sq ~ a    ÿÿÿÿpppq ~ q ~pt 	dataRel-1p~r )net.sf.jasperreports.engine.type.ModeEnum          xq ~ t TRANSPARENTppq ~ 5ppppq ~ 8  wñpppppt Verdanaq ~ pq ~ ¤pq ~ Ìpppppppsq ~ usq ~     sq ~ y  wñsq ~ a    ÿfffpppp~r .net.sf.jasperreports.engine.type.LineStyleEnum          xq ~ t SOLIDsr java.lang.FloatÚíÉ¢Û<ðì F valuexq ~ ?   q ~¦q ~¦q ~psq ~ |  wñsq ~ a    ÿfffppppq ~«sq ~­?   q ~¦q ~¦psq ~ z  wñppppq ~¦q ~¦psq ~   wñsq ~ a    ÿfffppppq ~«sq ~­?   q ~¦q ~¦psq ~   wñsq ~ a    ÿfffppppq ~«sq ~­?   q ~¦q ~¦pppppt 	Helveticappppppppppq ~   wñ        ppq ~ sq ~ :   
uq ~ =   sq ~ ?t 
new Date()t java.util.Dateppppppq ~ Ìppt dd/MM/yyyy HH.mm.sssq ~ k  wñ          ¦   S   pq ~ q ~pt textField-2ppppq ~ 5ppppq ~ 8  wñpppppt Arialsq ~    pq ~ ¤q ~ ¹ppppppppsq ~ upsq ~ y  wñsq ~ a    ÿÿÿÿppppppq ~Äq ~Äq ~Àpsq ~ |  wñsq ~ a    ÿÿÿÿppppppq ~Äq ~Äpsq ~ z  wñsq ~ a    ÿÿÿÿppppppq ~Äq ~Äpsq ~   wñsq ~ a    ÿÿÿÿppppppq ~Äq ~Äpsq ~   wñsq ~ a    ÿÿÿÿppppppq ~Äq ~Äpppppt Helvetica-Boldpppppppppp~q ~ t BOTTOM  wñ        ppq ~ sq ~ :   uq ~ =   sq ~ ?t tituloRelatoriot java.lang.Stringppppppq ~ Ìpppsr ,net.sf.jasperreports.engine.base.JRBaseImage      'Ø *I PSEUDO_SERIAL_VERSION_UIDI 
bookmarkLevelB evaluationTimeB hyperlinkTargetB 
hyperlinkTypeZ isLazyB onErrorTypeL anchorNameExpressionq ~ L borderq ~ L borderColorq ~ +L bottomBorderq ~ L bottomBorderColorq ~ +L 
bottomPaddingq ~ \L evaluationGroupq ~ /L evaluationTimeValueq ~ lL 
expressionq ~ L horizontalAlignmentq ~ L horizontalAlignmentValueq ~ oL hyperlinkAnchorExpressionq ~ L hyperlinkPageExpressionq ~ [ hyperlinkParametersq ~ mL hyperlinkReferenceExpressionq ~ L hyperlinkTooltipExpressionq ~ L isUsingCacheq ~ 'L 
leftBorderq ~ L leftBorderColorq ~ +L leftPaddingq ~ \L lineBoxq ~ pL 
linkTargetq ~ L linkTypeq ~ L onErrorTypeValuet 2Lnet/sf/jasperreports/engine/type/OnErrorTypeEnum;L paddingq ~ \L rightBorderq ~ L rightBorderColorq ~ +L rightPaddingq ~ \L 
scaleImageq ~ L scaleImageValuet 1Lnet/sf/jasperreports/engine/type/ScaleImageEnum;L 	topBorderq ~ L topBorderColorq ~ +L 
topPaddingq ~ \L verticalAlignmentq ~ L verticalAlignmentValueq ~ sxq ~ ]  wñ   C       Rÿÿÿÿ   pq ~ q ~pt image-1pppp~q ~ 4t FLOATppppq ~ 8  wîppsq ~ f  wñppppq ~Úp  wñ         ppppppp~q ~ t PAGEsq ~ :   uq ~ =   sq ~ ?t logoPadraoRelatoriot java.io.InputStreamppppppppq ~ ¹pppsq ~ upsq ~ y  wñsq ~ a    ÿÿÿÿppppppq ~æq ~æq ~Úpsq ~ |  wñsq ~ a    ÿÿÿÿppppppq ~æq ~æpsq ~ z  wñsq ~ a    ÿÿÿÿppppppq ~æq ~æpsq ~   wñsq ~ a    ÿÿÿÿppppppq ~æq ~æpsq ~   wñsq ~ a    ÿÿÿÿppppppq ~æq ~æpp~r 0net.sf.jasperreports.engine.type.OnErrorTypeEnum          xq ~ t BLANKppppp~r /net.sf.jasperreports.engine.type.ScaleImageEnum          xq ~ t 
FILL_FRAMEpppppsq ~ k  wñ           x  ù   
pq ~ q ~pppq ~£ppq ~ 5ppppq ~ 8  wñpppppt Verdanaq ~ pq ~ ¤pppppppppsq ~ upsq ~ y  wñpp~q ~ªt DOTTEDsq ~­?   q ~ùq ~ùq ~÷psq ~ |  wñppq ~ûsq ~­?   q ~ùq ~ùpsq ~ z  wñppppq ~ùq ~ùpsq ~   wñppq ~ûsq ~­?   q ~ùq ~ùpsq ~   wñppppq ~ùq ~ùppppppppppppppppq ~   wñ        ppq ~ sq ~ :   uq ~ =   sq ~ ?t 
"UsuÃ¡rio:"+ sq ~ ?t usuariot java.lang.Stringpppppppppt  sq ~ k  wñ           K  ù   pq ~ q ~ppppppq ~ 5ppppq ~ 8  wñpppppt Verdanaq ~ pq ~ ®pppppppppsq ~ upsq ~ y  wñppq ~ûsq ~­?   q ~q ~q ~psq ~ |  wñppq ~ûsq ~­?   q ~q ~psq ~ z  wñppppq ~q ~psq ~   wñppppq ~q ~psq ~   wñppppq ~q ~ppppppppppppppppq ~   wñ        ppq ~ sq ~ :   uq ~ =   sq ~ ?t "PÃ¡gina "+sq ~ ?t PAGE_NUMBERsq ~ ?t +" de"t java.lang.Stringppppppppppsq ~ k  wñ           -  D   pq ~ q ~ppppppq ~ 5ppppq ~ 8  wñpppppt Verdanaq ~ pppppppppppsq ~ upsq ~ y  wñppq ~ûsq ~­?   q ~!q ~!q ~sq ~    sq ~ |  wñppppq ~!q ~!psq ~ z  wñppppq ~!q ~!psq ~   wñppq ~ûsq ~­?   q ~!q ~!psq ~   wñppppq ~!q ~!ppppppppppppppppq ~   wñ        pp~q ~ t REPORTsq ~ :   uq ~ =   sq ~ ?t PAGE_NUMBERt java.lang.Integerppppppppppsq ~ k  wñ         ¦   S   &pq ~ q ~ppppppq ~ 5ppppq ~ 8  wñpppppt Arialsq ~    
pq ~ ¤pppppppppsq ~ upsq ~ y  wñppppq ~4q ~4q ~1psq ~ |  wñppppq ~4q ~4psq ~ z  wñppppq ~4q ~4psq ~   wñppppq ~4q ~4psq ~   wñppppq ~4q ~4ppppppppppppppppq ~   wñ        ppq ~ sq ~ :   uq ~ =   sq ~ ?t filtrost java.lang.Stringppppppppppsq ~ k  wñ          )      pq ~ q ~pt 
textField-216ppppq ~ 5ppppq ~ 8  wñpppppt Arialq ~3pq ~ ¤q ~ Ìq ~ ¹pppppppsq ~ upsq ~ y  wñsq ~ a    ÿÿÿÿppppppq ~Bq ~Bq ~?psq ~ |  wñsq ~ a    ÿÿÿÿppppppq ~Bq ~Bpsq ~ z  wñsq ~ a    ÿÿÿÿppppppq ~Bq ~Bpsq ~   wñsq ~ a    ÿÿÿÿppppppq ~Bq ~Bpsq ~   wñsq ~ a    ÿÿÿÿppppppq ~Bq ~Bpppppt Helvetica-BoldObliqueppppppppppq ~Ð  wñ        ppq ~ sq ~ :   uq ~ =   sq ~ ?t descricaoPeriodot java.lang.Stringppppppq ~ Ìpppxp  wñ   Epp~r .net.sf.jasperreports.engine.type.SplitTypeEnum          xq ~ t STRETCH~r /net.sf.jasperreports.engine.type.PrintOrderEnum          xq ~ t VERTICALpsq ~ sq ~ $   w   sq ~ [  wñ   K       x   ú   sq ~ a    ÿðïïpppq ~ q ~Ysq ~ a    ÿÿÿÿppppppppq ~ 5ppppq ~ 8  wîppsq ~ f  wñppppq ~[ppsq ~ k  wñ          w   ú   7pq ~ q ~Yppppppq ~ 5ppppq ~ 8  wñppppppsq ~    pq ~ ®q ~ ¹ppppppppsq ~ upsq ~ y  wñppppq ~aq ~aq ~_psq ~ |  wñppppq ~aq ~apsq ~ z  wñppppq ~aq ~apsq ~   wñppppq ~aq ~apsq ~   wñppppq ~aq ~appppppppppppppppq ~   wñ        ppq ~ sq ~ :   %uq ~ =   sq ~ ?t +"Total geral de comissÃµes calculadas: " + sq ~ ?t totalt java.lang.Stringppppppppppsq ~ k  wñ          w   ú   pq ~ q ~Yppppppq ~ 5ppppq ~ 8  wñppppppq ~`pq ~ ®q ~ ¹ppppppppsq ~ upsq ~ y  wñppppq ~oq ~oq ~npsq ~ |  wñppppq ~oq ~opsq ~ z  wñppppq ~oq ~opsq ~   wñppppq ~oq ~opsq ~   wñppppq ~oq ~oppppppppppppppppq ~   wñ        ppq ~ sq ~ :   &uq ~ =   	sq ~ ?t ""Total de itens na comissÃ£o: " + sq ~ ?t qtdContratosTotalsq ~ ?t  + (sq ~ ?t pagarComissaoProdutossq ~ ?t ? " (Contratos: " + sq ~ ?t 	contratossq ~ ?t +"; Produtos: "+sq ~ ?t produtossq ~ ?t 
+")" : "")t java.lang.Stringppppppppppsq ~ k  wñ          w   ú   pq ~ q ~Yppppppq ~ 5ppppq ~ 8  wñppppppq ~`pq ~ ®q ~ ¹ppppppppsq ~ upsq ~ y  wñppppq ~q ~q ~psq ~ |  wñppppq ~q ~psq ~ z  wñppppq ~q ~psq ~   wñppppq ~q ~psq ~   wñppppq ~q ~ppppppppppppppppq ~   wñ        ppq ~ sq ~ :   'uq ~ =   sq ~ ?t "Total de Geral pago: " + sq ~ ?t totalGeralPagot java.lang.Stringppppppppppsr +net.sf.jasperreports.engine.base.JRBaseLine      'Ø I PSEUDO_SERIAL_VERSION_UIDB 	directionL directionValuet 4Lnet/sf/jasperreports/engine/type/LineDirectionEnum;xq ~ ]  wñ          q       pq ~ q ~Yppppppq ~ 5ppppq ~ 8  wîppsq ~ f  wñppppq ~p  wñ ~r 2net.sf.jasperreports.engine.type.LineDirectionEnum          xq ~ t TOP_DOWNsq ~ k  wñ           î       .pq ~ q ~Yppppppq ~ 5pppp~q ~ 7t RELATIVE_TO_BAND_HEIGHT  wñppppppq ~ pppppppppppsq ~ upsq ~ y  wñppppq ~¢q ~¢q ~psq ~ |  wñppppq ~¢q ~¢psq ~ z  wñppppq ~¢q ~¢psq ~   wñppppq ~¢q ~¢psq ~   wñppppq ~¢q ~¢ppppppppppppppppp  wñ       ppq ~ sq ~ :   (uq ~ =   sq ~ ?t #"CÃ³digo: CÃ³digo do contrato." +
(sq ~ ?t pagarComissaoProdutossq ~ ?t U.equals(true) ?
    " Caso produto, apresentarÃ¡ o cÃ³digo do MovProduto." :
    " ")t java.lang.Stringppppppppppsq ~ k  wñ           î       pq ~ q ~Yppppppq ~ 5ppppq ~   wñppppppq ~ ppq ~ ¹ppppppppsq ~ upsq ~ y  wñppppq ~²q ~²q ~±psq ~ |  wñppppq ~²q ~²psq ~ z  wñppppq ~²q ~²psq ~   wñppppq ~²q ~²psq ~   wñppppq ~²q ~²ppppppppppppppppp  wñ       ppq ~ sq ~ :   )uq ~ =   sq ~ ?t "Vl. Total** :" +
(sq ~ ?t comissaoMatriculaRematriculasq ~ ?t f.equals(true) ?
    " Valor do contrato + MatrÃ­cula e RematrÃ­cula." :
    " Valor do Contrato.") +
(sq ~ ?t pagarComissaoProdutossq ~ ?t V.equals(true) ?
    " Caso produto, apresentarÃ¡ o valor total do produto." :
    " ")t java.lang.Stringppppppppppsq ~   wñ           F       pq ~ q ~Yppppppq ~ 5ppppq ~ 8  wñpppppppppq ~ ¹ppppppppsq ~ upsq ~ y  wñppppq ~Æq ~Æq ~Åpsq ~ |  wñppppq ~Æq ~Æpsq ~ z  wñppppq ~Æq ~Æpsq ~   wñppppq ~Æq ~Æpsq ~   wñppppq ~Æq ~Æpppppppppppppppppt Legenda:xp  wñ   Pppppp~r 3net.sf.jasperreports.engine.type.WhenNoDataTypeEnum          xq ~ t ALL_SECTIONS_NO_DETAILsr 6net.sf.jasperreports.engine.design.JRReportCompileData      'Ø L crosstabCompileDataq ~}L datasetCompileDataq ~}L mainDatasetCompileDataq ~ xpsq ~A?@     w       xsq ~A?@     w       xur [B¬óøTà  xp  .¯Êþº¾   .  ComissaoRel_1573501741807_753193  ,net/sf/jasperreports/engine/fill/JREvaluator  parameter_total 2Lnet/sf/jasperreports/engine/fill/JRFillParameter; parameter_qtdContratosTotal parameter_totalGeralPago parameter_JASPER_REPORT parameter_REPORT_TIME_ZONE parameter_usuario parameter_REPORT_FILE_RESOLVER parameter_modoVisualizacao parameter_REPORT_PARAMETERS_MAP parameter_SUBREPORT_DIR1 parameter_REPORT_CLASS_LOADER $parameter_REPORT_URL_HANDLER_FACTORY parameter_REPORT_DATA_SOURCE parameter_IS_IGNORE_PAGINATION parameter_qtdAlunos parameter_SUBREPORT_DIR2 parameter_REPORT_MAX_COUNT parameter_REPORT_TEMPLATES &parameter_comissaoMatriculaRematricula parameter_REPORT_LOCALE parameter_descricaoPeriodo parameter_REPORT_VIRTUALIZER parameter_SORT_FIELDS parameter_logoPadraoRelatorio parameter_REPORT_SCRIPTLET parameter_pagarComissaoProdutos parameter_REPORT_CONNECTION parameter_SUBREPORT_DIR parameter_REPORT_FORMAT_FACTORY parameter_tituloRelatorio parameter_nomeEmpresa  parameter_REPORT_RESOURCE_BUNDLE parameter_filtros field_codigo .Lnet/sf/jasperreports/engine/fill/JRFillField; field_qtdProdutos field_valorTotalProdutos  field_valorTotalComissaoProdutos field_valorTotal field_listaConfiguracao !field_valorTotalComissaoContratos field_valorTotalComissao 
field_nome field_valorTotalContratos field_qtdContratos variable_PAGE_NUMBER 1Lnet/sf/jasperreports/engine/fill/JRFillVariable; variable_COLUMN_NUMBER variable_REPORT_COUNT variable_PAGE_COUNT variable_COLUMN_COUNT variable_contratos variable_produtos <init> ()V Code ; <
  >  	  @  	  B  	  D 	 	  F 
 	  H  	  J  	  L 
 	  N  	  P  	  R  	  T  	  V  	  X  	  Z  	  \  	  ^  	  `  	  b  	  d  	  f  	  h  	  j  	  l  	  n  	  p  	  r   	  t ! 	  v " 	  x # 	  z $ 	  | % 	  ~ & 	   ' (	   ) (	   * (	   + (	   , (	   - (	   . (	   / (	   0 (	   1 (	   2 (	   3 4	   5 4	   6 4	   7 4	   8 4	    9 4	  ¢ : 4	  ¤ LineNumberTable customizedInit 0(Ljava/util/Map;Ljava/util/Map;Ljava/util/Map;)V 
initParams (Ljava/util/Map;)V © ª
  « 
initFields ­ ª
  ® initVars ° ª
  ± total ³ 
java/util/Map µ get &(Ljava/lang/Object;)Ljava/lang/Object; · ¸ ¶ ¹ 0net/sf/jasperreports/engine/fill/JRFillParameter » qtdContratosTotal ½ totalGeralPago ¿ 
JASPER_REPORT Á REPORT_TIME_ZONE Ã usuario Å REPORT_FILE_RESOLVER Ç modoVisualizacao É REPORT_PARAMETERS_MAP Ë SUBREPORT_DIR1 Í REPORT_CLASS_LOADER Ï REPORT_URL_HANDLER_FACTORY Ñ REPORT_DATA_SOURCE Ó IS_IGNORE_PAGINATION Õ 	qtdAlunos × SUBREPORT_DIR2 Ù REPORT_MAX_COUNT Û REPORT_TEMPLATES Ý comissaoMatriculaRematricula ß 
REPORT_LOCALE á descricaoPeriodo ã REPORT_VIRTUALIZER å SORT_FIELDS ç logoPadraoRelatorio é REPORT_SCRIPTLET ë pagarComissaoProdutos í REPORT_CONNECTION ï 
SUBREPORT_DIR ñ REPORT_FORMAT_FACTORY ó tituloRelatorio õ nomeEmpresa ÷ REPORT_RESOURCE_BUNDLE ù filtros û codigo ý ,net/sf/jasperreports/engine/fill/JRFillField ÿ qtdProdutos valorTotalProdutos valorTotalComissaoProdutos 
valorTotal listaConfiguracao	 valorTotalComissaoContratos valorTotalComissao
 nome valorTotalContratos qtdContratos PAGE_NUMBER /net/sf/jasperreports/engine/fill/JRFillVariable 
COLUMN_NUMBER REPORT_COUNT 
PAGE_COUNT COLUMN_COUNT 	contratos! produtos# evaluate (I)Ljava/lang/Object; 
Exceptions java/lang/Throwable( fD:\PactoJ\Desenvolvimento\Sistemas\ZillyonWeb\tronco-novo\src\java\relatorio\designRelatorio\contrato\* eE:\desenvolvimentosv\Projeto Pacto Solucoes\ZillyonWeb\src\java\relatorio\designRelatorio\financeiro\, java/lang/Integer. (I)V ;0
/1 getValue ()Ljava/lang/Object;34
 5 java/util/Date7
8 >
 ¼5 java/lang/String; java/io/InputStream= java/lang/StringBuffer? 	UsuÃ¡rio:A (Ljava/lang/String;)V ;C
@D append ,(Ljava/lang/String;)Ljava/lang/StringBuffer;FG
@H toString ()Ljava/lang/String;JK
@L PÃ¡gina N
5 ,(Ljava/lang/Object;)Ljava/lang/StringBuffer;FQ
@R  deT (net/sf/jasperreports/engine/JRDataSourceV valueOf &(Ljava/lang/Object;)Ljava/lang/String;XY
<Z ComissaoConsultor.jasper\ Vl. Total**^ java/lang/Boolean` (Z)Ljava/lang/Boolean;Xb
ac equals (Ljava/lang/Object;)Zef
ag intValue ()Iij
/k (I)Ljava/lang/Integer;Xm
/n &Total geral de comissÃµes calculadas: p Total de itens na comissÃ£o: r booleanValue ()Ztu
av 
 (Contratos: x ; Produtos: z )|  ~ Total de Geral pago:  CÃ³digo: CÃ³digo do contrato. 4 Caso produto, apresentarÃ¡ o cÃ³digo do MovProduto.   
Vl. Total** : / Valor do contrato + MatrÃ­cula e RematrÃ­cula.  Valor do Contrato. 5 Caso produto, apresentarÃ¡ o valor total do produto. evaluateOld getOldValue4
 
 evaluateEstimated getEstimatedValue4
 
SourceFile !     3                 	     
               
                                                                                                     !     "     #     $     %     &     ' (    ) (    * (    + (    , (    - (    . (    / (    0 (    1 (    2 (    3 4    5 4    6 4    7 4    8 4    9 4    : 4     ; <  =  ì    *· ?*µ A*µ C*µ E*µ G*µ I*µ K*µ M*µ O*µ Q*µ S*µ U*µ W*µ Y*µ [*µ ]*µ _*µ a*µ c*µ e*µ g*µ i*µ k*µ m*µ o*µ q*µ s*µ u*µ w*µ y*µ {*µ }*µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ ¡*µ £*µ ¥±    ¦   Ö 5      	          ! " " ' # , $ 1 % 6 & ; ' @ ( E ) J * O + T , Y - ^ . c / h 0 m 1 r 2 w 3 | 4  5  6  7  8  9  :  ; ¤ < © = ® > ³ ? ¸ @ ½ A Â B Ç C Ì D Ñ E Ö F Û G à H å I ê J ï K ô L ù M þ N   § ¨  =   4     *+· ¬*,· ¯*-· ²±    ¦       Z  [ 
 \  ]  © ª  =  ï    S*+´¹ º À ¼À ¼µ A*+¾¹ º À ¼À ¼µ C*+À¹ º À ¼À ¼µ E*+Â¹ º À ¼À ¼µ G*+Ä¹ º À ¼À ¼µ I*+Æ¹ º À ¼À ¼µ K*+È¹ º À ¼À ¼µ M*+Ê¹ º À ¼À ¼µ O*+Ì¹ º À ¼À ¼µ Q*+Î¹ º À ¼À ¼µ S*+Ð¹ º À ¼À ¼µ U*+Ò¹ º À ¼À ¼µ W*+Ô¹ º À ¼À ¼µ Y*+Ö¹ º À ¼À ¼µ [*+Ø¹ º À ¼À ¼µ ]*+Ú¹ º À ¼À ¼µ _*+Ü¹ º À ¼À ¼µ a*+Þ¹ º À ¼À ¼µ c*+à¹ º À ¼À ¼µ e*+â¹ º À ¼À ¼µ g*+ä¹ º À ¼À ¼µ i*+æ¹ º À ¼À ¼µ k*+è¹ º À ¼À ¼µ m*+ê¹ º À ¼À ¼µ o*+ì¹ º À ¼À ¼µ q*+î¹ º À ¼À ¼µ s*+ð¹ º À ¼À ¼µ u*+ò¹ º À ¼À ¼µ w*+ô¹ º À ¼À ¼µ y*+ö¹ º À ¼À ¼µ {*+ø¹ º À ¼À ¼µ }*+ú¹ º À ¼À ¼µ *+ü¹ º À ¼À ¼µ ±    ¦    "   e  f $ g 6 h H i Z j l k ~ l  m ¢ n ´ o Æ p Ø q ê r ü s t  u2 vD wV xh yz z { |° }Â ~Ô æ ø 
  . @ R   ­ ª  =       Ñ*+þ¹ º À À µ *+¹ º À À µ *+¹ º À À µ *+¹ º À À µ *+¹ º À À µ *+
¹ º À À µ *+¹ º À À µ *+¹ º À À µ *+¹ º À À µ *+¹ º À À µ *+¹ º À À µ ±    ¦   2       %  8  K  ^  q      ª  ½  Ð   ° ª  =   º     *+¹ º ÀÀµ *+¹ º ÀÀµ *+¹ º ÀÀµ *+¹ º ÀÀµ *+ ¹ º ÀÀµ ¡*+"¹ º ÀÀµ £*+$¹ º ÀÀµ ¥±    ¦   "    ¡  ¢ & £ 9 ¤ L ¥ _ ¦ r §  ¨ %& '    ) =  ±    
Mª         )   µ   ¼   Ã   Ê   Ö   â   î   ú        *  8  F  Q  _  m    ¯  ½  Ë  Ù  ç  õ    $  2  9  G  U  c  q      ¥  Ç  Õ  ã    k    º+M§O-M§H-M§A»/Y·2M§5»/Y·2M§)»/Y·2M§»/Y·2M§»/Y·2M§»/Y·2M§ù»/Y·2M§í»/Y·2M§á*´ ¶6À/M§Ó*´ ¶6À/M§Å»8Y·9M§º*´ {¶:À<M§¬*´ o¶:À>M§»@YB·E*´ K¶:À<¶I¶MM§»@YO·E*´ ¶PÀ/¶SU¶I¶MM§\*´ ¶PÀ/M§N*´ ¶:À<M§@*´ i¶:À<M§2*´ w¶:À<M§$*´ O¶:À<M§*´ ¶6ÀWM§»@Y*´ w¶:À<¸[·E]¶I¶MM§ç*´ ¶6À<M§Ù_M§Ò*´ ¶6À<M§Ä*´ ¶6À<M§¶*´ ¶6À/M§¨*´ ¶6À<M§*´ ¶6À<M§*´ ¶6À/M§~*´ s¶:Àa¸d¶h¸dM§f*´ ¶6À/¶l*´ ¶6À/¶l`¸oM§D*´ ¶6À<M§6*´ ¶6À<M§(»@Yq·E*´ A¶:À<¶I¶MM§
»@Ys·E*´ C¶:À/¶S*´ s¶:Àa¶w 9»@Yy·E*´ £¶PÀ/¶S{¶I*´ ¥¶PÀ/¶S}¶I¶M§ ¶I¶MM§  »@Y·E*´ E¶:À<¶I¶MM§ »@Y·E*´ s¶:Àa¸d¶h 	§ ¶I¶MM§ Q»@Y·E*´ e¶:Àa¸d¶h 	§ ¶I*´ s¶:Àa¸d¶h 	§ ¶I¶MM,°    ¦   d   °  ² ¸ ¶ ¼ · ¿ » Ã ¼ Æ À Ê Á Í Å Ö Æ Ù Ê â Ë å Ï î Ð ñ Ô ú Õ ý Ù Ú	 Þ ß ã ä! è* é- í8 î; òF óI ÷Q øT ü_ ýbmp¯²½ÀËÎÙÜç ê$õ%ø)*.$/'3245899<=G>JBUCXGcHfLqMtQRVW[¥\¨`ÇaÊeÕfØjãkæoptkunyz~ª°³¶~º½ÇÛáäçû & '    ) =  ±    
Mª         )   µ   ¼   Ã   Ê   Ö   â   î   ú        *  8  F  Q  _  m    ¯  ½  Ë  Ù  ç  õ    $  2  9  G  U  c  q      ¥  Ç  Õ  ã    k    º+M§O-M§H-M§A»/Y·2M§5»/Y·2M§)»/Y·2M§»/Y·2M§»/Y·2M§»/Y·2M§ù»/Y·2M§í»/Y·2M§á*´ ¶À/M§Ó*´ ¶À/M§Å»8Y·9M§º*´ {¶:À<M§¬*´ o¶:À>M§»@YB·E*´ K¶:À<¶I¶MM§»@YO·E*´ ¶À/¶SU¶I¶MM§\*´ ¶À/M§N*´ ¶:À<M§@*´ i¶:À<M§2*´ w¶:À<M§$*´ O¶:À<M§*´ ¶ÀWM§»@Y*´ w¶:À<¸[·E]¶I¶MM§ç*´ ¶À<M§Ù_M§Ò*´ ¶À<M§Ä*´ ¶À<M§¶*´ ¶À/M§¨*´ ¶À<M§*´ ¶À<M§*´ ¶À/M§~*´ s¶:Àa¸d¶h¸dM§f*´ ¶À/¶l*´ ¶À/¶l`¸oM§D*´ ¶À<M§6*´ ¶À<M§(»@Yq·E*´ A¶:À<¶I¶MM§
»@Ys·E*´ C¶:À/¶S*´ s¶:Àa¶w 9»@Yy·E*´ £¶À/¶S{¶I*´ ¥¶À/¶S}¶I¶M§ ¶I¶MM§  »@Y·E*´ E¶:À<¶I¶MM§ »@Y·E*´ s¶:Àa¸d¶h 	§ ¶I¶MM§ Q»@Y·E*´ e¶:Àa¸d¶h 	§ ¶I*´ s¶:Àa¸d¶h 	§ ¶I¶MM,°    ¦   d    ¸£ ¼¤ ¿¨ Ã© Æ­ Ê® Í² Ö³ Ù· â¸ å¼ î½ ñÁ úÂ ýÆÇ	ËÌÐÑ!Õ*Ö-Ú8Û;ßFàIäQåTé_êbîmïpóôø¯ù²ý½þÀËÎÙÜç
êõø$' 2!5%9&<*G+J/U0X4c5f9q:t>?CDH¥I¨MÇNÊRÕSØWãXæ\]akbnfgklªm°n³l¶kºo½sÇtÛuávätçwûxyws & '    ) =  ±    
Mª         )   µ   ¼   Ã   Ê   Ö   â   î   ú        *  8  F  Q  _  m    ¯  ½  Ë  Ù  ç  õ    $  2  9  G  U  c  q      ¥  Ç  Õ  ã    k    º+M§O-M§H-M§A»/Y·2M§5»/Y·2M§)»/Y·2M§»/Y·2M§»/Y·2M§»/Y·2M§ù»/Y·2M§í»/Y·2M§á*´ ¶6À/M§Ó*´ ¶6À/M§Å»8Y·9M§º*´ {¶:À<M§¬*´ o¶:À>M§»@YB·E*´ K¶:À<¶I¶MM§»@YO·E*´ ¶À/¶SU¶I¶MM§\*´ ¶À/M§N*´ ¶:À<M§@*´ i¶:À<M§2*´ w¶:À<M§$*´ O¶:À<M§*´ ¶6ÀWM§»@Y*´ w¶:À<¸[·E]¶I¶MM§ç*´ ¶6À<M§Ù_M§Ò*´ ¶6À<M§Ä*´ ¶6À<M§¶*´ ¶6À/M§¨*´ ¶6À<M§*´ ¶6À<M§*´ ¶6À/M§~*´ s¶:Àa¸d¶h¸dM§f*´ ¶6À/¶l*´ ¶6À/¶l`¸oM§D*´ ¶6À<M§6*´ ¶6À<M§(»@Yq·E*´ A¶:À<¶I¶MM§
»@Ys·E*´ C¶:À/¶S*´ s¶:Àa¶w 9»@Yy·E*´ £¶À/¶S{¶I*´ ¥¶À/¶S}¶I¶M§ ¶I¶MM§  »@Y·E*´ E¶:À<¶I¶MM§ »@Y·E*´ s¶:Àa¸d¶h 	§ ¶I¶MM§ Q»@Y·E*´ e¶:Àa¸d¶h 	§ ¶I*´ s¶:Àa¸d¶h 	§ ¶I¶MM,°    ¦   d    ¸ ¼ ¿ Ã Æ Ê Í Ö  Ù¤ â¥ å© îª ñ® ú¯ ý³´	¸¹½¾!Â*Ã-Ç8È;ÌFÍIÑQÒTÖ_×bÛmÜpàáå¯æ²ê½ëÀïËðÎôÙõÜùçúêþõÿø$	'
259<GJUX!c"f&q't+,015¥6¨:Ç;Ê?Õ@ØDãEæIJNkOnSTXYªZ°[³Y¶Xº\½`ÇaÛbácäaçdûefd`n     t _1573501741807_753193t 2net.sf.jasperreports.engine.design.JRJavacCompiler
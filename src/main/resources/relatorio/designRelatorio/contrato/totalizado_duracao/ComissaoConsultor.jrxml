<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="ComissaoConsultor" pageWidth="625" pageHeight="823" whenNoDataType="AllSectionsNoDetail" columnWidth="625" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0">
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.encoding" value="UTF-8"/>
	<property name="ireport.zoom" value="0.9330147604194674"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="java.util.*"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<parameter name="modoVisualizacao" class="java.lang.String"/>
	<parameter name="SUBREPORT_DIR" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA["D:\\PactoJ\\Desenvolvimento\\Sistemas\\ZillyonWeb\\tronco-novo\\src\\java\\relatorio\\designRelatorio\\contrato\\"]]></defaultValueExpression>
	</parameter>
	<field name="configuracao_apresentar" class="java.lang.String"/>
	<field name="listaComissoes" class="java.lang.Object"/>
	<field name="valorTotalConfiguracao" class="java.lang.String"/>
	<field name="valorComissaoConfiguracao" class="java.lang.String"/>
	<field name="qtdComissoes" class="java.lang.Integer"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band splitType="Stretch"/>
	</title>
	<pageHeader>
		<band splitType="Stretch"/>
	</pageHeader>
	<detail>
		<band height="20" splitType="Stretch">
			<textField isStretchWithOverflow="true">
				<reportElement isPrintRepeatedValues="false" x="30" y="0" width="300" height="20" isRemoveLineWhenBlank="true" isPrintWhenDetailOverflows="true"/>
				<textElement verticalAlignment="Middle">
					<font isBold="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{configuracao_apresentar}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="545" y="0" width="80" height="20"/>
				<textElement textAlignment="Right">
					<font isUnderline="false"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{valorComissaoConfiguracao}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="440" y="0" width="80" height="20"/>
				<textElement textAlignment="Right">
					<font isUnderline="false"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{valorTotalConfiguracao}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="340" y="0" width="50" height="20"/>
				<textElement textAlignment="Center">
					<font isUnderline="false"/>
				</textElement>
				<textFieldExpression class="java.lang.Integer"><![CDATA[$F{qtdComissoes}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<pageFooter>
		<band splitType="Stretch"/>
	</pageFooter>
	<summary>
		<band splitType="Stretch"/>
	</summary>
</jasperReport>

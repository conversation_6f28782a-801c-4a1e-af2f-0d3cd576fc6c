<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="RelatorioEstoqueProduto_subTotalizador" language="groovy" pageWidth="555" pageHeight="802" columnWidth="555" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0">
	<property name="ireport.zoom" value="1.5"/>
	<property name="ireport.x" value="131"/>
	<property name="ireport.y" value="0"/>
	<parameter name="totalestoque" class="java.lang.Integer"/>
	<parameter name="totalgeral" class="java.lang.Double"/>
	<field name="descricao" class="java.lang.String"/>
	<field name="estoque" class="java.lang.Integer"/>
	<field name="valorTotal" class="java.lang.Double"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band splitType="Stretch"/>
	</title>
	<pageHeader>
		<band splitType="Stretch"/>
	</pageHeader>
	<columnHeader>
		<band height="32">
			<staticText>
				<reportElement x="388" y="8" width="71" height="20"/>
				<textElement textAlignment="Right" verticalAlignment="Bottom">
					<font isBold="true"/>
				</textElement>
				<text><![CDATA[Estoque]]></text>
			</staticText>
			<staticText>
				<reportElement x="467" y="8" width="87" height="20"/>
				<textElement textAlignment="Right" verticalAlignment="Bottom">
					<font isBold="true"/>
				</textElement>
				<text><![CDATA[Valor Total]]></text>
			</staticText>
			<line>
				<reportElement x="223" y="30" width="330" height="1"/>
			</line>
			<staticText>
				<reportElement stretchType="RelativeToBandHeight" x="223" y="8" width="122" height="20" isPrintWhenDetailOverflows="true"/>
				<textElement verticalAlignment="Bottom">
					<font isBold="true"/>
				</textElement>
				<text><![CDATA[Totalizador Categoria]]></text>
			</staticText>
		</band>
	</columnHeader>
	<detail>
		<band height="13" splitType="Stretch">
			<textField pattern="#,##0.00">
				<reportElement x="466" y="0" width="88" height="13"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="8" isBold="true"/>
				</textElement>
				<textFieldExpression class="java.lang.Double"><![CDATA[$F{valorTotal}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="388" y="0" width="71" height="13"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="8" isBold="true"/>
				</textElement>
				<textFieldExpression class="java.lang.Integer"><![CDATA[$F{estoque}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="223" y="0" width="124" height="13"/>
				<textElement textAlignment="Left" verticalAlignment="Middle" markup="none">
					<font size="8" isBold="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{descricao}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<columnFooter>
		<band splitType="Stretch"/>
	</columnFooter>
	<pageFooter>
		<band splitType="Stretch"/>
	</pageFooter>
	<summary>
		<band height="28" splitType="Stretch">
			<line>
				<reportElement x="-1" y="23" width="555" height="1"/>
			</line>
			<staticText>
				<reportElement x="223" y="0" width="77" height="23"/>
				<textElement textAlignment="Left" verticalAlignment="Bottom">
					<font size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[Total Geral:]]></text>
			</staticText>
			<textField>
				<reportElement x="388" y="10" width="71" height="13"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="8" isBold="true"/>
				</textElement>
				<textFieldExpression class="java.lang.Integer"><![CDATA[$P{totalestoque}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00">
				<reportElement x="467" y="10" width="88" height="13"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="8" isBold="true"/>
				</textElement>
				<textFieldExpression class="java.lang.Double"><![CDATA[$P{totalgeral}]]></textFieldExpression>
			</textField>
		</band>
	</summary>
</jasperReport>

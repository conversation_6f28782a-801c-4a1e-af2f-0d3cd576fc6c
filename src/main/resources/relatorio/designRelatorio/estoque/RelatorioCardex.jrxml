<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="RelatorioCardex" pageWidth="595" pageHeight="842" whenNoDataType="NoDataSection" columnWidth="555" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20">
	<property name="ireport.zoom" value="1.5026296018031557"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<subDataset name="dataset1"/>
	<parameter name="logoPadraoRelatorio" class="java.io.InputStream" isForPrompting="false"/>
	<parameter name="nomeProduto" class="java.lang.String" isForPrompting="false"/>
	<parameter name="nomeEmpresa" class="java.lang.String" isForPrompting="false"/>
	<parameter name="enderecoEmpresa" class="java.lang.String" isForPrompting="false"/>
	<parameter name="cidadeEmpresa" class="java.lang.String" isForPrompting="false"/>
	<parameter name="dataInicial" class="java.lang.String" isForPrompting="false"/>
	<parameter name="dataFinal" class="java.lang.String" isForPrompting="false"/>
	<queryString language="xPath">
		<![CDATA[]]>
	</queryString>
	<field name="operacao.descricao" class="java.lang.String"/>
	<field name="data" class="java.util.Date"/>
	<field name="totalEntrada" class="java.lang.Integer"/>
	<field name="totalSaida" class="java.lang.Integer"/>
	<field name="saldoAnterior" class="java.lang.Integer"/>
	<field name="saldoAtual" class="java.lang.Integer"/>
	<field name="dataAux" class="java.util.Date"/>
	<field name="nomeAux" class="java.lang.String"/>
	<field name="codigo" class="java.lang.Integer"/>
	<field name="dataApresentar" class="java.lang.String"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<pageHeader>
		<band height="174">
			<staticText>
				<reportElement x="1" y="151" width="54" height="20"/>
				<textElement verticalAlignment="Bottom">
					<font isBold="true"/>
				</textElement>
				<text><![CDATA[Data]]></text>
			</staticText>
			<staticText>
				<reportElement x="313" y="151" width="56" height="20"/>
				<textElement textAlignment="Right" verticalAlignment="Bottom">
					<font isBold="true"/>
				</textElement>
				<text><![CDATA[Entrada(+)]]></text>
			</staticText>
			<staticText>
				<reportElement x="492" y="151" width="61" height="20"/>
				<textElement textAlignment="Right" verticalAlignment="Bottom">
					<font isBold="true"/>
				</textElement>
				<text><![CDATA[Saldo Atual]]></text>
			</staticText>
			<staticText>
				<reportElement x="81" y="151" width="100" height="20"/>
				<textElement verticalAlignment="Bottom">
					<font isBold="true"/>
				</textElement>
				<text><![CDATA[Operação]]></text>
			</staticText>
			<staticText>
				<reportElement x="372" y="151" width="47" height="20"/>
				<textElement textAlignment="Right" verticalAlignment="Bottom">
					<font isBold="true"/>
				</textElement>
				<text><![CDATA[Saída(-)]]></text>
			</staticText>
			<staticText>
				<reportElement x="421" y="151" width="71" height="20"/>
				<textElement textAlignment="Right" verticalAlignment="Bottom">
					<font isBold="true"/>
				</textElement>
				<text><![CDATA[Saldo Ant.]]></text>
			</staticText>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-208" x="123" y="6" width="113" height="14"/>
				<textElement>
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{nomeEmpresa}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-209" x="123" y="22" width="113" height="14"/>
				<textElement>
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{enderecoEmpresa}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-210" x="123" y="38" width="113" height="14"/>
				<textElement>
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{cidadeEmpresa}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement key="staticText-14" mode="Opaque" x="292" y="5" width="262" height="23"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Top" lineSpacing="Single">
					<font fontName="Microsoft Sans Serif" size="9" isBold="true" isItalic="true" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica-BoldOblique"/>
				</textElement>
				<text><![CDATA[Zillyon - Sistema Administrativo para Academias Desenvolvido por PACTO Soluções Tecnológicas Ltda.]]></text>
			</staticText>
			<textField evaluationTime="Report" isBlankWhenNull="false">
				<reportElement key="textField-212" x="543" y="47" width="29" height="17"/>
				<box bottomPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement>
					<font fontName="Arial" size="6" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[" " + $V{PAGE_NUMBER} + ""]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement key="staticText-15" mode="Opaque" x="458" y="36" width="96" height="12"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Top" lineSpacing="Single">
					<font fontName="Microsoft Sans Serif" size="6" isBold="true" isItalic="true" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica-BoldOblique"/>
				</textElement>
				<text><![CDATA[(0xx62) 3251-5820]]></text>
			</staticText>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-211" x="468" y="47" width="75" height="17"/>
				<box bottomPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right">
					<font fontName="Arial" size="6" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA["Página: " + $V{PAGE_NUMBER} + " de "]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="117" y="98" width="305" height="20"/>
				<textElement verticalAlignment="Bottom"/>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{nomeProduto}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="118" y="119" width="305" height="20"/>
				<textElement verticalAlignment="Bottom"/>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{dataInicial} + " à " + $P{dataFinal}]]></textFieldExpression>
			</textField>
			<image isUsingCache="true" onErrorType="Blank" evaluationTime="Page">
				<reportElement key="image-1" positionType="Float" stretchType="RelativeToBandHeight" x="0" y="4" width="121" height="48" isPrintWhenDetailOverflows="true" forecolor="#FFFFFF"/>
				<graphicElement>
					<pen lineColor="#FFFFFF"/>
				</graphicElement>
				<imageExpression class="java.io.InputStream"><![CDATA[$P{logoPadraoRelatorio}]]></imageExpression>
			</image>
			<staticText>
				<reportElement x="1" y="64" width="553" height="31"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="16" isBold="true"/>
				</textElement>
				<text><![CDATA[Cardex - Movimentações do Produto]]></text>
			</staticText>
			<staticText>
				<reportElement x="1" y="98" width="117" height="20"/>
				<textElement verticalAlignment="Bottom">
					<font isBold="true"/>
				</textElement>
				<text><![CDATA[Produto:]]></text>
			</staticText>
			<staticText>
				<reportElement x="1" y="119" width="117" height="20"/>
				<textElement verticalAlignment="Bottom">
					<font isBold="true"/>
				</textElement>
				<text><![CDATA[Período:]]></text>
			</staticText>
			<line>
				<reportElement x="0" y="173" width="555" height="1"/>
			</line>
			<staticText>
				<reportElement x="237" y="151" width="75" height="20"/>
				<textElement verticalAlignment="Bottom">
					<font isBold="true"/>
				</textElement>
				<text><![CDATA[Pessoa]]></text>
			</staticText>
		</band>
	</pageHeader>
	<detail>
		<band height="14" splitType="Stretch">
			<textField pattern="#,##0" isBlankWhenNull="true">
				<reportElement x="313" y="0" width="56" height="13"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.Integer"><![CDATA[$F{totalEntrada}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0" isBlankWhenNull="true">
				<reportElement x="372" y="0" width="47" height="13"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.Integer"><![CDATA[$F{totalSaida}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0" isBlankWhenNull="true">
				<reportElement x="419" y="0" width="73" height="13"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.Integer"><![CDATA[$F{saldoAnterior}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0" isBlankWhenNull="true">
				<reportElement x="492" y="0" width="62" height="13"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.Integer"><![CDATA[$F{saldoAtual}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="">
				<reportElement x="1" y="0" width="80" height="13"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{dataApresentar}]]></textFieldExpression>
			</textField>
			<textField pattern="">
				<reportElement x="81" y="0" width="157" height="13"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{operacao.descricao}.equals("Venda Cancelada") ?
$F{operacao.descricao} + " (dt. Venda:"  +
(new SimpleDateFormat("dd/MM/yyyy HH:mm:ss")).format($F{dataAux}) + ")"
:$F{operacao.descricao}]]></textFieldExpression>
			</textField>
			<textField pattern="">
				<reportElement x="239" y="0" width="73" height="13"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{nomeAux}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<summary>
		<band splitType="Stretch"/>
	</summary>
	<noData>
		<band height="50">
			<staticText>
				<reportElement x="1" y="13" width="542" height="20"/>
				<textElement textAlignment="Center">
					<font size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[Não há dados para serem exibidos ! verifique os parâmetros informados.]]></text>
			</staticText>
		</band>
	</noData>
</jasperReport>

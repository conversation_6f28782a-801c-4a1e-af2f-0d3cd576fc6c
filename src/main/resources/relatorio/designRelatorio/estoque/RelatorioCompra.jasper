¬í sr (net.sf.jasperreports.engine.JasperReport      'Ø L compileDatat Ljava/io/Serializable;L compileNameSuffixt Ljava/lang/String;L 
compilerClassq ~ xr -net.sf.jasperreports.engine.base.JRBaseReport      'Ø 'I bottomMarginI columnCountI 
columnSpacingI columnWidthZ ignorePaginationZ isFloatColumnFooterZ isSummaryNewPageZ  isSummaryWithPageHeaderAndFooterZ isTitleNewPageI 
leftMarginB orientationI 
pageHeightI 	pageWidthB 
printOrderI rightMarginI 	topMarginB whenNoDataTypeL 
backgroundt $Lnet/sf/jasperreports/engine/JRBand;L columnFooterq ~ L columnHeaderq ~ [ datasetst ([Lnet/sf/jasperreports/engine/JRDataset;L defaultFontt *Lnet/sf/jasperreports/engine/JRReportFont;L defaultStylet %Lnet/sf/jasperreports/engine/JRStyle;L detailq ~ L 
detailSectiont 'Lnet/sf/jasperreports/engine/JRSection;[ fontst +[Lnet/sf/jasperreports/engine/JRReportFont;L formatFactoryClassq ~ L 
importsSett Ljava/util/Set;L languageq ~ L lastPageFooterq ~ L mainDatasett 'Lnet/sf/jasperreports/engine/JRDataset;L nameq ~ L noDataq ~ L 
pageFooterq ~ L 
pageHeaderq ~ [ stylest &[Lnet/sf/jasperreports/engine/JRStyle;L summaryq ~ [ 	templatest /[Lnet/sf/jasperreports/engine/JRReportTemplate;L titleq ~ xp            +          J  S      sr +net.sf.jasperreports.engine.base.JRBaseBand      'Ø I PSEUDO_SERIAL_VERSION_UIDI heightZ isSplitAllowedL printWhenExpressiont *Lnet/sf/jasperreports/engine/JRExpression;L 	splitTypet Ljava/lang/Byte;xr 3net.sf.jasperreports.engine.base.JRBaseElementGroup      'Ø L childrent Ljava/util/List;L elementGroupt ,Lnet/sf/jasperreports/engine/JRElementGroup;xpsr java.util.ArrayListxÒÇa I sizexp    w   
xp  w&    psr java.lang.ByteN`îPõ B valuexr java.lang.Number¬à  xpppppppsr .net.sf.jasperreports.engine.base.JRBaseSection      'Ø [ bandst %[Lnet/sf/jasperreports/engine/JRBand;xpur %[Lnet.sf.jasperreports.engine.JRBand;Ý~ìÊ5  xp   sq ~ sq ~    w   
sr 0net.sf.jasperreports.engine.base.JRBaseTextField      'Ø I 
bookmarkLevelB evaluationTimeB hyperlinkTargetB 
hyperlinkTypeZ isStretchWithOverflowL anchorNameExpressionq ~ L evaluationGroupt %Lnet/sf/jasperreports/engine/JRGroup;L 
expressionq ~ L hyperlinkAnchorExpressionq ~ L hyperlinkPageExpressionq ~ [ hyperlinkParameterst 3[Lnet/sf/jasperreports/engine/JRHyperlinkParameter;L hyperlinkReferenceExpressionq ~ L hyperlinkTooltipExpressionq ~ L isBlankWhenNullt Ljava/lang/Boolean;L 
linkTargetq ~ L linkTypeq ~ L patternq ~ xr 2net.sf.jasperreports.engine.base.JRBaseTextElement      'Ø  L borderq ~ L borderColort Ljava/awt/Color;L bottomBorderq ~ L bottomBorderColorq ~ 'L 
bottomPaddingt Ljava/lang/Integer;L fontNameq ~ L fontSizeq ~ (L horizontalAlignmentq ~ L isBoldq ~ %L isItalicq ~ %L 
isPdfEmbeddedq ~ %L isStrikeThroughq ~ %L isStyledTextq ~ %L isUnderlineq ~ %L 
leftBorderq ~ L leftBorderColorq ~ 'L leftPaddingq ~ (L lineBoxt 'Lnet/sf/jasperreports/engine/JRLineBox;L lineSpacingq ~ L markupq ~ L paddingq ~ (L pdfEncodingq ~ L pdfFontNameq ~ L 
reportFontq ~ L rightBorderq ~ L rightBorderColorq ~ 'L rightPaddingq ~ (L rotationq ~ L 	topBorderq ~ L topBorderColorq ~ 'L 
topPaddingq ~ (L verticalAlignmentq ~ xr .net.sf.jasperreports.engine.base.JRBaseElement      'Ø I heightZ isPrintInFirstWholeBandZ isPrintRepeatedValuesZ isPrintWhenDetailOverflowsZ isRemoveLineWhenBlankB positionTypeB stretchTypeI widthI xI yL 	backcolorq ~ 'L defaultStyleProvidert 4Lnet/sf/jasperreports/engine/JRDefaultStyleProvider;L elementGroupq ~ L 	forecolorq ~ 'L keyq ~ L modeq ~ L parentStyleq ~ L parentStyleNameReferenceq ~ L printWhenExpressionq ~ L printWhenGroupChangesq ~ #L 
propertiesMapt -Lnet/sf/jasperreports/engine/JRPropertiesMap;[ propertyExpressionst 3[Lnet/sf/jasperreports/engine/JRPropertyExpression;xp   
       ×        pq ~ q ~  pppppppppppppppsr java.lang.Integerâ ¤÷8 I valuexq ~    ppppppppppsr .net.sf.jasperreports.engine.base.JRBaseLineBox      'Ø L 
bottomPaddingq ~ (L 	bottomPent +Lnet/sf/jasperreports/engine/base/JRBoxPen;L boxContainert ,Lnet/sf/jasperreports/engine/JRBoxContainer;L leftPaddingq ~ (L leftPenq ~ 2L paddingq ~ (L penq ~ 2L rightPaddingq ~ (L rightPenq ~ 2L 
topPaddingq ~ (L topPenq ~ 2xppsr 3net.sf.jasperreports.engine.base.JRBaseBoxBottomPen      'Ø  xr -net.sf.jasperreports.engine.base.JRBaseBoxPen      'Ø L lineBoxq ~ )xr *net.sf.jasperreports.engine.base.JRBasePen      'Ø L 	lineColorq ~ 'L 	lineStyleq ~ L 	lineWidtht Ljava/lang/Float;L penContainert ,Lnet/sf/jasperreports/engine/JRPenContainer;xppppq ~ 4q ~ 4q ~ .psr 1net.sf.jasperreports.engine.base.JRBaseBoxLeftPen      'Ø  xq ~ 6pppq ~ 4q ~ 4psq ~ 6pppq ~ 4q ~ 4psr 2net.sf.jasperreports.engine.base.JRBaseBoxRightPen      'Ø  xq ~ 6pppq ~ 4q ~ 4psr 0net.sf.jasperreports.engine.base.JRBaseBoxTopPen      'Ø  xq ~ 6pppq ~ 4q ~ 4pppppppppppppsq ~       ppsr 1net.sf.jasperreports.engine.base.JRBaseExpression      'Ø I id[ chunkst 0[Lnet/sf/jasperreports/engine/JRExpressionChunk;L valueClassNameq ~ L valueClassRealNameq ~ xp   ur 0[Lnet.sf.jasperreports.engine.JRExpressionChunk;mYÏÞiK£U  xp   sr 6net.sf.jasperreports.engine.base.JRBaseExpressionChunk      'Ø B typeL textq ~ xpt produto.descricaot java.lang.Stringppppppppppsq ~ "   
       >   ×    pq ~ q ~  pppppppppppppppq ~ 0sq ~ pppppppppsq ~ 1psq ~ 5pppq ~ Nq ~ Nq ~ Lpsq ~ ;pppq ~ Nq ~ Npsq ~ 6pppq ~ Nq ~ Npsq ~ >pppq ~ Nq ~ Npsq ~ @pppq ~ Nq ~ Npppppppppppppq ~ B      ppsq ~ C   uq ~ F   sq ~ Ht 
quantidadet java.lang.Integerppppppppppsq ~ "   
       A      pq ~ q ~  pppppppppppppppq ~ 0q ~ Mpppppppppsq ~ 1psq ~ 5pppq ~ Zq ~ Zq ~ Ypsq ~ ;pppq ~ Zq ~ Zpsq ~ 6pppq ~ Zq ~ Zpsq ~ >pppq ~ Zq ~ Zpsq ~ @pppq ~ Zq ~ Zpppppppppppppq ~ B      ppsq ~ C   uq ~ F   sq ~ Ht descontot java.lang.Doublepppppppppt #,##0.00sq ~ "   
       R  Ø    pq ~ q ~  pppppppppppppppq ~ 0q ~ Mpppppppppsq ~ 1psq ~ 5pppq ~ gq ~ gq ~ fpsq ~ ;pppq ~ gq ~ gpsq ~ 6pppq ~ gq ~ gpsq ~ >pppq ~ gq ~ gpsq ~ @pppq ~ gq ~ gpppppppppppppq ~ B      ppsq ~ C   uq ~ F   sq ~ Ht totalt java.lang.Doublepppppppppt #,##0.00sq ~ "   
       I  -    pq ~ q ~  pppppppppppppppq ~ 0q ~ Mpppppppppsq ~ 1psq ~ 5pppq ~ tq ~ tq ~ spsq ~ ;pppq ~ tq ~ tpsq ~ 6pppq ~ tq ~ tpsq ~ >pppq ~ tq ~ tpsq ~ @pppq ~ tq ~ tpppppppppppppq ~ B      ppsq ~ C   uq ~ F   sq ~ Ht 
valorUnitariot java.lang.Doublepppppppppt #,##0.00xp  w&   
pq ~ pppt javapsr .net.sf.jasperreports.engine.base.JRBaseDataset      'Ø Z isMainB whenResourceMissingType[ fieldst &[Lnet/sf/jasperreports/engine/JRField;L filterExpressionq ~ [ groupst &[Lnet/sf/jasperreports/engine/JRGroup;L nameq ~ [ 
parameterst *[Lnet/sf/jasperreports/engine/JRParameter;L 
propertiesMapq ~ ,L queryt %Lnet/sf/jasperreports/engine/JRQuery;L resourceBundleq ~ L scriptletClassq ~ [ 
scriptletst *[Lnet/sf/jasperreports/engine/JRScriptlet;[ 
sortFieldst *[Lnet/sf/jasperreports/engine/JRSortField;[ 	variablest )[Lnet/sf/jasperreports/engine/JRVariable;xpur &[Lnet.sf.jasperreports.engine.JRField;<ßÇN*òp  xp   sr ,net.sf.jasperreports.engine.base.JRBaseField      'Ø L descriptionq ~ L nameq ~ L 
propertiesMapq ~ ,L valueClassNameq ~ L valueClassRealNameq ~ xppt compra.numeroNFsr +net.sf.jasperreports.engine.JRPropertiesMap      'Ø L baseq ~ ,L propertiesListq ~ L 
propertiesMapt Ljava/util/Map;xppppt java.lang.Stringpsq ~ pt compra.empresa.nomesq ~ pppt java.lang.Stringpsq ~ pt compra.fornecedor.pessoa.nomesq ~ pppt java.lang.Stringpsq ~ pt compra.canceladasq ~ pppt java.lang.Booleanpsq ~ pt compra.contatosq ~ pppt java.lang.Stringpsq ~ pt compra.telefoneContatosq ~ pppt java.lang.Stringpsq ~ pt compra.dataEmissaosq ~ pppt java.util.Datepsq ~ pt compra.empresa.cidade.nomesq ~ pppt java.lang.Stringpsq ~ pt compra.empresa.enderecosq ~ pppt java.lang.Stringpsq ~ pt produto.descricaosq ~ pppt java.lang.Stringpsq ~ pt 
quantidadesq ~ pppt java.lang.Integerpsq ~ pt 
valorUnitariosq ~ pppt java.lang.Doublepsq ~ pt descontosq ~ pppt java.lang.Doublepsq ~ pt totalsq ~ pppt java.lang.Doublepppt RelatorioCompraur *[Lnet.sf.jasperreports.engine.JRParameter;" *Ã`!  xp   sr 0net.sf.jasperreports.engine.base.JRBaseParameter      'Ø 	Z isForPromptingZ isSystemDefinedL defaultValueExpressionq ~ L descriptionq ~ L nameq ~ L nestedTypeNameq ~ L 
propertiesMapq ~ ,L valueClassNameq ~ L valueClassRealNameq ~ xpppt REPORT_PARAMETERS_MAPpsq ~ pppt 
java.util.Mappsq ~ Êppt 
JASPER_REPORTpsq ~ pppt (net.sf.jasperreports.engine.JasperReportpsq ~ Êppt REPORT_CONNECTIONpsq ~ pppt java.sql.Connectionpsq ~ Êppt REPORT_MAX_COUNTpsq ~ pppt java.lang.Integerpsq ~ Êppt REPORT_DATA_SOURCEpsq ~ pppt (net.sf.jasperreports.engine.JRDataSourcepsq ~ Êppt REPORT_SCRIPTLETpsq ~ pppt /net.sf.jasperreports.engine.JRAbstractScriptletpsq ~ Êppt 
REPORT_LOCALEpsq ~ pppt java.util.Localepsq ~ Êppt REPORT_RESOURCE_BUNDLEpsq ~ pppt java.util.ResourceBundlepsq ~ Êppt REPORT_TIME_ZONEpsq ~ pppt java.util.TimeZonepsq ~ Êppt REPORT_FORMAT_FACTORYpsq ~ pppt .net.sf.jasperreports.engine.util.FormatFactorypsq ~ Êppt REPORT_CLASS_LOADERpsq ~ pppt java.lang.ClassLoaderpsq ~ Êppt REPORT_URL_HANDLER_FACTORYpsq ~ pppt  java.net.URLStreamHandlerFactorypsq ~ Êppt REPORT_FILE_RESOLVERpsq ~ pppt -net.sf.jasperreports.engine.util.FileResolverpsq ~ Êppt REPORT_VIRTUALIZERpsq ~ pppt )net.sf.jasperreports.engine.JRVirtualizerpsq ~ Êppt IS_IGNORE_PAGINATIONpsq ~ pppt java.lang.Booleanpsq ~ Êppt REPORT_TEMPLATESpsq ~ pppt java.util.Collectionpsq ~ Êppt XML_DATA_DOCUMENTpsq ~ pppt org.w3c.dom.Documentpsq ~ Êppt XML_DATE_PATTERNpsq ~ pppt java.lang.Stringpsq ~ Êppt XML_NUMBER_PATTERNpsq ~ pppq ~psq ~ Êppt 
XML_LOCALEpsq ~ pppq ~ æpsq ~ Êppt 
XML_TIME_ZONEpsq ~ pppq ~ îpsq ~ Ê  ppt logoPadraoRelatoriopsq ~ pppt java.io.InputStreampsq ~ psq ~    w   
t ireport.zoomt 	ireport.xt 	ireport.yxsr java.util.HashMapÚÁÃ`Ñ F 
loadFactorI 	thresholdxp?@     w      q ~"t 1.0q ~#t 0q ~$t 0xsr ,net.sf.jasperreports.engine.base.JRBaseQuery      'Ø [ chunkst +[Lnet/sf/jasperreports/engine/JRQueryChunk;L languageq ~ xppt xPathppppur )[Lnet.sf.jasperreports.engine.JRVariable;bæ|,·D  xp   sr /net.sf.jasperreports.engine.base.JRBaseVariable      'Ø 
B calculationB 
incrementTypeZ isSystemDefinedB 	resetTypeL 
expressionq ~ L incrementGroupq ~ #L incrementerFactoryClassNameq ~ L incrementerFactoryClassRealNameq ~ L initialValueExpressionq ~ L nameq ~ L 
resetGroupq ~ #L valueClassNameq ~ L valueClassRealNameq ~ xpppppsq ~ C    uq ~ F   sq ~ Ht new java.lang.Integer(1)q ~ Úpt PAGE_NUMBERpq ~ Úpsq ~0ppppsq ~ C   uq ~ F   sq ~ Ht new java.lang.Integer(1)q ~ Úpt 
COLUMN_NUMBERpq ~ Úpsq ~0sq ~ C   uq ~ F   sq ~ Ht new java.lang.Integer(1)q ~ Úppppsq ~ C   uq ~ F   sq ~ Ht new java.lang.Integer(0)q ~ Úpt REPORT_COUNTpq ~ Úpsq ~0sq ~ C   uq ~ F   sq ~ Ht new java.lang.Integer(1)q ~ Úppppsq ~ C   uq ~ F   sq ~ Ht new java.lang.Integer(0)q ~ Úpt 
PAGE_COUNTpq ~ Úpsq ~0sq ~ C   uq ~ F   sq ~ Ht new java.lang.Integer(1)q ~ Úppppsq ~ C   uq ~ F   sq ~ Ht new java.lang.Integer(0)q ~ Úpt COLUMN_COUNTpq ~ Úpsq ~0 sq ~ C   uq ~ F   sq ~ Ht totalt java.lang.Doublepppppt 	totalNotapq ~`pq ~ Çppppsq ~ sq ~    w   
sr 1net.sf.jasperreports.engine.base.JRBaseStaticText      'Ø L textq ~ xq ~ &   
       d  t    pq ~ q ~bpppppppppppppppsq ~ /   q ~ Msr java.lang.BooleanÍ rÕúî Z valuexpppppppppsq ~ 1psq ~ 5pppq ~iq ~iq ~epsq ~ ;pppq ~iq ~ipsq ~ 6pppq ~iq ~ipsq ~ >pppq ~iq ~ipsq ~ @pppq ~iq ~ipppppppppppppq ~ Bt Total:sq ~ "   
       R  Ø    pq ~ q ~bpppppppppppppppq ~fq ~ Mq ~hppppppppsq ~ 1psq ~ 5pppq ~qq ~qq ~ppsq ~ ;pppq ~qq ~qpsq ~ 6pppq ~qq ~qpsq ~ >pppq ~qq ~qpsq ~ @pppq ~qq ~qpppppppppppppq ~ B      ppsq ~ C   uq ~ F   sq ~ Ht 	totalNotat java.lang.Doublepppppppppt #,##0.00sr +net.sf.jasperreports.engine.base.JRBaseLine      'Ø B 	directionxr 5net.sf.jasperreports.engine.base.JRBaseGraphicElement      'Ø L fillq ~ L linePent #Lnet/sf/jasperreports/engine/JRPen;L penq ~ xq ~ *         +       pq ~ q ~bppppppppppsq ~ 7pppq ~pxp  w&   
pq ~ psq ~ sq ~    w   &sr ,net.sf.jasperreports.engine.base.JRBaseImage      'Ø $I 
bookmarkLevelB evaluationTimeB hyperlinkTargetB 
hyperlinkTypeZ isLazyB onErrorTypeL anchorNameExpressionq ~ L borderq ~ L borderColorq ~ 'L bottomBorderq ~ L bottomBorderColorq ~ 'L 
bottomPaddingq ~ (L evaluationGroupq ~ #L 
expressionq ~ L horizontalAlignmentq ~ L hyperlinkAnchorExpressionq ~ L hyperlinkPageExpressionq ~ [ hyperlinkParametersq ~ $L hyperlinkReferenceExpressionq ~ L hyperlinkTooltipExpressionq ~ L isUsingCacheq ~ %L 
leftBorderq ~ L leftBorderColorq ~ 'L leftPaddingq ~ (L lineBoxq ~ )L 
linkTargetq ~ L linkTypeq ~ L paddingq ~ (L rightBorderq ~ L rightBorderColorq ~ 'L rightPaddingq ~ (L 
scaleImageq ~ L 	topBorderq ~ L topBorderColorq ~ 'L 
topPaddingq ~ (L verticalAlignmentq ~ xq ~~   0     y        pq ~ q ~sr java.awt.Color¥3u F falphaI valueL cst Ljava/awt/color/ColorSpace;[ 	frgbvaluet [F[ fvalueq ~xp    ÿÿÿÿpppt image-1ppppppppsq ~ 7sq ~    ÿÿÿÿpppppq ~p      pppppppsq ~ C   	uq ~ F   sq ~ Ht logoPadraoRelatoriot java.io.InputStreampppppppq ~hpppsq ~ 1psq ~ 5pppq ~q ~q ~psq ~ ;pppq ~q ~psq ~ 6pppq ~q ~psq ~ >pppq ~q ~psq ~ @pppq ~q ~pppppppppppsq ~d   '       Ò   ì   pq ~ q ~pppppppppppppppsq ~ /   sq ~ q ~hppppppppsq ~ 1psq ~ 5pppq ~q ~q ~psq ~ ;pppq ~q ~psq ~ 6pppq ~q ~psq ~ >pppq ~q ~psq ~ @pppq ~q ~pppppppppppppq ~ Bt RelatÃ³rio de Comprasq ~ "   
       d   e   Jpq ~ q ~pppppppppppppppsq ~ /   ppppppppppsq ~ 1psq ~ 5pppq ~¤q ~¤q ~¢psq ~ ;pppq ~¤q ~¤psq ~ 6pppq ~¤q ~¤psq ~ >pppq ~¤q ~¤psq ~ @pppq ~¤q ~¤pppppppppppppp      ppsq ~ C   
uq ~ F   sq ~ Ht compra.numeroNFt java.lang.Stringppppppppppsq ~ "   
       ü   e   Wpq ~ q ~pppppppppppppppq ~£ppppppppppsq ~ 1psq ~ 5pppq ~°q ~°q ~¯psq ~ ;pppq ~°q ~°psq ~ 6pppq ~°q ~°psq ~ >pppq ~°q ~°psq ~ @pppq ~°q ~°pppppppppppppp      ppsq ~ C   uq ~ F   sq ~ Ht compra.fornecedor.pessoa.nomet java.lang.Stringppppppppppsq ~d   
       d       Wpq ~ q ~pppppppppppppppq ~£pq ~hppppppppsq ~ 1psq ~ 5pppq ~¼q ~¼q ~»psq ~ ;pppq ~¼q ~¼psq ~ 6pppq ~¼q ~¼psq ~ >pppq ~¼q ~¼psq ~ @pppq ~¼q ~¼ppppppppppppppt Fornecedor:sq ~d   
       d       Jpq ~ q ~pppppppppppppppq ~£pq ~hppppppppsq ~ 1psq ~ 5pppq ~Äq ~Äq ~Ãpsq ~ ;pppq ~Äq ~Äpsq ~ 6pppq ~Äq ~Äpsq ~ >pppq ~Äq ~Äpsq ~ @pppq ~Äq ~Äppppppppppppppt NÃºmero NF:sq ~d   
       d       epq ~ q ~pppppppppppppppq ~£pq ~hppppppppsq ~ 1psq ~ 5pppq ~Ìq ~Ìq ~Ëpsq ~ ;pppq ~Ìq ~Ìpsq ~ 6pppq ~Ìq ~Ìpsq ~ >pppq ~Ìq ~Ìpsq ~ @pppq ~Ìq ~Ìppppppppppppppt SituaÃ§Ã£o:sq ~ "   
       ý   d   epq ~ q ~pppppppppppppppq ~£ppppppppppsq ~ 1psq ~ 5pppq ~Ôq ~Ôq ~Ópsq ~ ;pppq ~Ôq ~Ôpsq ~ 6pppq ~Ôq ~Ôpsq ~ >pppq ~Ôq ~Ôpsq ~ @pppq ~Ôq ~Ôpppppppppppppp      ppsq ~ C   uq ~ F   sq ~ Ht compra.canceladasq ~ Ht 2.equals(new Boolean(true))  ? "Cancelada": "Ativa"t java.lang.Stringppppppppppsq ~d   
       d      spq ~ q ~pppppppppppppppq ~£pq ~hppppppppsq ~ 1psq ~ 5pppq ~âq ~âq ~ápsq ~ ;pppq ~âq ~âpsq ~ 6pppq ~âq ~âpsq ~ >pppq ~âq ~âpsq ~ @pppq ~âq ~âppppppppppppppt Contato:sq ~ "   
       ý   d   spq ~ q ~pppppppppppppppq ~£ppppppppppsq ~ 1psq ~ 5pppq ~êq ~êq ~épsq ~ ;pppq ~êq ~êpsq ~ 6pppq ~êq ~êpsq ~ >pppq ~êq ~êpsq ~ @pppq ~êq ~êpppppppppppppp      ppsq ~ C   
uq ~ F   sq ~ Ht compra.contatot java.lang.Stringppppppq ~hpppsq ~d   
       d      pq ~ q ~pppppppppppppppq ~£pq ~hppppppppsq ~ 1psq ~ 5pppq ~öq ~öq ~õpsq ~ ;pppq ~öq ~öpsq ~ 6pppq ~öq ~öpsq ~ >pppq ~öq ~öpsq ~ @pppq ~öq ~öppppppppppppppt Telefone Contato:sq ~ "   
       ý   d   pq ~ q ~pppppppppppppppq ~£ppppppppppsq ~ 1psq ~ 5pppq ~þq ~þq ~ýpsq ~ ;pppq ~þq ~þpsq ~ 6pppq ~þq ~þpsq ~ >pppq ~þq ~þpsq ~ @pppq ~þq ~þpppppppppppppp      ppsq ~ C   uq ~ F   sq ~ Ht compra.telefoneContatot java.lang.Stringppppppq ~hpppsq ~d   
       d      pq ~ q ~pppppppppppppppq ~£pq ~hppppppppsq ~ 1psq ~ 5pppq ~
q ~
q ~	psq ~ ;pppq ~
q ~
psq ~ 6pppq ~
q ~
psq ~ >pppq ~
q ~
psq ~ @pppq ~
q ~
ppppppppppppppt Data EmissÃ£o:sq ~ "   
          d   pq ~ q ~pppppppppppppppq ~£ppppppppppsq ~ 1psq ~ 5pppq ~q ~q ~psq ~ ;pppq ~q ~psq ~ 6pppq ~q ~psq ~ >pppq ~q ~psq ~ @pppq ~q ~pppppppppppppp      ppsq ~ C   uq ~ F   sq ~ Ht compra.dataEmissaot java.util.Dateppppppq ~hppt 
dd/MM/yyyysq ~ "          q   {   pq ~ q ~pt 
textField-208pppppppppppppppq ~hppppppppsq ~ 1psq ~ 5pppq ~ q ~ q ~psq ~ ;pppq ~ q ~ psq ~ 6pppq ~ q ~ psq ~ >pppq ~ q ~ psq ~ @pppq ~ q ~ ppppt Helvetica-Boldppppppppp      ppsq ~ C   uq ~ F   sq ~ Ht compra.empresa.nomet java.lang.Stringppppppsq ~g pppsq ~ "          q   {   pq ~ q ~pt 
textField-209pppppppppppppppq ~hppppppppsq ~ 1psq ~ 5pppq ~/q ~/q ~-psq ~ ;pppq ~/q ~/psq ~ 6pppq ~/q ~/psq ~ >pppq ~/q ~/psq ~ @pppq ~/q ~/ppppt Helvetica-Boldppppppppp      ppsq ~ C   uq ~ F   sq ~ Ht compra.empresa.enderecot java.lang.Stringppppppq ~,pppsq ~ "          q   {   "pq ~ q ~pt 
textField-210pppppppppppppppq ~hppppppppsq ~ 1psq ~ 5pppq ~=q ~=q ~;psq ~ ;pppq ~=q ~=psq ~ 6pppq ~=q ~=psq ~ >pppq ~=q ~=psq ~ @pppq ~=q ~=ppppt Helvetica-Boldppppppppp      ppsq ~ C   uq ~ F   sq ~ Ht compra.empresa.cidade.nomet java.lang.Stringppppppq ~,pppsq ~d           $   pq ~ q ~pt 
staticText-14sq ~ pppppppppppt Microsoft Sans Serifsq ~ /   	q ~ Mq ~hq ~hpq ~,pq ~,pppsq ~ 1psq ~ 5sq ~    ÿfffpppsq ~  sr java.lang.FloatÚíÉ¢Û<ðì F valuexq ~     q ~Nq ~Nq ~Ipsq ~ ;sq ~    ÿfffpppq ~Qsq ~R    q ~Nq ~Npsq ~ 6pppq ~Nq ~Npsq ~ >sq ~    ÿfffpppq ~Qsq ~R    q ~Nq ~Npsq ~ @sq ~    ÿfffpppq ~Qsq ~R    q ~Nq ~Nsq ~  pppt Helvetica-BoldObliqueppppppppsq ~ t eZillyon - Sistema Administrativo para Academias Desenvolvido por PACTO SoluÃ§Ãµes TecnolÃ³gicas Ltda.sq ~ "                +pq ~ q ~pt 
textField-212ppppppppppppt Arialsq ~ /   pq ~hppppppppsq ~ 1sq ~ /   sq ~ 5sq ~    ÿfffpppq ~Qsq ~R    q ~fq ~fq ~bpsq ~ ;sq ~    ÿfffpppq ~Qsq ~R    q ~fq ~fpsq ~ 6pppq ~fq ~fpsq ~ >sq ~    ÿfffpppq ~Qsq ~R    q ~fq ~fpsq ~ @sq ~    ÿ   pppq ~Qsq ~R    q ~fq ~fppppt Helvetica-Boldppppppppp      ppsq ~ C   uq ~ F   sq ~ Ht " " + sq ~ Ht PAGE_NUMBERsq ~ Ht  + ""t java.lang.Stringppppppq ~,pppsq ~d          `  Ê    pq ~ q ~pt 
staticText-15q ~Kpppppppppppt Microsoft Sans Serifsq ~ /   q ~ Mq ~hq ~hpq ~,pq ~,pppsq ~ 1psq ~ 5sq ~    ÿfffpppq ~Qsq ~R    q ~q ~q ~psq ~ ;sq ~    ÿfffpppq ~Qsq ~R    q ~q ~psq ~ 6pppq ~q ~psq ~ >sq ~    ÿfffpppq ~Qsq ~R    q ~q ~psq ~ @sq ~    ÿfffpppq ~Qsq ~R    q ~q ~q ~^pppt Helvetica-BoldObliqueppppppppq ~`t (0xx62) 3251-5820sq ~ "          K  Ô   +pq ~ q ~pt 
textField-211ppppppppppppt Arialsq ~ /   q ~ Mq ~hppppppppsq ~ 1sq ~ /   sq ~ 5sq ~    ÿfffpppq ~Qsq ~R    q ~q ~q ~psq ~ ;sq ~    ÿfffpppq ~Qsq ~R    q ~q ~psq ~ 6pppq ~q ~psq ~ >sq ~    ÿ   pppq ~Qsq ~R    q ~q ~psq ~ @sq ~    ÿ   pppq ~Qsq ~R    q ~q ~ppppt Helvetica-Boldppppppppp      ppsq ~ C   uq ~ F   sq ~ Ht "PÃ¡gina: " + sq ~ Ht PAGE_NUMBERsq ~ Ht 	 + " de "t java.lang.Stringppppppq ~,pppsq ~d          d       pq ~ q ~pppppppppppppppppq ~hppppppppsq ~ 1psq ~ 5pppq ~±q ~±q ~°psq ~ ;pppq ~±q ~±psq ~ 6pppq ~±q ~±psq ~ >pppq ~±q ~±psq ~ @pppq ~±q ~±pppppppppppppq ~ Bt Produtosq ~d          >   ×   pq ~ q ~ppppppppppppppppq ~ Mq ~hppppppppsq ~ 1psq ~ 5pppq ~¹q ~¹q ~¸psq ~ ;pppq ~¹q ~¹psq ~ 6pppq ~¹q ~¹psq ~ >pppq ~¹q ~¹psq ~ @pppq ~¹q ~¹pppppppppppppq ~ Bt 
Quantidadesq ~d          I  -   pq ~ q ~ppppppppppppppppq ~ Mq ~hppppppppsq ~ 1psq ~ 5pppq ~Áq ~Áq ~Àpsq ~ ;pppq ~Áq ~Ápsq ~ 6pppq ~Áq ~Ápsq ~ >pppq ~Áq ~Ápsq ~ @pppq ~Áq ~Ápppppppppppppq ~ Bt Valor UnitÃ¡riosq ~d          A     pq ~ q ~ppppppppppppppppq ~ Mq ~hppppppppsq ~ 1psq ~ 5pppq ~Éq ~Éq ~Èpsq ~ ;pppq ~Éq ~Épsq ~ 6pppq ~Éq ~Épsq ~ >pppq ~Éq ~Épsq ~ @pppq ~Éq ~Épppppppppppppq ~ Bt Descontosq ~d          <  î   pq ~ q ~ppppppppppppppppq ~ Mq ~hppppppppsq ~ 1psq ~ 5pppq ~Ñq ~Ñq ~Ðpsq ~ ;pppq ~Ñq ~Ñpsq ~ 6pppq ~Ñq ~Ñpsq ~ >pppq ~Ñq ~Ñpsq ~ @pppq ~Ñq ~Ñpppppppppppppq ~ Bt Totalsq ~}         +       °pq ~ q ~ppppppppppsq ~ 7pppq ~Øpxp  w&   ±ppsr 6net.sf.jasperreports.engine.design.JRReportCompileData      'Ø L crosstabCompileDataq ~ L datasetCompileDataq ~ L mainDatasetCompileDataq ~ xpsq ~%?@     w       xsq ~%?@     w       xur [B¬óøTà  xp  óÊþº¾   .= $RelatorioCompra_1363442738287_271231  ,net/sf/jasperreports/engine/fill/JREvaluator  parameter_REPORT_LOCALE 2Lnet/sf/jasperreports/engine/fill/JRFillParameter; parameter_JASPER_REPORT parameter_REPORT_VIRTUALIZER parameter_REPORT_TIME_ZONE parameter_REPORT_FILE_RESOLVER parameter_logoPadraoRelatorio parameter_REPORT_SCRIPTLET parameter_REPORT_PARAMETERS_MAP parameter_REPORT_CONNECTION parameter_REPORT_CLASS_LOADER parameter_REPORT_DATA_SOURCE $parameter_REPORT_URL_HANDLER_FACTORY parameter_IS_IGNORE_PAGINATION parameter_XML_DATE_PATTERN parameter_XML_DATA_DOCUMENT parameter_REPORT_FORMAT_FACTORY parameter_REPORT_MAX_COUNT parameter_XML_LOCALE parameter_REPORT_TEMPLATES parameter_XML_NUMBER_PATTERN  parameter_REPORT_RESOURCE_BUNDLE parameter_XML_TIME_ZONE field_total .Lnet/sf/jasperreports/engine/fill/JRFillField; field_desconto field_compra46contato field_compra46dataEmissao field_compra46cancelada field_compra46telefoneContato field_compra46numeroNF field_quantidade &field_compra46fornecedor46pessoa46nome field_valorUnitario field_compra46empresa46endereco field_produto46descricao #field_compra46empresa46cidade46nome field_compra46empresa46nome variable_PAGE_NUMBER 1Lnet/sf/jasperreports/engine/fill/JRFillVariable; variable_COLUMN_NUMBER variable_REPORT_COUNT variable_PAGE_COUNT variable_COLUMN_COUNT variable_totalNota <init> ()V Code 2 3
  5  	  7  	  9  	  ; 	 	  = 
 	  ?  	  A  	  C 
 	  E  	  G  	  I  	  K  	  M  	  O  	  Q  	  S  	  U  	  W  	  Y  	  [  	  ]  	  _  	  a  	  c  	  e  	  g   	  i ! 	  k " 	  m # 	  o $ 	  q % 	  s & 	  u ' 	  w ( 	  y ) 	  { * 	  } + ,	   - ,	   . ,	   / ,	   0 ,	   1 ,	   LineNumberTable customizedInit 0(Ljava/util/Map;Ljava/util/Map;Ljava/util/Map;)V 
initParams (Ljava/util/Map;)V  
   
initFields  
   initVars  
   
REPORT_LOCALE  
java/util/Map  get &(Ljava/lang/Object;)Ljava/lang/Object;     0net/sf/jasperreports/engine/fill/JRFillParameter   
JASPER_REPORT ¢ REPORT_VIRTUALIZER ¤ REPORT_TIME_ZONE ¦ REPORT_FILE_RESOLVER ¨ logoPadraoRelatorio ª REPORT_SCRIPTLET ¬ REPORT_PARAMETERS_MAP ® REPORT_CONNECTION ° REPORT_CLASS_LOADER ² REPORT_DATA_SOURCE ´ REPORT_URL_HANDLER_FACTORY ¶ IS_IGNORE_PAGINATION ¸ XML_DATE_PATTERN º XML_DATA_DOCUMENT ¼ REPORT_FORMAT_FACTORY ¾ REPORT_MAX_COUNT À 
XML_LOCALE Â REPORT_TEMPLATES Ä XML_NUMBER_PATTERN Æ REPORT_RESOURCE_BUNDLE È 
XML_TIME_ZONE Ê total Ì ,net/sf/jasperreports/engine/fill/JRFillField Î desconto Ð compra.contato Ò compra.dataEmissao Ô compra.cancelada Ö compra.telefoneContato Ø compra.numeroNF Ú 
quantidade Ü compra.fornecedor.pessoa.nome Þ 
valorUnitario à compra.empresa.endereco â produto.descricao ä compra.empresa.cidade.nome æ compra.empresa.nome è PAGE_NUMBER ê /net/sf/jasperreports/engine/fill/JRFillVariable ì 
COLUMN_NUMBER î REPORT_COUNT ð 
PAGE_COUNT ò COLUMN_COUNT ô 	totalNota ö evaluate (I)Ljava/lang/Object; 
Exceptions java/lang/Throwable û java/lang/Integer ý (I)V 2 ÿ
 þ  getValue ()Ljava/lang/Object;
 Ï java/lang/Double
 ¡ java/io/InputStream	 java/lang/String java/lang/Boolean
 (Z)V 2
 equals (Ljava/lang/Object;)Z
 	Cancelada Ativa java/util/Date java/lang/StringBuffer   (Ljava/lang/String;)V 2 
!
 í append ,(Ljava/lang/Object;)Ljava/lang/StringBuffer;$%
& toString ()Ljava/lang/String;()
* 	PÃ¡gina: ,  de . ,(Ljava/lang/String;)Ljava/lang/StringBuffer;$0
1 evaluateOld getOldValue4
 Ï5
 í5 evaluateEstimated getEstimatedValue9
 í: 
SourceFile !     *                 	     
               
                                                                                                !     "     #     $     %     &     '     (     )     *     + ,    - ,    . ,    / ,    0 ,    1 ,     2 3  4       ×*· 6*µ 8*µ :*µ <*µ >*µ @*µ B*µ D*µ F*µ H*µ J*µ L*µ N*µ P*µ R*µ T*µ V*µ X*µ Z*µ \*µ ^*µ `*µ b*µ d*µ f*µ h*µ j*µ l*µ n*µ p*µ r*µ t*µ v*µ x*µ z*µ |*µ ~*µ *µ *µ *µ *µ *µ ±       ² ,      	          "  '   , ! 1 " 6 # ; $ @ % E & J ' O ( T ) Y * ^ + c , h - m . r / w 0 | 1  2  3  4  5  6  7  8 ¤ 9 © : ® ; ³ < ¸ = ½ > Â ? Ç @ Ì A Ñ B Ö      4   4     *+· *,· *-· ±           N  O 
 P  Q     4  »    K*+¹  À ¡µ 8*+£¹  À ¡µ :*+¥¹  À ¡µ <*+§¹  À ¡µ >*+©¹  À ¡µ @*+«¹  À ¡µ B*+­¹  À ¡µ D*+¯¹  À ¡µ F*+±¹  À ¡µ H*+³¹  À ¡µ J*+µ¹  À ¡µ L*+·¹  À ¡µ N*+¹¹  À ¡µ P*+»¹  À ¡µ R*+½¹  À ¡µ T*+¿¹  À ¡µ V*+Á¹  À ¡µ X*+Ã¹  À ¡µ Z*+Å¹  À ¡µ \*+Ç¹  À ¡µ ^*+É¹  À ¡µ `*+Ë¹  À ¡µ b±       ^    Y  Z  [ - \ < ] K ^ Z _ i ` x a  b  c ¥ d ´ e Ã f Ò g á h ð i ÿ j k l, m; nJ o     4  #     Ó*+Í¹  À Ïµ d*+Ñ¹  À Ïµ f*+Ó¹  À Ïµ h*+Õ¹  À Ïµ j*+×¹  À Ïµ l*+Ù¹  À Ïµ n*+Û¹  À Ïµ p*+Ý¹  À Ïµ r*+ß¹  À Ïµ t*+á¹  À Ïµ v*+ã¹  À Ïµ x*+å¹  À Ïµ z*+ç¹  À Ïµ |*+é¹  À Ïµ ~±       >    w  x  y - z < { K | Z } i ~ x      ¥  ´  Ã  Ò      4        [*+ë¹  À íµ *+ï¹  À íµ *+ñ¹  À íµ *+ó¹  À íµ *+õ¹  À íµ *+÷¹  À íµ ±                -  <  K  Z   ø ù  ú     ü 4      "Mª            y            ©   µ   Á   Í   Ù   ç   õ      6  D  R  `  n  |    ¨  Ì  Ú  è  ö    » þY·M§» þY·M§» þY·M§» þY·M§w» þY·M§k» þY·M§_» þY·M§S» þY·M§G*´ d¶ÀM§9*´ B¶À
M§+*´ p¶ÀM§*´ t¶ÀM§*´ l¶À»Y·¶ 	§ M§ ê*´ h¶ÀM§ Ü*´ n¶ÀM§ Î*´ j¶ÀM§ À*´ ~¶ÀM§ ²*´ x¶ÀM§ ¤*´ |¶ÀM§ »Y·"*´ ¶#À þ¶'¶+M§ x»Y-·"*´ ¶#À þ¶'/¶2¶+M§ T*´ z¶ÀM§ F*´ r¶À þM§ 8*´ f¶ÀM§ **´ d¶ÀM§ *´ v¶ÀM§ *´ ¶#ÀM,°       â 8      | ¡  ¢  ¦  §  «  ¬   ° © ± ¬ µ µ ¶ ¸ º Á » Ä ¿ Í À Ð Ä Ù Å Ü É ç Ê ê Î õ Ï ø Ó Ô Ø Ù Ý6 Þ9 âD ãG çR èU ì` íc ñn òq ö| ÷ û ü ¨«ÌÏ
ÚÝèëöù# + 3 ù  ú     ü 4      "Mª            y            ©   µ   Á   Í   Ù   ç   õ      6  D  R  `  n  |    ¨  Ì  Ú  è  ö    » þY·M§» þY·M§» þY·M§» þY·M§w» þY·M§k» þY·M§_» þY·M§S» þY·M§G*´ d¶6ÀM§9*´ B¶À
M§+*´ p¶6ÀM§*´ t¶6ÀM§*´ l¶6À»Y·¶ 	§ M§ ê*´ h¶6ÀM§ Ü*´ n¶6ÀM§ Î*´ j¶6ÀM§ À*´ ~¶6ÀM§ ²*´ x¶6ÀM§ ¤*´ |¶6ÀM§ »Y·"*´ ¶7À þ¶'¶+M§ x»Y-·"*´ ¶7À þ¶'/¶2¶+M§ T*´ z¶6ÀM§ F*´ r¶6À þM§ 8*´ f¶6ÀM§ **´ d¶6ÀM§ *´ v¶6ÀM§ *´ ¶7ÀM,°       â 8  4 6 |: ; ? @ D E  I ©J ¬N µO ¸S ÁT ÄX ÍY Ð] Ù^ Üb çc êg õh ølmqrv6w9{D|GRU`cnq|¨«ÌÏ£Ú¤Ý¨è©ë­ö®ù²³·¸¼ Ä 8 ù  ú     ü 4      "Mª            y            ©   µ   Á   Í   Ù   ç   õ      6  D  R  `  n  |    ¨  Ì  Ú  è  ö    » þY·M§» þY·M§» þY·M§» þY·M§w» þY·M§k» þY·M§_» þY·M§S» þY·M§G*´ d¶ÀM§9*´ B¶À
M§+*´ p¶ÀM§*´ t¶ÀM§*´ l¶À»Y·¶ 	§ M§ ê*´ h¶ÀM§ Ü*´ n¶ÀM§ Î*´ j¶ÀM§ À*´ ~¶ÀM§ ²*´ x¶ÀM§ ¤*´ |¶ÀM§ »Y·"*´ ¶;À þ¶'¶+M§ x»Y-·"*´ ¶;À þ¶'/¶2¶+M§ T*´ z¶ÀM§ F*´ r¶À þM§ 8*´ f¶ÀM§ **´ d¶ÀM§ *´ v¶ÀM§ *´ ¶;ÀM,°       â 8  Í Ï |Ó Ô Ø Ù Ý Þ  â ©ã ¬ç µè ¸ì Áí Äñ Íò Ðö Ù÷ Üû çü ê  õ ø
69DGRU`c#n$q(|)-.2¨3«7Ì8Ï<Ú=ÝAèBëFöGùKLPQU ] <    t _1363442738287_271231t 2net.sf.jasperreports.engine.design.JRJavacCompiler
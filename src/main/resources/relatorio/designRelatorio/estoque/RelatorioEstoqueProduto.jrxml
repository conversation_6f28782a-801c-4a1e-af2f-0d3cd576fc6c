<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="RelatorioEstoqueProduto" pageWidth="595" pageHeight="842" whenNoDataType="NoDataSection" columnWidth="555" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20">
	<property name="ireport.zoom" value="1.5"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="135"/>
	<subDataset name="dataset1"/>
	<parameter name="logoPadraoRelatorio" class="java.io.InputStream" isForPrompting="false"/>
	<parameter name="tipoVisualizacao" class="java.lang.Integer"/>
	<parameter name="nomeCategoria" class="java.lang.String"/>
	<parameter name="statusProduto" class="java.lang.String"/>
	<parameter name="ordenacao" class="java.lang.String" isForPrompting="false"/>
	<parameter name="valorImpresso" class="java.lang.String" isForPrompting="false"/>
	<parameter name="totalizadores" class="java.lang.Object"/>
	<parameter name="SUBREPORT_DIR" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA["C:\\PactoJ\\Desenvolvimento\\Sistemas\\ZillyonWeb\\tronco-novo\\src\\main\\resources\\relatorio\\designRelatorio\\estoque\\"]]></defaultValueExpression>
	</parameter>
	<queryString language="xPath">
		<![CDATA[]]>
	</queryString>
	<field name="empresa.nome" class="java.lang.String"/>
	<field name="empresa.cidade.nome" class="java.lang.String"/>
	<field name="empresa.endereco" class="java.lang.String"/>
	<field name="produto.descricao" class="java.lang.String"/>
	<field name="estoque" class="java.lang.Integer"/>
	<field name="estoqueMinimo" class="java.lang.Integer"/>
	<field name="produto.categoriaProduto.descricao" class="java.lang.String"/>
	<field name="produto.codigo" class="java.lang.Integer"/>
	<field name="valorUnitario" class="java.lang.Double"/>
	<field name="valorTotalEstoque" class="java.lang.Double"/>
	<field name="pontos" class="java.lang.Integer"/>
	<sortField name="produto.categoriaProduto.descricao"/>
	<variable name="valorTotalGeral" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[$F{valorTotalEstoque}]]></variableExpression>
	</variable>
	<variable name="totalEstoque" class="java.lang.Integer" calculation="Sum">
		<variableExpression><![CDATA[$F{estoque}]]></variableExpression>
	</variable>
	<group name="categoria">
		<groupExpression><![CDATA[$F{produto.categoriaProduto.descricao}]]></groupExpression>
		<groupHeader>
			<band height="16">
				<textField>
					<reportElement x="1" y="1" width="406" height="14"/>
					<textElement>
						<font isBold="true"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA["CATEGORIA: " + $F{produto.categoriaProduto.descricao}]]></textFieldExpression>
				</textField>
				<line>
					<reportElement x="0" y="15" width="555" height="1"/>
				</line>
			</band>
		</groupHeader>
	</group>
	<background>
		<band splitType="Stretch"/>
	</background>
	<pageHeader>
		<band height="244">
			<staticText>
				<reportElement stretchType="RelativeToBandHeight" x="1" y="224" width="44" height="20" isPrintWhenDetailOverflows="true"/>
				<textElement verticalAlignment="Bottom">
					<font isBold="true"/>
				</textElement>
				<text><![CDATA[Codigo]]></text>
			</staticText>
			<staticText>
				<reportElement stretchType="RelativeToTallestObject" x="226" y="223" width="61" height="20" isPrintWhenDetailOverflows="true"/>
				<textElement textAlignment="Right" verticalAlignment="Bottom">
					<font size="10" isBold="true"/>
				</textElement>
				<text><![CDATA[Est. Mínimo]]></text>
			</staticText>
			<staticText>
				<reportElement x="439" y="223" width="46" height="20"/>
				<textElement textAlignment="Right" verticalAlignment="Bottom">
					<font size="10" isBold="true"/>
				</textElement>
				<text><![CDATA[Vl. Total]]></text>
			</staticText>
			<staticText>
				<reportElement x="45" y="224" width="169" height="20"/>
				<textElement verticalAlignment="Bottom">
					<font isBold="true"/>
				</textElement>
				<text><![CDATA[Produto]]></text>
			</staticText>
			<staticText>
				<reportElement stretchType="RelativeToTallestObject" x="292" y="224" width="71" height="20" isPrintWhenDetailOverflows="true"/>
				<textElement textAlignment="Right" verticalAlignment="Bottom">
					<font size="10" isBold="true"/>
				</textElement>
				<text><![CDATA[Est. Atual]]></text>
			</staticText>
			<staticText>
				<reportElement x="370" y="223" width="62" height="20"/>
				<textElement textAlignment="Right" verticalAlignment="Bottom">
					<font size="10" isBold="true"/>
				</textElement>
				<text><![CDATA[Vl. Unitário]]></text>
			</staticText>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-208" x="123" y="6" width="113" height="14"/>
				<textElement>
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{empresa.nome}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-209" x="123" y="22" width="113" height="14"/>
				<textElement>
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{empresa.endereco}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-210" x="123" y="38" width="113" height="14"/>
				<textElement>
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{empresa.cidade.nome}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement key="staticText-14" mode="Opaque" x="292" y="5" width="262" height="23"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Top" lineSpacing="Single">
					<font fontName="Microsoft Sans Serif" size="9" isBold="true" isItalic="true" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica-BoldOblique"/>
				</textElement>
				<text><![CDATA[Zillyon - Sistema Administrativo para Academias Desenvolvido por PACTO Soluções Tecnológicas Ltda.]]></text>
			</staticText>
			<textField evaluationTime="Report" isBlankWhenNull="false">
				<reportElement key="textField-212" x="543" y="47" width="29" height="17"/>
				<box bottomPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement>
					<font fontName="Arial" size="6" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[" " + $V{PAGE_NUMBER} + ""]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement key="staticText-15" mode="Opaque" x="458" y="26" width="96" height="12"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Top" lineSpacing="Single">
					<font fontName="Microsoft Sans Serif" size="6" isBold="true" isItalic="true" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica-BoldOblique"/>
				</textElement>
				<text><![CDATA[(0xx62) 3251-5820]]></text>
			</staticText>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-211" x="468" y="47" width="75" height="17"/>
				<box bottomPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right">
					<font fontName="Arial" size="6" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA["Página: " + $V{PAGE_NUMBER} + " de "]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="117" y="120" width="305" height="20"/>
				<textElement verticalAlignment="Bottom"/>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{tipoVisualizacao}.equals(new Integer(1)) ? "Todos Produtos" :"Somente Produtos com estoque mínimo"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="118" y="141" width="305" height="20"/>
				<textElement verticalAlignment="Bottom"/>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{nomeCategoria} == null ? "Todos" :  $P{nomeCategoria}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="1" y="161" width="117" height="20"/>
				<textElement verticalAlignment="Bottom">
					<font isBold="true"/>
				</textElement>
				<text><![CDATA[Status do Produto:]]></text>
			</staticText>
			<textField>
				<reportElement x="118" y="161" width="305" height="20"/>
				<textElement verticalAlignment="Bottom"/>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{statusProduto} == null ? "Todos" :  $P{statusProduto}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="118" y="181" width="305" height="20"/>
				<textElement verticalAlignment="Bottom"/>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{ordenacao}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="1" y="181" width="117" height="20"/>
				<textElement verticalAlignment="Bottom">
					<font isBold="true"/>
				</textElement>
				<text><![CDATA[Ordenado por:]]></text>
			</staticText>
			<image isUsingCache="true" onErrorType="Blank" evaluationTime="Page">
				<reportElement key="image-1" positionType="Float" stretchType="RelativeToBandHeight" x="0" y="4" width="121" height="48" isPrintWhenDetailOverflows="true" forecolor="#FFFFFF"/>
				<graphicElement>
					<pen lineColor="#FFFFFF"/>
				</graphicElement>
				<imageExpression class="java.io.InputStream"><![CDATA[$P{logoPadraoRelatorio}]]></imageExpression>
			</image>
			<staticText>
				<reportElement x="1" y="64" width="553" height="31"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="16" isBold="true"/>
				</textElement>
				<text><![CDATA[Relatório de Posição de Estoque]]></text>
			</staticText>
			<staticText>
				<reportElement x="1" y="120" width="117" height="20"/>
				<textElement verticalAlignment="Bottom">
					<font isBold="true"/>
				</textElement>
				<text><![CDATA[Posição do Estoque:]]></text>
			</staticText>
			<staticText>
				<reportElement x="1" y="141" width="117" height="20"/>
				<textElement verticalAlignment="Bottom">
					<font isBold="true"/>
				</textElement>
				<text><![CDATA[Categoria Produto:]]></text>
			</staticText>
			<line>
				<reportElement x="0" y="243" width="555" height="1"/>
			</line>
			<textField pattern="dd/MM/yyyy HH.mm.ss" isBlankWhenNull="false">
				<reportElement key="dataRel-1" mode="Transparent" x="458" y="34" width="97" height="12" backcolor="#FFFFFF"/>
				<box bottomPadding="0">
					<pen lineColor="#FFFFFF"/>
					<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#FFFFFF"/>
					<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#FFFFFF"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#FFFFFF"/>
					<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#FFFFFF"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="6" isBold="true" isItalic="false" isStrikeThrough="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.util.Date"><![CDATA[new Date()]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="1" y="201" width="117" height="20"/>
				<textElement verticalAlignment="Bottom">
					<font isBold="true"/>
				</textElement>
				<text><![CDATA[Valor Impresso:]]></text>
			</staticText>
			<textField>
				<reportElement x="118" y="201" width="305" height="20"/>
				<textElement verticalAlignment="Bottom"/>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{valorImpresso}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="508" y="223" width="40" height="20"/>
				<textElement textAlignment="Right" verticalAlignment="Bottom">
					<font size="10" isBold="true"/>
				</textElement>
				<text><![CDATA[Pontos]]></text>
			</staticText>
		</band>
	</pageHeader>
	<detail>
		<band height="14" splitType="Stretch">
			<textField pattern="">
				<reportElement x="303" y="1" width="46" height="13"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.Integer"><![CDATA[$F{estoque}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement x="38" y="0" width="198" height="13"/>
				<textElement verticalAlignment="Middle">
					<font size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{produto.descricao}]]></textFieldExpression>
			</textField>
			<textField pattern="">
				<reportElement x="226" y="1" width="66" height="13"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.Integer"><![CDATA[$F{estoqueMinimo}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00">
				<reportElement x="357" y="1" width="67" height="13"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.Double"><![CDATA[$F{valorUnitario}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00">
				<reportElement x="428" y="1" width="85" height="13"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.Double"><![CDATA[$F{valorTotalEstoque}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="10" y="1" width="28" height="13" isRemoveLineWhenBlank="true"/>
				<textElement verticalAlignment="Middle">
					<font size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[(new DecimalFormat("0000")).format($F{produto.codigo})]]></textFieldExpression>
			</textField>
			<textField pattern="">
				<reportElement x="514" y="0" width="41" height="13"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.Integer"><![CDATA[$F{pontos}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<summary>
		<band height="98" splitType="Stretch">
			<subreport>
				<reportElement x="0" y="14" width="552" height="54"/>
				<subreportParameter name="totalestoque">
					<subreportParameterExpression><![CDATA[$V{totalEstoque}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="totalgeral">
					<subreportParameterExpression><![CDATA[$V{valorTotalGeral}]]></subreportParameterExpression>
				</subreportParameter>
				<dataSourceExpression><![CDATA[$P{totalizadores}]]></dataSourceExpression>
				<subreportExpression class="java.lang.String"><![CDATA[$P{SUBREPORT_DIR} + "RelatorioEstoqueProduto_subTotalizador.jasper"]]></subreportExpression>
			</subreport>
		</band>
	</summary>
	<noData>
		<band height="50">
			<staticText>
				<reportElement x="1" y="13" width="542" height="20"/>
				<textElement textAlignment="Center">
					<font size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[Não há dados para serem exibidos ! verifique os parâmetros informados.]]></text>
			</staticText>
		</band>
	</noData>
</jasperReport>

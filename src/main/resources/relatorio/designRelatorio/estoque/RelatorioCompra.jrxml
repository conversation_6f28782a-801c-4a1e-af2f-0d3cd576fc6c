<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="RelatorioCompra" pageWidth="595" pageHeight="842" columnWidth="555" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" isIgnorePagination="true">
	<property name="ireport.zoom" value="1.5"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="121"/>
	<parameter name="logoPadraoRelatorio" class="java.io.InputStream" isForPrompting="false"/>
	<field name="compra.numeroNF" class="java.lang.String"/>
	<field name="compra.empresa.nome" class="java.lang.String"/>
	<field name="compra.fornecedor.pessoa.nome" class="java.lang.String"/>
	<field name="compra.cancelada" class="java.lang.Boolean"/>
	<field name="compra.contato" class="java.lang.String"/>
	<field name="compra.telefoneContato" class="java.lang.String"/>
	<field name="compra.dataEmissao" class="java.util.Date"/>
	<field name="compra.empresa.cidade.nome" class="java.lang.String"/>
	<field name="compra.empresa.endereco" class="java.lang.String"/>
	<field name="produto.descricao" class="java.lang.String"/>
	<field name="quantidade" class="java.lang.Integer"/>
	<field name="valorUnitario" class="java.lang.Double"/>
	<field name="desconto" class="java.lang.Double"/>
	<field name="total" class="java.lang.Double"/>
	<variable name="totalNota" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[$F{total}]]></variableExpression>
	</variable>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band height="236" splitType="Stretch">
			<image isUsingCache="true" onErrorType="Blank" evaluationTime="Page">
				<reportElement key="image-1" positionType="Float" stretchType="RelativeToBandHeight" x="0" y="0" width="121" height="48" isPrintWhenDetailOverflows="true" forecolor="#FFFFFF"/>
				<graphicElement>
					<pen lineColor="#FFFFFF"/>
				</graphicElement>
				<imageExpression class="java.io.InputStream"><![CDATA[$P{logoPadraoRelatorio}]]></imageExpression>
			</image>
			<staticText>
				<reportElement x="236" y="24" width="210" height="39"/>
				<textElement textAlignment="Center" verticalAlignment="Bottom">
					<font size="16" isBold="true"/>
				</textElement>
				<text><![CDATA[Relatório de Compra]]></text>
			</staticText>
			<textField>
				<reportElement x="101" y="74" width="100" height="20"/>
				<textElement/>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{compra.numeroNF}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="101" y="94" width="252" height="20"/>
				<textElement/>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{compra.fornecedor.pessoa.nome}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="0" y="94" width="100" height="20"/>
				<textElement>
					<font isBold="true"/>
				</textElement>
				<text><![CDATA[Fornecedor:]]></text>
			</staticText>
			<staticText>
				<reportElement x="0" y="74" width="100" height="20"/>
				<textElement>
					<font isBold="true"/>
				</textElement>
				<text><![CDATA[Número NF:]]></text>
			</staticText>
			<staticText>
				<reportElement x="0" y="114" width="100" height="20"/>
				<textElement>
					<font isBold="true"/>
				</textElement>
				<text><![CDATA[Situação:]]></text>
			</staticText>
			<textField>
				<reportElement x="100" y="114" width="253" height="20"/>
				<textElement/>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{compra.cancelada}.equals(new Boolean(true))  ? "Cancelada": "Ativa"]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="1" y="134" width="100" height="20"/>
				<textElement>
					<font isBold="true"/>
				</textElement>
				<text><![CDATA[Contato:]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="100" y="134" width="253" height="20"/>
				<textElement/>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{compra.contato}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="1" y="154" width="100" height="20"/>
				<textElement>
					<font isBold="true"/>
				</textElement>
				<text><![CDATA[Telefone Contato:]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="100" y="154" width="253" height="20"/>
				<textElement/>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{compra.telefoneContato}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="1" y="174" width="100" height="20"/>
				<textElement>
					<font isBold="true"/>
				</textElement>
				<text><![CDATA[Data Emissão:]]></text>
			</staticText>
			<textField pattern="dd/MM/yyyy" isBlankWhenNull="true">
				<reportElement x="100" y="174" width="159" height="20"/>
				<textElement/>
				<textFieldExpression class="java.util.Date"><![CDATA[$F{compra.dataEmissao}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-208" x="123" y="2" width="113" height="14"/>
				<textElement>
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{compra.empresa.nome}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-209" x="123" y="18" width="113" height="14"/>
				<textElement>
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{compra.empresa.endereco}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-210" x="123" y="34" width="113" height="14"/>
				<textElement>
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{compra.empresa.cidade.nome}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement key="staticText-14" mode="Opaque" x="292" y="1" width="262" height="23"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Top" lineSpacing="Single">
					<font fontName="Microsoft Sans Serif" size="9" isBold="true" isItalic="true" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica-BoldOblique"/>
				</textElement>
				<text><![CDATA[Zillyon - Sistema Administrativo para Academias Desenvolvido por PACTO Soluções Tecnológicas Ltda.]]></text>
			</staticText>
			<textField evaluationTime="Report" isBlankWhenNull="false">
				<reportElement key="textField-212" x="544" y="43" width="11" height="17"/>
				<box bottomPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement>
					<font fontName="Arial" size="6" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[" " + $V{PAGE_NUMBER} + ""]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement key="staticText-15" mode="Opaque" x="458" y="32" width="96" height="12"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Top" lineSpacing="Single">
					<font fontName="Microsoft Sans Serif" size="6" isBold="true" isItalic="true" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica-BoldOblique"/>
				</textElement>
				<text><![CDATA[(0xx62) 3251-5820]]></text>
			</staticText>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-211" x="468" y="43" width="75" height="17"/>
				<box bottomPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right">
					<font fontName="Arial" size="6" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA["Página: " + $V{PAGE_NUMBER} + " de "]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="0" y="216" width="100" height="20"/>
				<textElement verticalAlignment="Bottom">
					<font isBold="true"/>
				</textElement>
				<text><![CDATA[Produto]]></text>
			</staticText>
			<staticText>
				<reportElement x="215" y="216" width="62" height="20"/>
				<textElement textAlignment="Right" verticalAlignment="Bottom">
					<font isBold="true"/>
				</textElement>
				<text><![CDATA[Quantidade]]></text>
			</staticText>
			<staticText>
				<reportElement x="301" y="216" width="73" height="20"/>
				<textElement textAlignment="Right" verticalAlignment="Bottom">
					<font isBold="true"/>
				</textElement>
				<text><![CDATA[Valor Unitário]]></text>
			</staticText>
			<staticText>
				<reportElement x="393" y="216" width="65" height="20"/>
				<textElement textAlignment="Right" verticalAlignment="Bottom">
					<font isBold="true"/>
				</textElement>
				<text><![CDATA[Desconto]]></text>
			</staticText>
			<staticText>
				<reportElement x="494" y="216" width="60" height="20"/>
				<textElement textAlignment="Right" verticalAlignment="Bottom">
					<font isBold="true"/>
				</textElement>
				<text><![CDATA[Total]]></text>
			</staticText>
			<line>
				<reportElement x="0" y="235" width="555" height="1"/>
			</line>
		</band>
	</title>
	<detail>
		<band height="20" splitType="Stretch">
			<textField>
				<reportElement x="0" y="0" width="215" height="20"/>
				<textElement verticalAlignment="Bottom"/>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{produto.descricao}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="215" y="0" width="62" height="20"/>
				<textElement textAlignment="Right" verticalAlignment="Bottom"/>
				<textFieldExpression class="java.lang.Integer"><![CDATA[$F{quantidade}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00">
				<reportElement x="393" y="0" width="65" height="20"/>
				<textElement textAlignment="Right" verticalAlignment="Bottom"/>
				<textFieldExpression class="java.lang.Double"><![CDATA[$F{desconto}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00">
				<reportElement x="472" y="0" width="82" height="20"/>
				<textElement textAlignment="Right" verticalAlignment="Bottom"/>
				<textFieldExpression class="java.lang.Double"><![CDATA[$F{total}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00">
				<reportElement x="301" y="0" width="73" height="20"/>
				<textElement textAlignment="Right" verticalAlignment="Bottom"/>
				<textFieldExpression class="java.lang.Double"><![CDATA[$F{valorUnitario}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<summary>
		<band height="22" splitType="Stretch">
			<staticText>
				<reportElement x="372" y="0" width="100" height="20"/>
				<textElement textAlignment="Right" verticalAlignment="Bottom">
					<font isBold="true"/>
				</textElement>
				<text><![CDATA[Total:]]></text>
			</staticText>
			<textField pattern="#,##0.00">
				<reportElement x="472" y="0" width="82" height="20"/>
				<textElement textAlignment="Right" verticalAlignment="Bottom">
					<font isBold="true"/>
				</textElement>
				<textFieldExpression class="java.lang.Double"><![CDATA[$V{totalNota}]]></textFieldExpression>
			</textField>
			<line>
				<reportElement x="0" y="20" width="555" height="1"/>
			</line>
		</band>
	</summary>
</jasperReport>

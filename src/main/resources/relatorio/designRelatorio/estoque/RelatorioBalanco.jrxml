<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="RelatorioBalanco" pageWidth="595" pageHeight="842" columnWidth="555" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" isIgnorePagination="true">
	<property name="ireport.zoom" value="1.5"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="138"/>
	<parameter name="logoPadraoRelatorio" class="java.io.InputStream" isForPrompting="false"/>
	<field name="balanco.empresa.nome" class="java.lang.String"/>
	<field name="balanco.cancelado" class="java.lang.Boolean"/>
	<field name="balanco.dataCadastro" class="java.util.Date"/>
	<field name="balanco.empresa.cidade.nome" class="java.lang.String"/>
	<field name="balanco.empresa.endereco" class="java.lang.String"/>
	<field name="produto.descricao" class="java.lang.String"/>
	<field name="qtdeBalanco" class="java.lang.Integer"/>
	<field name="qtdeEstoqueAnterior" class="java.lang.Integer"/>
	<field name="balanco.usuarioCadastro.nome" class="java.lang.String"/>
	<field name="balanco.usuarioCancelamento.nome" class="java.lang.String"/>
	<field name="balanco.dataCancelamento" class="java.util.Date"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band height="236" splitType="Stretch">
			<image isUsingCache="true" onErrorType="Blank" evaluationTime="Page">
				<reportElement key="image-1" positionType="Float" stretchType="RelativeToBandHeight" x="0" y="0" width="121" height="48" isPrintWhenDetailOverflows="true" forecolor="#FFFFFF"/>
				<graphicElement>
					<pen lineColor="#FFFFFF"/>
				</graphicElement>
				<imageExpression class="java.io.InputStream"><![CDATA[$P{logoPadraoRelatorio}]]></imageExpression>
			</image>
			<staticText>
				<reportElement x="236" y="24" width="210" height="39"/>
				<textElement textAlignment="Center" verticalAlignment="Bottom">
					<font size="16" isBold="true"/>
				</textElement>
				<text><![CDATA[Relatório de Balanço]]></text>
			</staticText>
			<textField pattern="dd/MM/yyyy">
				<reportElement x="118" y="74" width="176" height="20"/>
				<textElement/>
				<textFieldExpression class="java.util.Date"><![CDATA[$F{balanco.dataCadastro}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="0" y="74" width="117" height="20"/>
				<textElement>
					<font isBold="true"/>
				</textElement>
				<text><![CDATA[Data Balanço:]]></text>
			</staticText>
			<staticText>
				<reportElement x="0" y="95" width="117" height="20"/>
				<textElement>
					<font isBold="true"/>
				</textElement>
				<text><![CDATA[Situação:]]></text>
			</staticText>
			<textField>
				<reportElement x="117" y="95" width="253" height="20"/>
				<textElement/>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{balanco.cancelado}.equals(new Boolean(true))  ? "Cancelado": "Ativo"]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="1" y="116" width="117" height="20"/>
				<textElement>
					<font isBold="true"/>
				</textElement>
				<text><![CDATA[Usuário Cadastro:]]></text>
			</staticText>
			<textField>
				<reportElement x="117" y="116" width="253" height="20"/>
				<textElement/>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{balanco.usuarioCadastro.nome}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="1" y="137" width="117" height="20">
					<printWhenExpression><![CDATA[$F{balanco.dataCancelamento} != null ? new Boolean(true) : new Boolean(false)]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font isBold="true"/>
				</textElement>
				<text><![CDATA[Data Cancelamento:]]></text>
			</staticText>
			<textField pattern="dd/MM/yyyy" isBlankWhenNull="true">
				<reportElement x="117" y="137" width="253" height="20"/>
				<textElement/>
				<textFieldExpression class="java.util.Date"><![CDATA[$F{balanco.dataCancelamento}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="1" y="157" width="117" height="20">
					<printWhenExpression><![CDATA[$F{balanco.dataCancelamento} != null ? new Boolean(true) : new Boolean(false)]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font isBold="true"/>
				</textElement>
				<text><![CDATA[Usuario Cancelamento:]]></text>
			</staticText>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement x="117" y="157" width="253" height="20"/>
				<textElement/>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{balanco.usuarioCancelamento.nome}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-208" x="123" y="2" width="113" height="14"/>
				<textElement>
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{balanco.empresa.nome}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-209" x="123" y="18" width="113" height="14"/>
				<textElement>
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{balanco.empresa.endereco}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-210" x="123" y="34" width="113" height="14"/>
				<textElement>
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{balanco.empresa.cidade.nome}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement key="staticText-14" mode="Opaque" x="292" y="1" width="262" height="23"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Top" lineSpacing="Single">
					<font fontName="Microsoft Sans Serif" size="9" isBold="true" isItalic="true" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica-BoldOblique"/>
				</textElement>
				<text><![CDATA[Zillyon - Sistema Administrativo para Academias Desenvolvido por PACTO Soluções Tecnológicas Ltda.]]></text>
			</staticText>
			<textField evaluationTime="Report" isBlankWhenNull="false">
				<reportElement key="textField-212" x="544" y="43" width="11" height="17"/>
				<box bottomPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement>
					<font fontName="Arial" size="6" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[" " + $V{PAGE_NUMBER} + ""]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement key="staticText-15" mode="Opaque" x="458" y="32" width="96" height="12"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Top" lineSpacing="Single">
					<font fontName="Microsoft Sans Serif" size="6" isBold="true" isItalic="true" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica-BoldOblique"/>
				</textElement>
				<text><![CDATA[(0xx62) 3251-5820]]></text>
			</staticText>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-211" x="468" y="43" width="75" height="17"/>
				<box bottomPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right">
					<font fontName="Arial" size="6" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA["Página: " + $V{PAGE_NUMBER} + " de "]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="0" y="216" width="100" height="20"/>
				<textElement verticalAlignment="Bottom">
					<font isBold="true"/>
				</textElement>
				<text><![CDATA[Produto]]></text>
			</staticText>
			<staticText>
				<reportElement x="285" y="216" width="136" height="20"/>
				<textElement textAlignment="Right" verticalAlignment="Bottom">
					<font isBold="true"/>
				</textElement>
				<text><![CDATA[Qtde. Estoque Anterior]]></text>
			</staticText>
			<staticText>
				<reportElement x="446" y="216" width="108" height="20"/>
				<textElement textAlignment="Right" verticalAlignment="Bottom">
					<font isBold="true"/>
				</textElement>
				<text><![CDATA[Qtde. Balanço]]></text>
			</staticText>
			<line>
				<reportElement x="0" y="235" width="555" height="1"/>
			</line>
		</band>
	</title>
	<detail>
		<band height="20" splitType="Stretch">
			<textField>
				<reportElement x="0" y="0" width="215" height="20"/>
				<textElement verticalAlignment="Bottom"/>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{produto.descricao}]]></textFieldExpression>
			</textField>
			<textField pattern="">
				<reportElement x="285" y="0" width="136" height="20"/>
				<textElement textAlignment="Right" verticalAlignment="Bottom"/>
				<textFieldExpression class="java.lang.Integer"><![CDATA[$F{qtdeEstoqueAnterior}]]></textFieldExpression>
			</textField>
			<textField pattern="">
				<reportElement x="472" y="0" width="82" height="20"/>
				<textElement textAlignment="Right" verticalAlignment="Bottom"/>
				<textFieldExpression class="java.lang.Integer"><![CDATA[$F{qtdeBalanco}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<summary>
		<band height="22" splitType="Stretch"/>
	</summary>
</jasperReport>

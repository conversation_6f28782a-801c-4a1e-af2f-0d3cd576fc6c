<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="ResumoModalidades" pageWidth="354" pageHeight="802" columnWidth="354" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0">
	<property name="ireport.zoom" value="2.0"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<field name="modalidade" class="java.lang.String"/>
	<field name="clientes" class="java.lang.String"/>
	<field name="contratos" class="java.lang.String"/>
	<field name="competencia" class="java.lang.String"/>
	<field name="faturamento" class="java.lang.String"/>
	<detail>
		<band height="12" splitType="Stretch">
			<rectangle>
				<reportElement x="0" y="0" width="70" height="12" backcolor="#F0F0F0">
					<printWhenExpression><![CDATA[new Boolean(($V{COLUMN_COUNT}.intValue()%2)==0)]]></printWhenExpression>
				</reportElement>
				<graphicElement>
					<pen lineWidth="0.0"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="142" y="0" width="70" height="12" backcolor="#F0F0F0">
					<printWhenExpression><![CDATA[new Boolean(($V{COLUMN_COUNT}.intValue()%2)==0)]]></printWhenExpression>
				</reportElement>
				<graphicElement>
					<pen lineWidth="0.0"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="213" y="0" width="70" height="12" backcolor="#F0F0F0">
					<printWhenExpression><![CDATA[new Boolean(($V{COLUMN_COUNT}.intValue()%2)==0)]]></printWhenExpression>
				</reportElement>
				<graphicElement>
					<pen lineWidth="0.0"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="284" y="0" width="70" height="12" backcolor="#F0F0F0">
					<printWhenExpression><![CDATA[new Boolean(($V{COLUMN_COUNT}.intValue()%2)==0)]]></printWhenExpression>
				</reportElement>
				<graphicElement>
					<pen lineWidth="0.0"/>
				</graphicElement>
			</rectangle>
			<textField pattern="">
				<reportElement x="284" y="0" width="70" height="12"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{faturamento}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="0" y="0" width="70" height="12"/>
				<textElement verticalAlignment="Middle">
					<font size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{modalidade}]]></textFieldExpression>
			</textField>
			<rectangle>
				<reportElement x="72" y="0" width="70" height="12" backcolor="#F0F0F0">
					<printWhenExpression><![CDATA[new Boolean(($V{COLUMN_COUNT}.intValue()%2)==0)]]></printWhenExpression>
				</reportElement>
				<graphicElement>
					<pen lineWidth="0.0"/>
				</graphicElement>
			</rectangle>
			<textField pattern="">
				<reportElement x="71" y="0" width="70" height="12"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{clientes}]]></textFieldExpression>
			</textField>
			<textField pattern="">
				<reportElement x="142" y="0" width="70" height="12"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{contratos}]]></textFieldExpression>
			</textField>
			<textField pattern="">
				<reportElement x="213" y="0" width="70" height="12"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{competencia}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
</jasperReport>

<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="GestaoComissaoRelExcel" pageWidth="1400" pageHeight="878" columnWidth="1400" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" whenResourceMissingType="Empty">
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.encoding" value="ISO-8859-1"/>
	<property name="ireport.zoom" value="3.5369215365000124"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="java.util.*"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<queryString>
		<![CDATA[]]>
	</queryString>
	<field name="matricula" class="java.lang.String"/>
	<field name="cliente" class="java.lang.String"/>
	<field name="situacao" class="java.lang.String"/>
	<field name="valorpago" class="java.lang.String"/>
	<field name="idpagamento" class="java.lang.String"/>
	<field name="modalidade" class="java.lang.String"/>
	<field name="fracaopagoporcent" class="java.lang.String"/>
	<field name="fracaopagovalor" class="java.lang.String"/>
	<field name="contrato" class="java.lang.String"/>
	<field name="dia" class="java.lang.String"/>
	<field name="horario" class="java.lang.String"/>
	<field name="identificadorturma" class="java.lang.String"/>
	<field name="professor" class="java.lang.String"/>
	<field name="comissao" class="java.lang.String"/>
	<field name="iniciomatricula" class="java.lang.String"/>
	<field name="fimmatricula" class="java.lang.String"/>
	<field name="valorcomissao" class="java.lang.String"/>
	<title>
		<band height="23">
			<staticText>
				<reportElement key="staticText-1" stretchType="RelativeToBandHeight" x="0" y="0" width="48" height="22"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Matricula]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-2" stretchType="RelativeToBandHeight" x="48" y="0" width="185" height="22"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Cliente]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-3" stretchType="RelativeToBandHeight" x="549" y="0" width="71" height="22"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Fração Pg. %]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-4" stretchType="RelativeToBandHeight" x="233" y="0" width="46" height="22"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Situação]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-5" stretchType="RelativeToBandHeight" x="620" y="0" width="71" height="22"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Fração Pg. R$]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-9" stretchType="RelativeToBandHeight" x="691" y="0" width="35" height="22"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Ctr]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-10" stretchType="RelativeToBandHeight" x="726" y="0" width="35" height="22"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Dia]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-11" stretchType="RelativeToBandHeight" x="761" y="0" width="69" height="22"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Horário]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-12" stretchType="RelativeToBandHeight" x="830" y="-1" width="64" height="22"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Nome Turma]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-13" stretchType="RelativeToBandHeight" x="894" y="-1" width="64" height="22"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Professor]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-14" stretchType="RelativeToBandHeight" x="958" y="-1" width="64" height="22"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Comissão %]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-15" stretchType="RelativeToBandHeight" x="1022" y="-1" width="77" height="22"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Inic. Matrícula]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-16" stretchType="RelativeToBandHeight" x="1099" y="-1" width="77" height="22"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Fim Matrícula]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-17" stretchType="RelativeToBandHeight" x="1176" y="-1" width="77" height="22"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Vl. Comissão]]></text>
			</staticText>
			<staticText>
				<reportElement x="349" y="0" width="72" height="22"/>
				<textElement verticalAlignment="Middle">
					<font isBold="true"/>
				</textElement>
				<text><![CDATA[Id. Pagamento]]></text>
			</staticText>
			<staticText>
				<reportElement x="279" y="0" width="70" height="22"/>
				<textElement verticalAlignment="Middle">
					<font isBold="true"/>
				</textElement>
				<text><![CDATA[Vl. Pago]]></text>
			</staticText>
			<staticText>
				<reportElement x="421" y="0" width="128" height="22"/>
				<textElement verticalAlignment="Middle">
					<font isBold="true"/>
				</textElement>
				<text><![CDATA[Modalidade]]></text>
			</staticText>
		</band>
	</title>
	<detail>
		<band height="25">
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="48" height="20"/>
				<textElement/>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{matricula}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="48" y="0" width="185" height="20"/>
				<textElement/>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{cliente}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="233" y="0" width="46" height="20"/>
				<textElement/>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{situacao}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="279" y="0" width="70" height="20"/>
				<textElement/>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{valorpago}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="421" y="0" width="128" height="20"/>
				<textElement/>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{modalidade}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="549" y="0" width="71" height="20"/>
				<textElement/>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{fracaopagoporcent}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="620" y="0" width="71" height="20"/>
				<textElement/>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{fracaopagovalor}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="691" y="0" width="35" height="20"/>
				<textElement/>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{contrato}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="726" y="0" width="35" height="20"/>
				<textElement/>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{dia}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="761" y="0" width="69" height="20"/>
				<textElement/>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{horario}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="830" y="0" width="64" height="20"/>
				<textElement/>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{identificadorturma}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="894" y="0" width="64" height="20"/>
				<textElement/>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{professor}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="958" y="0" width="64" height="20"/>
				<textElement/>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{comissao}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="1022" y="0" width="77" height="20"/>
				<textElement/>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{iniciomatricula}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="1099" y="0" width="77" height="20"/>
				<textElement/>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{fimmatricula}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="1176" y="0" width="77" height="20"/>
				<textElement/>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{valorcomissao}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="349" y="0" width="72" height="20"/>
				<textElement/>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{idpagamento}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
</jasperReport>

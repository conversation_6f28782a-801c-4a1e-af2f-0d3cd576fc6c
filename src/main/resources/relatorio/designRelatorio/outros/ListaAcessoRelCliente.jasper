¬í sr (net.sf.jasperreports.engine.JasperReport      'Ø L compileDatat Ljava/io/Serializable;L compileNameSuffixt Ljava/lang/String;L 
compilerClassq ~ xr -net.sf.jasperreports.engine.base.JRBaseReport      'Ø ,I PSEUDO_SERIAL_VERSION_UIDI bottomMarginI columnCountI 
columnSpacingI columnWidthZ ignorePaginationZ isFloatColumnFooterZ isSummaryNewPageZ  isSummaryWithPageHeaderAndFooterZ isTitleNewPageI 
leftMarginB orientationI 
pageHeightI 	pageWidthB 
printOrderI rightMarginI 	topMarginB whenNoDataTypeL 
backgroundt $Lnet/sf/jasperreports/engine/JRBand;L columnDirectiont 3Lnet/sf/jasperreports/engine/type/RunDirectionEnum;L columnFooterq ~ L columnHeaderq ~ [ datasetst ([Lnet/sf/jasperreports/engine/JRDataset;L defaultFontt *Lnet/sf/jasperreports/engine/JRReportFont;L defaultStylet %Lnet/sf/jasperreports/engine/JRStyle;L detailq ~ L 
detailSectiont 'Lnet/sf/jasperreports/engine/JRSection;[ fontst +[Lnet/sf/jasperreports/engine/JRReportFont;L formatFactoryClassq ~ L 
importsSett Ljava/util/Set;L languageq ~ L lastPageFooterq ~ L mainDatasett 'Lnet/sf/jasperreports/engine/JRDataset;L nameq ~ L noDataq ~ L orientationValuet 2Lnet/sf/jasperreports/engine/type/OrientationEnum;L 
pageFooterq ~ L 
pageHeaderq ~ L printOrderValuet 1Lnet/sf/jasperreports/engine/type/PrintOrderEnum;[ stylest &[Lnet/sf/jasperreports/engine/JRStyle;L summaryq ~ [ 	templatest /[Lnet/sf/jasperreports/engine/JRReportTemplate;L titleq ~ L whenNoDataTypeValuet 5Lnet/sf/jasperreports/engine/type/WhenNoDataTypeEnum;xp  wñ   
         F           ¨  n       
 sr +net.sf.jasperreports.engine.base.JRBaseBand      'Ø I PSEUDO_SERIAL_VERSION_UIDI heightZ isSplitAllowedL printWhenExpressiont *Lnet/sf/jasperreports/engine/JRExpression;L 	splitTypet Ljava/lang/Byte;L splitTypeValuet 0Lnet/sf/jasperreports/engine/type/SplitTypeEnum;xr 3net.sf.jasperreports.engine.base.JRBaseElementGroup      'Ø L childrent Ljava/util/List;L elementGroupt ,Lnet/sf/jasperreports/engine/JRElementGroup;xpsr java.util.ArrayListxÒÇa I sizexp    w    xp  wñ    pp~r .net.sf.jasperreports.engine.type.SplitTypeEnum          xr java.lang.Enum          xpt STRETCH~r 1net.sf.jasperreports.engine.type.RunDirectionEnum          xq ~ t LTRsq ~ sq ~     w    xp  wñ    ppq ~ sq ~ sq ~    
w   
sr +net.sf.jasperreports.engine.base.JRBaseLine      'Ø I PSEUDO_SERIAL_VERSION_UIDB 	directionL directionValuet 4Lnet/sf/jasperreports/engine/type/LineDirectionEnum;xr 5net.sf.jasperreports.engine.base.JRBaseGraphicElement      'Ø I PSEUDO_SERIAL_VERSION_UIDL fillq ~ L 	fillValuet +Lnet/sf/jasperreports/engine/type/FillEnum;L linePent #Lnet/sf/jasperreports/engine/JRPen;L penq ~ xr .net.sf.jasperreports.engine.base.JRBaseElement      'Ø I PSEUDO_SERIAL_VERSION_UIDI heightZ isPrintInFirstWholeBandZ isPrintRepeatedValuesZ isPrintWhenDetailOverflowsZ isRemoveLineWhenBlankB positionTypeB stretchTypeI widthI xI yL 	backcolort Ljava/awt/Color;L defaultStyleProvidert 4Lnet/sf/jasperreports/engine/JRDefaultStyleProvider;L elementGroupq ~ L 	forecolorq ~ .L keyq ~ L modeq ~ L 	modeValuet +Lnet/sf/jasperreports/engine/type/ModeEnum;L parentStyleq ~ L parentStyleNameReferenceq ~ L positionTypeValuet 3Lnet/sf/jasperreports/engine/type/PositionTypeEnum;L printWhenExpressionq ~ L printWhenGroupChangest %Lnet/sf/jasperreports/engine/JRGroup;L 
propertiesMapt -Lnet/sf/jasperreports/engine/JRPropertiesMap;[ propertyExpressionst 3[Lnet/sf/jasperreports/engine/JRPropertyExpression;L stretchTypeValuet 2Lnet/sf/jasperreports/engine/type/StretchTypeEnum;xp  wñ          F       pq ~ q ~ &pt line-1pppp~r 1net.sf.jasperreports.engine.type.PositionTypeEnum          xq ~ t FIX_RELATIVE_TO_TOPpppp~r 0net.sf.jasperreports.engine.type.StretchTypeEnum          xq ~ t 
NO_STRETCH  wîppsr *net.sf.jasperreports.engine.base.JRBasePen      'Ø I PSEUDO_SERIAL_VERSION_UIDL 	lineColorq ~ .L 	lineStyleq ~ L lineStyleValuet 0Lnet/sf/jasperreports/engine/type/LineStyleEnum;L 	lineWidtht Ljava/lang/Float;L penContainert ,Lnet/sf/jasperreports/engine/JRPenContainer;xp  wñppppq ~ 6p  wñ ~r 2net.sf.jasperreports.engine.type.LineDirectionEnum          xq ~ t TOP_DOWNsr 0net.sf.jasperreports.engine.base.JRBaseTextField      'Ø I PSEUDO_SERIAL_VERSION_UIDI 
bookmarkLevelB evaluationTimeB hyperlinkTargetB 
hyperlinkTypeZ isStretchWithOverflowL anchorNameExpressionq ~ L evaluationGroupq ~ 2L evaluationTimeValuet 5Lnet/sf/jasperreports/engine/type/EvaluationTimeEnum;L 
expressionq ~ L hyperlinkAnchorExpressionq ~ L hyperlinkPageExpressionq ~ [ hyperlinkParameterst 3[Lnet/sf/jasperreports/engine/JRHyperlinkParameter;L hyperlinkReferenceExpressionq ~ L hyperlinkTooltipExpressionq ~ L isBlankWhenNullt Ljava/lang/Boolean;L 
linkTargetq ~ L linkTypeq ~ L patternq ~ xr 2net.sf.jasperreports.engine.base.JRBaseTextElement      'Ø %I PSEUDO_SERIAL_VERSION_UIDL borderq ~ L borderColorq ~ .L bottomBorderq ~ L bottomBorderColorq ~ .L 
bottomPaddingt Ljava/lang/Integer;L fontNameq ~ L fontSizeq ~ KL horizontalAlignmentq ~ L horizontalAlignmentValuet 6Lnet/sf/jasperreports/engine/type/HorizontalAlignEnum;L isBoldq ~ IL isItalicq ~ IL 
isPdfEmbeddedq ~ IL isStrikeThroughq ~ IL isStyledTextq ~ IL isUnderlineq ~ IL 
leftBorderq ~ L leftBorderColorq ~ .L leftPaddingq ~ KL lineBoxt 'Lnet/sf/jasperreports/engine/JRLineBox;L lineSpacingq ~ L lineSpacingValuet 2Lnet/sf/jasperreports/engine/type/LineSpacingEnum;L markupq ~ L paddingq ~ KL pdfEncodingq ~ L pdfFontNameq ~ L 
reportFontq ~ L rightBorderq ~ L rightBorderColorq ~ .L rightPaddingq ~ KL rotationq ~ L 
rotationValuet /Lnet/sf/jasperreports/engine/type/RotationEnum;L 	topBorderq ~ L topBorderColorq ~ .L 
topPaddingq ~ KL verticalAlignmentq ~ L verticalAlignmentValuet 4Lnet/sf/jasperreports/engine/type/VerticalAlignEnum;xq ~ -  wñ          F       pq ~ q ~ &pt 
textField-214ppppq ~ 9ppppq ~ <  wñpppppt Arialsr java.lang.Integerâ ¤÷8 I valuexr java.lang.Number¬à  xp   
p~r 4net.sf.jasperreports.engine.type.HorizontalAlignEnum          xq ~ t CENTERsr java.lang.BooleanÍ rÕúî Z valuexpq ~ [pppppppsr .net.sf.jasperreports.engine.base.JRBaseLineBox      'Ø L 
bottomPaddingq ~ KL 	bottomPent +Lnet/sf/jasperreports/engine/base/JRBoxPen;L boxContainert ,Lnet/sf/jasperreports/engine/JRBoxContainer;L leftPaddingq ~ KL leftPenq ~ ]L paddingq ~ KL penq ~ ]L rightPaddingq ~ KL rightPenq ~ ]L 
topPaddingq ~ KL topPenq ~ ]xppsr 3net.sf.jasperreports.engine.base.JRBaseBoxBottomPen      'Ø  xr -net.sf.jasperreports.engine.base.JRBaseBoxPen      'Ø L lineBoxq ~ Mxq ~ >  wñsr java.awt.Color¥3u F falphaI valueL cst Ljava/awt/color/ColorSpace;[ 	frgbvaluet [F[ fvalueq ~ exp    ÿfffpppp~r .net.sf.jasperreports.engine.type.LineStyleEnum          xq ~ t SOLIDsr java.lang.FloatÚíÉ¢Û<ðì F valuexq ~ U?   q ~ _q ~ _q ~ Qpsr 1net.sf.jasperreports.engine.base.JRBaseBoxLeftPen      'Ø  xq ~ a  wñppq ~ hsq ~ j?   q ~ _q ~ _psq ~ a  wñppppq ~ _q ~ _psr 2net.sf.jasperreports.engine.base.JRBaseBoxRightPen      'Ø  xq ~ a  wñppq ~ hsq ~ j?   q ~ _q ~ _psr 0net.sf.jasperreports.engine.base.JRBaseBoxTopPen      'Ø  xq ~ a  wñppq ~ hsq ~ j?   q ~ _q ~ _pppppt Helvetica-BoldObliqueppppppppppp  wñ        pp~r 3net.sf.jasperreports.engine.type.EvaluationTimeEnum          xq ~ t NOWsr 1net.sf.jasperreports.engine.base.JRBaseExpression      'Ø I id[ chunkst 0[Lnet/sf/jasperreports/engine/JRExpressionChunk;L valueClassNameq ~ L valueClassRealNameq ~ xp   ur 0[Lnet.sf.jasperreports.engine.JRExpressionChunk;mYÏÞiK£U  xp   sr 6net.sf.jasperreports.engine.base.JRBaseExpressionChunk      'Ø B typeL textq ~ xpt filtrost java.lang.Stringppppppsq ~ Z pppsr 1net.sf.jasperreports.engine.base.JRBaseStaticText      'Ø L textq ~ xq ~ J  wñ           L      'pq ~ q ~ &pt staticText-2ppppq ~ 9ppppq ~ <  wñpppppppppq ~ [ppppppppsq ~ \psq ~ `  wñppppq ~ q ~ q ~ psq ~ l  wñppppq ~ q ~ psq ~ a  wñppppq ~ q ~ psq ~ p  wñppppq ~ q ~ psq ~ s  wñppppq ~ q ~ pppppt Helvetica-Boldpppppppppppt Mat. Clientesq ~   wñ           Q   N   'pq ~ q ~ &pt staticText-8ppppq ~ 9ppppq ~ <  wñpppppppppq ~ [ppppppppsq ~ \psq ~ `  wñppppq ~ q ~ q ~ psq ~ l  wñppppq ~ q ~ psq ~ a  wñppppq ~ q ~ psq ~ p  wñppppq ~ q ~ psq ~ s  wñppppq ~ q ~ pppppt Helvetica-Boldpppppppppppt Data Entradasq ~   wñ           U      'pq ~ q ~ &pt staticText-8ppppq ~ 9ppppq ~ <  wñpppppppppq ~ [ppppppppsq ~ \psq ~ `  wñppppq ~ q ~ q ~ psq ~ l  wñppppq ~ q ~ psq ~ a  wñppppq ~ q ~ psq ~ p  wñppppq ~ q ~ psq ~ s  wñppppq ~ q ~ pppppt Helvetica-Boldpppppppppppt Data SaÃ­dasq ~   wñ           +   ô   'pq ~ q ~ &pt staticText-8ppppq ~ 9ppppq ~ <  wñpppppppppq ~ [ppppppppsq ~ \psq ~ `  wñppppq ~ ¥q ~ ¥q ~ £psq ~ l  wñppppq ~ ¥q ~ ¥psq ~ a  wñppppq ~ ¥q ~ ¥psq ~ p  wñppppq ~ ¥q ~ ¥psq ~ s  wñppppq ~ ¥q ~ ¥pppppt Helvetica-Boldpppppppppppt Temposq ~   wñ           -     'pq ~ q ~ &pt staticText-8ppppq ~ 9ppppq ~ <  wñpppppppppq ~ [ppppppppsq ~ \psq ~ `  wñppppq ~ ¯q ~ ¯q ~ ­psq ~ l  wñppppq ~ ¯q ~ ¯psq ~ a  wñppppq ~ ¯q ~ ¯psq ~ p  wñppppq ~ ¯q ~ ¯psq ~ s  wñppppq ~ ¯q ~ ¯pppppt Helvetica-Boldpppppppppppt Sentidosq ~   wñ           N  ù   'pq ~ q ~ &pt staticText-8ppppq ~ 9ppppq ~ <  wñpppppppppq ~ [ppppppppsq ~ \psq ~ `  wñppppq ~ ¹q ~ ¹q ~ ·psq ~ l  wñppppq ~ ¹q ~ ¹psq ~ a  wñppppq ~ ¹q ~ ¹psq ~ p  wñppppq ~ ¹q ~ ¹psq ~ s  wñppppq ~ ¹q ~ ¹pppppt Helvetica-Boldpppppppppppt 
UsuÃ¡rio Lib.sq ~   wñ           >  L   'pq ~ q ~ &pt staticText-8ppppq ~ 9ppppq ~ <  wñpppppppppq ~ [ppppppppsq ~ \psq ~ `  wñppppq ~ Ãq ~ Ãq ~ Ápsq ~ l  wñppppq ~ Ãq ~ Ãpsq ~ a  wñppppq ~ Ãq ~ Ãpsq ~ p  wñppppq ~ Ãq ~ Ãpsq ~ s  wñppppq ~ Ãq ~ Ãpppppt Helvetica-Boldpppppppppppt Bloqueiosq ~   wñ          m     'pq ~ q ~ &pt staticText-8ppppq ~ 9ppppq ~ <  wñpppppppppq ~ [ppppppppsq ~ \psq ~ `  wñppppq ~ Íq ~ Íq ~ Ëpsq ~ l  wñppppq ~ Íq ~ Ípsq ~ a  wñppppq ~ Íq ~ Ípsq ~ p  wñppppq ~ Íq ~ Ípsq ~ s  wñppppq ~ Íq ~ Ípppppt Helvetica-Boldpppppppppppt Coletorxp  wñ   6ppq ~ ur ([Lnet.sf.jasperreports.engine.JRDataset;L6Í¬D  xp   sr .net.sf.jasperreports.engine.base.JRBaseDataset      'Ø I PSEUDO_SERIAL_VERSION_UIDZ isMainB whenResourceMissingType[ fieldst &[Lnet/sf/jasperreports/engine/JRField;L filterExpressionq ~ [ groupst &[Lnet/sf/jasperreports/engine/JRGroup;L nameq ~ [ 
parameterst *[Lnet/sf/jasperreports/engine/JRParameter;L 
propertiesMapq ~ 3L queryt %Lnet/sf/jasperreports/engine/JRQuery;L resourceBundleq ~ L scriptletClassq ~ [ 
scriptletst *[Lnet/sf/jasperreports/engine/JRScriptlet;[ 
sortFieldst *[Lnet/sf/jasperreports/engine/JRSortField;[ 	variablest )[Lnet/sf/jasperreports/engine/JRVariable;L whenResourceMissingTypeValuet >Lnet/sf/jasperreports/engine/type/WhenResourceMissingTypeEnum;xp  wñ  pppt Testeur *[Lnet.sf.jasperreports.engine.JRParameter;" *Ã`!  xp   sr 0net.sf.jasperreports.engine.base.JRBaseParameter      'Ø 	Z isForPromptingZ isSystemDefinedL defaultValueExpressionq ~ L descriptionq ~ L nameq ~ L nestedTypeNameq ~ L 
propertiesMapq ~ 3L valueClassNameq ~ L valueClassRealNameq ~ xpppt REPORT_PARAMETERS_MAPpsr +net.sf.jasperreports.engine.JRPropertiesMap      'Ø L baseq ~ 3L propertiesListq ~ L 
propertiesMapt Ljava/util/Map;xppppt 
java.util.Mappsq ~ äppt 
JASPER_REPORTpsq ~ çpppt (net.sf.jasperreports.engine.JasperReportpsq ~ äppt REPORT_CONNECTIONpsq ~ çpppt java.sql.Connectionpsq ~ äppt REPORT_MAX_COUNTpsq ~ çpppt java.lang.Integerpsq ~ äppt REPORT_DATA_SOURCEpsq ~ çpppt (net.sf.jasperreports.engine.JRDataSourcepsq ~ äppt REPORT_SCRIPTLETpsq ~ çpppt /net.sf.jasperreports.engine.JRAbstractScriptletpsq ~ äppt 
REPORT_LOCALEpsq ~ çpppt java.util.Localepsq ~ äppt REPORT_RESOURCE_BUNDLEpsq ~ çpppt java.util.ResourceBundlepsq ~ äppt REPORT_TIME_ZONEpsq ~ çpppt java.util.TimeZonepsq ~ äppt REPORT_FORMAT_FACTORYpsq ~ çpppt .net.sf.jasperreports.engine.util.FormatFactorypsq ~ äppt REPORT_CLASS_LOADERpsq ~ çpppt java.lang.ClassLoaderpsq ~ äppt REPORT_URL_HANDLER_FACTORYpsq ~ çpppt  java.net.URLStreamHandlerFactorypsq ~ äppt REPORT_FILE_RESOLVERpsq ~ çpppt -net.sf.jasperreports.engine.util.FileResolverpsq ~ äppt REPORT_TEMPLATESpsq ~ çpppt java.util.Collectionpsq ~ äppt SORT_FIELDSpsq ~ çpppt java.util.Listpsq ~ çppppppppur )[Lnet.sf.jasperreports.engine.JRVariable;bæ|,·D  xp   sr /net.sf.jasperreports.engine.base.JRBaseVariable      'Ø I PSEUDO_SERIAL_VERSION_UIDB calculationB 
incrementTypeZ isSystemDefinedB 	resetTypeL calculationValuet 2Lnet/sf/jasperreports/engine/type/CalculationEnum;L 
expressionq ~ L incrementGroupq ~ 2L incrementTypeValuet 4Lnet/sf/jasperreports/engine/type/IncrementTypeEnum;L incrementerFactoryClassNameq ~ L incrementerFactoryClassRealNameq ~ L initialValueExpressionq ~ L nameq ~ L 
resetGroupq ~ 2L resetTypeValuet 0Lnet/sf/jasperreports/engine/type/ResetTypeEnum;L valueClassNameq ~ L valueClassRealNameq ~ xp  wî   ~r 0net.sf.jasperreports.engine.type.CalculationEnum          xq ~ t SYSTEMpp~r 2net.sf.jasperreports.engine.type.IncrementTypeEnum          xq ~ t NONEppsq ~ z    uq ~ }   sq ~ t new java.lang.Integer(1)q ~ öpt PAGE_NUMBERp~r .net.sf.jasperreports.engine.type.ResetTypeEnum          xq ~ t REPORTq ~ öpsq ~&  wî   q ~,ppq ~/ppsq ~ z   uq ~ }   sq ~ t new java.lang.Integer(1)q ~ öpt 
COLUMN_NUMBERp~q ~6t PAGEq ~ öpsq ~&  wî   ~q ~+t COUNTsq ~ z   uq ~ }   sq ~ t new java.lang.Integer(1)q ~ öppq ~/ppsq ~ z   uq ~ }   sq ~ t new java.lang.Integer(0)q ~ öpt REPORT_COUNTpq ~7q ~ öpsq ~&  wî   q ~Bsq ~ z   uq ~ }   sq ~ t new java.lang.Integer(1)q ~ öppq ~/ppsq ~ z   uq ~ }   sq ~ t new java.lang.Integer(0)q ~ öpt 
PAGE_COUNTpq ~?q ~ öpsq ~&  wî   q ~Bsq ~ z   uq ~ }   sq ~ t new java.lang.Integer(1)q ~ öppq ~/ppsq ~ z   uq ~ }   sq ~ t new java.lang.Integer(0)q ~ öpt COLUMN_COUNTp~q ~6t COLUMNq ~ öp~r <net.sf.jasperreports.engine.type.WhenResourceMissingTypeEnum          xq ~ t NULLpppsr .net.sf.jasperreports.engine.base.JRBaseSection      'Ø [ bandst %[Lnet/sf/jasperreports/engine/JRBand;xpur %[Lnet.sf.jasperreports.engine.JRBand;Ý~ìÊ5  xp   sq ~ sq ~    
w   
sq ~ F  wñ           (   ô   pq ~ q ~kpt 
textField-223ppppq ~ 9pppp~q ~ ;t RELATIVE_TO_TALLEST_OBJECT  wñpppppppp~q ~ Wt LEFTpppppppppsq ~ \psq ~ `  wñppppq ~sq ~sq ~mpsq ~ l  wñppppq ~sq ~spsq ~ a  wñppppq ~sq ~spsq ~ p  wñppppq ~sq ~spsq ~ s  wñppppq ~sq ~spppppppppppppppp~r 2net.sf.jasperreports.engine.type.VerticalAlignEnum          xq ~ t MIDDLE  wñ       ppq ~ xsq ~ z   uq ~ }   sq ~ t intervaloDataHorast java.lang.Stringppppppq ~ [pppsq ~ F  wñ                pq ~ q ~kpt 	textFieldppppq ~ 9ppppq ~o  wñppppppppq ~qpppppppppsq ~ \psq ~ `  wñppppq ~q ~q ~psq ~ l  wñppppq ~q ~psq ~ a  wñppppq ~q ~psq ~ p  wñppppq ~q ~psq ~ s  wñppppq ~q ~ppppppppppppppppq ~z  wñ       ppq ~ xsq ~ z   uq ~ }   sq ~ t sentidot java.lang.Stringppppppq ~ [ppt  sq ~ F  wñ           U      pq ~ q ~kpt 
textField-224ppppq ~ 9ppppq ~o  wñppppppppq ~qpppppppppsq ~ \psq ~ `  wñppppq ~q ~q ~psq ~ l  wñppppq ~q ~psq ~ a  wñppppq ~q ~psq ~ p  wñppppq ~q ~psq ~ s  wñppppq ~q ~ppppppppppppppppq ~z  wñ       ppq ~ xsq ~ z   uq ~ }   sq ~ t 
dataHoraSaidat java.sql.Timestampppppppq ~ [ppq ~sq ~ F  wñ           L  ù   pq ~ q ~kpt 	textFieldppppq ~ 9ppppq ~o  wñppppppppq ~qpppppppppsq ~ \psq ~ `  wñppppq ~q ~q ~psq ~ l  wñppppq ~q ~psq ~ a  wñppppq ~q ~psq ~ p  wñppppq ~q ~psq ~ s  wñppppq ~q ~ppppppppppppppppq ~z  wñ       ppq ~ xsq ~ z   uq ~ }   sq ~ t usuario.primeiroNomeConcatenadot java.lang.Stringppppppq ~ [ppq ~sq ~ F  wñ           L      pq ~ q ~kpt 
textField-223ppppq ~ 9ppppq ~o  wñppppppppq ~qpppppppppsq ~ \psq ~ `  wñppppq ~«q ~«q ~©psq ~ l  wñppppq ~«q ~«psq ~ a  wñppppq ~«q ~«psq ~ p  wñppppq ~«q ~«psq ~ s  wñppppq ~«q ~«ppppppppppppppppq ~z  wñ       ppq ~ xsq ~ z   uq ~ }   sq ~ t cliente.matriculat java.lang.Stringppppppq ~ [pppsq ~ F  wñ           ß  3   pq ~ q ~kpt 	textFieldppppq ~ 9sq ~ z   uq ~ }   sq ~ t !sq ~ t situacao.idsq ~ t !.equals("RV_LIBACESSOAUTORIZADO")t java.lang.Booleanppppq ~o  wñppppppppq ~qpppppppppsq ~ \psq ~ `  wñppppq ~Áq ~Áq ~¶psq ~ l  wñppppq ~Áq ~Ápsq ~ a  wñppppq ~Áq ~Ápsq ~ p  wñppppq ~Áq ~Ápsq ~ s  wñppppq ~Áq ~Áppppppppppppppppq ~z  wñ       ppq ~ xsq ~ z   uq ~ }   sq ~ t situacao.descricaot java.lang.Stringppppppq ~ [ppq ~sq ~ F  wñ          ã   ]   pq ~ q ~kpt 	textFieldppppq ~ 9ppppq ~o  wñppppppsq ~ T   	pq ~qpppppppppsq ~ \psq ~ `  wñppppq ~Ïq ~Ïq ~Ìpsq ~ l  wñppppq ~Ïq ~Ïpsq ~ a  wñppppq ~Ïq ~Ïpsq ~ p  wñppppq ~Ïq ~Ïpsq ~ s  wñppppq ~Ïq ~Ïpppppppppppppppp~q ~yt TOP  wñ       ppq ~ xsq ~ z   uq ~ }   sq ~ t "meioIdentificacaoEntrada.descricaot java.lang.Stringppppppq ~ [ppq ~sq ~ F  wñ           Q   N   pq ~ q ~kpt 
textField-224ppppq ~ 9ppppq ~o  wñppppppq ~ Vpq ~qq ~ ppppppppsq ~ \psq ~ `  wñppppq ~Þq ~Þq ~Üpsq ~ l  wñppppq ~Þq ~Þpsq ~ a  wñppppq ~Þq ~Þpsq ~ p  wñppppq ~Þq ~Þpsq ~ s  wñppppq ~Þq ~Þppppppppppppppppq ~z  wñ       ppq ~ xsq ~ z   uq ~ }   sq ~ t dataHoraEntradat java.sql.Timestampppppppq ~ [pppsq ~ F  wñ           ç     pq ~ q ~kpt 	textFieldppppq ~ 9ppppq ~o  wñppppppppq ~qpppppppppsq ~ \psq ~ `  wñppppq ~ëq ~ëq ~épsq ~ l  wñppppq ~ëq ~ëpsq ~ a  wñppppq ~ëq ~ëpsq ~ p  wñppppq ~ëq ~ëpsq ~ s  wñppppq ~ëq ~ëppppppppppppppppq ~z  wñ       ppq ~ xsq ~ z    uq ~ }   sq ~ t coletor.descricaot java.lang.Stringppppppq ~ [ppq ~sq ~   wñ           a      pq ~ q ~kpt staticText-8ppppq ~ 9ppppq ~ <  wñppppppq ~Îppq ~ [ppppppppsq ~ \psq ~ `  wñppppq ~øq ~øq ~öpsq ~ l  wñppppq ~øq ~øpsq ~ a  wñppppq ~øq ~øpsq ~ p  wñppppq ~øq ~øpsq ~ s  wñppppq ~øq ~øpppppt Helvetica-Boldpppppppppppt Meio IdentificaÃ§Ã£o:xp  wñ   ppq ~ ppsr java.util.HashSetºD¸·4  xpw   ?@     t "net.sf.jasperreports.engine.data.*t net.sf.jasperreports.engine.*t java.util.*xt javasq ~ sq ~    	w   	sq ~   wñ           {     pq ~ q ~ppppppq ~ 9ppppq ~ <  wñppppppppppppppppppsq ~ \psq ~ `  wñppppq ~	q ~	q ~psq ~ l  wñppppq ~	q ~	psq ~ a  wñppppq ~	q ~	psq ~ p  wñppppq ~	q ~	psq ~ s  wñppppq ~	q ~	pppppt Helvetica-Boldpppppppppppt Total Geral de Acessos:sq ~ (  wñ          F   ÿÿÿþpq ~ q ~pt line-4ppppq ~ 9ppppq ~ <  wîppsq ~ >  wñppppq ~p  wñ q ~ Dsq ~ (  wñ          E      1pq ~ q ~pt line-5ppppq ~ 9ppppq ~ <  wîppsq ~ >  wñppppq ~p  wñ q ~ Dsq ~ (  wñ          E      3pq ~ q ~pt line-6ppppq ~ 9ppppq ~ <  wîppsq ~ >  wñppppq ~p  wñ q ~ Dsq ~ F  wñ          E      7pq ~ q ~pt 
textField-207ppppq ~ 9ppppq ~ <  wñpppppt Arialsq ~ T   pppq ~ [pppppppsq ~ \psq ~ `  wñsq ~ c    ÿfffppppq ~ hsq ~ j?   q ~q ~q ~psq ~ l  wñsq ~ c    ÿfffppppq ~ hsq ~ j?   q ~q ~psq ~ a  wñppppq ~q ~psq ~ p  wñsq ~ c    ÿfffppppq ~ hsq ~ j?   q ~q ~psq ~ s  wñsq ~ c    ÿfffppppq ~ hsq ~ j?   q ~q ~pppppt Helvetica-Obliqueppppppppppq ~z  wñ        ppq ~ xsq ~ z   !uq ~ }   sq ~ t " "+" UsuÃ¡rio:" + sq ~ t usuariot java.lang.Stringppppppq ~ ppq ~sq ~ F  wñ           d     pq ~ q ~ppppppq ~ 9ppppq ~ <  wñppppppppppppppppppsq ~ \psq ~ `  wñppppq ~5q ~5q ~4psq ~ l  wñppppq ~5q ~5psq ~ a  wñppppq ~5q ~5psq ~ p  wñppppq ~5q ~5psq ~ s  wñppppq ~5q ~5ppppppppppppppppp  wñ        ppq ~ xsq ~ z   "uq ~ }   sq ~ t 
totalclientest java.lang.Integerppppppppppsq ~ F  wñ   
        ~  ¾   ;sq ~ c    ÿÿÿÿpppq ~ q ~pt 	dataRel-1p~r )net.sf.jasperreports.engine.type.ModeEnum          xq ~ t OPAQUEppq ~ 9ppppq ~ <  wñpppppt Arialq ~pq ~qq ~ q ~ [pppq ~ pppsq ~ \sq ~ T   sq ~ `  wñsq ~ c    ÿfffppppq ~ hsq ~ j    q ~Gq ~Gq ~@psq ~ l  wñsq ~ c    ÿfffppppq ~ hsq ~ j    q ~Gq ~Gpsq ~ a  wñppppq ~Gq ~Gpsq ~ p  wñsq ~ c    ÿfffppppq ~ hsq ~ j    q ~Gq ~Gpsq ~ s  wñsq ~ c    ÿfffppppq ~ hsq ~ j    q ~Gq ~Gp~r 0net.sf.jasperreports.engine.type.LineSpacingEnum          xq ~ t SINGLEpppt Helvetica-Obliqueppppppppppq ~z  wñ        ppq ~ xsq ~ z   #uq ~ }   sq ~ t 
new Date()t java.util.Dateppppppq ~ [ppt dd/MM/yyyy HH.mm.sssq ~ F  wñ           d  à   pq ~ q ~ppppppq ~ 9ppppq ~ <  wñppppppppppppppppppsq ~ \psq ~ `  wñppppq ~aq ~aq ~`psq ~ l  wñppppq ~aq ~apsq ~ a  wñppppq ~aq ~apsq ~ p  wñppppq ~aq ~apsq ~ s  wñppppq ~aq ~appppppppppppppppp  wñ        ppq ~ xsq ~ z   $uq ~ }   sq ~ t totalalunost java.lang.Integerppppppppppsq ~   wñ           {  e   pq ~ q ~ppppppq ~ 9ppppq ~ <  wñppppppppppppppppppsq ~ \psq ~ `  wñppppq ~mq ~mq ~lpsq ~ l  wñppppq ~mq ~mpsq ~ a  wñppppq ~mq ~mpsq ~ p  wñppppq ~mq ~mpsq ~ s  wñppppq ~mq ~mpppppt Helvetica-Boldpppppppppppt Total Geral de Alunos:xp  wñ   Kppq ~ sq ~ ×  wñ ur &[Lnet.sf.jasperreports.engine.JRField;<ßÇN*òp  xp   
sr ,net.sf.jasperreports.engine.base.JRBaseField      'Ø L descriptionq ~ L nameq ~ L 
propertiesMapq ~ 3L valueClassNameq ~ L valueClassRealNameq ~ xpt  t cliente.matriculasq ~ çpppt java.lang.Stringpsq ~xpt intervaloDataHorassq ~ çpppt java.lang.Stringpsq ~xt  t cliente.pessoa.nomesq ~ çpppt java.lang.Stringpsq ~xt  t dataHoraEntradasq ~ çpppt java.util.Datepsq ~xt  t 
dataHoraSaidasq ~ çpppt java.util.Datepsq ~xt  t sentidosq ~ çpppt java.lang.Stringpsq ~xpt usuario.primeiroNomeConcatenadosq ~ çpppt java.lang.Stringpsq ~xpt situacao.descricaosq ~ çpppt java.lang.Stringpsq ~xpt situacao.idsq ~ çpppt java.lang.Stringpsq ~xpt "meioIdentificacaoEntrada.descricaosq ~ çpppt java.lang.Stringpsq ~xpt localAcesso.empresa.nomesq ~ çpppt java.lang.Stringpsq ~xpt coletor.descricaosq ~ çpppt java.lang.Stringpsq ~xpt cliente.pessoa.emailsq ~ çpppt java.lang.Stringppur &[Lnet.sf.jasperreports.engine.JRGroup;@£_zLýxê  xp   sr ,net.sf.jasperreports.engine.base.JRBaseGroup      'Ø I PSEUDO_SERIAL_VERSION_UIDB footerPositionZ isReprintHeaderOnEachPageZ isResetPageNumberZ isStartNewColumnZ isStartNewPageZ keepTogetherI minHeightToStartNewPageL 
countVariablet (Lnet/sf/jasperreports/engine/JRVariable;L 
expressionq ~ L footerPositionValuet 5Lnet/sf/jasperreports/engine/type/FooterPositionEnum;L groupFooterq ~ L groupFooterSectionq ~ 	L groupHeaderq ~ L groupHeaderSectionq ~ 	L nameq ~ xp  wñ          sq ~&  wî   q ~Bsq ~ z   	uq ~ }   sq ~ t new java.lang.Integer(1)q ~ öppq ~/ppsq ~ z   
uq ~ }   sq ~ t new java.lang.Integer(0)q ~ öpt nomeCliente_COUNTq ~·~q ~6t GROUPq ~ öpsq ~ z   
uq ~ }   sq ~ t cliente.pessoa.nomet java.lang.Objectp~r 3net.sf.jasperreports.engine.type.FooterPositionEnum          xq ~ t NORMALpsq ~fuq ~i   sq ~ sq ~    w   sq ~   wñ           Z  e    pq ~ q ~Îppppppq ~ 9ppppq ~ <  wñpppppppp~q ~ Wt RIGHTpppppppppsq ~ \psq ~ `  wñppppq ~Óq ~Óq ~Ðpsq ~ l  wñppppq ~Óq ~Ópsq ~ a  wñppppq ~Óq ~Ópsq ~ p  wñppppq ~Óq ~Ópsq ~ s  wñppppq ~Óq ~Ópppppt Helvetica-Boldpppppppppppt Total de Acessos:sq ~ F  wñ             Â    pq ~ q ~Îppppppq ~ 9ppppq ~ <  wñppppppppppppppppppsq ~ \psq ~ `  wñppppq ~Üq ~Üq ~Ûpsq ~ l  wñppppq ~Üq ~Üpsq ~ a  wñppppq ~Üq ~Üpsq ~ p  wñppppq ~Üq ~Üpsq ~ s  wñppppq ~Üq ~Üppppppppppppppppp  wñ       ppq ~ xsq ~ z   uq ~ }   sq ~ t nomeCliente_COUNTt java.lang.Integerppppppppppxp  wñ   ppppsq ~fuq ~i   sq ~ sq ~    w   sq ~ F  wñ          >      pq ~ q ~épt 
textField-224pppp~q ~ 8t FLOATppppq ~ <  wñppppppsq ~ T   ppq ~ [ppppppppsq ~ \psq ~ `  wñppppq ~ðq ~ðq ~ëpsq ~ l  wñppppq ~ðq ~ðpsq ~ a  wñppppq ~ðq ~ðpsq ~ p  wñppppq ~ðq ~ðpsq ~ s  wñppppq ~ðq ~ðpppppt Helvetica-Boldppppppppppp  wñ       ppq ~ xsq ~ z   uq ~ }   sq ~ t cliente.pessoa.nomesq ~ t  + (sq ~ t cliente.pessoa.emailsq ~ t .trim().equals("") || sq ~ t cliente.pessoa.emailsq ~ t ==null ? "" :  (" - " + sq ~ t cliente.pessoa.emailsq ~ t ))t java.lang.Stringppppppq ~ [pppsq ~ (  wñ          B       pq ~ q ~éppppppq ~ 9ppppq ~ <  wîppsq ~ >  wñppppq ~
p  wñ q ~ Dxp  wñ   pppt nomeClientet ListaAcessoRelClienteuq ~ â   sq ~ äppq ~ æpsq ~ çpppq ~ êpsq ~ äppq ~ ìpsq ~ çpppq ~ îpsq ~ äppq ~ ðpsq ~ çpppq ~ òpsq ~ äppq ~ ôpsq ~ çpppq ~ öpsq ~ äppq ~ øpsq ~ çpppq ~ úpsq ~ äppq ~ üpsq ~ çpppq ~ þpsq ~ äppq ~ psq ~ çpppq ~psq ~ äppq ~psq ~ çpppq ~psq ~ äppq ~psq ~ çpppq ~
psq ~ äppq ~psq ~ çpppq ~psq ~ äppq ~psq ~ çpppq ~psq ~ äppq ~psq ~ çpppq ~psq ~ äppq ~psq ~ çpppq ~psq ~ äppq ~psq ~ çpppq ~psq ~ äppq ~ psq ~ çpppq ~"psq ~ äppt REPORT_VIRTUALIZERpsq ~ çpppt )net.sf.jasperreports.engine.JRVirtualizerpsq ~ äppt IS_IGNORE_PAGINATIONpsq ~ çpppq ~Àpsq ~ ä  ppt logoPadraoRelatoriopsq ~ çpppt java.io.InputStreampsq ~ ä  ppt tituloRelatoriopsq ~ çpppt java.lang.Stringpsq ~ ä  ppt versaoSoftwarepsq ~ çpppt java.lang.Stringpsq ~ ä  ppt usuariopsq ~ çpppt java.lang.Stringpsq ~ ä  ppt filtrospsq ~ çpppt java.lang.Stringpsq ~ ä sq ~ z    uq ~ }   sq ~ t q"D:\\PactoJ\\Desenvolvimento\\Sistemas\\ZillyonWeb\\tronco-novo\\src\\java\\relatorio\\designRelatorio\\outros\\"t java.lang.Stringppt 
SUBREPORT_DIRpsq ~ çpppq ~Mpsq ~ ä ppt nomeEmpresapsq ~ çpppt java.lang.Stringpsq ~ ä ppt enderecoEmpresapsq ~ çpppt java.lang.Stringpsq ~ ä ppt 
cidadeEmpresapsq ~ çpppt java.lang.Stringpsq ~ ä  ppt dataInipsq ~ çpppt java.lang.Stringpsq ~ ä  ppt dataFimpsq ~ çpppt java.lang.Stringpsq ~ ä ppt SUBREPORT_DIR1psq ~ çpppt java.lang.Stringpsq ~ çpsq ~    w   t ireport.scriptlethandlingt ireport.encodingt ireport.zoomt 	ireport.xt 	ireport.yxsr java.util.HashMapÚÁÃ`Ñ F 
loadFactorI 	thresholdxp?@     w      q ~lt 1.5q ~kt 
ISO-8859-1q ~mt 215q ~nt 0q ~jt 0xpppppuq ~$   sq ~&  wî   q ~,ppq ~/ppsq ~ z   uq ~ }   sq ~ t new java.lang.Integer(1)q ~ öpq ~5pq ~7q ~ öpsq ~&  wî   q ~,ppq ~/ppsq ~ z   uq ~ }   sq ~ t new java.lang.Integer(1)q ~ öpq ~>pq ~?q ~ öpsq ~&  wî   q ~Bsq ~ z   uq ~ }   sq ~ t new java.lang.Integer(1)q ~ öppq ~/ppsq ~ z   uq ~ }   sq ~ t new java.lang.Integer(0)q ~ öpq ~Lpq ~7q ~ öpsq ~&  wî   q ~Bsq ~ z   uq ~ }   sq ~ t new java.lang.Integer(1)q ~ öppq ~/ppsq ~ z   uq ~ }   sq ~ t new java.lang.Integer(0)q ~ öpq ~Vpq ~?q ~ öpsq ~&  wî   q ~Bsq ~ z   uq ~ }   sq ~ t new java.lang.Integer(1)q ~ öppq ~/ppsq ~ z   uq ~ }   sq ~ t new java.lang.Integer(0)q ~ öpq ~`pq ~aq ~ öpq ~¸sq ~&  wî    q ~Bsq ~ z   uq ~ }   sq ~ t new Integer(sq ~ t cliente.matriculasq ~ t )q ~Èppq ~/pppt 
totalclientespq ~7t java.lang.Integerpsq ~&  wî    ~q ~+t DISTINCT_COUNTsq ~ z   uq ~ }   sq ~ t new Integer(sq ~ t cliente.matriculasq ~ t )q ~Èppq ~/pppt totalalunospq ~7t java.lang.Integerp~q ~ct EMPTYq ~
p~r 0net.sf.jasperreports.engine.type.OrientationEnum          xq ~ t 	LANDSCAPEsq ~ sq ~     w    xp  wñ    ppq ~ sq ~ sq ~    	w   	sq ~ F  wñ             #   3pq ~ q ~»pt 
textField-212ppppq ~ 9ppppq ~ <  wñpppppt Arialq ~ Vppq ~ [ppppppppsq ~ \q ~Hsq ~ `  wñsq ~ c    ÿfffppppq ~ hsq ~ j    q ~Àq ~Àq ~½psq ~ l  wñsq ~ c    ÿfffppppq ~ hsq ~ j    q ~Àq ~Àpsq ~ a  wñppppq ~Àq ~Àpsq ~ p  wñsq ~ c    ÿfffppppq ~ hsq ~ j    q ~Àq ~Àpsq ~ s  wñsq ~ c    ÿ   ppppq ~ hsq ~ j    q ~Àq ~Àpppppt Helvetica-Boldppppppppppp  wñ        pp~q ~ wt REPORTsq ~ z   uq ~ }   sq ~ t " " + sq ~ t PAGE_NUMBERsq ~ t  + ""t java.lang.Stringppppppq ~ pppsq ~   wñ           o  Ñ   #pq ~ q ~»pt 
staticText-15pq ~Dppq ~ 9ppppq ~ <  wñpppppt Microsoft Sans Serifq ~Îpq ~Ñq ~ [q ~ [pq ~ pq ~ pppsq ~ \psq ~ `  wñsq ~ c    ÿfffppppq ~ hsq ~ j    q ~Ýq ~Ýq ~Úpsq ~ l  wñsq ~ c    ÿfffppppq ~ hsq ~ j    q ~Ýq ~Ýpsq ~ a  wñppppq ~Ýq ~Ýpsq ~ p  wñsq ~ c    ÿfffppppq ~ hsq ~ j    q ~Ýq ~Ýpsq ~ s  wñsq ~ c    ÿfffppppq ~ hsq ~ j    q ~Ýq ~Ýpq ~Wpppt Helvetica-BoldObliqueppppppppppq ~Õt (0xx62) 3251-5820sq ~ F  wñ           K  Ó   3pq ~ q ~»pt 
textField-211ppppq ~ 9ppppq ~ <  wñpppppt Arialq ~ Vpq ~Ñq ~ [ppppppppsq ~ \q ~Hsq ~ `  wñsq ~ c    ÿfffppppq ~ hsq ~ j    q ~ðq ~ðq ~ípsq ~ l  wñsq ~ c    ÿfffppppq ~ hsq ~ j    q ~ðq ~ðpsq ~ a  wñppppq ~ðq ~ðpsq ~ p  wñsq ~ c    ÿ   ppppq ~ hsq ~ j    q ~ðq ~ðpsq ~ s  wñsq ~ c    ÿ   ppppq ~ hsq ~ j    q ~ðq ~ðpppppt Helvetica-Boldppppppppppp  wñ        ppq ~ xsq ~ z   uq ~ }   sq ~ t "PÃ¡gina: " + sq ~ t PAGE_NUMBERsq ~ t 	 + " de "t java.lang.Stringppppppq ~ pppsq ~   wñ           ½  @   pq ~ q ~»pt 
staticText-13ppppq ~ 9ppppq ~ <  wñppppppsq ~ T   pq ~ Xq ~ [ppppppppsq ~ \psq ~ `  wñppppq ~q ~q ~psq ~ l  wñppppq ~q ~psq ~ a  wñppppq ~q ~psq ~ p  wñppppq ~q ~psq ~ s  wñppppq ~q ~pppppt Helvetica-Boldpppppppppppt Lista de Acessossq ~ F  wñ           q   V   pq ~ q ~»pt 
textField-209ppppq ~ 9ppppq ~ <  wñpppppppppq ~ [ppppppppsq ~ \psq ~ `  wñppppq ~q ~q ~psq ~ l  wñppppq ~q ~psq ~ a  wñppppq ~q ~psq ~ p  wñppppq ~q ~psq ~ s  wñppppq ~q ~pppppt Helvetica-Boldppppppppppp  wñ        ppq ~ xsq ~ z   uq ~ }   sq ~ t enderecoEmpresat java.lang.Stringppppppq ~ pppsq ~ F  wñ           q   V   pq ~ q ~»pt 
textField-208ppppq ~ 9ppppq ~ <  wñpppppppppq ~ [ppppppppsq ~ \psq ~ `  wñppppq ~#q ~#q ~!psq ~ l  wñppppq ~#q ~#psq ~ a  wñppppq ~#q ~#psq ~ p  wñppppq ~#q ~#psq ~ s  wñppppq ~#q ~#pppppt Helvetica-Boldppppppppppp  wñ        ppq ~ xsq ~ z   uq ~ }   sq ~ t nomeEmpresat java.lang.Stringppppppq ~ pppsr ,net.sf.jasperreports.engine.base.JRBaseImage      'Ø *I PSEUDO_SERIAL_VERSION_UIDI 
bookmarkLevelB evaluationTimeB hyperlinkTargetB 
hyperlinkTypeZ isLazyB onErrorTypeL anchorNameExpressionq ~ L borderq ~ L borderColorq ~ .L bottomBorderq ~ L bottomBorderColorq ~ .L 
bottomPaddingq ~ KL evaluationGroupq ~ 2L evaluationTimeValueq ~ GL 
expressionq ~ L horizontalAlignmentq ~ L horizontalAlignmentValueq ~ LL hyperlinkAnchorExpressionq ~ L hyperlinkPageExpressionq ~ [ hyperlinkParametersq ~ HL hyperlinkReferenceExpressionq ~ L hyperlinkTooltipExpressionq ~ L isUsingCacheq ~ IL 
leftBorderq ~ L leftBorderColorq ~ .L leftPaddingq ~ KL lineBoxq ~ ML 
linkTargetq ~ L linkTypeq ~ L onErrorTypeValuet 2Lnet/sf/jasperreports/engine/type/OnErrorTypeEnum;L paddingq ~ KL rightBorderq ~ L rightBorderColorq ~ .L rightPaddingq ~ KL 
scaleImageq ~ L scaleImageValuet 1Lnet/sf/jasperreports/engine/type/ScaleImageEnum;L 	topBorderq ~ L topBorderColorq ~ .L 
topPaddingq ~ KL verticalAlignmentq ~ L verticalAlignmentValueq ~ Pxq ~ *  wñ   .       R       pq ~ q ~»pt image-1ppppq ~ 9ppppq ~ <  wîppsq ~ >  wñppppq ~2p  wñ         ppppppp~q ~ wt PAGEsq ~ z   uq ~ }   sq ~ t logoPadraoRelatoriot java.io.InputStreamppppppppq ~ [pppsq ~ \psq ~ `  wñsq ~ c    ÿfffppppq ~ hsq ~ j?   q ~<q ~<q ~2psq ~ l  wñsq ~ c    ÿfffppppq ~ hsq ~ j?   q ~<q ~<psq ~ a  wñppppq ~<q ~<psq ~ p  wñsq ~ c    ÿfffppppq ~ hsq ~ j?   q ~<q ~<psq ~ s  wñsq ~ c    ÿfffppppq ~ hsq ~ j?   q ~<q ~<pp~r 0net.sf.jasperreports.engine.type.OnErrorTypeEnum          xq ~ t BLANKpppppppppppsq ~   wñ            :   pq ~ q ~»pt 
staticText-14pq ~Dppq ~ 9ppppq ~ <  wñpppppt Microsoft Sans Serifq ~Îpq ~Ñq ~ [q ~ [pq ~ pq ~ pppsq ~ \psq ~ `  wñsq ~ c    ÿfffppppq ~ hsq ~ j    q ~Pq ~Pq ~Mpsq ~ l  wñsq ~ c    ÿfffppppq ~ hsq ~ j    q ~Pq ~Ppsq ~ a  wñppppq ~Pq ~Ppsq ~ p  wñsq ~ c    ÿfffppppq ~ hsq ~ j    q ~Pq ~Ppsq ~ s  wñsq ~ c    ÿfffppppq ~ hsq ~ j    q ~Pq ~Ppq ~Wpppt Helvetica-BoldObliqueppppppppppq ~Õt eZillyon - Sistema Administrativo para Academias Desenvolvido por PACTO SoluÃ§Ãµes TecnolÃ³gicas Ltda.sq ~ F  wñ           q   V   /pq ~ q ~»pt 
textField-210ppppq ~ 9ppppq ~ <  wñpppppppppq ~ [ppppppppsq ~ \psq ~ `  wñppppq ~bq ~bq ~`psq ~ l  wñppppq ~bq ~bpsq ~ a  wñppppq ~bq ~bpsq ~ p  wñppppq ~bq ~bpsq ~ s  wñppppq ~bq ~bpppppt Helvetica-Boldppppppppppp  wñ        ppq ~ xsq ~ z   uq ~ }   sq ~ t 
cidadeEmpresat java.lang.Stringppppppq ~ pppxp  wñ   Eppq ~ ~r /net.sf.jasperreports.engine.type.PrintOrderEnum          xq ~ t VERTICALpsq ~ sq ~     w    xp  wñ    ppq ~ psq ~ sq ~     w    xp  wñ    ppq ~ ~r 3net.sf.jasperreports.engine.type.WhenNoDataTypeEnum          xq ~ t NO_PAGESsr 6net.sf.jasperreports.engine.design.JRReportCompileData      'Ø L crosstabCompileDataq ~ èL datasetCompileDataq ~ èL mainDatasetCompileDataq ~ xpsq ~o?@     w       xsq ~o?@     w      q ~ áur [B¬óøTà  xp  ÖÊþº¾   .  0ListaAcessoRelCliente_Teste_1579609286605_296783  ,net/sf/jasperreports/engine/fill/JREvaluator  parameter_REPORT_LOCALE 2Lnet/sf/jasperreports/engine/fill/JRFillParameter; parameter_JASPER_REPORT parameter_REPORT_TIME_ZONE parameter_SORT_FIELDS parameter_REPORT_FILE_RESOLVER parameter_REPORT_SCRIPTLET parameter_REPORT_PARAMETERS_MAP parameter_REPORT_CONNECTION parameter_REPORT_CLASS_LOADER parameter_REPORT_DATA_SOURCE $parameter_REPORT_URL_HANDLER_FACTORY parameter_REPORT_FORMAT_FACTORY parameter_REPORT_MAX_COUNT parameter_REPORT_TEMPLATES  parameter_REPORT_RESOURCE_BUNDLE variable_PAGE_NUMBER 1Lnet/sf/jasperreports/engine/fill/JRFillVariable; variable_COLUMN_NUMBER variable_REPORT_COUNT variable_PAGE_COUNT variable_COLUMN_COUNT <init> ()V Code  
    	     	  "  	  $ 	 	  & 
 	  (  	  *  	  , 
 	  .  	  0  	  2  	  4  	  6  	  8  	  :  	  <  	  >  	  @  	  B  	  D  	  F LineNumberTable customizedInit 0(Ljava/util/Map;Ljava/util/Map;Ljava/util/Map;)V 
initParams (Ljava/util/Map;)V K L
  M 
initFields O L
  P initVars R L
  S 
REPORT_LOCALE U 
java/util/Map W get &(Ljava/lang/Object;)Ljava/lang/Object; Y Z X [ 0net/sf/jasperreports/engine/fill/JRFillParameter ] 
JASPER_REPORT _ REPORT_TIME_ZONE a SORT_FIELDS c REPORT_FILE_RESOLVER e REPORT_SCRIPTLET g REPORT_PARAMETERS_MAP i REPORT_CONNECTION k REPORT_CLASS_LOADER m REPORT_DATA_SOURCE o REPORT_URL_HANDLER_FACTORY q REPORT_FORMAT_FACTORY s REPORT_MAX_COUNT u REPORT_TEMPLATES w REPORT_RESOURCE_BUNDLE y PAGE_NUMBER { /net/sf/jasperreports/engine/fill/JRFillVariable } 
COLUMN_NUMBER  REPORT_COUNT  
PAGE_COUNT  COLUMN_COUNT  evaluate (I)Ljava/lang/Object; 
Exceptions java/lang/Throwable  java/lang/Integer  (I)V  
   evaluateOld evaluateEstimated 
SourceFile !                      	     
               
                                                                        Õ     i*· *µ !*µ #*µ %*µ '*µ )*µ +*µ -*µ /*µ 1*µ 3*µ 5*µ 7*µ 9*µ ;*µ =*µ ?*µ A*µ C*µ E*µ G±    H   Z       	          ! " " ' # , $ 1 % 6 & ; ' @ ( E ) J * O + T , Y - ^ . c / h   I J     4     *+· N*,· Q*-· T±    H       ;  < 
 =  >  K L    c    *+V¹ \ À ^À ^µ !*+`¹ \ À ^À ^µ #*+b¹ \ À ^À ^µ %*+d¹ \ À ^À ^µ '*+f¹ \ À ^À ^µ )*+h¹ \ À ^À ^µ +*+j¹ \ À ^À ^µ -*+l¹ \ À ^À ^µ /*+n¹ \ À ^À ^µ 1*+p¹ \ À ^À ^µ 3*+r¹ \ À ^À ^µ 5*+t¹ \ À ^À ^µ 7*+v¹ \ À ^À ^µ 9*+x¹ \ À ^À ^µ ;*+z¹ \ À ^À ^µ =±    H   B    F  G $ H 6 I H J Z K l L ~ M  N ¢ O ´ P Æ Q Ø R ê S ü T U  O L           ±    H       ]  R L          [*+|¹ \ À ~À ~µ ?*+¹ \ À ~À ~µ A*+¹ \ À ~À ~µ C*+¹ \ À ~À ~µ E*+¹ \ À ~À ~µ G±    H       e  f $ g 6 h H i Z j              ë     Mª             -   9   E   Q   ]   i   u   » Y· M§ T» Y· M§ H» Y· M§ <» Y· M§ 0» Y· M§ $» Y· M§ » Y· M§ » Y· M,°    H   J    r  t 0 x 9 y < } E ~ H  Q  T  ]  `  i  l  u  x       £              ë     Mª             -   9   E   Q   ]   i   u   » Y· M§ T» Y· M§ H» Y· M§ <» Y· M§ 0» Y· M§ $» Y· M§ » Y· M§ » Y· M,°    H   J    ¬  ® 0 ² 9 ³ < · E ¸ H ¼ Q ½ T Á ] Â ` Æ i Ç l Ë u Ì x Ð  Ñ  Õ  Ý              ë     Mª             -   9   E   Q   ]   i   u   » Y· M§ T» Y· M§ H» Y· M§ <» Y· M§ 0» Y· M§ $» Y· M§ » Y· M§ » Y· M,°    H   J    æ  è 0 ì 9 í < ñ E ò H ö Q ÷ T û ] ü `  i l u x
         xuq ~|  (Êþº¾   .t *ListaAcessoRelCliente_1579609286605_296783  ,net/sf/jasperreports/engine/fill/JREvaluator  parameter_enderecoEmpresa 2Lnet/sf/jasperreports/engine/fill/JRFillParameter; parameter_JASPER_REPORT parameter_REPORT_TIME_ZONE parameter_usuario parameter_REPORT_FILE_RESOLVER parameter_REPORT_PARAMETERS_MAP parameter_SUBREPORT_DIR1 parameter_REPORT_CLASS_LOADER $parameter_REPORT_URL_HANDLER_FACTORY parameter_REPORT_DATA_SOURCE parameter_IS_IGNORE_PAGINATION parameter_REPORT_MAX_COUNT parameter_REPORT_TEMPLATES parameter_dataIni parameter_REPORT_LOCALE parameter_REPORT_VIRTUALIZER parameter_SORT_FIELDS parameter_logoPadraoRelatorio parameter_REPORT_SCRIPTLET parameter_REPORT_CONNECTION parameter_SUBREPORT_DIR parameter_dataFim parameter_REPORT_FORMAT_FACTORY parameter_tituloRelatorio parameter_nomeEmpresa parameter_cidadeEmpresa  parameter_REPORT_RESOURCE_BUNDLE parameter_versaoSoftware parameter_filtros field_cliente46pessoa46nome .Lnet/sf/jasperreports/engine/fill/JRFillField; 
field_sentido field_intervaloDataHoras )field_meioIdentificacaoEntrada46descricao &field_usuario46primeiroNomeConcatenado field_situacao46descricao field_cliente46pessoa46email field_situacao46id field_coletor46descricao field_cliente46matricula field_dataHoraEntrada  field_localAcesso46empresa46nome field_dataHoraSaida variable_PAGE_NUMBER 1Lnet/sf/jasperreports/engine/fill/JRFillVariable; variable_COLUMN_NUMBER variable_REPORT_COUNT variable_PAGE_COUNT variable_COLUMN_COUNT variable_nomeCliente_COUNT variable_totalclientes variable_totalalunos <init> ()V Code : ;
  =  	  ?  	  A  	  C 	 	  E 
 	  G  	  I  	  K 
 	  M  	  O  	  Q  	  S  	  U  	  W  	  Y  	  [  	  ]  	  _  	  a  	  c  	  e  	  g  	  i  	  k  	  m  	  o  	  q   	  s ! 	  u " 	  w # $	  y % $	  { & $	  } ' $	   ( $	   ) $	   * $	   + $	   , $	   - $	   . $	   / $	   0 $	   1 2	   3 2	   4 2	   5 2	   6 2	   7 2	   8 2	   9 2	  ¡ LineNumberTable customizedInit 0(Ljava/util/Map;Ljava/util/Map;Ljava/util/Map;)V 
initParams (Ljava/util/Map;)V ¦ §
  ¨ 
initFields ª §
  « initVars ­ §
  ® enderecoEmpresa ° 
java/util/Map ² get &(Ljava/lang/Object;)Ljava/lang/Object; ´ µ ³ ¶ 0net/sf/jasperreports/engine/fill/JRFillParameter ¸ 
JASPER_REPORT º REPORT_TIME_ZONE ¼ usuario ¾ REPORT_FILE_RESOLVER À REPORT_PARAMETERS_MAP Â SUBREPORT_DIR1 Ä REPORT_CLASS_LOADER Æ REPORT_URL_HANDLER_FACTORY È REPORT_DATA_SOURCE Ê IS_IGNORE_PAGINATION Ì REPORT_MAX_COUNT Î REPORT_TEMPLATES Ð dataIni Ò 
REPORT_LOCALE Ô REPORT_VIRTUALIZER Ö SORT_FIELDS Ø logoPadraoRelatorio Ú REPORT_SCRIPTLET Ü REPORT_CONNECTION Þ 
SUBREPORT_DIR à dataFim â REPORT_FORMAT_FACTORY ä tituloRelatorio æ nomeEmpresa è 
cidadeEmpresa ê REPORT_RESOURCE_BUNDLE ì versaoSoftware î filtros ð cliente.pessoa.nome ò ,net/sf/jasperreports/engine/fill/JRFillField ô sentido ö intervaloDataHoras ø "meioIdentificacaoEntrada.descricao ú usuario.primeiroNomeConcatenado ü situacao.descricao þ cliente.pessoa.email  situacao.id coletor.descricao cliente.matricula dataHoraEntrada localAcesso.empresa.nome
 
dataHoraSaida PAGE_NUMBER /net/sf/jasperreports/engine/fill/JRFillVariable 
COLUMN_NUMBER REPORT_COUNT 
PAGE_COUNT COLUMN_COUNT nomeCliente_COUNT 
totalclientes totalalunos evaluate (I)Ljava/lang/Object; 
Exceptions java/lang/Throwable# dD:\PactoJ\Desenvolvimento\Sistemas\ZillyonWeb\tronco-novo\src\java\relatorio\designRelatorio\outros\% java/lang/Integer' (I)V :)
(* getValue ()Ljava/lang/Object;,-
 õ. java/lang/String0 (Ljava/lang/String;)V :2
(3 java/lang/StringBuffer5 valueOf &(Ljava/lang/Object;)Ljava/lang/String;78
19
63 trim ()Ljava/lang/String;<=
1>  @ equals (Ljava/lang/Object;)ZBC
1D  - F append ,(Ljava/lang/String;)Ljava/lang/StringBuffer;HI
6J toStringL=
6M
.  P ,(Ljava/lang/Object;)Ljava/lang/StringBuffer;HR
6S 	PÃ¡gina: U  de W
 ¹. java/io/InputStreamZ java/util/Date\ java/sql/Timestamp^ RV_LIBACESSOAUTORIZADO` java/lang/Booleanb (Z)Ljava/lang/Boolean;7d
ce   UsuÃ¡rio:g
] = evaluateOld getOldValuek-
 õl
l evaluateEstimated getEstimatedValuep-
q 
SourceFile !     2                 	     
               
                                                                                                     !     "     # $    % $    & $    ' $    ( $    ) $    * $    + $    , $    - $    . $    / $    0 $    1 2    3 2    4 2    5 2    6 2    7 2    8 2    9 2     : ;  <  ã     ÿ*· >*µ @*µ B*µ D*µ F*µ H*µ J*µ L*µ N*µ P*µ R*µ T*µ V*µ X*µ Z*µ \*µ ^*µ `*µ b*µ d*µ f*µ h*µ j*µ l*µ n*µ p*µ r*µ t*µ v*µ x*µ z*µ |*µ ~*µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ  *µ ¢±    £   Ò 4      	          ! " " ' # , $ 1 % 6 & ; ' @ ( E ) J * O + T , Y - ^ . c / h 0 m 1 r 2 w 3 | 4  5  6  7  8  9  :  ; ¤ < © = ® > ³ ? ¸ @ ½ A Â B Ç C Ì D Ñ E Ö F Û G à H å I ê J ï K ô L ù M þ   ¤ ¥  <   4     *+· ©*,· ¬*-· ¯±    £       Y  Z 
 [  \  ¦ §  <      *+±¹ · À ¹À ¹µ @*+»¹ · À ¹À ¹µ B*+½¹ · À ¹À ¹µ D*+¿¹ · À ¹À ¹µ F*+Á¹ · À ¹À ¹µ H*+Ã¹ · À ¹À ¹µ J*+Å¹ · À ¹À ¹µ L*+Ç¹ · À ¹À ¹µ N*+É¹ · À ¹À ¹µ P*+Ë¹ · À ¹À ¹µ R*+Í¹ · À ¹À ¹µ T*+Ï¹ · À ¹À ¹µ V*+Ñ¹ · À ¹À ¹µ X*+Ó¹ · À ¹À ¹µ Z*+Õ¹ · À ¹À ¹µ \*+×¹ · À ¹À ¹µ ^*+Ù¹ · À ¹À ¹µ `*+Û¹ · À ¹À ¹µ b*+Ý¹ · À ¹À ¹µ d*+ß¹ · À ¹À ¹µ f*+á¹ · À ¹À ¹µ h*+ã¹ · À ¹À ¹µ j*+å¹ · À ¹À ¹µ l*+ç¹ · À ¹À ¹µ n*+é¹ · À ¹À ¹µ p*+ë¹ · À ¹À ¹µ r*+í¹ · À ¹À ¹µ t*+ï¹ · À ¹À ¹µ v*+ñ¹ · À ¹À ¹µ x±    £   z    d  e $ f 6 g H h Z i l j ~ k  l ¢ m ´ n Æ o Ø p ê q ü r s  t2 uD vV wh xz y z {° |Â }Ô ~æ ø 
   ª §  <  >     ò*+ó¹ · À õÀ õµ z*+÷¹ · À õÀ õµ |*+ù¹ · À õÀ õµ ~*+û¹ · À õÀ õµ *+ý¹ · À õÀ õµ *+ÿ¹ · À õÀ õµ *+¹ · À õÀ õµ *+¹ · À õÀ õµ *+¹ · À õÀ õµ *+¹ · À õÀ õµ *+	¹ · À õÀ õµ *+¹ · À õÀ õµ *+
¹ · À õÀ õµ ±    £   :       $  6  H  Z  l      ¥  ¸  Ë  Þ  ñ   ­ §  <   Ñ     *+¹ · ÀÀµ *+¹ · ÀÀµ *+¹ · ÀÀµ *+¹ · ÀÀµ *+¹ · ÀÀµ *+¹ · ÀÀµ *+¹ · ÀÀµ  *+¹ · ÀÀµ ¢±    £   & 	      &   9 ¡ L ¢ _ £ r ¤  ¥  ¦  ! "    $ <  }    9Mª  4       $   ¡   ¨   ´   À   Ì   Ø   ä   ð   ü         5  J  X  ¹  Ç  å  	    %  3  A  O  ]  k  |      ·  Å  Ó  ä  ò      )&M§»(Y·+M§»(Y·+M§w»(Y·+M§k»(Y·+M§_»(Y·+M§S»(Y·+M§G»(Y·+M§;»(Y·+M§/»(Y·+M§#»(Y·+M§»(Y*´ ¶/À1·4M§»(Y*´ ¶/À1·4M§í*´ z¶/À1M§ß»6Y*´ z¶/À1¸:·;*´ ¶/À1¶?A¶E *´ ¶/À1Ç 	A§ »6YG·;*´ ¶/À1¶K¶N¶K¶NM§~*´ ¶OÀ(M§p»6YQ·;*´ ¶OÀ(¶T¶NM§R»6YV·;*´ ¶OÀ(¶TX¶K¶NM§.*´ @¶YÀ1M§ *´ p¶YÀ1M§*´ b¶YÀ[M§*´ r¶YÀ1M§ ö*´ x¶YÀ1M§ è*´ ~¶/À1M§ Ú*´ |¶/À1M§ Ì*´ ¶/À]À_M§ »*´ ¶/À1M§ ­*´ ¶/À1M§ *´ ¶/À1a¶E § ¸fM§ *´ ¶/À1M§ r*´ ¶/À1M§ d*´ ¶/À]À_M§ S*´ ¶/À1M§ E»6Yh·;*´ F¶YÀ1¶K¶NM§ '*´  ¶OÀ(M§ »]Y·iM§ *´ ¢¶OÀ(M,°    £  2 L   ®  ° ¤ ´ ¨ µ « ¹ ´ º · ¾ À ¿ Ã Ã Ì Ä Ï È Ø É Û Í ä Î ç Ò ð Ó ó × ü Ø ÿ Ü Ý á â æ  ç# ë5 ì8 ðJ ñM õX ö[ ú¹ û¼ ÿÇ Êåè		
%(36AD"O#R'](`,k-n1|267;<@·AºEÅFÈJÓKÖOäPçTòUõYZ^_!c)d,h7p j! "    $ <  }    9Mª  4       $   ¡   ¨   ´   À   Ì   Ø   ä   ð   ü         5  J  X  ¹  Ç  å  	    %  3  A  O  ]  k  |      ·  Å  Ó  ä  ò      )&M§»(Y·+M§»(Y·+M§w»(Y·+M§k»(Y·+M§_»(Y·+M§S»(Y·+M§G»(Y·+M§;»(Y·+M§/»(Y·+M§#»(Y·+M§»(Y*´ ¶mÀ1·4M§»(Y*´ ¶mÀ1·4M§í*´ z¶mÀ1M§ß»6Y*´ z¶mÀ1¸:·;*´ ¶mÀ1¶?A¶E *´ ¶mÀ1Ç 	A§ »6YG·;*´ ¶mÀ1¶K¶N¶K¶NM§~*´ ¶nÀ(M§p»6YQ·;*´ ¶nÀ(¶T¶NM§R»6YV·;*´ ¶nÀ(¶TX¶K¶NM§.*´ @¶YÀ1M§ *´ p¶YÀ1M§*´ b¶YÀ[M§*´ r¶YÀ1M§ ö*´ x¶YÀ1M§ è*´ ~¶mÀ1M§ Ú*´ |¶mÀ1M§ Ì*´ ¶mÀ]À_M§ »*´ ¶mÀ1M§ ­*´ ¶mÀ1M§ *´ ¶mÀ1a¶E § ¸fM§ *´ ¶mÀ1M§ r*´ ¶mÀ1M§ d*´ ¶mÀ]À_M§ S*´ ¶mÀ1M§ E»6Yh·;*´ F¶YÀ1¶K¶NM§ '*´  ¶nÀ(M§ »]Y·iM§ *´ ¢¶nÀ(M,°    £  2 L  y { ¤ ¨ « ´ · À Ã Ì Ï Ø Û ä ç ð ó¢ ü£ ÿ§¨¬­± ²#¶5·8»J¼MÀXÁ[Å¹Æ¼ÊÇËÊÏåÐèÔ	ÕÙÚÞ%ß(ã3ä6èAéDíOîRò]ó`÷kønü|ý·ºÅÈÓÖäçò õ$%)*!.)/,37; o! "    $ <  }    9Mª  4       $   ¡   ¨   ´   À   Ì   Ø   ä   ð   ü         5  J  X  ¹  Ç  å  	    %  3  A  O  ]  k  |      ·  Å  Ó  ä  ò      )&M§»(Y·+M§»(Y·+M§w»(Y·+M§k»(Y·+M§_»(Y·+M§S»(Y·+M§G»(Y·+M§;»(Y·+M§/»(Y·+M§#»(Y·+M§»(Y*´ ¶/À1·4M§»(Y*´ ¶/À1·4M§í*´ z¶/À1M§ß»6Y*´ z¶/À1¸:·;*´ ¶/À1¶?A¶E *´ ¶/À1Ç 	A§ »6YG·;*´ ¶/À1¶K¶N¶K¶NM§~*´ ¶rÀ(M§p»6YQ·;*´ ¶rÀ(¶T¶NM§R»6YV·;*´ ¶rÀ(¶TX¶K¶NM§.*´ @¶YÀ1M§ *´ p¶YÀ1M§*´ b¶YÀ[M§*´ r¶YÀ1M§ ö*´ x¶YÀ1M§ è*´ ~¶/À1M§ Ú*´ |¶/À1M§ Ì*´ ¶/À]À_M§ »*´ ¶/À1M§ ­*´ ¶/À1M§ *´ ¶/À1a¶E § ¸fM§ *´ ¶/À1M§ r*´ ¶/À1M§ d*´ ¶/À]À_M§ S*´ ¶/À1M§ E»6Yh·;*´ F¶YÀ1¶K¶NM§ '*´  ¶rÀ(M§ »]Y·iM§ *´ ¢¶rÀ(M,°    £  2 L  D F ¤J ¨K «O ´P ·T ÀU ÃY ÌZ Ï^ Ø_ Ûc äd çh ði óm ün ÿrswx| }#58JMX[¹¼ÇÊåè	 ¤¥©%ª(®3¯6³A´D¸O¹R½]¾`ÂkÃnÇ|ÈÌÍÑÒÖ·×ºÛÅÜÈàÓáÖåäæçêòëõïðôõ!ù)ú,þ7 s    t _1579609286605_296783t 2net.sf.jasperreports.engine.design.JRJavacCompiler
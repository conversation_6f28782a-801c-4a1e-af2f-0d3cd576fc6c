¬í sr (net.sf.jasperreports.engine.JasperReport      'Ø L compileDatat Ljava/io/Serializable;L compileNameSuffixt Ljava/lang/String;L 
compilerClassq ~ xr -net.sf.jasperreports.engine.base.JRBaseReport      'Ø +I PSEUDO_SERIAL_VERSION_UIDI bottomMarginI columnCountI 
columnSpacingI columnWidthZ ignorePaginationZ isFloatColumnFooterZ isSummaryNewPageZ  isSummaryWithPageHeaderAndFooterZ isTitleNewPageI 
leftMarginB orientationI 
pageHeightI 	pageWidthB 
printOrderI rightMarginI 	topMarginB whenNoDataTypeL 
backgroundt $Lnet/sf/jasperreports/engine/JRBand;L columnFooterq ~ L columnHeaderq ~ [ datasetst ([Lnet/sf/jasperreports/engine/JRDataset;L defaultFontt *Lnet/sf/jasperreports/engine/JRReportFont;L defaultStylet %Lnet/sf/jasperreports/engine/JRStyle;L detailq ~ L 
detailSectiont 'Lnet/sf/jasperreports/engine/JRSection;[ fontst +[Lnet/sf/jasperreports/engine/JRReportFont;L formatFactoryClassq ~ L 
importsSett Ljava/util/Set;L languageq ~ L lastPageFooterq ~ L mainDatasett 'Lnet/sf/jasperreports/engine/JRDataset;L nameq ~ L noDataq ~ L orientationValuet 2Lnet/sf/jasperreports/engine/type/OrientationEnum;L 
pageFooterq ~ L 
pageHeaderq ~ L printOrderValuet 1Lnet/sf/jasperreports/engine/type/PrintOrderEnum;[ stylest &[Lnet/sf/jasperreports/engine/JRStyle;L summaryq ~ [ 	templatest /[Lnet/sf/jasperreports/engine/JRReportTemplate;L titleq ~ L whenNoDataTypeValuet 5Lnet/sf/jasperreports/engine/type/WhenNoDataTypeEnum;xp  wî            "           S  J        sr +net.sf.jasperreports.engine.base.JRBaseBand      'Ø I PSEUDO_SERIAL_VERSION_UIDI heightZ isSplitAllowedL printWhenExpressiont *Lnet/sf/jasperreports/engine/JRExpression;L 	splitTypet Ljava/lang/Byte;L splitTypeValuet 0Lnet/sf/jasperreports/engine/type/SplitTypeEnum;xr 3net.sf.jasperreports.engine.base.JRBaseElementGroup      'Ø L childrent Ljava/util/List;L elementGroupt ,Lnet/sf/jasperreports/engine/JRElementGroup;xpsr java.util.ArrayListxÒÇa I sizexp    w    xp  wî    pp~r .net.sf.jasperreports.engine.type.SplitTypeEnum          xr java.lang.Enum          xpt STRETCHsq ~ sq ~    w   sr 1net.sf.jasperreports.engine.base.JRBaseStaticText      'Ø L textq ~ xr 2net.sf.jasperreports.engine.base.JRBaseTextElement      'Ø %I PSEUDO_SERIAL_VERSION_UIDL borderq ~ L borderColort Ljava/awt/Color;L bottomBorderq ~ L bottomBorderColorq ~ $L 
bottomPaddingt Ljava/lang/Integer;L fontNameq ~ L fontSizeq ~ %L horizontalAlignmentq ~ L horizontalAlignmentValuet 6Lnet/sf/jasperreports/engine/type/HorizontalAlignEnum;L isBoldt Ljava/lang/Boolean;L isItalicq ~ 'L 
isPdfEmbeddedq ~ 'L isStrikeThroughq ~ 'L isStyledTextq ~ 'L isUnderlineq ~ 'L 
leftBorderq ~ L leftBorderColorq ~ $L leftPaddingq ~ %L lineBoxt 'Lnet/sf/jasperreports/engine/JRLineBox;L lineSpacingq ~ L lineSpacingValuet 2Lnet/sf/jasperreports/engine/type/LineSpacingEnum;L markupq ~ L paddingq ~ %L pdfEncodingq ~ L pdfFontNameq ~ L 
reportFontq ~ L rightBorderq ~ L rightBorderColorq ~ $L rightPaddingq ~ %L rotationq ~ L 
rotationValuet /Lnet/sf/jasperreports/engine/type/RotationEnum;L 	topBorderq ~ L topBorderColorq ~ $L 
topPaddingq ~ %L verticalAlignmentq ~ L verticalAlignmentValuet 4Lnet/sf/jasperreports/engine/type/VerticalAlignEnum;xr .net.sf.jasperreports.engine.base.JRBaseElement      'Ø I PSEUDO_SERIAL_VERSION_UIDI heightZ isPrintInFirstWholeBandZ isPrintRepeatedValuesZ isPrintWhenDetailOverflowsZ isRemoveLineWhenBlankB positionTypeB stretchTypeI widthI xI yL 	backcolorq ~ $L defaultStyleProvidert 4Lnet/sf/jasperreports/engine/JRDefaultStyleProvider;L elementGroupq ~ L 	forecolorq ~ $L keyq ~ L modeq ~ L 	modeValuet +Lnet/sf/jasperreports/engine/type/ModeEnum;L parentStyleq ~ L parentStyleNameReferenceq ~ L positionTypeValuet 3Lnet/sf/jasperreports/engine/type/PositionTypeEnum;L printWhenExpressionq ~ L printWhenGroupChangest %Lnet/sf/jasperreports/engine/JRGroup;L 
propertiesMapt -Lnet/sf/jasperreports/engine/JRPropertiesMap;[ propertyExpressionst 3[Lnet/sf/jasperreports/engine/JRPropertyExpression;L stretchTypeValuet 2Lnet/sf/jasperreports/engine/type/StretchTypeEnum;xp  wî           '      pq ~ q ~  pppppp~r 1net.sf.jasperreports.engine.type.PositionTypeEnum          xq ~ t FIX_RELATIVE_TO_TOPpppp~r 0net.sf.jasperreports.engine.type.StretchTypeEnum          xq ~ t 
NO_STRETCH  wîpppppppppsr java.lang.BooleanÍ rÕúî Z valuexpppppppppsr .net.sf.jasperreports.engine.base.JRBaseLineBox      'Ø L 
bottomPaddingq ~ %L 	bottomPent +Lnet/sf/jasperreports/engine/base/JRBoxPen;L boxContainert ,Lnet/sf/jasperreports/engine/JRBoxContainer;L leftPaddingq ~ %L leftPenq ~ >L paddingq ~ %L penq ~ >L rightPaddingq ~ %L rightPenq ~ >L 
topPaddingq ~ %L topPenq ~ >xppsr 3net.sf.jasperreports.engine.base.JRBaseBoxBottomPen      'Ø  xr -net.sf.jasperreports.engine.base.JRBaseBoxPen      'Ø L lineBoxq ~ (xr *net.sf.jasperreports.engine.base.JRBasePen      'Ø I PSEUDO_SERIAL_VERSION_UIDL 	lineColorq ~ $L 	lineStyleq ~ L lineStyleValuet 0Lnet/sf/jasperreports/engine/type/LineStyleEnum;L 	lineWidtht Ljava/lang/Float;L penContainert ,Lnet/sf/jasperreports/engine/JRPenContainer;xp  wîppppq ~ @q ~ @q ~ 4psr 1net.sf.jasperreports.engine.base.JRBaseBoxLeftPen      'Ø  xq ~ B  wîppppq ~ @q ~ @psq ~ B  wîppppq ~ @q ~ @psr 2net.sf.jasperreports.engine.base.JRBaseBoxRightPen      'Ø  xq ~ B  wîppppq ~ @q ~ @psr 0net.sf.jasperreports.engine.base.JRBaseBoxTopPen      'Ø  xq ~ B  wîppppq ~ @q ~ @pppppt Helvetica-Boldpppppppppppt Total:sr 0net.sf.jasperreports.engine.base.JRBaseTextField      'Ø I PSEUDO_SERIAL_VERSION_UIDI 
bookmarkLevelB evaluationTimeB hyperlinkTargetB 
hyperlinkTypeZ isStretchWithOverflowL anchorNameExpressionq ~ L evaluationGroupq ~ 0L evaluationTimeValuet 5Lnet/sf/jasperreports/engine/type/EvaluationTimeEnum;L 
expressionq ~ L hyperlinkAnchorExpressionq ~ L hyperlinkPageExpressionq ~ [ hyperlinkParameterst 3[Lnet/sf/jasperreports/engine/JRHyperlinkParameter;L hyperlinkReferenceExpressionq ~ L hyperlinkTooltipExpressionq ~ L isBlankWhenNullq ~ 'L 
linkTargetq ~ L linkTypeq ~ L patternq ~ xq ~ #  wî           B   é   pq ~ q ~  ppppppq ~ 6ppppq ~ 9  wîppppppsr java.lang.Integerâ ¤÷8 I valuexr java.lang.Number¬à  xp   	p~r 4net.sf.jasperreports.engine.type.HorizontalAlignEnum          xq ~ t CENTERq ~ <pq ~ <ppppppsq ~ =psq ~ A  wîppppq ~ [q ~ [q ~ Tpsq ~ H  wîppppq ~ [q ~ [psq ~ B  wîppppq ~ [q ~ [psq ~ K  wîppppq ~ [q ~ [psq ~ M  wîppppq ~ [q ~ [pppppt Helvetica-Boldppppppppppp  wî        pp~r 3net.sf.jasperreports.engine.type.EvaluationTimeEnum          xq ~ t NOWsr 1net.sf.jasperreports.engine.base.JRBaseExpression      'Ø I id[ chunkst 0[Lnet/sf/jasperreports/engine/JRExpressionChunk;L valueClassNameq ~ L valueClassRealNameq ~ xp   !ur 0[Lnet.sf.jasperreports.engine.JRExpressionChunk;mYÏÞiK£U  xp   sr 6net.sf.jasperreports.engine.base.JRBaseExpressionChunk      'Ø B typeL textq ~ xpt totalAcessoLibt java.lang.Integerppppppq ~ <pppsq ~ Q  wî           H  ,   pq ~ q ~  ppppppq ~ 6ppppq ~ 9  wîppppppq ~ Wpq ~ Yq ~ <ppppppppsq ~ =psq ~ A  wîppppq ~ oq ~ oq ~ npsq ~ H  wîppppq ~ oq ~ opsq ~ B  wîppppq ~ oq ~ opsq ~ K  wîppppq ~ oq ~ opsq ~ M  wîppppq ~ oq ~ opppppt Helvetica-Boldppppppppppp  wî        ppq ~ csq ~ e   "uq ~ h   sq ~ jt totalJustificadoLibt java.lang.Integerppppppq ~ <pppsq ~ Q  wî           K  w   pq ~ q ~  ppppppq ~ 6ppppq ~ 9  wîppppppq ~ Wpq ~ Yq ~ <ppppppppsq ~ =psq ~ A  wîppppq ~ |q ~ |q ~ {psq ~ H  wîppppq ~ |q ~ |psq ~ B  wîppppq ~ |q ~ |psq ~ K  wîppppq ~ |q ~ |psq ~ M  wîppppq ~ |q ~ |pppppt Helvetica-Boldppppppppppp  wî        ppq ~ csq ~ e   #uq ~ h   sq ~ jt totalFaltaJustificarLibt java.lang.Integerppppppsq ~ ; pppsq ~ Q  wî           B   £   pq ~ q ~  ppppppq ~ 6ppppq ~ 9  wîppppppq ~ Wpq ~ Yq ~ <pq ~ <ppppppsq ~ =psq ~ A  wîppppq ~ q ~ q ~ psq ~ H  wîppppq ~ q ~ psq ~ B  wîppppq ~ q ~ psq ~ K  wîppppq ~ q ~ psq ~ M  wîppppq ~ q ~ pppppt Helvetica-Boldppppppppppp  wî        ppq ~ csq ~ e   $uq ~ h   sq ~ jt percentualLiberacaosq ~ jt /100t java.lang.Doubleppppppq ~ <ppt 
#,##0.00 %sq ~ "  wî                pq ~ q ~  pt staticText-1pppp~q ~ 5t FLOATppppq ~ 9  wîppppppsq ~ U   ppq ~ <ppppppppsq ~ =psq ~ A  wîppppq ~ q ~ q ~ psq ~ H  wîppppq ~ q ~ psq ~ B  wîppppq ~ q ~ psq ~ K  wîppppq ~ q ~ psq ~ M  wîppppq ~ q ~ pppppt Helvetica-Boldpppppppppp~r 2net.sf.jasperreports.engine.type.VerticalAlignEnum          xq ~ t BOTTOMt $RelaÃ§Ã£o dos Acessos de LiberaÃ§Ã£oxp  wî   *pppsq ~ sq ~    w   sq ~ Q  wî   
               lpq ~ q ~ ©pt 
textField-214ppppq ~ 6ppppq ~ 9  wîpppppt Arialsq ~ U   
p~q ~ Xt LEFTq ~ <q ~ <pppppppsq ~ =psq ~ A  wîsr java.awt.Color¥3u F falphaI valueL cst Ljava/awt/color/ColorSpace;[ 	frgbvaluet [F[ fvalueq ~ µxp    ÿfffpppp~r .net.sf.jasperreports.engine.type.LineStyleEnum          xq ~ t SOLIDsr java.lang.FloatÚíÉ¢Û<ðì F valuexq ~ V?   q ~ ±q ~ ±q ~ «psq ~ H  wîppq ~ ¸sq ~ º?   q ~ ±q ~ ±psq ~ B  wîppppq ~ ±q ~ ±psq ~ K  wîppq ~ ¸sq ~ º?   q ~ ±q ~ ±psq ~ M  wîppq ~ ¸sq ~ º?   q ~ ±q ~ ±pppppt Helvetica-BoldObliqueppppppppppp  wî        ppq ~ csq ~ e   uq ~ h   sq ~ jt percentualLiberacaosq ~ jt /100t java.lang.Doubleppppppq ~ ppt 
#,##0.00 %sq ~ "  wî   
           %   lpq ~ q ~ ©pt staticText-4ppppq ~ 6ppppq ~ 9  wîppppppppq ~ Yq ~ <q ~ <pppppppsq ~ =psq ~ A  wîppppq ~ Îq ~ Îq ~ Ìpsq ~ H  wîppppq ~ Îq ~ Îpsq ~ B  wîppppq ~ Îq ~ Îpsq ~ K  wîppppq ~ Îq ~ Îpsq ~ M  wîppppq ~ Îq ~ Îpppppt Helvetica-Boldpppppppppp~q ~ ¥t MIDDLEt dos Acessos foram LiberaÃ§Ã£osq ~ "  wî           C   £   ypq ~ q ~ ©pt staticText-4ppppq ~ 6ppppq ~ 9  wîppppppppq ~ Yq ~ <ppppppppsq ~ =psq ~ A  wîppppq ~ Úq ~ Úq ~ Øpsq ~ H  wîppppq ~ Úq ~ Úpsq ~ B  wîppppq ~ Úq ~ Úpsq ~ K  wîppppq ~ Úq ~ Úpsq ~ M  wîppppq ~ Úq ~ Úpppppt Helvetica-Boldppppppppppq ~ Õt % Acessosq ~ "  wî           C   ç   ypq ~ q ~ ©pt staticText-4ppppq ~ 6ppppq ~ 9  wîppppppppq ~ Yq ~ <ppppppppsq ~ =psq ~ A  wîppppq ~ äq ~ äq ~ âpsq ~ H  wîppppq ~ äq ~ äpsq ~ B  wîppppq ~ äq ~ äpsq ~ K  wîppppq ~ äq ~ äpsq ~ M  wîppppq ~ äq ~ äpppppt Helvetica-Boldppppppppppq ~ Õt Total Acessosq ~ "  wî           K  +   ypq ~ q ~ ©pt staticText-4ppppq ~ 6ppppq ~ 9  wîppppppppq ~ Yq ~ <ppppppppsq ~ =psq ~ A  wîppppq ~ îq ~ îq ~ ìpsq ~ H  wîppppq ~ îq ~ îpsq ~ B  wîppppq ~ îq ~ îpsq ~ K  wîppppq ~ îq ~ îpsq ~ M  wîppppq ~ îq ~ îpppppt Helvetica-Boldppppppppppq ~ Õt JÃ¡ Justificadosq ~ "  wî           K  w   ypq ~ q ~ ©pt staticText-4ppppq ~ 6ppppq ~ 9  wîppppppppq ~ Yq ~ <ppppppppsq ~ =psq ~ A  wîppppq ~ øq ~ øq ~ öpsq ~ H  wîppppq ~ øq ~ øpsq ~ B  wîppppq ~ øq ~ øpsq ~ K  wîppppq ~ øq ~ øpsq ~ M  wîppppq ~ øq ~ øpppppt Helvetica-Boldppppppppppq ~ Õt Falta Justificarsq ~ Q  wî           B   ì   <pq ~ q ~ ©pt 	textFieldppppq ~ 6ppppq ~ 9  wîppppppq ~ Wpq ~ Ypppppppppsq ~ =psq ~ A  wîppppq ~q ~q ~ psq ~ H  wîppppq ~q ~psq ~ B  wîppppq ~q ~psq ~ K  wîppppq ~q ~psq ~ M  wîppppq ~q ~ppppppppppppppppq ~ Õ  wî       ppq ~ csq ~ e   
uq ~ h   sq ~ jt totalClit java.lang.Integerppppppq ~ ppt  sq ~ Q  wî           B   ì   Ipq ~ q ~ ©pt 	textFieldppppq ~ 6ppppq ~ 9  wîppppppq ~ Wpq ~ Ypppppppppsq ~ =psq ~ A  wîppppq ~q ~q ~psq ~ H  wîppppq ~q ~psq ~ B  wîppppq ~q ~psq ~ K  wîppppq ~q ~psq ~ M  wîppppq ~q ~ppppppppppppppppq ~ Õ  wî        ppq ~ csq ~ e   uq ~ h   sq ~ jt totalColt java.lang.Integerppppppq ~ ppq ~
sq ~ Q  wî   
                pq ~ q ~ ©pt 
textField-214ppppq ~ 6ppppq ~ 9  wîpppppt Arialq ~ ®pq ~ ¯q ~ <q ~ <pppppppsq ~ =psq ~ A  wîsq ~ ³    ÿfffppppq ~ ¸sq ~ º?   q ~q ~q ~psq ~ H  wîppq ~ ¸sq ~ º?   q ~q ~psq ~ B  wîppppq ~q ~psq ~ K  wîppq ~ ¸sq ~ º?   q ~q ~psq ~ M  wîppq ~ ¸sq ~ º?   q ~q ~pppppt Helvetica-BoldObliqueppppppppppp  wî        ppq ~ csq ~ e   uq ~ h   sq ~ jt percentualCliColsq ~ jt /100t java.lang.Doubleppppppq ~ ppt 
#,##0.00 %sq ~ "  wî   
        ó   %    pq ~ q ~ ©pt staticText-4ppppq ~ 6ppppq ~ 9  wîppppppppq ~ Yq ~ <q ~ <pppppppsq ~ =psq ~ A  wîppppq ~4q ~4q ~2psq ~ H  wîppppq ~4q ~4psq ~ B  wîppppq ~4q ~4psq ~ K  wîppppq ~4q ~4psq ~ M  wîppppq ~4q ~4pppppt Helvetica-Boldppppppppppq ~ Õt -dos Acessos foram de Clientes e Colaboradoressq ~ "  wî           ;      Lpq ~ q ~ ©ppppppq ~ 6ppppq ~ 9  wîppppppq ~ Wppq ~ ppppppppsq ~ =psq ~ A  wîppppq ~=q ~=q ~<psq ~ H  wîppppq ~=q ~=psq ~ B  wîppppq ~=q ~=psq ~ K  wîppppq ~=q ~=psq ~ M  wîppppq ~=q ~=pppppppppppppppppt Colaboradorsq ~ "  wî           F   ¤   .pq ~ q ~ ©pt staticText-4ppppq ~ 6ppppq ~ 9  wîppppppppq ~ Yq ~ <ppppppppsq ~ =psq ~ A  wîppppq ~Fq ~Fq ~Dpsq ~ H  wîppppq ~Fq ~Fpsq ~ B  wîppppq ~Fq ~Fpsq ~ K  wîppppq ~Fq ~Fpsq ~ M  wîppppq ~Fq ~Fpppppt Helvetica-Boldppppppppppq ~ Õt % Acessosq ~ "  wî           C   ê   .pq ~ q ~ ©pt staticText-4ppppq ~ 6ppppq ~ 9  wîppppppppq ~ Yq ~ <ppppppppsq ~ =psq ~ A  wîppppq ~Pq ~Pq ~Npsq ~ H  wîppppq ~Pq ~Ppsq ~ B  wîppppq ~Pq ~Ppsq ~ K  wîppppq ~Pq ~Ppsq ~ M  wîppppq ~Pq ~Ppppppt Helvetica-Boldppppppppppq ~ Õt Total Acessosq ~ Q  wî           G   ¤   Jpq ~ q ~ ©ppppppq ~ 6ppppq ~ 9  wîppppppq ~ Wpq ~ Ypppppppppsq ~ =psq ~ A  wîppppq ~Yq ~Yq ~Xpsq ~ H  wîppppq ~Yq ~Ypsq ~ B  wîppppq ~Yq ~Ypsq ~ K  wîppppq ~Yq ~Ypsq ~ M  wîppppq ~Yq ~Yppppppppppppppppq ~ Õ  wî        ppq ~ csq ~ e   uq ~ h   sq ~ jt 
percentualColsq ~ jt /100t java.lang.Doublepppppppppt 
#,##0.00 %sq ~ Q  wî           G   ¤   <pq ~ q ~ ©ppppppq ~ 6ppppq ~ 9  wîppppppq ~ Wpq ~ Ypppppppppsq ~ =psq ~ A  wîppppq ~hq ~hq ~gpsq ~ H  wîppppq ~hq ~hpsq ~ B  wîppppq ~hq ~hpsq ~ K  wîppppq ~hq ~hpsq ~ M  wîppppq ~hq ~hppppppppppppppppq ~ Õ  wî        ppq ~ csq ~ e   uq ~ h   sq ~ jt 
percentualClisq ~ jt /100t java.lang.Doublepppppppppt 
#,##0.00 %sq ~ Q  wî           B   ì   Wpq ~ q ~ ©pt 	textFieldppppq ~ 6ppppq ~ 9  wîppppppq ~ Wpq ~ Yq ~ <ppppppppsq ~ =psq ~ A  wîppppq ~xq ~xq ~vpsq ~ H  wîppppq ~xq ~xpsq ~ B  wîppppq ~xq ~xpsq ~ K  wîppppq ~xq ~xpsq ~ M  wîppppq ~xq ~xpppppt Helvetica-Boldppppppppppq ~ Õ  wî        ppq ~ csq ~ e   uq ~ h   sq ~ jt totalColsq ~ jt  + sq ~ jt totalClit java.lang.Integerppppppq ~ ppq ~
sq ~ "  wî           ;      <pq ~ q ~ ©ppppppq ~ 6ppppq ~ 9  wîppppppq ~ Wppq ~ ppppppppsq ~ =psq ~ A  wîppppq ~q ~q ~psq ~ H  wîppppq ~q ~psq ~ B  wîppppq ~q ~psq ~ K  wîppppq ~q ~psq ~ M  wîppppq ~q ~pppppppppppppppppt Clientesq ~ "  wî           ;      [pq ~ q ~ ©ppppppq ~ 6ppppq ~ 9  wîppppppq ~ ®ppq ~ <ppppppppsq ~ =psq ~ A  wîppppq ~q ~q ~psq ~ H  wîppppq ~q ~psq ~ B  wîppppq ~q ~psq ~ K  wîppppq ~q ~psq ~ M  wîppppq ~q ~pppppt Helvetica-Boldpppppppppppt Total:sq ~ Q  wî           G   ¤   Wpq ~ q ~ ©ppppppq ~ 6ppppq ~ 9  wîppppppq ~ Wpq ~ Yq ~ <ppppppppsq ~ =psq ~ A  wîppppq ~q ~q ~psq ~ H  wîppppq ~q ~psq ~ B  wîppppq ~q ~psq ~ K  wîppppq ~q ~psq ~ M  wîppppq ~q ~pppppt Helvetica-Boldppppppppppq ~ Õ  wî        ppq ~ csq ~ e   uq ~ h   sq ~ jt (sq ~ jt 
percentualClisq ~ jt + sq ~ jt 
percentualColsq ~ jt )/100t java.lang.Doublepppppppppt 
#,##0.00 %sq ~ Q  wî                pq ~ q ~ ©pt 	textFieldppppq ~ 6ppppq ~ 9  wîppppppq ~ pppppppppppsq ~ =psq ~ A  wîppppq ~±q ~±q ~¯psq ~ H  wîppppq ~±q ~±psq ~ B  wîppppq ~±q ~±psq ~ K  wîppppq ~±q ~±psq ~ M  wîppppq ~±q ~±pppppt Helvetica-Boldppppppppppq ~ Õ  wî        ppq ~ csq ~ e   uq ~ h   sq ~ jt totalAcessost java.lang.Numberppppppq ~ ppq ~
sq ~ "  wî                 pq ~ q ~ ©pt 
staticText-12ppppq ~ 6ppppq ~ 9  wîppppppq ~ pq ~ Yq ~ <ppppppppsq ~ =psq ~ A  wîppppq ~¿q ~¿q ~½psq ~ H  wîppppq ~¿q ~¿psq ~ B  wîppppq ~¿q ~¿psq ~ K  wîppppq ~¿q ~¿psq ~ M  wîppppq ~¿q ~¿pppppt Helvetica-Boldppppppppppq ~ Õt Total Geral de Acessos:xp  wî   pp~q ~ t PREVENTppppsr .net.sf.jasperreports.engine.base.JRBaseSection      'Ø [ bandst %[Lnet/sf/jasperreports/engine/JRBand;xpur %[Lnet.sf.jasperreports.engine.JRBand;Ý~ìÊ5  xp   sq ~ sq ~    w   sq ~ Q  wî           L  *   pq ~ q ~Îpt 	textFieldppppq ~ 6sq ~ e   uq ~ h   sq ~ jt tipoLiberacaoEnum.descricaosq ~ jt !=nullt java.lang.Booleanppppq ~ 9  wîppppppq ~ Wpq ~ Ypppppppppsq ~ =psq ~ A  wîppppq ~Ùq ~Ùq ~Ðpsq ~ H  wîppppq ~Ùq ~Ùpsq ~ B  wîppppq ~Ùq ~Ùpsq ~ K  wîppppq ~Ùq ~Ùpsq ~ M  wîppppq ~Ùq ~Ùppppppppppppppppq ~ Õ  wî        ppq ~ csq ~ e   uq ~ h   sq ~ jt 
jaJustificadot java.lang.Integerppppppq ~ pppsq ~ Q  wî           C   £   pq ~ q ~Îpt 	textFieldppppq ~ 6sq ~ e   uq ~ h   sq ~ jt tipoLiberacaoEnum.descricaosq ~ jt !=nullq ~Øppppq ~ 9  wîppppppq ~ Wpq ~ Ypppppppppsq ~ =psq ~ A  wîppppq ~ìq ~ìq ~äpsq ~ H  wîppppq ~ìq ~ìpsq ~ B  wîppppq ~ìq ~ìpsq ~ K  wîppppq ~ìq ~ìpsq ~ M  wîppppq ~ìq ~ìppppppppppppppppq ~ Õ  wî        ppq ~ csq ~ e   uq ~ h   sq ~ jt percentualAcessossq ~ jt /100t java.lang.Doubleppppppq ~ ppt 
#,##0.00 %sq ~ Q  wî                pq ~ q ~Îpt 	textFieldppppq ~ 6ppppq ~ 9  wîppppppq ~ Wpppppppppppsq ~ =psq ~ A  wîppppq ~üq ~üq ~úpsq ~ H  wîppppq ~üq ~üpsq ~ B  wîppppq ~üq ~üpsq ~ K  wîppppq ~üq ~üpsq ~ M  wîppppq ~üq ~üppppppppppppppppq ~ Õ  wî        ppq ~ csq ~ e   uq ~ h   sq ~ jt tipoLiberacaoEnum.descricaot java.lang.Stringppppppq ~ <ppq ~
sq ~ Q  wî           D   æ   pq ~ q ~Îpt 	textFieldppppq ~ 6sq ~ e   uq ~ h   sq ~ jt tipoLiberacaoEnum.descricaosq ~ jt !=nullq ~Øppppq ~ 9  wîppppppq ~ Wpq ~ Ypppppppppsq ~ =psq ~ A  wîppppq ~q ~q ~psq ~ H  wîppppq ~q ~psq ~ B  wîppppq ~q ~psq ~ K  wîppppq ~q ~psq ~ M  wîppppq ~q ~ppppppppppppppppq ~ Õ  wî        ppq ~ csq ~ e   uq ~ h   sq ~ jt totalAcessot java.lang.Integerppppppq ~ pppsq ~ Q  wî           L  v   pq ~ q ~Îpt 	textFieldppppq ~ 6sq ~ e   uq ~ h   sq ~ jt tipoLiberacaoEnum.descricaosq ~ jt !=nullq ~Øppppq ~ 9  wîppppppq ~ Wpq ~ Ypppppppppsq ~ =psq ~ A  wîppppq ~"q ~"q ~psq ~ H  wîppppq ~"q ~"psq ~ B  wîppppq ~"q ~"psq ~ K  wîppppq ~"q ~"psq ~ M  wîppppq ~"q ~"ppppppppppppppppq ~ Õ  wî        ppq ~ csq ~ e   uq ~ h   sq ~ jt faltaJustificart java.lang.Integerppppppq ~ pppsq ~ Q  wî           $  ó    pq ~ q ~Îpt 	textFieldppppq ~ 6sq ~ e   uq ~ h   sq ~ jt tipoLiberacaoEnum.descricaosq ~ jt 
!=null && sq ~ jt tipoLiberacaoEnum.codigosq ~ jt ==4q ~Øppppq ~ 9  wîppppppq ~ Wpq ~ Ypppppppppsq ~ =psq ~ A  wîppppq ~9q ~9q ~-psq ~ H  wîppppq ~9q ~9psq ~ B  wîppppq ~9q ~9psq ~ K  wîppppq ~9q ~9psq ~ M  wîppppq ~9q ~9ppppppppppppppppq ~ Õ  wî        ppq ~ csq ~ e   uq ~ h   sq ~ jt qtdBVst java.lang.Integerppppppq ~ pppsq ~ "  wî           /  Ï    pq ~ q ~Îpt staticText-4ppppq ~ 6sq ~ e    uq ~ h   sq ~ jt tipoLiberacaoEnum.descricaosq ~ jt 
!=null && sq ~ jt tipoLiberacaoEnum.codigosq ~ jt ==4q ~Øppppq ~ 9  wîppppppq ~ Wpq ~ Yq ~ <ppppppppsq ~ =psq ~ A  wîppppq ~Pq ~Pq ~Dpsq ~ H  wîppppq ~Pq ~Ppsq ~ B  wîppppq ~Pq ~Ppsq ~ K  wîppppq ~Pq ~Ppsq ~ M  wîppppq ~Pq ~Ppppppt Helvetica-Boldppppppppppq ~ Õt 	Total BV:xp  wî   ppq ~ pppt javapsr .net.sf.jasperreports.engine.base.JRBaseDataset      'Ø I PSEUDO_SERIAL_VERSION_UIDZ isMainB whenResourceMissingType[ fieldst &[Lnet/sf/jasperreports/engine/JRField;L filterExpressionq ~ [ groupst &[Lnet/sf/jasperreports/engine/JRGroup;L nameq ~ [ 
parameterst *[Lnet/sf/jasperreports/engine/JRParameter;L 
propertiesMapq ~ 1L queryt %Lnet/sf/jasperreports/engine/JRQuery;L resourceBundleq ~ L scriptletClassq ~ [ 
scriptletst *[Lnet/sf/jasperreports/engine/JRScriptlet;[ 
sortFieldst *[Lnet/sf/jasperreports/engine/JRSortField;[ 	variablest )[Lnet/sf/jasperreports/engine/JRVariable;L whenResourceMissingTypeValuet >Lnet/sf/jasperreports/engine/type/WhenResourceMissingTypeEnum;xp  wî ur &[Lnet.sf.jasperreports.engine.JRField;<ßÇN*òp  xp   sr ,net.sf.jasperreports.engine.base.JRBaseField      'Ø L descriptionq ~ L nameq ~ L 
propertiesMapq ~ 1L valueClassNameq ~ L valueClassRealNameq ~ xppt tipoLiberacaoEnum.descricaosr +net.sf.jasperreports.engine.JRPropertiesMap      'Ø L baseq ~ 1L propertiesListq ~ L 
propertiesMapt Ljava/util/Map;xppppt java.lang.Stringpsq ~ept percentualAcessossq ~hpppt java.lang.Doublepsq ~ept totalAcessosq ~hpppt java.lang.Integerpsq ~ept 
jaJustificadosq ~hpppt java.lang.Integerpsq ~ept faltaJustificarsq ~hpppt java.lang.Integerpsq ~ept tipoLiberacaoEnum.codigosq ~hpppt java.lang.Integerpppt (FechamentoControleAcessoDWRel_fechamentour *[Lnet.sf.jasperreports.engine.JRParameter;" *Ã`!  xp   sr 0net.sf.jasperreports.engine.base.JRBaseParameter      'Ø 	Z isForPromptingZ isSystemDefinedL defaultValueExpressionq ~ L descriptionq ~ L nameq ~ L nestedTypeNameq ~ L 
propertiesMapq ~ 1L valueClassNameq ~ L valueClassRealNameq ~ xpppt REPORT_PARAMETERS_MAPpsq ~hpppt 
java.util.Mappsq ~ppt 
JASPER_REPORTpsq ~hpppt (net.sf.jasperreports.engine.JasperReportpsq ~ppt REPORT_CONNECTIONpsq ~hpppt java.sql.Connectionpsq ~ppt REPORT_MAX_COUNTpsq ~hpppt java.lang.Integerpsq ~ppt REPORT_DATA_SOURCEpsq ~hpppt (net.sf.jasperreports.engine.JRDataSourcepsq ~ppt REPORT_SCRIPTLETpsq ~hpppt /net.sf.jasperreports.engine.JRAbstractScriptletpsq ~ppt 
REPORT_LOCALEpsq ~hpppt java.util.Localepsq ~ppt REPORT_RESOURCE_BUNDLEpsq ~hpppt java.util.ResourceBundlepsq ~ppt REPORT_TIME_ZONEpsq ~hpppt java.util.TimeZonepsq ~ppt REPORT_FORMAT_FACTORYpsq ~hpppt .net.sf.jasperreports.engine.util.FormatFactorypsq ~ppt REPORT_CLASS_LOADERpsq ~hpppt java.lang.ClassLoaderpsq ~ppt REPORT_URL_HANDLER_FACTORYpsq ~hpppt  java.net.URLStreamHandlerFactorypsq ~ppt REPORT_FILE_RESOLVERpsq ~hpppt -net.sf.jasperreports.engine.util.FileResolverpsq ~ppt REPORT_TEMPLATESpsq ~hpppt java.util.Collectionpsq ~ppt REPORT_VIRTUALIZERpsq ~hpppt )net.sf.jasperreports.engine.JRVirtualizerpsq ~ppt IS_IGNORE_PAGINATIONpsq ~hpppq ~Øpsq ~  ppt percentualLiberacaopsq ~hpppt java.lang.Doublepsq ~ ppt totalClipsq ~hpppt java.lang.Integerpsq ~ ppt 
percentualClipsq ~hpppt java.lang.Doublepsq ~ ppt 
percentualColpsq ~hpppt java.lang.Doublepsq ~ ppt totalColpsq ~hpppt java.lang.Integerpsq ~  ppt percentualCliColpsq ~hpppt java.lang.Doublepsq ~  ppt totalAcessospsq ~hpppt java.lang.Numberpsq ~ ppt totalAcessoLibpsq ~hpppt java.lang.Integerpsq ~ ppt totalJustificadoLibpsq ~hpppt java.lang.Integerpsq ~ ppt totalFaltaJustificarLibpsq ~hpppt java.lang.Integerpsq ~ ppt qtdBVspsq ~hpppt java.lang.Integerpsq ~hpsq ~    w   t ireport.zoomt 	ireport.xt 	ireport.yxsr java.util.HashMapÚÁÃ`Ñ F 
loadFactorI 	thresholdxp?@     w      q ~ñt 1.9487171000000043q ~òt 60q ~ót 43xpppppur )[Lnet.sf.jasperreports.engine.JRVariable;bæ|,·D  xp   	sr /net.sf.jasperreports.engine.base.JRBaseVariable      'Ø I PSEUDO_SERIAL_VERSION_UIDB calculationB 
incrementTypeZ isSystemDefinedB 	resetTypeL calculationValuet 2Lnet/sf/jasperreports/engine/type/CalculationEnum;L 
expressionq ~ L incrementGroupq ~ 0L incrementTypeValuet 4Lnet/sf/jasperreports/engine/type/IncrementTypeEnum;L incrementerFactoryClassNameq ~ L incrementerFactoryClassRealNameq ~ L initialValueExpressionq ~ L nameq ~ L 
resetGroupq ~ 0L resetTypeValuet 0Lnet/sf/jasperreports/engine/type/ResetTypeEnum;L valueClassNameq ~ L valueClassRealNameq ~ xp  wî   ~r 0net.sf.jasperreports.engine.type.CalculationEnum          xq ~ t SYSTEMpp~r 2net.sf.jasperreports.engine.type.IncrementTypeEnum          xq ~ t NONEppsq ~ e    uq ~ h   sq ~ jt new java.lang.Integer(1)q ~pt PAGE_NUMBERp~r .net.sf.jasperreports.engine.type.ResetTypeEnum          xq ~ t REPORTq ~psq ~û  wî   q ~ppq ~ppsq ~ e   uq ~ h   sq ~ jt new java.lang.Integer(1)q ~pt 
COLUMN_NUMBERp~q ~t PAGEq ~psq ~û  wî   ~q ~ t COUNTsq ~ e   uq ~ h   sq ~ jt new java.lang.Integer(1)q ~ppq ~ppsq ~ e   uq ~ h   sq ~ jt new java.lang.Integer(0)q ~pt REPORT_COUNTpq ~q ~psq ~û  wî   q ~sq ~ e   uq ~ h   sq ~ jt new java.lang.Integer(1)q ~ppq ~ppsq ~ e   uq ~ h   sq ~ jt new java.lang.Integer(0)q ~pt 
PAGE_COUNTpq ~q ~psq ~û  wî   q ~sq ~ e   uq ~ h   sq ~ jt new java.lang.Integer(1)q ~ppq ~ppsq ~ e   uq ~ h   sq ~ jt new java.lang.Integer(0)q ~pt COLUMN_COUNTp~q ~t COLUMNq ~psq ~û  wî    ~q ~ t SUMsq ~ e   uq ~ h   sq ~ jt totalAcessosq ~ jt /2t java.lang.Integerppq ~pppt totalAcessospq ~q ~Apsq ~û  wî    q ~9sq ~ e   	uq ~ h   sq ~ jt 
jaJustificadosq ~ jt /2t java.lang.Integerppq ~pppt totalJaJustificadopq ~q ~Jpsq ~û  wî    q ~9sq ~ e   
uq ~ h   sq ~ jt faltaJustificarsq ~ jt /2t java.lang.Integerppq ~pppt totalFaltaJustificarpq ~q ~Spsq ~û  wî    q ~9sq ~ e   uq ~ h   sq ~ jt percentualAcessossq ~ jt /2t java.lang.Doubleppq ~pppt totalPercentualLiberacaopq ~q ~\p~r <net.sf.jasperreports.engine.type.WhenResourceMissingTypeEnum          xq ~ t NULLq ~p~r 0net.sf.jasperreports.engine.type.OrientationEnum          xq ~ t PORTRAITpp~r /net.sf.jasperreports.engine.type.PrintOrderEnum          xq ~ t VERTICALpppp~r 3net.sf.jasperreports.engine.type.WhenNoDataTypeEnum          xq ~ t NO_PAGESsr 6net.sf.jasperreports.engine.design.JRReportCompileData      'Ø L crosstabCompileDataq ~iL datasetCompileDataq ~iL mainDatasetCompileDataq ~ xpsq ~ô?@     w       xsq ~ô?@     w       xur [B¬óøTà  xp  $¿Êþº¾   .) =FechamentoControleAcessoDWRel_fechamento_1579529087806_569679  ,net/sf/jasperreports/engine/fill/JREvaluator  parameter_JASPER_REPORT 2Lnet/sf/jasperreports/engine/fill/JRFillParameter; parameter_REPORT_TIME_ZONE parameter_totalCol parameter_REPORT_FILE_RESOLVER parameter_qtdBVs parameter_REPORT_PARAMETERS_MAP parameter_totalJustificadoLib parameter_REPORT_CLASS_LOADER $parameter_REPORT_URL_HANDLER_FACTORY parameter_REPORT_DATA_SOURCE parameter_IS_IGNORE_PAGINATION parameter_REPORT_MAX_COUNT parameter_REPORT_TEMPLATES parameter_totalAcessoLib parameter_percentualCli parameter_REPORT_LOCALE parameter_REPORT_VIRTUALIZER parameter_REPORT_SCRIPTLET parameter_REPORT_CONNECTION parameter_totalCli parameter_percentualCol parameter_percentualCliCol parameter_REPORT_FORMAT_FACTORY parameter_totalAcessos parameter_percentualLiberacao  parameter_REPORT_RESOURCE_BUNDLE !parameter_totalFaltaJustificarLib field_tipoLiberacaoEnum46codigo .Lnet/sf/jasperreports/engine/fill/JRFillField; field_percentualAcessos field_faltaJustificar field_totalAcesso field_jaJustificado "field_tipoLiberacaoEnum46descricao variable_PAGE_NUMBER 1Lnet/sf/jasperreports/engine/fill/JRFillVariable; variable_COLUMN_NUMBER variable_REPORT_COUNT variable_PAGE_COUNT variable_COLUMN_COUNT variable_totalAcessos variable_totalJaJustificado variable_totalFaltaJustificar !variable_totalPercentualLiberacao <init> ()V Code 2 3
  5  	  7  	  9  	  ; 	 	  = 
 	  ?  	  A  	  C 
 	  E  	  G  	  I  	  K  	  M  	  O  	  Q  	  S  	  U  	  W  	  Y  	  [  	  ]  	  _  	  a  	  c  	  e  	  g  	  i   	  k ! "	  m # "	  o $ "	  q % "	  s & "	  u ' "	  w ( )	  y * )	  { + )	  } , )	   - )	   . )	   / )	   0 )	   1 )	   LineNumberTable customizedInit 0(Ljava/util/Map;Ljava/util/Map;Ljava/util/Map;)V 
initParams (Ljava/util/Map;)V  
   
initFields  
   initVars  
   
JASPER_REPORT  
java/util/Map  get &(Ljava/lang/Object;)Ljava/lang/Object;     0net/sf/jasperreports/engine/fill/JRFillParameter   REPORT_TIME_ZONE ¢ totalCol ¤ REPORT_FILE_RESOLVER ¦ qtdBVs ¨ REPORT_PARAMETERS_MAP ª totalJustificadoLib ¬ REPORT_CLASS_LOADER ® REPORT_URL_HANDLER_FACTORY ° REPORT_DATA_SOURCE ² IS_IGNORE_PAGINATION ´ REPORT_MAX_COUNT ¶ REPORT_TEMPLATES ¸ totalAcessoLib º 
percentualCli ¼ 
REPORT_LOCALE ¾ REPORT_VIRTUALIZER À REPORT_SCRIPTLET Â REPORT_CONNECTION Ä totalCli Æ 
percentualCol È percentualCliCol Ê REPORT_FORMAT_FACTORY Ì totalAcessos Î percentualLiberacao Ð REPORT_RESOURCE_BUNDLE Ò totalFaltaJustificarLib Ô tipoLiberacaoEnum.codigo Ö ,net/sf/jasperreports/engine/fill/JRFillField Ø percentualAcessos Ú faltaJustificar Ü totalAcesso Þ 
jaJustificado à tipoLiberacaoEnum.descricao â PAGE_NUMBER ä /net/sf/jasperreports/engine/fill/JRFillVariable æ 
COLUMN_NUMBER è REPORT_COUNT ê 
PAGE_COUNT ì COLUMN_COUNT î totalJaJustificado ð totalFaltaJustificar ò totalPercentualLiberacao ô evaluate (I)Ljava/lang/Object; 
Exceptions java/lang/Throwable ù java/lang/Integer û (I)V 2 ý
 ü þ getValue ()Ljava/lang/Object; 
 Ù intValue ()I
 ü valueOf (I)Ljava/lang/Integer;	
 ü
 java/lang/Double doubleValue ()D

@        (D)Ljava/lang/Double;


 ¡@Y       java/lang/Number java/lang/String java/lang/Boolean (Z)Ljava/lang/Boolean; 
! evaluateOld getOldValue$
 Ù% evaluateEstimated 
SourceFile !     *                 	     
               
                                                                                                     ! "    # "    $ "    % "    & "    ' "    ( )    * )    + )    , )    - )    . )    / )    0 )    1 )     2 3  4       ×*· 6*µ 8*µ :*µ <*µ >*µ @*µ B*µ D*µ F*µ H*µ J*µ L*µ N*µ P*µ R*µ T*µ V*µ X*µ Z*µ \*µ ^*µ `*µ b*µ d*µ f*µ h*µ j*µ l*µ n*µ p*µ r*µ t*µ v*µ x*µ z*µ |*µ ~*µ *µ *µ *µ *µ *µ ±       ² ,      	          "  '   , ! 1 " 6 # ; $ @ % E & J ' O ( T ) Y * ^ + c , h - m . r / w 0 | 1  2  3  4  5  6  7  8 ¤ 9 © : ® ; ³ < ¸ = ½ > Â ? Ç @ Ì A Ñ B Ö      4   4     *+· *,· *-· ±           N  O 
 P  Q     4  k    ç*+¹  À ¡À ¡µ 8*+£¹  À ¡À ¡µ :*+¥¹  À ¡À ¡µ <*+§¹  À ¡À ¡µ >*+©¹  À ¡À ¡µ @*+«¹  À ¡À ¡µ B*+­¹  À ¡À ¡µ D*+¯¹  À ¡À ¡µ F*+±¹  À ¡À ¡µ H*+³¹  À ¡À ¡µ J*+µ¹  À ¡À ¡µ L*+·¹  À ¡À ¡µ N*+¹¹  À ¡À ¡µ P*+»¹  À ¡À ¡µ R*+½¹  À ¡À ¡µ T*+¿¹  À ¡À ¡µ V*+Á¹  À ¡À ¡µ X*+Ã¹  À ¡À ¡µ Z*+Å¹  À ¡À ¡µ \*+Ç¹  À ¡À ¡µ ^*+É¹  À ¡À ¡µ `*+Ë¹  À ¡À ¡µ b*+Í¹  À ¡À ¡µ d*+Ï¹  À ¡À ¡µ f*+Ñ¹  À ¡À ¡µ h*+Ó¹  À ¡À ¡µ j*+Õ¹  À ¡À ¡µ l±       r    Y  Z $ [ 6 \ H ] Z ^ l _ ~ `  a ¢ b ´ c Æ d Ø e ê f ü g h  i2 jD kV lh mz n o p° qÂ rÔ sæ t     4        m*+×¹  À ÙÀ Ùµ n*+Û¹  À ÙÀ Ùµ p*+Ý¹  À ÙÀ Ùµ r*+ß¹  À ÙÀ Ùµ t*+á¹  À ÙÀ Ùµ v*+ã¹  À ÙÀ Ùµ x±           |  } $ ~ 6  H  Z  l      4   ß     £*+å¹  À çÀ çµ z*+é¹  À çÀ çµ |*+ë¹  À çÀ çµ ~*+í¹  À çÀ çµ *+ï¹  À çÀ çµ *+Ï¹  À çÀ çµ *+ñ¹  À çÀ çµ *+ó¹  À çÀ çµ *+õ¹  À çÀ çµ ±       * 
      $  6  H  Z  l  ~    ¢   ö ÷  ø     ú 4  Ë    Mª         $   ¡   ­   ¹   Å   Ñ   Ý   é   õ      -  C  [  s      §  ¿  ×  ù    -  F  T  m      ¬  º  Ó  á      C  Q  _  m» üY· ÿM§Ø» üY· ÿM§Ì» üY· ÿM§À» üY· ÿM§´» üY· ÿM§¨» üY· ÿM§» üY· ÿM§» üY· ÿM§*´ t¶À ü¶l¸M§n*´ v¶À ü¶l¸M§X*´ r¶À ü¶l¸M§B*´ p¶À
¶o¸M§**´ h¶À
¶o¸M§*´ ^¶À üM§*´ <¶À üM§ö*´ b¶À
¶o¸M§Þ*´ `¶À
¶o¸M§Æ*´ T¶À
¶o¸M§®*´ <¶À ü¶*´ ^¶À ü¶`¸M§*´ T¶À
¶*´ `¶À
¶co¸M§f*´ f¶ÀM§X*´ x¶ÀÆ § ¸"M§?*´ v¶À üM§1*´ x¶ÀÆ § ¸"M§*´ p¶À
¶o¸M§ *´ x¶ÀM§ ò*´ x¶ÀÆ § ¸"M§ Ù*´ t¶À üM§ Ë*´ x¶ÀÆ § ¸"M§ ²*´ r¶À üM§ ¤*´ x¶ÀÆ *´ n¶À ü¶  § ¸"M§ z*´ @¶À üM§ l*´ x¶ÀÆ *´ n¶À ü¶  § ¸"M§ B*´ R¶À üM§ 4*´ D¶À üM§ &*´ l¶À üM§ *´ h¶À
¶o¸M,°      2 L      ¤ ¡ ­ ¢ ° ¦ ¹ § ¼ « Å ¬ È ° Ñ ± Ô µ Ý ¶ à º é » ì ¿ õ À ø Ä Å É Ê Î- Ï0 ÓC ÔF Ø[ Ù^ Ýs Þv â ã ç è ì§ íª ñ¿ òÂ ö× ÷Ú ûù üü "-0
FITWmp#¬$¯(º)½-Ó.Ö2á3ä78<=ACBFFQGTK_LbPmQpU] # ÷  ø     ú 4  Ë    Mª         $   ¡   ­   ¹   Å   Ñ   Ý   é   õ      -  C  [  s      §  ¿  ×  ù    -  F  T  m      ¬  º  Ó  á      C  Q  _  m» üY· ÿM§Ø» üY· ÿM§Ì» üY· ÿM§À» üY· ÿM§´» üY· ÿM§¨» üY· ÿM§» üY· ÿM§» üY· ÿM§*´ t¶&À ü¶l¸M§n*´ v¶&À ü¶l¸M§X*´ r¶&À ü¶l¸M§B*´ p¶&À
¶o¸M§**´ h¶À
¶o¸M§*´ ^¶À üM§*´ <¶À üM§ö*´ b¶À
¶o¸M§Þ*´ `¶À
¶o¸M§Æ*´ T¶À
¶o¸M§®*´ <¶À ü¶*´ ^¶À ü¶`¸M§*´ T¶À
¶*´ `¶À
¶co¸M§f*´ f¶ÀM§X*´ x¶&ÀÆ § ¸"M§?*´ v¶&À üM§1*´ x¶&ÀÆ § ¸"M§*´ p¶&À
¶o¸M§ *´ x¶&ÀM§ ò*´ x¶&ÀÆ § ¸"M§ Ù*´ t¶&À üM§ Ë*´ x¶&ÀÆ § ¸"M§ ²*´ r¶&À üM§ ¤*´ x¶&ÀÆ *´ n¶&À ü¶  § ¸"M§ z*´ @¶À üM§ l*´ x¶&ÀÆ *´ n¶&À ü¶  § ¸"M§ B*´ R¶À üM§ 4*´ D¶À üM§ &*´ l¶À üM§ *´ h¶À
¶o¸M,°      2 L  f h ¤l ­m °q ¹r ¼v Åw È{ Ñ| Ô Ý à é ì õ ø-0CF£[¤^¨s©v­®²³·§¸ª¼¿½ÂÁ×ÂÚÆùÇüËÌ"Ð-Ñ0ÕFÖIÚTÛWßmàpäåéêî¬ï¯óºô½øÓùÖýáþäC
FQT_bmp ( ' ÷  ø     ú 4  Ë    Mª         $   ¡   ­   ¹   Å   Ñ   Ý   é   õ      -  C  [  s      §  ¿  ×  ù    -  F  T  m      ¬  º  Ó  á      C  Q  _  m» üY· ÿM§Ø» üY· ÿM§Ì» üY· ÿM§À» üY· ÿM§´» üY· ÿM§¨» üY· ÿM§» üY· ÿM§» üY· ÿM§*´ t¶À ü¶l¸M§n*´ v¶À ü¶l¸M§X*´ r¶À ü¶l¸M§B*´ p¶À
¶o¸M§**´ h¶À
¶o¸M§*´ ^¶À üM§*´ <¶À üM§ö*´ b¶À
¶o¸M§Þ*´ `¶À
¶o¸M§Æ*´ T¶À
¶o¸M§®*´ <¶À ü¶*´ ^¶À ü¶`¸M§*´ T¶À
¶*´ `¶À
¶co¸M§f*´ f¶ÀM§X*´ x¶ÀÆ § ¸"M§?*´ v¶À üM§1*´ x¶ÀÆ § ¸"M§*´ p¶À
¶o¸M§ *´ x¶ÀM§ ò*´ x¶ÀÆ § ¸"M§ Ù*´ t¶À üM§ Ë*´ x¶ÀÆ § ¸"M§ ²*´ r¶À üM§ ¤*´ x¶ÀÆ *´ n¶À ü¶  § ¸"M§ z*´ @¶À üM§ l*´ x¶ÀÆ *´ n¶À ü¶  § ¸"M§ B*´ R¶À üM§ 4*´ D¶À üM§ &*´ l¶À üM§ *´ h¶À
¶o¸M,°      2 L  1 3 ¤7 ­8 °< ¹= ¼A ÅB ÈF ÑG ÔK ÝL àP éQ ìU õV øZ[_`d-e0iCjFn[o^sstvxy}~§ª¿Â×Úùü"-0 F¡I¥T¦Wªm«p¯°´µ¹¬º¯¾º¿½ÃÓÄÖÈáÉäÍÎÒÓ×CØFÜQÝTá_âbæmçpëó (    t _1579529087806_569679t 2net.sf.jasperreports.engine.design.JRJavacCompiler
<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="GestaoTransacoes_subValoresParcelas" pageWidth="425" pageHeight="141" columnWidth="385" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20">
	<property name="ireport.zoom" value="1.9487171000000043"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<field name="situacao_Apresentar" class="java.lang.String"/>
	<field name="quantidade" class="java.lang.Integer"/>
	<field name="valor_Apresentar" class="java.lang.String"/>
	<field name="valor" class="java.lang.Double"/>
	<variable name="soma" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[$F{valor}]]></variableExpression>
	</variable>
	<background>
		<band splitType="Stretch"/>
	</background>
	<columnHeader>
		<band height="14">
			<staticText>
				<reportElement key="staticText-1" positionType="Float" x="0" y="0" width="129" height="14" isPrintInFirstWholeBand="true"/>
				<textElement>
					<font size="7" isBold="true" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Parcelas/Valor]]></text>
			</staticText>
			<line>
				<reportElement x="0" y="13" width="119" height="1"/>
			</line>
		</band>
	</columnHeader>
	<detail>
		<band height="17" splitType="Stretch">
			<textField>
				<reportElement x="0" y="0" width="48" height="17" isPrintInFirstWholeBand="true"/>
				<textElement>
					<font size="7" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{situacao_Apresentar}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="72" y="0" width="71" height="17" isPrintInFirstWholeBand="true"/>
				<textElement>
					<font size="7" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{valor_Apresentar}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="48" y="1" width="24" height="16"/>
				<textElement>
					<font size="7"/>
				</textElement>
				<textFieldExpression class="java.lang.Integer"><![CDATA[$F{quantidade}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<columnFooter>
		<band height="20">
			<textField pattern="¤ #,##0.00">
				<reportElement x="72" y="1" width="71" height="19" isPrintInFirstWholeBand="true"/>
				<textElement>
					<font size="7" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression class="java.lang.Double"><![CDATA[$V{soma}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="0" y="1" width="48" height="19"/>
				<textElement>
					<font size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[Total]]></text>
			</staticText>
			<line>
				<reportElement x="0" y="0" width="119" height="1"/>
			</line>
		</band>
	</columnFooter>
</jasperReport>

<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="ListaAcessoRelCliente" pageWidth="878" pageHeight="680" orientation="Landscape" columnWidth="838" leftMargin="20" rightMargin="20" topMargin="10" bottomMargin="10" whenResourceMissingType="Empty">
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.encoding" value="ISO-8859-1"/>
	<property name="ireport.zoom" value="1.5"/>
	<property name="ireport.x" value="215"/>
	<property name="ireport.y" value="0"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="java.util.*"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<subDataset name="Teste"/>
	<parameter name="logoPadraoRelatorio" class="java.io.InputStream" isForPrompting="false"/>
	<parameter name="tituloRelatorio" class="java.lang.String" isForPrompting="false"/>
	<parameter name="versaoSoftware" class="java.lang.String" isForPrompting="false"/>
	<parameter name="usuario" class="java.lang.String" isForPrompting="false"/>
	<parameter name="filtros" class="java.lang.String" isForPrompting="false"/>
	<parameter name="SUBREPORT_DIR" class="java.lang.String">
		<defaultValueExpression><![CDATA["D:\\PactoJ\\Desenvolvimento\\Sistemas\\ZillyonWeb\\tronco-novo\\src\\java\\relatorio\\designRelatorio\\outros\\"]]></defaultValueExpression>
	</parameter>
	<parameter name="nomeEmpresa" class="java.lang.String"/>
	<parameter name="enderecoEmpresa" class="java.lang.String"/>
	<parameter name="cidadeEmpresa" class="java.lang.String"/>
	<parameter name="dataIni" class="java.lang.String" isForPrompting="false"/>
	<parameter name="dataFim" class="java.lang.String" isForPrompting="false"/>
	<parameter name="SUBREPORT_DIR1" class="java.lang.String"/>
	<field name="cliente.matricula" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="intervaloDataHoras" class="java.lang.String"/>
	<field name="cliente.pessoa.nome" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="dataHoraEntrada" class="java.util.Date">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="dataHoraSaida" class="java.util.Date">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="sentido" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="usuario.primeiroNomeConcatenado" class="java.lang.String"/>
	<field name="situacao.descricao" class="java.lang.String"/>
	<field name="situacao.id" class="java.lang.String"/>
	<field name="meioIdentificacaoEntrada.descricao" class="java.lang.String"/>
	<field name="localAcesso.empresa.nome" class="java.lang.String"/>
	<field name="coletor.descricao" class="java.lang.String"/>
	<field name="cliente.pessoa.email" class="java.lang.String"/>
	<variable name="totalclientes" class="java.lang.Integer" calculation="Count">
		<variableExpression><![CDATA[new Integer($F{cliente.matricula})]]></variableExpression>
	</variable>
	<variable name="totalalunos" class="java.lang.Integer" calculation="DistinctCount">
		<variableExpression><![CDATA[new Integer($F{cliente.matricula})]]></variableExpression>
	</variable>
	<group name="nomeCliente">
		<groupExpression><![CDATA[$F{cliente.pessoa.nome}]]></groupExpression>
		<groupHeader>
			<band height="23">
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement key="textField-224" positionType="Float" x="2" y="2" width="830" height="20"/>
					<textElement>
						<font size="11" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$F{cliente.pessoa.nome} + ($F{cliente.pessoa.email}.trim().equals("") || $F{cliente.pessoa.email}==null ? "" :  (" - " + $F{cliente.pessoa.email}))]]></textFieldExpression>
				</textField>
				<line>
					<reportElement x="0" y="1" width="834" height="1"/>
				</line>
			</band>
		</groupHeader>
		<groupFooter>
			<band height="19">
				<staticText>
					<reportElement x="613" y="0" width="90" height="15"/>
					<textElement textAlignment="Right">
						<font pdfFontName="Helvetica-Bold"/>
					</textElement>
					<text><![CDATA[Total de Acessos:]]></text>
				</staticText>
				<textField isStretchWithOverflow="true">
					<reportElement x="706" y="0" width="130" height="15"/>
					<textElement/>
					<textFieldExpression class="java.lang.Integer"><![CDATA[$V{nomeCliente_COUNT}]]></textFieldExpression>
				</textField>
			</band>
		</groupFooter>
	</group>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band splitType="Stretch"/>
	</title>
	<pageHeader>
		<band height="69" splitType="Stretch">
			<textField evaluationTime="Report" isBlankWhenNull="false">
				<reportElement key="textField-212" x="803" y="51" width="31" height="17"/>
				<box bottomPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement>
					<font fontName="Arial" size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[" " + $V{PAGE_NUMBER} + ""]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement key="staticText-15" mode="Opaque" x="721" y="35" width="111" height="12"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Top" lineSpacing="Single">
					<font fontName="Microsoft Sans Serif" size="9" isBold="true" isItalic="true" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica-BoldOblique"/>
				</textElement>
				<text><![CDATA[(0xx62) 3251-5820]]></text>
			</staticText>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-211" x="723" y="51" width="75" height="17"/>
				<box bottomPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right">
					<font fontName="Arial" size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA["Página: " + $V{PAGE_NUMBER} + " de "]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement key="staticText-13" x="320" y="31" width="189" height="27"/>
				<textElement textAlignment="Center">
					<font size="20" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Lista de Acessos]]></text>
			</staticText>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-209" x="86" y="31" width="113" height="14"/>
				<textElement>
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{enderecoEmpresa}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-208" x="86" y="15" width="113" height="14"/>
				<textElement>
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{nomeEmpresa}]]></textFieldExpression>
			</textField>
			<image isUsingCache="true" onErrorType="Blank" evaluationTime="Page">
				<reportElement key="image-1" x="0" y="16" width="82" height="46" isPrintWhenDetailOverflows="true"/>
				<box>
					<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<imageExpression class="java.io.InputStream"><![CDATA[$P{logoPadraoRelatorio}]]></imageExpression>
			</image>
			<staticText>
				<reportElement key="staticText-14" mode="Opaque" x="570" y="6" width="262" height="23"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Top" lineSpacing="Single">
					<font fontName="Microsoft Sans Serif" size="9" isBold="true" isItalic="true" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica-BoldOblique"/>
				</textElement>
				<text><![CDATA[Zillyon - Sistema Administrativo para Academias Desenvolvido por PACTO Soluções Tecnológicas Ltda.]]></text>
			</staticText>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-210" x="86" y="47" width="113" height="14"/>
				<textElement>
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{cidadeEmpresa}]]></textFieldExpression>
			</textField>
		</band>
	</pageHeader>
	<columnHeader>
		<band height="54" splitType="Stretch">
			<line>
				<reportElement key="line-1" x="0" y="2" width="838" height="1"/>
			</line>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-214" x="0" y="3" width="838" height="30"/>
				<box>
					<topPen lineWidth="0.5" lineStyle="Solid"/>
					<leftPen lineWidth="0.5" lineStyle="Solid"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.5" lineStyle="Solid"/>
				</box>
				<textElement textAlignment="Center">
					<font fontName="Arial" size="10" isBold="true" isItalic="true" pdfFontName="Helvetica-BoldOblique"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{filtros}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement key="staticText-2" x="2" y="39" width="76" height="14"/>
				<textElement>
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Mat. Cliente]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-8" x="78" y="39" width="81" height="14"/>
				<textElement>
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Data Entrada]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-8" x="159" y="39" width="85" height="14"/>
				<textElement>
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Data Saída]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-8" x="244" y="39" width="43" height="14"/>
				<textElement>
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Tempo]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-8" x="287" y="39" width="45" height="14"/>
				<textElement>
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Sentido]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-8" x="761" y="39" width="78" height="14"/>
				<textElement>
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Usuário Lib.]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-8" x="332" y="39" width="62" height="14"/>
				<textElement>
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Bloqueio]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-8" x="396" y="39" width="365" height="14"/>
				<textElement>
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Coletor]]></text>
			</staticText>
		</band>
	</columnHeader>
	<detail>
		<band height="29" splitType="Stretch">
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement key="textField-223" stretchType="RelativeToTallestObject" x="244" y="1" width="40" height="14"/>
				<textElement textAlignment="Left" verticalAlignment="Middle"/>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{intervaloDataHoras}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement key="textField" stretchType="RelativeToTallestObject" x="284" y="1" width="23" height="14"/>
				<textElement textAlignment="Left" verticalAlignment="Middle"/>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{sentido}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement key="textField-224" stretchType="RelativeToTallestObject" x="159" y="1" width="85" height="14"/>
				<textElement textAlignment="Left" verticalAlignment="Middle"/>
				<textFieldExpression class="java.sql.Timestamp"><![CDATA[$F{dataHoraSaida}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement key="textField" stretchType="RelativeToTallestObject" x="761" y="1" width="76" height="14"/>
				<textElement textAlignment="Left" verticalAlignment="Middle"/>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{usuario.primeiroNomeConcatenado}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement key="textField-223" stretchType="RelativeToTallestObject" x="1" y="1" width="76" height="14"/>
				<textElement textAlignment="Left" verticalAlignment="Middle"/>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{cliente.matricula}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement key="textField" stretchType="RelativeToTallestObject" x="307" y="2" width="223" height="14">
					<printWhenExpression><![CDATA[!$F{situacao.id}.equals("RV_LIBACESSOAUTORIZADO")]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle"/>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{situacao.descricao}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement key="textField" stretchType="RelativeToTallestObject" x="93" y="15" width="739" height="14"/>
				<textElement textAlignment="Left" verticalAlignment="Top">
					<font size="9"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{meioIdentificacaoEntrada.descricao}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement key="textField-224" stretchType="RelativeToTallestObject" x="78" y="1" width="81" height="14"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font size="10" isBold="false"/>
				</textElement>
				<textFieldExpression class="java.sql.Timestamp"><![CDATA[$F{dataHoraEntrada}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement key="textField" stretchType="RelativeToTallestObject" x="530" y="2" width="231" height="14"/>
				<textElement textAlignment="Left" verticalAlignment="Middle"/>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{coletor.descricao}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement key="staticText-8" x="2" y="15" width="97" height="14"/>
				<textElement>
					<font size="9" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Meio Identificação:]]></text>
			</staticText>
		</band>
	</detail>
	<columnFooter>
		<band splitType="Stretch"/>
	</columnFooter>
	<pageFooter>
		<band splitType="Stretch"/>
	</pageFooter>
	<lastPageFooter>
		<band height="75" splitType="Stretch">
			<staticText>
				<reportElement x="390" y="20" width="123" height="20"/>
				<textElement>
					<font pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Total Geral de Acessos:]]></text>
			</staticText>
			<line>
				<reportElement key="line-4" x="1" y="-2" width="838" height="1"/>
			</line>
			<line>
				<reportElement key="line-5" x="1" y="49" width="837" height="1"/>
			</line>
			<line>
				<reportElement key="line-6" x="1" y="51" width="837" height="1"/>
			</line>
			<textField pattern="" isBlankWhenNull="false">
				<reportElement key="textField-207" x="1" y="55" width="837" height="19"/>
				<box>
					<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="8" isItalic="true" pdfFontName="Helvetica-Oblique"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[" "+" Usuário:" + $P{usuario}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="513" y="20" width="100" height="20"/>
				<textElement/>
				<textFieldExpression class="java.lang.Integer"><![CDATA[$V{totalclientes}]]></textFieldExpression>
			</textField>
			<textField pattern="dd/MM/yyyy HH.mm.ss" isBlankWhenNull="true">
				<reportElement key="dataRel-1" mode="Opaque" x="702" y="59" width="126" height="13" backcolor="#FFFFFF"/>
				<box bottomPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle" lineSpacing="Single">
					<font fontName="Arial" size="8" isBold="false" isItalic="true" isUnderline="false" pdfFontName="Helvetica-Oblique"/>
				</textElement>
				<textFieldExpression class="java.util.Date"><![CDATA[new Date()]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="736" y="20" width="100" height="20"/>
				<textElement/>
				<textFieldExpression class="java.lang.Integer"><![CDATA[$V{totalalunos}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="613" y="20" width="123" height="20"/>
				<textElement>
					<font pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Total Geral de Alunos:]]></text>
			</staticText>
		</band>
	</lastPageFooter>
	<summary>
		<band splitType="Stretch"/>
	</summary>
</jasperReport>

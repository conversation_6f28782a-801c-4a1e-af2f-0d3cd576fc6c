<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="DemonstrativoFinanceiroRelExcel" pageWidth="800" pageHeight="878" columnWidth="760" leftMargin="20" rightMargin="20" topMargin="2" bottomMargin="2" whenResourceMissingType="Empty">
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.encoding" value="ISO-8859-1"/>
	<property name="ireport.zoom" value="1.5"/>
	<property name="ireport.x" value="83"/>
	<property name="ireport.y" value="0"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="java.util.*"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<parameter name="tipoRelatorioDF" class="java.lang.Integer"/>
	<queryString>
		<![CDATA[]]>
	</queryString>
	<field name="descricaoLancamento" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="contrato" class="java.lang.Integer"/>
	<field name="nomePessoa" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="movProduto" class="java.lang.Integer">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="tipoFormaPagto.descricao" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="movPagamento" class="java.lang.Integer"/>
	<field name="valorLancamentoApresentarExcel" class="java.lang.String"/>
	<field name="tipoES.descricao" class="java.lang.String"/>
	<field name="valorLancamento" class="java.lang.Double"/>
	<variable name="tipoDescricao" class="java.lang.String">
		<variableExpression><![CDATA[( $F{valorLancamento}<0.0 ?  "Saída":"Entrada" )]]></variableExpression>
	</variable>
	<title>
		<band height="23">
			<staticText>
				<reportElement key="staticText-2" x="2" y="2" width="83" height="20"/>
				<textElement>
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Data Quitação]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-8" x="706" y="2" width="54" height="20"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Valor(R$)]]></text>
			</staticText>
		</band>
	</title>
	<detail>
		<band height="25">
			<textField isBlankWhenNull="true">
				<reportElement key="textField-224" x="2" y="3" width="83" height="20"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font size="10" isBold="false"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{dataQuitacao}]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement key="textField" positionType="Float" x="706" y="3" width="54" height="20"/>
				<textElement textAlignment="Center" verticalAlignment="Middle"/>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{valorLancamentoApresentarExcel}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
</jasperReport>

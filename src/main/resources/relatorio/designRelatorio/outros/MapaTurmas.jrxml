<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="MapaTurmas" pageWidth="842" pageHeight="595" orientation="Landscape" columnWidth="814" leftMargin="14" rightMargin="14" topMargin="14" bottomMargin="14">
	<property name="ireport.zoom" value="1.6105100000000014"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<parameter name="logoPadraoRelatorio" class="java.io.InputStream" isForPrompting="false"/>
	<parameter name="tituloRelatorio" class="java.lang.String" isForPrompting="false"/>
	<parameter name="versaoSoftware" class="java.lang.String" isForPrompting="false"/>
	<parameter name="usuario" class="java.lang.String" isForPrompting="false"/>
	<parameter name="filtros" class="java.lang.String" isForPrompting="false"/>
	<parameter name="SUBREPORT_DIR" class="java.lang.String">
		<defaultValueExpression><![CDATA["D:\\PactoJ\\Desenvolvimento\\Sistemas\\ZillyonWeb\\tronco-novo\\src\\java\\relatorio\\designRelatorio\\outros\\"]]></defaultValueExpression>
	</parameter>
	<parameter name="nomeEmpresa" class="java.lang.String"/>
	<parameter name="enderecoEmpresa" class="java.lang.String"/>
	<parameter name="cidadeEmpresa" class="java.lang.String"/>
	<parameter name="dataIni" class="java.lang.String" isForPrompting="false"/>
	<parameter name="dataFim" class="java.lang.String" isForPrompting="false"/>
	<parameter name="formatoRel" class="java.lang.String" isForPrompting="false"/>
	<parameter name="SUBREPORT_DIR1" class="java.lang.String"/>
	<parameter name="totalClientes" class="java.lang.String" isForPrompting="false"/>
	<parameter name="totalContratos" class="java.lang.String" isForPrompting="false"/>
	<parameter name="totalValor" class="java.lang.String" isForPrompting="false"/>
	<parameter name="totalCompetencia" class="java.lang.String" isForPrompting="false"/>
	<parameter name="listaTotais" class="java.lang.Object" isForPrompting="false"/>
	<parameter name="colMatricula" class="java.lang.Boolean"/>
	<parameter name="colNome" class="java.lang.Boolean"/>
	<parameter name="colSituacao" class="java.lang.Boolean"/>
	<parameter name="colVinculo" class="java.lang.Boolean"/>
	<parameter name="colPlano" class="java.lang.Boolean"/>
	<parameter name="colContrato" class="java.lang.Boolean"/>
	<parameter name="colModalidade" class="java.lang.Boolean"/>
	<parameter name="colValorModalidade" class="java.lang.Boolean"/>
	<parameter name="colDuracao" class="java.lang.Boolean"/>
	<parameter name="colDataLancamento" class="java.lang.Boolean"/>
	<parameter name="colInicio" class="java.lang.Boolean"/>
	<parameter name="colVence" class="java.lang.Boolean"/>
	<parameter name="colValor" class="java.lang.Boolean"/>
	<parameter name="colHorario" class="java.lang.Boolean"/>
	<parameter name="colFaturamento" class="java.lang.Boolean"/>
	<field name="horario" class="java.lang.String"/>
	<field name="mapa.segunda.itensJr" class="java.lang.Object"/>
	<field name="mapa.terca.itensJr" class="java.lang.Object"/>
	<field name="mapa.quarta.itensJr" class="java.lang.Object"/>
	<field name="mapa.quinta.itensJr" class="java.lang.Object"/>
	<field name="mapa.sexta.itensJr" class="java.lang.Object"/>
	<field name="mapa.sabado.itensJr" class="java.lang.Object"/>
	<field name="mapa.domingo.itensJr" class="java.lang.Object"/>
	<group name="Coluna" isReprintHeaderOnEachPage="true" minHeightToStartNewPage="154" keepTogether="true">
		<groupExpression><![CDATA[$V{PAGE_NUMBER}]]></groupExpression>
		<groupHeader>
			<band height="31">
				<printWhenExpression><![CDATA["EXCEL".equals($P{formatoRel}) == true]]></printWhenExpression>
				<rectangle>
					<reportElement x="0" y="15" width="814" height="16" forecolor="#FFFFFF" backcolor="#CCCCCC"/>
				</rectangle>
				<staticText>
					<reportElement x="1" y="15" width="110" height="16" backcolor="#CCCCCC"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<text><![CDATA[Segunda]]></text>
				</staticText>
				<staticText>
					<reportElement x="116" y="15" width="110" height="16"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<text><![CDATA[Terça]]></text>
				</staticText>
				<staticText>
					<reportElement x="236" y="15" width="110" height="16"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<text><![CDATA[Quarta]]></text>
				</staticText>
				<staticText>
					<reportElement x="352" y="15" width="110" height="16"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<text><![CDATA[Quinta]]></text>
				</staticText>
				<staticText>
					<reportElement x="469" y="15" width="110" height="16"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<text><![CDATA[Sexta]]></text>
				</staticText>
				<staticText>
					<reportElement x="586" y="15" width="110" height="16"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<text><![CDATA[Sábado]]></text>
				</staticText>
				<staticText>
					<reportElement x="704" y="15" width="110" height="16"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<text><![CDATA[Domingo]]></text>
				</staticText>
			</band>
			<band height="31">
				<printWhenExpression><![CDATA["EXCEL".equals($P{formatoRel}) == false]]></printWhenExpression>
				<rectangle>
					<reportElement x="0" y="15" width="814" height="16" forecolor="#FFFFFF" backcolor="#CCCCCC"/>
				</rectangle>
				<staticText>
					<reportElement x="1" y="15" width="110" height="16" backcolor="#CCCCCC"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<text><![CDATA[Segunda]]></text>
				</staticText>
				<staticText>
					<reportElement x="116" y="15" width="110" height="16"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<text><![CDATA[Terça]]></text>
				</staticText>
				<staticText>
					<reportElement x="236" y="15" width="110" height="16"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<text><![CDATA[Quarta]]></text>
				</staticText>
				<staticText>
					<reportElement x="352" y="15" width="110" height="16"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<text><![CDATA[Quinta]]></text>
				</staticText>
				<staticText>
					<reportElement x="469" y="15" width="110" height="16"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<text><![CDATA[Sexta]]></text>
				</staticText>
				<staticText>
					<reportElement x="586" y="15" width="110" height="16"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<text><![CDATA[Sábado]]></text>
				</staticText>
				<staticText>
					<reportElement x="704" y="15" width="110" height="16"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<text><![CDATA[Domingo]]></text>
				</staticText>
				<textField evaluationTime="Report" isBlankWhenNull="false">
					<reportElement key="textField-212" x="781" y="0" width="31" height="15"/>
					<box bottomPadding="2">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					</box>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[" " + $V{PAGE_NUMBER} + ""]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="false">
					<reportElement key="textField-211" x="706" y="0" width="75" height="15"/>
					<box bottomPadding="2">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Arial" size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA["Página: " + $V{PAGE_NUMBER} + " de "]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
	</group>
	<title>
		<band height="79" splitType="Immediate">
			<textField isBlankWhenNull="false">
				<reportElement key="textField-210" x="85" y="28" width="113" height="14"/>
				<textElement>
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{cidadeEmpresa}]]></textFieldExpression>
			</textField>
			<image isUsingCache="true" onErrorType="Blank" evaluationTime="Page">
				<reportElement key="image-1" x="0" y="0" width="82" height="46" isPrintWhenDetailOverflows="true"/>
				<box>
					<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<imageExpression class="java.io.InputStream"><![CDATA[$P{logoPadraoRelatorio}]]></imageExpression>
			</image>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-208" x="85" y="0" width="113" height="14"/>
				<textElement>
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{nomeEmpresa}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement key="staticText-13" x="269" y="23" width="263" height="27"/>
				<textElement textAlignment="Center">
					<font size="20" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Mapa de Turmas]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-14" mode="Opaque" x="626" y="0" width="185" height="23"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Top" lineSpacing="Single">
					<font fontName="Microsoft Sans Serif" size="9" isBold="true" isItalic="true" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica-BoldOblique"/>
				</textElement>
				<text><![CDATA[ZillyonWeb - Desenvolvido por PACTO Soluções Tecnológicas Ltda.]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-15" mode="Opaque" x="700" y="23" width="111" height="12"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Top" lineSpacing="Single">
					<font fontName="Microsoft Sans Serif" size="9" isBold="true" isItalic="true" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica-BoldOblique"/>
				</textElement>
				<text><![CDATA[(0xx62) 3251-5820]]></text>
			</staticText>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-209" x="85" y="14" width="113" height="14"/>
				<textElement>
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{enderecoEmpresa}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="false">
				<reportElement key="textField-214" x="1" y="57" width="813" height="22"/>
				<box>
					<topPen lineWidth="0.5" lineStyle="Solid"/>
					<leftPen lineWidth="0.5" lineStyle="Solid"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.5" lineStyle="Solid"/>
				</box>
				<textElement textAlignment="Center" markup="html">
					<font fontName="Arial" size="10" isBold="true" isItalic="true" pdfFontName="Helvetica-BoldOblique"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{filtros}]]></textFieldExpression>
			</textField>
			<textField pattern="dd/MM/yyyy HH:mm:ss" isBlankWhenNull="false">
				<reportElement key="dataImpressao1" mode="Transparent" x="752" y="36" width="60" height="8"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="SansSerif" size="6" isItalic="false" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression class="java.util.Date"><![CDATA[new Date()]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="703" y="36" width="49" height="8"/>
				<textElement>
					<font size="6" isBold="true"/>
				</textElement>
				<text><![CDATA[Data impressão:]]></text>
			</staticText>
		</band>
	</title>
	<detail>
		<band height="124" splitType="Immediate">
			<subreport>
				<reportElement isPrintRepeatedValues="false" x="706" y="19" width="108" height="100"/>
				<subreportParameter name="enderecoEmpresa"/>
				<subreportParameter name="totalCompetencia"/>
				<subreportParameter name="colValorModalidade"/>
				<subreportParameter name="colInicio"/>
				<subreportParameter name="usuario"/>
				<subreportParameter name="colVence"/>
				<subreportParameter name="totalValor"/>
				<subreportParameter name="colNome"/>
				<subreportParameter name="totalContratos"/>
				<subreportParameter name="SUBREPORT_DIR1"/>
				<subreportParameter name="colDuracao"/>
				<subreportParameter name="colHorario"/>
				<subreportParameter name="colSituacao"/>
				<subreportParameter name="colMatricula"/>
				<subreportParameter name="dataIni"/>
				<subreportParameter name="colFaturamento"/>
				<subreportParameter name="colContrato"/>
				<subreportParameter name="logoPadraoRelatorio"/>
				<subreportParameter name="totalClientes"/>
				<subreportParameter name="SUBREPORT_DIR">
					<subreportParameterExpression><![CDATA[$P{SUBREPORT_DIR}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="dataFim"/>
				<subreportParameter name="colModalidade"/>
				<subreportParameter name="tituloRelatorio"/>
				<subreportParameter name="colValor"/>
				<subreportParameter name="colPlano"/>
				<subreportParameter name="nomeEmpresa"/>
				<subreportParameter name="colVinculo"/>
				<subreportParameter name="colDataLancamento"/>
				<subreportParameter name="cidadeEmpresa"/>
				<subreportParameter name="listaTotais"/>
				<subreportParameter name="filtros"/>
				<subreportParameter name="versaoSoftware"/>
				<dataSourceExpression><![CDATA[$F{mapa.domingo.itensJr}]]></dataSourceExpression>
				<subreportExpression class="java.lang.String"><![CDATA[$P{SUBREPORT_DIR} + "MapaTurmasDia.jasper"]]></subreportExpression>
			</subreport>
			<subreport>
				<reportElement isPrintRepeatedValues="false" x="588" y="19" width="112" height="100"/>
				<subreportParameter name="enderecoEmpresa"/>
				<subreportParameter name="totalCompetencia"/>
				<subreportParameter name="colValorModalidade"/>
				<subreportParameter name="colInicio"/>
				<subreportParameter name="usuario"/>
				<subreportParameter name="colVence"/>
				<subreportParameter name="totalValor"/>
				<subreportParameter name="colNome"/>
				<subreportParameter name="totalContratos"/>
				<subreportParameter name="SUBREPORT_DIR1"/>
				<subreportParameter name="colDuracao"/>
				<subreportParameter name="colHorario"/>
				<subreportParameter name="colSituacao"/>
				<subreportParameter name="colMatricula"/>
				<subreportParameter name="dataIni"/>
				<subreportParameter name="colFaturamento"/>
				<subreportParameter name="colContrato"/>
				<subreportParameter name="logoPadraoRelatorio"/>
				<subreportParameter name="totalClientes"/>
				<subreportParameter name="SUBREPORT_DIR">
					<subreportParameterExpression><![CDATA[$P{SUBREPORT_DIR}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="dataFim"/>
				<subreportParameter name="colModalidade"/>
				<subreportParameter name="tituloRelatorio"/>
				<subreportParameter name="colValor"/>
				<subreportParameter name="colPlano"/>
				<subreportParameter name="nomeEmpresa"/>
				<subreportParameter name="colVinculo"/>
				<subreportParameter name="colDataLancamento"/>
				<subreportParameter name="cidadeEmpresa"/>
				<subreportParameter name="listaTotais"/>
				<subreportParameter name="filtros"/>
				<subreportParameter name="versaoSoftware"/>
				<dataSourceExpression><![CDATA[$F{mapa.sabado.itensJr}]]></dataSourceExpression>
				<subreportExpression class="java.lang.String"><![CDATA[$P{SUBREPORT_DIR} + "MapaTurmasDia.jasper"]]></subreportExpression>
			</subreport>
			<subreport>
				<reportElement isPrintRepeatedValues="false" x="118" y="19" width="112" height="100"/>
				<subreportParameter name="enderecoEmpresa"/>
				<subreportParameter name="totalCompetencia"/>
				<subreportParameter name="colValorModalidade"/>
				<subreportParameter name="colInicio"/>
				<subreportParameter name="usuario"/>
				<subreportParameter name="colVence"/>
				<subreportParameter name="totalValor"/>
				<subreportParameter name="colNome"/>
				<subreportParameter name="totalContratos"/>
				<subreportParameter name="SUBREPORT_DIR1"/>
				<subreportParameter name="colDuracao"/>
				<subreportParameter name="colHorario"/>
				<subreportParameter name="colSituacao"/>
				<subreportParameter name="colMatricula"/>
				<subreportParameter name="dataIni"/>
				<subreportParameter name="colFaturamento"/>
				<subreportParameter name="colContrato"/>
				<subreportParameter name="logoPadraoRelatorio"/>
				<subreportParameter name="totalClientes"/>
				<subreportParameter name="SUBREPORT_DIR">
					<subreportParameterExpression><![CDATA[$P{SUBREPORT_DIR}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="dataFim"/>
				<subreportParameter name="colModalidade"/>
				<subreportParameter name="tituloRelatorio"/>
				<subreportParameter name="colValor"/>
				<subreportParameter name="colPlano"/>
				<subreportParameter name="nomeEmpresa"/>
				<subreportParameter name="colVinculo"/>
				<subreportParameter name="colDataLancamento"/>
				<subreportParameter name="cidadeEmpresa"/>
				<subreportParameter name="listaTotais"/>
				<subreportParameter name="filtros"/>
				<subreportParameter name="versaoSoftware"/>
				<dataSourceExpression><![CDATA[$F{mapa.terca.itensJr}]]></dataSourceExpression>
				<subreportExpression class="java.lang.String"><![CDATA[$P{SUBREPORT_DIR} + "MapaTurmasDia.jasper"]]></subreportExpression>
			</subreport>
			<rectangle>
				<reportElement x="0" y="0" width="814" height="16" forecolor="#FFFFFF" backcolor="#CCCCCC"/>
			</rectangle>
			<subreport>
				<reportElement isPrintRepeatedValues="false" x="236" y="19" width="112" height="100"/>
				<subreportParameter name="colValorModalidade"/>
				<subreportParameter name="totalCompetencia"/>
				<subreportParameter name="enderecoEmpresa"/>
				<subreportParameter name="colInicio"/>
				<subreportParameter name="usuario"/>
				<subreportParameter name="colVence"/>
				<subreportParameter name="colNome"/>
				<subreportParameter name="totalValor"/>
				<subreportParameter name="totalContratos"/>
				<subreportParameter name="SUBREPORT_DIR1"/>
				<subreportParameter name="colDuracao"/>
				<subreportParameter name="colHorario"/>
				<subreportParameter name="colSituacao"/>
				<subreportParameter name="colMatricula"/>
				<subreportParameter name="dataIni"/>
				<subreportParameter name="colFaturamento"/>
				<subreportParameter name="colContrato"/>
				<subreportParameter name="logoPadraoRelatorio"/>
				<subreportParameter name="totalClientes"/>
				<subreportParameter name="SUBREPORT_DIR">
					<subreportParameterExpression><![CDATA[$P{SUBREPORT_DIR}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="dataFim"/>
				<subreportParameter name="colModalidade"/>
				<subreportParameter name="tituloRelatorio"/>
				<subreportParameter name="colValor"/>
				<subreportParameter name="colPlano"/>
				<subreportParameter name="nomeEmpresa"/>
				<subreportParameter name="colVinculo"/>
				<subreportParameter name="listaTotais"/>
				<subreportParameter name="cidadeEmpresa"/>
				<subreportParameter name="colDataLancamento"/>
				<subreportParameter name="versaoSoftware"/>
				<subreportParameter name="filtros"/>
				<dataSourceExpression><![CDATA[$F{mapa.quarta.itensJr}]]></dataSourceExpression>
				<subreportExpression class="java.lang.String"><![CDATA[$P{SUBREPORT_DIR} + "MapaTurmasDia.jasper"]]></subreportExpression>
			</subreport>
			<textField>
				<reportElement x="5" y="0" width="84" height="16"/>
				<textElement verticalAlignment="Middle">
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{horario}]]></textFieldExpression>
			</textField>
			<subreport>
				<reportElement isPrintRepeatedValues="false" x="354" y="19" width="112" height="100"/>
				<subreportParameter name="enderecoEmpresa"/>
				<subreportParameter name="totalCompetencia"/>
				<subreportParameter name="colValorModalidade"/>
				<subreportParameter name="colInicio"/>
				<subreportParameter name="usuario"/>
				<subreportParameter name="colVence"/>
				<subreportParameter name="totalValor"/>
				<subreportParameter name="colNome"/>
				<subreportParameter name="totalContratos"/>
				<subreportParameter name="SUBREPORT_DIR1"/>
				<subreportParameter name="colDuracao"/>
				<subreportParameter name="colHorario"/>
				<subreportParameter name="colSituacao"/>
				<subreportParameter name="colMatricula"/>
				<subreportParameter name="dataIni"/>
				<subreportParameter name="colFaturamento"/>
				<subreportParameter name="colContrato"/>
				<subreportParameter name="logoPadraoRelatorio"/>
				<subreportParameter name="totalClientes"/>
				<subreportParameter name="SUBREPORT_DIR">
					<subreportParameterExpression><![CDATA[$P{SUBREPORT_DIR}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="dataFim"/>
				<subreportParameter name="colModalidade"/>
				<subreportParameter name="tituloRelatorio"/>
				<subreportParameter name="colValor"/>
				<subreportParameter name="colPlano"/>
				<subreportParameter name="nomeEmpresa"/>
				<subreportParameter name="colVinculo"/>
				<subreportParameter name="colDataLancamento"/>
				<subreportParameter name="cidadeEmpresa"/>
				<subreportParameter name="listaTotais"/>
				<subreportParameter name="filtros"/>
				<subreportParameter name="versaoSoftware"/>
				<dataSourceExpression><![CDATA[$F{mapa.quinta.itensJr}]]></dataSourceExpression>
				<subreportExpression class="java.lang.String"><![CDATA[$P{SUBREPORT_DIR} + "MapaTurmasDia.jasper"]]></subreportExpression>
			</subreport>
			<subreport>
				<reportElement isPrintRepeatedValues="false" x="471" y="19" width="112" height="100"/>
				<subreportParameter name="enderecoEmpresa"/>
				<subreportParameter name="totalCompetencia"/>
				<subreportParameter name="colValorModalidade"/>
				<subreportParameter name="colInicio"/>
				<subreportParameter name="usuario"/>
				<subreportParameter name="colVence"/>
				<subreportParameter name="totalValor"/>
				<subreportParameter name="colNome"/>
				<subreportParameter name="totalContratos"/>
				<subreportParameter name="SUBREPORT_DIR1"/>
				<subreportParameter name="colDuracao"/>
				<subreportParameter name="colHorario"/>
				<subreportParameter name="colSituacao"/>
				<subreportParameter name="colMatricula"/>
				<subreportParameter name="dataIni"/>
				<subreportParameter name="colFaturamento"/>
				<subreportParameter name="colContrato"/>
				<subreportParameter name="logoPadraoRelatorio"/>
				<subreportParameter name="totalClientes"/>
				<subreportParameter name="SUBREPORT_DIR">
					<subreportParameterExpression><![CDATA[$P{SUBREPORT_DIR}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="dataFim"/>
				<subreportParameter name="colModalidade"/>
				<subreportParameter name="tituloRelatorio"/>
				<subreportParameter name="colValor"/>
				<subreportParameter name="colPlano"/>
				<subreportParameter name="nomeEmpresa"/>
				<subreportParameter name="colVinculo"/>
				<subreportParameter name="colDataLancamento"/>
				<subreportParameter name="cidadeEmpresa"/>
				<subreportParameter name="listaTotais"/>
				<subreportParameter name="filtros"/>
				<subreportParameter name="versaoSoftware"/>
				<dataSourceExpression><![CDATA[$F{mapa.sexta.itensJr}]]></dataSourceExpression>
				<subreportExpression class="java.lang.String"><![CDATA[$P{SUBREPORT_DIR} + "MapaTurmasDia.jasper"]]></subreportExpression>
			</subreport>
			<subreport>
				<reportElement isPrintRepeatedValues="false" x="0" y="19" width="113" height="100"/>
				<subreportParameter name="enderecoEmpresa"/>
				<subreportParameter name="totalCompetencia"/>
				<subreportParameter name="colValorModalidade"/>
				<subreportParameter name="colInicio"/>
				<subreportParameter name="usuario"/>
				<subreportParameter name="colVence"/>
				<subreportParameter name="totalValor"/>
				<subreportParameter name="colNome"/>
				<subreportParameter name="totalContratos"/>
				<subreportParameter name="SUBREPORT_DIR1"/>
				<subreportParameter name="colDuracao"/>
				<subreportParameter name="colHorario"/>
				<subreportParameter name="colSituacao"/>
				<subreportParameter name="colMatricula"/>
				<subreportParameter name="dataIni"/>
				<subreportParameter name="colFaturamento"/>
				<subreportParameter name="colContrato"/>
				<subreportParameter name="logoPadraoRelatorio"/>
				<subreportParameter name="totalClientes"/>
				<subreportParameter name="SUBREPORT_DIR">
					<subreportParameterExpression><![CDATA[$P{SUBREPORT_DIR}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="dataFim"/>
				<subreportParameter name="colModalidade"/>
				<subreportParameter name="tituloRelatorio"/>
				<subreportParameter name="colValor"/>
				<subreportParameter name="colPlano"/>
				<subreportParameter name="nomeEmpresa"/>
				<subreportParameter name="colVinculo"/>
				<subreportParameter name="colDataLancamento"/>
				<subreportParameter name="cidadeEmpresa"/>
				<subreportParameter name="listaTotais"/>
				<subreportParameter name="filtros"/>
				<subreportParameter name="versaoSoftware"/>
				<dataSourceExpression><![CDATA[$F{mapa.segunda.itensJr}]]></dataSourceExpression>
				<subreportExpression class="java.lang.String"><![CDATA[$P{SUBREPORT_DIR} + "MapaTurmasDia.jasper"]]></subreportExpression>
			</subreport>
		</band>
	</detail>
</jasperReport>

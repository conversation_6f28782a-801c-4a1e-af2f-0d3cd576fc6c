<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="MapaTurmasDia" pageWidth="113" pageHeight="283" orientation="Landscape" columnWidth="113" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0">
	<property name="ireport.zoom" value="9.415285130163051"/>
	<property name="ireport.x" value="254"/>
	<property name="ireport.y" value="0"/>
	<parameter name="logoPadraoRelatorio" class="java.io.InputStream" isForPrompting="false"/>
	<parameter name="tituloRelatorio" class="java.lang.String" isForPrompting="false"/>
	<parameter name="versaoSoftware" class="java.lang.String" isForPrompting="false"/>
	<parameter name="usuario" class="java.lang.String" isForPrompting="false"/>
	<parameter name="filtros" class="java.lang.String" isForPrompting="false"/>
	<parameter name="SUBREPORT_DIR" class="java.lang.String">
		<defaultValueExpression><![CDATA["D:\\PactoJ\\Desenvolvimento\\Sistemas\\ZillyonWeb\\tronco-novo\\src\\java\\relatorio\\designRelatorio\\outros\\"]]></defaultValueExpression>
	</parameter>
	<parameter name="nomeEmpresa" class="java.lang.String"/>
	<parameter name="enderecoEmpresa" class="java.lang.String"/>
	<parameter name="cidadeEmpresa" class="java.lang.String"/>
	<parameter name="dataIni" class="java.lang.String" isForPrompting="false"/>
	<parameter name="dataFim" class="java.lang.String" isForPrompting="false"/>
	<parameter name="SUBREPORT_DIR1" class="java.lang.String"/>
	<parameter name="totalClientes" class="java.lang.String" isForPrompting="false"/>
	<parameter name="totalContratos" class="java.lang.String" isForPrompting="false"/>
	<parameter name="totalValor" class="java.lang.String" isForPrompting="false"/>
	<parameter name="totalCompetencia" class="java.lang.String" isForPrompting="false"/>
	<parameter name="listaTotais" class="java.lang.Object" isForPrompting="false"/>
	<parameter name="colMatricula" class="java.lang.Boolean"/>
	<parameter name="colNome" class="java.lang.Boolean"/>
	<parameter name="colSituacao" class="java.lang.Boolean"/>
	<parameter name="colVinculo" class="java.lang.Boolean"/>
	<parameter name="colPlano" class="java.lang.Boolean"/>
	<parameter name="colContrato" class="java.lang.Boolean"/>
	<parameter name="colModalidade" class="java.lang.Boolean"/>
	<parameter name="colValorModalidade" class="java.lang.Boolean"/>
	<parameter name="colDuracao" class="java.lang.Boolean"/>
	<parameter name="colDataLancamento" class="java.lang.Boolean"/>
	<parameter name="colInicio" class="java.lang.Boolean"/>
	<parameter name="colVence" class="java.lang.Boolean"/>
	<parameter name="colValor" class="java.lang.Boolean"/>
	<parameter name="colHorario" class="java.lang.Boolean"/>
	<parameter name="colFaturamento" class="java.lang.Boolean"/>
	<field name="codigo" class="java.lang.Integer"/>
	<field name="nome" class="java.lang.String"/>
	<field name="matricula" class="java.lang.String"/>
	<field name="codigoCliente" class="java.lang.Integer"/>
	<detail>
		<band height="13" splitType="Prevent">
			<textField>
				<reportElement x="0" y="0" width="113" height="11"/>
				<textElement>
					<font size="7"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{codigoCliente} > 0 ? $F{codigo}+"-"+$F{matricula}+"-"+$F{nome} : $F{codigo}+"-"]]></textFieldExpression>
			</textField>
			<line>
				<reportElement x="11" y="11" width="102" height="1">
					<printWhenExpression><![CDATA[$F{codigoCliente} == 0]]></printWhenExpression>
				</reportElement>
			</line>
		</band>
	</detail>
</jasperReport>

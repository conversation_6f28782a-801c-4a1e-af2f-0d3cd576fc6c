¬í sr (net.sf.jasperreports.engine.JasperReport      'Ø L compileDatat Ljava/io/Serializable;L compileNameSuffixt Ljava/lang/String;L 
compilerClassq ~ xr -net.sf.jasperreports.engine.base.JRBaseReport      'Ø +I PSEUDO_SERIAL_VERSION_UIDI bottomMarginI columnCountI 
columnSpacingI columnWidthZ ignorePaginationZ isFloatColumnFooterZ isSummaryNewPageZ  isSummaryWithPageHeaderAndFooterZ isTitleNewPageI 
leftMarginB orientationI 
pageHeightI 	pageWidthB 
printOrderI rightMarginI 	topMarginB whenNoDataTypeL 
backgroundt $Lnet/sf/jasperreports/engine/JRBand;L columnFooterq ~ L columnHeaderq ~ [ datasetst ([Lnet/sf/jasperreports/engine/JRDataset;L defaultFontt *Lnet/sf/jasperreports/engine/JRReportFont;L defaultStylet %Lnet/sf/jasperreports/engine/JRStyle;L detailq ~ L 
detailSectiont 'Lnet/sf/jasperreports/engine/JRSection;[ fontst +[Lnet/sf/jasperreports/engine/JRReportFont;L formatFactoryClassq ~ L 
importsSett Ljava/util/Set;L languageq ~ L lastPageFooterq ~ L mainDatasett 'Lnet/sf/jasperreports/engine/JRDataset;L nameq ~ L noDataq ~ L orientationValuet 2Lnet/sf/jasperreports/engine/type/OrientationEnum;L 
pageFooterq ~ L 
pageHeaderq ~ L printOrderValuet 1Lnet/sf/jasperreports/engine/type/PrintOrderEnum;[ stylest &[Lnet/sf/jasperreports/engine/JRStyle;L summaryq ~ [ 	templatest /[Lnet/sf/jasperreports/engine/JRReportTemplate;L titleq ~ L whenNoDataTypeValuet 5Lnet/sf/jasperreports/engine/type/WhenNoDataTypeEnum;xp  wî            F           ¨  n        pppppppsr .net.sf.jasperreports.engine.base.JRBaseSection      'Ø [ bandst %[Lnet/sf/jasperreports/engine/JRBand;xpur %[Lnet.sf.jasperreports.engine.JRBand;Ý~ìÊ5  xp   sr +net.sf.jasperreports.engine.base.JRBaseBand      'Ø I PSEUDO_SERIAL_VERSION_UIDI heightZ isSplitAllowedL printWhenExpressiont *Lnet/sf/jasperreports/engine/JRExpression;L 	splitTypet Ljava/lang/Byte;L splitTypeValuet 0Lnet/sf/jasperreports/engine/type/SplitTypeEnum;xr 3net.sf.jasperreports.engine.base.JRBaseElementGroup      'Ø L childrent Ljava/util/List;L elementGroupt ,Lnet/sf/jasperreports/engine/JRElementGroup;xpsr java.util.ArrayListxÒÇa I sizexp   	w   	sr 0net.sf.jasperreports.engine.base.JRBaseTextField      'Ø I PSEUDO_SERIAL_VERSION_UIDI 
bookmarkLevelB evaluationTimeB hyperlinkTargetB 
hyperlinkTypeZ isStretchWithOverflowL anchorNameExpressionq ~ L evaluationGroupt %Lnet/sf/jasperreports/engine/JRGroup;L evaluationTimeValuet 5Lnet/sf/jasperreports/engine/type/EvaluationTimeEnum;L 
expressionq ~ L hyperlinkAnchorExpressionq ~ L hyperlinkPageExpressionq ~ [ hyperlinkParameterst 3[Lnet/sf/jasperreports/engine/JRHyperlinkParameter;L hyperlinkReferenceExpressionq ~ L hyperlinkTooltipExpressionq ~ L isBlankWhenNullt Ljava/lang/Boolean;L 
linkTargetq ~ L linkTypeq ~ L patternq ~ xr 2net.sf.jasperreports.engine.base.JRBaseTextElement      'Ø %I PSEUDO_SERIAL_VERSION_UIDL borderq ~ L borderColort Ljava/awt/Color;L bottomBorderq ~ L bottomBorderColorq ~ 'L 
bottomPaddingt Ljava/lang/Integer;L fontNameq ~ L fontSizeq ~ (L horizontalAlignmentq ~ L horizontalAlignmentValuet 6Lnet/sf/jasperreports/engine/type/HorizontalAlignEnum;L isBoldq ~ %L isItalicq ~ %L 
isPdfEmbeddedq ~ %L isStrikeThroughq ~ %L isStyledTextq ~ %L isUnderlineq ~ %L 
leftBorderq ~ L leftBorderColorq ~ 'L leftPaddingq ~ (L lineBoxt 'Lnet/sf/jasperreports/engine/JRLineBox;L lineSpacingq ~ L lineSpacingValuet 2Lnet/sf/jasperreports/engine/type/LineSpacingEnum;L markupq ~ L paddingq ~ (L pdfEncodingq ~ L pdfFontNameq ~ L 
reportFontq ~ L rightBorderq ~ L rightBorderColorq ~ 'L rightPaddingq ~ (L rotationq ~ L 
rotationValuet /Lnet/sf/jasperreports/engine/type/RotationEnum;L 	topBorderq ~ L topBorderColorq ~ 'L 
topPaddingq ~ (L verticalAlignmentq ~ L verticalAlignmentValuet 4Lnet/sf/jasperreports/engine/type/VerticalAlignEnum;xr .net.sf.jasperreports.engine.base.JRBaseElement      'Ø I PSEUDO_SERIAL_VERSION_UIDI heightZ isPrintInFirstWholeBandZ isPrintRepeatedValuesZ isPrintWhenDetailOverflowsZ isRemoveLineWhenBlankB positionTypeB stretchTypeI widthI xI yL 	backcolorq ~ 'L defaultStyleProvidert 4Lnet/sf/jasperreports/engine/JRDefaultStyleProvider;L elementGroupq ~ L 	forecolorq ~ 'L keyq ~ L modeq ~ L 	modeValuet +Lnet/sf/jasperreports/engine/type/ModeEnum;L parentStyleq ~ L parentStyleNameReferenceq ~ L positionTypeValuet 3Lnet/sf/jasperreports/engine/type/PositionTypeEnum;L printWhenExpressionq ~ L printWhenGroupChangesq ~ "L 
propertiesMapt -Lnet/sf/jasperreports/engine/JRPropertiesMap;[ propertyExpressionst 3[Lnet/sf/jasperreports/engine/JRPropertyExpression;L stretchTypeValuet 2Lnet/sf/jasperreports/engine/type/StretchTypeEnum;xp  wî           T   ¯   pq ~ q ~ pt 
textField-223pppp~r 1net.sf.jasperreports.engine.type.PositionTypeEnum          xr java.lang.Enum          xpt FIX_RELATIVE_TO_TOPpppp~r 0net.sf.jasperreports.engine.type.StretchTypeEnum          xq ~ 8t 
NO_STRETCH  wîppppppsr java.lang.Integerâ ¤÷8 I valuexr java.lang.Number¬à  xp   p~r 4net.sf.jasperreports.engine.type.HorizontalAlignEnum          xq ~ 8t CENTERpppppppppsr .net.sf.jasperreports.engine.base.JRBaseLineBox      'Ø L 
bottomPaddingq ~ (L 	bottomPent +Lnet/sf/jasperreports/engine/base/JRBoxPen;L boxContainert ,Lnet/sf/jasperreports/engine/JRBoxContainer;L leftPaddingq ~ (L leftPenq ~ EL paddingq ~ (L penq ~ EL rightPaddingq ~ (L rightPenq ~ EL 
topPaddingq ~ (L topPenq ~ Exppsr 3net.sf.jasperreports.engine.base.JRBaseBoxBottomPen      'Ø  xr -net.sf.jasperreports.engine.base.JRBaseBoxPen      'Ø L lineBoxq ~ *xr *net.sf.jasperreports.engine.base.JRBasePen      'Ø I PSEUDO_SERIAL_VERSION_UIDL 	lineColorq ~ 'L 	lineStyleq ~ L lineStyleValuet 0Lnet/sf/jasperreports/engine/type/LineStyleEnum;L 	lineWidtht Ljava/lang/Float;L penContainert ,Lnet/sf/jasperreports/engine/JRPenContainer;xp  wîppppq ~ Gq ~ Gq ~ 5psr 1net.sf.jasperreports.engine.base.JRBaseBoxLeftPen      'Ø  xq ~ I  wîppppq ~ Gq ~ Gpsq ~ I  wîppppq ~ Gq ~ Gpsr 2net.sf.jasperreports.engine.base.JRBaseBoxRightPen      'Ø  xq ~ I  wîppppq ~ Gq ~ Gpsr 0net.sf.jasperreports.engine.base.JRBaseBoxTopPen      'Ø  xq ~ I  wîppppq ~ Gq ~ Gpppppppppppppppp~r 2net.sf.jasperreports.engine.type.VerticalAlignEnum          xq ~ 8t MIDDLE  wî        pp~r 3net.sf.jasperreports.engine.type.EvaluationTimeEnum          xq ~ 8t NOWsr 1net.sf.jasperreports.engine.base.JRBaseExpression      'Ø I id[ chunkst 0[Lnet/sf/jasperreports/engine/JRExpressionChunk;L valueClassNameq ~ L valueClassRealNameq ~ xp   ur 0[Lnet.sf.jasperreports.engine.JRExpressionChunk;mYÏÞiK£U  xp   sr 6net.sf.jasperreports.engine.base.JRBaseExpressionChunk      'Ø B typeL textq ~ xpt contratosq ~ at  != 0 ? sq ~ at contratosq ~ at  : sq ~ at 
codProdutot java.lang.Integerppppppsr java.lang.BooleanÍ rÕúî Z valuexppppsq ~ !  wî           ]   R   pq ~ q ~ pt 
textField-224ppppq ~ 9ppppq ~ <  wîppppppq ~ @p~q ~ At LEFTsq ~ m ppppppppsq ~ Dpsq ~ H  wîppppq ~ tq ~ tq ~ opsq ~ O  wîppppq ~ tq ~ tpsq ~ I  wîppppq ~ tq ~ tpsq ~ R  wîppppq ~ tq ~ tpsq ~ T  wîppppq ~ tq ~ tppppppppppppppppq ~ W  wî        ppq ~ Zsq ~ \   uq ~ _   sq ~ at descricaoLancamentot java.lang.Stringppppppq ~ npppsq ~ !  wî           6  h   pq ~ q ~ pt 	textFieldpppp~q ~ 7t FLOATppppq ~ <  wîppppppq ~ @pq ~ Bpppppppppsq ~ Dpsq ~ H  wîppppq ~ q ~ q ~ psq ~ O  wîppppq ~ q ~ psq ~ I  wîppppq ~ q ~ psq ~ R  wîppppq ~ q ~ psq ~ T  wîppppq ~ q ~ ppppppppppppppppq ~ W  wî        ppq ~ Zsq ~ \   
uq ~ _   sq ~ at valorLancamentoApresentarExcelt java.lang.Stringppppppq ~ nppt  sq ~ !  wî           "  F   pq ~ q ~ pt 	textFieldppppq ~ ppppq ~ <  wîppppppq ~ @pq ~ Bpppppppppsq ~ Dpsq ~ H  wîppppq ~ q ~ q ~ psq ~ O  wîppppq ~ q ~ psq ~ I  wîppppq ~ q ~ psq ~ R  wîppppq ~ q ~ psq ~ T  wîppppq ~ q ~ ppppppppppppppppq ~ W  wî        ppq ~ Zsq ~ \   uq ~ _   sq ~ at 
tipoDescricaot java.lang.Stringppppppq ~ nppq ~ sq ~ !  wî           y  e   pq ~ q ~ ppppppq ~ 9ppppq ~ <  wîppppppq ~ @pppppppppppsq ~ Dpsq ~ H  wîppppq ~ q ~ q ~ psq ~ O  wîppppq ~ q ~ psq ~ I  wîppppq ~ q ~ psq ~ R  wîppppq ~ q ~ psq ~ T  wîppppq ~ q ~ ppppppppppppppppq ~ W  wî        ppq ~ Zsq ~ \   uq ~ _   sq ~ at descricaoFormaPagamentot java.lang.Stringppppppq ~ npppsq ~ !  wî           b     pq ~ q ~ pt 
textField-224ppppq ~ 9ppppq ~ <  wîppppppq ~ @pq ~ qq ~ sppppppppsq ~ Dpsq ~ H  wîppppq ~ ªq ~ ªq ~ ¨psq ~ O  wîppppq ~ ªq ~ ªpsq ~ I  wîppppq ~ ªq ~ ªpsq ~ R  wîppppq ~ ªq ~ ªpsq ~ T  wîppppq ~ ªq ~ ªppppppppppppppppq ~ W  wî        ppq ~ Zsq ~ \   uq ~ _   sq ~ at 
nomePessoat java.lang.Stringppppppq ~ npppsq ~ !  wî           Q      pq ~ q ~ ppppppq ~ 9ppppq ~ <  wîppppppq ~ @pppppppppppsq ~ Dpsq ~ H  wîppppq ~ ¶q ~ ¶q ~ µpsq ~ O  wîppppq ~ ¶q ~ ¶psq ~ I  wîppppq ~ ¶q ~ ¶psq ~ R  wîppppq ~ ¶q ~ ¶psq ~ T  wîppppq ~ ¶q ~ ¶ppppppppppppppppq ~ W  wî        ppq ~ Zsq ~ \   uq ~ _   sq ~ at 
planoFilhot java.lang.Stringppppppppppsq ~ !  wî           h  Þ   pq ~ q ~ pt 	textFieldppppq ~ ppppq ~ <  wîppppppq ~ @pq ~ Bpppppppppsq ~ Dpsq ~ H  wîppppq ~ Ãq ~ Ãq ~ Ápsq ~ O  wîppppq ~ Ãq ~ Ãpsq ~ I  wîppppq ~ Ãq ~ Ãpsq ~ R  wîppppq ~ Ãq ~ Ãpsq ~ T  wîppppq ~ Ãq ~ Ãppppppppppppppppq ~ W  wî        ppq ~ Zsq ~ \   uq ~ _   
sq ~ at (sq ~ at tipoRelatorioDFsq ~ at .equals(2) ||
					sq ~ at tipoRelatorioDFsq ~ at .equals(5) ||
					sq ~ at tipoRelatorioDFsq ~ at .equals(6)) ? sq ~ at dataMesApresentarsq ~ at  : sq ~ at dataApresentart java.lang.Stringppppppq ~ nppq ~ sq ~ !  wî           b     pq ~ q ~ pt 
textField-224ppppq ~ 9ppppq ~ <  wîppppppq ~ @pq ~ qq ~ sppppppppsq ~ Dpsq ~ H  wîppppq ~ âq ~ âq ~ àpsq ~ O  wîppppq ~ âq ~ âpsq ~ I  wîppppq ~ âq ~ âpsq ~ R  wîppppq ~ âq ~ âpsq ~ T  wîppppq ~ âq ~ âppppppppppppppppq ~ W  wî        ppq ~ Zsq ~ \   uq ~ _   sq ~ at empresaApresentart java.lang.Stringppppppq ~ npppxp  wî   pppppsr java.util.HashSetºD¸·4  xpw   ?@     t "net.sf.jasperreports.engine.data.*t net.sf.jasperreports.engine.*t java.util.*xt javapsr .net.sf.jasperreports.engine.base.JRBaseDataset      'Ø I PSEUDO_SERIAL_VERSION_UIDZ isMainB whenResourceMissingType[ fieldst &[Lnet/sf/jasperreports/engine/JRField;L filterExpressionq ~ [ groupst &[Lnet/sf/jasperreports/engine/JRGroup;L nameq ~ [ 
parameterst *[Lnet/sf/jasperreports/engine/JRParameter;L 
propertiesMapq ~ 2L queryt %Lnet/sf/jasperreports/engine/JRQuery;L resourceBundleq ~ L scriptletClassq ~ [ 
scriptletst *[Lnet/sf/jasperreports/engine/JRScriptlet;[ 
sortFieldst *[Lnet/sf/jasperreports/engine/JRSortField;[ 	variablest )[Lnet/sf/jasperreports/engine/JRVariable;L whenResourceMissingTypeValuet >Lnet/sf/jasperreports/engine/type/WhenResourceMissingTypeEnum;xp  wî ur &[Lnet.sf.jasperreports.engine.JRField;<ßÇN*òp  xp   sr ,net.sf.jasperreports.engine.base.JRBaseField      'Ø L descriptionq ~ L nameq ~ L 
propertiesMapq ~ 2L valueClassNameq ~ L valueClassRealNameq ~ xpt  t descricaoLancamentosr +net.sf.jasperreports.engine.JRPropertiesMap      'Ø L baseq ~ 2L propertiesListq ~ L 
propertiesMapt Ljava/util/Map;xppppt java.lang.Stringpsq ~ ÿpt contratosq ~pppt java.lang.Integerpsq ~ ÿt  t 
nomePessoasq ~pppt java.lang.Stringpsq ~ ÿt  t 
movProdutosq ~pppt java.lang.Integerpsq ~ ÿt  t tipoFormaPagto.descricaosq ~pppt java.lang.Stringpsq ~ ÿpt movPagamentosq ~pppt java.lang.Integerpsq ~ ÿpt valorLancamentoApresentarExcelsq ~pppt java.lang.Stringpsq ~ ÿpt descricaoFormaPagamentosq ~pppt java.lang.Stringpsq ~ ÿpt dataApresentarsq ~pppt java.lang.Stringpsq ~ ÿpt dataMesApresentarsq ~pppt java.lang.Stringpsq ~ ÿpt tipoES.descricaosq ~pppt java.lang.Stringpsq ~ ÿpt valorLancamentosq ~pppt java.lang.Doublepsq ~ ÿpt empresaApresentarsq ~pppt java.lang.Stringpsq ~ ÿpt 
codProdutosq ~pppt java.lang.Integerpppt DemonstrativoFinanceiroRelExcelur *[Lnet.sf.jasperreports.engine.JRParameter;" *Ã`!  xp   sr 0net.sf.jasperreports.engine.base.JRBaseParameter      'Ø 	Z isForPromptingZ isSystemDefinedL defaultValueExpressionq ~ L descriptionq ~ L nameq ~ L nestedTypeNameq ~ L 
propertiesMapq ~ 2L valueClassNameq ~ L valueClassRealNameq ~ xpppt REPORT_PARAMETERS_MAPpsq ~pppt 
java.util.Mappsq ~Appt 
JASPER_REPORTpsq ~pppt (net.sf.jasperreports.engine.JasperReportpsq ~Appt REPORT_CONNECTIONpsq ~pppt java.sql.Connectionpsq ~Appt REPORT_MAX_COUNTpsq ~pppq ~=psq ~Appt REPORT_DATA_SOURCEpsq ~pppt (net.sf.jasperreports.engine.JRDataSourcepsq ~Appt REPORT_SCRIPTLETpsq ~pppt /net.sf.jasperreports.engine.JRAbstractScriptletpsq ~Appt 
REPORT_LOCALEpsq ~pppt java.util.Localepsq ~Appt REPORT_RESOURCE_BUNDLEpsq ~pppt java.util.ResourceBundlepsq ~Appt REPORT_TIME_ZONEpsq ~pppt java.util.TimeZonepsq ~Appt REPORT_FORMAT_FACTORYpsq ~pppt .net.sf.jasperreports.engine.util.FormatFactorypsq ~Appt REPORT_CLASS_LOADERpsq ~pppt java.lang.ClassLoaderpsq ~Appt REPORT_URL_HANDLER_FACTORYpsq ~pppt  java.net.URLStreamHandlerFactorypsq ~Appt REPORT_FILE_RESOLVERpsq ~pppt -net.sf.jasperreports.engine.util.FileResolverpsq ~Appt REPORT_TEMPLATESpsq ~pppt java.util.Collectionpsq ~Appt REPORT_VIRTUALIZERpsq ~pppt )net.sf.jasperreports.engine.JRVirtualizerpsq ~Appt IS_IGNORE_PAGINATIONpsq ~pppt java.lang.Booleanpsq ~A ppt tipoRelatorioDFpsq ~pppt java.lang.Integerpsq ~A ppt 
planoFilhopsq ~pppt java.lang.Stringpsq ~A ppt 
tituloDatapsq ~pppt java.lang.Stringpsq ~psq ~    w   t ireport.scriptlethandlingt ireport.encodingt ireport.zoomt 	ireport.xt 	ireport.yxsr java.util.HashMapÚÁÃ`Ñ F 
loadFactorI 	thresholdxp?@     w      q ~t 1.5000000000000038q ~t 
ISO-8859-1q ~t 0q ~t 0q ~t 0xsr ,net.sf.jasperreports.engine.base.JRBaseQuery      'Ø [ chunkst +[Lnet/sf/jasperreports/engine/JRQueryChunk;L languageq ~ xppt sqlppppur )[Lnet.sf.jasperreports.engine.JRVariable;bæ|,·D  xp   sr /net.sf.jasperreports.engine.base.JRBaseVariable      'Ø I PSEUDO_SERIAL_VERSION_UIDB calculationB 
incrementTypeZ isSystemDefinedB 	resetTypeL calculationValuet 2Lnet/sf/jasperreports/engine/type/CalculationEnum;L 
expressionq ~ L incrementGroupq ~ "L incrementTypeValuet 4Lnet/sf/jasperreports/engine/type/IncrementTypeEnum;L incrementerFactoryClassNameq ~ L incrementerFactoryClassRealNameq ~ L initialValueExpressionq ~ L nameq ~ L 
resetGroupq ~ "L resetTypeValuet 0Lnet/sf/jasperreports/engine/type/ResetTypeEnum;L valueClassNameq ~ L valueClassRealNameq ~ xp  wî   ~r 0net.sf.jasperreports.engine.type.CalculationEnum          xq ~ 8t SYSTEMpp~r 2net.sf.jasperreports.engine.type.IncrementTypeEnum          xq ~ 8t NONEppsq ~ \    uq ~ _   sq ~ at new java.lang.Integer(1)q ~=pt PAGE_NUMBERp~r .net.sf.jasperreports.engine.type.ResetTypeEnum          xq ~ 8t REPORTq ~=psq ~¡  wî   q ~§ppq ~ªppsq ~ \   uq ~ _   sq ~ at new java.lang.Integer(1)q ~=pt 
COLUMN_NUMBERp~q ~±t PAGEq ~=psq ~¡  wî   ~q ~¦t COUNTsq ~ \   uq ~ _   sq ~ at new java.lang.Integer(1)q ~=ppq ~ªppsq ~ \   uq ~ _   sq ~ at new java.lang.Integer(0)q ~=pt REPORT_COUNTpq ~²q ~=psq ~¡  wî   q ~½sq ~ \   uq ~ _   sq ~ at new java.lang.Integer(1)q ~=ppq ~ªppsq ~ \   uq ~ _   sq ~ at new java.lang.Integer(0)q ~=pt 
PAGE_COUNTpq ~ºq ~=psq ~¡  wî   q ~½sq ~ \   uq ~ _   sq ~ at new java.lang.Integer(1)q ~=ppq ~ªppsq ~ \   uq ~ _   sq ~ at new java.lang.Integer(0)q ~=pt COLUMN_COUNTp~q ~±t COLUMNq ~=psq ~¡  wî    ~q ~¦t NOTHINGsq ~ \   uq ~ _   sq ~ at ( sq ~ at valorLancamentosq ~ at <0.0 ?  "SaÃ­da":"Entrada" )t java.lang.Stringppq ~ªpppt 
tipoDescricaopq ~²q ~ép~r <net.sf.jasperreports.engine.type.WhenResourceMissingTypeEnum          xq ~ 8t EMPTYq ~>p~r 0net.sf.jasperreports.engine.type.OrientationEnum          xq ~ 8t 	LANDSCAPEpp~r /net.sf.jasperreports.engine.type.PrintOrderEnum          xq ~ 8t VERTICALpppsq ~ sq ~    	w   	sr 1net.sf.jasperreports.engine.base.JRBaseStaticText      'Ø L textq ~ xq ~ &  wî           ]   R   pq ~ q ~ôpt staticText-2ppppq ~ 9ppppq ~ <  wîpppppppppq ~ nppppppppsq ~ Dpsq ~ H  wîppppq ~ùq ~ùq ~÷psq ~ O  wîppppq ~ùq ~ùpsq ~ I  wîppppq ~ùq ~ùpsq ~ R  wîppppq ~ùq ~ùpsq ~ T  wîppppq ~ùq ~ùpppppt Helvetica-Boldpppppppppppt DescriÃ§Ã£osq ~ö  wî           T   ¯   pq ~ q ~ôpt staticText-1ppppq ~ 9pppp~q ~ ;t RELATIVE_TO_TALLEST_OBJECT  wîpppppppppq ~ nppppppppsq ~ Dpsq ~ H  wîppppq ~q ~q ~psq ~ O  wîppppq ~q ~psq ~ I  wîppppq ~q ~psq ~ R  wîppppq ~q ~psq ~ T  wîppppq ~q ~pppppt Helvetica-Boldpppppppppppt Contrato/Produtosq ~ö  wî           "  F   pq ~ q ~ôpt staticText-8ppppq ~ 9ppppq ~ <  wîppppppppq ~ Bq ~ nppppppppsq ~ Dpsq ~ H  wîppppq ~q ~q ~
psq ~ O  wîppppq ~q ~psq ~ I  wîppppq ~q ~psq ~ R  wîppppq ~q ~psq ~ T  wîppppq ~q ~pppppt Helvetica-Boldppppppppppq ~ Wt Tiposq ~ö  wî           b     pq ~ q ~ôpt staticText-8ppppq ~ 9ppppq ~ <  wîpppppppppq ~ nppppppppsq ~ Dpsq ~ H  wîppppq ~q ~q ~psq ~ O  wîppppq ~q ~psq ~ I  wîppppq ~q ~psq ~ R  wîppppq ~q ~psq ~ T  wîppppq ~q ~pppppt Helvetica-Boldpppppppppppt Clientesq ~ö  wî           6  h   pq ~ q ~ôpt staticText-8ppppq ~ 9ppppq ~ <  wîppppppppq ~ Bq ~ nppppppppsq ~ Dpsq ~ H  wîppppq ~#q ~#q ~!psq ~ O  wîppppq ~#q ~#psq ~ I  wîppppq ~#q ~#psq ~ R  wîppppq ~#q ~#psq ~ T  wîppppq ~#q ~#pppppt Helvetica-Boldppppppppppq ~ Wt 	Valor(R$)sq ~ö  wî           y  e   pq ~ q ~ôpt staticText-8ppppq ~ 9sq ~ \   	uq ~ _   sq ~ at descricaoFormaPagamentosq ~ at  != nullq ~ppppq ~ <  wîpppppppppq ~ nppppppppsq ~ Dpsq ~ H  wîppppq ~3q ~3q ~+psq ~ O  wîppppq ~3q ~3psq ~ I  wîppppq ~3q ~3psq ~ R  wîppppq ~3q ~3psq ~ T  wîppppq ~3q ~3pppppt Helvetica-Boldppppppppppq ~ Wt Forma de Pagamentosq ~ö  wî           Q      pq ~ q ~ôppppppq ~ 9ppppq ~ <  wîpppppppppq ~ nppppppppsq ~ Dpsq ~ H  wîppppq ~<q ~<q ~;psq ~ O  wîppppq ~<q ~<psq ~ I  wîppppq ~<q ~<psq ~ R  wîppppq ~<q ~<psq ~ T  wîppppq ~<q ~<pppppt Helvetica-Boldpppppppppppt Plano de Contassq ~ !  wî           h  Þ   pq ~ q ~ôpt 	textFieldppppq ~ ppppq ~ <  wîppppppsq ~ >   
pq ~ Bq ~ nppppppppsq ~ Dpsq ~ H  wîppppq ~Gq ~Gq ~Dpsq ~ O  wîppppq ~Gq ~Gpsq ~ I  wîppppq ~Gq ~Gpsq ~ R  wîppppq ~Gq ~Gpsq ~ T  wîppppq ~Gq ~Gppppppppppppppppq ~ W  wî        ppq ~ Zsq ~ \   
uq ~ _   sq ~ at 
tituloDatat java.lang.Stringppppppq ~ nppq ~ sq ~ö  wî           b     pq ~ q ~ôpt staticText-8ppppq ~ 9ppppq ~ <  wîppppppppq ~ Bq ~ nppppppppsq ~ Dpsq ~ H  wîppppq ~Tq ~Tq ~Rpsq ~ O  wîppppq ~Tq ~Tpsq ~ I  wîppppq ~Tq ~Tpsq ~ R  wîppppq ~Tq ~Tpsq ~ T  wîppppq ~Tq ~Tpppppt Helvetica-Boldppppppppppq ~ Wt Empresaxp  wî   ppp~r 3net.sf.jasperreports.engine.type.WhenNoDataTypeEnum          xq ~ 8t NO_PAGESsr 6net.sf.jasperreports.engine.design.JRReportCompileData      'Ø L crosstabCompileDataq ~L datasetCompileDataq ~L mainDatasetCompileDataq ~ xpsq ~?@     w       xsq ~?@     w       xur [B¬óøTà  xp  =Êþº¾   .  4DemonstrativoFinanceiroRelExcel_1746537051984_610493  ,net/sf/jasperreports/engine/fill/JREvaluator  parameter_REPORT_LOCALE 2Lnet/sf/jasperreports/engine/fill/JRFillParameter; parameter_tituloData parameter_JASPER_REPORT parameter_REPORT_VIRTUALIZER parameter_REPORT_TIME_ZONE parameter_REPORT_FILE_RESOLVER parameter_planoFilho parameter_REPORT_SCRIPTLET parameter_tipoRelatorioDF parameter_REPORT_PARAMETERS_MAP parameter_REPORT_CONNECTION parameter_REPORT_CLASS_LOADER parameter_REPORT_DATA_SOURCE $parameter_REPORT_URL_HANDLER_FACTORY parameter_IS_IGNORE_PAGINATION parameter_REPORT_FORMAT_FACTORY parameter_REPORT_MAX_COUNT parameter_REPORT_TEMPLATES  parameter_REPORT_RESOURCE_BUNDLE field_nomePessoa .Lnet/sf/jasperreports/engine/fill/JRFillField; $field_valorLancamentoApresentarExcel field_valorLancamento field_dataApresentar field_movPagamento field_dataMesApresentar field_tipoES46descricao field_contrato field_movProduto field_tipoFormaPagto46descricao field_descricaoFormaPagamento field_empresaApresentar field_codProduto field_descricaoLancamento variable_PAGE_NUMBER 1Lnet/sf/jasperreports/engine/fill/JRFillVariable; variable_COLUMN_NUMBER variable_REPORT_COUNT variable_PAGE_COUNT variable_COLUMN_COUNT variable_tipoDescricao <init> ()V Code / 0
  2  	  4  	  6  	  8 	 	  : 
 	  <  	  >  	  @ 
 	  B  	  D  	  F  	  H  	  J  	  L  	  N  	  P  	  R  	  T  	  V  	  X  	  Z  	  \  	  ^  	  `  	  b  	  d   	  f ! 	  h " 	  j # 	  l $ 	  n % 	  p & 	  r ' 	  t ( )	  v * )	  x + )	  z , )	  | - )	  ~ . )	   LineNumberTable customizedInit 0(Ljava/util/Map;Ljava/util/Map;Ljava/util/Map;)V 
initParams (Ljava/util/Map;)V  
   
initFields  
   initVars  
   
REPORT_LOCALE  
java/util/Map  get &(Ljava/lang/Object;)Ljava/lang/Object;     0net/sf/jasperreports/engine/fill/JRFillParameter  
tituloData  
JASPER_REPORT  REPORT_VIRTUALIZER  REPORT_TIME_ZONE  REPORT_FILE_RESOLVER ¡ 
planoFilho £ REPORT_SCRIPTLET ¥ tipoRelatorioDF § REPORT_PARAMETERS_MAP © REPORT_CONNECTION « REPORT_CLASS_LOADER ­ REPORT_DATA_SOURCE ¯ REPORT_URL_HANDLER_FACTORY ± IS_IGNORE_PAGINATION ³ REPORT_FORMAT_FACTORY µ REPORT_MAX_COUNT · REPORT_TEMPLATES ¹ REPORT_RESOURCE_BUNDLE » 
nomePessoa ½ ,net/sf/jasperreports/engine/fill/JRFillField ¿ valorLancamentoApresentarExcel Á valorLancamento Ã dataApresentar Å movPagamento Ç dataMesApresentar É tipoES.descricao Ë contrato Í 
movProduto Ï tipoFormaPagto.descricao Ñ descricaoFormaPagamento Ó empresaApresentar Õ 
codProduto × descricaoLancamento Ù PAGE_NUMBER Û /net/sf/jasperreports/engine/fill/JRFillVariable Ý 
COLUMN_NUMBER ß REPORT_COUNT á 
PAGE_COUNT ã COLUMN_COUNT å 
tipoDescricao ç evaluate (I)Ljava/lang/Object; 
Exceptions java/lang/Throwable ì java/lang/Integer î (I)V / ð
 ï ñ getValue ()Ljava/lang/Object; ó ô
 À õ java/lang/Double ÷ doubleValue ()D ù ú
 ø û SaÃ­da ý Entrada ÿ java/lang/String java/lang/Boolean valueOf (Z)Ljava/lang/Boolean;

  õ intValue ()I

 ï
 Þ õ (I)Ljava/lang/Integer;
 ï equals (Ljava/lang/Object;)Z
 ï evaluateOld getOldValue ô
 À
 Þ evaluateEstimated getEstimatedValue ô
 Þ 
SourceFile !     '                 	     
               
                                                                                                !     "     #     $     %     &     '     ( )    * )    + )    , )    - )    . )     / 0  1       È*· 3*µ 5*µ 7*µ 9*µ ;*µ =*µ ?*µ A*µ C*µ E*µ G*µ I*µ K*µ M*µ O*µ Q*µ S*µ U*µ W*µ Y*µ [*µ ]*µ _*µ a*µ c*µ e*µ g*µ i*µ k*µ m*µ o*µ q*µ s*µ u*µ w*µ y*µ {*µ }*µ *µ ±       ¦ )      	          ! " " ' # , $ 1 % 6 & ; ' @ ( E ) J * O + T , Y - ^ . c / h 0 m 1 r 2 w 3 | 4  5  6  7  8  9  :  ; ¤ < © = ® > ³ ? ¸ @ ½ A Â B Ç      1   4     *+· *,· *-· ±           N  O 
 P  Q     1  »    W*+¹  À À µ 5*+¹  À À µ 7*+¹  À À µ 9*+¹  À À µ ;*+ ¹  À À µ =*+¢¹  À À µ ?*+¤¹  À À µ A*+¦¹  À À µ C*+¨¹  À À µ E*+ª¹  À À µ G*+¬¹  À À µ I*+®¹  À À µ K*+°¹  À À µ M*+²¹  À À µ O*+´¹  À À µ Q*+¶¹  À À µ S*+¸¹  À À µ U*+º¹  À À µ W*+¼¹  À À µ Y±       R    Y  Z $ [ 6 \ H ] Z ^ l _ ~ `  a ¢ b ´ c Æ d Ø e ê f ü g h  i2 jD kV l     1  M     ý*+¾¹  À ÀÀ Àµ [*+Â¹  À ÀÀ Àµ ]*+Ä¹  À ÀÀ Àµ _*+Æ¹  À ÀÀ Àµ a*+È¹  À ÀÀ Àµ c*+Ê¹  À ÀÀ Àµ e*+Ì¹  À ÀÀ Àµ g*+Î¹  À ÀÀ Àµ i*+Ð¹  À ÀÀ Àµ k*+Ò¹  À ÀÀ Àµ m*+Ô¹  À ÀÀ Àµ o*+Ö¹  À ÀÀ Àµ q*+Ø¹  À ÀÀ Àµ s*+Ú¹  À ÀÀ Àµ u±       >    t  u $ v 6 w H x Z y l z ~ {  | ¢ } ´ ~ Æ  Ø  ê  ü      1        m*+Ü¹  À ÞÀ Þµ w*+à¹  À ÞÀ Þµ y*+â¹  À ÞÀ Þµ {*+ä¹  À ÞÀ Þµ }*+æ¹  À ÞÀ Þµ *+è¹  À ÞÀ Þµ ±              $  6  H  Z  l   é ê  ë     í 1  ±    éMª  ä          ]   i   u            ¥   ±   ½   Û   ô    -  ;  I  W  e  s    Ù» ïY· òM§~» ïY· òM§r» ïY· òM§f» ïY· òM§Z» ïY· òM§N» ïY· òM§B» ïY· òM§6» ïY· òM§**´ _¶ öÀ ø¶ ü þ§  M§*´ o¶ öÀÆ § ¸M§ ó*´ 7¶	ÀM§ å*´ i¶ öÀ ï¶
 *´ i¶ öÀ ï§ 
*´ s¶ öÀ ïM§ º*´ u¶ öÀM§ ¬*´ ]¶ öÀM§ *´ ¶ÀM§ *´ o¶ öÀM§ *´ [¶ öÀM§ t*´ A¶	ÀM§ f*´ E¶	À ï¸¶ ,*´ E¶	À ï¸¶ *´ E¶	À ï¸¶ *´ e¶ öÀ§ 
*´ a¶ öÀM§ *´ q¶ öÀM,°       ¶ -      `  i  l £ u ¤ x ¨  ©  ­  ®  ²  ³  · ¥ ¸ ¨ ¼ ± ½ ´ Á ½ Â À Æ Û Ç Þ Ë ô Ì ÷ Ð Ñ Õ- Ö0 Ú; Û> ßI àL äW åZ ée êh îs ïv ó ô ø ù¬ úØ øÙ ûÜ ÿç  ê  ë     í 1  ±    éMª  ä          ]   i   u            ¥   ±   ½   Û   ô    -  ;  I  W  e  s    Ù» ïY· òM§~» ïY· òM§r» ïY· òM§f» ïY· òM§Z» ïY· òM§N» ïY· òM§B» ïY· òM§6» ïY· òM§**´ _¶À ø¶ ü þ§  M§*´ o¶ÀÆ § ¸M§ ó*´ 7¶	ÀM§ å*´ i¶À ï¶
 *´ i¶À ï§ 
*´ s¶À ïM§ º*´ u¶ÀM§ ¬*´ ]¶ÀM§ *´ ¶ÀM§ *´ o¶ÀM§ *´ [¶ÀM§ t*´ A¶	ÀM§ f*´ E¶	À ï¸¶ ,*´ E¶	À ï¸¶ *´ E¶	À ï¸¶ *´ e¶À§ 
*´ a¶ÀM§ *´ q¶ÀM,°       ¶ -    ` i l u x  ! % & * + / ¥0 ¨4 ±5 ´9 ½: À> Û? ÞC ôD ÷HIM-N0R;S>WIXL\W]Zaebhfsgvklpq¬rØpÙsÜwç  ê  ë     í 1  ±    éMª  ä          ]   i   u            ¥   ±   ½   Û   ô    -  ;  I  W  e  s    Ù» ïY· òM§~» ïY· òM§r» ïY· òM§f» ïY· òM§Z» ïY· òM§N» ïY· òM§B» ïY· òM§6» ïY· òM§**´ _¶ öÀ ø¶ ü þ§  M§*´ o¶ öÀÆ § ¸M§ ó*´ 7¶	ÀM§ å*´ i¶ öÀ ï¶
 *´ i¶ öÀ ï§ 
*´ s¶ öÀ ïM§ º*´ u¶ öÀM§ ¬*´ ]¶ öÀM§ *´ ¶ÀM§ *´ o¶ öÀM§ *´ [¶ öÀM§ t*´ A¶	ÀM§ f*´ E¶	À ï¸¶ ,*´ E¶	À ï¸¶ *´ E¶	À ï¸¶ *´ e¶ öÀ§ 
*´ a¶ öÀM§ *´ q¶ öÀM,°       ¶ -    ` i l u x    ¢ £ § ¥¨ ¨¬ ±­ ´± ½² À¶ Û· Þ» ô¼ ÷ÀÁÅ-Æ0Ê;Ë>ÏIÐLÔWÕZÙeÚhÞsßvãäèé¬êØèÙëÜïç÷     t _1746537051984_610493t 2net.sf.jasperreports.engine.design.JRJavacCompiler
¬í sr (net.sf.jasperreports.engine.JasperReport      'Ø L compileDatat Ljava/io/Serializable;L compileNameSuffixt Ljava/lang/String;L 
compilerClassq ~ xr -net.sf.jasperreports.engine.base.JRBaseReport      'Ø +I PSEUDO_SERIAL_VERSION_UIDI bottomMarginI columnCountI 
columnSpacingI columnWidthZ ignorePaginationZ isFloatColumnFooterZ isSummaryNewPageZ  isSummaryWithPageHeaderAndFooterZ isTitleNewPageI 
leftMarginB orientationI 
pageHeightI 	pageWidthB 
printOrderI rightMarginI 	topMarginB whenNoDataTypeL 
backgroundt $Lnet/sf/jasperreports/engine/JRBand;L columnFooterq ~ L columnHeaderq ~ [ datasetst ([Lnet/sf/jasperreports/engine/JRDataset;L defaultFontt *Lnet/sf/jasperreports/engine/JRReportFont;L defaultStylet %Lnet/sf/jasperreports/engine/JRStyle;L detailq ~ L 
detailSectiont 'Lnet/sf/jasperreports/engine/JRSection;[ fontst +[Lnet/sf/jasperreports/engine/JRReportFont;L formatFactoryClassq ~ L 
importsSett Ljava/util/Set;L languageq ~ L lastPageFooterq ~ L mainDatasett 'Lnet/sf/jasperreports/engine/JRDataset;L nameq ~ L noDataq ~ L orientationValuet 2Lnet/sf/jasperreports/engine/type/OrientationEnum;L 
pageFooterq ~ L 
pageHeaderq ~ L printOrderValuet 1Lnet/sf/jasperreports/engine/type/PrintOrderEnum;[ stylest &[Lnet/sf/jasperreports/engine/JRStyle;L summaryq ~ [ 	templatest /[Lnet/sf/jasperreports/engine/JRReportTemplate;L titleq ~ L whenNoDataTypeValuet 5Lnet/sf/jasperreports/engine/type/WhenNoDataTypeEnum;xp  wî   
         "           S  J       
 sr +net.sf.jasperreports.engine.base.JRBaseBand      'Ø I PSEUDO_SERIAL_VERSION_UIDI heightZ isSplitAllowedL printWhenExpressiont *Lnet/sf/jasperreports/engine/JRExpression;L 	splitTypet Ljava/lang/Byte;L splitTypeValuet 0Lnet/sf/jasperreports/engine/type/SplitTypeEnum;xr 3net.sf.jasperreports.engine.base.JRBaseElementGroup      'Ø L childrent Ljava/util/List;L elementGroupt ,Lnet/sf/jasperreports/engine/JRElementGroup;xpsr java.util.ArrayListxÒÇa I sizexp    w    xp  wî    pp~r .net.sf.jasperreports.engine.type.SplitTypeEnum          xr java.lang.Enum          xpt STRETCHsq ~ sq ~     w    xp  wî    ppq ~ sq ~ sq ~    w   sr 1net.sf.jasperreports.engine.base.JRBaseStaticText      'Ø L textq ~ xr 2net.sf.jasperreports.engine.base.JRBaseTextElement      'Ø %I PSEUDO_SERIAL_VERSION_UIDL borderq ~ L borderColort Ljava/awt/Color;L bottomBorderq ~ L bottomBorderColorq ~ &L 
bottomPaddingt Ljava/lang/Integer;L fontNameq ~ L fontSizeq ~ 'L horizontalAlignmentq ~ L horizontalAlignmentValuet 6Lnet/sf/jasperreports/engine/type/HorizontalAlignEnum;L isBoldt Ljava/lang/Boolean;L isItalicq ~ )L 
isPdfEmbeddedq ~ )L isStrikeThroughq ~ )L isStyledTextq ~ )L isUnderlineq ~ )L 
leftBorderq ~ L leftBorderColorq ~ &L leftPaddingq ~ 'L lineBoxt 'Lnet/sf/jasperreports/engine/JRLineBox;L lineSpacingq ~ L lineSpacingValuet 2Lnet/sf/jasperreports/engine/type/LineSpacingEnum;L markupq ~ L paddingq ~ 'L pdfEncodingq ~ L pdfFontNameq ~ L 
reportFontq ~ L rightBorderq ~ L rightBorderColorq ~ &L rightPaddingq ~ 'L rotationq ~ L 
rotationValuet /Lnet/sf/jasperreports/engine/type/RotationEnum;L 	topBorderq ~ L topBorderColorq ~ &L 
topPaddingq ~ 'L verticalAlignmentq ~ L verticalAlignmentValuet 4Lnet/sf/jasperreports/engine/type/VerticalAlignEnum;xr .net.sf.jasperreports.engine.base.JRBaseElement      'Ø I PSEUDO_SERIAL_VERSION_UIDI heightZ isPrintInFirstWholeBandZ isPrintRepeatedValuesZ isPrintWhenDetailOverflowsZ isRemoveLineWhenBlankB positionTypeB stretchTypeI widthI xI yL 	backcolorq ~ &L defaultStyleProvidert 4Lnet/sf/jasperreports/engine/JRDefaultStyleProvider;L elementGroupq ~ L 	forecolorq ~ &L keyq ~ L modeq ~ L 	modeValuet +Lnet/sf/jasperreports/engine/type/ModeEnum;L parentStyleq ~ L parentStyleNameReferenceq ~ L positionTypeValuet 3Lnet/sf/jasperreports/engine/type/PositionTypeEnum;L printWhenExpressionq ~ L printWhenGroupChangest %Lnet/sf/jasperreports/engine/JRGroup;L 
propertiesMapt -Lnet/sf/jasperreports/engine/JRPropertiesMap;[ propertyExpressionst 3[Lnet/sf/jasperreports/engine/JRPropertyExpression;L stretchTypeValuet 2Lnet/sf/jasperreports/engine/type/StretchTypeEnum;xp  wî          c   >   pq ~ q ~ "pt staticText-1pppp~r 1net.sf.jasperreports.engine.type.PositionTypeEnum          xq ~ t FLOATsr 1net.sf.jasperreports.engine.base.JRBaseExpression      'Ø I id[ chunkst 0[Lnet/sf/jasperreports/engine/JRExpressionChunk;L valueClassNameq ~ L valueClassRealNameq ~ xp   ur 0[Lnet.sf.jasperreports.engine.JRExpressionChunk;mYÏÞiK£U  xp   sr 6net.sf.jasperreports.engine.base.JRBaseExpressionChunk      'Ø B typeL textq ~ xpt dataHorasq ~ @t  != null || sq ~ @t pessoaApresentarsq ~ @t  != null || sq ~ @t localAcesso.descricaosq ~ @t  != nullt java.lang.Booleanpppp~r 0net.sf.jasperreports.engine.type.StretchTypeEnum          xq ~ t 
NO_STRETCH  wîppppppsr java.lang.Integerâ ¤÷8 I valuexr java.lang.Number¬à  xp   
p~r 4net.sf.jasperreports.engine.type.HorizontalAlignEnum          xq ~ t CENTERsr java.lang.BooleanÍ rÕúî Z valuexpppppppppsr .net.sf.jasperreports.engine.base.JRBaseLineBox      'Ø L 
bottomPaddingq ~ 'L 	bottomPent +Lnet/sf/jasperreports/engine/base/JRBoxPen;L boxContainert ,Lnet/sf/jasperreports/engine/JRBoxContainer;L leftPaddingq ~ 'L leftPenq ~ ZL paddingq ~ 'L penq ~ ZL rightPaddingq ~ 'L rightPenq ~ ZL 
topPaddingq ~ 'L topPenq ~ Zxppsr 3net.sf.jasperreports.engine.base.JRBaseBoxBottomPen      'Ø  xr -net.sf.jasperreports.engine.base.JRBaseBoxPen      'Ø L lineBoxq ~ *xr *net.sf.jasperreports.engine.base.JRBasePen      'Ø I PSEUDO_SERIAL_VERSION_UIDL 	lineColorq ~ &L 	lineStyleq ~ L lineStyleValuet 0Lnet/sf/jasperreports/engine/type/LineStyleEnum;L 	lineWidtht Ljava/lang/Float;L penContainert ,Lnet/sf/jasperreports/engine/JRPenContainer;xp  wîppppq ~ \q ~ \q ~ 6psr 1net.sf.jasperreports.engine.base.JRBaseBoxLeftPen      'Ø  xq ~ ^  wîppppq ~ \q ~ \psq ~ ^  wîppppq ~ \q ~ \psr 2net.sf.jasperreports.engine.base.JRBaseBoxRightPen      'Ø  xq ~ ^  wîppppq ~ \q ~ \psr 0net.sf.jasperreports.engine.base.JRBaseBoxTopPen      'Ø  xq ~ ^  wîppppq ~ \q ~ \pppppt Helvetica-Boldpppppppppppt Local de Acessosq ~ $  wî          [  Ä   pq ~ q ~ "pt staticText-1ppppq ~ 9sq ~ ;   uq ~ >   sq ~ @t dataHorasq ~ @t  != null || sq ~ @t pessoaApresentarsq ~ @t  != null || sq ~ @t localAcesso.descricaosq ~ @t  != nullq ~ Mppppq ~ O  wîppppppq ~ Spq ~ Uq ~ Xppppppppsq ~ Ypsq ~ ]  wîppppq ~ }q ~ }q ~ mpsq ~ d  wîppppq ~ }q ~ }psq ~ ^  wîppppq ~ }q ~ }psq ~ g  wîppppq ~ }q ~ }psq ~ i  wîppppq ~ }q ~ }pppppt Helvetica-Boldpppppppppppt UsuÃ¡rio Justificousr +net.sf.jasperreports.engine.base.JRBaseLine      'Ø I PSEUDO_SERIAL_VERSION_UIDB 	directionL directionValuet 4Lnet/sf/jasperreports/engine/type/LineDirectionEnum;xr 5net.sf.jasperreports.engine.base.JRBaseGraphicElement      'Ø I PSEUDO_SERIAL_VERSION_UIDL fillq ~ L 	fillValuet +Lnet/sf/jasperreports/engine/type/FillEnum;L linePent #Lnet/sf/jasperreports/engine/JRPen;L penq ~ xq ~ .  wî                +pq ~ q ~ "ppppppq ~ 9ppppq ~ O  wîppsq ~ _  wîppppq ~ p  wî ~r 2net.sf.jasperreports.engine.type.LineDirectionEnum          xq ~ t TOP_DOWNsq ~ $  wî          Z  j   pq ~ q ~ "pt staticText-1ppppq ~ 9sq ~ ;   uq ~ >   sq ~ @t dataHorasq ~ @t  != null || sq ~ @t pessoaApresentarsq ~ @t  != null || sq ~ @t localAcesso.descricaosq ~ @t  != nullq ~ Mppppq ~ O  wîppppppq ~ Spq ~ Uq ~ Xppppppppsq ~ Ypsq ~ ]  wîppppq ~ q ~ q ~ psq ~ d  wîppppq ~ q ~ psq ~ ^  wîppppq ~ q ~ psq ~ g  wîppppq ~ q ~ psq ~ i  wîppppq ~ q ~ pppppt Helvetica-Boldpppppppppppt Dt. Justificativasq ~   wî                pq ~ q ~ "ppppppq ~ 9ppppq ~ O  wîppsq ~ _  wîppppq ~ §p  wî q ~ sq ~ $  wî          =       pq ~ q ~ "pt staticText-1ppppq ~ 9sq ~ ;   uq ~ >   sq ~ @t dataHorasq ~ @t  != null || sq ~ @t pessoaApresentarsq ~ @t  != null || sq ~ @t localAcesso.descricaosq ~ @t  != nullq ~ Mppppq ~ O  wîppppppq ~ Spq ~ Uq ~ Xppppppppsq ~ Ypsq ~ ]  wîppppq ~ ¹q ~ ¹q ~ ©psq ~ d  wîppppq ~ ¹q ~ ¹psq ~ ^  wîppppq ~ ¹q ~ ¹psq ~ g  wîppppq ~ ¹q ~ ¹psq ~ i  wîppppq ~ ¹q ~ ¹pppppt Helvetica-Boldpppppppppppt Data Acessosq ~ $  wî          6  p   pq ~ q ~ "pt staticText-1ppppq ~ 9sq ~ ;   uq ~ >   sq ~ @t dataHorasq ~ @t  != null || sq ~ @t pessoaApresentarsq ~ @t  != null || sq ~ @t localAcesso.descricaosq ~ @t  != nullq ~ Mppppq ~ O  wîppppppq ~ Spq ~ Uq ~ Xppppppppsq ~ Ypsq ~ ]  wîppppq ~ Ñq ~ Ñq ~ Ápsq ~ d  wîppppq ~ Ñq ~ Ñpsq ~ ^  wîppppq ~ Ñq ~ Ñpsq ~ g  wîppppq ~ Ñq ~ Ñpsq ~ i  wîppppq ~ Ñq ~ Ñpppppt Helvetica-Boldpppppppppppt Pessoasq ~ $  wî          _     pq ~ q ~ "pt staticText-1ppppq ~ 9sq ~ ;   uq ~ >   sq ~ @t dataHorasq ~ @t  != null || sq ~ @t pessoaApresentarsq ~ @t  != null || sq ~ @t localAcesso.descricaosq ~ @t  != nullq ~ Mppppq ~ O  wîppppppq ~ Spq ~ Uq ~ Xppppppppsq ~ Ypsq ~ ]  wîppppq ~ éq ~ éq ~ Ùpsq ~ d  wîppppq ~ éq ~ épsq ~ ^  wîppppq ~ éq ~ épsq ~ g  wîppppq ~ éq ~ épsq ~ i  wîppppq ~ éq ~ épppppt Helvetica-Boldpppppppppppt UsuÃ¡rio Liberousq ~ $  wî          p   ¡   pq ~ q ~ "pt staticText-1ppppq ~ 9sq ~ ;   uq ~ >   sq ~ @t dataHorasq ~ @t  != null || sq ~ @t pessoaApresentarsq ~ @t  != null || sq ~ @t localAcesso.descricaosq ~ @t  != nullq ~ Mppppq ~ O  wîppppppq ~ Spq ~ Uq ~ Xppppppppsq ~ Ypsq ~ ]  wîppppq ~q ~q ~ ñpsq ~ d  wîppppq ~q ~psq ~ ^  wîppppq ~q ~psq ~ g  wîppppq ~q ~psq ~ i  wîppppq ~q ~pppppt Helvetica-Boldpppppppppppt Tipo de LiberaÃ§Ã£osq ~ $  wî          Ä  ¦   pq ~ q ~ "pt staticText-1ppppq ~ 9sq ~ ;   uq ~ >   sq ~ @t dataHorasq ~ @t  != null || sq ~ @t pessoaApresentarsq ~ @t  != null || sq ~ @t localAcesso.descricaosq ~ @t  != nullq ~ Mppppq ~ O  wîppppppq ~ Spq ~ Uq ~ Xppppppppsq ~ Ypsq ~ ]  wîppppq ~q ~q ~	psq ~ d  wîppppq ~q ~psq ~ ^  wîppppq ~q ~psq ~ g  wîppppq ~q ~psq ~ i  wîppppq ~q ~pppppt Helvetica-Boldpppppppppppt 
Justificativasr 0net.sf.jasperreports.engine.base.JRBaseSubreport      'Ø L connectionExpressionq ~ L dataSourceExpressionq ~ L 
expressionq ~ L isUsingCacheq ~ )[ 
parameterst 3[Lnet/sf/jasperreports/engine/JRSubreportParameter;L parametersMapExpressionq ~ [ returnValuest 5[Lnet/sf/jasperreports/engine/JRSubreportReturnValue;L runToBottomq ~ )xq ~ .  wî          3ÿÿÿï   pq ~ q ~ "pt 
subreport1pppp~q ~ 8t FIX_RELATIVE_TO_TOPppppq ~ Opsq ~ ;   %uq ~ >   sq ~ @t listaFechamentot (net.sf.jasperreports.engine.JRDataSourcepsq ~ ;   &uq ~ >   sq ~ @t 
SUBREPORT_DIRsq ~ @t 4 + "FechamentoControleAcessoDWRel_fechamento.jasper"t java.lang.Stringpq ~ Xur 3[Lnet.sf.jasperreports.engine.JRSubreportParameter;[£À¾B  xp   
sr 9net.sf.jasperreports.engine.base.JRBaseSubreportParameter      'Ø  xr 7net.sf.jasperreports.engine.base.JRBaseDatasetParameter      'Ø L 
expressionq ~ L nameq ~ xpsq ~ ;   uq ~ >   sq ~ @t 
percentualColt java.lang.Objectpt 
percentualColsq ~6sq ~ ;   uq ~ >   sq ~ @t 
SUBREPORT_DIRq ~=pt 
SUBREPORT_DIRsq ~6sq ~ ;   uq ~ >   sq ~ @t percentualCliColq ~=pt percentualCliColsq ~6sq ~ ;   uq ~ >   sq ~ @t totalColq ~=pt totalColsq ~6sq ~ ;   uq ~ >   sq ~ @t totalAcessoLibq ~=pt totalAcessoLibsq ~6sq ~ ;   uq ~ >   sq ~ @t listaFechamentoq ~=pt listaFechamentosq ~6sq ~ ;   uq ~ >   sq ~ @t totalAcessosq ~=pt totalAcessossq ~6sq ~ ;   uq ~ >   sq ~ @t qtdBVsq ~=pt qtdBVssq ~6sq ~ ;    uq ~ >   sq ~ @t 
percentualCliq ~=pt 
percentualClisq ~6sq ~ ;   !uq ~ >   sq ~ @t totalJustificadoLibq ~=pt totalJustificadoLibsq ~6sq ~ ;   "uq ~ >   sq ~ @t percentualLiberacaoq ~=pt percentualLiberacaosq ~6sq ~ ;   #uq ~ >   sq ~ @t totalCliq ~=pt totalClisq ~6sq ~ ;   $uq ~ >   sq ~ @t totalFaltaJustificarLibq ~=pt totalFaltaJustificarLibpppsq ~ $  wî         !       pq ~ q ~ "pq ~ ªppppq ~ 9sq ~ ;   'uq ~ >   sq ~ @t dataHorasq ~ @t  == null && sq ~ @t pessoaApresentarsq ~ @t  == null && sq ~ @t localAcesso.descricaosq ~ @t  == nullq ~ Mppppq ~ O  wîppppppq ~ Spq ~ Uq ~ Xppppppppsq ~ Ypsq ~ ]  wîppppq ~q ~q ~psq ~ d  wîppppq ~q ~psq ~ ^  wîppppq ~q ~psq ~ g  wîppppq ~q ~psq ~ i  wîppppq ~q ~pppppq ~ ¿pppppppppppt Nenhum Registro Encontrado!xp  wî   -ppq ~ ur ([Lnet.sf.jasperreports.engine.JRDataset;L6Í¬D  xp   sr .net.sf.jasperreports.engine.base.JRBaseDataset      'Ø I PSEUDO_SERIAL_VERSION_UIDZ isMainB whenResourceMissingType[ fieldst &[Lnet/sf/jasperreports/engine/JRField;L filterExpressionq ~ [ groupst &[Lnet/sf/jasperreports/engine/JRGroup;L nameq ~ [ 
parameterst *[Lnet/sf/jasperreports/engine/JRParameter;L 
propertiesMapq ~ 3L queryt %Lnet/sf/jasperreports/engine/JRQuery;L resourceBundleq ~ L scriptletClassq ~ [ 
scriptletst *[Lnet/sf/jasperreports/engine/JRScriptlet;[ 
sortFieldst *[Lnet/sf/jasperreports/engine/JRSortField;[ 	variablest )[Lnet/sf/jasperreports/engine/JRVariable;L whenResourceMissingTypeValuet >Lnet/sf/jasperreports/engine/type/WhenResourceMissingTypeEnum;xp  wî  pppt Testeur *[Lnet.sf.jasperreports.engine.JRParameter;" *Ã`!  xp   sr 0net.sf.jasperreports.engine.base.JRBaseParameter      'Ø 	Z isForPromptingZ isSystemDefinedL defaultValueExpressionq ~ L descriptionq ~ L nameq ~ L nestedTypeNameq ~ L 
propertiesMapq ~ 3L valueClassNameq ~ L valueClassRealNameq ~ xpppt REPORT_PARAMETERS_MAPpsr +net.sf.jasperreports.engine.JRPropertiesMap      'Ø L baseq ~ 3L propertiesListq ~ L 
propertiesMapt Ljava/util/Map;xppppt 
java.util.Mappsq ~¬ppt 
JASPER_REPORTpsq ~¯pppt (net.sf.jasperreports.engine.JasperReportpsq ~¬ppt REPORT_CONNECTIONpsq ~¯pppt java.sql.Connectionpsq ~¬ppt REPORT_MAX_COUNTpsq ~¯pppt java.lang.Integerpsq ~¬ppt REPORT_DATA_SOURCEpsq ~¯pppq ~,psq ~¬ppt REPORT_SCRIPTLETpsq ~¯pppt /net.sf.jasperreports.engine.JRAbstractScriptletpsq ~¬ppt 
REPORT_LOCALEpsq ~¯pppt java.util.Localepsq ~¬ppt REPORT_RESOURCE_BUNDLEpsq ~¯pppt java.util.ResourceBundlepsq ~¬ppt REPORT_TIME_ZONEpsq ~¯pppt java.util.TimeZonepsq ~¬ppt REPORT_FORMAT_FACTORYpsq ~¯pppt .net.sf.jasperreports.engine.util.FormatFactorypsq ~¬ppt REPORT_CLASS_LOADERpsq ~¯pppt java.lang.ClassLoaderpsq ~¬ppt REPORT_URL_HANDLER_FACTORYpsq ~¯pppt  java.net.URLStreamHandlerFactorypsq ~¬ppt REPORT_FILE_RESOLVERpsq ~¯pppt -net.sf.jasperreports.engine.util.FileResolverpsq ~¬ppt REPORT_TEMPLATESpsq ~¯pppt java.util.Collectionpsq ~¯ppppppppur )[Lnet.sf.jasperreports.engine.JRVariable;bæ|,·D  xp   sr /net.sf.jasperreports.engine.base.JRBaseVariable      'Ø I PSEUDO_SERIAL_VERSION_UIDB calculationB 
incrementTypeZ isSystemDefinedB 	resetTypeL calculationValuet 2Lnet/sf/jasperreports/engine/type/CalculationEnum;L 
expressionq ~ L incrementGroupq ~ 2L incrementTypeValuet 4Lnet/sf/jasperreports/engine/type/IncrementTypeEnum;L incrementerFactoryClassNameq ~ L incrementerFactoryClassRealNameq ~ L initialValueExpressionq ~ L nameq ~ L 
resetGroupq ~ 2L resetTypeValuet 0Lnet/sf/jasperreports/engine/type/ResetTypeEnum;L valueClassNameq ~ L valueClassRealNameq ~ xp  wî   ~r 0net.sf.jasperreports.engine.type.CalculationEnum          xq ~ t SYSTEMpp~r 2net.sf.jasperreports.engine.type.IncrementTypeEnum          xq ~ t NONEppsq ~ ;    uq ~ >   sq ~ @t new java.lang.Integer(1)q ~¾pt PAGE_NUMBERp~r .net.sf.jasperreports.engine.type.ResetTypeEnum          xq ~ t REPORTq ~¾psq ~é  wî   q ~ïppq ~òppsq ~ ;   uq ~ >   sq ~ @t new java.lang.Integer(1)q ~¾pt 
COLUMN_NUMBERp~q ~ùt PAGEq ~¾psq ~é  wî   ~q ~ît COUNTsq ~ ;   uq ~ >   sq ~ @t new java.lang.Integer(1)q ~¾ppq ~òppsq ~ ;   uq ~ >   sq ~ @t new java.lang.Integer(0)q ~¾pt REPORT_COUNTpq ~úq ~¾psq ~é  wî   q ~sq ~ ;   uq ~ >   sq ~ @t new java.lang.Integer(1)q ~¾ppq ~òppsq ~ ;   uq ~ >   sq ~ @t new java.lang.Integer(0)q ~¾pt 
PAGE_COUNTpq ~q ~¾psq ~é  wî   q ~sq ~ ;   uq ~ >   sq ~ @t new java.lang.Integer(1)q ~¾ppq ~òppsq ~ ;   uq ~ >   sq ~ @t new java.lang.Integer(0)q ~¾pt COLUMN_COUNTp~q ~ùt COLUMNq ~¾p~r <net.sf.jasperreports.engine.type.WhenResourceMissingTypeEnum          xq ~ t NULLpppsr .net.sf.jasperreports.engine.base.JRBaseSection      'Ø [ bandst %[Lnet/sf/jasperreports/engine/JRBand;xpur %[Lnet.sf.jasperreports.engine.JRBand;Ý~ìÊ5  xp   sq ~ sq ~    	w   	sr 0net.sf.jasperreports.engine.base.JRBaseRectangle      'Ø L radiusq ~ 'xq ~   wî                 sr java.awt.Color¥3u F falphaI valueL cst Ljava/awt/color/ColorSpace;[ 	frgbvaluet [F[ fvalueq ~4xp    ÿ´ÍÍpppq ~ q ~.sq ~2    ÿ´ÍÍppppp~r )net.sf.jasperreports.engine.type.ModeEnum          xq ~ t OPAQUEppq ~&sq ~ ;   (uq ~ >   sq ~ @t 
new Boolean((sq ~ @t COLUMN_COUNTsq ~ @t .intValue()%2)==0)q ~ Mpppp~q ~ Nt RELATIVE_TO_BAND_HEIGHT  wîppsq ~ _  wîppppq ~1ppsr 0net.sf.jasperreports.engine.base.JRBaseTextField      'Ø I PSEUDO_SERIAL_VERSION_UIDI 
bookmarkLevelB evaluationTimeB hyperlinkTargetB 
hyperlinkTypeZ isStretchWithOverflowL anchorNameExpressionq ~ L evaluationGroupq ~ 2L evaluationTimeValuet 5Lnet/sf/jasperreports/engine/type/EvaluationTimeEnum;L 
expressionq ~ L hyperlinkAnchorExpressionq ~ L hyperlinkPageExpressionq ~ [ hyperlinkParameterst 3[Lnet/sf/jasperreports/engine/JRHyperlinkParameter;L hyperlinkReferenceExpressionq ~ L hyperlinkTooltipExpressionq ~ L isBlankWhenNullq ~ )L 
linkTargetq ~ L linkTypeq ~ L patternq ~ xq ~ %  wî          Z  j   pq ~ q ~.ppppppq ~&ppppq ~B  wîppppppsq ~ Q   	pppppppppppsq ~ Ypsq ~ ]  wîppppq ~Jq ~Jq ~Hpsq ~ d  wîppppq ~Jq ~Jpsq ~ ^  wîppppq ~Jq ~Jpsq ~ g  wîppppq ~Jq ~Jpsq ~ i  wîppppq ~Jq ~Jpppppt 	Helveticappppppppppp  wî       pp~r 3net.sf.jasperreports.engine.type.EvaluationTimeEnum          xq ~ t NOWsq ~ ;   )uq ~ >   sq ~ @t dthrJustificativat java.util.Dateppppppq ~ Xppt dd/MM/yyyy HH.mm.sssq ~E  wî          O     pq ~ q ~.ppppppq ~&ppppq ~B  wîppppppq ~Ipppppppppppsq ~ Ypsq ~ ]  wîppppq ~[q ~[q ~Zpsq ~ d  wîppppq ~[q ~[psq ~ ^  wîppppq ~[q ~[psq ~ g  wîppppq ~[q ~[psq ~ i  wîppppq ~[q ~[pppppt 	Helveticappppppppppp  wî        ppq ~Rsq ~ ;   *uq ~ >   sq ~ @t usuario.primeiroNomeConcatenadot java.lang.Stringpppppppppt  sq ~E  wî          2      pq ~ q ~.ppppppq ~&ppppq ~B  wîppppppq ~Ipppppppppppsq ~ Ypsq ~ ]  wîppppq ~iq ~iq ~hpsq ~ d  wîppppq ~iq ~ipsq ~ ^  wîppppq ~iq ~ipsq ~ g  wîppppq ~iq ~ipsq ~ i  wîppppq ~iq ~ipppppt 	Helveticappppppppppp  wî        ppq ~Rsq ~ ;   +uq ~ >   sq ~ @t dataHorat java.util.Datepppppppppt dd/MM/yyyy HH.mm.sssq ~E  wî          [  Ä   pq ~ q ~.ppppppq ~&ppppq ~B  wîppppppq ~Ipppppppppppsq ~ Ypsq ~ ]  wîppppq ~wq ~wq ~vpsq ~ d  wîppppq ~wq ~wpsq ~ ^  wîppppq ~wq ~wpsq ~ g  wîppppq ~wq ~wpsq ~ i  wîppppq ~wq ~wpppppt 	Helveticappppppppppp  wî        ppq ~Rsq ~ ;   ,uq ~ >   sq ~ @t )usuarioJustificou.primeiroNomeConcatenadot java.lang.Stringppppppq ~ Xppq ~gsq ~E  wî          }   ¡   pq ~ q ~.ppppppq ~&ppppq ~B  wîppppppq ~Ipppppppppppsq ~ Ypsq ~ ]  wîppppq ~q ~q ~psq ~ d  wîppppq ~q ~psq ~ ^  wîppppq ~q ~psq ~ g  wîppppq ~q ~psq ~ i  wîppppq ~q ~pppppt 	Helveticappppppppppp  wî        ppq ~Rsq ~ ;   -uq ~ >   sq ~ @t tipoLiberacao.descricaot java.lang.Stringpppppppppq ~gsq ~E  wî         §  Ã    pq ~ q ~.ppppppq ~&ppppq ~B  wîppppppq ~Ipq ~ Upppppppppsq ~ Ypsq ~ ]  wîppppq ~q ~q ~psq ~ d  wîppppq ~q ~psq ~ ^  wîppppq ~q ~psq ~ g  wîppppq ~q ~psq ~ i  wîppppq ~q ~pppppt 	Helveticappppppppppp  wî        ppq ~Rsq ~ ;   .uq ~ >   sq ~ @t 
justificativat java.lang.Stringppppppq ~ Xppq ~gsq ~E  wî          S  p   pq ~ q ~.ppppppq ~&ppppq ~B  wîppppppq ~Ipppppppppppsq ~ Ypsq ~ ]  wîppppq ~q ~q ~psq ~ d  wîppppq ~q ~psq ~ ^  wîppppq ~q ~psq ~ g  wîppppq ~q ~psq ~ i  wîppppq ~q ~pppppt 	Helveticappppp~r -net.sf.jasperreports.engine.type.RotationEnum          xq ~ t NONEppppp  wî        ppq ~Rsq ~ ;   /uq ~ >   sq ~ @t pessoaApresentart java.lang.Stringppppppq ~ Xppq ~gsq ~E  wî          g   :   pq ~ q ~.ppppppq ~&ppppq ~B  wîppppppq ~Ipppppppppppsq ~ Ypsq ~ ]  wîppppq ~®q ~®q ~­psq ~ d  wîppppq ~®q ~®psq ~ ^  wîppppq ~®q ~®psq ~ g  wîppppq ~®q ~®psq ~ i  wîppppq ~®q ~®pppppt 	Helveticappppppppppp  wî        ppq ~Rsq ~ ;   0uq ~ >   sq ~ @t localAcesso.descricaot java.lang.Stringpppppppppq ~gxp  wî   ppq ~ ppsr java.util.HashSetºD¸·4  xpw   ?@     t "net.sf.jasperreports.engine.data.*t net.sf.jasperreports.engine.*t java.util.*xt javapsq ~  wî ur &[Lnet.sf.jasperreports.engine.JRField;<ßÇN*òp  xp   
sr ,net.sf.jasperreports.engine.base.JRBaseField      'Ø L descriptionq ~ L nameq ~ L 
propertiesMapq ~ 3L valueClassNameq ~ L valueClassRealNameq ~ xppt dataHorasq ~¯pppt java.util.Datepsq ~Ãpt localAcesso.descricaosq ~¯pppt java.lang.Stringpsq ~Ãpt coletor.descricaosq ~¯pppt java.lang.Stringpsq ~Ãpt 
sentido.idsq ~¯pppt java.lang.Stringpsq ~Ãpt tipoLiberacao.descricaosq ~¯pppt java.lang.Stringpsq ~Ãpt usuario.primeiroNomeConcatenadosq ~¯pppt java.lang.Stringpsq ~Ãpt pessoaApresentarsq ~¯pppt java.lang.Stringpsq ~Ãpt 
justificativasq ~¯pppt java.lang.Stringpsq ~Ãt  t dthrJustificativasq ~¯pppt java.util.Datepsq ~Ãpt )usuarioJustificou.primeiroNomeConcatenadosq ~¯pppt java.lang.Stringpppt FechamentoControleAcessoDWReluq ~ª   %sq ~¬ppq ~®psq ~¯pppq ~²psq ~¬ppq ~´psq ~¯pppq ~¶psq ~¬ppq ~¸psq ~¯pppq ~ºpsq ~¬ppq ~¼psq ~¯pppq ~¾psq ~¬ppq ~Àpsq ~¯pppq ~,psq ~¬ppq ~Ãpsq ~¯pppq ~Åpsq ~¬ppq ~Çpsq ~¯pppq ~Épsq ~¬ppq ~Ëpsq ~¯pppq ~Ípsq ~¬ppq ~Ïpsq ~¯pppq ~Ñpsq ~¬ppq ~Ópsq ~¯pppq ~Õpsq ~¬ppq ~×psq ~¯pppq ~Ùpsq ~¬ppq ~Ûpsq ~¯pppq ~Ýpsq ~¬ppq ~ßpsq ~¯pppq ~ápsq ~¬ppq ~ãpsq ~¯pppq ~åpsq ~¬ppt REPORT_VIRTUALIZERpsq ~¯pppt )net.sf.jasperreports.engine.JRVirtualizerpsq ~¬ppt IS_IGNORE_PAGINATIONpsq ~¯pppq ~ Mpsq ~¬  ppt logoPadraoRelatoriopsq ~¯pppt java.io.InputStreampsq ~¬  ppt tituloRelatoriopsq ~¯pppt java.lang.Stringpsq ~¬  ppt nomeEmpresapsq ~¯pppt java.lang.Stringpsq ~¬  ppt versaoSoftwarepsq ~¯pppt java.lang.Stringpsq ~¬  ppt usuariopsq ~¯pppt java.lang.Stringpsq ~¬  ppt filtrospsq ~¯pppt java.lang.Stringpsq ~¬  ppt enderecoEmpresapsq ~¯pppt java.lang.Stringpsq ~¬  ppt 
cidadeEmpresapsq ~¯pppt java.lang.Stringpsq ~¬  ppt totalAcessospsq ~¯pppt java.lang.Numberpsq ~¬  ppt percentualLiberacaopsq ~¯pppt java.lang.Doublepsq ~¬  ppt percentualCliColpsq ~¯pppt java.lang.Doublepsq ~¬ ppt totalClipsq ~¯pppt java.lang.Integerpsq ~¬ ppt 
percentualClipsq ~¯pppt java.lang.Doublepsq ~¬ ppt 
percentualColpsq ~¯pppt java.lang.Doublepsq ~¬ ppt totalColpsq ~¯pppt java.lang.Integerpsq ~¬  sq ~ ;    uq ~ >   sq ~ @t "./"t java.lang.Stringppt 
SUBREPORT_DIRpsq ~¯pppq ~Spsq ~¬  ppt listaFechamentopsq ~¯pppt java.lang.Objectpsq ~¬ ppt totalFaltaJustificarLibpsq ~¯pppt java.lang.Integerpsq ~¬ ppt totalJustificadoLibpsq ~¯pppt java.lang.Integerpsq ~¬ ppt totalAcessoLibpsq ~¯pppt java.lang.Integerpsq ~¬ ppt qtdBVspsq ~¯pppt java.lang.Integerpsq ~¯psq ~    w   t ireport.scriptlethandlingt ireport.encodingt ireport.zoomt 	ireport.xt 	ireport.yxsr java.util.HashMapÚÁÃ`Ñ F 
loadFactorI 	thresholdxp?@     w      q ~nt 1.7715610000000008q ~mt 
ISO-8859-1q ~ot 0q ~pt 0q ~lt 0xsr ,net.sf.jasperreports.engine.base.JRBaseQuery      'Ø [ chunkst +[Lnet/sf/jasperreports/engine/JRQueryChunk;L languageq ~ xppt SQLppppuq ~ç   sq ~é  wî   q ~ïppq ~òppsq ~ ;   uq ~ >   sq ~ @t new java.lang.Integer(1)q ~¾pq ~øpq ~úq ~¾psq ~é  wî   q ~ïppq ~òppsq ~ ;   uq ~ >   sq ~ @t new java.lang.Integer(1)q ~¾pq ~pq ~q ~¾psq ~é  wî   q ~sq ~ ;   uq ~ >   sq ~ @t new java.lang.Integer(1)q ~¾ppq ~òppsq ~ ;   uq ~ >   sq ~ @t new java.lang.Integer(0)q ~¾pq ~pq ~úq ~¾psq ~é  wî   q ~sq ~ ;   uq ~ >   sq ~ @t new java.lang.Integer(1)q ~¾ppq ~òppsq ~ ;   uq ~ >   sq ~ @t new java.lang.Integer(0)q ~¾pq ~pq ~q ~¾psq ~é  wî   q ~sq ~ ;   uq ~ >   sq ~ @t new java.lang.Integer(1)q ~¾ppq ~òppsq ~ ;   uq ~ >   sq ~ @t new java.lang.Integer(0)q ~¾pq ~#pq ~$q ~¾p~q ~&t EMPTYq ~íp~r 0net.sf.jasperreports.engine.type.OrientationEnum          xq ~ t 	LANDSCAPEsq ~ sq ~     w    xp  wî    ppq ~ sq ~ sq ~    
w   
sr ,net.sf.jasperreports.engine.base.JRBaseImage      'Ø *I PSEUDO_SERIAL_VERSION_UIDI 
bookmarkLevelB evaluationTimeB hyperlinkTargetB 
hyperlinkTypeZ isLazyB onErrorTypeL anchorNameExpressionq ~ L borderq ~ L borderColorq ~ &L bottomBorderq ~ L bottomBorderColorq ~ &L 
bottomPaddingq ~ 'L evaluationGroupq ~ 2L evaluationTimeValueq ~FL 
expressionq ~ L horizontalAlignmentq ~ L horizontalAlignmentValueq ~ (L hyperlinkAnchorExpressionq ~ L hyperlinkPageExpressionq ~ [ hyperlinkParametersq ~GL hyperlinkReferenceExpressionq ~ L hyperlinkTooltipExpressionq ~ L isUsingCacheq ~ )L 
leftBorderq ~ L leftBorderColorq ~ &L leftPaddingq ~ 'L lineBoxq ~ *L 
linkTargetq ~ L linkTypeq ~ L onErrorTypeValuet 2Lnet/sf/jasperreports/engine/type/OnErrorTypeEnum;L paddingq ~ 'L rightBorderq ~ L rightBorderColorq ~ &L rightPaddingq ~ 'L 
scaleImageq ~ L scaleImageValuet 1Lnet/sf/jasperreports/engine/type/ScaleImageEnum;L 	topBorderq ~ L topBorderColorq ~ &L 
topPaddingq ~ 'L verticalAlignmentq ~ L verticalAlignmentValueq ~ -xq ~   wî   .       R      pq ~ q ~©pt image-1ppppq ~&ppppq ~ O  wîppsq ~ _  wîppppq ~®p  wî         ppppppp~q ~Qt PAGEsq ~ ;   	uq ~ >   sq ~ @t logoPadraoRelatoriot java.io.InputStreamppppppppq ~ Xpppsq ~ Ypsq ~ ]  wîsq ~2    ÿfffpppp~r .net.sf.jasperreports.engine.type.LineStyleEnum          xq ~ t SOLIDsr java.lang.FloatÚíÉ¢Û<ðì F valuexq ~ R?   q ~¸q ~¸q ~®psq ~ d  wîsq ~2    ÿfffppppq ~¼sq ~¾?   q ~¸q ~¸psq ~ ^  wîppppq ~¸q ~¸psq ~ g  wîsq ~2    ÿfffppppq ~¼sq ~¾?   q ~¸q ~¸psq ~ i  wîsq ~2    ÿfffppppq ~¼sq ~¾?   q ~¸q ~¸pp~r 0net.sf.jasperreports.engine.type.OnErrorTypeEnum          xq ~ t BLANKpppppppppppsq ~E  wî           ¯   V   pq ~ q ~©pt 
textField-208ppppq ~&ppppq ~ O  wîpppppppppq ~ Xppppppppsq ~ Ypsq ~ ]  wîppppq ~Ïq ~Ïq ~Ípsq ~ d  wîppppq ~Ïq ~Ïpsq ~ ^  wîppppq ~Ïq ~Ïpsq ~ g  wîppppq ~Ïq ~Ïpsq ~ i  wîppppq ~Ïq ~Ïpppppt Helvetica-Boldppppppppppp  wî        ppq ~Rsq ~ ;   
uq ~ >   sq ~ @t nomeEmpresat java.lang.Stringppppppsq ~ W pppsq ~E  wî           ¯   V   pq ~ q ~©pt 
textField-209ppppq ~&ppppq ~ O  wîpppppppppq ~ Xppppppppsq ~ Ypsq ~ ]  wîppppq ~Þq ~Þq ~Üpsq ~ d  wîppppq ~Þq ~Þpsq ~ ^  wîppppq ~Þq ~Þpsq ~ g  wîppppq ~Þq ~Þpsq ~ i  wîppppq ~Þq ~Þpppppt Helvetica-Boldppppppppppp  wî        ppq ~Rsq ~ ;   uq ~ >   sq ~ @t enderecoEmpresat java.lang.Stringppppppq ~Ûpppsq ~E  wî           ¯   V   !pq ~ q ~©pt 
textField-210ppppq ~&ppppq ~ O  wîpppppppppq ~ Xppppppppsq ~ Ypsq ~ ]  wîppppq ~ìq ~ìq ~êpsq ~ d  wîppppq ~ìq ~ìpsq ~ ^  wîppppq ~ìq ~ìpsq ~ g  wîppppq ~ìq ~ìpsq ~ i  wîppppq ~ìq ~ìpppppt Helvetica-Boldppppppppppp  wî        ppq ~Rsq ~ ;   uq ~ >   sq ~ @t 
cidadeEmpresat java.lang.Stringppppppq ~Ûpppsq ~ $  wî               pq ~ q ~©pt 
staticText-13ppppq ~&ppppq ~ O  wîppppppsq ~ Q   pq ~ Uq ~ Xppppppppsq ~ Ypsq ~ ]  wîppppq ~ûq ~ûq ~øpsq ~ d  wîppppq ~ûq ~ûpsq ~ ^  wîppppq ~ûq ~ûpsq ~ g  wîppppq ~ûq ~ûpsq ~ i  wîppppq ~ûq ~ûpppppt Helvetica-Boldpppppppppppt Fechamento de Acessossq ~ $  wî               pq ~ q ~©pt 
staticText-14pq ~8ppq ~&ppppq ~ O  wîpppppt Microsoft Sans Serifq ~Ip~q ~ Tt RIGHTq ~ Xq ~ Xpq ~Ûpq ~Ûpppsq ~ Ypsq ~ ]  wîsq ~2    ÿfffppppq ~¼sq ~¾    q ~q ~q ~psq ~ d  wîsq ~2    ÿfffppppq ~¼sq ~¾    q ~q ~psq ~ ^  wîppppq ~q ~psq ~ g  wîsq ~2    ÿfffppppq ~¼sq ~¾    q ~q ~psq ~ i  wîsq ~2    ÿfffppppq ~¼sq ~¾    q ~q ~p~r 0net.sf.jasperreports.engine.type.LineSpacingEnum          xq ~ t SINGLEpppt Helvetica-BoldObliquepppppppppp~r 2net.sf.jasperreports.engine.type.VerticalAlignEnum          xq ~ t TOPt eZillyon - Sistema Administrativo para Academias Desenvolvido por PACTO SoluÃ§Ãµes TecnolÃ³gicas Ltda.sq ~ $  wî           o  ±   pq ~ q ~©pt 
staticText-15pq ~8ppq ~&ppppq ~ O  wîpppppt Microsoft Sans Serifq ~Ipq ~q ~ Xq ~ Xpq ~Ûpq ~Ûpppsq ~ Ypsq ~ ]  wîsq ~2    ÿfffppppq ~¼sq ~¾    q ~!q ~!q ~psq ~ d  wîsq ~2    ÿfffppppq ~¼sq ~¾    q ~!q ~!psq ~ ^  wîppppq ~!q ~!psq ~ g  wîsq ~2    ÿfffppppq ~¼sq ~¾    q ~!q ~!psq ~ i  wîsq ~2    ÿfffppppq ~¼sq ~¾    q ~!q ~!pq ~pppt Helvetica-BoldObliqueppppppppppq ~t (0xx62) 3251-5820sq ~E  wî           K  ·   %pq ~ q ~©pt 
textField-211ppppq ~&ppppq ~ O  wîpppppt Arialq ~ Spq ~q ~ Xppppppppsq ~ Ysq ~ Q   sq ~ ]  wîsq ~2    ÿfffppppq ~¼sq ~¾    q ~4q ~4q ~1psq ~ d  wîsq ~2    ÿfffppppq ~¼sq ~¾    q ~4q ~4psq ~ ^  wîppppq ~4q ~4psq ~ g  wîsq ~2    ÿ   ppppq ~¼sq ~¾    q ~4q ~4psq ~ i  wîsq ~2    ÿ   ppppq ~¼sq ~¾    q ~4q ~4pppppt Helvetica-Boldppppppppppp  wî        ppq ~Rsq ~ ;   
uq ~ >   sq ~ @t "PÃ¡gina: " + sq ~ @t PAGE_NUMBERsq ~ @t 	 + " de "t java.lang.Stringppppppq ~Ûpppsq ~E  wî                %pq ~ q ~©pt 
textField-212ppppq ~&ppppq ~ O  wîpppppt Arialq ~ Sppq ~ Xppppppppsq ~ Yq ~5sq ~ ]  wîsq ~2    ÿfffppppq ~¼sq ~¾    q ~Pq ~Pq ~Mpsq ~ d  wîsq ~2    ÿfffppppq ~¼sq ~¾    q ~Pq ~Ppsq ~ ^  wîppppq ~Pq ~Ppsq ~ g  wîsq ~2    ÿfffppppq ~¼sq ~¾    q ~Pq ~Ppsq ~ i  wîsq ~2    ÿ   ppppq ~¼sq ~¾    q ~Pq ~Ppppppt Helvetica-Boldppppppppppp  wî        pp~q ~Qt REPORTsq ~ ;   uq ~ >   sq ~ @t " " + sq ~ @t PAGE_NUMBERsq ~ @t  + ""t java.lang.Stringppppppq ~Ûpppsq ~E  wî                 9pq ~ q ~©pt 
textField-214ppppq ~&ppppq ~ O  wîpppppt Arialq ~ Spq ~ Uq ~ Xq ~ Xpppppppsq ~ Ypsq ~ ]  wîsq ~2    ÿfffppppq ~¼sq ~¾?   q ~mq ~mq ~jpsq ~ d  wîppq ~¼sq ~¾?   q ~mq ~mpsq ~ ^  wîppppq ~mq ~mpsq ~ g  wîppq ~¼sq ~¾?   q ~mq ~mpsq ~ i  wîppq ~¼sq ~¾?   q ~mq ~mpppppt Helvetica-BoldObliqueppppppppppp  wî        ppq ~Rsq ~ ;   uq ~ >   sq ~ @t filtrost java.lang.Stringppppppq ~ Xpppxp  wî   Jppq ~ ~r /net.sf.jasperreports.engine.type.PrintOrderEnum          xq ~ t VERTICALpsq ~ sq ~     w    xp  wî    ppq ~ psq ~ sq ~     w    xp  wî    ppq ~ ~r 3net.sf.jasperreports.engine.type.WhenNoDataTypeEnum          xq ~ t ALL_SECTIONS_NO_DETAILsr 6net.sf.jasperreports.engine.design.JRReportCompileData      'Ø L crosstabCompileDataq ~°L datasetCompileDataq ~°L mainDatasetCompileDataq ~ xpsq ~q?@     w       xsq ~q?@     w      q ~©ur [B¬óøTà  xp  Êþº¾   .  8FechamentoControleAcessoDWRel_Teste_1733409750829_983529  ,net/sf/jasperreports/engine/fill/JREvaluator  parameter_REPORT_LOCALE 2Lnet/sf/jasperreports/engine/fill/JRFillParameter; parameter_JASPER_REPORT parameter_REPORT_TIME_ZONE parameter_REPORT_FILE_RESOLVER parameter_REPORT_SCRIPTLET parameter_REPORT_PARAMETERS_MAP parameter_REPORT_CONNECTION parameter_REPORT_CLASS_LOADER parameter_REPORT_DATA_SOURCE $parameter_REPORT_URL_HANDLER_FACTORY parameter_REPORT_FORMAT_FACTORY parameter_REPORT_MAX_COUNT parameter_REPORT_TEMPLATES  parameter_REPORT_RESOURCE_BUNDLE variable_PAGE_NUMBER 1Lnet/sf/jasperreports/engine/fill/JRFillVariable; variable_COLUMN_NUMBER variable_REPORT_COUNT variable_PAGE_COUNT variable_COLUMN_COUNT <init> ()V Code  
    	    	  !  	  # 	 	  % 
 	  '  	  )  	  + 
 	  -  	  /  	  1  	  3  	  5  	  7  	  9  	  ;  	  =  	  ?  	  A  	  C LineNumberTable customizedInit 0(Ljava/util/Map;Ljava/util/Map;Ljava/util/Map;)V 
initParams (Ljava/util/Map;)V H I
  J 
initFields L I
  M initVars O I
  P 
REPORT_LOCALE R 
java/util/Map T get &(Ljava/lang/Object;)Ljava/lang/Object; V W U X 0net/sf/jasperreports/engine/fill/JRFillParameter Z 
JASPER_REPORT \ REPORT_TIME_ZONE ^ REPORT_FILE_RESOLVER ` REPORT_SCRIPTLET b REPORT_PARAMETERS_MAP d REPORT_CONNECTION f REPORT_CLASS_LOADER h REPORT_DATA_SOURCE j REPORT_URL_HANDLER_FACTORY l REPORT_FORMAT_FACTORY n REPORT_MAX_COUNT p REPORT_TEMPLATES r REPORT_RESOURCE_BUNDLE t PAGE_NUMBER v /net/sf/jasperreports/engine/fill/JRFillVariable x 
COLUMN_NUMBER z REPORT_COUNT | 
PAGE_COUNT ~ COLUMN_COUNT  evaluate (I)Ljava/lang/Object; 
Exceptions java/lang/Throwable  java/lang/Integer  (I)V  
   evaluateOld evaluateEstimated 
SourceFile !                      	     
               
                                                                   Ì     d*· *µ  *µ "*µ $*µ &*µ (*µ **µ ,*µ .*µ 0*µ 2*µ 4*µ 6*µ 8*µ :*µ <*µ >*µ @*µ B*µ D±    E   V       	          ! " " ' # , $ 1 % 6 & ; ' @ ( E ) J * O + T , Y - ^ . c   F G     4     *+· K*,· N*-· Q±    E       :  ; 
 <  =  H I    M     ý*+S¹ Y À [À [µ  *+]¹ Y À [À [µ "*+_¹ Y À [À [µ $*+a¹ Y À [À [µ &*+c¹ Y À [À [µ (*+e¹ Y À [À [µ **+g¹ Y À [À [µ ,*+i¹ Y À [À [µ .*+k¹ Y À [À [µ 0*+m¹ Y À [À [µ 2*+o¹ Y À [À [µ 4*+q¹ Y À [À [µ 6*+s¹ Y À [À [µ 8*+u¹ Y À [À [µ :±    E   >    E  F $ G 6 H H I Z J l K ~ L  M ¢ N ´ O Æ P Ø Q ê R ü S  L I           ±    E       [  O I          [*+w¹ Y À yÀ yµ <*+{¹ Y À yÀ yµ >*+}¹ Y À yÀ yµ @*+¹ Y À yÀ yµ B*+¹ Y À yÀ yµ D±    E       c  d $ e 6 f H g Z h              ë     Mª             -   9   E   Q   ]   i   u   » Y· M§ T» Y· M§ H» Y· M§ <» Y· M§ 0» Y· M§ $» Y· M§ » Y· M§ » Y· M,°    E   J    p  r 0 v 9 w < { E | H  Q  T  ]  `  i  l  u  x       ¡              ë     Mª             -   9   E   Q   ]   i   u   » Y· M§ T» Y· M§ H» Y· M§ <» Y· M§ 0» Y· M§ $» Y· M§ » Y· M§ » Y· M,°    E   J    ª  ¬ 0 ° 9 ± < µ E ¶ H º Q » T ¿ ] À ` Ä i Å l É u Ê x Î  Ï  Ó  Û              ë     Mª             -   9   E   Q   ]   i   u   » Y· M§ T» Y· M§ H» Y· M§ <» Y· M§ 0» Y· M§ $» Y· M§ » Y· M§ » Y· M,°    E   J    ä  æ 0 ê 9 ë < ï E ð H ô Q õ T ù ] ú ` þ i ÿ l u x 	 
       xuq ~  /½Êþº¾   .z 2FechamentoControleAcessoDWRel_1733409750829_983529  ,net/sf/jasperreports/engine/fill/JREvaluator  parameter_enderecoEmpresa 2Lnet/sf/jasperreports/engine/fill/JRFillParameter; parameter_JASPER_REPORT parameter_REPORT_TIME_ZONE parameter_totalCol parameter_usuario parameter_REPORT_FILE_RESOLVER parameter_qtdBVs parameter_REPORT_PARAMETERS_MAP parameter_totalJustificadoLib parameter_REPORT_CLASS_LOADER $parameter_REPORT_URL_HANDLER_FACTORY parameter_REPORT_DATA_SOURCE parameter_IS_IGNORE_PAGINATION parameter_REPORT_MAX_COUNT parameter_REPORT_TEMPLATES parameter_totalAcessoLib parameter_listaFechamento parameter_percentualCli parameter_REPORT_LOCALE parameter_REPORT_VIRTUALIZER parameter_logoPadraoRelatorio parameter_REPORT_SCRIPTLET parameter_REPORT_CONNECTION parameter_totalCli parameter_SUBREPORT_DIR parameter_percentualCol parameter_percentualCliCol parameter_REPORT_FORMAT_FACTORY parameter_tituloRelatorio parameter_nomeEmpresa parameter_totalAcessos parameter_percentualLiberacao parameter_cidadeEmpresa  parameter_REPORT_RESOURCE_BUNDLE !parameter_totalFaltaJustificarLib parameter_versaoSoftware parameter_filtros field_tipoLiberacao46descricao .Lnet/sf/jasperreports/engine/fill/JRFillField; 0field_usuarioJustificou46primeiroNomeConcatenado field_pessoaApresentar field_sentido46id field_justificativa field_dthrJustificativa field_dataHora &field_usuario46primeiroNomeConcatenado field_localAcesso46descricao field_coletor46descricao variable_PAGE_NUMBER 1Lnet/sf/jasperreports/engine/fill/JRFillVariable; variable_COLUMN_NUMBER variable_REPORT_COUNT variable_PAGE_COUNT variable_COLUMN_COUNT <init> ()V Code < =
  ?  	  A  	  C  	  E 	 	  G 
 	  I  	  K  	  M 
 	  O  	  Q  	  S  	  U  	  W  	  Y  	  [  	  ]  	  _  	  a  	  c  	  e  	  g  	  i  	  k  	  m  	  o  	  q  	  s   	  u ! 	  w " 	  y # 	  { $ 	  } % 	   & 	   ' 	   ( 	   ) 	   * 	   + ,	   - ,	   . ,	   / ,	   0 ,	   1 ,	   2 ,	   3 ,	   4 ,	   5 ,	   6 7	   8 7	  ¡ 9 7	  £ : 7	  ¥ ; 7	  § LineNumberTable customizedInit 0(Ljava/util/Map;Ljava/util/Map;Ljava/util/Map;)V 
initParams (Ljava/util/Map;)V ¬ ­
  ® 
initFields ° ­
  ± initVars ³ ­
  ´ enderecoEmpresa ¶ 
java/util/Map ¸ get &(Ljava/lang/Object;)Ljava/lang/Object; º » ¹ ¼ 0net/sf/jasperreports/engine/fill/JRFillParameter ¾ 
JASPER_REPORT À REPORT_TIME_ZONE Â totalCol Ä usuario Æ REPORT_FILE_RESOLVER È qtdBVs Ê REPORT_PARAMETERS_MAP Ì totalJustificadoLib Î REPORT_CLASS_LOADER Ð REPORT_URL_HANDLER_FACTORY Ò REPORT_DATA_SOURCE Ô IS_IGNORE_PAGINATION Ö REPORT_MAX_COUNT Ø REPORT_TEMPLATES Ú totalAcessoLib Ü listaFechamento Þ 
percentualCli à 
REPORT_LOCALE â REPORT_VIRTUALIZER ä logoPadraoRelatorio æ REPORT_SCRIPTLET è REPORT_CONNECTION ê totalCli ì 
SUBREPORT_DIR î 
percentualCol ð percentualCliCol ò REPORT_FORMAT_FACTORY ô tituloRelatorio ö nomeEmpresa ø totalAcessos ú percentualLiberacao ü 
cidadeEmpresa þ REPORT_RESOURCE_BUNDLE  totalFaltaJustificarLib versaoSoftware filtros tipoLiberacao.descricao ,net/sf/jasperreports/engine/fill/JRFillField
 )usuarioJustificou.primeiroNomeConcatenado pessoaApresentar 
sentido.id 
justificativa dthrJustificativa dataHora usuario.primeiroNomeConcatenado localAcesso.descricao coletor.descricao PAGE_NUMBER /net/sf/jasperreports/engine/fill/JRFillVariable  
COLUMN_NUMBER" REPORT_COUNT$ 
PAGE_COUNT& COLUMN_COUNT( evaluate (I)Ljava/lang/Object; 
Exceptions java/lang/Throwable- .// java/lang/Integer1 (I)V <3
24 getValue ()Ljava/lang/Object;67
 ¿8 java/io/InputStream: java/lang/String< java/lang/StringBuffer> 	PÃ¡gina: @ (Ljava/lang/String;)V <B
?C
!8 append ,(Ljava/lang/Object;)Ljava/lang/StringBuffer;FG
?H  de J ,(Ljava/lang/String;)Ljava/lang/StringBuffer;FL
?M toString ()Ljava/lang/String;OP
?Q  S
8 java/util/DateV java/lang/BooleanX valueOf (Z)Ljava/lang/Boolean;Z[
Y\ java/lang/Double^ java/lang/Number` (net/sf/jasperreports/engine/JRDataSourceb &(Ljava/lang/Object;)Ljava/lang/String;Zd
=e /FechamentoControleAcessoDWRel_fechamento.jasperg intValue ()Iij
2k (Z)V <m
Yn evaluateOld getOldValueq7
!r
r evaluateEstimated getEstimatedValuev7
!w 
SourceFile !     4                 	     
               
                                                                                                     !     "     #     $     %     &     '     (     )     *     + ,    - ,    . ,    / ,    0 ,    1 ,    2 ,    3 ,    4 ,    5 ,    6 7    8 7    9 7    : 7    ; 7     < =  >  õ    	*· @*µ B*µ D*µ F*µ H*µ J*µ L*µ N*µ P*µ R*µ T*µ V*µ X*µ Z*µ \*µ ^*µ `*µ b*µ d*µ f*µ h*µ j*µ l*µ n*µ p*µ r*µ t*µ v*µ x*µ z*µ |*µ ~*µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ  *µ ¢*µ ¤*µ ¦*µ ¨±    ©   Ú 6      	          ! " " ' # , $ 1 % 6 & ; ' @ ( E ) J * O + T , Y - ^ . c / h 0 m 1 r 2 w 3 | 4  5  6  7  8  9  :  ; ¤ < © = ® > ³ ? ¸ @ ½ A Â B Ç C Ì D Ñ E Ö F Û G à H å I ê J ï K ô L ù M þ N O   ª «  >   4     *+· ¯*,· ²*-· µ±    ©       [  \ 
 ]  ^  ¬ ­  >  K    *+·¹ ½ À ¿À ¿µ B*+Á¹ ½ À ¿À ¿µ D*+Ã¹ ½ À ¿À ¿µ F*+Å¹ ½ À ¿À ¿µ H*+Ç¹ ½ À ¿À ¿µ J*+É¹ ½ À ¿À ¿µ L*+Ë¹ ½ À ¿À ¿µ N*+Í¹ ½ À ¿À ¿µ P*+Ï¹ ½ À ¿À ¿µ R*+Ñ¹ ½ À ¿À ¿µ T*+Ó¹ ½ À ¿À ¿µ V*+Õ¹ ½ À ¿À ¿µ X*+×¹ ½ À ¿À ¿µ Z*+Ù¹ ½ À ¿À ¿µ \*+Û¹ ½ À ¿À ¿µ ^*+Ý¹ ½ À ¿À ¿µ `*+ß¹ ½ À ¿À ¿µ b*+á¹ ½ À ¿À ¿µ d*+ã¹ ½ À ¿À ¿µ f*+å¹ ½ À ¿À ¿µ h*+ç¹ ½ À ¿À ¿µ j*+é¹ ½ À ¿À ¿µ l*+ë¹ ½ À ¿À ¿µ n*+í¹ ½ À ¿À ¿µ p*+ï¹ ½ À ¿À ¿µ r*+ñ¹ ½ À ¿À ¿µ t*+ó¹ ½ À ¿À ¿µ v*+õ¹ ½ À ¿À ¿µ x*+÷¹ ½ À ¿À ¿µ z*+ù¹ ½ À ¿À ¿µ |*+û¹ ½ À ¿À ¿µ ~*+ý¹ ½ À ¿À ¿µ *+ÿ¹ ½ À ¿À ¿µ *+¹ ½ À ¿À ¿µ *+¹ ½ À ¿À ¿µ *+¹ ½ À ¿À ¿µ *+¹ ½ À ¿À ¿µ ±    ©    &   f  g $ h 6 i H j Z k l l ~ m  n ¢ o ´ p Æ q Ø r ê s ü t u  v2 wD xV yh zz { | }° ~Â Ô æ ø 
  . @ R e x     ° ­  >   ÿ     ¿*+	¹ ½ ÀÀµ *+
¹ ½ ÀÀµ *+¹ ½ ÀÀµ *+¹ ½ ÀÀµ *+¹ ½ ÀÀµ *+¹ ½ ÀÀµ *+¹ ½ ÀÀµ *+¹ ½ ÀÀµ *+¹ ½ ÀÀµ *+¹ ½ ÀÀµ ±    ©   .       &  9  L  _  r      «  ¾   ³ ­  >        `*+¹ ½ À!À!µ  *+#¹ ½ À!À!µ ¢*+%¹ ½ À!À!µ ¤*+'¹ ½ À!À!µ ¦*+)¹ ½ À!À!µ ¨±    ©       ¥  ¦ & § 9 ¨ L © _ ª *+ ,    . >  ¥    Mª  ü       0   Ñ   Ø   ä   ð   ü         ,  8  F  T  b  p    ²  À  ó  &  Y    ¿  ò  %  X  f  t        ©  ·  Å  Ó  á  ï  ý      :  m      «  ¹  Ç  Õ  ã  ñ0M§'»2Y·5M§»2Y·5M§»2Y·5M§»2Y·5M§÷»2Y·5M§ë»2Y·5M§ß»2Y·5M§Ó»2Y·5M§Ç*´ j¶9À;M§¹*´ |¶9À=M§«*´ B¶9À=M§*´ ¶9À=M§»?YA·D*´  ¶EÀ2¶IK¶N¶RM§k»?YT·D*´  ¶EÀ2¶I¶RM§M*´ ¶9À=M§?*´ ¶UÀWÇ !*´ ¶UÀ=Ç *´ ¶UÀ=Ç § ¸]M§*´ ¶UÀWÇ !*´ ¶UÀ=Ç *´ ¶UÀ=Ç § ¸]M§Ù*´ ¶UÀWÇ !*´ ¶UÀ=Ç *´ ¶UÀ=Ç § ¸]M§¦*´ ¶UÀWÇ !*´ ¶UÀ=Ç *´ ¶UÀ=Ç § ¸]M§s*´ ¶UÀWÇ !*´ ¶UÀ=Ç *´ ¶UÀ=Ç § ¸]M§@*´ ¶UÀWÇ !*´ ¶UÀ=Ç *´ ¶UÀ=Ç § ¸]M§
*´ ¶UÀWÇ !*´ ¶UÀ=Ç *´ ¶UÀ=Ç § ¸]M§Ú*´ ¶UÀWÇ !*´ ¶UÀ=Ç *´ ¶UÀ=Ç § ¸]M§§*´ t¶9À_M§*´ r¶9À=M§*´ v¶9À_M§}*´ H¶9À2M§o*´ `¶9À2M§a*´ b¶9M§V*´ ~¶9ÀaM§H*´ N¶9À2M§:*´ d¶9À_M§,*´ R¶9À2M§*´ ¶9À_M§*´ p¶9À2M§*´ ¶9À2M§ ô*´ b¶9ÀcM§ æ»?Y*´ r¶9À=¸f·Dh¶N¶RM§ Å*´ ¶UÀWÇ !*´ ¶UÀ=Ç *´ ¶UÀ=Ç § ¸]M§ »YY*´ ¨¶EÀ2¶lp § ·oM§ p*´ ¶UÀWM§ b*´ ¶UÀ=M§ T*´ ¶UÀWM§ F*´ ¶UÀ=M§ 8*´ ¶UÀ=M§ **´ ¶UÀ=M§ *´ ¶UÀ=M§ *´ ¶UÀ=M,°    ©   d   ²  ´ Ô ¸ Ø ¹ Û ½ ä ¾ ç Â ð Ã ó Ç ü È ÿ Ì Í Ñ Ò Ö  ×# Û, Ü/ à8 á; åF æI êT ëW ïb ðe ôp õs ù ú þ² ÿµÀÃó	ö
&)Y\¿Â!ò"õ&%'(+X,[0f1i5t6w:;?@DE¡I©J¬N·OºSÅTÈXÓYÖ]á^äbïcògýh lmqrv:w={m|p «®¹¼ÇÊÕØãæ£ñ¤ô¨ÿ° p+ ,    . >  ¥    Mª  ü       0   Ñ   Ø   ä   ð   ü         ,  8  F  T  b  p    ²  À  ó  &  Y    ¿  ò  %  X  f  t        ©  ·  Å  Ó  á  ï  ý      :  m      «  ¹  Ç  Õ  ã  ñ0M§'»2Y·5M§»2Y·5M§»2Y·5M§»2Y·5M§÷»2Y·5M§ë»2Y·5M§ß»2Y·5M§Ó»2Y·5M§Ç*´ j¶9À;M§¹*´ |¶9À=M§«*´ B¶9À=M§*´ ¶9À=M§»?YA·D*´  ¶sÀ2¶IK¶N¶RM§k»?YT·D*´  ¶sÀ2¶I¶RM§M*´ ¶9À=M§?*´ ¶tÀWÇ !*´ ¶tÀ=Ç *´ ¶tÀ=Ç § ¸]M§*´ ¶tÀWÇ !*´ ¶tÀ=Ç *´ ¶tÀ=Ç § ¸]M§Ù*´ ¶tÀWÇ !*´ ¶tÀ=Ç *´ ¶tÀ=Ç § ¸]M§¦*´ ¶tÀWÇ !*´ ¶tÀ=Ç *´ ¶tÀ=Ç § ¸]M§s*´ ¶tÀWÇ !*´ ¶tÀ=Ç *´ ¶tÀ=Ç § ¸]M§@*´ ¶tÀWÇ !*´ ¶tÀ=Ç *´ ¶tÀ=Ç § ¸]M§
*´ ¶tÀWÇ !*´ ¶tÀ=Ç *´ ¶tÀ=Ç § ¸]M§Ú*´ ¶tÀWÇ !*´ ¶tÀ=Ç *´ ¶tÀ=Ç § ¸]M§§*´ t¶9À_M§*´ r¶9À=M§*´ v¶9À_M§}*´ H¶9À2M§o*´ `¶9À2M§a*´ b¶9M§V*´ ~¶9ÀaM§H*´ N¶9À2M§:*´ d¶9À_M§,*´ R¶9À2M§*´ ¶9À_M§*´ p¶9À2M§*´ ¶9À2M§ ô*´ b¶9ÀcM§ æ»?Y*´ r¶9À=¸f·Dh¶N¶RM§ Å*´ ¶tÀWÇ !*´ ¶tÀ=Ç *´ ¶tÀ=Ç § ¸]M§ »YY*´ ¨¶sÀ2¶lp § ·oM§ p*´ ¶tÀWM§ b*´ ¶tÀ=M§ T*´ ¶tÀWM§ F*´ ¶tÀ=M§ 8*´ ¶tÀ=M§ **´ ¶tÀ=M§ *´ ¶tÀ=M§ *´ ¶tÀ=M,°    ©   d  ¹ » Ô¿ ØÀ ÛÄ äÅ çÉ ðÊ óÎ üÏ ÿÓÔØÙÝ Þ#â,ã/ç8è;ìFíIñTòWöb÷eûpüs ²µ
ÀÃóö&)Y\#¿$Â(ò)õ-%.(2X3[7f8i<t=wABFGKL¡P©Q¬U·VºZÅ[È_Ó`Ödáeäiïjònýo stxy}:~=mp «®¹¼ÇÊ Õ¡Ø¥ã¦æªñ«ô¯ÿ· u+ ,    . >  ¥    Mª  ü       0   Ñ   Ø   ä   ð   ü         ,  8  F  T  b  p    ²  À  ó  &  Y    ¿  ò  %  X  f  t        ©  ·  Å  Ó  á  ï  ý      :  m      «  ¹  Ç  Õ  ã  ñ0M§'»2Y·5M§»2Y·5M§»2Y·5M§»2Y·5M§÷»2Y·5M§ë»2Y·5M§ß»2Y·5M§Ó»2Y·5M§Ç*´ j¶9À;M§¹*´ |¶9À=M§«*´ B¶9À=M§*´ ¶9À=M§»?YA·D*´  ¶xÀ2¶IK¶N¶RM§k»?YT·D*´  ¶xÀ2¶I¶RM§M*´ ¶9À=M§?*´ ¶UÀWÇ !*´ ¶UÀ=Ç *´ ¶UÀ=Ç § ¸]M§*´ ¶UÀWÇ !*´ ¶UÀ=Ç *´ ¶UÀ=Ç § ¸]M§Ù*´ ¶UÀWÇ !*´ ¶UÀ=Ç *´ ¶UÀ=Ç § ¸]M§¦*´ ¶UÀWÇ !*´ ¶UÀ=Ç *´ ¶UÀ=Ç § ¸]M§s*´ ¶UÀWÇ !*´ ¶UÀ=Ç *´ ¶UÀ=Ç § ¸]M§@*´ ¶UÀWÇ !*´ ¶UÀ=Ç *´ ¶UÀ=Ç § ¸]M§
*´ ¶UÀWÇ !*´ ¶UÀ=Ç *´ ¶UÀ=Ç § ¸]M§Ú*´ ¶UÀWÇ !*´ ¶UÀ=Ç *´ ¶UÀ=Ç § ¸]M§§*´ t¶9À_M§*´ r¶9À=M§*´ v¶9À_M§}*´ H¶9À2M§o*´ `¶9À2M§a*´ b¶9M§V*´ ~¶9ÀaM§H*´ N¶9À2M§:*´ d¶9À_M§,*´ R¶9À2M§*´ ¶9À_M§*´ p¶9À2M§*´ ¶9À2M§ ô*´ b¶9ÀcM§ æ»?Y*´ r¶9À=¸f·Dh¶N¶RM§ Å*´ ¶UÀWÇ !*´ ¶UÀ=Ç *´ ¶UÀ=Ç § ¸]M§ »YY*´ ¨¶xÀ2¶lp § ·oM§ p*´ ¶UÀWM§ b*´ ¶UÀ=M§ T*´ ¶UÀWM§ F*´ ¶UÀ=M§ 8*´ ¶UÀ=M§ **´ ¶UÀ=M§ *´ ¶UÀ=M§ *´ ¶UÀ=M,°    ©   d  À Â ÔÆ ØÇ ÛË äÌ çÐ ðÑ óÕ üÖ ÿÚÛßàä å#é,ê/î8ï;óFôIøTùWýbþeps²
µÀÃóö&) Y!\%&*¿+Â/ò0õ4%5(9X:[>f?iCtDwHIMNRS¡W©X¬\·]ºaÅbÈfÓgÖkáläpïqòuýv z{:=mp «®¹¼¢Ç£Ê§Õ¨Ø¬ã­æ±ñ²ô¶ÿ¾ y    t _1733409750829_983529t 2net.sf.jasperreports.engine.design.JRJavacCompiler
<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="DemonstrativoFinanceiroRelExcel" pageWidth="878" pageHeight="680" orientation="Landscape" columnWidth="838" leftMargin="20" rightMargin="20" topMargin="2" bottomMargin="2" whenResourceMissingType="Empty">
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.encoding" value="ISO-8859-1"/>
	<property name="ireport.zoom" value="1.5000000000000038"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="java.util.*"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<parameter name="tipoRelatorioDF" class="java.lang.Integer"/>
	<parameter name="planoFilho" class="java.lang.String"/>
	<parameter name="tituloData" class="java.lang.String"/>
	<queryString>
		<![CDATA[]]>
	</queryString>
	<field name="descricaoLancamento" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="contrato" class="java.lang.Integer"/>
	<field name="nomePessoa" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="movProduto" class="java.lang.Integer">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="tipoFormaPagto.descricao" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="movPagamento" class="java.lang.Integer"/>
	<field name="valorLancamentoApresentarExcel" class="java.lang.String"/>
	<field name="descricaoFormaPagamento" class="java.lang.String"/>
	<field name="dataApresentar" class="java.lang.String"/>
	<field name="dataMesApresentar" class="java.lang.String"/>
	<field name="tipoES.descricao" class="java.lang.String"/>
	<field name="valorLancamento" class="java.lang.Double"/>
	<field name="empresaApresentar" class="java.lang.String"/>
	<field name="codProduto" class="java.lang.Integer"/>
	<variable name="tipoDescricao" class="java.lang.String">
		<variableExpression><![CDATA[( $F{valorLancamento}<0.0 ?  "Saída":"Entrada" )]]></variableExpression>
	</variable>
	<title>
		<band height="23">
			<staticText>
				<reportElement key="staticText-2" x="82" y="2" width="93" height="20"/>
				<textElement>
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Descrição]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-1" stretchType="RelativeToTallestObject" x="175" y="2" width="84" height="20"/>
				<textElement>
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Contrato/Produto]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-8" x="582" y="2" width="34" height="20"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Tipo]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-8" x="259" y="2" width="98" height="20"/>
				<textElement>
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Cliente]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-8" x="616" y="2" width="54" height="20"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Valor(R$)]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-8" x="357" y="2" width="121" height="20">
					<printWhenExpression><![CDATA[$F{descricaoFormaPagamento} != null]]></printWhenExpression>
				</reportElement>
				<textElement verticalAlignment="Middle">
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Forma de Pagamento]]></text>
			</staticText>
			<staticText>
				<reportElement x="1" y="2" width="81" height="20"/>
				<textElement>
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Plano de Contas]]></text>
			</staticText>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement key="textField" positionType="Float" x="478" y="2" width="104" height="20"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="10" isBold="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{tituloData}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement key="staticText-8" x="670" y="2" width="98" height="20"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Empresa]]></text>
			</staticText>
		</band>
	</title>
	<detail>
		<band height="27">
			<textField isBlankWhenNull="true">
				<reportElement key="textField-223" x="175" y="3" width="84" height="20"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.Integer"><![CDATA[$F{contrato} != 0 ? $F{contrato} : $F{codProduto}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-224" x="82" y="3" width="93" height="20"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font size="8" isBold="false"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{descricaoLancamento}]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement key="textField" positionType="Float" x="616" y="3" width="54" height="20"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{valorLancamentoApresentarExcel}]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement key="textField" positionType="Float" x="582" y="3" width="34" height="20"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$V{tipoDescricao}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="357" y="3" width="121" height="20"/>
				<textElement verticalAlignment="Middle">
					<font size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{descricaoFormaPagamento}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-224" x="259" y="3" width="98" height="20"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font size="8" isBold="false"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{nomePessoa}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="1" y="3" width="81" height="20"/>
				<textElement verticalAlignment="Middle">
					<font size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{planoFilho}]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement key="textField" positionType="Float" x="478" y="3" width="104" height="20"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[($P{tipoRelatorioDF}.equals(2) ||
					$P{tipoRelatorioDF}.equals(5) ||
					$P{tipoRelatorioDF}.equals(6)) ? $F{dataMesApresentar} : $F{dataApresentar}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-224" x="670" y="3" width="98" height="20"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font size="8" isBold="false"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{empresaApresentar}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
</jasperReport>

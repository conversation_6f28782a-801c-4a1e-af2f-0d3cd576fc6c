<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="FechamentoControleAcessoDWRel" pageWidth="842" pageHeight="595" orientation="Landscape" whenNoDataType="AllSectionsNoDetail" columnWidth="802" leftMargin="20" rightMargin="20" topMargin="10" bottomMargin="10" whenResourceMissingType="Empty">
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.encoding" value="ISO-8859-1"/>
	<property name="ireport.zoom" value="1.7715610000000008"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="java.util.*"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<subDataset name="Teste"/>
	<parameter name="logoPadraoRelatorio" class="java.io.InputStream" isForPrompting="false"/>
	<parameter name="tituloRelatorio" class="java.lang.String" isForPrompting="false"/>
	<parameter name="nomeEmpresa" class="java.lang.String" isForPrompting="false"/>
	<parameter name="versaoSoftware" class="java.lang.String" isForPrompting="false"/>
	<parameter name="usuario" class="java.lang.String" isForPrompting="false"/>
	<parameter name="filtros" class="java.lang.String" isForPrompting="false"/>
	<parameter name="enderecoEmpresa" class="java.lang.String" isForPrompting="false"/>
	<parameter name="cidadeEmpresa" class="java.lang.String" isForPrompting="false"/>
	<parameter name="totalAcessos" class="java.lang.Number" isForPrompting="false"/>
	<parameter name="percentualLiberacao" class="java.lang.Double" isForPrompting="false"/>
	<parameter name="percentualCliCol" class="java.lang.Double" isForPrompting="false"/>
	<parameter name="totalCli" class="java.lang.Integer"/>
	<parameter name="percentualCli" class="java.lang.Double"/>
	<parameter name="percentualCol" class="java.lang.Double"/>
	<parameter name="totalCol" class="java.lang.Integer"/>
	<parameter name="SUBREPORT_DIR" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA["./"]]></defaultValueExpression>
	</parameter>
	<parameter name="listaFechamento" class="java.lang.Object" isForPrompting="false"/>
	<parameter name="totalFaltaJustificarLib" class="java.lang.Integer"/>
	<parameter name="totalJustificadoLib" class="java.lang.Integer"/>
	<parameter name="totalAcessoLib" class="java.lang.Integer"/>
	<parameter name="qtdBVs" class="java.lang.Integer"/>
	<queryString language="SQL">
		<![CDATA[]]>
	</queryString>
	<field name="dataHora" class="java.util.Date"/>
	<field name="localAcesso.descricao" class="java.lang.String"/>
	<field name="coletor.descricao" class="java.lang.String"/>
	<field name="sentido.id" class="java.lang.String"/>
	<field name="tipoLiberacao.descricao" class="java.lang.String"/>
	<field name="usuario.primeiroNomeConcatenado" class="java.lang.String"/>
	<field name="pessoaApresentar" class="java.lang.String"/>
	<field name="justificativa" class="java.lang.String"/>
	<field name="dthrJustificativa" class="java.util.Date">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="usuarioJustificou.primeiroNomeConcatenado" class="java.lang.String"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band splitType="Stretch"/>
	</title>
	<pageHeader>
		<band height="74" splitType="Stretch">
			<image isUsingCache="true" onErrorType="Blank" evaluationTime="Page">
				<reportElement key="image-1" x="2" y="1" width="82" height="46" isPrintWhenDetailOverflows="true"/>
				<box>
					<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<imageExpression class="java.io.InputStream"><![CDATA[$P{logoPadraoRelatorio}]]></imageExpression>
			</image>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-208" x="86" y="1" width="175" height="14"/>
				<textElement>
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{nomeEmpresa}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-209" x="86" y="17" width="175" height="14"/>
				<textElement>
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{enderecoEmpresa}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-210" x="86" y="33" width="175" height="14"/>
				<textElement>
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{cidadeEmpresa}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement key="staticText-13" x="273" y="17" width="257" height="27"/>
				<textElement textAlignment="Center">
					<font size="20" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Fechamento de Acessos]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-14" mode="Opaque" x="538" y="1" width="262" height="23"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Top" lineSpacing="Single">
					<font fontName="Microsoft Sans Serif" size="9" isBold="true" isItalic="true" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica-BoldOblique"/>
				</textElement>
				<text><![CDATA[Zillyon - Sistema Administrativo para Academias Desenvolvido por PACTO Soluções Tecnológicas Ltda.]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-15" mode="Opaque" x="689" y="24" width="111" height="12"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Top" lineSpacing="Single">
					<font fontName="Microsoft Sans Serif" size="9" isBold="true" isItalic="true" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica-BoldOblique"/>
				</textElement>
				<text><![CDATA[(0xx62) 3251-5820]]></text>
			</staticText>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-211" x="695" y="37" width="75" height="17"/>
				<box bottomPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right">
					<font fontName="Arial" size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA["Página: " + $V{PAGE_NUMBER} + " de "]]></textFieldExpression>
			</textField>
			<textField evaluationTime="Report" isBlankWhenNull="false">
				<reportElement key="textField-212" x="770" y="37" width="31" height="17"/>
				<box bottomPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement>
					<font fontName="Arial" size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[" " + $V{PAGE_NUMBER} + ""]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-214" x="1" y="57" width="800" height="15"/>
				<box>
					<topPen lineWidth="0.5" lineStyle="Solid"/>
					<leftPen lineWidth="0.5" lineStyle="Solid"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.5" lineStyle="Solid"/>
				</box>
				<textElement textAlignment="Center">
					<font fontName="Arial" size="10" isBold="true" isItalic="true" pdfFontName="Helvetica-BoldOblique"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{filtros}]]></textFieldExpression>
			</textField>
		</band>
	</pageHeader>
	<columnHeader>
		<band height="45" splitType="Stretch">
			<staticText>
				<reportElement key="staticText-1" positionType="Float" x="62" y="28" width="99" height="14" isPrintInFirstWholeBand="true">
					<printWhenExpression><![CDATA[$F{dataHora} != null || $F{pessoaApresentar} != null || $F{localAcesso.descricao} != null]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Center">
					<font size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Local de Acesso]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-1" positionType="Float" x="708" y="28" width="91" height="14" isPrintInFirstWholeBand="true">
					<printWhenExpression><![CDATA[$F{dataHora} != null || $F{pessoaApresentar} != null || $F{localAcesso.descricao} != null]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Center">
					<font size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Usuário Justificou]]></text>
			</staticText>
			<line>
				<reportElement positionType="Float" x="1" y="43" width="800" height="1" isPrintInFirstWholeBand="true"/>
			</line>
			<staticText>
				<reportElement key="staticText-1" positionType="Float" x="618" y="28" width="90" height="14" isPrintInFirstWholeBand="true">
					<printWhenExpression><![CDATA[$F{dataHora} != null || $F{pessoaApresentar} != null || $F{localAcesso.descricao} != null]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Center">
					<font size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Dt. Justificativa]]></text>
			</staticText>
			<line>
				<reportElement positionType="Float" x="1" y="27" width="800" height="1" isPrintInFirstWholeBand="true"/>
			</line>
			<staticText>
				<reportElement key="staticText-1" positionType="Float" x="0" y="28" width="61" height="14" isPrintInFirstWholeBand="true">
					<printWhenExpression><![CDATA[$F{dataHora} != null || $F{pessoaApresentar} != null || $F{localAcesso.descricao} != null]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Center">
					<font size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Data Acesso]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-1" positionType="Float" x="368" y="28" width="54" height="14" isPrintInFirstWholeBand="true">
					<printWhenExpression><![CDATA[$F{dataHora} != null || $F{pessoaApresentar} != null || $F{localAcesso.descricao} != null]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Center">
					<font size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Pessoa]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-1" positionType="Float" x="273" y="28" width="95" height="14" isPrintInFirstWholeBand="true">
					<printWhenExpression><![CDATA[$F{dataHora} != null || $F{pessoaApresentar} != null || $F{localAcesso.descricao} != null]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Center">
					<font size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Usuário Liberou]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-1" positionType="Float" x="161" y="28" width="112" height="14" isPrintInFirstWholeBand="true">
					<printWhenExpression><![CDATA[$F{dataHora} != null || $F{pessoaApresentar} != null || $F{localAcesso.descricao} != null]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Center">
					<font size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Tipo de Liberação]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-1" positionType="Float" x="422" y="28" width="196" height="14" isPrintInFirstWholeBand="true">
					<printWhenExpression><![CDATA[$F{dataHora} != null || $F{pessoaApresentar} != null || $F{localAcesso.descricao} != null]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Center">
					<font size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Justificativa]]></text>
			</staticText>
			<subreport isUsingCache="true">
				<reportElement key="subreport1" x="-17" y="1" width="819" height="24"/>
				<subreportParameter name="percentualCol">
					<subreportParameterExpression><![CDATA[$P{percentualCol}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="SUBREPORT_DIR">
					<subreportParameterExpression><![CDATA[$P{SUBREPORT_DIR}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="percentualCliCol">
					<subreportParameterExpression><![CDATA[$P{percentualCliCol}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="totalCol">
					<subreportParameterExpression><![CDATA[$P{totalCol}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="totalAcessoLib">
					<subreportParameterExpression><![CDATA[$P{totalAcessoLib}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="listaFechamento">
					<subreportParameterExpression><![CDATA[$P{listaFechamento}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="totalAcessos">
					<subreportParameterExpression><![CDATA[$P{totalAcessos}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="qtdBVs">
					<subreportParameterExpression><![CDATA[$P{qtdBVs}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="percentualCli">
					<subreportParameterExpression><![CDATA[$P{percentualCli}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="totalJustificadoLib">
					<subreportParameterExpression><![CDATA[$P{totalJustificadoLib}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="percentualLiberacao">
					<subreportParameterExpression><![CDATA[$P{percentualLiberacao}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="totalCli">
					<subreportParameterExpression><![CDATA[$P{totalCli}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="totalFaltaJustificarLib">
					<subreportParameterExpression><![CDATA[$P{totalFaltaJustificarLib}]]></subreportParameterExpression>
				</subreportParameter>
				<dataSourceExpression><![CDATA[$P{listaFechamento}]]></dataSourceExpression>
				<subreportExpression class="java.lang.String"><![CDATA[$P{SUBREPORT_DIR} + "FechamentoControleAcessoDWRel_fechamento.jasper"]]></subreportExpression>
			</subreport>
			<staticText>
				<reportElement key="staticText-1" positionType="Float" x="0" y="28" width="801" height="14" isPrintInFirstWholeBand="true">
					<printWhenExpression><![CDATA[$F{dataHora} == null && $F{pessoaApresentar} == null && $F{localAcesso.descricao} == null]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Center">
					<font size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Nenhum Registro Encontrado!]]></text>
			</staticText>
		</band>
	</columnHeader>
	<detail>
		<band height="20" splitType="Stretch">
			<rectangle>
				<reportElement stretchType="RelativeToBandHeight" mode="Opaque" x="1" y="3" width="800" height="16" forecolor="#B4CDCD" backcolor="#B4CDCD">
					<printWhenExpression><![CDATA[new Boolean(($V{COLUMN_COUNT}.intValue()%2)==0)]]></printWhenExpression>
				</reportElement>
			</rectangle>
			<textField isStretchWithOverflow="true" pattern="dd/MM/yyyy HH.mm.ss" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="618" y="3" width="90" height="17" isPrintInFirstWholeBand="true"/>
				<textElement>
					<font size="9" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression class="java.util.Date"><![CDATA[$F{dthrJustificativa}]]></textFieldExpression>
			</textField>
			<textField pattern="">
				<reportElement stretchType="RelativeToBandHeight" x="287" y="3" width="79" height="17" isPrintInFirstWholeBand="true"/>
				<textElement>
					<font size="9" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{usuario.primeiroNomeConcatenado}]]></textFieldExpression>
			</textField>
			<textField pattern="dd/MM/yyyy HH.mm.ss">
				<reportElement stretchType="RelativeToBandHeight" x="1" y="3" width="50" height="17" isPrintInFirstWholeBand="true"/>
				<textElement>
					<font size="9" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression class="java.util.Date"><![CDATA[$F{dataHora}]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="708" y="3" width="91" height="17" isPrintInFirstWholeBand="true"/>
				<textElement>
					<font size="9" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{usuarioJustificou.primeiroNomeConcatenado}]]></textFieldExpression>
			</textField>
			<textField pattern="">
				<reportElement stretchType="RelativeToBandHeight" x="161" y="3" width="125" height="17" isPrintInFirstWholeBand="true"/>
				<textElement>
					<font size="9" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{tipoLiberacao.descricao}]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="451" y="0" width="167" height="20" isRemoveLineWhenBlank="true" isPrintInFirstWholeBand="true"/>
				<textElement textAlignment="Center">
					<font size="9" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{justificativa}]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="368" y="3" width="83" height="17" isPrintInFirstWholeBand="true"/>
				<textElement rotation="None">
					<font size="9" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{pessoaApresentar}]]></textFieldExpression>
			</textField>
			<textField pattern="">
				<reportElement stretchType="RelativeToBandHeight" x="58" y="3" width="103" height="15" isPrintInFirstWholeBand="true"/>
				<textElement>
					<font size="9" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{localAcesso.descricao}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<columnFooter>
		<band splitType="Stretch"/>
	</columnFooter>
	<pageFooter>
		<band splitType="Stretch"/>
	</pageFooter>
	<summary>
		<band splitType="Stretch"/>
	</summary>
</jasperReport>

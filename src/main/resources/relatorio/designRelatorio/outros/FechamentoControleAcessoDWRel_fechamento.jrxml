<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="FechamentoControleAcessoDWRel_fechamento" pageWidth="842" pageHeight="595" columnWidth="802" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20">
	<property name="ireport.zoom" value="1.9487171000000043"/>
	<property name="ireport.x" value="60"/>
	<property name="ireport.y" value="43"/>
	<parameter name="percentualLiberacao" class="java.lang.Double" isForPrompting="false"/>
	<parameter name="totalCli" class="java.lang.Integer"/>
	<parameter name="percentualCli" class="java.lang.Double"/>
	<parameter name="percentualCol" class="java.lang.Double"/>
	<parameter name="totalCol" class="java.lang.Integer"/>
	<parameter name="percentualCliCol" class="java.lang.Double" isForPrompting="false"/>
	<parameter name="totalAcessos" class="java.lang.Number" isForPrompting="false"/>
	<parameter name="totalAcessoLib" class="java.lang.Integer"/>
	<parameter name="totalJustificadoLib" class="java.lang.Integer"/>
	<parameter name="totalFaltaJustificarLib" class="java.lang.Integer"/>
	<parameter name="qtdBVs" class="java.lang.Integer"/>
	<field name="tipoLiberacaoEnum.descricao" class="java.lang.String"/>
	<field name="percentualAcessos" class="java.lang.Double"/>
	<field name="totalAcesso" class="java.lang.Integer"/>
	<field name="jaJustificado" class="java.lang.Integer"/>
	<field name="faltaJustificar" class="java.lang.Integer"/>
	<field name="tipoLiberacaoEnum.codigo" class="java.lang.Integer"/>
	<variable name="totalAcessos" class="java.lang.Integer" calculation="Sum">
		<variableExpression><![CDATA[$F{totalAcesso}/2]]></variableExpression>
	</variable>
	<variable name="totalJaJustificado" class="java.lang.Integer" calculation="Sum">
		<variableExpression><![CDATA[$F{jaJustificado}/2]]></variableExpression>
	</variable>
	<variable name="totalFaltaJustificar" class="java.lang.Integer" calculation="Sum">
		<variableExpression><![CDATA[$F{faltaJustificar}/2]]></variableExpression>
	</variable>
	<variable name="totalPercentualLiberacao" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[$F{percentualAcessos}/2]]></variableExpression>
	</variable>
	<background>
		<band splitType="Stretch"/>
	</background>
	<columnHeader>
		<band height="137" splitType="Prevent">
			<textField pattern="#,##0.00 %" isBlankWhenNull="false">
				<reportElement key="textField-214" x="0" y="108" width="800" height="13"/>
				<box>
					<topPen lineWidth="0.5" lineStyle="Solid"/>
					<leftPen lineWidth="0.5" lineStyle="Solid"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.5" lineStyle="Solid"/>
				</box>
				<textElement textAlignment="Left">
					<font fontName="Arial" size="10" isBold="true" isItalic="true" pdfFontName="Helvetica-BoldOblique"/>
				</textElement>
				<textFieldExpression class="java.lang.Double"><![CDATA[$P{percentualLiberacao}/100]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement key="staticText-4" x="37" y="108" width="149" height="13"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font isBold="true" isItalic="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[dos Acessos foram Liberação]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-4" x="163" y="121" width="67" height="14"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[% Acesso]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-4" x="231" y="121" width="67" height="14"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Total Acesso]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-4" x="299" y="121" width="75" height="14"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Já Justificado]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-4" x="375" y="121" width="75" height="14"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Falta Justificar]]></text>
			</staticText>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="false">
				<reportElement key="textField" x="236" y="60" width="66" height="14"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="9"/>
				</textElement>
				<textFieldExpression class="java.lang.Integer"><![CDATA[$P{totalCli}]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="false">
				<reportElement key="textField" x="236" y="73" width="66" height="14"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="9"/>
				</textElement>
				<textFieldExpression class="java.lang.Integer"><![CDATA[$P{totalCol}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00 %" isBlankWhenNull="false">
				<reportElement key="textField-214" x="0" y="32" width="800" height="13"/>
				<box>
					<topPen lineWidth="0.5" lineStyle="Solid"/>
					<leftPen lineWidth="0.5" lineStyle="Solid"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.5" lineStyle="Solid"/>
				</box>
				<textElement textAlignment="Left">
					<font fontName="Arial" size="10" isBold="true" isItalic="true" pdfFontName="Helvetica-BoldOblique"/>
				</textElement>
				<textFieldExpression class="java.lang.Double"><![CDATA[$P{percentualCliCol}/100]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement key="staticText-4" x="37" y="32" width="243" height="13"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font isBold="true" isItalic="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[dos Acessos foram de Clientes e Colaboradores]]></text>
			</staticText>
			<staticText>
				<reportElement x="12" y="76" width="59" height="14"/>
				<textElement>
					<font size="9" isBold="false"/>
				</textElement>
				<text><![CDATA[Colaborador]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-4" x="164" y="46" width="70" height="14"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[% Acesso]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-4" x="234" y="46" width="67" height="14"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Total Acesso]]></text>
			</staticText>
			<textField pattern="#,##0.00 %">
				<reportElement x="164" y="74" width="71" height="14"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="9"/>
				</textElement>
				<textFieldExpression class="java.lang.Double"><![CDATA[$P{percentualCol}/100]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00 %">
				<reportElement x="164" y="60" width="71" height="14"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="9"/>
				</textElement>
				<textFieldExpression class="java.lang.Double"><![CDATA[$P{percentualCli}/100]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="false">
				<reportElement key="textField" x="236" y="87" width="66" height="17"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="9" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.Integer"><![CDATA[$P{totalCol} + $P{totalCli}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="12" y="60" width="59" height="14"/>
				<textElement>
					<font size="9" isBold="false"/>
				</textElement>
				<text><![CDATA[Cliente]]></text>
			</staticText>
			<staticText>
				<reportElement x="12" y="91" width="59" height="14"/>
				<textElement>
					<font size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Total:]]></text>
			</staticText>
			<textField pattern="#,##0.00 %">
				<reportElement x="164" y="87" width="71" height="17"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="9" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.Double"><![CDATA[($P{percentualCli}+ $P{percentualCol})/100]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="false">
				<reportElement key="textField" x="145" y="1" width="135" height="21" isRemoveLineWhenBlank="true"/>
				<textElement verticalAlignment="Middle">
					<font size="12" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.Number"><![CDATA[$P{totalAcessos}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement key="staticText-12" x="1" y="1" width="144" height="21"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="12" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Total Geral de Acessos:]]></text>
			</staticText>
		</band>
	</columnHeader>
	<detail>
		<band height="16" splitType="Stretch">
			<textField isBlankWhenNull="false">
				<reportElement key="textField" x="298" y="1" width="76" height="14">
					<printWhenExpression><![CDATA[$F{tipoLiberacaoEnum.descricao}!=null]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="9"/>
				</textElement>
				<textFieldExpression class="java.lang.Integer"><![CDATA[$F{jaJustificado}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00 %" isBlankWhenNull="false">
				<reportElement key="textField" x="163" y="1" width="67" height="14">
					<printWhenExpression><![CDATA[$F{tipoLiberacaoEnum.descricao}!=null]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="9"/>
				</textElement>
				<textFieldExpression class="java.lang.Double"><![CDATA[$F{percentualAcessos}/100]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement key="textField" x="12" y="1" width="150" height="14" isRemoveLineWhenBlank="true"/>
				<textElement verticalAlignment="Middle">
					<font size="9"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{tipoLiberacaoEnum.descricao}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField" x="230" y="1" width="68" height="14">
					<printWhenExpression><![CDATA[$F{tipoLiberacaoEnum.descricao}!=null]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="9"/>
				</textElement>
				<textFieldExpression class="java.lang.Integer"><![CDATA[$F{totalAcesso}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField" x="374" y="1" width="76" height="14">
					<printWhenExpression><![CDATA[$F{tipoLiberacaoEnum.descricao}!=null]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="9"/>
				</textElement>
				<textFieldExpression class="java.lang.Integer"><![CDATA[$F{faltaJustificar}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField" x="499" y="0" width="36" height="15">
					<printWhenExpression><![CDATA[$F{tipoLiberacaoEnum.descricao}!=null && $F{tipoLiberacaoEnum.codigo}==4]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="9"/>
				</textElement>
				<textFieldExpression class="java.lang.Integer"><![CDATA[$P{qtdBVs}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement key="staticText-4" x="463" y="0" width="47" height="15">
					<printWhenExpression><![CDATA[$F{tipoLiberacaoEnum.descricao}!=null && $F{tipoLiberacaoEnum.codigo}==4]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="9" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Total BV:]]></text>
			</staticText>
		</band>
	</detail>
	<columnFooter>
		<band height="42">
			<staticText>
				<reportElement x="12" y="1" width="39" height="15"/>
				<textElement>
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Total:]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="233" y="1" width="66" height="15"/>
				<textElement textAlignment="Center">
					<font size="9" isBold="true" pdfFontName="Helvetica-Bold" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.Integer"><![CDATA[$P{totalAcessoLib}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="300" y="1" width="72" height="15"/>
				<textElement textAlignment="Center">
					<font size="9" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.Integer"><![CDATA[$P{totalJustificadoLib}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement x="375" y="1" width="75" height="15"/>
				<textElement textAlignment="Center">
					<font size="9" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.Integer"><![CDATA[$P{totalFaltaJustificarLib}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00 %" isBlankWhenNull="true">
				<reportElement x="163" y="1" width="66" height="15"/>
				<textElement textAlignment="Center">
					<font size="9" isBold="true" pdfFontName="Helvetica-Bold" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.Double"><![CDATA[$P{percentualLiberacao}/100]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement key="staticText-1" positionType="Float" x="0" y="25" width="799" height="17" isPrintInFirstWholeBand="true"/>
				<textElement verticalAlignment="Bottom">
					<font size="12" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Relação dos Acessos de Liberação]]></text>
			</staticText>
		</band>
	</columnFooter>
</jasperReport>

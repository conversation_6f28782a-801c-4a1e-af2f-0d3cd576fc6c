<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="RelatorioCliente" pageWidth="842" pageHeight="595" orientation="Landscape" columnWidth="786" leftMargin="28" rightMargin="28" topMargin="28" bottomMargin="28">
	<property name="ireport.zoom" value="2.7272727272727426"/>
	<property name="ireport.x" value="640"/>
	<property name="ireport.y" value="147"/>
	<parameter name="logoPadraoRelatorio" class="java.io.InputStream" isForPrompting="false"/>
	<parameter name="tituloRelatorio" class="java.lang.String" isForPrompting="false"/>
	<parameter name="versaoSoftware" class="java.lang.String" isForPrompting="false"/>
	<parameter name="usuario" class="java.lang.String" isForPrompting="false"/>
	<parameter name="filtros" class="java.lang.String" isForPrompting="false"/>
	<parameter name="SUBREPORT_DIR" class="java.lang.String">
		<defaultValueExpression><![CDATA["D:\\PactoJ\\Desenvolvimento\\Sistemas\\ZillyonWeb\\tronco-novo\\src\\java\\relatorio\\designRelatorio\\outros\\"]]></defaultValueExpression>
	</parameter>
	<parameter name="nomeEmpresa" class="java.lang.String"/>
	<parameter name="enderecoEmpresa" class="java.lang.String"/>
	<parameter name="cidadeEmpresa" class="java.lang.String"/>
	<parameter name="dataIni" class="java.lang.String" isForPrompting="false"/>
	<parameter name="dataFim" class="java.lang.String" isForPrompting="false"/>
	<parameter name="SUBREPORT_DIR1" class="java.lang.String"/>
	<parameter name="totalClientes" class="java.lang.String" isForPrompting="false"/>
	<parameter name="totalContratos" class="java.lang.String" isForPrompting="false"/>
	<parameter name="totalValor" class="java.lang.String" isForPrompting="false"/>
	<parameter name="totalCompetencia" class="java.lang.String" isForPrompting="false"/>
	<parameter name="listaTotais" class="java.lang.Object" isForPrompting="false"/>
	<parameter name="colMatricula" class="java.lang.Boolean"/>
	<parameter name="colNome" class="java.lang.Boolean"/>
	<parameter name="colSituacao" class="java.lang.Boolean"/>
	<parameter name="colVinculo" class="java.lang.Boolean"/>
	<parameter name="colPlano" class="java.lang.Boolean"/>
	<parameter name="colContrato" class="java.lang.Boolean"/>
	<parameter name="colModalidade" class="java.lang.Boolean"/>
	<parameter name="colValorModalidade" class="java.lang.Boolean"/>
	<parameter name="colDuracao" class="java.lang.Boolean"/>
	<parameter name="colDataLancamento" class="java.lang.Boolean"/>
	<parameter name="colInicio" class="java.lang.Boolean"/>
	<parameter name="colVence" class="java.lang.Boolean"/>
	<parameter name="colValor" class="java.lang.Boolean"/>
	<parameter name="colHorario" class="java.lang.Boolean"/>
	<parameter name="colFaturamento" class="java.lang.Boolean"/>
	<parameter name="colEmpresa" class="java.lang.Boolean"/>
	<parameter name="colCategoriaCliente" class="java.lang.Boolean"/>
	<field name="matricula" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="nome" class="java.lang.String"/>
	<field name="situacao" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="vinculo" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="plano" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="contrato" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="modalidade" class="java.lang.String"/>
	<field name="duracao" class="java.lang.String"/>
	<field name="horario" class="java.lang.String"/>
	<field name="inicio" class="java.lang.String"/>
	<field name="vence" class="java.lang.String"/>
	<field name="faturamento" class="java.lang.String"/>
	<field name="dataLancamento" class="java.lang.String"/>
	<field name="valorModalidade" class="java.lang.String"/>
	<field name="empresa" class="java.lang.String"/>
	<field name="categoriaCliente" class="java.lang.String"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band height="57" splitType="Stretch">
			<textField isBlankWhenNull="false">
				<reportElement key="textField-210" x="85" y="28" width="113" height="14"/>
				<textElement>
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{cidadeEmpresa}]]></textFieldExpression>
			</textField>
			<image isUsingCache="true" onErrorType="Blank" evaluationTime="Page">
				<reportElement key="image-1" x="0" y="0" width="82" height="46" isPrintWhenDetailOverflows="true"/>
				<box>
					<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<imageExpression class="java.io.InputStream"><![CDATA[$P{logoPadraoRelatorio}]]></imageExpression>
			</image>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-208" x="85" y="0" width="113" height="14"/>
				<textElement>
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{nomeEmpresa}]]></textFieldExpression>
			</textField>
			<textField evaluationTime="Report" isBlankWhenNull="false">
				<reportElement key="textField-212" x="756" y="36" width="31" height="17"/>
				<box bottomPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement>
					<font fontName="Arial" size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[" " + $V{PAGE_NUMBER} + ""]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-211" x="681" y="36" width="75" height="17"/>
				<box bottomPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right">
					<font fontName="Arial" size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA["Página: " + $V{PAGE_NUMBER} + " de "]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement key="staticText-13" x="269" y="23" width="263" height="27"/>
				<textElement textAlignment="Center">
					<font size="20" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Relatório de Clientes]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-14" mode="Opaque" x="524" y="0" width="262" height="23"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Top" lineSpacing="Single">
					<font fontName="Microsoft Sans Serif" size="9" isBold="true" isItalic="true" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica-BoldOblique"/>
				</textElement>
				<text><![CDATA[Zillyon - Sistema Administrativo para Academias Desenvolvido por PACTO Soluções Tecnológicas Ltda.]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-15" mode="Opaque" x="675" y="23" width="111" height="12"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Top" lineSpacing="Single">
					<font fontName="Microsoft Sans Serif" size="9" isBold="true" isItalic="true" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica-BoldOblique"/>
				</textElement>
				<text><![CDATA[(0xx62) 3251-5820]]></text>
			</staticText>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-209" x="85" y="14" width="113" height="14"/>
				<textElement>
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{enderecoEmpresa}]]></textFieldExpression>
			</textField>
		</band>
	</title>
	<columnHeader>
		<band height="56" splitType="Stretch">
			<staticText>
				<reportElement key="" positionType="Float" x="595" y="39" width="36" height="14">
					<printWhenExpression><![CDATA[$P{colDuracao}]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Duração]]></text>
			</staticText>
			<staticText>
				<reportElement key="" positionType="Float" x="680" y="39" width="51" height="14">
					<printWhenExpression><![CDATA[$P{colVence}]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Vencimento]]></text>
			</staticText>
			<staticText>
				<reportElement key="" positionType="Float" x="39" y="39" width="43" height="14">
					<printWhenExpression><![CDATA[$P{colSituacao}]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Situação]]></text>
			</staticText>
			<staticText>
				<reportElement key="" positionType="Float" x="731" y="39" width="55" height="14">
					<printWhenExpression><![CDATA[$P{colFaturamento}]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Right">
					<font size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Faturamento]]></text>
			</staticText>
			<staticText>
				<reportElement key="" positionType="Float" x="232" y="39" width="82" height="14">
					<printWhenExpression><![CDATA[$P{colHorario}]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Horário]]></text>
			</staticText>
			<staticText>
				<reportElement key="" positionType="Float" x="50" y="25" width="261" height="14">
					<printWhenExpression><![CDATA[$P{colNome}]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Nome]]></text>
			</staticText>
			<staticText>
				<reportElement key="" positionType="Float" x="0" y="25" width="50" height="14">
					<printWhenExpression><![CDATA[$P{colMatricula}]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Mat.]]></text>
			</staticText>
			<staticText>
				<reportElement key="" positionType="Float" x="631" y="39" width="34" height="14">
					<printWhenExpression><![CDATA[$P{colInicio}]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Início]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-1" positionType="Float" x="0" y="39" width="40" height="14">
					<printWhenExpression><![CDATA[$P{colContrato}]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Contrato]]></text>
			</staticText>
			<staticText>
				<reportElement key="" positionType="Float" x="150" y="39" width="26" height="14">
					<printWhenExpression><![CDATA[$P{colPlano}]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Plano]]></text>
			</staticText>
			<line>
				<reportElement key="line-4" positionType="Float" x="0" y="54" width="786" height="1"/>
			</line>
			<staticText>
				<reportElement key="" positionType="Float" x="412" y="25" width="112" height="14">
					<printWhenExpression><![CDATA[$P{colVinculo}]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Vínculo]]></text>
			</staticText>
			<textField isStretchWithOverflow="true" isBlankWhenNull="false">
				<reportElement key="textField-214" x="0" y="0" width="786" height="22"/>
				<box>
					<topPen lineWidth="0.5" lineStyle="Solid"/>
					<leftPen lineWidth="0.5" lineStyle="Solid"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.5" lineStyle="Solid"/>
				</box>
				<textElement textAlignment="Center" markup="html">
					<font fontName="Arial" size="10" isBold="true" isItalic="true" pdfFontName="Helvetica-BoldOblique"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{filtros}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement key="" positionType="Float" x="358" y="40" width="60" height="14">
					<printWhenExpression><![CDATA[$P{colModalidade}]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Modalidade]]></text>
			</staticText>
			<staticText>
				<reportElement key="" positionType="Float" x="464" y="40" width="56" height="14">
					<printWhenExpression><![CDATA[$P{colValorModalidade}]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Right">
					<font size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Vlr. Mod.]]></text>
			</staticText>
			<staticText>
				<reportElement key="" positionType="Float" x="532" y="40" width="50" height="14">
					<printWhenExpression><![CDATA[$P{colDataLancamento}]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Lançamento]]></text>
			</staticText>
			<staticText>
				<reportElement key="" positionType="Float" x="96" y="39" width="54" height="14">
					<printWhenExpression><![CDATA[$P{colEmpresa}]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Empresa]]></text>
			</staticText>
			<staticText>
				<reportElement key="" positionType="Float" x="314" y="40" width="43" height="14">
					<printWhenExpression><![CDATA[$P{colCategoriaCliente}]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Categoria]]></text>
			</staticText>
		</band>
	</columnHeader>
	<detail>
		<band height="14" splitType="Prevent">
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement key="textField-224" stretchType="RelativeToBandHeight" x="0" y="0" width="50" height="14">
					<printWhenExpression><![CDATA[$P{colMatricula}]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font size="10" isBold="true" isItalic="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{matricula}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement key="textField-224" stretchType="RelativeToBandHeight" x="50" y="0" width="261" height="14">
					<printWhenExpression><![CDATA[$P{colNome}]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font size="10" isBold="true" isItalic="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{nome}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement key="textField-224" stretchType="RelativeToBandHeight" x="410" y="0" width="376" height="14" isRemoveLineWhenBlank="true">
					<printWhenExpression><![CDATA[$P{colVinculo}]]></printWhenExpression>
				</reportElement>
				<textElement markup="html">
					<font size="10" isBold="false" isItalic="false" isStrikeThrough="false"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{vinculo}]]></textFieldExpression>
			</textField>
		</band>
		<band height="22">
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement key="textField-224" stretchType="RelativeToBandHeight" x="0" y="1" width="30" height="14">
					<printWhenExpression><![CDATA[$P{colContrato}]]></printWhenExpression>
				</reportElement>
				<textElement markup="html">
					<font size="8" isBold="false" isStrikeThrough="false"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{contrato}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement key="textField-224" stretchType="RelativeToBandHeight" x="30" y="1" width="66" height="14">
					<printWhenExpression><![CDATA[$P{colSituacao}]]></printWhenExpression>
				</reportElement>
				<textElement markup="html">
					<font size="8" isBold="false"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{situacao}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement key="textField-224" stretchType="RelativeToBandHeight" x="148" y="1" width="133" height="14">
					<printWhenExpression><![CDATA[$P{colPlano}]]></printWhenExpression>
				</reportElement>
				<textElement markup="html">
					<font size="8" isBold="false"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{plano}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement key="textField-224" stretchType="RelativeToBandHeight" x="354" y="1" width="110" height="14">
					<printWhenExpression><![CDATA[$P{colModalidade}]]></printWhenExpression>
				</reportElement>
				<textElement markup="html">
					<font size="8" isBold="false"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{modalidade}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement key="textField-224" stretchType="RelativeToBandHeight" x="591" y="1" width="40" height="14">
					<printWhenExpression><![CDATA[$P{colDuracao}]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Center" markup="html">
					<font size="8" isBold="false"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{duracao}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement key="textField-224" stretchType="RelativeToBandHeight" x="743" y="1" width="44" height="14">
					<printWhenExpression><![CDATA[$P{colFaturamento}]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Right" markup="html">
					<font size="8" isBold="false"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{faturamento}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement key="textField-224" stretchType="RelativeToBandHeight" x="680" y="1" width="63" height="14">
					<printWhenExpression><![CDATA[$P{colVence}]]></printWhenExpression>
				</reportElement>
				<textElement markup="html">
					<font size="8" isBold="false"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{vence}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement key="textField-224" stretchType="RelativeToBandHeight" x="631" y="1" width="49" height="14">
					<printWhenExpression><![CDATA[$P{colInicio}]]></printWhenExpression>
				</reportElement>
				<textElement markup="html">
					<font size="8" isBold="false"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{inicio}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement key="textField-224" stretchType="RelativeToBandHeight" x="229" y="1" width="82" height="14">
					<printWhenExpression><![CDATA[$P{colHorario}]]></printWhenExpression>
				</reportElement>
				<textElement markup="html">
					<font size="8" isBold="false"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{horario}]]></textFieldExpression>
			</textField>
			<line>
				<reportElement positionType="FixRelativeToBottom" x="1" y="18" width="786" height="1" forecolor="#CCCCCC"/>
			</line>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement key="textField-224" stretchType="RelativeToBandHeight" x="464" y="1" width="56" height="14">
					<printWhenExpression><![CDATA[$P{colValorModalidade}]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Right" markup="html">
					<font size="8" isBold="false"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{valorModalidade}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement key="textField-224" stretchType="RelativeToBandHeight" x="532" y="1" width="59" height="14">
					<printWhenExpression><![CDATA[$P{colDataLancamento}]]></printWhenExpression>
				</reportElement>
				<textElement markup="html">
					<font size="8" isBold="false"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{dataLancamento}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement key="textField-224" stretchType="RelativeToBandHeight" x="96" y="1" width="52" height="14">
					<printWhenExpression><![CDATA[$P{colEmpresa}]]></printWhenExpression>
				</reportElement>
				<textElement markup="html">
					<font size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{empresa}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement key="textField-224" stretchType="RelativeToBandHeight" x="313" y="1" width="41" height="14">
					<printWhenExpression><![CDATA[$P{colCategoriaCliente}]]></printWhenExpression>
				</reportElement>
				<textElement markup="html">
					<font size="8" isBold="false"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{categoriaCliente}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<pageFooter>
		<band height="17">
			<line>
				<reportElement key="line-6" x="0" y="1" width="786" height="1"/>
			</line>
			<textField pattern="dd/MM/yyyy HH.mm.ss" isBlankWhenNull="true">
				<reportElement key="dataRel-1" mode="Transparent" x="0" y="3" width="786" height="13" backcolor="#FFFFFF"/>
				<box bottomPadding="0">
					<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Verdana" size="8" isItalic="false" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression class="java.util.Date"><![CDATA[new Date()]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="false">
				<reportElement key="textField-207" x="0" y="3" width="786" height="13"/>
				<box>
					<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Verdana" size="8" isBold="false" isItalic="true" pdfFontName="Helvetica-Oblique"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[" "+" Usuário:" + $P{usuario}]]></textFieldExpression>
			</textField>
		</band>
	</pageFooter>
	<summary>
		<band height="70" splitType="Prevent">
			<staticText>
				<reportElement key="staticText-2" positionType="Float" x="0" y="14" width="96" height="14"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Total de Clientes: ]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-2" positionType="Float" x="0" y="28" width="96" height="14"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Total de Contratos: ]]></text>
			</staticText>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement key="textField-224" positionType="Float" x="96" y="28" width="100" height="14"/>
				<textElement textAlignment="Right" verticalAlignment="Middle" markup="html">
					<font size="10" isBold="false" isStrikeThrough="false"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{totalContratos}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement key="textField-224" positionType="Float" x="96" y="56" width="100" height="14"/>
				<textElement textAlignment="Right" verticalAlignment="Middle" markup="html">
					<font size="10" isBold="false" isStrikeThrough="false"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{totalValor}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement key="textField-224" positionType="Float" x="96" y="14" width="100" height="14"/>
				<textElement textAlignment="Right" verticalAlignment="Middle" markup="html">
					<font size="10" isBold="false" isStrikeThrough="false"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{totalClientes}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement key="staticText-2" positionType="Float" x="0" y="56" width="96" height="14"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Valor total: ]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-2" positionType="Float" x="0" y="42" width="96" height="14"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Valor por mês:]]></text>
			</staticText>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement key="textField-224" positionType="Float" x="96" y="42" width="100" height="14"/>
				<textElement textAlignment="Right" verticalAlignment="Middle" markup="html">
					<font size="10" isBold="false" isStrikeThrough="false"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{totalCompetencia}]]></textFieldExpression>
			</textField>
			<subreport>
				<reportElement key="subreport-1" x="432" y="26" width="354" height="12"/>
				<dataSourceExpression><![CDATA[$P{listaTotais}]]></dataSourceExpression>
				<subreportExpression class="java.lang.String"><![CDATA[$P{SUBREPORT_DIR} + "ResumoModalidades.jasper"]]></subreportExpression>
			</subreport>
			<staticText>
				<reportElement mode="Opaque" x="432" y="14" width="70" height="12" backcolor="#F0F0F0"/>
				<textElement>
					<font size="9" isBold="true"/>
				</textElement>
				<text><![CDATA[Modalidade]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="503" y="14" width="70" height="12" backcolor="#F0F0F0"/>
				<textElement textAlignment="Right">
					<font size="9" isBold="true"/>
				</textElement>
				<text><![CDATA[Clientes]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="574" y="14" width="70" height="12" backcolor="#F0F0F0"/>
				<textElement textAlignment="Right">
					<font size="9" isBold="true"/>
				</textElement>
				<text><![CDATA[Contratos]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="645" y="14" width="70" height="12" backcolor="#F0F0F0"/>
				<textElement textAlignment="Right">
					<font size="9" isBold="true"/>
				</textElement>
				<text><![CDATA[Valor por mês]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="716" y="14" width="70" height="12" backcolor="#F0F0F0"/>
				<textElement textAlignment="Right">
					<font size="9" isBold="true"/>
				</textElement>
				<text><![CDATA[Valor Total]]></text>
			</staticText>
		</band>
	</summary>
</jasperReport>

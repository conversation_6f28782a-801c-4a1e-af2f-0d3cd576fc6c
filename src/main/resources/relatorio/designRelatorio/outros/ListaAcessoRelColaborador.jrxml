<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="ListaAcessoRelColaborador" pageWidth="680" pageHeight="878" columnWidth="640" leftMargin="20" rightMargin="20" topMargin="10" bottomMargin="10" whenResourceMissingType="Empty">
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.encoding" value="ISO-8859-1"/>
	<property name="ireport.zoom" value="1.3636363636363635"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="java.util.*"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<subDataset name="Teste"/>
	<parameter name="logoPadraoRelatorio" class="java.io.InputStream" isForPrompting="false"/>
	<parameter name="tituloRelatorio" class="java.lang.String" isForPrompting="false"/>
	<parameter name="versaoSoftware" class="java.lang.String" isForPrompting="false"/>
	<parameter name="usuario" class="java.lang.String" isForPrompting="false"/>
	<parameter name="filtros" class="java.lang.String" isForPrompting="false"/>
	<parameter name="SUBREPORT_DIR" class="java.lang.String">
		<defaultValueExpression><![CDATA["D:\\PactoJ\\Desenvolvimento\\Sistemas\\ZillyonWeb\\tronco-novo\\src\\java\\relatorio\\designRelatorio\\outros\\"]]></defaultValueExpression>
	</parameter>
	<parameter name="nomeEmpresa" class="java.lang.String"/>
	<parameter name="enderecoEmpresa" class="java.lang.String"/>
	<parameter name="cidadeEmpresa" class="java.lang.String"/>
	<parameter name="dataIni" class="java.lang.String" isForPrompting="false"/>
	<parameter name="dataFim" class="java.lang.String" isForPrompting="false"/>
	<parameter name="SUBREPORT_DIR1" class="java.lang.String"/>
	<field name="colaborador.codigo" class="java.lang.Integer">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="intervaloDataHoras" class="java.lang.String"/>
	<field name="colaborador.pessoa.nome" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="dataHoraEntrada" class="java.util.Date">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="dataHoraSaida" class="java.util.Date">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="sentido" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="meioIdentificacaoEntrada.descricao" class="java.lang.String"/>
	<field name="localAcesso.empresa.nome" class="java.lang.String"/>
	<field name="colaborador.pessoa.email" class="java.lang.String"/>
	<variable name="totalcolaboradores" class="java.lang.Integer" calculation="Count">
		<variableExpression><![CDATA[$F{colaborador.codigo}]]></variableExpression>
	</variable>
	<group name="nomeColaborador">
		<groupExpression><![CDATA[$F{colaborador.pessoa.nome}]]></groupExpression>
		<groupHeader>
			<band height="16">
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement key="textField-224" x="8" y="1" width="630" height="15"/>
					<textElement>
						<font size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$F{colaborador.pessoa.nome} + ($F{colaborador.pessoa.email}.trim().equals("") || $F{colaborador.pessoa.email}==null ? "" :  (" - " + $F{colaborador.pessoa.email}))]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
		<groupFooter>
			<band height="18">
				<staticText>
					<reportElement x="368" y="3" width="93" height="15"/>
					<textElement>
						<font size="9" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<text><![CDATA[Total de Acessos:]]></text>
				</staticText>
				<textField>
					<reportElement x="461" y="3" width="143" height="15"/>
					<textElement>
						<font size="9"/>
					</textElement>
					<textFieldExpression class="java.lang.Integer"><![CDATA[$V{nomeColaborador_COUNT}]]></textFieldExpression>
				</textField>
			</band>
		</groupFooter>
	</group>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band splitType="Stretch"/>
	</title>
	<pageHeader>
		<band height="69" splitType="Stretch">
			<textField evaluationTime="Report" isBlankWhenNull="false">
				<reportElement key="textField-212" x="604" y="51" width="31" height="17"/>
				<box bottomPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement>
					<font fontName="Arial" size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[" " + $V{PAGE_NUMBER} + ""]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement key="staticText-15" mode="Opaque" x="522" y="35" width="111" height="12"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Top" lineSpacing="Single">
					<font fontName="Microsoft Sans Serif" size="9" isBold="true" isItalic="true" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica-BoldOblique"/>
				</textElement>
				<text><![CDATA[(0xx62) 3251-5820]]></text>
			</staticText>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-211" x="524" y="51" width="75" height="17"/>
				<box bottomPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right">
					<font fontName="Arial" size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA["Página: " + $V{PAGE_NUMBER} + " de "]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement key="staticText-13" x="244" y="35" width="189" height="27"/>
				<textElement textAlignment="Center">
					<font size="20" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Lista de Acessos]]></text>
			</staticText>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-209" x="86" y="31" width="113" height="14"/>
				<textElement>
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{enderecoEmpresa}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-208" x="86" y="15" width="113" height="14"/>
				<textElement>
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{nomeEmpresa}]]></textFieldExpression>
			</textField>
			<image isUsingCache="true" onErrorType="Blank" evaluationTime="Page">
				<reportElement key="image-1" x="0" y="16" width="82" height="46" isPrintWhenDetailOverflows="true"/>
				<box>
					<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<imageExpression class="java.io.InputStream"><![CDATA[$P{logoPadraoRelatorio}]]></imageExpression>
			</image>
			<staticText>
				<reportElement key="staticText-14" mode="Opaque" x="371" y="6" width="262" height="23"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Top" lineSpacing="Single">
					<font fontName="Microsoft Sans Serif" size="9" isBold="true" isItalic="true" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica-BoldOblique"/>
				</textElement>
				<text><![CDATA[Zillyon - Sistema Administrativo para Academias Desenvolvido por PACTO Soluções Tecnológicas Ltda.]]></text>
			</staticText>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-210" x="86" y="47" width="113" height="14"/>
				<textElement>
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{cidadeEmpresa}]]></textFieldExpression>
			</textField>
		</band>
	</pageHeader>
	<columnHeader>
		<band height="54" splitType="Stretch">
			<line>
				<reportElement key="line-1" x="0" y="2" width="640" height="1"/>
			</line>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-214" x="0" y="3" width="640" height="30"/>
				<box>
					<topPen lineWidth="0.5" lineStyle="Solid"/>
					<leftPen lineWidth="0.5" lineStyle="Solid"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.5" lineStyle="Solid"/>
				</box>
				<textElement textAlignment="Center">
					<font fontName="Arial" size="10" isBold="true" isItalic="true" pdfFontName="Helvetica-BoldOblique"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{filtros}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement key="staticText-2" x="1" y="39" width="40" height="14"/>
				<textElement>
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Código]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-8" x="41" y="39" width="88" height="14"/>
				<textElement>
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Data Entrada]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-8" x="129" y="39" width="125" height="14"/>
				<textElement>
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Data Saída]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-8" x="254" y="39" width="52" height="14"/>
				<textElement>
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Tempo]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-8" x="306" y="39" width="45" height="14"/>
				<textElement>
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Sentido]]></text>
			</staticText>
			<line>
				<reportElement key="line-1" x="0" y="53" width="640" height="1"/>
			</line>
			<staticText>
				<reportElement key="staticText-8" x="351" y="39" width="148" height="14"/>
				<textElement>
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Meio Identificação]]></text>
			</staticText>
		</band>
	</columnHeader>
	<detail>
		<band height="15" splitType="Stretch">
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement key="textField-223" x="254" y="1" width="52" height="14"/>
				<textElement/>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{intervaloDataHoras}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement key="textField" positionType="Float" x="306" y="1" width="43" height="14"/>
				<textElement textAlignment="Left" verticalAlignment="Top"/>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{sentido}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement key="textField-224" x="129" y="1" width="125" height="14"/>
				<textElement/>
				<textFieldExpression class="java.sql.Timestamp"><![CDATA[$F{dataHoraSaida}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement key="textField-224" x="41" y="1" width="91" height="14"/>
				<textElement>
					<font size="10" isBold="false"/>
				</textElement>
				<textFieldExpression class="java.sql.Timestamp"><![CDATA[$F{dataHoraEntrada}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement key="textField-223" x="1" y="1" width="40" height="14"/>
				<textElement/>
				<textFieldExpression class="java.lang.Integer"><![CDATA[$F{colaborador.codigo}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement key="textField" x="351" y="1" width="148" height="14"/>
				<textElement textAlignment="Left" verticalAlignment="Top"/>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{meioIdentificacaoEntrada.descricao}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<columnFooter>
		<band splitType="Stretch"/>
	</columnFooter>
	<pageFooter>
		<band splitType="Stretch"/>
	</pageFooter>
	<lastPageFooter>
		<band height="75" splitType="Stretch">
			<staticText>
				<reportElement x="369" y="20" width="123" height="20"/>
				<textElement>
					<font pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Total Geral de Acessos:]]></text>
			</staticText>
			<line>
				<reportElement key="line-4" x="1" y="11" width="640" height="1"/>
			</line>
			<line>
				<reportElement key="line-5" x="1" y="49" width="640" height="1"/>
			</line>
			<line>
				<reportElement key="line-6" x="1" y="51" width="640" height="1"/>
			</line>
			<textField pattern="" isBlankWhenNull="false">
				<reportElement key="textField-207" x="1" y="55" width="640" height="19"/>
				<box>
					<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="8" isItalic="true" pdfFontName="Helvetica-Oblique"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[" "+" Usuário:" + $P{usuario}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="492" y="20" width="100" height="20"/>
				<textElement/>
				<textFieldExpression class="java.lang.Integer"><![CDATA[$V{totalcolaboradores}]]></textFieldExpression>
			</textField>
			<textField pattern="dd/MM/yyyy HH.mm.ss" isBlankWhenNull="true">
				<reportElement key="dataRel-1" mode="Opaque" x="512" y="59" width="126" height="13" backcolor="#FFFFFF"/>
				<box bottomPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle" lineSpacing="Single">
					<font fontName="Arial" size="8" isBold="false" isItalic="true" isUnderline="false" pdfFontName="Helvetica-Oblique"/>
				</textElement>
				<textFieldExpression class="java.util.Date"><![CDATA[new Date()]]></textFieldExpression>
			</textField>
		</band>
	</lastPageFooter>
	<summary>
		<band splitType="Stretch"/>
	</summary>
</jasperReport>

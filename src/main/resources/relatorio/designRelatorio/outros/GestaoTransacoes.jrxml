<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="GestaoTransacoes" pageWidth="595" pageHeight="842" columnWidth="555" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20">
	<property name="ireport.zoom" value="1.3310000000000004"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="5"/>
	<style name="table">
		<box>
			<pen lineWidth="1.0" lineColor="#000000"/>
		</box>
	</style>
	<style name="table_TH" mode="Opaque" backcolor="#F0F8FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="table_CH" mode="Opaque" backcolor="#BFE1FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="table_TD" mode="Opaque" backcolor="#FFFFFF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<parameter name="logoPadraoRelatorio" class="java.io.InputStream" isForPrompting="false"/>
	<parameter name="tituloRelatorio" class="java.lang.String" isForPrompting="false"/>
	<parameter name="versaoSoftware" class="java.lang.String" isForPrompting="false"/>
	<parameter name="usuario" class="java.lang.String" isForPrompting="false"/>
	<parameter name="filtros" class="java.lang.String" isForPrompting="false"/>
	<parameter name="SUBREPORT_DIR" class="java.lang.String">
		<defaultValueExpression><![CDATA["D:\\PactoJ\\Desenvolvimento\\Sistemas\\ZillyonWeb\\tronco-novo\\src\\java\\relatorio\\designRelatorio\\outros\\"]]></defaultValueExpression>
	</parameter>
	<parameter name="nomeEmpresa" class="java.lang.String"/>
	<parameter name="enderecoEmpresa" class="java.lang.String"/>
	<parameter name="cidadeEmpresa" class="java.lang.String"/>
	<parameter name="dataIni" class="java.lang.String" isForPrompting="false"/>
	<parameter name="dataFim" class="java.lang.String" isForPrompting="false"/>
	<parameter name="SUBREPORT_DIR1" class="java.lang.String"/>
	<parameter name="dadosTotalPorSituacao" class="java.lang.Object" isForPrompting="false"/>
	<parameter name="dadosValoresPorSituacao" class="java.lang.Object" isForPrompting="false"/>
	<parameter name="dadosValoresParcelas" class="java.lang.Object" isForPrompting="false"/>
	<parameter name="totalTransacoesCobradas" class="java.lang.Integer"/>
	<field name="codigo" class="java.lang.Integer"/>
	<field name="codigoExterno" class="java.lang.String"/>
	<field name="valor_Apresentar" class="java.lang.String"/>
	<field name="nomePessoa" class="java.lang.String"/>
	<field name="situacao_Apresentar" class="java.lang.String"/>
	<field name="autorizacao" class="java.lang.String"/>
	<field name="bandeira" class="java.lang.String"/>
	<field name="cartaoMascarado" class="java.lang.String"/>
	<field name="dataProcessamento_Apresentar" class="java.lang.String"/>
	<field name="nomeUsuario" class="java.lang.String"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band height="105" splitType="Stretch">
			<textField evaluationTime="Report" isBlankWhenNull="false">
				<reportElement key="textField-212" x="526" y="40" width="31" height="17"/>
				<box bottomPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement>
					<font fontName="Arial" size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[" " + $V{PAGE_NUMBER} + ""]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-208" x="96" y="5" width="113" height="14"/>
				<textElement>
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{nomeEmpresa}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-209" x="96" y="21" width="113" height="14"/>
				<textElement>
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{enderecoEmpresa}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement key="staticText-14" mode="Opaque" x="295" y="5" width="262" height="23"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Top">
					<font fontName="Microsoft Sans Serif" size="9" isBold="true" isItalic="true" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica-BoldOblique"/>
				</textElement>
				<text><![CDATA[Zillyon - Sistema Administrativo para Academias Desenvolvido por PACTO Soluções Tecnológicas Ltda.]]></text>
			</staticText>
			<image vAlign="Middle" isUsingCache="true" onErrorType="Blank" evaluationTime="Page">
				<reportElement key="image-1" x="10" y="6" width="82" height="46" isPrintWhenDetailOverflows="true"/>
				<box>
					<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<imageExpression class="java.io.InputStream"><![CDATA[$P{logoPadraoRelatorio}]]></imageExpression>
			</image>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-211" x="444" y="40" width="75" height="17"/>
				<box bottomPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right">
					<font fontName="Arial" size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA["Página: " + $V{PAGE_NUMBER} + " de "]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement key="staticText-15" mode="Opaque" x="444" y="28" width="111" height="12"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Top">
					<font fontName="Microsoft Sans Serif" size="9" isBold="true" isItalic="true" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica-BoldOblique"/>
				</textElement>
				<text><![CDATA[(0xx62) 3251-5820]]></text>
			</staticText>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-210" x="96" y="37" width="113" height="14"/>
				<textElement>
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{cidadeEmpresa}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement key="staticText-13" x="2" y="57" width="555" height="23"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="18" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Gestão de Transações]]></text>
			</staticText>
			<textField isStretchWithOverflow="true" isBlankWhenNull="false">
				<reportElement key="textField-214" x="2" y="80" width="553" height="14"/>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="html">
					<font fontName="Arial" size="7" isBold="true" isItalic="true" pdfFontName="Helvetica-BoldOblique"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{filtros}]]></textFieldExpression>
			</textField>
		</band>
	</title>
	<columnHeader>
		<band height="21" splitType="Stretch">
			<staticText>
				<reportElement key="staticText-1" positionType="Float" x="0" y="0" width="58" height="14"/>
				<textElement>
					<font size="9" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Transação]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-1" positionType="Float" x="104" y="0" width="86" height="14"/>
				<textElement>
					<font size="9" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Titular]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-1" positionType="Float" x="59" y="0" width="45" height="14"/>
				<textElement>
					<font size="9" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Valor]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-1" positionType="Float" x="190" y="0" width="52" height="14"/>
				<textElement>
					<font size="9" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Sit.]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-1" positionType="Float" x="252" y="0" width="40" height="14"/>
				<textElement>
					<font size="9" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Aut.]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-1" positionType="Float" x="292" y="0" width="52" height="14"/>
				<textElement>
					<font size="9" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Band.]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-1" positionType="Float" x="344" y="0" width="72" height="14"/>
				<textElement>
					<font size="9" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Cartão]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-1" positionType="Float" x="416" y="0" width="100" height="14"/>
				<textElement>
					<font size="9" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Data/Hora]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-1" positionType="Float" x="516" y="0" width="39" height="14"/>
				<textElement>
					<font size="9" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Usuário]]></text>
			</staticText>
			<line>
				<reportElement x="0" y="14" width="555" height="1"/>
			</line>
		</band>
	</columnHeader>
	<detail>
		<band height="14" splitType="Stretch">
			<textField>
				<reportElement x="2" y="0" width="58" height="11"/>
				<textElement>
					<font size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{codigoExterno}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="59" y="0" width="45" height="11"/>
				<textElement>
					<font size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{valor_Apresentar}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="104" y="0" width="86" height="11"/>
				<textElement>
					<font size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{nomePessoa}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="190" y="0" width="52" height="11"/>
				<textElement>
					<font size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{situacao_Apresentar}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="252" y="0" width="40" height="11"/>
				<textElement>
					<font size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{autorizacao}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="292" y="0" width="52" height="11"/>
				<textElement>
					<font size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{bandeira}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="344" y="0" width="72" height="11"/>
				<textElement>
					<font size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{cartaoMascarado}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="416" y="0" width="100" height="11"/>
				<textElement>
					<font size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{dataProcessamento_Apresentar}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="516" y="0" width="39" height="11"/>
				<textElement>
					<font size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{nomeUsuario}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<summary>
		<band height="99">
			<subreport isUsingCache="false">
				<reportElement key="subreport1" x="10" y="48" width="141" height="23"/>
				<dataSourceExpression><![CDATA[$P{dadosTotalPorSituacao}]]></dataSourceExpression>
				<subreportExpression class="java.lang.String"><![CDATA[$P{SUBREPORT_DIR} + "GestaoTransacoes_subQtdSituacao.jasper"]]></subreportExpression>
			</subreport>
			<subreport isUsingCache="false">
				<reportElement key="subreport3" x="414" y="48" width="141" height="23"/>
				<dataSourceExpression><![CDATA[$P{dadosValoresParcelas}]]></dataSourceExpression>
				<subreportExpression class="java.lang.String"><![CDATA[$P{SUBREPORT_DIR} + "GestaoTransacoes_subValoresParcelas.jasper"]]></subreportExpression>
			</subreport>
			<textField>
				<reportElement x="31" y="32" width="447" height="13"/>
				<textElement>
					<font size="9" isBold="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA["* "+ $P{totalTransacoesCobradas}.toString() + " transações estão passíveis de tarifação."]]></textFieldExpression>
			</textField>
			<subreport isUsingCache="false">
				<reportElement key="subreport2" x="209" y="48" width="141" height="23"/>
				<dataSourceExpression><![CDATA[$P{dadosValoresPorSituacao}]]></dataSourceExpression>
				<subreportExpression class="java.lang.String"><![CDATA[$P{SUBREPORT_DIR} + "GestaoTransacoes_subValoresTransacoes.jasper"]]></subreportExpression>
			</subreport>
			<staticText>
				<reportElement x="2" y="4" width="553" height="21"/>
				<textElement textAlignment="Center">
					<font size="16" isBold="true"/>
				</textElement>
				<text><![CDATA[Sumário]]></text>
			</staticText>
			<line>
				<reportElement x="0" y="3" width="555" height="1"/>
			</line>
			<line>
				<reportElement x="0" y="25" width="555" height="1"/>
			</line>
		</band>
	</summary>
</jasperReport>

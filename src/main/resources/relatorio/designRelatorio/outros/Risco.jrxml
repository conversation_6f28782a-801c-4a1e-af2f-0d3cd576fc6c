<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="Risco" pageWidth="595" pageHeight="842" columnWidth="591" leftMargin="2" rightMargin="2" topMargin="2" bottomMargin="2" whenResourceMissingType="Empty">
	<property name="ireport.zoom" value="1.5"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.encoding" value="ISO-8859-1"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="java.util.*"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<subDataset name="Teste"/>
	<parameter name="logoPadraoRelatorio" class="java.io.InputStream" isForPrompting="false"/>
	<parameter name="nomeEmpresa" class="java.lang.String" isForPrompting="false"/>
	<parameter name="usuario" class="java.lang.String" isForPrompting="false"/>
	<parameter name="filtros" class="java.lang.String" isForPrompting="false"/>
	<queryString>
		<![CDATA[]]>
	</queryString>
	<field name="peso" class="java.lang.Integer"/>
	<field name="nomeCliente" class="java.lang.String"/>
	<field name="matriculaCliente" class="java.lang.String"/>
	<field name="foneCliente" class="java.lang.String"/>
	<variable name="total" class="java.lang.Integer" calculation="Count">
		<variableExpression><![CDATA[$F{matriculaCliente}]]></variableExpression>
	</variable>
	<variable name="total.pagina" class="java.lang.Integer" resetType="Page" calculation="Count">
		<variableExpression><![CDATA[$F{matriculaCliente}]]></variableExpression>
	</variable>
	<variable name="total.peso" class="java.lang.Integer" resetType="Group" resetGroup="cliente" calculation="Count">
		<variableExpression><![CDATA[$F{peso}]]></variableExpression>
	</variable>
	<group name="cliente" isReprintHeaderOnEachPage="true" keepTogether="true">
		<groupExpression><![CDATA[$F{peso}]]></groupExpression>
		<groupHeader>
			<band height="16" splitType="Stretch">
				<staticText>
					<reportElement key="staticText-3" x="113" y="0" width="265" height="16"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="12" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<text><![CDATA[ Nome]]></text>
				</staticText>
				<staticText>
					<reportElement key="staticText-11" x="378" y="0" width="38" height="16"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="12" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<text><![CDATA[Risco]]></text>
				</staticText>
				<staticText>
					<reportElement key="staticText-12" x="44" y="0" width="70" height="16"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="12" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<text><![CDATA[ Matrícula]]></text>
				</staticText>
				<staticText>
					<reportElement key="staticText-11" x="416" y="0" width="124" height="16"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="12" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<text><![CDATA[Telefone]]></text>
				</staticText>
			</band>
		</groupHeader>
		<groupFooter>
			<band height="37" splitType="Stretch">
				<rectangle>
					<reportElement x="416" y="10" width="125" height="14" backcolor="#CCCCCC"/>
					<graphicElement>
						<pen lineWidth="0.0"/>
					</graphicElement>
				</rectangle>
				<textField isBlankWhenNull="false">
					<reportElement key="textField-211" x="502" y="10" width="38" height="14"/>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font size="10" isBold="true" pdfFontName="Helvetica"/>
					</textElement>
					<textFieldExpression class="java.lang.Integer"><![CDATA[$V{total.peso}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement key="staticText-16" x="416" y="10" width="81" height="14"/>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font size="10" isBold="true" pdfFontName="Helvetica"/>
					</textElement>
					<text><![CDATA[Total:]]></text>
				</staticText>
			</band>
		</groupFooter>
	</group>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band splitType="Stretch"/>
	</title>
	<pageHeader>
		<band height="99" splitType="Stretch">
			<image isUsingCache="true" onErrorType="Blank" evaluationTime="Page">
				<reportElement key="image-1" x="1" y="6" width="82" height="52" isPrintWhenDetailOverflows="true"/>
				<box>
					<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<imageExpression class="java.io.InputStream"><![CDATA[$P{logoPadraoRelatorio}]]></imageExpression>
			</image>
			<staticText>
				<reportElement key="staticText-17" mode="Opaque" x="319" y="2" width="268" height="23"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Top" lineSpacing="Single">
					<font fontName="Microsoft Sans Serif" size="9" isBold="true" isItalic="true" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica-BoldOblique"/>
				</textElement>
				<text><![CDATA[Zillyon - Sistema Administrativo para Academias Desenvolvido por PACTO Soluções Tecnológicas Ltda.]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-18" mode="Opaque" x="476" y="30" width="111" height="12"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Top" lineSpacing="Single">
					<font fontName="Microsoft Sans Serif" size="9" isBold="true" isItalic="true" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica-BoldOblique"/>
				</textElement>
				<text><![CDATA[(0xx62) 3251-5820]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-19" x="83" y="37" width="392" height="27"/>
				<textElement textAlignment="Center">
					<font size="20" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Grupo de Risco]]></text>
			</staticText>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-212" x="95" y="4" width="200" height="14"/>
				<textElement>
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{nomeEmpresa}]]></textFieldExpression>
			</textField>
			<textField evaluationTime="Report" isBlankWhenNull="false">
				<reportElement key="textField-215" x="557" y="47" width="31" height="17"/>
				<box bottomPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement>
					<font fontName="Arial" size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[" " + $V{PAGE_NUMBER} + ""]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-216" x="475" y="47" width="82" height="17"/>
				<box bottomPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right">
					<font fontName="Arial" size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA["Página: " + $V{PAGE_NUMBER} + " de "]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-218" x="6" y="73" width="580" height="25"/>
				<box>
					<topPen lineWidth="0.5" lineStyle="Solid"/>
					<leftPen lineWidth="0.5" lineStyle="Solid"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.5" lineStyle="Solid"/>
				</box>
				<textElement textAlignment="Center">
					<font fontName="Arial" size="10" isBold="true" isItalic="true" pdfFontName="Helvetica-BoldOblique"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{filtros}]]></textFieldExpression>
			</textField>
		</band>
	</pageHeader>
	<columnHeader>
		<band splitType="Stretch"/>
	</columnHeader>
	<detail>
		<band height="15" splitType="Stretch">
			<rectangle>
				<reportElement key="rectangle-1" mode="Opaque" x="44" y="1" width="496" height="14" backcolor="#F0F0F0">
					<printWhenExpression><![CDATA[new Boolean(($V{COLUMN_COUNT}.intValue()%2)!=0)]]></printWhenExpression>
				</reportElement>
				<graphicElement>
					<pen lineWidth="0.0" lineStyle="Solid"/>
				</graphicElement>
			</rectangle>
			<textField isBlankWhenNull="false">
				<reportElement key="textField" x="46" y="0" width="70" height="14"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="10"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{matriculaCliente}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField" x="113" y="0" width="265" height="14"/>
				<textElement verticalAlignment="Middle">
					<font size="10"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{nomeCliente}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField" x="378" y="0" width="38" height="14"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="10"/>
				</textElement>
				<textFieldExpression class="java.lang.Integer"><![CDATA[$F{peso}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField" x="416" y="0" width="124" height="14"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="10"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{foneCliente}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<columnFooter>
		<band splitType="Stretch"/>
	</columnFooter>
	<pageFooter>
		<band height="40" splitType="Stretch">
			<textField pattern="" isBlankWhenNull="false">
				<reportElement key="textField-207" x="46" y="3" width="493" height="14"/>
				<box>
					<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="8" isItalic="true" pdfFontName="Helvetica-Oblique"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[" "+" Usuário:" + $P{usuario}]]></textFieldExpression>
			</textField>
			<textField pattern="dd/MM/yyyy HH.mm.ss" isBlankWhenNull="true">
				<reportElement key="dataRel-1" mode="Transparent" x="46" y="3" width="493" height="14" backcolor="#FFFFFF"/>
				<box bottomPadding="0">
					<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Verdana" size="8" isItalic="false" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression class="java.util.Date"><![CDATA[new Date()]]></textFieldExpression>
			</textField>
		</band>
	</pageFooter>
	<summary>
		<band height="40" splitType="Stretch">
			<rectangle>
				<reportElement x="295" y="1" width="245" height="14" backcolor="#CCCCCC"/>
				<graphicElement>
					<pen lineWidth="0.0"/>
				</graphicElement>
			</rectangle>
			<textField evaluationTime="Report" isBlankWhenNull="false">
				<reportElement key="textField-211" x="500" y="1" width="38" height="14"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="10" isBold="true" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression class="java.lang.Integer"><![CDATA[$V{total}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement key="staticText-16" x="416" y="1" width="81" height="14"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="10" isBold="true" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Total de Clientes]]></text>
			</staticText>
		</band>
	</summary>
</jasperReport>

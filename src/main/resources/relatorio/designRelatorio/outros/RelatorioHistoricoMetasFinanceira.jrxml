<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="RelatorioHistoricoMetasFinanceira" pageWidth="842" pageHeight="595" orientation="Landscape" columnWidth="802" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20">
	<property name="ireport.zoom" value="1.0"/>
	<property name="ireport.x" value="83"/>
	<property name="ireport.y" value="0"/>
	<parameter name="logoPadraoRelatorio" class="java.io.InputStream" isForPrompting="false"/>
	<parameter name="tituloRelatorio" class="java.lang.String" isForPrompting="false"/>
	<parameter name="versaoSoftware" class="java.lang.String" isForPrompting="false"/>
	<parameter name="usuario" class="java.lang.String" isForPrompting="false"/>
	<parameter name="filtros" class="java.lang.String" isForPrompting="false"/>
	<parameter name="nomeEmpresa" class="java.lang.String"/>
	<parameter name="enderecoEmpresa" class="java.lang.String"/>
	<parameter name="cidadeEmpresa" class="java.lang.String"/>
	<parameter name="SUBREPORT_DIR" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA["C:\\PactoJ\\Desenvolvimento\\Sistemas\\ZillyonWebx\\tronco-novo\\src\\java\\relatorio\\designRelatorio\\outros\\"]]></defaultValueExpression>
	</parameter>
	<field name="empresa.nome" class="java.lang.String"/>
	<field name="mes.descricao" class="java.lang.String"/>
	<field name="ano" class="java.lang.Integer"/>
	<field name="listaValoresMeta" class="java.lang.Object"/>
	<field name="metaAtingida" class="java.lang.Double"/>
	<field name="descricao" class="java.lang.String"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<pageHeader>
		<band height="88" splitType="Stretch">
			<textField isBlankWhenNull="true">
				<reportElement key="textField-214" x="0" y="60" width="802" height="23"/>
				<box>
					<topPen lineWidth="0.5" lineStyle="Solid"/>
					<leftPen lineWidth="0.5" lineStyle="Solid"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.5" lineStyle="Solid"/>
				</box>
				<textElement textAlignment="Center">
					<font fontName="Arial" size="10" isBold="true" isItalic="true" pdfFontName="Helvetica-BoldOblique"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{filtros}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-211" x="696" y="39" width="75" height="17"/>
				<box bottomPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right">
					<font fontName="Arial" size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA["Página: " + $V{PAGE_NUMBER} + " de "]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-210" x="85" y="34" width="175" height="14"/>
				<textElement>
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{cidadeEmpresa}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement key="staticText-13" x="260" y="21" width="431" height="27"/>
				<textElement textAlignment="Center">
					<font size="20" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Histórico de Metas Financeiras]]></text>
			</staticText>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-209" x="85" y="19" width="175" height="14"/>
				<textElement>
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{enderecoEmpresa}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement key="staticText-15" mode="Opaque" x="691" y="27" width="111" height="12"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Top" lineSpacing="Single">
					<font fontName="Microsoft Sans Serif" size="9" isBold="true" isItalic="true" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica-BoldOblique"/>
				</textElement>
				<text><![CDATA[(0xx62) 3251-5820]]></text>
			</staticText>
			<textField evaluationTime="Report" isBlankWhenNull="false">
				<reportElement key="textField-212" x="771" y="39" width="31" height="17"/>
				<box bottomPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement>
					<font fontName="Arial" size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[" " + $V{PAGE_NUMBER} + ""]]></textFieldExpression>
			</textField>
			<image isUsingCache="true" onErrorType="Blank" evaluationTime="Page">
				<reportElement key="image-1" x="0" y="4" width="82" height="46" isPrintWhenDetailOverflows="true"/>
				<box>
					<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<imageExpression class="java.io.InputStream"><![CDATA[$P{logoPadraoRelatorio}]]></imageExpression>
			</image>
			<staticText>
				<reportElement key="staticText-14" mode="Opaque" x="540" y="3" width="262" height="23"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Top" lineSpacing="Single">
					<font fontName="Microsoft Sans Serif" size="9" isBold="true" isItalic="true" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica-BoldOblique"/>
				</textElement>
				<text><![CDATA[Zillyon - Sistema Administrativo para Academias Desenvolvido por PACTO Soluções Tecnológicas Ltda.]]></text>
			</staticText>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-208" x="85" y="3" width="175" height="14"/>
				<textElement>
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{nomeEmpresa}]]></textFieldExpression>
			</textField>
		</band>
	</pageHeader>
	<columnHeader>
		<band height="43" splitType="Stretch">
			<staticText>
				<reportElement key="staticText-1" positionType="Float" x="0" y="28" width="61" height="14" isPrintInFirstWholeBand="true"/>
				<textElement textAlignment="Left">
					<font size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Empresa]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-1" positionType="Float" x="144" y="28" width="61" height="14" isPrintInFirstWholeBand="true"/>
				<textElement textAlignment="Left">
					<font size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Mês]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-1" positionType="Float" x="248" y="28" width="90" height="14" isPrintInFirstWholeBand="true"/>
				<textElement textAlignment="Left">
					<font size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Descrição]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-1" positionType="Float" x="208" y="28" width="36" height="14" isPrintInFirstWholeBand="true"/>
				<textElement textAlignment="Left">
					<font size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Ano]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-1" positionType="Float" x="486" y="28" width="58" height="14" isPrintInFirstWholeBand="true"/>
				<textElement textAlignment="Right">
					<font size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Meta 2]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-1" positionType="Float" x="547" y="28" width="58" height="14" isPrintInFirstWholeBand="true"/>
				<textElement textAlignment="Right">
					<font size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Meta 3]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-1" positionType="Float" x="608" y="28" width="58" height="14" isPrintInFirstWholeBand="true"/>
				<textElement textAlignment="Right">
					<font size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Meta 4]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-1" positionType="Float" x="668" y="28" width="58" height="14" isPrintInFirstWholeBand="true"/>
				<textElement textAlignment="Right">
					<font size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Meta 5]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-1" positionType="Float" x="729" y="28" width="73" height="14" isPrintInFirstWholeBand="true"/>
				<textElement textAlignment="Right">
					<font size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Meta Atingida]]></text>
			</staticText>
			<line>
				<reportElement x="0" y="42" width="802" height="1"/>
			</line>
			<staticText>
				<reportElement key="staticText-1" positionType="Float" x="426" y="28" width="58" height="14" isPrintInFirstWholeBand="true"/>
				<textElement textAlignment="Right">
					<font size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Meta 1]]></text>
			</staticText>
		</band>
	</columnHeader>
	<detail>
		<band height="15" splitType="Stretch">
			<textField pattern="">
				<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="132" height="15" isPrintInFirstWholeBand="true"/>
				<textElement>
					<font size="9" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{empresa.nome}]]></textFieldExpression>
			</textField>
			<textField pattern="">
				<reportElement stretchType="RelativeToBandHeight" x="144" y="0" width="61" height="15" isPrintInFirstWholeBand="true"/>
				<textElement>
					<font size="9" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{mes.descricao}]]></textFieldExpression>
			</textField>
			<textField pattern="">
				<reportElement stretchType="RelativeToBandHeight" x="208" y="0" width="36" height="15" isPrintInFirstWholeBand="true"/>
				<textElement>
					<font size="9" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression class="java.lang.Integer"><![CDATA[$F{ano}]]></textFieldExpression>
			</textField>
			<textField pattern="">
				<reportElement stretchType="RelativeToBandHeight" x="248" y="0" width="127" height="15" isPrintInFirstWholeBand="true"/>
				<textElement>
					<font size="9" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{descricao}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00">
				<reportElement stretchType="RelativeToBandHeight" x="729" y="0" width="73" height="15" isPrintInFirstWholeBand="true"/>
				<textElement textAlignment="Right">
					<font size="9" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression class="java.lang.Double"><![CDATA[$F{metaAtingida}]]></textFieldExpression>
			</textField>
			<subreport>
				<reportElement x="426" y="0" width="300" height="15"/>
				<dataSourceExpression><![CDATA[$F{listaValoresMeta}]]></dataSourceExpression>
				<subreportExpression class="java.lang.String"><![CDATA[$P{SUBREPORT_DIR} + "RelatorioHistoricoMetasFinanceira_subreport1.jasper"]]></subreportExpression>
			</subreport>
		</band>
	</detail>
	<lastPageFooter>
		<band height="50">
			<textField pattern="" isBlankWhenNull="false">
				<reportElement key="textField-207" x="1" y="29" width="800" height="19"/>
				<box>
					<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="8" isItalic="true" pdfFontName="Helvetica-Oblique"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[" "+" Usuário:" + $P{usuario}]]></textFieldExpression>
			</textField>
			<textField pattern="dd/MM/yyyy HH.mm.ss" isBlankWhenNull="true">
				<reportElement key="dataRel-1" mode="Opaque" x="666" y="34" width="126" height="13" backcolor="#FFFFFF"/>
				<box bottomPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle" lineSpacing="Single">
					<font fontName="Arial" size="8" isBold="false" isItalic="true" isUnderline="false" pdfFontName="Helvetica-Oblique"/>
				</textElement>
				<textFieldExpression class="java.util.Date"><![CDATA[new Date()]]></textFieldExpression>
			</textField>
		</band>
	</lastPageFooter>
</jasperReport>

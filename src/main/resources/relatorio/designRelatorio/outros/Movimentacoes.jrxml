<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="Movimentacoes" pageWidth="842" pageHeight="595" orientation="Landscape" columnWidth="802" leftMargin="20" rightMargin="20" topMargin="2" bottomMargin="2" whenResourceMissingType="Empty">
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.encoding" value="ISO-8859-1"/>
	<property name="ireport.zoom" value="1.0"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="java.util.*"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<parameter name="tituloRelatorio" class="java.lang.String"/>
	<parameter name="logoPadraoRelatorio" class="java.io.InputStream"/>
	<parameter name="usuario" class="java.lang.String"/>
	<parameter name="totalEntrada" class="java.lang.String"/>
	<parameter name="totalSaida" class="java.lang.String"/>
	<parameter name="periodo" class="java.lang.String"/>
	<parameter name="valorInicial" class="java.lang.String"/>
	<parameter name="valorFinal" class="java.lang.String"/>
	<parameter name="nomeEmpresa" class="java.lang.String"/>
	<parameter name="enderecoEmpresa" class="java.lang.String"/>
	<parameter name="cidadeEmpresa" class="java.lang.String"/>
	<queryString>
		<![CDATA[]]>
	</queryString>
	<field name="movConta.dataQuitacao_ApresentarSemHora" class="java.lang.String"/>
	<field name="descricao" class="java.lang.String"/>
	<field name="numeroDocumento" class="java.lang.String"/>
	<field name="formaPagamentoVO.descricao" class="java.lang.String"/>
	<field name="movConta.tipoOperacaoLancamento.descricaoCurta" class="java.lang.String"/>
	<field name="tipoES.descricaoCurta" class="java.lang.String"/>
	<field name="movConta.lote.codigo" class="java.lang.Integer"/>
	<field name="valorFormatado" class="java.lang.String"/>
	<field name="movConta.favorecido" class="java.lang.String"/>
	<field name="saldoFormatado" class="java.lang.String"/>
	<variable name="tipoDescricao" class="java.lang.String">
		<variableExpression><![CDATA[]]></variableExpression>
	</variable>
	<pageHeader>
		<band height="36">
			<textField isBlankWhenNull="false">
				<reportElement key="textField-2" x="84" y="0" width="613" height="36"/>
				<box>
					<pen lineWidth="0.5" lineStyle="Solid"/>
					<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="14" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{tituloRelatorio}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="697" y="24" width="70" height="12"/>
				<box>
					<leftPen lineWidth="0.5" lineStyle="Dotted"/>
					<bottomPen lineWidth="0.5" lineStyle="Dotted"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Verdana" size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA["Página "+$V{PAGE_NUMBER}+" de"]]></textFieldExpression>
			</textField>
			<image isUsingCache="true" onErrorType="Blank" evaluationTime="Page">
				<reportElement key="image-1" positionType="Float" x="2" y="0" width="82" height="36" isPrintWhenDetailOverflows="true"/>
				<box>
					<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<imageExpression class="java.io.InputStream"><![CDATA[$P{logoPadraoRelatorio}]]></imageExpression>
			</image>
			<textField evaluationTime="Report">
				<reportElement x="767" y="24" width="35" height="12"/>
				<box leftPadding="4">
					<bottomPen lineWidth="0.5" lineStyle="Dotted"/>
					<rightPen lineWidth="0.5" lineStyle="Dotted"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Verdana" size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.Integer"><![CDATA[$V{PAGE_NUMBER}]]></textFieldExpression>
			</textField>
			<textField pattern="dd/MM/yyyy HH.mm.ss" isBlankWhenNull="false">
				<reportElement key="dataRel-1" mode="Transparent" x="697" y="0" width="105" height="12" backcolor="#FFFFFF"/>
				<box bottomPadding="0">
					<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Verdana" size="8" isItalic="false" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression class="java.util.Date"><![CDATA[new Date()]]></textFieldExpression>
			</textField>
			<textField pattern="">
				<reportElement mode="Transparent" x="697" y="12" width="105" height="12"/>
				<box>
					<leftPen lineWidth="0.5" lineStyle="Dotted"/>
					<bottomPen lineWidth="0.5" lineStyle="Dotted"/>
					<rightPen lineWidth="0.5" lineStyle="Dotted"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Verdana" size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA["Usuário:"+ $P{usuario}]]></textFieldExpression>
			</textField>
		</band>
	</pageHeader>
	<columnHeader>
		<band height="49">
			<rectangle>
				<reportElement positionType="Float" x="0" y="27" width="802" height="20" forecolor="#FFFFFF" backcolor="#CCCCCC"/>
			</rectangle>
			<staticText>
				<reportElement key="staticText-8" positionType="Float" x="666" y="29" width="63" height="20"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Valor(R$)]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-8" positionType="Float" x="599" y="29" width="35" height="20"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Lote]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-8" positionType="Float" x="632" y="29" width="35" height="20"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[E/S]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-8" positionType="Float" x="550" y="29" width="48" height="20"/>
				<textElement verticalAlignment="Middle">
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Operação]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-8" positionType="Float" x="374" y="29" width="89" height="20"/>
				<textElement verticalAlignment="Middle">
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Forma Pgto]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-2" positionType="Float" x="212" y="29" width="162" height="20"/>
				<textElement verticalAlignment="Middle">
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Descrição]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-2" positionType="Float" x="63" y="27" width="149" height="20"/>
				<textElement verticalAlignment="Middle">
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Favorecido1]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-8" positionType="Float" x="729" y="29" width="73" height="20"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Saldo(R$)]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-2" positionType="Float" x="2" y="27" width="62" height="20"/>
				<textElement verticalAlignment="Middle">
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Dt.Quitação]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-2" positionType="Float" x="4" y="5" width="55" height="20"/>
				<textElement verticalAlignment="Top">
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Empresa:]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-224" positionType="Float" x="58" y="5" width="212" height="20"/>
				<textElement textAlignment="Left" verticalAlignment="Top">
					<font size="10" isBold="false"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{nomeEmpresa}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-224" positionType="Float" x="695" y="5" width="106" height="20">
					<printWhenExpression><![CDATA[$V{PAGE_NUMBER} == 1]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Right" verticalAlignment="Top">
					<font size="10" isBold="false"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{valorInicial}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement key="staticText-2" positionType="Float" x="620" y="5" width="75" height="20">
					<printWhenExpression><![CDATA[$V{PAGE_NUMBER} == 1]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Right" verticalAlignment="Top">
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Saldo Inicial:]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-224" positionType="Float" x="324" y="5" width="197" height="20"/>
				<textElement textAlignment="Left" verticalAlignment="Top">
					<font size="10" isBold="false"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{periodo}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement key="staticText-2" positionType="Float" x="272" y="5" width="53" height="20"/>
				<textElement verticalAlignment="Top">
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Período:]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-8" positionType="Float" x="463" y="29" width="89" height="20"/>
				<textElement verticalAlignment="Middle">
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Nº Documento]]></text>
			</staticText>
		</band>
	</columnHeader>
	<detail>
		<band height="21">
			<textField isBlankWhenNull="true">
				<reportElement key="textField-224" x="1" y="0" width="62" height="20"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font size="10" isBold="false"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{movConta.dataQuitacao_ApresentarSemHora}]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement key="textField" positionType="Float" x="666" y="0" width="63" height="20"/>
				<textElement textAlignment="Right" verticalAlignment="Middle"/>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{valorFormatado}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-224" x="374" y="0" width="89" height="20"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font size="10" isBold="false"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{formaPagamentoVO.descricao}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-224" x="632" y="0" width="35" height="20"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="10" isBold="false"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{tipoES.descricaoCurta}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-224" x="598" y="0" width="35" height="20">
					<printWhenExpression><![CDATA[$F{movConta.lote.codigo} > 0]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="10" isBold="false"/>
				</textElement>
				<textFieldExpression class="java.lang.Integer"><![CDATA[$F{movConta.lote.codigo}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-224" x="552" y="0" width="47" height="20"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font size="10" isBold="false"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{movConta.tipoOperacaoLancamento.descricaoCurta}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-224" x="62" y="0" width="150" height="20"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font size="10" isBold="false"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{movConta.favorecido}]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement key="textField" positionType="Float" x="728" y="0" width="73" height="20"/>
				<textElement textAlignment="Right" verticalAlignment="Middle"/>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{saldoFormatado}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-224" x="463" y="1" width="89" height="20"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font size="10" isBold="false"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{numeroDocumento}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-224" x="212" y="0" width="162" height="20"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font size="10" isBold="false"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{descricao}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<summary>
		<band height="33">
			<staticText>
				<reportElement key="staticText-2" x="620" y="10" width="75" height="20"/>
				<textElement textAlignment="Right" verticalAlignment="Top">
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Saldo Final:]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-224" x="695" y="10" width="106" height="20"/>
				<textElement textAlignment="Right" verticalAlignment="Top">
					<font size="10" isBold="false"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{valorFinal}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-224" x="514" y="10" width="106" height="20"/>
				<textElement textAlignment="Right" verticalAlignment="Top">
					<font size="10" isBold="false"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{totalSaida}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-224" x="333" y="10" width="106" height="20"/>
				<textElement textAlignment="Right" verticalAlignment="Top">
					<font size="10" isBold="false"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{totalEntrada}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement key="staticText-2" x="439" y="10" width="75" height="20"/>
				<textElement textAlignment="Right" verticalAlignment="Top">
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Total Saída:]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-2" x="259" y="10" width="75" height="20"/>
				<textElement textAlignment="Right" verticalAlignment="Top">
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Total Entrada:]]></text>
			</staticText>
			<line>
				<reportElement x="0" y="2" width="802" height="1"/>
			</line>
		</band>
	</summary>
</jasperReport>

#Fri Oct 13 17:46:08 GMT-03:00 2017
prt_Cheque_bancoprt_voltar=Voltar
prt_hoje=Hoje
prt_percentual=Percentual
prt_codigo=C\u00F3digo
prt_Finan_Lancamentos_pessoaprt_Login_titulo=Login
prt_Finan_pagamentoMenor=Confirmar Pagamento Menor
prt_Finan_pagamentoMaior=Confirmar Pagamento Maior
prt_Finan_pagamentoParcial=Confirmar Pagamento Parcial
prt_Finan_corrigir=Corrigir
prt_Finan_msg_qtdChequesFinanceiro=O financeiro s\u00F3 permite um cheque por lan\u00E7amento, para usar mais de um cheque divida esta conta em outros lan\u00E7amentos.
prt_Finan_Questao=O valor da quita\u00E7\u00E3o est\u00E1 diferente do valor original do lan\u00E7amento. O que deseja fazer?
prt_Finan_lancarVariasParcelasSaldoDevedor=Habilitar lan\u00E7amento de v\u00E1rias parcelas de saldo devedor
prt_msg_usuario=Usu\u00E1rio
prt_msg_data=Data
prt_msg_senha=Senha
prt_msg_pagina=P\u00E1gina
prt_msg_itens=Itens
prt_usar_nono_digito_wapp=Adicionar 9\u00BA d\u00EDgito no WhatsApp
<%--PropertiesJSFOC--%>=
prt_ModeloMensagem_tituloForm=Modelo Mensagem
prt_ModeloMensagem_codigo=C\u00F3digo
prt_ModeloMensagem_meioDeEnvio=Meio de Envio\:
prt_ModeloMensagem_titulo=T\u00EDtulo\:
prt_ModeloMensagem_mensagem=Mensagem\:
prt_ModeloMensagem_tipoMensagem=Tipo Mensagem\:
prt_ModeloMensagem_adicionarTag=Adicionar Tag\:
prt_ModeloMensagem_adicionarTagVendasOnline=Adicionar Tag(Vendas Online)\:
prt_ModeloMensagem_modeloMensagem=Modelo de Mensagens\:
prt_ModeloMensagem_caminhoImagem=Caminho Imagem\:
prt_OperadoraCartao_tituloForm=Operadora Cart\u00E3o
prt_OperadoraCartao_codigo=C\u00F3digo
prt_OperadoraCartao_codigoOperadora=C\u00F3digo Operadora\:
prt_OperadoraCartao_descricao=Descri\u00E7\u00E3o\:
prt_OperadoraCartao_codigoIntegracao=C\u00F3digo Pagamento Digital\:
prt_OperadoraCartao_codigoIntegracaoAPF=C\u00F3digo Aprova F\u00E1cil\:
prt_OperadoraCartao_codigoIntegracaoVindi=Bandeira Vindi\:
prt_OperadoraCartao_codigoIntegracaoMaxiPago=Bandeira MaxiPago\:
prt_OperadoraCartao_codigoIntegracaoERede=Bandeira e-Rede\:
prt_OperadoraCartao_codigoIntegracaoFitnessCard=Bandeira Fitness Card\:
prt_OperadoraCartao_codigoIntegracaoCielo=Bandeira Cielo\:
prt_OperadoraCartao_codigoIntegracaoStone=Bandeira Stone Online\:
prt_OperadoraCartao_codigoIntegracaoMundiPagg=Bandeira Mundipagg\:
prt_OperadoraCartao_codigoIntegracaoPagarMe=Bandeira Pagar.me\:
prt_OperadoraCartao_credito=\u00C9 Cart\u00E3o de Cr\u00E9dito?
prt_OperadoraCartao_qtdeparcelas=Quantidade M\u00E1xima de Parcelas\:
prt_MalaDiretaEnviada_tituloForm=Mala Direta Enviada
prt_MalaDiretaEnviada_pessoa=Nome\:
prt_MalaDiretaEnviada_Email=E-mail
prt_MalaDiretaEnviada_Telefone_Celular=Telefone(s) Celular(es)
prt_MalaDireta_tituloForm=Mailing
prt_MalaDireta_codigo=C\u00F3digo
prt_MalaDireta_meioEnvio=* Meio de Envio\:
prt_MalaDireta_dataEnvio=* Data Envio\:
prt_MalaDireta_dataCriacao=* Data Cria\u00E7\u00E3o\:
ANALITICO_PROFESSOR=Anal\u00EDtico por professor
prt_MalaDireta_mensagem=* Mensagem\:
prt_MalaDireta_titulo=* Titulo\:
prt_MalaDireta_remetente=Remetente\:
prt_MalaDireta_consultarRemetente=Consultar Remetente\:
prt_MalaDireta_modeloMensagem=Modelo Mensagem\:
prt_MalaDireta_consultarModeloMensagem=Consultar Modelo Mensagem\:
prt_MalaDireta_email=E-mail
prt_MalaDireta_listaEmail=Lista E-mail
prt_MalaDireta_usarAgendamento=Usar agendamento
prt_MalaDireta_IniciarEm=Iniciar em
prt_MalaDireta_EventosPreDefinidos=Eventos Pr\u00E9-definidos
prt_MalaDireta_tipoAgendamento=Tipo Agendamento
prt_MalaDireta_VigenteAte=Vigente At\u00E9
prt_GestaoPersonal_empresa=Empresa
prt_GestaoPersonal_nomeCliente=Cliente
prt_GestaoPersonal_nomePersonal=Personal
prt_GestaoPersonal_consultarColaborador=Consulta Personal Trainers
prt_GestaoPersonal_codigo=C\u00F3digo
prt_GestaoPersonal_nome=Nome
prt_GestaoPersonal_mes=M\u00EAs Refer\u00EAncia
prt_GestaoPersonal_dataLancamento=Data de Lan\u00E7amento
prt_GestaoPersonal_aluno=Aluno
prt_GestaoPersonal_produto=Produto
prt_GestaoPersonal_valorProduto=Valor
prt_GestaoPersonal_desconto=Desconto
prt_GestaoPersonal_total=Valor Final
prt_EstornoMovProduto_tituloForm=Estorno de Produto
prt_CancelamentoSessao_tituloForm=Cancelamento de Sess\u00F5es
prt_EstornoContrato_tituloForm=Estorno do Contrato
prt_EstornoRecibo_tituloForm=Consulta de Recibos
prt_EstornoRecibo_codigo=C\u00F3digo
prt_EstornoRecibo_label_codigo=C\u00F3digo
prt_EstornoRecibo_reciboNumero=C\u00F3digo do Recibo\:
prt_EstornoRecibo_data=Data Entrada No Caixa
prt_EstornoRecibo_label_data=Data
prt_EstornoRecibo_valorRecibo=Valor Recibo
prt_EstornoRecibo_valorTotal=Valor Total\:
prt_EstornoRecibo_label_valorTotal=Valor
prt_EstornoRecibo_nome=Nome do cliente\:
prt_EstornoRecibo_modalidade=Modalidade\:
prt_EstornoRecibo_label_cliente=Cliente
prt_EstornoRecibo_nomePessoa=Nome Pessoa
prt_EstornoRecibo_label_empresa=Empresa
prt_EstornoRecibo_responsavelLancamento=Respons\u00E1vel Lan\u00E7amento
prt_EstornoRecibo_label_responsavelLancamento=Respons\u00E1vel
prt_EstornoRecibo_nomeAluno=Nome Aluno
prt_CupomFiscal_tituloForm=Cupom Fiscal
prt_CupomFiscal_codigo=C\u00F3digo\:
prt_CupomFiscal_label_codigo=C\u00F3digo
prt_CupomFiscal_label_recibo=Recibo
prt_CupomFiscal_label_cliente=Cliente
prt_CupomFiscal_label_dataVenda=Venda
prt_CupomFiscal_label_dataImpressao=Emiss\u00E3o
prt_CupomFiscal_label_situacao=Situa\u00E7\u00E3o
prt_CupomFiscal_label_valor=Valor
prt_Recibo_tituloPanel=Sele\u00E7\u00E3o de Empresa
prt_Recibo_empresa=Empresa\:
prt_ModalidadeEmpresa_tituloForm=Modalidade Empresa
prt_ModalidadeEmpresa_codigo=C\u00F3digo
prt_ModalidadeEmpresa_modalidade=Modalidade
prt_ModalidadeEmpresa_empresa=Empresa
prt_Questionario_tituloForm=Question\u00E1rio
prt_Questionario_Pesquisa_tituloForm=Pesquisa de Satisfa\u00E7\u00E3o / NPS
prt_Questionario_codigo=C\u00F3digo\:
prt_Questionario_label_codigo=C\u00F3digo
prt_Questionario_descricao=* Descri\u00E7\u00E3o\:
prt_Pergunta_descricao=* Descri\u00E7\u00E3o\:
prt_Questionario_nome_interno=* Nome interno:
prt_Questionario_titulo=* T\u00EDtulo da Pesquisa:
prt_Questionario_tipo=* Tipo\:
prt_Questionario_label_descricao=Descri\u00E7\u00E3o
prt_Questionario_perguntaConsulta=Selecione as Perguntas desejadas\:
prt_Telefone_tituloForm=Telefone\:
prt_Telefone_comercial=Tel.Comercial\:
prt_Telefone_comercial_maiusculo=TEL. COMERCIAL
prt_Telefone_residencial=Tel.Residencial\:
prt_Telefone_residencial_maiusculo=TEL.RESIDENCIAL
prt_Telefone_celular=Tel.Celular\:
prt_Telefone_celular_maiusculo=TEL. CELULAR
prt_Telefone_codigo=C\u00F3digo\:
prt_Telefone_numero=N\u00FAmero\:
prt_Telefone_numero_emergencia=N\u00FAmero de emerg\u00EAncia\:
prt_contato_emergencia=Contato de emerg\u00EAncia\:
prt_contato_emergencia_maiusculo=CONTATO EMERG\u00CANCIA\:
prt_Telefone_tipoTelefone=Tipo do Telefone\:
prt_Telefone_pessoa=Pessoa\:
prt_Telefone_descricao=Descri\u00E7\u00E3o\:
prt_Telefone_descricao_maiusculo=DESCRI\u00C7\u00C3O
prt_Desconto_tituloForm=Desconto
prt_Desconto_codigo=C\u00F3digo\:
prt_Desconto_label_codigo=C\u00F3digo
prt_Desconto_descricao=* Descri\u00E7\u00E3o\:
prt_Desconto_label_descricao=Descri\u00E7\u00E3o
prt_Desconto_valor=* Valor\:
prt_Desconto_percentual=* Percentual\:
prt_Desconto_dias=* Dias\:
prt_Desconto_label_valor=Valor
prt_Desconto_tipoDesconto=* Tipo de Desconto\:
prt_Desconto_label_tipoDesconto=Tipo
prt_Desconto_tipoProduto=* Tipo de Produto\:
prt_Desconto_label_tipoProduto=Tipo de Produto
prt_Desconto_ativo=* Ativo\:
prt_Desconto_label_ativo=Ativo
prt_Antecipado_tituloForm=Desconto em Renova\u00E7\u00E3o Antecipada
prt_Antecipado_tipoIntervalo=Tipo de Intervalo\:
prt_Antecipado_intervaloDe=Intervalo de\:
prt_Antecipado_intervaloAte=Intervalo at\u00E9\:
prt_Antecipado_justificativaBonus=Justificativa do Bonus\:
prt_Antecipado_valor=Valor\:
prt_Antecipado_tipoDesconto=Tipo de Desconto\:
prt_Adquirente_tituloForm=Adquirente
prt_Pais_tituloForm=Pa\u00EDs
prt_Pais_codigo=C\u00F3digo\:
prt_Pais_label_codigo=C\u00F3digo
prt_Pais_nome=* Nome\:
prt_Pais_nacionalidade=Nacionalidade\:
prt_Pais_label_nome=Nome
prt_ClienteClassificacao_tituloForm=Classifica\u00E7\u00E3o do Cliente
prt_ClienteClassificacao_codigo=C\u00F3digo\:
prt_ClienteClassificacao_cliente=Cliente\:
prt_ClienteClassificacao_classificacao=Classifica\u00E7\u00E3o\:
prt_Parentesco_tituloForm=Parentesco
prt_Parentesco_codigo=C\u00F3digo\:
prt_Parentesco_label_codigo=C\u00F3digo
prt_Parentesco_descricao=* Descri\u00E7\u00E3o\:
prt_Parentesco_label_descricao=Descri\u00E7\u00E3o
prt_Parentesco_idadeLimiteDependencia=* Idade Limite Depend\u00EAncia\:
prt_Parentesco_label_idadeLimite=Idade Limite
prt_modal_app_gestor_titulo=Obter acesso ao "App do Gestor"
prt_modal_app_assinatura_digital=Acessar Assinatura Digital
prt_modal_app_cartao_vacina=Acessar Cartao de Vacina
prt_modal_app_gestor_gerar=Gerar QRCode
prt_modal_app_gestor_texto=Fa\u00E7a login sem dificuldades\: Entre com sua senha do ZW para gerar um QRCode que poder\u00E1 ser lido pelo "App do Gestor".
prt_modal_assinatura_digital_texto=Leia o QRCode e v\u00E1 at\u00E9 o Assinatura Digital no seu dispositivo m\u00F3vel, ou acesse o link\:
prt_modal_cartao_vacina_texto=Leia o QRCode e v\u00E1 at\u00E9 o Cart\u00E3o de Vacina no seu dispositivo m\u00F3vel, ou acesse o link\:
prt_QuestionarioPerguntaCliente_tituloForm=Question\u00E1rio de Pergunta do Cliente
prt_QuestionarioPerguntaCliente_codigo=C\u00F3digo\:
prt_QuestionarioPerguntaCliente_questionarioCliente=Question\u00E1rio do Cliente\:
prt_QuestionarioPerguntaCliente_perguntaCliente=Pergunta do Cliente\:
prt_Cliente_tituloForm=Cliente
prt_Cliente_codigo=C\u00F3digo\:
prt_Cliente_label_codigo=C\u00F3digo
prt_Cliente_pessoa=Nome\:
prt_Cliente_label_mat=Mat
prt_Cliente_label_nome=Nome
prt_Cliente_label_nome_maiusculo=NOME
prt_Cliente_telefone=Telefone\:
prt_Cliente_label_dataNasc=Data de Nascimento
prt_Cliente_situacao=Situa\u00E7\u00E3o\:
prt_Cliente_fim=Fim
prt_Cliente_inicioContrato=In\u00EDcio Contrato
prt_Cliente_label_situacao=Situa\u00E7\u00E3o
prt_Cliente_matricula=Matr\u00EDcula\:
prt_Cliente_label_matricula=MATR\u00CDCULA
prt_Cliente_label_matricula_maiusculo=MATR\u00CDCULA
prt_Cliente_categoria=Categoria\:
prt_Cliente_categoria_maiusculo=CATEGORIA
prt_Cliente_label_categoria=Categoria
prt_Cliente_codAcesso=C\u00F3digo de Acesso\:
prt_Cliente_codAcessoAlternativo=C\u00F3digo de Acesso Alternativo\:
prt_Cliente_banco=Banco\:
prt_Cliente_agencia=Ag\u00EAncia\:
prt_Cliente_agenciaDigito=Ag\u00EAncia D\u00EDgito\:
prt_Cliente_conta=Conta\:
prt_Cliente_contaDigito=Conta D\u00EDgito\:
prt_Cliente_identificadorParaCobranca=Identificador Para Cobran\u00E7a\:
prt_Cliente_email=Email\:
prt_Cliente_label_empresa=Empresa
prt_Cliente_foto=Foto\:
prt_Cliente_basico=B\u00E1sico
prt_Cliente_studio=Studio
prt_Cliente_freePass=Free Pass\:
prt_Cliente_freePass_maiusculo=FREE PASS
prt_Cliente_freePass_nomeCliente=Nome Cliente\:
prt_Cliente_UsuarioMovel_titulo=Usu\u00E1rio M\u00F3vel
prt_Cliente_UsuarioMovel_nome=E-mail
prt_Cliente_UsuarioMovel_nomeUsuario=Nome Usu\u00E1rio/E-mail
prt_Cliente_UsuarioMovel_senha=Senha
prt_Cliente_UsuarioMovel_colaborador=Colaborador
prt_Cliente_UsuarioMovel_cliente=Cliente
prt_Cliente_UsuarioMovel_ativo=Ativo
prt_Cliente_TokenGymPass=Token GymPass\:
prt_Colaborador_UsuarioMovidesk_email=E-mail Movidesk\:
prt_Familiar_tituloForm=Familiares
prt_Familiar_codigo=C\u00F3digo\:
prt_Familiar_cliente=Cliente\:
prt_Familiar_familiar=Familiar\:
prt_Familiar_matricula=Matr\u00EDcula\:
prt_Familiar_parentesco=Parentesco\:
prt_Familiar_codAcesso=C\u00F3digo de Acesso\:
prt_Familiar_identificador=Identificador\:
prt_Familiar_compartilharPlano=Compartilhar o plano\:
prt_Familiar_nome=Nome\:
prt_Pessoa_tituloForm=Pessoa
prt_Pessoa_codigo=C\u00F3digo
prt_Pessoa_profissao=Profiss\u00E3o\:
prt_Pessoa_profissao_maiusculo=PROFISS\u00C3O
prt_Pessoa_dataCadastro=Data de Cadastro\:
prt_Pessoa_dataCadastro_maiusculo=DATA DE CADASTRO
prt_Pessoa_nome=Nome
prt_Pessoa_nome_maiusculo=NOME
prt_Pessoa_dataNasc=Data de Nascimento
prt_Pessoa_dataNasc_maiusculo=DATA DE NASCIMENTO
prt_Pessoa_dataNasc_MMDDYYY_maiusculo=DATA DE NASCIMENTO (MM/DD/YYYY)
prt_Pessoa_nomePai=Nome do Pai\:
prt_Pessoa_nomePai_responsavel=Nome do Pai ou Respons\u00E1vel\:
prt_Pessoa_RgPai_responsavel=RG do Pai:
prt_Pessoa_RgMae_responsavel=RG da M\u00E3e:
prt_Pessoa_RgPai_responsavel_maiusculo=RG DO PAI:
prt_Pessoa_RgMae_responsavel_maiusculo=RG DA M\u00C3E:
prt_Pessoa_nomePai_responsavel_maiusculo=NOME DO PAI OU RESPONS\u00C1VEL
prt_Pessoa_CPFPai=CPF do Pai
prt_Pessoa_CPFPai_maiusculo=CPF DO PAI
prt_Pessoa_nomeMae=Nome da M\u00E3e ou Respons\u00E1vel
prt_Pessoa_cpfMae=CPF da M\u00E3e ou Respons\u00E1vel
prt_Pessoa_nomeMae_maiusculo=NOME DA M\u00C3E OU RESPONS\u00C1VEL
prt_Pessoa_CPFMae=CPF da M\u00E3e
prt_Pessoa_CPFMae_maiusculo=CPF DA M\u00C3E
prt_Pessoa_EmitirNotaNomeMae=Emitir Nota no Nome da M\u00E3e\:
prt_Pessoa_cfp=CPF
prt_Pessoa_BI = BI
prt_Pessoa_NUIT = NUIT
prt_Pessoa_tipoPessoa=Tipo Pessoa\:
prt_Pessoa_rg=RG\:
prt_Pessoa_rg_maiusculo=RG
prt_Pessoa_cfdf=CFDF\:
prt_Pessoa_cfdf_Terceiro=CFDF Terceiro\:
prt_Pessoa_rgOrgao=\u00D3rg\u00E3o Emissor\:
prt_Pessoa_rgUf=Estado de Emiss\u00E3o\:
prt_Pessoa_cidade=Cidade
prt_Pessoa_cidade_maiusculo=CIDADE
prt_Pessoa_estado=Estado\:
prt_Pessoa_estado_maiusculo=ESTADO
prt_Pessoa_pais=Pa\u00EDs\:
prt_Pessoa_pais_maiusculo=PA\u00CDS
prt_Pessoa_estadoCivil=Estado Civil\:
prt_Pessoa_estadoCivil_maiusculo=ESTADO CIVIL
prt_Pessoa_nacionalidade=Nacionalidade\:
prt_Pessoa_naturalidade=Naturalidade
prt_Pessoa_genero=G\u00EAnero\:
prt_Pessoa_genero_maiusculo=G\u00CANERO
prt_Pessoa_nome_registro=Nome Registro\:
prt_Pessoa_nome_registro_maiusculo=NOME REGISTRO
prt_Pessoa_sexo=Sexo biol\u00F3gico\:
prt_Pessoa_sexo_maiusculo=SEXO BIOL\u00D3GICO
prt_Finan_Lancamento_anexos=Anexos
prt_Finan_Lancamento_anexo=Anexo
prt_Pessoa_email=Email\:
prt_Pessoa_email_maiusculo=EMAIL
prt_Pessoa_grauInstrucao=Grau de Instru\u00E7\u00E3o\:
prt_Pessoa_grauInstrucao_maiusculo=GRAU DE INSTRU\u00C7\u00C3O
prt_Pessoa_webPage=Web Page\:
prt_Pessoa_webPage_maiusculo=WEB PAGE
prt_Pessoa_senhaAcesso=Senha de Acesso\:
prt_Pessoa_liberaSenhaAcesso=Habilitar senha acesso
prt_Pessoa_confirmarSenhaAcesso=Confirmar Senha\:
prt_Pessoa_definirSenhaAcesso=Definir senha de acesso\:
prt_Pessoa_foto=Foto
prt_DefinirSenhaAcesso=Definir senha de acesso
//prt_Pessoa_nomeAnimalEstimacao=Nome animal estima\u00E7\u00E3o
prt_ConfiguracaoSistema_tituloForm=Configura\u00E7\u00F5es
prt_ConfiguracaoSistema_aba_basico=B\u00E1sico
prt_ConfiguracaoSistema_aba_acesso=Acesso Academia
prt_ConfiguracaoSistema_aba_cadastro_cliente=Campos Cadastro Simplificado de Cliente
prt_ConfiguracaoSistema_aba_cadastro_colaborador=Campos Cadastro Simplificado de Colaborador
prt_ConfiguracaoSistema_aba_outros=Outras Configura\u00E7\u00F5es
prt_ConfiguracaoSistema_aba_Studio=Gest\u00E3o de Studio
prt_ConfiguracaoSistema_aba_recorrencia=Negocia\u00E7\u00E3o por Recorr\u00EAncia em Cart\u00E3o de Cr\u00E9dito
prt_ConfiguracaoSistema_aba_email=Email
prt_ConfiguracaoSistema_aba_quest=Question\u00E1rios
prt_ConfiguracaoSistema_aba_quest_sessao=Question\u00E1rios Sess\u00E3o
prt_ConfiguracaoSistema_aba_robo=Rob\u00F4
prt_ConfiguracaoSistema_codigo=C\u00F3digo\:
prt_ConfiguracaoSistema_questionarioPrimeiraVisita=* Quest. de Primeira Visita\:
prt_ConfiguracaoSistema_questionarioPrimeiraCompra=* Quest. de Primeira Compra\:
prt_ConfiguracaoSistema_questionarioRetorno=* Quest. de Retorno\:
prt_ConfiguracaoSistema_questionarioReMatricula=* Quest. de Rematr\u00EDcula\:
prt_ConfiguracaoSistema_permiteSituacaoAtestadoContrato=Permite Situa\u00E7\u00E3o de Atestado no Contrato\:
prt_ConfiguracaoSistema_permiteContratosConcomintante=Permite Contratos Concomitantes\:
prt_ConfiguracaoSistema_juroParcela=Juro da Parcela\:
prt_ConfiguracaoSistema_toleranciaPagamento=Dias de toler\u00E2ncia para pagamento\:
prt_ConfiguracaoSistema_tentativasliberarparcelavencida=N\u00FAmero de tentativas para liberar acesso de parcelas vencidas em remessa\:
prt_ConfiguracaoSistema_toleranciaConratoVencido=Toler\u00E2ncia Contrato Vencido\:
prt_ConfiguracaoSistema_bloquearacessoseparcelaaberta=Bloquear acesso se houver alguma parcela em aberto (Mesmo que não esteja vencida)\:
prt_ConfiguracaoSistema_bloquearacessosedebitoemconta=Bloquear acesso se houver saldo atual negativo em conta corrente do cliente\:
prt_ConfiguracaoSistema_bloquearacessoseparcelaaberta_title=Marque essa configura\u00E7\u00E3o se quer bloquear o acesso do aluno caso tenha alguma parcela em aberta, exceto parcelas de plano recorrente
prt_ConfiguracaoSistema_bloquearacessosedebitoemconta_title=Marque essa configura\u00E7\u00E3o se quer bloquear o acesso do aluno caso saldo atual em conta corrente negativo
prt_ConfiguracaoSistema_bloquearAcessosSemAssinaturaDigital=Bloquear alunos que n\u00E3o possuem assinatura digital\:
prt_ConfiguracaoSistema_bloquearAcessoDiariaEmpresaDiferente=Bloquear alunos com di\u00e1ria de empresa diferente\:
prt_ConfiguracaoSistema_bloquearAcessoSemTermoResponsabilidade=Bloquear alunos que n\u00E3o possuem termo de responsabilidade\:
prt_ConfiguracaoSistema_bloquearAcessoAlunoParqNaoAssinado=Bloquear alunos com Par-Q n\u00E3o assinado\:
prt_ConfiguracaoSistema_bloquearAcessoAlunoParqNaoAssinado_title=Ao marcar esta op\u00E7\u00E3o caso o aluno n\u00E3o tenha assinado o Par-Q ele ter\u00E1 o acesso bloqueado na catraca.
prt_ConfiguracaoSistema_bloquearAcessoCrefVencido=Bloquear acesso de Professor/Personal com CREF vencido\:
prt_ConfiguracaoSistema_bloquearacessosearmariovigenciavencida=Bloquear acesso se o arm\u00E1rio estiver com data de Vig\u00EAncia vencida
prt_ConfiguracaoSistema_bloquearacessomatricularematriculatotemsempagamento=Bloquear acesso de matr\u00EDcula/rematr\u00EDcula lan\u00E7\ada no Autoatendimento sem pagamento
prt_ConfiguracaoSistema_forcarValidacaoCodigoAlternativo=For\u00E7ar Valida\u00E7\u00E3o por C\u00F3digo Alternativo\:
prt_ConfiguracaoSistema_marcarPresencaPeloAcesso=Marcar Presen\u00E7a Pelo Acesso\:
prt_ConfiguracaoSistema_toleranciaOcupacaoTurma=Toler\u00E2ncia Ocupa\u00E7\u00E3o Turma\:
prt_ConfiguracaoSistema_acessoListaChamada=Lista de Chamada Gera Acesso\:
prt_ConfiguracaoSistema_acessoLocalAcesso=Local de Acesso Padr\u00E3o\:
prt_ConfiguracaoSistema_acessoColetor=Coletor Padr\u00E3o\:
prt_ConfiguracaoSistema_multa=Multa\:
prt_corEmpresa=Cor empresa\:
prt_ConfiguracaoSistema_nrdiasquestionariovisita=* N\u00FAmero de Dias Vigentes Para Question\u00E1rio Primeira Visita\:
prt_ConfiguracaoSistema_nrdiasquestionarioretorno=* N\u00FAmero de Dias Vigentes Para Question\u00E1rio Retorno\:
prt_ConfiguracaoSistema_nrdiasquestionariorematricula=* N\u00FAmero de Dias Vigentes Para Question\u00E1rio Rematr\u00EDcula\:
prt_ConfiguracaoSistema_situacaoOb=Situa\u00E7\u00E3o
prt_ConfiguracaoSistema_codAcessoOb=C\u00F3digo de Acesso
prt_ConfiguracaoSistema_codAcessoAlternativoOb=C\u00F3digo de Acesso Alternativo
prt_ConfiguracaoSistema_bancoOb=Banco
prt_ConfiguracaoSistema_agenciaOb=Ag\u00EAncia
prt_ConfiguracaoSistema_agenciaDigitoOb=Ag\u00EAncia D\u00EDgito
prt_ConfiguracaoSistema_contaOb=Conta
prt_ConfiguracaoSistema_contaDigitoOb=Conta D\u00EDgito
prt_ConfiguracaoSistema_categoriaOb=Categoria
prt_ConfiguracaoSistema_empresaOb=Empresa
prt_ConfiguracaoSistema_dataCadastroOb=Data de Cadastro
prt_ConfiguracaoSistema_nomeOb=Nome
prt_ConfiguracaoSistema_matriculaOb=Matr\u00EDcula
prt_ConfiguracaoSistema_dataNascOb=Data de Nascimento
prt_ConfiguracaoSistema_nomePaiOb=Nome do Pai
prt_ConfiguracaoSistema_nomeMaeOb=Nome da M\u00E3e
prt_ConfiguracaoSistema_grauInstrucaoOb=Grau de Instru\u00E7\u00E3o
prt_ConfiguracaoSistema_cfpOb=CPF
prt_ConfiguracaoSistema_cfpValidar=Validar CPF
prt_ConfiguracaoSistema_usarStudio=Usar Gest\u00E3o de Est\u00FAdio
prt_ConfiguracaoSistema_validarContatoAberturaMeta=Validar se o usu\u00E1rio abriu meta do CRM antes de fazer contato com aluno
prt_ConfiguracaoSistema_defaultEnderecoCorrespondecia=Em um novo Endere\u00E7o a op\u00E7\u00E3o de correspond\u00EAncia dever\u00E1 vir marcada
prt_ConfiguracaoSistema_habilitarGestaoArmarios=Habilitar m\u00F3dulo Gest\u00E3o de Arm\u00E1rios Avan\u00E7ado
prt_ConfiguracaoSistema_nrDiasProrataArmario=Dia do m\u00EAs para c\u00E1lculo do Pr\u00F3-Rata
prt_ConfiguracaoSistema_sequencialItem=Sequencial do Item
prt_modeloContrato=Contrato Arm\u00E1rio
prt_ConfiguracaoSistema_toleranciaProrataArmario=N\u00FAmero de Dias para Toler\u00E2ncia do Pr\u00F3-Rata\:
prt_ConfiguracaoSistema_nomeDataNascValidar=Validar Nome e Data de Nascimento
prt_ConfiguracaoSistema_urlRecorrencia=URL Recorr\u00EAncia (Aprova F\u00E1cil)
prt_ConfiguracaoSistema_fecharNegociacaoSemAutorizacaoDCC=Permite Fechar Negocia\u00E7\u00E3o Sem Autoriza\u00E7\u00E3o de Cobran\u00E7a\:
prt_ConfiguracaoSistema_ConsultorResponsavelVendaAvulsa=Consultor respons\u00E1vel (Venda Avulsa)
prt_ConfiguracaoSistema_emailResponsavelRecorrencia=E-mail respons\u00E1vel
prt_ConfiguracaoSistema_listaEmailsRecorrencia=Lista de E-mails dos respons\u00E1veis
prt_ConfiguracaoSistema_enviarRemessasRemotamente=Enviar remessas via SFTP
prt_ConfiguracaoSistema_irTelaPagamentoCartaoCreditoRecorrente=Habilitar cobran\u00E7a online atrav\u00E9s do caixa em aberto para contratos do tipo recorr\u00EAncia
prt_ConfiguracaoSistema_urlGoogleAgenda=URL Google Agenda
prt_ConfiguracaoSistema_rgOb=RG
prt_ConfiguracaoSistema_rgOrgaoOb=Org\u00E3o do RG
prt_ConfiguracaoSistema_rgUfOb=Estado do RG
prt_ConfiguracaoSistema_estadoCivilOb=Estado Civil
prt_ConfiguracaoSistema_nacionalidadeOb=Nacionalidade
prt_ConfiguracaoSistema_naturalidadeOb=Naturalidade
prt_ConfiguracaoSistema_sexoOb=Sexo biol\u00F3gico
prt_ConfiguracaoSistema_emailOb=Email
prt_ConfiguracaoSistema_webPageOb=Web Page
prt_ConfiguracaoSistema_estadoOb=Estado
prt_ConfiguracaoSistema_fotoOb=Foto
prt_ConfiguracaoSistema_enderecoOb=Endere\u00E7o
prt_ConfiguracaoSistema_telefoneOb=Telefone
prt_ConfiguracaoSistema_profissaoOb=Profiss\u00E3o
prt_ConfiguracaoSistema_cidadeOb=Cidade
prt_ConfiguracaoSistema_paisOb=Pa\u00EDs
prt_ConfiguracaoSistema_carenciaRenovacao=* Car\u00EAncia Renova\u00E7\u00E3o\:
prt_ConfiguracaoSistema_carencia=N\u00FAmero M\u00EDnimo de Dias para Cada Solicita\u00E7\u00E3o de F\u00E9rias\:
prt_ConfiguracaoSistema_nrDiasAvencer=* N\u00FAmero de Dias A Vencer Renova\u00E7\u00E3o\:
prt_ConfiguracaoSistema_login=Login
prt_ConfiguracaoSistema_senha=Senha
prt_ConfiguracaoSistema_remetente=Remetente Padr\u00E3o
prt_ConfiguracaoSistema_mailServer=Servidor Email
prt_ConfiguracaoSistema_nrDiasProrata=N\u00FAmero M\u00E1x.de Dias para Gerar um M\u00EAs Pr\u00F3-Rata a Mais\:
prt_ConfiguracaoSistema_toleranciaProrata=N\u00FAmero de Dias para Toler\u00E2ncia do Pr\u00F3-Rata\:
prt_ConfiguracaoSistema_vendaAvulsaIncluirAutomatico=Permitir que a venda avulsa inclua o Produto Automaticamente
prt_ConfiguracaoSistema_sesc=Usar configura\u00E7\u00E3o SESC
prt_ConfiguracaoSistema_SesiCe=Usar configura\u00E7\u00E3o SESI - CE
prt_ConfiguracaoSistema_SescGo=Usar API de integra\u00E7\u00E3o SESC - GO
prt_ConfiguracaoSistema_lumi=Usar configura\u00E7\u00E3o LUMI
prt_ConfiguracaoSistema_nomeArquivoRemessaPadraoTivit=Gerar arquivo de remessa na nomenclatura padr\u00E3o TIVIT:
prt_ConfiguracaoSistema_nomeArquivoRemessaPadraoTivit_hint=Esta configura\u00E7\u00E3o quando marcada, altera o nome do arquivo de envio de remessa para o padr\u00E3o definido pela TIVIT.
prt_ConfiguracaoSistema_definirDataInicioPlanosRecorrencia=Permitir definir data de inicio para planos recorr\u00EAncia:
prt_ConfiguracaoSistema_mascaraTelefone=M\u00E1scara Telefone (##)#####-####\:
prt_ConfiguracaoSistema_formatoDataNascimento=Utilizar formato mm/dd/yyyy para data de nascimento\:
prt_ConfiguracaoSistema_definirDataInicioPlanosRecorrencia_hint=Ao marcar esta configura\u00E7\u00E3o, ser\u00E1 poss\u00EDvel colocar uma data de in\u00EDcio futura fixa para todos os contratos de um plano do tipo recorrencia. Recurso utilizado geralmente para pr\u00E9-venda de planos recorrentes.
prt_ConfiguracaoSistema_vencimentoColaborador=Dia de Vencimento do Personal Trainer\:
prt_ConfiguracaoSistema_idadeMinima=Definir a idade mínima\:
prt_ConfiguracaoSistema_alteracaoDataBaseContrato=Permite Altera\u00E7\u00E3o da Data Base do Contrato
prt_ConfiguracaoSistema_qtdDiasExpirarSenha=Quantidade de Dias para Expirar Senha dos Usu\u00E1rios\:
prt_ConfiguracaoSistema_qtdDiasEstornoAutomatico=Quantidade de Dias ap\u00F3s o lan\u00E7amento para o Contrato ser Estornado Automaticamente <br/> (0 \= desabilitado)\:
prt_ConfiguracaoSistema_qtdDiasRepetirCobrancaParcelasRecorrencia=Intervalo de dias para repetir transa\u00E7\u00F5es de parcelas n\u00E3o aprovadas
prt_ConfiguracaoSistema_qtdDiasLimiteCobrancaParcelasRecorrencia=Limite de dias para parcela ainda entrar na repescagem
prt_ConfiguracaoSistema_enviarSmsAutomatico=Permitir enviar SMS autom\u00E1tico aos colaboradores\:
prt_ConfiguracaoSistema_tempoAposFaltaReposicao=Dias ap\u00F3s falta para fazer reposi\u00E7\u00E3o\:
prt_ConfiguracaoSistema_cobrarRematriculaApos=Quantidade de Dias para Cobrar Rematr\u00EDcula\:
prt_ConfiguracaoSistema_nrDiasDesistenteRemoverVinculoTreino=Quantidade de Dias para Remover o v\u00EDnculo com o TreinoWeb\:
prt_ConfiguracaoSistema_liberarpersonalcomtaxaemaberto=Liberar entrada de Personal se estiver com pelo menos uma taxa paga\:
prt_ConfiguracaoSistema_devolucaoentranocaixa=Devolu\u00E7\u00F5es saem fisicamente do caixa\:
prt_usar_manutencao_comissao=Usar manuten\u00E7\u00E3o de modalidade na comiss\u00E3o
prt_ConfiguracaoSistema_RemoverVinculosAposDesistencia=Remover V\u00EDnculos do TreinoWeb ap\u00F3s o aluno se tornar desistente ou cancelado\:
prt_ConfiguracaoSistema_qtdDiasEstornoAutomaticoPrimeiraParcelaVencida=Quantidade de Dias ap\u00F3s o vencimento da primeira parcela do contrato, Estornar Automaticamente <br/> (0 \= desabilitado)\:
prt_ConfiguracaoSistema_qtdDiasEstornoAutomaticoPrimeiraParcelaVencidaOrigemVendasOnline=Quantidade de Dias ap\u00F3s o vencimento da primeira parcela do contrato com origem VENDAS 2.0 (Vendas Online) para Estornar Automaticamente <br/> (0 \= desabilitado)\:
prt_ConfiguracaoSistema_msgAlertaVinculos=No caso do cancelamento, o v\u00EDnculo \u00E9 removido imediatamente, n\u00E3o obedecendo a configura\u00E7\u00E3o de dias abaixo\:
prt_Empresa_tituloForm=Empresa
prt_Empresa_codigo=C\u00F3digo\:
prt_Empresa_label_codigo=C\u00F3digo
prt_Empresa_situacao=Situa\u00E7\u00E3o\:
prt_Empresa_label_situacao=Situa\u00E7\u00E3o
prt_Empresa_nome=* Nome\:
prt_Empresa_label_nome=Nome
prt_Empresa_razaoSocial=* Raz\u00E3o Social\:
prt_Empresa_label_razaoSocial=Raz\u00E3o Social
prt_Empresa_endereco=* Endere\u00E7o\:
prt_Empresa_setor=Bairro\:
prt_Empresa_numero=N\u00FAmero\:
prt_Empresa_complemento=Complemento\:
prt_Empresa_latitude=Latitude\:
prt_Empresa_longitude=Longitude\:
prt_Empresa_cidade=* Cidade\:
prt_Empresa_estado=* Estado\:
prt_Empresa_pais=* Pa\u00EDs\:
prt_Empresa_CEP=CEP\:
prt_Empresa_CNPJ=* CNPJ\:
prt_Empresa_label_CNPJ=CNPJ
prt_Empresa_inscEstadual=* Inscri\u00E7\u00E3o Estadual\:
prt_Empresa_inscEstadualNaoObrigatorio=Inscri\u00E7\u00E3o Estadual\:
prt_Empresa_label_inscEstadual=Inscri\u00E7\u00E3o Estadual
prt_Empresa_label_inscEstadual_Terceiro=Inscri\u00E7\u00E3o Estadual Terceiro
prt_Empresa_telComercial1=Tel.Comercial 1\:
prt_Empresa_telComercial2=Tel.Comercial 2\:
prt_Empresa_telComercial3=Tel.Comercial 3\:
prt_Empresa_email=Email\:
prt_Empresa_emailNotificacaoVendasOnline=Email notifica\u00E7\u00F5es Vendas Online (antigo)\:
prt_Empresa_emailNotificacaoVendasOnline_tooltip=E-mail para receber as notifica\u00E7\u00F5es provenientes das transa\u00E7\u00F5es do Vendas Online antigo. Para configurar o email de notifica\u00E7\u00F5es do Vendas Online novo, voc\u00EA deve fazer isso no "Gest\u00E3o de Vendas Online"
prt_Empresa_site=Site\:
prt_Empresa_fax=Fax\:
prt_Empresa_permiteAcessoDeOutrasEmpresas=Permite acesso de alunos de outras empresas do grupo\:
prt_Empresa_somaDv=Soma para c\u00E1lculo de d\u00EDgito verificador\:
prt_Empresa_mascaraMatricula=* M\u00E1scara Matr\u00EDcula (Digite Apenas Letra x)\:
prt_Empresa_questionarioPrimeiraVisita=Question\u00E1rio de Primeira Visita\:
prt_Empresa_questionarioRetorno=Question\u00E1rio de Retorno\:
prt_Empresa_questionarioReMatricula=Question\u00E1rio de Rematr\u00EDcula\:
prt_Empresa_permiteSituacaoAtestadoContrato=Permite Situa\u00E7\u00E3o de Atestado no Contrato\:
prt_Empresa_permiteContratosConcomintante=Permite Contratos Concomitantes\:
prt_Empresa_nrDiasAvencer=N\u00FAmero de Dias A Vencer Renova\u00E7\u00E3o\:
prt_Empresa_fusoHorairo=Fuso Hor\u00E1rio\:
prt_Empresa_diasChequeAVista=N\u00FAmero de Dias para Considerar Cheque \u00E0 Vista\:
prt_Empresa_diasCompensacaoDebito=N\u00FAmero de Dias para Compensa\u00E7\u00E3o de D\u00E9bito\:
prt_Empresa_serviceUsuario=Usu\u00E1rio para o Service\:
prt_Empresa_serviceSenha=Senha para o Service\:
prt_Empresa_codigoRede=C\u00F3digo na Rede:
prt_ContaCorrenteEmpresa_tituloForm=Conta Corrente da Empresa
prt_CancelDevolveProdEmpresa_tituloForm=Produtos a serem devolvidos no cancelamento
prt_ContaCorrenteEmpresa_contaCorrente=Conta Corrente
prt_LogoTipo_tituloForm=Logotipo
prt_Empresa_tokenSMS=Token SMS
prt_Empresa_usarNomeResponsavelNFCe=Emitir NFC-e no nome e CPF do respons\u00E1vel caso aluno seja menor de 18 anos\:
prt_Empresa_enviarNFCeCidadeEmpresa=Enviar NFC-e com a cidade da empresa\:
prt_Empresa_cpfObrigatorioNFCe=CPF obrigat\u00F3rio\:
prt_Empresa_enderecoObrigatorioNFCe=Endere\u00E7o obrigat\u00F3rio\:
prt_Grupo_tituloForm=Grupo com Desconto
prt_Grupo_codigo=C\u00F3digo\:
prt_Grupo_label_codigo=C\u00F3digo
prt_Grupo_descricao=* Descri\u00E7\u00E3o\:
prt_Grupo_label_descricao=Descri\u00E7\u00E3o
prt_Grupo_percentualDescontoGrupo=Percentual\:
prt_Grupo_valorDescontoGrupo=Valor\:
prt_Grupo_tipoDesconto=* Tipo de Desconto\:
prt_Grupo_label_tipoDesconto=Tipo
prt_Gympass_tituloForm=Cadastro de Repasse Gympass
prt_Gympass_dia=\u00B0 Dia
prt_Gympass_valormaximo=Valor M\u00E1ximo da comiss\u00E3o por aluno
prt_Gympass_datainicial=Data Inicial do Per\u00EDodo
prt_Gympass_datafinal=Data Final do Per\u00EDodo
prt_Classificacao_tituloForm=Classifica\u00E7\u00E3o
prt_Classificacao_codigo=C\u00F3digo\:
prt_Classificacao_label_codigo=C\u00F3digo
prt_Classificacao_nome=* Nome\:
prt_Classificacao_label_descricao=Descri\u00E7\u00E3o
prt_Classificacao_enviarSmsAutomatico=Enviar SMS Autom\u00E1tico\:
prt_Classificacao_label_SmsAutomatico=SMS Autom\u00E1tico
prt_PerguntaClienteRespostaPergCliente_tituloForm=Pergunta do Cliente e Resposta da Pergunta do Cliente
prt_PerguntaClienteRespostaPergCliente_codigo=C\u00F3digo\:
prt_PerguntaClienteRespostaPergCliente_perguntaCliente=Pergunta do Cliente\:
prt_PerguntaClienteRespostaPergCliente_respostaPerguntaCliente=Resposta da Pergunta do Cliente\:
prt_Profissao_tituloForm=Profiss\u00E3o
prt_Profissao_codigo=C\u00F3digo\:
prt_Profissao_maiusculo=C\u00D3DIGO
prt_Profissao_label_codigo=C\u00F3digo
prt_Profissao_descricao=* Descri\u00E7\u00E3o\:
prt_Profissao_descricao_maiusculo=* DESCRI\u00C7\u00C3O
prt_Profissao_label_descricao=Descri\u00E7\u00E3o
prt_GrauInstrucao_tituloForm=Grau de Instru\u00E7\u00E3o
prt_GrauInstrucao_codigo=C\u00F3digo\:
prt_GrauInstrucao_label_codigo=C\u00F3digo
prt_GrauInstrucao_descricao=* Descri\u00E7\u00E3o\:
prt_GrauInstrucao_label_descricao=Descri\u00E7\u00E3o
prt_RespostaPerguntaCliente_tituloForm=Resposta Pergunta do Cliente
prt_RespostaPerguntaCliente_codigo=C\u00F3digo\:
prt_RespostaPerguntaCliente_descricaoRespota=Descri\u00E7\u00E3o da Resposta\:
prt_RespostaPerguntaCliente_perguntaCliente=Pergunta do Cliente\:
prt_RespostaPerguntaCliente_respostaOpcao=Resposta Op\u00E7\u00E3o\:
prt_RespostaPerguntaCliente_respostaTextual=Resposta Textual\:
prt_Cheque_banco=Banco
prt_Cheque_agencia=Ag\u00EAncia
prt_Cheque_conta=Conta
prt_Cheque_numeroCheque=N\u00FAmero Cheque
prt_Cheque_nomeNoCheque=Nome no Cheque
prt_Cheque_data=Data Compensa\u00E7\u00E3o
prt_Cheque_valor=Valor
prt_Cheque_situacao=Situa\u00E7\u00E3o
prt_Cheque_codigo=C\u00F3digo
prt_Cheque_alterarStatus=Alterar Status
prt_Conta_label_codigo=C\u00F3digo\:
prt_Conta_label_banco=Conta
prt_Conta_label_numero=N\u00FAmero
prt_Conta_label_agencia=Ag\u00EAncia
prt_Categoria_tituloForm=Categoria de Clientes
prt_Categoria_codigo=C\u00F3digo\:
prt_Categoria_codigo_maiusculo=C\u00D3DIGO
prt_Categoria_label_codigo=C\u00F3digo
prt_Categoria_nome=* Nome\:
prt_Categoria_nome_maiusculo=NOME
prt_Categoria_label_descricao=Descri\u00E7\u00E3o
prt_Categoria_tipoCategoria=* Tipo de Categoria\:
prt_Categoria_tipoCategoria_maiusculo=TIPO CATEGORIA
prt_Categoria_label_tipoCategoria=Tipo
prt_Categoria_nrConvitePermitido=* N\u00FAmero de Convites Permitidos\:
prt_Categoria_nrConvitePermitido_maiusculo=* N\u00DAMERO DE CONVITES PERMITIDOS
prt_Departamento_tituloForm=Departamentos
prt_Departamento_label_codigo=C\u00F3digo
prt_Departamento_codigo=C\u00F3digo\:
prt_Departamento_label_nome=Nome
prt_Departamento_nome=Nome\:
prt_Departamento_label_empresa=Empresa
prt_Departamento_empresa=Empresa\:
prt_Departamento_label_concessionario=Concession\u00E1rio
prt_Departamento_concessionario=Concession\u00E1rio\:
prt_MovimentoContaCorrenteCliente_tituloForm=Movimento de Conta Corrente do Cliente
prt_MovimentoContaCorrenteCliente_codigo=C\u00F3digo\:
prt_MovimentoContaCorrenteCliente_label_codigo=C\u00F3d
prt_MovimentoContaCorrenteCliente_pessoaNome=* Nome\:
prt_MovimentoContaCorrenteCliente_label_cliente=Cliente
prt_MovimentoContaCorrenteCliente_cpf=CPF\:
prt_MovimentoContaCorrenteCliente_dataNascimento=Data de Nascimento\:
prt_MovimentoContaCorrenteCliente_valor=Valor\:
prt_MovimentoContaCorrenteCliente_tipo=Tipo
prt_MovimentoContaCorrenteCliente_saldoAnteior=Saldo Anteior\:
prt_MovimentoContaCorrenteCliente_saldoAtual=Saldo Atual\:
prt_MovimentoContaCorrenteCliente_descricao=* Descri\u00E7\u00E3o\:
prt_MovimentoContaCorrenteCliente_label_descricao=Descri\u00E7\u00E3o
prt_MovimentoContaCorrenteCliente_dataRegistro=Data do Registro\:
prt_MovimentoContaCorrenteCliente_label_dataRegistro=Data
prt_MovimentoContaCorrenteCliente_responsavelRegistro=Respons\u00E1vel pelo Registro\:
prt_MovimentoContaCorrenteCliente_label_responsavelRegistro=Respons\u00E1vel
prt_MovimentoContaCorrenteCliente_responsavelAutorizacao=* Respons\u00E1vel pela Autoriza\u00E7\u00E3o\:
prt_MovimentoContaCorrenteCliente_label_valor=Valor
prt_TransferenciaContaCorrenteCliente_consultarCliente=Consultar Cliente
prt_TransferenciaContaCorrenteCliente_nomeCliente=Cliente
prt_TransferenciaContaCorrenteCliente_valorTransferido=Valor da Transfer\u00EAncia
prt_TransferenciaContaCorrenteCliente_valorMaximo=Saldo Cliente
prt_GerarParcelaContaCorrenteCliente_valorMaximo=D\u00E9bito Cliente
prt_GerarParcelaContaCorrenteCliente_valorParcela=Valor a Pagar
prt_PerguntaCliente_tituloForm=Pergunta Cliente
prt_PerguntaCliente_codigo=C\u00F3digo\:
prt_PerguntaCliente_descricao=Descri\u00E7\u00E3o\:
prt_PerguntaCliente_tipoPergunta=Tipo de Pergunta\:
prt_Cidade_tituloForm=Cidade
prt_Cidade_codigo=C\u00F3digo\:
prt_Cidade_codigo_maiusculo=C\u00D3DIGO
prt_Cidade_label_codigo=C\u00F3digo
prt_Cidade_nome=* Nome\:
prt_Cidade_nome_maiusculo=NOME
prt_Cidade_label_nome=Nome
prt_Cidade_estado=* Estado\:
prt_Cidade_estado_maiusculo=ESTADO
prt_Cidade_label_estado=Estado
prt_Cidade_pais=* Pa\u00EDs\:
prt_Cidade_pais_maiusculo=PA\u00CDS
prt_Cidade_label_pais=Pa\u00EDs
prt_Estado_tituloForm=Estado
prt_Estado_sigla=* Sigla\:
prt_Estado_descricao=* Descri\u00E7\u00E3o\:
prt_ClienteGrupo_tituloForm=Grupo do Cliente
prt_ClienteGrupo_codigo=C\u00F3digo\:
prt_ClienteGrupo_grupo=Grupo\:
prt_ClienteGrupo_cliente=Cliente\:
prt_RespostaPergunta_tituloForm=Resposta da Pergunta
prt_RespostaPergunta_codigo=C\u00F3digo
prt_RespostaPergunta_descricaoRespota=Descri\u00E7\u00E3o\:
prt_RespostaPergunta_pergunta=Pergunta\:
prt_PerguntaRespostaPergunta_tituloForm=Pergunta e Resposta da Pergunta
prt_PerguntaRespostaPergunta_codigo=C\u00F3digo\:
prt_PerguntaRespostaPergunta_pergunta=Pergunta\:
prt_PerguntaRespostaPergunta_respostaPergunta=Resposta da Pergunta\:
prt_Pergunta_tituloForm=Pergunta
prt_Pergunta_codigo=C\u00F3digo\:
prt_Pergunta_label_codigo=C\u00F3digo
prt_Pergunta_titulo=* T\u00EDtulo da Pesquisa:
prt_Pergunta_label_descricao=Descri\u00E7\u00E3o
prt_Pergunta_tipoPergunta=* Tipo Pergunta\:
prt_Pergunta_label_tipoPergunta=Tipo
prt_QuestionarioPergunta_tituloForm=Question\u00E1rio Pergunta
prt_QuestionarioPergunta_codigo=C\u00F3digo\:
prt_QuestionarioPergunta_questionario=Question\u00E1rio\:
prt_QuestionarioPergunta_pergunta=Pergunta
prt_QuestionarioPergunta_obrigatorio=Obrigat\u00F3ria
prt_QuestionarioPergunta_obrigatorio_titulo=Existe uma configura\u00E7\u00E3o de empresa para obrigar o preenchimento do BV, <br/> com este par\u00E2metro marcado todas as perguntas ser\u00E3o obrigat\u00F3rias.
prt_QuestionarioCliente_tituloForm=Question\u00E1rio Cliente
prt_QuestionarioCliente_codigo=C\u00F3digo\:
prt_QuestionarioCliente_questionario=Question\u00E1rio\:
prt_QuestionarioCliente_cliente=Cliente\:
prt_QuestionarioCliente_consultor=Consultor\:
prt_QuestionarioCliente_consultor_maiusculo=CONSULTOR
prt_QuestionarioCliente_consultor_obrigatorio=* Consultor\:
prt_QuestionarioCliente_evento=Evento\:
prt_QuestionarioCliente_evento_maiusculo=EVENTO
prt_QuestionarioCliente_data=Data\:
prt_QuestionarioCliente_data_maiusculo=DATA
prt_Endereco_tituloForm=Endere\u00E7o
prt_Endereco_codigo=C\u00F3digo\:
prt_Endereco_endereco=Endere\u00E7o\:
prt_Endereco_enderecoOb=Endere\u00E7o\:
prt_Endereco_enderecoOb_maiusculo=ENDERE\u00C7O
prt_Endereco_complemento=Complemento\:
prt_Endereco_complementoOb=Complemento\:
prt_Endereco_complementoOb_maiusculo=COMPLEMENTO
prt_Endereco_numero=N\u00FAmero\:
prt_Endereco_numeroOb=N\u00FAmero\:
prt_Endereco_numeroOb_maiusculo=N\u00DAMERO\:
prt_Endereco_bairro=Bairro\:
prt_Endereco_bairroOb=Bairro\:
prt_Endereco_bairroOb_maiusculo=BAIRRO
prt_Endereco_cep=Cep\:
prt_Endereco_cep_maiusculo=CEP
prt_Endereco_cepOb=Cep\:
prt_Endereco_tipoEndereco=Tipo de Endere\u00E7o\:
prt_Endereco_tipoEnderecoOb=Tipo de Endere\u00E7o\:
prt_Endereco_pessoa=Pessoa\:
prt_Endereco_enderecoCorrespondencia=Endere\u00E7o para Correspond\u00EAncia\:
prt_CEP_tituloConsulta=Consulta CEP
prt_CEP_titulo=CEP
prt_CEP_bairroC=Bairro
prt_CEP_logradouroC=Logradouro
prt_CEP_cidadeC=Cidade
prt_CEP_cidadeC_maiusculo=CIDADE
prt_CEP_estadoC=Estado
prt_CEP_estadoC_maiusculo=ESTADO
prt_CEP_logradouro=Logradouro\:
prt_CEP_logradouro_maiusculo=LOGRADOURO
prt_CEP_cidade=Cidade\:
prt_CEP_estado=Estado\:
prt_CEP_bairro=Bairro\:
prt_CEP_bairro_maiusculo=BAIRRO
prt_Email_tituloForm=E-mail
prt_Familia_tituloForm=Familia
prt_Email_codigo=C\u00F3digo\:
prt_Email_email=E-mail\:
prt_Email_email_usuario=*E-mail\:
prt_Email_email_username_usuario=*E-mail / Username\:
prt_Email_emailCorrespondencia=E-mail para Correspond\u00EAncia\:
prt_Email_emailBloqueadoBounce=Bloqueado pelo Anti-Spam\:
prt_Placa_tituloForm=Placa
prt_Placa_placa=Placa\:
prt_Placa_descricao=Descri\u00E7\u00E3o\:
prt_DadosCliente_tituloForm=Dados do Cliente
prt_DadosPessoais_tituloForm=Dados Pessoais
prt_DadosColaborador_tituloForm=Dados do Colaborador
prt_DadosBasicos_tituloForm=Dados B\u00E1sicos
prt_Modalidades_tituloForm=Modalidades do Plano
prt_PlanoConfiguracoesCancelamento_tituloForm=Configura\u00E7\u00F5es de Cancelamento
prt_PlanoConfiguracoesFerias_tituloForm=Configura\u00E7\u00F5es de F\u00E9rias
prt_PlanoConfiguracoesProdutosPadrao_tituloForm=Configura\u00E7\u00F5es de Produtos Padr\u00E3o
prt_PlanoConfiguracoesProrata_tituloForm=Configura\u00E7\u00F5es de Pr\u00F3-Rata
prt_PlanoConfiguracoesProrata_vencimentos=Dias de Vencimento
prt_PlanoConfiguracoesProrata_obrigatorio=Obrigat\u00F3rio
prt_PlanoConfiguracoesProrata_prorata=Informe um dia de Vencimento
prt_PlanoRecorrencia_tituloForm=Recorr\u00EAncia
prt_PlanoRecorrencia_taxaAdesao=Taxa de Ades\u00E3o
prt_PlanoRecorrencia_taxaAdesaoVezes=Vezes
prt_PlanoRecorrencia_valorAnuidade=Valor da Anuidade
prt_PlanoRecorrencia_valorMensal=Valor da Mensalidade
prt_PlanoRecorrencia_diaAnuidade=Dia
prt_PlanoRecorrencia_mesAnuidade=M\u00EAs
prt_PlanoRecorrencia_fidelidadePlano=Fidelidade do Plano (meses)
prt_PlanoRecorrencia_qtdDiasAposVencimentoCancelamentoAutomatico=Qtde Dias ap\u00F3s vencimento da parcela para Cancelamento Autom\u00E1tico do contrato
prt_PlanoRecorrencia_qtdDiasAposVencimenstoCancelamentoAutomaticoToolTip=Quantidade de dias ap\u00F3s vencimento da parcela mais antiga sem pagamento, para cancelamento autom\u00E1tico do contrato \u2013 onde, valor ZERO n\u00E3o efetua o cancelamento autom\u00E1tico
prt_PlanoRecorrencia_regimeRecorrencia=Regime de Recorr\u00EAncia
prt_PlanoRecorrencia_renovavelAutomaticamente=Renov\u00E1vel Automaticamente
prt_PlanoRecorrencia_renovarProdutoObrigatorio=Renovar Produtos Obrigat\u00F3rios
prt_PlanoRecorrencia_renovarAutomaticamenteUtilizandoValorBaseContrato=Renovar contrato automaticamente utilizando valor base do contrato
prt_PlanoRecorrencia_naorenovarparcelaaberta=N\u00E3o renovar com parcela aberta (vencida)
prt_PlanoReplicarEmpresa_tituloForm=Replicar Empresa
prt_PlanoRedeEmpresa_nomeUnidade=Nome da Unidade
prt_PlanoRedeEmpresa_mensagemSituacao=Mensagem
prt_PlanoRedeEmpresa_chave=Chave

prt_PlanoExcecao_tituloForm=Exce\u00E7\u00F5es
prt_PlanoExcecao_pacote=Pacote
prt_PlanoExcecao_modalidade=Modalidade
prt_PlanoExcecao_horario=Hor\u00E1rio
prt_PlanoExcecao_vezessemana=Vezes/Semana
prt_PlanoExcecao_duracao=Dura\u00E7\u00E3o
prt_PlanoExcecao_valor=Valor Mensal R$
prt_Plano_descricaoReajuste=1. Ele deve ser utilizado para reajustar os valores dos contratos e suas parcelas deste plano;<br/>2. Aplicando o reajuste para as parcelas em aberto dos contratos que satisfazem os filtros abaixo;<br/>3. Recalculando o valor do Contrato e gerando produtos com as respectivas compet\u00EAncias.
prt_Plano_tipoReajuste=Reajuste por Valor ou Porcentagem\:
prt_Plano_valorAcrescentar=Valor Acrescentar em cada Mensalidade\:
prt_Plano_vencimentoParcelas=Vencimento parcelas (a partir de qual vencimento as parcelas passar\u00E3o a ter o reajuste)\:
prt_Plano_dataLancamentoContrato=Data Lan\u00E7amento Contrato\:
prt_Plano_tiposContrato=Tipos de Contrato\:
prt_Colaborador_tituloForm=Colaborador
prt_Colaborador_codigo=C\u00F3digo\:
prt_Colaborador_label_codigo=C\u00F3digo
prt_Cadastro_label_codigo_maiusculo=COD
prt_Cadastro_label_convenioCobranca_maiusculo=CONV\u00CANIO COBRAN\u00C7A
prt_Cadastro_label_nrParcelas_maiusculo=NR. DE PARCELAS
prt_Cadastro_label_percentualEntrada_maiusculo=PERCENTUAL DE ENTRADA
prt_Cadastro_label_nome_maiusculo=NOME
prt_Cadastro_label_cpnj_maiusculo=CNPJ
prt_Cadastro_label_cpf_maiusculo=CPF
prt_Cadastro_label_cidade_maiusculo=CIDADE
prt_Cadastro_label_nascimento_maiusculo=NASCIMENTO
prt_Cadastro_label_tipo_maiusculo=TIPO
prt_Cadastro_label_tipoProduto_maiusculo=TIPO DE PRODUTO
prt_Cadastro_label_produto_maiusculo=PRODUTO
prt_Cadastro_label_fornecedor_maiusculo=FORNECEDOR
prt_Cadastro_label_contato_maiusculo=CONTATO
prt_Cadastro_label_usuario_maiusculo=USU\u00C1RIO
prt_Cadastro_label_lancamento_maiusculo=LAN\u00C7AMENTO
prt_Cadastro_label_empresa_maiusculo=EMPRESA
prt_Cadastro_label_categoria_maiusculo=CATEGORIA
prt_Cadastro_label_data_maiusculo=DATA
prt_Cadastro_label_data_compra_maiusculo=DATA DA COMPRA
prt_Cadastro_label_valor_maiusculo=VALOR
prt_Cadastro_label_ativo_maiusculo=ATIVO
prt_Cadastro_label_inicio_maiusculo=IN\u00CDCIO
prt_Cadastro_label_fim_maiusculo=FIM
prt_Cadastro_label_tipoOperacao_maiusculo=TIPO OPERA\u00C7\u00C3O
prt_Cadastro_label_responsavel_maiusculo=RESPONS\u00C1VEL
prt_Cadastro_label_contrato_maiusculo=CONTRATO
prt_Cadastro_label_definicao_maiusculo=DEFINI\u00C7\u00C3O
prt_Cadastro_label_consumidor_maiusculo=CONSUMIDOR
prt_Cadastro_label_estoqueAtual_maiusculo=ESTOQUE ATUAL
prt_Cadastro_label_estoqueMinimo_maiusculo=ESTOQUE M\u00CDNIMO
prt_Cadastro_label_concessionario_maiusculo=CONCESSION\u00C1RIO
prt_Cadastro_label_descricao_maiusculo=DESCRI\u00C7\u00C3O
prt_Cadastro_label_descricaoConta_maiusculo=DESCRI\u00C7\u00C3O DA CONTA
prt_Cadastro_label_situacao_maiusculo=SITUA\u00C7\u00C3O
prt_Cadastro_label_razaoSocial_maiusculo=RAZ\u00C3O SOCIAL
prt_Cadastro_label_inscricaoEstadual_maiusculo=INSCRI\u00C7\u00C3O ESTADUAL
prt_Cadastro_label_quantidade_maiusculo=QUANTIDADE
prt_Cadastro_label_comportamento_maiusculo=COMPORTAMENTO
prt_Cadastro_label_codigo_integracao_miusculo=C\u00D3DIGO INTEGRA\u00C7\u00C3O
prt_Cadastro_label_sms_maiusculo=SMS AUTOM\u00C1TICO
prt_Cadastro_label_idadeLimite_maiusculo=IDADE LIMITE
prt_Cadastro_label_idadeMinima_maiusculo=IDADE M\u00CDNIMA
prt_Cadastro_label_idadeMaxima_maiusculo=IDADE M\u00C1XIMA
prt_Cadastro_label_cliente_maiusculo=CLIENTE
prt_Cadastro_label_modeloorcamento_maiusculo=OR\u00C7AMENTO
prt_Cadastro_label_saldoAtual_maiusculo=SALDO ATUAL
prt_Cadastro_label_vigenciaDe_maiusculo=VIG\u00CANCIA DE
prt_Cadastro_label_vigenciaAte_maiusculo=VIG\u00CANCIA AT\u00C9
prt_Cadastro_label_ingressoAte_maiusculo=INGRESSO AT\u00C9
prt_Cadastro_label_numeroVezes_maiusculo=NR DE VEZES
prt_Cadastro_label_modalidade_maiusculo=MODALIDADE
prt_Cadastro_label_identificador_maiusculo=IDENTIFICADOR
prt_Cadastro_label_tipoConta_maiusculo=TIPO DE CONTA
prt_Cadastro_label_banco_maiusculo=BANCO
prt_Cadastro_label_agencia_maiusculo=AG\u00CANCIA
prt_Cadastro_label_digitoVerificadorAgencia_maiusculo=D\u00CDGITO AG\u00CANCIA
prt_Cadastro_label_contaCorrente_maiusculo=CONTA CORRENTE
prt_Cadastro_label_digitoVerificadorContaCorrente_maiusculo=D\u00CDGITO CONTA CORRENTE
prt_Cadastro_label_convenio_maiusculo=CONV\u00CANIO
prt_Cadastro_label_arquivoLayout_maiusculo=ARQUIVO DE LAYOUT
prt_Cadastro_label_codigoOperadora_maiusculo=C\u00D3DIGO OPERADORA
prt_Cadastro_label_perfilAcesso_maiusculo=PERFIL DE ACESSO
prt_Cadastro_label_username_maiusculo=USERNAME
prt_Cadastro_label_empresaRemota_maiusculo=EMPRESA REMOTA
prt_Cadastro_label_matricula_maiusculo=MATR\u00CDCULA
prt_Cadastro_label_codigoAcesso_maiusculo=COD ACESSO
prt_Colaborador_pessoa=Pessoa\:
prt_Colaborador_nome=* Nome\:
prt_Colaborador_label_nome=Nome
prt_Colaborador_matricula_aluno=Matrícula Aluno\:
prt_Colaborador_dataNasc=* Data de Nascimento\:
prt_Colaborador_label_dataNasc=Data de Nascimento
prt_Colaborador_empresa=Empresa\:
prt_Colaborador_label_empresa=Empresa
prt_Colaborador_tipoColaborador=Tipo Colaborador\:
prt_Colaborador_cargaHoraria=Carga Hor\u00E1ria\:
prt_Colaborador_permitirAcessoRedeEmpresa=Permitir acessar as unidades da Rede\:
prt_Colaborador_tipoColaborador_usuario=*Tipo Colaborador\:
prt_Colaborador_label_tipoColaborador=Tipo
prt_Colaborador_situacao=* Situa\u00E7\u00E3o\:
prt_Colaborador_label_situacao=Situa\u00E7\u00E3o
prt_Colaborador_codAcesso=C\u00F3digo de Acesso\:
prt_Colaborador_codAcessoAlternativo=C\u00F3digo de Acesso Alternativo\:
prt_Colaborador_funcionario=Funcion\u00E1rio\:
prt_Colaborador_cpf=CPF\:
prt_Colaborador_tipoVisao=Tipo Vis\u00E3o\:
prt_Colaborador_diaVencimento=Dia de Vencimento do Personal Trainer\:
prt_Colaborador_produtoDefault=Produto Default do Personal trainer\:
prt_Colaborador_porcComissao=Porcentagem Comiss\u00E3o (%)\:
prt_Colaborador_valorComissao=Valor fixo Comiss\u00E3o (R$)\:
prt_Colaborador_porcComissaoEstudio=Porcentagem Comiss\u00E3o (%) Estudio\:
prt_Colaborador_comissao=Comiss\u00E3o
prt_Colaborador_modalidade=Modalidade
prt_Colaborador_turma=Turma
prt_Colaborador_aluno=Aluno
prt_Colaborador_corAgendaProfissional=Cor Agenda Profissional
prt_Colaborador_configurarTempoEntreAcessos=Bloquear acessos seguidos\:
prt_Colaborador_tempoEntreAcessos=Tempo m\u00EDnimo entre um acesso e outro\:
prt_Colaborador_cref=CREF\:
prt_Colaborador_Validade_cref=Validade CREF\:
prt_Colaborador_autenticarGoogle=Autenticar Google\:
prt_Colaborador_departamento=Departamento\:
prt_ColaboradorModalidade_tituloForm=Modalidade\:
prt_ColaboradorModalidade_modalidade=* Modalidade\:
prt_Gestao_Comissao_somenteTurmas=Somente alunos em turmas
prt_Gestao_Comissao_professor=Professor
prt_Foto_tituloForm=Foto
prt_MovProdutoParcela_tituloForm=Movimento Produto Parcela
prt_MovProdutoParcela_codigo=C\u00F3digo\:
prt_MovProdutoParcela_movProduto=Movimenta\u00E7\u00E3o Produto\:
prt_MovProdutoParcela_movParcela=Movimenta\u00E7\u00E3o Parcela\:
prt_MovProdutoParcela_valorPago=Valor Pago\:
prt_MovParcela_tituloForm=Movimento da Parcela
prt_MovParcela_codigo=C\u00F3digo\:
prt_MovParcela_label_codigo=C\u00F3digo
prt_MovParcela_movProduto=Movimento Produto\:
prt_MovParcela_dataRegistro=Data de Registro\:
prt_MovParcela_label_dataRegistro=Registro
prt_MovParcela_dataVencimento=Data Vencimento\:
prt_MovParcela_label_dataVencimento=Vencimento
prt_MovParcela_situacao=Situa\u00E7\u00E3o\:
prt_MovParcela_label_situacao=Situa\u00E7\u00E3o
prt_MovParcela_formaPagamento=Forma Pagamento\:
prt_MovParcela_responsavel=Respons\u00E1vel\:
prt_MovParcela_label_responsavel=Respons\u00E1vel
prt_MovParcela_contratoParcelaPagamento=Contrato Parcela Pagamento\:
prt_MovParcela_percentualMulta=Percentual Multa\:
prt_MovParcela_percentualJuro=Percentual Juro\:
prt_MovParcela_utilizaConvenio=Utiliza Conv\u00EAnio\:
prt_MovParcela_convenioCobranca=Conv\u00EAnio de Cobranca\:
prt_MovParcela_imprimirBoletoParcela=ImprimirBoletoParcela\:
prt_MovParcela_contato=N\u00BA do Contrato\:
prt_MovParcela_pessoa=Nome Cliente\:
prt_MovParcela_label_cliente=Cliente
prt_MovParcela_descricao=Descri\u00E7\u00E3o\:
prt_MovParcela_valorParcela=Valor da Parcela\:
prt_MovParcela_label_valorParcela=Valor
prt_MovParcela_label_empresa=Empresa
prt_Forncedor_label_codigo=C\u00F3digo
prt_Fornecedor_label_nome=Fornecedor
prt_Fornecedor_label_contato=Contato
prt_FormaPagamento_tituloForm=Formas de Pagamento
prt_FormaPagamento_codigo=C\u00F3digo\:
prt_FormaPagamento_label_codigo=C\u00F3digo
prt_FormaPagamento_descricao=Descri\u00E7\u00E3o\:
prt_FormaPagamento_label_descricao=Descri\u00E7\u00E3o
prt_FormaPagamento_convenio=Conv\u00EAnio\:
prt_FormaPagamento_label_convenio=Conv\u00EAnio
prt_FormaPagamento_tipoFormaPagamento=Tipo Forma de Pagamento\:
prt_FormaPagamento_label_tipoFormaPagamento=Tipo
prt_FormaPagamento_taxaCartao=Taxa do Cart\u00E3o\:
prt_FormaPagamento_taxaAntecipacao=Taxa da Antecipa\u00E7\u00E3o:
prt_FormaPagamento_taxaPix=Taxa do Pix\:
prt_FormaPagamento_taxaBoleto=Taxa do Boleto\:
prt_FormaPagamento_adquirente=Adquirente
prt_FormaPagamento_bandeira=Operadora
prt_FormaPagamento_defaultRecorrencia=Default Recorr\u00EAncia\:
prt_FormaPagamento_exigeCodAutorizacao=Obrigat\u00F3rio Informar Codigo Autoriza\u00E7\u00E3o\:
prt_FormaPagamento_defaultDco=Default DCO\:
prt_FormaPagamento_ativo=Ativo\:
prt_FormaPagamento_label_situacao=Situacao
prt_FormaPagamento_somenteFinanceiro=Usar Somente no Financeiro\:
prt_FormaPagamento_compensacaoDiasUteis=Compensa\u00E7\u00E3o apenas em dias \u00FAteis\:
prt_FormaPagamento_apresentarNSU=Apresentar campo NSU\:
prt_FormaPagamento_perfilAcesso=Perfil Acesso
prt_FormaPagamento_pinpad=Pinpad
prt_FormaPagamento_pdvpinpad=PDV Pinpad
prt_FormaPagamento_cnpjpinpad=CNPJ Pinpad
prt_FormaPagamento_diasCompensacaoCartaoCredito=Dias compensa\u00E7\u00E3o cart\u00E3o cr\u00E9dito\:
prt_FormaPagamento_vigenciaInicial=Vig\u00EAncia Inicial\:
prt_FormaPagamento_vigenciaFinal=Vig\u00EAncia Final\:
prt_PagamentoMovParcela_tituloForm=Pagamento Movimento Parcela
prt_PagamentoMovParcela_codigo=C\u00F3digo\:
prt_PagamentoMovParcela_movPagamento=Movimenta\u00E7\u00E3o Pagamento\:
prt_PagamentoMovParcela_movParcela=Movimenta\u00E7\u00E3o Parcela\:
prt_PagamentoMovParcela_descricao=Descri\u00E7\u00E3o\:
prt_PagamentoMovParcela_valorParcela=Valor da Parcela\:
prt_PagamentoMovParcela_valorPago=Valor da Pago\:
prt_PagamentoMovParcela_situacao=Situa\u00E7\u00E3o\:
prt_MovPagamento_tituloForm=Movimento do Pagamento
prt_MovPagamento_codigo=C\u00F3digo\:
prt_MovPagamento_label_codigo=C\u00F3digo
prt_MovPagamento_pessoa=* Pessoa\:
prt_MovPagamento_label_cliente=Cliente
prt_MovPagamento_dataPagamento=* Data Pagamento\:
prt_MovPagamento_label_dataPagamento=Pagamento
prt_MovPagamento_dataLancamento=* Data Lan\u00E7amento\:
prt_MovPagamento_valor=Valor\:
prt_MovPagamento_label_valorTotal=Valor
prt_MovPagamento_formaPagamento=* Forma Pagamento\:
prt_MovPagamento_label_formaPagamento=Forma de Pagamento
prt_MovPagamento_label_empresa=Empresa
prt_MovPagamento_nomePagador=* Nome do Pagador\:
prt_MovPagamento_cpfPagador=* CPF do Pagador\:
prt_MovPagamento_numeroCheque=* N\u00FAmero Cheque\:
prt_MovPagamento_agenciaCheque=* Ag\u00EAncia Cheque\:
prt_MovPagamento_bancoCheque=* Banco Cheque\:
prt_MovPagamento_numeroCartao=* N\u00FAmero Cart\u00E3o\:
prt_MovPagamento_codigoOperacaoCartao=* C\u00F3digo Opera\u00E7\u00E3o Cart\u00E3o\:
prt_Usuario_tituloForm=Usu\u00E1rio
prt_Usuario_codigo=* C\u00F3digo\:
prt_Usuario_label_codigo=C\u00F3digo
prt_Usuario_nome=* Nome da Pessoa\:
prt_Usuario_label_nome=Nome
prt_Usuario_username=* Nome do Usu\u00E1rio\:
prt_Usuario_telefone=Celular\:
prt_Usuario_label_username=Usu\u00E1rio
prt_Usuario_senha=* Senha\:
prt_Usuario_confirmar_senha=* Confirmar Senha\:
prt_Usuario_pin=* PIN (N\u00FAmero de Identifica\u00E7\u00E3o Pessoal)\:
prt_Usuario_situacao=Situa\u00E7\u00E3o\:
prt_Usuario_serviceUsuario=Usu\u00E1rio do Service\:
prt_Usuario_serviceSenha=Senha do Service\:
prt_Usuario_dataAlteracaosenha=Data da \u00DAltima Altera\u00E7\u00E3o da Senha
prt_Usuario_codPerfilAcesso=* Perfil de Acesso\:
prt_Usuario_label_PerfilAcesso=Perfil de Acesso
prt_Usuario_label_situacao=Situa\u00E7\u00E3o
prt_Usuario_nomePessoa=Nome do Cliente\:
prt_Usuario_colaborador=Nome do Colaborador\:
prt_Usuario_operador=Nome do Operador\:
prt_Usuario_cpf=CPF\:
prt_Usuario_dataNasc=Data de Nascimento\:
prt_Usuario_tipoUsuario=* Tipo de Usu\u00E1rio\:
prt_Usuario_administrador=Administrador\:
prt_Usuario_permiteAlteracaoPropriaSenha=Permite Altera\u00E7\u00E3o da Pr\u00F3pria Senha
prt_ModuloNotas_tituloForm=M\u00F3dulo de Notas
prt_ModuloNotas_permissao_alterar_rps=Permiss\u00E3o alterar RPS\:
prt_PerfilAcesso_tituloForm=Perfil de Acesso
prt_PerfilAcesso_perfilCriadoAlterado=Perfil que est\u00E1 sendo criado/alterado
prt_PerfilAcesso_copiarPermissoes=Copiar permiss\u00F5es de outro perfil
prt_PerfilAcesso_expandirModulos=Expandir M\u00F3dulos
prt_PerfilAcesso_marcarTodos=Marcar Todos
prt_PerfilAcesso_retrairModulos=Retrair M\u00F3dulos
prt_PerfilAcesso_codigo=C\u00F3digo\:
prt_PerfilAcesso_label_codigo=C\u00F3digo
prt_PerfilAcesso_nome=NOME
prt_PerfilAcesso_tipo=TIPO DE PERFIL
prt_PerfilAcesso_label_descricao=Descri\u00E7\u00E3o
prt_PerfilAcesso_Desconto_Contrato=Negocia\u00E7\u00E3o de contratos\:
prt_UsuarioPerfilAcesso_tituloForm=Usu\u00E1rio Perfil Acesso
prt_UsuarioPerfilAcesso_empresa=* Nome da Empresa\:
prt_UsuarioPerfilAcesso_codPerfilAcesso=* Nome do Perfil de Acesso\:
prt_Permissao_tituloForm=Permiss\u00E3o
prt_Permissao_codPerfilAcesso=Perfil de Acesso\:
prt_Permissao_nomeEntidade=Nome da Entidade\:
prt_Permissao_permissoes=Tipo de Acesso\:
prt_Tipo_Acesso_Maiusculo=TIPO DE ACESSO\:
prt_Permissao_tituloApresentacao=T\u00EDtulo Permiss\u00E3o\:
prt_Permissao_tipoPermissao=Tipo Permiss\u00E3o\:
prt_Permissao_valorEspecifico=Valor Permitido\:
prt_Permissao_valorInicial=Valor Permitido Inicial\:
prt_Permissao_valorFinal=Valor Permitido Final\:
prt_Permissao_nomeAcoes=Nome da A\u00E7\u00E3o\:
prt_Permissao_Acoes=A\u00E7\u00E3o\:
prt_Permissao_Funcionalidades=Funcionalidade\:
prt_Permissao_Modulos=M\u00F3dulo\:
prt_Permissao_nomeModulos=Nome do M\u00F3dulo\:
prt_PlanoHorario_tituloForm=Hor\u00E1rios do Plano
prt_PlanoHorario_codigo=C\u00F3digo\:
prt_PlanoHorario_horario=* Hor\u00E1rio\:
prt_PlanoHorario_tipoOperacao=Tipo Opera\u00E7\u00E3o\:
prt_PlanoHorario_formaDesconto=Forma de C\u00E1lculo\:
prt_PlanoHorario_valorEspecifico=* Valor\:
prt_PlanoHorario_percentualDesconto=* Percentual\:
prt_PlanoHorario_plano=Plano\:
prt_PlanoTextoPadrao_tituloForm=Modelo de Contrato
prt_PlanoTextoPadrao_codigo=C\u00F3digo\:
prt_PlanoTextoPadrao_label_codigo=C\u00F3digo
prt_PlanoTextoPadrao_descricao=* Descri\u00E7\u00E3o\:
prt_PlanoTextoPadrao_label_descricao=Descri\u00E7\u00E3o
prt_PlanoTextoPadrao_dataDefinicao=* Data Defini\u00E7\u00E3o\:
prt_PlanoTextoPadrao_label_dataDefinicao=Defini\u00E7\u00E3o
prt_PlanoTextoPadrao_responsavelDefinicao=* Respons\u00E1vel Defini\u00E7\u00E3o\:
prt_PlanoTextoPadrao_label_responsavelDefinicao=Respons\u00E1vel
prt_PlanoTextoPadrao_texto=* Texto\:
prt_PlanoTextoPadrao_imagem=* Insira a logomarca no contrato\:
prt_PlanoTextoPadrao_situacao=* Situa\u00E7\u00E3o\:
prt_PlanoTextoPadrao_Tipo=* Tipo\:
prt_HorarioTurma_tituloForm=Hor\u00E1rio Turma
prt_HorarioTurma_codigo=C\u00F3digo\:
prt_HorarioTurma_turma=Turma\:
prt_HorarioTurma_identificador=* Identificador\:
prt_HorarioTurma_diaSemana=* Dia da Semana\:
prt_HorarioTurma_horaInicial=* Hora Inicial\:
prt_HorarioTurma_toleranciaEntradaMinutos=Toler\u00E2ncia\:
prt_HorarioTurma_horaFinal=* Hora Final\:
prt_HorarioTurma_professor=* Professor\:
prt_HorarioTurma_ambiente=* Ambiente\:
prt_HorarioTurma_nivelTurma=* N\u00EDvel da Turma\:
prt_HorarioTurma_situacao=* Situa\u00E7\u00E3o\:
prt_HorarioTurma_nrMaximoAluno=* N\u00BA M\u00E1x. de Alunos\:
prt_HorarioTurma_liberadoMarcacaoApp=Mostrar Aula no APP:
prt_CondicaoPagamentoParcela_tituloForm=Condi\u00E7\u00E3o de Pagamento da Parcela
prt_CondicaoPagamentoParcela_codigo=C\u00F3digo\:
prt_CondicaoPagamentoParcela_condicaoPagamento=Condi\u00E7\u00E3o de Pagamento\:
prt_CondicaoPagamentoParcela_percentualParcela=Percentual da Parcela\:
prt_CondicaoPagamentoParcela_nrDiasParcela=N\u00FAmero de Dias\:
prt_CondicaoPagamentoParcela_nrParcela=N\u00FAmero da Parcela\:
prt_FichaTecnica_tituloForm=Ficha T\u00E9cnica
prt_FichaTecnica_nivel=N\u00EDvel
prt_FichaTecnica_estagio=Est\u00E1gio
prt_FichaTecnica_aula=Aula
prt_FichaTecnica_adicionarCampo=Adicionar Campo
prt_FichaTecnica_editarTabela=Editar Tabela
prt_FichaTecnica_editandoTabela=EDITANDO TABELA
prt_Turma_tituloForm=Turma
prt_Turma_codigo=C\u00F3digo\:
prt_Turma_label_codigo=C\u00F3digo
prt_Turma_descricao=* Nome da Turma\:
prt_Turma_label_descricao=Descri\u00E7\u00E3o
prt_Turma_identificador=* Identificador da Turma\:
prt_Turma_label_identificador=Identificador
prt_Turma_modalidade=* Modalidade\:
prt_Turma_label_modalidade=Modalidade
prt_Turma_empresa=* Empresa\:
prt_Turma_label_empresa=Empresa
prt_Turma_dataInicialVigencia=* Data Inicial de Vig\u00EAncia\:
prt_Turma_dataFinalVigencia=* Data Final de Vig\u00EAncia\:
prt_Turma_idadeMinima=Idade M\u00EDnima (Anos)\:
prt_Turma_label_idadeMinima=Idade M\u00EDnima
prt_Turma_idadeMeses=(Meses)\:
prt_Turma_idadeMaxima=* Idade M\u00E1xima (Anos)\:
prt_Turma_label_idadeMaxima=Idade M\u00E1xima
prt_Turma_bloquearMatriculasAcimaLimite=Bloquear Matr\u00EDculas Acima do Limite\:
prt_Turma_bloquearReposicoesAcimaLimite=Bloquear Reposi\u00E7\u00F5es Acima do Limite\:
prt_Turma_permitirDesmarcarReposicoes=Permitir Desmarcar Reposi\u00E7\u00F5es\:
prt_Turma_monitorar=Monitorar presen\u00E7as,faltas e reposi\u00E7\u00F5es\:
prt_Turma_monitorada=Monitorada\:
prt_Turma_permitirAulaExperimental=Permitir incluir visitantes
prt_ConsultaAlunoTurma_descricao=Nome da Turma\:
prt_ConsultaAlunoTurma_professor=Professor\:
prt_ConsultaAlunoTurma_nivel=N\u00EDvel\:
prt_ConsultaAlunoTurma_ambiente=Ambiente\:
prt_ConsultaAlunoTurma_horaInicio=Hora In\u00EDcio\:
prt_ConsultaAlunoTurma_horaFinal=Hora Fim\:
prt_ConsultaAlunoTurma_diaDaSemana=Dia da Semana\:
prt_ConsultaAlunoTurma_nrAlunoMatriculado=N\u00BA alunos matriculados\:
prt_ConsultaAlunoTurma_nrAlunoReposicao=N\u00BA alunos reposi\u00E7\u00E3o\:
prt_ConsultaAlunoTurma_nrAlunoMatriculaFutura=N\u00BA matr\u00EDculas futuras\:
prt_ConsultaAlunoTurma_nrMaximoAluno=N\u00FAmero m\u00E1ximo de alunos\:
prt_ConsultaAlunoTurma_dataChamada=Data da Chamada\:
prt_ConsultaAlunoTurma_listaAlunos=Lista de Alunos
prt_ConsultaAlunoTurma_Horario_codigo=C\u00F3digo Hor\u00E1rio\:
prt_ConsultaAlunoTurma_Turma_codigo=C\u00F3digo Turma\:
prt_Modalidade_tituloForm=Modalidade
prt_Modalidade_codigo=C\u00F3digo\:
prt_Modalidade_label_codigo=C\u00F3digo
prt_Modalidade_nome=* Nome\:
prt_Modalidade_label_descricao=Descri\u00E7\u00E3o
prt_Modalidade_ativo=Ativo\:
prt_Modalidade_label_situcao=Situa\u00E7\u00E3o
prt_Modalidade_valorMensal=Valor Mensal\:
prt_Modalidade_label_valor=Valor
prt_Modalidade_nrVezes=* N\u00FAmero de Vezes por Semana\:
prt_Modalidade_frequenciasPossiveis=Frequ\u00EAncias poss\u00EDveis\:
prt_Modalidade_vagasDisponiveis=Vagas dispon\u00EDveis\:
prt_Modalidade_label_nrVezes=Nr de Vezes
prt_Modalidade_utilizarTurma=Utilizar Turma\:
prt_Modalidade_modalidadeDefault=Modalidade Padr\u00E3o\:
prt_Modalidade_label_empresa=Empresa(s)
prt_PlanoModalidade_tituloForm=Plano Modalidade
prt_PlanoModalidade_codigo=C\u00F3digo\:
prt_PlanoModalidade_plano=Plano\:
prt_PlanoModalidade_modalidade=* Modalidade\:
prt_PlanoModalidade_valor=Valor da Modalidade no Mensal\:
prt_PlanoModalidade_valorNoPlano=Valor de Refer\u00EAncia neste Plano\:
prt_PlanoModalidade_valorAjustado=Valor Ajustado\:
prt_PlanoModalidade_modalidadeDefault=Modalidade Padr\u00E3o\:
prt_DescontoRenovacao_tituloForm=Desconto Renovacao
prt_DescontoRenovacao_codigo=C\u00F3digo\:
prt_DescontoRenovacao_nome=* Descri\u00E7\u00E3o\:
prt_DescontoRenovacao_intervalos=Intervalos
prt_DescontoRenovacao_tipoIntervalo=* Tipo Intervalo\:
prt_DescontoRenovacao_tipoDesconto=* Tipo Desconto\:
prt_DescontoRenovacao_intervaloDe=Intervalo De\:
prt_DescontoRenovacao_intervaloAte=Intervalo Ate\:
prt_DescontoRenovacao_valorDesconto=* Valor Desconto\:
prt_NivelTurma_tituloForm=N\u00EDvel Turma
prt_NivelTurma_codigo=C\u00F3digo\:
prt_NivelTurma_label_codigo=C\u00F3digo
prt_NivelTurma_descricao=* Descri\u00E7\u00E3o\:
prt_NivelTurma_label_descricao=Descri\u00E7\u00E3o
prt_Ambiente_tituloForm=Ambiente
prt_Ambiente_codigo=C\u00F3digo\:
prt_Ambiente_label_codigo=C\u00F3digo
prt_Ambiente_descricao=* Descri\u00E7\u00E3o\:
prt_Ambiente_label_descricao=Descri\u00E7\u00E3o
prt_Ambiente_capacidade=* Capacidade\:
prt_Ambiente_situacao=* Situa\u00E7\u00E3o\:
prt_Ambiente_label_situacao=Situa\u00E7\u00E3o
prt_Ambiente_tipoAmbiente=* Tipo do Ambiente\:
prt_Ambiente_label_TipoAmbiente=Tipo
prt_Ambiente_situacaoAmbiente=* Situa\u00E7\u00E3o do Ambiente\:
prt_TipoAmbiente_tituloForm=Tipo Ambiente
prt_Plano_tituloForm=Plano
prt_Plano_codigo=C\u00F3digo\:
prt_Plano_label_codigo=C\u00F3digo
prt_Plano_descricao=* Descri\u00E7\u00E3o\:
prt_Plano_label_descricao=Descri\u00E7\u00E3o
prt_Plano_label_site=Site\:
prt_Plano_label_permitirVendaPlanoSiteNoBalcao=Permitir venda no balc\u00E3o\:
prt_Plano_label_permitirVendaPlanoTotemNoBalcao=Permitir venda de plano Totem no balc\u00E3o\:
prt_plano_label_restringeVendaPorCategoria=Restringir venda por categoria do cliente\:
prt_PlanoCategoria_tituloForm=Categoria\:
prt_Plano_label_restigir_marcacoes_aulacoletiva=Restringir Marca\u00E7\u00F5es de Aulas Coletivas(quantidade de vezes por semana da modalidade)\:
prt_Plano_label_restigir_marcacoes_aulacoletiva_validando_mesmo_dia=Validar vezes na semana para marca\u00E7\u00E3o de aula contabilizando no mesmo dia\:
prt_Plano_label_restigir_um_marcacoes_pordia_aulacheia=Restringir Quantidade Marca\u00E7\u00E3o Por Dia de Aula Coletiva(por modalidade)\:
prt_Plano_label_restigir_um_marcacoes_pordia=Restringir Quantidade Marca\u00E7\u00E3o Por Dia de Aula Coletiva(geral)\:
prt_Plano_label_totem=Totem\:
prt_Plano_label_totem_hint=Marque esta op\u00E7\u00E3o caso queira que este plano seja vendido no Autoatendimento. S\u00F3 aparecer\u00E3o no Autoatendimento os planos que possuem esta flag marcada e est\u00E3o vigentes.
prt_Plano_empresa=* Empresa\:
prt_Plano_label_empresa=Empresa
prt_Plano_bolsa=Bolsa\:
prt_Plano_inicioMinimoContrato=Contratos iniciam a partir do dia\:
prt_Plano_parcelamentoOperadora=Parcelamento pela operadora\:
prt_Plano_descontoAntecipado=Desconto em Renova\u00E7\u00E3o Antecipada\:
prt_Plano_vigenciaDe=* Vig\u00EAncia De\:
prt_Plano_label_vigenciaDe=In\u00EDcio
prt_Plano_vigenciaAte=* Vig\u00EAncia At\u00E9\:
prt_Plano_label_vigenciaAte=Fim
prt_Plano_ingressoAte=* Ingresso At\u00E9\:
prt_Plano_label_ingressoAte=Prazo de Ingresso
prt_Plano_recorrencia=Recorr\u00EAncia
prt_Plano_permitePagarComBoleto=Permite Pagar Com Boleto\:
prt_Plano_comissao=Comiss\u00E3o
prt_Plano_produtoPadraoGerarParcelasContrato=* Produto Padr\u00E3o Gerar Parcelas Contrato\:
prt_Plano_planoTextoPadrao=* Texto Contrato do Plano\:
prt_Plano_reciboTextoPadrao=* Texto Recibo\:
prt_Plano_produtoTaxaCancelamento=* Produto Taxa Cancelamento\:
prt_Plano_percentualMultaCancelamento=* Percentual da Multa Cancelamento\:
prt_Plano_apresentaVendaRapida=Apresentar no Venda R\u00E1pida\:
prt_Produto_tituloForm=Produto
prt_Produto_codigo=C\u00F3digo\:
prt_Produto_label_codigo=C\u00F3digo
prt_Produto_categoriaProduto=* Categoria de Produto\:
prt_Produto_label_categoriaProduto=Categoria
prt_Produto_descricao=* Descri\u00E7\u00E3o\:
prt_Produto_label_descricao=Descri\u00E7\u00E3o
prt_Produto_capacidade=* Capacidade\:
prt_Produto_valor=* Valor\:
prt_Produto_label_valor=Valor
prt_Produto_tipoVigencia=Tipo de Vig\u00EAncia\:
prt_Produto_dataInicioVigencia=* Data In\u00EDcio de Vig\u00EAncia\:
prt_Produto_dataFinalVigenciaFixa=* Data Final de Vig\u00EAncia\:
prt_Produto_nrDiasVigencia=* N\u00FAmero de Dias de Vig\u00EAncia\:
prt_Produto_renovavelAutomaticamente=Renov\u00E1vel Automaticamente:
prt_Produto_bloqueiaPelaVigencia=Bloquear acesso ap\u00F3s Dias de Vig\u00EAncia\:
prt_Produto_possuiValidade=Possui Validade\:
prt_Produto_nrDiasValidade=* N\u00FAmero de Dias de Validade\:
prt_Produto_possuiEstoque=Possui Estoque\:
prt_Produto_estoque=* Estoque\:
prt_Produto_tipoProduto=* Tipo de Produto\:
prt_Produto_ncm=NCM\:
prt_Produto_cfop=CFOP\:
prt_Produto_ncmNFCe=NCM para NFC-e\:
prt_Produto_codigoListaServico=C\u00F3digo Lista Servi\u00E7o\:
prt_Produto_codigoTributacaoMunicipio=C\u00F3digo Tributa\u00E7\u00E3o Munic\u00EDpio\:
prt_Produto_aliquotaPIS=Al\u00EDquota PIS\:
prt_Produto_aliquotaCOFINS=Al\u00EDquota COFINS\:
prt_Produto_aliquotaICMS=Al\u00EDquota ICMS\:
prt_Produto_aliquotaISSQN=Al\u00EDquota ISSQN\:
prt_Produto_label_tipoProduto=Tipo
prt_Produto_desativado=Desativado\:
prt_Produto_label_situacao=Situa\u00E7\u00E3o
prt_Produto_observacao=Observa\u00E7\u00E3o\:
prt_Produto_quantidade_convites=Quantidade de convites\:
prt_Produto_permitir_freepass_contrato=Permitir aplicar freepass com contrato\:
prt_ConfiguracaoEstoque_relEstoqueProduto=Relat\u00F3rio Estoque de Produto
prt_TamanhoArmario_tituloForm=Tamanho de Arm\u00E1rio
prt_Cliente_armario=Cliente
prt_Produto_armario=Arm\u00E1rio
prt_DataFim_armario=Fim
prt_CategoriaProduto_tituloForm=Categoria de Produto
prt_CategoriaProduto_codigo=C\u00F3digo\:
prt_CategoriaProduto_label_codigo=C\u00F3digo
prt_CategoriaProduto_descricao=* Descri\u00E7\u00E3o\:
prt_CategoriaProduto_label_descricao=Descri\u00E7\u00E3o
prt_Balanco_tituloForm=Balan\u00E7o
prt_Balanco_codigo=C\u00F3digo\:
prt_Balanco_label_codigo=C\u00F3digo
prt_Balanco_label_dataBalanco=Data
prt_Balanco_label_situacao=Situa\u00E7\u00E3o
prt_Balanco_label_empresa=Empresa
prt_Compra_tituloForm=Compra
prt_Compra_codigo=C\u00F3digo\:
prt_Compra_label_codigo=C\u00F3digo
prt_Compra_label_fornecedor=Fornecedor
prt_Compra_label_dataCadastro=Data da Compra
prt_Compra_label_dataLancamento=Data de Lan\u00E7amento
prt_Compra_label_situacao=Situa\u00E7\u00E3o
prt_Compra_label_qtdade=Quantidade
prt_Compra_label_empresa=Empresa
prt_Compra_label_valorTotal=Valor
prt_ProdutoEstoque_tituloForm=Configurar Produto Estoque
prt_ProdutoEstoque_codigo=C\u00F3digo\:
prt_ProdutoEstoque_label_codigo=C\u00F3digo
prt_ProdutoEstoque_label_produto=Produto
prt_ProdutoEstoque_label_situacao=Situa\u00E7\u00E3o
prt_ProdutoEstoque_label_empresa=Empresa
prt_ProdutoEstoque_label_estoque=Estoque Atual
prt_ProdutoEstoque_label_estoqueMinimo=Estoque M\u00EDnimo
prt_ProdutoSugerido_tituloForm=Produto Sugerido
prt_ProdutoSugerido_codigo=C\u00F3digo\:
prt_ProdutoSugerido_modalidade=Modalidade\:
prt_ProdutoSugerido_produto=Produto\:
prt_ProdutoSugerido_valorProduto=Valor\:
prt_ProdutoSugerido_obrigatorio=Obrigat\u00F3rio\:
prt_PlanoProdutoSugerido_tituloForm=Produtos Sugeridos
prt_PlanoProdutoSugerido_codigo=C\u00F3digo\:
prt_PlanoProdutoSugerido_produto=Produto\:
prt_PlanoProdutoSugerido_valorProduto=Valor do Produto para esse Plano\:
prt_PlanoProdutoSugerido_obrigatorio=Obrigat\u00F3rio\:
prt_PlanoProdutoSugerido_situacaoProduto=Situa\u00E7\u00E3o Produto\:
prt_Composicao_tituloForm=Pacote
prt_Composicao_codigo=C\u00F3digo\:
prt_Composicao_label_codigo=C\u00D3DIGO
prt_Composicao_plano=Plano\:
prt_Composicao_descricao=* Descri\u00E7\u00E3o\:
prt_Composicao_label_descricao=DESCRI\u00C7\u00C3O
prt_Composicao_precoComposicao=Valor Total do Pacote\:
prt_Composicao_label_valorComposicao=VALOR
prt_Composicao_label_empresa=EMPRESA
prt_Composicao_composicaoAdicional=Pacote Adicional\:
prt_Composicao_composicaoDefault=Pacote Padr\u00E3o\:
prt_Composicao_escolherModalidades=Modalidades espec\u00EDficas\:
prt_Pacote_tituloForm=Pacote
prt_VezesSemana_tituloForm=Vezes Semana
prt_VezesSemana_codigo=C\u00F3digo\:
prt_VezesSemana_nrVezes=* N\u00FAmero de Vezes\:
prt_VezesSemana_percentualDesconto=* Percentual de Desconto\:
prt_VezesSemana_forma_Desconto=Forma\:
prt_VezesSemana_valorEspecifico=* Valor\:
prt_VezesSemana_vezeSemanaDefault=Vezes Semana Padr\u00E3o\:
prt_PlanoComposicao_tituloForm=Pacote do Plano
prt_PlanoComposicao_codigo=C\u00F3digo\:
prt_PlanoComposicao_plano=Plano\:
prt_PlanoComposicao_composicao=* Pacote\:
prt_PlanoCategoria_categoria=* Categoria\:
prt_PlanoComposicao_precoComposicao=* Valor Mensal\:
prt_Banco_tituloForm=Banco
prt_Banco_codigo=C\u00F3digo\:
prt_Banco_label_codigo=C\u00F3digo
prt_Banco_codigoBanco=* C\u00F3digo do Banco\:
prt_Banco_label_codigoBanco=C\u00F3digo do Banco
prt_Banco_nome=* Nome\:
prt_Banco_label_nome=Nome
prt_vendaConsumidor_tituloForm=Venda de Consumidor
prt_vendaConsumidor_codigo=C\u00F3digo da Venda
prt_vendaConsumidor_label_codigo=C\u00F3digo
prt_vendaConsumidor_nome=Nome do Consumidor
prt_vendaConsumidor_label_nomeConsumidor=Consumidor
prt_vendaConsumidor_label_responsavel=Respons\u00E1vel
prt_vendaConsumidor_label_empresa=Empresa
prt_vendaConsumidor_valorCompra=Valor da Compra
prt_vendaConsumidor_label_valorCompra=Valor
prt_vendaConsumidor_dataRegistro=Data de Registro
prt_vendaConsumidor_label_dataRegistro=Data
prt_ContaCorrente_tituloForm=Conta Corrente
prt_ContaCorrente_codigo=C\u00F3digo\:
prt_ContaCorrente_label_codigo=C\u00F3digo
prt_ContaCorrente_agencia=* Ag\u00EAncia\:
prt_ContaCorrente_label_agencia=Ag\u00EAncia
prt_ContaCorrente_agenciaDV=* D\u00EDgito Verificador da Ag\u00EAncia\:
prt_ContaCorrente_label_agenciaDV=D\u00EDgito Verificador da Ag\u00EAncia
prt_ContaCorrente_contaCorrente=* Conta Corrente\:
prt_ContaCorrente_label_contaCorrente=Conta Corrente
prt_ContaCorrente_contaCorrenteDV=* D\u00EDgito Verificador da Conta Corrente\:
prt_ContaCorrente_label_contaCorrenteDV=D\u00EDgito Verificador da Conta Corrente
prt_ContaCorrente_banco=* Banco\:
prt_ContaCorrente_label_banco=Banco
prt_Duracao_tituloForm=DURA\u00C7\u00C3O
prt_Duracao_caixa_baixa=Dura\u00E7\u00E3o
prt_Duracao_codigo=C\u00F3digo\:
prt_Duracao_numeroMeses=* N\u00FAmero de Meses\:
prt_Duracao_duracaoDefault=Dura\u00E7\u00E3o Padr\u00E3o\:
prt_Horario_tituloForm=Hor\u00E1rio
prt_Horario_codigo=C\u00F3digo\:
prt_Horario_ativo=Ativo:
prt_Horario_label_codigo=C\u00F3digo
prt_Horario_descricao=* Descri\u00E7\u00E3o\:
prt_Horario_label_descricao=Descri\u00E7\u00E3o
prt_Horario_formaDesconto=Forma de C\u00E1lculo\:
prt_Horario_percentualDesconto=* Percentual\:
prt_Horario_valorEspecifico=* Valor\:
prt_Horario_livre=Livre\:
prt_Horario_horarioDefault=Hor\u00E1rio Padr\u00E3o\:
prt_CondicaoPagamento_tituloForm=Condi\u00E7\u00E3o de Pagamento
prt_CondicaoPagamento_codigo=C\u00F3digo\:
prt_CondicaoPagamento_label_codigo=C\u00F3digo
prt_CondicaoPagamento_descricao=* Descri\u00E7\u00E3o\:
prt_CondicaoPagamento_label_descricao=Descri\u00E7\u00E3o
prt_CondicaoPagamento_nrParcelas=* N\u00FAmero de Parcelas\:
prt_CondicaoPagamento_label_nrParcelas=Nr. de Parcelas
prt_CondicaoPagamento_condicaoPagamentoDefault=Condi\u00E7\u00E3o de Pagamento Padr\u00E3o
prt_CondicaoPagamento_entrada=Entrada\:
prt_CondicaoPagamento_percentualValorEntrada=Percentual de Entrada\:
prt_CondicaoPagamento_label_percentualValorEntrada=Percentual de Entrada
prt_CondicaoPagamento_intervaloEntreParcelas=* Intervalo de Dias Entre as Parcelas\:
prt_CondicaoPagamento_tipoConvenioCobranca=* Tipo Conv\u00EAnio Cobran\u00E7a\:
prt_CondicaoPagamento_label_tipoConvenioCobranca=Conv\u00EAnio Cobran\u00E7a
prt_TaxaComissao_tituloForm=Taxas de comiss\u00E3o para Consultor
prt_TaxaComissao_rematricula=Rematr\u00EDcula
prt_TaxaComissao_outraDuracao=Outra dura\u00E7\u00E3o
prt_TaxaComissao_valorEspontaneo=VL. FIXO ESP.
prt_TaxaComissao_valorFixoAgendado=VL. FIXO AGEN.
prt_TaxaComissao_porcentagemEspontaneo=PORC. ESP.
prt_TaxaComissao_porcentagemAgendado=PORC. AGEN.
prt_PlanoCondicaoPagamento_tituloForm=Plano Condi\u00E7\u00E3o Pagamento
prt_PlanoCondicaoPagamento_codigo=C\u00F3digo\:
prt_PlanoCondicaoPagamento_plano=Plano\:
prt_PlanoCondicaoPagamento_condicaoPagamento=* Condi\u00E7\u00E3o de Pagamento\:
prt_PlanoCondicaoPagamento_tipoOperacao=Tipo Opera\u00E7\u00E3o\:
prt_PlanoCondicaoPagamento_formaDesconto=* Forma de C\u00E1lculo\:
prt_PlanoCondicaoPagamento_valorEspecifico=* Valor (baseado no valor total do plano)\:
prt_PlanoCondicaoPagamento_percentualDesconto=* Percentual (baseado no valor total do plano)\:
prt_ComposicaoModalidade_tituloForm=Pacote de Modalidades
prt_ComposicaoModalidade_codigo=C\u00F3digo\:
prt_ComposicaoModalidade_modalidade=Modalidade\:
prt_ComposicaoModalidade_composicao=* Pacote\:
prt_ComposicaoModalidade_precoModalidade=Valor da Modalidade\:
prt_ComposicaoModalidade_valorMensalComposicao=Valor Modalidade para o Pacote\:
prt_ComposicaoModalidade_qtdeModalidade=Quantidade de Modalidades\:
prt_ComposicaoModalidade_vezesSemana=Vezes por Semana\:
prt_ComposicaoModalidade_vezesSemanaNoPacote=Vezes por Semana no Pacote\:
prt_ComposicaoModalidade_valorForcadoNoPacote=For\u00E7ar Valor no Pacote\:
prt_PlanoModalidadeVezesSemana_tituloForm=Plano Modalidade Vezes Semana
prt_PlanoModalidadeVezesSemana_codigo=C\u00F3digo\:
prt_PlanoModalidadeVezesSemana_vezesSemana=* Vezes na Semana\:
prt_PlanoModalidadeVezesSemana_nrVezes=* N\u00FAmero de Vezes Por Semana\:
prt_PlanoModalidadeVezesSemana_vezesSemanaDefault=Vezes na Semana Refer\u00EAncia\:
prt_PlanoModalidadeVezesSemana_vezesSemanaReferencia=Vezes na Semana Refer\u00EAncia\:
prt_PlanoModalidadeVezesSemana_vezesSemanaDefaultHint=Marque para utilizar este registro Vezes/Semana como Refer\u00EAncia\: substituir\u00E1 o Valor Mensal do Cadastro da Modalidade
prt_PlanoModalidadeVezesSemana_planoModalidade=Plano Modalidade\:
prt_PlanoModalidadeVezesSemana_tipoOperacao=Tipo de Opera\u00E7\u00E3o\:
prt_PlanoModalidadeVezesSemana_valorEspecifico=* Valor\:
prt_PlanoModalidadeVezesSemana_percentualDesconto=* Percentual\:
prt_PlanoModalidadeVezesSemana_tipoValor=Forma de C\u00E1lculo\:
prt_HorarioDisponibilidade_tituloForm=Hor\u00E1rio de Disponibilidade
prt_HorarioDisponibilidade_codigo=C\u00F3digo\:
prt_HorarioDisponibilidade_identificador=Identificador\:
prt_HorarioDisponibilidade_horario=Hor\u00E1rio\:
prt_HorarioDisponibilidade_hora0000=00\:00 at\u00E9 00\:30\:
prt_HorarioDisponibilidade_hora0030=00\:30 at\u00E9 01\:00\:
prt_HorarioDisponibilidade_hora0100=01\:00 at\u00E9 01\:30\:
prt_HorarioDisponibilidade_hora0130=01\:30 at\u00E9 02\:00\:
prt_HorarioDisponibilidade_hora0200=02\:00 at\u00E9 02\:30\:
prt_HorarioDisponibilidade_hora0230=02\:30 at\u00E9 03\:00\:
prt_HorarioDisponibilidade_hora0300=03\:00 at\u00E9 03\:30\:
prt_HorarioDisponibilidade_hora0330=03\:30 at\u00E9 04\:00\:
prt_HorarioDisponibilidade_hora0400=04\:00 at\u00E9 04\:30\:
prt_HorarioDisponibilidade_hora0430=04\:30 at\u00E9 05\:00\:
prt_HorarioDisponibilidade_hora0500=05\:00 at\u00E9 05\:30\:
prt_HorarioDisponibilidade_hora0530=05\:30 at\u00E9 06\:00\:
prt_HorarioDisponibilidade_hora0600=06\:00 at\u00E9 06\:30\:
prt_HorarioDisponibilidade_hora0630=06\:30 at\u00E9 07\:00\:
prt_HorarioDisponibilidade_hora0700=07\:00 at\u00E9 07\:30\:
prt_HorarioDisponibilidade_hora0730=07\:30 at\u00E9 08\:00\:
prt_HorarioDisponibilidade_hora0800=08\:00 at\u00E9 08\:30\:
prt_HorarioDisponibilidade_hora0830=08\:30 at\u00E9 09\:00\:
prt_HorarioDisponibilidade_hora0900=09\:00 at\u00E9 09\:30\:
prt_HorarioDisponibilidade_hora0930=09\:30 at\u00E9 10\:00\:
prt_HorarioDisponibilidade_hora1000=10\:00 at\u00E9 10\:30\:
prt_HorarioDisponibilidade_hora1030=10\:30 at\u00E9 11\:00\:
prt_HorarioDisponibilidade_hora1100=11\:00 at\u00E9 11\:30\:
prt_HorarioDisponibilidade_hora1130=11\:30 at\u00E9 12\:00\:
prt_HorarioDisponibilidade_hora1200=12\:00 at\u00E9 12\:30\:
prt_HorarioDisponibilidade_hora1230=12\:30 at\u00E9 13\:00\:
prt_HorarioDisponibilidade_hora1300=13\:00 at\u00E9 13\:30\:
prt_HorarioDisponibilidade_hora1330=13\:30 at\u00E9 14\:00\:
prt_HorarioDisponibilidade_hora1400=14\:00 at\u00E9 14\:30\:
prt_HorarioDisponibilidade_hora1430=14\:30 at\u00E9 15\:00\:
prt_HorarioDisponibilidade_hora1500=15\:00 at\u00E9 15\:30\:
prt_HorarioDisponibilidade_hora1530=15\:30 at\u00E9 16\:00\:
prt_HorarioDisponibilidade_hora1600=16\:00 at\u00E9 16\:30\:
prt_HorarioDisponibilidade_hora1630=16\:30 at\u00E9 17\:00\:
prt_HorarioDisponibilidade_hora1700=17\:00 at\u00E9 17\:30\:
prt_HorarioDisponibilidade_hora1730=17\:30 at\u00E9 18\:00\:
prt_HorarioDisponibilidade_hora1800=18\:00 at\u00E9 18\:30\:
prt_HorarioDisponibilidade_hora1830=18\:30 at\u00E9 19\:00\:
prt_HorarioDisponibilidade_hora1900=19\:00 at\u00E9 19\:30\:
prt_HorarioDisponibilidade_hora1930=19\:30 at\u00E9 20\:00\:
prt_HorarioDisponibilidade_hora2000=20\:00 at\u00E9 20\:30\:
prt_HorarioDisponibilidade_hora2030=20\:30 at\u00E9 21\:00\:
prt_HorarioDisponibilidade_hora2100=21\:00 at\u00E9 21\:30\:
prt_HorarioDisponibilidade_hora2130=21\:30 at\u00E9 22\:00\:
prt_HorarioDisponibilidade_hora2200=22\:00 at\u00E9 22\:30\:
prt_HorarioDisponibilidade_hora2230=22\:30 at\u00E9 23\:00\:
prt_HorarioDisponibilidade_hora2300=23\:00 at\u00E9 23\:30\:
prt_HorarioDisponibilidade_hora2330=23\:30 at\u00E9 00\:00\:
prt_PlanoDuracao_tituloForm=Dura\u00E7\u00E3o do Plano
prt_PlanoDuracao_codigo=C\u00F3digo\:
prt_PlanoDuracao_duracao=* Dura\u00E7\u00E3o\:
prt_PlanoDuracao_numeroMeses=* N\u00FAmero de Meses\:
prt_PlanoDuracao_plano=Plano\:
prt_PlanoDuracao_nrMaximoParcelasCondPagamento=* N\u00FAmero M\u00E1ximo de Parcelas da Condi\u00E7\u00E3o de Pagamento\:
prt_PlanoDuracao_formaDesconto=Forma de C\u00E1lculo\:
prt_PlanoDuracao_percentualDesconto=* Percentual\:
prt_PlanoDuracao_valorEspecifico=* Valor\:
prt_PlanoDuracao_valorDesejadoMensal=* Valor Desejado Mensal\:
prt_PlanoDuracao_valorDesejadoParcela=* Valor Desejado da Parcela\:
prt_PlanoDuracao_valorDesejadoAjuste=* Valor  mensal  para Ajuste\:
prt_PlanoDuracao_numeroMeses_red=N\u00BA Meses
prt_PlanoDuracao_nrMaximoParcelasCondPagamento_red=Nr Parcelas
prt_PlanoDuracao_situacao_red=Situa\u00E7\u00E3o
prt_PlanoDuracao_formaDesconto_red=Forma de C\u00E1lculo
prt_PlanoDuracao_percentualDesconto_red=Percentual
prt_PlanoDuracao_valorEspecifico_red=Valor
prt_PlanoDuracao_tipoOperacao_red=Opera\u00E7\u00E3o
prt_PlanoDuracao_tipoOperacao=Tipo da Opera\u00E7\u00E3o\:
prt_PlanoDuracao_tipoCalculo=* Tipo do C\u00E1lculo\:
prt_PlanoDuracao_valorDesejado=* Valor Desejado\:
prt_PlanoDuracao_valorTotal=Valor Total
prt_ContratoModalidadeTurma_tituloForm=Contrato Modalidade Turma
prt_ContratoModalidadeTurma_codigo=C\u00F3digo\:
prt_ContratoModalidadeTurma_contratoModalidade=ContratoModalidade\:
prt_ContratoModalidadeTurma_turma=Turma\:
prt_ContratoTextoPadrao_tituloForm=Contrato Texto Padr\u00E3o
prt_ContratoTextoPadrao_codigo=C\u00F3digo\:
prt_ContratoTextoPadrao_descricao=Descri\u00E7\u00E3o\:
prt_ContratoTextoPadrao_dataDefinicao=Data Defini\u00E7\u00E3o\:
prt_ContratoTextoPadrao_responsavelDefinicao=Respons\u00E1vel Defini\u00E7\u00E3o\:
prt_ContratoTextoPadrao_texto=Texto\:
prt_MovProduto_tituloForm=Movimento Produto
prt_MovProduto_codigo=C\u00F3digo\:
prt_MovProduto_label_codigo=C\u00F3digo
prt_MovProduto_produto=Produto\:
prt_MovProduto_label_produto=Produto
prt_MovProduto_contrato=Contrato\:
prt_MovProduto_label_contrato=Contrato
prt_MovProduto_pessoa=Pessoa\:
prt_MovProduto_label_pessoa=Cliente
prt_MovProduto_empresa=Empresa\:
prt_MovProduto_label_empresa=Empresa
prt_MovProduto_descricao=Descri\u00E7\u00E3o\:
prt_MovProduto_quantidade=Quantidade\:
prt_MovProduto_precoUnitario=Pre\u00E7o Unit\u00E1rio\:
prt_MovProduto_valorDesconto=Valor Desconto\:
prt_MovProduto_totalFinal=Total Final\:
prt_MovProduto_dataLancamento=Data Lan\u00E7amento\:
prt_MovProduto_responsavelLancamento=Respons\u00E1vel Lan\u00E7amento\:
prt_MovProduto_label_responsavelLancamento=Respons\u00E1vel
prt_MovProduto_mesReferencia=M\u00EAs Refer\u00EAncia\:
prt_MovProduto_anoReferencia=Ano Refer\u00EAncia\:
prt_MovProduto_dataInicioVigencia=Data de In\u00EDcio de Vig\u00EAncia\:
prt_MovProduto_dataFinalVigencia=Data Final de Vig\u00EAncia\:
prt_MovProduto_situacao=Situa\u00E7\u00E3o\:
prt_ContratoModalidadeHorarioTurma_tituloForm=Contrato Modalidade Hor\u00E1rio Turma
prt_ContratoModalidadeHorarioTurma_codigo=C\u00F3digo\:
prt_ContratoModalidadeHorarioTurma_contratoModalidadeTurma=Contrato Modalidade Turma\:
prt_ContratoModalidadeHorarioTurma_horarioTurma=Hor\u00E1rio Turma\:
prt_ContratoModalidade_tituloForm=Contrato Modalidade
prt_ContratoModalidade_codigo=C\u00F3digo\:
prt_ContratoModalidade_contrato=Contrato\:
prt_ContratoModalidade_modalidade=Modalidade\:
prt_ContratoModalidade_nivelAluno=N\u00EDvel do Aluno\:
prt_ContratoModalidade_valorBaseCalculo=Valor Base C\u00E1lculo\:
prt_ContratoModalidade_valorFinalModalidade=Valor Final da Modalidade\:
prt_HistoricoContrato_tituloForm=Hist\u00F3rico do Contrato
prt_HistoricoContrato_codigo=C\u00F3digo
prt_HistoricoContrato_contrato=Contrato
prt_HistoricoContrato_descricao=Descri\u00E7\u00E3o
prt_HistoricoContrato_responsavelRegistro=Respons\u00E1vel Registro
prt_HistoricoContrato_responsavelLiberacaoMudancaHistorico=Respons\u00E1vel Libera\u00E7\u00E3o Mudan\u00E7a Hist\u00F3rico
prt_HistoricoContrato_dataRegistro=Data Registro
prt_HistoricoContrato_situacaoRelativaHistorico=Situa\u00E7\u00E3o Relativa Hist\u00F3rico Contrato\:
prt_HistoricoContrato_dataInicioSituacao=Data In\u00EDcio
prt_HistoricoContrato_dataFinalSituacao=Data Final
prt_HistoricoContrato_dataLancamento=Data Lan\u00E7amento
prt_Contrato_tituloForm=Contrato
prt_Contrato_codigo=C\u00F3digo\:
prt_Contrato_empresa=Empresa\:
prt_Contrato_pessoa=Pessoa\:
prt_Contrato_plano=Plano\:
prt_Contrato_contratoTextoPadrao=Contrato Texto Padr\u00E3o\:
prt_Contrato_situacao=Situa\u00E7\u00E3o\:
prt_Contrato_estendeCoberturaFamiliares=Estende Cobertura para Familiares\:
prt_Contrato_vigenciaDe=Data de Vig\u00EAncia\:
prt_Contrato_vigenciaAte=Data Final Vig\u00EAncia\:
prt_Contrato_duracao=Dura\u00E7\u00E3o\:
prt_Contrato_horario=Hor\u00E1rio\:
prt_Contrato_condicaoPagamento=Condi\u00E7\u00E3o de Pagamento\:
prt_Contrato_valorBaseCalculo=Valor Base C\u00E1lculo\:
prt_Contrato_valorFinal=Valor Final\:
prt_Contrato_responsavelLiberacaoCondicaoPagamento=Respons\u00E1vel Libera\u00E7\u00E3o Condi\u00E7\u00E3o de Pagamento\:
prt_Contrato_numeroContrato=N\u00FAmero Contrato\:
prt_Contrato_dataInicio=Data de In\u00EDcio\:
prt_Contrato_dataTermino=Data de T\u00E9rmino\:
prt_Contrato_espontaneo=ESPONT\u00C2NEO
prt_Contrato_agendado=AGENDADO
prt_Contrato_Tipodica=Clique para alterar. Voc\u00EA precisa ter a permiss\u00E3o 'Alterar Contrato Agendado/Espont\u00E2neo-Autorizar' para confirmar a altera\u00E7\u00E3o.
prt_Contrato_autorizacaoCobranca=Autoriza\u00E7\u00E3o cobran\u00E7a
prt_Contrato_Tipotitulo=Altera\u00E7\u00E3o do tipo do contrato
prt_Contrato_TipotituloConfirmar=Confirmar Altera\u00E7\u00E3o do tipo do contrato
prt_Convenio_tituloForm=Conv\u00EAnio
prt_Convenio_codigo=C\u00F3digo\:
prt_Convenio_descricao=Descri\u00E7\u00E3o\:
prt_Convenio_dataAssinatura=Data de Assinatura\:
prt_Convenio_dataInicioVigencia=Data de In\u00EDcio Vig\u00EAncia\:
prt_Convenio_dataFinalVigencia=Data Final Vig\u00EAncia\:
prt_Convenio_descontoParcela=Desconto Parcela\:
prt_Convenio_responsavelAutorizacao=Respons\u00E1vel Autoriza\u00E7\u00E3o\:
prt_Convenio_dataAutorizacao=Data Autoriza\u00E7\u00E3o\:
prt_Convenio_situacao=Situa\u00E7\u00E3o\:
prt_TipoRemessa_tituloForm=Tipo de Remessa
prt_TipoRemessa_codigo=C\u00F3digo\:
prt_TipoRemessa_label_codigo=C\u00F3digo
prt_TipoRemessa_descricao=* Descri\u00E7\u00E3o\:
prt_TipoRemessa_label_descricao=Descri\u00E7\u00E3o
prt_TipoRemessa_tipoRetorno=* Tipo de Retorno\:
prt_TipoRemessa_arquivoLayoutRemessa=* Arquivo de Layout da Remessa\:
prt_TipoRemessa_label_arquivoLayoutRemessa=Arquivo de Layout
prt_TipoRemessa_tipoRemessa=*Tipo de Remessa\:
prt_TipoRemessa_label_tipoRemessa=Tipo
prt_Vinculo_tituloForm=V\u00EDnculo
prt_Vinculo_codigo=C\u00F3digo
prt_Vinculo_cliente=Cliente
prt_Vinculo_colaborador=* Colaborador
prt_Vinculo_NaoObrigatorio_colaborador=Colaborador
prt_Vinculo_NaoObrigatorio_colaborador_maiusculo=COLABORADOR
prt_Vinculo_tipoVinculo=* Tipo de V\u00EDnculo\:
prt_Vinculo_NaoObrigatorio_tipoVinculo=Tipo de V\u00EDnculo\:
prt_Vinculo_NaoObrigatorio_tipoVinculo_maiusculo=TIPO V\u00CDNCULO
prt_TipoRetorno_tituloForm=Tipo de Retorno
prt_TipoRetorno_codigo=C\u00F3digo\:
prt_TipoRetorno_label_codigo=C\u00F3digo
prt_TipoRetorno_descricao=* Descri\u00E7\u00E3o\:
prt_TipoRetorno_label_descricao=Descri\u00E7\u00E3o
prt_TipoRetorno_arquivoLayoutRetorno=* Arquivo de Layout do Retorno\:
prt_TipoRetorno_label_arquivoLayoutRetorno=Arquivo de Layout
prt_PinPad_tituloForm=Pinpad
prt_ConvenioCobranca_tituloForm=Conv\u00EAnio de Cobran\u00E7a
prt_PixWebhook_tituloForm=Cadastro Webhook do Pix
prt_ConvenioCobranca_codigo=C\u00F3digo\:
prt_ConvenioCobranca_label_codigo=C\u00F3digo
prt_ConvenioCobranca_descricao=* Descri\u00E7\u00E3o\:
prt_ConvenioCobranca_label_descricao=Descri\u00E7\u00E3o
prt_ConvenioCobranca_empresa=* Empresa\:
prt_ConvenioCobranca_label_empresa=Empresa
prt_ConvenioCobranca_tipoConvenio=Tipo Conv\u00EAnio\:
prt_ConvenioCobranca_situacao=Situa\u00E7\u00E3o\:
prt_ConvenioCobranca_label_tipoConvenio=Tipo
prt_ConvenioCobranca_multa=* Multa\:
prt_ConvenioCobranca_juros=* Juros\:
prt_ConvenioCobranca_extensaoArquivoRemessa=* Extens\u00E3o do Arquivo de Remessa\:
prt_ConvenioCobranca_extensaoArquivoRetorno=* Extens\u00E3o do Arquivo de Retorno\:
prt_ConvenioCobranca_diretorioGravaRemessa=* Diret\u00F3rio Grava Remessa\:
prt_ConvenioCobranca_diretorioLerRetorno=* Diret\u00F3rio Ler Retorno\:
prt_ConvenioCobranca_mensagem=* Mensagem\:
prt_ConvenioCobranca_sequencialDoArquivo=* Sequencial Do Arquivo\:
prt_ConvenioCobranca_sequencialDoItem=* Sequencial dos itens de remessa\:
prt_ConvenioCobranca_sequencialUnico=* Utilizar sequencial \u00FAnico\:
prt_ConvenioCobranca_utilizarMultaJurosSistemaBoletoItau=Enviar Multa e Juros na Remessa do Boleto Ita\u00FA\:
prt_ConvenioCobranca_sequencialDoArquivoCancelamento=* Sequencial Do Arquivo Cancelamento\:
prt_ConvenioCobranca_label_banco=Banco
prt_ConvenioCobranca_contaEmpresa=*Conta Corrente\:
prt_ConvenioCobranca_numeroContrato=Num. Contrato/Num. Estabelecimento\:
prt_ConvenioCobranca_numeroLogico=* Num. L\u00F3gico\:
prt_ConvenioCobranca_carteira=* Carteira\:
prt_ConvenioCobranca_carteiraBoleto=* Carteira para o Boleto\:
prt_ConvenioCobranca_codigoTransmissao=C\u00F3digo de Transmiss\u00E3o\:
prt_ConvenioCobranca_variacao=* Varia\u00E7\u00E3o\:
prt_ConvenioCobranca_nrDiasProtesto=Nr Dias para Protesto:
prt_ConvenioCobranca_nrDiasBaixaAutomatica=Nr Dias para Baixa Autom\u00E1tica:
prt_ConvenioCobranca_tipoRemessa=* Tipo de Remessa\:
prt_ConvenioCobranca_instrucoesBoleto=Instru\u00E7\u00F5es do Boleto\:
prt_ConvenioCobranca_retornaTarifaSeparadaValorPago=Tarifa \u00E9 retornada deduzida do valor pago\:
prt_ConvenioCobranca_sitesBoleto=Sites no Boleto\:
prt_ConvenioCobranca_hostSFTP=Host SFTP\:
prt_ConvenioCobranca_portSFTP=Porta SFTP\:
prt_ConvenioCobranca_userSFTP=Usu\u00E1rio SFTP\:
prt_ConvenioCobranca_pwdSFTP=Senha SFTP\:
prt_ConvenioCobranca_TivitOUTFTP=Diret\u00F3rio OUT\:
prt_ConvenioCobranca_TivitINFTP=Diret\u00F3rio IN\:
prt_ConvenioCobranca_SFTPNow=Enviar remessa ao gerar\:
prt_ConvenioCobranca_diretorioLocalTIVIT=Diret\u00F3rio Local\:
prt_ConvenioCobranca_diretorioLocalUploadTIVIT=Diret\u00F3rio Local Upload\:
prt_ConvenioCobranca_diretorioLocalDownloadTIVIT=Diret\u00F3rio Local Download\:
prt_ConvenioCobranca_chaveGETNET=Chave criptografia GETNET\:
prt_ConvenioCobranca_chaveBIN=Chave criptografia BIN\:
prt_ConvenioCobranca_nossaChave=Nossa chave\:
prt_ConvenioCobranca_nossaSenha=Nossa senha\:
prt_ConvenioCobranca_identificadorClienteEmpresa=Identificador Cliente Empresa\:
prt_ConvenioCobranca_autorizarDebitoCliente=Autorizar D\u00E9bito Cliente no Banco\:
prt_ConvenioCobranca_CancelamentoGETNETOUT=Diret\u00F3rio GETNET Cancelamento OUT\:
prt_ConvenioCobranca_CancelamentoGETNETIN=Diret\u00F3rio GETNET Cancelamento IN\:
prt_ConvenioCobranca_ChaveAPI=Chave privada\:
prt_ConvenioCobranca_MerchantId=MerchantId\:
prt_ConvenioCobranca_MerchantKey=MerchantKey\:
prt_ConvenioDescontoConfiguracao_tituloForm=Configura\u00E7\u00E3o do Conv\u00EAnio de Desconto
prt_ConvenioDescontoConfiguracao_codigo=C\u00F3digo\:
prt_ConvenioDescontoConfiguracao_duracao=Dura\u00E7\u00E3o do Plano\:
prt_ConvenioDescontoConfiguracao_valorDesconto=Valor de Desconto\:
prt_ConvenioDescontoConfiguracao_porcentagemDesconto=Porcentagem de Desconto\:
prt_ConvenioDescontoConfiguracao_tipoDesconto=Tipo de Desconto\:
prt_ConvenioDescontoConfiguracao_convenioDesconto=ConvenioDesconto\:
prt_ConvenioDesconto_tituloForm=Conv\u00EAnio Desconto
prt_ConvenioDesconto_codigo=C\u00F3digo\:
prt_ConvenioDesconto_empresa=Empresa\:
prt_ConvenioDesconto_label_codigo=C\u00F3digo
prt_ConvenioDesconto_descricao=Descri\u00E7\u00E3o\:
prt_ConvenioDesconto_label_descricao=Descri\u00E7\u00E3o
prt_ConvenioDesconto_dataAssinatura=Data de Assinatura\:
prt_ConvenioDesconto_dataInicioVigencia=Data In\u00EDcio de Vig\u00EAncia\:
prt_ConvenioDesconto_label_dataInicioVigencia=In\u00EDcio
prt_ConvenioDesconto_dataFinalVigencia=Data Final de Vig\u00EAncia\:
prt_ConvenioDesconto_label_dataFinalVigencia=Fim
prt_ConvenioDesconto_descontoParcela=Desconto nas Parcela\:
prt_ConvenioDesconto_responsavelAutorizacao=Respons\u00E1vel Autoriza\u00E7\u00E3o\:
prt_ConvenioDesconto_label_responsavelAutorizacao=Respons\u00E1vel
prt_ConvenioDesconto_dataAutorizacao=Data Autoriza\u00E7\u00E3o\:
prt_ConvenioDesconto_isentarMatricula=Isentar Matr\u00EDcula\:
prt_ConvenioDesconto_isentarRematricula=Isentar Rematr\u00EDcula\:
prt_MatriculaAlunoHorarioTurma_tituloRel=Relat\u00F3rio Lista de Chamada
prt_MatriculaAlunoHorarioTurma_empresa=Empresa
prt_MatriculaAlunoHorarioTurma_codigo=C\u00F3digo
prt_MatriculaAlunoHorarioTurma_contrato=Contrato
prt_MatriculaAlunoHorarioTurma_pessoa=Colaborador
prt_MatriculaAlunoHorarioTurma_modalidade=Modalidade
prt_MatriculaAlunoHorarioTurma_turma=Turma
prt_MatriculaAlunoHorarioTurma_quebrarPagina=Quebrar p\u00E1gina
prt_repasse_matricula=Matr\u00EDcula
prt_repasse_nomeAluno=Nome do Aluno
prt_repasse_plano=Plano
prt_repasse_pagamento=Cod. Pagto
prt_repasse_valorPago=Valor Pago
prt_repasse_dataCompensacao=Data Compensa\u00E7\u00E3o
prt_repasse_totalDescontar=Total a descontar
prt_repasse_valorCompensado=Valor Compensado
prt_repasse_valorRepasse=Valor Repasse
prt_repasse_totalValorPago=Total Valor Pago\:
prt_repasse_totalDescontado=Total Descontado\:
prt_repasse_totalCompensado=Total Compensado\:
prt_repasse_totalRepasse=Total Repasse\:
prt_MatriculaAlunoHorarioTurma_horarioTurma=Hor\u00E1rio Turma
prt_MatriculaAlunoHorarioTurma_dataInicioMatricula=Per\u00EDodo
prt_MatriculaAlunoHorarioTurma_dataFimMatricula=Data Fim Matr\u00EDcula
prt_MatriculaAlunoHorarioTurma_dataFreguencia=Data Frequ\u00EAncia
prt_CaixaPorOperador_tituloRel=Relat\u00F3rio Fechamento de Caixa por Operador
prt_CaixaPorOperador_periodoPesquisa=Faturamento Recebido\:
prt_CaixaPorOperador_periodoFaturamentoProduto=Faturamento Produto\:
prt_CaixaPorOperador_ate=at\u00E9
prt_CaixaPorOperador_nomeOperador=Nome do Operador\:
prt_CaixaPorOperador_tipoComprador=Tipo de Comprador\:
prt_CaixaPorOperador_faixaHorario=Faixa de Hor\u00E1rio\:
prt_CaixaPorOperador_quem=Resp. Recebimento
prt_CaixaPorOperador_quemLancou=Resp. Lan\u00E7amento
prt_CaixaPorOperador_consultor=Consultor Respons\u00E1vel
prt_CaixaPorOperador_situacaoPagamento=Situa\u00E7\u00E3o do Pagamento
prt_CaixaPorOperador_situacaoPagamentoDesbloqueado=Desbloqueados
prt_CaixaPorOperador_situacaoPagamentoCancelado=Cancelados
prt_CaixaPorOperador_situacaoPagamentoPagoBanco=Pagos em Bancos
prt_CaixaPorOperador_mostrarRecebimento=Mostrar Observa\u00E7\u00F5es do Recebimento
prt_CaixaPorOperador_mostrarNumeroContrato=Mostrar N\u00FAmero do Contrato
prt_CaixaPorOperador_ordenacao=Ordena\u00E7\u00E3o\:
prt_CaixaPorOperador_tipoVisualizacao=Tipo de Visualiza\u00E7\u00E3o
prt_CaixaPorOperador_nomeColaborador=Nome do Colaborador
prt_Relatorio_equivalentes=Relat\u00F3rios Equivalentes
prt_ReceitaPorPeriodoSintetico_tituloRel=Relat\u00F3rio Receita Por Per\u00EDodo Sint\u00E9tico
prt_ReceitaPorPeriodoSintetico_periodoPesquisa=Per\u00EDodo de Pesquisa
prt_ReceitaPorPeriodoSintetico_faturamentoRecebido=Faturamento Recebido
prt_ReceitaPorPeriodoSintetico_formaPagamento=Forma de Pagamento
prt_ReceitaPorPeriodoSintetico_visao=Vis\u00E3o
prt_ReceitaPorPeriodoSintetico_ate=At\u00E9
prt_FrequenciaOcupacaoTurma_TituloForm=Consulta de Frequ\u00EAncia e Ocupa\u00E7\u00E3o de Turmas
prt_DescontoOcupacaoTurma_TituloForm=Desconto por Ocupa\u00E7\u00E3o na Turma
prt_ConsultarTurma_TituloForm=Consulta de Turma
prt_ConsultarFrequenciaTurma_TituloForm=Relat\u00F3rio de Frequ\u00EAncia de Turma
prt_ConsultarTurma_empresa=* EMPRESA\:
prt_ConsultarTurma_atividade=Atividade
prt_ConsultarTurma_modalidade=MODALIDADE\:
prt_ConsultarTurma_professor=PROFESSOR\:
prt_ConsultarTurma_nivel=N\u00CDVEL\:
prt_ConsultarTurma_periodo=Per\u00EDodo\:
prt_ConsultarTurma_ate=at\u00E9
prt_ConsultarTurma_semana=Semana\:
prt_ConsultarTurma_consultar=Consultar M\u00EAs\:
prt_ConsultarTurma_exibirReposicoes=EXIBIR REPOSI\u00C7\u00D5ES\:
prt_ConsultarTurma_xAlunosDesmarcaram=Alunos que desmarcaram para Repor em outro dia
prt_ConsultarTurma_xAlunosMarcaram=Alunos que agendaram uma Reposi\u00E7\u00E3o para este dia
prt_ConsultarTurma_turma=TURMA\:
prt_ConsultarTurma_diaSemana=DIAS DA SEMANA\:
prt_ConsultarTurma_horarios=HOR\u00C1RIOS\:
prt_ConsultarTurma_ambiente=AMBIENTE\:
prt_ConsultarTurma_tituloProfessor=PROFESSOR
prt_ConsultarTurma_tituloTurma=TURMA
prt_ConsultarTurma_tituloDiaSemana=DIAS DA SEMANA
prt_ConsultarTurma_tituloHorarios=HOR\u00C1RIOS
prt_ConsultarTurma_tituloAmbiente=AMBIENTE
prt_ConsultarTurma_tituloInicio=In\u00EDcio
prt_ConsultarTurma_tituloFim=Fim
prt_ConsultarTurma_tituloVagas=Vagas
prt_ConsultarTurma_tituloOcupadas=Ocupadas
prt_ConsultarTurma_tituloTxOcupacao=Tx.Ocupa\u00E7\u00E3o
prt_ConsultarTurma_tituloPrevisto=Presenc Prev.
prt_ConsultarTurma_tituloPresencas=Presenc.
prt_ConsultarTurma_tituloFreqMedia=Freq.M\u00E9dia
prt_ConsultarTurma_tituloListaChamada=Lista de Chamada
prt_ConsultarTurma_mapaTurma=Mapa de Turmas
prt_ConsultarTurma_DataReferencia=DATA REFER\u00CANCIA\:
prt_PeriodoAcessoCliente_tituloForm=Per\u00EDodo Acesso
prt_TipoAcessoCliente_tituloForm=Per\u00EDodo Acesso
prt_PeriodoAcessoCliente_codigo=C\u00F3digo
prt_PeriodoAcessoCliente_cliente=Cliente
prt_PeriodoAcessoCliente_contrato=Contrato
prt_PeriodoAcessoCliente_dataInicioAcesso=Data In\u00EDcio Acesso
prt_PeriodoAcessoCliente_dataFinalAcesso=Data Final Acesso
prt_PeriodoAcessoCliente_tipoAcesso=Tipo Acesso
prt_PeriodoAcessoCliente_legenda=Legenda
prt_Servico_CE_codigo=C\u00F3digo
prt_Servico_CE_descricao=Descri\u00E7\u00E3o
prt_Servico_CE_valor=Valor
prt_ProdutoLocacao_CE_codigo=C\u00F3digo
prt_ProdutoLocacao_CE_nome=Nome
prt_ProdutoLocacao_CE_valor=Valor
prt_ProdutoLocacao_CE_tipo=Tipo
prt_ProdutoLocacao_CE_rastreado=Rastreado
prt_TipoAmbiente_CE_codigo=C\u00F3digo
prt_TipoAmbiente_CE=Tipo Ambiente
prt_TipoAmbiente_CE_Descricao=Descri\u00E7\u00E3o
prt_TipoAmbiente_CE_Reservas=Reservas/dia(M\u00E1ximo)
prt_TipoAmbiente_CE_Adicional=Adicional
prt_TipoAmbiente_CE_HoraInicial=Inicial
prt_TipoAmbiente_CE_HoraFinal=Final
prt_AcessoCliente_UltimosAcessos=\u00DAltimos Acessos
prt_AcessoCliente_TipoAcessoAutorizado=Tipo de Acesso Autorizado
prt_AcessoCliente_codigo=C\u00F3digo
prt_AcessoCliente_sentido=Sentido
prt_AcessoCliente_situacao=Situa\u00E7\u00E3o
prt_AcessoCliente_localAcesso=Local Acesso
prt_AcessoCliente_coletor=Coletor
prt_AcessoCliente_meioIdentificacaoEntrada=Meio Identifica\u00E7\u00E3o Entrada
prt_AcessoColaborador_meioIdentificacaoEntrada=Meio Identifica\u00E7\u00E3o Entrada
prt_AcessoCliente_usuarioLiberou=Usu\u00E1rio Liberou Acesso
prt_AcessoCliente_bloqueio=Bloqueio
prt_AcessoCliente_dataHoraEntrada=Data/Hora Entrada
prt_AcessoCliente_dataHoraSaida=Data/Hora Sa\u00EDda
prt_valorDefaultColetor=Setar valores Default para o Coletor
prt_HistoricoComprasCliente_contrato=N\u00B0 Contrato
prt_HistoricoComprasCliente_descricao=Descri\u00E7\u00E3o
prt_HistoricoComprasCliente_quantidade=Quantidade
prt_HistoricoComprasCliente_preco=Pre\u00E7o
prt_HistoricoComprasCliente_unitario=Valor Unit.
prt_HistoricoComprasCliente_totalFinal=Valor Total
prt_HistoricoComprasCliente_desconto=Desconto
prt_HistoricoComprasCliente_situacao=Situa\u00E7\u00E3o
prt_HistoricoComprasCliente_dataLancamento=Data Lan\u00E7amento
prt_HistoricoComprasCliente_recibo=Recibo
prt_HistoricoComprasCliente_nrRecibo=N\u00BA Recibo
prt_HistoricoComprasCliente_nomePagador=Nome do Pagador
prt_HistoricoComprasCliente_formaPagamento=Forma Pagamento
prt_HistoricoComprasCliente_tipo_formaPagamento=Tipo Forma Pagamento
prt_HistoricoComprasCliente_valor=Valor
prt_Hist\u00F3ricoCompras=Hist\u00F3rico de Compras
prt_HistoricoCobranca=Hist\u00F3rico de Cobran\u00E7a
prt_HistoricoComprasCliente_codigoProduto=N\u00BA Produto
prt_HistoricoComprasCliente_codigo=C\u00F3digo
prt_HistoricoParcelaCliente_codigo=N\u00B0 Contrato
prt_HistoricoParcelaCliente_descricao=Descri\u00E7\u00E3o
prt_HistoricoParcelaCliente_modalidade=Modalidade
prt_HistoricoParcelaCliente_situacao=Situa\u00E7\u00E3o
prt_HistoricoParcelaCliente_Opcoes=Cupom
prt_HistoricoParcelaCliente_DataLancada=Lan\u00E7amento
prt_HistoricoParcelaCliente_DataVencimento=Vencimento
prt_HistoricoParcelaCliente_valor=Valor
prt_HistoricoParcelaCliente_nomeAluno=Nome do Aluno
prt_HistoricoParcelaCliente_nomeDoComprador=Nome do Comprador
prt_HistoricoParcelaCliente_codigoProd=N\u00BA Produto
prt_HistoricoParcelasGeradas=Hist\u00F3rico de Parcelas Geradas
prt_HistoricoPagamentosEfetuados=Hist\u00F3rico de Pagamentos Efetuados
prt_planos_personal=Planos Personal
prt_plano=Plano
prt_dataInicio=Data In\u00EDcio
prt_dataFim=Data Fim
prt_registro=Data de Registro
prt_empresa=Empresa
prt_personal=Personal
prt_Observacao=Observa\u00E7\u00E3o
prt_ContratoOperacao_tituloForm=Contrato Opera\u00E7\u00E3o
prt_ContratoOperacao_codigo=C\u00F3digo
prt_ContratoOperacao_contrato=Contrato
prt_ContratoOperacao_tipoOperacao=Tipo Opera\u00E7\u00E3o
prt_ContratoOperacao_operacaoPaga=Opera\u00E7\u00E3o Paga
prt_ContratoOperacao_dataOperacao=Data Opera\u00E7\u00E3o
prt_ContratoOperacao_dataInicioEfetivacaoOperacao=Data In\u00EDcio Efetiva\u00E7\u00E3o Opera\u00E7\u00E3o
prt_ContratoOperacao_dataFimEfetivacaoOperacao=Data Fim Efetiva\u00E7\u00E3o Opera\u00E7\u00E3o
prt_ContratoOperacao_responsavel=Respons\u00E1vel
prt_ContratoOperacao_observacao=Observa\u00E7\u00E3o
prt_ContratoOperacao_descricaoCalculo=Descri\u00E7\u00E3o C\u00E1lculo
prt_Contrato_responsavelPeloContrato=Resp. Lan\u00E7amento Contrato
prt_CancelamentoContrato_dataCancelamento=Data Cancelamento
prt_ControleLog_tituloForm=Controle de Log
prt_ControleLog_codigo=C\u00F3digo
prt_ControleLog_descricaoLog=Descri\u00E7\u00E3o do Log
prt_ControleLog_data=Data
prt_ControleLog_entidade=Entidade
prt_ControleLog_responsavel=Respons\u00E1vel
prt_ControleLog_campo=Campo
prt_ControleLog_valorAnterior=Valor Anterior
prt_ControleLog_valorPosterior=Valor Posterior
prt_Log_tituloForm=Controle de Log
prt_Log_label_codigo=C\u00F3digo
prt_Log_entidade=Entidade
prt_Log_label_entidade=Entidade
prt_Log_dataAlteracao=Data Altera\u00E7\u00E3o
prt_Log_dataHora=Data e Hora
prt_Log_label_dataHora=Data e Hora
prt_Log_valorCampoAlterado=Valor Alterado
prt_Log_valorCampoAnterior=Valor Anterior
prt_Log_nomeCampo=Campo
prt_Log_label_nomeCampo=Campo
prt_Log_responsavel=Respons\u00E1vel
prt_Log_label_responsavel=Respons\u00E1vel
prt_Log_chavePrimaria=Chave Prim\u00E1ria
prt_Log_chavePrimariaEntidadeSubordinada=Chave Prim\u00E1ria Subordinada
prt_Log_operacao=Opera\u00E7\u00E3o
prt_Log_label_operacao=Opera\u00E7\u00E3o
prt_Log_descricao=Descri\u00E7\u00E3o
prt_DefinirSenha_titulo_cli=Definir Nova Senha
prt_NovaSenha_cli=Nova Senha
prt_ConfirmarSenha_cli=Confirmar Senha
prt_AlterarSenhaSenha_titulo_cli=Alterar Senha
prt_SenhaAtual_cli=Senha Atual
prt_NomeUsuario_cli=Nome Usu\u00E1rio
prt_Username_cli=Username
prt_VendaAvulsa_tituloForm=Venda Avulsa
prt_VendaAvulsa_codigo=C\u00F3digo
prt_VendaAvulsa_tipoComprador=Tipo Comprador
prt_VendaAvulsa_nomeComprador=Nome Comprador
prt_VendaAvulsa_cliente=Cliente
prt_VendaAvulsa_consultarCliente=Consultar Cliente
prt_VendaAvulsa_colaborador=Colaborador
prt_VendaAvulsa_consultarColaborador=Consultar Colaborador
prt_VendaAvulsa_valorTotal=Valor Total
prt_VendaAvulsa_empresa=* Empresa
prt_VendaAvulsa_autorizar=Autorizar Desconto do Produto
prt_VendaAvulsa_editarData=Autorizar Edi\u00E7\u00E3o da Data
prt_VendaAvulsa_obsDesconto=*Obs.\: Autoriza\u00E7\u00E3o (Desconto em produto de Venda Avulsa) necess\u00E1ria.
prt_VendaAvulsa_obsData=*Obs.\: Autoriza\u00E7\u00E3o (Alterar data de Venda Avulsa) necess\u00E1ria.
prt_VendaAvulsa_parcelamento=Parcelamento
prt_AulaAvulsa_empresa=*Empresa\:
prt_AulaAvulsa_nomeComprador=*Cliente\:
prt_AulaAvulsa_consultarCliente=Consultar Cliente
prt_AulaAvulsa_consultarColaborador=Consultar Colaborador
prt_AulaAvulsa_modalidade=*Modalidade\:
prt_AulaAvulsa_produto=*Produto\:
prt_AulaAvulsa_datainicio=*Data In\u00EDcio\:
prt_AulaAvulsa_desconto=Desconto\:
prt_AulaAvulsa_consultarModalidade=Consultar Modalidade
prt_AulaAvulsa_consultarModalidade_modalidade=Modalidade\:
prt_AulaAvulsa_autorizar=Autorizar Desconto do Produto\:
prt_AulaAvulsa_obsDesconto=*Obs.\: Autoriza\u00E7\u00E3o (Desconto em produto de Aula Avulsa) necess\u00E1ria.
prt_VendaDiaria_obsDesconto=*Obs.\: Autoriza\u00E7\u00E3o (Desconto em produto de Venda Di\u00E1ria) necess\u00E1ria.
prt_ItemAulaAvulsa_tituloForm=Item Aula Avulsa
prt_ItemAulaAvulsa_codigoAula=C\u00F3digo
prt_ItemAulaAvulsa_nomeAula=Nome Aula
prt_ItemAulaAvulsa_consultarTurma=Consultar Turma
prt_ItemAulaAvulsa_quantidade=Quantidade
prt_ItemAulaAvulsa_precoAula=Pre\u00E7o Aula
prt_ItemAulaAvulsa_descontoAula=Desconto
prt_ItemAulaAvulsa_valorParcial=Valor Parcial
prt_ItemAulaAvulsa_tabelaDesconto=Tabela Desconto
prt_ItemVendaAvulsa_tituloForm=Item Venda Avulsa
prt_ItemVendaAvulsa_DescontoManual=Desconto Manual
prt_ItemVendaAvulsa_codigo=C\u00F3digo
prt_ItemVendaAvulsa_vendaAvulsa=Venda Avulsa
prt_ItemVendaAvulsa_nomeProduto=Nome Produto
prt_ItemVendaAvulsa_codigoProduto=C\u00F3digo
prt_ItemVendaAvulsa_consultarProduto=Consultar Produto
prt_ItemVendaAvulsa_quantidade=Quantidade
prt_ItemVendaAvulsa_precoProduto=Pre\u00E7o Produto
prt_ItemVendaAvulsa_valorParcial=Valor Parcial
prt_ItemVendaAvulsa_descontoProduto=Desconto
prt_ItemVendaAvulsa_tabelaDesconto=Tabela Desconto
prt_ItemVendaAvulsa_pacote=Pacote
prt_JustificativaOperacao_tituloForm=Justificativa Opera\u00E7\u00E3o
prt_JustificativaOperacao_codigo=C\u00F3digo\:
prt_JustificativaOperacao_label_codigo=C\u00F3digo
prt_JustificativaOperacao_empresa=Empresa\:
prt_JustificativaOperacao_label_empresa=Empresa
prt_JustificativaOperacao_tipoOperacao=Tipo Opera\u00E7\u00E3o\:
prt_JustificativaOperacao_ativo=Ativo\:
prt_JustificativaOperacao_apresentarTodasEmpresas=Apresentar para todas as Empresas\:
prt_JustificativaOperacao_isentarMultaCancelamento=Isentar a multa no cancelamento\:
prt_JustificativaOperacao_naoCobrarParcelasAtrasadasCancelamento=N\u00E3o cobrar parcelas atrasadas no cancelamento\:
prt_JustificativaOperacao_necessarioAnexarComprovante=Necess\u00E1rio anexar um comprovante\:
prt_JustificativaOperacao_label_tipoOperacao=Tipo Opera\u00E7\u00E3o
prt_JustificativaOperacao_descricao=Descri\u00E7\u00E3o\:
prt_JustificativaOperacao_label_descricao=Descri\u00E7\u00E3o
prt_JustificativaOperacao_consultarJustificativa=Tipo da Justificativa Opera\u00E7\u00E3o
prt_Saldo_Credito_tituloForm=Relat\u00F3rio de Saldo de Cr\u00E9dito
prt_Saldo_Credito_empresa=Empresa
prt_Saldo_Credito_ativo=Ativo
prt_Saldo_Credito_inativo=Inativo
prt_Saldo_Credito_visitante=Visitante
prt_ClienteRel_tituloRel=Relat\u00F3rio de Clientes
prt_ClienteRel_periodoPesquisa=Per\u00EDodo de Pesquisa
prt_ClienteRel_ate=At\u00E9
prt_ClienteRel_situacao=Situa\u00E7\u00E3o do Cliente
prt_ClienteRel_empresa=Empresa
prt_ClienteRel_totalAtivos=Total Ativos
prt_ClienteRel_totalInativos=Total Inativos
prt_ClienteRel_totalCancelados=Total Cancelados
prt_ClienteRel_totalVisitantes=Total Visitantes
prt_ClienteRel_totalBolsistas=Total Bolsistas
prt_ClientePorDuracao_tituloForm=Relat\u00F3rio Contratos por Dura\u00E7\u00E3o
prt_ClientePorDuracao_periodoPesquisa=Dia
prt_ClientePorDuracao_normal=Normal
prt_ClientePorDuracao_atestado=Atestado
prt_ClientePorDuracao_vencido=Vencido
prt_ClientePorDuracao_avencer=A vencer
prt_ClientePorDuracao_carencia=F\u00E9rias
prt_ClientePorDuracao_trancado=Trancado
prt_ClientePorDuracao_semBolsa=Sem Bolsa
prt_FrequenciaPorTurma_tituloForm=Frequ\u00EAncia por Turma
prt_FrequenciaPorTurma_empresa=Empresa
prt_FrequenciaPorTurma_turmas=Turmas
prt_FrequenciaPorTurma_inicio=In\u00EDcio
prt_FrequenciaPorTurma_fim=Fim
prt_ClientePorAniversario_tituloForm=Relat\u00F3rio de Aniversariantes
prt_ClientePorAniversario_empresa=Empresa
prt_ClientePorAniversario_periodo=Per\u00EDodo
prt_ClientePorAniversario_ate=at\u00E9
prt_ClientePorAniversario_ativo=Ativo
prt_ClientePorAniversario_dataAniversario=Data Anivers\u00E1rio
prt_ClientePorAniversario_matricula=Matr\u00EDcula
prt_ClientePorAniversario_nome=Nome
prt_ClientePorAniversario_plano=Plano\:
prt_ClientePorAniversario_planoRe=Plano
prt_ClientePorAniversario_situacao=Situa\u00E7\u00E3o\:
prt_ClientePorAniversario_situacaoRe=Situa\u00E7\u00E3o
prt_ClientePorAniversario_inativo=Inativo
prt_ClientePorAniversario_vejaFiltrosUtilizados=Veja os Filtros Utilizados
prt_ClientePorAniversario_visitante=Visitante
prt_ClientePorAniversario_trancado=Trancado
prt_consultarTurma_tituloForm=Consultar Turma\:
prt_cancelamentoTransferenciaCliente_consultarCliente=Consultar Cliente
prt_cancelamentoTransferenciaCliente_nomeCliente=Cliente
prt_cancelamentoTransferenciaCliente_consultarContrato=Consultar Contrato
prt_cancelamentoTransferenciaCliente_consultarContrato_plano=Plano
prt_cancelamentoTransferenciaCliente_consultarContrato_dataInicio=Data In\u00EDcio
prt_cancelamentoTransferenciaCliente_consultarContrato_dataTermino=Data T\u00E9rmino
prt_cancelamentoTransferenciaCliente_consultarContrato_numeroContrato=N\u00BA Contrato
prt_cancelamentoTransferenciaCliente_nomeContrato=Contrato
prt_trancamentoContrato_tituloForm=Trancamento
prt_retornoTrancamentoContrato_tituloForm=Retorno de Trancamento
prt_cancelamentoTrancamento_consultarNomeCliente=Nome do Cliente\:
prt_cancelamentoTrancamento_consultarContrato_numeroContrato=N\u00BA Contrato
prt_cancelamentoTrancamento_consultarContrato_plano=Plano
prt_cancelamentoTrancamento_consultarContrato_dataInicio=Data In\u00EDcio
prt_trancamentoContrato_consultarContrato_dataTermino=Data T\u00E9rmino
prt_trancamentoContrato_dataDeTrancamento=Data de Trancamento
prt_trancamentoContrato_dataDeRetorno=Data de Retorno
prt_trancamentoContrato_tipoJustificativa=Justificativa\:
prt_trancamentoContrato_valorTrancamento=Valor Trancamento\:
prt_trancamentoContrato_produtoTrancamento=Produto Trancamento
prt_trancamentoContrato_consultarContrato_valorBaseContrato=Valor
prt_afastamentoContrato_tituloForm=Afastamento
prt_afastamentoContrato_Atestado_ContratoVencido=* O Contrato est\u00E1 vencido! Ent\u00E3o ser\u00E1  adicionado apenas o per\u00EDodo de acesso referente aos dias que o(a) aluno(a) tem direito.
prt_FreePass_consulta_cliente_titulo=Consultar Cliente
prt_FreePass_consulta_cliente_codigo=Consultar Cliente
prt_FreePass_consulta_cliente_pessoa=Cliente
prt_FreePass_consulta_cliente_situacao=Situa\u00E7\u00E3o
prt_FreePass_consulta_cliente_matricula=Matricula
prt_FreePass_campo_empresa=*Empresa
prt_FreePass_consultarCliente=*Consultar Cliente
prt_FreePass_produto=Produto\:
prt_FreePass_cliente=Cliente\:
prt_FreePass_tituloForm=FreePass
prt_FreePassVO_tituloForm=FreePass
prt_LogControleUsabilidade_tituloForm=Cadastro de Log de Controle de Usabilidade
prt_LogControleUsabilidade_codigo=C\u00F3digo
prt_LogControleUsabilidade_entidade=Entidade
prt_LogControleUsabilidade_maquina=M\u00E1quina
prt_LogControleUsabilidade_acao=A\u00E7\u00E3o
prt_LogControleUsabilidade_dataRegistro=Data de Registro
prt_LogControleUsabilidade_usuario=Usu\u00E1rio
prt_LogControleUsabilidade_empresa=Empresa
prt_SituacaoContratoSinteticoDW_consulta_Empresa_titulo=Consultar Empresa
prt_SituacaoContratoSinteticoDW_consulta_Empresa_codigo=C\u00F3digo
prt_SituacaoContratoSinteticoDW_consulta_Empresa_nome=Nome
prt_SituacaoContratoSinteticoDW_consulta_Empresa_razao_social=Raz\u00E3o Social
prt_SituacaoContratoSinteticoDW_consulta_Empresa_inscricao_estadual=Inscri\u00E7\u00E3o Estadual
prt_SituacaoContratoSinteticoDW_consulta_Plano_titulo=Consultar Plano
prt_SituacaoContratoSinteticoDW_consulta_Plano_codigo=C\u00F3digo
prt_SituacaoContratoSinteticoDW_consulta_Plano_descricao=Descri\u00E7\u00E3o
prt_SituacaoContratoSinteticoDW_consulta_Consultor_titulo=Consultar
prt_SituacaoContratoSinteticoDW_consulta_Orientador_titulo=Consultar Orientador
prt_SituacaoContratoSinteticoDW_consulta_PersonalTraining_titulo=Consultar Personal Training
prt_SituacaoContratoSinteticoDW_consulta_Colaborador_codigo=C\u00F3digo
prt_SituacaoContratoSinteticoDW_consulta_Colaborador_nome=Nome
prt_SituacaoContratoSinteticoDW_tituloGrafico=Relat\u00F3rio Gr\u00E1fico de Clientes
prt_SituacaoContratoSinteticoDW_qtdeClientes=Quantidade Clientes
prt_SituacaoContratoSinteticoDW_situacaoCliente=Situa\u00E7\u00E3o Clientes
prt_SituacaoContratoSinteticoDW_graficoBarra=Gr\u00E1fico Modelo Barra
prt_SituacaoContratoSinteticoDW_graficoPizza=Gr\u00E1fico Modelo Pizza
prt_SituacaoRenovacaoSinteticoDW_tituloGrafico=Relat\u00F3rio Previs\u00E3o de Renova\u00E7\u00E3o
prt_SituacaoRenovacaoSinteticoDW_situacaoRenovacao=Situa\u00E7\u00E3o Renova\u00E7\u00F5es
prt_SituacaoRenovacaoSinteticoDW_qtdeRenovacao=Quantidade Renova\u00E7\u00F5es
prt_SituacaoRenovacaoSinteticoDW_consulta_Empresa_titulo=Consultar Empresa
prt_SituacaoRenovacaoSinteticoDW_consulta_Empresa_codigo=C\u00F3digo
prt_SituacaoRenovacaoSinteticoDW_consulta_Empresa_nome=Nome
prt_SituacaoRenovacaoSinteticoDW_consulta_Empresa_razao_social=Raz\u00E3o Social
prt_SituacaoRenovacaoSinteticoDW_consulta_Empresa_inscricao_estadual=Inscri\u00E7\u00E3o Estadual
prt_SituacaoRenovacaoSinteticoDW_consulta_Plano_titulo=Consultar Plano
prt_SituacaoRenovacaoSinteticoDW_consulta_Plano_codigo=C\u00F3digo
prt_SituacaoRenovacaoSinteticoDW_consulta_Plano_descricao=Descri\u00E7\u00E3o
prt_SituacaoRenovacaoSinteticoDW_consulta_Consultor_titulo=Consultar
prt_SituacaoRenovacaoSinteticoDW_consulta_Orientador_titulo=Consultar Orientador
prt_SituacaoRenovacaoSinteticoDW_consulta_PersonalTraining_titulo=Consultar Personal Training
prt_SituacaoRenovacaoSinteticoDW_consulta_Colaborador_codigo=C\u00F3digo
prt_SituacaoRenovacaoSinteticoDW_consulta_Colaborador_nome=Nome
prt_SituacaoRenovacaoSinteticoDW_graficoBarra=Gr\u00E1fico Modelo Barra
prt_SituacaoRenovacaoSinteticoDW_graficoPizza=Gr\u00E1fico Modelo Pizza
prt_SituacaoContratoAnaliticoDW_labelFiltros=Passe o Mouse Aqui para ver os Filtros Utilizados
prt_SituacaoContratoAnaliticoDW_imprimir=Imprimir
prt_SituacaoContratoAnaliticoDW_consultar=Consultar
prt_SituacaoContratoAnaliticoDW_tituloLista=Relat\u00F3rio Lista de Clientes
prt_SituacaoContratoAnaliticoDW_consulta_Empresa_titulo=Consultar Empresa
prt_SituacaoContratoAnaliticoDW_consulta_Empresa_codigo=C\u00F3digo
prt_SituacaoContratoAnaliticoDW_consulta_Empresa_nome=Nome
prt_SituacaoContratoAnaliticoDW_consulta_Empresa_razao_social=Raz\u00E3o Social
prt_SituacaoContratoAnaliticoDW_consulta_Empresa_inscricao_estadual=Inscri\u00E7\u00E3o Estadual
prt_SituacaoContratoAnaliticoDW_consulta_Plano_titulo=Consultar Plano
prt_SituacaoContratoAnaliticoDW_consulta_Plano_codigo=C\u00F3digo
prt_SituacaoContratoAnaliticoDW_consulta_Plano_descricao=Descri\u00E7\u00E3o
prt_SituacaoContratoAnaliticoDW_consulta_Consultor_titulo=Consultar
prt_SituacaoContratoAnaliticoDW_consulta_Orientador_titulo=Consultar Orientador
prt_SituacaoContratoAnaliticoDW_consulta_PersonalTraining_titulo=Consultar Personal Training
prt_SituacaoContratoAnaliticoDW_consulta_Colaborador_codigo=C\u00F3digo
prt_SituacaoContratoAnaliticoDW_consulta_Colaborador_nome=Nome
prt_SituacaoRenovacaoAnaliticoDW_consulta_Empresa_titulo=Consultar Empresa
prt_SituacaoRenovacaoAnaliticoDW_consulta_Empresa_codigo=C\u00F3digo
prt_SituacaoRenovacaoAnaliticoDW_consulta_Empresa_nome=Nome
prt_SituacaoRenovacaoAnaliticoDW_consulta_Empresa_razao_social=Raz\u00E3o Social
prt_SituacaoRenovacaoAnaliticoDW_consulta_Empresa_inscricao_estadual=Inscri\u00E7\u00E3o Estadual
prt_SituacaoRenovacaoAnaliticoDW_consulta_Plano_titulo=Consultar Plano
prt_SituacaoRenovacaoAnaliticoDW_consulta_Plano_codigo=C\u00F3digo
prt_SituacaoRenovacaoAnaliticoDW_consulta_Plano_descricao=Descri\u00E7\u00E3o
prt_SituacaoRenovacaoAnaliticoDW_consulta_Consultor_titulo=Consultar
prt_SituacaoRenovacaoAnaliticoDW_consulta_Orientador_titulo=Consultar Orientador
prt_SituacaoRenovacaoAnaliticoDW_consulta_PersonalTraining_titulo=Consultar Personal Training
prt_SituacaoRenovacaoAnaliticoDW_consulta_Colaborador_codigo=C\u00F3digo
prt_SituacaoRenovacaoAnaliticoDW_consulta_Colaborador_nome=Nome
prt_SituacaoRenovacaoAnaliticoDW_imprimir=Imprimir
prt_ConfiguracoesGeraisferias_tituloForm=Panel Atualiza\u00E7\u00E3o de F\u00E9rias
prt_ConfiguracoesGeraisFerias_ferias=N\u00BA Dias de F\u00E9rias
prt_ConfiguracoesGeraisFerias_nrFerias=N\u00BA F\u00E9rias
prt_ClienteMensagem_tituloForm=Mensagem e Avisos
prt_ClienteMensagem_bloquearAcesso=Bloquear Acesso Agora
prt_ClienteMensagem_dataBloqueio=Agendar Bloqueio
prt_ClienteMensagem_bloqueio=Agendamento Bloqueio de Acesso Cliente Catraca
prt_Aviso_tituloForm=Aviso
prt_AvisoExpiracaoSenha_tituloForm=Aviso
prt_RecuperacaoSenha_tituloForm=Recupera\u00E7\u00E3o de Senha
prt_RecuperacaoSenha_cpf=CPF\:
prt_RecuperacaoSenha_dataNasc=Data de Nasc.\:
prt_LocalAcesso_tituloForm=Local de Acesso
prt_LocalAcesso_codigo=C\u00F3digo\:
prt_LocalAcesso_label_codigo=C\u00F3digo
prt_LocalAcesso_descricao=Descri\u00E7\u00E3o\:
prt_LocalAcesso_label_descricao=Descri\u00E7\u00E3o
prt_LocalAcesso_nomeComputador=Concentrador\:
prt_LocalAcesso_empresa=Empresa\:
prt_LocalAcesso_label_empresa=Empresa
prt_LocalAcesso_tempoEntreAcessos=Tempo m\u00EDnimo entre um acesso e outro (Cliente)\:
prt_LocalAcesso_tempoEntreAcessosColaborador=Tempo m\u00EDnimo entre um acesso e outro (Colaborador)\:
prt_LocalAcesso_servidorImpressoes=Servidor de impress\u00F5es\:
prt_LocalAcesso_portaServidorImp=Porta servidor impress\u00F5es\:
prt_LocalAcesso_servidorFacialInner=Servidor Facial Inner\:
prt_LocalAcesso_portaServidorFacialInner=Porta Facial Inner\:
prt_LocalAcesso_pedirSenhaLib=Pedir senha de libera\u00E7\u00E3o para cada acesso\:
prt_LocalAcesso_utilizarModoOffline=Utilizar modo offline\:
prt_LocalAcesso_capacidadeLimite=Capacidade Limite de Alunos Simult\u00E2neos\:
prt_LocalAcesso_dataBaseOffline=\u00DAltima gera\u00E7\u00E3o dos dados para a Base Offline\:
prt_LocalAcesso_dataDownloadBase=\u00DAltimo download da Base Offline\:
prt_LocalAcesso_versaoAcesso=Vers\u00E3o Acesso
prt_LocalAcesso_categoriaLocalAcesso=Categoria do local de acesso
prt_LocalAcesso_restringirAcessoOutrasUnidades=Restringir acesso de alunos de outras unidades\:
prt_Coletor_tituloForm=Lista de Coletores Cadastrados
prt_Coletor_codigo=C\u00F3digo\:
prt_Coletor_nfc=NFC\:
prt_Coletor_descricao=Descri\u00E7\u00E3o\:
prt_Coletor_modelo=Protocolo\:
prt_Coletor_numSerie=N\u00FAmero de s\u00E9rie\:
prt_Coletor_tipoComunicacao=Tipo de comunica\u00E7\u00E3o\:
prt_Coletor_portaComunicacao=Porta de comunica\u00E7\u00E3o\:
prt_Coletor_tecladoGertec=Teclado Gertec\:
prt_Coletor_DispAlternativo=Disp. Alternativo\:
prt_Coletor_DigitosCartao=D\u00EDgitos Cart\u00E3o\:
prt_Coletor_InverterSinal=Inverter Sinal\:
prt_Coletor_portaLeitorSerial=Porta Leitor Serial\:
prt_Coletor_portaParalela=Porta Paralela\:
prt_Coletor_modoTransmissao=Modo de transmiss\u00E3o\:
prt_Coletor_velocTransmissao=Velocidade de transmiss\u00E3o\:
prt_Coletor_resolucaoDPI=Resolu\u00E7\u00E3o (DPI)\:
prt_Coletor_rele=Rel\u00EA\:
prt_Coletor_releEntrada=Rel\u00EA de entrada\:
prt_Coletor_tempoReleEntrada=Tempo de acionamento (entrada)\:
prt_Coletor_releSaida=Rel\u00EA de sa\u00EDda\:
prt_Coletor_tempoReleSaida=Tempo de acionamento (sa\u00EDda)\:
prt_Coletor_tempoRele=Tempo de acionamento\:
prt_Coletor_msgDisplay=Mensagem para display\:
prt_Coletor_ip=IP Coletor\:
prt_Coletor_ipServidor=IP Servidor:
prt_Coletor_mascaraSubrede=M\u00E1scara Rede:
prt_Coletor_ip_servidor=IP Servidor Integra F\u00E1cil:
prt_Coletor_porta=Porta\:
prt_Coletor_portaServidor=Porta Servidor\:
prt_Coletor_cartaoMaster=Cart\u00E3o Master
prt_Coletor_biometrico=Biometrico
prt_Coletor_sentidoAcesso=Sentido do acesso\:
prt_Coletor_biometriaNaCatraca=Usa biometria\:
prt_Coletor_aguardaGiro=Aguardar giro da catraca\:
prt_Coletor_usaFacial=Usa Facial\:
prt_Coletor_usaRtsp=Usa Rtsp\:
prt_Coletor_arquivoPrograma=Programa BAS\:
prt_Coletor_padraoCadastro=Padr\u00E3o para cadastro de impress\u00F5es\:
prt_Coletor_padraoColetor=Padr\u00E3o coletor\:
prt_Coletor_sensorEntrada=Sensor de entrada\:
prt_Coletor_sensorSaida=Sensor de sa\u00EDda\:
prt_Coletor_numeroTerminal=N\u00FAmero do terminal\:
prt_Coletor_CodigoNFC=C\u00F3digo do NFC\:
prt_Coletor_numeroTerminalAcionamento=N\u00FAmero do terminal para Acionar:
prt_ColetorVal_titulo=Lista de Valida\u00E7\u00F5es Cadastradas
prt_ColetorVal_Info=Defina as regras de valida\u00E7\u00E3o para que uma pessoa possa passar por esta catraca.
prt_ColetorVal_TipoVal=Tipo Valida\u00E7\u00E3o\:
prt_ColetorVal_TipoHor=Tipo Hor\u00E1rio\:
prt_FaturamentoSintetico_tituloForm=Relat\u00F3rio Faturamento por Per\u00EDodo
prt_Relatorio_Pix=Relat\u00F3rio de Transa\u00E7\u00F5es Pix
prt_FaturamentoRecebidoSintetico_tituloForm=Relat\u00F3rio Faturamento Recebido por Per\u00EDodo
prt_FaturamentoSintetico_empresa=Empresa\:
prt_FaturamentoSintetico_faturamento=Faturamento
prt_FaturamentoSintetico_periodoPesquisa=Per\u00EDodo de\:
prt_FaturamentoSintetico_agrupamento=Agrupar plano por\:
prt_FaturamentoSintetico_tituloFormPessoas=Faturamento Lista Movimenta\u00E7\u00E3o de Produto Por Pessoa
prt_RelatorioArmario_parcelasAtrasadas=Somente alunos com parcela atrasadas
prt_RelatorioArmario_tipoArmario=Tipo Armario
prt_RelatorioArmario_numeroArmario=N\u00FAmero do Arm\u00E1rio
prt_RelatorioArmario_contratoAsssinado=Contrato Assinado
prt_RelatorioArmario_tamanhoArmario=Tamanho Arm\u00E1rio
prt_RelatorioArmario_planoLocacao=Plano de Loca\u00E7\u00E3o
prt_RelatorioArmario_tituloForm=Relat\u00F3rio Arm\u00E1rios
prt_CompetenciaSintetico_tituloForm=Relat\u00F3rio Compet\u00EAncia Mensal
prt_CompetenciaSintetico_empresa=Empresa\:
prt_CompetenciaSintetico_periodoPesquisa=Per\u00EDodo de\:
prt_CompetenciaSintetico_agrupamento=Agrupar plano por\:
prt_CompetenciaSintetico_tituloFormPessoas=Compet\u00EAncia Lista Movimenta\u00E7\u00E3o de Produto Por Pessoa
prt_CompetenciaSintetico_tituloFormModalidade=Modalidade por Contrato
prt_de=De
prt_ate=at\u00E9
prt_as=\u00E0s
prt_limparCampo=Limpar Campo
prt_vizualizarEmail=Visualizar Email
prt_titlePosVenda=Defina aqui a quantidade de dias p\u00F3s venda em que dever\u00E1 ser considerado para o envio do mailing. Ser\u00E3o considerados os contratos ininterruptos, ou seja, se definido 30 dias, ent\u00E3o o sistema ir\u00E1 enviar o mailing somente para os alunos ATIVOS com contrato ininterrupto de 30 dias corridos. Se definido 200, o sistema enviar\u00E1 o mailing para os alunos que est\u00E3o h\u00E1 200 dias ativos ininterruptos, independente se fez renova\u00E7\u00E3o ou n\u00E3o. Vale ressaltar que se o aluno for uma rematr\u00EDcula, o filtro \u00E9 zerado contando novamente \u00E0 partir de 0 dias.
prt_Asterisco=*
prt_ParcelaEmAberto_tituloRel=Relat\u00F3rio Parcelas
prt_Optin_tituloRel=Relat\u00F3rio Opt-in
prt_ParcelaEmAberto_Empresa=Empresa
prt_ParcelaEmAberto_periodoNegativacao=Pesquisa por período de negativação
prt_ParcelaEmAberto_periodoPesquisa=Per\u00EDodo da Pesquisa
prt_ParcelaEmAberto_periodoVencimento=Pesquisa por data de vencimento
prt_ParcelaEmAberto_periodoFaturamento=Pesquisa por data de faturamento
prt_ParcelaEmAberto_periodoPagamento=Pesquisa por data de pagamento
prt_ParcelaEmAberto_situacaoPesquisa=Pesquisa Por Data de
prt_ParcelaEmAberto_parcelasCanceladas=Parcelas Canceladas
prt_ParcelaEmAberto_recorrencia=Recorr\u00EAncia
prt_ParcelaEmAberto_situacaoCliente=Situa\u00E7\u00E3o do Cliente
prt_ParcelaEmAberto_recorrenciasomente=Regime de recorr\u00EAncia
prt_ParcelaEmAberto_plano=Plano
prt_ParcelaEmAberto_tipoRelatorio=Tipo Relat\u00F3rio
prt_ParcelaEmAberto_horario=Hor\u00E1rio do Plano
prt_ParcelaEmAberto_Situacao=Situa\u00E7\u00E3o
prt_ParcelaEmAberto_nomeOperador=Respons\u00E1vel pelo Cadastro da parcela
prt_ParcelaEmAberto_Clientenome=Nome do Cliente
prt_ParcelaEmAberto_RespPgto=Respons\u00E1vel pelo Pagamento
prt_ParcelaEmAberto_Turma=Turma
prt_SaldoContaCorrente_tituloRel=Relat\u00F3rio Saldo Conta Corrente do Cliente
prt_SaldoContaCorrente_Empresa=Empresa
prt_SaldoContaCorrente_Clientenome=Cliente
prt_SaldoContaCorrente_SaldoEntre=Saldo Entre R$
prt_SaldoContaCorrente_ate=A
prt_SaldoContaCorrente_positivo=Positivo
prt_SaldoContaCorrente_negativo=Negativo
prt_SaldoContaCorrente_$=R$
prt_TotalizadorFrequencia_tituloForm=Relat\u00F3rio Totalizador de Acessos
prt_TotalizadorFrequencia_periodoPesquisa=Per\u00EDodo\:
prt_TotalizadorFrequencia_frequencia=Frequ\u00EAncias\:
prt_TotalizadorFrequencia_agrupamento=Agrupamento\:
prt_TotalizadorFrequencia_grafico=Gr\u00E1fico\:
prt_TotalizadorFrequencia_contabilizarAcessoDia=Considerar primeiro acesso do dia\:
prt_TotalizadorTickets_tituloForm=Relat\u00F3rio Totalizador de Tickets
prt_TotalizadorTickets_periodoPesquisa=Per\u00EDodo:
prt_ListaAcesso_tituloForm=Relat\u00F3rio Lista de Acessos
prt_ListaAcesso_empresa=Empresa\:
prt_ListaAcesso_periodoPesquisa=Per\u00EDodo de Pesquisa\:
prt_ListaAcesso_faixaHorario=Faixa Hor\u00E1ria\:
prt_ListaAcesso_faixaHoraInicial=In\u00EDcio\:
prt_ListaAcesso_faixaHoraFinal=Fim\:
prt_ListaAcesso_tipoConsultaAcesso=Filtrar por\:
prt_ListaAcesso_clienteNome=Cliente\:
prt_ListaAcesso_colaboradorNome=Colaborador\:
prt_ListaAcesso_professorNome=Professor Turma\:
prt_ListaAcesso_professorVinculo=Professor V\u00EDnculo\:
prt_ListaAcesso_grupo=Grupo\:
prt_ListaAcesso_plano=Plano\:
prt_ListaAcesso_modalidade=Modalidade\:
prt_ListaAcesso_professorTreinoNome=Professor TreinoWeb\:
prt_OrganizadorCarteira_tituloForm=Organizador de Carteira
prt_OrganizadorCarteira_descricao=Descri\u00E7\u00E3o
prt_OrganizadorCarteira_tipoGrupo=Tipo Grupo
prt_OrganizadorCarteira_tipoVisao=Tipo Vis\u00E3o
prt_OrganizadorCarteira_gerente=Respons\u00E1vel pelo grupo
prt_OrganizadorCarteira_codigo=C\u00F3digo
prt_OrganizadorCarteira_total=Total
prt_OrganizadorCarteira_codigoColaborador=C\u00F3digo
prt_OrganizadorCarteira_nomeColaborador=Nome
prt_OrganizadorCarteira_clienteCodigo=C\u00F3digo
prt_OrganizadorCarteira_clienteNome=Nome do Aluno
prt_OrganizadorCarteira_clienteMatricula=Matr\u00EDcula
prt_OrganizadorCarteira_opcao=Op.
prt_OrganizadorCarteira_qtdCliente=Total Cliente
prt_OrganizadorCarteira_dataNasc=Data de Nasc
prt_OrganizadorCarteira_profissao=Profiss\u00E3o
prt_OrganizadorCarteira_idadeCliente=Idade
prt_OrganizadorCarteira_situacaoCliente=Situa\u00E7\u00E3o Cliente
prt_OrganizadorCarteira_codigoContrato=Contrato
prt_OrganizadorCarteira_nomeModalidade=Modalidades
prt_OrganizadorCarteira_duracao=Dura\u00E7\u00E3o
prt_OrganizadorCarteira_descricaoPlano=Plano
prt_OrganizadorCarteira_valorFatura=Valor Fatura do Contrato
prt_OrganizadorCarteira_valorPago=Valor Pago Contrato
prt_OrganizadorCarteira_valorAberto=Valor Parcelas em Aberto
prt_OrganizadorCarteira_saldoConta=Saldo Conta Corrente
prt_OrganizadorCarteira_vinculoCarteira=Ver Carteira
prt_OrganizadorCarteira_nomeColaboradorCarteira=Desmembrar Carteira
prt_OrganizadorCarteira_diaVigenciaDe=Dia In\u00EDcio Vig\u00EAncia Contrato
prt_OrganizadorCarteira_mesVigenciaDe=M\u00EAs In\u00EDcio Vig\u00EAncia Contrato
prt_OrganizadorCarteira_anoVigenciaDe=Ano In\u00EDcio Vig\u00EAncia Contrato
prt_OrganizadorCarteira_dataBV=Data BV
prt_OrganizadorCarteira_dataTermino=Data T\u00E9rmino Contrato.
prt_OrganizadorCarteira_dataInicio=Data In\u00EDcio Contrato.
prt_OrganizadorCarteira_dataRematricula=Data Rematr\u00EDcula
prt_OrganizadorCarteira_nrDiasUltimoAcesso=Dias Ult. Acesso
prt_OrganizadorCarteira_horaUltimoAcesso=Per\u00EDodo Mais Acessado
prt_OrganizadorCarteira_diavigenciaate=Dia T\u00E9rmino Original Contrato
prt_OrganizadorCarteira_mesvigenciaate=M\u00EAs T\u00E9rmino Original Contrato
prt_OrganizadorCarteira_anovigenciaate=Ano T\u00E9rmino Original Contrato
prt_OrganizadorCarteira_diavigenciaateajustada=Dia T\u00E9rmino Ajustado Contrato
prt_OrganizadorCarteira_mesvigenciaateajustada=Mes T\u00E9rmino Ajustado Contrato
prt_OrganizadorCarteira_anovigenciaateajustada=Ano T\u00E9rmino Ajustado Contrato
prt_OrganizadorCarteira_diadatalancamento=Dia Lan\u00E7amento Contrato
prt_OrganizadorCarteira_mesdatalancamento=M\u00EAs Lan\u00E7amento Contrato
prt_OrganizadorCarteira_anodatalancamento=Ano Lan\u00E7amento Contrato
prt_OrganizadorCarteira_diadatarenovarrealizada=Dia Renova\u00E7\u00E3o Realizada
prt_OrganizadorCarteira_mesdatarenovarrealizada=M\u00EAs Renova\u00E7\u00E3o Realizada
prt_OrganizadorCarteira_anodatarenovarrealizada=Ano Renova\u00E7\u00E3o Realizada
prt_OrganizadorCarteira_diadatarematricularealizada=Dia Rematr\u00EDcula Realizada
prt_OrganizadorCarteira_mesdatarematricularealizada=M\u00EAs Rematr\u00EDcula Realizada
prt_OrganizadorCarteira_anodatarematricularealizada=Ano Rematr\u00EDcula Realizada
prt_OrganizadorCarteira_diaultimobv=Dia \u00DAltimo BV
prt_OrganizadorCarteira_mesultimobv=M\u00EAs \u00DAltimo BV
prt_OrganizadorCarteira_anoultimobv=Ano \u00DAltimo BV
prt_OrganizadorCarteira_msgIgnoraMes=Consultar todos os grupos que tenham um cliente vinculado a uma carteira
prt_OrganizadorCarteira_msgNMes=Consultar grupos que tenham clientes com um par\u00E2metro n\u00FAmero de dias para ver menos pessoas. Inativos a menos de X dias, Vencidos a menos de X dias, BV feitos a menos de X dias ,Trancados e Trancados vencidos a menos de X dias e todos os Ativos .
prt_OrganizadorCarteira_msgDesmembrarCarteira=Com essa op\u00E7\u00E3o marcada o usu\u00E1rio podera ver o nome dos colaboradores vinculado ao aluno.
prt_ClientePorDuracao_empresa=Empresa
prt_GrupoColaboradorParticipante_tituloForm=Participante
prt_GrupoColaboradorParticipante_codigo=C\u00F3digo
prt_GrupoColaboradorParticipante_colaborador=Colaborador
prt_GrupoColaboradorParticipante_tipoColaborador=Tipo Colaborador
prt_GrupoColaboradorParticipante_consultarColaborador=Consultar Colaborador
prt_GrupoColaboradorParticipante_grupoColaborador=Grupo Colaborador
prt_GrupoColaboradorParticipante_consultarGrupoColaborador=Consultar Grupo Colaborador
prt_GrupoColaboradorParticipante_tipoVisao=Tipo Vis\u00E3o
prt_GrupoColaboradorParticipante_mensagemConsultarParticipante=Consultar Participante\:
prt_GrupoColaboradorParticipante_editarParticipante=Editar Participante
prt_GrupoColaboradorParticipante_qtdCliente=Total Cliente
prt_GrupoColaborador_tituloForm=Grupo Colaborador
prt_GrupoColaborador_codigo=C\u00F3digo
prt_GrupoColaborador_descricao=* Descri\u00E7\u00E3o
prt_GrupoColaborador_tipoGrupo=Tipo do Grupo
prt_GrupoColaborador_situacaoGrupo=Situa\u00E7\u00E3o
prt_GrupoColaborador_gerente=* Usu\u00E1rio Respons\u00E1vel pelo grupo
prt_GrupoColaborador_cpf=CPF
prt_GrupoColaborador_consultarGerente=Consultar Gerente
prt_GrupoColaborador_nome=Nome
prt_DefinirLayout_tituloForm=Definir Layout
prt_DefinirLayout_codigo=C\u00F3digo
prt_DefinirLayout_usuario=Usu\u00E1rio
prt_DefinirLayout_titulo=T\u00EDtulo
prt_DefinirLayout_url=Url
prt_DefinirLayout_sequencia=Sequ\u00EAncia
prt_ConfiguracaoSistemaCRM_tituloForm=Configura\u00E7\u00E3o Sistema CRM
prt_ConfiguracaoSistemaCRM_codigo=C\u00F3digo
prt_ConfiguracaoSistemaCRM_remetentePadrao=Remetente Padr\u00E3o
prt_ConfiguracaoSistemaCRM_usarRemetentePadrao=Usar Remetente Padr\u00E3o/Colaborador
prt_ConfiguracaoSistemaCRM_emailPadrao=Email-Padr\u00E3o
prt_ConfiguracaoSistemaCRM_mailServer=Mail Server
prt_ConfiguracaoSistemaCRM_login=Login
prt_ConfiguracaoSistemaCRM_senha=Senha/Token
prt_ConfiguracaoSistemaCRM_conexaoSegura=Conex\u00E3o Segura
prt_ConfiguracaoSistemaCRM_iniciarTLS=Iniciar TLS
prt_ConfiguracaoSistemaCRM_enviarEmailIndividualmente=Envio Individual de E-mail
prt_ConfiguracaoSistemaCRM_urlJenkins=URL Servi\u00E7o Agendamento Mailing
prt_ConfiguracaoSistemaCRM_urlMailing=URL Servi\u00E7o Disparo Mailing
prt_ConfiguracaoSistemaCRM_remetentePadraoMailing=Remetente Padr\u00E3o para Mailing
prt_ConfiguracaoSistemaCRM_abertoSabado=Aberto S\u00E1bado
prt_ConfiguracaoSistemaCRM_abertoDomingo=Aberto Domingo
prt_ConfiguracaoSistemaCRM_nrFaltaPlanoMensal=N\u00FAmero de faltas permitidos para Plano Mensal (Ser\u00E1 considerado os planos de 2 meses)
prt_ConfiguracaoSistemaCRM_nrFaltaPlanoTrimestral=N\u00FAmero de faltas permitidos para Plano Trimestral (Ser\u00E1 considerado os planos de 4 e 5 meses)
prt_ConfiguracaoSistemaCRM_nrFaltaPlanoAcimaSemestral=N\u00FAmero de faltas permitidos para Plano Semestral (Ser\u00E1 considerado todos os planos com mais de 5 meses)
prt_ConfiguracaoSistemaCRM_nrRisco=Peso menor ou igual a ser desconsiderado no grupo de risco
prt_ConfiguracaoSistemaCRM_nrDiasParaClientePreveRenovacao=N\u00FAmero de dias para que o sistema comece a identificar cliente que tenham contrato previstos para renovar.
prt_ConfiguracaoSistemaCRM_nrDiasParaClientePreveRenovacaoMaiorUmMes=N\u00FAmero de dias para que o sistema comece a identificar cliente que tenham contrato previstos para renovar, para contrato maior que um m\u00EAs.
prt_ConfiguracaoSistemaCRM_nrDiasParaClientePrevePerda=(Clientes em situa\u00E7\u00E3o INATIVO) - Dias ap\u00F3s o vencimento do contrato para considerar Desistente
prt_ConfiguracaoSistemaCRM_emailTeste=Email para Teste
prt_ConfiguracaoSistemaCRM_antecipacaoAgendado=Quantidade de dias anteriores a um agendamento para considerar contrato Agendado
prt_ConfiguracaoSistemaCRM_vencidoAgendado=Quantidade de dias ap\u00F3s um agendamento vencido para ainda considerar contrato Agendado
prt_ConfiguracaoSistemaCRM_exAlunoAgendado=Quantidade de dias ap\u00F3s um contato de Ex-Aluno para considerar convers\u00E3o de Ex-Aluno
prt_ConfiguracaoExAlunos_tituloForm=Dias para contatos com Ex-Alunos
prt_ConfiguracaoUltimoaAcessoGym_tituloForm=Dias para contatos com últimos acesso Wellhub
prt_ConfiguracaoDiasPosVenda_tituloForm=Dias para entrar em contato com clientes Matriculados e Rematriculados
prt_ConfiguracaoDiasPosVenda_codigo=C\u00F3digo
prt_ConfiguracaoDiasPosVenda_configuracaoSistemaCRM=Configura\u00E7\u00E3o Sistema CRM
prt_ConfiguracaoDiasPosVenda_consultarConfiguracaoSistemaCRM=Consultar Configura\u00E7\u00E3o Sistema CRM
prt_ConfiguracaoDiasPosVenda_nrDia=N\u00FAmero de Dias
prt_ConfiguracaoDiasPosVenda_descricao=Motivo para entrar em contato com Cliente
prt_ConfiguracaoDiasPosVenda_fase=Fase
prt_ConfiguracaoDiasMetas_descricao=Descri\u00E7\u00E3o
prt_ConfiguracaoDiasPosVenda_tituloNumeroDeFaltas=Clientes que est\u00E3o a dias sem entrar na Academia \!
prt_ConfiguracaoDiasPosVenda_NumeroRisco=Clientes que obtiveram Peso com N\u00DAMERO DE RISCO acima do permitido
prt_ConfiguracaoDiasPosVenda_tituloContratosEspontaneos=Contratos Espont\u00E2neos ou Agendados
prt_ConfiguracaoDiasPosVenda_tituloDataLimiteAgendamento=N\u00FAmero de dias limite para agendamento futuro
prt_ConfiguracaoDiasPosVenda_tituloAgendamentos=Agendamentos
prt_ConfiguracaoDiasPosVenda_tituloMetas=Metas por m\u00EAs por consultor
prt_ConfiguracaoDiasPosVenda_tituloMetasIndicacao=Quantidade de indica\u00E7\u00F5es
prt_ConfiguracaoDiasPosVenda_tituloMetasConvAgendados=Quantidade de Convers\u00F5es de Agendados
prt_ConfiguracaoDiasPosVenda_tituloMetasConvExAlunos=Quantidade de Convers\u00F5es de Ex-Alunos
prt_ConfiguracaoDiasPosVenda_ativo=Ativo
prt_ConfiguracaoDiasPosVenda_renovacao=Incluir Contratos Renovados
prt_ConfiguracaoDiasMetas_semagendamento=Qtde. dias sem agendamento
prt_ConfiguracaoDiasMetas_sessoesfinais=N\u00FAmero de sess\u00F5es restantes
prt_CRMExtra_tituloForm=Meta Extra
prt_Passivo_tituloForm=Receptivo
prt_Passivo_codigo=C\u00F3digo
prt_Passivo_nome=* Nome
prt_Passivo_telefoneResidencial=* Telefone Residencial
prt_Passivo_telefoneCelular=* Telefone Celular
prt_Passivo_telefoneTrabalho=* Telefone Trabalho
prt_Passivo_usuarioResponsavelCadastro=Usu\u00E1rio Respons\u00E1vel Cadastro
prt_Passivo_consultarUsuarioResponsavelCadastro=Consultar Usu\u00E1rio Respons\u00E1vel Cadastro
prt_Passivo_dia=Dia
prt_Passivo_observacao=Observa\u00E7\u00E3o
prt_Passivo_colaborador=Colaborador
prt_Passivo_consultarColaborador=Consultar Colaborador
prt_Passivo_email=E-mail
prt_Indicacao_tituloForm=Indica\u00E7\u00E3o
prt_Indicacao_tituloCons=Lan\u00E7amentos de Indica\u00E7\u00E3o
prt_Indicacao_codigo=C\u00F3digo
prt_Indicacao_usuarioResponsavelCadastro=Respons\u00E1vel Cadastro
prt_Indicacao_consultarUsuarioResponsavelCadastro=Consultar Usu\u00E1rio Respons\u00E1vel Cadastro
prt_Indicacao_clienteQueIndicou=* Cliente que realizou a Indica\u00E7\u00E3o
prt_Indicacao_colaboradorQueIndicou=* Colaborador que realizou a Indica\u00E7\u00E3o
prt_Indicacao_consultarColaboradorQueIndicou=Consultar Colaborador que realizou a Indica\u00E7\u00E3o
prt_Indicacao_nomeIndicado=* Nome Indicado\:
prt_Indicacao_telefoneIndicado=* Telefone Indicado\:
prt_Indicacao_telefone=* Telefone\:
prt_Indicacao_cliente=Cliente
prt_Indicacao_consultarCliente=Consultar Cliente
prt_Indicacao_consultarAluno=Consultar Aluno
prt_Indicacao_colaborador=Colaborador
prt_Indicacao_consultarColaborador=Consultar Colaborador
prt_Indicacao_passivo=Passivo\:
prt_Indicacao_observacao=Observa\u00E7\u00E3o\:
prt_Indicacao_email=E-mail\:
prt_Indicacao_aluno=Aluno\:
VALOR_COMISSAO=Valor comiss\u00E3o
VALOR_PAGO=Valor pago
MODALIDADES=Modalidade(s)
HORARIOS=Hor\u00E1rio(s)
prt_Indicacao_tituloInformacaoIndicador=Informa\u00E7\u00F5es dos Indicados
prt_Indicacao_consultarPassivo=Consultar Passivo
prt_Indicacao_dia=Dia
prt_Indicacao_nomeDoIndicador=Quem Indicou
prt_Indicacao_dataCadastro=Data Cadastro
prt_Indicacao_tipoCliente=Tipo Cliente
prt_Indicacao_colaboradorResponsavel=Colaborador Respons\u00E1vel
prt_Indicacao_evento=Evento
prt_Feriado_tituloForm=Feriado
prt_Feriado_codigo=C\u00F3digo\:
prt_Feriado_descricao=* Descri\u00E7\u00E3o\:
prt_Feriado_dia=* Dia\:
prt_Feriado_mes=M\u00EAs\:
prt_Feriado_nacional=Nacional\:
prt_Feriado_estadual=Estadual\:
prt_Feriado_estado=* Estado\:
prt_Feriado_cidade=* Cidade\:
prt_Feriado_consultarCidade=Consultar Cidade
prt_Feriado_naoRecorrente=* Repetir feriado nos pr\u00F3ximos anos\:
prt_Feriado_feriadoRecorrenteItem=Repetir feriado nos pr\u00F3ximos anos\:
prt_Feriado_pais=* Pa\u00EDs\:
prt_Indicado_nomeIndicado=Nome Indicado\:
prt_Indicado_telefoneIndicado=Telefone Indicado\:
prt_Indicado_telefone=Telefone\:
prt_Indicado_email=E-mail\:
prt_Indicado_tipoCliente=Tipo Situa\u00E7\u00E3o
prt_Indicado_opcoes=Op\u00E7\u00F5es
prt_Agenda_realizarContato=Realizar Contato
prt_Agenda_realizarContatoAvulso=Realizar Contato Avulso
prt_Agenda_historicoContato=Hist\u00F3rico Contato
prt_Agenda_tituloForm=Agendamento
prt_Agenda_codigo=C\u00F3digo
prt_Agenda_passivo=Passivo
prt_Agenda_consultarPassivo=Consultar Cliente Passivo
prt_Agenda_indicacao=Cliente de Indica\u00E7\u00E3o
prt_Agenda_consultarIndicacao=Consultar Cliente de Indica\u00E7\u00E3o
prt_Agenda_colaborador=Colaborador Respons\u00E1vel
prt_Agenda_consultarColaborador=Consultar Colaborador
prt_Agenda_cliente=Cliente
prt_Agenda_consultarCliente=Consultar Cliente
prt_Agenda_dia=Dia
prt_Agenda_hora=Hora
prt_Agenda_tipoAgendamento=Tipo Agendamento
prt_Agenda_modalidade=Modalidade
prt_Agenda_usuarioResponsavelCadastro=Respons\u00E1vel Cadastro
prt_Agenda_consultarUsuarioResponsavelCadastro=Consultar Usu\u00E1rio Respons\u00E1vel
prt_Agenda_totalContatos=Total Contatos
prt_Agenda_realizouVenda=Realizou Venda
prt_Agenda_observacaoAgenda=Observa\u00E7\u00E3o Agenda
prt_Agenda_aluno=Aluno
prt_Agenda_dataCadastro=Data Cadastro
prt_Agenda_diasCadastrado=Dias Cadastrado
prt_Agenda_vencimentoContrato=Vencimento Contrato
prt_Agenda_diasVencido=Dias vencido
prt_Agenda_idade=Idade
prt_Agenda_estadoCivil=Estado Civil
prt_Agenda_ligacoes=Qtde Liga\u00E7\u00F5es
prt_Agenda_diasUltAcesso=Dias \u00DAlt.Acesso
prt_Agenda_avaliacaoFisica=Avalia\u00E7\u00E3o F\u00EDsica
prt_Agenda_exameMedico=Exame M\u00E9dico
prt_Agenda_faseAtual=Fase Atual
prt_Agenda_tipoOperacao=Tipo Opera\u00E7\u00E3o
prt_Agenda_situacao=Situa\u00E7\u00E3o
prt_Agenda_telefones=Telefones
prt_Agenda_contato=Contato
prt_Agenda_ocupado=Ocupado
prt_Agenda_naoEstava=N\u00E3o estava
prt_Agenda_foneIncorreto=Fone Incorreto
prt_Agenda_tipoContato=Tipo Contato
prt_Agenda_grauSatisfacao=Grau Satisfa\u00E7\u00E3o
prt_Agenda_Selecione=-- Selecione --
prt_Agenda_email=E-mail
prt_Agenda_telefoneNaoEncontrado=Cliente n\u00E3o possui nenhum telefone cadastrado\!
prt_Agenda_numero=N\u00FAmero
prt_Agenda_tipoTelefone=Tipo Telefone
prt_Agenda_descricao=Descri\u00E7\u00E3o
prt_Agenda_enviarEmail=Enviar E-mail
prt_Agenda_listaEmail=Lista de E-mail
prt_Agenda_comentario=Coment\u00E1rio
prt_Agenda_situacaoContato=Situa\u00E7\u00E3o Contato
prt_Agenda_escolhaAgendamento=Escolha o tipo de Agendamento
prt_Agenda_reagendamento=Reagendamento
prt_Agenda_data=Data
prt_Agenda_dataAgendada=Data Agendada
prt_Agenda_horaAgendada=Hora Agendada
prt_Agenda_agendado=- AGENDADO -
prt_Agenda_dataLancamento=Data Lan\u00E7amento
prt_Agenda_qtdEmail=Qtde Email
prt_Agenda_qtdSMS=Qtde SMS
prt_Agenda_naoPossueEmail=Cliente n\u00E3o possui E-mail
prt_Agenda_emails=E-mail
prt_Agenda_minuto=Minutos
prt_Agenda_qtdPessoal=Qtde Pessoal
prt_Agenda_dataHora=Data/Hora
prt_Agenda_fase=Fase
prt_Agenda_resultado=Resultado
prt_Agenda_resultadoA=Resultado
prt_Agenda_usuario=Usu\u00E1rio
prt_Agenda_residencial=Tel. Residencial
prt_Agenda_celular=Tel. Celular
prt_Agenda_gravarSimplesRegistro=Deseja realmente gravar um Simples Registro ?
prt_Agenda_SimplesRegistro=Simples Registro
prt_HistoricoIndicacao_titulo=Hist\u00F3rico de Indica\u00E7\u00F5es
prt_HistoricoIndicacao_pessoa=Pessoa
prt_HistoricoIndicacao_idade=Idade
prt_HistoricoIndicacao_estadoCivil=Estado Civil
prt_HistoricoIndicacao_dataCadastro=Data do Cadastro
prt_HistoricoIndicacao_dataHora=Data/Hora
prt_HistoricoIndicacao_evento=Evento
prt_HistoricoIndicacao_indicados=Indicados
prt_HistoricoIndicacao_responsavel=Respons\u00E1vel Cadastro
prt_HistoricoContato_tituloForm=Hist\u00F3rico Contato
prt_HistoricoContato_colaborador=Colaborador
prt_HistoricoContato_fase=Fase
prt_HistoricoContato_agenda=Agenda
prt_HistoricoContato_renovacao=Renova\u00E7\u00E3o
prt_HistoricoContato_faltas=Faltas
prt_HistoricoContato_indicado=Indicado
prt_HistoricoContato_perda=Desistente
prt_HistoricoContato_horas=24 Horas
prt_HistoricoContato_posVenda=P\u00F3s-Venda
prt_HistoricoContato_aniversario=Aniversariantes
prt_HistoricoContato_passivo=Passivo
prt_HistoricoContato_objecao=Obje\u00E7\u00E3o
prt_HistoricoContato_operacao=Opera\u00E7\u00E3o
prt_HistoricoContato_contatos=Tipo Contato
prt_HistoricoContato_matricula=Matr\u00EDcula
prt_HistoricoContato_nome=Nome
prt_HistoricoContato_faseAtual=Fase Atual
prt_HistoricoContato_faseAnterior=Fase Anterior
prt_HistoricoContato_faseInicio=Fase Inicio
prt_HistoricoContato_resultado=Resultado
prt_HistoricoContato_dataContato=Data Contato
prt_HistoricoContato_responsavelCadastro=Respons\u00E1vel Cadastro
prt_HistoricoContato_dataUltimoContato=\u00DAltimo Contato
prt_HistoricoContato_risco=Grupo de Risco
prt_HistoricoContato_gerouAgendamento=Gerou Agendamento
prt_HistoricoContato_faturamento=Faturamento
prt_HistoricoContato_qtdVenda=QtdVendas
prt_HistoricoContato_reagendamento=Reagendamento
prt_HistoricoContato_confirmacao=Confirma\u00E7\u00E3o
prt_HistoricoContato_comparecimento=Comparecimento
prt_HistoricoContato_contatoPessoa=Pessoal
prt_HistoricoContato_contatoTelefonico=Telefone
prt_HistoricoContato_contatoWhatsApp=WhatsApp
prt_HistoricoContato_ligacaoSemContato=Liga\u00E7\u00E3o Sem Contato
prt_HistoricoContato_contatoEmail=E-mail
prt_HistoricoContato_contatoSMS=SMS
prt_HistoricoContato_OutrosOpcoes=Outras Opc\u00F5es
prt_HistoricoContato_resultadoSimplesContato=Simples Contato
prt_HistoricoContato_resultadoObjecao=Obje\u00E7\u00E3o
prt_HistoricoContato_resultadoAgLigacao=Ag. liga\u00E7\u00E3o
prt_HistoricoContato_resultadoAgAula=Ag. Aula
prt_HistoricoContato_resultadoAgVisita=Ag. Visita
prt_HistoricoContato_resultadoOutros=Outros
prt_FecharMeta_tituloForm=FecharMeta
prt_FecharMeta_codigo=C\u00F3digo
prt_FecharMeta_dataRegistro=Data Registro
prt_FecharMeta_responsavelCadastro=ResponsavelCadastro
prt_FecharMeta_colaboradorResponsavel=ColaboradorResponsavel
prt_FecharMeta_meta=Meta
prt_FecharMeta_metaAtingida=Meta Atingida
prt_FecharMeta_porcentagem=Porcentagem
prt_FecharMeta_justificativa=Justificativa
prt_FecharMeta_codOrigem=C\u00F3digo Origem
prt_FecharMeta_aberturaMeta=Abertura Meta
prt_AberturaMeta_tituloForm=Abertura Meta
prt_AberturaMeta_codigo=* C\u00F3digo
prt_AberturaMeta_colaboradorResponsavel=* Colaborador Respons\u00E1vel
prt_AberturaMeta_responsavelCadastro=* Respons\u00E1vel Cadastro
prt_AberturaMeta_dia=* Dia
prt_AberturaMeta_descricao=Descri\u00E7\u00E3o\:
prt_AberturaMeta_meta=Meta
prt_AberturaMeta_porcentagem=(%)
prt_AberturaMeta_metaAtingida=Meta Atingida
prt_AberturaMeta_usuario=* Usu\u00E1rio\:
prt_AberturaMeta_senha=* Senha\:
prt_AberturaMeta_resultado=resultado\:
prt_AberturaMeta_metaDoDiaVenda=Metas Indicador de Vendas
prt_AberturaMeta_metaDoDiaRetencao=Metas Indicador de Reten\u00E7\u00E3o
prt_AberturaMeta_metaDoDiaEstudio=Metas Indicador de Vendas - Agenda Est\u00FAdio
prt_AberturaMeta_totalizadorClientePotencial=Total
prt_AberturaMeta_totalMeta=Total Meta
prt_AberturaMeta_totalMetaLista=Total Lista
prt_AberturaMeta_totalComparecidos=Total Comparecidos
prt_AberturaMeta_totalizadorClientePotencialEmail=Total selecionados
prt_AberturaMeta_periodo=Data da Meta
prt_AberturaMeta_ate=at\u00E9
prt_AberturaMeta_informacoesEspecifica=Informa\u00E7\u00F5es espec\u00EDficas
prt_AberturaMeta_sinalIgual=\=
prt_AberturaMeta_tagNome=<Nome>
prt_AberturaMeta_metaPorcentagem=(%)
prt_AberturaMeta_descricaoGrupos=Descri\u00E7\u00E3o dos Grupos -
prt_AberturaMeta_totalizadorListaMailing=Total de pessoas na gympassVO
prt_AberturaMeta_totalizadorEnviados=Total de pessoas na gympassVO enviadas
prt_AberturaMeta_descricaoBotaoAtualizar=Bot\u00E3o respons\u00E1vel por atualizar o \u00EDndice de metas atingidas somente do colaborador respons\u00E1vel, e se o colaborador fizer parte de algum grupo, ent\u00E3o ele vir\u00E1 marcado na descri\u00E7\u00E3o dos grupos.
prt_AberturaMeta_descricaoBotaoAtualizarIndicadores=Atualizar indicadores.
prt_Evento_tituloForm=Evento
prt_Evento_codigo=C\u00F3digo
prt_Evento_descricao=* Descri\u00E7\u00E3o
prt_Evento_status=* Status
prt_HistoricoVinculo_tituloForm=Hist\u00F3rico V\u00EDnculo
prt_HistoricoVinculo_codigo=C\u00F3digo
prt_HistoricoVinculo_cliente=Cliente
prt_HistoricoVinculo_dataHoraEntrada=Data Entrada
prt_HistoricoVinculo_dataHoraSaida=Data Saida
prt_HistoricoVinculo_dataRegistro=Data Registro
prt_HistoricoVinculo_tipoHistoricoVinculo=Tipo Hist\u00F3rico V\u00EDnculo
prt_HistoricoVinculo_colaborador=Colaborador
prt_HistoricoVinculo_tipoColaborador=Tipo Colaborador
prt_HistoricoVinculo_origem=Origem
prt_HistoricoVinculo_respAlteracao=Resp. Altera\u00E7\u00E3o
prt_SituacaoClienteSinteticoDW_tituloForm=Situa\u00E7\u00E3o Clientes Sint\u00E9tico
prt_GestaoTransacoes_tituloForm=Gest\u00E3o Transa\u00E7\u00F5es
prt_Objecao_tituloForm=Obje\u00E7\u00E3o
prt_Objecao_descricao=* Descri\u00E7\u00E3o
prt_Objecao_grupo=* Grupo
prt_Objecao_ativo=Ativo
prt_Objecao_tipoGrupo=* Tipo Grupo
prt_Objecao_codigo=C\u00F3digo
prt_Objecao_objecao=- Obje\u00E7\u00E3o -
prt_Objecao_descricaoObjecao=Obje\u00E7\u00E3o\:
prt_Objecao_consultarObjecao=Consultar Obje\u00E7\u00E3o
prt_Objecao_comentario=Coment\u00E1rio
prt_ClientePotencial_consProf=Cons/Prof.
prt_ClientePotencial_data=Data\:
prt_ClientePotencial_consultarProfessor=Consultor/Professor\:
prt_ClientePotencial_filtrarPor=Filtrar Por\:
prt_ClientePotencial_colaboradorResponsavel=Colaborador Respons\u00E1vel
prt_ClientePotencial_responsavelCadastro=Respons\u00E1vel Cadastro
prt_ClientePotencial_orientador=Orientador
prt_ClientePotencial_matricula=Matr\u00EDc.
prt_ClientePotencial_nomePessoa=Nome da Pessoa
prt_ClientePotencial_nome=Nome
prt_ClientePotencial_dataBV=Data BV
prt_ClientePotencial_telResidencial=Tel. Residencial
prt_ClientePotencial_telCelular=Tel. Celular
prt_ClientePotencial_telTrabalho=Tel. Trabalho
prt_ClientePotencial_email=E-mail
prt_ClientePotencial_evento=Evento
prt_ClientePotencial_resultado=Resultado
prt_ClientePotencial_dataLancamento=Data Lan\u00E7amento
prt_ClientePotencial_dataUltimoContato=\u00DAltimo Contato
prt_ClientePotencial_opcoes=Op\u00E7\u00F5es
prt_ClientePotencial_nomeIndicado=Nome Indicado
prt_ClientePotencial_telefoneIndicado=Telefone Indicado
prt_ClientePotencial_telefone=Telefone
prt_ClientePotencial_indicador=Indicador
prt_ClientePotencial_pesquisar=Pesquisar\:
prt_ClientePotencial_enviarEmail=Enviar E-mail
prt_RenovacaoAnalitico_tituloLista=Relat\u00F3rio de Previs\u00E3o de Renova\u00E7\u00E3o por Contrato
prt_HistoricoIndicado_tituloForm=Indicado
prt_HistoricoIndicado_consProf=Cons/Prof.
prt_HistoricoIndicado_nome=Nome
prt_HistoricoIndicado_ate=at\u00E9
prt_HistoricoIndicado_opcoes=Op\u00E7\u00F5es
prt_HistoricoIndicado_nomePessoa=Nome Indicado
prt_HistoricoIndicado_dataLancamento=Data Lan\u00E7amento
prt_HistoricoIndicado_telIndicado=Tel. Indicado
prt_HistoricoIndicado_telefone=Telefone
prt_HistoricoIndicado_email=E-mail
prt_HistoricoIndicado_evento=Evento
prt_HistoricoIndicado_colaboradorResponsavel=Colaborador Respons\u00E1vel
prt_HistoricoIndicado_resultado=Resultado
prt_HistoricoIndicado_periodo=Per\u00EDodo de
prt_HistoricoIndicado_pesquisar=Pesquisar\:
prt_Agendados_opcoes=Op\u00E7\u00F5es
prt_Agendados_nomePessoa=Nome Pessoa
prt_Agendados_dataLancamento=Data Lan\u00E7amento
prt_Agendados_matricula=Matr\u00EDcula
prt_Agendados_tituloForm=Agendados
prt_Ligacao_Agendados_tituloForm=Agendados de Amanh\u00E3
prt_ConversaoAgendados_tituloForm=Convers\u00E3o de Agendados
prt_ConversaoAgendados_ObteveSucesso=Obteve sucesso?
prt_Agendados_tipoAgendamento=Tipo Agendamento
prt_Agendados_pesquisar=Pesquisar\:
prt_Agendados_periodo=Per\u00EDodo de
prt_Agendados_nome=Nome
prt_Agendados_dataComparecimento=Data Comparecimento
prt_Agendados_dataAgendamento=Data Agendamento
prt_Agendados_responsavel=Colaborador Respons\u00E1vel
prt_Agendados_responsavelComparecimento=Respons\u00E1vel Comparecimento
prt_Agendados_resultadoHistorico=Result. Hist\u00F3rico
prt_Agendados_resultadoAgendamento=Result. Agendamento
prt_Agendados_motivoContato=Motivo entrar Contato
prt_Agendados_valorContrato=Valor Contrato
prt_Agendados_modalidade=Modalidade
prt_Agendados_responsavelcadastro=Respons\u00E1vel Lan\u00E7amento
prt_VinteQuatroHoras_tituloForm=24 Horas
prt_VinteQuatroHoras_ate=at\u00E9
prt_VinteQuatroHoras_periodo=Per\u00EDodo de
prt_VinteQuatroHoras_nome=Nome
prt_VinteQuatroHoras_opcoes=Op\u00E7\u00F5es
prt_Vendas_tituloForm=Quantidade Vendas
prt_Vendas_ate=at\u00E9
prt_Vendas_periodo=Per\u00EDodo de
prt_Vendas_nome=Nome
prt_Vendas_opcoes=Op\u00E7\u00F5es
prt_Faturamento_tituloForm=Faturamento
prt_Faturamento_ate=at\u00E9
prt_Faturamento_periodo=Per\u00EDodo de
prt_Faturamento_nome=Nome
prt_Faturamento_opcoes=Op\u00E7\u00F5es
prt_Faltas_tituloForm=Faltas
prt_Faltas_ate=at\u00E9
prt_Faltas_periodo=Per\u00EDodo de
prt_Faltas_nome=Nome
prt_Faltas_opcoes=Op\u00E7\u00F5es
prt_Desistentes_tituloForm=Desistentes
prt_Desistentes_ate=at\u00E9
prt_Desistentes_periodo=Per\u00EDodo de
prt_Desistentes_nome=Nome
prt_Desistentes_opcoes=Op\u00E7\u00F5es
prt_Vencidos_tituloForm=Vencidos
prt_Vencidos_ate=at\u00E9
prt_Vencidos_periodo=Per\u00EDodo de
prt_Vencidos_nome=Nome
prt_Vencidos_opcoes=Op\u00E7\u00F5es
prt_GrupoRisco_tituloForm=Grupo Risco
prt_GrupoRisco_ate=at\u00E9
prt_GrupoRisco_periodo=Per\u00EDodo de
prt_GrupoRisco_nome=Nome
prt_GrupoRisco_NumeroPeso=Peso
prt_GrupoRisco_opcoes=Op\u00E7\u00F5es
prt_PosVenda_tituloForm=P\u00F3s Venda
prt_PosVenda_ate=at\u00E9
prt_PosVenda_periodo=Per\u00EDodo de
prt_PosVenda_nome=Nome
prt_PosVenda_opcoes=Op\u00E7\u00F5es
prt_Renovacao_tituloForm=Renova\u00E7\u00E3o
prt_Renovacao_ate=at\u00E9
prt_Renovacao_periodo=Per\u00EDodo de
prt_Renovacao_nome=Nome
prt_Renovacao_opcoes=Op\u00E7\u00F5es
prt_Aniversariante_tituloForm=Aniversariantes
prt_Aniversariante_ate=at\u00E9
prt_Aniversariante_periodo=Per\u00EDodo de
prt_Aniversariante_nome=Nome
prt_Aniversariante_opcoes=Op\u00E7\u00F5es
prt_FechamentoDia_tituloForm=Fechamento da Meta do Dia
prt_FechamentoDia_AberturatituloForm=Abertura Meta
prt_FechamentoDia_percentual=Percentual
prt_FechamentoDia_metaAtingida=Meta Atingida
prt_FechamentoDia_metas=Metas
prt_FechamentoDia_meta=Meta
prt_FechamentoDia_identificadorMeta=Identificador Metas
prt_FechamentoDia_justificativa=Justificativa
prt_FechamentoDia_status=Status
prt_FechamentoDia_dia=Dia
prt_FechamentoDia_colaboradorResponsavel=Colaborador Respons\u00E1vel\:
prt_FechamentoDia_responsavelCadstro=Respons\u00E1vel Cadastro\:
prt_FechamentoDia_responsavelCadstroColuna=Respons\u00E1vel Cadastro
prt_FechamentoDia_metaEmAberto=Meta em Aberto
prt_FechamentoDia_totalizadorMetas=Totalizador de Metas
prt_FechamentoDia_periodode=Per\u00EDodo de
prt_FechamentoDia_ate=at\u00E9
prt_FechamentoDia_repescagem=Repescagem
prt_FechamentoDia_total=Totais
prt_FechamentoDia_resultado=Resultado
prt_EditarParcelas_tituloForm=Edi\u00E7\u00E3o de Parcelas
prt_EditarParcelas_codigo=C\u00F3digo\:
prt_EditarParcelas_descricao=Descri\u00E7\u00E3o\:
prt_EditarParcelas_dataVencimento=Data Vencimento\:
prt_EditarParcelas_valorParcela=Valor Parcela\:
prt_RelatorioCliente_tituloForm=Relat\u00F3rio de Clientes
prt_RelatorioCliente_Matricula=Matr\u00EDcula
prt_RelatorioCliente_Nome=Nome
prt_RelatorioCliente_Situacao=Situa\u00E7\u00E3o
prt_RelatorioCliente_Empresa=Empresa
prt_RelatorioCliente_vinculo=V\u00EDnculo
prt_RelatorioCliente_plano=Plano
prt_RelatorioCliente_contrato=Contrato
prt_RelatorioCliente_modalidade=Modalidade
prt_RelatorioCliente_duracao=Dura\u00E7\u00E3o
prt_RelatorioCliente_horario=Hor\u00E1rio
prt_RelatorioCliente_inicio=Inicio
prt_RelatorioCliente_vence=Vence
prt_RelatorioCliente_valor=Faturamento
prt_RelatorioCliente_receita=Receita
prt_RelatorioCliente_valorModalidade=Valor Mod.
prt_RelatorioCliente_dtLancamento=Lan\u00E7amento
prt_RelatorioCliente_categoriaCliente=Categoria
prt_RelatorioCliente_nivelTurma=N\u00EDvel de Turma
prt_UltimosAcessosSistema=\u00DAltimos Acessos no Sistema
prt_UltimosAcessosSistema_codigo=C\u00F3digo
prt_UltimosAcessosSistema_empresa=Empresa
prt_UltimosAcessosSistema_usuario=Usu\u00E1rio
prt_UltimosAcessosSistema_dataRegistro=Data de Registro
prt_UltimosAcessosSistema_horaRegistro=Hor\u00E1rio de Registro
prt_UltimosAcessosSistema_acao=A\u00E7\u00E3o
prt_UltimosAcessosSistema_periodoPesquisa=Per\u00EDodo\:
prt_UltimosAcessosSistema_ate=At\u00E9\:
prt_ListaClientes_buscarAluno=Voc\u00EA pode buscar por um aluno aqui\:
prt_ListaClientes_dataCadastramento=Data Cadastro\:
prt_ListaClientes_dataMatricula=Data Matr\u00EDcula\:
prt_ListaClientes_dataRematricula=\u00DAltima Rematr\u00EDcula\:
prt_ListaClientes_dadosAluno=Dados do Aluno
prt_ListaClientes_contaAcademia=Saldo Conta Academia\:
prt_ListaClientes_Idade=Idade\:
prt_ListaClientes_mensagensAvisos=Mensagens / Avisos
prt_ListaClientes_grauSatisfacao=Grau de Satisfa\u00E7\u00E3o\:
prt_ListaClientes_nrDiasUltimoContato=N\u00BA de dias do \u00FAltimo contato \:
prt_Comparativo_ContratosVencemMesAtual=Contratos vencem no m\u00EAs atual
prt_Comparativo_ContratosEstaoVencidos=Contratos que est\u00E3o vencidos
prt_Comparativo_estaoGrupoRisco=Est\u00E3o no Grupo de Risco
prt_Comparativo_ClientesSemContato=Clientes sem Contato
prt_Comparativo_ContratosMetade=Contratos que est\u00E3o na metade
prt_ConsultaMeta_tituloForm=Consulta de Metas
prt_pagamentoCartaoCredito_nomeTitular=Nome do titular impresso no cart\u00E3o de cr\u00E9dito.
prt_pagamentoCartaoCredito_validade=A validade indicada no cart\u00E3o de cr\u00E9dito. Ex\: 10/2025.
prt_pagamentoCartaoCredito_parcelas=N\u00FAmero de parcelas em que ser\u00E1 dividido o pagamento.
prt_pagamentoCartaoCredito_anoValidade=O ano indicado na validade do cart\u00E3o de cr\u00E9dito.
prt_pagamentoCartaoCredito_cpfPortador=O CPF/CNPJ do portador do cart\u00E3o de cr\u00E9dito.
prt_pagamentoCartaoCredito_nomeMaeTitular=Nome da m\u00E3e do titular do cart\u00E3o de cr\u00E9dito.
prt_existeMatricula_atualizarMatricula=Deseja gerar novamente a matr\u00EDcula?
prt_Finan_Lancamentos_tituloForm=Lan\u00E7amentos Financeiros
prt_Finan_Lancamentos_tituloFormRecebimentos=Contas a Receber
prt_Finan_Lancamentos_tituloFormPagamentos=Contas a Pagar
prt_Finan_Lancamentos_parametros=Par\u00E2metros
prt_Finan_Lancamentos_filtro=Filtro
prt_Finan_Lancamentos_valorDe=Valor de
prt_Finan_Lancamentos_a=a
prt_Finan_Lancamentos_formaPagamento=Forma de Pagamento\:
prt_Finan_Lancamentos_conta=Conta\:
prt_Finan_Lancamentos_empresa=Empresa
prt_Finan_Lancamentos_tipoLancamento=Tipo de Lan\u00E7amento\:
prt_Finan_Lancamentos_descricao=Descri\u00E7\u00E3o
prt_Finan_Lancamentos_empresaOrigem=Empresa
prt_Finan_Lancamentos_parcela=Parcela
prt_Finan_Lancamentos_dados=Dados do Lan\u00E7amento
prt_Finan_Lancamentos_parcelas=Parcelas
prt_Finan_Lancamentos_tipoPesquisa=Tipo de Pesquisa\:
prt_Finan_Lancamentos_periodo=Per\u00EDodo\:
prt_Finan_Lancamentos_periodoAte=At\u00E9\:
prt_Finan_Lancamentos_pesquisarPor=Pesquisar por\:
prt_Finan_Lancamentos_alteracao=.Deseja replicar as altera\u00E7\u00F5es para as contas futuras?
prt_Finan_Lancamentos_alteracaoAntesQuitar=foram alterados. \u00C9 necess\u00E1rio salvar essas altera\u00E7\u00F5es antes de quitar.
prt_Finan_Lancamentos_somenteQuitados=Somente Quitados
prt_Finan_Lancamentos_somenteNaoQuitados=Somente N\u00E3o Quitados
prt_Finan_Lancamentos_incluirRecebiveis=Incluir Receb\u00EDveis
prt_Finan_Lancamentos_incluirRecebiveisComLote=Com Lote
prt_Finan_Lancamentos_lotepagamovconta=Lan\u00E7amento que este lote paga\:
prt_Finan_Lancamentos_loteusadoparapagar=Lote usado\:
prt_Finan_Lancamentos_incluirRecebiveisSemLote=Sem Lote
prt_Finan_Lancamentos_confirmaExclusao=Excluir parcela
prt_Finan_Lancamentos_confirmaExclusaoPararAgendamento=Deseja parar o agendamento que gerou este lan\u00E7amento?
prt_Finan_Lancamentos_confirmaExclusaoPararAgendamentoConfirmar=Excluir e parar o agendamento
prt_Finan_Lancamentos_confirmaExclusaoPararAgendamentoSomente=Somente excluir o lan\u00E7amento
prt_Finan_Lancamentos_confirmaExclusaoRateio=Excluir rateio selecionado?
prt_Finan_Lancamentos_dataLancamento=Data de Lan\u00E7amento
prt_Finan_Lancamentos_dataLancamento_abreviado=Dt. Lanc
prt_Finan_Lancamentos_dataQuitacao=Data de Quita\u00E7\u00E3o
prt_Finan_Lancamentos_dataQuitacao_abreviado=Dt. Quit
prt_Finan_Lancamentos_dataVencimento=Dt. Venc.
prt_Finan_Lancamentos_dataVencimentoFiltros=Data de Vencimento
prt_Finan_Lancamentos_vencimento=Vencimento
prt_Finan_Lancamentos_quitacao=Quita\u00E7\u00E3o
prt_Finan_Lancamentos_dataCompetencia=Data de Compet\u00EAncia
prt_Finan_Lancamentos_pessoa=Pessoa
prt_Finan_Lancamentos_favorecido=Favorecido
prt_Finan_Lancamentos_valor=Valor R$
prt_Finan_Lancamentos_tipo=Tipo
prt_Finan_Lancamentos_operacoes=Opera\u00E7\u00E3o
prt_Finan_Lancamentos_formaPg=Forma Pgto.\:
prt_Finan_Lancamentos_totalRegistros=Total de Registros\:
prt_Finan_Lancamentos_totalReceb=Receb.\:
prt_Finan_Lancamentos_totalPag=Pag.\:
prt_Finan_Lancamentos_total=Saldo\:
prt_Finan_Lancamentos_abaixoMetaMinima=Abaixo da meta m\u00EDnima
prt_Finan_visualizarDetalhes=Visualizar Detalhes
prt_Finan_Lancamento_empresa=*Empresa\:
prt_Finan_Lancamento_conta=Conta\:
prt_Finan_Lancamento_planoContas=Plano de Contas\:
prt_Finan_Lancamento_centroCusto=Centro de Custo\:
prt_Finan_Lancamento_formaPagamento=Forma de Pagamento\:
prt_Finan_Lancamento_tipoDocumento=Tipo de Documento\:
prt_Finan_Lancamento_descricao=*Descri\u00E7\u00E3o\:
prt_Finan_Lancamento_codigoBarras=C\u00F3digo de barras\:
prt_Finan_Lancamento_numDocumento=N\u00BA Documento\:
prt_Finan_Lancamento_valor=*Valor\:
prt_Finan_Lancamento_LTV=Inside C\u00E1lculo CAC\:
prt_Finan_Lancamento_valorLiquido=*Valor l\u00EDquido\:
prt_Finan_Lancamento_dataLancamento=*Data de Lan\u00E7amento\:
prt_Finan_Lancamento_dataQuitacao=Data de Quita\u00E7\u00E3o\:
prt_Finan_Lancamento_dataCompetencia=*Dt. de Compet\:
prt_Finan_Lancamento_dataVencimento=*Data de Vencimento\:
prt_Finan_Lancamento_observacoes=Observa\u00E7\u00F5es\:
prt_Finan_Lancamento_contaSemPonto=Conta Destino
prt_Finan_Lancamentos_rateioValor=Divis\u00E3o/Rateio de Valor
prt_Finan_Lancamentos_divisaoSoma=Soma da divis\u00E3o\:
prt_Finan_Lancamentos_diferenca=Diferen\u00E7a\:
prt_Finan_Lancamentos_valorLancamento=Valor Lan\u00E7amento\:
prt_Finan_Lancamento_descricaoQuitacao=Descri\u00E7\u00E3o\:
prt_Finan_Lancamento_valorQuitacao=Valor\:
prt_Finan_Lancamento_vencimentoQuitacao=Vencimento\:
prt_Finan_Lancamento_pagamentoQuitacao=Data Quita\u00E7\u00E3o\:
prt_Finan_Caixa_codigo=C\u00F3digo
prt_Finan_Caixa_dataMovimento=Data Lan\u00E7amento
prt_Finan_Caixa_periodoAbertura=Abertura
prt_Finan_Caixa_periodoFechamento=Fechamento
prt_Finan_Caixa_periodoTrabalho=Trabalho
prt_Finan_Caixa_tipoVisualizacao=Tipo Visualiza\u00E7\u00E3o
prt_Finan_Caixa_planoContas=Movimenta\u00E7\u00E3o por Plano de Contas
prt_Finan_Caixa_Contas=Movimenta\u00E7\u00E3o das Contas
prt_Finan_Agendamento_tituloIncluir=Incluir Agendamento
prt_Finan_Agendamento_tituloAlterar=Alterar Agendamento
prt_Finan_Agendamento_descricao=Descri\u00E7\u00E3o\:
prt_Finan_Agendamento_layoutDescricao=Layout para descri\u00E7\u00E3o autom\u00E1tica\:
prt_Finan_Agendamento_frequencia=Frequ\u00EAncia\:
prt_Finan_Agendamento_diaVencimento=Dia Vencimento\:
prt_Finan_Agendamento_proximoVencimento=Pr\u00F3ximo Vencimento\:
prt_Finan_Agendamento_parcelasFixas=Gerar quantidade de parcelas fixas
prt_Finan_Agendamento_parcelaInicial=Parcela Inicial
prt_Finan_Agendamento_parcelaFinal=Parcela Final
prt_Finan_Agendamento_ultimoVencimento=Vencimento da \u00FAltima parcela
prt_Finan_Agendamento_informacoes=Informa\u00E7\u00F5es para gera\u00E7\u00E3o das pr\u00F3ximas parcelas
prt_Finan_Agendamento_gerar=Gerar
prt_Finan_Agendamento_novaGeracao=parcelas a cada nova gera\u00E7\u00E3o.
prt_Finan_Agendamento_faltando=Gerar novas parcelas quando faltar
prt_Finan_Agendamento_paraVencer=dias para vencer a \u00FAltima parcela.
prt_Finan_Agendamento_aviso=Ser\u00E1 considerada como primeira parcela o lan\u00E7amento da tela anterior.
prt_Finan_metaAtingidaAte=Meta atingida at\u00E9
prt_Finan_legenda=Legenda
prt_Finan_consultarPor=Consultar por
prt_Finan_tituloFiltroDetalheMeta=Filtros do detalhamento da meta
prt_Finan_filtros=Filtros
prt_Finan_nrDiasAcademiaAberta=N\u00FAmero de dias academia aberta no m\u00EAs
prt_Finan_Dia=Dia
prt_Finan_fatRealizado=Realizado
prt_Finan_fatAcumulado=Acumulado
prt_Finan_data=Data
prt_Finan_Cliente=Cliente
prt_Finan_Valor=Valor
prt_Finan_Contrato=Contrato
prt_Finan_ValorTotal=Valor total
prt_Finan_RegistrosTotal=Total de registros
prt_Finan_Responsavel=Respons\u00E1vel
prt_Finan_ConsultorResponsavel=Consult. Resp. na \u00C9poca
prt_Finan_ConsultorAtual=Consultor atual
prt_Finan_formaPagamento=Forma de Pagamento
prt_Finan_metaAtingida=Meta atingida
prt_Finan_atualizar=Atualizar
prt_Finan_colaborador=Colaborador
prt_Finan_semMeta=N\u00E3o h\u00E1 meta para este colaborador.
prt_Finan_dreCentroCustos=Visualizar por centro de custos
prt_Finan_CaixaAdministrativo=Caixa Administrativo
prt_Finan_usarCentralFinanceiro=Usar Central de Eventos no Financeiro
prt_Finan_NaoUsarMovimentacaoContas=Usar movimenta\u00E7\u00E3o de contas
prt_Finan_SolicitaSenhaLancarConta=Solicitar senha ao lan\u00E7ar conta a pagar ou receber
prt_Finan_selecionarContaOutraEmpresa=Permitir selecionar conta de qualquer unidade
prt_Finan_especificarCompetencia=Especificar Compet\u00EAncias
prt_Finan_exportacaoAlterData=Habilitar exporta\u00E7\u00E3o para sistema cont\u00E1bil AlterData
prt_CadastroPessoa_tituloForm=Cadastro de Pessoa
prt_CadastroPessoa_empresa=*Empresa\:
prt_CadastroPessoa_tipo=*Tipo\:
prt_CadastroPessoa_nome=*Nome\:
prt_CadastroPessoa_cpf=CPF\:
prt_CadastroPessoaSimp_cpf=*CPF\:
prt_CadastroPessoa_cnpj=CNPJ\:
prt_CadastroPessoa_telefone=Telefone\:
prt_CadastroPessoa_contato=Contato\:
prt_CadastroPessoa_sexo=Sexo biol\u00F3gico\:
prt_CadastroPessoa_email=Email\:
prt_CadastroPessoa_dataNascimento=Data Nascimento\:
prt_CadastroPessoaSimp_dataNascimento=*Data Nascimento\:
prt_CadastroPessoa_endereco=Endere\u00E7o\:
prt_CadastroPessoa_bairro=Bairro\:
prt_CadastroPessoa_cidade=Cidade\:
prt_CadastroPessoa_uf=UF\:
prt_CadastroPessoa_pais=Pa\u00EDs\:
prt_CadastroPessoa_numero=N\u00FAmero\:
prt_Impressao_Lancamento_cabecalho1=Zillyon Web - Financeiro
prt_Impressao_Lancamento_cabecalho2=Desenvolvido por PACTO Solu\u00E7\u00F5es Tecnol\u00F3gicas Ltda.
prt_Impressao_Lancamento_cabecalho3=(0xx62) 3251-5820
prt_Impressao_Lancamento_filtros=Filtros
prt_troca_Cartao_Recorrencia=Para confirmar a troca do cart\u00E3o de cr\u00E9dito de um contrato em regime de recorr\u00EAncia, voc\u00EA deve efetuar um pagamento de uma parcela em aberto deste mesmo contrato. Aqui abaixo est\u00E1 listada a parcela em aberto com data de vencimento mais pr\u00F3xima de hoje. Clicando em 'Confirmar' voc\u00EA ser\u00E1 levado at\u00E9 a tela de pagamento onde poder\u00E1 pag\u00E1-la, e assim realizar a troca do cart\u00E3o.
prt_troca_Cartao_Recorrencia_sem_parcela=Para confirmar a troca do cart\u00E3o de cr\u00E9dito de um contrato em regime de recorr\u00EAncia, voc\u00EA deve efetuar um pagamento de uma parcela em aberto deste mesmo contrato. Por\u00E9m, este n\u00E3o possui mais parcelas em aberto.
prt_Finan_ConsultaPlanoConta_tituloForm=Plano de Contas
prt_Finan_GestaoRecebiveis_tituloForm=Gest\u00E3o de Receb\u00EDveis
prt_Finan_GestaoRecebiveis_empresa=Empresa
prt_Finan_GestaoRecebiveis_nome=Nome Pagador
prt_Finan_GestaoRecebiveis_nomeTerceiro=Nome Terceiro
prt_Finan_GestaoRecebiveis_operador=Operador de Caixa
prt_Finan_GestaoRecebiveis_periodoCompensacao=Per\u00EDodo de Compensa\u00E7\u00E3o
prt_Finan_GestaoRecebiveis_periodoLancamento=Per\u00EDodo de Lan\u00E7amento
prt_Finan_GestaoRecebiveis_faturamentoRecebido=Faturamento Recebido
prt_Finan_GestaoRecebiveis_dataLancamento=Dt.Lan\u00E7amento
prt_Finan_GestaoRecebiveis_dataCompensacao=Dt.Compensa\u00E7\u00E3o
prt_Finan_GestaoRecebiveis_periodoConciliacao=Per\u00EDodo de Concilia\u00E7\u00E3o
prt_Finan_GestaoRecebiveis_valor=Valor
prt_Finan_GestaoRecebiveis_numeroCheque=N.Cheque
prt_Finan_GestaoRecebiveis_dadosLote=Dados do Lote
prt_Finan_GestaoRecebiveis_descricao=Descri\u00E7\u00E3o
prt_Finan_GestaoRecebiveis_lancamentoPagoConjuntos=Lan\u00E7amentos pagos em conjunto 
prt_Finan_GestaoRecebiveis_valorNaoAlterado=Este lan\u00E7amento n\u00E3o pode ter o valor alterado.
prt_Finan_GestaoRecebiveis_dataDeposito=Data Dep\u00F3sito
prt_Finan_GestaoRecebiveis_responsavelLancamento=Respons\u00E1vel Lan\u00E7amento:
prt_Finan_GestaoRecebiveis_movimentacaoDeposito=Movimenta\u00E7\u00E3o/Dep\u00F3sito\:
prt_Finan_GestaoRecebiveis_Lote=Lote
prt_Finan_GestaoRecebiveis_CustodiaDe=Cust\u00F3dia de
prt_Finan_GestaoRecebiveis_Autorizacao=Autoriza\u00E7\u00E3o
prt_Finan_GestaoRecebiveis_NSU=NSU
prt_Finan_GestaoRecebiveis_total=Total Selecionado
prt_Finan_GestaoRecebiveis_qtdeTotal=Quantidade Selecionada
prt_Finan_GestaoRecebiveis_cpf=CPF
prt_Finan_GestaoRecebiveis_nomeClienteContrato=Nome do Cliente
prt_Finan_GestaoRecebiveis_agencia=Ag\u00EAncia
prt_Finan_GestaoRecebiveis_banco=Banco
prt_Finan_GestaoRecebiveis_conta=Conta
prt_Finan_GestaoRecebiveis_nCheque=N\u00BACheque
prt_Finan_GestaoRecebiveis_dataInicio=Data in\u00EDcio
prt_Finan_GestaoRecebiveis_dataFim=Data fim
prt_Finan_GestaoRecebiveis_status=Status
prt_Finan_GestaoRecebiveis_apresentarCaixa=Recebido do ZillyonWeb
prt_Finan_GestaoRecebiveis_codigoAutorizacao=C\u00F3d. Autoriza\u00E7\u00E3o
prt_Finan_GestaoRecebiveis_operadora=Operadora
prt_Finan_GestaoRecebiveis_nsu=NSU
prt_Finan_GestaoRecebiveis_numeroDocumento=N\u00FAmero do Documento
prt_Finan_GestaoRecebiveis_matricula=Matr\u00EDcula
prt_Finan_GestaoLotes_tituloCons=Gest\u00E3o de Lotes
prt_Finan_GestaoLotes_tituloForm=Edi\u00E7\u00E3o de Lotes
prt_Finan_GestaoLotes_tituloConsultaCheques=Consulta Cheques
prt_Finan_GestaoLotes_tituloConsultaCartoes=Consulta Cart\u00F5es Cr\u00E9dito
prt_Finan_GestaoLotes_codigo=C\u00F3digo
prt_Finan_GestaoLotes_descricao=Descri\u00E7\u00E3o
prt_Finan_GestaoLotes_periodoDeposito=Per\u00EDodo de Dep\u00F3sito
prt_Finan_GestaoLotes_total=Total Lote
prt_Finan_GestaoLotes_dataDeposito=Dt.Dep\u00F3sito
prt_Finan_Caixa_Desc=Caixa-Administrativo
prt_Finan_Conta_Movimentacao=Movimenta\u00E7\u00E3o da Conta
prt_Finan_Conta_Conciliar=Conciliar Saldo da Conta
prt_Finan_Conta_Valor_Conciliar=Valor Conciliar
prt_Finan_Conta_data_Conciliar=Data Concilia\u00E7\u00E3o
prt_Finan_Conta_ConciliarMsg=OBS.\: Ser\u00E1 inclu\u00EDdo uma entrada ou uma sa\u00EDda na diferen\u00E7a de valores entre Saldo Atual e o Valor a Conciliar, caso o usu\u00E1rio tenha permiss\u00E3o em "Financeiro\: Conciliar Saldo".
prt_Finan_MetaFinanceiro_tituloForm=Metas do Financeiro
prt_Finan_MetaFinanceiro_empresa=Empresa
prt_Finan_MetaFinanceiro_empresa_maiusculo=EMPRESA
prt_Finan_MetaFinanceiro_ano=Ano
prt_Finan_MetaFinanceiro_mes=M\u00EAs
prt_Finan_MetaFinanceiro_mes_maiusculo=M\u00CAS
prt_Finan_MetaFinanceiro_anoMes=Ano/M\u00EAs
prt_Finan_MetaFinanceiro_descricao=Descri\u00E7\u00E3o
prt_Finan_MetaFinanceiro_descricao_maiusculo=DESCRI\u00C7\u00C3O
prt_Finan_MetaFinanceiro_opcoes=Op\u00E7\u00F5es
prt_Finan_MetaFinanceiro_metas=Metas
prt_Finan_MetaFinanceiro_meta1=Meta 1
prt_Finan_MetaFinanceiro_meta1_maiusculo=META 1
prt_Finan_MetaFinanceiro_meta2=Meta 2
prt_Finan_MetaFinanceiro_meta2_maiusculo=META 2
prt_Finan_MetaFinanceiro_meta3=Meta 3
prt_Finan_MetaFinanceiro_meta3_maiusculo=META 3
prt_Finan_MetaFinanceiro_meta4=Meta 4
prt_Finan_MetaFinanceiro_meta4_maiusculo=META 4
prt_Finan_MetaFinanceiro_meta5=Meta 5
prt_Finan_MetaFinanceiro_meta5_maiusculo=META 5
prt_Finan_MetaFinanceiro_observacoes=Observa\u00E7\u00F5es
prt_Finan_MetaFinanceiro_cor=Cor
prt_Finan_MetaFinanceiro_consultor=Consultor
prt_Finan_MetaFinanceiro_percentual=Percentual
prt_Finan_MetaFinanceiro_historico=Consultar Hist\u00F3rico de Metas
prt_Finan_MetaFinanceiro_atingida=Meta Atingida
prt_Finan_MetaFinanceiro_gerar=Gerar metas p/ todos os consultores
prt_ManutencaoModalidade_modalidade=Modalidade
prt_ManutencaoModalidade_vezesSemana=Vezes Por Semana
prt_ManutencaoModalidade_opcoes=Op\u00E7\u00F5es
prt_ManutencaoModalidade_edicaoModalidade=Edi\u00E7\u00E3o de Modalidade
prt_ManutencaoModalidade_idTurma=Identificador
prt_ManutencaoModalidade_descricaoTurma=Descri\u00E7\u00E3o
prt_ManutencaoModalidade_turmaModalidade=Turmas da Modalidade
prt_ManutencaoModalidade_turma=Turma
prt_ManutencaoModalidade_horariosTurma=Hor\u00E1rios da Turma
prt_ManutencaoModalidade_selecaoModalidades=Sele\u00E7\u00E3o de Modalidades
prt_ManutencaoModalidade_selecaoVezesPorSemana=Vezes Por Semana da Modalidade
prt_ListaClientes_situacao=Situa\u00E7\u00E3o
prt_ListaClientes_matricula=Matr\u00EDcula
prt_ListaClientes_nome=Nome
prt_Finan_CadastroPlanoConta_tituloForm=Cadastro de Plano de Contas
prt_Finan_CadastroPlanoConta_planoPai=Plano Pai\:
prt_Finan_CadastroPlanoConta_descricao=*Descri\u00E7\u00E3o\:
prt_Finan_CadastroPlanoConta_tipoPadrao=*Tipo\:
prt_Finan_CadastroPlanoConta_equivalenciaDRE=Equival\u00EAncia no DRE\:
prt_Finan_CadastroPlanoConta_metaDRE=Meta DRE
prt_Finan_CadastroPlanoConta_codigoLumi=C\u00F3digo LUMI
prt_Finan_CadastroPlanoConta_codigoContabil=*C\u00F3digo Cont\u00E1bil\:
prt_Finan_CadastroPlanoConta_trocarPlanoPai=Trocar Plano Pai
prt_Finan_CadastroPlanoConta_alterarCodigo=Alterar C\u00F3digo
prt_Finan_CadastroPlanoConta_codigoPlano=C\u00F3digo Plano\:
prt_Finan_selecao=Sele\u00E7\u00E3o
prt_Finan_ConsultaCentroCustos_tituloForm=Centro de Custos
prt_Finan_CadastroCentroCustos_tituloForm=Cadastro de Centro de Custos
prt_Finan_CadastroCentroCustos_centroPai=Centro Pai\:
prt_Finan_CadastroCentroCustos_codigoCentro=C\u00F3digo Centro\:
prt_Finan_CadastroCentroCustos_descricao=*Descri\u00E7\u00E3o\:
prt_Finan_CadastroCentroCustos_tipoPadrao=*Tipo Padr\u00E3o\:
prt_Finan_CadastroCentroCustos_codigoContabil=*C\u00F3digo Cont\u00E1bil\:
prt_Finan_CadastroCentroCustos_trocarCentroPai=Trocar Centro Pai
prt_Finan_Conta_tituloForm=Conta
prt_Finan_Conta_consultarPor=Consultar por\:
prt_Finan_Conta_tipoConta=Tipo de Conta
prt_Finan_Conta_empresa=*Empresa\:
prt_Finan_Conta_banco=Banco
prt_Finan_Conta_codigo=C\u00F3digo
prt_Finan_Conta_descricaoConta=Descri\u00E7\u00E3o da Conta
prt_Finan_Conta_agencia=Ag\u00EAncia\:
prt_Finan_Conta_numeroConta=N\u00FAmero da Conta\:
prt_Finan_Conta_contaAtiva=Esta conta \u00E9 ativa?
prt_Finan_Conta_contaTotalizadaBI=Esta conta ser\u00E1 totalizada no BI?
prt_Finan_Conta_observacao=Observa\u00E7\u00E3o
prt_Finan_Conta_ResumoConta=Resumo de Contas
prt_Finan_Conta_saldo=Saldo atual
prt_Finan_Conta_saldoAnterior=Saldo anterior
prt_Finan_Conta_saldoFinal=Saldo final
prt_Finan_Conta_entrada=Entrada
prt_Finan_Conta_saida=Sa\u00EDda
prt_Finan_Conta_formaPgto=Forma Pgto
prt_Finan_Conta_descricaoContaLimpo=Descri\u00E7\u00E3o da Conta
prt_finan_configuracaoFinanceiro_taxa=Informe o Plano de Contas e o Centro de Custos do lan\u00E7amento das taxas de Operadora de Cart\u00E3o
prt_finan_configuracaoFinanceiro_taxaBoleto=Informe o Plano de Contas e o Centro de Custos do lan\u00E7amento das taxas de Boleto
prt_Finan_Tipo_Conta_tituloForm=Tipo de Conta
prt_Finan_Tipo_Conta_descricao=Descri\u00E7\u00E3o
prt_Finan_Tipo_Conta_codigo=C\u00F3digo
prt_Finan_Tipo_Conta_tipoConta=Tipo de Conta
prt_Finan_Tipo_Conta_Comportamento=Comportamento
prt_Finan_Conta_Contabil_tituloForm=Conta Cont\u00E1bil
prt_Finan_Tipo_Documento_tituloForm=Tipo de Documento
prt_Finan_Tipo_Documento_descricao=Descri\u00E7\u00E3o\:
prt_Finan_Tipo_Documento_codigo=C\u00F3digo\:
prt_Finan_Tipo_Documento_tipoDocumento=Tipo de Documento\:
prt_FechamentoAcessos_TituloForm=Fechamento de Controle de Acessos de Catraca
prt_FechamentoAcessos_empresa=Empresa\:
prt_FechamentoAcessos_PeriodoAcesso=Per\u00EDodo\:
prt_CRM_Organizador=Filtrar apenas na carteira
prt_ExpandirParcelas=Mostrar parcelas
prt_OcultarParcelas=Ocultar parcelas
prt_HistoricoCheque=Hist\u00F3rico do Cheque
prt_HistoricoCartao=Hist\u00F3rico de Movimenta\u00E7\u00F5es de Cart\u00E3o
operacoes.atualizar=Atualizar
operacoes.atualizar.bd.verificar=Verificar atualiza\u00E7\u00F5es de Banco de Dados dispon\u00EDveis
operacoes.atualizar.bd=Atualizar Banco de Dados
operacoes.atualizar.bd.executado=Atualiza\u00E7\u00F5es de Banco de Dados executadas
operacoes.consultar=Consultar
operacoes.todos=--Todos--
entidade.atualizacao.versao=Vers\u00E3o
entidade.atualizacao.script=Script
entidade.atualizacao.resultado=Resultado
entidade.atualizacao.mensagem=Mensagem
entidade.atualizacao.stackTrace=StackTrace
entidade.atualizacao.data=Data
entidade.atualizacao.descricao=Descri\u00E7\u00E3o
entidade.atualizacao.usuario=Usu\u00E1rio
entidade.dataDe=Per\u00EDodo\: de
entidade.dataAte=at\u00E9
legenda_agenda_falta_justificada=Existiu um contato com o aluno no qual ele explicou a falta, agora o cliente est\u00E1 com o cr\u00E9dito deste dia para ser agendado em uma data futura.
legenda_agenda_falta_semDebito=Existiu um contato com o aluno no qual ele explicou a falta, agora o cliente deseja que seja exclu\u00EDdo o d\u00E9bito gerado.
legenda_agenda_falta=O cliente n\u00E3o veio e n\u00E3o ligou para remarcar ou justificar a falta, neste caso o profissional ficou esperando e perdeu seu hor\u00E1rio.
legenda_agenda_ministrada=Significa que o servi\u00E7o foi realizado. A sess\u00E3o foi ministrada pelo profissional nesta data e hora.
legenda_agenda_sessao_confirmada=Teve um contato com o cliente e ele confirmou que vir\u00E1 neste dia e hora marcada.
legenda_agenda_sessao_remarcada=O agendamento foi remarcado para esta data.
legenda_agenda_sem_definicao=Sem Status, ainda n\u00E3o teve a\u00E7\u00E3o ap\u00F3s o agendamento.
socialMailing_mensagens=Mensagens
socialMailing_grupoConversa=Grupo de Conversa
socialMailing_contatos=Contatos
socialMailing_resultadoBusca=Resultado da Busca
gestaoRecebiveis_recebidosZW=Marque aqui para considerar que as movimenta\u00E7\u00F5es com receb\u00EDveis que voc\u00EA ir\u00E1 realizar s\u00E3o do caixa da academia para o departamento financeiro. Essas movimenta\u00E7\u00F5es ser\u00E3o apresentadas no relat\u00F3rio de Movimenta\u00E7\u00F5es Financeiras.
gestaoRecebiveis_origem=Movimenta\u00E7\u00F5es a serem realizadas tem origem no ZillyonWeb?
sim=Sim
nao=N\u00E3o
prt_Finan_AlteracaoDataCompensacao=Altera\u00E7\u00E3o em Data de Compensa\u00E7\u00E3o de Cart\u00E3o de Cr\u00E9dito
prt_Finan_GestaoLotes=Gest\u00E3o de Lotes
prt_mailing_agendamento_Iniciar=Iniciar
prt_mailing_agendamento_RepetirCada=Repetir a cada
prt_mailing_agendamento_semanasEm=semana em
prt_mailing_agendamento_faixa=Faixa de hor\u00E1rio
prt_mailing_agendamento_ate=at\u00E9
prt_mailing_agendamento_meses=Meses
prt_mailing_agendamento_dia=Dias
prt_mailing_agendamento_diad=dia
prt_mailing_agendamento_mes=m\u00EAs
prt_mailing_agendamento_todosDias=Todos os dias
prt_mailing_agendamento_todosDiasSemana=Todos os dias da semana
prt_mailing_agendamento_todosMeses=Todos os meses
prt_Relatorio_situacao_ativo=Ativo
prt_Relatorio_situacao_inativo=Inativo
prt_Relatorio_situacao_visitante=Visitante
prt_Relatorio_situacao_trancado=Trancado
prt_Relatorio_situacao_desitente=Desist\u00EAncia
prt_Relatorio_situacao_vencido=Vencidos
prt_Relatorio_situacao_avencer=A Vencer
prt_Relatorio_situacao_cancelado=Cancelados
prt_Relatorio_situacao_atestado=Atestado
prt_Relatorio_situacao_ferias=F\u00E9rias
prt_Relatorio_situacao_gympas=Gympass
prt_Relatorio_situacao_normal=Normal
prt_Relatorio_situacao_dependente=Dependente
prt_finan_maisdeumcaixa=Este usu\u00E1rio possui mais de um caixa em aberto. Qual deles deseja fechar?
prt_finan_chequesSeremRetirados=Cheques a serem retirados
prt_finan_Pagador=Pagador
prt_finan_compensacao=Compensa\u00E7\u00E3o
prt_finan_chequesSeremExcluidos=Cheques a serem retirados
prt_finan_cartoesSeremExcluidos=Cart\u00F5es a serem retirados
prt_finan_acoesMassa=A\u00E7\u00F5es em massa
prt_finan_pagar_conjunto=Pagar com cheque
prt_finan_pagar_conjunto_lbl=Pagamento de contas em conjunto
prt_finan_pagos_conjunto_lbl=Pagos em conjunto
ptr_finan_Agendamento_DataVencimentoAviso=Ao informar a data da \u00FAltima parcela o sistema n\u00E3o gerar\u00E1 mais parcelas para esse agendamento. Data n\u00E3o pode ser alterada uma vez informada
prt_acesso_integracao_grupo=Integra\u00E7\u00E3o de Acesso em Grupo Empresarial
prt_acesso_integracao_url=URL
prt_acesso_integracao_chave=Chave
prt_acesso_empresa_remota=Empresa remota
prt_acesso_empresa_local=Empresa local
prt_acesso_integracao_localAcesso=Local de acesso
prt_acesso_integracao_coletor=Coletor
prt_acesso_integracao=Integra\u00E7\u00E3o de Acesso
prt_acesso_integracao_Terminal=Terminal
prt_acesso_autorizacao=Autoriza\u00E7\u00E3o de Acesso em Grupo Empresarial
prt_acesso_autorizacao_simples=Autoriza\u00E7\u00E3o de Acesso
prt_acesso_gerador_consultas=Gerador de Consultas
prt_acesso_autorizacao_integracao=Integra\u00E7\u00E3o
prt_acesso_autorizacao_tipo=Tipo
prt_acesso_autorizacao_aluno=Aluno
prt_acesso_autorizacao_colaborador=Colaborador
prt_acesso_autorizacao_nome=Nome
prt_acesso_autorizacao_codAcesso=C\u00F3d. Acesso
prt_acesso_autorizacao_codAcessoAlter=C\u00F3d. Acesso Alternativo
prt_acesso_autorizacao_codigoCol=C\u00F3d. Colaborador
prt_acesso_autorizacao_matricula=Matr\u00EDcula
prt_acesso_autorizacao_selecionarPessoa=Selecionar autorizado
prt_acesso_autorizacao_alterarPessoa=Alterar autorizado
prt_acesso_autorizacao_nenhumAluno=Nenhum aluno encontrado. Fa\u00E7a uma nova consulta.
prt_acesso_autorizacao_nenhumColaborador=Nenhum colaborador encontrado. Fa\u00E7a uma nova consulta.
prt_acesso_autorizacao_telefone=Telefone
prt_acesso_autorizacao_email=E-mail
prt_acesso_autorizacao_datanasc=Data Nascimento
prt_acesso_autorizacao_codAutorizacao=C\u00F3digo da Autoriza\u00E7\u00E3o
prt_acesso_autorizacao_comissaoParaConsultor=Comiss\u00E3o para Consultor
prt_acesso_autorizacao_senha=Senha Acesso Integra\u00E7\u00E3o
prt_acesso_autorizacao_alterarSenha=Alterar Senha
prt_acesso_autorizacao_incluirSenha=Cadastrar Senha
prt_acesso_autorizacao_definirSenha=Definir Senha Acesso
prt_acesso_autorizacao_cpf=CPF
prt_acesso_autorizacao_possuiBiometriaFacial=Possui biometria facial
prt_acesso_autorizacao_possuiBiometriaDigital=Possui biometria digital
prt_acesso_autorizacao_sim=Sim
prt_acesso_autorizacao_nao=N\u00E3o
prt_correspondenciaZD=Correspond\u00EAncia ZD
prt_correspondenciaZDInfo=Informe os identificadores dos planos no ZD separados por ';'
prt_contasApresentarBI=Contas
prt_contasMarcarTodos=Marcar/Desmarcar Todos
prt_contasDesmarcarTodos=Desmarcar Todos
prt_bloquearProdutosEmAbertoCategoria=Bloquear acesso com parcela vencida em aberto
prt_pagamentoconjuntodiferentevalor=No pagamento em conjunto o valor da quita\u00E7\u00E3o deve sempre ser igual a soma das contas a pagar selecionadas.
prt_Produto_prevalecerVigenciaContrato=Prevalecer vig\u00EAncia do contrato\:
prt_Produto_prevalecerVigenciaContratoExplain=Se este produto for vendido dentro da negocia\u00E7\u00E3o de um contrato, esta propriedade vai dizer se a vig\u00EAncia do produto vai estar associada ao contrato ou ao n\u00FAmero de dias aqui cadastrados.
prt_ignorarremessasemretorno=Ignorar parcelas com remessa sem retorno
prt_mostrarNrtentativasMotivoRetorno=Apresentar Nr Tentativas e Motivo Retorno
prt_PlanoRecorrencia_permitecobrarAdesaoSeparada=Permite cobrar ades\u00E3o separada
prt_PlanoRecorrencia_naoCobrarAnuidadeProporcional=N\u00E3o cobrar anuidade proporcional
prt_PlanoRecorrencia_cobrarAdesaoSeparada=Cobrar ades\u00E3o separada
prt_PlanoRecorrencia_Nrvezesparcelar=M\u00E1ximo vezes parcelar ades\u00E3o
prt_PlanoRecorrencia_NrvezesparcelarNeg=Nr. vezes parcelar ades\u00E3o
prt_Plano_permitecobrarProdutoSeparado=Permite cobrar produtos separado
prt_Plano_permiteCobrarMatriculaSeparada=Permite cobrar Matr\u00EDcula ou Rematr\u00EDcula separada
prt_Plano_NrVezesParcelarMatricula=M\u00E1ximo vezes parcelar Matr\u00EDcula ou Rematr\u00EDcula
prt_Plano_NrVezesParcelarProduto=M\u00E1ximo vezes parcelar produto
prt_acoes_remessas_config=Configurar a\u00E7\u00F5es para cada status
prt_acoes_remessas_config_btn=Configurar a\u00E7\u00F5es
prt_acoes_remessas_config_codigo_status=C\u00F3digo status
prt_acoes_remessas_config_acao=A\u00E7\u00E3o associada
prt_acoes_remessas_repescagem_automatica=Repescagem Autom\u00E1tica
prt_acoes_remessas_dias_repescagem_automatica=Dias
prt_acoes_remessas_tentativas_repescagem_automatica=Tentativas
prt_acoes_remessas_reagendarParcelasAutomaticamente=Repescagem autom\u00E1tica
prt_acoes_remessas_qtdTentativasParaReagendamentoAutomatico=M\u00E1x tentativas para Repescagem(0 \= desabilitado)
prt_acoes_remessas_qtdDiasParaReagendamentoAutomatico=Qtd dias para Repescagem(0 \= desabilitado)
prt_acoes_remessas_config_semacao=Sem a\u00E7\u00E3o definida
agrupar=Agrupar por a\u00E7\u00F5es
mostrarParcelasComBoleto=Mostrar parcelas com boleto
prt_Lancamento_produto_coletivo_tituloForm=Lan\u00E7amento de produto coletivo
prt_descricao=Descri\u00E7\u00E3o
prt_data_lancamento=Lan\u00E7amento
prt_desconsiderarDataTurmas=Desconsiderar datas das turmas
prt_retrocederValorMensalPlanoCancelamento=Retroceder ao valor do plano mensal no c\u00E1lculo do cancelamento com devolu\u00E7\u00E3o
prt_numero_telefone_Cielo=N\u00FAmero de telefone da Cielo
prt_nomenclatura_venda_credito=Nomenclatura utilizada para planos Venda Cr\u00E9dito de Treino
prt_nome_sobrenome=Nome e sobrenome\:
prt_CalculoCancelamentoAntecedencia=Utilizar C\u00E1lculo de cancelamento com anteced\u00EAncia para vencimento de sua pr\u00F3xima parcela
prt_arredondamento_parcelas=Arredondar
prt_arredondamento_parcela_opcoes=Op\u00E7\u00F5es de valor de parcelas\:
prt_Colaborador_usoCreditoPersonal=Uso dos cr\u00E9ditos de personal\:
prt_ConfiguracaoSistema_minimoVencimento2parcela=Vencimento da segunda parcela somente ap\u00F3s nr. de dias de pr\u00F3-rata ou mais (recorr\u00EAncia)
prt_ConfiguracaoSistema_usarnomeresponsavelnanota=Emitir nota fiscal no nome e CPF do respons\u00E1vel caso aluno seja menor de 18 anos
prt_ConfiguracaoSistema_emiteCarteirinha=Habilitar impress\u00E3o Carteirinha
prt_ConfiguracaoSistema_limiteConvite=Quantidade m\u00E1xima de convites de aula experimental que uma pessoa pode receber
prt_ConfiguracaoSistema_Incluir_Visitante_Nav_Bar=Mostrar bot\u00E3o "Incluir visitante" ao inv\u00E9s de "Incluir cliente" no menu
DIASCARENCIA=Dias de f\u00E9rias do contrato
prt_Agendamentos_CRM_tituloForm=Relat\u00F3rio de Agendamentos
prt_contatos_app_tituloForm=Contatos via APP
prt_cadastrarArmario=Cadastrar arm\u00E1rio
prt_admin_Armario=Cadastrar de arm\u00E1rios
prt_Armario=Arm\u00E1rio
prt_editar_Armario=Editar arm\u00E1rio
prt_alugar_Armario=Aluguel de arm\u00E1rio
prt_trocar_Armario=Troca de arm\u00E1rio
prt_gravar_Armario=Gravar arm\u00E1rio
prt_abrir_Armario=Abrir arm\u00E1rio
prt_abrir_telaCaixa=ir Tela Caixa
prt_inserir_Armario=Incluir arm\u00E1rios
prt_a=a
prt_tamanho=Tamanho
prt_tamanhos=Tamanhos
prt_grupo=Grupo
prt_aberto=Abertos
prt_fechado=Fechados
prt_grupos=Grupos
prt_inicio=In\u00EDcio
prt_fim=Fim
prt_finalizarVenda=Finalizar Venda
prt_finalizarEditarVenda=Salvar Aluguel
prt_trocarArmario=Trocar Arm\u00E1rio
prt_historicoArmario=Hist\u00F3rico do arm\u00E1rio
prt_primeira_parcela=Data vencimento
prt_contratoAssinado=Contrato Assinado
prt_nrVezes_Parcelar=N\u00FAmero de parcelas
prt_situacaoArmarios=Situa\u00E7\u00E3o
prt_Modalidade_usa_treino=Usa treino
prt_Modalidade_crossfit=Cross
prt_Modalidade_imagem=Imagem
prt_negociacao_contrato=Negocia\u00E7\u00E3o
prt_sorteio_titulo=Sorteio
prt_sorteio_ultimosSorteios=\u00DAltimos Sorteios
prt_sorteio_configuracoes=Configura\u00E7\u00F5es dos Sorteios
prt_BICRM_fase=Fase
prt_BICRM_atingida=Atingida
prt_MeusDados_tituloForm=Meus Dados
prt_Importacao_tituloForm=Importa\u00E7\u00E3o
prt_Importacao_Produto_tituloForm=Importa\u00E7\u00E3o de Produtos
prt_clientes=Clientes
prt_LogAjusteGeral_tituloForm=Log Ajustes Gerais
prt_Indicador_Acesso=Relat\u00F3rio Indicador de Acessos
ATUALIZAR=Atualizar
PROFESSOR=Professor
PROFESSORES=Professores
COMISSAO=Comiss\u00E3o
PARCELA=Parcela
PAGAMENTO=Pagamento
TRANSFERENCIA_SAIDA=Transfer\u00EAncia Sa\u00EDda
ALTERACAO_DURACAO=Altera\u00E7\u00E3o Dura\u00E7\u00E3o
BONUS_ACRESCIMO=B\u00F4nus - Acr\u00E9scimo
BONUS_REDUCAO=B\u00F4nus - Redu\u00E7\u00E3o
BONUS_COLETIVO=B\u00F4nus Coletivo
LIBERAR_VAGA=Liberar Vaga
CARENCIA=F\u00E9rias
TRANSFERENCIA_ENTRADA=Transfer\u00EAncia Entrada
CANCELAMENTO=Cancelamento
ALTERACAO_HORARIO=Altera\u00E7\u00E3o de Hor\u00E1rio
TRANCAMENTO=Trancamento
TRANCAMENTO_VENCIDO=Trancamento vencido
RETORNO_TRANCAMENTO=Retorno de trancamento
INCLUIR_MODALIDADE=Incluir modalidade
ALTERAR_MODALIDADE=Alterar modalidade
EXCLUIR_MODALIDADE=Excluir modalidade
ALTERACAO_CONTRATO=Altera\u00E7\u00E3o de contrato
ATESTADO=Atestado
RETORNO_ATESTADO=Retorno de atestado
CONTATO=Contato
CONTATO_TELEFONE=Contato telef\u00F4nico
CONTATO_EMAIL=E-mail
CONTATO_PESSOAL=Contato pessoal
LIGACAO_SEM_CONTATO=Liga\u00E7\u00E3o sem contato
CONTATO_SMS=SMS
CONTATO_APP=Contato via APP
VENCIMENTO=Vencimento
RECEBER=Receber
RECEBIMENTO=Recebimento
TOTAL=Total
CONSULTA=Consulta
OBSERVACAO=Observa\u00E7\u00E3o
OBSERVACOES=Observa\u00E7\u00F5es
CONSULTAR=Consultar
ANALITICO=Anal\u00EDtico
ALUNO=Aluno
DETALHAMENTO=Detalhamento
MODALIDADE=Modalidade
NOME=Nome
NOME_RESPONSAVEL=Nome Respons\u00E1vel
Nome_Responsavel_Emissao = Respons\u00E1vel pela emiss\u00E3o
NOME_MAISC=NOME
TURMA=Turma
TURMAS=Turmas
OCULTAR=Ocultar
CTR=Ctr
FILTRAR=Filtrar
IMPRIMIR=Imprimir
FILTROS=Filtros
EXPANDIR=Expandir
TODOS=Todos
TODOS_MAISC=TODOS
TOTAL_LANCADO=Total Lan\u00E7ado
SELECIONAR=Selecionar
QUANTIDADE=Quantidade
CANCELAR=Cancelar
HORARIO=Hor\u00E1rio
ANO=Ano
MES=M\u00EAs
DIA=Dia
DIAS=Dias
DIARIA=Di\u00E1ria
MESES=Meses
CLIENTE=Cliente
CLIENTES=Clientes
CLIENTE_MAISC=CLIENTE
PRODUTO=Produto
PRODUTO_MAIUSC=PRODUTO
DATA_LACAMENTO=Data Lan\u00E7amento
DATA_LACAMENTO_MAIUSC=DATA LAN\u00C7AMENTO
DATA_INICI=Data \u00CDnicio
DATA_INCI_MAISC=DATA \u00CDNICIO
DESCONTO=Desconto
DESCONTO_MAIUSC=DESCONTO
DATA_INICIO=Data In\u00EDcio
DATA_INICIO_MAIUSC=DATA IN\u00CDCIO
FREE_PASS=FreePass
CONFIRMAR=Confirmar
OPCOES=Op\u00E7\u00F5es
TRANSACOES=Transa\u00E7\u00F5es
REPESCAGEM=Repescagem
TODAS=Todas
PENDENTE=Pendente
DESCRICAO=Descri\u00E7\u00E3o
DESCRICAO_MAISC=DESCRI\u00C7\u00C3O
DATA_AULA=Data Aula
DATA_OPERACAO=Dt. Opera\u00E7\u00E3o
EMPRESA=Empresa
EMPRESA_MAISC=EMPRESA
PERIODO=Per\u00EDodo
COMPETENCIA=Compet\u00EAncia
DURACAO=Dura\u00E7\u00E3o
MATRICULA=Matr\u00EDcula
REMATRICULA=Rematr\u00EDcula
RENOVACAO=Renova\u00E7\u00E3o
DETALHADO=Detalhado
NIVEL=N\u00EDvel
AMBIENTE=Ambiente
CONFIRMACAO=Confirma\u00E7\u00E3o
CONTINUAR=Continuar
CONTRATO=Contrato
CODIGO=C\u00F3digo
USUARIO=\u00DAsuario
SENHA=Senha
SITUACAO=Situa\u00E7\u00E3o
PAGO=Pago
VENCIDO=Vencido
LIVRE=Livre
VALOR=Valor
LEGENDA=Legenda
NEGOCIADO=Negociado
PERSONAL=Personal
PROXIMO=Pr\u00F3ximo
FATURAMENTO=Faturamento
ANTERIOR=Anterior
MENSAL=Mensal
MENSAL_MAISC=MENSAL
ANUAL=Anual
ANUAL_MAISC=ANUAL
SECAO=Se\u00E7\u00E3o
COLABORADOR=Colaborador
COLABORADOR_MAISC=COLABORADOR
CLASSIFICACAO=Classifica\u00E7\u00E3o
CIDADE=Cidade
QUESTIONARIO=Question\u00E1rio
PROFISSAO=Profiss\u00E3o
PARENTESCO=Parentesco
PERGUNTA=Pergunta
PAIS=Pa\u00EDs
BRINDE=Brinde
COLABORADORES=Colaboradores
CATEGORIA=Categoria
ESTADO=Estado
PLANO=Plano
PACOTE=Pacote
COMPRA=Compra
CARDEX=Cardex
DATA=Data
OPERACAO=Opera\u00E7\u00E3o
ENTRADA=Entrada
SAIDA=Sa\u00EDda
ATIVO=Ativo
INATIVO=Inativo
EXATAMENTE=Exatamente
VIGENTE=Vigente
SESSAO=Sess\u00E3o
EMITIDA=Emitida
RETORNO=Retorno
LOG=Log
FECHAR=Fechar
ENVIAR=Enviar
TOTALIZADOR=Totalizador
ADICIONAR=Adicionar
PRONTO=Pronto
ENVIANDO=Enviando
PARAR=Parar
COD=COD
ANO_MES=ANO/M\u00CAS
META_MAISC=META
DUR=DUR.
SIT=SIT.
VL_FIXO_ESP=VL. FIXO ESP.
VL_FIXO_AGEN=VL. FIXO AGEN.
PORC_ESP=PORC. ESP.
PORC_AGEN=PORC. AGEN.
VIG_INICIO=VIG. IN\u00CDCIO
VIG_FINAL=VIG. FINAL
STATUS=Status
RELATORIOS=Relat\u00F3rios
CONSULTOR=Consultor
CONSULTORES=Consultores
LIMPAR=Limpar
TELEFONE=Telefone
CADASTRO=Cadastro
NASCIMENTO=Nascimento
JUSTIFICATIVA=Justificativa
JUSTIFICAR=Justificar

EDITAR=Editar
ITENS=Itens
SEXO=Sexo biol\u00F3gico
PESSOA=Pessoa
ACESSO=Acesso
JUSTIFICADO=Justificado
REGISTROS=Registros
RUIM=Ruim
REGULAR=Regular
BOM=Bom
OTIMO=\u00D3timo
LOCAL=Local
COLETOR=Coletor
SENTIDO=Sentido
TIPO=Tipo
TIPO_MAISC=TIPO
ENDERECO=Endere\u00E7o
prt_Cidade_codMun=C\u00F3digo Mun. IBGE
prt_tela_crossfit_desabilitado_modalidade=A configura\u00E7\u00E3o Cross n\u00E3o pode ser alterada pois j\u00E1 existem alunos utilizando esta modalidade.
prt_aplicar_contratos_lancados=Aplicar \u00E0 contratos j\u00E1 lan\u00E7ados
prt_aplicar_texto_padrao_contratos_lancados_explica=Clicando neste link, voc\u00EA altera o texto dos contratos j\u00E1 lan\u00E7ados e ativos com este plano. Ser\u00E1 gerado um log dessa a\u00E7\u00E3o.
prt_aplicar_quantidade_maxima_frequencia_contratos_lancados_explica=Clicando neste link, voc\u00EA ir\u00E1 alterar a quantidade m\u00E1xima de frequ\u00EAncia semanal permitida para os contratos j\u00E1 lan\u00E7ados com este plano. Ser\u00E1 gerado um log dessa a\u00E7\u00E3o.
prt_Pessoa_nomeAnimalEstimacao=Nome Animal de Estima\u00E7\u00E3o
prt_escolha_empresa_logar=Escolha uma empresa para logar
prt_tela_ini_texto_1=Para utiliza\u00E7\u00E3o do Pacto Zillyon Web, tenha em m\u00E3os a sua chave de acesso fornecida pela Pacto Solu\u00E7\u00F5es. Ela ser\u00E1 necess\u00E1ria algumas vezes durante o dia para utiliza\u00E7\u00E3o desse software.
prt_tela_ini_texto_2=Recomendamos que uma vez conhecida sua chave, voc\u00EA poder\u00E1 adicionar ao endere\u00E7o (URL) a sua chave de acesso e, posteriormente grav\u00E1-lo nos favoritos de seu navegador.
prt_data_sistema_dois_pontos=Data sistema\:
prt_chave_dois_pontos=Chave\:
prt_info_cod_verif=Inforque o c\u00F3digo de verifica\u00E7\u00E3o
prt_dias_para_bloqueio=Dias para bloqueio
prt_alerta_para_bloqueio=Alerta de bloqueio
prt_alerta_para_bloqueio_parcial=Alerta de Bloqueio Parcial
prt_clique_aqui_recu_senha=Clique aqui para recuperar sua senha
prt_carregando_tres_pontos=Carregando...
prt_favor_slc_modulo_logar_dois_pontos=Por favor, selecione o m\u00F3dulo que voc\u00EA deseja logar\:
prt_busque_administre=Busque e Administre
prt_suas=suas
prt_notas_fiscais=Notas Fiscais
prt_com_facilidade=com facilidade
prt_usuario_perf_acesso=O usu\u00E1rio informado n\u00E3o possui perfil de acesso.
prt_nao_emp_logada=N\u00E3o h\u00E1 nenhuma empresa logada.
prt_email_invalido=E-mail inv\u00E1lido\!
prt_senha_prox_expirar=Sua senha est\u00E1 pr\u00F3xima de expirar.
prt_clic_alterar_senha=Clique aqui para alterar sua senha
prt_exp_de_senha=Expira\u00E7\u00E3o de Senha
prt_assinatura_digital=Assinatura Digital
prt_itens_obrig=Itens obrigat\u00F3rios\:
ptr_foto_aluno=Foto do aluno
prt_cmp_endereco=Comprovante de endere\u00E7o
prt_documentos=Documentos
prt_atest_apt_fisic=Atestado de aptid\u00E3o f\u00EDsica
prt_aten_popup_bloq=Aten\u00E7\u00E3o\! Pop-up bloqueado.
prt_para_sua_seg_a_sessa_fina=Para sua seguran\u00E7a, sua sess\u00E3o finalizar\u00E1 em..
prt_retm_min_sessao=Retornar minha sess\u00E3o
prt_incluir_cliente=Incluir Cliente
prt_business_itelligence=Business Intelligence
prt_Bi_cobrancaRecorrencia_aReceber=A Receber
prt_Bi_cobrancaRecorrencia_recebido=Recebido
prt_Bi_cobrancaRecorrencia_deInadimplencia=de inadimpl\u00EAncia
prt_Bi_cobrancaRecorrencia_inadimplencia=Inadimpl\u00EAncia(%)
prt_Bi_icv_matricula=Matr\u00EDcula
prt_Bi_icv_bv=BV
prt_Bi_ticketmedio_competencia=Compet\u00EAncia
prt_Bi_ticketmedio_despesas=Despesas
prt_Bi_ticketmedio_receita=Receita
prt_agenda=Agenda
prt_categoria=Categoria
prt_matricula=Matr\u00EDcula
prt_matricula_dois_pontos=Matr\u00EDcula\:
prt_diferc_gente_envolve=A diferen\u00E7a \u00E9 que a gente se envolve.
prt_todos_dirt_reserv=Copyright 2008-2018 \u00A9     Todos os direitos reservados
prt_wiki_versao_atual=Wiki\:Vers\u00E3o Atual
prt_foto_pedente=Voc\u00EA possui foto pendente
prt_nao_exbr_novamente=N\u00E3o exibir novamente
prt_nao_mostr_avis_novament=N\u00E3o mostrar este aviso novamente
prt_caixa_aberto=Caixa em Aberto
prt_wiki_caixa_aberto=Wiki\: Caixa em Aberto
prt_conf_canc_parcelas=Confirma\u00E7\u00E3o de Cancelamento de Parcelas
prt_justf_cancelamento=Justificativa para o Cancelamento
prt_justificativa=Justificativa
prt_justificativa_dois_pontos=Justificativa\:
prt_filtros_pesquisa=Filtros de Pesquisa
prt_consult_todos_hoje=Consultar todos de Hoje
prt_consult_todos_semana=Consultar todos da semana
prt_incluir_parc_recorr=Incluir parcelas Recorr\u00EAncia
prt_ordenar_dt_lancamento=Ordenar por data de lan\u00E7amento
prt_ordem_alfabetica=Ordem Alfab\u00E9tica
prt_nao_apres_venc_messes_futuros=N\u00E3o apresentar vencimentos de meses futuros
prt_cliente=Cliente
prt_cliente_dois_pontos=Cliente\:
prt_periodo_venc_parc_dois_pontos=Per\u00EDodo de Vencimento de Parcelas\:
prt_buscar_parcelas=Buscar Parcelas
prt_limpar_busca_result_parc=Limpar Busca de Resultado de Parcelas
prt_limpar_busca=Limpar Busca
prt_resp_pagmt_dois_pontos=Respons\u00E1vel Pagamento\:
prt_total_final_cxalta=TOTAL FINAL\:
prt_sifrao_dinheiro=R$
prt_click_exibir_parc_aberto=Clique para exibir as parcelas em aberto
prt_traco_boleto_bancario=- (Boleto Banc\u00E1rio)
prt_data_lacamento=Data de Lan\u00E7amento
prt_num_vezes=Nr. vezes
prt_renegociar_parc=Renegociar parcelas
prt_imprimir_contrato=Imprimir o Contrato
prt_vizual_clinte=Visualizar o Cliente
prt_respons_pagamnt=Respons\u00E1vel Pagamento\:
prt_cancelar_parcelas=Cancelar Parcelas
prt_parcelas_selecionadas=Parcelas Selecionadas
prt_nr_pessoa_parcela_selecionadas=Pessoa - Nr. Parcelas Selecionadas
prt_gerador_boleto=Gerar Boleto
prt_venda_avulsa=Venda Avulsa
prt_conrfirmacao_venda=Confirma\u00E7\u00E3o da Venda
prt_codigo_dois_pontos=C\u00F3digo\:
prt_usuario_dois_pontos=Usu\u00E1rio\:
prt_senha_dois_pontos=Senha\:
prt_consulta_desconto_produto=Consulta de Desconto Para Produto
prt_responder_boletim_visita=Responder Boletim de Visita
prt_pacotes_credito_personal=Pacotes de cr\u00E9dito para personal
prt_valor_pre_pago=Valor Pr\u00E9-Pago
prt_valor_pos_pago=Valor P\u00F3s-Pago
prt_wiki_diaria=Wiki\: Di\u00E1ria
prt_editar_data_lacamento=Editar data de lan\u00E7amento
prt_aplicar_data=Aplicar Data
prt_limpar_alteracao=Limpar Altera\u00E7\u00E3o
prt_limpar_aluno=Limpar Aluno
prt_limpar_modalidade=Limpar modalidade
prt_limpar_produto=Limpar produto
prt_aplicar_novo_valor=Aplicar Novo Valor
prt_inf_valor_desconto=Informar Valor do Desconto
prt_confirm_free_pass=Confirma\u00E7\u00E3o de FreePass
prt_wiki_free_pass=Wiki\: FreePass
prt_deletar_freepass_existente=Deletar FreePass Existente
prt_visualizar_logs=Visualizar logs
prt_gestao_transacoes=Gest\u00E3o de Transa\u00E7\u00F5es
prt_wiki_gestao_transacoes=Wiki\: Gest\u00E3o de Transa\u00E7\u00F5es
prt_de_dois_pontos=De\:
prt_ate_dois_pontos=At\u00E9\:
prt_gerar_relt_pdf=Gerar relat\u00F3rio em PDF
prt_gerar_relt_excel=Gerar relat\u00F3rio em Excel
prt_digite_algo_filtrar=Digite algo para filtrar...
prt_registrados_por_pagina=Registros por p\u00E1gina
prt_itens_por_pagina=itens por p\u00E1gina
prt_transacoes_cobabradas_indentif_col=Transa\u00E7\u00F5es cobradas s\u00E3o aquelas que possuem o n\u00FAmero identificador na coluna "Transa\u00E7\u00E3o"
prt_atencao_dois_pontos=Aten\u00E7\u00E3o\:
prt_transacoes_passiveis_de_tarifacao=transa\u00E7\u00F5es est\u00E3o pass\u00EDveis de tarifa\u00E7\u00E3o.
prt_num_transacoes=N\u00BA Transa\u00E7\u00F5es
prt_transacoes_em=Transa\u00E7\u00F5es em
prt_parcelas_em=Parcelas em
prt_intervalo_data_dois_pontos=Intervalo de Data\:
prt_situacao_dois_pontos=Situa\u00E7\u00E3o\:
prt_comunicacao_adm_dados_invalidos=Erro de comunica\u00E7\u00E3o com a Administradora ou Dados Inv\u00E1lidos.
prt_aprovada_porem_nao_capturada=Aprovada, por\u00E9m n\u00E3o capturada (confirmada).
prt_nao_aprovada_prob_cartao=N\u00E3o aprovada por problemas com o Cart\u00E3o.
prt_aprovada_confirm_sucess=Aprovada e Confirmada com sucesso.
prt_erro_durante_conf_aprovacao=Erro durante confirma\u00E7\u00E3o da aprova\u00E7\u00E3o.
prt_cancelada_zillyon_cielo=Cancelada no ZillyonWeb e na Cielo.
prt_desconsiderada_zillyon=Desconsiderada pelo ZillyonWeb, alguma pend\u00EAncia j\u00E1 foi resolvida por um Usu\u00E1rio.
prt_pagamento_zillyon_estornado=Pagamento no ZillyonWeb estornado. Por\u00E9m, precisa-se ligar na Cielo para estornar manualmente.
prt_gestao_remessas=Gest\u00E3o de Remessas
prt_wiki_gestao_remessas=Wiki\: Gest\u00E3o de Remessas
prt_saldo_transacoes=Saldo de Transa\u00E7\u00F5es
prt_wiki_saldo_doc=Wiki\: Saldo DCC
prt_consultar_gerar_remessa=Consultar / Gerar Remessa
prt_processar_retorno=Processar Retorno
prt_empresa_convcobranca_plano=Empresa/Conv\u00EAnio Cobran\u00E7a/Plano
prt_empresa_convcobraca=Empresa/Conv\u00EAnio Cobran\u00E7a
prt_convenio_cobranca_dois_pontos=Conv\u00EAnio Cobran\u00E7a\:
prt_plano_dois_pontos=Plano\:
prt_limpar_plano=Limpar Plano
prt_tipos_parcelas=Tipos de Parcelas
prt_procss_arq_retorno=Processamento Arquivo de Retorno
prt_kb_carregados={_KB}KB de {KB}KB carregados --- {mm}\:{ss}
prt_tag_arq_retorno=<b>Arquivo de retorno\:</b>
prt_tag_data_previst_cred=<b>Data Prevista Cr\u00E9dito\:</b>
prt_tag_quant_itens=<b>Quantidade de itens\:</b>
prt_tag_valor_total_arqv=<b>Valor Total do Arquivo\:</b>
prt_tag_total_baixar_duplic=<b>Total A Baixar em Duplicidade\:</b>
prt_tag_baixar_valor_menor=<b>Total A Baixar com Valor Menor\:</b>
prt_tag_baixar_valor_maior=<b>Total A Baixar com Valor Maior\:</b>
prt_tag_baixar_form_normal=<b>Total A Baixar de forma Normal\:</b>
prt_tag_baixar_form_manual=<b>Total J\u00E1 Baixado de forma Manual\:</b>
prt_criar_novaremessa=Criar nova remessa
prt_gestaoremessas=GestaoRemessas
prt_relatorio_gestao_remessas=Relat\u00F3rio Gest\u00E3o de Remessas
bi_aula_experimental=Aulas experimentais
bi_aula_experimental_wiki=Wiki\: BI - Aulas experimentais
aulas_experimentais_agendadas=Agendadas
aulas_experimentais_executadas=Executadas
aulas_experimentais_convertidas=Convertidas
aulas_aconteceram=Agendamentos que j\u00E1 aconteceram
aulas_acontecer=Agendamentos que ir\u00E3o acontecer
somente_executadas=Somente executados
somente_convertidos=Somente convertidos
presente_aula=Presente na aula
prt_traco_periodo=- Per\u00EDodo\:
prt_traco_vencimento=- Vencimento\:
prt_traco_cobranca=- Cobran\u00E7a\:
prt_traco_plano=- Plano\:
prt_nao_informado=N\u00E3o informado
prt_senha_incorreta=senha incorreta\!
prt_data_geracao_remessa=Data de Gera\u00E7\u00E3o da Remessa
prt_vencimento_parcelas=Vencimento Parcelas
prt_data_pgmt_parcelas=Data de Pagamento das Parcelas
prt_gestao_armarios=Gest\u00E3o de Arm\u00E1rios
prt_wiki_gestao_armarios=Wiki\: Gest\u00E3o de Arm\u00E1rios
prt_odernacao_dois_prontos=Orderna\u00E7\u00E3o\:
prt_data_final_vigencia=Data final da vig\u00EAncia
prt_filtros_selecionados=Filtros Selecionados
prt_gestao_professor=Comiss\u00E3o para Professor
prt_wiki_gestao_professor=Wiki\: Comiss\u00E3o para Professor
prt_tipo_consulta=Tipo Consulta
prt_intervalo_consulta=Intervalo da Consulta
prt_filtro_professor=Filtrar Professores
prt_visualizar_impressao=Visualizar Impress\u00E3o
prt_exportar_excel=Exportar EXCEL
prt_digite_algo_filtar=Digite algo para filtrar...
prt_objetos_na_lista=objetos na gympassVO
prt_vl_pago=Vl. Pago
prt_id_pagamento=Id. Pagamento
prt_fracao_pg=Fra\u00E7\u00E3o Pg.
prt_inc_matricula=Inic. Matr\u00EDcula
prt_fim_matricula=Fim Matr\u00EDcula
prt_vl_comissao=Vl. Comiss\u00E3o
prt_registros_por_pagina=Registros por p\u00E1gina
prt_relatorio_gestao_professor=Relat\u00F3rio Comiss\u00E3o para Professor
prt_por_competencia=Por Compet\u00EAncia
prt_por_compensacao=Por Compensa\u00E7\u00E3o
prt_comissao_consultor=Comiss\u00E3o para consultor
prt_consulta_consultor=Consulta Consultor
prt_consulta_atendente=Consulta Atendente
prt_tipo_dos_dados=Tipo dos Dados
prt_somente_recebimento_partir=Somente recebimento a partir
prt_operador_responsavel=Operador Respons\u00E1vel
prt_responsavel_lancamento=Respons\u00E1vel Lan\u00E7amento
prt_responsavel_recebimento=Respons\u00E1vel Recebimento
prt_nome_consultor=Nome do consultor
prt_tipo_pagamento=Tipo do pagamento
prt_impressao_por=Impress\u00E3o por
prt_consultor_contrato=Consultor Contrato
prt_consultor_atual=Consultor Atual
prt_somente_operador=Somento Operador
prt_somente_responsavel_lancamento=Somente Respons\u00E1vel Lan\u00E7amento
prt_ira_trazer_comissao_agrp_consultor=Ir\u00E1 trazer a comiss\u00E3o agrupada pelos consultores do contrato (no caso de produtos, consultor do cliente na \u00E9poca da venda)
ptr_trara_relat_comissao_agrp_consultor=Trar\u00E1 o relat\u00F3rio de comiss\u00E3o agrupado pelo consultor atual do cliente
prt_agrp_relatorio_comissao_recibo=Agrupar\u00E1 o relat\u00F3rio de comiss\u00E3o por quem lan\u00E7ou o recibo no sistema
prt_agrp_relatorio_comissao_venda=Agrupar\u00E1 o relat\u00F3rio de comiss\u00E3o por quem lan\u00E7ou a venda no sistema
prt_modo_visualizacao=Modo de visualiza\u00E7\u00E3o
prt_totalizado_aluno=Totalizado por Aluno
prt_totalizado_duracao=Totalizado por Dura\u00E7\u00E3o
prt_por_porcentagem=Por porcentagem
prt_por_valor_fixo=Por valor fixo
prt_caso_emitir_comissao_produto=Caso queira emitir comiss\u00E3o para produtos, por favor v\u00E1 nas Configura\u00E7\u00F5es de Empresa.
prt_relatorio_comissoes_produto=Este relat\u00F3rio tamb\u00E9m apresentar\u00E1 comiss\u00F5es para Produtos.
prt_gerar_relatorio_pdf=Gerar Relat\u00F3rio(PDF)
prt_gestao_comissao=Gest\u00E3o da Comiss\u00E3o
prt_somente_consultores=Somente Consultores
prt_qualquer_duracao=Qualquer dura\u00E7\u00E3o
prt_operador_contrato=Operador Contrato
prt_operador_pagamento=Operador Pagamento
prt_contrato_produto=Contrato/Produto
prt_valor_comissoes=Valor de Comiss\u00F5es
prt_consulta_de=Consulta de
prt_consultar_turmas_inativas=CONSULTAR TURMAS INATIVAS
prt_ordernar_por=ORDENAR POR
prt_exportar_mapa_form_pdf=Exportar Mapa para o formato PDF
prt_exportar_form_excel=Exportar em Excel
prt_consultarturma=Consultar Turma
prt_exportar_form_pdf=Exportar em PDF
prt_id_turma=ID da Turma
prt_hora_inicio=Hora In\u00EDcio
prt_hora_fim=Hora Fim
prt_l_limite_v_vagas=L \= limite / V \= vagas
prt_dados_reposicao=Dados da Reposi\u00E7\u00E3o
prt_aula_desmarcada=Aula Desmarcada
prt_click_visualizar_alunos_matriculados=Clique para visualizar alunos matriculados nesta turma e nesta data
prt_aula_reposicao=Aula Reposi\u00E7\u00E3o
prt_dom=Dom
prt_seg=Seg
prt_ter=Ter
prt_qua=Qua
prt_qui=Qui
prt_sex=Sex
prt_sab=Sab
prt_identificador_turma=Idenficador Turma
prt_nome_professor=Nome Professor
prt_pedirSenhaCadastrarMaisBiometrias=Pedir senha para cadastrar mais de uma biometria
prt_solicitarJustificativaLiberacaoManual=Solicitar justificativa de acesso ao realizar libera\u00E7\u00E3o manual
prt_solicitarJustificativaLiberacaoManual_hint=Com esta configura\u00E7\u00E3o habilitada, todas as vezes que o colaborador realizar uma libera\u00E7\u00E3o manual de acesso, ser\u00E1 necess\u00E1rio adicionar uma justificativa.
prt_ignorarconsumocredito=Ignorar consumo de cr\u00E9dito de acessos realizados nesse local de acesso
prt_ignorarconsumocredito_hint=Com esta configura\u00E7\u00E3o habilitada, todos os acessos desse local de acesso n\uFFFDo contabilizar\uFFFDo no consumo de cr\uFFFDditos.
prt_relatorio_personal=Relat\u00F3rio de Personal
prt_pedirSenhaCadastrarMaisBiometrias_hint=Com esta configura\u00E7\u00E3o habilitada, todas as vezes que o colaborador for cadastrar a partir da segunda digital para um aluno, o sistema de acesso (ZillyonAcessoWeb) ir\u00E1 solicitar o usu\u00E1rio e senha desse colaborador. Ser\u00E1 validado a permiss\u00E3o "1.14 - Permitir cadastrar mais de uma digital para os clientes, com autentica\u00E7\u00E3o". Com esta configura\u00E7\u00E3o desabilitada, n\u00E3o ser\u00E1 solicitado usu\u00E1rio e senha no cadastro de biometria em nenhum momento.
prt_produtos_parcela=Produtos da Parcela
prt_wiki_gestao_personal_trainer=Wiki\: Gestao de Personal Trainer
prt_parcela_cliente_gerada=Parcela para este cliente est\u00E1 Gerada.
prt_parcela_cliente_paga=Parcela para este cliente est\u00E1 Paga.
prt_parcela_cliente_vencida=Parcela para este cliente est\u00E1 Vencida.
prt_aluno_nao_negociado_ainda=Aluno N\u00E3o Negociado Ainda.
prt_nao_ha_parcelas_personal=N\u00E3o h\u00E1 parcelas a serem geradas para este Personal.
prt_total_pago=Total Pago
prt_total_lancado=Total Lan\u00E7ado
prt_calculadora_economia=Calculadora de Economia
prt_desc_fator_zw=Descubra seu Fator ZW
prt_wiki_desc_fator_zw=Wiki\: Descubra seu Fator ZW
prt_calc_fator_zw=Calcule seu Fator ZW
prt_infor_taxas_operacao_cred_deb=Informe as taxas para opera\u00E7\u00E3o de cr\u00E9dito/d\u00E9bito em sua academia atualmente.
prt_escolha_periodo=Escolha um per\u00EDodo
prt_forma_pagamento=Forma de Pagamento
prt_verificar_faturamento=Verificar faturamento
msg_turma_aluno_exp=Com esta configura\u00E7\u00E3o marcada, o sistema ir\u00E1 permitir a inclus\u00E3o de alunos visitantes nessa turma atrav\u00E9s da agenda do AulaCheia
prt_movimentacao_financeira=Movimenta\u00E7\u00E3o Financeira
prt_custo_atual=Custo Atual
prt_visualizar_taxas=Visualizar taxas
prt_exibir_resultado_economia=Exibir Resultado da Economia
prt_veja_fator_zw_influencia_resultado=Veja como o Fator ZW influencia no resultado da sua academia
prt_custo_autal=Custo Atual
prt_valor_pago_operadora=Valor pago \u00E0 operadora
prt_com_sua_taxa_atual=com sua taxa atual
prt_taxa_pacto=Taxa PACTO
prt_valor_que_fator_zw=Valor que o Fator ZW
prt_economiza_sua_academia=economiza para sua academia
prt_eonomia_do_fator_zw=Economia do Fator ZW
prt_visualizar_faturamento=Visualizar Faturamento
prt_limpar_taxas_inseridos_manualmente=Limpa as taxas inseridos manualmente
prt_executar_novamente=Executar Novamente
prt_mes_corrente=M\u00EAs Corrente
prt_mes_passado=M\u00EAs Passado
prt_ultimos_6_meses=\u00DAltimos 6 meses
prt_data_sorteio=Data do Sorteio
prt_regras_sorteio=Regras do Sorteio
prt_cliente_vencedor_sorteio=CLIENTE VENCEDOR SORTEIO
prt_wiki_cadastros_auxiliares=Wiki\: Cadastros Auxiliares
prt_cadastros_auxiliares=Cadastros auxiliares
prt_produtos_ambientes=Produtos e Ambientes
prt_wiki_produto_plano_turma=Wiki\: Produtos, Planos e Turmas
prt_wiki_config_financeiras=Wiki\: Configura\u00E7\u00F5es Financeiras
prt_config_financeira=Config. Financeiras
prt_acesso_sistema=Acesso ao sistema
prt_wiki_acesso_sistema=Wiki\: Acesso ao Sistema
prt_config_contrato=Config. de contrato
prt_wiki_conf_contrato=Wiki\: Conf. de Contrato
prt_produtos_planos_turmas=Produtos, Planos e Turmas
prt_deverao_ser_cadastrados_campo_colaborador=Dever\u00E3o ser cadastrados, no campo cadastro de colaborador, todos os funcion\u00E1rios da academia.Dentre eles podem estar\: os consultores, os professores, os personais, os gerentes, os respons\u00E1veis pela limpeza, dentre outros.
prt_apesar_de_serem_incluos_dif_tipo=Apesar de serem inclusos em uma mesma tela, eles se diferenciar\u00E3o pelo seu TIPO, que dever\u00E1 ser definido na hora do cadastro.
prt_cadastro_colaborador_tipo_consultor=Se cadastrarmos um colaborador e este for definido como TIPO CONSULTOR, o sistema tratar\u00E1 este colaborador como um Consultor de Vendas para todos os procedimentos e opera\u00E7\u00F5es realizadas.
prt_grupo_com_desconto=Grupo com Desconto
prt_este_campo_ultilizado_def_desconto=Este campo ser\u00E1 utilizado quando algum o colaborador quiser definir um desconto que ser\u00E1 aplicado a um determinado grupo de pessoas, sem que este desconto seja caracterizado como Conv\u00EAnio de Desconto, j\u00E1 que este tipo de conv\u00EAnio \u00E9 fechado com algum estabelecimento.
prt_familia_quatro_pessoas_praticar_atv_fisc=Uma fam\u00EDlia composta por quatro pessoas resolveu praticar alguma atividade na mesma academia. O colaborador poder\u00E1 encaixar as quatro pessoas em um grupo com desconto.
prt_grupo_desconto_juntamente_plano=O grupo com desconto s\u00F3 poder\u00E1 ser utilizado juntamente com algum plano.
prt_atraves_classf_indetf_agr_caracteristicas=Atrav\u00E9s da classifica\u00E7\u00E3o voc\u00EA poder\u00E1 identificar e agrupar os alunos com caracter\u00EDsticas espec\u00EDficas. Dessa maneira, ser\u00E1 mais f\u00E1cil organizar as informa\u00E7\u00F5es e identificar oportunidades de neg\u00F3cios para os alunos de uma mesma classifica\u00E7\u00E3o. Voc\u00EA poder\u00E1 criar diversas classifica\u00E7\u00F5es, conforme as suas necessidades, sendo que uma \u00FAnica pessoa poder\u00E1 ter mais de uma Classifica\u00E7\u00E3o.
prt_agrp_alunos_prof_atle=Voc\u00EA poder\u00E1 agrupar todos os seus alunos com idade acima de 60 anos, classificando-os como alunos de TERCEIRA IDADE. Voc\u00EA tamb\u00E9m poder\u00E1 identificar seus atletas profissionais classificando-os como PROFISSIONAL.
prt_opcao_cadastr_cidade=Esta op\u00E7\u00E3o ser\u00E1 utilizada para efetuar o CADASTRO DE CIDADES. Fazendo isso, voc\u00EA facilitar\u00E1 o cadastro de clientes. Esta op\u00E7\u00E3o ser\u00E1 muito \u00FAtil quando o cliente n\u00E3o souber o seu pr\u00F3prio CEP.
prt_campo_cadastro_cliente_consulta_alunos=No campo de cadastro do cliente voc\u00EA poder\u00E1 consultar todos os dados dos alunos da academia. \u00C9 importante lembrar que a qualidade de seus relat\u00F3rios depende do qu\u00E3o completo est\u00E3o os dados do Cliente, por isso, ao realizar o cadastro dos alunos \u00E9 importante que sejam preenchidos, se n\u00E3o todos os campos, a maioria deles dos campos apresentados.
prt_definir_campos_importantes_pesquisa=Voc\u00EA tamb\u00E9m poder\u00E1 definir alguns campos (que julgar mais importantes para suas pesquisas) como obrigat\u00F3rios, assim voc\u00EA garantir\u00E1 que os mesmos ser\u00E3o sempre preenchidos na hora do cadastro, facilitando suas pesquisas e relat\u00F3rios.
prt_campo_quest_criar_boletim_visita=No campo de cadastro de QUESTION\u00C1RIOS poder\u00E3o ser criados v\u00E1rios Boletins de Visitas diferentes. O colaborador poder\u00E1 criar o question\u00E1rio de acordo com as necessidades da academia.
prt_forma_simpl_definir_perguntas=De forma simplificada o colaborador conseguir\u00E1 definir um nome e escolher as perguntas que far\u00E3o parte do seu boletim e pronto.
prt_campo_cads_prof_aluno=Neste campo voc\u00EA cadastrar\u00E1 a profiss\u00E3o do aluno. \u00C9 importante que o colaborador sempre mantenha seu CADASTRO DE PROFISS\u00C3O completo ao cadastrar novos alunos ou funcion\u00E1rios. Lembre-se, tamb\u00E9m, que voc\u00EA s\u00F3 conseguir\u00E1 retirar um relat\u00F3rio com as profiss\u00F5es dos alunos se as mesmas estiverem corretamente cadastradas e lan\u00E7adas.
prt_opcao_geralmente_ulti_clubes=Esta op\u00E7\u00E3o, geralmente, \u00E9 muito utilizada em clubes. O campo PARENTESCO existe para identificar qual o la\u00E7o consangu\u00EDneo existe entre as pessoas cadastradas em um mesmo Grupo.
prt_realizar_cadastr_familia=Ao realizar um cadastro de alguma fam\u00EDlia o colaborador poder\u00E1 classificar o grau de parentesco dos dependentes com o titular, como m\u00E3e, pai, irm\u00E3o.
prt_categoria_clientes=Categoria de Clientes
prt_seu_negocio_fitnness_cat_cliente=Se o seu neg\u00F3cio \u00E9 Fitness, voc\u00EA poder\u00E1 utilizar a CATEGORIA DE CLIENTES para diferenciar os diversos tipos de alunos que tem acesso a sua academia. Dessa forma voc\u00EA conseguir\u00E1 agrupar todos aqueles que possuem caracter\u00EDsticas semelhantes, facilitando o controle e, principalmente, organizando os dados do seu sistema.
prt_categoria_cliente_criar_categoria=Com a categoria de clientes voc\u00EA poderia criar a categoria ALUNO PERSONAL, do tipo ALUNO, para diferenciar os alunos comuns dos alunos que possuem seu pr\u00F3prio personal
prt_caso_trab_clubes=Mas, caso voc\u00EA trabalhe com clubes, utilize sempre os tipos, S\u00D3CIO e N\u00C3O S\u00D3CIO ao cadastrar suas categorias. Al\u00E9m disso, voc\u00EA poder\u00E1 definir a quantidade m\u00E1xima de convites permitida para cada categoria.
prt_conseguira_criar_categ_soc_efetv_soc_contrb=Voc\u00EA tamb\u00E9m conseguir\u00E1 criar categorias de S\u00D3CIO EFETIVO e S\u00D3CIO CONTRIBUINTE, para diferenciar os dois tipos de s\u00F3cios.
prt_aqui_cadast_perg_anamnese=Aqui voc\u00EA poder\u00E1 cadastrar todas as perguntas de sua Anamnese (entrevista) que posteriormente poder\u00E3o compor seu Boletim de Visita. As perguntas cadastradas aqui, poder\u00E3o ser escolhidas, individualmente, no momento em que voc\u00EA criar o seu Question\u00E1rio
prt_campo_cadastro_colaborador_preenche_pais_org=Neste campo de cadastro o colaborador dever\u00E1 preencher com o PA\u00CDS de origem do aluno. Se necess\u00E1rio, voc\u00EA poder\u00E1 cadastrar novos pa\u00EDses para manter seu cadastro mais completo poss\u00EDvel.
prt_opcao_cadastr_brindes=Esta op\u00E7\u00E3o ser\u00E1 utilizada para efetuar o CADASTRO DE BRINDES. Sera usado na utiliza\u00E7\u00E3o da pontua\u00E7\u00E3o realizada.
prt_visualizar_log_geral_entidade=visualizar log geral da entidade
prt_estorno_cappta=Estorno Linx
prt_estorno_cappta_senha_administrativa=Senha administrativa
prt_estorno_cappta_senha_nr_controle=N\u00FAmero de controle
prt_estorno_cappta_executar_operacao=Executar opera\u00E7\u00E3o
prt_abrir_wiki=Clique e saiba mais:
prt_central_eventos_log=Central de Eventos - Log
prt_pag_inicial=pagInicial
prt_pag_anterior=pagAnterior
prt_pag_posterior=pagPosterior
prt_pag_final=pagFinal
prt_tipo_categoria=Tipo de Categoria
prt_tipo_desconto=Tipo de Desconto
prt_idade_limite_dependencia=Idade Limite Depend\u00EAncia
prt_tipo_pergunta=Tipo Pergunta
prt_ListaAcesso_gympass=Relat\u00F3rio Gympass
prt_GymPass_periodoPesquisa=PER\u00CDODO DE PESQUISA\:
prt_ate_Maisculo=AT\u00C9
prt_para_manter_organizacao_sua_academia=Para manter a organiza\u00E7\u00E3o em sua Academia, obter relat\u00F3rios mais precisos e facilitar a manuten\u00E7\u00E3o dos dados, sempre agrupe seus produtos em Categorias.
prt_categoria_alimenticios_poderia_conter_produtos=Categoria Aliment\u00EDcios poderia conter os produtos Sanduiche Natural, Suco de Laranja. Categoria Servi\u00E7os poderia conter os produtos Fisioterapia, Drenagem Linf\u00E1tica, Avalia\u00E7\u00E3o F\u00EDsica, Pilates, etc.
prt_aqui_esta_principal_cadastro_seu_sistema=Aqui est\u00E1 o principal cadastro de seu sistema, quanto mais completo estiver, mais f\u00E1cil ser\u00E1 lan\u00E7ar suas vendas no Sistema e voc\u00EA notar\u00E1 uma grande flexibilidade ao formar v\u00E1rias combina\u00E7\u00F5es para se chegar com exatid\u00E3o, no plano desejado pelo Aluno, incluindo descontos, produtos, modalidades diferentes e conv\u00EAnios, tudo em uma mesma janela de f\u00E1cil acesso e manuseio.
prt_cadastre_aqui_todas_possiveis_pagamento=Cadastre aqui todas as poss\u00EDveis condi\u00E7\u00F5es de pagamento permitidas em sua academia. Quanto mais completo estiver o cadastro, mais f\u00E1cil ser\u00E1 o processo de recebimento de Plano.
prt_voce_poderia_cadastrar_seguintes_condicoes_pagamento=Voc\u00EA poderia cadastrar as seguintes condi\u00E7\u00F5es de pagamento\: \u00C0 vista, 3x Sem Entrada, Entrada + 2x, etc.
prt_serao_cadastradas_todas_turmas_academia=Aqui ser\u00E3o cadastradas todas as turmas de sua Academia, ou mais especificamente, em quais hor\u00E1rios a modalidade da Turma cadastrada \u00E9 oferecida. Quanto mais completo for seu cadastro de turmas mais f\u00E1cil ser\u00E1 o processo de inclus\u00E3o dos Alunos e a procura por vagas.
prt_cadastraremos_natacao_infantil_horario_acesso=Cadastraremos Nata\u00E7\u00E3o Infantil, onde o hor\u00E1rio de Acesso se d\u00E1 no per\u00EDodo da manh\u00E3 entre 07\:00 e 11\:00 horas de segunda a sexta-feira. Al\u00E9m disso, iremos definir o ambiente, anteriormente cadastrado, como Piscina Infantil.
prt_cadastre_todos_produtos_vendidos_sua_academia=Cadastre todos os produtos vendidos em sua Academia e defina o pre\u00E7o pago por cada um deles. N\u00E3o se esque\u00E7a de definir uma Categoria, \u00E9 muito importante. Defina nomes consistentes e lembre-se que estas informa\u00E7\u00F5es ir\u00E3o para os Relat\u00F3rios.
prt_aqui_voce_podera_cadastrar_todos_tipos_descontos=Aqui voc\u00EA poder\u00E1 cadastrar todos os tipos de descontos aplicados em sua Academia, desconto para planos, produtos, matr\u00EDculas, rematr\u00EDculas, etc. Al\u00E9m disso, voc\u00EA pode definir a forma de desconto como sendo por porcentagem ou valor fixo.
prt_voce_poderia_cadastrar_desconto_chamado_pagamento_vista=Voc\u00EA poderia cadastrar um desconto chamado Pagamento \u00E0 Vista e definir o valor do mesmo como 5%. Cadastrar desconto Isen\u00E7\u00E3o de Matr\u00EDcula e definir seu valor como 100%.
prt_aqui_serao_cadastradas_todas_modalidades_oferecidas_academia=Aqui ser\u00E3o cadastradas todas as modalidades oferecidas em sua Academia. Ser\u00E1 necess\u00E1rio atribuir qual o Valor mensal para aquela modalidade. \u00C9 poss\u00EDvel tamb\u00E9m, obrigar ou sugerir a venda de algum produto espec\u00EDfico para os alunos que adquirirem a modalidade espec\u00EDfica.
prt_poderiamos_cadastrar_modalidade_musculacao=Poder\u00EDamos cadastrar a modalidade Muscula\u00E7\u00E3o, obrigar a venda de Avalia\u00E7\u00E3o F\u00EDsica e sugerir a venda de Luvas.
prt_voce_trabalha_modalidade_geralmente_ocorrem=Se voc\u00EA trabalha com Modalidades, sabe que elas geralmente ocorrem em locais espec\u00EDficos. Cadastrando Ambientes ser\u00E1 poss\u00EDvel, definir exatamente onde ser\u00E1 realizada a Aula. Defina os Ambientes de acordo com suas necessidades e estrutura de sua Academia.
prt_cadastrar_modalidade_natacao_poderia_definir_ambiente=Ao cadastrar uma modalidade de Nata\u00E7\u00E3o voc\u00EA poderia definir o Ambiente como Piscina Infantil, ou Piscina 02.
prt_voce_pode_agrupar_varias_modalidades_pactoes=Voc\u00EA pode agrupar v\u00E1rias modalidades em Pacotes, a fim de facilitar o processo de recebimento dos planos. Fazendo isso voc\u00EA pode aplicar descontos especiais para os Alunos que decidem adquirir mais de um tipo de atividade.
prt_voce_poderia_criar_pacote_com_modalidades_musculacao=Voc\u00EA poderia criar um pacote com as modalidades Muscula\u00E7\u00E3o e Nata\u00E7\u00E3o e conceder desconto para os Alunos que adquirirem este pacote.
prt_dividindo_suas_turmas_em_niveis_pode_facilitar_controle=Dividindo suas turmas em n\u00EDveis pode facilitar o controle e ainda evitar que pessoas iniciantes fa\u00E7am atividade junto com outras mais experientes. Cadastre os n\u00EDveis de suas turmas e evite problemas futuros.
prt_voce_poderia_cadastrar_iniciante_intermediario_profissional=Voc\u00EA poderia cadastrar Iniciante, Intermedi\u00E1rio, Profissional, etc.
prt_podemos_definir_horarios_especificos_acesso_academia=Podemos definir hor\u00E1rios espec\u00EDficos de Acesso a Academia, hor\u00E1rios promocionais, impedir acesso em determinados dias entre outras opera\u00E7\u00F5es. Fazendo isso, voc\u00EA organizar\u00E1 quando cada Aluno poder\u00E1 acessar seu estabelecimento.
prt_horario_livre_permitiria_acesso_qualquer_dia_horario=Hor\u00E1rio Livre que permitiria acesso em qualquer dia e hor\u00E1rio, Hor\u00E1rio Executivo, que poderia ser definido como hor\u00E1rio promocional. Crie seus hor\u00E1rios de acordo com sua necessidade e regras de neg\u00F3cio.
prt_convenio_desconto=Conv\u00EAnio de Desconto
prt_aqui_poderao_cadastrados_todos_convenios_desconto_oferecidos=Aqui poder\u00E3o ser cadastrados todos os Conv\u00EAnios de Desconto oferecidos por sua Academia. Voc\u00EA poder\u00E1 definir um valor diferente de desconto para cada dura\u00E7\u00E3o de plano lan\u00E7ada no Cadastro e o valor do desconto poder\u00E1 ser estipulado em percentual ou valores fixos. Al\u00E9m disso, voc\u00EA poder\u00E1 escolher se o Aluno deste conv\u00EAnio ir\u00E1 ou n\u00E3o, pagar taxas de matr\u00EDcula e rematr\u00EDcula.
prt_poderiamos_cadastrar_convenio_para_pacto_solucoes=Poder\u00EDamos cadastrar um conv\u00EAnio para a Pacto Solu\u00E7\u00F5es Tecnol\u00F3gicas, onde planos Mensais n\u00E3o ganhariam desconto, planos Trimestrais teriam 3% de desconto, Semestrais, 7% de desconto e Anuais, teriam 10% de desconto.
prt_tipo_produto=Tipo de Produto
prt_tipo_vigencia=Tipo de Vig\u00EAncia
prt_controle_estoque=Controle de Estoque
prt_zilluon_web_conceito_mult_empresa=O Zillyon Web trabalha com o conceito de multi-empresa, onde atrav\u00E9s de apenas um sistema ser\u00E1 poss\u00EDvel controlar o estoque de v\u00E1rias filiais de uma mesma franquia. Cadastre nesta tela os balan\u00E7os para ajustar o estoque de produtos.
prt_cds_tela_compras_produtos_tipo_est=Cadastre nesta tela as compras dos produtos do tipo 'estoque'.
prt_nesta_tela_consultar_movimentacoes=Nesta tela ser\u00E1 poss\u00EDvel consultar todas as movimenta\u00E7\u00F5es do produto que originaram o estoque.
prt_tela_defin_prod_ctr_estq=Esta tela \u00E9 utilizada para definir os produtos que ter\u00E3o controle de estoque.
prt_posic_estoque=Posi\u00E7\u00E3o do Estoque
prt_tela_inf_posc_estq_atual_minimo=Esta tela \u00E9 utilizada para informar a posi\u00E7\u00E3o do estoque atual e estoque m\u00EDnimo.
prt_imprimir_forml=Imprimir Formul\u00E1rio
prt_inf_emp_real_balanco=Informe a Empresa que ser\u00E1 realizado o Balan\u00E7o
prt_categoria_produto=Categoria Produto
prt_periodo_movimentacoes=Per\u00EDodo movimenta\u00E7\u00F5es
prt_saldor_anterior=Saldo Anterior
prt_saldo_atual=Saldo Atual
prt_imprimir_relat=Imprimir Relat\u00F3rio
prt_detalhes_movmt=Detalhes da Movimenta\u00E7\u00E3o
prt_data_venda=Data Venda
prt_total_entrada=Total Entrada
prt_total_saida=Total Sa\u00EDda
prt_a_ate=\u00E0
prt_todos_produtos=Todos Produtos
prt_somente_prod_estq_min=Somente Produtos com estoque m\u00EDnimo
prt_valor_impresso=Valor Impresso
prt_preco_venda=Pre\u00E7o de Venda
prt_custo_ultima_compra=Custo da \u00FAltima Compra
prt_status_produto=Status Produto
prt_nome_produto=Nome do Produto
prt_codigo_produto=C\u00F3digo do Produto
prt_quantidade_estoque=Quantidade de Estoque
prt_nao_vigente=N\u00E3o Vigente
prt_vigencia_de=Vig\u00EAncia De
prt_vigencia_ate=Vig\u00EAncia At\u00E9
prt_numero_parcelas=N\u00FAmero de Parcelas
prt_data_assinatura=Data de Assinatura
prt_data_inicio_vigencia=Data Inicio de Vigencia
prt_data_final_de_vigencia=Data Final de Vigencia
prt_desconto_parcelas=Desconto nas Parcelas
prt_responsavel_autoriazacao=Respons\u00E1vel Autoriza\u00E7\u00E3o
prt_data_autorizacao=Data Autoriza\u00E7\u00E3o
prt_nome_turma=Nome da Turma
prt_indentificador_turma=Identificador da Turma
prt_data_inicial_vigencia=Data Inicial de Vig\u00EAncia
prt_consultar_produto=Consulta do Produto
prt_codigo_recibo=C\u00F3digo Recibo
prt_data_emissao=Data de Emiss\u00E3o
prt_data_pagamento=Data de Pagamento
prt_nome_pessoa=Nome Pessoa
prt_gestao_notas=Gest\u00E3o de Notas
prt_gestao_nfce=Gest\u00E3o de NFC-e
prt_nota_individual=Nota Individual
prt_mes_competencia=M\u00EAs de Compet\u00EAncia
prt_mes_competencia_independente=M\u00EAs de Compet\u00EAncia Independente da Quita\u00E7\u00E3o
prt_nenhuma_pessoa_encontrada=Nenhuma Pessoa encontrada!
prt_valor_desejado_emissao_notas=Valor desejado para emiss\u00E3o de notas
prt_sequencia_rps=Sequ\u00EAncia RPS
prt_enviar_todas=Enviar todas
prt_visualizar_log=Visualizar Log
prt_nota_manual=Nota Manual
prt_informe_num_nota_manual=Informe o n\u00FAmero da Nota Manual
prt_gerar_nota_manual=Gerar Nota Manual
prt_excluir_nota_manual=Excluir Nota Manual
prt_informe_num_nota_manual_sera_exc=Informe o n\u00FAmero da Nota Manual que ser\u00E1 excluida
prt_imp_notas_manuais_emit=Imprimir Notas Manuais Emitidas
prt_titulo_relatorio=T\u00EDtulo do relat\u00F3rio
prt_notas_manuais_selecionadas=Nota Manual Selecionadas
prt_nota_manual_todas=Nota Manual Todas
prt_relatorio_notas_manuais_emitidas=Relat\u00F3rio Notas Manuais Emitidas
prt_valor_emitir=Valor a Emitir
prt_nfse_emitida=NFSe Enviada
prt_nfce_emitida=NFC-e Enviada
prt_enviar_selecionados=Enviar Selecionados
prt_desvincular_selecionados=Desvincular Selecionados
prt_excluir_selecionados=Excluir Selecionados
prt_gerar_xml_selecionadas=Gerar XML Selecionadas
prt_gerar_xml_todas=Gerar XML Todas
prt_aluno_familia=Aluno(Grupo)
prt_recibos_devoluc_ajutes_saldo=Recibos de devolu\u00E7\u00F5es em ajustes de saldo
prt_data_devolucao=Data de devolu\u00E7\u00E3o
prt_data_registro=Data do Registro
prt_codigo_pessoa=C\u00F3digo da pessoa
prt_falha_transmissao=Falha de Transmiss\u00E3o
prt_indice_finaceiro=Indice Financeiro
prt_tipo_indice=Tipo \u00CDndice
prt_relatorio_indice_fina_reajus_precos=Relat\u00F3rio \u00CDndice Financeiro para Reajuste de Pre\u00E7os
prt_percentual_acumulado=Percentual Acumulado
prt_renovacao_automatica_contrato_recorrencia=Renova\u00E7\u00E3o autom\u00E1tica de contrato recorr\u00EAncia
prt_acesso_ao_sistema=Acesso ao Sistema
prt_zillyon_conceito_mult_empresa=O Zillyon Web trabalha com o conceito de multi-empresa, onde atrav\u00E9s de apenas um sistema ser\u00E1 poss\u00EDvel controlar v\u00E1rias filiais de uma mesma franquia. Cadastre nesta tela as empresas que utilizar\u00E3o o sistema e acompanhe de perto o rendimento de cada uma delas.
prt_para_acessar_sistema_necessario_usuario_cadastrado_login=Para acessar o sistema \u00E9 necess\u00E1rio que o usu\u00E1rio seja devidamente cadastrado e possua Login e Senha de acesso. Este cadastro ser\u00E1 feito por esta op\u00E7\u00E3o. Lembre-se que senhas s\u00E3o \u00FAnicas e n\u00E3o devem ser compartilhadas.
prt_controle_funcionarios_podem_acessar_importante=O controle do que os funcion\u00E1rios podem acessar ou n\u00E3o dentro de um sistema \u00E9 muito importante e atrav\u00E9s desta tela de cadastro voc\u00EA poder\u00E1 definir perfis de acesso, limitando e permitindo que pessoas tenham acessos diferentes ao sistema.
prt_poderiamos_criar_perfil_administrador=Poder\u00EDamos criar um perfil Administrador, que teria acesso a todo o sistema, enquanto os usu\u00E1rios do perfil Consultores teriam acesso a apenas fazer vendas.
prt_essa_ferramenta_permite_consultas_especificas=Essa ferramenta permite que voc\u00EA fa\u00E7a consultas especificas em t\u00F3picos pr\u00E9 cadastrados em formato de SQL. Alguns exemplos s\u00E3o:
prt_acesso_todos_acessos=ACESSO -> Todos Acessos
prt_clientes_alunos_mesmo_cpf=CLIENTES -> Alunos com o mesmo CPF?
prt_por_faturamento_parcela=Por Faturamento Recebido Por Parcela
prt_informe_dados=Informe os dados
prt_tela_cliente=Ir para tela de Edi\u00E7\u00E3o do Cliente
prt_historico_pontos=Historico de pontos
prt_LancaBrinde_tituloForm=Lan\u00E7amento de Brinde
btn_lancarnovo_brinde=Novo Lan\u00E7amento
btn_descricao_brinde=DESCRI\u00C7\u00C3O*
prt_imprimir_recibo_branco=Imprimir Recibo em Branco
prt_nesta_tela_cadastro_podera_definir_contrato_prest_servc=Nesta tela de Cadastro voc\u00EA poder\u00E1 definir como ser\u00E1 o Contrato de Presta\u00E7\u00E3o de Servi\u00E7os de sua Academia. Poder\u00E1 tamb\u00E9m, definir como ser\u00E3o os Recibos de venda de produtos e servi\u00E7os. Utilize o editor para criar seus layouts de acordo com suas necessidades.
prt_justificativa_operacao=Justificativa de Opera\u00E7\u00E3o
prt_procedimentos_lancamento_bonus_atestado_cancelamento_treinamento=Os procedimentos de Lan\u00E7amento de B\u00F4nus, Atestado, Cancelamento, Trancamento e Troca de Planos, necessitam de lan\u00E7amento de justificativas, a fim de se mapear os motivos pelos quais os Alunos se afastam ou abandonam a atividade f\u00EDsica, mas, para conseguirmos tal n\u00EDvel de acompanhamento, precisamos cadastrar as Justificativas mais comuns atrav\u00E9s desta tela de cadastro.
prt_poderiamos_cadastrar_justificativa=Poder\u00EDamos cadastrar a justificativa ?Mudan\u00E7a de Localidade? para o procedimento de Cancelamento.
prt_codigo_verificacao=C\u00F3digo de verifica\u00E7\u00E3o
prt_confira_aqui_seus_bis=Confira aqui seus BI's
prt_saiba_como_ter_melhores_resultados=Saiba como ter melhores resultados com seu Business Intelligence
prt_relatorios_visitantes=Relat\u00F3rio de Visitantes
prt_wiki_relatorios_visitantes=Clique e saiba mais: Relat\u00F3rio de Visitantes
prt_dados_visitantes=Dados dos visitantes
prt_periodo_bv=Per\u00EDodo de BV
prt_situacao_bv=Situa\u00E7\u00E3o de BVs
prt_limpar_periodo_bv=Limpar per\u00EDodo de bv.
prt_limpar_filtros=Limpar filtros
prt_clientes_cancelados=Clientes Cancelados
prt_wiki_clientes_cancelados=Clique e saiba mais: Relat\u00F3rio Clientes Cancelados
prt_dados_cancelados=Dados do cancelamento
prt_data_cancelamento=Data cancelamento
prt_limpar_periodo_cancelamento=Limpar per\u00EDodo de cancelamento.
prt_lancamento_operacao=Lan\u00E7amento Opera\u00E7\u00E3o
prt_ultimo_acesso=\u00DAltimo Acesso
prt_clientes_trancados=Clientes Trancados
prt_wiki_clientes_trancados=Clique e saiba mais: Relat\u00F3rio Clientes Trancados
prt_inicio_trancamento=Inicio Trancamento
prt_fim_trancamento=Fim Trancamento
prt_clientes_bonus=Clientes com B\u00F4nus
prt_wiki_clientes_bonus=Clique e saiba mais: Relat\u00F3rio Clientes B\u00F4nus
prt_dados_bonus=Dados do B\u00F4nus
prt_tipo_bonus=Tipo do B\u00F4nus
prt_dados_impressao=Dados da impress\u00E3o
prt_favor_selecione_inf_deseja_imprimir=Por Favor, selecione as informa\u00E7\u00F5es que deseja imprimir
prt_dados_cadastrais=Dados Cadastrais
prt_informacoes_plano=Informa\u00E7\u00F5es de Plano
prt_relatorio_alunos_bonus=Rel\u00E1torio de Alunos com B\u00F4nus
prt_data_matricula=Data Matr\u00EDcula
prt_incio_plano=Inicio Plano
prt_vencimento_plano=Vencimento Plano
prt_clientes_atestado=Clientes com Atestado
prt_wiki_clientes_atestado=Clique e saiba mais: Relat\u00F3rio Clientes Atestado
prt_dados_atestado=Dados do atestado
prt_data_cadastro_aluno=Data de Cadastro no aluno
prt_inicio_atestado=In\u00EDcio Atestado
prt_fim_atestado=Fim Atestado
prt_por_favor_selecione_informacoes_deseja_imprimir=Por Favor, selecione as informa\u00E7\u00F5es que deseja imprimir
prt_relatorio_alunos_atestado=Rel\u00E1torio de Alunos com Atestado
prt_selecionar_mes_atual=Selecionar M\u00EAs Atual
prt_gerar_relatorio=Gerar Relat\u00F3rio
prt_ordernar_por_min=Ordernar por
prt_consultar_clinte=Consultar Cliente
prt_faixa_etaria=Faixa Et\u00E1ria
prt_dados_nao_encontrados=Dados n\u00E3o encontrados!
prt_total_acessos=Total de Acessos
prt_dos_acessos_clientes_colaboradores=dos Acessos foram de Clientes e Colaboradores
prt_dos_acessos_foram_liberacao=dos Acessos foram Libera\u00E7\u00E3o
prt_ja_justificado=J\u00E1 Justificado
prt_falta_justificar=Falta Justificar
prt_total_bv=Total BV
prt_registros=registros
prt_todos_tipos=Todos os tipos
prt_data_acesso=Data Acesso
prt_tipo_liberacao=Tipo de Libera\u00E7\u00E3o
prt_usuario_liberou=Usu\u00E1rio Liberou
prt_dt_justificativa=Dt.Justificativa
prt_usuario_justificou=Usu\u00E1rio Justificou
prt_enviar_email_fechamento=Enviar email de Fechamento
prt_lista_email=Lista E-mail
prt_relatorio_gyn_pass=Relatorio Gym Pass
prt_lista_clientes_dados_basicos=Lista de Clientes - Dados B\u00E1sicos
prt_lista_clientes_simplificada=Lista de Clientes Simplificada
prt_wiki_clientes_simplificado=Clique e saiba mais: Clientes Simplificado
prt_Empresa_codigoChaveIntegracaoDigitais=C\u00F3digo da chave para Biometrias Unificadas
prt_administradora=Administradora
prt_loja=Loja
prt_tipo_parcelamento_stone_lojista=Lojista (sem juros) 
prt_tipo_parcelamento_stone_emissor=Emissor (com juros)
prt_tipoParcelamento=Tipo de parcelamento
prt_fluxo_caixa_previsto=Fluxo de caixa - Previsto
prt_fluxo_caixa_realizado=Fluxo de caixa - Realizado
prt_entradas=Entradas
prt_saidas=Sa\u00EDdas 
prt_total= Total
prt_saldo=Saldo
prt_saldoinicial=Saldo inicial
prt_atualizarperiodo=Atualizar per\u00EDodo
prt_vincular_datas_fluxo_caixa=Vincular as datas do fluxo de caixa previsto com as datas do fluxo de caixa realizado.
prt_desvincular_datas_fluxo_caixa=Desvincular as datas do fluxo de caixa previsto com as datas do fluxo de caixa realizado.
prt_grafico_fluxo_caixa=Ver gr\u00E1fico do fluxo de caixa
prt_lbl_grafico_fluxo_caixa=Gr\u00E1fico de saldo do fluxo de caixa
prt_tabela_fluxo_caixa=Ver tabelas do fluxo de caixa
prt_aplicar_saldo_realizado=Clique para aplicar o <b>saldo realizado</b> deste dia em diante.
prt_voltar_saldo_previsto=Clique para <b>voltar o saldo previsto</b> deste dia em diante
prt_simulando_saldo=Simulando o saldo <b>realizado</b> a partir daqui
prt_HistoricoPontos_tituloForm=Historico Pontos
prt_previsto=Previsto
prt_simulado_realizado=Simulado
prt_realizado=Realizado
prt_filtros_fc_contas=Filtrar por contas
prt_nao_informado_plano_contas=N\u00E3o informado
prt_descricao_curta=Descri\u00E7\u00E3o curta
prt_nao_movimentados=N\u00E3o movimentados
prt_nao_movimentados_tip=Incluir receb\u00EDveis n\u00E3o movimentados para o financeiro
prt_clique_expandir=Clique para expandir (Clique duas vezes para expandir tudo)
prt_visualizar_saldo_contas=Marcar contas para saldo inicial
prt_desfazer=Desfazer
prt_alterar_manualmente_saldo=Clique para alterar manualmente
prt_mostrar_saldo_realizado_contas=Mostrar saldo realizado das contas nesse dia
prt_nenhum_cliente_encontrado_seguintes_filtros=Nenhum cliente foi encontrado com os seguintes filtros de pesquisa:
prt_e_cliente_seta=e CLIENTE -> 
prt_periodo_vencimento_parcelas_de_seta=e  PER\u00CDODO DE VENCIMENTO DE PARCELAS DE -> 
USAR_PINPAD_DEBITO=Usar pinpad para cart\u00E3o de d\u00E9bito
FORMA_PAGAMENTO_DEBITO=Forma de pagamento para d\u00E9bito
USAR_PINPAD_CREDITO=Usar pinpad para cart\u00E3o de cr\u00E9dito
FORMA_PAGAMENTO_CREDITO=Forma de pagamento para cr\u00E9dito
USAR_TRANSACAO_ONLINE=Usar transa\u00E7\u00E3o online
CONVENIO_COBRANCA=Conv\u00EAnio de cobran\u00E7a padr\u00E3o
prt_Empresa_pontosAlunoAcesso=Pontua\u00E7\u00E3o por acesso:
prt_link_zw_auto=Link para o ZW Auto
prt_autoatendimento=Autoatendimento
prt_abaNotas_titulo=Nota Fiscal
prt_abaNotas_emailsNotificacao=E-mail notifica\u00E7\u00E3o
prt_abaNotas_listaEmailsNotificacao=Lista de E-mails para a notifica\u00E7\u00E3o
prt_autoatendimento_link=Clique para abrir 
prt_pesquisar=Pesquisar
prt_informe_saldo_desejado=Informe o saldo desejado.Para informar um valor <b>negativo</b>, adicione o sinal<b> - </b>antes.
prt_saldo_inicial_contas=Clique para marcar as contas que ir\u00E3o compor o saldo inicial. Caso nenhuma conta esteja selecionada, o saldo ficar\u00E1 zerado.  
prt_dia_competencia=Dia de Compet\u00EAncia
prt_Brinde_tituloForm=Brinde
prt_Brinde_label_codigo=C\u00F3digo
prt_Brinde_label_nome=Nome
prt_Brinde_label_situacao=Situa\u00E7\u00E3o
prt_Brinde_label_pontos=Pontos
prt_Brinde_codigo=C\u00F3digo\:
prt_Brinde_nome=Nome\:
prt_Brinde_ativo=Ativo\:
prt_Brinde_pontos=Valor em pontos\:
prt_Brinde_descricao=Descri\u00E7\u00E3o\:
prt_Brinde_empresa=Empresa\:
prt_Brinde_alterar_empresa=Empresa n\u00E3o pode ser alterada ap\u00F3s GRAVADO (Desative o Brinde adicione um novo)
prt_Brinde_tituloForm_Maisculo=BRINDE
prt_Pontuacao_situacaoAluno=Situa\u00E7\u00E3o do Aluno\:
prt_Pontuacao_situacaoAluno_Maisculo=SITUA\u00C7\u00C3O DO ALUNO
DATA_CONFIRMACAO=Data Confirma\u00E7\u00E3o
prt_Pontuacao_tituloForm=Pontua\u00E7\u00E3o de Alunos
prt_Pontucao_sing_tituloForm=Pontua\u00E7\u00E3o do Aluno
PONTOS=Pontos
PONTOS_MAISC=PONTOS
EXPORTAR_PDF=Exportar para o formato PDF
EXPORTAR_EXCEL=Exportar para o formato Excel
TOTAL_PONTOS=Total Pontos
title_insira_valor_inteiro=Insira um valor inteiro 
prt_ServidorFacial_tituloForm=Servidor Facial
prt_ReconhecimentoFacial_tituloForm=Reconhecimento Facial
prt_ServidorBancoDadosFacial=Servidor de Banco de dados Facial
prt_entre_com_numeros_sep_virgula=Entre com os n\u00FAmeros de lote que deseja consultar Ex. 50,51,52
prt_OperadoraCartao_ativo=Ativo
prt_ConvenioCobranca_Permitir_Receber_Boleto_Apos_Vencimento=Permitir receber boleto ap\u00F3s o vencimento:
USAR_RENOVAR_PLANO=Habilitar 'Renovar plano'
USAR_PLANO=Habilitar 'Comprar plano'
USAR_MEU_FINANCEIRO=Habilitar 'Meu financeiro' 
USAR_TREINO_DIA=Habilitar 'Treino do dia'
USAR_AVALIACAO_FISICA=Habilitar 'Avalia\u00E7\u00E3o f\u00EDsica'
USAR_AULAS_COLETIVAS=Habilitar 'Aulas coletivas'
USAR_MEUS_CONTRATOS=Habilitar 'Meus contratos'
USAR_MEUS_CONTRATOS_IMPRIMIR_CONTRATO=Permitir cliente imprimir contrato
USAR_MEUS_CONTRATOS_FERIAS=Permitir cliente exibir f\u00E9rias
USAR_MEUS_CONTRATOS_TRANCAMENTO=Permitir cliente realizar trancamento
USAR_CAPTURAR_FOTO=Habilitar 'Capturar foto'
USAR_BIOMETRIA=Habilitar 'Biometria'
CONVENIO_COBRANCA_DCO=Conv\u00EAnio de cobran\u00E7a padr\u00E3o para DCO
PAGAR_PRIMEIRA_A_VISTA=Pagar primeira parcela \u00E0 vista 
ALUNO_ALTERA_DIA_VENCIMENTO=Permitir ao aluno alterar o dia do vencimento
ALUNO_PREENCHER_BV=Solicitar ao aluno que preencha o BV
ALUNO_PREENCHER_PARQ=Solicitar ao aluno que preencha o Par-Q
USAR_DCO=Usar d\u00E9bito em conta corrente
prt_Usar_Catraca_Offline=Utilizar catraca offline
prt_usar_catraca_offiline_hint=Ao marcar esta op\u00E7\u00E3o, a catraca funcionar\u00E1 offline, ou seja, o cadastro da biometria ser\u00E1 realizado diretamente no menu da pr\u00F3pria catraca e n\u00E3o pelo acesso. Obs: Essa configura\u00E7\u00E3o n\u00E3o tem nada a ver com base online/offline
PERMITE_PAGAR_DEPOIS=Permite Pagar Depois
MOSTRAR_MSG_PARCELA_PAGAR=Mostrar mensagem pagar parcelas ao logar
HABILITAR_CARD_CADASTRO_VISITANTE=Habilitar card para cadastro de visitante sem lan\u00E7amento de contrato
HABILITAR_MULTI_EMPRESA=Habilitar multi-empresa
EXIGIR_RESPONSAVEL_MENOR=Exigir respons\u00E1vel em cadastro de aluno menor de idade
FORMATO_VALOR_TOTAL_OPCAO_PARCELADO_PINPAD=Pagamento total com op\u00E7\u00E3o de parcelamento pela operadora via pinpad
prt_PlanoConfiguracoesFeria_tituloForm=Configura\u00E7\u00F5es de F\u00E9rias
PERMITE_SELECIONAR_CONSULTOR=Permite selecionar consultor no ato do cadastro
PERMITE_PAGAR_DEPOIS_HINT=Ao marcar esta op\u00E7\u00E3o, o sistema ir\u00E1 mostrar o bot\u00E3o 'Pagar Depois' na tela de pagamento permitindo que o aluno conclua seu cadastro por\u00E9m deixe sua parcela em aberto caso queira pagar na recep\u00E7\u00E3o ou mesmo no pr\u00F3prio toten depois. Lembrando que com a parcela em aberto ele n\u00E3o ir\u00E1 passar na catraca. Se estiver desmarcada n\u00E3o ser\u00E1 mostrado o bot\u00E3o 'Pagar Depois' na tela de pagamento do aluno.
MOSTRAR_MSG_PAGAR_PARCELAS_HINT=Ao marcar esta op\u00E7\u00E3o, o sistema ir\u00E1 esconder a mensagem de pagar parcelas no momento do login do cliente no auto atendimento.
PERMITE_SELECIONAR_CONSULTOR_HINT=Ao marcar esta op\u00E7\u00E3o, no ato de cadastro de um novo aluno o sistema ir\u00E1 perguntar se o aluno foi atendido por algum consultor. Se ele selecionar a op\u00E7\u00E3o 'Sim' ent\u00E3o o sistema ir\u00E1 mostrar a lista de consultores pra ele escolher, agora se ele selecionar 'N\u00E3o' o sistema prossegue com o consultor padr\u00E3o 'Recorr\u00EAncia'.
NOME_ADESAO=Descri\u00E7\u00E3o para ades\u00E3o a ser mostrada no totem
TELEFONE_OBRIGATORIO=Telefone obrigat\u00F3rio
COBRAR_NO_CADASTRO=Cobrar no cadastro
EMAIL_OBRIGATORIO=E-mail obrigat\u00F3rio
ENDERECO_OBRIGATORIO=Endere\u00E7o obrigat\u00F3rio
APRESENTAR_PARCELAS_EA_BOLETO=Apresentar parcelas em aberto de boleto
HABILITAR_QR_CODE_PIX=Habilitar QR Code pix nas opções de pagamento
HABILITAR_QR_CODE_PIX_HINT=Necess\u00E1rio configurar conv\u00EAnio padr\u00E3o link de pagamento na empresa
PERMITIR_ESTORNAR_CONTRATO_TROCA_PLANO=Permitir estornar contrato e trocar de plano durante negociação
PERMITIR_ESTORNAR_CONTRATO_TROCA_PLANO_HINT=Permitir que durante a opera\u00E7\u00E3o de negocia\u00E7\u00E3o o cliente possa estornar o contrato e trocar de plano
PERMITIR_CUPOM=Permitir cupom de desconto
USAR_BALANCA_BIOIMPEDANCIA=Usar balan\u00E7a de bioimped\u00E2ncia
PERMITIR_CONVIDAR=Permitir emitir convites 
NOME_ADESAO_HINT=Defina aqui o nome da Taxa de Ades\u00E3o que deseja que o Zw Auto mostre no ato de cadastro de um novo aluno.
LINK_APP_ANDROID_HINT=Link app Android.
LINK_APP_IOS_HINT =Link app IOS
LINK_APP_IOS=Link app IOS
LINK_APP_ANDROID=Link app Android
HABILITAR_STONE_CONNECT=Usar Pinpad Stone Connect
PINPAD_STONE_CONNECT_DEBITO=Pinpad Cr\u00E9dito (Stone Connect)
PINPAD_STONE_CONNECT_CREDITO=Pinpad D\u00E9bito (Stone Connect)
HABILITAR_STONE_CONNECT_HINT=Usar Pinpad Stone Connect
PINPAD_STONE_CONNECT_DEBITO_HINT=Pinpad Cr\u00E9dito (Stone Connect)
PINPAD_STONE_CONNECT_CREDITO_HINT=Pinpad D\u00E9bito (Stone Connect)
prt_Empresa_tokenSMSShortCode=Token SMS(Short Code)
prt_Coletor_numSerie_Finger=N\u00FAmero de s\u00E9rie (finger):
prt_Coletor_numSerie_Plc=N\u00FAmero de s\u00E9rie (plc):
prt_valor_em_descontos=Valor em descontos
prt_valor_em_descontos_tip=Este BI mostra os descontos concedidos pela academia em venda de planos e produtos no per\u00EDodo. Entram no BI os descontos por conv\u00EAnio, desconto extra e desconto na venda de produto.
prt_adquirente_tipoConvenioCobranca=Tipo Conv\u00EAnio Cobran\u00E7a
prt_Produto_aparecerAulaCheia=Mostrar no Aula Cheia
prt_Produto_aparecerAulaCheiatip=Quando esta configura\u00E7\u00E3o estiver marcada, o produto aparece como op\u00E7\u00E3o para o usu\u00E1rio no lan\u00E7amento de aula experimental do Aula Cheia.
ALUNOS=Aluno(s)
prt_LocalAcesso_tempoToleranciaSaida=Tempo de toler\u00E2ncia para sa\u00EDda:
prt_campanha_duracao_tituloForm=Campanha Dura\u00E7\u00E3o
prt_ItensCampanha_tituloForm=Itens da Campanha
tipo_itemCampanha=Tipo Item
prt_itemCampanha_tituloForm=Item Campanha
prt_indicadores=Indicadores
prt_campanha=Minhas Campanhas
prt_campanha_pontuacao=Pontua\u00E7\u00E3o
prt_campanha_titulo_acesso=POR CADA ACESSO
prt_campanha_titulo_plano=Escolha um Plano
prt_campanha_titulo_aula=Escolha uma Aula
prt_campanha_titulo_planoDuracao=Pontua\u00E7\u00E3o por Dura\u00E7\u00E3o
prt_campanha_titulo_produto=Escolha um Produto
prt_brinde=Brinde
prt_relatorio_pontos=Relat\u00F3rio de pontos
prt_relatorioPontos=Relat\u00F3rio
prt_ModeloMensagem_adicionarTagEmailConfirmacaoCompra =Adicionar Tag para o envio de confirma\u00E7\u00E3o de compra via e-mail:
prt_ModeloMensagem_url_site=Url redirecionamento:
prt_ContratoOperacao_msgOperacaoFutura=Retorno dessa opera\u00E7\u00E3o n\u00E3o pode ser realizado porque existe outra opera\u00E7\u00E3o de afastamento futura. Estorne a opera\u00E7\u00E3o futura e depois realize esse retorno.
prt_Modalidade_tipoModalidade= Tipo Modalidade:
prt_Modalidade_tiponome=* Tipo\:
prt_TipoModalidade_tituloForm=Tipo Modalidade
prt_TipoModalidade_codigo=C\u00F3digo
prt_TipoModalidade_nome=* Nome\:
prt_TipoModalidade_Identificador=Identificador
msg_inf_nome_alun_titu_ou_depend=Informe nome de aluno titular ou dependente
msg_nao_poss_encontrar_cliente=Nenhum aluno encontrado
prt_Empresa_pontosAlunoAcessoChuva=Pontua\u00E7\u00E3o por acesso na chuva:
prt_Empresa_pontosAlunoAcessoFrio=Pontua\u00E7\u00E3o por acesso no frio:
prt_Empresa_pontosAlunoAcessoCalor=Pontua\u00E7\u00E3o por acesso no calor:
PROIBIR_RENOVAR_PLANO_COM_PARCELAS_ABERTAS=N\u00E3o permitir Renovar Plano com parcela em aberto
PROIBIR_RENOVAR_PLANO_COM_PARCELAS_ABERTAS_HINT=Quando marcado impede que o aluno consiga renovar o plano possuindo alguma parcela em aberto
prt_Empresa_parceiroFidelidade_numeroLogico=N\u00FAmero L\u00F3gico Loja:
prt_Empresa_parceiroFidelidade_numeroMaquineta=N\u00FAmero Maquineta:
prt_Empresa_parceiroFidelidade_nomeTabela=Nome Tabela:
prt_Empresa_parceiroFidelidade_tituloTabelaPontuacao=Tabela Pontua\u00E7\u00E3o Dotz
prt_Empresa_parceiroFidelidade_valorInicial=De R$:
prt_Empresa_parceiroFidelidade_valorFinal=At\u00E9 R$:
prt_Empresa_parceiroFidelidade_multiplicador=Multiplicador:
prt_Empresa_parceiroFidelidade_tituloForm=Identifica\u00E7\u00E3o Integra\u00E7\u00E3o
prt_Empresa_parceiroFidelidade_tabelaDefaultRecorrencia=Recorr\u00EAncia:
prt_Empresa_formaPagamento_gerarPontos=Gerar pontos Parceiro Fidelidade:
prt_Empresa_formaPagamento_tipoParceiro=Tipo Parceiro Fidelidade:
prt_adquirente_situacao=SITUA\u00C7\u00C3O
prt_adquirente_ativa=Ativa:
codigo=C\u00F3digo
prt_FormaPagamento_descricaoPinPad=Descri\u00E7\u00E3o\:
PAGAMENTO_CONJUNTO=Possuem pagamento em conjunto
MESMO_CARTAO=Dividem o mesmo cart\u00E3o de cr\u00E9dito
MESMO_ENDERECO=Dividem o mesmo endere\u00E7o
prt_HorarioTurma_ativo= Hor\u00E1rio Dispon\u00EDvel (Venda):
prt_HorarioTurma_ativo_title= Dispon\u00EDvel para que novos contratos sejam vendidos nesse hor\u00E1rio.
prt_ConfigEmissao_tituloForm=Configura\u00E7\u00F5es de Emiss\u00E3o
prt_minutos_para_creditar_pontos=Minutos ap\u00F3s \u00FAltimo acesso para creditar outro ponto
prt_considerar_apenas_primeiro_acesso=Considerar apenas primeiro acesso do dia 
prt_pontuacao=Pontua\u00E7\u00E3o
prt_ir_para_tela_plano=Ir para tela do Plano
prt_total_de= Total de 
prt_clube_vantagens_itens_pontuacao=Itens Pontuados
prt_clube_vantagens_zerar_pontuacao_vencido=Zerar pontos do aluno 
prt_clube_vantagens_configuracao_pontos=Configura\u00E7\u00F5es
prt_New_Mala_Direta_Form_Codigo=C\u00F3digo
prt_New_Mala_Direta_Form_descricao=Descri\u00E7\u00E3o
prt_New_Mala_Direta_Form_Periodo=Per\u00EDodo para realizar a Meta:
prt_New_Mala_Direta_Form_ate=at\u00E9
prt_New_Mala_Direta_Form_pre_definido=Evento Pr\u00E9-definido:
prt_New_Mala_Direta_Form_pre_min=M\u00EDn.
prt_New_Mala_Direta_Form_qtd_min=Quantidade m\u00EDnima de acessos:
prt_New_Mala_Direta_Form_pre_max=M\u00E1x.
prt_New_Mala_Direta_Form_qtd_max=Quantidade m\u00E1xima de acessos:
prt_New_Mala_Direta_Form_recorrencia=Somente planos do tipo recorr\u00EAncia
prt_New_Mala_Direta_Form_duracao=Dura\u00E7\u00E3o dos planos:
prt_New_Mala_Direta_Form_situacao=Situa\u00E7\u00E3o:
prt_New_Mala_Direta_Form_usuarios=Usu\u00E1rios Participantes da Meta
prt_New_Mala_Direta_Form_qtd_pos=Qtde. de dias p\u00F3s-venda:
prt_New_Mala_Direta_Form_serao_considerados=Ser\u00E3o considerados todos os alunos
prt_New_Mala_Direta_Form_serao_matriculados_filtro_padrao=matriculados e rematriculados como filtro padr\u00E3o. Para contratos renovados ser\u00E1 necess\u00E1rio habilitar na configura\u00E7\u00E3o do sistema a op\u00E7\u00E3o Incluir Contratos Renovados na aba P\u00F3s Venda.
prt_New_Mala_Direta_Form_pesquisar_tambem=Pesquisar tamb\u00E9m quem tem mais dias de falta do que o informado
prt_New_Mala_Direta_Form_pesquisar_dias_inicio=Nr. dias do in\u00EDcio do Freepass: 
prt_New_Mala_Direta_Form_Qtd_creditos=Qtde. de Cr\u00E9ditos: 
prt_New_Mala_Direta_Form_nao_tornam_alunos=Apenas os que n\u00E3o se tornaram alunos
prt_New_Mala_Direta_Form_prametros_conexao_FTP=Voc\u00EA precisa configurar os parametros de conex\u00E3o FTP em configura\u00E7\u00F5es do sistema, na aba E-mail, na sess\u00E3o Mailing Autom\u00E1tico. O sistema precisa dessas configura\u00E7\u00F5es para enviar o arquivo xml com a lista de clientes conforme este agendamento.
prt_New_Mala_Direta_Form_reponsavel=Nenhum respons\u00E1vel encontrado!
#----------VENDA RAPIDA---------------
prt_venda_rapida_aluno_esta_ativo_e_nao_sao_permitidos_contratos_concomitantes=Aluno est\u00E1 ativo e n\u00E3o s\u00E3o permitidos contratos concomitantes
prt_venda_rapida_cpf_responsavel_invalido=CPF do respons\u00E1vel \u00E9 invalido.
prt_venda_rapida_cpf_invalido=O CPF informado n\u00E3o \u00E9 v\u00E1lido.
prt_venda_rapida_cupom_removido_com_sucesso=Cupom removido com sucesso!
prt_venda_rapida_cupom_aplicado_com_sucesso=Cupom aplicado com sucesso!
prt_venda_rapida_informe_o_numero_cupom=Informe o n\u00FAmero do cupom!
prt_venda_rapida_informe_nascimento_responsavel=Informe a data de nascimento do respons\u00E1vel!
a_vista=\u00C1 vista
tipo_do_cartao_nao_credito=O tipo de cart\u00E3o do cliente n\u00E3o \u00E9 um cart\u00E3o de cr\u00E9dito.
qtde_parcela_parcelamento_operadora=A quantidade de parcelas deve ser maior que 0(zero)
dados_da_conta_sao_invalidos=Os dados da conta s\u00E3o inv\u00E1lidos!
a_forma_pagamento_tem_que_ser_informada=A forma de pagamento tem que ser informada!
um_cartao_de_credito_tem_que_ser_informado=Um cart\u00E3o de cr\u00E9dito tem que ser informado!
cartao_de_credito_obrigatorio_venda_rapida=\u00C9 obrigat\u00F3rio informar uma forma de pagamento do tipo cart\u00E3o de cr\u00E9dito.
o_convenio_tem_que_ser_informada=O conv\u00EAnio de cobran\u00E7a tem que ser informado!
aluno_tem_parcelas_em_aberto_receber_debito=Aluno tem parcelas em aberto, receba o d\u00E9bito antes de lan\u00E7ar a nova venda.
plano_deve_ser_informado=O plano deve ser informado!
esta_inadimplente=Est\u00E1 inadimplente
esta_pendente_com=Est\u00E1 pendente com
titulos_totalizando_um_valor_total_de=T\u00EDtulos totalizando um valor total de
o_que_deseja_fazer=O que deseja fazer?
prt_venda_rapida_voce_nao_possui_permissao= Voc\u00EA n\u00E3o possui permiss\u00E3o para esta a\u00E7\u00E3o. A permiss\u00E3o necess\u00E9ria 4.11 - Venda Avulsa.
prt_Qtde_dias_Operacao=Qtde Dias
prt_ChavePJBANK=Chave PJBank
prt_CredencialPJBANK=Credencial PJBank
prt_GrupoBoleto=Grupo de Boletos
prt_CampanhaDuracao_tituloForm=Campanha Dura\u00E7\u00E3o
prt_ItemCampanha_tituloForm=Item Campanha
prt_ConfigNotaFiscal_tituloForm=Configura\u00E7\u00F5es de Nota Fiscal
prt_Introducao_Pesquisa=Texto de boas vindas / Introdu\u00E7\u00E3o:
prt_Limitar_Uma_Pesquisa=Marque se n\u00E3o quiser que o cliente responda \u00E0 pesquisa mais de uma vez
prt_titulo_pesquisa =T\u00EDtulo que ficar\u00E1 vis\u00EDvel para o seu cliente 
prt_imagem_fundo =Tamanho m\u00E1ximo 5mb
prt_Produto_qtdePontos =Quantidade Pontos:
prt_Produto_pontos =Pontos:
prt_Produto_pontos_maiusculo =PTS PRODUTO:
prt_OrganizadorCarteira_total_pontos=Total Pontos
prt_Contrato_responsavelPeloContrato_extenso=Respons\u00E1vel Lan\u00E7amento Contrato
prt_Empresa_enviaraApenasAtivoTW=Enviar apenas alunos ativos para o Treino
prt_Empresa_enviaraApenasAtivoTW_tittle=Sistema s\u00F3 enviar\u00E1 alunos ao Treino quando se tornarem ativos.
prt_operacaoColetiva_titulo=Opera\u00E7\u00F5es Coletivas
prt_telefone_emergencia_maiusculo=N\u00DAMERO EMERG\u00CANCIA\:
prt_Cliente_UsernameAmigoFit=Usu\u00E1rio:
prt_Cliente_senhaAmigoFit=Senha:
prt_Cliente_codAmigoFit=C\u00F3digo Amigo Fit:
prt_Cliente_tokenAmigoFit=Token Amigo Fit:
prt_Cliente_bancoAmigoFit=Banco Amigo Fit:
prt_PlanoTextoPadrao_linkDePagamento=Aparecer no Link Pagamento:
prt_Cadastro_label_codigomgb=Nivel Mgb:
prt_Cadastro_label_codigoPiscinaMgb=Piscina Mgb
prt_CupomDesconto_redeEmpresa=Cupom v\u00E1lido para rede:
TRANSFERENCIA_DIREITO_USO=Transfer\u00EAncia dos direito de uso
RECUPERACAO_DIREITO_USO=Recupera\u00E7\u00E3o dos direito de uso
prt_lancar_contas_financeiro_sem_plano_de_conta=Escolha um plano de conta para prosseguir com a opera\u00E7\u00E3o. Para lan\u00E7ar sem plano de conta, \u00E9 necess\u00E1rio desmarcar a permiss\u00E3o 9.59 - Restri\u00E7\u00E3o de Lan\u00E7amento de Conta sem Plano de Conta.
prt_LocalImpressao_titulo=Local impress\u00E3o
prt_LocalImpressao_nome=Nome
prt_LocalImpressao_nome_computador=Nome computador
prt_Produto_descricaoServicoMunicipio=Descri\u00E7\u00E3o Servi\u00E7o\ Munic\u00EDpio\:
prt_Produto_exibirRelatorioSMD=Exibir Relat\u00F3rio SMD\:
prt_Produto_exibirRelatorioSMDExplain=Se estiver marcado, ser\u00E1 considerado no relat\u00F3rio SMD.

<?xml version="1.0" encoding="ISO-8859-1"?>
<aplicacao>
    <bd>
        <nomeBDCep>BDCEP</nomeBDCep>
        <nomeBD> </nomeBD>
        <servidor>localhost</servidor>
        <username>postgres</username>
        <senha>pactodb</senha>
        <porta>5432</porta>

        <!--Nunca subir para o SVN este arquivo sem aviso previo!
        
        Para desativar o OAMD  (Multibanco) e usar o cfgXML para conexao,
        deve-se apenas deixar a tag url-oamd vazia.        
        -->
        <url-oamd>*************************************</url-oamd>
        <NFE>false</NFE>
        <!-- Se roboApenasComoServico = true, fara com que o robo so seja utilizado
        como servico e nunca atraves da aplicacao JSF. Valor default = false
        -->
        <roboApenasComoServico>true</roboApenasComoServico>

        <!--<PERSON> padrão é desabilitado = vazio, caso não adicionar [ip]:[porta] -->
        <ipServidoresMemCached>DISABLED</ipServidoresMemCached>

        <urlBdRemessa>***************************************</urlBdRemessa>
        <userBdRemessa>postgres</userBdRemessa>
        <pwdBdRemessa>pactodb</pwdBdRemessa>

        <!--alguns exemplos de context name, para tomcat vide tambem context.xml-->
        <JNDI-GlassFish>jdbc/Teste</JNDI-GlassFish>
        <JNDI-Tomcat>java:/comp/env/jdbc/BDTeste</JNDI-Tomcat>

        <!--em uso-->
        <JNDI>java:/comp/env/jdbc/BDZillyon</JNDI>        
        
        <DESV>false</DESV>
    </bd>
</aplicacao>

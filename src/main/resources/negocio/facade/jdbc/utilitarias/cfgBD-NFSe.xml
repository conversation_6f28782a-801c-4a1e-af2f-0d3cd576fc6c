<?xml version="1.0" encoding="ISO-8859-1"?>
<aplicacao>
    <bd>
        <!--AS CONFIGURAÇÕES ATUAIS DO CFGBD-NFSE ESTÃO APONTANDO PARA PRODUÇÃO! -->
        <!--AS CONFIGURAÇÕES ATUAIS DO CFGBD-NFSE ESTÃO APONTANDO PARA PRODUÇÃO! -->
        <!--AS CONFIGURAÇÕES ATUAIS DO CFGBD-NFSE ESTÃO APONTANDO PARA PRODUÇÃO! -->

        <nomeBDCep>BDCEP</nomeBDCep>
        <nomeBD>DBNFSe</nomeBD>
        <servidor>186.202.37.151</servidor>
        <username>sa</username>
        <senha>pactodb</senha>
        <porta>1470</porta>

        <!--Nunca subir para o SVN este arquivo sem aviso previo!

        Para desativar o OAMD  (Multibanco) e usar o cfgXML para conexao,
        deve-se apenas deixar a tag url-oamd vazia.
        -->
        <url-oamd></url-oamd>
        <NFE>true</NFE>
        <!-- Se roboApenasComoServico = true, fara com que o robo so seja utilizado
        como servico e nunca atraves da aplicacao JSF. Valor default = false
        -->
        <roboApenasComoServico>true</roboApenasComoServico>


        <!--alguns exemplos de context name, para tomcat vide tambem context.xml-->
        <JNDI-GlassFish>jdbc/Teste</JNDI-GlassFish>
        <JNDI-Tomcat>java:/comp/env/jdbc/BDTeste</JNDI-Tomcat>

        <!--em uso-->
        <JNDI>java:/comp/env/jdbc/BDZillyon</JNDI>

        <DESV>false</DESV>
    </bd>
</aplicacao>
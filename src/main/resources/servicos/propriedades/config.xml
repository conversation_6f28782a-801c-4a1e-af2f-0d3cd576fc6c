<?xml version='1.0' encoding='UTF-8'?>
<project>
    <actions/>
    <description></description>
    <logRotator class="hudson.tasks.LogRotator">
        <daysToKeep>2</daysToKeep>
        <numToKeep>2</numToKeep>
        <artifactDaysToKeep>-1</artifactDaysToKeep>
        <artifactNumToKeep>-1</artifactNumToKeep>
    </logRotator>
    <keepDependencies>false</keepDependencies>
    <properties/>
    <scm class="hudson.scm.NullSCM"/>
    <canRoam>true</canRoam>
    <disabled>false</disabled>
    <blockBuildWhenDownstreamBuilding>false</blockBuildWhenDownstreamBuilding>
    <blockBuildWhenUpstreamBuilding>false</blockBuildWhenUpstreamBuilding>
    <triggers class="vector">
        <hudson.triggers.TimerTrigger>
            <spec></spec>
        </hudson.triggers.TimerTrigger>
    </triggers>
    <concurrentBuild>true</concurrentBuild>
    <builders>
        <hudson.tasks.Ant plugin="ant@1.1">
            <targets></targets>
            <antName>/opt/deploy/ant/bin</antName>
            <antOpts>-Dchave=MINHAEMPRESA -Did_mailing=1 -Durl=http://app.pactosolucoes.com.br</antOpts>
            <buildFile>/opt/deploy/ant/build.request.mailing.xml</buildFile>
        </hudson.tasks.Ant>
    </builders>
    <publishers/>
    <buildWrappers/>
</project>
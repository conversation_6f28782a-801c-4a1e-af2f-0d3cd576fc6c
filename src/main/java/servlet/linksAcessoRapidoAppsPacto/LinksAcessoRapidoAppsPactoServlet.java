package servlet.linksAcessoRapidoAppsPacto;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import controle.arquitetura.security.LoginControle;
import negocio.comuns.arquitetura.EnvelopeRespostaDTO;
import negocio.comuns.arquitetura.UsuarioEmailVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.ControleAcesso;
import negocio.facade.jdbc.arquitetura.UsuarioEmail;
import org.json.JSONObject;
import servicos.propriedades.PropsService;
import servlet.arquitetura.SuperServlet;

import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.sql.Connection;

public class LinksAcessoRapidoAppsPactoServlet extends SuperServlet {

    @Override
    protected void processRequest(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        response.setHeader("Access-Control-Allow-Origin", "*");
        response.setHeader("Access-Control-Allow-Methods", "GET");
        response.setHeader("Access-Control-Allow-Headers", "Authorization,empresaId");
        response.setContentType("application/json");

        String key = request.getParameter("key");
        String empresa = request.getParameter("empresa");
        String usuariozw = request.getParameter("usuariozw");
        String ip = request.getParameter("ip");
        String userAgent = request.getParameter("userAgent");
        String acao = request.getParameter("acao");
        String urlZW = PropsService.getPropertyValue(key, PropsService.roboControle);

        try {
            switch (acao) {
                case "assinaturadigital":
                    JSONObject paramsJson = new JSONObject();
                    paramsJson.put("chave", key);
                    paramsJson.put("usuario", usuariozw);
                    paramsJson.put("empresaLogada", empresa);
                    if (!UteisValidacao.emptyString(ip)) {
                        paramsJson.put("tokenIp", ip);
                    }
                    if (!UteisValidacao.emptyString(userAgent)) {
                        paramsJson.put("tokenUserAgent", userAgent);
                    }
                    String params = Uteis.encriptar(paramsJson.toString(), "pactotknssntrdgtl");
                    String linkAcesso = "/assinatura/contratos.html?token=" + params;
                    String tela = request.getParameter("tela");

                    switch (tela) {
                        case "par-q":
                            linkAcesso += "&tela=par-q";
                            break;
                        case "vacina":
                            linkAcesso += "&tela=vacina";
                            break;
                        default:
                            linkAcesso += "&tela=inicio";
                            break;
                    }

                    String qrCodeAcesso = "/QRCode?w=325&h=325&encr=n&qrtext=" + (urlZW + linkAcesso.replace("&", "%26"));
                    JSONObject linksEQrCode = new JSONObject();
                    linksEQrCode.put("linkAcesso", linkAcesso);
                    linksEQrCode.put("qrCodeAcesso", qrCodeAcesso);
                    JSONObject content = new JSONObject().put("content", linksEQrCode);
                    response.getWriter().append(content.toString());
                    break;
                case "obteremailappgestor":
                    String emailUsuario = obterEmailUsuarioPorUsuario(Integer.parseInt(usuariozw), request);
                    JSONObject contentEmail = new JSONObject().put("content", emailUsuario != null ? emailUsuario : "email-nao-encontrado");
                    response.getWriter().append(contentEmail.toString());
                    break;
                case "gerarqrcodeappgestor":
                    String email = request.getParameter("email");
                    String senha = request.getParameter("senha");
                    String retornoQrCodeAppGestor = "";
                    JSONObject contentQrCodeAppGestor = new JSONObject();

                    if (validarEmailSenha(email, senha, key, request)) {
                        JSONObject json = new JSONObject();
                        json.put("email", email);
                        json.put("senha", senha.toUpperCase());
                        json.put("empresa", empresa);
                        retornoQrCodeAppGestor = urlZW + "/QRCode?w=325&h=325&encr=s&qrtext=" + Uteis.encriptar(json.toString(), "TxTQrcOdE");
                        contentQrCodeAppGestor.put("status", "sucesso");
                        contentQrCodeAppGestor.put("qrcode", retornoQrCodeAppGestor);
                    } else {
                        contentQrCodeAppGestor.put("status", "senhaIncorreta");
                    }

                    response.getWriter().append(contentQrCodeAppGestor.toString());
                    break;
            }

        } catch (Exception ex ) {
            ex.printStackTrace();
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            response.getWriter().append(new JSONObject(EnvelopeRespostaDTO.erro("Erro", ex.getMessage())).toString());
        }
    }

    private Connection obterConexao(ServletRequest request) throws Exception {
        String key = request.getParameter("key");
        if (UteisValidacao.emptyString(key)) {
            throw new Exception("Chave não informada.");
        }
        return new DAO().obterConexaoEspecifica(key.trim());
    }

    private void finalizarConexao(Connection con) {
        try {
            if (con != null) {
                con.close();
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    private String obterEmailUsuarioPorUsuario(Integer usuario, ServletRequest request) throws Exception {
        Connection con = null;
        try {
            try {
                con = obterConexao(request);
                UsuarioEmail usuarioEmail = new UsuarioEmail(con);
                UsuarioEmailVO usuarioEmailVO = usuarioEmail.consultarPorUsuario(usuario);
                return usuarioEmailVO != null ? usuarioEmailVO.getEmail() : null;
            } catch (Exception ex) {
                ex.printStackTrace();
            }
            return null;
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        } finally {
            finalizarConexao(con);
        }
    }

    private Boolean validarEmailSenha(String email, String senha, String key, ServletRequest request) {
        Connection con = null;
        try {
            try {
                con = obterConexao(request);
                ControleAcesso controleAcesso = new ControleAcesso(con);
                UsuarioVO usuarioVO = controleAcesso.verificarLoginUsuario(email, senha);
                LoginControle loginControle = new LoginControle();
                loginControle.adicionarUsuarioServicoDescobrir(key, email, usuarioVO);
                return true;
            } catch (Exception ex) {
                ex.printStackTrace();
            }
            return false;
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        } finally {
            finalizarConexao(con);
        }
    }
}

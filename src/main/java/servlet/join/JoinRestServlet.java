package servlet.join;

import br.com.pactosolucoes.atualizadb.processo.geolocalizacao.GoogleApiGeocodeService;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.utilitarias.UteisValidacao;
import org.json.JSONArray;
import org.json.JSONObject;
import servicos.integracao.impl.join.JoinService;
import servlet.arquitetura.SuperServlet;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.stream.Collectors;

public class JoinRestServlet extends SuperServlet {


    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        processRequest(request, response);
    }

    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        processRequest(request, response);
    }

    protected void processRequest(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        response.addHeader("Access-Control-Allow-Origin", "*");
        response.addHeader("Access-Control-Allow-Methods", "GET");
        response.setContentType("application/json");
        String chave = request.getParameter("chave");
        Integer empresa = Integer.valueOf(request.getParameter("empresa"));
        Integer cod = UteisValidacao.emptyString(request.getParameter("cod")) ? null : Integer.valueOf(request.getParameter("cod"));

        try {
            String body = request.getReader().lines().collect(Collectors.joining(System.lineSeparator()));
            Integer codigo = getService(chave).processarNovaLead(cod, body, empresa);
            JSONObject json = new JSONObject();
            json.put("codigo", codigo);
            response.getWriter().append(json.toString());
        }catch (Exception e ){
            System.out.println("Erro na api rest integração join. Message: "+e.getMessage());
            processarErro(e, request, response, null);
        }

    }

    private JoinService getService(String chave) throws Exception {
        return new JoinService(new DAO().obterConexaoEspecifica(chave));
    }
}

package servlet.pactoprint;

import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import org.json.JSONArray;
import org.json.JSONObject;
import servlet.arquitetura.SuperServletIntegracaoDelphi;
import negocio.comuns.pactoprint.LocalImpressaoVO;

import javax.servlet.http.HttpServletRequest;


/**
 * date : 01/04/2015 15:52:30
 * autor: Ulisses
 */
public class LocalImpressaoServletIntegracaoDelphi  extends SuperServletIntegracaoDelphi {

	@Override
	protected JSONObject processarRequisicao(HttpServletRequest request)throws Exception{
    	String nomeComputador = request.getParameter("nomeComputador");
    	if ((nomeComputador == null) || (nomeComputador.trim().equals(""))){
    		throw new ConsistirException("É necessário informar o nome do computador.");
    	}

    	LocalImpressaoVO localImpressaoVO = getAcessoControle().getLocalImpressao().consultarPorNomeComputador(nomeComputador, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
    	if (localImpressaoVO != null){
    	    JSONArray jSONArray = new JSONArray();
            jSONArray.put(localImpressaoVO.getJson());
    	    JSONObject jSONObject = new JSONObject();
    	    jSONObject.put("localImpressao", jSONArray);
    	    return jSONObject;
    	}else{
    	  throw new ConsistirException("Computador '" + nomeComputador + "' não cadastrado na tela de Local de Impressão no ZillyonWeb.");
    	}
	}
	
	
}

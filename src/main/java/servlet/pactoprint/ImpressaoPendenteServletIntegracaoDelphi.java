package servlet.pactoprint;

import org.json.JSONObject;
import servlet.arquitetura.SuperServletIntegracaoDelphi;

import javax.servlet.http.HttpServletRequest;

public class ImpressaoPendenteServletIntegracaoDelphi extends SuperServletIntegracaoDelphi {

    @Override
    protected JSONObject processarRequisicao(HttpServletRequest request) {
        return new JSONObject(); // mock return - TODO pactoprint recuperar servlet impressao pendente do zw-tijuca caso necessario
    }
}

package servlet.pactoprint;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.pactoprint.CarteirinhaClienteVO;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.facade.jdbc.basico.Cliente;
import negocio.facade.jdbc.basico.Empresa;
import negocio.facade.jdbc.utilitarias.Conexao;
import org.apache.commons.codec.binary.Base64;
import org.json.JSONObject;
import servicos.operacoes.midias.MidiaService;
import servicos.operacoes.midias.commons.MidiaEntidadeEnum;
import servlet.arquitetura.SuperServletIntegracaoDelphi;

import javax.servlet.http.HttpServletRequest;
import java.io.BufferedReader;
import java.sql.Connection;
import java.util.*;

/**
 * date : 12/05/2015 14:48:06
 * autor: Ulisses
 */
public class CarteirinhaClienteServletIntegracaoDelphi extends SuperServletIntegracaoDelphi {

    @Override
    protected JSONObject processarRequisicao(HttpServletRequest request) throws Exception {
        String url = request.getRequestURL().toString();
        String tipoOperacao = request.getParameter("tipoOperacao");
        String key = request.getParameter("key");

        Connection con = null;
        con = new DAO().obterConexaoEspecifica(key);
        Conexao.guardarConexaoForJ2SE(con);

        Integer codigoCliente = null;
        Integer codigoEmpresa = null;

        if(request.getParameter("codigoCliente") != null) {
            codigoCliente = Integer.parseInt(request.getParameter("codigoCliente"));
        };

        if(request.getParameter("empresa") != null) {
            codigoEmpresa = Integer.parseInt(request.getParameter("empresa"));
        };

        switch (tipoOperacao) {
            case "IMPRESSAO":
                return processarImpressao(request, url);

            case "ATUALIZAR_IMPRESSAO_CARTEIRINHA":
                atualizarImpressaoCarteirinha(request);
                return new JSONObject().put("status", "carteirinha atualizada com sucesso");

            case "SOLICITAR_NOVA_CARTEIRINHA":
                solicitarNovaCarteirinha(request, con);
                return new JSONObject().put("status", "carteirinha solicitada com sucesso");

            case "CONSULTAR_CARTEIRINHA_CLIENTE":
                if (codigoCliente != null) {
                    return getAcessoControle().getCarteirinhaCliente()
                            .consultarCarteirinhaPorCodigoClienteFormatoJson(codigoCliente, codigoEmpresa, key);
                } else {
                    throw new ConsistirException("Código do cliente é obrigatório para esta operação.");
                }

            case "CONFIG_PACTO_PRINT":
                return new JSONObject().put("configUtilizaPactoPrint", isConfiguracaoUtilizaPactoprint(request, con));

            case "SALVAR_CARTEIRINHA_BASE64":
                salvarCarteirinhaBase64(request);
                return new JSONObject().put("status", "carteirinha salva como base64");

            case "CONSULTAR_HISTORICO":
                return new JSONObject().put("historico", consultarHistorico(request, con));

            default:
                throw new ConsistirException("Operação não reconhecida.");
        }
    }

    private JSONObject processarImpressao(HttpServletRequest request, String url) throws Exception {
        String codigoEmpresa = request.getParameter("codigoEmpresa");
        String dataHoraInicial = request.getParameter("dataHoraInicial");
        String dataHoraFim = request.getParameter("dataHoraFinal");
        String tipoConsulta = request.getParameter("tipoConsulta");
        String tipoCarteirinha = request.getParameter("tipoCarteirinha");

        validarParametrosImpressao(codigoEmpresa, dataHoraInicial, dataHoraFim, tipoConsulta);

        return getAcessoControle().getCarteirinhaCliente()
                .consultarCarteirinhaParaImpressao(
                        url.split("prest")[0],
                        getChave(),
                        Integer.parseInt(codigoEmpresa),
                        dataHoraInicial,
                        dataHoraFim,
                        tipoConsulta,
                        tipoCarteirinha
                );
    }

    private void salvarCarteirinhaBase64(HttpServletRequest request) throws Exception {
        StringBuilder stringBuilder = new StringBuilder();
        String line;

        BufferedReader reader = request.getReader();
        while ((line = reader.readLine()) != null) {
            stringBuilder.append(line);
        }

        JSONObject jsonBody = new JSONObject(stringBuilder.toString());
        String base64Carteirinha = jsonBody.optString("base64");
        Integer codigoCarteirinha = jsonBody.optInt("codigoCarteirinha");

        if (UteisValidacao.emptyString(base64Carteirinha)) {
            throw new ConsistirException("ERRO: Nenhum código Base64 foi fornecido.");
        }
        base64Carteirinha = base64Carteirinha.contains(",")
                ? base64Carteirinha.split(",")[1]
                : base64Carteirinha;
        base64Carteirinha = base64Carteirinha.replace(" ","+");


        String key = request.getParameter("key");

        String chaveArquivo = MidiaService.getInstance().uploadObjectFromByteArray(key,
                MidiaEntidadeEnum.CARTEIRINHA_CLIENTE_PACTO_PRINT, codigoCarteirinha + "_DOC", Base64.decodeBase64(base64Carteirinha));

        getAcessoControle().getCarteirinhaCliente().atualizarChaveArquivo(chaveArquivo, codigoCarteirinha);
        getAcessoControle().getCarteirinhaCliente().atualizarImpressaoCarteirinha(codigoCarteirinha);
    }



    private void atualizarImpressaoCarteirinha(HttpServletRequest request) throws Exception {
        String listaCodigo = request.getParameter("listaCodigo");

        for (String codigoCarteirinha: listaCodigo.split(",")) {
            getAcessoControle().getCarteirinhaCliente().atualizarImpressaoCarteirinha(Integer.parseInt(codigoCarteirinha));
            getAcessoControle().getCarteirinhaCliente().atualizarDataValidade(Integer.parseInt(codigoCarteirinha));
        }
    }

    private void validarParametrosImpressao(String codigoEmpresa, String dataHoraInicial, String dataHoraFim, String tipoConsulta) throws ConsistirException {
        if ((codigoEmpresa == null) || codigoEmpresa.trim().isEmpty()) {
            throw new ConsistirException("É necessário informar o código da empresa.");
        }

        if ((dataHoraInicial == null) || dataHoraInicial.trim().isEmpty() ||
                (dataHoraFim == null) || dataHoraFim.trim().isEmpty()) {
            throw new ConsistirException("É necessário informar o período das carteirinhas.");
        }

        if ((tipoConsulta == null) || tipoConsulta.trim().isEmpty()) {
            throw new ConsistirException("É necessário informar o tipo de consulta.");
        }
    }

    private void solicitarNovaCarteirinha(HttpServletRequest request, Connection con) throws Exception {
        Integer codigoCliente = Integer.parseInt(request.getParameter("codigoCliente"));
        Integer codigoUsuario = Integer.parseInt(request.getParameter("usuario"));
        Integer codigoEmpresa = Integer.parseInt(request.getParameter("empresa"));

        if (getAcessoControle().getCarteirinhaCliente().existeCarteirinhaPendenteDeImpressao(codigoCliente)) {
            throw new ConsistirException("Operação não permitida. Já existe uma carteirinha pendente de impressão para esse cliente");
        }

        Empresa empresaDao = new Empresa(con);
        EmpresaVO empresaVO = empresaDao.consultarPorCodigo(codigoEmpresa, Uteis.NIVELMONTARDADOS_DADOSBASICOS);

        String nomeEmpresa = getAcessoControle().getCarteirinhaCliente().obterNomeEmpresa(codigoCliente);
        Usuario userDao = new Usuario(con);
        UsuarioVO usuario = userDao.consultarPorCodigoUsuario(codigoUsuario, true, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        Cliente cliDao = new Cliente(con);
        ClienteVO clienteVO = cliDao.consultarPorCodigo(codigoCliente, true, Uteis.NIVELMONTARDADOS_DADOSBASICOS);

        if (UteisValidacao.emptyString(nomeEmpresa)) {
            throw new ConsistirException("Operação não realizada. O cliente não possui empresa vigente vinculada no cadastro.");
        }

        CarteirinhaClienteVO carteirinhaClienteVO = new CarteirinhaClienteVO();
        carteirinhaClienteVO.setClienteVO(clienteVO);
        carteirinhaClienteVO.setUsuarioVO(usuario);

        // Calcula dt de validade da carteirinha adicionando o número de meses na data atual
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        Integer qtdMesesValidade = empresaVO.getValidadeMesesCarteirinhaSocio();
        calendar.add(Calendar.MONTH, qtdMesesValidade);
        Date dataValidade = calendar.getTime();
        carteirinhaClienteVO.setDataValidadeCarteirinha(dataValidade);

        try {
            getAcessoControle().getCarteirinhaCliente().incluirNovaCarteirinha(carteirinhaClienteVO);
        } catch (Exception e) {
            throw new Exception("Erro ao incluir nova carteirinha: " + e.getMessage(), e);
        }

    }

    private List<HashMap<String, Object>> consultarHistorico(HttpServletRequest request, Connection con) throws Exception {
        String valorCampoVazio = "sem registro";
        Integer codigoCliente = Integer.parseInt(request.getParameter("codigoCliente"));
        Cliente cliDao = new Cliente(con);
        ClienteVO clienteVO = cliDao.consultarPorCodigo(codigoCliente, true, Uteis.NIVELMONTARDADOS_DADOSBASICOS);

        List<CarteirinhaClienteVO> carteirinhas = getAcessoControle().getCarteirinhaCliente()
                .consultarCarteirinhas(clienteVO, Uteis.NIVELMONTARDADOS_DADOSBASICOS);

        List<HashMap<String, Object>> historico = new ArrayList<>();
        for (CarteirinhaClienteVO carteirinha : carteirinhas) {
            HashMap<String, Object> map = new HashMap<>();
            map.put("codigo", carteirinha.getCodigo());
            map.put("dataLancamento", carteirinha.getDataLancamento() != null ? carteirinha.getDataLancamento() : valorCampoVazio);
            map.put("dataImpressao", carteirinha.getDataImpressao() != null ? carteirinha.getDataImpressao() : valorCampoVazio);
            map.put("dataValidadeCarteirinha", carteirinha.getDataValidadeCarteirinha() != null ? carteirinha.getDataValidadeCarteirinha() : valorCampoVazio);
            map.put("via", carteirinha.getVia() != null ? carteirinha.getVia() : valorCampoVazio);
            historico.add(map);
        }
        return historico;
    }

    private Boolean isConfiguracaoUtilizaPactoprint(HttpServletRequest request, Connection con) throws Exception {
        Integer codigoEmpresa = Integer.parseInt(request.getParameter("empresa"));

        Empresa empresaDao = new Empresa(con);
        EmpresaVO empresaVO = empresaDao.consultarPorCodigo(codigoEmpresa, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        return empresaVO.getUtilizarPactoPrint();

    }



}

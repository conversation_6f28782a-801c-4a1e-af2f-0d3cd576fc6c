package servlet.enotas;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.notaFiscal.NotaFiscal;
import negocio.facade.jdbc.notaFiscal.NotaFiscalOperacao;
import org.apache.commons.io.IOUtils;
import org.json.JSONObject;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.sql.Connection;
import java.util.Enumeration;

public class WebHookEnotasServlet extends HttpServlet {

    /**
     * Processes requests for both HTTP
     * <code>GET</code> and
     * <code>POST</code> methods.
     *
     * @param request  servlet request
     * @param response servlet response
     * @throws ServletException if a servlet-specific error occurs
     * @throws IOException      if an I/O error occurs
     */

    private static String ERROR = "error";
    private static String RETURN = "return";
    private static String RETURN_SUCESSO = "ok";

    protected void processRequest(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {

        Connection con = null;
        JSONObject jsonObject = new JSONObject();
        NotaFiscal notaDAO = null;
        NotaFiscalOperacao operacaoDAO = null;
        try {

            if (!UteisValidacao.emptyString(request.getHeader("key"))) {

                final String chave = request.getHeader("key");

                con = new DAO().obterConexaoEspecifica(chave);
                notaDAO = new NotaFiscal(con);
                operacaoDAO = new NotaFiscalOperacao(con);

            } else {
                throw new Exception("KEY NAO INFORMADA");
            }


            //incluir historico de retorno enotas
            String retorno = "";
            String headers = "";
            try {
                retorno = IOUtils.toString(request.getReader());

                try {
                    if (request.getHeaderNames() != null) {
                        Enumeration<String> headerNames = request.getHeaderNames();
                        if (headerNames != null) {
                            JSONObject jsonHeader = new JSONObject();
                            while (headerNames.hasMoreElements()) {
                                String headerName = headerNames.nextElement();
                                jsonHeader.put(headerName, request.getHeader(headerName));
                            }
                            headers = jsonHeader.toString();
                        }
                    }
                } catch (Exception ex) {
                    ex.printStackTrace();
                }

                notaDAO.incluirNotaFiscalWebHookHistorico(retorno, headers);
                Uteis.logar(true, null, "RETORNO ENOTAS: " + retorno);
            } catch (Exception ex) {
                Uteis.logar(true, null, "ERRO WEBHOOK: " + ex.getMessage());
            }


            final String idPacto = request.getHeader("idReferencia");
            final String codigoNotaFiscalZW = request.getHeader("codigoNotaFiscalZW");
            final String codigoOperacaoZW = request.getHeader("codigoOperacaoZW");
//            final String statusCode = request.getHeader("statusCode");
            final String idExterno = request.getHeader("idExterno");
            Integer codNotaFiscal;
            try {
                codNotaFiscal = Integer.parseInt(codigoNotaFiscalZW);
            } catch (Exception e) {
                codNotaFiscal = 0;
            }


            if (!UteisValidacao.emptyString(idExterno) &&
                    !UteisValidacao.emptyNumber(codNotaFiscal)) {

                notaDAO.registrarIdExterno(idExterno, codNotaFiscal);
                jsonObject.put(RETURN, RETURN_SUCESSO);


            } else if (!UteisValidacao.emptyNumber(codNotaFiscal) || !UteisValidacao.emptyString(idPacto)) {


                if (!UteisValidacao.emptyNumber(codNotaFiscal)) {
                    //TIVER COM O CÓDIGO DE NOTA FISCAL É PQ DEU ERRO NA EMISSÃO DA NOTA!

                    notaDAO.processarRetornoErroNaEmissao(retorno, Integer.parseInt(codigoNotaFiscalZW));
                    jsonObject.put(RETURN, RETURN_SUCESSO);

                } else if (!UteisValidacao.emptyString(idPacto)) {
                    //SE TIVER COM O idPacto É PQ É UM RETORNO DO ENOTAS!

                    notaDAO.processarRetornoWebHookEnotas(retorno, idPacto);
                    jsonObject.put(RETURN, RETURN_SUCESSO);
                }

            } else if (!UteisValidacao.emptyString(codigoOperacaoZW)) {

                Integer codNotaFiscalOperacao = Integer.parseInt(codigoOperacaoZW);

                if (!UteisValidacao.emptyNumber(codNotaFiscalOperacao)) {

                    operacaoDAO.processarRetornoOperacao(retorno, codNotaFiscalOperacao);
                    jsonObject.put(RETURN, RETURN_SUCESSO);
                }

            } else {
                throw new Exception("PARAMETROS NAO INFORMADOS");
            }

        } catch (Exception ex) {
            Uteis.logar(true, null, "ERRO - REQUISIÇÃO WebHookNotaFiscal: " + ex.getMessage());
            ex.printStackTrace();
            jsonObject.put(ERROR, ex.getMessage().toUpperCase());
        } finally {
            try {
                notaDAO = null;
                operacaoDAO = null;
                if (con != null) {
                    con.close();
                }
            } catch (Exception ignored) {
            }
        }
        response.setCharacterEncoding("UTF-8");
        response.setContentType("application/json");
        response.getOutputStream().print(jsonObject.toString());
    }

    // <editor-fold defaultstate="collapsed" desc="HttpServlet methods. Click on the + sign on the left to edit the code.">

    /**
     * Handles the HTTP
     * <code>GET</code> method.
     *
     * @param request  servlet request
     * @param response servlet response
     * @throws ServletException if a servlet-specific error occurs
     * @throws IOException      if an I/O error occurs
     */
    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        processRequest(request, response);
    }

    /**
     * Handles the HTTP
     * <code>POST</code> method.
     *
     * @param request  servlet request
     * @param response servlet response
     * @throws ServletException if a servlet-specific error occurs
     * @throws IOException      if an I/O error occurs
     */
    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        processRequest(request, response);
    }

    /**
     * Returns a short description of the servlet.
     *
     * @return a String containing servlet description
     */
    @Override
    public String getServletInfo() {
        return "Short description";
    }// </editor-fold>
}

package servlet.canalCliente;

import negocio.comuns.utilitarias.Uteis;
import org.json.JSONObject;

import java.util.Date;

/**
 * Created with IntelliJ IDEA.
 * User: <PERSON><PERSON>
 * Date: 13/06/2020
 */
public class InfoEmpresaDTO {

    private Integer codigo;
    private String chave;
    private String nome;
    private String cnpj;
    private String razaoSocial;
    private String endereco;
    private String complemento;
    private String numero;
    private String setor;
    private String cep;
    private String telcomercial1;
    private String telcomercial2;
    private String telcomercial3;
    private String email;
    private Integer codigoEmpresaFinanceiro;
    private String tokenSMSTransacional;
    private String tokenSMSMarketing;
    private String dataExpiracao;
    private String longitude;
    private String latitude;
    private String cidade;
    private String cidadeNomeSemAcendo;
    private String estado;
    private String estadoUF;
    private String pais;
    private Integer tipoCobrancaPacto;
    private String tipoCobrancaPactoDescricao;
    private Integer creditoDCCAtual;
    private boolean convenioCartaoCredito;
    private boolean convenioDebitoConta;
    private boolean convenioBoleto;
    private boolean reconhecimentoFacial;
    private boolean totem;
    private boolean notaFiscal;
    private boolean appGestor;


    public InfoEmpresaDTO() {
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getChave() {
        return chave;
    }

    public void setChave(String chave) {
        this.chave = chave;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getCnpj() {
        return cnpj;
    }

    public void setCnpj(String cnpj) {
        this.cnpj = cnpj;
    }

    public String getRazaoSocial() {
        return razaoSocial;
    }

    public void setRazaoSocial(String razaoSocial) {
        this.razaoSocial = razaoSocial;
    }

    public String getEndereco() {
        return endereco;
    }

    public void setEndereco(String endereco) {
        this.endereco = endereco;
    }

    public String getComplemento() {
        return complemento;
    }

    public void setComplemento(String complemento) {
        this.complemento = complemento;
    }

    public String getNumero() {
        return numero;
    }

    public void setNumero(String numero) {
        this.numero = numero;
    }

    public String getSetor() {
        return setor;
    }

    public void setSetor(String setor) {
        this.setor = setor;
    }

    public String getCep() {
        return cep;
    }

    public void setCep(String cep) {
        this.cep = cep;
    }

    public String getTelcomercial1() {
        return telcomercial1;
    }

    public void setTelcomercial1(String telcomercial1) {
        this.telcomercial1 = telcomercial1;
    }

    public String getTelcomercial2() {
        return telcomercial2;
    }

    public void setTelcomercial2(String telcomercial2) {
        this.telcomercial2 = telcomercial2;
    }

    public String getTelcomercial3() {
        return telcomercial3;
    }

    public void setTelcomercial3(String telcomercial3) {
        this.telcomercial3 = telcomercial3;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public Integer getCodigoEmpresaFinanceiro() {
        return codigoEmpresaFinanceiro;
    }

    public void setCodigoEmpresaFinanceiro(Integer codigoEmpresaFinanceiro) {
        this.codigoEmpresaFinanceiro = codigoEmpresaFinanceiro;
    }

    public String getDataExpiracao() {
        return dataExpiracao;
    }

    public void setDataExpiracao(String dataExpiracao) {
        this.dataExpiracao = dataExpiracao;
    }

    public Integer getTipoCobrancaPacto() {
        return tipoCobrancaPacto;
    }

    public void setTipoCobrancaPacto(Integer tipoCobrancaPacto) {
        this.tipoCobrancaPacto = tipoCobrancaPacto;
    }

    public String getLongitude() {
        return longitude;
    }

    public void setLongitude(String longitude) {
        this.longitude = longitude;
    }

    public String getLatitude() {
        return latitude;
    }

    public void setLatitude(String latitude) {
        this.latitude = latitude;
    }

    public String getCidade() {
        return cidade;
    }

    public void setCidade(String cidade) {
        this.cidade = cidade;
    }

    public String getCidadeNomeSemAcendo() {
        return cidadeNomeSemAcendo;
    }

    public void setCidadeNomeSemAcendo(String cidadeNomeSemAcendo) {
        this.cidadeNomeSemAcendo = cidadeNomeSemAcendo;
    }

    public String getEstado() {
        return estado;
    }

    public void setEstado(String estado) {
        this.estado = estado;
    }

    public String getEstadoUF() {
        return estadoUF;
    }

    public void setEstadoUF(String estadoUF) {
        this.estadoUF = estadoUF;
    }

    public String getPais() {
        return pais;
    }

    public void setPais(String pais) {
        this.pais = pais;
    }

    public String getTipoCobrancaPactoDescricao() {
        return tipoCobrancaPactoDescricao;
    }

    public void setTipoCobrancaPactoDescricao(String tipoCobrancaPactoDescricao) {
        this.tipoCobrancaPactoDescricao = tipoCobrancaPactoDescricao;
    }

    public Integer getCreditoDCCAtual() {
        return creditoDCCAtual;
    }

    public void setCreditoDCCAtual(Integer creditoDCCAtual) {
        this.creditoDCCAtual = creditoDCCAtual;
    }

    public boolean isConvenioCartaoCredito() {
        return convenioCartaoCredito;
    }

    public void setConvenioCartaoCredito(boolean convenioCartaoCredito) {
        this.convenioCartaoCredito = convenioCartaoCredito;
    }

    public boolean isConvenioDebitoConta() {
        return convenioDebitoConta;
    }

    public void setConvenioDebitoConta(boolean convenioDebitoConta) {
        this.convenioDebitoConta = convenioDebitoConta;
    }

    public boolean isConvenioBoleto() {
        return convenioBoleto;
    }

    public void setConvenioBoleto(boolean convenioBoleto) {
        this.convenioBoleto = convenioBoleto;
    }

    public boolean isReconhecimentoFacial() {
        return reconhecimentoFacial;
    }

    public void setReconhecimentoFacial(boolean reconhecimentoFacial) {
        this.reconhecimentoFacial = reconhecimentoFacial;
    }

    public boolean isTotem() {
        return totem;
    }

    public void setTotem(boolean totem) {
        this.totem = totem;
    }

    public boolean isNotaFiscal() {
        return notaFiscal;
    }

    public void setNotaFiscal(boolean notaFiscal) {
        this.notaFiscal = notaFiscal;
    }

    public boolean isAppGestor() {
        return appGestor;
    }

    public void setAppGestor(boolean appGestor) {
        this.appGestor = appGestor;
    }

    public String getTokenSMSTransacional() {
        return tokenSMSTransacional;
    }

    public void setTokenSMSTransacional(String tokenSMSTransacional) {
        this.tokenSMSTransacional = tokenSMSTransacional;
    }

    public String getTokenSMSMarketing() {
        return tokenSMSMarketing;
    }

    public void setTokenSMSMarketing(String tokenSMSMarketing) {
        this.tokenSMSMarketing = tokenSMSMarketing;
    }
}

package servlet.canalCliente;

import org.json.JSONObject;

public class OperacaoPactoStoreDTO {

    private String operacao;
    private Integer empresa;
    private Integer empresaFinanceiro;
    private Integer usuario;
    private String tokenSMSTransacional;
    private String tokenSMSMarketing;
    private String listaVenda;
    private Integer codigoFinanceiroPagador;
    private boolean somenteAtiva = true;
    //criar empresa nova
    private String nomeFantasia;
    private String razaoSocial;
    private String cnpj;
    private String endereco;
    private String cep;

    public OperacaoPactoStoreDTO() {
    }

    public OperacaoPactoStoreDTO(JSONObject json) {
        this.operacao = json.optString("operacao");
        this.empresa = json.optInt("empresa");
        this.empresaFinanceiro = json.optInt("empresaFinanceiro");
        this.usuario = json.optInt("usuario");
        this.tokenSMSTransacional = json.optString("tokenSMSTransacional");
        this.tokenSMSMarketing = json.optString("tokenSMSMarketing");
        this.listaVenda = json.optString("listaVenda");
        this.codigoFinanceiroPagador = json.optInt("codigoFinanceiroPagador");
        this.somenteAtiva = json.optBoolean("somenteAtiva", true);
        this.nomeFantasia = json.optString("nomeFantasia");
        this.razaoSocial = json.optString("razaoSocial");
        this.cnpj = json.optString("cnpj");
        this.endereco = json.optString("endereco");
        this.cep = json.optString("cep");
    }

    public String getOperacao() {
        if (operacao == null) {
            operacao = "";
        }
        return operacao;
    }

    public void setOperacao(String operacao) {
        this.operacao = operacao;
    }

    public Integer getEmpresa() {
        if (empresa == null) {
            empresa = 0;
        }
        return empresa;
    }

    public void setEmpresa(Integer empresa) {
        this.empresa = empresa;
    }

    public Integer getEmpresaFinanceiro() {
        return empresaFinanceiro;
    }

    public void setEmpresaFinanceiro(Integer empresaFinanceiro) {
        this.empresaFinanceiro = empresaFinanceiro;
    }

    public Integer getUsuario() {
        if (usuario == null) {
            usuario = 0;
        }
        return usuario;
    }

    public void setUsuario(Integer usuario) {
        this.usuario = usuario;
    }

    public String getTokenSMSTransacional() {
        if (tokenSMSTransacional == null) {
            tokenSMSTransacional = "";
        }
        return tokenSMSTransacional;
    }

    public void setTokenSMSTransacional(String tokenSMSTransacional) {
        this.tokenSMSTransacional = tokenSMSTransacional;
    }

    public String getTokenSMSMarketing() {
        if (tokenSMSMarketing == null) {
            tokenSMSMarketing = "";
        }
        return tokenSMSMarketing;
    }

    public void setTokenSMSMarketing(String tokenSMSMarketing) {
        this.tokenSMSMarketing = tokenSMSMarketing;
    }

    public String getListaVenda() {
        if (listaVenda == null) {
            listaVenda = "";
        }
        return listaVenda;
    }

    public void setListaVenda(String listaVenda) {
        this.listaVenda = listaVenda;
    }

    public Integer getCodigoFinanceiroPagador() {
        return codigoFinanceiroPagador;
    }

    public void setCodigoFinanceiroPagador(Integer codigoFinanceiroPagador) {
        this.codigoFinanceiroPagador = codigoFinanceiroPagador;
    }

    public boolean isSomenteAtiva() {
        return somenteAtiva;
    }

    public void setSomenteAtiva(boolean somenteAtiva) {
        this.somenteAtiva = somenteAtiva;
    }

    public String getNomeFantasia() {
        return nomeFantasia;
    }

    public void setNomeFantasia(String nomeFantasia) {
        this.nomeFantasia = nomeFantasia;
    }

    public String getRazaoSocial() {
        return razaoSocial;
    }

    public void setRazaoSocial(String razaoSocial) {
        this.razaoSocial = razaoSocial;
    }

    public String getCnpj() {
        return cnpj;
    }

    public void setCnpj(String cnpj) {
        this.cnpj = cnpj;
    }

    public String getEndereco() {
        return endereco;
    }

    public void setEndereco(String endereco) {
        this.endereco = endereco;
    }

    public String getCep() {
        return cep;
    }

    public void setCep(String cep) {
        this.cep = cep;
    }
}

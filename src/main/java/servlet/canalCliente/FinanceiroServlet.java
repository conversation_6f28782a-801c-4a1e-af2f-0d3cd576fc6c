package servlet.canalCliente;

import br.com.pactosolucoes.comuns.json.JSONMapper;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import cfin.BeanFin;
import cfin.wrapper.ParcelaConsultada;
import cfin.wrapper.TFavorecido;
import cfin.wrapper.TResultadoBoleto;
import cfin.wrapper.TResultadoParcelaConsultada;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.nfe.NFSeImprimirTO;
import negocio.comuns.nfe.NotasImprimirTO;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.basico.Empresa;
import negocio.facade.jdbc.financeiro.FinanceiroPacto;
import org.json.JSONArray;
import org.json.JSONObject;
import relatorio.controle.arquitetura.SuperControleRelatorio;
import servicos.integracao.adm.beans.EmpresaWS;
import servicos.propriedades.PropsService;
import servicos.util.ExecuteRequestHttpService;
import servlet.arquitetura.SuperServlet;
import ws.TResultadoTransacaoWS;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.sql.Connection;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class FinanceiroServlet extends SuperServlet {

    private static String CRIPTO_NOTA = "F1N4NC31R0";

    @Override
    protected void processRequest(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {

        response.addHeader("Access-Control-Allow-Origin", "*");
        response.addHeader("Access-Control-Allow-Methods", "GET");
        response.setContentType("application/json;charset=UTF-8");

        String path = request.getServletPath();
        String[] pathParts = path.split("/");
        String modulo = pathParts[2];
        String recurso = pathParts[3];

        String chave = request.getParameter("chave");
        String empresa = request.getParameter("empresa");
        String qtde = request.getParameter("qtde");
        String parcela = request.getParameter("parcela");
        String calcularCobrancasExtras = request.getParameter("parcela");
        String codigosParcelas = request.getParameter("parcelas");
        String cnpj = request.getParameter("cnpj");

        try {

            switch (recurso) {
                case "consultar-parcelas":
                    conultarFaturas(request, response, chave, Integer.parseInt(empresa), Integer.parseInt(qtde));
                    break;
                case "emitir-boleto":
                    emitirBoleto(request, response, chave, Integer.parseInt(parcela), Boolean.valueOf(calcularCobrancasExtras));
                    break;
                case "consultar-notas":
                    consultarNotas(request, response, chave, codigosParcelas);
                    break;
                case "imprimir-nota":
                    imprimirNota(request, response);
                    break;
                case "consultar-cnpj":
                    consultarEmpresa(response, cnpj);
                    break;
                case "consultar-empresa":
                    conultarEmpresas(response, chave);
                    break;
            }

        } catch (Exception e) {
            System.out.println("Erro na api rest " + recurso + ". Message: " + e.getMessage());
            processarErro(e, request, response, null);
        }

    }

    private void emitirBoleto(HttpServletRequest request, HttpServletResponse response, String chave, Integer parcela, Boolean calcularCobrancasExtras) throws Exception {
        Connection con = null;
        FinanceiroPacto financeiroPacto = null;
        JSONObject result = new JSONObject();
        try {
            con = new DAO().obterConexaoEspecifica(chave);
            financeiroPacto = new FinanceiroPacto(con);


            TResultadoBoleto resultBoleto = financeiroPacto.regerarParcela(parcela, calcularCobrancasExtras);
            String pathArquivo = resultBoleto.getNomeArquivo();
            String urlBoleto = "";
            if (!pathArquivo.isEmpty() && pathArquivo != null) {
                urlBoleto = Uteis.getUrlBoletosFinanceiroPacto() + pathArquivo;
            }

            result.put("url", urlBoleto);
            response.getWriter().append(result.toString());
        } catch (Exception ex) {
            ex.printStackTrace();
            result.put("url", "Boleto não gerado. Aguarde uns dias ou entre em contato com o Financeiro da Pacto.");
            response.getWriter().append(result.toString());
            throw new Exception("Boleto não gerado. Aguarde uns dias ou entre em contato com o Financeiro da Pacto.");
        } finally {
            financeiroPacto = null;
            try {
                if (con != null) {
                    con.close();
                }
            } catch (Exception ex) {
                ex.printStackTrace();
            }
        }
    }

    private void conultarFaturas(HttpServletRequest request, HttpServletResponse response, String chave, Integer empresa, Integer qtde) throws Exception {
        FinanceiroPacto financeiroPacto;
        try {
            financeiroPacto = new FinanceiroPacto();
            JSONArray result = new JSONArray();
            TResultadoParcelaConsultada resultFinanceiro = financeiroPacto.obterParcelasAcademia(chave, empresa, qtde);
            for (ParcelaConsultada parcela : resultFinanceiro.getParcelas()) {
                JSONObject parcelaJSON = new JSONObject();
                parcelaJSON.put("codigo", parcela.getCodigoParcela());
                parcelaJSON.put("descricao", parcela.getDescricao());
                parcelaJSON.put("vencimento", parcela.getDataVencimentoApresentar());
                parcelaJSON.put("valor", parcela.getValor());
                parcelaJSON.put("urlBoleto", !UteisValidacao.emptyString(parcela.getUrlBoleto())
                        ? Uteis.getUrlBoletosFinanceiroPacto() + parcela.getUrlBoleto() : "");
                parcelaJSON.put("situacao", parcela.getSituacao());
                result.put(parcelaJSON);
            }
            response.getWriter().append(result.toString());
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        } finally {
            financeiroPacto = null;
        }
    }

    private void consultarNotas(HttpServletRequest request, HttpServletResponse response, String chave, String codigosParcelaParam) throws Exception {
        try {
            JSONArray result = new JSONArray();

            List<NotaFiscalDTO> notas = consultarNotasIntegradorFinanceiro(codigosParcelaParam);
            for (NotaFiscalDTO notaFiscalDTO : notas) {
                try {
                    JSONObject nfeJSON = new JSONObject();
                    nfeJSON.put("parcela", notaFiscalDTO.getId_agendparcela().toString());
                    if (!UteisValidacao.emptyNumber(notaFiscalDTO.getId_rps())) {
                        //se não tem id é pq é o modelo antigo

                        nfeJSON.put("nota", notaFiscalDTO.getId_rps());

                        JSONObject json = new JSONObject();
                        json.put("id_rps", notaFiscalDTO.getId_rps());
                        String token = Uteis.encriptar(json.toString(), CRIPTO_NOTA);
                        String urlServletImprimir = "" ;
                        try {
                            String urlAplicacao = Uteis.getUrlAplicacao();
                            if (UteisValidacao.emptyString(urlAplicacao) || urlAplicacao.startsWith("@")) {
                                urlServletImprimir = request.getRequestURL().toString().replace("consultar-notas", "imprimir-nota");
                            } else {
                                urlServletImprimir = (urlAplicacao + "/prest/canalCliente/imprimir-nota");
                            }
                        } catch (Exception ex) {
                            ex.printStackTrace();
                        }
                        nfeJSON.put("url", urlServletImprimir + "?tk=" + token);
                    } else {
                        nfeJSON.put("nota", notaFiscalDTO.getId());
                        nfeJSON.put("url", notaFiscalDTO.getLinkPdf());
                    }
                    result.put(nfeJSON);
                } catch (Exception ex) {
                    ex.printStackTrace();
                }
            }

            response.getWriter().append(result.toString());
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        }
    }

    private List<NotaFiscalDTO> consultarNotasIntegradorFinanceiro(String codigosParcelaParam) {
        try {
            if (UteisValidacao.emptyString(codigosParcelaParam)) {
                return new ArrayList<>();
            }

            String url = PropsService.getPropertyValue(PropsService.urlIntegradorOamd) + "/nota-fiscal/find-by-id-agendparcela?idAgendparcela=" + codigosParcelaParam;
            String response = ExecuteRequestHttpService.executeRequestGet(url, new HashMap<>());
            if (response.contains("content")) {
                JSONObject jsonResp = new JSONObject(response);
                if (jsonResp.has("content")) {
                    return JSONMapper.getList(jsonResp.getJSONArray("content"), NotaFiscalDTO.class);
                }
            }
            throw new Exception(response);
        } catch (Exception ex) {
            ex.printStackTrace();
            return new ArrayList<>();
        }
    }

    private void consultarEmpresa(HttpServletResponse response, String cnpj) throws Exception {
        BeanFin bean = new BeanFin(Uteis.getUrlFinanceiroPacto());
        TFavorecido tFavorecido = bean.consultarFavorecido(Uteis.removerMascara(cnpj));
        JSONObject json = new JSONObject(tFavorecido);
        json.put("empresaCadastrada", tFavorecido.getResultado().equals(TResultadoTransacaoWS.rtOK));
        response.getWriter().append(json.toString());
    }

    private void conultarEmpresas(HttpServletResponse response, String chave) throws Exception {
        Connection con = null;
        Empresa empresa = null;
        try {
            con = new DAO().obterConexaoEspecifica(chave);
            empresa = new Empresa(con);

            List<EmpresaWS> empresaWSArrayList = new ArrayList<EmpresaWS>();
            List<EmpresaVO> listEmpresas = empresa.consultarEmpresas(Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            for (EmpresaVO obj : listEmpresas) {
                empresaWSArrayList.add(new EmpresaWS(obj));
            }
            response.getWriter().append(new JSONArray(empresaWSArrayList).toString());
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        } finally {
            empresa = null;
            try {
                if (con != null) {
                    con.close();
                }
            } catch (Exception ex) {
                ex.printStackTrace();
            }
        }
    }

    private NotasImprimirTO consultarNotaImprimirIntegradorFinanceiro(Integer id_rps) throws Exception {
        NotasImprimirTO notasImprimirTO = new NotasImprimirTO();
        String url = PropsService.getPropertyValue(PropsService.urlIntegradorOamd) + "/nota-fiscal/nota-imprimir/" + id_rps;
        String response = ExecuteRequestHttpService.executeRequestGet(url, new HashMap<>());
        if (response.contains("content")) {
            JSONObject jsonResp = new JSONObject(response);
            if (jsonResp.has("content")) {
                NFSeImprimirTO nfSeImprimirTO = JSONMapper.getObject(jsonResp.getJSONObject("content"), NFSeImprimirTO.class);
                notasImprimirTO.setNotaNFSe(nfSeImprimirTO);
            }
        }
        return notasImprimirTO;
    }

    private void imprimirNota(HttpServletRequest request, HttpServletResponse response) throws Exception {

        String token = request.getParameter("tk");
        JSONObject json = new JSONObject(Uteis.desencriptar(token, CRIPTO_NOTA));
        Integer id_rps = json.getInt("id_rps");

        NotasImprimirTO notasImprimirTO = consultarNotaImprimirIntegradorFinanceiro(id_rps);

        //GERAR SOMENTE DE 1 ARQUIVO

        String arquivo = gerarArquivoPDFNotaFiscal(notasImprimirTO, request);
        File pdfFile = new File(request.getRealPath("relatorio") + File.separator + arquivo);
        String nomeDownload = notasImprimirTO.getNomeArquivo() + ".pdf";

        response.setContentType("application/pdf");
        response.setHeader("Content-Disposition", "attachment; filename=" + nomeDownload);
        response.setContentLength((int) pdfFile.length());

        FileInputStream fileInputStream = new FileInputStream(pdfFile);
        OutputStream responseOutputStream = response.getOutputStream();
        int bytes;
        while ((bytes = fileInputStream.read()) != -1) {
            responseOutputStream.write(bytes);
        }

    }

    private String gerarArquivoPDFNotaFiscal(NotasImprimirTO nota, HttpServletRequest request) throws Exception {
        preencherLogomarcas(nota, request);

        List<NFSeImprimirTO> lista = new ArrayList<>();
        lista.add(nota.getNotaNFSe());

        Map<String, Object> params = new HashMap<String, Object>();
        params.put("nomeDesignIReport", "relatorio" + File.separator + "designRelatorio" + File.separator + "nfse" + File.separator + "NFSe.jrxml");
        params.put("SUBREPORT_DIR", "relatorio" + File.separator + "designRelatorio" + File.separator + "nfse" + File.separator);
        params.put("tipoRelatorio", "PDF");
        params.put("tipoImplementacao", "OBJETO");
        params.put("nomeRelatorio", "NFSE");
        params.put("nomeEmpresa", "");
        params.put("listaObjetos", lista);
        return new SuperControleRelatorio().imprimirNotaFiscal(request, params);
    }

    private void preencherLogomarcas(NotasImprimirTO nota, HttpServletRequest request) throws Exception {
        nota.getNotaNFSe().setLogomarcaPrefeitura(getImagemPadrao(request, true));
        nota.setLogomarcaPrestador(getImagemPadrao(request, false));
    }

    private InputStream getImagemPadrao(HttpServletRequest request, boolean prefeitura) throws Exception {
        String caminho = request.getRealPath("images") + File.separator + (prefeitura ? "logo_nfse_prefe_goiania.jpg" : "logo_nfse_pacto.png");
        File imagem = new File(caminho);
        ByteArrayOutputStream arrayOutputStream = new ByteArrayOutputStream();
        byte buffer[] = new byte[4096];
        int bytesRead = 0;
        FileInputStream fi = new FileInputStream(imagem.getAbsolutePath());
        while ((bytesRead = fi.read(buffer)) != -1) {
            arrayOutputStream.write(buffer, 0, bytesRead);
        }
        byte[] a = (arrayOutputStream.toByteArray());
        InputStream fs = new ByteArrayInputStream(a);
        arrayOutputStream.close();
        fi.close();
        return fs;
    }
}

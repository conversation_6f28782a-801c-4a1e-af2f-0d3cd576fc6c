package servlet.canalCliente;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class NotaFiscalDTO {

    private Integer id;
    private Integer id_agendparcela;
    private String id_enotas;
    private Integer id_rps;
    private String status;
    private String linkPdf;
    private String linkXml;
    private String mensagem;

    public String getId_enotas() {
        return id_enotas;
    }

    public void setId_enotas(String id_enotas) {
        this.id_enotas = id_enotas;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getLinkPdf() {
        return linkPdf;
    }

    public void setLinkPdf(String linkPdf) {
        this.linkPdf = linkPdf;
    }

    public String getLinkXml() {
        return linkXml;
    }

    public void setLinkXml(String linkXml) {
        this.linkXml = linkXml;
    }

    public String getMensagem() {
        return mensagem;
    }

    public void setMensagem(String mensagem) {
        this.mensagem = mensagem;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getId_agendparcela() {
        return id_agendparcela;
    }

    public void setId_agendparcela(Integer id_agendparcela) {
        this.id_agendparcela = id_agendparcela;
    }

    public Integer getId_rps() {
        return id_rps;
    }

    public void setId_rps(Integer id_rps) {
        this.id_rps = id_rps;
    }
}

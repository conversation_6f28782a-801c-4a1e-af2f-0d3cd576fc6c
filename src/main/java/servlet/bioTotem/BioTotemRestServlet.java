package servlet.bioTotem;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.plano.enumerador.TipoProduto;
import negocio.comuns.utilitarias.Calendario;
import negocio.facade.jdbc.basico.Cliente;
import negocio.facade.jdbc.plano.Produto;
import org.json.JSONObject;
import servlet.arquitetura.SuperServlet;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.sql.Connection;
import java.util.Date;

public class BioTotemRestServlet extends SuperServlet {

    @Override
    protected void processRequest(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {

        response.addHeader("Access-Control-Allow-Origin", "*");
        response.addHeader("Access-Control-Allow-Methods", "GET");
        response.setContentType("application/json");
        String chave = request.getParameter("chave");
        Integer empresa = request.getParameter("empresa") != null ?
                Integer.valueOf(request.getParameter("empresa")) : null;
        Integer codCliente = Integer.valueOf(request.getParameter("codCliente"));
        Date hoje = Calendario.hoje();

        try {
            boolean empresaVendeBioTotem;
            int quantidadeProdutoBioTotemVendaAvulsa;
            int quantidadeProdutoBioTotemContrato;
            try (Connection getConnection = getCon(chave)) {
                Cliente cliente = new Cliente(getConnection);
                Produto produto = new Produto(getConnection);
                empresaVendeBioTotem = produto.consultarExistePorCodigoTipoProduto(TipoProduto.BIO_TOTEM.getCodigo());
                quantidadeProdutoBioTotemVendaAvulsa = cliente.consultarQuantidadeProdutoVigenteVendaAvulsa(TipoProduto.BIO_TOTEM.getCodigo(), hoje, codCliente, empresa, true);
                quantidadeProdutoBioTotemContrato = cliente.consultarQuantidadeProdutoVigenteContrato(TipoProduto.BIO_TOTEM.getCodigo(), hoje, codCliente, empresa, true);
            }

            JSONObject jsonBool = new JSONObject();
            jsonBool.put("empresaVendeBioTotem",empresaVendeBioTotem);
            jsonBool.put("quantidadeProdutoBioTotemTotal", quantidadeProdutoBioTotemVendaAvulsa + quantidadeProdutoBioTotemContrato);
            jsonBool.put("quantidadeProdutoBioTotemVendaAvulsa", quantidadeProdutoBioTotemVendaAvulsa);
            jsonBool.put("quantidadeProdutoBioTotemContrato", quantidadeProdutoBioTotemContrato);
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("retorno",jsonBool);
            response.getWriter().append(jsonObject.toString());
        }catch (Exception e ){
            System.out.println("Erro na api rest BioTotem. Message: " + e.getMessage());
            processarErro(e, request, response, null);
        }

    }

    private Connection getCon(String chave) throws Exception {
        return new DAO().obterConexaoEspecifica(chave);
    }

}

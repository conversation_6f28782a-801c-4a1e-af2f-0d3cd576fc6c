package servlet.caixaemaberto;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import com.google.common.net.HttpHeaders;
import controle.arquitetura.exceptions.ParametroInvalidoException;
import controle.arquitetura.exceptions.ParametroObrigatorioException;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.MovimentoContaCorrenteClienteVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.financeiro.FormaPagamentoVO;
import negocio.comuns.financeiro.MovPagamentoVO;
import negocio.comuns.financeiro.MovParcelaVO;
import negocio.comuns.financeiro.ReciboPagamentoVO;
import negocio.comuns.financeiro.enumerador.TipoFormaPagto;
import negocio.comuns.financeiro.enumerador.TipoOperacaoContaCorrenteEnum;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.facade.jdbc.financeiro.FormaPagamento;
import negocio.facade.jdbc.financeiro.MovPagamento;
import negocio.facade.jdbc.utilitarias.Conexao;
import org.json.JSONArray;
import org.json.JSONObject;
import servlet.arquitetura.SuperServlet;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.sql.Connection;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

public class FormasPagamentoServlet extends SuperServlet {

    @Override
    protected void processRequest(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {

        response.setHeader("Access-Control-Allow-Origin", "*");
        response.setHeader("Access-Control-Allow-Methods", "GET, OPTIONS");
        response.setHeader("Access-Control-Max-Age", "3600");
        response.setHeader("Access-Control-Allow-Headers", "Authorization, content-type, xsrf-token");
        response.addHeader("Access-Control-Expose-Headers", "xsrf-token");
        response.setContentType("application/json");

        try{
            validarAuthorizationComChave(request);
            validarParametrosObrigatorios(request, new String[]{"empresa", "tiposFormaPagamento"});

            String chave = request.getHeader(HttpHeaders.AUTHORIZATION);
            Integer empresa = Uteis.converterInteiro(request.getParameter("empresa"));
            Boolean pinpad = Boolean.parseBoolean(request.getParameter("pinpad"));
            String tiposFormaPagamento = request.getParameter("tiposFormaPagamento");

            List<TipoFormaPagto> tipoFormaPagtosEnum = converterParametroTiposFormaPagamento(tiposFormaPagamento);

            Connection connection = new DAO().obterConexaoEspecifica(chave);
            Conexao.guardarConexaoForJ2SE(chave, connection);

            FormaPagamento formaPagamentoDao = new FormaPagamento(connection);

            List<FormaPagamentoVO> formaPagamentoVOS = formaPagamentoDao.consultarPorTiposFormaPagamento(
                    tipoFormaPagtosEnum,
                    empresa,
                    true,
                    true,
                    pinpad,
                    Uteis.NIVELMONTARDADOS_DADOSBASICOS);

            response.setCharacterEncoding("UTF-8");
            response.getWriter().append(converterFormasPagamento(formaPagamentoVOS).toString());

            formaPagamentoDao = null;
        } catch (Exception e) {
            processarErro(e, request, response, null);
        }
    }

    private List<TipoFormaPagto> converterParametroTiposFormaPagamento(String siglasFormaPagamento) throws ParametroInvalidoException {
        List<String> siglas = Arrays.asList(siglasFormaPagamento.split(","));
        List<TipoFormaPagto> tipoFormaPagtosEnum = new ArrayList<>();
        for(String sigla: siglas){
            if(sigla == null || sigla.trim().isEmpty()){
                new ParametroInvalidoException("A sigla de forma de pagamento "+siglasFormaPagamento+" é inválida.");
            }else{
                tipoFormaPagtosEnum.add(TipoFormaPagto.getTipoFormaPagtoSigla(sigla));
            }
        }

        return tipoFormaPagtosEnum;
    }

    private JSONArray converterFormasPagamento(List<FormaPagamentoVO> formaPagamentoVOS) {
        JSONArray formasPagemntoJSON = new JSONArray();

        for(FormaPagamentoVO formaPagamentoVO: formaPagamentoVOS){
            formasPagemntoJSON.put(formaPagamentoVO.toJSON());
        }

        return formasPagemntoJSON;
    }

    private void validarParametroDatas(Date dataInicio, Date dataFim) throws ParametroObrigatorioException {
        Integer periodoConsulta = Calendario.diferencaEmDias(dataInicio,dataFim);
        if(periodoConsulta > 31 ){
            throw new ParametroObrigatorioException("O período máximo permitido de consulta é de 31 dias. Sua consulta contém "+periodoConsulta+" dias.");
        }
    }
}

package servlet.caixaemaberto;

import controle.arquitetura.exceptions.ServiceException;
import negocio.comuns.utilitarias.Uteis;
import org.json.JSONArray;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;

public class PagamentoDTO {

    private Integer contrato;
    private Integer vendaAvulsa;
    private Integer usuario;
    private Boolean pagamentoIntegral;
    private Integer aulaAvulsa;
    private List<Integer> parcelasContrato;
    private List<Integer> parcelasVendaAvulsa;
    private List<FormaPagamentoDTO> formasPagamento;
    private List<Integer> aulasAvulsas;
    public PagamentoDTO(JSONObject jsonBody, Boolean pactoApp) throws ServiceException {
        this.contrato = Uteis.getParamJsonInt(jsonBody, "contrato");
        this.usuario = jsonBody.getInt("usuario");
        if(!pactoApp) {
            this.vendaAvulsa = Uteis.getParamJsonInt(jsonBody, "vendaAvulsa");
            this.aulaAvulsa = Uteis.getParamJsonInt(jsonBody, "aulaAvulsa");
        } else {
            this.parcelasContrato = obtemListRequest(jsonBody, "parcelasContrato");
            if (jsonBody.has("aulaAvulsa")) {
                this.aulaAvulsa = jsonBody.getInt("aulaAvulsa");
            }
            if (jsonBody.has("parcelasVendaAvulsa")) {
                this.parcelasVendaAvulsa = obtemListRequest(jsonBody, "parcelasVendaAvulsa");
            }
        }
        if (jsonBody.has("pagamentoIntegral")) {
            this.pagamentoIntegral = jsonBody.getBoolean("pagamentoIntegral");
        }

        convertFormasPagamentoJSON(jsonBody.getJSONArray("formasPagamento"));
    }

    public PagamentoDTO(PagamentoDTO pagamentoDTO) {
        this.contrato = pagamentoDTO.contrato;
        this.vendaAvulsa = pagamentoDTO.vendaAvulsa;
        this.usuario = pagamentoDTO.usuario;
        this.pagamentoIntegral = pagamentoDTO.pagamentoIntegral;
        this.aulaAvulsa = pagamentoDTO.aulaAvulsa;

        this.formasPagamento = new ArrayList<>();
        for (FormaPagamentoDTO forma : pagamentoDTO.formasPagamento) {
            this.formasPagamento.add(new FormaPagamentoDTO(forma));
        }
    }

    private List<Integer> obtemListRequest(JSONObject jsonBody, String campo) throws ServiceException {
        List<Integer> listaCodigos = new ArrayList<>();

        if (jsonBody.optJSONArray(campo) != null) {
            JSONArray jsonArray = jsonBody.optJSONArray(campo);
            for (int i = 0; i < jsonArray.length(); i++) {
                Integer elemento = jsonArray.optInt(i);
                listaCodigos.add(elemento);
            }
        } else {
            throw new ServiceException("Para pagamentos do pacto App, é necessário informar uma lista no campo " + campo);
        }

        return listaCodigos;
    }

    public List<Integer> getParcelasContrato() {
        return parcelasContrato;
    }

    public void setParcelasContrato(List<Integer> parcelasContrato) {
        this.parcelasContrato = parcelasContrato;
    }

    private void convertFormasPagamentoJSON(JSONArray jsonArray) {
        this.formasPagamento = new ArrayList<>();
        for (int i = 0; i < jsonArray.length(); i++) {
            this.formasPagamento.add(new FormaPagamentoDTO((JSONObject) jsonArray.get(i)));
        }
    }

    public Integer getContrato() {
        return contrato;
    }

    public void setContrato(Integer contrato) {
        this.contrato = contrato;
    }

    public Integer getUsuario() {
        return usuario;
    }

    public void setUsuario(Integer usuario) {
        this.usuario = usuario;
    }

    public List<FormaPagamentoDTO> getFormasPagamento() {
        return formasPagamento;
    }

    public void setFormasPagamento(List<FormaPagamentoDTO> formasPagamento) {
        this.formasPagamento = formasPagamento;
    }

    public Integer getVendaAvulsa() {
        return vendaAvulsa;
    }
    public void setVendaAvulsa(Integer vendaAvulsa) {
        this.vendaAvulsa = vendaAvulsa;
    }

    public Boolean getPagamentoIntegral() {
        return pagamentoIntegral;
    }

    public void setPagamentoIntegral(Boolean pagamentoIntegral) {
        this.pagamentoIntegral = pagamentoIntegral;
    }

    public List<Integer> getAulasAvulsas() {
        return aulasAvulsas;
    }

    public void setAulasAvulsas(List<Integer> aulasAvulsas) {
        this.aulasAvulsas = aulasAvulsas;
    }

    public Integer getAulaAvulsa() {
        return aulaAvulsa;
    }

    public void setAulaAvulsa(Integer aulaAvulsa) {
        this.aulaAvulsa = aulaAvulsa;
    }

    public List<Integer> getParcelasVendaAvulsa() {
        return parcelasVendaAvulsa;
    }

    public void setParcelasVendaAvulsa(List<Integer> parcelasVendaAvulsa) {
        this.parcelasVendaAvulsa = parcelasVendaAvulsa;
    }
}

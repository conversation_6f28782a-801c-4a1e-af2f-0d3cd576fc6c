package servlet.caixaemaberto;

import org.json.JSONArray;
import org.json.JSONObject;

public class FormaPagamentoDTO {


    private Integer codigoFormaPagamento;
    private Double valor;
    private Integer numeroParcelasCartao;
    private String autorizacaoCartao;
    private Integer codigoAdquirente;
    private Integer codigoOperadoraCartao;

    public FormaPagamentoDTO(JSONObject jsonBody) {
        this.codigoFormaPagamento = jsonBody.getInt("codigoFormaPagamento");
        this.valor = jsonBody.getDouble("valor");
        this.numeroParcelasCartao = jsonBody.optInt("numeroParcelasCartao");
        this.autorizacaoCartao = jsonBody.optString("autorizacaoCartao");
        this.codigoAdquirente = jsonBody.optInt("codigoAdquirente");
        this.codigoOperadoraCartao = jsonBody.optInt("codigoOperadoraCartao");
    }

    public FormaPagamentoDTO(FormaPagamentoDTO formaPagamentoDTO) {
        this.codigoFormaPagamento = formaPagamentoDTO.codigoFormaPagamento;
        this.valor = formaPagamentoDTO.valor;
        this.numeroParcelasCartao = formaPagamentoDTO.numeroParcelasCartao;
        this.autorizacaoCartao = formaPagamentoDTO.autorizacaoCartao;
        this.codigoAdquirente = formaPagamentoDTO.codigoAdquirente;
        this.codigoOperadoraCartao = formaPagamentoDTO.codigoOperadoraCartao;
    }

    public Integer getCodigoFormaPagamento() {
        return codigoFormaPagamento;
    }

    public void setCodigoFormaPagamento(Integer codigoFormaPagamento) {
        this.codigoFormaPagamento = codigoFormaPagamento;
    }

    public Double getValor() {
        return valor;
    }

    public void setValor(Double valor) {
        this.valor = valor;
    }

    public Integer getNumeroParcelasCartao() {
        return numeroParcelasCartao;
    }

    public void setNumeroParcelasCartao(Integer numeroParcelasCartao) {
        this.numeroParcelasCartao = numeroParcelasCartao;
    }

    public String getAutorizacaoCartao() {
        return autorizacaoCartao;
    }

    public void setAutorizacaoCartao(String autorizacaoCartao) {
        this.autorizacaoCartao = autorizacaoCartao;
    }

    public Integer getCodigoAdquirente() {
        return codigoAdquirente;
    }

    public void setCodigoAdquirente(Integer codigoAdquirente) {
        this.codigoAdquirente = codigoAdquirente;
    }

    public Integer getCodigoOperadoraCartao() {
        return codigoOperadoraCartao;
    }

    public void setCodigoOperadoraCartao(Integer codigoOperadoraCartao) {
        this.codigoOperadoraCartao = codigoOperadoraCartao;
    }
}

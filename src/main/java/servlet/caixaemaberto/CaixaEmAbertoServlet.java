package servlet.caixaemaberto;

import br.com.pactosolucoes.estrutura.paginacao.ConfPaginacao;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import com.google.common.net.HttpHeaders;
import com.sun.xml.fastinfoset.stax.events.Util;
import controle.arquitetura.exceptions.ParametroObrigatorioException;
import negocio.comuns.financeiro.CaixaAbertoTO;
import negocio.comuns.financeiro.MovParcelaVO;
import negocio.facade.jdbc.financeiro.MovParcela;
import negocio.facade.jdbc.utilitarias.Conexao;
import org.json.JSONArray;
import org.json.JSONObject;
import servlet.arquitetura.SuperServlet;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.sql.Connection;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class CaixaEmAbertoServlet extends SuperServlet {

    @Override
    protected void processRequest(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {

        response.setHeader("Access-Control-Allow-Origin", "*");
        response.setHeader("Access-Control-Allow-Methods", "GET, OPTIONS");
        response.setHeader("Access-Control-Max-Age", "3600");
        response.setHeader("Access-Control-Allow-Headers", "Authorization, content-type, xsrf-token");
        response.addHeader("Access-Control-Expose-Headers", "xsrf-token");
        response.setContentType("application/json");

        try {
            validarAuthorizationComChave(request);

            String chave = request.getHeader(HttpHeaders.AUTHORIZATION);
            try (Connection connection = new DAO().obterConexaoEspecifica(chave)) {
                Conexao.guardarConexaoForJ2SE(chave, connection);

                JSONObject body = getJSONBody(request);
                String matricula = body.optString("matricula");
                String nomeCliente = body.optString("nomeCliente");
                String produto = body.optString("produto");
                Integer codEmpresa = body.optInt("codEmpresa");
                Integer itensPorPagina = body.optInt("itensPorPagina") == 0 ? 1000 : body.optInt("itensPorPagina");
                Integer pagina = body.optInt("pagina");
                Date dataInicio = getDataFromJSON(body, "dataInicio");
                Date dataFinal = getDataFromJSON(body, "dataFinal");
                Boolean incluirParcelasRecorrencia = body.optBoolean("incluirParcelasRecorrencia");
                Boolean ordenarPorLancamento = body.optBoolean("ordenarPorLancamento");
                Boolean naoApresentarVencimentosDeMesesFuturos = body.optBoolean("naoApresentarVencimentosDeMesesFuturos");
                List<Integer> parcelasSelecionadas = new ArrayList<>();

                ConfPaginacao confPaginacao = new ConfPaginacao();
                confPaginacao.setItensPorPagina(itensPorPagina);
                confPaginacao.setPaginarBanco(true);

                MovParcela movParcela = new MovParcela(connection);

                List<CaixaAbertoTO> caixaAbertoTOS = movParcela.consultaCaixaEmAberto(matricula, nomeCliente, produto,
                        codEmpresa, dataInicio, dataFinal, confPaginacao, incluirParcelasRecorrencia, ordenarPorLancamento, parcelasSelecionadas, naoApresentarVencimentosDeMesesFuturos, pagina);

                JSONArray jsonArray = new JSONArray();

                for (CaixaAbertoTO caixaAbertoTO : caixaAbertoTOS) {
                    JSONObject jsonObject = new JSONObject();
                    jsonObject.put("contrato", caixaAbertoTO.getContrato());
                    jsonObject.put("diaria", caixaAbertoTO.getDiaria());
                    jsonObject.put("vendaAvulsa", caixaAbertoTO.getVendaAvulsa());
                    jsonObject.put("pessoa", caixaAbertoTO.getPessoa());
                    jsonObject.put("colaborador", caixaAbertoTO.getColaborador());
                    jsonObject.put("cliente", caixaAbertoTO.getCliente());
                    jsonObject.put("nomeCliente", caixaAbertoTO.getNomeCliente());
                    jsonObject.put("descricao", caixaAbertoTO.getDescricao());
                    jsonObject.put("valorTotal", caixaAbertoTO.getValorTotal());
                    jsonObject.put("lancamento", caixaAbertoTO.getLancamentoFormatada());
                    jsonObject.put("codigoEvento", caixaAbertoTO.getCodigoEvento());
                    jsonObject.put("empresaNome", caixaAbertoTO.getEmpresaNome());

                    JSONArray jsonArrayParcelas = new JSONArray();
                    for (MovParcelaVO movParcelaVO : caixaAbertoTO.getParcelas()) {
                        JSONObject jsonObjectParcelas = movParcelaVO.toJSON();
                        jsonArrayParcelas.put(jsonObjectParcelas);
                    }
                    jsonObject.put("parcelas", jsonArrayParcelas);
                    jsonObject.put("empresaCodigo", caixaAbertoTO.getEmpresaCodigo());

                    jsonArray.put(jsonObject);
                }

                response.getWriter().append(jsonArray.toString());

                movParcela = null;
            }
        } catch (Exception e) {
            processarErro(e, request, response, null);
        }
    }

    private Date getDataFromJSON(JSONObject body, String campo) throws ParametroObrigatorioException {
        try {
            String dataStr = body.optString(campo);
            if (!Util.isEmptyString(dataStr)) {
                SimpleDateFormat dateFormat = new SimpleDateFormat("dd/MM/yyyy");
                Date data = dateFormat.parse(dataStr);

                return data;
            }
        } catch (Exception e) {
            throw new ParametroObrigatorioException("A data deve ser informada no formato dd/MM/yyyy");
        }

        return null;
    }
}

package servlet.caixaemaberto;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import com.google.common.net.HttpHeaders;
import controle.arquitetura.exceptions.ParametroObrigatorioException;
import controle.arquitetura.exceptions.ServiceException;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.financeiro.MovParcelaVO;
import negocio.comuns.financeiro.ReciboPagamentoVO;
import negocio.comuns.financeiro.ReciboPagamentosVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.contrato.Contrato;
import negocio.facade.jdbc.financeiro.MovPagamento;
import negocio.facade.jdbc.financeiro.MovParcela;
import negocio.facade.jdbc.utilitarias.Conexao;
import org.apache.commons.lang3.StringEscapeUtils;
import org.json.JSONArray;
import org.json.JSONObject;
import servlet.arquitetura.SuperServlet;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.sql.Connection;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

public class PagamentosServlet extends SuperServlet {

    @Override
    protected void processRequest(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {

        response.setHeader("Access-Control-Allow-Origin", "*");
        response.setHeader("Access-Control-Allow-Methods", "GET, OPTIONS");
        response.setHeader("Access-Control-Max-Age", "3600");
        response.setHeader("Access-Control-Allow-Headers", "Authorization, content-type, xsrf-token");
        response.addHeader("Access-Control-Expose-Headers", "xsrf-token");
        response.setContentType("application/json");

        try {
            validarAuthorizationComChave(request);

            String chave = request.getHeader(HttpHeaders.AUTHORIZATION);
            Connection connection = new DAO().obterConexaoEspecifica(chave);
            Conexao.guardarConexaoForJ2SE(chave, connection);

            MovPagamento movPagamento = new MovPagamento(connection);

            JSONObject body = getJSONBody(request);
            Boolean pactoApp = Boolean.valueOf(request.getParameter("pactoApp"));

            PagamentoDTO pagamentoDTO = new PagamentoDTO(body, pactoApp);

            validarParametros(pagamentoDTO, pactoApp);
            if (pactoApp) {
                geraReciboPactoApp(response, connection, movPagamento, pagamentoDTO);
            } else {
                ReciboPagamentoVO reciboPagamentoVO = movPagamento.gravarPagamento(pagamentoDTO, false, true, false);
                response.getWriter().append(reciboPagamentoVO.toJson().toString());
            }

            movPagamento = null;
        } catch (Exception e) {
            processarErro(e, request, response, null);
        }
    }

    private static void geraReciboPactoApp(HttpServletResponse response, Connection connection, MovPagamento movPagamento, PagamentoDTO pagamentoDTO) throws Exception {
        Boolean vendaAvulsa = pagamentoDTO.getParcelasVendaAvulsa().size() > 0;
        Boolean contratoPreenchido = pagamentoDTO.getParcelasContrato().size() > 0;
        Boolean aulaAvulsa = pagamentoDTO.getAulasAvulsas().size() > 0;

        List<ReciboPagamentosVO> reciboPagamentosVOList = new ArrayList<>();
        List<PagamentoDTO> pagamentoDTOList = new ArrayList<>();

        List<MovParcelaVO> movparcelas = new ArrayList<>();
        List<MovParcelaVO> movParcelasContrato = new ArrayList<>();
        List<MovParcelaVO> movparcelasVendaAvulsa = new ArrayList<>();
        List<MovParcelaVO> movparcelasAulaAvulsa = new ArrayList<>();
        MovParcela movParcela = new MovParcela(connection);

        if (vendaAvulsa) {
            for(Integer cod: pagamentoDTO.getParcelasVendaAvulsa()) {
                addValorVendaAvulsa(pagamentoDTO, movparcelasVendaAvulsa, movParcela, cod, pagamentoDTOList);
            }
        }
        if (contratoPreenchido) {
            for (Integer cod : pagamentoDTO.getParcelasContrato()) {
                addValorParcelasContrato(pagamentoDTO, movParcela, pagamentoDTOList, movParcelasContrato, cod);
            }
        }
        if (aulaAvulsa) {
            for (Integer cod : pagamentoDTO.getAulasAvulsas()) {
                addValorAulaAvulsa(pagamentoDTO, movparcelasAulaAvulsa, movParcela, pagamentoDTOList, cod);
            }
        }

        addMovParcelasTotais(movparcelas, movParcelasContrato, movparcelasVendaAvulsa, movparcelasAulaAvulsa);
        validaValorInformado(pagamentoDTO, movparcelas);

        for (PagamentoDTO pagamentoDTO1 : pagamentoDTOList) {
            ReciboPagamentosVO reciboPagamentosVO = new ReciboPagamentosVO();
            String tipoPagamento = getTipoPagamento(pagamentoDTO1);
            try {
                ReciboPagamentoVO reciboPagamentoVO = movPagamento.gravarPagamento(pagamentoDTO1, true, true, false);

                reciboPagamentosVO.setReciboPagamentoVO(reciboPagamentoVO);
                reciboPagamentosVOList.add(reciboPagamentosVO);
            } catch (Exception e) {
                reciboPagamentosVO.setErro("Erro ao realizar pagamento de " + tipoPagamento + ": " + e.getMessage());
                reciboPagamentosVOList.add(reciboPagamentosVO);
            }
            reciboPagamentosVO.setTipoPagamento(tipoPagamento);
        }

        JSONArray jsonArray = convertReciboPagamentosListToJsonArray(reciboPagamentosVOList);

        response.getWriter().append(jsonArray.toString());
    }

    private static void addMovParcelasTotais(List<MovParcelaVO> movparcelas, List<MovParcelaVO> movparcelasContrato, List<MovParcelaVO> movparcelasVendaAvulsa, List<MovParcelaVO> movparcelasAulaAvulsa) {
        movparcelas.addAll(movparcelasContrato);
        movparcelas.addAll(movparcelasAulaAvulsa);
        movparcelas.addAll(movparcelasVendaAvulsa);
    }

    private static void addValorAulaAvulsa(PagamentoDTO pagamentoDTO, List<MovParcelaVO> movparcelasAulaAvulsa, MovParcela movParcela, List<PagamentoDTO> pagamentoDTOList, Integer codigo) throws Exception {
        List<MovParcelaVO> movParcelaVOS = movParcela.consultarPorCodigoAulaAvulsaDiariaLista(codigo, "EA", false, null, null, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        movparcelasAulaAvulsa.addAll(movParcelaVOS);
        criaPagamentoAulaAvulsa(pagamentoDTO, pagamentoDTOList, movParcelaVOS, codigo);
    }

    private static void addValorVendaAvulsa(PagamentoDTO pagamentoDTO, List<MovParcelaVO> movParcelasVendaAvulsa, MovParcela movParcela, Integer cod, List<PagamentoDTO> pagamentoDTOList) throws Exception {
        MovParcelaVO movParcelaVO = movParcela.consultarPorCodigo(cod, Uteis.NIVELMONTARDADOS_MINIMOS);
        if (movParcelaVO != null && Calendario.maiorOuIgual(movParcelaVO.getDataVencimento(), Calendario.hoje()) && movParcelaVO.getVendaAvulsaVO() != null && movParcelaVO.getVendaAvulsaVO().getCodigo() > 0) {
            movParcelasVendaAvulsa.add(movParcelaVO);

            PagamentoDTO pagamentoDTO1 = encontrarPorVendaAvulsa(pagamentoDTOList, movParcelaVO);
            if (pagamentoDTO1 != null) {
                pagamentoDTO1.getParcelasVendaAvulsa().add(movParcelaVO.getCodigo());
                pagamentoDTO1.getFormasPagamento().get(0).setValor(pagamentoDTO1.getFormasPagamento().get(0).getValor() + movParcelaVO.getValorParcela());
            } else {
                PagamentoDTO pagamentoDTOCopy = new PagamentoDTO(pagamentoDTO);
                pagamentoDTOCopy.setAulaAvulsa(0);
                pagamentoDTOCopy.setContrato(0);

                List<Integer> parcelas = new ArrayList<>();
                parcelas.add(movParcelaVO.getCodigo());
                pagamentoDTOCopy.setParcelasVendaAvulsa(parcelas);
                pagamentoDTOCopy.setVendaAvulsa(movParcelaVO.getVendaAvulsaVO().getCodigo());
                pagamentoDTOCopy.getFormasPagamento().get(0).setValor(movParcelaVO.getValorParcela());
                pagamentoDTOList.add(pagamentoDTOCopy);
            }

        } else {
            throw new ServiceException("Não foi possível realizar o pagamento devido a parcela " + cod + "! Verifique se o código está correto e a data limite de pagamento.");
        }
    }

    private static void addValorParcelasContrato(PagamentoDTO pagamentoDTO, MovParcela movParcela, List<PagamentoDTO> pagamentoDTOList, List<MovParcelaVO> movparcelasContrato, Integer cod) throws Exception {
        MovParcelaVO movParcelaVO = movParcela.consultarPorCodigo(cod, Uteis.NIVELMONTARDADOS_MINIMOS);
        if (movParcelaVO != null && Calendario.maiorOuIgual(movParcelaVO.getDataVencimento(), Calendario.hoje())) {
            movparcelasContrato.add(movParcelaVO);

            PagamentoDTO pagamentoDTO1 = encontrarPorContrato(pagamentoDTOList, movParcelaVO);
            if (pagamentoDTO1 != null) {
                pagamentoDTO1.getParcelasContrato().add(movParcelaVO.getCodigo());
                pagamentoDTO1.getFormasPagamento().get(0).setValor(pagamentoDTO1.getFormasPagamento().get(0).getValor() + movParcelaVO.getValorParcela());
            } else {
                PagamentoDTO pagamentoDTOCopy = new PagamentoDTO(pagamentoDTO);
                pagamentoDTOCopy.setAulaAvulsa(0);
                pagamentoDTOCopy.setVendaAvulsa(0);

                List<Integer> parcelas = new ArrayList<>();
                parcelas.add(movParcelaVO.getCodigo());
                pagamentoDTOCopy.setParcelasContrato(parcelas);
                pagamentoDTOCopy.setContrato(movParcelaVO.getCodigoContrato());
                pagamentoDTOCopy.getFormasPagamento().get(0).setValor(movParcelaVO.getValorParcela());
                pagamentoDTOList.add(pagamentoDTOCopy);
            }

        } else {
            throw new ServiceException("Não foi possível realizar o pagamento devido a parcela " + cod + "! Verifique se o código está correto e a data limite de pagamento.");
        }
    }

    private static PagamentoDTO encontrarPorContrato(List<PagamentoDTO> pagamentoDTOList, MovParcelaVO movParcelaVO) {
        Optional<PagamentoDTO> pagamentoDTO = pagamentoDTOList.stream()
                .filter(p -> p.getContrato().equals(movParcelaVO.getCodigoContrato()))
                .findFirst();

        return pagamentoDTO.orElse(null);
    }

    private static PagamentoDTO encontrarPorVendaAvulsa(List<PagamentoDTO> pagamentoDTOList, MovParcelaVO movParcelaVO) {
        Optional<PagamentoDTO> pagamentoDTO = pagamentoDTOList.stream()
                .filter(p -> p.getVendaAvulsa().equals(movParcelaVO.getVendaAvulsaVO().getCodigo()))
                .findFirst();

        return pagamentoDTO.orElse(null);
    }

    private static String getTipoPagamento(PagamentoDTO pagamentoDTO1) {
        String tipoPagamento = !(pagamentoDTO1.getVendaAvulsa() == null || pagamentoDTO1.getVendaAvulsa() == 0) ? "venda avulsa" : "";
        tipoPagamento += !(pagamentoDTO1.getContrato() == null || pagamentoDTO1.getContrato() == 0) ? "contrato" : "";
        tipoPagamento += !(pagamentoDTO1.getAulaAvulsa() == null || pagamentoDTO1.getAulaAvulsa() == 0) ? "aula avulsa" : "";
        return tipoPagamento;
    }

    public static JSONArray convertReciboPagamentosListToJsonArray(List<ReciboPagamentosVO> reciboPagamentosVOList) {
        JSONArray jsonArray = new JSONArray();

        for (ReciboPagamentosVO reciboPagamentosVO : reciboPagamentosVOList) {
            JSONObject jsonObject = new JSONObject();

            if (reciboPagamentosVO.getReciboPagamentoVO() != null) {
                jsonObject.put("reciboPagamentoVO", StringEscapeUtils.unescapeJson(reciboPagamentosVO.getReciboPagamentoVO().toJson().toString()));
            }

            if (reciboPagamentosVO.getErro() != null) {
                jsonObject.put("erro", reciboPagamentosVO.getErro());
            }

            if (reciboPagamentosVO.getTipoPagamento() != null) {
                jsonObject.put("tipoPagamento", reciboPagamentosVO.getTipoPagamento());
            }

            jsonArray.put(jsonObject);
        }

        return jsonArray;
    }

    private static void criaPagamentoAulaAvulsa(PagamentoDTO pagamentoDTO, List<PagamentoDTO> pagamentoDTOList, List<MovParcelaVO> movparcelas, Integer codigoAulaAvulsa) {
        Double valorTotalParcelas = Math.round(movparcelas.stream().mapToDouble(parcelaVO -> parcelaVO.getValorParcela().doubleValue()).sum() * 100.0) / 100.0;

        PagamentoDTO pagamentoDTOCopy = new PagamentoDTO(pagamentoDTO);
        pagamentoDTOCopy.setContrato(0);
        pagamentoDTOCopy.setVendaAvulsa(0);
        pagamentoDTOCopy.setAulaAvulsa(codigoAulaAvulsa);
        pagamentoDTOCopy.getFormasPagamento().get(0).setValor(valorTotalParcelas);
        pagamentoDTOList.add(pagamentoDTOCopy);
    }

    private static void validaValorInformado(PagamentoDTO pagamentoDTO, List<MovParcelaVO> movparcelas) throws ServiceException {
        Double valorTotalParcelas = Math.round(movparcelas.stream().mapToDouble(parcelaVO -> parcelaVO.getValorParcela().doubleValue()).sum() * 100.0) / 100.0;
        Double valorTotalPgamentos = Math.round(pagamentoDTO.getFormasPagamento().stream().mapToDouble(pagamento -> pagamento.getValor()).sum() * 100.0) / 100.0;

        if (!valorTotalParcelas.equals(valorTotalPgamentos)) {
            throw new ServiceException("O valor do pagamento " + valorTotalParcelas + " é diferente do valor informado: " + valorTotalPgamentos);
        }
    }

    private static ContratoVO getContratoVO(Connection connection, Integer codContrato) throws Exception {
        Contrato contrato = new Contrato(connection);
        ContratoVO contratoVO = contrato.consultarPorChavePrimaria(codContrato, Uteis.NIVELMONTARDADOS_TODOS);
        if (contratoVO == null) {
            throw new ServiceException("Contrato não encontrado");
        }
        contratoVO = contrato.consultarPorCodigo(codContrato, Uteis.NIVELMONTARDADOS_TODOS);
        if (contratoVO == null) {
            throw new ServiceException("Contrato não encontrado");
        }
        return contratoVO;
    }

    private void validarParametros(PagamentoDTO pagamentoDTO, Boolean pactoApp) throws ParametroObrigatorioException {
        if (pagamentoDTO.getUsuario() == null || pagamentoDTO.getUsuario() <= 0) {
            throw new ParametroObrigatorioException("O usuario é obrigatório");
        }

        if(pagamentoDTO.getFormasPagamento() == null || pagamentoDTO.getFormasPagamento().size() == 0){
            if(pagamentoDTO.getUsuario() == null || pagamentoDTO.getUsuario() <= 0){
                throw new ParametroObrigatorioException("As formasPagamento são obrigatórias");
            }

            for(FormaPagamentoDTO formaPagamentoDTO: pagamentoDTO.getFormasPagamento()){
                if(formaPagamentoDTO.getCodigoFormaPagamento() == null || formaPagamentoDTO.getCodigoFormaPagamento() <= 0){
                    throw new ParametroObrigatorioException("O codigoFormaPagamento é obrigatório");
                }
                if(formaPagamentoDTO.getValor() == null || formaPagamentoDTO.getValor() <= 0){
                    throw new ParametroObrigatorioException("O valor é obrigatório");
                }
            }
        }

        if((pagamentoDTO.getContrato() == null || pagamentoDTO.getContrato() == 0) &&
                (pagamentoDTO.getAulaAvulsa() == null || pagamentoDTO.getAulaAvulsa() == 0) &&
                (pagamentoDTO.getVendaAvulsa() == null || pagamentoDTO.getVendaAvulsa() == 0)){
            throw new ParametroObrigatorioException("É necessário informar um contrato, aula avulsa ou venda avulsa");
        } else if ((pactoApp && pagamentoDTO.getParcelasVendaAvulsa().isEmpty() &&
                pagamentoDTO.getAulasAvulsas().isEmpty() &&
                pagamentoDTO.getParcelasContrato().isEmpty())) {
            throw new ParametroObrigatorioException("É necessário informar pelo menos uma parcela de contrato, uma parcela de venda avulsa ou uma aula avulsa");
        }
    }
}

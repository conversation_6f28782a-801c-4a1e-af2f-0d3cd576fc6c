package servlet.pinpad;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.financeiro.PinPadPedidoVO;
import negocio.comuns.financeiro.enumerador.OpcoesPinpadEnum;
import negocio.comuns.financeiro.enumerador.StatusPinpadEnum;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.financeiro.PinPad;
import negocio.facade.jdbc.financeiro.PinPadPedido;
import org.json.JSONObject;
import servlet.arquitetura.SuperServlet;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.PrintWriter;
import java.sql.Connection;

public class PinpadWebhookServlet extends SuperServlet {

    @Override
    protected void processRequest(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        Connection con = null;
        PinPad pinpadDAO = null;
        try {
            response.addHeader("Access-Control-Allow-Origin", "*");
            response.addHeader("Access-Control-Allow-Methods", "GET,POST");
            response.setContentType("application/json");
            response.setCharacterEncoding("UTF-8");

            String method = request.getMethod();

            if (!method.equalsIgnoreCase("POST")) {
                throw new Exception("Methodo não suportado para esse recurso");
            }

            String key = request.getParameter("key");
            Integer pinPadPedido = UteisValidacao.converterInteiro(request.getParameter("pedido"));
            Integer tipoPinPad = UteisValidacao.converterInteiro(request.getParameter("tipo"));

            if (UteisValidacao.emptyString(key)) {
                throw new Exception("Key não informada");
            }

            StringBuffer body = new StringBuffer();
            try {
                InputStream inputStream = request.getInputStream();
                BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream, "UTF-8"));

                String line = null;

                while ((line = reader.readLine()) != null) {
                    body.append(line);
                }
            } catch (Exception ex) {
                ex.printStackTrace();
                throw new Exception("Erro body: " + ex.getMessage());
            }

            con = new DAO().obterConexaoEspecifica(key);

            String idExterno = "";
            OpcoesPinpadEnum pinpadEnum = null;
            if (tipoPinPad != null) {
                pinpadEnum = OpcoesPinpadEnum.fromCodigo(tipoPinPad);
                if (pinpadEnum != null && pinpadEnum.equals(OpcoesPinpadEnum.STONE_CONNECT)) {
                    try {
                        JSONObject json = new JSONObject(body.toString());
                        JSONObject jsonDados = json.getJSONObject("data");
                        idExterno = jsonDados.getString("id");
                    } catch (Exception ignored) {
                    }
                }
            }

            pinpadDAO = new PinPad(con);
            pinpadDAO.incluirHistorico(pinPadPedido, idExterno, "webhook", body.toString());

            if (pinpadEnum != null && pinpadEnum.equals(OpcoesPinpadEnum.STONE_CONNECT)) {
                processarWebhookStone(pinPadPedido, body.toString(), con);
            }

            response.setStatus(HttpServletResponse.SC_OK);
            PrintWriter out = response.getWriter();
            out.print(this.toJSON(true, "ok"));
            out.flush();
        } catch (Exception ex) {
            response.setStatus(400);
            PrintWriter out = response.getWriter();
            out.print(this.toJSON(false, ex.getMessage()));
            out.flush();
        } finally {
            pinpadDAO = null;
            try {
                if (con != null) {
                    con.close();
                }
            } catch (Exception ex) {
                ex.printStackTrace();
            }
        }
    }

    private JSONObject toJSON(boolean sucesso, Object obj) {
        JSONObject json = new JSONObject();
        json.put("sucesso", sucesso);
        json.put("dados", obj);
        return json;
    }

    private void processarWebhookStone(Integer codigoPedido, String body, Connection con) {
        PinPadPedidoVO pedidoVO;
        PinPadPedido pinPadPedidoDAO;
        try {
            pinPadPedidoDAO = new PinPadPedido(con);

            JSONObject jsonBody = new JSONObject(body);
            String type = jsonBody.optString("type");
            if (type.equalsIgnoreCase("order.closed") ||
                    type.equalsIgnoreCase("order.paid") ||
                    type.equalsIgnoreCase("order.canceled") ||
                    type.equalsIgnoreCase("order.updated") ||
                    type.equalsIgnoreCase("order.payment_failed")) {

                JSONObject jsonData = jsonBody.getJSONObject("data");
                String status = jsonData.optString("status"); //paid, canceled ou failed
                StatusPinpadEnum statusPinpadEnum = null;
                if (status.equalsIgnoreCase("paid")) {
                    statusPinpadEnum = StatusPinpadEnum.PAGO;
                } else if (status.equalsIgnoreCase("canceled")) {
                    statusPinpadEnum = StatusPinpadEnum.CANCELADO;
                } else if (status.equalsIgnoreCase("failed")) {
                    statusPinpadEnum = StatusPinpadEnum.FALHA;
                }

                if (statusPinpadEnum != null) {
                    pedidoVO = pinPadPedidoDAO.consultarPorChavePrimaria(codigoPedido);
                    if (!pedidoVO.getStatus().equals(statusPinpadEnum)) {
                        pedidoVO.setStatus(statusPinpadEnum);
                        pinPadPedidoDAO.alterarStatus(pedidoVO);
                    }
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        } finally {
            pinPadPedidoDAO = null;
        }
    }
}

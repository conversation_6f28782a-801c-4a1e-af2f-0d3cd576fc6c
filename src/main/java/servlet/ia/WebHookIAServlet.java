package servlet.ia;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.ia.RiscoEvasaoVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.ia.RiscoEvasao;
import org.apache.commons.io.IOUtils;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

public class WebHookIAServlet extends HttpServlet {

    /**
     * Processes requests for both HTTP
     * <code>GET</code> and
     * <code>POST</code> methods.
     *
     * @param request  servlet request
     * @param response servlet response
     * @throws ServletException if a servlet-specific error occurs
     * @throws IOException      if an I/O error occurs
     */

    private static String ERROR = "error";
    private static String SUCESSO = "ok";

    private String montaSqlIn(List<RiscoEvasaoVO> listRiscoEvasaoJson) {
        StringBuilder sqlIn = new StringBuilder();
        for (RiscoEvasaoVO re : listRiscoEvasaoJson) {
            sqlIn.append(re.getCliente()).append(",");
        }
        return sqlIn.substring(0, sqlIn.length() - 1);
    }

    private List<RiscoEvasaoVO> consultaClientesExistentes(Connection con, List<RiscoEvasaoVO> listRiscoEvasaoJson) throws SQLException {
        String sqlIn = montaSqlIn(listRiscoEvasaoJson);
        if (UteisValidacao.emptyString(sqlIn))
            return null;

        StringBuilder sql = new StringBuilder("SELECT\n")
                .append("codigo, cliente\n")
                .append("FROM riscoevasao\n")
                .append("WHERE cliente in (").append(sqlIn).append(")\n");

        PreparedStatement stm = con.prepareStatement(sql.toString());
        ResultSet rs = stm.executeQuery();

        List<RiscoEvasaoVO> listRiscoEvasao = new ArrayList<>();
        RiscoEvasaoVO riscoEvasaoVO;

        while (rs.next()) {
            riscoEvasaoVO = new RiscoEvasaoVO();
            riscoEvasaoVO.setCodigo(rs.getInt("codigo"));
            riscoEvasaoVO.setCliente(rs.getInt("cliente"));

            listRiscoEvasao.add(riscoEvasaoVO);
        }

        con.close();

        return listRiscoEvasao;
    }

    private List<RiscoEvasaoVO> removeClientesDuplicados(String body) throws Exception {
        List<RiscoEvasaoVO> listRiscoEvasaoVO = new ArrayList<>();

        JSONObject jsonObject = new JSONObject(body);
        JSONArray jsonArray = jsonObject.getJSONArray("data_frame");
        for (int i = 0; i < jsonArray.length(); i++) {
            boolean encontrou = false;
            int cliente = jsonArray.getJSONArray(i).getInt(0);

            if (listRiscoEvasaoVO.size() > 0) {
                for (RiscoEvasaoVO re : listRiscoEvasaoVO) {
                    if (re.getCliente() == cliente) {
                        encontrou = true;
                        break;
                    }
                }
            }

            if (!encontrou) {
                RiscoEvasaoVO riscoEvasaoVO = new RiscoEvasaoVO();
                riscoEvasaoVO.setCliente(jsonArray.getJSONArray(i).getInt(0));
                riscoEvasaoVO.setDataPredicao(Uteis.getDate(jsonArray.getJSONArray(i).getString(1), "yyyy-MM-dd"));
                riscoEvasaoVO.setChanceSair30Dias(Float.valueOf(jsonArray.getJSONArray(i).get(2).toString()));
                riscoEvasaoVO.setDataPredicao30Dias(Uteis.getDate(jsonArray.getJSONArray(i).getString(3), "yyyy-MM-dd"));
                listRiscoEvasaoVO.add(riscoEvasaoVO);
            }
        }

        return listRiscoEvasaoVO;
    }

    private void gravarRiscoEvasao(Connection con, String bodyJson) throws Exception {
        List<RiscoEvasaoVO> listRiscoEvasaoJson = removeClientesDuplicados(bodyJson);
        List<RiscoEvasaoVO> listRiscoEvasaoConsulta = consultaClientesExistentes(con, listRiscoEvasaoJson);

        StringBuilder sqlInsertValues = new StringBuilder();
        StringBuilder sqlUpdateValues = new StringBuilder();
        for (RiscoEvasaoVO reJson : listRiscoEvasaoJson) {
            boolean encontrou = false;
            if (!UteisValidacao.emptyList(listRiscoEvasaoConsulta)) {
                for (RiscoEvasaoVO reConsulta : listRiscoEvasaoConsulta) {
                    if (reJson.getCliente().equals(reConsulta.getCliente())) {
                        encontrou = true;
                        reJson.setCodigo(reConsulta.getCodigo());
                        break;
                    }
                }
            }

            if (encontrou) {
                // Monta atualizacao de varios registros apenas uma vez
                sqlUpdateValues.append("UPDATE RiscoEvasao\n")
                        .append("SET datapredicao = '").append(reJson.getDataPredicao()).append("',\n")
                        .append("chancesair30dias = ").append(reJson.getChanceSair30Dias()).append(",\n")
                        .append("datapredicao30dias = '").append(reJson.getDataPredicao30Dias()).append("'\n")
                        .append("WHERE codigo = ").append(reJson.getCodigo()).append(";\n");
            } else {
                // Monta insercao de varios registros apenas uma vez
                sqlInsertValues.append("(").append(reJson.getCliente())
                        .append(",'").append(reJson.getDataPredicao())
                        .append("',").append(reJson.getChanceSair30Dias())
                        .append(",'").append(reJson.getDataPredicao30Dias()).append("'),\n");
            }
        }

        // Grava registros na tabela
        RiscoEvasao riscoEvasao = new RiscoEvasao(con);
        StringBuilder sqlCompleta = new StringBuilder();
        sqlCompleta.append(sqlUpdateValues.toString());

        if (!UteisValidacao.emptyString(sqlInsertValues.toString())) {
            String removeCaracter = sqlInsertValues.toString().substring(0, sqlInsertValues.toString().length() - 2);
            StringBuilder sql = new StringBuilder("INSERT INTO RiscoEvasao\n")
                    .append("(cliente, datapredicao, chancesair30dias, datapredicao30dias)\n")
                    .append("VALUES ")
                    .append(removeCaracter);
            sqlCompleta.append(sql.toString());
        }

        riscoEvasao.gravarSQL(sqlCompleta.toString());

        con.close();
    }

    private JSONObject retornoMensagem(String status, String mensagem) {
        JSONObject jsonObject = new JSONObject();

        jsonObject.put("status", status);
        jsonObject.put("mensagem", mensagem);

        return jsonObject;
    }

    private boolean isJSONValid(String test) {
        try {
            new JSONObject(test);
        } catch (JSONException ex) {
            try {
                new JSONArray(test);
            } catch (JSONException ex1) {
                return false;
            }
        }
        return true;
    }

    protected void processRequest(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        Connection con = null;
        JSONObject jsonObject;
        try {
            if (!UteisValidacao.emptyString(request.getHeader("key"))) {
                String body = IOUtils.toString(request.getReader());
                if (!isJSONValid(body)) {
                    response.setStatus(HttpServletResponse.SC_BAD_REQUEST);
                    throw new Exception("Body inválido");
                }

                final String chave = request.getHeader("key");
                con = new DAO().obterConexaoEspecifica(chave);
                gravarRiscoEvasao(con, body);

                jsonObject = retornoMensagem(SUCESSO, "Operação realizada com sucesso.");

            } else {
                jsonObject = retornoMensagem(ERROR, "KEY NAO INFORMADA");
            }

        } catch (Exception ex) {
            jsonObject = retornoMensagem(ERROR, "ERRO - REQUISIÇÃO WebHookIA: " + ex.getMessage());
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        } finally {
            try {
                if (con != null) {
                    con.close();
                }
            } catch (Exception ignored) {
                response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            }
        }
        response.setContentType("application/json");
        response.getOutputStream().print(jsonObject.toString());
    }

    /**
     * Handles the HTTP
     * <code>GET</code> method.
     *
     * @param request  servlet request
     * @param response servlet response
     * @throws ServletException if a servlet-specific error occurs
     * @throws IOException      if an I/O error occurs
     */
//    @Override
//    protected void doGet(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
//        try {
//            throw new Exception("MÉTODO NÃO IMPLEMENTADO");
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//    }

    /**
     * Handles the HTTP
     * <code>POST</code> method.
     *
     * @param request  servlet request
     * @param response servlet response
     * @throws ServletException if a servlet-specific error occurs
     * @throws IOException      if an I/O error occurs
     */
    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        processRequest(request, response);
    }

    /**
     * Returns a short description of the servlet.
     *
     * @return a String containing servlet description
     */
    @Override
    public String getServletInfo() {
        return "Short description";
    }// </editor-fold>
}
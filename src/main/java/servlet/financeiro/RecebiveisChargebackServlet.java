package servlet.financeiro;

import br.com.pactosolucoes.enumeradores.SituacaoItemExtratoEnum;
import br.com.pactosolucoes.enumeradores.TipoConciliacaoEnum;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.financeiro.CartaoCreditoTO;
import negocio.comuns.financeiro.ExtratoDiarioItemVO;
import negocio.comuns.financeiro.TotalizadorConciliacaoDTO;
import negocio.comuns.financeiro.enumerador.TipoConvenioCobrancaEnum;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.financeiro.ExtratoDiarioItem;
import negocio.facade.jdbc.utilitarias.Conexao;
import org.json.JSONObject;
import servlet.arquitetura.SuperServlet;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.sql.Connection;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static negocio.facade.jdbc.arquitetura.FacadeManager.getFacade;

public class RecebiveisChargebackServlet extends SuperServlet {

    private Connection con;
    private boolean ok = true;
    private boolean pendencia = true;
    private boolean nao_encontrado = true;
    private boolean estornado = true;
    private boolean estornadoOperadora = true;
    private Double okValor = 0.0;
    private Double pendenciaValor = 0.0;
    private Double nao_encontradoValor = 0.0;
    List<ExtratoDiarioItemVO> extratoDiarioBack;
    private List<ExtratoDiarioItemVO> extratoDiario;
    private List<ExtratoDiarioItemVO> extratoDiarioCancelamentos;
    private TotalizadorConciliacaoDTO totalizadorConciliacaoDTO;


    public List<ExtratoDiarioItemVO> getExtratoDiarioCancelamentos() {
        return extratoDiarioCancelamentos;
    }

    public List<ExtratoDiarioItemVO> getExtratoDiario() {
        return extratoDiario;
    }

    @Override
    protected void processRequest(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        try {
            response.addHeader("Access-Control-Allow-Origin", "*");
            response.addHeader("Access-Control-Allow-Methods", "GET,POST");
            response.setContentType("application/json");

            String method = request.getMethod();

            if (!method.equalsIgnoreCase("GET")) {
                throw new Exception("Método não suportado.");
            }

            String key = request.getParameter("key");
            if (UteisValidacao.emptyString(key)) {
                throw new Exception("Chave não informada.");
            }

            String dataInicio = request.getParameter("dataInicio");
            if (Objects.isNull(dataInicio)) {
                throw new Exception("Informe a data de inicio.");
            }

            String dataFinal = request.getParameter("dataFim");
            if (Objects.isNull(dataFinal)) {
                throw new Exception("Informe a data final da conciliação.");
            }
            String tipoConciliacaoString = request.getParameter("tipoConciliacao");
            if (Objects.isNull(tipoConciliacaoString)) {
                throw new Exception("Informe o tipo da conciliação.");
            }

            String codigoEmpresa = request.getParameter("codigoEmpresa");
            if (Objects.isNull(codigoEmpresa)) {
                throw new Exception("Informe o codigo da empresa .");
            }

            Date dataInicialConciliacao = Calendario.getDate("dd/MM/yyyy", dataInicio);
            Date dataFinalConciliacao = Calendario.getDate("dd/MM/yyyy", dataFinal);

            long diffInMillies = Math.abs(dataFinalConciliacao.getTime() - dataInicialConciliacao.getTime());
            long diffInDays = TimeUnit.DAYS.convert(diffInMillies, TimeUnit.MILLISECONDS);

            if (diffInDays > 31) {
                throw new Exception("O intervalo de datas não pode ser superior a 31 dias.");
            }

            Integer tipoConciliacao = Integer.parseInt(request.getParameter("tipoConciliacao"));
            Integer empresa = Integer.parseInt(request.getParameter("codigoEmpresa"));

            String ro = Objects.isNull(request.getParameter("ro")) ? "" : request.getParameter("ro");
            String codAutorizacaoedi = Objects.isNull(request.getParameter("autorizacao")) ? "" : request.getParameter("autorizacao");

            Integer codigoConvenio = request.getParameter("codigoConvenio") != null ? Integer.parseInt(request.getParameter("codigoConvenio")) : null;
            Integer formaPagamento = request.getParameter("formaPagamento") != null ? Integer.parseInt(request.getParameter("formaPagamento")) : null;

            Boolean apresentarPagamentosCancelados = !Objects.isNull(request.getParameter("apresentarPagamentosCancelados"))
                    && Boolean.parseBoolean(request.getParameter("apresentarPagamentosCancelados"));

            Integer operadoraCartao = Objects.isNull(request.getParameter("operadoraCartao")) ? 0 :
                    Integer.parseInt(request.getParameter("operadoraCartao"));

            Integer adquirente = Objects.isNull(request.getParameter("adquirente")) ? 0 :
                    Integer.parseInt(request.getParameter("adquirente"));

            String nsu = Objects.isNull(request.getParameter("nsu")) ? "" : request.getParameter("nsu");

            this.con = new DAO().obterConexaoEspecifica(key);
            Conexao.guardarConexaoForJ2SE(con);

            extratoDiarioBack = new ExtratoDiarioItem(con).consultarExtratoParaConciliacao(
                    dataInicialConciliacao, dataFinalConciliacao, ro, codAutorizacaoedi, tipoConciliacao, empresa,
                    codigoConvenio, formaPagamento, apresentarPagamentosCancelados, operadoraCartao, adquirente, nsu);

            if (extratoDiarioBack.isEmpty()) {
                response.setStatus(204);
                response.getWriter().append(this.toJSON(false, "Nenhum extrato foi encontrado no período.").toString());
                return;
            }
            filtrarLista();

            reponseJson(response);
        } catch (Exception ex) {
            response.setStatus(400);
            response.getWriter().append(this.toJSON(false, ex.getMessage()).toString());
        } finally {
            if (con != null) {
                try {
                    con.close();
                    this.con =  null;
                } catch (Exception ex) {
                    ex.printStackTrace();
                }
            }
        }
    }

    private void reponseJson(HttpServletResponse response) throws Exception {
        JSONObject json = new JSONObject();
        json.put("sucesso", true);

        json.put("dados", extratoDiario.stream().map(
                RecebiveisChargebackServlet::getRecebiveisChargebackDTO
        ).collect(Collectors.toList()));

        json.put("cancelados", extratoDiarioCancelamentos.stream().map(
                RecebiveisChargebackServlet::getRecebiveisChargebackDTO
        ).collect(Collectors.toList()));

        try {
            response.getWriter().append(this.toJSON(true, json).toString());
        } catch (Exception e) {
            e.printStackTrace();
            throw new Exception("Erro ao processar Json : " + e.getMessage());
        }
    }

    private static RecebiveisChargebackDTO getRecebiveisChargebackDTO(ExtratoDiarioItemVO ext) {
        RecebiveisChargebackDTO recebiveisChargebackDTO = new RecebiveisChargebackDTO();
        recebiveisChargebackDTO.setNomePagador(ext.getNomeAlunoApresentar());
        recebiveisChargebackDTO.setContaMovimento(ext.getContaMovimento());
        recebiveisChargebackDTO.setAutorizacao(ext.getAutorizacao());
        recebiveisChargebackDTO.setValorCC(ext.getValorCC());
        recebiveisChargebackDTO.setValorMP(ext.getValorMP());
        recebiveisChargebackDTO.setFormaPagamento(ext.getTipoFormaPagamento());
        recebiveisChargebackDTO.setNsu(ext.getNsu());
        recebiveisChargebackDTO.setTipoConcilicacao(ext.getTipoConciliacaoApresentar());
        recebiveisChargebackDTO.setDataLancamentoMP(ext.getDataLancamentoMP());
        recebiveisChargebackDTO.setGetNrParcelas_apresentar(ext.getNrParcelas_apresentar());
        recebiveisChargebackDTO.setValorBruto(ext.getValorBrutoApresentar());
        //recebiveisChargebackDTO.setNrParcelaCartaoCredito(ext.getMovPagamento().getNrParcelaCartaoCredito());
        recebiveisChargebackDTO.setDataLancamentoApresentar(ext.getDataLancamentoApresentar());
        return recebiveisChargebackDTO;
    }

    private JSONObject toJSON(boolean sucesso, Object dados) {
        JSONObject json = new JSONObject();
        json.put("sucesso", sucesso);
        json.put("dados", dados);
        return json;
    }

    private void filtrarLista() throws Exception {
        totalizadorConciliacaoDTO = new TotalizadorConciliacaoDTO();
        okValor = 0.0;
        nao_encontradoValor = 0.0;
        extratoDiario = new ArrayList<>();
        extratoDiarioCancelamentos = new ArrayList<>();
        pendenciaValor = 0.0;
        Set<Integer> compProcessada = new HashSet<>();
        for (ExtratoDiarioItemVO extratoItem : extratoDiarioBack) {
            try {
                if ((extratoItem.getTipoConvenioCobrancaEnum().equals(TipoConvenioCobrancaEnum.DCC_STONE_ONLINE) || extratoItem.getTipoConvenioCobrancaEnum().equals(TipoConvenioCobrancaEnum.PINPAD_STONE_CONNECT))
                        && (extratoItem.getSituacao().equals(SituacaoItemExtratoEnum.AUTORIZACAO_NAO_EXISTE))) {
                    if (Calendario.maiorOuIgual(extratoItem.getDataPrevistaPagamento(),
                            Calendario.somarDias(extratoItem.getDataLancamento(), 3))) {
                        extratoItem.setCredito(true);
                    }
                }
                if (tratarComposicao(compProcessada, extratoItem)) continue;
                if (estornadoOperadora && extratoItem.getValorBruto() < 0.0 && !extratoItem.getSituacao().equals(SituacaoItemExtratoEnum.AUTORIZACAO_NAO_EXISTE)) {
                    int tipoConciliacaoEnum = (!UteisValidacao.emptyNumber(extratoItem.getTipoConciliacao())
                            && extratoItem.getTipoConciliacao() != TipoConciliacaoEnum.CANCELAMENTO.getCodigo()) ? extratoItem.getTipoConciliacao() : TipoConciliacaoEnum.CANCELAMENTO.getCodigo();
                    extratoItem.setTipoConciliacao(tipoConciliacaoEnum);
                    extratoItem.setSituacao(SituacaoItemExtratoEnum.ESTORNADO_OPERADORA);
                } else if (nao_encontrado && extratoItem.getTipoConciliacao() != 0 && extratoItem.getSituacao().equals(SituacaoItemExtratoEnum.AUTORIZACAO_NAO_EXISTE)) {
                    int tipoConciliacaoEnum = (!UteisValidacao.emptyNumber(extratoItem.getTipoConciliacao())
                            && extratoItem.getTipoConciliacao() != TipoConciliacaoEnum.CANCELAMENTO.getCodigo()) ? extratoItem.getTipoConciliacao() : TipoConciliacaoEnum.CANCELAMENTO.getCodigo();
                    extratoItem.setTipoConciliacao(tipoConciliacaoEnum);
                    extratoItem.setSituacao(SituacaoItemExtratoEnum.ESTORNADO_SISTEMA);
                }
                if ((ok && SituacaoItemExtratoEnum.OK.equals(extratoItem.getSituacao()))
                        || (pendencia && SituacaoItemExtratoEnum.PENDENCIAS.equals(extratoItem.getSituacao()))
                        || (nao_encontrado && SituacaoItemExtratoEnum.AUTORIZACAO_NAO_EXISTE.equals(extratoItem.getSituacao()))
                        || (estornadoOperadora && SituacaoItemExtratoEnum.ESTORNADO_OPERADORA.equals(extratoItem.getSituacao()))
                        || (estornado && SituacaoItemExtratoEnum.ESTORNADO_SISTEMA.equals(extratoItem.getSituacao()))) {

                    if (estornadoOperadora && extratoItem.getValorBruto() < 0.0) {
                        getExtratoDiarioCancelamentos().add(extratoItem);
                    } else {
                        getExtratoDiario().add(extratoItem);
                    }
                    totalizadorConciliacaoDTO.totalizar(extratoItem);
                }
            } catch (Exception e) {
            }
        }
        extratoDiario = Ordenacao.ordenarLista(extratoDiario, "nomePagador");
        extratoDiarioCancelamentos = Ordenacao.ordenarLista(extratoDiarioCancelamentos, "nomePagador");

    }

    private boolean tratarComposicao(Set<Integer> compProcessada, ExtratoDiarioItemVO extratoItem) {
        if (extratoItem.getComposicao() != 0 && extratoItem.getCartoes() != null && !extratoItem.getCartoes().isEmpty()) {
            for (CartaoCreditoTO cardComp : extratoItem.getCartoes()) {
                if (!compProcessada.contains(cardComp.getCodigo())) {
                    compProcessada.add(extratoItem.getComposicao());
                }
            }

            for (CartaoCreditoTO cardComp2 : extratoItem.getCartoes()) {
                if (compProcessada.contains(cardComp2.getCodigo())) {
                    return true;
                }
            }
        }
        return false;
    }


}

package servlet.financeiro;

import controle.arquitetura.security.LoginControle;
import servlet.arquitetura.SuperServlet;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;

public class PinPadServlet extends SuperServlet {
    @Override
    protected void processRequest(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        super.processRequest(request, response);
        PrintWriter out = response.getWriter();
        String json;
        try {
            LoginControle loginControle = ((LoginControle)getAttribute(request, "LoginControle"));
            Integer empresa = loginControle.getEmpresa().getCodigo();
            if (loginControle.permissao("ConsultarInfoTodasEmpresas")) {
                empresa = 0;
            }
            String sEcho = obterParametroString(request.getParameter("sEcho"));
            String clausulaLike = obterParametroString(request.getParameter("sSearch"));
            json = ff(request).getPinPad().consultarJSON(empresa, sEcho, clausulaLike);

        } catch (Exception e) {
            json = (e.getMessage() == null) ? NullPointerException.class.getName() : e.getMessage();
        }
        out.println(json);
        json = null;
    }
}

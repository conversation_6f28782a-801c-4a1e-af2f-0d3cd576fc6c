package servlet.financeiro;

import controle.arquitetura.security.LoginControle;
import servlet.arquitetura.SuperServlet;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;

/**
 * Created with IntelliJ IDEA.
 * User: franciscoanjos
 * Date: 04/09/13
 * Time: 11:12
 * To change this template use File | Settings | File Templates.
 */

public class MetaFinanceiraEmpresaServlet extends SuperServlet {
    @Override
    protected void processRequest(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        super.processRequest(request, response);
        PrintWriter out = response.getWriter();
        String json;
        try {
            Integer empresa = ((LoginControle) getAttribute(request, "LoginControle")).getEmpresa().getCodigo();
            json = ff(request).getMetaFinanceiraEmpresa().consultarJSON(empresa);
        } catch (Exception e) {
            json = (e.getMessage() == null) ? NullPointerException.class.getName() : e.getMessage();
        }
        out.println(json);
        json = null;
    }
}
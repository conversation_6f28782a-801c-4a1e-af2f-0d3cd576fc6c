package servlet.financeiro;

import br.com.pactosolucoes.enumeradores.TipoPeriodoConsultaLancamentos;
import br.com.pactosolucoes.enumeradores.TipoPesquisaConsultaLancamentos;
import br.com.pactosolucoes.estrutura.paginacao.ConfPaginacao;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import com.google.common.net.HttpHeaders;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.financeiro.FiltroLancamentosTO;
import negocio.comuns.financeiro.MovContaVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.basico.Empresa;
import negocio.facade.jdbc.financeiro.MovConta;
import negocio.facade.jdbc.utilitarias.Conexao;
import org.json.JSONArray;
import org.json.JSONObject;
import servlet.arquitetura.SuperServlet;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.sql.Connection;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

public class LancamentoServlet extends SuperServlet {

    @Override
    protected void processRequest(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {

        response.setHeader("Access-Control-Allow-Origin", "*");
        response.setHeader("Access-Control-Allow-Methods", "GET, OPTIONS");
        response.setHeader("Access-Control-Max-Age", "3600");
        response.setHeader("Access-Control-Allow-Headers", "Authorization, content-type, xsrf-token");
        response.addHeader("Access-Control-Expose-Headers", "xsrf-token");
        response.setContentType("application/json");
        response.setCharacterEncoding("UTF-8");

        try{
            validarAuthorizationComChave(request);
            validarParametrosObrigatorios(request, new String[]{"empresa", "dataInicio", "dataFim"});

            String chave = request.getHeader(HttpHeaders.AUTHORIZATION);
            Connection connection = new DAO().obterConexaoEspecifica(chave);
            Conexao.guardarConexaoForJ2SE(chave, connection);

            Integer empresa = Uteis.converterInteiro(request.getParameter("empresa"), 1);
            Integer itensPorPagina = Uteis.converterInteiro(request.getParameter("itensPorPagina"), 30);
            Integer pagina = Uteis.converterInteiro(request.getParameter("pagina"), 1);
            Date dataInicio = Uteis.getDate(request.getParameter("dataInicio"));
            Date dataFim = Uteis.getDate(request.getParameter("dataFim"));
            Boolean excluidos = Boolean.parseBoolean(request.getParameter("excluidos"));

            MovConta servico = new MovConta(connection);
            FiltroLancamentosTO filtro = new FiltroLancamentosTO();
            filtro.setInicioLancamento(Calendario.getDataComHoraZerada(dataInicio));
            filtro.setFinalLancamento(dataFim);
            filtro.setTipoPesquisaEnum(TipoPesquisaConsultaLancamentos.PERIODO);
            filtro.setTipoPeriodoEnum(TipoPeriodoConsultaLancamentos.LANCAMENTOS_DIA);
            ArrayList<EmpresaVO> listaEmpresas = new ArrayList<>();
            Empresa empresaDao = new Empresa(connection);
            EmpresaVO empresaVO = empresaDao.consultarPorCodigo(empresa, Uteis.NIVELMONTARDADOS_MINIMOS);
            if(empresaVO != null) {
                empresaVO.setEmpresaEscolhida(true);
                listaEmpresas.add(empresaVO);
            } else {
                listaEmpresas.add(new EmpresaVO(empresa));
            }
            filtro.setListaEmpresa(listaEmpresas);

            ConfPaginacao paginacao = new ConfPaginacao();
            paginacao.setItensPorPagina(itensPorPagina);
            paginacao.setPaginaAtual(pagina);
            paginacao.setPagNavegacao(null);
            paginacao.setColunaOrdenacao("movConta.datavencimento");
            paginacao.setDirecaoOrdenacao("DESC");

            Map<String, Number> totais = servico.consultarValorTotalLancamentos(filtro);

            List<MovContaVO> movContaVOList = servico.consultarPaginado(filtro, paginacao, false, excluidos);

            response.getWriter().append(converterContaVOListJson(movContaVOList, totais, paginacao).toString());

            servico = null;
            movContaVOList = null;
        } catch (Exception e) {
            processarErro(e, request, response, null);
        }
    }

    private JSONObject converterContaVOListJson(List<MovContaVO> movContaVOList, Map<String, Number> totais, ConfPaginacao paginacao){
        JSONObject lancamentoJSON = new JSONObject();
        JSONArray listaLancamentoJSON = new JSONArray();

        for (int i = 0; i < movContaVOList.size(); i++) {
            listaLancamentoJSON.put(converterMovContaVoJson(movContaVOList.get(i)));
        }

        lancamentoJSON.put("lancamentos", listaLancamentoJSON);
        lancamentoJSON.put("paginacao", converterPaginacaoJson(paginacao));
        totais.forEach((key, value) -> {
            lancamentoJSON.put(key, value);
        });

        return lancamentoJSON;
    }

    private JSONObject converterPaginacaoJson(ConfPaginacao paginacao){
        JSONObject paginacaoJSON = new JSONObject();
        paginacaoJSON.put("itensPorPagina", paginacao.getItensPorPagina());
        paginacaoJSON.put("paginaAtual", paginacao.getPaginaAtual());
        paginacaoJSON.put("totalPaginas", paginacao.getNrTotalPaginas());
        paginacaoJSON.put("totalItens", paginacao.getNumeroTotalItens());

        return paginacaoJSON;
    }

    private JSONObject converterMovContaVoJson(MovContaVO movContaVO){
        JSONObject lancamentoJson = new JSONObject();

        lancamentoJson.put("codigo", movContaVO.getCodigo());
        lancamentoJson.put("valor", movContaVO.getValor());
        lancamentoJson.put("descricao",  movContaVO.getDescricao());
        lancamentoJson.put("dataLancamento",  movContaVO.getDataLancamento());
        lancamentoJson.put("dataVencimento",  movContaVO.getDataVencimento());
        lancamentoJson.put("favorecido",  movContaVO.getFavorecido());
        lancamentoJson.put("dataQuitacao",  movContaVO.getDataQuitacao());
        lancamentoJson.put("dataCompetencia",  movContaVO.getDataCompetencia());
        lancamentoJson.put("contaDestino",  movContaVO.getContaDestino());
        lancamentoJson.put("tipoOperacao",  movContaVO.getTipoOperacaoLancamento_Apresentar());
        lancamentoJson.put("dataQuitacao",  movContaVO.getDataQuitacao());
        lancamentoJson.put("contaDestino",  movContaVO.getContaDestino());
        lancamentoJson.put("codigoNota",  movContaVO.getCodigoNotaEmitidaMostrar());

        return lancamentoJson;
    }
}

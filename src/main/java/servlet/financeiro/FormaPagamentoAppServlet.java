package servlet.financeiro;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.facade.jdbc.financeiro.FormaPagamento;
import servlet.arquitetura.SuperServlet;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.sql.Connection;

public class FormaPagamentoAppServlet extends SuperServlet {
    @Override
    protected void processRequest(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        response.setCharacterEncoding("UTF-8");
        response.setContentType("application/json;charset=UTF-8");

        PrintWriter out = response.getWriter(); // Obtém o Writer após configurar a codificação
        String json;
        try {
            final String situacao = obterParametroString(request.getParameter("situacao"));
            String chave = request.getParameter("chave");
            try (Connection con = new DAO().obterConexaoEspecifica(chave)) {
                FormaPagamento formaPagamento = new FormaPagamento(con);
                json = formaPagamento.consultarJSON(situacao);
            }
        } catch (Exception e) {
            json = (e.getMessage() == null) ? NullPointerException.class.getName() : e.getMessage();
        }

        out.append(json);
        out.flush();
    }
}

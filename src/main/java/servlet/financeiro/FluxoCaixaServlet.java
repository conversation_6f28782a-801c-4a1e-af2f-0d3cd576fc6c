package servlet.financeiro;

import br.com.pactosolucoes.enumeradores.BIEnum;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import com.google.common.net.HttpHeaders;
import negocio.comuns.arquitetura.BI.FiltroBiVO;
import negocio.comuns.crm.MetaCRMTO;
import negocio.comuns.financeiro.FluxoCaixaTO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.BI.FiltroBi;
import negocio.facade.jdbc.utilitarias.Conexao;
import org.json.JSONArray;
import org.json.JSONObject;
import servicos.impl.fluxocaixa.FluxoCaixaServiceImpl;
import servlet.arquitetura.SuperServlet;
import servlet.crm.FiltroMetaDiariaJSON;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.sql.Connection;
import java.util.Date;
import java.util.List;

public class FluxoCaixaServlet extends SuperServlet {

    @Override
    protected void processRequest(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {

        response.setHeader("Access-Control-Allow-Origin", "*");
        response.setHeader("Access-Control-Allow-Methods", "GET, OPTIONS");
        response.setHeader("Access-Control-Max-Age", "3600");
        response.setHeader("Access-Control-Allow-Headers", "Authorization, content-type, xsrf-token");
        response.addHeader("Access-Control-Expose-Headers", "xsrf-token");
        response.setContentType("application/json");

        try{
            validarAuthorizationComChave(request);
            validarParametrosObrigatorios(request, new String[]{"empresa", "dataInicio", "dataFim"});

            String chave = request.getHeader(HttpHeaders.AUTHORIZATION);
            Connection connection = new DAO().obterConexaoEspecifica(chave);
            Conexao.guardarConexaoForJ2SE(chave, connection);

            Integer empresa = Integer.parseInt(request.getParameter("empresa"));
            Date dataInicio = Calendario.converterDataPrimeiraHoraDia(request.getParameter("dataInicio"));
            Date dataFim = Calendario.converterDataUltimaHoraDia(request.getParameter("dataFim"));
            Boolean incluirParcelasRecorrencia = Boolean.parseBoolean(request.getParameter("incluirParcelasRecorrencia"));
            Boolean incluirParcelasEmAberto = Boolean.parseBoolean(request.getParameter("incluirParcelasEmAberto"));
            Boolean reloadFull = Boolean.parseBoolean(request.getParameter("reloadFull"));
            // TODO: provisório. para manter a retrocompatibilidade com app. Após a versão do app ser atualizado em todos os clientes, esse parametro pode ser removido.
            String returnType = request.getParameter("returnType");
            returnType = returnType == null ? "data" : returnType;

            FluxoCaixaServiceImpl servico = new FluxoCaixaServiceImpl(connection);
            FiltroBi filtroBi = new FiltroBi(connection);
            FiltroFluxoCaixaJSON filtroJSON = new FiltroFluxoCaixaJSON(empresa, dataInicio.getTime(), dataFim.getTime(), incluirParcelasRecorrencia);
            FiltroBiVO filtroBiVO = filtroBi.obterPorToken(filtroJSON.getToken(), "FLUXO_CAIXA");

            if(filtroBiVO == null || reloadFull){
                if(filtroBiVO != null){
                    filtroBi.excluir(filtroBiVO.getTokenFiltro());
                }
                List<FluxoCaixaTO> listaFluxoCaixaPrevisto = servico.processarFluxoCaixa(
                        empresa,
                        dataInicio,
                        dataFim,
                        true,
                        0.0,
                        new FluxoCaixaTO(),
                        "",
                        incluirParcelasRecorrencia,
                        incluirParcelasEmAberto);

                List<FluxoCaixaTO> listaFluxoCaixaRealizado = servico.processarFluxoCaixa(
                        empresa,
                        dataInicio,
                        dataFim,
                        false,
                        0.0,
                        new FluxoCaixaTO(),
                        "",
                        incluirParcelasRecorrencia,
                        incluirParcelasEmAberto);

                JSONObject fluxoCaixa = converterFluxosCaixaJson(listaFluxoCaixaPrevisto, listaFluxoCaixaRealizado);
                filtroBiVO = filtroBi.incluir("FLUXO_CAIXA", filtroJSON, fluxoCaixa);
            }

            if(returnType.equals("cache")){
                response.getWriter().append(filtroBiVO.toJSON().toString());
            }else{
                response.getWriter().append(filtroBiVO.getJsonDados());
            }

            servico = null;
        } catch (Exception e) {
            processarErro(e, request, response, null);
        }
    }

    private JSONObject converterFluxosCaixaJson(List<FluxoCaixaTO> listaFluxoCaixaPrevisto, List<FluxoCaixaTO> listaFluxoCaixaRealizado){
        JSONObject fluxoCaixaJson = new JSONObject();
        JSONArray listaFluxtoCaixaJson = new JSONArray();
        Date hoje = Calendario.hoje();

        FluxoCaixaTO ultimoItemFluxoCaixaPrevisto = listaFluxoCaixaPrevisto.get(listaFluxoCaixaPrevisto.size() - 1);
        FluxoCaixaTO ultimoItemFluxoCaixaRealizado = listaFluxoCaixaRealizado.get(listaFluxoCaixaRealizado.size() - 1);

        fluxoCaixaJson.put("dataHoje", Uteis.getData(hoje));
        fluxoCaixaJson.put("dataFinal", Uteis.getData(ultimoItemFluxoCaixaPrevisto.getDia()));

        for (int i = 0; i < listaFluxoCaixaPrevisto.size(); i++) {
            JSONObject fluxoCaixaItemJson = new JSONObject();
            fluxoCaixaItemJson.put("dia", Uteis.getData(listaFluxoCaixaRealizado.get(i).getDia()));
            fluxoCaixaItemJson.put("previsto", converterfluxoCaixaItemJson(listaFluxoCaixaPrevisto.get(i)));
            fluxoCaixaItemJson.put("realizado", converterfluxoCaixaItemJson(listaFluxoCaixaRealizado.get(i)));

            if(Calendario.igual(listaFluxoCaixaRealizado.get(i).getDia(), hoje)){
                fluxoCaixaJson.put("saldoPrevistoHoje", listaFluxoCaixaPrevisto.get(i).getSaldo());
                fluxoCaixaJson.put("saldoRealizadoHoje", listaFluxoCaixaRealizado.get(i).getSaldo());

                fluxoCaixaJson.put("entradasPrevistasHoje", listaFluxoCaixaPrevisto.get(i).getEntradas());
                fluxoCaixaJson.put("entradasRealizasHoje", listaFluxoCaixaRealizado.get(i).getEntradas());

                fluxoCaixaJson.put("saidasPrevistasHoje", listaFluxoCaixaPrevisto.get(i).getSaidas());
                fluxoCaixaJson.put("saidasRealizasHoje", listaFluxoCaixaRealizado.get(i).getSaidas());
            }

            listaFluxtoCaixaJson.put(fluxoCaixaItemJson);
        }

        fluxoCaixaJson.put("saldoFinalPrevisto", ultimoItemFluxoCaixaPrevisto.getSaldo());
        fluxoCaixaJson.put("saldoFinalRealizado", ultimoItemFluxoCaixaRealizado.getSaldo());

        fluxoCaixaJson.put("fluxoCaixa", listaFluxtoCaixaJson);

        return fluxoCaixaJson;
    }

    private JSONObject converterfluxoCaixaItemJson(FluxoCaixaTO fluxoCaixaTO){
        JSONObject fluxoCaixa = new JSONObject();

        fluxoCaixa.put("entradas", fluxoCaixaTO.getEntradas());
        fluxoCaixa.put("saidas", fluxoCaixaTO.getSaidas());
        fluxoCaixa.put("saldo",  fluxoCaixaTO.getSaldo());

        return fluxoCaixa;
    }
}

package servlet.financeiro;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.arquitetura.UsuarioPerfilAcessoVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.Usuario;
import org.json.JSONObject;
import relatorio.negocio.jdbc.financeiro.TicketMedio;
import servlet.arquitetura.SuperServlet;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.sql.Connection;

public class TicketMedioAppServlet extends SuperServlet {

    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        processRequest(request, response);
    }

    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        processRequest(request, response);
    }


    protected void processRequest(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {

        response.addHeader("Access-Control-Allow-Origin", "*");
        response.addHeader("Access-Control-Allow-Methods", "GET");
        response.setContentType("application/json");
        response.setCharacterEncoding("UTF-8");
        try {

            String chave = request.getParameter("chave");
            String database = request.getParameter("database");
            Boolean atualizar = request.getParameter("atualizar") == null ? false : Boolean.valueOf(request.getParameter("atualizar"));
            Integer empresa = request.getParameter("empresa") == null ? null : Integer.valueOf(request.getParameter("empresa"));
            Integer mes = Calendario.getMes(Uteis.getDate(request.getParameter("database")));
            Integer ano = Calendario.getAno(Uteis.getDate(request.getParameter("database")));

            Boolean consultarPeloDRE = request.getParameter("consultarPeloDRE") == null ? false : Boolean.valueOf(request.getParameter("consultarPeloDRE"));
            Boolean incluirBolsa = request.getParameter("incluirBolsa") == null ? false : Boolean.valueOf(request.getParameter("incluirBolsa"));
            Integer usuario = request.getParameter("usuario") == null ? null : Integer.valueOf(request.getParameter("usuario"));

            try (Connection con = new DAO().obterConexaoEspecifica(chave)) {
                Usuario usuarioCon = new Usuario(con);
                UsuarioVO usuarioApp = usuarioCon.consultarPorCodigo(usuario, Uteis.NIVELMONTARDADOS_TODOS);
                if (usuarioApp == null) {
                    response.setStatus(HttpServletResponse.SC_NO_CONTENT);
                    processarErro("Usuário não encontrado.", response);
                } else if (!permissaoFuncionalidadeVisualizarBIETicketMedio(usuarioApp)) {
                    response.setStatus(HttpServletResponse.SC_FORBIDDEN);
                    processarErro("Usuário sem permissão para visualizar o ticket médio.", response);
                } else {
                    JSONObject content = new TicketMedio(con).gerarTicketMedioApp(atualizar, empresa, database, mes, ano, consultarPeloDRE, incluirBolsa);
                    response.getWriter().append(content.toString());
                }
            }
        } catch (Exception e) {
            System.out.println("Erro na api rest ticket medio. Message: " + e.getMessage());
            processarErro(e, request, response, null);
        }
    }

    private Boolean permissaoFuncionalidadeVisualizarBIETicketMedio(UsuarioVO usuarioApp) {
        for (UsuarioPerfilAcessoVO perfil : usuarioApp.getUsuarioPerfilAcessoVOs()) {
             if (perfil.getPerfilAcesso().getPermissaoVOs().stream().anyMatch(permissao -> "VisualizarBI".equals(permissao.getNomeEntidade())) &&
                     perfil.getPerfilAcesso().getPermissaoVOs().stream().anyMatch(permissao -> "TicketMedio".equals(permissao.getNomeEntidade()))) {
                    return true;
             }
        }
        return false;
    }
}

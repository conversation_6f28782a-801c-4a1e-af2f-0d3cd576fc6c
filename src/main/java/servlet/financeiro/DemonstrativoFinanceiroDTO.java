package servlet.financeiro;

import java.math.BigDecimal;
import java.util.List;

public class DemonstrativoFinanceiroDTO {
    private String nomeAgrupador;
    private List<LancamentoDTO> listaLancamentos;

    public String getNomeAgrupador() {
        return nomeAgrupador;
    }

    public void setNomeAgrupador(String nomeAgrupador) {
        this.nomeAgrupador = nomeAgrupador;
    }

    public void setListaLancamentos(List<LancamentoDTO> listaLancamentos) {
        this.listaLancamentos = listaLancamentos;
    }

    public List<LancamentoDTO> getListaLancamentos() {
        return listaLancamentos;
    }

    static class LancamentoDTO {
        private String descricaoLancamento;
        private String contrato;
        private String nomePessoa;
        private String mesReferencia;
        private Double valorLancamento;
        private List<RateioDTO> rateios;

        public List<RateioDTO> getRateios() {
            return rateios;
        }

        public void setRateios(List<RateioDTO> rateios) {
            this.rateios = rateios;
        }

        public Double getValorLancamento() {
            return valorLancamento;
        }

        public void setValorLancamento(Double valorLancamento) {
            this.valorLancamento = valorLancamento;
        }

        public String getMesReferencia() {
            return mesReferencia;
        }

        public void setMesReferencia(String mesReferencia) {
            this.mesReferencia = mesReferencia;
        }

        public String getNomePessoa() {
            return nomePessoa;
        }

        public void setNomePessoa(String nomePessoa) {
            this.nomePessoa = nomePessoa;
        }

        public String getContrato() {
            return contrato;
        }

        public void setContrato(String contrato) {
            this.contrato = contrato;
        }

        public String getDescricaoLancamento() {
            return descricaoLancamento;
        }

        public void setDescricaoLancamento(String descricaoLancamento) {
            this.descricaoLancamento = descricaoLancamento;
        }

    }

    static class RateioDTO {
        private String descricao;
        private String nomePlano;
        private Double percentagem;
        private Double totalPlanoConta;
        private String nomeCentroCusto;
        private Double percentagemCentroCusto;
        private Double totalCentroCusto;

        public String getDescricao() {
            return descricao;
        }

        public void setDescricao(String descricao) {
            this.descricao = descricao;
        }

        public String getNomePlano() {
            return nomePlano;
        }

        public void setNomePlano(String nomePlano) {
            this.nomePlano = nomePlano;
        }

        public Double getPercentagem() {
            return percentagem;
        }

        public void setPercentagem(Double percentagem) {
            this.percentagem = percentagem;
        }

        public Double getTotalPlanoConta() {
            return totalPlanoConta;
        }

        public void setTotalPlanoConta(Double totalPlanoConta) {
            this.totalPlanoConta = totalPlanoConta;
        }

        public String getNomeCentroCusto() {
            return nomeCentroCusto;
        }

        public void setNomeCentroCusto(String nomeCentroCusto) {
            this.nomeCentroCusto = nomeCentroCusto;
        }

        public Double getPercentagemCentroCusto() {
            return percentagemCentroCusto;
        }

        public void setPercentagemCentroCusto(Double percentagemCentroCusto) {
            this.percentagemCentroCusto = percentagemCentroCusto;
        }

        public Double getTotalCentroCusto() {
            return totalCentroCusto;
        }

        public void setTotalCentroCusto(Double totalCentroCusto) {
            this.totalCentroCusto = totalCentroCusto;
        }
    }

}

package servlet.financeiro;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import com.fasterxml.jackson.databind.ObjectMapper;
import controle.financeiro.DREControle;
import negocio.comuns.financeiro.enumerador.TipoRelatorioDF;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.utilitarias.Conexao;
import org.json.JSONObject;
import relatorio.negocio.jdbc.financeiro.DemonstrativoFinanceiro;
import relatorio.negocio.jdbc.financeiro.LancamentoDF;
import relatorio.negocio.jdbc.financeiro.RelatorioDRE;
import servlet.arquitetura.SuperServlet;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.sql.Connection;
import java.util.*;
import java.util.stream.Collectors;

public class DREFinanceiroServlet extends SuperServlet {

    private Connection con;

    @Override
    protected void processRequest(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        response.setHeader("Access-Control-Allow-Origin", "*");
        response.setHeader("Access-Control-Allow-Methods", "GET, OPTIONS");
        response.setHeader("Access-Control-Max-Age", "3600");
        response.setHeader("Access-Control-Allow-Headers", "Authorization, content-type, xsrf-token");
        response.addHeader("Access-Control-Expose-Headers", "xsrf-token");
        response.setContentType("application/json");
        response.setCharacterEncoding("UTF-8");
        String method = request.getMethod();
        try {
            if (!method.equalsIgnoreCase("GET")) {
                throw new Exception("Método não suportado.");
            }
            String key = request.getParameter("key");
            if (UteisValidacao.emptyString(key)) {
                throw new Exception("Chave não informada.");
            }
            String dataInicio = request.getParameter("dataInicio");
            if (Objects.isNull(dataInicio)) {
                throw new Exception("Informe a data de inicio.");
            }
            String dataFinal = request.getParameter("dataFim");
            if (Objects.isNull(dataFinal)) {
                throw new Exception("Informe a data final da conciliação.");
            }
            String codigoEmpresa = request.getParameter("codigoEmpresa");
            if (Objects.isNull(codigoEmpresa)) {
                throw new Exception("Informe o codigo da empresa .");
            }
            Date dataIni = Calendario.getDate("dd/MM/yyyy", dataInicio);
            Date dataFim = Calendario.getDate("dd/MM/yyyy", dataFinal);

            this.con = new DAO().obterConexaoEspecifica(key);
            Conexao.guardarConexaoForJ2SE(con);

            DREControle controle = new DREControle();
            String tipoRelatorioParam = request.getParameter("tipoRelatorio");

            TipoRelatorioDF tipoRelatorioDF = Arrays.stream(TipoRelatorioDF.values())
                    .filter(a -> a.name().equals(tipoRelatorioParam))
                    .findFirst()
                    .orElse(TipoRelatorioDF.FATURAMENTO_DE_CAIXA);

            boolean apresentarDevolucoesRel = true;
            List<DemonstrativoFinanceiro> listaRelatorioDemonstrativo = controle.obterDemonstrativos(
                    tipoRelatorioDF,
                    true,
                    apresentarDevolucoesRel,
                    dataIni,
                    dataFim,
                    Integer.parseInt(codigoEmpresa)
            );
            List<DemonstrativoFinanceiroDTO> demonstrativosFinanceiros = listaRelatorioDemonstrativo.stream().map(item -> {
                DemonstrativoFinanceiroDTO dto = new DemonstrativoFinanceiroDTO();
                dto.setNomeAgrupador(item.getNomeAgrupador());
                List<DemonstrativoFinanceiroDTO.LancamentoDTO> lancamentoDTOS = new ArrayList<>();
                if(item.getListaTotalizadorMeses() != null && !item.getListaTotalizadorMeses().isEmpty()) {
                    item.getListaTotalizadorMeses().get(0).getListaLancamentos().forEach(lancamento -> {
                        DemonstrativoFinanceiroDTO.LancamentoDTO lancamentoDTO = new DemonstrativoFinanceiroDTO.LancamentoDTO();
                        lancamentoDTO.setDescricaoLancamento(lancamento.getDescricaoLancamento());
                        lancamentoDTO.setContrato(String.valueOf(lancamento.getContrato()));
                        lancamentoDTO.setValorLancamento(lancamento.getValorLancamento());
                        lancamentoDTO.setMesReferencia(lancamento.getMesReferencia());
                        lancamentoDTO.setNomePessoa(lancamento.getNomePessoa());
                        List<DemonstrativoFinanceiroDTO.RateioDTO> rateioDTOS = new ArrayList<>();
                        if(lancamento.getRateios() != null && !lancamento.getRateios().isEmpty()) {
                            lancamento.getRateios().forEach(rateio -> {
                                DemonstrativoFinanceiroDTO.RateioDTO rateioDTO = new DemonstrativoFinanceiroDTO.RateioDTO();
                                rateioDTO.setDescricao(rateio.getDescricao());
                                rateioDTO.setPercentagem(rateio.getPercentagem());
                                rateioDTO.setNomePlano(rateio.getNomePlano());
                                rateioDTO.setNomeCentroCusto(rateio.getNomeCentro());
                                rateioDTO.setPercentagemCentroCusto(rateio.getPercentagemCentroCusto());
                                rateioDTO.setTotalCentroCusto(rateio.getTotalCentroCusto());
                                rateioDTO.setTotalPlanoConta(rateio.getTotalPlanoConta());
                                rateioDTOS.add(rateioDTO);
                            });
                        }
                        lancamentoDTO.setRateios(rateioDTOS);
                        lancamentoDTOS.add(lancamentoDTO);
                    });
                }
                dto.setListaLancamentos(lancamentoDTOS);
                return dto;
            }).collect(Collectors.toList());
            ObjectMapper mapper = new ObjectMapper();
            String json = mapper.writeValueAsString(demonstrativosFinanceiros);
            response.getWriter().write(json);
        } catch (Exception ex) {
            response.setStatus(400);
            response.getWriter().append(this.toJSON(false, ex.getMessage()).toString());
        } finally {
            if (con != null) {
                try {
                    con.close();
                } catch (Exception ex) {}
            }
        }
    }

    private JSONObject toJSON(boolean sucesso, Object dados) {
        JSONObject json = new JSONObject();
        json.put("sucesso", sucesso);
        json.put("dados", dados);
        return json;
    }


}

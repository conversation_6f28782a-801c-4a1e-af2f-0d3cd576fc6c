package servlet.financeiro;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import controle.arquitetura.threads.ThreadDemonstrativoFinanceiro;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.MovPagamentoIntegracaoTO;
import negocio.comuns.financeiro.RateioIntegracaoTO;
import negocio.comuns.financeiro.enumerador.TipoCamposPesquisarRateio;
import negocio.comuns.financeiro.enumerador.TipoVisualizacaoRelatorioDF;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.FacadeManager;
import negocio.facade.jdbc.basico.Empresa;
import negocio.facade.jdbc.financeiro.MovPagamento;
import negocio.facade.jdbc.utilitarias.Conexao;
import org.apache.commons.collections.map.HashedMap;
import org.json.JSONObject;
import relatorio.negocio.jdbc.financeiro.ContratoModalidadePercentual;
import relatorio.negocio.jdbc.financeiro.LancamentoDF;
import relatorio.negocio.jdbc.financeiro.MesProcessar;
import relatorio.negocio.jdbc.financeiro.ProdutoRatear;
import servlet.arquitetura.SuperServlet;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.sql.Connection;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class RecebiveisServlet extends SuperServlet {

    private Connection con;
    private ThreadDemonstrativoFinanceiro threadDemonstrativoFinanceiro;
    private List<RateioIntegracaoTO> listaRateiosIntegracao = null;
    private EmpresaVO empresaVO = null;

    @Override
    protected void processRequest(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        try {
            response.addHeader("Access-Control-Allow-Origin", "*");
            response.addHeader("Access-Control-Allow-Methods", "GET,POST");
            response.setContentType("application/json");

            String method = request.getMethod();

            if (!method.equalsIgnoreCase("POST")) {
                throw new Exception("Método não suportado.");
            }

            String key = request.getParameter("key");
            if (UteisValidacao.emptyString(key)) {
                throw new Exception("Chave não informada.");
            }

            OperacaoRecebiveisDTO operacaoDTO = null;

            try {
                InputStream inputStream = request.getInputStream();
                BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream, "UTF-8"));

                StringBuffer body = new StringBuffer();
                String line = null;

                while ((line = reader.readLine()) != null) {
                    body.append(line);
                }

                operacaoDTO = new OperacaoRecebiveisDTO(new JSONObject(body.toString()));
            } catch (Exception ex) {
                ex.printStackTrace();
                throw new Exception("Erro body: " + ex.getMessage());
            }

            if (UteisValidacao.emptyNumber(operacaoDTO.getEmpresa()) && UteisValidacao.emptyString(operacaoDTO.getCnpj())) {
                throw new Exception("Empresa não informada.");
            }

            if (operacaoDTO.getDataLancamentoInicial() == null || operacaoDTO.getDataLancamentoFinal() == null) {
                throw new Exception("Informe o período de lançamento.");
            }

            if (Calendario.diferencaEmDias(operacaoDTO.getDataLancamentoInicial(), operacaoDTO.getDataLancamentoFinal()) > 31) {
                throw new Exception("Intervalo máximo das datas de 31 dias");
            }

            this.con = new DAO().obterConexaoEspecifica(key);
            Conexao.guardarConexaoForJ2SE(con);

            if(!UteisValidacao.emptyNumber(operacaoDTO.getEmpresa())) {
                this.empresaVO = FacadeManager.getFacade().getEmpresa().consultarPorCodigo(operacaoDTO.getEmpresa(), Uteis.NIVELMONTARDADOS_ROBO);
            } else {
                this.empresaVO = FacadeManager.getFacade().getEmpresa().consultarPorCnpj(operacaoDTO.getCnpj(), Uteis.NIVELMONTARDADOS_ROBO);
            }

            if (this.empresaVO == null) {
                throw new Exception("Empresa não encontrada.");
            }

            Object objRetorno = null;
            if (operacaoDTO.getOperacao().equalsIgnoreCase("consultarRecebiveis")) {
                MovPagamento dao = new MovPagamento(con);
                ResultSet rsMovimentacoes = dao.consultarMovimentacoesRecebiveisIntegracao(operacaoDTO.getDataLancamentoInicial(), operacaoDTO.getDataLancamentoFinal(), empresaVO.getCodigo(), operacaoDTO.getMatricula());;

                List<RecebiveisResumoDTO> listaRetorno = new ArrayList<>();
                Map<Integer, MovPagamentoIntegracaoTO> composicoesCartao = new HashMap();
                Map<Integer, MovPagamentoIntegracaoTO> composicoesCheque = new HashMap();

                this.threadDemonstrativoFinanceiro = new ThreadDemonstrativoFinanceiro(con, null, null, null, null, false);
                this.listaRateiosIntegracao = this.threadDemonstrativoFinanceiro.consultarTodosRateiosIntegracao(con);

                List<MovPagamentoIntegracaoTO> listaMovimentacoesComComposicao = new ArrayList<>();

                while (rsMovimentacoes.next()) {
                    MovPagamentoIntegracaoTO item = new MovPagamentoIntegracaoTO(rsMovimentacoes);
                    MovPagamentoIntegracaoTO itemComComposicao = null;

                    //separar composição
                    if (!UteisValidacao.emptyNumber(item.getCodigoCartao()) && !UteisValidacao.emptyString(item.getComposicaoCartao())) {
                        itemComComposicao = getItemComposicao(composicoesCartao, item.getCodigoCartao(), item.getComposicaoCartao(), item);
                    } else if (!UteisValidacao.emptyNumber(item.getCodigoCheque()) && !UteisValidacao.emptyString(item.getComposicaoCheque())) {
                        itemComComposicao = getItemComposicao(composicoesCheque, item.getCodigoCartao(), item.getComposicaoCheque(), item);
                    } else {
                        preencherRecebiveis(item, listaRetorno, operacaoDTO.getDocumento());
                        continue;
                    }

                    if (itemComComposicao != null) {
                        continue; //não adicionar esse item a lista porque a referencia de composição dele já foi adicionada
                    }

                    listaMovimentacoesComComposicao.add(item);
                }

                for (MovPagamentoIntegracaoTO item : listaMovimentacoesComComposicao) {
                    preencherRecebiveis(item, listaRetorno, operacaoDTO.getDocumento());
                }

                objRetorno = listaRetorno;
            }

            if (objRetorno == null) {
                throw new Exception("Nenhuma operação executada");
            }

            response.getWriter().append(this.toJSON(true, objRetorno).toString());
        } catch (Exception ex) {
            response.setStatus(400);
            response.getWriter().append(this.toJSON(false, ex.getMessage()).toString());
        } finally {
            if (con != null) {
                try {
                    con.close();
                } catch (Exception ex) {
                }
            }
        }
    }

    private void preencherRecebiveis(MovPagamentoIntegracaoTO item, List<RecebiveisResumoDTO> listaRetorno, String documento) throws Exception {
        RecebiveisResumoDTO dto = new RecebiveisResumoDTO(item);

        if (UteisValidacao.emptyString(documento) || documento.equals(dto.getDocumento())) {
            List<ProdutoRatear> listaProdutoRatearDoItem = this.threadDemonstrativoFinanceiro.pesquisarProdutosDoPagamento(item.getCodigo(), item.getProdutosPagos(), con, null);
            dto.setMovto(getCodigoPlanoContasRecebiveis(listaProdutoRatearDoItem));
            listaRetorno.add(dto);
        }
    }

    private MovPagamentoIntegracaoTO getItemComposicao(Map<Integer, MovPagamentoIntegracaoTO> mapComposicoes, Integer codigo, String composicao, MovPagamentoIntegracaoTO item) throws Exception {
        if (mapComposicoes.containsKey(codigo)) {
            return mapComposicoes.get(codigo);
        }

        MovPagamentoIntegracaoTO itemComComposicao = null;

        if (UteisValidacao.emptyString(composicao)) {
            String[] composicoes = composicao.split(",");
            for (int x = 0; x < composicoes.length; x++) {
                try {
                    itemComComposicao = mapComposicoes.get(Integer.parseInt(composicoes[x].trim()));
                    if (itemComComposicao != null) {
                        adicionarProdutosPagosDasComposicoes(itemComComposicao, item);
                    }
                } catch (Exception ex) {
                    Uteis.logar(ex, RecebiveisServlet.class);
                }
            }

            mapComposicoes.put(codigo, item);
        }

        return itemComComposicao;
    }

    private void adicionarProdutosPagosDasComposicoes(MovPagamentoIntegracaoTO itemComComposicao, MovPagamentoIntegracaoTO itemDuplicado) {
        if (!UteisValidacao.emptyString(itemDuplicado.getProdutosPagos())) {
            String produtosPagos = itemComComposicao.getProdutosPagos() != null ? itemComComposicao.getProdutosPagos() : "";

            if (!produtosPagos.contains(itemDuplicado.getProdutosPagos())) {
                produtosPagos += itemDuplicado.getProdutosPagos();
            }

            itemComComposicao.setProdutosPagos(produtosPagos);
            itemDuplicado.setProdutosPagos(produtosPagos);
        }
    }

    private JSONObject toJSON(boolean sucesso, Object dados) {
        JSONObject json = new JSONObject();
        json.put("sucesso", sucesso);
        json.put("dados", dados);
        return json;
    }

    private Integer getCodigoPlanoContasRecebiveis(List<ProdutoRatear> listaProdutoRatearDoItem) throws Exception {
        try {
            List<RateioIntegracaoTO> listaRateio = new ArrayList<>();
            for (ProdutoRatear produtoRatear : listaProdutoRatearDoItem) {
                //dto.setMulta(produtoRatear.getMultaJuros());
                if (produtoRatear.getTipoProduto().equals("PM")){
                    // Realizar rateio para venda de Plano
                    realizarRateioPlanoRecebiveis(produtoRatear, listaRateio);
                } else if ((produtoRatear.getTipoProduto().equals("DI"))
                        || (produtoRatear.getTipoProduto().equals("AA"))) {
                    // Realizar rateio para venda de Diária e Aula Avulsa
                    realizarRateioAulaAvulsaEDiariaRecebiveis(produtoRatear, listaRateio);
                } else {
                    // Realizar rateio para venda de Produto/Serviço
                    realizarRateioVendaProdutoServicoRecebiveis(produtoRatear, listaRateio);
                }
            }

            Map<Integer, RateioIntegracaoTO> mapPlanoContas = new HashedMap();
            Integer ultimoCodigoPlanoContas = null;

            for(RateioIntegracaoTO item : listaRateio) {
                if(!UteisValidacao.emptyNumber(item.getCodigoPlanoContas()) && !mapPlanoContas.containsKey(item.getCodigoPlanoContas())) {
                    mapPlanoContas.put(item.getCodigoPlanoContas(), item);
                    if(UteisValidacao.emptyNumber(ultimoCodigoPlanoContas)) {
                        ultimoCodigoPlanoContas = item.getCodigoPlanoContas();
                    } else if(ultimoCodigoPlanoContas < item.getCodigoPlanoContas()) {
                        ultimoCodigoPlanoContas = item.getCodigoPlanoContas();
                    }
                }
            }

            return FacadeManager.getFacade().getPlanoConta().consultarCodigoLumi(ultimoCodigoPlanoContas);
        } catch (Exception e) {
            e.printStackTrace();
        }

        return 0;
    }

    private void realizarRateioPlanoRecebiveis(ProdutoRatear produtoRatear, List<RateioIntegracaoTO> rateios) throws Exception {
        List<ContratoModalidadePercentual> listaModalidadesPercentual = produtoRatear.getListaContratoModalidadePercentual();
        Double perc = 0.0;
        if(listaModalidadesPercentual != null){
            for(ContratoModalidadePercentual obj : listaModalidadesPercentual){
                perc += obj.getPercentagem();
            }
        }
        for (ContratoModalidadePercentual obj : listaModalidadesPercentual) {
            if(perc.equals(new Double(0.0))){
                obj.setValor(Uteis.arredondarForcando2CasasDecimais(produtoRatear.getValorRatear() / listaModalidadesPercentual.size()));
            }else{
                Double valorperc = (new Double(obj.getPercentagem()).equals(new Double(0.0)) && perc.equals(new Double(0.0))) ? 0.0 : obj.getPercentagem()/perc;
                // Calcular o valor que cada modalidade representa em relação ao valor do Lancamento.
                obj.setValor(listaModalidadesPercentual.size() == 1 ? produtoRatear.getValorRatear() : valorperc * produtoRatear.getValorRatear());
            }
            obj.setNomeParcela(produtoRatear.getDescricaoParcela());
        }

        for (ContratoModalidadePercentual contratoModalidadePerc : listaModalidadesPercentual) {
            // Pesquisa os rateios definido para cada modalidade.
            List<RateioIntegracaoTO> listaRateiosModalidade = this.threadDemonstrativoFinanceiro.pesquisarRateios(this.listaRateiosIntegracao,
                    TipoCamposPesquisarRateio.MODALIDADE,
                    contratoModalidadePerc.getCodigoModalidade(),
                    TipoVisualizacaoRelatorioDF.PLANOCONTA.PLANOCONTA, empresaVO.getCodigo());
            if (listaRateiosModalidade.isEmpty()) {
                listaRateiosModalidade = this.threadDemonstrativoFinanceiro.pesquisarRateios(this.listaRateiosIntegracao,
                        TipoCamposPesquisarRateio.GERAL_MODALIDADES,
                        contratoModalidadePerc.getCodigoModalidade(),
                        TipoVisualizacaoRelatorioDF.PLANOCONTA, empresaVO.getCodigo());
            }
            if (listaRateiosModalidade.size() > 0) {
                rateios.addAll(listaRateiosModalidade);
            }
        }
    }

    private void realizarRateioAulaAvulsaEDiariaRecebiveis(ProdutoRatear produtoRatear, List<RateioIntegracaoTO> rateios) throws Exception {
        // Pesquisar os rateios definidos para o Produto "Aula avulsa" ou "Diária".
        List<RateioIntegracaoTO> listaRateios = this.threadDemonstrativoFinanceiro.pesquisarRateios(this.listaRateiosIntegracao, TipoCamposPesquisarRateio.PRODUTO, produtoRatear.getCodigoProduto(), TipoVisualizacaoRelatorioDF.PLANOCONTA, empresaVO.getCodigo());

        if (listaRateios.size() <= 0) {
            // Não foi informado um rateio para o produto, desta forma, pesquisar os rateios da categoria do produto.
            listaRateios = this.threadDemonstrativoFinanceiro.pesquisarRateios(this.listaRateiosIntegracao, TipoCamposPesquisarRateio.CATEGORIAPRODUTO, produtoRatear.getCodigoCategoriaProduto(), TipoVisualizacaoRelatorioDF.PLANOCONTA, empresaVO.getCodigo());
        }

        if (listaRateios.size() > 0) {
            rateios.addAll(listaRateios);
        }
    }

    private void realizarRateioVendaProdutoServicoRecebiveis(ProdutoRatear produtoRatear, List<RateioIntegracaoTO> rateios) throws Exception {
        // Pesquisar rateios definido para o produto.
        List<RateioIntegracaoTO> listaRateios = this.threadDemonstrativoFinanceiro.pesquisarRateios(this.listaRateiosIntegracao,
                TipoCamposPesquisarRateio.PRODUTO,
                produtoRatear.getCodigoProduto(),
                TipoVisualizacaoRelatorioDF.PLANOCONTA, empresaVO.getCodigo());
        if (listaRateios.size() <= 0) {
            // Pesquisar rateios definido para a categoria do produto.
            listaRateios = this.threadDemonstrativoFinanceiro.pesquisarRateios(this.listaRateiosIntegracao,
                    TipoCamposPesquisarRateio.CATEGORIAPRODUTO,
                    produtoRatear.getCodigoCategoriaProduto(),
                    TipoVisualizacaoRelatorioDF.PLANOCONTA, empresaVO.getCodigo());

        }

        if (listaRateios.size() > 0) {
            rateios.addAll(listaRateios);
        }
    }

}

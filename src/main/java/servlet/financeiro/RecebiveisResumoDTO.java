package servlet.financeiro;

import negocio.comuns.basico.MovPagamentoIntegracaoTO;
import negocio.comuns.financeiro.RecebivelTO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;

public class RecebiveisResumoDTO {

    private String tipo;
    private String estab;
    private String cpf_cnpj;
    private String vencimento;
    private String emissao;
    private Double valor;
    private Double multa;
    private Double desconto;
    private String documento;
    private String historico;
    private Integer movto;
    private String liquida;

    public RecebiveisResumoDTO(MovPagamentoIntegracaoTO movPagamentoIntegracaoTO) {
        setTipo(Uteis.nrDiasEntreDatas(movPagamentoIntegracaoTO.getDataCompensacao(), Calendario.hoje()) <= 0 ? "R" : "P");
        setEstab(Uteis.aplicarMascara(movPagamentoIntegracaoTO.getCnpjEmpresa(), "99.999.999/9999-99"));
        setCpf_cnpj(Uteis.aplicarMascara(movPagamentoIntegracaoTO.getCnpjEmpresa(), "99.999.999/9999-99"));
        setVencimento(Uteis.getData(movPagamentoIntegracaoTO.getDataCompensacao(), "br"));
        setEmissao(Uteis.getData(movPagamentoIntegracaoTO.getDataLancamento(), "br"));
        setValor(movPagamentoIntegracaoTO.getValor());
        setMulta(0.0);
        setDesconto(0.0);
        setDocumento(movPagamentoIntegracaoTO.getDocumento());
        if (!UteisValidacao.emptyNumber(movPagamentoIntegracaoTO.getCodigoCartao()) && !UteisValidacao.emptyString(movPagamentoIntegracaoTO.getAutorizacaoCartao())) {
            setHistorico((UteisValidacao.emptyString(movPagamentoIntegracaoTO.getCpfPessoa()) ?
                    "0" : movPagamentoIntegracaoTO.getCpfPessoa()) + ";" + movPagamentoIntegracaoTO.getAutorizacaoCartao());
        }
        setMovto(null);
        setLiquida(Uteis.getData(movPagamentoIntegracaoTO.getDataCompensacao(), "br"));
    }

    public String getTipo() {
        return tipo;
    }

    public void setTipo(String tipo) {
        this.tipo = tipo;
    }

    public String getEstab() {
        return estab;
    }

    public void setEstab(String estab) {
        this.estab = estab;
    }

    public String getCpf_cnpj() {
        return cpf_cnpj;
    }

    public void setCpf_cnpj(String cpf_cnpj) {
        this.cpf_cnpj = cpf_cnpj;
    }

    public String getVencimento() {
        return vencimento;
    }

    public void setVencimento(String vencimento) {
        this.vencimento = vencimento;
    }

    public String getEmissao() {
        return emissao;
    }

    public void setEmissao(String emissao) {
        this.emissao = emissao;
    }

    public Double getValor() {
        return valor;
    }

    public void setValor(Double valor) {
        this.valor = valor;
    }

    public Double getMulta() {
        return multa;
    }

    public void setMulta(Double multa) {
        this.multa = multa;
    }

    public Double getDesconto() {
        return desconto;
    }

    public void setDesconto(Double desconto) {
        this.desconto = desconto;
    }

    public String getDocumento() {
        return documento;
    }

    public void setDocumento(String documento) {
        this.documento = documento;
    }

    public String getHistorico() {
        return historico;
    }

    public void setHistorico(String historico) {
        this.historico = historico;
    }

    public Integer getMovto() {
        return movto;
    }

    public void setMovto(Integer movto) {
        this.movto = movto;
    }

    public String getLiquida() {
        return liquida;
    }

    public void setLiquida(String liquida) {
        this.liquida = liquida;
    }
}

package servlet.financeiro;

public class RecebiveisChargebackDTO {

    private String nomePagador;
    private String contaMovimento;
    private String autorizacao;
    private String nsu;
    private String tipoConcilicacao;
    private String getNrParcelas_apresentar;
    private String valorBruto;
    private String formaDePagamento;
    private String valorCC;
    private String valorMP;
    private String dataLancamentoMP;
    private String dataPrevistaPagamentoApresentar;
    private String dataLancamentoApresentar;
    private Integer nrParcelaCartaoCredito;

    public RecebiveisChargebackDTO() {
    }


    public String getNomePagador() {
        return nomePagador;
    }

    public void setNomePagador(String nomePagador) {
        this.nomePagador = nomePagador;
    }

    public String getContaMovimento() {
        return contaMovimento;
    }

    public void setContaMovimento(String contaMovimento) {
        this.contaMovimento = contaMovimento;
    }

    public String getAutorizacao() {
        return autorizacao;
    }

    public void setAutorizacao(String autorizacao) {
        this.autorizacao = autorizacao;
    }

    public String getNsu() {
        return nsu;
    }

    public void setNsu(String nsu) {
        this.nsu = nsu;
    }


    public String getGetNrParcelas_apresentar() {
        return getNrParcelas_apresentar;
    }

    public void setGetNrParcelas_apresentar(String getNrParcelas_apresentar) {
        this.getNrParcelas_apresentar = getNrParcelas_apresentar;
    }

    public String getValorBruto() {
        return valorBruto;
    }

    public void setValorBruto(String valorBruto) {
        this.valorBruto = valorBruto;
    }

    public String getFormaPagamento() {
        return formaDePagamento;
    }

    public void setFormaPagamento(String formaPagamentoDiferente) {
        this.formaDePagamento = formaPagamentoDiferente;
    }

    public String getValorCC() {
        return valorCC;
    }

    public void setValorCC(String valorCC) {
        this.valorCC = valorCC;
    }

    public String getValorMP() {
        return valorMP;
    }

    public void setValorMP(String valorMP) {
        this.valorMP = valorMP;
    }

    public String getDataLancamentoMP() {
        return dataLancamentoMP;
    }

    public void setDataLancamentoMP(String dataLancamentoMP) {
        this.dataLancamentoMP = dataLancamentoMP;
    }

    public String getDataPrevistaPagamentoApresentar() {
        return dataPrevistaPagamentoApresentar;
    }

    public void setDataPrevistaPagamentoApresentar(String dataPrevistaPagamentoApresentar) {
        this.dataPrevistaPagamentoApresentar = dataPrevistaPagamentoApresentar;
    }

    public String getDataLancamentoApresentar() {
        return dataLancamentoApresentar;
    }

    public void setDataLancamentoApresentar(String dataLancamentoApresentar) {
        this.dataLancamentoApresentar = dataLancamentoApresentar;
    }

    public Integer getNrParcelaCartaoCredito() {
        return nrParcelaCartaoCredito;
    }

    public void setNrParcelaCartaoCredito(Integer nrParcelaCartaoCredito) {
        this.nrParcelaCartaoCredito = nrParcelaCartaoCredito;
    }

    public String getTipoConcilicacao() {
        return tipoConcilicacao;
    }

    public void setTipoConcilicacao(String tipoConcilicacao) {
        this.tipoConcilicacao = tipoConcilicacao;
    }
}

package servlet.financeiro;

import br.com.pactosolucoes.enumeradores.TipoContaPagarLoteEnum;
import br.com.pactosolucoes.enumeradores.TipoOperacaoLancamento;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import controle.arquitetura.threads.ThreadDemonstrativoFinanceiro;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.MovPagamentoIntegracaoTO;
import negocio.comuns.financeiro.MovContaVO;
import negocio.comuns.financeiro.RateioIntegracaoTO;
import negocio.comuns.financeiro.enumerador.TipoCamposPesquisarRateio;
import negocio.comuns.financeiro.enumerador.TipoVisualizacaoRelatorioDF;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.FacadeManager;
import negocio.facade.jdbc.financeiro.MovConta;
import org.apache.commons.collections.map.HashedMap;
import org.json.JSONObject;
import relatorio.negocio.jdbc.financeiro.ContratoModalidadePercentual;
import relatorio.negocio.jdbc.financeiro.ProdutoRatear;
import servlet.arquitetura.SuperServlet;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.sql.Connection;
import java.util.*;

public class ModuloFinanceiroServlet extends SuperServlet {

    private Connection con;
    private ThreadDemonstrativoFinanceiro threadDemonstrativoFinanceiro;
    private List<RateioIntegracaoTO> listaRateiosIntegracao = null;
    private EmpresaVO empresaVO = null;

    @Override
    protected void processRequest(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        try {
            response.addHeader("Access-Control-Allow-Origin", "*");
            response.addHeader("Access-Control-Allow-Methods", "GET,POST");
            response.setContentType("application/json");

            String method = request.getMethod();

            if (!method.equals("POST")) {
                throw new Exception("Método não permitido.");
            }

            String chave = request.getParameter("chave");
            if (UteisValidacao.emptyString(chave)) {
                throw new Exception("Chave não informada.");
            }

            String op = request.getParameter("op");

            if (op.equals("cadastrarConta")) {
                String body = obterBody(request);
                MovContaVO movContaVO = new MovContaVO();
                try {
                    movContaVO = new MovContaVO(new JSONObject(body));
                } catch (Exception ex) {
                    PrintWriter out = response.getWriter();
                    JSONObject json = new JSONObject();
                    json.put("ERRO: ", ex.getMessage());
                    out.println(json.toString());
                    return;
                }

                JSONObject json = cadastrarConta(movContaVO, chave);
                PrintWriter out = response.getWriter();
                out.println(json.toString());

            } else {
                throw new Exception("Nenhuma operação realizada");
            }
        } catch (Exception ex) {
            response.setStatus(400);
            response.getWriter().append(this.toJSON(false, ex.getMessage()).toString());
        }
    }

    private JSONObject cadastrarConta(MovContaVO movContaVO, String chave) throws Exception {
        try {
            validarDadosMovConta(movContaVO);
            return incluirMovConta(movContaVO, chave);
        } catch (Exception ex) {
            JSONObject json = new JSONObject();
            json.put("ERRO: ", ex.getMessage());
            return json;
        }
    }

    private JSONObject incluirMovConta(MovContaVO movContaVO, String chave) throws Exception {
        try (Connection con = new DAO().obterConexaoEspecifica(chave)) {
            MovConta movContaDAO;
            try {
                movContaDAO = new MovConta(con);
                MovContaVO movContaVOIncluida = movContaDAO.incluir(movContaVO);
                return movContaToJson(movContaVOIncluida);
            } catch (Exception ex) {
                ex.printStackTrace();
                throw ex;
            } finally {
                movContaDAO = null;
            }
        }
    }

    public JSONObject movContaToJson(MovContaVO movContaVO) {
        JSONObject json = new JSONObject();
        json.put("codigo", movContaVO.getCodigo());
        json.put("descricao", movContaVO.getDescricao());
        json.put("pessoa", movContaVO.getPessoaVO().getCodigo());
        json.put("usuario", movContaVO.getUsuarioVO().getCodigo());
        json.put("empresa", movContaVO.getEmpresaVO().getCodigo());
        json.put("observacoes", movContaVO.getObservacoes());
        json.put("valor", movContaVO.getValor());
        json.put("dataLancamento", Uteis.getDataComHHMMSS(movContaVO.getDataLancamento()));
        json.put("dataVencimento", Uteis.getData(movContaVO.getDataVencimento()));
        json.put("dataCompetencia", Uteis.getData(movContaVO.getDataCompetencia()));
        json.put("tipoOperacaoLancamentoEnum", movContaVO.getTipoOperacaoLancamento().getCodigo());
        if (movContaVO.getTipoContaPagarLoteEnum() != null) {
            json.put("tipoContaPagarLoteEnum", movContaVO.getTipoContaPagarLoteEnum().getCodigo());
        }
        json.put("codigoBarras", movContaVO.getCodigoBarras());
        json.put("payloadPix", movContaVO.getPayloadPix());
        return json;
    }

    private void validarDadosMovConta(MovContaVO movContaVO) throws Exception {
        if (movContaVO.getPessoaVO() == null || UteisValidacao.emptyNumber(movContaVO.getPessoaVO().getCodigo())) {
            throw new Exception("Pessoa não informada.");
        }
        if (movContaVO.getUsuarioVO() == null || UteisValidacao.emptyNumber(movContaVO.getUsuarioVO().getCodigo())) {
            throw new Exception("Usuário não informado.");
        }
        if (movContaVO.getEmpresaVO() == null || UteisValidacao.emptyNumber(movContaVO.getEmpresaVO().getCodigo())) {
            throw new Exception("Empresa não informada.");
        }
        if (UteisValidacao.emptyString(movContaVO.getDescricao())) {
            throw new Exception("Descrição não informada.");
        }
        if (UteisValidacao.emptyNumber(movContaVO.getValor())) {
            throw new Exception("Valor não informado.");
        }

        if (movContaVO.getTipoOperacaoLancamento() == null) {
            throw new Exception("tipoOperacaoLancamentoEnum não informado.");
        } else if (!TipoOperacaoLancamento.getTipoOperacaoLancamento(movContaVO.getTipoOperacaoLancamento().getCodigo()).equals(TipoOperacaoLancamento.PAGAMENTO) &&
                !TipoOperacaoLancamento.getTipoOperacaoLancamento(movContaVO.getTipoOperacaoLancamento().getCodigo()).equals(TipoOperacaoLancamento.RECEBIMENTO)) {
            throw new Exception("No momento só é aceito PAGAMENTO ou RECEBIMENTO no tipoOperacaoLancamentoEnum.");
        }

        if (movContaVO.getDataLancamento() == null) { //se não informar vai usar a atual mesmo, mas se informar, precisa validar o formato
            throw new Exception("Data de Lançamento não informado ou formato inválido. Formato esperado é dd/MM/yyyy HH:mm:ss.");
        }

        if (movContaVO.getDataVencimento() == null) { //se não informar vai usar a atual mesmo, mas se informar, precisa validar o formato
            throw new Exception("Data de Vencimento não informado ou formato inválido. Formato esperado é dd/MM/yyyy HH:mm:ss.");
        }

        if (movContaVO.getDataCompetencia() == null) { //se não informar vai usar a atual mesmo, mas se informar, precisa validar o formato
            throw new Exception("Data de Competência não informada ou formato inválido. Formato esperado é dd/MM/yyyy HH:mm:ss.");
        }
    }

    private void validarData(String data, String label) throws Exception {
        try {
            Calendario.getDate("dd/MM/yyyy HH:mm:ss", data);
        } catch (Exception ex) {
            throw new Exception("Data informada é inválida: " + label);
        }
    }

    private String obterBody(HttpServletRequest request) throws Exception {
        InputStream inputStream = request.getInputStream();
        BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream, "UTF-8"));

        StringBuffer body = new StringBuffer();
        String line = null;

        while ((line = reader.readLine()) != null) {
            body.append(line);
        }
        return body.toString();
    }

    private void preencherRecebiveis(MovPagamentoIntegracaoTO item, List<RecebiveisResumoDTO> listaRetorno, String documento) throws Exception {
        RecebiveisResumoDTO dto = new RecebiveisResumoDTO(item);

        if (UteisValidacao.emptyString(documento) || documento.equals(dto.getDocumento())) {
            List<ProdutoRatear> listaProdutoRatearDoItem = this.threadDemonstrativoFinanceiro.pesquisarProdutosDoPagamento(item.getCodigo(), item.getProdutosPagos(), con, null);
            dto.setMovto(getCodigoPlanoContasRecebiveis(listaProdutoRatearDoItem));
            listaRetorno.add(dto);
        }
    }

    private MovPagamentoIntegracaoTO getItemComposicao(Map<Integer, MovPagamentoIntegracaoTO> mapComposicoes, Integer codigo, String composicao, MovPagamentoIntegracaoTO item) throws Exception {
        if (mapComposicoes.containsKey(codigo)) {
            return mapComposicoes.get(codigo);
        }

        MovPagamentoIntegracaoTO itemComComposicao = null;

        if (UteisValidacao.emptyString(composicao)) {
            String[] composicoes = composicao.split(",");
            for (int x = 0; x < composicoes.length; x++) {
                try {
                    itemComComposicao = mapComposicoes.get(Integer.parseInt(composicoes[x].trim()));
                    if (itemComComposicao != null) {
                        adicionarProdutosPagosDasComposicoes(itemComComposicao, item);
                    }
                } catch (Exception ex) {
                    Uteis.logar(ex, ModuloFinanceiroServlet.class);
                }
            }

            mapComposicoes.put(codigo, item);
        }

        return itemComComposicao;
    }

    private void adicionarProdutosPagosDasComposicoes(MovPagamentoIntegracaoTO itemComComposicao, MovPagamentoIntegracaoTO itemDuplicado) {
        if (!UteisValidacao.emptyString(itemDuplicado.getProdutosPagos())) {
            String produtosPagos = itemComComposicao.getProdutosPagos() != null ? itemComComposicao.getProdutosPagos() : "";

            if (!produtosPagos.contains(itemDuplicado.getProdutosPagos())) {
                produtosPagos += itemDuplicado.getProdutosPagos();
            }

            itemComComposicao.setProdutosPagos(produtosPagos);
            itemDuplicado.setProdutosPagos(produtosPagos);
        }
    }

    private JSONObject toJSON(boolean sucesso, Object dados) {
        JSONObject json = new JSONObject();
        json.put("sucesso", sucesso);
        json.put("dados", dados);
        return json;
    }

    private Integer getCodigoPlanoContasRecebiveis(List<ProdutoRatear> listaProdutoRatearDoItem) throws Exception {
        try {
            List<RateioIntegracaoTO> listaRateio = new ArrayList<>();
            for (ProdutoRatear produtoRatear : listaProdutoRatearDoItem) {
                //dto.setMulta(produtoRatear.getMultaJuros());
                if (produtoRatear.getTipoProduto().equals("PM")) {
                    // Realizar rateio para venda de Plano
                    realizarRateioPlanoRecebiveis(produtoRatear, listaRateio);
                } else if ((produtoRatear.getTipoProduto().equals("DI"))
                        || (produtoRatear.getTipoProduto().equals("AA"))) {
                    // Realizar rateio para venda de Diária e Aula Avulsa
                    realizarRateioAulaAvulsaEDiariaRecebiveis(produtoRatear, listaRateio);
                } else {
                    // Realizar rateio para venda de Produto/Serviço
                    realizarRateioVendaProdutoServicoRecebiveis(produtoRatear, listaRateio);
                }
            }

            Map<Integer, RateioIntegracaoTO> mapPlanoContas = new HashedMap();
            Integer ultimoCodigoPlanoContas = null;

            for (RateioIntegracaoTO item : listaRateio) {
                if (!UteisValidacao.emptyNumber(item.getCodigoPlanoContas()) && !mapPlanoContas.containsKey(item.getCodigoPlanoContas())) {
                    mapPlanoContas.put(item.getCodigoPlanoContas(), item);
                    if (UteisValidacao.emptyNumber(ultimoCodigoPlanoContas)) {
                        ultimoCodigoPlanoContas = item.getCodigoPlanoContas();
                    } else if (ultimoCodigoPlanoContas < item.getCodigoPlanoContas()) {
                        ultimoCodigoPlanoContas = item.getCodigoPlanoContas();
                    }
                }
            }

            return FacadeManager.getFacade().getPlanoConta().consultarCodigoLumi(ultimoCodigoPlanoContas);
        } catch (Exception e) {
            e.printStackTrace();
        }

        return 0;
    }

    private void realizarRateioPlanoRecebiveis(ProdutoRatear produtoRatear, List<RateioIntegracaoTO> rateios) throws Exception {
        List<ContratoModalidadePercentual> listaModalidadesPercentual = produtoRatear.getListaContratoModalidadePercentual();
        Double perc = 0.0;
        if (listaModalidadesPercentual != null) {
            for (ContratoModalidadePercentual obj : listaModalidadesPercentual) {
                perc += obj.getPercentagem();
            }
        }
        for (ContratoModalidadePercentual obj : listaModalidadesPercentual) {
            if (perc.equals(new Double(0.0))) {
                obj.setValor(Uteis.arredondarForcando2CasasDecimais(produtoRatear.getValorRatear() / listaModalidadesPercentual.size()));
            } else {
                Double valorperc = (new Double(obj.getPercentagem()).equals(new Double(0.0)) && perc.equals(new Double(0.0))) ? 0.0 : obj.getPercentagem() / perc;
                // Calcular o valor que cada modalidade representa em relação ao valor do Lancamento.
                obj.setValor(listaModalidadesPercentual.size() == 1 ? produtoRatear.getValorRatear() : valorperc * produtoRatear.getValorRatear());
            }
            obj.setNomeParcela(produtoRatear.getDescricaoParcela());
        }

        for (ContratoModalidadePercentual contratoModalidadePerc : listaModalidadesPercentual) {
            // Pesquisa os rateios definido para cada modalidade.
            List<RateioIntegracaoTO> listaRateiosModalidade = this.threadDemonstrativoFinanceiro.pesquisarRateios(this.listaRateiosIntegracao,
                    TipoCamposPesquisarRateio.MODALIDADE,
                    contratoModalidadePerc.getCodigoModalidade(),
                    TipoVisualizacaoRelatorioDF.PLANOCONTA.PLANOCONTA, empresaVO.getCodigo());
            if (listaRateiosModalidade.isEmpty()) {
                listaRateiosModalidade = this.threadDemonstrativoFinanceiro.pesquisarRateios(this.listaRateiosIntegracao,
                        TipoCamposPesquisarRateio.GERAL_MODALIDADES,
                        contratoModalidadePerc.getCodigoModalidade(),
                        TipoVisualizacaoRelatorioDF.PLANOCONTA, empresaVO.getCodigo());
            }
            if (listaRateiosModalidade.size() > 0) {
                rateios.addAll(listaRateiosModalidade);
            }
        }
    }

    private void realizarRateioAulaAvulsaEDiariaRecebiveis(ProdutoRatear produtoRatear, List<RateioIntegracaoTO> rateios) throws Exception {
        // Pesquisar os rateios definidos para o Produto "Aula avulsa" ou "Diária".
        List<RateioIntegracaoTO> listaRateios = this.threadDemonstrativoFinanceiro.pesquisarRateios(this.listaRateiosIntegracao, TipoCamposPesquisarRateio.PRODUTO, produtoRatear.getCodigoProduto(), TipoVisualizacaoRelatorioDF.PLANOCONTA, empresaVO.getCodigo());

        if (listaRateios.size() <= 0) {
            // Não foi informado um rateio para o produto, desta forma, pesquisar os rateios da categoria do produto.
            listaRateios = this.threadDemonstrativoFinanceiro.pesquisarRateios(this.listaRateiosIntegracao, TipoCamposPesquisarRateio.CATEGORIAPRODUTO, produtoRatear.getCodigoCategoriaProduto(), TipoVisualizacaoRelatorioDF.PLANOCONTA, empresaVO.getCodigo());
        }

        if (listaRateios.size() > 0) {
            rateios.addAll(listaRateios);
        }
    }

    private void realizarRateioVendaProdutoServicoRecebiveis(ProdutoRatear produtoRatear, List<RateioIntegracaoTO> rateios) throws Exception {
        // Pesquisar rateios definido para o produto.
        List<RateioIntegracaoTO> listaRateios = this.threadDemonstrativoFinanceiro.pesquisarRateios(this.listaRateiosIntegracao,
                TipoCamposPesquisarRateio.PRODUTO,
                produtoRatear.getCodigoProduto(),
                TipoVisualizacaoRelatorioDF.PLANOCONTA, empresaVO.getCodigo());
        if (listaRateios.size() <= 0) {
            // Pesquisar rateios definido para a categoria do produto.
            listaRateios = this.threadDemonstrativoFinanceiro.pesquisarRateios(this.listaRateiosIntegracao,
                    TipoCamposPesquisarRateio.CATEGORIAPRODUTO,
                    produtoRatear.getCodigoCategoriaProduto(),
                    TipoVisualizacaoRelatorioDF.PLANOCONTA, empresaVO.getCodigo());

        }

        if (listaRateios.size() > 0) {
            rateios.addAll(listaRateios);
        }
    }

}

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package servlet.financeiro;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import java.io.IOException;
import java.io.PrintWriter;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import negocio.facade.jdbc.utilitarias.Conexao;
import servicos.impl.dcc.base.RemessaService;

/**
 *
 * <AUTHOR>
 */
public class RemessaServlet extends HttpServlet {

    /**
     * Processes requests for both HTTP <code>GET</code> and <code>POST</code>
     * methods.
     *
     * @param request servlet request
     * @param response servlet response
     * @throws ServletException if a servlet-specific error occurs
     * @throws IOException if an I/O error occurs
     */
    protected void processRequest(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        response.setContentType("text/html;charset=UTF-8");
        PrintWriter out = response.getWriter();
        try {
            /* TODO output your page here. You may use following sample code. */
            
            if(request.getParameter("op").equals("retornoBoletoRede")){
                Conexao.guardarConexaoForJ2SE(request.getParameter("chave"), new DAO().obterConexaoEspecifica(request.getParameter("chave")));
                RemessaService remessaService = new RemessaService();
                out.println(remessaService.processarRetornoBoletoServlet(
                        request.getParameter("nomeArquivo"),
                        request.getParameter("arquivo"),
                        request.getParameter("codigoItens"),
                        request.getParameter("identificador"),
                        request.getParameter("Usuario"), 
                        request.getParameter("empresaProcessada"),
                        request.getParameter("tipoConvenio"),
                        request.getParameter("bancoConvenio")));
                remessaService = null;
            }
        }catch (Exception e){
            
        } finally {
            out.close();
        }
    }

    // <editor-fold defaultstate="collapsed" desc="HttpServlet methods. Click on the + sign on the left to edit the code.">
    /**
     * Handles the HTTP <code>GET</code> method.
     *
     * @param request servlet request
     * @param response servlet response
     * @throws ServletException if a servlet-specific error occurs
     * @throws IOException if an I/O error occurs
     */
    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        processRequest(request, response);
    }

    /**
     * Handles the HTTP <code>POST</code> method.
     *
     * @param request servlet request
     * @param response servlet response
     * @throws ServletException if a servlet-specific error occurs
     * @throws IOException if an I/O error occurs
     */
    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        processRequest(request, response);
    }

    /**
     * Returns a short description of the servlet.
     *
     * @return a String containing servlet description
     */
    @Override
    public String getServletInfo() {
        return "Short description";
    }// </editor-fold>

}

package servlet.financeiro;

import controle.arquitetura.security.LoginControle;
import servlet.arquitetura.SuperServlet;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;

/**
 * Created with IntelliJ IDEA.
 * User: franciscoanjos
 * Date: 04/09/13
 * Time: 11:11
 * To change this template use File | Settings | File Templates.
 */

public class ConvenioCobrancaServlet extends SuperServlet {
    @Override
    protected void processRequest(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        super.processRequest(request, response);
        PrintWriter out = response.getWriter();
        String json;
        try {
            LoginControle loginControle = ((LoginControle)getAttribute(request, "LoginControle"));
            Integer empresa = loginControle.getEmpresa().getCodigo();
            if (loginControle.permissao("ConsultarInfoTodasEmpresas")) {
                empresa = 0;
            }
            String situacao = obterParametroString(request.getParameter("situacao"));
            String sEcho = obterParametroString(request.getParameter("sEcho"));
            String colunaOrdenar = obterParametroString(request.getParameter("ordenar"));
            String sentidoOrdenar = obterParametroString(request.getParameter("sentidoOrdenacao"));
            Integer tipoCobrancaEnum = obterParametro(request.getParameter("tipo"));
            String clausulaLike = obterParametroString(request.getParameter("sSearch"));
            json = ff(request).getConvenioCobranca().consultarJSON(empresa,situacao, sEcho, tipoCobrancaEnum, clausulaLike, colunaOrdenar, sentidoOrdenar);

        } catch (Exception e) {
            json = (e.getMessage() == null) ? NullPointerException.class.getName() : e.getMessage();
        }
        out.println(json);
        json = null;
    }
}

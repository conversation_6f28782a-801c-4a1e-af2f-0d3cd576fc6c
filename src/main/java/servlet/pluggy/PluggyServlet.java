package servlet.pluggy;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.arquitetura.EnvelopeRespostaDTO;
import negocio.comuns.financeiro.PluggyConnectorDTO;
import negocio.comuns.financeiro.PluggyItemVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import org.json.JSONObject;
import servicos.impl.pluggy.PluggyService;
import servlet.arquitetura.SuperServlet;

import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.sql.Connection;
import java.util.ArrayList;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 * User: Rodrigo Estulano
 * Date: 12/07/2023
 */

public class PluggyServlet extends SuperServlet {

    @Override
    protected void processRequest(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        try {
            response.setHeader("Access-Control-Allow-Origin", "*");
            response.setHeader("Access-Control-Allow-Methods", "GET,POST");
            response.setHeader("Access-Control-Allow-Headers", "*");
            response.setContentType("application/json");

            String operacao = request.getParameter("op");
            if (UteisValidacao.emptyString(operacao)) {
                throw new Exception("operacao 'op' não informada na url.");
            }

            String key = request.getParameter("key");
            if (UteisValidacao.emptyString(key)) {
                throw new Exception("Chave não informada na url.");
            }

            EnvelopeRespostaDTO envelopeRespostaDTO;
            if (operacao.equalsIgnoreCase("validarQtdLmtContasConectadas")) {
               envelopeRespostaDTO = validarQtdLmtContasConectadas(request);
            }else if (operacao.equalsIgnoreCase("obterTokenPluggyConnect")) {
                envelopeRespostaDTO = createConnectToken(request);
            } else if (operacao.equalsIgnoreCase("obterTokenToUpdatePluggyConnect")) {
                envelopeRespostaDTO = createConnectTokenUpdatingItem(request);
            } else if (operacao.equalsIgnoreCase("salvarNovaContaConectada")) {
                envelopeRespostaDTO = salvarNovaContaConectada(request);
            } else if (operacao.equalsIgnoreCase("updateContaConectada")) {
                envelopeRespostaDTO = updateContaConectada(request);
            }else if (operacao.equalsIgnoreCase("obterContasConectadas")) {
                envelopeRespostaDTO = obterContasConectadas(request);
            } else if (operacao.equalsIgnoreCase("inativarContaConectada")) {
                envelopeRespostaDTO = inativarItem(request);
            } else {
                throw new Exception("Nenhuma operação executada");
            }
            response.setStatus(HttpServletResponse.SC_OK);
            response.getWriter().append(new JSONObject(envelopeRespostaDTO).toString());
        } catch (Exception ex) {
            ex.printStackTrace();
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            response.getWriter().append(new JSONObject(EnvelopeRespostaDTO.erro("Erro", ex.getMessage())).toString());
        }
    }

    private EnvelopeRespostaDTO createConnectToken(ServletRequest request) throws Exception {
        Connection con;
        PluggyService service = null;
        try {
            con = obterConexao(request);
            service = new PluggyService(con);

            //primeiro gerar apiKey
            String apiKey = service.createAPIKey();

            //agora gerar connect token usando apikey
            return EnvelopeRespostaDTO.of(service.createConnectToken(apiKey));

        } catch (Exception ex) {
            throw new Exception("Não foi possível obter o token: " + ex.getMessage());
        } finally {
            con = null;
            service = null;
        }
    }

    private EnvelopeRespostaDTO createConnectTokenUpdatingItem(ServletRequest request) throws Exception {
        Connection con;
        PluggyService service = null;
        try {

            String itemID = request.getParameter("idItem");
            if (UteisValidacao.emptyString(itemID)) {
                throw new Exception("idItem não informado para atualizar.");
            }

            con = obterConexao(request);
            service = new PluggyService(con);

            //primeiro gerar apiKey
            String apiKey = service.createAPIKey();

            //agora gerar connect token usando apikey
            return EnvelopeRespostaDTO.of(service.prepareUpdateConnectToken(apiKey, itemID));

        } catch (Exception ex) {
            throw new Exception("Não foi possível atualizar o conector: " + ex.getMessage());
        } finally {
            con = null;
            service = null;
        }
    }

    private EnvelopeRespostaDTO salvarNovaContaConectada(ServletRequest request) throws Exception {
        Connection con = null;
        PluggyService service = null;
        try {

            String empresa = request.getParameter("empresa");
            if (UteisValidacao.emptyString(empresa)) {
                throw new Exception("Código da empresa não informado.");
            }

            String dadosRetorno = obterBody(request);
            con = obterConexao(request);
            service = new PluggyService(con);

            String resp = service.incluirItem(con, Integer.parseInt(empresa), dadosRetorno);

            return EnvelopeRespostaDTO.of(resp);

        } catch (Exception ex) {
            throw new Exception("Erro: " + ex.getMessage());
        } finally {
            con = null;
            service = null;
        }
    }

    private EnvelopeRespostaDTO updateContaConectada(ServletRequest request) throws Exception {
        Connection con = null;
        PluggyService service = null;
        try {

            String empresa = request.getParameter("empresa");
            if (UteisValidacao.emptyString(empresa)) {
                throw new Exception("Código da empresa não informado.");
            }

            String dadosRetorno = obterBody(request);
            con = obterConexao(request);
            service = new PluggyService(con);

            String resp = service.updateItem(con, Integer.parseInt(empresa), dadosRetorno);

            return EnvelopeRespostaDTO.of(resp);

        } catch (Exception ex) {
            throw new Exception("Erro: " + ex.getMessage());
        } finally {
            con = null;
            service = null;
        }
    }

    private EnvelopeRespostaDTO obterContasConectadas(ServletRequest request) throws Exception {
        Connection con = null;
        PluggyService service = null;
        try {

            String empresa = request.getParameter("empresa");
            if (UteisValidacao.emptyString(empresa)) {
                throw new Exception("Código da empresa não informado.");
            }

            con = obterConexao(request);
            service = new PluggyService(con);

            List<PluggyItemVO> lista = service.consultarPluggyItem(con, Integer.parseInt(empresa));
            List<PluggyConnectorDTO> listaConnector = new ArrayList<>();

            if (!UteisValidacao.emptyList(lista)) {
                String apiKey = service.createAPIKey(); //primeiro gerar apiKey
                for (PluggyItemVO item : lista) {
                    try {
                        listaConnector.add(service.obterInfoItem(item.getId(), apiKey, item.getDadosRetorno()));
                    } catch (Exception ex) {
                        //se der erro ao obter infoItem, inativa o item que por erro do lado de lá ficam sem o infoitem, Então precisa cancelar do nosso lado pois o conector fica inacessível e precisa criar um novo neste caso.
                        if (ex.getMessage().contains("JSONObject[\"connector\"] not found")) {
                            service.inativarItemSomenteNoBanco(con, item.getId());
                        }
                    }
                }
            }

            return EnvelopeRespostaDTO.of(listaConnector);

        } catch (Exception ex) {
            throw new Exception("Não foi possível obter o token: " + ex.getMessage());
        } finally {
            con = null;
            service = null;
        }
    }

    private EnvelopeRespostaDTO inativarItem(ServletRequest request) throws Exception {
        Connection con = null;
        PluggyService service = null;
        try {

            String idItem = request.getParameter("idItem");
            if (UteisValidacao.emptyString(idItem)) {
                throw new Exception("idItem não informado para inativação.");
            }

            con = obterConexao(request);
            service = new PluggyService(con);

            service.inativarItem(con, idItem);

            return EnvelopeRespostaDTO.of("sucesso");

        } catch (Exception ex) {
            throw new Exception("Não foi possível inativar o item: " + ex.getMessage());
        } finally {
            con = null;
            service = null;
        }
    }

    private EnvelopeRespostaDTO validarQtdLmtContasConectadas(ServletRequest request) throws Exception {
        Connection con = null;
        PluggyService service = null;
        try {

            String empresa = request.getParameter("empresa");
            if(UteisValidacao.emptyString(empresa)) {
                throw new Exception("Código da empresa não foi informado.");
            }

            con = obterConexao(request);
            service = new PluggyService(con);

            boolean qtdLimiteAtingida = service.validarQtdLimiteContasConectadas(con, Integer.parseInt(empresa));

            return EnvelopeRespostaDTO.of(qtdLimiteAtingida);

        } catch (Exception e) {
            throw new Exception("Não foi possível validar a quantidade limite de contas: " + e.getMessage());
        }finally {
            con = null;
            service = null;
        }
    }

    private Connection obterConexao(ServletRequest request) throws Exception {
        String key = request.getParameter("key");
        return new DAO().obterConexaoEspecifica(key.trim());
    }

    private String obterBody(ServletRequest request) throws Exception {
        StringBuffer body = new StringBuffer();
        try {
            InputStream inputStream = request.getInputStream();
            BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream, StandardCharsets.UTF_8));
            String line = null;

            while ((line = reader.readLine()) != null) {
                body.append(line);
            }
            return body.toString();
        } catch (Exception ex) {
            ex.printStackTrace();
            throw new Exception("Erro body: " + ex.getMessage());
        }
    }

}

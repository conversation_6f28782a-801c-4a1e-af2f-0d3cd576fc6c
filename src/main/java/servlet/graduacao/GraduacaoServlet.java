package servlet.graduacao;

import br.com.pactosolucoes.agendatotal.json.AgendadoJSON;
import br.com.pactosolucoes.graduacao.service.impl.GraduacaoServiceImpl;
import br.com.pactosolucoes.graduacao.service.interf.GraduacaoService;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import br.com.pactosolucoes.turmas.servico.impl.TurmasServiceImpl;
import br.com.pactosolucoes.turmas.servico.intf.TurmasServiceInterface;
import negocio.comuns.utilitarias.UteisValidacao;
import org.json.JSONArray;
import org.json.JSONObject;
import servicos.integracao.impl.join.JoinService;
import servlet.arquitetura.SuperServlet;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLDecoder;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public class GraduacaoServlet extends SuperServlet {

    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        processRequest(request, response);
    }

    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        processRequest(request, response);
    }


    protected void processRequest(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        response.addHeader("Access-Control-Allow-Origin", "*");
        response.addHeader("Access-Control-Allow-Methods", "GET");
        response.setContentType("application/json");
        String path = request.getServletPath();
        String[] pathParts = path.split("/");
        String recurso = pathParts[3];
        String chave = request.getParameter("chave");
        JSONObject json = new JSONObject();
        try {
            switch (recurso){
                case "aulas-aluno":
                    Integer matricula = Integer.valueOf(request.getParameter("matricula"));
                    String modalidade = request.getParameter("modalidade");
                    Long data = Long.valueOf(request.getParameter("data"));
                    json.put("aulas", getService(chave).aulasAluno(modalidade, matricula, data, null, null));
                    response.getWriter().append(json.toString());
                    break;
                case "filtros-alunos":
                    String modalidades = request.getParameter("modalidades");
                    json.put("filtros", getGraduacaoService(chave).filtrosAlunosGraduacao(null, modalidades));
                    response.getWriter().append(json.toString());
                    break;
                case "consulta-alunos-todos":
                    String body = request.getReader().lines().collect(Collectors.joining(System.lineSeparator()));
                    JSONArray codigos = new JSONArray(body);
                    Boolean paraAvaliacao = Boolean.valueOf(request.getParameter("paraAvaliacao"));
                    String quicksearchValue = request.getParameter("quicksearchValue");
                    JSONObject filtros = new JSONObject() {{
                        put("paraAvaliacao", paraAvaliacao);
                        put("quicksearchValue", quicksearchValue);
                        put("codigos", codigos);
                    }};
                    try {
                        String filters = request.getParameter("filters");
                        filtros.put("filtros", new JSONObject(URLDecoder.decode(filters, "UTF-8")));
                    }catch(Exception e){
                        e.printStackTrace();
                    }
                    json.put("alunos", getGraduacaoService(chave).alunosGraduacao(null, filtros, true));
                    response.getWriter().append(json.toString());
                    break;
                case "consulta-alunos":
                    alunos(request, response, chave, json);
                    break;
                case "consultar-informacoes-aluno":
                    json.put("frequencias", getGraduacaoService(chave).consultarInformacoesAluno(Integer.valueOf(request.getParameter("matricula"))));
                    response.getWriter().append(json.toString());
                    break;
                case "consultar-horarios-dias-aluno-por-periodo":
                    json.put("content",
                            getGraduacaoService(chave).consultarHorariosEDiasAlunoPorPeriodo(
                                    Integer.valueOf(request.getParameter("matricula")),
                                    Long.valueOf(request.getParameter("data")))
                    );
                    response.getWriter().append(json.toString());
                    break;
            }
        } catch (Exception e ) {
            System.out.println("Erro na api rest "+recurso+". Message: "+e.getMessage());
            processarErro(e, request, response, null);
        }

    }

    private void alunos(HttpServletRequest request, HttpServletResponse response, String chave, JSONObject json) throws Exception {
        String body = request.getReader().lines().collect(Collectors.joining(System.lineSeparator()));
        JSONArray codigos = new JSONArray(body);
        Integer limit = Integer.valueOf(request.getParameter("limit"));
        Integer page = Integer.valueOf(request.getParameter("page"));
        Boolean paraAvaliacao = Boolean.valueOf(request.getParameter("paraAvaliacao"));
        String modalidades = request.getParameter("modalidades");
        String sort = request.getParameter("sort");
        String quicksearchValue = request.getParameter("quicksearchValue");
        JSONObject filtros = new JSONObject() {{
            put("limit", limit);
            put("quicksearchValue", quicksearchValue);
            put("paraAvaliacao", paraAvaliacao);
            put("sort", sort);
            put("codigos", codigos);
            put("page", page);
            put("modalidades", modalidades);
        }};
        try {
            String filters = request.getParameter("filters");
            filtros.put("filtros", new JSONObject(URLDecoder.decode(filters, "UTF-8")));
        }catch(Exception e){
            e.printStackTrace();
        }
        json.put("total", getGraduacaoService(chave).totalAlunosGraduacao(null, filtros));
        json.put("alunos", getGraduacaoService(chave).alunosGraduacao(null, filtros, false));
        response.getWriter().append(json.toString());
    }

    private TurmasServiceInterface getService(String chave) throws Exception {
        return new TurmasServiceImpl(new DAO().obterConexaoEspecifica(chave));
    }

    private GraduacaoService getGraduacaoService(String chave) throws Exception {
        return new GraduacaoServiceImpl(new DAO().obterConexaoEspecifica(chave));
    }

}

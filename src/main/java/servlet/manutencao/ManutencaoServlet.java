package servlet.manutencao;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import org.json.JSONArray;
import org.json.JSONObject;
import servlet.arquitetura.SuperServlet;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;


public class ManutencaoServlet extends SuperServlet {

    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        processRequest(request, response);
    }

    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        processRequest(request, response);
    }

    protected void processRequest(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {

        response.addHeader("Access-Control-Allow-Origin", "*");
        response.addHeader("Access-Control-Allow-Methods", "GET");
        response.setContentType("application/json");

        String path = request.getServletPath();
        String[] pathParts = path.split("/");
        String recurso = pathParts[3];

        String chave = request.getParameter("chave");
        String matricula = request.getParameter("matricula");

        try {
            switch (recurso){
                case "corrigir-nome-usuario-pacto":
                    String retorno = corrigirNomeUsuarioPacto(chave);
                    response.getWriter().append(new JSONObject().put("content", retorno).toString());
                    break;
                case"obter-dados-alunos-para-sincronizar":
                    JSONArray retornoSintetico = obterDadosAlunosParaSincronizar(chave);
                    JSONObject clienteSintetico = new JSONObject().put("content", retornoSintetico);
                    response.getWriter().append(clienteSintetico.toString());
                    break;
                case"set-aluno-parq-false":
                    String retornoSetParqFalse = setAlunoParqFalse(chave, matricula);
                    response.getWriter().append(new JSONObject().put("content", retornoSetParqFalse).toString());
                    break;
                case "corrigir-datas-matriculaalunohorarioturma":
                    String retornoCorrigirMatricula = corrigirDatasMatriculaalunohorarioturma(chave, request.getParameter("codMAHT"), request.getParameter("dataInicio"), request.getParameter("dataFim"));
                    response.getWriter().append(new JSONObject().put("content", retornoCorrigirMatricula).toString());
                    break;
            }
        } catch (Exception e ) {
            System.out.println("Erro na api rest "+recurso+". Message: "+e.getMessage());
            processarErro(e, request, response, null);
        }

    }

    private String corrigirNomeUsuarioPacto(String ctx) throws Exception {
        Connection con = new DAO().obterConexaoEspecifica(ctx);

        String retorno = "";

        StringBuilder sql = new StringBuilder();
        sql.append("SELECT codigo FROM usuario u WHERE UPPER(username) LIKE UPPER('pactobr')");
        ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), con);
        if (rs.next()) {
            Integer codigoUsuario = rs.getInt("codigo");

            StringBuilder update = new StringBuilder();
            update.append("UPDATE usuario SET nome = 'PACTO - MÉTODO DE GESTÃO' WHERE codigo = ").append(codigoUsuario);
            try (PreparedStatement pst = con.prepareStatement(update.toString())) {
                pst.execute();
                retorno = "| Banco ZW: Nome de usuario atualizado com sucesso ";
            }
        }

        sql = new StringBuilder();
        sql.append("SELECT p.codigo \n");
        sql.append("FROM pessoa p \n");
        sql.append("INNER JOIN colaborador c ON c.pessoa = p.codigo  \n");
        sql.append("INNER JOIN usuario u ON u.colaborador = c.codigo  \n");
        sql.append("WHERE UPPER(u.username) LIKE UPPER('pactobr') \n");
        rs = SuperFacadeJDBC.criarConsulta(sql.toString(), con);
        if (rs.next()) {
            Integer codigoPessoa = rs.getInt("codigo");

            StringBuilder update = new StringBuilder();
            update.append("UPDATE pessoa SET nome = 'PACTO - MÉTODO DE GESTÃO' WHERE codigo = ").append(codigoPessoa);
            try (PreparedStatement pst = con.prepareStatement(update.toString())) {
                pst.execute();
                retorno += "| Banco ZW: Nome de pessoa atualizado com sucesso ";
            }
        }

        return retorno;
    }

    private JSONArray obterDadosAlunosParaSincronizar(String ctx) throws Exception {

        Connection con = new DAO().obterConexaoEspecifica(ctx);
        JSONArray array = new JSONArray();

        StringBuilder sql = new StringBuilder();
        sql.append("select matricula, nomecliente, nomeplano, p.sexo\n" +
                "from situacaoclientesinteticodw s \n" +
                "inner join pessoa p on p.codigo = s.codigopessoa \n" +
                "where p.sexo is not null\n" +
                "order by nomeplano desc");
        ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), con);
        while (rs.next()) {
                JSONObject json = new JSONObject();
                json.put("matricula", rs.getInt("matricula"));
                json.put("nomecliente", rs.getString("nomecliente"));
                json.put("nomeplano", rs.getString("nomeplano"));
                json.put("sexo", rs.getString("sexo"));
                array.put(json);
            }
        return array;
    }

    private String setAlunoParqFalse(String chave, String matricula) throws Exception {
        Integer mat = Integer.parseInt(matricula);
        Connection con = new DAO().obterConexaoEspecifica(chave);

        StringBuilder update = new StringBuilder();
        update.append("update cliente set parqpositivo = false where codigomatricula = ").append(mat);
        try (PreparedStatement ps = con.prepareStatement(update.toString())) {
            ps.execute();
            return "ok";
        }
    }

    private String corrigirDatasMatriculaalunohorarioturma(String chave, String codigo, String dataInicio, String dataFim) throws Exception {
        Integer codMAHT = Integer.parseInt(codigo);
        if (UteisValidacao.emptyString(chave) || UteisValidacao.emptyString(dataInicio) || UteisValidacao.emptyString(dataFim) || UteisValidacao.emptyNumber(codMAHT)) {
            return "Um dos valores necessários para o ajuste não foi informado corretamente e devido a isso não pode prosseguir com o ajuste.";
        }
        Connection con = new DAO().obterConexaoEspecifica(chave);
        String update =
                "update matriculaalunohorarioturma \n" +
                "set datainicio = '" + dataInicio + "', \n" +
                "datafim = '" + dataFim + "'\n" +
                "where codigo = " + codMAHT;
        try (PreparedStatement ps = con.prepareStatement(update)) {
            ps.execute();
            if (!ps.isClosed()) {
                ps.close();
            }
            return "ok";
        }
    }

}

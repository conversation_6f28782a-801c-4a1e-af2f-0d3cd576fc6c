package servlet.kobana;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.arquitetura.EnvelopeRespostaDTO;
import negocio.comuns.financeiro.enumerador.AmbienteEnum;
import negocio.comuns.utilitarias.UteisValidacao;
import org.json.JSONObject;
import servicos.impl.kobana.IntegracaoKobanaService;
import servlet.arquitetura.SuperServlet;

import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.sql.Connection;

/**
 * Created with IntelliJ IDEA.
 * User: <PERSON>
 * Date: 21/06/2024
 */

public class IntegracaoKobanaServlet extends SuperServlet {

    @Override
    protected void processRequest(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        try {
            response.setHeader("Access-Control-Allow-Origin", "*");
            response.setHeader("Access-Control-Allow-Methods", "GET,POST");
            response.setHeader("Access-Control-Allow-Headers", "*");
            response.setContentType("application/json");

            String operacao = request.getParameter("op");
            if (UteisValidacao.emptyString(operacao)) {
                throw new Exception("operacao 'op' não informada na url.");
            }

            EnvelopeRespostaDTO envelopeRespostaDTO;
            if (operacao.equalsIgnoreCase("criarConta")) {
                envelopeRespostaDTO = criarConta(request);
            } else if (operacao.equalsIgnoreCase("inativarConta")) {
                envelopeRespostaDTO = inativarConta(request);
            } else if (operacao.equalsIgnoreCase("reativarConta")) {
                envelopeRespostaDTO = reativarConta(request);
            } else if (operacao.equalsIgnoreCase("obterDados")) {
                envelopeRespostaDTO = obterDados(request);
            } else {
                throw new Exception("Nenhuma operação executada");
            }
            response.setStatus(HttpServletResponse.SC_OK);
            response.getWriter().append(new JSONObject(envelopeRespostaDTO).toString());
        } catch (Exception ex) {
            ex.printStackTrace();
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            response.getWriter().append(new JSONObject(EnvelopeRespostaDTO.erro("Erro", ex.getMessage())).toString());
        }
    }

    private EnvelopeRespostaDTO criarConta(ServletRequest request) throws Exception {
        Connection con = null;
        IntegracaoKobanaService service = null;
        try {

            String empresa = request.getParameter("empresa");
            if (UteisValidacao.emptyString(empresa)) {
                throw new Exception("Código da empresa não informado.");
            }
            if (UteisValidacao.emptyString(request.getParameter("ambiente"))) {
                throw new Exception("Ambiente não informado.");
            }
            AmbienteEnum ambiente = AmbienteEnum.consultarPorCodigo(Integer.valueOf(request.getParameter("ambiente")));

            String body = obterBody(request);
            con = obterConexao(request);
            service = new IntegracaoKobanaService(con, ambiente);

            String resp = service.criarSubconta(Integer.valueOf(empresa), body);

            return EnvelopeRespostaDTO.of(resp);

        } catch (Exception ex) {
            throw new Exception("Erro: " + ex.getMessage());
        } finally {
            con = null;
            service = null;
        }
    }

    private EnvelopeRespostaDTO inativarConta(ServletRequest request) throws Exception {
        Connection con = null;
        IntegracaoKobanaService service = null;
        try {

            String empresa = request.getParameter("empresa");
            if (UteisValidacao.emptyString(request.getParameter("ambiente"))) {
                throw new Exception("Ambiente não informado.");
            }
            AmbienteEnum ambiente = AmbienteEnum.consultarPorCodigo(Integer.valueOf(request.getParameter("ambiente")));
            con = obterConexao(request);
            service = new IntegracaoKobanaService(con, ambiente);

            service.inativarConta(con, Integer.valueOf(empresa));

            return EnvelopeRespostaDTO.of("sucesso");

        } catch (Exception ex) {
            throw new Exception("Não foi possível inativar o item: " + ex.getMessage());
        } finally {
            con = null;
            service = null;
        }
    }

    private EnvelopeRespostaDTO reativarConta(ServletRequest request) throws Exception {
        Connection con = null;
        IntegracaoKobanaService service = null;
        try {

            String empresa = request.getParameter("empresa");
            if (UteisValidacao.emptyString(request.getParameter("ambiente"))) {
                throw new Exception("Ambiente não informado.");
            }
            AmbienteEnum ambiente = AmbienteEnum.consultarPorCodigo(Integer.valueOf(request.getParameter("ambiente")));
            con = obterConexao(request);
            service = new IntegracaoKobanaService(con, ambiente);

            service.reativarConta(con, Integer.valueOf(empresa));

            return EnvelopeRespostaDTO.of("sucesso");

        } catch (Exception ex) {
            throw new Exception("Não foi possível inativar o item: " + ex.getMessage());
        } finally {
            con = null;
            service = null;
        }
    }

    private EnvelopeRespostaDTO obterDados(ServletRequest request) throws Exception {
        Connection con = null;
        IntegracaoKobanaService service = null;
        try {

            String empresa = request.getParameter("empresa");
            if (UteisValidacao.emptyString(empresa)) {
                throw new Exception("Código da empresa não informado.");
            }
            if (UteisValidacao.emptyString(request.getParameter("ambiente"))) {
                throw new Exception("Ambiente não informado.");
            }
            AmbienteEnum ambiente = AmbienteEnum.consultarPorCodigo(Integer.valueOf(request.getParameter("ambiente")));
            con = obterConexao(request);
            service = new IntegracaoKobanaService(con, ambiente);

            String resposta = service.consultarDados(con, Integer.parseInt(empresa));

            return EnvelopeRespostaDTO.of(resposta);

        } catch (Exception ex) {
            throw new Exception("Não foi possível obter as informações: " + ex.getMessage());
        } finally {
            con = null;
            service = null;
        }
    }

    private Connection obterConexao(ServletRequest request) throws Exception {
        String key = request.getParameter("key");
        return new DAO().obterConexaoEspecifica(key.trim());
    }

    private String obterBody(ServletRequest request) throws Exception {
        StringBuffer body = new StringBuffer();
        try {
            InputStream inputStream = request.getInputStream();
            BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream, StandardCharsets.UTF_8));
            String line = null;

            while ((line = reader.readLine()) != null) {
                body.append(line);
            }
            return body.toString();
        } catch (Exception ex) {
            ex.printStackTrace();
            throw new Exception("Erro body: " + ex.getMessage());
        }
    }

}

package servlet.kobana;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.utilitarias.Conexao;
import org.json.JSONObject;
import servicos.impl.kobana.KobanaWebhookService;
import servlet.arquitetura.SuperServlet;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.sql.Connection;


/**
 * Created with IntelliJ IDEA.
 * User: <PERSON>
 * Date: 08/11/2024
 */

public class KobanaWebhookServlet extends SuperServlet {

    @Override
    protected void processRequest(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        Connection con = null;
        try {
            response.addHeader("Access-Control-Allow-Origin", "*");
            response.addHeader("Access-Control-Allow-Methods", "POST");
            response.setContentType("application/json");
            response.setCharacterEncoding("UTF-8");

            String method = request.getMethod();

            if (!method.equalsIgnoreCase("POST")) {
                throw new Exception("Método não suportado para esse recurso");
            }

            String chaveZW = request.getParameter("chaveZW");
            if (UteisValidacao.emptyString(chaveZW)) {
                throw new Exception("chaveZW não informada");
            }

            StringBuffer body = new StringBuffer();
            try {
                InputStream inputStream = request.getInputStream();
                BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream, "UTF-8"));

                String line = null;

                while ((line = reader.readLine()) != null) {
                    body.append(line);
                }
            } catch (Exception ex) {
                ex.printStackTrace();
                throw new Exception("Erro body: " + ex.getMessage());
            }

            con = obterConexao(chaveZW);
            proccessWebhook(con, chaveZW, body.toString());

            response.setStatus(HttpServletResponse.SC_OK);
            PrintWriter out = response.getWriter();
            out.print(this.toJSON(true, "ok"));
            out.flush();
        } catch (Exception ex) {
            response.setStatus(HttpServletResponse.SC_BAD_REQUEST);
            PrintWriter out = response.getWriter();
            out.print(this.toJSON(false, ex.getMessage()));
            out.flush();
        } finally {
            try {
                if (con != null) {
                    con.close();
                }
            } catch (Exception ex) {
                ex.printStackTrace();
            }
        }
    }

    public void proccessWebhook(Connection con, String chaveZW, String body) throws Exception {
        KobanaWebhookService service = null;

        //PROCESSAR WEBHOOK
        //criar conexão específica para o service
        try (Connection connection = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            service = new KobanaWebhookService(connection);
            service.processarWebhook(chaveZW, body);
        } catch (Exception ex) {
            //Sempre que der algum erro, lançar uma mensagem genérica, pra não ficar gravando informações de negócio nossa
            // lá no portal Kobana, pois lá tem a gestão de logs das tentativas de webhooks e grava todos os retornos.
            throw new Exception("Erro ao processar webhook. Pode tentar novamente.");
        }
    }

    private Connection obterConexao(String chaveZW) throws Exception {
        return new DAO().obterConexaoEspecifica(chaveZW.trim());
    }

    private JSONObject toJSON(boolean sucesso, Object obj) {
        JSONObject json = new JSONObject();
        json.put("sucesso", sucesso);
        json.put("dados", obj);
        return json;
    }
}

package servlet.appConsultor;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.arquitetura.HorarioAcessoSistemaVO;
import negocio.comuns.utilitarias.Uteis;
import org.json.JSONArray;
import org.json.JSONObject;
import servlet.arquitetura.SuperServlet;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import negocio.facade.jdbc.basico.HorarioAcessoSistema;

import java.io.IOException;
import java.util.List;


public class HorarioAcessoServlet extends SuperServlet {

    @Override
    protected void processRequest(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {

        response.addHeader("Access-Control-Allow-Methods", "GET");
        response.setContentType("application/json");

        String usuarioID = request.getParameter("usuario");
        String chave = request.getParameter("chave");
        int usuario = 0;
        if (usuarioID != null) {
            usuario = Integer.parseInt(usuarioID);
        }
        try {
            consultarHorariosUsuario(response,usuario,chave);
        } catch (Exception e) {
            processarErro(e, request, response, null);
        }
    }
    private void consultarHorariosUsuario(HttpServletResponse response,int usuario,String chave) throws Exception {
        HorarioAcessoSistema horarioAcessoSistema = new HorarioAcessoSistema(new DAO().obterConexaoEspecifica(chave));
        List<HorarioAcessoSistemaVO> horarioAcessoSistemaVOs = horarioAcessoSistema.consultar(usuario, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        JSONArray returnJson = new JSONArray();
        for (HorarioAcessoSistemaVO horarioVo : horarioAcessoSistemaVOs) {
            returnJson.put(this.toJSON(horarioVo));
        }

        response.getWriter().append(returnJson.toString());
    }
    private JSONObject toJSON(HorarioAcessoSistemaVO horario) {
        JSONObject horarioJSON = new JSONObject();
        if (horario != null) {
            horarioJSON.put("diaSemana", horario.getDiaSemana());
            horarioJSON.put("horaInicial", horario.getHoraInicial());
            horarioJSON.put("horaFinal", horario.getHoraFinal());
        }

        return horarioJSON;
    }
}

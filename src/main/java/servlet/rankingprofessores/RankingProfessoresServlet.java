package servlet.rankingprofessores;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import br.com.pactosolucoes.turmas.servico.impl.TurmasServiceImpl;
import br.com.pactosolucoes.turmas.servico.intf.TurmasServiceInterface;
import negocio.facade.jdbc.basico.Colaborador;
import org.json.JSONObject;
import servlet.arquitetura.SuperServlet;
import org.json.JSONArray;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

public class RankingProfessoresServlet extends SuperServlet {

    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        processRequest(request, response);
    }

    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        processRequest(request, response);
    }

    protected void processRequest(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        response.addHeader("Access-Control-Allow-Origin", "*");
        response.addHeader("Access-Control-Allow-Methods", "GET");
        response.setContentType("application/json");

        String path = request.getServletPath();
        String[] pathParts = path.split("/");

        Integer mes = request.getParameter("mes") == null ? null : Integer.valueOf(request.getParameter("mes"));
        Integer ano = request.getParameter("ano") == null ? null : Integer.valueOf(request.getParameter("ano"));
        Integer empresa = request.getParameter("empresa") == null ? null : Integer.valueOf(request.getParameter("empresa"));
        Integer professor = request.getParameter("professor") == null ? null : Integer.valueOf(request.getParameter("professor"));
        String chave = request.getParameter("chave");
        String recurso = pathParts[3];
        try {
            switch (recurso) {
                case "detalhes":
                    String colaborador = request.getParameter("colaborador");
                    Colaborador colaboradorDao = new Colaborador(new DAO().obterConexaoEspecifica(chave));
                    JSONObject jsonObject = colaboradorDao.detalhesProfessor(Integer.valueOf(colaborador));
                    response.getWriter().append(jsonObject.toString());
                    break;
                case "detalhes-professor":
                    String colaboradorProfessor = request.getParameter("colaborador");
                    Colaborador colaboradorProfessorDao = new Colaborador(new DAO().obterConexaoEspecifica(chave));
                    JSONObject jsonObjectProfessor = colaboradorProfessorDao.detalhesColaboradorProfessor(Integer.valueOf(colaboradorProfessor));
                    response.getWriter().append(jsonObjectProfessor.toString());
                    break;
                default:
                    JSONArray alunosCrossfitMes = getService(chave).alunosCancelados(empresa, mes, ano, professor);
                    response.getWriter().append(alunosCrossfitMes.toString());
            }
        }catch (Exception e ){
            System.out.println("Erro na api rest  Message: "+e.getMessage());
            processarErro(e, request, response, null);
        }
    }

    private TurmasServiceInterface getService(String chave) throws Exception {
        return new TurmasServiceImpl(new DAO().obterConexaoEspecifica(chave));
    }
}

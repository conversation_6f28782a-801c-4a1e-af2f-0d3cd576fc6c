package servlet.tokenOperacao;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.tokenOperacao.TokenOperacaoVO;
import org.json.JSONObject;
import servicos.impl.tokenOperacao.TokenOperacaoService;
import servlet.arquitetura.SuperServlet;

import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.sql.Connection;

/**
 * Created with IntelliJ IDEA.
 * User: <PERSON>
 * Date: 14/11/2023
 */

public class TokenOperacaoServlet extends SuperServlet {

    @Override
    protected void processRequest(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        try {
            response.setHeader("Access-Control-Allow-Origin", "*");
            response.setHeader("Access-Control-Allow-Methods", "GET,POST");
            response.setHeader("Access-Control-Allow-Headers", "*");
            response.setContentType("application/json");

            String operacao = request.getParameter("op");
            if (UteisValidacao.emptyString(operacao)) {
                throw new Exception("Parâmetro 'op' (operação) não informado.");
            }

            String key = request.getParameter("chave");
            if (UteisValidacao.emptyString(key)) {
                throw new Exception("Parâmetro 'chave' não informado.");
            }

            String usuario = request.getParameter("usuario");
            if (UteisValidacao.emptyString(usuario)) {
                throw new Exception("Parâmetro 'usuario' não informado.");
            }

            TokenOperacaoVO tokenGerado = null;
            if (operacao.equalsIgnoreCase("gerarTokenOperacao")) {
                tokenGerado = gerarToken(request);
            }

            if (tokenGerado == null) {
                throw new Exception("Não foi possível gerar o token");
            }

            response.setStatus(HttpServletResponse.SC_OK);
            JSONObject json = new JSONObject();
            json.put("token", tokenGerado.getToken());
            json.put("dataRegistro", tokenGerado.getData_Apresentar());
            json.put("usuario", tokenGerado.getUsuario());
            json.put("username", tokenGerado.getUsuario_Apresentar());
            response.getWriter().append(json.toString());
        } catch (Exception ex) {
            ex.printStackTrace();
            response.setStatus(HttpServletResponse.SC_FORBIDDEN);
            JSONObject json = new JSONObject();
            json.put("erro", ex.getMessage());
            response.getWriter().append(json.toString());
        }
    }

    private TokenOperacaoVO gerarToken(ServletRequest request) throws Exception {
        Connection con;
        TokenOperacaoService service = null;
        Usuario usuarioDAO;
        try {
            con = obterConexao(request);
            service = new TokenOperacaoService(con);
            usuarioDAO = new Usuario(con);

            int usuario = Integer.parseInt(request.getParameter("usuario"));

            //validar se o código do usuário existe antes de consultar ou gerar o token
            UsuarioVO usuarioVO = usuarioDAO.consultarPorCodigo(usuario, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);

            if (usuarioVO == null || UteisValidacao.emptyNumber(usuarioVO.getCodigo())) {
                throw new Exception("Usuário informado não existe para gerar Token!");
            }

            TokenOperacaoVO tokenGerado = service.consultarGerarToken(usuario);
            tokenGerado.setUsuarioVO(usuarioVO);

            return tokenGerado;

        } catch (Exception ex) {
            throw new Exception("Não foi possível gerar o token: " + ex.getMessage());
        } finally {
            con = null;
            service = null;
            usuarioDAO = null;
        }
    }

    private Connection obterConexao(ServletRequest request) throws Exception {
        String chave = request.getParameter("chave");
        return new DAO().obterConexaoEspecifica(chave.trim());
    }
}

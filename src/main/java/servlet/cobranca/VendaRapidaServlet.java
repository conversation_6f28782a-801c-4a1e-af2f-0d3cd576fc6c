package servlet.cobranca;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.basico.CidadeVO;
import negocio.comuns.basico.CidadeWS;
import negocio.comuns.basico.EstadoVO;
import negocio.comuns.basico.EstadoWS;
import negocio.comuns.basico.PaisVO;
import negocio.comuns.plano.PlanoEmpresaVO;
import negocio.comuns.plano.PlanoVO;
import negocio.comuns.plano.PlanoWS;
import negocio.comuns.utilitarias.CepVO;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.basico.Cidade;
import negocio.facade.jdbc.basico.Estado;
import negocio.facade.jdbc.basico.Pais;
import negocio.facade.jdbc.plano.Plano;
import negocio.facade.jdbc.utilitarias.Cep;
import org.json.JSONArray;
import org.json.JSONObject;
import servlet.arquitetura.SuperServlet;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.sql.Connection;
import java.util.ArrayList;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 * User: Luiz Felipe
 * Date: 25/08/2022
 */
public class VendaRapidaServlet extends SuperServlet {

    @Override
    protected void processRequest(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        Connection con = null;
        try {
            response.addHeader("Access-Control-Allow-Origin", "*");
            response.addHeader("Access-Control-Allow-Methods", "GET,POST");
            response.setContentType("application/json");

            String method = request.getMethod();

            if (!method.equalsIgnoreCase("POST")) {
                throw new Exception("Método não suportado.");
            }

            String key = request.getParameter("key");
            if (UteisValidacao.emptyString(key)) {
                throw new Exception("Chave não informada.");
            }

            JSONObject jsonBody = null;

            try {
                InputStream inputStream = request.getInputStream();
                BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream, "UTF-8"));

                StringBuffer body = new StringBuffer();
                String line = null;

                while ((line = reader.readLine()) != null) {
                    body.append(line);
                }

                jsonBody = new JSONObject(body.toString());
            } catch (Exception ex) {
                ex.printStackTrace();
                throw new Exception("Erro body: " + ex.getMessage());
            }

            con = new DAO().obterConexaoEspecifica(key);

            String operacao = jsonBody.optString("operacao");

            Object objRetorno = null;
            if (operacao.equalsIgnoreCase("consultarPlanos")) {
                objRetorno = consultarPlanos(jsonBody.optInt("empresa"), con);
            } else if (operacao.equalsIgnoreCase("consultarPaises")) {
                objRetorno = consultarPaises(con);
            } else if (operacao.equalsIgnoreCase("consultarEstados")) {
                objRetorno = consultarEstados(jsonBody.optInt("pais"), con);
            } else if (operacao.equalsIgnoreCase("consultarCidades")) {
                objRetorno = consultarCidades(jsonBody.optInt("estado"), con);
            } else if (operacao.equalsIgnoreCase("consultarCEP")) {
                objRetorno = consultarCEP(jsonBody.optString("cep"), con);
            } else {
                throw new Exception("Nenhuma operação executada");
            }

            if (objRetorno == null) {
                throw new Exception("Nenhuma operação executada");
            }

            response.getWriter().append(this.toJSON(true, objRetorno).toString());
        } catch (Exception ex) {
            response.setStatus(400);
            response.getWriter().append(this.toJSON(false, ex.getMessage()).toString());
        } finally {
            if (con != null) {
                try {
                    con.close();
                } catch (Exception ex) {
                }
            }
        }
    }

    private JSONObject toJSON(boolean sucesso, Object dados) {
        JSONObject json = new JSONObject();
        json.put("sucesso", sucesso);
        json.put("dados", dados);
        return json;
    }

    private Object consultarPlanos(Integer empresa, Connection con) throws Exception {
        Plano planoDAO = null;
        try {
            planoDAO = new Plano(con);

            List<PlanoVO> planos = planoDAO.consultarPlanosSimplesVendaRapida(empresa, Uteis.NIVELMONTARDADOS_DADOSENTIDADESUBORDINADAS);
            JSONArray planosJson = new JSONArray();
            for (PlanoVO plano : planos) {
                try {
                    PlanoWS planoWS = obterPlanoWSValidandoEmpresa(empresa, plano, con);
                    JSONObject planoJsonObj = new JSONObject(planoWS);
                    planosJson.put(planoJsonObj);
                } catch (Exception ex) {
                    ex.printStackTrace();
                }
            }
            return planosJson;
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        } finally {
            planoDAO = null;
        }
    }

    private Object consultarPaises(Connection con) throws Exception {
        Pais paisDAO = null;
        try {
            paisDAO = new Pais(con);

            List<PaisVO> lista = paisDAO.consultarTodos(Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            Ordenacao.ordenarLista(lista, "nome");
            JSONArray listaJSON = new JSONArray();
            for (PaisVO obj : lista) {
                JSONObject json = new JSONObject();
                json.put("codigo", obj.getCodigo());
                json.put("nome", obj.getNome());
                listaJSON.put(json);
            }
            return listaJSON;
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        } finally {
            paisDAO = null;
        }
    }

    private Object consultarEstados(Integer pais, Connection con) throws Exception {
        Estado estadoDAO = null;
        try {
            estadoDAO = new Estado(con);

            List<EstadoVO> lista = estadoDAO.consultarEstados(pais, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            Ordenacao.ordenarLista(lista, "descricao");
            List<EstadoWS> listaJSON = new ArrayList<>();
            for (EstadoVO obj : lista) {
                listaJSON.add(obj.toWS());
            }
            return listaJSON;
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        } finally {
            estadoDAO = null;
        }
    }

    private Object consultarCidades(Integer estado, Connection con) throws Exception {
        Cidade cidadeDAO = null;
        try {
            cidadeDAO = new Cidade(con);

            List<CidadeVO> lista = cidadeDAO.consultarPorCodigoEstado(estado, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            Ordenacao.ordenarLista(lista, "nome");
            List<CidadeWS> listaJSON = new ArrayList<>();
            for (CidadeVO obj : lista) {
                listaJSON.add(obj.toWS());
            }
            return listaJSON;
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        } finally {
            cidadeDAO = null;
        }
    }

    private Object consultarCEP(String cep, Connection con) throws Exception {
        Pais paisDAO = null;
        Estado estadoDAO = null;
        Cidade cidadeDAO = null;
        Cep cepDAO = null;
        try {
            paisDAO = new Pais(con);
            estadoDAO = new Estado(con);
            cidadeDAO = new Cidade(con);
            cepDAO = new Cep();

            if (UteisValidacao.emptyString(cep)) {
                throw new Exception("Informe o CEP corretamente!");
            }

            cep = Uteis.removerMascara(cep);

            CepVO cepVO = new CepVO();
            try {
                cepVO = cepDAO.consultarPorNumeroCep(Uteis.removerMascara(cep), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            } catch (Exception e) {
                try {
                    cepVO = cepDAO.consultarPorNumeroCepGeral(Uteis.removerMascara(cep), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                } catch (Exception ex) {
                    cepVO = cepDAO.consultarApiViaCEP(cep);
                }
            }

            JSONObject json = new JSONObject();
            json.put("cep", cep);
            json.put("bairro", cepVO.getBairroDescricao().trim());
            json.put("endereco", cepVO.getEnderecoLogradouro().trim());
            json.put("complemento", cepVO.getEnderecoCompleto().trim());


            PaisVO objPais = null;
            EstadoVO objEstado = null;
            CidadeVO objCidade = null;
            if (!UteisValidacao.emptyString(cepVO.getCidadeDescricao().trim())) {
                objCidade = cidadeDAO.consultarPorNome(Uteis.retirarAcentuacao(cepVO.getCidadeDescricao().trim()), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            }

            if (objCidade != null) {
                objPais = objCidade.getPais();
                objEstado = objCidade.getEstado();
            } else {
                objCidade = new CidadeVO();
                objPais = paisDAO.consultarPorNome("Brasil", Uteis.NIVELMONTARDADOS_DADOSBASICOS);

                if (objPais == null) {
                    throw new Exception("O país de nome Brasil deve ser cadastrado.");
                }
                objEstado = estadoDAO.consultarPorSiglaDescricaoEPais(
                        cepVO.getUfSigla().trim(),
                        cepVO.getUfDescricao().trim(), objPais.getCodigo(), false,
                        Uteis.NIVELMONTARDADOS_DADOSBASICOS);

                if (objEstado == null) {
                    objEstado = new EstadoVO();
                    objEstado.setDescricao(cepVO.getUfDescricao());
                    objEstado.setSigla(cepVO.getUfSigla());
                    objEstado.setPais(objPais.getCodigo());
                    estadoDAO.incluir(objEstado);

                }
                objCidade.setPais(objPais);
                objCidade.setEstado(objEstado);
                objCidade.setNome(cepVO.getCidadeDescricao().trim());
                cidadeDAO.incluir(objCidade);
            }


            if (objPais != null) {
                json.put("pais_codigo", objPais.getCodigo());
                json.put("pais_nome", objPais.getNome());
            }

            if (objEstado != null) {
                json.put("estado_codigo", objEstado.getCodigo());
                json.put("estado_nome", objEstado.getDescricao());
                json.put("estado_uf", objEstado.getSigla());
            }

            if (objCidade != null) {
                json.put("cidade_codigo", objCidade.getCodigo());
                json.put("cidade_nome", objCidade.getNomeSemAcento());
            }

            return json;
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        } finally {
            estadoDAO = null;
        }
    }

    private PlanoWS obterPlanoWSValidandoEmpresa(int empresa, PlanoVO plano, Connection con) throws Exception {
        PlanoEmpresaVO planoEmpresaVO = plano.obterPlanoEmpresa(empresa);
        return plano.toWS(planoEmpresaVO, con);
    }

}

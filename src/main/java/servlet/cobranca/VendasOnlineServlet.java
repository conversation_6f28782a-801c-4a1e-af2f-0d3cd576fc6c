package servlet.cobranca;

import acesso.webservice.AcessoControle;
import acesso.webservice.DaoAuxiliar;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.ConfiguracaoSistemaVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.FormaPagamentoPlanoProdutoTO;
import negocio.comuns.financeiro.BoletoVO;
import negocio.comuns.financeiro.ConvenioCobrancaVO;
import negocio.comuns.financeiro.MovParcelaVO;
import negocio.comuns.financeiro.PixVO;
import negocio.comuns.financeiro.enumerador.SituacaoConvenioCobranca;
import negocio.comuns.financeiro.enumerador.TipoCobrancaEnum;
import negocio.comuns.plano.CategoriaProdutoVO;
import negocio.comuns.plano.PlanoTextoPadraoVO;
import negocio.comuns.plano.ProdutoVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.basico.Cliente;
import negocio.facade.jdbc.financeiro.ConvenioCobranca;
import negocio.facade.jdbc.plano.CategoriaProduto;
import negocio.facade.jdbc.plano.Produto;
import negocio.facade.jdbc.utilitarias.Conexao;
import negocio.facade.jdbc.vendas.VendasConfig;
import negocio.facade.jdbc.vendas.VendasOnlineConvenio;
import negocio.facade.jdbc.vendas.VendasOnlineConvenioVO;
import negocio.vendasOnline.TokenVendasOnline;
import negocio.vendasOnline.TokenVendasOnlineVO;
import org.json.JSONArray;
import org.json.JSONObject;
import servicos.integracao.adm.AdmWS;
import servicos.integracao.pjbank.recebimento.BoletosManager;
import servicos.operacoes.midias.MidiaService;
import servicos.operacoes.midias.commons.MidiaEntidadeEnum;
import servicos.vendasonline.VendasOnlineService;
import servicos.vendasonline.dto.CategoriaProdutoVendasOnlineDTO;
import servicos.vendasonline.dto.ProdutoVendasOnlineDTO;
import servicos.vendasonline.dto.RetornoVendaTO;
import servicos.vendasonline.dto.VendaDTO;
import servlet.arquitetura.SuperServlet;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.sql.Connection;
import java.util.ArrayList;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Created with IntelliJ IDEA.
 * User: Luiz Felipe
 * Date: 04/06/2020
 */
public class VendasOnlineServlet extends SuperServlet {

    @Override
    protected void processRequest(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        Connection con = null;
        try {
            response.addHeader("Access-Control-Allow-Origin", "*");
            response.addHeader("Access-Control-Allow-Methods", "GET,POST");
            response.setContentType("application/json");

            String method = request.getMethod();

            if (!method.equalsIgnoreCase("POST")) {
                throw new Exception("Método não suportado.");
            }

            String key = request.getParameter("key");
            if (UteisValidacao.emptyString(key)) {
                throw new Exception("Chave não informada.");
            }

            OperacaoVendasOnlineDTO operacaoDTO = null;

            try {
                InputStream inputStream = request.getInputStream();
                BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream, "UTF-8"));

                StringBuffer body = new StringBuffer();
                String line = null;

                while ((line = reader.readLine()) != null) {
                    body.append(line);
                }

                operacaoDTO = new OperacaoVendasOnlineDTO(new JSONObject(body.toString()));
            } catch (Exception ex) {
                ex.printStackTrace();
                throw new Exception("Erro body: " + ex.getMessage());
            }

            con = new DAO().obterConexaoEspecifica(key);

            Object objRetorno = null;
            if (operacaoDTO.getOperacao().equalsIgnoreCase("obterConfig")) {
                objRetorno = obterConfiguracaoVendasOnline(key, operacaoDTO, con);
            }else if (operacaoDTO.getOperacao().equalsIgnoreCase("consultarDadosToken")) {
                objRetorno = obterDadosToken(request.getParameter("token"), con);
            }else if (operacaoDTO.getOperacao().equalsIgnoreCase("formaPagamentoPlanoProduto")) {
                objRetorno = obterVendasOnlineConvenioConfigs(Integer.valueOf(request.getParameter("empresa")), con);
            }else if (operacaoDTO.getOperacao().equalsIgnoreCase("buscarPorPlanoOuProduto")) {
                objRetorno = obterFormasPagamentoPlanoProduto(Integer.valueOf(request.getParameter("empresa")),
                        Integer.valueOf(request.getParameter("idConsulta")), Boolean.parseBoolean(request.getParameter("produto")), con);
            } else if (operacaoDTO.getOperacao().equalsIgnoreCase("consultarProdutos")) {
                objRetorno = consultarListaProdutos(operacaoDTO, con);
            } else if (operacaoDTO.getOperacao().equalsIgnoreCase("produto")) {
                objRetorno = consultarProduto(operacaoDTO, con);
            } else if (operacaoDTO.getOperacao().equalsIgnoreCase("consultarCategorias")) {
                objRetorno = consultarCategorias(con);
            } else if (operacaoDTO.getOperacao().equalsIgnoreCase("consultarConveniosCobranca")) {
                objRetorno = consultarConveniosCobranca(con, operacaoDTO);
            } else if (operacaoDTO.getOperacao().equalsIgnoreCase("consultarParcelasVencidas")) {
                objRetorno = consultarParcelasVencidas(operacaoDTO, con);
            } else if (operacaoDTO.getOperacao().equalsIgnoreCase("consultarDescontoCobrancaAntecipada")) {
                objRetorno = consultarDescontoCobrancaAntecipada(operacaoDTO, con);
            } else if (operacaoDTO.getOperacao().equalsIgnoreCase("imprimirTermoAceiteLink")) {
                objRetorno = imprimirTermoAceiteLinkPag(con, key);
            }else if(operacaoDTO.getOperacao().equalsIgnoreCase("cobrarParcelasPix")){
                try {
                    operacaoDTO.setUrlZw(request.getRequestURL().substring(0, request.getRequestURL().length() - request.getRequestURI().length()) + request.getContextPath());
                }catch (Exception ignore){}
                objRetorno = geraPixParcelasAbertas(key, operacaoDTO, con);
            }else if (operacaoDTO.getOperacao().equalsIgnoreCase("obterLogoEmail")) {
                objRetorno = obterLogoEmail(operacaoDTO, con);
            }else if(operacaoDTO.getOperacao().equalsIgnoreCase("consultaParcelasPix")) {
                objRetorno = consultaCobrancaPix(key, operacaoDTO, con);
            }else if(operacaoDTO.getOperacao().equalsIgnoreCase("obterEmpresasComSituacao")){
                if (UteisValidacao.emptyString(operacaoDTO.getEmpresa().toString())) {
                    throw new Exception("empresa não informada.");
                }
                objRetorno = obterEmpresasComSituacao(key, true, operacaoDTO.getEmpresa().toString());
                if (objRetorno == null) {
                    throw new Exception("Erro ao obter empresa, verifique se está ativa no cadastro da empresa!");
                }
            }else if(operacaoDTO.getOperacao().equalsIgnoreCase("cobrarParcelasSaldo")){
                VendaDTO vendaDTO = new VendaDTO();
                vendaDTO.setUnidade(operacaoDTO.getEmpresa());
                vendaDTO.setCobrancaAntecipada(operacaoDTO.getCobrancaAntecipada());
                vendaDTO.setPactoPayComunicacao(operacaoDTO.getPactoPayComunicacao());
                VendasOnlineService vendasOnlineService = new VendasOnlineService(key, con);
                objRetorno = vendasOnlineService.cobrarParcelasAbertasSaldo(key, operacaoDTO.getMatricula(), vendaDTO, operacaoDTO.getUsuario());
            }else if(operacaoDTO.getOperacao().equalsIgnoreCase("obterSaldoContaCorrenteCliente")){
                Cliente clienteDAO = new Cliente(con);
                ClienteVO clienteVO = new ClienteVO();
                clienteVO.getEmpresa().setCodigo(operacaoDTO.getEmpresa());
                clienteVO.getPessoa().setCodigo(operacaoDTO.getPessoa());
                objRetorno = clienteDAO.consultarSaldoCliente(clienteVO);
            } else if (operacaoDTO.getOperacao().equalsIgnoreCase("cobrarParcelasBoleto")) {
                objRetorno = gerarBoletoParcelasAbertas(key, operacaoDTO, con);
            } else if (operacaoDTO.getOperacao().equalsIgnoreCase("incluirAlunoContratoPix")) {
                objRetorno = incluirAlunoContratoPix(key, operacaoDTO, con);
            } else if (operacaoDTO.getOperacao().equalsIgnoreCase("obterValorComTaxaParcelamentoStone")) {
                objRetorno = obterValorComTaxaParcelamentoStone(key, operacaoDTO, con);
            } else if (operacaoDTO.getOperacao().equalsIgnoreCase("incluirAlunoContratoBoleto")) {
                objRetorno = incluirAlunoContratoBoleto(key, operacaoDTO, con);
            } else {
                throw new Exception("Nenhuma operação executada");
            }

            if (objRetorno == null) {
                throw new Exception("Nenhuma operação executada");
            }

            response.getWriter().append(this.toJSON(true, objRetorno).toString());
        } catch (Exception ex) {
            response.setStatus(400);
            response.getWriter().append(this.toJSON(false, ex.getMessage()).toString());
        } finally {
            if (con != null) {
                try {
                    con.close();
                } catch (Exception ex) {
                }
            }
        }
    }

    public List<JSONObject>  obterEmpresasComSituacao( String key, boolean ativa, String Unidade) {
        JSONObject jsonRetorno = new JSONObject();

        List<JSONObject> jsonListRetorno = new ArrayList<>();
        try {
            try {
                Uteis.logar(null, "====START INTEGRAÇÃO obterEmpresas ==== " + key);
                List<EmpresaVO> empresas = new ArrayList<EmpresaVO>();
                List<EmpresaVO> listEmpresas = DaoAuxiliar.retornarAcessoControle(key).getEmpresaDao().consultarEmpresas(Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                ConfiguracaoSistemaVO configuracaoSistemaVO = DaoAuxiliar.retornarAcessoControle(key).getConfiguracaoDao().consultarConfigs(Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                for (EmpresaVO empresa : listEmpresas) {
                    if ((ativa && empresa.isAtiva()) &&  empresa.getCodigo().toString().equals(Unidade) ) {
                        empresas.add(empresa);
                    }
                }
                Uteis.logar(null, "====END INTEGRAÇÃO obterEmpresas ==== " + empresas.toString());
                jsonRetorno.put("codigo", empresas.get(0).getCodigo());
                jsonRetorno.put("nome", empresas.get(0).getNome());
                jsonRetorno.put("chave",key);
                jsonRetorno.put("cidade", empresas.get(0).getCidade().getNome());
                jsonRetorno.put("estado", empresas.get(0).getCidade().getEstado().getDescricao());
                jsonRetorno.put("telefone", empresas.get(0).getTelComercial1());
                jsonRetorno.put("email", empresas.get(0).getEmail());
                jsonRetorno.put("cep", empresas.get(0).getCEP());
                jsonRetorno.put("longitude", empresas.get(0).getLongitude());
                jsonRetorno.put("latitude", empresas.get(0).getLatitude());
                jsonRetorno.put("endereco", empresas.get(0).getEndereco());
                jsonRetorno.put("logo", "");
                jsonRetorno.put("imagens",  new ArrayList<String>());
                jsonRetorno.put("complemento", empresas.get(0).getComplemento());
                jsonRetorno.put("usarSistemaInternacional", empresas.get(0).isUsarSistemaInternacional());
                jsonRetorno.put("moeda", empresas.get(0).getMoeda());
                jsonRetorno.put("locale", empresas.get(0).getLocaleTexto());
                jsonRetorno.put("mascaraTelefone", configuracaoSistemaVO.getMascaraTelefone());
                jsonRetorno.put("utilizaGestaoClientesComRestricoes", empresas.get(0).isUtilizaGestaoClientesComRestricoes());
                jsonRetorno.put("habilitarValidacaoHorariosMesmaTurma", empresas.get(0).getHabilitarValidacaoHorariosMesmaTurma());

                jsonListRetorno.add(jsonRetorno);

                return  jsonListRetorno;
            } catch (Exception ex) {
                Logger.getLogger(AdmWS.class.getName()).log(Level.SEVERE, null, ex);
                return null;
            }
        } catch (Exception ex) {
            Uteis.logar(ex, this.getClass());
            jsonRetorno.put("ERRO", ex.getMessage());
            jsonListRetorno.add(jsonRetorno);
            return  jsonListRetorno;
        }
    }

    public JSONObject consultaCobrancaPix(String key, OperacaoVendasOnlineDTO operacaoDTO, Connection con) {
        JSONObject jsonRetorno = new JSONObject();
        VendasOnlineService vendasOnlineService = null;
        try {
            if (UteisValidacao.emptyNumber(operacaoDTO.getCodigoPix())) {
                Uteis.logar("Código Pix não informado");
                jsonRetorno.put("ERRO", "Código Pix não informado");
                return jsonRetorno;
            }
            vendasOnlineService = new VendasOnlineService(key, con);
            jsonRetorno = vendasOnlineService.consultarCobrancaPix(operacaoDTO.getCodigoPix(), key);
        } catch (Exception ex) {
            Uteis.logar(ex, this.getClass());
            jsonRetorno.put("ERRO", ex.getMessage());
        } finally {
            vendasOnlineService = null;
        }
        return jsonRetorno;
    }

    public JSONObject geraPixParcelasAbertas(String key, OperacaoVendasOnlineDTO operacaoDTO, Connection con) {
        JSONObject jsonRetorno = new JSONObject();
        VendasOnlineService vendasOnlineService = null;
        try {
            vendasOnlineService = new VendasOnlineService(key, con);
            VendaDTO vendaDTO = new VendaDTO();
            vendaDTO.setUnidade(operacaoDTO.getEmpresa());
            vendaDTO.setOrigemCobranca(operacaoDTO.getOrigemCobranca());
            vendaDTO.setCobrancaAntecipada(operacaoDTO.getCobrancaAntecipada());
            vendaDTO.setPactoPayComunicacao(operacaoDTO.getPactoPayComunicacao());
            vendaDTO.setParcelasSelecionadas(operacaoDTO.getParcelasSelecionadas());
            vendaDTO.setEnviarEmail(operacaoDTO.getEnviarEmail());
            vendaDTO.setUrlZw(operacaoDTO.getUrlZw());
            vendaDTO.setTodasEmAberto(operacaoDTO.getTodasEmAberto());
            jsonRetorno = vendasOnlineService.cobrarParcelasAbertasPix(key, operacaoDTO.getMatricula(), vendaDTO);
        } catch (Exception ex) {
            Uteis.logar(ex, this.getClass());
            jsonRetorno.put("ERRO", ex.getMessage());
        } finally {
            vendasOnlineService = null;
        }
        return jsonRetorno;
    }

    public String obterLogoEmail(OperacaoVendasOnlineDTO operacaoDTO, Connection con) {
        try {
            String chave = DAO.resolveKeyFromConnection(con);
            String genKey = MidiaService.getInstance().genKey(chave, MidiaEntidadeEnum.FOTO_EMPRESA_EMAIL, operacaoDTO.getEmpresa().toString());
            if (UteisValidacao.emptyString(genKey)) {
                genKey = MidiaService.getInstance().genKey(chave, MidiaEntidadeEnum.FOTO_EMPRESA, operacaoDTO.getEmpresa().toString());
            }
            return Uteis.getPaintFotoDaNuvem(genKey);
        } catch (Exception ex) {
            return ex.getMessage();
        }
    }

    private JSONObject toJSON(boolean sucesso, Object dados) {
        JSONObject json = new JSONObject();
        json.put("sucesso", sucesso);
        json.put("dados", dados);
        return json;
    }

    private Object obterDadosToken(String token, Connection con) throws Exception {
        TokenVendasOnline tokenVendasOnlineDAO;
        try {
            tokenVendasOnlineDAO = new TokenVendasOnline(con);
            TokenVendasOnlineVO tokenVendasOnlineVO = tokenVendasOnlineDAO.consultarPorToken(token);
            //Tempo de expiração padrão do link é de 5 DIAS
            if (UteisValidacao.emptyNumber(tokenVendasOnlineVO.getDadosTokenDTO().getCodCobrancaAntecipada()) &&
                Calendario.menorComHora(Calendario.somarMinutos(tokenVendasOnlineVO.getDataRegistro(),7200), Calendario.hoje())) {
                throw new Exception("O link está expirado. O tempo de expiração do link é de 5 dias, entre em contato com a academia e solicite um novo.");
            }
            //Links que já foram pagos com sucesso possuem a coluna dataUtilizado preenchida e também são considerados expirados
            if (!UteisValidacao.emptyString(Uteis.getDataFormatoBD(tokenVendasOnlineVO.getDataUtilizado()))) {
                throw new Exception("Este link já foi utilizado. Entre em contato com a academia e solicite um novo.");
            }
            return tokenVendasOnlineVO.getDados();
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        } finally {
            tokenVendasOnlineDAO = null;
        }
    }

    private JSONArray obterVendasOnlineConvenioConfigs(Integer empresa, Connection con) throws Exception {
        VendasConfig vendasConfigDAO;
        try {
            vendasConfigDAO = new VendasConfig(con);
            List<FormaPagamentoPlanoProdutoTO> formaPagamentoPlanoProdutoTOS = vendasConfigDAO.obterVendasOnlineConvenioConfigs(empresa, true);

            return new JSONArray(formaPagamentoPlanoProdutoTOS);
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        } finally {
            vendasConfigDAO = null;
        }
    }

    private List<String> obterFormasPagamentoPlanoProduto(Integer empresa, Integer idConsulta, boolean produto, Connection con) throws Exception {
        VendasOnlineConvenio vendasOnlineConvenioDAO;
        try {
            vendasOnlineConvenioDAO = new VendasOnlineConvenio(con);
            List<VendasOnlineConvenioVO> configs = vendasOnlineConvenioDAO.consultarPorProdutoOuPlano(empresa, produto, idConsulta, false, Uteis.NIVELMONTARDADOS_DADOSBASICOS, true, 0);
            List<String> formasPagamento = new ArrayList<>();
            if (!configs.isEmpty()) {
                configs.forEach(config -> {
                    if (!UteisValidacao.emptyNumber(config.getFormaPagamento())) {
                            formasPagamento.add(config.getFormaPagamento().toString());
                    }
                });
            }
            return formasPagamento;
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        } finally {
            vendasOnlineConvenioDAO = null;
        }
    }

    private Object consultarListaProdutos(OperacaoVendasOnlineDTO operacaoDTO, Connection con) throws Exception {
        Produto produtoDAO = null;
        try {
            produtoDAO = new Produto(con);

            JSONArray listaDTO = new JSONArray();
            List<ProdutoVO> produtos = new ArrayList<>();
            if (operacaoDTO.isPactoFlow()) {
                produtos = produtoDAO.consultarParaPactoFlow(0, operacaoDTO.getCategoria(), operacaoDTO.getEmpresa(), Uteis.NIVELMONTARDADOS_TODOS);
            } else {
                produtos = produtoDAO.consultarParaVendasOnline(0, operacaoDTO.getCategoria(), operacaoDTO.getEmpresa(), Uteis.NIVELMONTARDADOS_TODOS);
            }
            for (ProdutoVO obj : produtos) {
                listaDTO.put(new JSONObject(new ProdutoVendasOnlineDTO(obj)));
            }
            return listaDTO;
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        } finally {
            produtoDAO = null;
        }
    }

    private Object consultarProduto(OperacaoVendasOnlineDTO operacaoDTO, Connection con) throws Exception {
        Produto produtoDAO = null;
        try {
            produtoDAO = new Produto(con);

            if (UteisValidacao.emptyNumber(operacaoDTO.getProduto())) {
                throw new Exception("Produto não informado.");
            }

            List<ProdutoVO> produtos = produtoDAO.consultarParaVendasOnline(operacaoDTO.getProduto(), operacaoDTO.getCategoria(), operacaoDTO.getEmpresa(), Uteis.NIVELMONTARDADOS_TODOS);
            if (UteisValidacao.emptyList(produtos)) {
                throw new Exception("Produto não encontrado com o código " + operacaoDTO.getProduto());
            }
            return new JSONObject(new ProdutoVendasOnlineDTO(produtos.get(0)));
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        } finally {
            produtoDAO = null;
        }
    }

    private Object consultarCategorias(Connection con) throws Exception {
        CategoriaProduto categoriaProdutoDAO = null;
        try {
            categoriaProdutoDAO = new CategoriaProduto(con);

            JSONArray listaDTO = new JSONArray();
            List<CategoriaProdutoVO> categorias = categoriaProdutoDAO.consultarCategoriasParaVendasOnline();
            for (CategoriaProdutoVO obj : categorias) {
                listaDTO.put(new JSONObject(new CategoriaProdutoVendasOnlineDTO(obj)));
            }
            return listaDTO;
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        } finally {
            categoriaProdutoDAO = null;
        }
    }

    private Object consultarParcelasVencidas(OperacaoVendasOnlineDTO operacaoDTO, Connection con) throws Exception {
        VendasOnlineService vendasOnlineService;
        try {
            vendasOnlineService = new VendasOnlineService(null, con);

            List<MovParcelaVO> movParcelasEmAberto = vendasOnlineService.consultarParcelasVencidas(operacaoDTO.getCliente(),
                    operacaoDTO.getPessoa(), operacaoDTO.getEmpresa(),
                    operacaoDTO.getTipoCobranca(), operacaoDTO.getCobrancaAntecipada(), operacaoDTO.getOrigem());

            JSONArray movParcelas = new JSONArray();
            for (MovParcelaVO obj : movParcelasEmAberto) {
                movParcelas.put(new JSONObject(obj.toWS()));
            }
            return movParcelas.toString();
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        } finally {
            vendasOnlineService = null;
        }
    }

    public String imprimirTermoAceiteLinkPag(Connection con, String key) {
        String retorno = "";
        AcessoControle acessoControle;
        try {
            acessoControle = DaoAuxiliar.retornarAcessoControle(key);
            Conexao.guardarConexaoForJ2SE(key, acessoControle.getCon());


            List<PlanoTextoPadraoVO> modelosTermoAceite = acessoControle.getPlanoTextoPadraoDao().consultarPorCodigoSituacaoTipo(0, "AT", "LP", false, Uteis.NIVELMONTARDADOS_TODOS);
            if (modelosTermoAceite.size() > 0) {
                retorno = modelosTermoAceite.get(0).getTexto();
            } else {
                retorno = null;
            }
        } catch (Exception ex) {
            retorno = ex.getMessage();
            Uteis.logar(ex, this.getClass());
        }

        return retorno;
    }

    private Object consultarConveniosCobranca(Connection con, OperacaoVendasOnlineDTO operacaoDTO) throws Exception {
        ConvenioCobranca convenioCobrancaDAO = null;
        try {
            convenioCobrancaDAO = new ConvenioCobranca(con);

            TipoCobrancaEnum tipoCobrancaEnum = TipoCobrancaEnum.obterPorId(operacaoDTO.getTipoCobranca());

            List<ConvenioCobrancaVO> lista = convenioCobrancaDAO.consultarPorTipoCobranca(tipoCobrancaEnum, operacaoDTO.getEmpresa(), SituacaoConvenioCobranca.ATIVO, null, Uteis.NIVELMONTARDADOS_DADOSBASICOS);

            JSONArray listaDTO = new JSONArray();
            for (ConvenioCobrancaVO obj : lista) {
                JSONObject json = new JSONObject();
                json.put("codigo", obj.getCodigo());
                json.put("descricao", obj.getDescricao());
                listaDTO.put(json);
            }
            return listaDTO;
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        } finally {
            convenioCobrancaDAO = null;
        }
    }

    public JSONObject gerarBoletoParcelasAbertas(String key, OperacaoVendasOnlineDTO operacaoDTO, Connection con) {
        JSONObject jsonRetorno = new JSONObject();
        VendasOnlineService vendasOnlineService = null;
        try {
            vendasOnlineService = new VendasOnlineService(key, con);
            VendaDTO vendaDTO = new VendaDTO();
            vendaDTO.setUnidade(operacaoDTO.getEmpresa());
            vendaDTO.setOrigemCobranca(operacaoDTO.getOrigemCobranca());
            vendaDTO.setCobrancaAntecipada(operacaoDTO.getCobrancaAntecipada());
            vendaDTO.setPactoPayComunicacao(operacaoDTO.getPactoPayComunicacao());
            vendaDTO.setParcelasSelecionadas(operacaoDTO.getParcelasSelecionadas());
            vendaDTO.setTodasEmAberto(operacaoDTO.getTodasEmAberto());
            jsonRetorno = vendasOnlineService.cobrarParcelasAbertasBoleto(key, operacaoDTO.getMatricula(), vendaDTO);
        } catch (Exception ex) {
            Uteis.logar(ex, this.getClass());
            jsonRetorno.put("ERRO", ex.getMessage());
        } finally {
            vendasOnlineService = null;
        }
        return jsonRetorno;
    }

    private Object consultarDescontoCobrancaAntecipada(OperacaoVendasOnlineDTO operacaoDTO, Connection con) throws Exception {
        VendasOnlineService vendasOnlineService;
        try {
            vendasOnlineService = new VendasOnlineService(null, con);
            return vendasOnlineService.consultarDescontoCobrancaAntecipada(operacaoDTO.getValorCobrar(),
                    operacaoDTO.getEmpresa(), operacaoDTO.getCobrancaAntecipada());
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        } finally {
            vendasOnlineService = null;
        }
    }

    private Object incluirAlunoContratoPix(String key, OperacaoVendasOnlineDTO operacaoDTO, Connection con) throws Exception {
        VendasOnlineService service;
        try {
            service = new VendasOnlineService(key, con);
            operacaoDTO.getVenda().setPix(true);
            List<RetornoVendaTO> listaRet = service.incluirAlunoEVendaOnlineRetornaLista(key, operacaoDTO.getVenda());

            PixVO pixVO = listaRet.get(0).getPixVO();
            JSONObject jsonDados = new JSONObject();
            if (listaRet.get(0).isErro() == false && pixVO.getCodigo() != null) {
                jsonDados.put("pix", pixVO.getCodigo());
                jsonDados.put("UrlQRcode", pixVO.obterUrlSomenteImagemQRcode(key));
                jsonDados.put("qrtext", pixVO.getTextoImagemQrcode());
                jsonDados.put("sucesso", true);
            } else {
                jsonDados.put("mensagem", "Renovação efetuada com sucesso");
                jsonDados.put("sucesso", true);
            }
            return jsonDados;
        } finally {
            service = null;
        }
    }

    private String obterValorComTaxaParcelamentoStone(String key, OperacaoVendasOnlineDTO operacaoDTO, Connection con) throws Exception {
        VendasOnlineService service;
        try {
            service = new VendasOnlineService(key, con);
            return service.obterValorComTaxaParcelamentoStone(operacaoDTO);
        } finally {
            service = null;
        }
    }

    private Object incluirAlunoContratoBoleto(String key, OperacaoVendasOnlineDTO operacaoDTO, Connection con) throws Exception {
        VendasOnlineService service;
        ConvenioCobranca convenioCobrancaDAO;
        try {
            service = new VendasOnlineService(key, con);
            operacaoDTO.getVenda().setBoleto(true);
            List<RetornoVendaTO> listaRet = service.incluirAlunoEVendaOnlineRetornaLista(key, operacaoDTO.getVenda());

            JSONObject jsonDados = new JSONObject();
            BoletoVO boletoVO = null;
            if (listaRet.get(0).getBoletosGerados().size() == 1) {
                boletoVO = listaRet.get(0).getBoletosGerados().get(0);
                jsonDados.put("boleto_url", boletoVO.getLinkBoleto());

            } else {
                String urlBoletos = listaRet.get(0).getBoletosGerados().get(0).getLinkBoleto();
                try {
                    convenioCobrancaDAO = new ConvenioCobranca(con);
                    ConvenioCobrancaVO convenioCobrancaVO = convenioCobrancaDAO.consultarPorChavePrimaria(listaRet.get(0).getBoletosGerados().get(0).getConvenioCobrancaVO().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                    LinkedHashSet<String> pedidos = BoletoVO.obterListaImpressaoBoleto(listaRet.get(0).getBoletosGerados());
                    BoletosManager boletosManager = new BoletosManager(convenioCobrancaVO.getCredencialPJBank(), convenioCobrancaVO.getChavePJBank(), convenioCobrancaVO);
                    urlBoletos = boletosManager.getByIds(pedidos);
                } catch (Exception ex) {
                    ex.printStackTrace();
                }
                jsonDados.put("boleto_url", urlBoletos);
            }

            jsonDados.put("boleto_codigo", boletoVO != null ? boletoVO.getCodigo() : 0);
            jsonDados.put("boleto_linha_digitavel", boletoVO != null ? boletoVO.getLinhaDigitavel() : "");
            jsonDados.put("boleto_valor", boletoVO != null ? boletoVO.getValorApresentar() : "");
            jsonDados.put("boleto_vencimento", boletoVO != null ? boletoVO.getDataVencimentoApresentar() : "");
            jsonDados.put("boleto_qtd", listaRet.get(0).getBoletosGerados().size());
            jsonDados.put("sucesso", true);
            return jsonDados;
        } finally {
            service = null;
            convenioCobrancaDAO = null;
        }
    }

    private String obterConfiguracaoVendasOnline(String key, OperacaoVendasOnlineDTO operacaoDTO, Connection con) {
        VendasOnlineService service;
        try {
            service = new VendasOnlineService(key, con);
            JSONObject configsVendas = service.obterConfigsVendas(operacaoDTO.getEmpresa());
            return configsVendas.toString();
        } catch (Exception e) {
            e.printStackTrace();
            JSONObject jsonErro = new JSONObject();
            jsonErro.put("erro", e.getMessage());
            return jsonErro.toString();
        }
    }
}

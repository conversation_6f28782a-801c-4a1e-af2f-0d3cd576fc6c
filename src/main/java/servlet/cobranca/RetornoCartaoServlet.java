package servlet.cobranca;

import br.com.pactosolucoes.atualizadb.processo.ProcessoRetornoCartoes;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import org.json.JSONObject;
import servlet.arquitetura.SuperServlet;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.sql.Connection;

/**
 * Created with IntelliJ IDEA.
 * User: Lu<PERSON>
 * Date: 06/03/2020
 */
public class RetornoCartaoServlet extends SuperServlet {

    @Override
    protected void processRequest(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {

        response.addHeader("Access-Control-Allow-Origin", "*");
        response.addHeader("Access-Control-Allow-Methods", "GET");
        response.setContentType("application/json");

        String chave = request.getParameter("chave");
        String operacao = request.getParameter("op");
        String inicio = request.getParameter("inicio"); //yyyyMMdd
        String fim = request.getParameter("fim"); //yyyyMMdd
        String empresa = request.getParameter("empresa");
        String method = request.getMethod();

        Connection con = null;
        try {
            if (!method.equalsIgnoreCase("POST")) {
                response.setStatus(400);
                JSONObject json = new JSONObject();
                json.put("sucesso", false);
                json.put("mensagem", "Methodo não suportado para esse recurso");
                response.getWriter().append(json.toString());
                return;
            }

            con = new DAO().obterConexaoEspecifica(chave);

            switch (operacao) {
                case "retornoCartao":
                    retornoCartao(empresa, inicio, fim, response, con);
                    break;
                case "atualizarDataCadastro":
                    atualizarDataCadastro(response, con);
                    break;
            }
        } catch (Exception ex) {
            response.getWriter().append(this.toJSON(false, ex.getMessage()).toString());
        } finally {
            try {
                if (con != null) {
                    con.close();
                }
            } catch (Exception ignored) {
            }
        }
    }

    private JSONObject toJSON(boolean sucesso, String msg) {
        JSONObject json = new JSONObject();
        json.put("sucesso", sucesso);
        json.put("msg", msg);
        return json;
    }

    private void retornoCartao(String empresa, String inicio, String fim,
                               HttpServletResponse resp, Connection con) throws Exception {
        Integer codEmpresa = 0;
        if (!UteisValidacao.emptyString(empresa)) {
            codEmpresa = Integer.parseInt(empresa);
        }
        ProcessoRetornoCartoes retornoCartoes = new ProcessoRetornoCartoes(con);
        JSONObject json = retornoCartoes.processar(codEmpresa, Uteis.getDate(inicio, "yyyyMMdd"), Uteis.getDate(fim, "yyyyMMdd"));
        retornoCartoes = null;
        resp.getWriter().append(json.toString());
    }

    private void atualizarDataCadastro(HttpServletResponse resp, Connection con) throws Exception {
        ProcessoRetornoCartoes retornoCartoes = new ProcessoRetornoCartoes(con);
        JSONObject json = retornoCartoes.atualizarDataCadastroCartao();
        retornoCartoes = null;
        resp.getWriter().append(json.toString());
    }
}

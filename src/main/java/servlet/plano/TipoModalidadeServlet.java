package servlet.plano;

import servlet.arquitetura.SuperServlet;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;

/**
 * Created with IntelliJ IDEA.
 * User: <PERSON><PERSON> <PERSON><PERSON><PERSON>
 * Date: 14/11/2018
 * Time: 11:19
 * To change this template use File | Settings | File Templates.
 */

public class TipoModalidadeServlet extends SuperServlet {
    @Override
    protected void processRequest(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        super.processRequest(request, response);
        PrintWriter out = response.getWriter();
        String json;
        try {
            String codigoEmpresa = request.getParameter("empresa");
            json = ff(request).getTipoModalidade().consultarJSON(codigoEmpresa);

        } catch (Exception e) {
            json = (e.getMessage() == null) ? NullPointerException.class.getName() : e.getMessage();
        }
        out.println(json);
        json = null;
    }
}
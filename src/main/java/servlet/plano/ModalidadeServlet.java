package servlet.plano;

import controle.arquitetura.security.LoginControle;
import servlet.arquitetura.SuperServlet;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;

/**
 * Created with IntelliJ IDEA.
 * User: franciscoanjos
 * Date: 04/09/13
 * Time: 11:19
 * To change this template use File | Settings | File Templates.
 */

public class ModalidadeServlet extends SuperServlet {
    @Override
    protected void processRequest(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        super.processRequest(request, response);
        PrintWriter out = response.getWriter();
        String json;
        try {
            String situacao = obterParametroString(request.getParameter("situacao"));
            Integer empresa = ((LoginControle)getAttribute(request, "LoginControle")).getEmpresa().getCodigo();
            String sEcho = obterParametroString(request.getParameter("sEcho"));
            Integer offset = obterParametro(request.getParameter("iDisplayStart"));
            Integer limit = obterParametro(request.getParameter("iDisplayLength"));
            String clausulaLike = obterParametroString(request.getParameter("sSearch"));
            Integer colOrdenar = obterParametro(request.getParameter("iSortCol_0"));
            String dirOrdenar = obterParametroString(request.getParameter("sSortDir_0"));
            json = ff(request).getModalidade().consultarJSON(empresa, sEcho, offset, limit, clausulaLike, colOrdenar, dirOrdenar,situacao);

        } catch (Exception e) {
            json = (e.getMessage() == null) ? NullPointerException.class.getName() : e.getMessage();
        }
        out.println(json);
        json = null;
    }
}
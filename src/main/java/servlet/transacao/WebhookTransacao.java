package servlet.transacao;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.financeiro.TransacaoVO;
import negocio.comuns.financeiro.enumerador.SituacaoTransacaoEnum;
import negocio.comuns.financeiro.enumerador.TipoTransacaoEnum;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.financeiro.Transacao;
import negocio.facade.jdbc.utilitarias.Conexao;
import org.json.JSONObject;
import servicos.impl.gatewaypagamento.VerificadorTransacaoService;
import servicos.integracao.pjbank.enums.TipoTransacao;
import servlet.arquitetura.SuperServlet;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.sql.Connection;


/**
 * Created with IntelliJ IDEA.
 * User: <PERSON><PERSON>
 * Date: 08/05/2020
 * <p>
 * #########################################################################################
 * ######################################## ATENÇÃO ########################################
 * <p>
 * ESSA CLASSE FOI CRIADA COM O OBJETIVO DE RECEBER WEBHOOK DE INTEGRAÇÕES DE TRANSAÇÕES ONLINE
 * SÓ É ACEITO REQUISAÇÃO POST !
 * <p>
 * ######################################## REGRAS ########################################
 * <p>
 * 1 - Passar a chave da empresa via parametro (key)
 * 2 - Passar o código do tipo da transação (TipoTransacaoEnum) via parametro (tipo)
 * Exemplo:
 * http://localhost:8084/zw/prest/transacao/webhook?key=teste&tipo=12
 * <p>
 * #########################################################################################
 * #########################################################################################
 */
public class WebhookTransacao extends SuperServlet {

    @Override
    protected void processRequest(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        Connection con = null;
        Transacao transacaoDAO = null;
        try {
            response.addHeader("Access-Control-Allow-Origin", "*");
            response.addHeader("Access-Control-Allow-Methods", "GET");
            response.setContentType("application/json");
            response.setCharacterEncoding("UTF-8");

            String method = request.getMethod();

            if (!method.equalsIgnoreCase("POST")) {
                throw new Exception("Methodo não suportado para esse recurso");
            }

            String key = request.getParameter("key");
            String tipo = request.getParameter("tipo");
            if (UteisValidacao.emptyString(key)) {
                throw new Exception("Key não informada");
            }

            if (UteisValidacao.emptyString(tipo)) {
                throw new Exception("Tipo não informado");
            }

            TipoTransacaoEnum tipoTransacaoEnum = TipoTransacaoEnum.getTipoTransacaoEnum(Integer.parseInt(tipo));

            StringBuffer body = new StringBuffer();
            try {
                InputStream inputStream = request.getInputStream();
                BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream, "UTF-8"));

                String line = null;

                while ((line = reader.readLine()) != null) {
                    body.append(line);
                }
            } catch (Exception ex) {
                ex.printStackTrace();
                throw new Exception("Erro body: " + ex.getMessage());
            }


            con = new DAO().obterConexaoEspecifica(key);
            Conexao.guardarConexaoForJ2SE(key, con);
            transacaoDAO = new Transacao(con);
//            transacaoDAO.incluirTransacaoWebhook(tipoTransacaoEnum.getId(), body.toString());

            if (tipoTransacaoEnum.equals(TipoTransacaoEnum.VINDI)) {
                processarVindi(body.toString(), con);
            } else if (tipoTransacaoEnum.equals(TipoTransacaoEnum.MUNDIPAGG)) {
                processarMundiPagg(body.toString(), con);
            } else if (tipoTransacaoEnum.equals(TipoTransacaoEnum.PAGAR_ME)) {
                processarPagarMe(body.toString(), con);
            }

            response.setStatus(200);
            PrintWriter out = response.getWriter();
            out.print(this.toJSON(true, "ok"));
            out.flush();
        } catch (Exception ex) {
            response.setStatus(400);
            PrintWriter out = response.getWriter();
            out.print(this.toJSON(false, ex.getMessage()));
            out.flush();
        } finally {
            transacaoDAO = null;
            try {
                if (con != null) {
                    con.close();
                }
            } catch (Exception ex) {
                ex.printStackTrace();
            }
        }
    }

    private JSONObject toJSON(boolean sucesso, Object obj) {
        JSONObject json = new JSONObject();
        json.put("sucesso", sucesso);
        json.put("dados", obj);
        return json;
    }

    private void processarVindi(String body, Connection con) throws Exception {
        VerificadorTransacaoService service = null;
        Transacao transacaoDAO = null;
        try {
            Uteis.logar(null, "Webhook VINDI | Início");

            transacaoDAO = new Transacao(con);

            JSONObject respJSON = new JSONObject(body);
            JSONObject eventJSON = respJSON.getJSONObject("event");
            String type = eventJSON.optString("type");

            //teste de webhook da vindi...
            //ignorar somente retornar como sucesso..!
            if (type.equalsIgnoreCase("test")) {
                return;
            }

            Integer idBill = eventJSON.getJSONObject("data").getJSONObject("charge").getJSONObject("bill").optInt("id");
            TransacaoVO transacaoVO = transacaoDAO.consultarPorCodigoExternoETipo(idBill.toString(), TipoTransacaoEnum.VINDI);

            boolean webhookCancelamento = type.equalsIgnoreCase("charge_refunded") || type.equalsIgnoreCase("charge_canceled");
            boolean isPrecisaProcessarWebhookCancelamento = webhookCancelamento
                    && !transacaoVO.getSituacao().equals(SituacaoTransacaoEnum.CANCELADA)
                    && !transacaoVO.getSituacao().equals(SituacaoTransacaoEnum.NAO_APROVADA);

            if (isPrecisaProcessarWebhookCancelamento
                    || (type.equalsIgnoreCase("bill_paid") && !transacaoVO.getSituacao().equals(SituacaoTransacaoEnum.CONCLUIDA_COM_SUCESSO))
                    || (type.equalsIgnoreCase("charge_rejected") && transacaoVO.getSituacao().equals(SituacaoTransacaoEnum.APROVADA))) {
                service = new VerificadorTransacaoService(con);
                service.processarWebhook(TipoTransacaoEnum.VINDI, idBill.toString(), null);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            Uteis.logar(null, "Webhook VINDI | Erro: " + ex.getMessage());
            throw ex;
        } finally {
            service = null;
            transacaoDAO = null;
            Uteis.logar(null, "Webhook VINDI | FIM");
        }
    }

    private void processarMundiPagg(String body, Connection con) throws Exception {
        Uteis.logar(null, "Webhook MUNDIPAGG início...");

        VerificadorTransacaoService service = null;
        try {
            Uteis.logar(null, "Webhook MUNDIPAGG | Início");

            if (UteisValidacao.emptyString(body)) {
                throw new Exception("Não foi possivel obter o conteudo.");
            }

            service = new VerificadorTransacaoService(con);

            JSONObject json = new JSONObject(body);
            String type = json.optString("type");

            Uteis.logar(null, "Webhook MUNDIPAGG | " + type);

            if (type.equalsIgnoreCase("order.canceled") ||
                    type.equalsIgnoreCase("order.payment_failed") ||
                    type.equalsIgnoreCase("order.updated") ||
                    type.equalsIgnoreCase("order.paid")) {

                JSONObject dataJSON = json.getJSONObject("data");
                String codigoExterno = dataJSON.optString("id");
                service.processarWebhook(TipoTransacaoEnum.MUNDIPAGG, codigoExterno, null);
            }

        } catch (Exception ex) {
            Uteis.logar(null, "Webhook MUNDIPAGG | Erro: " + ex.getMessage());
            throw ex;
        } finally {
            service = null;
            Uteis.logar(null, "Webhook MUNDIPAGG | FIM");
        }
    }

    private void processarPagarMe(String body, Connection con) throws Exception {
        Uteis.logar(null, "Webhook PAGAR.ME início...");

        VerificadorTransacaoService service = null;
        try {
            Uteis.logar(null, "Webhook PAGAR.ME | Início");

            if (UteisValidacao.emptyString(body)) {
                throw new Exception("Não foi possivel obter o conteudo.");
            }

            service = new VerificadorTransacaoService(con);

            JSONObject json = new JSONObject(body);
            String event = json.optString("event");

            Uteis.logar(null, "Webhook PAGAR.ME | " + event);

            if (event.equalsIgnoreCase("transaction_status_changed")) {
                Integer codigoTransacao = 0;
                try {
                    String reference_key = json.optString("reference_key");
                    codigoTransacao = Integer.parseInt(reference_key.replaceAll("[^0-9.]", ""));
                } catch (Exception ignored) {
                }
                String codigoExterno = json.optString("id");
                service.processarWebhook(TipoTransacaoEnum.PAGAR_ME, codigoExterno, codigoTransacao);
            }

        } catch (Exception ex) {
            Uteis.logar(null, "Webhook PAGAR.ME | Erro: " + ex.getMessage());
            throw ex;
        } finally {
            service = null;
            Uteis.logar(null, "Webhook MUNDIPAGG | FIM");
        }
    }
}

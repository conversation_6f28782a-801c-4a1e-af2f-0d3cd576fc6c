/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package servlet.acesso;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import javassist.NotFoundException;
import negocio.comuns.acesso.AutorizacaoAcessoGrupoEmpresarialVO;
import negocio.facade.jdbc.acesso.AutorizacaoAcessoGrupoEmpresarial;
import servlet.arquitetura.SuperServlet;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.EOFException;
import java.io.IOException;
import java.io.PrintWriter;
import java.sql.Connection;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * <AUTHOR>
 */
public class AutorizacaoAcessoGrupoEmpresarialServlet extends SuperServlet {
    @Override
    protected void processRequest(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        String path = request.getServletPath();
        String[] pathParts = path.split("/");
        String recurso = pathParts[3];
        if ("findAutorizacaoAcesso".equals(recurso)) {
            processFindByRequest(request, response);
            return;
        }
        String empresa = obterParametroString(request.getParameter("empresa"));

        super.processRequest(request, response);
        PrintWriter out = response.getWriter();
        String json;
        try {
            json = ff(request).getAutorizacaoAcessoGrupoEmpresarial().consultarJSON(Integer.parseInt(empresa));
        } catch (Exception e) {
            json = (e.getMessage() == null) ? NullPointerException.class.getName() : e.getMessage();
        }
        out.println(json);
        json = null;
    }

    private void processFindByRequest(HttpServletRequest request, HttpServletResponse response) throws IOException {
        response.addHeader("Access-Control-Allow-Origin", "*");
        response.addHeader("Access-Control-Allow-Methods", "GET");
        response.setContentType("application/json");

        String chave = request.getParameter("key");
        String text = request.getParameter("cpfOrTelefone");

        try (Connection c = new DAO().obterConexaoEspecifica(chave)) {
            AutorizacaoAcessoGrupoEmpresarial autorizacaoDAO = new AutorizacaoAcessoGrupoEmpresarial(c);
            AutorizacaoAcessoGrupoEmpresarialVO autorizado = autorizacaoDAO.consultarPorCpfOuTelefone(text);

            if (autorizado != null) {
                response.getWriter().append(autorizado.toJSON().toString());
                return;
            }

            response.setStatus(HttpServletResponse.SC_NOT_FOUND);
            montarErro(response, "Não foi encontrado autorizado com esses dados");
        } catch (Exception e) {
            Logger.getLogger(getClass().getName()).log(Level.SEVERE, null, e);
            response.setStatus(HttpServletResponse.SC_BAD_REQUEST);
            montarErro(response, e.getMessage());
        }
    }

    private void montarErro(HttpServletResponse response, String msg) throws IOException {
        response.getWriter().append("{" + "\"sucesso\": \"false\"," + "\"mensagem\": \"").append(msg).append("\"").append("}");
    }
}

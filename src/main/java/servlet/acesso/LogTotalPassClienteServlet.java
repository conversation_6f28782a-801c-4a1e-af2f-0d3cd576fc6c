/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package servlet.acesso;

import servlet.arquitetura.SuperServlet;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;

/**
 *
 * <AUTHOR>
 */
public class LogTotalPassClienteServlet extends SuperServlet {
        @Override
    protected void processRequest(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        super.processRequest(request, response);
        PrintWriter out = response.getWriter();
        String json;
        try {
            String cliente = obterParametroString(request.getParameter("cliente"));
            json = ff(request).getLogTotalPass().consultarLogTotalPassJSON(Integer.valueOf(cliente), null);

        } catch (Exception e) {
            json = (e.getMessage() == null) ? NullPointerException.class.getName() : e.getMessage();
        }
        out.println(json);
        json = null;
    }
}

package servlet.acesso;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import com.google.common.net.HttpHeaders;
import controle.arquitetura.exceptions.ServiceException;
import controle.arquitetura.security.LoginControle;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.facade.jdbc.utilitarias.Conexao;
import negocio.modulos.integracao.usuariomovel.UsuarioMovel;
import negocio.modulos.integracao.usuariomovel.UsuarioMovelVO;
import org.json.JSONObject;
import servicos.impl.autenticacaoMs.AutenticacaoMsService;
import servicos.integracao.treino.IdiomaBancoEnum;
import servicos.oamd.OamdMsService;
import servicos.oamd.RedeEmpresaDataDTO;
import servicos.propriedades.PropsService;
import servicos.util.ExecuteRequestHttpService;
import servicos.vendasonline.dto.VendaDTO;
import servlet.arquitetura.SuperServlet;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.sql.Connection;
import java.util.HashMap;
import java.util.Map;

import static servicos.propriedades.PropsService.modulos;

public class BINovoTreinoServlet extends SuperServlet {

    @Override
    protected void processRequest(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {

        response.setHeader("Access-Control-Allow-Origin", "*");
        response.setHeader("Access-Control-Allow-Methods", "GET, OPTIONS");
        response.setHeader("Access-Control-Max-Age", "3600");
        response.setHeader("Access-Control-Allow-Headers", "Authorization, content-type, xsrf-token");
        response.addHeader("Access-Control-Expose-Headers", "xsrf-token");
        response.setContentType("application/json");

        try{
            validarAuthorizationComChave(request);

            String chave = request.getHeader(HttpHeaders.AUTHORIZATION);
            String username = request.getParameter("username");
            String senha = request.getParameter("senha");
            String empresa = request.getParameter("empresa");
            Connection connection = new DAO().obterConexaoEspecifica(chave);
            Conexao.guardarConexaoForJ2SE(chave, connection);
            Usuario usuario = new Usuario(connection);
            UsuarioVO usuarioVO = usuario.consultarPorUsername(username, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            String linguagem = usuarioVO.getLinguagem() == null ? "pt" : usuarioVO.getLinguagem();

            String urlBITreino = PropsService.getPropertyValue(chave, PropsService.treinoFront) + "/" +
                    linguagem;

            JSONObject jsonBody = new JSONObject();
            jsonBody.put("chave", chave);
            jsonBody.put("username", username);
            jsonBody.put("senha", senha);
            Map<String, String> maps = new HashMap<>();
            maps.put("Content-Type", "application/json");
            String tokenZW = AutenticacaoMsService.token(chave, username, senha);

            urlBITreino += "/adicionarConta?";
            urlBITreino += "token=" + tokenZW;
            urlBITreino += "&moduleId=" + "NTR";
            urlBITreino += "&idiomabanco=" + (UteisValidacao.emptyString(usuarioVO.getLinguagem()) ?
                    0 : IdiomaBancoEnum.fromValue(usuarioVO.getLinguagem().toUpperCase().replace("-", "_")).ordinal());
            urlBITreino += "&modulos=" + modulos.replaceAll("\\s", "");
            urlBITreino += "&integracaoZW=true";
            urlBITreino += "&empresaId=" + empresa;
            urlBITreino += "&urladm=" + "";
            urlBITreino += "&urlapi=" + PropsService.getPropertyValue(chave, PropsService.urlTreinoWeb) + "/prest";
            urlBITreino += "&usuarioOamd=" + "";
            urlBITreino += "&redirect=/treino/bi/dashboard";

            JSONObject json = new JSONObject();
            json.put("url", urlBITreino);
            response.getWriter().append(json.toString());
        } catch (Exception e) {
            processarErro(new ServiceException(e), request, response, null);
        }
    }
}

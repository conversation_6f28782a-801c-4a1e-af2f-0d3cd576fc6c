package servlet.notaFiscal;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.notaFiscal.NotaFiscalJSON;
import negocio.comuns.notaFiscal.NotaFiscalVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.facade.jdbc.notaFiscal.NotaFiscal;
import org.json.JSONObject;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.sql.Connection;
import java.util.ArrayList;
import java.util.List;

public class NotaFiscalServlet extends HttpServlet {

    /**
     * Processes requests for both HTTP
     * <code>GET</code> and
     * <code>POST</code> methods.
     *
     * @param request  servlet request
     * @param response servlet response
     * @throws ServletException if a servlet-specific error occurs
     * @throws IOException      if an I/O error occurs
     */

    private static String ERROR = "error";
    private static String RETURN = "return";
    private static String RETURN_SUCESSO = "ok";

    protected void processRequest(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        Connection con = null;
        JSONObject jsonResponse = new JSONObject();

        try {
            String key;
            String operacao = "";

            if (!UteisValidacao.emptyString(request.getHeader("key"))) {

                key = request.getHeader("key");
                con = new DAO().obterConexaoEspecifica(key);

            } else {
                throw new Exception("KEY NÃO INFORMADA");
            }

            if (!UteisValidacao.emptyString(request.getHeader("operacao"))) {
                operacao = request.getHeader("operacao");
                if (UteisValidacao.emptyString(operacao)) {
                    throw new Exception("OPERAÇÃO NÃO INFORMADA");
                }
            }

            if (operacao.equalsIgnoreCase("consultarNotaAluno")) {
                consultarNotasFiscalAluno(request, jsonResponse, con);
            } else if (operacao.equalsIgnoreCase("enviarNotaEmail")) {
                enviarEmailNotaFiscalAluno(request, jsonResponse, con);
            } else {
                throw new Exception("OPERAÇÃO NÃO INFORMADA");
            }

        } catch (Exception ex) {
            Uteis.logar(null, "ERRO - REQUISIÇÃO NotaFiscalServlet: " + ex.getMessage());
            ex.printStackTrace();
            jsonResponse.put(ERROR, ex.getMessage().toUpperCase());
        } finally {
            try {
                if (con != null) {
                    con.close();
                }
            } catch (Exception ignored) {
            }
        }
        response.setCharacterEncoding("UTF-8");
        response.setContentType("application/json");
        response.getOutputStream().print(jsonResponse.toString());
    }

    private void consultarNotasFiscalAluno(HttpServletRequest request, JSONObject jsonResponse, Connection con) throws Exception {

        final String pessoa = request.getHeader("pessoa");
        if (UteisValidacao.emptyString(pessoa)) {
            throw new Exception("PESSOA NÃO INFORMADA");
        }

        String pagina = request.getHeader("pagina");
        if (UteisValidacao.emptyString(pagina)) {
            throw new Exception("PÁGINA NÃO INFORMADA");
        }

        String limitePorPagina = request.getHeader("limitePorPagina");
        if (UteisValidacao.emptyString(limitePorPagina)) {
            throw new Exception("LIMITE POR PÁGINA NÃO INFORMADO");
        }

        String orderBy = request.getHeader("orderBy");

        Integer offset = (Integer.parseInt(pagina) == 0 ? 0 : (Integer.parseInt(pagina) - 1) * Integer.parseInt(limitePorPagina) + 1);

        NotaFiscal notaDAO = new NotaFiscal(con);
        List<NotaFiscalVO> notas = notaDAO.consultarNotas(Integer.parseInt(pessoa), Integer.parseInt(limitePorPagina), offset, orderBy, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        Integer totalNotas = notaDAO.consultarNotasTotal(Integer.parseInt(pessoa));
        notaDAO = null;

        List<NotaFiscalJSON> listaRetorno = new ArrayList<NotaFiscalJSON>();
        for (NotaFiscalVO obj : notas) {
            try {
                listaRetorno.add(new NotaFiscalJSON(obj));
            } catch (Exception ignored) {}
        }
        JSONObject jsonNotas = new JSONObject();
        jsonNotas.put("total", totalNotas);
        jsonNotas.put("notas", listaRetorno);
        jsonResponse.put(RETURN, jsonNotas);
    }

    private void enviarEmailNotaFiscalAluno(HttpServletRequest request, JSONObject jsonResponse, Connection con) throws Exception {

        final String codNotaFiscal = request.getHeader("notaFiscal");
        if (UteisValidacao.emptyString(codNotaFiscal)) {
            throw new Exception("CÓDIGO NOTA FISCAL NÃO INFORMADA");
        }

        String email = request.getHeader("email");

        NotaFiscal notaDAO = new NotaFiscal(con);
        Usuario usuarioDAO = new Usuario(con);

        try {
            NotaFiscalVO notaFiscalVO = notaDAO.consultarPorChavePrimaria(Integer.parseInt(codNotaFiscal), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if (UteisValidacao.emptyNumber(notaFiscalVO.getCodigo())) {
                throw new Exception("Nota Fiscal não encontrada!");
            }

            //SE NÃO INFORMAR EMAIL USAR O EMAIL DA NOTA FISCAL
            if (UteisValidacao.emptyString(email)) {
                email = notaFiscalVO.getClienteEmail();
            }
            String retorno = notaDAO.enviarEmailNotaFiscal(email, notaFiscalVO, usuarioDAO.getUsuarioRecorrencia(), " - WS");
            jsonResponse.put(RETURN, retorno);
        } catch (Exception ex) {
            jsonResponse.put(ERROR, ex.getMessage());
        } finally {
            notaDAO = null;
            usuarioDAO = null;
        }
    }

    // <editor-fold defaultstate="collapsed" desc="HttpServlet methods. Click on the + sign on the left to edit the code.">

    /**
     * Handles the HTTP
     * <code>GET</code> method.
     *
     * @param request  servlet request
     * @param response servlet response
     * @throws ServletException if a servlet-specific error occurs
     * @throws IOException      if an I/O error occurs
     */
    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        processRequest(request, response);
    }

    /**
     * Handles the HTTP
     * <code>POST</code> method.
     *
     * @param request  servlet request
     * @param response servlet response
     * @throws ServletException if a servlet-specific error occurs
     * @throws IOException      if an I/O error occurs
     */
    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        processRequest(request, response);
    }

    /**
     * Returns a short description of the servlet.
     *
     * @return a String containing servlet description
     */
    @Override
    public String getServletInfo() {
        return "Short description";
    }// </editor-fold>
}

package servlet.turma;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import br.com.pactosolucoes.turmas.servico.dto.ModalidadeTurmaDTO;
import br.com.pactosolucoes.turmas.servico.dto.TurmaDTO;
import br.com.pactosolucoes.turmas.servico.impl.TurmasServiceImpl;
import br.com.pactosolucoes.turmas.servico.intf.TurmasServiceInterface;
import negocio.comuns.utilitarias.Uteis;
import org.json.JSONArray;
import org.json.JSONObject;
import servlet.arquitetura.SuperServlet;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;

public class TurmaVendasOnlineServlet extends SuperServlet {

    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        processRequest(request, response);
    }

    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        processRequest(request, response);
    }


    protected void processRequest(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {

        response.addHeader("Access-Control-Allow-Origin", "*");
        response.addHeader("Access-Control-Allow-Methods", "GET");
        response.setContentType("application/json");

        String path = request.getServletPath();
        String[] pathParts = path.split("/");
        String recurso = pathParts[3];

        String chave = request.getParameter("chave");
        try (TurmasServiceInterface service = getService(chave)) {
            switch (recurso){
                case "turmas":
                    String modalidades = request.getParameter("modalidades");
                    Integer empresa = Integer.valueOf(request.getParameter("empresa"));
                    Integer idade = Integer.valueOf(request.getParameter("idade"));
                    Integer codigoPessoa = request.getParameter("codigoPessoa") != null ?
                            Integer.valueOf(request.getParameter("codigoPessoa")) : null;
                    List<TurmaDTO> turmas = service.consultarTurmasIdade(true, false, empresa, idade, modalidades, codigoPessoa);
                    List<ModalidadeTurmaDTO> modalidadesTurma = new ArrayList<>();
                    String[] mods = modalidades.split(",");
                    Arrays.stream(mods).forEach(m ->{
                        ModalidadeTurmaDTO modalidadeTurmaDTO = new ModalidadeTurmaDTO();
                        modalidadeTurmaDTO.setCodigo(Integer.parseInt(m));
                        modalidadeTurmaDTO.setTurmas(new ArrayList<>());
                        turmas.stream().filter(t -> t.getModalidade().equals(Integer.parseInt(m))).forEach(t -> {
                            modalidadeTurmaDTO.setDescricao(t.getModalidadeDesc());
                            modalidadeTurmaDTO.getTurmas().add(t);
                        });
                        modalidadesTurma.add(modalidadeTurmaDTO);
                    });
                    JSONObject content = new JSONObject().put("content", new JSONArray(modalidadesTurma.isEmpty() ? new ArrayList<ModalidadeTurmaDTO>() : modalidadesTurma).toString());
                    response.getWriter().append(content.toString());
                    break;
            }
        }catch (Exception e ){
            Uteis.logarDebug(String.format("Erro na api rest %s. Message: %s",
                    recurso, e.getMessage()));
            processarErro(e, request, response, null);
        }

    }

    private TurmasServiceInterface getService(String chave) throws Exception {
        return new TurmasServiceImpl(new DAO().obterConexaoEspecifica(chave));
    }

}

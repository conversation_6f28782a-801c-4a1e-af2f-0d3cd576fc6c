package servlet.relatorio;

import controle.arquitetura.security.LoginControle;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.crm.GrupoColaboradorParticipanteVO;
import negocio.comuns.crm.GrupoColaboradorVO;
import negocio.comuns.utilitarias.Uteis;
import relatorio.controle.basico.BIControle;
import relatorio.controle.sad.RenovacaoSinteticoControle;
import servlet.arquitetura.SuperServlet;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.util.ArrayList;
import java.util.List;

/**
 * Created by glauco on 19/05/2014.
 */
public class RenovacaoSinteticoServlet extends SuperServlet {
    protected void processRequest(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        super.processRequest(request, response);
        PrintWriter out = response.getWriter();
        String json;
        try {
            RenovacaoSinteticoControle controle = (RenovacaoSinteticoControle)getAttribute(request, "RenovacaoSinteticoControle");

            List<ColaboradorVO> listaColaboradorVOS = new ArrayList<>();
            try {
                listaColaboradorVOS = ((BIControle)request.getSession().getAttribute(BIControle.class.getSimpleName())).getListaColaboradorVOs();
            } catch (Exception ignored) {

            }


            Integer empresa = ((LoginControle)getAttribute(request, "LoginControle")).getEmpresa().getCodigo();
            BIControle biControle = ((BIControle)getAttribute(request, "BIControle"));

            if(biControle != null && biControle.getEmpresaFiltro() != null){
                empresa = biControle.getEmpresaFiltro().getCodigo();
            }
            json = ff(request).getContrato().
                    clientesComContratosPrevistosPeriodoJSON(empresa,
                            Uteis.obterPrimeiroDiaMesPrimeiraHora(controle.getDataBaseFiltroBI()),
                            Uteis.obterUltimoDiaMesUltimaHora(controle.getDataBaseFiltroBI()),
                            listaColaboradorVOS, controle.getRelatorioAbrir(), null, null,
                            controle.getListaClientes());
        } catch (Exception e) {
            json = (e.getMessage() == null) ? NullPointerException.class.getName() : e.getMessage();
            e.printStackTrace();
        }
        out.println(json);
        json = null;
    }

}

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package servlet.relatorio;

import controle.arquitetura.security.LoginControle;
import controle.basico.LancarBrindeClienteControle;
import java.io.IOException;
import java.io.PrintWriter;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import negocio.comuns.utilitarias.UteisValidacao;
import servlet.arquitetura.SuperServlet;

/**
 *
 * <AUTHOR>
 */
public class LancarBrindeClienteServlet extends SuperServlet{
    
    protected void processRequest(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        super.processRequest(request, response);
        PrintWriter out = response.getWriter();
        String json = null;
        try {
            LancarBrindeClienteControle controle = (LancarBrindeClienteControle)getAttribute(request, "LancarBrindeClienteControle");
            
            Integer pontos = 0;
            String mostrarTodos = obterParametroString(request.getParameter("mostrartodos"));
            if (controle.getClienteSelecionado() != null && UteisValidacao.notEmptyNumber(controle.getClienteSelecionado().getCodigo())) {
                pontos = ff(request).getHistoricoPontos().obterPontosTotalPorCliente(controle.getClienteSelecionado().getCodigo());
            } else {
                mostrarTodos = "sim";
            }

            
            Integer empresa = ((LoginControle)getAttribute(request, "LoginControle")).getEmpresa().getCodigo();
            

            json = ff(request).getBrinde().consultarBrindeDisponiveisPorPontoJson(pontos, empresa,mostrarTodos);

            controle.setPontosClienteSelecioando(pontos);
            
        } catch (Exception e) {
            json = (e.getMessage() == null) ? NullPointerException.class.getName() : e.getMessage();
        }
        out.println(json);
        json = null;
    }
    
}

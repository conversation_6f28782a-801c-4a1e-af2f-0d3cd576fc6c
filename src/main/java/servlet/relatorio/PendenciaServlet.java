package servlet.relatorio;

import negocio.comuns.basico.enumerador.TipoPendenciaEnum;
import relatorio.controle.basico.BIControle;
import relatorio.controle.basico.PendenciaControleRel;
import servlet.arquitetura.SuperServlet;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;

/**
 * Created by glauco on 19/05/2014.
 */
public class PendenciaServlet extends SuperServlet {
    protected void processRequest(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        super.processRequest(request, response);
        PrintWriter out = response.getWriter();
        String json = null;
        try {

            PendenciaControleRel controle = (PendenciaControleRel) getAttribute(request, "PendenciaControleRel");
            Integer empresa = controle.getEmpresaFiltroBI().getCodigo();

            BIControle biControle = ((BIControle) getAttribute(request, "BIControle"));

            if (biControle != null && biControle.getEmpresaFiltro() != null) {
                empresa = biControle.getEmpresaFiltro().getCodigo();
            }

            if (controle.getPendenciaSelecionada().equals(TipoPendenciaEnum.CreditoCCorrente)) {
                json = ff(request).getMovimentoContaCorrenteCliente().
                        consultarPendenciasClienteCreditoContaCorrenteJSON(
                                empresa,
                                controle.getColaboradoresSelecionados(),
                                controle.getDataBaseInicialFiltro());
            }
        } catch (Exception e) {
            json = (e.getMessage() == null) ? NullPointerException.class.getName() : e.getMessage();
        }
        out.println(json);
        json = null;
    }

}

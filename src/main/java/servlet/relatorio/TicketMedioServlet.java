/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package servlet.relatorio;

import negocio.comuns.utilitarias.Uteis;
import relatorio.controle.sad.BITicketMedioControle;
import servlet.arquitetura.SuperServlet;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.util.Date;

/**
 * <AUTHOR>
 */
public class TicketMedioServlet extends SuperServlet {

    protected void processRequest(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        super.processRequest(request, response);
        PrintWriter out = response.getWriter();
        String json;

        try {
            BITicketMedioControle controle = (BITicketMedioControle)getAttribute(request, BITicketMedioControle.class.getSimpleName());
            Date inicio = Uteis.obterPrimeiroDiaMes(controle.getDataBase());
            Date fim = Uteis.obterUltimoDiaMesUltimaHora(controle.getDataBase());
            switch (controle.getTipo()) {
                case COMPETENCIA:
                    json = ff(request).getTicketMedio().povoarJSON(controle.getTicket(), true);
                    break;
                case FATURAMENTO_RECEBIDO:
                    json = ff(request).getTicketMedio().povoarJSON(controle.getTicket(), false);
                    break;
                case RECEITA:
                    json = ff(request).getTicketMedio().caixaReceitaJSON(controle.getEmpresaFiltro().getCodigo(), inicio, fim);
                    break;
                default:
                    json = "{\"aaData\":[]}";
                    break;
            }

        } catch (Exception e) {
            json = (e.getMessage() == null) ? NullPointerException.class.getName() : e.getMessage();
        }
        out.println(json);
        json = null;
    }
}

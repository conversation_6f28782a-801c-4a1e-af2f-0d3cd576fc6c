package servlet.relatorio;

import controle.arquitetura.security.LoginControle;
import negocio.comuns.crm.FiltroCarteiraTO;
import negocio.comuns.utilitarias.DataTableServerSideProperties;
import relatorio.controle.crm.CarteirasRel;
import servlet.arquitetura.SuperServlet;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;

/**
 * Created with IntelliJ IDEA.
 * User: glauco
 * Date: 05/09/13
 * Time: 15:58
 */

public class CarteirasServlet extends SuperServlet {

    @Override
    protected void processRequest(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        super.processRequest(request, response);
        PrintWriter out = response.getWriter();
        String json;
        try {
            FiltroCarteiraTO filtroCarteiraTO = (FiltroCarteiraTO)getAttribute(request, "filtroCarteiraTO");
            Integer empresa = ((LoginControle)getAttribute(request, "LoginControle")).getEmpresa().getCodigo();
            filtroCarteiraTO.setEmpresaLogado(empresa);

            DataTableServerSideProperties dataTableServerSideProperties = new DataTableServerSideProperties(request);
            CarteirasRel carteirasRel = ff(request).getCarteirasRel();

            if (dataTableServerSideProperties.isDataTableServerSidePropertiesAtivado()) {
                json = carteirasRel.consultarJSONPaginado(filtroCarteiraTO, dataTableServerSideProperties);
            } else {
                json = carteirasRel.consultarJSON(filtroCarteiraTO);
            }
        } catch (Exception e) {
            if(!(e instanceof NullPointerException)) {
                json = " {\"idraw\":1, \"iTotalDisplayRecords\" : 0, \"iTotalRecords\" : 0, \"aaData\":[], \"sError\":\""+e.getMessage()+"\" }";
            }else {
                json = "{\"idraw\":1, \"iTotalDisplayRecords\" : 0, \"iTotalRecords\" : 0, \"aaData\":[], \"sError\":\"Servidor indisponível, tente novamente\" }";
            }
        }
        out.println(json);
        json = null;
    }
}
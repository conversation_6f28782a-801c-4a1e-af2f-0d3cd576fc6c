package servlet.relatorio;

import controle.arquitetura.security.LoginControle;
import relatorio.controle.basico.BIControle;
import relatorio.controle.sad.RelControleOperacoesControle;
import servlet.arquitetura.SuperServlet;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;

/**
 * Created by <PERSON><PERSON> on 16/09/2014.
 */
public class ContratosCanceladosServlet extends SuperServlet {
    protected void processRequest(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        super.processRequest(request, response);
        PrintWriter out = response.getWriter();
        String json;
        try {
            RelControleOperacoesControle controle = (RelControleOperacoesControle)getAttribute(request, "RelControleOperacoesControle");

            Integer empresa = ((BIControle)getAttribute(request, "BIControle")).getEmpresaFiltro().getCodigo();
            json = ff(request).getContratoOperacao().consultarClientesCanceladosJSON(empresa, controle.getListaColaboradorFiltroBI(), controle.getInicio(), controle.getDataBaseFiltroBI());
        } catch (Exception e) {
            json = (e.getMessage() == null) ? NullPointerException.class.getName() : e.getMessage();
        }
        out.println(json);
        json = null;
    }

}

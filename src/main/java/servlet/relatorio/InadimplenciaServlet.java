/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package servlet.relatorio;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.utilitarias.Calendario;
import servicos.relatorio.InadimplenciaService;
import servlet.arquitetura.SuperServlet;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.sql.Connection;
import java.util.Date;

/**
 * <AUTHOR>
 */
public class InadimplenciaServlet extends SuperServlet {

    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        processRequest(request, response);
    }

    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        processRequest(request, response);
    }

    protected void processRequest(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        response.addHeader("Access-Control-Allow-Origin", "*");
        response.addHeader("Access-Control-Allow-Methods", "GET,POST");
        response.setContentType("application/json");

        String chave = request.getParameter("chave");
        Integer empresa = Integer.valueOf(request.getParameter("empresa"));

        String path = request.getServletPath();
        String[] pathParts = path.split("/");
        String recurso = pathParts[4];

        try {
            switch (recurso) {
                case "consultar-parcelas":
                    try (Connection con = new DAO().obterConexaoEspecifica(chave)) {
                        Date dataInicioVencimento = Calendario.getDate("yyyy/MM/dd", request.getParameter("datainiciovencimento"));
                        Date dataFimVencimento = Calendario.getDate("yyyy/MM/dd", request.getParameter("datafimvencimento"));
                        Integer colaborador = Integer.valueOf(request.getParameter("colaborador"));
                        boolean canceladaAposVencimento = Boolean.parseBoolean(request.getParameter("canceladaaposvencimento"));
                        boolean canceladaAntesVencimento = Boolean.parseBoolean(request.getParameter("canceladaantesvencimento"));
                        String situacoesParcela = request.getParameter("situacoesparcela");
                        response.getWriter().append(new InadimplenciaService(con).consultarParcelas(empresa, dataInicioVencimento, dataFimVencimento,
                                false, "", true, colaborador, false, canceladaAposVencimento,
                                canceladaAntesVencimento, situacoesParcela).toString());
                    }
                    break;

            }
        } catch (Exception e) {
            System.out.println("Erro na api rest " + recurso + ". Message: " + e.getMessage());
            processarErro(e, request, response, null);
        }
    }
}

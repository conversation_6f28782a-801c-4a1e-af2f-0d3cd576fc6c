package servlet.relatorio;

import controle.arquitetura.security.LoginControle;
import controle.basico.LtvControle;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.utilitarias.Uteis;
import relatorio.controle.basico.BIControle;
import relatorio.controle.sad.RenovacaoSinteticoControle;
import servlet.arquitetura.SuperServlet;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.util.ArrayList;
import java.util.List;

public class LTVServlet extends SuperServlet {
    protected void processRequest(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        super.processRequest(request, response);
        PrintWriter out = response.getWriter();
        String json;
        try {
            LtvControle controle = (LtvControle)getAttribute(request, "LtvControle");

            Integer empresa = ((LoginControle)getAttribute(request, "LoginControle")).getEmpresa().getCodigo();
            BIControle biControle = ((BIControle)getAttribute(request, "BIControle"));

            if(biControle != null && biControle.getEmpresaFiltro() != null){
                empresa = biControle.getEmpresaFiltro().getCodigo();
            }
            json = ff(request).getContrato().
                    consultarLifeTimeContratosVigentesJSON(controle.isConfigLtvRealizado(), empresa,
                            Uteis.obterUltimoDiaMesUltimaHora(controle.getInicio()));
        } catch (Exception e) {
            json = (e.getMessage() == null) ? NullPointerException.class.getName() : e.getMessage();
            e.printStackTrace();
        }
        out.println(json);
        json = null;
    }

}

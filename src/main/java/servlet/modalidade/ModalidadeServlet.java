package servlet.modalidade;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import cfin.wrapper.TResultadoBoleto;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.basico.Colaborador;
import negocio.facade.jdbc.plano.Modalidade;
import org.json.JSONArray;
import org.json.JSONObject;
import servlet.arquitetura.SuperServlet;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

public class ModalidadeServlet extends SuperServlet {

    @Override
    protected void processRequest(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {

        response.addHeader("Access-Control-Allow-Origin", "*");
        response.addHeader("Access-Control-Allow-Methods", "GET");
        response.setContentType("application/json");

        String path = request.getServletPath();
        String[] pathParts = path.split("/");
        String recurso = pathParts[3];

        String chave = request.getParameter("chave");
        String size = request.getParameter("size");
        String page = request.getParameter("page");
        String empresa = request.getParameter("empresa");
        String codigo = request.getParameter("codigo");
        String tipo = request.getParameter("tipo");

        try {

            switch (recurso){
                case "todos":
                    todasModalidades(request,
                            response,
                            chave,
                            empresa == null ? null : Integer.parseInt(empresa),
                            size == null ? null : Integer.parseInt(size),
                            page == null ? null : Integer.parseInt(page));
                    break;
            }

        }catch (Exception e ){
            System.out.println("Erro na api rest "+recurso+". Message: "+e.getMessage());
            processarErro(e, request, response, null);
        }

    }

    private void todasModalidades(HttpServletRequest request, HttpServletResponse response,
                                    String chave,
                                    Integer empresa,
                                    Integer size, Integer page) throws Exception {
        JSONArray colaboradoresArray = getDao(chave).todasModalidades(empresa, size, page);
        response.getWriter().append(colaboradoresArray.toString());
    }

    private Modalidade getDao(String chave) throws Exception {
        return new Modalidade(new DAO().obterConexaoEspecifica(chave));
    }
}

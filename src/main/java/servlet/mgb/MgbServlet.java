package servlet.mgb;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import com.amazonaws.util.IOUtils;
import negocio.comuns.utilitarias.UteisValidacao;
import org.json.JSONObject;
import servicos.integracao.mgb.impl.MgbServiceImpl;
import servicos.integracao.mgb.impl.StudentDTO;
import servicos.integracao.mgb.intf.MgbService;
import servlet.arquitetura.SuperServlet;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.sql.Connection;

public class MgbServlet extends SuperServlet {


    @Override
    protected void processRequest(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        response.setHeader("Access-Control-Allow-Origin", "*");
        response.setHeader("Access-Control-Allow-Methods", "GET,POST");
        response.setHeader("Access-Control-Allow-Headers", "*");
        response.setContentType("application/json");

        String path = request.getServletPath();
        String[] pathParts = path.split("/");
        String recurso = pathParts[3];

        if ("mgb".equals(recurso)) {
            if (!UteisValidacao.emptyString(pathParts[4])) {
                recurso = pathParts[4];
            }
        }

        String chave = request.getParameter("chave");
        String matricula = request.getParameter("matricula");
        String empresa = request.getParameter("empresa");
        String publicIdMgb = request.getParameter("publicIdMgb");
        String token = request.getParameter("token");
        MgbService mgbService;
        try {
            try (Connection con = new DAO().obterConexaoEspecifica(chave)) {
                mgbService = new MgbServiceImpl(con);

                switch (recurso) {
                    case "tokensEmpresas":
                        response.getWriter().append(mgbService.tokens().toString());
                        break;
                    case "findByMatricula":
                        student(response,
                                matricula == null ? null : Integer.parseInt(matricula),
                                mgbService);
                        break;
                    case "insertToken":
                        mgbService.insertToken(chave, Integer.parseInt(empresa), token);
                        response.getWriter().append("token inserido com sucesso!");
                        break;
                    case "updateToken":
                        mgbService.updateToken(chave, Integer.parseInt(empresa), token);
                        response.getWriter().append("token alterado com sucesso!");
                        break;
                    case "publicidMgb":
                        buscarIdPublicMgb(response,
                                matricula == null ? null : Integer.parseInt(matricula),
                                mgbService);
                        break;
                    case "tokenMgb":
                        buscarTokencMgb(response,
                                empresa == null ? null : Integer.parseInt(empresa),
                                mgbService);
                        break;
                    case "updatePublicIdMgb": // ver possibilidade de manter esse fluxo apenas para aluno que atenda as condições de sincronização
                        // este fluxo é chamado pelo MGB e está causando divergência, será retirado temporariamente.
                        // inserirPublicMgb(request,
                        //          response,
                        //          matricula == null ? null : Integer.parseInt(matricula),
                        //          publicIdMgb, mgbService);
                        break;
                    case "sincronizar-alunos-ativos":
                        sincronizarAlunosAtivos(response, Integer.parseInt(empresa), mgbService);
                        break;
                    case "sync-horario-turma-mgb":
                        syncHorarioTurmaMgb(response, chave, Integer.parseInt(empresa), mgbService, Integer.parseInt(request.getParameter("codigoHorarioTurma")));
                        break;
                    case "sync-turma-mgb":
                        syncTurmaMgb(response, chave, Integer.parseInt(empresa), mgbService, Integer.parseInt(request.getParameter("codTurma")));
                        break;
                    case "sync-todas-turmas-com-mgb":
                        // Caso vaiValidarVagas = false, irá ignorar validação de quantidade de vagas no momento da sincronização
                        // Caso margemMinutoInicioFimHorario for maior que 0 na validação da hora de inicio ou fim vai adicionar margem de minuto,
                        // - por exemplo, se for passado o valor 1, e na pacto o horario da turma terminar as 10:30 e no mgb tiver sido cadastrado 10:29 ou 10:31
                        // - será considerado igual e não vai criar uma nova turma no mgb mas vai associar o horario da pacto à turma do mgb

                        Boolean vaiValidarVagas = Boolean.parseBoolean(request.getParameter("vaiValidarVagas"));
                        Integer margemMinutoInicioFimHorario = Integer.parseInt(request.getParameter("margemMinutoInicioFimHorario"));
                        syncTodasTurmasComMgb(response, Integer.parseInt(empresa), chave, vaiValidarVagas, margemMinutoInicioFimHorario, mgbService);
                        break;
                    case "inativar-turma-mgb":
                        inativarTurmaMgb(response, Integer.parseInt(empresa), mgbService, Integer.parseInt(request.getParameter("codigoHorarioTurma")));
                        break;
                    case "deletar-turma-mgb":
                        deletarTurmaMgb(response, Integer.parseInt(empresa), mgbService, Integer.parseInt(request.getParameter("codigoHorarioTurma")));
                        break;
                    case "deletar-todas-turmas-no-mgb":
                        deletarTodasTurmasNoMgb(response, Integer.parseInt(empresa), mgbService, Boolean.parseBoolean(request.getParameter("apenasValidar")));
                        break;
                    case "sincronizar-aluno-mgb":
                        syncAlunoMgb(response, Integer.parseInt(empresa), mgbService, Integer.parseInt(request.getParameter("codigoCliente")));
                        break;
                    case "consultar-dados-aluno":
                        consultarDadosMgbAluno(response, Integer.parseInt(empresa), mgbService, Integer.parseInt(request.getParameter("codigoCliente")));
                        break;
                    case "deletar-publicid-professor":
                        deletarPublicIdProfessor(response, Integer.parseInt(empresa), mgbService, Integer.parseInt(request.getParameter("codigoColaborador")));
                        break;
                    case "remover-utilizacao-piscina":
                        // se não tiver ambiente vinculado a piscina retornar ok
                        // se tiver ambiente vinculado a piscina, mas ambiente não é utilizado na turma, remover publicId da piscina e retornar ok
                        // se tiver ambiente vinculado a piscina e o ambiente é utilizado em uma turma, retornar que não pode remover o publicid da piscina do ambiente
                        removerUtilizacaoPiscina(response, mgbService, request.getParameter("publicIdPiscina"));
                        break;
                }
            }
        } catch (Exception e) {
            System.out.println("Erro na api rest " + recurso + ". Message: " + e.getMessage());
            processarErro(e, request, response, null);
        } finally {
            mgbService = null;
        }
    }

    private void student(HttpServletResponse response, Integer matricula,
                         MgbService mgbService) throws Exception {
        String codNivelMgb = mgbService.verificarPublicidmgb(matricula);
        StudentDTO aluno = mgbService.findByMatricula(matricula, codNivelMgb);
        response.getWriter().append(aluno.toString());
    }

    private void buscarIdPublicMgb(HttpServletResponse response, Integer matricula, MgbService mgbService) throws Exception {
        String codNivelMgb = mgbService.verificarPublicidmgb(matricula);
        response.getWriter().append(codNivelMgb);
    }

    private void buscarTokencMgb(HttpServletResponse response, Integer empresa, MgbService mgbService) throws Exception {
        String token = mgbService.tokenIntegracao(empresa);
        response.getWriter().append(token);
    }

    private void inserirPublicMgb(HttpServletRequest request, HttpServletResponse response, Integer matricula,
                                  String publicIdMgb, MgbService mgbService) throws Exception {
//        String body = IOUtils.toString(request.getInputStream());
//        mgbService.alterarpublicidmgb(matricula, publicIdMgb, body);
//        response.getWriter().append("sucesso");
    }

    private void sincronizarAlunosAtivos(HttpServletResponse response, Integer empresa, MgbService mgbService) throws Exception {
        String retorno = mgbService.sincronizarAlunosAtivos(empresa);
        response.getWriter().append(retorno);
    }

    private void syncHorarioTurmaMgb(HttpServletResponse response, String chave, Integer empresa, MgbService mgbService, Integer codHorarioTurma) throws Exception {
        String retorno = mgbService.syncHorarioTurmaMgb(chave, empresa, codHorarioTurma, true, 0);
        response.getWriter().append(retorno);
    }

    private void syncTurmaMgb(HttpServletResponse response, String chave, Integer empresa, MgbService mgbService, Integer codTurma) throws Exception {
        String retorno = mgbService.syncTurmaMgb(chave, empresa, codTurma, true, 0);
        response.getWriter().append(retorno);
    }

    private void syncTodasTurmasComMgb(HttpServletResponse response, Integer empresa, String ctx, Boolean vaiValidarVagas, Integer margemMinutoInicioFimHorario, MgbService mgbService) throws Exception {
        String retorno = mgbService.syncTodasTurmasComMgb(empresa, ctx, vaiValidarVagas, margemMinutoInicioFimHorario);
        response.getWriter().append(retorno);
    }

    private void inativarTurmaMgb(HttpServletResponse response, Integer empresa, MgbService mgbService, Integer codigoHorarioTurma) throws Exception {
        String retorno = mgbService.inativarTurmaMgb(empresa, codigoHorarioTurma);
        response.getWriter().append(retorno);
    }

    private void deletarTurmaMgb(HttpServletResponse response, Integer empresa, MgbService mgbService, Integer codigoHorarioTurma) throws Exception {
        String retorno = mgbService.deletarTurmaMgb(empresa, codigoHorarioTurma);
        response.getWriter().append(retorno);
    }

    private void deletarTodasTurmasNoMgb(HttpServletResponse response, Integer empresa, MgbService mgbService, Boolean apenasValidar) throws Exception {
        String retorno = mgbService.deletarTodasTurmasNoMgb(empresa, apenasValidar);
        response.getWriter().append(retorno);
    }

    private void syncAlunoMgb(HttpServletResponse response, Integer empresa, MgbService mgbService, Integer codigoCliente) throws Exception {
        String retorno = mgbService.syncAlunoMgb(empresa, codigoCliente, null);
        if (retorno.toUpperCase().contains("ERRO")) {
            throw new Exception(retorno);
        }
        response.getWriter().append(retorno);
    }

    private void consultarDadosMgbAluno(HttpServletResponse response, Integer empresa, MgbService mgbService, Integer codigoCliente) throws Exception {
        JSONObject retorno = mgbService.consultarDadosMgbAluno(empresa, codigoCliente);
        response.getWriter().append(retorno.toString());
    }

    private void deletarPublicIdProfessor(HttpServletResponse response, Integer empresa, MgbService mgbService, Integer codigoColaborador) throws Exception {
        String retorno = mgbService.deletarPublicIdProfessor(empresa, codigoColaborador);
        response.getWriter().append(retorno);
    }

    private void removerUtilizacaoPiscina(HttpServletResponse response, MgbService mgbService, String publicIdPiscina) throws Exception {
        String retorno = mgbService.removerUtilizacaoPiscina(publicIdPiscina);
        response.getWriter().append(retorno);
    }

}

package servlet.indicadores;

import acesso.webservice.DaoAuxiliar;
import br.com.pactosolucoes.enumeradores.SituacaoClienteEnum;
import org.json.JSONArray;
import negocio.comuns.utilitarias.Uteis;
import negocio.interfaces.basico.ClienteInterfaceFacade;
import servlet.arquitetura.SuperServlet;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

public class IndicadoresClienteServlet extends SuperServlet {

    private ClienteInterfaceFacade getClienteDao() throws Exception {
        return DaoAuxiliar.retornarAcessoControle(getChave()).getClienteDao();
    }

    @Override
    protected void processRequest(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        super.processRequest(request, response);

        try {

            String situacao = request.getParameter("situacao") == null ? "" : request.getParameter("situacao");
            SituacaoClienteEnum situacaoEnum = SituacaoClienteEnum.getSituacaoCliente(situacao);

            JSONArray clientesJSON = getClienteDao().consultarIndicadoresJSON(getParametroEmpresa(), situacaoEnum, getParametroLimit(), getParametroOffset());
            int total = getClienteDao().consultarIndicadoresCount(getParametroEmpresa(), situacaoEnum, getParametroLimit(), getParametroOffset());

            response.setHeader("Total", String.valueOf(total));
            response.setHeader("Limite-por-pagina", String.valueOf(getParametroLimit()));
            response.setHeader("Pagina", String.valueOf(getParametroPagina()));
            response.setHeader("Empresa", String.valueOf(getParametroEmpresa()));

            response.getWriter().append(clientesJSON.toString());
        } catch (Exception e) {
            Uteis.logar(e, IndicadoresClienteServlet.class);
            processarErro(e, request, response, null);
        }

    }
}

package servlet.dadosgerenciais;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.financeiro.enumerador.IndicadoresDadosGerencialEnum;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.financeiro.DadosGerencialPmg;
import negocio.facade.jdbc.utilitarias.Conexao;
import org.json.JSONObject;
import servicos.dadosgerenciaispagamento.DadosGerenciaisPagamentoService;
import servlet.arquitetura.SuperServlet;

import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.sql.Connection;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 * User: <PERSON><PERSON>
 * Date: 02/03/2021
 * <p>
 * #########################################################################################
 * ######################################## ATENÇÃO ########################################
 * <p>
 * ESSA CLASSE FOI CRIADA COM O OBJETIVO DE CRIAR UM SERVLET GENERICO PARA USO DE ALGUMAS OPERACOES EXPECÍFICAS.
 * SÓ É ACEITO REQUISAÇÃO POST !
 * <p>
 * Deve-se criar a OPERAÇÃO que será enviada na requisição (parametro "op") e criar um método para essa operação onde será realizado a operação.
 * <p>
 * #########################################################################################
 * #########################################################################################
 */
public class DadosGerenciaisServlet extends SuperServlet {

    @Override
    protected void processRequest(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        try {
            response.addHeader("Access-Control-Allow-Origin", "*");
            response.addHeader("Access-Control-Allow-Methods", "GET,POST");
            response.setContentType("application/json");

            String operacao = request.getParameter("op");
            if (UteisValidacao.emptyString(operacao)) {
                throw new Exception("Operação não informada");
            }

            String chave = request.getParameter("chave");

            String retorno = "";
            switch (operacao) {
                case "DadosGerenciaisPagamento":
                    retorno = processarDadosGerenciaisPagamento(chave, request);
                    break;
                case "DadosGerenciaisPMG":
                    retorno = processarDadosGerenciaisPMG(chave, request);
                    break;
                case "AtualizarIndicadorDiarioMesInteiro":
                    retorno = processarDadosGerenciaisDiarioDoMes(chave, request);
                    break;
                case "outro":
                    break;
            }

            response.getWriter().append(this.toJSON(true, retorno).toString());
        } catch (Exception ex) {
            response.setStatus(400);
            response.getWriter().append(this.toJSON(false, ex.getMessage()).toString());
        }
    }

    private JSONObject toJSON(boolean sucesso, String mensagem) {
        JSONObject json = new JSONObject();
        json.put("sucesso", sucesso);
        json.put("msg", mensagem);
        return json;
    }

    private String processarDadosGerenciaisPagamento(String chave, HttpServletRequest request) throws Exception {
        DadosGerenciaisPagamentoService service = null;
        try {
            String dia = request.getParameter("mes");
            if (UteisValidacao.emptyString(dia)) {
                throw new Exception("mes não informado");
            }
            service = new DadosGerenciaisPagamentoService(chave);
            service.processaDados(Calendario.getDate("yyyyMMdd", dia));
            return "ok";
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        } finally {
            service = null;
        }
    }

    private Connection obterConexao(ServletRequest request, String chave) throws Exception {
        if (!UteisValidacao.emptyString(chave)) {
            return new DAO().obterConexaoEspecifica(chave.trim());
        }

        String key = request.getParameter("key");
        if (UteisValidacao.emptyString(key)) {
            return new DAO().obterConexaoEspecifica(key.trim());
        }
        String chaveP = request.getParameter("chave");
        if (UteisValidacao.emptyString(chaveP)) {
            return new DAO().obterConexaoEspecifica(chaveP.trim());
        }
        throw new Exception("Chave não informada.");
    }

    private void finalizarConexao(Connection con) {
        try {
            if (con != null) {
                con.close();
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    private String processarDadosGerenciaisPMG(String chave, HttpServletRequest request) throws Exception {
        Connection con = null;
        DadosGerencialPmg dadosGerencialPmg = null;
        try {

            if (UteisValidacao.emptyString(chave)) {
                throw new Exception("Chave não informada");
            }

            String tipo = request.getParameter("tipo");
            if (UteisValidacao.emptyString(tipo)) {
                throw new Exception("Tipo não informado");
            }
            if (!tipo.equalsIgnoreCase("dia") &&
                    !tipo.equalsIgnoreCase("mes")) {
                throw new Exception("Tipo não implementado | " + tipo);
            }

            String dia = request.getParameter("dia");
            Date dataBase = Calendario.hoje();
            if (!UteisValidacao.emptyString(dia)) {
                dataBase = Calendario.getDate("yyyyMMdd", dia);
            }
            Integer empresa = UteisValidacao.converterInteiro(request.getParameter("emp"));

            String indicadoresStr = request.getParameter("indicadores");
            List<IndicadoresDadosGerencialEnum> indicadores = new ArrayList<>();
            if (!UteisValidacao.emptyString(indicadoresStr)) {
                for (String ind : indicadoresStr.split("\\|")) {
                    IndicadoresDadosGerencialEnum indEnum = IndicadoresDadosGerencialEnum.getIndicadorPorSigla(ind);
                    if (indEnum != null) {
                        indicadores.add(indEnum);
                    }
                }
            }

            con = obterConexao(request, chave);

            dadosGerencialPmg = new DadosGerencialPmg(con);
            Conexao.guardarConexaoForJ2SE(con);
            if (tipo.equalsIgnoreCase("mes")) {
                dadosGerencialPmg.gerarDadosPMG(empresa, chave, dataBase, indicadores);
            } else if (tipo.equalsIgnoreCase("dia")) {
                dadosGerencialPmg.gerarDadosDiaPMG(empresa, chave, dataBase, indicadores);
            }
            return "ok";
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        } finally {
            dadosGerencialPmg = null;
            finalizarConexao(con);
        }
    }

    private String processarDadosGerenciaisDiarioDoMes(String chave, HttpServletRequest request) throws Exception {
        Connection con = null;
        DadosGerencialPmg dadosGerencialPmg = null;
        try {

            if (UteisValidacao.emptyString(chave)) {
                throw new Exception("Chave não informada");
            }

            Integer mes = 0;
            try {
                mes = Integer.parseInt(request.getParameter("mes"));
            } catch (Exception e) {
                throw new Exception("Informe corretamente o mês");
            }
            Integer ano = 0;
            try {
                ano = Integer.parseInt(request.getParameter("ano"));
            } catch (Exception e) {
                throw new Exception("Informe corretamente o ano");
            }
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(Calendario.hoje());
            if(mes < 1 || mes > 12 || ano < 2000 || ano > calendar.get(Calendar.YEAR)){
                throw new Exception("Informe corretamente o mês (entre 1 e 12) e o ano(2000 e o ano atual)");
            }

            String indicadoresStr = request.getParameter("indicadores");
            List<IndicadoresDadosGerencialEnum> indicadores = new ArrayList<>();
            if (!UteisValidacao.emptyString(indicadoresStr)) {
                for (String ind : indicadoresStr.split("\\|")) {
                    IndicadoresDadosGerencialEnum indEnum = IndicadoresDadosGerencialEnum.getIndicadorPorSigla(ind);
                    if (indEnum != null) {
                        indicadores.add(indEnum);
                    }
                }
            }

            Integer empresa = UteisValidacao.converterInteiro(request.getParameter("emp"));

            con = obterConexao(request, chave);
            dadosGerencialPmg = new DadosGerencialPmg(con);

            calendar.set(Calendar.YEAR, ano);
            calendar.set(Calendar.MONTH, mes - 1); // Mês é base 0 (janeiro = 0, fevereiro = 1, ..., dezembro = 11)
            calendar.set(Calendar.DAY_OF_MONTH, 1);

            while (calendar.get(Calendar.MONTH) == (mes - 1)) {
                Date dataBase = calendar.getTime();
                if(Calendario.menorOuIgual(dataBase, Calendario.hoje())) {
                    dadosGerencialPmg.gerarDadosDiaPMG(empresa, chave, dataBase, indicadores);
                }
                calendar.add(Calendar.DAY_OF_MONTH, 1);
            }

            return "ok";
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        } finally {
            dadosGerencialPmg = null;
            finalizarConexao(con);
        }
    }
}

package servlet.integracao;

import br.com.pactosolucoes.gympass.CheckinGympassService;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.basico.Empresa;
import org.json.JSONObject;
import servlet.arquitetura.SuperServlet;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.sql.Connection;

public class GympassCheckinServlet extends SuperServlet {

    @Override
    protected void processRequest(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        try {
            response.addHeader("Access-Control-Allow-Origin", "*");
            response.addHeader("Access-Control-Allow-Methods", "POST");
            response.setContentType("application/json");

            String method = request.getMethod();

            if (!method.equalsIgnoreCase("POST")) {
                throw new Exception("Methodo não suportado para esse recurso");
            }

            String path = request.getServletPath();
            String[] pathParts = path.split("/");
            String recurso = pathParts[3];
            try {
                switch (recurso){
                    case "gymid":
                        gymid(request, response);
                        break;
                    case "checkin":
                        checkin(request, response, true, false);
                        break;
                    case "checkinAcessControl":
                        checkin(request, response, true, false);
                        break;
                    case "checkinRemoveAcessControl":
                        checkin(request, response, true, true);
                        break;
                }
            }catch (Exception e ){
                System.out.println("Erro na api rest "+recurso+". Message: "+e.getMessage());
                processarErro(e, request, response, null);
            }
        } catch (Exception ex) {
            response.setStatus(400);
            response.getWriter().append(this.toJSON(false, ex.getMessage()).toString());
        }
    }

    private void checkin(HttpServletRequest request, HttpServletResponse response, boolean control, boolean remover){
        try {
            InputStream inputStream = request.getInputStream();
            BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream, StandardCharsets.UTF_8));

            StringBuffer body = new StringBuffer();
            String line = null;

            while ((line = reader.readLine()) != null) {
                body.append(line);
            }
            String key = request.getParameter("key");
            Integer empresa = Integer.valueOf(request.getParameter("empresa"));

            // VALIDAÇÃO NECESSÁRIA POIS O CODIGO DA EMPRESA ESTAVA VINDO INCORRETO EM ALGUMAS VEZES QUANDO É MULTI UNIDADE, NECESSÁRIO OBTER A EMPRESA PELO GYMID;
            try {
                JSONObject objJson = new JSONObject(body.toString());
                Integer gymId = objJson.getJSONObject("event_data").getJSONObject("gym").getInt("id");
                Integer codEmpresaZW = obterEmpresaPorGymId(request, gymId);
                empresa = codEmpresaZW != null && codEmpresaZW > 0 ? codEmpresaZW : empresa;
            } catch (Exception ex) {
                Uteis.logar(ex, this.getClass());
            }

            CheckinGympassService checkinGympassService = new CheckinGympassService();
            String retorno = checkinGympassService.checkInGymPassValidarAcesso(key, body.toString(), empresa, remover);
            response.getWriter().append(this.toJSON(!retorno.startsWith("ERRO"), retorno).toString());
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void gymid(HttpServletRequest request, HttpServletResponse response) {
        String key = request.getParameter("key");
        try (Connection con = new DAO().obterConexaoEspecifica(key)) {
            Empresa dao = new Empresa(con);
            response.getWriter().append(dao.gymIds().toString());
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private Integer obterEmpresaPorGymId(HttpServletRequest request, Integer gymId){
        String key = request.getParameter("key");
        try (Connection con = new DAO().obterConexaoEspecifica(key);) {
            Empresa dao = new Empresa(con);
            return dao.obterCodEmpresaPorGymID(gymId.toString());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    private JSONObject toJSON(boolean sucesso, String mensagem) {
        JSONObject json = new JSONObject();
        json.put("sucesso", sucesso);
        json.put("msg", mensagem);
        return json;
    }

}

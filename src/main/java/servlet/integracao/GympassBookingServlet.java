package servlet.integracao;

import br.com.pactosolucoes.comuns.json.JSONMapper;
import br.com.pactosolucoes.gympass.GymPassBookingZWJSON;
import br.com.pactosolucoes.gympass.TurmaGymPassJSON;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.arquitetura.LogVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.plano.TurmaVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.Log;
import negocio.facade.jdbc.basico.Cliente;
import negocio.facade.jdbc.basico.Colaborador;
import negocio.facade.jdbc.basico.Empresa;
import negocio.facade.jdbc.plano.Turma;
import negocio.modulos.integracao.usuariomovel.UsuarioMovel;
import negocio.modulos.integracao.usuariomovel.UsuarioMovelVO;
import org.json.JSONArray;
import org.json.JSONObject;
import servlet.arquitetura.SuperServlet;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.sql.Connection;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 * User: Luiz Felipe
 * Date: 06/04/2020
 *
 */
public class GympassBookingServlet extends SuperServlet {

    @Override
    protected void processRequest(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        try {
            response.addHeader("Access-Control-Allow-Origin", "*");
            response.addHeader("Access-Control-Allow-Methods", "GET");
            response.setContentType("application/json");

            String method = request.getMethod();

            if (!method.equalsIgnoreCase("POST")) {
                throw new Exception("Methodo não suportado para esse recurso");
            }

            GymPassBookingZWJSON gymPassBookingZWJSON = null;
            try {
                InputStream inputStream = request.getInputStream();
                BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream, StandardCharsets.UTF_8));

                StringBuffer body = new StringBuffer();
                String line = null;

                while ((line = reader.readLine()) != null) {
                    body.append(line);
                }

                JSONObject jsonBody = new JSONObject(body.toString());
                gymPassBookingZWJSON = JSONMapper.getObject(jsonBody, GymPassBookingZWJSON.class);
            } catch (Exception e) {
                e.printStackTrace();
            }


            if (gymPassBookingZWJSON == null) {
                throw new Exception("JSON não encontrado.");
            }


            String retorno = "";
            switch (gymPassBookingZWJSON.getOperacao()) {
                case "consultaTurma":
                    retorno = obterDadosTurma(gymPassBookingZWJSON);
                    break;
                case "atualizar":
                    retorno = atualizar(gymPassBookingZWJSON);
                    break;
                case "consultaAluno":
                    retorno = consultarAluno(gymPassBookingZWJSON);
                    break;
                case "turmas-id-classes":
                    JSONArray array = dadosId(gymPassBookingZWJSON.getChave(), gymPassBookingZWJSON.getEmpresaZw());
                    retorno = array.toString();
                    break;
            }

            response.getWriter().append(this.toJSON(true, retorno).toString());
        } catch (Exception ex) {
            response.setStatus(400);
            response.getWriter().append(this.toJSON(false, ex.getMessage()).toString());
        }
    }

    private JSONObject toJSON(boolean sucesso, String mensagem) {
        JSONObject json = new JSONObject();
        json.put("sucesso", sucesso);
        json.put("msg", mensagem);
        return json;
    }

    private String obterDadosTurma(GymPassBookingZWJSON obj) throws Exception {
        Connection con = null;
        Turma turmaDAO = null;
        try {
            con = new DAO().obterConexaoEspecifica(obj.getChave());

            turmaDAO = new Turma(con);
            TurmaVO turmaVO = null;
            if (!UteisValidacao.emptyNumber(obj.getTurma())) {
                turmaVO = turmaDAO.consultarPorChavePrimaria(obj.getTurma(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            } else if (!UteisValidacao.emptyNumber(obj.getProdutoGymPass())) {
                turmaVO = turmaDAO.consultarUltimoCadastradoPorProdutoGymPass(obj.getProdutoGymPass(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            }
            if (turmaVO == null) {
                throw new Exception("Turma não encontrada");
            }
            return new JSONObject(new TurmaGymPassJSON(turmaVO)).toString();
        } catch (Exception ex) {
            throw ex;
        } finally {
            turmaDAO = null;
            if (con != null) {
                con.close();
            }
        }
    }

    private String atualizar(GymPassBookingZWJSON obj) throws Exception {
        Connection con = null;
        Turma turmaDAO = null;
        try {
            con = new DAO().obterConexaoEspecifica(obj.getChave());
            turmaDAO = new Turma(con);
            turmaDAO.alterarIdClasseGymPass(obj.getIdClasseGymPass(), obj.getTurma());
            return "ok";
        } catch (Exception ex) {
            throw ex;
        } finally {
            turmaDAO = null;
            if (con != null) {
                con.close();
            }
        }
    }

    private JSONArray dadosId(String chave, Integer empresa) throws Exception {
        Connection con = null;
        Turma turmaDAO = null;
        try {
            con = new DAO().obterConexaoEspecifica(chave);
            turmaDAO = new Turma(con);
            return turmaDAO.aulasCodigos(empresa);
        } catch (Exception ex) {
            throw ex;
        } finally {
            turmaDAO = null;
            if (con != null) {
                con.close();
            }
        }
    }

    public ClienteVO gerarAluno(String chave,
                                Integer empresa,
                                String token, String nome,
                                String telefone, String email) throws Exception {
        Connection con = null;
        Cliente clienteDAO = null;
        Colaborador colaboradorDAO = null;
        Empresa empresaDAO = null;
        UsuarioMovel usuarioMovelDAO = null;
        try {
            con = new DAO().obterConexaoEspecifica(chave);
            usuarioMovelDAO = new UsuarioMovel(con);
            empresaDAO = new Empresa(con);
            clienteDAO = new Cliente(con);
            colaboradorDAO = new Colaborador(con);

            GymPassBookingZWJSON obj = new GymPassBookingZWJSON();
            obj.setChave(chave);
            obj.setClienteEmail(email);
            obj.setClienteNome(nome);
            obj.setClienteTelefone(telefone);
            obj.setEmpresaZw(empresa);
            obj.setClienteUniqueToken(token);
            return inserirAlunoGympass(obj, con, clienteDAO, colaboradorDAO, empresaDAO);
        }catch (Exception e){
            throw e;
        } finally {
            usuarioMovelDAO = null;
            clienteDAO = null;
            colaboradorDAO = null;
            empresaDAO = null;
            if (con != null) {
                con.close();
            }
        }
    }

    private String consultarAluno(GymPassBookingZWJSON obj) throws Exception {
        Connection con = null;
        Cliente clienteDAO = null;
        Colaborador colaboradorDAO = null;
        Empresa empresaDAO = null;
        UsuarioMovel usuarioMovelDAO = null;
        JSONObject jsonRetorno = new JSONObject();
        String msg = null;
        try {
            con = new DAO().obterConexaoEspecifica(obj.getChave());

            usuarioMovelDAO = new UsuarioMovel(con);
            empresaDAO = new Empresa(con);
            clienteDAO = new Cliente(con);
            colaboradorDAO = new Colaborador(con);
            Integer codigoCliente = clienteDAO.consultarCodigoClientePorGympassUniqueToken(obj.getClienteUniqueToken(), obj.getEmpresaZw());
            if (codigoCliente != null) {

                jsonRetorno.put("cliente", codigoCliente.toString());

                ClienteVO clienteVO = new ClienteVO();
                clienteVO.setCodigo(codigoCliente);

                UsuarioMovelVO usuarioMovelVO = usuarioMovelDAO.consultarPorCliente(clienteVO, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                if (usuarioMovelVO == null || UteisValidacao.emptyNumber(usuarioMovelVO.getCodigo())) {
                    try {
                        usuarioMovelVO = clienteDAO.gerarUsuarioMovelAluno(obj.getChave(), codigoCliente, "", obj.getClienteEmail());
                        if (usuarioMovelVO == null || UteisValidacao.emptyNumber(usuarioMovelVO.getCodigo())) {
                            msg = "Não importado para TreinoWeb";
                        } else {
                            msg = "sucesso";
                        }
                    } catch (Exception ex) {
                        msg = "Não importado para TreinoWeb " + ex.getMessage();
                    }
                } else {
                    msg = "sucesso";
                }
                jsonRetorno.put("msg", msg);
                return jsonRetorno.toString();
            } else {
                ClienteVO clienteVO = inserirAlunoGympass(obj, con, clienteDAO, colaboradorDAO, empresaDAO);
                jsonRetorno.put("cliente", clienteVO.getCodigo().toString());

                try {
                    UsuarioMovelVO usuarioMovelVO = clienteDAO.gerarUsuarioMovelAluno(obj.getChave(), clienteVO.getCodigo(), "", obj.getClienteEmail());
                    if (usuarioMovelVO == null || UteisValidacao.emptyNumber(usuarioMovelVO.getCodigo())) {
                        msg = "Não importado para TreinoWeb";
                    } else {
                        msg = "sucesso";
                    }
                } catch (Exception ex) {
                    msg = "Não importado para TreinoWeb " + ex.getMessage();
                }

                jsonRetorno.put("msg", msg);
                return jsonRetorno.toString();
            }
        } catch (Exception ex) {
            throw ex;
        } finally {
            usuarioMovelDAO = null;
            clienteDAO = null;
            colaboradorDAO = null;
            empresaDAO = null;
            if (con != null) {
                con.close();
            }
        }
    }

    private ClienteVO inserirAlunoGympass(GymPassBookingZWJSON obj,
                                          Connection con,
                                          Cliente clienteDAO, Colaborador colaboradorDAO, Empresa empresaDAO) throws Exception {
        List<ColaboradorVO> listaColaborador = colaboradorDAO.consultarPorNomePessoa("PACTO", obj.getEmpresaZw(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        if (UteisValidacao.emptyList(listaColaborador)) {
            listaColaborador = colaboradorDAO.consultarPorCodigoEmpresa(obj.getEmpresaZw(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        }

        if (UteisValidacao.emptyList(listaColaborador)) {
            throw new Exception("Colaborador não encontrado");
        }

        ColaboradorVO colaboradorVO = listaColaborador.get(0);
        EmpresaVO empresaVO = empresaDAO.consultarPorChavePrimaria(obj.getEmpresaZw(), Uteis.NIVELMONTARDADOS_MINIMOS);

        //não encontrou vou cadastrar
        String telefoneCliente = null;
        if (!UteisValidacao.emptyString(obj.getClienteTelefone())) {
            String numero = obj.getClienteTelefone();
            if (numero.startsWith("+55") || numero.startsWith("55")) {
                numero = numero.replaceFirst("\\+55", "").replaceFirst("55", "");
            }
            telefoneCliente = Uteis.aplicarMascara(numero, "(99)999999999");
        }

        ClienteVO clienteVO = clienteDAO.incluirClienteViaWebService(colaboradorVO, empresaVO, obj.getClienteNome(), obj.getClienteEmail(), null, telefoneCliente);
        if(clienteVO == null || UteisValidacao.emptyNumber(clienteVO.getCodigo())) {
            throw new Exception("Cliente não cadastrado");
        }
        incluirLog(clienteVO.getCodigo(), "unique_token: " + obj.getClienteUniqueToken(), "INCLUSAO", con);
        clienteDAO.alterarGymPassUniqueToken(obj.getClienteUniqueToken(), clienteVO.getCodigo());
        return clienteVO;
    }

    public void incluirLog(Integer cliente, String msg, String op, Connection con) {
        LogVO obj = new LogVO();
        obj.setChavePrimaria(cliente.toString());
        obj.setNomeEntidade("CLIENTE");
        obj.setNomeEntidadeDescricao("Cliente");
        obj.setOperacao(op);
        obj.setResponsavelAlteracao("BOOKING_GYMPASS");
        obj.setNomeCampo("TODOS");
        obj.setDataAlteracao(Calendario.hoje());
        obj.setValorCampoAnterior("");
        obj.setValorCampoAlterado(msg);
        Log logDAO = null;
        try {
            logDAO = new Log(con);
            logDAO.incluirSemCommit(obj);
        } catch (Exception ex) {
            ex.printStackTrace();
        } finally {
            logDAO = null;
        }
    }
}

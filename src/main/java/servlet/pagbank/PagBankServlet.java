package servlet.pagbank;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.arquitetura.EnvelopeRespostaDTO;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.financeiro.ConvenioCobranca;
import org.json.JSONObject;
import servicos.impl.pagbank.PagBankService;
import servlet.arquitetura.SuperServlet;

import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.sql.Connection;

/**
 * Created with IntelliJ IDEA.
 * User: <PERSON>
 * Date: 20/10/2024
 */

public class PagBankServlet extends SuperServlet {
    @Override
    protected void processRequest(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        try {
            response.setHeader("Access-Control-Allow-Origin", "*");
            response.setHeader("Access-Control-Allow-Methods", "GET,POST");
            response.setHeader("Access-Control-Allow-Headers", "*");
            response.setContentType("application/json");

            String operacao = request.getParameter("op");
            if (UteisValidacao.emptyString(operacao)) {
                throw new Exception("operacao 'op' não informada na url.");
            }

            String key = request.getParameter("chave");
            if (UteisValidacao.emptyString(key)) {
                throw new Exception("Chave não informada na url.");
            }
            EnvelopeRespostaDTO envelopeRespostaDTO;
            if (operacao.equalsIgnoreCase("Auth")) {
                String body = obterBody(request);
                JSONObject jsonObject = new JSONObject(body);
                String chave = jsonObject.optString("chave");
                String convenio = jsonObject.optString("convenio");
                String code = jsonObject.optString("code");
                validarParametrosVaziosObrigatorios(chave, convenio, code);

                //primeiro salvar o authorization code
                String accessToken = gerarAccessTokenAuthorizationCode(chave, Integer.parseInt(convenio), code);
                JSONObject json = new JSONObject(accessToken);
                String access_Token = json.getString("access_token");
                String refresh_Token = json.getString("refresh_token");
                incluirInformacoesAccessTokenPagBank(chave, access_Token, code, refresh_Token, Integer.parseInt(convenio));
                envelopeRespostaDTO = EnvelopeRespostaDTO.of("sucesso");
            } else {
                envelopeRespostaDTO = EnvelopeRespostaDTO.of("Operação não encontrada");
            }
            response.getWriter().append(new JSONObject(envelopeRespostaDTO).toString());
        } catch (Exception ex) {
            EnvelopeRespostaDTO envelopeRespostaDTO;
            envelopeRespostaDTO = EnvelopeRespostaDTO.of("Erro ao salvar o Authorization Code" + ex.getMessage());
            response.getWriter().append(new JSONObject(envelopeRespostaDTO).toString());
        }
    }

    public String gerarAccessTokenAuthorizationCode(String chave, int convenio, String code) throws Exception {
        Connection con;
        PagBankService service;
        try {
            con = new DAO().obterConexaoEspecifica(chave);
            service = new PagBankService(con, convenio, true);
            return service.gerarAccessTokenConnect(code);
        } catch (Exception ex) {
            throw ex;
        } finally {
            con = null;
            service = null;
        }
    }

    public void incluirInformacoesAccessTokenPagBank(String chave, String accessToken, String authorizationCode, String refreshToken, int codConvenio) throws Exception {
        Connection con;
        ConvenioCobranca convenioCobrancaDAO;
        try {
            con = new DAO().obterConexaoEspecifica(chave);
            convenioCobrancaDAO = new ConvenioCobranca(con);
            convenioCobrancaDAO.incluirInformacoesAccessTokenPagBank(accessToken, authorizationCode, refreshToken, codConvenio);
        } catch (Exception ex) {
            throw ex;
        } finally {
            con = null;
            convenioCobrancaDAO = null;
        }
    }

    public void validarParametrosVaziosObrigatorios(String... parametros) {
        for (String parametro : parametros) {
            if (UteisValidacao.emptyString(parametro)) {
                throw new IllegalArgumentException("Parâmetro obrigatório '" + parametro + "' não informado");
            }
        }
    }

    private String obterBody(ServletRequest request) throws Exception {
        StringBuffer body = new StringBuffer();
        try {
            InputStream inputStream = request.getInputStream();
            BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream, StandardCharsets.UTF_8));
            String line = null;

            while ((line = reader.readLine()) != null) {
                body.append(line);
            }
            return body.toString();
        } catch (Exception ex) {
            ex.printStackTrace();
            throw new Exception("Erro body: " + ex.getMessage());
        }
    }
}

package servlet.botConversa;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import negocio.comuns.arquitetura.EnvelopeRespostaDTO;
import negocio.comuns.financeiro.PluggyConnectorDTO;
import negocio.comuns.financeiro.PluggyItemVO;
import negocio.comuns.utilitarias.UteisValidacao;
import java.lang.reflect.Type;
import org.json.JSONObject;
import servicos.impl.pluggy.PluggyService;
import servicos.integracao.impl.integracaoBotConversaService.IntegracaoBotConversaService;
import servlet.arquitetura.SuperServlet;

import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.sql.Connection;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * Created with IntelliJ IDEA.
 * User: Rodrigo Estulano
 * Date: 12/07/2023
 */

public class BotConversaServlet extends SuperServlet {

    @Override
    protected void processRequest(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        try {
            response.setHeader("Access-Control-Allow-Origin", "*");
            response.setHeader("Access-Control-Allow-Methods", "GET,POST");
            response.setHeader("Access-Control-Allow-Headers", "*");
            response.setContentType("application/json");

            String operacao = request.getParameter("op");
            if (UteisValidacao.emptyString(operacao)) {
                throw new Exception("operacao 'op' não informada na url.");
            }

            String key = request.getParameter("key");
            if (UteisValidacao.emptyString(key)) {
                throw new Exception("Chave não informada na url.");
            }

            EnvelopeRespostaDTO envelopeRespostaDTO;
            if (operacao.equalsIgnoreCase("dispararFluxo")) {
                envelopeRespostaDTO = dispararFluxo(request);
            } else if (operacao.equalsIgnoreCase("dispararFluxoGymbotPro")){
                envelopeRespostaDTO = dispararFluxoGymbotPro(request);
            }else {
                throw new Exception("Nenhuma operação executada");
            }

            response.setStatus(HttpServletResponse.SC_OK);
            response.getWriter().append(new JSONObject(envelopeRespostaDTO).toString());
        } catch (Exception ex) {
            ex.printStackTrace();
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            response.getWriter().append(new JSONObject(EnvelopeRespostaDTO.erro("Erro", ex.getMessage())).toString());
        }
    }

    private EnvelopeRespostaDTO dispararFluxo(ServletRequest request) throws Exception {
        Connection con;
        IntegracaoBotConversaService service = null;
        try {
            con = obterConexao(request);

            String jsonString = obterBody(request);
            Gson gson = new Gson();
            Type type = new TypeToken<Map<String, String>>(){}.getType();
            Map<String, String> map = gson.fromJson(jsonString, type);

            service = new IntegracaoBotConversaService(con);

            //agora gerar connect token usando apikey
            return EnvelopeRespostaDTO.of(service.dispararFluxoGymbot(map));

        } catch (Exception ex) {
            throw new Exception("Não foi possível obter o token: " + ex.getMessage());
        } finally {
            con = null;
            service = null;
        }
    }

    private EnvelopeRespostaDTO dispararFluxoGymbotPro(ServletRequest request) throws Exception {
        Connection con;
        IntegracaoBotConversaService service = null;
        try {
            con = obterConexao(request);

            String jsonString = obterBody(request);
            Gson gson = new Gson();
            Type type = new TypeToken<Map<String, String>>(){}.getType();
            Map<String, String> map = gson.fromJson(jsonString, type);

            service = new IntegracaoBotConversaService(con);

            //agora gerar connect token usando apikey
            return EnvelopeRespostaDTO.of(service.dispararFluxoGymbotPro(map));

        } catch (Exception ex) {
            throw new Exception("Não foi possível obter o token: " + ex.getMessage());
        } finally {
            con = null;
            service = null;
        }
    }

    private Connection obterConexao(ServletRequest request) throws Exception {
        String key = request.getParameter("key");
        return new DAO().obterConexaoEspecifica(key.trim());
    }

    private String obterBody(ServletRequest request) throws Exception {
        StringBuffer body = new StringBuffer();
        try {
            InputStream inputStream = request.getInputStream();
            BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream, StandardCharsets.UTF_8));
            String line = null;

            while ((line = reader.readLine()) != null) {
                body.append(line);
            }
            return body.toString();
        } catch (Exception ex) {
            ex.printStackTrace();
            throw new Exception("Erro body: " + ex.getMessage());
        }
    }

}

package servlet.arquitetura;

import controle.arquitetura.security.LoginControle;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;

/**
 * Created with IntelliJ IDEA.
 * User: franciscoanjos
 * Date: 04/09/13
 * Time: 11:07
 * To change this template use File | Settings | File Templates.
 */

public class UsuarioServlet extends SuperServlet {
    @Override
    protected void processRequest(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        super.processRequest(request, response);
        PrintWriter out = response.getWriter();
        String json;
        try {
            String situacao = obterParametroString(request.getParameter("situacao"));
            String empresa = obterParametroString(request.getParameter("empresa"));
            json = ff(request).getUsuario().consultarJSON(Integer.parseInt(empresa), situacao);
        } catch (Exception e) {
            json = (e.getMessage() == null) ? NullPointerException.class.getName() : e.getMessage();
        }
        out.println(json);
        json = null;
    }
}
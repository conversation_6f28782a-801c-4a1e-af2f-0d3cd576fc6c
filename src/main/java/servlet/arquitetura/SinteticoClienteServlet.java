package servlet.arquitetura;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.arquitetura.EnvelopeRespostaDTO;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.plano.Modalidade;
import org.json.JSONObject;

import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.sql.Connection;

/**
 * #########################################################################################
 * ######################################## ATENÇÃO ########################################
 * <p>
 * ESSA CLASSE FOI CRIADA COM O OBJETIVO DE CRIAR UM SERVLET GENERICO PARA ATUALIZAÇÕES RELACIONADAS AO SINTÉTICO DO CLIENTE ENQUANTO NÃO HÁ UM MS PARA O SINTÉTICO AINDA
 * <p>
 * SÓ É ACEITO REQUISAÇÃO GET,POST !
 * <p>
 * Deve-se criar a OPERAÇÃO que será enviada na requisição (parametro "op") e criar um método para essa operação onde será realizado a operação.
 * <p>
 * #########################################################################################
 * #########################################################################################
 */
public class SinteticoClienteServlet extends SuperServlet {

    @Override
    protected void processRequest(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        Connection con = null;
        try {
            response.setHeader("Access-Control-Allow-Origin", "*");
            response.setHeader("Access-Control-Allow-Methods", "GET,POST");
            response.setHeader("Access-Control-Allow-Headers", "*");
            response.setContentType("application/json");
            request.setAttribute("servlet-relatorio", "true");
            if (request.getParameter("key") != null &&
                    !UteisValidacao.emptyString(request.getParameter("key"))) {
                request.setAttribute("servlet-chave", request.getParameter("key"));
            }

            String operacao = request.getParameter("op");
            if (UteisValidacao.emptyString(operacao)) {
                throw new Exception("Operação não informada");
            }

            con = obterConexao(request, null);

            EnvelopeRespostaDTO envelopeRespostaDTO;
            if (operacao.equalsIgnoreCase("atualizarAlunosCrossfit")) {
                envelopeRespostaDTO = atualizarAlunosCrossfit(request, con);
            } else {
                throw new Exception("Nenhuma operação executada");
            }

            response.setStatus(HttpServletResponse.SC_OK);
            response.getWriter().append(new JSONObject(envelopeRespostaDTO).toString());
        } catch (Exception ex) {
            ex.printStackTrace();
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            response.getWriter().append(new JSONObject(EnvelopeRespostaDTO.erro("Erro", ex.getMessage())).toString());
        } finally {
            finalizarConexao(con);
        }
    }

    private EnvelopeRespostaDTO atualizarAlunosCrossfit(ServletRequest request, Connection con) throws Exception {
        Modalidade modalidadeDao;
        try {
            modalidadeDao = new Modalidade(con);
            Integer codigoModalidade = Integer.valueOf(request.getParameter("codModalidade"));
            Boolean crossfit = Boolean.valueOf(request.getParameter("crossfit"));
            modalidadeDao.atualizarAlunosCrossfit(codigoModalidade, crossfit, obterChave(request));

            return EnvelopeRespostaDTO.of("Atualização das informações de crossfit realizada com sucesso");
        } finally {
            modalidadeDao = null;
        }
    }

    private Connection obterConexao(ServletRequest request, String chave) throws Exception {
        if (!UteisValidacao.emptyString(chave)) {
            return new DAO().obterConexaoEspecifica(chave.trim());
        }
        return new DAO().obterConexaoEspecifica(obterChave(request));
    }

    private String obterChave(ServletRequest request) throws Exception {
        String key = request.getParameter("key");
        if (UteisValidacao.emptyString(key)) {
            throw new Exception("Chave não informada.");
        }
        return key.trim();
    }

    private void finalizarConexao(Connection con) {
        try {
            if (con != null) {
                con.close();
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

}

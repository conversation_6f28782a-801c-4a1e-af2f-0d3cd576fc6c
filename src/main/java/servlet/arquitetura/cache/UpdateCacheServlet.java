package servlet.arquitetura.cache;

import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import controle.arquitetura.session.SessionTO;
import controle.arquitetura.session.listener.SessionState;
import negocio.comuns.arquitetura.EnvelopeRespostaDTO;
import negocio.comuns.basico.ConfiguracaoSistemaCadastroClienteVO;
import negocio.comuns.basico.ConfiguracaoSistemaVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.basico.ConfiguracaoSistema;
import negocio.facade.jdbc.basico.ConfiguracaoSistemaCadastroCliente;
import org.json.JSONObject;
import servlet.arquitetura.SuperServlet;

import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.io.IOException;
import java.sql.Connection;
import java.util.List;
import java.util.Map;

/**
 * #########################################################################################
 * ######################################## ATENÇÃO ########################################
 * <p>
 * ESSA CLASSE FOI CRIADA COM O OBJETIVO DE CRIAR UM SERVLET PARA ATUALIZAÇÃO DE CACHES DE SESSÃO DE USUÁRIO.
 * <p>
 * SÓ É ACEITO REQUISAÇÃO GET !
 * <p>
 * Deve-se criar a OPERAÇÃO que será enviada na requisição (parametro "op") e criar um método para essa operação onde será realizado a operação.
 * <p>
 * <p>
 * É obrigatório o envio do header X-ZW-JS-ID que é o token que contém o id da sessão
 * <p>
 * #########################################################################################
 * #########################################################################################
 */
public class UpdateCacheServlet extends SuperServlet {

    @Override
    protected void processRequest(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        Connection con = null;
        try {
            response.setHeader("Access-Control-Allow-Origin", "*");
            response.setHeader("Access-Control-Allow-Methods", "GET");
            response.setHeader("Access-Control-Allow-Headers", "*");
            response.setContentType("application/json");
            if (request.getParameter("key") != null &&
                    !UteisValidacao.emptyString(request.getParameter("key"))) {
                request.setAttribute("servlet-chave", request.getParameter("key"));
            }

            String zwJSId = request.getParameter("zwJSId");
            if (UteisValidacao.emptyString(zwJSId)) {
                response.setStatus(HttpServletResponse.SC_BAD_REQUEST);
                processarErro(new JSONObject(EnvelopeRespostaDTO.erro("JSessionId não informado", "É obrigatório enviar o JSessionId!")).toString(), response);
            } else {
                String operacao = request.getParameter("op");
                if (UteisValidacao.emptyString(operacao)) {
                    throw new Exception("Operação não informada");
                }

                con = obterConexao(request, null);

                zwJSId = Uteis.desencriptar(zwJSId, "chave_login_unificado");

                EnvelopeRespostaDTO envelopeRespostaDTO;
                if (operacao.equalsIgnoreCase("config-sistema-adm")) {
                    envelopeRespostaDTO = updateConfigSistema(request, con, zwJSId);
                } else {
                    throw new Exception("Nenhuma operação executada");
                }

                response.setStatus(HttpServletResponse.SC_OK);
                response.getWriter().append(new JSONObject(envelopeRespostaDTO).toString());
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            response.getWriter().append(new JSONObject(EnvelopeRespostaDTO.erro("Erro", ex.getMessage())).toString());
        } finally {
            finalizarConexao(con);
        }
    }

    private EnvelopeRespostaDTO updateConfigSistema(HttpServletRequest request, Connection con, String zwJSId) throws Exception {
        ConfiguracaoSistema configuracaoSistema;
        ConfiguracaoSistemaCadastroCliente configuracaoSistemaCadastroCliente;
        try {
            configuracaoSistema = new ConfiguracaoSistema(con);
            configuracaoSistemaCadastroCliente = new ConfiguracaoSistemaCadastroCliente(con);

            Map<String, SessionTO> sesisonStates = SessionState.getInstance();
            if (sesisonStates.containsKey(zwJSId)) {
                HttpSession session = sesisonStates.get(zwJSId).getSession();

                ConfiguracaoSistemaVO configuracaoSistemaVO = configuracaoSistema.consultarPorChavePrimaria(1, Uteis.NIVELMONTARDADOS_TODOS);
                List<ConfiguracaoSistemaCadastroClienteVO> listaConfCadCli = configuracaoSistemaCadastroCliente.consultar(false);
                configuracaoSistemaVO.setarDadosConfiguracaoCadastroCliente(listaConfCadCli);
                session.setAttribute(JSFUtilities.CONFIGURACAO_SISTEMA, configuracaoSistemaVO);
                session.setAttribute(JSFUtilities.CONF_SISTEMA, configuracaoSistemaVO);

                return EnvelopeRespostaDTO.of("Cache da configuração do sistema atualizado!");
            }

            return EnvelopeRespostaDTO.of("Não houve atualização do cache da configuração do sistema pois a sessão não foi encontrada!");
        } finally {
            configuracaoSistema = null;
        }
    }

    private Connection obterConexao(ServletRequest request, String chave) throws Exception {
        if (!UteisValidacao.emptyString(chave)) {
            return new DAO().obterConexaoEspecifica(chave.trim());
        }
        return new DAO().obterConexaoEspecifica(obterChave(request));
    }


    private String obterChave(ServletRequest request) throws Exception {
        String key = request.getParameter("key");
        if (UteisValidacao.emptyString(key)) {
            throw new Exception("Chave não informada.");
        }
        return key.trim();
    }

    private void finalizarConexao(Connection con) {
        try {
            if (con != null) {
                con.close();
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }
}

    package servlet.arquitetura;
    import br.com.pactosolucoes.oamd.controle.basico.DAO;
    import negocio.facade.jdbc.arquitetura.Robo;
    import negocio.facade.jdbc.crm.AberturaMeta;
    import org.json.JSONObject;

    import javax.servlet.ServletException;
    import javax.servlet.http.HttpServletRequest;
    import javax.servlet.http.HttpServletResponse;
    import java.io.IOException;

    public class RoboServlet extends SuperServlet {
        @Override
        protected void processRequest(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {

            response.addHeader("Access-Control-Allow-Origin", "*");
            response.addHeader("Access-Control-Allow-Methods", "GET");
            response.setContentType("application/json");

            String chave = request.getParameter("chave");

            String method = request.getMethod();
            try {
                switch (method) {
                    case "GET":
                            consultarExecucoesMeta(request, response, chave);
                        break;
                    default:
                        processarErro(
                                new UnsupportedOperationException("Methodo não suportado para esse recurso"),
                                request,
                                response,
                                null);
                }
            }catch (Exception e) {
                processarErro(e, request, response, null);
            }
        }

        private void consultarExecucoesMeta(HttpServletRequest request, HttpServletResponse response, String chave) throws Exception {
            JSONObject result = new JSONObject();
            result.put("ultimoDiaProcessado", getRobo(chave).consultaParaObterUltimoDiaprocessado());
            result.put("utlimaAberturaMeta",  getAberturaMeta(chave).consultarUltimaAberturaMeta());
            response.getWriter().append(result.toString());
        }

        private Robo getRobo(String chave) throws Exception {
            return new Robo(new DAO().obterConexaoEspecifica(chave));
        }

        private AberturaMeta getAberturaMeta(String chave) throws Exception {
            return new AberturaMeta(new DAO().obterConexaoEspecifica(chave));
        }
    }
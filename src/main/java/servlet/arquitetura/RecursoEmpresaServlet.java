package servlet.arquitetura;

import acesso.webservice.DaoAuxiliar;
import br.com.pactosolucoes.comuns.notificacao.RecursoSistema;
import controle.arquitetura.SuperControle;
import org.json.JSONObject;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

public class RecursoEmpresaServlet extends SuperServlet {
    @Override
    protected void processRequest(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        response.setHeader("Access-Control-Allow-Origin", "*");
        response.setHeader("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS");
        response.setHeader("Access-Control-Max-Age", "3600");
        response.setHeader("Access-Control-Allow-Headers", "content-type,accept,Authorization,empresaId");
        response.addHeader("Access-Control-Expose-Headers", "xsrf-token, accept");
        response.setContentType("application/json");

        String key = request.getParameter("chave");
        String recurso = request.getParameter("recurso");
        Integer empresaId = Integer.parseInt(request.getParameter("empresaId"));
        String usuario = request.getParameter("usuario");

        try {
            SuperControle.notificarRecursoEmpresa(DaoAuxiliar.retornarAcessoControle(key).getCon(),
                    key, RecursoSistema.fromDescricao(recurso),
                    String.valueOf(empresaId),
                    usuario);
            JSONObject objRetorno = new JSONObject();
            objRetorno.put("status", "success");
            response.getWriter().append(objRetorno.toString());
        } catch (Exception e) {
            processarErro(e, request, response, null);
        }
    }
}

package servlet.arquitetura;

import br.com.pactosolucoes.enumeradores.TipoTelefoneEnum;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.arquitetura.UsuarioEmailVO;
import negocio.comuns.arquitetura.UsuarioPerfilAcessoVO;
import negocio.comuns.arquitetura.UsuarioTO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.TelefoneVO;
import negocio.comuns.feed.PerfilUsuarioEnum;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.Permissao;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.facade.jdbc.arquitetura.UsuarioEmail;
import negocio.facade.jdbc.basico.Telefone;
import org.apache.commons.lang.StringEscapeUtils;
import org.json.JSONArray;
import org.json.JSONObject;
import servicos.usuario.UsuarioService;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.sql.Connection;
import java.util.List;
import java.util.stream.Collectors;

public class UsuarioServletV2 extends SuperServlet {
    @Override
    protected void processRequest(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {

        response.addHeader("Access-Control-Allow-Origin", "*");
        response.addHeader("Access-Control-Allow-Methods", "GET");
        response.setContentType("application/json");

        String chave = request.getParameter("chave");
        String codigo = getCodigoRecurso(request);
        String empresaId = request.getParameter("empresa");
        int empresa = 0;
        if (empresaId != null) {
            empresa = Integer.parseInt(empresaId);
        }

        String method = request.getMethod();
        try {
            switch (method) {
                case "GET":
                    if (codigo != null) {
                        obterUsuario(request, response, chave, Integer.parseInt(codigo), empresa);
                    } else {
                        consultarUsuarios(request, response, chave);
                    }
                    break;
                case "POST":
                    InputStream inputStream = request.getInputStream();
                    BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream , StandardCharsets.UTF_8));
                    JSONObject params = new JSONObject(reader.lines().collect(Collectors.joining(System.lineSeparator())));
                    try (Connection con = new DAO().obterConexaoEspecifica(chave);
                         UsuarioService usuarioService = new UsuarioService(con)) {

                        try {
                            UsuarioTO usuarioTO;
                            if (params.optBoolean("app")) {
                                usuarioTO = usuarioService.loginUsuarioSemSenha(chave, params.getString("username"));
                            } else {
                                usuarioTO = usuarioService.validarUsuario(chave, StringEscapeUtils.unescapeJava(params.getString("senha")), params.getString("username"));
                            }

                            response.getWriter().append(usuarioTO.toJSON());
                        } catch (Exception e) {
                            JSONObject json = new JSONObject();
                            json.put("invalido", true);
                            response.getWriter().append(json.toString());
                        }

                    }

                    break;
                default:
                    processarErro(
                            new UnsupportedOperationException("Methodo não suportado para esse recurso"),
                            request,
                            response,
                            null);
            }
        } catch (Exception e) {
            processarErro(e, request, response, null);
        }
    }

    private void obterUsuario(HttpServletRequest request, HttpServletResponse response, String chave, Integer codigo, Integer empresaId) throws Exception {
        try (Connection connection = new DAO().obterConexaoEspecifica(chave)) {
            Usuario usuarioDAO = new Usuario(connection);
            UsuarioEmail usuarioEmailDAO = new UsuarioEmail(connection);
            Permissao permissaoDAO = new Permissao(connection);

            UsuarioVO usuario = usuarioDAO.consultarPorCodigo(codigo, Uteis.NIVELMONTARDADOS_VALIDACAOACESSO);
            //email
            UsuarioEmailVO usuarioEmailVO = usuarioEmailDAO.consultarPorUsuario(usuario.getCodigo());
            usuario.setEmail(usuarioEmailVO.getEmail());
            //telefone
            TelefoneVO telefoneVO = obterTelefone(usuario.getColaboradorVO().getPessoa().getCodigo(), chave);
            usuario.setTelefone(telefoneVO);
            //permissões
            boolean permissaoFaturas = validarPermissao(permissaoDAO, "BoletosSistema", "4.31 - Boletos do Sistema", usuario, empresaId);
            response.getWriter().append(this.toJSON(usuario, permissaoFaturas).toString());
        }
    }

    private TelefoneVO obterTelefone(Integer pessoa, String chave) throws Exception {
        try (Connection connection = new DAO().obterConexaoEspecifica(chave)) {
            Telefone telefoneDAO = new Telefone(connection);

            List<TelefoneVO> lista = telefoneDAO.consultarTelefones(pessoa, false, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            if (UteisValidacao.emptyList(lista)) {
                return new TelefoneVO();
            } else {
                for (TelefoneVO tel : lista) {
                    if (tel.getTipoTelefone().equalsIgnoreCase(TipoTelefoneEnum.CELULAR.getCodigo())) {
                        return tel;
                    }
                }
                return lista.get(0);
            }
        }
    }

    private void consultarUsuarios(HttpServletRequest request, HttpServletResponse response, String chave) throws Exception {
        try (Connection connection = new DAO().obterConexaoEspecifica(chave)) {
            Usuario usuarioDAO = new Usuario(connection);

            JSONArray result = new JSONArray();

            List<UsuarioVO> usuarios = usuarioDAO.consultarAtivosSemAdministradorComTelefone();
            for (UsuarioVO usuario : usuarios) {
                result.put(this.toJSON(usuario, false));
            }

            response.getWriter().append(result.toString());
        }
    }

    private JSONObject toJSON(UsuarioVO usuario, boolean permissaoFaturas) {
        JSONObject usuarioJSON = new JSONObject();
        if (usuario != null) {
            usuarioJSON.put("codigo", usuario.getCodigo());
            usuarioJSON.put("nome", usuario.getNome());
            usuarioJSON.put("username", usuario.getUsername());
            usuarioJSON.put("tipoUsuario", usuario.getTipoUsuario());
            usuarioJSON.put("email", usuario.getEmail());
            usuarioJSON.put("administrador", usuario.getAdministrador());
            usuarioJSON.put("telefone", obterTelefoneJSON(usuario));

            JSONArray perfis = new JSONArray();
            JSONArray tipoPerfis = new JSONArray();
            for (Object perfil : usuario.getUsuarioPerfilAcessoVOs()) {
                UsuarioPerfilAcessoVO obj = (UsuarioPerfilAcessoVO) perfil;
                perfis.put(obj.getPerfilAcesso().getNome());
                try {
                    tipoPerfis.put(PerfilUsuarioEnum.getFromOrdinal(obj.getPerfilAcesso().getTipo().getId()).getNome());
                } catch (Exception ex) {
                    tipoPerfis.put("");
                }
            }
            usuarioJSON.put("perfis", perfis);
            usuarioJSON.put("tipoPerfis", tipoPerfis);
            usuarioJSON.put("cpf", usuario.getColaboradorVO().getPessoa().getCfp());
            usuarioJSON.put("permissaoFaturas", permissaoFaturas);
        }

        return usuarioJSON;
    }

    private JSONObject obterTelefoneJSON(UsuarioVO usuario) {
        JSONObject telefoneJSON = new JSONObject();
        telefoneJSON.put("numero", usuario.getTelefone().getNumero());
        telefoneJSON.put("tipotelefone", usuario.getTelefone().getTipoTelefone());
        telefoneJSON.put("descricao", usuario.getTelefone().getDescricao());
        telefoneJSON.put("ddi", usuario.getTelefone().getDdi());
        return telefoneJSON;
    }

    public boolean validarPermissao(Permissao permissaoDAO, String permissao, String descricao, UsuarioVO usuario, Integer empresaId) {
        if (usuario.getUsuarioPerfilAcessoVOs().isEmpty()) {
            return usuario.getAdministrador();
        }
        for (UsuarioPerfilAcessoVO usuarioPerfilAcesso : usuario.getUsuarioPerfilAcessoVOs()) {
            if (empresaId.equals(usuarioPerfilAcesso.getEmpresa().getCodigo())) {
                try {
                    usuarioPerfilAcesso.getPerfilAcesso().setPermissaoVOs(permissaoDAO.consultarPermissaos(usuarioPerfilAcesso.getPerfilAcesso().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                    permissaoDAO.verificarPermissaoUsuarioFuncionalidade(usuarioPerfilAcesso.getPerfilAcesso(), usuario, permissao, descricao);
                    return true;
                } catch (Exception ex) {
                    ex.printStackTrace();
                    return false;
                }
            }
        }
        return false;
    }

}

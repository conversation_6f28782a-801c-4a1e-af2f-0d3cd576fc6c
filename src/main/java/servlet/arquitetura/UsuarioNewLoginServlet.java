package servlet.arquitetura;

import br.com.pactosolucoes.atualizadb.processo.SincronizarUsuarioNovoLogin;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.facade.jdbc.utilitarias.Conexao;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.sql.Connection;

import static org.apache.commons.lang3.StringUtils.isNotBlank;

public class UsuarioNewLoginServlet extends SuperServlet {
    @Override
    protected void processRequest(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {

        response.addHeader("Access-Control-Allow-Origin", "*");
        response.addHeader("Access-Control-Allow-Methods", "GET,POST");
        response.setContentType("application/json");

        String path = request.getServletPath();
        String[] pathParts = path.split("/");
        String recurso = pathParts[5];

        String chave = request.getParameter("chave");
        Integer empresa = Integer.valueOf(request.getParameter("empresa"));

        try {
            switch (recurso) {
                case "sincronizarNovoLogin":
                    try (Connection con = new DAO().obterConexaoEspecifica(chave)) {
                        UsuarioVO us = new UsuarioVO();
                        us.setCodigo(Integer.valueOf(request.getParameter("usuario")));
                        us.setUsuarioGeral(new Usuario(con).consultarUsuarioGeral(us));
                        if (isNotBlank(us.getUsuarioGeral())) {
                            SincronizarUsuarioNovoLogin.atualizarUsuarioGeral(us.getCodigo(), con,
                                    empresa, new Usuario(con).getUsuarioRecorrencia(), request.getParameter("ipCliente"),
                                    Boolean.parseBoolean(request.getParameter("integNovoLogin")));
                        } else {
                            SincronizarUsuarioNovoLogin.enviarEmailNovoUsuario(us.getCodigo(), false, false, null,
                                    Conexao.getFromSession(), empresa, new Usuario(con).getUsuarioRecorrencia(), request.getParameter("ipCliente"),
                                    Boolean.parseBoolean(request.getParameter("integNovoLogin")));
                        }
                        response.getWriter().append("OK");
                    }
                    break;
            }
        }catch (Exception e ){
            System.out.println("Erro na api rest "+recurso+". Message: "+e.getMessage());
            processarErro(e, request, response, null);
        }
    }
}

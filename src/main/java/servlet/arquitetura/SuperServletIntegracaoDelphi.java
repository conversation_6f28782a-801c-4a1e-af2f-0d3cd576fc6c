package servlet.arquitetura;

import acesso.webservice.AcessoControle;
import acesso.webservice.DaoAuxiliar;
import negocio.comuns.utilitarias.ConsistirException;
import org.json.JSONObject;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;

/**
 * date : 26/03/2015 16:11:27
 * autor: Ulisses
 */
public abstract class SuperServletIntegracaoDelphi extends HttpServlet {

	private AcessoControle acessoControle;
	private String chave;
	protected abstract JSONObject processarRequisicao(HttpServletRequest request)throws Exception;
	
    private void processRequest(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        response.setContentType("application/json");
        response.setCharacterEncoding("UTF-8");
		PrintWriter out = response.getWriter();        
    	try{
        	String key = request.getParameter("key");
        	if ((key == null) || (key.trim().equals(""))){
        		throw new ConsistirException("Nenhuma chave de empresa foi informada para esta operaþÒo.");
        	}
        	setChave(key); 
    		setAcessoControle(DaoAuxiliar.retornarAcessoControle(key));
    		
    		out.println(processarRequisicao(request));
    	}catch (ConsistirException ex){
    		out.println("ERRO_REQUISICAO_SERVLET: " + this.getClass().getName() + "'. Erro:"  + ex.getMessage());
    	}catch (Exception e){
    		out.println("ERRO_REQUISICAO_SERVLET: " + this.getClass().getName() + "'. Erro:"  + e.getMessage());
    	}
    }
	
	
    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        processRequest(request, response);
    }

    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        processRequest(request, response);
    }


	public AcessoControle getAcessoControle() {
		return acessoControle;
	}


	public void setAcessoControle(AcessoControle acessoControle) {
		this.acessoControle = acessoControle;
	}

	public String getChave() {
		return chave;
	}

	public void setChave(String chave) {
		this.chave = chave;
	}
}

package servlet.arquitetura;

import br.com.pactosolucoes.comuns.notificacao.RecursoSistema;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.arquitetura.EnvelopeRespostaDTO;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.ZillyonWebFacade;
import org.json.JSONObject;

import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.sql.Connection;

/**
 * Created with IntelliJ IDEA.
 * User: <PERSON><PERSON>
 * Date: 09/08/2021
 */
public class NotificacaoRecursoEmpresaServlet extends SuperServlet {

    @Override
    protected void processRequest(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        Connection con = null;
        ZillyonWebFacade zillyonWebFacade = null;
        try {
            response.addHeader("Access-Control-Allow-Origin", "*");
            response.addHeader("Access-Control-Allow-Methods", "GET,POST");
            response.addHeader("Access-Control-Allow-Headers", "Authorization,empresaId");
            response.setContentType("application/json");

            String key = request.getParameter("key");
            if (UteisValidacao.emptyString(key)) {
                throw new Exception("Key não informada");
            }

            String recurso = request.getParameter("r");
            if (UteisValidacao.emptyString(recurso)) {
                throw new Exception("Recurso não informado");
            }

            Integer empresa = obterEmpresa(request, true);

            String usuario = request.getParameter("u");
            if (UteisValidacao.emptyString(usuario)) {
                throw new Exception("Usuario não informado");
            }

            RecursoSistema recursoEnum = RecursoSistema.fromDescricao(recurso);
            if (recursoEnum == null) {
                throw new Exception("RecursoSistema não identificado");
            }

            con = obterConexao(request);
            zillyonWebFacade = new ZillyonWebFacade(con);
            zillyonWebFacade.notificarRecursoSistema(key, recursoEnum, Integer.parseInt(usuario), empresa);

            response.setStatus(HttpServletResponse.SC_OK);
            response.getWriter().append(new JSONObject(EnvelopeRespostaDTO.of("ok")).toString());
        } catch (Exception ex) {
            ex.printStackTrace();
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            response.getWriter().append(new JSONObject(EnvelopeRespostaDTO.erro("Erro", ex.getMessage())).toString());
        } finally {
            zillyonWebFacade = null;
            finalizarConexao(con);
        }
    }

    private Connection obterConexao(ServletRequest request) throws Exception {
        String key = request.getParameter("key");
        if (UteisValidacao.emptyString(key)) {
            throw new Exception("Chave não informada.");
        }
        return new DAO().obterConexaoEspecifica(key.trim());
    }

    private void finalizarConexao(Connection con) {
        try {
            if (con != null) {
                con.close();
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    private Integer obterEmpresa(ServletRequest request, boolean validar) throws Exception {
        Integer empresa = UteisValidacao.converterInteiro(request.getParameter("empresa"));
        if (UteisValidacao.emptyNumber(empresa)) {
            empresa = UteisValidacao.converterInteiro(request.getParameter("e"));
        }
        if (validar && UteisValidacao.emptyNumber(empresa)) {
            throw new Exception("Empresa não informado");
        }
        return empresa;
    }
}

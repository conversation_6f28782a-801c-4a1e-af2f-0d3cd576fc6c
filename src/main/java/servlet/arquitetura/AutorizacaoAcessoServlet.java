package servlet.arquitetura;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.arquitetura.EnvelopeRespostaDTO;
import negocio.comuns.arquitetura.UsuarioPerfilAcessoVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.ControleAcesso;
import negocio.facade.jdbc.arquitetura.Permissao;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.facade.jdbc.basico.Empresa;
import org.json.JSONObject;

import javax.faces.context.FacesContext;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.sql.Connection;
import java.util.Iterator;

import static org.apache.commons.lang3.StringUtils.isNotBlank;

public class AutorizacaoAcessoServlet extends SuperServlet {

    @Override
    protected void processRequest(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        Connection con = null;
        FacesContext fc = FacesContext.getCurrentInstance();
        if(fc != null){
            fc.release();
        }
        try {
            response.setHeader("Access-Control-Allow-Origin", "*");
            response.setHeader("Access-Control-Allow-Methods", "GET,POST");
            response.setHeader("Access-Control-Allow-Headers", "*");
            response.setContentType("application/json");

            con = obterConexao(request, null);

            String op = request.getParameter("op");
            EnvelopeRespostaDTO envelopeRespostaDTO = new EnvelopeRespostaDTO();
            if (UteisValidacao.emptyString(op)) {
                envelopeRespostaDTO = validarPermissao(request, con);
            } else if(op.equals("validarPermissaoUsuarioLogado")) {
                envelopeRespostaDTO = validarPermissaoUsuarioLogado(request, con);
            }


            response.setStatus(HttpServletResponse.SC_OK);
            response.getWriter().append(new JSONObject(envelopeRespostaDTO).toString());
        } catch (Exception ex) {
            ex.printStackTrace();
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            response.getWriter().append(new JSONObject(EnvelopeRespostaDTO.erro("Erro", ex.getMessage())).toString());
        } finally {
            finalizarConexao(con);
        }
    }

    private EnvelopeRespostaDTO validarPermissaoUsuarioLogado(HttpServletRequest request, Connection con) throws Exception {
        Usuario usuarioDAO;
        Empresa empresaDAO;
        ControleAcesso controleAcessoDAO = null;
        Permissao permissaoDAO = null;
        try {
            usuarioDAO = new Usuario(con);
            empresaDAO = new Empresa(con);
            controleAcessoDAO = new ControleAcesso(con);
            permissaoDAO = new Permissao(con);
            JSONObject jsonObject = getJSONBody(request);
            Integer codigoUsuario = jsonObject.getInt("codigoUsuario");
            Integer codigoEmpresa = jsonObject.getInt("codigoEmpresa");
            String permissao = jsonObject.getString("permissao");
            String funcionalidade = jsonObject.getString("funcionalidade");
            String operacaoString = null;
            try {
                if (isNotBlank(jsonObject.getString("operacaoString"))) {
                    operacaoString = jsonObject.getString("operacaoString");
                }
            }catch (Exception ignore){}

            UsuarioVO usuarioVO = usuarioDAO.consultarPorCodigoUsuario(codigoUsuario, false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            EmpresaVO empresaVO = empresaDAO.consultarPorChavePrimaria(codigoEmpresa, Uteis.NIVELMONTARDADOS_DADOSBASICOS);

            if (usuarioVO.getUsuarioPerfilAcessoVOs().isEmpty()) {
                throw new Exception("O usua?io informado não possui perfil de acesso.");
            }

            if (empresaVO.getCodigo() == 0) {
                throw new Exception("Empresa não encontrada!");
            }

            Iterator i = usuarioVO.getUsuarioPerfilAcessoVOs().iterator();
            while (i.hasNext()) {
                UsuarioPerfilAcessoVO usuarioPerfilAcesso = (UsuarioPerfilAcessoVO) i.next();
                if (empresaVO.getCodigo().equals(usuarioPerfilAcesso.getEmpresa().getCodigo())) {
                    usuarioPerfilAcesso.getPerfilAcesso().setPermissaoVOs(permissaoDAO.consultarPermissaos(usuarioPerfilAcesso.getPerfilAcesso().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                    controleAcessoDAO.verificarPermissaoUsuarioFuncionalidade(usuarioPerfilAcesso.getPerfilAcesso(), usuarioVO, funcionalidade, permissao, operacaoString);
                    usuarioVO.setPorcetagemDescontoContrato(usuarioPerfilAcesso.getPerfilAcesso().getPorcetagemDescontoContrato());
                }
            }
            return EnvelopeRespostaDTO.of(usuarioVO.getCodigo());
        } finally {
            usuarioDAO = null;
            empresaDAO = null;
        }
    }

    private EnvelopeRespostaDTO validarPermissao(HttpServletRequest request, Connection con) throws Exception {
        ControleAcesso controleAcessoDAO = null;
        Permissao permissaoDAO = null;
        Empresa emrpesaDAO = null;
        try {
            controleAcessoDAO = new ControleAcesso(con);
            permissaoDAO = new Permissao(con);
            emrpesaDAO = new Empresa(con);
            JSONObject jsonObject = getJSONBody(request);
            String username = jsonObject.getString("username");
            String senha = jsonObject.getString("senha");
            String permissao = jsonObject.getString("permissao");
            String funcionalidade = jsonObject.getString("funcionalidade");
            String empresa = jsonObject.getString("empresa");
            EmpresaVO empresaVO = null;
            if(empresa.matches("\\d+")){
                empresaVO = emrpesaDAO.consultarPorChavePrimaria(Integer.parseInt(empresa), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            } else {
                empresaVO = emrpesaDAO.consultarPorNomeEmpresa(empresa, false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            }
            UsuarioVO usuarioVO = null;
            try{
                usuarioVO = controleAcessoDAO.verificarLoginUsuario(username, senha);
            } catch (Exception e){
                e.printStackTrace();
            }
            if(usuarioVO == null){
                usuarioVO = controleAcessoDAO.verificarLoginUsuarioPIN(username, senha);
            }
            for (Object o : usuarioVO.getUsuarioPerfilAcessoVOs()) {
                UsuarioPerfilAcessoVO usuarioPerfilAcesso = (UsuarioPerfilAcessoVO) o;

                if (empresaVO.getCodigo().equals(usuarioPerfilAcesso.getEmpresa().getCodigo())) {
                    usuarioPerfilAcesso.getPerfilAcesso().setPermissaoVOs(
                            permissaoDAO.consultarPermissaos(
                                    usuarioPerfilAcesso.getPerfilAcesso().getCodigo(),
                                    Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                    controleAcessoDAO.verificarPermissaoUsuarioFuncionalidade(
                            usuarioPerfilAcesso.getPerfilAcesso(),
                            usuarioVO,
                            funcionalidade,
                            permissao
                    );
                    break;
                }
            }
            return EnvelopeRespostaDTO.of(usuarioVO.getCodigo());
        } finally {
            controleAcessoDAO = null;
            permissaoDAO = null;
            emrpesaDAO = null;
        }
    }

    private String obterBody(ServletRequest request) throws Exception {
        StringBuffer body = new StringBuffer();
        try {
            InputStream inputStream = request.getInputStream();
            BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream, StandardCharsets.UTF_8));
            String line = null;

            while ((line = reader.readLine()) != null) {
                body.append(line);
            }
            return body.toString();
        } catch (Exception ex) {
            ex.printStackTrace();
            throw new Exception("Erro body: " + ex.getMessage());
        }
    }

    private Connection obterConexao(ServletRequest request, String chave) throws Exception {
        if (!UteisValidacao.emptyString(chave)) {
            return new DAO().obterConexaoEspecifica(chave.trim());
        }
        return new DAO().obterConexaoEspecifica(obterChave(request));
    }

    private String obterChave(ServletRequest request) throws Exception {
        String key = request.getParameter("key");
        if (UteisValidacao.emptyString(key)) {
            throw new Exception("Chave não informada.");
        }
        return key.trim();
    }

    private void finalizarConexao(Connection con) {
        try {
            if (con != null) {
                con.close();
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

}

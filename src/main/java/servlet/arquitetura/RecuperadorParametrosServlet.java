package servlet.arquitetura;

import negocio.comuns.utilitarias.UteisValidacao;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * <AUTHOR>
 * @since 29/01/2019
 */
public class RecuperadorParametrosServlet {

    public static Integer obterParametro(Object parametro) {
        if (parametro != null) {
            return Integer.parseInt(parametro.toString());
        }
        return null;
    }

    public static String obterParametroString(Object parametro) {
        if (parametro != null) {
            return parametro.toString();
        }
        return null;
    }

    public static Date obterParametroDate(Object parametro) throws ParseException {
        if (parametro != null) {
            if (!UteisValidacao.emptyString(parametro.toString())) {
                return new SimpleDateFormat("dd/MM/yyyy").parse(parametro.toString());
            }
        }
        return null;
    }

}

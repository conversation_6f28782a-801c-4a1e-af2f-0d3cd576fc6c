package servlet.arquitetura;

import br.com.pactosolucoes.comuns.json.ConfigCobrancaMensalJSON;
import br.com.pactosolucoes.enumeradores.TipoCobrancaPactoEnum;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.basico.Empresa;
import org.json.JSONArray;
import org.json.JSONObject;
import servicos.adm.CreditoDCCService;
import servicos.propriedades.PropsService;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.sql.Connection;
import java.util.List;

public class CreditoDCCServlet extends SuperServlet {

    @Override
    protected void processRequest(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        Connection con = null;
        try {
            response.addHeader("Access-Control-Allow-Origin", "*");
            response.addHeader("Access-Control-Allow-Methods", "GET");
            response.setContentType("application/json");

            String method = request.getMethod();

            if (!method.equalsIgnoreCase("POST")) {
                throw new Exception("Methodo não suportado para esse recurso");
            }

            String chave = request.getParameter("chave");
            if (UteisValidacao.emptyString(chave)) {
                throw new Exception("Chave não informada");
            }

            String operacao = request.getParameter("op");
            if (UteisValidacao.emptyString(operacao)) {
                throw new Exception("Operação não informada");
            }

            con = new DAO().obterConexaoEspecifica(chave);

            InputStream inputStream = request.getInputStream();
            BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream, StandardCharsets.UTF_8));

            StringBuffer body = new StringBuffer();
            String line = null;
            try {
                while ((line = reader.readLine()) != null) {
                    body.append(line);
                }

            } catch (Exception e) {
                e.printStackTrace();
            }

            JSONObject jsonBody = new JSONObject();
            if (!UteisValidacao.emptyString(body.toString())) {
                jsonBody = new JSONObject(body.toString());
            }

            switch (operacao) {
                case "addBonus":
                    adicionarBonus(jsonBody, response, con);
                    break;
                case "qtdAtualBonus":
                    qtdAtualBonus(jsonBody, response, con);
                    break;
                case "alterarInformacoes":
                    alterarInformacoesEmpresaCobrancaPacto(jsonBody, response, con);
                    break;
                case "cobrancaMensalRede":
                    cobrancaMensalRede(jsonBody, response, con);
                    break;
                case "informacoes":
                    informacoes(jsonBody, response, con, chave);
                    break;
                case "criarColunaNecessariaNaoExistente":
                    criarColunaNecessariaNaoExistente(jsonBody, con);
                    break;
            }
        } catch (Exception ex) {
            response.setStatus(400);
            response.getWriter().append(this.toJSON(false, ex.getMessage()).toString());
        } finally {
            try {
                if (con != null) {
                    con.close();
                }
            } catch (Exception ignored) {
            }
        }
    }

    private JSONObject toJSON(boolean sucesso, String mensagem) {
        JSONObject json = new JSONObject();
        json.put("sucesso", sucesso);
        json.put("msg", mensagem);
        return json;
    }

    private JSONObject toJSONObj(boolean sucesso, Object obj) {
        JSONObject json = new JSONObject();
        json.put("sucesso", sucesso);
        json.put("msg", obj);
        return json;
    }

    private void adicionarBonus(JSONObject jsonBody, HttpServletResponse response, Connection con) throws Exception {
        Integer empresa = jsonBody.optInt("empresa");
        Integer qtdBonus = jsonBody.optInt("qtd");
        String observacao = jsonBody.optString("observacao");
        String usuario = jsonBody.optString("usuario");

        CreditoDCCService service = new CreditoDCCService(con);
        service.alterarBonus(empresa, qtdBonus, observacao, usuario, true);
        service = null;
        response.getWriter().append(this.toJSON(true, "Alterado para " + qtdBonus + " créditos de bônus.").toString());
    }

    private void qtdAtualBonus(JSONObject jsonBody, HttpServletResponse response, Connection con) throws Exception {
        Integer empresa = jsonBody.optInt("empresa");
        CreditoDCCService service = new CreditoDCCService(con);
        Integer qtdBonus = service.consultarBonus(empresa);
        service = null;
        response.getWriter().append(this.toJSON(true, qtdBonus.toString()).toString());
    }

    private void alterarInformacoesEmpresaCobrancaPacto(JSONObject jsonBody, HttpServletResponse response, Connection con) throws Exception {
        try {

            Integer codigoEmpresa = jsonBody.getInt("codigoEmpresa");
            Integer tipoCobrancaPacto = jsonBody.getInt("tipoCobrancaPacto");
            boolean gerarCobrancaAutomaticaPacto = jsonBody.getBoolean("gerarCobrancaAutomaticaPacto");
            Integer qtdDiasFechamentoCobrancaPacto = jsonBody.getInt("qtdDiasFechamentoCobrancaPacto");
            Double valorCreditoPacto = jsonBody.getDouble("valorCreditoPacto");
            boolean gerarNotaFiscalCobrancaPacto = jsonBody.getBoolean("gerarNotaFiscalCobrancaPacto");
            Integer qtdParcelasCobrancaPacto = jsonBody.getInt("qtdParcelasCobrancaPacto");
            Integer qtdCreditoRenovarPrePagoCobrancaPacto = jsonBody.getInt("qtdCreditoRenovarPrePagoCobrancaPacto");
            String nomeUsuarioOAMD = jsonBody.getString("nomeUsuarioOAMD");
            Integer diaVencimentoCobrancaPacto = jsonBody.getInt("diaVencimentoCobrancaPacto");
            boolean empresaResponsavelCobrancaPacto = jsonBody.getBoolean("empresaResponsavelCobrancaPacto");
            Boolean cobrarCreditoPactoBoleto = null;

            try {
                cobrarCreditoPactoBoleto = jsonBody.getBoolean("cobrarCreditoPactoBoleto");
            } catch (Exception ex) {}

            CreditoDCCService service = new CreditoDCCService(con);
            String retorno = service.alterarInformacoesEmpresaCobrancaPacto(codigoEmpresa, tipoCobrancaPacto, gerarCobrancaAutomaticaPacto,
                    qtdDiasFechamentoCobrancaPacto, valorCreditoPacto, gerarNotaFiscalCobrancaPacto, qtdParcelasCobrancaPacto,
                    qtdCreditoRenovarPrePagoCobrancaPacto, nomeUsuarioOAMD, diaVencimentoCobrancaPacto, empresaResponsavelCobrancaPacto, cobrarCreditoPactoBoleto);
            service = null;

            if (!retorno.startsWith("Sucesso")) {
                throw new Exception(retorno);
            }
            response.getWriter().append(this.toJSON(true, retorno).toString());
        } catch (Exception ex) {
            ex.printStackTrace();
            throw new Exception("ERRO: " + ex.getMessage());
        }
    }

    private void cobrancaMensalRede(JSONObject jsonBody, HttpServletResponse response, Connection con) throws Exception {
        try {

            Integer codigoEmpresa = jsonBody.getInt("codigoEmpresa");

            CreditoDCCService service = new CreditoDCCService(con);
            String json = service.processarClientePosPagoMensalRedeEmpresa(codigoEmpresa);
            service = null;

            response.getWriter().append(this.toJSON(true, json).toString());
        } catch (Exception ex) {
            ex.printStackTrace();
            throw new Exception("ERRO: " + ex.getMessage());
        }
    }

    private void informacoes(JSONObject jsonBody, HttpServletResponse response, Connection con, String key) throws Exception {
        CreditoDCCService service = null;
        Empresa empresaDAO = null;
        try {
            service = new CreditoDCCService(con);
            empresaDAO = new Empresa(con);

            List<EmpresaVO> listaEmpresas = empresaDAO.consultarEmpresas();

            JSONArray array = new JSONArray();
            for (EmpresaVO empresaVO : listaEmpresas) {
                JSONObject json = new JSONObject();
                String msg_erro = "";
                try {
                    json.put("chave", key);
                    json.put("empresa", empresaVO.getCodigo());
                    json.put("empresa_nome", empresaVO.getNome());
                    json.put("ativa", empresaVO.isAtiva());
                    json.put("codigo_financeiro", empresaVO.getCodEmpresaFinanceiro());
                    json.put("cnpj", empresaVO.getCNPJ());

                    TipoCobrancaPactoEnum tipoCobrancaPactoEnum = TipoCobrancaPactoEnum.getConsultarPorCodigo(empresaVO.getTipoCobrancaPacto());
                    if (tipoCobrancaPactoEnum == null) {
                        throw new Exception("TipoCobrancaPacto não identificado");
                    }
                    json.put("tipo", tipoCobrancaPactoEnum.getCodigo());
                    json.put("tipo_descricao", tipoCobrancaPactoEnum.getDescricao());
                    json.put("tipo_name", tipoCobrancaPactoEnum.name());
                    json.put("recorrente", empresaVO.isGerarCobrancaAutomaticaPacto());

                    Integer credito_disponivel = null;
                    Integer credito_utilizado = null;
                    Double valor_unitario = null;

                    if (!tipoCobrancaPactoEnum.equals(TipoCobrancaPactoEnum.PRE_PAGO)) {
                        credito_utilizado = service.consultarQtdCreditoUtilizado(empresaVO.getCodigo(), empresaVO.getTipoCobrancaPacto());
                    }

                    if (tipoCobrancaPactoEnum.equals(TipoCobrancaPactoEnum.POS_PAGO_EFETIVADO_MENSAL) ||
                            tipoCobrancaPactoEnum.equals(TipoCobrancaPactoEnum.POS_PAGO_EFETIVADO_MENSAL_REDE_EMPRESAS)) {
                        ConfigCobrancaMensalJSON configCobrancaMensalJSON = service.consultarConfigCobrancaMensalJSON(key, empresaVO);

                        valor_unitario = Uteis.arredondarForcando2CasasDecimais(configCobrancaMensalJSON.getValorMensal() / configCobrancaMensalJSON.getQtdMaxima());
                        json.put("tabela_recorrencia_id", configCobrancaMensalJSON.getCodigo());
                        json.put("tabela_recorrencia", configCobrancaMensalJSON.getDescricao());
                        json.put("valor_mensal", configCobrancaMensalJSON.getValorMensal());
                        json.put("valor_excedente", configCobrancaMensalJSON.getValorUnitarioExcedente());
                        json.put("qtd_minima", configCobrancaMensalJSON.getQtdMinima());
                        json.put("qtd_maxima", configCobrancaMensalJSON.getQtdMaxima());

                        credito_disponivel = configCobrancaMensalJSON.getQtdMaxima() - credito_utilizado;
                    } else if (tipoCobrancaPactoEnum.equals(TipoCobrancaPactoEnum.PRE_PAGO) ||
                            tipoCobrancaPactoEnum.equals(TipoCobrancaPactoEnum.PRE_PAGO_EFETIVADO)) {
                        credito_disponivel = empresaVO.getCreditoDCC();
                    } else if (tipoCobrancaPactoEnum.equals(TipoCobrancaPactoEnum.REDE_EMPRESAS_PRE_PAGO)) {
                        String urlOamd = PropsService.getPropertyValue(key, PropsService.urlOamd);
                        JSONObject info = empresaDAO.obterInfoRedeDCC(urlOamd, key);
                        credito_disponivel = info.getInt("creditos");
                    }

                    json.put("credito_bonus", service.consultarBonus(empresaVO.getCodigo()));
                    json.put("credito_disponivel", credito_disponivel);
                    json.put("credito_utilizado", credito_utilizado);
                    json.put("valor_unitario", valor_unitario);
                    json.put("ultima_cobranca", Uteis.getDataAplicandoFormatacao(empresaVO.getDtUltimaCobrancaPacto(), "dd/MM/yyyy HH:mm:ss"));
                } catch (Exception ex) {
                    ex.printStackTrace();
                    msg_erro = ex.getMessage();
                } finally {
                    json.put("msg_erro", msg_erro);
                    array.put(json);
                }
            }

            response.setStatus(HttpServletResponse.SC_OK);
            response.getWriter().append(this.toJSONObj(true, array).toString());
        } catch (Exception ex) {
            ex.printStackTrace();
            throw new Exception("ERRO: " + ex.getMessage());
        } finally {
            service = null;
            empresaDAO = null;
        }
    }

    private void criarColunaNecessariaNaoExistente(JSONObject body, Connection con) throws Exception {
        try {
            String colunaCriar = body.optString("coluna");
            if (!UteisValidacao.emptyString(colunaCriar) && colunaCriar.equals("cobrarCreditoPactoBoleto")) {
                SuperFacadeJDBC.executarUpdate("ALTER TABLE empresa ADD COLUMN IF NOT EXISTS " + colunaCriar + " BOOLEAN DEFAULT FALSE;", con);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            throw new Exception("ERRO: " + ex.getMessage());
        }
    }
}

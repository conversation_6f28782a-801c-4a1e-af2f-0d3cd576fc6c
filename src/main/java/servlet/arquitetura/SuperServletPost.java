package servlet.arquitetura;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.utilitarias.UteisValidacao;
import org.json.JSONObject;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.sql.Connection;

/**
 * Created by ulisses on 25/01/2023.
 */
public class SuperServletPost extends SuperServlet {

    private Connection con = null;
    private JSONObject jsonBody = null;

    @Override
    protected void processRequest(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        try {
            response.addHeader("Access-Control-Allow-Origin", "*");
            response.addHeader("Access-Control-Allow-Methods", "POST");
            response.setContentType("application/json");

            String method = request.getMethod();
            if (!method.equalsIgnoreCase("POST")) {
                throw new Exception("Método não suportado.");
            }
            String key = request.getParameter("key");
            if (UteisValidacao.emptyString(key)) {
                throw new Exception("Chave não informada.");
            }
            try {
                InputStream inputStream = request.getInputStream();
                BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream, "UTF-8"));

                StringBuffer body = new StringBuffer();
                String line = null;

                while ((line = reader.readLine()) != null) {
                    body.append(line);
                }

                jsonBody = new JSONObject(body.toString());
            } catch (Exception ex) {
                ex.printStackTrace();
                throw new Exception("Erro body: " + ex.getMessage());
            }

            con = new DAO().obterConexaoEspecifica(key);

            String operacao = jsonBody.optString("operacao");


            Object objRetorno = tratarOperacao(operacao);

            if (objRetorno == null) {
                throw new Exception("Nenhuma operação executada");
            }

            response.getWriter().append(toJSON(true, objRetorno).toString());
        } catch (Exception ex) {
            response.setStatus(400);
            response.getWriter().append(toJSON(false, ex.getMessage()).toString());
        } finally {
            if (con != null) {
                try {
                    con.close();
                } catch (Exception ex) {
                }
            }
        }
    }

    public Object tratarOperacao(String operacao)throws Exception{
        return null;
    }

    private JSONObject toJSON(boolean sucesso, Object dados) {
        JSONObject json = new JSONObject();
        json.put("sucesso", sucesso);
        json.put("dados", dados);
        return json;
    }

    public Connection getCon() {
        return con;
    }

    public JSONObject getJsonBody() {
        return jsonBody;
    }
}

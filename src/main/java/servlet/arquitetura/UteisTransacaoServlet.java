package servlet.arquitetura;

import br.com.pactosolucoes.atualizadb.processo.PovoarMovParcelaTentativaConvenio;
import br.com.pactosolucoes.atualizadb.processo.ProcessosTransacao;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.basico.CreditoPactoHistorico;
import negocio.facade.jdbc.financeiro.ConvenioCobranca;
import negocio.facade.jdbc.financeiro.Transacao;
import negocio.facade.jdbc.utilitarias.Conexao;
import org.json.JSONObject;
import servicos.impl.dcc.base.RemessaService;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.sql.Connection;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 * User: Luiz Felipe
 * Date: 01/09/2021
 */
public class UteisTransacaoServlet extends SuperServlet {

    @Override
    protected void processRequest(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        try {
            response.addHeader("Access-Control-Allow-Origin", "*");
            response.addHeader("Access-Control-Allow-Methods", "GET,POST");
            response.setContentType("application/json");

            String chave = request.getParameter("chave");
            String op = request.getParameter("op");

            if (op.equalsIgnoreCase("verificador")) {
                verificador(chave);
            } else if (op.equalsIgnoreCase("verificadorBoletos")) {
                verificadorBoletos(chave, request);
            } else if (op.equalsIgnoreCase("atualizarOutrasInformacoes")) {
                JSONObject json = atualizarOutrasInformacoes(chave, request);
                response.getWriter().append(this.toJSON(true, json).toString());
            } else if (op.equalsIgnoreCase("atualizarDescricaoRetorno")) {
                Integer qtd = atualizarDescricaoRetorno(chave, request);
                response.getWriter().append(this.toJSON(true, qtd + " transações ajustadas").toString());
                return;
            } else if (op.equalsIgnoreCase("transacaoSemRecibo")) {
                JSONObject json = transacaoSemRecibo(chave);
                response.getWriter().append(this.toJSON(true, json).toString());
                return;
            } else if (op.equalsIgnoreCase("preencherTabelasEnums")) {
                response.getWriter().append(this.toJSON(true, preencherTabelasEnums(chave)).toString());
                return;
            } else if (op.equalsIgnoreCase("processarCreditoPactoHistorico")) {
                response.getWriter().append(this.toJSON(true, processarCreditoPactoHistorico(chave, request)).toString());
                return;
            } else {
                throw new Exception("Nenhuma operação realizada");
            }

            response.getWriter().append(this.toJSON(true, "ok").toString());
        } catch (Exception ex) {
            response.setStatus(400);
            response.getWriter().append(this.toJSON(false, ex.getMessage()).toString());
        }
    }

    private JSONObject toJSON(boolean sucesso, Object obj) {
        JSONObject json = new JSONObject();
        json.put("sucesso", sucesso);
        json.put("msg", obj);
        return json;
    }

    private void verificador(String chave) throws Exception {
        try (Connection con = new DAO().obterConexaoEspecifica(chave)) {
            Conexao.guardarConexaoForJ2SE(chave, con);
            RemessaService remessaService;
            try {
                remessaService = new RemessaService(con);
                remessaService.verificarPendenciasTransacaoOnlineGeral();
            } catch (Exception ex) {
                ex.printStackTrace();
                throw ex;
            } finally {
                remessaService = null;
            }
        }
    }

    private void verificadorBoletos(String chave, HttpServletRequest request) throws Exception {
        try (Connection con = new DAO().obterConexaoEspecifica(chave)) {
            Conexao.guardarConexaoForJ2SE(chave, con);
            RemessaService remessaService;
            try {
                remessaService = new RemessaService(con);
                Date dataVencimentoLimite = Calendario.somarDias(Calendario.hoje(), 3);
                try {
                    dataVencimentoLimite = Calendario.getDate("ddMMyyyy", request.getParameter("data"));
                } catch (Exception ex) {
                    ex.printStackTrace();
                }

                remessaService.processarBoletosPendentesGeral(dataVencimentoLimite);
            } catch (Exception ex) {
                ex.printStackTrace();
                throw ex;
            } finally {
                remessaService = null;
            }
        }
    }

    private Integer atualizarDescricaoRetorno(String chave, HttpServletRequest request) throws Exception {
        try (Connection con = new DAO().obterConexaoEspecifica(chave)) {
            try {
                Date inicio = Calendario.getDate("yyyyMMdd", request.getParameter("inicio"));
                Date fim = Calendario.getDate("yyyyMMdd", request.getParameter("fim"));
                Integer limit = UteisValidacao.converterInteiro(request.getParameter("limite"));
                if (UteisValidacao.emptyNumber(limit)) {
                    limit = 500;
                }
                return PovoarMovParcelaTentativaConvenio.preencherCodigoRetornoDescricaoTransacao(null, null, inicio, fim, limit, con);
            } catch (Exception ex) {
                ex.printStackTrace();
                throw ex;
            }
        }
    }

    private JSONObject transacaoSemRecibo(String chave) throws Exception {
        try (Connection con = new DAO().obterConexaoEspecifica(chave)) {
            Transacao transacaoDAO;
            try {
                transacaoDAO = new Transacao(con);
                return transacaoDAO.gerarReciboTransacoesSemRecibo();
            } catch (Exception ex) {
                ex.printStackTrace();
                throw ex;
            } finally {
                transacaoDAO = null;
            }
        }
    }

    private JSONObject atualizarOutrasInformacoes(String chave, HttpServletRequest request) throws Exception {
        try (Connection con = new DAO().obterConexaoEspecifica(chave)) {
            try {
                Date inicio = Calendario.getDate("yyyyMMdd", request.getParameter("inicio"));
                Date fim = Calendario.getDate("yyyyMMdd", request.getParameter("fim"));
                Integer limit = UteisValidacao.converterInteiro(request.getParameter("limite"));
                if (UteisValidacao.emptyNumber(limit)) {
                    limit = 500;
                }

                boolean somenteAprovadas = request.getParameter("aprovadas") != null && request.getParameter("aprovadas").equals("true");
                boolean atualizarExistentes = request.getParameter("atualizarExistentes") != null && request.getParameter("atualizarExistentes").equals("true");
                String tipo = request.getParameter("tipo"); //"nrvezes-bandeira-adquirente";
                return ProcessosTransacao.atualizarOutrasInformacoes(somenteAprovadas, atualizarExistentes, tipo, inicio, fim, limit, con);
            } catch (Exception ex) {
                ex.printStackTrace();
                throw ex;
            }
        }
    }

    private String preencherTabelasEnums(String chave) throws Exception {
        try (Connection con = new DAO().obterConexaoEspecifica(chave)) {
            ConvenioCobranca convenioCobrancaDAO;
            try {
                convenioCobrancaDAO = new ConvenioCobranca(con);
                convenioCobrancaDAO.preencherTabelasEnums(true);
                return "ok";
            } catch (Exception ex) {
                ex.printStackTrace();
                throw ex;
            } finally {
                convenioCobrancaDAO = null;
            }
        }
    }

    private String processarCreditoPactoHistorico(String chave, HttpServletRequest request) throws Exception {
        try (Connection con = new DAO().obterConexaoEspecifica(chave)) {
            CreditoPactoHistorico creditoPactoHistoricoDAO;
            try {
                creditoPactoHistoricoDAO = new CreditoPactoHistorico(con);

                String paramMes = request.getParameter("mes");

                List<Date> mesesProcessar = new ArrayList<>();
                try {
                    if (paramMes != null) {
                        if (paramMes.startsWith("ultimos")) {
                            Integer mesesReprocessar = Integer.parseInt(paramMes.replace("ultimos", ""));
                            mesesProcessar.addAll(Uteis.getMesesEntreDatas(Calendario.somarMeses(Calendario.hoje(), -mesesReprocessar), Calendario.hoje()));
                        } else {
                            Date mes = Calendario.getDate("MMyyyy", paramMes);
                            mesesProcessar.add(mes);
                        }
                    } else {
                        mesesProcessar.add(Calendario.hoje());
                    }
                } catch (Exception ex) {
                    ex.printStackTrace();
                }

                for (Date mesReferencia : mesesProcessar) {
                    creditoPactoHistoricoDAO.processarTodasEmpresas(mesReferencia);
                }
                return "ok";
            } catch (Exception ex) {
                ex.printStackTrace();
                throw ex;
            } finally {
                creditoPactoHistoricoDAO = null;
            }
        }
    }
}

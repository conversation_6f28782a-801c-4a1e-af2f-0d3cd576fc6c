package servlet.arquitetura;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import importador.outros.ProcessoRecursoPadrao;
import importador.outros.ProcessoSincronizarColaboradoresTreino;
import negocio.comuns.arquitetura.EnvelopeRespostaDTO;
import negocio.comuns.utilitarias.UteisValidacao;
import org.json.JSONObject;

import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.sql.Connection;

public class UteisProcessoServlet extends SuperServlet {

    @Override
    protected void processRequest(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        try {
            response.setContentType("application/json");

            String operacao = request.getParameter("op");
            if (UteisValidacao.emptyString(operacao)) {
                throw new Exception("Operação informada");
            }

            EnvelopeRespostaDTO envelopeRespostaDTO;
            if (operacao.equalsIgnoreCase("recurso-padrao-tela-aluno")) {
                envelopeRespostaDTO = recursoPadraoTelaAluno(request);
            } else if (operacao.equalsIgnoreCase("sincronizar-professor-colaborador-treino")) {
                envelopeRespostaDTO = sincronizarProfessorColaborador(request);
            } else {
                throw new Exception("Nenhuma operação executada");
            }

            if (!(envelopeRespostaDTO.getMeta() != null && envelopeRespostaDTO.getMeta().getError() != null)) {
                response.setStatus(HttpServletResponse.SC_OK);
            }
            response.getWriter().append(new JSONObject(envelopeRespostaDTO).toString());
        } catch (Exception ex) {
            ex.printStackTrace();
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            response.getWriter().append(new JSONObject(EnvelopeRespostaDTO.erro("Erro", ex.getMessage())).toString());
        }
    }

    private Connection obterConexao(ServletRequest request, String chave) throws Exception {
        if (!UteisValidacao.emptyString(chave)) {
            return new DAO().obterConexaoEspecifica(chave.trim());
        }

        String key = request.getParameter("key");
        if (UteisValidacao.emptyString(key)) {
            throw new Exception("Chave não informada.");
        }
        return new DAO().obterConexaoEspecifica(key.trim());
    }

    private void finalizarConexao(Connection con) {
        try {
            if (con != null) {
                con.close();
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    private String obterBody(ServletRequest request) throws Exception {
        StringBuffer body = new StringBuffer();
        try {
            InputStream inputStream = request.getInputStream();
            BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream, StandardCharsets.UTF_8));
            String line = null;

            while ((line = reader.readLine()) != null) {
                body.append(line);
            }
            return body.toString();
        } catch (Exception ex) {
            ex.printStackTrace();
            throw new Exception("Erro body: " + ex.getMessage());
        }
    }

    private EnvelopeRespostaDTO recursoPadraoTelaAluno(ServletRequest request) throws Exception {
        Connection con = null;
        try {
            String chave = request.getParameter("key");
            if (UteisValidacao.emptyString(chave)) {
                throw new Exception("Chave informada");
            }
            con = obterConexao(request, chave);
            String ret = ProcessoRecursoPadrao.processarTelaAluno(chave, con);
            return EnvelopeRespostaDTO.of(ret);
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        } finally {
            finalizarConexao(con);
        }
    }

    private EnvelopeRespostaDTO sincronizarProfessorColaborador(ServletRequest request) throws Exception {
        try {
            String chave = request.getParameter("key");
            if (UteisValidacao.emptyString(chave)) {
                throw new Exception("Chave informada");
            }
            String ret = ProcessoSincronizarColaboradoresTreino.processar(chave);
            return EnvelopeRespostaDTO.of(ret);
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        }
    }
}

package servlet.arquitetura;

import br.com.pactosolucoes.enumeradores.TipoColaboradorEnum;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import com.fasterxml.jackson.databind.ObjectMapper;
import negocio.comuns.arquitetura.UsuarioEmailVO;
import negocio.comuns.arquitetura.UsuarioPerfilAcessoVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.EmailVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.TipoColaboradorVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.facade.jdbc.arquitetura.UsuarioEmail;
import negocio.facade.jdbc.basico.TipoColaborador;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.Date;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.sql.Connection;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

public class UsuarioServletV4 extends SuperServlet {

    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        try {
            processRequest(request, response);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    protected void processRequest(HttpServletRequest request, HttpServletResponse response) throws IOException {
        try {
            response.addHeader("Access-Control-Allow-Origin", "*");
            response.addHeader("Access-Control-Allow-Methods", "POST");
            response.setContentType("application/json");

            if (request.getParameter("empresa") == null || request.getParameter("chave") == null) {
                response.getWriter().append("{\"status\": \"erro:\", \"error\": \"URL incompativel (empresa ou chave faltando na url).\"}");
                return;
            }
            String chave = request.getParameter("chave");
            Integer empresa = Integer.valueOf(request.getParameter("empresa"));

            ObjectMapper objectMapper = new ObjectMapper();
            UsuarioVO usuarioVO = objectMapper.readValue(request.getReader(), UsuarioVO.class);

            Connection con = new DAO().obterConexaoEspecifica(chave);

            Usuario usuarioDao = new Usuario(con);
            UsuarioEmail usuarioEmailDAO = new UsuarioEmail(con);
            TipoColaborador tipoColaborador = new TipoColaborador(con);

            UsuarioVO usuarioPactooBRVO = usuarioDao.consultarPorUsername("PACTOBR", Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            List<UsuarioPerfilAcessoVO> perfilAcessoVO = usuarioPactooBRVO.getUsuarioPerfilAcessoVOs().stream().filter(perfil -> perfil.getEmpresa().getCodigo().equals(empresa)).collect(Collectors.toList());
            usuarioVO.setUsuarioPerfilAcessoVOs(perfilAcessoVO);
            usuarioVO.setPerfilTw(usuarioPactooBRVO.getPerfilTw());
            EmailVO emailVO = new EmailVO();
            emailVO.setEmail(usuarioVO.getEmail());
            usuarioVO.setTipoUsuario("NC");
            usuarioVO.getColaboradorVO().getPessoa().setNome(usuarioVO.getNome());
            //toodo: qual a data de nascimento?
            LocalDate localDate = LocalDate.of(1990, 1, 1);
            Date dataNasc = Date.from(localDate.atStartOfDay(ZoneId.systemDefault()).toInstant());
            usuarioVO.getColaboradorVO().getPessoa().setDataNasc(dataNasc);
            //toodo: qual CPF?
            usuarioVO.getColaboradorVO().getPessoa().setCfp("12345678901");
            usuarioVO.setAdministrador(false);
            EmpresaVO empresaVO = new EmpresaVO();
            empresaVO.setCodigo(empresa);
            usuarioVO.getColaboradorVO().setEmpresa(empresaVO);
            usuarioVO.getColaboradorVO().setSituacao("AT");
            usuarioVO.getColaboradorVO().setTipoColaborador(TipoColaboradorEnum.CONSULTOR.getSigla());
            List<TipoColaboradorVO> lista = new ArrayList<>();
            TipoColaboradorVO consultor = new TipoColaboradorVO();

            usuarioVO.getColaboradorVO().getPessoa().setEmail(usuarioVO.getEmail());
            usuarioVO.setEmailVO(emailVO);
            usuarioVO.setUsuarioHorarioAcessoSistemaVOs(usuarioPactooBRVO.getUsuarioHorarioAcessoSistemaVOs());

            usuarioDao.incluir(usuarioVO);

            UsuarioVO usuarioPactoConversasVO = usuarioDao.consultarPorUsername("pactoconversas", Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            consultor.setColaborador(usuarioPactoConversasVO.getColaboradorVO().getCodigo());
            consultor.setDescricao(TipoColaboradorEnum.CONSULTOR.getSigla());
            tipoColaborador.incluir(consultor);

            UsuarioEmailVO usuarioEmailVO = new UsuarioEmailVO();
            usuarioEmailVO.setUsuario(usuarioVO.getCodigo());
            usuarioEmailVO.setEmail(usuarioVO.getEmail());
            usuarioEmailVO.setVerificado(true);
            usuarioEmailDAO.incluir(usuarioEmailVO);

            response.getWriter().append("{\"status\": \"sucesso\", \"mensagem\": \"Salvo com Sucesso\"}");
        } catch (Exception e) {
            if (e.getMessage() != null)
                response.getWriter().append("{\"status\": \"error\", \"erro\": \"").append(e.getMessage()).append("\"}");
            else
                response.getWriter().append("{" + "\"status\": \"error\",\"error\": \"Erro ao tentar salvar usuario\"}");
            e.printStackTrace();
        }

    }

}


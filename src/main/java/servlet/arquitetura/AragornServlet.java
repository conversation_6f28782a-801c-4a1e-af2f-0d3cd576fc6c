package servlet.arquitetura;

import br.com.pactosolucoes.integracao.aragorn.MigradorAragorn;
import br.com.pactosolucoes.integracao.aragorn.RemoverCartao;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.facade.jdbc.basico.ConfiguracaoSistema;
import org.json.JSONObject;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.sql.Connection;

public class AragornServlet extends SuperServlet {

    @Override
    protected void processRequest(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {

        response.addHeader("Access-Control-Allow-Origin", "*");
        response.addHeader("Access-Control-Allow-Methods", "GET");
        response.setContentType("application/json");

        String path = request.getServletPath();
        String[] pathParts = path.split("/");
        String modulo = pathParts[2];
        String recurso = pathParts[3];

        String chave = request.getParameter("chave");
        String method = request.getMethod();

        try {
            if (!method.equalsIgnoreCase("POST")) {
                response.setStatus(400);
                JSONObject json = new JSONObject();
                json.put("sucesso", false);
                json.put("mensagem", "Methodo não suportado para esse recurso");
                response.getWriter().append(json.toString());
                return;
            }

            switch (recurso) {
                case "ativar":
                    ativar(response, chave);
                    break;
                case "migrador":
                    migrador(response, chave);
                    break;
                case "removerHistorico":
                    removerHistorico(response, chave);
                    break;
                case "revert":
                    revertAutorizacao(response, chave);
                    break;
            }
        } catch (Exception e) {
            processarErro(e, request, response, null);
        }
    }

    private ConfiguracaoSistema getConfiguracaoSistemaDao(String key) throws Exception {
        return new ConfiguracaoSistema(new DAO().obterConexaoEspecifica(key));
    }

    private void ativar(HttpServletResponse response, String key) throws Exception {
//        ConfiguracaoSistema configDAO = getConfiguracaoSistemaDao(key);
//        boolean aragorn = configDAO.isUtilizarAragorn();
//        configDAO.alterarUtilizarAragorn(!aragorn);
//        aragorn = configDAO.isUtilizarAragorn();
//
//        JSONObject json = new JSONObject();
//        json.put("utilizarAragorn", aragorn);
//        response.getWriter().append(json.toString());
    }

    private void migrador(HttpServletResponse response, String key) throws Exception {
        MigradorAragorn migradorAragorn = new MigradorAragorn(new DAO().obterConexaoEspecifica(key));
        JSONObject cliente = migradorAragorn.migrarAutorizacoesClientes();
        JSONObject colaborador = migradorAragorn.migrarAutorizacoesColaboradores();
        migradorAragorn = null;

        JSONObject json = new JSONObject();
        json.put("autorizacaoCliente", cliente);
        json.put("autorizacaoColaborador", colaborador);
        response.getWriter().append(json.toString());
    }

    private void removerHistorico(HttpServletResponse response, String key) throws Exception {
        Connection con = null;
        JSONObject json = new JSONObject();
        try {
            con =  new DAO().obterConexaoEspecifica(key);
            RemoverCartao removerCartao = new RemoverCartao(con);
            removerCartao.removerCartoes();
            removerCartao = null;
            json.put("msg", "Processo executado.");
        } catch (Exception ex){
            json.put("msg", ex.getMessage());
        } finally {
            if (con != null) {
                con.close();
            }
        }
        response.getWriter().append(json.toString());
    }

    private void revertAutorizacao(HttpServletResponse response, String key) throws Exception {
//        MigradorAragorn migradorAragorn = new MigradorAragorn(new DAO().obterConexaoEspecifica(key), false);
//        String mensagem = migradorAragorn.revertAutorizacao();
//
//        JSONObject json = new JSONObject();
//        json.put("mensagem", mensagem);
//        response.getWriter().append(json.toString());
    }
}

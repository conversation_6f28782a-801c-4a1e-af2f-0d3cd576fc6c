package servlet.arquitetura;

import br.com.pactosolucoes.enumeradores.FasesCRMEnum;
import br.com.pactosolucoes.enumeradores.TipoAgendaEnum;
import br.com.pactosolucoes.enumeradores.TipoColaboradorEnum;
import br.com.pactosolucoes.enumeradores.TipoContatoCRM;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import controle.crm.MetaCRMControle;
import negocio.comuns.arquitetura.EnvelopeRespostaDTO;
import negocio.comuns.arquitetura.UsuarioPerfilAcessoVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.crm.*;
import negocio.comuns.plano.ModalidadeVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.ControleAcesso;
import negocio.facade.jdbc.arquitetura.Permissao;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.facade.jdbc.arquitetura.UsuarioPerfilAcesso;
import negocio.facade.jdbc.basico.Cliente;
import negocio.facade.jdbc.basico.ConfiguracaoSistema;
import negocio.facade.jdbc.crm.*;
import negocio.facade.jdbc.utilitarias.Conexao;
import negocio.interfaces.arquitetura.UsuarioInterfaceFacade;
import negocio.interfaces.basico.ClienteInterfaceFacade;
import negocio.interfaces.crm.ConfiguracaoCrmIAInterfaceFacade;
import negocio.interfaces.crm.LeadInterfaceFacade;
import negocio.interfaces.crm.PassivoInterfaceFacade;
import org.json.JSONObject;

import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.sql.Connection;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Iterator;


public class AgendamentoRegistroContatoAIServlet extends SuperServlet {

    @Override
    protected void processRequest(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        Connection con = null;
        try {
            response.setHeader("Access-Control-Allow-Origin", "*");
            response.setHeader("Access-Control-Allow-Methods", "POST, OPTIONS");
            response.setHeader("Access-Control-Max-Age", "3600");
            response.setHeader("Access-Control-Allow-Headers", "Authorization, content-type, xsrf-token");
            response.addHeader("Access-Control-Expose-Headers", "xsrf-token");
            response.setContentType("application/json");
            String key = request.getParameter("chave");
            if (UteisValidacao.emptyString(key))
                throw new Exception("Key não informada.");


            try {
                con = new DAO().obterConexaoEspecifica(key);
                Conexao.guardarConexaoForJ2SE(con);
            } catch (Exception e) {
                response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
                response.getWriter().append(new JSONObject(EnvelopeRespostaDTO.erro("Erro", "Chave " + key + " não encontrada.")).toString());
                return;
            }

            salvarRegistroObjecao(request, con);
            response.setStatus(HttpServletResponse.SC_OK);
            response.getWriter().append("{\"mensagem\":\"Sucesso\"}");
        } catch (Exception ex) {
            ex.printStackTrace();
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            response.getWriter().append(new JSONObject(EnvelopeRespostaDTO.erro("Erro", ex.getMessage())).toString());
        } finally {
            finalizarConexao(con);
        }
    }

    public void salvarRegistroObjecao(ServletRequest request, Connection con) throws Exception {
        HistoricoContato historicoContatoDAO = null;
        Agenda agendaDAO = null;
        ConfiguracaoSistemaCRM configuracaoSistemaCRMDAO = null;
        String body = request.getReader().lines().collect(java.util.stream.Collectors.joining());
        JSONObject json = new JSONObject(body);
        String observacao = json.optString("observacao");
        Date dataAgendamento = toDate(json.optString("dataAgendamento"));
        String tipoAgendamento = json.optString("tipoAgendamento");
        String horario = json.optString("horario");
        String tipoProfessor = json.optString("tipoProfessor");
        Integer empresa = json.optInt("empresa");
        Integer codigoProfessor = json.optInt("codigoProfessor");
        Integer codigoModalidade = json.optInt("codigoModalidade");
        Integer codigoCliente = json.optInt("codigoCliente");
        Integer codigoLead = json.optInt("codigoLead");
        AgendaVO agendaVO = new AgendaVO();
        HistoricoContatoVO historicoContatoVO = new HistoricoContatoVO();
        EmpresaVO empresaVO = new EmpresaVO();
        MalaDiretaVO malaDiretaVO = new MalaDiretaVO();
        historicoContatoDAO = new HistoricoContato(con);
        agendaDAO = new Agenda(con);
        configuracaoSistemaCRMDAO = new ConfiguracaoSistemaCRM(con);
        String faseAtual = json.optString("faseAtual");
        String siglaFaseAtual  = FasesCRMEnum.getSiglaPorString(faseAtual);
        String[] horaMinuto = horario.split(":");

        validarCampos(observacao, empresa, agendaVO, siglaFaseAtual, horaMinuto, dataAgendamento, tipoProfessor, codigoProfessor, codigoModalidade, tipoAgendamento);
        popularVOs(con, historicoContatoVO, observacao, codigoLead, codigoCliente, siglaFaseAtual, tipoAgendamento, horaMinuto, agendaVO, dataAgendamento, tipoProfessor, codigoProfessor, codigoModalidade);

        agendaDAO.verificaExisteAgendamentoDiaHoraMinuto(historicoContatoVO, agendaVO);
        ConfiguracaoSistemaCRMVO configuracaoSistemaCRMVO = configuracaoSistemaCRMDAO.consultarConfiguracaoSistemaCRM(Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        Date dataLimite = configuracaoSistemaCRMDAO.obterDataCalculadaDiasUteis(Calendario.hoje(), false, configuracaoSistemaCRMVO.getNrDiasLimiteAgendamentoFuturo(), empresaVO);

        if (Calendario.maior(agendaVO.getDataAgendamento(), dataLimite) && (agendaVO.getTipoAgendamento().equals(TipoAgendaEnum.AULA_EXPERIMENTAL.getId()) || agendaVO.getTipoAgendamento().equals(TipoAgendaEnum.VISITA.getId())))
            throw new ConsistirException("Agendamento com data superior a " + configuracaoSistemaCRMVO.getNrDiasLimiteAgendamentoFuturoDescricao() + " só pode ser do tipo Ligação. Veja campo 'Número de dias limite para agendamento futuro' nas configurações do CRM.");


        if (Calendario.igual(agendaVO.getDataAgendamento(), Calendario.hoje()) && agendaVO.getTipoAgendamento().equals(TipoAgendaEnum.LIGACAO.getId()) && historicoContatoVO.getFase().equals(FasesCRMEnum.AGENDAMENTOS_LIGACOES.getSigla())) {
            throw new ConsistirException("Agendamento de ligações só é possível para datas futuras, se preferir crie um simples registro ou um agendamento de visita! ");
        }


        UsuarioVO usuarioVO = getUsuarioPactoConversa(empresa, con);

        if (validarMetaNaoAberta(empresa, usuarioVO.getCodigo(), con))
            throw new Exception("Para realizar contato abra primeiramente a meta de hoje.");

        validarPermissao(empresa, "RealizarContato", "7.23 - Realizar Contato", usuarioVO, con);

        setUsuario(usuarioVO, historicoContatoVO);
        malaDiretaVO.setUsuarioVO(usuarioVO);

        if (usuarioVO != null)
            historicoContatoDAO.gravarHistoricoContatoComColaborador(FasesCRMEnum.AGENDAMENTO.getSigla(), historicoContatoVO, agendaVO, malaDiretaVO, empresaVO.getCodigo(), usuarioVO);
        else
            historicoContatoDAO.gravarHistoricoContato(FasesCRMEnum.AGENDAMENTO.getSigla(), historicoContatoVO, agendaVO, malaDiretaVO, empresa);

    }

    private Date toDate(String dataAgendamento) {
        try {
            return new SimpleDateFormat("dd/MM/yyyy").parse(dataAgendamento);
        } catch (ParseException e) {
            e.printStackTrace();
            return null;
        }
    }

    private void validarCampos(String observacao, Integer empresa, AgendaVO agendaVO, String siglaFaseAtual, String[] horaMinuto, Date dataAgendamento, String tipoProfessor, Integer codigoProfessor, Integer codigoModalidade, String tipoAgendamento) throws Exception {

        if (observacao.isEmpty())
            throw new Exception("Informe o comentário.");

        if (empresa == 0)
            throw new Exception("Informe a empresa valida.");

        if (siglaFaseAtual.isEmpty())
            throw new Exception("Informe a sigla fase atual no atributo 'siglaFaseAtual'.");

        if (horaMinuto.length != 2)
            throw new Exception("Informe o horário no formato HH:MM.");

        if (dataAgendamento == null)
            throw new Exception("Informe a data de agendamento válida no formato DD/MM/YYYY.");

        if (tipoAgendamento.equals(TipoAgendaEnum.AULA_EXPERIMENTAL.getId())) {

            if (tipoProfessor.isEmpty())
                throw new Exception("Informe o tipo de professor ex. " + TipoColaboradorEnum.PROFESSOR.getDescricao() + " -> " + TipoColaboradorEnum.PROFESSOR.getSigla() + " , " + TipoColaboradorEnum.PROFESSOR_TREINO.getDescricao() + " -> " + TipoColaboradorEnum.PROFESSOR_TREINO.getSigla());

            if (codigoProfessor == 0)
                throw new Exception("Informe o código do professor.");

            if (codigoModalidade == 0)
                throw new Exception("Informe o código da modalidade.");
        }
    }

    public void validarHorario(AgendaVO agendaVO) throws ConsistirException {
        if (Integer.parseInt(agendaVO.getHora()) > 23) {
            throw new ConsistirException(("A Hora não pode ser superior a 23."));
        }
        if (Integer.parseInt(agendaVO.getMinuto()) > 59) {
            throw new ConsistirException(("Os Minutos não pode ser superior a 59."));
        }
    }

    private void popularVOs(Connection con, HistoricoContatoVO historicoContatoVO, String observacao, Integer codigoLead, Integer codigoCliente, String siglaFaseAtual, String tipoAgendamento, String[] horaMinuto, AgendaVO agendaVO, Date dataAgendamento, String tipoProfessor, Integer codigoProfessor, Integer codigoModalidade) throws Exception {
        /**
         * Metodos que deve ser analisados para esse save ...
         * Referencia para o {@link MetaCRMControle#confirmarSimplesRegistroMeta()}
         * Referencia para o {@link MetaCRMControle#gravarSimplesRegistro()}
         */
        PassivoVO passivoVO;
        ClienteVO clienteVO;
        historicoContatoVO.setContatoAvulso(false);
        historicoContatoVO.setObservacao(observacao);
        historicoContatoVO.setFase(siglaFaseAtual);
        historicoContatoVO.setTipoContato(TipoContatoCRM.CONTATO_WHATSAPP.getSigla());

        if (tipoAgendamento.equals(TipoAgendaEnum.AULA_EXPERIMENTAL.getId())) {
            agendaVO.setCodigoProfessor(codigoProfessor);
            agendaVO.getModalidade().setCodigo(codigoModalidade);
            agendaVO.setTipoProfessor(tipoProfessor);
        }

        agendaVO.setDataAgendamento(dataAgendamento);
        agendaVO.setHora(horaMinuto[0]);
        agendaVO.setMinuto(horaMinuto[1]);
        validarHorario(agendaVO);
        agendaVO.setTipoAgendamento(tipoAgendamento);

        if (!agendaVO.getTipoAgendamento().equals(TipoAgendaEnum.AULA_EXPERIMENTAL.getId()))
            agendaVO.setModalidade(new ModalidadeVO());


        if (codigoLead != 0) {
            LeadInterfaceFacade leadInterfaceFacade = new Lead(con);
            LeadVO leadVO = leadInterfaceFacade.consultarPorChavePrimaria(codigoLead, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            PassivoInterfaceFacade passivoInterfaceFacade = new Passivo(con);
            passivoVO = passivoInterfaceFacade.consultarPorChavePrimaria(leadVO.getPassivo().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            historicoContatoVO.setPassivoVO(passivoVO);
        } else if (codigoCliente != 0) {
            ClienteInterfaceFacade clienteInterfaceFacade = new Cliente(con);
            clienteVO = clienteInterfaceFacade.consultarPorChavePrimaria(codigoCliente, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            historicoContatoVO.setClienteVO(clienteVO);
        } else
            throw new Exception("Informe o codigoCliente ou codigoLead.");
    }

    public void setUsuario(UsuarioVO usuario, HistoricoContatoVO historicoContatoVO) {
        historicoContatoVO.setUsuarioVO(usuario);
        historicoContatoVO.setColaboradorResponsavel(usuario);
        historicoContatoVO.setResponsavelCadastro(usuario);
    }

    private void finalizarConexao(Connection con) {
        try {
            if (con != null) {
                con.close();
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    private boolean validarMetaNaoAberta(Integer empresa, Integer codigUsuarioPactoConversa, Connection con) throws Exception {
        ConfiguracaoSistema configuracaoSistemaDAO = new ConfiguracaoSistema(con);
        AberturaMeta aberturaMetaDAO = new AberturaMeta(con);
        boolean validarCfgValidacaoContato = configuracaoSistemaDAO.verificarValidacaoContatoMeta();
        return validarCfgValidacaoContato && !aberturaMetaDAO.consultarAberturaPorCodigoUsuario(codigUsuarioPactoConversa, empresa, Calendario.hoje());
    }

    public void validarPermissao(Integer empresa, String permissao, String descricao, UsuarioVO usuarioPactoConversa, Connection con) throws Exception {
        Permissao permissaoDAO;
        ControleAcesso controleAcessoDAO;
        UsuarioPerfilAcesso usuarioPerfilAcessoDAO;
        try {
            permissaoDAO = new Permissao(con);
            controleAcessoDAO = new ControleAcesso(con);
            usuarioPerfilAcessoDAO = new UsuarioPerfilAcesso(con);
            usuarioPactoConversa.setUsuarioPerfilAcessoVOs(usuarioPerfilAcessoDAO.consultarUsuarioPerfilAcesso(usuarioPactoConversa.getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));

            if (usuarioPactoConversa.getUsuarioPerfilAcessoVOs().isEmpty()) {
                if (usuarioPactoConversa.getAdministrador()) {
                    return;
                }
                throw new Exception("O usuario informado não tem nenhum perfil acesso informado.");
            }
            Iterator i = usuarioPactoConversa.getUsuarioPerfilAcessoVOs().iterator();
            while (i.hasNext()) {
                UsuarioPerfilAcessoVO usuarioPerfilAcesso = (UsuarioPerfilAcessoVO) i.next();
                if (empresa.equals(usuarioPerfilAcesso.getEmpresa().getCodigo())) {
                    usuarioPerfilAcesso.getPerfilAcesso().setPermissaoVOs(permissaoDAO.consultarPermissaos(
                            usuarioPerfilAcesso.getPerfilAcesso().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                    controleAcessoDAO.verificarPermissaoUsuarioFuncionalidade(usuarioPerfilAcesso.getPerfilAcesso(),
                            usuarioPactoConversa, permissao, descricao);
                }
            }
        } finally {
            permissaoDAO = null;
            controleAcessoDAO = null;
            usuarioPerfilAcessoDAO = null;
        }
    }

    private UsuarioVO getUsuarioPactoConversa(Integer codigoEmpresa, Connection con) throws Exception {
        UsuarioInterfaceFacade usuarioService = new Usuario(con);
        ConfiguracaoCrmIAInterfaceFacade ConfiguracaoCrmIAInterfaceFacade = new ConfiguracaoCrmIA(con);
        ConfiguracaoCrmIAInterfaceFacade.consultarTodos();
        ConfiguracaoCrmIAVO configuracaoCrmIAVO = ConfiguracaoCrmIAInterfaceFacade.consultarPorEmpresa(codigoEmpresa);
        if (configuracaoCrmIAVO == null)
            return null;
        return usuarioService.consultarPorUsername(configuracaoCrmIAVO.getPactoConversasLogin(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
    }

}
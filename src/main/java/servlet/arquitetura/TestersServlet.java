package servlet.arquitetura;

import br.com.pactosolucoes.oamd.controle.basico.BetaTestersService;
import controle.arquitetura.servlet.update.UpdateServlet;
import org.json.JSONObject;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.HashMap;

public class TestersServlet extends SuperServlet {
    @Override
    protected void processRequest(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {

        response.addHeader("Access-Control-Allow-Origin", "*");
        response.addHeader("Access-Control-Allow-Methods", "GET");
        response.setContentType("application/json");

        String reinit = request.getParameter("reinit");
        String applyAll = request.getParameter("all");
        String method = request.getMethod();
        try {
            switch (method) {
                case "GET":
                    if(reinit != null && reinit.equals("y")){
                        BetaTestersService betaTestersService = new BetaTestersService();
                        betaTestersService.init();
                        HashMap hashMap = new HashMap();
                        hashMap.put("reinit", "y");
                        UpdateServlet.propagarRequestInner("", response.getWriter(), hashMap, request.getRequestURL().toString());
                    }

                    if(applyAll != null){
                        BetaTestersService.applyAll =  applyAll.equals("y");
                    }

                    JSONObject testersJSON = new JSONObject();
                    testersJSON.put("testers", BetaTestersService.applyAll ? "para todas as chaves" : BetaTestersService.testers);
                    response.getWriter().append(testersJSON.toString());
                    break;
                default:
                    processarErro(
                            new UnsupportedOperationException("Methodo não suportado para esse recurso"),
                            request,
                            response,
                            null);
            }
        } catch (Exception e) {
            processarErro(e, request, response, null);
        }
    }



}

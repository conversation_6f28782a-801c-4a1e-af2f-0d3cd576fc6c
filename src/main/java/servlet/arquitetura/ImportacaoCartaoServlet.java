package servlet.arquitetura;

import br.com.pactosolucoes.autorizacaocobranca.modelo.AutorizacaoCobrancaClienteVO;
import br.com.pactosolucoes.comuns.json.JSONMapper;
import br.com.pactosolucoes.encrypt.gpg.PgpEncryption;
import br.com.pactosolucoes.enumeradores.TipoImportacaoCartaoEnum;
import br.com.pactosolucoes.integracao.aragorn.ImportacaoCartaoAPIDTO;
import br.com.pactosolucoes.integracao.aragorn.ImportacaoCartaoRespostaDTO;
import br.com.pactosolucoes.integracao.aragorn.ImportarCartaoService;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.arquitetura.EnvelopeRespostaDTO;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.json.JSONObject;
import servicos.propriedades.PropsService;

import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.BufferedReader;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.security.Security;
import java.sql.Connection;

/**
 * #########################################################################################
 * ######################################## ATENÇÃO ########################################
 * <p>
 * ESSA CLASSE FOI CRIADA COM O OBJETIVO DE CRIAR UM SERVLET PARA IMPORTAÇAÕ DE CARTÃO DE CRÉDITO
 * <p>
 * SÓ É ACEITO REQUISAÇÃO POST !
 * <p>
 * #########################################################################################
 * #########################################################################################
 */
public class ImportacaoCartaoServlet extends SuperServlet {

    @Override
    protected void processRequest(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        try {
            response.setHeader("Access-Control-Allow-Origin", "*");
            response.setHeader("Access-Control-Allow-Methods", "GET,POST");
            response.setHeader("Access-Control-Allow-Headers", "*");
            response.setContentType("application/json");

            String bearerApiToken = request.getHeader("Authorization");
            if (UteisValidacao.emptyString(bearerApiToken) ||
                    !bearerApiToken.replace("Bearer", "").
                            replace("Basic", "").
                            replace(" ", "").
                            equals(PropsService.getPropertyValue(PropsService.tokenImportCc))) {
                throw new Exception("Token invalido!");
            }

            JSONObject jsonBody = new JSONObject(obterBody(request));
            String dadosCripto = jsonBody.getString("data").replace(" ", "\n");

            String arquivoChaveDescripto = PropsService.getPropertyValue(PropsService.chaveCriptoImportCc);
            String chaveCripto = Uteis.readLineByLineJava8(arquivoChaveDescripto);
            InputStream chave = new ByteArrayInputStream(chaveCripto.getBytes());

            byte[] decrypt;
            try {
                Security.addProvider(new BouncyCastleProvider());
                decrypt = PgpEncryption.decrypt(dadosCripto.getBytes(), chave, "", false);
//                decrypt = PgpEncryption.decryptNoPassword(dadosCripto.getBytes(), chave);
            } catch (NoClassDefFoundError ncdf) {
                ncdf.printStackTrace();
                throw new Exception(ncdf.getMessage());
            }

            StringBuilder dadosDecrypt = new StringBuilder(new String(decrypt, StandardCharsets.UTF_8));
            ImportacaoCartaoAPIDTO cartaoAPIDTO = JSONMapper.getObject(new JSONObject(dadosDecrypt.toString()), ImportacaoCartaoAPIDTO.class);

            if (UteisValidacao.emptyString(cartaoAPIDTO.getKey())) {
                throw new Exception("Key não informado");
            }
            if (UteisValidacao.emptyString(cartaoAPIDTO.getTipo())) {
                throw new Exception("Tipo não informado");
            }
            if (UteisValidacao.emptyNumber(cartaoAPIDTO.getEmpresa())) {
                throw new Exception("Cod Empresa não informado");
            }
            if (UteisValidacao.emptyNumber(cartaoAPIDTO.getConvenioCobranca())) {
                throw new Exception("Cod Convenio Cobranca não informado");
            }

            EnvelopeRespostaDTO envelopeRespostaDTO;
            if (cartaoAPIDTO.getTipo().equalsIgnoreCase(TipoImportacaoCartaoEnum.CLOUD_GYM_8.name())) {
                envelopeRespostaDTO = importarCartaoCloudGym(cartaoAPIDTO, request);
            } else {
                throw new Exception("Nenhuma operação executada");
            }

            response.setStatus(HttpServletResponse.SC_OK);
            response.getWriter().append(new JSONObject(envelopeRespostaDTO).toString());
        } catch (Exception ex) {
            ex.printStackTrace();
            if (ex.getMessage().toLowerCase().contains("token invalido")) {
                response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
            } else {
                response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            }
            response.getWriter().append(new JSONObject(EnvelopeRespostaDTO.erro("Erro", ex.getMessage())).toString());
        }
    }

    private String obterBody(ServletRequest request) throws Exception {
        StringBuffer body = new StringBuffer();
        try {
            InputStream inputStream = request.getInputStream();
            BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream, StandardCharsets.UTF_8));
            String line = null;

            while ((line = reader.readLine()) != null) {
                body.append(line);
            }
            return body.toString();
        } catch (Exception ex) {
            ex.printStackTrace();
            throw new Exception("Erro body: " + ex.getMessage());
        }
    }

    private Connection obterConexao(ServletRequest request, String chave) throws Exception {
        if (!UteisValidacao.emptyString(chave)) {
            return new DAO().obterConexaoEspecifica(chave.trim());
        }
        return new DAO().obterConexaoEspecifica(obterChave(request));
    }

    private String obterChave(ServletRequest request) throws Exception {
        String key = request.getParameter("key");
        if (UteisValidacao.emptyString(key)) {
            throw new Exception("Chave não informada.");
        }
        return key.trim();
    }

    private void finalizarConexao(Connection con) {
        try {
            if (con != null) {
                con.close();
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    private EnvelopeRespostaDTO importarCartaoCloudGym(ImportacaoCartaoAPIDTO cartaoAPIDTO, HttpServletRequest request) throws Exception {

        String bearerApiToken = request.getHeader("Authorization");
        String chave = cartaoAPIDTO.getKey();
        if (UteisValidacao.emptyString(chave)) {
            throw new Exception("Chave não informada");
        }

        try (Connection con = obterConexao(request, chave)) {
            ImportarCartaoService service;
            try {
                service = new ImportarCartaoService(cartaoAPIDTO.getKey(), cartaoAPIDTO.getEmpresa(), cartaoAPIDTO.getConvenioCobranca(),
                        TipoImportacaoCartaoEnum.CLOUD_GYM_8, false, bearerApiToken, con);
                AutorizacaoCobrancaClienteVO obj = service.importarIndividual(cartaoAPIDTO.getCard(), con);
                ImportacaoCartaoRespostaDTO respostaDTO = new ImportacaoCartaoRespostaDTO();
                respostaDTO.setAutorizacao(obj.getCodigo());
                respostaDTO.setToken(Uteis.mascararDado(obj.getTokenAragorn(), 5, true));
                return EnvelopeRespostaDTO.of(respostaDTO.toString());
            } finally {
                service = null;
            }
        }
    }
}

package servlet.arquitetura;

import controle.arquitetura.security.LoginControle;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;

/*
 * Created by luiz on 19/02/2016.
 */

public class UsuarioPerfilAcessoServlet extends SuperServlet {
    @Override
    protected void processRequest(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        super.processRequest(request, response);
        PrintWriter out = response.getWriter();
        String json;
        try {
            String codigoPerfilAcesso = request.getParameter("codigoPerfilAcesso");
            String situacao = obterParametroString(request.getParameter("situacao"));
            String sEcho = obterParametroString(request.getParameter("sEcho"));
            Integer offset = obterParametro(request.getParameter("iDisplayStart"));
            Integer limit = obterParametro(request.getParameter("iDisplayLength"));
            String clausulaLike = obterParametroString(request.getParameter("sSearch"));
            Integer colOrdenar = obterParametro(request.getParameter("iSortCol_0"));
            String dirOrdenar = obterParametroString(request.getParameter("sSortDir_0"));
            json = ff(request).getPerfilAcesso().consultarUsuariosPerfilAcessoJSON(Integer.parseInt(codigoPerfilAcesso),situacao, sEcho, offset, limit, clausulaLike, colOrdenar, dirOrdenar);

        } catch (Exception e) {
            json = (e.getMessage() == null) ? NullPointerException.class.getName() : e.getMessage();
        }
        out.println(json);
        json = null;
    }
}
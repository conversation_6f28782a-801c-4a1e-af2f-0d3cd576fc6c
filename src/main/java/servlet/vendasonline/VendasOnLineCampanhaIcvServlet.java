package servlet.vendasonline;

import negocio.facade.jdbc.vendas.VendasOnlineCampanhaIcv;
import negocio.facade.jdbc.vendas.VendasOnlineCampanhaIcvTO;
import negocio.facade.jdbc.vendas.VendasOnlineCampanhaIcvVO;
import org.json.JSONObject;
import servlet.arquitetura.SuperServletPost;

import java.sql.Connection;

/**
 * Created by ulisses on 23/01/2023.
 */
public class VendasOnLineCampanhaIcvServlet extends SuperServletPost {

    @Override
    public Object tratarOperacao(String operacao)throws Exception{
        JSONObject objRetorno = null;
        if (operacao.equalsIgnoreCase("registrarInicioAcessoPagina")) {
            objRetorno = registrarInicioAcessoPagina(getCon(), getJsonBody());
        }else if (operacao.equalsIgnoreCase("consultarRegistroPagina")) {
            objRetorno = consultarRegistroPagina(getCon(), getJsonBody());
        }

        return objRetorno;
    }

    private JSONObject registrarInicioAcessoPagina(Connection con, JSONObject jsonBody) throws Exception {
        VendasOnlineCampanhaIcv vendasOnlineCampanhaIcvDao = null;
        try {
            vendasOnlineCampanhaIcvDao = new VendasOnlineCampanhaIcv(con);
            VendasOnlineCampanhaIcvVO  vendasOnlineCampanhaIcvVO = new VendasOnlineCampanhaIcvVO(jsonBody);
            vendasOnlineCampanhaIcvDao.incluir(vendasOnlineCampanhaIcvVO);

            JSONObject jsonObject = new JSONObject(new VendasOnlineCampanhaIcvTO(vendasOnlineCampanhaIcvVO));
            //jsonObject.put("codigo",vendasOnlineCampanhaIcvVO.getCodigo());
            return jsonObject;
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        } finally {
            vendasOnlineCampanhaIcvDao = null;
        }
    }

    private JSONObject consultarRegistroPagina(Connection con, JSONObject jsonBody) throws Exception {
        VendasOnlineCampanhaIcv vendasOnlineCampanhaIcvDao = null;
        try {
            vendasOnlineCampanhaIcvDao = new VendasOnlineCampanhaIcv(con);
            VendasOnlineCampanhaIcvVO  vendasOnlineCampanhaIcvVO = vendasOnlineCampanhaIcvDao.consultar(jsonBody.optInt("codigo"));
            JSONObject jsonObject = new JSONObject(new VendasOnlineCampanhaIcvTO(vendasOnlineCampanhaIcvVO));

            return jsonObject;
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        } finally {
            vendasOnlineCampanhaIcvDao = null;
        }
    }


}

package servlet.cliente;


import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.basico.ConfirmacaoEmailCompraVO;
import negocio.comuns.crm.ModeloMensagemVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.basico.ConfirmacaoEmailCompra;
import negocio.facade.jdbc.contrato.Contrato;
import negocio.facade.jdbc.crm.ModeloMensagem;
import negocio.facade.jdbc.utilitarias.Conexao;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.sql.Connection;


public class ConfirmacaoEmailCompraServlet extends HttpServlet {


    protected void processRequest(HttpServletRequest request, HttpServletResponse response) throws Exception {

        String token = request.getParameter("token");
        String key = request.getParameter("key");
        Connection con = null;
        DAO dao;
        response.setContentType("text/html;charset=UTF-8");
        dao = new DAO();
        con = dao.obterConexaoEspecifica(key);
        Conexao.guardarConexaoForJ2SE(key, con);

        try {

            ConfirmacaoEmailCompra confirmacaoEmailCompraDAO = new ConfirmacaoEmailCompra(con);
            ConfirmacaoEmailCompraVO obj = confirmacaoEmailCompraDAO.consultarCadastroEmailConfirmacaoCompra(token);
            ModeloMensagem modeloMensagemDao = new ModeloMensagem(con);
            ModeloMensagemVO msg = modeloMensagemDao.consultarPorTipo("EC", Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if(Calendario.menorOuIgualComHora(Uteis.somarDias(obj.getDataRegistro(), 7), Calendario.hoje())){
                  throw new Exception("O periodo de 7 dias para a confirmação expirou, faça a compra novamente");
            }
            Contrato contratoDao = new Contrato(con);
            contratoDao.gravarContratoSite(key, obj.getPlano(),obj.getCliente().getCodigo(), obj.getNrparcelasadesao(), obj.getNrparcelasproduto(), obj.getNumeroCupomDesconto(), true, true, obj.getNrParcelasPagamento(), false, 0, 0, false, null, obj.getDiaVencimento(), null);

            String site = msg.getUrlRedirecionamento();
            response.setStatus(response.SC_MOVED_TEMPORARILY);
            response.setHeader("Location", site);
        } catch (Exception e) {
            throw e;

        }
    }


    // <editor-fold defaultstate="collapsed" desc="HttpServlet methods. Click on the + sign on the left to edit the code.">

    /**
     * Handles the HTTP
     * <code>GET</code> method.
     *
     * @param request  servlet request
     * @param response servlet response
     * @throws ServletException if a servlet-specific error occurs
     * @throws IOException      if an I/O error occurs
     */
    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        try {
            processRequest(request, response);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * Handles the HTTP
     * <code>POST</code> method.
     *
     * @param request  servlet request
     * @param response servlet response
     * @throws ServletException if a servlet-specific error occurs
     * @throws IOException      if an I/O error occurs
     */
    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        try {
            processRequest(request, response);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * Returns a short description of the servlet.
     *
     * @return a String containing servlet description
     */
    @Override
    public String getServletInfo() {
        return "Short description";
    }// </editor-fold>
}

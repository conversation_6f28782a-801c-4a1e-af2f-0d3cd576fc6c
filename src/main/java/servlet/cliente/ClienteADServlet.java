package servlet.cliente;

import br.com.pactosolucoes.controle.json.SuperServletControle;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.facade.jdbc.basico.Cliente;
import negocio.interfaces.basico.ClienteInterfaceFacade;
import org.json.JSONObject;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.sql.Connection;
import java.util.ArrayList;
import java.util.List;

public class ClienteADServlet extends SuperServletControle {

    private static int mockin = 1;


    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        processRequest(request, response);
    }

    @Override
    protected void processRequest(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        super.processRequest(request, response);
        PrintWriter out = response.getWriter();
        String json;
        try {
            Connection con = new DAO().obterConexaoEspecifica(obterParametroString(request, "key"));
            String reset = obterParametroString(request, "todos");
            String mock = obterParametroString(request, "mock");

            if(mock == null){
                ClienteInterfaceFacade clienteDao = new Cliente(con);
                List<ClienteADJSON> fit = clienteDao.consultarClientesAD("FIT", reset != null && reset.equals("t"));
                json = fit.toString();
            }else{
                List<ClienteADJSON> fit = mockAD();
                json = fit.toString();
            }
        } catch (Exception e) {
            json = (e.getMessage() == null) ? NullPointerException.class.getName() : e.getMessage();
        }
        out.println(json);
        json = null;
    }

    public List<ClienteADJSON> mockAD(){
        List<ClienteADJSON> clientes = new ArrayList<ClienteADJSON>();
        switch (mockin){
            case 1:
                clientes.add(new ClienteADJSON("LUKE SKYWALKER", "00123", "6544", "adicionar"));
                clientes.add(new ClienteADJSON("JOHN WICK", "00485", "7788", "adicionar"));
                clientes.add(new ClienteADJSON("ROBERT LANGDON", "0077", "6544", "adicionar"));
                clientes.add(new ClienteADJSON("LEIA ORGANA", "04445", "7844", "adicionar"));
                mockin = 2;
                break;
            case 2:

                clientes.add(new ClienteADJSON("LUKE SKYWALKER", "00123", "6544", "remover"));
                clientes.add(new ClienteADJSON("JOHN WICK", "00485", "7788", "remover"));
                clientes.add(new ClienteADJSON("ROBERT LANGDON", "0077", "6544", "remover"));
                clientes.add(new ClienteADJSON("LEIA ORGANA", "04445", "7844", "remover"));

                clientes.add(new ClienteADJSON("LARA CROFT", "00998", "6004", "adicionar"));
                clientes.add(new ClienteADJSON("SARAH CONNOR", "004451", "0088", "adicionar"));
                clientes.add(new ClienteADJSON("OBAMA HUSSEIN", "0366", "6500", "adicionar"));
                clientes.add(new ClienteADJSON("ROMELU LUKAKU", "02245", "0004", "adicionar"));
                mockin = 3;
                break;
            case 3:

                clientes.add(new ClienteADJSON("LARA CROFT", "00998", "6004", "remover"));
                clientes.add(new ClienteADJSON("SARAH CONNOR", "004451", "0088", "remover"));
                clientes.add(new ClienteADJSON("OBAMA HUSSEIN", "0366", "6500", "remover"));
                clientes.add(new ClienteADJSON("ROMELU LUKAKU", "02245", "0004", "remover"));
                mockin = 1;
                break;
        }

        return clientes;
    }
}

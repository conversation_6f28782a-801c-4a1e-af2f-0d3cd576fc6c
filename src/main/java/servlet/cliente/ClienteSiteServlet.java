package servlet.cliente;

import acesso.webservice.DaoAuxiliar;
import br.com.pactosolucoes.controle.json.SuperServletControle;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import com.amazonaws.util.IOUtils;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.basico.webservice.IntegracaoCadastros;
import org.json.JSONObject;
import servicos.treino.TreinoService;
import servlet.arquitetura.SuperServlet;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.sql.Connection;
import java.sql.Date;

import static br.com.pactosolucoes.controle.json.SuperServletControle.STATUS_ERRO;
import static br.com.pactosolucoes.controle.json.SuperServletControle.STATUS_SUCESSO;

public class ClienteSiteServlet extends SuperServletControle {

    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        processRequest(request, response);
    }

    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        processRequest(request, response);
    }

    protected void processRequest(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {

        PrintWriter out = response.getWriter();
        response.addHeader("Access-Control-Allow-Origin", "*");
        response.addHeader("Access-Control-Allow-Methods", "GET,POST");
        response.setContentType("application/json");

        String path = request.getServletPath();
        String[] pathParts = path.split("/");
        String recurso = pathParts[3];

        String chave = request.getParameter("chave");
        Integer empresa = Integer.valueOf(request.getParameter("empresa"));
        JSONObject retorno = new JSONObject();
        try {
            switch (recurso){
                case "add":
                    String body = IOUtils.toString(request.getInputStream());
                    String mensagem = getService(chave).persistirClienteSite(empresa, new JSONObject(body));
                    if (mensagem.startsWith("ERRO")) {
                        throw new Exception(mensagem);
                    }
                    retorno.put(SuperServletControle.STATUS_SUCESSO, mensagem);
                    break;
            }
        }catch (Exception ex){
            ex.printStackTrace();
            retorno.put(STATUS_ERRO, UteisValidacao.emptyString(ex.getMessage()) ? "Erro interno" : ex.getMessage());
        }
        out.println(retorno);
    }

    private IntegracaoCadastros getService(String chave) throws Exception {
        return new IntegracaoCadastros(new DAO().obterConexaoEspecifica(chave));
    }

}

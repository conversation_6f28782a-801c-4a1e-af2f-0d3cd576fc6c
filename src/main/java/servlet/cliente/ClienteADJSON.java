package servlet.cliente;

import br.com.pactosolucoes.comuns.json.SuperJSON;

public class ClienteADJSON extends SuperJSON {

    public String matricula;
    public String nome;
    public String matriculaExterna;
    public String tipo;

    public ClienteADJSON() {

    }

    @Override
    public String toString(){
        return toJSON();
    }

    public ClienteADJSON(String nome, String matricula, String matriculaExterna, String tipo) {
        this.nome = nome;
        this.matricula = matricula;
        this.matriculaExterna = matriculaExterna;
        this.tipo = tipo;
    }

    public String getMatricula() {
        return matricula;
    }

    public void setMatricula(String matricula) {
        this.matricula = matricula;
    }

    public String getMatriculaExterna() {
        return matriculaExterna;
    }

    public void setMatriculaExterna(String matriculaExterna) {
        this.matriculaExterna = matriculaExterna;
    }

    public String getTipo() {
        return tipo;
    }

    public void setTipo(String tipo) {
        this.tipo = tipo;
    }
}

package servlet.cliente;

import br.com.pactosolucoes.atualizadb.processo.geolocalizacao.GoogleApiGeocodeService;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.facade.jdbc.basico.Empresa;
import org.json.JSONArray;
import org.json.JSONObject;
import servlet.arquitetura.SuperServlet;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

public class GeolocalizarRestServlet extends SuperServlet {


    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        processRequest(request, response, false);
    }

    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        processRequest(request, response, true);
    }

    protected void processRequest(HttpServletRequest request, HttpServletResponse response, Boolean processar) throws ServletException, IOException {

        response.addHeader("Access-Control-Allow-Origin", "*");
        response.addHeader("Access-Control-Allow-Methods", "GET");
        response.setContentType("application/json");

        String path = request.getServletPath();
        String[] pathParts = path.split("/");
        String recurso = pathParts[3];

        String chave = request.getParameter("chave");
        Integer empresa = Integer.valueOf(request.getParameter("empresa"));
        String limite = request.getParameter("limite");

        try {
            switch (recurso){
                case "lista-geolocalizar":
                    listaGeolocalizar(response, chave, empresa);
                    break;
                case "geolocalizar":
                    if(processar){
                        Boolean atualizouUm = getService(chave).processarComLimite(empresa, limite == null ? 100 : Integer.valueOf(limite));
                        JSONObject json = new JSONObject();
                        json.put("atualizar", atualizouUm);
                        response.getWriter().append(json.toString());
                    }else{
                        Boolean temEnderecoParaGeolocalizar = getService(chave).temEnderecoParaGeolocalizar(empresa);
                        JSONObject json = new JSONObject();
                        json.put("atualizar", temEnderecoParaGeolocalizar);
                        response.getWriter().append(json.toString());
                    }
                    break;
            }
        }catch (Exception e ){
            System.out.println("Erro na api rest "+recurso+". Message: "+e.getMessage());
            processarErro(e, request, response, null);
        }

    }

    private void listaGeolocalizar(HttpServletResponse response, String chave, Integer empresa) throws Exception {
        JSONArray array = getService(chave).arrayProcessar(empresa);
        response.getWriter().append(array.toString());
    }

    private GoogleApiGeocodeService getService(String chave) throws Exception {
        return new GoogleApiGeocodeService(new DAO().obterConexaoEspecifica(chave));
    }
}

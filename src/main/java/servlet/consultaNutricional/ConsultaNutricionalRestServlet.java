package servlet.consultaNutricional;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.plano.enumerador.TipoProduto;
import negocio.comuns.utilitarias.Calendario;
import negocio.facade.jdbc.basico.Cliente;
import negocio.facade.jdbc.plano.Produto;
import org.json.JSONObject;
import servlet.arquitetura.SuperServlet;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.sql.Connection;
import java.util.Date;

public class ConsultaNutricionalRestServlet extends SuperServlet {

    @Override
    protected void processRequest(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {

        response.addHeader("Access-Control-Allow-Origin", "*");
        response.addHeader("Access-Control-Allow-Methods", "GET");
        response.setContentType("application/json");
        String chave = request.getParameter("chave");
        Integer empresa = Integer.valueOf(request.getParameter("empresa"));
        Integer codCliente = Integer.valueOf(request.getParameter("codCliente"));
        Date hoje = Calendario.hoje();

        try {
            boolean empresaVendeConsultaNutricional;
            int quantidadeProdutoConsultaNutricionalVendaAvulsa;
            int quantidadeProdutoConsultaNutricionalContrato;
            try (Connection getConnection = getCon(chave)) {
                Cliente cliente = new Cliente(getConnection);
                Produto produto = new Produto(getConnection);
                empresaVendeConsultaNutricional = produto.consultarExistePorCodigoTipoProduto(TipoProduto.CONSULTA_NUTRICIONAL.getCodigo());
                quantidadeProdutoConsultaNutricionalVendaAvulsa = cliente.consultarQuantidadeProdutoVigenteVendaAvulsa(TipoProduto.CONSULTA_NUTRICIONAL.getCodigo(), hoje, codCliente, empresa, false);
                quantidadeProdutoConsultaNutricionalContrato = cliente.consultarQuantidadeProdutoVigenteContrato(TipoProduto.CONSULTA_NUTRICIONAL.getCodigo(), hoje, codCliente, empresa, false);
            }

            JSONObject jsonBool = new JSONObject();
            jsonBool.put("empresaVendeConsultaNutricional",empresaVendeConsultaNutricional);
            jsonBool.put("quantidadeProdutoConsultaNutricionalTotal", quantidadeProdutoConsultaNutricionalVendaAvulsa + quantidadeProdutoConsultaNutricionalContrato);
            jsonBool.put("quantidadeProdutoConsultaNutricionalVendaAvulsa", quantidadeProdutoConsultaNutricionalVendaAvulsa);
            jsonBool.put("quantidadeProdutoConsultaNutricionalContrato", quantidadeProdutoConsultaNutricionalContrato);
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("retorno",jsonBool);
            response.getWriter().append(jsonObject.toString());
        }catch (Exception e ){
            System.out.println("Erro na api rest ConsultaNutricional. Message: " + e.getMessage());
            processarErro(e, request, response, null);
        }

    }

    private Connection getCon(String chave) throws Exception {
        return new DAO().obterConexaoEspecifica(chave);
    }

}

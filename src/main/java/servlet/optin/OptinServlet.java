package servlet.optin;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import br.com.pactosolucoes.optin.servico.impl.OptinServiceImpl;
import negocio.comuns.crm.optin.OptinVO;
import negocio.comuns.utilitarias.UteisValidacao;
import org.joda.time.LocalDate;
import org.json.JSONObject;
import servlet.arquitetura.SuperServlet;
import servlet.crm.optin.OptinDTO;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.sql.Connection;
import java.sql.Date;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.Types;
import java.util.Objects;

import static java.lang.String.valueOf;

/**
 * Created with IntelliJ IDEA.
 * User: carlospereira
 * Date: 30/06/22
 * To change this template use File | Settings | File Templates.
 */

public class OptinServlet extends SuperServlet {

    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        processRequest(request, response);
    }

    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        processRequest(request, response);
    }

    @Override
    protected void doPut(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        processRequest(request, response);
    }

    @Override
    protected void processRequest(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {

        Connection con = null;

        try {
            response.addHeader("Access-Control-Allow-Origin", "*");
            response.addHeader("Access-Control-Allow-Methods", "GET");
            response.setContentType("application/json");

            String key = extrairChave(request);
            OptinDTO optinDTO = extrairDTO(request);
            String operacao = extrairOperacao(request);

            OptinServiceImpl optinService = new OptinServiceImpl(new DAO().obterConexaoEspecifica(key));

            Object objRetorno;
            switch (operacao) {
                case "salvar":
                    objRetorno = optinService.salvar(optinDTO.toVo());
                    break;
                case "atualizar":
                    objRetorno = optinService.atualizar(optinDTO.toVo());
                    break;
                default:
                    throw new Exception("Nenhuma operação executada");
            }
            if (objRetorno == null) {
                throw new Exception("Nenhuma operação executada");
            }

            response.getWriter().append(this.toJSON(true, objRetorno).toString());

        } catch (Exception e) {
            response.setStatus(400);
            response.getWriter().append(this.toJSON(false, e.getMessage()).toString());
        }   finally {
            if (con != null) {
                try {
                    con.close();
                } catch (Exception ex) {
                }
            }
        }
    }
    public OptinDTO extrairDTO(HttpServletRequest request) throws Exception {
        try {
            InputStream inputStream = request.getInputStream();
            BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream, "UTF-8"));

            StringBuffer body = new StringBuffer();
            String line;

            while ((line = reader.readLine()) != null) {
                body.append(line);
            }

            return new OptinDTO(new JSONObject(body.toString()));
        } catch (Exception ex) {
            ex.printStackTrace();
            throw new Exception("Erro body: " + ex.getMessage());
        }
    }
    public String extrairOperacao(HttpServletRequest request){
        String path = request.getServletPath();
        String[] pathParts = path.split("/");
        return pathParts[3];
    }

    public String extrairChave(HttpServletRequest request) throws Exception {
        if (UteisValidacao.emptyString(request.getParameter("chave"))) {
            throw new Exception("Chave não informada.");
        }
        return request.getParameter("chave");
    }

    private JSONObject toJSON(boolean sucesso, Object dados) {
        JSONObject json = new JSONObject();
        json.put("sucesso", sucesso);
        json.put("dados", dados);
        return json;
    }

}

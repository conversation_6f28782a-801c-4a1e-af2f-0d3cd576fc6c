package servlet.pactoPay;

import br.com.pactosolucoes.comuns.json.JSONMapper;
import br.com.pactosolucoes.integracao.pactopay.dto.ResultadoDTO;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.financeiro.PactoPayOperacaoServlet;
import org.json.JSONObject;
import servlet.arquitetura.SuperServlet;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.sql.Connection;

/**
 * Created with IntelliJ IDEA.
 * User: <PERSON><PERSON>
 * Date: 05/11/2020
 */
public class PactoPayServlet extends SuperServlet {

    @Override
    protected void processRequest(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        Connection con = null;
        PactoPayOperacaoServlet pactoPayDAO = null;
        try {
            response.addHeader("Access-Control-Allow-Origin", "*");
            response.addHeader("Access-Control-Allow-Methods", "GET,POST");
            response.setContentType("application/json");

            String method = request.getMethod();

            if (!method.equalsIgnoreCase("POST")) {
                throw new Exception("Método não suportado.");
            }

            String key = request.getParameter("key");
            if (UteisValidacao.emptyString(key)) {
                throw new Exception("Chave não informada.");
            }

            con = new DAO().obterConexaoEspecifica(key);

            ResultadoDTO resultadoDTO = null;
            try {
                InputStream inputStream = request.getInputStream();
                BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream, "UTF-8"));

                StringBuffer body = new StringBuffer();
                String line = null;

                while ((line = reader.readLine()) != null) {
                    body.append(line);
                }

                //incluir histórico
                pactoPayDAO = new PactoPayOperacaoServlet(con, body.toString());

                resultadoDTO = JSONMapper.getObject(new JSONObject(body.toString()), ResultadoDTO.class);
            } catch (Exception ex) {
                ex.printStackTrace();
                throw new Exception("Erro body: " + ex.getMessage());
            }

            Object objRetorno = pactoPayDAO.processarWebhook(resultadoDTO);
            if (objRetorno == null) {
                throw new Exception("Nenhuma operação executada");
            }

            response.getWriter().append(this.toJSON(true, objRetorno).toString());
        } catch (Exception ex) {
            response.setStatus(400);
            response.getWriter().append(this.toJSON(false, ex.getMessage()).toString());
        } finally {
            pactoPayDAO = null;
            if (con != null) {
                try {
                    con.close();
                } catch (Exception ex) {
                    ex.printStackTrace();
                }
            }
        }
    }

    private JSONObject toJSON(boolean sucesso, Object dados) {
        JSONObject json = new JSONObject();
        json.put("sucesso", sucesso);
        json.put("dados", dados);
        return json;
    }
}

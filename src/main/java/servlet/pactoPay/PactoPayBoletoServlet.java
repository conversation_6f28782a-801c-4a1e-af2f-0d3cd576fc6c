package servlet.pactoPay;

import br.com.pactosolucoes.integracao.pactopay.front.FiltroPactoPayDTO;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.arquitetura.EnvelopeRespostaDTO;
import negocio.comuns.arquitetura.PaginadorDTO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.EmailVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.financeiro.BoletoVO;
import negocio.comuns.financeiro.ConvenioCobrancaVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.facade.jdbc.basico.Email;
import negocio.facade.jdbc.basico.Pessoa;
import negocio.facade.jdbc.financeiro.Boleto;
import negocio.facade.jdbc.financeiro.ConvenioCobranca;
import org.json.JSONArray;
import org.json.JSONObject;
import servicos.integracao.pjbank.recebimento.BoletosManager;
import servlet.arquitetura.SuperServlet;

import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.sql.Connection;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;

public class PactoPayBoletoServlet extends SuperServlet {

    @Override
    protected void processRequest(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        try {
            response.addHeader("Access-Control-Allow-Origin", "*");
            response.addHeader("Access-Control-Allow-Methods", "GET,POST");
            response.addHeader("Access-Control-Allow-Headers", "Authorization,empresaId");
            response.setContentType("application/json");

            String operacao = request.getParameter("op");

            EnvelopeRespostaDTO envelopeRespostaDTO;
            if (operacao.equalsIgnoreCase("informacoes")) {
                envelopeRespostaDTO = informacoes(request);
            } else if (operacao.equalsIgnoreCase("cancelar")) {
                envelopeRespostaDTO = cancelar(request);
            } else if (operacao.equalsIgnoreCase("sincronizar")) {
                envelopeRespostaDTO = sincronizar(request);
            } else if (operacao.equalsIgnoreCase("enviar-email")) {
                envelopeRespostaDTO = enviarEmail(request);
            } else {
                throw new Exception("Nenhuma operação executada");
            }
            response.setStatus(HttpServletResponse.SC_OK);
            response.getWriter().append(new JSONObject(envelopeRespostaDTO).toString());
        } catch (Exception ex) {
            ex.printStackTrace();
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            response.getWriter().append(new JSONObject(EnvelopeRespostaDTO.erro("Erro", ex.getMessage())).toString());
        }
    }

    private String obterChave(ServletRequest request) throws Exception {
        String key = request.getParameter("key");
        if (UteisValidacao.emptyString(key)) {
            throw new Exception("Chave não informada.");
        }
        return key;
    }

    private Connection obterConexao(ServletRequest request) throws Exception {
        String key = obterChave(request);
        return new DAO().obterConexaoEspecifica(key.trim());
    }

    private void finalizarConexao(Connection con) {
        try {
            if (con != null) {
                con.close();
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    private String obterBody(ServletRequest request) throws Exception {
        StringBuffer body = new StringBuffer();
        try {
            InputStream inputStream = request.getInputStream();
            BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream, StandardCharsets.UTF_8));
            String line = null;

            while ((line = reader.readLine()) != null) {
                body.append(line);
            }
            return body.toString();
        } catch (Exception ex) {
            ex.printStackTrace();
            throw new Exception("Erro body: " + ex.getMessage());
        }
    }

    private EnvelopeRespostaDTO informacoes(ServletRequest request) throws Exception {
        Connection con = null;
        Boleto boletoDAO;
        try {
            con = obterConexao(request);
            boletoDAO = new Boleto(con);
            PaginadorDTO paginadorDTO = new PaginadorDTO(request);
            FiltroPactoPayDTO filtroDTO = new FiltroPactoPayDTO(request);

            Integer boleto = UteisValidacao.converterInteiro(request.getParameter("boleto"));

            String detalhesBoletoPJBank = boletoDAO.obterDetalheBoletoPJBank(boleto);
            return EnvelopeRespostaDTO.of(detalhesBoletoPJBank);
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        } finally {
            boletoDAO = null;
            finalizarConexao(con);
        }
    }

    private EnvelopeRespostaDTO cancelar(ServletRequest request) throws Exception {
        Connection con = null;
        Boleto boletoDAO;
        Usuario usuarioDAO;
        try {
            con = obterConexao(request);
            boletoDAO = new Boleto(con);
            usuarioDAO = new Usuario(con);

            String body = obterBody(request);
            JSONObject jsonBody = new JSONObject(body);
            String username = jsonBody.optString("username");
            JSONArray boletos = jsonBody.getJSONArray("boletos");

            if (UteisValidacao.emptyString(username)) {
                throw new Exception("Usuario não informado");
            }

            if (UteisValidacao.emptyNumber(boletos.length())) {
                throw new Exception("Nenhum boleto informado");
            }

            UsuarioVO usuarioVO = usuarioDAO.consultarPorUsername(username, Uteis.NIVELMONTARDADOS_METACOLABORADORRESPONSAVEL);
            if (usuarioVO == null || UteisValidacao.emptyNumber(usuarioVO.getCodigo())) {
                throw new Exception("Usuário não encontrado");
            }

            Integer totalSucesso = 0;
            String erro = "";
            for (int e = 0; e < boletos.length(); e++) {
                try {
                    Integer codigo = boletos.getInt(e);
                    BoletoVO obj = boletoDAO.consultarPorChavePrimaria(codigo, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                    if (!obj.isPodeCancelar()) {
                        throw new Exception("Boleto não permite cancelar");
                    }
                    boletoDAO.cancelarBoleto(obj, usuarioVO, "PactoPay - Cancelar");
                    totalSucesso++;
                } catch (Exception ex) {
                    ex.printStackTrace();
                    erro = ex.getMessage();
                    if (boletos.length() == 1) {
                        throw ex;
                    }
                }
            }

            if (UteisValidacao.emptyNumber(totalSucesso)) {
                throw new Exception("Nenhuma boleto cancelado. " + erro);
            } else {
                return EnvelopeRespostaDTO.of(totalSucesso > 1 ? "Foram cancelados " + totalSucesso + " boletos" : "Boleto cancelado");
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        } finally {
            boletoDAO = null;
            usuarioDAO = null;
            finalizarConexao(con);
        }
    }

    private EnvelopeRespostaDTO sincronizar(ServletRequest request) throws Exception {
        Connection con = null;
        Boleto boletoDAO;
        Usuario usuarioDAO;
        try {
            con = obterConexao(request);
            boletoDAO = new Boleto(con);
            usuarioDAO = new Usuario(con);

            String body = obterBody(request);
            JSONObject jsonBody = new JSONObject(body);
            String username = jsonBody.optString("username");
            JSONArray boletos = jsonBody.getJSONArray("boletos");

            if (UteisValidacao.emptyString(username)) {
                throw new Exception("Usuario não informado");
            }

            if (UteisValidacao.emptyNumber(boletos.length())) {
                throw new Exception("Nenhum boleto informado");
            }

            UsuarioVO usuarioVO = usuarioDAO.consultarPorUsername(username, Uteis.NIVELMONTARDADOS_METACOLABORADORRESPONSAVEL);
            if (usuarioVO == null || UteisValidacao.emptyNumber(usuarioVO.getCodigo())) {
                throw new Exception("Usuário não encontrado");
            }

            Integer totalSucesso = 0;
            String erro = "";
            for (int e = 0; e < boletos.length(); e++) {
                try {
                    Integer codigo = boletos.getInt(e);
                    BoletoVO obj = boletoDAO.consultarPorChavePrimaria(codigo, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                    if (!obj.isPodeSincronizar()) {
                        throw new Exception("Boleto não permite sincronizar");
                    }
                    String msg = boletoDAO.sincronizarBoleto(obj, usuarioVO, "PactoPay - Sincronizar");
                    totalSucesso++;

                    if (boletos.length() == 1) {
                        return EnvelopeRespostaDTO.of(msg);
                    }
                } catch (Exception ex) {
                    ex.printStackTrace();
                    erro = ex.getMessage();
                    if (boletos.length() == 1) {
                        throw ex;
                    }
                }
            }

            if (UteisValidacao.emptyNumber(totalSucesso)) {
                throw new Exception("Nenhuma boleto sincronizado. " + erro);
            } else {
                return EnvelopeRespostaDTO.of(totalSucesso > 1 ? "Foram sincronizados " + totalSucesso + " boletos" : "Boleto sincronizado");
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        } finally {
            boletoDAO = null;
            usuarioDAO = null;
            finalizarConexao(con);
        }
    }

    private EnvelopeRespostaDTO enviarEmail(ServletRequest request) throws Exception {
        Connection con = null;
        Boleto boletoDAO;
        Usuario usuarioDAO;
        Email emailDAO;
        Pessoa pessoaDAO;
        ConvenioCobranca convenioCobrancaDAO;
        try {
            con = obterConexao(request);
            boletoDAO = new Boleto(con);
            usuarioDAO = new Usuario(con);
            emailDAO = new Email(con);
            pessoaDAO = new Pessoa(con);
            convenioCobrancaDAO = new ConvenioCobranca(con);

            String key = obterChave(request);
            String body = obterBody(request);
            JSONObject jsonBody = new JSONObject(body);
            String username = jsonBody.optString("username");
            JSONArray boletos = jsonBody.getJSONArray("boletos");

            if (UteisValidacao.emptyString(username)) {
                throw new Exception("Usuario não informado");
            }

            if (UteisValidacao.emptyNumber(boletos.length())) {
                throw new Exception("Nenhum boleto informado");
            }

            UsuarioVO usuarioVO = usuarioDAO.consultarPorUsername(username, Uteis.NIVELMONTARDADOS_METACOLABORADORRESPONSAVEL);
            if (usuarioVO == null || UteisValidacao.emptyNumber(usuarioVO.getCodigo())) {
                throw new Exception("Usuário não encontrado");
            }

            Map<Integer, List<BoletoVO>> mapBoletos = new HashMap<>();
            for (int e = 0; e < boletos.length(); e++) {
                try {
                    Integer codigo = boletos.getInt(e);
                    BoletoVO obj = boletoDAO.consultarPorChavePrimaria(codigo, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                    if (!obj.isPodeEnviarEmail()) {
                        throw new Exception("Boleto não permite enviar e-mail");
                    }

                    List<BoletoVO> lista = mapBoletos.get(obj.getPessoaVO().getCodigo());
                    if (lista == null) {
                        lista = new ArrayList<>();
                    }

                    lista.add(obj);
                    mapBoletos.put(obj.getPessoaVO().getCodigo(), lista);
                } catch (Exception ex) {
                    ex.printStackTrace();
                    if (boletos.length() == 1) {
                        throw ex;
                    }
                }
            }

            if (mapBoletos.isEmpty()) {
                throw new Exception("Nenhum boleto disponível para enviar e-mail");
            }

            Integer totalSucesso = 0;
            String erro = "";
            for (Integer pessoa : mapBoletos.keySet()) {
                try {
                    List<BoletoVO> listaBoletosEnviar = mapBoletos.get(pessoa);

                    List<EmailVO> emailsEnviar = emailDAO.consultarEmails(pessoa, false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                    if (UteisValidacao.emptyList(emailsEnviar)) {
                        PessoaVO pessoaVO = pessoaDAO.consultarPorChavePrimaria(pessoa, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                        throw new Exception(pessoaVO.getNome() + " não tem e-mail cadastrado.");
                    }

                    String[] emails = new String[emailsEnviar.size()];
                    int i = 0;
                    for (Object objEmail : emailsEnviar) {
                        EmailVO emailVO = (EmailVO) objEmail;
                        emails[i] = emailVO.getEmail();
                        i++;
                    }

                    if (listaBoletosEnviar.size() == 1) {
                        boletoDAO.enviarEmailBoleto(key,
                                listaBoletosEnviar.get(0), emails, true, false);
                    } else {
                        ConvenioCobrancaVO convenioCobrancaVO = convenioCobrancaDAO.consultarPorChavePrimaria(listaBoletosEnviar.get(0).getConvenioCobrancaVO().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                        LinkedHashSet<String> pedidos = BoletoVO.obterListaImpressaoBoleto(listaBoletosEnviar);
                        BoletosManager boletosManager = new BoletosManager(convenioCobrancaVO.getCredencialPJBank(), convenioCobrancaVO.getChavePJBank(), convenioCobrancaVO);
                        String linkBoleto = boletosManager.getByIds(pedidos);
                        boletoDAO.enviarEmailBoletoLink(key, linkBoleto, listaBoletosEnviar.get(0).getEmpresaVO().getCodigo(),
                                listaBoletosEnviar.get(0).getPessoaVO().getCodigo(), emails);
                    }
                    totalSucesso++;

                    String msg = ("E-mail enviado com sucesso, para " + Arrays.toString(emails).replace("[", "").replace("]", ""));
                    if (mapBoletos.size() == 1) {
                        return EnvelopeRespostaDTO.of(msg);
                    }
                } catch (Exception ex) {
                    ex.printStackTrace();
                    erro = ex.getMessage();
                    if (mapBoletos.size() == 1) {
                        throw ex;
                    }
                }
            }

            if (UteisValidacao.emptyNumber(totalSucesso)) {
                throw new Exception("Nenhum e-mail enviado. " + erro);
            } else {
                return EnvelopeRespostaDTO.of("E-mails enviados");
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        } finally {
            boletoDAO = null;
            usuarioDAO = null;
            emailDAO = null;
            pessoaDAO = null;
            convenioCobrancaDAO = null;
            finalizarConexao(con);
        }
    }
}

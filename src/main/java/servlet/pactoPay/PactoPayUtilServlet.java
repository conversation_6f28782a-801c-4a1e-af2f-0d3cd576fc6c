package servlet.pactoPay;

import br.com.pactosolucoes.autorizacaocobranca.modelo.TipoAutorizacaoCobrancaEnum;
import br.com.pactosolucoes.integracao.pactopay.StatusPactoPayEnum;
import br.com.pactosolucoes.integracao.pactopay.front.ConvenioCobrancaDTO;
import br.com.pactosolucoes.integracao.pactopay.front.FacilitePayConfigDTO;
import br.com.pactosolucoes.integracao.pactopay.front.GenericoIntegerDTO;
import br.com.pactosolucoes.integracao.pactopay.front.InfoWeHelpDTO;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.arquitetura.EnvelopeRespostaDTO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.crm.MeioEnvio;
import negocio.comuns.financeiro.enumerador.OrigemCobrancaEnum;
import negocio.comuns.financeiro.enumerador.SituacaoBoletoEnum;
import negocio.comuns.financeiro.enumerador.SituacaoConvenioCobranca;
import negocio.comuns.financeiro.enumerador.SituacaoTransacaoEnum;
import negocio.comuns.financeiro.enumerador.TipoCobrancaEnum;
import negocio.comuns.financeiro.enumerador.TipoConvenioCobrancaEnum;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.facade.jdbc.basico.Empresa;
import negocio.facade.jdbc.basico.FacilitePay;
import negocio.facade.jdbc.basico.PactoPayComunicacao;
import negocio.facade.jdbc.basico.Pessoa;
import negocio.oamd.CustomerSuccessTO;
import negocio.oamd.RedeEmpresaVO;
import org.json.JSONObject;
import servlet.arquitetura.SuperServlet;

import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 * User: Luiz Felipe
 * Date: 01/06/2021
 */
public class PactoPayUtilServlet extends SuperServlet {

    @Override
    protected void processRequest(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        try {
            response.addHeader("Access-Control-Allow-Origin", "*");
            response.addHeader("Access-Control-Allow-Methods", "GET,POST");
            response.addHeader("Access-Control-Allow-Headers", "Authorization,empresaId");
            response.setContentType("application/json");

            String operacao = request.getParameter("op");

            EnvelopeRespostaDTO envelopeRespostaDTO;
            if (operacao.equalsIgnoreCase("usuario")) {
                envelopeRespostaDTO = validarUsuario(request);
            } else if (operacao.equalsIgnoreCase("wehelp")) {
                envelopeRespostaDTO = weHelp(request);
            } else if (operacao.equalsIgnoreCase("convenio")) {
                envelopeRespostaDTO = consultarConveniosCobranca(request);
            } else if (operacao.equalsIgnoreCase("situacao")) {
                envelopeRespostaDTO = obterSituacoes(request);
            } else if (operacao.equalsIgnoreCase("origem")) {
                envelopeRespostaDTO = obterOrigemPactoPay();
            } else if (operacao.equalsIgnoreCase("configFacilitePay")) {
                envelopeRespostaDTO = configFacilitePay(request);
            } else if (operacao.equalsIgnoreCase("operacao_regua_cobranca")) {
                envelopeRespostaDTO = operacaoReguaCobranca(request);
            } else {
                throw new Exception("Nenhuma operação executada");
            }
            response.setStatus(HttpServletResponse.SC_OK);
            response.getWriter().append(new JSONObject(envelopeRespostaDTO).toString());
        } catch (Exception ex) {
            ex.printStackTrace();
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            response.getWriter().append(new JSONObject(EnvelopeRespostaDTO.erro("Erro", ex.getMessage())).toString());
        }
    }

    private Connection obterConexao(ServletRequest request) throws Exception {
        String key = request.getParameter("key");
        if (UteisValidacao.emptyString(key)) {
            throw new Exception("Chave não informada.");
        }
        return new DAO().obterConexaoEspecifica(key.trim());
    }

    private void finalizarConexao(Connection con) {
        try {
            if (con != null) {
                con.close();
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    private String obterBody(ServletRequest request) throws Exception {
        StringBuffer body = new StringBuffer();
        try {
            InputStream inputStream = request.getInputStream();
            BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream, StandardCharsets.UTF_8));
            String line = null;

            while ((line = reader.readLine()) != null) {
                body.append(line);
            }
            return body.toString();
        } catch (Exception ex) {
            ex.printStackTrace();
            throw new Exception("Erro body: " + ex.getMessage());
        }
    }

    private EnvelopeRespostaDTO validarUsuario(ServletRequest request) throws Exception {
        Connection con = null;
        try {
            boolean tempermissao = false;
            try {
                Integer usuario = UteisValidacao.converterInteiro(request.getParameter("u"));
                Integer empresa = obterEmpresa(request, false);
                String username = request.getParameter("username");

                if (!UteisValidacao.emptyNumber(empresa) && (!UteisValidacao.emptyNumber(usuario) || !UteisValidacao.emptyString(username))) {
                    con = obterConexao(request);

                    StringBuilder sql = new StringBuilder();
                    sql.append("select exists( \n");
                    sql.append("select \n");
                    sql.append("up.codigo \n");
                    sql.append("from usuarioperfilacesso up \n");
                    sql.append("inner join perfilacesso pa on pa.codigo = up.perfilacesso \n");
                    sql.append("inner join permissao p on p.codperfilacesso = pa.codigo \n");
                    sql.append("inner join usuario us on us.codigo = up.usuario \n");
                    sql.append("where p.nomeentidade ilike 'ModuloPactoPay' \n");
                    sql.append("and up.empresa = ").append(empresa).append(" \n");
                    if (!UteisValidacao.emptyString(username)) {
                        sql.append("and us.username ilike '").append(username).append("' \n");
                    } else {
                        sql.append("and up.usuario = ").append(usuario).append(" \n");
                    }
                    sql.append(") as tempermissao \n");

                    try (PreparedStatement stm = con.prepareStatement(sql.toString())) {
                        try (ResultSet rs = stm.executeQuery()) {
                            if (rs.next()) {
                                tempermissao = rs.getBoolean("tempermissao");
                            }
                        }
                    }
                }
            } catch (Exception ex) {
                ex.printStackTrace();
            }
            return EnvelopeRespostaDTO.of(tempermissao);
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        } finally {
            finalizarConexao(con);
        }
    }

    private EnvelopeRespostaDTO weHelp(ServletRequest request) throws Exception {
        Connection con = null;
        Empresa empresaDAO;
        Usuario usuarioDAO;
        Pessoa pessoaDAO;
        try {
            con = obterConexao(request);
            empresaDAO = new Empresa(con);
            usuarioDAO = new Usuario(con);
            pessoaDAO = new Pessoa(con);

            String key = request.getParameter("key");
            Integer empresa = obterEmpresa(request, false);
            String username = request.getParameter("username");

            EmpresaVO empresaVO = empresaDAO.consultarPorChavePrimaria(empresa, Uteis.NIVELMONTARDADOS_TODOS);
            UsuarioVO usuarioVO = usuarioDAO.consultarPorUsername(username, Uteis.NIVELMONTARDADOS_TODOS);
            if (UteisValidacao.emptyNumber(usuarioVO.getCodigo())) {
                throw new Exception("Usuário não encontrado");
            }

            if (!UteisValidacao.emptyNumber(usuarioVO.getColaboradorVO().getPessoa().getCodigo())) {
                usuarioVO.getColaboradorVO().setPessoa(pessoaDAO.consultarPorChavePrimaria(usuarioVO.getColaboradorVO().getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_PESSOA_PACTO_PAY));
            }
            RedeEmpresaVO redeEmpresaVO = empresaDAO.consultarRedeEmpresaOAMD(key);
            CustomerSuccessTO customerSuccessTO = empresaDAO.consultarResponsavelPacto(key, empresa);

            return EnvelopeRespostaDTO.of(new InfoWeHelpDTO(key, usuarioVO, empresaVO, redeEmpresaVO, customerSuccessTO));
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        } finally {
            finalizarConexao(con);
            empresaDAO = null;
            usuarioDAO = null;
            pessoaDAO = null;
        }
    }

    private Integer obterEmpresa(ServletRequest request, boolean validar) throws Exception {
        Integer empresa = UteisValidacao.converterInteiro(request.getParameter("empresa"));
        if (UteisValidacao.emptyNumber(empresa)) {
            empresa = UteisValidacao.converterInteiro(request.getParameter("e"));
        }
        if (validar && UteisValidacao.emptyNumber(empresa)) {
            throw new Exception("Empresa não informado");
        }
        return empresa;
    }

    private EnvelopeRespostaDTO consultarConveniosCobranca(ServletRequest request) throws Exception {
        Connection con = null;
        try {
            con = obterConexao(request);

            TipoConvenioCobrancaEnum[] arrayTiposConvenio = null;

            Integer tipoCobranca = UteisValidacao.converterInteiro(request.getParameter("tipo_cobranca"));
            if (!UteisValidacao.emptyNumber(tipoCobranca)) {
                TipoCobrancaEnum tipoCobrancaEnum = TipoCobrancaEnum.obterPorId(tipoCobranca);
                arrayTiposConvenio = TipoConvenioCobrancaEnum.obterListaTipoCobranca(tipoCobrancaEnum).toArray(new TipoConvenioCobrancaEnum[0]);
            }

            Integer tipoAutorizacao = UteisValidacao.converterInteiro(request.getParameter("tipo_autorizacao"));
            if (!UteisValidacao.emptyNumber(tipoAutorizacao)) {
                TipoAutorizacaoCobrancaEnum tipoAutorizacaoCobrancaEnum = TipoAutorizacaoCobrancaEnum.valueOf(tipoAutorizacao);
                arrayTiposConvenio = tipoAutorizacaoCobrancaEnum.getTiposConvenio();
            }

            Integer empresa = obterEmpresa(request, false);

            SituacaoConvenioCobranca situacaoEnum = SituacaoConvenioCobranca.ATIVO;
            String situacao = request.getParameter("situacao");
            if (!UteisValidacao.emptyString(situacao)) {
                if (situacao.equalsIgnoreCase("ativo")) {
                    situacaoEnum = SituacaoConvenioCobranca.ATIVO;
                } else if (situacao.equalsIgnoreCase("inativo")) {
                    situacaoEnum = SituacaoConvenioCobranca.INATIVO;
                } else if (situacao.equalsIgnoreCase("todos")) {
                    situacaoEnum = null;
                }
            }

            StringBuilder sql = new StringBuilder();
            sql.append("select \n");
            sql.append("distinct \n");
            sql.append("c.codigo, \n");
            sql.append("c.descricao, \n");
            sql.append("case  \n");
            sql.append(" when c.situacao = 0 then 'INATIVO' \n");
            sql.append(" when c.situacao = 1 then 'ATIVO' \n");
            sql.append(" else '' end as situacao, \n");
            sql.append("array_to_string(array(SELECT empresa FROM conveniocobrancaempresa WHERE conveniocobranca = c.codigo order by empresa), ',', '') as empresas \n");
            sql.append("from conveniocobranca c \n");
            sql.append("inner join conveniocobrancaempresa ce on ce.conveniocobranca = c.codigo \n");
            sql.append("where 1 = 1 \n");

            if (situacaoEnum != null) {
                sql.append("and (c.situacao = ").append(situacaoEnum.getCodigo()).append(" or c.apresentarInativoNoPactoPay) \n");
            }
            if (!UteisValidacao.emptyNumber(empresa)) {
                sql.append("and ce.empresa = ").append(empresa).append(" \n");
            }

            if (arrayTiposConvenio != null && arrayTiposConvenio.length > 0) {
                String condicaoTipos = Uteis.splitFromArray(arrayTiposConvenio, false, "codigo");
                if (!UteisValidacao.emptyString(condicaoTipos)) {
                    sql.append("and c.tipoconvenio in (").append(condicaoTipos).append(") \n");
                }
            }
            sql.append("order by c.descricao \n");

            List<ConvenioCobrancaDTO> lista = new ArrayList<>();
            try (Statement stm = con.createStatement()) {
                try (ResultSet rs = stm.executeQuery(sql.toString())) {
                    while (rs.next()) {
                        ConvenioCobrancaDTO obj = new ConvenioCobrancaDTO();
                        obj.setCodigo(rs.getInt("codigo"));
                        obj.setDescricao(rs.getString("descricao"));
                        obj.setSituacao(rs.getString("situacao"));

                        obj.setEmpresas(new ArrayList<>());
                        try {
                            String empresas = rs.getString("empresas");
                            for (String emp : empresas.split(",")) {
                                try {
                                    obj.getEmpresas().add(Integer.parseInt(emp));
                                } catch (Exception ignored) {
                                }
                            }
                        } catch (Exception ignored) {
                        }
                        lista.add(obj);
                    }
                }
            }

            return EnvelopeRespostaDTO.of(lista);
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        } finally {
            finalizarConexao(con);
        }
    }

    private EnvelopeRespostaDTO obterSituacoes(ServletRequest request) throws Exception {
        try {
            String tipo = request.getParameter("tipo");
            if (UteisValidacao.emptyString(tipo)) {
                throw new Exception("Tipo não informado");
            }

            if (tipo.equalsIgnoreCase("pix")) {
                return EnvelopeRespostaDTO.of(obterSituacoesPix());
            } else if (tipo.equalsIgnoreCase("transacao")) {
                return EnvelopeRespostaDTO.of(obterSituacoesTransacao());
            } else if (tipo.equalsIgnoreCase("boleto")) {
                return EnvelopeRespostaDTO.of(obterSituacoesBoleto());
            } else {
                throw new Exception("Tipo inválido");
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        }
    }

    private List<GenericoIntegerDTO> obterSituacoesPix() {
        List<GenericoIntegerDTO> lista = new ArrayList<>();
        lista.add(new GenericoIntegerDTO(StatusPactoPayEnum.AGUARDANDO.getCodigo(), StatusPactoPayEnum.AGUARDANDO.getDescricaoPix()));
        lista.add(new GenericoIntegerDTO(StatusPactoPayEnum.CONCLUIDA_COM_SUCESSO.getCodigo(), StatusPactoPayEnum.CONCLUIDA_COM_SUCESSO.getDescricaoPix()));
        lista.add(new GenericoIntegerDTO(StatusPactoPayEnum.CANCELADA.getCodigo(), StatusPactoPayEnum.CANCELADA.getDescricaoPix()));
        lista.add(new GenericoIntegerDTO(StatusPactoPayEnum.EXPIRADO.getCodigo(), StatusPactoPayEnum.EXPIRADO.getDescricaoPix()));
        Ordenacao.ordenarLista(lista, "descricao");
        return lista;
    }

    private List<GenericoIntegerDTO> obterSituacoesTransacao() {
        List<GenericoIntegerDTO> lista = new ArrayList<>();
        for (SituacaoTransacaoEnum obj : SituacaoTransacaoEnum.values()) {
            if (obj.equals(SituacaoTransacaoEnum.COM_ERRO) ||
                    obj.equals(SituacaoTransacaoEnum.APROVADA) ||
                    obj.equals(SituacaoTransacaoEnum.NAO_APROVADA) ||
                    obj.equals(SituacaoTransacaoEnum.CONCLUIDA_COM_SUCESSO) ||
                    obj.equals(SituacaoTransacaoEnum.CANCELADA) ||
                    obj.equals(SituacaoTransacaoEnum.ESTORNADA)) {
                GenericoIntegerDTO dto = new GenericoIntegerDTO();
                dto.setCodigo(obj.getId());
                dto.setDescricao(obj.getStatusPactoPayEnum().getDescricaoFront());
                lista.add(dto);
            }
        }
        Ordenacao.ordenarLista(lista, "descricao");
        return lista;
    }

    private List<GenericoIntegerDTO> obterSituacoesBoleto() {
        List<GenericoIntegerDTO> lista = new ArrayList<>();
        for (SituacaoBoletoEnum obj : SituacaoBoletoEnum.values()) {
            if (!obj.equals(SituacaoBoletoEnum.NENHUMA)) {
                lista.add(new GenericoIntegerDTO(obj));
            }
        }
        Ordenacao.ordenarLista(lista, "descricao");
        return lista;
    }

    private EnvelopeRespostaDTO obterOrigemPactoPay() {
        List<GenericoIntegerDTO> lista = new ArrayList<>();
        for (OrigemCobrancaEnum obj : OrigemCobrancaEnum.values()) {
            if (obj.equals(OrigemCobrancaEnum.NENHUM) ||
                    obj.equals(OrigemCobrancaEnum.PACTO_STORE)) {
                continue;
            }
            GenericoIntegerDTO dto = new GenericoIntegerDTO();
            dto.setCodigo(obj.getCodigo());
            dto.setDescricao(obj.getDescricao().replace("Link Pagamento - ", ""));
            lista.add(dto);
        }
        Ordenacao.ordenarLista(lista, "descricao");
        return EnvelopeRespostaDTO.of(lista);
    }

    private EnvelopeRespostaDTO configFacilitePay(ServletRequest request) throws Exception {
        Connection con = null;
        FacilitePay facilitePayDAO;
        try {
            con = obterConexao(request);
            facilitePayDAO = new FacilitePay(con);

            Integer cod_empresa = obterEmpresa(request, false);
            if (UteisValidacao.emptyNumber(cod_empresa)) {
                throw new Exception("Empresa não informada");
            }

            Integer cod_usuario = UteisValidacao.converterInteiro(request.getParameter("usuario"));
            String username = request.getParameter("username");
            if (UteisValidacao.emptyNumber(cod_usuario) && UteisValidacao.emptyString(username)) {
                throw new Exception("Usuário não informada");
            }

            FacilitePayConfigDTO facilitePayConfigDTO = facilitePayDAO.configFacilitePay(cod_empresa, cod_usuario, username);
            return EnvelopeRespostaDTO.of(facilitePayConfigDTO);
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        } finally {
            facilitePayDAO = null;
            finalizarConexao(con);
        }
    }

    private UsuarioVO obterUsuarioVO(String username, Integer codigoUsuario, Connection con, int nivelMontarDados) throws Exception {
        Usuario usuarioDAO;
        try {
            usuarioDAO = new Usuario(con);
            UsuarioVO usuarioVO = null;
            if (!UteisValidacao.emptyString(username)) {
                usuarioVO = usuarioDAO.consultarPorUsername(username, nivelMontarDados);
            }
            if (!UteisValidacao.emptyNumber(codigoUsuario) &&
                    (usuarioVO == null || UteisValidacao.emptyNumber(usuarioVO.getCodigo()))) {
                usuarioVO = usuarioDAO.consultarPorChavePrimaria(codigoUsuario, nivelMontarDados);
            }
            return usuarioVO;
        } finally {
            usuarioDAO = null;
        }
    }

    private EnvelopeRespostaDTO operacaoReguaCobranca(ServletRequest request) throws Exception {
        Connection con = null;
        PactoPayComunicacao pactoPayComunicacaoDAO;
        try {
            con = obterConexao(request);
            pactoPayComunicacaoDAO = new PactoPayComunicacao(con);

            String body = obterBody(request);
            JSONObject jsonBody = new JSONObject(body);
            Integer codPactoPayComunicacao = jsonBody.optInt("pactoPayComunicacao");
            String operacao = jsonBody.optString("operacao");

            boolean clicou = operacao.equalsIgnoreCase("clicou");
            boolean lido = operacao.equalsIgnoreCase("lido");

            if (clicou || lido) {

                if (UteisValidacao.emptyNumber(codPactoPayComunicacao)) {
                    throw new Exception("Código da comunicação não informado");
                }

                MeioEnvio meioEnvioEnum = null;
                ResultSet rs = SuperFacadeJDBC.criarConsulta("select codigo,meioenvio from pactopaycomunicacao WHERE codigo = " + codPactoPayComunicacao, con);
                if (rs.next()) {
                    meioEnvioEnum = MeioEnvio.getMeioEnvioPorCodigo(rs.getInt("meioenvio"));
                } else {
                    throw new Exception("PactoPayComunicacao não encontrato");
                }

                pactoPayComunicacaoDAO.incluirPactoPayComunicacaoLog(codPactoPayComunicacao, "BODY_REQUEST_1", body);

                String dataString = jsonBody.optString("data");
                Date data = Calendario.hoje();
                boolean sobrescrever = false;
                if (!UteisValidacao.emptyString(dataString)) {
                    if (dataString.equalsIgnoreCase("null")) {
                        data = null;
                        sobrescrever = true; //somente sobrescrever se informar uma data se não deixar registrado somente a primeira vez que o cara clicou ou leu
                    } else {
                        data = Calendario.getDate("yyyyMMddHHmmss", dataString);
                        sobrescrever = true; //somente sobrescrever se informar uma data se não deixar registrado somente a primeira vez que o cara clicou ou leu
                    }
                }

                if (clicou) {
                    pactoPayComunicacaoDAO.alterarClicou(data, codPactoPayComunicacao, sobrescrever, true);
                    //se ele clicou é pq ele foi lido!
                    pactoPayComunicacaoDAO.alterarLido(data, codPactoPayComunicacao, sobrescrever, true);
                }
                if (lido) {
                    pactoPayComunicacaoDAO.alterarLido(data, codPactoPayComunicacao, sobrescrever, true);
                }

            } else if (operacao.equalsIgnoreCase("gravar_body")) {
                pactoPayComunicacaoDAO.incluirPactoPayComunicacaoLog(codPactoPayComunicacao, "BODY_REQUEST", body);
            } else {
                throw new Exception("Nenhuma operação executada | " + operacao);
            }
            return EnvelopeRespostaDTO.of("ok");
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        } finally {
            pactoPayComunicacaoDAO = null;
            finalizarConexao(con);
        }
    }
}

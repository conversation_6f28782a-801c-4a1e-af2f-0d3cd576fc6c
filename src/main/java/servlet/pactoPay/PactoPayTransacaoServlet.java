package servlet.pactoPay;

import br.com.pactosolucoes.integracao.pactopay.front.FiltroPactoPayDTO;
import br.com.pactosolucoes.integracao.pactopay.front.cartao.CobrancaDetalheDTO;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.arquitetura.EnvelopeRespostaDTO;
import negocio.comuns.arquitetura.PaginadorDTO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.EmailVO;
import negocio.comuns.financeiro.ConvenioCobrancaVO;
import negocio.comuns.financeiro.MovParcelaVO;
import negocio.comuns.financeiro.TransacaoVO;
import negocio.comuns.financeiro.enumerador.OrigemCobrancaEnum;
import negocio.comuns.financeiro.enumerador.SituacaoConvenioCobranca;
import negocio.comuns.financeiro.enumerador.SituacaoTransacaoEnum;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.facade.jdbc.basico.Email;
import negocio.facade.jdbc.financeiro.ConvenioCobranca;
import negocio.facade.jdbc.financeiro.MovParcela;
import negocio.facade.jdbc.financeiro.Transacao;
import negocio.facade.jdbc.financeiro.TransacaoMovParcela;
import org.json.JSONArray;
import org.json.JSONObject;
import servicos.impl.gatewaypagamento.PagamentoService;
import servlet.arquitetura.SuperServlet;

import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.sql.Connection;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.StringTokenizer;

/**
 * Created with IntelliJ IDEA.
 * User: Luiz Felipe
 * Date: 08/04/2022
 */
public class PactoPayTransacaoServlet extends SuperServlet {

    @Override
    protected void processRequest(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        try {
            response.addHeader("Access-Control-Allow-Origin", "*");
            response.addHeader("Access-Control-Allow-Methods", "GET,POST");
            response.addHeader("Access-Control-Allow-Headers", "Authorization,empresaId");
            response.setContentType("application/json");

            String operacao = request.getParameter("op");

            EnvelopeRespostaDTO envelopeRespostaDTO;
            if (operacao.equalsIgnoreCase("consultar")) {
                envelopeRespostaDTO = consultar(request);
            } else if (operacao.equalsIgnoreCase("totalizador")) {
                envelopeRespostaDTO = totalizador(request);
            } else if (operacao.equalsIgnoreCase("totalizador_tipo")) {
                envelopeRespostaDTO = totalizadorPorTipo(request);
            } else if (operacao.equalsIgnoreCase("totalizador_parcela")) {
                envelopeRespostaDTO = totalizadorPorParcela(request);
            } else if (operacao.equalsIgnoreCase("totalizador_parcela_lista")) {
                envelopeRespostaDTO = totalizadorPorParcelaLista(request);
            } else if (operacao.equalsIgnoreCase("totalizador_bandeira")) {
                envelopeRespostaDTO = totalizadorPorBandeira(request);
            } else if (operacao.equalsIgnoreCase("retentar_transacao")) {
                envelopeRespostaDTO = retentarTransacoes(request);
            } else if (operacao.equalsIgnoreCase("sincronizar_transacao")) {
                envelopeRespostaDTO = sincronizarTransacoes(request);
            } else if (operacao.equalsIgnoreCase("cancelar_transacao")) {
                envelopeRespostaDTO = cancelarTransacoes(request);
            } else if (operacao.equalsIgnoreCase("enviar_comprovante_transacao")) {
                envelopeRespostaDTO = enviarComprovantePagamentoTransacoes(request);
            } else if (operacao.equalsIgnoreCase("enviar_cancelamento_transacao")) {
                envelopeRespostaDTO = enviarComprovanteCancelamentoTransacoes(request);
            } else {
                throw new Exception("Nenhuma operação executada");
            }
            response.setStatus(HttpServletResponse.SC_OK);
            response.getWriter().append(new JSONObject(envelopeRespostaDTO).toString());
        } catch (Exception ex) {
            ex.printStackTrace();
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            response.getWriter().append(new JSONObject(EnvelopeRespostaDTO.erro("Erro", ex.getMessage())).toString());
        }
    }

    private Connection obterConexao(ServletRequest request) throws Exception {
        String key = request.getParameter("key");
        if (UteisValidacao.emptyString(key)) {
            throw new Exception("Chave não informada.");
        }
        return new DAO().obterConexaoEspecifica(key.trim());
    }

    private void finalizarConexao(Connection con) {
        try {
            if (con != null) {
                con.close();
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    private String obterBody(ServletRequest request) throws Exception {
        StringBuffer body = new StringBuffer();
        try {
            InputStream inputStream = request.getInputStream();
            BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream, StandardCharsets.UTF_8));
            String line = null;

            while ((line = reader.readLine()) != null) {
                body.append(line);
            }
            return body.toString();
        } catch (Exception ex) {
            ex.printStackTrace();
            throw new Exception("Erro body: " + ex.getMessage());
        }
    }

    private EnvelopeRespostaDTO consultar(ServletRequest request) throws Exception {
        Connection con = null;
        Transacao transacaoDAO;
        ConvenioCobranca convenioCobrancaDAO;
        try {
            con = obterConexao(request);
            transacaoDAO = new Transacao(con);
            convenioCobrancaDAO = new ConvenioCobranca(con);
            PaginadorDTO paginadorDTO = new PaginadorDTO(request);
            FiltroPactoPayDTO filtroDTO = new FiltroPactoPayDTO(request);

            Map<Integer, ConvenioCobrancaVO> mapaConvenio = new HashMap<>();

            List<CobrancaDetalheDTO> lista = new ArrayList<>();

            validarFiltro(filtroDTO);
            List<TransacaoVO> transacoes = transacaoDAO.consultarPactoPay(filtroDTO, paginadorDTO);
            for (TransacaoVO transacaoVO : transacoes) {
                if (!UteisValidacao.emptyNumber(transacaoVO.getConvenioCobrancaVO().getCodigo())) {
                    ConvenioCobrancaVO convenioCobrancaVO = mapaConvenio.get(transacaoVO.getConvenioCobrancaVO().getCodigo());
                    if (convenioCobrancaVO == null) {
                        convenioCobrancaVO = convenioCobrancaDAO.consultarPorChavePrimaria(transacaoVO.getConvenioCobrancaVO().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                        mapaConvenio.put(convenioCobrancaVO.getCodigo(), convenioCobrancaVO);
                    }
                    transacaoVO.setConvenioCobrancaVO(convenioCobrancaVO);;
                }

                lista.add(new CobrancaDetalheDTO(transacaoVO));
            }
            return EnvelopeRespostaDTO.of(lista, paginadorDTO);
        } finally {
            transacaoDAO = null;
            convenioCobrancaDAO = null;
            finalizarConexao(con);
        }
    }

    private EnvelopeRespostaDTO totalizador(ServletRequest request) throws Exception {
        Connection con = null;
        Transacao transacaoDAO;
        try {
            con = obterConexao(request);
            transacaoDAO = new Transacao(con);
            FiltroPactoPayDTO filtroDTO = new FiltroPactoPayDTO(request);
            PaginadorDTO paginadorDTO = new PaginadorDTO(request);
            validarFiltro(filtroDTO);
            return EnvelopeRespostaDTO.of(transacaoDAO.consultarPactoPayTotalizador(filtroDTO, paginadorDTO), paginadorDTO);
        } finally {
            transacaoDAO = null;
            finalizarConexao(con);
        }
    }

    private EnvelopeRespostaDTO totalizadorPorTipo(ServletRequest request) throws Exception {
        Connection con = null;
        Transacao transacaoDAO;
        try {
            con = obterConexao(request);
            transacaoDAO = new Transacao(con);
            FiltroPactoPayDTO filtroDTO = new FiltroPactoPayDTO(request);
            PaginadorDTO paginadorDTO = new PaginadorDTO(request);
            validarFiltro(filtroDTO);
            return EnvelopeRespostaDTO.of(transacaoDAO.consultarPactoPayTotalizadorPorTipo(filtroDTO, paginadorDTO), paginadorDTO);
        } finally {
            transacaoDAO = null;
            finalizarConexao(con);
        }
    }

    private EnvelopeRespostaDTO totalizadorPorParcela(ServletRequest request) throws Exception {
        Connection con = null;
        Transacao transacaoDAO;
        try {
            con = obterConexao(request);
            transacaoDAO = new Transacao(con);
            FiltroPactoPayDTO filtroDTO = new FiltroPactoPayDTO(request);
            PaginadorDTO paginadorDTO = new PaginadorDTO(request);
            validarFiltro(filtroDTO);
            return EnvelopeRespostaDTO.of(transacaoDAO.consultarPactoPayTotalizadorPorParcela(filtroDTO, paginadorDTO), paginadorDTO);
        } finally {
            transacaoDAO = null;
            finalizarConexao(con);
        }
    }

    private EnvelopeRespostaDTO totalizadorPorParcelaLista(ServletRequest request) throws Exception {
        Connection con = null;
        Transacao transacaoDAO;
        try {
            con = obterConexao(request);
            transacaoDAO = new Transacao(con);
            PaginadorDTO paginadorDTO = new PaginadorDTO(request);
            FiltroPactoPayDTO filtroDTO = new FiltroPactoPayDTO(request);
            validarFiltro(filtroDTO);
            return EnvelopeRespostaDTO.of(transacaoDAO.consultarPactoPayTotalizadorPorParcelaLista(filtroDTO, request.getParameter("situacao"), paginadorDTO), paginadorDTO);
        } finally {
            transacaoDAO = null;
            finalizarConexao(con);
        }
    }

    private EnvelopeRespostaDTO totalizadorPorBandeira(ServletRequest request) throws Exception {
        Connection con = null;
        Transacao transacaoDAO;
        try {
            con = obterConexao(request);
            transacaoDAO = new Transacao(con);
            FiltroPactoPayDTO filtroDTO = new FiltroPactoPayDTO(request);
            PaginadorDTO paginadorDTO = new PaginadorDTO(request);
            validarFiltro(filtroDTO);
            return EnvelopeRespostaDTO.of(transacaoDAO.consultarPactoPayTotalizadorPorBandeira(filtroDTO, paginadorDTO), paginadorDTO);
        } finally {
            transacaoDAO = null;
            finalizarConexao(con);
        }
    }

    private EnvelopeRespostaDTO retentarTransacoes(HttpServletRequest request) throws Exception {
        ConvenioCobranca convenioCobrancaDAO;
        Usuario usuarioDAO;
        MovParcela movParcelaDAO;
        TransacaoMovParcela transacaoMovParcelaDAO;
        Transacao transacaoDAO;
        PagamentoService pagamentoService;
        Connection con = null;
        try {
            con = obterConexao(request);
            convenioCobrancaDAO = new ConvenioCobranca(con);
            usuarioDAO = new Usuario(con);
            movParcelaDAO = new MovParcela(con);
            transacaoDAO = new Transacao(con);
            transacaoMovParcelaDAO = new TransacaoMovParcela(con);

            JSONObject jsonBody = new JSONObject(obterBody(request));
            Integer convenio = jsonBody.getInt("convenio");
            Integer empresa = jsonBody.getInt("empresa");
            String username = jsonBody.optString("username");
            JSONArray transacoes = jsonBody.getJSONArray("transacoes");

            if (UteisValidacao.emptyNumber(convenio)) {
                throw new Exception("Convênio não informado");
            }
            if (UteisValidacao.emptyNumber(empresa)) {
                throw new Exception("Empresa não informada");
            }
            if (UteisValidacao.emptyString(username)) {
                throw new Exception("Usuário não informada");
            }
            if (UteisValidacao.emptyNumber(transacoes.length())) {
                throw new Exception("Transação não informada");
            }

            UsuarioVO usuarioVO = usuarioDAO.consultarPorUsername(username, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            ConvenioCobrancaVO convenioCobrancaVO = convenioCobrancaDAO.consultarPorCodigoEmpresa(convenio, empresa, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            validarConvenioCobranca(convenioCobrancaVO);

            if (!convenioCobrancaVO.getTipo().isTransacaoOnline()) {
                throw new Exception("Convênio não é do tipo Transação Online");
            }

            Set<Integer> transacoesConsultar = new HashSet<>();
            for (int e = 0; e < transacoes.length(); e++) {
                Integer transacao = transacoes.getInt(e);
                try {
                    TransacaoVO transacaoVO = transacaoDAO.consultarPorChavePrimaria(transacao, Uteis.NIVELMONTARDADOS_BASICO_TRANSACAO);
                    if (!transacaoVO.isPermiteRetentativa()) {
                        continue;
                    }
                    transacoesConsultar.add(transacaoVO.getCodigo());
                } catch (Exception ex) {
                    ex.printStackTrace();
                }
            }

            if (UteisValidacao.emptyList(transacoesConsultar)) {
                throw new Exception("Nenhuma transação para realizar a retentativa.");
            }

            Set<Integer> parcelasAdicionadas = new HashSet<>();
            List<MovParcelaVO> listaParcelas = new ArrayList<>();

            Set<Integer> parcelasConsultar = transacaoMovParcelaDAO.obterListaParcelasRetentativa(transacoesConsultar);
            for (Integer parcela : parcelasConsultar) {
                try {
                    MovParcelaVO movParcelaVO = movParcelaDAO.consultarPorChavePrimaria(parcela, Uteis.NIVELMONTARDADOS_MINIMOS);
                    if (!movParcelaVO.getSituacao().equalsIgnoreCase("EA") ||
                            movParcelaDAO.parcelaEstaBloqueadaPorCobranca(movParcelaVO)) {
                        continue;
                    }

                    //validar se a parcela já foi adicionada na lista
                    if (parcelasAdicionadas.contains(movParcelaVO.getCodigo())) {
                        continue;
                    }
                    listaParcelas.add(movParcelaVO);
                    parcelasAdicionadas.add(movParcelaVO.getCodigo());
                } catch (Exception ex) {
                    ex.printStackTrace();
                }
            }

            if (UteisValidacao.emptyList(listaParcelas)) {
                throw new Exception("Nenhuma parcela para realizar a cobrança.");
            }

            String ipCliente = obterIpCliente(request);
            pagamentoService = new PagamentoService(con, convenioCobrancaVO);
            List<String> msgErro = new ArrayList<>();
            Set<Integer> transacaoCriadas = pagamentoService.processarCobrancaNaoPresencial(listaParcelas, usuarioVO, msgErro, true, OrigemCobrancaEnum.PACTO_PAY_RETENTATIVA, false, ipCliente);
            if (UteisValidacao.emptyNumber(transacaoCriadas.size())) {
                throw new Exception("Nenhuma retentativa realizada");
            }
            return EnvelopeRespostaDTO.of("Foi criado " + transacaoCriadas.size() + " transações.");
        } finally {
            convenioCobrancaDAO = null;
            usuarioDAO = null;
            movParcelaDAO = null;
            pagamentoService = null;
            finalizarConexao(con);
        }
    }

    private String obterIpCliente(HttpServletRequest request) {
        String ipCliente = "";
        try {
            String xForwardedForHeader = request.getHeader("X-Forwarded-For");
            if (xForwardedForHeader == null) {
                ipCliente = request.getRemoteAddr();
            } else {
                // As of https://en.wikipedia.org/wiki/X-Forwarded-For
                // The general format of the field is: X-Forwarded-For: client, proxy1, proxy2 ...
                // we only want the client
                ipCliente = new StringTokenizer(xForwardedForHeader, ",").nextToken().trim();
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return ipCliente;
    }

    private void validarConvenioCobranca(ConvenioCobrancaVO obj) throws Exception {
        if (obj.getSituacao().equals(SituacaoConvenioCobranca.INATIVO)) {
            throw new Exception("Convênio \"" + obj.getDescricao() + "\" não está ativo");
        }
    }

    public EnvelopeRespostaDTO cancelarTransacoes(ServletRequest request) throws Exception {
        Connection con = null;
        Transacao transacaoDAO = null;
        Usuario usuarioDAO = null;
        try {
            con = obterConexao(request);
            transacaoDAO = new Transacao(con);
            usuarioDAO = new Usuario(con);

            String key = request.getParameter("key");

            String body = obterBody(request);
            JSONObject jsonBody = new JSONObject(body);
            String username = jsonBody.optString("username");
            JSONArray transacoes = jsonBody.getJSONArray("transacoes");

            if (UteisValidacao.emptyString(username)) {
                throw new Exception("Usuario não informado");
            }

            if (UteisValidacao.emptyNumber(transacoes.length())) {
                throw new Exception("Nenhuma cobranca informada");
            }

            UsuarioVO usuarioVO = usuarioDAO.consultarPorUsername(username, Uteis.NIVELMONTARDADOS_METACOLABORADORRESPONSAVEL);
            if (usuarioVO == null || UteisValidacao.emptyNumber(usuarioVO.getCodigo())) {
                throw new Exception("Usuário não encontrado");
            }

            Integer totalCancelado = 0;
            for (int e = 0; e < transacoes.length(); e++) {
                try {
                    Integer transacao = transacoes.getInt(e);
                    TransacaoVO transacaoVO = transacaoDAO.consultarPorChavePrimaria(transacao);
                    if (!transacaoVO.isPermiteCancelar()) {
                        throw new Exception("Transação não permite cancelar");
                    }

                    String msgRetorno = transacaoDAO.cancelarTransacao(transacaoVO, true, usuarioVO, key);
                    transacaoVO = transacaoDAO.consultarPorChavePrimaria(transacao);
                    if (transacaoVO.getSituacao().equals(SituacaoTransacaoEnum.ESTORNADA) ||
                            transacaoVO.getSituacao().equals(SituacaoTransacaoEnum.CANCELADA)) {
                        totalCancelado++;
                    }
                    if (transacoes.length() == 1) {
                        return EnvelopeRespostaDTO.of(msgRetorno);
                    }
                } catch (Exception ex) {
                    if (transacoes.length() == 1) {
                        throw ex;
                    }
                }
            }

            if (UteisValidacao.emptyNumber(totalCancelado)) {
                throw new Exception("Nenhuma transação cancelada");
            } else {
                return EnvelopeRespostaDTO.of(totalCancelado > 1 ? "Foram canceladas " + totalCancelado + " transações" : "Transação cancelada");
            }
        } finally {
            transacaoDAO = null;
            usuarioDAO = null;
            finalizarConexao(con);
        }
    }

    public EnvelopeRespostaDTO sincronizarTransacoes(ServletRequest request) throws Exception {
        Connection con = null;
        Transacao transacaoDAO = null;
        Usuario usuarioDAO = null;
        try {
            con = obterConexao(request);
            transacaoDAO = new Transacao(con);
            usuarioDAO = new Usuario(con);

            String body = obterBody(request);
            JSONObject jsonBody = new JSONObject(body);
            String username = jsonBody.optString("username");
            JSONArray transacoes = jsonBody.getJSONArray("transacoes");

            if (UteisValidacao.emptyString(username)) {
                throw new Exception("Usuario não informado");
            }

            if (UteisValidacao.emptyNumber(transacoes.length())) {
                throw new Exception("Nenhuma cobranca informada");
            }

            UsuarioVO usuarioVO = usuarioDAO.consultarPorUsername(username, Uteis.NIVELMONTARDADOS_METACOLABORADORRESPONSAVEL);
            if (usuarioVO == null || UteisValidacao.emptyNumber(usuarioVO.getCodigo())) {
                throw new Exception("Usuário não encontrado");
            }

            Integer totalSucesso = 0;
            for (int e = 0; e < transacoes.length(); e++) {
                try {
                    Integer transacao = transacoes.getInt(e);
                    TransacaoVO transacaoVO = transacaoDAO.consultarPorChavePrimaria(transacao);
                    if (!transacaoVO.isPermiteSincronizar()) {
                        throw new Exception("Transação não permite sincronizar");
                    }

                    String msgRetorno = transacaoDAO.sincronizarTransacao(transacaoVO, usuarioVO, true);
                    if (transacoes.length() == 1) {
                        return EnvelopeRespostaDTO.of(msgRetorno);
                    }
                    totalSucesso++;
                } catch (Exception ex) {
                    if (transacoes.length() == 1) {
                        throw ex;
                    }
                }
            }

            if (UteisValidacao.emptyNumber(totalSucesso)) {
                throw new Exception("Nenhuma transação sincronizada");
            } else {
                return EnvelopeRespostaDTO.of(totalSucesso > 1 ? "Foram sincronizadas " + totalSucesso + " transações" : "Transação sincronizada");
            }
        } finally {
            transacaoDAO = null;
            usuarioDAO = null;
            finalizarConexao(con);
        }
    }

    public EnvelopeRespostaDTO enviarComprovantePagamentoTransacoes(ServletRequest request) throws Exception {
        Connection con = null;
        Transacao transacaoDAO = null;
        Email emailDAO = null;
        try {
            con = obterConexao(request);
            transacaoDAO = new Transacao(con);
            emailDAO = new Email(con);

            String body = obterBody(request);
            JSONObject jsonBody = new JSONObject(body);
            String email = jsonBody.optString("email");
            JSONArray transacoes = jsonBody.getJSONArray("transacoes");

            if (transacoes.length() > 1) {
                email = "";
            }

            if (UteisValidacao.emptyNumber(transacoes.length())) {
                throw new Exception("Nenhuma cobranca informada");
            }

            Integer totalSucesso = 0;
            for (int e = 0; e < transacoes.length(); e++) {
                try {
                    Integer transacao = transacoes.getInt(e);
                    TransacaoVO transacaoVO = transacaoDAO.consultarPorChavePrimaria(transacao);
                    if (UteisValidacao.emptyNumber(transacaoVO.getReciboPagamento())) {
                        throw new Exception("Transação sem recibo");
                    }

                    if (UteisValidacao.emptyString(email)) {
                        try {
                            List<EmailVO> emailVOList = emailDAO.consultarEmails(transacaoVO.getPessoaPagador().getCodigo(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                            for (EmailVO emailVO : emailVOList) {
                                if (!UteisValidacao.emptyString(emailVO.getEmail())) {
                                    email = emailVO.getEmail();
                                    break;
                                }
                            }
                        } catch (Exception ignored) {
                        }
                    }

                    transacaoDAO.enviarEmailComprovantePagamento(email, transacaoVO);
                    totalSucesso++;
                } catch (Exception ex) {
                    if (transacoes.length() == 1) {
                        throw ex;
                    }
                }
            }

            if (UteisValidacao.emptyNumber(totalSucesso)) {
                throw new Exception("Nenhum email enviado");
            } else {
                return EnvelopeRespostaDTO.of("Foi enviado o comprovante por email");
            }
        } finally {
            transacaoDAO = null;
            emailDAO = null;
            finalizarConexao(con);
        }
    }

    public EnvelopeRespostaDTO enviarComprovanteCancelamentoTransacoes(ServletRequest request) throws Exception {
        Connection con = null;
        Transacao transacaoDAO = null;
        Email emailDAO = null;
        try {
            con = obterConexao(request);
            transacaoDAO = new Transacao(con);
            emailDAO = new Email(con);

            String body = obterBody(request);
            JSONObject jsonBody = new JSONObject(body);
            String email = jsonBody.optString("email");
            JSONArray transacoes = jsonBody.getJSONArray("transacoes");

            if (transacoes.length() > 1) {
                email = "";
            }

            if (UteisValidacao.emptyNumber(transacoes.length())) {
                throw new Exception("Nenhuma cobranca informada");
            }

            Integer totalSucesso = 0;
            for (int e = 0; e < transacoes.length(); e++) {
                try {
                    Integer transacao = transacoes.getInt(e);
                    TransacaoVO transacaoVO = transacaoDAO.consultarPorChavePrimaria(transacao);
                    if (UteisValidacao.emptyNumber(transacaoVO.getReciboPagamento())) {
                        throw new Exception("Transação sem recibo");
                    }

                    if (UteisValidacao.emptyString(email)) {
                        try {
                            List<EmailVO> emailVOList = emailDAO.consultarEmails(transacaoVO.getPessoaPagador().getCodigo(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                            for (EmailVO emailVO : emailVOList) {
                                if (!UteisValidacao.emptyString(emailVO.getEmail())) {
                                    email = emailVO.getEmail();
                                    break;
                                }
                            }
                        } catch (Exception ignored) {
                        }
                    }

                    transacaoDAO.enviarEmailComprovanteCancelamento(email, transacaoVO);
                    totalSucesso++;
                } catch (Exception ex) {
                    if (transacoes.length() == 1) {
                        throw ex;
                    }
                }
            }

            if (UteisValidacao.emptyNumber(totalSucesso)) {
                throw new Exception("Nenhum email enviado");
            } else {
                return EnvelopeRespostaDTO.of("Foi enviado o comprovante por email");
            }
        } finally {
            transacaoDAO = null;
            emailDAO = null;
            finalizarConexao(con);
        }
    }

    private void validarFiltro(FiltroPactoPayDTO filtroDTO) throws Exception {
        if (filtroDTO.getInicioDate() == null) {
            throw new Exception("Data início pesquisa não informado");
        }
        if (filtroDTO.getFimDate() == null) {
            throw new Exception("Data fim pesquisa não informado");
        }
    }
}

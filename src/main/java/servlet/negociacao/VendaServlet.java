package servlet.negociacao;

import br.com.pactosolucoes.notificacao.ServicoNotificacaoPush;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import com.google.common.net.HttpHeaders;
import controle.arquitetura.exceptions.ServiceException;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.contrato.Contrato;
import negocio.facade.jdbc.plano.Produto;
import negocio.facade.jdbc.utilitarias.Conexao;
import org.json.JSONObject;
import servicos.vendasonline.VendasOnlineService;
import servicos.vendasonline.dto.RetornoVendaDTO;
import servicos.vendasonline.dto.VendaDTO;
import servicos.vendasonline.dto.VendaProdutoDTO;
import servlet.arquitetura.SuperServlet;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.sql.Connection;
import java.util.ArrayList;
import java.util.List;

public class VendaServlet extends SuperServlet {

    @Override
    protected void processRequest(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {

        response.setHeader("Access-Control-Allow-Origin", "*");
        response.setHeader("Access-Control-Allow-Methods", "GET, OPTIONS");
        response.setHeader("Access-Control-Max-Age", "3600");
        response.setHeader("Access-Control-Allow-Headers", "Authorization, content-type, xsrf-token");
        response.addHeader("Access-Control-Expose-Headers", "xsrf-token");
        response.setContentType("application/json");
        response.setCharacterEncoding("UTF-8");


        try {
            validarAuthorizationComChave(request);
            VendaDTO vendaDTO = new VendaDTO(getJSONBody(request));
            vendaDTO.setVendaConsultor(true);

            validarPayload(vendaDTO);

            String chave = request.getHeader(HttpHeaders.AUTHORIZATION);
            processarVenda(request, response, chave, vendaDTO);
        } catch (Exception e) {

            if (e.getMessage() != null && e.getMessage().contains("ERROR: trigger:Aluno já possui contrato de matrícula lançado a poucos minutos com esse mesmo plano")) {
                JSONObject result = new JSONObject();
                result.put("sucesso", false);
                result.put("mensagem", "Aluno já possui contrato de matrícula lançado a poucos minutos com esse mesmo plano");
                response.setStatus(400);
                response.getWriter().append(result.toString());
                return;
            }

            processarErro(new ServiceException(e), request, response, null);
        }
    }

    private void processarVenda(HttpServletRequest request, HttpServletResponse response, String chave, VendaDTO vendaDTO) throws Exception {
        try (Connection connection = new DAO().obterConexaoEspecifica(chave)) {
            Conexao.guardarConexaoForJ2SE(chave, connection);

            boolean validaProdutoPactoApp = Boolean.parseBoolean(request.getParameter("pactoApp"));
            if (validaProdutoPactoApp) {
                validaProdutosPactoApp(vendaDTO, connection);
            }

            VendasOnlineService vendasOnlineService = new VendasOnlineService(chave, connection);

            RetornoVendaDTO retornoVendaDTO = vendasOnlineService.incluirAlunoVendaApp(chave, vendaDTO);

            if (retornoVendaDTO.isErro()) {
                JSONObject result = new JSONObject();
                result.put("sucesso", false);
                result.put("mensagem", retornoVendaDTO.getMsgRetorno());
                response.setStatus(400);
                response.getWriter().append(result.toString());
            }

            registraNotificaoApp(vendaDTO, chave, retornoVendaDTO, connection);

            response.getWriter().append(retornoVendaDTO.toJSON().toString());

            vendasOnlineService = null;
        }
    }

    private void validaProdutosPactoApp(VendaDTO vendaDTO, Connection con) throws Exception {
        Produto produto = new Produto(con);
        List<Integer> codProdutosNaoApresentaPactoApp = new ArrayList<>();
        List<Integer> listaProdutos = new ArrayList<>();
        for(VendaProdutoDTO vendaProdutoDTO : vendaDTO.getProdutos()) {
            listaProdutos.add(vendaProdutoDTO.getProduto());
        }
        codProdutosNaoApresentaPactoApp.addAll(produto.produtoNaoApresentaAppPacto(listaProdutos, vendaDTO.getUnidade()));

        if(!codProdutosNaoApresentaPactoApp.isEmpty()) {
            String erro = "Existem produtos que não podem ser apresentado pelo App. Código(s): " + codProdutosNaoApresentaPactoApp.get(0);
            for (int i = 1; i < codProdutosNaoApresentaPactoApp.size(); i++) {
                erro += ", " + codProdutosNaoApresentaPactoApp.get(i);
            }
            throw new Exception(erro);
        }
    }

    private static void registraNotificaoApp(VendaDTO vendaDTO, String chave, RetornoVendaDTO retornoVendaDTO, Connection connection) throws Exception {
        if (retornoVendaDTO.getContrato() != null) {
            Contrato contrato = new Contrato(connection);
            ContratoVO contratoVO = contrato.consultarPorCodigo(retornoVendaDTO.getContrato().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);
            ServicoNotificacaoPush.enviaNotificacaoContrato(chave, vendaDTO.getUnidade(), false, contrato.consultaOrigemSistema(retornoVendaDTO.getContrato().getCodigo()),
                    contratoVO.getValorFinal(), contratoVO.getPlano().getDescricao(), contratoVO.getContratoDuracao().getNumeroMeses(), contratoVO.getEmpresa().getNome(), contratoVO.getCliente().getCodigo(), connection, contratoVO.getResponsavelContrato().getColaboradorVO().getPessoa().getPrimeiroNomeConcatenado());
            contrato = null;
        }
    }

    private void validarPayload(VendaDTO vendaDTO) throws ServiceException {
        if (vendaDTO.isVendaConsultor() && UteisValidacao.emptyNumber(vendaDTO.getCodigoColaborador())) {
            throw new ServiceException("O codigoColaborador é obrigatório");
        }

        if (vendaDTO.getCpf().isEmpty()) {
            throw new ServiceException("O cpf precisa ser informado");
        }
    }
}

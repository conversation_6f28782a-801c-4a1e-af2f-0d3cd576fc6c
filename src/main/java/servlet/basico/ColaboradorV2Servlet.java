package servlet.basico;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.basico.Colaborador;
import org.json.JSONObject;
import servlet.arquitetura.SuperServlet;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.sql.Connection;

public class ColaboradorV2Servlet extends SuperServlet {

    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        processRequest(request, response);
    }

    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        processRequest(request, response);
    }

    protected void processRequest(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {

        response.addHeader("Access-Control-Allow-Origin", "*");
        response.addHeader("Access-Control-Allow-Methods", "GET,POST");
        response.setContentType("application/json");

        String path = request.getServletPath();
        String[] pathParts = path.split("/");
        String recurso = pathParts[3];

        String chave = request.getParameter("chave");

        try {
            switch (recurso) {
                case "consultar":
                    try (Connection con = new DAO().obterConexaoEspecifica(chave)) {
                        JSONObject filtro = new JSONObject(request.getParameter("filtro"));
                        String email = getParamString(filtro, "email");
                        String cpf = getParamString(filtro, "cpf");
                        Integer empresa = getParamInt(filtro, "empresa");
                        Integer colaborador = getParamInt(filtro, "colaborador");
                        Colaborador colaboradorDAO = new Colaborador(con);
                        response.getWriter().append(colaboradorDAO.obterColaboradorEmailCpfDTO(
                                colaboradorDAO.consultarColaboradorEmailOuCPF(email, cpf, empresa, colaborador, Uteis.NIVELMONTARDADOS_DADOSBASICOS)).toString());
                    }
                    break;
            }
        } catch (Exception ex) {
            response.getWriter().append("ERRO: " + ex.getMessage());
        }
    }

    private String getParamString(JSONObject obj, String param) {
        try {
            return obj.getString(param);
        } catch (Exception ex) {
            return "";
        }
    }

    private Integer getParamInt(JSONObject obj, String param) {
        try {
            return obj.getInt(param);
        } catch (Exception ex) {
            return 0;
        }
    }
}

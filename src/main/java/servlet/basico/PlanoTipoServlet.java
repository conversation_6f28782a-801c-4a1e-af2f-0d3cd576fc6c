package servlet.basico;

import org.json.JSONObject;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisJSON;
import servlet.arquitetura.SuperServlet;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;

public class PlanoTipoServlet extends SuperServlet {
    @Override
    protected void processRequest(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        super.processRequest(request, response);
        PrintWriter out = response.getWriter();
        JSONObject result = new JSONObject();
        try {
            result.put("aaData", ff(request).getPlanoTipo().consultarJson(Uteis.NIVELMONTARDADOS_DADOSBASICOS).toString());
        } catch (Exception e) {
            Uteis.logar(e, PlanoTipoServlet.class);
        }
        out.println(UteisJSON.arrayJsonToString(result));
    }
}
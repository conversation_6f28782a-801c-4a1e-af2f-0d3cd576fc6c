package servlet.basico;

import controle.arquitetura.security.LoginControle;
import servlet.arquitetura.SuperServlet;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;

public class ConfiguracaoNotaFiscalServlet extends SuperServlet {
    @Override
    protected void processRequest(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        super.processRequest(request, response);
        PrintWriter out = response.getWriter();
        String json;
        try {
            LoginControle loginControle = ((LoginControle)getAttribute(request, "LoginControle"));
            Integer empresa = loginControle.getEmpresa().getCodigo();
            String situacao = obterParametroString(request.getParameter("situacao"));
            if (loginControle.getUsuario().getUsuarioPactoSolucoes()) {
                empresa = 0;
            }
            json = ff(request).getConfiguracaoNotaFiscal().consultarJSON(empresa, situacao);
        } catch (Exception e) {
            json = (e.getMessage() == null) ? NullPointerException.class.getName() : e.getMessage();
        }
        out.println(json);
        json = null;
    }
}

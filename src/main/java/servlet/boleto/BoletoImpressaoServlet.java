package servlet.boleto;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.basico.AuxiliarBoletoBancarioTO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.financeiro.BoletoVO;
import negocio.comuns.financeiro.ConvenioCobrancaVO;
import negocio.comuns.financeiro.enumerador.ArquivoLayoutRemessaEnum;
import negocio.comuns.financeiro.enumerador.TipoBoletoEnum;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.basico.Empresa;
import negocio.facade.jdbc.financeiro.Boleto;
import negocio.facade.jdbc.financeiro.ConvenioCobranca;
import negocio.facade.jdbc.utilitarias.Conexao;
import org.jboleto.JBoleto;
import org.jboleto.JBoletoBean;
import org.json.JSONObject;
import relatorio.controle.arquitetura.SuperControleRelatorio;
import servicos.impl.boleto.itau.ItauService;
import servicos.operacoes.midias.commons.MidiaEntidadeEnum;
import servicos.propriedades.PropsService;
import servlet.arquitetura.SuperServlet;

import javax.servlet.ServletException;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.PrintWriter;
import java.sql.Connection;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * Created with IntelliJ IDEA.
 * User: Luiz Felipe
 * Date: 06/01/2022
 * <p>
 * #########################################################################################
 * ######################################## ATENÇÃO ########################################
 * <p>
 * ESSA CLASSE FOI CRIADA COM O OBJETIVO DE GERAR O PDF DO BOLETO ONLINE
 * SÓ É ACEITO REQUISAÇÃO GET !
 * <p>
 * ######################################## REGRAS ########################################
 * <p>
 * 1 - Passar a chave da empresa via parametro (key)
 * 2 - Passar o código do boleto via parametro (codigo)
 * Exemplo:
 * http://localhost:8084/zw/prest/boleto/imprimir?key=teste&codigo=12
 * <p>
 * #########################################################################################
 * #########################################################################################
 */
public class BoletoImpressaoServlet extends SuperServlet {

    @Override
    protected void processRequest(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        Connection con = null;
        Boleto boletoDAO = null;
        ConvenioCobranca convenioCobrancaDAO = null;
        try {
            response.addHeader("Access-Control-Allow-Origin", "*");
            response.addHeader("Access-Control-Allow-Methods", "GET");
            response.setContentType("application/json");
            response.setCharacterEncoding("UTF-8");

            String method = request.getMethod();

            if (!method.equalsIgnoreCase("GET")) {
                throw new Exception("Methodo não suportado para esse recurso");
            }

            String token = request.getParameter("tk");
            if (UteisValidacao.emptyString(token)) {
                throw new Exception("Token não informado");
            }

            JSONObject json = new JSONObject(Uteis.desencriptar(token, PropsService.getPropertyValue(PropsService.chaveCriptoBoleto)));

            String key = json.getString("chave");
            Integer boleto = json.getInt("boleto");

            if (UteisValidacao.emptyString(key)) {
                throw new Exception("Key não informada");
            }
            if (UteisValidacao.emptyNumber(boleto)) {
                throw new Exception("Boleto não informado");
            }

            con = new DAO().obterConexaoEspecifica(key);
            boletoDAO = new Boleto(con);
            convenioCobrancaDAO = new ConvenioCobranca(con);

            BoletoVO boletoVO = boletoDAO.consultarPorChavePrimaria(boleto, Uteis.NIVELMONTARDADOS_DADOSBASICOS);

            if (boletoVO.getTipo().equals(TipoBoletoEnum.ITAU)) {
                ConvenioCobrancaVO convenioCobrancaVO = convenioCobrancaDAO.consultarPorChavePrimaria(boletoVO.getConvenioCobrancaVO().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS);
                boletoVO.setConvenioCobrancaVO(convenioCobrancaVO);

                ItauService itauService = new ItauService(con, boletoVO.getEmpresaVO().getCodigo(), convenioCobrancaVO.getCodigo());
                JBoletoBean boletoBean = itauService.obterJBoletoBean(boletoVO);
                JBoleto jBoleto = new JBoleto(null, boletoBean, convenioCobrancaVO.getBanco().getCodigoBanco(), false);

                Map<String, Object> params = obterParamsImpressao(key, boletoVO, jBoleto, con);
                Conexao.guardarConexaoForJ2SE(key, con); //necessário devido a impressão
                String arquivo = new SuperControleRelatorio().imprimirRetornaNomeArquivo(request, params);
                File pdfFile = new File(request.getRealPath("relatorio") + File.separator + arquivo);

                //fazer download do arquivo
//            String nomeDownload = "BOLETO-" + key + "-"+ boletoVO.getNossoNumero() + ".pdf";
//            response.setContentType("application/pdf");
//            response.setHeader("Content-Disposition", "attachment; filename=" + nomeDownload);
//            response.setContentLength((int) pdfFile.length());
//
//            FileInputStream fileInputStream = new FileInputStream(pdfFile);
//            OutputStream responseOutputStream = response.getOutputStream();
//            int bytes;
//            while ((bytes = fileInputStream.read()) != -1) {
//                responseOutputStream.write(bytes);
//            }

                //abrir o pdf
                response.setContentType("application/pdf");
                response.setContentLength((int) pdfFile.length());
                ServletOutputStream ouputStream = response.getOutputStream();
                ouputStream.write(fileToByte(pdfFile), 0, (int) pdfFile.length());
                ouputStream.flush();
                ouputStream.close();

            } else if (boletoVO.getTipo().equals(TipoBoletoEnum.PJ_BANK)) {

                response.sendRedirect(boletoVO.getLinkBoleto());
            }
        } catch (Exception ex) {
            response.setStatus(400);
            PrintWriter out = response.getWriter();
            out.print(this.toJSON(false, ex.getMessage()));
            out.flush();
        } finally {
            boletoDAO = null;
            convenioCobrancaDAO = null;
            try {
                if (con != null) {
                    con.close();
                }
            } catch (Exception ex) {
                ex.printStackTrace();
            }
        }
    }

    public static byte[] fileToByte(File imagem) throws Exception {
        FileInputStream fis = new FileInputStream(imagem);
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        byte[] buffer = new byte[8192];
        int bytesRead = 0;
        while ((bytesRead = fis.read(buffer, 0, 8192)) != -1) {
            baos.write(buffer, 0, bytesRead);
        }
        return baos.toByteArray();
    }

    private JSONObject toJSON(boolean sucesso, Object obj) {
        JSONObject json = new JSONObject();
        json.put("sucesso", sucesso);
        json.put("dados", obj);
        return json;
    }

    private Map<String, Object> obterParamsImpressao(String chave, BoletoVO boletoVO, JBoleto boleto, Connection con) throws Exception {

        Map<String, Object> params = new HashMap<String, Object>();

        EmpresaVO empresaVO = new EmpresaVO();
        byte[] propaganda = null;

        Empresa empresaDAO;
        try {
            empresaDAO = new Empresa(con);
            empresaVO = empresaDAO.consultarPorChavePrimaria(boletoVO.getEmpresaVO().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            propaganda = empresaDAO.obterFotoPadrao(chave, empresaVO.getCodigo(), MidiaEntidadeEnum.FOTO_EMPRESA_PROPAGANDA_BOLETO);
        } finally {
            empresaDAO = null;
        }

        ArquivoLayoutRemessaEnum layoutRemessaEnum = boletoVO.getConvenioCobrancaVO().getLayoutBoletoOnline();

        params.put("nomeRelatorio", "Boleto");
        params.put("nomeEmpresa", empresaVO.getNome());

        params.put("tituloRelatorio", "Boleto");
        params.put("nomeDesignIReport", getDesignIReportRelatorio(layoutRemessaEnum));
        params.put("nomeDesignSubReport", layoutRemessaEnum.getSubReport());
//        if (JSFUtilities.isJSFContext()) {
//            params.put("nomeUsuario", getUsuarioLogado().getNomeAbreviado());
//        } else {
        params.put("nomeUsuario", "BOLETO_USUARIO");
//        }

        List<AuxiliarBoletoBancarioTO> auxiliarBoletoBancarioTOs = new ArrayList<AuxiliarBoletoBancarioTO>();
        AuxiliarBoletoBancarioTO auxiliar = new AuxiliarBoletoBancarioTO();
        auxiliar.setBoleto(boleto);
        auxiliarBoletoBancarioTOs.add(auxiliar);

        params.put("listaObjetos", auxiliarBoletoBancarioTOs);

        StringBuilder sb = getEnderecoEmpresa(empresaVO);

        params.put("enderecoEmpresa", sb.toString());
        params.put("cnpjEmpresa", empresaVO.getCNPJ());
        params.put("cidadeEmpresa", empresaVO.getCidade().getNome());
        params.put("SUBREPORT_DIR", getCaminhoSubRelatorio());

        InputStream fs = null;
        if (propaganda != null)
            fs = new ByteArrayInputStream(propaganda);
        params.put("propaganda", fs);
        return params;
    }

    public String getDesignIReportRelatorio(ArquivoLayoutRemessaEnum layoutRemessaEnum) throws ConsistirException {
        String nomeArquivo = layoutRemessaEnum.getReport();
        if (UteisValidacao.emptyString(nomeArquivo)) {
            throw new ConsistirException("Configuração do tipo de remessa inválido");
        }
        return "relatorio" + File.separator + "designRelatorio" + File.separator + "financeiro" + File.separator + "boletos" + File.separator + nomeArquivo + ".jrxml";
    }

    public static String getCaminhoSubRelatorio() {
        return ("relatorio" + File.separator + "designRelatorio" + File.separator + "financeiro" + File.separator + "boletos" + File.separator);
    }

    public StringBuilder getEnderecoEmpresa(EmpresaVO empre) {
        StringBuilder sb = new StringBuilder();
        boolean adicionouEndereco = false;
        if (!UteisValidacao.emptyString(empre.getEndereco())) {
            sb.append(empre.getEndereco());
            adicionouEndereco = true;
        }
        if (!UteisValidacao.emptyString(empre.getNumero())) {
            if (adicionouEndereco) {
                sb.append(", ");
            }
            sb.append(empre.getNumero());
            adicionouEndereco = true;
        }
        if (!UteisValidacao.emptyString(empre.getSetor())) {
            if (adicionouEndereco) {
                sb.append(", ");
            }
            sb.append(empre.getSetor());
            adicionouEndereco = true;
        }
        if (!UteisValidacao.emptyString(empre.getCidade_Apresentar())) {
            if (adicionouEndereco) {
                sb.append(", ");
            }
            sb.append(empre.getCidade_Apresentar());
            adicionouEndereco = true;
        }
        if (!UteisValidacao.emptyString(empre.getEstado().getSigla())) {
            if (adicionouEndereco) {
                sb.append(", ");
            }
            sb.append(empre.getEstado().getSigla());
            adicionouEndereco = true;
        }
        if (!UteisValidacao.emptyString(empre.getCEP())) {
            if (adicionouEndereco) {
                sb.append(", ");
            }
            sb.append(empre.getCEP());
        }
        return sb;
    }
}

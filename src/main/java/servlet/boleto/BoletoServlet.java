package servlet.boleto;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import servicos.boleto.BoletoService;
import servlet.arquitetura.SuperServlet;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.sql.Connection;

public class BoletoServlet extends SuperServlet {

    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        processRequest(request, response);
    }

    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        processRequest(request, response);
    }

    @Override
    protected void processRequest(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        response.addHeader("Access-Control-Allow-Origin", "*");
        response.addHeader("Access-Control-Allow-Methods", "GET");
        response.setContentType("application/json");

        String path = request.getServletPath();
        String[] pathParts = path.split("/");
        String recurso = pathParts[3];

        String chave = request.getParameter("chave");
        Integer empresa = Integer.valueOf(request.getParameter("empresa"));

        try {
            switch (recurso){
                case "todos":
                    try (Connection con = new DAO().obterConexaoEspecifica(chave)) {
                        String matricula = request.getParameter("matricula");
                        int movParcela = Integer.parseInt(request.getParameter("movParcela"));
                        int limit = Integer.parseInt(request.getParameter("limit"));
                        int offset = Integer.parseInt(request.getParameter("offset"));
                        response.getWriter().append(new BoletoService(con).todos(empresa, matricula, limit, offset, movParcela).toString());
                    }
                    break;
            }
        }catch (Exception e ){
            System.out.println("Erro na api rest "+recurso+". Message: "+e.getMessage());
            processarErro(e, request, response, null);
        }
    }
}

package servlet.boleto;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.financeiro.enumerador.TipoBoletoEnum;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.financeiro.Boleto;
import org.json.JSONObject;
import servlet.arquitetura.SuperServlet;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.PrintWriter;
import java.sql.Connection;


/**
 * Created with IntelliJ IDEA.
 * User: <PERSON><PERSON>
 * Date: 07/12/2021
 * <p>
 * #########################################################################################
 * ######################################## ATENÇÃO ########################################
 * <p>
 * ESSA CLASSE FOI CRIADA COM O OBJETIVO DE RECEBER WEBHOOK DE INTEGRAÇÕES DE BOLETO ONLINE
 * SÓ É ACEITO REQUISAÇÃO POST !
 * <p>
 * ######################################## REGRAS ########################################
 * <p>
 * 1 - Passar a chave da empresa via parametro (key)
 * 2 - Passar o tipo do boleto (TipoBoletoEnum) via parametro (tipo)
 * 3 - Passar o código do boleto via parametro (codigo)
 * Exemplo:
 * http://localhost:8084/zw/prest/boleto/webhook?key=teste&codigo=12
 * <p>
 * #########################################################################################
 * #########################################################################################
 */
public class BoletoWebhookServlet extends SuperServlet {

    @Override
    protected void processRequest(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        Connection con = null;
        Boleto boletoDAO = null;
        try {
            response.addHeader("Access-Control-Allow-Origin", "*");
            response.addHeader("Access-Control-Allow-Methods", "GET");
            response.setContentType("application/json");
            response.setCharacterEncoding("UTF-8");

            String method = request.getMethod();

            if (!method.equalsIgnoreCase("POST")) {
                throw new Exception("Methodo não suportado para esse recurso");
            }

            String key = request.getParameter("key");
            String tipo = request.getParameter("tipo");
            String boleto = request.getParameter("boleto");
            String boletoExterno = request.getParameter("boletoExterno");
            String convenio = request.getParameter("convenio");

            if (UteisValidacao.emptyString(key)) {
                throw new Exception("Key não informada");
            }
            if (UteisValidacao.emptyString(tipo)) {
                throw new Exception("Tipo não informado");
            }

            StringBuffer body = new StringBuffer();
            try {
                InputStream inputStream = request.getInputStream();
                BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream, "UTF-8"));

                String line = null;

                while ((line = reader.readLine()) != null) {
                    body.append(line);
                }
            } catch (Exception ex) {
                ex.printStackTrace();
                throw new Exception("Erro body: " + ex.getMessage());
            }

            con = new DAO().obterConexaoEspecifica(key);
//            Conexao.guardarConexaoForJ2SE(key, con);
            boletoDAO = new Boleto(con);
            if (tipo.equals(TipoBoletoEnum.BANCO_BRASIL.getCodigo().toString())) {
                boletoDAO.processarWebhookBancoBrasil(body.toString());
            } else {
                boletoDAO.processarWebhook(TipoBoletoEnum.obterPorCodigo(Integer.parseInt(tipo)),
                        UteisValidacao.converterInteiro(boleto),
                        UteisValidacao.converterInteiro(convenio),
                        body.toString(), boletoExterno);
            }

            response.setStatus(HttpServletResponse.SC_OK);
            PrintWriter out = response.getWriter();
            out.print(this.toJSON(true, "ok"));
            out.flush();
        } catch (Exception ex) {
            response.setStatus(400);
            PrintWriter out = response.getWriter();
            out.print(this.toJSON(false, ex.getMessage()));
            out.flush();
        } finally {
            boletoDAO = null;
            try {
                if (con != null) {
                    con.close();
                }
            } catch (Exception ex) {
                ex.printStackTrace();
            }
        }
    }

    private JSONObject toJSON(boolean sucesso, Object obj) {
        JSONObject json = new JSONObject();
        json.put("sucesso", sucesso);
        json.put("dados", obj);
        return json;
    }
}

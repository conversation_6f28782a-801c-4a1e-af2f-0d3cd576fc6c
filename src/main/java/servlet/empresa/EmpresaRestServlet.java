package servlet.empresa;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.basico.Empresa;
import org.json.JSONArray;
import servlet.arquitetura.SuperServlet;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.sql.Connection;

public class EmpresaRestServlet extends SuperServlet {

    @Override
    protected void processRequest(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {

        response.addHeader("Access-Control-Allow-Origin", "*");
        response.addHeader("Access-Control-Allow-Methods", "GET");
        response.setContentType("application/json");

        String path = request.getServletPath();
        String[] pathParts = path.split("/");
        String recurso = pathParts[3];

        String chave = request.getParameter("chave");
        String codEmpresa = request.getParameter("empresa");

        if (!UteisValidacao.emptyString(codEmpresa)) {
            recurso = "nomeEspecifica";
        }

        try (Connection con = new DAO().obterConexaoEspecifica(chave)) {
            Empresa empresaDAO = new Empresa(con);
            switch (recurso) {
                case "todas":
                    JSONArray empresasArray = empresaDAO.todas(chave);
                    response.getWriter().append(empresasArray.toString());
                    empresasArray = null;
                    break;
                case "nomeEspecifica":
                    EmpresaVO empresaVO = empresaDAO.consultarPorChavePrimaria(Integer.parseInt(codEmpresa), Uteis.NIVELMONTARDADOS_MINIMOS);
                    response.getWriter().append(empresaVO.getNome());
                    empresaVO = null;
                    break;
            }
        } catch (Exception e) {
            System.out.println("Erro na api rest " + recurso + ". Message: " + e.getMessage());
            processarErro(e, request, response, null);
        }
    }
}

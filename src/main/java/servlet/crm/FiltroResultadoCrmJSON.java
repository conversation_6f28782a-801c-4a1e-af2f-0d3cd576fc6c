package servlet.crm;

import br.com.pactosolucoes.comuns.json.SuperJSON;
import negocio.interfaces.bi.FiltroJSON;

public class FiltroResultadoCrmJSON extends SuperJSON implements FiltroJSON {

    private Integer empresa;
    private Long dataInicio;
    private Long dataFim;
    private Integer usuario;

    public FiltroResultadoCrmJSON(Integer empresa, Long dataInicio, Long dataFim, Integer usuario) {
        this.empresa = empresa;
        this.dataInicio = dataInicio;
        this.dataFim = dataFim;
        this.usuario = usuario;
    }

    public String getToken() {
        return "empresa=" + getEmpresa() +
                "|usuario="+ getUsuario() +
                "|dataInicio=" + getDataInicio() +
                "|dataFim=" + getDataFim();
    }

    public Integer getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Integer empresa) {
        this.empresa = empresa;
    }

    public Long getDataInicio() {
        return dataInicio;
    }

    public void setDataInicio(Long dataInicio) {
        this.dataInicio = dataInicio;
    }

    public Long getDataFim() {
        return dataFim;
    }

    public void setDataFim(Long dataFim) {
        this.dataFim = dataFim;
    }

    public Integer getUsuario() {
        return usuario;
    }

    public void setUsuario(Integer usuario) {
        this.usuario = usuario;
    }
}

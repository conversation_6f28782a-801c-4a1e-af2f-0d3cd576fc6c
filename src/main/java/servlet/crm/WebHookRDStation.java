/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package servlet.crm;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import java.io.IOException;
import java.sql.Connection;
import java.util.Set;
import java.util.logging.Level;
import java.util.logging.Logger;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import org.json.JSONObject;
import servicos.impl.gatewaypagamento.VerificadorTransacaoService;
import servicos.integracao.impl.rd.IntegracaoRDServiceImpl;
import servlet.arquitetura.SuperServlet;

/**
 *
 * <AUTHOR>
 */
public class WebHookRDStation extends SuperServlet{

    @Override
    protected void processRequest(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        try{
            Logger.getLogger(this.getClass().getName()).log(Level.INFO, "Chamada ao Webhook RD");
            String chave = request.getParameter("key");
            Integer empresa = Integer.parseInt(request.getParameter("emp"));
            String params = "";
            try {
                Set keySet = request.getParameterMap().keySet();
                for(Object k : keySet){
                    params += k.toString() + ":"+request.getParameter(k.toString())+";";
                }
                Logger.getLogger(this.getClass().getName()).log(Level.INFO, "Webhook chamado com os seguintes parametros:{0}", params);
            } catch (Exception e) {
                //IGNORE
            }
            
            if(!UteisValidacao.emptyString(chave)){
                Connection con = new DAO().obterConexaoEspecifica(chave);
                String body = Uteis.convertStreamToStringBuffer(request.getInputStream()).toString();
                new IntegracaoRDServiceImpl(con).processarNovaLead(body,empresa);
            }
        }catch (Exception e){
            Uteis.logar(e, this.getClass());
        }
    }
}

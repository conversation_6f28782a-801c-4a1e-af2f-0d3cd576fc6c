package servlet.crm;

import acesso.webservice.DaoAuxiliar;
import negocio.comuns.crm.ConfiguracaoEmpresaBitrixVO;
import negocio.facade.jdbc.crm.ConfiguracaoSistemaCRM;
import org.json.JSONObject;
import servicos.integracao.sendy.services.SendyImpl;
import servicos.integracao.sendy.to.BrandTO;
import servlet.arquitetura.SuperServlet;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;

public class ConfigBitirxRestServlet extends SuperServlet {

    @Override
    protected void processRequest(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        response.addHeader("Access-Control-Allow-Origin", "*");
        response.addHeader("Access-Control-Allow-Methods", "GET");
        response.setContentType("application/json");
        String chave = request.getParameter("chave");
        String acao = request.getParameter("acao");
        try{
            InputStream inputStream = request.getInputStream();
            JSONObject json = new JSONObject();
            ConfiguracaoEmpresaBitrixVO config = getDao(chave).consultarConfiguracaoEmpresaBitrix24(chave).get(0);
            json.put("url", config.getUrl());
            json.put("acao", config.getAcao());
            json.put("acaoobjecao", config.getAcaoobjecao());
            json.put("responsavelPadrao", config.getResponsavelPadrao());

            response.getOutputStream().print(json.toString());

        }  catch (Exception ex){}
    }

    private ConfiguracaoSistemaCRM getDao(String chave) throws Exception {
        return new ConfiguracaoSistemaCRM(DaoAuxiliar.retornarAcessoControle(chave).getCon());
    }
}

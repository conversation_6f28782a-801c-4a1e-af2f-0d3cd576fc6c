package servlet.crm;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.facade.jdbc.basico.Empresa;
import org.json.JSONObject;
import servlet.arquitetura.SuperServlet;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.sql.Connection;

public class ConfigTokenMqvServlet extends SuperServlet {

    @Override
    protected void processRequest(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        Connection con = null;
        try
        {
            String key = request.getParameter("chave");
            String empresa = request.getParameter("empresa");
            String token = request.getParameter("token");
            con = new DAO().obterConexaoEspecifica(key);
            Empresa emp = new Empresa(con);
            emp.atualizarTokenMqvEmpresa(empresa, token);
            response.getWriter().append(this.toJSON(true, "Ok").toString());
        }
        catch (Exception ex) {
          ex.printStackTrace();
          response.getWriter().append(this.toJSON(false, ex.getMessage()).toString());
        }
    }

    private JSONObject toJSON(boolean sucesso, Object dados) {
        JSONObject json = new JSONObject();
        json.put("sucesso", sucesso);
        json.put("dados", dados);
        return json;
    }
}

package servlet.crm;

import acesso.webservice.DaoAuxiliar;
import negocio.facade.jdbc.crm.ConfiguracaoSistemaCRM;
import org.json.JSONObject;
import servicos.integracao.sendy.services.SendyImpl;
import servicos.integracao.sendy.to.BrandTO;
import servlet.arquitetura.SuperServlet;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

public class ConfigCRMRestServlet extends SuperServlet {

    @Override
    protected void processRequest(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {

        response.addHeader("Access-Control-Allow-Origin", "*");
        response.addHeader("Access-Control-Allow-Methods", "GET");
        response.setContentType("application/json");

        String chave = request.getParameter("chave");
        String credito = request.getParameter("limite");

        try {
            getDao(chave).alterarLimiteMensal(Integer.valueOf(credito));
            JSONObject retorno = new JSONObject();
            retorno.put("return", "OK");
            SendyImpl sendy = new SendyImpl();
            retorno.put("returnSendy", sendy.updateQuota(new BrandTO(chave, credito)));
            response.getWriter().append(retorno.toString());
        }catch (Exception e ){
            System.out.println("Erro na api rest . Message: "+e.getMessage());
            processarErro(e, request, response, null);
        }
    }

    private ConfiguracaoSistemaCRM getDao(String chave) throws Exception {
        return new ConfiguracaoSistemaCRM(DaoAuxiliar.retornarAcessoControle(chave).getCon());
    }

}

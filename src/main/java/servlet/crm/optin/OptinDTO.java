package servlet.crm.optin;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.crm.optin.OptinVO;
import negocio.comuns.crm.optin.OrigemEnvioEnum;
import org.json.JSONObject;

import java.time.LocalDate;
import java.util.Objects;


public class OptinDTO {

    private Integer codigo;
    private Integer cliente;
    private Integer empresa;
    private String email;
    private String ipCliente;
    private Boolean bloqueadoBounce;
    private Integer origemEnvio;

    private String dataRegistro;

    private String dataInscricao;

    private String dataExclusao;

    public OptinDTO() {
    }

    public OptinDTO(JSONObject json) {
        this.codigo = json.optInt("codigo");
        this.cliente = !Objects.equals(json.optString("cliente"), "TAG_CODCLIENTE") ? json.optInt("cliente") : 0;
        this.empresa = json.optInt("empresa");
        this.email = json.optString("email");
        this.ipCliente = json.optString("ipCliente");
        this.bloqueadoBounce = json.optBoolean("bloqueadoBounce");
        this.origemEnvio = json.optInt("origemEnvio");
        this.dataRegistro = json.optString("dataRegistro");
        this.dataInscricao = json.optString("dataInscricao");
        this.dataExclusao = json.optString("dataExclusao");
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getCliente() {
        if (cliente == null) {
            cliente = 0;
        }
        return cliente;
    }

    public void setCliente(Integer cliente) {
        this.cliente = cliente;
    }

    public Integer getEmpresa() {
        if (empresa == null) {
            empresa = 0;
        }
        return empresa;
    }

    public void setEmpresa(Integer empresa) {
        this.empresa = empresa;
    }

    public String getEmail() {
        if (email == null) {
            email = "";
        }
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getIpCliente() {
        if (ipCliente == null) {
            ipCliente = "";
        }
        return ipCliente;
    }

    public void setIpCliente(String ipCliente) {
        this.ipCliente = ipCliente;
    }

    public Boolean getBloqueadoBounce() {
        return bloqueadoBounce;
    }

    public void setBloqueadoBounce(Boolean bloqueadoBounce) {
        this.bloqueadoBounce = bloqueadoBounce;
    }

    public Integer getOrigemEnvio() {
        return origemEnvio;
    }

    public void setOrigemEnvio(Integer origemEnvio) {
        this.origemEnvio = origemEnvio;
    }

    public String getDataRegistro() {
        return dataRegistro;
    }

    public void setDataRegistro(String dataRegistro) {
        this.dataRegistro = dataRegistro;
    }

    public String getDataInscricao() {
        return dataInscricao;
    }

    public void setDataInscricao(String dataInscricao) {
        this.dataInscricao = dataInscricao;
    }

    public String getDataExclusao() {
        return dataExclusao;
    }

    public void setDataExclusao(String dataExclusao) {
        this.dataExclusao = dataExclusao;
    }

    public OptinVO toVo(){
        OptinVO optinVO = new OptinVO();
        ClienteVO clienteVO = new ClienteVO();
        EmpresaVO empresaVO = new EmpresaVO();

        clienteVO.setCodigo(this.cliente);
        empresaVO.setCodigo(this.empresa);

        optinVO.setCodigo(this.codigo);
        optinVO.setCliente(clienteVO);
        optinVO.setEmpresa(empresaVO);
        optinVO.setEmail(this.email);
        optinVO.setIpCliente(this.ipCliente);
        optinVO.setBloqueadoBounce(this.bloqueadoBounce);
        optinVO.setOrigemEnvio(this.origemEnvio == 0 ? OrigemEnvioEnum.WAGI : OrigemEnvioEnum.SMTP );
        optinVO.setDataRegistro( !this.dataRegistro.equals("") ? LocalDate.parse(this.dataRegistro) : null);
        optinVO.setDataInscricao( !this.dataInscricao.equals("") ? LocalDate.parse(this.dataInscricao) : null);
        optinVO.setDataExclusao( !this.dataExclusao.equals("") ? LocalDate.parse(this.dataExclusao) : null);

        return optinVO;
    }
}

package servlet.crm;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import com.google.common.net.HttpHeaders;
import controle.arquitetura.exceptions.ParametroObrigatorioException;
import negocio.comuns.arquitetura.BI.FiltroBiVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.crm.MetaCRMTO;
import negocio.comuns.crm.TipoMetaCRMTO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.BI.FiltroBi;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.facade.jdbc.crm.ConfiguracaoSistemaCRM;
import negocio.facade.jdbc.crm.FecharMeta;
import org.json.JSONArray;
import org.json.JSONObject;
import servlet.arquitetura.SuperServlet;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.sql.Connection;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

public class MetaDiariaServlet extends SuperServlet {

    @Override
    protected void processRequest(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {

        response.setHeader("Access-Control-Allow-Origin", "*");
        response.setHeader("Access-Control-Allow-Methods", "GET, OPTIONS");
        response.setHeader("Access-Control-Max-Age", "3600");
        response.setHeader("Access-Control-Allow-Headers", "Authorization, content-type, xsrf-token");
        response.addHeader("Access-Control-Expose-Headers", "xsrf-token");
        response.setContentType("application/json;charset=UTF-8");

        try {
            validarAuthorizationComChave(request);
            validarParametrosObrigatorios(request, new String[]{"empresa", "dataInicio", "dataFim"});
        } catch (Exception e) {
            processarErro(e, request, response, null);
            return;
        }

        String chave = request.getHeader(HttpHeaders.AUTHORIZATION);

        try (Connection connection = new DAO().obterConexaoEspecifica(chave)) {

            Integer empresa = Uteis.converterInteiro(request.getParameter("empresa"), 1);
            Date dataInicio = Calendario.converterDataPrimeiraHoraDia(request.getParameter("dataInicio"));
            Date dataFim = Calendario.converterDataUltimaHoraDia(request.getParameter("dataFim"));
            boolean reloadFull = Boolean.parseBoolean(request.getParameter("reloadFull"));
            String codColaboradores = request.getParameter("colaboradores");

            List<Integer> codColaboradoresList = new ArrayList<>();
            List<UsuarioVO> usuarioVOS = new ArrayList<>();
            Usuario usuario = new Usuario(connection);
            if (!UteisValidacao.emptyString(codColaboradores)) {
                String[] codColaboradoresArray = codColaboradores.split(",");
                for (String codColaborador : codColaboradoresArray) {
                    if (!codColaborador.isEmpty()) {
                        usuarioVOS.add(usuario.consultarPorCodigoColaborador(Integer.parseInt(codColaborador), Uteis.NIVELMONTARDADOS_MINIMOS));
                        codColaboradoresList.add(Integer.parseInt(codColaborador));
                    }
                }
            } else {
                ConfiguracaoSistemaCRM configuracaoSistemaCRMDAO = new ConfiguracaoSistemaCRM(connection);
                usuarioVOS = usuario.consultarListaUsuariosCRM(empresa, !configuracaoSistemaCRMDAO.consultarConfiguracaoSistemaCRM(Uteis.NIVELMONTARDADOS_DADOSBASICOS).isApresentarColaboradoresInativos(), false);
                configuracaoSistemaCRMDAO = null;
                for (UsuarioVO usuarioVO : usuarioVOS) {
                    codColaboradoresList.add(usuarioVO.getColaboradorVO().getCodigo());
                }
            }
            usuario = null;
            Set<Integer> codColaboradoresUnicos = new HashSet<>(codColaboradoresList);
            codColaboradoresList = new ArrayList<>(codColaboradoresUnicos);
            Collections.sort(codColaboradoresList);

            validarParametroDatas(dataInicio, dataFim);

            FiltroBi filtroBi = new FiltroBi(connection);
            FiltroMetaDiariaJSON filtroJSON = new FiltroMetaDiariaJSON(empresa, dataInicio.getTime(), dataFim.getTime(), codColaboradoresList);
            FiltroBiVO filtroBiVO = filtroBi.obterPorToken(filtroJSON.getToken(), "META_DIARIA");
            FecharMeta servico = new FecharMeta(connection);

            if (filtroBiVO == null || reloadFull) {
                if (filtroBiVO != null) {
                    filtroBi.excluir(filtroBiVO.getTokenFiltro());
                }

//                List<MetaCRMTO> metas = servico.consultarMeta(dataInicio, dataFim, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA, usuarioVOS, empresa, true);
                List<MetaCRMTO> metas = servico.consultarMetaToServlet(dataInicio, dataFim, usuarioVOS, empresa, true);
                filtroBiVO = filtroBi.incluir("META_DIARIA", filtroJSON, converterListaMetaJson(metas));
            }

            response.getWriter().append(filtroBiVO.toJSON().toString());

            servico = null;
        } catch (Exception e) {
            processarErro(e, request, response, null);
        }
    }

    private void validarParametroDatas(Date dataInicio, Date dataFim) throws ParametroObrigatorioException {
        int periodoConsulta = Calendario.diferencaEmDias(dataInicio, dataFim);
        if (periodoConsulta > 31) {
            throw new ParametroObrigatorioException("O período máximo permitido de consulta é de 31 dias. Sua consulta contém " + periodoConsulta + " dias.");
        }
    }

    private JSONObject converterListaMetaJson(List<MetaCRMTO> metas){
        JSONObject metaJSON = new JSONObject();
        JSONArray listaMetaJSON = new JSONArray();

        Double totalPrevisto = 0.0;
        Double totalRealizado = 0.0;

        for (MetaCRMTO meta: metas) {
            listaMetaJSON.put(converterMetaCrmJson(meta));
            totalPrevisto += meta.getTotalMeta();
            totalRealizado += meta.getTotalMetaRealizada();
        }

        metaJSON.put("metas", listaMetaJSON);
        metaJSON.put("totalPrevisto", totalPrevisto);
        metaJSON.put("totalRealizado", totalRealizado);
        metaJSON.put("percentualRealizado", Uteis.arrendondar(totalRealizado > 0.0 ? (totalRealizado / totalPrevisto) * 100 : 0.0));

        return metaJSON;
    }

    private JSONObject converterMetaCrmJson(MetaCRMTO metaCRMTO){
        JSONObject metaJson = new JSONObject();

        metaJson.put("totalMeta", metaCRMTO.getTotalMeta());
        metaJson.put("totalMetaRealizada", metaCRMTO.getTotalMetaRealizada());
        metaJson.put("descricaoTipoFaseCrm", metaCRMTO.getTipoFaseCRM().getDescricao());
        metaJson.put("tipoFaseCrmResumida", metaCRMTO.getTipoFaseCRM().getDescricaoCurta());
        metaJson.put("situacaoAtualMeta", metaCRMTO.getSituacaoAtualMetaEnum().getDescricao());
        metaJson.put("tiposMeta", converterListaTiposMetaJson(metaCRMTO.getListaTipoMetaVO()));

        return metaJson;
    }

    private JSONArray converterListaTiposMetaJson(List<TipoMetaCRMTO> listaTipoMetaVO) {
        JSONArray tiposMeta = new JSONArray();
        for (TipoMetaCRMTO tipoMetaCRMTO: listaTipoMetaVO) {
            tiposMeta.put(converterTipoMetaJson(tipoMetaCRMTO));
        }
        return tiposMeta;
    }

    private JSONObject converterTipoMetaJson(TipoMetaCRMTO tipoMetaCRMTO) {
        JSONObject tipoMeta = new JSONObject();
        tipoMeta.put("descricao", tipoMetaCRMTO.getFasesCRMEnum().getDescricao());
        tipoMeta.put("total", tipoMetaCRMTO.getTotalMeta());
        tipoMeta.put("totalRealizada", tipoMetaCRMTO.getTotalMetaRealizada());

        return tipoMeta;
    }
}

package servlet.crm;

import br.com.pactosolucoes.comuns.json.SuperJSON;
import com.sun.xml.fastinfoset.stax.events.Util;
import negocio.interfaces.bi.FiltroJSON;

import java.util.List;

public class FiltroMetaDiariaJSON extends SuperJSON implements FiltroJSON {

    private Integer empresa;
    private Long dataInicio;
    private Long dataFim;
    private List<Integer> colaboradores;

    public FiltroMetaDiariaJSON(Integer empresa, Long dataInicio, Long dataFim, List<Integer> colaboradores) {
        this.empresa = empresa;
        this.dataInicio = dataInicio;
        this.dataFim = dataFim;
        this.colaboradores = colaboradores;
    }

    public String getToken() {
        String tokenColaborador = (getColaboradores().size() > 0) ? "|colaboradores=" + getColaboradores() : "";
        return "empresa=" + getEmpresa() +
                "|dataInicio=" + getDataInicio() +
                "|dataFim=" + getDataFim() +
                tokenColaborador;
    }

    public Integer getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Integer empresa) {
        this.empresa = empresa;
    }

    public Long getDataInicio() {
        return dataInicio;
    }

    public void setDataInicio(Long dataInicio) {
        this.dataInicio = dataInicio;
    }

    public Long getDataFim() {
        return dataFim;
    }

    public void setDataFim(Long dataFim) {
        this.dataFim = dataFim;
    }

    public List<Integer> getColaboradores() {
        return colaboradores;
    }

    public void setColaboradores(List<Integer> colaboradores) {
        this.colaboradores = colaboradores;
    }
}

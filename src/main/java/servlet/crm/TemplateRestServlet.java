package servlet.crm;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import controle.crm.MsgBuildDTO;
import negocio.comuns.crm.MeioEnvio;
import negocio.facade.jdbc.basico.Empresa;
import negocio.facade.jdbc.crm.ModeloMensagem;
import org.json.JSONArray;
import servlet.arquitetura.SuperServlet;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

public class TemplateRestServlet extends SuperServlet {

    @Override
    protected void processRequest(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {

        response.addHeader("Access-Control-Allow-Origin", "*");
        response.addHeader("Access-Control-Allow-Methods", "GET");
        response.setContentType("application/json");
        String chave = request.getParameter("chave");
        String reinit = request.getParameter("reinit");

        try {
            ModeloMensagem dao = getDao(chave);
            if(reinit != null){
                dao.clearTemplatesPacto();
                response.getWriter().append("modelos reiniciados");
            } else {
                List<MsgBuildDTO> msgBuildDTOS = dao.consultarMensagensBuilder(MeioEnvio.EMAIL);
                JSONArray jsonArray = new JSONArray(msgBuildDTOS);
                response.getWriter().append(jsonArray.toString());
            }

        }catch (Exception e ){
            System.out.println("Erro na api rest templates de modelo de mensagem. Message: "+e.getMessage());
            processarErro(e, request, response, null);
        }

    }

    private ModeloMensagem getDao(String chave) throws Exception {
        return new ModeloMensagem(new DAO().obterConexaoEspecifica(chave));
    }
}

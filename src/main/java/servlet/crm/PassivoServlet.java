package servlet.crm;

import controle.arquitetura.security.LoginControle;
import servlet.arquitetura.SuperServlet;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.util.Date;

/*
 * Created by luiz on 26/08/2014.
 */
public class PassivoServlet extends SuperServlet {
    @Override
    protected void processRequest(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        super.processRequest(request, response);
        PrintWriter out = response.getWriter();
        String json;
        try {
            Integer empresa = ((LoginControle)getAttribute(request, "LoginControle")).getEmpresa().getCodigo();
            Date dtInicio = obterParametroDate(request.getParameter("dtInicio"));
            Date dtFim = obterParametroDate(request.getParameter("dtFim"));
            String sEcho = obterParametroString(request.getParameter("sEcho"));
            Integer offset = obterParametro(request.getParameter("iDisplayStart"));
            Integer limit = obterParametro(request.getParameter("iDisplayLength"));
            String clausulaLike = obterParametroString(request.getParameter("buscar"));
            Integer colOrdenar = obterParametro(request.getParameter("iSortCol_0"));
            String dirOrdenar = obterParametroString(request.getParameter("sSortDir_0"));
            boolean apresentarConvertidos = true;
            try {
                apresentarConvertidos = Boolean.parseBoolean(obterParametroString(request.getParameter("apresentarConvertidos")));
            } catch (Exception ignore) {
            }
            json = ff(request).getPassivo().consultarJSON(empresa,sEcho,offset,limit,clausulaLike,colOrdenar,dirOrdenar,dtInicio,dtFim, apresentarConvertidos);
        } catch (Exception e) {
            json = (e.getMessage() == null) ? NullPointerException.class.getName() : e.getMessage();
        }
        out.println(json);
        json = null;
    }
}
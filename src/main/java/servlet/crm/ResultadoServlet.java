package servlet.crm;

import br.com.pactosolucoes.enumeradores.BIEnum;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import com.google.common.net.HttpHeaders;
import controle.arquitetura.exceptions.ParametroObrigatorioException;
import controle.crm.BusinessIntelligenceCRMControle;
import negocio.comuns.arquitetura.BI.FiltroBiVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.crm.FecharMetaVO;
import negocio.comuns.crm.GrupoColaboradorParticipanteVO;
import negocio.comuns.crm.GrupoColaboradorVO;
import negocio.comuns.crm.MetaCRMTO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.BI.FiltroBi;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.facade.jdbc.basico.Empresa;
import negocio.facade.jdbc.utilitarias.Conexao;
import org.json.JSONArray;
import org.json.JSONObject;
import servlet.arquitetura.SuperServlet;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.sql.Connection;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class ResultadoServlet extends SuperServlet {

    @Override
    protected void processRequest(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {

        response.setHeader("Access-Control-Allow-Origin", "*");
        response.setHeader("Access-Control-Allow-Methods", "GET, OPTIONS");
        response.setHeader("Access-Control-Max-Age", "3600");
        response.setHeader("Access-Control-Allow-Headers", "Authorization, content-type, xsrf-token");
        response.addHeader("Access-Control-Expose-Headers", "xsrf-token");
        response.setContentType("application/json");

        try{
            validarAuthorizationComChave(request);
            validarParametrosObrigatorios(request, new String[]{"empresa", "dataInicio", "dataFim", "usuario"});

            String chave = request.getHeader(HttpHeaders.AUTHORIZATION);
            Connection connection = new DAO().obterConexaoEspecifica(chave);
            Conexao.guardarConexaoForJ2SE(chave, connection);

            // TODO: fazer autentificação com token gerado pelo micro serviço de autentificação e obter empresa e usuário
            Integer empresa = Uteis.converterInteiro(request.getParameter("empresa"), 1);
            Integer usuario = Uteis.converterInteiro(request.getParameter("usuario"), 1);
            Date dataInicio = Calendario.converterDataPrimeiraHoraDia(request.getParameter("dataInicio"));
            Date dataFim = Calendario.converterDataUltimaHoraDia(request.getParameter("dataFim"));
            boolean reloadFull = Boolean.parseBoolean(request.getParameter("reloadFull"));
            // TODO: provisório. para manter a retrocompatibilidade com app. Após a versão do app ser atualizado em todos os clientes, esse parametro pode ser removido.
            String returnType = request.getParameter("returnType");
            returnType = returnType == null ? "data" : returnType;
            validarParametroDatas(dataInicio, dataFim);

            FiltroBi filtroBi = new FiltroBi(connection);
            FiltroResultadoCrmJSON filtroJSON = new FiltroResultadoCrmJSON(empresa, dataInicio.getTime(), dataFim.getTime(), usuario);
            FiltroBiVO filtroBiVO = filtroBi.obterPorToken(filtroJSON.getToken(), "RESULTADO_CRM");

            if(filtroBiVO == null || reloadFull){
                if(filtroBiVO != null){
                    filtroBi.excluir(filtroBiVO.getTokenFiltro());
                }

                BusinessIntelligenceCRMControle controlador = new BusinessIntelligenceCRMControle();
                Usuario usuarioService = new Usuario();
                Empresa empresaService = new Empresa();
                UsuarioVO usuarioVO = usuarioService.consultarPorCodigo(usuario, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                EmpresaVO empresaVO = empresaService.consultarPorCodigo(empresa, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                controlador.montarListaGrupoColaborador(usuarioVO, empresaVO);
                List<GrupoColaboradorVO> grupoColaboradorVOS = controlador.getListaGrupoColaborador();
                List<UsuarioVO> usuariosColaboradores = new ArrayList<>();

                for (GrupoColaboradorVO grupoColaboradorVO :grupoColaboradorVOS) {
                    for (GrupoColaboradorParticipanteVO grupoColaboradorParticipanteVO: grupoColaboradorVO.getGrupoColaboradorParticipanteVOs()) {
                        usuariosColaboradores.add(usuarioService.consultarPorCodigoPessoa(grupoColaboradorParticipanteVO.getColaboradorParticipante().getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_METACOLABORADORRESPONSAVEL));
                    }
                }

                controlador.totalizarBIsVendasFidelizacaoStudioCRMExtra(dataInicio, dataFim, empresa, usuariosColaboradores);
                JSONObject resultadoCrm = converterResultadoJson(controlador.getTotaisResultado(), controlador.getFases());

                filtroBiVO = filtroBi.incluir("RESULTADO_CRM", filtroJSON, resultadoCrm);

                controlador = null;
                usuarioService = null;
                empresaService = null;
            }

            if(returnType.equals("cache")){
                response.getWriter().append(filtroBiVO.toJSON().toString());
            }else{
                response.getWriter().append(filtroBiVO.getJsonDados());
            }

        } catch (Exception e) {
            processarErro(e, request, response, null);
        }
    }

    private void validarParametroDatas(Date dataInicio, Date dataFim) throws ParametroObrigatorioException {
        Integer periodoConsulta = Calendario.diferencaEmDias(dataInicio,dataFim);
        if(periodoConsulta > 31 ){
            throw new ParametroObrigatorioException("O período máximo permitido de consulta é de 31 dias. Sua consulta contém "+periodoConsulta+" dias.");
        }
    }

    private JSONObject converterResultadoJson(List<FecharMetaVO> metas, List<FecharMetaVO> fases){
        JSONObject metaJSON = new JSONObject();
        JSONArray listaMetaJSON = new JSONArray();

        Double totalPrevisto = 0.0;
        Double totalRealizado = 0.0;

        for (FecharMetaVO meta: metas) {
            listaMetaJSON.put(converterMetaCrmJson(meta));
            totalPrevisto += meta.getMeta();
            totalRealizado += meta.getMetaAtingida();
        }

        metaJSON.put("metas", listaMetaJSON);
        metaJSON.put("totalPrevistas", totalPrevisto);
        metaJSON.put("totalRealizadas", totalRealizado);
        metaJSON.put("percentualRealizadas", Uteis.arrendondar(totalRealizado > 0.0 ? (totalRealizado / totalPrevisto) * 100 : 0.0));

        return metaJSON;
    }

    private JSONObject converterMetaCrmJson(FecharMetaVO fecharMetaVO){
        JSONObject metaJson = new JSONObject();

        metaJson.put("prevista", fecharMetaVO.getMeta());
        metaJson.put("realizada", fecharMetaVO.getMetaAtingida());
        metaJson.put("percentualRealizada",  Uteis.arrendondar(fecharMetaVO.getMetaAtingida() > 0.0 ? (fecharMetaVO.getMetaAtingida() / fecharMetaVO.getMeta()) * 100 : 0.0));
        metaJson.put("fase", fecharMetaVO.getFase().getDescricao());

        return metaJson;
    }
}

package servlet.crm;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.basico.ConfiguracaoEmpresaHubspotVO;
import negocio.facade.jdbc.crm.ConfiguracaoEmpresaHubspot;
import org.json.JSONObject;
import servicos.integracao.impl.hubspot.IntegracaoHubspostLeadServiceImpl;
import servlet.arquitetura.SuperServlet;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.sql.Connection;


public class ConfigHubspoRestServlet extends SuperServlet {

    protected void processRequest(HttpServletRequest request, HttpServletResponse response)   {

        response.addHeader("Access-Control-Allow-Origin", "*");
        response.addHeader("Access-Control-Allow-Methods", "POST");
        response.setContentType("application/json");
        String chave = request.getParameter("chave");
        String acao = request.getParameter("acao");
        String empresa = request.getParameter("empresa");

        try (Connection con = new DAO().obterConexaoEspecifica(chave)) {

            IntegracaoHubspostLeadServiceImpl integracaoHubspostLeadService = new IntegracaoHubspostLeadServiceImpl(con);

            JSONObject model = new JSONObject();
            String bodyString = "";
            try {
                InputStream inputStream = request.getInputStream();
                BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream, "UTF-8"));
                StringBuffer body = new StringBuffer();
                String line;
                while ((line = reader.readLine()) != null) {
                    body.append(line);
                }
                bodyString = body.toString();
                model = new JSONObject(body.toString());
            } catch (Exception ex) {
                ex.printStackTrace();
            }

            JSONObject json = new JSONObject();
            switch (acao){
                case "obter_config":
                    ConfiguracaoEmpresaHubspot configDAO = new ConfiguracaoEmpresaHubspot(con);
                    ConfiguracaoEmpresaHubspotVO configVO = configDAO.consultarPorEmpresa(Integer.parseInt(empresa));
                    json.put("message", configVO.toString());
                    break;
                case "config":
                    ConfiguracaoEmpresaHubspot configuracaoEmpresaHubspotDAO = new ConfiguracaoEmpresaHubspot(con);
                    ConfiguracaoEmpresaHubspotVO config = toConfigVo(model);
                    if (config.getCodigo() > 0) {
                        configuracaoEmpresaHubspotDAO.alterar(config);
                    }else {
                        configuracaoEmpresaHubspotDAO.incluir(config);
                    }
                    break;
                case "inserircode":
                    json.put("message", integracaoHubspostLeadService.inserirCodeHubspot(model.getString("codigo"), model.getInt("empresa") , chave));
                    break;
                case "atualizacode":
                   json.put("message", integracaoHubspostLeadService.atualizarCodeHubspot(model.getString("codigo"), model.getInt("empresa") , chave));
                    break;
                case "validaCodigo":
                  json.put("message", integracaoHubspostLeadService.temCodigoValido( model.getInt("empresa")));
                    break;
                case "exiteToken":
                    json.put("message", integracaoHubspostLeadService.temTokenAuth2Valido( model.getInt("empresa")));
                    break;
                case "existeCodigo":
                    json.put("message", integracaoHubspostLeadService.existeCodigoInserido( model.getInt("empresa")));
                    break;
                case "obterAcess":
                    json.put("message", integracaoHubspostLeadService.obterAcessToken( model.getInt("empresa")));
                    break;
                case "buscaClientid":
                    json.put("message", integracaoHubspostLeadService.buscaClintIDSecret( model.getInt("empresa")));
                    break;
                case "atualizaTokenHubspot":
                    json.put("message", integracaoHubspostLeadService.atulizarTokenHubspot( model.getString("access_token"), model.getString("refresh_token"), model.getInt("empresa"), getChave()));
                    break;
                case "obterRefresh":
                    json.put("message", integracaoHubspostLeadService.obterRefreshToken( model.getInt("empresa")));
                    break;
                case "processarLead":
                    integracaoHubspostLeadService.processarNovaLead(model.getString("lead"), model.getInt("empresa"));
                    json.put("message","SUCESSO");
                    break;
                case "webhook":
                    integracaoHubspostLeadService.processarWebhook(Integer.parseInt(empresa), bodyString);
                    json.put("message","SUCESSO");
                    break;
            }
            response.getOutputStream().print(json.toString());
        } catch (Exception ex){
            ex.printStackTrace();
        }
    }

    private  ConfiguracaoEmpresaHubspotVO toConfigVo(JSONObject model){

        ConfiguracaoEmpresaHubspotVO configvo = new ConfiguracaoEmpresaHubspotVO();
        configvo.setClientId(model.optString("clientId"));;
        configvo.setAppId(model.optString("appId"));;
        configvo.setUrl_instalacao(model.optString("url_instalacao"));
        configvo.setEmpresa(Integer.parseInt(model.optString("empresa")));
        configvo.setClientsecret(model.optString("clientsecret"));
        configvo.setEmpresausahub(model.optBoolean("empresausahub"));
        configvo.setHoraexpiracao(model.optString("horaexpiracao"));
        configvo.setUrl_redirect(model.getString("url_redirect"));
        configvo.setCodigo(model.getInt("codigo"));

        return  configvo;

    }
}

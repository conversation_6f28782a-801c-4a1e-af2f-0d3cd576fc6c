package servlet.appGestor;

import acesso.webservice.DaoAuxiliar;
import br.com.pactosolucoes.comuns.json.SimplesJSON;
import br.com.pactosolucoes.controle.json.SuperServletControle;
import negocio.facade.jdbc.financeiro.DadosGerencialPmg;
import negocio.facade.jdbc.utilitarias.Conexao;
import negocio.interfaces.basico.DadosGameInterfaceFacade;
import org.json.JSONArray;
import org.json.JSONObject;
import servlet.appGestor.appGestorDados.OperacoesFinanceiroGestor;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.sql.Connection;
import java.util.Date;
import java.util.List;

public class MSVendasServlet extends SuperServletControle {

    @Override
    protected void processRequest(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        super.processRequest(request, response);
        PrintWriter out = response.getWriter();
        JSONObject jsonRetorno = new JSONObject();

        try {
            try {
                String key = obterParametroString(request, "key");
                Date dataConsultar = obterParametroSimpleDate(request, "dataConsultar");
                Integer empresa = obterParametroInt(request, "empresaId");
                String operacao = obterParametroString(request, "operacao");

                OperacoesFinanceiroGestor operacaoEnum = OperacoesFinanceiroGestor.obterOperacao(operacao);
                if (operacaoEnum == null) {
                    throw new Exception("Não foi possível identificar qual operação deve ser realizada.");
                }
                Connection c = null;
                switch (operacaoEnum) {
                    case vendas:
                        Connection con = DaoAuxiliar.retornarAcessoControle(key).getCon();
                        c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con);
                        DadosGerencialPmg dao = new DadosGerencialPmg(c);
                        String vendas = dao.obterValorVendasDia(empresa, key, dataConsultar, null);
                        jsonRetorno.put("resultado", vendas);
                        break;
                }
            } catch (Exception e) {

                jsonRetorno.put(STATUS_ERRO, e.getMessage());
            }
            out.println(jsonRetorno);
        } catch (
                Exception e) {
            out.println(e.getMessage());
        }
    }
}


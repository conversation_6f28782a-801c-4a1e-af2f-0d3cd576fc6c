package servlet.appGestor.appGestorDados;

public enum OperacoesAlunoGestor {
    ESTORNO_CONTRATOS_USUARIO,
    ESTORNOS_CONTRATOS_RECORRENCIA,
    ESTORNOS_CONTRATOS_ADM,
    CONTRATOS_CANCELADOS,
    CLIENTES_BONUS,
    FREEPASS,
    EXCLUSAO_VISITANTES,
    INATIVOS_PERIODO_ACESSO,
    OPERACOES_RETROATIVAS,
    CONSULTORES_CONTRATOS_ALTERADOS,
    PARCELAS_CANCELADAS,
    ESTORNOS_RECIBO,
    CONTRATOS_DATABASE_ALTERADA,
    PAGAMENTOS_DATABASE_ALTERADA,
    EDICOES_PAGAMENTO,
    GYMPASS,
    ALUNOS_BOLSA,
    CLIENTES_COM_AUTORIZACAO_SEM_RENOVACAO_AUTOMATICA,
    CLIENTES_COM_AUTORIZACAO_COM_RENOVACAO_AUTOMATICA,
    VALOR_DESCONTOS,
    ALUNOS_EXCLUIDOS_TREINOWEB,
    ALUNOS_EXCLUIDOS_BASE_DADOS,
    RENEGOCIACAO_PARCELAS,
    TRANSFERIDOS_COM_CONTRATOS_CANCELADOS;
    
    public static OperacoesAlunoGestor obterOperacao(String o) {
        if (o == null) {
            return null;
        }
        for (OperacoesAlunoGestor op : values()) {
            if (op.name().toLowerCase().equals(o.toLowerCase())) {
                return op;
            }
        }
        return null;
    }

}


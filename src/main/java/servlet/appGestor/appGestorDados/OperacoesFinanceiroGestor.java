package servlet.appGestor.appGestorDados;

public enum OperacoesFinanceiroGestor {
    saldoContas,
    lancamentoContas,
    obterDetalhamento,
    obterRecebiveis,
    saldoGeral,
    lancamentosFuturos,
    vendas,
    crm;

    public static OperacoesFinanceiroGestor obterOperacao(String o) {
        if (o == null) {
            return null;
        }
        for (OperacoesFinanceiroGestor op : values()) {
            if (op.name().toLowerCase().equals(o.toLowerCase())) {
                return op;
            }
        }
        return null;
    }

}


package servlet.appGestor.appGestorDados;

import acesso.webservice.retorno.RetornoRequisicaoWS;

import java.util.ArrayList;
import java.util.List;

public class TipoContaListToJSON extends RetornoRequisicaoWS {

    private List<TipoContaJSON> contasList;

    public List<TipoContaJSON> getContasList() {
        if (contasList == null) {
            contasList = new ArrayList<TipoContaJSON>();
        }
        return contasList;
    }

    public void setContasList(List<TipoContaJSON> contasList) {
        this.contasList = contasList;
    }
}

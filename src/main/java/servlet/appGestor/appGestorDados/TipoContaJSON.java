package servlet.appGestor.appGestorDados;

import java.util.ArrayList;
import java.util.List;

public class TipoContaJSON {

    private String tipoConta;
    private List<ContaJSON> contas;

    public String getTipoConta() {
        return tipoConta;
    }

    public void setTipoConta(String tipoConta) {
        this.tipoConta = tipoConta;
    }

    public List<ContaJSON> getContas() {
        if (contas == null) {
            contas = new ArrayList<ContaJSON>();
        }
        return contas;
    }

    public void setContas(List<ContaJSON> contas) {
        this.contas = contas;
    }
}

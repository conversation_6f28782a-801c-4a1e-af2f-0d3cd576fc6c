package servlet.appGestor.appGestorDados;

import acesso.webservice.retorno.RetornoRequisicaoWS;

import java.util.ArrayList;
import java.util.List;

public class VendaDescontoListToJSON extends RetornoRequisicaoWS {

    private List<VendaDescontoJSON> vendaDescontoJSONS;

    public List<VendaDescontoJSON> getAlunoList() {
        if (vendaDescontoJSONS == null) {
            vendaDescontoJSONS = new ArrayList<VendaDescontoJSON>();
        }
        return vendaDescontoJSONS;
    }

    public void setAlunoList(List<VendaDescontoJSON> vendaDescontoJSONS) {
        this.vendaDescontoJSONS = vendaDescontoJSONS;
    }
}

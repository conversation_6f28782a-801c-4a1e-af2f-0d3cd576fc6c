package servlet.appGestor.appGestorDados;

import acesso.webservice.retorno.RetornoRequisicaoWS;

import java.util.ArrayList;
import java.util.List;

public class AlunoListToJSON extends RetornoRequisicaoWS {

    private List<AlunoSimplesJSON> alunoSimplesJSONS;

    public List<AlunoSimplesJSON> getAlunoList() {
        if (alunoSimplesJSONS == null) {
            alunoSimplesJSONS = new ArrayList<AlunoSimplesJSON>();
        }
        return alunoSimplesJSONS;
    }

    public void setAlunoList(List<AlunoSimplesJSON> alunoSimplesJSONS) {
        this.alunoSimplesJSONS = alunoSimplesJSONS;
    }
}

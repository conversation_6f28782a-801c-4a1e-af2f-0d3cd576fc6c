package servlet.appGestor.appGestorDados;

import acesso.webservice.retorno.RetornoRequisicaoWS;

import java.util.ArrayList;
import java.util.List;

public class LancamentoToJSON extends RetornoRequisicaoWS {

    private List<LancamentoJSON> lancamentoJSONS;

    public List<LancamentoJSON> getLancamentoJSONS() {
        if (lancamentoJSONS == null) {
            lancamentoJSONS = new ArrayList<>();
        }
        return lancamentoJSONS;
    }

    public void setLancamentoJSONS(List<LancamentoJSON> lancamentoJSONS) {
        this.lancamentoJSONS = lancamentoJSONS;
    }
}

package servlet.appGestor.appGestorDados;

import acesso.webservice.retorno.RetornoRequisicaoWS;

import java.util.Date;

public class DadosEmpresa extends RetornoRequisicaoWS {

    private Integer codigo;
    private String chave;
    private String nomeEmpresa;
    private Integer empresa;
    private String indicador;
    private String identificador;
    private String periodicidade;
    private Date geracao;
    private Integer valor;
    private Integer PREVISAO_RENOVACAO = 0;
    private Integer INDICE_RENOVACAO = 0;
    private Integer RENOVADOS = 0;
    private Integer RESULTADO_CRM_CONVERSAO_AGENDADOS = 0;
    private Integer NUMERO_CONTATOS_CRM = 0;
    private Integer META_CONTATOS_CRM = 0;


    public Integer getPREVISAO_RENOVACAO() {
        return PREVISAO_RENOVACAO;
    }

    public void setPREVISAO_RENOVACAO(Integer PREVISAO_RENOVACAO) {
        this.PREVISAO_RENOVACAO = PREVISAO_RENOVACAO;
    }

    public Integer getINDICE_RENOVACAO() {
        return INDICE_RENOVACAO;
    }

    public void setINDICE_RENOVACAO(Integer INDICE_RENOVACAO) {
        this.INDICE_RENOVACAO = INDICE_RENOVACAO;
    }

    public Integer getRENOVADOS() {
        return RENOVADOS;
    }

    public void setRENOVADOS(Integer RENOVADOS) {
        this.RENOVADOS = RENOVADOS;
    }

    public Integer getRESULTADO_CRM_CONVERSAO_AGENDADOS() {
        return RESULTADO_CRM_CONVERSAO_AGENDADOS;
    }

    public void setRESULTADO_CRM_CONVERSAO_AGENDADOS(Integer RESULTADO_CRM_CONVERSAO_AGENDADOS) {
        this.RESULTADO_CRM_CONVERSAO_AGENDADOS = RESULTADO_CRM_CONVERSAO_AGENDADOS;
    }

    public Integer getNUMERO_CONTATOS_CRM() {
        return NUMERO_CONTATOS_CRM;
    }

    public void setNUMERO_CONTATOS_CRM(Integer NUMERO_CONTATOS_CRM) {
        this.NUMERO_CONTATOS_CRM = NUMERO_CONTATOS_CRM;
    }

    public Integer getMETA_CONTATOS_CRM() {
        return META_CONTATOS_CRM;
    }

    public void setMETA_CONTATOS_CRM(Integer META_CONTATOS_CRM) {
        this.META_CONTATOS_CRM = META_CONTATOS_CRM;
    }

    public Integer getValor() {
        return valor;
    }

    public void setValor(Integer valor) {
        this.valor = valor;
    }


    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getChave() {
        return chave;
    }

    public void setChave(String chave) {
        this.chave = chave;
    }

    public String getNomeEmpresa() {
        return nomeEmpresa;
    }

    public void setNomeEmpresa(String nomeEmpresa) {
        this.nomeEmpresa = nomeEmpresa;
    }

    public Integer getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Integer empresa) {
        this.empresa = empresa;
    }

    public String getIndicador() {
        return indicador;
    }

    public void setIndicador(String indicador) {
        this.indicador = indicador;
    }

    public String getIdentificador() {
        return identificador;
    }

    public void setIdentificador(String identificador) {
        this.identificador = identificador;
    }

    public String getPeriodicidade() {
        return periodicidade;
    }

    public void setPeriodicidade(String periodicidade) {
        this.periodicidade = periodicidade;
    }

    public Date getGeracao() {
        return geracao;
    }

    public void setGeracao(Date geracao) {
        this.geracao = geracao;
    }

}

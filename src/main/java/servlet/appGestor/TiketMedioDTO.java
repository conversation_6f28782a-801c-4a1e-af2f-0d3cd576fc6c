package servlet.appGestor;

public class TiketMedioDTO {

    private TiketMedioCaixaDTO tiketMedioCaixa;
    private TiketMedioReceitaDTO tiketMedioReceita;
    private TiketMedioDespesaDTO tiketMedioDespesa;

    public TiketMedioCaixaDTO getTiketMedioCaixa() {
        return tiketMedioCaixa;
    }

    public void setTiketMedioCaixa(TiketMedioCaixaDTO tiketMedioCaixa) {
        this.tiketMedioCaixa = tiketMedioCaixa;
    }

    public TiketMedioReceitaDTO getTiketMedioReceita() {
        return tiketMedioReceita;
    }

    public void setTiketMedioReceita(TiketMedioReceitaDTO tiketMedioReceita) {
        this.tiketMedioReceita = tiketMedioReceita;
    }

    public TiketMedioDespesaDTO getTiketMedioDespesa() {
        return tiketMedioDespesa;
    }

    public void setTiketMedioDespesa(TiketMedioDespesaDTO tiketMedioDespesa) {
        this.tiketMedioDespesa = tiketMedioDespesa;
    }
}

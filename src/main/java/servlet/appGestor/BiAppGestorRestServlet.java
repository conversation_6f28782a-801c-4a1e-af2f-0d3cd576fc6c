package servlet.appGestor;

import acesso.webservice.DaoAuxiliar;
import br.com.pactosolucoes.enumeradores.BIEnum;
import br.com.pactosolucoes.enumeradores.ConfiguracaoBIEnum;
import br.com.pactosolucoes.enumeradores.StatusMovimentacaoEnum;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import controle.arquitetura.security.LoginControle;
import controle.crm.MsgBuildDTO;
import controle.financeiro.BIFinanceiroControle;
import controle.financeiro.GestaoRecebiveisControle;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ConfiguracaoBIVO;
import negocio.comuns.crm.GenericoTO;
import negocio.comuns.crm.MeioEnvio;
import negocio.comuns.financeiro.ChequeVO;
import negocio.comuns.financeiro.FormaPagamentoVO;
import negocio.comuns.financeiro.MovPagamentoVO;
import negocio.comuns.financeiro.enumerador.TipoFormaPagto;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.basico.ConfiguracaoBI;
import negocio.facade.jdbc.crm.ModeloMensagem;
import negocio.facade.jdbc.financeiro.FormaPagamento;
import negocio.facade.jdbc.financeiro.MovPagamento;
import negocio.interfaces.basico.ConfiguracaoBIInterfaceFacade;
import org.json.JSONArray;
import org.json.JSONObject;
import relatorio.controle.sad.BITicketMedioControle;
import relatorio.negocio.comuns.basico.TicketMedioVO;
import relatorio.negocio.comuns.financeiro.CompetenciaSinteticoProdutoVO;
import relatorio.negocio.jdbc.financeiro.TicketMedio;
import servicos.DemonstrativoFinanceiroSintetico;
import servicos.impl.recebiveis.GestaoRecebiveisServiceImpl;
import servicos.operacoes.RelatorioCompetenciaService;
import servlet.arquitetura.SuperServlet;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.sql.Connection;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

public class BiAppGestorRestServlet extends SuperServlet {

    @Override
    protected void processRequest(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {

        response.addHeader("Access-Control-Allow-Origin", "*");
        response.addHeader("Access-Control-Allow-Methods", "GET");
        response.setContentType("application/json");
        String chave = request.getParameter("chave");
        Integer empresa = Integer.valueOf(request.getParameter("empresa"));
        int mes = Integer.parseInt(request.getParameter("mes"));
        int ano = Integer.parseInt(request.getParameter("ano"));
        Date hoje = Calendario.hoje();
        hoje.setMonth(pegarMes(mes));
        Calendario.setAnoData(hoje,ano);

        try {
            List<ConfiguracaoBIVO> cfgs;
            TiketMedioDTO tiketMedioDTO;
            TiketMedioCaixaDTO tiketMedioCaixaDTO;
            TiketMedioReceitaDTO tiketMedioReceitaDTO;
            TiketMedioDespesaDTO tiketMedioDespesaDTO;
            TicketMedio ticketDao;
            Date inicio;
            Date fim;
            FormaPagamento formaPagamento;
            MovPagamento movPagamento;
            GestaoRecebiveisServiceImpl gestaoRecebiveisService;
            TicketMedioVO ticketMedioVO;
            List<FormaPagamentoVO> lista;
            try (Connection getConnection = getCon(chave)) {
                ConfiguracaoBI configuracaoBI = new ConfiguracaoBI(getConnection);
                cfgs = configuracaoBI.consultarPorBI(BIEnum.TICKET_MEDIO, empresa);
                //-------------------------------
                cfgs.removeIf(c -> c.getConfiguracao().equals(ConfiguracaoBIEnum.INCLUIR_PRODUTOS_RECEITA));

                tiketMedioDTO = new TiketMedioDTO();
                tiketMedioCaixaDTO = new TiketMedioCaixaDTO();
                tiketMedioReceitaDTO = new TiketMedioReceitaDTO();
                tiketMedioDespesaDTO = new TiketMedioDespesaDTO();
                ticketDao = new TicketMedio(getConnection);
                inicio = Calendario.getDataComHoraZerada(Uteis.obterPrimeiroDiaMes(hoje));
                fim = Uteis.obterUltimoDiaMesUltimaHora(hoje);
                formaPagamento = new FormaPagamento(getConnection);
                movPagamento = new MovPagamento(getConnection);
                gestaoRecebiveisService = new GestaoRecebiveisServiceImpl(getConnection);
                lista = consultarRecebiveis(formaPagamento,movPagamento,gestaoRecebiveisService,empresa,inicio,fim);
                ticketMedioVO = ticketDao.montarTicketMedio(true,cfgs,empresa,hoje);
            }
            JSONObject jsonObject = new JSONObject();
            if (ticketMedioVO != null){
                tiketMedioCaixaDTO.setValor(ticketMedioVO.getCaixaCompetencia());
                tiketMedioCaixaDTO.setValorMedio(ticketMedioVO.getTicketCompetencia());
                tiketMedioReceitaDTO.setValor(ticketMedioVO.getCaixaReceita());
                tiketMedioReceitaDTO.setValorMedio(ticketMedioVO.getTicketReceita());
                tiketMedioDespesaDTO.setValor(ticketMedioVO.getDespesaTotalMes());
                tiketMedioDespesaDTO.setValorMedio(ticketMedioVO.getDespesaPorAluno());
                tiketMedioDTO.setTiketMedioCaixa(tiketMedioCaixaDTO);
                tiketMedioDTO.setTiketMedioDespesa(tiketMedioDespesaDTO);
                tiketMedioDTO.setTiketMedioReceita(tiketMedioReceitaDTO);
                RetornoBIAppGestorDTO retorno = new RetornoBIAppGestorDTO();
                RetornoRecebiveisDTO retornoRecebiveisDTO = new RetornoRecebiveisDTO();
                popularRetonoRecebiveis(lista,retornoRecebiveisDTO);
                retorno.setRecebiveis(retornoRecebiveisDTO);
                retorno.setTiketMedio(tiketMedioDTO);
                jsonObject = new JSONObject(retorno);
            } else {
                jsonObject.append("retorno","Nada encontrado");
            }
            response.getWriter().append(jsonObject.toString());
        }catch (Exception e ){
            System.out.println("Erro na api rest templates de modelo de mensagem. Message: "+e.getMessage());
            processarErro(e, request, response, null);
        }

    }

    private void popularRetonoRecebiveis(List<FormaPagamentoVO> lista, RetornoRecebiveisDTO retornoRecebiveisDTO) {
        if (lista.size() > 0){
            List<FormaPagamentoDTO> formaPagamentoList = new ArrayList<>();
            double total = 0;
            double totalEspecie = 0;
            double totalBoleto = 0;
            for (FormaPagamentoVO item : lista){
                FormaPagamentoDTO formaPagamentoDTO = new FormaPagamentoDTO();
                if (item.getValor() > 0){
                    formaPagamentoDTO.setNomeForma(item.getDescricao());
                    formaPagamentoDTO.setValor(item.getValor());
                    if (item.getTipoFormaPagamentoEnum().equals(TipoFormaPagto.BOLETOBANCARIO)){
                        total += item.getValor();
                        totalBoleto += item.getValor();
                    } else {
                        total += item.getValor();
                        totalEspecie += item.getValor();
                    }
                    formaPagamentoList.add(formaPagamentoDTO);
                }
            }
            retornoRecebiveisDTO.setTotal(total);
            retornoRecebiveisDTO.setTotalEspecie(totalEspecie);
            retornoRecebiveisDTO.setTotalBoleto(totalBoleto);
            retornoRecebiveisDTO.setFormaPagamento(formaPagamentoList);
        }
    }

    private List<FormaPagamentoVO> consultarRecebiveis(FormaPagamento formaPagamento,
                                                       MovPagamento movPagamento,
                                                       GestaoRecebiveisServiceImpl gestaoRecebiveisService,
                                                       int empresa,
                                                       Date inicio,
                                                       Date fim) throws Exception {

        List<FormaPagamentoVO> listaPreparada = formaPagamento.consultarPorDescricaoTipoFormaPagamento(
                "",
                false,
                false,
                false,
                false,
                Uteis.NIVELMONTARDADOS_MINIMOS
        );
        List<MovPagamentoVO> listaConsultaRecebiveis = movPagamento.consultarComFiltros(
                empresa,
                "",
                "",
                inicio,
                fim,
                "00:00",
                "23:59",
                null,
                null,
                "",
                "",
                "",
                "",
                "",
                "",
                new ChequeVO(),
                null,
                new UsuarioVO(),
                "",
                0,
                "",
                false,
                false,
                false,
                new ArrayList<>(),
                false,
                Uteis.NIVELMONTARDADOS_CAIXAPARAOPERADOR,
                0,
                false,
                StatusMovimentacaoEnum.TODOS,
                0, null);

        return gestaoRecebiveisService.montarTotais(
                listaPreparada,
                listaConsultaRecebiveis,
                null,
                false,
                false,
                null,
                null,
                false,
                StatusMovimentacaoEnum.TODOS,
                new ChequeVO(),
                "",
                false,
                null,
                null,
                "",
                "");

    }

    private int pegarMes(int mes) {
        switch (mes){
            case 1:
                return Calendar.JANUARY;
            case 2:
                return Calendar.FEBRUARY;
            case 3:
                return Calendar.MARCH;
            case 4:
                return Calendar.APRIL;
            case 5:
                return Calendar.MAY;
            case 6:
                return Calendar.JUNE;
            case 7:
                return Calendar.JULY;
            case 8:
                return Calendar.AUGUST;
            case 9:
                return Calendar.SEPTEMBER;
            case 10:
                return Calendar.OCTOBER;
            case 11:
                return Calendar.NOVEMBER;
            case 12:
                return Calendar.DECEMBER;
            default:
                throw new IllegalStateException("Unexpected value: " + mes);
        }
    }

    private Connection getCon(String chave) throws Exception {
        return new DAO().obterConexaoEspecifica(chave);
    }

}

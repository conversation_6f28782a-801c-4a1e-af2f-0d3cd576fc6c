package servlet.appGestor;

import java.util.List;

public class RetornoRecebiveisDTO {

    private List<FormaPagamentoDTO> formaPagamento;
    private Double totalEspecie;
    private Double totalBoleto;
    private Double total;

    public List<FormaPagamentoDTO> getFormaPagamento() {
        return formaPagamento;
    }

    public void setFormaPagamento(List<FormaPagamentoDTO> formaPagamento) {
        this.formaPagamento = formaPagamento;
    }

    public Double getTotalEspecie() {
        return totalEspecie;
    }

    public void setTotalEspecie(Double totalEspecie) {
        this.totalEspecie = totalEspecie;
    }

    public Double getTotalBoleto() {
        return totalBoleto;
    }

    public void setTotalBoleto(Double totalBoleto) {
        this.totalBoleto = totalBoleto;
    }

    public Double getTotal() {
        return total;
    }

    public void setTotal(Double total) {
        this.total = total;
    }
}

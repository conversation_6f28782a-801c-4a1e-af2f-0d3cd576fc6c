package servlet.appGestor;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.Usuario;
import org.json.JSONObject;
import servlet.arquitetura.SuperServlet;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.sql.Connection;

public class LoginGestorRestServlet extends SuperServlet {

    @Override
    protected void processRequest(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {

        response.addHeader("Access-Control-Allow-Origin", "*");
        response.addHeader("Access-Control-Allow-Methods", "GET");
        response.setContentType("application/json");
        String chave = request.getParameter("chave");
        int codUsuario = Integer.parseInt(request.getParameter("codUsuario"));

        try {
            boolean ativo = true;
            try (Connection getConnection = getCon(chave)) {
                Usuario usuario = new Usuario(getConnection);
                UsuarioVO usu = usuario.consultarPorCodigo(codUsuario, Uteis.NIVELMONTARDADOS_VALIDACAOACESSO);
                if (null == usu){
                    throw new Exception("Usuário não encontrado");
                }
                if (usu.getColaboradorVO() != null && usu.getColaboradorVO().getSituacao().equals("NA")) {
                    ativo = false;
                }
            }

            JSONObject jsonBool = new JSONObject();
            jsonBool.put("ativo",ativo);
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("retorno",jsonBool);
            response.getWriter().append(jsonObject.toString());
        }catch (Exception e ){
            JSONObject json = new JSONObject();
            json.put("ERRO",e);
            response.getWriter().append(json.toString());
        }

    }

    private Connection getCon(String chave) throws Exception {
        return new DAO().obterConexaoEspecifica(chave);
    }

}

package servlet.appGestor;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.financeiro.MovPagamentoVO;
import negocio.comuns.financeiro.MovParcelaVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.financeiro.MovPagamento;
import negocio.facade.jdbc.financeiro.MovParcela;
import org.json.JSONObject;
import servlet.arquitetura.SuperServlet;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.sql.Connection;
import java.util.List;

public class StatusPersonalRestServlet extends SuperServlet {

    @Override
    protected void processRequest(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {

        response.addHeader("Access-Control-Allow-Origin", "*");
        response.addHeader("Access-Control-Allow-Methods", "GET");
        response.setContentType("application/json");
        String chave = request.getParameter("chave");
        String matricula = request.getParameter("matricula");
        String situacao = "em dia";
        String dataCancelamento = "nenhum cancelamento atrasado";
        long atraso = 0;
        try {
            try (Connection getConnection = getCon("1bf8ab74e4f43c402cbb3af93fc9daad")) {
                MovParcela movParcela = new MovParcela(getConnection);
                List<MovParcelaVO> listaMovParcelas =  movParcela.consultarPorMatricula(matricula, Uteis.NIVELMONTARDADOS_MINIMOS);
                for (MovParcelaVO parcela : listaMovParcelas) {
                    if (UteisValidacao.dataMenorDataAtualSemHora(Uteis.somarDias(parcela.getDataVencimento(),5))
                            && parcela.getSituacao().equals("EA")){
                        situacao = "atrasado";
                        atraso = Uteis.nrDiasEntreDatas(parcela.getDataVencimento(), Calendario.hoje());
                        dataCancelamento = Uteis.getData(parcela.getDataVencimento());
                        break;
                    }
                }
            }
            RetornoSituacaoPersonalGestorDTO retornoDTO = new RetornoSituacaoPersonalGestorDTO();
            retornoDTO.setSituacao(situacao);
            retornoDTO.setAtraso(atraso == 0 ? "nenhum atraso." : atraso + " dias de atraso.");
            retornoDTO.setDataCancelamento(dataCancelamento);
            JSONObject jsonObject = new JSONObject(retornoDTO);
            response.getWriter().append(jsonObject.toString());
        }catch (Exception e ){
            System.out.println("Erro na api rest templates de modelo de mensagem. Message: "+e.getMessage());
            processarErro(e, request, response, null);
        }

    }

    private Connection getCon(String chave) throws Exception {
        return new DAO().obterConexaoEspecifica(chave);
    }

}

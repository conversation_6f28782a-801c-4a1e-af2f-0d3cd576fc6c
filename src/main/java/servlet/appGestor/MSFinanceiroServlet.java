package servlet.appGestor;

import acesso.webservice.DaoAuxiliar;
import br.com.pactosolucoes.controle.json.SuperServletControle;
import org.json.JSONObject;
import servlet.appGestor.Interface.MSFinanceiroService;
import servlet.appGestor.appGestorDados.DadosEmpresa;
import servlet.appGestor.appGestorDados.LancamentoJSON;
import servlet.appGestor.appGestorDados.LancamentoToJSON;
import servlet.appGestor.appGestorDados.LancamentoTotal;
import servlet.appGestor.appGestorDados.OperacoesFinanceiroGestor;
import servlet.appGestor.appGestorDados.TipoContaJSON;
import servlet.appGestor.appGestorDados.TipoContaListToJSON;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

public class MSFinanceiroServlet extends SuperServletControle {

    @Override
    protected void processRequest(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        super.processRequest(request, response);
        PrintWriter out = response.getWriter();
        JSONObject jsonRetorno = new JSONObject();

        try {
            Date dataInicio = new Date();
            Date dataFim = new Date();

            try {
                String key = obterParametroString(request, "key");
                if (obterParametroSimpleDate(request, "dataInicio") != null) {
                    dataInicio = obterParametroSimpleDate(request, "dataInicio");
                }
                if (obterParametroSimpleDate(request, "dataFim") != null) {
                    dataFim = obterParametroSimpleDate(request, "dataFim");
                }
                else{
                    dataFim.setTime(Calendar.HOUR_OF_DAY);
                    dataFim = obterParametroSimpleDate(request, "dataFim");

                }
                String operacao = obterParametroString(request, "operacao");
                OperacoesFinanceiroGestor operacaoEnum = OperacoesFinanceiroGestor.obterOperacao(operacao);
                if (operacaoEnum == null) {
                    throw new Exception("Não foi possível identificar qual operação deve ser realizada.");
                }
                MSFinanceiroService msFinanceiroService = DaoAuxiliar.retornarAcessoControle(key).getAppGestorService();
                Integer empresa = obterParametroInt(request, "empresaId");
                switch (operacaoEnum) {
                    case saldoContas:
                        List<TipoContaJSON> contaJSONS = msFinanceiroService.consultarContas(dataInicio, dataFim);
                        TipoContaListToJSON tipoContaListToJSON = new TipoContaListToJSON();
                        tipoContaListToJSON.setContasList(contaJSONS);
                        jsonRetorno.put("resultado", tipoContaListToJSON.toJSON());
                        break;

                    case lancamentoContas:
                        List<LancamentoJSON> lancamentoJSONS = msFinanceiroService.obterDetalhamentoVencidos(empresa, dataInicio, dataFim, false);
                        LancamentoToJSON lancamentoToJSON = new LancamentoToJSON();
                        lancamentoToJSON.setLancamentoJSONS(lancamentoJSONS);
                        jsonRetorno.put("resultado", lancamentoToJSON.toJSON());
                        break;

                    case obterDetalhamento:
                        List<LancamentoJSON> detalhamento = msFinanceiroService.obterLancamentos(empresa, dataInicio, dataFim, true);
                        LancamentoToJSON detalhamentoJSON = new LancamentoToJSON();
                        detalhamentoJSON.setLancamentoJSONS(detalhamento);
                        jsonRetorno.put("resultado", detalhamentoJSON.toJSON());
                        break;

                    case obterRecebiveis:
                        List<LancamentoJSON> recebiveis = msFinanceiroService.obterRecebiveis(empresa, dataInicio, dataFim);
                        LancamentoToJSON recebiveisJSON = new LancamentoToJSON();
                        recebiveisJSON.setLancamentoJSONS(recebiveis);
                        jsonRetorno.put("resultado", recebiveisJSON.toJSON());
                        break;

                    case saldoGeral:
                        LancamentoTotal saldoGeral = msFinanceiroService.obterSaldoGeral(empresa, dataInicio, dataFim);
                        jsonRetorno.put("resultado", saldoGeral.toJSON());
                        break;

                    case lancamentosFuturos:
                        List<LancamentoJSON> lancamentosFuturos = msFinanceiroService.obterLancamentosFuturos(empresa, dataInicio, dataFim, true);
                        LancamentoToJSON lancamentosFuturosJSON = new LancamentoToJSON();
                        lancamentosFuturosJSON.setLancamentoJSONS(lancamentosFuturos);
                        jsonRetorno.put("resultado", lancamentosFuturosJSON.toJSON());
                        break;

                    case crm:
                        DadosEmpresa dadosEmpresas = msFinanceiroService.obterDadosEmpresaPeriodo(key, empresa, dataInicio);
                        jsonRetorno.put("resultado", dadosEmpresas.toJSON());
                        break;

                }
            } catch (Exception e) {

                jsonRetorno.put(STATUS_ERRO, e.getMessage());
            }
            out.println(jsonRetorno);
        } catch (
                Exception e) {
            out.println(e.getMessage());
        }
    }
}


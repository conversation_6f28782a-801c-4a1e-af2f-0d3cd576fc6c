package servlet.pix;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.financeiro.enumerador.TipoConvenioCobrancaEnum;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.financeiro.Pix;
import negocio.facade.jdbc.utilitarias.Conexao;
import org.json.JSONObject;
import servicos.impl.pix.PixWebhookService;
import servlet.arquitetura.SuperServlet;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.sql.Connection;


/**
 * Created with IntelliJ IDEA.
 * User: Rodrigo <PERSON>
 * Date: 23/11/2023
 */

public class PixWebhookServlet extends SuperServlet {

    @Override
    protected void processRequest(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        Connection con = null;
        Pix pixDAO = null;
        try {
            response.addHeader("Access-Control-Allow-Origin", "*");
            response.addHeader("Access-Control-Allow-Methods", "POST");
            response.setContentType("application/json");
            response.setCharacterEncoding("UTF-8");

            String method = request.getMethod();

            if (!method.equalsIgnoreCase("POST")) {
                throw new Exception("Método não suportado para esse recurso");
            }

            String chaveZW = request.getParameter("chaveZW");

            TipoConvenioCobrancaEnum tipoConvenioCobrancaEnum = null;
            try {
                tipoConvenioCobrancaEnum = TipoConvenioCobrancaEnum.valueOf(request.getParameter("tipoPix"));
            } catch (Exception e) {
                e.printStackTrace();
            }

            if (tipoConvenioCobrancaEnum == null &&
                    !UteisValidacao.emptyString(request.getParameter("tipoConvenioCobranca")) &&
                    !UteisValidacao.emptyNumber(UteisValidacao.converterInteiro(request.getParameter("tipoConvenioCobranca")))) {
                tipoConvenioCobrancaEnum = TipoConvenioCobrancaEnum.obterPorCodigo(UteisValidacao.converterInteiro(request.getParameter("tipoConvenioCobranca")));
            }

            int codigoPixWebhookOamd = 0;
            try {
                codigoPixWebhookOamd = Integer.parseInt(request.getParameter("codigoPixWebhookOamd"));
            } catch (Exception ignore) {}

            if (UteisValidacao.emptyString(chaveZW)) {
                throw new Exception("chaveZW não informada");
            }

            StringBuffer body = new StringBuffer();
            try {
                InputStream inputStream = request.getInputStream();
                BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream, "UTF-8"));

                String line = null;

                while ((line = reader.readLine()) != null) {
                    body.append(line);
                }
            } catch (Exception ex) {
                ex.printStackTrace();
                throw new Exception("Erro body: " + ex.getMessage());
            }

            con = obterConexao(chaveZW);
            proccessWebhook(con, chaveZW, tipoConvenioCobrancaEnum, body.toString(), codigoPixWebhookOamd);

            response.setStatus(HttpServletResponse.SC_OK);
            PrintWriter out = response.getWriter();
            out.print(this.toJSON(true, "ok"));
            out.flush();
        } catch (Exception ex) {
            response.setStatus(400);
            PrintWriter out = response.getWriter();
            out.print(this.toJSON(false, ex.getMessage()));
            out.flush();
        } finally {
            pixDAO = null;
            try {
                if (con != null) {
                    con.close();
                }
            } catch (Exception ex) {
                ex.printStackTrace();
            }
        }
    }

    public void proccessWebhook(Connection con, String chaveZW, TipoConvenioCobrancaEnum tipoConvenio, String body, int codigoPixWebhookOamd) throws Exception {
        PixWebhookService service = null;

        //PROCESSAR WEBHOOK
        //criar conexão específica para o service
        try (Connection connection = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            service = new PixWebhookService(connection);
            service.processar(chaveZW, tipoConvenio, body, codigoPixWebhookOamd);
        }
    }

    private Connection obterConexao(String chaveZW) throws Exception {
        return new DAO().obterConexaoEspecifica(chaveZW.trim());
    }

    private JSONObject toJSON(boolean sucesso, Object obj) {
        JSONObject json = new JSONObject();
        json.put("sucesso", sucesso);
        json.put("dados", obj);
        return json;
    }
}

package servlet.totem;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import controle.financeiro.PinpadTO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.basico.enumerador.ConfigTotemEnum;
import negocio.comuns.financeiro.AdquirenteVO;
import negocio.comuns.financeiro.FormaPagamentoVO;
import negocio.comuns.financeiro.MovParcelaVO;
import negocio.comuns.financeiro.OperadoraCartaoVO;
import negocio.comuns.financeiro.PinPadPedidoVO;
import negocio.comuns.financeiro.PinPadVO;
import negocio.comuns.financeiro.ReciboPagamentoVO;
import negocio.comuns.financeiro.enumerador.OrigemCobrancaEnum;
import negocio.comuns.financeiro.enumerador.StatusPinpadEnum;
import negocio.comuns.financeiro.enumerador.TipoFormaPagto;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.Log;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.facade.jdbc.basico.ConfiguracaoEmpresaTotem;
import negocio.facade.jdbc.basico.Empresa;
import negocio.facade.jdbc.basico.Pessoa;
import negocio.facade.jdbc.financeiro.Adquirente;
import negocio.facade.jdbc.financeiro.FormaPagamento;
import negocio.facade.jdbc.financeiro.MovPagamento;
import negocio.facade.jdbc.financeiro.MovParcela;
import negocio.facade.jdbc.financeiro.MovProdutoParcela;
import negocio.facade.jdbc.financeiro.OperadoraCartao;
import negocio.facade.jdbc.financeiro.PinPad;
import negocio.facade.jdbc.financeiro.PinPadPedido;
import org.json.JSONObject;
import servicos.impl.stone.connect.PedidoRetornoDTO;
import servicos.impl.stone.connect.StatusPedidoTotemDTO;
import servicos.impl.stone.connect.StoneConnectService;
import servlet.arquitetura.SuperServlet;

import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.PrintWriter;
import java.nio.charset.StandardCharsets;
import java.sql.Connection;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;


/**
 * Created with IntelliJ IDEA.
 * User: Luiz Felipe
 * Date: 23/03/2023
 * <p>
 * #########################################################################################
 * ######################################## ATENÇÃO ########################################
 * <p>
 * ESSA CLASSE FOI CRIADA COM O OBJETIVO DE EXECUTAR OPERAÇÕES PARA O TOTEM
 * SÓ É ACEITO REQUISAÇÃO POST !
 * <p>
 * ######################################## REGRAS ########################################
 * <p>
 * 1 - Passar a chave da empresa via parametro (key)
 * Exemplo:
 * http://localhost:8084/zw/prest/totem
 * <p>
 * #########################################################################################
 * #########################################################################################
 */
public class TotemServlet extends SuperServlet {

    @Override
    protected void processRequest(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        try {
            response.addHeader("Access-Control-Allow-Origin", "*");
            response.addHeader("Access-Control-Allow-Methods", "GET,POST");
            response.addHeader("Access-Control-Allow-Headers", "Authorization,empresaId");
            response.setContentType("application/json");

            String method = request.getMethod();

            if (!method.equalsIgnoreCase("POST")) {
                throw new Exception("Methodo não suportado para esse recurso");
            }

            String key = request.getParameter("key");
            String operacao = request.getParameter("op");

            if (UteisValidacao.emptyString(key)) {
                throw new Exception("Key não informada");
            }
            if (UteisValidacao.emptyString(operacao)) {
                throw new Exception("Operação não informada");
            }

            Object objResp;
            if (operacao.equalsIgnoreCase("pinpad_cobrar")) {
                objResp = criarCobrancaPinPad(request);
            } else if (operacao.equalsIgnoreCase("pinpad_status")) {
                objResp = consultarPedidoPinPad(request);
            } else if (operacao.equalsIgnoreCase("pinpad_cancelar")) {
                objResp = cancelarPedidoPinPad(request);
            } else if (operacao.equalsIgnoreCase("gravar_log_cappta")) {
                objResp = gravarLogCappta(request);
            } else {
                throw new Exception("Nenhuma operação executada");
            }

            response.setStatus(HttpServletResponse.SC_OK);
            PrintWriter out = response.getWriter();
            out.print(this.toJSON(true, objResp));
            out.flush();
        } catch (Exception ex) {
            ex.printStackTrace();
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            PrintWriter out = response.getWriter();
            out.print(this.toJSON(false, ex.getMessage()));
            out.flush();
        }
    }

    private JSONObject toJSON(boolean sucesso, Object obj) {
        JSONObject json = new JSONObject();
        json.put("sucesso", sucesso);
        json.put("dados", obj.toString());
        return json;
    }

    private String obterChave(ServletRequest request) throws Exception {
        String key = request.getParameter("key");
        if (UteisValidacao.emptyString(key)) {
            throw new Exception("Key não informada.");
        }
        return key;
    }

    private Connection obterConexao(ServletRequest request) throws Exception {
        String key = obterChave(request);
        return new DAO().obterConexaoEspecifica(key.trim());
    }

    private void finalizarConexao(Connection con) {
        try {
            if (con != null) {
                con.close();
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    private String obterBody(ServletRequest request) throws Exception {
        StringBuffer body = new StringBuffer();
        try {
            InputStream inputStream = request.getInputStream();
            BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream, StandardCharsets.UTF_8));
            String line = null;

            while ((line = reader.readLine()) != null) {
                body.append(line);
            }
            return body.toString();
        } catch (Exception ex) {
            ex.printStackTrace();
            throw new Exception("Erro body: " + ex.getMessage());
        }
    }

    private Object criarCobrancaPinPad(ServletRequest request) throws Exception {
        Connection con = null;
        Pessoa pessoaDAO;
        Empresa empresaDAO;
        PinPad pinPadDAO;
        PinPadPedido pinPadPedidoDAO;
        FormaPagamento formaPagamentoDAO;
        ConfiguracaoEmpresaTotem configuracaoEmpresaTotemDAO;
        Usuario usuarioDAO;
        try {
            con = obterConexao(request);
            pessoaDAO = new Pessoa(con);
            empresaDAO = new Empresa(con);
            pinPadDAO = new PinPad(con);
            pinPadPedidoDAO = new PinPadPedido(con);
            formaPagamentoDAO = new FormaPagamento(con);
            configuracaoEmpresaTotemDAO = new ConfiguracaoEmpresaTotem(con);
            usuarioDAO = new Usuario(con);

            String chave = obterChave(request);
            String body = obterBody(request);
            JSONObject jsonBody = new JSONObject(body);

            Integer pessoa = jsonBody.getInt("pessoa");
            Integer empresa = jsonBody.getInt("empresa");
            Double valor = jsonBody.getDouble("valor");
            Double multa = jsonBody.optDouble("multa");
            String tipoPagamento = jsonBody.getString("tipo");
            String totem = jsonBody.getString("totem");
            String parcelas = jsonBody.getString("parcelas");

            List<Integer> parcelasInt = new ArrayList<>();
            for (String p : parcelas.split("\\_")) {
                if (!UteisValidacao.emptyString(p)) {
                    parcelasInt.add(Integer.valueOf(p));
                }
            }

            if (UteisValidacao.emptyList(parcelasInt)) {
                throw new Exception("Parcelas não informado");
            }

            TipoFormaPagto tipoFormaPagto = TipoFormaPagto.getTipoFormaPagtoSigla(tipoPagamento);
            if (tipoFormaPagto == null ||
                    (!tipoFormaPagto.equals(TipoFormaPagto.CARTAOCREDITO) && !tipoFormaPagto.equals(TipoFormaPagto.CARTAODEBITO))) {
                throw new Exception("Tipo da forma de pagamento não implementado");
            }

            Integer nrparcelas = 0;
            if (tipoFormaPagto.equals(TipoFormaPagto.CARTAOCREDITO)) {
                nrparcelas = jsonBody.getInt("nrparcelas");
                if (UteisValidacao.emptyNumber(nrparcelas)) {
                    throw new Exception("Numero de parcelas não informado");
                }
            }

            PessoaVO pessoaVO = pessoaDAO.consultarPorChavePrimaria(pessoa, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            EmpresaVO empresaVO = empresaDAO.consultarPorChavePrimaria(empresa, Uteis.NIVELMONTARDADOS_EMPRESA_BASICO);

            Integer pinpad = 0;
            Integer formaPagamento = 0;
            if (tipoFormaPagto.equals(TipoFormaPagto.CARTAOCREDITO)) {
                try {
                    formaPagamento = Integer.valueOf(configuracaoEmpresaTotemDAO.obterValorConfigs(empresa, totem, ConfigTotemEnum.FORMA_PAGAMENTO_CREDITO));
                    String pinPadStoneConnectCredito = configuracaoEmpresaTotemDAO.obterValorConfigs(empresa, totem, ConfigTotemEnum.PINPAD_STONE_CONNECT_CREDITO);
                    if (!UteisValidacao.emptyString(pinPadStoneConnectCredito)) {
                        pinpad = Integer.valueOf(pinPadStoneConnectCredito);
                    }
                } catch (Exception ex) {
                    ex.printStackTrace();
                }
            } else {
                try {
                    formaPagamento = Integer.valueOf(configuracaoEmpresaTotemDAO.obterValorConfigs(empresa, totem, ConfigTotemEnum.FORMA_PAGAMENTO_DEBITO));
                    String pinPadStoneConnectDebito = configuracaoEmpresaTotemDAO.obterValorConfigs(empresa, totem, ConfigTotemEnum.PINPAD_STONE_CONNECT_DEBITO);
                    if (!UteisValidacao.emptyString(pinPadStoneConnectDebito)) {
                        pinpad = Integer.valueOf(pinPadStoneConnectDebito);
                    }
                } catch (Exception ex) {
                    ex.printStackTrace();
                }
            }

            if (UteisValidacao.emptyNumber(pinpad)) {
                throw new Exception("Pinpad Stone Connect não configurada para totem " + totem);
            }
            if (UteisValidacao.emptyNumber(formaPagamento)) {
                throw new Exception("Forma de pagamento Stone Connect não configurada para totem " + totem);
            }

            PinPadVO pinPadVO = pinPadDAO.consultarPorChavePrimaria(pinpad);
            FormaPagamentoVO formaPagamentoVO = formaPagamentoDAO.consultarPorChavePrimaria(formaPagamento, Uteis.NIVELMONTARDADOS_MINIMOS);

            PinpadTO pinpadTO = new PinpadTO();
            pinpadTO.setPinpadEnum(pinPadVO.getOpcoesPinpadEnum());
            pinpadTO.setNrParcelas(nrparcelas);
            pinpadTO.setValorPinpad(valor);

            StoneConnectService.validarPedidoAguardandoEFechar(pinPadVO, con);

            PinPadPedidoVO pinPadPedidoVO = StoneConnectService.gerarPedido(chave, tipoFormaPagto, pessoaVO, empresaVO, pinPadVO,
                    pinpadTO, formaPagamentoVO, OrigemCobrancaEnum.TOTEM, usuarioDAO.getUsuarioRecorrencia(), null, con);
            if (pinPadPedidoVO.getStatus() == null ||
                    !pinPadPedidoVO.getStatus().equals(StatusPinpadEnum.AGUARDANDO)) {
                throw new Exception(pinPadPedidoVO.getMsg());
            }

            pinPadPedidoVO.getDadosPedidoDTO().setTipo(tipoFormaPagto.getSigla());
            if (!multa.isNaN()) {
                pinPadPedidoVO.getDadosPedidoDTO().setMultaJuros(multa);
            } else {
                pinPadPedidoVO.getDadosPedidoDTO().setMultaJuros(0.0);
            }
            pinPadPedidoVO.getDadosPedidoDTO().setFormapagamento(formaPagamentoVO.getCodigo());
            pinPadPedidoVO.getDadosPedidoDTO().setParcelas(parcelasInt);
            pinPadPedidoDAO.alterar(pinPadPedidoVO);

            return pinPadPedidoVO.getCodigo();
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        } finally {
            finalizarConexao(con);
            pessoaDAO = null;
            empresaDAO = null;
            pinPadDAO = null;
            pinPadPedidoDAO = null;
            formaPagamentoDAO = null;
            configuracaoEmpresaTotemDAO = null;
            usuarioDAO = null;
        }
    }

    private Object consultarPedidoPinPad(ServletRequest request) throws Exception {
        Connection con = null;
        PinPadPedido pinPadPedidoDAO;
        try {
            con = obterConexao(request);
            pinPadPedidoDAO = new PinPadPedido(con);

            String bodyString = obterBody(request);
            JSONObject body = new JSONObject(bodyString);
            Integer pedido = body.getInt("pedido");

            StatusPedidoTotemDTO dto = new StatusPedidoTotemDTO();
            dto.setPedido(pedido);

            PinPadPedidoVO pinPadPedidoVO = pinPadPedidoDAO.consultarPorChavePrimaria(pedido);
            dto.setStatus(pinPadPedidoVO.getStatus().getDescricao());

            PedidoRetornoDTO pedidoRetornoDTO = pinPadPedidoDAO.consultarPedido(pinPadPedidoVO);

            if (pedidoRetornoDTO != null) {

                dto.setMensagem(pedidoRetornoDTO.getDados());

                if (pedidoRetornoDTO.getStatus() != null &&
                        !pedidoRetornoDTO.getStatus().equals(StatusPinpadEnum.AGUARDANDO)) {

                    pinPadPedidoVO.setStatus(pedidoRetornoDTO.getStatus());

                    if (pinPadPedidoVO.getStatus().equals(StatusPinpadEnum.PAGO)) {

                        PinpadTO pinpad = new PinpadTO();
                        pinpad.setPinpadCappta(false);
                        pinpad.setValorPinpad(pinPadPedidoVO.getValor());
                        pinpad.setPinpadEnum(pinPadPedidoVO.getPinpad());
                        pinpad.setCodigoAutorizacao(pedidoRetornoDTO.getAutorizacao());
                        pinpad.setCodigoNSU(pedidoRetornoDTO.getNsu());
                        pinpad.setBandeira(pedidoRetornoDTO.getBandeira());
                        pinpad.setRespostaRequisicao(pedidoRetornoDTO.getDados());
                        pinpad.setConvenioCobranca(pinPadPedidoVO.getConvenioCobrancaVO().getCodigo());
                        pinpad.setPinpadPedido(pinPadPedidoVO.getCodigo());

                        if (UteisValidacao.emptyNumber(pinPadPedidoVO.getReciboPagamentoVO().getCodigo())) {
                            gerarPagamentoPinpad(pinPadPedidoVO, pinpad, con);
                            pinPadPedidoDAO.alterar(pinPadPedidoVO);
                        }

                        dto.setAutorizacao(pedidoRetornoDTO.getAutorizacao());
                        dto.setNsu(pedidoRetornoDTO.getNsu());
                        dto.setRecibo(pinPadPedidoVO.getReciboPagamentoVO().getCodigo());
                        dto.setStatus(pinPadPedidoVO.getStatus().getDescricao());
                        dto.setMensagem("Pagamento Efetuado");
                    } else if (pinPadPedidoVO.getStatus().equals(StatusPinpadEnum.FALHA) ||
                            pinPadPedidoVO.getStatus().equals(StatusPinpadEnum.CANCELADO)) {
                        dto.setStatus(pinPadPedidoVO.getStatus().getDescricao());
                        dto.setMensagem("Operação abortada");
                    }
                }
            }

            return new JSONObject(dto);
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        } finally {
            finalizarConexao(con);
            pinPadPedidoDAO = null;
        }
    }

    private void gerarPagamentoPinpad(PinPadPedidoVO pinPadPedidoVO, PinpadTO pinpadTO,
                                      Connection con) throws Exception {
        OperadoraCartao operadoraCartaoDAO;
        Adquirente adquirenteDAO;
        FormaPagamento formaPagamentoDAO;
        MovPagamento movPagamentoDAO;
        Empresa empresaDAO;
        Pessoa pessoaDAO;
        MovParcela movParcelaDAO;
        MovProdutoParcela movProdutoParcelaDAO;
        try {
            operadoraCartaoDAO = new OperadoraCartao(con);
            adquirenteDAO = new Adquirente(con);
            formaPagamentoDAO = new FormaPagamento(con);
            movPagamentoDAO = new MovPagamento(con);
            empresaDAO = new Empresa(con);
            pessoaDAO = new Pessoa(con);
            movParcelaDAO = new MovParcela(con);
            movProdutoParcelaDAO = new MovProdutoParcela(con);

            FormaPagamentoVO formaPagamentoVO = formaPagamentoDAO.consultarPorChavePrimaria(pinPadPedidoVO.getFormaPagamentoVO().getCodigo(), Uteis.NIVELMONTARDADOS_MINIMOS);
            EmpresaVO empresaVO = empresaDAO.consultarPorChavePrimaria(pinPadPedidoVO.getEmpresaVO().getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            PessoaVO pessoaVO = pessoaDAO.consultarPorChavePrimaria(pinPadPedidoVO.getPessoaVO().getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);

            List<MovParcelaVO> listaParcelas = new ArrayList<>();
            for (Integer parcela : pinPadPedidoVO.getDadosPedidoDTO().getParcelas()) {
                MovParcelaVO parc = movParcelaDAO.consultarPorCodigo(parcela, Uteis.NIVELMONTARDADOS_PARCELA);
                parc.setMovProdutoParcelaVOs(movProdutoParcelaDAO.consultarPorCodigoMovParcela(parc.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS));
                listaParcelas.add(parc);
            }

            Double multa = movParcelaDAO.montarMultaJurosParcelaVencida(empresaVO, listaParcelas, Calendario.hoje(), false, 1.0, null);

            List<MovParcelaVO> multas = new ArrayList<MovParcelaVO>();
            if (!UteisValidacao.emptyNumber(multa)) {

                // Estamos tendo problemas onde cria as parcelas de Multa e Juros e as mesmas não são apagadas em caso de erro.
                // Mas não temos como saber a origem, pois a criação vem de vários locais diferentes.
                // Por isso a inclusão desses logs
                if (!UteisValidacao.emptyList(listaParcelas)) {
                    String codigosMovParcelas = listaParcelas.stream()
                            .map(p -> String.valueOf(p.getCodigo()))
                            .collect(Collectors.joining(","));
                    Uteis.logarDebug("TotemServlet - gerarPagamentoPinpad - Identificar multa e juros criada e nao excluida apos pagamento. - Parcelas: " + codigosMovParcelas);
                }

                multas = movParcelaDAO.criarParcelaMultaJuros(listaParcelas,
                        empresaVO, pinPadPedidoVO.getUsuarioVO(), Calendario.hoje(), 1.0, null, true);
            }
            listaParcelas.addAll(multas);

            AdquirenteVO adquirenteVO = null;
            try {
                if (!UteisValidacao.emptyString(pinpadTO.getAdquirenteGravar())) {
                    adquirenteVO = adquirenteDAO.consultarOuCriaSeNaoExistir(pinpadTO.getAdquirenteGravar());
                }
            } catch (Exception ex) {
                ex.printStackTrace();
            }

            OperadoraCartaoVO operadoraCartaoVO = null;
            try {
                if (pinpadTO.getOperadorasExternasAprovaFacilEnum() != null) {
                    boolean credito = formaPagamentoVO.getTipoFormaPagamento().equals(TipoFormaPagto.CARTAOCREDITO.getSigla());
                    operadoraCartaoVO = operadoraCartaoDAO.consultarOuCriaSeNaoExistirPinpad(pinpadTO.getOperadorasExternasAprovaFacilEnum(),
                            pinpadTO.getPinpadEnum(), credito, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                }
            } catch (Exception ex) {
                ex.printStackTrace();
            }

            ReciboPagamentoVO reciboPagamentoVO = movPagamentoDAO.gerarPagamentoCartao(pessoaVO, pinpadTO.getValorPinpad(), listaParcelas,
                    pinpadTO.getNrParcelas(), pinpadTO.getNsu(), pinpadTO.getAutorizacao(), pinPadPedidoVO.getUsuarioVO(), formaPagamentoVO,
                    pinPadPedidoVO.getConvenioCobrancaVO(), adquirenteVO, operadoraCartaoVO, empresaVO);

            pinPadPedidoVO.setReciboPagamentoVO(reciboPagamentoVO);
            try {
                pinPadPedidoVO.setMovPagamentoVO(reciboPagamentoVO.getPagamentosDesteRecibo().get(0));
            } catch (Exception ex) {
                ex.printStackTrace();
            }
        } finally {
            operadoraCartaoDAO = null;
            adquirenteDAO = null;
            formaPagamentoDAO = null;
            movPagamentoDAO = null;
            empresaDAO = null;
            pessoaDAO = null;
            movParcelaDAO = null;
            movProdutoParcelaDAO = null;
        }
    }

    private Object cancelarPedidoPinPad(ServletRequest request) throws Exception {
        Connection con = null;
        PinPadPedido pinPadPedidoDAO;
        try {
            con = obterConexao(request);
            pinPadPedidoDAO = new PinPadPedido(con);

            String bodyString = obterBody(request);
            JSONObject body = new JSONObject(bodyString);
            Integer pedido = body.getInt("pedido");

            PinPadPedidoVO pinPadPedidoVO = pinPadPedidoDAO.consultarPorChavePrimaria(pedido);

            String retorno = StoneConnectService.fecharPedido(pinPadPedidoVO, StatusPinpadEnum.FALHA, true, true, con);

            pinPadPedidoVO = pinPadPedidoDAO.consultarPorChavePrimaria(pedido);
            if (pinPadPedidoVO.getStatus().equals(StatusPinpadEnum.FALHA) ||
                    pinPadPedidoVO.getStatus().equals(StatusPinpadEnum.CANCELADO)) {
                return "Operação Cancelada";
            } else {
                throw new Exception(retorno);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        } finally {
            finalizarConexao(con);
            pinPadPedidoDAO = null;
        }
    }

    private Object gravarLogCappta(ServletRequest request) throws Exception {
        String retorno;
        Connection con = null;
        MovParcela movParcelaDAO;
        Pessoa pessoaDAO;
        Empresa empresaDAO;
        Log logDAO;
        try{
            con = obterConexao(request);
            movParcelaDAO = new MovParcela(con);
            pessoaDAO = new Pessoa(con);
            empresaDAO = new Empresa(con);
            logDAO = new Log(con);

            String body = obterBody(request);
            JSONObject jsonBody = new JSONObject(body);

            Integer empresa = jsonBody.getInt("empresa");
            String totem = jsonBody.getString("totem");
            String parcelasSelecionadas = jsonBody.getString("parcelasSelecionadas");

            String[] arrayParcelasSelecionadas = parcelasSelecionadas.split("_");

            PessoaVO pessoaVO = new PessoaVO();
            if (arrayParcelasSelecionadas[0].length() > 0) {
                Integer idPessoaParcela = movParcelaDAO.consultarPessoaDaMovParcela(Integer.parseInt(arrayParcelasSelecionadas[0]));
                pessoaVO = pessoaDAO.consultarPorCodigo(idPessoaParcela, Uteis.NIVELMONTARDADOS_CONSULTA_WS);
            }
            EmpresaVO empresaVO = empresaDAO.consultarPorCodigo(empresa, Uteis.NIVELMONTARDADOS_MINIMOS);

            StringBuilder infoDados = new StringBuilder();
            infoDados.append("PESSOA CODIGO: ").append(pessoaVO.getCodigo()).append(", ");
            infoDados.append("PESSOA NOME: ").append(pessoaVO.getNome()).append(", ");
            infoDados.append("EMPRESA CODIGO: ").append(empresaVO.getCodigo()).append(", ");
            infoDados.append("EMPRESA NOME: ").append(empresaVO.getNome()).append(", ");
            infoDados.append("PARCELA CODIGO: ");

            for (int i = 0; i < arrayParcelasSelecionadas.length; i++) {
                infoDados.append(arrayParcelasSelecionadas[i]).append(", ");
            }

            logDAO.incluirLogCappta(totem, infoDados.toString(), "tentativaCobranca");
            retorno = "Log gravado com Sucesso";
        }catch (Exception ex) {
            retorno = "ERRO: " + ex.getMessage();
            Uteis.logar(ex, this.getClass());
        } finally {
            finalizarConexao(con);
            movParcelaDAO = null;
            pessoaDAO = null;
            empresaDAO = null;
            logDAO = null;
        }
        return retorno;
    }

}

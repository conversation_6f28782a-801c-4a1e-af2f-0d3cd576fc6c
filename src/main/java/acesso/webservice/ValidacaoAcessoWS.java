package acesso.webservice;

import acesso.webservice.retorno.*;
import br.com.pactosolucoes.comuns.util.Formatador;
import br.com.pactosolucoes.enumeradores.TipoColaboradorEnum;
import negocio.comuns.acesso.AutorizacaoAcessoGrupoEmpresarialVO;
import negocio.comuns.acesso.LocalAcessoVO;
import negocio.comuns.acesso.enumerador.DirecaoAcessoEnum;
import negocio.comuns.acesso.enumerador.MeioIdentificacaoEnum;
import negocio.comuns.acesso.enumerador.MetodoAcessoEnum;
import negocio.comuns.acesso.enumerador.SituacaoAcessoEnum;
import negocio.comuns.acesso.enumerador.TipoAcessoEnum;
import negocio.comuns.acesso.enumerador.TipoLiberacaoEnum;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.xmlgen.ClienteXML;
import negocio.comuns.basico.xmlgen.ColaboradorXML;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import org.json.JSONObject;
import servicos.acesso.AcessoConfigService;
import servicos.acesso.AcessoOfflineService;
import servicos.acesso.AcessoPerfilClienteService;
import servicos.acesso.AcessoPessoaService;
import servicos.acesso.AutorizacaoAcessoService;
import servicos.acesso.BiometriaDigitalService;
import servicos.acesso.BiometriaFacialService;
import servicos.acesso.ValidacaoAcessoService;
import servicos.integracao.ValidacaoAcessoWSConsumer;
import servicos.notificador.NotificacaoTO;
import servicos.notificador.NotificadorServiceControle;
import servicos.propriedades.PropsService;

import javax.jws.WebMethod;
import javax.jws.WebParam;
import javax.jws.WebService;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 *
 * <AUTHOR>
 */
@WebService(name = "ValidacaoAcessoWS", serviceName = "ValidacaoAcessoWS")
public class ValidacaoAcessoWS extends BaseAcessoWS {


    @WebMethod(operationName = "validarAcessoPeloCodigoAcessoAvaliandoIntegracao")
    public RetornoRequisicaoValidacaoAcesso validarAcessoPeloCodigoAcessoAvaliandoIntegracao(
            @WebParam(name = "codigoCartao") String codigoCartao,
            @WebParam(name = "empresa") Integer empresa,
            @WebParam(name = "forcarLib") Boolean forcarLib,
            @WebParam(name = "key") String key,
            @WebParam(name = "localAcesso") Integer localAcesso,
            @WebParam(name = "meioIdentificacao") MeioIdentificacaoEnum meioIdentificacao,
            @WebParam(name = "sentido") DirecaoAcessoEnum direcao,
            @WebParam(name = "terminal") String terminal,
            @WebParam(name = "acessoOutraEmpresa") Boolean acessoOutraEmpresa) throws Exception {

       return ValidacaoAcessoService.validarAcessoPeloCodigoAcessoAvaliandoIntegracao(
              codigoCartao,
               empresa,
               forcarLib,
               key,
               localAcesso,
               meioIdentificacao,
               direcao,
               terminal,
               acessoOutraEmpresa);
    }

    /*
     * Validar o acesso da pessoa através do código de acesso.
     */
    @Deprecated
    @WebMethod(operationName = "validarAcessoPeloCodigoAcesso")
    public RetornoRequisicaoValidacaoAcesso validarAcessoPeloCodigoAcesso(
            @WebParam(name = "codigoCartao") String codigoCartao,
            @WebParam(name = "empresa") Integer empresa,
            @WebParam(name = "forcarLib") Boolean forcarLib,
            @WebParam(name = "key") String key,
            @WebParam(name = "localAcesso") Integer localAcesso,
            @WebParam(name = "meioIdentificacao") MeioIdentificacaoEnum meioIdentificacao,
            @WebParam(name = "sentido") DirecaoAcessoEnum direcao,
            @WebParam(name = "terminal") String terminal) throws Exception {
        return validarAcessoPeloCodigoAcessoAvaliandoIntegracao(codigoCartao, empresa, forcarLib, key, localAcesso, meioIdentificacao, direcao, terminal, false);
    }

    /*
     * Valida o acesso do cliente pela Matrícula.
     */
    @WebMethod(operationName = "validarAcessoClientePelaMatricula")
    public RetornoRequisicaoValidacaoAcesso validarAcessoClientePelaMatricula(
            @WebParam(name = "empresa") Integer empresa,
            @WebParam(name = "forcarLib") Boolean forcarLib,
            @WebParam(name = "key") String key,
            @WebParam(name = "localAcesso") Integer localAcesso,
            @WebParam(name = "matricula") String matricula,
            @WebParam(name = "meioIdentificacao") MeioIdentificacaoEnum meioIdentificacao,
            @WebParam(name = "sentido") DirecaoAcessoEnum direcao,
            @WebParam(name = "terminal") String terminal) throws Exception {
        AutorizacaoAcessoGrupoEmpresarialVO autorizacao = new AutorizacaoAcessoGrupoEmpresarialVO();
        //AutorizacaoAcessoGrupoEmpresarialVO autorizacao = DaoAuxiliar.retornarAcessoControle(key).getAutorizacaoDao().consultarPorCodigo(null, Integer.valueOf(matricula),
        //        null, null, null, null, null, null, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        if (UteisValidacao.emptyNumber(autorizacao.getCodigo())) {
            String codigoAcesso = "";
            RetornoRequisicaoValidacaoAcesso retorno = null;
            ClienteVO cliente = null;
            try {
                if (Integer.parseInt(matricula) != 0) {
                    AcessoControle acessoControle = DaoAuxiliar.retornarAcessoControle(key);
                    cliente = acessoControle.getClienteDao().consultarPorMatricula(matricula, false, Uteis.NIVELMONTARDADOS_MINIMOS);
                    if (cliente != null) {
                        codigoAcesso = cliente.getCodAcesso();
                    }
                }
                if ((cliente == null) || (Integer.parseInt(matricula) == 0)) {
                    RetornoRequisicaoValidacaoAcesso validarAcessoWS = RetornoRequisicaoWS.montarRetornoValidacaoAcesso(
                            SituacaoAcessoEnum.RV_BLOQALUNOMATNAOCADASTRADO);
                    validarAcessoWS.setTerminal(terminal);
                    return validarAcessoWS;
                }
                retorno = Validador.validarAcesso(codigoAcesso, empresa, localAcesso,
                        terminal, direcao, forcarLib, meioIdentificacao, key);
            } catch (Exception e) {
                if (retorno == null) {
                    retorno = new RetornoRequisicaoValidacaoAcesso();
                }
                retorno.setResultado(ResultadoWSEnum.ERRO);
                retorno.setTerminal(terminal);
                retorno.setMsgErro("Método que ocorreu o erro: "
                        + "\"public RetornoRequisicaoValidacaoAcesso validarAcessoClientePelaMatricula\""
                        + " Classe do Erro: " + e.getClass()
                        + " Mensagem Erro: " + e.getMessage());

            }
            return retorno;
        } else {
            return ValidacaoAcessoWSConsumer.validarAcesso(autorizacao, matricula,
                    autorizacao.getEmpresaRemota().getCodigo(),
                    autorizacao.getIntegracao().getLocalAcesso(),
                    autorizacao.getIntegracao().getTerminal().toString(),
                    direcao,
                    forcarLib, meioIdentificacao, autorizacao.getIntegracao().getChave(), autorizacao.getIntegracao().getUrlZillyonWeb(), MetodoAcessoEnum.PELAMATRICULA, terminal, false, null);
        }
    }

    /*Author: Ulisses
     *Data: 02/06/11
     *Objetivo: Validar o acesso de Cliente e Colaborador por senha.
     * Obs.:Inicialmente a senha será digitada no teclado da catraca.
     */
    @WebMethod(operationName = "validarAcessoPessoaPorSenha")
    public RetornoRequisicaoValidacaoAcesso validarAcessoPessoaPorSenha(
            @WebParam(name = "empresa") Integer empresa,
            @WebParam(name = "forcarLib") Boolean forcarLib,
            @WebParam(name = "key") String key,
            @WebParam(name = "localAcesso") Integer localAcesso,
            @WebParam(name = "meioIdentificacao") MeioIdentificacaoEnum meioIdentificacao,
            @WebParam(name = "senha") String senha,
            @WebParam(name = "sentido") DirecaoAcessoEnum direcao,
            @WebParam(name = "terminal") String terminal) throws Exception {

        AutorizacaoAcessoGrupoEmpresarialVO autorizacao = DaoAuxiliar.retornarAcessoControle(key).getAutorizacaoDao().consultarPorCodigo(null, null, null, null, senha, null, null, null, Uteis.NIVELMONTARDADOS_DADOSBASICOS, null, null);
        EmpresaVO empresaVO =  DaoAuxiliar.retornarAcessoControle(key).getEmpresaDao().consultarPorChavePrimaria(empresa,Uteis.NIVELMONTARDADOS_GESTAOREMESSA);
        if (UteisValidacao.emptyNumber(autorizacao.getCodigo())) {
            String codAcessoCliente = null;
            String codAcessoColaborador = null;
            RetornoRequisicaoValidacaoAcesso retorno = null;
            RetornoRequisicaoValidacaoAcesso retornoCliente = null;
            RetornoRequisicaoValidacaoAcesso retornoColab = null;
            ClienteVO clienteVo = null;
            ColaboradorVO colaboradorVo = null;
            boolean senhaInvalida = false;
            ResultSet codigos = null;
            try {
                senhaInvalida = (senha.length() != (empresaVO.isSenhaAcessoOnzeDigitos() ? 11 : 5));
                AcessoControle acessoControle = DaoAuxiliar.retornarAcessoControle(key);
                if (!senhaInvalida) {
                    String senhaEncriptada = Uteis.encriptar(senha);
                    codigos = acessoControle.obterCodigoAcessoPorSenhaEmpresa(senhaEncriptada);
                }
                int qtdCodigos = 0;
                if (codigos != null && codigos.next()) {
                    codigos.last();
                    qtdCodigos = codigos.getRow();
                    codigos.beforeFirst();

                    while (codigos.next()) {
                        if (!UteisValidacao.emptyString(codigos.getString("codcliente")) && (UteisValidacao.emptyString(codigos.getString("codcolaborador")) || UteisValidacao.emptyNumber(codigos.getInt("acessocolaborador")))) {
                            retornoCliente = Validador.validarAcesso(codigos.getString("codcliente"), empresa, localAcesso,
                                    terminal, direcao, forcarLib, meioIdentificacao, key);
                            retorno = retornoCliente;
                            if (retornoCliente.getBloqueadoLiberado().equals("B") && !UteisValidacao.emptyString(codigos.getString("codcolaborador"))) {
                                retornoColab = Validador.validarAcesso(codigos.getString("codcolaborador"), empresa, localAcesso,
                                        terminal, direcao, forcarLib, meioIdentificacao, key);
                                retorno = retornoColab;
                            }
                            if (retornoCliente.getSituacaoAcesso().name().equals("RV_BLOQALUNOPARCELAABERTA") && (retornoColab != null && retornoColab.getSituacaoAcesso().name().equals("RV_BLOQCOLABORADORINATIVO"))){
                                retorno = retornoCliente;
                            }
                        } else {
                            if (qtdCodigos > 1) {
                                ColaboradorVO colaborador = acessoControle.getColaboradorDao().consultarPorCodAcesso(codigos.getString("codcolaborador"), empresa, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                                if (colaborador == null) {
                                    continue;
                                }
                            }

                            retorno = Validador.validarAcesso(codigos.getString("codcolaborador"), empresa, localAcesso,
                                    terminal, direcao, forcarLib, meioIdentificacao, key);
                            if (retorno.getBloqueadoLiberado().equals("B") && !UteisValidacao.emptyString(codigos.getString("codcliente"))) {
                                retorno = Validador.validarAcesso(codigos.getString("codcliente"), empresa, localAcesso,
                                        terminal, direcao, forcarLib, meioIdentificacao, key);
                            }
                        }
                    }
                } else {
                    RetornoRequisicaoValidacaoAcesso validarAcessoWS = RetornoRequisicaoWS.montarRetornoValidacaoAcesso(
                            SituacaoAcessoEnum.RV_BLOQPESSOASENHAINVALIDA);
                    validarAcessoWS.setTerminal(terminal);
                    return validarAcessoWS;
                }
            } catch (Exception e) {
                if (retorno == null) {
                    retorno = new RetornoRequisicaoValidacaoAcesso();
                }
                retorno.setResultado(ResultadoWSEnum.ERRO);
                retorno.setTerminal(terminal);
                retorno.setMsgErro("Método que ocorreu o erro: "
                        + "\"public RetornoRequisicaoValidacaoAcesso validarAcessoPessoaPorSenha\""
                        + " Classe do Erro: " + e.getClass()
                        + " Mensagem Erro: " + e.getMessage());

            }
            return retorno;
        } else {
            if(autorizacao.getTipoPessoa().equals("CL")){
                if(UteisValidacao.emptyNumber(autorizacao.getCodigoMatricula())){
                    String codAcesso = autorizacao.getCodAcesso().startsWith("NU") ? autorizacao.getCodAcesso().substring(2) : autorizacao.getCodAcesso();
                    return ValidacaoAcessoWSConsumer.validarAcesso(autorizacao, codAcesso,
                            autorizacao.getEmpresaRemota().getCodigo(),
                            autorizacao.getIntegracao().getLocalAcesso(),
                            autorizacao.getIntegracao().getTerminal().toString(),
                            direcao,
                            forcarLib, meioIdentificacao, autorizacao.getIntegracao().getChave(), autorizacao.getIntegracao().getUrlZillyonWeb(), MetodoAcessoEnum.PELOCODIGOACESSO, terminal, false, null);
                } else {
                    return ValidacaoAcessoWSConsumer.validarAcesso(autorizacao, autorizacao.getCodigoMatricula().toString(),
                            autorizacao.getEmpresaRemota().getCodigo(),
                            autorizacao.getIntegracao().getLocalAcesso(),
                            autorizacao.getIntegracao().getTerminal().toString(),
                            direcao,
                            forcarLib, meioIdentificacao, autorizacao.getIntegracao().getChave(), autorizacao.getIntegracao().getUrlZillyonWeb(), MetodoAcessoEnum.PELAMATRICULA, terminal, false, null);
                }

            } else {
                return ValidacaoAcessoWSConsumer.validarAcesso(autorizacao, autorizacao.getCodigoGenerico().toString(),
                        autorizacao.getEmpresaRemota().getCodigo(),
                        autorizacao.getIntegracao().getLocalAcesso(),
                        autorizacao.getIntegracao().getTerminal().toString(),
                        direcao,
                        forcarLib, meioIdentificacao, autorizacao.getIntegracao().getChave(), autorizacao.getIntegracao().getUrlZillyonWeb(), MetodoAcessoEnum.PELOCODIGOCOLABORADOR, terminal, false, null);
            }
        }

    }

    /*
     * Valida o acesso do Colaborador pelo código
     */
    @WebMethod(operationName = "validarAcessoColaboradorPeloCodigo")
    public RetornoRequisicaoValidacaoAcesso validarAcessoColaboradorPeloCodigo(
            @WebParam(name = "codigo") String codigo,
            @WebParam(name = "empresa") Integer empresa,
            @WebParam(name = "forcarLib") Boolean forcarLib,
            @WebParam(name = "key") String key,
            @WebParam(name = "localAcesso") Integer localAcesso,
            @WebParam(name = "meioIdentificacao") MeioIdentificacaoEnum meioIdentificacao,
            @WebParam(name = "sentido") DirecaoAcessoEnum direcao,
            @WebParam(name = "terminal") String terminal) throws Exception {

        String codigoAcesso = "";
        RetornoRequisicaoValidacaoAcesso retorno = null;
        try {
            AcessoControle acessoControle = DaoAuxiliar.retornarAcessoControle(key);
            ColaboradorVO colaborador = acessoControle.getColaboradorDao().consultarPorCodigo(Integer.valueOf(codigo), 0, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            if ((colaborador != null) && (colaborador.getCodigo().intValue() != 0)) {
                codigoAcesso = colaborador.getCodAcesso();
                retorno = Validador.validarAcesso(codigoAcesso, empresa, localAcesso,
                        terminal, direcao, forcarLib, meioIdentificacao, key);
            } else {
                RetornoRequisicaoValidacaoAcesso validarAcessoWS = RetornoRequisicaoWS.montarRetornoValidacaoAcesso(
                        SituacaoAcessoEnum.RV_BLOQCOLABORADORCODNAOCADASTRADO);
                validarAcessoWS.setTerminal(terminal);
                retorno = validarAcessoWS;
            }
        } catch (Exception e) {
            if (retorno == null) {
                retorno = new RetornoRequisicaoValidacaoAcesso();
            }
            retorno.setTerminal(terminal);
            retorno.setResultado(ResultadoWSEnum.ERRO);
            retorno.setMsgErro("Método que ocorreu o erro: "
                    + "\"public RetornoRequisicaoValidacaoAcesso validarAcessoColaboradorPeloCodigo\""
                    + " Classe do Erro: " + e.getClass()
                    + " Mensagem Erro: " + e.getMessage());
        }
        return retorno;

    }

    /**
     * Operação de serviço web Objetivo: Retornar as informações do Local de
     * Acesso, bem como seus coletores.
     */
    @WebMethod(operationName = "buscarConfigLocalAcesso")
    public RetornoRequisicaoBuscarLocais buscarConfigLocalAcesso(
            @WebParam(name = "empresa") Integer empresa,
            @WebParam(name = "key") String key,
            @WebParam(name = "nomeComputador") String nomeComputador) throws Exception {
        RetornoRequisicaoBuscarLocais retorno = null;
        try {
            AcessoControle acessoControle = DaoAuxiliar.retornarAcessoControle(key);
            LocalAcessoVO localAcessoVo = acessoControle.getLocalAcessoDao().consultarPorComputador(nomeComputador, empresa);
            if ((localAcessoVo.getCodigo() == null) || (localAcessoVo.getCodigo() <= 0)) {
                retorno = new RetornoRequisicaoBuscarLocais();
                retorno.setResultado(ResultadoWSEnum.ERRO);
                retorno.setMsgErro("Nenhum local cadastrado para o computador \"" + nomeComputador + "\"");
            } else {
                localAcessoVo.setListaColetores(acessoControle.getColetorDao().consultarColetores(localAcessoVo.getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));
                EmpresaVO empresaVO = acessoControle.getEmpresaDao().consultarPorChavePrimaria(localAcessoVo.getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_GESTAOREMESSA);
                localAcessoVo.setEmpresa(empresaVO);
                retorno = RetornoRequisicaoWS.montarRetornoLocalAcesso(localAcessoVo, empresaVO.isSenhaAcessoOnzeDigitos(), empresaVO.isRegistrarTentativasAcesso());
                //TODO: Removido LogControleUsabilidade
                /*acessoControle.gravarLogSolicitacaoConfLocal(empresa, nomeComputador);*/
            }
        } catch (Exception e) {
            if (retorno == null) {
                retorno = new RetornoRequisicaoBuscarLocais();
            }
            retorno.setResultado(ResultadoWSEnum.ERRO);
            retorno.setMsgErro("Método que ocorreu o erro: "
                    + "\"public RetornoRequisicaoBuscarLocais buscarConfigLocalAcesso\""
                    + " Classe do Erro: " + e.getClass()
                    + " Mensagem Erro: " + e.getMessage());

        }
        return retorno;
    }

    @WebMethod(operationName = "validarPemissaoUsuario")
    public RetornoRequisicaoValidarPermissaoUsuario validarPemissaoUsuario(
            @WebParam(name = "empresa") Integer empresa,
            @WebParam(name = "key") String key,
            @WebParam(name = "recurso") String recurso,
            @WebParam(name = "senha") String senha,
            @WebParam(name = "terminal") Integer terminal,
            @WebParam(name = "usuario") String usuario) {
        RetornoRequisicaoValidarPermissaoUsuario retorno = new RetornoRequisicaoValidarPermissaoUsuario();
        try {
            AcessoControle acessoControle = DaoAuxiliar.retornarAcessoControle(key);
            try {

                Integer usuarioCodigo;
                if( Uteis.isInteger(usuario) ){
                    usuarioCodigo = Integer.parseInt(usuario);
                }else{
                    UsuarioVO usuarioVO = acessoControle.getUsuarioDao()
                            .consultarPorUsername(usuario, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                    usuarioCodigo = usuarioVO.getCodigo();
                }

                retorno = acessoControle.validarPermissaoUsuario(usuarioCodigo, senha, empresa, recurso);
            } catch (Exception e) {
                /*
                 * Na validação do usuário, se o usuário não tem permissão é lançada uma exceção
                 */
                retorno.setTemPemissao(false);
                retorno.setMensagem(e.getMessage());
            }
        } catch (Exception e) {
            retorno.setResultado(ResultadoWSEnum.ERRO);
            retorno.setMsgErro("Método que ocorreu o erro: "
                    + "\"public RetornoRequisicaoLiberarForcado permiteLiberarForcado\""
                    + " Classe do Erro: " + e.getClass()
                    + " Mensagem Erro: " + e.getMessage());
        }
        return retorno;
    }

    /**
     * Operação de serviço web
     */
    @WebMethod(operationName = "registrarAcesso")
    public RetornoRequisicaoRegistrarAcesso registrarAcesso(
            @WebParam(name = "codigo") Integer codigo,
            @WebParam(name = "dataAcesso") Date dataAcesso,
            @WebParam(name = "direcao") DirecaoAcessoEnum direcao,
            @WebParam(name = "empresa") Integer empresa,
            @WebParam(name = "key") String key,
            @WebParam(name = "local") Integer local,
            @WebParam(name = "meioIdentificacao") MeioIdentificacaoEnum meioIdentificacao,
            @WebParam(name = "situacao") SituacaoAcessoEnum situacao,
            @WebParam(name = "terminal") Integer terminal,
            @WebParam(name = "tipo") String tipo,
            @WebParam(name = "usuario") Integer usuario) throws Exception {

        return registrarAcessoInternal(codigo, dataAcesso, direcao, empresa, key,
                local, meioIdentificacao, situacao, terminal, tipo, usuario, null, "", null);

    }

    @WebMethod(operationName = "buscarPorCodigoAcesso")
    public RetornoRequisicaoBuscarCodigoAcesso buscarPorCodigoAcesso(
            @WebParam(name = "empresa") Integer empresa,
            @WebParam(name = "key") String key,
            @WebParam(name = "local") Integer local,
            @WebParam(name = "codigoAcesso") String codigoAcesso,
            @WebParam(name = "terminal") Integer terminal,
            @WebParam(name = "tipo") String tipo) throws Exception {

        AutorizacaoAcessoGrupoEmpresarialVO autorizacao = DaoAuxiliar.retornarAcessoControle(key).getAutorizacaoDao().consultarPorCodigo(null, null, null, null, null, codigoAcesso, null, null, Uteis.NIVELMONTARDADOS_DADOSBASICOS, null, null);
        RetornoRequisicaoBuscarCodigoAcesso retorno = new RetornoRequisicaoBuscarCodigoAcesso();
        retorno.setTerminal(terminal.toString());
        AcessoControle acessoControle = DaoAuxiliar.retornarAcessoControle(key);
        try {
            if (!UteisValidacao.emptyNumber(autorizacao.getCodigo())) {
                retorno.setCodigoAcesso(autorizacao.getCodigoAutorizacao());
                retorno.setNomeCliente(autorizacao.getNomePessoa());
            } else if (TipoAcessoEnum.TA_ALUNO.getId().equals(tipo)) {
                ClienteVO cliente = acessoControle.getClienteDao().consultarPorCodAcesso(codigoAcesso, false, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                if (cliente != null) {
                    cliente.setPessoa(acessoControle.getPessoaDao().consultarPorCodigo(cliente.getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));
                    retorno.setCodigoAcesso(cliente.getCodAcesso());
                    retorno.setNomeCliente(cliente.getPessoa().getNome());
                }
            } else if (TipoAcessoEnum.TA_COLABORADOR.getId().equals(tipo)) {
                ColaboradorVO colaborador = acessoControle.getColaboradorDao().consultarPorCodAcesso(codigoAcesso, 0, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                if (colaborador != null) {
                    colaborador.setPessoa(acessoControle.getPessoaDao().consultarPorCodigo(colaborador.getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));
                    retorno.setCodigoAcesso(colaborador.getCodAcesso());
                    retorno.setNomeCliente(colaborador.getPessoa().getNome());
                }
            }
        } catch (Exception e) {
            retorno.setResultado(ResultadoWSEnum.ERRO);
            retorno.setMsgErro("Método que ocorreu o erro: "
                    + "\"public RetornoRequisicaoBuscarCodigoAcesso buscarCodigoAcesso\""
                    + " Classe do Erro: " + e.getClass()
                    + " Mensagem Erro: " + e.getMessage());

        }
        return retorno;
    }

    @WebMethod(operationName = "buscarCodigoAcesso")
    public RetornoRequisicaoBuscarCodigoAcesso buscarCodigoAcesso(
            @WebParam(name = "empresa") Integer empresa,
            @WebParam(name = "key") String key,
            @WebParam(name = "local") Integer local,
            @WebParam(name = "matricula") String matricula,
            @WebParam(name = "terminal") Integer terminal,
            @WebParam(name = "tipo") String tipo) throws Exception {

        AutorizacaoAcessoGrupoEmpresarialVO autorizacao = DaoAuxiliar.retornarAcessoControle(key).getAutorizacaoDao().consultarPorCodigo(null, null, null, null, null, matricula, null, null, Uteis.NIVELMONTARDADOS_DADOSBASICOS, null, null);
        RetornoRequisicaoBuscarCodigoAcesso retorno = new RetornoRequisicaoBuscarCodigoAcesso();
        retorno.setTerminal(terminal.toString());
        AcessoControle acessoControle = DaoAuxiliar.retornarAcessoControle(key);
        try {
            if (!UteisValidacao.emptyNumber(autorizacao.getCodigo())) {
                retorno.setCodigoAcesso(autorizacao.getCodigoAutorizacao());
                retorno.setNomeCliente(autorizacao.getNomePessoa());
            } else if (TipoAcessoEnum.TA_ALUNO.getId().equals(tipo)) {
                ClienteVO cliente = acessoControle.getClienteDao().consultarPorMatricula(matricula, false, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                if (cliente != null) {
                    cliente.setPessoa(acessoControle.getPessoaDao().consultarPorCodigo(cliente.getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));
                    retorno.setCodigoAcesso(cliente.getCodAcesso());
                    retorno.setNomeCliente(cliente.getPessoa().getNome());
                    retorno.setBiometriaFacial(!UteisValidacao.emptyString(acessoControle.getPessoaDao().obterAssinaturaBiometriaFacial(cliente.getPessoa().getCodigo())));
                }
            } else if (TipoAcessoEnum.TA_COLABORADOR.getId().equals(tipo)) {
                ColaboradorVO colaborador = acessoControle.getColaboradorDao().consultarPorCodigo(Integer.valueOf(matricula), 0, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                if (colaborador != null) {
                    colaborador.setPessoa(acessoControle.getPessoaDao().consultarPorCodigo(colaborador.getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));
                    retorno.setCodigoAcesso(colaborador.getCodAcesso());
                    retorno.setNomeCliente(colaborador.getPessoa().getNome());
                    retorno.setBiometriaFacial(!UteisValidacao.emptyString(acessoControle.getPessoaDao().obterAssinaturaBiometriaFacial(colaborador.getPessoa().getCodigo())));
                }
            }
        } catch (Exception e) {
            retorno.setResultado(ResultadoWSEnum.ERRO);
            retorno.setMsgErro("Método que ocorreu o erro: "
                    + "\"public RetornoRequisicaoBuscarCodigoAcesso buscarCodigoAcesso\""
                    + " Classe do Erro: " + e.getClass()
                    + " Mensagem Erro: " + e.getMessage());

        }
        return retorno;
    }

    /**
     * @author: Ulisses Data: 22/12/10 Objetivo do método: Pesquisar clientes
     * pelo nome. Tipo de Retorno : Retorna uma "String", cujo conteúdo é um
     * arquivo XML no formato DataPacket, com a relação dos clientes
     * consultados.
     */
    @WebMethod(operationName = "consultarClientesPeloNome")
    public RetornoRequisicaoConsultarClientes consultarClientesPeloNome(
            @WebParam(name = "idEmpresa") Integer idEmpresa,
            @WebParam(name = "key") String key,
            @WebParam(name = "nomePesquisar") String nomePesquisar) throws Exception {
        return consultarClientesPeloNomeGeral(idEmpresa, key, nomePesquisar, false);
    }

    @WebMethod(operationName = "consultarClientesPeloNomeAutorizacaoCobranca")
    public RetornoRequisicaoConsultarClientes consultarClientesPeloNomeAutorizacaoCobranca(
            @WebParam(name = "idEmpresa") Integer idEmpresa,
            @WebParam(name = "key") String key,
            @WebParam(name = "nomePesquisar") String nomePesquisar) throws Exception {
        RetornoRequisicaoConsultarClientes retorno = new RetornoRequisicaoConsultarClientes();
        try {
            AcessoControle acessoControle = DaoAuxiliar.retornarAcessoControle(key);
            List<ClienteVO> listaClientes = acessoControle.getClienteDao().consultarPorNomeClienteComAutorizacao(nomePesquisar, 0, false, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA, 50);
            retorno.setClientesXML((new ClienteXML()).criarArquivoXMLDataPacket(listaClientes, true));
        } catch (Exception e) {
            retorno.setResultado(ResultadoWSEnum.ERRO);
            retorno.setMsgErro("Método que ocorreu o erro: "
                    + "\"public RetornoRequisicaoConsultarClientes consultarClientesPeloNome\""
                    + " Classe do Erro: " + e.getClass()
                    + " Mensagem Erro: " + e.getMessage());

        }
        return retorno;
    }

    private RetornoRequisicaoConsultarClientes consultarClientesPeloNomeGeral(Integer idEmpresa, String key, String nomePesquisar, boolean comAutorizacaoCobranca) {
        RetornoRequisicaoConsultarClientes retorno = new RetornoRequisicaoConsultarClientes();
        try {
            AcessoControle acessoControle = DaoAuxiliar.retornarAcessoControle(key);
            List<ClienteVO> listaClientes = acessoControle.consultarClientesPeloNome(nomePesquisar, 0, 50);
            listaClientes.addAll(acessoControle.getAutorizacaoDao().consultarPorNomeMock(nomePesquisar));
            retorno.setClientesXML((new ClienteXML()).criarArquivoXMLDataPacket(listaClientes, comAutorizacaoCobranca));
        } catch (Exception e) {
            retorno.setResultado(ResultadoWSEnum.ERRO);
            retorno.setMsgErro("Método que ocorreu o erro: "
                    + "\"public RetornoRequisicaoConsultarClientes consultarClientesPeloNome\""
                    + " Classe do Erro: " + e.getClass()
                    + " Mensagem Erro: " + e.getMessage());

        }
        return retorno;
    }

    @WebMethod(operationName = "consultarColaboradoresPeloNome")
    public RetornoRequisicaoConsultarColaboradores consultarColaboradoresPeloNome(
            @WebParam(name = "idEmpresa") Integer idEmpresa,
            @WebParam(name = "key") String key,
            @WebParam(name = "nomePesquisar") String nomePesquisar,
            @WebParam(name = "consultarTerceirizado") Boolean consultarTerceirizado) throws Exception {
        RetornoRequisicaoConsultarColaboradores retorno = new RetornoRequisicaoConsultarColaboradores();
        try {
            AcessoControle acessoControle = DaoAuxiliar.retornarAcessoControle(key);

            List<ColaboradorVO> listaColaboradores;
            if (consultarTerceirizado) {
                listaColaboradores = acessoControle.getColaboradorDao().consultarPorNomeTipoColaborador(nomePesquisar,
                        idEmpresa, false, false, Uteis.NIVELMONTARDADOS_CONSULTA_WS,
                        TipoColaboradorEnum.TERCEIRIZADO, TipoColaboradorEnum.FORNECEDOR);
            } else {
                listaColaboradores = acessoControle.getColaboradorDao().consultarPorNomeTipoColaborador(nomePesquisar,
                        idEmpresa, false, false, Uteis.NIVELMONTARDADOS_CONSULTA_WS,
                        TipoColaboradorEnum.PROFESSOR, TipoColaboradorEnum.PROFESSOR_TREINO, TipoColaboradorEnum.PERSONAL_TRAINER,
                        TipoColaboradorEnum.ORIENTADOR, TipoColaboradorEnum.CONSULTOR, TipoColaboradorEnum.PERSONAL_INTERNO,
                        TipoColaboradorEnum.PERSONAL_EXTERNO, TipoColaboradorEnum.ESTUDIO, TipoColaboradorEnum.COORDENADOR,
                        TipoColaboradorEnum.MEDICO, TipoColaboradorEnum.FUNCIONARIO, TipoColaboradorEnum.ADMINISTRADOR);
            }

            List<String> codAcessoIncluidos = new ArrayList<String>();
            List<ColaboradorVO> listaRetornar = new ArrayList<ColaboradorVO>();
            for (ColaboradorVO colaboradorVO : listaColaboradores) {
                if (!codAcessoIncluidos.contains(colaboradorVO.getCodAcesso())) {
                    codAcessoIncluidos.add(colaboradorVO.getCodAcesso());
                    listaRetornar.add(colaboradorVO);
                }
            }

            retorno.setColaboradoresXML((new ColaboradorXML()).criarArquivoXMLDataPacket(listaRetornar));
        } catch (Exception e) {
            retorno.setResultado(ResultadoWSEnum.ERRO);
            retorno.setMsgErro("Método que ocorreu o erro: "
                    + "\"public RetornoRequisicaoConsultarColaboradores consultarColaboradoresPeloNome\""
                    + " Classe do Erro: " + e.getClass()
                    + " Mensagem Erro: " + e.getMessage());

        }
        return retorno;

    }

    /**
     * @author: Ulisses Data: 29/12/10 Objetivo do método: Registrar o acesso
     * rápido de visitantes, terceiros, alunos que esqueceram o número de sua
     * matrícula etc... Tipo de Retorno : RetornoRequisicaoLiberacaoAcesso
     *
     */
    @Deprecated
    @WebMethod(operationName = "registrarLiberacaoAcesso")
    public RetornoRequisicaoLiberacaoAcesso registrarLiberacaoAcesso(
            @WebParam(name = "dataAcesso") Date dataAcesso,
            @WebParam(name = "direcao") DirecaoAcessoEnum direcao,
            @WebParam(name = "empresa") Integer empresa,
            @WebParam(name = "key") String key,
            @WebParam(name = "local") Integer local,
            @WebParam(name = "terminal") Integer terminal,
            @WebParam(name = "tipoLiberacao") TipoLiberacaoEnum tipoLiberacao,
            @WebParam(name = "usuario") Integer usuario) throws Exception {

        return ValidacaoAcessoService.liberacaoAcesso(dataAcesso, direcao, empresa, key, local, terminal, tipoLiberacao, usuario, null, "", null);
    }

    @WebMethod(operationName = "getVersaoSistema")
    public String getVersaoSistema() {
        return PropsService.getPropertyValue(PropsService.VERSAO_SISTEMA);
    }

    /**
     * @author: Waller Maciel
     * @since 14/02/11 Resetar o HashMap de controladores AcessoControle, para
     * que novas configuracoes sejam aceitas.
     *
     */
    @WebMethod
    public void resetMapaControladores() {
        DaoAuxiliar.resetMapaControladores();
    }

    /*Author: XiquiN
     *Data: 02/12/11
     *Objetivo: Limpar a lista de locais de acesso para onde a foto do cliente já foi enviada.
     Dessa forma, a foto é enviada novamente.
     */
    @WebMethod(operationName = "excluirPessoaFotoLocalAcesso")
    public void excluirPessoaFotoLocalAcesso(
            @WebParam(name = "key") String key,
            @WebParam(name = "localAcesso") Integer localAcesso,
            @WebParam(name = "pessoa") Integer pessoa,
            @WebParam(name = "codigoAutorizacao") String codigoAutorizacao) throws Exception {

        if (codigoAutorizacao == null || codigoAutorizacao.equals("")) {
            AcessoControle acessoControle = DaoAuxiliar.retornarAcessoControle(key);
            acessoControle.getPessoaFotoLocalAcessoDao().excluirFotoPessoaLocalAcesso(localAcesso, pessoa);
        } else {
            AutorizacaoAcessoGrupoEmpresarialVO autorizacao = DaoAuxiliar.retornarAcessoControle(key).getAutorizacaoDao().consultarPorCodigo(null, null, null, null, null, codigoAutorizacao, null, null, Uteis.NIVELMONTARDADOS_DADOSBASICOS, null, null);
            if (!UteisValidacao.emptyNumber(autorizacao.getCodigo())) {
                ValidacaoAcessoWSConsumer.excluirPessoaFotoLocalAcesso(autorizacao, autorizacao.getIntegracao().getLocalAcesso(), autorizacao.getIntegracao().getChave(), autorizacao.getIntegracao().getUrlZillyonWeb());
            }
        }

    }

    /*Author: XiquiN
     *Data: 17/12/11
     *Objetivo: Enviar a lista das pessoas que acessaram nos últimos 3 meses e que têm foto.
     */
    @WebMethod(operationName = "montarListaAcessosPessoasComFoto")
    public String[] montarListaAcessosPessoasComFoto(
            @WebParam(name = "key") String key,
            @WebParam(name = "localAcesso") Integer localAcesso) throws Exception {
        AcessoControle acessoControle = DaoAuxiliar.retornarAcessoControle(key);
        return acessoControle.montarListaAcessosPessoasComFoto(localAcesso);
    }

    /*Author: XiquiN
     *Data: 17/12/11
     *Objetivo: Enviar a foto da pessoa informada. Ao enviar, é gravado um registro na tabela pessoaFotoLocalAcesso
     */
    @WebMethod(operationName = "pegarFotoPessoa")
    public byte[] pegarFotoPessoa(
            @WebParam(name = "key") String key,
            @WebParam(name = "localAcesso") Integer localAcesso,
            @WebParam(name = "pessoa") Integer pessoa) throws Exception {

        return AcessoPerfilClienteService.pegarFotoPessoa(key, localAcesso, pessoa);
    }

    /*Author: XiquiN
     *Data: 02/05/12
     *Objetivo: Gravar em arquivo o que for recebido
     */
    @WebMethod(operationName = "gravarArquivoLocal")
    public void gravarArquivoLocal(
            @WebParam(name = "conteudo") byte[] conteudo,
            @WebParam(name = "key") String key,
            @WebParam(name = "nome") String nome) throws Exception {

        try {
            String dir = PropsService.getPropertyValue(PropsService.pathLogsZAcesso);
            nome = dir + '/' + key + '_' + nome;

            File temp = new File(dir);
            if (!temp.exists()) {
                temp.mkdirs();
            }
            temp = new File(nome);
            if (temp.exists()) {
                temp.delete();
            }

            FileOutputStream file = new FileOutputStream(nome, false);
            file.write(conteudo);
            file.close();

            Uteis.logar(null, "Arquivo gravado: " + nome + " - "
                    + Formatador.formatarValorNumerico(Double.valueOf(conteudo.length) / 1024, ",##0.00")
                    + " kb");
        } catch (Exception erro) {
            Uteis.logar(null, "Erro gravando arquivo "
                    + nome + ": " + erro.getMessage());
        }
    }

    @WebMethod(operationName = "registrarDownloadBaseOffline")
    public void registrarDownloadBaseOffline(@WebParam(name = "key") String key,
                                             @WebParam(name = "localAcesso") Integer localAcesso) {
        try {
            AcessoControle acessoControle = DaoAuxiliar.retornarAcessoControle(key);
            acessoControle.getLocalAcessoDao().alterarDataDownloadBase(localAcesso, Calendario.hoje());
        } catch (Exception e) {
            Uteis.logar(null, e.toString());
        }
    }
    @WebMethod(operationName = "registrarVersaoAcesso")
    public void registrarVersaoAcesso(@WebParam(name = "key") String key,
                                      @WebParam(name = "localAcesso") Integer localAcesso,@WebParam(name= "versao") String versao,@WebParam(name= "parametros") String parametros) {
        try {
            AcessoControle acessoControle = DaoAuxiliar.retornarAcessoControle(key);
            acessoControle.getLocalAcessoDao().alterarVersaoAcesso(localAcesso, versao,parametros);
        } catch (Exception e) {
            Uteis.logar(null, e.toString());
        }
    }
    @WebMethod(operationName = "montarDadosOffline")
    public RetornoRequisicaoDadosOffline montarDadosOffline(
            @WebParam(name = "key") String key,
            @WebParam(name = "localAcesso") Integer localAcesso) {

        return AcessoOfflineService.montarDadosOffline(key, localAcesso);
    }

    @WebMethod(operationName = "montarDadosOfflinePessoa")
    public RetornoRequisicaoDadosOffline montarDadosOfflinePessoa(
            @WebParam(name = "key") String key,
            @WebParam(name = "localAcesso") Integer localAcesso,
            @WebParam(name = "codigoPessoa") Integer codigoPessoa) {

        return AcessoOfflineService.montarDadosOfflinePessoa(key, localAcesso, codigoPessoa);
    }


    @WebMethod(operationName = "temDadosOfflineGerados")
    public Boolean temDadosOfflineGerados(
            @WebParam(name = "key") String key,
            @WebParam(name = "localAcesso") Integer localAcesso) {
        try {
            AcessoControle ac = DaoAuxiliar.retornarAcessoControle(key);
            //verifica se o Local possui "UtilizarModoOffLine" marcado
            if (ac.getLocalAcessoDao().utilizaModoOffLine(localAcesso)) {
                boolean temDadosOffLineHoje = ac.getLocalAcessoDao().temDadosOffLine(localAcesso);
                Uteis.logar(null, "Tem aquivo novo off-line para " + key + " -> " + temDadosOffLineHoje);
                return temDadosOffLineHoje;
            } else {
                return false;
            }
        } catch (Exception ex) {
            Logger.getLogger(ValidacaoAcessoWS.class.getName()).log(Level.SEVERE, null, ex);
        }
        return false;
    }

    @WebMethod(operationName = "ultimoDadosOfflineGerado")
    public String ultimoDadosOfflineGerado(
            @WebParam(name = "key") String key,
            @WebParam(name = "localAcesso") Integer localAcesso) {
        try {
            AcessoControle ac = DaoAuxiliar.retornarAcessoControle(key);
            //verifica se o Local possui "UtilizarModoOffLine" marcado
            if (ac.getLocalAcessoDao().utilizaModoOffLine(localAcesso)) {
                Date ultimaAtualizacao = ac.getLocalAcessoDao().ultimoDadosOffLineGerado(localAcesso);
                if (ultimaAtualizacao != null) {
                    final String data = Calendario.getData(ultimaAtualizacao, "yyyy-MM-dd HH:mm:ss");
                    final String msg = String.format("Tem aquivo novo off-line para %s com data %s ", key, data);
                    Uteis.logar(null, msg);
                    return data;
                }
            }
        } catch (Exception ex) {
            Logger.getLogger(ValidacaoAcessoWS.class.getName()).log(Level.SEVERE, null, ex);
        }
        return "";
    }

    @WebMethod(operationName = "solicitarUtilitario")
    public String solicitarUtilitario(
            @WebParam(name = "nome") String nome) throws Exception {

        StringBuilder ret = new StringBuilder();

        // Por algum motivo se não criptogravar no ZAW o nome não chega direito
        // Então por enquanto estamos usando a criptografia
        nome = Uteis.desencriptarRetornoZAWOffline(nome);
        String dir = PropsService.getPropertyValue(PropsService.pathUtilsZAcesso) + "/";
        File io = new File(dir + nome);
        if (!io.exists()) {
            throw new IOException("Utilitário não encontrado");
        }

        ret.append("Usuario=").append(PropsService.getPropertyValue(PropsService.usuarioHTTPAcesso)).append("\n");
        ret.append("Senha=").append(PropsService.getPropertyValue(PropsService.senhaHTTPAcesso)).append("\n");
        ret.append("URL=").append(PropsService.getPropertyValue(PropsService.urlUtilsZAcesso)).
                append("/").append(nome);
        return Uteis.encriptarRetornoZAWOffline(ret.toString());
    }

    @WebMethod(operationName = "consultarOperacoesPendentes")
    public String consultarOperacoesPendentes(
            @WebParam(name = "key") String key,
            @WebParam(name = "localAcesso") Integer localAcesso) {
        //Tratar notificações agendadas para os Concentradores Desktops
//        Uteis.logar("============ consultarOperacoesPendentes ENTROU =============");
        List<NotificacaoTO> notfs = NotificadorServiceControle.obterListaNotificacoesPorTipo("WS");
        String operacoes = "";
        for (NotificacaoTO notf : notfs) {
            if (notf.isAtiva()) {
                if (notf.getChave() != null && !notf.getChave().isEmpty() && notf.getChave().equals(key)) {//chave especifica
                    if (notf.getLocalAcesso() != null && !notf.getLocalAcesso().isEmpty() && notf.getLocalAcesso().equals(
                            localAcesso.toString())) {//local de acesso especifico, dessa chave, dessa empresa
                        operacoes += notf.getDescricaoPlusID();
                        notf.setLida(true);
                    } else if (notf.getLocalAcesso() == null || notf.getLocalAcesso().isEmpty()) {//qualquer local de acesso dessa chave, dessa empresa
                        operacoes += notf.getDescricaoPlusID();
                        notf.setLida(true);
                    }
                } else if (notf.getChave() == null || notf.getChave().isEmpty()) {//qualquer chave, qualquer empresa
                    operacoes += notf.getDescricaoPlusID();
                    notf.setLida(true);
                }
            }
        }
//        Uteis.logar("============ consultarOperacoesPendentes RETORNO:  ==========");
//        Uteis.logar(operacoes);
//        Uteis.logar("============ consultarOperacoesPendentes FIM ===============");
        return operacoes;
    }


    @WebMethod(operationName = "registrarCodigoAcessoAlternativo")
    public ResultadoWS registrarCodigoAcessoAlternativo(@WebParam(name = "key") String key,
                                                 @WebParam(name = "codigoAcesso") String codigoAcesso,
                                                 @WebParam(name = "codigoAcessoAlternativo") String codigoAcessoAlternativo) {

        ResultadoWS result = new ResultadoWS();
        try {
            AcessoControle acessoControle = DaoAuxiliar.retornarAcessoControle(key);
            acessoControle.getClienteDao().alterarCodigoAcessoAlternativo(codigoAcesso, codigoAcessoAlternativo);

            result.setMensagem("Assinatura atualizada");
            result.setSucesso(true);
        } catch (Exception e) {
            result.setSucesso(false);
            result.setMensagem("Houve um erro ao gravar o código de acesso alternativo");

            Uteis.logar(null, e.toString());
        }
        return result;
    }

    @WebMethod(operationName = "registrarCodigoPossuiMaisDeumaDigital")
    public void registrarCodigoPossuiMaisDeumaDigital(@WebParam(name = "key") String key,
                                                      @WebParam(name = "codigoAcesso") String codigoAcesso) {

        try {
            AcessoControle acessoControle = DaoAuxiliar.retornarAcessoControle(key);
            acessoControle.getClienteDao().alterarSomenteTemMaisDeUmaDigital(codigoAcesso, true);
        } catch (Exception e) {
            Uteis.logar(null, e.toString());
        }
    }

    @WebMethod(operationName = "registrarCodigoNaoPossuiMaisDeumaDigital")
    public void registrarCodigoNaoPossuiMaisDeumaDigital(@WebParam(name = "key") String key,
                                                         @WebParam(name = "codigoAcesso") String codigoAcesso) {

        try {
            AcessoControle acessoControle = DaoAuxiliar.retornarAcessoControle(key);
            acessoControle.getClienteDao().alterarSomenteTemMaisDeUmaDigital(codigoAcesso, false);
        } catch (Exception e) {
            Uteis.logar(null, e.toString());
        }
    }

    /**
     * Será verificado se os clientes estão inativos (Inativo ou Visitante) há pelo menos 90 dias;
     */
    @WebMethod(operationName = "validarSePodeExcluirDigital")
    public Boolean validarSePodeExcluirDigital(@WebParam(name = "key") String key,
                                               @WebParam(name = "codigoAcesso") String codigoAcesso) {
        return BiometriaDigitalService.validarSePodeExcluirDigital(key, codigoAcesso);
    }

    @WebMethod(operationName = "buscarConfigIntegracaoAcesso")
    public RetornoRequisicaoBuscarIntegracoes buscarConfigIntegracaoAcesso(
            @WebParam(name = "empresa") Integer empresa,
            @WebParam(name = "key") String key) {

       return AcessoConfigService.buscarConfigIntegracaoAcesso(empresa, key);
    }

    @WebMethod(operationName = "gravarSenhaIntegracao")
    public RetornoRequisicaoWS gravarSenhaIntegracao(
            @WebParam(name = "key") String key,
            @WebParam(name = "codigoAcesso") String codigoAcesso,
            @WebParam(name = "senha") String senha) throws Exception {

        return  AcessoConfigService.gravarSenhaIntegracao(key, codigoAcesso, senha);
    }

    @WebMethod(operationName = "gravarSenhaIntegracaoEncriptada")
    public RetornoRequisicaoWS gravarSenhaIntegracaoEncriptada(
            @WebParam(name = "key") String key,
            @WebParam(name = "codigoAcesso") String codigoAcesso,
            @WebParam(name = "senhaEncriptada") String senhaEncriptada) throws Exception {

        return AcessoOfflineService.gravarSenhaIntegracaoEncriptada(key,codigoAcesso, senhaEncriptada);
    }

    @WebMethod(operationName = "registrarLiberacaoAcessoComJustificativa")
    public RetornoRequisicaoLiberacaoAcesso registrarLiberacaoAcessoComJustificativa(
            @WebParam(name = "dataAcesso") Date dataAcesso,
            @WebParam(name = "direcao") DirecaoAcessoEnum direcao,
            @WebParam(name = "empresa") Integer empresa,
            @WebParam(name = "key") String key,
            @WebParam(name = "local") Integer local,
            @WebParam(name = "terminal") Integer terminal,
            @WebParam(name = "tipoLiberacao") TipoLiberacaoEnum tipoLiberacao,
            @WebParam(name = "usuario") Integer usuario,
            @WebParam(name = "codigoAcesso") String codigoAcesso,
            @WebParam(name = "justificativa") String justificativa,
            @WebParam(name = "nomeGenerico") String nomeGenerico) throws Exception {

        return ValidacaoAcessoService.liberacaoAcesso(dataAcesso, direcao, empresa, key, local, terminal, tipoLiberacao, usuario, codigoAcesso, justificativa, nomeGenerico);
    }

    @WebMethod(operationName = "registrarIpLocalAcesso")
    public void registrarIpLocalAcesso(@WebParam(name = "key") String key,
                                       @WebParam(name = "localAcesso") Integer localAcesso,
                                       @WebParam(name = "ip") String ip) {

        AcessoConfigService.registrarIpLocalAcesso(key, localAcesso, ip);
    }

    @WebMethod(operationName = "obterIpLocalAcesso")
    public String obterIpLocalAcesso(@WebParam(name = "key") String key,
                                     @WebParam(name = "categoria") String categoria) {
        JSONObject retorno = new JSONObject();
        try {
            AcessoControle acessoControle = DaoAuxiliar.retornarAcessoControle(key);
            retorno.put("sucesso", acessoControle.getLocalAcessoDao().obterIp(categoria));
        } catch (Exception e) {
            Uteis.logar(null, e.toString());
            retorno.put("erro", e.getMessage());
        }
        return retorno.toString();
    }

    @WebMethod
    public String atualizarFotoPerfilAluno(
            @WebParam(name = "key") String key,
            @WebParam(name = "codigoAcesso") String codigoAcesso,
            @WebParam(name = "imagem") String foto) {

        return AcessoPerfilClienteService.atualizarFotoPerfilAluno(key, codigoAcesso, foto);
    }

    private void gravarAssinaturaBiometriaDigitalColaborador(ColaboradorVO colaborador, ResultadoWS resultadoWS,
                                                             AcessoControle acessoControle, String assinatura, String codigoAcesso) throws Exception {
        if(colaborador == null){
            resultadoWS.setMensagem("O colaborador com código de acesso "+ codigoAcesso +" não foi encontrado");
            resultadoWS.setSucesso(false);
        }else{
            acessoControle.getPessoaDao().atualizarAssinaturaBiometriaDigital(colaborador.getPessoa().getCodigo(), assinatura);
            resultadoWS.setMensagem("Assinatura digital do colaborador atualizada");
            resultadoWS.setSucesso(true);
        }
    }

    @WebMethod(operationName = "atualizarAssinaturaBiometriaDigitalPorCodAcesso")
    public ResultadoWS atualizarAssinaturaBiometriaDigitalPorCodAcesso(
            @WebParam(name = "key") String key,
            @WebParam(name = "codigoAcesso") String codigoAcesso,
            @WebParam(name = "assinatura") String assinatura) throws Exception {

       return BiometriaDigitalService.atualizarAssinaturaBiometriaDigitalPorCodAcesso(key, codigoAcesso, assinatura);
    }

    @WebMethod(operationName = "obterAssinaturaBiometriaDigitalPorCodAcesso")
    public ResultadoWS obterAssinaturaBiometriaDigitalPorCodAcesso(
            @WebParam(name = "key") String key,
            @WebParam(name = "codigoAcesso") String codigoAcesso) throws Exception {

       return BiometriaDigitalService.obterAssinaturaBiometriaDigitalPorCodAcesso(key, codigoAcesso);
    }


    @WebMethod(operationName = "obterCodigoPessoaPorCodigoAcesso")
    public ResultadoWS obterCodigoPessoaPorCodigoAcesso(
        @WebParam(name = "key") String key,
        @WebParam(name = "codigoAcesso") String codigoAcesso
    ) throws Exception {

        return AcessoPessoaService.obterCodigoPessoaPorCodigoAcesso(key, codigoAcesso);
    }

    @WebMethod(operationName = "inserirAutorizacaoAcesso")
    public ResultadoWS inserirAutorizacaoAcesso(
            @WebParam(name = "key") String key,
            @WebParam(name = "codAcesso") String codAcesso,
            @WebParam(name = "codAcessoAlternativo") String codAcessoAlternativo,
            @WebParam(name = "nome") String nome,
            @WebParam(name = "codPessoa") Integer codPessoa,
            @WebParam(name = "matricula") String matricula,
            @WebParam(name = "senhaAcesso") String senhaAcesso,
            @WebParam(name = "tipoPessoa") String tipoPessoa,
            @WebParam(name = "chaveRemota") String chaveRemota,
            @WebParam(name = "codEmpresaRemota") Integer codEmpresaRemota,
            @WebParam(name = "codEmpresaLocal") Integer codEmpresaLocal
    ) throws Exception {

        return AutorizacaoAcessoService.inserirAutorizacaoAcesso(
                key,
                codAcesso,
                codAcessoAlternativo,
                nome,
                codPessoa,
                matricula,
                senhaAcesso,
                tipoPessoa,
                chaveRemota,
                codEmpresaRemota,
                codEmpresaLocal);
    }

    @WebMethod(operationName = "obterInformacoesAcessoAlunoPorCPF")
    public RetornoRequisicaoInformacoesAcessoAluno obterInformacoesAcessoAlunoPorCPF(
            @WebParam(name = "key") String key,
            @WebParam(name = "cpf") String cpf
    ) throws Exception {

        return AcessoPessoaService.obterInformacoesAcessoAlunoPorCPF(key, cpf);
    }

    @WebMethod(operationName = "obterCodigoAcessoPorCPF")
    public ResultadoWS obterCodigoAcessoPorCPF(
            @WebParam(name = "key") String key,
            @WebParam(name = "cpf") String cpf
    ) throws Exception {

        return AcessoPessoaService.obterCodigoAcessoPorCPF(key, cpf);
    }

    @Deprecated
    @WebMethod(operationName = "atualizarAssinaturaBiometriaDigital")
    public ResultadoWS atualizarAssinaturaBiometriaDigital(
            @WebParam(name = "key") String key,
            @WebParam(name = "matricula") String matricula,
            @WebParam(name = "assinatura") String assinatura) throws Exception {

        return BiometriaDigitalService.atualizarAssinaturaBiometriaDigital(key, matricula, assinatura);
    }

    @Deprecated
    @WebMethod(operationName = "obterAssinaturaBiometriaDigital")
    public ResultadoWS obterAssinaturaBiometriaDigital(
            @WebParam(name = "key") String key,
            @WebParam(name = "matricula") String matricula) {

        return BiometriaDigitalService.obterAssinaturaBiometriaDigital(key, matricula);
    }

    @WebMethod(operationName = "atualizarAssinaturaBiometriaFacial")
    public ResultadoWS atualizarAssinaturaBiometriaFacial(
            @WebParam(name = "key") String key,
            @WebParam(name = "codigoAcesso") String codigoAcesso,
            @WebParam(name = "assinatura") String assinatura) {

        return BiometriaFacialService.atualizarAssinaturaBiometriaFacial(key, codigoAcesso, assinatura);
    }

    @WebMethod(operationName = "obterAssinaturaBiometriaFacial")
    public ResultadoWS obterAssinaturaBiometriaFacial(
            @WebParam(name = "key") String key,
            @WebParam(name = "codigoAcesso") String codigoAcesso) {

        return BiometriaFacialService.obterAssinaturaBiometriaFacial(key, codigoAcesso);
    }

    @WebMethod(operationName = "atualizarAssinaturaBiometriaDigitalAutorizacao")
    public ResultadoWS atualizarAssinaturaBiometriaDigitalAutorizacao(
            @WebParam(name = "key") String key,
            @WebParam(name = "codigoAutorizacao") Integer codigoAutorizacao,
            @WebParam(name = "assinatura") String assinatura) {

        return BiometriaDigitalService.atualizarAssinaturaBiometriaDigitalAutorizacao(key, codigoAutorizacao, assinatura);
    }
}

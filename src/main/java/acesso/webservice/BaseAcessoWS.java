/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package acesso.webservice;

import acesso.webservice.retorno.ResultadoWSEnum;
import acesso.webservice.retorno.RetornoRequisicaoRegistrarAcesso;
import negocio.comuns.acesso.AutorizacaoAcessoGrupoEmpresarialVO;
import negocio.comuns.acesso.IntegracaoAcessoGrupoEmpresarialVO;
import negocio.comuns.acesso.enumerador.DirecaoAcessoEnum;
import negocio.comuns.acesso.enumerador.MeioIdentificacaoEnum;
import negocio.comuns.acesso.enumerador.SituacaoAcessoEnum;
import negocio.comuns.acesso.enumerador.TipoAcessoEnum;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.enumerador.TipoPessoaEnum;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.oamd.RedeEmpresaVO;
import servicos.impl.microsservice.acessosistema.AcessoSistemaMSService;
import servicos.integracao.RegistrarAcessoConsumer;
import servicos.integracao.ValidacaoAcessoWSConsumer;
import servicos.jms.zaw.beans.RegistroAcessoJMSBean;
import servicos.oamd.RedeEmpresaService;

import javax.jms.Connection;
import javax.jms.ConnectionFactory;
import javax.jms.JMSException;
import javax.jms.Message;
import javax.jms.MessageProducer;
import javax.jms.Queue;
import javax.jms.Session;
import java.util.Date;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 *
 * <AUTHOR>
 */
public class BaseAcessoWS {

    private Queue zawQueue;
    private ConnectionFactory zawQueueFactory;
    private boolean isQueuePresent = false;

    public BaseAcessoWS() {
//        try {
//            if (PropsService.getPropertyValue(PropsService.enableZawQueue).equals("true")
//                    && zawQueue == null && zawQueueFactory == null) {
//                zawQueueFactory = InitialContext.doLookup("JMS_ZAW_QueueFactory");
//                zawQueue = InitialContext.doLookup("JMS_ZAW_Queue");
//                isQueuePresent = true;
//                Uteis.logar(null, "Lookups em zawQueue Realizados com sucesso!");
//            }
//        } catch (NamingException ex) {
//            Logger.getLogger(ValidacaoAcessoWS.class.getName()).log(Level.SEVERE, null, ex);
//        }
    }

    private Message createJMSMessageForjmsZawQueue(Session session, RegistroAcessoJMSBean registro) throws JMSException {
        // TODO create and populate message to send
        return session.createObjectMessage(registro);
    }

    public void enfileirarRegistro(String key,
            Date dataAcesso, String tipo, Integer codigo,
            Integer empresa, Integer local, Integer terminal,
            DirecaoAcessoEnum direcao, SituacaoAcessoEnum situacao, Integer usuario,
            MeioIdentificacaoEnum meioIdentificacao) throws JMSException {
        RegistroAcessoJMSBean registro = new RegistroAcessoJMSBean(
                key, dataAcesso, tipo, codigo, empresa, local, terminal, direcao, situacao, usuario, meioIdentificacao);
        sendJMSMessageToZawQueue(registro);
    }

    private void sendJMSMessageToZawQueue(RegistroAcessoJMSBean registro) throws JMSException {
        Connection connection = null;
        Session session = null;
        try {
            Uteis.logar(null, "Vou enfileirar para ZAWQueue -> " + registro.toString());
            connection = zawQueueFactory.createConnection();
            session = connection.createSession(false, Session.AUTO_ACKNOWLEDGE);
            MessageProducer messageProducer = session.createProducer(zawQueue);
            messageProducer.send(createJMSMessageForjmsZawQueue(session, registro));
            Uteis.logar(null, "Enfileirado para ZAWQueue -> " + registro.toString());
        } finally {
            if (session != null) {
                try {
                    session.close();
                } catch (JMSException e) {
                    Logger.getLogger(this.getClass().getName()).log(Level.WARNING, "Cannot close session", e);
                }
            }
            if (connection != null) {
                connection.close();
            }
        }
    }

    public boolean isIsQueuePresent() {
        return isQueuePresent;
    }

    public RetornoRequisicaoRegistrarAcesso registrarAcessoInternal(
            Integer codigo,
            Date dataAcesso,
            DirecaoAcessoEnum direcao,
            Integer empresa,
            String key,
            Integer local,
            MeioIdentificacaoEnum meioIdentificacao,
            SituacaoAcessoEnum situacao,
            Integer terminal,
            String tipo,
            Integer usuario,
            String codAcessoIntegracao,
            String nomeCodEmpresaAcessou,
            Integer codigoMatricula) {
        RetornoRequisicaoRegistrarAcesso retorno = new RetornoRequisicaoRegistrarAcesso();
        try {
            if (tipo.equals(TipoAcessoEnum.AUTORIZADO.getId())) {
                if (!UteisValidacao.emptyString(codAcessoIntegracao)) {
                    String identificadorOutraEmpresa = codAcessoIntegracao.substring(0, 4);
                    IntegracaoAcessoGrupoEmpresarialVO integracao = DaoAuxiliar.retornarAcessoControle(key).getIntegracaoDao().consultarPorCodigoChaveIdentificacao(identificadorOutraEmpresa, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);

                    AutorizacaoAcessoGrupoEmpresarialVO autorizacao = new AutorizacaoAcessoGrupoEmpresarialVO();
                    autorizacao.setTipoPessoa(TipoPessoaEnum.ALUNO.getTipo());
                    autorizacao.setCodigoGenerico(codigo);

                    retorno = RegistrarAcessoConsumer.registrarAcesso(autorizacao, direcao, situacao, usuario, meioIdentificacao, integracao, false, "", false);
                } else {

                    String nomeCodEmpresaRedeAcessou = "";
                    AutorizacaoAcessoGrupoEmpresarialVO autorizacao;
                    RedeEmpresaVO redeEmpresa = RedeEmpresaService.obterRedePorChave(key);
                    if (redeEmpresa != null && redeEmpresa.getGestaoRedes()) {
                        try {
                            EmpresaVO emp = DaoAuxiliar.retornarAcessoControle(key).getEmpresaDao().consultarPorCodigo(empresa, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                            nomeCodEmpresaRedeAcessou = emp.getCodigo() + " - " + emp.getNome();
                        }catch (Exception ignore){}
                        autorizacao = AcessoSistemaMSService.findById(codigo, redeEmpresa);
                    } else {
                        autorizacao = DaoAuxiliar.retornarAcessoControle(key).getAutorizacaoDao().consultarPorCodigo(codigo, null, null, null, null, null, null, null, Uteis.NIVELMONTARDADOS_DADOSBASICOS, null, null);
                    }

                    if (UteisValidacao.emptyNumber(autorizacao.getCodigo())) {
                        Logger.getLogger(BaseAcessoWS.class.getName()).log(Level.SEVERE, "Não foi possível localizar "
                                + "a autoriza\u00e7\u00e3o de acesso de c\u00f3digo {0}", codigo);
                    } else {
                        // registrar primeiro o acesso do aluno autorizado no local acesso atual
                        if (!UteisValidacao.emptyNumber(autorizacao.getCodigo()) &&
                                autorizacao.getIntegracao() != null && !UteisValidacao.emptyNumber(autorizacao.getIntegracao().getCodigo())) {
                            String tipoRegistrar;
                            if (autorizacao.getTipoPessoa().equals(TipoPessoaEnum.ALUNO.getTipo())) {
                                tipoRegistrar = TipoAcessoEnum.TA_ALUNO.getId();
                                DaoAuxiliar.retornarAcessoControle(key).registrarAcesso(dataAcesso, tipoRegistrar, 0, situacao, direcao, local, terminal, usuario, meioIdentificacao, key, empresa, nomeCodEmpresaRedeAcessou, autorizacao, null);
                            } else {
                                tipoRegistrar = TipoAcessoEnum.TA_COLABORADOR.getId();
                            }
                        }

                        //registrar na unidade de origem informando unidade que acessou
                        retorno = RegistrarAcessoConsumer.registrarAcesso(autorizacao, direcao, situacao, usuario, meioIdentificacao, autorizacao.getIntegracao(), (redeEmpresa != null && redeEmpresa.getGestaoRedes()), nomeCodEmpresaRedeAcessou, true);

                    }
                }
            } else {
                /*
                 * "Key": Chave que identifica o banco de dados do cliente. Desta forma, para
                 * cada banco de dados haverá uma chave única.
                 */
                if (isIsQueuePresent()) {
                    //JMS Queue
                    enfileirarRegistro(key, dataAcesso, tipo, codigo, empresa,
                            local, terminal, direcao, situacao, usuario, meioIdentificacao);
                } else {
                    Date d1 = negocio.comuns.utilitarias.Calendario.hoje();
                    AcessoControle acessoControle = DaoAuxiliar.retornarAcessoControle(key);
                    acessoControle.registrarAcesso(dataAcesso, tipo, codigo, situacao, direcao, local, terminal, usuario, meioIdentificacao, key, empresa, nomeCodEmpresaAcessou, null, codigoMatricula);
                    retorno.setAcessoRegistrado(true);
                    retorno.setTerminal(terminal.toString());
                    Date d2 = negocio.comuns.utilitarias.Calendario.hoje();
                    long dif = d2.getTime() - d1.getTime();
                    Logger.getLogger(ValidacaoAcessoWS.class.getSimpleName()).log(Level.INFO,
                            String.format("Cartao: %s - Chave: %s - Empresa: %s - Local: %s "
                            + "- Terminal: %s - Direção: %s - Data: %s "
                            + "- Meio Ident: %s - SituacaoAcesso: %s %sms",
                            codigo,
                            key,
                            empresa,
                            local,
                            terminal,
                            direcao,
                            Calendario.getData(dataAcesso, "dd/MM/yyyy HH:mm:ss"),
                            meioIdentificacao,
                            situacao,
                            String.valueOf(dif)));
                }
            }

        } catch (Exception e) {

            Logger.getLogger(ValidacaoAcessoWS.class.getSimpleName()).log(Level.SEVERE,
                    String.format("Erro ao registrar acesso - "
                    + "chave: %s  - local: %s - "
                    + "terminal %s - "
                    + "codigo: %s - "
                    + "EXC: %s ", new Object[]{key, local, terminal, codigo, e.getMessage()}));

            retorno.setResultado(ResultadoWSEnum.ERRO);
            retorno.setTerminal(terminal.toString());
            retorno.setMsgErro("Método que ocorreu o erro: "
                    + "\"public RetornoRequisicaoRegistrarAcesso registrarAcesso\""
                    + " Classe do Erro: " + e.getClass()
                    + " Mensagem Erro: " + e.getMessage());

        }
        return retorno;
    }
}

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package acesso.webservice;

import acesso.webservice.retorno.RetornoRequisicaoRegistrarAcesso;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.logging.Level;
import java.util.logging.Logger;
import javax.jws.WebService;
import javax.jws.WebMethod;
import javax.jws.WebParam;
import negocio.comuns.acesso.enumerador.DirecaoAcessoEnum;
import negocio.comuns.acesso.enumerador.MeioIdentificacaoEnum;
import negocio.comuns.acesso.enumerador.SituacaoAcessoEnum;
import servicos.acesso.AutorizacaoAcessoService;

/**
 *
 * <AUTHOR>
 */
@WebService(serviceName = "RegistrarAcessoWS")
public class RegistrarAcessoWS extends BaseAcessoWS {

    @WebMethod(operationName = "registrarAcessoAvaliandoIntegracao")
    public RetornoRequisicaoRegistrarAcesso registrarAcessoAvaliandoIntegracao(
            @WebParam(name = "codigo") Integer codigo,
            @WebParam(name = "dataAcesso") Date dataAcesso,
            @WebParam(name = "direcao") DirecaoAcessoEnum direcao,
            @WebParam(name = "empresa") Integer empresa,
            @WebParam(name = "key") String key,
            @WebParam(name = "local") Integer local,
            @WebParam(name = "meioIdentificacao") MeioIdentificacaoEnum meioIdentificacao,
            @WebParam(name = "situacao") SituacaoAcessoEnum situacao,
            @WebParam(name = "terminal") Integer terminal,
            @WebParam(name = "tipo") String tipo,
            @WebParam(name = "usuario") Integer usuario,
            @WebParam(name = "codAcessoIntegracao") String codAcessoIntegracao) throws Exception {

        AutorizacaoAcessoService autorizacaoAcessoService = new AutorizacaoAcessoService();
        return autorizacaoAcessoService.registrarAcessoAvaliandoIntegracao(
                codigo,
                dataAcesso,
                direcao,
                empresa,
                key,
                local,
                meioIdentificacao,
                situacao,
                terminal,
                tipo,
                usuario,
                codAcessoIntegracao,
                "",
                null
        );
    }

    @Deprecated
    @WebMethod(operationName = "registrarAcesso")
    public RetornoRequisicaoRegistrarAcesso registrarAcesso(
            @WebParam(name = "codigo") Integer codigo,
            @WebParam(name = "dataAcesso") Date dataAcesso,
            @WebParam(name = "direcao") DirecaoAcessoEnum direcao,
            @WebParam(name = "empresa") Integer empresa,
            @WebParam(name = "key") String key,
            @WebParam(name = "local") Integer local,
            @WebParam(name = "meioIdentificacao") MeioIdentificacaoEnum meioIdentificacao,
            @WebParam(name = "situacao") SituacaoAcessoEnum situacao,
            @WebParam(name = "terminal") Integer terminal,
            @WebParam(name = "tipo") String tipo,
            @WebParam(name = "usuario") Integer usuario) throws Exception {
        return registrarAcessoAvaliandoIntegracao(codigo, dataAcesso, direcao, empresa, key, local, meioIdentificacao, situacao, terminal, tipo, usuario, null);
    }

}

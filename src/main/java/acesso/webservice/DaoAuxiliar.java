/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package acesso.webservice;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class DaoAuxiliar {

    private static Map<String, AcessoControle> mapaControladores = new HashMap<String, AcessoControle>();

    public static Map<String, AcessoControle> getMapaControladores() {
        return mapaControladores;
    }
    protected static String keyControleAcesso;

    public static String getKeyControleAcesso() {
        return keyControleAcesso;
    }
    public static void resetMapaControladores() {
        mapaControladores = null;
        mapaControladores = new HashMap<>();
    }

    public static AcessoControle retornarAcessoControle(String chave) throws Exception {
        AcessoControle validacao;
        if (chave == null || chave.trim().isEmpty()) {
            throw new Exception("Nenhuma chave de empresa foi informada para esta operação.");
        }
        keyControleAcesso = chave;
        if (!DaoAuxiliar.getMapaControladores().containsKey(chave)) {
            validacao = new AcessoControle(chave);
            DaoAuxiliar.getMapaControladores().put(chave, validacao);
        } else {
            validacao = DaoAuxiliar.getMapaControladores().get(chave);
            if (validacao == null) {
                validacao = new AcessoControle(chave);
                DaoAuxiliar.getMapaControladores().put(chave, validacao);
            }
        }
        validacao.prepararConexao();
        return validacao;
    }

    public static boolean existeControle(String chave) throws Exception {
        if (chave != null && !chave.trim().isEmpty()) {
            return DaoAuxiliar.getMapaControladores().containsKey(chave);
        } else {
            return false;
        }
    }
}

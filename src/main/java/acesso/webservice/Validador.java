/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package acesso.webservice;

import acesso.webservice.retorno.ResultadoWSEnum;
import acesso.webservice.retorno.RetornoRequisicaoValidacaoAcesso;
import acesso.webservice.retorno.RetornoRequisicaoWS;
import java.util.Date;
import java.util.logging.Level;
import java.util.logging.Logger;

import negocio.comuns.acesso.enumerador.DirecaoAcessoEnum;
import negocio.comuns.acesso.enumerador.MeioIdentificacaoEnum;
import negocio.comuns.acesso.enumerador.SituacaoAcessoEnum;

/**
 *
 * <AUTHOR>
 * @descrição: Singleton responsável por invocar os métodos de validação
 */
public class Validador {

    /*
     * Realizar a validação do acesso através do código de acesso.
     */
    public static RetornoRequisicaoValidacaoAcesso validarAcesso(
            String codigoAcesso,
            Integer empresa,
            Integer localAcesso,
            String terminal,
            DirecaoAcessoEnum direcao,
            Boolean forcarLib,
            MeioIdentificacaoEnum meioIdentificacao,
            String key) throws Exception {


        /*
         * "Key": Chave que identifica o banco de dados do cliente. Desta forma, para
         * cada banco de dados haverá uma chave única.
         */
        AcessoControle validacao = DaoAuxiliar.retornarAcessoControle(key);
        Date d1 = negocio.comuns.utilitarias.Calendario.hoje();
        RetornoRequisicaoValidacaoAcesso retorno = new RetornoRequisicaoValidacaoAcesso();
        boolean leitorSerial = false;
        try {
            if ((meioIdentificacao.getCodigo().equals(MeioIdentificacaoEnum.CODIGOACESSOLEITORSERIAL.getCodigo())) ||
                    (meioIdentificacao == MeioIdentificacaoEnum.CODIGOBARRACOMPUTADOR)) {
                leitorSerial = true;
            }
            boolean terminalAula = validacao.getAmbienteDao().existeAmbienteComTerminal(terminal);

            SituacaoAcessoEnum situacaoAcesso = validacao.tentarAcesso(
                    codigoAcesso, direcao, empresa, localAcesso, terminal, leitorSerial, terminalAula);

            if((situacaoAcesso == SituacaoAcessoEnum.RV_BLOQSEMAUTORIZACAO
                    || situacaoAcesso == SituacaoAcessoEnum.RV_BLOQALUNONAOCADASTRADO
                    || situacaoAcesso == SituacaoAcessoEnum.RV_BLOQSTATUSALUNO) && validacao.isValidaAlunoTotalPass(codigoAcesso))  {
                //se for tentativa para gerar base offline, não ir na totalpass pq necessita do aluno fazer o checkin
                if(validacao.getValidacaoAcessoOffline()){
                    situacaoAcesso = SituacaoAcessoEnum.RV_BLOQSEMAUTORIZACAO;
                } else {
                    situacaoAcesso = validacao.validaAcessoTotalPass(codigoAcesso, direcao, empresa, localAcesso, terminal, leitorSerial, terminalAula, validacao,false);
               }
            }

            Date d2 = negocio.comuns.utilitarias.Calendario.hoje();
            long dif = d2.getTime() - d1.getTime();
            retorno = RetornoRequisicaoWS.montarRetornoValidacaoAcesso(situacaoAcesso, validacao, forcarLib);
            retorno.setTerminal(terminal);
            retorno.setCodigoAcesso(codigoAcesso);
            if (!validacao.getValidacaoAcessoOffline()) {
                Logger.getLogger(ValidacaoAcessoWS.class.getSimpleName()).log(Level.INFO,
                        "Cartao: {0} - Chave: {1} - Empresa: {2} - Local: {3} "
                                + "- Terminal: {4} - Direção: {5} - Forçar Lib: {6} "
                                + "- Meio Ident: {7} - SituacaoAcesso: {8} {9}ms",
                        new Object[]{
                                codigoAcesso,
                                key,
                                empresa,
                                localAcesso,
                                terminal,
                                direcao,
                                forcarLib,
                                meioIdentificacao,
                                situacaoAcesso,
                                String.valueOf(dif)});
            }
        } catch (Exception e) {
            e.printStackTrace();
            if (retorno == null) {
                retorno = new RetornoRequisicaoValidacaoAcesso();
            }
            System.out.println(String.format("Erro ao tentar acesso - "
                            + "chave: %s  - local: %s - "
                            + "terminal %s - "
                            + "codigo: %s - "
                            + "EXC: %s ",
                    new Object[]{key, localAcesso, terminal, codigoAcesso,
                            e.getMessage()}));
            retorno.setResultado(ResultadoWSEnum.ERRO);
            retorno.setMsgErro("Método que ocorreu o erro: "
                    + "\"private RetornoRequisicaoValidacaoAcesso validarAcesso\""
                    + " Classe do Erro: " + e.getClass()
                    + " Mensagem Erro: " + e.getMessage());
        }
        return retorno;
    }
}

package acesso.webservice;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import controle.arquitetura.SuperControle;
import java.io.*;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

import javax.servlet.*;
import javax.servlet.http.*;
import negocio.comuns.utilitarias.UteisValidacao;
import servicos.operacoes.midias.MidiaService;
import servicos.operacoes.midias.commons.MidiaEntidadeEnum;
import servicos.propriedades.PropsService;

/**
 * Classe padrão do JasperReport utilizada para preparar as imagens a serem
 * apresentadas durante a exibição dos relatórios.
 */
public class ImageServletRedeSocial extends HttpServlet {

    protected void processRequest(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        Map imagesMap = (Map) request.getSession().getAttribute("IMAGES_MAP");

        if (imagesMap != null) {
            String imageName = request.getParameter("image");
            if (imageName != null) {
                byte[] imageData = (byte[]) imagesMap.get(Integer.parseInt(imageName));

                response.setContentType("image/jpeg");
                if (imageData != null) {
                    response.setContentLength(imageData.length);
                    ServletOutputStream ouputStream = response.getOutputStream();
                    ouputStream.write(imageData, 0, imageData.length);
                    ouputStream.flush();
                    ouputStream.close();
                }

            }
        }
        String chave = request.getParameter("key");
        String campo = request.getParameter("campo");
        String empresa = request.getParameter("empresa");
        if (chave != null) {
            Integer codEmpresa = Integer.valueOf(empresa);
            try {
                byte[] imageData = obterFotoEmpresa(codEmpresa, chave, campo == null ? "fotoredesocial" : campo);
                response.setContentType("image/jpeg");
                if (imageData != null) {
                    ServletOutputStream ouputStream = response.getOutputStream();
                    SuperControle.paintFotoInterno(ouputStream, imageData, "", 640, 640);
                    ouputStream.flush();
                }

            } catch (Exception ex) {
                Logger.getLogger(ImageServletAcesso.class.getName()).log(Level.SEVERE, null, ex);
            }
        }

    }

    // <editor-fold defaultstate="collapsed" desc="HttpServlet methods. Click on the + sign on the left to edit the code.">
    /**
     * Handles the HTTP
     * <code>GET</code> method.
     *
     * @param request servlet request
     * @param response servlet response
     */
    protected void doGet(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        processRequest(request, response);
    }

    /**
     * Handles the HTTP
     * <code>POST</code> method.
     *
     * @param request servlet request
     * @param response servlet response
     */
    protected void doPost(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        processRequest(request, response);
    }

    /**
     * Returns a short description of the servlet.
     */
    public String getServletInfo() {
        return "Short description";
    }
    // </editor-fold>

    public byte[] obterFotoEmpresa(Integer codigoEmpresa, String chave, String campo) {
        DAO dao = null;
        Connection con = null;
        byte[] imageData = null;
        try {
            if (PropsService.isTrue(PropsService.fotosParaNuvem)) {
                MidiaEntidadeEnum midia;
                if(campo != null && campo.equals("homeBackground640x551")){
                    midia = MidiaEntidadeEnum.FOTO_EMPRESA_HomeBackground640x551;
                }else if(campo != null && campo.equals("homeBackground320x276")){
                    midia = MidiaEntidadeEnum.FOTO_EMPRESA_HomeBackground320x276;
                }else if(campo != null && campo.equals("foto")){
                    midia = MidiaEntidadeEnum.FOTO_EMPRESA;
                }else{
                    midia = MidiaEntidadeEnum.FOTO_EMPRESA_REDESOCIAL;
                }
                if (!UteisValidacao.emptyNumber(codigoEmpresa)) {
                    imageData = MidiaService.getInstance().downloadObjectAsByteArray(chave,
                            midia, codigoEmpresa.toString(), null);
                }
            } else {
                String sql = "select " + campo + " from empresa where codigo = " + codigoEmpresa;
                dao = new DAO();
                con = dao.obterConexaoEspecifica(chave);
                Statement stm = con.createStatement();
                ResultSet rs = stm.executeQuery(sql);
                if (rs.next()) {
                    imageData = rs.getBytes(1);
                }
            }
        } catch (Exception ex) {
            Logger.getLogger(ImageServletAcesso.class.getName()).log(Level.SEVERE, null, ex);
        } finally {
            if (con != null) {
                try {
                    con.close();
                } catch (SQLException ex) {
                    Logger.getLogger(ImageServletAcesso.class.getName()).log(Level.SEVERE, null, ex);
                }
            }
            con = null;
            dao = null;
        }
        return imageData;
    }
}

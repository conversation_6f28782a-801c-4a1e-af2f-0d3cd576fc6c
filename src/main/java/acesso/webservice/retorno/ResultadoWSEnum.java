package acesso.webservice.retorno;

import org.json.JSONString;

/**
 * Enumerador para informar o resultado das requisições feitas via WebService.
 */
public enum ResultadoWSEnum implements JSONString {

    SUCESSO(1, "Solicitação realizada com sucesso."),
    ERRO(2, "Erro no processo da requisição.");

    private final Integer codigo;
    private final String descricao;

    ResultadoWSEnum(final Integer codigo, final String descricao) {
        this.codigo = codigo;
        this.descricao = descricao;
    }

    public Integer getCodigo() {
        return this.codigo;
    }

    public String getDescricao() {
        return this.descricao;
    }


    @Override
    public String toJSONString() {
        return "\"" + this + "\"";
    }
}

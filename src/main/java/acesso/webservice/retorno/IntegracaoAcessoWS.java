package acesso.webservice.retorno;

import br.com.pactosolucoes.comuns.json.SuperJSON;

public class IntegracaoAcessoWS extends SuperJSON {

    private Integer codigoChaveIntegracaoDigitais;
    private Integer codigoEmpresaRemota;
    private String chaveEmpresaRemota;

    public Integer getCodigoChaveIntegracaoDigitais() {
        return codigoChaveIntegracaoDigitais;
    }

    public void setCodigoChaveIntegracaoDigitais(Integer codigoChaveIntegracaoDigitais) {
        this.codigoChaveIntegracaoDigitais = codigoChaveIntegracaoDigitais;
    }

    public Integer getCodigoEmpresaRemota() {
        return codigoEmpresaRemota;
    }

    public void setCodigoEmpresaRemota(Integer codigoEmpresaRemota) {
        this.codigoEmpresaRemota = codigoEmpresaRemota;
    }

    public String getChaveEmpresaRemota() {
        return chaveEmpresaRemota;
    }

    public void setChaveEmpresaRemota(String chaveEmpresaRemota) {
        this.chaveEmpresaRemota = chaveEmpresaRemota;
    }
}

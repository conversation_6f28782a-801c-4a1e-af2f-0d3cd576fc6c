/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package acesso.webservice.retorno;

import acesso.webservice.AcessoControle;
import br.com.pactosolucoes.comuns.json.SuperJSON;
import com.fasterxml.jackson.databind.ObjectMapper;
import negocio.comuns.acesso.ColetorVO;
import negocio.comuns.acesso.LocalAcessoVO;
import negocio.comuns.acesso.enumerador.SituacaoAcessoEnum;

import java.io.Serializable;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 *
 * <AUTHOR> Objetivo: Classe base para informar o resultado do
 * processamento das requisições feitas via WebService.
 */
public class RetornoRequisicaoWS implements Serializable{

    private ResultadoWSEnum resultado = ResultadoWSEnum.SUCESSO;
    private String msgErro = "";
    private String terminal = "-1";

    public String getTerminal() {
        return terminal;
    }

    public void setTerminal(String terminal) {
        this.terminal = terminal;
    }

    public String getMsgErro() {
        return msgErro;
    }

    public void setMsgErro(String msgErro) {
        this.msgErro = msgErro;
    }

    public ResultadoWSEnum getResultado() {
        return resultado;
    }

    public void setResultado(ResultadoWSEnum resultado) {
        this.resultado = resultado;
    }

    public static RetornoRequisicaoValidacaoAcesso montarRetornoValidacaoAcesso(SituacaoAcessoEnum situacaoAcesso) {
        RetornoRequisicaoValidacaoAcesso retorno = new RetornoRequisicaoValidacaoAcesso();
        retorno.setBloqueadoLiberado(situacaoAcesso.getBloqueadoLiberado());
        retorno.setSituacaoAcesso(situacaoAcesso);
        retorno.setMsgColetor(situacaoAcesso.getMsgColetor());
        retorno.setMsgValidacao(situacaoAcesso.getDescricao());

        return retorno;
    }

    public static RetornoRequisicaoValidacaoAcesso montarRetornoValidacaoAcesso(SituacaoAcessoEnum situacaoAcesso,
            AcessoControle acessoControle,
            Boolean forcarLib) throws Exception {
        RetornoRequisicaoValidacaoAcesso retorno = new RetornoRequisicaoValidacaoAcesso();
        retorno.setBloqueadoLiberado(situacaoAcesso.getBloqueadoLiberado());
        retorno.setSituacaoAcesso(situacaoAcesso);
        retorno.setAcessoEsperado(acessoControle.getRxTx().getAcessoEsperado().getId());
        if ((retorno.getBloqueadoLiberado().equals("B"))
                && (forcarLib)) {
            // Liberar acesso, caso haja liberação de acesso forçado.
            acessoControle.getRxTx().setResultado(SituacaoAcessoEnum.RV_LIBACESSOAUTORIZADO);
            retorno.setBloqueadoLiberado("L");
        }
        String msg = "";
        if (retorno.getBloqueadoLiberado().equals("L")) {
            if (retorno.getAcessoEsperado().equals("E")) {
                msg = situacaoAcesso.getDescricao() + " - Entrada";

            } else if (retorno.getAcessoEsperado().equals("S")) {
                retorno.setTicket(acessoControle.getRxTx().getTicket());
                retorno.setPermanencia(acessoControle.getRxTx().getPermanencia());
                msg = situacaoAcesso.getDescricao() + " - Saída";

            } else {
                msg = situacaoAcesso.getDescricao();

            }
        } else {
            msg = situacaoAcesso.getMsgColetor();
        }
        retorno.setMsgColetor(msg);
        if (retorno.getBloqueadoLiberado().equals("L")) {
            /* Se o acesso foi liberado, então a mensagem que aparecerá no coletor será a mesma
             que será apresentada na tela de validação de acesso */
            retorno.setMsgValidacao(msg);
        } else {
            retorno.setMsgValidacao(acessoControle.getRxTx().getResultado().getDescricao());
        }

        retorno.setNomeCliente(acessoControle.getRxTx().getCliente().getPessoa().getNome());
        retorno.setCpfCliente(acessoControle.getRxTx().getCliente().getPessoa().getCfp());
        retorno.setUrlFotoCliente(acessoControle.getRxTx().getCliente().getPessoa().getUrlFoto());
        retorno.setMatriculaCliente(acessoControle.getRxTx().getCliente().getMatricula());
        retorno.setCategoriaCliente(acessoControle.getRxTx().getCliente().getCategoria());
        retorno.setMsgCliente(acessoControle.getRxTx().getCliente().getMensagem());
        if (acessoControle.getRxTx().getCliente().getAniversarioHj()) {
            retorno.setMsgAniversario("Feliz Aniversário");
        }
        retorno.setVencimento(acessoControle.getRxTx().getDataLimiteAcesso());
        retorno.setDuracaoContrato(acessoControle.getRxTx().getDuracaoContrato());
        retorno.setFotoCliente(acessoControle.getRxTx().getCliente().getPessoa().getFoto());

        retorno.setTipoCartao(acessoControle.getRxTx().getCartao().getTipo());
        retorno.setCodigoCliente(acessoControle.getRxTx().getCliente().getCodigo().toString());
        retorno.setCodigoPessoa(acessoControle.getRxTx().getCliente().getPessoa().getCodigo().toString());
        retorno.setMsgValidacao(situacaoAcesso.getDescricao());
        retorno.setChavesEmpresasPermiteAcesso(acessoControle.getRxTx().getCliente().getChavesEmpresasPermiteAcesso());

        acessoControle = null; // Colocar o objeto elegível para o garbage collections

        return retorno;
    }

    public static RetornoRequisicaoBuscarLocais montarRetornoLocalAcesso(LocalAcessoVO localAcessoVo, boolean senhaAcessoOnzeDigitos, boolean registrarTentativasAcesso) {
        RetornoRequisicaoBuscarLocais retorno = new RetornoRequisicaoBuscarLocais();
        // Preecher o objeto  "RetornoRequisicaoBuscarLocais" para ser retornado.
        retorno.setCodigo(localAcessoVo.getCodigo());
        retorno.setDescricao(localAcessoVo.getDescricao());
        retorno.setNomeComputador(localAcessoVo.getNomeComputador());
        retorno.setTempoEntreAcessos(localAcessoVo.getTempoEntreAcessos());
        retorno.setTempoEntreAcessosColaborador(localAcessoVo.getTempoEntreAcessosColaborador());
        retorno.setServidorImpressoes(localAcessoVo.getServidorImpressoes());
        retorno.setPortaServidorImp(localAcessoVo.getPortaServidorImp());
        retorno.setServidorFacialInner(localAcessoVo.getServidorFacialInner());
        retorno.setPortaServidorFacialInner(localAcessoVo.getPortaServidorFacialInner());
        retorno.setPedirSenhaLibParaCadaAcesso(localAcessoVo.getPedirSenhaLibParaCadaAcesso());
        retorno.setPedirSenhaCadastrarMaisBiometrias(localAcessoVo.getPedirSenhaCadastrarMaisBiometrias());
        retorno.setSolicitarJustificativaLiberacaoManual(localAcessoVo.getSolicitarJustificativaLiberacaoManual());
        retorno.setUtilizarModoOffline(localAcessoVo.getUtilizarModoOffline());
        retorno.setUsarReconhecimento(localAcessoVo.getUsarReconhecimento());
        retorno.setPortaImagens(localAcessoVo.getPortaImagens());
        retorno.setUrlServidorCamera(localAcessoVo.getUrlServidorCamera());
        retorno.setPortaServidorCamera(localAcessoVo.getPortaServidorCamera());
        retorno.setTempoToleranciaSaida(localAcessoVo.getTempoToleranciaSaida());
        retorno.setCapacidadeSimultanea(localAcessoVo.getEmpresa().getCapacidadeSimultanea());
        retorno.setRestringirAcessoOutrasUnidades(localAcessoVo.getRestringirAcessoOutrasUnidades());

        // Montar lista de coletores
        for (ColetorVO col : localAcessoVo.getListaColetores()) {

            if (col.getDesativado()) {
                // Não enviar dados dos coletores desativados.
                continue;
            }
            ColetorWS coletor = new ColetorWS();
            coletor.setCodigo(col.getCodigo());
            coletor.setDescricao(col.getDescricao());
            coletor.setAguardaGiro(col.getAguardaGiro());
            coletor.setLocalAcesso(col.getLocalAcesso());
            coletor.setModelo(col.getModelo());
            coletor.setDescricaoModeloColetor(col.getModelo().getDescricao());
            coletor.setModoTransmissao(col.getModoTransmissao());
            coletor.setDescricaoModoTransmissao(col.getModoTransmissao().getDescricao());
            coletor.setMsgDisplay(col.getMsgDisplay());
            coletor.setNumSerie(col.getNumSerie());
            coletor.setNumeroTerminal(col.getNumeroTerminal());
            coletor.setNumeroTerminalAcionamento(col.getNumTerminalAcionamento());
            coletor.setPadraoCadastro(col.getPadraoCadastro());
            coletor.setPortaComunicacao(col.getPortaComunicacao());
            coletor.setPortaParalela(col.getPortaParalela());
            coletor.setPortaLeitorSerial(col.getPortaLeitorSerial());
            coletor.setReleEntrada(col.getReleEntrada());
            coletor.setReleSaida(col.getReleSaida());
            coletor.setResolucaoDPI(col.getResolucaoDPI());
            coletor.setSensorEntrada(col.getSensorEntrada());
            coletor.setSensorSaida(col.getSensorSaida());
            coletor.setSentidoAcesso(col.getSentidoAcesso());
            coletor.setDescricaoSentidoAcesso(col.getSentidoAcesso().getDescricao());
            coletor.setTempoReleEntrada(col.getTempoReleEntrada());
            coletor.setTempoReleSaida(col.getTempoReleSaida());
            coletor.setVelocTransmissao(col.getVelocTransmissao());
            coletor.setDispAlternativo(col.getDispAlternativo());
            coletor.setDigitosLeituraCartao(col.getDigitosLeituraCartao());
            coletor.setInverterSinal(col.getInverterSinal());
            coletor.setIp(col.getIp());
            coletor.setPorta(col.getPorta());
            coletor.setBiometrico(col.getBiometrico());
            coletor.setCartaoMaster(col.getCartaoMaster());
            coletor.setBiometriaNaCatraca(col.getBiometriaNaCatraca());
            coletor.setLeitorGertec(col.getLeitorGertec());
            coletor.setSenhaAcessoOnzeDigitos(senhaAcessoOnzeDigitos);
            coletor.setRegistrarTentativaAcesso(registrarTentativasAcesso);
            coletor.setIndiceCamera(col.getIndiceCamera());
            coletor.setUsarSenhaAcessoComoBiometria(col.isUsarSenhaAcessoComoBiometria());
            coletor.setUsarCatracaOffline(col.isUsarCatracaOffline());
            coletor.setUsarNumSerieFinger(col.isUsarNumSerieFinger());
            coletor.setNumSeriePlc(col.getNumSeriePlc());
            coletor.setUtilizarMatriculaComoSenha(col.getUtilizarMatriculaComoSenha());
            coletor.setPadraoCadastroFacial(col.getPadraoCadastroFacial());
            coletor.setIpServidor(col.getIpServidor());
            coletor.setMascaraSubrede(col.getMascaraSubrede());
            coletor.setPortaServidor(col.getPortaServidor());
            coletor.setUsuarioColetor(col.getUsuarioColetor());
            coletor.setSenhaColetor(col.getSenhaColetor());
            coletor.setMaxTemperatura(col.getMaxTemperatura());
            coletor.setUsoMascaraObrigatorio(col.getUsoMascaraObrigatorio());
            coletor.setUsaFacial(col.getUsaFacial());
            coletor.setUsaRtsp(col.getUsaRtsp());

            retorno.getListaColetores().add(coletor);
        }
        localAcessoVo = null; // Colocar o objeto elegível para o garbage collections
        return retorno;
    }

    public String toJSON() {
        ObjectMapper mapper = new ObjectMapper();
        try {
            return mapper.writeValueAsString(this);
        } catch (Exception e) {
            Logger.getLogger(SuperJSON.class.getName()).log(Level.SEVERE, e.getMessage());
            return null;
        }
    }


}

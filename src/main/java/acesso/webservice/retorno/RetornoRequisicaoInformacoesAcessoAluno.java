package acesso.webservice.retorno;

public class RetornoRequisicaoInformacoesAcessoAluno extends ResultadoWS {

    private Integer codigoEmpresa;
    private String nome;
    private String tipo;

    private String codigoAcesso;
    private String codigoAcessoAlternativo;

    private Integer codigoPessoa;
    private String codigoMatricula;

    private String templateFacial;
    private String templateBiometria;
    private String senhaAcesso;

    public String getCodigoAcesso() {
        return codigoAcesso;
    }

    public void setCodigoAcesso(String codigoAcesso) {
        this.codigoAcesso = codigoAcesso;
    }

    public String getCodigoAcessoAlternativo() {
        return codigoAcessoAlternativo;
    }

    public void setCodigoAcessoAlternativo(String codigoAcessoAlternativo) {
        this.codigoAcessoAlternativo = codigoAcessoAlternativo;
    }

    public String getTipo() {
        return tipo;
    }

    public void setTipo(String tipo) {
        this.tipo = tipo;
    }

    public String getTemplateFacial() {
        return templateFacial;
    }

    public void setTemplateFacial(String templateFacial) {
        this.templateFacial = templateFacial;
    }

    public String getTemplateBiometria() {
        return templateBiometria;
    }

    public void setTemplateBiometria(String templateBiometria) {
        this.templateBiometria = templateBiometria;
    }

    public String getSenhaAcesso() {
        return senhaAcesso;
    }

    public void setSenhaAcesso(String senhaAcesso) {
        this.senhaAcesso = senhaAcesso;
    }

    public Integer getCodigoEmpresa() {
        return codigoEmpresa;
    }

    public void setCodigoEmpresa(Integer codigoEmpresa) {
        this.codigoEmpresa = codigoEmpresa;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public Integer getCodigoPessoa() {
        return codigoPessoa;
    }

    public void setCodigoPessoa(Integer codigoPessoa) {
        this.codigoPessoa = codigoPessoa;
    }

    public String getCodigoMatricula() {
        return codigoMatricula;
    }

    public void setCodigoMatricula(String codigoMatricula) {
        this.codigoMatricula = codigoMatricula;
    }
}

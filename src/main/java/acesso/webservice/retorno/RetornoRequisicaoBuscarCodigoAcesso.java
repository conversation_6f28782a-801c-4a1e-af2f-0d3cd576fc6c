/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package acesso.webservice.retorno;

/**
 *
 * <AUTHOR>
 */
public class RetornoRequisicaoBuscarCodigoAcesso extends RetornoRequisicaoWS {

    private String codigoAcesso = "XXX";
    private String nomeCliente = "Código não encontrado";
    private boolean biometriaFacial = false; //informar para acesso se tem template de biometria facial cadastrada
    private String urlFotoCliente = "";

    public String getCodigoAcesso() {
        return codigoAcesso;
    }

    public void setCodigoAcesso(String codigoAcesso) {
        this.codigoAcesso = codigoAcesso;
    }

    public String getNomeCliente() {
        return nomeCliente;
    }

    public void setNomeCliente(String nomeCliente) {
        this.nomeCliente = nomeCliente;
    }

    public boolean isBiometriaFacial() {
        return biometriaFacial;
    }

    public void setBiometriaFacial(boolean biometriaFacial) {
        this.biometriaFacial = biometriaFacial;
    }

    public String getUrlFotoCliente() {
        return urlFotoCliente;
    }

    public void setUrlFotoCliente(String urlFotoCliente) {
        this.urlFotoCliente = urlFotoCliente;
    }
}

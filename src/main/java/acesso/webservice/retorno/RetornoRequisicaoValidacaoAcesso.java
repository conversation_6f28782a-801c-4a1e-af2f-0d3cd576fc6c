/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package acesso.webservice.retorno;

import negocio.comuns.acesso.enumerador.SituacaoAcessoEnum;

import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 * Data: 20/12/10
 * Objetivo: Retornar a validação do acesso, para o WebService de Validação de Acesso.
 */
public class RetornoRequisicaoValidacaoAcesso extends RetornoRequisicaoWS {

    String nomeCliente = "";
    String matriculaCliente = "";
    byte[] fotoCliente = null;
    String vencimento = "";
    String CategoriaCliente = "";
    String msgAniversario = "";
    String msgCliente = "";
    String msgValidacao = "";
    String bloqueadoLiberado = "";
    String acessoEsperado = "";
    String tipoCartao = "";
    String codigoCliente = "";
    String codigoPessoa = "";
    String msgColetor = "";
    SituacaoAcessoEnum situacaoAcesso;
    String codigoAcesso = "";
    String ticket = "";
    int permanencia = 0;
    String urlFotoCliente = "";

    int duracaoContrato= 0;
    String cpfCliente = "";
    List<String> chavesEmpresasPermiteAcesso = new ArrayList<>();

    public void fromClient(negocio.comuns.acesso.webservice.client.RetornoRequisicaoValidacaoAcesso retorno) {
        try {
            setNomeCliente(retorno.getNomeCliente());
            setMatriculaCliente(retorno.getMatriculaCliente());
            setFotoCliente(retorno.getFotoCliente());
            setVencimento(retorno.getVencimento());
            setCategoriaCliente(retorno.getCategoriaCliente());
            setMsgAniversario(retorno.getMsgAniversario());
            setMsgCliente(retorno.getMsgCliente());
            setMsgValidacao(retorno.getMsgValidacao());
            setBloqueadoLiberado(retorno.getBloqueadoLiberado());
            setAcessoEsperado(retorno.getAcessoEsperado());
            setTipoCartao(retorno.getTipoCartao());
            setCodigoCliente(retorno.getCodigoCliente());
            setCodigoPessoa(retorno.getCodigoPessoa());
            setMsgColetor(retorno.getMsgColetor());
            setSituacaoAcesso(SituacaoAcessoEnum.valueOf(retorno.getSituacaoAcesso().toString()));
            setTerminal(retorno.getTerminal());
            setTicket(retorno.getTicket());
            setCpfCliente(retorno.getCpfCliente());
            setChavesEmpresasPermiteAcesso(retorno.getChavesEmpresasPermiteAcesso());
        } catch (Exception e) {
        }

    }

    public SituacaoAcessoEnum getSituacaoAcesso() {
        return situacaoAcesso;
    }

    public void setSituacaoAcesso(SituacaoAcessoEnum situacaoAcesso) {
        this.situacaoAcesso = situacaoAcesso;
    }

    public String getAcessoEsperado() {
        return acessoEsperado;
    }

    public void setAcessoEsperado(String acessoEsperado) {
        this.acessoEsperado = acessoEsperado;
    }

    public String getBloqueadoLiberado() {
        return bloqueadoLiberado;
    }

    public void setBloqueadoLiberado(String bloqueadoLiberado) {
        this.bloqueadoLiberado = bloqueadoLiberado;
    }

    public String getCodigoCliente() {
        return codigoCliente;
    }

    public void setCodigoCliente(String codigoCliente) {
        this.codigoCliente = codigoCliente;
    }

    public String getMsgColetor() {
        return msgColetor;
    }

    public void setMsgColetor(String msgColetor) {
        this.msgColetor = msgColetor;
    }

    public String getTipoCartao() {
        return tipoCartao;
    }

    public void setTipoCartao(String tipoCartao) {
        this.tipoCartao = tipoCartao;
    }

    public String getCategoriaCliente() {
        return CategoriaCliente;
    }

    public void setCategoriaCliente(String CategoriaCliente) {
        this.CategoriaCliente = CategoriaCliente;
    }

    public byte[] getFotoCliente() {
        return fotoCliente;
    }

    public void setFotoCliente(byte[] fotoCliente) {
        this.fotoCliente = fotoCliente;
    }

    public String getMatriculaCliente() {
        return matriculaCliente;
    }

    public void setMatriculaCliente(String matriculaCliente) {
        this.matriculaCliente = matriculaCliente;
    }

    public String getMsgAniversario() {
        return msgAniversario;
    }

    public void setMsgAniversario(String msgAniversario) {
        this.msgAniversario = msgAniversario;
    }

    public String getMsgCliente() {
        return msgCliente;
    }

    public void setMsgCliente(String msgCliente) {
        this.msgCliente = msgCliente;
    }

    public String getMsgValidacao() {
        return msgValidacao;
    }

    public void setMsgValidacao(String msgValidacao) {
        this.msgValidacao = msgValidacao;
    }

    public String getNomeCliente() {
        return nomeCliente;
    }

    public void setNomeCliente(String nomeCliente) {
        this.nomeCliente = nomeCliente;
    }

    public String getVencimento() {
        return vencimento;
    }

    public void setVencimento(String vencimento) {
        this.vencimento = vencimento;
    }

    public String getCodigoPessoa() {
        return codigoPessoa;
    }

    public void setCodigoPessoa(String codigoPessoa) {
        this.codigoPessoa = codigoPessoa;
    }

    public String getCodigoAcesso() {
        return codigoAcesso;
    }

    public void setCodigoAcesso(String codigoAcesso) {
        this.codigoAcesso = codigoAcesso;
    }

    public String getTicket() {
        return ticket;
    }

    public void setTicket(String ticket) {
        this.ticket = ticket;
    }

    public int getPermanencia() {
        return permanencia;
    }

    public void setPermanencia(int permanencia) {
        this.permanencia = permanencia;
    }

    public String getUrlFotoCliente() {
        return urlFotoCliente;
    }

    public void setUrlFotoCliente(String urlFotoCliente) {
        this.urlFotoCliente = urlFotoCliente;
    }

    public int getDuracaoContrato() {
        return duracaoContrato;
    }

    public void setDuracaoContrato(int duracaoContrato) {
        this.duracaoContrato = duracaoContrato;
    }

    public String getCpfCliente() {
        return cpfCliente;
    }

    public void setCpfCliente(String cpfCliente) {
        this.cpfCliente = cpfCliente;
    }

    public List<String> getChavesEmpresasPermiteAcesso() {
        if (chavesEmpresasPermiteAcesso == null) {
            chavesEmpresasPermiteAcesso = new ArrayList<>();
        }
        return chavesEmpresasPermiteAcesso;
    }

    public void setChavesEmpresasPermiteAcesso(List<String> chavesEmpresasPermiteAcesso) {
        this.chavesEmpresasPermiteAcesso = chavesEmpresasPermiteAcesso;
    }
}

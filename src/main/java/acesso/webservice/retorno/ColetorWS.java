/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package acesso.webservice.retorno;

import negocio.comuns.acesso.enumerador.DispositivoAlternativoEnum;
import negocio.comuns.acesso.enumerador.ModeloColetorEnum;
import negocio.comuns.acesso.enumerador.ModoTransmissaoEnum;
import negocio.comuns.acesso.enumerador.SentidoAcessoEnum;

/**
 *
 * <AUTHOR>
 * Data: 20/12/10
 * Objetivo: Retornar os dados principais da classe ColetorVO, para o
 * WebService de Validação de Acesso.

 */
public class ColetorWS {

    private Integer codigo = 0;
    private String descricao = "";
    private Boolean aguardaGiro = true;
    private Integer localAcesso = 0;
    private ModeloColetorEnum modelo = ModeloColetorEnum.MODELO_COLETOR_DESCONHECIDO;
    private ModoTransmissaoEnum modoTransmissao = ModoTransmissaoEnum.MODOTRANSMISSAO_COLETOR_AUTO;
    private String DescricaoModoTransmissao = "";
    private String msgDisplay = "";
    private String numSerie = "";
    private Integer numeroTerminal = 0;
    private Integer numeroTerminalAcionamento = 0;
    private Boolean padraoCadastro = false;
    private String portaComunicacao = "";
    private String portaParalela = "";
    private String portaLeitorSerial = "";
    private Integer releEntrada = 1;
    private Integer releSaida = 2;
    private Integer resolucaoDPI = 512;
    private Integer sensorEntrada = 0;
    private Integer sensorSaida = 1;
    private SentidoAcessoEnum sentidoAcesso = SentidoAcessoEnum.SENTIDOACESSO_COLETOR_INDIFERENTE;
    private Integer tempoReleEntrada = 5000;
    private Integer tempoReleSaida = 5000;
    private Integer velocTransmissao = 0;
    private String DescricaoSentidoAcesso = "";
    private String DescricaoModeloColetor = "";
    private DispositivoAlternativoEnum dispAlternativo = DispositivoAlternativoEnum.NENHUM_DISP;
    private Integer digitosLeituraCartao = 0;
    private Boolean inverterSinal = false;
    private String ip = "";
    private Integer porta;
    private String cartaoMaster;
    private Boolean biometrico;
    private Boolean biometriaNaCatraca;
    private int leitorGertec = 0;
    private boolean senhaAcessoOnzeDigitos = false;
    private Integer indiceCamera;
    private boolean usarSenhaAcessoComoBiometria = false;
    private boolean usarCatracaOffline = false;
    private boolean usarNumSerieFinger = false;
    private String numSeriePlc = "";
    private boolean utilizarMatriculaComoSenha;
    private Boolean padraoCadastroFacial = false;
    private Boolean usaFacial = false;
    private Boolean usaRtsp = false;
    private String ipServidor = "";
    private String mascaraSubrede = "";
    private Integer portaServidor;
    private String usuarioColetor;
    private String senhaColetor;
    private Double maxTemperatura = 0.0;
    private Boolean usoMascaraObrigatorio = false;
    private boolean registrarTentativaAcesso = false;

    public String getDescricaoModeloColetor() {
        return DescricaoModeloColetor;
    }

    public void setDescricaoModeloColetor(String DescricaoModeloColetor) {
        this.DescricaoModeloColetor = DescricaoModeloColetor;
    }

    public String getDescricaoModoTransmissao() {
        return DescricaoModoTransmissao;
    }

    public void setDescricaoModoTransmissao(String DescricaoModoTransmissao) {
        this.DescricaoModoTransmissao = DescricaoModoTransmissao;
    }

    public String getDescricaoSentidoAcesso() {
        return DescricaoSentidoAcesso;
    }

    public void setDescricaoSentidoAcesso(String DescricaoSentidoAcesso) {
        this.DescricaoSentidoAcesso = DescricaoSentidoAcesso;
    }

    public Boolean getAguardaGiro() {
        return aguardaGiro;
    }

    public void setAguardaGiro(Boolean aguardaGiro) {
        this.aguardaGiro = aguardaGiro;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public Integer getLocalAcesso() {
        return localAcesso;
    }

    public void setLocalAcesso(Integer localAcesso) {
        this.localAcesso = localAcesso;
    }

    public ModeloColetorEnum getModelo() {
        return modelo;
    }

    public void setModelo(ModeloColetorEnum modelo) {
        this.modelo = modelo;
    }

    public ModoTransmissaoEnum getModoTransmissao() {
        return modoTransmissao;
    }

    public void setModoTransmissao(ModoTransmissaoEnum modoTransmissao) {
        this.modoTransmissao = modoTransmissao;
    }

    public String getMsgDisplay() {
        return msgDisplay;
    }

    public void setMsgDisplay(String msgDisplay) {
        this.msgDisplay = msgDisplay;
    }

    public String getNumSerie() {
        return numSerie;
    }

    public void setNumSerie(String numSerie) {
        this.numSerie = numSerie;
    }

    public Integer getNumeroTerminal() {
        return numeroTerminal;
    }

    public void setNumeroTerminal(Integer numeroTerminal) {
        this.numeroTerminal = numeroTerminal;
    }

    public Integer getNumeroTerminalAcionamento() {
        return numeroTerminalAcionamento;
    }

    public void setNumeroTerminalAcionamento(Integer numeroTerminalAcionamento) {
        this.numeroTerminalAcionamento = numeroTerminalAcionamento;
    }

    public Boolean getPadraoCadastro() {
        return padraoCadastro;
    }

    public void setPadraoCadastro(Boolean padraoCadastro) {
        this.padraoCadastro = padraoCadastro;
    }

    public String getPortaComunicacao() {
        return portaComunicacao;
    }

    public void setPortaComunicacao(String portaComunicacao) {
        this.portaComunicacao = portaComunicacao;
    }

    public Integer getReleEntrada() {
        return releEntrada;
    }

    public void setReleEntrada(Integer releEntrada) {
        this.releEntrada = releEntrada;
    }

    public Integer getReleSaida() {
        return releSaida;
    }

    public void setReleSaida(Integer releSaida) {
        this.releSaida = releSaida;
    }

    public Integer getResolucaoDPI() {
        return resolucaoDPI;
    }

    public void setResolucaoDPI(Integer resolucaoDPI) {
        this.resolucaoDPI = resolucaoDPI;
    }

    public Integer getSensorEntrada() {
        return sensorEntrada;
    }

    public void setSensorEntrada(Integer sensorEntrada) {
        this.sensorEntrada = sensorEntrada;
    }

    public Integer getSensorSaida() {
        return sensorSaida;
    }

    public void setSensorSaida(Integer sensorSaida) {
        this.sensorSaida = sensorSaida;
    }

    public SentidoAcessoEnum getSentidoAcesso() {
        return sentidoAcesso;
    }

    public void setSentidoAcesso(SentidoAcessoEnum sentidoAcesso) {
        this.sentidoAcesso = sentidoAcesso;
    }

    public Integer getTempoReleEntrada() {
        return tempoReleEntrada;
    }

    public void setTempoReleEntrada(Integer tempoReleEntrada) {
        this.tempoReleEntrada = tempoReleEntrada;
    }

    public Integer getTempoReleSaida() {
        return tempoReleSaida;
    }

    public void setTempoReleSaida(Integer tempoReleSaida) {
        this.tempoReleSaida = tempoReleSaida;
    }

    public Integer getVelocTransmissao() {
        return velocTransmissao;
    }

    public void setVelocTransmissao(Integer velocTransmissao) {
        this.velocTransmissao = velocTransmissao;
    }

    public String getPortaParalela() {
        return portaParalela;
    }

    public void setPortaParalela(String portaParalela) {
        this.portaParalela = portaParalela;
    }

    public String getPortaLeitorSerial() {
        return portaLeitorSerial;
    }

    public void setPortaLeitorSerial(String portaLeitorSerial) {
        this.portaLeitorSerial = portaLeitorSerial;
    }

    public DispositivoAlternativoEnum getDispAlternativo() {
        return dispAlternativo;
    }

    public void setDispAlternativo(DispositivoAlternativoEnum dispAlternativo) {
        this.dispAlternativo = dispAlternativo;
    }

    public Integer getDigitosLeituraCartao() {
        return digitosLeituraCartao;
    }

    public void setDigitosLeituraCartao(Integer digitosLeituraCartao) {
        this.digitosLeituraCartao = digitosLeituraCartao;
    }

    public Boolean getInverterSinal() {
        return inverterSinal;
    }

    public void setInverterSinal(Boolean inverterSinal) {
        this.inverterSinal = inverterSinal;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public Integer getPorta() {
        return porta;
    }

    public void setPorta(Integer porta) {
        this.porta = porta;
    }

    public String getCartaoMaster() {
        return cartaoMaster;
    }

    public void setCartaoMaster(String cartaoMaster) {
        this.cartaoMaster = cartaoMaster;
    }

    public Boolean getBiometrico() {
        return biometrico;
    }

    public void setBiometrico(Boolean biometrico) {
        this.biometrico = biometrico;
    }

    public Boolean getBiometriaNaCatraca() {
        return biometriaNaCatraca;
    }

    public void setBiometriaNaCatraca(Boolean biometriaNaCatraca) {
        this.biometriaNaCatraca = biometriaNaCatraca;
    }

    public int getLeitorGertec() {
        return leitorGertec;
    }

    public void setLeitorGertec(int leitorGertec) {
        this.leitorGertec = leitorGertec;
    }

    public boolean isSenhaAcessoOnzeDigitos() {
        return senhaAcessoOnzeDigitos;
    }

    public void setSenhaAcessoOnzeDigitos(boolean senhaAcessoOnzeDigitos) {
        this.senhaAcessoOnzeDigitos = senhaAcessoOnzeDigitos;
    }

    public Integer getIndiceCamera() {
        return indiceCamera;
    }

    public void setIndiceCamera(Integer indiceCamera) {
        this.indiceCamera = indiceCamera;
    }

    public boolean isUsarSenhaAcessoComoBiometria() {
        return usarSenhaAcessoComoBiometria;
    }

    public void setUsarSenhaAcessoComoBiometria(boolean usarSenhaAcessoComoBiometria) {
        this.usarSenhaAcessoComoBiometria = usarSenhaAcessoComoBiometria;
    }

    public boolean isUsarCatracaOffline() {
        return usarCatracaOffline;
    }

    public void setUsarCatracaOffline(boolean usarCatracaOffline) {
        this.usarCatracaOffline = usarCatracaOffline;
    }

    public boolean isUsarNumSerieFinger() {
        return usarNumSerieFinger;
    }

    public void setUsarNumSerieFinger(boolean usarNumSerieFinger) {
        this.usarNumSerieFinger = usarNumSerieFinger;
    }

    public String getNumSeriePlc() {
        return numSeriePlc;
    }

    public void setNumSeriePlc(String numSeriePlc) {
        this.numSeriePlc = numSeriePlc;
    }

    public boolean isUtilizarMatriculaComoSenha() {
        return utilizarMatriculaComoSenha;
    }

    public void setUtilizarMatriculaComoSenha(boolean utilizarMatriculaComoSenha) {
        this.utilizarMatriculaComoSenha = utilizarMatriculaComoSenha;
    }

    public Boolean getPadraoCadastroFacial() {
        return padraoCadastroFacial;
    }

    public void setPadraoCadastroFacial(Boolean padraoCadastroFacial) {
        this.padraoCadastroFacial = padraoCadastroFacial;
    }

    public Boolean getUsaFacial() {
        return usaFacial;
    }

    public void setUsaFacial(Boolean usaFacial) {
        this.usaFacial = usaFacial;
    }

    public Boolean getUsaRtsp() {
        return usaRtsp;
    }

    public void setUsaRtsp(Boolean usaRtsp) {
        this.usaRtsp = usaRtsp;
    }

    public String getIpServidor() {
        return ipServidor;
    }

    public void setIpServidor(String ipServidor) {
        this.ipServidor = ipServidor;
    }

    public String getMascaraSubrede() {
        return mascaraSubrede;
    }

    public void setMascaraSubrede(String mascaraSubrede) {
        this.mascaraSubrede = mascaraSubrede;
    }

    public Integer getPortaServidor() {
        return portaServidor;
    }

    public void setPortaServidor(Integer portaServidor) {
        this.portaServidor = portaServidor;
    }

    public String getUsuarioColetor() {
        return usuarioColetor;
    }

    public void setUsuarioColetor(String usuarioColetor) {
        this.usuarioColetor = usuarioColetor;
    }

    public String getSenhaColetor() {
        return senhaColetor;
    }

    public void setSenhaColetor(String senhaColetor) {
        this.senhaColetor = senhaColetor;
    }

    public Double getMaxTemperatura() {
        return maxTemperatura;
    }

    public void setMaxTemperatura(Double maxTemperatura) {
        this.maxTemperatura = maxTemperatura;
    }

    public Boolean isUsoMascaraObrigatorio() {
        return usoMascaraObrigatorio;
    }

    public void setUsoMascaraObrigatorio(Boolean usoMascaraObrigatorio) {
        this.usoMascaraObrigatorio = usoMascaraObrigatorio;
    }

    public boolean getRegistrarTentativaAcesso() {
        return registrarTentativaAcesso;
    }

    public void setRegistrarTentativaAcesso(boolean registrarTentativaAcesso) {
        this.registrarTentativaAcesso = registrarTentativaAcesso;
    }
}

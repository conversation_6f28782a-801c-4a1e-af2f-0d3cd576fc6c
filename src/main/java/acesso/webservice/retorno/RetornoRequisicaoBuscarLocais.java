/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package acesso.webservice.retorno;

import java.util.ArrayList;

/**
 *
 * <AUTHOR> Data: 20/12/10 Objetivo: Retornar os dados principais da
 * classe LocalAcessoVO, para o WebService de Validação de Acesso.
 */
public class RetornoRequisicaoBuscarLocais extends RetornoRequisicaoWS {

    private Integer codigo = 0;
    private String descricao = "";
    private String nomeComputador = "";
    private Integer tempoEntreAcessos = 0;
    private Integer tempoEntreAcessosColaborador = 0;
    private Integer tempoToleranciaSaida = 0;
    private String servidorImpressoes = "";
    private String servidorFacialInner = "";
    private Integer portaServidorFacialInner = 0;
    private Integer portaServidorImp = 0;
    private Boolean pedirSenhaLibParaCadaAcesso;
    private Boolean utilizarModoOffline = false;
    private ArrayList<ColetorWS> listaColetores = new ArrayList<>();
    private Boolean pedirSenhaCadastrarMaisBiometrias = false;
    private Boolean solicitarJustificativaLiberacaoManual = true;

    private Boolean usarReconhecimento = false;
    private Integer portaImagens = 0;
    private String urlServidorCamera = "";
    private Integer portaServidorCamera = 0;

    private Integer capacidadeSimultanea = 0;
    private Boolean restringirAcessoOutrasUnidades = false;

    public Boolean getPedirSenhaLibParaCadaAcesso() {
        return pedirSenhaLibParaCadaAcesso;
    }

    public void setPedirSenhaLibParaCadaAcesso(Boolean pedirSenhaLibParaCadaAcesso) {
        this.pedirSenhaLibParaCadaAcesso = pedirSenhaLibParaCadaAcesso;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public ArrayList<ColetorWS> getListaColetores() {
        return listaColetores;
    }

    public void setListaColetores(ArrayList<ColetorWS> listaColetores) {
        this.listaColetores = listaColetores;
    }

    public String getNomeComputador() {
        return nomeComputador;
    }

    public void setNomeComputador(String nomeComputador) {
        this.nomeComputador = nomeComputador;
    }

    public Integer getPortaServidorImp() {
        return portaServidorImp;
    }

    public void setPortaServidorImp(Integer portaServidorImp) {
        this.portaServidorImp = portaServidorImp;
    }

    public String getServidorImpressoes() {
        return servidorImpressoes;
    }

    public void setServidorImpressoes(String servidorImpressoes) {
        this.servidorImpressoes = servidorImpressoes;
    }

    public String getServidorFacialInner() {
        return servidorFacialInner;
    }

    public void setServidorFacialInner(String servidorFacialInner) {
        this.servidorFacialInner = servidorFacialInner;
    }

    public Integer getPortaServidorFacialInner() {
        return portaServidorFacialInner;
    }

    public void setPortaServidorFacialInner(Integer portaServidorFacialInner) {
        this.portaServidorFacialInner = portaServidorFacialInner;
    }

    public Integer getTempoEntreAcessos() {
        return tempoEntreAcessos;
    }

    public void setTempoEntreAcessos(Integer tempoEntreAcessos) {
        this.tempoEntreAcessos = tempoEntreAcessos;
    }

    public Integer getTempoEntreAcessosColaborador() {
        return tempoEntreAcessosColaborador;
    }

    public void setTempoEntreAcessosColaborador(Integer tempoEntreAcessosColaborador) {
        this.tempoEntreAcessosColaborador = tempoEntreAcessosColaborador;
    }

    public Integer getTempoToleranciaSaida() {
        return tempoToleranciaSaida;
    }

    public void setTempoToleranciaSaida(Integer tempoToleranciaSaida) {
        this.tempoToleranciaSaida = tempoToleranciaSaida;
    }

    public Boolean getUtilizarModoOffline() {
        return utilizarModoOffline;
    }

    public void setUtilizarModoOffline(Boolean utilizarModoOffline) {
        this.utilizarModoOffline = utilizarModoOffline;
    }

    public Boolean getPedirSenhaCadastrarMaisBiometrias() {
        return pedirSenhaCadastrarMaisBiometrias;
    }

    public void setPedirSenhaCadastrarMaisBiometrias(Boolean pedirSenhaCadastrarMaisBiometrias) {
        this.pedirSenhaCadastrarMaisBiometrias = pedirSenhaCadastrarMaisBiometrias;
    }

    public Boolean getUsarReconhecimento() {
        return usarReconhecimento;
    }

    public void setUsarReconhecimento(Boolean usarReconhecimento) {
        this.usarReconhecimento = usarReconhecimento;
    }

    public Integer getPortaImagens() {
        return portaImagens;
    }

    public void setPortaImagens(Integer portaImagens) {
        this.portaImagens = portaImagens;
    }

    public String getUrlServidorCamera() {
        return urlServidorCamera;
    }

    public void setUrlServidorCamera(String urlServidorCamera) {
        this.urlServidorCamera = urlServidorCamera;
    }

    public Integer getPortaServidorCamera() {
        return portaServidorCamera;
    }

    public void setPortaServidorCamera(Integer portaServidorCamera) {
        this.portaServidorCamera = portaServidorCamera;
    }

    public Boolean getSolicitarJustificativaLiberacaoManual() {
        return solicitarJustificativaLiberacaoManual;
    }

    public void setSolicitarJustificativaLiberacaoManual(Boolean solicitarJustificativaLiberacaoManual) {
        this.solicitarJustificativaLiberacaoManual = solicitarJustificativaLiberacaoManual;
    }

    public Integer getCapacidadeSimultanea() {
        return capacidadeSimultanea;
    }

    public void setCapacidadeSimultanea(Integer capacidadeSimultanea) {
        this.capacidadeSimultanea = capacidadeSimultanea;
    }

    public Boolean getRestringirAcessoOutrasUnidades() {
        return restringirAcessoOutrasUnidades;
    }

    public void setRestringirAcessoOutrasUnidades(Boolean restringirAcessoOutrasUnidades) {
        this.restringirAcessoOutrasUnidades = restringirAcessoOutrasUnidades;
    }
}

package acesso.webservice.retorno;

import org.json.JSONObject;

public class ResultadoWS {
    private boolean sucesso;
    private String mensagem;

    public ResultadoWS() {
    }

    public ResultadoWS(boolean sucesso, String mensagem) {
        this.sucesso = sucesso;
        this.mensagem = mensagem;
    }

    public String getMensagem() {
        return mensagem;
    }

    public void setMensagem(String mensagem) {
        this.mensagem = mensagem;
    }

    public boolean isSucesso() {
        return sucesso;
    }

    public void setSucesso(boolean sucesso) {
        this.sucesso = sucesso;
    }

    public JSONObject toJSON(){
        return new JSONObject(this);
    }
}

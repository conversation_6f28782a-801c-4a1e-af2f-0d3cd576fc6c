package acesso.webservice;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import controle.arquitetura.SuperControle;
import controle.basico.EmpresaControle;
import controle.modulos.SmartBoxControle;
import java.io.*;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;
import javax.faces.context.FacesContext;

import javax.servlet.*;
import javax.servlet.http.*;

import negocio.comuns.utilitarias.UteisValidacao;
import negocio.modulos.smartbox.SmartBoxVO;
import negocio.modulos.smartbox.TotalPorIndicadorTO;
import servicos.operacoes.midias.commons.MidiaEntidadeEnum;
import servicos.operacoes.midias.MidiaService;
import servicos.propriedades.PropsService;

/**
 * Classe padrão do JasperReport utilizada para preparar as imagens a serem
 * apresentadas durante a exibição dos relatórios.
 */
public class ImageServletAcesso extends HttpServlet {

    protected void processRequest(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        Map imagesMap = (Map) request.getSession().getAttribute("IMAGES_MAP");

        if (imagesMap != null) {
            String imageName = request.getParameter("image");
            if (imageName != null) {
                byte[] imageData = (byte[]) imagesMap.get(Integer.parseInt(imageName));
                response.setContentType("image/jpeg");
                if (imageData != null) {
                    response.setContentLength(imageData.length);
                    ServletOutputStream ouputStream = response.getOutputStream();
                    ouputStream.write(imageData, 0, imageData.length);
                    ouputStream.flush();
                    ouputStream.close();
                }

            }
        }
        MidiaEntidadeEnum midia = MidiaEntidadeEnum.FOTO_EMPRESA_RELATORIO;
        int altura = 70;
        String tipo = request.getParameter("tipo");
        if(tipo != null){
            midia = tipo.equals("logo") ? MidiaEntidadeEnum.FOTO_EMPRESA : MidiaEntidadeEnum.FOTO_EMPRESA_RELATORIO;
            altura = tipo.equals("logo") ? 156 : 70;
        }
        String imageEmpresa = request.getParameter("emp");
        if (imageEmpresa != null) {
            EmpresaControle control = (EmpresaControle) request.getSession().
                    getAttribute("EmpresaControle");
            if (control != null) {
                control.getEmpresaVO().setFotoRelatorio(obterFotoEmpresa(control.getEmpresaVO().getCodigo(), obterChave("key"), midia));
                byte[] imageData = (byte[]) control.getEmpresaVO().getFotoRelatorio();
                response.setContentType("image/jpeg");
                if (imageData != null) {
                    response.setContentLength(imageData.length);
                    ServletOutputStream ouputStream = response.getOutputStream();
                    ouputStream.write(imageData, 0, imageData.length);
                    ouputStream.flush();
                    ouputStream.close();
                }
            }

        }
        String chave = request.getParameter("key");
        if (chave != null) {            
            byte[] imageData = obterFotoEmpresa(1, chave, midia);
            response.setContentType("image/jpeg");
            if (imageData != null) {
                ServletOutputStream ouputStream = response.getOutputStream();
                try {
                    SuperControle.paintFotoInterno(ouputStream, imageData, "", 156, altura);
                } catch (Exception ex) {
                    Logger.getLogger(ImageServletAcesso.class.getName()).log(Level.SEVERE, null, ex);
                } finally {
                    ouputStream.flush();
                }
            }
        }

        String box = (String) request.getParameter("box");
        String indicador = (String) request.getParameter("indicador");
        if (box != null && indicador != null) {

            SmartBoxControle control = (SmartBoxControle) request.getSession().getAttribute("SmartBoxControle");
            if (control != null) {
                List<SmartBoxVO> lista = control.getListaCaixas();
                for (SmartBoxVO smartBoxVO : lista) {
                    List<TotalPorIndicadorTO> listaIndicadorVOs = smartBoxVO.getListaIndicadores();
                    for (TotalPorIndicadorTO totalPorIndicadorTO : listaIndicadorVOs) {
                        if (totalPorIndicadorTO.getTipoBox().name().equals(box)
                                && totalPorIndicadorTO.getIndicador().name().equals(indicador)) {
                            byte[] imageData = totalPorIndicadorTO.getChart();
                            response.setContentType("image/png");
                            if (imageData != null) {
                                response.setContentLength(imageData.length);
                                ServletOutputStream ouputStream = response.getOutputStream();
                                ouputStream.write(imageData, 0, imageData.length);
                                ouputStream.flush();
                                ouputStream.close();

                                break;
                            }
                        }
                    }
                }
            }
        }
    }

    // <editor-fold defaultstate="collapsed" desc="HttpServlet methods. Click on the + sign on the left to edit the code.">
    /**
     * Handles the HTTP
     * <code>GET</code> method.
     *
     * @param request servlet request
     * @param response servlet response
     */
    protected void doGet(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        processRequest(request, response);
    }

    /**
     * Handles the HTTP
     * <code>POST</code> method.
     *
     * @param request servlet request
     * @param response servlet response
     */
    protected void doPost(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        processRequest(request, response);
    }

    /**
     * Returns a short description of the servlet.
     */
    public String getServletInfo() {
        return "Short description";
    }
    // </editor-fold>

    public byte[] obterFotoEmpresa(Integer empresa, String chave, MidiaEntidadeEnum midia) {
        DAO dao = null;
        Connection con = null;
        byte[] imageData = null;
        try {
            if (PropsService.isTrue(PropsService.fotosParaNuvem)) {
                if (empresa != null && !UteisValidacao.emptyNumber(empresa)) {
                    imageData = MidiaService.getInstance().downloadObjectAsByteArray(chave,
                            midia, empresa.toString(), null);
                } else if (empresa == null) {
                    imageData = MidiaService.getInstance().downloadObjectAsByteArray(chave,
                            midia, null, null);
                }
            } else {
                String sql;
                if (empresa == null) {
                    sql = "select fotorelatorio from empresa where length(fotorelatorio) > 0 limit 1";
                } else {
                    sql = "select fotorelatorio from empresa "
                            + "where codigo = " + empresa;
                }
                dao = new DAO();
                con = dao.obterConexaoEspecifica(chave);
                Statement stm = con.createStatement();
                ResultSet rs = stm.executeQuery(sql);
                if (rs.next()) {
                    imageData = rs.getBytes(1);
                }
            }
        } catch (Exception ex) {
            Logger.getLogger(ImageServletAcesso.class.getName()).log(Level.SEVERE, null, ex);
        } finally {
            if (con != null) {
                try {
                    con.close();
                } catch (SQLException ex) {
                    Logger.getLogger(ImageServletAcesso.class.getName()).log(Level.SEVERE, null, ex);
                }
            }
            con = null;
            dao = null;
        }
        return imageData;
    }

    private String obterChave(String key) {
        final FacesContext ctx = FacesContext.getCurrentInstance();
        if (ctx != null) {
            final Map sessionState = ctx.getExternalContext().getSessionMap();
            return sessionState.get(key) != null ? sessionState.get(key).toString() : "";
        } else {
            return null;
        }
    }
}

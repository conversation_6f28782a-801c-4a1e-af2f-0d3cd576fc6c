package controle.relatorios;

import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.plano.ModalidadeVO;
import relatorio.controle.arquitetura.SuperControleRelatorio;

import javax.faces.model.SelectItem;
import java.util.ArrayList;
import java.util.List;

public class MapaEstatisticoControle extends SuperControleRelatorio {

    private List<SelectItem> listaModalidade;
    private ModalidadeVO modalidadeVO;


    public List<SelectItem> getListaModalidade() {
        return listaModalidade;
    }

    public void setListaModalidade(List<SelectItem> listaModalidade) {
        this.listaModalidade = listaModalidade;
    }

    public void inicializar(){
        try {
            setEmpresa(new EmpresaVO());
            montarListaEmpresas();
            montarModalidades();
        }catch (Exception e){
            montarErro(e);
        }
    }

    public void montarModalidades() throws Exception{
        List<ModalidadeVO> modalidades = getFacade().getModalidade().consultarSimples(getEmpresa().getCodigo());
        listaModalidade = new ArrayList<SelectItem>();
        listaModalidade.add(new SelectItem(null, ""));
        for(ModalidadeVO m : modalidades){
            listaModalidade.add(new SelectItem(m.getCodigo(), m.getNome()));
        }
    }

    public ModalidadeVO getModalidadeVO() {
        return modalidadeVO;
    }

    public void setModalidadeVO(ModalidadeVO modalidadeVO) {
        this.modalidadeVO = modalidadeVO;
    }
}

package controle.basico;

import negocio.comuns.basico.FamiliaTO;

import java.util.Date;

public class AgendaProfessor {

    private Integer codigo = 0;
    private String nome;
    private String tipo;
    private Integer visitante = 0;
    private Integer convertido = 0;
    private String icv;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getTipo() {
        return tipo;
    }

    public void setTipo(String tipo) {
        this.tipo = tipo;
    }

    public Integer getVisitante() {
        return visitante;
    }

    public void setVisitante(Integer visitante) {
        this.visitante = visitante;
    }

    public Integer getConvertido() {
        return convertido;
    }

    public void setConvertido(Integer convertido) {
        this.convertido = convertido;
    }

    public String getIcv() {
        return icv;
    }

    public void setIcv(String icv) {
        this.icv = icv;
    }
}
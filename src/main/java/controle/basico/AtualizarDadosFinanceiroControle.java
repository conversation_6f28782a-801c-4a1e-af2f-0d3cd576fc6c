package controle.basico;

import br.com.pactosolucoes.ce.comuns.ex.ValidacaoException;
import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.enumeradores.TipoColaboradorEnum;
import controle.arquitetura.SuperControle;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.financeiro.AtualizacaoFinanceiroDTO;
import negocio.comuns.financeiro.enumerador.NichoEnum;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import org.json.JSONObject;
import servicos.util.ExecuteRequestHttpService;

import javax.faces.model.SelectItem;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;


public class AtualizarDadosFinanceiroControle extends SuperControle {

    private PessoaVO pessoaVO;
    private UsuarioVO usuarioVO;
    private EmpresaVO empresaVO;
    private AtualizacaoFinanceiroDTO atualizacaoFinanceiroDTO;
    private String onComplete;
    private boolean preenchimentoObrigatorio = false;
    private boolean mostrarModalAtualizaDados = false;

    private NichoEnum nichoEnum;

    private static final String HEADER_AUTHORIZATION = "Authorization";
    private static final String HEADER_CONTENT_TYPE = "Content-Type";
    private static final String APPLICATION_JSON = "application/json";
    private static final String CHARSET_UTF8 = "UTF-8";
    private static final String CONTENT = "content";


    public AtualizarDadosFinanceiroControle() {
        limparMsg();
        setOnComplete("");
        setEmpresaVO(new EmpresaVO());
        setUsuarioVO(new UsuarioVO());
        setPessoaVO(new PessoaVO());
        setAtualizacaoFinanceiroDTO(new AtualizacaoFinanceiroDTO());
        mostrarModalAtualizaDados = false;
        inicializar();
    }

    private void inicializar() {
        try {
            limparMsg();
            setOnComplete("");

            mostrarModalAtualizaDados = false;

            if (!SuperControle.isAtualizacaoCadastral()) {
                return;
            }

            setEmpresaVO(getEmpresaLogado());
            if (getEmpresaVO() == null || UteisValidacao.emptyNumber(getEmpresaVO().getCodigo())) {
                return;
            }

            setUsuarioVO(getUsuarioLogado());
            if (getUsuarioVO().getAdministrador() == null || !getUsuarioVO().getAdministrador()) {
                try {
                    ColaboradorVO colaborador = getFacade().getColaborador()
                            .consultarPorCodigoIgualTipoColaborador(getUsuarioVO().getColaboradorVO().getCodigo(),
                                    getEmpresaVO().getCodigo(), TipoColaboradorEnum.ADMINISTRADOR, false,
                                    Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                    if (colaborador == null) {
                        throw new Exception();
                    }
                } catch (Exception e) {
                    return;
                }
            }

            getAtualizacaoFinanceiroDTO().setChaveZw((String) JSFUtilities.getFromSession("key"));
            getAtualizacaoFinanceiroDTO().setCodigoZw(getEmpresaVO().getCodigo());

            try {
                buscarAtualizacaoFinanceiroNoIof();
            } catch (Exception exception) {
                Uteis.logarDebug(String.format(
                        "Problemas ao buscarAtualizacaofinanceira (IOF) para chave %s - %d." +
                                "%n%s",
                        JSFUtilities.getFromSession("key"),
                        getEmpresaVO().getCodigo(),
                        exception.getMessage()));
            }

            if (getAtualizacaoFinanceiroDTO() == null) {
                return;
            }

            boolean possuiDadosPreencher = false;
            try {
                 validarDados();
            } catch (Exception e) {
                possuiDadosPreencher = true;
            }

            if (!possuiDadosPreencher) {
                return;
            }

            Date hoje = new Date();

            if (getAtualizacaoFinanceiroDTO().getDataAtualizacaoFinanceiro() != null && Uteis.nrDiasEntreDatas(getAtualizacaoFinanceiroDTO().getDataAtualizacaoFinanceiro(), hoje) >= 3) {
                preenchimentoObrigatorio = true;
            }

            setPessoaVO(getFacade().getPessoa().consultarPorChavePrimaria(getUsuarioVO().getColaboradorVO().getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));

            //mostrarModalAtualizaDados = (getUsuarioVO().getUsuarioPactoSolucoes() ? false : true && (isAtualizarDadosEmpresa() || getPessoaVO().isAtualizarDados()) && !atualizacaoCadastralAdiada);
            mostrarModalAtualizaDados = true;
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void buscarAtualizacaoFinanceiroNoIof() throws Exception {
        String url = getUrlIntegradorOamd() + "/sincronizar/buscarAtualizacaofinanceira?chaveZw=" + getAtualizacaoFinanceiroDTO().getChaveZw() +
                "&empresaZw=" + getAtualizacaoFinanceiroDTO().getCodigoZw();

        String response = ExecuteRequestHttpService.executeRequestGet(url, new HashMap<>());

        JSONObject resposta = null;
        try {
            resposta = new JSONObject(response).getJSONObject("content");
        } catch (Exception e) {}

        if (resposta != null) {
            atualizacaoFinanceiroDTO = new AtualizacaoFinanceiroDTO(resposta.toString());
            nichoEnum = NichoEnum.consultarPorSigla(atualizacaoFinanceiroDTO.getNicho());
        } else {
            atualizacaoFinanceiroDTO = null;
        }
    }

    public void salvarAtualizacaoCadastral() {
        try {
            limparMsg();
            setOnComplete("");

            getAtualizacaoFinanceiroDTO().setNicho(getNichoEnum() != null ? getNichoEnum().getSigla() : null);

            validarDados();

            JSONObject json = getAtualizacaoFinanceiroDTO().toJson();

            String url = getUrlIntegradorOamd() + "/sincronizar/salvarAtualizacaofinanceira";

            JSONObject resposta = null;

            try {
                //qualquer erro no servidor irá forçar o fechamento da tela de atualização
                String response = Uteis.executeRequestSintetico(url, json.toString(), new HashMap<>());
                resposta = new JSONObject(response).getJSONObject("content");
            } catch (Exception e) {}

            mostrarModalAtualizaDados = false;
            if (resposta != null) {
                atualizacaoFinanceiroDTO = new AtualizacaoFinanceiroDTO(resposta.toString());
                nichoEnum = NichoEnum.consultarPorSigla(atualizacaoFinanceiroDTO.getNicho());
                montarSucessoGrowl("Atualização Cadastral realizada com sucesso. Obrigado.");
                setOnComplete(getMensagemNotificar());
            } else {
                montarAviso("Não foi possível fazer a atualização neste momento. Tente mais tarde!");
                setOnComplete(getMensagemNotificar());
            }
        } catch (ValidacaoException ex) {
            tratarValidacaoException(ex);
        } catch (Exception ex) {
            montarErro(ex);
            setOnComplete(getMensagemNotificar());
        }

    }

    private void validarDados() throws Exception {
        if (UteisValidacao.emptyString(getAtualizacaoFinanceiroDTO().getNomeDono())) {
            throw new ValidacaoException(new String[]{"nomeDonoModalCadastro"}, "Informe o nome do dono da unidade");
        }
        getAtualizacaoFinanceiroDTO().setNomeDono(getAtualizacaoFinanceiroDTO().getNomeDono().toUpperCase());

        if (UteisValidacao.emptyString(getAtualizacaoFinanceiroDTO().getResponsavelGeral())) {
            throw new ValidacaoException(new String[]{"nomeResponsavelModalCadastro"}, "Informe o nome do responsável geral da unidade");
        }
        getAtualizacaoFinanceiroDTO().setResponsavelGeral(getAtualizacaoFinanceiroDTO().getResponsavelGeral().toUpperCase());

        if (UteisValidacao.emptyString(getAtualizacaoFinanceiroDTO().getResponsavelTelefone())) {
            throw new ValidacaoException(new String[]{"telefoneModalCadastro"}, "Informe o número de contato do responsável.");
        }

        if (!Uteis.isValidEmailAddressRegex(getAtualizacaoFinanceiroDTO().getResponsavelEmail())) {
            throw new ValidacaoException(new String[]{"emailModalCadastro"}, "Informe o principal e-mail do responsável.");
        }

        if (UteisValidacao.emptyString(getAtualizacaoFinanceiroDTO().getNicho())) {
            throw new ValidacaoException(new String[]{"nichoModalCadastro"}, "Selecione o nicho da unidade");
        }

        if (UteisValidacao.emptyNumber(getAtualizacaoFinanceiroDTO().getMetragem())) {
            throw new ValidacaoException(new String[]{"metragemModalCadastro"}, "Informe o espaço total de sua unidade");
        }

        if (!getAtualizacaoFinanceiroDTO().isMetragemValid()) {
            throw new ValidacaoException(new String[]{"metragemModalCadastro"}, "O espaço deverá ser entre 30 e 6000 m²");
        }
    }

    public void naoApresentarModalAtualizarFinanceiros() {
        setMostrarModalAtualizaDados(false);
    }

    public PessoaVO getPessoaVO() {
        if (pessoaVO == null) {
            pessoaVO = new PessoaVO();
        }
        return pessoaVO;
    }

    public void setPessoaVO(PessoaVO pessoaVO) {
        this.pessoaVO = pessoaVO;
    }

    public UsuarioVO getUsuarioVO() {
        if (usuarioVO == null) {
            usuarioVO = new UsuarioVO();
        }
        return usuarioVO;
    }

    public void setUsuarioVO(UsuarioVO usuarioVO) {
        this.usuarioVO = usuarioVO;
    }

    public boolean isPreenchimentoObrigatorio() {
        return preenchimentoObrigatorio;
    }

    public void setPreenchimentoObrigatorio(boolean preenchimentoObrigatorio) {
        this.preenchimentoObrigatorio = preenchimentoObrigatorio;
    }

    public boolean isMostrarModalAtualizaDados() {
        return mostrarModalAtualizaDados;
    }

    public void setMostrarModalAtualizaDados(boolean mostrarModalAtualizaDados) {
        this.mostrarModalAtualizaDados = mostrarModalAtualizaDados;
    }

    public NichoEnum getNichoEnum() {
        return nichoEnum;
    }

    public void setNichoEnum(NichoEnum nichoEnum) {
        this.nichoEnum = nichoEnum;
    }

    public String getOnComplete() {
        if (onComplete == null) {
            onComplete = "";
        }
        return onComplete;
    }

    public void setOnComplete(String onComplete) {
        this.onComplete = onComplete;
    }

    public EmpresaVO getEmpresaVO() {
        if (empresaVO == null) {
            empresaVO = new EmpresaVO();
        }
        return empresaVO;
    }

    public void setEmpresaVO(EmpresaVO empresaVO) {
        this.empresaVO = empresaVO;
    }

    public AtualizacaoFinanceiroDTO getAtualizacaoFinanceiroDTO() {
        return atualizacaoFinanceiroDTO;
    }

    public void setAtualizacaoFinanceiroDTO(AtualizacaoFinanceiroDTO atualizacaoFinanceiroDTO) {
        this.atualizacaoFinanceiroDTO = atualizacaoFinanceiroDTO;
    }

    private void tratarValidacaoException(ValidacaoException ex) {
        montarErro(ex);
        setOnComplete(getMensagemNotificar());
        String[] campos = ex.getCampos();

        if (campos != null && campos.length > 0) {
            StringBuilder function = new StringBuilder("try{camposRequeridos('");
            for (String c : campos) {
                function.append(",").append(c);
            }
            function = new StringBuilder(function.toString().replaceFirst(",", "") + "', false)}catch(e){console.log(e)}; ");
            setOnComplete(function + getOnComplete());
        }
    }

    public List<SelectItem> getListaNichos() {
        List<SelectItem> listaCarregadaNichos = new ArrayList<>();
        for (NichoEnum item : NichoEnum.values()) {
            listaCarregadaNichos.add(new SelectItem(item, item.getDescricao()));
        }
        Ordenacao.ordenarLista(listaCarregadaNichos, "label");
        listaCarregadaNichos.add(0, new SelectItem(null, "Selecione um nicho"));
        return listaCarregadaNichos;
    }
}

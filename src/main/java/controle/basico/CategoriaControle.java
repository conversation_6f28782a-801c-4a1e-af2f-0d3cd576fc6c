package controle.basico;

import java.util.ArrayList;
import java.util.List;

import javax.faces.model.SelectItem;

import controle.basico.clube.MensagemGenericaControle;
import negocio.comuns.basico.CategoriaVO;
import negocio.comuns.utilitarias.ControleConsulta;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.basico.Categoria;
import br.com.pactosolucoes.ce.comuns.enumerador.TipoCategoria;
import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.estrutura.exportador.controle.ExportadorListaControle;
import controle.arquitetura.SuperControle;
import controle.arquitetura.security.LoginControle;
import javax.faces.event.ActionEvent;

/**
 * Classe responsável por implementar a interação entre os componentes JSF das páginas 
 * categoriaForm.jsp categoriaCons.jsp) com as funcionalidades da classe <code>Categoria</code>.
 * Implemtação da camada controle (Backing Bean).
 * @see SuperControle
 * @see Categoria
 * @see CategoriaVO
 */
public class CategoriaControle extends SuperControle {

    private CategoriaVO categoriaVO;
    private Integer codigoEditar = 0;
    private String oncompleteLog;
    private Boolean campoConsultaSelectItem = false;
    private String msgAlert;

    /**
     * Interface <code>CategoriaInterfaceFacade</code> responsável pela interconexão da camada de controle com a camada de negócio.
     * Criando uma independência da camada de controle com relação a tenologia de persistência dos dados (DesignPatter: Façade).
     */
    public CategoriaControle() throws Exception {
        obterUsuarioLogado();
        inicializarFacades();
        setControleConsulta(new ControleConsulta());
        setMensagemID("");
    }

    /**
     * Rotina responsável por disponibilizar um novo objeto da classe <code>Categoria</code>
     * para edição pelo usuário da aplicação.
     */
    public String novo() {
        setCategoriaVO(new CategoriaVO());
        setSucesso(false);
        setErro(false);
        return "editar";
    }

    public void novoSemRedirect(){
        novo();
    }

    /**
     * Rotina responsável por disponibilizar os dados de um objeto da classe <code>Categoria</code> para alteração.
     * O objeto desta classe é disponibilizado na session da página (request) para que o JSP correspondente possa disponibilizá-lo para edição.
     */
    public String editar() {
        Integer codigoConsulta = Integer.parseInt(request().getParameter("chavePrimaria"));
        try {
            CategoriaVO obj = getFacade().getCategoria().consultarPorChavePrimaria(codigoConsulta, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            obj.setNovoObj(false);
            obj.registrarObjetoVOAntesDaAlteracao();
            setCategoriaVO(new CategoriaVO());
            setCategoriaVO(obj);
        } catch (Exception e) {
            setErro(true);
            setSucesso(false);
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
        return "editar";
    }

    /**
     * Rotina responsável por gravar no BD os dados editados de um novo objeto da classe <code>Categoria</code>.
     * Caso o objeto seja novo (ainda não gravado no BD) é acionado a operação <code>incluir()</code>. Caso contrário é acionado o <code>alterar()</code>.
     * Se houver alguma inconsistência o objeto não é gravado, sendo re-apresentado para o usuário juntamente com uma mensagem de erro.
     */
    public String gravar(boolean centralEventos) {
        try {
            if (centralEventos) {
                this.verificarAutorizacao();
                if (categoriaVO.isNovoObj().booleanValue()) {
                    getFacade().getCategoria().incluir(categoriaVO, true);
                    incluirLogInclusao("CATEGORIA CLI CE");
                } else {
                    getFacade().getCategoria().alterar(categoriaVO, true);
                    incluirLogAlteracao("CATEGORIA CLI CE");
                }
            } else {
                if (categoriaVO.isNovoObj().booleanValue()) {
                    getFacade().getCategoria().incluir(categoriaVO);
                    incluirLogInclusao("CATEGORIA CLI");
                } else {
                    getFacade().getCategoria().alterar(categoriaVO);
                    incluirLogAlteracao("CATEGORIA CLI");
                }
            }
            atualizarListaCategoriasSessao();
            setMensagemID("msg_dados_gravados");
            setSucesso(true);
            setErro(false);
            return "editar";
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
            return "editar";
        }
    }
    public void atualizarListaCategoriasSessao(){
        SuperControle superControl = (SuperControle)JSFUtilities.getFromSession("SuperControle");
        superControl.montarListaCategorias();
    }
    /**
     * Inclui o log de inclusão de categoria
     * @throws Exception
     */
    public void incluirLogInclusao(String nome) throws Exception {
        try {
            categoriaVO.setObjetoVOAntesAlteracao(new CategoriaVO());
            categoriaVO.setNovoObj(true);
            registrarLogObjetoVO(categoriaVO, categoriaVO.getCodigo(), nome, 0);
        } catch (Exception e) {
            registrarLogErroObjetoVO(nome, categoriaVO.getCodigo(), "ERRO AO GERAR LOG DE INCLUSÃO DE " + nome, this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
        categoriaVO.setNovoObj(new Boolean(false));
        categoriaVO.registrarObjetoVOAntesDaAlteracao();
    }
    
    public void incluirLogExclusao(String nome) throws Exception {
        try {
            categoriaVO.setObjetoVOAntesAlteracao(new CategoriaVO());
            categoriaVO.setNovoObj(true);
            registrarLogExclusaoTodosDadosObjetoVO(categoriaVO, categoriaVO.getCodigo(), nome, 0);
        } catch (Exception e) {
            registrarLogErroObjetoVO(nome, categoriaVO.getCodigo(), "ERRO AO GERAR LOG DE EXCLUSÃO DE " + nome, this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
    }

    /**
     * Inclui o log de alteração de categoria
     * @throws Exception
     */
    public void incluirLogAlteracao(String nome) throws Exception {
        try {
            registrarLogObjetoVO(categoriaVO, categoriaVO.getCodigo(), nome, 0);
        } catch (Exception e) {
            registrarLogErroObjetoVO(nome, categoriaVO.getCodigo(), "ERRO AO GERAR LOG DE ALTERAÇÃO DE " + nome, this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
        categoriaVO.registrarObjetoVOAntesDaAlteracao();
    }

    /**
     * <AUTHOR>
     * 24/03/2011
     * @return
     */
    public String gravar() {
        return this.gravar(false);
    }

    /**
     * <AUTHOR>
     * 24/03/2011
     * @return
     */
    public String gravarCE() {
        return this.gravar(true);
    }

    /**
     * Rotina responsavel por executar as consultas disponiveis no JSP CategoriaCons.jsp.
     * Define o tipo de consulta a ser executada, por meio de ComboBox denominado campoConsulta, disponivel neste mesmo JSP.
     * Como resultado, disponibiliza um List com os objetos selecionados na sessao da pagina.
     */
    public String consultar() {
        try {
            super.consultar();
            List objs = new ArrayList();

            //elimina espaços em branco antes de pesquisar
            getControleConsulta().setValorConsulta(getControleConsulta().getValorConsulta().trim());

            if (getControleConsulta().getCampoConsulta().equals("codigo")) {
                if (getControleConsulta().getValorConsulta().equals("")) {
                    objs = getFacade().getCategoria().consultarPorCodigo(new Integer(0), true, Uteis.NIVELMONTARDADOS_TODOS);
                } else {
                    int valorInt = Integer.parseInt(getControleConsulta().getValorConsulta());
                    CategoriaVO categoria = getFacade().getCategoria().consultarPorCodigo(new Integer(valorInt), Uteis.NIVELMONTARDADOS_TODOS);
                    if (categoria != null) {
                        objs.add(categoria);
                    }
                }
            }
            if (getControleConsulta().getCampoConsulta().equals("nome")) {
                objs = getFacade().getCategoria().consultarPorNome(getControleConsulta().getValorConsulta(), true, Uteis.NIVELMONTARDADOS_TODOS);
            }
            if (getControleConsulta().getCampoConsulta().equals("tipoCategoria")) {
                objs = getFacade().getCategoria().consultarPorTipoCategoria(getControleConsulta().getValorConsulta(), true, Uteis.NIVELMONTARDADOS_TODOS);
            }
            //TODO Deve ser deletado após falar com Marcos Ribeiro!!
            /*if (getControleConsulta().getCampoConsulta().equals("nrConvitePermitido")) {
            if (getControleConsulta().getValorConsulta().equals("")) {
            getControleConsulta().setValorConsulta("0");
            }
            int valorInt = Integer.parseInt(getControleConsulta().getValorConsulta());
            objs = getFacade().getCategoria().consultarPorNrConvitePermitido(new Integer(valorInt), true, Uteis.NIVELMONTARDADOS_TODOS);
            }*/
            objs = ControleConsulta.obterSubListPaginaApresentar(objs, controleConsulta);
            definirVisibilidadeLinksNavegacao(controleConsulta.getPaginaAtual(), controleConsulta.getNrTotalPaginas());
            setListaConsulta(objs);
            setSucesso(true);
            setErro(false);
            return "consultar";
        } catch (Exception e) {
            setListaConsulta(new ArrayList());
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
            return "consultar";
        }
    }

    /**
     * Operação responsável por processar a exclusão um objeto da classe <code>CategoriaVO</code>
     * Após a exclusão ela automaticamente aciona a rotina para uma nova inclusão.
     */
    public String excluir(boolean centralEventos) {
        try {
            if (centralEventos) {
                this.verificarAutorizacao();
                getFacade().getCategoria().excluir(categoriaVO, true);

                //LOG - INICIO
                try {
                    registrarLogExclusaoObjetoVO(categoriaVO, categoriaVO.getCodigo().intValue(), "CATEGORIA CLI CE", 0);
                } catch (Exception e) {
                    registrarLogErroObjetoVO("CATEGORIA CLI CE", categoriaVO.getCodigo(), "ERRO AO GERAR LOG DE EXCLUSÃO DE CATEGORIA CLI CE", this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
                    e.printStackTrace();
                }
                //LOG - FIM
            } else {
                getFacade().getCategoria().excluir(categoriaVO);
                incluirLogExclusao("CATEGORIA CLI");
                
            }
            atualizarListaCategoriasSessao();
            setCategoriaVO(new CategoriaVO());
            setMensagemID("msg_dados_excluidos");
            setSucesso(true);
            setErro(false);
            return "consultar";
        } catch (Exception e) {
            if (e.getMessage().contains("ERRO: atualização ou exclusão em tabela \"categoria\" viola restrição de chave estrangeira")){
                setMensagemDetalhada("Esta categoria não pode ser excluída, pois está sendo utilizada!");
            }else {
            setMensagemDetalhada("msg_erro", e.getMessage());
            }
            setSucesso(false);
            setErro(true);
            return "editar";
        }
    }

    /**
     * <AUTHOR>
     * 24/03/2011
     * @return
     */
    public String excluir() {
        return this.excluir(false);
    }

    /**
     * <AUTHOR>
     * 24/03/2011
     * @return
     */
    public String excluirCE() {
        return this.excluir(true);
    }

    public void irPaginaInicial() throws Exception {
        controleConsulta.setPaginaAtual(1);
        this.consultar();
    }

    public void irPaginaAnterior() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getPaginaAtual() - 1);
        this.consultar();
    }

    public void irPaginaPosterior() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getPaginaAtual() + 1);
        this.consultar();
    }

    public void irPaginaFinal() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getNrTotalPaginas());
        this.consultar();
    }

    /** Método responsável por inicializar List<SelectItem> de valores do 
     * ComboBox correspondente ao atributo <code>tipoCategoria</code>
     */
    public List getListaSelectItemTipoCategoriaCategoriaCE() throws Exception {
        List<SelectItem> itens = new ArrayList();
        itens.add(new SelectItem("", ""));
        for (TipoCategoria tipoObj : TipoCategoria.values(true)) {
            itens.add(new SelectItem(tipoObj.getCodigo(), tipoObj.getDescricao()));
        }
        Ordenacao.ordenarLista(itens, "label");
        return itens;
    }

    public List getListaSelectItemTipoCategoriaCategoria() throws Exception {
        List<SelectItem> itens = new ArrayList();
        itens.add(new SelectItem("", ""));
        for (TipoCategoria tipoObj : TipoCategoria.values(false)) {
            itens.add(new SelectItem(tipoObj.getCodigo(), tipoObj.getDescricao()));
        }
        Ordenacao.ordenarLista(itens, "label");
        return itens;
    }

    /**
     * Rotina responsável por preencher a combo de consulta da telas.
     */
    public List getTipoConsultaCombo() {
        List<SelectItem> itens = new ArrayList();
        itens.add(new SelectItem("nome", "Nome"));
        itens.add(new SelectItem("codigo", "Código"));
        itens.add(new SelectItem("tipoCategoria", "Tipo de Categoria"));
        //TODO Deve ser deletado após falar com Marcos Ribeiro!!
        //itens.add(new SelectItem("nrConvitePermitido", "Número de Convite Permitidos"));
        Ordenacao.ordenarLista(itens, "label");
        return itens;
    }

    public void alterarCampoConsulta() {
        String campoSelecionado = getControleConsulta().getCampoConsulta();
        getControleConsulta().setValorConsulta("");
        if (campoSelecionado.equals("tipoCategoria")) {
            setCampoConsultaSelectItem(Boolean.TRUE);
        } else {
            setCampoConsultaSelectItem(Boolean.FALSE);
        }
    }

    public List getListaSelectItemConsulta() {
        List<SelectItem> itens = new ArrayList();
        itens.add(new SelectItem("", ""));

        try {
            if (getControleConsulta().getCampoConsulta().equals("tipoCategoria")) {
                return getListaSelectItemTipoCategoriaCategoria();
            }
        } catch (Exception e) {
            setErro(true);
            setSucesso(false);
            setMensagemDetalhada("msg_erro", e.getMessage());
            e.printStackTrace();
        }

        return itens;
    }

    /**
     * Rotina responsável por organizar a paginação entre as páginas resultantes de uma consulta.
     */
    public String inicializarConsultar() {
        setPaginaAtualDeTodas("0/0");
        setListaConsulta(new ArrayList());
        definirVisibilidadeLinksNavegacao(0, 0);
        setMensagemID("");
        setSucesso(false);
        setErro(false);
        return "consultar";
    }

    /**
     * Operação que inicializa as Interfaces Façades com os respectivos objetos de
     * persistência dos dados no banco de dados. 
     */
    protected boolean inicializarFacades() {
        try {
            super.inicializarFacades();

            return true;
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro_conectarBD", e.getMessage());
            return false;
        }
    }

    public CategoriaVO getCategoriaVO() {
        return categoriaVO;
    }

    public void setCategoriaVO(CategoriaVO categoriaVO) {
        this.categoriaVO = categoriaVO;
    }

    /**
     * Consulta de logs de categorias
     */
    @SuppressWarnings("unchecked")
    public void realizarConsultaLogObjetoSelecionado() {
        LoginControle loginControle = (LoginControle) context().getExternalContext().getSessionMap().get("LoginControle");
        loginControle.setNomeClasse("Categoria de Clientes");
        loginControle.consultarLogObjetoSelecionado("CATEGORIA CLI", categoriaVO.getCodigo(), null);
    }
    @SuppressWarnings("unchecked")
    public void realizarConsultaLogObjetoGeral() {
      categoriaVO = new CategoriaVO();
      realizarConsultaLogObjetoSelecionado();
    }

    /**
     * @return the oncompleteLog
     */
    public String getOncompleteLog() {
        return oncompleteLog;
    }

    /**
     * @param oncompleteLog the oncompleteLog to set
     */
    public void setOncompleteLog(String oncompleteLog) {
        if (oncompleteLog == null) {
            oncompleteLog = "";
        }
        this.oncompleteLog = oncompleteLog;
    }

    public void setCampoConsultaSelectItem(Boolean campoConsultaSelectItem) {
        this.campoConsultaSelectItem = campoConsultaSelectItem;
    }

    public Boolean getCampoConsultaSelectItem() {
        return campoConsultaSelectItem;
    }

    public Integer getCodigoEditar() {
        return codigoEditar;
    }

    public void setCodigoEditar(Integer codigoEditar) {
        this.codigoEditar = codigoEditar;
    }

    public void exportar(ActionEvent evt) throws Exception {
        ExportadorListaControle exportadorListaControle = (ExportadorListaControle) JSFUtilities.getFromRequest(ExportadorListaControle.class.getSimpleName());
        String paramsTableFiltrada = context().getExternalContext().getRequestParameterMap().get("paramsTableFiltrada");

        String[] split = paramsTableFiltrada.split(",");
        String campoOrdenacao = split[0].replace("[", "");
        String ordem = split[1];
        String filtro = split[2].replace("''", "");
        filtro = filtro.replace("]", "");
        List listaParaImpressao = getFacade().getCategoria().consultarParaImpressao(filtro, ordem, campoOrdenacao, getEmpresaLogado() == null ? 0 : getEmpresaLogado().getCodigo());
        exportadorListaControle.exportar(evt, listaParaImpressao, filtro, null);

    }

    public void setMsgAlert(String msgAlert) {
        this.msgAlert = msgAlert;
    }

    public String getMsgAlert() {
        if (msgAlert == null) {
            return "";
        }
        return msgAlert;
    }


    public void confirmarExcluir(){
        MensagemGenericaControle control = (MensagemGenericaControle) getControlador(MensagemGenericaControle.class);
        control.setMensagemDetalhada("", "");
        setMsgAlert("Richfaces.showModalPanel('mdlMensagemGenerica');");
        control.init("Exclusão de Categoria",
                "Deseja excluir a Categoria?",
                this, "excluir", "", "", "", "grupoBtnExcluir");
    }
}

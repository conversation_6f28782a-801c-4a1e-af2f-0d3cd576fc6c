package controle.basico;

import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.estrutura.exportador.controle.ExportadorListaControle;
import java.util.ArrayList;
import java.util.List;

import javax.faces.model.SelectItem;

import controle.basico.clube.MensagemGenericaControle;
import negocio.comuns.basico.ClassificacaoVO;
import negocio.comuns.utilitarias.ControleConsulta;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.basico.Classificacao;
import controle.arquitetura.SuperControle;
import controle.arquitetura.security.LoginControle;
import javax.faces.event.ActionEvent;

/**
 * Classe responsável por implementar a interação entre os componentes JSF das páginas 
 * classificacaoForm.jsp classificacaoCons.jsp) com as funcionalidades da classe <code>Classificacao</code>.
 * Implemtação da camada controle (Backing Bean).
 * @see SuperControle
 * @see Classificacao
 * @see ClassificacaoVO
 */
public class ClassificacaoControle extends SuperControle {

    private ClassificacaoVO classificacaoVO;
    private String oncompleteLog;
    private String msgAlert;

    /**
     * Interface <code>ClassificacaoInterfaceFacade</code> responsável pela interconexão da camada de controle com a camada de negócio.
     * Criando uma independência da camada de controle com relação a tenologia de persistência dos dados (DesignPatter: Façade).
     */
    public ClassificacaoControle() throws Exception {
        obterUsuarioLogado();
        inicializarFacades();
        setControleConsulta(new ControleConsulta());
        setMensagemID("");
    }

    /**
     * Rotina responsável por disponibilizar um novo objeto da classe <code>Classificacao</code>
     * para edição pelo usuário da aplicação.
     */
    public String novo() {
        setClassificacaoVO(new ClassificacaoVO());
        limparMsg();
        return "editar";
    }

    /**
     * Rotina responsável por disponibilizar os dados de um objeto da classe <code>Classificacao</code> para alteração.
     * O objeto desta classe é disponibilizado na session da página (request) para que o JSP correspondente possa disponibilizá-lo para edição.
     */
    public String editar() {
        Integer codigoConsulta = Integer.parseInt(request().getParameter("chavePrimaria"));
        try {
            ClassificacaoVO obj = getFacade().getClassificacao().consultarPorChavePrimaria(codigoConsulta, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            obj.setNovoObj(false);
            obj.registrarObjetoVOAntesDaAlteracao();
            setClassificacaoVO(new ClassificacaoVO());
            setClassificacaoVO(obj);
            limparMsg();
        } catch (Exception e) {
            setErro(true);
            setSucesso(false);
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
        return "editar";
    }

    /**
     * Rotina responsável por gravar no BD os dados editados de um novo objeto da classe <code>Classificacao</code>.
     * Caso o objeto seja novo (ainda não gravado no BD) é acionado a operação <code>incluir()</code>. Caso contrário é acionado o <code>alterar()</code>.
     * Se houver alguma inconsistência o objeto não é gravado, sendo re-apresentado para o usuário juntamente com uma mensagem de erro.
     */
    public String gravar() {
        try {
            if (classificacaoVO.isNovoObj().booleanValue()) {
                getFacade().getClassificacao().incluir(classificacaoVO);
                incluirLogInclusao();
            } else {
                getFacade().getClassificacao().alterar(classificacaoVO);
                incluirLogAlteracao();
            }
            setSucesso(true);
            setErro(false);
            setMensagemID("msg_dados_gravados");
            return "editar";
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
            return "editar";
        }
    }

    public void incluirLogInclusao() throws Exception {
        //LOG - INICIO
        try {
            classificacaoVO.setObjetoVOAntesAlteracao(new ClassificacaoVO());
            classificacaoVO.setNovoObj(true);
            registrarLogObjetoVO(classificacaoVO, classificacaoVO.getCodigo(), "CLASSIFICACAO", 0);
        } catch (Exception e) {
            registrarLogErroObjetoVO("CLASSIFICACAO", classificacaoVO.getCodigo(), "ERRO AO GERAR LOG DE INCLUSÃO DE CLASSIFICACAO", this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
        classificacaoVO.setNovoObj(new Boolean(false));
        classificacaoVO.registrarObjetoVOAntesDaAlteracao();
        //LOG - FIM
    }
    
    public void incluirLogExclusao() throws Exception {
        //LOG - INICIO
        try {
            classificacaoVO.setObjetoVOAntesAlteracao(new ClassificacaoVO());
            classificacaoVO.setNovoObj(true);
            registrarLogExclusaoTodosDadosObjetoVO(classificacaoVO, classificacaoVO.getCodigo(), "CLASSIFICACAO", 0);
        } catch (Exception e) {
            registrarLogErroObjetoVO("CLASSIFICACAO", classificacaoVO.getCodigo(), "ERRO AO GERAR LOG DE EXCLUSÃO DE CLASSIFICACAO", this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
        //LOG - FIM
    }


    public void incluirLogAlteracao() throws Exception {
        //LOG - INICIO
        try {
            registrarLogObjetoVO(classificacaoVO, classificacaoVO.getCodigo(), "CLASSIFICACAO", 0);
        } catch (Exception e) {
            registrarLogErroObjetoVO("CLASSIFICACAO", classificacaoVO.getCodigo(), "ERRO AO GERAR LOG DE ALTERAÇÃO DE CLASSIFICACAO", this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
        classificacaoVO.registrarObjetoVOAntesDaAlteracao();
        //LOG - FIM
    }

    /**
     * Rotina responsavel por executar as consultas disponiveis no JSP ClassificacaoCons.jsp.
     * Define o tipo de consulta a ser executada, por meio de ComboBox denominado campoConsulta, disponivel neste mesmo JSP.
     * Como resultado, disponibiliza um List com os objetos selecionados na sessao da pagina.
     */
    public String consultar() {
        try {
            super.consultar();
            List objs = new ArrayList();

            //limpar espaços em branco antes de realizar a consulta
            getControleConsulta().setValorConsulta(getControleConsulta().getValorConsulta().trim());

            if (getControleConsulta().getCampoConsulta().equals("codigo")) {
                if (getControleConsulta().getValorConsulta().equals("")) {
                    objs = getFacade().getClassificacao().consultarPorCodigo(new Integer(0), true, Uteis.NIVELMONTARDADOS_TODOS);
                } else {
                    int valorInt = Integer.parseInt(getControleConsulta().getValorConsulta());
                    ClassificacaoVO classificacao = getFacade().getClassificacao().consultarPorCodigoExato(new Integer(valorInt), true, Uteis.NIVELMONTARDADOS_TODOS);
                    if (classificacao != null) {
                        objs.add(classificacao);
                    }
                }
            }
            if (getControleConsulta().getCampoConsulta().equals("nome")) {
                objs = getFacade().getClassificacao().consultarPorNome(getControleConsulta().getValorConsulta(), true, Uteis.NIVELMONTARDADOS_TODOS);
            }
            objs = ControleConsulta.obterSubListPaginaApresentar(objs, controleConsulta);
            definirVisibilidadeLinksNavegacao(controleConsulta.getPaginaAtual(), controleConsulta.getNrTotalPaginas());
            setListaConsulta(objs);
            limparMsg();
            return "consultar";
        } catch (Exception e) {
            setListaConsulta(new ArrayList());
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
            return "consultar";
        }
    }

    /**
     * Operação responsável por processar a exclusão um objeto da classe <code>ClassificacaoVO</code>
     * Após a exclusão ela automaticamente aciona a rotina para uma nova inclusão.
     */
    public String excluir() {
        try {
            getFacade().getClassificacao().excluir(classificacaoVO);
            incluirLogExclusao();
            setClassificacaoVO(new ClassificacaoVO());
//            redirect("/faces/classificacaoCons.jsp");
            setMensagemID("msg_dados_excluidos");
            setSucesso(true);
            setErro(false);
            return "consultar";
        } catch (Exception e) {
            setSucesso(false);
            setErro(true);
            if (e.getMessage().contains("ERRO: atualização ou exclusão em tabela \"classificacao\" viola restrição de chave estrangeira")){
                setMensagemDetalhada("Esta classificação não pode ser excluída, pois está sendo utilizada!");
            }
            return "editar";
        }
    }

    public void irPaginaInicial() throws Exception {
        controleConsulta.setPaginaAtual(1);
        this.consultar();
    }

    public void irPaginaAnterior() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getPaginaAtual() - 1);
        this.consultar();
    }

    public void irPaginaPosterior() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getPaginaAtual() + 1);
        this.consultar();
    }

    public void irPaginaFinal() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getNrTotalPaginas());
        this.consultar();
    }

    /**
     * Rotina responsável por preencher a combo de consulta da telas.
     */
    public List<SelectItem> getTipoConsultaCombo() {
        List<SelectItem> itens = new ArrayList();
        itens.add(new SelectItem("codigo", "Código"));
        itens.add(new SelectItem("nome", "Nome"));
        return itens;
    }

    /**
     * Rotina responsável por organizar a paginação entre as páginas resultantes de uma consulta.
     */
    public String inicializarConsultar() {
        setPaginaAtualDeTodas("0/0");
        setListaConsulta(new ArrayList());
        definirVisibilidadeLinksNavegacao(0, 0);
        limparMsg();
        return "consultar";
    }

    /**
     * Operação que inicializa as Interfaces Façades com os respectivos objetos de
     * persistência dos dados no banco de dados. 
     */
    protected boolean inicializarFacades() {
        try {
            super.inicializarFacades();
            return true;
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro_conectarBD", e.getMessage());
            return false;
        }
    }

    public ClassificacaoVO getClassificacaoVO() {
        return classificacaoVO;
    }

    public void setClassificacaoVO(ClassificacaoVO classificacaoVO) {
        this.classificacaoVO = classificacaoVO;
    }

    /**
     * Consulta de logs de cidade
     */
    @SuppressWarnings("unchecked")
    public void realizarConsultaLogObjetoSelecionado() {
        LoginControle loginControle = (LoginControle) context().getExternalContext().getSessionMap().get("LoginControle");
        loginControle.setNomeClasse("Classificação");
        loginControle.consultarLogObjetoSelecionado("CLASSIFICACAO", classificacaoVO.getCodigo(), null);
    }
    
    @SuppressWarnings("unchecked")
    public void realizarConsultaLogObjetoGeral() {
        classificacaoVO = new ClassificacaoVO();
        realizarConsultaLogObjetoSelecionado();
    }

    /**
     * @return the oncompleteLog
     */
    public String getOncompleteLog() {
        return oncompleteLog;
    }

    /**
     * @param oncompleteLog the oncompleteLog to set
     */
    public void setOncompleteLog(String oncompleteLog) {
        this.oncompleteLog = oncompleteLog;
    }

    public void exportar(ActionEvent evt) throws Exception {
        ExportadorListaControle exportadorListaControle = (ExportadorListaControle) JSFUtilities.getFromRequest(ExportadorListaControle.class.getSimpleName());
        String paramsTableFiltrada = context().getExternalContext().getRequestParameterMap().get("paramsTableFiltrada");

        String[] split = paramsTableFiltrada.split(",");
        String campoOrdenacao = split[0].replace("[", "");
        String ordem = split[1];
        String filtro = split[2].replace("''", "");
        filtro = filtro.replace("]", "");
        List listaParaImpressao = getFacade().getClassificacao().consultarParaImpressao(filtro, ordem, campoOrdenacao, getEmpresaLogado() == null ? 0 : getEmpresaLogado().getCodigo());
        exportadorListaControle.exportar(evt, listaParaImpressao, filtro, null);

    }

    public void setMsgAlert(String msgAlert) {
        this.msgAlert = msgAlert;
    }

    public String getMsgAlert() {
        if (msgAlert == null) {
            return "";
        }
        return msgAlert;
    }


    public void confirmarExcluir(){
        MensagemGenericaControle control = (MensagemGenericaControle) getControlador(MensagemGenericaControle.class);
        control.setMensagemDetalhada("", "");
        setMsgAlert("Richfaces.showModalPanel('mdlMensagemGenerica');");
        control.init("Exclusão de Classificação",
                "Deseja excluir a Classificação?",
                this, "excluir", "", "", "", "grupoMensagem");
    }
}

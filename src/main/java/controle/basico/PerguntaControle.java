package controle.basico;

import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.estrutura.exportador.controle.ExportadorListaControle;
import controle.arquitetura.security.LoginControle;
import java.util.Hashtable;
import java.util.Enumeration;
import java.util.Collections;

import controle.basico.clube.MensagemGenericaControle;
import negocio.facade.jdbc.basico.Pergunta;
import negocio.comuns.utilitarias.*;
import negocio.comuns.basico.*;

import javax.faces.model.SelectItem;
import java.util.Iterator;
import java.util.List;
import java.util.ArrayList;
import controle.arquitetura.SuperControle;
import javax.faces.event.ActionEvent;
import negocio.comuns.arquitetura.LogVO;

/**
 * Classe responsável por implementar a interação entre os componentes JSF das páginas 
 * perguntaForm.jsp perguntaCons.jsp) com as funcionalidades da classe <code>Pergunta</code>.
 * Implemtação da camada controle (Backing Bean).
 * @see SuperControle
 * @see Pergunta
 * @see PerguntaVO
 */
public class PerguntaControle extends SuperControle {

    private PerguntaVO perguntaVO;
    private Boolean campoConsultaSelectItem = false;
    protected Boolean apresentarGrid;
    protected Boolean apresentarGridVO;
    protected String valorAnterior;
    /**
     * Interface <code>PerguntaInterfaceFacade</code> responsável pela interconexão da camada de controle com a camada de negócio.
     * Criando uma independência da camada de controle com relação a tenologia de persistência dos dados (DesignPatter: Façade).
     */
    private RespostaPerguntaVO respostaPerguntaVO;
    private List<RespostaPerguntaVO> listaObjetosExcluidos;
    private String msgAlert;

    public PerguntaControle() throws Exception {

        obterUsuarioLogado();
        inicializarFacades();
        setControleConsulta(new ControleConsulta());
        setMensagemID("");
    }

    /**
     * Rotina responsável por disponibilizar um novo objeto da classe <code>Pergunta</code>
     * para edição pelo usuário da aplicação.
     */
    public String novo() {
        setPerguntaVO(new PerguntaVO());
        setRespostaPerguntaVO(new RespostaPerguntaVO());
        setApresentarGridVO(new Boolean(true));
        setApresentarGrid(new Boolean(false));
        setListaObjetosExcluidos(new ArrayList<RespostaPerguntaVO>());
        setValorAnterior("");
        setSucesso(false);
        setErro(false);
        return "editar";
    }

    /**
     * Rotina responsável por disponibilizar os dados de um objeto da classe <code>Pergunta</code> para alteração.
     * O objeto desta classe é disponibilizado na session da página (request) para que o JSP correspondente possa disponibilizá-lo para edição.
     */
    public String editar() {
        Integer codigoConsulta = Integer.parseInt(request().getParameter("chavePrimaria"));
        try {
            PerguntaVO obj = getFacade().getPergunta().consultarPorChavePrimaria(codigoConsulta, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            obj.registrarObjetoVOAntesDaAlteracao();
            obj.registrarRespostaAntesAlteracao();
            obj.setNovoObj(false);
            setListaObjetosExcluidos(new ArrayList<RespostaPerguntaVO>());
            setRespostaPerguntaVO(new RespostaPerguntaVO());
            setPerguntaVO(new PerguntaVO());
            setPerguntaVO(obj);
            apresentarDados();
        } catch (Exception e) {
            setErro(true);
            setSucesso(false);
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
        return "editar";
    }

    public String gravar() {
        return this.gravar(false);
    }

    public String gravarCE() {
        return this.gravar(true);
    }

    /**
     * Rotina responsável por gravar no BD os dados editados de um novo objeto da classe <code>Pergunta</code>.
     * Caso o objeto seja novo (ainda não gravado no BD) é acionado a operação <code>incluir()</code>. Caso contrário é acionado o <code>alterar()</code>.
     * Se houver alguma inconsistência o objeto não é gravado, sendo re-apresentado para o usuário juntamente com uma mensagem de erro.
     */
    public String gravar(boolean centralEventos) {
        try {
            perguntaVO.setDescricao(perguntaVO.getDescricao().trim());
            if (centralEventos) {
                this.verificarAutorizacao();
                if (perguntaVO.isNovoObj().booleanValue()) {
                    getFacade().getPergunta().incluir(perguntaVO, true);
                    incluirLogInclusao();
                } else {
                    getFacade().getPergunta().alterar(perguntaVO, true);
                    incluirLogAlteracao();
                }
            } else {
                if (perguntaVO.isNovoObj().booleanValue()) {
                    getFacade().getPergunta().incluir(perguntaVO);
                    incluirLogInclusao();
                } else {
                    getFacade().getPergunta().alterar(perguntaVO);
                    incluirLogAlteracao();
                }
            }
            perguntaVO.registrarObjetoVOAntesDaAlteracao();
            setMensagemID("msg_dados_gravados");
            setSucesso(true);
            setErro(false);
            return "editar";
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
            return "editar";
        }
    }

    /**
     * Inclui o log de inclusão de pergunta
     * @throws Exception
     */
    public void incluirLogInclusao() throws Exception {
        try {
            perguntaVO.setObjetoVOAntesAlteracao(new PerguntaVO());
            perguntaVO.setNovoObj(true);
            registrarLogObjetoVO(perguntaVO, perguntaVO.getCodigo(), "PERGUNTA", 0);
            incluirLogAlteracoesRespostas();
        } catch (Exception e) {
            registrarLogErroObjetoVO("PERGUNTA", perguntaVO.getCodigo(), "ERRO AO GERAR LOG DE INCLUSÃO DE PERGUNTA", this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
        perguntaVO.setNovoObj(false);
        perguntaVO.registrarObjetoVOAntesDaAlteracao();
        perguntaVO.registrarRespostaAntesAlteracao();
    }
    
     public void incluirLogExclusao() throws Exception {
        try {
            perguntaVO.setObjetoVOAntesAlteracao(new PerguntaVO());
            perguntaVO.setNovoObj(true);
            registrarLogExclusaoTodosDadosObjetoVO(perguntaVO, perguntaVO.getCodigo(), "PERGUNTA", 0);
        } catch (Exception e) {
            registrarLogErroObjetoVO("PERGUNTA", perguntaVO.getCodigo(), "ERRO AO GERAR LOG DE EXCLUSÃO DE PERGUNTA", this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
    }

    /**
     * Inclui o log de alteração de pergunta
     * @throws Exception
     */
    public void incluirLogAlteracao() throws Exception {
        try {
            registrarLogObjetoVO(perguntaVO, perguntaVO.getCodigo(), "PERGUNTA", 0);
            incluirLogAlteracoesRespostas();
        } catch (Exception e) {
            registrarLogErroObjetoVO("PERGUNTA", perguntaVO.getCodigo(), "ERRO AO GERAR LOG DE ALTERAÇÃO DE PERGUNTA", this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
        perguntaVO.registrarObjetoVOAntesDaAlteracao();
        perguntaVO.registrarRespostaAntesAlteracao();
    }

    /**
     * Consulta de logs de pergunta
     */
    @SuppressWarnings("unchecked")
    public void realizarConsultaLogObjetoSelecionado() {
        LoginControle loginControle = (LoginControle) context().getExternalContext().getSessionMap().get("LoginControle");
        loginControle.setNomeClasse("Pergunta");
        loginControle.consultarLogObjetoSelecionado("PERGUNTA", perguntaVO.getCodigo(), null);
    }
    
     @SuppressWarnings("unchecked")
    public void realizarConsultaLogObjetoGeral() {
        perguntaVO = new PerguntaVO();
        realizarConsultaLogObjetoSelecionado();
    }

    /**
     * Rotina responsavel por executar as consultas disponiveis no JSP PerguntaCons.jsp.
     * Define o tipo de consulta a ser executada, por meio de ComboBox denominado campoConsulta, disponivel neste mesmo JSP.
     * Como resultado, disponibiliza um List com os objetos selecionados na sessao da pagina.
     */
    public String consultar() {
        try {
            super.consultar();
            List objs = new ArrayList();

            //limpar espaços em branco antes de realizar a consulta
            getControleConsulta().setValorConsulta(getControleConsulta().getValorConsulta().trim());

            if (getControleConsulta().getCampoConsulta().equals("codigo")) {
                if (getControleConsulta().getValorConsulta().equals("")) {
                    objs = getFacade().getPergunta().consultarPorCodigo(new Integer(0), true, Uteis.NIVELMONTARDADOS_TODOS);
                } else {
                    int valorInt = Integer.parseInt(getControleConsulta().getValorConsulta());
                    PerguntaVO pergunta = getFacade().getPergunta().consultarPorCodigoExato(new Integer(valorInt), true, Uteis.NIVELMONTARDADOS_TODOS);
                    if (pergunta != null) {
                        objs.add(pergunta);
                    }
                }
            }
            if (getControleConsulta().getCampoConsulta().equals("descricao")) {
                objs = getFacade().getPergunta().consultarPorDescricao(getControleConsulta().getValorConsulta(), true, Uteis.NIVELMONTARDADOS_TODOS);
            }
            if (getControleConsulta().getCampoConsulta().equals("tipoPergunta")) {
                objs = getFacade().getPergunta().consultarPorTipoPergunta(getControleConsulta().getValorConsulta(), true, Uteis.NIVELMONTARDADOS_TODOS);
            }
            objs = ControleConsulta.obterSubListPaginaApresentar(objs, controleConsulta);
            definirVisibilidadeLinksNavegacao(controleConsulta.getPaginaAtual(), controleConsulta.getNrTotalPaginas());
            setListaConsulta(objs);
            setSucesso(true);
            setErro(false);
            return "consultar";
        } catch (Exception e) {
            setListaConsulta(new ArrayList());
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
            return "consultar";
        }
    }

    public String excluir() {
        return excluir(false);
    }

    public String excluirCE() {
        return excluir(true);
    }

    /**
     * Operação responsável por processar a exclusão um objeto da classe <code>PerguntaVO</code>
     * Após a exclusão ela automaticamente aciona a rotina para uma nova inclusão.
     */
    public String excluir(boolean centralEventos) {
        try {
            if (centralEventos) {
                getFacade().getPergunta().excluir(perguntaVO, true);
                //registrar log
                registrarLogExclusaoObjetoVO(perguntaVO, perguntaVO.getCodigo().intValue(), "PERGUNTA", 0);
            } else {
                getFacade().getPergunta().excluir(perguntaVO);
                incluirLogExclusao();
            }
            setPerguntaVO(new PerguntaVO());

            setRespostaPerguntaVO(new RespostaPerguntaVO());
            setMensagemID("msg_dados_excluidos");
            setSucesso(true);
            setErro(false);
            return "consultar";
        } catch (Exception e) {
            if(e.getMessage().contains("ERRO: atualização ou exclusão em tabela \"pergunta\" viola restrição de chave estrangeira") || e.getMessage().contains("ERROR: update or delete on table \"pergunta\" violates foreign key")){
                setMensagemDetalhada("Esta Pergunta não pode ser excluída, pois está sendo utilizada!");
            }else {
                setMensagemDetalhada("msg_erro", e.getMessage());
            }
            setSucesso(false);
            setErro(true);
            return "editar";
        }
    }

    /* Método responsável por adicionar um novo objeto da classe <code>RespostaPergunta</code>
     * para o objeto <code>perguntaVO</code> da classe <code>Pergunta</code>
     */
    public void adicionarRespostaPergunta() throws Exception {
        try {
            if (!getPerguntaVO().getCodigo().equals(new Integer(0))) {
                respostaPerguntaVO.setPergunta(getPerguntaVO().getCodigo());
            }
            getPerguntaVO().adicionarObjRespostaPerguntaVOs(getRespostaPerguntaVO());
            numeraPerguntas();
            this.setRespostaPerguntaVO(new RespostaPerguntaVO());
            setMensagemID("msg_dados_adicionados");

        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());

        }
    }

    /* Método responsável por disponibilizar dados de um objeto da classe <code>RespostaPergunta</code>
     * para edição pelo usuário.
     */
    public String editarRespostaPergunta() throws Exception {
        RespostaPerguntaVO obj = (RespostaPerguntaVO) context().getExternalContext().getRequestMap().get("respostaPergunta");
        setRespostaPerguntaVO(obj);
        return "editar";
    }

    /* Método responsável por remover um novo objeto da classe <code>RespostaPergunta</code>
     * do objeto <code>perguntaVO</code> da classe <code>Pergunta</code>
     */
    public String removerRespostaPergunta() throws Exception {
        RespostaPerguntaVO obj = (RespostaPerguntaVO) context().getExternalContext().getRequestMap().get("respostaPergunta");
        getPerguntaVO().excluirObjRespostaPerguntaVOs(obj.getDescricaoRespota(), listaObjetosExcluidos);
        numeraPerguntas();
        setMensagemID("msg_dados_excluidos");
        return "editar";
    }

    public void irPaginaInicial() throws Exception {
        controleConsulta.setPaginaAtual(1);
        this.consultar();
    }

    public void irPaginaAnterior() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getPaginaAtual() - 1);
        this.consultar();
    }

    public void irPaginaPosterior() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getPaginaAtual() + 1);
        this.consultar();
    }

    public void irPaginaFinal() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getNrTotalPaginas());
        this.consultar();
    }

    public void selecionouNPS() {
        try {
            if (getPerguntaVO().isNps()){
                getPerguntaVO().setTipoPergunta("NS");
                atualizarDadosTela();


            }
        } catch (Exception ignored){
        }
    }

    public void atualizarDadosTela() {
        String valor = getPerguntaVO().getTipoPergunta();
        if (valor == null) {
            setApresentarGridVO(new Boolean(false));
            setApresentarGrid(new Boolean(false));
            getPerguntaVO().setRespostaPerguntaVOs(new ArrayList());
            setValorAnterior("");
        } else {

            if (valor.equals("NS")) {

                getPerguntaVO().setNps(true);
                setApresentarGridVO(false);
                setApresentarGrid(false);
                getPerguntaVO().setRespostaPerguntaVOs(new ArrayList<>());
                setValorAnterior("NS");

                int i = 0;
                try {
                    while (i <= 10) {
                        getRespostaPerguntaVO().setDescricaoRespota(String.valueOf(i));
                        adicionarRespostaPergunta();
                        i++;
                    }
                } catch (Exception ignored) {
                }

                setMensagemID("msg_entre_dados");
            } else if (valor.equals("TE")) {
                getPerguntaVO().setNps(false);
                setApresentarGridVO(new Boolean(false));
                setApresentarGrid(new Boolean(false));
                getPerguntaVO().setRespostaPerguntaVOs(new ArrayList());
                setValorAnterior("TE");
                setMensagemID("msg_entre_dados");
            } else if (valor.equals("SN")) {
                try {
                    getPerguntaVO().setNps(false);
                    getPerguntaVO().setRespostaPerguntaVOs(new ArrayList());
                    getRespostaPerguntaVO().setDescricaoRespota("Sim");
                    adicionarRespostaPergunta();
                    getRespostaPerguntaVO().setDescricaoRespota("Não");
                    adicionarRespostaPergunta();
                    setApresentarGridVO(new Boolean(false));
                    setApresentarGrid(new Boolean(true));
                    setValorAnterior("SN");
                } catch (Exception ex) {
                }
            } else if ((valor.equals("ME")) && ("SE".equals(valorAnterior))) {
                getPerguntaVO().setNps(false);
                setApresentarGridVO(new Boolean(true));
                setApresentarGrid(new Boolean(true));
                setValorAnterior("ME");
                setMensagemID("msg_entre_dados");
            } else if (valor.equals("ME")) {
                getPerguntaVO().setNps(false);
                setApresentarGridVO(new Boolean(true));
                setApresentarGrid(new Boolean(true));
                getPerguntaVO().setRespostaPerguntaVOs(new ArrayList());
                setValorAnterior("ME");
                setMensagemID("msg_entre_dados");
            } else if ((valor.equals("SE")) && ("ME".equals(valorAnterior))) {
                getPerguntaVO().setNps(false);
                setApresentarGridVO(new Boolean(true));
                setApresentarGrid(new Boolean(true));
                setValorAnterior("ME");
                setMensagemID("msg_entre_dados");
            } else if (valor.equals("SE")) {
                setApresentarGridVO(new Boolean(true));
                setApresentarGrid(new Boolean(true));
                getPerguntaVO().setRespostaPerguntaVOs(new ArrayList());
                setValorAnterior("SE");
                setMensagemID("msg_entre_dados");
            } else {
                getPerguntaVO().setNps(false);
                setApresentarGridVO(new Boolean(false));
                setApresentarGrid(new Boolean(false));
                getPerguntaVO().setRespostaPerguntaVOs(new ArrayList());
                setValorAnterior("");
                setMensagemID("msg_entre_dados");
            }
        }
    }

    public void apresentarDados() {
        String valor = getPerguntaVO().getTipoPergunta();
        if (valor.equals("TE")) {
            setApresentarGridVO(new Boolean(false));
            setApresentarGrid(new Boolean(false));
        } else if (valor.equals("SN")) {
            setApresentarGridVO(new Boolean(false));
            setApresentarGrid(new Boolean(true));
        } else if (valor.equals("ME")) {
            setApresentarGridVO(new Boolean(true));
            setApresentarGrid(new Boolean(true));
        } else if (valor.equals("SE")) {
            setApresentarGridVO(new Boolean(true));
            setApresentarGrid(new Boolean(true));
        } else {
            setApresentarGridVO(new Boolean(false));
            setApresentarGrid(new Boolean(false));
        }
    }

    /* Método responsável por inicializar List<SelectItem> de valores do 
     * ComboBox correspondente ao atributo <code>tipoPergunta</code>
     */
    public List getListaSelectItemTipoPerguntaPergunta() throws Exception {
        List<SelectItem> objs = new ArrayList();
        objs.add(new SelectItem("", ""));
        Hashtable tipoPerguntas = (Hashtable) Dominios.getTipoPergunta();
        Enumeration keys = tipoPerguntas.keys();
        while (keys.hasMoreElements()) {
            String value = (String) keys.nextElement();
            String label = (String) tipoPerguntas.get(value);
            objs.add(new SelectItem(value, label));
        }
        SelectItemOrdemValor ordenador = new SelectItemOrdemValor();
        Collections.sort((List) objs, ordenador);
        return objs;
    }

    /**
     * Rotina responsável por preencher a combo de consulta da telas.
     */
    public List<SelectItem> getTipoConsultaCombo() {
        List<SelectItem> itens = new ArrayList();
        itens.add(new SelectItem("codigo", "Código"));
        itens.add(new SelectItem("descricao", "Descrição"));
        itens.add(new SelectItem("tipoPergunta", "Tipo Pergunta"));
        return itens;
    }

    public void alterarCampoConsulta() {
        String campoSelecionado = getControleConsulta().getCampoConsulta();
        getControleConsulta().setValorConsulta("");
        if (campoSelecionado.equals("tipoPergunta")) {
            setCampoConsultaSelectItem(Boolean.TRUE);
        } else {
            setCampoConsultaSelectItem(Boolean.FALSE);
        }
    }

    public List getListaSelectItemConsulta() {
        List<SelectItem> itens = new ArrayList();
        itens.add(new SelectItem("", ""));

        try {
            if (getControleConsulta().getCampoConsulta().equals("tipoPergunta")) {
                return getListaSelectItemTipoPerguntaPergunta();
            }
        } catch (Exception e) {
            setErro(true);
            setSucesso(false);
            setMensagemDetalhada("msg_erro", e.getMessage());
            e.printStackTrace();
        }

        return itens;
    }

    /**
     * Rotina responsável por organizar a paginação entre as páginas resultantes de uma consulta.
     */
    public String inicializarConsultar() {
        setPaginaAtualDeTodas("0/0");
        setListaConsulta(new ArrayList());
        definirVisibilidadeLinksNavegacao(0, 0);
        setMensagemID("msg_entre_prmconsulta");
        setSucesso(false);
        setErro(false);
        return "consultar";
    }

    /**
     * Operação que inicializa as Interfaces Façades com os respectivos objetos de
     * persistência dos dados no banco de dados. 
     */
    protected boolean inicializarFacades() {
        try {
            super.inicializarFacades();

            return true;
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro_conectarBD", e.getMessage());
            return false;
        }
    }

    public RespostaPerguntaVO getRespostaPerguntaVO() {
        return respostaPerguntaVO;
    }

    public void setRespostaPerguntaVO(RespostaPerguntaVO respostaPerguntaVO) {
        this.respostaPerguntaVO = respostaPerguntaVO;
    }

    public PerguntaVO getPerguntaVO() {
        return perguntaVO;
    }

    public void setPerguntaVO(PerguntaVO perguntaVO) {
        this.perguntaVO = perguntaVO;
    }

    public Boolean getApresentarGrid() {
        return apresentarGrid;
    }

    public void setApresentarGrid(Boolean apresentarGrid) {
        this.apresentarGrid = apresentarGrid;
    }

    public Boolean getApresentarGridVO() {
        return apresentarGridVO;
    }

    public void setApresentarGridVO(Boolean apresentarGridVO) {
        this.apresentarGridVO = apresentarGridVO;
    }

    public String getValorAnterior() {
        return valorAnterior;
    }

    public void setValorAnterior(String valorAnterior) {
        this.valorAnterior = valorAnterior;
    }

    /**
     * @return the listaObjetosExcluidos
     */
    public List<RespostaPerguntaVO> getListaObjetosExcluidos() {
        return listaObjetosExcluidos;
    }

    /**
     * @param listaObjetosExcluidos the listaObjetosExcluidos to set
     */
    public void setListaObjetosExcluidos(List<RespostaPerguntaVO> listaObjetosExcluidos) {
        this.listaObjetosExcluidos = listaObjetosExcluidos;
    }

    public void setCampoConsultaSelectItem(Boolean campoConsultaSelectItem) {
        this.campoConsultaSelectItem = campoConsultaSelectItem;
    }

    public Boolean getCampoConsultaSelectItem() {
        return campoConsultaSelectItem;
    }

    public void exportar(ActionEvent evt) throws Exception {
        ExportadorListaControle exportadorListaControle = (ExportadorListaControle) JSFUtilities.getFromRequest(ExportadorListaControle.class.getSimpleName());
        String paramsTableFiltrada = context().getExternalContext().getRequestParameterMap().get("paramsTableFiltrada");

        String[] split = paramsTableFiltrada.split(",");
        String campoOrdenacao = split[0].replace("[", "");
        String ordem = split[1];
        String filtro = split[2].replace("''", "");
        filtro = filtro.replace("]", "");
        List listaParaImpressao = getFacade().getPergunta().consultarParaImpressao(filtro, ordem, campoOrdenacao, getEmpresaLogado() == null ? 0 : getEmpresaLogado().getCodigo());
        exportadorListaControle.exportar(evt, listaParaImpressao, filtro, null);

    }

    private void incluirLogAlteracoesRespostas() throws Exception {
        for(RespostaPerguntaVO atual : perguntaVO.getRespostaPerguntaVOs()){
            boolean nova = true;
            for(RespostaPerguntaVO anterior : perguntaVO.getRespostaPerguntaVOsAntesAlteracao()){
                if(anterior.getCodigo().equals(atual.getCodigo())){
                    if(!anterior.getDescricaoRespota().equals(atual.getDescricaoRespota())){
                        incluirLogAlteracaoResposta(anterior ,atual);
                    }
                    nova = false;
                    break;
                }
            }
            if(nova){
                incluirLogInclusaoResposta(atual);
            }
        }
        for(RespostaPerguntaVO anterior : perguntaVO.getRespostaPerguntaVOsAntesAlteracao()){
            boolean excluida = true;
            for(RespostaPerguntaVO atual : perguntaVO.getRespostaPerguntaVOs()){
                 if(anterior.getCodigo().equals(atual.getCodigo())){
                     excluida = false;
                     break;
                 }
            }
            if(excluida){
                incluirLogExclusaoResposta(anterior);
            }
        }
    }

    private void incluirLogAlteracaoResposta(RespostaPerguntaVO anterior, RespostaPerguntaVO atual) throws Exception {
         try{
            LogVO logVO = new LogVO();
            logVO.setOperacao("ALTERAÇÃO");
            logVO.setChavePrimaria(String.valueOf(perguntaVO.getCodigo()));
            logVO.setNomeEntidade("PERGUNTA");
            logVO.setNomeEntidadeDescricao("Pergunta - RespostaPergunta/Resposta da Pergunta");
            logVO.setResponsavelAlteracao(getUsuarioLogado().getNome());
            logVO.setUserOAMD(getUsuarioLogado().getUserOamd());
            logVO.setNomeCampo("descricaoResposta");
            logVO.setDataAlteracao(negocio.comuns.utilitarias.Calendario.hoje());
            logVO.setValorCampoAnterior(anterior.getDescricaoRespota());
            logVO.setValorCampoAlterado(atual.getDescricaoRespota());

            if (logVO != null) {
                logVO.setPessoa(0);
                getFacade().getLog().incluirSemCommit(logVO);
            }
        } catch (Exception e) {
            registrarLogErroObjetoVO("PERGUNTA", perguntaVO.getCodigo(), "ERRO AO GERAR LOG DE ALTERAÇÃO DE RESPOSTA", this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
    }

    private void incluirLogInclusaoResposta(RespostaPerguntaVO atual) throws Exception {
        try{
            LogVO logVO = new LogVO();
            logVO.setOperacao("INCLUSÃO");
            logVO.setChavePrimaria(String.valueOf(perguntaVO.getCodigo()));
            logVO.setNomeEntidade("PERGUNTA");
            logVO.setNomeEntidadeDescricao("Pergunta - RespostaPergunta/Resposta da Pergunta");
            logVO.setResponsavelAlteracao(getUsuarioLogado().getNome());
            logVO.setUserOAMD(getUsuarioLogado().getUserOamd());
            logVO.setNomeCampo("descricaoResposta");
            logVO.setDataAlteracao(negocio.comuns.utilitarias.Calendario.hoje());
            logVO.setValorCampoAnterior("");
            logVO.setValorCampoAlterado(atual.getDescricaoRespota());

            if (logVO != null) {
                logVO.setPessoa(0);
                getFacade().getLog().incluirSemCommit(logVO);
            }
        } catch (Exception e) {
            registrarLogErroObjetoVO("PERGUNTA", perguntaVO.getCodigo(), "ERRO AO GERAR LOG DE INCLUSÃO DE RESPOSTA", this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
    }

    private void incluirLogExclusaoResposta(RespostaPerguntaVO anterior) throws Exception {
        try{
            LogVO logVO = new LogVO();
            logVO.setOperacao("EXCLUSÃO");
            logVO.setChavePrimaria(String.valueOf(perguntaVO.getCodigo()));
            logVO.setNomeEntidade("PERGUNTA");
            logVO.setNomeEntidadeDescricao("Pergunta - RespostaPergunta/Resposta da Pergunta");
            logVO.setResponsavelAlteracao(getUsuarioLogado().getNome());
            logVO.setUserOAMD(getUsuarioLogado().getUserOamd());
            logVO.setNomeCampo("descricaoResposta");
            logVO.setDataAlteracao(negocio.comuns.utilitarias.Calendario.hoje());
            logVO.setValorCampoAnterior("");
            logVO.setValorCampoAlterado(anterior.getDescricaoRespota());

            if (logVO != null) {
                logVO.setPessoa(0);
                getFacade().getLog().incluirSemCommit(logVO);
            }
         } catch (Exception e) {
            registrarLogErroObjetoVO("PERGUNTA", perguntaVO.getCodigo(), "ERRO AO GERAR LOG DE EXCLUSÃO DE RESPOSTA", this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
    }

    public void setMsgAlert(String msgAlert) {
        this.msgAlert = msgAlert;
    }

    public String getMsgAlert() {
        if (msgAlert == null) {
            return "";
        }
        return msgAlert;
    }

    public void confirmarExcluir(){
        MensagemGenericaControle control = (MensagemGenericaControle) getControlador(MensagemGenericaControle.class);
        control.setMensagemDetalhada("", "");
        setMsgAlert("Richfaces.showModalPanel('mdlMensagemGenerica');");
        control.init("Exclusão de Pergunta",
                "Deseja excluir a Pergunta?",
                this, "excluir", "", "", "", "grupoBtnExcluir");
    }

    public void numeraPerguntas() {
        Iterator i = this.perguntaVO.getRespostaPerguntaVOs().iterator();
        int cont = 0;
        while (i.hasNext()) {
            RespostaPerguntaVO obj = (RespostaPerguntaVO) i.next();
            obj.setNrQuestao(cont);
            cont++;
        }
    }

    public void moverParaBaixo() {
        RespostaPerguntaVO obj = (RespostaPerguntaVO) context().getExternalContext().getRequestMap().get("respostaPergunta");
        RespostaPerguntaVO aux = new RespostaPerguntaVO();
        Iterator i = this.perguntaVO.getRespostaPerguntaVOs().iterator();
        while (i.hasNext()) {
            RespostaPerguntaVO respostaPerguntaVO = (RespostaPerguntaVO) i.next();
            if (respostaPerguntaVO.getNrQuestao() == obj.getNrQuestao() + 1) {

                aux = obj;
                obj = respostaPerguntaVO;
                respostaPerguntaVO = aux;
                this.perguntaVO.getRespostaPerguntaVOs().set(obj.getNrQuestao().intValue(), respostaPerguntaVO);
                this.perguntaVO.getRespostaPerguntaVOs().set(respostaPerguntaVO.getNrQuestao().intValue(), obj);
                break;

            }
        }
        numeraPerguntas();
    }

    public void moverParaCima() {
        RespostaPerguntaVO obj = (RespostaPerguntaVO) context().getExternalContext().getRequestMap().get("respostaPergunta");
        RespostaPerguntaVO aux = new RespostaPerguntaVO();
        Iterator i = this.perguntaVO.getRespostaPerguntaVOs().iterator();
        while (i.hasNext()) {
            RespostaPerguntaVO respostaPerguntaVO = (RespostaPerguntaVO) i.next();
            if (respostaPerguntaVO.getNrQuestao() == obj.getNrQuestao() - 1) {
                aux = obj;
                obj = respostaPerguntaVO;
                respostaPerguntaVO = aux;
                this.perguntaVO.getRespostaPerguntaVOs().set(obj.getNrQuestao().intValue(), respostaPerguntaVO);
                this.perguntaVO.getRespostaPerguntaVOs().set(respostaPerguntaVO.getNrQuestao().intValue(), obj);
                break;

            }
        }
        numeraPerguntas();
    }

}

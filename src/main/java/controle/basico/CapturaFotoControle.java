/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package controle.basico;

import br.com.pactosolucoes.comuns.notificacao.RecursoSistema;
import br.com.pactosolucoes.comuns.util.JSFUtilities;
import controle.arquitetura.SuperControle;
import java.util.Calendar;
import java.util.Date;
import javax.faces.event.ActionEvent;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import servicos.operacoes.midias.commons.MidiaEntidadeEnum;

/**
 *
 * <AUTHOR>
 */
public class CapturaFotoControle extends SuperControle {

    private Integer pessoa = 0;
    private String tipoDoc = null;
    private String retorno = "";

    public Integer getPessoa() {
        return pessoa;
    }

    public String getTipoDoc() {
        return tipoDoc;
    }

    public void setTipoDoc(String tipoDoc) {
        this.tipoDoc = tipoDoc;
    }

    public String getRetorno() {
        return retorno;
    }

    public void setRetorno(String retorno) {
        this.retorno = retorno;
    }

    public void foo() {
        Uteis.logar(null, "Retorno WEBCAM: " + retorno);
    }

    public void selecionarPessoa(ActionEvent evt) {
        notificarRecursoEmpresa(RecursoSistema.FOTO_CLIENTE);
        Integer codPessoa = (Integer) evt.getComponent().getAttributes().get("pessoa");
        JSFUtilities.storeOnSession(CapturaFotoControle.class.getSimpleName(), null);
        CapturaFotoControle controle = new CapturaFotoControle();
        controle.pessoa = codPessoa;
        JSFUtilities.storeOnSession(CapturaFotoControle.class.getSimpleName(), controle);
        if (codPessoa != null) {
            this.pessoa = codPessoa;
        }
    }

    public void selecionarTipoDocumento(ActionEvent evt) {
        MidiaEntidadeEnum tdoc = (MidiaEntidadeEnum) evt.getComponent().getAttributes().get("tipoDoc");
        Integer codPessoa = (Integer) evt.getComponent().getAttributes().get("pessoa");
        JSFUtilities.storeOnSession(CapturaFotoControle.class.getSimpleName(), null);
        CapturaFotoControle controle = new CapturaFotoControle();
        controle.tipoDoc = tdoc.toString();
        controle.pessoa = codPessoa;
        JSFUtilities.storeOnSession(CapturaFotoControle.class.getSimpleName(), controle);
    }
}

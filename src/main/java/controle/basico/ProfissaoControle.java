package controle.basico;

import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.estrutura.exportador.controle.ExportadorListaControle;
import java.util.ArrayList;
import java.util.List;

import javax.faces.model.SelectItem;

import controle.basico.clube.MensagemGenericaControle;
import negocio.comuns.basico.ProfissaoVO;
import negocio.comuns.utilitarias.ControleConsulta;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.basico.Profissao;
import controle.arquitetura.SuperControle;
import controle.arquitetura.security.LoginControle;
import javax.faces.event.ActionEvent;

/**
 * Classe responsável por implementar a interação entre os componentes JSF das páginas 
 * profissaoForm.jsp profissaoCons.jsp) com as funcionalidades da classe <code>Profissao</code>.
 * Implemtação da camada controle (Backing Bean).
 * @see SuperControle
 * @see Profissao
 * @see ProfissaoVO
 */
public class ProfissaoControle extends SuperControle {

    private ProfissaoVO profissaoVO;
    private String msgAlert;

    /**
     * Interface <code>ProfissaoInterfaceFacade</code> responsável pela interconexão da camada de controle com a camada de negócio.
     * Criando uma independência da camada de controle com relação a tenologia de persistência dos dados (DesignPatter: Façade).
     */
    public ProfissaoControle() throws Exception {
        obterUsuarioLogado();
        inicializarFacades();
        setControleConsulta(new ControleConsulta());
        setMensagemID("");
    }

    public void inicializarProfisaoControle() throws Exception {
        obterUsuarioLogado();
        inicializarFacades();
        novo();
        setControleConsulta(new ControleConsulta());
        setMensagemID("");
    }

    /**
     * Rotina responsável por disponibilizar um novo objeto da classe <code>Profissao</code>
     * para edição pelo usuário da aplicação.
     */
    public String novo() {
        setProfissaoVO(new ProfissaoVO());
        setSucesso(false);
        setErro(false);
        return "editar";
    }

    /**
     * Rotina responsável por disponibilizar os dados de um objeto da classe <code>Profissao</code> para alteração.
     * O objeto desta classe é disponibilizado na session da página (request) para que o JSP correspondente possa disponibilizá-lo para edição.
     */
    public String editar() {
        Integer codigoConsulsta = Integer.parseInt(request().getParameter("chavePrimaria"));
        try {
            ProfissaoVO obj = getFacade().getProfissao().consultarPorChavePrimaria(codigoConsulsta, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            obj.setNovoObj(false);
            obj.registrarObjetoVOAntesDaAlteracao();
            setProfissaoVO(new ProfissaoVO());
            setProfissaoVO(obj);
        } catch (Exception e) {
            setErro(true);
            setSucesso(false);
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
        return "editar";
    }

    /**
     * Rotina responsável por gravar no BD os dados editados de um novo objeto da classe <code>Profissao</code>.
     * Caso o objeto seja novo (ainda não gravado no BD) é acionado a operação <code>incluir()</code>. Caso contrário é acionado o <code>alterar()</code>.
     * Se houver alguma inconsistência o objeto não é gravado, sendo re-apresentado para o usuário juntamente com uma mensagem de erro.
     */
    public String gravar(boolean centralEventos) {
        try {
            if (centralEventos) {
                this.verificarAutorizacao();
                if (profissaoVO.isNovoObj().booleanValue()) {
                    getFacade().getProfissao().incluir(profissaoVO, true);
                    incluirLogInclusao();
                } else {
                    getFacade().getProfissao().alterar(profissaoVO, true);
                    incluirLogAlteracao();
                }
            } else {
                if (profissaoVO.isNovoObj().booleanValue()) {
                    getFacade().getProfissao().incluir(profissaoVO);
                    incluirLogInclusao();
                } else {
                    getFacade().getProfissao().alterar(profissaoVO);
                    incluirLogAlteracao();
                }
            }
            setMensagemID("msg_dados_gravados");
            setSucesso(true);
            setErro(false);
            return "editar";
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
            return "editar";
        }
    }

    /**
     * Inclui o log de inclusão de profissão
     * @throws Exception
     */
    public void incluirLogInclusao() throws Exception {
        try {
            profissaoVO.setObjetoVOAntesAlteracao(new ProfissaoVO());
            profissaoVO.setNovoObj(true);
            registrarLogObjetoVO(profissaoVO, profissaoVO.getCodigo(), "PROFISSAO", 0);
      
        } catch (Exception e) {
            registrarLogErroObjetoVO("PROFISSAO", profissaoVO.getCodigo(), "ERRO AO GERAR LOG DE INCLUSÃO DE PROFISSAO", this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
        profissaoVO.setNovoObj(false);
        profissaoVO.registrarObjetoVOAntesDaAlteracao();
    }
    
    /**
     * Inclui o log de inclusão de profissão
     * @throws Exception
     */
    public void incluirLogExclusao() throws Exception {
        try {
            profissaoVO.setObjetoVOAntesAlteracao(new ProfissaoVO());
            profissaoVO.setNovoObj(true);
            registrarLogExclusaoTodosDadosObjetoVO(profissaoVO, profissaoVO.getCodigo(), "PROFISSAO", 0);
        } catch (Exception e) {
            registrarLogErroObjetoVO("PROFISSAO", profissaoVO.getCodigo(), "ERRO AO GERAR LOG DE EXCLUSÃO DE PROFISSAO", this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
    }

    /**
     * Inclui o log de alteração de profissão
     * @throws Exception
     */
    public void incluirLogAlteracao() throws Exception {
        try {
            registrarLogObjetoVO(profissaoVO, profissaoVO.getCodigo(), "PROFISSAO", 0);
        } catch (Exception e) {
            registrarLogErroObjetoVO("PROFISSAO", profissaoVO.getCodigo(), "ERRO AO GERAR LOG DE ALTERAÇÃO DE PROFISSAO", this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
        profissaoVO.registrarObjetoVOAntesDaAlteracao();
    }

    /**
     * <AUTHOR>
     * 24/03/2011
     * @return
     */
    public String gravarCE() {
        return this.gravar(true);
    }

    /**
     * <AUTHOR>
     * 24/03/2011
     * @return
     */
    public String gravar() {
        return this.gravar(false);
    }

    /**
     * Rotina responsavel por executar as consultas disponiveis no JSP ProfissaoCons.jsp.
     * Define o tipo de consulta a ser executada, por meio de ComboBox denominado campoConsulta, disponivel neste mesmo JSP.
     * Como resultado, disponibiliza um List com os objetos selecionados na sessao da pagina.
     */
    public String consultar() {
        try {
            super.consultar();
            List objs = new ArrayList();

            //limpar espaços em branco antes de realizar a consulta
            getControleConsulta().setValorConsulta(getControleConsulta().getValorConsulta().trim());

            if (getControleConsulta().getCampoConsulta().equals("codigo")) {
                if (getControleConsulta().getValorConsulta().equals("")) {
                    objs = getFacade().getProfissao().consultarPorCodigo(new Integer(0), true, Uteis.NIVELMONTARDADOS_TODOS);
                } else {
                    int valorInt = Integer.parseInt(getControleConsulta().getValorConsulta());
                    ProfissaoVO profissao = getFacade().getProfissao().consultarPorCodigoExato(new Integer(valorInt), true, Uteis.NIVELMONTARDADOS_TODOS);
                    if (profissao != null) {
                        objs.add(profissao);
                    }
                }
            }
            if (getControleConsulta().getCampoConsulta().equals("descricao")) {
                objs = getFacade().getProfissao().consultarPorDescricao(getControleConsulta().getValorConsulta(), true, Uteis.NIVELMONTARDADOS_TODOS);
            }
            objs = ControleConsulta.obterSubListPaginaApresentar(objs, controleConsulta);
            definirVisibilidadeLinksNavegacao(controleConsulta.getPaginaAtual(), controleConsulta.getNrTotalPaginas());
            setListaConsulta(objs);
            setSucesso(true);
            setErro(false);
            return "consultar";
        } catch (Exception e) {
            setListaConsulta(new ArrayList());
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
            return "consultar";
        }
    }

    public String excluir() {
        return this.excluir(false);
    }

    public String excluirCE() {
        return this.excluir(true);
    }

    /**
     * Operação responsável por processar a exclusão um objeto da classe <code>ProfissaoVO</code>
     * Após a exclusão ela automaticamente aciona a rotina para uma nova inclusão.
     */
    public String excluir(boolean centralEventos) {
        try {
            if (centralEventos) {
                getFacade().getProfissao().excluir(profissaoVO, true);
                //registrar log
                registrarLogExclusaoObjetoVO(profissaoVO, profissaoVO.getCodigo().intValue(), "PROFISSAO", 0);
            } else {
                getFacade().getProfissao().excluir(profissaoVO);
                incluirLogExclusao();
            }

            setProfissaoVO(new ProfissaoVO());
            setMensagemID("msg_dados_excluidos");
            setSucesso(true);
            setErro(false);
            return "consultar";
        } catch (Exception e) {
            if(e.getMessage().contains("ERRO: atualização ou exclusão em tabela \"profissao\" viola restrição de chave estrangeira") || e.getMessage().contains("ERROR: update or delete on table \"profissao\" violates foreign key")){
                setMensagemDetalhada("Esta Profissão não pode ser excluída, pois está sendo utilizada!");
            }else {
                setMensagemDetalhada("msg_erro", e.getMessage());
            }
            setSucesso(false);
            setErro(true);
            return "editar";
        }
    }

    public void irPaginaInicial() throws Exception {
        controleConsulta.setPaginaAtual(1);
        this.consultar();
    }

    public void irPaginaAnterior() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getPaginaAtual() - 1);
        this.consultar();
    }

    public void irPaginaPosterior() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getPaginaAtual() + 1);
        this.consultar();
    }

    public void irPaginaFinal() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getNrTotalPaginas());
        this.consultar();
    }

    /**
     * Rotina responsável por preencher a combo de consulta da telas.
     */
    public List<SelectItem> getTipoConsultaCombo() {
        List<SelectItem> itens = new ArrayList();
        itens.add(new SelectItem("codigo", "Código"));
        itens.add(new SelectItem("descricao", "Descrição"));
        return itens;
    }

    /**
     * Rotina responsável por organizar a paginação entre as páginas resultantes de uma consulta.
     */
    public String inicializarConsultar() {
        setPaginaAtualDeTodas("0/0");
        setListaConsulta(new ArrayList());
        definirVisibilidadeLinksNavegacao(0, 0);
        setMensagemID("");
        setSucesso(false);
        setErro(false);
        return "consultar";
    }

    /**
     * Operação que inicializa as Interfaces Façades com os respectivos objetos de
     * persistência dos dados no banco de dados. 
     */
    protected boolean inicializarFacades() {
        try {
            super.inicializarFacades();

            return true;
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro_conectarBD", e.getMessage());
            return false;
        }
    }

    public ProfissaoVO getProfissaoVO() {
        return profissaoVO;
    }

    public void setProfissaoVO(ProfissaoVO profissaoVO) {
        this.profissaoVO = profissaoVO;
    }

    public void exportar(ActionEvent evt) throws Exception {
        ExportadorListaControle exportadorListaControle = (ExportadorListaControle) JSFUtilities.getFromRequest(ExportadorListaControle.class.getSimpleName());
        String paramsTableFiltrada = context().getExternalContext().getRequestParameterMap().get("paramsTableFiltrada");

        String[] split = paramsTableFiltrada.split(",");
        String campoOrdenacao = split[0].replace("[", "");
        String ordem = split[1];
        String filtro = split[2].replace("''", "");
        filtro = filtro.replace("]", "");
        List listaParaImpressao = getFacade().getProfissao().consultarParaImpressao(filtro, ordem, campoOrdenacao, getEmpresaLogado() == null ? 0 : getEmpresaLogado().getCodigo());
        exportadorListaControle.exportar(evt, listaParaImpressao, filtro, null);

    }

    /**
     * Consulta de logs de profissao
     */
    @SuppressWarnings("unchecked")
    public void realizarConsultaLogObjetoSelecionado() {
        LoginControle loginControle = (LoginControle) context().getExternalContext().getSessionMap().get("LoginControle");
        loginControle.setNomeClasse("Profissão");
        loginControle.consultarLogObjetoSelecionado("PROFISSAO", profissaoVO.getCodigo(), null);
    }
    @SuppressWarnings("unchecked")
    public void realizarConsultaLogObjetoGeral() {
        profissaoVO = new ProfissaoVO();
        realizarConsultaLogObjetoSelecionado();
    }

    public void setMsgAlert(String msgAlert) {
        this.msgAlert = msgAlert;
    }

    public String getMsgAlert() {
        if (msgAlert == null) {
            return "";
        }
        return msgAlert;
    }

    public void confirmarExcluir(){
        MensagemGenericaControle control = (MensagemGenericaControle) getControlador(MensagemGenericaControle.class);
        control.setMensagemDetalhada("", "");
        setMsgAlert("Richfaces.showModalPanel('mdlMensagemGenerica');");
        control.init("Exclusão de Profissão",
                "Deseja excluir a Profissão?",
                this, "excluir", "", "", "", "grupoBtnExcluir");
    }

}

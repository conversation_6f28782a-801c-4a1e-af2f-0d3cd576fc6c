package controle.basico;

import br.com.pactosolucoes.comuns.util.Formatador;
import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.estrutura.exportador.controle.ExportadorListaControle;
import controle.arquitetura.SuperControle;
import controle.arquitetura.security.LoginControle;
import negocio.comuns.basico.GympassDiaVO;
import negocio.comuns.basico.GympassVO;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;

import javax.faces.event.ActionEvent;
import java.util.ArrayList;
import java.util.List;

public class GympassControle extends SuperControle {

    private GympassVO gympassVO;
    private String onComplete;
    private Double[] valores;

    public GympassControle() throws Exception {
        obterUsuarioLogado();
        inicializarFacades();
        setMensagemID("");
    }

    public String novo() {
        try {
            valores = new Double[] {0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0};
            gympassVO = new GympassVO();
            updateCamposTela();
            limparMsg();
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
        }
        return "editar";
    }

    public String consultar() {
        try {
            super.consultar();
            List objs = new ArrayList();

            //limpar espaços em branco antes de realizar a consulta
            getControleConsulta().setValorConsulta(getControleConsulta().getValorConsulta().trim());

            if (getControleConsulta().getCampoConsulta().equals("codigo")) {
                if (getControleConsulta().getValorConsulta().equals("")) {
                    objs = getFacade().getGympass().consultar(Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                } else {
                    int valorInt = Integer.parseInt(getControleConsulta().getValorConsulta());
                    GympassVO aux = getFacade().getGympass().buscarPorCodigo(valorInt, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                    if(aux != null) {
                        objs.add(aux);
                    }
                }
            }
            objs = getControleConsulta().obterSubListPaginaApresentar(objs, controleConsulta);
            definirVisibilidadeLinksNavegacao(controleConsulta.getPaginaAtual(), controleConsulta.getNrTotalPaginas());
            setListaConsulta(objs);
            setSucesso(true);
            setErro(false);
            return "consultar";
        } catch (Exception e) {
            setListaConsulta(new ArrayList());
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
            return "consultar";
        }
    }

    public String inicializarConsultar(){
        setPaginaAtualDeTodas("0/0");
        setListaConsulta(new ArrayList());
        definirVisibilidadeLinksNavegacao(0, 0);
        setMensagemID("msg_entre_prmconsulta");
        setSucesso(false);
        setErro(false);

        return "consultar";
    }

    public void gravar() {
        try {
            updateCamposVO();
            if (gympassVO.getCodigo() == null || gympassVO.getCodigo() < 1) {
                verificaPeriodoTabela();
                getFacade().getGympass().incluir(gympassVO);
                incluirLogInclusao();
//                notificarRecursoEmpresa(RecursoSistema.CADASTRO_REPASSE_GYMPASS);
                montarSucessoGrowl("Gympass cadastrado com sucesso!");
            } else {
                getFacade().getGympass().alterar(gympassVO);
                incluirLogAlteracao();
                montarSucessoGrowl("Gympass ("
                        + Formatador.formatarData(gympassVO.getDataInicial(), "dd/MM/YYYY") + " até "
                        + Formatador.formatarData(gympassVO.getDataFinal(), "dd/MM/YYYY") + ") alterado com sucesso!");
            }
        } catch (Exception e) {
            montarErro(e);
        }
    }

    public String editar(){
        Integer codigoConsulta = Integer.parseInt(request().getParameter("chavePrimaria"));
        try {
            valores = new Double[] {0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0};
            setGympassVO(getFacade().getGympass().buscarPorCodigo(codigoConsulta, Uteis.NIVELMONTARDADOS_TODOS));
            getGympassVO().registrarObjetoVOAntesDaAlteracao();
            updateCamposTela();
        } catch (Exception e) {
            setErro(true);
            setSucesso(false);
            setMensagemDetalhada("msg_erro", e.getMessage());
            Uteis.logar(e, GympassControle.class);
            return "";
        }
        return "editar";
    }

    public void validarDadosExclusao(){
        try {
            setOnComplete("");
            if (getGympassVO().getCodigo() == 0) {
                throw new Exception("Selecione um Gympass para a exclusão");
            }
            setOnComplete("Richfaces.showModalPanel('mdlAvisoExcluirGympass')");
        } catch (Exception e) {
            montarErro(e);
            setOnComplete(getMensagemNotificar());
        }
    }

    public void excluir(){
        try {
            getFacade().getGympass().excluir(getGympassVO());
            incluirLogExclusao();
            setGympassVO(new GympassVO());
            montarSucessoGrowl("Gympass excluido com Sucesso!");
        } catch (Exception e) {
            montarErro(e);
        }
    }

    private void incluirLogInclusao() throws Exception {
        //LOG - INICIO
        try {
            gympassVO.setObjetoVOAntesAlteracao(new GympassVO());
            gympassVO.setNovoObj(true);
            registrarLogObjetoVO(gympassVO, gympassVO.getCodigo(), "Gympass", 0,true);
        } catch (Exception e) {
            registrarLogErroObjetoVO("GYMPASS", gympassVO.getCodigo(), "ERRO AO GERAR LOG DE INCLUSÃO DE GYMPASS",
                    this.getUsuarioLogado().getNome(), this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
        gympassVO.setNovoObj(new Boolean(false));
        gympassVO.registrarObjetoVOAntesDaAlteracao();
        //LOG - FIM
    }

    public void incluirLogAlteracao() throws Exception {
        //LOG - INICIO
        try {
            registrarLogObjetoVO(gympassVO, gympassVO.getCodigo(), "Gympass", 0,false);
        } catch (Exception e) {
            registrarLogErroObjetoVO("GYMPASS", gympassVO.getCodigo(), "ERRO AO GERAR LOG DE ALTERAÇÃO DE GYMPASS",
                    this.getUsuarioLogado().getNome(), this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
        gympassVO.registrarObjetoVOAntesDaAlteracao();
        //LOG - FIM
    }

    public void incluirLogExclusao() throws Exception{
        //LOG - INICIO
        try {
            gympassVO.setObjetoVOAntesAlteracao(new GympassVO());
            gympassVO.setNovoObj(true);
            registrarLogExclusaoTodosDadosObjetoVO(gympassVO, gympassVO.getCodigo(),"GYMPASS",0);
        } catch (Exception e) {
            registrarLogErroObjetoVO("GYMPASS", gympassVO.getCodigo(),"ERRO AO GERAR LOG DE EXCLUSÃO DO BRINDE",
                    this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
        //LOG - FIM
    }

    public void realizarConsultaLogObjetoSelecionado() {
        LoginControle loginControle = (LoginControle) context().getExternalContext().getSessionMap().get("LoginControle");
        loginControle.setNomeClasse("Gympass");
        loginControle.consultarLogObjetoSelecionado("GYMPASS", gympassVO.getCodigo(), null);
    }

    public void realizarConsultaLogObjetoGeral() {
        gympassVO = new GympassVO();
        realizarConsultaLogObjetoSelecionado();
    }

    public void exportar(ActionEvent evt) throws Exception{
        ExportadorListaControle exportadorListaControle = (ExportadorListaControle) JSFUtilities.getFromRequest(ExportadorListaControle.class.getSimpleName());
        String paramsTableFiltrada = context().getExternalContext().getRequestParameterMap().get("paramsTableFiltrada");
        String[] split = paramsTableFiltrada.split(",");
        String campoOrdenacao = split[0].replace("[", "").replace("<span>", "").replace("</span>", "");
        String ordem = split[1];
        String filtro = split[2].replace("''", "");
        filtro = filtro.replace("]", "");
        List listaParaImpressao = getFacade().getGympass().consultarParaImpressao(filtro, ordem, campoOrdenacao);
        exportadorListaControle.exportar(evt, listaParaImpressao, filtro, null);
    }

    protected boolean inicializarFacades() {
        try {
            super.inicializarFacades();
            return true;
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro_conectarBD", e.getMessage());
            return false;
        }
    }

    public GympassVO getGympassVO() {
        return gympassVO;
    }

    public void setGympassVO(GympassVO gympassVO) {
        this.gympassVO = gympassVO;
    }

    public String getOnComplete() {
        if (onComplete == null)
            onComplete = "";
        return onComplete;
    }

    public void setOnComplete(String GympassLog) {
        this.onComplete = GympassLog;
    }

    private void updateCamposVO() {
        for (int i = 0; i < 9; i++) {
            Double valor = valores[i];
            GympassDiaVO dia = new GympassDiaVO();
            dia.setSequencia(i);
            if (gympassVO.getDias().contains(dia)) {
                int aux = gympassVO.getDias().indexOf(dia);
                gympassVO.getDias().get(aux).setValorDia(valor);
            } else {
                dia.setValorDia(valor);
                gympassVO.getDias().add(dia);
            }
        }
    }

    private void updateCamposTela() {
        if (gympassVO.getDias() == null) return;
        for (GympassDiaVO dia : gympassVO.getDias()) {
            valores[dia.getSequencia()] = dia.getValorDia();
        }
    }

    private void verificaPeriodoTabela() throws Exception {
        GympassVO.validarPeriodo(gympassVO);
        GympassVO aux = getFacade().getGympass().buscarPorTabelaAtivaPeriodo(
                gympassVO.getDataInicial(),  gympassVO.getDataFinal(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        if (aux != null) {
            throw new ConsistirException("Já existe tabela Gympass ativa no período de ("+
                    Uteis.getData(aux.getDataInicial(), "br")+" - "+
                    Uteis.getData(aux.getDataFinal(), "br")+").");
        }
    }

    public Double[] getValores() {
        return valores;
    }

    public void setValores(Double[] valores) {
        this.valores = valores;
    }
}

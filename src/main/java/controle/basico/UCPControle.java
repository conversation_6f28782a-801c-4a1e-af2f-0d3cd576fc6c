/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package controle.basico;

import controle.arquitetura.SuperControle;
import negocio.comuns.basico.*;

import java.util.*;

/*
 * <AUTHOR>
 */

public class UCPControle extends SuperControle {

    public static List<IntegracaoTagUcpTO> listaIntegracaoTagUcp = new ArrayList<IntegracaoTagUcpTO>();
    public static List<PerguntaUcpTO> listaTodosConhecimentosUCP = new ArrayList<PerguntaUcpTO>();
    public static List<PerguntaUcpTO> listaConhecimentosFundamentais = new ArrayList<PerguntaUcpTO>();
    public static List<IntegracaoTagUcpTO> listaTelaConhecimentosUCP = new ArrayList<IntegracaoTagUcpTO>();

    public UCPControle() throws Exception {

    }

    public List<PerguntaUcpTO> getListaConhecimentosFundamentais() {
        if (listaConhecimentosFundamentais == null) {
            listaConhecimentosFundamentais = new ArrayList<PerguntaUcpTO>();
        }
        return listaConhecimentosFundamentais;
    }

}

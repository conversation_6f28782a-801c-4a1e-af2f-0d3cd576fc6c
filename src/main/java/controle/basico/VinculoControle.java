package controle.basico;

import java.util.Iterator;

import br.com.pactosolucoes.enumeradores.TipoColaboradorEnum;
import negocio.facade.jdbc.basico.Vinculo;
import negocio.comuns.utilitarias.*;
import negocio.comuns.basico.*;
import javax.faces.model.SelectItem;
import java.util.List;
import java.util.ArrayList;
import controle.arquitetura.SuperControle;
import java.util.Date;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.contrato.ContratoModalidadeHorarioTurmaVO;
import negocio.comuns.contrato.ContratoModalidadeTurmaVO;
import negocio.comuns.contrato.ContratoModalidadeVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.crm.VinculosTipoTO;
import negocio.comuns.plano.HorarioTurmaVO;
import static negocio.facade.jdbc.arquitetura.FacadeManager.getFacade;
import relatorio.negocio.comuns.sad.SituacaoClienteSinteticoEnum;

/**
 * Classe responsável por implementar a interação entre os componentes JSF das páginas 
 * vinculoForm.jsp vinculoCons.jsp) com as funcionalidades da classe <code>Vinculo</code>.
 * Implemtação da camada controle (Backing Bean).
 * @see SuperControle
 * @see Vinculo
 * @see VinculoVO
 */
public class VinculoControle extends SuperControle {
    private VinculoVO vinculoVO;
    protected List listaSelectItemCliente;
    protected List listaSelectItemColaborador;

    public VinculoControle() throws Exception {
        obterUsuarioLogado();
        inicializarFacades();
        setControleConsulta(new ControleConsulta());
        setMensagemID("");
    }

    public String novo() {
        setVinculoVO(new VinculoVO());
        inicializarListasSelectItemTodosComboBox();
        setMensagemID("msg_entre_dados");
        return "editar";
    }

    public String editar() {
        VinculoVO obj = (VinculoVO) context().getExternalContext().getRequestMap().get("vinculo");
        inicializarAtributosRelacionados(obj);
        obj.setNovoObj(new Boolean(false));
        setVinculoVO(obj);
        inicializarListasSelectItemTodosComboBox();
        setMensagemID("msg_dados_editar");
        return "editar";
    }

    public void inicializarAtributosRelacionados(VinculoVO obj) {
        if (obj.getCliente() == null) {
            obj.setCliente(new ClienteVO());
        }
        if (obj.getColaborador() == null) {
            obj.setColaborador(new ColaboradorVO());
        }
    }

    public String gravar() {
        try {
            if (vinculoVO.isNovoObj().booleanValue()) {
                getFacade().getVinculo().incluir(vinculoVO, "VINCULO", null);
            } else {
                getFacade().getVinculo().alterar(vinculoVO, "VINCULO", null);
            }
            setMensagemID("msg_dados_gravados");
            return "editar";
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            return "editar";
        }
    }

    public String consultar() {
        try {
            super.consultar();
            List objs = new ArrayList();
            if (getControleConsulta().getCampoConsulta().equals("codigo")) {
                if (getControleConsulta().getValorConsulta().equals("")) {
                    getControleConsulta().setValorConsulta("0");
                }
                int valorInt = Integer.parseInt(getControleConsulta().getValorConsulta());
                objs = getFacade().getVinculo().consultarPorCodigo(new Integer(valorInt), true, Uteis.NIVELMONTARDADOS_TODOS);
            }
            if (getControleConsulta().getCampoConsulta().equals("matriculaCliente")) {
                objs = getFacade().getVinculo().consultarPorMatriculaCliente(getControleConsulta().getValorConsulta(), Uteis.NIVELMONTARDADOS_TODOS);
            }
            if (getControleConsulta().getCampoConsulta().equals("situacaoColaborador")) {
                objs = getFacade().getVinculo().consultarPorSituacaoColaborador(getControleConsulta().getValorConsulta(), Uteis.NIVELMONTARDADOS_TODOS);
            }
            objs = ControleConsulta.obterSubListPaginaApresentar(objs, controleConsulta);
            definirVisibilidadeLinksNavegacao(controleConsulta.getPaginaAtual(), controleConsulta.getNrTotalPaginas());
            setListaConsulta(objs);
            setMensagemID("msg_dados_consultados");
            return "consultar";
        } catch (Exception e) {
            setListaConsulta(new ArrayList());
            setMensagemDetalhada("msg_erro", e.getMessage());
            return "consultar";
        }
    }

    public String excluir() {
        try {
            getFacade().getVinculo().excluir(vinculoVO);
            setVinculoVO(new VinculoVO());
            setMensagemID("msg_dados_excluidos");
            return "editar";
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            return "editar";
        }
    }

    public void irPaginaInicial() throws Exception {
        controleConsulta.setPaginaAtual(1);
        this.consultar();
    }

    public void irPaginaAnterior() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getPaginaAtual() - 1);
        this.consultar();
    }

    public void irPaginaPosterior() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getPaginaAtual() + 1);
        this.consultar();
    }

    public void irPaginaFinal() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getNrTotalPaginas());
        this.consultar();
    }

    /**
     * Método responsável por gerar uma lista de objetos do tipo <code>SelectItem</code> para preencher
     * o comboBox relativo ao atributo <code>Colaborador</code>.
     */
    public void montarListaSelectItemColaborador(String prm) throws Exception {
        List resultadoConsulta = consultarColaboradorPorSituacao(prm);
        Iterator i = resultadoConsulta.iterator();
        List objs = new ArrayList();
        objs.add(new SelectItem(new Integer(0), ""));
        while (i.hasNext()) {
            ColaboradorVO obj = (ColaboradorVO) i.next();
            objs.add(new SelectItem(obj.getCodigo(), obj.getSituacao().toString()));
        }
        setListaSelectItemColaborador(objs);
    }

    /**
     * Método responsável por atualizar o ComboBox relativo ao atributo <code>Colaborador</code>.
     * Buscando todos os objetos correspondentes a entidade <code>Colaborador</code>. Esta rotina não recebe parâmetros para filtragem de dados, isto é
     * importante para a inicialização dos dados da tela para o acionamento por meio requisições Ajax.
     */
    public void montarListaSelectItemColaborador() {
        try {
            montarListaSelectItemColaborador("");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * Método responsável por consultar dados da entidade <code><code> e montar o atributo <code>situacao</code>
     * Este atributo é uma lista (<code>List</code>) utilizada para definir os valores a serem apresentados no ComboBox correspondente
     */
    public List consultarColaboradorPorSituacao(String situacaoPrm) throws Exception {
        List lista = getFacade().getColaborador().consultarPorSituacao(situacaoPrm, getEmpresaLogado().getCodigo().intValue(), false, Uteis.NIVELMONTARDADOS_TODOS);
        return lista;
    }

    /**
     * Método responsável por gerar uma lista de objetos do tipo <code>SelectItem</code> para preencher
     * o comboBox relativo ao atributo <code>Cliente</code>.
     */
    public void montarListaSelectItemCliente(String prm) throws Exception {
        List resultadoConsulta = consultarClientePorMatricula(prm);
        Iterator i = resultadoConsulta.iterator();
        List objs = new ArrayList();
        objs.add(new SelectItem(new Integer(0), ""));
        while (i.hasNext()) {
            ClienteVO obj = (ClienteVO) i.next();
            objs.add(new SelectItem(obj.getCodigo(), obj.getMatricula().toString()));
        }
        setListaSelectItemCliente(objs);
    }

    /**
     * Método responsável por atualizar o ComboBox relativo ao atributo <code>Cliente</code>.
     * Buscando todos os objetos correspondentes a entidade <code>Cliente</code>. Esta rotina não recebe parâmetros para filtragem de dados, isto é
     * importante para a inicialização dos dados da tela para o acionamento por meio requisições Ajax.
     */
    public void montarListaSelectItemCliente() {
        try {
            montarListaSelectItemCliente("");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * Método responsável por consultar dados da entidade <code><code> e montar o atributo <code>matricula</code>
     * Este atributo é uma lista (<code>List</code>) utilizada para definir os valores a serem apresentados no ComboBox correspondente
     */
    public List consultarClientePorMatricula(String matriculaPrm) throws Exception {
        List lista = getFacade().getCliente().consultarPorMatricula(matriculaPrm, getEmpresaLogado().getCodigo().intValue(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        return lista;
    }

    /**
     * Método responsável por inicializar a lista de valores (<code>SelectItem</code>) para todos os ComboBox's.
     */
    public void inicializarListasSelectItemTodosComboBox() {
        montarListaSelectItemCliente();
        montarListaSelectItemColaborador();
    }

    /**
     * Rotina responsável por preencher a combo de consulta da telas.
     */
    public List getTipoConsultaCombo() {
        List itens = new ArrayList();
        itens.add(new SelectItem("codigo", "Código"));
        itens.add(new SelectItem("matriculaCliente", "Cliente"));
        itens.add(new SelectItem("situacaoColaborador", "Colaborador"));
        return itens;
    }

    /**
     * Rotina responsável por organizar a paginação entre as páginas resultantes de uma consulta.
     */
    public String inicializarConsultar() {
        setPaginaAtualDeTodas("0/0");
        setListaConsulta(new ArrayList());
        definirVisibilidadeLinksNavegacao(0, 0);
        setMensagemID("msg_entre_prmconsulta");
        return "consultar";
    }

    /**
     * Operação que inicializa as Interfaces Façades com os respectivos objetos de
     * persistência dos dados no banco de dados. 
     */
    protected boolean inicializarFacades() {
        try {
            super.inicializarFacades();

            return true;
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro_conectarBD", e.getMessage());
            return false;
        }
    }

    public List getListaSelectItemColaborador() {
        return (listaSelectItemColaborador);
    }

    public void setListaSelectItemColaborador(List listaSelectItemColaborador) {
        this.listaSelectItemColaborador = listaSelectItemColaborador;
    }

    public List getListaSelectItemCliente() {
        return (listaSelectItemCliente);
    }

    public void setListaSelectItemCliente(List listaSelectItemCliente) {
        this.listaSelectItemCliente = listaSelectItemCliente;
    }

    public VinculoVO getVinculoVO() {
        return vinculoVO;
    }

    public void setVinculoVO(VinculoVO vinculoVO) {
        this.vinculoVO = vinculoVO;
    }

// * processamento de vinculos do contrato *************************************

    public static void processarVinculosNegociacao(ContratoVO contrato) throws Exception {
        ClienteVO cliente = getFacade().getCliente().consultarPorCodigoPessoa(contrato.getPessoa().getCodigo(),
                        Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
        boolean possuiContratoAtivo = getFacade().getContrato().possuiContratoVigentePessoa(cliente.getPessoa().getCodigo(),contrato.getCodigo());
        if(!contrato.isContratoMatricula() || !possuiContratoAtivo) {
            ContratoVO contratoAntigo;
            List<ContratoModalidadeVO> contratoModalidades = new ArrayList<ContratoModalidadeVO>();
            // pega o contrato que foi rematriculado ou renovado
            if(contrato.isContratoRematricula()) {
                contratoAntigo = getFacade().getContrato().consultarPorChavePrimaria(
                        contrato.getContratoBaseadoRematricula(), Uteis.NIVELMONTARDADOS_DADOSENTIDADESUBORDINADAS);
                contratoModalidades.addAll( contratoAntigo.getContratoModalidadeVOs());
            } else if(!contrato.isContratoMatricula()){
                contratoAntigo = getFacade().getContrato().consultarPorChavePrimaria(
                        contrato.getContratoBaseadoRenovacao(), Uteis.NIVELMONTARDADOS_DADOSENTIDADESUBORDINADAS);
                contratoModalidades.addAll( contratoAntigo.getContratoModalidadeVOs());
            }
            if(!possuiContratoAtivo) {
               List<ContratoVO> contratos = getFacade().getContrato().consultarContratoCancelado(cliente.getPessoa().getCodigo(),Uteis.NIVELMONTARDADOS_DADOSENTIDADESUBORDINADAS);
               for(ContratoVO obj : contratos){
                   contratoModalidades.addAll(obj.getContratoModalidadeVOs());
               }
            }
            // pega todos os contratos ativos ou trancados
            List<ContratoVO> contratos = getFacade().getContrato().consultarPorSituacaoContratoECodigoPessoa(
                    "AT','TR", contrato.getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSENTIDADESUBORDINADAS);
            processaParaExclusao(contrato.getVigenciaDe(), contratoModalidades, cliente.getCodigo(), contratos, "NEGOCIAÇÃO", contrato.getResponsavelContrato());
        }
        incluirVinculos(contrato, cliente, contrato.getVigenciaDe(), "NEGOCIAÇÃO", contrato.getResponsavelContrato());
    }

    public static void processarVinculosManutencaoModalidade(ContratoVO contrato, ContratoVO contratoAntigo, UsuarioVO responsavel) throws Exception {
        ClienteVO cliente = getFacade().getCliente().consultarPorCodigoPessoa(contrato.getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
        // pega todos os contratos ativos ou trancados
        List<ContratoVO> contratos = getFacade().getContrato().consultarPorSituacaoContratoECodigoPessoa("AT','TR", contrato.getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSENTIDADESUBORDINADAS);
        atualizaContrato(contrato);
        atualizaLista(contratos, contrato);
        if(Calendario.maior(contrato.getVigenciaDe(), Calendario.hoje())) {
            processaParaExclusao(contrato.getVigenciaDe(), contratoAntigo.getContratoModalidadeVOs(), cliente.getCodigo(), contratos, "MANUTENÇÃO MODALIDADE", responsavel);
            incluirVinculos(contrato, cliente, contrato.getVigenciaDe(), "MANUTENÇÃO MODALIDADE", responsavel);
        } else {
            processaParaExclusao(Calendario.hoje(), contratoAntigo.getContratoModalidadeVOs(), cliente.getCodigo(), contratos, "MANUTENÇÃO MODALIDADE", responsavel);
            incluirVinculos(contrato, cliente, Calendario.hoje(), "MANUTENÇÃO MODALIDADE", responsavel);
        }
    }

    private static void atualizaContrato(ContratoVO contrato) {
        // percorre a lista de modalidades
        for(ContratoModalidadeVO modalidade : contrato.getContratoModalidadeVOs()) {
            // se a modalidade usa turma
            if(modalidade.getModalidade().isUtilizarTurma()) {
                // percorre a lista de turmas de cada modalidade
                for (Object obj : modalidade.getContratoModalidadeTurmaVOs()) {
                    ContratoModalidadeTurmaVO cmt = (ContratoModalidadeTurmaVO) obj;
                    // percorre a lista de horarios de cada turma
                    Iterator j = cmt.getContratoModalidadeHorarioTurmaVOs().iterator();
                    while (j.hasNext()) {
                        ContratoModalidadeHorarioTurmaVO cmht = (ContratoModalidadeHorarioTurmaVO) j.next();
                        if (!cmht.getHorarioTurma().getHorarioTurmaEscolhida())
                            j.remove();
                    }
                }
            }
        }
    }

    private static void atualizaLista(List<ContratoVO> contratos, ContratoVO contrato) {
        // processa a lista para colocar o contrato novo no lugar do antigo
        Iterator i = contratos.iterator();
        while(i.hasNext()) {
            ContratoVO cont = (ContratoVO)i.next();
            // se encontrar o contrato
            if(cont.getCodigo().intValue() == contrato.getCodigo()) {
                i.remove();
                contratos.add(contrato);
                break;
            }
        }
    }

    private static void processaParaExclusao(Date vigencia, List<ContratoModalidadeVO> modalidades, int cliente, List<ContratoVO> contratos, String origem, UsuarioVO responsavel) throws Exception {
        // percorre a lista de modalidades do contrato antigo
        for (ContratoModalidadeVO modalidade : modalidades) {
            // se a modalidade usa turma
            if (modalidade.getModalidade().isUtilizarTurma()) {
                // percorre a lista de turmas de cada modalidade
                for (Object obj : modalidade.getContratoModalidadeTurmaVOs()) {
                    ContratoModalidadeTurmaVO cmt = (ContratoModalidadeTurmaVO) obj;
                    // percorre a lista de horarios de cada turma
                    for (Object obj1 : cmt.getContratoModalidadeHorarioTurmaVOs()) {
                        ContratoModalidadeHorarioTurmaVO cmht = (ContratoModalidadeHorarioTurmaVO) obj1;
                        boolean exclui = true;
                        // para cada professor do contrato antigo verifica se ele existe em algum contrato ativo
                        for (ContratoVO cont : contratos) {
                            if (existeProfessorContrato(cmht.getHorarioTurma().getProfessor().getCodigo(), cont)) {
                                exclui = false;
                                break;
                            }
                        }
                        // se o professor nao existe em nenhum contrato ativo exclui
                        if (exclui) {
                            excluirVinculos(cmht.getHorarioTurma().getProfessor().getCodigo(), cliente, vigencia, origem, responsavel);
                        }
                    }
                }
            }
        }
    }

    private static boolean existeProfessorContrato(int professor, ContratoVO contrato) throws Exception {
        // percorre a lista de modalidades
        for(ContratoModalidadeVO modalidade : contrato.getContratoModalidadeVOs()) {
            // se a modalidade usa turma
            if(modalidade.getModalidade().isUtilizarTurma()) {
                // percorre a lista de turmas de cada modalidade
                Iterator i = modalidade.getContratoModalidadeTurmaVOs().iterator();
                while(i.hasNext()) {
                    ContratoModalidadeTurmaVO cmt = (ContratoModalidadeTurmaVO)i.next();
                    // percorre a lista de horarios de cada turma
                    Iterator j = cmt.getContratoModalidadeHorarioTurmaVOs().iterator();
                    while(j.hasNext()) {
                        ContratoModalidadeHorarioTurmaVO cmht = (ContratoModalidadeHorarioTurmaVO)j.next();
                        // verifica se professor nao esta no contrato novo
                        if(cmht.getHorarioTurma().getProfessor().getCodigo().intValue() == professor) {
                            return true;
                        }
                    }
                }
            }
        }
        return false;
    }

    private static void incluirVinculos(ContratoVO contrato, ClienteVO cliente, Date inicioVinculo, String origem, UsuarioVO responsavel) throws Exception {
        // percorre todas as modalidades
        for(ContratoModalidadeVO cm : contrato.getContratoModalidadeVOs()) {
            // se a modalidade pocm.getModalidade().isUtilizarTurma()ssui turmas
            if(cm.getModalidade().isUtilizarTurma() && cm.getModalidade().getModalidadeEscolhida()) {
                Iterator i = cm.getContratoModalidadeTurmaVOs().iterator();
                // percorre todas as turmas da modalidade
                while(i.hasNext()) {
                    ContratoModalidadeTurmaVO cmt = (ContratoModalidadeTurmaVO)i.next();
                    Iterator j = cmt.getContratoModalidadeHorarioTurmaVOs().iterator();
                    // percorre todos os horarios da turma
                    while(j.hasNext()) {
                        ContratoModalidadeHorarioTurmaVO cmht = (ContratoModalidadeHorarioTurmaVO)j.next();
                        // consulta a tabela de vinculos o vinculo pode ja existir
                        VinculoVO vinculo = getFacade().getVinculo().consultarVinculoPorCodigoColaboradorClienteTipoVinculo(
                                cmht.getHorarioTurma().getProfessor().getCodigo(), cliente.getCodigo(), TipoColaboradorEnum.PROFESSOR.getSigla(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                        // se o vinculo nao existir
                        if(vinculo.getCodigo().intValue() == 0) {
                            vinculo.setCliente(cliente);
                            vinculo.setColaborador(cmht.getHorarioTurma().getProfessor());
                            vinculo.setTipoVinculo(TipoColaboradorEnum.PROFESSOR.getSigla());
                            getFacade().getVinculo().incluir(vinculo, inicioVinculo, origem, true, responsavel, null);
                        }
                    }
                }
            }
        }
    }

    private static void excluirVinculos(int professor, int cliente, Date vigenciaDe, String origem, UsuarioVO responsavel) throws Exception {
        // consulta a tabela de vinculos para poder alterar o historico no momento da exclusao
        VinculoVO vinculo = getFacade().getVinculo().consultarVinculoPorCodigoColaboradorClienteTipoVinculo(
                professor, cliente, TipoColaboradorEnum.PROFESSOR.getSigla(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        if(vinculo.getCodigo() != 0) {
            // excluir o vinculo com alteração de historico
            getFacade().getVinculo().excluirVinculoPorCodigoColaboradorClienteTipoVinculo(
                    vinculo.getCodigo(), vinculo.getColaborador().getCodigo(), vinculo.getCliente().getCodigo(), TipoColaboradorEnum.PROFESSOR.getSigla(),
                    Uteis.obterDataAnterior(vigenciaDe,1), origem, responsavel);
        }
    }

// * processamento de vinculos da turma ****************************************

    public static void processarVinculosHorario(HorarioTurmaVO horario) throws Exception {
        // se houve alteração de professor
        if(horario.getProfessorAnterior() != horario.getProfessor().getCodigo()) {
            List<ContratoVO> contratos = getFacade().getContrato().consultarPorHorarioTurma(horario.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSENTIDADESUBORDINADAS);
            int pessoa = 0;
            boolean alteraParaPessoa = false;
            // percorre a lista de contratos com este horario turma
            for(ContratoVO contrato : contratos) {

                // inicio da execução
                if(pessoa == 0) { 
                    pessoa = contrato.getPessoa().getCodigo();

                // se mudou o titular do contrato
                } else if(pessoa != contrato.getPessoa().getCodigo()) {
                    processaVinculoDaPessoa(pessoa, horario, alteraParaPessoa, null);
                    pessoa = contrato.getPessoa().getCodigo();

                // se ainda é o mesmo titular dos contratos
                } else {
                    // se nao é para alterar para pessoa, não precisa mais testar os outros contratos
                    if(!alteraParaPessoa)
                        continue;
                }
                // verifica se contrato não possuir o mesmo professor em outro horarioturma retorna true
                alteraParaPessoa = verificaVinculosContrato(contrato, horario);
            }
            if(pessoa != 0)
                processaVinculoDaPessoa(pessoa, horario, alteraParaPessoa, null);
        }
    }

    private static boolean verificaVinculosContrato(ContratoVO contrato, HorarioTurmaVO horario) {
        // percorre a lista de modalidades do contrato antigo
        for(ContratoModalidadeVO modalidade : contrato.getContratoModalidadeVOs()) {
            // se a modalidade usa turma
            if(modalidade.getModalidade().isUtilizarTurma()) {
                // percorre a lista de turmas de cada modalidade
                Iterator i = modalidade.getContratoModalidadeTurmaVOs().iterator();
                while(i.hasNext()) {
                    ContratoModalidadeTurmaVO cmt = (ContratoModalidadeTurmaVO)i.next();
                    // percorre a lista de horarios de cada turma
                    Iterator j = cmt.getContratoModalidadeHorarioTurmaVOs().iterator();
                    while(j.hasNext()) {
                        ContratoModalidadeHorarioTurmaVO cmht = (ContratoModalidadeHorarioTurmaVO)j.next();
                        // se professor é igual mesmo sendo um horario turma diferente
                        if(horario.getCodigo().intValue() != cmht.getHorarioTurma().getCodigo() &&
                           horario.getProfessorAnterior() == cmht.getHorarioTurma().getProfessor().getCodigo()) {
                           return false;
                        }
                    }
                }
            }
        }
        return true;
    }

    private static void processaVinculoDaPessoa(int pessoa, HorarioTurmaVO horario, boolean alteraParaPessoa, UsuarioVO responsavel) throws Exception {
        ClienteVO cliente = getFacade().getCliente().consultarPorCodigoPessoa(pessoa,  Uteis.NIVELMONTARDADOS_SITUACAOCLIENTESINTETICODW);
        // exclui o vinculo antigo e inclui o novo
        if(alteraParaPessoa) {
            excluirVinculos(horario.getProfessorAnterior(), cliente.getCodigo(), Calendario.hoje(), "HORÁRIO TURMA", responsavel);
        }
        // consulta a tabela de vinculos pois turmas diferentes podem possuir o mesmo professor.
        VinculoVO vinculo = getFacade().getVinculo().consultarVinculoPorCodigoColaboradorClienteTipoVinculo(
                horario.getProfessor().getCodigo(), cliente.getCodigo(), TipoColaboradorEnum.PROFESSOR.getSigla(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        // se o vinculo nao existir
        if(vinculo.getCodigo() == 0) {
            vinculo.setCliente(cliente);
            vinculo.setColaborador(horario.getProfessor());
            vinculo.setTipoVinculo(TipoColaboradorEnum.PROFESSOR.getSigla());
            getFacade().getVinculo().incluir(vinculo, Calendario.hoje(), "HORÁRIO TURMA", true, null, null);
        }
        getFacade().getZWFacade().atualizarSintetico(cliente,
                    Calendario.hoje(), SituacaoClienteSinteticoEnum.GRUPO_VINCULO, false);
    }
    
    public static void processarVinculosTrocaEmpresa(ClienteVO clienteVO, UsuarioVO responsavel) throws Exception {
        //metodo transfere aluno de um colaborador para o registro desse colaborador na nova empresa do cliente, se existir
        List<VinculoVO> vinculos = getFacade().getVinculo().consultarPorCodigoCliente(clienteVO.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS, true);
        for(VinculoVO vinculoVO : vinculos){
            if(!vinculoVO.getColaborador().getEmpresa().getCodigo().equals(clienteVO.getEmpresa().getCodigo())){
                ColaboradorVO colaboradorEmpresa = getFacade().getColaborador().consultarPorCodigoPessoa(vinculoVO.getColaborador().getPessoa().getCodigo(), clienteVO.getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_ORGANIZADORCARTEIRA);
                if(UteisValidacao.notEmptyNumber(colaboradorEmpresa.getCodigo())){
                    for (TipoColaboradorVO tipoColaboradorVO : colaboradorEmpresa.getListaTipoColaboradorVOs()) {
                        if(tipoColaboradorVO.getDescricao().equals(vinculoVO.getTipoVinculo())){
                            VinculoVO novo = (VinculoVO) vinculoVO.getClone(true);
                            getFacade().getVinculo().excluir(vinculoVO,"TRANSFERÊNCIA DE EMPRESA", false);
                            novo.setCliente(clienteVO);
                            novo.setColaborador(colaboradorEmpresa);
                            getFacade().getVinculo().incluir(novo, Calendario.hoje(), "TRANSFERÊNCIA DE EMPRESA", false, responsavel, null);
                            break;
                        }
                    }
                }
            }
        }
    }
    
}
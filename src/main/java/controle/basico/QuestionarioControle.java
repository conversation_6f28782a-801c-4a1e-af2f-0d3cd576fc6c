package controle.basico;

import br.com.pactosolucoes.comuns.notificacao.RecursoSistema;
import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.estrutura.exportador.controle.ExportadorListaControle;

import java.io.File;
import java.util.*;

import javax.faces.model.SelectItem;

import controle.basico.clube.MensagemGenericaControle;
import negocio.comuns.basico.PerguntaVO;
import negocio.comuns.basico.QuestionarioPerguntaVO;
import negocio.comuns.basico.QuestionarioVO;
import negocio.comuns.basico.enumerador.TipoServicoEnum;
import negocio.comuns.utilitarias.*;
import negocio.facade.jdbc.basico.Pergunta;
import negocio.facade.jdbc.basico.Questionario;
import negocio.interfaces.basico.PerguntaInterfaceFacade;
import controle.arquitetura.SuperControle;
import controle.arquitetura.security.LoginControle;

import javax.faces.event.ActionEvent;

import negocio.comuns.arquitetura.LogVO;
import org.json.JSONObject;
import org.richfaces.event.UploadEvent;
import org.richfaces.model.UploadItem;
import servicos.operacoes.midias.MidiaService;
import servicos.operacoes.midias.commons.MidiaEntidadeEnum;
import servicos.propriedades.PropsService;

/**
 * Classe responsável por implementar a interação entre os componentes JSF das páginas
 * questionarioForm.jsp questionarioCons.jsp) com as funcionalidades da classe <code>Questionario</code>.
 * Implemtação da camada controle (Backing Bean).
 *
 * @see SuperControle
 * @see Questionario
 * @see QuestionarioVO
 */
public class QuestionarioControle extends SuperControle {

    private QuestionarioVO questionarioVO;
    protected List listaPerguntaQuestionario;
    protected List listaSelectItemPergunta;
    /**
     * Interface <code>QuestionarioInterfaceFacade</code> responsável pela interconexão da camada de controle com a camada de negócio.
     * Criando uma independência da camada de controle com relação a tenologia de persistência dos dados (DesignPatter: Façade).
     */
    private QuestionarioPerguntaVO questionarioPerguntaVO;
    private PerguntaInterfaceFacade perguntaFacade = null;
    private boolean cadastroPesquisa = false;
    private File arquivo;
    private String extensaoArquivo;
    private String nomeArquivo;
    private String onCompleteArquivo;
    private QuestionarioVO questionarioVOClone;
    private String msgAlert;
    private String urlP;
    private String pergunta;
    private PerguntaVO perguntaVO;
    List<QuestionarioVO> questionarioVOS = null;
    private boolean modoPrev;

    public QuestionarioControle() throws Exception {
        pergunta = "";
        modoPrev = true;
        perguntaVO = null;
        obterUsuarioLogado();
        inicializarFacades();
        setControleConsulta(new ControleConsulta());
        setMensagemID("");
        setCadastroPesquisa(false);
    }

    /**
     * Rotina responsável por disponibilizar um novo objeto da classe <code>Questionario</code>
     * para edição pelo usuário da aplicação.
     */
    public String novo() {
        limparMsg();
        setQuestionarioVO(new QuestionarioVO());
        setArquivo(null);
        if (isCadastroPesquisa()) {
            getQuestionarioVO().setTipoQuestionario(TipoServicoEnum.PESQUISA.getTipo());
            getQuestionarioVO().setFundoCor("#217cc2");
        }
        inicializarListasSelectItemTodosComboBox();
        setQuestionarioPerguntaVO(new QuestionarioPerguntaVO());
        setListaPerguntaQuestionario(new ArrayList());
        consultarPergunta();
        setSucesso(false);
        setErro(false);
        perguntaVO = null;
        pergunta = "";
        return "editar";
    }

    public String novoCRM() {
        setQuestionarioVO(new QuestionarioVO());
        inicializarListasSelectItemTodosComboBox();
        setQuestionarioPerguntaVO(new QuestionarioPerguntaVO());
        setListaPerguntaQuestionario(new ArrayList());
        setSucesso(false);
        setErro(false);
        setMensagemID("msg_entre_dados");
        return "editar";
    }

    /**
     * Rotina responsável por disponibilizar os dados de um objeto da classe <code>Questionario</code> para alteração.
     * O objeto desta classe é disponibilizado na session da página (request) para que o JSP correspondente possa disponibilizá-lo para edição.
     */
    public String editar() {
        try {
            limparMsg();
            setOnCompleteArquivo("");
            Integer codigoConsulta = Integer.parseInt(request().getParameter("chavePrimaria"));
            QuestionarioVO obj = getFacade().getQuestionario().consultarPorChavePrimaria(codigoConsulta, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            obj.setNovoObj(new Boolean(false));
            obj.registrarObjetoVOAntesDaAlteracao();
            setQuestionarioVO(new QuestionarioVO());
            setQuestionarioVO(obj);
            setArquivo(null);
            numeraQuestionario();
            questionarioVO.registrarPerguntasAntesAlteracao();
            inicializarListasSelectItemTodosComboBox();
            setQuestionarioPerguntaVO(new QuestionarioPerguntaVO());
            questionarioVOClone = (QuestionarioVO) questionarioVO.getClone(true);
            setListaPerguntaQuestionario(new ArrayList());
        } catch (Exception e) {
            setErro(true);
            setSucesso(false);
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
        return "editar";
    }

    /**
     * @return
     * <AUTHOR>
     * 24/03/2011
     */
    public String gravar() {
        return this.gravar(false);
    }

    /**
     * @return
     * <AUTHOR>
     * 24/03/2011
     */
    public String gravarCE() {
        return this.gravar(true);
    }

    /**
     * Rotina responsável por gravar no BD os dados editados de um novo objeto da classe <code>Questionario</code>.
     * Caso o objeto seja novo (ainda não gravado no BD) é acionado a operação <code>incluir()</code>. Caso contrário é acionado o <code>alterar()</code>.
     * Se houver alguma inconsistência o objeto não é gravado, sendo re-apresentado para o usuário juntamente com uma mensagem de erro.
     */
    public String gravar(boolean centralEventos) {
        try {
            limparMsg();
            setOnCompleteArquivo("");

            questionarioVO.setTituloPesquisa(questionarioVO.getTituloPesquisa().trim());

            if (isCadastroPesquisa() && UteisValidacao.emptyString(getQuestionarioVO().getFundoCor())) {
                throw new Exception("Informe uma cor de fundo.");
            }

            if (centralEventos) {
                this.verificarAutorizacao();
                if (questionarioVO.isNovoObj().booleanValue()) {
                    notificarRecursoEmpresa(RecursoSistema.CADASTRO_PESQUISA);
                    getFacade().getQuestionario().incluir(questionarioVO, true);
                    incluirLogInclusao();
                } else {
                    getFacade().getQuestionario().alterar(questionarioVO, true);
                    incluirLogAlteracao();
                }
            } else {
                if (questionarioVO.isNovoObj().booleanValue()) {
                    getFacade().getQuestionario().incluir(questionarioVO);
                    incluirLogInclusao();
                } else {
                    getFacade().getQuestionario().alterar(questionarioVO);
                    incluirLogAlteracao();
                }
            }
            montarSucessoGrowl("Dados gravados com sucesso!");
            return "";
        } catch (Exception e) {
            montarErro(e);
            return "";
        } finally {
            if (!questionarioVO.equals(questionarioVOClone)) {
                for (QuestionarioPerguntaVO perguntaVO : questionarioVO.getQuestionarioPerguntaVOs()) {
                    if (perguntaVO.getObrigatoria()) {
                        notificarRecursoEmpresa(RecursoSistema.QUESTIONARIO_PERGUNTA_OBRIGATORIA_MARCOU);
                    }
                }
            }
            try {
                questionarioVOClone = (QuestionarioVO) questionarioVO.getClone(true);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }
    /*
     * Inclui log de inclusão de questionário
     * @throws Exception
     */

    public void incluirLogInclusao() throws Exception {
        try {
            questionarioVO.setObjetoVOAntesAlteracao(new QuestionarioVO());
            questionarioVO.setNovoObj(true);
            registrarLogObjetoVO(questionarioVO, questionarioVO.getCodigo(), obterNomeEntidade(), 0);
            incluirLogAlteracoesPerguntas();
        } catch (Exception e) {
            registrarLogErroObjetoVO(obterNomeEntidade(), questionarioVO.getCodigo(), "ERRO AO GERAR LOG DE INCLUSÃO DE " + obterNomeEntidade(), this.getUsuarioLogado().getNome(), this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
        questionarioVO.setNovoObj(false);
        questionarioVO.registrarObjetoVOAntesDaAlteracao();
        questionarioVO.registrarPerguntasAntesAlteracao();

    }

    public void incluirLogExclusao() throws Exception {
        try {
            questionarioVO.setObjetoVOAntesAlteracao(new QuestionarioVO());
            questionarioVO.setNovoObj(true);
            registrarLogExclusaoTodosDadosObjetoVO(questionarioVO, questionarioVO.getCodigo(), obterNomeEntidade(), 0);
        } catch (Exception e) {
            registrarLogErroObjetoVO(obterNomeEntidade(), questionarioVO.getCodigo(), "ERRO AO GERAR LOG DE EXCLUSÃO DE " + obterNomeEntidade(), this.getUsuarioLogado().getNome(), this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
    }

    /**
     * Inclui log de alteração de questionário
     *
     * @throws Exception
     */
    public void incluirLogAlteracao() throws Exception {
        try {
            registrarLogObjetoVO(questionarioVO, questionarioVO.getCodigo(), obterNomeEntidade(), 0);
            incluirLogAlteracoesPerguntas();
        } catch (Exception e) {
            registrarLogErroObjetoVO(obterNomeEntidade(), questionarioVO.getCodigo(), "ERRO AO GERAR LOG DE ALTERAÇÃO DE " + obterNomeEntidade(), this.getUsuarioLogado().getNome(), this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
        questionarioVO.registrarObjetoVOAntesDaAlteracao();
        questionarioVO.registrarPerguntasAntesAlteracao();
    }

    /**
     * Rotina responsavel por executar as consultas disponiveis no JSP QuestionarioCons.jsp.
     * Define o tipo de consulta a ser executada, por meio de ComboBox denominado campoConsulta, disponivel neste mesmo JSP.
     * Como resultado, disponibiliza um List com os objetos selecionados na sessao da pagina.
     */
    public String consultar() {
        try {
            super.consultar();
            List objs = new ArrayList();

            //limpar espaços em branco antes de realizar a consulta
            getControleConsulta().setValorConsulta(getControleConsulta().getValorConsulta().trim());

            if (getControleConsulta().getCampoConsulta().equals("codigo")) {
                if (getControleConsulta().getValorConsulta().equals("")) {
                    objs = getFacade().getQuestionario().consultarPorCodigo(new Integer(0), true, Uteis.NIVELMONTARDADOS_TODOS);
                } else {
                    int valorInt = Integer.parseInt(getControleConsulta().getValorConsulta());
                    QuestionarioVO questionario = getFacade().getQuestionario().consultarPorCodigoExato(new Integer(valorInt), true, Uteis.NIVELMONTARDADOS_TODOS);
                    if (questionario != null) {
                        objs.add(questionario);
                    }
                }
            }
            if (getControleConsulta().getCampoConsulta().equals("descricao")) {
                objs = getFacade().getQuestionario().consultarPorDescricao(getControleConsulta().getValorConsulta(), "", true, Uteis.NIVELMONTARDADOS_TODOS);
            }
            objs = ControleConsulta.obterSubListPaginaApresentar(objs, controleConsulta);
            definirVisibilidadeLinksNavegacao(controleConsulta.getPaginaAtual(), controleConsulta.getNrTotalPaginas());
            setListaConsulta(objs);
            setSucesso(true);
            setErro(false);
            return "consultar";
        } catch (Exception e) {
            setListaConsulta(new ArrayList());
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
            return "consultar";
        }
    }

    /**
     * Operação responsável por processar a exclusão um objeto da classe <code>QuestionarioVO</code>
     * Após a exclusão ela automaticamente aciona a rotina para uma nova inclusão.
     */
    public String excluir() {
        try {
            limparMsg();
            getFacade().getQuestionario().excluir(questionarioVO);
            incluirLogExclusao();
            setQuestionarioVO(new QuestionarioVO());
            setQuestionarioPerguntaVO(new QuestionarioPerguntaVO());
            novo();
            montarSucessoGrowl("Dados excluídos com sucesso!");
            return "consultar";
        } catch (Exception e) {
            if (e.getMessage().toLowerCase().contains("fk_maladireta_questionario") ||
                    e.getMessage().toLowerCase().contains("fk_questionariocliente_questionario")) {
                if (isCadastroPesquisa()) {
                    setMensagemDetalhada("A pesquisa já foi utilizada não é possível excluir.");
                } else {
                    setMensagemDetalhada("O questionário já foi utilizada não é possível excluir.");
                }
            } else if(e.getMessage().contains("ERRO: atualização ou exclusão em tabela \"questionario\" viola restrição de chave estrangeira") || e.getMessage().contains("ERROR: update or delete on table \"profissao\" violates foreign key")){
                setMensagemDetalhada("Este questionário não pode ser excluído, pois está sendo utilizado!");
            }else {
                setMensagemDetalhada("msg_erro", e.getMessage());
            }
            return "editar";
        }
    }

    /* Método responsável por adicionar um novo objeto da classe <code>QuestionarioPergunta</code>
     * para o objeto <code>questionarioVO</code> da classe <code>Questionario</code>
     */
    public String adicionarQuestionarioPergunta() throws Exception {
        try {
            if (!getQuestionarioVO().getCodigo().equals(new Integer(0))) {
                questionarioPerguntaVO.setQuestionario(getQuestionarioVO().getCodigo());
            }
            if (getQuestionarioPerguntaVO().getPergunta().getCodigo().intValue() != 0) {
                Integer campoConsulta = getQuestionarioPerguntaVO().getPergunta().getCodigo();
                PerguntaVO pergunta = perguntaFacade.consultarPorChavePrimaria(campoConsulta, Uteis.NIVELMONTARDADOS_TODOS);
                getQuestionarioPerguntaVO().setPergunta(pergunta);
            }
            getQuestionarioVO().adicionarObjQuestionarioPerguntaVOs(getQuestionarioPerguntaVO());
            this.setQuestionarioPerguntaVO(new QuestionarioPerguntaVO());
            setMensagemID("msg_dados_adicionados");
            setSucesso(true);
            setErro(false);
            return "editar";
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
            return "editar";
        }
    }

    /* Método responsável por disponibilizar dados de um objeto da classe <code>QuestionarioPergunta</code>
     * para edição pelo usuário.
     */
    public String editarQuestionarioPergunta() throws Exception {
        QuestionarioPerguntaVO obj = (QuestionarioPerguntaVO) context().getExternalContext().getRequestMap().get("questionarioPergunta");
        setQuestionarioPerguntaVO(obj);
        setSucesso(false);
        setErro(false);
        return "editar";
    }

    /* Método responsável por remover um novo objeto da classe <code>QuestionarioPergunta</code>
     * do objeto <code>questionarioVO</code> da classe <code>Questionario</code>
     */
    public String removerQuestionarioPergunta() throws Exception {
        QuestionarioPerguntaVO obj = (QuestionarioPerguntaVO) context().getExternalContext().getRequestMap().get("questionarioPergunta");
        getQuestionarioVO().excluirObjQuestionarioPerguntaVOs(obj.getPergunta().getCodigo());
        numeraQuestionario();
        setMensagemID("msg_dados_excluidos");
        setSucesso(true);
        setErro(false);
        return "editar";
    }

    public void irPaginaInicial() throws Exception {
        controleConsulta.setPaginaAtual(1);
        this.consultar();
    }

    public void irPaginaAnterior() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getPaginaAtual() - 1);
        this.consultar();
    }

    public void irPaginaPosterior() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getPaginaAtual() + 1);
        this.consultar();
    }

    public void irPaginaFinal() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getNrTotalPaginas());
        this.consultar();
    }

    public void adicionarPerguntaQuestionarioLista() throws Exception {
        List lista = getListaPerguntaQuestionario();
        Iterator i = lista.iterator();
        while (i.hasNext()) {
            PerguntaVO pergunta = (PerguntaVO) i.next();
            if (pergunta.getAdicionarPergunta().booleanValue()) {
                getQuestionarioPerguntaVO().setPergunta(pergunta);
                adicionarQuestionarioPergunta();
                setQuestionarioPerguntaVO(new QuestionarioPerguntaVO());
                pergunta.setAdicionarPergunta(false);
            }
        }
        numeraQuestionario();
/*            lista = null;
            pergunta = "";
            perguntaVO = null;*/

    }

    public void numeraQuestionario() {
        Iterator i = this.questionarioVO.getQuestionarioPerguntaVOs().iterator();
        int cont = 0;
        while (i.hasNext()) {
            QuestionarioPerguntaVO obj = (QuestionarioPerguntaVO) i.next();
            obj.setNrQuestao(cont);
            cont++;
        }

    }

    public void moverParaBaixo() {
        QuestionarioPerguntaVO obj = (QuestionarioPerguntaVO) context().getExternalContext().getRequestMap().get("questionarioPergunta");
        QuestionarioPerguntaVO aux = new QuestionarioPerguntaVO();
        Iterator i = this.questionarioVO.getQuestionarioPerguntaVOs().iterator();
        while (i.hasNext()) {
            QuestionarioPerguntaVO quesPergunta = (QuestionarioPerguntaVO) i.next();
            if (quesPergunta.getNrQuestao() == obj.getNrQuestao() + 1) {

                aux = obj;
                obj = quesPergunta;
                quesPergunta = aux;
                this.questionarioVO.getQuestionarioPerguntaVOs().set(obj.getNrQuestao().intValue(), quesPergunta);
                this.questionarioVO.getQuestionarioPerguntaVOs().set(quesPergunta.getNrQuestao().intValue(), obj);
                break;

            }
        }
        numeraQuestionario();
    }

    public void moverParaCima() {
        QuestionarioPerguntaVO obj = (QuestionarioPerguntaVO) context().getExternalContext().getRequestMap().get("questionarioPergunta");
        QuestionarioPerguntaVO aux = new QuestionarioPerguntaVO();
        Iterator i = this.questionarioVO.getQuestionarioPerguntaVOs().iterator();
        while (i.hasNext()) {
            QuestionarioPerguntaVO quesPergunta = (QuestionarioPerguntaVO) i.next();
            if (quesPergunta.getNrQuestao() == obj.getNrQuestao() - 1) {
                aux = obj;
                obj = quesPergunta;
                quesPergunta = aux;
                this.questionarioVO.getQuestionarioPerguntaVOs().set(obj.getNrQuestao().intValue(), quesPergunta);
                this.questionarioVO.getQuestionarioPerguntaVOs().set(quesPergunta.getNrQuestao().intValue(), obj);
                break;

            }
        }
        numeraQuestionario();

    }

    public void consultarPergunta() {
        try {
            List lista = perguntaFacade.consultarPorCodigo(new Integer(0), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            setListaPerguntaQuestionario(lista);
            lista = null;
        } catch (Exception ex) {
            setListaPerguntaQuestionario(new ArrayList());
        }
    }

    /**
     * Método responsável por gerar uma lista de objetos do tipo <code>SelectItem</code> para preencher
     * o comboBox relativo ao atributo <code>Pergunta</code>.
     */
    public void montarListaSelectItemPergunta(String prm) throws Exception {
        List<SelectItem> itens = new ArrayList();
        itens.add(new SelectItem(new Integer(0), ""));
        for (PerguntaVO pergunta : (ArrayList<PerguntaVO>) consultarPerguntaPorDescricao(prm)) {
            itens.add(new SelectItem(pergunta.getCodigo(), pergunta.getDescricao().toString()));
        }
        Ordenacao.ordenarLista(itens, "label");
        setListaSelectItemPergunta(itens);
    }

    /**
     * Método responsável por atualizar o ComboBox relativo ao atributo <code>Pergunta</code>.
     * Buscando todos os objetos correspondentes a entidade <code>Pergunta</code>. Esta rotina não recebe parâmetros para filtragem de dados, isto é
     * importante para a inicialização dos dados da tela para o acionamento por meio requisições Ajax.
     */
    public void montarListaSelectItemPergunta() {
        try {
            montarListaSelectItemPergunta("");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * Método responsável por consultar dados da entidade <code><code> e montar o atributo <code>descricao</code>
     * Este atributo é uma lista (<code>List</code>) utilizada para definir os valores a serem apresentados no ComboBox correspondente
     */
    public List consultarPerguntaPorDescricao(String descricaoPrm) throws Exception {
        List lista = perguntaFacade.consultarPorDescricao(descricaoPrm, false, Uteis.NIVELMONTARDADOS_TODOS);
        return lista;
    }

    /**
     * Método responsável por inicializar a lista de valores (<code>SelectItem</code>) para todos os ComboBox's.
     */
    public void inicializarListasSelectItemTodosComboBox() {
        montarListaSelectItemPergunta();
    }

    /**
     * Rotina responsável por preencher a combo de consulta da telas.
     */
    public List getTipoConsultaCombo() {
        List<SelectItem> itens = new ArrayList();
        itens.add(new SelectItem("codigo", "Código"));
        itens.add(new SelectItem("descricao", "Descrição"));
        return itens;
    }

    /**
     * Rotina responsável por organizar a paginação entre as páginas resultantes de uma consulta.
     */
    public String inicializarConsultar() {
        setPaginaAtualDeTodas("0/0");
        setListaConsulta(new ArrayList());
        definirVisibilidadeLinksNavegacao(0, 0);
        setMensagemID("msg_entre_prmconsulta");
        setSucesso(false);
        setErro(false);
        return "consultar";
    }

    /**
     * Operação que inicializa as Interfaces Façades com os respectivos objetos de
     * persistência dos dados no banco de dados.
     */
    protected boolean inicializarFacades() {
        try {
            super.inicializarFacades();

            perguntaFacade = new Pergunta();
            return true;
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro_conectarBD", e.getMessage());
            return false;
        }
    }

    public List getListaSelectItemPergunta() {
        return (listaSelectItemPergunta);
    }

    public void setListaSelectItemPergunta(List listaSelectItemPergunta) {
        this.listaSelectItemPergunta = listaSelectItemPergunta;
    }

    public QuestionarioPerguntaVO getQuestionarioPerguntaVO() {
        return questionarioPerguntaVO;
    }

    public void setQuestionarioPerguntaVO(QuestionarioPerguntaVO questionarioPerguntaVO) {
        this.questionarioPerguntaVO = questionarioPerguntaVO;
    }

    public QuestionarioVO getQuestionarioVO() {
        return questionarioVO;
    }

    public void setQuestionarioVO(QuestionarioVO questionarioVO) {
        this.questionarioVO = questionarioVO;
    }

    public List getListaPerguntaQuestionario() {
        return listaPerguntaQuestionario;
    }

    public void setListaPerguntaQuestionario(List listaPerguntaQuestionario) {
        this.listaPerguntaQuestionario = listaPerguntaQuestionario;
    }

    /**
     * Consulta de logs de pergunta
     */
    @SuppressWarnings("unchecked")
    public void realizarConsultaLogObjetoSelecionado() {
        LoginControle loginControle = (LoginControle) context().getExternalContext().getSessionMap().get("LoginControle");
        if (isCadastroPesquisa()) {
            loginControle.setNomeClasse("Pesquisa");
        } else {
            loginControle.setNomeClasse("Questionário");
        }
        loginControle.consultarLogObjetoSelecionado(obterNomeEntidade(), questionarioVO.getCodigo(), null);
    }

    @SuppressWarnings("unchecked")
    public void realizarConsultaLogObjetoGeral() {
        questionarioVO = new QuestionarioVO();
        realizarConsultaLogObjetoSelecionado();
    }

    public void exportar(ActionEvent evt) throws Exception {
        ExportadorListaControle exportadorListaControle = (ExportadorListaControle) JSFUtilities.getFromRequest(ExportadorListaControle.class.getSimpleName());
        String paramsTableFiltrada = context().getExternalContext().getRequestParameterMap().get("paramsTableFiltrada");

        String[] split = paramsTableFiltrada.split(",");
        String campoOrdenacao = split[0].replace("[", "");
        String ordem = split[1];
        String filtro = split[2].replace("''", "");
        filtro = filtro.replace("]", "");
        List listaParaImpressao = getFacade().getQuestionario().consultarParaImpressao(filtro, ordem, campoOrdenacao, getEmpresaLogado() == null ? 0 : getEmpresaLogado().getCodigo(), isCadastroPesquisa());
        exportadorListaControle.exportar(evt, listaParaImpressao, filtro,null );

    }

    public List<SelectItem> getListaSelectItemTipo() {
        List<SelectItem> objs = new ArrayList<SelectItem>();
        objs.add(new SelectItem("", ""));
        for (TipoServicoEnum tipoProduto : TipoServicoEnum.values()) {
            String value = tipoProduto.getTipo();
            String label = tipoProduto.getDescricao();
            if (isCadastroPesquisa() && tipoProduto.equals(TipoServicoEnum.PESQUISA)) {
                objs.add(new SelectItem(value, label));
            } else if (!isCadastroPesquisa() && !tipoProduto.equals(TipoServicoEnum.PESQUISA)) {
                objs.add(new SelectItem(value, label));
            }
        }
        SelectItemOrdemValor ordenador = new SelectItemOrdemValor();
        Collections.sort((List) objs, ordenador);
        return objs;
    }

    private void incluirLogAlteracoesPerguntas() throws Exception {
        for (QuestionarioPerguntaVO atual : questionarioVO.getQuestionarioPerguntaVOs()) {
            boolean nova = true;
            for (QuestionarioPerguntaVO anterior : questionarioVO.getQuestionarioPerguntaVOsAntesAlteracao()) {
                if (anterior.getPergunta().getCodigo().equals(atual.getPergunta().getCodigo())) {
                    if (!anterior.getNrQuestao().equals(atual.getNrQuestao())) {
                        incluirLogAlteracaoPergunta(anterior, atual);
                    }
                    nova = false;
                    break;
                }
            }
            if (nova) {
                incluirLogInclusaoPergunta(atual);
            }
        }
        for (QuestionarioPerguntaVO anterior : questionarioVO.getQuestionarioPerguntaVOsAntesAlteracao()) {
            boolean excluida = true;
            for (QuestionarioPerguntaVO atual : questionarioVO.getQuestionarioPerguntaVOs()) {
                if (anterior.getPergunta().getCodigo().equals(atual.getPergunta().getCodigo())) {
                    excluida = false;
                    break;
                }
            }
            if (excluida) {
                incluirLogExclusaoPergunta(anterior);
            }
        }

    }


    private void incluirLogInclusaoPergunta(QuestionarioPerguntaVO atual) throws Exception {
        try {
            LogVO logVO = new LogVO();
            logVO.setOperacao("INCLUSÃO");
            logVO.setChavePrimaria(String.valueOf(questionarioVO.getCodigo()));
            logVO.setNomeEntidade(obterNomeEntidade());
            logVO.setNomeEntidadeDescricao("Questionario - QuestionarioPergunta/Questionário Pergunta");
            logVO.setResponsavelAlteracao(getUsuarioLogado().getNome());
            logVO.setUserOAMD(getUsuarioLogado().getUserOamd());
            logVO.setNomeCampo("Campo(s)");
            logVO.setDataAlteracao(negocio.comuns.utilitarias.Calendario.hoje());
            logVO.setValorCampoAnterior("");
            logVO.setValorCampoAlterado("Pergunta:" + atual.getPergunta().getCodigo() + "\n  Ordem= " + atual.getNrQuestao());

            if (logVO != null) {
                logVO.setPessoa(0);
                getFacade().getLog().incluirSemCommit(logVO);
            }
        } catch (Exception e) {
            registrarLogErroObjetoVO(obterNomeEntidade(), questionarioVO.getCodigo(), "ERRO AO GERAR LOG DE INCLUSÃO DE PERGUNTA", this.getUsuarioLogado().getNome(), this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
    }

    private void incluirLogExclusaoPergunta(QuestionarioPerguntaVO anterior) throws Exception {
        try {
            LogVO logVO = new LogVO();
            logVO.setOperacao("EXCLUSÃO");
            logVO.setChavePrimaria(String.valueOf(questionarioVO.getCodigo()));
            logVO.setNomeEntidade(obterNomeEntidade());
            logVO.setNomeEntidadeDescricao("Questionario - QuestionarioPergunta/Questionário Pergunta");
            logVO.setResponsavelAlteracao(getUsuarioLogado().getNome());
            logVO.setUserOAMD(getUsuarioLogado().getUserOamd());
            logVO.setNomeCampo("Campo(s)");
            logVO.setDataAlteracao(negocio.comuns.utilitarias.Calendario.hoje());
            logVO.setValorCampoAnterior("");
            logVO.setValorCampoAlterado("Pergunta:" + anterior.getPergunta().getCodigo() + "\n  Ordem= " + anterior.getNrQuestao());

            if (logVO != null) {
                logVO.setPessoa(0);
                getFacade().getLog().incluirSemCommit(logVO);
            }
        } catch (Exception e) {
            registrarLogErroObjetoVO(obterNomeEntidade(), questionarioVO.getCodigo(), "ERRO AO GERAR LOG DE EXCLUSÃO DE PERGUNTA", this.getUsuarioLogado().getNome(), this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
    }

    private void incluirLogAlteracaoPergunta(QuestionarioPerguntaVO anterior, QuestionarioPerguntaVO atual) throws Exception {
        try {
            LogVO logVO = new LogVO();
            logVO.setOperacao("ALTERAÇÃO");
            logVO.setChavePrimaria(String.valueOf(questionarioVO.getCodigo()));
            logVO.setNomeEntidade(obterNomeEntidade());
            logVO.setNomeEntidadeDescricao("Questionario - QuestionarioPergunta/Questionário Pergunta");
            logVO.setResponsavelAlteracao(getUsuarioLogado().getNome());
            logVO.setUserOAMD(getUsuarioLogado().getUserOamd());
            logVO.setNomeCampo("orderm");
            logVO.setDataAlteracao(negocio.comuns.utilitarias.Calendario.hoje());
            logVO.setValorCampoAnterior("Pergunta:" + anterior.getPergunta().getCodigo() + "\n  Ordem= " + anterior.getNrQuestao());
            logVO.setValorCampoAlterado("Pergunta:" + atual.getPergunta().getCodigo() + "\n  Ordem= " + atual.getNrQuestao());

            if (logVO != null) {
                logVO.setPessoa(0);
                getFacade().getLog().incluirSemCommit(logVO);
            }
        } catch (Exception e) {
            registrarLogErroObjetoVO(obterNomeEntidade(), questionarioVO.getCodigo(), "ERRO AO GERAR LOG DE ALTERAÇÃO DE PERGUNTA", this.getUsuarioLogado().getNome(), this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
    }

    public boolean isPesquisa() {
        return getQuestionarioVO() != null && getQuestionarioVO().getTipoQuestionario().equals(TipoServicoEnum.PESQUISA.getTipo());
    }

    public boolean isCadastroPesquisa() {
        return cadastroPesquisa;
    }

    public void setCadastroPesquisa(boolean cadastroPesquisa) {
        this.cadastroPesquisa = cadastroPesquisa;
    }

    public File getArquivo() {
        return arquivo;
    }

    public void setArquivo(File arquivo) {
        this.arquivo = arquivo;
    }

    public String getExtensaoArquivo() {
        return extensaoArquivo;
    }

    public void setExtensaoArquivo(String extensaoArquivo) {
        this.extensaoArquivo = extensaoArquivo;
    }

    public String getNomeArquivo() {
        return nomeArquivo;
    }

    public void setNomeArquivo(String nomeArquivo) {
        this.nomeArquivo = nomeArquivo;
    }

    public void upload(UploadEvent upload) {
        limparMsg();
        setOnCompleteArquivo("");
        try {
            UploadItem item = upload.getUploadItem();
            if (item.getFile().length() > 5242880) {
                throw new ConsistirException("Arquivo tem tamanho superior a 5 Megabyte");
            }

            setArquivo(item.getFile());
            setNomeArquivo(item.getFileName());
            String[] partes = item.getFileName().split("\\.");
            setExtensaoArquivo("." + partes[partes.length - 1]);

            if (!getExtensaoArquivo().toLowerCase().equals(".jpg") && !getExtensaoArquivo().toLowerCase().equals(".jpeg") && !getExtensaoArquivo().toLowerCase().equals(".png")) {
                throw new Exception("Somente arquivos no formado (.JPG, .JPEG, .PNG) são aceitos");
            }

            String chaveArquivo = MidiaService.getInstance().uploadObjectWithExtension(getKey(), MidiaEntidadeEnum.PESQUISA,
                    "PESQUISA_IMG_" + Calendario.hoje().getTime(),
                    getArquivo(), getExtensaoArquivo());
            getQuestionarioVO().setFundoImagem(chaveArquivo);

            montarInfo("Clique em gravar para salvar a imagem.");
        } catch (Exception e) {
            getQuestionarioVO().setFundoImagem("");
            setArquivo(null);
            setExtensaoArquivo("");
            montarErro(e.getMessage());
        }
        setOnCompleteArquivo(getMensagemNotificar());
    }

    public String getOnCompleteArquivo() {
        if (onCompleteArquivo == null) {
            onCompleteArquivo = "";
        }
        return onCompleteArquivo;
    }

    public void setOnCompleteArquivo(String onCompleteArquivo) {
        this.onCompleteArquivo = onCompleteArquivo;
    }

    public void limparArquivo() {
        limparMsg();
        setOnCompleteArquivo("");
        getQuestionarioVO().setFundoImagem("");
        setArquivo(null);
        montarInfo("Clique em gravar para salvar a operação.");
        setOnCompleteArquivo(getMensagemNotificar());
    }

    private String obterNomeEntidade() {
        if (isCadastroPesquisa()) {
            return "PESQUISA";
        } else {
            return "QUESTIONARIO";
        }
    }

    public String getUrlPesquisa() {
        try {
            if (UteisValidacao.emptyNumber(getQuestionarioVO().getCodigo())) {
                return "";
            }

            JSONObject json = new JSONObject();
            json.put("key", getKey());
            json.put("questionario", getQuestionarioVO().getCodigo());
            json.put("cliente", 0);
            json.put("empresa", getEmpresaLogado().getCodigo());
            json.put("colaborador", getUsuarioLogado().getColaboradorVO().getCodigo());
            return PropsService.getPropertyValue("DISCOVERY_URL")+"/redir"+"/"+getKey()+"/zillyonWeb?"+"m=/faces/pesquisa.jsp&q=" + Uteis.encriptar(json.toString(), "PESQUIS@");
        } catch (Exception ex) {
            return "";
        }
    }

    public String getPaintFotoDaNuvem() {
        return getPaintFotoDaNuvem(getQuestionarioVO().getFundoImagem());
    }

    public void setMsgAlert(String msgAlert) {
        this.msgAlert = msgAlert;
    }

    public String getMsgAlert() {
        if (msgAlert == null) {
            return "";
        }
        return msgAlert;
    }

    public void confirmarExcluir(){
        MensagemGenericaControle control = (MensagemGenericaControle) getControlador(MensagemGenericaControle.class);
        control.setMensagemDetalhada("", "");
        setMsgAlert("Richfaces.showModalPanel('mdlMensagemGenerica');");
        control.init("Exclusão de Questionário",
                "Deseja excluir o Questionário?",
                this, "excluir", "", "", "", "grupoBtnExcluir");
    }

    public void preVisuzalizacao() {
        try {

            String idTemp = "PES_" + Calendario.hoje().getTime();
            JSFUtilities.storeOnSession(idTemp, getQuestionarioVO());
            JSONObject json = new JSONObject();
            json.put("key", getKey());
            json.put("questionario", getQuestionarioVO().getCodigo());
            json.put("cliente", 0);
            json.put("empresa", getEmpresaLogado().getCodigo());
            json.put("colaborador", getUsuarioLogado().getColaboradorVO().getCodigo());
            json.put("idTemp", idTemp);
            StringBuilder url = new StringBuilder();
            url.append(PropsService.getPropertyValue("DISCOVERY_URL")+"/redir"+"/"+getKey()+"/zillyonWeb?"+"m=/faces/pesquisa.jsp&q=" + Uteis.encriptar(json.toString(), "PESQUIS@"));
            setUrlP(url.toString());
        } catch (Exception ex) {
            Uteis.logar(ex, QuestionarioControle.class);
        }
    }
    public void entrarFomulario(){
        modoPrev = true;
    }

    public void sairFomulario() {
        preVisuzalizacao();
        modoPrev = false;
    }


    public String getUrlP() {
        return urlP;
    }

    public void setUrlP(String urlP) {
        this.urlP = urlP;
    }
    public List<QuestionarioVO> executarAutocompletePesq(Object suggest) {
        List<QuestionarioVO> questionarioVOS = null;
        try {
            questionarioVOS = perguntaFacade.consultarPorDescricao(suggest.toString(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        } catch (Exception e) {
            setSucesso(false);
            setErro(true);
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
        return questionarioVOS;
    }
    public void selecionarPergunta() {
        try {
            PerguntaVO obj = (PerguntaVO) context().getExternalContext().getRequestMap().get("result");
            obj.setAdicionarPergunta(true);
            getListaPerguntaQuestionario().add(obj);
            this.perguntaVO = obj;
            adicionarPerguntaQuestionarioLista();
            pergunta = "";
        } catch (Exception e ) {
            Uteis.logar(e, QuestionarioControle.class);
            montarErro("Erro ao selecionar pergunta");
        }
    }

    public String getPergunta() {
        return pergunta;
    }

    public void setPergunta(String pergunta) {
        this.pergunta = pergunta;
    }

    public void setPerguntaVO(PerguntaVO perguntaVO) {
        this.perguntaVO = perguntaVO;
    }

    public PerguntaVO getPerguntaVO() {
        if (perguntaVO == null)
            perguntaVO = new PerguntaVO();
        return perguntaVO;
    }

    public boolean isModoPrev() {
        return modoPrev;
    }
}

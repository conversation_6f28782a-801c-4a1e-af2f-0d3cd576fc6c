package controle.basico;


import controle.arquitetura.SuperControle;
import negocio.comuns.financeiro.ObjetoGenerico;
import negocio.comuns.financeiro.PinPadPedidoVO;
import negocio.comuns.financeiro.enumerador.StatusPinpadEnum;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.utilitarias.Conexao;
import servicos.impl.stone.connect.StoneConnectService;

import javax.faces.event.ActionEvent;
import javax.faces.model.SelectItem;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class PinpadPedidoRelatorioControle extends SuperControle {

    private Date dataInicial;
    private Date dataFinal;
    private List<PinPadPedidoVO> lista;
    private PinPadPedidoVO pinPadPedidoVO;
    private List<ObjetoGenerico> listaParametrosSelecionado = new ArrayList();
    private Integer statusAtualizar;
    private String onComplete;
    private boolean atualizarTodosPedidos = false;

    public void consultarRel() {
        try {
            limparMsg();
            setOnComplete("");
            lista = getFacade().getPinPadPedido().consultarPorPeriodo(dataInicial, dataFinal, Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS);
            if (UteisValidacao.emptyList(lista)) {
                montarAviso("Nenhum registro encontrado");
            }
            setOnComplete("Notifier.cleanAll();" + getMensagemNotificar());
        } catch (Exception ex) {
            montarErro(ex);
            setOnComplete(getMensagemNotificar());
        }
    }

    public Date getDataInicial() {
        if (dataInicial == null) {
            setDataInicial(Calendario.primeiroDiaMes());
        }
        return dataInicial;
    }

    public void setDataInicial(Date dataInicial) {
        this.dataInicial = Calendario.primeiraHoraDia(dataInicial);
    }

    public Date getDataFinal() {
        if (dataFinal == null) {
            setDataFinal(Calendario.ultimoDiaMes());
        }
        return dataFinal;
    }

    public void setDataFinal(Date dataFinal) {
        this.dataFinal = Calendario.ultimaHoraDia(dataFinal);
    }

    public List<ObjetoGenerico> getListaParametrosSelecionado() {
        return listaParametrosSelecionado;
    }

    public void setListaParametrosSelecionado(List<ObjetoGenerico> listaParametrosSelecionado) {
        this.listaParametrosSelecionado = listaParametrosSelecionado;
    }

    public void exibirParams(ActionEvent evt) {
        try {
            limparMsg();
            setOnComplete("");
            pinPadPedidoVO = null;
            listaParametrosSelecionado = null;

            String params = (String) evt.getComponent().getAttributes().get("params");
            PinPadPedidoVO obj = (PinPadPedidoVO) evt.getComponent().getAttributes().get("item");
            if (params == null || obj == null) {
                throw new Exception("Erro ao obter params");
            }

            pinPadPedidoVO = obj;
            if (params.equals("envio")) {
                listaParametrosSelecionado = Uteis.obterListaParametrosValores(obj.getParamsEnvio());
            } else if (params.equals("resposta")) {
                listaParametrosSelecionado = Uteis.obterListaParametrosValores(obj.getParamsResp());
            }
            setOnComplete("Notifier.cleanAll();Richfaces.showModalPanel('modalParamsPedido')");
        } catch (Exception ex) {
            montarErro(ex);
            setOnComplete(getMensagemNotificar());
        }
    }

    public List<PinPadPedidoVO> getLista() {
        if (lista == null) {
            lista = new ArrayList<>();
        }
        return lista;
    }

    public void setLista(List<PinPadPedidoVO> lista) {
        this.lista = lista;
    }

    public PinPadPedidoVO getPinPadPedidoVO() {
        if (pinPadPedidoVO == null) {
            pinPadPedidoVO = new PinPadPedidoVO();
        }
        return pinPadPedidoVO;
    }

    public void setPinPadPedidoVO(PinPadPedidoVO pinPadPedidoVO) {
        this.pinPadPedidoVO = pinPadPedidoVO;
    }

    public String getOnComplete() {
        if (onComplete == null || lista.size() == 0) {
            return "";
        }
        return onComplete;
    }

    public void setOnComplete(String onComplete) {
        this.onComplete = onComplete;
    }

    public List<SelectItem> getListaSelectItemStatus() {
        List<SelectItem> lista = new ArrayList<>();
        lista.add(new SelectItem(0, ""));
        lista.add(new SelectItem(StatusPinpadEnum.FALHA.getCodigo(), StatusPinpadEnum.FALHA.getDescricao()));
        lista.add(new SelectItem(StatusPinpadEnum.CANCELADO.getCodigo(), StatusPinpadEnum.CANCELADO.getDescricao()));
        return lista;
    }

    public void abrirModalAlterar() {
        try {
            limparMsg();
            setOnComplete("");
            setStatusAtualizar(null);

            PinPadPedidoVO obj = (PinPadPedidoVO) context().getExternalContext().getRequestMap().get("item");
            setPinPadPedidoVO(obj);

            setOnComplete("Notifier.cleanAll();Richfaces.showModalPanel('modalAlterarPedido')");
        } catch (Exception ex) {
            montarErro(ex);
            setOnComplete(getMensagemNotificar());
        }
    }

    public void alterarPedido() {
        try {
            limparMsg();
            setOnComplete("");
            if (UteisValidacao.emptyNumber(this.getStatusAtualizar())) {
                throw new Exception("Selecione um status");
            }
            if (this.getPinPadPedidoVO() == null) {
                throw new Exception("Pedido não encontrado");
            }

            if (this.isAtualizarTodosPedidos()){
                for(PinPadPedidoVO pinPadPedidoVO1: this.getLista()){
                    if(pinPadPedidoVO1.getStatus().equals(StatusPinpadEnum.AGUARDANDO)){
                        StoneConnectService.fecharPedido(pinPadPedidoVO1, StatusPinpadEnum.fromCodigo(this.getStatusAtualizar()), true, true, Conexao.getFromSession());
                    }
                }
                this.setAtualizarTodosPedidos(false);
                montarSucessoGrowl("Pedidos atualizados");
            } else {
                String retorno = StoneConnectService.fecharPedido(this.getPinPadPedidoVO(), StatusPinpadEnum.fromCodigo(this.getStatusAtualizar()), true, true, Conexao.getFromSession());
                montarSucessoGrowl("Pedido atualizado | " + retorno);
            }
            setOnComplete("Notifier.cleanAll();Richfaces.hideModalPanel('modalAlterarPedido');" + getMensagemNotificar());
        } catch (Exception ex) {
            ex.printStackTrace();
            montarErro(ex);
            setOnComplete(getMensagemNotificar());
        } finally {
            this.setAtualizarTodosPedidos(false);
        }
    }

    public void acaoAtualizarTodosPedidos() {
        if(lista.size() > 0){
            this.setAtualizarTodosPedidos(true);
            abrirModalAlterar();
        }
    }

    public void atualizarIndividualPedido() {
        this.setAtualizarTodosPedidos(false);
        abrirModalAlterar();
    }

    public Integer getStatusAtualizar() {
        if (statusAtualizar == null) {
            statusAtualizar = 0;
        }
        return statusAtualizar;
    }

    public void setStatusAtualizar(Integer statusAtualizar) {
        this.statusAtualizar = statusAtualizar;
    }

    public boolean isAtualizarTodosPedidos() {
        return atualizarTodosPedidos;
    }

    public void setAtualizarTodosPedidos(boolean atualizarTodos) {
        this.atualizarTodosPedidos = atualizarTodos;
    }
}

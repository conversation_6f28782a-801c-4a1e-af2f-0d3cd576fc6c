package controle.basico;

import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.estrutura.ItemExportacaoEnum;
import br.com.pactosolucoes.estrutura.exportador.controle.ExportadorListaControle;
import br.com.pactosolucoes.estrutura.paginacao.ConfPaginacao;

import java.util.Date;

import controle.arquitetura.security.AutorizacaoFuncionalidadeControle;
import controle.arquitetura.security.AutorizacaoFuncionalidadeListener;
import controle.basico.clube.MensagemGenericaControle;
import negocio.facade.jdbc.basico.MovimentoContaCorrenteCliente;
import negocio.comuns.utilitarias.*;
import negocio.comuns.basico.*;
import negocio.comuns.financeiro.MovPagamentoVO;

import javax.faces.model.SelectItem;
import java.util.List;
import java.util.ArrayList;

import controle.arquitetura.SuperControle;

import java.util.Collections;
import java.util.Enumeration;
import java.util.Hashtable;
import java.util.Iterator;

import javax.faces.event.ActionEvent;

import negocio.comuns.arquitetura.UsuarioVO;
import negocio.facade.jdbc.arquitetura.Usuario;

/**
 * Classe responsável por implementar a interação entre os componentes JSF das páginas
 * movimentoContaCorrenteClienteForm.jsp movimentoContaCorrenteClienteCons.jsp) com as funcionalidades da classe <code>MovimentoContaCorrenteCliente</code>.
 * Implemtação da camada controle (Backing Bean).
 *
 * @see SuperControle
 * @see MovimentoContaCorrenteCliente
 * @see MovimentoContaCorrenteClienteVO
 */
public class MovimentoContaCorrenteClienteControle extends SuperControle {

    private MovimentoContaCorrenteClienteVO movimentoContaCorrenteClienteVO;
    // protected List listaSelectItemResponsavelRegistro;
    //protected List listaSelectItemResponsavelAutorizacao;
    /**
     * Interface <code>MovimentoContaCorrenteClienteInterfaceFacade</code> responsável pela interconexão da camada de controle com a camada de negócio.
     * Criando uma independência da camada de controle com relação a tenologia de persistência dos dados (DesignPatter: Façade).
     */
    protected List listaConsultaPessoa;
    protected List listaConsultarCliente;
    private String campoConsultaPessoa;
    private String valorConsultaPessoa;
    private String campoConsultaCliente;
    protected String valorConsultarCliente;
    private String paramTipoConsulta;
    private boolean abrirRichConfirmacaoTransferencia;
    protected Boolean apresentarBoteos;
    private String oncomplete = "";
    private String msgAlert;
    private boolean possuiPermissaoEditarSaldoContaCorrente;

    public MovimentoContaCorrenteClienteControle() throws Exception {
        setPossuiPermissaoEditarSaldoContaCorrente(permissao("AlterarSaldoContaCorrenteCliente"));
        obterUsuarioLogado();
        inicializarFacades();
        setControleConsulta(new ControleConsulta());
    }

    public void obterUsuario() throws Exception {
        UsuarioVO obj = new Usuario().consultarPorChavePrimaria(getUsuarioLogado().getCodigo().intValue(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        movimentoContaCorrenteClienteVO.setResponsavelAutorizacao(obj);

    }

    /**
     * Rotina responsável por disponibilizar um novo objeto da classe <code>MovimentoContaCorrenteCliente</code>
     * para edição pelo usuário da aplicação.
     */
    public String novo() throws Exception {
        try {
            setMovimentoContaCorrenteClienteVO(new MovimentoContaCorrenteClienteVO());
            setListaConsultaPessoa(new ArrayList());
            movimentoContaCorrenteClienteVO.setTipoMovimentacao("CR");
            setControleConsulta(new ControleConsulta());
            obterUsuario();
            setAbrirRichConfirmacaoTransferencia(false);
            inicializarCliente("");
            limparMsg();
            return "editar";
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            return "";
        }
    }

    public String novoTransferencia() throws Exception {
        try {
            setMovimentoContaCorrenteClienteVO(new MovimentoContaCorrenteClienteVO());
            setListaConsultaPessoa(new ArrayList());
            movimentoContaCorrenteClienteVO.setTipoMovimentacao("DE");
            setControleConsulta(new ControleConsulta());
            setAbrirRichConfirmacaoTransferencia(false);
            setApresentarBoteos(true);
            obterUsuario();
            inicializarCliente("transferencia");
            setSucesso(false);
            setErro(false);
            setMensagemID("msg_entre_dados");
            return "";
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            return "";
        }
    }

    public String novaTransferencia(ClienteVO clienteVO) throws Exception {
        try {
            setMovimentoContaCorrenteClienteVO(new MovimentoContaCorrenteClienteVO());
            setListaConsultaPessoa(new ArrayList());
            movimentoContaCorrenteClienteVO.setTipoMovimentacao("DE");
            setControleConsulta(new ControleConsulta());
            setAbrirRichConfirmacaoTransferencia(false);
            setApresentarBoteos(true);
            obterUsuario();
            inicializarTransferenciaSaldo("transferencia", clienteVO);
            setSucesso(false);
            setErro(false);
            setMensagemID("msg_entre_dados");
            return "";
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            return "";
        }
    }

    public String novoGerarParcelaDebito() throws Exception {
        setMovimentoContaCorrenteClienteVO(new MovimentoContaCorrenteClienteVO());
        try {
            setListaConsultaPessoa(new ArrayList());
            movimentoContaCorrenteClienteVO.setTipoMovimentacao("CR");
            setControleConsulta(new ControleConsulta());
            setApresentarBoteos(true);
            setAbrirRichConfirmacaoTransferencia(false);
            obterUsuario();
            inicializarCliente("gerarParcela");
            setSucesso(false);
            setErro(false);
            setMensagemID("msg_entre_dados");
            return "";
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            return "";
        }
    }

    public String novoReceberParcelaDebito(ClienteVO clienteVO) throws Exception {
        setMovimentoContaCorrenteClienteVO(new MovimentoContaCorrenteClienteVO());
        try {
            setListaConsultaPessoa(new ArrayList());
            movimentoContaCorrenteClienteVO.setTipoMovimentacao("CR");
            setControleConsulta(new ControleConsulta());
            setApresentarBoteos(true);
            setAbrirRichConfirmacaoTransferencia(false);
            obterUsuario();
            inicializarClienteReceberDebito("gerarParcela", clienteVO);
            setSucesso(false);
            setErro(false);
            setMensagemID("msg_entre_dados");
            return "";
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            return "";
        }
    }

    private void inicializarTransferenciaSaldo(String operacao, ClienteVO clienteVO) throws Exception {
        ClienteControle clienteControle = (ClienteControle) context().getExternalContext().getSessionMap().get("ClienteControle");
        clienteControle.setClienteVO(clienteVO);
        if (clienteVO != null) {
            getControleConsulta().setValorConsulta(clienteVO.getPessoa().getCodigo().toString());
            getControleConsulta().setCampoConsulta("codPessoa");
            //é setado o cliente para facilitar o uso a partir da tela do cliente.
            getMovimentoContaCorrenteClienteVO().setPessoa(clienteVO.getPessoa());
            MovimentoContaCorrenteClienteVO mov;
            if (operacao.equals("transferencia")) {
                mov = getFacade().getMovimentoContaCorrenteCliente().consultarPorCodigoPessoa(getMovimentoContaCorrenteClienteVO().getPessoa().getCodigo().intValue(), Uteis.NIVELMONTARDADOS_TODOS);
                if (mov != null && mov.getSaldoAtual() > 0) {
                    movimentoContaCorrenteClienteVO.setMovPagamentosVOs(mov.getMovPagamentosVOs());
                    movimentoContaCorrenteClienteVO.setSaldoAtual(mov.getSaldoAtual());
                    movimentoContaCorrenteClienteVO.setValorTransferencia(mov.getSaldoAtual());
                } else {
                    setApresentarBoteos(false);
                    throw new ConsistirException("O Cliente não possui crédito para transferência.");
                }

            } else if (operacao.equals("gerarParcela")) {
                mov = getFacade().getMovimentoContaCorrenteCliente().consultarPorCodigoPessoa(getMovimentoContaCorrenteClienteVO().getPessoa().getCodigo().intValue(), Uteis.NIVELMONTARDADOS_TODOS);
                if (mov != null && mov.getSaldoAtual() < 0) {
                    movimentoContaCorrenteClienteVO.setMovPagamentosVOs(mov.getMovPagamentosVOs());
                    movimentoContaCorrenteClienteVO.setSaldoAtual(mov.getSaldoAtual());
                    movimentoContaCorrenteClienteVO.setValorParcelaDebito(mov.getSaldoAtual() * -1);
                } else {
                    setApresentarBoteos(false);
                    throw new ConsistirException("O Cliente não possui débito para acertar.");
                }
            } else {
                mov = getFacade().getMovimentoContaCorrenteCliente().consultarPorCodigoPessoa(getMovimentoContaCorrenteClienteVO().getPessoa().getCodigo().intValue(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);

                if (mov == null) {
                    movimentoContaCorrenteClienteVO.setSaldoAtual(0.0);
                } else {
                    movimentoContaCorrenteClienteVO.setSaldoAtual(mov.getSaldoAtual());
                }
                this.setParamTipoConsulta("detalhada");
                consultarPaginado();
            }
        }
    }

    private void inicializarCliente(String operacao) throws Exception {
        ClienteControle clienteControle = (ClienteControle) context().getExternalContext().getSessionMap().get("ClienteControle");
        if (clienteControle != null) {
            clienteControle.pegarClienteTelaCliente();
            getControleConsulta().setValorConsulta(clienteControle.getClienteVO().getPessoa().getCodigo().toString());
            getControleConsulta().setCampoConsulta("codPessoa");
            //é setado o cliente para facilitar o uso a partir da tela do cliente.
            getMovimentoContaCorrenteClienteVO().setPessoa(clienteControle.getClienteVO().getPessoa());
            MovimentoContaCorrenteClienteVO mov;
            if (operacao.equals("transferencia")) {
                mov = getFacade().getMovimentoContaCorrenteCliente().consultarPorCodigoPessoa(getMovimentoContaCorrenteClienteVO().getPessoa().getCodigo().intValue(), Uteis.NIVELMONTARDADOS_TODOS);
                if (mov != null && mov.getSaldoAtual() > 0) {
                    movimentoContaCorrenteClienteVO.setMovPagamentosVOs(mov.getMovPagamentosVOs());
                    movimentoContaCorrenteClienteVO.setSaldoAtual(mov.getSaldoAtual());
                    movimentoContaCorrenteClienteVO.setValorTransferencia(mov.getSaldoAtual());
                } else {
                    setApresentarBoteos(false);
                    throw new ConsistirException("O Cliente não possui crédito para transferência.");
                }

            } else if (operacao.equals("gerarParcela")) {
                mov = getFacade().getMovimentoContaCorrenteCliente().consultarPorCodigoPessoa(getMovimentoContaCorrenteClienteVO().getPessoa().getCodigo().intValue(), Uteis.NIVELMONTARDADOS_TODOS);
                if (mov != null && mov.getSaldoAtual() < 0) {
                    movimentoContaCorrenteClienteVO.setMovPagamentosVOs(mov.getMovPagamentosVOs());
                    movimentoContaCorrenteClienteVO.setSaldoAtual(mov.getSaldoAtual());
                    movimentoContaCorrenteClienteVO.setValorParcelaDebito(mov.getSaldoAtual() * -1);
                } else {
                    setApresentarBoteos(false);
                    throw new ConsistirException("O Cliente não possui débito para acertar.");
                }
            } else {
                mov = getFacade().getMovimentoContaCorrenteCliente().consultarPorCodigoPessoa(getMovimentoContaCorrenteClienteVO().getPessoa().getCodigo().intValue(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);

                if (mov == null) {
                    movimentoContaCorrenteClienteVO.setSaldoAtual(0.0);
                } else {
                    movimentoContaCorrenteClienteVO.setSaldoAtual(mov.getSaldoAtual());
                }
                this.setParamTipoConsulta("detalhada");
                consultarPaginado();
            }
        }
    }

    private void inicializarClienteReceberDebito(String operacao, ClienteVO clienteVO) throws Exception {

        if (clienteVO != null) {
            getControleConsulta().setValorConsulta(clienteVO.getPessoa().getCodigo().toString());
            getControleConsulta().setCampoConsulta("codPessoa");
            //é setado o cliente para facilitar o uso a partir da tela do cliente.
            getMovimentoContaCorrenteClienteVO().setPessoa(clienteVO.getPessoa());
            MovimentoContaCorrenteClienteVO mov;
            if (operacao.equals("transferencia")) {
                mov = getFacade().getMovimentoContaCorrenteCliente().consultarPorCodigoPessoa(getMovimentoContaCorrenteClienteVO().getPessoa().getCodigo().intValue(), Uteis.NIVELMONTARDADOS_TODOS);
                if (mov != null && mov.getSaldoAtual() > 0) {
                    movimentoContaCorrenteClienteVO.setMovPagamentosVOs(mov.getMovPagamentosVOs());
                    movimentoContaCorrenteClienteVO.setSaldoAtual(mov.getSaldoAtual());
                    movimentoContaCorrenteClienteVO.setValorTransferencia(mov.getSaldoAtual());
                } else {
                    setApresentarBoteos(false);
                    throw new ConsistirException("O Cliente não possui crédito para transferência.");
                }

            } else if (operacao.equals("gerarParcela")) {
                mov = getFacade().getMovimentoContaCorrenteCliente().consultarPorCodigoPessoa(getMovimentoContaCorrenteClienteVO().getPessoa().getCodigo().intValue(), Uteis.NIVELMONTARDADOS_TODOS);
                if (mov != null && mov.getSaldoAtual() < 0) {
                    movimentoContaCorrenteClienteVO.setMovPagamentosVOs(mov.getMovPagamentosVOs());
                    movimentoContaCorrenteClienteVO.setSaldoAtual(mov.getSaldoAtual());
                    movimentoContaCorrenteClienteVO.setValorParcelaDebito(mov.getSaldoAtual() * -1);
                } else {
                    setApresentarBoteos(false);
                    throw new ConsistirException("O Cliente não possui débito para acertar.");
                }
            } else {
                mov = getFacade().getMovimentoContaCorrenteCliente().consultarPorCodigoPessoa(getMovimentoContaCorrenteClienteVO().getPessoa().getCodigo().intValue(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);

                if (mov == null) {
                    movimentoContaCorrenteClienteVO.setSaldoAtual(0.0);
                } else {
                    movimentoContaCorrenteClienteVO.setSaldoAtual(mov.getSaldoAtual());
                }
                this.setParamTipoConsulta("detalhada");
                consultarPaginado();
            }
        }
    }

    public void carregarDados(ClienteVO clienteVO) throws Exception {
        ClienteControle clienteControle = (ClienteControle) context().getExternalContext().getSessionMap().get("ClienteControle");

        if (clienteControle == null) {
            clienteControle = new ClienteControle();
            context().getExternalContext().getSessionMap().put("ClienteControle", clienteControle);
        }

        clienteControle.setClienteVO(clienteVO);
        if (clienteVO != null) {
            getControleConsulta().setValorConsulta(clienteVO.getPessoa().getCodigo().toString());
            getControleConsulta().setCampoConsulta("codPessoa");
            //é setado o cliente para facilitar o uso a partir da tela do cliente.
            getMovimentoContaCorrenteClienteVO().setPessoa(clienteVO.getPessoa());

            MovimentoContaCorrenteClienteVO mov;
            mov = getFacade().getMovimentoContaCorrenteCliente().consultarPorCodigoPessoa(getMovimentoContaCorrenteClienteVO().getPessoa().getCodigo().intValue(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);

                if (mov == null) {
                    movimentoContaCorrenteClienteVO.setSaldoAtual(0.0);
                } else {
                    movimentoContaCorrenteClienteVO.setSaldoAtual(mov.getSaldoAtual());
                }
                this.setParamTipoConsulta("detalhada");
                consultarPaginado();
            }
    }


    public void limparCampoCliente() {
        movimentoContaCorrenteClienteVO.setClienteTransferencia(new ClienteVO());
    }

    /**
     * Rotina responsável por disponibilizar os dados de um objeto da classe <code>MovimentoContaCorrenteCliente</code> para alteração.
     * O objeto desta classe é disponibilizado na session da página (request) para que o JSP correspondente possa disponibilizá-lo para edição.
     */
    public String editar() {
        Integer codigoConsulta = Integer.parseInt(request().getParameter("chavePrimaria"));
        try {
            MovimentoContaCorrenteClienteVO obj = getFacade().getMovimentoContaCorrenteCliente().consultarPorChavePrimaria(codigoConsulta, Uteis.NIVELMONTARDADOS_TODOS);
            inicializarAtributosRelacionados(obj);
            setListaConsultaPessoa(new ArrayList());
            obj.setNovoObj(false);
            obj.registrarObjetoVOAntesDaAlteracao();
            setMovimentoContaCorrenteClienteVO(obj);
            movimentoContaCorrenteClienteVO.setResponsavelAutorizacao(obj.getResponsavelAutorizacao());
            //       inicializarListasSelectItemTodosComboBox();
        } catch (Exception e) {
            setErro(true);
            setSucesso(false);
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
        return "editar";
    }

    /**
     * Método responsável inicializar objetos relacionados a classe <code>MovimentoContaCorrenteClienteVO</code>.
     * Esta inicialização é necessária por exigência da tecnologia JSF, que não trabalha com valores nulos para estes atributos.
     */
    public void inicializarAtributosRelacionados(MovimentoContaCorrenteClienteVO obj) {
        if (obj.getPessoa() == null) {
            obj.setPessoa(new PessoaVO());
        }

//        if (obj.getResponsavelRegistro() == null) {
//            obj.setResponsavelRegistro(new ColaboradorVO());
//        }

        if (obj.getResponsavelAutorizacao() == null) {
            obj.setResponsavelAutorizacao(new UsuarioVO());
        }

    }

    public List getTipoConsultarComboCliente() {
        List itens = new ArrayList();
        itens.add(new SelectItem("nomePessoa", "Nome"));
        itens.add(new SelectItem("matricula", "Matrícula"));
        itens.add(new SelectItem("codigo", "Código"));
        return itens;
    }

    public void transferir() {
        try {
            MovimentoContaCorrenteClienteVO depositante = new MovimentoContaCorrenteClienteVO();
            MovimentoContaCorrenteClienteVO beneficiado = new MovimentoContaCorrenteClienteVO();
            depositante.setPessoa(movimentoContaCorrenteClienteVO.getPessoa());
            beneficiado.setPessoa(movimentoContaCorrenteClienteVO.getClienteTransferencia().getPessoa());
            depositante.setValor(movimentoContaCorrenteClienteVO.getValorTransferencia());
            beneficiado.setValor(movimentoContaCorrenteClienteVO.getValorTransferencia());
            depositante.setTipoMovimentacao("DE");
            beneficiado.setTipoMovimentacao("CR");
            depositante.setDataRegistro(negocio.comuns.utilitarias.Calendario.hoje());
            beneficiado.setDataRegistro(negocio.comuns.utilitarias.Calendario.hoje());
            depositante.setDescricao("OPERAÇÃO DE TRANSFÊNCIA DE VALORES PARA O ALUNO: " + movimentoContaCorrenteClienteVO.getClienteTransferencia().getPessoa().getNome());
            beneficiado.setDescricao("OPERAÇÃO DE TRANSFÊNCIA DE VALORES DO ALUNO: " + movimentoContaCorrenteClienteVO.getPessoa().getNome());
            depositante.setResponsavelAutorizacao(movimentoContaCorrenteClienteVO.getResponsavelAutorizacao());
            beneficiado.setResponsavelAutorizacao(movimentoContaCorrenteClienteVO.getResponsavelAutorizacao());
            depositante.setSaldoAnterior(movimentoContaCorrenteClienteVO.getSaldoAtual());
            depositante.setSaldoAtual(Uteis.arredondarForcando2CasasDecimais(movimentoContaCorrenteClienteVO.getSaldoAtual() - movimentoContaCorrenteClienteVO.getValorTransferencia()));

            getFacade().getMovimentoContaCorrenteCliente().incluirTransferenciaCredito(depositante, beneficiado, movimentoContaCorrenteClienteVO);

            setApresentarBoteos(false);
            setMensagem("Transferência realizada com sucesso");
            setMensagemID("msg_transferencia_conta_corrente");
        } catch (Exception e) {
            setApresentarBoteos(true);
            setAbrirRichConfirmacaoTransferencia(false);
            setMensagemID("");
            setMensagemDetalhada("msg_erro", e.getMessage());
            e.printStackTrace();
        }
    }

    public void gerarParcelaDebito() {
        try {
            ClienteVO cliente = getFacade().getCliente().consultarPorCodigoPessoa(movimentoContaCorrenteClienteVO.getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            boolean permitirLancarVariasParcelasSaldoDevedor = getEmpresaLogado().isPermitirLancarVariasParcelasSaldoDevedor();
            if (!permitirLancarVariasParcelasSaldoDevedor) {
                permitirLancarVariasParcelasSaldoDevedor = !(getFacade().getMovProduto().consultarSeExisteSaldoDevedorCCEmAberto(
                        movimentoContaCorrenteClienteVO.getPessoa().getCodigo(),
                        cliente.getEmpresa().getCodigo()));
            }
            if (permitirLancarVariasParcelasSaldoDevedor) {
                MovimentoContaCorrenteClienteVO movCCC = new MovimentoContaCorrenteClienteVO();
                movCCC.setPessoa(movimentoContaCorrenteClienteVO.getPessoa());
                movCCC.setValor(movimentoContaCorrenteClienteVO.getValorParcelaDebito());
                movCCC.setTipoMovimentacao("CR");
                movCCC.setDataRegistro(negocio.comuns.utilitarias.Calendario.hoje());
                movCCC.setDescricao("PAGAMENTO DE DÉBITO");
                movCCC.setResponsavelAutorizacao(movimentoContaCorrenteClienteVO.getResponsavelAutorizacao());
                movCCC.setSaldoAnterior(movimentoContaCorrenteClienteVO.getSaldoAtual());
                movCCC.setSaldoAtual(Uteis.arredondarForcando2CasasDecimaisMantendoSinal(movimentoContaCorrenteClienteVO.getSaldoAtual() + movimentoContaCorrenteClienteVO.getValorParcelaDebito()));

                getFacade().getMovimentoContaCorrenteCliente().incluirParcelaParcialDebito(movCCC, movimentoContaCorrenteClienteVO);

                setApresentarBoteos(false);
                setMensagemID("msg_parcelaDebito_conta_corrente");
            } else {
                montarMsgAlert("Pague a(s) parcela(s) em aberto existente(s) referente(s) a pagamento(s) de saldo devedor na conta deste cliente antes de receber este débito");

                throw new Exception(
                        "Pague a(s) parcela(s) em aberto existente(s) referente(s) a pagamento(s) de saldo devedor na conta deste cliente antes de receber este débito");

            }
        } catch (Exception e) {
            setApresentarBoteos(false);
            setMensagemID("");
            setMensagemDetalhada("msg_erro", e.getMessage());
            e.printStackTrace();
        }
    }

    private void distribuirPagamentos(
            MovimentoContaCorrenteClienteVO depositante,
            MovimentoContaCorrenteClienteVO beneficiado) throws Exception {
        Iterator i = movimentoContaCorrenteClienteVO.getMovPagamentosVOs().iterator();
        Double valor = movimentoContaCorrenteClienteVO.getValorTransferencia();
        while (i.hasNext()) {
            MovPagamentoVO pagamento = (MovPagamentoVO) i.next();
            if (Uteis.arredondarForcando2CasasDecimais(pagamento.getValor()) <= valor && valor > 0.0) {
                valor = Uteis.arredondarForcando2CasasDecimais(valor - pagamento.getValor());
                beneficiado.getMovPagamentosVOs().add(pagamento);
            } else if (valor > 0.0) {
                pagamento.setValor(Uteis.arredondarForcando2CasasDecimais(pagamento.getValor() - valor));
                if (pagamento.getFormaPagamento().getTipoFormaPagamento().equals("CH")) {
                    pagamento.setChequeVOs(Ordenacao.ordenarLista(pagamento.getChequeVOs(), "dataCompensacao"));
                    pagamento = getFacade().getMovPagamento().atualizarListaCheques(pagamento);
                }
                if (pagamento.getFormaPagamento().getTipoFormaPagamento().equals("CA")) {
                    pagamento.setCartaoCreditoVOs(Ordenacao.ordenarLista(pagamento.getCartaoCreditoVOs(), "dataCompensacao"));
                    pagamento = getFacade().getMovPagamento().atualizarListaCartaoCredito(pagamento);
                }
                MovPagamentoVO novo = (MovPagamentoVO) pagamento.getClone(true);
                novo.setValor(Uteis.arredondarForcando2CasasDecimaisMantendoSinal(pagamento.getValorTotal() - pagamento.getValor()));
                pagamento.setValorTotal(pagamento.getValor());
                novo.setValorTotal(novo.getValor());
                if (pagamento.getFormaPagamento().getTipoFormaPagamento().equals("CH")) {
                    pagamento = getFacade().getMovPagamento().retiraChequesCancelados(pagamento);
                    novo = getFacade().getMovPagamento().atualizarChequeMovimentoCC(novo);
                }
                if (pagamento.getFormaPagamento().getTipoFormaPagamento().equals("CA")) {
                    pagamento = getFacade().getMovPagamento().retiraCartoesCancelados(pagamento);
                    novo = getFacade().getMovPagamento().atualizarCartaoMovimentoCC(novo);
                }

                getFacade().getMovPagamento().incluirSemCommit(novo);
                getFacade().getMovPagamento().alterarSemCommit(pagamento);
                depositante.getMovPagamentosVOs().add(pagamento);
                beneficiado.getMovPagamentosVOs().add(novo);
                valor = 0.0;

            } else {
                depositante.getMovPagamentosVOs().add(pagamento);
            }

        }


    }

    /**
     * @return
     * <AUTHOR>
     * 24/03/2011
     */
    public void gravar() {
        this.gravar(false);
    }

    /**
     * @return
     * <AUTHOR>
     * 24/03/2011
     */
    public void gravarCE() {
        this.gravar(true);
    }

    /**
     * Rotina responsável por gravar no BD os dados editados de um novo objeto da classe <code>MovimentoContaCorrenteCliente</code>.
     * Caso o objeto seja novo (ainda não gravado no BD) é acionado a operação <code>incluir()</code>. Caso contrário é acionado o <code>alterar()</code>.
     * Se houver alguma inconsistência o objeto não é gravado, sendo re-apresentado para o usuário juntamente com uma mensagem de erro.
     */
    public void gravar(boolean centralEventos) {
        setMsgAlert("");
        StringBuilder sbOnComplete = new StringBuilder("");
        try {
            MovimentoContaCorrenteClienteVO.validarDados(movimentoContaCorrenteClienteVO, "lancarCreditoDebito");

            if (movimentoContaCorrenteClienteVO.getTipoMovimentacao().equals("CR")) {
                movimentoContaCorrenteClienteVO.setSaldoAtual(movimentoContaCorrenteClienteVO.getSaldoAtual() + movimentoContaCorrenteClienteVO.getValor());
            } else {
                movimentoContaCorrenteClienteVO.setSaldoAtual(movimentoContaCorrenteClienteVO.getSaldoAtual() - movimentoContaCorrenteClienteVO.getValor());
            }
            if (centralEventos) {
                this.verificarAutorizacao();
                if (movimentoContaCorrenteClienteVO.isNovoObj().booleanValue()) {
                    getFacade().getMovimentoContaCorrenteCliente().incluirSemCommit(movimentoContaCorrenteClienteVO, true);
                    //LOG - INICIO
                    try {
                        movimentoContaCorrenteClienteVO.setObjetoVOAntesAlteracao(new MovimentoContaCorrenteClienteVO());
                        movimentoContaCorrenteClienteVO.setNovoObj(true);
                        registrarLogObjetoVO(movimentoContaCorrenteClienteVO, movimentoContaCorrenteClienteVO.getCodigo(), "MOVIMENTOCCC", 0);
                    } catch (Exception e) {
                        registrarLogErroObjetoVO("MOVIMENTOCCC", movimentoContaCorrenteClienteVO.getCodigo(), "ERRO AO GERAR LOG DE INCLUSÃO DE MOVIMENTO CONTA CORRENTE CLIENTE", this.getUsuarioLogado().getNome(), this.getUsuarioLogado().getUserOamd());
                        e.printStackTrace();
                    }
                    //LOG - FIM
                } else {
                    getFacade().getMovimentoContaCorrenteCliente().alterar(movimentoContaCorrenteClienteVO, true);
                    //LOG - INICIO
                    try {
                        registrarLogObjetoVO(movimentoContaCorrenteClienteVO, movimentoContaCorrenteClienteVO.getCodigo(), "MOVIMENTOCCC", 0);
                    } catch (Exception e) {
                        registrarLogErroObjetoVO("MOVIMENTOCCC", movimentoContaCorrenteClienteVO.getCodigo(), "ERRO AO GERAR LOG DE ALTERAÇÃO DE MOVIMENTO CONTA CORRENTE CLIENTE", this.getUsuarioLogado().getNome(), this.getUsuarioLogado().getUserOamd());
                        e.printStackTrace();
                    }
                    //LOG - FIM
                }
            } else {
                if (movimentoContaCorrenteClienteVO.isNovoObj()) {
                    if (movimentoContaCorrenteClienteVO.getTipoMovimentacao().equals("CR")) {
                        getFacade().getMovimentoContaCorrenteCliente().gerarParcelaCredito(movimentoContaCorrenteClienteVO);
                        setMensagemID("msg_dados_gravados_movimentacaoCCC");
                        try {
                            permissaoFuncionalidade(getUsuarioLogado(), "CaixaEmAberto", "2.44 - Operação - Caixa em Aberto");
                            sbOnComplete.append("fireElementFromAnyParent('form:linkCaixaEmAberto');");
                        } catch (Exception ex) {
                            sbOnComplete.append("");
                        }
                        setMsgAlert("Richfaces.showModalPanel('panelInformacao');");
                    } else {
                        getFacade().getMovimentoContaCorrenteCliente().gerarDebito(movimentoContaCorrenteClienteVO);
                        setMsgAlert("Richfaces.showModalPanel('panelInformacao');fireElementFromParent('form:btnAtualizaCliente');");
                        setMensagemID("msg_dados_gravados_movimentacaoDCC");
                    }
                } else {
                    getFacade().getMovimentoContaCorrenteCliente().alterar(movimentoContaCorrenteClienteVO);
                    setMsgAlert("Richfaces.showModalPanel('panelInformacao');fireElementFromParent('form:btnAtualizaCliente');");
                }
            }

            setSucesso(true);
            setErro(false);
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
        }
        sbOnComplete.append("Richfaces.hideModalPanel('panelInformacao');");
        if (!getErro()) {
            sbOnComplete.append("jQuery('.consultar')[0].click();");
        }
        setOncomplete(sbOnComplete.toString());
    }

    public String fecharPanelInformacao() throws Exception {
        if (getErro()) {
            return "";
        }
        return inicializarConsultar();
    }

    /**
     * Rotina responsavel por executar as consultas disponiveis no JSP MovimentoContaCorrenteClienteCons.jsp.
     * Define o tipo de consulta a ser executada, por meio de ComboBox denominado campoConsulta, disponivel neste mesmo JSP.
     * Como resultado, disponibiliza um List com os objetos selecionados na sessao da pagina.
     */
    public String consultar() {
        try {
            super.consultar();
            List objs = new ArrayList();
            if (getControleConsulta().getCampoConsulta().equals("codigo")) {
                if (getControleConsulta().getValorConsulta().equals("")) {
                    getControleConsulta().setValorConsulta("0");
                }
                int valorInt = Integer.parseInt(getControleConsulta().getValorConsulta());
                objs = getFacade().getMovimentoContaCorrenteCliente().consultarPorCodigo(new Integer(valorInt), true, Uteis.NIVELMONTARDADOS_TODOS);
            }

            if (getControleConsulta().getCampoConsulta().equals("descricao")) {
                objs = getFacade().getMovimentoContaCorrenteCliente().consultarPorDescricao(getControleConsulta().getValorConsulta(), true, Uteis.NIVELMONTARDADOS_TODOS);
            }
            if (getControleConsulta().getCampoConsulta().equals("nome")) {
                objs = getFacade().getMovimentoContaCorrenteCliente().consultarPorNomeCliente(getControleConsulta().getValorConsulta(), Uteis.NIVELMONTARDADOS_TODOS);
            }


            if (getControleConsulta().getCampoConsulta().equals("dataRegistro")) {
                Date valorData = Uteis.getDate(getControleConsulta().getValorConsulta());
                objs = getFacade().getMovimentoContaCorrenteCliente().consultarPorDataRegistro(Uteis.getDateTime(valorData, 0, 0, 0), Uteis.getDateTime(valorData, 23, 59, 59), true, Uteis.NIVELMONTARDADOS_TODOS);
            }

            objs = ControleConsulta.obterSubListPaginaApresentar(objs, controleConsulta);
            definirVisibilidadeLinksNavegacao(controleConsulta.getPaginaAtual(), controleConsulta.getNrTotalPaginas());
            setListaConsulta(objs);
            limparMsg();
            return "consultar";
        } catch (Exception e) {
            setListaConsulta(new ArrayList());
            setMensagemDetalhada("msg_erro", e.getMessage());
            return "consultar";
        }

    }

    /**
     * Metodo listener que obtem os parametros necessarios para realizar
     * a paginacao e filtros da tela
     * <p>
     * * Autora: Carla
     * Criado em 14/01/2011
     */
    public void consultarPaginadoListener(ActionEvent evt) {
        //==================================================================================================================================

        //VERIFICACAO NECESSARIA POR CAUSA DOS FILTROS
        Object compPaginaInicial = evt.getComponent().getAttributes().get("paginaInicial");
        if (compPaginaInicial != null && !"".equals(compPaginaInicial.toString())) {
            if (compPaginaInicial.toString().equals("paginaInicial")) {
                setConfPaginacao(new ConfPaginacao());
            }
        }

        //Obtendo qual pagina deverá ser exibida
        Object component = evt.getComponent().getAttributes().get("pagNavegacao");
        if (component != null && !"".equals(component.toString())) {
            getConfPaginacao().setPagNavegacao(component.toString());
        }

        //==================================================================================================================================

        Object compTipoConsulta = evt.getComponent().getAttributes().get("tipoConsulta");
        if (compTipoConsulta != null && !"".equals(compTipoConsulta.toString())) {
            if (!compTipoConsulta.toString().equals(this.paramTipoConsulta)) {
                setConfPaginacao(new ConfPaginacao());
            }
            this.setParamTipoConsulta(new String(compTipoConsulta.toString()));
        }
        consultarPaginado();
    }

    /**
     * Metodo responsavel por retornar uma consulta paginada em banco
     * <p>
     * * Autora: Carla
     * Criado em 14/01/2011
     */
    @SuppressWarnings("unchecked")
    public void consultarPaginado() {
        try {
            super.consultar();
            List objs = new ArrayList();
            MovimentoContaCorrenteClienteFiltroVO filtro = new MovimentoContaCorrenteClienteFiltroVO();
            filtro.setControlarAcesso(true);
            filtro.setNivelMontarDados(Uteis.NIVELMONTARDADOS_TELACONSULTA);

            if ("detalhada".equals(this.getParamTipoConsulta())) {
                if (getEmpresaLogado().getCodigo() != 0) {
                    filtro.setEmpresaVO(new EmpresaVO());
                    filtro.getEmpresaVO().setCodigo(getEmpresaLogado().getCodigo());
                }
                filtro.setMovContaCorrenteClienteVO(new MovimentoContaCorrenteClienteVO());
                filtro.getMovContaCorrenteClienteVO().setDataRegistro(null);

                if (getControleConsulta().getCampoConsulta().equals("codPessoa")) {
                    int valorInt = 0;
                    if (!getControleConsulta().getValorConsulta().trim().isEmpty()) {
                        valorInt = Integer.parseInt(getControleConsulta().getValorConsulta().trim());
                        filtro.setCodigoPessoa(valorInt);

                    }
                } else //quando o usuario selecionar o nome do cliente para consulta
                    if (getControleConsulta().getCampoConsulta().equals("nomeCliente")) {
                        if (!getControleConsulta().getValorConsulta().trim().isEmpty()) {
                            filtro.getMovContaCorrenteClienteVO().setPessoa(new PessoaVO());
                            filtro.getMovContaCorrenteClienteVO().getPessoa().setNome(getControleConsulta().getValorConsulta().trim());
                        }
                    } //quando o usuario selecionar o tipo de consulta por codigo de movimento de conta corrente
                    else if (getControleConsulta().getCampoConsulta().equals("codigo")) {
                        int valorInt = 0;
                        if (!getControleConsulta().getValorConsulta().trim().isEmpty()) {
                            valorInt = Integer.parseInt(getControleConsulta().getValorConsulta().trim());
                            filtro.getMovContaCorrenteClienteVO().setCodigo(valorInt);

                        }
                    } //quando o usuario selecionar a descricao do movimento para consulta
                    else if (getControleConsulta().getCampoConsulta().equals("descricao")) {
                        if (!getControleConsulta().getValorConsulta().trim().isEmpty()) {
                            filtro.getMovContaCorrenteClienteVO().setDescricao(getControleConsulta().getValorConsulta().trim());
                        }
                    }//quando o usuario selecionar a data de registro do movimento para consulta
                    else if (getControleConsulta().getCampoConsulta().equals("dataRegistro")) {
                        if (!getControleConsulta().getValorConsulta().trim().isEmpty()) {
                            validarData(filtro);
                        }
                    }

                objs = getFacade().getMovimentoContaCorrenteCliente().consultarPaginado(filtro, getConfPaginacao());

                setListaConsulta(objs);
                setMensagemID("msg_dados_consultados");
                setSucesso(true);
                setErro(false);

                this.getConfPaginacao().definirVisibilidadeLinksNavegacao();
            }
        } catch (java.text.ParseException e) {
            setSucesso(false);
            setErro(true);
            setMensagemDetalhada("msg_erro", "A data deve ser informada com o seguinte formato: dd/mm/aaaa");
        } catch (Exception e) {
            setListaConsulta(new ArrayList());
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
        }
    }

    public void validarData(MovimentoContaCorrenteClienteFiltroVO filtro) throws Exception {
        Date dataRegistro = Uteis.getSQLData(Uteis.getDate(getControleConsulta().getValorConsulta().trim()));
        if (dataRegistro != null) {
            filtro.getMovContaCorrenteClienteVO().setDataRegistro(dataRegistro);
            return;
        }
    }

    public void richConfirmacaoDadosTransferencia() {
        AutorizacaoFuncionalidadeControle auto = getControlador(AutorizacaoFuncionalidadeControle.class);
        auto.setPedirPermissao(false);
        AutorizacaoFuncionalidadeListener listener = new AutorizacaoFuncionalidadeListener() {

            @Override
            public void onAutorizacaoComSucesso() throws Exception {
                AutorizacaoFuncionalidadeControle auto = getControlador(AutorizacaoFuncionalidadeControle.class);
                movimentoContaCorrenteClienteVO.setResponsavelAutorizacao(auto.getUsuario());
                transferir();
                setExecutarAoCompletar("fireElementFromParent('form:btnAtualizaCliente');" + getMensagemNotificar());
            }

            @Override
            public void onAutorizacaoComErro(Exception e) {
                montarErro(e);
            }

            @Override
            public void onFecharModalAutorizacao() {
                limparMsg();
            }
        };

        limparMsg();
        try {
            movimentoContaCorrenteClienteVO.validarDados(movimentoContaCorrenteClienteVO, "transferencia");
            setMensagem("");
            setMensagemDetalhada("");
            auto.autorizar("Confirmação de Transferência de Valores", "AlterarSaldoContaCorrenteCliente",
                    "Você precisa da permissão \"2.42 - Alterar o saldo da conta corrente (cancelar débito ou devolver crédito) do aluno\"",
                    "form,panelBotoes,panelMensagem,msgTfr,msgConf", listener);
        } catch (Exception e) {
            montarErro(e);
            setMensagemID("");
            setMensagemDetalhada(e.getMessage());
        }

    }

    /**
     * FIm
     **/
    public void movimentoContacorrente() {
        try {
            inicializarFacades();
            setControleConsulta(new ControleConsulta());
            super.consultar();
            setListaConsulta(new ArrayList());
            List objs = new ArrayList();
            ClienteControle clienteControle = (ClienteControle) context().getExternalContext().getSessionMap().get("ClienteControle");
            Integer valor = clienteControle.getClienteVO().getPessoa().getCodigo().intValue();
            objs = getFacade().getMovimentoContaCorrenteCliente().consultarPorCodigoPessoa(valor, true, Uteis.NIVELMONTARDADOS_TODOS);

            //   objs = ControleConsulta.obterSubListPaginaApresentar(objs, controleConsulta);
            //   definirVisibilidadeLinksNavegacao(controleConsulta.getPaginaAtual(), controleConsulta.getNrTotalPaginas());
            setListaConsulta(objs);
            setMensagemID("msg_dados_consultados");

        } catch (Exception e) {
            setListaConsulta(new ArrayList());
            setMensagemDetalhada("msg_erro", e.getMessage());

        }
    }

    public void consultarPessoa() {
        try {
            super.consultar();
            List objs = new ArrayList();
            if (getCampoConsultaPessoa().equals("codigo")) {
                if (getValorConsultaPessoa().equals("")) {
                    setValorConsultaPessoa("0");
                }
                int valorInt = Integer.parseInt(getValorConsultaPessoa());
                objs = getFacade().getCliente().consultarPorCodigo(new Integer(valorInt), getEmpresaLogado().getCodigo().intValue(), true, Uteis.NIVELMONTARDADOS_MINIMOS);
            }
            if (getCampoConsultaPessoa().equals("nome")) {
                objs = getFacade().getCliente().consultarPorNomePessoa(getValorConsultaPessoa(), getEmpresaLogado().getCodigo().intValue(), Uteis.NIVELMONTARDADOS_MINIMOS, null);
            }
            if (getCampoConsultaPessoa().equals("matricula")) {
                objs = getFacade().getCliente().consultarPorMatricula(getValorConsultaPessoa(), getEmpresaLogado().getCodigo().intValue(), true, Uteis.NIVELMONTARDADOS_MINIMOS);
            }
            setListaConsultaPessoa(objs);
            setMensagemID("msg_dados_consultados");
        } catch (Exception e) {
            setListaConsultaPessoa(new ArrayList());
            setMensagemDetalhada("msg_erro", e.getMessage());
        }

    }

    //    public String consultarMovimentoPessoa() throws Exception {
//        inicializarFacades();
//        setMovimentoContaCorrenteClienteVO(new MovimentoContaCorrenteClienteVO());
//        setControleConsulta(new ControleConsulta());
//        setListaConsulta(new ArrayList());
//        ClienteControle clienteControle = (ClienteControle) context().getExternalContext().getSessionMap().get("ClienteControle");
//        ClienteVO clienteVO = clienteControle.getClienteVO();
//        movimentoContaCorrenteClienteVO.setSaldoAtual(clienteControle.getSaldoClienteAcademia());
//        movimentoContaCorrenteClienteVO.setPessoa(clienteVO.getPessoa());
//        super.consultar();
//        List objs = new ArrayList();
//        objs = movimentoContaCorrenteClienteFacade.consultarPorCodigoPessoa(clienteVO.getPessoa().getCodigo().intValue(), true, Uteis.NIVELMONTARDADOS_TODOS);
////        objs = ControleConsulta.obterSubListPaginaApresentar(objs, controleConsulta);
////        definirVisibilidadeLinksNavegacao(controleConsulta.getPaginaAtual(), controleConsulta.getNrTotalPaginas());
//        setListaConsulta(objs);
//        return "consultaMovimentacao";
//    }
    public void selecionarPessoa() throws Exception {
        ClienteVO obj = (ClienteVO) context().getExternalContext().getRequestMap().get("cliente");
        MovimentoContaCorrenteClienteVO mov = getFacade().getMovimentoContaCorrenteCliente().consultarPorCodigoPessoa(obj.getPessoa().getCodigo().intValue(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        movimentoContaCorrenteClienteVO.setPessoa(obj.getPessoa());
        if (mov == null) {
            movimentoContaCorrenteClienteVO.setSaldoAtual(0.0);
        } else {
            movimentoContaCorrenteClienteVO.setSaldoAtual(mov.getSaldoAtual());
        }


    }

    public void verificarTipoConsulta() {
        if (getControleConsulta().getCampoConsulta().equals("dataRegistro")) {
        }
    }

    /**
     * @return
     * <AUTHOR>
     * 24/03/2011
     */
    public String excluir() {
        return this.excluir(false);
    }

    /**
     * @return
     * <AUTHOR>
     * 24/03/2011
     */
    public String excluirCE() {
        return this.excluir(true);
    }

    /**
     * Operação responsável por processar a exclusão um objeto da classe <code>MovimentoContaCorrenteClienteVO</code>
     * Após a exclusão ela automaticamente aciona a rotina para uma nova inclusão.
     */
    public String excluir(boolean centralEventos) {
        try {
            if (centralEventos) {
                this.verificarAutorizacao();
                getFacade().getMovimentoContaCorrenteCliente().excluir(movimentoContaCorrenteClienteVO);
                //registrar log
                registrarLogExclusaoObjetoVO(movimentoContaCorrenteClienteVO, movimentoContaCorrenteClienteVO.getCodigo().intValue(), "MOVIMENTOCCC", 0);
            } else {
                getFacade().getMovimentoContaCorrenteCliente().excluir(movimentoContaCorrenteClienteVO);
            }
            setMovimentoContaCorrenteClienteVO(
                    new MovimentoContaCorrenteClienteVO());
            setListaConsultaPessoa(new ArrayList());
            setMensagemID("msg_dados_excluidos");
            return "editar";
        } catch (Exception e) {
            if (e.getMessage().contains("ERRO: atualização ou exclusão em tabela \"movimentocontacorrentecliente\" viola restrição de chave estrangeira") || e.getMessage().contains("ERROR: update or delete on table \"movimentocontacorrentecliente\" violates foreign key")) {
                setMensagemDetalhada("Esta Movimentação de Conta corrente não pode ser excluída, pois já foi utilizada!");
            } else {
                setMensagemDetalhada("msg_erro", e.getMessage());
            }
            return "editar";
        }

    }

    public void irPaginaInicial() throws Exception {
        controleConsulta.setPaginaAtual(1);
        this.consultar();
    }

    public void irPaginaAnterior() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getPaginaAtual() - 1);
        this.consultar();
    }

    public void irPaginaPosterior() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getPaginaAtual() + 1);
        this.consultar();
    }

    public void irPaginaFinal() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getNrTotalPaginas());
        this.consultar();
    }

//    /**
//     * Método responsável por gerar uma lista de objetos do tipo <code>SelectItem</code> para preencher
//     * o comboBox relativo ao atributo <code>ResponsavelAutorizacao</code>.
//     */
//    public void montarListaSelectItemResponsavelAutorizacao(String prm) throws Exception {
//        List resultadoConsulta = consultarColaboradorPorSituacao(prm);
//        Iterator i = resultadoConsulta.iterator();
//        List objs = new ArrayList();
//        objs.add(new SelectItem(new Integer(0), ""));
//        while (i.hasNext()) {
//            ColaboradorVO obj = (ColaboradorVO) i.next();
//            objs.add(new SelectItem(obj.getCodigo(), obj.getSituacao().toString()));
//        }
//
//        setListaSelectItemResponsavelAutorizacao(objs);
//    }
//
//    /**
//     * Método responsável por atualizar o ComboBox relativo ao atributo <code>ResponsavelAutorizacao</code>.
//     * Buscando todos os objetos correspondentes a entidade <code>Colaborador</code>. Esta rotina não recebe parâmetros para filtragem de dados, isto é
//     * importante para a inicialização dos dados da tela para o acionamento por meio requisições Ajax.
//     */
//    public void montarListaSelectItemResponsavelAutorizacao() {
//        try {
//            montarListaSelectItemResponsavelAutorizacao("");
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//
//    }
//
//    /**
//     * Método responsável por gerar uma lista de objetos do tipo <code>SelectItem</code> para preencher
//     * o comboBox relativo ao atributo <code>ResponsavelRegistro</code>.
//     */
//    public void montarListaSelectItemResponsavelRegistro(String prm) throws Exception {
//        List resultadoConsulta = consultarColaboradorPorSituacao(prm);
//        Iterator i = resultadoConsulta.iterator();
//        List objs = new ArrayList();
//        objs.add(new SelectItem(new Integer(0), ""));
//        while (i.hasNext()) {
//            ColaboradorVO obj = (ColaboradorVO) i.next();
//            objs.add(new SelectItem(obj.getCodigo(), obj.getSituacao().toString()));
//        }
//
//        setListaSelectItemResponsavelRegistro(objs);
//    }
//
//    /**
//     * Método responsável por atualizar o ComboBox relativo ao atributo <code>ResponsavelRegistro</code>.
//     * Buscando todos os objetos correspondentes a entidade <code>Colaborador</code>. Esta rotina não recebe parâmetros para filtragem de dados, isto é
//     * importante para a inicialização dos dados da tela para o acionamento por meio requisições Ajax.
//     */
//    public void montarListaSelectItemResponsavelRegistro() {
//        try {
//            montarListaSelectItemResponsavelRegistro("");
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//
//    }
//
//    /**
//     * Método responsável por consultar dados da entidade <code><code> e montar o atributo <code>situacao</code>
//     * Este atributo é uma lista (<code>List</code>) utilizada para definir os valores a serem apresentados no ComboBox correspondente
//     */
//    public List consultarColaboradorPorSituacao(
//            String situacaoPrm) throws Exception {
//        List lista = ColaboradorFacade.consultarPorSituacao(situacaoPrm, false, Uteis.NIVELMONTARDADOS_TODOS);
//        return lista;
//    }
//
//    /**
//     * Método responsável por gerar uma lista de objetos do tipo <code>SelectItem</code> para preencher
//     * o comboBox relativo ao atributo <code>Cliente</code>.
//     */
//    public void montarListaSelectItemCliente(String prm) throws Exception {
//        List resultadoConsulta = consultarClientePorMatricula(prm);
//        Iterator i = resultadoConsulta.iterator();
//        List objs = new ArrayList();
//        objs.add(new SelectItem(new Integer(0), ""));
//        while (i.hasNext()) {
//            ClienteVO obj = (ClienteVO) i.next();
//            objs.add(new SelectItem(obj.getCodigo(), obj.getMatricula().toString()));
//        }
//
//        setListaSelectItemCliente(objs);
//    }
//
//    /**
//     * Método responsável por atualizar o ComboBox relativo ao atributo <code>Cliente</code>.
//     * Buscando todos os objetos correspondentes a entidade <code>Cliente</code>. Esta rotina não recebe parâmetros para filtragem de dados, isto é
//     * importante para a inicialização dos dados da tela para o acionamento por meio requisições Ajax.
//     */
//    public void montarListaSelectItemCliente() {
//        try {
//            montarListaSelectItemCliente("");
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//
//    }
//
//    /**
//     * Método responsável por consultar dados da entidade <code><code> e montar o atributo <code>matricula</code>
//     * Este atributo é uma lista (<code>List</code>) utilizada para definir os valores a serem apresentados no ComboBox correspondente
//     */
//    public List consultarClientePorMatricula(
//            String matriculaPrm) throws Exception {
//        List lista = clienteFacade.consultarPorMatricula(matriculaPrm, false, Uteis.NIVELMONTARDADOS_TODOS);
//        return lista;
//    }

    /**
     * Método responsável por inicializar a lista de valores (<code>SelectItem</code>) para todos os ComboBox's.
     */
//    public void inicializarListasSelectItemTodosComboBox() {
//        montarListaSelectItemCliente();
//        montarListaSelectItemResponsavelRegistro();
//        montarListaSelectItemResponsavelAutorizacao();
//    }
    public List getListaSelectItemTipoMovimentacao() throws Exception {
        List objs = new ArrayList();
        Hashtable situacaoClientes = (Hashtable) Dominios.getTipoMovimentacao();
        if (getUsuario().getLinguagem().equals("en")){
            situacaoClientes.put("CR", "Credit");
            situacaoClientes.put("DE", "Debit");
        }
        Enumeration keys = situacaoClientes.keys();
        while (keys.hasMoreElements()) {
            String value = (String) keys.nextElement();
            String label = (String) situacaoClientes.get(value);
            objs.add(new SelectItem(value, label));
        }
        SelectItemOrdemValor ordenador = new SelectItemOrdemValor();
        Collections.sort((List) objs, ordenador);
        return objs;
    }
//

    /**
     * Rotina responsável por preencher a combo de consulta da telas.
     */
    public List getTipoConsultaCombo() {
        List itens = new ArrayList();
        itens.add(new SelectItem("nomeCliente", "Nome do Cliente"));
        itens.add(new SelectItem("codigo", "Código"));
        //itens.add(new SelectItem("matriculaCliente", "Matricula"));
        //   itens.add(new SelectItem("saldoAnteior", "Saldo Anteior"));
        //   itens.add(new SelectItem("saldoAtual", "Saldo Atual"));
        itens.add(new SelectItem("descricao", "Descrição"));
        itens.add(new SelectItem("dataRegistro", "Data do Registro"));
        itens.add(new SelectItem("codPessoa", "Código da pessoa"));
        //    itens.add(new SelectItem("situacaoColaborador", "Responsável pelo Registro"));
        //     itens.add(new SelectItem("situacaoColaborador", "Responsável pela Autorização"));
        return itens;
    }

    public List getTipoConsultaComboPessoa() {
        List itens = new ArrayList();
        itens.add(new SelectItem("nome", "Nome"));
        itens.add(new SelectItem("codigo", "Código"));
        itens.add(new SelectItem("matricula", "Matricula"));
        return itens;
    }

    /**
     * Rotina responsável por organizar a paginação entre as páginas resultantes de uma consulta.
     *
     * @throws Exception
     */
    public String inicializarConsultar() throws Exception {
        setPaginaAtualDeTodas("0/0");
        setListaConsulta(new ArrayList());
        definirVisibilidadeLinksNavegacao(0, 0);
        setMensagemID("msg_entre_prmconsulta");
        inicializarCliente("");
        return "consultar";
    }

    /**
     * Operação que inicializa as Interfaces Façades com os respectivos objetos de
     * persistência dos dados no banco de dados.
     */
    protected boolean inicializarFacades() {
        try {
            super.inicializarFacades();

            return true;
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro_conectarBD", e.getMessage());
            return false;
        }

    }

    /* Teste de Carla para consultar cliente pela chave primarias
    public String consultarPorChavePrimaria() {
    try {
    super.consultar();
    List objs = new ArrayList();
    if (getListaConsultaPessoa().equals(getCampoConsultaPessoa())) {
    if (getControleConsulta().getValorConsulta().equals("")) {
    getControleConsulta().setValorConsulta("0");
    }
    int valorInt = Integer.parseInt(getControleConsulta().getValorConsulta());
    objs = getFacade().getMovimentoContaCorrenteCliente().consultarPorCodigo(new Integer(valorInt), true, Uteis.NIVELMONTARDADOS_TODOS);
    }

    if (getControleConsulta().getCampoConsulta().equals("descricao")) {
    objs = getFacade().getMovimentoContaCorrenteCliente().consultarPorDescricao(getControleConsulta().getValorConsulta(), true, Uteis.NIVELMONTARDADOS_TODOS);
    }
    if (getControleConsulta().getCampoConsulta().equals("nome")) {
    objs = getFacade().getMovimentoContaCorrenteCliente().consultarPorNomeCliente(getControleConsulta().getValorConsulta(), Uteis.NIVELMONTARDADOS_TODOS);
    }



    if (getControleConsulta().getCampoConsulta().equals("dataRegistro")) {
    Date valorData = Uteis.getDate(getControleConsulta().getValorConsulta());
    objs = getFacade().getMovimentoContaCorrenteCliente().consultarPorDataRegistro(Uteis.getDateTime(valorData, 0, 0, 0), Uteis.getDateTime(valorData, 23, 59, 59), true, Uteis.NIVELMONTARDADOS_TODOS);
    }

    objs = ControleConsulta.obterSubListPaginaApresentar(objs, controleConsulta);
    definirVisibilidadeLinksNavegacao(controleConsulta.getPaginaAtual(), controleConsulta.getNrTotalPaginas());
    setListaConsulta(objs);
    setMensagemID("msg_dados_consultados");
    return "consultar";
    } catch (Exception e) {
    setListaConsulta(new ArrayList());
    setMensagemDetalhada("msg_erro", e.getMessage());
    return "consultar";
    }

    }*/
//    public List getListaSelectItemResponsavelAutorizacao() {
//        return (listaSelectItemResponsavelAutorizacao);
//    }
//
//    public void setListaSelectItemResponsavelAutorizacao(List listaSelectItemResponsavelAutorizacao) {
//        this.listaSelectItemResponsavelAutorizacao = listaSelectItemResponsavelAutorizacao;
//    }
//    public List getListaSelectItemResponsavelRegistro() {
//        return (listaSelectItemResponsavelRegistro);
//    }
//
//    public void setListaSelectItemResponsavelRegistro(List listaSelectItemResponsavelRegistro) {
//        this.listaSelectItemResponsavelRegistro = listaSelectItemResponsavelRegistro;
//    }
//    public List getListaSelectItemCliente() {
//        return (listaSelectItemCliente);
//    }
//
//    public void setListaSelectItemCliente(List listaSelectItemCliente) {
//        this.listaSelectItemCliente = listaSelectItemCliente;
//    }
    public MovimentoContaCorrenteClienteVO getMovimentoContaCorrenteClienteVO() {
        return movimentoContaCorrenteClienteVO;
    }

    public void setMovimentoContaCorrenteClienteVO(MovimentoContaCorrenteClienteVO movimentoContaCorrenteClienteVO) {
        this.movimentoContaCorrenteClienteVO = movimentoContaCorrenteClienteVO;
    }

    public String getCampoConsultaPessoa() {
        return campoConsultaPessoa;
    }

    public void setCampoConsultaPessoa(String campoConsultaPessoa) {
        this.campoConsultaPessoa = campoConsultaPessoa;
    }

    public String getValorConsultaPessoa() {
        return valorConsultaPessoa;
    }

    public void setValorConsultaPessoa(String valorConsultaPessoa) {
        this.valorConsultaPessoa = valorConsultaPessoa;
    }

    public List getListaConsultaPessoa() {
        return listaConsultaPessoa;
    }

    public void setListaConsultaPessoa(List listaConsultaPessoa) {
        this.listaConsultaPessoa = listaConsultaPessoa;
    }

    /**
     * @return the paramTipoConsulta
     */
    public String getParamTipoConsulta() {
        return paramTipoConsulta;
    }

    /**
     * @param paramTipoConsulta the paramTipoConsulta to set
     */
    public void setParamTipoConsulta(String paramTipoConsulta) {
        this.paramTipoConsulta = paramTipoConsulta;
    }

    public void consultarUsuarioSenha() throws Exception {
        try {
            movimentoContaCorrenteClienteVO.setResponsavelAutorizacao(getFacade().getUsuario().consultarPorChavePrimaria(movimentoContaCorrenteClienteVO.getResponsavelAutorizacao().getCodigo().intValue(), Uteis.NIVELMONTARDADOS_TODOS));
            setMensagemID("msg_dados_consultados");
            setMensagemDetalhada("");
        } catch (Exception e) {
            setMensagemID("");
            setMensagemDetalhada(e.getMessage());
        }
    }

    public void setCampoConsultaCliente(String campoConsultaCliente) {
        this.campoConsultaCliente = campoConsultaCliente;
    }

    public String getCampoConsultaCliente() {
        return campoConsultaCliente;
    }

    public void setAbrirRichConfirmacaoTransferencia(
            boolean abrirRichConfirmacaoTransferencia) {
        this.abrirRichConfirmacaoTransferencia = abrirRichConfirmacaoTransferencia;
    }

    public boolean isAbrirRichConfirmacaoTransferencia() {
        return abrirRichConfirmacaoTransferencia;
    }

    public String getValorConsultarCliente() {
        return valorConsultarCliente;
    }

    public void setValorConsultarCliente(String valorConsultarCliente) {
        this.valorConsultarCliente = valorConsultarCliente;
    }

    public void consultarCliente() {
        try {
            List objs = new ArrayList();
            String situacao = "";
            ClienteControle clienteControle = (ClienteControle) context().getExternalContext().getSessionMap().get("ClienteControle");
            if (getCampoConsultaCliente().equals("codigo")) {
                if (getValorConsultarCliente().equals("")) {
                    setValorConsultarCliente("0");
                }
                int valorInt = Integer.parseInt(getValorConsultarCliente());
                objs = getFacade().getCliente().consultarPorCodigoPessoaSemClienteCancelamento(
                        new Integer(valorInt), getEmpresaLogado().getCodigo().intValue(), 0,
                        situacao, clienteControle.getClienteVO().getCodigo(), true, Uteis.NIVELMONTARDADOS_TELACONSULTAESPECIAL);
            }
            if (getCampoConsultaCliente().equals("nomePessoa")) {
                objs = getFacade().getCliente().consultarPorNomePessoaSemClienteCancelamento(
                        getValorConsultarCliente(), getEmpresaLogado().getCodigo().intValue(),
                        0, situacao, clienteControle.getClienteVO().getCodigo(), true, Uteis.NIVELMONTARDADOS_MINIMOS);
            }
            if (getCampoConsultaCliente().equals("matricula")) {
                objs = getFacade().getCliente().consultarPorMatricula(getValorConsultarCliente(),
                        getEmpresaLogado().getCodigo().intValue(), 0, situacao, null, null, 0, 0, 0, 0, true, Uteis.NIVELMONTARDADOS_TELACONSULTAESPECIAL);
            }
            setListaConsultarCliente(objs);
            setMensagemDetalhada("");
            setMensagemID("msg_dados_consultados");
        } catch (Exception e) {
            setListaConsultarCliente(new ArrayList());
            setMensagemID("");
            setMensagemDetalhada(e.getMessage());
        }
    }

    public void selecionarCliente() throws Exception {
        ClienteVO obj = (ClienteVO) context().getExternalContext().getRequestMap().get("cliente");
        movimentoContaCorrenteClienteVO.setClienteTransferencia(getFacade().getCliente().consultarPorChavePrimaria(obj.getCodigo(), Uteis.NIVELMONTARDADOS_TODOS));
        setMensagemDetalhada("");
    }

    public void setListaConsultarCliente(List listaConsultarCliente) {
        this.listaConsultarCliente = listaConsultarCliente;
    }

    public List getListaConsultarCliente() {
        return listaConsultarCliente;
    }

    public Boolean getApresentarBoteos() {
        return apresentarBoteos;
    }

    public void setApresentarBoteos(Boolean apresentarBoteos) {
        this.apresentarBoteos = apresentarBoteos;
    }

    public void richConfirmacaoGerarParcela() {
        AutorizacaoFuncionalidadeControle auto = getControlador(AutorizacaoFuncionalidadeControle.class);
        auto.setPedirPermissao(false);
        AutorizacaoFuncionalidadeListener listener = new AutorizacaoFuncionalidadeListener() {

            @Override
            public void onAutorizacaoComSucesso() {
                AutorizacaoFuncionalidadeControle auto = getControlador(AutorizacaoFuncionalidadeControle.class);
                gerarParcelaDebito();
                setExecutarAoCompletar("fireElementFromParent('form:btnAtualizaCliente');" + getMensagemNotificar());
            }

            @Override
            public void onAutorizacaoComErro(Exception e) {
                montarErro(e);
            }

            @Override
            public void onFecharModalAutorizacao() {
                limparMsg();
                auto.setRenderComponents("panelAutorizacaoFuncionalidade");
            }
        };

        limparMsg();
        try {
            MovimentoContaCorrenteClienteVO.validarDados(movimentoContaCorrenteClienteVO, "gerarParcela");
            setMensagem("");
            setMensagemDetalhada("");
            if (!possuiPermissaoEditarSaldoContaCorrente) {
                auto.autorizar("Confirmação de Geração de Parcela", "AlterarSaldoContaCorrenteCliente",
                        "Você precisa da permissão \"2.42 - Alterar o saldo da conta corrente (cancelar débito ou devolver crédito) do aluno\"",
                        "form,panelBotoes,panelMensagem,msgGrPar,msgConf", listener);
            } else {
                gerarParcelaDebito();
                setMsgAlert("fireElementFromParent('form:btnAtualizaCliente');");
            }
        } catch (Exception e) {
            montarErro(e);
            setMensagemID("");
            setMensagemDetalhada(e.getMessage());
        }

    }

    public Boolean getAbrirRichConfirmacaoTransferencia() {
        return abrirRichConfirmacaoTransferencia;
    }

    public void setAbrirRichConfirmacaoTransferencia(Boolean abrirRichConfirmacaoTransferencia) {
        this.abrirRichConfirmacaoTransferencia = abrirRichConfirmacaoTransferencia;
    }

    public void limparPessoa() {
        setMovimentoContaCorrenteClienteVO(new MovimentoContaCorrenteClienteVO());
    }

    public void exportar(ActionEvent evt) throws Exception {
        try {
            oncomplete = "";
            ExportadorListaControle exportadorListaControle = (ExportadorListaControle) JSFUtilities.getFromRequest(ExportadorListaControle.class.getSimpleName());
            String paramsTableFiltrada = context().getExternalContext().getRequestParameterMap().get("paramsTableFiltrada");
            String extensao = (String) JSFUtilities.getFromActionEvent("tipo", evt);

            extensao = extensao.equals("xls") ? "vnd.ms-excel" : "pdf";

            String[] split = paramsTableFiltrada.split(",");
            String campoOrdenacao = split[0].replace("[", "");
            String ordem = split[1];
            String filtro = split[2].replace("''", "");
            filtro = filtro.replace("]", "");
            List listaParaImpressao = getFacade().getMovimentoContaCorrenteCliente().consultarParaImpressao(filtro, ordem, campoOrdenacao, getEmpresaLogado() == null ? 0 : getEmpresaLogado().getCodigo(), getMovimentoContaCorrenteClienteVO() == null ? "0" : getMovimentoContaCorrenteClienteVO().getPessoa().getCodigo().toString());
            if (listaParaImpressao.size() > 0) {
                exportadorListaControle.exportar(evt, listaParaImpressao, filtro, ItemExportacaoEnum.MOV_CC_CLIENTE);
                if(exportadorListaControle.getErro()){
                    throw new Exception(exportadorListaControle.getMensagemDetalhada());
                }
                oncomplete = "abrirPopup('UpdateServlet?op=downloadfile&file=" + exportadorListaControle.getFileName() + "&mimetype=application/" + extensao + "','Transacoes', 800,200);" + exportadorListaControle.getMsgAlert();
            } else {
                throw new Exception("Nenhum resultado para exibir.");
            }
        } catch (Exception e){
            montarErro(e);
        }
    }

    public String getOncomplete() {
        return oncomplete;
    }

    public void setOncomplete(String oncomplete) {
        this.oncomplete = oncomplete;
    }

    public void setMsgAlert(String msgAlert) {
        this.msgAlert = msgAlert;
    }

    public String getMsgAlert() {
        if (msgAlert == null) {
            return "";
        }
        return msgAlert;
    }


    public void confirmarExcluir() {
        MensagemGenericaControle control = (MensagemGenericaControle) getControlador(MensagemGenericaControle.class);
        control.setMensagemDetalhada("", "");
        setMsgAlert("Richfaces.showModalPanel('mdlMensagemGenerica');");
        control.init("Exclusão de Movimentação Conta Corrente Cliente",
                "Deseja excluir a Movimentação Conta Corrente Cliente?",
                this, "excluir", "", "", "", "grupoBtnExcluir");
    }

    public boolean isPossuiPermissaoEditarSaldoContaCorrente() {
        return possuiPermissaoEditarSaldoContaCorrente;
    }

    public void setPossuiPermissaoEditarSaldoContaCorrente(boolean possuiPermissaoEditarSaldoContaCorrente) {
        this.possuiPermissaoEditarSaldoContaCorrente = possuiPermissaoEditarSaldoContaCorrente;
    }
}

package controle.basico;

import br.com.pactosolucoes.ce.comuns.ex.ValidacaoException;
import br.com.pactosolucoes.comuns.json.EstatisticaSolicitacaoJSON;
import br.com.pactosolucoes.comuns.notificacao.RecursoSistema;
import br.com.pactosolucoes.comuns.util.JSFUtilities;
import cfin.wrapper.ParcelaConsultada;
import cfin.wrapper.TResultadoBoleto;
import cfin.wrapper.TResultadoParcelaConsultada;
import controle.arquitetura.SuperControle;
import controle.arquitetura.security.LogoutControle;
import negocio.comuns.basico.ConfiguracaoSistemaVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.comuns.utilitarias.UtilReflection;
import negocio.facade.jdbc.financeiro.FinanceiroPacto;
import negocio.intranet.SolicitacaoJSON;
import org.json.JSONArray;
import org.json.JSONObject;
import servicos.propriedades.PropsService;
import ws.TParcelaConsultada;
import ws.TResultadoTransacaoWS;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Comparator;
import java.util.List;

/**
 * Created by ulisses on 03/07/2017.
 */
public class CanalPactoControle extends SuperControle {

    public static final Integer LIMIT_CONSULTA_SOLICITACOES_CONCLUIDAS = 30;
    public static final Integer LIMIT_SOLICITACOES_ESTATISTICA = 30;
    private List<SolicitacaoJSON> listaSolicitacoesEmAberto = new ArrayList<>();
    private List<SolicitacaoJSON> listaSolicitacoesConcluidas = new ArrayList<>();
    private EstatisticaSolicitacaoJSON estatisticaSolicitacaoJSON;
    private boolean telaAtualAtendimentoPacto = false;
    private String dataSolEmAberto;
    private String dataSolConcluida;
    private transient FinanceiroPacto financeiroPacto;
    private transient TResultadoParcelaConsultada parcelasAcademia;
    private ParcelaConsultada parcelaSelecionada;
    private String urlIncludeCanalCliente;
    private ConfiguracaoSistemaVO configuracaoSistema;
    private Integer codigoEmpresaLogado;
    private Integer codigoUsuarioLogado;
    private Integer codigoFinanceiro;
    private String pagDirecionar = "";

    public CanalPactoControle() throws Exception {
        iniciarConfiguracaoSistema();
    }

    private void iniciarConfiguracaoSistema() throws Exception {
        this.configuracaoSistema = getFacade().getConfiguracaoSistema().consultarConfigs(Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        this.codigoEmpresaLogado = getEmpresaLogado().getCodigo();
        this.codigoUsuarioLogado = getUsuarioLogado().getCodigo();
        this.codigoFinanceiro = getEmpresaLogado().getCodEmpresaFinanceiro();
    }

    public String abrirTelaAtendimentoPacto() {
        try {
            this.telaAtualAtendimentoPacto = true;
            if (this.estatisticaSolicitacaoJSON == null) {
                this.estatisticaSolicitacaoJSON = getFacade().getDashboardService().consultarEstatisticaSolicitacao(getEmpresaLogado().getEmail(), LIMIT_SOLICITACOES_ESTATISTICA);
            }
            gerarLogUtilizacaoTela("atendimentoPacto.jsp");
        } catch (Exception e) {
            this.estatisticaSolicitacaoJSON = null;
            montarErro(e);
        }
        return "atendimentoPacto";
    }

    public String abrirTelaMinhaConta() {
        notificarRecursoEmpresa(RecursoSistema.CANAL_PACTO);
        return abrirTelaMinhaConta("");
    }

    public String abrirTelaMinhaContaPactoStore() {
        notificarRecursoEmpresa(RecursoSistema.CANAL_CLIENTE_PACTO_STORE);
        return abrirTelaMinhaConta("store");
    }

    public String abrirTelaMinhaContaFaturas() {
        notificarRecursoEmpresa(RecursoSistema.CANAL_CLIENTE_FATURAS);
        return abrirTelaMinhaConta("faturas");
    }


    public String abrirTelaMinhaConta(String redirect) {
        try {
            if (redirect.equals("store")){
                setPagDirecionar("/canal-cliente/zw/store");
            } else  if (redirect.equals("faturas")){
                setPagDirecionar("/canal-cliente/zw/faturas");
            } else {
                setPagDirecionar(null);
            }
            this.telaAtualAtendimentoPacto = false;
            consultarTodosDadosTelaMinhaConta();
            gerarLogUtilizacaoTela("minhaContaPacto.jsp");
        } catch (Exception e) {
            this.estatisticaSolicitacaoJSON = null;
            montarErro(e);
        }
        return "minhaContaPacto";
    }

    public void abrirTelaMinhaContaViaPopUp() {
        try {
            notificarRecursoEmpresa(RecursoSistema.CANAL_PACTO);
            this.telaAtualAtendimentoPacto = false;
            consultarTodosDadosTelaMinhaConta();
            gerarLogUtilizacaoTela("minhaContaPacto.jsp");
        } catch (Exception e) {
            this.estatisticaSolicitacaoJSON = null;
            montarErro(e);
        }
    }

    private void consultarTodosDadosTelaMinhaConta() throws Exception {
        if (this.estatisticaSolicitacaoJSON == null) {
            this.estatisticaSolicitacaoJSON = getFacade().getDashboardService().consultarEstatisticaSolicitacao(getEmpresaLogado().getEmail(), LIMIT_SOLICITACOES_ESTATISTICA);
        }
    }

    public String abrirTelaBoletoAntigo() {
        try {
            gerarLogUtilizacaoTela("financeiroPacto.jsp");
        } catch (Exception e) {
            montarErro(e);
            return null;
        }
        return "financeiroPacto";
    }

    public void consultarParcelasAcademia() {
        if (this.parcelasAcademia == null) {
            try {
                if (this.financeiroPacto == null) {
                    JSFUtilities.setManagedBeanValue(LogoutControle.class.getSimpleName() + ".deveEfetuarLogout", false);
                    this.financeiroPacto = new FinanceiroPacto();
                }
                String chave = (String) JSFUtilities.getFromSession("key");
                if (!UtilReflection.objetoMaiorQueZero(getEmpresaLogado(), "getCodigo()")) {
                    throw new Exception("Para utilizar esta operação é necessário está logado em uma empresa.");
                }
                this.parcelasAcademia = getFinanceiroPacto().obterParcelasAcademia(chave, getEmpresaLogado().getCodigo(), 4);

                /* Ordena as parcelas por data de vencimento crescente */
                parcelasAcademia.getParcelas().sort(Comparator.comparing(TParcelaConsultada::getDataVencimento));

                consultarNfe();
            } catch (Exception ex) {
                Uteis.logar(ex, CanalPactoControle.class);
            }
        }
    }

    private void consultarNfe() throws Exception {
        List<Integer> idsReferencia = new ArrayList<>();
        for (ParcelaConsultada parcelaConsultada : this.parcelasAcademia.getParcelas()) {
            idsReferencia.add(parcelaConsultada.getCodigoParcela());
        }

        String obj = getFacade().getLoteNFSe().pegarLinkNFSeDoIdExterno(idsReferencia);
        JSONObject retorno = new JSONObject(obj);
        JSONArray arrayRpsReferencia = retorno.optJSONArray("listaArrayRpsReferencia");
        for (int i = 0; i < arrayRpsReferencia.length(); i++) {
            JSONObject objReferencia = arrayRpsReferencia.getJSONObject(i);
            String idRef = objReferencia.optString("IdReferencia");
            if (!UteisValidacao.emptyString(idRef)) {
                for (ParcelaConsultada parcelaConsultada : this.parcelasAcademia.getParcelas()) {
                    int codParcela = parcelaConsultada.getCodigoParcela();
                    if (idRef.contains(Integer.toString(codParcela))) {
                        Integer idLote = objReferencia.getInt("IdRps");
                        String urlNFe = Uteis.getUrlModuloNFSe() + "/nota?rps=" + idLote;
                        parcelaConsultada.setUrlBoleto("location.href='" + urlNFe + "'");
                    }
                }
            }
        }
    }

    public boolean isPermiteGerarBoleto(ParcelaConsultada obj) {
        if (Calendario.maiorOuIgual(obj.getDataVencimento().getTime(), Calendario.hoje())) {
            return false;
        }

        Calendar calendar = Calendario.getInstance();
        int horaDoDia = calendar.get(Calendar.HOUR_OF_DAY);

        return horaDoDia <= 17;
    }


    public void downloadBoleto() {
        try {
            notificarRecursoEmpresa(RecursoSistema.CANAL_PACTO_DOWNLOAD_BOLETO);
            montarSucesso("");
            setMsgAlert("");

            TResultadoBoleto resultadoBoleto = getFinanceiroPacto().regerarParcela(getParcelaSelecionada().getCodigoParcela(), isPermiteGerarBoleto(getParcelaSelecionada()));

            String nomeArquivo;
            if (resultadoBoleto.getResultado().equals(TResultadoTransacaoWS.rtOK)) {
                if (resultadoBoleto.getNomeArquivo().contains("\\")) {
                    nomeArquivo = resultadoBoleto.getNomeArquivo().split("boleto\\\\")[1];
                } else {
                    nomeArquivo = resultadoBoleto.getNomeArquivo();
                }
                if (!nomeArquivo.equals("")) {
                    String urlArquivo = Uteis.getUrlBoletosFinanceiroPacto() + nomeArquivo;
                    setMsgAlert("abrirPopup('" + urlArquivo + "', 'Boleto', 1000, 670);");
                    setSucesso(true);
                    montarSucesso("");
                    if (!isPermiteGerarBoleto(getParcelaSelecionada())) {
                        throw new ValidacaoException("O boleto impresso não foi gerado agora!");
                    }
                }
            } else {
                throw new ConsistirException("Arquivo não encontrado");
            }

        } catch (ValidacaoException ex) {
            montarInfo("Informação sobre boleto", ex.getMessage());
        } catch (Exception ex) {
            montarErro(ex);
        }
    }

    public String abrirTelaSolicitacoesEmAberto() {
        consultarSolicitacoesEmAberto();
        gerarLogUtilizacaoTela("solicitacoesEmAberto.jsp");
        return "solicitacoesEmAberto";
    }

    public String abrirTelaMinhaContaSolicitacoesEmAberto() {
        consultarSolicitacoesEmAberto();
        gerarLogUtilizacaoTela("minhaContaPactoSolicitacoesEmAberto.jsp");
        return "minhaContaPactoSolicitacoesEmAberto";
    }

    public String abrirTelaMinhaContaSolicitacoesConcluidas() {
        consultarSolicitacoesConcluidas();
        gerarLogUtilizacaoTela("minhaContaPactoSolicitacoesConcluida.jsp");
        return "minhaContaPactoSolicitacoesConcluidas";
    }

    public String abrirTelaSolConcluidas() {
        if (this.telaAtualAtendimentoPacto) {
            return abrirTelaSolicitacoesConcluidas();
        } else {
            return abrirTelaMinhaContaSolicitacoesConcluidas();
        }
    }

    public String abrirTelaSolEmAberta() {
        if (this.telaAtualAtendimentoPacto) {
            return abrirTelaSolicitacoesEmAberto();
        } else {
            return abrirTelaMinhaContaSolicitacoesEmAberto();
        }
    }


    public String abrirTelaSolicitacoesConcluidas() {
        consultarSolicitacoesConcluidas();
        gerarLogUtilizacaoTela("solicitacoesConcluidas.jsp");
        return "solicitacoesConcluidas";
    }

    public void consultarSolicitacoesEmAberto() {
        try {
            if ((this.listaSolicitacoesEmAberto == null) || (this.listaSolicitacoesEmAberto.size() <= 0)) {
                this.listaSolicitacoesEmAberto = getFacade().getDashboardService().buscarSolicitacoesEmAbertoServiceBMP(getEmpresaLogado().getEmail());
                this.dataSolEmAberto = Calendario.getData(Calendario.hoje(), "dd/MM/yyyy HH:mm:ss");
            }
            montarSucesso("");
        } catch (Exception e) {
            montarErro(e);
        }
    }

    public void atualizarConsultaSolicitacoesEmAberto() {
        try {
            this.listaSolicitacoesEmAberto = getFacade().getDashboardService().buscarSolicitacoesEmAbertoServiceBMP(getEmpresaLogado().getEmail());
            this.dataSolEmAberto = Calendario.getData(Calendario.hoje(), "dd/MM/yyyy HH:mm:ss");
            this.estatisticaSolicitacaoJSON = getFacade().getDashboardService().consultarEstatisticaSolicitacao(getEmpresaLogado().getEmail(), LIMIT_SOLICITACOES_ESTATISTICA);
        } catch (Exception e) {
            montarErro(e);
        }
    }

    public void atualizarConsultaSolicitacoesConcluida() {
        try {
            this.listaSolicitacoesConcluidas = getFacade().getDashboardService().buscarSolicitacoesConcluidasBMP(getEmpresaLogado().getEmail(), LIMIT_CONSULTA_SOLICITACOES_CONCLUIDAS);
            this.dataSolConcluida = Calendario.getData(Calendario.hoje(), "dd/MM/yyyy HH:mm:ss");
            this.estatisticaSolicitacaoJSON = getFacade().getDashboardService().consultarEstatisticaSolicitacao(getEmpresaLogado().getEmail(), LIMIT_SOLICITACOES_ESTATISTICA);
        } catch (Exception e) {
            montarErro(e);
        }
    }


    public void consultarSolicitacoesConcluidas() {
        try {
            if ((this.listaSolicitacoesConcluidas == null) || (this.listaSolicitacoesConcluidas.size() <= 0)) {
                this.listaSolicitacoesConcluidas = getFacade().getDashboardService().buscarSolicitacoesConcluidasBMP(getEmpresaLogado().getEmail(), LIMIT_CONSULTA_SOLICITACOES_CONCLUIDAS);
                this.dataSolConcluida = Calendario.getData(Calendario.hoje(), "dd/MM/yyyy HH:mm:ss");
            }
            montarSucesso("");
        } catch (Exception e) {
            montarErro(e);
        }
    }

    public void expandirTudoSolEmAberto() {
        try {
            getFacade().getDashboardService().buscarAndamentosDasSolicitacoes(this.listaSolicitacoesEmAberto);
            for (SolicitacaoJSON solicitacaoJSON : this.listaSolicitacoesEmAberto) {
                solicitacaoJSON.setExibirAndamentos(true);
            }
        } catch (Exception e) {
            montarErro(e);
        }
    }

    public void retrairTudoSolEmAberto() {
        try {
            for (SolicitacaoJSON solicitacaoJSON : this.listaSolicitacoesEmAberto) {
                solicitacaoJSON.setExibirAndamentos(false);
            }
        } catch (Exception e) {
            montarErro(e);
        }
    }

    public void expandirTudoSolConcluida() {
        try {
            getFacade().getDashboardService().buscarAndamentosDasSolicitacoes(this.listaSolicitacoesConcluidas);
            for (SolicitacaoJSON solicitacaoJSON : this.listaSolicitacoesConcluidas) {
                solicitacaoJSON.setExibirAndamentos(true);
            }
        } catch (Exception e) {
            montarErro(e);
        }
    }

    public void retrairTudoSolConcluida() {
        try {
            for (SolicitacaoJSON solicitacaoJSON : this.listaSolicitacoesConcluidas) {
                solicitacaoJSON.setExibirAndamentos(false);
            }
        } catch (Exception e) {
            montarErro(e);
        }
    }


    public void exibirAndamentosDaAtividade() {
        try {
            SolicitacaoJSON obj = (SolicitacaoJSON) context().getExternalContext().getRequestMap().get("item");
            obj.setExibirAndamentos(!obj.isExibirAndamentos());
            if (obj.isExibirAndamentos()) {
                getFacade().getDashboardService().buscarAndamentosDaSolicitacao(obj);
            }
        } catch (Exception e) {
            montarErro(e);
        }
    }


    public List<SolicitacaoJSON> getListaSolicitacoesEmAberto() {
        return listaSolicitacoesEmAberto;
    }

    public void setListaSolicitacoesEmAberto(List<SolicitacaoJSON> listaSolicitacoesEmAberto) {
        this.listaSolicitacoesEmAberto = listaSolicitacoesEmAberto;
    }

    public List<SolicitacaoJSON> getListaSolicitacoesConcluidas() {
        return listaSolicitacoesConcluidas;
    }

    public void setListaSolicitacoesConcluidas(List<SolicitacaoJSON> listaSolicitacoesConcluidas) {
        this.listaSolicitacoesConcluidas = listaSolicitacoesConcluidas;
    }

    public Integer getLimitConsultaSolicitacoesConcluidas() {
        return LIMIT_CONSULTA_SOLICITACOES_CONCLUIDAS;
    }

    public EstatisticaSolicitacaoJSON getEstatisticaSolicitacaoJSON() {
        return estatisticaSolicitacaoJSON;
    }

    public void setEstatisticaSolicitacaoJSON(EstatisticaSolicitacaoJSON estatisticaSolicitacaoJSON) {
        this.estatisticaSolicitacaoJSON = estatisticaSolicitacaoJSON;
    }

    public boolean isTelaAtualAtendimentoPacto() {
        return telaAtualAtendimentoPacto;
    }

    public void setTelaAtualAtendimentoPacto(boolean telaAtualAtendimentoPacto) {
        this.telaAtualAtendimentoPacto = telaAtualAtendimentoPacto;
    }

    public Integer getLimitSolicitacoesEstatistica() {
        return LIMIT_SOLICITACOES_ESTATISTICA;
    }

    public String getDataSolEmAberto() {
        return dataSolEmAberto;
    }

    public void setDataSolEmAberto(String dataSolEmAberto) {
        this.dataSolEmAberto = dataSolEmAberto;
    }

    public String getDataSolConcluida() {
        return dataSolConcluida;
    }

    public void setDataSolConcluida(String dataSolConcluida) {
        this.dataSolConcluida = dataSolConcluida;
    }

    public FinanceiroPacto getFinanceiroPacto() {
        return financeiroPacto;
    }

    public void setFinanceiroPacto(FinanceiroPacto financeiroPacto) {
        this.financeiroPacto = financeiroPacto;
    }

    public TResultadoParcelaConsultada getParcelasAcademia() {
        return parcelasAcademia;
    }

    public void setParcelasAcademia(TResultadoParcelaConsultada parcelasAcademia) {
        this.parcelasAcademia = parcelasAcademia;
    }

    public ParcelaConsultada getParcelaSelecionada() {
        return parcelaSelecionada;
    }

    public void setParcelaSelecionada(ParcelaConsultada parcelaSelecionada) {
        this.parcelaSelecionada = parcelaSelecionada;
    }

    public String getUrlIncludeCanalCliente() {
        if (UteisValidacao.emptyString(this.urlIncludeCanalCliente)) {
            String urlIncludeFrontEnd = isHttps() ?
                    PropsService.getPropertyValue(PropsService.URL_HTTPS_PLATAFORMA_PACTO)
                    : PropsService.getPropertyValue(PropsService.URL_HTTP_PLATAFORMA_PACTO);

            String urlOamd = isHttps() ? Uteis.getUrlOamdSegura() : Uteis.getUrlOamd();
            String chave = getKey();
            String urlZw = getUrl();

            urlIncludeFrontEnd += "/adicionarConta?" +
                    "oamdUrl=" + urlOamd +
                    "&zwUrl=" + urlZw +
                    "&chave=" + chave +
                    "&empresaId=" + this.getCodigoEmpresaLogado() +
                    "&usuarioZwId=" + this.getCodigoUsuarioLogado() +
                    "&codigoFinanceiro=" + this.getCodigoFinanceiro() +
                    (UteisValidacao.emptyString(pagDirecionar) ? "" : ("&paginaDirecionar=" + pagDirecionar));

            this.urlIncludeCanalCliente = urlIncludeFrontEnd;
        }
        return urlIncludeCanalCliente;
    }

    public void setUrlIncludeCanalCliente(String urlIncludeCanalCliente) {
        this.urlIncludeCanalCliente = urlIncludeCanalCliente;
    }

    public ConfiguracaoSistemaVO getConfiguracaoSistema() throws Exception {
        if (configuracaoSistema == null) {
            iniciarConfiguracaoSistema();
        }
        return configuracaoSistema;
    }

    public void setConfiguracaoSistema(ConfiguracaoSistemaVO configuracaoSistema) {
        this.configuracaoSistema = configuracaoSistema;
    }

    public Integer getCodigoEmpresaLogado() {
        return codigoEmpresaLogado;
    }

    public void setCodigoEmpresaLogado(Integer codigoEmpresaLogado) {
        this.codigoEmpresaLogado = codigoEmpresaLogado;
    }

    public Integer getCodigoUsuarioLogado() {
        return codigoUsuarioLogado;
    }

    public void setCodigoUsuarioLogado(Integer codigoUsuarioLogado) {
        this.codigoUsuarioLogado = codigoUsuarioLogado;
    }

    public Integer getCodigoFinanceiro() {
        return codigoFinanceiro;
    }

    public void setCodigoFinanceiro(Integer codigoFinanceiro) {
        this.codigoFinanceiro = codigoFinanceiro;
    }

    public String getPagDirecionar() {
        return pagDirecionar;
    }

    public void setPagDirecionar(String pagDirecionar) {
        this.pagDirecionar = pagDirecionar;
    }
}

package controle.basico;

import br.com.pactosolucoes.ce.comuns.enumerador.TipoTelefone;
import br.com.pactosolucoes.ce.comuns.ex.ValidacaoException;
import br.com.pactosolucoes.comuns.notificacao.RecursoSistema;
import br.com.pactosolucoes.comuns.util.Formatador;
import br.com.pactosolucoes.comuns.util.JSFUtilities;
import controle.arquitetura.SuperControle;
import negocio.comuns.arquitetura.UsuarioPerfilAcessoVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ConfiguracaoSistemaVO;
import negocio.comuns.basico.EmailVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.basico.TelefoneVO;
import negocio.comuns.basico.enumerador.CargoEnum;
import negocio.comuns.basico.enumerador.ClassificacaoNegocioEnum;
import negocio.comuns.basico.enumerador.FuncaoEnum;
import negocio.comuns.feed.PerfilUsuarioEnum;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;

import javax.faces.model.SelectItem;
import java.util.ArrayList;
import java.util.List;


public class AtualizarDadosControle extends SuperControle {

    private PessoaVO pessoaVO;
    private UsuarioVO usuarioVO;
    private EmpresaVO empresaVO;
    private ConfiguracaoSistemaVO configuracaoSistemaVO;
    private boolean modalDadosPessoa = false;
    private boolean modalTermosPoliticas = false;
    private boolean modalDadosEmpresa = false;
    private boolean mostrarModalAtualizaDados = false;
    private boolean atualizarDadosEmpresa = false;
    private boolean atualizacaoCadastralAdiada = false;
    private TelefoneVO telefoneVO;
    private EmailVO emailVO;
    private String onComplete;
    private String pin;

    public AtualizarDadosControle() {
        limparMsg();
        setOnComplete("");
        setEmpresaVO(new EmpresaVO());
        setUsuarioVO(new UsuarioVO());
        setPessoaVO(new PessoaVO());
        mostrarModalAtualizaDados = false;
        inicializar();
    }

    private void inicializar() {
        try {
            limparMsg();
            setOnComplete("");
            setModalDadosPessoa(true);
            setModalTermosPoliticas(false);

            if (!SuperControle.isAtualizacaoCadastral()) {
                mostrarModalAtualizaDados = false;
                return;
            }

            setEmpresaVO(getEmpresaLogado());
            if (getEmpresaVO() == null || UteisValidacao.emptyNumber(getEmpresaVO().getCodigo())) {
                return;
            }

            setConfiguracaoSistemaVO((ConfiguracaoSistemaVO) JSFUtilities.getFromSession(JSFUtilities.CONFIGURACAO_SISTEMA));

            setUsuarioVO(getUsuarioLogado());
            if (getUsuarioVO() == null
                    || getUsuarioVO().getColaboradorVO() == null
                    || getUsuarioVO().getColaboradorVO().getPessoa() == null
                    || UteisValidacao.emptyNumber(getUsuarioVO().getColaboradorVO().getPessoa().getCodigo())) {

                if (getUsuarioVO() == null) {
                    Uteis.logarDebug(String.format("Erro na chave %s. Usuário está NULL.",
                            JSFUtilities.getFromSession(JSFUtilities.KEY)));
                } else {
                    Uteis.logarDebug(String.format("Erro na chave %s com a pessoa %d e Usuário %d",
                            JSFUtilities.getFromSession(JSFUtilities.KEY),
                            getPessoaVO().getCodigo(),
                            getUsuarioVO().getCodigo()));
                }
                return;
            }

            setPessoaVO(getFacade().getPessoa().consultarPorChavePrimaria(getUsuarioVO().getColaboradorVO().getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));

            consultarPerfilAdm();

            mostrarModalAtualizaDados = (!getUsuarioVO().getUsuarioPactoSolucoes() && (isAtualizarDadosEmpresa() || getPessoaVO().isAtualizarDados()) && !atualizacaoCadastralAdiada);
            if (!getPessoaVO().isAtualizarDados()) {
                setModalDadosPessoa(false);
                setModalTermosPoliticas(true);
                getPessoaVO().setAceiteTermosPacto(false);
            }

            if (!UteisValidacao.emptyNumber(getPessoaVO().getCodigo())) {
                String nomeCompleto = getPessoaVO().getNome();
                getPessoaVO().setNome(Uteis.getPrimeiroNome(nomeCompleto));
                getPessoaVO().setSobreNome(Uteis.getSobrenome(nomeCompleto));
            }

            setTelefoneVO(new TelefoneVO());
            if (!UteisValidacao.emptyNumber(getPessoaVO().getCodigo()) && !UteisValidacao.emptyList(getPessoaVO().getTelefoneVOs())) {
                Ordenacao.ordenarListaReverse(getPessoaVO().getTelefoneVOs(), "codigo");
                for (TelefoneVO telefone : getPessoaVO().getTelefoneVOs()) {
                    if (telefone.getTipoTelefone().equals(TipoTelefone.CELULAR.getCodigo())) {
                        setTelefoneVO(telefone);
                        break;
                    }
                }
            }

            setEmailVO(new EmailVO());
            if (!UteisValidacao.emptyNumber(getPessoaVO().getCodigo()) && !UteisValidacao.emptyList(getPessoaVO().getEmailVOs())) {
                Ordenacao.ordenarListaReverse(getPessoaVO().getEmailVOs(), "codigo");
                for (EmailVO email : getPessoaVO().getEmailVOs()) {
                    setEmailVO(email);
                    break;
                }
            }

        } catch (Exception e) {
            mostrarModalAtualizaDados = false;
            Uteis.logarDebug(String.format("Erro na chave %s com a pessoa %d e Usuário %d",
                    JSFUtilities.getFromSession(JSFUtilities.KEY),
                    getPessoaVO().getCodigo(),
                    getUsuarioVO().getCodigo()));
            e.printStackTrace();
        }
    }

    public void avancarModal() {
        try {
            mudarPaginaModal();
            if (isModalDadosPessoa()) {
                setModalDadosPessoa(false);
                setModalDadosEmpresa(isAtualizarDadosEmpresa());
                setModalTermosPoliticas(!isAtualizarDadosEmpresa());
            } else if (isModalDadosEmpresa()) {
                validarDadosEmpresaAoNavegar();
                setModalDadosPessoa(false);
                setModalDadosEmpresa(false);
                setModalTermosPoliticas(true);
            }
        } catch (ValidacaoException ex) {
            tratarValidacaoException(ex);
        } catch (Exception ex) {
            montarErro(ex);
            setOnComplete(getMensagemNotificar());
        }
    }

    public void voltarModal() {
        if (isModalTermosPoliticas()) {
            setModalDadosPessoa(!isAtualizarDadosEmpresa());
            setModalDadosEmpresa(isAtualizarDadosEmpresa());
            setModalTermosPoliticas(false);
        } else if (isModalDadosEmpresa()) {
            setModalDadosPessoa(true);
            setModalDadosEmpresa(false);
            setModalTermosPoliticas(false);
        }

        try {
            mudarPaginaModal();
        } catch (ValidacaoException ex) {
            tratarValidacaoException(ex);
        } catch (Exception ex) {
            montarErro(ex);
        }
    }

    public void mudarPaginaModal() throws Exception {
        limparMsg();
        setOnComplete("");
        validarDadosColaborador();
        getPessoaVO().setAceiteTermosPacto(false);
    }

    private void consultarPerfilAdm() throws Exception {
        setAtualizarDadosEmpresa(false);
        if (getEmpresaVO().isAtualizarDadosCadastro()) {
            List<UsuarioPerfilAcessoVO> usuarioPerfilAcessoVOS = getFacade().getUsuarioPerfilAcesso().consultarUsuarioPerfilAcesso(getUsuarioVO().getCodigo(), Uteis.NIVELMONTARDADOS_MINIMOS);
            for (UsuarioPerfilAcessoVO usuarioPerfilAcessoVO : usuarioPerfilAcessoVOS) {
                if (usuarioPerfilAcessoVO.getEmpresa().getCodigo().equals(getEmpresaVO().getCodigo()) &&
                        usuarioPerfilAcessoVO.getPerfilAcesso().getTipo() == PerfilUsuarioEnum.ADMINISTRADOR && getPessoaVO().isAtualizarDados()) {
                    setAtualizarDadosEmpresa(true);
                    break;
                }
            }
        }
    }

    public void adiarAtualizacaoCadastral() {
        setAtualizacaoCadastralAdiada(true);
        setMostrarModalAtualizaDados(false);
        try {
            getFacade().getCliente().incluirAtualizacaoCadastral(getUsuarioLogado().getCodigo(), false);
            notificarRecursoEmpresa(RecursoSistema.ADIOU_ATUALIZACAO_CADASTRAL);
            montarErro("");
            setOnComplete("");
        } catch (Exception ignored) {
        }
    }

    private void validarDadosEmpresaAoNavegar() throws Exception {
        if (isAtualizarDadosEmpresa()) {

            boolean informou = false;
            if (getEmpresaVO().getClassificacaoNegocioEnum() != null && !UteisValidacao.emptyString(getEmpresaVO().getClassificacaoNegocioEnum().getDescricao()) &&
                    !UteisValidacao.emptyString(getEmpresaVO().getGestor()) &&
                    !UteisValidacao.emptyString(getEmpresaVO().getResponsavelFinanceiro())) {
                informou = true;
            }

            if (!informou) {
                validarDadosEmpresa();
            }

            //atualizar dados no oamd
            if (getEmpresaVO().getClassificacaoNegocioEnum() != null &&
                    !UteisValidacao.emptyString(getEmpresaVO().getClassificacaoNegocioEnum().getDescricao()) &&
                    !UteisValidacao.emptyString(getEmpresaVO().getGestor()) &&
                    !UteisValidacao.emptyString(getEmpresaVO().getResponsavelFinanceiro())) {

                boolean atualizouOAMD = getFacade().getEmpresa().atualizarEmpresaFinanceiroOAMD(getKey(),
                        getEmpresaVO().getCodigo(),
                        getEmpresaVO().getClassificacaoNegocioEnum().getDescricao(),
                        getEmpresaVO().getGestor(),
                        getEmpresaVO().getResponsavelFinanceiro());
                if (atualizouOAMD) {
                    getEmpresaVO().setAtualizarDadosCadastro(false);
                    getFacade().getEmpresa().alterarSomenteAtualizarDadosCadastroSemCommit(getEmpresaVO());
                    notificarRecursoEmpresa(RecursoSistema.ATUALIZACAO_CADASTRO_EMPRESA);
                }
            }
        }
    }

    public void salvarAtualizacaoCadastral() {
        try {
            limparMsg();
            setOnComplete("");
            validarDadosEmpresaAoNavegar();
            validarPinETermos();

            if (!UteisValidacao.emptyString(this.getTelefoneVO().getNumero())) {
                getTelefoneVO().setUsarSistemaInternacional(getConfiguracaoSistemaVO().isUsarSistemaInternacional());
                getTelefoneVO().setTipoTelefone(TipoTelefone.CELULAR.getCodigo());
                getTelefoneVO().setNumero(Formatador.formataTelefone(getTelefoneVO().getNumero()));
                getPessoaVO().adicionarObjTelefoneVOs(getTelefoneVO());
            }

            if (!UteisValidacao.emptyString(this.getEmailVO().getEmail())) {
                getPessoaVO().adicionarObjEmailVOs(getEmailVO());
            }

            getPessoaVO().setEmpresaInternacional(getConfiguracaoSistemaVO().isUsarSistemaInternacional());

            boolean atualizarDados = getPessoaVO().isAtualizarDados();
            getPessoaVO().setAtualizarDados(false);
            getPessoaVO().setIpAceiteTermosPacto(getIpCliente());
            getPessoaVO().setInfoBrowserTermosPacto((String) JSFUtilities.getFromSession("browser"));
            getPessoaVO().setDataAceiteTermosPacto(Calendario.hoje());

            getFacade().getPessoa().gravarModalAtualizacaoCadastral(getPessoaVO(), getUsuarioVO());

            getUsuarioLogado().setPin(getPin());

            if (!getUsuarioLogado().getUsuarioPactoSolucoes()) {
                getFacade().getUsuario().atualizarPin(getUsuarioLogado().getCodigo(), getPin());
            }

            try {
                getFacade().getCliente().incluirAtualizacaoCadastral(getUsuarioLogado().getCodigo(), true);
            } catch (Exception e) {
            }

            if (atualizarDados) {
                notificarRecursoEmpresa(RecursoSistema.ATUALIZACAO_CADASTRO_USUARIO);
            }
            mostrarModalAtualizaDados = false;
            montarSucessoGrowl("Atualização Cadastral realizada com sucesso. Obrigado.");
            setOnComplete(getMensagemNotificar());
        } catch (ValidacaoException ex) {
            tratarCamposPreenchidosCorretamente(ex);
            tratarValidacaoException(ex);
        } catch (Exception ex) {
            montarErro(ex);
            setOnComplete(getMensagemNotificar());
        }

    }

    public void tratarCamposPreenchidosCorretamente(Exception ex) {
        // Significa que não preencheu o pin corretamente, então limpa o pin
        if (ex.getMessage().contains("O PIN")) {
            setPin("");
            getPessoaVO().setAceiteTermosPacto(getPessoaVO().isAceiteTermosPacto());
        }
        // Significa que a senha está fora do padrão, limpa todos os campos de senha
        else if (ex.getMessage().contains("requisitos")) {
            setPin(getPin());
            getPessoaVO().setAceiteTermosPacto(getPessoaVO().isAceiteTermosPacto());
        }
        // Significa que as senhas não estão iguais, então limpa somente a confirmação de senha
        else if (ex.getMessage().contains("A nova senha deve ser igual")) {
            setPin(getPin());
            getPessoaVO().setAceiteTermosPacto(getPessoaVO().isAceiteTermosPacto());
        }
        // Significa que não aceitou os termos, mantém todos os campos
        else if (ex.getMessage().contains("Você deve ler e aceitar os termos")) {
            setPin(getPin());
            getPessoaVO().setAceiteTermosPacto(false);
        }
    }

    public List<SelectItem> getListaCargos() {
        List<SelectItem> listaCarregadaCargos = new ArrayList<>();
        for (CargoEnum value : CargoEnum.values()) {
            listaCarregadaCargos.add(new SelectItem(value, value.getDescricao()));
        }
        Ordenacao.ordenarLista(listaCarregadaCargos, "label");
        listaCarregadaCargos.add(0, new SelectItem(null, "Selecione um cargo"));
        return listaCarregadaCargos;
    }

    public List<SelectItem> getListaFuncao() {
        List<SelectItem> listaCarregadaFuncoes = new ArrayList<>();
        for (FuncaoEnum value : FuncaoEnum.values()) {
            listaCarregadaFuncoes.add(new SelectItem(value, value.getDescricao()));
        }
        Ordenacao.ordenarLista(listaCarregadaFuncoes, "label");
        listaCarregadaFuncoes.add(0, new SelectItem(null, "Selecione uma função"));
        return listaCarregadaFuncoes;
    }

    public List<SelectItem> getListaClassificacaoNegocio() {
        List<SelectItem> listaCarregadaClassificacao = new ArrayList<>();
        for (ClassificacaoNegocioEnum value : ClassificacaoNegocioEnum.values()) {
            listaCarregadaClassificacao.add(new SelectItem(value, value.getDescricao()));
        }
        Ordenacao.ordenarLista(listaCarregadaClassificacao, "label");
        listaCarregadaClassificacao.add(0, new SelectItem(null, "Selecione uma classificação"));
        return listaCarregadaClassificacao;
    }

    public List<UsuarioVO> executarAutocompleteUsuarios(Object suggest) {
        String pref = (String) suggest;
        ArrayList<UsuarioVO> result;
        try {
            if (pref.equals("%")) {
                result = (ArrayList<UsuarioVO>) getFacade().getUsuario().consultarTodosUsuarioComLimite(
                        false, getEmpresaVO().getCodigo(), Uteis.NIVELMONTARDADOS_TELACONSULTA);
            } else {
                result = (ArrayList<UsuarioVO>) getFacade().getUsuario().consultarPorNomeUsuarioComLimite(
                        pref, getEmpresaVO().getCodigo(), false, Uteis.NIVELMONTARDADOS_TELACONSULTA);
            }
        } catch (Exception ex) {
            result = (new ArrayList<>());
        }
        return result;
    }

    private void validarPinETermos() throws Exception {
        validarPin();
        validarTermos();
    }

    private void validarDadosColaborador() throws Exception {
        if (UteisValidacao.emptyString(getPessoaVO().getNome())) {
            throw new ValidacaoException(new String[]{"nomeModalCadastro"}, "Informe seu primeiro nome.");
        }
        getPessoaVO().setNome(getPessoaVO().getNome().toUpperCase());

        if (UteisValidacao.emptyString(getPessoaVO().getSobreNome())) {
            throw new ValidacaoException(new String[]{"sobrenomeModalCadastro"}, "Informe seu sobrenome.");
        }
        getPessoaVO().setSobreNome(getPessoaVO().getSobreNome().toUpperCase());

        if (getPessoaVO().getCargoEnum() == null) {
            throw new ValidacaoException(new String[]{"cargoModalCadastro"}, "Selecione seu cargo.");
        }

        if (getPessoaVO().getFuncaoEnum() == null) {
            throw new ValidacaoException(new String[]{"funcaoModalCadastro"}, "Selecione sua função.");
        }
    }

    private void validarPin() throws Exception {
        if (!UteisValidacao.validarPinSeguro(getPin())) {
            throw new ValidacaoException(new String[]{"novoPin"}, "O PIN deve ser apenas números, ter quatro caracteres e não pode ser em sequência crescente ou decrescente!");
        }
    }

    private void validarTermos() throws Exception {
        if (!getPessoaVO().isAceiteTermosPacto()) {
            throw new ValidacaoException(new String[]{"termosModalCadastro"}, "Você deve ler e aceitar os termos de uso e privacidade.");
        }
    }

    private void validarDadosEmpresa() throws Exception {

        if (UteisValidacao.emptyString(getEmpresaVO().getResponsavelFinanceiro())) {
            throw new ValidacaoException(new String[]{"inputResponsavelFinanCadastro"}, "Você deve informar o responsável financeiro.");
        }

        if (getEmpresaVO().getClassificacaoNegocioEnum() == null || UteisValidacao.emptyString(getEmpresaVO().getClassificacaoNegocioEnum().getDescricao())) {
            throw new ValidacaoException(new String[]{"classificacaoNegocioEnum"}, "Você deve selecionar a classificação do negócio.");
        }

        if (UteisValidacao.emptyString(getEmpresaVO().getGestor())) {
            throw new ValidacaoException(new String[]{"inputGestorAcademiaCadastro"}, "Você deve informar o gestor da academia.");
        }
    }

    public PessoaVO getPessoaVO() {
        if (pessoaVO == null) {
            pessoaVO = new PessoaVO();
        }
        return pessoaVO;
    }

    public void setPessoaVO(PessoaVO pessoaVO) {
        this.pessoaVO = pessoaVO;
    }

    public UsuarioVO getUsuarioVO() {
        if (usuarioVO == null) {
            usuarioVO = new UsuarioVO();
        }
        return usuarioVO;
    }

    public void setUsuarioVO(UsuarioVO usuarioVO) {
        this.usuarioVO = usuarioVO;
    }

    public ConfiguracaoSistemaVO getConfiguracaoSistemaVO() {
        return configuracaoSistemaVO;
    }

    public void setConfiguracaoSistemaVO(ConfiguracaoSistemaVO configuracaoSistemaVO) {
        this.configuracaoSistemaVO = configuracaoSistemaVO;
    }

    public boolean isModalTermosPoliticas() {
        return modalTermosPoliticas;
    }

    public void setModalTermosPoliticas(boolean modalTermosPoliticas) {
        this.modalTermosPoliticas = modalTermosPoliticas;
    }

    public boolean isMostrarModalAtualizaDados() {
        return mostrarModalAtualizaDados;
    }

    public void setMostrarModalAtualizaDados(boolean mostrarModalAtualizaDados) {
        this.mostrarModalAtualizaDados = mostrarModalAtualizaDados;
    }

    public TelefoneVO getTelefoneVO() {
        if (telefoneVO == null) {
            telefoneVO = new TelefoneVO();
        }
        return telefoneVO;
    }

    public void setTelefoneVO(TelefoneVO telefoneVO) {
        this.telefoneVO = telefoneVO;
    }

    public String getOnComplete() {
        if (onComplete == null) {
            onComplete = "";
        }
        return onComplete;
    }

    public void setOnComplete(String onComplete) {
        this.onComplete = onComplete;
    }

    public EmailVO getEmailVO() {
        if (emailVO == null) {
            emailVO = new EmailVO();
        }
        return emailVO;
    }

    public void setEmailVO(EmailVO emailVO) {
        this.emailVO = emailVO;
    }

    public EmpresaVO getEmpresaVO() {
        if (empresaVO == null) {
            empresaVO = new EmpresaVO();
        }
        return empresaVO;
    }

    public void setEmpresaVO(EmpresaVO empresaVO) {
        this.empresaVO = empresaVO;
    }

    private void tratarValidacaoException(ValidacaoException ex) {
        montarErro(ex);
        setOnComplete(getMensagemNotificar());
        String[] campos = ex.getCampos();

        if (campos != null && campos.length > 0) {
            StringBuilder function = new StringBuilder("try{camposRequeridos('");
            for (String c : campos) {
                function.append(",").append(c);
            }
            function = new StringBuilder(function.toString().replaceFirst(",", "") + "', false)}catch(e){console.log(e)}; ");
            setOnComplete(function + getOnComplete());
        }
    }

    public boolean isModalDadosPessoa() {
        return modalDadosPessoa;
    }

    public void setModalDadosPessoa(boolean modalDadosPessoa) {
        this.modalDadosPessoa = modalDadosPessoa;
    }

    public boolean isAtualizarDadosEmpresa() {
        return atualizarDadosEmpresa;
    }

    public void setAtualizarDadosEmpresa(boolean atualizarDadosEmpresa) {
        this.atualizarDadosEmpresa = atualizarDadosEmpresa;
    }

    public String getMensagemTitulo() {
        if (isModalDadosPessoa()) {
            return "atualize seus dados";
        } else if (isModalDadosEmpresa() && isAtualizarDadosEmpresa()) {
            return "atualize as informações da sua empresa (" + getEmpresaVO().getNome() + ")";
        } else {
            return "verifique os termos de uso e privacidade";
        }
    }

    public String getPin() {
        return pin;
    }

    public void setPin(String pin) {
        this.pin = pin;
    }

    public boolean isAtualizacaoCadastralAdiada() {
        return atualizacaoCadastralAdiada;
    }

    public void setAtualizacaoCadastralAdiada(boolean atualizacaoCadastralAdiada) {
        this.atualizacaoCadastralAdiada = atualizacaoCadastralAdiada;
    }

    public boolean isModalDadosEmpresa() {
        return modalDadosEmpresa;
    }

    public void setModalDadosEmpresa(boolean modalDadosEmpresa) {
        this.modalDadosEmpresa = modalDadosEmpresa;
    }
}

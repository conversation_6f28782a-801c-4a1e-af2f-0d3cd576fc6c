package controle.basico;

import br.com.pactosolucoes.comuns.notificacao.RecursoSistema;
import br.com.pactosolucoes.comuns.util.JSFUtilities;
import controle.arquitetura.FavoritoEnum;
import controle.arquitetura.FuncionalidadeSistemaEnum;
import controle.arquitetura.MenuControle;
import controle.arquitetura.SuperControle;
import controle.arquitetura.menu.FuncionalidadeSistemaEnumTO;
import controle.arquitetura.menu.MenuExplorarConfig;
import controle.arquitetura.menu.MenuExplorarGrupo;
import controle.arquitetura.security.LoginControle;
import negocio.comuns.arquitetura.FuncionalidadeUsuario;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.utilitarias.Uteis;

import javax.faces.event.ActionEvent;
import java.text.MessageFormat;

public class MenuAcessoFacilControle extends SuperControle {

    public static final int HISTORICO_LIMITE = 15;
    public static final int FAVORITO_LIMITE = 15;
    private boolean mostrar = false;
    private boolean inicializado = false;
    private FuncionalidadeUsuario acessoMenuEmFavoritos;
    private FuncionalidadeUsuario funcionalidades;
    private String resultFavorito;
    private String msgResultAddFavorito;
    private String topMsgResultAddFavorito;

    public void inicializar() {
        if (!inicializado) {
            funcionalidades = obterFuncionalidades();
            processarMostrar();
            inicializado = true;
        }
    }

    private void processarMostrar(){
        mostrar = funcionalidades.getHistorico().size() > 0 || funcionalidades.getFavorito().size() > 0;
    }

    public void addFavoritoFuncionalidade(ActionEvent evt){
        try{
            notificarRecursoEmpresa(RecursoSistema.MENU_ACESSO_RAPIDO_FIXOU);
            FuncionalidadeSistemaEnum fun = (FuncionalidadeSistemaEnum)evt.getComponent().getAttributes().get("funcionalidade");
            addFavorito(fun);
            obterFuncionalidades();
            processarMostrar();
        }catch (Exception e){
            Uteis.logar(e,MenuAcessoFacilControle.class);
        }



    }

    private void addFavorito(FuncionalidadeSistemaEnum fun) throws Exception{
        funcionalidades = obterFuncionalidades();
        if(!funcionalidades.getFavorito().contains(fun)){
            String msgRetorno;
            if(funcionalidades.getFavorito().size()< FAVORITO_LIMITE) {
                funcionalidades.getFavorito().add(0, fun);
                if(funcionalidades.getHistorico().contains(fun)){
                    funcionalidades.getHistorico().remove(funcionalidades.getHistorico().indexOf(fun));
                    excluirHistorico(fun, getUsuarioLogado());
                }
                salvarFavorito(fun);
                funcionalidades = obterFuncionalidades();
                Integer qtde = FAVORITO_LIMITE - funcionalidades.getFavorito().size();
//                setResultFavorito("abrirMsgNotificacao()");
                msgRetorno = MessageFormat.format(getMensagemInternalizacao("msg_favorito_adicionado"), qtde, FAVORITO_LIMITE);
                setTopMsgResultAddFavorito(getMensagemInternalizacao("msg_top_favorito_adicionado"));
                setResultFavorito("try{ Notifier.success(\"Fixado favorito\",\"" + msgRetorno + "\"); } catch(e){}");
                setMsgResultAddFavorito(msgRetorno);
                processarFavoritos(fun, true);
            }else{
//                setResultFavorito("abrirMsgErro()");
                msgRetorno = MessageFormat.format(getMensagemInternalizacao("msg_favorito_limite_maximo_atingido"),  FAVORITO_LIMITE);
                setTopMsgResultAddFavorito(getMensagemInternalizacao("msg_top_favorito_limite_maximo_atingido"));
                setMsgResultAddFavorito(msgRetorno);
                setResultFavorito("try{ Notifier.error(\"Limite favoritos\",\"" + msgRetorno + "\"); } catch(e){}");
            }
        }
    }

    public void processarFavoritos(FuncionalidadeSistemaEnum fun, boolean adicionar) {
        try {
            LoginControle loginControle = JSFUtilities.getControlador(LoginControle.class);
            loginControle.preencherFuncionalidadesFavoritos();

            MenuControle menuControle = JSFUtilities.getControlador(MenuControle.class);
            for (MenuExplorarGrupo menuExplorarGrupo : menuControle.getGruposMenuExplorar()) {
                for (MenuExplorarConfig menuExplorarConfig : menuExplorarGrupo.getMenuExplorarConfigs()) {
                    if (menuExplorarConfig.getSubgrupos() != null) {
                        for (MenuExplorarConfig menuExplorarConfigSub : menuExplorarConfig.getSubgrupos()) {
                            for (FuncionalidadeSistemaEnumTO funcionalidadeSistemaEnumTO : menuExplorarConfigSub.getFuncionalidadesFavorito()) {
                                if (funcionalidadeSistemaEnumTO.getFuncionalidade().equals(fun)) {
                                    funcionalidadeSistemaEnumTO.setFavorito(adicionar);
                                }
                            }
                        }
                    }
                    for (FuncionalidadeSistemaEnumTO funcionalidadeSistemaEnumTO : menuExplorarConfig.getFuncionalidadesFavorito()) {
                        if (funcionalidadeSistemaEnumTO.getFuncionalidade().equals(fun)) {
                            funcionalidadeSistemaEnumTO.setFavorito(adicionar);
                        }
                    }
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    public void removerFavorito(ActionEvent evt){
        try{
            notificarRecursoEmpresa(RecursoSistema.MENU_ACESSO_RAPIDO_DESAFIXOU);
            FuncionalidadeSistemaEnum fun = (FuncionalidadeSistemaEnum)evt.getComponent().getAttributes().get("funcionalidade");
            funcionalidades = obterFuncionalidades();
            if(funcionalidades.getFavorito().contains(fun)){
                funcionalidades.getFavorito().remove(funcionalidades.getFavorito().indexOf(fun));
            }

            excluirFavorito(fun, getUsuarioLogado());
            processarFavoritos(fun, false);
        }catch (Exception e){
            Uteis.logar(e,MenuAcessoFacilControle.class);
        }

        processarMostrar();
    }

    private void excluirFavorito(FuncionalidadeSistemaEnum fun, UsuarioVO usuarioLogado){
        try {
            getFacade().getFavorito().excluirAcessoFacil(fun, usuarioLogado);
//            setResultFavorito("abrirMsgNotificacao()");
            setResultFavorito("try{ Notifier.success(\"Favorito removido\",\"Removido dos favoritos!\"); } catch(e){}");
            setTopMsgResultAddFavorito(getMensagemInternalizacao("msg_top_removido_favorito"));
            setMsgResultAddFavorito("");
        }catch (Exception e){
            Uteis.logar(e, MenuAcessoFacilControle.class);
        }
    }

    private void excluirHistorico(FuncionalidadeSistemaEnum fun, UsuarioVO usuarioLogado){
        try {
            getFacade().getFavorito().excluirAcessoFacilHistorico(fun, usuarioLogado);
        }catch (Exception e){
            Uteis.logar(e, MenuAcessoFacilControle.class);
        }
    }

    private void salvarHistorico(FuncionalidadeSistemaEnum fun){
        try {
            getFacade().getFavorito().inserirAcessoFacilHistorico(getUsuarioLogado().getCodigo(), fun);
        }catch (Exception e){
            Uteis.logar(e, MenuAcessoFacilControle.class);
        }
    }

    private void salvarFavorito(FuncionalidadeSistemaEnum fun){
        try {
            getFacade().getFavorito().inserirAcessoFacil(getUsuarioLogado().getCodigo(), fun);
        }catch (Exception e){
            Uteis.logar(e, MenuAcessoFacilControle.class);
        }
    }

    public void addHistoricoPreparaFuncionalidade(ActionEvent evt) {
        FuncionalidadeControle funcionalidadeControle = (FuncionalidadeControle) JSFUtilities.getFromSession("FuncionalidadeControle");
        if (funcionalidadeControle != null) {
            funcionalidadeControle.prepararFuncionalidade(evt);
        }
        addHistorinoFuncionalidade(evt);
    }

    public void addHistorinoFuncionalidade(ActionEvent evt){
        try{
            String nomeFun = (String) evt.getComponent().getAttributes().get("funcionalidade");
            FuncionalidadeSistemaEnum fun = FuncionalidadeSistemaEnum.obterPorNome(nomeFun);
            addHistorico(fun);
        }catch (Exception e){
            Uteis.logar(e,MenuAcessoFacilControle.class);
        }

        processarMostrar();
    }

    public void addHistorico(FuncionalidadeSistemaEnum funcionalidade) throws Exception{
        funcionalidades = obterFuncionalidades();
        acessoMenuEmFavoritos = obterFuncionalidades();
        if (!funcionalidades.getHistorico().contains(funcionalidade)) {
            acessoMenuEmFavoritos.getHistorico().add(0, funcionalidade);
            if(!funcionalidades.getFavorito().contains(funcionalidade)){
                int totalfuncionalidades = funcionalidades.getHistorico().size() + funcionalidades.getFavorito().size();
                if(totalfuncionalidades >= HISTORICO_LIMITE){
                    excluirHistorico(funcionalidades.getHistorico().get(funcionalidades.getHistorico().size() - 1), getUsuarioLogado());
                    funcionalidades.getHistorico().remove(funcionalidades.getHistorico().size() - 1);
                }
                funcionalidades.getHistorico().add(0, funcionalidade);
                salvarHistorico(funcionalidade);
            }
        }

        processarMostrar();
    }

    private void processarFuncionalidades(FuncionalidadeUsuario fun){
        FuncionalidadeControle funcionalidadeControle = getFuncionalidadeControle() == null ? new FuncionalidadeControle() : getFuncionalidadeControle();
        for(FuncionalidadeSistemaEnum funHistorico: fun.getHistorico()){
            try {
                funHistorico.prepararSubFuncionalidade();
                funHistorico.setRenderizar(funcionalidadeControle.validarPermissao(funHistorico.getExpressaoRenderizar()));
            }catch (Exception e){
                //ignorar
            }

        }
        for(FuncionalidadeSistemaEnum funFavorito: fun.getFavorito()){
            try{funFavorito.prepararSubFuncionalidade();
                funFavorito.setRenderizar(funcionalidadeControle.validarPermissao(funFavorito.getExpressaoRenderizar()));
            }catch (Exception e){
                //ignore
            }

        }
    }

    public FuncionalidadeUsuario obterFuncionalidades(){
        FuncionalidadeUsuario fun = JSFUtilities.getControlador(FuncionalidadeUsuario.class);
        if(fun == null){
            fun = new FuncionalidadeUsuario();
            JSFUtilities.storeOnSession("FuncionalidadeUsuario", fun);
        }
        try {
            Object menuZwUi = JSFUtilities.getFromSession("menuZwUi");
            if(menuZwUi == null){
                menuZwUi = isApresentarMenuZWUI();
            }
            if(menuZwUi == null){
                JSFUtilities.storeOnSession("menuZwUi", Boolean.TRUE);
                menuZwUi = Boolean.TRUE;
            }
            Integer limitFavorito = (Boolean) menuZwUi ? FAVORITO_LIMITE : 5;
            Integer limitHistorico = (Boolean) menuZwUi ? HISTORICO_LIMITE : 5;
            fun.setFavorito(getFacade().getFavorito().consultarAcessoFacil(getUsuarioLogado().getCodigo(), FavoritoEnum.ACESSO_FACIL_FAVORITO, limitFavorito));
            fun.setHistorico(getFacade().getFavorito().consultarAcessoFacil(getUsuarioLogado().getCodigo(), FavoritoEnum.ACESSO_FACIL_HISTORICO, limitHistorico));
            processarFuncionalidades(fun);
        } catch (Exception e) {
            montarErro(e);
        }

        return fun;
    }

    public FuncionalidadeUsuario getFuncionalidades() {
        return funcionalidades;
    }

    public void setFuncionalidades(FuncionalidadeUsuario funcionalidades) {
        this.funcionalidades = funcionalidades;
    }

    public boolean isMostrar() {
        if(funcionalidades == null) {
            inicializar();
        }
        return mostrar;
    }

    public void setMostrar(boolean mostrar) {
        this.mostrar = mostrar;
    }

    public boolean isInicializado() {
        return inicializado;
    }

    public void setInicializado(boolean inicializado) {
        this.inicializado = inicializado;
    }
    public String getMsgResultAddFavorito() { return msgResultAddFavorito; }

    public void setMsgResultAddFavorito(String msgResultAddFavorito) { this.msgResultAddFavorito = msgResultAddFavorito; }

    public String getTopMsgResultAddFavorito() {
        return topMsgResultAddFavorito;
    }

    public void setTopMsgResultAddFavorito(String topMsgResultAddFavorito) {
        this.topMsgResultAddFavorito = topMsgResultAddFavorito;
    }

    public String getResultFavorito() {
        return resultFavorito;
    }

    public void setResultFavorito(String resultFavorito) {
        this.resultFavorito = resultFavorito;
    }
}

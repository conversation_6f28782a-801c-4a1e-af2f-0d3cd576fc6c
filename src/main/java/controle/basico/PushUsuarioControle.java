/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package controle.basico;

import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.socialmailing.controle.SocialMailingControle;
import br.com.pactosolucoes.socialmailing.modelo.SocialMailGrupoVO;
import br.com.pactosolucoes.socialmailing.modelo.TipoSocialMailEnum;
import controle.arquitetura.SuperControle;
import controle.arquitetura.security.UsuarioControle;
import java.text.DecimalFormat;
import java.util.Calendar;
import negocio.comuns.arquitetura.UsuarioPerfilAcessoVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.feed.PerfilUsuarioEnum;
import negocio.comuns.plano.IndiceFinanceiroReajustePrecoVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import static negocio.facade.jdbc.arquitetura.FacadeManager.getFacade;

/*
 * <AUTHOR>
 */

public class PushUsuarioControle extends SuperControle {

    private String mensagem;
    private String onComplete;
    private SocialMailGrupoVO socialMailGrupoAbrir;

    public PushUsuarioControle() throws Exception {
        inicializar();
    }

    public void inicializar() {
    }

    public void processarMensagemPush() {
        try {
            setOnComplete("");

            if (SuperControle.getHabilitarLembreteSolicitacoes() && getMensagem().contains(getKey()) && !getMensagem().startsWith("ERRO")) {

                String[] split = getMensagem().split("##");
                String usuario = split[3];
                String idSolicitacao = split[4];

                Integer codUsuario = 0;
                boolean codUsu = false;
                try {
                    codUsuario = Integer.parseInt(usuario);
                    codUsu = true;
                } catch (Exception ex) {
                    codUsu = false;
                }

                try {
                    setSocialMailGrupoAbrir(getFacade().getSocialMailGrupo().consultarPorIdSolicitacao(Integer.parseInt(idSolicitacao)));
                    getSocialMailGrupoAbrir().setParticipantes(getFacade().getSocialMailGrupo().consultarParticipantes(getSocialMailGrupoAbrir().getCodigo()));
                } catch (Exception ex) {
                    setSocialMailGrupoAbrir(null);
                }

                UsuarioVO usuarioDestino = new UsuarioVO();
                if (codUsu) {
                    usuarioDestino = getFacade().getUsuario().consultarPorCodigo(codUsuario, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                } else {
                    usuarioDestino = getFacade().getUsuario().consultarPorUsername(usuario, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                }

                if (usuarioDestino != null && usuarioDestino.getCodigo() != 0) {
                    UsuarioVO usuarioVO = getUsuarioLogado();

                    if (usuarioVO.getAdministrador()) {
                        setOnComplete("");
                    } else {
                        usuarioVO.setUsuarioPerfilAcessoVOs(getFacade().getUsuarioPerfilAcesso().consultarUsuarioPerfilAcesso(usuarioVO.getCodigo(), Uteis.NIVELMONTARDADOS_MINIMOS));
                        for (Object o : usuarioVO.getUsuarioPerfilAcessoVOs()) {
                            UsuarioPerfilAcessoVO usuarioPerfilAcesso = (UsuarioPerfilAcessoVO) o;
                            if (usuarioDestino.getCodigo().equals(usuarioVO.getCodigo()) || (usuarioPerfilAcesso.getPerfilAcesso().getTipo().equals(PerfilUsuarioEnum.ADMINISTRADOR)
                                    && usuarioPerfilAcesso.getEmpresa().getCodigo().equals(getEmpresaLogado().getCodigo()))){
                                setOnComplete("Richfaces.showModalPanel('modalPush');");
                            }
                        }
                    }
                }
            }
        } catch (Exception ignored) {
        }
    }

    public void abrirSocialMailingConversa() {
        try {
            setMsgAlert("");
            try {
                SocialMailingControle social = (SocialMailingControle) getControlador(SocialMailingControle.class.getSimpleName());
                UsuarioControle usuarioControle = (UsuarioControle) getControlador(UsuarioControle.class.getSimpleName());
                social.setTipoSocialMail(TipoSocialMailEnum.SOLICITACAO);
                if (getSocialMailGrupoAbrir() != null && getSocialMailGrupoAbrir().getCodigo() != 0) {
                    social.preparaAbrirSolicitacoes();
                    social.escolherGrupo(getSocialMailGrupoAbrir());
                } else {
                    social.atualizarConversas();
                }
                usuarioControle.atualizarNrMsgNaoLidas(false);
                setMsgAlert("abrirPopup('" + JSFUtilities.getRequest().getContextPath() + "/faces/socialMailing.jsp', 'SocialMailing', 725, 620)");
            } catch (Exception e) {
                montarMsgAlert(e.getMessage());
            }
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    @Override
    public String getMensagem() {
        if (mensagem == null) {
            mensagem = "";
        }
        return mensagem;
    }

    @Override
    public void setMensagem(String mensagem) {
        this.mensagem = mensagem;
    }

    public String getOnComplete() {
        if (onComplete == null) {
            onComplete = "";
        }
        return onComplete;
    }

    public void setOnComplete(String onComplete) {
        this.onComplete = onComplete;
    }

    public SocialMailGrupoVO getSocialMailGrupoAbrir() {
        return socialMailGrupoAbrir;
    }

    public void setSocialMailGrupoAbrir(SocialMailGrupoVO socialMailGrupoAbrir) {
        this.socialMailGrupoAbrir = socialMailGrupoAbrir;
    }
}

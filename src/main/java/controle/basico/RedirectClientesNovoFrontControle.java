package controle.basico;

import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.enumeradores.Modulo;
import br.com.pactosolucoes.enumeradores.TipoInfoMigracaoEnum;
import controle.arquitetura.MenuControle;
import controle.arquitetura.security.LoginControle;
import controle.contrato.ContratoControle;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;

import javax.faces.context.FacesContext;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class RedirectClientesNovoFrontControle extends ClienteControle {

    private boolean novaTelaAlunoPadrao = false;
    private boolean novaTelaAlunoPadraoEmpresa = false;
    private String urlNovoCliente = "";

    public RedirectClientesNovoFrontControle() throws Exception {
        try {
            this.setUrlNovoCliente("");
            String pagina = FacesContext.getCurrentInstance().getExternalContext().getRequestParameterMap().get("page");

            Map<String, String> parametros = new HashMap<>();
            if (!FacesContext.getCurrentInstance().getExternalContext().getRequestParameterMap().isEmpty()) {
                parametros.putAll(FacesContext.getCurrentInstance().getExternalContext().getRequestParameterMap());
            }

            if (UteisValidacao.emptyString(pagina)) {
                pagina = "clientes";
            }

            processarRecursoPadrao();
            verificarAbrirNovaTelaCliente();
            if (!UteisValidacao.emptyString(this.urlNovoCliente)) {
                return;
            }

            StringBuilder urlParams = new StringBuilder();
            for (String key : parametros.keySet()) {
                urlParams.append("&").append(key).append("=").append(parametros.get(key));
            }

            String destino = ("/faces/" + pagina + ".jsp" + (urlParams.length() > 0 ? (urlParams.toString().replaceFirst("&", "?")) : ""));
            System.out.println(destino);
            redirect(destino);
        } catch (Exception e) {
            montarErro(e);
        }
    }

    private void processarRecursoPadrao() {
        try {
            this.novaTelaAlunoPadraoEmpresa = getFacade().getUsuario().recursoPadraoEmpresa(TipoInfoMigracaoEnum.TELA_ALUNO, getClienteVO().getEmpresa().getCodigo());
        }catch (Exception e){
            Uteis.logar(e, ContratoControle.class);
        }
        try {
            this.novaTelaAlunoPadrao = (this.novaTelaAlunoPadraoEmpresa ||
                    (getFacade().getUsuario().recursoHabilitado(TipoInfoMigracaoEnum.TELA_ALUNO,
                    getUsuarioLogado().getCodigo(), getClienteVO().getEmpresa().getCodigo())));
        }catch (Exception e){
            Uteis.logar(e, RedirectClientesNovoFrontControle.class);
        }
    }

    public void verificarAbrirNovaTelaCliente() {
        try {
            LoginControle loginControle = (LoginControle) JSFUtilities.getFromSession("LoginControle");
            if (this.isNovaTelaAlunoPadrao() &&
                    loginControle != null &&
                    loginControle.isApresentarModuloNovoTreino()) {
                MenuControle menuControle = JSFUtilities.getControlador(MenuControle.class);
                menuControle.setUrlGoBackRedirect(null);
                this.setUrlNovoCliente("window.open('" + loginControle.getAbrirNovaPlataforma(Modulo.NOVO_TREINO.getSiglaModulo()) + "&redirect=/pessoas/lista-v2', '_self')");
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    public boolean isNovaTelaAlunoPadrao() {
        return novaTelaAlunoPadrao;
    }

    public void setNovaTelaAlunoPadrao(boolean novaTelaAlunoPadrao) {
        this.novaTelaAlunoPadrao = novaTelaAlunoPadrao;
    }

    public boolean isNovaTelaAlunoPadraoEmpresa() {
        return novaTelaAlunoPadraoEmpresa;
    }

    public void setNovaTelaAlunoPadraoEmpresa(boolean novaTelaAlunoPadraoEmpresa) {
        this.novaTelaAlunoPadraoEmpresa = novaTelaAlunoPadraoEmpresa;
    }

    public String getUrlNovoCliente() {
        return urlNovoCliente;
    }

    public void setUrlNovoCliente(String urlNovoCliente) {
        this.urlNovoCliente = urlNovoCliente;
    }
}

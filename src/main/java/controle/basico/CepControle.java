package controle.basico;

import br.com.pactosolucoes.integracao.viacep.ViaCepServico;
import controle.arquitetura.SuperControle;

import java.util.List;
import java.util.ArrayList;

import negocio.comuns.utilitarias.CepVO;
import negocio.comuns.utilitarias.ControleConsulta;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.utilitarias.Cep;

/**
 * Classe responsável por implementar a interação entre os componentes JSF das páginas 
 * com as funcionalidades da classe <code>Cep</code>.
 * @see SuperControle
 * @see Cep
 * @see CepVO
 */
public class CepControle extends SuperControle {
    private CepVO cepVO;
    protected List listaConsultaCep;

    /**
     * Interface <code>CepInterfaceFacade</code> responsável pela interconexão da camada de controle com a camada de negócio.
     * Criando uma independência da camada de controle com relação a tenologia de persistência dos dados (DesignPatter: Façade).
     */

    public CepControle() throws Exception {
        novo();
        obterUsuarioLogado();
        inicializarFacades();
        setControleConsulta(new ControleConsulta());
        setMensagemID("msg_entre_prmconsulta");
    }

    public void consultarCEP(String cep) throws Exception {
        try {
            CepVO obj = new CepVO();
            try {
                obj = getFacade().getCep().consultarPorNumeroCep(Uteis.removerMascara(cep), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            } catch (Exception e) {
                try {
                    obj = getFacade().getCep().consultarPorNumeroCepGeral(Uteis.removerMascara(cep), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                } catch (Exception ex) {
                    obj = getFacade().getCep().consultarApiViaCEP(cep);
                }
            }
            setCepVO(obj);
            setMensagemDetalhada("");
            setMensagemID("");
            setMensagem("");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setCepVO(new CepVO());
            setErro(true);
            setSucesso(false);
            e.printStackTrace();
        }
    }

    public void consultarCEPCadastroCompleto(String cep) throws Exception {
        try {
            CepVO obj;
            try {
                obj = getFacade().getCep().consultarPorNumeroCep(Uteis.removerMascara(cep), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            } catch (Exception e) {
                obj = getFacade().getCep().consultarApiViaCEP(cep);
            }
            setCepVO(obj);
            setMensagemDetalhada("");
            setMensagemID("");
            setMensagem("");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setCepVO(new CepVO());
            setErro(true);
            setSucesso(false);
            e.printStackTrace();
        }
    }

    /**
     * Para usar esta consulta atualize o atributo cepVO com os seguintes dados
     * cepVO.enderecoLogradouro, cepVO.cidadeDescricao, cepVO.ufSigla
     * @throws Exception
     */
    @SuppressWarnings("unchecked")
	public void consultarCEPDetalhe() throws Exception {
        try {
            // esta consulta utiliza apenas 3 atributos para consultar: cep.enderecoLogradouro, cep.cidadeDescricao, cep.ufSigla
            List lista = getFacade().getCep().consultarDetalhe(getCepVO(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            setListaConsultaCep(lista);
            setMensagemDetalhada("");
            setMensagemID("");
            setMensagem("");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setListaConsultaCep(new ArrayList());
            setErro(true);
            setSucesso(false);
            e.printStackTrace();
        }
    }

    /**
     * Rotina responsável por disponibilizar um novo objeto da classe <code>CepControle</code>
     * para edição pelo usuário da aplicação.
     */
    public String novo() {
        setCepVO(new CepVO());
        setListaConsultaCep(new ArrayList());
        setMensagemID("msg_entre_dados");
        setSucesso(false);
        setErro(false);
        return "editar";
    }

    public void irPaginaInicial() throws Exception {
        controleConsulta.setPaginaAtual(1);
        this.consultar();
    }

    public void irPaginaAnterior() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getPaginaAtual() - 1);
        this.consultar();
    }

    public void irPaginaPosterior() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getPaginaAtual() + 1);
        this.consultar();
    }

    public void irPaginaFinal() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getNrTotalPaginas());
        this.consultar();
    }

    /**
     * Rotina responsável por organizar a paginação entre as páginas resultantes de uma consulta.
     */
    public String inicializarConsultar() {
        setPaginaAtualDeTodas("0/0");
        setListaConsulta(new ArrayList());
        definirVisibilidadeLinksNavegacao(0, 0);
        setMensagemID("msg_entre_prmconsulta");
        setSucesso(false);
        setErro(false);
        return "consultar";
    }

    /**
     * Operação que inicializa as Interfaces Façades com os respectivos objetos de
     * persistência dos dados no banco de dados. 
     */
    protected boolean inicializarFacades() {
        try {
            super.inicializarFacades();
            
            return true;
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro_conectarBD", e.getMessage());
            return false;
        }
    }

    public CepVO getCepVO() {
        return cepVO;
    }

    public void setCepVO(CepVO cepVO) {
        this.cepVO = cepVO;
    }

    public List getListaConsultaCep() {
        return listaConsultaCep;
    }

    public void setListaConsultaCep(List listaConsultaCep) {
        this.listaConsultaCep = listaConsultaCep;
    }
}

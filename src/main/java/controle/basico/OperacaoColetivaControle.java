package controle.basico;

import br.com.pactosolucoes.comuns.notificacao.RecursoSistema;
import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.enumeradores.TipoTurmaEnum;
import controle.arquitetura.SuperControle;
import controle.arquitetura.security.LoginControle;
import negocio.comuns.basico.ConfiguracaoSistemaVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.ManutencaoAjusteGeralTO;
import negocio.comuns.contrato.OperacaoColetivaVO;
import negocio.comuns.contrato.StatusOperacaoColetivaEnum;
import negocio.comuns.contrato.TipoOperacaoColetivaEnum;
import negocio.comuns.plano.ModalidadeVO;
import negocio.comuns.plano.PlanoVO;
import negocio.comuns.plano.TurmaVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;

import javax.faces.model.SelectItem;
import java.util.ArrayList;
import java.util.List;

/**
 * Created by GlaucoT on 15/02/2016
 */
public class OperacaoColetivaControle extends SuperControle {

    private OperacaoColetivaVO operacaoColetivaVO;
    private List<OperacaoColetivaVO> listaOperacoesColetivas;
    private List<SelectItem> listaSelectItemPlanosOperacaoColetiva;
    private List<SelectItem> listaSelectItemOperacoesColetivas;
    private ManutencaoAjusteGeralTO manutencaoAjusteGeralTO;
    private List<SelectItem> listaSelectItemModalidadesOperacaoColetiva;
    private List<SelectItem> listaSelectItemTurmasOperacaoColetiva;
    private ConfiguracaoSistemaVO configuracaoSistema;

    private void inicializarDados() throws Exception {
        listaOperacoesColetivas = getFacade().getOperacaoColetiva().consultar(null, null, null, Uteis.NIVELMONTARDADOS_TODOS);

        montarListaEmpresas();

        operacaoColetivaVO = new OperacaoColetivaVO();
        if (!getUsuarioLogado().getAdministrador()) {
            EmpresaVO empresaLogado = getEmpresaLogado();
            operacaoColetivaVO.getEmpresa().setCodigo(empresaLogado.getCodigo());

            montarListasPlanosOperacaoColetiva();
            montarListasModalidadesOperacaoColetiva();
            listaSelectItemTurmasOperacaoColetiva = new ArrayList<SelectItem>();
        }
        montarListaOperacoeColetivas();
        inicializarConfiguracaoSistema();
    }

    private void montarListaOperacoeColetivas() {
        listaSelectItemOperacoesColetivas = new ArrayList<>();
        listaSelectItemOperacoesColetivas.add(new SelectItem(null, ""));
        for (TipoOperacaoColetivaEnum tipoOperacao : TipoOperacaoColetivaEnum.values()) {
            listaSelectItemOperacoesColetivas.add(new SelectItem(tipoOperacao, tipoOperacao.getDescricao()));
        }
    }

    public void inicializarConfiguracaoSistema(){
        try {
            setConfiguracaoSistema((ConfiguracaoSistemaVO) JSFUtilities.getFromSession(JSFUtilities.CONFIGURACAO_SISTEMA));
        } catch (Exception e){
            throw e;
        }
    }

    public void abrirPopup() {
        try {
            setMsgAlert("");
            limparMsg();

            inicializarDados();

            setMsgAlert("abrirPopup('" + request().getContextPath() + "/faces/operacoesColetivasCons.jsp', 'Operações Coletivas', 1000, 650);");
        } catch (Exception e) {
            setMensagemDetalhada(e);
            montarMsgAlert(getMensagemDetalhada());
        }
    }

    public void gravarOperacaoColetiva() {
        limparMsg();
        try {
            operacaoColetivaVO.setStatus(StatusOperacaoColetivaEnum.AGUARDANDO);
            operacaoColetivaVO.setUsuario(getUsuarioLogado().getNome() + " ^ " + getUsuarioLogado().getUserOamd());
            operacaoColetivaVO.validarDados();
            String validarConflito = getFacade().getOperacaoColetiva().existeOperacaoConflitante(operacaoColetivaVO);
            if (!UteisValidacao.emptyString(validarConflito)) {
                throw new Exception(validarConflito);
            }
            getFacade().getOperacaoColetiva().incluir(operacaoColetivaVO);
            carregarDadosEntidades();
            incluirLogInclusao();
            if (Calendario.menorOuIgual(operacaoColetivaVO.getDataInicio(), Calendario.hoje())) {
                montarSucessoGrowl("Solicitação foi gravada e será processada automaticamente no dia " + Uteis.getData(Uteis.somarDias(Calendario.hoje(), 1)));
            } else {
                montarSucessoGrowl("Solicitação foi gravada e será processada automaticamente no dia " + Uteis.getData(operacaoColetivaVO.getDataInicio()));
            }
            listaOperacoesColetivas = getFacade().getOperacaoColetiva().consultar(null, null, null, Uteis.NIVELMONTARDADOS_TODOS);
            operacaoColetivaVO = new OperacaoColetivaVO();
            notificarRecursoEmpresa(RecursoSistema.ADICIONAR_BONUS_COLETIVO);
        } catch (Exception ex) {
            montarErro(ex);
        }
    }

    private void carregarDadosEntidades() throws Exception {
        if (operacaoColetivaVO != null) {
            if (!UteisValidacao.emptyNumber(operacaoColetivaVO.getEmpresa().getCodigo())) {
                operacaoColetivaVO.setEmpresa(getFacade().getEmpresa().consultarPorChavePrimaria(operacaoColetivaVO.getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_GESTAOREMESSA));
            }
            if (!UteisValidacao.emptyNumber(operacaoColetivaVO.getPlanoVO().getCodigo())) {
                operacaoColetivaVO.setPlanoVO(getFacade().getPlano().consultarPorChavePrimaria(operacaoColetivaVO.getPlanoVO().getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));
            }
            if (!UteisValidacao.emptyNumber(operacaoColetivaVO.getModalidadeVO().getCodigo())) {
                operacaoColetivaVO.setModalidadeVO(getFacade().getModalidade().consultarPorChavePrimaria(operacaoColetivaVO.getModalidadeVO().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            }
            if (!UteisValidacao.emptyNumber(operacaoColetivaVO.getTurmaVO().getCodigo())) {
                operacaoColetivaVO.setTurmaVO(getFacade().getTurma().consultarPorChavePrimaria(operacaoColetivaVO.getTurmaVO().getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));
            }
        }
    }

    public void montarListasPlanosOperacaoColetiva() {
        operacaoColetivaVO.setPlanoVO(new PlanoVO());
        try {
            listaOperacoesColetivas = getFacade().getOperacaoColetiva().consultar(operacaoColetivaVO.getEmpresa().getCodigo(), null, null, Uteis.NIVELMONTARDADOS_TODOS);
        } catch (Exception e) {
            montarErro(e);
        }
        setListaSelectItemPlanosOperacaoColetiva(montarListaSelectItemPlanos(operacaoColetivaVO.getEmpresa()));
    }

    private List<SelectItem> montarListaSelectItemPlanos(EmpresaVO empresa) {
        List<SelectItem> listaSelectItemPlanos = new ArrayList<>();
        try {
            if (empresa != null && empresa.getCodigo() > 0) {
                listaSelectItemPlanos.add(new SelectItem(0, "TODOS"));
                List<PlanoVO> resultadoConsulta = getFacade().getPlano().consultarPlanosPorCodigoEmpresa(empresa.getCodigo(), Uteis.NIVELMONTARDADOS_MINIMOS);
                for (PlanoVO obj : resultadoConsulta) {
                    listaSelectItemPlanos.add(new SelectItem(obj.getCodigo(), obj.getDescricao()));
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return listaSelectItemPlanos;
    }

    public void excluirOperacaoColetiva() {
        try {
            limparMsg();
            OperacaoColetivaVO obj = (OperacaoColetivaVO) context().getExternalContext().getRequestMap().get("operacao");
            if (obj != null) {
                operacaoColetivaVO = obj;
                carregarDadosEntidades();
                operacaoColetivaVO.registrarObjetoVOAntesDaAlteracao();
            }

            operacaoColetivaVO.setUsuarioExclusao(getUsuarioLogado().getNome() + " ^ " + getUsuarioLogado().getUserOamd());

            if (operacaoColetivaVO.getStatus().equals(StatusOperacaoColetivaEnum.PROCESSADA)) {
                if (Calendario.menor(operacaoColetivaVO.getDataFinal(), Calendario.hoje())) {
                    throw new Exception("Operação só pode ser excluída até a data final do recesso.");
                }
                operacaoColetivaVO.setStatus(StatusOperacaoColetivaEnum.AGUARDANDO_EXCLUSAO);
                getFacade().getOperacaoColetiva().alterar(operacaoColetivaVO);
                montarSucessoGrowl("Solicitação já tinha sido processada. Todos alunos terão a operação estornada durante a madrugada. ");
                incluirLogAlteracao();
            } else if (operacaoColetivaVO.getStatus().equals(StatusOperacaoColetivaEnum.AGUARDANDO_EXCLUSAO)) {
                throw new Exception("Operação aguardando exclusão, não é possível realizar o estorno.");
            } else {
                operacaoColetivaVO.setStatus(StatusOperacaoColetivaEnum.EXCLUIDA);
                operacaoColetivaVO.setResultado("Operação excluída antes de ter sido processada.");
                getFacade().getOperacaoColetiva().alterar(operacaoColetivaVO);
                montarSucessoGrowl("Solicitação ainda não havia sido processada e foi excluída");
                incluirLogExclusao();
            }
            listaOperacoesColetivas = getFacade().getOperacaoColetiva().consultar(null, null, null, Uteis.NIVELMONTARDADOS_TODOS);
            operacaoColetivaVO = new OperacaoColetivaVO();

            notificarRecursoEmpresa(RecursoSistema.REMOVER_BONUS_COLETIVO);
        } catch (Exception ex) {
            montarErro(ex);
        }
    }

    public OperacaoColetivaVO getOperacaoColetivaVO() {
        if (operacaoColetivaVO == null) {
            operacaoColetivaVO = new OperacaoColetivaVO();
        }
        return operacaoColetivaVO;
    }

    public void setOperacaoColetivaVO(OperacaoColetivaVO operacaoColetivaVO) {
        this.operacaoColetivaVO = operacaoColetivaVO;
    }


    public List<OperacaoColetivaVO> getListaOperacoesColetivas() {
        if (listaOperacoesColetivas == null) {
            listaOperacoesColetivas = new ArrayList<>();
        }
        return listaOperacoesColetivas;
    }

    public void setListaOperacoesColetivas(List<OperacaoColetivaVO> listaOperacoesColetivas) {
        this.listaOperacoesColetivas = listaOperacoesColetivas;
    }


    public List<SelectItem> getListaSelectItemPlanosOperacaoColetiva() {
        if (listaSelectItemPlanosOperacaoColetiva == null) {
            listaSelectItemPlanosOperacaoColetiva = new ArrayList<>();
        }
        return listaSelectItemPlanosOperacaoColetiva;
    }

    public void setListaSelectItemPlanosOperacaoColetiva(List<SelectItem> listaSelectItemPlanosOperacaoColetiva) {
        this.listaSelectItemPlanosOperacaoColetiva = listaSelectItemPlanosOperacaoColetiva;
    }

    public ManutencaoAjusteGeralTO getManutencaoAjusteGeralTO() {
        return manutencaoAjusteGeralTO;
    }

    public void setManutencaoAjusteGeralTO(ManutencaoAjusteGeralTO manutencaoAjusteGeralTO) {
        this.manutencaoAjusteGeralTO = manutencaoAjusteGeralTO;
    }

    public List<SelectItem> getListaSelectItemOperacoesColetivas() {
        return listaSelectItemOperacoesColetivas;
    }

    public void setListaSelectItemOperacoesColetivas(List<SelectItem> listaSelectItemOperacoesColetivas) {
        this.listaSelectItemOperacoesColetivas = listaSelectItemOperacoesColetivas;
    }

    public List<SelectItem> getListaSelectItemModalidadesOperacaoColetiva() {
        return listaSelectItemModalidadesOperacaoColetiva;
    }

    public void setListaSelectItemModalidadesOperacaoColetiva(List<SelectItem> listaSelectItemModalidadesOperacaoColetiva) {
        this.listaSelectItemModalidadesOperacaoColetiva = listaSelectItemModalidadesOperacaoColetiva;
    }

    public List<SelectItem> getListaSelectItemTurmasOperacaoColetiva() {
        return listaSelectItemTurmasOperacaoColetiva;
    }

    public void setListaSelectItemTurmasOperacaoColetiva(List<SelectItem> listaSelectItemTurmasOperacaoColetiva) {
        this.listaSelectItemTurmasOperacaoColetiva = listaSelectItemTurmasOperacaoColetiva;
    }

    public void montarListasModalidadesOperacaoColetiva() {
        operacaoColetivaVO.setModalidadeVO(new ModalidadeVO());
        try {
            listaOperacoesColetivas = getFacade().getOperacaoColetiva().consultar(operacaoColetivaVO.getEmpresa().getCodigo(), null, null, Uteis.NIVELMONTARDADOS_TODOS);
        } catch (Exception e) {
            montarErro(e);
        }
        setListaSelectItemModalidadesOperacaoColetiva(montarListaSelectItemModalidades(operacaoColetivaVO.getEmpresa()));
    }

    private List<SelectItem> montarListaSelectItemModalidades(EmpresaVO empresa) {
        List<SelectItem> listaSelectItemModalidades = new ArrayList<>();
        try {
            if (empresa != null && empresa.getCodigo() > 0) {
                listaSelectItemModalidades.add(new SelectItem(0, "SELECIONE"));
                List<ModalidadeVO> resultadoConsulta = getFacade().getModalidade().consultarModalidadeAtivaComTurma(empresa.getCodigo(), TipoTurmaEnum.TURMA_ZW, Uteis.NIVELMONTARDADOS_MINIMOS);
                for (ModalidadeVO obj : resultadoConsulta) {
                    listaSelectItemModalidades.add(new SelectItem(obj.getCodigo(), obj.getNome()));
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return listaSelectItemModalidades;
    }

    public void montarListasTurmasOperacaoColetiva() {
        operacaoColetivaVO.setTurmaVO(new TurmaVO());
        try {
            listaOperacoesColetivas = getFacade().getOperacaoColetiva().consultar(operacaoColetivaVO.getEmpresa().getCodigo(), null, null, Uteis.NIVELMONTARDADOS_TODOS);
        } catch (Exception e) {
            montarErro(e);
        }
        setListaSelectItemTurmasOperacaoColetiva(montarListaSelectItemTurmas(operacaoColetivaVO.getEmpresa()));
    }

    private List<SelectItem> montarListaSelectItemTurmas(EmpresaVO empresa) {
        List<SelectItem> listaSelectItemTurmas = new ArrayList<>();
        try {
            if (empresa != null && empresa.getCodigo() > 0) {
                listaSelectItemTurmas.add(new SelectItem(0, "SELECIONE"));
                List<TurmaVO> resultadoConsulta = getFacade().getTurma().consultarPorCodigoModalidade(operacaoColetivaVO.getModalidadeVO().getCodigo(), empresa.getCodigo(), Uteis.NIVELMONTARDADOS_MINIMOS);
                for (TurmaVO obj : resultadoConsulta) {
                    String labelComIdentificador = obj.getDescricao() + " - " + obj.getIdentificador();
                    listaSelectItemTurmas.add(new SelectItem(obj.getCodigo(), labelComIdentificador));
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return listaSelectItemTurmas;
    }

    public void incluirLogInclusao() throws Exception {
        try {
            operacaoColetivaVO.setObjetoVOAntesAlteracao(new OperacaoColetivaVO());
            operacaoColetivaVO.setNovoObj(true);
            registrarLogObjetoVO(operacaoColetivaVO, operacaoColetivaVO.getCodigo(), getNomeClasse().toUpperCase(), 0);
        } catch (Exception e) {
            registrarLogErroObjetoVO(getNomeClasse().toUpperCase(), operacaoColetivaVO.getCodigo(), "ERRO AO GERAR LOG DE INCLUSÃO DE OPERAÇÃO COLETIVA", this.getUsuarioLogado().getNome(), this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
        operacaoColetivaVO.setNovoObj(Boolean.FALSE);
        operacaoColetivaVO.registrarObjetoVOAntesDaAlteracao();
    }

    public void incluirLogExclusao() throws Exception {
        try {
            registrarLogExclusaoTodosDadosObjetoVO(operacaoColetivaVO, operacaoColetivaVO.getCodigo(), getNomeClasse().toUpperCase(), 0);
        } catch (Exception e) {
            registrarLogErroObjetoVO(getNomeClasse().toUpperCase(), operacaoColetivaVO.getCodigo(), "ERRO AO GERAR LOG DE EXCLUSÃO DE OPERAÇÃO COLETIVA", this.getUsuarioLogado().getNome(), this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
        operacaoColetivaVO.setObjetoVOAntesAlteracao(new OperacaoColetivaVO());
        operacaoColetivaVO.setNovoObj(true);
    }

    public void incluirLogAlteracao() throws Exception {
        try {
            registrarLogObjetoVO(operacaoColetivaVO, operacaoColetivaVO.getCodigo(), getNomeClasse().toUpperCase(), 0);
        } catch (Exception e) {
            registrarLogErroObjetoVO(getNomeClasse().toUpperCase(), operacaoColetivaVO.getCodigo(), "ERRO AO GERAR LOG DE ALTERAÇÃO DE OPERAÇÃO COLETIVA", this.getUsuarioLogado().getNome(), this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
        operacaoColetivaVO.registrarObjetoVOAntesDaAlteracao();
    }

    public void realizarConsultaLogObjetoGeral() {
        LoginControle loginControle = (LoginControle) context().getExternalContext().getSessionMap().get("LoginControle");
        loginControle.setNomeClasse(loginControle.getNomeEntidadeCamposLog("prt_" + getNomeClasse() + "_tituloForm"));
        loginControle.consultarLogObjetoSelecionado(getNomeClasse().toUpperCase(), 0, 0);
    }

    private String getNomeClasse() {
        String nomeClasse = operacaoColetivaVO.getClass().getSimpleName();
        return nomeClasse.substring(0, nomeClasse.length() - 2);
    }

    public ConfiguracaoSistemaVO getConfiguracaoSistema() {
        return configuracaoSistema;
    }

    public void setConfiguracaoSistema(ConfiguracaoSistemaVO configuracaoSistema) {
        this.configuracaoSistema = configuracaoSistema;
    }
}

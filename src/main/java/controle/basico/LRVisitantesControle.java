package controle.basico;

import br.com.pactosolucoes.comuns.to.FiltrosConsultaRelatorioBVsTO;
import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.enumeradores.TipoColaboradorEnum;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.TipoColaboradorVO;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.Uteis;
import org.richfaces.component.UIDataTable;
import relatorio.controle.arquitetura.SuperControleRelatorio;
import relatorio.negocio.comuns.basico.ItemRelatorioTO;

import javax.faces.event.ActionEvent;
import javax.faces.model.SelectItem;
import java.io.File;
import java.util.*;

/**
 * <AUTHOR>
 */
public class LRVisitantesControle extends SuperControleRelatorio {

    private List<ColaboradorVO> consultores;
    private ColaboradorVO consultor = new ColaboradorVO();
    private TipoColaboradorVO tipoColaboradorVO = new TipoColaboradorVO();
    private FiltrosConsultaRelatorioBVsTO filtrosConsulta;
    private List<ItemRelatorioTO> resultado = new ArrayList<ItemRelatorioTO>();
    private String situacao;
    private Integer tipoConsulta = 1;
    private Date inicioBv;
    private Date fimBv;
    private String bvCompIncomp;
    private ItemRelatorioTO itemRelatorio = new ItemRelatorioTO();
    private boolean apresentarLinha1 = true;
    private boolean apresentarLinha2 = true;
    private boolean apresentarLinha3 = false;
    private boolean permissaoConsultaTodasEmpresas = false;
    private Integer filtroEmpresa;

    public LRVisitantesControle() {
        super();
        montarListaSelectItemEmpresa();
    }

    public void montarListaSelectItemEmpresa() {
        try {
            permissaoConsultaTodasEmpresas = permissao("ConsultarInfoTodasEmpresas");
            if (permissaoConsultaTodasEmpresas) {
                montarListaEmpresasComItemTodas();
            }
            setFiltroEmpresa(getEmpresaLogado().getCodigo());
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static String getCaminhoSubRelatorio() {
        return ("relatorio" + File.separator + "designRelatorio" + File.separator + "listasRelatorios" + File.separator);
    }

    public void recarregarTela() throws Exception {
        getListaConsultor();
    }

    public void limparFiltros() {
        bvCompIncomp = "";
        consultores = null;
        limparBv();
    }

    public void irParaTelaCliente() {
        ItemRelatorioTO obj = (ItemRelatorioTO) context().getExternalContext().getRequestMap().get("item");
        try {
            if (obj == null) {
                throw new Exception("Cliente Não Encontrado.");
            } else {
                irParaTelaCliente(obj.getClienteVO());
            }
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void prepareEditar(ActionEvent evt) {
        setNavigationCase(tratarNavigationCaseIntegracaoModulo("editar",
                "modulo_visualiza_cliente", evt));

    }

    public void limparBv() {
        inicioBv = null;
        fimBv = null;
    }

    public void prepararImpr() {
        ItemRelatorioTO impr = (ItemRelatorioTO) context().getExternalContext().getRequestMap().get("impr");
        if (impr != null) {
            setItemRelatorio(impr);
        }
    }

    public void consultarRelatorioVi() {
        try {
            resultado = getFacade().getCliente().consultarRelatorioVi(
                    getInicioBv(), getFimBv(), getConsultor(), consultores, getSituacao(), getBvCompIncomp(), getFiltroEmpresa());
        } catch (Exception e) {
            setMensagemID("msg_erro");
            setMensagemDetalhada(e);
        }
    }

    public int getTotalItens() {
        return getResultado().size();
    }
// <------------------------ IMPRIMIR ---------------------->

    public String getDadosImpressao() throws Exception {
        StringBuilder filtros = new StringBuilder();
        boolean pontuacao = (isApresentarLinha1() || isApresentarLinha2() || isApresentarLinha3());
        if (isApresentarLinha1()) {
            filtros.append("Dados Cadastrais");
            if (pontuacao) {
                filtros.append(", ");
            } else {
                filtros.append(".");
            }
        }
        pontuacao = pontuacao && (isApresentarLinha3());
        if (isApresentarLinha2()) {
            filtros.append("Endereço");
            if (pontuacao) {
                filtros.append(", ");
            }
        }
        if (isApresentarLinha3()) {
            filtros.append("Contratos e Planos.");
        }
        return filtros.toString();
    }

    private Map<String, Object> prepareParams() throws Exception {
        Integer emp = getUsuarioLogado().getAdministrador() ? 0 : getEmpresaLogado().getCodigo();
        EmpresaVO empre = new EmpresaVO();
        if (emp != 0) {
            empre = getFacade().getEmpresa().consultarPorChavePrimaria(emp, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        }

        Map<String, Object> params = new HashMap<String, Object>();
        params.put("nomeRelatorio", "ListaAlunos");
        params.put("nomeEmpresa", empre.getNome());

        params.put("tituloRelatorio", "Relatório de Visitantes");
        params.put("nomeDesignIReport", getDesignIReportRelatorio());
        params.put("usuario", getUsuarioLogado().getNomeAbreviado());

        params.put("listaObjetos", resultado);
        params.put("enderecoEmpresa", empre.getEndereco());
        params.put("cidadeEmpresa", empre.getCidade().getNome());
        params.put("totalClientes", getTotalItens());
        params.put("apresentarLinha1", isApresentarLinha1());
        params.put("apresentarLinha2", isApresentarLinha2());
        params.put("apresentarLinha3", isApresentarLinha3());
        params.put("dadosImpressao", getDadosImpressao());
        params.put("filtro", montarFiltros());

        return params;
    }

    public String montarFiltros() throws Exception {
        StringBuilder filtros = new StringBuilder();

        if (getInicioBv() != null && getFimBv() != null) {
            filtros.append("Data de BV: ").append(Uteis.getDataAplicandoFormatacao(getInicioBv(), "dd/MM/yyyy")).append(" a ").append(Uteis.getDataAplicandoFormatacao(getFimBv(), "dd/MM/yyyy")).append(" | ");
        }
        if (!getConsultores().isEmpty()) {
            String consultoresSelecionados = "";
            for (ColaboradorVO colab : consultores) {
                if (colab.getColaboradorEscolhido()) {
                    consultoresSelecionados += colab.getPessoa().getNome() + ", ";
                }
            }
            consultoresSelecionados = consultoresSelecionados.trim();
            if (consultoresSelecionados.length() > 0) {
                filtros.append("Consultor: ");
                consultoresSelecionados = consultoresSelecionados.substring(0, (consultoresSelecionados.length() - 1));
                filtros.append(consultoresSelecionados).append(" | ");
            }
        }
        String filtrosApresentar = filtros.toString();
        if (filtrosApresentar.length() > 0) {
            filtrosApresentar = filtrosApresentar.trim().substring(0, (filtrosApresentar.length() - 2));
        }
        return filtrosApresentar;
    }

    @Override
    public String getNomeRefPastaRelatorioRelatorioGeradoAgora() {
        String hRef = getNomeArquivoRelatorioGeradoAgora();
        if (!hRef.isEmpty()) {
            return "location.href=\"../../relatorio/" + hRef + "\"";
        } else {
            return "";
        }
    }

    public String getDesignIReportRelatorio() {
        return ("relatorio" + File.separator + "designRelatorio" + File.separator + "listasRelatorios" + File.separator + "ListaAlunos.jrxml");
    }

    public void imprimirListaRelatorio() throws Exception {
        try {
            if (!resultado.isEmpty()) {
                UIDataTable dataTable = (UIDataTable) context().getViewRoot().findComponent("form:resultados");
                if (dataTable != null) {
                    String colunaOrdenacao = JSFUtilities.getColunaOrdenacao(dataTable);
                    if (!colunaOrdenacao.isEmpty()) {
                        String[] params = colunaOrdenacao.split(":");
                        Ordenacao.ordenarLista(resultado, params[0]);
                        if (params[1].equals("DESC")) {
                            Collections.reverse(resultado);
                        }
                    }
                }
            }
            setMsgAlert("");
            if (resultado.isEmpty()) {
                throw new ConsistirException("Nenhum registro a ser impresso, faça a consulta novamente.");
            }
            //Verificando se foi marcado no minimo uma opção(Dados Cadastrais, Endereço) de relatório de visitantes na téla.
            if(apresentarLinha1 == false && apresentarLinha2 == false){
                throw new Exception(getMensagemInternalizacao("dados_impressao_marcar_ao_menos_uma_opcao"));
            }
            apresentarRelatorioObjetos(prepareParams());
            setMsgAlert("abrirPopupPDFImpressao('relatorio/" + getNomeArquivoRelatorioGeradoAgora() + "','', 780, 595);Richfaces.hideModalPanel('relatorioImprimir');");
            setMensagemDetalhada("", "");
            setMensagemID("msg_entre_prmrelatorio");
        } catch (Exception e) {
            montarErro(e);
            setMsgAlert(getMensagemNotificar());
        }
    }
    // <------------------------ LISTAS ---------------------->

    public List<SelectItem> getListaBvCompIncomp() throws Exception {
        List<SelectItem> objs = new ArrayList<SelectItem>();
        objs.add(new SelectItem("C", "Completo"));
        objs.add(new SelectItem("BP", "Incompleto"));
        return objs;
    }

    public void getListaConsultor() throws Exception {
        consultores = getFacade().getColaborador().consultarPorNomeTipoVinculoPossivel("", TipoColaboradorEnum.CONSULTOR, getFiltroEmpresa(), Uteis.NIVELMONTARDADOS_MINIMOS);
    }

    //<----------------GETTER E SETTER---------------------------------->
    public List<ColaboradorVO> getConsultores() throws Exception {
        if (consultores == null) {
            getListaConsultor();
        }
        return consultores;
    }

    public void setConsultores(List<ColaboradorVO> consultores) {
        this.consultores = consultores;
    }

    public Date getFimBv() {
        return fimBv;
    }

    public void setFimBv(Date fimBv) {
        this.fimBv = fimBv;
    }

    public Date getInicioBv() {
        return inicioBv;
    }

    public void setInicioBv(Date inicioBv) {
        this.inicioBv = inicioBv;
    }

    public ColaboradorVO getConsultor() {
        return consultor;
    }

    public void setConsultor(ColaboradorVO consultor) {
        this.consultor = consultor;
    }

    public FiltrosConsultaRelatorioBVsTO getFiltrosConsulta() {
        return filtrosConsulta;
    }

    public void setFiltrosConsulta(FiltrosConsultaRelatorioBVsTO filtrosConsulta) {
        this.filtrosConsulta = filtrosConsulta;
    }

    public List<ItemRelatorioTO> getResultado() {
        return resultado;
    }

    public void setResultado(List<ItemRelatorioTO> resultado) {
        this.resultado = resultado;
    }

    public TipoColaboradorVO getTipoColaboradorVO() {
        return tipoColaboradorVO;
    }

    public void setTipoColaboradorVO(TipoColaboradorVO tipoColaboradorVO) {
        this.tipoColaboradorVO = tipoColaboradorVO;
    }

    public String getSituacao() {
        return situacao;
    }

    public void setSituacao(String situacao) {
        this.situacao = situacao;
    }

    public Integer getTipoConsulta() {
        return tipoConsulta;
    }

    public void setTipoConsulta(Integer tipoConsulta) {
        this.tipoConsulta = tipoConsulta;
    }

    public String getBvCompIncomp() {
        return bvCompIncomp;
    }

    public void setBvCompIncomp(String bvCompIncomp) {
        this.bvCompIncomp = bvCompIncomp;
    }

    public ItemRelatorioTO getItemRelatorio() {
        return itemRelatorio;
    }

    public void setItemRelatorio(ItemRelatorioTO itemRelatorio) {
        this.itemRelatorio = itemRelatorio;
    }

    public boolean isApresentarLinha1() {
        return apresentarLinha1;
    }

    public void setApresentarLinha1(boolean apresentarLinha1) {
        this.apresentarLinha1 = apresentarLinha1;
    }

    public boolean isApresentarLinha2() {
        return apresentarLinha2;
    }

    public void setApresentarLinha2(boolean apresentarLinha2) {
        this.apresentarLinha2 = apresentarLinha2;
    }

    public boolean isApresentarLinha3() {
        return apresentarLinha3;
    }

    public void setApresentarLinha3(boolean apresentarLinha3) {
        this.apresentarLinha3 = apresentarLinha3;
    }

    public boolean isPermissaoConsultaTodasEmpresas() {
        return permissaoConsultaTodasEmpresas;
    }

    public void setPermissaoConsultaTodasEmpresas(boolean permissaoConsultaTodasEmpresas) {
        this.permissaoConsultaTodasEmpresas = permissaoConsultaTodasEmpresas;
    }

    public Integer getFiltroEmpresa() {
        if (filtroEmpresa == null) {
            filtroEmpresa = 0;
        }
        return filtroEmpresa;
    }

    public void setFiltroEmpresa(Integer filtroEmpresa) {
        this.filtroEmpresa = filtroEmpresa;
    }
}

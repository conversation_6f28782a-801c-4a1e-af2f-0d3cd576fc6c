package controle.basico;

import controle.arquitetura.SuperControle;
import controle.arquitetura.security.AutorizacaoFuncionalidadeControle;
import controle.arquitetura.security.AutorizacaoFuncionalidadeListener;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.contrato.MovProdutoVO;
import negocio.comuns.financeiro.ItemVendaAvulsaVO;
import negocio.comuns.financeiro.VendaAvulsaVO;
import negocio.comuns.plano.ProdutoVO;
import negocio.comuns.utilitarias.Uteis;

import javax.faces.event.ActionEvent;
import javax.faces.model.SelectItem;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class RenovarProdutoControle extends SuperControle {

    private ClienteVO clienteVO = new ClienteVO();
    private MovProdutoVO movProdutoVO = new MovProdutoVO();
    private ProdutoVO produtoEscolhido = new ProdutoVO();
    private List<SelectItem> produtosParaRenovar = new ArrayList<SelectItem>();
    private String onComplete = "";

    public void renovarProduto(ActionEvent evt) throws Exception {
        setMovProdutoVO((MovProdutoVO) evt.getComponent().getAttributes().get("movProdutoVO"));
        setClienteVO((ClienteVO) evt.getComponent().getAttributes().get("clienteVO"));
        setProdutosParaRenovar(new ArrayList<SelectItem>());
        List<ProdutoVO> produtos = getFacade().getProduto().consultarProdutosPorTipoProdutoTipoVigencia(getMovProdutoVO().getProduto().getTipoProduto(), "ID", false, Uteis.NIVELMONTARDADOS_MINIMOS);
        for (ProdutoVO produtoVO : produtos) {
            getProdutosParaRenovar().add(new SelectItem(produtoVO.getCodigo(), produtoVO.getDescricao() + " - " + produtoVO.getValorFinal_Apresentar()));
        }
    }

    public void confirmarLancarProduto() {
        AutorizacaoFuncionalidadeControle auto = (AutorizacaoFuncionalidadeControle) getControlador(AutorizacaoFuncionalidadeControle.class);
        limparMsg();
        AutorizacaoFuncionalidadeListener listener = new AutorizacaoFuncionalidadeListener() {

            @Override
            public void onAutorizacaoComSucesso() {
                setOnComplete("Richfaces.showModalPanel('modalEscolherProdutoRenovar');");
            }

            @Override
            public String getExecutarAoCompletar() {
                return getOnComplete();
            }
        };
        setOnComplete("");
        auto.autorizar("Confirmar Venda Avulsa", "VendaAvulsa",
                "Você precisa da permissão \"Venda Avulsa\"",
                "form:conteinerProdutoValidade", listener);
    }

    public String getOnComplete() {
        return onComplete;
    }

    public void setOnComplete(String onComplete) {
        this.onComplete = onComplete;
    }

    public Object acaoRenovarProduto() throws Exception {
        try {
            setErro(false);
            setSucesso(false);

            setProdutoEscolhido(getFacade().getProduto().consultarPorChavePrimaria(getProdutoEscolhido().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));

            UsuarioVO usuarioResponsavel = ((AutorizacaoFuncionalidadeControle) getControlador(AutorizacaoFuncionalidadeControle.class)).getUsuario();
            // Ao renovar um produto que tem prazo de vigência, deve ser setado na data de compra do novo produto uma data posterior a data final de vigênvia do produto anterior.
            Date dataCompraNovoProduto = Uteis.obterDataFutura2(getMovProdutoVO().getDataFinalVigencia(), 1);
            gerarProduto(getProdutoEscolhido(), getProdutoEscolhido().getValorFinal(), usuarioResponsavel, dataCompraNovoProduto, getEmpresaLogado());

            setSucesso(true);

            ((TelaClienteControle) getControlador(TelaClienteControle.class)).obterListaProdutoComValidadeCliente();

            return true;
        } catch (Exception ex) {
            setErro(true);
            setMensagemDetalhada(ex);
            setSucesso(false);
            return false;
        }
    }

    public VendaAvulsaVO gerarProduto(ProdutoVO produto, Double valor, UsuarioVO responsavel, Date data, EmpresaVO empresaVO) throws Exception {
        VendaAvulsaVO vendaAvulsaVO = new VendaAvulsaVO();
        vendaAvulsaVO.setTipoComprador("CI");
        vendaAvulsaVO.setCliente(getClienteVO());
        vendaAvulsaVO.setDataRegistro(data);
        vendaAvulsaVO.setEmpresa(empresaVO);

        ItemVendaAvulsaVO item = new ItemVendaAvulsaVO();
        item.setDataVenda(data);
        item.setQuantidade(1);
        item.setUsuarioVO(responsavel);
        item.setProduto(produto);
        item.setValorParcial(valor);
        item.getProduto().setValorFinal(valor);
        vendaAvulsaVO.setValorTotal(valor);
        vendaAvulsaVO.setNomeComprador(vendaAvulsaVO.getCliente().getPessoa().getNome());
        vendaAvulsaVO.getItemVendaAvulsaVOs().add(item);
        vendaAvulsaVO.setResponsavel(item.getUsuarioVO());
        vendaAvulsaVO.setDescricaoAdicional("Venda Avulsa");

        getFacade().getVendaAvulsa().incluirSemCommit(vendaAvulsaVO, false, data);
        return vendaAvulsaVO;
    }

    public String getAcaoAjaxRenovarProduto() {
        if (getSucesso()) {
            return "Richfaces.hideModalPanel('modalEscolherProdutoRenovar');";
        }
        return "";
    }

    public ClienteVO getClienteVO() {
        return clienteVO;
    }

    public void setClienteVO(ClienteVO clienteVO) {
        this.clienteVO = clienteVO;
    }

    public MovProdutoVO getMovProdutoVO() {
        return movProdutoVO;
    }

    public void setMovProdutoVO(MovProdutoVO movProdutoVO) {
        this.movProdutoVO = movProdutoVO;
    }

    public List<SelectItem> getProdutosParaRenovar() {
        return produtosParaRenovar;
    }

    public void setProdutosParaRenovar(List<SelectItem> produtosParaRenovar) {
        this.produtosParaRenovar = produtosParaRenovar;
    }

    public ProdutoVO getProdutoEscolhido() {
        return produtoEscolhido;
    }

    public void setProdutoEscolhido(ProdutoVO produtoEscolhido) {
        this.produtoEscolhido = produtoEscolhido;
    }
}

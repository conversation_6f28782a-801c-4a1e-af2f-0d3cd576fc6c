package controle.basico;

import br.com.pactosolucoes.bi.dto.FiltroDTO;
import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.enumeradores.BIEnum;
import br.com.pactosolucoes.estrutura.ItemExportacaoEnum;
import br.com.pactosolucoes.estrutura.exportador.controle.ExportadorListaControle;
import negocio.comuns.basico.ClienteSimplificadoTO;
import negocio.comuns.financeiro.BILtvChurnRateEmpresa;
import negocio.comuns.financeiro.BILtvGraficoChurnRateDTO;
import negocio.comuns.financeiro.BIPlanoContaDTO;
import negocio.comuns.financeiro.BIProdutoDTO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.FacadeManager;
import org.json.JSONArray;
import org.json.JSONObject;
import relatorio.controle.basico.BIControle;

import javax.faces.event.ActionEvent;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

public class LtvControle extends BIControle {
    private double ltv = 0D;
    private double ltvGeral = 0D;
    private double cac = 0D;
    private double churn = 0D;
    private double totalReceita = 0D;
    private double totalDespesa = 0D;
    private double gastoExterno = 0D;
    private double valorTotalProdutos = 0D;
    private double valorTotalServicos = 0D;
    private double valorTotalDespesas = 0D;
    private int quantidadeContratosAtivos = 0;
    private Integer codigoEmpresaSelecionada = 0;
    private String codigosProduto;
    private String codigosPlanoConta;
    private Date inicio;
    private Date fim;
    private boolean produtos = false;
    private boolean servicos = true;

    private boolean configLtvRealizado = false;
    private List<BIPlanoContaDTO> listaPlanoContaDespesaDTO;
    private List<Double> listaValoresDespesas;
    private List<Double> listaValoresProdutos;
    private List<Double> listaValoresServicos;
    private List<BIProdutoDTO> listaProdutos;
    private List<BIProdutoDTO> listaProdutosMarcados;
    private boolean exibirBi;
    private boolean marcarTodasAsDespesas = false;
    private int tempoDuracaoMedioDosContratos = 0;
    private String corChurn;
    private List<BILtvGraficoChurnRateDTO> listaAnoAtual;
    private List<BILtvGraficoChurnRateDTO> listaAnoPassado;
    private List<BILtvGraficoChurnRateDTO> listaAnoRetrasado;
    private int anoAtual;
    private int anoPassado;
    private int anoRetrasado;
    private static final String[] MESES = {"Janeiro", "Fevereiro", "Março", "Abril", "Maio", "Junho", "Julho", "Agosto", "Setembro", "Outubro", "Novembro", "Dezembro"};
    List<BILtvChurnRateEmpresa> listaChurnRatePorEmpresa;
    private double mesAtual = 0.00D;
    private double mesAnterior = 0.00D;
    private double diferencaMesAnterior = 0.00D;
    private String corDiferencaMesAnterior;
    private double seisMeses = 0.00D;
    private double diferencaSeisMeses = 0.00D;
    private String corDiferencaSeisMeses;
    private double dozeMeses = 0.00D;
    private double diferencaDozeMeses = 0.00D;
    private String corDiferencaDozeMeses;
    private Integer empresaConsulta = 0;
    private String nomeEmpresaSelecionada;
    private double mediaJaneiro = 0.00D;
    private double mediaFevereiro = 0.00D;
    private double mediaMarco = 0.00D;
    private double mediaAbril = 0.00D;
    private double mediaMaio = 0.00D;
    private double mediaJunho = 0.00D;
    private double mediaJulho = 0.00D;
    private double mediaAgosto = 0.00D;
    private double mediaSetembro = 0.00D;
    private double mediaOutubro = 0.00D;
    private double mediaNovembro = 0.00D;
    private double mediaDezembro = 0.00D;
    private boolean carregouExibirBi = false;
    private String dadosGrafico = "[]";
    private double totalDespesasMarcadas = 0.00D;

    private List<Integer> planosDeContaSelecionados;

    private String ltvRealizadoTooltipContent = new StringBuilder()
            .append("<div style='text-align: left !important'>")
            .append("<h3><b>LTV Realizado:</b></h3></br>")
            .append("<span style='list-style-type: lower-alpha;'>Esse filtro altera a fórmula de cálculo do LTV:<br /><br /></span>")
            .append("<span style='list-style-type: lower-alpha; font-style: italic;'>LTV = (Valor da soma equivalente aos meses utilizados de todos os contratos / soma dos meses utilizados dos contratos) * Tempo médio de vida dos clientes<br /><br /><br /></span>")
            .append("</hr>")
            .append("<span style='list-style-type: lower-alpha;'><b>Valor da soma equivalente aos meses utilizados de todos os contratos: </b></br> É a receita mensal paga dos contratos ativos no mês analisado;<br /><br /><br /></span>")
            .append("<span style='list-style-type: lower-alpha;'><b>Soma dos meses utilizados dos contratos: </b></br>É a soma dos meses utilizados até o momento dos contratos ativos no mês analisado; <br /><br /><br /></span>")
            .append("<span style='list-style-type: lower-alpha;'><b>Tempo médio de vida dos clientes: </b></br>É a média da Life Time dos clientes ativos envolvidos no cálculo, referente ao período\n" +
                    "utilizado até o momento.\n<br /><br /><br /></span>")
            .append("</div>")
            .toString();

    public LtvControle() {
        buscaProdutos();
        setPermiteConsultarTodasEmpresas(permissao("ConsultarInfoTodasEmpresas"));
    }

    public void initData(){
        setInicio(getInicioMesAtual());
        setFim(getFimMesAtual());
    }
    public void calcularMetricas() {
        try {
            recalcularMetricas(false, true);
        } catch (Exception e) {
            montarErro(e);
        }
    }

    public void recalcularMetricas(String key) {
        try {
            recalcularMetricas(true, key, true);
        } catch (Exception e) {
            montarErro(e);
        }
    }

    public void recalcularMetricas() {
        try {
            recalcularMetricas(true, false);
        } catch (Exception e) {
            montarErro(e);
        }
    }

    public void recalcularMetricas(boolean atualizar, boolean consultarContasNovamente) throws Exception {
        recalcularMetricas(atualizar, null, consultarContasNovamente);
    }

    public void recalcularMetricas(boolean atualizar, String key, boolean consultarContasNovamente) throws Exception {
        boolean consultarTodasEmpresas = JSFUtilities.isJSFContext() ? (getUsuarioLogado().getAdministrador() || isPermiteConsultarTodasEmpresas()) : true;

        JSONObject dados = buscarDadosBIMS(atualizar, key, consultarTodasEmpresas, consultarContasNovamente);
        cac = dados.getDouble("cac");
        churn = dados.getDouble("churn");
        ltv = dados.getDouble("ltv");
        tempoDuracaoMedioDosContratos = dados.getInt("tempoDuracaoMedioDosContratos");
        atualizarGrafico(dados);
    }

    public JSONObject buscarDadosBIMS(boolean atualizar, String key , boolean consultarTodasEmpresas, boolean consultarContasNovamente) throws Exception {
        getIdEmpresaLogadaOuSelecionada();
        if(consultarContasNovamente) {
            buscaTotalDespesas();
        } else {
            calculaTotalDespesasMarcadas();
        }
        FiltroDTO filtroDTO = getFacade().getBiMSService().obterBI(getKey(key), BIEnum.LTV, getFiltroDTO(atualizar, empresaConsulta, consultarTodasEmpresas), atualizar);
        JSONObject dados = new JSONObject(filtroDTO.getJsonDados());
        return  dados;
    }

    private void calcularChurn(Date date) {
        churn = 0.00D;
        try {
            churn = getMediaMesAtualTodasEmpresas(date);
            if (churn <= 3) {
                setCorChurn("#28A24E");
            } else if (churn > 3 && churn <= 8) {
                setCorChurn("#F37021");
            } else {
                setCorChurn("#EF3C34");
            }
        } catch (Exception e) {
        }
    }

    private void validaTodasDespesasMarcadas() {
        if (listaPlanoContaDespesaDTO != null)
            for (BIPlanoContaDTO dto : listaPlanoContaDespesaDTO) {
                if (!dto.getPlanoContaTO().isInsidirLTV()) {
                    setMarcarTodasAsDespesas(false);
                    break;
                }
            }
    }

    private void calcularCAC() {
        calcularChurn(getFim());
        String codigosPlanos = "";
        cac = 0D;
        valorTotalDespesas = 0D;
        try {
            validaTodasDespesasMarcadas();
            for (BIPlanoContaDTO dto : listaPlanoContaDespesaDTO) {
                if (dto.getPlanoContaTO().isInsidirLTV() || isMarcarTodasAsDespesas()) {
                    codigosPlanos += dto.getPlanoContaTO().getCodigo() + ",";
                }
            }

            this.listaValoresDespesas = FacadeManager.getFacade().getFinanceiro().getPlanoConta().consultarValorRateioPorPlanoContaDataVencimentoEEmpresa(removeUltimaVirgula(codigosPlanos), getInicio(), getFim(), codigoEmpresaSelecionada);
            for (double valor : listaValoresDespesas) {
                valorTotalDespesas += valor;
            }
            cac = valorTotalDespesas / quantidadeContratosAtivos;
        } catch (Exception e) {
            cac = 0D;
        }
    }

    private void calcularLTV() {
        ltv = 0D;
        ltvGeral = 0D;
        double meses = 1D;
        try {
            tempoDuracaoMedioContratos();
            consultarValoresProdutos();
            if (servicos) {
                buscaTotalServicos();
            }
            try {
                HashMap<String, Double> map = FacadeManager.getFacade().getFinanceiro().getPlanoConta().valorMedioMensalContratosAtivos(codigoEmpresaSelecionada);
                ltv = map.get("total") - getCac();
                meses = map.get("meses");
            } catch (Exception e) {
                ltv = 0D;
                meses = 1D;
            }
            double mesesConsulta = (double) (getFim().getTime() - getInicio().getTime()) / (1000 * 60 * 60 * 24) / 30;
            if (mesesConsulta > 1 && mesesConsulta < 2) {
                mesesConsulta = 2;
            }
            if (servicos && valorTotalServicos > 0) {
                valorTotalProdutos += valorTotalServicos;
            }
            if (valorTotalProdutos > 0) {
                ltv += (valorTotalProdutos / meses);
            }
        } catch (Exception e) {
        }
    }


    public void carregaListaChurnRatePorEmpresa() {
        try {
            setListaChurnRatePorEmpresa(FacadeManager.getFacade().getFinanceiro().getPlanoConta().churnRatePorEmpresa());
        } catch (Exception e) {
            setListaChurnRatePorEmpresa(listaChurnRatePorEmpresa = new ArrayList<>());
        }
    }

    public void calculaMediaAnoAtual(List<BILtvGraficoChurnRateDTO> lista) {
        try {
            int tamanho = listaChurnRatePorEmpresa.size();
            mediaJaneiro = 0.00D;
            mediaFevereiro = 0.00D;
            mediaMarco = 0.00D;
            mediaAbril = 0.00D;
            mediaMaio = 0.00D;
            mediaJunho = 0.00D;
            mediaJulho = 0.00D;
            mediaAgosto = 0.00D;
            mediaSetembro = 0.00D;
            mediaOutubro = 0.00D;
            mediaNovembro = 0.00D;
            mediaDezembro = 0.00D;
            mesAtual = 0.00D;
            mesAnterior = 0.00D;
            seisMeses = 0.00D;
            dozeMeses = 0.00D;
            for (BILtvChurnRateEmpresa valores : listaChurnRatePorEmpresa) {
                if (empresaConsulta == valores.getCodigoEmpresa()) {
                    if (Calendar.getInstance().get(Calendar.MONTH) == 0) {
                        mesAtual = valores.getJaneiro();
                    } else if (Calendar.getInstance().get(Calendar.MONTH) == 1) {
                        mesAtual = valores.getFevereiro();
                    } else if (Calendar.getInstance().get(Calendar.MONTH) == 2) {
                        mesAtual = valores.getMarco();
                    } else if (Calendar.getInstance().get(Calendar.MONTH) == 3) {
                        mesAtual = valores.getAbril();
                    } else if (Calendar.getInstance().get(Calendar.MONTH) == 4) {
                        mesAtual = valores.getMaio();
                    } else if (Calendar.getInstance().get(Calendar.MONTH) == 5) {
                        mesAtual = valores.getJunho();
                    } else if (Calendar.getInstance().get(Calendar.MONTH) == 6) {
                        mesAtual = valores.getJulho();
                    } else if (Calendar.getInstance().get(Calendar.MONTH) == 7) {
                        mesAtual = valores.getAgosto();
                    } else if (Calendar.getInstance().get(Calendar.MONTH) == 8) {
                        mesAtual = valores.getSetembro();
                    } else if (Calendar.getInstance().get(Calendar.MONTH) == 9) {
                        mesAtual = valores.getOutubro();
                    } else if (Calendar.getInstance().get(Calendar.MONTH) == 10) {
                        mesAtual = valores.getNovembro();
                    } else if (Calendar.getInstance().get(Calendar.MONTH) == 11) {
                        mesAtual = valores.getDezembro();
                    }
                    try {
                        if (Calendar.getInstance().get(Calendar.MONTH) == lista.get(1).getCompetenciaEvasao()) {
                            mesAnterior = lista.get(1).getChurnRate();
                        } else {
                            mesAnterior = lista.get(2).getChurnRate();
                        }
                    } catch (Exception e) {
                    }
                    try {
                        boolean entrar = true;
                        for (int x = 0; x < 12; x++) {
                            if (x < 6 && entrar) {
                                try {
                                    seisMeses += lista.get(x).getChurnRate();
                                    if (x == 5) {
                                        entrar = false;
                                    }
                                } catch (ArrayIndexOutOfBoundsException a) {
                                    entrar = false;
                                }
                            }
                            dozeMeses += lista.get(x).getChurnRate();
                        }
                    } catch (Exception e) {
                    }
                }

                mediaJaneiro += valores.getJaneiro();
                mediaFevereiro += valores.getFevereiro();
                mediaMarco += valores.getMarco();
                mediaAbril += valores.getAbril();
                mediaMaio += valores.getMaio();
                mediaJunho += valores.getJunho();
                mediaJulho += valores.getJulho();
                mediaAgosto += valores.getAgosto();
                mediaSetembro += valores.getSetembro();
                mediaOutubro += valores.getOutubro();
                mediaNovembro += valores.getNovembro();
                mediaDezembro += valores.getDezembro();
            }
            seisMeses /= 6;
            dozeMeses /= 12;
            mediaJaneiro /= tamanho;
            mediaFevereiro /= tamanho;
            mediaMarco /= tamanho;
            mediaAbril /= tamanho;
            mediaMaio /= tamanho;
            mediaJunho /= tamanho;
            mediaJulho /= tamanho;
            mediaAgosto /= tamanho;
            mediaSetembro /= tamanho;
            mediaOutubro /= tamanho;
            mediaNovembro /= tamanho;
            mediaDezembro /= tamanho;
        } catch (Exception e) {
        }
    }

    public double getMediaMesAtualTodasEmpresas(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        switch (calendar.get(Calendar.MONTH)) {
            case 0:
                return mediaJaneiro;
            case 1:
                return mediaFevereiro;
            case 2:
                return mediaMarco;
            case 3:
                return mediaAbril;
            case 4:
                return mediaMaio;
            case 5:
                return mediaJunho;
            case 6:
                return mediaJulho;
            case 7:
                return mediaAgosto;
            case 8:
                return mediaSetembro;
            case 9:
                return mediaOutubro;
            case 10:
                return mediaNovembro;
            case 11:
                return mediaDezembro;
            default:
                return 0.0;
        }
    }

    public List<BILtvGraficoChurnRateDTO> invesrteLista(List<BILtvGraficoChurnRateDTO> lista) {
        List<BILtvGraficoChurnRateDTO> listaInvertida = new ArrayList<BILtvGraficoChurnRateDTO>();
        for (int x = lista.size() - 1; x >= 0; x--) {
            listaInvertida.add(lista.get(x));
        }
        return listaInvertida;
    }


    public void atualizarGrafico(JSONObject jsonObject) {
        setDadosGrafico(obterDadosGrafico(jsonObject));
    }

    private String obterDadosGrafico(JSONObject jsonObject) {
        carregaListaChurnRatePorEmpresa(jsonObject.getJSONArray("dadosDoAnoDasEmpresas"));
        List<BILtvGraficoChurnRateDTO> listaAnoAtual = obterDadosGrafico(jsonObject.getJSONArray("listaAnoAtual"));
        List<BILtvGraficoChurnRateDTO> listaAnoPassado = obterDadosGrafico(jsonObject.getJSONArray("listaAnoPassado"));
        List<BILtvGraficoChurnRateDTO> listaAnoRetrasado = obterDadosGrafico(jsonObject.getJSONArray("listaAnoRetrasado"));
        List<BILtvGraficoChurnRateDTO> listaCompleta = new ArrayList<BILtvGraficoChurnRateDTO>();
        listaCompleta.addAll(invesrteLista(listaAnoAtual));
        listaCompleta.addAll(invesrteLista(listaAnoPassado));
        listaCompleta.addAll(invesrteLista(listaAnoRetrasado));
        calculaMediaAnoAtual(listaCompleta);
        StringBuilder json = new StringBuilder("[");
        for (int anoAtual = 0, anoPassado = 0, anoRetrasado = 0, x = 0; x < 12; x++) {
            json.append("{");
            try {
                if (listaAnoAtual.get(anoAtual).getCompetenciaEvasao() == x + 1) {
                    json.append("\"anoAtual\": " + Uteis.formataDuasCasasDecimaisEPontoCasasMilenio(listaAnoAtual.get(anoAtual).getChurnRate()).replaceAll(",", ".") + ",");
                    anoAtual++;
                } else {
                    json.append("\"anoAtual\": 0.00,");
                }
            } catch (Exception e) {
                json.append("\"anoAtual\": 0.00,");
            }
            json.append("\"mesAno\": \"" + MESES[x] + "\",");
            try {
                if (listaAnoPassado.get(anoPassado).getCompetenciaEvasao() == x + 1) {
                    json.append("\"anoPassado\": " + Uteis.formataDuasCasasDecimaisEPontoCasasMilenio(listaAnoPassado.get(anoPassado).getChurnRate()).replaceAll(",", ".") + ",");
                    anoPassado++;
                } else {
                    json.append("\"anoPassado\": 0.00,");
                }
            } catch (Exception e) {
                json.append("\"anoPassado\": 0.00,");
            }
            try {
                if (listaAnoRetrasado.get(anoRetrasado).getCompetenciaEvasao() == x + 1) {
                    json.append("\"anoRetrasado\": " + Uteis.formataDuasCasasDecimaisEPontoCasasMilenio(listaAnoRetrasado.get(anoRetrasado).getChurnRate()).replaceAll(",", "."));
                    anoRetrasado++;
                } else {
                    json.append("\"anoRetrasado\": 0.00");
                }
            } catch (Exception e) {
                json.append("\"anoRetrasado\": 0.00");
            }
            if (x < 12) {
                json.append("},");
            } else {
                json.append("}");
            }
        }
        int tamanhoJson = json.toString().length();
        if (tamanhoJson > 0 && json.toString().substring(tamanhoJson - 1, tamanhoJson).equals(",")) {
            json = new StringBuilder(json.substring(0, tamanhoJson - 1));
        }
        json.append("]");
        calculaDiferencaMesAnterior();
        calculaDiferencaSeisMeses();
        calculaDiferencaDozeMeses();
        return json.toString();

    }

    public void selecionarEmpresaGrafico() {
        try {
            nomeEmpresaSelecionada = request().getParameter("nomeEmpresaSelecionada");
            try {
                empresaConsulta = Integer.parseInt(request().getParameter("empresaConsulta"));
            } catch (Exception e) {
                try {
                    empresaConsulta = listaChurnRatePorEmpresa.get(0).getCodigoEmpresa();
                    nomeEmpresaSelecionada = listaChurnRatePorEmpresa.get(0).getNomeEmpresa();
                } catch (Exception ex) {
                }
            }
            recalcularMetricas(true, true);
        } catch (Exception e) {
            montarErro(e);
        }
    }

    private List<BILtvGraficoChurnRateDTO> obterDadosGrafico(JSONArray listaJson) {
        List<BILtvGraficoChurnRateDTO> lista = new ArrayList<>();
        if (listaJson == null) {
            return lista;
        }

        for (int i = 0; i < listaJson.length(); i++) {
            lista.add(new BILtvGraficoChurnRateDTO((JSONObject) listaJson.get(i)));
        }

        return lista;
    }

    private void carregaListaChurnRatePorEmpresa(JSONArray dadosDoAnoDasEmpresas) {
        getListaChurnRatePorEmpresa().clear();
        for (int i = 0; i < dadosDoAnoDasEmpresas.length(); i++) {
            getListaChurnRatePorEmpresa().add(new BILtvChurnRateEmpresa((JSONObject) dadosDoAnoDasEmpresas.get(i)));
        }
    }


    public void calculaDiferencaMesAnterior() {
        diferencaMesAnterior = (mesAnterior - mesAtual) * -1;
        corDiferencaMesAnterior = setaCorDiferenca(diferencaMesAnterior);
    }

    public void calculaDiferencaSeisMeses() {
        diferencaSeisMeses = (seisMeses - mesAtual) * -1;
        corDiferencaSeisMeses = setaCorDiferenca(diferencaSeisMeses);
    }

    public void calculaDiferencaDozeMeses() {
        diferencaDozeMeses = (dozeMeses - mesAtual) * -1;
        corDiferencaDozeMeses = setaCorDiferenca(diferencaDozeMeses);
    }

    private String setaCorDiferenca(double valor) {
        if (valor < 0) {
            return "#64AF45";
        } else {
            return "#EF3C34";
        }
    }

    private void consultarValoresProdutos() {
        String codigosProdutos = "";
        valorTotalProdutos = 0D;
        try {
            for (BIProdutoDTO dto : listaProdutosMarcados) {
                if (dto.isMarcado()) {
                    codigosProdutos += dto.getCodigo() + ",";
                }
            }
            this.listaValoresProdutos = FacadeManager.getFacade().getFinanceiro().getPlanoConta().consultarValoresProdutos(removeUltimaVirgula(codigosProdutos), getInicio(), getFim(), codigoEmpresaSelecionada);
            valorTotalProdutos = 0D;
            for (double valor : listaValoresProdutos) {
                valorTotalProdutos += valor;
            }
        } catch (Exception e) {
            valorTotalProdutos = 0D;
        }
    }

    public String getCacClienteIndividual() {
        return Uteis.formataDuasCasasDecimaisEPontoCasasMilenio(getCacClienteIndividualDouble());
    }

    public Double getCacClienteIndividualDouble() {
        calcularChurn(getFim());
        String codigosPlanos = "";
        double cacIndividual = 0D;
        double valorTotalDespesasIndividual = 0D;
        try {
            buscaTotalDespesas();
            for (BIPlanoContaDTO dto : listaPlanoContaDespesaDTO) {
                if (dto.getPlanoContaTO().isInsidirLTV()) {
                    codigosPlanos += dto.getPlanoContaTO().getCodigo() + ",";
                }
            }
            List<Double> lista = FacadeManager.getFacade().getFinanceiro().getPlanoConta().consultarValorRateioPorPlanoContaDataVencimentoEEmpresa(removeUltimaVirgula(codigosPlanos), getInicio(), getFim(), codigoEmpresaSelecionada);
            for (double valor : lista) {
                valorTotalDespesasIndividual += valor;
            }
            cacIndividual = valorTotalDespesasIndividual / quantidadeContratosAtivos;

        } catch (Exception e) {
            cacIndividual = 0.00D;
        }

        return cacIndividual;
    }

    private String removeUltimaVirgula(String texto) {
        if (null != texto && texto.trim().length() > 0) {
            texto = texto.trim();
            if (texto.substring(texto.length() - 1, texto.length()).equalsIgnoreCase(",")) {
                texto = texto.substring(0, texto.length() - 1);
            }
        }
        return texto;
    }

    public void tempoDuracaoMedioContratos() {
        try {
            this.tempoDuracaoMedioDosContratos = FacadeManager.getFacade().getFinanceiro().getPlanoConta().tempoDuracaoMedioContratos(getInicio(), getFim(), codigoEmpresaSelecionada);
        } catch (Exception e) {
        }
    }

    public int getQuantidadeProdutos() {

        return getListaProdutos().size();
    }


    public void getIdEmpresaLogadaOuSelecionada() {
        try {
            if (null != getEmpresaFiltroBI()) {
                codigoEmpresaSelecionada = getEmpresaFiltroBI().getCodigo();
            } else if (null != getEmpresaLogado()) {
                codigoEmpresaSelecionada = getEmpresaLogado().getCodigo();
                if (UteisValidacao.emptyNumber(empresaConsulta)) {
                    empresaConsulta = codigoEmpresaSelecionada;
                }
            } else {
                codigoEmpresaSelecionada = 0;
            }
        } catch (Exception e) {
            codigoEmpresaSelecionada = 0;
        }
    }


    private void quantidadeContratosAtivos() {
        try {
            this.quantidadeContratosAtivos = FacadeManager.getFacade().getFinanceiro().getPlanoConta().quantidadeContratosAtivos(codigoEmpresaSelecionada);
        } catch (Exception e) {
        }
    }

    private void buscaProdutos() {
        try {
            listaProdutosMarcados = listaProdutos;
            listaProdutos = new ArrayList<BIProdutoDTO>();
            this.listaProdutos = FacadeManager.getFacade().getFinanceiro().getPlanoConta().consultarProdutos();
        } catch (Exception e) {
        }
    }


    public void buscaTotalDespesas() {
        try {
            this.listaPlanoContaDespesaDTO = FacadeManager.getFacade().getFinanceiro().getPlanoConta().consultarTodosPlanoContaComValoresPorPeriodoEEmpresa(codigoEmpresaSelecionada, getInicio(), getFim());
            calculaTotalDespesasMarcadas();
        } catch (Exception e) {
        }
    }

    public void calculaTotalDespesasMarcadas() {
        try {
            planosDeContaSelecionados = new ArrayList<>();
            if (listaPlanoContaDespesaDTO != null) {
                totalDespesasMarcadas = 0D;
                for (BIPlanoContaDTO planoContaDTO : listaPlanoContaDespesaDTO) {
                    if (planoContaDTO.getPlanoContaTO().isInsidirLTV()) {
                        totalDespesasMarcadas += planoContaDTO.getValorTotal();
                        planosDeContaSelecionados.add(planoContaDTO.getPlanoContaTO().getCodigo());
                    }
                }
            }
        } catch (Exception e) {
        }
    }

    public void marcarTodasDespesas() {
        for (BIPlanoContaDTO dto : listaPlanoContaDespesaDTO) {
            dto.getPlanoContaTO().setInsidirLTV(marcarTodasAsDespesas);
        }
        calculaTotalDespesasMarcadas();
    }

    public void marcarTodosProdutos() {
        try {
            for (BIProdutoDTO dto : listaProdutos) {
                dto.setMarcado(isProdutos());
            }
        } catch (Exception e) {
        }
    }

    public int getQuantidadeDeDespesas() {
        return getListaPlanoContaDespesaDTO().size();
    }

    private void buscaTotalServicos() {
        try {
            this.listaValoresServicos = FacadeManager.getFacade().getFinanceiro().getPlanoConta().consultarValoresServicos(getInicio(), getFim(), codigoEmpresaSelecionada);
            valorTotalServicos = 0;
            for (Double valor : this.listaValoresServicos) {
                valorTotalServicos += valor;
            }
        } catch (Exception e) {
        }
    }

    public Date getInicio() {
        if (null == inicio) {
            inicio = getInicioMesAtual();
        }
        return inicio;
    }

    public Date getInicioMesAtual() {
        try {
            return Uteis.obterPrimeiroDiaMes(getDataBaseFiltroBI() == null ? Calendario.hoje() : getDataBaseFiltroBI());
        } catch (ParseException e) {
            return new Date();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    public void setInicio(Date inicio) {
        this.inicio = inicio;
    }

    public Date getFim() {
        if (null == fim) {
            fim = getFimMesAtual();
        }
        return fim;
    }

    public Date getFimMesAtual() {
        return Uteis.obterUltimoDiaMes(getDataBaseFiltroBI() == null ? Calendario.hoje() : getDataBaseFiltroBI());
    }

    public void setFim(Date fim) {
        this.fim = fim;
    }


    public double getLtv() {
        return this.ltv;
    }

    public void setLtv(double ltv) {
        this.ltv = ltv;
    }

    public double getCac() {
        return this.cac;
    }

    public void setCac(double cac) {
        this.cac = cac;
    }

    public double getChurn() {
        return churn;
    }

    public void setChurn(double churn) {
        this.churn = churn;
    }


    public double getTotalReceita() {
        return totalReceita;
    }

    public void setTotalReceita(double totalReceita) {
        this.totalReceita = totalReceita;
    }

    public double getTotalDespesa() {
        return totalDespesa;
    }

    public void setTotalDespesa(double totalDespesa) {
        this.totalDespesa = totalDespesa;
    }

    public double getGastoExterno() {
        return gastoExterno;
    }

    public void setGastoExterno(double gastoExterno) {
        this.gastoExterno = gastoExterno;
    }

    public double getValorTotalProdutos() {
        return valorTotalProdutos;
    }

    public void setValorTotalProdutos(double valorTotalProdutos) {
        this.valorTotalProdutos = valorTotalProdutos;
    }

    public double getValorTotalServicos() {
        return valorTotalServicos;
    }

    public void setValorTotalServicos(double valorTotalServicos) {
        this.valorTotalServicos = valorTotalServicos;
    }

    public int getQuantidadeContratosAtivos() {
        return quantidadeContratosAtivos;
    }

    public void setQuantidadeContratosAtivos(int quantidadeContratosAtivos) {
        this.quantidadeContratosAtivos = quantidadeContratosAtivos;
    }

    public Integer getCodigoEmpresaSelecionada() {
        return codigoEmpresaSelecionada;
    }

    public void setCodigoEmpresaSelecionada(Integer codigoEmpresaSelecionada) {
        this.codigoEmpresaSelecionada = codigoEmpresaSelecionada;
    }

    public String getCodigosProduto() {
        return codigosProduto;
    }

    public void setCodigosProduto(String codigosProduto) {
        this.codigosProduto = codigosProduto;
    }

    public String getCodigosPlanoConta() {
        return codigosPlanoConta;
    }

    public void setCodigosPlanoConta(String codigosPlanoConta) {
        this.codigosPlanoConta = codigosPlanoConta;
    }

    public boolean isProdutos() {
        return produtos;
    }

    public void setProdutos(boolean produtos) {
        this.produtos = produtos;
    }

    public boolean isServicos() {
        return servicos;
    }

    public void setServicos(boolean servicos) {
        this.servicos = servicos;
    }

    public boolean isConfigLtvRealizado() {
        return configLtvRealizado;
    }

    public void setConfigLtvRealizado(boolean configLtvRealizado) {
        this.configLtvRealizado = configLtvRealizado;
    }

    public List<BIPlanoContaDTO> getListaPlanoContaDespesaDTO() {
        if (listaPlanoContaDespesaDTO == null)
            listaPlanoContaDespesaDTO = new ArrayList<>();
        return listaPlanoContaDespesaDTO;
    }

    public void setListaPlanoContaDespesaDTO(List<BIPlanoContaDTO> listaPlanoContaDespesaDTO) {
        this.listaPlanoContaDespesaDTO = listaPlanoContaDespesaDTO;
    }

    public List<Double> getListaValoresDespesas() {
        return listaValoresDespesas;
    }

    public void setListaValoresDespesas(List<Double> listaValoresDespesas) {
        this.listaValoresDespesas = listaValoresDespesas;
    }

    public List<Double> getListaValoresProdutos() {
        return listaValoresProdutos;
    }

    public void setListaValoresProdutos(List<Double> listaValoresProdutos) {
        this.listaValoresProdutos = listaValoresProdutos;
    }

    public List<Double> getListaValoresServicos() {
        return listaValoresServicos;
    }

    public void setListaValoresServicos(List<Double> listaValoresServicos) {
        this.listaValoresServicos = listaValoresServicos;
    }

    public List<BIProdutoDTO> getListaProdutos() {
        if (listaProdutos == null)
            listaProdutos = new ArrayList<>();
        return listaProdutos;
    }

    public void setListaProdutos(List<BIProdutoDTO> listaProdutos) {
        this.listaProdutos = listaProdutos;
    }

    public boolean isExibirBi() {
        try {
            if (!carregouExibirBi) {
                exibirBi = getFacade().getFinanceiro().getPlanoConta().exibirBI(BIEnum.LTV.getIndice());
                carregouExibirBi = true;
            }
        } catch (Exception e) {
            exibirBi = false;
        }
        return exibirBi;
    }

    public void setExibirBi(boolean exibirBi) {
        this.exibirBi = exibirBi;
    }

    public boolean isMarcarTodasAsDespesas() {
        return marcarTodasAsDespesas;
    }

    public void setMarcarTodasAsDespesas(boolean marcarTodasAsDespesas) {
        this.marcarTodasAsDespesas = marcarTodasAsDespesas;
    }

    public double getValorTotalDespesas() {
        return valorTotalDespesas;
    }

    public void setValorTotalDespesas(double valorTotalDespesas) {
        this.valorTotalDespesas = valorTotalDespesas;
    }

    public int getTempoDuracaoMedioDosContratos() {
        return tempoDuracaoMedioDosContratos;
    }

    public void setTempoDuracaoMedioDosContratos(int tempoDuracaoMedioDosContratos) {
        this.tempoDuracaoMedioDosContratos = tempoDuracaoMedioDosContratos;
    }

    public List<BIProdutoDTO> getListaProdutosMarcados() {
        return listaProdutosMarcados;
    }

    public void setListaProdutosMarcados(List<BIProdutoDTO> listaProdutosMarcados) {
        this.listaProdutosMarcados = listaProdutosMarcados;
    }

    public String getCorChurn() {
        return corChurn;
    }

    public void setCorChurn(String corChurn) {
        this.corChurn = corChurn;
    }

    public double getLtvGeral() {
        return ltvGeral;
    }

    public void setLtvGeral(double ltvGeral) {
        this.ltvGeral = ltvGeral;
    }

    public List<BILtvGraficoChurnRateDTO> getListaAnoAtual() {
        return listaAnoAtual;
    }

    public void setListaAnoAtual(List<BILtvGraficoChurnRateDTO> listaAnoAtual) {
        this.listaAnoAtual = listaAnoAtual;
    }

    public List<BILtvGraficoChurnRateDTO> getListaAnoPassado() {
        return listaAnoPassado;
    }

    public void setListaAnoPassado(List<BILtvGraficoChurnRateDTO> listaAnoPassado) {
        this.listaAnoPassado = listaAnoPassado;
    }

    public List<BILtvGraficoChurnRateDTO> getListaAnoRetrasado() {
        return listaAnoRetrasado;
    }

    public void setListaAnoRetrasado(List<BILtvGraficoChurnRateDTO> listaAnoRetrasado) {
        this.listaAnoRetrasado = listaAnoRetrasado;
    }

    public int getAnoAtual() {
        this.anoAtual = Calendar.getInstance().get(Calendar.YEAR);
        return anoAtual;
    }

    public void setAnoAtual(int anoAtual) {
        this.anoAtual = anoAtual;
    }

    public int getAnoPassado() {
        this.anoPassado = getAnoAtual() - 1;
        return anoPassado;
    }

    public void setAnoPassado(int anoPassado) {
        this.anoPassado = anoPassado;
    }

    public int getAnoRetrasado() {
        this.anoRetrasado = getAnoAtual() - 2;
        return anoRetrasado;
    }

    public void setAnoRetrasado(int anoRetrasado) {
        this.anoRetrasado = anoRetrasado;
    }

    public List<BILtvChurnRateEmpresa> getListaChurnRatePorEmpresa() {
        if (listaChurnRatePorEmpresa == null) {
            listaChurnRatePorEmpresa = new ArrayList<>();
        }
        return listaChurnRatePorEmpresa;
    }

    public void setListaChurnRatePorEmpresa(List<BILtvChurnRateEmpresa> listaChurnRatePorEmpresa) {
        this.listaChurnRatePorEmpresa = listaChurnRatePorEmpresa;
    }

    public double getMesAnterior() {
        return mesAnterior;
    }

    public void setMesAnterior(double mesAnterior) {
        this.mesAnterior = mesAnterior;
    }

    public double getDiferencaMesAnterior() {
        return diferencaMesAnterior;
    }

    public void setDiferencaMesAnterior(double diferencaMesAnterior) {
        this.diferencaMesAnterior = diferencaMesAnterior;
    }

    public double getSeisMeses() {
        return seisMeses;
    }

    public void setSeisMeses(double seisMeses) {
        this.seisMeses = seisMeses;
    }

    public double getDiferencaSeisMeses() {
        return diferencaSeisMeses;
    }

    public void setDiferencaSeisMeses(double diferencaSeisMeses) {
        this.diferencaSeisMeses = diferencaSeisMeses;
    }

    public double getDozeMeses() {
        return dozeMeses;
    }

    public void setDozeMeses(double dozeMeses) {
        this.dozeMeses = dozeMeses;
    }

    public double getDiferencaDozeMeses() {
        return diferencaDozeMeses;
    }

    public void setDiferencaDozeMeses(double diferencaDozeMeses) {
        this.diferencaDozeMeses = diferencaDozeMeses;
    }

    public Integer getEmpresaConsulta() {
        return empresaConsulta;
    }

    public void setEmpresaConsulta(Integer empresaConsulta) {
        this.empresaConsulta = empresaConsulta;
    }

    public String getNomeEmpresaSelecionada() {
        return nomeEmpresaSelecionada;
    }

    public void setNomeEmpresaSelecionada(String nomeEmpresaSelecionada) {
        this.nomeEmpresaSelecionada = nomeEmpresaSelecionada;
    }

    public double getMediaJaneiro() {
        return mediaJaneiro;
    }

    public void setMediaJaneiro(double mediaJaneiro) {
        this.mediaJaneiro = mediaJaneiro;
    }

    public double getMediaFevereiro() {
        return mediaFevereiro;
    }

    public void setMediaFevereiro(double mediaFevereiro) {
        this.mediaFevereiro = mediaFevereiro;
    }

    public double getMediaMarco() {
        return mediaMarco;
    }

    public void setMediaMarco(double mediaMarco) {
        this.mediaMarco = mediaMarco;
    }

    public double getMediaAbril() {
        return mediaAbril;
    }

    public void setMediaAbril(double mediaAbril) {
        this.mediaAbril = mediaAbril;
    }

    public double getMediaMaio() {
        return mediaMaio;
    }

    public void setMediaMaio(double mediaMaio) {
        this.mediaMaio = mediaMaio;
    }

    public double getMediaJunho() {
        return mediaJunho;
    }

    public void setMediaJunho(double mediaJunho) {
        this.mediaJunho = mediaJunho;
    }

    public double getMediaJulho() {
        return mediaJulho;
    }

    public void setMediaJulho(double mediaJulho) {
        this.mediaJulho = mediaJulho;
    }

    public double getMediaAgosto() {
        return mediaAgosto;
    }

    public void setMediaAgosto(double mediaAgosto) {
        this.mediaAgosto = mediaAgosto;
    }

    public double getMediaSetembro() {
        return mediaSetembro;
    }

    public void setMediaSetembro(double mediaSetembro) {
        this.mediaSetembro = mediaSetembro;
    }

    public double getMediaOutubro() {
        return mediaOutubro;
    }

    public void setMediaOutubro(double mediaOutubro) {
        this.mediaOutubro = mediaOutubro;
    }

    public double getMediaNovembro() {
        return mediaNovembro;
    }

    public void setMediaNovembro(double mediaNovembro) {
        this.mediaNovembro = mediaNovembro;
    }

    public double getMediaDezembro() {
        return mediaDezembro;
    }

    public void setMediaDezembro(double mediaDezembro) {
        this.mediaDezembro = mediaDezembro;
    }

    public String getCorDiferencaMesAnterior() {
        return corDiferencaMesAnterior;
    }

    public void setCorDiferencaMesAnterior(String corDiferencaMesAnterior) {
        this.corDiferencaMesAnterior = corDiferencaMesAnterior;
    }

    public String getCorDiferencaSeisMeses() {
        return corDiferencaSeisMeses;
    }

    public void setCorDiferencaSeisMeses(String corDiferencaSeisMeses) {
        this.corDiferencaSeisMeses = corDiferencaSeisMeses;
    }

    public String getCorDiferencaDozeMeses() {
        return corDiferencaDozeMeses;
    }

    public void setCorDiferencaDozeMeses(String corDiferencaDozeMeses) {
        this.corDiferencaDozeMeses = corDiferencaDozeMeses;
    }

    public double getMesAtual() {
        return mesAtual;
    }

    public void setMesAtual(double mesAtual) {
        this.mesAtual = mesAtual;
    }

    public String getDadosGrafico() {
        return dadosGrafico;
    }

    public void setDadosGrafico(String dadosGrafico) {
        this.dadosGrafico = dadosGrafico;
    }

    public double getTotalDespesasMarcadas() {
        return totalDespesasMarcadas;
    }

    public void setTotalDespesasMarcadas(double totalDespesasMarcadas) {
        this.totalDespesasMarcadas = totalDespesasMarcadas;
    }

    public String getLtvRealizadoTooltipContent() {
        return ltvRealizadoTooltipContent;
    }

    public void setLtvRealizadoTooltipContent(String ltvRealizadoTooltipContent) {
        this.ltvRealizadoTooltipContent = ltvRealizadoTooltipContent;
    }

    private FiltroDTO getFiltroDTO(boolean atualizarAgora, Integer empresaGrafico, boolean consultarTodasEmpresas) throws Exception {
        FiltroDTO filtroDTO = new FiltroDTO();
        filtroDTO.setNome(BIEnum.LTV.name());
        JSONObject filtros = new JSONObject();
        filtros.put("atualizarAgora", atualizarAgora);
        filtros.put("dataInicio", Calendario.getDataComHoraZerada(getInicio()).getTime());
        filtros.put("dataFim", Calendario.getDataComHoraZerada(getFim()).getTime());
        if (UteisValidacao.notEmptyNumber(codigoEmpresaSelecionada)) {
            filtros.put("empresa", codigoEmpresaSelecionada);
        }
        if (UteisValidacao.emptyNumber(empresaGrafico)) {
            if (UteisValidacao.notEmptyNumber(codigoEmpresaSelecionada)) {
                filtros.put("empresaGrafico", codigoEmpresaSelecionada);
//                empresaConsulta = codigoEmpresaSelecionada;
            }

        } else {
            filtros.put("empresaGrafico", empresaGrafico);
        }


        filtros.put("produtos", getListaIdsProdutosSelecionados());
        filtros.put("planoDeContas", planosDeContaSelecionados);
        filtros.put("servicos", servicos);
        filtros.put("configLtvRealizado", configLtvRealizado);
        filtros.put("consultarTodasEmpresas", consultarTodasEmpresas);


        filtroDTO.setFiltros(filtros.toString());
        return filtroDTO;
    }


    private List<Integer> getListaIdsProdutosSelecionados() {
        List<Integer> produtosSelecionados = new ArrayList<>();
        if (UteisValidacao.emptyList(listaProdutos)) {
            return produtosSelecionados;
        }
        for (BIProdutoDTO dto : listaProdutos) {
            if (dto.isMarcado()) {
                produtosSelecionados.add(dto.getCodigo());
            }
        }
        return produtosSelecionados;
    }

    public void exportarListas(ActionEvent evt) throws Exception {
        try {
            limparMsg();
            setMsgAlert("");
            ExportadorListaControle exportadorListaControle = (ExportadorListaControle) JSFUtilities.getFromRequest(ExportadorListaControle.class.getSimpleName());
            String paramsTableFiltrada = context().getExternalContext().getRequestParameterMap().get("paramsTableFiltrada");

            String[] split = paramsTableFiltrada.split(",");
            String campoOrdenacao = split[0].replace("[", "");
            String ordem = split[1];
            String filtro = split[2].replace("''", "");
            filtro = filtro.replace("]", "");
            List listaParaImpressao = getFacade().getContrato().consultarLifeTimeContratosVigentesExportar(isConfigLtvRealizado(), codigoEmpresaSelecionada, getInicio(), campoOrdenacao, ordem, filtro);
            exportadorListaControle.exportar(evt, listaParaImpressao, filtro, ItemExportacaoEnum.BI_LTV_LT);
            if(exportadorListaControle.getErro()){
                throw new Exception(exportadorListaControle.getMensagemDetalhada());
            }
            setMsgAlert(exportadorListaControle.getOperacaoOnComplete());
        } catch (Exception ex) {
            montarErro(ex);
        }
    }

    public void irParaTelaClienteDatatables() {
        try {
            Integer codigoConsulta = Integer.parseInt(request().getParameter("chavePrimaria"));
            if (codigoConsulta == null) {
                throw new Exception("Cliente Não Encontrado.");
            } else {
                ClienteSimplificadoTO obj = new ClienteSimplificadoTO();
                obj.setCodigo(codigoConsulta);

                irParaTelaCliente(getFacade().getCliente().consultarPorCodigoMatricula(obj.getCodigo(),codigoEmpresaSelecionada,Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            }
        } catch (Exception ex) {
            setMensagemDetalhada("msg_erro", ex.getMessage());
        }
    }

    public String getTitleFiltroLtvRealizadoAtivado() {
        return "<div style='text-align: left !important'>"
                + "<h3><b>Lifetime Value (LTV):</b></h3><br />"
                + "<span style='list-style-type: lower-alpha;'>"
                + "O Valor aqui é a soma equivalente aos meses utilizados de todos os contratos, dividido pela soma dos meses utilizados dos mesmos, multiplicado pelo tempo médio de vida dos clientes.<br />"
                + "Calculando de modo geral entre todos os clientes, o tempo de vida útil é de " + getTempoDuracaoMedioDosContratos() + " meses.<br /><br />"
                + "(Dados LTV filtrados por período e empresa).<br /><br /><br />"
                + "<span style='font-weight: bold'>OBS: O filtro de LTV realizado está ativado.</span><br />"
                + "</span></div>";
    }

    public String getTitleFiltroLtvRealizadoDesativado() {
        return "<div style='text-align: left !important'>"
                + "<h3><b>Lifetime Value (LTV):</b></h3>"
                + "<span style='list-style-type: lower-alpha;'>"
                + "O Valor aqui é a soma de todos os contratos ativos no inicio do mês, dividido pela soma do total da duração dos mesmos, multiplicado pelo tempo médio de vida dos clientes.<br />"
                + "Calculando de modo geral entre todos os clientes, o tempo de vida útil é de " + getTempoDuracaoMedioDosContratos() + " meses.<br /><br />"
                + "(Dados LTV filtrados por período e empresa).<br /><br /><br />"
                + "</span></div>";
    }

}

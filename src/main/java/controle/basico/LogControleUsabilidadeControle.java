package controle.basico;
import negocio.interfaces.basico.LogControleUsabilidadeInterfaceFacade;
import java.util.Date;
import negocio.facade.jdbc.basico.LogControleUsabilidade;
import negocio.comuns.utilitarias.*;
import negocio.comuns.basico.*;
import javax.faces.model.SelectItem;
import java.util.List;
import java.util.ArrayList;
import controle.arquitetura.SuperControle;

/**
 * Classe responsável por implementar a interação entre os componentes JSF das páginas 
 * logControleUsabilidadeForm.jsp logControleUsabilidadeCons.jsp) com as funcionalidades da classe <code>LogControleUsabilidade</code>.
 * Implemtação da camada controle (Backing Bean).
 * @see SuperControle
 * @see LogControleUsabilidade
 * @see LogControleUsabilidadeVO
*/
public class LogControleUsabilidadeControle extends SuperControle {
    private LogControleUsabilidadeVO logControleUsabilidadeVO;
    /**
    * Interface <code>LogControleUsabilidadeInterfaceFacade</code> responsável pela interconexão da camada de controle com a camada de negócio.
    * Criando uma independência da camada de controle com relação a tenologia de persistência dos dados (DesignPatter: Façade).
    */

    public LogControleUsabilidadeControle() throws Exception {
        obterUsuarioLogado();
        inicializarFacades();
        setControleConsulta(new ControleConsulta());
        setMensagemID("msg_entre_prmconsulta");
    }

    /**
    * Rotina responsável por disponibilizar um novo objeto da classe <code>LogControleUsabilidade</code>
    * para edição pelo usuário da aplicação.
    */
    public String novo() {
        setLogControleUsabilidadeVO(new LogControleUsabilidadeVO());
        setMensagemID("msg_entre_dados");
        return "editar";
    }

    /**
    * Rotina responsável por disponibilizar os dados de um objeto da classe <code>LogControleUsabilidade</code> para alteração.
    * O objeto desta classe é disponibilizado na session da página (request) para que o JSP correspondente possa disponibilizá-lo para edição.
    */
    public String editar() {
        LogControleUsabilidadeVO obj = (LogControleUsabilidadeVO)context().getExternalContext().getRequestMap().get("logControleUsabilidade");
        obj.setNovoObj(new Boolean(false));
        setLogControleUsabilidadeVO(obj);
        setMensagemID("msg_dados_editar");
        return "editar";
    }

    /**
    * Rotina responsável por gravar no BD os dados editados de um novo objeto da classe <code>LogControleUsabilidade</code>.
    * Caso o objeto seja novo (ainda não gravado no BD) é acionado a operação <code>incluir()</code>. Caso contrário é acionado o <code>alterar()</code>.
    * Se houver alguma inconsistência o objeto não é gravado, sendo re-apresentado para o usuário juntamente com uma mensagem de erro.
    */
    public String gravar() {
        try {
            if (logControleUsabilidadeVO.isNovoObj().booleanValue()) {
                getFacade().getLogControleUsabilidade().incluir(logControleUsabilidadeVO);
            } else {
                getFacade().getLogControleUsabilidade().alterar(logControleUsabilidadeVO);
            }
            setMensagemID("msg_dados_gravados");
            return "editar";
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            return "editar";
        }
    }

    /**
    * Rotina responsavel por executar as consultas disponiveis no JSP LogControleUsabilidadeCons.jsp.
    * Define o tipo de consulta a ser executada, por meio de ComboBox denominado campoConsulta, disponivel neste mesmo JSP.
    * Como resultado, disponibiliza um List com os objetos selecionados na sessao da pagina.
    */
    public String consultar() {
        try {
            super.consultar();
            List objs = new ArrayList();
            if (getControleConsulta().getCampoConsulta().equals("codigo")) {
                if (getControleConsulta().getValorConsulta().equals("")) {
                    getControleConsulta().setValorConsulta("0");
                }
                int valorInt = Integer.parseInt(getControleConsulta().getValorConsulta());
                objs = getFacade().getLogControleUsabilidade().consultarPorCodigo(new Integer(valorInt), true, Uteis.NIVELMONTARDADOS_TODOS);
            }
            if (getControleConsulta().getCampoConsulta().equals("entidade")) {
                objs = getFacade().getLogControleUsabilidade().consultarPorEntidade(getControleConsulta().getValorConsulta(), true, Uteis.NIVELMONTARDADOS_TODOS);
            }
            if (getControleConsulta().getCampoConsulta().equals("maquina")) {
                objs = getFacade().getLogControleUsabilidade().consultarPorMaquina(getControleConsulta().getValorConsulta(), true, Uteis.NIVELMONTARDADOS_TODOS);
            }
            if (getControleConsulta().getCampoConsulta().equals("acao")) {
                objs = getFacade().getLogControleUsabilidade().consultarPorAcao(getControleConsulta().getValorConsulta(), true, Uteis.NIVELMONTARDADOS_TODOS);
            }
            if (getControleConsulta().getCampoConsulta().equals("dataRegistro")) {
                Date valorData = Uteis.getDate(getControleConsulta().getValorConsulta());
                objs = getFacade().getLogControleUsabilidade().consultarPorDataRegistro(Uteis.getDateTime(valorData,0,0,0), Uteis.getDateTime(valorData,23,59,59), true, Uteis.NIVELMONTARDADOS_TODOS);
            }
            if (getControleConsulta().getCampoConsulta().equals("usuario")) {
                if (getControleConsulta().getValorConsulta().equals("")) {
                    getControleConsulta().setValorConsulta("0");
                }
                int valorInt = Integer.parseInt(getControleConsulta().getValorConsulta());
                objs = getFacade().getLogControleUsabilidade().consultarPorUsuario(new Integer(valorInt), true, Uteis.NIVELMONTARDADOS_TODOS);
            }
            if (getControleConsulta().getCampoConsulta().equals("empresa")) {
                if (getControleConsulta().getValorConsulta().equals("")) {
                    getControleConsulta().setValorConsulta("0");
                }
                int valorInt = Integer.parseInt(getControleConsulta().getValorConsulta());
                objs = getFacade().getLogControleUsabilidade().consultarPorEmpresa(new Integer(valorInt), true, Uteis.NIVELMONTARDADOS_TODOS);
            }
            setListaConsulta(objs);
            setMensagemID("msg_dados_consultados");
            return "consultar";
        } catch (Exception e) {
            setListaConsulta(new ArrayList());
            setMensagemDetalhada("msg_erro", e.getMessage());
            return "consultar";
        }
    }

    /**
     * Operação responsável por processar a exclusão um objeto da classe <code>LogControleUsabilidadeVO</code>
     * Após a exclusão ela automaticamente aciona a rotina para uma nova inclusão.
     */
    public String excluir() {
        try {
            getFacade().getLogControleUsabilidade().excluir(logControleUsabilidadeVO);
            novo();
            setMensagemID("msg_dados_excluidos");
            return "editar";
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            return "editar";
        }
    }

    /**
    * Rotina responsável por atribui um javascript com o método de mascara para campos do tipo Data, CPF, CNPJ, etc.
    */
    public String getMascaraConsulta() {
        return "";
    }

    /**
    * Rotina responsável por preencher a combo de consulta da telas.
    */
    public List getTipoConsultaCombo() {
        List itens = new ArrayList();
        itens.add(new SelectItem("codigo", "Código"));
        itens.add(new SelectItem("entidade", "Entidade"));
        itens.add(new SelectItem("maquina", "Máquina"));
        itens.add(new SelectItem("acao", "Ação"));
        itens.add(new SelectItem("dataRegistro", "Data Registro"));
        itens.add(new SelectItem("usuario", "Usuario"));
        itens.add(new SelectItem("empresa", "Empresa"));
        return itens;
    }

    /**
    * Rotina responsável por organizar a paginação entre as páginas resultantes de uma consulta.
    */
    public String inicializarConsultar() {
        setListaConsulta(new ArrayList());
        setMensagemID("msg_entre_prmconsulta");
        return "consultar";
    }

    /**
    * Operação que libera todos os recursos (atributos, listas, objetos) do backing bean.
    * Garantindo uma melhor atuação do Garbage Coletor do Java. A mesma é automaticamente 
    * quando realiza o logout.
    */
    protected void limparRecursosMemoria() {
        super.limparRecursosMemoria();
        logControleUsabilidadeVO = null;

    }

    /**
    * Operação que inicializa as Interfaces Façades com os respectivos objetos de
    * persistência dos dados no banco de dados. 
    */
    protected boolean inicializarFacades() {
        try {
            super.inicializarFacades();

            return true;
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro_conectarBD", e.getMessage());
            return false;
        }
    }

    public LogControleUsabilidadeVO getLogControleUsabilidadeVO() {
        return logControleUsabilidadeVO;
    }
     
    public void setLogControleUsabilidadeVO(LogControleUsabilidadeVO logControleUsabilidadeVO) {
        this.logControleUsabilidadeVO = logControleUsabilidadeVO;
    }
}
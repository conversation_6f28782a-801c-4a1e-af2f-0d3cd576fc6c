/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package controle.basico;

import br.com.pactosolucoes.comuns.notificacao.RecursoSistema;
import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.estrutura.exportador.controle.ExportadorListaControle;
import controle.arquitetura.SuperControle;
import static controle.arquitetura.SuperControle.registrarLogErroObjetoVO;
import controle.arquitetura.security.LoginControle;
import java.util.ArrayList;
import java.util.List;
import javax.faces.event.ActionEvent;
import javax.faces.model.SelectItem;

import negocio.comuns.basico.BrindeTipoEnum;
import negocio.comuns.basico.BrindeVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.plano.PlanoVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;

/**
 *
 * <AUTHOR>
 */
public class BrindeControle extends SuperControle {
    
    private BrindeVO brindeVO;
    private int empresaSelecionada;
    private String oncomplete;
    private List<SelectItem> tipos = new ArrayList<SelectItem>();
    private List<SelectItem> planos = new ArrayList<SelectItem>();
    private String tipo;
    public BrindeControle() throws Exception {
        brindeVO = new BrindeVO();
        obterUsuarioLogado();
        inicializarFacades();
        montarListaEmpresasComItemTodasClubeVantagens();
        empresaSelecionada = getEmpresaLogado().getCodigo();
        setMensagemID("");
    }

    public String novo(){
        try{
            montarDadosEmpresa();
            setBrindeVO(new BrindeVO());
            montarTipos();
            montarPlanos();
            getBrindeVO().setAplicarPontuacaoParaTodosOsPlanos(true);
        }catch (Exception e){
            setErro(true);
            setSucesso(false);
            setMensagemDetalhada("msg_erro", e.getMessage());
            Uteis.logar(e, BrindeControle.class);
        }
        return "editar";
    }

    private void montarDadosEmpresa() throws Exception {
        setEmpresaSelecionada(getEmpresaLogado().getCodigo());
    }

    private void montarPlanos() throws Exception {
        List<PlanoVO> planos = getFacade().getPlano()
                .consultarPorCodigoEmpresa(getEmpresaSelecionada(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);

        setPlanos(new ArrayList<SelectItem>());
        for (PlanoVO plano: planos) {
            getPlanos().add(new SelectItem(
                    plano.getCodigo(),
                    plano.getDescricao()
            ));
        }
    }

    private void montarTipos() {
        setTipos(new ArrayList<SelectItem>());
        for (BrindeTipoEnum tipo: BrindeTipoEnum.values()) {
            tipos.add(new SelectItem(tipo.name(), tipo.getDescricao()));
        }

    }

    public String inicializarConsultar(){
        try {
            setPaginaAtualDeTodas("0/0");
            setListaConsulta(new ArrayList());
            definirVisibilidadeLinksNavegacao(0, 0);
            setMensagemID("msg_entre_prmconsulta");
            setSucesso(false);
            setErro(false);

            montarDadosEmpresa();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "consultar";
    }

    public void gravar(){
        try {
            if (getBrindeVO().isNovoObj()) {
                if (getBrindeVO().getNome().isEmpty()) {
                    throw new Exception("O campo nome deve ser informado.");
                }
                if (getBrindeVO().getCodigoPlanos() != null &&
                        getBrindeVO().getCodigoPlanos().size() == 0 &&
                        !getBrindeVO().isAplicarPontuacaoParaTodosOsPlanos()) {
                    throw new Exception("Você deve escolher pelo menos um plano.");
                }
                if (UteisValidacao.emptyNumber(empresaSelecionada)) {
                    BrindeVO brindeVOTodasEmpresas = (BrindeVO) getBrindeVO().getClone(false);
                    for (SelectItem empresa : getListaEmpresas()) {
                        if (Integer.parseInt(empresa.getValue().toString()) == 0)
                            continue;
                        EmpresaVO empresaVoTodas = new EmpresaVO();
                        empresaVoTodas.setCodigo(Integer.parseInt(empresa.getValue().toString()));
                        brindeVOTodasEmpresas.setEmpresa(empresaVoTodas);
                        getFacade().getBrinde().incluir(brindeVOTodasEmpresas);
                        incluirLogInclusao(brindeVOTodasEmpresas);
                    }
                } else {
                    getBrindeVO().setEmpresa(new EmpresaVO(empresaSelecionada,""));
                    getFacade().getBrinde().incluir(getBrindeVO());
                    incluirLogInclusao(getBrindeVO());
                }
                notificarRecursoEmpresa(RecursoSistema.CADASTRO_BRINDE_POR_INDICACAO);
            }else{
                getFacade().getBrinde().alterar(getBrindeVO());
                incluirLogAlteracao();
            }
            montarSucessoGrowl("Brinde cadastrado com sucesso!");
            setBrindeVO(new BrindeVO());
        } catch (Exception e) {
            montarErro("Falha ao salvar o Brinde:\n" + e.getMessage());
        }
    }

    public String editar(){
        Integer codigoConsulta = Integer.parseInt(request().getParameter("chavePrimaria"));
        try {
            setBrindeVO(getFacade().getBrinde().consultarPorChavePrimaria(codigoConsulta));
            setEmpresaSelecionada(getBrindeVO().getEmpresa().getCodigo());
            getBrindeVO().setNovoObj(false);
            getBrindeVO().registrarObjetoVOAntesDaAlteracao();
            montarTipos();
            montarPlanos();
        } catch (Exception e) {
            setErro(true);
            setSucesso(false);
            setMensagemDetalhada("msg_erro", e.getMessage());
            Uteis.logar(e, BrindeControle.class);
            return "";
        }
        return "editar";
    }
    
    public void validarDadosExclusao(){
        try {
            setOncomplete("");
            if (getBrindeVO().getCodigo() == 0) {
                throw new Exception("Selecione um brinde para a exclusão");
            }
            
            setOncomplete("Richfaces.showModalPanel('mdlAvisoExcluirBrinde')");
        } catch (Exception e) {
            setOncomplete(getMensagemNotificar());
            montarErro(e);
        }
    }
    
    public void excluir(){
        try {
            getFacade().getBrinde().excluir(getBrindeVO());
            incluirLogExclusao();
            empresaSelecionada = getEmpresaLogado().getCodigo();
            setBrindeVO(new BrindeVO());
            
            montarSucessoGrowl("Brinde excluido com Sucesso!");

            novo();
        } catch (Exception e) {
            if(e.getMessage().contains("permissão"))
                montarErro(e.getMessage());
            else
                montarErro("Este brinde não pode ser excluído pois já foi resgatado.");
        }finally {
            setOncomplete("Richfaces.hideModalPanel('mdlAvisoLancamentoBrinde')");
        }
    }

    public void realizarConsultaLogObjetoSelecionado() {
        LoginControle loginControle = (LoginControle) context().getExternalContext().getSessionMap().get("LoginControle");
        String nomeClasse = brindeVO.getClass().getSimpleName();
        nomeClasse = nomeClasse.substring(0, nomeClasse.length() - 2);
        loginControle.setNomeClasse(loginControle.getNomeEntidadeCamposLog("prt_" + nomeClasse + "_tituloForm"));
        loginControle.consultarLogObjetoSelecionado(nomeClasse.toUpperCase(), brindeVO.getCodigo(), 0);
    }
    
    public void incluirLogExclusao() throws Exception{
        try {
            brindeVO.setObjetoVOAntesAlteracao(new BrindeVO());
            brindeVO.setNovoObj(true);
            registrarLogExclusaoTodosDadosObjetoVO(brindeVO,brindeVO.getCodigo(),"BRINDE",0);
        } catch (Exception e) {
            registrarLogErroObjetoVO("BRINDE",brindeVO.getCodigo(),"ERRO AO GERAR LOG DE EXCLUSÃO DO BRINDE",this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
    }

    public void incluirLogInclusao(BrindeVO brindeVO) throws Exception{
        try {
            brindeVO.setObjetoVOAntesAlteracao(new BrindeVO());
            brindeVO.setNovoObj(true);
            registrarLogObjetoVO(brindeVO,brindeVO.getCodigo(),"BRINDE",0);
        } catch (Exception e) {
            registrarLogErroObjetoVO("BRINDE",brindeVO.getCodigo(),"ERRO AO GERAR LOG DE INCLUSÃO DO BRINDE",this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
        brindeVO.setNovoObj(false);
        brindeVO.registrarObjetoVOAntesDaAlteracao();
    }

    public void incluirLogAlteracao() throws Exception {
        try {
            registrarLogObjetoVO(brindeVO,brindeVO.getCodigo(),"BRINDE",0);
        } catch (Exception e) {
            registrarLogErroObjetoVO("BRINDE",brindeVO.getCodigo(),"ERRO AO GERAR LOG DE ALTERAÇÃO DO BRINDE",this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
        brindeVO.registrarObjetoVOAntesDaAlteracao();
    }
    
    public void realizarConsultaLogObjetoGeral() {
       brindeVO = new BrindeVO();
       realizarConsultaLogObjetoSelecionado();
    }
    
    public void exportar(ActionEvent evt) throws Exception{
        ExportadorListaControle exportadorListaControle = (ExportadorListaControle) JSFUtilities.getFromRequest(ExportadorListaControle.class.getSimpleName());
        String paramsTableFiltrada = context().getExternalContext().getRequestParameterMap().get("paramsTableFiltrada");

        String[] split = paramsTableFiltrada.split(",");
        String campoOrdenacao = split[0].replace("[", "").replace("<span>", "").replace("</span>", "");
        String ordem = split[1];
        String filtro = split[2].replace("''", "");
        filtro = filtro.replace("]", "");
        List listaParaImpressao = getFacade().getBrinde().consultarParaImpressao(filtro, ordem, campoOrdenacao, getEmpresaLogado() == null ? 1 : getEmpresaLogado().getCodigo());
        exportadorListaControle.exportar(evt, listaParaImpressao, filtro, null);
    }
    
    public BrindeVO getBrindeVO() {
        return brindeVO;
    }

    public void setBrindeVO(BrindeVO brindeVO) {
        this.brindeVO = brindeVO;
    }

    public int getEmpresaSelecionada() {
        return empresaSelecionada;
    }

    public void setEmpresaSelecionada(int empresaSelecionada) {
        this.empresaSelecionada = empresaSelecionada;
    }

    public String getOncomplete() {
        if (oncomplete == null) {
            oncomplete = "";
        }
        return oncomplete;
    }

    public void setOncomplete(String oncomplete) {
        this.oncomplete = oncomplete;
    }

    public List<SelectItem> getTipos() {
        return tipos;
    }

    public void setTipos(List<SelectItem> tipos) {
        this.tipos = tipos;
    }

    public List<SelectItem> getPlanos() {
        return planos;
    }

    public void setPlanos(List<SelectItem> planos) {
        this.planos = planos;
    }

    public String getTipo() {
        return tipo;
    }

    public void setTipo(String tipo) {
        this.tipo = tipo;
    }
}

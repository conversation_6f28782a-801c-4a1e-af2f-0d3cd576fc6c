package controle.basico;

import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.enumeradores.TipoEnderecoEnum;
import br.com.pactosolucoes.enumeradores.TipoTelefoneEnum;
import br.com.pactosolucoes.estrutura.exportador.controle.ExportadorListaControle;
import controle.arquitetura.SuperControle;
import negocio.comuns.basico.CidadeVO;
import negocio.comuns.basico.ConfiguracaoSistemaVO;
import negocio.comuns.basico.EnderecoVO;
import negocio.comuns.basico.PaisVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.basico.ProfissaoVO;
import negocio.comuns.basico.TelefoneVO;
import negocio.comuns.utilitarias.ControleConsulta;
import negocio.comuns.utilitarias.Dominios;
import negocio.comuns.utilitarias.SelectItemOrdemValor;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.basico.Pessoa;

import javax.faces.event.ActionEvent;
import javax.faces.model.SelectItem;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Enumeration;
import java.util.Hashtable;
import java.util.Iterator;
import java.util.List;

/**
 * Classe responsável por implementar a interação entre os componentes JSF das páginas
 * pessoaForm.jsp pessoaCons.jsp) com as funcionalidades da classe <code>Pessoa</code>.
 * Implemtação da camada controle (Backing Bean).
 * @see SuperControle
 * @see Pessoa
 * @see PessoaVO
*/
public class PessoaControle extends SuperControle {
    private PessoaVO pessoaVO;
    protected List listaSelectItemProfissao;
    protected List listaSelectItemCidade;
    protected List listaSelectItemPais;
    private ConfiguracaoSistemaVO configuracaoSistema;
    /**
    * Interface <code>PessoaInterfaceFacade</code> responsável pela interconexão da camada de controle com a camada de negócio.
    * Criando uma independência da camada de controle com relação a tenologia de persistência dos dados (DesignPatter: Façade).
    */

    private EnderecoVO enderecoVO;
    private TelefoneVO telefoneVO;
    public static final String chavePessoaAlterarSenhaAcesso = "chavePessoaSenhaAcesso";

    public PessoaControle() throws Exception {
        setPessoaVO(new PessoaVO());
        obterUsuarioLogado();
        inicializarFacades();
        inicializarConfiguracaoSistema();
        setControleConsulta(new ControleConsulta());
        setMensagemID("");
    }

    public void inicializarConfiguracaoSistema() {
        try {
            setConfiguracaoSistema((ConfiguracaoSistemaVO) JSFUtilities.getFromSession(JSFUtilities.CONFIGURACAO_SISTEMA));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
        /**
    * Rotina responsável por disponibilizar um novo objeto da classe <code>Pessoa</code>
    * para edição pelo usuário da aplicação.
    */
    public String novo() throws Exception {
        setPessoaVO(new PessoaVO());
        setTelefoneVO(new TelefoneVO());
        setEnderecoVO(new EnderecoVO());
        inicializarListasSelectItemTodosComboBox();
        setMensagemID("msg_entre_dados");
        return "editar";
    }

    /**
    * Rotina responsável por disponibilizar os dados de um objeto da classe <code>Pessoa</code> para alteração.
    * O objeto desta classe é disponibilizado na session da página (request) para que o JSP correspondente possa disponibilizá-lo para edição.
    */
    public String editar() {
        PessoaVO obj = (PessoaVO)context().getExternalContext().getRequestMap().get("pessoa");
        inicializarAtributosRelacionados(obj);
        obj.setNovoObj(new Boolean(false));
        setPessoaVO(obj);
        setTelefoneVO(new TelefoneVO());
        setEnderecoVO(new EnderecoVO());
        inicializarListasSelectItemTodosComboBox();
        setMensagemID("msg_dados_editar");
        return "editar";
    }

    /**
    * Método responsável inicializar objetos relacionados a classe <code>PessoaVO</code>.
    * Esta inicialização é necessária por exigência da tecnologia JSF, que não trabalha com valores nulos para estes atributos.
    */
    public void inicializarAtributosRelacionados(PessoaVO obj) {
        if (obj.getProfissao() == null) {
            obj.setProfissao(new ProfissaoVO());
        }
        if (obj.getCidade() == null) {
            obj.setCidade(new CidadeVO());
        }
        if (obj.getPais() == null) {
            obj.setPais(new PaisVO());
        }
    }

    /**
    * Rotina responsável por gravar no BD os dados editados de um novo objeto da classe <code>Pessoa</code>.
    * Caso o objeto seja novo (ainda não gravado no BD) é acionado a operação <code>incluir()</code>. Caso contrário é acionado o <code>alterar()</code>.
    * Se houver alguma inconsistência o objeto não é gravado, sendo re-apresentado para o usuário juntamente com uma mensagem de erro.
    */
    public String gravar() {
        try {
            if (pessoaVO.isNovoObj().booleanValue()) {
                getFacade().getPessoa().incluir(pessoaVO);
            } else {
                getFacade().getPessoa().alterar(pessoaVO);
            }
            setMensagemID("msg_dados_gravados");
            return "editar";
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            return "editar";
        }
    }

    /**
    * Rotina responsavel por executar as consultas disponiveis no JSP PessoaCons.jsp.
    * Define o tipo de consulta a ser executada, por meio de ComboBox denominado campoConsulta, disponivel neste mesmo JSP.
    * Como resultado, disponibiliza um List com os objetos selecionados na sessao da pagina.
    */
    public String consultar() {
        try {
            super.consultar();
            List objs = new ArrayList();
            if (getControleConsulta().getCampoConsulta().equals("codigo")) {
                if (getControleConsulta().getValorConsulta().equals("")) {
                    getControleConsulta().setValorConsulta("0");
                }
                int valorInt = Integer.parseInt(getControleConsulta().getValorConsulta());
                objs = getFacade().getPessoa().consultarPorCodigo(new Integer(valorInt), true, Uteis.NIVELMONTARDADOS_TODOS);
            }
            if (getControleConsulta().getCampoConsulta().equals("tipoPessoa")) {
                objs = getFacade().getPessoa().consultarPorTipoPessoa(getControleConsulta().getValorConsulta(), true, Uteis.NIVELMONTARDADOS_TODOS);
            }
            if (getControleConsulta().getCampoConsulta().equals("descricaoProfissao")) {
                objs = getFacade().getPessoa().consultarPorDescricaoProfissao(getControleConsulta().getValorConsulta(), Uteis.NIVELMONTARDADOS_TODOS);
            }
            if (getControleConsulta().getCampoConsulta().equals("nome")) {
                objs = getFacade().getPessoa().consultarPorNome(getControleConsulta().getValorConsulta(), true, Uteis.NIVELMONTARDADOS_TODOS);
            }
            if (getControleConsulta().getCampoConsulta().equals("cfp")) {
                objs = getFacade().getPessoa().consultarPorCfp(getControleConsulta().getValorConsulta(), true, Uteis.NIVELMONTARDADOS_TODOS);
            }
            if (getControleConsulta().getCampoConsulta().equals("nomeCidade")) {
                objs = getFacade().getPessoa().consultarPorNomeCidade(getControleConsulta().getValorConsulta(), Uteis.NIVELMONTARDADOS_TODOS);
            }
            objs = ControleConsulta.obterSubListPaginaApresentar(objs, controleConsulta);
            definirVisibilidadeLinksNavegacao(controleConsulta.getPaginaAtual(), controleConsulta.getNrTotalPaginas());
            setListaConsulta(objs);
            setMensagemID("msg_dados_consultados");
            return "consultar";
        } catch (Exception e) {
            setListaConsulta(new ArrayList());
            setMensagemDetalhada("msg_erro", e.getMessage());
            return "consultar";
        }
    }

    /**
     * Operação responsável por processar a exclusão um objeto da classe <code>PessoaVO</code>
     * Após a exclusão ela automaticamente aciona a rotina para uma nova inclusão.
     */
    public String excluir() {
        try {
            getFacade().getPessoa().excluir(pessoaVO);
            setPessoaVO( new PessoaVO());

            setTelefoneVO(new TelefoneVO());

            setEnderecoVO(new EnderecoVO());
            setMensagemID("msg_dados_excluidos");
            return "editar";
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            return "editar";
        }
    }






    /* Método responsável por adicionar um novo objeto da classe <code>Telefone</code>
     * para o objeto <code>pessoaVO</code> da classe <code>Pessoa</code>
     */
    public void adicionarTelefone() throws Exception {
        try {
            if (!getPessoaVO().getCodigo().equals(new Integer(0))) {
                telefoneVO.setPessoa(getPessoaVO().getCodigo());
            }
            this.telefoneVO.setUsarSistemaInternacional(configuracaoSistema.isUsarSistemaInternacional());
            getPessoaVO().adicionarObjTelefoneVOs(getTelefoneVO());
            this.setTelefoneVO(new TelefoneVO());
            setMensagemID("msg_dados_adicionados");
  //          return "editar";
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
   //         return "editar";
        }
    }

    /* Método responsável por disponibilizar dados de um objeto da classe <code>Telefone</code>
     * para edição pelo usuário.
     */
    public void editarTelefone() throws Exception {
        TelefoneVO obj = (TelefoneVO)context().getExternalContext().getRequestMap().get("telefone");
        setTelefoneVO(obj);
        //return "editar";
    }

    /* Método responsável por remover um novo objeto da classe <code>Telefone</code>
     * do objeto <code>pessoaVO</code> da classe <code>Pessoa</code>
     */
    public void removerTelefone() throws Exception {
        TelefoneVO obj = (TelefoneVO)context().getExternalContext().getRequestMap().get("telefone");
        getPessoaVO().excluirObjTelefoneVOs(obj.getNumero());
        setMensagemID("msg_dados_excluidos");
    //    return "editar";
    }

    /* Método responsável por adicionar um novo objeto da classe <code>Endereco</code>
     * para o objeto <code>pessoaVO</code> da classe <code>Pessoa</code>
     */
    public void adicionarEndereco() throws Exception {
        try {
            if (!getPessoaVO().getCodigo().equals(new Integer(0))) {
                enderecoVO.setPessoa(getPessoaVO().getCodigo());
            }
            getPessoaVO().adicionarObjEnderecoVOs( getEnderecoVO());
            this.setEnderecoVO(new EnderecoVO());
            setMensagemID("msg_dados_adicionados");
 //           return "editar";
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
  //          return "editar";
        }
    }

    /* Método responsável por disponibilizar dados de um objeto da classe <code>Endereco</code>
     * para edição pelo usuário.
     */
    public void editarEndereco() throws Exception {
        EnderecoVO obj = (EnderecoVO)context().getExternalContext().getRequestMap().get("endereco");
        setEnderecoVO(obj);
 //       return "editar";
    }

    /* Método responsável por remover um novo objeto da classe <code>Endereco</code>
     * do objeto <code>pessoaVO</code> da classe <code>Pessoa</code>
     */
    public void removerEndereco() throws Exception {
        EnderecoVO obj = (EnderecoVO)context().getExternalContext().getRequestMap().get("endereco");
        getPessoaVO().excluirObjEnderecoVOs(obj);
        setMensagemID("msg_dados_excluidos");
  //      return "editar";
    }

    public void irPaginaInicial() throws Exception{
        controleConsulta.setPaginaAtual(1);
        this.consultar();
    }

    public void irPaginaAnterior() throws Exception{
        controleConsulta.setPaginaAtual(controleConsulta.getPaginaAtual() - 1);
        this.consultar();
    }

    public void irPaginaPosterior() throws Exception{
        controleConsulta.setPaginaAtual(controleConsulta.getPaginaAtual() + 1);
        this.consultar();
    }

    public void irPaginaFinal() throws Exception{
        controleConsulta.setPaginaAtual(controleConsulta.getNrTotalPaginas());
        this.consultar();
    }

    /* Método responsável por inicializar List<SelectItem> de valores do
     * ComboBox correspondente ao atributo <code>tipoTelefone</code>
     */
    public List getListaSelectItemTipoTelefoneTelefone() throws Exception {
        List objs = new ArrayList();
        objs.add(new SelectItem("", ""));
        for (TipoTelefoneEnum tipoTelefone : TipoTelefoneEnum.values()) {
            String value = tipoTelefone.getCodigo();
            String label = tipoTelefone.getDescricao();
            objs.add(new SelectItem(value, label));
		}
        SelectItemOrdemValor ordenador = new SelectItemOrdemValor();
        Collections.sort((List) objs, ordenador);
        return objs;
    }

    /* Método responsável por inicializar List<SelectItem> de valores do
     * ComboBox correspondente ao atributo <code>tipoEndereco</code>
     */
    public List getListaSelectItemTipoEnderecoEndereco() throws Exception {
        List objs = new ArrayList();
        objs.add(new SelectItem("", ""));
        for (TipoEnderecoEnum obj : TipoEnderecoEnum.values()) {
            String value = obj.getCodigo();
            String label = obj.getDescricao();
            objs.add(new SelectItem(value, label));
		}
        SelectItemOrdemValor ordenador = new SelectItemOrdemValor();
        Collections.sort((List) objs, ordenador);
        return objs;
    }

    /* Método responsável por inicializar List<SelectItem> de valores do
     * ComboBox correspondente ao atributo <code>sexo</code>
     */
    public List getListaSelectItemSexoPessoa() throws Exception {
        List objs = new ArrayList();
        objs.add(new SelectItem("", ""));
        Hashtable sexos = (Hashtable)Dominios.getSexo();
        Enumeration keys = sexos.keys();
        while (keys.hasMoreElements()) {
            String value = (String)keys.nextElement();
            String label = (String)sexos.get(value);
            objs.add(new SelectItem( value, label));
        }
        SelectItemOrdemValor ordenador = new SelectItemOrdemValor();
        Collections.sort((List) objs, ordenador);
        return objs;
    }

    public List getListaSelectItemGeneroPessoa() throws Exception {
        List objs = new ArrayList();
        objs.add(new SelectItem("", ""));
        Hashtable generos = (Hashtable) Dominios.getGenero();
        Enumeration keys = generos.keys();

        while (keys.hasMoreElements()) {
            String value = (String) keys.nextElement();
            String label = (String) generos.get(value);
            objs.add(new SelectItem(value, label));
        }

        SelectItemOrdemValor ordenador = new SelectItemOrdemValor();
        Collections.sort((List) objs, ordenador);

        return objs;
    }

    /* Método responsável por inicializar List<SelectItem> de valores do
     * ComboBox correspondente ao atributo <code>estadoCivil</code>
     */
    public List getListaSelectItemEstadoCivilPessoa() throws Exception {
        List objs = new ArrayList();
        objs.add(new SelectItem("", ""));
        Hashtable estadoCivils = (Hashtable)Dominios.getEstadoCivil();
        Enumeration keys = estadoCivils.keys();
        while (keys.hasMoreElements()) {
            String value = (String)keys.nextElement();
            String label = (String)estadoCivils.get(value);
            objs.add(new SelectItem( value, label));
        }
        SelectItemOrdemValor ordenador = new SelectItemOrdemValor();
        Collections.sort((List) objs, ordenador);
        return objs;
    }

    /* Método responsável por inicializar List<SelectItem> de valores do
     * ComboBox correspondente ao atributo <code>rgUf</code>
     */
    public List getListaSelectItemRgUfPessoa() throws Exception {
        List objs = new ArrayList();
        objs.add(new SelectItem("", ""));
        Hashtable estados = (Hashtable)Dominios.getEstado();
        Enumeration keys = estados.keys();
        while (keys.hasMoreElements()) {
            String value = (String)keys.nextElement();
            String label = (String)estados.get(value);
            objs.add(new SelectItem( value, label));
        }
        SelectItemOrdemValor ordenador = new SelectItemOrdemValor();
        Collections.sort((List) objs, ordenador);
        return objs;
    }

    /* Método responsável por inicializar List<SelectItem> de valores do
     * ComboBox correspondente ao atributo <code>tipoPessoa</code>
     */
    public List getListaSelectItemTipoPessoaPessoa() throws Exception {
        List objs = new ArrayList();
        objs.add(new SelectItem("", ""));
        Hashtable tipoPessoas = (Hashtable)Dominios.getTipoPessoa();
        Enumeration keys = tipoPessoas.keys();
        while (keys.hasMoreElements()) {
            String value = (String)keys.nextElement();
            String label = (String)tipoPessoas.get(value);
            objs.add(new SelectItem( value, label));
        }
        SelectItemOrdemValor ordenador = new SelectItemOrdemValor();
        Collections.sort((List) objs, ordenador);
        return objs;
    }

    /**
     * Método responsável por gerar uma lista de objetos do tipo <code>SelectItem</code> para preencher
     * o comboBox relativo ao atributo <code>Pais</code>.
    */
    public void montarListaSelectItemPais(String prm) throws Exception {
        List resultadoConsulta = consultarPaisPorNome(prm);
        Iterator i = resultadoConsulta.iterator();
        List objs = new ArrayList();
        objs.add(new SelectItem(new Integer(0), ""));
        while (i.hasNext()) {
            PaisVO obj = (PaisVO)i.next();
            objs.add(new SelectItem(obj.getCodigo(), obj.getNome().toString()));
        }
        setListaSelectItemPais(objs);
    }

    /**
     * Método responsável por atualizar o ComboBox relativo ao atributo <code>Pais</code>.
     * Buscando todos os objetos correspondentes a entidade <code>Pais</code>. Esta rotina não recebe parâmetros para filtragem de dados, isto é
     * importante para a inicialização dos dados da tela para o acionamento por meio requisições Ajax.
    */
    public void montarListaSelectItemPais() {
        try {
            montarListaSelectItemPais("");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * Método responsável por consultar dados da entidade <code><code> e montar o atributo <code>nome</code>
     * Este atributo é uma lista (<code>List</code>) utilizada para definir os valores a serem apresentados no ComboBox correspondente
    */
    public List consultarPaisPorNome(String nomePrm) throws Exception {
        List lista = getFacade().getPais().consultarPorNome(nomePrm, false, Uteis.NIVELMONTARDADOS_TODOS);
        return lista;
    }

    /**
     * Método responsável por gerar uma lista de objetos do tipo <code>SelectItem</code> para preencher
     * o comboBox relativo ao atributo <code>Cidade</code>.
    */
    public void montarListaSelectItemCidade(String prm) throws Exception {
        List resultadoConsulta = consultarCidadePorNome(prm);
        Iterator i = resultadoConsulta.iterator();
        List objs = new ArrayList();
        objs.add(new SelectItem(new Integer(0), ""));
        while (i.hasNext()) {
            CidadeVO obj = (CidadeVO)i.next();
            objs.add(new SelectItem(obj.getCodigo(), obj.getNome().toString()));
        }
        setListaSelectItemCidade(objs);
    }

    /**
     * Método responsável por atualizar o ComboBox relativo ao atributo <code>Cidade</code>.
     * Buscando todos os objetos correspondentes a entidade <code>Cidade</code>. Esta rotina não recebe parâmetros para filtragem de dados, isto é
     * importante para a inicialização dos dados da tela para o acionamento por meio requisições Ajax.
    */
    public void montarListaSelectItemCidade() {
        try {
            montarListaSelectItemCidade("");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * Método responsável por consultar dados da entidade <code><code> e montar o atributo <code>nome</code>
     * Este atributo é uma lista (<code>List</code>) utilizada para definir os valores a serem apresentados no ComboBox correspondente
    */
    public List consultarCidadePorNome(String nomePrm) throws Exception {
        List lista = getFacade().getCidade().consultarPorNome(nomePrm, false, Uteis.NIVELMONTARDADOS_TODOS);
        return lista;
    }

    /**
     * Método responsável por gerar uma lista de objetos do tipo <code>SelectItem</code> para preencher
     * o comboBox relativo ao atributo <code>Profissao</code>.
    */
    public void montarListaSelectItemProfissao(String prm) throws Exception {
        List resultadoConsulta = consultarProfissaoPorDescricao(prm);
        Iterator i = resultadoConsulta.iterator();
        List objs = new ArrayList();
        objs.add(new SelectItem(new Integer(0), ""));
        while (i.hasNext()) {
            ProfissaoVO obj = (ProfissaoVO)i.next();
            objs.add(new SelectItem(obj.getCodigo(), obj.getDescricao().toString()));
        }
        setListaSelectItemProfissao(objs);
    }

    /**
     * Método responsável por atualizar o ComboBox relativo ao atributo <code>Profissao</code>.
     * Buscando todos os objetos correspondentes a entidade <code>Profissao</code>. Esta rotina não recebe parâmetros para filtragem de dados, isto é
     * importante para a inicialização dos dados da tela para o acionamento por meio requisições Ajax.
    */
    public void montarListaSelectItemProfissao() {
        try {
            montarListaSelectItemProfissao("");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * Método responsável por consultar dados da entidade <code><code> e montar o atributo <code>descricao</code>
     * Este atributo é uma lista (<code>List</code>) utilizada para definir os valores a serem apresentados no ComboBox correspondente
    */
    public List consultarProfissaoPorDescricao(String descricaoPrm) throws Exception {
        List lista = getFacade().getProfissao().consultarPorDescricao(descricaoPrm, false, Uteis.NIVELMONTARDADOS_TODOS);
        return lista;
    }

    /**
     * Método responsável por inicializar a lista de valores (<code>SelectItem</code>) para todos os ComboBox's.
    */
    public void inicializarListasSelectItemTodosComboBox() {
        montarListaSelectItemProfissao();
        montarListaSelectItemCidade();
        montarListaSelectItemPais();
    }

    /**
    * Rotina responsável por preencher a combo de consulta da telas.
    */
    public List getTipoConsultaCombo() {
        List itens = new ArrayList();
        itens.add(new SelectItem("codigo", "Código"));
        itens.add(new SelectItem("tipoPessoa", "Tipo Pessoa"));
        itens.add(new SelectItem("descricaoProfissao", "Profissão"));
        itens.add(new SelectItem("nome", "Nome"));
        itens.add(new SelectItem("cfp", "Cfp"));
        itens.add(new SelectItem("nomeCidade", "Cidade"));
        return itens;
    }

    /**
    * Rotina responsável por organizar a paginação entre as páginas resultantes de uma consulta.
    */
    public String inicializarConsultar() {
        setPaginaAtualDeTodas("0/0");
        setListaConsulta(new ArrayList());
        definirVisibilidadeLinksNavegacao(0, 0);
        setMensagemID("msg_entre_prmconsulta");
        return "consultar";
    }

    /**
    * Operação que inicializa as Interfaces Façades com os respectivos objetos de
    * persistência dos dados no banco de dados.
    */
    protected boolean inicializarFacades() {
        try {
            super.inicializarFacades();

            return true;
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro_conectarBD", e.getMessage());
            return false;
        }
    }

    public TelefoneVO getTelefoneVO() {
        if(telefoneVO == null){
            telefoneVO = new TelefoneVO();
        }
        return telefoneVO;
    }
    public void setTelefoneVO(TelefoneVO telefoneVO) {
        this.telefoneVO = telefoneVO;
    }

    public EnderecoVO getEnderecoVO() {
        return enderecoVO;
    }

    public void setEnderecoVO(EnderecoVO enderecoVO) {
        this.enderecoVO = enderecoVO;
    }

    public List getListaSelectItemPais() {
        return (listaSelectItemPais);
    }

    public void setListaSelectItemPais( List listaSelectItemPais ) {
        this.listaSelectItemPais = listaSelectItemPais;
    }

    public List getListaSelectItemCidade() {
        return (listaSelectItemCidade);
    }

    public void setListaSelectItemCidade( List listaSelectItemCidade ) {
        this.listaSelectItemCidade = listaSelectItemCidade;
    }

    public ConfiguracaoSistemaVO getConfiguracaoSistema() {
        return configuracaoSistema;
    }

    public void setConfiguracaoSistema(ConfiguracaoSistemaVO configuracaoSistema) {
        this.configuracaoSistema = configuracaoSistema;
    }

    public List getListaSelectItemProfissao() {
        return (listaSelectItemProfissao);
    }

    public void setListaSelectItemProfissao( List listaSelectItemProfissao ) {
        this.listaSelectItemProfissao = listaSelectItemProfissao;
    }

    public PessoaVO getPessoaVO() {
        return pessoaVO;
    }

    private boolean isLiberarOnzeDigitos(){
        try {
            return getEmpresa().isSenhaAcessoOnzeDigitos();
        }catch (Exception ex){
            return  false;
        }
    }
    public Integer getNumeroDigitosSenhaAcesso(){
        return isLiberarOnzeDigitos() ? 11 : 5;
    }

    public void setPessoaVO(PessoaVO pessoaVO) {
        this.pessoaVO = pessoaVO;
    }
    /*Author: Ulisses
    * data: 02/06/11
     * Objetivo: Definir uma senha de acesso para a pessoa utilizá-la no teclado da catraca,
     *           para realizar o acesso à academia.
    */
     public void alterarSenhaAcesso(){
       setSucesso(false);
       setErro(true);
       PessoaVO objPessoa = (PessoaVO) context().getExternalContext().getSessionMap().get(chavePessoaAlterarSenhaAcesso);
       pessoaVO.setCodigo(objPessoa.getCodigo());
       try{
           pessoaVO.validarSenhaAcesso(isLiberarOnzeDigitos());
           // A senha tem que ser única.
           String senhaEncriptada = Uteis.encriptar(pessoaVO.getSenhaAcesso());
           if (getFacade().getPessoa().senhaAcessoJaUtilizada(0, pessoaVO.getCodigo(), senhaEncriptada)) {
               throw new Exception("Senha não permitida. Informe outra senha.");
           }
           // Se chegou até aqui sem exceção, então não houve restrições na validação. Desta forma, gravar a senha no banco de dados.
           getFacade().getPessoa().alterarSenhaAcesso(pessoaVO.getCodigo(), pessoaVO.getSenhaAcesso());
           setSucesso(true);
           setErro(false);
           setMensagemDetalhada("");
           setMensagem("Senha alterada com sucesso !");

       }catch (Exception e){
         setSucesso(false);
         setErro(true);
         setMensagem(e.getMessage() );

       }

    }
    /*Author: Rafael
       * data: 20/07/2015
        * Objetivo: Excluir senha catraca para que o aluno so tenha a opção de usar biometria.
       */
    public void removerSenhaAcesso(){
        setSucesso(false);
        setErro(true);
        PessoaVO objPessoa = (PessoaVO) context().getExternalContext().getSessionMap().get(chavePessoaAlterarSenhaAcesso);
        pessoaVO.setCodigo(objPessoa.getCodigo());
        try{
           // pessoaVO.validarSenhaAcesso();
            getFacade().getPessoa().liberarRemoverSenhaAcesso(pessoaVO);
            setSucesso(true);
            setErro(false);
            setMensagemDetalhada("");
            if(pessoaVO.getLiberaSenhaAcesso())
            montarMensagemTelaInicial();
            if(!pessoaVO.getLiberaSenhaAcesso())
            setMensagem("Senha excluída e desabilitada com sucesso!" );
        }catch (Exception e){
            setSucesso(false);
            setErro(true);
            setMensagem(e.getMessage() );

        }

    }
    public void montarMensagemTelaInicial(){
        if(pessoaVO.getLiberaSenhaAcesso()){
            setMensagem("Informe uma senha de "+(isLiberarOnzeDigitos() ? 11 : 5)+" dígitos (somente números), para ser usada na catraca.");
        } else {
            setMensagem("Habilite o uso da senha para essa pessoa!");
        }
    }

    public void exportar(ActionEvent evt) throws Exception {
        ExportadorListaControle exportadorListaControle = (ExportadorListaControle) JSFUtilities.getFromRequest(ExportadorListaControle.class.getSimpleName());
        String paramsTableFiltrada = context().getExternalContext().getRequestParameterMap().get("paramsTableFiltrada");

        String[] split = paramsTableFiltrada.split(",");
        String campoOrdenacao = split[0].replace("[", "");
        String ordem = split[1];
        String filtro = split[2].replace("''", "");
        filtro = filtro.replace("]", "");
        List listaParaImpressao = getFacade().getPessoa().consultarParaImpressao(filtro, ordem, campoOrdenacao, getEmpresaLogado() == null ? 0 : getEmpresaLogado().getCodigo());
        exportadorListaControle.exportar(evt, listaParaImpressao, filtro, null );

    }

}

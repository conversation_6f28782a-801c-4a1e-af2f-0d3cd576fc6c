package controle.basico;

import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.estrutura.exportador.controle.ExportadorListaControle;
import controle.arquitetura.SuperControle;
import controle.arquitetura.security.LoginControle;
import controle.basico.clube.MensagemGenericaControle;
import negocio.comuns.basico.DepartamentoVO;
import negocio.comuns.utilitarias.Uteis;

import javax.faces.event.ActionEvent;
import java.util.List;

public class DepartamentoControle extends SuperControle {

    private DepartamentoVO departamentoVO;
    private Integer codigoEditar = 0;
    private String msgAlert;

    public DepartamentoControle() throws Exception {
        obterUsuarioLogado();
        inicializarFacades();
        setMensagemID("");
    }


    public String inicializarConsultar() {
        limparMsg();
        return "consultar";
    }
    /**
     * Rotina responsável por disponibilizar um novo objeto da classe <code>Departamento</code>
     * para edição pelo usuário da aplicação.
     */
    public String novo() {
        try {
            prepararFormulario();
            setDepartamentoVO(new DepartamentoVO());
            limparMsg();
        } catch (Exception e) {
            setSucesso(false);
            setErro(true);
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
        return "editar";
    }

    private void prepararFormulario() throws Exception {
        montarListaEmpresas();
    }

    public String editar() {
        try {
            prepararFormulario();
            Integer codigoConsulta = Integer.parseInt(request().getParameter("chavePrimaria"));
            DepartamentoVO obj = getFacade().getDepartamento().consultarPorChavePrimaria(codigoConsulta, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            obj.setNovoObj(false);
            obj.registrarObjetoVOAntesDaAlteracao();
            setDepartamentoVO(obj);
            limparMsg();
        } catch (Exception e) {
            setErro(true);
            setSucesso(false);
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
        return "editar";
    }

    public String gravar() {
        try {
            if (!getUsuarioLogado().getAdministrador()) {
                departamentoVO.getEmpresaVO().setCodigo(getEmpresaLogado().getCodigo());
            }

            if (departamentoVO.isNovoObj()) {
                getFacade().getDepartamento().incluir(departamentoVO);
                incluirLogInclusao("DEPARTAMENTO");
            } else {
                getFacade().getDepartamento().alterar(departamentoVO);
                incluirLogAlteracao("DEPARTAMENTO");
            }

            setMensagemID("msg_dados_gravados");
            setSucesso(true);
            setErro(false);
            return "editar";
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
            return "editar";
        }
    }

    public void incluirLogInclusao(String nome) throws Exception {
        try {
            departamentoVO.setObjetoVOAntesAlteracao(new DepartamentoVO());
            departamentoVO.setNovoObj(true);
            registrarLogObjetoVO(departamentoVO, departamentoVO.getCodigo(), nome, 0);
        } catch (Exception e) {
            registrarLogErroObjetoVO(nome, departamentoVO.getCodigo(), "ERRO AO GERAR LOG DE INCLUSÃO DE " + nome, this.getUsuarioLogado().getNome(), this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
        departamentoVO.setNovoObj(false);
        departamentoVO.registrarObjetoVOAntesDaAlteracao();
    }
    
    public void incluirLogExclusao(String nome) throws Exception {
        try {
            departamentoVO.setObjetoVOAntesAlteracao(new DepartamentoVO());
            departamentoVO.setNovoObj(true);
            registrarLogExclusaoTodosDadosObjetoVO(departamentoVO, departamentoVO.getCodigo(), nome, 0);
        } catch (Exception e) {
            registrarLogErroObjetoVO(nome, departamentoVO.getCodigo(), "ERRO AO GERAR LOG DE EXCLUSÃO DE " + nome, this.getUsuarioLogado().getNome(), this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
    }

    public void incluirLogAlteracao(String nome) throws Exception {
        try {
            registrarLogObjetoVO(departamentoVO, departamentoVO.getCodigo(), nome, 0);
        } catch (Exception e) {
            registrarLogErroObjetoVO(nome, departamentoVO.getCodigo(), "ERRO AO GERAR LOG DE ALTERAÇÃO DE " + nome, this.getUsuarioLogado().getNome(), this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
        departamentoVO.registrarObjetoVOAntesDaAlteracao();
    }

    public String excluir() {
        try {
            getFacade().getDepartamento().excluir(departamentoVO);
            incluirLogExclusao("DEPARTAMENTO");
            setDepartamentoVO(new DepartamentoVO());
            setMensagemID("msg_dados_excluidos");
            setSucesso(true);
            setErro(false);
            return "consultar";
        } catch (Exception e) {
//            setMensagemDetalhada("msg_erro", e.getMessage());
            if (e.getMessage().contains("ERRO: atualização ou exclusão em tabela \"departamento\" viola restrição de chave estrangeira")){
                setMensagemDetalhada("Este departamento não pode ser excluído, pois está sendo utilizado!");
            }else {
                setMensagemDetalhada("msg_erro", e.getMessage());
            }
            setSucesso(false);
            setErro(true);
            return "editar";
        }
    }

    protected boolean inicializarFacades() {
        try {
            super.inicializarFacades();
            return true;
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro_conectarBD", e.getMessage());
            return false;
        }
    }

    public DepartamentoVO getDepartamentoVO() {
        return departamentoVO;
    }

    public void setDepartamentoVO(DepartamentoVO departamentoVO) {
        this.departamentoVO = departamentoVO;
    }

    public Integer getCodigoEditar() {
        return codigoEditar;
    }

    public void setCodigoEditar(Integer codigoEditar) {
        this.codigoEditar = codigoEditar;
    }

    public void exportar(ActionEvent evt) throws Exception {
        ExportadorListaControle exportadorListaControle = (ExportadorListaControle) JSFUtilities.getFromRequest(ExportadorListaControle.class.getSimpleName());
        String paramsTableFiltrada = context().getExternalContext().getRequestParameterMap().get("paramsTableFiltrada");

        String[] split = paramsTableFiltrada.split(",");
        String campoOrdenacao = split[0].replace("[", "");
        String ordem = split[1];
        String filtro = split[2].replace("''", "");
        filtro = filtro.replace("]", "");
        List listaParaImpressao = getFacade().getDepartamento().consultarParaImpressao(filtro, ordem, campoOrdenacao, getEmpresaLogado() == null ? 0 : getEmpresaLogado().getCodigo());
        exportadorListaControle.exportar(evt, listaParaImpressao, filtro, null);
    }
    
     @SuppressWarnings("unchecked")
    public void realizarConsultaLogObjetoSelecionado() {
        LoginControle loginControle = (LoginControle) context().getExternalContext().getSessionMap().get("LoginControle");
        loginControle.setNomeClasse("Departamento");
        loginControle.consultarLogObjetoSelecionado("DEPARTAMENTO", departamentoVO.getCodigo(), null);
    }
    @SuppressWarnings("unchecked")
    public void realizarConsultaLogObjetoGeral() {
      departamentoVO = new DepartamentoVO();
      realizarConsultaLogObjetoSelecionado();
    }

    public void setMsgAlert(String msgAlert) {
        this.msgAlert = msgAlert;
    }

    public String getMsgAlert() {
        if (msgAlert == null) {
            return "";
        }
        return msgAlert;
    }


    public void confirmarExcluir(){
        MensagemGenericaControle control = (MensagemGenericaControle) getControlador(MensagemGenericaControle.class);
        control.setMensagemDetalhada("", "");
        setMsgAlert("Richfaces.showModalPanel('mdlMensagemGenerica');");
        control.init("Exclusão de Departamento",
                "Deseja excluir o Departamento?",
                this, "excluir", "", "", "", "grupoBtnExcluir");
    }

}

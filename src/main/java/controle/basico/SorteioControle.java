package controle.basico;

import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.enumeradores.SituacaoClienteEnum;
import controle.arquitetura.SuperControle;
import negocio.comuns.arquitetura.LogVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.ConfiguracaoSorteioVO;
import negocio.comuns.basico.ObservacaoOperacaoVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.basico.SorteioVO;
import negocio.comuns.basico.enumerador.TipoObservacaoOperacaoEnum;
import negocio.comuns.crm.GenericoTO;
import negocio.comuns.financeiro.MovParcelaVO;
import negocio.comuns.plano.PlanoVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;

import java.io.OutputStream;
import java.sql.Connection;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.List;

/**
 * Created by GlaucoT on 15/02/2016
 */
public class SorteioControle extends SuperControle {

    private ClienteVO clienteVO;
    private PessoaVO pessoaVO;
    private List<MovParcelaVO> parcelaVOs;
    private boolean sorteioValidado = false;

    private List<GenericoTO> situacoesCliente;
    private List<PlanoVO> planos;
    private ConfiguracaoSorteioVO configuracaoSorteioVO;

    public void abrirPopup() {
        try {
            setMsgAlert("");
            limparMsg();
            setMsgAlert("abrirPopup('" + request().getContextPath() + "/faces/sorteioCons.jsp', 'Sorteio', 1000, 650);");
        } catch (Exception e) {
            setMensagemDetalhada(e);
            montarMsgAlert(getMensagemDetalhada());
        }
    }

    public Object prepararConfiguracoes() {
        try {
            setMensagemDetalhada("", "");
            montarListaPlanos();
            montarListaSituacoesCliente();

            setConfiguracaoSorteioVO(getFacade().getConfiguracaoSorteio().obterConfiguracao(getEmpresaLogado().getCodigo()));
            prepararTelaConfiguracoes();
        } catch (Exception ex) {
            setMensagemDetalhada(ex);
        }
        return "configurar";
    }

    private void prepararTelaConfiguracoes() {
        for (PlanoVO planoVO : getPlanos()) {
            for (PlanoVO planoConfigurado : getConfiguracaoSorteioVO().getPlanoVOs()) {
                if (planoConfigurado.getCodigo().equals(planoVO.getCodigo())) {
                    planoVO.setSelecionado(true);
                }
            }
        }

        for (GenericoTO situacaoCliente : getSituacoesCliente()) {
            for (GenericoTO situacaoConfigurada : getConfiguracaoSorteioVO().getSituacoesCliente()) {
                if (situacaoConfigurada.getCodigoString().equals(situacaoCliente.getCodigoString())) {
                    situacaoCliente.setSelecionado(true);
                }
            }
        }
    }

    public void gravarConfiguracao() {
        try {
            configuracaoSorteioVO.setPlanoVOs(planos);
            configuracaoSorteioVO.setSituacoesCliente(situacoesCliente);
            configuracaoSorteioVO.setEmpresaVO(getEmpresaLogado());

            if (getConfiguracaoSorteioVO().isNovoObj()) {
                getFacade().getConfiguracaoSorteio().incluir(configuracaoSorteioVO);
            } else {
                getFacade().getConfiguracaoSorteio().alterar(configuracaoSorteioVO);
            }

            setMensagemID("msg_dados_gravados");
        } catch (Exception ex) {
            setMensagemDetalhada(ex);
        }
    }

    private void montarListaSituacoesCliente() {
        try {
            List<GenericoTO> situacoesCliente = new ArrayList<GenericoTO>();
            for (SituacaoClienteEnum situacaoEnum : SituacaoClienteEnum.values()) {
                if (situacaoEnum.isSituacaoCliente()) {
                    GenericoTO situacao = new GenericoTO(situacaoEnum.getCodigo(), situacaoEnum.getDescricao());
                    situacoesCliente.add(situacao);
                }
            }
            setSituacoesCliente(situacoesCliente);
        } catch (Exception ex) {
            setMensagemDetalhada(ex);
        }
    }

    private void montarListaPlanos() {
        try {
            List<PlanoVO> planos = getFacade().getPlano().consultarPorCodigo(0, getEmpresaLogado().getCodigo(), false, Uteis.NIVELMONTARDADOS_MINIMOS);
            setPlanos(planos);
        } catch (Exception ex) {
            setMensagemDetalhada(ex);
        }
    }

    public void recarregarFoto() throws Exception {
        JSFUtilities.getResponse().addHeader("Expires", Calendario.anterior(Calendar.DATE, Calendario.hoje()).toString());
        JSFUtilities.getResponse().addDateHeader("Last-Modified", new Date().getTime());
        JSFUtilities.getResponse().addHeader("Cache-Control", "no-store, no-cache, must-revalidate, max-age=0, post-check=0, pre-check=0");
        JSFUtilities.getResponse().addHeader("Pragma", "no-cache");
        if (isFotosNaNuvem()) {
            final String fotoKey = getFacade().getPessoa().obterFotoKey(getPessoaVO().getCodigo());
            if (!UteisValidacao.emptyString(fotoKey)) {
                getClienteVO().getPessoa().setFotoKey(fotoKey + "?time=" + getTimeStamp());
            }
        } else {
            getClienteVO().getPessoa().setFoto(getFacade().getPessoa().obterFoto(getKey(), getClienteVO().getPessoa().getCodigo()));
        }
    }

    public void paintFoto(OutputStream out, Object data) throws Exception {
        if (getClienteVO().getPessoa().getFoto() == null || getClienteVO().getPessoa().getFoto().length == 0) {
            recarregarFoto();
        }
        SuperControle.paintFoto(out, getClienteVO().getPessoa().getFoto());
    }

    public Object sortearCliente() {
        try {
            setSorteioValidado(false);
            setConfiguracaoSorteioVO(getFacade().getConfiguracaoSorteio().obterConfiguracao(getEmpresaLogado().getCodigo()));

            ClienteVO clienteSorteado = getFacade().getCliente().sortearCliente(getConfiguracaoSorteioVO(), Uteis.NIVELMONTARDADOS_TODOS);
            setClienteVO(clienteSorteado);
            setPessoaVO(clienteSorteado.getPessoa());

            List<MovParcelaVO> parcelas = getFacade().getMovParcela().consultarPorCodigoPessoaSituacao(getPessoaVO().getCodigo(), "", null, Uteis.NIVELMONTARDADOS_DADOSBASICOS,false, null);
            setParcelaVOs(parcelas);
            Collections.reverse(getParcelaVOs());

            return "novo";
        } catch (Exception e) {
            Uteis.logar(e, this.getClass());
        }
        return "";
    }

    public void validarResultado() throws Exception {
        List<MovParcelaVO> parcelas = new ArrayList<MovParcelaVO>();

        for (MovParcelaVO parcela : getParcelaVOs()) {
            if (parcela.getParcelaEscolhida()) {
                parcelas.add(parcela);
            }
        }
        Connection con = getFacade().getZWFacade().getCon();
        try {
            con.setAutoCommit(false);
            if (!parcelas.isEmpty()) {
                cancelarProdutosVinculados(parcelas);
                for (MovParcelaVO parcela : parcelas) {
                    List<LogVO> listaLog = new ArrayList<LogVO>();
                    listaLog.add(parcela.gerarlogCancelamentoParcela(getUsuarioLogado()));
                    for (LogVO logs : listaLog) {
                        logs.setOperacao("SORTEIO");
                    }
                    parcela.setSituacao("CA");
                    parcela.setJustificativaCancelamento("CLIENTE VENCEDOR DE SORTEIO");
                    getFacade().getMovParcela().alterarSomenteSituacaoSemCommit(parcela);
                    getFacade().getClienteMensagem().excluirClienteMensagemPorMovParcela(parcela.getCodigo());
                    try {
                        SuperControle.registrarLogObjetoVO(listaLog, parcela.getPessoa().getCodigo());
                    } catch (Exception e) {
                        SuperControle.registrarLogErroObjetoVO("CANCELAMENTO PARCELA", parcela.getPessoa().getCodigo(), "ERRO AO GERAR LOG DE CANCELAMENTO DE PARCELA", getUsuarioLogado().getNome(), getUsuarioLogado().getUserOamd());
                        e.printStackTrace();
                    }
                }
            }

            SorteioVO sorteioVO = new SorteioVO();
            sorteioVO.setClienteVO(getClienteVO());
            sorteioVO.setDataSorteio(Calendario.hoje());
            StringBuilder observacoes = new StringBuilder();
            if (parcelas.size() == 0) {
                observacoes.append("Nenhuma parcela foi alterada.");
            } else {
                if (parcelas.size() == 1) {
                    observacoes.append("1 parcela foi alterada.");
                } else {
                    observacoes.append(parcelas.size() + " parcelas foram alteradas.");
                }
            }
            observacoes.append("\n");
            observacoes.append("Regras do Sorteio:\n").append(configuracaoSorteioVO.getRegras());
            sorteioVO.setObservacoes(observacoes.toString());
            sorteioVO.setUsuarioVO(getUsuarioLogado());

            getFacade().getSorteio().incluir(sorteioVO);

            setSorteioValidado(true);
            con.commit();
        } catch (Exception ex) {
            setSorteioValidado(false);
            con.rollback();
        } finally {
            con.setAutoCommit(true);
        }
    }

    private void cancelarProdutosVinculados(List<MovParcelaVO> parcelas) throws Exception {
        getFacade().getMovParcela().cancelarProdutosVinculados(parcelas);
    }

    public ClienteVO getClienteVO() {
        if (clienteVO == null) {
            clienteVO = new ClienteVO();
        }
        return clienteVO;
    }

    public void setClienteVO(ClienteVO clienteVO) {
        this.clienteVO = clienteVO;
    }

    public PessoaVO getPessoaVO() {
        if (pessoaVO == null) {
            pessoaVO = new PessoaVO();
        }
        return pessoaVO;
    }

    public void setPessoaVO(PessoaVO pessoaVO) {
        this.pessoaVO = pessoaVO;
    }

    public List<MovParcelaVO> getParcelaVOs() {
        if (parcelaVOs == null) {
            parcelaVOs = new ArrayList<MovParcelaVO>();
        }
        return parcelaVOs;
    }

    public void setParcelaVOs(List<MovParcelaVO> parcelaVOs) {
        this.parcelaVOs = parcelaVOs;
    }

    public boolean isSorteioValidado() {
        return sorteioValidado;
    }

    public void setSorteioValidado(boolean sorteioValidado) {
        this.sorteioValidado = sorteioValidado;
    }

    public List<GenericoTO> getSituacoesCliente() {
        if (situacoesCliente == null) {
            situacoesCliente = new ArrayList<GenericoTO>();
        }
        return situacoesCliente;
    }

    public void setSituacoesCliente(List<GenericoTO> situacoesCliente) {
        this.situacoesCliente = situacoesCliente;
    }

    public List<PlanoVO> getPlanos() {
        if (planos == null) {
            planos = new ArrayList<PlanoVO>();
        }
        return planos;
    }

    public void setPlanos(List<PlanoVO> planos) {
        this.planos = planos;
    }

    public ConfiguracaoSorteioVO getConfiguracaoSorteioVO() {
        return configuracaoSorteioVO;
    }

    public void setConfiguracaoSorteioVO(ConfiguracaoSorteioVO configuracaoSorteioVO) {
        this.configuracaoSorteioVO = configuracaoSorteioVO;
    }

    public String getPaintFotoDaNuvem() {
        return getPaintFotoDaNuvem(getClienteVO().getPessoa().getFotoKey());
    }
}

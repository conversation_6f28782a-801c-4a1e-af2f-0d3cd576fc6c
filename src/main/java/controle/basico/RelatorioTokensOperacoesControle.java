package controle.basico;

import controle.arquitetura.SuperControle;
import negocio.comuns.financeiro.ObjetoGenerico;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.tokenOperacao.TokenOperacao;
import negocio.tokenOperacao.TokenOperacaoVO;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 * User: <PERSON>
 * Date: 14/11/2023
 */

public class RelatorioTokensOperacoesControle extends SuperControle {


    private List<TokenOperacaoVO> tokensOperacoesVOS;
    private Date dataInicial;
    private Date dataFinal;
    private TokenOperacao tokenOperacaoDAO;
    private String mensagemResultado;

    private TokenOperacaoVO tokenOperacaoVOSelecionado;
    private List<ObjetoGenerico> listaParametrosSelecionado = new ArrayList();
    private boolean exibirModalParametros = false;
    private boolean exibirPix = false;

    public void consultarTokens() {
        try {
            tokensOperacoesVOS = getTokenOperacaoDAO().consultarPorPeriodo(dataInicial, dataFinal);
            if (tokensOperacoesVOS.size() == 0) {
                mensagemResultado = "Não existem Tokens gerados no período informado.";
            } else {
                mensagemResultado = null;
            }
        } catch (Exception e) {
            Uteis.logar("Falha ao consultar relatório de Tokens");
            e.printStackTrace();
        }
    }

    public TokenOperacao getTokenOperacaoDAO() throws Exception {
        if (tokenOperacaoDAO == null) {
            tokenOperacaoDAO = new TokenOperacao();
        }
        return tokenOperacaoDAO;
    }

    public void setTokenOperacaoDAO(TokenOperacao tokenOperacaoDAO) {
        this.tokenOperacaoDAO = tokenOperacaoDAO;
    }

    public List<TokenOperacaoVO> getTokensOperacoesVOS() {
        return tokensOperacoesVOS;
    }

    public void setTokensOperacoesVOS(List<TokenOperacaoVO> tokensOperacoesVOS) {
        this.tokensOperacoesVOS = tokensOperacoesVOS;
    }

    public Date getDataInicial() {
        if (dataInicial == null) {
            setDataInicial(Calendario.primeiroDiaMes());
        }
        return dataInicial;
    }

    public void setDataInicial(Date dataInicial) {
        this.dataInicial = Calendario.primeiraHoraDia(dataInicial);
    }

    public Date getDataFinal() {
        if (dataFinal == null) {
            setDataFinal(Calendario.ultimoDiaMes());
        }
        return dataFinal;
    }

    public void setDataFinal(Date dataFinal) {
        this.dataFinal = Calendario.ultimaHoraDia(dataFinal);
    }

    public String getMensagemResultado() {
        return mensagemResultado;
    }

    public void setMensagemResultado(String mensagemResultado) {
        this.mensagemResultado = mensagemResultado;
    }

    public TokenOperacaoVO getTokenOperacaoVOSelecionado() {
        if (tokenOperacaoVOSelecionado == null) {
            tokenOperacaoVOSelecionado = new TokenOperacaoVO();
        }
        return tokenOperacaoVOSelecionado;
    }

    public void setTokenOperacaoVOSelecionado(TokenOperacaoVO tokenOperacaoVOSelecionado) {
        this.tokenOperacaoVOSelecionado = tokenOperacaoVOSelecionado;
    }

    public List<ObjetoGenerico> getListaParametrosSelecionado() {
        return listaParametrosSelecionado;
    }

    public void setListaParametrosSelecionado(List<ObjetoGenerico> listaParametrosSelecionado) {
        this.listaParametrosSelecionado = listaParametrosSelecionado;
    }

    public boolean isExibirModalParametros() {
        return exibirModalParametros;
    }

    public void setExibirModalParametros(boolean exibirModalParametros) {
        this.exibirModalParametros = exibirModalParametros;
    }

    public void fecharPanelDadosParametros() {
        this.setExibirModalParametros(false);
        this.setExibirPix(false);
    }

    public boolean isExibirPix() {
        return exibirPix;
    }

    public void setExibirPix(boolean exibirPix) {
        this.exibirPix = exibirPix;
    }

}

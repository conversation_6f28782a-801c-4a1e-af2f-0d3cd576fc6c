package controle.basico;

import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.enumeradores.TipoTurmaEnum;
import br.com.pactosolucoes.estrutura.ItemExportacaoEnum;
import br.com.pactosolucoes.estrutura.exportador.controle.ExportadorListaControle;
import controle.arquitetura.SuperControle;
import controle.arquitetura.security.LoginControle;
import negocio.comuns.arquitetura.LogVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.conviteaulaexperimental.TipoConviteAulaExperimentalModalidadeHorarioVO;
import negocio.comuns.conviteaulaexperimental.TipoConviteAulaExperimentalModalidadeVO;
import negocio.comuns.conviteaulaexperimental.TipoConviteAulaExperimentalVO;
import negocio.comuns.plano.ModalidadeVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UtilReflection;

import javax.faces.event.ActionEvent;
import javax.faces.model.SelectItem;
import java.util.ArrayList;
import java.util.List;

/**
 * Created by ulisses on 08/01/2016.
 */
public class TipoConviteAulaExperimentalControle extends SuperControle {

    private TipoConviteAulaExperimentalVO tipoConviteAulaExperimentalVO;
    private TipoConviteAulaExperimentalModalidadeHorarioVO tipoConviteAulaExperimentalModalidadeHorarioVO;
    private List<SelectItem> listaSelectItemUsuario;
    private List<SelectItem> listaSelectItemModalidade;
    private TipoConviteAulaExperimentalModalidadeVO tipoConviteAulaExperimentalModalidadeVO;
    private boolean apresentarConfiguracaoHorario = false;
    private Integer codigoModalidade;


    public TipoConviteAulaExperimentalControle()throws Exception{
        inicializarDados();
    }

    private void inicializarDados() throws Exception{
        inicializarDadosTipoConvite();
        montarListaSelectItemUsuarioResponsavel();
        montarListaSelectItemModalidade();
        montarListaEmpresas();
    }

    private void inicializarDadosTipoConvite()throws Exception{
        this.tipoConviteAulaExperimentalVO = new TipoConviteAulaExperimentalVO();
        EmpresaVO empresaVO = getEmpresaLogado();
        if (UtilReflection.objetoMaiorQueZero(empresaVO, "getCodigo()")){
            this.tipoConviteAulaExperimentalVO.setEmpresaVO(empresaVO);
        }else{
            this.tipoConviteAulaExperimentalVO.setEmpresaVO(new EmpresaVO());
        }
        tipoConviteAulaExperimentalModalidadeVO = new TipoConviteAulaExperimentalModalidadeVO();
        this.codigoModalidade = 0;
    }

    public void montarListaSelectItemUsuarioResponsavel() throws Exception {
        this.listaSelectItemUsuario = new ArrayList<SelectItem>();
        this.listaSelectItemUsuario.add(new SelectItem(0, ""));
        if (UtilReflection.objetoMaiorQueZero(this.tipoConviteAulaExperimentalVO, "getEmpresaVO().getCodigo()")){
            List<UsuarioVO> lista = getFacade().getUsuario().consultarUsuarioAberturaMeta(this.tipoConviteAulaExperimentalVO.getEmpresaVO().getCodigo(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            for (UsuarioVO usuarioVO: lista){
                this.listaSelectItemUsuario.add(new SelectItem(usuarioVO.getCodigo(), usuarioVO.getNome()));
            }
        }
    }

    public void gravar() {
        try {
            if (this.tipoConviteAulaExperimentalVO.isNovoObj()) {
                getFacade().getTipoConviteAulaExperimental().incluir(this.tipoConviteAulaExperimentalVO);
                registrarLogManualmente(true);
            } else {
                getFacade().getTipoConviteAulaExperimental().alterar(this.tipoConviteAulaExperimentalVO);

                //LOG - INICIO
                try {
                    //registrarLogObjetoVO(this.tipoConviteAulaExperimentalVO, 0);
                    registrarLogManualmente(false);
                } catch (Exception e) {
                    registrarLogErroObjetoVO("TIPOCONVITEAULAEXPERIMENTAL", this.tipoConviteAulaExperimentalVO.getCodigo(), "ERRO AO GERAR LOG DE ALTERAÇÃO DE TIPOCONVITEAULAEXPERIMENTAL", this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
                    e.printStackTrace();
                }
                //LOG - FIM
            }
            this.tipoConviteAulaExperimentalVO.registrarObjetoVOAntesDaAlteracao();
            montarSucesso("msg_dados_gravados");
        } catch (Exception e) {
            montarErro(e);
        }
    }

    private void registrarLogManualmente(boolean inclusao)throws Exception{
        LogVO obj = new LogVO();
        obj.setChavePrimaria(this.tipoConviteAulaExperimentalVO.getCodigo().toString());
        obj.setNomeEntidade("TIPOCONVITEAULAEXPERIMENTAL");
        obj.setNomeEntidadeDescricao("CONVITE AULA EXPERIMENTAL");
        obj.setResponsavelAlteracao(getUsuarioLogado().getNome());
        obj.setUserOAMD(getUsuarioLogado().getUserOamd());
        obj.setNomeCampo("MENSAGEM");
        obj.setValorCampoAlterado("");
        StringBuilder msg = new StringBuilder();
        String simNao ="";
        List<TipoConviteAulaExperimentalModalidadeVO> listaModalidadeBanco = null;
        if (inclusao)
          obj.setOperacao("INCLUSÃO");
        else
            obj.setOperacao("ALTERAÇÃO");
        msg.append("Descrição:").append(this.tipoConviteAulaExperimentalVO.getDescricao()).append("\n");
        msg.append("Vigência De:").append(Calendario.getData(this.tipoConviteAulaExperimentalVO.getVigenciaInicial(), "dd/MM/yyyy")).append("\n");
        msg.append("Vigência Até:").append(Calendario.getData(this.tipoConviteAulaExperimentalVO.getVigenciaFinal(), "dd/MM/yyyy")).append("\n");
        msg.append("Quantidade aula experimental:").append(this.tipoConviteAulaExperimentalVO.getQuantidadeAulaExperimental()).append("\n");
        msg.append("Quantidade máxima de convite o aluno pode enviar:").append(this.tipoConviteAulaExperimentalVO.getQuantidadeConviteAlunoPodeEnviar()).append("\n");
        simNao = this.tipoConviteAulaExperimentalVO.isAlunoPodeEnviarConvite() ? "SIM" : "NÃO";
        msg.append("Aluno pode enviar convite:").append(simNao).append("\n");
        simNao = this.tipoConviteAulaExperimentalVO.isColaboradorPodeEnviarConvite() ? "SIM" : "NÃO";
        msg.append("Colaborador pode enviar convite:").append(simNao).append("\n");

        msg.append("Restrições de Modalidades: \n");
        if (this.tipoConviteAulaExperimentalVO.getListaModalidade().size() <= 0){
            msg.append("Nenhuma. \n");
        }else{
            StringBuilder msgHorario = new StringBuilder();
            for (TipoConviteAulaExperimentalModalidadeVO conviteModalidade: this.tipoConviteAulaExperimentalVO.getListaModalidade()){
                msgHorario.delete(0,msgHorario.length());
                if (conviteModalidade.getListaHorario_Apresentar().size() <= 0){
                    msgHorario.append("nenhum.");
                }else{
                    for (TipoConviteAulaExperimentalModalidadeHorarioVO modalidadeHorario: conviteModalidade.getListaHorario_Apresentar()){
                        msgHorario.append("Dias da Semana:").append(modalidadeHorario.getDiasSemanaApresentarNaGrid()).append("\n Horário de:").append(modalidadeHorario.getHoraInicial()).append(" até ").append(modalidadeHorario.getHoraFinal()).append("\n");
                    }
                }
                msg.append(conviteModalidade.getModalidadeVO().getNome()).append(". \n Horários: \n").append(msgHorario.toString()).append("\n");
            }
        }
        obj.setValorCampoAlterado(msg.toString());
        obj.setDataAlteracao(negocio.comuns.utilitarias.Calendario.hoje());
        registrarLogObjetoVO(obj, tipoConviteAulaExperimentalVO.getCodigo());

    }


    public void exportar(ActionEvent evt) throws Exception {
        try {
            limparMsg();
            setMsgAlert("");
            ExportadorListaControle exportadorListaControle = (ExportadorListaControle) JSFUtilities.getFromRequest(ExportadorListaControle.class.getSimpleName());
            String paramsTableFiltrada = context().getExternalContext().getRequestParameterMap().get("paramsTableFiltrada");

            String[] split = paramsTableFiltrada.split(",");
            String campoOrdenacao = split[0].replace("[", "");
            String ordem = split[1];
            String filtro = split[2].replace("''", "");
            filtro = filtro.replace("]", "");
            List listaParaImpressao = getFacade().getTipoConviteAulaExperimental().consultarParaImpressao(filtro, ordem, campoOrdenacao, getEmpresaLogado() == null ? 0 : getEmpresaLogado().getCodigo());
            exportadorListaControle.exportar(evt, listaParaImpressao, filtro, ItemExportacaoEnum.REL_CONVITE_EXPERIMENTAL);
            if(exportadorListaControle.getErro()){
                throw new Exception(exportadorListaControle.getMensagemDetalhada());
            }
            setMsgAlert(exportadorListaControle.getOperacaoOnComplete());
        } catch (Exception e) {
            montarErro(e);
        }
    }

    public String editar() {
        Integer codigoConsulta = Integer.parseInt(request().getParameter("chavePrimaria"));
        try {
            this.tipoConviteAulaExperimentalVO = getFacade().getTipoConviteAulaExperimental().consultarPorCodigo(codigoConsulta, Uteis.NIVELMONTARDADOS_TODOS);
            this.tipoConviteAulaExperimentalVO.registrarObjetoVOAntesDaAlteracao();
            this.codigoModalidade = 0;
            if (getUsuarioLogado().getAdministrador()){
                montarListaSelectItemModalidade();
            }
            limparMsg();
        } catch (Exception e) {
            montarErro(e);
        }
        return "editarTipoConviteAulaExperimental";
    }


    public TipoConviteAulaExperimentalVO getTipoConviteAulaExperimentalVO() {
        return tipoConviteAulaExperimentalVO;
    }

    public void setTipoConviteAulaExperimentalVO(TipoConviteAulaExperimentalVO tipoConviteAulaExperimentalVO) {
        this.tipoConviteAulaExperimentalVO = tipoConviteAulaExperimentalVO;
    }

    public String novo() {
        try{
            inicializarDadosTipoConvite();
            if (getUsuarioLogado().getAdministrador()){
                montarListaSelectItemModalidade();
            }
            montarSucesso("msg_entre_dados");
            limparMsg();
            return "editarTipoConviteAulaExperimental";
        }catch (Exception e){
            montarErro(e);
        }
        return "consultarTipoConviteAulaExperimental";

    }

    public void inicializarDadosAbaRestricao(){
        try{
            this.apresentarConfiguracaoHorario = false;
            this.tipoConviteAulaExperimentalModalidadeVO = new TipoConviteAulaExperimentalModalidadeVO();
            if (getUsuarioLogado().getAdministrador()){
                montarListaSelectItemModalidade();
            }
            montarSucesso("");
        }catch (Exception e){
          montarErro(e);
        }

    }


    public String excluir() {
        try {
            getFacade().getTipoConviteAulaExperimental().excluir(this.tipoConviteAulaExperimentalVO);
            inicializarDadosTipoConvite();
            montarSucesso("msg_dados_excluidos");
        } catch (Exception e) {
            montarErro(e);
        }
        return "editarTipoConviteAulaExperimental";
    }

    public String inicializarConsultar() {
        setPaginaAtualDeTodas("0/0");
        setListaConsulta(new ArrayList());
        definirVisibilidadeLinksNavegacao(0, 0);
        limparMsg();
        return "consultarTipoConviteAulaExperimental";
    }

    public void realizarConsultaLogObjetoSelecionado() {
        LoginControle loginControle = (LoginControle) context().getExternalContext().getSessionMap().get("LoginControle");
        String nomeClasse = this.tipoConviteAulaExperimentalVO.getClass().getSimpleName();
        nomeClasse = nomeClasse.substring(0, nomeClasse.length() - 2);
        loginControle.setNomeClasse(loginControle.getNomeEntidadeCamposLog("prt_" + nomeClasse + "_tituloForm"));
        loginControle.consultarLogObjetoSelecionado(nomeClasse.toUpperCase(), this.tipoConviteAulaExperimentalVO.getCodigo(), 0);
    }

    public boolean isMostrarConfiguracaoRestricao()throws Exception{
        if (getUsuarioLogado().getAdministrador()){
            return tipoConviteAulaExperimentalVO.getCodigo() > 0;
        }
        return true;
    }


    public void adicionarModalidade(){
        try {
            if ((this.codigoModalidade == null) || (this.codigoModalidade <= 0)){
                throw new ConsistirException("Operação não permitida. Selecione a modalidade.");
            }
            if (apresentarConfiguracaoHorario){
                this.codigoModalidade = 0;
                throw new ConsistirException("Operação não permitida. Grave a alteração de horário primeiro e depois adicione outra modalidade.");
            }
            this.tipoConviteAulaExperimentalModalidadeVO.setModalidadeVO(getFacade().getModalidade().consultarPorChavePrimaria(this.codigoModalidade, Uteis.NIVELMONTARDADOS_TODOS));
            if (this.tipoConviteAulaExperimentalVO.getListaModalidade().contains(this.tipoConviteAulaExperimentalModalidadeVO)){
               throw new ConsistirException("Operação não permitida. A modalidade já foi adicionada.");
            }
            this.tipoConviteAulaExperimentalModalidadeVO.setTipoConviteAulaExperimentalVO(this.tipoConviteAulaExperimentalVO);
            this.tipoConviteAulaExperimentalVO.getListaModalidade().add(this.tipoConviteAulaExperimentalModalidadeVO);
            this.tipoConviteAulaExperimentalModalidadeVO = new TipoConviteAulaExperimentalModalidadeVO();
            this.tipoConviteAulaExperimentalModalidadeVO.setTipoConviteAulaExperimentalVO(this.tipoConviteAulaExperimentalVO);
            this.codigoModalidade = 0;
            montarSucesso("msg_dados_adicionados");
        } catch (Exception e) {
            montarErro(e);
        }
    }

    public void adicionarHorario(){
        try {
            TipoConviteAulaExperimentalModalidadeHorarioVO.validarDados(this.tipoConviteAulaExperimentalModalidadeHorarioVO);
            this.tipoConviteAulaExperimentalModalidadeHorarioVO.montarDescricaoDiaSemanaSelecionados();
            this.tipoConviteAulaExperimentalModalidadeHorarioVO.setTipoConviteAulaExperimentalModalidadeVO(this.tipoConviteAulaExperimentalModalidadeVO);
            this.tipoConviteAulaExperimentalModalidadeVO.getListaHorario().add(this.tipoConviteAulaExperimentalModalidadeHorarioVO);
            this.tipoConviteAulaExperimentalModalidadeHorarioVO = new TipoConviteAulaExperimentalModalidadeHorarioVO();
            montarSucesso("msg_dados_adicionados");
        } catch (Exception e) {
            montarErro(e);
        }
    }


    public void editarHorario(){
        try {
            //this.tipoConviteAulaExperimentalModalidadeVO = tipoConviteAulaExperimentalModalidadeHorarioVO.getTipoConviteAulaExperimentalModalidadeVO();
            montarSucesso("msg_dados_editar");
        } catch (Exception e) {
            montarErro(e);
        }
    }

    public void removerHorario(){
        try {
            this.tipoConviteAulaExperimentalModalidadeVO.getListaHorario().remove(this.tipoConviteAulaExperimentalModalidadeHorarioVO);
            this.tipoConviteAulaExperimentalModalidadeHorarioVO = new TipoConviteAulaExperimentalModalidadeHorarioVO();
            montarSucesso("msg_dados_excluidos");
        } catch (Exception e) {
            montarErro(e);
        }
    }


    public void removerModalidade(){
        try {
            this.tipoConviteAulaExperimentalVO.getListaModalidade().remove(this.tipoConviteAulaExperimentalModalidadeVO);
            montarSucesso("msg_dados_excluidos");
        } catch (Exception e) {
            montarErro(e);
        }
    }

    public void fechaJanelaConviteHorario() {
        apresentarConfiguracaoHorario = false;
        this.tipoConviteAulaExperimentalModalidadeVO = new TipoConviteAulaExperimentalModalidadeVO();
        this.tipoConviteAulaExperimentalModalidadeVO.setTipoConviteAulaExperimentalVO(this.tipoConviteAulaExperimentalVO);
        montarSucesso("msg_dados_adicionados");
    }


    public void configurarConviteModalidadeHorario() throws Exception {
        this.apresentarConfiguracaoHorario = true;
        this.tipoConviteAulaExperimentalModalidadeHorarioVO = new TipoConviteAulaExperimentalModalidadeHorarioVO();
        this.tipoConviteAulaExperimentalModalidadeHorarioVO.setTipoConviteAulaExperimentalModalidadeVO(this.tipoConviteAulaExperimentalModalidadeVO);
        montarSucesso("msg_dados_selecionados");
    }


    public void montarListaSelectItemModalidade() {
        try{
            this.listaSelectItemModalidade = new ArrayList<SelectItem>();
            this.listaSelectItemModalidade.add(new SelectItem(0, ""));
            if (UtilReflection.objetoMaiorQueZero(tipoConviteAulaExperimentalVO, "getEmpresaVO().getCodigo()")){
                List<ModalidadeVO> listaModalidade = getFacade().getModalidade().consultarModalidadeAtivaComTurma(this.tipoConviteAulaExperimentalVO.getEmpresaVO().getCodigo(), TipoTurmaEnum.TODOS, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                for (ModalidadeVO modalidadeVO: listaModalidade){
                    this.listaSelectItemModalidade.add(new SelectItem(modalidadeVO.getCodigo(), modalidadeVO.getNome()));
                }
            }
            montarSucesso("");
        } catch (Exception e) {
            montarErro(e);
        }

    }


    public List<SelectItem> getListaSelectItemUsuario() {
        return listaSelectItemUsuario;
    }

    public void setListaSelectItemUsuario(List<SelectItem> listaSelectItemUsuario) {
        this.listaSelectItemUsuario = listaSelectItemUsuario;
    }

    public List<SelectItem> getListaSelectItemModalidade() {
        return listaSelectItemModalidade;
    }

    public void setListaSelectItemModalidade(List<SelectItem> listaSelectItemModalidade) {
        this.listaSelectItemModalidade = listaSelectItemModalidade;
    }

    public TipoConviteAulaExperimentalModalidadeVO getTipoConviteAulaExperimentalModalidadeVO() {
        return tipoConviteAulaExperimentalModalidadeVO;
    }

    public void setTipoConviteAulaExperimentalModalidadeVO(TipoConviteAulaExperimentalModalidadeVO tipoConviteAulaExperimentalModalidadeVO) {
        this.tipoConviteAulaExperimentalModalidadeVO = tipoConviteAulaExperimentalModalidadeVO;
    }

    public TipoConviteAulaExperimentalModalidadeHorarioVO getTipoConviteAulaExperimentalModalidadeHorarioVO() {
        return tipoConviteAulaExperimentalModalidadeHorarioVO;
    }

    public void setTipoConviteAulaExperimentalModalidadeHorarioVO(TipoConviteAulaExperimentalModalidadeHorarioVO tipoConviteAulaExperimentalModalidadeHorarioVO) {
        this.tipoConviteAulaExperimentalModalidadeHorarioVO = tipoConviteAulaExperimentalModalidadeHorarioVO;
    }

    public boolean isApresentarConfiguracaoHorario() {
        return apresentarConfiguracaoHorario;
    }

    public void setApresentarConfiguracaoHorario(boolean apresentarConfiguracaoHorario) {
        this.apresentarConfiguracaoHorario = apresentarConfiguracaoHorario;
    }

    public Integer getCodigoModalidade() {
        return codigoModalidade;
    }

    public void setCodigoModalidade(Integer codigoModalidade) {
        this.codigoModalidade = codigoModalidade;
    }
}

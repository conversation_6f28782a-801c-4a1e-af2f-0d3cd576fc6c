/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package controle.basico;

import br.com.pactosolucoes.comuns.notificacao.RecursoSistema;
import org.json.JSONArray;
import org.json.JSONObject;
import controle.arquitetura.FuncionalidadeSistemaEnum;
import controle.arquitetura.SuperControle;
import negocio.comuns.basico.*;
import negocio.comuns.utilitarias.Criptografia;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import servicos.propriedades.PropsService;
import servicos.util.ExecuteRequestHttpService;

import java.io.IOException;
import java.net.URL;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static controle.basico.UCPControle.listaIntegracaoTagUcp;
import static controle.basico.UCPControle.listaTelaConhecimentosUCP;
import static controle.basico.UCPControle.listaTodosConhecimentosUCP;

/*
 * <AUTHOR> Felipe
 */

public class UCPUsuarioControle extends SuperControle {

    private String paginaAtual;
    private List<PerguntaUcpTO> resultadoConhecimentosUCP;
    private String filtroUCP;
    private String msgApresentarResultado;
    private boolean apresentarBtnSuporte = false;
    private String msgTotalPesquisa;
    private boolean pesquisaSolr = false;
    private String serviceUsuario;
    private String serviceSenha;
    private boolean apresentarAcompanharSolicitacoes = false;
    private List<PerguntaUcpTO> listaTelaUCP;
    private List<PerguntaUcpTO> listaTelaUCPApresentar;
    private int index = 0;

    public UCPUsuarioControle() throws Exception {
        inicializar();
    }

    public void inicializar() {
        msgApresentarResultado = "";
        msgTotalPesquisa = "";
        apresentarBtnSuporte = false;
    }

    public Integer getQtdConhecimentos() throws IOException {
        try {
            Integer qtdTela = qtdConhecimentosTela();

            if (qtdTela != null) {
                return qtdTela;
            } else {
                return 0;
            }
        } catch (Exception e) {
            return 0;
        }
    }

    private String verificaPaginaAtual() {
        try {
            StringBuffer url = request().getRequestURL();
            URL u = new URL(url.toString());
            String urlNav = u.getPath();
            String[] temp = urlNav.split("/");
            Integer qtdPag = temp.length;
            String paginaAtual = temp[qtdPag - 1];
            paginaAtual = paginaAtual.replace(".jsp", "");
            setPaginaAtual(paginaAtual);
            return paginaAtual;
        } catch (Exception e) {
            return e.getMessage();
        }
    }

    private Integer qtdConhecimentosTela() {
        String paginaAtual = verificaPaginaAtual();
        String paginaAtualForm = paginaAtual;
        if (paginaAtual.toLowerCase().endsWith("cons") && paginaAtualForm.length() > 4) {
            try {
                int limit = paginaAtualForm.length() - 4;
                paginaAtualForm = paginaAtualForm.substring(0,  limit);
                paginaAtualForm = paginaAtualForm + "form";
            } catch (Exception e){
                paginaAtualForm = "";
            }
        }

        setListaTelaUCP(new ArrayList<PerguntaUcpTO>());
        for (IntegracaoTagUcpTO ucpTagTO : listaTelaConhecimentosUCP) {
            if (paginaAtual.toLowerCase().equals(ucpTagTO.getNome().toLowerCase()) || paginaAtualForm.toLowerCase().equals(ucpTagTO.getNome().toLowerCase())) {
                setListaTelaUCP(Ordenacao.ordenarLista(ucpTagTO.getPerguntas(), "titulo"));
                return ucpTagTO.getPerguntas().size();
    }
        }
        return 0;
    }

    public IntegracaoTagUcpTO getTagPergunta() {
        String paginaAtual = verificaPaginaAtual();
        for (IntegracaoTagUcpTO ucpTagTO : listaIntegracaoTagUcp) {
            if (paginaAtual.toLowerCase().equals(ucpTagTO.getNome().toLowerCase())) {
                return ucpTagTO;
            }
        }
        return new IntegracaoTagUcpTO();
    }

    public String getPaginaAtual() {
        if (paginaAtual == null) {
            paginaAtual = "";
        }
        return paginaAtual;
    }

    public String getPaginaAtualEncriptada() {
        try {
            return Criptografia.encriptarHexadecimal(getPaginaAtual(),"chave_busca_ucp");
        } catch (Exception ex) {
            ex.printStackTrace();
            return "";
        }
    }

    public void setPaginaAtual(String paginaAtual) {
        this.paginaAtual = paginaAtual;
    }

    public void filtrarConhecimentosUCP() throws Exception {
        List<PerguntaUcpTO> listaFiltrada;

        if (!UteisValidacao.emptyString(filtroUCP)) {

            try {
                listaFiltrada = filtrarSolr();
                setPesquisaSolr(true);
            } catch (Exception e) {
                Uteis.logar(e, UCPUsuarioControle.class);
                listaFiltrada = filtrarPadrao();
                setPesquisaSolr(false);
            }

            int i = 1;
            for (PerguntaUcpTO perguntaUcpTO : listaFiltrada) {
                perguntaUcpTO.setPosicao(i);
                i++;
            }

            if (!UteisValidacao.emptyList(listaFiltrada)) {
                msgApresentarResultado = "Principais resultados da sua busca:";

                if (listaFiltrada.size() == 1) {
                    if (isPesquisaSolr()) {
                        msgTotalPesquisa = listaFiltrada.size() + " Resultado";
                    } else {
                        msgTotalPesquisa = listaFiltrada.size() + " resultado";
                    }
                } else {
                    if (isPesquisaSolr()) {
                        msgTotalPesquisa = listaFiltrada.size() + " Resultados";
                    } else {
                        msgTotalPesquisa = listaFiltrada.size() + " resultados";
                    }
                }

                apresentarBtnSuporte = true;
                resultadoConhecimentosUCP = listaFiltrada;
            } else {
                msgApresentarResultado = "Nenhum conhecimento foi encontrado.";
                msgTotalPesquisa = "";
                apresentarBtnSuporte = true;
                resultadoConhecimentosUCP = new ArrayList<PerguntaUcpTO>();
            }
        } else {
            msgApresentarResultado = "";
            msgTotalPesquisa = "";
            apresentarBtnSuporte = false;
            resultadoConhecimentosUCP = new ArrayList<PerguntaUcpTO>();
        }
    }

    private List<PerguntaUcpTO> filtrarPadrao() {
        List<PerguntaUcpTO> listaFiltrada = new ArrayList<PerguntaUcpTO>();
        String fraseDigitada = Uteis.retirarAcentuacaoRegex(getFiltroUCP()).toLowerCase();
        String[] listaPalavrasBusca = fraseDigitada.split(" ");
        Map<Integer, PerguntaUcpTO> map = new HashMap<Integer, PerguntaUcpTO>();
        for (PerguntaUcpTO perguntaUcpTO : listaTodosConhecimentosUCP) {
            try {
                if (Uteis.retirarAcentuacaoRegex(perguntaUcpTO.getTitulo().toLowerCase()).contains(fraseDigitada)
                        || Uteis.retirarAcentuacaoRegex(perguntaUcpTO.getDescricao().toLowerCase()).contains(fraseDigitada)) {
                    map.put(perguntaUcpTO.getCodigo(), perguntaUcpTO);
                }
            } catch (Exception ignored) {
            }
        }

        for (String palavra : listaPalavrasBusca) {
            for (PerguntaUcpTO perguntaUcpTO : listaTodosConhecimentosUCP) {
                try {
                    if (Uteis.retirarAcentuacaoRegex(perguntaUcpTO.getTitulo().toLowerCase()).contains(fraseDigitada)
                            || Uteis.retirarAcentuacaoRegex(perguntaUcpTO.getDescricao().toLowerCase()).contains(fraseDigitada)) {
                        map.put(perguntaUcpTO.getCodigo(), perguntaUcpTO);
                    }

                    for (TagUcpTO tag : perguntaUcpTO.getTags()) {
                        if (Uteis.retirarAcentuacaoRegex(tag.getNome().toLowerCase()).contains(palavra)) {
                            map.put(perguntaUcpTO.getCodigo(), perguntaUcpTO);
                        }
                    }

                } catch (Exception ignored) {
                }
            }
        }
        listaFiltrada.addAll(map.values());
        Ordenacao.ordenarListaReverse(listaFiltrada, "qtdVisualizada");
        return listaFiltrada;
    }

    private List<PerguntaUcpTO> filtrarSolr() throws Exception {
        List<PerguntaUcpTO> listaFiltrada = new ArrayList<PerguntaUcpTO>();
        Map<String, String> params = new HashMap<String, String>();
        params.put("q", filtroUCP);
        params.put("campos", "id,titulo,descricaoPlanaPergunta");
        params.put("usoInterno", "false");
        params.put("especificaEmpresa", "false");

        ExecuteRequestHttpService executeRequestHttpService = new ExecuteRequestHttpService();
        executeRequestHttpService.connectTimeout = ExecuteRequestHttpService.CONNECT_TIMEOUT;
        executeRequestHttpService.readTimeout = ExecuteRequestHttpService.READ_TIMEOUT;
        String retorno = executeRequestHttpService.executeRequestInner(PropsService.getPropertyValue(PropsService.myUpUrlBase) + "/prest/conhecimento/consultar", params, "UTF-8");

        JSONObject jsonObject = new JSONObject(retorno);
        if (jsonObject.has("erro")) {
            throw new Exception("Falha na consulta do SOLR");
        } else {
            JSONObject responsa = jsonObject.getJSONObject("response");
            JSONArray lista = responsa.getJSONArray("docs");
            for (int e = 0; e < lista.length(); e++) {
                JSONObject obj = lista.getJSONObject(e);
                PerguntaUcpTO pergunta = new PerguntaUcpTO();
                pergunta.setCodigo(obj.getInt("id"));
                pergunta.setTitulo(obj.getString("titulo"));
                pergunta.setDescricao(obj.getString("descricaoPlanaPergunta"));
                listaFiltrada.add(pergunta);
            }
        }
        return listaFiltrada;
    }

    public String getFiltroUCP() {
        if (filtroUCP == null) {
            filtroUCP = "";
        }
        return filtroUCP;
    }

    public void setFiltroUCP(String filtroUCP) {
        this.filtroUCP = filtroUCP;
    }

    public List<PerguntaUcpTO> getResultadoConhecimentosUCP() {
        if (resultadoConhecimentosUCP == null) {
            resultadoConhecimentosUCP = new ArrayList<PerguntaUcpTO>();
        }
        return resultadoConhecimentosUCP;
    }

    public void setResultadoConhecimentosUCP(List<PerguntaUcpTO> resultadoConhecimentosUCP) {
        this.resultadoConhecimentosUCP = resultadoConhecimentosUCP;
    }

    public String getMsgApresentarResultado() {
        if (msgApresentarResultado == null) {
            msgApresentarResultado = "";
        }
        return msgApresentarResultado;
    }

    public void setMsgApresentarResultado(String msgApresentarResultado) {
        this.msgApresentarResultado = msgApresentarResultado;
    }

    public boolean isApresentarBtnSuporte() {
        return apresentarBtnSuporte;
    }

    public void setApresentarBtnSuporte(boolean apresentarBtnSuporte) {
        this.apresentarBtnSuporte = apresentarBtnSuporte;
    }

    public String getMsgTotalPesquisa() {
        if (msgTotalPesquisa == null) {
            msgTotalPesquisa = "";
        }
        return msgTotalPesquisa;
    }

    public void setMsgTotalPesquisa(String msgTotalPesquisa) {
        this.msgTotalPesquisa = msgTotalPesquisa;
    }

    public void inicializaBuscaUCP() throws Exception {
        filtroUCP = "";
        apresentarBtnSuporte = false;
        resultadoConhecimentosUCP.clear();
        prepararUsuarioService();
    }

    public String getFiltroCripto() {
        try {
            return Criptografia.encrypt(getFiltroUCP(), SuperControle.Crypt_KEY, SuperControle.Crypt_ALGORITM);
        } catch (Exception e) {
            return "";
        }
    }

    private boolean isPesquisaSolr() {
        return pesquisaSolr;
    }

    private void setPesquisaSolr(boolean pesquisaSolr) {
        this.pesquisaSolr = pesquisaSolr;
    }

    public String getServiceSenha() {
        if (serviceSenha == null) {
            serviceSenha = "";
        }
        return serviceSenha;
    }

    public void setServiceSenha(String serviceSenha) {
        this.serviceSenha = serviceSenha;
    }

    public String getServiceUsuario() {
        if (serviceUsuario == null) {
            serviceUsuario = "";
        }
        return serviceUsuario;
    }

    public void setServiceUsuario(String serviceUsuario) {
        this.serviceUsuario = serviceUsuario;
    }

    private void prepararUsuarioService() {
        try {
            setApresentarAcompanharSolicitacoes(false);
            setServiceUsuario("");
            setServiceSenha("");
            String usuario = getUsuarioLogado().getServiceUsuario();
            String senha = getUsuarioLogado().getServiceSenha();

            if (UteisValidacao.emptyString(usuario)) {
                EmpresaVO empresa = getEmpresaLogado();
                if (!UteisValidacao.emptyString(empresa.getServiceUsuario())) {
                    usuario = empresa.getServiceUsuario();
                    senha = empresa.getServiceSenha();
                }
            }

            if (!UteisValidacao.emptyString(usuario)) {
                senha = Criptografia.decrypt(senha, SuperControle.Crypt_KEY, SuperControle.Crypt_ALGORITM);
                serviceUsuario = usuario;
                serviceSenha = senha;
                apresentarAcompanharSolicitacoes = true;
            }
        } catch (Exception e) {
            apresentarAcompanharSolicitacoes = false;
        }
    }

    public boolean isApresentarAcompanharSolicitacoes() {
        return apresentarAcompanharSolicitacoes;
    }

    public void setApresentarAcompanharSolicitacoes(boolean apresentarAcompanharSolicitacoes) {
        this.apresentarAcompanharSolicitacoes = apresentarAcompanharSolicitacoes;
    }

    public List<PerguntaUcpTO> getListaTelaUCP() {
        if (listaTelaUCP == null) {
            listaTelaUCP = new ArrayList<PerguntaUcpTO>();
}
        return listaTelaUCP;
    }

    public void setListaTelaUCP(List<PerguntaUcpTO> listaTelaUCP) {
        this.listaTelaUCP = listaTelaUCP;
    }

    public List<PerguntaUcpTO> getListaTelaUCPApresentar() {
        if (listaTelaUCPApresentar == null) {
            listaTelaUCPApresentar = new ArrayList<PerguntaUcpTO>();
        }
        return listaTelaUCPApresentar;
    }

    public void setListaTelaUCPApresentar(List<PerguntaUcpTO> listaTelaUCPApresentar) {
        this.listaTelaUCPApresentar = listaTelaUCPApresentar;
    }

    public int getIndex() {
        return index;
    }

    public void setIndex(int index) {
        this.index = index;
    }

    public void mostrarMaisConhecimentoTela() {
        index += 5;
        listaTelaUCPApresentar = listaTelaUCP.size() < index ? listaTelaUCP : listaTelaUCP.subList(0, index);
    }

    public void preencherListaApresentar() {
        notificarRecursoEmpresa(RecursoSistema.APRENDA_MAIS_SOBRE_ESTA_TELA);
        listaTelaUCPApresentar = new ArrayList<PerguntaUcpTO>();
        qtdConhecimentosTela();
        index = 5;
        listaTelaUCPApresentar = listaTelaUCP.size() < index ? new ArrayList(listaTelaUCP) : new ArrayList(listaTelaUCP).subList(0, index);
    }

    public String getQuantidadeVerMais() {
        try {
            Integer qtd = getListaTelaUCP().size() - getListaTelaUCPApresentar().size();
            return " " + qtd.toString();
        } catch (Exception ignored){
            return " ";
        }
    }

    public Boolean getApresentarBtnMostrarMais() {
        if(UteisValidacao.emptyList(listaTelaUCPApresentar)){
            listaTelaUCPApresentar = new ArrayList<>();
        }
        if(UteisValidacao.emptyList(listaTelaUCP)){
            listaTelaUCP = new ArrayList<>();
        }
        return listaTelaUCPApresentar.size() < listaTelaUCP.size();
    }

    /**
     * @autor Tiago Vitorino
     *
     * @since 09/04/2019
     * Método realiza a notificação ao clicar na lista criada ao abrir o modal "Aprenda Mais Sobre Esta Tela"
     */
    public void notificarClick() {
        notificarRecursoEmpresa(RecursoSistema.APRENDA_MAIS_SOBRE_ESTA_TELA_CLIQUE_DENTRO);
    }
}

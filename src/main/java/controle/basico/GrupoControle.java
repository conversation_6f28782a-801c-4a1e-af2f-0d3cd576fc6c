package controle.basico;

import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.enumeradores.GrupoTipoEnum;
import br.com.pactosolucoes.enumeradores.SituacaoClienteEnum;
import br.com.pactosolucoes.estrutura.exportador.controle.ExportadorListaControle;
import controle.arquitetura.security.LoginControle;
import controle.basico.clube.MensagemGenericaControle;
import negocio.facade.jdbc.basico.Grupo;
import javax.faces.model.SelectItem;
import java.util.List;
import java.util.ArrayList;
import controle.arquitetura.SuperControle;
import negocio.comuns.basico.GrupoVO;
import negocio.comuns.plano.enumerador.TipoDesconto;
import negocio.comuns.utilitarias.ControleConsulta;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.Uteis;
import javax.faces.event.ActionEvent;

/**
 * Classe responsável por implementar a interação entre os componentes JSF das páginas 
 * grupoForm.jsp grupoCons.jsp) com as funcionalidades da classe <code>Grupo</code>.
 * Implemtação da camada controle (Backing Bean).
 * @see SuperControle
 * @see Grupo
 * @see GrupoVO
 */
public class GrupoControle extends SuperControle {

    private GrupoVO grupoVO;
    private Boolean percentual;
    private Boolean valor;
    private Boolean campoConsultaSelectItem = false;
    private String msgAlert;

    public GrupoControle() throws Exception {
        obterUsuarioLogado();
        setControleConsulta(new ControleConsulta());
        setMensagemID("");
    }

    public String novo() {
        setGrupoVO(new GrupoVO());
        setValor(false);
        setPercentual(false);
        limparMsg();
        return "editar";
    }

    public String editar() {
        Integer codigoConsulta = Integer.parseInt(request().getParameter("chavePrimaria"));
        try {
            GrupoVO obj = getFacade().getGrupo().consultarPorChavePrimaria(codigoConsulta, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            obj.setNovoObj(false);
            obj.registrarObjetoVOAntesDaAlteracao();
            setGrupoVO(new GrupoVO());
            setGrupoVO(obj);
            validarTipoDescontoEditar();
        } catch (Exception e) {
            setErro(true);
            setSucesso(false);
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
        return "editar";
    }

    public String gravar() {
        try {
            if (grupoVO.isNovoObj().booleanValue()) {
                getFacade().getGrupo().incluir(grupoVO);
                incluirLogInclusao();
            } else {
                getFacade().getGrupo().alterar(grupoVO);
                incluirLogAlteracao();
            }
            setSucesso(true);
            setErro(false);
            setMensagemID("msg_dados_gravados");
            return "editar";
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
            return "editar";
        }
    }

    public void incluirLogInclusao() throws Exception {
        //LOG - INICIO
        try {
            grupoVO.setObjetoVOAntesAlteracao(new GrupoVO());
            grupoVO.setNovoObj(true);
            registrarLogObjetoVO(grupoVO, grupoVO.getCodigo(), "GRUPO", 0);
        } catch (Exception e) {
            registrarLogErroObjetoVO("GRUPO", grupoVO.getCodigo(), "ERRO AO GERAR LOG DE INCLUSÃO DE GRUPO", this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
        grupoVO.setNovoObj(false);
    }
    
    public void incluirLogExclusao() throws Exception {
        //LOG - INICIO
        try {
            grupoVO.setObjetoVOAntesAlteracao(new GrupoVO());
            grupoVO.setNovoObj(true);
            registrarLogExclusaoTodosDadosObjetoVO(grupoVO, grupoVO.getCodigo(), "GRUPO", 0);
        } catch (Exception e) {
            registrarLogErroObjetoVO("GRUPO", grupoVO.getCodigo(), "ERRO AO GERAR LOG DE EXCLUSÃO DE GRUPO", this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
    }

    public void incluirLogAlteracao() throws Exception {
        //LOG - INICIO
        try {
            registrarLogObjetoVO(grupoVO, grupoVO.getCodigo(), "GRUPO", 0);
        } catch (Exception e) {
            registrarLogErroObjetoVO("GRUPO", grupoVO.getCodigo(), "ERRO AO GERAR LOG DE ALTERAÇÃO DE GRUPO", this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
        grupoVO.registrarObjetoVOAntesDaAlteracao();
        //LOG - FIM
    }

    public String consultar() {
        try {
            super.consultar();
            List objs = new ArrayList();

            //limpar espaços em branco antes de realizar a consulta
            getControleConsulta().setValorConsulta(getControleConsulta().getValorConsulta().trim());

            if (getControleConsulta().getCampoConsulta().equals("codigo")) {
                if (getControleConsulta().getValorConsulta().equals("")) {
                    objs = getFacade().getGrupo().consultarPorCodigo(new Integer(0), true, Uteis.NIVELMONTARDADOS_TODOS);
                } else {
                    int valorInt = Integer.parseInt(getControleConsulta().getValorConsulta());
                    GrupoVO grupo = getFacade().getGrupo().consultarPorCodigoExato(new Integer(valorInt), true, Uteis.NIVELMONTARDADOS_TODOS);
                    if (grupo != null) {
                        objs.add(grupo);
                    }
                }
            }
            if (getControleConsulta().getCampoConsulta().equals("descricao")) {
                objs = getFacade().getGrupo().consultarPorDescricao(getControleConsulta().getValorConsulta(), true, Uteis.NIVELMONTARDADOS_TODOS);
            }
            if (getControleConsulta().getCampoConsulta().equals("tipoDesconto")) {
                objs = getFacade().getGrupo().consultarPorTipoDesconto(getControleConsulta().getValorConsulta().substring(0, 2), true, Uteis.NIVELMONTARDADOS_TODOS);
            }
            objs = ControleConsulta.obterSubListPaginaApresentar(objs, controleConsulta);
            definirVisibilidadeLinksNavegacao(controleConsulta.getPaginaAtual(), controleConsulta.getNrTotalPaginas());
            setListaConsulta(objs);
            limparMsg();
            return "consultar";
        } catch (Exception e) {
            setListaConsulta(new ArrayList());
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
            return "consultar";
        }
    }

    public String excluir() {
        try {
            getFacade().getGrupo().excluir(grupoVO);
            incluirLogExclusao();
            setGrupoVO(new GrupoVO());
            setMensagemID("msg_dados_excluidos");
            setSucesso(true);
            setErro(false);
            return "consultar";
        } catch (Exception e) {
            if(e.getMessage().contains("ERRO: atualização ou exclusão em tabela \"grupo\" viola restrição de chave estrangeira") || e.getMessage().contains("ERROR: update or delete on table \"grupo\" violates foreign key")){
                setMensagemDetalhada("Este Grupo de Desconto não pode ser excluído, pois está sendo utilizado!");
            }else {
                setMensagemDetalhada("msg_erro", e.getMessage());
            }
            setSucesso(false);
            setErro(true);
            return "editar";
        }
    }

    public void validarTipoDesconto() {
        if ((grupoVO.getTipoDesconto() == null) || (grupoVO.getTipoDesconto().equals(""))) {
            setPercentual(false);
            setValor(false);
            grupoVO.setPercentualDescontoGrupo(new Double(0));
            grupoVO.setValorDescontoGrupo(new Double(0));
        } else if (grupoVO.getTipoDesconto().equals("PE")) {
            setPercentual(true);
            setValor(false);
            grupoVO.setPercentualDescontoGrupo(new Double(0));
            grupoVO.setValorDescontoGrupo(new Double(0));
        } else if (grupoVO.getTipoDesconto().equals("VA")) {
            setPercentual(false);
            setValor(true);
            grupoVO.setPercentualDescontoGrupo(new Double(0));
            grupoVO.setValorDescontoGrupo(new Double(0));
        }
    }

    public void validarTipoDescontoEditar() {
        if (grupoVO.getTipoDesconto() == null) {
            setPercentual(false);
            setValor(false);
        } else if (grupoVO.getTipoDesconto().equals("PE")) {
            setPercentual(true);
            setValor(false);
        } else if (grupoVO.getTipoDesconto().equals("VA")) {
            setPercentual(false);
            setValor(true);
        }
    }

    public void irPaginaInicial() throws Exception {
        controleConsulta.setPaginaAtual(1);
        this.consultar();
    }

    public void irPaginaAnterior() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getPaginaAtual() - 1);
        this.consultar();
    }

    public void irPaginaPosterior() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getPaginaAtual() + 1);
        this.consultar();
    }

    public void irPaginaFinal() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getNrTotalPaginas());
        this.consultar();
    }

    public List getListaSelectItemTipoDescontoGrupo() throws Exception {
        List<SelectItem> itens = new ArrayList();
        for (TipoDesconto td : TipoDesconto.values()) {
            itens.add(new SelectItem(td, td.getDescricao()));
        }
        Ordenacao.ordenarLista(itens, "label");
        return itens;
    }

    public List<SelectItem> getListaSituacaoFamiliar() throws Exception {
        List<SelectItem> itens = new ArrayList();
        for (SituacaoClienteEnum situacaoClienteEnum : SituacaoClienteEnum.values()) {
            itens.add(new SelectItem(situacaoClienteEnum.getCodigo(), situacaoClienteEnum.getDescricao()));
        }
        Ordenacao.ordenarLista(itens, "label");
        return itens;
    }

    public List<SelectItem> getListaGrupoTipo(){
        List<SelectItem> items = new ArrayList();
        for(GrupoTipoEnum grupoTipoEnum: GrupoTipoEnum.values()){
            items.add(new SelectItem(grupoTipoEnum.getCodigo(), grupoTipoEnum.getDescricao()));
        }

        return items;
    }

    public List<SelectItem> getTipoConsultaCombo() {
        List<SelectItem> itens = new ArrayList();
        itens.add(new SelectItem("codigo", "Código"));
        itens.add(new SelectItem("descricao", "Descrição"));
        itens.add(new SelectItem("tipoDesconto", "Tipo de Desconto"));
        return itens;
    }

    public void alterarCampoConsulta() {
        String campoSelecionado = getControleConsulta().getCampoConsulta();
        getControleConsulta().setValorConsulta("");
        if (campoSelecionado.equals("tipoDesconto")) {
            setCampoConsultaSelectItem(Boolean.TRUE);
        } else {
            setCampoConsultaSelectItem(Boolean.FALSE);
        }
    }

    public List getListaSelectItemConsulta() {
        List<SelectItem> itens = new ArrayList();
        itens.add(new SelectItem("", ""));

        try {
            if (getControleConsulta().getCampoConsulta().equals("tipoDesconto")) {
                return getListaSelectItemTipoDescontoGrupo();
            }
        } catch (Exception e) {
            setErro(true);
            setSucesso(false);
            setMensagemDetalhada("msg_erro", e.getMessage());
            e.printStackTrace();
        }

        return itens;
    }

    public String inicializarConsultar() {
        setPaginaAtualDeTodas("0/0");
        setListaConsulta(new ArrayList());
        definirVisibilidadeLinksNavegacao(0, 0);
        setMensagemID("");
        setSucesso(false);
        setErro(false);
        return "consultar";
    }

    public GrupoVO getGrupoVO() {
        return grupoVO;
    }

    public void setGrupoVO(GrupoVO grupoVO) {
        this.grupoVO = grupoVO;
    }

    public Boolean getPercentual() {
        return percentual;
    }

    public void setPercentual(Boolean percentual) {
        this.percentual = percentual;
    }

    public Boolean getValor() {
        return valor;
    }

    public void setValor(Boolean valor) {
        this.valor = valor;
    }

    /**
     * Consulta de logs de grupo
     */
    @SuppressWarnings("unchecked")
    public void realizarConsultaLogObjetoSelecionado() {
        LoginControle loginControle = (LoginControle) context().getExternalContext().getSessionMap().get("LoginControle");
        loginControle.setNomeClasse("Grupo");
        loginControle.consultarLogObjetoSelecionado("GRUPO", grupoVO.getCodigo(), null);
    }
    
     /**
     * Consulta de logs de grupo
     */
    @SuppressWarnings("unchecked")
    public void realizarConsultaLogObjetoGeral() {
        grupoVO = new GrupoVO();
        realizarConsultaLogObjetoSelecionado();
    }

    public void setCampoConsultaSelectItem(Boolean campoConsultaSelectItem) {
        this.campoConsultaSelectItem = campoConsultaSelectItem;
    }

    public Boolean getCampoConsultaSelectItem() {
        return campoConsultaSelectItem;
    }

    public void exportar(ActionEvent evt) throws Exception {
        ExportadorListaControle exportadorListaControle = (ExportadorListaControle) JSFUtilities.getFromRequest(ExportadorListaControle.class.getSimpleName());
        String paramsTableFiltrada = context().getExternalContext().getRequestParameterMap().get("paramsTableFiltrada");

        String[] split = paramsTableFiltrada.split(",");
        String campoOrdenacao = split[0].replace("[", "");
        String ordem = split[1];
        String filtro = split[2].replace("''", "");
        filtro = filtro.replace("]", "");
        List listaParaImpressao = getFacade().getGrupo().consultarParaImpressao(filtro, ordem, campoOrdenacao, getEmpresaLogado() == null ? 0 : getEmpresaLogado().getCodigo());
        exportadorListaControle.exportar(evt, listaParaImpressao, filtro, null);

    }

    public void setMsgAlert(String msgAlert) {
        this.msgAlert = msgAlert;
    }

    public String getMsgAlert() {
        if (msgAlert == null) {
            return "";
        }
        return msgAlert;
    }


    public void confirmarExcluir(){
        MensagemGenericaControle control = (MensagemGenericaControle) getControlador(MensagemGenericaControle.class);
        control.setMensagemDetalhada("", "");
        setMsgAlert("Richfaces.showModalPanel('mdlMensagemGenerica');");
        control.init("Exclusão de Grupo de Desconto",
                "Deseja excluir o Grupo de Desconto?",
                this, "excluir", "", "", "", "grupoBtnExcluir");
    }

}

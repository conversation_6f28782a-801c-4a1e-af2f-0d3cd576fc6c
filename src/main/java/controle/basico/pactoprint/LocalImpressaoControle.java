package controle.basico.pactoprint;

import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.estrutura.exportador.controle.ExportadorListaControle;
import controle.arquitetura.SuperControle;
import controle.arquitetura.security.LoginControle;
import controle.basico.clube.MensagemGenericaControle;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.pactoprint.LocalImpressaoVO;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.ControleConsulta;
import negocio.comuns.utilitarias.Uteis;

import javax.faces.event.ActionEvent;
import javax.faces.model.SelectItem;
import java.util.ArrayList;
import java.util.List;

/**
 * date : 06/04/2015 15:18:06
 * autor: Ulisses
 */

public class LocalImpressaoControle extends SuperControle {
    private LocalImpressaoVO localImpressaoVO;
    private List<SelectItem> listaLocalImpressao;
    protected List listaSelectItemEmpresa;


    public String getOncompleteLog() {
        return oncompleteLog;
    }

    private String oncompleteLog;
    private static final String ENTIDADE_LOG = "LOCALIMPRESSAO";
    private String nomeComputadorImpressao;

    public LocalImpressaoVO getLocalImpressaoVO() {
        return localImpressaoVO;
    }

    public void setLocalImpressaoVO(LocalImpressaoVO localImpressaoVO) {
        this.localImpressaoVO = localImpressaoVO;
    }

    private Integer codigoEmpresa;

    public LocalImpressaoControle() throws Exception {
        obterUsuarioLogado();
        montarListaSelectItemEmpresa();
        setControleConsulta(new ControleConsulta());
        povoarCombos();
        setMensagemID("msg_entre_dados");
    }

    public String novo() {
        try {
            reset();
            this.localImpressaoVO.setEmpresaVO(getEmpresaLogado());
            setErro(false);
            setSucesso(true);
            setMensagemID("msg_entre_dados");
        } catch (Exception e) {
            setErro(true);
            setSucesso(false);
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
        return "editar";
    }

    public void reset() {
        setLocalImpressaoVO(new LocalImpressaoVO());
        setMensagemID("msg_entre_dados");
        setSucesso(false);
        setErro(false);
    }

    public String editar() {
        Integer codigoConsulta = Integer.parseInt(request().getParameter("chavePrimaria"));
        try {
            LocalImpressaoVO obj = getFacade().getLocalImpressao().consultarPorCodigo(codigoConsulta, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            obj.setNovoObj(false);
            obj.registrarObjetoVOAntesDaAlteracao();
            setLocalImpressaoVO(obj);
        } catch (Exception e) {
            setErro(true);
            setSucesso(false);
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
        return "editar";
    }

    public String gravar() {
        try {
            if (localImpressaoVO.isNovoObj().booleanValue()) {

                getFacade().getLocalImpressao().incluir(this.localImpressaoVO);
                incluirLogInclusao();
            } else {
                getFacade().getLocalImpressao().alterar(this.localImpressaoVO);
                incluirLogAlteracao();
            }
            setMensagemID("msg_dados_gravados");
            setSucesso(true);
            setErro(false);
            return "editar";
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
            return "editar";
        }
    }

    public void incluirLogInclusao() throws Exception {
        try {
            this.localImpressaoVO.setObjetoVOAntesAlteracao(new LocalImpressaoVO());
            this.localImpressaoVO.setNovoObj(true);
            registrarLogObjetoVO(this.localImpressaoVO, this.localImpressaoVO.getCodigo(), ENTIDADE_LOG, 0);
            this.localImpressaoVO.setNovoObj(false);
            this.localImpressaoVO.registrarObjetoVOAntesDaAlteracao();
        } catch (Exception e) {
            registrarLogErroObjetoVO(ENTIDADE_LOG, this.localImpressaoVO.getCodigo(),
                    "ERRO AO GERAR LOG DE INCLUS?O DE " + ENTIDADE_LOG, this.getUsuarioLogado().getNome(), "");
            e.printStackTrace();
        }
    }

    public void incluirLogAlteracao() throws Exception {
        try {
            registrarLogObjetoVO(this.localImpressaoVO, this.localImpressaoVO.getCodigo(), ENTIDADE_LOG, 0);
        } catch (Exception e) {
            registrarLogErroObjetoVO(ENTIDADE_LOG, this.localImpressaoVO.getCodigo(),
                    "ERRO AO GERAR LOG DE ALTERAÃ?O DE " + ENTIDADE_LOG, this.getUsuarioLogado().getNome(), "");
            e.printStackTrace();
        }
    }

    public String excluir() {
        try {
            registrarLogExclusaoObjetoVO(this.localImpressaoVO, this.localImpressaoVO.getCodigo().intValue(), ENTIDADE_LOG, 0);
            getFacade().getLocalImpressao().excluir(this.localImpressaoVO);
            setLocalImpressaoVO(new LocalImpressaoVO());
            setMensagemID("msg_dados_excluidos");
            setSucesso(true);
            setErro(false);
            return "consultar";
        } catch (Exception e) {
            if (e.getMessage().contains("ERRO: atualização ou exclusão em tabela \"localimpressao\" viola restrição de chave estrangeira")) {
                setMensagemDetalhada("Esta categoria não pode ser excluída, pois está sendo utilizada!");
            } else {
                setMensagemDetalhada("msg_erro", e.getMessage());
            }
            setSucesso(false);
            setErro(true);
            return "editar";
        }
    }

    public void povoarCombos() {
        try {
            if ((getEmpresaLogado() != null) && (getEmpresaLogado().getCodigo() != null) && (getEmpresaLogado().getCodigo() > 0)) {
                this.codigoEmpresa = getEmpresaLogado().getCodigo();
            }
            montarComboLocalImpressao();
            montarListaEmpresas();
            setErro(false);
            setSucesso(true);
        } catch (Exception e) {
            setErro(true);
            setSucesso(false);
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void adicionarLocalImpressao() throws Exception {
        try {
            this.setLocalImpressaoVO(new LocalImpressaoVO());
            setMensagemID("msg_dados_adicionados");

        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());

        }
    }

    public void irPaginaInicial() throws Exception {
        controleConsulta.setPaginaAtual(1);
        this.consultar();
    }

    public void irPaginaAnterior() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getPaginaAtual() - 1);
        this.consultar();
    }

    public void irPaginaPosterior() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getPaginaAtual() + 1);
        this.consultar();
    }

    public void irPaginaFinal() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getNrTotalPaginas());
        this.consultar();
    }

    public List getTipoConsultaCombo() {
        List<SelectItem> itens = new ArrayList();
        itens.add(new SelectItem("codigo", "C¾digo"));
        itens.add(new SelectItem("nome", "Nome"));
        return itens;
    }

    public String inicializarConsultar() {
        setPaginaAtualDeTodas("0/0");
        setListaConsulta(new ArrayList());
        definirVisibilidadeLinksNavegacao(0, 0);
        setMensagemID("msg_entre_prmconsulta");
        setSucesso(false);
        setErro(false);
        return "consultar";
    }

    protected boolean inicializarFacades() {
        try {
            super.inicializarFacades();

            return true;
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro_conectarBD", e.getMessage());
            return false;
        }
    }

    public void exportar(ActionEvent evt) throws Exception {
        ExportadorListaControle exportadorListaControle = (ExportadorListaControle) JSFUtilities.getFromRequest(ExportadorListaControle.class.getSimpleName());
        String paramsTableFiltrada = context().getExternalContext().getRequestParameterMap().get("paramsTableFiltrada");

        String[] split = paramsTableFiltrada.split(",");
        String campoOrdenacao = split[0].replace("[", "");
        String ordem = split[1];
        String filtro = split[2].replace("''", "");
        filtro = filtro.replace("]", "");
        List listaParaImpressao = getFacade().getLocalImpressao().consultarParaImpressao(filtro, ordem, campoOrdenacao, getEmpresaLogado() == null ? 0 : getEmpresaLogado().getCodigo());
        exportadorListaControle.exportar(evt, listaParaImpressao, filtro, null);

    }


    public List getListaSelectItemEmpresa() {
        return listaSelectItemEmpresa;
    }

    public void setListaSelectItemEmpresa(List listaSelectItemEmpresa) {
        this.listaSelectItemEmpresa = listaSelectItemEmpresa;
    }

    public void montarListaSelectItemEmpresa() throws Exception {
        List listaEmpresa = new ArrayList();
        listaEmpresa.add(new SelectItem(new Integer(0), ""));
        List listaConsulta = getFacade().getEmpresa().consultarPorNome("", false, false, Uteis.NIVELMONTARDADOS_TODOS);
        for (Object obj : listaConsulta) {
            EmpresaVO empresa = (EmpresaVO) obj;
            listaEmpresa.add(new SelectItem(empresa.getCodigo(), empresa.getNome()));
        }
        setListaSelectItemEmpresa(listaEmpresa);
    }

    public void realizarConsultaLogObjetoSelecionado() {
        localImpressaoVO = new LocalImpressaoVO();
        LoginControle loginControle = (LoginControle) context().getExternalContext().getSessionMap().get("LoginControle");
        loginControle.setNomeClasse("Local ImpressÒo");
        if (localImpressaoVO.getCodigo() != null && localImpressaoVO.getCodigo() != 0) {
            loginControle.consultarLogObjetoSelecionado(ENTIDADE_LOG, localImpressaoVO.getCodigo(), null);
        } else {
            loginControle.setListaConsultaLog(new ArrayList());
            loginControle.getListaConsultaLog().clear();
        }
        setOncompleteLog("abrirPopup('" + request().getContextPath() + "/faces/visualizadorLogForm.jsp', 'VisualizadorLog', 785, 595);");

    }

    public void setOncompleteLog(String oncompleteLog) {
        if (oncompleteLog == null) {
            oncompleteLog = "";
        }
        this.oncompleteLog = oncompleteLog;
    }

    public void gravarLocalAcessoNaSessao() {
        try {
            if ((this.nomeComputadorImpressao == null) || (this.nomeComputadorImpressao.trim().equals(""))) {
                throw new ConsistirException("Informe o Local de impressÒo.");
            }
            JSFUtilities.storeOnSession(LocalImpressaoVO.CHAVE_LOCAL_IMPRESSAO, this.nomeComputadorImpressao);
            setMsgAlert("Richfaces.hideModalPanel('modalLocalImpressao');alert('Local de impressÒo gravado com sucesso!');");
            setErro(false);
            setSucesso(true);
            setMensagemID("msg_entre_dados");
        } catch (Exception e) {
            setErro(true);
            setSucesso(false);
            setMensagemDetalhada("msg_erro", e.getMessage());
        }


    }

    public void montarComboLocalImpressao() throws Exception {
        listaLocalImpressao = new ArrayList<SelectItem>();
        listaLocalImpressao.add(new SelectItem("", "Selecione um Local"));
        if ((codigoEmpresa != null) && (codigoEmpresa > 0)) {
            List<LocalImpressaoVO> lista = getFacade().getLocalImpressao().consultarLocalImpressao(codigoEmpresa, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if (lista != null) {
                for (LocalImpressaoVO localImpressaoVO : lista) {
                    listaLocalImpressao.add(new SelectItem(localImpressaoVO.getNomeComputador(), localImpressaoVO.getNomeComputador()));
                }
            } else {
                listaLocalImpressao.clear();
                listaLocalImpressao.add(new SelectItem("Nenhum Local Cadastrado", "Nenhum Local Cadastrado"));
            }
        }

    }

    public List<SelectItem> getListaLocalImpressao() {
        return listaLocalImpressao;
    }

    public void setListaLocalImpressao(List<SelectItem> listaLocalImpressao) {
        this.listaLocalImpressao = listaLocalImpressao;
    }

    public String getNomeComputadorImpressao() {
        return nomeComputadorImpressao;
    }

    public void setNomeComputadorImpressao(String nomeComputadorImpressao) {
        this.nomeComputadorImpressao = nomeComputadorImpressao;
    }

    public Integer getCodigoEmpresa() {
        return codigoEmpresa;
    }

    public void setCodigoEmpresa(Integer codigoEmpresa) {
        this.codigoEmpresa = codigoEmpresa;
    }

    public void confirmarExcluir() {
        MensagemGenericaControle control = getControlador(MensagemGenericaControle.class);
        control.setMensagemDetalhada("", "");
        setMsgAlert("Richfaces.showModalPanel('mdlMensagemGenerica');");
        control.init("Exclusão de Categoria",
                "Deseja excluir o Local impressão?",
                this, "excluir", "", "", "", "grupoBtnExcluir");
    }

}

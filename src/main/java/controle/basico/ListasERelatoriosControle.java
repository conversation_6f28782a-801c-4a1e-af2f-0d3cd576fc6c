package controle.basico;

import br.com.pactosolucoes.ce.comuns.enumerador.Mes;
import br.com.pactosolucoes.comuns.to.FiltrosConsultaRelatorioBVsTO;
import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.enumeradores.OrigemSistemaEnum;
import br.com.pactosolucoes.enumeradores.SituacaoClienteEnum;
import br.com.pactosolucoes.enumeradores.SituacaoEnum;
import br.com.pactosolucoes.enumeradores.TipoColaboradorEnum;
import br.com.pactosolucoes.estrutura.ItemExportacaoEnum;
import controle.arquitetura.security.LoginControle;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.basico.ConfiguracaoSistemaVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.TipoColaboradorVO;
import negocio.comuns.contrato.ContratoDuracaoVO;
import negocio.comuns.contrato.ConvenioDescontoVO;
import negocio.comuns.crm.EventoVO;
import negocio.comuns.crm.GenericoTO;
import negocio.comuns.plano.ComposicaoVO;
import negocio.comuns.plano.ModalidadeVO;
import negocio.comuns.plano.PlanoVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Dominios;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.SelectItemOrdemValor;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import org.richfaces.component.UIDataTable;
import org.json.JSONObject;
import relatorio.controle.arquitetura.SuperControleRelatorio;
import relatorio.negocio.comuns.basico.BVsRelatorioTO;
import relatorio.negocio.comuns.basico.ItemRelatorioTO;
import servicos.bi.exportador.Exportador;
import servicos.bi.exportador.RelatorioBuilder;
import servicos.http.MetodoHttpEnum;
import servicos.http.RequestHttpService;
import servicos.http.RespostaHttpDTO;
import servicos.propriedades.PropsService;

import javax.faces.event.ActionEvent;
import javax.faces.model.SelectItem;
import java.io.File;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.Hashtable;
import java.util.List;
import java.util.Map;

public class ListasERelatoriosControle extends SuperControleRelatorio {

    private static final String SUBDIRETORIO_ARQUIVOS = "relatoriogeralcliente";

    private EmpresaVO empresaVO;
    private List<ColaboradorVO> professores;
    private int registrosTotais=0;
    private int registrosSemSexo=0;
    private int registrosSemDataNasc=0;
    private int registrosPessoasVelhas = 0;
    private int registrosDesconsiderados=0;
    private int registrosConsiderados= 0;
    private List<ColaboradorVO> professoresTreino;
    private List<ColaboradorVO> consultores;
    private List<SelectItem> listaSelectItemConvenios;
    private BVsRelatorioTO bVsRelatorioTO = new BVsRelatorioTO();
    private TipoColaboradorVO tipoColaboradorVO = new TipoColaboradorVO();
    private Boolean mostrarConteudo;
    private List<ModalidadeVO> listaModalidades = new ArrayList<ModalidadeVO>();
    private FiltrosConsultaRelatorioBVsTO filtrosConsulta;
    private List<SelectItem> listaEmpresa = new ArrayList<SelectItem>();
    private ItemRelatorioTO itemRelatorioTO = new ItemRelatorioTO();
    private List<ItemRelatorioTO> resultado = new ArrayList<ItemRelatorioTO>();
    private List<ItemRelatorioTO> resultadoGrafico = new ArrayList<ItemRelatorioTO>();
    private List<SelectItem> listaSelectItemPlanos;
    private String urlUploadArquivo;
    // filtros relatorioClientes
    private Integer codigoMes;
    private Integer codigoEmpresa;
    private Integer duracao = null;
    private String nome = "";
    private String rg = "";
    private String cpf = "";



    private StringBuilder dadosGrafico = null;
    private String sexo = "";
    private String filtroParQ = "";
    private Date inicioCadastro;
    private Date fimCadastro;
    private Integer inicioIdade = 0;
    private Integer fimIdade = null;
    private String situacaoCliente = "";
    private String situacaoContrato = "";
    private String situacaoApresentar = "";
    private String situacaoClienteApresentar = "";
    private Date inicioMatricula;
    private Date fimMatricula;
    private Date inicioRematricula;
    private Date finalRematricula;
    private Date inicioRenovacao;
    private Date finalRenovacao;
    private Integer inicioDiasSemAparecer = null;
    private Integer fimDiasSemAparecer = null;
    private Date inicioVencimento;
    private Date fimVencimento;
    private ColaboradorVO consultor = new ColaboradorVO();
    private ColaboradorVO professor = new ColaboradorVO();
    private String bairro = "";
    private String tabSelecionada = "tabAnalitico";
    private String situacaoTrancamento = "";
    private ItemRelatorioTO itemRelatorio = new ItemRelatorioTO();
    private boolean apresentarLinha1 = true;
    private boolean apresentarLinha2 = true;
    private boolean apresentarLinha3 = true;
    private boolean situacaoAtivo = false;
    private boolean situacaoInativo = false;
    private boolean situacaoTrancado = false;
    private boolean situacaoAtestado = false;
    private boolean situacaoCarencia = false;
    private boolean situacaoCancelado = false;
    private boolean situacaoVisitante = false;
    private boolean situacaoAVencer = false;
    private boolean situacaoVencido = false;
    private boolean situacaoDesistente = false;
    private boolean situacaoNormal = false;
    private boolean situacaoDependente = false;
    private Boolean abrirPopupDownload;
    private Integer convenio;
    private Integer plano;
    private String nomeRespContrato = "";
    private int situacaoModalidade = 1;
    private boolean tipoPlanoBolsa = false;
    private boolean tipoPlanoCredito = false;
    private Integer categoria;
    private String nomeRelatorio;
    private Boolean utilizaConfiguracaoSesc;
    private Boolean consultarTodasEmpresas;
    private List<GenericoTO> vezesSemana = new ArrayList<GenericoTO>();
    private Integer pacote;
    private List<SelectItem> listaSelectItemPacotes;
    private Integer codigoEventoSelecionado;
    private List<SelectItem> listaSelectItemEvento;
    private Date inicioLancamento;
    private Date finalLancamento;

    private boolean permissaoConsultaTodasEmpresas = false;
    private Integer filtroEmpresa;
    private List<SelectItem> listaDuracoesContratos;
    private ConfiguracaoSistemaVO configuracaoSistema;
    private String[] displayIdentificadorFront;
    //private OrigemSistemaEnum origemSistema;
    private boolean origemZW = false;
    private boolean origemAulaCheia = false;
    private boolean origemTreino = false;
    private boolean origemAppTreino = false;
    private boolean origemAppProfessor = false;
    private boolean origemAutoAtendimento = false;
    private boolean origemBuzzLead = false;
    private boolean origemVendasOnline = false;
    private boolean contratoConcomVarEmpresas = false;
    private boolean ultimoContratoCliente = true;
    private boolean situacaoGympass = false;
    private boolean situacaoAdimplentes = false;
    private boolean situacaoInadimplentes = false;
    private boolean mostrarSituacao = false;

    public String geFiltroOrigensContratos() {
        String origens = "";
        try {
            if (isOrigemZW()) {
                origens += OrigemSistemaEnum.ZW.getCodigo() + ",";
            }
            if (isOrigemAulaCheia()) {
                origens += OrigemSistemaEnum.AULA_CHEIA.getCodigo() + ",";
            }
            if (isOrigemTreino()) {
                origens += OrigemSistemaEnum.TREINO.getCodigo() + ",";
            }
            if (isOrigemAppTreino()) {
                origens += OrigemSistemaEnum.APP_TREINO.getCodigo() + ",";
            }
            if (isOrigemAppProfessor()) {
                origens += OrigemSistemaEnum.APP_PROFESSOR.getCodigo() + ",";
            }
            if (isOrigemAutoAtendimento()) {
                origens += OrigemSistemaEnum.AUTO_ATENDIMENTO.getCodigo() + ",";
            }
            if (isOrigemBuzzLead()) {
                origens += OrigemSistemaEnum.BUZZLEAD.getCodigo() + ",";
            }
            if (isOrigemVendasOnline()) {
                origens += OrigemSistemaEnum.VENDAS_ONLINE_2.getCodigo() + ",";
            }
            if (null == origens || origens.trim().length() == 0) {
                origens = null;
            }
            if (null != origens && origens.trim().length() > 0) {
                origens = origens.substring(0, origens.length() - 1);
            }
        } catch (Exception e) {
            e.printStackTrace();
            origens = null;
        }
        return origens;
    }

    public ListasERelatoriosControle() throws Exception {
        super();
        inicializarCodigoEmpresa();
        inicializarConfiguracaoSistema();
        identificacaoPessoalInternacional();
        utilizaConfiguracaoSesc = configuracaoSistema.getSesc();
        permissaoConsultaTodasEmpresas = permissao("ConsultarInfoTodasEmpresas");
        montarListaEvento();
        if (permissaoConsultaTodasEmpresas) {
            montarListaEmpresasComItemTodas();
            filtroEmpresa = getEmpresaLogado().getCodigo();
        }
//        Removendo esse carregarFiltros devido ao método de abrir sempre chamar em FuncionalidadeControle.java
//        carregarFiltros();
    }

    public void inicializarConfiguracaoSistema(){
        try {
            setConfiguracaoSistema((ConfiguracaoSistemaVO) JSFUtilities.getFromSession(JSFUtilities.CONFIGURACAO_SISTEMA));
        } catch (Exception e) {
            throw e;
        }
    }

    public String getTabSelecionada() {
        return tabSelecionada;
    }

    public void setTabSelecionada(String tabSelecionada) {
        this.tabSelecionada = tabSelecionada;
    }

    public String getDadosGrafico() {
        if(dadosGrafico == null) {
        dadosGrafico = new StringBuilder();
            return dadosGrafico.toString();
        }else
        return dadosGrafico.toString();
    }

    public List<ColaboradorVO> getProfessoresTreino() {
        if (professoresTreino == null)  {
            professoresTreino = new ArrayList<ColaboradorVO>();
        }
        return professoresTreino;
    }

    public void setProfessoresTreino(List<ColaboradorVO> professoresTreino) {
        this.professoresTreino = professoresTreino;
    }

    public List<ItemRelatorioTO> getResultadoGrafico() {
        return resultadoGrafico;
    }

    public void setResultadoGrafico(List<ItemRelatorioTO> resultadoGrafico) {
        this.resultadoGrafico = resultadoGrafico;
    }

    public void setDadosGrafico(StringBuilder dadosGrafico) {
        this.dadosGrafico = dadosGrafico;
    }
    public static String getIsDesignIReportRelatorioExcel(String nomeRel) {
        return ("designRelatorio" + File.separator + "estudio" + File.separator + "relatorioGeralExcel");
    }

    public static String getCaminhoSubRelatorio() {
        return ("relatorio" + File.separator + "designRelatorio" + File.separator + "listasRelatorios" + File.separator);
    }

    private void inicializarCodigoEmpresa() throws Exception {
        if (!getUsuarioLogado().getAdministrador()) {
            codigoEmpresa = getEmpresaLogado().getCodigo();
        }
        setEmpresaVO(new EmpresaVO());
    }
    public void selecionaTabSintetico(){
        setTabSelecionada("tabSintetico");
    }
    public void selecionaTabAnalitico(){
        setTabSelecionada("tabAnalitico");
    }
    public int getRegistrosDesconsiderados() {
        return registrosDesconsiderados;
    }

    public void setRegistrosDesconsiderados(int registrosDesconsiderados) {
        this.registrosDesconsiderados = registrosDesconsiderados;
    }

    public int getRegistrosConsiderados() {
        return registrosConsiderados;
    }

    public void setRegistrosConsiderados(int registrosConsiderados) {
        this.registrosConsiderados = registrosConsiderados;
    }

    public void povoarGraficoClientes(){
        registrosSemDataNasc=0;
        registrosSemSexo=0;
        registrosTotais=0;
        registrosPessoasVelhas=0;
        registrosConsiderados = 0;
        registrosDesconsiderados = 0;
        int avulsos=0;
        List<List<Integer>> faixaEtarias = new ArrayList<List<Integer>>();
        List<String> labelFaixaEtaria = new ArrayList<String>();
        String aspas = "\"";
        dadosGrafico = new StringBuilder();
        dadosGrafico.append("var chartData = [");
        labelFaixaEtaria.add("Idade 0 a 15 anos");
        labelFaixaEtaria.add("Idade 16 a 25 anos");
        labelFaixaEtaria.add("Idade 26 a 39 anos");
        labelFaixaEtaria.add("Idade 40 a 55 anos");
        labelFaixaEtaria.add("Idade 56 a 120 anos");
        //Idade 0 a 10 anos
        faixaEtarias.add(Arrays.asList(0,0));
        //Idade 11 a 18
        faixaEtarias.add(Arrays.asList(0,0));
        //Idade 18 a 22
        faixaEtarias.add(Arrays.asList(0,0));
        //Idade 23 a 35
        faixaEtarias.add(Arrays.asList(0,0));
        //Idade 36 a 120
        faixaEtarias.add(Arrays.asList(0, 0));

        setRegistrosTotais(getResultado().size());
        for (ItemRelatorioTO item : getResultado()) {

            if (item.getDataNascimento() != null && (!item.getSexo().equals("") || item.getSexo() != null) && !item.getSexo().isEmpty()
                    && !((Calendario.hoje().getYear() - item.getDataNascimento().getYear()) > 120) && !item.getDataNascimentoApresentar().equals("")
                    ) {
                faixaEtarias.set(0, calculaFaixaEtariaSumario(0, 15, item, faixaEtarias.get(0)));
                faixaEtarias.set(1, calculaFaixaEtaria(16, 25, item, faixaEtarias.get(1)));
                faixaEtarias.set(2, calculaFaixaEtaria(26, 39, item, faixaEtarias.get(2)));
                faixaEtarias.set(3, calculaFaixaEtaria(40, 55, item, faixaEtarias.get(3)));
                faixaEtarias.set(4, calculaFaixaEtaria(56, 120, item, faixaEtarias.get(4)));
            } else if (item.getDataNascimento() == null || item.getDataNascimentoApresentar().equals("")) {
                registrosSemDataNasc++;
            } else if (item.getSexo().equals("") || item.getSexo() == null || item.getSexo().isEmpty()) {
                registrosSemSexo++;
            } else if ((Calendario.hoje().getYear() - item.getDataNascimento().getYear()) > 120) {
                registrosPessoasVelhas++;
            } else {
                registrosDesconsiderados++;
            }
        }
        for (int e = 0; e < faixaEtarias.size(); e++) {
            dadosGrafico.append("{year: ").append(aspas).append(labelFaixaEtaria.get(e)).append(aspas);
            dadosGrafico.append(",homen:");
            dadosGrafico.append(faixaEtarias.get(e).get(0).toString());
            dadosGrafico.append(",");
            dadosGrafico.append("mulher:");
            dadosGrafico.append(faixaEtarias.get(e).get(1).toString());
            dadosGrafico.append("}");
            if ((e + 1) < faixaEtarias.size()) {
                dadosGrafico.append(",");
            }
        }
        dadosGrafico.append("];");
        dadosGrafico.append("");
    }

    public void contabilizarRegistroGrafico() {
        registrosConsiderados++;
    }

    public List<Integer> calculaFaixaEtariaSumario(int idadeInicial, int idadeFinal, ItemRelatorioTO pessoa, List<Integer> faixaEtarias) {
        int idade = 0;
        idade = Calendario.hoje().getYear() - pessoa.getDataNascimento().getYear();
        if(idade >= idadeInicial && idade <= idadeFinal) {

            if (!pessoa.getSexo().isEmpty() && pessoa.getSexo().equals("Masculino")) {
                int valor = (Integer) faixaEtarias.get(0) + 1;
                contabilizarRegistroGrafico();
                faixaEtarias.set(0, valor);
            } else if (!pessoa.getSexo().isEmpty() && pessoa.getSexo().equals("Feminino")) {
                int valor = (Integer) faixaEtarias.get(1) + 1;
                contabilizarRegistroGrafico();
                faixaEtarias.set(1, valor);
            }else if(idade==0){
                registrosDesconsiderados++;
            }else if(!pessoa.getSexo().isEmpty() && !pessoa.getSexo().equals("Masculino") && !pessoa.getSexo().equals("Feminino")){
                registrosDesconsiderados++;
            }else if(pessoa.getSexo().isEmpty()){
                registrosDesconsiderados++;
            }
        }
        return faixaEtarias;
    }

    public List<Integer> calculaFaixaEtaria(int idadeInicial, int idadeFinal, ItemRelatorioTO pessoa, List<Integer> faixaEtarias) {
        int idade = Calendario.hoje().getYear() - pessoa.getDataNascimento().getYear();
        if (idade >= idadeInicial && idade <= idadeFinal) {
            if (!pessoa.getSexo().isEmpty() && pessoa.getSexo().equals("Masculino")) {
                int valor = (Integer) faixaEtarias.get(0) + 1;
                contabilizarRegistroGrafico();
                faixaEtarias.set(0, valor);
            } else if (!pessoa.getSexo().isEmpty() && pessoa.getSexo().equals("Feminino")) {
                int valor = (Integer) faixaEtarias.get(1) + 1;
                contabilizarRegistroGrafico();
                faixaEtarias.set(1, valor);
            }
        }
        return faixaEtarias;
    }


    public void consultarGeralAlunos() {
        try {
            if (getCodigoEmpresa() != null && getCodigoEmpresa() > 0) {
                setEmpresaVO(getFacade().getEmpresa().consultarPorChavePrimaria(getCodigoEmpresa(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            } else {
                setEmpresaVO(new EmpresaVO());
            }

            Integer codEmpresaConsultar = codigoEmpresa;
            if (getConsultarTodasEmpresas()) {
                codEmpresaConsultar = 0;
            }

            codEmpresaConsultar = permissaoConsultaTodasEmpresas ? filtroEmpresa : codEmpresaConsultar;
            if (permissaoConsultaTodasEmpresas){
                List<EmpresaVO> empresas = getFacade().getEmpresa().consultarPorNome("",true, false, Uteis.NIVELMONTARDADOS_TELACONSULTA);
                for (int i= 0;empresas.size()>i;i++){
                    if (empresas.get(i).getPermiteContratosConcomintante()){
                        setContratoConcomVarEmpresas(true);
                        break;
                    }
                }
            }

            apresentarSituacao();
            processarSituacao();

            String listMatAlunosFiltroParQ = null;
            try {
                listMatAlunosFiltroParQ = consultarAlunosFiltroParQAssinadoTreino(filtroParQ, codEmpresaConsultar);
            } catch (Exception e) {}

            resultado = getFacade().getCliente().relatorioGeralAlunos(inicioVencimento, fimVencimento, inicioCadastro, fimCadastro,
                    inicioMatricula, fimMatricula, inicioRematricula, finalRematricula, inicioRenovacao, finalRenovacao, nome, sexo, situacaoCliente, situacaoContrato, codEmpresaConsultar, inicioIdade, fimIdade, codigoMes, inicioDiasSemAparecer,
                    fimDiasSemAparecer, bairro, consultor, professor,
                    obterSelecionadoLista(consultores), obterSelecionadoLista(professores),obterSelecionadoLista(professoresTreino),listaModalidades, duracao, convenio, plano,rg,tipoPlanoBolsa,tipoPlanoCredito,
                    getCategoria(), getVezesSemana(), pacote, inicioLancamento, finalLancamento, ultimoContratoCliente, geFiltroOrigensContratos(),this.codigoEventoSelecionado,this.situacaoDependente,this.situacaoGympass, this.situacaoAdimplentes, this.situacaoInadimplentes, listMatAlunosFiltroParQ, filtroParQ);
           // povoarGraficoClientes();
            selecionaTabAnalitico();
            povoarGraficoClientes();
        } catch (Exception e) {
            setMensagemID("msg_erro");
            setMensagemDetalhada("msg_erro", e.getMessage());
            montarMsgAlert(getMensagemDetalhada());
        }
    }

    private String consultarAlunosFiltroParQAssinadoTreino(String filtroParQ, Integer codigoEmpresa) throws Exception {
        try {
            LoginControle loginControle = (LoginControle) JSFUtilities.getFromSession("LoginControle");

            if (loginControle != null && loginControle.isApresentarModuloNovoTreino()) {
                String filtroParQFinal = UteisValidacao.emptyString(filtroParQ) ? "todos" : filtroParQ;

                String urlTreino = PropsService.getPropertyValue(getKey(), PropsService.urlTreino);
                String fullUrl = urlTreino + "/prest/avaliacao/" + getKey() + "/obter-mat-alunos-parq/" + codigoEmpresa + "/" + filtroParQFinal;
                if (getEmpresaLogado() != null && !UteisValidacao.emptyNumber(getEmpresaLogado().getDiasParaVencimentoParq())) {
                    fullUrl += "?diasParaVencimentoParq=" + getEmpresaLogado().getDiasParaVencimentoParq();
                }
                RequestHttpService httpService = new RequestHttpService();
                RespostaHttpDTO respostaHttpDTO = httpService.executeRequest(fullUrl, null, null, null, MetodoHttpEnum.GET);
                String retornoConsulta = respostaHttpDTO.getResponse();
                if (new JSONObject(retornoConsulta).has("return")) {
                    return new JSONObject(retornoConsulta).optString("return");
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }

        return null;
    }

    private static List<ColaboradorVO> obterSelecionadoLista(final List<ColaboradorVO> lista){
        List<ColaboradorVO> i = new ArrayList<ColaboradorVO>();
        for(ColaboradorVO obj : lista){
            if(obj.getColaboradorEscolhido()){
                i.add(obj);
            }
        }
        return i;
    }
    //----------------------------- EXCEL--------------------------------------------------
    public void acaoImprimirExcel() {
        String result = null;
        limparMsg();
        setMsgAlert("");

        try {
            if (!resultado.isEmpty()) {
                UIDataTable dataTable = (UIDataTable) context().getViewRoot().findComponent("form:resultados");
                if (dataTable != null) {
                    String colunaOrdenacao = JSFUtilities.getColunaOrdenacao(dataTable);
                    if (!colunaOrdenacao.isEmpty()) {
                        String[] params = colunaOrdenacao.split(":");
                        Ordenacao.ordenarLista(resultado, params[0]);
                        if (params[1].equals("DESC")) {
                            Collections.reverse(resultado);
                        }
                    }
                }
            }
            validarPermissaoEIncluirLogExportacao(ItemExportacaoEnum.GERAL_CLIENTES, resultado.size(),montarFiltros(), "XLSX", "", "");
            setAbrirPopupDownload(true);
            DateTimeFormatter dtf = DateTimeFormatter.ofPattern("yyyyMMdd_HH.mm.ss.SSS");
            LocalDateTime now = LocalDateTime.now();
            String nomeArquivo = "relatorioGeralExcel_"+dtf.format(now)+".xlsx";
            gerarRelatorioExcel(getResultado(), gravarArquivoRelatorio(nomeArquivo));
            setMsgAlert("location.href='DownloadSV?mimeType=application/vnd.ms-excel&relatorio=" + nomeArquivo+"'");
        } catch (Exception e) {
            setAbrirPopupDownload(false);
            montarErro(e);
        }
    }

    /**
     * Gera o arquivo a ser utilizado pelo excel.
     * @param s
     * @throws Exception
     */
    private File gravarArquivoRelatorio(String s) throws Exception{
        File arquivo = new File(Uteis.obterCaminhoWeb() + File.separator + "relatorio" + File.separator + s);
        if (arquivo.exists()) {
            arquivo.delete();
        }else{
            new File(getDiretorioArquivos() + File.separator + SUBDIRETORIO_ARQUIVOS).mkdirs();
        }
        arquivo.createNewFile();
        return arquivo;
    }

    private void gerarRelatorioExcel(List<ItemRelatorioTO> resultado, File arquivo) throws  Exception{
        String filtros = montarFiltros();
        RelatorioBuilder relatorio = new RelatorioBuilder();
        relatorio.dado(resultado);
        relatorio.titulo("Relatório Geral de Clientes");
        for(String filtro : filtros.split("\\|;")){
            String filtroSeparado[] = filtro.split(":");
            relatorio.addFiltro(filtroSeparado[0], filtroSeparado[1]);
        }
        relatorio.addColuna("Matrícula", "matricula")
                .addColuna("Nome","nome")
                .addColuna("Documento","documento")
                .addColuna("Cnpj","cnpj")
                .addColuna("RG","rg")
                .addColuna("Telefone","telefone")
                .addColuna("Email","email")
                .addColuna("Nascimento","dataNascimento")
                .addColuna("Situação","situacaoClienteApresentar")
                .addColuna("Sexo","sexo")
                .addColuna("Logradouro","logradouro")
                .addColuna("Número","numero")
                .addColuna("Bairro","bairro")
                .addColuna("Cidade","cidade")
                .addColuna("CEP","cep")
                .addColuna("Complemento","complemento")
                .addColuna("Data Matricula","dataMatricula")
                .addColuna("Início Plano","inicioPlano")
                .addColuna("Vencimento Plano","vencimentoPlano")
                .addColuna("Duração do Plano","duracaoApresentar")
                .addColuna("Data de Lançamento", "dataLancamento");
        if(configuracaoSistema.getSesc()) {
                    relatorio.addColuna("Habilitação Sesc", "habilitacaoSesc")
                            .addColuna("Validade Cartão Sesc", "validadeCartaoSesc");

                }

                relatorio.addColuna("Último Acesso","dataUltimoAcesso")
                .addColuna("Plano","plano")
                .addColuna("Cadastro","dataCadastro")
                .addColuna("Consultor/Professor","consultor")
                .addColuna("Consultor","nomeConsultor")
                .addColuna("Professores","nomeProfessores")
                .addColuna("Professor(TreinoWeb)","professorTreino")
                .addColuna("Responsável Contrato","nomeRespContrato")
                .addColuna("Estado Civil","estadoCivil")
                .addColuna("Profissão","profissao")
                .addColuna("Grupo de Risco","grupoRiscoApresentar")
                .addColuna("Grupo Desconto", "grupoDesconto")
                .addColuna("Categoria","categoria")
                .addColuna("Empresa","nomeEmpresa")
                .addColuna("Modalidades","modalidades")
                .addColuna("Vezes na semana","vezesSemana")
                .addColuna("Evento","evento")
                .addColuna("Par-Q","parq");
        Exportador.exportarExcel(relatorio, arquivo);
    }

    public Boolean getAbrirPopupDownload() {
        if (abrirPopupDownload == null) {
            abrirPopupDownload = false;
        }
        return abrirPopupDownload;
    }
// <------------------------ IMPRIMIR PDF ---------------------->

    public void setAbrirPopupDownload(Boolean abrirPopupDownload) {
        this.abrirPopupDownload = abrirPopupDownload;
    }

    private Map<String, Object> prepareParams() throws Exception {
        Integer emp = getUsuarioLogado().getAdministrador() ? 0 : getEmpresaLogado().getCodigo();
        EmpresaVO empre = new EmpresaVO();
        if (emp != 0) {
            empre = getFacade().getEmpresa().consultarPorChavePrimaria(emp, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        }

        Map<String, Object> params = new HashMap<String, Object>();
        params.put("nomeRelatorio", "ListaAlunos");
        params.put("nomeEmpresa", empre.getNome());

        params.put("tituloRelatorio", "Relatório Geral de Clientes");
        params.put("nomeDesignIReport", getDesignIReportRelatorio());
        params.put("usuario", getUsuarioLogado().getNomeAbreviado());

        params.put("listaObjetos", resultado);
        params.put("enderecoEmpresa", empre.getEndereco());
        params.put("cidadeEmpresa", empre.getCidade().getNome());
        params.put("totalClientes", getTotalItens());
        params.put("apresentarLinha1", isApresentarLinha1());
        params.put("apresentarLinha2", isApresentarLinha2());
        params.put("apresentarLinha3", isApresentarLinha3());
        params.put("dadosImpressao", getDadosImpressao());
        String filtros = montarFiltros();
        params.put("filtro", filtros.replaceAll("\\|;", "\\|"));

        return params;
    }

    @Override
    public String getNomeRefPastaRelatorioRelatorioGeradoAgora() {
        String hRef = getNomeArquivoRelatorioGeradoAgora();
        if (!hRef.isEmpty()) {
            return "location.href=\"../../relatorio/" + hRef + "\"";
        } else {
            return "";
        }
    }

    public String getDesignIReportRelatorio() {
        return ("relatorio" + File.separator + "designRelatorio" + File.separator + "listasRelatorios" + File.separator + "ListaAlunos.jrxml");
    }

    public void imprimirListaRelatorio() throws Exception {
        try {
            if (!resultado.isEmpty()) {
                UIDataTable dataTable = (UIDataTable) context().getViewRoot().findComponent("form:resultados");
                if (dataTable != null) {
                    String colunaOrdenacao = JSFUtilities.getColunaOrdenacao(dataTable);
                    if (!colunaOrdenacao.isEmpty()) {
                        String[] params = colunaOrdenacao.split(":");
                        Ordenacao.ordenarLista(resultado, params[0]);
                        if (params[1].equals("DESC")) {
                            Collections.reverse(resultado);
                        }
                    }
                }
            }
            setMsgAlert("");
            if (resultado.isEmpty()) {
                throw new ConsistirException("Nenhum registro a ser impresso, faça a consulta novamente.");
            }
            validarPermissaoEIncluirLogExportacao(ItemExportacaoEnum.GERAL_CLIENTES, resultado.size(),montarFiltros(), "PDF", "", "");
            apresentarRelatorioObjetos(prepareParams());
            setMsgAlert("abrirPopupPDFImpressao('relatorio/" + getNomeArquivoRelatorioGeradoAgora() + "','', 780, 595); Richfaces.hideModalPanel('relatorioImprimir');");
            setMensagemDetalhada("", "");
            setMensagemID("msg_entre_prmrelatorio");
        } catch (Exception e) {
            montarErro(e);
        }
    }

    //----------------------------FIM IMPRIMIR PDF-----------------------------------------------------------
    private void processarSituacao() {
        StringBuilder situacaoContrato = new StringBuilder("");
        StringBuilder situacaoCliente = new StringBuilder("");
        if (isSituacaoAtivo()) {
            situacaoCliente.append(",AT");
        }
        if (isSituacaoInativo()) {
            situacaoCliente.append(",IN");
        }
        if (isSituacaoTrancado()) {
            situacaoCliente.append(",TR");
        }
        if (isSituacaoVisitante()) {
            situacaoCliente.append(",VI");
        }
        if (isSituacaoAtestado()) {
            situacaoContrato.append(",AE");
        }
        if (isSituacaoCancelado()) {
            situacaoContrato.append(",CA");
        }
        if (isSituacaoAVencer()) {
            situacaoContrato.append(",AV");
        }
        if (isSituacaoNormal()) {
            situacaoContrato.append(",NO");
        }
        if (isSituacaoVencido()) {
            situacaoContrato.append(",VE");
        }
        if (isSituacaoDesistente()) {
            situacaoContrato.append(",DE");
        }
        if (isSituacaoCarencia()) {
            situacaoContrato.append(",CR");
        }
        setSituacaoCliente(situacaoCliente.toString().replaceFirst(",", ""));
        setSituacaoContrato(situacaoContrato.toString().replaceFirst(",", ""));
    }

    //-----------------------SITUAÇÃO APRESENTAR------------------------------------
    private String apresentarSituacao() {
        StringBuilder situacaoApresentarSB = new StringBuilder();

        if (isSituacaoAtivo()) {
            situacaoApresentarSB.append(SituacaoClienteEnum.ATIVO.getDescricao()).append(", ");
        }
        if (isSituacaoInativo()) {
            situacaoApresentarSB.append(SituacaoClienteEnum.INATIVO.getDescricao()).append(", ");
        }
        if (isSituacaoVisitante()) {
            situacaoApresentarSB.append(SituacaoClienteEnum.VISITANTE.getDescricao()).append(", ");
        }
        if (isSituacaoTrancado()) {
            situacaoApresentarSB.append(SituacaoClienteEnum.TRANCADO.getDescricao()).append(", ");
        }
        if (isSituacaoAVencer()) {
            situacaoApresentarSB.append(SituacaoEnum.A_VENCER.getDescricao()).append(", ");
        }
        if (isSituacaoNormal()) {
            situacaoApresentarSB.append(SituacaoEnum.NORMAL.getDescricao()).append(", ");
        }
        if (isSituacaoCancelado()) {
            situacaoApresentarSB.append(SituacaoEnum.CANCELADO.getDescricao()).append(", ");
        }
        if (isSituacaoVencido()) {
            situacaoApresentarSB.append(SituacaoEnum.VENCIDO.getDescricao()).append(", ");
        }
        if (isSituacaoAtestado()) {
            situacaoApresentarSB.append(SituacaoEnum.ATESTADO.getDescricao()).append(", ");
        }
        if (isSituacaoDesistente()) {
            situacaoApresentarSB.append(SituacaoEnum.DESISTENCIA.getDescricao()).append(", ");
        }
        if (isSituacaoCarencia()) {
            situacaoApresentarSB.append(SituacaoEnum.CARENCIA.getDescricao()).append(", ");
        }
        String apresentarSituacao = situacaoApresentarSB.toString();
        if (apresentarSituacao.length() > 0) {
            apresentarSituacao = apresentarSituacao.trim().substring(0, (apresentarSituacao.length() - 2));
        }
        setSituacaoApresentar(apresentarSituacao);
        return getSituacaoApresentar();
    }

    public String getSituacaoClienteApresentar() {
        StringBuilder st = new StringBuilder();
        if (situacaoContrato == null) {
            st.append(" ");
        }
        if (situacaoContrato.equals("AV")) {
            st.append("A Vencer");
        }
        if (situacaoContrato.equals("CA")) {
            st.append("Cancelado");
        }
        if (situacaoContrato.equals("VE")) {
            st.append("Vencido");
        }
        if (situacaoContrato.equals("DE")) {
            st.append("Desistente");
        }
        if (situacaoContrato.equals("AE")) {
            st.append("Atestado");
        }
        if (situacaoContrato.equals("CR")) {
            st.append("Férias");
        }
        if (situacaoContrato.equals("NO")) {
            st.append("Normal");
        }

        if (situacaoCliente == null) {
            st.append(" ");
        }
        if (situacaoCliente.equals("AT")) {
            st.append("Ativo");
        }
        if (situacaoCliente.equals("IN")) {
            st.append("Inativo");
        }
        if (situacaoCliente.equals("VI")) {
            st.append("Visitante");
        }
        if (situacaoCliente.equals("TR")) {
            st.append("Trancado");
        }

        return situacaoClienteApresentar;
    }
    //------------------------BOTÃO LIMPAR-------------------------------------------

    public void setSituacaoClienteApresentar(String situacaoClienteApresentar) {
        this.situacaoClienteApresentar = situacaoClienteApresentar;
    }

    public void limparSituacao() {
        situacaoAtivo = false;
        situacaoInativo = false;
        situacaoTrancado = false;
        situacaoAtestado = false;
        situacaoCancelado = false;
        situacaoVisitante = false;
        situacaoAVencer = false;
        situacaoVencido = false;
        situacaoDesistente = false;
        situacaoNormal = false;
    }

    public void limparFiltros() {
        nome = "";
        inicioIdade = null;
        fimIdade = null;
        sexo = "";
        bairro = "";
        codigoMes = null;
        inicioDiasSemAparecer = null;
        fimDiasSemAparecer = null;
        situacaoCliente = "";
        situacaoContrato = "";
        professores = null;
        consultores = null;
        professoresTreino = null;
        listaModalidades = null;
        duracao = null;
        tipoPlanoBolsa = false;
        tipoPlanoCredito = false; 
        limparVencimento();
        limparMatricula();
        limparCadastro();
        limparSituacao();
        limparVezes();
    }

    public void limparVezes(){
        for(GenericoTO g : getVezesSemana()){
            g.setSelecionado(false);
        }
    }

    public String limparVencimento() {
        inicioVencimento = null;
        fimVencimento = null;
        return "";
    }

    public String limparMatricula() {
        inicioMatricula = null;
        fimMatricula = null;
        return "";
    }

    public String limparRematricula() {
        inicioRematricula = null;
        finalRematricula = null;
        return "";
    }

    public String limparRenovacao() {
        inicioRenovacao = null;
        finalRenovacao = null;
        return "";
    }

    public void limparCadastro() {
        inicioCadastro = null;
        fimCadastro = null;
    }

    public void prepararImpr() {
        ItemRelatorioTO impr = (ItemRelatorioTO) context().getExternalContext().getRequestMap().get("impr");
        if (impr != null) {
            setItemRelatorio(impr);
        }
    }

    public void prepararConsulta() {
        ItemRelatorioTO item = (ItemRelatorioTO) context().getExternalContext().getRequestMap().get("item");
        if (item != null) {
            setItemRelatorio(item);
        }
    }

    public int getTotalItens() {
        return getResultado().size();
    }

    public String getDadosImpressao() throws Exception {
        StringBuilder filtros = new StringBuilder();
        boolean pontuacao = true;
        pontuacao = pontuacao && (isApresentarLinha2() || isApresentarLinha3());
        if (isApresentarLinha1()) {
            filtros.append("Dados Cadastrais");
            if (pontuacao) {
                filtros.append(", ");
            } else {
                filtros.append(".");
            }
        }
        pontuacao = pontuacao && (isApresentarLinha3());
        if (isApresentarLinha2()) {
            filtros.append("Endereço");
            if (pontuacao) {
                filtros.append(", ");
            }
        }
        if (isApresentarLinha3()) {
            filtros.append("Contratos e Planos.");
        }
        return filtros.toString();
    }

    public void irParaTelaCliente() {
        ItemRelatorioTO obj = (ItemRelatorioTO) context().getExternalContext().getRequestMap().get("item");
        try {
            if (obj == null) {
                throw new Exception("Cliente Não Encontrado.");
            } else {
                irParaTelaCliente(obj.getClienteVO());
            }
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void prepareEditar(ActionEvent evt) {
        setNavigationCase(tratarNavigationCaseIntegracaoModulo("editar",
                "modulo_visualiza_cliente", evt));

    }

    public void inicializarEmpresaLogado() throws Exception {
        if (getEmpresaLogado() != null && getEmpresaLogado().getCodigo().intValue() != 0) {
            getEmpresaVO().setCodigo(getEmpresaLogado().getCodigo());
            getEmpresaVO().setNome(getEmpresaLogado().getNome());
        }
    }

    public void inicializarUsuarioLogado() throws Exception {
        if (getUsuarioLogado() != null && getUsuarioLogado().getCodigo().intValue() != 0) {
            getItemRelatorioTO().setUsuarioVO(new UsuarioVO());
            getItemRelatorioTO().getUsuarioVO().setCodigo(getUsuarioLogado().getCodigo());
            getItemRelatorioTO().getUsuarioVO().setNome(getUsuarioLogado().getNome());
            getItemRelatorioTO().getUsuarioVO().setAdministrador(getUsuarioLogado().getAdministrador());
        }
    }

//     <------------------------ LISTAS ---------------------->
    public void carregarDuracoesContrato() throws Exception {
        List<SelectItem> lista = new ArrayList<SelectItem>();
        lista.add(new SelectItem(0, ""));
        List<ContratoDuracaoVO> listaNumerosMeses = getFacade().getContratoDuracao().consultarNumeroMeses();
        for (ContratoDuracaoVO duracao : listaNumerosMeses) {
            lista.add(new SelectItem(duracao.getNumeroMeses().intValue(), duracao.getNumeroMeses().toString()));
        }
        setListaDuracoesContratos(lista);
    }


    public List<SelectItem> getListaDuracoesContratos() {
        if (listaDuracoesContratos == null){
           listaDuracoesContratos = new ArrayList<>();
        }
        return listaDuracoesContratos;
    }

    public void setListaDuracoesContratos(List<SelectItem> listaDuracoesContratos) {
        this.listaDuracoesContratos = listaDuracoesContratos;
    }

    public List<SelectItem> getListaConvenio() throws Exception {

        Integer codEmpresa = 0;
        if (!getUsuarioLogado().getAdministrador()) {
            codEmpresa = getEmpresaLogado().getCodigo();
        }

        List<SelectItem> listaConv = new ArrayList<SelectItem>();
        listaConv.add(new SelectItem(0, ""));
        List<ConvenioDescontoVO> listaConvenios = getFacade().getConvenioDesconto().consultarPorCodigo(0, codEmpresa, false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        for (ConvenioDescontoVO convenio : listaConvenios) {
            listaConv.add(new SelectItem(convenio.getCodigo(), convenio.getDescricao()));
        }
        Ordenacao.ordenarLista(listaConv, "label");
        return listaConv;
    }

    public List<SelectItem> getListaPlano() throws Exception {

        List<SelectItem> listaPlano = new ArrayList<SelectItem>();
        listaPlano.add(new SelectItem(0, ""));
        if (permissao("ConsultarInfoTodasEmpresas") || codigoEmpresa == null) {
            codigoEmpresa = 0;
        }
        List<PlanoVO> listaPlanos = getFacade().getPlano().consultarPorDescricao("", codigoEmpresa, false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        for (PlanoVO plano : listaPlanos) {
            listaPlano.add(new SelectItem(plano.getCodigo(), plano.getDescricao()));
        }
        Ordenacao.ordenarLista(listaPlano, "label");
        return listaPlano;
    }

    public List<SelectItem> getListaPacote() throws Exception {

        List<SelectItem> listaPacote = new ArrayList<SelectItem>();
        listaPacote.add(new SelectItem(0, ""));
        if (permissao("ConsultarInfoTodasEmpresas") || codigoEmpresa == null) {
            codigoEmpresa = 0;
        }
        List<ComposicaoVO> listaPacotes = getFacade().getComposicao().consultarPorDescricao("", codigoEmpresa, false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        for (ComposicaoVO pacote : listaPacotes) {
            listaPacote.add(new SelectItem(pacote.getCodigo(), pacote.getDescricao()));
        }
        Ordenacao.ordenarLista(listaPacote, "label");
        return listaPacote;
    }

    private void montarListaEvento() throws Exception {
        if (this.listaSelectItemEvento == null){
            this.listaSelectItemEvento = new ArrayList<>();
            this.listaSelectItemEvento.add(new SelectItem(0, ""));
            List<EventoVO> lista = getFacade().getEvento().consultarPorNomeEvento("",Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if (lista != null){
                for (EventoVO eventoVO: lista){
                    this.listaSelectItemEvento.add(new SelectItem(eventoVO.getCodigo(), eventoVO.getDescricao()));
                }
            }
        }

    }

    public List getListaSelectItemSexo() throws Exception {
        List objs = new ArrayList();
        objs.add(new SelectItem("F", "Feminino"));
        objs.add(new SelectItem("M", "Masculino"));
        return objs;
    }

    //    public void montarSelectItemProdutoFreePass(String prm) throws Exception {
//        List objs = new ArrayList();
//        objs.add(new SelectItem(new Integer(0), ""));
//        List resultadoConsulta = consultarProdutoFreePass(prm);
//        Iterator i = resultadoConsulta.iterator();
//        while (i.hasNext()) {
//            ProdutoVO obj = (ProdutoVO) i.next();
//            objs.add(new SelectItem(obj.getCodigo(), obj.getDescricao().toString() + " - " + obj.getNrDiasVigencia() + " Dias"));
//        }
//        setListaSelectItemProdutoFreePass(objs);
//    }
    public List getListaSituacaoCliente() throws Exception {
        List objs = new ArrayList();
        objs.add(new SelectItem("AE", "Atestado Médico"));
        Hashtable situacaoClientes = (Hashtable) Dominios.getSituacaoCliente();
        Enumeration keys = situacaoClientes.keys();

        while (keys.hasMoreElements()) {
            String value = (String) keys.nextElement();
            String label = (String) situacaoClientes.get(value);
            objs.add(new SelectItem(value, label));
        }
        SelectItemOrdemValor ordenador = new SelectItemOrdemValor();
        Collections.sort((List) objs, ordenador);

        return objs;
    }

    public List<SelectItem> getListaEmpresa() throws Exception {
        // montar lista de select itens
        if (listaEmpresa.isEmpty()) {
            List<EmpresaVO> empresas = getFacade().getEmpresa().consultarPorNome("",true, false, Uteis.NIVELMONTARDADOS_TELACONSULTA);
            for (EmpresaVO empresa : empresas) {
                listaEmpresa.add(new SelectItem(empresa.getCodigo(), empresa.getNome().toString()));
            }
        }
        return listaEmpresa;
    }

    public List<SelectItem> getListaMeses() throws Exception {
        // criar lista para retorno
        final List<SelectItem> Meses = new ArrayList<SelectItem>();
        // percorrer os valores do enumerador TipoMesesEnum
        for (Mes tipoMeses : Mes.values()) {
            // adicionar os meses na lista de retorno
            Meses.add(new SelectItem(tipoMeses.getCodigo(), tipoMeses.getDescricao()));
        }
        // retornar a lista
        return Meses;
    }

    public String montarFiltros() throws Exception {
        StringBuilder filtros = new StringBuilder();

        if (!getNome().isEmpty()) {
            filtros.append("Nome: ").append(getNome()).append(" |; ");
        }

        if (getCodigoEmpresa() != null && getCodigoEmpresa() > 0) {
            filtros.append("Empresa: ").append(empresaVO.getNome()).append(" |; ");
        }
        if (getInicioIdade() != null && getFimIdade() != null) {
            filtros.append("Idade Inicial: ").append(getInicioIdade()).append(" a ").append(getFimIdade()).append(" |; ");
        }
        if (getSexo() != null) {
            if (getSexo().equals("M")) {
                filtros.append("Sexo: Masculino").append(" |; ");
            }
            if (getSexo().equals("F")) {
                filtros.append("Sexo: Feminino").append(" |; ");
            }
        }
        if (!getBairro().isEmpty()) {
            filtros.append("Bairro: ").append(getBairro()).append(" |; ");
        }
        if (getInicioVencimento() != null && getFimVencimento() != null) {
            filtros.append("Data Vencimento: ").append(Uteis.getDataAplicandoFormatacao(getInicioVencimento(), "dd/MM/yyyy")).append(" a ").append(Uteis.getDataAplicandoFormatacao(getFimVencimento(), "dd/MM/yyyy")).append(" |; ");
        }
        if (getInicioCadastro() != null && getFimCadastro() != null) {
            filtros.append("Data Cadastro: ").append(Uteis.getDataAplicandoFormatacao(getInicioCadastro(), "dd/MM/yyyy")).append(" a ").append(Uteis.getDataAplicandoFormatacao(getFimCadastro(), "dd/MM/yyyy")).append(" |; ");
        }
        if (getInicioMatricula() != null && getFimMatricula() != null) {
            filtros.append("Data Matrícula: ").append(Uteis.getDataAplicandoFormatacao(getInicioMatricula(), "dd/MM/yyyy")).append(" a ").append(Uteis.getDataAplicandoFormatacao(getFimMatricula(), "dd/MM/yyyy")).append(" |; ");
        }
        if (getInicioDiasSemAparecer() != null && getFimDiasSemAparecer() != null) {
            filtros.append("Dias sem Comparecer: ").append(getInicioDiasSemAparecer()).append(" a ").append(getFimDiasSemAparecer()).append(" |; ");
        }
        if (getCodigoMes() != null) {
            filtros.append("Aniversáriantes do Mês: ").append(Mes.getMesPeloCodigo(getCodigoMes())).append(" |; ");
        }
        if (!UteisValidacao.emptyNumber(getDuracao())) {
            filtros.append("Duração: ").append(getDuracao()).append(" |; ");
        }
        if (getSituacaoApresentar() != null && !getSituacaoApresentar().isEmpty()) {
            filtros.append("Situação: ").append(getSituacaoApresentar()).append(" |; ");
        }
        if (!getConsultores().isEmpty()) {
            String consultoresSelecionados = "";
            for (ColaboradorVO colab : consultores) {
                if (colab.getColaboradorEscolhido()) {
                    consultoresSelecionados += colab.getPessoa().getNome() + ", ";
                }
            }
            consultoresSelecionados = consultoresSelecionados.trim();
            if (consultoresSelecionados.length() > 0) {
                filtros.append("Consultor: ");
                consultoresSelecionados = consultoresSelecionados.substring(0, (consultoresSelecionados.length() - 1));
                filtros.append(consultoresSelecionados).append(" |; ");
            }
        }

        if (!getProfessores().isEmpty()) {
            String professoresSelecionados = "";
            List<ColaboradorVO> listaFiltro = new ArrayList<ColaboradorVO>();
            listaFiltro.addAll(professores);
            listaFiltro.addAll(professoresTreino);
            for (ColaboradorVO prof : listaFiltro) {
                if (prof.getColaboradorEscolhido()) {
                    professoresSelecionados += prof.getPessoa().getNome() + ", ";
                }
            }
            professoresSelecionados = professoresSelecionados.trim();
            if (professoresSelecionados.length() > 0) {
                filtros.append("Professor: ");
                professoresSelecionados = professoresSelecionados.substring(0, (professoresSelecionados.length() - 1));
                filtros.append(professoresSelecionados).append(" |; ");
            }
        }
        
        String filtrosApresentar = filtros.toString();
        if (filtrosApresentar.length() > 0) {
            filtrosApresentar = filtrosApresentar.trim().substring(0, (filtrosApresentar.length() - 2));
        }
        return filtrosApresentar;
    }

    public String carregarFiltros() throws Exception {
        carregarConsultores();
        carregarProfessores();
        carregarProfessoresTreino();
        povoarListaModalidades();
        carregarDuracoesContrato();
        return "geralClientes";
    }

    public void carregarConsultores() throws Exception {
        consultores = getFacade().getColaborador().consultarPorNomeTipoVinculoPossivel("", TipoColaboradorEnum.CONSULTOR, getCodigoEmpresa() == null ? 0 : getCodigoEmpresa(), Uteis.NIVELMONTARDADOS_MINIMOS);
    }

    public void carregarProfessores() throws Exception {
        professores = getFacade().getColaborador().consultarPorNomeTipoVinculoPossivel("", TipoColaboradorEnum.PROFESSOR, getCodigoEmpresa() == null ? 0 : getCodigoEmpresa(), Uteis.NIVELMONTARDADOS_MINIMOS);
    }

    public void carregarProfessoresTreino() throws Exception {
        professoresTreino = getFacade().getColaborador().consultarPorNomeTipoVinculoPossivel("", TipoColaboradorEnum.PROFESSOR_TREINO, getCodigoEmpresa() == null ? 0 : getCodigoEmpresa(), Uteis.NIVELMONTARDADOS_MINIMOS);
    }

    public void povoarListaModalidades() throws Exception {
        if (getSituacaoModalidade() == 0) {
            listaModalidades = getFacade().getModalidade().consultarTodasModalidadesComLimite(getEmpresaLogado().getCodigo(), 0);
        } else if (getSituacaoModalidade() == 1) {
            listaModalidades = getFacade().getModalidade().consultarTodasModalidades(getEmpresaLogado().getCodigo(), true, null);
        } else if (getSituacaoModalidade() == 2) {
            listaModalidades = getFacade().getModalidade().consultarTodasModalidades(getEmpresaLogado().getCodigo(), false, null);
        }
    }
    // <------------------------ GETTER E SETTER ---------------------->
    public List<ColaboradorVO> getProfessores() throws Exception {
        if (professores == null) {
            professores = new ArrayList<ColaboradorVO>();
        }
        return professores;
    }

    public void setProfessores(List<ColaboradorVO> professores) {
        this.professores = professores;
    }

    public List<ColaboradorVO> getConsultores() {
        if (consultores == null) {
            consultores = new ArrayList<ColaboradorVO>();
        }
        return consultores;
    }

    public void setConsultores(List<ColaboradorVO> colaboradores) {
        this.consultores = colaboradores;

    }

    public BVsRelatorioTO getbVsRelatorioTO() {
        return bVsRelatorioTO;
    }

    public void setbVsRelatorioTO(BVsRelatorioTO bVsRelatorioTO) {
        this.bVsRelatorioTO = bVsRelatorioTO;
    }

    public TipoColaboradorVO getTipoColaboradorVO() {
        return tipoColaboradorVO;
    }

    public void setTipoColaboradorVO(TipoColaboradorVO tipoColaboradorVO) {
        this.tipoColaboradorVO = tipoColaboradorVO;
    }

    public Boolean getMostrarConteudo() {
        return mostrarConteudo;
    }

    public void setMostrarConteudo(Boolean mostrarConteudo) {
        this.mostrarConteudo = mostrarConteudo;
    }

    public FiltrosConsultaRelatorioBVsTO getFiltrosConsulta() {
        return filtrosConsulta;
    }

    public void setFiltrosConsulta(FiltrosConsultaRelatorioBVsTO filtrosConsulta) {
        this.filtrosConsulta = filtrosConsulta;
    }

    public List<ItemRelatorioTO> getResultado() {
        return resultado;
    }

    public void setResultado(List<ItemRelatorioTO> resultado) {
        this.resultado = resultado;
    }

    public Integer getCodigoMes() {
        return codigoMes;
    }

    public void setCodigoMes(Integer codigoMes) {
        this.codigoMes = codigoMes;
    }

    public Integer getCodigoEmpresa() {
        return codigoEmpresa;
    }

    public void setCodigoEmpresa(Integer codigoEmpresa) {
        this.codigoEmpresa = codigoEmpresa;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getSexo() {
        return sexo;
    }

    public void setSexo(String sexo) {
        this.sexo = sexo;
    }

    public Date getInicioCadastro() {
        return inicioCadastro;
    }

    public void setInicioCadastro(Date inicioCadastro) {
        this.inicioCadastro = inicioCadastro;
    }

    public Date getFimCadastro() {
        return fimCadastro;
    }

    public void setFimCadastro(Date fimCadastro) {
        this.fimCadastro = fimCadastro;
    }

    public Integer getInicioIdade() {
        return inicioIdade;
    }

    public void setInicioIdade(Integer inicioIdade) {
        this.inicioIdade = inicioIdade;
    }

    public Integer getFimIdade() {
        return fimIdade;
    }

    public int getRegistroConsideradoGrafico() {
        return registrosTotais - (registrosSemDataNasc + registrosSemSexo + registrosDesconsiderados + registrosPessoasVelhas);
    }

    public void setFimIdade(Integer fimIdade) {
        this.fimIdade = fimIdade;
    }

    public String getSituacaoCliente() {
        return situacaoCliente;
    }

    public void setSituacaoCliente(String situacaoCliente) {
        this.situacaoCliente = situacaoCliente;
    }

    public String getSituacaoContrato() {
        return situacaoContrato;
    }

    public void setSituacaoContrato(String situacaoContrato) {
        this.situacaoContrato = situacaoContrato;
    }

    public int getRegistrosTotais() {
        return registrosTotais;
    }

    public void setRegistrosTotais(int registrosTotais) {
        this.registrosTotais = registrosTotais;
    }

    public int getRegistrosSemDataNasc() {
        return registrosSemDataNasc;
    }

    public void setRegistrosSemDataNasc(int registrosSemDataNasc) {
        this.registrosSemDataNasc = registrosSemDataNasc;
    }

    public int getRegistrosSemSexo() {
        return registrosSemSexo;
    }

    public void setRegistrosSemSexo(int registrosSemSexo) {
        this.registrosSemSexo = registrosSemSexo;
    }

    public Date getInicioMatricula() {
        return inicioMatricula;
    }

    public void setInicioMatricula(Date inicioMatricula) {
        this.inicioMatricula = inicioMatricula;
    }

    public Date getFimMatricula() {
        return fimMatricula;
    }

    public void setFimMatricula(Date fimMatricula) {
        this.fimMatricula = fimMatricula;
    }

    public Integer getInicioDiasSemAparecer() {
        return inicioDiasSemAparecer;
    }

    public void setInicioDiasSemAparecer(Integer inicioDiasSemAparecer) {
        this.inicioDiasSemAparecer = inicioDiasSemAparecer;
    }

    public Integer getFimDiasSemAparecer() {
        return fimDiasSemAparecer;
    }

    public void setFimDiasSemAparecer(Integer fimDiasSemAparecer) {
        this.fimDiasSemAparecer = fimDiasSemAparecer;
    }

    public Date getInicioVencimento() {
        return inicioVencimento;
    }

    public void setInicioVencimento(Date inicioVencimento) {
        this.inicioVencimento = inicioVencimento;
    }

    public Date getFimVencimento() {
        return fimVencimento;
    }

    public void setFimVencimento(Date fimVencimento) {
        this.fimVencimento = fimVencimento;
    }

    public ColaboradorVO getConsultor() {
        return consultor;
    }

    public void setConsultor(ColaboradorVO consultor) {
        this.consultor = consultor;
    }

    public ColaboradorVO getProfessor() {
        return professor;
    }

    public void setProfessor(ColaboradorVO professor) {
        this.professor = professor;
    }

    public String getBairro() {
        return bairro;
    }

    public String getRg() {
        return rg;
    }

    public void setRg(String rg) {
        this.rg = rg;
    }

    public void setBairro(String bairro) {
        this.bairro = bairro;
    }

    public String getSituacaoTrancamento() {
        return situacaoTrancamento;
    }

    public void setSituacaoTrancamento(String situacaoTrancamento) {
        this.situacaoTrancamento = situacaoTrancamento;
    }

    public ItemRelatorioTO getItemRelatorio() {
        return itemRelatorio;
    }

    public void setItemRelatorio(ItemRelatorioTO itemRelatorio) {
        this.itemRelatorio = itemRelatorio;
    }

    public boolean isApresentarLinha1() {
        return apresentarLinha1;
    }

    public void setApresentarLinha1(boolean apresentarLinha1) {
        this.apresentarLinha1 = apresentarLinha1;
    }

    public boolean isApresentarLinha2() {
        return apresentarLinha2;
    }

    public void setApresentarLinha2(boolean apresentarLinha2) {
        this.apresentarLinha2 = apresentarLinha2;
    }

    public boolean isApresentarLinha3() {
        return apresentarLinha3;
    }

    public void setApresentarLinha3(boolean apresentarLinha3) {
        this.apresentarLinha3 = apresentarLinha3;
    }

    public ItemRelatorioTO getItemRelatorioTO() {
        return itemRelatorioTO;
    }

    public void setItemRelatorioTO(ItemRelatorioTO itemRelatorioTO) {
        this.itemRelatorioTO = itemRelatorioTO;
    }

    public EmpresaVO getEmpresaVO() {
        return empresaVO;
    }

    public void setEmpresaVO(EmpresaVO empresaVO) {
        this.empresaVO = empresaVO;
    }

    public List<ModalidadeVO> getListaModalidades() {
        if (listaModalidades == null || listaModalidades.isEmpty()) {
            listaModalidades = new ArrayList<>();
        }
        return listaModalidades;
    }

    public void setListaModalidades(List<ModalidadeVO> listaModalidades) {
        this.listaModalidades = listaModalidades;
    }

    public boolean isSituacaoAVencer() {
        return situacaoAVencer;
    }

    public void setSituacaoAVencer(boolean situacaoAVencer) {
        this.situacaoAVencer = situacaoAVencer;
    }

    public boolean isSituacaoNormal() {
        return situacaoNormal;
    }

    public void setSituacaoNormal(boolean situacaoNormal) {
        this.situacaoNormal = situacaoNormal;
    }

    public boolean isSituacaoAtestado() {
        return situacaoAtestado;
    }

    public void setSituacaoAtestado(boolean situacaoAtestado) {
        this.situacaoAtestado = situacaoAtestado;
    }

    public boolean isSituacaoAtivo() {
        return situacaoAtivo;
    }

    public void setSituacaoAtivo(boolean situacaoAtivo) {
        this.situacaoAtivo = situacaoAtivo;
    }

    public boolean isSituacaoCancelado() {
        return situacaoCancelado;
    }

    public void setSituacaoCancelado(boolean situacaoCancelado) {
        this.situacaoCancelado = situacaoCancelado;
    }

    public boolean isSituacaoDesistente() {
        return situacaoDesistente;
    }

    public void setSituacaoDesistente(boolean situacaoDesistente) {
        this.situacaoDesistente = situacaoDesistente;
    }

    public boolean isSituacaoInativo() {
        return situacaoInativo;
    }

    public void setSituacaoInativo(boolean situacaoInativo) {
        this.situacaoInativo = situacaoInativo;
    }

    public boolean isSituacaoTrancado() {
        return situacaoTrancado;
    }

    public void setSituacaoTrancado(boolean situacaoTrancado) {
        this.situacaoTrancado = situacaoTrancado;
    }

    public boolean isSituacaoVencido() {
        return situacaoVencido;
    }

    public void setSituacaoVencido(boolean situacaoVencido) {
        this.situacaoVencido = situacaoVencido;
    }

    public boolean isSituacaoVisitante() {
        return situacaoVisitante;
    }

    public void setSituacaoVisitante(boolean situacaoVisitante) {
        this.situacaoVisitante = situacaoVisitante;
    }

    public Integer getDuracao() {
        return duracao;
    }

    public void setDuracao(Integer duracao) {
        this.duracao = duracao;
    }

    public String getSituacaoApresentar() {
        return situacaoApresentar;
    }

    public void setSituacaoApresentar(String situacaoApresentar) {
        this.situacaoApresentar = situacaoApresentar;
    }

    public Integer getConvenio() {
        return convenio;
    }

    public void setConvenio(Integer convenio) {
        this.convenio = convenio;
    }

    public List<SelectItem> getListaSelectItemConvenios() throws Exception {
        if (listaSelectItemConvenios == null) {
            listaSelectItemConvenios = getListaConvenio();
        }
        return listaSelectItemConvenios;
    }

    public int getRegistrosPessoasVelhas() {
        return registrosPessoasVelhas;
    }

    public void setRegistrosPessoasVelhas(int registrosPessoasVelhas) {
        this.registrosPessoasVelhas = registrosPessoasVelhas;
    }


    public void setListaSelectItemConvenios(List<SelectItem> listaSelectItemConvenios) {
        this.listaSelectItemConvenios = listaSelectItemConvenios;
    }


    //Plano

    public Integer getPlano() {
        return plano;
    }

    public void setPlano(Integer plano) {
        this.plano = plano;
    }

    public List<SelectItem> getListaSelectItemPlanos() throws Exception {
        if (listaSelectItemPlanos == null) {
            listaSelectItemPlanos = getListaPlano();
        }
        return listaSelectItemPlanos;
    }

    public void setListaSelectItemPlanos(List<SelectItem> listaSelectItemPlanos) {
        this.listaSelectItemPlanos = listaSelectItemPlanos;
    }



    public String getCpf() {
        return cpf;
    }

    public void setCpf(String cpf) {
        this.cpf = cpf;
    }

    public Date getInicioRematricula() {
        return inicioRematricula;
    }

    public void setInicioRematricula(Date inicioRematricula) {
        this.inicioRematricula = inicioRematricula;
    }

    public Date getFinalRematricula() {
        return finalRematricula;
    }

    public void setFinalRematricula(Date finalRematricula) {
        this.finalRematricula = finalRematricula;
    }

    public Date getInicioRenovacao() {
        return inicioRenovacao;
    }

    public void setInicioRenovacao(Date inicioRenovacao) {
        this.inicioRenovacao = inicioRenovacao;
    }

    public Date getFinalRenovacao() {
        return finalRenovacao;
    }

    public void setFinalRenovacao(Date finalRenovacao) {
        this.finalRenovacao = finalRenovacao;
    }

    public String getNomeRespContrato() {
        return nomeRespContrato;
    }

    public void setNomeRespContrato(String nomeRespContrato) {
        this.nomeRespContrato = nomeRespContrato;
    }

    public boolean isSituacaoCarencia() {
        return situacaoCarencia;
    }

    public void setSituacaoCarencia(boolean situacaoCarencia) {
        this.situacaoCarencia = situacaoCarencia;
    }

    public List<SelectItem> getSituacaoModalidades(){
        List<SelectItem> lista = new ArrayList<SelectItem>();
        lista.add(new SelectItem(0,"Todas"));
        lista.add(new SelectItem(1,"Ativa"));
        lista.add(new SelectItem(2,"Desativada"));
        return lista;
    }

    public int getSituacaoModalidade() {
        return situacaoModalidade;
    }

    public void setSituacaoModalidade(int situacaoModalidade) {
        this.situacaoModalidade = situacaoModalidade;
    }

    public String getUrlUploadArquivo() {
        return urlUploadArquivo;
    }

    public void setUrlUploadArquivo(String urlUploadArquivo) {
        this.urlUploadArquivo = urlUploadArquivo;
    }

    public void setTipoPlanoBolsa(boolean tipoPlanoBolsa) {
        this.tipoPlanoBolsa = tipoPlanoBolsa;
    }

    public boolean isTipoPlanoBolsa() {
        return tipoPlanoBolsa;
    }

    public void setTipoPlanoCredito(boolean tipoPlanoCredito) {
        this.tipoPlanoCredito = tipoPlanoCredito;
    }

    public boolean isTipoPlanoCredito() {
        return tipoPlanoCredito;
    }

    public Integer getCategoria() {
        if (categoria == null) {
            categoria = 0;
        }
        return categoria;
    }

    public void setCategoria(Integer categoria) {
        this.categoria = categoria;
    }

    public String getNomeRelatorio() {
        if (nomeRelatorio == null) {
            nomeRelatorio = "Relatório de Clientes";
        }
        return nomeRelatorio;
    }

    public void setNomeRelatorio(String nomeRelatorio) {
        this.nomeRelatorio = nomeRelatorio;
    }

    public Boolean getUtilizaConfiguracaoSesc() {
        return utilizaConfiguracaoSesc;
    }

    public void setUtilizaConfiguracaoSesc(Boolean utilizaConfiguracaoSesc) {
        this.utilizaConfiguracaoSesc = utilizaConfiguracaoSesc;
    }

    public Boolean getConsultarTodasEmpresas() {
        if(consultarTodasEmpresas == null){
            consultarTodasEmpresas = false;
        }
        return consultarTodasEmpresas;
    }

    public void setConsultarTodasEmpresas(Boolean consultarTodasEmpresas) {
        this.consultarTodasEmpresas = consultarTodasEmpresas;
    }

    public List<GenericoTO> getVezesSemana() {
        if(UteisValidacao.emptyList(vezesSemana)){
            vezesSemana = new ArrayList<GenericoTO>();
            vezesSemana.add(new GenericoTO("1", "1"));
            vezesSemana.add(new GenericoTO("2", "2"));
            vezesSemana.add(new GenericoTO("3", "3"));
            vezesSemana.add(new GenericoTO("4", "4"));
            vezesSemana.add(new GenericoTO("5", "5"));
            vezesSemana.add(new GenericoTO("6", "6"));
            vezesSemana.add(new GenericoTO("7", "7"));
        }
        return vezesSemana;
    }

    public void setVezesSemana(List<GenericoTO> vezesSemana) {
        this.vezesSemana = vezesSemana;
    }

    public boolean isPermissaoConsultaTodasEmpresas() {
        return permissaoConsultaTodasEmpresas;
    }

    public void setPermissaoConsultaTodasEmpresas(boolean permissaoConsultaTodasEmpresas) {
        this.permissaoConsultaTodasEmpresas = permissaoConsultaTodasEmpresas;
    }

    public Integer getFiltroEmpresa() {
        return filtroEmpresa;
    }

    public void setFiltroEmpresa(Integer filtroEmpresa) {
        this.filtroEmpresa = filtroEmpresa;
    }

    public Integer getPacote() {
        return pacote;
    }

    public void setPacote(Integer pacote) {
        this.pacote = pacote;
    }

    public List<SelectItem> getListaSelectItemPacotes() throws Exception {
        if (listaSelectItemPacotes == null) {
            listaSelectItemPacotes = getListaPacote();
        }
        return listaSelectItemPacotes;
    }

    public void setListaSelectItemPacotes(List<SelectItem> listaSelectItemPacotes) {
        this.listaSelectItemPacotes = listaSelectItemPacotes;
    }

    public String getFiltroParQ() {
        return filtroParQ;
    }

    public void setFiltroParQ(String filtroParQ) {
        this.filtroParQ = filtroParQ;
    }

    public List getListaSelectItemParQ() throws Exception {
        List objs = new ArrayList();
        objs.add(new SelectItem("assinado", "Assinado"));
        if (getEmpresaLogado() != null && !UteisValidacao.emptyNumber(getEmpresaLogado().getDiasParaVencimentoParq())) {
            objs.add(new SelectItem("vencido", "Vencido"));
        }
        objs.add(new SelectItem("nao_assinado", "Não Assinado"));
        return objs;
    }

    public String limparLancamento() {
        inicioLancamento = null;
        finalLancamento = null;
        return "";
    }

    public String[] identificacaoPessoalInternacional() {
        try {
            displayIdentificadorFront = identificadorPessoaInternacional(getEmpresaLogado().getPais().getNome());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return displayIdentificadorFront;

    }

    public Date getInicioLancamento() {
        return inicioLancamento;
    }

    public void setInicioLancamento(Date inicioLancamento) {
        this.inicioLancamento = inicioLancamento;
    }

    public Date getFinalLancamento() {
        return finalLancamento;
    }

    public void setFinalLancamento(Date finalLancamento) {
        this.finalLancamento = finalLancamento;
    }

    public String[] getDisplayIdentificadorFront() {
        return displayIdentificadorFront;
    }

    public void setDisplayIdentificadorFront(String[] displayIdentificadorFront) {
        this.displayIdentificadorFront = displayIdentificadorFront;
    }

    public ConfiguracaoSistemaVO getConfiguracaoSistema() {
        return configuracaoSistema;
    }

    public void setConfiguracaoSistema(ConfiguracaoSistemaVO configuracaoSistema) {
        this.configuracaoSistema = configuracaoSistema;
    }

    public boolean isOrigemZW() {
        return origemZW;
    }

    public void setOrigemZW(boolean origemZW) {
        this.origemZW = origemZW;
    }

    public boolean isOrigemAulaCheia() {
        return origemAulaCheia;
    }

    public void setOrigemAulaCheia(boolean origemAulaCheia) {
        this.origemAulaCheia = origemAulaCheia;
    }

    public boolean isOrigemTreino() {
        return origemTreino;
    }

    public void setOrigemTreino(boolean origemTreino) {
        this.origemTreino = origemTreino;
    }

    public boolean isOrigemAppTreino() {
        return origemAppTreino;
    }

    public void setOrigemAppTreino(boolean origemAppTreino) {
        this.origemAppTreino = origemAppTreino;
    }

    public boolean isOrigemAppProfessor() {
        return origemAppProfessor;
    }

    public void setOrigemAppProfessor(boolean origemAppProfessor) {
        this.origemAppProfessor = origemAppProfessor;
    }

    public boolean isOrigemAutoAtendimento() {
        return origemAutoAtendimento;
    }

    public void setOrigemAutoAtendimento(boolean origemAutoAtendimento) {
        this.origemAutoAtendimento = origemAutoAtendimento;
    }

    public boolean isOrigemBuzzLead() {
        return origemBuzzLead;
    }

    public void setOrigemBuzzLead(boolean origemBuzzLead) {
        this.origemBuzzLead = origemBuzzLead;
    }

    public boolean isOrigemVendasOnline() {
        return origemVendasOnline;
    }

    public void setOrigemVendasOnline(boolean origemVendasOnline) {
        this.origemVendasOnline = origemVendasOnline;
    }

    public boolean isContratoConcomVarEmpresas() {
        return contratoConcomVarEmpresas;
    }

    public void setContratoConcomVarEmpresas(boolean contratoConcomVarEmpresas) {
        this.contratoConcomVarEmpresas = contratoConcomVarEmpresas;
    }

    public boolean isUltimoContratoCliente() {
        return ultimoContratoCliente;
    }

    public void setUltimoContratoCliente(boolean ultimoContratoCliente) {
        this.ultimoContratoCliente = ultimoContratoCliente;
    }

    public Integer getCodigoEventoSelecionado() {
        return codigoEventoSelecionado;
    }

    public void setCodigoEventoSelecionado(Integer codigoEventoSelecionado) {
        this.codigoEventoSelecionado = codigoEventoSelecionado;
    }

    public List<SelectItem> getListaSelectItemEvento() {
        return listaSelectItemEvento;
    }

    public void setListaSelectItemEvento(List<SelectItem> listaSelectItemEvento) {
        this.listaSelectItemEvento = listaSelectItemEvento;
    }

    public boolean isSituacaoDependente() {
        return situacaoDependente;
    }

    public void setSituacaoDependente(boolean situacaoDependente) {
        this.situacaoDependente = situacaoDependente;
    }

    public boolean isSituacaoGympass() {
        return situacaoGympass;
    }

    public void setSituacaoGympass(boolean situacaoGympass) {
        this.situacaoGympass = situacaoGympass;
    }

    public boolean isSituacaoAdimplentes() {
        return situacaoAdimplentes;
    }

    public void setSituacaoAdimplentes(boolean situacaoAdimplentes) {
        this.situacaoAdimplentes = situacaoAdimplentes;
    }

    public boolean isSituacaoInadimplentes() {
        return situacaoInadimplentes;
    }

    public void setSituacaoInadimplentes(boolean situacaoInadimplentes) {
        this.situacaoInadimplentes = situacaoInadimplentes;
    }

    public boolean isMostrarSituacao() {
        return mostrarSituacao;
    }

    public void setMostrarSituacao(boolean mostrarSituacao) {
        this.mostrarSituacao = mostrarSituacao;
    }

    public void apresentarAbaSituacao() {
        setMostrarSituacao(isSituacaoAtivo() || isSituacaoInativo() || isSituacaoTrancado() || isSituacaoVisitante() ||
                isSituacaoAtestado() || isSituacaoCancelado() || isSituacaoAVencer() ||
                isSituacaoNormal() || isSituacaoVencido() || isSituacaoDesistente() ||
                isSituacaoCarencia() || isSituacaoDependente() || isSituacaoGympass());
        if(isMostrarSituacao() == false){
            setSituacaoInadimplentes(false);
            setSituacaoAdimplentes(false);
        }
    }

}

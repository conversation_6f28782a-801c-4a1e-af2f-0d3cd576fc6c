/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package controle.basico;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import javax.faces.model.SelectItem;

import br.com.pactosolucoes.comuns.notificacao.RecursoSistema;
import negocio.comuns.basico.BrindeVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.HistoricoPontosVO;
import negocio.comuns.utilitarias.Uteis;
import relatorio.controle.arquitetura.SuperControleRelatorio;

/**
 *
 * <AUTHOR>
 */
public class HistoricoPontosControle extends SuperControleRelatorio{

    private HistoricoPontosVO historicoPontos;
    private List<HistoricoPontosVO> listaTotalPontos;
    private List<HistoricoPontosVO> listaPorCliente;
    private Boolean clienteAtivo;
    private Boolean clienteInativo;
    private ClienteVO clienteSelecionado;
    private Boolean pesquisarPorPerido;
    private Date dataInicial;
    private Date dataFinal;
    private List<SelectItem> listaDeBrindes = new ArrayList<SelectItem>();
    private BrindeVO brindeSelecionado;
    private Boolean pesquisaDetalhada;
    private String nomeAluno;
    private ClienteVO clientePesquisa;
    private boolean permissaoConsultaTodasEmpresas = false;
    private Integer filtroEmpresa;
    private Integer qtdTotalResultados;

    public HistoricoPontosControle() throws Exception {
        historicoPontos = new HistoricoPontosVO();
        brindeSelecionado = new BrindeVO();
        obterUsuarioLogado();
        inicializarFacades();
        montarListaSelectItemEmpresa();
        setMensagemID("");
        montarListaBrindes();
    }

    public void montarListaSelectItemEmpresa() {
        try {
            permissaoConsultaTodasEmpresas = permissao("ConsultarInfoTodasEmpresas");
            if (permissaoConsultaTodasEmpresas) {
                montarListaEmpresasComItemTodas();
            }
            setFiltroEmpresa(getEmpresaLogado().getCodigo());
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void montarListaBrindes() throws Exception{
        if (listaDeBrindes.isEmpty()) {
            List<BrindeVO> listaBrindes = getFacade().getBrinde().consultarBrindesAtivos(getFiltroEmpresa(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            listaDeBrindes.add(new SelectItem(0, ""));
            for (BrindeVO brinde : listaBrindes) {
                listaDeBrindes.add(new SelectItem(brinde.getCodigo(), brinde.getNome()));
            }
        }
    }
    
    public String gerarRelatorio(){
        try {
            StringBuffer situacao = new StringBuffer();
            if (clienteAtivo && !clienteInativo) {
                situacao.append("'AT'");
            } else if (clienteInativo && !clienteAtivo) {
                situacao.append("'IN'");
            } else if (clienteAtivo && clienteInativo) {
                situacao.append("'AT','IN'");
            }

            listaTotalPontos = getFacade().getHistoricoPontos().consultaTotalPontosAlunos(situacao.toString(), getPesquisaDetalhada(),
                    getDataInicial(), getDataFinal(), getBrindeSelecionado(), getClientePesquisa(), getFiltroEmpresa());
            notificarRecursoEmpresa(RecursoSistema.CLUBE_VANTAGEM_HISTORICO_DE_PONTOS);
            setQtdTotalResultados(listaTotalPontos.size());
            return "editar";
        } catch (Exception e) {
            setMensagemDetalhada(e.getMessage());
            return "";
        }
    }
    
    public String voltar() {
        listaPorCliente = new ArrayList<HistoricoPontosVO>();
        listaTotalPontos = new ArrayList<HistoricoPontosVO>();
        clienteAtivo = null;
        clienteInativo = null;
        pesquisaDetalhada = false;
        pesquisarPorPerido = false;
        brindeSelecionado = new BrindeVO();
        nomeAluno = "";
        setMensagemDetalhada("");
        clientePesquisa = new ClienteVO();
        dataInicial = null;
        dataFinal = null;
        return "consultar";
    }
    
    public void irParaTelaCliente(){
        try {
            HistoricoPontosVO obj = (HistoricoPontosVO) context().getExternalContext().getRequestMap().get("lista");
             if (obj == null) {
                throw new Exception("Cliente Não Encontrado.");
            } else {
                irParaTelaCliente(getFacade().getCliente().consultarPorChavePrimaria(obj.getCliente().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS));
            }
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }
    
    public void irParaTelaExtratoPontos(){
        try {
            HistoricoPontosVO obj = (HistoricoPontosVO) context().getExternalContext().getRequestMap().get("lista");
            if (obj == null) {
                throw new Exception("Cliente Não Encontrado.");
            } else {
                listaPorCliente = getFacade().getHistoricoPontos().consultarHistoricoPorCliente(obj.getCliente().getCodigo(), Uteis.NIVELMONTARDADOS_PONTUACAO);
            }
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }
    
    public void montarListaHistoricoExterno(ClienteVO cliente) throws Exception{
        listaPorCliente = getFacade().getHistoricoPontos().consultarHistoricoPorCliente(cliente.getCodigo(), Uteis.NIVELMONTARDADOS_PONTUACAO);
    }
    
    public void excluirItemLista(){
        try {
            HistoricoPontosVO obj = (HistoricoPontosVO) context().getExternalContext().getRequestMap().get("lista");
            if (obj == null) {
                throw new Exception("Cliente Não Encontrado.");
            } else {
                getFacade().getHistoricoPontos().excluir(obj.getCodigo());
                listaPorCliente = getFacade().getHistoricoPontos().consultarHistoricoPorCliente(obj.getCliente().getCodigo(), Uteis.NIVELMONTARDADOS_PONTUACAO);
                incluirLogExclusao(obj);
            }
            setSucesso(true);
            setErro(false);
            montarSucesso("Exculido com Sucesso!");
        } catch (Exception e) {
            setSucesso(false);
            setErro(true);
            montarErro(e);
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }
    
    public void incluirLogExclusao(HistoricoPontosVO obj) throws Exception{
        try {
            obj.setObjetoVOAntesAlteracao(new HistoricoPontosVO());
            obj.setNovoObj(true);
            registrarLogExclusaoTodosDadosObjetoVO(obj, obj.getCodigo(), "HistoricoPontos", obj.getCliente().getCodigo());
        } catch (Exception e) {
            registrarLogErroObjetoVO("HistoricoPontos", obj.getCodigo(), "ERRO AO GERAR LOG DE EXCLUSÃO DO HISTORICO PONTOS",this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
    }
    
    public List<ClienteVO> executarAutocompleteConsultaCliente(Object suggest){
        String pref = (String) suggest;
        ArrayList<ClienteVO> result;
        try {
            if (pref.equals("%")) {
                result = (ArrayList<ClienteVO>) getFacade().getCliente().consultarTodosClienteComLimite(getFiltroEmpresa(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            } else {
                result = (ArrayList<ClienteVO>) getFacade().getCliente().consultarPorNomeClienteComLimite(getFiltroEmpresa(), pref, false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            }

        } catch (Exception ex) {
            result = (new ArrayList<ClienteVO>());
            montarErro(ex.getMessage());
        }
        return result;
    }
    
    public void fazerPesquisaDetalhada(){
        pesquisaDetalhada = true;
    }
    
    public void fazerPesquisaSintetica(){
        pesquisaDetalhada = false;
    }
    
    public void selecionarClienteSuggestionBox() throws Exception {
        ClienteVO obj = (ClienteVO) request().getAttribute("result");
        clientePesquisa = getFacade().getCliente().consultarPorChavePrimaria(obj.getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);
    }
    
    public void limparCliente() {
        clientePesquisa = new ClienteVO();
    }
    
    public HistoricoPontosVO getHistoricoPontos() {
        return historicoPontos;
    }

    public void setHistoricoPontos(HistoricoPontosVO historicoPontos) {
        this.historicoPontos = historicoPontos;
    }

    public List<HistoricoPontosVO> getListaTotalPontos() {
        return listaTotalPontos;
    }

    public void setListaTotalPontos(List<HistoricoPontosVO> listaTotalPontos) {
        this.listaTotalPontos = listaTotalPontos;
    }

    public List<HistoricoPontosVO> getListaPorCliente() {
        return listaPorCliente;
    }

    public void setListaPorCliente(List<HistoricoPontosVO> listaPorCliente) {
        this.listaPorCliente = listaPorCliente;
    }

    public Boolean getClienteAtivo() {
        return clienteAtivo;
    }

    public void setClienteAtivo(Boolean clienteAtivo) {
        this.clienteAtivo = clienteAtivo;
    }

    public Boolean getClienteInativo() {
        return clienteInativo;
    }

    public void setClienteInativo(Boolean clienteInativo) {
        this.clienteInativo = clienteInativo;
    }
    
    public ClienteVO getClienteSelecionado() {
        return clienteSelecionado;
    }

    public void setClienteSelecionado(ClienteVO clienteSelecionado) {
        this.clienteSelecionado = clienteSelecionado;
    }
    
    public Boolean getPesquisarPorPerido() {
        if (pesquisarPorPerido == null) {
            pesquisarPorPerido = false;
        }
        return pesquisarPorPerido;
    }

    public void setPesquisarPorPerido(Boolean pesquisarPorPerido) {
        this.pesquisarPorPerido = pesquisarPorPerido;
    }

    public Date getDataInicial() {
        return dataInicial;
    }

    public void setDataInicial(Date dataInicial) {
        this.dataInicial = dataInicial;
    }

    public Date getDataFinal() {
        return dataFinal;
    }

    public void setDataFinal(Date dataFinal) {
        this.dataFinal = dataFinal;
    }

    public List<SelectItem> getListaDeBrindes() {
        return listaDeBrindes;
    }

    public void setListaDeBrindes(List<SelectItem> listaDeBrindes) {
        this.listaDeBrindes = listaDeBrindes;
    }

    public BrindeVO getBrindeSelecionado() {
        return brindeSelecionado;
    }

    public void setBrindeSelecionado(BrindeVO brindeSelecionado) {
        this.brindeSelecionado = brindeSelecionado;
    }

    public Boolean getPesquisaDetalhada() {
        if (pesquisaDetalhada == null) {
            pesquisaDetalhada = false;
        }
        return pesquisaDetalhada;
    }

    public void setPesquisaDetalhada(Boolean pesquisaDetalhada) {
        this.pesquisaDetalhada = pesquisaDetalhada;
    }

    public String getNomeAluno() {
        if (nomeAluno == null) {
            nomeAluno = "";
        }
        return nomeAluno;
    }

    public void setNomeAluno(String nomeAluno) {
        this.nomeAluno = nomeAluno;
    }

    public ClienteVO getClientePesquisa() {
        if (clientePesquisa == null) {
            clientePesquisa = new ClienteVO();
        }
        return clientePesquisa;
    }

    public void setClientePesquisa(ClienteVO clientePesquisa) {
        this.clientePesquisa = clientePesquisa;
    }
    
    public String getDadosExportacao(){
        if(this.pesquisaDetalhada){
            return "matricula_Apresentar=Matricula,nome_Apresentar=Aluno,telefone_Apresentar=Telefone,pontos=Pontos,nomeEmpresa=Empresa";
        } 
        return "matricula_Apresentar=Matricula,nome_Apresentar=Aluno,telefone_Apresentar=Telefone,pontostotal=Total de Pontos,nomeEmpresa=Empresa";
    }

    public boolean isPermissaoConsultaTodasEmpresas() {
        return permissaoConsultaTodasEmpresas;
    }

    public void setPermissaoConsultaTodasEmpresas(boolean permissaoConsultaTodasEmpresas) {
        this.permissaoConsultaTodasEmpresas = permissaoConsultaTodasEmpresas;
    }

    public Integer getFiltroEmpresa() {
        if (filtroEmpresa == null) {
            filtroEmpresa = 0;
        }
        return filtroEmpresa;
    }

    public void setFiltroEmpresa(Integer filtroEmpresa) {
        this.filtroEmpresa = filtroEmpresa;
    }

    public Integer getQtdTotalResultados() {
        return qtdTotalResultados;
    }

    public void setQtdTotalResultados(Integer qtdTotalResultados) {
        this.qtdTotalResultados = qtdTotalResultados;
    }
}

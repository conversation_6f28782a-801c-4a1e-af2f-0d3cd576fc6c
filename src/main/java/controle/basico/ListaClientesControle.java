package controle.basico;

import java.util.ArrayList;
import java.util.List;

import javax.faces.event.ActionEvent;

import br.com.pactosolucoes.comuns.util.JSFUtilities;

import negocio.comuns.basico.ClienteVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;

import controle.arquitetura.SuperControle;

/**
 * <AUTHOR>
 *
 */
public class ListaClientesControle extends SuperControle {
	private String nomeConsulta;
	private List<ClienteVO> listaClientes = new ArrayList<ClienteVO>();
	private List<ClienteVO> listaClientesSemFiltro = new ArrayList<ClienteVO>();
	private Boolean detalharCliente = Boolean.FALSE;
	private String imagem = "";
	private Boolean apresentarColunaDuracao = Boolean.FALSE;
	
	/**
	 * <PERSON><PERSON>cides
	 * 20/01/2012
	 * @param evento
	 */
	@SuppressWarnings("unchecked")
	public void preencherLista(ActionEvent evento){
		inicializar((List<ClienteVO>) evento.getComponent().getAttributes().get("listaClientes"));
	}


	public void inicializar(List<ClienteVO> clientes) {
		this.setNomeConsulta("");
		this.setDetalharCliente(Boolean.FALSE);
		this.setListaClientesSemFiltro(clientes);
		this.setListaClientes(this.getListaClientesSemFiltro());
	}

	
	/**
	 * Joao Alcides
	 * 26/01/2012
	 * @throws Exception
	 */
	public void consultarAluno() throws Exception{
		this.setListaClientes(new ArrayList<ClienteVO>());
		if(UteisValidacao.emptyString(this.getNomeConsulta())){
			this.setListaClientes(this.getListaClientesSemFiltro());
			return;
		}
		for(ClienteVO cliente : this.getListaClientesSemFiltro()){
			if(cliente.getPessoa().getNome().contains(this.getNomeConsulta().toUpperCase())){
				this.getListaClientes().add(cliente);	
			}
		}
	}
	
	public void voltarListaClientes(){
		this.setDetalharCliente(Boolean.FALSE);
		this.setListaClientes(this.getListaClientesSemFiltro());
	}
	
	/**
	 * Joao Alcides
	 * 26/01/2012
	 * @throws Exception 
	 */
	public void detalharAluno() throws Exception{
		ClienteVO obj = (ClienteVO) context().getExternalContext().getRequestMap().get("cliente");
		this.setDetalharCliente(Boolean.TRUE);
		ClienteControle clienteControle = (ClienteControle) JSFUtilities.getFromSession(ClienteControle.class.getSimpleName());
		if(clienteControle == null){
			clienteControle = new ClienteControle();
		}
		clienteControle.detalharCliente(obj);
		this.setListaClientes(new ArrayList<ClienteVO>());
		this.getListaClientes().add(obj);
	}
	
	
	public void setNomeConsulta(String nomeConsulta) {
		this.nomeConsulta = nomeConsulta;
	}

	public String getNomeConsulta() {
		return nomeConsulta;
	}

	public void setListaClientes(List<ClienteVO> listaClientes) {
		this.listaClientes = listaClientes;
	}

	public List<ClienteVO> getListaClientes() {
		return listaClientes;
	}


	public void setListaClientesSemFiltro(List<ClienteVO> listaClientesSemFiltro) {
		this.listaClientesSemFiltro = listaClientesSemFiltro;
	}


	public List<ClienteVO> getListaClientesSemFiltro() {
		return listaClientesSemFiltro;
	}
	
	public Boolean getApresentarLista(){
		return !this.getListaClientes().isEmpty();
	}


	public void setDetalharCliente(Boolean detalharCliente) {
		this.detalharCliente = detalharCliente;
	}


	public Boolean getDetalharCliente() {
		return detalharCliente;
	}


	public void setImagem(String imagem) {
		this.imagem = imagem;
	}


	public String getImagem() {
		return imagem;
	}


	public void setApresentarColunaDuracao(Boolean apresentarCampoDuracao) {
		this.apresentarColunaDuracao = apresentarCampoDuracao;
	}


	public Boolean getApresentarColunaDuracao() {
		return apresentarColunaDuracao;
	}
	

}

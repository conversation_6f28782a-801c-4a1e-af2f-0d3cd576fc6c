package controle.basico;

import controle.arquitetura.SuperControle;
import controle.basico.clube.MensagemGenericaControle;
import negocio.comuns.utilitarias.ControleConsulta;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.basico.PlanoTipoVO;

import java.util.List;

public class PlanoTipoControle extends SuperControle {

    private PlanoTipoVO planoTipoVO = new PlanoTipoVO();
    private String msgAlert;

    public PlanoTipoControle() {

    }

    public String novo() {
        setPlanoTipoVO(new PlanoTipoVO());
        try {
            getPlanoTipoVO().setTiposProduto(getFacade().getPlanoTipo().montarListaTiposProdutos(0, getFacade().getPlanoTipo().getCon()));
        } catch (Exception e) {
            montarErroComLog(e);
        }
        return "editar";
    }

    public String editar() {
        try {
            setPlanoTipoVO(getFacade().getPlanoTipo().consultar(getCodigoAtual(), Uteis.NIVELMONTARDADOS_TODOS).get(0));
        } catch (Exception e) {
            montarErroComLog(e);
        }
        return "editar";
    }

    public String excluir(){
        try {
            getFacade().getPlanoTipo().excluir(planoTipoVO.getCodigo());
        } catch (Exception e) {
            montarErroComLog(e);
            if(e.getMessage().contains("ERRO: atualização ou exclusão em tabela \"planotipo\" viola restrição de chave estrangeira") || e.getMessage().contains("ERROR: update or delete on table \"planotipo\" violates foreign key")){
                setMensagemDetalhada("Este Tipo de Plano não pode ser excluído, pois está sendo utilizado!");
            }else {
                setMensagemDetalhada("msg_erro", e.getMessage());
            }
            return "editar";
        }

        return "consultar";
    }

    public String consultar(){
        try {
            super.consultar();

            getControleConsulta().setValorConsulta(getControleConsulta().getValorConsulta().trim());
            List<PlanoTipoVO> tipos =getFacade().getPlanoTipo().consultar(Uteis.NIVELMONTARDADOS_DADOSBASICOS);

            tipos = ControleConsulta.obterSubListPaginaApresentar(tipos, controleConsulta);

            definirVisibilidadeLinksNavegacao(controleConsulta.getPaginaAtual(), controleConsulta.getNrTotalPaginas());
            setListaConsulta(tipos);
            limparMsg();

        } catch (Exception e) {
            montarErroComLog(e);
            return "consultar";
        }

        return "consultar";
    }

    public String gravar(){
        try {
            if(planoTipoVO.getCodigo() == null){
                getFacade().getPlanoTipo().incluir(planoTipoVO);
            }else{
                getFacade().getPlanoTipo().alterar(planoTipoVO);
            }
        } catch (Exception e) {
            montarErroComLog(e);
            return "editar";
        }

        return "consultar";
    }

    public Integer getCodigoAtual(){
        return Integer.parseInt(request().getParameter("chavePrimaria"));
    }

    public PlanoTipoVO getPlanoTipoVO() {
        return planoTipoVO;
    }

    public void setPlanoTipoVO(PlanoTipoVO tipoPlano) {
        this.planoTipoVO = tipoPlano;
    }

    public String inicializarConsulta() {
        return "consultar";
    }

    public void setMsgAlert(String msgAlert) {
        this.msgAlert = msgAlert;
    }

    public String getMsgAlert() {
        if (msgAlert == null) {
            return "";
        }
        return msgAlert;
    }

    public void confirmarExcluir(){
        MensagemGenericaControle control = (MensagemGenericaControle) getControlador(MensagemGenericaControle.class);
        control.setMensagemDetalhada("", "");
        setMsgAlert("Richfaces.showModalPanel('mdlMensagemGenerica');");
        control.init("Exclusão de Tipo de Plano",
                "Deseja excluir o Tipo de Plano?",
                this, "excluir", "", "", "", "grupoBtnExcluir");
    }

}

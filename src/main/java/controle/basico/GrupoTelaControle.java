
package controle.basico;

import controle.arquitetura.SuperControle;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.crm.GrupoColaboradorParticipanteVO;
import negocio.comuns.crm.GrupoColaboradorVO;
import negocio.comuns.utilitarias.Uteis;

import java.util.ArrayList;
import java.util.List;

/**
 * Classe que gerencia os grupos do usuário logado
 * <AUTHOR>
 */
public class GrupoTelaControle extends SuperControle {
    // lista de colaboradores dos grupos sem a repetição que ocorre nos grupos
    private List<ColaboradorVO> listaColaboradores;
    // lista de grupos que o usuario logado tem acesso.
    private List<GrupoColaboradorVO> listaGrupos;

    public GrupoTelaControle() {
        this(null, null, true);
    }

    public GrupoTelaControle(UsuarioVO usuarioVO, EmpresaVO empresaVO) {
        this(usuarioVO, empresaVO, true);
    }

    public GrupoTelaControle(UsuarioVO usuarioVO, EmpresaVO empresaVO, boolean incializarDados) {
        try {
            setUsuario(usuarioVO);
            setEmpresa(empresaVO);
        } catch (Exception e) {
            Uteis.logar("Falaha ao atribuir usuario para GrupoTelaControle");
            e.printStackTrace();
        }
        if (incializarDados) {
            inicializarDados();
        }
    }

    public void inicializarDados() {
        montarGrupos(false);
        montarListaSemRepeticao();
    }

    public String getInicializarDadosMetaFinanceira(){
        montarGrupos(true);
        montarListaSemRepeticao();
        return("Richfaces.showModalPanel('filtroConversaoColaborador')");
    }

    /**
     * inicializa ou atualiza a lista de grupos que o usuario tem acesso
     */
    @SuppressWarnings("unchecked")
    private void montarGrupos(boolean validarPermissaoMetaFinanceira) {
        try {
            UsuarioVO usuario = getUsuarioLogado();
            if(usuario == null){
                usuario = getUsuario();
            }
            listaGrupos = new ArrayList<GrupoColaboradorVO>();
            final int nivelDadosGrupos = Uteis.NIVELMONTARDADOS_RESULTADOS_BI;
            if (usuario == null || usuario.getAdministrador()) {
                listaGrupos.addAll(getFacade().getGrupoColaborador().consultarPorCodigo(0, false,
                        nivelDadosGrupos, null));
            } else {
                boolean possuiPermissaoMetaFinanceira = permissao("VisualizaRMetaFinanceiraEmpresaBI") || permissao("VisualizarMetasFinanceirasTodasEmpresas");
                if (getFacade().getAberturaMeta().verificarPermitirVisualizarTodasCarteiras(usuario) || (validarPermissaoMetaFinanceira && possuiPermissaoMetaFinanceira)) {
                    listaGrupos.addAll(getFacade().getGrupoColaborador().consultarGrupoColaboradorComParticipantes(false,
                            nivelDadosGrupos, getEmpresaLogado().getCodigo()));
                } else {
                    listaGrupos.addAll(getFacade().getGrupoColaborador().consultarGrupoColaboradorComParticipantesPorCodigoColaboradorPorTipoVisao(
                            usuario.getColaboradorVO().getCodigo(), "VI", false,
                            nivelDadosGrupos, getEmpresaLogado().getCodigo()));
                    listaGrupos.addAll(getFacade().getGrupoColaborador().consultarPorResponsavelGrupo(usuario.getNome(), false,
                            nivelDadosGrupos, getEmpresaLogado().getCodigo()));
                    listaGrupos.addAll(getFacade().getGrupoColaborador().consultarGrupoColaboradorComParticipantesPorCodigoColaboradorPorTipoVisao(
                            usuario.getColaboradorVO().getCodigo(), "IG", false,
                            nivelDadosGrupos, getEmpresaLogado().getCodigo()));

                    List<GrupoColaboradorVO> listaGruposAC = new ArrayList<GrupoColaboradorVO>();
                    listaGruposAC.addAll(getFacade().getGrupoColaborador().consultarGrupoColaboradorComParticipantesPorCodigoColaboradorPorTipoVisao(
                            usuario.getColaboradorVO().getCodigo(), "AC", false,
                            nivelDadosGrupos, getEmpresaLogado().getCodigo()));
                    for (GrupoColaboradorVO grupo : listaGruposAC) {
                        GrupoColaboradorParticipanteVO novoParticipante = new GrupoColaboradorParticipanteVO();
                        novoParticipante.setUsuarioParticipante(usuario);
                        novoParticipante.setColaboradorParticipante(usuario.getColaboradorVO());
                        grupo.setGrupoColaboradorParticipanteVOs(new ArrayList<GrupoColaboradorParticipanteVO>());
                        grupo.getGrupoColaboradorParticipanteVOs().add(novoParticipante);
                    }
                    listaGrupos.addAll(listaGruposAC);
                }

                List<GrupoColaboradorVO> novaLista = new ArrayList<GrupoColaboradorVO>();
                for (GrupoColaboradorVO grupoColaboradorVO : listaGrupos) {
                    if (!novaLista.contains(grupoColaboradorVO)) {
                        novaLista.add(grupoColaboradorVO);
                    }
                }
                listaGrupos = novaLista;

                List<GrupoColaboradorParticipanteVO> usuariosSemGrupo = getFacade().getGrupoColaboradorParticipante().
                        consultarUsuariosSemGrupo(getEmpresaLogado().getCodigo(), nivelDadosGrupos);
                if (!usuariosSemGrupo.isEmpty()) {
                    criaGrupoVazio(usuariosSemGrupo);
                }
            }
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            Uteis.logar(e, GrupoTelaControle.class);
        }
    }

    /**
     * cria um grupo pro usuario que nao tem grupos
     */
    private void criaGrupoVazio(List<GrupoColaboradorParticipanteVO> usuarios) {
        // cria um grupo
        GrupoColaboradorVO novoGrupo = new GrupoColaboradorVO();
        // coloca os dados no novo grupo
        novoGrupo.setDescricao("SEM GRUPO");
        novoGrupo.setDescricaoOrdenacao("ZZZZ_SEM GRUPO");
        novoGrupo.setSemGrupo(true);
        for (GrupoColaboradorParticipanteVO novoParticipante : usuarios) {
            novoGrupo.getGrupoColaboradorParticipanteVOs().add(novoParticipante);
        }
        // adiciona o grupo na lista
        listaGrupos.add(novoGrupo);
    }

    /**
     * a partir da lista de grupos monta uma lista unica de colaboradores
     * desconsiderando os que são repetidos
     */
    private void montarListaSemRepeticao() {
        listaColaboradores = new ArrayList<ColaboradorVO>();
        // percorre a lista de grupos
        for(GrupoColaboradorVO i : listaGrupos) {
            List<GrupoColaboradorParticipanteVO> listaParticipantes = i.getGrupoColaboradorParticipanteVOs();
            // percorre a lista de participantes de cada grupo
            for(GrupoColaboradorParticipanteVO j : listaParticipantes) {
                ColaboradorVO participante = j.getColaboradorParticipante();
                // se colaborador ainda nao existe na lista
                if(!listaColaboradores.contains(participante))
                    listaColaboradores.add(participante);
            }
        }
    }

    /**
     * Para uso em relatorios, quando a lista de colaboradores nao depende do usuario logado
     * @param colaboradores
     * @return
     * @throws Exception
     */
    public static List<GrupoColaboradorVO> separarColaboradoresEmGrupos(List<ColaboradorVO> colaboradores, Integer empresa) throws Exception {
        List<GrupoColaboradorVO> grupos = new ArrayList<GrupoColaboradorVO>();
        GrupoColaboradorVO semGrupo = new GrupoColaboradorVO();
        semGrupo.setDescricao("USUÁRIOS SEM GRUPO");

        // para cada colaborador
        for(ColaboradorVO colaborador : colaboradores) {
            // verifica os grupos que ele faz parte
            List<GrupoColaboradorParticipanteVO> lista = getFacade().getGrupoColaboradorParticipante().consultarPorColaborador(colaborador.getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);
            // se usuario nao tem grupo
            if(lista.isEmpty()) {
                // * ATENCAO! os participantes deste grupo NAO possuem codigo, pois este grupo nao existe realmente.
                GrupoColaboradorParticipanteVO participante = new GrupoColaboradorParticipanteVO();
                participante.setColaboradorParticipante(colaborador);
                semGrupo.getGrupoColaboradorParticipanteVOs().add(participante);
            }

            for(GrupoColaboradorParticipanteVO participante : lista) {
                // verifica se o grupo que ele faz parte já está na lista
                int indice = grupos.indexOf(participante.getGrupoColaborador());
                // se não estiver 
                if(indice < 0) {
                    GrupoColaboradorVO aux = getFacade().getGrupoColaborador().consultarPorChavePrimaria(participante.getGrupoColaborador().getCodigo(), Uteis.NIVELMONTARDADOS_TELACONSULTA, empresa);
                    // adiciona o participante no grupo
                    aux.getGrupoColaboradorParticipanteVOs().add(participante);
                    // adiciona o grupo na lista
                    grupos.add(aux);
                } else
                    // se o grupo estiver na lista adiciona o participante nela
                    grupos.get(indice).getGrupoColaboradorParticipanteVOs().add(participante);
            }
        }

        if(!semGrupo.getGrupoColaboradorParticipanteVOs().isEmpty()) {
            grupos.add(semGrupo);
        }

        return grupos;
    }

    /**
     * retorna a lista de grupos do usuario.
     * @return
     */
    public List<GrupoColaboradorVO> getListaGrupos() {
        List<GrupoColaboradorVO> novaLista = new ArrayList<GrupoColaboradorVO>();
        try {
            for(GrupoColaboradorVO aux : listaGrupos)
                novaLista.add((GrupoColaboradorVO)aux.getClone(true));
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        } finally {
            return novaLista;
        }
    }

    /**
     * retorna a lista de colaboradores não repetidos.
     * @return
     */
    public List<ColaboradorVO> getListaColaboradores() {
        List<ColaboradorVO> novaLista = new ArrayList<ColaboradorVO>();
        try {
            for(ColaboradorVO aux : listaColaboradores)
                novaLista.add((ColaboradorVO)aux.getClone(true));
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        } finally {
            return novaLista;
        }
    }
}

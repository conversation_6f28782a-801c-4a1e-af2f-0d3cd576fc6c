package controle.basico;

import br.com.pactosolucoes.ce.comuns.enumerador.Mes;
import br.com.pactosolucoes.comuns.notificacao.RecursoSistema;
import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.enumeradores.TipoPlanoEnum;
import br.com.pactosolucoes.estrutura.exportador.controle.ExportadorListaControle;
import controle.arquitetura.SuperControle;
import controle.arquitetura.security.LoginControle;
import controle.basico.clube.MensagemGenericaControle;
import negocio.comuns.arquitetura.LogVO;
import negocio.comuns.basico.enumerador.TipoIndiceFinanceiroEnum;
import negocio.comuns.plano.IndiceFinanceiroReajustePrecoVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import servicos.bi.exportador.Exportador;
import servicos.bi.exportador.RelatorioBuilder;

import javax.faces.event.ActionEvent;
import javax.faces.model.SelectItem;
import java.io.File;
import java.util.ArrayList;
import java.util.List;

/**
 * Created by ulisses on 19/11/2016.
 */
public class IndiceFinanceiroReajustePrecoControle extends SuperControle {

    private List<SelectItem> listaSelectItemTipoIndice;
    private List<SelectItem> listaSelectItemMes;
    private List<SelectItem> listaSelectItemTipoPlano;
    private IndiceFinanceiroReajustePrecoVO indiceFinanceiroReajustePrecoVO;
    private String urlUploadArquivo;


    public IndiceFinanceiroReajustePrecoControle()throws Exception{
        inicializarDados();
    }

    private void inicializarDados() throws Exception{
        montarListaSelectItemTipoIndice();
    }

    public String novo() {
        this.indiceFinanceiroReajustePrecoVO = new IndiceFinanceiroReajustePrecoVO();
        montarSucesso("msg_entre_dados");
        return "editarIndiceFinanceiro";
    }

    private File criarArquivo(String nomeArquivo) throws Exception{
        File arquivo = new File(Uteis.obterCaminhoWeb() + File.separator + "relatorio" + File.separator + nomeArquivo);
        if (arquivo.exists()) {
            arquivo.delete();
        }else{
            new File(getDiretorioArquivos() + File.separator + "relatoriofinanceiro").mkdirs();
        }
        arquivo.createNewFile();
        return arquivo;
    }

    public void imprimirRelatorioExcel(){
        try{
            String nomeArquivo = "indicefinanceiro"+System.nanoTime()+".xlsx";
            List<IndiceFinanceiroReajustePrecoVO> lista = getFacade().getIndiceFinanceiroReajustePreco().consultarParaImpressao("", "asc", "");
            gerarRelatorioExcel(lista, criarArquivo(nomeArquivo));
            this.urlUploadArquivo = "DownloadSV?mimeType=application/vnd.ms-excel&relatorio=" + nomeArquivo;
        }catch (Exception ex){
            montarErro(ex);
            ex.printStackTrace();
        }
    }

    private void gerarRelatorioExcel(List<IndiceFinanceiroReajustePrecoVO> resultado, File arquivo) throws  Exception{
        RelatorioBuilder relatorio = new RelatorioBuilder();
        relatorio.dado(resultado);
        relatorio.titulo("Relatório Índice Financeiro para Reajuste de Preços");
        relatorio
                .addColuna("Tipo Índice", "tipoIndice_Apresentar")
                .addColuna("Percentual Acumulado","percentualAcumulado")
                .addColuna("Mês", "mes")
                .addColuna("Ano", "ano")
                .addColuna("Renovação automática de contrato recorrência", "aplicarreajusterenovacaocontratorecorrencia_apresentar");
        Exportador.exportarExcel(relatorio, arquivo);
    }


    public String gravar() {
        try{
            if (this.indiceFinanceiroReajustePrecoVO.isNovoObj()) {
                getFacade().getIndiceFinanceiroReajustePreco().incluir(this.indiceFinanceiroReajustePrecoVO);
                registrarLogInclusaoManualmente();
                notificarRecursoEmpresaIndiceFinanceiro();
            } else {
                getFacade().getIndiceFinanceiroReajustePreco().alterar(this.indiceFinanceiroReajustePrecoVO);
                registrarLogAlteracaoManualmente();
                notificarRecursoEmpresaIndiceFinanceiro();
            }

            this.indiceFinanceiroReajustePrecoVO.registrarObjetoVOAntesDaAlteracao();
            montarSucesso("msg_dados_gravados");
        }catch (Exception e){
            montarErro(e);
        }
        return "editarIndiceFinanceiro";
    }

    public String editar() {
        Integer codigoConsulta = Integer.parseInt(request().getParameter("chavePrimaria"));
        try {
            this.indiceFinanceiroReajustePrecoVO = getFacade().getIndiceFinanceiroReajustePreco().consultarPorCodigo(codigoConsulta, Uteis.NIVELMONTARDADOS_TODOS);
            indiceFinanceiroReajustePrecoVO.setNovoObj(false);
            this.indiceFinanceiroReajustePrecoVO.registrarObjetoVOAntesDaAlteracao();
            montarSucesso("msg_entre_dados");
        } catch (Exception e) {
            montarErro(e);
        }
        return "editarIndiceFinanceiro";
    }

    public String excluir() {
        try {
            getFacade().getIndiceFinanceiroReajustePreco().excluir(this.indiceFinanceiroReajustePrecoVO);
            incluirLogExclusao();
            this.indiceFinanceiroReajustePrecoVO  = new IndiceFinanceiroReajustePrecoVO();
            montarSucesso("msg_dados_excluidos");
        } catch (Exception e) {
            montarErro(e);
        }
        return "editarIndiceFinanceiro";
    }


    public void confirmarExcluir(){
        MensagemGenericaControle control = (MensagemGenericaControle) getControlador(MensagemGenericaControle.class);
        control.setMensagemDetalhada("", "");
        setMsgAlert("Richfaces.showModalPanel('mdlMensagemGenerica');");
        control.init("Exclusão de Indice Financeiro",
                "Deseja excluir o índice financeiro?",
                this, "excluir", "", "", "", "form");
    }

    public String inicializarConsultar() {
        setPaginaAtualDeTodas("0/0");
        setListaConsulta(new ArrayList());
        definirVisibilidadeLinksNavegacao(0, 0);
        montarSucesso("msg_entre_prmconsulta");
        return "consultarIndiceFinanceiro";
    }

    public void realizarConsultaLogObjetoSelecionado() {
        LoginControle loginControle = (LoginControle) context().getExternalContext().getSessionMap().get("LoginControle");
        loginControle.setNomeClasse("INDICEFINANCEIROREAJUSTEPRECO");
        loginControle.consultarLogObjetoSelecionado("INDICEFINANCEIROREAJUSTEPRECO", indiceFinanceiroReajustePrecoVO.getCodigo(), null);
    }
    
    public void realizarConsultaLogObjetoGeral() {
        indiceFinanceiroReajustePrecoVO =  new IndiceFinanceiroReajustePrecoVO();
        realizarConsultaLogObjetoSelecionado();
    }
    


    private void registrarLogInclusaoManualmente()throws Exception{
        LogVO obj = new LogVO();
        obj.setChavePrimaria(this.indiceFinanceiroReajustePrecoVO.getCodigo().toString());
        obj.setNomeEntidade("INDICEFINANCEIROREAJUSTEPRECO");
        obj.setNomeEntidadeDescricao("INDICE FINANCEIRO REAJUSTE PREÇO");
        obj.setResponsavelAlteracao(getUsuarioLogado().getNome());
        obj.setUserOAMD(getUsuarioLogado().getUserOamd());
        obj.setNomeCampo("MENSAGEM");
        obj.setValorCampoAlterado("");
        StringBuilder msg = new StringBuilder();
        String simNao ="";
        obj.setOperacao("INCLUSÃO");
        msg.append("Tipo índice:").append(this.indiceFinanceiroReajustePrecoVO.getTipoIndiceEnum().getSigla() + " - " + this.indiceFinanceiroReajustePrecoVO.getTipoIndiceEnum().getDescricao()).append("\n");
        msg.append("Mês:").append(this.indiceFinanceiroReajustePrecoVO.getMes()).append("\n");
        msg.append("Ano:").append(this.indiceFinanceiroReajustePrecoVO.getAno()).append("\n");
        msg.append("Percentual acumulado:").append(String.format("%.2f", this.indiceFinanceiroReajustePrecoVO.getPercentualAcumulado())).append("\n");
        simNao = this.indiceFinanceiroReajustePrecoVO.getAplicarreajusterenovacaocontratorecorrencia() ? "SIM" : "NÃO";
        msg.append("Aplicar reajuste para Renovação automática de contrato recorrência:").append(simNao).append("\n");
        msg.append("Tipo plano: "+TipoPlanoEnum.getTipoPlanoEnum(this.indiceFinanceiroReajustePrecoVO.getTipoPlano()));
        obj.setValorCampoAlterado(msg.toString());
        obj.setDataAlteracao(negocio.comuns.utilitarias.Calendario.hoje());
        registrarLogObjetoVO(obj, this.indiceFinanceiroReajustePrecoVO.getCodigo());
    }

    private void registrarLogAlteracaoManualmente()throws Exception{
        IndiceFinanceiroReajustePrecoVO antigo = (IndiceFinanceiroReajustePrecoVO)indiceFinanceiroReajustePrecoVO.getObjetoVOAntesAlteracao();
        IndiceFinanceiroReajustePrecoVO novo = this.indiceFinanceiroReajustePrecoVO;
        String mesAlterado = null;
        String anoAlterado = null;
        String tipoIndiceAlterado = null;
        String renovavelAutoAlterado = null;
        String percentualAlterado = null;
        String tipoPlano = null;
        if (!novo.getMes().equals(antigo.getMes())){
            mesAlterado = antigo.getMes() + " para " + novo.getMes();
        }
        if (!novo.getAno().equals(antigo.getAno())){
            anoAlterado = antigo.getAno() + " para " + novo.getAno();
        }
        if (!novo.getTipoIndice().equals(antigo.getTipoIndice())){
            tipoIndiceAlterado = antigo.getTipoIndiceEnum().getSigla() + " - " + antigo.getTipoIndiceEnum().getDescricao() + " para " +novo.getTipoIndiceEnum().getSigla() + " - " + novo.getTipoIndiceEnum().getDescricao();
        }
        if (!novo.getAplicarreajusterenovacaocontratorecorrencia().equals(antigo.getAplicarreajusterenovacaocontratorecorrencia())){
            renovavelAutoAlterado = antigo.getAplicarreajusterenovacaocontratorecorrencia_apresentar() +  " para " + novo.getAplicarreajusterenovacaocontratorecorrencia_apresentar() ;
        }
        if (!novo.getPercentualAcumulado().equals(antigo.getPercentualAcumulado())) {
            percentualAlterado = String.format("%.2f", antigo.getPercentualAcumulado()) +  " para " +   String.format("%.2f", novo.getPercentualAcumulado());
        }
        if (!novo.getTipoPlano().equals(antigo.getTipoPlano())) {
            tipoPlano = TipoPlanoEnum.getTipoPlanoEnum(antigo.getTipoPlano())+  " para " +   TipoPlanoEnum.getTipoPlanoEnum(novo.getTipoPlano());
        }

        if ((mesAlterado != null) || (anoAlterado != null) || (tipoIndiceAlterado != null) || (renovavelAutoAlterado != null) || (percentualAlterado != null)) {
            LogVO obj = new LogVO();
            obj.setChavePrimaria(this.indiceFinanceiroReajustePrecoVO.getCodigo().toString());
            obj.setNomeEntidade("INDICEFINANCEIROREAJUSTEPRECO");
            obj.setNomeEntidadeDescricao("INDICE FINANCEIRO REAJUSTE PREÇO");
            obj.setResponsavelAlteracao(getUsuarioLogado().getNome());
            obj.setUserOAMD(getUsuarioLogado().getUserOamd());
            obj.setNomeCampo("MENSAGEM");
            obj.setValorCampoAlterado("");
            StringBuilder msg = new StringBuilder();
            String simNao ="";
            obj.setOperacao("ALTERAÇÃO");
            if (tipoIndiceAlterado != null)
              msg.append("Tipo índice:").append(tipoIndiceAlterado).append("\n");
            if (mesAlterado != null)
              msg.append("Mês:").append(mesAlterado).append("\n");
            if (anoAlterado != null)
              msg.append("Ano:").append(anoAlterado).append("\n");
            if (percentualAlterado != null)
              msg.append("Percentual acumulado:").append(percentualAlterado).append("\n");
            if (renovavelAutoAlterado !=null )
               msg.append("Aplicar reajuste para Renovação automática de contrato recorrência:").append(renovavelAutoAlterado).append("\n");
            if(UteisValidacao.emptyString(tipoPlano)){
                msg.append("Tipo Plano: ").append(tipoPlano).append("\n");
            }
            obj.setValorCampoAlterado(msg.toString());
            obj.setDataAlteracao(negocio.comuns.utilitarias.Calendario.hoje());
            registrarLogObjetoVO(obj, this.indiceFinanceiroReajustePrecoVO.getCodigo());
        }
    }


    private void montarListaSelectItemTipoIndice() throws Exception{
        this.listaSelectItemTipoIndice = new ArrayList<SelectItem>();
        this.listaSelectItemTipoIndice.add(new SelectItem(0, ""));
        for (TipoIndiceFinanceiroEnum tipo: TipoIndiceFinanceiroEnum.values()){
            this.listaSelectItemTipoIndice.add(new SelectItem(tipo.getCodigo(), tipo.getSigla() + " - " + tipo.getDescricao()));
        }
    }

    public void exportar(ActionEvent evt) throws Exception {
        ExportadorListaControle exportadorListaControle = (ExportadorListaControle) JSFUtilities.getFromRequest(ExportadorListaControle.class.getSimpleName());
        String paramsTableFiltrada = context().getExternalContext().getRequestParameterMap().get("paramsTableFiltrada");

        String[] split = paramsTableFiltrada.split(",");
        String campoOrdenacao = split[0].replace("[", "");
        String ordem = split[1];
        String filtro = split[2].replace("''", "");
        filtro = filtro.replace("]", "");
        List listaParaImpressao = getFacade().getIndiceFinanceiroReajustePreco().consultarParaImpressao(filtro, ordem, campoOrdenacao);
        exportadorListaControle.exportar(evt, listaParaImpressao, filtro, null );
    }


    public List<SelectItem> getListaSelectItemTipoIndice() {
        return listaSelectItemTipoIndice;
    }

    public void setListaSelectItemTipoIndice(List<SelectItem> listaSelectItemTipoIndice) {
        this.listaSelectItemTipoIndice = listaSelectItemTipoIndice;
    }

    public IndiceFinanceiroReajustePrecoVO getIndiceFinanceiroReajustePrecoVO() {
        return indiceFinanceiroReajustePrecoVO;
    }

    public void setIndiceFinanceiroReajustePrecoVO(IndiceFinanceiroReajustePrecoVO indiceFinanceiroReajustePrecoVO) {
        this.indiceFinanceiroReajustePrecoVO = indiceFinanceiroReajustePrecoVO;
    }

    public String getUrlUploadArquivo() {
        return urlUploadArquivo;
    }

    public void setUrlUploadArquivo(String urlUploadArquivo) {
        this.urlUploadArquivo = urlUploadArquivo;
    }
    public void incluirLogExclusao() throws Exception {
        try {
            indiceFinanceiroReajustePrecoVO.setObjetoVOAntesAlteracao(new IndiceFinanceiroReajustePrecoVO());
            indiceFinanceiroReajustePrecoVO.setNovoObj(true);
            registrarLogExclusaoTodosDadosObjetoVO(indiceFinanceiroReajustePrecoVO, indiceFinanceiroReajustePrecoVO.getCodigo(), "INDICEFINANCEIROREAJUSTEPRECO", 0);
        } catch (Exception e) {
            registrarLogErroObjetoVO("INDICEFINANCEIROREAJUSTEPRECO", indiceFinanceiroReajustePrecoVO.getCodigo(), "ERRO AO GERAR LOG DE EXCLUSÃO DE INDICEFINANCEIROREAJUSTEPRECO", this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
    }

    public List<SelectItem> getListaSelectItemMes() {
        if(UteisValidacao.emptyList(listaSelectItemMes)){
            listaSelectItemMes = new ArrayList<SelectItem>();
            for(Mes m : Mes.values()){
                listaSelectItemMes.add(new SelectItem(m.getCodigoStr(), m.getDescricao()));
            }
        }
        return listaSelectItemMes;
    }

    public void setListaSelectItemMes(List<SelectItem> listaSelectItemMes) {
        this.listaSelectItemMes = listaSelectItemMes;
    }

    public List<SelectItem> getListaSelectItemTipoPlano(){
        if(UteisValidacao.emptyList(listaSelectItemTipoPlano)){
            listaSelectItemTipoPlano = new ArrayList<>();
            listaSelectItemTipoPlano.add(new SelectItem(TipoPlanoEnum.PLANO_NORMAL.getCodigo(), TipoPlanoEnum.PLANO_NORMAL.getDescricao()));
            listaSelectItemTipoPlano.add(new SelectItem(TipoPlanoEnum.PLANO_RECORRENCIA.getCodigo(), TipoPlanoEnum.PLANO_RECORRENCIA.getDescricao()));
        }
        return listaSelectItemTipoPlano;
    }

    public void setListaSelectItemTipoPlano(List<SelectItem> listaSelectItemTipoPlano) {
        this.listaSelectItemTipoPlano = listaSelectItemTipoPlano;
    }

    public void notificarRecursoEmpresaIndiceFinanceiro() {
        notificarRecursoEmpresa(RecursoSistema.INDICE_FINANCEIRO_ESPECIFICO_MARCOU);
    }

}

package controle.basico;

import java.util.Iterator;
import java.util.Date;
import negocio.facade.jdbc.basico.HistoricoVinculo;
import negocio.comuns.utilitarias.*;
import negocio.comuns.basico.*;
import negocio.comuns.crm.ClienteOrganizadorCarteiraVO;

import javax.faces.model.SelectItem;
import java.util.List;
import java.util.ArrayList;
import controle.arquitetura.SuperControle;

/**
 * Classe responsável por implementar a interação entre os componentes JSF das páginas 
 * historicoVinculoForm.jsp historicoVinculoCons.jsp) com as funcionalidades da classe <code>HistoricoVinculo</code>.
 * Implemtação da camada controle (Backing Bean).
 * @see SuperControle
 * @see HistoricoVinculo
 * @see HistoricoVinculoVO
*/
public class HistoricoVinculoControle extends SuperControle {
    private ClienteVO cliente = new ClienteVO();
    private HistoricoVinculoVO historicoVinculoVO;
    protected List listaSelectItemCliente;
    protected List listaSelectItemColaborador;
    private List<HistoricoVinculoVO> listaHistoricoVinculo = new ArrayList<HistoricoVinculoVO>();

    /**
    * Interface <code>HistoricoVinculoInterfaceFacade</code> responsável pela interconexão da camada de controle com a camada de negócio.
    * Criando uma independência da camada de controle com relação a tenologia de persistência dos dados (DesignPatter: Façade).
    */

    public HistoricoVinculoControle() throws Exception {
        obterUsuarioLogado();
        setControleConsulta(new ControleConsulta());
        setMensagemID("msg_entre_prmconsulta");
    }

    /**
    * Rotina responsável por disponibilizar um novo objeto da classe <code>HistoricoVinculo</code>
    * para edição pelo usuário da aplicação.
    */
    public String novo() {
        setHistoricoVinculoVO(new HistoricoVinculoVO());
        inicializarListasSelectItemTodosComboBox();
        setMensagemID("msg_entre_dados");
        return "editar";
    }

    public void historicoCliente() throws Exception {
        ClienteControle controle = (ClienteControle)context().getExternalContext().getSessionMap().get("ClienteControle");
        if(getCliente() == null || controle.getClienteVO().getCodigo().intValue() != 0) {
            setListaHistoricoVinculo(new ArrayList<HistoricoVinculoVO>());
            setCliente(controle.getClienteVO());
            obterUsuarioLogado();
            listaVerHistoricoVinculo();
        }
    }
    
    /**
     * este método lista o histórico de vinculos de um cliente no organizador de carteiras
     * <AUTHOR>
     * 27/06/2011
     */
    public void historicoClienteOrganizadorCarteira() throws Exception {
    	ClienteOrganizadorCarteiraVO cliente = (ClienteOrganizadorCarteiraVO) context().getExternalContext().getRequestMap().get("vinculo");
    	setListaHistoricoVinculo(getFacade().getHistoricoVinculo().consultarPorCodigoClienteOrganizadorCarteira(cliente.getCliente().getCodigo(), true, Uteis.NIVELMONTARDADOS_TODOS));
    }

    /**
    * Rotina responsável por disponibilizar os dados de um objeto da classe <code>HistoricoVinculo</code> para alteração.
    * O objeto desta classe é disponibilizado na session da página (request) para que o JSP correspondente possa disponibilizá-lo para edição.
    */
    public String editar() {
        HistoricoVinculoVO obj = (HistoricoVinculoVO)context().getExternalContext().getRequestMap().get("historicoVinculo");
        inicializarAtributosRelacionados(obj);
        obj.setNovoObj(false);
        setHistoricoVinculoVO(obj);
        inicializarListasSelectItemTodosComboBox();
        setMensagemID("msg_dados_editar");
        return "editar";
    }

    /**
    * Método responsável inicializar objetos relacionados a classe <code>HistoricoVinculoVO</code>.
    * Esta inicialização é necessária por exigência da tecnologia JSF, que não trabalha com valores nulos para estes atributos.
    */
    public void inicializarAtributosRelacionados(HistoricoVinculoVO obj) {
        if (obj.getCliente() == null) {
            obj.setCliente(new ClienteVO());
        }
        if (obj.getColaborador() == null) {
            obj.setColaborador(new ColaboradorVO());
        }
    }

    /**
    * Rotina responsável por gravar no BD os dados editados de um novo objeto da classe <code>HistoricoVinculo</code>.
    * Caso o objeto seja novo (ainda não gravado no BD) é acionado a operação <code>incluir()</code>. Caso contrário é acionado o <code>alterar()</code>.
    * Se houver alguma inconsistência o objeto não é gravado, sendo re-apresentado para o usuário juntamente com uma mensagem de erro.
    */
    public String gravar() {
        try {
            if (historicoVinculoVO.isNovoObj().booleanValue()) {
                getFacade().getHistoricoVinculo().incluir(historicoVinculoVO);
            } else {
                getFacade().getHistoricoVinculo().alterar(historicoVinculoVO);
            }
            setMensagemID("msg_dados_gravados");
            return "editar";
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            return "editar";
        }
    }

    /**
    * Rotina responsavel por executar as consultas disponiveis no JSP HistoricoVinculoCons.jsp.
    * Define o tipo de consulta a ser executada, por meio de ComboBox denominado campoConsulta, disponivel neste mesmo JSP.
    * Como resultado, disponibiliza um List com os objetos selecionados na sessao da pagina.
    */
    @Override
    public String consultar() {
        try {
            super.consultar();
            List objs = new ArrayList();
            if (getControleConsulta().getCampoConsulta().equals("codigo")) {
                if (getControleConsulta().getValorConsulta().equals("")) {
                    getControleConsulta().setValorConsulta("0");
                }
                int valorInt = Integer.parseInt(getControleConsulta().getValorConsulta());
                objs = getFacade().getHistoricoVinculo().consultarPorCodigo(new Integer(valorInt), true, Uteis.NIVELMONTARDADOS_TODOS);
            }
            if (getControleConsulta().getCampoConsulta().equals("situacaoCliente")) {
                objs = getFacade().getHistoricoVinculo().consultarPorSituacaoCliente(getControleConsulta().getValorConsulta(), Uteis.NIVELMONTARDADOS_TODOS);
            }
            if (getControleConsulta().getCampoConsulta().equals("dataRegistro")) {
                Date valorData = Uteis.getDate(getControleConsulta().getValorConsulta());
                objs = getFacade().getHistoricoVinculo().consultarPorDataRegistro(Uteis.getDateTime(valorData,0,0,0), Uteis.getDateTime(valorData,23,59,59), true, Uteis.NIVELMONTARDADOS_TODOS);
            }
            if (getControleConsulta().getCampoConsulta().equals("tipoHistoricoVinculo")) {
                objs = getFacade().getHistoricoVinculo().consultarPorTipoHistoricoVinculo(getControleConsulta().getValorConsulta(), true, Uteis.NIVELMONTARDADOS_TODOS);
            }
            if (getControleConsulta().getCampoConsulta().equals("situacaoColaborador")) {
                objs = getFacade().getHistoricoVinculo().consultarPorSituacaoColaborador(getControleConsulta().getValorConsulta(), Uteis.NIVELMONTARDADOS_TODOS);
            }
            setListaConsulta(objs);
            setMensagemID("msg_dados_consultados");
            return "consultar";
        } catch (Exception e) {
            setListaConsulta(new ArrayList());
            setMensagemDetalhada("msg_erro", e.getMessage());
            return "consultar";
        }
    }

    /**
     * Operação responsável por processar a exclusão um objeto da classe <code>HistoricoVinculoVO</code>
     * Após a exclusão ela automaticamente aciona a rotina para uma nova inclusão.
     */
    public String excluir() {
        try {
            getFacade().getHistoricoVinculo().excluir(historicoVinculoVO);
            novo();
            setMensagemID("msg_dados_excluidos");
            return "editar";
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            return "editar";
        }
    }

    /**
     * Método responsável por gerar uma lista de objetos do tipo <code>SelectItem</code> para preencher
     * o comboBox relativo ao atributo <code>Colaborador</code>.
    */
    public void montarListaSelectItemColaborador(String prm) throws Exception {
        List resultadoConsulta = consultarColaboradorPorSituacao(prm);
        Iterator i = resultadoConsulta.iterator();
        List objs = new ArrayList();
        objs.add(new SelectItem(new Integer(0), ""));
        while (i.hasNext()) {
            ColaboradorVO obj = (ColaboradorVO)i.next();
            objs.add(new SelectItem(obj.getCodigo(), obj.getSituacao().toString()));
        }
        Uteis.liberarListaMemoria(resultadoConsulta);
        setListaSelectItemColaborador(objs);
    }

    /**
     * Método responsável por atualizar o ComboBox relativo ao atributo <code>Colaborador</code>.
     * Buscando todos os objetos correspondentes a entidade <code>Colaborador</code>. Esta rotina não recebe parâmetros para filtragem de dados, isto é
     * importante para a inicialização dos dados da tela para o acionamento por meio requisições Ajax.
    */
    public void montarListaSelectItemColaborador() {
        try {
            montarListaSelectItemColaborador("");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * Método responsável por consultar dados da entidade <code><code> e montar o atributo <code>situacao</code>
     * Este atributo é uma lista (<code>List</code>) utilizada para definir os valores a serem apresentados no ComboBox correspondente
    */
    public List consultarColaboradorPorSituacao(String situacaoPrm) throws Exception {
        List lista = getFacade().getColaborador().consultarPorSituacao(situacaoPrm, getEmpresaLogado().getCodigo(), false, Uteis.NIVELMONTARDADOS_TODOS);
        return lista;
    }

    /**
     * Método responsável por gerar uma lista de objetos do tipo <code>SelectItem</code> para preencher
     * o comboBox relativo ao atributo <code>Cliente</code>.
    */
    public void montarListaSelectItemCliente(String prm) throws Exception {
        List resultadoConsulta = consultarClientePorSituacao(prm);
        Iterator i = resultadoConsulta.iterator();
        List objs = new ArrayList();
        objs.add(new SelectItem(new Integer(0), ""));
        while (i.hasNext()) {
            ClienteVO obj = (ClienteVO)i.next();
            objs.add(new SelectItem(obj.getCodigo(), obj.getSituacao().toString()));
        }
        Uteis.liberarListaMemoria(resultadoConsulta);
        setListaSelectItemCliente(objs);
    }

    /**
     * Método responsável por atualizar o ComboBox relativo ao atributo <code>Cliente</code>.
     * Buscando todos os objetos correspondentes a entidade <code>Cliente</code>. Esta rotina não recebe parâmetros para filtragem de dados, isto é
     * importante para a inicialização dos dados da tela para o acionamento por meio requisições Ajax.
    */
    public void montarListaSelectItemCliente() {
        try {
            montarListaSelectItemCliente("");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * Método responsável por consultar dados da entidade <code><code> e montar o atributo <code>situacao</code>
     * Este atributo é uma lista (<code>List</code>) utilizada para definir os valores a serem apresentados no ComboBox correspondente
    */
    public List consultarClientePorSituacao(String situacaoPrm) throws Exception {
        List lista = getFacade().getCliente().consultarPorSituacao(situacaoPrm, getEmpresaLogado().getCodigo(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        return lista;
    }

    /**
     * Método responsável por inicializar a lista de valores (<code>SelectItem</code>) para todos os ComboBox's.
    */
    public void inicializarListasSelectItemTodosComboBox() {
        montarListaSelectItemCliente();
        montarListaSelectItemColaborador();
    }

    /**
    * Rotina responsável por atribui um javascript com o método de mascara para campos do tipo Data, CPF, CNPJ, etc.
    */
    public String getMascaraConsulta() {
        return "";
    }

    /**
    * Rotina responsável por preencher a combo de consulta da telas.
    */
    public List getTipoConsultaCombo() {
        List itens = new ArrayList();
        itens.add(new SelectItem("codigo", "Código"));
        itens.add(new SelectItem("situacaoCliente", "Cliente"));
        itens.add(new SelectItem("dataRegistro", "Data Registro"));
        itens.add(new SelectItem("tipoHistoricoVinculo", "Tipo Histórico Vinculo"));
        itens.add(new SelectItem("situacaoColaborador", "Colaborador"));
        return itens;
    }

    /**
    * Rotina responsável por organizar a paginação entre as páginas resultantes de uma consulta.
    */
    public String inicializarConsultar() {
        setListaConsulta(new ArrayList());
        setMensagemID("msg_entre_prmconsulta");
        return "consultar";
    }

    /**
    * Operação que libera todos os recursos (atributos, listas, objetos) do backing bean.
    * Garantindo uma melhor atuação do Garbage Coletor do Java. A mesma é automaticamente 
    * quando realiza o logout.
    */
    @Override
    protected void limparRecursosMemoria() {
        super.limparRecursosMemoria();
        historicoVinculoVO = null;
        Uteis.liberarListaMemoria(listaSelectItemCliente);
        Uteis.liberarListaMemoria(listaSelectItemColaborador);
    }

    public void listaVerHistoricoVinculo() {
        try {
            setListaHistoricoVinculo(getFacade().getHistoricoVinculo().consultarPorCodigoClienteOrganizadorCarteira(getCliente().getCodigo(), true, Uteis.NIVELMONTARDADOS_TODOS));
            setMensagemID("");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public List getListaSelectItemColaborador() {
        if (listaSelectItemColaborador == null) {
            listaSelectItemColaborador = new ArrayList();
        }
        return (listaSelectItemColaborador);
    }
     
    public void setListaSelectItemColaborador( List listaSelectItemColaborador ) {
        this.listaSelectItemColaborador = listaSelectItemColaborador;
    }

    public List getListaSelectItemCliente() {
        if (listaSelectItemCliente == null) {
            listaSelectItemCliente = new ArrayList();
        }
        return (listaSelectItemCliente);
    }
     
    public void setListaSelectItemCliente( List listaSelectItemCliente ) {
        this.listaSelectItemCliente = listaSelectItemCliente;
    }

    public HistoricoVinculoVO getHistoricoVinculoVO() {
        return historicoVinculoVO;
    }
     
    public void setHistoricoVinculoVO(HistoricoVinculoVO historicoVinculoVO) {
        this.historicoVinculoVO = historicoVinculoVO;
    }

    public List<HistoricoVinculoVO> getListaHistoricoVinculo() {
        return listaHistoricoVinculo;
    }

    public void setListaHistoricoVinculo(List<HistoricoVinculoVO> listaHistoricoVinculo) {
        this.listaHistoricoVinculo = listaHistoricoVinculo;
    }

    public ClienteVO getCliente() {
        return cliente;
    }

    public void setCliente(ClienteVO cliente) {
        this.cliente = cliente;
    }
}
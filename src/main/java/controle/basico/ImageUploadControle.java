/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package controle.basico;

import br.com.pactosolucoes.comuns.util.FileUtilities;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.acesso.PessoaFotoLocalAcesso;
import org.apache.commons.io.FileUtils;
import servicos.operacoes.midias.MidiaService;
import servicos.operacoes.midias.commons.MidiaEntidadeEnum;
import servicos.propriedades.PropsService;
import servicos.util.ExecuteRequestHttpService;

import java.io.File;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.util.HashMap;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * <AUTHOR> Executado via RemoteObject pelo Flex
 */
public class ImageUploadControle {

    private String nome = "Upload foto";

    public ImageUploadControle() {
    }

    public String doUpload(byte[] bytes, String idObjeto, String key) throws Exception {
        return doUpload(bytes, idObjeto, key, null);
    }

    public String doUpload(byte[] bytes, String idObjeto, String key, String tipoDoc) throws Exception {
        if (bytes.length == 0) {
            throw new Exception("Erro: Arquivo vazio.");
        }
        Uteis.logar(null, String.format("Pegou a foto da empresa %s, pessoa %s, tamanho -> %s bytes",
                key, idObjeto, bytes.length));
        String retorno = "";
        String gencode = "";
        int codigo = Integer.valueOf(idObjeto);
        if (codigo > 0) {
            DAO dao = new DAO();
            Connection con = dao.obterConexaoEspecifica(key);
            retorno = "OK";
            try {
                MidiaEntidadeEnum entidade = MidiaEntidadeEnum.FOTO_PESSOA;
                if (tipoDoc != null && !tipoDoc.isEmpty() && !tipoDoc.contains("usuariologado")) {
                    entidade = MidiaEntidadeEnum.valueOf(tipoDoc);
                    
                }
                if (PropsService.isTrue(PropsService.fotosParaNuvem)) {
                    File tmp = File.createTempFile(String.format("zwphoto-%s-%s-%s", key, entidade, idObjeto), ".jpg");
                    try {
                        Uteis.logar(null, "Criado arquivo temporario -> " + tmp.getAbsolutePath());
                        FileUtils.writeByteArrayToFile(tmp, bytes);
                        gencode = MidiaService.getInstance().uploadObject(key, entidade, idObjeto, tmp);
                        if (entidade.equals(MidiaEntidadeEnum.FOTO_PESSOA)) {
                            String sql = "UPDATE Pessoa set fotokey = ? WHERE codigo = ?";
                            PreparedStatement sqlAlterar = con.prepareStatement(sql);
                            sqlAlterar.setString(1, gencode);
                            sqlAlterar.setInt(2, codigo);
                            sqlAlterar.execute();
                        }
                        retorno = gencode;
                    } finally {
                        tmp.delete();
                    }
                } else {
                    try {
                        String sql = "UPDATE Pessoa set foto = ? WHERE codigo = ?";
                        PreparedStatement sqlAlterar = con.prepareStatement(sql);
                        sqlAlterar.setBytes(1, bytes);
                        sqlAlterar.setInt(2, codigo);
                        sqlAlterar.execute();
                    } catch (Exception ex) {
                        Logger.getLogger(ImageUploadControle.class.getName()).log(Level.SEVERE,
                                ex.getMessage(), ex);
                        throw ex;
                    }
                }
                if (entidade.equals(MidiaEntidadeEnum.FOTO_PESSOA)) {
                    PessoaFotoLocalAcesso fotoLocal = new PessoaFotoLocalAcesso(con);
                    fotoLocal.excluirFotoPessoaLocalAcesso(codigo);
                    fotoLocal = null;

                    try {
                        String urlTreino = PropsService.getPropertyValue(key, PropsService.urlTreino);
                        Map<String, String> params = new HashMap<String, String>();
                        params.put("codigopessoa", String.valueOf(codigo));
                        params.put("fotoKey", gencode);
                        ExecuteRequestHttpService.executeRequest(urlTreino+"/prest/config/"+key+"/baixarFotoAlunoPorFotoKey", params);
                    } catch (Exception ignored) {
                        System.out.println("Problema ao enviar solicitação de trocar a foto do aluno");
                    }


                }
            } finally {
                if (con != null && !con.isClosed()) {
                    con.close();
                }
            }
        }else{
            File f = new File(PropsService.getPropertyValue(
                                PropsService.diretorioArquivos) + "/fotos_pessoa_temp");
            f.getParentFile().mkdirs();
            //Excluir do arquivo as fotos
            Uteis.excluirArquivosDoDiretorio(f.getPath());
            String [] arquivos = tipoDoc.split("_");
            File tmp = new File(String.format(f.getPath()+"/%s@%s@%s.jpg", key, arquivos[1], 0));
            FileUtilities.saveToFile(bytes,tmp.getPath());
            retorno = "OK";
        }
        
        return retorno;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }
}

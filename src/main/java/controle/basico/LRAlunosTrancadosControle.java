package controle.basico;

import br.com.pactosolucoes.comuns.to.FiltrosConsultaRelatorioBVsTO;
import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.enumeradores.TipoColaboradorEnum;
import br.com.pactosolucoes.estrutura.ItemExportacaoEnum;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.TipoColaboradorVO;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import org.richfaces.component.UIDataTable;
import relatorio.controle.arquitetura.SuperControleRelatorio;
import relatorio.negocio.comuns.basico.ItemRelatorioTO;

import javax.faces.event.ActionEvent;
import java.io.File;
import java.util.*;

/**
 * Created on : 27/06/2013, 14:23:42
 *
 * <AUTHOR>
 */
public class LRAlunosTrancadosControle extends SuperControleRelatorio {

    private EmpresaVO empresaVO;
    private List<ColaboradorVO> professores;
    private ColaboradorVO professor = new ColaboradorVO();
    private List<ColaboradorVO> consultores;
    private ColaboradorVO consultor = new ColaboradorVO();
    private TipoColaboradorVO tipoColaboradorVO = new TipoColaboradorVO();
    private FiltrosConsultaRelatorioBVsTO filtrosConsulta;
    private List<ItemRelatorioTO> resultado = new ArrayList<ItemRelatorioTO>();
    private ItemRelatorioTO itemRelatorio = new ItemRelatorioTO();
    private boolean apresentarLinha1 = true;
    private boolean apresentarLinha2 = false;
    private boolean apresentarLinha3 = true;
    private boolean permissaoConsultaTodasEmpresas = false;
    private Integer filtroEmpresa;

    public LRAlunosTrancadosControle() {
        super();
        montarListaSelectItemEmpresa();
    }

    public void montarListaSelectItemEmpresa() {
        try {
            permissaoConsultaTodasEmpresas = permissao("ConsultarInfoTodasEmpresas");
            if (permissaoConsultaTodasEmpresas) {
                montarListaEmpresasComItemTodas();
            }
            setFiltroEmpresa(getEmpresaLogado().getCodigo());
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static String getCaminhoSubRelatorio() {
        return ("relatorio" + File.separator + "designRelatorio" + File.separator + "listasRelatorios" + File.separator);
    }

    public void recarregarTela() throws Exception {
        getListaConsultor();
        getListaProfessor();
    }

    public void limparFiltros() {
        professores = null;
        consultores = null;
    }

    public void prepareEditar(ActionEvent evt) {
        setNavigationCase(tratarNavigationCaseIntegracaoModulo("editar",
                "modulo_visualiza_cliente", evt));
    }

    public void irParaTelaCliente() {
        ItemRelatorioTO obj = (ItemRelatorioTO) context().getExternalContext().getRequestMap().get("item");
        try {
            if (obj == null) {
                throw new Exception("Cliente Não Encontrado.");
            } else {
                irParaTelaCliente(obj.getClienteVO());
            }
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void prepararImpr() {
        ItemRelatorioTO impr = (ItemRelatorioTO) context().getExternalContext().getRequestMap().get("impr");
        if (impr != null) {
            setItemRelatorio(impr);
        }
    }

    public void consultarAlunosTrancados() {
        try {
            limparMsg();
            setResultado(getFacade().getCliente().consultarAlunosTrancados(getConsultor(), consultores, getProfessor(), professores, getFiltroEmpresa()));
            if (UteisValidacao.emptyList(getResultado())) {
                throw new Exception("Nenhum trancamento encontrado.");
            }
        } catch (Exception e) {
            setMensagemID("msg_erro");
            setMensagemDetalhada(e);
            montarErro(e.getMessage());
        }
    }

    public int getTotalItens() {
        return getResultado().size();
    }

    public String getDadosImpressao() {
        StringBuilder filtros = new StringBuilder();
        boolean pontuacao = (isApresentarLinha1() || isApresentarLinha2() || isApresentarLinha3());
        if (isApresentarLinha1()) {
            filtros.append("Dados Cadastrais");
            if (pontuacao) {
                filtros.append(", ");
            } else {
                filtros.append(".");
            }
        }
        pontuacao = pontuacao && (isApresentarLinha3());
        if (isApresentarLinha2()) {
            filtros.append("Endereço");
            if (pontuacao) {
                filtros.append(", ");
            }
        }
        if (isApresentarLinha3()) {
            filtros.append("Contratos e Planos.");
        }
        return filtros.toString();
    }

    // <------------------------ IMPRIMIR ---------------------->
    private Map<String, Object> prepareParams() throws Exception {
        EmpresaVO empre = new EmpresaVO();
        if (!UteisValidacao.emptyNumber(getFiltroEmpresa())) {
            empre = getFacade().getEmpresa().consultarPorChavePrimaria(getFiltroEmpresa(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        }

        Map<String, Object> params = new HashMap<String, Object>();
        params.put("nomeRelatorio", "ListaAlunos");
        params.put("nomeEmpresa", empre.getNome());

        params.put("tituloRelatorio", "Relatório de Clientes Trancados");
        params.put("nomeDesignIReport", getDesignIReportRelatorio());
        params.put("usuario", getUsuarioLogado().getNomeAbreviado());

        params.put("listaObjetos", resultado);
        params.put("enderecoEmpresa", empre.getEndereco());
        params.put("cidadeEmpresa", empre.getCidade().getNome());
        params.put("totalClientes", getTotalItens());
        params.put("apresentarLinha1", isApresentarLinha1());
        params.put("apresentarLinha2", isApresentarLinha2());
        params.put("apresentarLinha3", isApresentarLinha3());
        params.put("dadosImpressao", getDadosImpressao());
        params.put("filtro", montarFiltros());

        return params;
    }

    public String montarFiltros() throws Exception {
        StringBuilder filtros = new StringBuilder();

        if (!getConsultores().isEmpty()) {
            String consultoresSelecionados = "";
            for (ColaboradorVO colab : consultores) {
                if (colab.getColaboradorEscolhido()) {
                    consultoresSelecionados += colab.getPessoa().getNome() + ", ";
                }
            }
            consultoresSelecionados = consultoresSelecionados.trim();
            if (consultoresSelecionados.length() > 0) {
                filtros.append("Consultor: ");
                consultoresSelecionados = consultoresSelecionados.substring(0, (consultoresSelecionados.length() - 1));
                filtros.append(consultoresSelecionados).append(" | ");
            }
        }

        if (!getProfessores().isEmpty()) {
            String professoresSelecionados = "";
            for (ColaboradorVO prof : professores) {
                if (prof.getColaboradorEscolhido()) {
                    professoresSelecionados += prof.getPessoa().getNome() + ", ";
                }
            }
            professoresSelecionados = professoresSelecionados.trim();
            if (professoresSelecionados.length() > 0) {
                filtros.append("Professor: ");
                professoresSelecionados = professoresSelecionados.substring(0, (professoresSelecionados.length() - 1));
                filtros.append(professoresSelecionados).append(" | ");
            }
        }

        String filtrosApresentar = filtros.toString();
        if (filtrosApresentar.length() > 0) {
            filtrosApresentar = filtrosApresentar.trim().substring(0, (filtrosApresentar.length() - 2));
        }
        return filtrosApresentar;
    }

    @Override
    public String getNomeRefPastaRelatorioRelatorioGeradoAgora() {
        String hRef = getNomeArquivoRelatorioGeradoAgora();
        if (!hRef.isEmpty()) {
            return "location.href=\"../../relatorio/" + hRef + "\"";
        } else {
            return "";
        }
    }

    public String getDesignIReportRelatorio() {
        return ("relatorio" + File.separator + "designRelatorio" + File.separator + "listasRelatorios" + File.separator + "ListaAlunos.jrxml");
    }

    public void imprimirListaRelatorio() throws Exception {
        try {
            limparMsg();
            setMsgAlert("");
            if (!resultado.isEmpty()) {
                UIDataTable dataTable = (UIDataTable) context().getViewRoot().findComponent("form:resultados");
                if (dataTable != null) {
                    String colunaOrdenacao = JSFUtilities.getColunaOrdenacao(dataTable);
                    if (!colunaOrdenacao.isEmpty()) {
                        String[] params = colunaOrdenacao.split(":");
                        Ordenacao.ordenarLista(resultado, params[0]);
                        if (params[1].equals("DESC")) {
                            Collections.reverse(resultado);
                        }
                    }
                }
            }
            if (resultado.isEmpty()) {
                throw new ConsistirException("Nenhum registro a ser impresso, faça a consulta novamente.");
            }
            validarPermissaoEIncluirLogExportacao(ItemExportacaoEnum.REL_TRANCADOS, resultado.size(), montarFiltros(), "pdf", "", "");
            apresentarRelatorioObjetos(prepareParams());
            setMsgAlert("abrirPopupPDFImpressao('relatorio/" + getNomeArquivoRelatorioGeradoAgora() + "','', 780, 595);Richfaces.hideModalPanel('relatorioImprimir');");
        } catch (Exception e) {
            montarErro(e);
        }
    }
    // <------------------------ LISTAS ---------------------->

    public void getListaConsultor() throws Exception {
        consultores = getFacade().getColaborador().consultarPorNomeTipoVinculoPossivel("", TipoColaboradorEnum.CONSULTOR, getFiltroEmpresa() == null ? 0 : getFiltroEmpresa(), Uteis.NIVELMONTARDADOS_MINIMOS);
    }

    public void getListaProfessor() throws Exception {
        professores = getFacade().getColaborador().consultarPorNomeTipoVinculoPossivel("", TipoColaboradorEnum.PROFESSOR, getFiltroEmpresa() == null ? 0 : getFiltroEmpresa(), Uteis.NIVELMONTARDADOS_MINIMOS);
    }

    // <------------------------ GETTER E SETTER ---------------------->

    public ColaboradorVO getConsultor() {
        return consultor;
    }

    public void setConsultor(ColaboradorVO consultor) {
        this.consultor = consultor;
    }

    public List<ColaboradorVO> getConsultores() throws Exception {
        if (consultores == null) {
            getListaConsultor();

        }
        return consultores;
    }

    public void setConsultores(List<ColaboradorVO> consultores) {
        this.consultores = consultores;
    }

    public FiltrosConsultaRelatorioBVsTO getFiltrosConsulta() {
        return filtrosConsulta;
    }

    public void setFiltrosConsulta(FiltrosConsultaRelatorioBVsTO filtrosConsulta) {
        this.filtrosConsulta = filtrosConsulta;
    }

    public ColaboradorVO getProfessor() {
        return professor;
    }

    public void setProfessor(ColaboradorVO professor) {
        this.professor = professor;
    }

    public List<ColaboradorVO> getProfessores() throws Exception {
        if (professores == null) {
            getListaProfessor();
        }
        return professores;
    }

    public void setProfessores(List<ColaboradorVO> professores) {
        this.professores = professores;
    }

    public List<ItemRelatorioTO> getResultado() {
        return resultado;
    }

    public void setResultado(List<ItemRelatorioTO> resultado) {
        this.resultado = resultado;
    }

    public TipoColaboradorVO getTipoColaboradorVO() {
        return tipoColaboradorVO;
    }

    public void setTipoColaboradorVO(TipoColaboradorVO tipoColaboradorVO) {
        this.tipoColaboradorVO = tipoColaboradorVO;
    }

    public ItemRelatorioTO getItemRelatorio() {
        return itemRelatorio;
    }

    public void setItemRelatorio(ItemRelatorioTO itemRelatorio) {
        this.itemRelatorio = itemRelatorio;
    }

    public boolean isApresentarLinha1() {
        return apresentarLinha1;
    }

    public void setApresentarLinha1(boolean apresentarLinha1) {
        this.apresentarLinha1 = apresentarLinha1;
    }

    public boolean isApresentarLinha2() {
        return apresentarLinha2;
    }

    public void setApresentarLinha2(boolean apresentarLinha2) {
        this.apresentarLinha2 = apresentarLinha2;
    }

    public boolean isApresentarLinha3() {
        return apresentarLinha3;
    }

    public void setApresentarLinha3(boolean apresentarLinha3) {
        this.apresentarLinha3 = apresentarLinha3;
    }

    public EmpresaVO getEmpresaVO() {
        return empresaVO;
    }

    public void setEmpresaVO(EmpresaVO empresaVO) {
        this.empresaVO = empresaVO;
    }

    public boolean isPermissaoConsultaTodasEmpresas() {
        return permissaoConsultaTodasEmpresas;
    }

    public void setPermissaoConsultaTodasEmpresas(boolean permissaoConsultaTodasEmpresas) {
        this.permissaoConsultaTodasEmpresas = permissaoConsultaTodasEmpresas;
    }

    public Integer getFiltroEmpresa() {
        if (filtroEmpresa == null) {
            filtroEmpresa = 0;
        }
        return filtroEmpresa;
    }

    public void setFiltroEmpresa(Integer filtroEmpresa) {
        this.filtroEmpresa = filtroEmpresa;
    }
}

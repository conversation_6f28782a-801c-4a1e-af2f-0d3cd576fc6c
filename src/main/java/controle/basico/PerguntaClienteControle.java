package controle.basico;
import negocio.interfaces.basico.PerguntaClienteInterfaceFacade;
import java.util.Hashtable;
import java.util.Enumeration;
import negocio.comuns.utilitarias.SelectItemOrdemValor;
import java.util.Collections;
import negocio.facade.jdbc.basico.PerguntaCliente;
import negocio.comuns.utilitarias.*;
import negocio.comuns.basico.*;
import javax.faces.model.SelectItem;
import java.util.List;
import java.util.ArrayList;
import controle.arquitetura.SuperControle;

/**
 * Classe responsável por implementar a interação entre os componentes JSF das páginas 
 * perguntaClienteForm.jsp perguntaClienteCons.jsp) com as funcionalidades da classe <code>PerguntaCliente</code>.
 * Implemtação da camada controle (Backing Bean).
 * @see SuperControle
 * @see PerguntaCliente
 * @see PerguntaClienteVO
*/
public class PerguntaClienteControle extends SuperControle {
    private PerguntaClienteVO perguntaClienteVO;
    /**
    * Interface <code>PerguntaClienteInterfaceFacade</code> responsável pela interconexão da camada de controle com a camada de negócio.
    * Criando uma independência da camada de controle com relação a tenologia de persistência dos dados (DesignPatter: Façade).
    */
    private RespostaPergClienteVO respostaPergClienteVO;

    public PerguntaClienteControle() throws Exception {
        obterUsuarioLogado();
        inicializarFacades();
        setControleConsulta(new ControleConsulta());
        setMensagemID("msg_entre_prmconsulta");
    }

    /**
    * Rotina responsável por disponibilizar um novo objeto da classe <code>PerguntaCliente</code>
    * para edição pelo usuário da aplicação.
    */
    public String novo() {
        setPerguntaClienteVO(new PerguntaClienteVO());
        setRespostaPergClienteVO(new RespostaPergClienteVO());
        setMensagemID("msg_entre_dados");
        return "editar";
    }

    /**
    * Rotina responsável por disponibilizar os dados de um objeto da classe <code>PerguntaCliente</code> para alteração.
    * O objeto desta classe é disponibilizado na session da página (request) para que o JSP correspondente possa disponibilizá-lo para edição.
    */
    public String editar() {
        PerguntaClienteVO obj = (PerguntaClienteVO)context().getExternalContext().getRequestMap().get("perguntaCliente");
        obj.setNovoObj(new Boolean(false));
        setPerguntaClienteVO(obj);
        setRespostaPergClienteVO(new RespostaPergClienteVO());
        setMensagemID("msg_dados_editar");
        return "editar";
    }

    /**
    * Rotina responsável por gravar no BD os dados editados de um novo objeto da classe <code>PerguntaCliente</code>.
    * Caso o objeto seja novo (ainda não gravado no BD) é acionado a operação <code>incluir()</code>. Caso contrário é acionado o <code>alterar()</code>.
    * Se houver alguma inconsistência o objeto não é gravado, sendo re-apresentado para o usuário juntamente com uma mensagem de erro.
    */
    public String gravar() {
        try {
            if (perguntaClienteVO.isNovoObj().booleanValue()) {
                getFacade().getPerguntaCliente().incluir(perguntaClienteVO);
            } else {
                getFacade().getPerguntaCliente().alterar(perguntaClienteVO);
            }
            setMensagemID("msg_dados_gravados");
            return "editar";
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            return "editar";
        }
    }

    /**
    * Rotina responsavel por executar as consultas disponiveis no JSP PerguntaClienteCons.jsp.
    * Define o tipo de consulta a ser executada, por meio de ComboBox denominado campoConsulta, disponivel neste mesmo JSP.
    * Como resultado, disponibiliza um List com os objetos selecionados na sessao da pagina.
    */
    public String consultar() {
        try {
            super.consultar();
            List objs = new ArrayList();
            if (getControleConsulta().getCampoConsulta().equals("codigo")) {
                if (getControleConsulta().getValorConsulta().equals("")) {
                    getControleConsulta().setValorConsulta("0");
                }
                int valorInt = Integer.parseInt(getControleConsulta().getValorConsulta());
                objs = getFacade().getPerguntaCliente().consultarPorCodigo(new Integer(valorInt), true, Uteis.NIVELMONTARDADOS_TODOS);
            }
            if (getControleConsulta().getCampoConsulta().equals("descricao")) {
                objs = getFacade().getPerguntaCliente().consultarPorDescricao(getControleConsulta().getValorConsulta(), true, Uteis.NIVELMONTARDADOS_TODOS);
            }
            if (getControleConsulta().getCampoConsulta().equals("tipoPergunta")) {
                objs = getFacade().getPerguntaCliente().consultarPorTipoPergunta(getControleConsulta().getValorConsulta(), true, Uteis.NIVELMONTARDADOS_TODOS);
            }
            objs = ControleConsulta.obterSubListPaginaApresentar(objs, controleConsulta);
            definirVisibilidadeLinksNavegacao(controleConsulta.getPaginaAtual(), controleConsulta.getNrTotalPaginas());
            setListaConsulta(objs);
            setMensagemID("msg_dados_consultados");
            return "consultar";
        } catch (Exception e) {
            setListaConsulta(new ArrayList());
            setMensagemDetalhada("msg_erro", e.getMessage());
            return "consultar";
        }
    }

    /**
     * Operação responsável por processar a exclusão um objeto da classe <code>PerguntaClienteVO</code>
     * Após a exclusão ela automaticamente aciona a rotina para uma nova inclusão.
     */
    public String excluir() {
        try {
            getFacade().getPerguntaCliente().excluir(perguntaClienteVO);
            setPerguntaClienteVO( new PerguntaClienteVO());

            setRespostaPergClienteVO(new RespostaPergClienteVO());
            setMensagemID("msg_dados_excluidos");
            return "editar";
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            return "editar";
        }
    }

    /* Método responsável por adicionar um novo objeto da classe <code>RespostaPergCliente</code>
     * para o objeto <code>perguntaClienteVO</code> da classe <code>PerguntaCliente</code>
     */ 
    public String adicionarRespostaPergCliente() throws Exception {
        try {
            if (!getPerguntaClienteVO().getCodigo().equals(new Integer(0))) {
                respostaPergClienteVO.setPerguntaCliente(getPerguntaClienteVO().getCodigo());
            }
            getPerguntaClienteVO().adicionarObjRespostaPergClienteVOs( getRespostaPergClienteVO());
            this.setRespostaPergClienteVO(new RespostaPergClienteVO());
            setMensagemID("msg_dados_adicionados");
            return "editar";
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            return "editar";
        }
    }

    /* Método responsável por disponibilizar dados de um objeto da classe <code>RespostaPergCliente</code>
     * para edição pelo usuário.
     */ 
    public String editarRespostaPergCliente() throws Exception {
        RespostaPergClienteVO obj = (RespostaPergClienteVO)context().getExternalContext().getRequestMap().get("respostaPergCliente");
        setRespostaPergClienteVO(obj);
        return "editar";
    }

    /* Método responsável por remover um novo objeto da classe <code>RespostaPergCliente</code>
     * do objeto <code>perguntaClienteVO</code> da classe <code>PerguntaCliente</code>
     */ 
    public String removerRespostaPergCliente() throws Exception {
        RespostaPergClienteVO obj = (RespostaPergClienteVO)context().getExternalContext().getRequestMap().get("respostaPergCliente");
        getPerguntaClienteVO().excluirObjRespostaPergClienteVOs(obj.getDescricaoRespota());
        setMensagemID("msg_dados_excluidos");
        return "editar";
    }

    public void irPaginaInicial() throws Exception{
        controleConsulta.setPaginaAtual(1);
        this.consultar();
    }

    public void irPaginaAnterior() throws Exception{
        controleConsulta.setPaginaAtual(controleConsulta.getPaginaAtual() - 1);
        this.consultar();
    }

    public void irPaginaPosterior() throws Exception{
        controleConsulta.setPaginaAtual(controleConsulta.getPaginaAtual() + 1);
        this.consultar();
    }

    public void irPaginaFinal() throws Exception{
        controleConsulta.setPaginaAtual(controleConsulta.getNrTotalPaginas());
        this.consultar();
    }

    /* Método responsável por inicializar List<SelectItem> de valores do 
     * ComboBox correspondente ao atributo <code>tipoPergunta</code>
     */ 
    public List getListaSelectItemTipoPerguntaPerguntaCliente() throws Exception {
        List objs = new ArrayList();
        objs.add(new SelectItem("", ""));
        Hashtable tipoPerguntas = (Hashtable)Dominios.getTipoPergunta();
        Enumeration keys = tipoPerguntas.keys();
        while (keys.hasMoreElements()) {
            String value = (String)keys.nextElement();
            String label = (String)tipoPerguntas.get(value);
            objs.add(new SelectItem( value, label));
        }
        SelectItemOrdemValor ordenador = new SelectItemOrdemValor();
        Collections.sort((List) objs, ordenador);
        return objs;
    }

    /**
    * Rotina responsável por preencher a combo de consulta da telas.
    */
    public List getTipoConsultaCombo() {
        List itens = new ArrayList();
        itens.add(new SelectItem("descricao", "Descrição"));
        itens.add(new SelectItem("codigo", "Código"));        
        itens.add(new SelectItem("tipoPergunta", "Tipo de Pergunta"));
        return itens;
    }

    /**
    * Rotina responsável por organizar a paginação entre as páginas resultantes de uma consulta.
    */
    public String inicializarConsultar() {
        setPaginaAtualDeTodas("0/0");
        setListaConsulta(new ArrayList());
        definirVisibilidadeLinksNavegacao(0, 0);
        setMensagemID("msg_entre_prmconsulta");
        return "consultar";
    }

    /**
    * Operação que inicializa as Interfaces Façades com os respectivos objetos de
    * persistência dos dados no banco de dados. 
    */
    protected boolean inicializarFacades() {
        try {
            super.inicializarFacades();

            return true;
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro_conectarBD", e.getMessage());
            return false;
        }
    }

    public RespostaPergClienteVO getRespostaPergClienteVO() {
        return respostaPergClienteVO;
    }
     
    public void setRespostaPergClienteVO(RespostaPergClienteVO respostaPergClienteVO) {
        this.respostaPergClienteVO = respostaPergClienteVO;
    }

    public PerguntaClienteVO getPerguntaClienteVO() {
        return perguntaClienteVO;
    }
     
    public void setPerguntaClienteVO(PerguntaClienteVO perguntaClienteVO) {
        this.perguntaClienteVO = perguntaClienteVO;
    }
}
package controle.basico;

import java.io.File;
import java.sql.Connection;

import acesso.webservice.DaoAuxiliar;
import br.com.pactosolucoes.comuns.util.FileUtilities;
import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import controle.arquitetura.SuperControle;
import controle.arquitetura.SuperControleCRM;
import controle.arquitetura.security.AlgoritmoCriptoEnum;
import controle.basico.clube.MensagemGenericaControle;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.ConfiguracaoSistemaVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.crm.AgendaVO;
import negocio.comuns.crm.ConfiguracaoSistemaCRMVO;
import negocio.comuns.crm.HistoricoContatoVO;
import negocio.comuns.crm.MalaDiretaVO;
import negocio.comuns.crm.optin.OptinVO;
import negocio.comuns.utilitarias.Criptografia;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.comuns.utilitarias.queue.MsgTO;
import negocio.facade.jdbc.utilitarias.Conexao;
import servicos.operacoes.midias.MidiaService;
import servicos.operacoes.midias.commons.MidiaEntidadeEnum;
import servicos.propriedades.PropsService;

/**
 * Created by Victor Vilela on 25/05/2022
 */
public class EmailOptinControle extends SuperControleCRM {

    private MalaDiretaVO malaDiretaVO;
    private AgendaVO agendaVO;
    private EmpresaVO empresaVO;
    private OptinVO optinVO;
    private HistoricoContatoVO historicoContatoVO;
    private ConfiguracaoSistemaCRMVO configuracaoSistemaCRMVO;
    private String modalMensagemGenerica;

    private ConfiguracaoSistemaVO configuracaoSistemaVO;

    public  EmailOptinControle(){
        setConfiguracaoSistemaVO((ConfiguracaoSistemaVO) JSFUtilities.getFromSession(JSFUtilities.CONFIGURACAO_SISTEMA));
    }
    public MalaDiretaVO getMalaDiretaVO() {
        if (malaDiretaVO == null) {
            malaDiretaVO = new MalaDiretaVO();
        }
        return malaDiretaVO;
    }

    public void setMalaDiretaVO(MalaDiretaVO malaDiretaVO) {
        this.malaDiretaVO = malaDiretaVO;
    }

    public AgendaVO getAgendaVO() {
        return agendaVO;
    }

    public void setAgendaVO(AgendaVO agendaVO) {
        this.agendaVO = agendaVO;
    }

    public ConfiguracaoSistemaCRMVO getConfiguracaoSistemaCRMVO() {
        return configuracaoSistemaCRMVO;
    }

    public HistoricoContatoVO getHistoricoContatoVO() {
        return historicoContatoVO;
    }

    public void setHistoricoContatoVO(HistoricoContatoVO historicoContatoVO) {
        this.historicoContatoVO = historicoContatoVO;
    }

    @Override
    public void setConfiguracaoSistemaCRMVO(ConfiguracaoSistemaCRMVO configuracaoSistemaCRMVO) {
        this.configuracaoSistemaCRMVO = configuracaoSistemaCRMVO;
    }

    public String getModalMensagemGenerica() {
        if(modalMensagemGenerica == null){
            modalMensagemGenerica = "";
        }
        return modalMensagemGenerica;
    }

    public void setModalMensagemGenerica(String modalMensagemGenerica) {
        this.modalMensagemGenerica = modalMensagemGenerica;
    }

    public void montarMsgGenerica(String titulo, String msg, boolean msgInformacao, String botaoSim, String botaoNao, String reRender) {
        MensagemGenericaControle control = (MensagemGenericaControle) getControlador(MensagemGenericaControle.class);
        control.setMensagemDetalhada("", "");
        control.setMensagemApresentar("");
        setModalMensagemGenerica("Richfaces.showModalPanel('mdlMensagemGenerica');adicionarPlaceHolderCRM();");

        if (msgInformacao) {
            control.init(titulo, msg, this, "Fechar", "adicionarPlaceHolderCRM()", reRender);
        } else {
            control.init(titulo, msg, this, botaoSim, "adicionarPlaceHolderCRM()", botaoNao, "adicionarPlaceHolderCRM()", reRender + ",mdlMensagemGenerica");
        }
    }

    public void enviarEmailOptin(ClienteVO clienteVO, String chave, Integer unidade) {
        try {
            if (!getEmpresaLogado().getNome().isEmpty()) {
                empresaVO = getEmpresaLogado();
                chave = getKey();
            } else {
                empresaVO = getFacade().getEmpresa().consultarPorChavePrimaria(unidade, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            }

            boolean configuracaoSesc = false;
            if (getConfiguracaoSistemaVO() != null) {
                configuracaoSesc = getConfiguracaoSistemaVO().getSesc();
            }
            getFacade().getOptin().enviarEmailOptin(empresaVO, clienteVO, chave, configuracaoSesc);

        } catch (Exception ex) {
            ex.getMessage();
        }
    }

    public void renviarEmailOptin(ClienteVO clienteVO, String chave, Integer unidade) {
        try {
            if (!getEmpresaLogado().getNome().isEmpty()) {
                empresaVO = getEmpresaLogado();
                chave = getKey();
            } else {
                empresaVO = getFacade().getEmpresa().consultarPorChavePrimaria(unidade, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            }

            getFacade().getOptin().renviarEmailOptin(empresaVO, clienteVO, chave);

        } catch (Exception ex) {
            ex.getMessage();
        }
    }

    public Integer obterValorChavePrimariaCodigo(String chave) throws Exception {
        Connection con = new DAO().obterConexaoEspecifica(chave);
        return Conexao.obterUltimoCodigoGeradoTabela(con, "cliente") + 1;
    }

    public ConfiguracaoSistemaVO getConfiguracaoSistemaVO() {
        return configuracaoSistemaVO;
    }

    public void setConfiguracaoSistemaVO(ConfiguracaoSistemaVO configuracaoSistemaVO) {
        this.configuracaoSistemaVO = configuracaoSistemaVO;
    }
}

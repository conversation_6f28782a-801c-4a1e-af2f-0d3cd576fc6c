package controle.basico;

import br.com.pactosolucoes.comuns.notificacao.RecursoSistema;
import br.com.pactosolucoes.enumeradores.BIEnum;
import br.com.pactosolucoes.enumeradores.ZonaChurnEnum;
import negocio.comuns.arquitetura.RiscoChurnDTO;
import br.com.pactosolucoes.estrutura.ItemExportacaoEnum;
import negocio.comuns.arquitetura.RiscoVO;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.crm.GrupoColaboradorVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.Uteis;
import relatorio.controle.basico.BIControle;
import relatorio.negocio.comuns.basico.ResultadoBITO;

import java.io.File;
import java.util.*;

/**
 *
 * <AUTHOR>
 */
public class RiscoControle extends BIControle {

    private List<RiscoVO> listaRiscoVOs;
    private String PERCENTUAL_RISCO = "percentualRisco";
    private String PERCENTUAL_CONTATO_RISCO = "percentualContatoRisco";
    private String PESO_6 = "riscoPeso6";
    private String PESO_7 = "riscoPeso7";
    private String PESO_8 = "riscoPeso8";
    private String tituloResumoClientes = "";
    private Boolean abrirTelaEdicaoCliente;
    private Double percentualRisco;
    private Double percentualContatoRisco;
    private Integer totalRisco6;
    private Integer totalRisco7;
    private Integer totalRisco8;
    private Integer totalRisco;
    private boolean mostrarGrupos = false;
    private Boolean marcarUsuario;
    private List<RiscoChurnDTO> listaRiscoChurn = new ArrayList<>();
    private String zonaListagem ="";

    public RiscoControle() throws Exception {
        novo();
    }
    /**
     * Rotina responsável por disponibilizar um novo objeto da classe <code>Cidade</code>
     * para edição pelo usuário da aplicação.
     */
    public void novo() throws Exception {
        setListaRiscoVOs(new ArrayList<RiscoVO>());
        setPercentualContatoRisco(0.0);
        setPercentualRisco(0.0);
        setTotalRisco(0);
        setTotalRisco6(0);
        setTotalRisco7(0);
        setTotalRisco8(0);
        setAbrirTelaEdicaoCliente(false);
    }

    public void consultarClienteEmRisco() {
        try {
            notificarRecursoEmpresa(RecursoSistema.GRUPO_RISCO_VER_MAIS);
            setListaRiscoVOs(new ArrayList<>());
            List<ColaboradorVO> listaColaboradorFiltroBI = getListaColaboradorFiltroBI(BIEnum.GRUPO_RISCO.name());
            setListaRiscoVOs(getFacade().getRisco().consultarClientePorVinculoColaborador(
                    getKey(), listaColaboradorFiltroBI,
                    getEmpresaFiltroBI().getCodigo(), getListaPesos(), true, false));
            setListaRiscoChurn(getFacade().getRisco().gerarBiRiscoChurn(listaColaboradorFiltroBI, getEmpresaFiltroBI().getCodigo()));
            setTituloResumoClientes("Todos os Clientes em Risco");
            limparMsg();
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void mostrarListaClientesSemContato() {
        try {
            notificarRecursoEmpresa(RecursoSistema.GRUPO_RISCO_SEM_CONTATO);
            setListaRiscoVOs(new ArrayList<>());
            setListaRiscoVOs(
                    getFacade().getRisco().consultarClienteEmRiscoSemContato(
                            getListaColaboradorFiltroBI(BIEnum.GRUPO_RISCO.name()),
                            getEmpresaFiltroBI().getCodigo(),
                            Uteis.obterDataAnterior(Calendario.hoje(), 15),
                            Calendario.getDataComHoraZerada(Calendario.hoje()),
                            true));
            setTituloResumoClientes("Clientes que não receberam contato nos últimos 15 dias");
            limparMsg();
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void mostrarListaClientesPeso8() {
        try {
            notificarRecursoEmpresa(RecursoSistema.GRUPO_RISCO_PESO_8);
            setListaRiscoVOs(new ArrayList<>());
            setListaRiscoVOs(getFacade().getRisco().consultarClientePorVinculoColaborador(getKey(), getListaColaboradorFiltroBI(BIEnum.GRUPO_RISCO.name()),
                    getEmpresaFiltroBI().getCodigo(), getListaPeso8(), true, false));
            setTituloResumoClientes("Clientes com peso 8");
            limparMsg();
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void mostrarListaClientesPeso7() {
        try {
            notificarRecursoEmpresa(RecursoSistema.GRUPO_RISCO_PESO_7);
            setListaRiscoVOs(new ArrayList<>());
            setListaRiscoVOs(getFacade().getRisco().consultarClientePorVinculoColaborador(getKey(), getListaColaboradorFiltroBI(BIEnum.GRUPO_RISCO.name()),
                    getEmpresaFiltroBI().getCodigo(), getListaPeso7(), true, false));
            setTituloResumoClientes("Clientes com peso 7");
            limparMsg();
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void mostrarListaClientesPeso6() {
        try {
            notificarRecursoEmpresa(RecursoSistema.GRUPO_RISCO_PESO_6);
            setListaRiscoVOs(new ArrayList<>());
            setListaRiscoVOs(getFacade().getRisco().consultarClientePorVinculoColaborador(getKey(), getListaColaboradorFiltroBI(BIEnum.GRUPO_RISCO.name()),
                    getEmpresaFiltroBI().getCodigo(), getListaPeso6(), true, false));
            setTituloResumoClientes("Clientes com peso 6");
            limparMsg();
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void contarClienteEmRisco(boolean atualizar) {
        try {
            if (getEmpresaFiltroBI() == null && getEmpresaFiltroBI().getCodigo() == 0) {
                throw new Exception("O campo empresa deve ser informado");
            }
            HashMap<String, Integer> qtdClientes = getFacade().getRisco().contarClientePorVinculoColaboradorClienteEmRiscoSemContato(getKey(), getListaColaboradorFiltroBI(BIEnum.GRUPO_RISCO.name()), getEmpresaFiltroBI().getCodigo(),
                    Uteis.obterDataAnterior(Calendario.hoje(), 15), Calendario.getDataComHoraZerada(Calendario.hoje()), true, atualizar);
            //contando o total de clientes em risco (todos os pesos)z
            int totalClientesRisco = qtdClientes.get("todos");
            //contando o total de clientes com risco 6,7 ou 8
            setTotalRisco6(qtdClientes.get("peso6"));
            setTotalRisco7(qtdClientes.get("peso7"));
            setTotalRisco8(qtdClientes.get("peso8"));

            int totalClientesRisco678 = getTotalRisco6() + getTotalRisco7() + getTotalRisco8();

            if (totalClientesRisco > 0 && totalClientesRisco678 > 0) {
                setPercentualRisco(Uteis.arredondarForcando2CasasDecimais(totalClientesRisco678 * 100.0 / totalClientesRisco));
            } else {
                setPercentualRisco(0.0);
            }

            //contando o total de clientes que não tiveram contato nestes ultimos 15 dias
            int totalClientesSemContatoUltimos15Dias = qtdClientes.get("qtdContatos");
            if (totalClientesSemContatoUltimos15Dias > 0 && totalClientesRisco678 > 0) {
                setPercentualContatoRisco(Uteis.arredondarForcando2CasasDecimais(totalClientesSemContatoUltimos15Dias * 100.0 / totalClientesRisco678));
            } else {
                setPercentualContatoRisco(0.0);
            }

            //contando o total de clientes com peso 6,7 ou 8

            setTotalRisco(totalClientesRisco678);
            setMensagemDetalhada("", "");
            setMensagem("");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public List<Integer> getListaPesos() {
        List<Integer> listaPesos = new ArrayList<Integer>();
        listaPesos.add(6);
        listaPesos.add(7);
        listaPesos.add(8);
        return listaPesos;
    }

    public List<Integer> getListaPeso6() {
        List<Integer> listaPesos = new ArrayList<Integer>();
        listaPesos.add(6);
        return listaPesos;
    }

    public List<Integer> getListaPeso7() {
        List<Integer> listaPesos = new ArrayList<Integer>();
        listaPesos.add(7);
        return listaPesos;
    }

    public List<Integer> getListaPeso8() {
        List<Integer> listaPesos = new ArrayList<Integer>();
        listaPesos.add(8);
        return listaPesos;
    }

    public void carregarBI(boolean atualizarCache, boolean atualizar) {

        try {
            List<ColaboradorVO> listaColaboradorFiltroBI = getListaColaboradorFiltroBI(BIEnum.GRUPO_RISCO.name());
            setListaRiscoChurn(getFacade().getRisco().gerarBiRiscoChurn(listaColaboradorFiltroBI, getEmpresaFiltroBI().getCodigo()));
            ResultadoBITO resultado = obterResultadoBIDiaCache(BIEnum.GRUPO_RISCO);
            if (validarResultadoBIDia(resultado) || atualizarCache) {
                contarClienteEmRisco(atualizar);
                resultado = new ResultadoBITO();
                resultado.getResultadosBI().put(PERCENTUAL_RISCO, getPercentualRisco());
                resultado.getResultadosBI().put(PERCENTUAL_CONTATO_RISCO, getPercentualContatoRisco());
                resultado.getResultadosBI().put(PESO_6, getTotalRisco6());
                resultado.getResultadosBI().put(PESO_7, getTotalRisco7());
                resultado.getResultadosBI().put(PESO_8, getTotalRisco8());

                adicionarResultadoBI(BIEnum.GRUPO_RISCO, resultado);
            } else {
                setPercentualRisco((Double) resultado.getResultadosBI().get(PERCENTUAL_RISCO));
                setPercentualContatoRisco((Double) resultado.getResultadosBI().get(PERCENTUAL_CONTATO_RISCO));
                setTotalRisco6((Integer) resultado.getResultadosBI().get(PESO_6));
                setTotalRisco7((Integer) resultado.getResultadosBI().get(PESO_7));
                setTotalRisco8((Integer) resultado.getResultadosBI().get(PESO_8));
            }
        } catch (Exception ex) {

        }
        setMensagem("");
        setMensagemID("");
        setMensagemDetalhada("", "");
    }

    @Override
    public void carregar() {
        try {
            carregarBI(false, false);
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }
    public void atualizar() {
        atualizar(true);
    }
    public void atualizar(boolean atualizar) {
        try {
            gravarHistoricoAcessoBI(BIEnum.GRUPO_RISCO);
            carregarBI(true, atualizar);
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }
    public void filtrarRiscoPorEmpresa() {
        try {
            if (getEmpresaFiltroBI() != null && getEmpresaFiltroBI().getCodigo() != 0) {
                contarClienteEmRisco(false);
            } else {
                throw new Exception("O campo empresa deve ser informado");
            }
            setMensagem("");
            setMensagemID("");
            setMensagemDetalhada("");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void irParaTelaCliente() {
        RiscoVO obj = (RiscoVO) context().getExternalContext().getRequestMap().get("risco");
        try {
            if (obj == null) {
                throw new Exception("Cliente Não Encontrado.");
            } else {
                irParaTelaCliente(getFacade().getCliente().consultarPorChavePrimaria(obj.getClienteVO().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            }
            setAbrirTelaEdicaoCliente(true);
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void selecionarGrupoColaboradorParticipante() throws Exception {
        try {
            GrupoColaboradorVO obj = (GrupoColaboradorVO) context().getExternalContext().getRequestMap().get("grupoColaborador");
            obj.setAbrirSimpleTooglePanelPassivo(!obj.getAbrirSimpleTooglePanelPassivo());
        } catch (Exception e) {
            setMensagemDetalhada("", e.getMessage());
        }
    }
    public void imprimirRelatorioGeral() {
        notificarRecursoEmpresa(RecursoSistema.GRUPO_RISCO_IMPRIMIR);
        consultarClienteEmRisco();
        imprimirRelatorio();
    }

    public void imprimirRelatorio() {
        try {
            limparMsg();
            setMsgAlert("");
            if (!listaRiscoVOs.isEmpty()) {
                validarPermissaoEIncluirLogExportacao(ItemExportacaoEnum.BI_RISCO, listaRiscoVOs.size(), getDescricaoFiltros(), "PDF", "", "");
                Map<Integer, ArrayList<RiscoVO>> mapaRiscos = new HashMap<Integer, ArrayList<RiscoVO>>();
                for (RiscoVO risco : listaRiscoVOs) {
                    if (mapaRiscos.get(risco.getPeso()) != null) {
                        mapaRiscos.get(risco.getPeso()).add(risco);
                    } else {
                        ArrayList<RiscoVO> riscoVOs = new ArrayList<RiscoVO>();
                        riscoVOs.add(risco);
                        mapaRiscos.put(risco.getPeso(), riscoVOs);
                    }
                }

                List<Integer> pesosOrdenados = new ArrayList<Integer>();
                for (Integer peso : mapaRiscos.keySet()) {
                    pesosOrdenados.add(peso);
                }
               
                Collections.sort(pesosOrdenados);
                listaRiscoVOs = new ArrayList<RiscoVO>();
                for (Integer peso : pesosOrdenados) {
                    ArrayList<RiscoVO> riscosDoPeso = mapaRiscos.get(peso);
                    
                    Ordenacao.ordenarLista(riscosDoPeso, "nomeCliente");//nomeCliente
                    
                    listaRiscoVOs.addAll(riscosDoPeso);
//                    if (params.length > 1 && params[1].equals("DESC")) {
//                        Collections.reverse(listaRiscoVOs);
//                    }
                }
            }
            
            String nomeRelatorio = "Risco";
            String titulo = "Risco";
            String design = getDesignIReportRelatorio();
            setRelatorio("sim");
//            consultarClienteEmRisco();
            setListaRelatorio(getListaRiscoVOs());
            apresentarRelatorioObjetos(nomeRelatorio, titulo, getEmpresaLogado().getNome(), "", "", getTipoRelatorio(),
                    "/Risco/registros", design, getUsuarioLogado().getNome(), getDescricaoFiltros(), getListaRelatorio());
            setMsgAlert("abrirPopupPDFImpressao('relatorio/"+getNomeArquivoRelatorioGeradoAgora()+"','', 780, 595);");
        } catch (Exception e) {
            montarErro(e);
        }
    }

    private String getDesignIReportRelatorio() {
        return ("relatorio" + File.separator + "designRelatorio" + File.separator + "outros" + File.separator + "Risco.jrxml");
    }

    private String getDescricaoFiltros() {
        int qtde = 0;
        StringBuilder ret = new StringBuilder();
        for (ColaboradorVO aux : getListaColaboradorFiltroBI(BIEnum.GRUPO_RISCO.name())) {
            if (aux.getColaboradorEscolhidoPendencia()) {
                if (qtde++ > 0) {
                    ret.append(", ");
                }
                ret.append(aux.getPessoa().getNome());
            }
        }
        return ret.toString();
    }

    public void toggleMostrarGrupos() {
        setMostrarGrupos(!isMostrarGrupos());
    }

    public boolean isMostrarGrupos() {
        return mostrarGrupos;
    }

    public void setMostrarGrupos(boolean mostrarGrupos) {
        this.mostrarGrupos = mostrarGrupos;
    }

    public Boolean getAbrirTelaEdicaoCliente() {
        return abrirTelaEdicaoCliente;
    }

    public void setAbrirTelaEdicaoCliente(Boolean abrirTelaEdicaoCliente) {
        this.abrirTelaEdicaoCliente = abrirTelaEdicaoCliente;
    }

    public List<RiscoVO> getListaRiscoVOs() {
        return listaRiscoVOs;
    }

    public void setListaRiscoVOs(List<RiscoVO> listaRiscoVOs) {
        this.listaRiscoVOs = listaRiscoVOs;
    }

    @Override
    protected void limparRecursosMemoria() {
        super.limparRecursosMemoria();
        abrirTelaEdicaoCliente = null;
    }

    public String inicializarRiscoControle() {
        return "";
    }

    public Double getPercentualRisco() {
        if (percentualRisco == null) {
            percentualRisco = 0.0;
        }
        return percentualRisco;
    }

    public void setPercentualRisco(Double percentualRisco) {
        this.percentualRisco = percentualRisco;
    }

    public Double getPercentualContatoRisco() {
        if (percentualContatoRisco == null) {
            percentualContatoRisco = 0.0;
        }
        return percentualContatoRisco;
    }

    public void setPercentualContatoRisco(Double percentualContatoRisco) {
        this.percentualContatoRisco = percentualContatoRisco;
    }

    public Integer getTotalRisco6() {
        if (totalRisco6 == null) {
            totalRisco6 = 0;
        }
        return totalRisco6;
    }

    public void setTotalRisco6(Integer totalRisco6) {
        this.totalRisco6 = totalRisco6;
    }

    public Integer getTotalRisco7() {
        if (totalRisco7 == null) {
            totalRisco7 = 0;
        }
        return totalRisco7;
    }

    public void setTotalRisco7(Integer totalRisco7) {
        this.totalRisco7 = totalRisco7;
    }

    public Integer getTotalRisco8() {
        if (totalRisco8 == null) {
            totalRisco8 = 0;
        }
        return totalRisco8;
    }

    public void setTotalRisco8(Integer totalRisco8) {
        this.totalRisco8 = totalRisco8;
    }

    public Integer getTotalRisco() {
        return totalRisco;
    }

    public void setTotalRisco(Integer totalRisco) {
        this.totalRisco = totalRisco;
    }

    public String getTituloResumoClientes() {
        return tituloResumoClientes;
    }

    public void setTituloResumoClientes(String tituloResumoClientes) {
        this.tituloResumoClientes = tituloResumoClientes;
    }

    public List<RiscoChurnDTO> getListaRiscoChurn() {
        return listaRiscoChurn;
    }

    public void setListaRiscoChurn(List<RiscoChurnDTO> listaRiscoChurn) {
        this.listaRiscoChurn = listaRiscoChurn;
    }

    public String getZonaListagem() {
        return zonaListagem;
    }

    public void setZonaListagem(String zonaListagem) {
        this.zonaListagem = zonaListagem;
    }

    public void mostrarListaClientesRiscoChurn() {
        try {
            ZonaChurnEnum zonaChurnEnum = ZonaChurnEnum.valueOf(zonaListagem);
            if(zonaChurnEnum == null){
                throw new Exception("Zona de risco não encontrada");
            }
            notificarRecursoEmpresa(RecursoSistema.GRUPO_RISCO_SEM_CONTATO);
            setListaRiscoVOs(new ArrayList<>());
            setListaRiscoVOs(
                    getFacade().getRisco().consultarListaChurn(
                            getListaColaboradorFiltroBI(BIEnum.GRUPO_RISCO.name()),
                            getEmpresaFiltroBI().getCodigo(),
                            zonaChurnEnum));
            setTituloResumoClientes("Lista de cliente na " + zonaChurnEnum.getDescricao());
            limparMsg();
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }
}

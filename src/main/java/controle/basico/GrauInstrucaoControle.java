package controle.basico;

import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.estrutura.exportador.controle.ExportadorListaControle;
import controle.arquitetura.security.LoginControle;

import controle.basico.clube.MensagemGenericaControle;
import negocio.comuns.utilitarias.*;
import negocio.comuns.basico.*;
import javax.faces.model.SelectItem;
import java.util.List;
import java.util.ArrayList;
import controle.arquitetura.SuperControle;
import javax.faces.event.ActionEvent;

public class GrauInstrucaoControle extends SuperControle {

    private GrauInstrucaoVO grauInstrucaoVO;
    private String msgAlert;

    public GrauInstrucaoControle() throws Exception {
        obterUsuarioLogado();
        inicializarFacades();
        setControleConsulta(new ControleConsulta());
        setMensagemID("");
    }

    public void inicializarGrauInstrucaoControle() throws Exception {
        obterUsuarioLogado();
        inicializarFacades();
        novo();
        setControleConsulta(new ControleConsulta());
        setMensagemID("msg_entre_prmconsulta");
    }

    public String novo() {
        setGrauInstrucaoVO(new GrauInstrucaoVO());
        limparMsg();
        return "editar";
    }

    public String editar() {
        Integer codigoConsulta = Integer.parseInt(request().getParameter("chavePrimaria"));
        try {
            GrauInstrucaoVO obj = getFacade().getGrauInstrucao().consultarPorChavePrimaria(codigoConsulta, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            obj.setNovoObj(false);
            obj.registrarObjetoVOAntesDaAlteracao();
            setGrauInstrucaoVO(new GrauInstrucaoVO());
            setGrauInstrucaoVO(obj);
        } catch (Exception e) {
            setErro(true);
            setSucesso(false);
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
        return "editar";
    }

    /**
     * <AUTHOR>
     * 24/03/2011
     * @return
     */
    public String gravar() {
        return this.gravar(false);
    }

    /**
     * <AUTHOR>
     * 24/03/2011
     * @return
     */
    public String gravarCE() {
        return this.gravar(true);
    }

    public String gravar(boolean centralEventos) {
        try {
            if (centralEventos) {
                this.verificarAutorizacao();
                if (grauInstrucaoVO.isNovoObj().booleanValue()) {
                    getFacade().getGrauInstrucao().incluir(grauInstrucaoVO, true);
                    incluirLogInclusao();
                } else {
                    getFacade().getGrauInstrucao().alterar(grauInstrucaoVO, true);
                    incluirLogAlteracao();
                }
            } else {
                if (grauInstrucaoVO.isNovoObj().booleanValue()) {
                    getFacade().getGrauInstrucao().incluir(grauInstrucaoVO);
                    incluirLogInclusao();
                } else {
                    getFacade().getGrauInstrucao().alterar(grauInstrucaoVO);
                    incluirLogAlteracao();
                }
            }
            setMensagemID("msg_dados_gravados");
            setSucesso(true);
            setErro(false);
            return "editar";
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
            return "editar";
        }
    }

    public void incluirLogInclusao() throws Exception {
        //LOG - INICIO
        try {
            grauInstrucaoVO.setObjetoVOAntesAlteracao(new GrauInstrucaoVO());
            grauInstrucaoVO.setNovoObj(true);
            registrarLogObjetoVO(grauInstrucaoVO, grauInstrucaoVO.getCodigo(), "GRAU INSTRUCAO", 0);
        } catch (Exception e) {
            registrarLogErroObjetoVO("GRAU INSTRUCAO", grauInstrucaoVO.getCodigo(), "ERRO AO GERAR LOG DE INCLUSÃO DE GRAU INSTRUCAO", this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
        grauInstrucaoVO.setNovoObj(false);
        grauInstrucaoVO.registrarObjetoVOAntesDaAlteracao();
    }
    
    public void incluirLogExclusao() throws Exception {
        //LOG - INICIO
        try {
            grauInstrucaoVO.setObjetoVOAntesAlteracao(new GrauInstrucaoVO());
            grauInstrucaoVO.setNovoObj(true);
            registrarLogExclusaoTodosDadosObjetoVO(grauInstrucaoVO, grauInstrucaoVO.getCodigo(), "GRAU INSTRUCAO", 0);
        } catch (Exception e) {
            registrarLogErroObjetoVO("GRAU INSTRUCAO", grauInstrucaoVO.getCodigo(), "ERRO AO GERAR LOG DE EXCLUSÃO DE GRAU INSTRUCAO", this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
    }

    public void incluirLogAlteracao() throws Exception {
        //LOG - INICIO
        try {
            registrarLogObjetoVO(grauInstrucaoVO, grauInstrucaoVO.getCodigo(), "GRAU INSTRUCAO", 0);
        } catch (Exception e) {
            registrarLogErroObjetoVO("GRAU INSTRUCAO", grauInstrucaoVO.getCodigo(), "ERRO AO GERAR LOG DE ALTERAÇÃO DE GRAU INSTRUCAO", this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
        grauInstrucaoVO.registrarObjetoVOAntesDaAlteracao();
        //LOG - FIM
    }

    public String consultar() {
        try {
            super.consultar();
            List objs = new ArrayList();
            
            //limpar espaços em branco antes de realizar a consulta
            getControleConsulta().setValorConsulta(getControleConsulta().getValorConsulta().trim());
            
            if (getControleConsulta().getCampoConsulta().equals("codigo")) {
                if (getControleConsulta().getValorConsulta().equals("")) {
                    objs = getFacade().getGrauInstrucao().consultarPorCodigo(new Integer(0), true, Uteis.NIVELMONTARDADOS_TODOS);
                } else {
	                int valorInt = Integer.parseInt(getControleConsulta().getValorConsulta());
	                GrauInstrucaoVO grauInstrucao = getFacade().getGrauInstrucao().consultarPorCodigoExato(new Integer(valorInt), true, Uteis.NIVELMONTARDADOS_TODOS);
	                if(grauInstrucao != null) {
		                objs.add(grauInstrucao);
	                }
                }
            }
            if (getControleConsulta().getCampoConsulta().equals("descricao")) {
                objs = getFacade().getGrauInstrucao().consultarPorDescricao(getControleConsulta().getValorConsulta(), true, Uteis.NIVELMONTARDADOS_TODOS);
            }
            objs = ControleConsulta.obterSubListPaginaApresentar(objs, controleConsulta);
            definirVisibilidadeLinksNavegacao(controleConsulta.getPaginaAtual(), controleConsulta.getNrTotalPaginas());
            setListaConsulta(objs);
            limparMsg();
            return "consultar";
        } catch (Exception e) {
            setListaConsulta(new ArrayList());
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
            return "consultar";
        }
    }

    public String excluir() {
        return this.excluir(false);
    }

    public String excluirCE() {
        return this.excluir(true);
    }

    public String excluir(boolean centralEventos) {
        try {
            if (centralEventos) {
                this.verificarAutorizacao();
                getFacade().getGrauInstrucao().excluir(grauInstrucaoVO, true);
            } else {
                getFacade().getGrauInstrucao().excluir(grauInstrucaoVO);
                incluirLogExclusao();
            }
            setGrauInstrucaoVO(new GrauInstrucaoVO());
            setMensagemID("msg_dados_excluidos");
            setSucesso(true);
            setErro(false);
            return "consultar";
        } catch (Exception e) {
            if (e.getMessage().contains("ERRO: atualização ou exclusão em tabela \"grauinstrucao\" viola restrição de chave estrangeira") || e.getMessage().contains("ERROR: update or delete on table \"grauinstrucao\" violates foreign key")){
                setMensagemDetalhada("Este Grau de Instrução não pode ser excluído, pois está sendo utilizado!");
            }else {
                setMensagemDetalhada("msg_erro", e.getMessage());
            }
            setSucesso(false);
            setErro(true);
            return "editar";
        }
    }

    public void irPaginaInicial() throws Exception {
        controleConsulta.setPaginaAtual(1);
        this.consultar();
    }

    public void irPaginaAnterior() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getPaginaAtual() - 1);
        this.consultar();
    }

    public void irPaginaPosterior() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getPaginaAtual() + 1);
        this.consultar();
    }

    public void irPaginaFinal() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getNrTotalPaginas());
        this.consultar();
    }

    public List<SelectItem> getTipoConsultaCombo() {
        List<SelectItem> itens = new ArrayList();
        itens.add(new SelectItem("codigo", "Código"));
        itens.add(new SelectItem("descricao", "Descrição"));
        return itens;
    }

    public String inicializarConsultar() {
        setPaginaAtualDeTodas("0/0");
        setListaConsulta(new ArrayList());
        definirVisibilidadeLinksNavegacao(0, 0);
        setMensagemID("");
        setSucesso(false);
        setErro(false);
        return "consultar";
    }

    protected boolean inicializarFacades() {
        try {
            super.inicializarFacades();

            return true;
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro_conectarBD", e.getMessage());
            return false;
        }
    }

    public GrauInstrucaoVO getGrauInstrucaoVO() {
        return grauInstrucaoVO;
    }

    public void setGrauInstrucaoVO(GrauInstrucaoVO grauInstrucaoVO) {
        this.grauInstrucaoVO = grauInstrucaoVO;
    }

       /**
     * Consulta de logs de cidade
     */
    @SuppressWarnings("unchecked")
    public void realizarConsultaLogObjetoSelecionado() {
        LoginControle loginControle = (LoginControle) context().getExternalContext().getSessionMap().get("LoginControle");
        loginControle.setNomeClasse("Grau de Instrução");
        loginControle.consultarLogObjetoSelecionado("GRAU INSTRUCAO", grauInstrucaoVO.getCodigo(), null);
    }
    
     @SuppressWarnings("unchecked")
    public void realizarConsultaLogObjetoGeral() {
        grauInstrucaoVO = new GrauInstrucaoVO();
        realizarConsultaLogObjetoSelecionado();
    }
     public void exportar(ActionEvent evt) throws Exception {
        ExportadorListaControle exportadorListaControle = (ExportadorListaControle) JSFUtilities.getFromRequest(ExportadorListaControle.class.getSimpleName());
        String paramsTableFiltrada = context().getExternalContext().getRequestParameterMap().get("paramsTableFiltrada");

        String[] split = paramsTableFiltrada.split(",");
        String campoOrdenacao = split[0].replace("[", "");
        String ordem = split[1];
        String filtro = split[2].replace("''", "");
        filtro = filtro.replace("]", "");
        List listaParaImpressao = getFacade().getGrauInstrucao().consultarParaImpressao(filtro, ordem, campoOrdenacao, getEmpresaLogado() == null ? 0 : getEmpresaLogado().getCodigo());
        exportadorListaControle.exportar(evt, listaParaImpressao, filtro, null);

    }

    public void setMsgAlert(String msgAlert) {
        this.msgAlert = msgAlert;
    }

    public String getMsgAlert() {
        if (msgAlert == null) {
            return "";
        }
        return msgAlert;
    }


    public void confirmarExcluir(){
        MensagemGenericaControle control = (MensagemGenericaControle) getControlador(MensagemGenericaControle.class);
        control.setMensagemDetalhada("", "");
        setMsgAlert("Richfaces.showModalPanel('mdlMensagemGenerica');");
        control.init("Exclusão de Grau de Instrução",
                "Deseja excluir o Grau de Instrução?",
                this, "excluir", "", "", "", "grupoBtnExcluir");
    }

}

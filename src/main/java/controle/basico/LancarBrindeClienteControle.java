/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package controle.basico;

import br.com.pactosolucoes.comuns.notificacao.RecursoSistema;
import controle.arquitetura.security.LoginControle;
import negocio.comuns.basico.BrindeVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.HistoricoPontosVO;
import negocio.comuns.basico.enumerador.TipoItemCampanhaEnum;
import negocio.comuns.utilitarias.Calendario;
import relatorio.controle.arquitetura.SuperControleRelatorio;

import javax.faces.model.SelectItem;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
public class LancarBrindeClienteControle extends SuperControleRelatorio {

    private ClienteVO clienteSelecionado;
    private List<SelectItem> listaMostrarTodosBrindes;
    private String mostrarTodos;
    private BrindeVO brindeSelecionado;
    private Integer pontosClienteSelecioando;
    private HistoricoPontosVO dadosBrindeLancar;
    private String msgModal;
    private String onCompleteGravar;
    private Integer pontosDepoisDeGravar;
    private Boolean mostrarPontosDepoisDeGravar = true;
    private ClienteVO clienteSelecionadoAjuste;
    private HistoricoPontosVO historicoPontosAjuste;
    private List<SelectItem> listaAjusteTipoPonto = new ArrayList<>();
    private String tipoPontoAjuste;
    private String nomeArquivoComprovanteOperacao;

    public LancarBrindeClienteControle() throws Exception {
        listaMostrarTodosBrindes = new ArrayList<>();
        dadosBrindeLancar = new HistoricoPontosVO();
        obterUsuarioLogado();
        inicializarFacades();
        montarListaEmpresas();
        setMensagemID("");
        montarListaMostrarTodosBrindes();
        setMensagemDetalhada("");
        setMensagem("");
        montarListaAjusteTipoPonto();
    }

    public void montarListaMostrarTodosBrindes(){
        if (this.listaMostrarTodosBrindes.isEmpty()) {
            this.listaMostrarTodosBrindes.add(new SelectItem("nao", "Brindes permitidos"));
            this.listaMostrarTodosBrindes.add(new SelectItem("sim", "Mostrar Todos"));
        }
    }

    public void montarListaAjusteTipoPonto(){
        if (this.listaAjusteTipoPonto.isEmpty()) {
            this.listaAjusteTipoPonto.add(new SelectItem("entrada", "Adicionar Ponto"));
            this.listaAjusteTipoPonto.add(new SelectItem("saida", "Retirar Ponto"));
        }
    }

    public String selecionarBrindeCliente(){
        try {
            Integer codigoConsulta = Integer.parseInt(request().getParameter("chavePrimaria"));

            setBrindeSelecionado(getFacade().getBrinde().consultarPorChavePrimaria(codigoConsulta));

            if (getBrindeSelecionado().getPontos() > getPontosClienteSelecioando()) {
                throw new Exception("O cliente não possui pontos suficiente para esse brinde");
            }

            setPontosDepoisDeGravar(getPontosClienteSelecioando() - getBrindeSelecionado().getPontos());

            setSucesso(true);
            setErro(false);
            setMostrarPontosDepoisDeGravar(true);
            setMensagemDetalhada("");
            return "lancar";
        } catch (Exception e) {

            setSucesso(false);
            setErro(true);
            setMensagemDetalhada(e);
            return "";
        }
    }

    public void limparDados(){
        setMensagemDetalhada("");
        setBrindeSelecionado(null);
        setPontosClienteSelecioando(null);
        setPontosDepoisDeGravar(null);
        setMostrarTodos(null);
        setSucesso(true);
        setErro(false);
        dadosBrindeLancar = new HistoricoPontosVO();
        setMsgModal(null);
        setOnCompleteGravar(null);
        setMensagem("");
        limparDadosAjuste();
    }

    public void validarDadosParaGravar(){
        try {

            dadosBrindeLancar.setBrinde(getBrindeSelecionado());
            dadosBrindeLancar.setCliente(getClienteSelecionado());
            dadosBrindeLancar.setTipoPonto(TipoItemCampanhaEnum.RESGATE_BRINDE);
            dadosBrindeLancar.setDataaula(getHoje());
            dadosBrindeLancar.setDescricao("Resgate do Brinde " + getBrindeSelecionado().getNome());
            dadosBrindeLancar.setDataConfirmacao(getHoje());
            dadosBrindeLancar.setEntrada(false);
            dadosBrindeLancar.setPontos(getBrindeSelecionado().getPontosNegativo());

            setOnCompleteGravar("Richfaces.showModalPanel('mdlAvisoLancamentoBrinde');");

            setSucesso(true);
            setErro(false);
            setMensagemDetalhada("");
        } catch (Exception e) {
            setSucesso(false);
            setErro(true);
            setMensagemDetalhada(e);
        }
    }

    public void gravar(){
        try {
            permissaoFuncionalidade(getUsuarioLogado(), "LancamentoBrindeAluno", "5.68 - Permitir lançar brinde para aluno");

            getFacade().getHistoricoPontos().incluir(getDadosBrindeLancar());

            incluirLogInclusao();
            setSucesso(true);
            setErro(false);
            setMensagemDetalhada("");
            setMensagem("Brinde Lançado com Sucesso!");
            setMostrarPontosDepoisDeGravar(false);

            setPontosClienteSelecioando(getFacade().getHistoricoPontos().obterPontosTotalPorCliente(getClienteSelecionado().getCodigo()));

            setOnCompleteGravar("Richfaces.hideModalPanel('mdlAvisoLancamentoBrinde');fireElementFromParent('form:btnAtualizaCliente');");
            notificarRecursoEmpresa(RecursoSistema.BRINDE_RESGATADO_SUCESSO);
        } catch (Exception e) {
            setSucesso(false);
            setErro(true);
            setMensagemDetalhada(e);
            setOnCompleteGravar("Richfaces.hideModalPanel('mdlAvisoLancamentoBrinde');");
        }
    }

    public String lancarNovoBrinde() {
        setMostrarTodos(null);
        setSucesso(true);
        setErro(false);
        dadosBrindeLancar = new HistoricoPontosVO();
        setMsgModal(null);
        setOnCompleteGravar(null);
        setMensagem("");
        setMensagemDetalhada("");

        return "consultar";
    }

    public void realizarConsultaLogObjetoGeral(){
        realizarConsultaLogObjetoSelecionado();
    }

    public void realizarConsultaLogObjetoSelecionado() {
        LoginControle loginControle = (LoginControle) context().getExternalContext().getSessionMap().get("LoginControle");
        String nomeClasse = dadosBrindeLancar.getClass().getSimpleName();
        nomeClasse = nomeClasse.substring(0, nomeClasse.length() - 2);
        loginControle.setNomeClasse(loginControle.getNomeEntidadeCamposLog("prt_" + nomeClasse + "_tituloForm"));
        loginControle.consultarLogObjetoSelecionado(nomeClasse.toUpperCase(), dadosBrindeLancar.getCodigo(), getClienteSelecionado().getCodigo());
    }

    public void incluirLogInclusao() throws Exception{
        try {
            dadosBrindeLancar.setObjetoVOAntesAlteracao(new HistoricoPontosVO());
            dadosBrindeLancar.setNovoObj(true);
            registrarLogObjetoVO(dadosBrindeLancar, dadosBrindeLancar.getCodigo(), "HistoricoPontos", getClienteSelecionado().getCodigo());
        } catch (Exception e) {
            registrarLogErroObjetoVO("Historico Pontos", dadosBrindeLancar.getCodigo(), "ERRO AO GERAR LOG DE INCLUSÃO DO HISTORICO PONTOS",this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
    }

    public void lancarAjustePontuacao(){
        try {

            permissaoFuncionalidade(getUsuarioLogado(), "AjusteManualPontos", "5.69 - Permitir ajuste manual de pontos");

            if (historicoPontosAjuste.getDescricao().isEmpty()) {
                throw new Exception("Descrição e obrigatório");
            }

            if (getTipoPontoAjuste().isEmpty()) {
                throw new Exception("Tipo de Operação obrigatório");
            }

            if (getHistoricoPontosAjuste().getPontos().intValue() < 0) {
                throw new Exception("Pontução deve ser informada");
            }

            Integer valor = 0;
            if (getTipoPontoAjuste().equals("entrada")) {
                getHistoricoPontosAjuste().setEntrada(true);
            }else if(getTipoPontoAjuste().equals("saida")){
                getHistoricoPontosAjuste().setEntrada(false);
                valor = (getHistoricoPontosAjuste().getPontos() * -1);
                getHistoricoPontosAjuste().setPontos(valor);
            }

            getHistoricoPontosAjuste().setTipoPonto(TipoItemCampanhaEnum.AJUSTE_PONTO);
            getHistoricoPontosAjuste().setDataConfirmacao(Calendario.hoje());
            getHistoricoPontosAjuste().setDataaula(Calendario.hoje());
            getHistoricoPontosAjuste().setCliente(getClienteSelecionadoAjuste());

            getFacade().getHistoricoPontos().incluir(getHistoricoPontosAjuste());

            montarSucessoGrowl("Ajuste de pontuação lançado com Sucesso!");

            historicoPontosAjuste = new HistoricoPontosVO();
            setTipoPontoAjuste("");
        } catch (Exception e) {
            montarErro(e);
        }
    }

    public void limparDadosAjuste(){
        historicoPontosAjuste = new HistoricoPontosVO();
        setTipoPontoAjuste("");
    }

    public void imprimirComprovanteOperacao() {
        try {
            if (getDadosBrindeLancar().getCodigo() != 0) {
                setNomeArquivoComprovanteOperacao(new SuperControleRelatorio().imprimirComprovanteResgateBrinde(getDadosBrindeLancar(), getEmpresaLogado()));
            } else {
                throw new Exception("Não foi possível imprimir o comprovante de resgate de brinde.");
            }
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public ClienteVO getClienteSelecionado() {
        return clienteSelecionado;
    }

    public void setClienteSelecionado(ClienteVO clienteSelecionado) {
        this.clienteSelecionado = clienteSelecionado;
    }

    public List<SelectItem> getListaMostrarTodosBrindes() {
        return listaMostrarTodosBrindes;
    }

    public void setListaMostrarTodosBrindes(List<SelectItem> listaMostrarTodosBrindes) {
        this.listaMostrarTodosBrindes = listaMostrarTodosBrindes;
    }

    public String getMostrarTodos() {
        if (mostrarTodos == null) {
            mostrarTodos = "";
        }
        return mostrarTodos;
    }

    public void setMostrarTodos(String mostrarTodos) {
        this.mostrarTodos = mostrarTodos;
    }

    public BrindeVO getBrindeSelecionado() {
        if (brindeSelecionado == null) {
            brindeSelecionado = new BrindeVO();
        }

        return brindeSelecionado;
    }

    public void setBrindeSelecionado(BrindeVO brindeSelecionado) {
        this.brindeSelecionado = brindeSelecionado;
    }

    public Integer getPontosClienteSelecioando() {
        if (pontosClienteSelecioando == null) {
            pontosClienteSelecioando = 0;
        }
        return pontosClienteSelecioando;
    }

    public void setPontosClienteSelecioando(Integer pontosClienteSelecioando) {
        this.pontosClienteSelecioando = pontosClienteSelecioando;
    }

    public HistoricoPontosVO getDadosBrindeLancar() {
        return dadosBrindeLancar;
    }

    public void setDadosBrindeLancar(HistoricoPontosVO dadosBrindeLancar) {
        this.dadosBrindeLancar = dadosBrindeLancar;
    }

    public String getMsgModal() {
        if (msgModal == null) {
            msgModal = "";
        }
        return msgModal;
    }

    public void setMsgModal(String msgModal) {
        this.msgModal = msgModal;
    }

    public String getOnCompleteGravar() {
        if (onCompleteGravar == null) {
            onCompleteGravar = "";
        }
        return onCompleteGravar;
    }

    public void setOnCompleteGravar(String onCompleteGravar) {
        this.onCompleteGravar = onCompleteGravar;
    }

    public Integer getPontosDepoisDeGravar() {
        if (pontosDepoisDeGravar == null) {
            pontosDepoisDeGravar = 0;
        }
        return pontosDepoisDeGravar;
    }

    public void setPontosDepoisDeGravar(Integer pontosDepoisDeGravar) {
        this.pontosDepoisDeGravar = pontosDepoisDeGravar;
    }

    public Boolean getMostrarPontosDepoisDeGravar() {
        return mostrarPontosDepoisDeGravar;
    }

    public void setMostrarPontosDepoisDeGravar(Boolean mostrarPontosDepoisDeGravar) {
        this.mostrarPontosDepoisDeGravar = mostrarPontosDepoisDeGravar;
    }

    public ClienteVO getClienteSelecionadoAjuste() {
        if (clienteSelecionadoAjuste == null) {
            clienteSelecionadoAjuste = new ClienteVO();
        }
        return clienteSelecionadoAjuste;
    }

    public void setClienteSelecionadoAjuste(ClienteVO clienteSelecionadoAjuste) {
        this.clienteSelecionadoAjuste = clienteSelecionadoAjuste;
    }

    public HistoricoPontosVO getHistoricoPontosAjuste() {
        if (historicoPontosAjuste == null) {
            historicoPontosAjuste = new HistoricoPontosVO();
        }
        return historicoPontosAjuste;
    }

    public void setHistoricoPontosAjuste(HistoricoPontosVO historicoPontosAjuste) {
        this.historicoPontosAjuste = historicoPontosAjuste;
    }

    public List<SelectItem> getListaAjusteTipoPonto() {
        return listaAjusteTipoPonto;
    }

    public void setListaAjusteTipoPonto(List<SelectItem> listaAjusteTipoPonto) {
        this.listaAjusteTipoPonto = listaAjusteTipoPonto;
    }

    public String getTipoPontoAjuste() {
        if (tipoPontoAjuste == null) {
            tipoPontoAjuste = "";
        }
        return tipoPontoAjuste;
    }

    public void setTipoPontoAjuste(String tipoPontoAjuste) {
        this.tipoPontoAjuste = tipoPontoAjuste;
    }

    public String getNomeArquivoComprovanteOperacao() {
        if (nomeArquivoComprovanteOperacao == null) {
            nomeArquivoComprovanteOperacao = "";
        }
        return nomeArquivoComprovanteOperacao;
    }

    public void setNomeArquivoComprovanteOperacao(String nomeArquivoComprovanteOperacao) {
        this.nomeArquivoComprovanteOperacao = nomeArquivoComprovanteOperacao;
    }

}

package controle.basico;

import br.com.pactosolucoes.comuns.json.JSONMapper;
import org.json.JSONArray;
import controle.arquitetura.SuperControle;
import negocio.comuns.basico.ItemCampanhaJSON;
import negocio.comuns.basico.enumerador.TagsEnum;
import negocio.comuns.utilitarias.Uteis;
import servicos.propriedades.PropsService;

import java.io.File;
import java.util.ArrayList;
import java.util.List;

/**
 * Created by <PERSON> on 30/03/2016.
 */
public class BlogControle extends SuperControle {

    private static final String CHAVE_APP_LOGIN = "appLoginFoto";
    List<ItemCampanhaJSON> itemsCampanhaBlog ;

    public BlogControle(){
        carregarItemsCampanha();
    }

    public void carregarItemsCampanha(){
        try {
            String nomeArquivo = "dadosCampanha";
            String path = PropsService.getPropertyValue(PropsService.diretorioArquivos);
            String url = path + nomeArquivo + ".txt";
            File file = new File(url);
            JSONArray array = new JSONArray(Uteis.obterStringArquivoTexto(file));
            itemsCampanhaBlog = new ArrayList<ItemCampanhaJSON>();
            for(int e = 0; e < array.length() || e == 3; e++){
                 ItemCampanhaJSON obj = JSONMapper.getObject(array.getJSONObject(e).getJSONObject("JSONObject"), ItemCampanhaJSON.class);
                if(obj.getTag() == TagsEnum.CONTEUDO_BLOG) {
                    obj.setUrlImagem(PropsService.getPropertyValue(PropsService.urlFotosNuvem)+"/"+obj.getUrlImagem());
                    itemsCampanhaBlog.add(obj);
                }
            }
        }catch (Exception ignored){
        }
    }

    public List<ItemCampanhaJSON> getItemsCampanhaBlog() {
        return itemsCampanhaBlog;
    }

    public void setItemsCampanhaBlog(List<ItemCampanhaJSON> itemsCampanhaBlog) {
        this.itemsCampanhaBlog = itemsCampanhaBlog;
    }
}

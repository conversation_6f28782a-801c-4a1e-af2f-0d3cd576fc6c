package controle.basico;

import java.util.Iterator;

import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.enumeradores.TipoColaboradorEnum;
import negocio.facade.jdbc.basico.QuestionarioCliente;
import negocio.comuns.utilitarias.*;
import negocio.comuns.basico.*;
import javax.faces.model.SelectItem;
import java.util.List;
import java.util.ArrayList;
import controle.arquitetura.SuperControle;

/**
 * Classe responsável por implementar a interação entre os componentes JSF das páginas
 * questionarioClienteForm.jsp questionarioClienteCons.jsp) com as funcionalidades da classe <code>QuestionarioCliente</code>.
 * Implemtação da camada controle (Backing Bean).
 * @see SuperControle
 * @see QuestionarioCliente
 * @see QuestionarioClienteVO
 */
public class QuestionarioClienteCRMControle extends SuperControle {

    private QuestionarioClienteVO questionarioClienteVO;
    protected List listaSelectConsultor;
    protected List listaSelectQuestionario;
    /**
     * Interface <code>QuestionarioClienteInterfaceFacade</code> responsável pela interconexão da camada de controle com a camada de negócio.
     * Criando uma independência da camada de controle com relação a tenologia de persistência dos dados (DesignPatter: Façade).
     */

    private QuestionarioPerguntaClienteVO questionarioPerguntaClienteVO;
    private RespostaPergClienteVO repostaPergClienteVO;

    public QuestionarioClienteCRMControle() throws Exception {
        obterUsuarioLogado();
        inicializarFacades();
        setControleConsulta(new ControleConsulta());
        setMensagemID("msg_entre_prmconsulta");
        novoCRM();
    }
    
    public QuestionarioClienteCRMControle(ClienteVO cliente) throws Exception {
        obterUsuarioLogado();
        inicializarFacades();
        setControleConsulta(new ControleConsulta());
        setMensagemID("msg_entre_prmconsulta");
        novoCRM(cliente);
    }
    public void montarHistoricoBV(ClienteVO cliente) throws Exception {
        ClienteControle control = (ClienteControle) JSFUtilities.getManagedBean("ClienteControle");
        if(control == null){
            control = new ClienteControle();
            JSFUtilities.setManagedBeanValue("ClienteControle",control);
        }
        if(!control.getClienteVO().getCodigo().equals(cliente.getCodigo())) {
            control.prepararTelaCliente(cliente, true);
        }
        novoCRM(cliente);
    }

    public String novoCRM() throws Exception {
        ClienteControle clienteControle = (ClienteControle) context().getExternalContext().getSessionMap().get("ClienteControle");
        if (clienteControle == null) {
            return novoCRM(new ClienteVO());
        }
        clienteControle.pegarClienteTelaCliente();
        return novoCRM(clienteControle.getClienteVO());
    }
    public String novoCRM(ClienteVO cliente) throws Exception {
        try {
            setQuestionarioClienteVO(new QuestionarioClienteVO());
            setQuestionarioPerguntaClienteVO(new QuestionarioPerguntaClienteVO());
            setRepostaPergClienteVO(new RespostaPergClienteVO());
            inicializarListasSelectItemTodosComboBox();
            inicializarClienteCRM(cliente);
            setMensagemID("msg_entre_dados");
            return "";
        } catch (Exception e) {
            inicializarListasSelectItemTodosComboBox();
            setMensagemDetalhada(e.getMessage());
            return "";
        }
    }

    public void inicializarClienteCRM() throws Exception {
        try {
            ClienteControle clienteControle = (ClienteControle) context().getExternalContext().getSessionMap().get("ClienteControle");
            if (clienteControle != null) {
                inicializarClienteCRM(clienteControle.getClienteVO());
            }
            getQuestionarioClienteVO().getConsultorAnterior().setCodigo(getQuestionarioClienteVO().getConsultor().getCodigo());
        } catch (Exception e) {
            throw e;
        }
    }
    public void inicializarClienteCRM(ClienteVO cliente) throws Exception {
        try {
            if (cliente != null) {
                questionarioClienteVO.setCliente(cliente);
                List resultadoConsulta = getFacade().getQuestionarioCliente().consultarPorCodigoCliente(questionarioClienteVO.getCliente().getCodigo().intValue(), Uteis.NIVELMONTARDADOS_TODOS);
                setListaSelectQuestionario(resultadoConsulta);
                int tamanho = getListaSelectQuestionario().size();
                tamanho--;
                QuestionarioClienteVO quest = (QuestionarioClienteVO) getListaSelectQuestionario().get(tamanho);
                setQuestionarioClienteVO((quest == null ? new QuestionarioClienteVO() : quest));
                if (getQuestionarioClienteVO().getQuestionario().getCodigo().intValue() != 0) {
                    inicializarQuestionarioRespondido(getQuestionarioClienteVO().getQuestionarioPerguntaClienteVOs());
                } else {
                    getQuestionarioClienteVO().setQuestionarioPerguntaClienteVOs(new ArrayList());
                }
            }
            getQuestionarioClienteVO().getConsultorAnterior().setCodigo(getQuestionarioClienteVO().getConsultor().getCodigo());

        } catch (Exception e) {
            throw e;
        }
    }

    public void questionarioRespondidoPeloCliente() throws Exception {
        List resultadoConsulta = getFacade().getQuestionarioCliente().consultarPorCodigoCliente(questionarioClienteVO.getCliente().getCodigo().intValue(), Uteis.NIVELMONTARDADOS_TODOS);
        setListaSelectQuestionario(resultadoConsulta);
    }

    public void selecionarQuestionarioSerRespondidoPeloCliente() throws Exception {
        setMensagemID("msg_entre_dados");
        QuestionarioClienteVO quest = (QuestionarioClienteVO) context().getExternalContext().getRequestMap().get("questionario");
        setQuestionarioClienteVO((quest == null ? new QuestionarioClienteVO() : quest));
        if (getQuestionarioClienteVO().getQuestionario().getCodigo().intValue() != 0) {
            inicializarQuestionarioRespondido(getQuestionarioClienteVO().getQuestionarioPerguntaClienteVOs());
        } else {
            getQuestionarioClienteVO().setQuestionarioPerguntaClienteVOs(new ArrayList());
        }
        getQuestionarioClienteVO().getConsultorAnterior().setCodigo(getQuestionarioClienteVO().getConsultor().getCodigo());
    }

    public void selecionarQuestionarioSerRespondidoPeloClienteCRM(QuestionarioClienteVO quest) throws Exception {
        setQuestionarioClienteVO((quest == null ? new QuestionarioClienteVO() : quest));
        if (getQuestionarioClienteVO().getQuestionario().getCodigo().intValue() != 0) {
            inicializarQuestionarioRespondido(getQuestionarioClienteVO().getQuestionarioPerguntaClienteVOs());

        } else {
            getQuestionarioClienteVO().setQuestionarioPerguntaClienteVOs(new ArrayList());
        }
        getQuestionarioClienteVO().getConsultorAnterior().setCodigo(getQuestionarioClienteVO().getConsultor().getCodigo());
    }


    /**Metodo que realiza a verificação da opcao marcado pelo cliente, pois a perguntas que poderao ter somente uma resposta
     * e para isso o metodo necessita do atributo marcado que e inicializado pelos metodos INICIALIZARQUESTIONARIO E
     * INICIALIZARQUESTIONARIORESPONDIDO.
     * */
    public void escolhaSimples() {
        RespostaPergClienteVO obj = (RespostaPergClienteVO) context().getExternalContext().getRequestMap().get("repostaPergCliente");
        Iterator i = questionarioClienteVO.getQuestionarioPerguntaClienteVOs().iterator();
        while (i.hasNext()) {
            QuestionarioPerguntaClienteVO objExistente = (QuestionarioPerguntaClienteVO) i.next();
            if (objExistente.getPerguntaCliente().getSimples()) {
                Iterator j = objExistente.getPerguntaCliente().getRespostaPergClienteVOs().iterator();
                while (j.hasNext()) {
                    RespostaPergClienteVO resposta = (RespostaPergClienteVO) j.next();
                    if (obj.getMarcado().equals(resposta.getMarcado())) {
                        if (obj.getDescricaoRespota().equals(resposta.getDescricaoRespota())) {
                            resposta.setRespostaOpcao(true);
                        } else {
                            resposta.setRespostaOpcao(false);
                        }
                    }
                }
            }
        }
    }

    /**Metodo que recebe a lista do questionario e realiza sua montagem onde passa pelo processo de 2 iteraçoes onde a primeira
     * monta as pergunta do questionario e colocando na lista de pergunta do cliente e a outra iteração monta as possiveis resposta
     * para pergunta e colocando na lista de respota do cliente. E atributo cont e necessario pois ele e fundamental para outro metodo
     * na hora de validar os dados marcado pelo cliente.
     * */
    public void inicializarQuestionario(List questionario) {
        int cont = 0;
        Iterator i = questionario.iterator();
        while (i.hasNext()) {
            QuestionarioPerguntaVO objExistente = (QuestionarioPerguntaVO) i.next();
            questionarioPerguntaClienteVO.getPerguntaCliente().setDescricao(objExistente.getPergunta().getDescricao());
            questionarioPerguntaClienteVO.getPerguntaCliente().setTipoPergunta(objExistente.getPergunta().getTipoPergunta());
            if (objExistente.getPergunta().getTipoPergunta().equals("SE") || objExistente.getPergunta().getTipoPergunta().equals("SN")) {
                questionarioPerguntaClienteVO.getPerguntaCliente().setSimples(new Boolean(true));
            }
            if (objExistente.getPergunta().getTipoPergunta().equals("ME")) {
                questionarioPerguntaClienteVO.getPerguntaCliente().setMultipla(new Boolean(true));
            }
            if (objExistente.getPergunta().getTipoPergunta().equals("TE")) {
                questionarioPerguntaClienteVO.getPerguntaCliente().setTextual(new Boolean(true));
            }
            Iterator j = objExistente.getPergunta().getRespostaPerguntaVOs().iterator();
            if (!j.hasNext()) {
                repostaPergClienteVO.setMarcado(cont);
                questionarioPerguntaClienteVO.getPerguntaCliente().getRespostaPergClienteVOs().add(repostaPergClienteVO);
                setRepostaPergClienteVO(new RespostaPergClienteVO());
            } else {
                while (j.hasNext()) {
                    RespostaPerguntaVO objrespota = (RespostaPerguntaVO) j.next();
                    repostaPergClienteVO.setDescricaoRespota(objrespota.getDescricaoRespota());
                    repostaPergClienteVO.setMarcado(cont);
                    questionarioPerguntaClienteVO.getPerguntaCliente().getRespostaPergClienteVOs().add(repostaPergClienteVO);
                    setRepostaPergClienteVO(new RespostaPergClienteVO());
                }
            }
            cont++;
            questionarioClienteVO.getQuestionarioPerguntaClienteVOs().add(questionarioPerguntaClienteVO);
            setQuestionarioPerguntaClienteVO(new QuestionarioPerguntaClienteVO());
        }

    }

    /**Metodo que recebe a lista do questionario ja respondido e realiza sua montagem e colocando o atributo cont para realização
     * no metodo Escolha simples
     * */
    public void inicializarQuestionarioRespondido(List questionario) {
    	validarConsultorInativo();
    	int cont = 0;
        getQuestionarioClienteVO().setQuestionarioPerguntaClienteVOs(new ArrayList());
        Iterator i = questionario.iterator();
        while (i.hasNext()) {
            QuestionarioPerguntaClienteVO objExistente = (QuestionarioPerguntaClienteVO) i.next();
            Iterator j = objExistente.getPerguntaCliente().getRespostaPergClienteVOs().iterator();
            int index = 0;
            while (j.hasNext()) {
                RespostaPergClienteVO objrespota = (RespostaPergClienteVO) j.next();
                objrespota.setMarcado(cont);
                objExistente.getPerguntaCliente().getRespostaPergClienteVOs().set(index, objrespota);
                index++;
            }
            cont++;
            questionarioClienteVO.getQuestionarioPerguntaClienteVOs().add(objExistente);
        }

    }
    
    public void validarConsultorInativo(){
        boolean existeConsulto = false;
    	Iterator i = getListaSelectConsultor().iterator();
        while (i.hasNext()) {
        	SelectItem obj = (SelectItem) i.next();
            if(obj.getValue().equals(getQuestionarioClienteVO().getConsultor().getCodigo())){
            	existeConsulto = true;
            	break;
            }
        }
        if(!existeConsulto){
        	getListaSelectConsultor().add(new SelectItem(getQuestionarioClienteVO().getConsultor().getCodigo(), getQuestionarioClienteVO().getConsultor().getPessoa().getNome().toString()));
        }
    }

    /**
     * Operação responsável por processar a exclusão um objeto da classe <code>QuestionarioClienteVO</code>
     * Após a exclusão ela automaticamente aciona a rotina para uma nova inclusão.
     */
    public String excluir() {
        try {
            getFacade().getQuestionarioCliente().excluir(questionarioClienteVO);
            setQuestionarioClienteVO(new QuestionarioClienteVO());
            setQuestionarioPerguntaClienteVO(new QuestionarioPerguntaClienteVO());
            setMensagemID("msg_dados_excluidos");
            return "editar";
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            return "erro";
        }
    }

    public void irPaginaInicial() throws Exception {
        controleConsulta.setPaginaAtual(1);
        this.consultar();
    }

    public void irPaginaAnterior() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getPaginaAtual() - 1);
        this.consultar();
    }

    public void irPaginaPosterior() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getPaginaAtual() + 1);
        this.consultar();
    }

    public void irPaginaFinal() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getNrTotalPaginas());
        this.consultar();
    }

    /**
     * Método responsável por gerar uma lista de objetos do tipo <code>SelectItem</code> para preencher
     * o comboBox relativo ao atributo <code>Consultor</code>.
     */
    public void montarListaSelectItemConsultor(String prm) throws Exception {
        Integer empresa = 0;
        if (!getEmpresaLogado().getCodigo().equals(new Integer(0)) && (!getEmpresaLogado().getCodigo().equals(null))) {
            empresa = getEmpresaLogado().getCodigo().intValue();
        }
        List resultadoConsulta = consultarColaboradorPorSituacao(prm, empresa);
        Iterator i = resultadoConsulta.iterator();
        List objs = new ArrayList();
        objs.add(new SelectItem(new Integer(0), ""));
        while (i.hasNext()) {
            ColaboradorVO obj = (ColaboradorVO) i.next();
            objs.add(new SelectItem(obj.getCodigo(), obj.getPessoa().getNome().toString()));
        }
        setListaSelectConsultor(objs);
    }

    /**
     * Método responsável por atualizar o ComboBox relativo ao atributo <code>Consultor</code>.
     * Buscando todos os objetos correspondentes a entidade <code>Colaborador</code>. Esta rotina não recebe parâmetros para filtragem de dados, isto é
     * importante para a inicialização dos dados da tela para o acionamento por meio requisições Ajax.
     */
    public void montarListaSelectItemConsultor() {
        try {
            montarListaSelectItemConsultor("");
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    /**
     * Método responsável por consultar dados da entidade <code><code> e montar o atributo <code>situacao</code>
     * Este atributo é uma lista (<code>List</code>) utilizada para definir os valores a serem apresentados no ComboBox correspondente
     */
    public List consultarColaboradorPorSituacao(String situacaoPrm, Integer empresa) throws Exception {
        List lista = getFacade().getColaborador().consultarPorTipoColaboradorAtivo(TipoColaboradorEnum.CONSULTOR, "AT", empresa, false, Uteis.NIVELMONTARDADOS_TODOS);
        return lista;
    }

    /**
     * Método responsável por inicializar a lista de valores (<code>SelectItem</code>) para todos os ComboBox's.
     */
    public void inicializarListasSelectItemTodosComboBox() {
        // montarListaSelectItemQuestionario();
        montarListaSelectItemConsultor();

    }

    /**
     * Rotina responsável por preencher a combo de consulta da telas.
     */
    public List getTipoConsultaCombo() {
        List itens = new ArrayList();
        itens.add(new SelectItem("descricaoQuestionario", "Questionário"));
        itens.add(new SelectItem("codigo", "Código"));
        itens.add(new SelectItem("matriculaCliente", "Cliente"));
        itens.add(new SelectItem("situacaoColaborador", "Consultor"));
        itens.add(new SelectItem("data", "Data"));
        return itens;
    }

    /**
     * Rotina responsável por organizar a paginação entre as páginas resultantes de uma consulta.
     */
    public String inicializarConsultar() {
        setPaginaAtualDeTodas("0/0");
        setListaConsulta(new ArrayList());
        definirVisibilidadeLinksNavegacao(0, 0);
        setMensagemID("msg_entre_prmconsulta");
        return "consultar";
    }

    /**
     * Operação que inicializa as Interfaces Façades com os respectivos objetos de
     * persistência dos dados no banco de dados.
     */
    protected boolean inicializarFacades() {
        try {
            super.inicializarFacades();

            return true;
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro_conectarBD", e.getMessage());
            return false;
        }

    }

    public QuestionarioPerguntaClienteVO getQuestionarioPerguntaClienteVO() {
        return questionarioPerguntaClienteVO;
    }

    public void setQuestionarioPerguntaClienteVO(QuestionarioPerguntaClienteVO questionarioPerguntaClienteVO) {
        this.questionarioPerguntaClienteVO = questionarioPerguntaClienteVO;
    }

    public List getListaSelectConsultor() {
        return (listaSelectConsultor);
    }

    public void setListaSelectConsultor(List listaSelectConsultor) {
        this.listaSelectConsultor = listaSelectConsultor;
    }

    public List getListaSelectQuestionario() {
        return (listaSelectQuestionario);
    }

    public void setListaSelectQuestionario(List listaSelectQuestionario) {
        this.listaSelectQuestionario = listaSelectQuestionario;
    }

    public QuestionarioClienteVO getQuestionarioClienteVO() {
        return questionarioClienteVO;
    }

    public void setQuestionarioClienteVO(QuestionarioClienteVO questionarioClienteVO) {
        this.questionarioClienteVO = questionarioClienteVO;
    }

    public RespostaPergClienteVO getRepostaPergClienteVO() {
        return repostaPergClienteVO;
    }

    public void setRepostaPergClienteVO(RespostaPergClienteVO repostaPergClienteVO) {
        this.repostaPergClienteVO = repostaPergClienteVO;
    }
}

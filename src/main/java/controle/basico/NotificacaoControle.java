/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package controle.basico;

import br.com.pactosolucoes.enumeradores.TipoPlanoEnum;
import controle.arquitetura.SuperControle;
import java.text.DecimalFormat;
import java.util.Calendar;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.plano.IndiceFinanceiroReajustePrecoVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;

/**
 *
 * <AUTHOR>
 */
public class NotificacaoControle extends SuperControle{

    private String contratosNaoRenovados;
    private String contratosNaoRenovadosMesCorrente;
    private String contratosMensagem;
    private boolean mostrarBotaoIndiceFinanceiro = false;
    private Boolean modalIndiceFinanceiro;
    private boolean mostrarIndiceFinancerioProximoMes = false;
    private boolean mostrarIndiceFinanceiroMesAtual = false;
    private Boolean verificouModal = false;
    private boolean mostrarBotoes = true;
    private String tiposIGPM;
    private String tiposIGPMMesCorrente;

    public NotificacaoControle() throws Exception{
        verificarModalIndiceFinanceiro();
    }
    
    public void verificarModalIndiceFinanceiro() throws Exception{
        validarPermissaoUsuarioIndiceFinanceiro();
        try {
            EmpresaVO empresa = getFacade().getEmpresa().consultarPorNomeEmpresa(getNomeEmpresaLogada(), false, Uteis.NIVELMONTARDADOS_GESTAOREMESSA);
            if (empresa.isNaoRenovarContratoSemIndiceFinanceiro()) {
                Calendar dataInicioContratoProximoMes = Calendario.getInstance();
                Integer proximoMes = Uteis.getMesData(dataInicioContratoProximoMes.getTime());
                dataInicioContratoProximoMes.setTime(Uteis.somarMeses(dataInicioContratoProximoMes.getTime(), 1));
                IndiceFinanceiroReajustePrecoVO indiceFinanceiroVOProximoMes = getFacade().getIndiceFinanceiroReajustePreco().consultarIndiceFinanceiroPeriodo(proximoMes.toString(), String.valueOf(dataInicioContratoProximoMes.get(Calendar.YEAR)), true, TipoPlanoEnum.PLANO_RECORRENCIA, true, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                IndiceFinanceiroReajustePrecoVO indiceFinanceiroVOProximoMesPlanoNormal = getFacade().getIndiceFinanceiroReajustePreco().consultarIndiceFinanceiroPeriodo(proximoMes.toString(), String.valueOf(dataInicioContratoProximoMes.get(Calendar.YEAR)), true, TipoPlanoEnum.PLANO_NORMAL, true, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                if (indiceFinanceiroVOProximoMes == null && indiceFinanceiroVOProximoMesPlanoNormal == null) {
                    mensagem = (Uteis.getDataMesAnoConcatenado(dataInicioContratoProximoMes.getTime()));
                    contratosNaoRenovados = getFacade().getContrato().obterQuantidadeDeContratoRenovadoAutomaticamente(String.valueOf(dataInicioContratoProximoMes.get(Calendar.YEAR)),proximoMes.toString()).toString();
                    tiposIGPM = "Plano Normal e Plano Recorrência";
                    setModalIndiceFinanceiro(true);
                    setMostrarIndiceFinancerioProximoMes(true);
                }else if (indiceFinanceiroVOProximoMes != null && indiceFinanceiroVOProximoMesPlanoNormal == null){
                    mensagem = (Uteis.getDataMesAnoConcatenado(dataInicioContratoProximoMes.getTime()));
                    contratosNaoRenovados = getFacade().getContrato().obterQuantidadeDeContratoRenovadoAutomaticamente(String.valueOf(dataInicioContratoProximoMes.get(Calendar.YEAR)),proximoMes.toString()).toString();
                    tiposIGPM = "Plano Normal";
                    setModalIndiceFinanceiro(true);
                    setMostrarIndiceFinancerioProximoMes(true);
                }else if(indiceFinanceiroVOProximoMes == null && indiceFinanceiroVOProximoMesPlanoNormal != null){
                    mensagem = (Uteis.getDataMesAnoConcatenado(dataInicioContratoProximoMes.getTime()));
                    contratosNaoRenovados = getFacade().getContrato().obterQuantidadeDeContratoRenovadoAutomaticamente(String.valueOf(dataInicioContratoProximoMes.get(Calendar.YEAR)),proximoMes.toString()).toString();
                    tiposIGPM = "Plano Recorrência";
                    setModalIndiceFinanceiro(true);
                    setMostrarIndiceFinancerioProximoMes(true);
                }else {
                    tiposIGPM = "";
                    setModalIndiceFinanceiro(false);
                }
                Calendar dataInicioContratoMesCorrente = Calendario.getInstance();
                Integer mescorrente = Uteis.getMesData(dataInicioContratoMesCorrente.getTime());
                IndiceFinanceiroReajustePrecoVO indiceFinanceiroVOMesCorrente = getFacade().getIndiceFinanceiroReajustePreco().consultarIndiceFinanceiroPeriodo(mescorrente.toString(), String.valueOf(dataInicioContratoMesCorrente.get(Calendar.YEAR)), true, TipoPlanoEnum.PLANO_RECORRENCIA, true, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                IndiceFinanceiroReajustePrecoVO indiceFinanceiroVOMesCorrentePlanoNormal = getFacade().getIndiceFinanceiroReajustePreco().consultarIndiceFinanceiroPeriodo(mescorrente.toString(), String.valueOf(dataInicioContratoMesCorrente.get(Calendar.YEAR)), true, TipoPlanoEnum.PLANO_NORMAL, true, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                if (indiceFinanceiroVOMesCorrente == null && indiceFinanceiroVOMesCorrentePlanoNormal == null) {
                    contratosMensagem = (Uteis.getDataMesAnoConcatenado(dataInicioContratoMesCorrente.getTime()));
                    contratosNaoRenovadosMesCorrente = getFacade().getContrato().obterQuantidadeDeContratoRenovadoAutomaticamente(String.valueOf(dataInicioContratoMesCorrente.get(Calendar.YEAR)),mescorrente.toString()).toString();
                    tiposIGPMMesCorrente = "Plano Normal e Plano Recorrência";
                    setModalIndiceFinanceiro(true);
                    setMostrarIndiceFinanceiroMesAtual(true);
                }else if (indiceFinanceiroVOMesCorrente != null && indiceFinanceiroVOMesCorrentePlanoNormal == null){
                    contratosMensagem = (Uteis.getDataMesAnoConcatenado(dataInicioContratoMesCorrente.getTime()));
                    contratosNaoRenovadosMesCorrente = getFacade().getContrato().obterQuantidadeDeContratoRenovadoAutomaticamente(String.valueOf(dataInicioContratoMesCorrente.get(Calendar.YEAR)),mescorrente.toString()).toString();
                    tiposIGPMMesCorrente = "Plano Normal";
                    setModalIndiceFinanceiro(true);
                    setMostrarIndiceFinanceiroMesAtual(true);
                }else if (indiceFinanceiroVOMesCorrente == null && indiceFinanceiroVOMesCorrentePlanoNormal != null){
                    contratosMensagem = (Uteis.getDataMesAnoConcatenado(dataInicioContratoMesCorrente.getTime()));
                    contratosNaoRenovadosMesCorrente = getFacade().getContrato().obterQuantidadeDeContratoRenovadoAutomaticamente(String.valueOf(dataInicioContratoMesCorrente.get(Calendar.YEAR)),mescorrente.toString()).toString();
                    tiposIGPMMesCorrente = "Plano Recorrência";
                    setModalIndiceFinanceiro(true);
                    setMostrarIndiceFinanceiroMesAtual(true);
                }else {
                    if(getModalIndiceFinanceiro() == false){
                        setModalIndiceFinanceiro(false);
                    }
                }
            }else{
                setModalIndiceFinanceiro(false);
            }

        } catch (Exception e) {
            setModalIndiceFinanceiro(true);
            setMostrarBotoes(false);
            setMensagemDetalhada(e.getMessage());
        }
    }
    
    public void validarPermissaoUsuarioIndiceFinanceiro() throws Exception{
        try {
            if (getUsuarioLogado().getUsuarioPerfilAcessoVOs().isEmpty()) {
                if (getUsuarioLogado().getAdministrador()) {
                    mostrarBotaoIndiceFinanceiro = true;
                }
            }
            mostrarBotaoIndiceFinanceiro = getFacade().getControleAcesso().verificarPermissaoFuncionalidade("IndiceFinanceiroReajustePreco");
        } catch (Exception e) {
            Uteis.logar("Erro ao consultar permissão: " + e, NotificacaoControle.class);
            setModalIndiceFinanceiro(true);
            setMostrarBotoes(false);
            setMensagemDetalhada(e.getMessage());
        }
    }
    
    public void setContratosNaoRenovados(String contratosNaoRenovados) {
        this.contratosNaoRenovados = contratosNaoRenovados;
    }

    public String getContratosNaoRenovados() {
        return contratosNaoRenovados;
    }

    public void setContratosMensagem(String contratosMensagem) {
        this.contratosMensagem = contratosMensagem;
    }

    public String getContratosMensagem() {
        return contratosMensagem;
    }

    public void setContratosNaoRenovadosMesCorrente(String contratosNaoRenovadosMesCorrente) {
        this.contratosNaoRenovadosMesCorrente = contratosNaoRenovadosMesCorrente;
    }

    public String getContratosNaoRenovadosMesCorrente() {
        return contratosNaoRenovadosMesCorrente;
    }
    
    public void setMostrarBotaoIndiceFinanceiro(boolean mostrarBotaoIndiceFinanceiro) {
        this.mostrarBotaoIndiceFinanceiro = mostrarBotaoIndiceFinanceiro;
    }

    public boolean isMostrarBotaoIndiceFinanceiro() {
        return mostrarBotaoIndiceFinanceiro;
    }
    
    public void setModalIndiceFinanceiro(Boolean modalIndiceFinanceiro) {
        this.modalIndiceFinanceiro = modalIndiceFinanceiro;
    }

    public Boolean getModalIndiceFinanceiro() {
        return this.modalIndiceFinanceiro;
    }
    
    public void setMostrarIndiceFinancerioProximoMes(boolean mostrarIndiceFinancerioProximoMes) {
        this.mostrarIndiceFinancerioProximoMes = mostrarIndiceFinancerioProximoMes;
    }

    public boolean isMostrarIndiceFinancerioProximoMes() {
        return mostrarIndiceFinancerioProximoMes;
    }
    
    public void setMostrarIndiceFinanceiroMesAtual(boolean mostrarIndiceFinanceiroMesAtual) {
        this.mostrarIndiceFinanceiroMesAtual = mostrarIndiceFinanceiroMesAtual;
    }

    public boolean isMostrarIndiceFinanceiroMesAtual() {
        return mostrarIndiceFinanceiroMesAtual;
    }
    
    public void setVerificouModal(Boolean verificouModal) {
        this.verificouModal = verificouModal;
    }

    public Boolean getVerificouModal() {
        return verificouModal;
    }
    
    public void toogleVerificado(){
        this.verificouModal = true;
    }

    public void setMostrarBotoes(boolean mostrarBotoes) {
        this.mostrarBotoes = mostrarBotoes;
    }

    public boolean isMostrarBotoes() {
        return mostrarBotoes;
    }

    public String getTiposIGPM() {
        return tiposIGPM;
    }

    public void setTiposIGPM(String tiposIGPM) {
        this.tiposIGPM = tiposIGPM;
    }

    public String getTiposIGPMMesCorrente() {
        return tiposIGPMMesCorrente;
    }

    public void setTiposIGPMMesCorrente(String tiposIGPMMesCorrente) {
        this.tiposIGPMMesCorrente = tiposIGPMMesCorrente;
    }
}

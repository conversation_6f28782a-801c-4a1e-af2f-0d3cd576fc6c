package controle.basico;

import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.estrutura.exportador.controle.ExportadorListaControle;
import br.com.pactosolucoes.integracao.enotas.to.InfoCidadeEnotasTO;
import br.com.pactosolucoes.integracao.enotas.to.InfoEmpresaEnotasTO;
import br.com.pactosolucoes.integracao.enotas.to.InfoServicoEnotasTO;
import negocio.comuns.basico.enumerador.TipoExceptionEnum;
import negocio.comuns.utilitarias.*;
import org.json.JSONObject;
import controle.arquitetura.SuperControle;
import controle.arquitetura.security.LoginControle;
import negocio.comuns.basico.*;
import negocio.comuns.financeiro.enumerador.TipoExigibilidadeISSEnum;
import negocio.comuns.nfe.enumerador.CRTEnum;
import negocio.comuns.notaFiscal.AmbienteEmissaoNotaFiscalEnum;
import negocio.comuns.notaFiscal.TipoNotaFiscalEnum;
import negocio.facade.jdbc.basico.Estado;
import negocio.facade.jdbc.utilitarias.Cep;
import org.richfaces.event.UploadEvent;
import org.richfaces.model.UploadItem;
import servicos.operacoes.midias.MidiaService;
import servicos.operacoes.midias.commons.MidiaEntidadeEnum;
import servicos.propriedades.PropsService;

import javax.faces.event.ActionEvent;
import javax.faces.model.SelectItem;
import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.OutputStream;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

public class ConfiguracaoNotaFiscalControle extends SuperControle {

    private ConfiguracaoNotaFiscalVO configuracaoNotaFiscalVO;
    private List<SelectItem> listaSelectItemPais;
    private List<SelectItem> listaSelectItemEstado;
    private List<SelectItem> listaSelectItemCidade;
    private File arquivo;
    private String extensaoArquivo;
    private String nomeArquivo;
    private String onCompleteArquivo;

    private File arquivoLogo;
    private String extensaoArquivoLogo;
    private String nomeArquivoLogo;
    private String onCompleteArquivoLogo;
    private boolean apresentarTamanhoLogo = false;
    private String alturaLogo = "";
    private String larguraLogo = "";

    private JSONObject infoEnotasJSON;
    private String infoEnotas;
    private String onCompleteEnotas;

    private InfoCidadeEnotasTO infoCidadeEnotasTO;
    private List<InfoServicoEnotasTO> listaInfoServicoTO;
    private InfoEmpresaEnotasTO infoEmpresaEnotasTO;

    private String tipo;
    private List<SelectItem> listaTipo = new ArrayList<SelectItem>();

    public ConfiguracaoNotaFiscalControle() throws Exception {
        limparMsg();
        obterUsuarioLogado();
        inicializarFacades();
        setMensagemID("msg_entre_prmconsulta");
    }

    public String novo() throws Exception {
        limparMsg();
        setArquivo(null);
        setArquivoLogo(null);
        setConfiguracaoNotaFiscalVO(new ConfiguracaoNotaFiscalVO());
        getConfiguracaoNotaFiscalVO().setEmpresaVO(getEmpresaLogado());
        getConfiguracaoNotaFiscalVO().setTipoNotaFiscal(null);
        setApresentarTamanhoLogo(false);
        montarListaEmpresas();
        montarListaPais();
        montarListaEstado();
        montarListaCidade();
        return "editar";
    }

    public String editar() {
        limparMsg();
        Integer codigoEditar = Integer.parseInt(request().getParameter("chavePrimaria"));
        try {
            setApresentarTamanhoLogo(false);
            ConfiguracaoNotaFiscalVO obj = getFacade().getConfiguracaoNotaFiscal().consultarPorChavePrimaria(codigoEditar, Uteis.NIVELMONTARDADOS_TODOS);
            obj.setEmpresaVO(getFacade().getEmpresa().consultarPorChavePrimaria(obj.getEmpresaVO().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS));
            obj.setNovoObj(false);
            setConfiguracaoNotaFiscalVO(obj);
            configuracaoNotaFiscalVO.registrarObjetoVOAntesDaAlteracao();
            configuracaoNotaFiscalVO.getConfigProducaoVO().registrarObjetoVOAntesDaAlteracao();
            configuracaoNotaFiscalVO.getConfigHomologacaoVO().registrarObjetoVOAntesDaAlteracao();
            montarListaPais();
            montarListaEstado();
            montarListaCidade();
            montarListaEmpresas();
        } catch (Exception e) {
            setErro(true);
            setSucesso(false);
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
        return "editar";
    }

    public void gravar() {
        try {
            limparMsg();
            validarCnpjConfig();
            if (configuracaoNotaFiscalVO.isNovoObj()) {
                getFacade().getConfiguracaoNotaFiscal().incluir(configuracaoNotaFiscalVO);
                incluirLogInclusao();
            } else {
                getFacade().getConfiguracaoNotaFiscal().alterar(configuracaoNotaFiscalVO);
                incluirLogAlteracao();
            }

            //não deve "enviarConfiguracoesAmbiente" sincronizar com o enotas aqui!!!
            //pois tem um botão para sincronizar enviando!
            //isso para que não seja alterado a sequencia do rps sem que esteja a sequencia correta.!
            //by Luiz Felipe 15/07/2020
            StringBuilder msg = new StringBuilder("Dados gravados com sucesso!");

            String enotas = getFacade().getConfiguracaoNotaFiscal().sincronizarEnotas(getKey(), false, configuracaoNotaFiscalVO);
          //  String moduloNotas = getFacade().getConfiguracaoNotaFiscal().atualizarDadosModuloNFe(configuracaoNotaFiscalVO);

            if(configuracaoNotaFiscalVO.isEnotas() != ((ConfiguracaoNotaFiscalVO)configuracaoNotaFiscalVO.getObjetoVOAntesAlteracao()).isEnotas()) {
                String hd = getFacade().getConfiguracaoNotaFiscal().habilitarDesabilitarEmpresa(configuracaoNotaFiscalVO.isEnotas(),
                        configuracaoNotaFiscalVO.getIdEnotas());
                if(!UteisValidacao.emptyString(hd)) {
                    msg.append(" ").append(hd);
                }
            }

            if (!UteisValidacao.emptyString(enotas)) {
                msg.append(" ").append(enotas);
            }
       /*     if (!UteisValidacao.emptyString(moduloNotas)) {
                msg.append(" ").append(moduloNotas);
            }

        */
            configuracaoNotaFiscalVO.registrarObjetoVOAntesDaAlteracao();
            montarSucessoGrowl(msg.toString());
        } catch (Exception e) {
            if(e.getCause() != null && e.getCause().getMessage().equals(TipoExceptionEnum.ALERTA.name())){
                montarAviso(e.getMessage());
            }else{
                montarErro(e);
            }
        }
    }

    public void selecionarEmpresa() throws Exception {
        if (UteisValidacao.emptyNumber(configuracaoNotaFiscalVO.getEmpresaVO().getCodigo())) {
            configuracaoNotaFiscalVO.setEmpresaVO(new EmpresaVO());
        } else {
            configuracaoNotaFiscalVO.setEmpresaVO(getFacade().getEmpresa().consultarPorChavePrimaria(configuracaoNotaFiscalVO.getEmpresaVO().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
        }
    }

    public void obterInformacoesModuloNotas() {
        try {
            limparMsg();
            getFacade().getConfiguracaoNotaFiscal().obterInformacoesModuloNotas(getKey(), configuracaoNotaFiscalVO);
            montarSucessoGrowl("Dados sincronizados! Clique em gravar para salvar.");
        } catch (Exception e) {
            montarErro(e);
        }
    }

    public void obterInformacoesCidade() {
        try {
            limparMsg();
            setInfoEnotas("");
            setOnCompleteEnotas("");
            setInfoCidadeEnotasTO(getFacade().getConfiguracaoNotaFiscal().obterInformacoesCidade(configuracaoNotaFiscalVO));
            setOnCompleteEnotas("Richfaces.showModalPanel('modalInfoCidade');");
        } catch (Exception e) {
            montarErro(e);
        }
    }

    public void obterInformacoesServico() {
        try {
            limparMsg();
            setInfoEnotas("");
            setOnCompleteEnotas("");
            setListaInfoServicoTO(getFacade().getConfiguracaoNotaFiscal().obterInformacoesServico(configuracaoNotaFiscalVO));
            setOnCompleteEnotas("Richfaces.showModalPanel('modalInfoServico');");
        } catch (Exception e) {
            montarErro(e);
        }
    }

    public String consultar() {
        limparMsg();
        return "consultar";
    }

    public String excluir() {
        //não deve excluir para evitar problemas de referencia com módulo de notas
        return "";
    }

    protected boolean inicializarFacades() {
        try {
            super.inicializarFacades();
            return true;
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro_conectarBD", e.getMessage());
            return false;
        }
    }

    public void exportar(ActionEvent evt) throws Exception {
        ExportadorListaControle exportadorListaControle = (ExportadorListaControle) JSFUtilities.getFromRequest(ExportadorListaControle.class.getSimpleName());
        List<ConfiguracaoNotaFiscalVO> listaParaImpressao = getFacade().getConfiguracaoNotaFiscal().consultarConfiguracaoNotaFiscal(getEmpresaLogado().getCodigo(), null, null, null, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        exportadorListaControle.exportar(evt, listaParaImpressao, "", null);
    }

    public void incluirLogInclusao() throws Exception {
        try {
            configuracaoNotaFiscalVO.setObjetoVOAntesAlteracao(new ConfiguracaoNotaFiscalVO());
            configuracaoNotaFiscalVO.setNovoObj(true);
            registrarLogObjetoVO(configuracaoNotaFiscalVO, configuracaoNotaFiscalVO.getCodigo(), "CONFIGURACAONOTAFISCAL", 0);
        } catch (Exception e) {
            registrarLogErroObjetoVO("CONFIGURACAONOTAFISCAL", configuracaoNotaFiscalVO.getCodigo(), "ERRO AO GERAR LOG DE INCLUSÃO DE CONFIGURACAO NOTA FISCAL", this.getUsuarioLogado().getNome(), this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
        configuracaoNotaFiscalVO.setNovoObj(false);
    }

    public void incluirLogExclusao() throws Exception {
        try {
            configuracaoNotaFiscalVO.setObjetoVOAntesAlteracao(new ConfiguracaoNotaFiscalVO());
            configuracaoNotaFiscalVO.setNovoObj(true);
            registrarLogExclusaoTodosDadosObjetoVO(configuracaoNotaFiscalVO, configuracaoNotaFiscalVO.getCodigo(), "CONFIGURACAONOTAFISCAL", 0);
        } catch (Exception e) {
            registrarLogErroObjetoVO("CONFIGURACAONOTAFISCAL", configuracaoNotaFiscalVO.getCodigo(), "ERRO AO GERAR LOG DE EXCLUSÃO DE CONFIGURACAO NOTA FISCAL ", this.getUsuarioLogado().getNome(), this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
    }

    public void incluirLogAlteracao() throws Exception {
        try {
            registrarLogObjetoVO(configuracaoNotaFiscalVO, configuracaoNotaFiscalVO.getCodigo(), "CONFIGURACAONOTAFISCAL", 0);
            registrarLogObjetoVO(configuracaoNotaFiscalVO.getConfigProducaoVO(), configuracaoNotaFiscalVO.getConfigProducaoVO().getCodigo(), "CONFIGURACAONOTAFISCALAMBIENTE", 0);
            registrarLogObjetoVO(configuracaoNotaFiscalVO.getConfigHomologacaoVO(), configuracaoNotaFiscalVO.getConfigHomologacaoVO().getCodigo(), "CONFIGURACAONOTAFISCALAMBIENTE", 0);
        } catch (Exception e) {
            registrarLogErroObjetoVO("CONFIGURACAONOTAFISCAL", configuracaoNotaFiscalVO.getCodigo(), "ERRO AO GERAR LOG DE ALTERAÇÃO DE CONFIGURACAO NOTA FISCAL ", this.getUsuarioLogado().getNome(), this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
    }

    public void realizarConsultaLogObjetoSelecionado() {
        Map<Integer, String> nomesCodigosEntidades = new HashMap<>();

        LoginControle loginControle = (LoginControle) context().getExternalContext().getSessionMap().get("LoginControle");
        String nomeClasseConfigNota = configuracaoNotaFiscalVO.getClass().getSimpleName();
        nomesCodigosEntidades.put(configuracaoNotaFiscalVO.getCodigo(), nomeClasseConfigNota.substring(0, nomeClasseConfigNota.length() - 2).toUpperCase());

        if(!nomesCodigosEntidades.containsKey(configuracaoNotaFiscalVO.getConfigProducaoVO().getCodigo())){
            String nomeClasseConfigNotaAmbienteProd = configuracaoNotaFiscalVO.getConfigProducaoVO().getClass().getSimpleName();
            nomesCodigosEntidades.put(configuracaoNotaFiscalVO.getConfigProducaoVO().getCodigo(), nomeClasseConfigNotaAmbienteProd.substring(0, nomeClasseConfigNotaAmbienteProd.length() - 2).toUpperCase());
        }

        if(!nomesCodigosEntidades.containsKey(configuracaoNotaFiscalVO.getConfigHomologacaoVO().getCodigo())){
            String nomeClasseConfigNotaAmbienteHomol = configuracaoNotaFiscalVO.getConfigHomologacaoVO().getClass().getSimpleName();
            nomesCodigosEntidades.put(configuracaoNotaFiscalVO.getConfigHomologacaoVO().getCodigo(), nomeClasseConfigNotaAmbienteHomol.substring(0, nomeClasseConfigNotaAmbienteHomol.length() - 2).toUpperCase());
        }

        loginControle.setNomeClasse(loginControle.getNomeEntidadeCamposLog("prt_" + nomeClasseConfigNota + "_tituloForm"));
        loginControle.consultarLogListaObjetosSelecionados(nomesCodigosEntidades, 0);
    }

    public void realizarConsultaLogObjetoGeral() {
        configuracaoNotaFiscalVO = new ConfiguracaoNotaFiscalVO();
        realizarConsultaLogObjetoSelecionado();
    }

    public List<SelectItem> getListaTipoNota() {
        List<SelectItem> itens = new ArrayList<SelectItem>();
        for (TipoNotaFiscalEnum tipo : TipoNotaFiscalEnum.values()) {
            if (!tipo.equals(TipoNotaFiscalEnum.TODAS)) {
                itens.add(new SelectItem(tipo, tipo.getDescricao()));
            }
        }
        return itens;
    }

    public List<SelectItem> getListaExigibilidadeISS() {
        return TipoExigibilidadeISSEnum.getSelectListExigibilidadeISS();
    }

    public List<SelectItem> getListaSelectItemPais() {
        if (listaSelectItemPais == null) {
            listaSelectItemPais = new ArrayList<SelectItem>();
        }
        return listaSelectItemPais;
    }

    public void setListaSelectItemPais(List<SelectItem> listaSelectItemPais) {
        this.listaSelectItemPais = listaSelectItemPais;
    }

    public List<SelectItem> getListaSelectItemEstado() {
        if (listaSelectItemEstado == null) {
            listaSelectItemEstado = new ArrayList<SelectItem>();
        }
        return listaSelectItemEstado;
    }

    public void setListaSelectItemEstado(List<SelectItem> listaSelectItemEstado) {
        this.listaSelectItemEstado = listaSelectItemEstado;
    }

    public List<SelectItem> getListaSelectItemCidade() {
        if (listaSelectItemCidade == null) {
            listaSelectItemCidade = new ArrayList<SelectItem>();
        }
        return listaSelectItemCidade;
    }

    public void setListaSelectItemCidade(List<SelectItem> listaSelectItemCidade) {
        this.listaSelectItemCidade = listaSelectItemCidade;
    }

    public void montarListaPais() {
        try {
            setListaSelectItemPais(new ArrayList<SelectItem>());
            setListaSelectItemEstado(new ArrayList<SelectItem>());
            setListaSelectItemCidade(new ArrayList<SelectItem>());
            List<SelectItem> objs = new ArrayList<SelectItem>();
            List<PaisVO> lista = getFacade().getPais().consultarPorNome("", false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            Ordenacao.ordenarLista(lista, "nome");
            objs.add(new SelectItem(0, ""));
            for (PaisVO obj : lista) {
                objs.add(new SelectItem(obj.getCodigo(), obj.getNome().toUpperCase()));
            }
            setListaSelectItemPais(objs);
        } catch (Exception ex) {
            setListaSelectItemPais(new ArrayList<SelectItem>());
        }
    }

    public void montarListaEstado() {
        try {
            setListaSelectItemEstado(new ArrayList<SelectItem>());
            setListaSelectItemCidade(new ArrayList<SelectItem>());
            List<SelectItem> objs = new ArrayList<SelectItem>();
            List<EstadoVO> lista = getFacade().getEstado().consultarEstados(getConfiguracaoNotaFiscalVO().getPaisVO().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            Ordenacao.ordenarLista(lista, "descricao");
            objs.add(new SelectItem(0, ""));
            for (EstadoVO obj : lista) {
                objs.add(new SelectItem(obj.getCodigo(), obj.getDescricao().toUpperCase()));
            }
            setListaSelectItemEstado(objs);
        } catch (Exception ex) {
            setListaSelectItemEstado(new ArrayList<SelectItem>());
        }
    }

    public void montarListaCidade() {
        try {
            setListaSelectItemCidade(new ArrayList<SelectItem>());
            List<SelectItem> objs = new ArrayList<SelectItem>();
            List<CidadeVO> lista = getFacade().getCidade().consultarPorCodigoEstado(getConfiguracaoNotaFiscalVO().getEstadoVO().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            Ordenacao.ordenarLista(lista, "nome");
            objs.add(new SelectItem(0, ""));
            for (CidadeVO obj : lista) {
                objs.add(new SelectItem(obj.getCodigo(), obj.getNome().toUpperCase()));
            }
            setListaSelectItemCidade(objs);
        } catch (Exception ex) {
            setListaSelectItemCidade(new ArrayList<SelectItem>());
        }
    }

    public List<SelectItem> getListaAmbienteEmissao() {
        List<SelectItem> itens = new ArrayList<SelectItem>();
        for (AmbienteEmissaoNotaFiscalEnum tipo : AmbienteEmissaoNotaFiscalEnum.values()) {
            itens.add(new SelectItem(tipo, tipo.getDescricao()));
        }
        return itens;
    }

    public void consultarCEP() {
        try {
            String cep = Uteis.removerMascara(getConfiguracaoNotaFiscalVO().getCep());
            if (UteisValidacao.emptyString(cep) || cep.length() < 8) {
                return;
            }
            CepVO obj = new Cep().consultarPorNumeroCep(cep, Uteis.NIVELMONTARDADOS_TODOS);
            getConfiguracaoNotaFiscalVO().setBairro(obj.getBairroDescricao().trim());
            getConfiguracaoNotaFiscalVO().setLogradouro(obj.getEnderecoLogradouro().trim());
            getConfiguracaoNotaFiscalVO().setComplemento(obj.getEnderecoCompleto().trim());
            CidadeVO objCidade = getFacade().getCidade().consultarPorNome(Uteis.retirarAcentuacao(obj.getCidadeDescricao().trim()), Uteis.NIVELMONTARDADOS_TODOS);
            if (objCidade != null) {
                getConfiguracaoNotaFiscalVO().setPaisVO(objCidade.getPais());
                montarListaEstado();
                getConfiguracaoNotaFiscalVO().setEstadoVO(objCidade.getEstado());
                montarListaCidade();
                getConfiguracaoNotaFiscalVO().setCidadeVO(objCidade);
            } else {
                objCidade = new CidadeVO();
                PaisVO objPais = getFacade().getPais().consultarPorNome("Brasil", Uteis.NIVELMONTARDADOS_TODOS);
                if (objPais == null) {
                    throw new Exception("O pais de nome Brasil deve ser cadastrado.");
                }
                EstadoVO objEstado = new Estado().consultarPorSiglaDescricaoEPais(obj.getUfSigla().trim(), obj.getUfDescricao().trim(), objPais.getCodigo(), false, Uteis.NIVELMONTARDADOS_TODOS);
                if (objEstado == null) {
                    objEstado = new EstadoVO();
                    objEstado.setDescricao(obj.getUfDescricao());
                    objEstado.setSigla(obj.getUfSigla());
                    objEstado.setPais(objPais.getCodigo());
                    new Estado().incluir(objEstado);
                }
                objCidade.setPais(objPais);
                objCidade.setEstado(objEstado);
                objCidade.setNome(obj.getCidadeDescricao().trim());
                getFacade().getCidade().incluir(objCidade);
                consultarCEP();
            }
            setMensagemDetalhada("");
        } catch (Exception e) {
            getConfiguracaoNotaFiscalVO().setBairro("");
            getConfiguracaoNotaFiscalVO().setLogradouro("");
            getConfiguracaoNotaFiscalVO().setComplemento("");
            getConfiguracaoNotaFiscalVO().setPaisVO(new PaisVO());
            getConfiguracaoNotaFiscalVO().setEstadoVO(new EstadoVO());
            getConfiguracaoNotaFiscalVO().setCidadeVO(new CidadeVO());
            montarListaEstado();
            montarListaCidade();
            montarErro(e.getMessage());
        }
    }

    public void clonar() {
        limparMsg();
        try {
            configuracaoNotaFiscalVO = (ConfiguracaoNotaFiscalVO) configuracaoNotaFiscalVO.getClone(false);
            configuracaoNotaFiscalVO.setObjetoVOAntesAlteracao(new ConfiguracaoNotaFiscalVO());
            configuracaoNotaFiscalVO.setCodigo(0);
            configuracaoNotaFiscalVO.setNovoObj(true);
            configuracaoNotaFiscalVO.setDescricao("CÓPIA - " + getConfiguracaoNotaFiscalVO().getDescricao());
            configuracaoNotaFiscalVO.getConfigHomologacaoVO().setCodigo(0);
            configuracaoNotaFiscalVO.getConfigHomologacaoVO().setNovoObj(true);
            configuracaoNotaFiscalVO.getConfigProducaoVO().setCodigo(0);
            configuracaoNotaFiscalVO.getConfigProducaoVO().setNovoObj(true);
            montarSucessoGrowl("Dados clonados com sucesso!");
        } catch (Exception ex) {
            Logger.getLogger(ConfiguracaoNotaFiscalControle.class.getName()).log(Level.SEVERE, null, ex);
            montarErro(ex);
        }
    }

    public ConfiguracaoNotaFiscalVO getConfiguracaoNotaFiscalVO() {
        if (configuracaoNotaFiscalVO == null) {
            configuracaoNotaFiscalVO = new ConfiguracaoNotaFiscalVO();
        }
        return configuracaoNotaFiscalVO;
    }

    public void setConfiguracaoNotaFiscalVO(ConfiguracaoNotaFiscalVO configuracaoNotaFiscalVO) {
        this.configuracaoNotaFiscalVO = configuracaoNotaFiscalVO;
    }

    public File getArquivo() {
        return arquivo;
    }

    public void setArquivo(File arquivo) {
        this.arquivo = arquivo;
    }

    public String getExtensaoArquivo() {
        return extensaoArquivo;
    }

    public void setExtensaoArquivo(String extensaoArquivo) {
        this.extensaoArquivo = extensaoArquivo;
    }

    public String getNomeArquivo() {
        return nomeArquivo;
    }

    public void setNomeArquivo(String nomeArquivo) {
        this.nomeArquivo = nomeArquivo;
    }

    public String getOnCompleteArquivo() {
        if (onCompleteArquivo == null) {
            onCompleteArquivo = "";
        }
        return onCompleteArquivo;
    }

    public void setOnCompleteArquivo(String onCompleteArquivo) {
        this.onCompleteArquivo = onCompleteArquivo;
    }

    public void upload(UploadEvent upload) {
        limparMsg();
        setOnCompleteArquivo("");
        try {
            UploadItem item = upload.getUploadItem();
            if (item.getFile().length() > 1024000) {
                setSucesso(false);
                setErro(true);
                setMensagemDetalhada("Arquivo tem tamanho superior a 1Megabyte");
                throw new ConsistirException("Tamanho Superior a 1Megabyte");
            }

            setArquivo(item.getFile());
            setNomeArquivo(item.getFileName());
            String[] partes = item.getFileName().split("\\.");
            setExtensaoArquivo("." + partes[partes.length - 1]);

            if (getArquivo() == null) {
                throw new Exception("Erro ao salvar certificado, repita a operação.");
            }

            if (!getExtensaoArquivo().toLowerCase().equals(".pfx") && !getExtensaoArquivo().toLowerCase().equals(".p12")) {
                throw new Exception("Somente arquivos no formado .PFX e .P12 são aceitos");
            }

            String chaveArquivo = MidiaService.getInstance().uploadObjectWithExtension(getKey(), MidiaEntidadeEnum.NOTAFISCAL_CERTIFICADO,
                    "CONFIG_NOTA_CERTIFICADO_" + getConfiguracaoNotaFiscalVO().getCodigo().toString(),
                    getArquivo(), getExtensaoArquivo());
            getConfiguracaoNotaFiscalVO().setFormatoCertificado(getExtensaoArquivo());
            getConfiguracaoNotaFiscalVO().setChaveCertificado(chaveArquivo);
            getConfiguracaoNotaFiscalVO().setCaminhoCertificadoTemp(item.getFile().toString());

            montarInfo("Clique em gravar para salvar o certificado.");
        } catch (Exception e) {
            setArquivo(null);
            montarErro(e.getMessage());
        }
        setOnCompleteArquivo(getMensagemNotificar());
    }

    public void limparArquivo() {
        setOnCompleteArquivo("");
        setArquivo(null);
        getConfiguracaoNotaFiscalVO().setFormatoCertificado("");
        getConfiguracaoNotaFiscalVO().setChaveCertificado("");
        montarInfo("Clique em gravar para salvar a operação.");
        setOnCompleteArquivo(getMensagemNotificar());
    }

    public File getArquivoLogo() {
        return arquivoLogo;
    }

    public void setArquivoLogo(File arquivoLogo) {
        this.arquivoLogo = arquivoLogo;
    }

    public String getExtensaoArquivoLogo() {
        return extensaoArquivoLogo;
    }

    public void setExtensaoArquivoLogo(String extensaoArquivoLogo) {
        this.extensaoArquivoLogo = extensaoArquivoLogo;
    }

    public String getNomeArquivoLogo() {
        return nomeArquivoLogo;
    }

    public void setNomeArquivoLogo(String nomeArquivoLogo) {
        this.nomeArquivoLogo = nomeArquivoLogo;
    }

    public String getOnCompleteArquivoLogo() {
        if (onCompleteArquivoLogo == null) {
            onCompleteArquivoLogo = "";
        }
        return onCompleteArquivoLogo;
    }

    public void setOnCompleteArquivoLogo(String onCompleteArquivoLogo) {
        this.onCompleteArquivoLogo = onCompleteArquivoLogo;
    }

    public void uploadLogo(UploadEvent upload) {
        limparMsg();
        setOnCompleteArquivoLogo("");
        apresentarTamanhoLogo = false;
        try {
            UploadItem item = upload.getUploadItem();
            if (item.getFile().length() > 1024000) {
                setSucesso(false);
                setErro(true);
                setMensagemDetalhada("Arquivo tem tamanho superior a 1Megabyte");
                throw new ConsistirException("Tamanho Superior a 1Megabyte");
            }

            setArquivoLogo(item.getFile());
            setNomeArquivoLogo(item.getFileName());
            String[] partes = item.getFileName().split("\\.");
            setExtensaoArquivoLogo("." + partes[partes.length - 1]);

            if (getArquivoLogo() == null) {
                throw new Exception("Erro ao salvar logotipo, repita a operação.");
            }

            if (!getExtensaoArquivoLogo().toLowerCase().equals(".png") && !getExtensaoArquivoLogo().toLowerCase().equals(".jpg")) {
                throw new Exception("Somente arquivos no formado .PNG e .JPG são aceitos");
            }

            String chave = MidiaService.getInstance().uploadObjectWithExtension(getKey(), MidiaEntidadeEnum.NOTAFISCAL_LOGOTIPO,
                    "CONFIG_NOTA_LOGOTIPO_" + getConfiguracaoNotaFiscalVO().getCodigo().toString(),
                    getArquivoLogo(), getExtensaoArquivoLogo());
            getConfiguracaoNotaFiscalVO().setFormatoLogotipo(getExtensaoArquivoLogo());
            getConfiguracaoNotaFiscalVO().setChaveLogotipo(chave);

            BufferedImage outImage = ImageIO.read(item.getFile());
            alturaLogo = String.valueOf(outImage.getHeight());
            larguraLogo = String.valueOf(outImage.getWidth());
            apresentarTamanhoLogo = true;

            montarInfo("Clique em gravar para salvar a logotipo.");
        } catch (Exception e) {
            setArquivoLogo(null);
            montarErro(e.getMessage());
        }
        setOnCompleteArquivoLogo(getMensagemNotificar());
    }

    public void limparArquivoLogo() {
        setOnCompleteArquivoLogo("");
        setArquivoLogo(null);
        getConfiguracaoNotaFiscalVO().setFormatoLogotipo("");
        getConfiguracaoNotaFiscalVO().setChaveLogotipo("");
        montarInfo("Clique em gravar para salvar a operação.");
        setOnCompleteArquivoLogo(getMensagemNotificar());
    }

    public void paintLogotipo(OutputStream out, Object data) throws Exception {
        if (getConfiguracaoNotaFiscalVO().getChaveLogotipo() == null && isFotosNaNuvem()) {
            try {
                getConfiguracaoNotaFiscalVO().setLogotipoByte(getFacade().getEmpresa().obterFoto(getKey(), getConfiguracaoNotaFiscalVO().getCodigo(), MidiaEntidadeEnum.NOTAFISCAL_LOGOTIPO));
            } catch (Exception e) {
                Uteis.logar(e, EmpresaControle.class);
            }
        }
        SuperControle.paintFotoEmpresa(out, getConfiguracaoNotaFiscalVO().getLogotipoByte());
    }

    public List<SelectItem> getListaRegimeEspecialTributacao() {
        List<SelectItem> lista = new ArrayList<SelectItem>();
        for (CRTEnum crt : CRTEnum.values()) {
            lista.add(new SelectItem(crt.getCodigo(), crt.getDescricao()));
        }
        return lista;
    }

    public String getInfoEnotas() {
        if (infoEnotas == null) {
            infoEnotas = "";
        }
        return infoEnotas;
    }

    public void setInfoEnotas(String infoEnotas) {
        this.infoEnotas = infoEnotas;
    }

    public String getOnCompleteEnotas() {
        if (onCompleteEnotas == null) {
            onCompleteEnotas = "";
        }
        return onCompleteEnotas;
    }

    public void setOnCompleteEnotas(String onCompleteEnotas) {
        this.onCompleteEnotas = onCompleteEnotas;
    }

    public JSONObject getInfoEnotasJSON() {
        return infoEnotasJSON;
    }

    public void setInfoEnotasJSON(JSONObject infoEnotasJSON) {
        this.infoEnotasJSON = infoEnotasJSON;
    }

    public void preencherCodigoIBGE() {
        try {
            limparMsg();
            getFacade().getEstado().preencherCodigosIBGEEstado();
            getFacade().getCidade().preencherCodigosIBGECidade();
            montarSucessoGrowl("Estados e Cidades processados com sucesso!");
        } catch (Exception e) {
            montarErro(e);
        }
    }

    public void obterInformacoesEmpresa() {
        try {
            limparMsg();
            if (UteisValidacao.emptyNumber(getConfiguracaoNotaFiscalVO().getEmpresaVO().getCodigo())) {
                throw new Exception("Selecione uma empresa.");
            }


            EmpresaVO empresaVO = getFacade().getEmpresa().consultarPorChavePrimaria(getConfiguracaoNotaFiscalVO().getEmpresaVO().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);

            getConfiguracaoNotaFiscalVO().setNomeFantasia(empresaVO.getNome());
            getConfiguracaoNotaFiscalVO().setRazaoSocial(empresaVO.getRazaoSocial());
            getConfiguracaoNotaFiscalVO().setCnpj(Uteis.aplicarMascara(Uteis.removerMascara(empresaVO.getCNPJ()), "99.999.999/9999-99"));
            getConfiguracaoNotaFiscalVO().setInscricaoMunicipal(empresaVO.getInscMunicipal());
            getConfiguracaoNotaFiscalVO().setInscricaoEstadual(empresaVO.getInscEstadual());

            if (!UteisValidacao.emptyString(empresaVO.getTelComercial1())){
                getConfiguracaoNotaFiscalVO().setTelefoneComercial(empresaVO.getTelComercial1());
            } else if (!UteisValidacao.emptyString(empresaVO.getTelComercial2())){
                getConfiguracaoNotaFiscalVO().setTelefoneComercial(empresaVO.getTelComercial2());
            } else if (!UteisValidacao.emptyString(empresaVO.getTelComercial3())){
                getConfiguracaoNotaFiscalVO().setTelefoneComercial(empresaVO.getTelComercial3());
            }

            getConfiguracaoNotaFiscalVO().setEmail(empresaVO.getEmail());
            getConfiguracaoNotaFiscalVO().setCep(empresaVO.getCEP());
            getConfiguracaoNotaFiscalVO().setLogradouro(empresaVO.getEndereco());
            getConfiguracaoNotaFiscalVO().setNumero(empresaVO.getNumero());
            getConfiguracaoNotaFiscalVO().setComplemento(empresaVO.getComplemento());
            getConfiguracaoNotaFiscalVO().setBairro(empresaVO.getSetor());
            getConfiguracaoNotaFiscalVO().setPaisVO(empresaVO.getPais());
            getConfiguracaoNotaFiscalVO().setEstadoVO(empresaVO.getEstado());
            getConfiguracaoNotaFiscalVO().setCidadeVO(empresaVO.getCidade());

            montarListaPais();
            montarListaEstado();
            montarListaCidade();

            montarSucessoGrowl("Informações da empresa carregado.");
        } catch (Exception e) {
            montarErro(e);
        }
    }

    public InfoCidadeEnotasTO getInfoCidadeEnotasTO() {
        if (infoCidadeEnotasTO == null) {
            infoCidadeEnotasTO = new InfoCidadeEnotasTO();
        }
        return infoCidadeEnotasTO;
    }

    public void setInfoCidadeEnotasTO(InfoCidadeEnotasTO infoCidadeEnotasTO) {
        this.infoCidadeEnotasTO = infoCidadeEnotasTO;
    }

    public List<InfoServicoEnotasTO> getListaInfoServicoTO() {
        if (listaInfoServicoTO == null) {
            listaInfoServicoTO = new ArrayList<>();
        }
        return listaInfoServicoTO;
    }

    public void setListaInfoServicoTO(List<InfoServicoEnotasTO> listaInfoServicoTO) {
        this.listaInfoServicoTO = listaInfoServicoTO;
    }

    public void enviarCertificadoEnotas() {
        try {
            limparMsg();
            montarSucessoGrowl(getFacade().getConfiguracaoNotaFiscal().enviarCertificadoEnotas(getKey(), configuracaoNotaFiscalVO));
        } catch (Exception e) {
            montarErro(e);
        }
    }

    public void enviarLogotipoEnotas() {
        try {
            limparMsg();
            montarSucessoGrowl(getFacade().getConfiguracaoNotaFiscal().enviarLogotipoEnotas(getKey(), configuracaoNotaFiscalVO));
        } catch (Exception e) {
            montarErro(e);
        }
    }

    public void enviarEmpresaComConfiguracoesAmbiente() {
        try {
            limparMsg();
            String enotas = getFacade().getConfiguracaoNotaFiscal().sincronizarEnotas(getKey(), true, configuracaoNotaFiscalVO);
            montarSucessoGrowl(enotas);
        } catch (Exception e) {
            montarErro(e);
        }
    }

    public InfoEmpresaEnotasTO getInfoEmpresaEnotasTO() {
        return infoEmpresaEnotasTO;
    }

    public void setInfoEmpresaEnotasTO(InfoEmpresaEnotasTO infoEmpresaEnotasTO) {
        this.infoEmpresaEnotasTO = infoEmpresaEnotasTO;
    }

    public void consultarSituacaoEmpresaEnotas() {
        try {
            limparMsg();
            setInfoEnotas("");
            setOnCompleteEnotas("");
            setInfoEmpresaEnotasTO(getFacade().getConfiguracaoNotaFiscal().consultarSituacaoEmpresaEnotas(configuracaoNotaFiscalVO));
            setOnCompleteEnotas("Richfaces.showModalPanel('modalInfoEmpresa');");
        } catch (Exception e) {
            montarErro(e);
        }
    }

    public void obterLinkDownloadSetupSAT() {
        try {
            limparMsg();
            setInfoEnotas("");
            setOnCompleteEnotas("");
            String link = getFacade().getConfiguracaoNotaFiscal().obterLinkDownloadSetupSAT(configuracaoNotaFiscalVO);
            setOnCompleteEnotas("location.href='" + link + "'");
        } catch (Exception e) {
            montarErro(e);
        }
    }

    public boolean isApresentaSetupSAT() {
        try {
            if (UteisValidacao.emptyString(getConfiguracaoNotaFiscalVO().getIdEnotas())) {
                return false;
            }

            for (SelectItem item : getListaSelectItemCidade()) {
                if (item.getValue().equals(getConfiguracaoNotaFiscalVO().getCidadeVO().getCodigo()) && Uteis.retirarAcentuacao(item.getLabel()).equals("SAO PAULO")) {
                    return true;
                }
            }
             return false;
        } catch (Exception ex) {
            return false;
        }
    }

    public String getPaintFotoDaNuvem() {
        try {
            return getPaintFotoDaNuvem(getConfiguracaoNotaFiscalVO().getChaveLogotipo());
        } catch (Exception e) {
            return String.format("%s/%s",
                    PropsService.getPropertyValue(PropsService.urlFotosNuvem),
                    "fotoPadrao.jpg");
        }
    }

    public boolean isApresentarTamanhoLogo() {
        return apresentarTamanhoLogo;
    }

    public void setApresentarTamanhoLogo(boolean apresentarTamanhoLogo) {
        this.apresentarTamanhoLogo = apresentarTamanhoLogo;
    }

    public String getAlturaLogo() {
        return alturaLogo;
    }

    public void setAlturaLogo(String alturaLogo) {
        this.alturaLogo = alturaLogo;
    }

    public String getLarguraLogo() {
        return larguraLogo;
    }

    public void setLarguraLogo(String larguraLogo) {
        this.larguraLogo = larguraLogo;
    }

    public String getTipo() {
        if (tipo == null) {
            tipo = "AT";
        }
        return tipo;
    }

    public void setTipo(String tipo) {
        this.tipo = tipo;
    }

    public List<SelectItem> getListaTipo() {
        if(UteisValidacao.emptyList(listaTipo)){
            listaTipo = new ArrayList<SelectItem>();
            this.listaTipo.add(new SelectItem("AT",  "Ativos"));
            this.listaTipo.add(new SelectItem("IN", "Inativos"));
            this.listaTipo.add(new SelectItem("TD",  "Todos"));
        }
        return listaTipo;
    }

    public void setListaTipo(List<SelectItem> listaTipo) {
        this.listaTipo = listaTipo;
    }

    private void validarCnpjConfig() throws  Exception,ConsistirException{
        if(!usuarioLogadoPodeCadastrarConfigNotaCnpjDiferente() && alterandoCnpj() &&
               !Uteis.removerMascara(getConfiguracaoNotaFiscalVO().getEmpresaVO().getCNPJ()).equals(Uteis.removerMascara(getConfiguracaoNotaFiscalVO().getCnpj()))){
            throw new ConsistirException("Seu usuário não tem permissão para cadastrar CNPJ (Configuração Emissão) diferente da empresa informada.", TipoExceptionEnum.ALERTA);
        }
    }

    private Boolean alterandoCnpj(){
       return getConfiguracaoNotaFiscalVO().isNovoObj() || !getConfiguracaoNotaFiscalVO().isNovoObj() && getConfiguracaoNotaFiscalVO().getObjetoVOAntesAlteracao() != null &&
                !Uteis.removerMascara(((ConfiguracaoNotaFiscalVO) getConfiguracaoNotaFiscalVO().getObjetoVOAntesAlteracao()).getCnpj()).equals(Uteis.removerMascara(Uteis.removerMascara(getConfiguracaoNotaFiscalVO().getCnpj())));
    }


    private Boolean usuarioLogadoPodeCadastrarConfigNotaCnpjDiferente() throws Exception,ConsistirException {
        return !UteisValidacao.emptyString(getUsuarioLogado().getUserOamd().trim())
                && !getUsuarioLogado().getUserOamd().equalsIgnoreCase("undefined")
                && getUsuarioLogado().isUsuarioLogadoPodeCadastrarConfigNotaCnpjDiferente();
    }

}

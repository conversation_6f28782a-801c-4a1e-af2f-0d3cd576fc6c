package controle.basico;

import acesso.webservice.AcessoControle;
import acesso.webservice.DaoAuxiliar;
import br.com.pactosolucoes.comuns.util.JSFUtilities;
import controle.arquitetura.SuperControle;
import negocio.comuns.acesso.AcessoClienteVO;
import negocio.comuns.acesso.ColetorVO;
import negocio.comuns.acesso.LocalAcessoVO;
import negocio.comuns.acesso.enumerador.DirecaoAcessoEnum;
import negocio.comuns.acesso.enumerador.MeioIdentificacaoEnum;
import negocio.comuns.acesso.enumerador.SituacaoAcessoEnum;
import negocio.comuns.arquitetura.LogVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.ConfiguracaoSistemaVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import relatorio.negocio.comuns.sad.SituacaoClienteSinteticoEnum;
import servicos.integracao.TreinoWSConsumer;

import javax.faces.model.SelectItem;
import java.io.IOException;
import java.io.OutputStream;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

/*
 * Luiz Felipe
 */
public class RegistrarAcessoAvulsoControle extends SuperControle {

    private Date dataAcesso;
    private String horaEntradaRegistroAcesso;
    private String horaSaidaRegistroAcesso;
    private ClienteVO clienteVO;
    private List<LocalAcessoVO> listaLocalAcesso;
    private List<ColetorVO> listaColetor;
    private LocalAcessoVO localAcessoVO;
    private ColetorVO coletorVO;
    private EmpresaVO empresaSelecionada;
    private boolean registrarSaida = true;
    private List<AcessoClienteVO> listaAcessosAdicionados;
    private boolean telaCliente = false;
    private String onComplete;
    private Date dataInicialFiltrar;
    private Date dataFinalFiltrar;
    private boolean buscaPorMatricula = false;
    private List<AcessoClienteVO> acessosNoDia;
    private boolean alterarAcesso = false;
    private AcessoClienteVO acessoClienteAlterar;

    public RegistrarAcessoAvulsoControle() throws Exception {
        inicializarDados();
    }

    public void inicializarDados() throws Exception {
        limparMsg();
        setTelaCliente(false);
        setClienteVO(new ClienteVO());
        setHoraEntradaRegistroAcesso(Calendario.getData(Calendario.hoje(), "HH:mm"));
        setHoraSaidaRegistroAcesso(Calendario.getData(Uteis.somarCampoData(Calendario.hoje(), Calendar.HOUR, 1), "HH:mm"));
        setDataAcesso(Calendario.hoje());
        setDataInicialFiltrar(Calendario.hoje());
        setDataFinalFiltrar(Calendario.hoje());
        montarListaEmpresas();
        obterUsuarioLogado();
        empresaSelecionada = new EmpresaVO();
        montarListaLocalAcesso();
        setAcessosNoDia(new ArrayList<AcessoClienteVO>());
    }

    public String getHoraEntradaRegistroAcesso() {
        if (horaEntradaRegistroAcesso == null) {
            horaEntradaRegistroAcesso = "";
        }
        return horaEntradaRegistroAcesso;
    }

    public void setHoraEntradaRegistroAcesso(String horaEntradaRegistroAcesso) {
        this.horaEntradaRegistroAcesso = horaEntradaRegistroAcesso;
    }

    public String getHoraSaidaRegistroAcesso() {
        if (horaSaidaRegistroAcesso == null) {
            horaSaidaRegistroAcesso = "";
        }
        return horaSaidaRegistroAcesso;
    }

    public void setHoraSaidaRegistroAcesso(String horaSaidaRegistroAcesso) {
        this.horaSaidaRegistroAcesso = horaSaidaRegistroAcesso;
    }

    public ClienteVO getClienteVO() {
        if (clienteVO == null) {
            clienteVO = new ClienteVO();
        }
        return clienteVO;
    }

    public void setClienteVO(ClienteVO clienteVO) {
        this.clienteVO = clienteVO;
    }

    public List<ClienteVO> executarAutocompleteCliente(Object suggest) {
        String pref = (String) suggest;
        ArrayList<ClienteVO> result = new ArrayList<ClienteVO>();
        try {
            if (pref.equals("%")) {
                result = (ArrayList<ClienteVO>) getFacade().getCliente().consultarTodosClienteComLimite(getEmpresaLogado().getCodigo(), false, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            } else {
                result = (ArrayList<ClienteVO>) getFacade().getCliente().consultarClienteFuncionalidade((String) JSFUtilities.getFromSession(JSFUtilities.KEY),
                        pref, getEmpresaLogado().getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            }
        } catch (Exception ex) {
            result = (new ArrayList<ClienteVO>());
        }
        return result;
    }

    public void selecionarClienteSuggestionBox() throws Exception {
        setOnComplete("");
        ClienteVO clienteVO = (ClienteVO) request().getAttribute("cliente");
        if (clienteVO != null) {
            clienteVO = getFacade().getCliente().consultarPorCodigo(clienteVO.getCodigo(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            setClienteVO(clienteVO);
            setBuscaPorMatricula(false);
            setOnComplete("document.getElementById('form:dataAcessoInputDate').focus();");
        } else {
            setOnComplete("document.getElementById('form:clienteOUColaboradorIndicou').focus();");
        }
    }

    public void limparCampoClienteSuggestion() {
        setClienteVO(new ClienteVO());
    }

    public Date getDataAcesso() {
        return dataAcesso;
    }

    public void setDataAcesso(Date dataAcesso) {
        this.dataAcesso = dataAcesso;
    }

    public void montarListaLocalAcesso() throws Exception {
        setColetorVO(new ColetorVO());
        setLocalAcessoVO(new LocalAcessoVO());
        setListaLocalAcesso(getFacade().getLocalAcesso().consultarPorEmpresa(getEmpresaLogado().getCodigo(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS));
        if (getListaLocalAcesso().size() >= 1) {
            setLocalAcessoVO((LocalAcessoVO) getListaLocalAcesso().get(0).getClone(true));
            montarListaColetor();
        }
    }
    public void selecionarLocalAcesso() throws Exception {
        for (LocalAcessoVO localAcessoVO : getListaLocalAcesso()) {
            if(localAcessoVO.getCodigo().equals(getLocalAcessoVO().getCodigo())) {
                setLocalAcessoVO((LocalAcessoVO) localAcessoVO.getClone(true));
                break;
            }
        }
        montarListaColetor();
    }

    public void montarListaColetor() throws Exception {
        setListaColetor(getFacade().getColetor().consultarColetores(getLocalAcessoVO().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
        if (getListaColetor().size() >= 1) {
            setColetorVO(getListaColetor().get(0));
        }
    }

    public List<SelectItem> getSelectItemLocalAcesso() {
        List<SelectItem> retorno = new ArrayList<SelectItem>();
        for (LocalAcessoVO localAcessoVO : getListaLocalAcesso()) {
            retorno.add(new SelectItem(localAcessoVO.getCodigo(), localAcessoVO.getDescricao()));
        }
        return retorno;
    }

    public List<SelectItem> getSelectItemColetor() {
        List<SelectItem> retorno = new ArrayList<SelectItem>();
        for (ColetorVO coletorVO : getListaColetor()) {
            retorno.add(new SelectItem(coletorVO.getCodigo(), coletorVO.getDescricao()));
        }
        return retorno;
    }

    public List<LocalAcessoVO> getListaLocalAcesso() {
        if (listaLocalAcesso == null) {
            listaLocalAcesso = new ArrayList<LocalAcessoVO>();
        }
        return listaLocalAcesso;
    }

    public void setListaLocalAcesso(List<LocalAcessoVO> listaLocalAcesso) {
        this.listaLocalAcesso = listaLocalAcesso;
    }

    public List<ColetorVO> getListaColetor() {
        if (listaColetor == null) {
            listaColetor = new ArrayList<ColetorVO>();
        }
        return listaColetor;
    }

    public void setListaColetor(List<ColetorVO> listaColetor) {
        this.listaColetor = listaColetor;
    }

    public LocalAcessoVO getLocalAcessoVO() {
        if (localAcessoVO == null) {
            localAcessoVO = new LocalAcessoVO();
        }
        return localAcessoVO;
    }

    public void setLocalAcessoVO(LocalAcessoVO localAcessoVO) {
        this.localAcessoVO = localAcessoVO;
    }

    public ColetorVO getColetorVO() {
        if (coletorVO == null) {
            coletorVO = new ColetorVO();
        }
        return coletorVO;
    }

    public void setColetorVO(ColetorVO coletorVO) {
        this.coletorVO = coletorVO;
    }

    public EmpresaVO getEmpresaSelecionada() {
        return empresaSelecionada;
    }

    public void setEmpresaSelecionada(EmpresaVO empresaSelecionada) {
        this.empresaSelecionada = empresaSelecionada;
    }

    public boolean isRegistrarSaida() {
        return registrarSaida;
    }

    public void setRegistrarSaida(boolean registrarSaida) {
        this.registrarSaida = registrarSaida;
    }

    private void validarDados() throws Exception {

        if (UteisValidacao.emptyNumber(getClienteVO().getCodigo())) {
            setOnComplete("document.getElementById('form:matricula').focus()");
            throw new Exception("Nenhum aluno selecionado");
        }

        if (getDataAcesso() == null) {
            setOnComplete("document.getElementById('form:dataAcessoInputDate').focus()");
            throw new Exception("Informe a data do acesso");
        }

        if (UteisValidacao.emptyString(getHoraEntradaRegistroAcesso())) {
            setOnComplete("document.getElementById('form:horaEntradaRegistroAcesso').focus()");
            throw new Exception("Informe a hora da entrada");
        }

        if (isRegistrarSaida()) {
            if (UteisValidacao.emptyString(getHoraSaidaRegistroAcesso())) {
                setOnComplete("document.getElementById('form:horaSaidaRegistroAcesso').focus()");
                throw new Exception("Informe a hora da saída");
            }

            String[] saida = getHoraSaidaRegistroAcesso().split(":");
            if (Integer.parseInt(saida[0]) > 24) {
                setOnComplete("document.getElementById('form:horaSaidaRegistroAcesso').focus()");
                throw new Exception("Hora da saída inválida");
            }
            if (Integer.parseInt(saida[1]) > 59) {
                setOnComplete("document.getElementById('form:horaSaidaRegistroAcesso').focus()");
                throw new Exception("Minutos da saída inválida");
            }
        }

        if (UteisValidacao.emptyNumber(getLocalAcessoVO().getCodigo())) {
            setOnComplete("document.getElementById('form:localacesso').focus()");
            throw new Exception("Nenhum local de acesso selecionado");
        }

        if (UteisValidacao.emptyNumber(getColetorVO().getCodigo())) {
            setOnComplete("document.getElementById('form:coletor').focus()");
            throw new Exception("Nenhum coletor selecionado");
        }

        String[] entrada = getHoraEntradaRegistroAcesso().split(":");
        if (Integer.parseInt(entrada[0]) > 24) {
            setOnComplete("document.getElementById('form:horaEntradaRegistroAcesso').focus()");
            throw new Exception("Hora da entrada inválida");
        }
        if (Integer.parseInt(entrada[1]) > 59) {
            setOnComplete("document.getElementById('form:horaEntradaRegistroAcesso').focus()");
            throw new Exception("Minutos da entrada inválida");
        }

    }

    public void registrarAcessosSemValidar() {
        registrarAcessos(false);
    }

    public void registrarAcessos() {
        registrarAcessos(true);
    }

    public void registrarAcessos(boolean validarAcessoDia) {
        try {
            if (isAlterarAcesso()) {
                
                limparMsg();
                setOnComplete("");
                validarDados();
                setAcessosNoDia(new ArrayList<AcessoClienteVO>());
                
                String[] entrada = getHoraEntradaRegistroAcesso().split(":");
                if (Calendario.maior(getDataAcesso(), Calendario.hoje())) {
                    throw new Exception("Não é possivel registrar acesso futuro");
                }
                
                Calendar calendarEntrada = Calendario.getInstance(getDataAcesso());
                calendarEntrada.set(Calendar.HOUR, Integer.parseInt(entrada[0]));
                calendarEntrada.set(Calendar.MINUTE, Integer.parseInt(entrada[1]));
                Date dataGravarEntrada = calendarEntrada.getTime();
                
                getAcessoClienteAlterar().setDataHoraEntrada(dataGravarEntrada);
                
                if (isRegistrarSaida()) {
                    
                    String[] saida = getHoraSaidaRegistroAcesso().split(":");
                    Calendar calendarSaida = Calendario.getInstance(getDataAcesso());
                    calendarSaida.set(Calendar.HOUR, Integer.parseInt(saida[0]));
                    calendarSaida.set(Calendar.MINUTE, Integer.parseInt(saida[1]));
                    Date dataGravarSaida = calendarSaida.getTime();
                    
                    if (Calendario.menorComHora(dataGravarSaida, dataGravarEntrada)) {
                        throw new Exception("A hora de saida deve ser maior que a entrada");
                    }
                    
                    getAcessoClienteAlterar().setDataHoraSaida(dataGravarSaida);
                    getFacade().getSituacaoClienteSinteticoDW().registrarUltimoAcessoSaida(getClienteVO().getCodigo(), dataGravarSaida);
                }
                
                getFacade().getAcessoCliente().alterarAcessoEntradaSaida(getAcessoClienteAlterar());
                
                setAlterarAcesso(false);
                
                AcessoClienteVO ultimoAcesso = getFacade().getAcessoCliente().consultarUltimoAcesso(getClienteVO(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);

                if (!UteisValidacao.emptyNumber(ultimoAcesso.getCodigo())) {
                    getFacade().getCliente().registrarUltimoAcesso(getClienteVO().getCodigo(), ultimoAcesso.getCodigo());
                }
                TreinoWSConsumer.atualizarStatusAluno(getKey(),getClienteVO().getCodigo(), dataGravarEntrada, localAcessoVO.getEmpresa().getCodigo());
                getFacade().getSituacaoClienteSinteticoDW().registrarUltimoAcesso(getClienteVO().getCodigo(), dataGravarEntrada);
                getFacade().getZWFacade().atualizarSintetico(getClienteVO(), Calendario.hoje(), SituacaoClienteSinteticoEnum.GRUPO_DADOSACESSO, false);

                //REGISTRAR LOG INCLUSAO
                try {
                    LogVO log = new LogVO();
                    log.setChavePrimaria(getClienteVO().getPessoa().getCodigo().toString());
                    log.setNomeEntidade("CLIENTE - ACESSO MANUAL");
                    log.setNomeEntidadeDescricao("Cliente - Acesso Manual");
                    log.setOperacao("ALTERAR - REGISTRO ACESSO MANUAL");
                    log.setResponsavelAlteracao(getUsuarioLogado().getNome());
                    log.setUserOAMD(getUsuarioLogado().getUserOamd());
                    log.setNomeCampo("AcessoManual-Cliente");
                    log.setValorCampoAnterior("");
                    StringBuilder campoAlterado = new StringBuilder();
                    if (getAcessoClienteAlterar() != null && getAcessoClienteAlterar().getCodigo() != null) {
                        campoAlterado.append("CÓDIGO ACESSO: " + getAcessoClienteAlterar().getCodigo()).append(" \n");
                    }
                    if (getAcessoClienteAlterar() != null && getAcessoClienteAlterar().getDataHoraEntrada() != null) {
                        campoAlterado.append("ENTRADA: " + Uteis.getDataAplicandoFormatacao(getAcessoClienteAlterar().getDataHoraEntrada(), "dd/MM HH:mm:ss")).append(" \n");
                    }
                    if (getAcessoClienteAlterar() != null && getAcessoClienteAlterar().getDataHoraSaida() != null) {
                        campoAlterado.append("SAÍDA: " + Uteis.getDataAplicandoFormatacao(getAcessoClienteAlterar().getDataHoraSaida(), "dd/MM HH:mm:ss")).append(" \n");
                    }
                    log.setValorCampoAlterado(campoAlterado.toString());
                    log.setDataAlteracao(negocio.comuns.utilitarias.Calendario.hoje());
                    registrarLogObjetoVO(log, getClienteVO().getPessoa().getCodigo());
                } catch (Exception e) {
                    registrarLogErroObjetoVO("CLIENTE - ACESSO MANUAL", getClienteVO().getPessoa().getCodigo(), "ERRO AO REGISTRAR LOG ACESSO MANUAL", this.getUsuarioLogado().getNome(), this.getUsuarioLogado().getUserOamd());
                    e.printStackTrace();
                }
                
                montarSucessoGrowl("Último acesso alterado para MAT "  + getClienteVO().getMatricula() + " - "  + getClienteVO().getNome_Apresentar() + " - " + Uteis.getDataAplicandoFormatacao(getAcessoClienteAlterar().getDataHoraEntrada(), "dd/MM/yyyy HH:mm"));
            
                setOnComplete("Richfaces.hideModalPanel('modalAcessosDia');");
                
                setClienteVO(new ClienteVO());
            }else{
                
                limparMsg();
                setOnComplete("");
                validarDados();
                setAcessosNoDia(new ArrayList<AcessoClienteVO>());

                List<AcessoClienteVO> listaAcessosDia = getFacade().getAcessoCliente().consultarUltimoAcessoDia(getClienteVO(), getDataAcesso(), null, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                if (!UteisValidacao.emptyList(listaAcessosDia) && validarAcessoDia) {
                    setAcessosNoDia(listaAcessosDia);
                    setOnComplete("Richfaces.showModalPanel('modalAcessosDia');document.getElementById('formAce:btnCancelarModal').focus()");
                    return;
                }

                String[] entrada = getHoraEntradaRegistroAcesso().split(":");
                if (Calendario.maior(getDataAcesso(), Calendario.hoje())) {
                    throw new Exception("Não é possivel registrar acesso futuro");
                }
                Calendar calendarEntrada = Calendario.getInstance(getDataAcesso());
                calendarEntrada.set(Calendar.HOUR, Integer.parseInt(entrada[0]));
                calendarEntrada.set(Calendar.MINUTE, Integer.parseInt(entrada[1]));
                Date dataGravarEntrada = calendarEntrada.getTime();

                String[] saida = getHoraSaidaRegistroAcesso().split(":");
                Calendar calendarSaida = Calendario.getInstance(getDataAcesso());
                calendarSaida.set(Calendar.HOUR, Integer.parseInt(saida[0]));
                calendarSaida.set(Calendar.MINUTE, Integer.parseInt(saida[1]));
                Date dataGravarSaida = calendarSaida.getTime();

                AcessoClienteVO acessoEntrada = null;
                AcessoClienteVO acessoSaida = null;

                if (isRegistrarSaida()) {
                    if (Calendario.menorComHora(dataGravarSaida, dataGravarEntrada)) {
                        throw new Exception("A hora de saida deve ser maior que a entrada");
                    }
                }

                acessoEntrada = getFacade().getAcessoCliente().registrarAcessoCliente(
                        dataGravarEntrada, getClienteVO(), SituacaoAcessoEnum.RV_LIBACESSOAUTORIZADO, DirecaoAcessoEnum.DA_ENTRADA, getLocalAcessoVO(), getColetorVO(),
                        getUsuarioLogado(), MeioIdentificacaoEnum.AVULSO, getFacade().getControleCreditoTreino(), (String) JSFUtilities.getFromSession(JSFUtilities.KEY));

                ConfiguracaoSistemaVO configuracaoSistemaVO = getFacade().getConfiguracaoSistema().consultarConfigs(Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                if (configuracaoSistemaVO.isMarcarPresencaPeloAcesso()) {
                    AcessoControle acessoControle = DaoAuxiliar.retornarAcessoControle(getKey());
                    acessoControle.marcarPresenca(getClienteVO(), dataGravarEntrada, getLocalAcessoVO().getEmpresa().getCodigo(), getUsuarioLogado());
                    acessoControle.marcarPresencaAulaCheia(getClienteVO(), dataGravarEntrada, getLocalAcessoVO().getEmpresa().getCodigo(), getUsuarioLogado().getColaboradorVO().getCodigo());

                }

                if (isRegistrarSaida()) {

                    acessoSaida = getFacade().getAcessoCliente().registrarAcessoCliente(
                            dataGravarSaida, getClienteVO(), SituacaoAcessoEnum.RV_LIBACESSOAUTORIZADO, DirecaoAcessoEnum.DA_SAIDA, getLocalAcessoVO(), getColetorVO(),
                            getUsuarioLogado(), MeioIdentificacaoEnum.AVULSO, getFacade().getControleCreditoTreino(), (String) JSFUtilities.getFromSession(JSFUtilities.KEY));
                }

                AcessoClienteVO ultimoAcesso = getFacade().getAcessoCliente().consultarUltimoAcesso(getClienteVO(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);

                if (!UteisValidacao.emptyNumber(ultimoAcesso.getCodigo())) {
                    getFacade().getCliente().registrarUltimoAcesso(getClienteVO().getCodigo(), ultimoAcesso.getCodigo());
                }

                TreinoWSConsumer.atualizarStatusAluno(getKey(),getClienteVO().getCodigo(), dataGravarEntrada, localAcessoVO.getEmpresa().getCodigo());
                getFacade().getSituacaoClienteSinteticoDW().registrarUltimoAcesso(getClienteVO().getCodigo(), dataGravarEntrada);
                if(getFacade().getEmpresa().integracaoMyWellnesHabilitada(localAcessoVO.getEmpresa().getCodigo(), false)){
                    getFacade().getZWFacade().startThreadMyWellness(getClienteVO().getCodigo(), null, true, null);
                }
                getFacade().getZWFacade().atualizarSintetico(getClienteVO(), Calendario.hoje(), SituacaoClienteSinteticoEnum.GRUPO_DADOSACESSO, false);

                if (isRegistrarSaida()) {
                    getFacade().getSituacaoClienteSinteticoDW().registrarUltimoAcessoSaida(getClienteVO().getCodigo(), dataGravarSaida);
                }
                
                
                //REGISTRAR LOG INCLUSAO
                try {
                    LogVO log = new LogVO();
                    log.setChavePrimaria(getClienteVO().getPessoa().getCodigo().toString());
                    log.setNomeEntidade("CLIENTE - ACESSO MANUAL");
                    log.setNomeEntidadeDescricao("Cliente - Acesso Manual");
                    log.setOperacao("INCLUIR - REGISTRO ACESSO MANUAL");
                    log.setResponsavelAlteracao(getUsuarioLogado().getNome());
                    log.setUserOAMD(getUsuarioLogado().getUserOamd());
                    log.setNomeCampo("AcessoManual-Cliente");
                    log.setValorCampoAnterior("");
                    StringBuilder campoAlterado = new StringBuilder();
                    if (acessoEntrada != null && acessoEntrada.getCodigo() != null) {
                        campoAlterado.append("CÓDIGO ACESSO: " + acessoEntrada.getCodigo()).append(" \n");
                    }
                    if (acessoEntrada != null && acessoEntrada.getDataHoraEntrada() != null) {
                        campoAlterado.append("ENTRADA: " + Uteis.getDataAplicandoFormatacao(acessoEntrada.getDataHoraEntrada(), "dd/MM HH:mm:ss")).append(" \n");
                    }
                    if (acessoSaida != null && acessoSaida.getDataHoraSaida() != null) {
                        campoAlterado.append("SAÍDA: " + Uteis.getDataAplicandoFormatacao(acessoSaida.getDataHoraSaida(), "dd/MM HH:mm:ss")).append(" \n");
                    }
                    log.setValorCampoAlterado(campoAlterado.toString());
                    log.setDataAlteracao(negocio.comuns.utilitarias.Calendario.hoje());
                    registrarLogObjetoVO(log, getClienteVO().getPessoa().getCodigo());
                } catch (Exception e) {
                    registrarLogErroObjetoVO("CLIENTE - ACESSO MANUAL", getClienteVO().getPessoa().getCodigo(), "ERRO AO REGISTRAR LOG ACESSO MANUAL", this.getUsuarioLogado().getNome(), this.getUsuarioLogado().getUserOamd());
                    e.printStackTrace();
                }

                montarSucessoGrowl("Último acesso gravado para MAT "  + getClienteVO().getMatricula() + " - "  + getClienteVO().getNome_Apresentar() + " - " + Uteis.getDataAplicandoFormatacao(acessoEntrada.getDataHoraEntrada(), "dd/MM/yyyy HH:mm"));

                if (isTelaCliente()) {
                    setOnComplete("Richfaces.hideModalPanel('modalAcessosDia');");
                } else {
                    setClienteVO(new ClienteVO());
                    if (isBuscaPorMatricula()) {
                        setOnComplete("document.getElementById('form:matricula').focus();Richfaces.hideModalPanel('modalAcessosDia');");
                    } else {
                        setOnComplete("document.getElementById('form:clienteOUColaboradorIndicou').focus();Richfaces.hideModalPanel('modalAcessosDia');");
                    }
                }
                
                setAlterarAcesso(false);
            }
        } catch (Exception e) {
            montarErro(e);
        }
    }

    public void alterarAcesso(){
        
        try {
            
            setOnComplete("");
            
            permissaoFuncionalidade(getUsuarioLogado(), "AlterarAcessoManual", "9.44 - Permitir alterar acesso manual");
            
            AcessoClienteVO acessoClienteVO = (AcessoClienteVO) context().getExternalContext().getRequestMap().get("acesso");

            if (acessoClienteVO.getDataHoraEntrada() != null) {
                setHoraEntradaRegistroAcesso(Uteis.gethoraHHMMAjustado(acessoClienteVO.getDataHoraEntrada()));
            }else{
                setHoraEntradaRegistroAcesso("");
            }

            if (acessoClienteVO.getDataHoraSaida() != null) {
                setHoraSaidaRegistroAcesso(Uteis.gethoraHHMMAjustado(acessoClienteVO.getDataHoraSaida()));
            }else{
                setHoraSaidaRegistroAcesso("");
            }

            setAlterarAcesso(true);

            setAcessoClienteAlterar(acessoClienteVO);
            
            setOnComplete("Richfaces.hideModalPanel('modalAcessosDia')");
        } catch (Exception ex) {
            montarErro(ex);
        }
    }
    
    public void limparCliente() {
        setClienteVO(new ClienteVO());
    }

    public List<AcessoClienteVO> getListaAcessosAdicionados() {
        if (listaAcessosAdicionados == null) {
            listaAcessosAdicionados = new ArrayList<AcessoClienteVO>();
        }
        return listaAcessosAdicionados;
    }

    public void setListaAcessosAdicionados(List<AcessoClienteVO> listaAcessosAdicionados) {
        this.listaAcessosAdicionados = listaAcessosAdicionados;
    }

    public boolean isTelaCliente() {
        return telaCliente;
    }

    public void setTelaCliente(boolean telaCliente) {
        this.telaCliente = telaCliente;
    }

    public String getOnComplete() {
        if (onComplete == null) {
            onComplete = "";
        }
        return onComplete;
    }

    public void setOnComplete(String onComplete) {
        this.onComplete = onComplete;
    }

    public void consultarAcessosLancados() {
        try {
            limparMsg();
            setOnComplete("");
            setListaAcessosAdicionados(new ArrayList<AcessoClienteVO>());

            if (getDataInicialFiltrar() == null) {
                throw new Exception("Informe a data inicial para pesquisar");
            }
            if (getDataFinalFiltrar() == null) {
                throw new Exception("Informe a data final para pesquisar");
            }

            List<AcessoClienteVO> acessos = getFacade().getAcessoCliente().consultarAcessosClienteDataMeioIdentificacao(new ClienteVO(), getDataInicialFiltrar(), getDataFinalFiltrar(), MeioIdentificacaoEnum.AVULSO, Uteis.NIVELMONTARDADOS_TODOS);
            setListaAcessosAdicionados(acessos);
        } catch (Exception e) {
            montarErro(e);
        }
    }

    public Date getDataInicialFiltrar() {
        return dataInicialFiltrar;
    }

    public void setDataInicialFiltrar(Date dataInicialFiltrar) {
        this.dataInicialFiltrar = dataInicialFiltrar;
    }

    public Date getDataFinalFiltrar() {
        return dataFinalFiltrar;
    }

    public void setDataFinalFiltrar(Date dataFinalFiltrar) {
        this.dataFinalFiltrar = dataFinalFiltrar;
    }

    public Integer getTotalLancado() {
        return getListaAcessosAdicionados().size();
    }

    public void buscarMatricula() {
        try {
            setOnComplete("");
            if (!UteisValidacao.emptyString(getClienteVO().getMatricula())) {
                ClienteVO clienteVO = getFacade().getCliente().consultarPorMatricula(getClienteVO().getMatricula(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                if (clienteVO == null) {
                    setClienteVO(new ClienteVO());
                    setOnComplete("document.getElementById('form:matricula').focus();");
                    throw new Exception("Cliente não encontrado");
                } else {
                    limparMsg();
                    setClienteVO(clienteVO);
                    setBuscaPorMatricula(true);
                    setOnComplete("document.getElementById('form:clienteOUColaboradorIndicou').focus();");
                }
            } else {
                setClienteVO(new ClienteVO());
                setOnComplete("document.getElementById('form:clienteOUColaboradorIndicou').focus();");
            }
        } catch (Exception e) {
            montarErro(e);
        }
    }

    public void excluirAcesso() {
        try {
            AcessoClienteVO obj = (AcessoClienteVO) context().getExternalContext().getRequestMap().get("acesso");

            if (obj != null) {
                AcessoClienteVO ultimoAcesso = getFacade().getAcessoCliente().consultarUltimoAcessoDiferenteDe(obj.getCliente(), obj.getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);

                getFacade().getCliente().registrarUltimoAcesso(getClienteVO().getCodigo(), ultimoAcesso.getCodigo());
                getFacade().getAcessoCliente().excluir(obj, "REGISTRO DE ACESSO MANUAL EXCLUIDO", getFacade().getControleCreditoTreino());
                getFacade().getZWFacade().atualizarSintetico(getClienteVO(), Calendario.hoje(), SituacaoClienteSinteticoEnum.GRUPO_DADOSACESSO, false);
                consultarAcessosLancados();

                //REGISTRAR LOG EXCLUSÃO
                try {
                    LogVO log = new LogVO();
                    log.setChavePrimaria(obj.getCliente().getPessoa().getCodigo().toString());
                    log.setNomeEntidade("CLIENTE - ACESSO MANUAL");
                    log.setNomeEntidadeDescricao("Cliente - Acesso Manual");
                    log.setOperacao("EXCLUIR - REGISTRO ACESSO MANUAL");
                    log.setResponsavelAlteracao(getUsuarioLogado().getNome());
                    log.setUserOAMD(getUsuarioLogado().getUserOamd());
                    log.setNomeCampo("AcessoManual-Cliente");
                    log.setValorCampoAnterior("");
                    StringBuilder campoAlterado = new StringBuilder();
                    if (obj.getCodigo() != null) {
                        campoAlterado.append("CÓDIGO ACESSO: " + obj.getCodigo()).append(" \n");
                    }
                    if (obj.getDataHoraEntrada() != null) {
                        campoAlterado.append("ENTRADA: " + Uteis.getDataAplicandoFormatacao(obj.getDataHoraEntrada(), "dd/MM HH:mm:ss")).append(" \n");
                    }
                    if (obj.getDataHoraSaida() != null) {
                        campoAlterado.append("SAÍDA: " + Uteis.getDataAplicandoFormatacao(obj.getDataHoraSaida(), "dd/MM HH:mm:ss")).append(" \n");
                    }
                    log.setValorCampoAlterado(campoAlterado.toString());
                    log.setDataAlteracao(negocio.comuns.utilitarias.Calendario.hoje());
                    registrarLogObjetoVO(log, obj.getCliente().getPessoa().getCodigo());
                } catch (Exception e) {
                    registrarLogErroObjetoVO("CLIENTE - ACESSO MANUAL", obj.getCliente().getPessoa().getCodigo(), "ERRO AO REGISTRAR LOG ACESSO MANUAL", this.getUsuarioLogado().getNome(), this.getUsuarioLogado().getUserOamd());
                    e.printStackTrace();
                }
            } else {
                throw new Exception("Acesso não encontrado");
            }

            montarSucessoGrowl("Acesso excluído com sucesso.");
        } catch (Exception e) {
            montarErro(e);
        }
    }

    public void irParaTelaCliente() {
        setOnComplete("");
        AcessoClienteVO obj = (AcessoClienteVO) context().getExternalContext().getRequestMap().get("acesso");
        try {
            if (obj == null) {
                throw new Exception("Cliente Não Encontrado.");
            } else {
                ClienteVO clienteVO = getFacade().getCliente().consultarPorChavePrimaria(obj.getCliente().getCodigo(), Uteis.NIVELMONTARDADOS_MINIMOS);
                irParaTelaCliente(clienteVO);
                setOnComplete("abrirPopup('../clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);");
            }
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public boolean isBuscaPorMatricula() {
        return buscaPorMatricula;
    }

    public void setBuscaPorMatricula(boolean buscaPorMatricula) {
        this.buscaPorMatricula = buscaPorMatricula;
    }

    public void paintFoto(OutputStream out, Object data) throws IOException, Exception {
        if (getClienteVO().getPessoa().getFoto() == null || getClienteVO().getPessoa().getFoto().length == 0) {
            recarregarFoto();
        }
        SuperControle.paintFoto(out, getClienteVO().getPessoa().getFoto());
    }

    public String getPaintFotoDaNuvem() {
        return getPaintFotoDaNuvem(getClienteVO().getPessoa().getFotoKey());
    }

    public void recarregarFoto() throws Exception {
        if (isFotosNaNuvem()) {
            final String fotoKey = getFacade().getPessoa().obterFotoKey(
                    getClienteVO().getPessoa().getCodigo());
            if (!UteisValidacao.emptyString(fotoKey)) {
                getClienteVO().getPessoa().setFotoKey(fotoKey + "?time=" + getTimeStamp());
            }
        } else {
            getClienteVO().getPessoa().setFoto(getFacade().getPessoa().obterFoto(
                    getKey(), getClienteVO().getPessoa().getCodigo()));
        }
    }

    public List<AcessoClienteVO> getAcessosNoDia() {
        if (acessosNoDia == null) {
            acessosNoDia = new ArrayList<AcessoClienteVO>();
        }
        return acessosNoDia;
    }

    public void setAcessosNoDia(List<AcessoClienteVO> acessosNoDia) {
        this.acessosNoDia = acessosNoDia;
    }

    public boolean isAlterarAcesso() {
        return alterarAcesso;
    }

    public void setAlterarAcesso(boolean alterarAcesso) {
        this.alterarAcesso = alterarAcesso;
    }

    public AcessoClienteVO getAcessoClienteAlterar() {
        if (acessoClienteAlterar == null) {
            acessoClienteAlterar =  new AcessoClienteVO();
        }
        return acessoClienteAlterar;
    }

    public void setAcessoClienteAlterar(AcessoClienteVO acessoClienteAlterar) {
        this.acessoClienteAlterar = acessoClienteAlterar;
    }
    
}

package controle.basico;

import br.com.pactosolucoes.comuns.notificacao.RecursoSistema;
import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.enumeradores.Modulo;
import br.com.pactosolucoes.enumeradores.SituacaoClienteEnum;
import br.com.pactosolucoes.enumeradores.TipoInfoMigracaoEnum;
import br.com.pactosolucoes.estrutura.ItemExportacaoEnum;
import br.com.pactosolucoes.estrutura.exportador.controle.ExportadorListaControle;
import br.com.pactosolucoes.estrutura.paginacao.ConfPaginacao;
import controle.arquitetura.MenuControle;
import controle.arquitetura.ModuloAberto;
import controle.arquitetura.SuperControle;
import controle.arquitetura.security.LoginControle;
import controle.contrato.ContratoControle;
import controle.financeiro.MovParcelaControle;
import negocio.comuns.basico.CategoriaVO;
import negocio.comuns.basico.ClassificacaoVO;
import negocio.comuns.basico.ClienteFiltroVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.ConfiguracaoSistemaVO;
import negocio.comuns.basico.ConsultaClienteTO;
import negocio.comuns.basico.EmailVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.GrauInstrucaoVO;
import negocio.comuns.basico.GrupoVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.basico.PlacaVO;
import negocio.comuns.basico.ProfissaoVO;
import negocio.comuns.basico.TelefoneVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.utilitarias.ControleConsulta;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.SelectItemOrdemValor;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import relatorio.negocio.jdbc.sad.SituacaoClienteSinteticoDW;

import javax.faces.context.FacesContext;
import javax.faces.event.ActionEvent;
import javax.faces.model.SelectItem;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.io.OutputStream;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;

public class ConsultaClienteControle extends SuperControle {

    private Integer consultaCategoria = 0;
    private Integer consultaEmpresa;
    private Integer valorUltimosCadastros = 0;
    private String consultaSituacao = "";
    private String paramLetraConsultPag = "";
    private String paramTipoConsulta = "";
    private String orderBy = "pes.nome";
    private String orderByAD = "ASC";
    private String valorConsultaParametrizada = "";
    private List<SelectItem> listaSelectItemCategoria = new ArrayList<SelectItem>();
    private List<SelectItem> listaTipoConsultaComboCliente = new ArrayList<SelectItem>();
    private List<SelectItem> listaSelectItemSituacaoCliente = new ArrayList<SelectItem>();
    private boolean checado = true;
    private boolean mostrarCarregando = true;
    private boolean permissaoConsultaInfoTodasEmpresas = false;
    private boolean permissaoExportarContratos = false;
    private ConfiguracaoSistemaVO configuracaoSistema;
    private String[] displayIdentificadorFront;

    public void paintFoto(OutputStream out, Object data) throws IOException, Exception {
        HttpServletRequest request = (HttpServletRequest) FacesContext.getCurrentInstance().getExternalContext().getRequest();
        String pessoa = request.getParameter("pessoa");
        SuperControle.paintFoto(out, null);
    }
    
    public ConsultaClienteControle() throws Exception {
        inicializarConfiguracaoSistema();
        identificacaoPessoalInternacional();
        setPermissaoConsultaInfoTodasEmpresas(permissao("ConsultarAlunosCaixaAbertoTodasEmpresas"));
        setPermissaoExportarContratos(permissao("ExportarRelatorioCliente"));
        montarListaEmpresas();
        montarListaSelectItemCategoria();
        montarListaSelectItemTipoConsulta();
        montarListaSelectItemSituacaoCliente();
        setControleConsulta(new ControleConsulta());
        setConfPaginacao(new ConfPaginacao(50));
        if(JSFUtilities.isJSFContext() &&
                request().getRequestURI().contains("/clientes.jsp")){
            consultarPaginado();
        }
    }

    public void inicializarConfiguracaoSistema(){
        try {
            setConfiguracaoSistema((ConfiguracaoSistemaVO) JSFUtilities.getFromSession(JSFUtilities.CONFIGURACAO_SISTEMA));
        } catch (Exception e) {
            throw e;
        }
    }

    public void exportar(ActionEvent evt) {
        try {
            limparMsg();
            setMsgAlert("");
            getConfPaginacao().setPaginarBanco(false);
            ClienteFiltroVO filtroVO = iniciarFiltro();
            List<ConsultaClienteTO> objs = getFacade().getCliente().consultarPaginadoTelaConsulta(filtroVO, getConfPaginacao());
            getConfPaginacao().setPaginarBanco(true);
            String filtro = (String) JSFUtilities.getFromActionEvent("filtro", evt);//facultativo
            ExportadorListaControle exportControle = (ExportadorListaControle) JSFUtilities.getManagedBeanValue(ExportadorListaControle.class);
            exportControle.exportar(evt, objs, filtro, ItemExportacaoEnum.TELA_CLIENTES);
            if(exportControle.getErro()){
                throw new Exception(exportControle.getMensagemDetalhada());
            }
            setMsgAlert(exportControle.getOperacaoOnComplete());
        } catch (Exception e){
            montarErro(e);
        }
    }

    public void consultarPaginadoListenerUltimos7Dias(ActionEvent evt) {
        setListaConsulta(new ArrayList());
        String tipoConsulta = UteisValidacao.emptyNumber(valorUltimosCadastros) ? "detalhada"
                : valorUltimosCadastros.equals(1) ? "contratos" : "visitantes";
        evt.getComponent().getAttributes().put("tipoConsulta", tipoConsulta);
        evt.getComponent().getAttributes().put("paginaInicial", "paginaInicial");
        consultarPaginadoListener(evt);
    }

    public void consultarPaginadoListenerReset(ActionEvent evt) {
        valorUltimosCadastros = 0;
        setListaConsulta(new ArrayList());
        consultarPaginadoListener(evt);
    }
    
    public void consultarPaginadoListener(ActionEvent evt) {
        setNavigationCase(tratarNavigationCaseIntegracaoModulo("tela3", "modulo_lista_clientes", evt));

        if (isChecado()) {
            // VARIAVEL QUE DETERMINA SE A CONSULTA TERÁ PAGINACAO EM BANCO OU NAO
            getConfPaginacao().setPaginarBanco(true);
        } else {
            setConfPaginacao(new ConfPaginacao(50));
            getConfPaginacao().setPaginarBanco(false);
        }

        // VERIFICACAO NECESSARIA POR CAUSA DOS FILTROS
        Object compPaginaInicial = evt.getComponent().getAttributes().get("paginaInicial");

        if (compPaginaInicial != null && !"".equals(compPaginaInicial.toString())) {
            if (compPaginaInicial.toString().equals("paginaInicial")) {
                setConfPaginacao(new ConfPaginacao(50));
            }
        }

        // Obtendo qual pagina deverá ser exibida
        Object component = evt.getComponent().getAttributes().get("pagNavegacao");

        if (component != null && !"".equals(component.toString())) {
            getConfPaginacao().setPagNavegacao(component.toString());
        }

        Object compTipoConsulta = evt.getComponent().getAttributes().get("tipoConsulta");

        if (compTipoConsulta != null && !"".equals(compTipoConsulta.toString())) {
            if (!compTipoConsulta.toString().equals(getParamTipoConsulta())) {
                setConfPaginacao(new ConfPaginacao(50));
            }
            setParamTipoConsulta(compTipoConsulta.toString());
        }

        if ("letra".equals(getParamTipoConsulta())) {
            // Obtendo o parametro de pesquisa para a consulta por letra
            Object compLetraConsultPag = evt.getComponent().getAttributes().get("letraConsultPag");

            if (compLetraConsultPag != null && !"".equals(compLetraConsultPag.toString())) {
                if (!compLetraConsultPag.toString().equals(getParamLetraConsultPag())) {
                    setConfPaginacao(new ConfPaginacao(50));
                }
                setParamLetraConsultPag(compLetraConsultPag.toString());
            }
        }
        consultarPaginado();
    }

    public boolean isMostrarCarregando() {
        return mostrarCarregando;
    }

    public void setMostrarCarregando(boolean mostrarCarregando) {
        this.mostrarCarregando = mostrarCarregando;
    }

    public void consultarPaginado() {
        try {
            super.consultar();
            ClienteFiltroVO filtro = iniciarFiltro();
            List<ConsultaClienteTO> objs = getFacade().getCliente().consultarPaginadoTelaConsulta(filtro, getConfPaginacao());
            mostrarCarregando = objs.size() >= 50 && objs.size() < getConfPaginacao().getNumeroTotalItens();
            setListaConsulta(objs);
            setMensagemID("msg_dados_consultados");
            setSucesso(true);
            setErro(false);

            // ============================================================================================
            if (context() != null) {
                //Liberar Conta do Caixa da memoria
                MovParcelaControle movParcela = (MovParcelaControle) context().getExternalContext().getSessionMap().get("MovParcelaControle");
                if (movParcela != null) {
                    movParcela.liberarBackingBeanMemoria("MovParcelaControle");
                }
                //Liberar Contrato da memoria
                ContratoControle contratoControle = (ContratoControle) context().getExternalContext().getSessionMap().get("ContratoControle");
                if (contratoControle != null) {
                    contratoControle.liberarBackingBeanMemoria("ContratoControle");
                }
            }

        } catch (Exception e) {
            setListaConsulta(new ArrayList());
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
            System.out.println("WARNING -> " + this.getClass().getSimpleName() + " => " + e.getMessage());
        }
    }

    public String acao() {
        try {
            LoginControle l = (LoginControle) JSFUtilities.getManagedBean("LoginControle");
            l.setModuloAberto(ModuloAberto.ZILLYONWEB);

            setMensagemID("msg_entre_dados");
            getControleConsulta().setValorConsulta(valorConsultaParametrizada);
            valorConsultaParametrizada = "";
            if (getListaConsulta().size() == 1) {
                if (getNavigationCase().contains("4bf2add2267962ea87f029fef8f75a2f")) {
                    ClienteControle control = (ClienteControle) JSFUtilities.getManagedBean("ClienteControle");
                    if (control == null) {
                        control = new ClienteControle();
                    }
                    control.inicializaDados();
                    control.prepararTelaCliente(((ConsultaClienteTO) getListaConsulta().get(0)).obterClienteVO(), true);
                    return "modulo_visualiza_cliente?modulo=4bf2add2267962ea87f029fef8f75a2f";
                }
//                return tratarNavigationCaseIntegracaoModulo("editarCliente", "modulo_visualiza_cliente");
                redirect("/faces/clienteNav.jsp?page=cliente&matricula="
                        +((ConsultaClienteTO) getListaConsulta().get(0)).getMatricula());
                return "";
            }
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
        return getNavigationCase();
    }

    public String todosClientes() {
        if(isApresentarMenuZWUI()){
            setarModuloAberto(ModuloAberto.PESSOAS);
            MenuControle menuControle = JSFUtilities.getControlador(MenuControle.class);
            if(menuControle != null){
                menuControle.setExibirFavoritos(false);
                menuControle.setExibirConfiguracao(false);
                menuControle.setReprocessar(true);
                menuControle.setUrlGoBackRedirect(null);
                menuControle.setSiglaModulo(ModuloAberto.PESSOAS.getSigla());
                menuControle.processarLabelBtnVoltar();
            }
        }else{
            setarModuloAberto(ModuloAberto.ZILLYONWEB);
        }

        String linkNovaTela = getLinkNovaTelaClientes();
        if (!UteisValidacao.emptyString(linkNovaTela)) {
            try {
                redirectUrl(linkNovaTela);
            } catch (Exception ex) {
                ex.printStackTrace();
            }
            return "";
        } else {
            return "todosClientes";
        }
    }
    
    public List<CategoriaVO> consultarCategoriaPorNome(String nomePrm) throws Exception {
        return getFacade().getCategoria().consultarPorNome(nomePrm, false, Uteis.NIVELMONTARDADOS_TODOS);
    }

    public void montarListaSelectItemCategoria() throws Exception {
        if (JSFUtilities.isJSFContext()) {
            SuperControle superControle = (SuperControle)JSFUtilities.getManagedBean("SuperControle");
            setListaSelectItemCategoria(superControle.getListaCategorias());

            for(SelectItem it : getListaSelectItemCategoria()){
                if(it.getValue() == null || ((Integer)it.getValue()).equals(0)){
                    it.setLabel("Categoria");
                }
            }
        }
    }
    
    public void montarListaSelectItemTipoConsulta() {
        List<SelectItem> itens = new ArrayList<SelectItem>();
        itens.add(new SelectItem("classificacao", "Classificação"));
        itens.add(new SelectItem("codigo", "Código"));
        itens.add(new SelectItem("codPessoa", "Cod.Pessoa"));
        itens.add(new SelectItem("contrato", "Contrato"));
        itens.add(new SelectItem("cpf", displayIdentificadorFront[0]));
        itens.add(new SelectItem("rne", "RNE"));
        itens.add(new SelectItem("passaporte", "Passaporte"));
        itens.add(new SelectItem("descricaoProfissao", "Profissão"));
        itens.add(new SelectItem("email", "Email"));
        itens.add(new SelectItem("grupo", "Grupo"));
        itens.add(new SelectItem("matricula", "Matrícula"));
        itens.add(new SelectItem("telefone", "Telefone"));
        itens.add(new SelectItem("rg", displayIdentificadorFront[1]));
        itens.add(new SelectItem("placa", "Placa do Veículo"));
        itens.add(new SelectItem("responsavel", "Responsável"));
        itens.add(new SelectItem("codAcessoAlternativo", "Cod.Acesso Alternativo"));
        Ordenacao.ordenarLista(itens, "label");
        itens.add(0, new SelectItem("nomePessoa", "Nome"));
        setListaTipoConsultaComboCliente(itens);
    }

    public void montarListaSelectItemSituacaoCliente() throws Exception {
        List<SelectItem> objs = new ArrayList<SelectItem>();
        for (SituacaoClienteEnum situacaoClienteEnum : SituacaoClienteEnum.values()) {
            if (situacaoClienteEnum.getDescricao().equals(SituacaoClienteEnum.CONTRATOS_ATIVO.getDescricao())) continue;
            String value = situacaoClienteEnum.getCodigo();
            String label = situacaoClienteEnum.getDescricao();
            objs.add(new SelectItem(value, label));
        }
        if (getConfiguracaoSistema() != null &&
                getConfiguracaoSistema().isUsaPlanoRecorrenteCompartilhado()){
            objs.add(new SelectItem("Dependentes", "Dependentes"));
        }
        SelectItemOrdemValor ordenador = new SelectItemOrdemValor();
        Collections.sort((List) objs, ordenador);
        setListaSelectItemSituacaoCliente(objs);
    }

    /**
     * Getters e Setters
     */

    public boolean isChecado() {
        return checado;
    }

    public void setChecado(boolean checado) {
        this.checado = checado;
    }

    public String getParamLetraConsultPag() {
        return paramLetraConsultPag;
    }

    public void setParamLetraConsultPag(String paramLetraConsultPag) {
        this.paramLetraConsultPag = paramLetraConsultPag;
    }

    public String getParamTipoConsulta() {
        return paramTipoConsulta;
    }

    public void setParamTipoConsulta(String paramTipoConsulta) {
        this.paramTipoConsulta = paramTipoConsulta;
    }

    public String getConsultaSituacao() {
        return consultaSituacao;
    }

    public void setConsultaSituacao(String consultaSituacao) {
        this.consultaSituacao = consultaSituacao;
    }

    public String getValorConsultaParametrizada() {
        return valorConsultaParametrizada;
    }

    public void setValorConsultaParametrizada(String valorConsultaParametrizada) {
        this.valorConsultaParametrizada = valorConsultaParametrizada;
    }

    public Integer getConsultaCategoria() {
        return consultaCategoria;
    }

    public void setConsultaCategoria(Integer consultaCategoria) {
        this.consultaCategoria = consultaCategoria;
    }

    public List<SelectItem> getListaSelectItemCategoria() {
        return listaSelectItemCategoria;
    }

    public void setListaSelectItemCategoria(List<SelectItem> listaSelectItemCategoria) {
        this.listaSelectItemCategoria = listaSelectItemCategoria;
    }

    public List<SelectItem> getListaTipoConsultaComboCliente() {
        return listaTipoConsultaComboCliente;
    }

    public void setListaTipoConsultaComboCliente(List<SelectItem> listaTipoConsultaComboCliente) {
        this.listaTipoConsultaComboCliente = listaTipoConsultaComboCliente;
    }

    public List<SelectItem> getListaSelectItemSituacaoCliente() {
        return listaSelectItemSituacaoCliente;
    }

    public void setListaSelectItemSituacaoCliente(List<SelectItem> listaSelectItemSituacaoCliente) {
        this.listaSelectItemSituacaoCliente = listaSelectItemSituacaoCliente;
    }

    public Integer getValorUltimosCadastros() {
        return valorUltimosCadastros;
    }

    public void setValorUltimosCadastros(Integer valorUltimosCadastros) {
        this.valorUltimosCadastros = valorUltimosCadastros;
    }

    private ClienteFiltroVO iniciarFiltro() throws Exception {

        if(!isPermissaoConsultaInfoTodasEmpresas()){
            consultaEmpresa = getEmpresaLogado().getCodigo();
        }
        ClienteFiltroVO filtro = new ClienteFiltroVO();
        if (getConsultaSituacao() == null) {
            setConsultaSituacao("");
        }
        // ============================================================================================
        filtro.setControlarAcesso(true);
        filtro.setEmpresaVO(new EmpresaVO());
        filtro.getEmpresaVO().setCodigo(consultaEmpresa);
        if(filtro.getEmpresaVO().getCodigo() == 0){
            filtro.setConsultarDeTodasEmpresas(true);
        }

        filtro.setNivelMontarDados(Uteis.NIVELMONTARDADOS_TELACONSULTA);

        if ("letra".equals(this.getParamTipoConsulta())) {
            // Definindo filtro de pesquisa
            filtro.setPessoaVO(new PessoaVO());
            filtro.getPessoaVO().setNome(this.getParamLetraConsultPag());
            filtro.getPessoaVO().setCfp("sem valor");
            filtro.getPessoaVO().setRg("sem valor");
            filtro.getPessoaVO().setRne("sem valor");
            filtro.getPessoaVO().setPassaporte("sem valor");
            filtro.setEmpresaVO(new EmpresaVO());
            filtro.getEmpresaVO().setCodigo(consultaEmpresa);
        } else if ("parametrizado".equals(this.getParamTipoConsulta())) {
            // Definindo filtro de pesquisa
            filtro.setPessoaVO(new PessoaVO());

            Integer matricula;
            if (!Uteis.getValidarStringSePossuiLetra(getValorConsultaParametrizada().trim())) {
                String valorConsulta = Uteis.getDesmontarMatricula(getValorConsultaParametrizada());
                matricula = !UteisValidacao.emptyString(valorConsulta) ? new Integer(valorConsulta) : 0;
                filtro.setClienteVO(new ClienteVO());
                filtro.getClienteVO().setCodigoMatricula(matricula);
                filtro.getClienteVO().setSituacao("");
                filtro.getClienteVO().setCategoria(new CategoriaVO());
                filtro.getClienteVO().getCategoria().setCodigo(getConsultaCategoria());
                filtro.setPessoaVO(new PessoaVO());
                filtro.getPessoaVO().setNome("sem valor");
            } else {
                filtro.getPessoaVO().setNome(this.getValorConsultaParametrizada().trim());
            }
            filtro.getPessoaVO().setCfp("sem valor");
            filtro.getPessoaVO().setRg("sem valor");
            filtro.getPessoaVO().setRne("sem valor");
            filtro.getPessoaVO().setPassaporte("sem valor");
            filtro.setEmpresaVO(new EmpresaVO());
            filtro.getEmpresaVO().setCodigo(consultaEmpresa);
        } else if ("paramSugestion".equals(this.getParamTipoConsulta())) {
            // Definindo filtro de pesquisa
            Integer codigo;
            codigo = new Integer(Integer.parseInt(this.getValorConsultaParametrizada()));
            filtro.setSituacaoClienteSinteticoDW(new SituacaoClienteSinteticoDW());
            filtro.setClienteVO(new ClienteVO());
            filtro.getClienteVO().setCodigo(codigo);
            filtro.getClienteVO().setSituacao("");
            filtro.setPessoaVOSetSemValorNomeRgCpfRnePassaporte(new PessoaVO());
        } else if ("visitantes".equals(this.getParamTipoConsulta())) {
            Date fim = Uteis.getDate(Uteis.getDataAtual());
            Date inicio = Uteis.obterDataAnterior(fim, 7);

            filtro.setClienteVO(new ClienteVO());
            filtro.getClienteVO().setSituacao("VI");
            filtro.getClienteVO().setCategoria(new CategoriaVO());
            filtro.getClienteVO().getCategoria().setCodigo(getConsultaCategoria());
            filtro.setEmpresaVO(new EmpresaVO());
            filtro.getEmpresaVO().setCodigo(consultaEmpresa);
            filtro.setInicio(inicio);
            filtro.setFim(fim);
            filtro.setParamTipoConsulta(this.getParamTipoConsulta());
        } else if ("contratos".equals(this.getParamTipoConsulta())) {
            Date fim = Uteis.getDate(Uteis.getDataAtual());
            Date inicio = Uteis.obterDataAnterior(fim, 7);

            filtro.setParamTipoConsulta("contratos");

            String situacao = getConsultaSituacao() == null ? "" : getConsultaSituacao();
            filtro.setClienteVO(new ClienteVO());
            filtro.getClienteVO().setSituacao(situacao);
            filtro.getClienteVO().setCategoria(new CategoriaVO());
            filtro.getClienteVO().getCategoria().setCodigo(getConsultaCategoria());

            filtro.setContratoVO(new ContratoVO());
            filtro.setEmpresaVO(new EmpresaVO());
            filtro.getEmpresaVO().setCodigo(consultaEmpresa);
            filtro.setInicio(inicio);
            filtro.setFim(fim);
        } else if ("detalhada".equals(this.getParamTipoConsulta())) {
            String situacao = getConsultaSituacao() == null ? "" : getConsultaSituacao();

            filtro.setEmpresaVO(new EmpresaVO());
            filtro.getEmpresaVO().setCodigo(consultaEmpresa);
            if (getControleConsulta().getCampoConsulta().equals("codigo")) {

                int valorInt = 0;
                if (!getControleConsulta().getValorConsulta().trim().isEmpty()) {
                    valorInt = Integer.parseInt(getControleConsulta().getValorConsulta().trim());
                }
                filtro.setSituacaoClienteSinteticoDW(new SituacaoClienteSinteticoDW());
                filtro.setClienteVO(new ClienteVO());
                filtro.getClienteVO().setCodigo(valorInt);
                filtro.getClienteVO().setSituacao(situacao);
                filtro.getClienteVO().setCategoria(new CategoriaVO());
                filtro.getClienteVO().getCategoria().setCodigo(getConsultaCategoria());
                filtro.setPessoaVOSetSemValorNomeRgCpfRnePassaporte(new PessoaVO());
            } else if (getControleConsulta().getCampoConsulta().equals("codAcessoAlternativo")){
                ClienteVO clienteVO = new ClienteVO();
                clienteVO.setSituacao(situacao);
                CategoriaVO categoriaVO = new CategoriaVO();
                categoriaVO.setCodigo(getConsultaCategoria());
                clienteVO.setCategoria(categoriaVO);
                clienteVO.setCodAcessoAlternativo(getControleConsulta().getValorConsulta().trim());
                filtro.setClienteVO(clienteVO);
                filtro.setPessoaVOSetSemValorNomeRgCpfRnePassaporte(new PessoaVO());
            } else if (getControleConsulta().getCampoConsulta().equals("nomePessoa")) {
                filtro.setSituacaoClienteSinteticoDW(new SituacaoClienteSinteticoDW());
                filtro.setClienteVO(new ClienteVO());
                filtro.getClienteVO().setSituacao(situacao);
                filtro.getClienteVO().setCategoria(new CategoriaVO());
                filtro.getClienteVO().getCategoria().setCodigo(getConsultaCategoria());
                filtro.setPessoaVO(new PessoaVO());
                filtro.getPessoaVO().setNome(getControleConsulta().getValorConsulta().trim());
                filtro.getPessoaVO().setCfp("sem valor");
                filtro.getPessoaVO().setRg("sem valor");
                filtro.getPessoaVO().setRne("sem valor");
                filtro.getPessoaVO().setPassaporte("sem valor");
            } else if (getControleConsulta().getCampoConsulta().equals("codPessoa")) {
                int valorInt = 0;
                if (!getControleConsulta().getValorConsulta().trim().isEmpty()) {
                    valorInt = Integer.parseInt(getControleConsulta().getValorConsulta().trim());
                }
                PessoaVO pessoaVO = new PessoaVO();
                pessoaVO.setCodigo(valorInt);
                filtro.setPessoaVOSetSemValorNomeRgCpfRnePassaporte(pessoaVO);
            } else if (getControleConsulta().getCampoConsulta().equals("cpf")) {
                filtro.setSituacaoClienteSinteticoDW(new SituacaoClienteSinteticoDW());
                filtro.setClienteVO(new ClienteVO());
                filtro.getClienteVO().setSituacao(situacao);
                filtro.getClienteVO().setCategoria(new CategoriaVO());
                filtro.getClienteVO().getCategoria().setCodigo(getConsultaCategoria());
                filtro.setPessoaVO(new PessoaVO());
                filtro.getPessoaVO().setNome("sem valor");
                filtro.getPessoaVO().setRne("sem valor");
                filtro.getPessoaVO().setPassaporte("sem valor");
                filtro.getPessoaVO().setRg("sem valor");
                filtro.getPessoaVO().setCfp(this.getControleConsulta().getValorConsulta().trim());
            } else if (getControleConsulta().getCampoConsulta().equals("rne")) {
                filtro.setSituacaoClienteSinteticoDW(new SituacaoClienteSinteticoDW());
                filtro.setClienteVO(new ClienteVO());
                filtro.getClienteVO().setSituacao(situacao);
                filtro.getClienteVO().setCategoria(new CategoriaVO());
                filtro.getClienteVO().getCategoria().setCodigo(getConsultaCategoria());
                filtro.setPessoaVO(new PessoaVO());
                filtro.getPessoaVO().setNome("sem valor");
                filtro.getPessoaVO().setCfp("sem valor");
                filtro.getPessoaVO().setRne(this.getControleConsulta().getValorConsulta().trim());
                filtro.getPessoaVO().setPassaporte("sem valor");
                filtro.getPessoaVO().setRg("sem valor");
            } else if (getControleConsulta().getCampoConsulta().equals("passaporte")) {
                filtro.setSituacaoClienteSinteticoDW(new SituacaoClienteSinteticoDW());
                filtro.setClienteVO(new ClienteVO());
                filtro.getClienteVO().setSituacao(situacao);
                filtro.getClienteVO().setCategoria(new CategoriaVO());
                filtro.getClienteVO().getCategoria().setCodigo(getConsultaCategoria());
                filtro.setPessoaVO(new PessoaVO());
                filtro.getPessoaVO().setNome("sem valor");
                filtro.getPessoaVO().setCfp("sem valor");
                filtro.getPessoaVO().setRne("sem valor");
                filtro.getPessoaVO().setPassaporte(this.controleConsulta.getValorConsulta().trim());
                filtro.getPessoaVO().setRg("sem valor");
            } else if (getControleConsulta().getCampoConsulta().equals("rg")) {
                filtro.setSituacaoClienteSinteticoDW(new SituacaoClienteSinteticoDW());
                filtro.setClienteVO(new ClienteVO());
                filtro.getClienteVO().setSituacao(situacao);
                filtro.getClienteVO().setCategoria(new CategoriaVO());
                filtro.getClienteVO().getCategoria().setCodigo(getConsultaCategoria());
                filtro.setPessoaVO(new PessoaVO());
                filtro.getPessoaVO().setNome("sem valor");
                filtro.getPessoaVO().setCfp("sem valor");
                filtro.getPessoaVO().setRne("sem valor");
                filtro.getPessoaVO().setPassaporte("sem valor");
                filtro.getPessoaVO().setRg(this.getControleConsulta().getValorConsulta().trim());
            } else if (getControleConsulta().getCampoConsulta().equals("descricaoProfissao")) {
                filtro.setSituacaoClienteSinteticoDW(new SituacaoClienteSinteticoDW());
                filtro.setClienteVO(new ClienteVO());
                filtro.getClienteVO().setSituacao(situacao);
                filtro.getClienteVO().setCategoria(new CategoriaVO());
                filtro.getClienteVO().getCategoria().setCodigo(getConsultaCategoria());
                filtro.setProfissaoVO(new ProfissaoVO());
                filtro.getProfissaoVO().setDescricao(getControleConsulta().getValorConsulta().trim());
                filtro.setPessoaVOSetSemValorNomeRgCpfRnePassaporte(new PessoaVO());
            } else if (getControleConsulta().getCampoConsulta().equals("descricaoGrauInstrucao")) {
                filtro.setSituacaoClienteSinteticoDW(new SituacaoClienteSinteticoDW());
                filtro.setClienteVO(new ClienteVO());
                filtro.getClienteVO().setSituacao(situacao);
                filtro.getClienteVO().setCategoria(new CategoriaVO());
                filtro.getClienteVO().getCategoria().setCodigo(getConsultaCategoria());
                filtro.setGrauInstrucaoVO(new GrauInstrucaoVO());
                filtro.getGrauInstrucaoVO().setDescricao(getControleConsulta().getValorConsulta().trim());
            } else if (getControleConsulta().getCampoConsulta().equals("matricula")) {
                filtro.setSituacaoClienteSinteticoDW(new SituacaoClienteSinteticoDW());
                filtro.setClienteVO(new ClienteVO());
                filtro.getClienteVO().setSituacao(situacao);
                filtro.getClienteVO().setCategoria(new CategoriaVO());
                filtro.getClienteVO().getCategoria().setCodigo(getConsultaCategoria());

                int valorInt = 0;
                if (!getControleConsulta().getValorConsulta().trim().isEmpty()) {
                    valorInt = Integer.parseInt(getControleConsulta().getValorConsulta().trim());
                }
                filtro.getClienteVO().setCodigoMatricula(valorInt);
            } else if (getControleConsulta().getCampoConsulta().equals("grupo")) {
                filtro.setSituacaoClienteSinteticoDW(new SituacaoClienteSinteticoDW());
                filtro.setClienteVO(new ClienteVO());
                filtro.getClienteVO().setSituacao(situacao);
                filtro.getClienteVO().setCategoria(new CategoriaVO());
                filtro.getClienteVO().getCategoria().setCodigo(getConsultaCategoria());
                filtro.setPessoaVOSetSemValorNomeRgCpfRnePassaporte(new PessoaVO());
                filtro.setGrupoVO(new GrupoVO());
                filtro.getGrupoVO().setDescricao(getControleConsulta().getValorConsulta().trim());
            } else if (getControleConsulta().getCampoConsulta().equals("classificacao")) {
                filtro.setSituacaoClienteSinteticoDW(new SituacaoClienteSinteticoDW());
                filtro.setClienteVO(new ClienteVO());
                filtro.getClienteVO().setSituacao("");
                filtro.getClienteVO().setCategoria(new CategoriaVO());
                filtro.getClienteVO().getCategoria().setCodigo(getConsultaCategoria());
                filtro.setPessoaVOSetSemValorNomeRgCpfRnePassaporte(new PessoaVO());
                filtro.setClassificacaoVO(new ClassificacaoVO());
                filtro.getClassificacaoVO().setNome(getControleConsulta().getValorConsulta().trim());
            } else if (getControleConsulta().getCampoConsulta().equals("situacao")) {
                filtro.setSituacaoClienteSinteticoDW(new SituacaoClienteSinteticoDW());
                filtro.setClienteVO(new ClienteVO());
                filtro.getClienteVO().setSituacao(situacao);
                filtro.getClienteVO().setCategoria(new CategoriaVO());
                filtro.getClienteVO().getCategoria().setCodigo(getConsultaCategoria());
                filtro.setPessoaVO(new PessoaVO());
                filtro.getPessoaVO().setNome(getControleConsulta().getValorConsulta().trim());
                filtro.getPessoaVO().setCfp("sem valor");
                filtro.getPessoaVO().setRg("sem valor");
                filtro.getPessoaVO().setRne("sem valor");
                filtro.getPessoaVO().setPassaporte("sem valor");
            } else if (getControleConsulta().getCampoConsulta().equals("nomeCategoria")) {
                filtro.setSituacaoClienteSinteticoDW(null);
                filtro.setClienteVO(new ClienteVO());
                filtro.getClienteVO().getCategoria().setCodigo(this.getConsultaCategoria());
            } else if (getControleConsulta().getCampoConsulta().equals("contrato")) {
                filtro.setSituacaoClienteSinteticoDW(null);
                filtro.setClienteVO(new ClienteVO());
                filtro.setContratoVO(new ContratoVO());

                int valorInt = 0;
                if (!getControleConsulta().getValorConsulta().trim().isEmpty()) {
                    valorInt = Integer.parseInt(getControleConsulta().getValorConsulta().trim());
                }
                filtro.getContratoVO().setCodigo(valorInt);
                filtro.getClienteVO().setSituacao(situacao);
                filtro.getClienteVO().setCategoria(new CategoriaVO());
                filtro.getClienteVO().getCategoria().setCodigo(getConsultaCategoria());
                filtro.setPessoaVO(null);
            } else if (getControleConsulta().getCampoConsulta().equals("email")) {
                filtro.setSituacaoClienteSinteticoDW(new SituacaoClienteSinteticoDW());
                filtro.setClienteVO(new ClienteVO());
                filtro.setContratoVO(null);
                filtro.setEmailVO(new EmailVO());
                String endEmail = getControleConsulta().getValorConsulta().trim();
                filtro.getEmailVO().setEmail(endEmail);
                filtro.getClienteVO().setSituacao(situacao);
                filtro.getClienteVO().setCategoria(new CategoriaVO());
                filtro.getClienteVO().getCategoria().setCodigo(getConsultaCategoria());
                filtro.setPessoaVO(null);
            } else if (getControleConsulta().getCampoConsulta().equals("telefone")) {
                filtro.setSituacaoClienteSinteticoDW(new SituacaoClienteSinteticoDW());
                filtro.setClienteVO(new ClienteVO());
                filtro.setContratoVO(null);
                filtro.setTelefoneVO(new TelefoneVO());
                String telefone = getControleConsulta().getValorConsulta().trim();
                filtro.getTelefoneVO().setNumero(telefone);
                filtro.getClienteVO().setSituacao(situacao);
                filtro.getClienteVO().setCategoria(new CategoriaVO());
                filtro.getClienteVO().getCategoria().setCodigo(getConsultaCategoria());
                filtro.setPessoaVO(null);
            } else if (getControleConsulta().getCampoConsulta().equals("placa")) {
                filtro.setSituacaoClienteSinteticoDW(new SituacaoClienteSinteticoDW());
                filtro.setClienteVO(new ClienteVO());
                filtro.setContratoVO(null);
                filtro.setPlacaVO(new PlacaVO());
                String placa = getControleConsulta().getValorConsulta().trim();
                filtro.getPlacaVO().setPlaca(placa);
                filtro.getClienteVO().setSituacao(situacao);
                filtro.getClienteVO().setCategoria(new CategoriaVO());
                filtro.getClienteVO().getCategoria().setCodigo(getConsultaCategoria());
                filtro.setPessoaVO(null);
            } else if (getControleConsulta().getCampoConsulta().equals("responsavel")) {
                filtro.setSituacaoClienteSinteticoDW(new SituacaoClienteSinteticoDW());
                filtro.setClienteVO(new ClienteVO());
                filtro.setContratoVO(null);
                filtro.setPlacaVO(null);
                filtro.setResponsavel(getControleConsulta().getValorConsulta());
                filtro.getClienteVO().setSituacao(situacao);
                filtro.getClienteVO().setCategoria(new CategoriaVO());
                filtro.getClienteVO().getCategoria().setCodigo(getConsultaCategoria());
            }

        } else if ("empresa".equals(this.getParamTipoConsulta())){
            filtro.setClienteVO(new ClienteVO());
            filtro.setSituacaoClienteSinteticoDW(new SituacaoClienteSinteticoDW());
            String situacao = getConsultaSituacao() == null ? "" : getConsultaSituacao();
            filtro.getClienteVO().setSituacao(situacao);
            filtro.getClienteVO().setCategoria(new CategoriaVO());
            filtro.getClienteVO().getCategoria().setCodigo(getConsultaCategoria());
        } else {
            filtro.setClienteVO(new ClienteVO());
            filtro.getClienteVO().setSituacao("AT");
            setConsultaSituacao("AT");
        }
        
        filtro.setOrderBy(orderBy);
        filtro.setOrderByAD(orderByAD);
        return filtro;
    }

    public String getOrderBy() {
        return orderBy;
    }

    public void setOrderBy(String orderBy) {
        this.orderBy = orderBy;
    }

    public String getOrderByAD() {
        return orderByAD;
    }

    public void setOrderByAD(String orderByAD) {
        this.orderByAD = orderByAD;
    }
    
    public void trocarAD(ActionEvent evt){
        orderByAD = orderByAD.equals("ASC") ? "DESC" : "ASC";
        consultarPaginadoListenerReset(evt);
    }

    public Integer getConsultaEmpresa() {
        return consultaEmpresa;
    }

    public void setConsultaEmpresa(Integer consultaEmpresa) {
        this.consultaEmpresa = consultaEmpresa;
    }

    public boolean isPermissaoConsultaInfoTodasEmpresas() {
        return permissaoConsultaInfoTodasEmpresas;
    }

    public void setPermissaoConsultaInfoTodasEmpresas(boolean permissaoConsultaInfoTodasEmpresas) {
        this.permissaoConsultaInfoTodasEmpresas = permissaoConsultaInfoTodasEmpresas;
    }

    public String[] getDisplayIdentificadorFront() {
        return displayIdentificadorFront;
    }

    public void setDisplayIdentificadorFront(String[] displayIdentificadorFront) {
        this.displayIdentificadorFront = displayIdentificadorFront;
    }

    public String[] identificacaoPessoalInternacional() {
        try {
            displayIdentificadorFront = identificadorPessoaInternacional(getEmpresaLogado().getPais().getNome());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return displayIdentificadorFront;

    }

    private SuperControle getSuperControle(){
        SuperControle superControle = (SuperControle) JSFUtilities.getFromSession("SuperControle");
        if (superControle == null) {
            superControle = (SuperControle) JSFUtilities.getManagedBean("SuperControle");
        }
        return superControle;
    }

    @Override
    public List<SelectItem> getListaEmpresas() {
        if(getSuperControle() != null){
            return getSuperControle().getListaEmpresas();
        }else{
            return new ArrayList<SelectItem>();
        }
    }

    public ConfiguracaoSistemaVO getConfiguracaoSistema() {
        return configuracaoSistema;
    }

    public void setConfiguracaoSistema(ConfiguracaoSistemaVO configuracaoSistema) {
        this.configuracaoSistema = configuracaoSistema;
    }

    public boolean isPermissaoExportarContratos() {
        return permissaoExportarContratos;
    }

    public void setPermissaoExportarContratos(boolean permissaoExportarContratos) {
        this.permissaoExportarContratos = permissaoExportarContratos;
    }

    @Override
    public void setListaEmpresas(List<SelectItem> listaEmpresas) {
        getSuperControle().setListaEmpresas(listaEmpresas);
    }

    @Override
    public void montarListaEmpresas() throws Exception {
        try {
            if(getListaEmpresas() == null || getListaEmpresas().size() == 0){

                List<SelectItem> listaEmpresas = obterListaEmpresas(
                        getFacade().getEmpresa().consultarPorNome("", true, false, Uteis.NIVELMONTARDADOS_MINIMOS),
                        LABEL_TODAS_EMPRESAS);

                setListaEmpresas(listaEmpresas);

                if(isPermissaoConsultaInfoTodasEmpresas()){
                    consultaEmpresa = 0;
                }else{
                    consultaEmpresa = getEmpresaLogado().getCodigo();
                }
            }
        } catch (Exception ex) {

        }
    }

    public void abrirNovaListaPessoasExperimente() {
        try {
            limparMsg();
            setMsgAlert("");
            LoginControle loginControle = (LoginControle) JSFUtilities.getFromSession("LoginControle");
            String openWindow = "window.open('" + loginControle.getAbrirNovaPlataforma(Modulo.NOVO_TREINO.getSiglaModulo()) + "&redirect=/pessoas/lista-v2', '_self')";
            notificarRecursoEmpresa(RecursoSistema.EXPERIMENTE_TELA_CLIENTES);
            setMsgAlert(openWindow);
        } catch (Exception ex) {
            ex.printStackTrace();
            montarErro(ex);
            setMsgAlert(getMensagemNotificar());
        }
    }

    public String getLinkNovaTelaClientes() {
        try {
            if (!Uteis.isHabilitarRecursoPadraoTelaCliente() ||
                    UteisValidacao.emptyNumber(getUsuarioLogado().getCodigo()) ||
                    getUsuarioLogado() == null) {
                return "";
            }
            boolean novaTelaAlunoPadrao = getFacade().getUsuario().recursoHabilitado(TipoInfoMigracaoEnum.TELA_ALUNO, getUsuarioLogado().getCodigo(), getEmpresaLogado().getCodigo());
            LoginControle loginControle = (LoginControle) JSFUtilities.getFromSession("LoginControle");
            if (!novaTelaAlunoPadrao ||
                    loginControle == null ||
                    !loginControle.isApresentarModuloNovoTreino()) {
                return "";
            }
            String openWindow = (loginControle.getAbrirNovaPlataforma(Modulo.NOVO_TREINO.getSiglaModulo()) + "&redirect=/pessoas/lista-v2");
            notificarRecursoEmpresa(RecursoSistema.NOVA_TELA_CLIENTES_ABRIR);
            return openWindow;
        } catch (Exception ex) {
            ex.printStackTrace();
            return "";
        }
    }

    public boolean isHabilitarRecursoPadraoTelaCliente() {
        return Uteis.isHabilitarRecursoPadraoTelaCliente();
    }

    public void verificarAbrirNovaListaPessoas() {
        try {
            limparMsg();
            setMsgAlert("");
            boolean notaTelaAluno = getFacade().getUsuario().recursoHabilitado(TipoInfoMigracaoEnum.TELA_ALUNO, getUsuarioLogado().getCodigo(), getEmpresaLogado().getCodigo());
            LoginControle loginControle = (LoginControle) JSFUtilities.getFromSession("LoginControle");
            if (notaTelaAluno &&
                    loginControle != null &&
                    loginControle.isApresentarModuloNovoTreino()) {
                MenuControle menuControle = JSFUtilities.getControlador(MenuControle.class);
                menuControle.setUrlGoBackRedirect(null);
                String openWindow = "window.open('" + loginControle.getAbrirNovaPlataforma(Modulo.NOVO_TREINO.getSiglaModulo()) + "&redirect=/pessoas/lista-v2', '_self')";
                setMsgAlert(openWindow);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }
}

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package controle.basico;

import br.com.pactosolucoes.comuns.notificacao.RecursoSistema;
import controle.arquitetura.SuperControle;
import negocio.comuns.utilitarias.*;
import negocio.tokenOperacao.TokenOperacaoVO;
import servicos.propriedades.PropsService;

import java.util.Date;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Objeto genérico para controlar o ModalPanel de solciitação de Tokens
 * Para utilizar esse controlador existe alguns pré-requisitos do controlador que irá integrá-lo:
 * 1. Deverá iniciar os seus parâmetros através do método 'init'
 * 1.1 Titulo: nome que será exibido no título do ModalPanel;
 * 1.2 Control: referência do objeto Controlador que estará integrado no momento (cuidado com conflitos);
 * 1.3 MetodoInvocar: nome do método void sem argumentos que será invocado no botão "SIM";
 * 2.
 * 3.
 *
 * <AUTHOR>
 */
public class TokenOperacaoControle extends SuperControle {

    private String mensagemApresentar;
    private transient Object control;
    private String metodoInvocarAoClicarBotaoValidar;
    private String metodoInvocarAoClicarBotaoNao;
    private String reRenderComponents;
    private String onCompleteBotaoValidar;
    private String onCompleteBotaoNao;
    private String onCompleteBotaoFechar;
    private String labelsCamposExibirNoModalTokenOperacao;
    private Integer qtdCamposAlterados;

    private String input1;
    private String input2;
    private String input3;
    private String input4;
    private String input5;
    private String input6;

    public void init(final String mensagemApresentar,
                     Object control,
                     final String metodoInvocarAoClicarBotaoValidar, final String onCompleteBotaoValidar,
                     final String metodoInvocarAoClicarBotaoNao, final String onCompleteBotaoNao,
                     final String textoCamposExibirNoModalTokenOperacao, final Integer qtdCamposAlterados,
                     final String reRenderComponents,
                     RecursoSistema recursoSistema) {
        limpar();
        this.mensagemApresentar = mensagemApresentar;
        this.control = control;
        this.metodoInvocarAoClicarBotaoValidar = metodoInvocarAoClicarBotaoValidar;
        this.onCompleteBotaoValidar = onCompleteBotaoValidar;
        this.metodoInvocarAoClicarBotaoNao = metodoInvocarAoClicarBotaoNao;
        this.onCompleteBotaoNao = onCompleteBotaoNao;
        if (UteisValidacao.emptyString(reRenderComponents)) {
            this.reRenderComponents = "form";
        } else {
            this.reRenderComponents = reRenderComponents;
        }
        this.onCompleteBotaoFechar = null;
        this.labelsCamposExibirNoModalTokenOperacao = textoCamposExibirNoModalTokenOperacao;
        this.qtdCamposAlterados = qtdCamposAlterados;
        notificarRecursoEmpresa(recursoSistema);
    }

    public void limpar() {
        this.mensagemApresentar = "";
        this.control = new Object();
        this.metodoInvocarAoClicarBotaoValidar = "";
        this.onCompleteBotaoValidar = "";
        this.metodoInvocarAoClicarBotaoNao = "";
        this.onCompleteBotaoNao = "";
        this.reRenderComponents = "";
        this.onCompleteBotaoFechar = "";
        this.labelsCamposExibirNoModalTokenOperacao = "";
        this.qtdCamposAlterados = 0;
        this.input1 = "";
        this.input2 = "";
        this.input3 = "";
        this.input4 = "";
        this.input5 = "";
        this.input6 = "";
    }

    public void fecharModal() throws Exception {
            setOnCompleteBotaoValidar("Richfaces.hideModalPanel('modalTokenOperacao')");
    }

    public String getMontarTextoAnteriorCamposExibirNoModalTokenOperacao() {
        try {
            if (getQtdCamposAlterados() == 1) {
                return "A alteração no campo ";
            } else if (getQtdCamposAlterados() > 1) {
                return "As alterações nos campos ";
            }
        } catch (Exception ex) {
        }
        return "";
    }

    public String getMontarTextoPosteriorCamposExibirNoModalTokenOperacao() {
        try {
            if (getQtdCamposAlterados() == 1) {
                return " é considerada de alto risco.";
            } else if (getQtdCamposAlterados() > 1) {
                return " são consideradas de alto risco.";
            }
        } catch (Exception ex) {
        }
        return "";
    }

    public String invokeBotaoValidar() {
        setMensagemDetalhada("", "");
        try {
            if (this.metodoInvocarAoClicarBotaoValidar != null) {
                Object objRetorno = UtilReflection.invoke(control, metodoInvocarAoClicarBotaoValidar);

                if ((objRetorno instanceof String)) {
                    return (String) objRetorno;
                }

                SuperControle superControle = (SuperControle) control;
                if (superControle.getErro()) {
                    montarErro(superControle.getMensagemDetalhada());
                }
            }
        } catch (Exception ex) {
            setMensagemDetalhada(ex);
            Logger.getLogger(TokenOperacaoControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return null;
    }

    public void validarToken(RecursoSistema recursoSistemaSucesso, RecursoSistema recursoSistemaErro) throws Exception {
        try {
            if (UteisValidacao.emptyString(this.getToken())) {
                throw new Exception("Informe um token para continuar!");
            }

            if (!isValidFormatToken()) {
                throw new Exception("Formato inválido de token!");
            }

            TokenOperacaoVO tokenOperacaoVO = getFacade().getTokenOperacao().consultarPeloToken(this.getToken());

            if (tokenOperacaoVO == null) {
                throw new Exception("Token inválido! Informe um token válido para continuar.");
            }

            //validar se o token já foi utilizado
            if (tokenOperacaoVO.isUtilizado()) {
                throw new Exception("Token já utilizado! Você precisa gerar um novo token.");
            }

            //validar se o token gerado é do mesmo usuário logado
            if (!Integer.valueOf(tokenOperacaoVO.getUsuario()).equals(getUsuarioLogado().getCodigo())) {
                throw new Exception("Token inválido! O token informado foi gerado para outro usuário logado.");
            }

            Date dataHoraTokenGerado = Uteis.getDate(Uteis.getDataAplicandoFormatacao(tokenOperacaoVO.getDataRegistro(), "dd/MM/yyyy HH:mm:ss"), "dd/MM/yyyy HH:mm:ss");
            //Padrão atual: 60 segundos (1 minuto)
            Integer tempoSegundosExpiracaoToken = Integer.valueOf(PropsService.getPropertyValue(PropsService.tempoSegundosExpiracaoTokenOperacao));
            if (Calendario.diferencaEmSegundos(dataHoraTokenGerado, Calendario.hoje()) >= tempoSegundosExpiracaoToken) {
                throw new Exception("Token Expirado! Você precisa gerar um novo token.");
            }
            //Chegou até aqui deu sucesso no token, notificar recurso Empresa no OAMD
            notificarRecursoEmpresa(recursoSistemaSucesso);
        } catch (Exception ex) {
            //Chegou aqui, não validou com sucesso, notificar recurso Empresa no OAMD e depois lançar exceção
                notificarRecursoEmpresa(recursoSistemaErro);
                throw ex;
        }
    }

    public void inutilizarToken() throws Exception {
        getFacade().getTokenOperacao().inutilizarToken(this.getToken());
    }

    public String getToken() {
        return getInput1() + getInput2() + getInput3() + getInput4() + getInput5() + getInput6();
    }

    public boolean isValidFormatToken() {
        if (getToken().length() > 6 || getToken().length() < 6) {
            return false;
        }
        return true;
    }

    public Object getControl() {
        return control;
    }

    public void setControl(Object control) {
        this.control = control;
    }


    public String getMetodoInvocarAoClicarBotaoValidar() {
        return metodoInvocarAoClicarBotaoValidar;
    }

    public void setMetodoInvocarAoClicarBotaoValidar(String metodoInvocarAoClicarBotaoValidar) {
        this.metodoInvocarAoClicarBotaoValidar = metodoInvocarAoClicarBotaoValidar;
    }

    public String getMetodoInvocarAoClicarBotaoNao() {
        return metodoInvocarAoClicarBotaoNao;
    }

    public void setMetodoInvocarAoClicarBotaoNao(String metodoInvocarAoClicarBotaoNao) {
        this.metodoInvocarAoClicarBotaoNao = metodoInvocarAoClicarBotaoNao;
    }

    public String getReRenderComponents() {
        return reRenderComponents;
    }

    public void setReRenderComponents(String reRenderComponents) {
        this.reRenderComponents = reRenderComponents;
    }

    public String getMensagemApresentar() {
        return mensagemApresentar;
    }

    public void setMensagemApresentar(String mensagemApresentar) {
        this.mensagemApresentar = mensagemApresentar;
    }

    public String getOnCompleteBotaoValidar() {
        if (onCompleteBotaoValidar == null) {
            onCompleteBotaoValidar = "";
        }
        return onCompleteBotaoValidar;
    }

    public void setOnCompleteBotaoValidar(String onCompleteBotaoValidar) {
        this.onCompleteBotaoValidar = onCompleteBotaoValidar;
    }

    public String getOnCompleteBotaoNao() {
        if (onCompleteBotaoNao == null) {
            onCompleteBotaoNao = "";
        }
        return onCompleteBotaoNao;
    }

    public void setOnCompleteBotaoNao(String onCompleteBotaoNao) {
        this.onCompleteBotaoNao = onCompleteBotaoNao;
    }

    public String getOnCompleteBotaoFechar() {
        if (onCompleteBotaoFechar == null) {
            onCompleteBotaoFechar = "";
        }
        return onCompleteBotaoFechar;
    }

    public void setOnCompleteBotaoFechar(String onCompleteBotaoFechar) {
        this.onCompleteBotaoFechar = onCompleteBotaoFechar;
    }

    public String getLabelsCamposExibirNoModalTokenOperacao() {
        return labelsCamposExibirNoModalTokenOperacao;
    }

    public void setLabelsCamposExibirNoModalTokenOperacao(String labelsCamposExibirNoModalTokenOperacao) {
        this.labelsCamposExibirNoModalTokenOperacao = labelsCamposExibirNoModalTokenOperacao;
    }

    public Integer getQtdCamposAlterados() {
        return qtdCamposAlterados;
    }

    public void setQtdCamposAlterados(Integer qtdCamposAlterados) {
        this.qtdCamposAlterados = qtdCamposAlterados;
    }

    public String getInput1() {
        if (UteisValidacao.emptyString(input1)) {
            return "";
        }
        return input1.toUpperCase();
    }

    public void setInput1(String input1) {
        this.input1 = input1;
    }

    public String getInput2() {
        if (UteisValidacao.emptyString(input2)) {
            return "";
        }
        return input2.toUpperCase();
    }

    public void setInput2(String input2) {
        this.input2 = input2;
    }

    public String getInput3() {
        if (UteisValidacao.emptyString(input3)) {
            return "";
        }
        return input3.toUpperCase();
    }

    public void setInput3(String input3) {
        this.input3 = input3;
    }

    public String getInput4() {
        if (UteisValidacao.emptyString(input4)) {
            return "";
        }
        return input4.toUpperCase();
    }

    public void setInput4(String input4) {
        this.input4 = input4;
    }

    public String getInput5() {
        if (UteisValidacao.emptyString(input5)) {
            return "";
        }
        return input5.toUpperCase();
    }

    public void setInput5(String input5) {
        this.input5 = input5;
    }

    public String getInput6() {
        if (UteisValidacao.emptyString(input6)) {
            return "";
        }
        return input6.toUpperCase();
    }

    public void setInput6(String input6) {
        this.input6 = input6;
    }
}

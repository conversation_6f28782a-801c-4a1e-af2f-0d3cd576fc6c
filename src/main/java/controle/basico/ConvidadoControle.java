package controle.basico;

import br.com.pactosolucoes.comuns.notificacao.RecursoSistema;
import controle.arquitetura.SuperControle;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.ConviteVO;
import negocio.comuns.plano.PlanoVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.ControleAcesso;

import java.util.ArrayList;
import java.util.List;

public class ConvidadoControle extends SuperControle {

    private ClienteVO anfitriao = new ClienteVO();
    private ClienteVO convidado = new ClienteVO();
    private PlanoVO plano;
    private List<ConviteVO> historico;
    private Boolean cadastrandoConvidado = false;
    private Integer convitesUsados = 0;


    public void abrirHistoricoConvidado() throws Exception {
        TelaClienteControle telaControle = (TelaClienteControle) context().getExternalContext().getRequestMap().get(TelaClienteControle.class.getSimpleName());
        if (telaControle != null && telaControle.getCliente() != null && !UteisValidacao.emptyNumber(telaControle.getCliente().getCodigo())) {
            anfitriao = telaControle.getCliente();
        }
        historico = getFacade().getConvite().historico(anfitriao.getCodigo());
        notificarRecursoEmpresa(RecursoSistema.HISTORICO_CONVIDADO_CLIENTE);
    }

    public void cadastrarConvidado() throws Exception{
        notificarRecursoEmpresa(RecursoSistema.CONVIDADO_CADASTRO_CLIENTE);
        redirect("/faces/inclusaoAlunoVenda.jsp?convidou="+anfitriao.getMatricula()+"&convidado="+convidado.getNome_Apresentar());
    }

    public void cancelarCadastro(){
        cadastrandoConvidado = false;
    }

    public void lancarConvite(){
        try {
            getFacade().getConvite().lancarConvite(getUsuarioLogado(), convidado, anfitriao);
            montarSucesso("convite_lancado_sucesso");
            setMsgAlert("Richfaces.hideModalPanel('modalConvidado');" + getMensagemNotificar());
            notificarRecursoEmpresa(RecursoSistema.CONVIDADO_LANCADO_SUCESSO);
        }catch (Exception e){
            montarErro(e);
            setMsgAlert(getMensagemNotificar());
        }
    }

    public void limparConvidado(){
        convidado = new ClienteVO();
    }


    public void selecionarConvidadoSuggestionBox() throws Exception {
        ClienteVO conv = (ClienteVO) request().getAttribute("result");
        if (conv != null) {
            convidado = conv;
        }
    }

    public List<ClienteVO> executarAutocomplete(Object suggest) {
        List<ClienteVO> result = new ArrayList<ClienteVO>();
        try {
            String pref = (String) suggest;
            int codigoEmpresa = getAnfitriao().getEmpresa().getCodigo();
            if (ControleAcesso.isPermiteMultiEmpresas() && permissao("ConsultarAlunosCaixaAbertoTodasEmpresas")) {
                codigoEmpresa = 0;
            }
            result = getFacade().getCliente().consultarPorNomePessoa(pref, codigoEmpresa, Uteis.NIVELMONTARDADOS_DADOSBASICOS, 20, "VI");
        } catch (Exception ex) {
            setMensagemDetalhada("msg_erro", ex.getMessage());
        }
        return result;
    }

    public void novoConvidado() {
        notificarRecursoEmpresa(RecursoSistema.CONVITE_CLIENTE);
        limparMsg();
        convidado = new ClienteVO();
        TelaClienteControle telaControle = (TelaClienteControle) context().getExternalContext().getRequestMap().get(TelaClienteControle.class.getSimpleName());
        if (telaControle != null && telaControle.getCliente() != null && !UteisValidacao.emptyNumber(telaControle.getCliente().getCodigo())) {
            anfitriao = telaControle.getCliente();
            try {
                convitesUsados = getFacade().getConvite().totalizarMes(anfitriao.getCodigo());
                if(!UteisValidacao.emptyList(telaControle.getListaContratos())){
                    plano = telaControle.getListaContratos().get(0).getPlano();
                }
            }catch (Exception e){
                e.printStackTrace();
            }
        }

        if(convitesUsados < telaControle.getConvitesDireito()){
            setMsgAlert("Richfaces.showModalPanel('modalConvidado');");
        }else{
            montarInfo("Todos os convites disponíveis para este mês já foram utilizados!");
            setMsgAlert(getMensagemNotificar());
        }

    }

    public ClienteVO getConvidado() {
        return convidado;
    }

    public void setConvidado(ClienteVO convidado) {
        this.convidado = convidado;
    }

    public ClienteVO getAnfitriao() {
        return anfitriao;
    }

    public void setAnfitriao(ClienteVO anfitriao) {
        this.anfitriao = anfitriao;
    }

    public PlanoVO getPlano() {
        return plano;
    }

    public void setPlano(PlanoVO plano) {
        this.plano = plano;
    }

    public List<ConviteVO> getHistorico() {
        return historico;
    }

    public void setHistorico(List<ConviteVO> historico) {
        this.historico = historico;
    }

    public Boolean getCadastrandoConvidado() {
        return cadastrandoConvidado;
    }

    public void setCadastrandoConvidado(Boolean cadastrandoConvidado) {
        this.cadastrandoConvidado = cadastrandoConvidado;
    }

    public Integer getConvitesUsados() {
        return convitesUsados;
    }

    public void setConvitesUsados(Integer convitesUsados) {
        this.convitesUsados = convitesUsados;
    }
}

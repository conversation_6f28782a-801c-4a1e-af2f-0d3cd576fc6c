package controle.basico;

import java.util.Iterator;
import java.util.Date;

import br.com.pactosolucoes.enumeradores.TipoColaboradorEnum;
import edu.emory.mathcs.backport.java.util.Collections;
import negocio.facade.jdbc.basico.QuestionarioCliente;
import negocio.comuns.utilitarias.*;
import negocio.comuns.basico.*;
import javax.faces.model.SelectItem;
import java.util.List;
import java.util.ArrayList;
import controle.arquitetura.SuperControle;
import static controle.arquitetura.SuperControle.registrarLogErroObjetoVO;
import static controle.arquitetura.SuperControle.registrarLogObjetoVO;
import negocio.comuns.arquitetura.LogVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.crm.EventoVO;

/**
 * Classe responsável por implementar a interação entre os componentes JSF das
 * páginas
 * questionarioClienteForm.jsp questionarioClienteCons.jsp) com as
 * funcionalidades da classe <code>QuestionarioCliente</code>.
 * Implemtação da camada controle (Backing Bean).
 * 
 * @see SuperControle
 * @see QuestionarioCliente
 * @see QuestionarioClienteVO
 */
public class QuestionarioClienteControle extends SuperControle {

    private QuestionarioClienteVO questionarioClienteVO;
    protected List listaSelectConsultor;
    protected List listaSelectQuestionario;
    private QuestionarioClienteVO questionarioClienteVoSemAlteracao;
    /**
     * Interface <code>QuestionarioClienteInterfaceFacade</code> responsável
     * pela interconexão da camada de controle com a camada de negócio.
     * Criando uma independência da camada de controle com relação a tenologia
     * de persistência dos dados (DesignPatter: Façade).
     */
    private QuestionarioPerguntaClienteVO questionarioPerguntaClienteVO;
    private RespostaPergClienteVO repostaPergClienteVO;
    private Boolean apresentarBotaoAtualizar = true;
    private boolean apresentarBotaoAlterarConsultorBV = false;
    private boolean apresentarTextoQuestionario= false;
    
    public QuestionarioClienteControle() throws Exception {
        obterUsuarioLogado();
        inicializarFacades();
        setControleConsulta(new ControleConsulta());
        setMensagemID("msg_entre_prmconsulta");
        novo();
    }

    /**
     * Rotina responsável por disponibilizar um novo objeto da classe
     * <code>QuestionarioCliente</code> para edição pelo usuário da aplicação.
     */
    public String novo() throws Exception {
        try {
            setQuestionarioClienteVO(new QuestionarioClienteVO());
            setQuestionarioPerguntaClienteVO(new QuestionarioPerguntaClienteVO());
            setRepostaPergClienteVO(new RespostaPergClienteVO());
            inicializarListasSelectItemTodosComboBox();
            inicializarCliente();
            validarPermissaoAlterarConsultorBV();
            setQuestionarioClienteVoSemAlteracao((QuestionarioClienteVO)getQuestionarioClienteVO().getClone(true));
            
            setMensagemID("msg_entre_dados");
            return "";
        } catch (Exception e) {
            inicializarListasSelectItemTodosComboBox();
            setMensagemDetalhada("msg_erro", e.getMessage());
            return "";

        }
    }

    public String abrirTelaBV(){
        try {
            setQuestionarioClienteVO(new QuestionarioClienteVO());
            setQuestionarioPerguntaClienteVO(new QuestionarioPerguntaClienteVO());
            setRepostaPergClienteVO(new RespostaPergClienteVO());
            inicializarListasSelectItemTodosComboBox();
            inicializarCliente();
            if(!UteisValidacao.emptyList(listaSelectQuestionario)){
                if(getListaSelectQuestionario().size() > 1){
                        Ordenacao.ordenarLista(getListaSelectQuestionario(),"data");
                        Collections.reverse(getListaSelectQuestionario());
                }
                selecionarQuestionario((QuestionarioClienteVO) getListaSelectQuestionario().get(0));
            }
            
            setQuestionarioClienteVoSemAlteracao((QuestionarioClienteVO)getQuestionarioClienteVO().getClone(true));
            setMensagemID("msg_entre_dados");
            return "";
        } catch (Exception e) {
            inicializarListasSelectItemTodosComboBox();
            setMensagemDetalhada("msg_erro", e.getMessage());
            return "";

        }
    }
    private void selecionarQuestionario(QuestionarioClienteVO quest) throws Exception{
        setQuestionarioClienteVO((quest == null ? new QuestionarioClienteVO() : quest));
        if (getQuestionarioClienteVO().getQuestionario().getCodigo().intValue() != 0) {
            inicializarQuestionarioRespondido(getQuestionarioClienteVO().getQuestionarioPerguntaClienteVOs());
        } else {
            getQuestionarioClienteVO().setQuestionarioPerguntaClienteVOs(new ArrayList());
        }
        getQuestionarioClienteVO().getConsultorAnterior().setCodigo(getQuestionarioClienteVO().getConsultor().getCodigo());
    }
    
    public void validarPermissaoAlterarConsultorBV(){
        try {
            permissaoFuncionalidade(getUsuarioLogado(), "PermiteAlterarConsultorBV", "2.71 - Permite alterar consultor BV");
            
            setApresentarBotaoAlterarConsultorBV(true);
        } catch (Exception e) {
            setApresentarBotaoAlterarConsultorBV(false);
        }
    }
    
    /**
     * Rotina responsável por disponibilizar os dados de um objeto da classe
     * <code>QuestionarioCliente</code> para alteração.
     * O objeto desta classe é disponibilizado na session da página (request)
     * para que o JSP correspondente possa disponibilizá-lo para edição.
     */
    public String editar() {
        QuestionarioClienteVO obj = (QuestionarioClienteVO) context().getExternalContext().getRequestMap().get("questionarioCliente");
        inicializarAtributosRelacionados(obj);
        obj.setNovoObj(false);
        setQuestionarioClienteVO(obj);
        getQuestionarioClienteVO().getConsultorAnterior().setCodigo(getQuestionarioClienteVO().getConsultor().getCodigo());
        inicializarListasSelectItemTodosComboBox();
        setQuestionarioPerguntaClienteVO(new QuestionarioPerguntaClienteVO());
        setRepostaPergClienteVO(new RespostaPergClienteVO());
        setMensagemID("msg_dados_editar");
        return "editar";
    }

    /**
     * Método responsável inicializar objetos relacionados a classe
     * <code>QuestionarioClienteVO</code>.
     * Esta inicialização é necessária por exigência da tecnologia JSF, que não
     * trabalha com valores nulos para estes atributos.
     */
    public void inicializarAtributosRelacionados(QuestionarioClienteVO obj) {
        if (obj.getQuestionario() == null) {
            obj.setQuestionario(new QuestionarioVO());
        }
        if (obj.getCliente() == null) {
            obj.setCliente(new ClienteVO());
        }
        if (obj.getConsultor() == null) {
            obj.setConsultor(new ColaboradorVO());
        }
    }

    /**
     * Rotina responsável por gravar no BD os dados editados de um novo objeto
     * da classe <code>QuestionarioCliente</code>.
     * Caso o objeto seja novo (ainda não gravado no BD) é acionado a operação
     * <code>incluir()</code>. Caso contrário é acionado o
     * <code>alterar()</code>.
     * Se houver alguma inconsistência o objeto não é gravado, sendo
     * re-apresentado para o usuário juntamente com uma mensagem de erro.
     */
    public void gravar() {
        try {
            if (questionarioClienteVO.isNovoObj().booleanValue()) {
                getFacade().getQuestionarioCliente().incluir(questionarioClienteVO, true);
            } else {
                getFacade().getQuestionarioCliente().alterar(questionarioClienteVO, true);
            }
            montarSucessoDadosGravados();
        } catch (Exception e) {
            montarErro(e);
        }
    }
    public void gravarComConfiguracao() {
        try {
            if (questionarioClienteVO.isNovoObj().booleanValue()) {
                getFacade().getQuestionarioCliente().incluir(questionarioClienteVO, getEmpresaLogado().isBvObrigatorio());
            } else {
                getFacade().getQuestionarioCliente().alterar(questionarioClienteVO, getEmpresaLogado().isBvObrigatorio());
                alterarConsultorContrato();
            }
            montarSucessoDadosGravados();
            incluirLog();
            setQuestionarioClienteVoSemAlteracao((QuestionarioClienteVO)getQuestionarioClienteVO().getClone(true));
        } catch (Exception e) {
            montarErro(e);
        }
    }
    
    public void validarDadosAlteracao(){
        if (!getQuestionarioClienteVoSemAlteracao().getConsultor().getCodigo().equals(questionarioClienteVO.getConsultor().getCodigo())) {
            apresentarTextoQuestionario = true;
        }else{
            apresentarTextoQuestionario = false;
        }
        
    }
    
    public void alterarConsultorContrato() throws Exception{
        Integer dias = getFacade().getConfiguracaoSistema().obterDiasQuestionarioPorTipo(questionarioClienteVO.getQuestionario().getCodigo());
        Date dataFinal = Uteis.somarDias(questionarioClienteVO.getData(), dias);
        Integer mesReferencia = Uteis.getMesData(questionarioClienteVO.getData());
        List<ContratoVO> listaContrato = getFacade().getContrato().consultarContratoPorDataVigenciaBV(questionarioClienteVO.getCliente().getPessoa().getCodigo(), 
                questionarioClienteVO.getData(), dataFinal, mesReferencia, questionarioClienteVO.getConsultor().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);
        for (ContratoVO contratoVO : listaContrato) {
            contratoVO.setConsultor(questionarioClienteVO.getConsultor());
            getFacade().getContrato().alterarConsultorContrato(contratoVO);
        }
    }
    
    public void incluirLog() throws Exception{
        try {
            if (!getQuestionarioClienteVoSemAlteracao().getConsultor().getCodigo().equals(questionarioClienteVO.getConsultor().getCodigo())) {
                Integer empresa = 0;
                if (!getEmpresaLogado().getCodigo().equals(new Integer(0)) && (!getEmpresaLogado().getCodigo().equals(null))) {
                    empresa = getEmpresaLogado().getCodigo().intValue();
                }
                ColaboradorVO colaboradorVO = getFacade().getColaborador().consultarColaboradorPorCodigo(questionarioClienteVO.getConsultor().getCodigo(), empresa,false, Uteis.NIVELMONTARDADOS_MINIMOS);
                LogVO log = new LogVO();
                log.setChavePrimaria(questionarioClienteVO.getCodigo().toString());
                log.setNomeEntidade("QuestionarioCliente");
                log.setNomeEntidadeDescricao("Questionario Cliente");
                log.setOperacao("ALTERAÇÃO DE CONSULTOR BV");
                log.setNomeCampo("Consultor:");
                log.setValorCampoAnterior(getQuestionarioClienteVoSemAlteracao().getConsultor().getPessoa().getNome());
                log.setValorCampoAlterado(colaboradorVO.getPessoa().getNome());
                log.setResponsavelAlteracao(getUsuarioLogado().getNome());
                log.setDataAlteracao(Calendario.hoje());
                log.setUserOAMD(this.getUsuarioLogado().getUserOamd());
                registrarLogObjetoVO(log,questionarioClienteVO.getCliente().getPessoa().getCodigo());
            }
        } catch (Exception e) {
            registrarLogErroObjetoVO("QuestionarioCliente", questionarioClienteVO.getCodigo(), "ERRO AO GERAR LOG DO QUESTIONARIOCLIENTEVO", this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
        }
        
    }
    
    /**
     * Rotina responsavel por executar as consultas disponiveis no JSP
     * QuestionarioClienteCons.jsp.
     * Define o tipo de consulta a ser executada, por meio de ComboBox
     * denominado campoConsulta, disponivel neste mesmo JSP.
     * Como resultado, disponibiliza um List com os objetos selecionados na
     * sessao da pagina.
     */
    public String consultar() {
        try {
            super.consultar();
            List objs = new ArrayList();
            if (getControleConsulta().getCampoConsulta().equals("codigo")) {
                if (getControleConsulta().getValorConsulta().equals("")) {
                    getControleConsulta().setValorConsulta("0");
                }
                int valorInt = Integer.parseInt(getControleConsulta().getValorConsulta());
                objs = getFacade().getQuestionarioCliente().consultarPorCodigo(new Integer(valorInt), true, Uteis.NIVELMONTARDADOS_TODOS);
            }
            if (getControleConsulta().getCampoConsulta().equals("descricaoQuestionario")) {
                objs = getFacade().getQuestionarioCliente().consultarPorDescricaoQuestionario(getControleConsulta().getValorConsulta(), Uteis.NIVELMONTARDADOS_TODOS);
            }
            if (getControleConsulta().getCampoConsulta().equals("matriculaCliente")) {
                objs = getFacade().getQuestionarioCliente().consultarPorMatriculaCliente(getControleConsulta().getValorConsulta(), Uteis.NIVELMONTARDADOS_TODOS);
            }
            if (getControleConsulta().getCampoConsulta().equals("situacaoColaborador")) {
                objs = getFacade().getQuestionarioCliente().consultarPorSituacaoColaborador(getControleConsulta().getValorConsulta(), Uteis.NIVELMONTARDADOS_TODOS);
            }
            if (getControleConsulta().getCampoConsulta().equals("data")) {
                Date valorData = Uteis.getDate(getControleConsulta().getValorConsulta());
                objs = getFacade().getQuestionarioCliente().consultarPorData(Uteis.getDateTime(valorData, 0, 0, 0), Uteis.getDateTime(valorData, 23, 59, 59),0, true, Uteis.NIVELMONTARDADOS_TODOS);
            }
            objs = ControleConsulta.obterSubListPaginaApresentar(objs, controleConsulta);
            definirVisibilidadeLinksNavegacao(controleConsulta.getPaginaAtual(), controleConsulta.getNrTotalPaginas());
            setListaConsulta(objs);
            setMensagemID("msg_dados_consultados");
            return "consultar";
        } catch (Exception e) {
            setListaConsulta(new ArrayList());
            setMensagemDetalhada("msg_erro", e.getMessage());
            return "consultar";
        }
    }

    public void validarConsultorInativo() {
        boolean existeConsultor = false;
        Iterator i = getListaSelectConsultor().iterator();
        while (i.hasNext()) {
            SelectItem obj = (SelectItem) i.next();
            if (obj.getValue().equals(getQuestionarioClienteVO().getConsultor().getCodigo())) {
                existeConsultor = true;
                break;
            }
        }
        if (!existeConsultor) {
            getListaSelectConsultor().add(
                    new SelectItem(getQuestionarioClienteVO().getConsultor().getCodigo(),
                        getQuestionarioClienteVO().getConsultor().getPessoa().getNome().toString()));
        }
    }

    public void inicializarCliente() throws Exception {
        ClienteControle clienteControle = (ClienteControle) context().getExternalContext().getSessionMap().get("ClienteControle");
        if (clienteControle != null) {
            clienteControle.pegarClienteTelaCliente();
            if (!UteisValidacao.emptyNumber(clienteControle.getClienteVO().getCodigo())) {
                inicializarCliente(clienteControle.getClienteVO());
            }
        }
    }

    public void inicializarCliente(ClienteVO clienteVO) throws Exception {
        if (!UteisValidacao.emptyNumber(clienteVO.getCodigo())) {
            questionarioClienteVO.setCliente(clienteVO);
            List resultadoConsulta = getFacade().getQuestionarioCliente().consultarPorCodigoCliente(questionarioClienteVO.getCliente().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);
            setListaSelectQuestionario(resultadoConsulta);
        }
    }

    public void inicializarClienteCRM() throws Exception {
        try {
            ClienteControle clienteControle = (ClienteControle) context().getExternalContext().getSessionMap().get("ClienteControle");
            if (clienteControle != null && clienteControle.getClienteVO().getCodigo().intValue() != 0) {
                questionarioClienteVO.setCliente(clienteControle.getClienteVO());
                List resultadoConsulta = getFacade().getQuestionarioCliente().consultarPorCodigoCliente(questionarioClienteVO.getCliente().getCodigo().intValue(), Uteis.NIVELMONTARDADOS_TODOS);
                setListaSelectQuestionario(resultadoConsulta);
                int tamanho = getListaSelectQuestionario().size();
                tamanho--;
                QuestionarioClienteVO quest = (QuestionarioClienteVO) getListaSelectQuestionario().get(tamanho);
                setQuestionarioClienteVO((quest == null ? new QuestionarioClienteVO() : quest));
                if (getQuestionarioClienteVO().getQuestionario().getCodigo().intValue() != 0) {
                    inicializarQuestionarioRespondido(getQuestionarioClienteVO().getQuestionarioPerguntaClienteVOs());
                } else {
                    getQuestionarioClienteVO().setQuestionarioPerguntaClienteVOs(new ArrayList());
                }
                getQuestionarioClienteVO().getConsultorAnterior().setCodigo(getQuestionarioClienteVO().getConsultor().getCodigo());
            }
        } catch (Exception e) {
            throw e;
        }
    }

    public void inicializarCliente(Integer codigoCliente) throws Exception {
        try {
            questionarioClienteVO.setCliente(getFacade().getCliente().consultarPorChavePrimaria(codigoCliente, Uteis.NIVELMONTARDADOS_MINIMOS));
            List resultadoConsulta = getFacade().getQuestionarioCliente().consultarPorCodigoCliente(questionarioClienteVO.getCliente().getCodigo().intValue(), Uteis.NIVELMONTARDADOS_TODOS);
            setListaSelectQuestionario(resultadoConsulta);
            int tamanho = getListaSelectQuestionario().size();
            tamanho--;
            QuestionarioClienteVO quest = (QuestionarioClienteVO) getListaSelectQuestionario().get(tamanho);
//			setQuestionarioClienteVO((quest == null ? new QuestionarioClienteVO() : quest));
//			if (getQuestionarioClienteVO().getQuestionario().getCodigo().intValue() != 0) {
//				inicializarQuestionarioRespondido(getQuestionarioClienteVO().getQuestionarioPerguntaClienteVOs());
//			} else {
//				getQuestionarioClienteVO().setQuestionarioPerguntaClienteVOs(new ArrayList());
//			}


        } catch (Exception e) {
            throw e;
        }
    }

    /**
     * Método responsável por gerar uma lista de objetos do tipo
     * <code>SelectItem</code> para preencher
     * o comboBox relativo ao atributo <code>Evento</code>.
     */
    public List<SelectItem> getListaSelectEvento() throws Exception {
        List resultadoConsulta = getFacade().getEvento().consultarPorNomeEvento("", Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        Iterator i = resultadoConsulta.iterator();
        List<SelectItem> objs = new ArrayList<SelectItem>();
        objs.add(new SelectItem(new Integer(0), ""));
        while (i.hasNext()) {
            EventoVO obj = (EventoVO) i.next();
            objs.add(new SelectItem(obj.getCodigo(), obj.getDescricao()));
        }
        return objs;
    }


    public void questionarioRespondidoPeloCliente() throws Exception {
        List resultadoConsulta = getFacade().getQuestionarioCliente().consultarPorCodigoCliente(questionarioClienteVO.getCliente().getCodigo().intValue(), Uteis.NIVELMONTARDADOS_TODOS);
        setListaSelectQuestionario(resultadoConsulta);
    }

    public void selecionarQuestionarioSerRespondidoPeloCliente() throws Exception {
        setMensagemID("msg_entre_dados");
        QuestionarioClienteVO quest = (QuestionarioClienteVO) context().getExternalContext().getRequestMap().get("questionario");
        setQuestionarioClienteVO((quest == null ? new QuestionarioClienteVO() : quest));
        if (getQuestionarioClienteVO().getQuestionario().getCodigo().intValue() != 0) {
            inicializarQuestionarioRespondido(getQuestionarioClienteVO().getQuestionarioPerguntaClienteVOs());
        } else {
            getQuestionarioClienteVO().setQuestionarioPerguntaClienteVOs(new ArrayList());
        }
        getQuestionarioClienteVO().getConsultorAnterior().setCodigo(getQuestionarioClienteVO().getConsultor().getCodigo());
        setQuestionarioClienteVoSemAlteracao((QuestionarioClienteVO)getQuestionarioClienteVO().getClone(true));
       // getQuestionarioClienteVO().setEventoVO(getFacade().getEvento().consultarPorNomeEvento(getQuestionarioClienteVO().getEventoVO().getDescricao(),, aasEntidade));
    }

    public void selecionarQuestionarioSerRespondidoPeloClienteCRM(QuestionarioClienteVO quest) throws Exception {
        setQuestionarioClienteVO((quest == null ? new QuestionarioClienteVO() : quest));
        if (getQuestionarioClienteVO().getQuestionario().getCodigo().intValue() != 0) {
            inicializarQuestionarioRespondido(getQuestionarioClienteVO().getQuestionarioPerguntaClienteVOs());

        } else {
            getQuestionarioClienteVO().setQuestionarioPerguntaClienteVOs(new ArrayList());
        }
        getQuestionarioClienteVO().getConsultorAnterior().setCodigo(getQuestionarioClienteVO().getConsultor().getCodigo());
    }

    /**
     * Metodo que realiza a verificação da opcao marcado pelo cliente, pois a
     * perguntas que poderao ter somente uma resposta
     * e para isso o metodo necessita do atributo marcado que e inicializado
     * pelos metodos INICIALIZARQUESTIONARIO E
     * INICIALIZARQUESTIONARIORESPONDIDO.
     * */
    public void escolhaSimples() {
        RespostaPergClienteVO obj = (RespostaPergClienteVO) context().getExternalContext().getRequestMap().get("repostaPergCliente");
        Iterator i = questionarioClienteVO.getQuestionarioPerguntaClienteVOs().iterator();
        while (i.hasNext()) {
            QuestionarioPerguntaClienteVO objExistente = (QuestionarioPerguntaClienteVO) i.next();
            if (objExistente.getPerguntaCliente().getSimples()) {
                Iterator j = objExistente.getPerguntaCliente().getRespostaPergClienteVOs().iterator();
                while (j.hasNext()) {
                    RespostaPergClienteVO resposta = (RespostaPergClienteVO) j.next();
                    if (obj.getMarcado().equals(resposta.getMarcado()) && obj.getPerguntaCliente().intValue()==resposta.getPerguntaCliente().intValue()) {
                        if (obj.getDescricaoRespota().equals(resposta.getDescricaoRespota())){
                            resposta.setRespostaOpcao(true);
                        } else {
                            resposta.setRespostaOpcao(false);
                        }
                    }
                }
            }
        }
    }

    /**
     * Metodo que recebe a lista do questionario e realiza sua montagem onde
     * passa pelo processo de 2 iteraçoes onde a primeira
     * monta as pergunta do questionario e colocando na lista de pergunta do
     * cliente e a outra iteração monta as possiveis resposta
     * para pergunta e colocando na lista de respota do cliente. E atributo cont
     * e necessario pois ele e fundamental para outro metodo
     * na hora de validar os dados marcado pelo cliente.
     * */
    public void inicializarQuestionario(List questionario) {
        int cont = 0;
        Iterator i = questionario.iterator();
        while (i.hasNext()) {
            QuestionarioPerguntaVO objExistente = (QuestionarioPerguntaVO) i.next();
            questionarioPerguntaClienteVO.getPerguntaCliente().setDescricao(objExistente.getPergunta().getDescricao());
            questionarioPerguntaClienteVO.getPerguntaCliente().setTipoPergunta(objExistente.getPergunta().getTipoPergunta());
            if (objExistente.getPergunta().getTipoPergunta().equals("SE") || objExistente.getPergunta().getTipoPergunta().equals("SN")) {
                questionarioPerguntaClienteVO.getPerguntaCliente().setSimples(new Boolean(true));
            }
            if (objExistente.getPergunta().getTipoPergunta().equals("ME")) {
                questionarioPerguntaClienteVO.getPerguntaCliente().setMultipla(new Boolean(true));
            }
            if (objExistente.getPergunta().getTipoPergunta().equals("TE")) {
                questionarioPerguntaClienteVO.getPerguntaCliente().setTextual(new Boolean(true));
            }
            Iterator j = objExistente.getPergunta().getRespostaPerguntaVOs().iterator();
            if (!j.hasNext()) {
                repostaPergClienteVO.setMarcado(cont);
                questionarioPerguntaClienteVO.getPerguntaCliente().getRespostaPergClienteVOs().add(repostaPergClienteVO);
                setRepostaPergClienteVO(new RespostaPergClienteVO());
            } else {
                while (j.hasNext()) {
                    RespostaPerguntaVO objrespota = (RespostaPerguntaVO) j.next();
                    repostaPergClienteVO.setDescricaoRespota(objrespota.getDescricaoRespota());
                    repostaPergClienteVO.setMarcado(cont);
                    questionarioPerguntaClienteVO.getPerguntaCliente().getRespostaPergClienteVOs().add(repostaPergClienteVO);
                    setRepostaPergClienteVO(new RespostaPergClienteVO());
                }
            }
            cont++;
            questionarioClienteVO.getQuestionarioPerguntaClienteVOs().add(questionarioPerguntaClienteVO);
            setQuestionarioPerguntaClienteVO(new QuestionarioPerguntaClienteVO());
        }

    }

    /**
     * Metodo que recebe a lista do questionario ja respondido e realiza sua
     * montagem e colocando o atributo cont para realização
     * no metodo Escolha simples
     * */
    public void inicializarQuestionarioRespondido(List questionario) {
        validarConsultorInativo();
        int cont = 0;
        getQuestionarioClienteVO().setQuestionarioPerguntaClienteVOs(new ArrayList());
        Iterator i = questionario.iterator();
        while (i.hasNext()) {
            QuestionarioPerguntaClienteVO objExistente = (QuestionarioPerguntaClienteVO) i.next();
            Iterator j = objExistente.getPerguntaCliente().getRespostaPergClienteVOs().iterator();
            int index = 0;
            while (j.hasNext()) {
                RespostaPergClienteVO objrespota = (RespostaPergClienteVO) j.next();
                objrespota.setMarcado(cont);
                objExistente.getPerguntaCliente().getRespostaPergClienteVOs().set(index, objrespota);
                index++;
            }
            cont++;
            questionarioClienteVO.getQuestionarioPerguntaClienteVOs().add(objExistente);
        }

    }

    /**
     * Operação responsável por processar a exclusão um objeto da classe
     * <code>QuestionarioClienteVO</code> Após a exclusão ela automaticamente
     * aciona a rotina para uma nova inclusão.
     */
    public String excluir() {
        try {
            getFacade().getQuestionarioCliente().excluir(questionarioClienteVO);
            setQuestionarioClienteVO(new QuestionarioClienteVO());
            setQuestionarioPerguntaClienteVO(new QuestionarioPerguntaClienteVO());
            setMensagemID("msg_dados_excluidos");
            return "editar";
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            return "erro";
        }
    }

    public void irPaginaInicial() throws Exception {
        controleConsulta.setPaginaAtual(1);
        this.consultar();
    }

    public void irPaginaAnterior() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getPaginaAtual() - 1);
        this.consultar();
    }

    public void irPaginaPosterior() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getPaginaAtual() + 1);
        this.consultar();
    }

    public void irPaginaFinal() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getNrTotalPaginas());
        this.consultar();
    }

    /**
     * Método responsável por gerar uma lista de objetos do tipo
     * <code>SelectItem</code> para preencher
     * o comboBox relativo ao atributo <code>Consultor</code>.
     */
    public void montarListaSelectItemConsultor(String prm) throws Exception {
        Integer empresa = 0;
        if (!getEmpresaLogado().getCodigo().equals(new Integer(0)) && (!getEmpresaLogado().getCodigo().equals(null))) {
            empresa = getEmpresaLogado().getCodigo().intValue();
        }
        List resultadoConsulta = consultarColaboradorPorSituacao(prm, empresa);
        Iterator i = resultadoConsulta.iterator();
        List objs = new ArrayList();
        objs.add(new SelectItem(new Integer(0), ""));
        while (i.hasNext()) {
            ColaboradorVO obj = (ColaboradorVO) i.next();
            objs.add(new SelectItem(obj.getCodigo(), obj.getPessoa().getNome().toString()));
        }
        setListaSelectConsultor(objs);
    }

    /**
     * Método responsável por atualizar o ComboBox relativo ao atributo
     * <code>Consultor</code>.
     * Buscando todos os objetos correspondentes a entidade
     * <code>Colaborador</code>. Esta rotina não recebe parâmetros para
     * filtragem de dados, isto é
     * importante para a inicialização dos dados da tela para o acionamento por
     * meio requisições Ajax.
     */
    public void montarListaSelectItemConsultor() {
        try {
            montarListaSelectItemConsultor("");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * Método responsável por consultar dados da entidade
     * <code><code> e montar o atributo <code>situacao</code> Este atributo é
     * uma lista (<code>List</code>) utilizada para definir os valores a serem
     * apresentados no ComboBox correspondente
     */
    public List consultarColaboradorPorSituacao(String situacaoPrm, Integer empresa) throws Exception {
        return getFacade().getColaborador().consultarPorTipoColaboradorAtivo(TipoColaboradorEnum.CONSULTOR, "AT", empresa, false, Uteis.NIVELMONTARDADOS_TODOS);
    }

    /**
     * Método responsável por inicializar a lista de valores (
     * <code>SelectItem</code>) para todos os ComboBox's.
     */
    public void inicializarListasSelectItemTodosComboBox() {
        // montarListaSelectItemQuestionario();
        montarListaSelectItemConsultor();

    }

    /**
     * Rotina responsável por preencher a combo de consulta da telas.
     */
    public List getTipoConsultaCombo() {
        List itens = new ArrayList();
        itens.add(new SelectItem("descricaoQuestionario", "Questionário"));
        itens.add(new SelectItem("codigo", "Código"));
        itens.add(new SelectItem("matriculaCliente", "Cliente"));
        itens.add(new SelectItem("situacaoColaborador", "Consultor"));
        itens.add(new SelectItem("data", "Data"));
        return itens;
    }

    /**
     * Rotina responsável por organizar a paginação entre as páginas resultantes
     * de uma consulta.
     */
    public String inicializarConsultar() {
        setPaginaAtualDeTodas("0/0");
        setListaConsulta(new ArrayList());
        definirVisibilidadeLinksNavegacao(0, 0);
        setMensagemID("msg_entre_prmconsulta");
        return "consultar";
    }

    public Boolean getApresentarBotoes() {
        if (getQuestionarioClienteVO().getCodigo().intValue() != 0) {
            return true;
        }
        return false;
    }

    /**
     * Operação que inicializa as Interfaces Façades com os respectivos objetos
     * de
     * persistência dos dados no banco de dados.
     */
    protected boolean inicializarFacades() {
        try {
            super.inicializarFacades();

            return true;
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro_conectarBD", e.getMessage());
            return false;
        }
    }

    public QuestionarioPerguntaClienteVO getQuestionarioPerguntaClienteVO() {
        return questionarioPerguntaClienteVO;
    }

    public void setQuestionarioPerguntaClienteVO(QuestionarioPerguntaClienteVO questionarioPerguntaClienteVO) {
        this.questionarioPerguntaClienteVO = questionarioPerguntaClienteVO;
    }

    public List getListaSelectConsultor() {
        return (listaSelectConsultor);
    }

    public void setListaSelectConsultor(List listaSelectConsultor) {
        this.listaSelectConsultor = listaSelectConsultor;
    }

    public List getListaSelectQuestionario() {
        return (listaSelectQuestionario);
    }

    public void setListaSelectQuestionario(List listaSelectQuestionario) {
        this.listaSelectQuestionario = listaSelectQuestionario;
    }

    public QuestionarioClienteVO getQuestionarioClienteVO() {
        return questionarioClienteVO;
    }

    public void setQuestionarioClienteVO(QuestionarioClienteVO questionarioClienteVO) {
        this.questionarioClienteVO = questionarioClienteVO;
    }

    public RespostaPergClienteVO getRepostaPergClienteVO() {
        return repostaPergClienteVO;
    }

    public void setRepostaPergClienteVO(RespostaPergClienteVO repostaPergClienteVO) {
        this.repostaPergClienteVO = repostaPergClienteVO;
    }
    // Metodo usado na tela de organizadorCarteiraForm

    public String getInicializarControladorVazio() {
        return "";

    }

    public void setApresentarBotaoAtualizar(Boolean apresentarBotaoAtualizar) {
        this.apresentarBotaoAtualizar = apresentarBotaoAtualizar;
    }

    public Boolean getApresentarBotaoAtualizar() {
        return apresentarBotaoAtualizar;
    }

    public boolean isApresentarBotaoAlterarConsultorBV() {
        return apresentarBotaoAlterarConsultorBV;
    }

    public void setApresentarBotaoAlterarConsultorBV(boolean apresentarBotaoAlterarConsultorBV) {
        this.apresentarBotaoAlterarConsultorBV = apresentarBotaoAlterarConsultorBV;
    }

    public QuestionarioClienteVO getQuestionarioClienteVoSemAlteracao() {
        return questionarioClienteVoSemAlteracao;
    }

    public void setQuestionarioClienteVoSemAlteracao(QuestionarioClienteVO questionarioClienteVoSemAlteracao) {
        this.questionarioClienteVoSemAlteracao = questionarioClienteVoSemAlteracao;
    }

    public boolean isApresentarTextoQuestionario() {
        return apresentarTextoQuestionario;
    }

    public void setApresentarTextoQuestionario(boolean apresentarTextoQuestionario) {
        this.apresentarTextoQuestionario = apresentarTextoQuestionario;
    }

}

/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package controle.basico;

import br.com.pactosolucoes.comuns.json.JSONMapper;
import br.com.pactosolucoes.comuns.notificacao.RecursoSistema;
import br.com.pactosolucoes.comuns.util.FileUtilities;
import br.com.pactosolucoes.comuns.util.Formatador;
import br.com.pactosolucoes.comuns.util.JSFUtilities;
import controle.arquitetura.FuncionalidadeSistemaEnum;
import controle.arquitetura.ModuloAberto;
import controle.arquitetura.SuperControle;
import controle.arquitetura.security.LoginControle;
import negocio.comuns.arquitetura.BannerVO;
import negocio.comuns.arquitetura.ClubeDeBeneficiosVO;
import negocio.comuns.arquitetura.SuperTO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.enumerador.negocio.comuns.basico.enumerador.cadastrodinamico.LocaleEnum;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Criptografia;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.memcached.ObjetoCacheEnum;
import org.apache.commons.io.Charsets;
import org.json.JSONArray;
import org.json.JSONObject;
import servicos.propriedades.PropsService;
import servicos.util.ExecuteRequestHttpService;

import javax.faces.context.FacesContext;
import javax.faces.event.ActionEvent;
import javax.servlet.http.HttpServletRequest;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.net.URLEncoder;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Properties;
import java.util.Set;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * <AUTHOR>
 */
public class SuporteControle extends SuperControle {

    private static final Logger LOG = Logger.getLogger(SuperControle.class.getSimpleName());

    private static final int TIMEOUT = 2000;
    private static final String MOVI_DESK = "movidesk";
    private static final String PARAMETRO_CHAVE = "key=";
    private static final String PARAMETRO_CODIGO_EMPRESA = "codigoEmpresa=";
    private static final String PARAMETRO_NOME_FANTASIA = "nomeFantasia=";
    private static final String PARAMETRO_RAZAO_SOCIAL = "razaoSocial=";
    private static final String PARAMETRO_CPF_CNPJ = "cpfCnpj=";
    private static final String PARAMETRO_ESTADO = "estado=";
    private static final String PARAMETRO_CIDADE = "cidade=";
    private boolean apresentarMedidor = false;
    private boolean apresentarGoogleAgenda = false;
    private String oncomplete;
    private String returnURL;
    private String serviceUsuario;
    private String serviceSenha;
    private List<DataComemorativa> listaComemoracoes = new ArrayList();
    private List<BannerVO> banners = new ArrayList();
    private static String urlGetBanners = "http://app.pactosolucoes.com.br/UpdateServlet";
    private static String urlMedidor;
    private static final String bannerTela1 = "bannerTela1";
    private static final String bannerCRM = "bannerCRM";
    private static final String bannerCadastro = "bannerCadastro";
    private static final String bannerFinanceiro = "bannerFinanceiro";
    private static final String bannerBI = "bannerBI";
    private static final String bannerRemessa = "bannerRemessa";
    private List<BannerVO> bannersCRM;
    private List<BannerVO> bannersCadastro;
    private List<BannerVO> bannersFinanceiro;
    private List<BannerVO> bannersBI;
    private List<BannerVO> bannersRemessa;
    private String uf;
    private String pais;
    private String funcionalidadeAbrir;

    private Boolean apresentarMensagemNFe;
    private Boolean apresentarMensagemExpiracao;
    private String mensagemExpiracaoTopo;

    private Map<String, Long> mapaDiasExpiracao;
    private String dataPadraoNovoPadraoZWUI;

    private ClubeDeBeneficiosVO clubeDeBeneficios;

    static {
        urlGetBanners = PropsService.getPropertyValue(PropsService.urlObterBanners);
        urlMedidor = PropsService.getPropertyValue(PropsService.urlMedidor);
    }

    private void fillBannersFromString(final String texto, List<BannerVO> bans) {


        if (texto != null && texto.length() > 1) {
            String[] urls = texto.split(",");


            //Ira tratar e validar BANNER por ESTADO
            if (urls != null && urls.length > 0) {
                for (int i = 0; i < urls.length; i++) {
                    String[] lista = urls[i].split("###");
                    String[] reg = urls[i].split("#UF#");
                    String estados = reg[0];
                    String[] listaEstados = estados.split("-");
                    for (String ufBanner : listaEstados) {
                        String paisBanner = "";
                        paisBanner = listaEstados[0];
                        try {
                            if (ufBanner.toUpperCase().equals(this.getUF().toUpperCase()) && paisBanner.equals(this.getPais())) {
                                String imagemLink = reg[1];
                                String[] linkIm = imagemLink.split("###");

                                if (linkIm.length == 2) {
                                    if (linkIm[1].startsWith("FUNCIONALIDADE")) {
                                        preencherFuncionalidade(linkIm, bans);
                                    } else if (linkIm[1].startsWith("UCP")) {
                                        preencherBannerLinkUPC(linkIm, bans);
                                    } else {
                                        bans.add(new BannerVO(linkIm[0], linkIm[1]));
                                    }
                                } else {
                                    bans.add(new BannerVO(linkIm[0], ""));
                                }
                            }
                        } catch (Exception e) {
                            Uteis.logar(e, SuperControle.class);
                        }
                    }

                    if (urls[i].startsWith("https://")) {
                        if (lista.length == 1) {
                            bans.add(new BannerVO(lista[0], ""));
                        } else {
                            if (lista[1].startsWith("FUNCIONALIDADE")) {
                                preencherFuncionalidade(lista, bans);
                            } else if (lista[1].startsWith("UCP")) {
                                preencherBannerLinkUPC(lista, bans);
                            } else {
                                bans.add(new BannerVO(lista[0], lista[1]));
                            }
                        }
                    }

                }
            }
        }
    }

    private void preencherFuncionalidade(String[] lista, List<BannerVO> listaBanner) {
        try {
            //exemplo:
            //https://app.pactosolucoes.com.br/banner/carousel/Outubro_Semana-do-App_Banner_.png###FUNCIONALIDADE=CANAL_CLIENTE_PACTO_STORE

            BannerVO bannerVO = new BannerVO();
            bannerVO.setUrlImagem(lista[0]);

            //verificar se a funcionalidade existe e se é necessário permissão para acessar
            String funcionalidade = lista[1].split("FUNCIONALIDADE=")[1].trim();

            FuncionalidadeSistemaEnum funcionalidadeSistemaEnum = FuncionalidadeSistemaEnum.obterPorNome(funcionalidade);
            if (funcionalidadeSistemaEnum != null) {
                FuncionalidadeControle funcionalidadeControle = (FuncionalidadeControle) getControlador(FuncionalidadeControle.class.getSimpleName());
                Boolean validarPermissao = funcionalidadeControle.validarPermissao(funcionalidadeSistemaEnum.getExpressaoRenderizar());
                if (validarPermissao != null && validarPermissao) {
                    bannerVO.setFuncionalidade(funcionalidade);
                }
            }
            listaBanner.add(bannerVO);
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    private void preencherBannerLinkUPC(String[] lista, List<BannerVO> listaBanner) {
        try {
//          codPerguntaUCP= + codigoConhecimento;  LINK PARA CONHECIMENTO
//          solicitacao=true;                      LINK PARA TELA ABERTURA CHAMADO

            String link = lista[1].replaceFirst("UCP=", "");
            String request = request().getContextPath();
            String urlMontada = request + "/redir?up&" + link;
            lista[1] = urlMontada;
            listaBanner.add(new BannerVO(lista[0], lista[1]));
        } catch (Exception ignored) {
        }
    }

    private static String getBannersRemote(final String modulo) {
        Map<String, String> p = new HashMap();
        p.put("op", "urlBanners");
        p.put("modulo", modulo);
        p.put("ts", String.valueOf(Calendario.getInstance().getTimeInMillis()));
        try {

            if (!UteisValidacao.emptyString(urlGetBanners)) {
                ExecuteRequestHttpService exec = new ExecuteRequestHttpService();
                exec.connectTimeout = TIMEOUT;
                exec.readTimeout = TIMEOUT;
                return exec.executeRequestInner(
                        urlGetBanners, p);
            }
        } catch (IOException ex1) {
            Logger.getLogger(SuporteControle.class.getName()).log(Level.SEVERE, "Falha ao carregar banners!", ex1);
        }
        return "";
    }

    public List<BannerVO> carregarBanners(final String modulo) {
        Properties props = FileUtilities.getPropsFromFile("/servicos/propriedades/Banners.properties");
        List<BannerVO> bans = new ArrayList<>();
        if (props != null) {
            String caminho = props.getProperty("caminhoConfigBanners");
            FileInputStream in = null;
            try {
                in = new FileInputStream(caminho);
                Properties propsBanner = new Properties();
                propsBanner.load(in);
                in.close();
                if (caminho != null && !caminho.isEmpty()) {
                    String texto = propsBanner.getProperty(modulo);
                    fillBannersFromString(texto, bans);
                }
            } catch (FileNotFoundException fne) {
                if (bans.isEmpty()) {
                    fillBannersFromString(getBannersRemote(modulo), bans);
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return bans;
    }

    public static String carregarURLBanners(final String modulo) {
        Properties props = FileUtilities.getPropsFromFile(
                "/servicos/propriedades/Banners.properties");
        if (props != null) {
            String caminho = props.getProperty("caminhoConfigBanners");
            FileInputStream in = null;
            try {
                in = new FileInputStream(caminho);
                Properties propsBanner = new Properties();
                propsBanner.load(in);
                in.close();
                if (caminho != null && !caminho.isEmpty()) {
                    if (modulo == null) {
                        return propsBanner.getProperty("bannerTela1");
                    } else {
                        return propsBanner.getProperty(modulo);
                    }
                }
            } catch (Exception ex) {
                return getBannersRemote(modulo);
            }
        }
        return "";
    }

    private void carregarDatasComemorativas() {
        Properties props = FileUtilities.getPropsFromFile("/servicos/propriedades/DatasComemorativas.properties");
        Set<Object> s = props.keySet();
        for (Object obj : s) {
            String nomeComemorativo = obj.toString();
            String valor = props.getProperty(nomeComemorativo);
            listaComemoracoes.add(new DataComemorativa(valor));
        }
    }

    public void carregarClubeDeBeneficiosMarketingMs() {
        int codigoRequest = 0;
        String msg = null;
        try {
            LocaleEnum localeEnum = LocaleEnum.obterLocale(getUsuario().getLinguagem());
            String l = localeEnum.getLocale().toString().replace("_", "-");
            Map<String, String> headers = new HashMap<>();
            headers.put("Accept-Language", l);

            String urlBaseMarketingMS = PropsService.getPropertyValue(PropsService.urlMarketingMs);
            String chaveEmpresa = getKey();
            StringBuilder urlMarketingMS = new StringBuilder();
            urlMarketingMS.append(urlBaseMarketingMS + "/v1/clube-beneficios/find-by-filters");
            String paramentros = "?chaveEmpresa=" + chaveEmpresa;
            paramentros += "&tipoPerfil=" + URLEncoder.encode(getPerfilUsuarioLogado().getTipo() == null ? "" : getPerfilUsuarioLogado().getTipo().getNome());
            urlMarketingMS.append(paramentros);

            String keyMapBanners = Uteis.encriptar(paramentros);
            JSONObject jsonRetorno = obterJsonFromMemCached(keyMapBanners, ObjetoCacheEnum.MAPA_CLUBE_DE_BENEFICIOS);

                if (jsonRetorno == null) {
                Uteis.logarDebug("Cache não foi encontrado para consulta do clube de beneficios chave: " + chaveEmpresa + "!");
                String result = ExecuteRequestHttpService.executeHttpRequest(urlMarketingMS.toString(), null,
                        headers, ExecuteRequestHttpService.METODO_GET,
                        Charsets.UTF_8.name());
                jsonRetorno = new JSONObject(result);
                if (getFacade().getMemCachedManager().getMemcachedOn()) {
                    getFacade().getMemCachedManager().salvarEmCache(keyMapBanners, jsonRetorno.toString(), ObjetoCacheEnum.MAPA_CLUBE_DE_BENEFICIOS, 432000);
                }
            } else {
                Uteis.logarDebug("Cache foi encontrado para consulta do clube de beneficios chave: " + chaveEmpresa + "!");
            }

            if(jsonRetorno != null && jsonRetorno.has("meta")
                    && jsonRetorno.getJSONObject("meta").optInt("messageID") == 0) {
                clubeDeBeneficios = JSONMapper.getObject(jsonRetorno.getJSONObject("content"), ClubeDeBeneficiosVO.class);
            } else {
                Uteis.logar(msg);
            }

        } catch (IOException e) {
            Uteis.logar(e.getMessage());
        } catch (Exception e) {
            e.printStackTrace();
            Uteis.logar(e.getMessage());
            clubeDeBeneficios = new ClubeDeBeneficiosVO();
        }
    }

    public SuporteControle() {
        // carregarBannersmarketingMS Substituiu o metodo carregarBanners bannersFinanceiro = carregarBanners(bannerFinanceiro);
        carregarDatasComemorativas();
        if(Uteis.getHabilitaMarketing().equals("true")){
            long millisInicioCarregarBanners = System.currentTimeMillis();
            banners = carregarBannersmarketingMS(ModuloAberto.ZILLYONWEB);
            bannersCRM = carregarBannersmarketingMS(ModuloAberto.CRMWEB);
            bannersFinanceiro = carregarBannersmarketingMS(ModuloAberto.FINAN);
            bannersCadastro = new ArrayList<>();
            bannersRemessa = new ArrayList<>();
            bannersBI = new ArrayList<>();
            Uteis.logarDebug("Processamento dos banners demorou " + (System.currentTimeMillis() - millisInicioCarregarBanners) + " milissegundos.");
        }else{
            banners = carregarBanners(bannerTela1);
            bannersCRM = carregarBanners(bannerCRM);
            bannersCadastro = carregarBanners(bannerCadastro);
            bannersFinanceiro = carregarBanners(bannerFinanceiro);
            bannersRemessa = carregarBanners(bannerRemessa);
            bannersBI = carregarBanners(bannerBI);
        }
        validarApresentarMensagemExpiracao();
        prepararMensagemBloqueio();
        if(Uteis.getHabilitaClubeDeBenfeficios().equals("true")) {
            carregarClubeDeBeneficiosMarketingMs();
        }
    }

    public List<BannerVO> carregarBannersmarketingMS(ModuloAberto modulo) {
        List<BannerVO> bans = new ArrayList<>();
        if (!UteisValidacao.emptyString(modulo.getBannerHomePath())) {
            bans.add(new BannerVO(modulo.getBannerHomePath()));
        }
        int codigoRequest = 0;
        String msg = null;
        try {
            LocaleEnum localeEnum = LocaleEnum.obterLocale(getUsuario().getLinguagem());
            String l = localeEnum.getLocale().toString().replace("_", "-");
            Map<String, String> headers = new HashMap<>();
            headers.put("Accept-Language", l);

            String urlMarketingMs = montaUrlmarketingMS(modulo.getSigla(), "codigo");
            String keyMapBanners = Uteis.encriptar(urlMarketingMs);
            JSONObject jsonRetorno = obterJsonFromMemCached(keyMapBanners, ObjetoCacheEnum.MAPA_CAMPANHA_BANNERS);

            if (jsonRetorno == null) {
                Uteis.logarDebug("Cache não foi encontrado para consulta de campanhas(marketing-ms) do módulo: " + modulo + "!");
                String result = ExecuteRequestHttpService.executeHttpRequest(urlMarketingMs, null,
                        headers, ExecuteRequestHttpService.METODO_GET,
                        Charsets.UTF_8.name());
                jsonRetorno = new JSONObject(result);
                if (getFacade().getMemCachedManager().getMemcachedOn()) {
                    getFacade().getMemCachedManager().salvarEmCache(keyMapBanners, jsonRetorno.toString(), ObjetoCacheEnum.MAPA_CAMPANHA_BANNERS, Uteis.getTempoSegundosExpiracaoCacheBanners());
                }
            } else {
                Uteis.logarDebug("Cache foi encontrado para consulta de campanhas(marketing-ms) do módulo: " + modulo + "!");
            }

            JSONArray jsonArrayMsg = new JSONArray(jsonRetorno.get("message").toString());

            for (int e = 0; e < jsonArrayMsg.length(); e++) {
                try {
                    JSONObject json = jsonArrayMsg.getJSONObject(e);
                    codigoRequest = (Integer) json.get("code");
                    msg = (String) json.get("message");
                } catch (Exception ignored) {
                    Uteis.logar(ignored.getMessage());
                }
            }

            if(codigoRequest==0){
                JSONArray jsonArray = new JSONArray(jsonRetorno.get("result").toString());
                for (int e = 0; e < jsonArray.length(); e++) {
                    try {
                        JSONObject json = jsonArray.getJSONObject(e);

                        JSONArray a = json.getJSONArray("itens");
                        for (int i = 0; i < a.length(); i++) {
                            JSONObject o = a.getJSONObject(i);
                            String[] linkIm = o.get("link").toString().split(",");
                            bans.add(new BannerVO(o.get("urlImagem").toString(), linkIm[0]));
                        }
                    } catch (Exception ignored) {
                        Uteis.logar(ignored.getMessage());
                    }
                }

            }else{
                Uteis.logar(msg);
            }

        } catch (IOException e) {
            Uteis.logar(e.getMessage());
        } catch (Exception e) {
            Uteis.logar(e.getMessage());
            bans = new ArrayList<>();
        }
        return bans;
    }

    private JSONObject obterJsonFromMemCached(String keyMemCached, ObjetoCacheEnum objetoCacheEnum) {
        try {
            keyMemCached = keyMemCached.toUpperCase();
            Map<String, String> map = getFacade().getMemCachedManager().obterMapa(objetoCacheEnum);
            if (map == null) {
                return null;
            }
            return map.get(keyMemCached) != null ? new JSONObject(map.get(keyMemCached)) : null;
        } catch (Exception e) {
            Uteis.logarDebug(e.getMessage());
        }
        return null;
    }

    public String montaUrlmarketingMS(String modulo, String orderByParametro){
        try {
            String urlMarketingMs = PropsService.getPropertyValue(PropsService.urlMarketingMs);
            LoginControle loginControle = (LoginControle) JSFUtilities.getFromSession("LoginControle");
            boolean ativa = true;
            String redeEmpresa = loginControle != null && loginControle.getRedeEmpresaVO() != null ? loginControle.getRedeEmpresaVO().getId().toString() : "";
            String chaveEmpresa = getKey();
            String siglaEstado = getEmpresaLogado().getCidade().getEstado().getSigla();
            String tipoPerfil = getPerfilUsuarioLogado().getTipo() != null ? getPerfilUsuarioLogado().getTipo().getNome() : "";
            String nomePais = getEmpresaLogado().getPais().getNome();
            String linguagem = getUsuarioLogado() != null ? getUsuarioLogado().getLinguagem() : "";
            int page = 0;
            int size = 50;

            StringBuilder urlMarketingMS = new StringBuilder();
            urlMarketingMS.append(urlMarketingMs).append("/v1/campanha");
            urlMarketingMS.append("?ativa=").append(ativa);
            if (!UteisValidacao.emptyString(redeEmpresa)) {
                urlMarketingMS.append("&redeEmpresa=").append(URLEncoder.encode(redeEmpresa, "UTF-8"));
            }
            urlMarketingMS.append("&chaveEmpresa=").append(URLEncoder.encode(chaveEmpresa, "UTF-8"));
            urlMarketingMS.append("&siglaEstado=").append(URLEncoder.encode(siglaEstado, "UTF-8"));
            urlMarketingMS.append("&tipoPerfil=").append(URLEncoder.encode(tipoPerfil, "UTF-8"));
            urlMarketingMS.append("&nomePais=").append(URLEncoder.encode(nomePais, "UTF-8"));
            urlMarketingMS.append("&modulo=").append(URLEncoder.encode(modulo, "UTF-8"));
            urlMarketingMS.append("&linguagem=").append(URLEncoder.encode(linguagem, "UTF-8"));
            urlMarketingMS.append("&page=").append(page);
            urlMarketingMS.append("&size=").append(size);
            urlMarketingMS.append("&orderBy=").append(URLEncoder.encode(orderByParametro, "UTF-8"));
            return urlMarketingMS.toString();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
    private void validarApresentarMensagemExpiracao() {
        try {
            EmpresaVO empresa = getEmpresaLogado();
            Long diasExpirar = null;
            Long diasExpirarDCC = null;
            Long diasExpirarNFE = null;
            Long diasExpirarVendasOnline = null;
            Long diasExpirarApp = null;

            if (empresa.getDataExpiracao() != null) {
                diasExpirar = Uteis.nrDiasEntreDatas(Calendario.hoje(), empresa.getDataExpiracao());
            }

            if (empresa.getDataExpiracaoCreditoDCC() != null) {
                diasExpirarDCC = Uteis.nrDiasEntreDatas(Calendario.hoje(), empresa.getDataExpiracaoCreditoDCC());
            }

            if (empresa.getDataExpiracaoNFe() != null) {
                diasExpirarNFE = Uteis.nrDiasEntreDatas(Calendario.hoje(), empresa.getDataExpiracaoNFe());
                setApresentarMensagemNFe(true);
            }

            if (empresa.getDataExpiracaoVendasOnline() != null) {
                diasExpirarVendasOnline = Uteis.nrDiasEntreDatas(Calendario.hoje(), empresa.getDataExpiracaoVendasOnline());
            }

            if (empresa.getDataExpiracaoApp() != null) {
                diasExpirarApp = Uteis.nrDiasEntreDatas(Calendario.hoje(), empresa.getDataExpiracaoApp());
            }


            setApresentarMensagemExpiracao(
                    (diasExpirar != null && diasExpirar <= 5) ||
                    (diasExpirarDCC != null && diasExpirarDCC <= 5) ||
                    (diasExpirarNFE != null && diasExpirarNFE <= 5) ||
                    (diasExpirarVendasOnline != null && diasExpirarVendasOnline <= 5) ||
                    (diasExpirarApp != null && diasExpirarApp <= 5)
            );

        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    private void prepararMensagemBloqueio() {
        try {
            EmpresaVO empresa = getEmpresaLogado();
            String sistema = "";
            Long diasExpirar = null;
            LoginControle login = (LoginControle) getControlador(LoginControle.class.getSimpleName());
            if (empresa.getDataExpiracao() != null) {
                sistema = "Sistema Pacto";
                diasExpirar = Uteis.nrDiasEntreDatas(Calendario.hoje(), empresa.getDataExpiracao());
                if(diasExpirar < 1){
                    String diasExpiracaoSessao = getDiasParaExpirar();
                    try {
                        diasExpirar = UteisValidacao.emptyString(diasExpiracaoSessao) ? diasExpirar : Long.valueOf(diasExpiracaoSessao);
                    }catch (Exception e ){
                        Uteis.logar(e.getMessage());
                    }
                }
                getMapaDiasExpiracao().put("Sistema Pacto", diasExpirar);
            } else if (empresa.getDataExpiracaoCreditoDCC() != null) {
                sistema = "DCC";
                diasExpirar = Uteis.nrDiasEntreDatas(Calendario.hoje(), empresa.getDataExpiracaoCreditoDCC());
                getMapaDiasExpiracao().put("DCC", diasExpirar);
            } else if (empresa.getDataExpiracaoNFe() != null) {
                sistema = "NFE";
                diasExpirar = Uteis.nrDiasEntreDatas(Calendario.hoje(), empresa.getDataExpiracaoNFe());
                getMapaDiasExpiracao().put("NFE", diasExpirar);
            } else if (empresa.getDataExpiracaoVendasOnline() != null) {
                sistema = "Vendas Online";
                diasExpirar = Uteis.nrDiasEntreDatas(Calendario.hoje(), empresa.getDataExpiracaoVendasOnline());
                getMapaDiasExpiracao().put("Vendas Online", diasExpirar);
            } else if (empresa.getDataExpiracaoApp() != null) {
                sistema = "APP";
                diasExpirar = Uteis.nrDiasEntreDatas(Calendario.hoje(), empresa.getDataExpiracaoApp());
                getMapaDiasExpiracao().put("APP", diasExpirar);
            }

            if (diasExpirar != null && diasExpirar > 0) {
                mensagemExpiracaoTopo = "Seu " + sistema + " expira em " + diasExpirar + (diasExpirar > 1 ? " dias." : " dia.");
            } else if (diasExpirar != null && getDiasParaExpirarInteger() == 0) {
                mensagemExpiracaoTopo = "Seu " + sistema + " expira hoje.";
            } else if (diasExpirar != null && getDiasParaExpirarInteger() < 0) {
                mensagemExpiracaoTopo = "Seu " + sistema + " expirou.";
            } else {
                mensagemExpiracaoTopo += "";
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    public boolean isApresentarMedidor() {
        return apresentarMedidor;
    }

    public void setApresentarMedidor(boolean apresentarMedidor) {
        this.apresentarMedidor = apresentarMedidor;
    }

    public void fecharMedidor() {
        setApresentarMedidor(false);
    }

    public void abrirMedidor() {
        setApresentarMedidor(true);
    }

    public String getApresentarMensagem() {
        if (isApresentarMedidor()) {
            return "Richfaces.showModalPanel('panelMedidor')";
        } else {
            return "Richfaces.hideModalPanel('panelMedidor')";
        }
    }

    public boolean isApresentarGoogleAgenda() {
        return apresentarGoogleAgenda;
    }

    public void setApresentarGoogleAgenda(boolean apresentarGoogleAgenda) {
        this.apresentarGoogleAgenda = apresentarGoogleAgenda;
    }

    public void fecharGoolgleAgenda() {
        setApresentarGoogleAgenda(false);
    }

    public void abrirGoogleAgenda() {
        setApresentarGoogleAgenda(true);
    }

    public String getApresentarMensagemGoogleAgenda() {
        if (isApresentarGoogleAgenda()) {
            return "Richfaces.showModalPanel('panelGoogleAgenda')";
        } else {
            return "Richfaces.hideModalPanel('panelGoogleAgenda')";
        }
    }

    public String getDataNovoPadraoZWUI() {
        Locale locale = Formatador.BRASIL;
        try {
            if (getEmpresaLogado() != null && getEmpresaLogado().getCodigo() > 0) {
                locale = getEmpresaLogado().getLocale();
            }
        } catch (Exception ignored) {
        }
        DateFormat dfmt = new SimpleDateFormat("dd/MM/yyyy HH:mm", locale);
        Calendar cal = Calendario.getInstance(locale);
        return dfmt.format(cal.getTime());
    }

    public String getHojePorExtenso() {
        Locale locale = Formatador.BRASIL;
        try {
            if (getEmpresaLogado() != null && getEmpresaLogado().getCodigo() > 0) {
                locale = getEmpresaLogado().getLocale();
            }
        } catch (Exception ignored) {
        }
        DateFormat dfmt = new SimpleDateFormat("EEEE, d 'de' MMMM 'de' yyyy", locale);
        Calendar cal = Calendario.getInstance(locale);
        return dfmt.format(cal.getTime());
    }

    public String getDataAtual(){
        return Uteis.getDataAtualAplicandoFormatacao("dd/MM/yyyy");
    }

    public String getAgora() {
        return Uteis.gethoraHHMMAjustado(Calendario.hoje());
    }

    public void vazio() {
    }

    public String getDataExpiracao() {
        return (String) JSFUtilities.getFromSession("dataExpiracao");
    }

    public String getDiasParaExpirar() {
        return String.valueOf(JSFUtilities.getFromSession(JSFUtilities.DIAS_PARA_EXPIRAR));
    }

    public Integer getDiasParaExpirarInteger() {
        Integer diasExpirarInteger = (Integer) JSFUtilities.getFromSession(JSFUtilities.DIAS_PARA_EXPIRAR);
        if (diasExpirarInteger == null) {
            diasExpirarInteger = 5;
        }
        return diasExpirarInteger;
    }

    public String getMensagemExpiracaoTopo() {
        if (mensagemExpiracaoTopo == null) {
            mensagemExpiracaoTopo = "";
        }
        return mensagemExpiracaoTopo;
    }

    public void setMensagemExpiracaoTopo(String mensagemExpiracaoTopo) {
        this.mensagemExpiracaoTopo = mensagemExpiracaoTopo;
    }

    public Boolean getApresentarMensagemExpiracao() {
        if (apresentarMensagemExpiracao == null) {
            apresentarMensagemExpiracao = false;
        }
        return apresentarMensagemExpiracao;
    }

    public void setApresentarMensagemExpiracao(Boolean apresentarMensagemExpiracao) {
        this.apresentarMensagemExpiracao = apresentarMensagemExpiracao;
    }

    public Boolean getApresentarMensagemNFe() {
        Long qtdDias = getMapaDiasExpiracao().get("NFE");
        if(qtdDias == null ) {
           qtdDias = 0L;
        }
        if (apresentarMensagemNFe == null) {
            apresentarMensagemNFe = false;
        }
        return apresentarMensagemNFe && qtdDias < 0;
    }

    public void setApresentarMensagemNFe(Boolean apresentarMensagemNFe) {
        this.apresentarMensagemNFe = apresentarMensagemNFe;
    }

    /**
     * Método retorna uma class CSS para mudar ou não a logomarca do Sistema em
     * datas comemorativas
     *
     * @return
     * <AUTHOR> Maciel
     * @throws Exception
     */
    public String getClassTopoComemorativo(String modulo) {
        for (DataComemorativa dataComemorativa : listaComemoracoes) {
            if (isHojeEstaEntreSemanaEspecial(dataComemorativa.inicioSemana, dataComemorativa.terminoSemana) && dataComemorativa.modulo.contains(modulo)) {
                return dataComemorativa.css;
            }
        }
        return "";
    }

    public String getClassTopo() throws Exception {
        return getClassTopo("ZW");
    }

    private String getClassTopo(String modulo) throws Exception {
        String classTopo = getClassTopoComemorativo(modulo);
        if (!classTopo.equals("")) {
            return classTopo;
        }
        LoginControle login = (LoginControle) getControlador(LoginControle.class.getSimpleName());
        if (login.isNFe()) {
            return "logoNFe";
        }
        return ((!login.isApresentarLinkZW()) && login.isApresentarLinkEstudio()) ? "logoEstudio" : "logo";
    }

    public String getClassTopoCRM() throws Exception {
        return getClassTopo("CRM") + "CRM";
    }

    public String getClassTopoFIN() throws Exception {
        return getClassTopo("FIN") + "FIN";
    }

    private boolean isHojeEstaEntreSemanaEspecial(Date inicioSemana, Date terminoSemana) {
        Date hoje = Calendario.hoje();
        return Calendario.entre(hoje, inicioSemana, terminoSemana);
    }

    public String getOncomplete() {
        return oncomplete;
    }

    public void setOncomplete(String oncomplete) {
        this.oncomplete = oncomplete;
    }

    public String getReturnURL() {
        return returnURL;
    }

    public void setReturnURL(String returnURL) {
        this.returnURL = returnURL;
    }

    public String getServiceSenha() {
        return serviceSenha;
    }

    public void setServiceSenha(String serviceSenha) {
        this.serviceSenha = serviceSenha;
    }

    public String getServiceUsuario() {
        return serviceUsuario;
    }

    public void setServiceUsuario(String serviceUsuario) {
        this.serviceUsuario = serviceUsuario;
    }

    public void prepareListener(ActionEvent evt) {
        returnURL = (String) evt.getComponent().getAttributes().get("returnURL");
    }

    public String prepareUserService() {
        try {
            String usuario = getUsuarioLogado().getServiceUsuario();
            String senha = getUsuarioLogado().getServiceSenha();
            setOncomplete("");
            setServiceUsuario("");
            setServiceSenha("");

            if (!UteisValidacao.emptyString(usuario)) {
                usuario = getUsuarioLogado().getServiceUsuario();
                senha = getUsuarioLogado().getServiceSenha();
            } else {
                EmpresaVO empresa = getEmpresaLogado();
                if (!UteisValidacao.emptyString(empresa.getServiceUsuario())) {
                    usuario = empresa.getServiceUsuario();
                    senha = empresa.getServiceSenha();
                }
            }

            if (!UteisValidacao.emptyString(usuario)) {
                senha = Criptografia.decrypt(senha,
                        SuperControle.Crypt_KEY, SuperControle.Crypt_ALGORITM);
                setServiceUsuario(usuario);
                setServiceSenha(senha);
            } else {
                setOncomplete("javascript:alert('O usuário do Service não foi encontrado. "
                        + "Entre em contato com a Pacto Soluções.')");
            }
        } catch (Exception e) {
            setOncomplete("javascript:alert('Não foi possível acessar o Service: " + e.getMessage() + "')");
        }
        return "";
    }

    public void limparUsuarioService() {
        setServiceUsuario("");
        setServiceSenha("");
    }

    public String getLoginService() {
        String script = "http://suporte2.pactosolucoes.com.br:81/service/portal/login.sp?acao=logar&login=" + serviceUsuario + "&senha=" + serviceSenha + "";
        return "var janela = window.open('" + script + "', '_blank', 'width=5, height=5,screenX=0,screenY=0;'); "
                + "setTimeout(function(){janela.close();},1000);";
    }

    private String criptografiaOID() throws Exception {
        String nomeUsuarioLogado = getUsuarioLogado().getNome();
        String email = getUsuarioLogado().getColaboradorVO().getPessoa().getEmail();
        String usuario = getUsuarioLogado().getServiceUsuario();
        String senha = getUsuarioLogado().getServiceSenha();
        String empresa = getEmpresaLogado().getNome();
        String telefone = getEmpresaLogado().getPrimeiroTelefoneNaoNulo();

        if (!UteisValidacao.emptyString(usuario)) {
            usuario = getUsuarioLogado().getServiceUsuario();
            senha = getUsuarioLogado().getServiceSenha();
        } else {
            EmpresaVO empresaLogada = getEmpresaLogado();
            if (!UteisValidacao.emptyString(empresaLogada.getServiceUsuario())) {
                usuario = empresaLogada.getServiceUsuario();
                senha = empresaLogada.getServiceSenha();
                email = empresaLogada.getEmail();
            }
        }

        if (!UteisValidacao.emptyString(usuario)) {
            senha = Criptografia.decrypt(senha, SuperControle.Crypt_KEY, SuperControle.Crypt_ALGORITM);
        }



        StringBuilder sb = new StringBuilder();
        if (!UteisValidacao.emptyString(email)) {
            sb.append("email=").append(email).append(";");
        } else if (!UteisValidacao.emptyString(getEmpresaLogado().getEmail())) {
            sb.append("email=").append(getEmpresaLogado().getEmail()).append(";");
        }

        if (!UteisValidacao.emptyString(empresa)) {
            sb.append("empresa=").append(empresa).append(";");
        }

        if (!UteisValidacao.emptyString(telefone)) {
            sb.append("telefone=").append(telefone).append(";");
        }

        if (!UteisValidacao.emptyString(usuario)) {
            sb.append("usuarioservice=").append(usuario).append(";");
        }

        if (!UteisValidacao.emptyString(senha)) {
            sb.append("senhaservice=").append(senha).append(";");
        }

        if (!UteisValidacao.emptyString(nomeUsuarioLogado)) {
            sb.append("nomeusuariologado=").append(nomeUsuarioLogado).append(";");
        }

        sb.append("SolicitacaoSuporte=").append(MOVI_DESK).append(";")
                .append(PARAMETRO_CHAVE).append(getKey()).append(";")
                .append(PARAMETRO_CODIGO_EMPRESA).append(getEmpresaLogado().getCodigo()).append(";")
                .append(PARAMETRO_NOME_FANTASIA).append(getEmpresaLogado().getNome()).append(";")
                .append(PARAMETRO_RAZAO_SOCIAL).append(getEmpresaLogado().getRazaoSocial()).append(";")
                .append(PARAMETRO_CPF_CNPJ).append(getEmpresaLogado().getCNPJ()).append(";")
                .append(PARAMETRO_ESTADO).append(getEmpresaLogado().getEstadoSigla()).append(";")
                .append(PARAMETRO_CIDADE).append(getEmpresaLogado().getCidadeNome()).append(";");

        return Uteis.encriptarSol(sb.toString());
    }

    public String getUrlSolicitacao() throws Exception {
        return getUrlSolicitacaoProperties() + "oid?solicitacao=" + criptografiaOID();
    }

    public String getUrlSolicitacaoProperties() throws Exception {
        return PropsService.getPropertyValue(PropsService.urlSolicitacao);
    }

    public List getBannersCRM() {
        return bannersCRM;
    }

    public List<BannerVO> getBannersRemessa() {
        return bannersRemessa;
    }

    public void setBannersRemessa(List<BannerVO> bannersRemessa) {
        this.bannersRemessa = bannersRemessa;
    }

    public String getFuncionalidadeAbrir() {
        return funcionalidadeAbrir;
    }

    public void setFuncionalidadeAbrir(String funcionalidadeAbrir) {
        this.funcionalidadeAbrir = funcionalidadeAbrir;
    }

    private class DataComemorativa extends SuperTO {

        private String css;
        private Date inicioSemana;
        private Date terminoSemana;
        private String modulo;

        private DataComemorativa(String valorPropriedadeNaoTratado) {
            String[] valores = valorPropriedadeNaoTratado.split(",");
            if (valores.length == 4) {
                this.css = valores[0];
                try {
                    //dia e mes especificado no props + o ano atual
                    this.inicioSemana = Calendario.getDataComHoraZerada(Uteis.getDate(valores[1]));
                    this.terminoSemana = Calendario.getDataComHoraZerada(Uteis.getDate(valores[2]));
                    this.terminoSemana = Uteis.somarDias(this.terminoSemana, 1);
                    this.modulo = valores[3];
                } catch (Exception ex) {
                    Logger.getLogger(SuporteControle.class.getName()).log(Level.SEVERE, null, ex);
                }
            }
        }
    }

    public List getBanners() {
        LoginControle login = (LoginControle) getControlador(LoginControle.class.getSimpleName());
        if (banners == null || banners.isEmpty()){
           banners = new ArrayList<>();
           banners.add(new BannerVO());
        }
        if (login.getModuloAberto().equals(ModuloAberto.ZILLYONWEB)) {
            banners.get(0).setUrlImagem(ModuloAberto.ZILLYONWEB.getBannerHomePath());
        }
        if (login.getModuloAberto().equals(ModuloAberto.FINAN)) {
            banners.get(0).setUrlImagem(ModuloAberto.FINAN.getBannerHomePath());
        }
        if (login.getModuloAberto().equals(ModuloAberto.CRMWEB)) {
            banners.get(0).setUrlImagem(ModuloAberto.CRMWEB.getBannerHomePath());
        }
        return banners;
    }

    public boolean isApresentarFAQ() {
        return !PropsService.isEmpty(PropsService.myFaqUrlBase)
                && !PropsService.isEmpty(PropsService.myFaqEmpresas)
                && PropsService.asString(PropsService.myFaqEmpresas).contains(
                getKey());
    }

    public String getUrlFAQ() {
        return String.format("%s/%s", PropsService.asString(PropsService.myFaqUrlBase),
                getKey());
    }

    public List<BannerVO> getBannersFinanceiro() {
        return bannersFinanceiro;
    }

    public void setBannersFinanceiro(List<BannerVO> bannersFinanceiro) {
        this.bannersFinanceiro = bannersFinanceiro;
    }

    public List<BannerVO> getBannersCadastro() {
        return bannersCadastro;
    }

    public void setBannersCadastro(List<BannerVO> bannersCadastro) {
        this.bannersCadastro = bannersCadastro;
    }

    public String getUrlMedidor(){
        return SuporteControle.urlMedidor;
    }

    public String getUF() {
        EmpresaVO empresaVO = null;
        try {
            empresaVO = getEmpresaLogado();
            uf = empresaVO.getEstado().getSigla();
        } catch (Exception e) {
            e.printStackTrace();
}
        return uf;
    }

    public String getPais() {
        EmpresaVO empresaVO = null;
        try {
            empresaVO = getEmpresaLogado();
            pais = empresaVO.getPais().getNome();
        } catch (Exception e) {
            e.printStackTrace();
}
        return pais;
    }

    public void notificarClick(){
       notificarRecursoEmpresa(RecursoSistema.CLIQUE_BANNER_PRINCIPAL);
    }

    public void notificarClickCRM(){
        notificarRecursoEmpresa(RecursoSistema.CLIQUE_BANNER_CRM);
    }

    public void notificarClickFinanceiro(){
        notificarRecursoEmpresa(RecursoSistema.CLIQUE_BANNER_FINANCEIRO);
    }

    public void notificarClickCadastro(){
        notificarRecursoEmpresa(RecursoSistema.CLIQUE_BANNER_CADASTRO);
    }

    public void notificarClickBI(){
        notificarRecursoEmpresa(RecursoSistema.CLIQUE_BANNER_BI);
    }

    public List<BannerVO> getBannersBI() {
        return bannersBI;
    }

    public void setBannersBI(List<BannerVO> bannersBI) {
        this.bannersBI = bannersBI;
    }

    public Map<String, Long> getMapaDiasExpiracao() {
        if (mapaDiasExpiracao == null) {
            mapaDiasExpiracao = new HashMap<>();
        }
        return mapaDiasExpiracao;
    }

    public void setMapaDiasExpiracao(Map<String, Long> mapaDiasExpiracao) {
        this.mapaDiasExpiracao = mapaDiasExpiracao;
    }

    public ClubeDeBeneficiosVO getClubeDeBeneficios() {
        if (clubeDeBeneficios == null) {
            return new ClubeDeBeneficiosVO();
        }
        return clubeDeBeneficios;
    }

    public void setClubeDeBeneficios(ClubeDeBeneficiosVO clubeDeBeneficios) {
        this.clubeDeBeneficios = clubeDeBeneficios;
    }

    public boolean isExibirClubeDeBeneficios() {
        try {
            HttpServletRequest request = (HttpServletRequest) FacesContext.getCurrentInstance().getExternalContext().getRequest();
            return !UteisValidacao.emptyString(getClubeDeBeneficios().getLink()) && request.getParameter("cb").equals("true");
        } catch (Exception ex) {
            return false;
        }
    }

}

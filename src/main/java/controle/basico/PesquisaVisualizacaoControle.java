package controle.basico;


import acesso.webservice.AcessoControle;
import acesso.webservice.DaoAuxiliar;
import br.com.pactosolucoes.ce.comuns.ex.ValidacaoException;
import br.com.pactosolucoes.comuns.util.JSFUtilities;
import org.json.JSONObject;
import controle.arquitetura.SuperControle;
import negocio.comuns.basico.*;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisEmail;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.utilitarias.Conexao;
import servicos.operacoes.midias.MidiaService;
import servicos.operacoes.midias.commons.MidiaEntidadeEnum;
import servicos.propriedades.PropsService;

import javax.faces.context.FacesContext;
import javax.servlet.ServletContext;
import java.io.File;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;

/**
 * Created by <PERSON><PERSON> on 18/03/2019.
 */
public class PesquisaVisualizacaoControle extends SuperControle {

    private final String chaveCriptoPesquisa = "PESQUIS@";
    private ClienteVO clienteVO;
    private EmpresaVO empresaVO;
    private String urlLogoEmpresa;
    private String urlImagemFundo;
    private String key;
    private ColaboradorVO colaboradorVO;
    private QuestionarioClienteVO questionarioClienteVO;
    private boolean finalizado = false;
    private String msgFinalizado;
    private AcessoControle acessoControle;
    private ServletContext servletContext;
    private boolean temPerguntaObrigatoria = false;
    private boolean visulizacao = false;


    public PesquisaVisualizacaoControle() {
        try {
            String cripto = FacesContext.getCurrentInstance().getExternalContext().getRequestParameterMap().get("q");
            JSONObject json = new JSONObject(Uteis.desencriptar(cripto, chaveCriptoPesquisa));

            String key = json.getString("key");
            String idTemp = json.has("idTemp") ? json.getString("idTemp") : "";
            Integer cliente = json.getInt("cliente");
            Integer questionario = json.getInt("questionario");
            Integer colaborador = json.getInt("colaborador");
            Integer empresa = json.getInt("empresa");

            acessoControle = DaoAuxiliar.retornarAcessoControle(key);
            setKey(key);

            ClienteVO clienteVO = new ClienteVO();
            try {
                clienteVO = acessoControle.getClienteDao().consultarPorChavePrimaria(cliente, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            } catch (Exception ignored) {
            }
            setClienteVO(clienteVO);

            EmpresaVO empresaVO = new EmpresaVO();
            try {
                if (!UteisValidacao.emptyNumber(empresa)) {
                    empresaVO = acessoControle.getEmpresaDao().consultarPorChavePrimaria(empresa, Uteis.NIVELMONTARDADOS_GESTAOREMESSA);
                } else {
                    empresaVO = acessoControle.getEmpresaDao().consultarPorChavePrimaria(clienteVO.getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_GESTAOREMESSA);
                }
            } catch (Exception ignored) {
            }
            setEmpresaVO(empresaVO);

            setUrlLogoEmpresa(obterURLFotoEmpresa());

            ColaboradorVO colaboradorVO = acessoControle.getColaboradorDao().consultarPorChavePrimaria(colaborador, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            setColaboradorVO(colaboradorVO);

            QuestionarioVO questionarioVO;
            if (!UteisValidacao.emptyString(idTemp)) {
                questionarioVO = (QuestionarioVO) JSFUtilities.getFromSession(idTemp);
                visulizacao = true;
            } else {
                questionarioVO = acessoControle.getQuestionarioDao().consultarPorChavePrimaria(questionario, Uteis.NIVELMONTARDADOS_TODOS);
            }

            montarURLImagemFundo(questionarioVO);
            setQuestionarioClienteVO(new QuestionarioClienteVO());
            getQuestionarioClienteVO().setQuestionario(questionarioVO);

            if (!questionarioVO.isAtivo()) {
                throw new Exception("Esta pesquisa não está mais ativa!<br/>Obrigado.");
            }

            if (questionarioVO.isSomenteUmaResposta()) {
                List<QuestionarioClienteVO> respostas = acessoControle.getQuestionarioClienteDao().consultarPorClienteQuestionario(clienteVO.getCodigo(), questionarioVO.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                if (!UteisValidacao.emptyList(respostas)) {
                    throw new Exception("Você já respondeu a pesquisa!<br/>Obrigado.");
                }
            }

            inicializarQuestionario(questionarioVO);

        } catch (Exception e) {
            setFinalizado(true);
            setMsgFinalizado(e.getMessage());
            montarErro(e.getMessage());
        }
    }

    public void inicializarQuestionario(QuestionarioVO questionarioVO) {

        QuestionarioClienteVO questionarioClienteVO = new QuestionarioClienteVO();
        questionarioClienteVO.setCliente(getClienteVO());
        questionarioClienteVO.setConsultor(getColaboradorVO());
        questionarioClienteVO.setQuestionario(questionarioVO);
        questionarioClienteVO.setData(Calendario.hoje());
        questionarioClienteVO.setQuestionarioPerguntaClienteVOs(new ArrayList<QuestionarioPerguntaClienteVO>());

        int cont = 1;
        for (QuestionarioPerguntaVO pergunta : questionarioVO.getQuestionarioPerguntaVOs()) {

            PerguntaClienteVO perguntaClienteVO = new PerguntaClienteVO();
            perguntaClienteVO.setDescricao(pergunta.getPergunta().getDescricao());
            perguntaClienteVO.setObrigatoria(pergunta.getObrigatoria());
            perguntaClienteVO.setRespostaPergClienteVOs(new ArrayList());

            String campo = "pergunta" + cont;
            perguntaClienteVO.setCampo(campo);
            cont++;


            if (pergunta.getPergunta().getTipoPergunta().equals("SE") || pergunta.getPergunta().getTipoPergunta().equals("SN")) {
                perguntaClienteVO.setSimples(true);
            }
            if (pergunta.getPergunta().getTipoPergunta().equals("NS")) {
                perguntaClienteVO.setNps(true);
            }

            if (pergunta.getPergunta().getTipoPergunta().equals("ME")) {
                perguntaClienteVO.setMultipla(true);
            }

            if (pergunta.getPergunta().getTipoPergunta().equals("TE")) {
                perguntaClienteVO.setTextual(true);
            }

            if (pergunta.getPergunta().getRespostaPerguntaVOs().size() == 0) {

                RespostaPergClienteVO respostaCliente = new RespostaPergClienteVO();
                respostaCliente.setDescricaoRespota("");
                perguntaClienteVO.getRespostaPergClienteVOs().add(respostaCliente);

            } else {

                for (RespostaPerguntaVO respostaPerguntaVO : pergunta.getPergunta().getRespostaPerguntaVOs()) {
                    RespostaPergClienteVO respostaCliente = new RespostaPergClienteVO();
                    respostaCliente.setDescricaoRespota(respostaPerguntaVO.getDescricaoRespota());
                    perguntaClienteVO.getRespostaPergClienteVOs().add(respostaCliente);
                }

                if (pergunta.getPergunta().getTipoPergunta().equals("NS")) {
                    ((List<RespostaPergClienteVO>) perguntaClienteVO.getRespostaPergClienteVOs()).sort(Comparator.comparingInt(r -> Integer.parseInt(r.getDescricaoRespota())));
                }

            }


            QuestionarioPerguntaClienteVO questionarioPerguntaClienteVO = new QuestionarioPerguntaClienteVO();
            questionarioPerguntaClienteVO.setPerguntaCliente(perguntaClienteVO);
            questionarioPerguntaClienteVO.setQuestionarioCliente(questionarioClienteVO.getCodigo());

            if (pergunta.getObrigatoria()) {
                temPerguntaObrigatoria = true;
            }

            questionarioClienteVO.getQuestionarioPerguntaClienteVOs().add(questionarioPerguntaClienteVO);
        }

        setQuestionarioClienteVO(questionarioClienteVO);
    }

    public void gravar() {
        try {
            limparMsg();
            setMsgAlert("");

            respondeuTodasAsQuestoesObrigatorias();
            acessoControle.getQuestionarioClienteDao().incluir(getQuestionarioClienteVO(), false);

            setFinalizado(true);
            setMsgFinalizado("Obrigado pela resposta!");
            setMsgAlert("jQuery('.growl-container').empty();");
        } catch (ValidacaoException e) {
            tratarException(e, e.getCampos());
        } catch (Exception e) {
            setFinalizado(true);
            setMsgFinalizado(e.getMessage());
            montarErro(e.getMessage());
        }

    }

    private void tratarException(Exception e, String[] campos) {
        montarErro(e);
        setMsgAlert(getMensagemNotificar());

        if (campos != null && campos.length > 0) {
            String function = "try{camposRequeridos('";
            for (String c : campos) {
                function += "," + c + "";
            }
            function = function.replaceFirst(",", "") + "')}catch(e){console.log(e)}; ";
            setMsgAlert(function + getMsgAlert());
        }
    }

    public void respondeuTodasAsQuestoesObrigatorias() throws ValidacaoException {
        for (Object quest : questionarioClienteVO.getQuestionarioPerguntaClienteVOs()) {
            QuestionarioPerguntaClienteVO q = (QuestionarioPerguntaClienteVO) quest;
            if (!q.getPerguntaCliente().getObrigatoria()) {
                continue;
            }
            boolean respondeuPergunta = false;
            for (Object per : q.getPerguntaCliente().getRespostaPergClienteVOs()) {
                RespostaPergClienteVO res = (RespostaPergClienteVO) per;
                if (q.getPerguntaCliente().getTextual()) {
                    respondeuPergunta = respondeuPergunta || !res.getDescricaoRespota().isEmpty();
                } else {
                    respondeuPergunta = respondeuPergunta || res.getRespostaOpcao();
                }
            }

            if (!respondeuPergunta) {
                throw new ValidacaoException(new String[]{"panel" + q.getPerguntaCliente().getCampo()}, "A questão \"" + q.getPerguntaCliente().getDescricao() + "\" é obrigatória.");
            }
        }
    }

    public ClienteVO getClienteVO() {
        if (clienteVO == null) {
            clienteVO = new ClienteVO();
        }
        return clienteVO;
    }

    public void setClienteVO(ClienteVO clienteVO) {
        this.clienteVO = clienteVO;
    }

    public QuestionarioClienteVO getQuestionarioClienteVO() {
        if (questionarioClienteVO == null) {
            questionarioClienteVO = new QuestionarioClienteVO();
        }
        return questionarioClienteVO;
    }

    public void setQuestionarioClienteVO(QuestionarioClienteVO questionarioClienteVO) {
        this.questionarioClienteVO = questionarioClienteVO;
    }

    public boolean isFinalizado() {
        return finalizado;
    }

    public void setFinalizado(boolean finalizado) {
        this.finalizado = finalizado;
    }

    public String getMsgFinalizado() {
        if (msgFinalizado == null) {
            msgFinalizado = "";
        }
        return msgFinalizado;
    }

    public void setMsgFinalizado(String msgFinalizado) {
        this.msgFinalizado = msgFinalizado;
    }

    public ColaboradorVO getColaboradorVO() {
        if (colaboradorVO == null) {
            colaboradorVO = new ColaboradorVO();
        }
        return colaboradorVO;
    }

    public void setColaboradorVO(ColaboradorVO colaboradorVO) {
        this.colaboradorVO = colaboradorVO;
    }

    public EmpresaVO getEmpresaVO() {
        if (empresaVO == null) {
            empresaVO = new EmpresaVO();
        }
        return empresaVO;
    }

    public void setEmpresaVO(EmpresaVO empresaVO) {
        this.empresaVO = empresaVO;
    }

    private String obterURLFotoEmpresa() {
        try {
            if (PropsService.isTrue(PropsService.fotosParaNuvem)) {
                String fotoKey = MidiaService.getInstance().genKey(getKey(), MidiaEntidadeEnum.FOTO_EMPRESA_RELATORIO, getEmpresaVO().getCodigo().toString());
                return Uteis.getPaintFotoDaNuvem(fotoKey);

            } else {

                String path = "/imagensCRM/email/tmp/";
                File caminhoBase = new File(servletContext.getRealPath("/"));

                String pathFull = caminhoBase.getAbsolutePath() + path;
                String nomeImagem = Uteis.retirarAcentuacaoRegex(getEmpresaVO().getNome().replaceAll(" ", ""));

                getEmpresaVO().setFotoRelatorio(acessoControle.getEmpresaDao().obterFoto(getKey(), getEmpresaVO().getCodigo(), MidiaEntidadeEnum.FOTO_EMPRESA_RELATORIO));
                UteisEmail.criarImagem(pathFull, getEmpresaVO().getFotoRelatorio(), nomeImagem + ".jpg");
                path = "." + path + nomeImagem + ".jpg";
                return path;
            }
        } catch (Exception ex) {
            return "";
        }
    }

    @Override
    public String getKey() {
        if (key == null) {
            key = "";
        }
        return key;
    }

    @Override
    public void setKey(String key) {
        this.key = key;
    }

    public String getUrlLogoEmpresa() {
        if (urlLogoEmpresa == null) {
            urlLogoEmpresa = "";
        }
        return urlLogoEmpresa;
    }

    public void setUrlLogoEmpresa(String urlLogoEmpresa) {
        this.urlLogoEmpresa = urlLogoEmpresa;
    }

    public ServletContext getServletContext() {
        return servletContext;
    }

    public void setServletContext(ServletContext servletContext) {
        this.servletContext = servletContext;
    }

    private void montarURLImagemFundo(QuestionarioVO questionarioVO) {
        try {
            if (!UteisValidacao.emptyString(questionarioVO.getFundoImagem())) {
                setUrlImagemFundo(getPaintFotoDaNuvem(questionarioVO.getFundoImagem()));
            } else {
                setUrlImagemFundo("");
            }
        } catch (Exception ignored) {
            setUrlImagemFundo("");
        }

        if (UteisValidacao.emptyString(getUrlImagemFundo()) && UteisValidacao.emptyString(questionarioVO.getFundoCor())) {
            questionarioVO.setFundoCor("#2f96d2");
        }
    }

    public String getUrlImagemFundo() {
        if (urlImagemFundo == null) {
            urlImagemFundo = "";
        }
        return urlImagemFundo;

    }

    public void setUrlImagemFundo(String urlImagemFundo) {
        this.urlImagemFundo = urlImagemFundo;
    }

    public boolean isTemPerguntaObrigatoria() {
        return temPerguntaObrigatoria;
    }

    public void setTemPerguntaObrigatoria(boolean temPerguntaObrigatoria) {
        this.temPerguntaObrigatoria = temPerguntaObrigatoria;
    }

    public boolean isVisulizacao() {
        return visulizacao;
    }

    public void setVisulizacao(boolean visulizacao) {
        this.visulizacao = visulizacao;
    }
}

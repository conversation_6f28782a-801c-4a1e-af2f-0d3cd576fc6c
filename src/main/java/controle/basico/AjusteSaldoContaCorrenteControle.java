/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package controle.basico;

import br.com.pactosolucoes.comuns.util.Formatador;
import controle.arquitetura.security.AutorizacaoFuncionalidadeControle;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;

import controle.arquitetura.security.AutorizacaoFuncionalidadeListener;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.MovimentoContaCorrenteClienteVO;
import negocio.comuns.financeiro.CartaoCreditoVO;
import negocio.comuns.financeiro.ChequeVO;
import negocio.comuns.financeiro.MovPagamentoVO;
import negocio.comuns.financeiro.ReciboDevolucaoVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import relatorio.controle.arquitetura.SuperControleRelatorio;

/**
 *
 * <AUTHOR>
 */
public class AjusteSaldoContaCorrenteControle  extends SuperControleRelatorio {
    private ClienteVO cliente;
    private Double novoValorContaCliente;
    private String novoValorContaClienteFormatado;
    private Double valorDevolverCliente;
    private String valorDevolverClienteFormatado;
    private MovimentoContaCorrenteClienteVO ultimoMovimento;
    private Boolean finalizado = Boolean.FALSE;
    public static final int ZERAR = 1;
    public static final int ALTERAR_VALOR = 2;
    private int modo = ZERAR;
    private Date dataRegistro = Calendario.hoje();
    private Boolean debito;
    private Boolean devolverRestanteEmDinheiro;
    private ReciboDevolucaoVO reciboDevolucao = null;
    private List<ReciboDevolucaoVO> recibos = new ArrayList<ReciboDevolucaoVO>();

    public AjusteSaldoContaCorrenteControle(){
        try {
            devolverRestanteEmDinheiro = false;
            ClienteControle clienteControle = (ClienteControle) context().getExternalContext().getSessionMap().get(ClienteControle.class.getSimpleName());
            cliente = clienteControle.getClienteVO();
            ultimoMovimento = getFacade().getMovimentoContaCorrenteCliente().consultarPorCodigoPessoa(cliente.getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);
            debito = ultimoMovimento.getSaldoAtual() < 0.0;
            Ordenacao.ordenarLista(ultimoMovimento.getMovPagamentosVOs(), "dataLancamento");
            Collections.reverse(ultimoMovimento.getMovPagamentosVOs());
            if(debito){
                novoValorContaCliente = 0.0;
            }else{
                devolverTodos();
            }
            
        } catch (Exception e) {
            setMensagemID("msg_erro");
            setMensagemDetalhada(e);
        }
    }
    public void imprimirReciboDevolucao() {
        imprimirReciboDevolucao(getReciboDevolucao());
    }
    public void imprimirReciboDevolucaoLista() {
        ReciboDevolucaoVO recibo = (ReciboDevolucaoVO)context().getExternalContext().getRequestMap().get("recibo");
        imprimirReciboDevolucao(recibo);
        setMsgAlert("abrirPopupPDFImpressao('relatorio/"+getNomeArquivoRelatorioGeradoAgora()+"','', 780, 595);");
    }

    public void consultarHistorico(){
        try {
            setMsgAlert("");
            ClienteControle clienteControle = (ClienteControle) context().getExternalContext().getSessionMap().get(ClienteControle.class.getSimpleName());
            cliente = clienteControle.getClienteVO();
            recibos = getFacade().getReciboDevolucao().consultarPorPessoa(cliente.getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);
            setMsgAlert(UteisValidacao.emptyList(recibos) ? "alert('Nenhum recibo de devolução encontrado!')" : "Richfaces.showModalPanel('modalHistoricoAjustes');");
        } catch (Exception e) {
            setMensagemID("msg_erro");
            setMensagemDetalhada(e);
        }
    }
    public void confirmarAlteracaoContaCorrente() {
        AutorizacaoFuncionalidadeControle auto = (AutorizacaoFuncionalidadeControle) getControlador(AutorizacaoFuncionalidadeControle.class);
        auto.setPedirPermissao(false);
        AutorizacaoFuncionalidadeListener listener = new AutorizacaoFuncionalidadeListener() {

            @Override
            public void onAutorizacaoComSucesso() throws Exception {
                reciboDevolucao =  getFacade().getMovimentoContaCorrenteCliente().ajustarSaldoContaCorrenteCliente(cliente,ultimoMovimento,
                        valorDevolverCliente == null ? 0.0 : valorDevolverCliente,
                        getUsuario(), dataRegistro, devolverRestanteEmDinheiro, novoValorContaCliente, true);
                finalizado = Boolean.TRUE;
                setMsgAlert("fireElementFromParent('form:btnAtualizaCliente');");
                setMensagem(debito ? "Débito ajustado com sucesso. O saldo da conta corrente do aluno foi atualizado!"
                        : "Crédito devolvido com sucesso. O saldo da conta corrente do aluno foi atualizado!");
                ultimoMovimento = getFacade().getMovimentoContaCorrenteCliente().consultarPorCodigoPessoa(cliente.getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);
                ultimoMovimento.setMovPagamentosVOs(new ArrayList<MovPagamentoVO>());
            }
            @Override
            public String getExecutarAoCompletar() {
                return getOnComplete();
            }
        };
        limparMsg();
        setMsgAlert("");
        reciboDevolucao = null;
        try {
            if(debito && Uteis.arredondarForcando2CasasDecimais(ultimoMovimento.getSaldoAtual()) <= Uteis.arredondarForcando2CasasDecimais(novoValorContaCliente)){
                throw new ConsistirException("O valor informado não pode ser maior ou igual a " + Formatador.formatarValorMonetarioSemMoeda(Uteis.arredondarForcando2CasasDecimais(ultimoMovimento.getSaldoAtual())));
            }
            auto.autorizar("Alterar saldo da conta corrente do cliente", "AlterarSaldoContaCorrenteCliente",
                    "Você precisa da permissão \"Alterar saldo da conta corrente do cliente\"",
                    "panelAutorizacaoFuncionalidade,form", listener);
        } catch (Exception e) {
            setMensagemID("msg_erro");
            setMensagemDetalhada(e);
        }
    }

    public void verificarAlteracaoValor(){
        setMsgAlert("");
        MovPagamentoVO pagamento = (MovPagamentoVO)context().getExternalContext().getRequestMap().get("pagamento");
        if(pagamento.getValor() < pagamento.getValorReceberOuDevolverContaCorrente()){
            montarMsgAlert("Você não pode devolver um valor maior do que o pagamento.");
            pagamento.setValorReceberOuDevolverContaCorrente(pagamento.getValor());
        }
        calcularDevolucao();
    }

    public void devolverTodos() {
        for (MovPagamentoVO pagamento : ultimoMovimento.getMovPagamentosVOs()) {
            pagamento.setValorReceberOuDevolverContaCorrente(pagamento.getValor());
            pagamento.setMovPagamentoEscolhidaFinan(true);
            for (CartaoCreditoVO cartao : pagamento.getCartaoCreditoVOs()) {
                pagamento.setMovPagamentoEscolhidaFinan(false);
                if(!cartao.getTemLote() && !cartao.getTemComposicao()){
                    cartao.setCartaoEscolhido(true);
                }
            }
            for (ChequeVO cheque : pagamento.getChequeVOs()) {
                pagamento.setMovPagamentoEscolhidaFinan(false);
                if(!cheque.getTemLote() && !cheque.getTemComposicao()){
                    cheque.setChequeEscolhido(true);
                }
                
            }
        }
        calcularDevolucao();
    }

    public void calcularDevolucao(){
        valorDevolverCliente = 0.0;
        for(MovPagamentoVO pagamento : ultimoMovimento.getMovPagamentosVOs()){
            if(pagamento.getFormaPagamento().getTipoFormaPagamento().equals("CA")){
                for(CartaoCreditoVO cartao : pagamento.getCartaoCreditoVOs()){
                    if(cartao.isCartaoEscolhido()){
                        valorDevolverCliente += cartao.getValor();
                    }
                }
            }else if(pagamento.getFormaPagamento().getTipoFormaPagamento().equals("CH")){
                for(ChequeVO cheque : pagamento.getChequeVOs()){
                    if(cheque.getChequeEscolhido()){
                        valorDevolverCliente += cheque.getValor();
                    }
                }
            }else {
                if(pagamento.getMovPagamentoEscolhidaFinan()){
                    valorDevolverCliente += pagamento.getValorReceberOuDevolverContaCorrente();
                }else{
                    pagamento.setValorReceberOuDevolverContaCorrente(pagamento.getValor());
                }
            }
        }

        novoValorContaCliente = Uteis.arredondarForcando2CasasDecimaisMantendoSinal(ultimoMovimento.getSaldoAtual()) -
                Uteis.arredondarForcando2CasasDecimaisMantendoSinal(valorDevolverCliente);
    }


    public Double getNovoValorContaCliente() {
        return novoValorContaCliente;
    }

    public void setNovoValorContaCliente(Double novoValorContaCliente) {
        this.novoValorContaCliente = novoValorContaCliente;
    }

    public ClienteVO getCliente() {
        return cliente;
    }

    public void setCliente(ClienteVO cliente) {
        this.cliente = cliente;
    }

    public String getOnComplete() {
        return getMsgAlert();
    }

    public Boolean getFinalizado() {
        return finalizado;
    }

    public void setFinalizado(Boolean finalizado) {
        this.finalizado = finalizado;
    }

    public MovimentoContaCorrenteClienteVO getUltimoMovimento() {
        return ultimoMovimento;
    }

    public void setUltimoMovimento(MovimentoContaCorrenteClienteVO ultimoMovimento) {
        this.ultimoMovimento = ultimoMovimento;
    }

    public int getModo() {
        return modo;
    }

    public void setModo(int modo) {
        this.modo = modo;
    }

    public Date getDataRegistro() {
        return dataRegistro;
    }

    public void setDataRegistro(Date dataRegistro) {
        this.dataRegistro = dataRegistro;
    }

    public Boolean getDebito() {
        return debito;
    }

    public void setDebito(Boolean debito) {
        this.debito = debito;
    }

    public Double getValorDevolverCliente() {
        return valorDevolverCliente;
    }

    public void setValorDevolverCliente(Double valorDevolverCliente) {
        this.valorDevolverCliente = valorDevolverCliente;
    }

    public Boolean getDevolverRestanteEmDinheiro() {
        return devolverRestanteEmDinheiro;
    }

    public void setDevolverRestanteEmDinheiro(Boolean devolverRestanteEmDinheiro) {
        this.devolverRestanteEmDinheiro = devolverRestanteEmDinheiro;
    }

    public ReciboDevolucaoVO getReciboDevolucao() {
        return reciboDevolucao;
    }

    public void setReciboDevolucao(ReciboDevolucaoVO reciboDevolucao) {
        this.reciboDevolucao = reciboDevolucao;
    }

    public List<ReciboDevolucaoVO> getRecibos() {
        return recibos;
    }

    public void setRecibos(List<ReciboDevolucaoVO> recibos) {
        this.recibos = recibos;
    }

    public String getValorDevolverClienteFormatado() {
        valorDevolverClienteFormatado = Formatador.formatarMoeda(valorDevolverCliente);
        return valorDevolverClienteFormatado;
    }

    public String getNovoValorContaClienteFormatado() {
        novoValorContaClienteFormatado = Formatador.formatarMoeda(novoValorContaCliente);
        return novoValorContaClienteFormatado;
    }
}

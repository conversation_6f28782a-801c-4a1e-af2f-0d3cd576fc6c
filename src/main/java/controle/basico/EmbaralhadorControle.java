package controle.basico;

import br.com.pactosolucoes.comuns.util.GeradorCartaoCredito;
import br.com.pactosolucoes.comuns.util.GeradorTelefone;
import controle.arquitetura.SuperControle;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UtilWS;
import org.json.JSONArray;
import org.json.JSONObject;
import servicos.impl.apf.APF;
import servicos.operacoes.midias.MidiaService;
import servicos.operacoes.midias.commons.MidiaEntidadeEnum;
import servicos.propriedades.PropsService;
import servicos.util.ExecuteRequestHttpService;

import java.io.*;
import java.nio.charset.Charset;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by Johnys on 23/11/2016.
 */
public class EmbaralhadorControle extends SuperControle{


    public void embaralharDados(String chave) throws Exception{
        try{
            getFacade().getPlano().getCon().setAutoCommit(false);
            setarConfiguracoes();
            embaralharEmpresas(chave);
            embaralharDadosPessoa();
            embaralharDadosEndereco();
            embaralharEmail();
            embaralharDadosCartaoCredito();
            embaralharDadosTelefone();
            embaralharModalidades();
            embaralharPlanos();
            embaralharTurmas();
            getFacade().getPlano().getCon().setAutoCommit(true);
            getFacade().getPlano().getCon().commit();
        }catch (Exception e){
            getFacade().getPlano().getCon().rollback();
            throw e;
        }
    }

    private void setarConfiguracoes() throws Exception {
        getFacade().getConfiguracaoSistema().alterarBancoApresentacao();
    }

    private void embaralharEmpresas(String chave) throws  Exception{
        List<Integer> codigos = getCodigosEmpresaAtualizar();
        JSONArray jsonArray = consultarDadosEmpresa();
        int pos = 0;
        for(Integer codigo : codigos){
            if(pos >= jsonArray.length()){
                pos = 0;
            }
            JSONObject object = jsonArray.getJSONObject(pos);
            getFacade().getEmpresa().alterarNomeEmpresa(object.optString("nome").toUpperCase(), object.optString("nome").toUpperCase(), codigo);
            byte[] foto = baixarFotoEmpresa(object.getString("logo"));
            alterarFotoEmpresa(chave, codigo, foto);
            pos++;
        }
    }

    private byte[] baixarFotoEmpresa(String logo) throws  Exception{
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        new ExecuteRequestHttpService().executeRequestDownload(logo, out);
        return out.toByteArray();
    }

    private void alterarFotoEmpresa(String chave, Integer codigo, byte[] foto) throws  Exception{
        MidiaService.getInstance().uploadObjectFromByteArray(chave, MidiaEntidadeEnum.FOTO_EMPRESA, codigo.toString(), foto);

        MidiaService.getInstance().uploadObjectFromByteArray(chave, MidiaEntidadeEnum.FOTO_EMPRESA_EMAIL, codigo.toString(), foto);

        MidiaService.getInstance().uploadObjectFromByteArray(chave, MidiaEntidadeEnum.FOTO_EMPRESA_RELATORIO, codigo.toString(), foto);

        MidiaService.getInstance().uploadObjectFromByteArray(chave, MidiaEntidadeEnum.FOTO_EMPRESA_REDESOCIAL, codigo.toString(), foto);

        MidiaService.getInstance().uploadObjectFromByteArray(chave, MidiaEntidadeEnum.FOTO_EMPRESA_HomeBackground320x276, codigo.toString(), foto);

        MidiaService.getInstance().uploadObjectFromByteArray(chave, MidiaEntidadeEnum.FOTO_EMPRESA_HomeBackground640x551, codigo.toString(), foto);

        MidiaService.getInstance().uploadObjectFromByteArray(chave, MidiaEntidadeEnum.FOTO_EMPRESA_PROPAGANDA_BOLETO, codigo.toString(), foto);

        ExecuteRequestHttpService.executeRequest(PropsService.getPropertyValue(PropsService.urlOamd)+"/imagenslogoapp?key=" + chave + "&empresa=" + codigo + "&reload=true", null);
    }

    private void embaralharTurmas() throws  Exception{
        List<Integer> codigos = getCodigosTurmaAtualizar();
        JSONArray jsonArray = consultarDadosTurmas();
        int pos = 0;
        for(Integer codigo : codigos){
            if(pos >= jsonArray.length()){
                pos = 0;
            }
            getFacade().getTurma().alterarDescricaoTurma(codigo, jsonArray.optString(pos).toUpperCase());
            pos++;
        }
    }

    private void embaralharModalidades() throws  Exception{
        List<Integer> codigos = getCodigosModalidadeAtualizar();
        JSONArray jsonArray = consultarDadosModalidades();
        int pos = 0;
        for(Integer codigo : codigos){
            if(pos >= jsonArray.length()){
                pos = 0;
            }
            getFacade().getModalidade().alterarNomeModalidade(codigo, jsonArray.optString(pos).toUpperCase());
            pos++;
        }
    }

    private void embaralharPlanos() throws  Exception{
        List<Integer> codigos = getCodigosPlanoAtualizar();
        JSONArray jsonArray = consultarDadosPlanos();
        int pos = 0;
        for(Integer codigo : codigos){
            if(pos >= jsonArray.length()){
                pos = 0;
            }
            getFacade().getPlano().alterarDescricaoPlano(codigo, jsonArray.optString(pos).toUpperCase());
            pos++;
        }
    }

    private void embaralharEmail() throws  Exception{
        getFacade().getEmail().embaralharEmails();
    }

    private void embaralharDadosCartaoCredito() throws  Exception{
        List<Integer> codigos = getCodigosAutorizacoesCartoesAtualizar();
        int pos = 0;
        for(Integer codigo : codigos){
            getFacade().getAutorizacaoCobrancaCliente().alterarNumeroCartao(codigo, APF.encriptar(GeradorCartaoCredito.gerarNumeroCartao(GeradorCartaoCredito.Operadora.VISA)));
        }
    }

    private void embaralharDadosTelefone() throws  Exception{
        List<Integer> codigos = getCodigosTelefoneAtualizar();
        int pos = 0;
        for(Integer codigo : codigos){
            getFacade().getTelefone().atualizarTelefone(codigo, GeradorTelefone.gerarNumeroTelefone(9));
        }
    }


    private void embaralharDadosEndereco() throws  Exception{
        List<Integer> codigos = getCodigosEnderecoAtualizar();
        JSONArray jsonArray = consultarDadosEndereco();
        int pos = 0;
        for(Integer codigo : codigos){
            if(pos >= jsonArray.length()){
                pos = 0;
            }
            JSONObject obj = jsonArray.getJSONObject(pos);
            getFacade().getEndereco().atualizarEndereco(codigo, obj.optString("bairro").toUpperCase(), obj.optString("endereco").toUpperCase(), obj.optString("cep"));
            pos++;
        }
    }

    private void embaralharDadosPessoa() throws Exception{
        List<Integer> codigos = getCodigosPessoaAtualizar();
        JSONArray dadosPessoaFoto = consultarDadosPessoaComFoto();
        int pos = 0;
        for(Integer codigo : codigos){
            if(pos < dadosPessoaFoto.length()){
                JSONObject obj = dadosPessoaFoto.getJSONObject(pos);
                getFacade().getPessoa().atualizarNomeFotoKey(codigo, obj.optString("nome").toUpperCase(), obj.optString("imagem"));
                pos++;
            }else{
                break;
            }
        }
        if(pos < codigos.size()){
            JSONArray dadosPessoaSemFoto = consultarDadosPessoaSemFoto();
            int posDados = 0;
            for(int i = pos; i < codigos.size(); i ++){
                if(posDados >= dadosPessoaSemFoto.length()){
                    posDados = 0;
                }
                getFacade().getPessoa().atualizarNomeFotoKey(codigos.get(i), dadosPessoaSemFoto.getString(posDados), null);
                posDados++;
            }
        }
        getFacade().getSituacaoClienteSinteticoDW().atualizarNomesBaseadoCliente();
    }

    private JSONArray getJSONAOAMD(String nomeTabela) throws  Exception{
        Map<String, String> params = new HashMap<String, String>();
        params.put("nomeTabela", nomeTabela);
        params.put("tipoTabela", "1");
        JSONObject json = new JSONObject(UtilWS.executeRequestOAMDRestFul("tabelaZwJSONControle/consultarTabelaZwJSON", params));
        return new JSONArray(json.getJSONArray("return").getJSONObject(0).getString("dadosJson"));
    }

    private JSONArray consultarDadosEmpresa() throws  Exception{
        return getJSONAOAMD("empresas");
    }

    private JSONArray consultarDadosPessoaComFoto() throws  Exception{
        return getJSONAOAMD("pessoa_artistas");
    }

    private JSONArray consultarDadosPessoaSemFoto() throws  Exception{
        return getJSONAOAMD("pessoa_nomes");
    }

    private JSONArray consultarDadosEndereco() throws  Exception{
        return getJSONAOAMD("endereco");
    }

    private JSONArray consultarDadosPlanos() throws  Exception{
        return getJSONAOAMD("planos");
    }

    private JSONArray consultarDadosModalidades() throws  Exception{
        return getJSONAOAMD("modalidade");
    }

    private JSONArray consultarDadosTurmas() throws  Exception{
        return getJSONAOAMD("turmas");
    }

    private List<Integer> getCodigosPessoaAtualizar() throws  Exception{
        return getFacade().getPessoa().getCodigosPessoaEmbaralhar();
    }

    private List<Integer> getCodigosEnderecoAtualizar() throws  Exception{
        return getFacade().getEndereco().consultarTodosCodigos();
    }

    private List<Integer> getCodigosTelefoneAtualizar() throws  Exception{
        return getFacade().getTelefone().consultarTodosCodigos();
    }

    private List<Integer> getCodigosPlanoAtualizar() throws  Exception{
        return getFacade().getPlano().consultarTodosCodigos();
    }

    private List<Integer> getCodigosModalidadeAtualizar() throws  Exception{
        return getFacade().getModalidade().consultarTodosCodigos();
    }

    private List<Integer> getCodigosTurmaAtualizar() throws  Exception{
        return getFacade().getTurma().consultarTodosCodigos();
    }

    private List<Integer> getCodigosEmpresaAtualizar() throws  Exception{
        return getFacade().getEmpresa().consultarTodosCodigos();
    }

    private List<Integer> getCodigosAutorizacoesCartoesAtualizar() throws  Exception{
        return getFacade().getAutorizacaoCobrancaCliente().consultarCodigosAutorizacaoCobrancaCartao();
    }


}

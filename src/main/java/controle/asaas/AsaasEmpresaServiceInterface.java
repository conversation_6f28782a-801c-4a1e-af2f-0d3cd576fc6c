/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package controle.asaas;

import br.com.pactosolucoes.comuns.to.BoletoOnlineTO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.financeiro.BoletoVO;

/**
 * Created with IntelliJ IDEA.
 * User: <PERSON>
 * Date: 26/05/2023
 */
public interface AsaasEmpresaServiceInterface {

    BoletoVO criar(BoletoOnlineTO boletoOnlineTO) throws Exception;

    void sincronizar(BoletoVO boletoVO, UsuarioVO usuarioVO, String operacao) throws Exception;

    void cancelar(BoletoVO boletoVO, UsuarioVO usuarioVO, String operacao, boolean origemProcessoManutencao) throws Exception;

    void processarWebhook(BoletoVO boletoVO, String dados) throws Exception;

}

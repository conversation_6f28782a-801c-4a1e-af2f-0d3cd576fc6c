package controle.asaas;

import negocio.comuns.basico.AsaasEmpresaHistoricoVO;
import negocio.comuns.basico.AsaasEmpresaVO;
import negocio.comuns.financeiro.enumerador.AmbienteEnum;
import negocio.comuns.utilitarias.*;
import negocio.facade.jdbc.basico.AsaasEmpresa;
import negocio.facade.jdbc.basico.AsaasEmpresaHistorico;
import org.json.JSONObject;
import servicos.SuperServico;
import servicos.http.MetodoHttpEnum;
import servicos.http.RequestHttpService;
import servicos.http.RespostaHttpDTO;
import servicos.impl.boleto.asaas.DadosComerciaisContaAsaasDTO;
import servicos.impl.boleto.asaas.SituacaoCadastralContaAsaasDTO;
import servicos.impl.boleto.asaas.TaxasContaAsaasDTO;
import servicos.propriedades.PropsService;

import java.sql.Connection;
import java.util.HashMap;
import java.util.Map;

/**
 * Created with IntelliJ IDEA.
 * User: <PERSON>
 * Date: 26/05/2023
 */
public class AsaasEmpresaService extends SuperServico {

    private AsaasEmpresa asaasEmpresaDAO;
    private AsaasEmpresaHistorico asaasEmpresaHistoricoDAO;
    private String apiKeyPacto;
    private String apiKeySubconta;
    private AmbienteEnum ambienteEnum;
    private String URL_API_ASAAS = "";

    private String idChavePixAsaas;

    public AsaasEmpresaService(Connection con, String apiKeySubConta, AmbienteEnum ambienteEnum) throws Exception {
        super(con);
        this.apiKeySubconta = apiKeySubConta;
        this.ambienteEnum = ambienteEnum;
        popularInformacoes();
        inicializarDAO(con);
    }

    public AsaasEmpresaService(Connection con, String apiKeySubConta, AmbienteEnum ambienteEnum, String idChavePixAsaas) throws Exception {
        super(con);
        this.apiKeySubconta = apiKeySubConta;
        this.ambienteEnum = ambienteEnum;
        this.idChavePixAsaas = idChavePixAsaas;
        popularInformacoes();
        inicializarDAO(con);
    }

    public AsaasEmpresaService(Connection con, AmbienteEnum ambienteEnum) throws Exception {
        super(con);
        this.ambienteEnum = ambienteEnum;
        popularInformacoes();
        inicializarDAO(con);
    }

    private void inicializarDAO(Connection con) throws Exception {
        this.asaasEmpresaDAO = new AsaasEmpresa(con);
        this.asaasEmpresaHistoricoDAO = new AsaasEmpresaHistorico(con);
    }

    public AsaasEmpresaVO criar(AsaasEmpresaVO asaasEmpresaVO, int codEmpresa) throws Exception {
        String paramsEnvio = "";
        String paramsResposta = "";

        JSONObject jsonEnvio = montarJsonEnvio(asaasEmpresaVO);
        paramsEnvio = jsonEnvio.toString();

        if (this.ambienteEnum == null || this.ambienteEnum.equals(AmbienteEnum.NENHUM)) {
            throw new Exception("Não foi possível obter o ambiente para consultar a integração");
        }

        String apiKey = "";
        if (this.ambienteEnum.equals(AmbienteEnum.PRODUCAO)) {
            apiKey = PropsService.getPropertyValue(PropsService.apiKeyAsaasPactoProducao); //usa sempre apikey da pacto na criação de qualquer subconta
        } else {
            apiKey = PropsService.getPropertyValue(PropsService.apiKeyAsaasPactoSandbox); //usa sempre apikey da pacto na criação de qualquer subconta
        }

        if (UteisValidacao.emptyString(apiKey)) {
            throw new ConsistirException("Não foi possível obter a apiKey da conta principal para criar a subconta");
        }

        Map<String, String> headers = new HashMap<>();
        headers.put("access_token", apiKey);

        //Produção somente
        String endpoint = this.URL_API_ASAAS + "api/v3/accounts";
        RequestHttpService service = new RequestHttpService();

        RespostaHttpDTO resposta = service.executeRequest(endpoint, headers, null, jsonEnvio.toString(), MetodoHttpEnum.POST);
        paramsResposta = resposta.getResponse();

        if (resposta.getHttpStatus() != 200) {
            throw new ConsistirException(tratarMensagemErro(resposta.getResponse()));
        }

        AsaasEmpresaVO asaasEmpresaVORetorno = new AsaasEmpresaVO(new JSONObject(resposta.getResponse()));
        asaasEmpresaVORetorno.setEmpresa(codEmpresa);
        asaasEmpresaVORetorno.setAmbienteEnum(asaasEmpresaVO.getAmbienteEnum());
        asaasEmpresaVORetorno.setDataCriacao(Calendario.hoje());
        asaasEmpresaVORetorno.setParamsEnvio(paramsEnvio);
        asaasEmpresaVORetorno.setParamsResposta(paramsResposta);

        boolean erro = false;
        try {
            this.asaasEmpresaDAO.incluir(asaasEmpresaVORetorno);
        } catch (Exception ex) {
            //incluir paramsResposta para análise
            AsaasEmpresaHistoricoVO asaasEmpresaHistoricoVO = new AsaasEmpresaHistoricoVO();
            asaasEmpresaHistoricoVO.setEmpresa(codEmpresa);
            asaasEmpresaHistoricoVO.setDataCriacao(Calendario.hoje());
            asaasEmpresaHistoricoVO.setParamsEnvio(paramsEnvio);
            asaasEmpresaHistoricoVO.setParamsResposta(paramsResposta);
            this.asaasEmpresaHistoricoDAO.incluir(asaasEmpresaHistoricoVO);
            erro = true;
        }

        if (erro) {
            throw new ConsistirException("Não foi possível incluir os dados da integração no banco. Entre em contato com a Pacto");
        }

        return asaasEmpresaVORetorno;
    }

    public JSONObject montarJsonEnvio(AsaasEmpresaVO asaasEmpresaVO) {
        try {
            JSONObject params = new JSONObject();
            params.put("name", asaasEmpresaVO.getName());
            params.put("email", asaasEmpresaVO.getEmail());
            params.put("cpfCnpj", Uteis.formatarCpfCnpj(asaasEmpresaVO.getCpfCnpj(), true));
            params.put("companyType", asaasEmpresaVO.getCompanyType());
            params.put("phone", UteisTelefone.removerCaracteresEspeciais(asaasEmpresaVO.getPhone()));
            params.put("mobilePhone", UteisTelefone.removerCaracteresEspeciais(asaasEmpresaVO.getMobilePhone()));
            params.put("address", asaasEmpresaVO.getAddress());
            params.put("addressNumber", asaasEmpresaVO.getAddressNumber());
            params.put("province", asaasEmpresaVO.getProvince());
            params.put("postalCode", asaasEmpresaVO.getPostalCode().replace("-", ""));
            return params;

        } catch (Exception ex) {
        }

        return new JSONObject();
    }


    public AsaasEmpresaVO consultar(String idSubConta) throws Exception {

        Map<String, String> headers = new HashMap<>();
        headers.put("access_token", this.apiKeyPacto); //pra consultar suconta a apikey a ser usada é sempre a da pacto e nunca a da própria subconta

        String endpoint = this.URL_API_ASAAS + "api/v3/accounts?id=" + idSubConta;
        RequestHttpService service = new RequestHttpService();

        RespostaHttpDTO resposta = service.executeRequest(endpoint, headers, null, null, MetodoHttpEnum.GET);

        if (resposta.getHttpStatus() != 200) {
             throw new ConsistirException(tratarMensagemErro(resposta.getResponse()));
        }

        return new AsaasEmpresaVO(new JSONObject(resposta.getResponse()));
    }

    public String tratarMensagemErro(String resposta) {
        if (!UteisValidacao.emptyString(resposta)) {
            JSONObject jsonObject = new JSONObject(resposta);
            if (jsonObject.has("errors") && jsonObject.optJSONArray("errors") != null) {
                JSONObject jsonErrors = new JSONObject(jsonObject.getJSONArray("errors").get(0).toString());
                if (!UteisValidacao.emptyString(jsonErrors.optString("description"))) {
                    if (jsonErrors.optString("description").equalsIgnoreCase("Excedido o máximo de 1 requisições realizadas para: Registrar chave")) {
                        return "Por limitações aplicadas pelo Banco Central, deve-se aguardar 1 minuto entre cada chave criada para uma conta.";
                    }
                    return jsonErrors.optString("description");
                }
            }
            return resposta;
        }
        return "Erro ao realizar operação no Asaas";
    }


    public TaxasContaAsaasDTO consultarTaxasConta() throws Exception {
        TaxasContaAsaasDTO taxasContaAsaasDTO = new TaxasContaAsaasDTO();
        Map<String, String> headers = new HashMap<>();
        headers.put("access_token", this.apiKeySubconta); //usar apikey da própria subconta

        String endpoint = this.URL_API_ASAAS + "api/v3/myAccount/fees";
        RequestHttpService service = new RequestHttpService();
        RespostaHttpDTO resposta = service.executeRequest(endpoint, headers, null, null, MetodoHttpEnum.GET);

        if (resposta.getHttpStatus() != 200) {
            throw new ConsistirException(tratarMensagemErro(resposta.getResponse()));
        } else {
            JSONObject json = new JSONObject(resposta.getResponse());
            taxasContaAsaasDTO = new TaxasContaAsaasDTO(json);
        }
        return taxasContaAsaasDTO;
    }

    public SituacaoCadastralContaAsaasDTO consultarSituacaoCadastralConta() throws Exception {
        SituacaoCadastralContaAsaasDTO situacaoCadastralContaAsaasDTO = new SituacaoCadastralContaAsaasDTO();
        Map<String, String> headers = new HashMap<>();
        headers.put("access_token", this.apiKeySubconta); //usar apikey da própria subconta

        String endpoint = this.URL_API_ASAAS + "api/v3/myAccount/status";
        RequestHttpService service = new RequestHttpService();
        RespostaHttpDTO resposta = service.executeRequest(endpoint, headers, null, null, MetodoHttpEnum.GET);

        if (resposta.getHttpStatus() != 200) {
            throw new ConsistirException(tratarMensagemErro(resposta.getResponse()));
        } else {
            JSONObject json = new JSONObject(resposta.getResponse());
            situacaoCadastralContaAsaasDTO = new SituacaoCadastralContaAsaasDTO(json);
        }
        return situacaoCadastralContaAsaasDTO;
    }


    public DadosComerciaisContaAsaasDTO consultarDadosComerciaisConta() throws Exception {
        DadosComerciaisContaAsaasDTO dadosComerciaisContaAsaasDTO = new DadosComerciaisContaAsaasDTO();
        Map<String, String> headers = new HashMap<>();
        headers.put("access_token", this.apiKeySubconta); //usar apikey da própria subconta

        String endpoint = this.URL_API_ASAAS + "api/v3/myAccount/commercialInfo";
        RequestHttpService service = new RequestHttpService();
        RespostaHttpDTO resposta = service.executeRequest(endpoint, headers, null, null, MetodoHttpEnum.GET);

        if (resposta.getHttpStatus() != 200) {
            throw new ConsistirException(tratarMensagemErro(resposta.getResponse()));
        } else {
            JSONObject json = new JSONObject(resposta.getResponse());
            dadosComerciaisContaAsaasDTO = new DadosComerciaisContaAsaasDTO(json);
        }
        return dadosComerciaisContaAsaasDTO;
    }

    public JSONObject cadastrarChavePix() throws Exception {
        Map<String, String> headers = new HashMap<>();
        headers.put("access_token", this.apiKeySubconta); //usar apikey da própria subconta

        JSONObject body = new JSONObject();
        body.put("type", "EVP"); //chave aleatória

        String endpoint = this.URL_API_ASAAS + "api/v3/pix/addressKeys";
        RequestHttpService service = new RequestHttpService();
        RespostaHttpDTO resposta = service.executeRequest(endpoint, headers, null, body.toString(), MetodoHttpEnum.POST);

        if (resposta.getHttpStatus() == 401) {
            throw new ConsistirException("Chave da API informada no convênio de cobrança é inválida.");
        }

        if (resposta.getHttpStatus() != 200) {
            throw new ConsistirException(tratarMensagemErro(resposta.getResponse()));
        } else {
            JSONObject json = new JSONObject(resposta.getResponse());
            return json;
        }
    }

    public JSONObject listarChavesPix() throws Exception {
        Map<String, String> headers = new HashMap<>();
        headers.put("access_token", this.apiKeySubconta); //usar apikey da pr pria subconta

        String endpoint = this.URL_API_ASAAS + "api/v3/pix/addressKeys?status=ACTIVE";
        RequestHttpService service = new RequestHttpService();
        RespostaHttpDTO resposta = service.executeRequest(endpoint, headers, null, null, MetodoHttpEnum.GET);

        if (resposta.getHttpStatus() == 401) {
            throw new ConsistirException("Chave da API informada no conv nio de cobran a   inv lida.");
        }

        if (resposta.getHttpStatus() != 200) {
            throw new ConsistirException(tratarMensagemErro(resposta.getResponse()));
        } else {
            JSONObject json = new JSONObject(resposta.getResponse());
            return json;
        }
    }

    private void popularInformacoes() {
        if (ambienteEnum != null) {
            if (this.ambienteEnum.equals(AmbienteEnum.PRODUCAO)) {
                this.URL_API_ASAAS = PropsService.getPropertyValue(PropsService.urlApiAsaasProducao);
                this.apiKeyPacto = PropsService.getPropertyValue(PropsService.apiKeyAsaasPactoProducao);
            } else {
                this.URL_API_ASAAS = PropsService.getPropertyValue(PropsService.urlApiAsaasSandbox);
                this.apiKeyPacto = PropsService.getPropertyValue(PropsService.apiKeyAsaasPactoSandbox);
            }
        }
    }
}

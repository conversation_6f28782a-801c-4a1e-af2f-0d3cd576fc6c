package controle.oamd;

import br.com.pactosolucoes.comuns.notificacao.RecursoSistema;
import br.com.pactosolucoes.comuns.util.FileUtilities;
import br.com.pactosolucoes.comuns.util.Formatador;
import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.enumeradores.TipoConsultaCupomDescontoEnum;
import br.com.pactosolucoes.enumeradores.TipoDistribuicaoCupomDescontoEnum;
import br.com.pactosolucoes.enumeradores.TipoPremioCupomDescontoEnum;
import br.com.pactosolucoes.estrutura.exportador.controle.ExportadorListaControle;
import controle.arquitetura.SuperControle;
import controle.arquitetura.security.LoginControle;
import controle.basico.clube.MensagemGenericaControle;
import edu.emory.mathcs.backport.java.util.Arrays;
import importador.LeitorExcel;
import negocio.comuns.plano.PlanoVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.oamd.CampanhaCupomDescontoPremioPortadorVO;
import negocio.oamd.CampanhaCupomDescontoVO;
import negocio.oamd.CupomDescontoVO;
import negocio.oamd.EmpresaFinanceiroVO;
import negocio.oamd.HistoricoUtilizacaoCupomDescontoVO;
import negocio.oamd.RedeEmpresaVO;
import org.apache.poi.hssf.usermodel.HSSFRow;
import org.apache.poi.poifs.filesystem.OfficeXmlFileException;
import org.richfaces.event.UploadEvent;
import org.richfaces.model.UploadItem;
import servicos.impl.oamd.OAMDService;
import servicos.integracao.oamd.to.EmpresaFinanceiroOAMDTO;

import javax.faces.event.ActionEvent;
import javax.faces.model.SelectItem;
import java.io.File;
import java.io.FileOutputStream;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by Luiz Felipe
 */
public class CampanhaCupomDescontoControle extends SuperControle {

    private List<SelectItem> listaSelectItemTipoDistribuicao;
    private List<SelectItem> listaSelectItemTipoPremio;
    private List<SelectItem> listaSelectItemUnidadesRede;
    private List<SelectItem> listaTipoConsulta;
    private List<SelectItem> listaTipoPremioPortador;
    private List<SelectItem> listaTipoDesconto;
    private String oncompleteLog;
    private CampanhaCupomDescontoVO campanhaCupomDescontoVO;
    private Integer quantidadeCupomLote = 0;
    private String observacaoLoteCupom = "";
    private Date dataBaseIsentarMensalidade;
    private File arquivo;
    private List<CupomDescontoVO> listaCupomProcessar;
    private boolean carregarArquivo = false;
    private boolean mostrarBotaoIsentarMensalidade = false;
    private String chaveUnidadeSelecionada;
    private Integer empresaZWSelecionada;
    private Integer empresaFinanceiroSelecionada;
    private List<HistoricoUtilizacaoCupomDescontoVO> listaConsultaHistoricoUtilizacaoCupom;
    private int codigoTipoConsultaSelecionado = 1;
    private String numeroCupomPesquisar;
    private CampanhaCupomDescontoPremioPortadorVO campanhaCupomDescontoPremioPortadorVO;
    private List<String> listaRestricaoPlano;
    private String descricaoPlanoRestringir;
    private String tipoDesconto = "";
    private boolean mostrarEsconderPremioPortador = false;
    private boolean mostrarEsconderRestricaoPlano = false;
    private List<SelectItem> listaDescricaoMensalidades;
    private Map<Integer, EmpresaFinanceiroOAMDTO> mapaEmpresaFinanceiro;
    private List<SelectItem> listaPlanos;
    private String nomeCupomEspecifico = "";
    private boolean gerarNomeCupomAleatorio = true;
    private RedeEmpresaVO redeEmpresaVO;

    public CampanhaCupomDescontoControle() throws Exception {
        inicializarDados();
    }

    private void inicializarDados() throws Exception {
        setRedeEmpresaVO((RedeEmpresaVO) JSFUtilities.getFromSession(JSFUtilities.REDE_EMPRESA));

        montarListaSelectItemTipoDistribuicao();
        montarListaSelectItemTipoPremio();
        if (redeEmpresaVO != null) {
            montarListaSelectItemUnidadesRede(redeEmpresaVO);
        } else {
            setListaSelectItemUnidadesRede(new ArrayList<>());
            getListaSelectItemUnidadesRede().add(new SelectItem(getEmpresaLogado().getCodEmpresaFinanceiro(), getEmpresaLogado().getNome()));

            setChaveUnidadeSelecionada(getKey());
            setEmpresaZWSelecionada(getEmpresaLogado().getCodigo());
            setEmpresaFinanceiroSelecionada(getEmpresaLogado().getCodEmpresaFinanceiro());
        }
        montarListaTipoConsulta();
        montarListaSelectItemTipoPremioPortador();
        montarListaSelectItemTipoDesconto();
        montarListaDescricaoMensalidades();
        montarListaPlanos();
    }

    private void montarListaSelectItemTipoDistribuicao() {
        this.listaSelectItemTipoDistribuicao = new ArrayList<>();
        this.listaSelectItemTipoDistribuicao.add(new SelectItem(0, ""));
        for (TipoDistribuicaoCupomDescontoEnum tipoDistribuicaoCupomDescontoEnum : TipoDistribuicaoCupomDescontoEnum.values()) {
            this.listaSelectItemTipoDistribuicao.add(new SelectItem(tipoDistribuicaoCupomDescontoEnum.getCodigo(), tipoDistribuicaoCupomDescontoEnum.getDescricao()));
        }

        getCampanhaCupomDescontoVO().setTipoDistribuicaoCupom(TipoDistribuicaoCupomDescontoEnum.AGENCIA_MARKETING.getCodigo()); //FIXO
    }

    private void montarListaSelectItemUnidadesRede(RedeEmpresaVO redeEmpresaVO) throws Exception {
        try {
            OAMDService oamdService = new OAMDService();
            try {
                setMapaEmpresaFinanceiro(new HashMap<>());
                this.listaSelectItemUnidadesRede = new ArrayList<>();
                this.listaSelectItemUnidadesRede.add(new SelectItem("", ""));
                List<EmpresaFinanceiroOAMDTO> lista = oamdService.consultarUnidadesDaRedeOAMD(redeEmpresaVO);
                for (EmpresaFinanceiroOAMDTO obj : lista) {
                    getMapaEmpresaFinanceiro().put(obj.getCodigo(), obj);
                    this.listaSelectItemUnidadesRede.add(new SelectItem(obj.getCodigo(), obj.getNomeFantasia()));
                }
            } catch (Exception e) {
                System.out.println("CampanhaCupomDescontoControle | ERRO: " + e.getMessage());
                montarErro(e);
            } finally {
                oamdService = null;
            }

        } catch (Exception e) {
            System.out.println("CampanhaCupomDescontoControle | ERRO: " + e.getMessage());
            throw e;
        }
    }

    private void montarListaTipoConsulta() throws Exception {
        this.listaTipoConsulta = new ArrayList<SelectItem>();
        for (TipoConsultaCupomDescontoEnum tipoConsultaCupomDescontoEnum : TipoConsultaCupomDescontoEnum.values()) {
            this.listaTipoConsulta.add(new SelectItem(tipoConsultaCupomDescontoEnum.getCodigo(), tipoConsultaCupomDescontoEnum.getDescricao()));
        }
    }


    private void montarListaSelectItemTipoPremio() {
        this.listaSelectItemTipoPremio = new ArrayList<>();
        this.listaSelectItemTipoPremio.add(new SelectItem(0, ""));
        for (TipoPremioCupomDescontoEnum tipoPremioCupomDescontoEnum : TipoPremioCupomDescontoEnum.values()) {
            this.listaSelectItemTipoPremio.add(new SelectItem(tipoPremioCupomDescontoEnum.getCodigo(), tipoPremioCupomDescontoEnum.getDescricao()));
        }
        getCampanhaCupomDescontoVO().setTipoPremio(TipoPremioCupomDescontoEnum.MENSALIDADE.getCodigo());
    }

    private void montarListaSelectItemTipoPremioPortador() {
        this.listaTipoPremioPortador = new ArrayList<>();
        this.listaTipoPremioPortador.add(new SelectItem("", ""));
        this.listaTipoPremioPortador.add(new SelectItem(CampanhaCupomDescontoPremioPortadorVO.TIPO_PREMIO_PRODUTO, CampanhaCupomDescontoPremioPortadorVO.TIPO_PREMIO_PRODUTO));
        this.listaTipoPremioPortador.add(new SelectItem(CampanhaCupomDescontoPremioPortadorVO.TIPO_PREMIO_MENSALIDADE, CampanhaCupomDescontoPremioPortadorVO.TIPO_PREMIO_MENSALIDADE));
    }

    private void montarListaSelectItemTipoDesconto() {
        this.listaTipoDesconto = new ArrayList<>();
        this.listaTipoDesconto.add(new SelectItem("", ""));
        this.listaTipoDesconto.add(new SelectItem(CampanhaCupomDescontoPremioPortadorVO.TIPO_DESCONTO_PERCENTUAL, CampanhaCupomDescontoPremioPortadorVO.TIPO_DESCONTO_PERCENTUAL));
        this.listaTipoDesconto.add(new SelectItem(CampanhaCupomDescontoPremioPortadorVO.TIPO_DESCONTO_VALOR, CampanhaCupomDescontoPremioPortadorVO.TIPO_DESCONTO_VALOR));
    }

    private void montarListaDescricaoMensalidades() {
        this.listaDescricaoMensalidades = new ArrayList<>();
        this.listaDescricaoMensalidades.add(new SelectItem("", ""));
        Integer meses = 12;
        try {
            meses = getFacade().getPlanoDuracao().obterPlanoDuracaoMesesMaximo();
        } catch (Exception ignored){}

        if (UteisValidacao.emptyNumber(meses) || meses < 12) {
            meses = 12; //quantidade mínima
        }

        for (int i = 1; i <= meses; i++) {
            String desc = "PARCELA " + i;
            this.listaDescricaoMensalidades.add(new SelectItem(desc, desc));
        }
    }


    private void montarListaRestricaoPlano() {
        this.listaRestricaoPlano = new ArrayList<>();
        if ((this.campanhaCupomDescontoVO.getPlanosQueParticiparaoDaCampanha() != null) && (!this.campanhaCupomDescontoVO.getPlanosQueParticiparaoDaCampanha().trim().equals(""))) {
            String[] arrayPlanos = this.campanhaCupomDescontoVO.getPlanosQueParticiparaoDaCampanha().split(";");
            this.listaRestricaoPlano.addAll(Arrays.asList(arrayPlanos));
        }
        java.util.Collections.sort(this.listaRestricaoPlano);
    }

    public void gravar() {
        try {
            OAMDService oamdService = new OAMDService();
            try {
                if (campanhaCupomDescontoVO.getAplicarParaRede()) {
                    this.campanhaCupomDescontoVO.setRedeEmpresaVO(getRedeEmpresaVO());
                    this.campanhaCupomDescontoVO.setEmpresaFinanceiroVO(null);
                } else {
                    EmpresaFinanceiroVO empresaFinanceiroVO = new EmpresaFinanceiroVO(getEmpresaLogado().getCodEmpresaFinanceiro());
                    this.campanhaCupomDescontoVO.setRedeEmpresaVO(null);
                    this.campanhaCupomDescontoVO.setEmpresaFinanceiroVO(empresaFinanceiroVO);
                }

                montarCampoRestricaoPlano();
                if (this.campanhaCupomDescontoVO.getId() == null || this.campanhaCupomDescontoVO.getId() == 0) {
                    campanhaCupomDescontoVO = oamdService.incluirCampanhaCupomDesconto(campanhaCupomDescontoVO, getUsuarioLogado(), getKey(), getFacade().getLog());
                    notificarRecursoEmpresa(RecursoSistema.CADASTRO_NOVA_CAMPANHA_CUPOM_DESCONTO);
                } else {
                    campanhaCupomDescontoVO = oamdService.alterarCampanhaCupomDesconto(this.campanhaCupomDescontoVO, getUsuarioLogado(), getKey(), getFacade().getLog());
                }
                campanhaCupomDescontoVO.setAplicarParaRede(campanhaCupomDescontoVO.getRedeEmpresaVO() != null && campanhaCupomDescontoVO.getRedeEmpresaVO().getId() != 0);
                this.campanhaCupomDescontoVO.registrarObjetoVOAntesDaAlteracao();
                montarSucesso("msg_dados_gravados");
            } catch (Exception e) {
                System.out.println("CampanhaCupomDescontoControle | ERRO: " + e.getMessage());
                montarErro(e);
            } finally {
                oamdService = null;
            }

        } catch (Exception e) {
            System.out.println("CampanhaCupomDescontoControle | ERRO: " + e.getMessage());
            montarErro(e);
        }
    }

    public void expandirRetrairPremioPortador() {
        this.mostrarEsconderPremioPortador = !mostrarEsconderPremioPortador;
    }

    public void expandirRetrairRestricaoPlano() {
        this.mostrarEsconderRestricaoPlano = !mostrarEsconderRestricaoPlano;
    }


    private void montarCampoRestricaoPlano() {
        Collections.sort(this.listaRestricaoPlano);
        if (this.listaRestricaoPlano.size() > 0) {
            StringBuilder desc = new StringBuilder();
            for (String plano : this.listaRestricaoPlano) {
                if (desc.length() <= 0) {
                    desc.append(plano);
                } else {
                    desc.append(";").append(plano);
                }
            }
            if (this.campanhaCupomDescontoVO.getPlanosQueParticiparaoDaCampanha() != null) {
                if (!this.campanhaCupomDescontoVO.getPlanosQueParticiparaoDaCampanha().equals(desc.toString())) {
                    this.campanhaCupomDescontoVO.setPlanosQueParticiparaoDaCampanha(desc.toString());
                }
            } else {
                this.campanhaCupomDescontoVO.setPlanosQueParticiparaoDaCampanha(desc.toString());
            }

        } else {
            if ((this.campanhaCupomDescontoVO.getPlanosQueParticiparaoDaCampanha() != null) && (!this.campanhaCupomDescontoVO.getPlanosQueParticiparaoDaCampanha().trim().equals(""))) {
                this.campanhaCupomDescontoVO.setPlanosQueParticiparaoDaCampanha(null);
            }
        }
    }

    public String editar() {
        Integer codigoConsulta = Integer.parseInt(request().getParameter("chavePrimaria"));
        try {
            OAMDService oamdService = new OAMDService();
            try {
                this.campanhaCupomDescontoVO = oamdService.consultarCampanhaCupomDescontoPorId(codigoConsulta, Uteis.NIVELMONTARDADOS_TODOS);
                this.campanhaCupomDescontoVO.registrarObjetoVOAntesDaAlteracao();
                this.listaCupomProcessar = new ArrayList<>();
                montarListaRestricaoPlano();
                this.campanhaCupomDescontoPremioPortadorVO = new CampanhaCupomDescontoPremioPortadorVO();
                this.arquivo = null;
                montarSucesso("msg_entre_dados");
                mostrarEsconderPremioPortador = false;
                mostrarEsconderRestricaoPlano = false;

            } catch (Exception e) {
                System.out.println("CampanhaCupomDescontoControle | ERRO: " + e.getMessage());
                montarErro(e);
            } finally {
                oamdService = null;
            }
        } catch (Exception e) {
            montarErro(e);
        }
        return "editarCampanhaCupomDesconto";
    }

    public void atualizarTotalCuponsUtilizados() {
        try {
            OAMDService oamdService = new OAMDService();
            try {
                this.campanhaCupomDescontoVO.setTotalCupomUtilizado(oamdService.consultarTotalCupomDescontoJaUtilizado(this.campanhaCupomDescontoVO.getId(), getKey()));

            } catch (Exception e) {
                montarErro(e);
            } finally {
                oamdService = null;
            }
            montarSucesso("");
        } catch (Exception e) {
            montarErro(e);
        }

    }

    public void abrirModalExcluirCupomDesconto() {
        MensagemGenericaControle control = getControlador(MensagemGenericaControle.class);
        control.setMensagemDetalhada("", "");
        control.init("Excluir Campanha Cupom Desconto", "Confirma exclusão da campanha de cupom de desconto'.", this, "excluir", "", null, "", "pgCupons, pgMensagemCadCampanha");
        setMsgAlert("Richfaces.showModalPanel('mdlMensagemGenerica');");
    }


    public String excluir() {
        try {
            OAMDService oamdService = new OAMDService();
            try {
                oamdService.excluirCampanhaCupomDesconto(campanhaCupomDescontoVO);

            } catch (Exception e) {
                montarErro(e);
                return "editarCampanhaCupomDesconto";
            } finally {
                oamdService = null;
            }
            montarSucesso("msg_dados_excluidos");
        } catch (Exception e) {
            montarErro(e);
        }
        return "consultarCampanhaCupomDesconto";
    }

    public void novoLoteCupomDesconto() {
        try {
            setMsgAlert("");
            this.gerarNomeCupomAleatorio = true;
            this.nomeCupomEspecifico = "";
            this.quantidadeCupomLote = 0;
            this.observacaoLoteCupom = "";
            setMsgAlert("Richfaces.showModalPanel('modalNovoLoteCupomDesconto');");
            montarSucesso("msg_entre_dados");
        } catch (Exception e) {
            montarErro(e);
            setMsgAlert("");
        }
    }


    public void gravarNovoLoteCupomDesconto() {
            try {
            OAMDService oamdService = new OAMDService();
            try {
                if (!isGerarNomeCupomAleatorio()) {
                    if (UteisValidacao.emptyString(this.nomeCupomEspecifico)) {
                        throw new ConsistirException("Informe um nome para o cupom.");
                    }
                    CupomDescontoVO cupomDescontoVO = oamdService.consultarPorNumeroCupom(this.nomeCupomEspecifico, getEmpresaLogado().getCodEmpresaFinanceiro());
                    if (cupomDescontoVO != null) {
                        throw new ConsistirException("Um cupom com este mesmo nome já foi cadastrado em outra Campanha.");
                    }
                }
                setMsgAlert("");
                if (this.quantidadeCupomLote <= 0) {
                    throw new ConsistirException("Informe uma quantidade de cupons maior que zero.");
                }
                this.campanhaCupomDescontoVO = oamdService.gerarNovoLoteCupomDesconto(getUsuarioLogado(),
                        this.campanhaCupomDescontoVO, quantidadeCupomLote,
                        this.observacaoLoteCupom, this.nomeCupomEspecifico,
                        getKey(), getFacade().getLog());

                this.quantidadeCupomLote = 0;
                this.observacaoLoteCupom = "";

                setMsgAlert("Richfaces.hideModalPanel('modalNovoLoteCupomDesconto');");
                montarSucesso("msg_dados_gravados");
            } catch (Exception e) {
                montarErro(e);
                setMsgAlert("");
            } finally {
                oamdService = null;
            }
        } catch (Exception e) {
            montarErro(e);
            setMsgAlert("");
        }

    }


    private void inicializarDadosNovaCampanhaCupom() throws Exception {
        this.listaConsultaHistoricoUtilizacaoCupom = new ArrayList<>();
        this.campanhaCupomDescontoVO = new CampanhaCupomDescontoVO();
        this.campanhaCupomDescontoVO.setRedeEmpresaVO(getRedeEmpresaVO());
        EmpresaFinanceiroVO empresaFinanceiroVO = new EmpresaFinanceiroVO(getEmpresaLogado().getCodEmpresaFinanceiro());
        this.campanhaCupomDescontoVO.setEmpresaFinanceiroVO(empresaFinanceiroVO);

        montarListaSelectItemTipoPremio();
        montarListaSelectItemTipoDistribuicao();
        montarListaRestricaoPlano();
        montarListaPlanos();

        this.campanhaCupomDescontoPremioPortadorVO = new CampanhaCupomDescontoPremioPortadorVO();
    }

    public void consultarCupons() throws Exception {
        OAMDService oamdService = new OAMDService();
        try {
            this.listaConsultaHistoricoUtilizacaoCupom = oamdService.consultarCupons(this.campanhaCupomDescontoVO, getChaveUnidadeSelecionada(), getEmpresaZWSelecionada(), TipoConsultaCupomDescontoEnum.TODOS, this.numeroCupomPesquisar);
        } catch (Exception e) {
            montarErro(e);
            setMsgAlert("");
        } finally {
            oamdService = null;
        }
    }

    public String novo() {
        try {
            inicializarDadosNovaCampanhaCupom();
        } catch (Exception e) {
            montarErro(e);
            return "consultarCampanhaCupomDesconto";
        }
        return "editarCampanhaCupomDesconto";

    }

    public List<SelectItem> getListaSelectItemTipoDistribuicao() {
        if (listaSelectItemTipoDistribuicao == null) {
            listaSelectItemTipoDistribuicao = new ArrayList<SelectItem>();
        }
        return listaSelectItemTipoDistribuicao;
    }

    public void setListaSelectItemTipoDistribuicao(List<SelectItem> listaSelectItemTipoDistribuicao) {
        this.listaSelectItemTipoDistribuicao = listaSelectItemTipoDistribuicao;
    }

    public List<SelectItem> getListaSelectItemTipoPremio() {
        if (listaSelectItemTipoPremio == null) {
            listaSelectItemTipoPremio = new ArrayList<>();
        }
        return listaSelectItemTipoPremio;
    }

    public void setListaSelectItemTipoPremio(List<SelectItem> listaSelectItemTipoPremio) {
        this.listaSelectItemTipoPremio = listaSelectItemTipoPremio;
    }

    public CampanhaCupomDescontoVO getCampanhaCupomDescontoVO() {
        if (campanhaCupomDescontoVO == null) {
            campanhaCupomDescontoVO = new CampanhaCupomDescontoVO();
        }
        return campanhaCupomDescontoVO;
    }

    public void setCampanhaCupomDescontoVO(CampanhaCupomDescontoVO campanhaCupomDescontoVO) {
        this.campanhaCupomDescontoVO = campanhaCupomDescontoVO;
    }

    private List<CampanhaCupomDescontoVO> consultarListaCampanhaCupomDesconto(String filtro, String ordem, String campoOrdenacao) throws Exception {
        OAMDService oamdService = new OAMDService();
        List<CampanhaCupomDescontoVO> lista;
        try {
            lista = oamdService.consultarCampanhaCupomDescontoParaImpressao(filtro, ordem, campoOrdenacao, (String) JSFUtilities.getFromSession("key"));
        } finally {
            oamdService = null;
        }
        return lista;
    }

    public void exportar(ActionEvent evt) throws Exception {
        ExportadorListaControle exportadorListaControle = (ExportadorListaControle) JSFUtilities.getFromRequest(ExportadorListaControle.class.getSimpleName());
        String paramsTableFiltrada = context().getExternalContext().getRequestParameterMap().get("paramsTableFiltrada");

        String[] split = paramsTableFiltrada.split(",");
        String campoOrdenacao = split[0].replace("[", "");
        String ordem = split[1];
        String filtro = split[2].replace("''", "");
        filtro = filtro.replace("]", "");
        List listaParaImpressao = consultarListaCampanhaCupomDesconto(filtro, ordem, campoOrdenacao);
        exportadorListaControle.exportar(evt, listaParaImpressao, filtro, null);
    }

    public String inicializarConsultar() {
        setPaginaAtualDeTodas("0/0");
        setListaConsulta(new ArrayList<>());
        definirVisibilidadeLinksNavegacao(0, 0);
        setMensagemID("msg_entre_prmconsulta");
        setSucesso(false);
        setErro(false);
        return "consultarCampanhaCupomDesconto";
    }

    public void exportarListaCupom(ActionEvent evt) throws Exception {
        ExportadorListaControle exportadorListaControle = getControlador(ExportadorListaControle.class);
        StringBuilder filtro = new StringBuilder();
        filtro.append("  Campanha Cupom Desconto: ").append(this.campanhaCupomDescontoVO.getDescricaoCampanha());
        SimpleDateFormat sdf = new SimpleDateFormat("dd/MM/yyyy");
        filtro.append("  Vigência: ").append(sdf.format(this.campanhaCupomDescontoVO.getVigenciaInicial())).append(" até ").append(sdf.format(this.campanhaCupomDescontoVO.getVigenciaFinal()));
        exportadorListaControle.exportar(evt, this.campanhaCupomDescontoVO.getListaCupom(), filtro.toString(), null);
    }

    public void exportarResultadoConsultaCupom(ActionEvent evt) throws Exception {
        ExportadorListaControle exportadorListaControle = getControlador(ExportadorListaControle.class);
        StringBuilder filtro = new StringBuilder();
        filtro.append("  Tipo consulta: ").append(TipoConsultaCupomDescontoEnum.getTipo(this.codigoTipoConsultaSelecionado).getDescricao());
        if ((chaveUnidadeSelecionada == null) || (chaveUnidadeSelecionada.trim().equals(""))) {
            filtro.append("  Unidade: Todas");
        } else {
            filtro.append("  Unidade: ").append(retornarNomeUnidade());
        }

        exportadorListaControle.exportar(evt, this.listaConsultaHistoricoUtilizacaoCupom, filtro.toString(), null);
    }

    private String retornarNomeUnidade() {
        for (SelectItem it : listaSelectItemUnidadesRede) {
            if (it.getValue().equals(chaveUnidadeSelecionada)) {
                return it.getLabel();
            }
        }
        return "";
    }

    public void realizarConsultaLogObjetoSelecionado() {
        LoginControle loginControle = (LoginControle) context().getExternalContext().getSessionMap().get("LoginControle");
        loginControle.setNomeClasse("Cidade");
        if (this.getCampanhaCupomDescontoVO().getId() != null && this.getCampanhaCupomDescontoVO().getId() != 0) {
            consultarLogObjetoSelecionado("CAMPANHACUPOMDESCONTO", this.getCampanhaCupomDescontoVO().getId(), null, loginControle);
        } else {
            loginControle.setListaConsultaLog(new ArrayList<>());
            loginControle.getListaConsultaLog().clear();
        }
        setOncompleteLog("abrirPopup('" + request().getContextPath() + "/faces/visualizadorLogForm.jsp', 'VisualizadorLog', 785, 595);");
    }

    public void consultarLogObjetoSelecionado(String nomeEntidade, Integer codigoEntidade, Integer codigoPessoa, LoginControle loginControle) {
        loginControle.setListaConsultaLog(new ArrayList<>());
        try {
            OAMDService oamdService = new OAMDService();
            try {
                List lista = oamdService.consultarPorNomeCodigoEntidadeAgrupado(nomeEntidade, codigoEntidade, null, null, codigoPessoa, Uteis.NIVELMONTARDADOS_DADOSBASICOS, true);
                loginControle.setListaConsultaLog(lista);
            } catch (Exception e) {
                loginControle.setListaConsultaLog(new ArrayList<>());
                loginControle.getListaConsultaLog().clear();
            } finally {
                oamdService = null;
            }
        } catch (Exception e) {
            loginControle.setListaConsultaLog(new ArrayList<>());
            loginControle.getListaConsultaLog().clear();
        }
    }

    public void uploadArquivoListener(final UploadEvent event) throws Exception {
        // Obter o arquivo a partir do evento
        final UploadItem item = event.getUploadItem();
        final File arquivoUploaded = item.getFile();
        // cria um novo arquivo de imagem
        arquivo = new File(item.getFile().getParent() + File.separator + item.getFileName());
        // caso exista o arquivo ele é deletado
        arquivo.delete();
        final FileOutputStream out = new FileOutputStream(arquivo);
        out.write(FileUtilities.obterBytesArquivo(arquivoUploaded));
        // Limpa a memória assim que o arquivo e carregado
        out.flush();
        out.close();
        carregarArquivo = true;
    }

    public void removerArquivo() {
        arquivo = null;
        carregarArquivo = false;
    }

    public void lerArquivoCupomDesconto() {
        try {
            if (arquivo == null) {
                throw new Exception("É necessário fazer primeiro o upload do arquivo.");
            }

            this.listaCupomProcessar = new ArrayList<>();
            List<HSSFRow> linhas = LeitorExcel.lerLinhas(arquivo.getPath());
            for (HSSFRow linha : linhas) {
                try {
                    CupomDescontoVO cupomDescontoVO = montarExcel(linha);
                    if ((cupomDescontoVO.getNumeroCupom() != null) && (!cupomDescontoVO.getNumeroCupom().trim().equals(""))) {
                        this.listaCupomProcessar.add(cupomDescontoVO);
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
            this.mostrarBotaoIsentarMensalidade = (this.listaCupomProcessar != null) && (this.listaCupomProcessar.size() > 0);

            montarSucesso("msg_dados_gravados");
            carregarArquivo = false;
        } catch (Exception e) {
            if (e instanceof OfficeXmlFileException) {
                setMensagemDetalhada("msg_erro", "Sua planilha deve estar no formato XLS");
            } else {
                setMensagemDetalhada(e);
                setMensagemDetalhada("msg_erro", getMensagemDetalhada());
            }

        }
    }

    public void concederPremioCupomDescontoAoAluno() {
        try {
            if (this.dataBaseIsentarMensalidade == null)
                throw new ConsistirException("É necessário informar o campo 'Isentar mensalidades com vencimento maior ou igual a'.");
            OAMDService oamdService = new OAMDService();
            this.campanhaCupomDescontoVO = oamdService.concederPremioCupomDescontoAoAluno(this.campanhaCupomDescontoVO, dataBaseIsentarMensalidade, this.listaCupomProcessar, getUsuarioLogado(), getEmpresaLogado().getCodEmpresaFinanceiro());
            oamdService = null;
            this.mostrarBotaoIsentarMensalidade = false;

            MensagemGenericaControle control = (MensagemGenericaControle) getControlador(MensagemGenericaControle.class);
            control.init("Processamento Cupom Desconto", this.campanhaCupomDescontoVO.getMsgResultadoProcessamento(), this, "Fechar", "", "");
            control.setMensagemDetalhada("", "");
            setMsgAlert("Richfaces.showModalPanel('mdlMensagemGenerica');");

            montarSucesso("msg_dados_gravados");
        } catch (Exception e) {
            montarErro(e);
        }
    }

    public void adicionarRestricaoPlano() {
        try {
            limparMsg();
            if ((this.descricaoPlanoRestringir == null) || (this.descricaoPlanoRestringir.trim().equals(""))) {
                throw new Exception("Selecione o plano.");
            }
            if (this.listaRestricaoPlano.contains(this.descricaoPlanoRestringir.toUpperCase())) {
                throw new Exception("O plano " + this.descricaoPlanoRestringir.toUpperCase() + " já foi adicionado.");
            } else {
                this.listaRestricaoPlano.add(this.descricaoPlanoRestringir.toUpperCase());
            }
            java.util.Collections.sort(this.listaRestricaoPlano);
            this.descricaoPlanoRestringir = "";
            montarSucesso("");
        } catch (Exception e) {
            montarErro(e);
        }
    }

    public void adicionarPremioPortador() {
        try {
            limparMsg();
            if ((this.campanhaCupomDescontoPremioPortadorVO.getTipoPremio() == null) || (this.campanhaCupomDescontoPremioPortadorVO.getTipoPremio().trim().equals(""))) {
                throw new Exception("Informe o tipo do prêmio.");
            }
            if ((this.campanhaCupomDescontoPremioPortadorVO.getDescricaoPremio() == null) || (this.campanhaCupomDescontoPremioPortadorVO.getDescricaoPremio().trim().equals(""))) {
                throw new Exception("Informe a " + this.campanhaCupomDescontoPremioPortadorVO.getLabelDescricaoTipoPremioPortador());
            }
            if ((this.tipoDesconto == null) || (this.tipoDesconto.trim().equals(""))) {
                throw new Exception("Informe se o desconto é por VALOR ou por PERCENTUAL ");
            }
            if (this.tipoDesconto.equals(CampanhaCupomDescontoPremioPortadorVO.TIPO_DESCONTO_VALOR)) {
                if (this.campanhaCupomDescontoPremioPortadorVO.getValorDesconto() <= 0) {
                    throw new Exception("Informe o valor do desconto ");
                }
            }
            if (this.tipoDesconto.equals(CampanhaCupomDescontoPremioPortadorVO.TIPO_DESCONTO_PERCENTUAL)) {
                if (this.campanhaCupomDescontoPremioPortadorVO.getPercentualDesconto() <= 0) {
                    throw new Exception("Informe o percentual de desconto ");
                }
            }
            if (this.campanhaCupomDescontoVO.getListaPremioPortador().contains(this.campanhaCupomDescontoPremioPortadorVO)) {
                throw new Exception("Já foi adicionado um prêmio com estas mesma informações.");
            } else {
                this.campanhaCupomDescontoVO.getListaPremioPortador().add(this.campanhaCupomDescontoPremioPortadorVO);
            }
            this.campanhaCupomDescontoPremioPortadorVO.setDescricaoPremio(this.campanhaCupomDescontoPremioPortadorVO.getDescricaoPremio().toUpperCase());
            this.campanhaCupomDescontoPremioPortadorVO.setDescricaoPlano(this.campanhaCupomDescontoPremioPortadorVO.getDescricaoPlano().toUpperCase());
            java.util.Collections.sort(this.campanhaCupomDescontoVO.getListaPremioPortador(), CampanhaCupomDescontoPremioPortadorVO.COMPARATOR_TIPO_PREMIO);
            this.tipoDesconto = "";

            this.campanhaCupomDescontoPremioPortadorVO = new CampanhaCupomDescontoPremioPortadorVO();
            montarSucesso("");
        } catch (Exception e) {
            montarErro(e);
        }
    }

    public void removerRestricaoPlano() {
        try {
            limparMsg();
            this.listaRestricaoPlano.remove(this.descricaoPlanoRestringir);
            this.descricaoPlanoRestringir = "";
            montarSucesso("");
        } catch (Exception e) {
            montarErro(e);
        }
    }

    public void removerPremioPortador() {
        try {
            limparMsg();
            this.campanhaCupomDescontoVO.getListaPremioPortador().remove(this.campanhaCupomDescontoPremioPortadorVO);
            java.util.Collections.sort(this.campanhaCupomDescontoVO.getListaPremioPortador(), CampanhaCupomDescontoPremioPortadorVO.COMPARATOR_TIPO_PREMIO);
            this.campanhaCupomDescontoPremioPortadorVO = new CampanhaCupomDescontoPremioPortadorVO();
            this.tipoDesconto = "";
            montarSucesso("");
        } catch (Exception e) {
            montarErro(e);
        }
    }


    private CupomDescontoVO montarExcel(HSSFRow linha) {
        CupomDescontoVO cupomDescontoVO = new CupomDescontoVO();
        String numeroCupom = LeitorExcel.obterString(linha, 0);
        String cpf = LeitorExcel.obterString(linha, 1);
        cpf = cpf == null ? ""
                : (Formatador.removerMascara(cpf).length() > 11 ? Formatador.formatarString("##.###.###/####-##", Formatador.removerMascara(cpf))
                : Formatador.formatarString("###.###.###-##", Formatador.removerMascara(cpf)));
        cupomDescontoVO.setNumeroCupom(numeroCupom.trim());
        cupomDescontoVO.setCpfAluno(cpf.trim());

        return cupomDescontoVO;
    }

    public String getOncompleteLog() {
        return oncompleteLog;
    }

    public void setOncompleteLog(String oncompleteLog) {
        this.oncompleteLog = oncompleteLog;
    }

    public Integer getQuantidadeCupomLote() {
        return quantidadeCupomLote;
    }

    public void setQuantidadeCupomLote(Integer quantidadeCupomLote) {
        this.quantidadeCupomLote = quantidadeCupomLote;
    }

    public String getObservacaoLoteCupom() {
        return observacaoLoteCupom;
    }

    public void setObservacaoLoteCupom(String observacaoLoteCupom) {
        this.observacaoLoteCupom = observacaoLoteCupom;
    }

    public Date getDataBaseIsentarMensalidade() {
        return dataBaseIsentarMensalidade;
    }

    public void setDataBaseIsentarMensalidade(Date dataBaseIsentarMensalidade) {
        this.dataBaseIsentarMensalidade = dataBaseIsentarMensalidade;
    }

    public File getArquivo() {
        return arquivo;
    }

    public void setArquivo(File arquivo) {
        this.arquivo = arquivo;
    }

    public List<CupomDescontoVO> getListaCupomProcessar() {
        return listaCupomProcessar;
    }

    public void setListaCupomProcessar(List<CupomDescontoVO> listaCupomProcessar) {
        this.listaCupomProcessar = listaCupomProcessar;
    }

    public boolean isMostrarBotaoIsentarMensalidade() {
        return mostrarBotaoIsentarMensalidade;
    }

    public void setMostrarBotaoIsentarMensalidade(boolean mostrarBotaoIsentarMensalidade) {
        this.mostrarBotaoIsentarMensalidade = mostrarBotaoIsentarMensalidade;
    }

    public boolean isCarregarArquivo() {
        return carregarArquivo;
    }

    public void setCarregarArquivo(boolean carregarArquivo) {
        this.carregarArquivo = carregarArquivo;
    }

    public List<SelectItem> getListaSelectItemUnidadesRede() {
        return listaSelectItemUnidadesRede;
    }

    public void setListaSelectItemUnidadesRede(List<SelectItem> listaSelectItemUnidadesRede) {
        this.listaSelectItemUnidadesRede = listaSelectItemUnidadesRede;
    }

    public String getChaveUnidadeSelecionada() {
        return chaveUnidadeSelecionada;
    }

    public void setChaveUnidadeSelecionada(String chaveUnidadeSelecionada) {
        this.chaveUnidadeSelecionada = chaveUnidadeSelecionada;
    }

    public List<HistoricoUtilizacaoCupomDescontoVO> getListaConsultaHistoricoUtilizacaoCupom() {
        return listaConsultaHistoricoUtilizacaoCupom;
    }

    public void setListaConsultaHistoricoUtilizacaoCupom(List<HistoricoUtilizacaoCupomDescontoVO> listaConsultaHistoricoUtilizacaoCupom) {
        this.listaConsultaHistoricoUtilizacaoCupom = listaConsultaHistoricoUtilizacaoCupom;
    }

    public List<SelectItem> getListaTipoConsulta() {
        return listaTipoConsulta;
    }

    public void setListaTipoConsulta(List<SelectItem> listaTipoConsulta) {
        this.listaTipoConsulta = listaTipoConsulta;
    }

    public int getCodigoTipoConsultaSelecionado() {
        return codigoTipoConsultaSelecionado;
    }

    public void setCodigoTipoConsultaSelecionado(int codigoTipoConsultaSelecionado) {
        this.codigoTipoConsultaSelecionado = codigoTipoConsultaSelecionado;
    }

    public String getNumeroCupomPesquisar() {
        return numeroCupomPesquisar;
    }

    public void setNumeroCupomPesquisar(String numeroCupomPesquisar) {
        this.numeroCupomPesquisar = numeroCupomPesquisar;
    }

    public List<SelectItem> getListaTipoPremioPortador() {
        if (listaTipoPremioPortador == null) {
            listaTipoPremioPortador = new ArrayList<SelectItem>();
        }
        return listaTipoPremioPortador;
    }

    public void setListaTipoPremioPortador(List<SelectItem> listaTipoPremioPortador) {
        this.listaTipoPremioPortador = listaTipoPremioPortador;
    }

    public CampanhaCupomDescontoPremioPortadorVO getCampanhaCupomDescontoPremioPortadorVO() {
        return campanhaCupomDescontoPremioPortadorVO;
    }

    public void setCampanhaCupomDescontoPremioPortadorVO(CampanhaCupomDescontoPremioPortadorVO campanhaCupomDescontoPremioPortadorVO) {
        this.campanhaCupomDescontoPremioPortadorVO = campanhaCupomDescontoPremioPortadorVO;
    }

    public List<SelectItem> getListaTipoDesconto() {
        if (listaTipoDesconto == null) {
            listaTipoDesconto = new ArrayList<>();
        }
        return listaTipoDesconto;
    }

    public void setListaTipoDesconto(List<SelectItem> listaTipoDesconto) {
        this.listaTipoDesconto = listaTipoDesconto;
    }

    public List<String> getListaRestricaoPlano() {
        if (listaRestricaoPlano == null) {
            listaRestricaoPlano = new ArrayList<>();
        }
        return listaRestricaoPlano;
    }

    public void setListaRestricaoPlano(List<String> listaRestricaoPlano) {
        this.listaRestricaoPlano = listaRestricaoPlano;
    }

    public String getDescricaoPlanoRestringir() {
        if (descricaoPlanoRestringir == null) {
            descricaoPlanoRestringir = "";
        }
        return descricaoPlanoRestringir;
    }

    public void setDescricaoPlanoRestringir(String descricaoPlanoRestringir) {
        this.descricaoPlanoRestringir = descricaoPlanoRestringir;
    }

    public String getTipoDesconto() {
        return tipoDesconto;
    }

    public void setTipoDesconto(String tipoDesconto) {
        this.tipoDesconto = tipoDesconto;
    }

    public boolean isMostrarEsconderPremioPortador() {
        return mostrarEsconderPremioPortador;
    }

    public void setMostrarEsconderPremioPortador(boolean mostrarEsconderPremioPortador) {
        this.mostrarEsconderPremioPortador = mostrarEsconderPremioPortador;
    }

    public boolean isMostrarEsconderRestricaoPlano() {
        return mostrarEsconderRestricaoPlano;
    }

    public void setMostrarEsconderRestricaoPlano(boolean mostrarEsconderRestricaoPlano) {
        this.mostrarEsconderRestricaoPlano = mostrarEsconderRestricaoPlano;
    }

    public List<SelectItem> getListaDescricaoMensalidades() {
        if (listaDescricaoMensalidades == null) {
            listaDescricaoMensalidades = new ArrayList<>();
        }
        return listaDescricaoMensalidades;
    }

    public void setListaDescricaoMensalidades(List<SelectItem> listaDescricaoMensalidades) {
        this.listaDescricaoMensalidades = listaDescricaoMensalidades;
    }

    public String getColunasImpressaoCupons() {
//        return "numeroCupom=Código do Cupom,lote=Lote,nomeAluno=Nome Aluno,dataPremioAluno_Apresentar=Data isenção mensalidade,valorPremioAluno=Valor isenção mensalidade,nomePortadorCupom=Nome portador cupom,dataPremioPortadorCupom_Apresentar=Data Utilização cupom Portador,valorPremioPremioPortadorCupom=Valor isenção adesão,observacaoProcessamento=Observação Processamento";
        return "numeroCupom=Código do Cupom,lote=Lote,dataLancamento_Apresentar=Data Geração,dataPremioPortadorCupom_Apresentar=Data Utilização cupom Portador";
    }

    public String getColunasImpressaoCuponsUtilizados() {
//        return "numeroCupom=Código do Cupom,lote=Lote,dataPremioPortadorCupom_Apresentar=Data prêmio portador cupom,nomeUnidadePortador=Unidade portador cupom,nomePortadorCupom=Nome portador cupom,valorPremioProdutosPortadorCupom=Tot. prêmio prod. portador,valorPremioMensalidadePortadorCupom=Tot. prêmio mens. portador,valorPremioPortadorCupom=Tot. geral prêmio portador,cpfAluno=CPF aluno,nomeAluno=Nome Aluno,nomeUnidadeAluno=Unidade aluno,valorPremioAluno=prêmio indicação,observacaoProcessamento=Observação Processamento";
        return "numeroCupom=Código do Cupom,lote=Lote,dataPremioPortadorCupom_Apresentar=Data prêmio portador cupom,nomeUnidadePortador=Unidade portador cupom,nomePortadorCupom=Nome portador cupom,valorPremioProdutosPortadorCupom=Tot. prêmio prod. portador,valorPremioMensalidadePortadorCupom=Tot. prêmio mens. portador,valorPremioPortadorCupom=Tot. geral prêmio portador,contrato=Contrato,contratoEstornado_Apresentar=Contrato Estornado";
    }

    public Integer getEmpresaZWSelecionada() {
        if (empresaZWSelecionada == null) {
            empresaZWSelecionada = 0;
        }
        return empresaZWSelecionada;
    }

    public void setEmpresaZWSelecionada(Integer empresaZWSelecionada) {
        this.empresaZWSelecionada = empresaZWSelecionada;
    }

    public void selecionarEmpresa() {

        if (UteisValidacao.emptyNumber(getEmpresaFinanceiroSelecionada())) {

            setChaveUnidadeSelecionada("");
            setEmpresaZWSelecionada(0);

        } else {

            EmpresaFinanceiroOAMDTO empresaFinanceiroOAMDTO = getMapaEmpresaFinanceiro().get(getEmpresaFinanceiroSelecionada());

            if (empresaFinanceiroOAMDTO != null) {
                setChaveUnidadeSelecionada(empresaFinanceiroOAMDTO.getChaveZw());
                setEmpresaZWSelecionada(empresaFinanceiroOAMDTO.getEmpresazw());
            }
        }
    }

    public Map<Integer, EmpresaFinanceiroOAMDTO> getMapaEmpresaFinanceiro() {
        if (mapaEmpresaFinanceiro == null) {
            mapaEmpresaFinanceiro = new HashMap<>();
        }
        return mapaEmpresaFinanceiro;
    }

    public void setMapaEmpresaFinanceiro(Map<Integer, EmpresaFinanceiroOAMDTO> mapaEmpresaFinanceiro) {
        this.mapaEmpresaFinanceiro = mapaEmpresaFinanceiro;
    }

    public Integer getEmpresaFinanceiroSelecionada() {
        if (empresaFinanceiroSelecionada == null) {
            empresaFinanceiroSelecionada = 0;
        }
        return empresaFinanceiroSelecionada;
    }

    public void setEmpresaFinanceiroSelecionada(Integer empresaFinanceiroSelecionada) {
        this.empresaFinanceiroSelecionada = empresaFinanceiroSelecionada;
    }

    private void montarListaPlanos() {
        try {
            setListaPlanos(new ArrayList<SelectItem>());
            List<PlanoVO> planos = getFacade().getPlano().consultarTodos(Calendario.hoje(), Uteis.NIVELMONTARDADOS_TELACONSULTA);
            getListaPlanos().add(new SelectItem("", ""));
            for (PlanoVO planoVO : planos) {
                getListaPlanos().add(new SelectItem(planoVO.getDescricao(), planoVO.getDescricao() + " | " + planoVO.getEmpresa().getNome()));
            }
        } catch (Exception ex) {
            setListaPlanos(new ArrayList<SelectItem>());
        }
    }

    public List<SelectItem> getListaProdutos() {
        List<SelectItem> lista = new ArrayList<SelectItem>();
        lista.add(new SelectItem("", ""));
        lista.add(new SelectItem("ADESÃO PLANO RECORRENTE", "ADESÃO PLANO RECORRENTE"));
        lista.add(new SelectItem("ANUIDADE PLANO RECORRENTE", "ANUIDADE PLANO RECORRENTE"));
        return lista;
    }

    public List<SelectItem> getListaPlanos() {
        if (listaPlanos == null) {
            listaPlanos = new ArrayList<>();
        }
        return listaPlanos;
    }

    public void setListaPlanos(List<SelectItem> listaPlanos) {
        this.listaPlanos = listaPlanos;
    }

    public String getNomeCupomEspecifico() {
        return nomeCupomEspecifico;
    }

    public void setNomeCupomEspecifico(String nomeCupomEspecifico) {
        this.nomeCupomEspecifico = nomeCupomEspecifico;
    }

    public boolean isGerarNomeCupomAleatorio() {
        return gerarNomeCupomAleatorio;
    }

    public void setGerarNomeCupomAleatorio(boolean gerarNomeCupomAleatorio) {
        this.gerarNomeCupomAleatorio = gerarNomeCupomAleatorio;
    }

    public RedeEmpresaVO getRedeEmpresaVO() {
        return redeEmpresaVO;
    }

    public void setRedeEmpresaVO(RedeEmpresaVO redeEmpresaVO) {
        this.redeEmpresaVO = redeEmpresaVO;
    }
}

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package controle.armario;

import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.estrutura.exportador.controle.ExportadorListaControle;
import static controle.arquitetura.SuperControle.registrarLogErroObjetoVO;
import static controle.arquitetura.SuperControle.registrarLogObjetoVO;
import controle.arquitetura.security.LoginControle;
import controle.basico.clube.MensagemGenericaControle;
import controle.plano.ProdutoControle;
import java.util.List;
import javax.faces.event.ActionEvent;
import negocio.armario.TamanhoArmarioVO;
import relatorio.controle.arquitetura.SuperControleRelatorio;

/**
 *
 * <AUTHOR>
 */
public class TamanhoArmarioControle extends SuperControleRelatorio {
    
    private TamanhoArmarioVO tamanhoArmarioVO = new TamanhoArmarioVO();

    public TamanhoArmarioVO getTamanhoArmarioVO() {
        return tamanhoArmarioVO;
    }
    private String msgAlert;

    public void setTamanhoArmarioVO(TamanhoArmarioVO tamanhoArmarioVO) {
        this.tamanhoArmarioVO = tamanhoArmarioVO;
    }
    
    
    public void exportar(ActionEvent evt) throws Exception {
        ExportadorListaControle exportadorListaControle = (ExportadorListaControle) JSFUtilities.getFromRequest(ExportadorListaControle.class.getSimpleName());
        String paramsTableFiltrada = context().getExternalContext().getRequestParameterMap().get("paramsTableFiltrada");
        String[] split = paramsTableFiltrada.split(",");
        String campoOrdenacao = split[0].replace("[", "");
        String ordem = split[1];
        String filtro = split[2].replace("''", "");
        filtro = filtro.replace("]", "");
        List listaParaImpressao = getFacade().getTamanhoArmario().consultarParaImpressao(filtro, ordem, campoOrdenacao, getEmpresaLogado() == null ? 0 : getEmpresaLogado().getCodigo());
        exportadorListaControle.exportar(evt, listaParaImpressao, filtro, null);

    }
    
    /**
     * Rotina responsável por disponibilizar um novo objeto da classe <code>CategoriaProduto</code>
     * para edição pelo usuário da aplicação.
     */
    public String novo() {
        setTamanhoArmarioVO(new TamanhoArmarioVO());
        limparMsg();
        return "editar";
    }

    /**
     * Rotina responsável por disponibilizar os dados de um objeto da classe <code>CategoriaProduto</code> para alteração.
     * O objeto desta classe é disponibilizado na session da página (request) para que o JSP correspondente possa disponibilizá-lo para edição.
     */
    public String editar() {
        Integer codigoConsulta = Integer.parseInt(request().getParameter("chavePrimaria"));
        try {
            TamanhoArmarioVO obj = getFacade().getTamanhoArmario().consultarPorChavePrimaria(codigoConsulta);
            obj.setNovoObj(false);
            obj.registrarObjetoVOAntesDaAlteracao();
            setTamanhoArmarioVO(obj);
        } catch (Exception e) {
            setErro(true);
            setSucesso(false);
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
        return "editar";
    }
    
    public String gravar() {
        try {
            if (tamanhoArmarioVO.isNovoObj().booleanValue()) {
                getFacade().getTamanhoArmario().incluir(tamanhoArmarioVO);
                incluirLogInclusao();
            } else {
                getFacade().getTamanhoArmario().alterar(tamanhoArmarioVO);
                incluirLogAlteracao();
            }
            ProdutoControle produtoControle = (ProdutoControle) getControlador(ProdutoControle.class);
            if(produtoControle != null){
                produtoControle.resetarComboTiposArmario();
            }
            GestaoArmarioControle gestaoArmarioControle = (GestaoArmarioControle) getControlador(GestaoArmarioControle.class);
            if(gestaoArmarioControle != null){
                gestaoArmarioControle.resetarTiposArmario();
            }
            setMensagemID("msg_dados_gravados");
            setSucesso(true);
            setErro(false);
            return "editar";
        } catch (Exception e) {
            setSucesso(false);
            setErro(true);
            setMensagemDetalhada("msg_erro", e.getMessage());
            return "editar";
        }
    }
    
    public String inicializarConsultar() {
        return "consultar";
    }
    
    public String excluir() {
        try {
            getFacade().getTamanhoArmario().excluir(tamanhoArmarioVO);
            incluirLogExclusao();
            setTamanhoArmarioVO(new TamanhoArmarioVO());
            setSucesso(true);
            setErro(false);
            setMensagemID("msg_dados_excluidos");
            return "consultar";
        } catch (Exception e) {
            setSucesso(false);
            setErro(true);
           if(e.getMessage().contains("ERRO: atualização ou exclusão em tabela \"tamanhoarmario\" viola restrição de chave estrangeira") || e.getMessage().contains("ERROR: update or delete on table \"tamanhoarmario\" violates foreign key")){
                setMensagemDetalhada("Este cadastro não pode ser excluído, pois está sendo utilizado!");
            }else {
                setMensagemDetalhada("msg_erro", e.getMessage());
            }
            return "editar";
        }
    }
    
        public void incluirLogInclusao() throws Exception {
        try {
            tamanhoArmarioVO.setObjetoVOAntesAlteracao(new TamanhoArmarioVO());
            tamanhoArmarioVO.setNovoObj(true);
            registrarLogObjetoVO(tamanhoArmarioVO, tamanhoArmarioVO.getCodigo(), "TAMANHOARMARIO", 0);
        } catch (Exception e) {
            registrarLogErroObjetoVO("TAMANHOARMARIO", tamanhoArmarioVO.getCodigo(), "ERRO AO GERAR LOG DE INCLUSÃO DE TAMANHOARMARIO", this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
        tamanhoArmarioVO.setNovoObj(new Boolean(false));
        tamanhoArmarioVO.registrarObjetoVOAntesDaAlteracao();
    }
    
    public void incluirLogExclusao() throws Exception {
        try {
            tamanhoArmarioVO.setObjetoVOAntesAlteracao(new TamanhoArmarioVO());
            tamanhoArmarioVO.setNovoObj(true);
            registrarLogExclusaoTodosDadosObjetoVO(tamanhoArmarioVO, tamanhoArmarioVO.getCodigo(), "TAMANHOARMARIO", 0);
        } catch (Exception e) {
            registrarLogErroObjetoVO("TAMANHOARMARIO", tamanhoArmarioVO.getCodigo(), "ERRO AO GERAR LOG DE EXCLUSÃO DE TAMANHOARMARIO ", this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
    }

    /**
     * Inclui o log de alteração de categoria
     * @throws Exception
     */
    public void incluirLogAlteracao() throws Exception {
        try {
            registrarLogObjetoVO(tamanhoArmarioVO, tamanhoArmarioVO.getCodigo(), "TAMANHOARMARIO", 0);
        } catch (Exception e) {
            registrarLogErroObjetoVO("TAMANHOARMARIO", tamanhoArmarioVO.getCodigo(), "ERRO AO GERAR LOG DE ALTERAÇÃO DE TAMANHOARMARIO ", this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
        tamanhoArmarioVO.registrarObjetoVOAntesDaAlteracao();
    }
    
     public void realizarConsultaLogObjetoSelecionado() {
        LoginControle loginControle = (LoginControle) context().getExternalContext().getSessionMap().get("LoginControle");
        String nomeClasse = tamanhoArmarioVO.getClass().getSimpleName();
        nomeClasse = nomeClasse.substring(0, nomeClasse.length() - 2);
        loginControle.setNomeClasse(loginControle.getNomeEntidadeCamposLog("prt_" + nomeClasse + "_tituloForm"));
        loginControle.consultarLogObjetoSelecionado(nomeClasse.toUpperCase(), tamanhoArmarioVO.getCodigo(), 0);
    }
    public void realizarConsultaLogObjetoGeral() {
       tamanhoArmarioVO = new TamanhoArmarioVO();
       realizarConsultaLogObjetoSelecionado();
    }

    public void setMsgAlert(String msgAlert) {
        this.msgAlert = msgAlert;
    }

    public String getMsgAlert() {
        if (msgAlert == null) {
            return "";
        }
        return msgAlert;
    }


    public void confirmarExcluir(){
        MensagemGenericaControle control = (MensagemGenericaControle) getControlador(MensagemGenericaControle.class);
        control.setMensagemDetalhada("", "");
        setMsgAlert("Richfaces.showModalPanel('mdlMensagemGenerica');");
        control.init("Exclusão de Tamanho de Armário",
                "Deseja excluir o Tamanho de Armário?",
                this, "excluir", "", "", "", "grupoBtnExcluir");
    }

}

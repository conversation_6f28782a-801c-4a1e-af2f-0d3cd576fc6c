/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package controle.estoque;

import br.com.pactosolucoes.comuns.notificacao.RecursoSistema;
import br.com.pactosolucoes.comuns.util.JSFUtilities;
import controle.arquitetura.FuncionalidadeSistemaEnum;
import controle.arquitetura.SuperControle;
import controle.basico.ClienteControle;
import controle.basico.ColaboradorControle;
import controle.financeiro.VendaConsumidorControle;
import negocio.comuns.arquitetura.UsuarioPerfilAcessoVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.estoque.CardexVO;
import negocio.comuns.estoque.CardexVO.OperacaoCardex;
import negocio.comuns.plano.ProdutoVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.ControleConsulta;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;

import javax.faces.model.SelectItem;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class CardexControle extends SuperControle  {


    protected List listaSelectItemEmpresa;

    // Atributos para pesquisa.
    private Date periodoDe = Calendario.hoje();
    private Date periodoAte = Calendario.hoje();
    private Integer codigoEmpresa;
    private ProdutoVO produtoVO;

    // atributos para o modal de detalhes da operação
    private CardexVO cardexVO;
    private String descricaoDetalhes;
    private boolean mostrarDetalhesOperacao = false;
    private List<CardexVO> listaDetalhesOperacao = new ArrayList<CardexVO>();
    private boolean mostrarColunaNome = false;
    private boolean mostrarColunaDataCancelVenda = false;
    private boolean mostrarColunaDataEstorno = false;
    private String descricaoColuna;
    private Integer totalEntrada = 0;
    private Integer totalSaida = 0;
    private boolean operacaoProdutoEstoque = false;
    private String telaVisualizarDetalhes = "";

    // Atributos para o suggestion Box de Produto.
    private String produtoSelecionado = "";

    public CardexControle() throws Exception {
        inicializarDados();
    }

    private void inicializarDados() throws Exception{
        obterUsuarioLogado();
        montarListaSelectItemEmpresa();
        setControleConsulta(new ControleConsulta());
    }

    public void montarListaSelectItemEmpresa() throws Exception {
        List listaEmpresa = new ArrayList();
        listaEmpresa.add(new SelectItem(new Integer(0), ""));
        List listaConsulta = getFacade().getEmpresa().consultarPorNome("",true, false, Uteis.NIVELMONTARDADOS_TODOS);
        for (Object obj: listaConsulta){
            EmpresaVO empresa = (EmpresaVO) obj;
            listaEmpresa.add(new SelectItem(empresa.getCodigo(), empresa.getNome()));
        }
        setListaSelectItemEmpresa(listaEmpresa);
    }

    public void validarParametrosPesquisa()throws Exception{
        if (!getUsuarioLogado().getAdministrador()){
            this.codigoEmpresa = getEmpresaLogado().getCodigo();
        }
        if (this.periodoDe != null){
            if (this.periodoAte == null)
              throw new ConsistirException("Informe a data final");
        }
        if (this.periodoAte != null){
            if (this.periodoDe == null)
              throw new ConsistirException("Informe a data inicial");
        }
        if ((this.codigoEmpresa == null) || (this.codigoEmpresa <= 0)){
            throw new ConsistirException("Informe a empresa");
        }
       if ((this.produtoVO == null) ||  (this.produtoVO.getCodigo() == null) ||
                (this.produtoVO.getCodigo() <= 0)){
            throw new ConsistirException("Informe o produto");
        }

    }

    public String consultar() {
        try {
            if (!getUsuarioLogado().getAdministrador()){
                this.codigoEmpresa = getEmpresaLogado().getCodigo();
            }
            validarPermissaoVisualizarCardex();
            validarParametrosPesquisa();
            super.consultar();
            List objs = new ArrayList();
            List<CardexVO> listaCardex = getFacade().getCardex().consultarCardexAgrupadoPorDia(this.codigoEmpresa, this.produtoVO.getCodigo(), periodoDe, periodoAte);
            objs.addAll(listaCardex);
            objs = ControleConsulta.obterSubListPaginaApresentar(objs, controleConsulta);
            definirVisibilidadeLinksNavegacao(controleConsulta.getPaginaAtual(), controleConsulta.getNrTotalPaginas());
            setListaConsulta(objs);
            limparMsg();
            return "consultar";
        } catch (Exception e) {
            setListaConsulta(new ArrayList());
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
            return "consultar";
        }
    }

    public String abrirTelaConsulta(){
        setSucesso(true);
        setErro(false);
        setMostrarDetalhesOperacao(false);
        this.produtoSelecionado = "";
        this.produtoVO = new ProdutoVO();
        setListaConsulta(null);
        limparMsg();
        return "consultar";
    }

    public String abrirTelaCardex(){
        try {
            notificarRecursoEmpresa(RecursoSistema.fromDescricao(FuncionalidadeSistemaEnum.CARDEX.toString()));
            abrirCardex();
            setMsgAlert("abrirPopup('cardexCons.jsp', 'Cardex', 1000, 650);");
            return abrirTelaConsulta();
        } catch (Exception e) {
            montarMsgAlert(e.getMessage());
            return "";
        }
    }

    public void abrirCardex() throws Exception {
        setMensagemDetalhada("", "");
        if (getUsuarioLogado().getUsuarioPerfilAcessoVOs().isEmpty()) {
            if (getUsuarioLogado().getAdministrador()) {
                return;
            }
            throw new Exception("O usuário informado não tem nenhum perfil acesso informado.");
        }
        for (Object o : getUsuarioLogado().getUsuarioPerfilAcessoVOs()) {
            UsuarioPerfilAcessoVO usuarioPerfilAcesso = (UsuarioPerfilAcessoVO) o;
            if (getEmpresaLogado().getCodigo().equals(usuarioPerfilAcesso.getEmpresa().getCodigo().intValue())) {
                usuarioPerfilAcesso.getPerfilAcesso().setPermissaoVOs(getFacade().getPermissao().
                        consultarPermissaos(usuarioPerfilAcesso.getPerfilAcesso().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                getFacade().getControleAcesso().verificarPermissaoUsuarioFuncionalidade(usuarioPerfilAcesso.getPerfilAcesso(),
                        getUsuarioLogado(), "VisualizarCardex", "12.05 - Visualizar Cardex");
            }
        }
    }

    public void irPaginaInicial() throws Exception {
        controleConsulta.setPaginaAtual(1);
        this.consultar();
    }

    public void irPaginaAnterior() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getPaginaAtual() - 1);
        this.consultar();
    }

    public void irPaginaPosterior() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getPaginaAtual() + 1);
        this.consultar();
    }

    public void irPaginaFinal() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getNrTotalPaginas());
        this.consultar();
    }

    /**
     * Rotina responsável por organizar a paginação entre as páginas resultantes de uma consulta.
     */
    public String inicializarConsultar() {
        setPaginaAtualDeTodas("0/0");
        setListaConsulta(new ArrayList());
        definirVisibilidadeLinksNavegacao(0, 0);
        limparMsg();
        return "consultar";
    }


    public List getTipoConsultaCombo() {
        List itens = new ArrayList();
        itens.add(new SelectItem("nome", "Nome"));
        itens.add(new SelectItem("codigo", "Código"));

        return itens;
    }


    public void abrirTelaDetalhes() {
        try {
            setTelaVisualizarDetalhes("");
            setMsgAlert("");
            this.cardexVO = (CardexVO) context().getExternalContext().getRequestMap().get("cardex2");
            if ((this.cardexVO.getOperacao() == OperacaoCardex.COMPRA) ||
                    (this.cardexVO.getOperacao() == OperacaoCardex.COMPRA_CANCELADA)) {
                CompraControle compraControle = (CompraControle) JSFUtilities.getManagedBean("CompraControle");
                compraControle.consultarCompra(this.cardexVO.getCodigo());
                setTelaVisualizarDetalhes("abrirPopup('compraForm.jsp', 'Compra', 800, 595);");
            } else if ((this.cardexVO.getOperacao() == OperacaoCardex.BALANCO) ||
                    (this.cardexVO.getOperacao() == OperacaoCardex.BALANCO_CANCELADO)) {
                BalancoControle balancoControle = (BalancoControle) JSFUtilities.getManagedBean("BalancoControle");
                balancoControle.consultarBalanco(this.cardexVO.getCodigo());
                setTelaVisualizarDetalhes("abrirPopup('balancoForm.jsp', 'Compra', 800, 595);");
            } else if ((this.cardexVO.getOperacao() == OperacaoCardex.VENDA) ||
                    (this.cardexVO.getOperacao() == OperacaoCardex.VENDA_CANCELADA) || (this.cardexVO.getOperacao() == OperacaoCardex.VENDA_CANCELADA_BALANCO)) {
                if (!UteisValidacao.emptyNumber(this.getCardexVO().getCodigoPessoa())) {
                    ClienteControle clienteControle = (ClienteControle) getControlador(ClienteControle.class);
                    ClienteVO cliente = getFacade().getCliente().consultarPorCodigoPessoa(this.cardexVO.getCodigoPessoa(), codigoEmpresa, Uteis.NIVELMONTARDADOS_TODOS);

                    if (cliente != null && cliente.getCodigo() > 0) {
                        clienteControle.setClienteVO(cliente);
                        clienteControle.validarTela();
                        setTelaVisualizarDetalhes("abrirPopup('clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);");
                    } else {
                        ColaboradorVO colaboradorVO = getFacade().getColaborador().consultarPorCodigoPessoa(this.cardexVO.getCodigoPessoa(), getEmpresaLogado().getCodigo(), Uteis.NIVELMONTARDADOS_MINIMOS);
                        irParaTelaColaborador(colaboradorVO);
                        setTelaVisualizarDetalhes("abrirPopup('colaboradorForm.jsp', 'Colaborador', 1024, 700);");
                    }

                } else if (!UteisValidacao.emptyNumber(this.cardexVO.getVendaavulsa())) {
                    VendaConsumidorControle vendaControle = (VendaConsumidorControle) getControlador(VendaConsumidorControle.class);
                    vendaControle.montarListaHistoricoCompras(this.cardexVO.getVendaavulsa());
                    setTelaVisualizarDetalhes("abrirPopup('vendaConsumidorForm.jsp', 'VendaConsumidor', 1000, 650);");
                } else {
                    montarMsgAlert("Venda Cancelada de Consumidor de " + this.getCardexVO().getTotalEntrada() + " produto(s) no dia " + this.getCardexVO().getDataApresentar());
                    setTelaVisualizarDetalhes(getMsgAlert());
                }
            }
            setSucesso(true);
            setErro(false);
            setMensagemID("msg_entre_prmconsulta");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
        }

    }

    public void visualizarDetalhes() {
        try {
            SimpleDateFormat sdf = new  SimpleDateFormat("dd/MM/yyyy");
            this.cardexVO = (CardexVO) context().getExternalContext().getRequestMap().get("cardex");
            if ((this.cardexVO.getOperacao() == CardexVO.OperacaoCardex.INCLUIDO_CONTROLE_ESTOQUE) ||
                    (this.cardexVO.getOperacao() == CardexVO.OperacaoCardex.REMOVIDO_CONTROLE_ESTOQUE)){
                return ;
            }
            setMostrarColunaDataCancelVenda(this.cardexVO.getOperacao() == CardexVO.OperacaoCardex.VENDA_CANCELADA);
            setMostrarColunaDataEstorno(this.cardexVO.getOperacao() == OperacaoCardex.VENDA_CANCELADA_BALANCO);
            setMostrarColunaNome(this.cardexVO.getOperacao() == CardexVO.OperacaoCardex.COMPRA ||
                                 this.cardexVO.getOperacao() == CardexVO.OperacaoCardex.COMPRA_CANCELADA ||
                                 this.cardexVO.getOperacao() == CardexVO.OperacaoCardex.VENDA ||
                                 this.cardexVO.getOperacao() == CardexVO.OperacaoCardex.VENDA_CANCELADA ||
                    this.cardexVO.getOperacao() == OperacaoCardex.VENDA_CANCELADA_BALANCO);
            this.listaDetalhesOperacao = getFacade().getCardex().consultarDetalhesOperacao(codigoEmpresa, produtoVO.getCodigo(), cardexVO);
            totalEntrada = 0;
            totalSaida = 0;
            for (CardexVO obj: listaDetalhesOperacao){
                totalEntrada = totalEntrada + ((obj.getTotalEntrada() != null) ? obj.getTotalEntrada():0);
                totalSaida = totalSaida + ((obj.getTotalSaida() != null) ? obj.getTotalSaida():0);
            }
            setDescricaoDetalhes("Relação de " + this.cardexVO.getOperacao().getDescricao() + " no dia: " + sdf.format(this.cardexVO.getData()));
            setMostrarDetalhesOperacao(true);
            setSucesso(true);
            setErro(false);
            setMensagemID("msg_entre_prmconsulta");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
        }
    }


    public List getListaSelectItemEmpresa() {
        return listaSelectItemEmpresa;
    }

    public void setListaSelectItemEmpresa(List listaSelectItemEmpresa) {
        this.listaSelectItemEmpresa = listaSelectItemEmpresa;
    }
    public Date getPeriodoAte() {
        return periodoAte;
    }

    public void setPeriodoAte(Date periodoAte) {
        this.periodoAte = periodoAte;
    }

    public Date getPeriodoDe() {
        return periodoDe;
    }

    public void setPeriodoDe(Date periodoDe) {
        this.periodoDe = periodoDe;
    }


    public List<ProdutoVO> executarAutocompletePesqProduto(Object suggest) {
        List<ProdutoVO> listaProdutos = null;
        try {
            if (!getUsuarioLogado().getAdministrador()){
                this.codigoEmpresa = getEmpresaLogado().getCodigo();
            }
            if ((this.codigoEmpresa == null) ||(this.codigoEmpresa <= 0)){
                throw new ConsistirException("Antes de selecionar o produtos é necessário informar a empresa.");
            }
            String nomePesq = (String) suggest;
            listaProdutos = getFacade().getProduto().consultarProdutosComControleEstoque(this.codigoEmpresa, null, nomePesq, false, Uteis.NIVELMONTARDADOS_TELACONSULTA);
            setMensagemID("msg_dados_consultados");
            setSucesso(true);
            setErro(false);

        } catch (Exception e) {
            setSucesso(false);
            setErro(true);
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
        return listaProdutos;
    }

    public void selecionarProduto() {
        ProdutoVO obj = (ProdutoVO) context().getExternalContext().getRequestMap().get("result");
        this.produtoVO = obj;
        setPaginaAtualDeTodas("0/0");
        setListaConsulta(new ArrayList());
        definirVisibilidadeLinksNavegacao(0, 0);
        setMensagemID("msg_entre_prmconsulta");;
    }

    public String getProdutoSelecionado() {
        return produtoSelecionado;
    }

    public void setProdutoSelecionado(String produtoSelecionado) {
        this.produtoSelecionado = produtoSelecionado;
    }

    public Integer getCodigoEmpresa() {
        return codigoEmpresa;
    }

    public void setCodigoEmpresa(Integer codigoEmpresa) {
        this.codigoEmpresa = codigoEmpresa;
    }


    public void validarPermissaoVisualizarCardex() throws Exception {
        if (getUsuarioLogado().getUsuarioPerfilAcessoVOs().isEmpty()) {
            if (getUsuarioLogado().getAdministrador()) {
                setMensagemDetalhada("");
                setSucesso(true);
                return;
            }
            throw new Exception("O usuário informado não tem nenhum perfil acesso informado.");
        }
        Iterator i = getUsuarioLogado().getUsuarioPerfilAcessoVOs().iterator();
        while (i.hasNext()) {
            UsuarioPerfilAcessoVO usuarioPerfilAcesso = (UsuarioPerfilAcessoVO) i.next();
            if (getEmpresaLogado().getCodigo().equals(usuarioPerfilAcesso.getEmpresa().getCodigo().intValue())) {
                usuarioPerfilAcesso.getPerfilAcesso().setPermissaoVOs(getFacade().getPermissao().consultarPermissaos(usuarioPerfilAcesso.getPerfilAcesso().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                getFacade().getControleAcesso().verificarPermissaoUsuarioFuncionalidade(usuarioPerfilAcesso.getPerfilAcesso(),
                        getUsuarioLogado(), "VisualizarCardex", "12.05 - Visualizar Cardex");
            }
        }
    }

    public CardexVO getCardexVO() {
        return cardexVO;
    }

    public void setCardexVO(CardexVO cardexVO) {
        this.cardexVO = cardexVO;
    }

    public String getDescricaoDetalhes() {
        return descricaoDetalhes;
    }

    public void setDescricaoDetalhes(String descricaoDetalhes) {
        this.descricaoDetalhes = descricaoDetalhes;
    }

    public ProdutoVO getProdutoVO() {
        return produtoVO;
    }

    public void setProdutoVO(ProdutoVO produtoVO) {
        this.produtoVO = produtoVO;
    }

    public boolean isMostrarDetalhesOperacao() {
        return mostrarDetalhesOperacao;
    }

    public void setMostrarDetalhesOperacao(boolean mostrarDetalhesOperacao) {
        this.mostrarDetalhesOperacao = mostrarDetalhesOperacao;
    }


    public void fecharModalDetalhe(){
        setMostrarDetalhesOperacao(false);
    }

    public List<CardexVO> getListaDetalhesOperacao() {
        return listaDetalhesOperacao;
    }

    public void setListaDetalhesOperacao(List<CardexVO> listaDetalhesOperacao) {
        this.listaDetalhesOperacao = listaDetalhesOperacao;
    }

    public boolean isMostrarColunaNome() {
        return mostrarColunaNome;
    }

    public void setMostrarColunaNome(boolean mostrarColunaNome) {
        this.mostrarColunaNome = mostrarColunaNome;
    }

    public String getDescricaoColuna() {
        if ((this.cardexVO.getOperacao() == OperacaoCardex.COMPRA) || (this.cardexVO.getOperacao() == OperacaoCardex.COMPRA_CANCELADA))
            return "Fornecedor";
        else if ((this.cardexVO.getOperacao() == OperacaoCardex.VENDA) || (this.cardexVO.getOperacao() == OperacaoCardex.VENDA_CANCELADA)
                || (this.cardexVO.getOperacao() == OperacaoCardex.VENDA_CANCELADA_BALANCO))
                return "Cliente";
        else return "";
    }

    public void setDescricaoColuna(String descricaoColuna) {
        this.descricaoColuna = descricaoColuna;
    }

    public Integer getTotalEntrada() {
        return totalEntrada;
    }

    public void setTotalEntrada(Integer totalEntrada) {
        this.totalEntrada = totalEntrada;
    }

    public Integer getTotalSaida() {
        return totalSaida;
    }

    public void setTotalSaida(Integer totalSaida) {
        this.totalSaida = totalSaida;
    }

    public boolean isOperacaoProdutoEstoque() {
        return operacaoProdutoEstoque;
    }

    public void setOperacaoProdutoEstoque(boolean operacaoProdutoEstoque) {
        this.operacaoProdutoEstoque = operacaoProdutoEstoque;
    }

    public boolean isMostrarColunaDataCancelVenda() {
        return mostrarColunaDataCancelVenda;
    }

    public void setMostrarColunaDataCancelVenda(boolean mostrarColunaDataCancelVenda) {
        this.mostrarColunaDataCancelVenda = mostrarColunaDataCancelVenda;
    }

    public String getTelaVisualizarDetalhes() {
        return telaVisualizarDetalhes;
    }

    public void setTelaVisualizarDetalhes(String telaVisualizarDetalhes) {
        this.telaVisualizarDetalhes = telaVisualizarDetalhes;
    }

    public boolean isMostrarColunaDataEstorno() {
        return mostrarColunaDataEstorno;
    }

    public void setMostrarColunaDataEstorno(boolean mostrarColunaDataEstorno) {
        this.mostrarColunaDataEstorno = mostrarColunaDataEstorno;
    }
}

package controle.estoque.xmlNfe;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlElementWrapper;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;

import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
public class InfNfe {

    @JacksonXmlProperty(localName = "ide")
    private Ide ide;

    @JacksonXmlProperty(localName = "emit")
    private Emitente emitente;

    @JacksonXmlProperty(localName = "dest")
    private Destinatario destinatario;

    @JacksonXmlElementWrapper(useWrapping = false)
    @JacksonXmlProperty(localName = "det")
    private List<DetalheProduto> detalhesProduto;

    public Ide getIde() {
        return ide;
    }

    public void setIde(Ide ide) {
        this.ide = ide;
    }

    public Emitente getEmitente() {
        return emitente;
    }

    public void setEmitente(Emitente emitente) {
        this.emitente = emitente;
    }

    public Destinatario getDestinatario() {
        return destinatario;
    }

    public void setDestinatario(Destinatario destinatario) {
        this.destinatario = destinatario;
    }

    public List<DetalheProduto> getDetalhesProduto() {
        return detalhesProduto;
    }

    public void setDetalhesProduto(List<DetalheProduto> detalhesProduto) {
        this.detalhesProduto = detalhesProduto;
    }
}
package controle.estoque.xmlNfe;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;

@JsonIgnoreProperties(ignoreUnknown = true)
public class DetalheProduto {

    private boolean cadastrarNovo;
    private Double valorFinal;
    private Integer produtoParaVincularNoEstoque;

    @JacksonXmlProperty(isAttribute = true, localName = "nItem")
    private Integer nItem;

    @JacksonXmlProperty(localName = "prod")
    private ProdutoXmlNfe produto;

    @JacksonXmlProperty(localName = "imposto")
    private Imposto imposto;

    @JacksonXmlProperty(localName = "infAdProd")
    private String infAdProdText;

    public DetalheProduto(Integer nItem, ProdutoXmlNfe produto, Imposto imposto, String infAdProdText) {
        this.nItem = nItem;
        this.produto = produto;
        this.imposto = imposto;
        this.infAdProdText = infAdProdText;
    }

    public DetalheProduto() {
    }

    public Integer getnItem() {
        return nItem;
    }

    public void setnItem(Integer nItem) {
        this.nItem = nItem;
    }

    public ProdutoXmlNfe getProduto() {
        return produto;
    }

    public void setProduto(ProdutoXmlNfe produto) {
        this.produto = produto;
    }

    public Imposto getImposto() {
        return imposto;
    }

    public void setImposto(Imposto imposto) {
        this.imposto = imposto;
    }

    public String getInfAdProdText() {
        return infAdProdText;
    }

    public void setInfAdProdText(String infAdProdText) {
        this.infAdProdText = infAdProdText;
    }

    public boolean getCadastrarNovo() {
        return cadastrarNovo;
    }

    public Integer getProdutoParaVincularNoEstoque() {
        return produtoParaVincularNoEstoque;
    }

    public void setProdutoParaVincularNoEstoque(Integer produtoParaVincularNoEstoque) {
        this.produtoParaVincularNoEstoque = produtoParaVincularNoEstoque;
    }


    public void setCadastrarNovo(boolean cadastrarNovo) {
        this.cadastrarNovo = cadastrarNovo;
    }

    public Double getValorFinal() {
        return valorFinal;
    }

    public void setValorFinal(Double valorFinal) {
        this.valorFinal = valorFinal;
    }
}

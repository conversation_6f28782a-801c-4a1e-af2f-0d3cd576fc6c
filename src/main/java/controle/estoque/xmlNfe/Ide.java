package controle.estoque.xmlNfe;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;

@JsonIgnoreProperties(ignoreUnknown = true)
public class Ide {

    @JacksonXmlProperty(localName = "cUF")
    private String cUF;

    @JacksonXmlProperty(localName = "natOp")
    private String natOp;

    @JacksonXmlProperty(localName = "mod")
    private String mod;

    @JacksonXmlProperty(localName = "nNF")
    private String numeroNF;

    @JacksonXmlProperty(localName = "dhEmi")
    private String dataEmitida;

    public String getcUF() {
        return cUF;
    }

    public void setcUF(String cUF) {
        this.cUF = cUF;
    }

    public String getNatOp() {
        return natOp;
    }

    public void setNatOp(String natOp) {
        this.natOp = natOp;
    }

    public String getMod() {
        return mod;
    }

    public void setMod(String mod) {
        this.mod = mod;
    }

    public String getNumeroNF() {
        return numeroNF;
    }

    public void setNumeroNF(String numeroNF) {
        this.numeroNF = numeroNF;
    }

    public String getDataEmitida() {
        return dataEmitida;
    }

    public void setDataEmitida(String dataEmitida) {
        this.dataEmitida = dataEmitida;
    }
}
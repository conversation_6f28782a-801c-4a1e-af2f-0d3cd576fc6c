package controle.estoque.xmlNfe;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;

@JsonIgnoreProperties(ignoreUnknown = true)
public class Imposto {

    @JacksonXmlProperty(localName = "vTotTrib")
    private String totalTributos;

    public String getTotalTributos() {
        return totalTributos;
    }

    public void setTotalTributos(String totalTributos) {
        this.totalTributos = totalTributos;
    }
}

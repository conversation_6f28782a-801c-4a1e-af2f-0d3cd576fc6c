package controle.estoque.xmlNfe;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;

@JsonIgnoreProperties(ignoreUnknown = true)
public class Nfe {

    @JacksonXmlProperty(localName = "infNFe")
    private InfNfe infNFe;

    public InfNfe getInfNFe() {
        return infNFe;
    }

    public void setInfNFe(InfNfe infNFe) {
        this.infNFe = infNFe;
    }
}
package controle.estoque.xmlNfe;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlRootElement;

@JacksonXmlRootElement(localName = "nfeProc")
@JsonIgnoreProperties(ignoreUnknown = true)
public class NfeProc {

    @JacksonXmlProperty(localName = "NFe")
    private Nfe nfe;

    public NfeProc() {
    }

    public NfeProc(Nfe nfe) {
        this.nfe = nfe;
    }

    public Nfe getNfe() {
        return nfe;
    }

    public void setNfe(Nfe nfe) {
        this.nfe = nfe;
    }
}

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package controle.estoque;

import br.com.pactosolucoes.comuns.util.JSFUtilities;
import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;


import negocio.comuns.utilitarias.Uteis;
import relatorio.controle.arquitetura.SuperControleRelatorio;
import java.util.Map;

import negocio.comuns.estoque.CompraVO;

/**
 *
 * <AUTHOR>
 */
public class RelatorioCompraControle extends SuperControleRelatorio {

    private CompraVO compraVO;
    private String retorno = "";

    public RelatorioCompraControle() throws Exception {
        obterUsuarioLogado();
    }

    public void imprimirRelatorioCompraPDF() {
        try {
            this.compraVO = (CompraVO) context().getExternalContext().getRequestMap().get("compra");
            if(compraVO ==  null){
                CompraControle compraCon = (CompraControle) JSFUtilities.getFromSession(CompraControle.class.getSimpleName());
                compraVO = compraCon.getCompraVO();
            }
            this.compraVO = getFacade().getCompra().consultarPorChavePrimaria(this.compraVO.getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);

            setListaRelatorio(new ArrayList());
            Map<String, Object> parametros = new HashMap<String, Object>();
            parametros.put("listaObjetos", this.compraVO.getItensList());
            parametros.put("nomeRelatorio", "RelatorioCompra");
            parametros.put("nomeEmpresa", compraVO.getEmpresa().getNome());
            parametros.put("nomeDesignIReport", getDesignIReportRelatorio());
            parametros.put("SUBREPORT_DIR", getCaminhoSubRelatorio());
            parametros.put("SUBREPORT_DIR1", getCaminhoSubRelatorio());
            parametros.put("SUBREPORT_DIR2", getCaminhoSubRelatorio());

            apresentarRelatorioObjetos(parametros);
            setMensagemDetalhada("", "");
            setMensagemID("msg_entre_prmrelatorio");

        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        } finally {
            this.compraVO = null;
        }
    }

    public String getIrPara() {
        return retorno;
    }

    public static String getDesignIReportRelatorio() {
        return ("relatorio" + File.separator + "designRelatorio"
                + File.separator + "estoque" + File.separator + "RelatorioCompra.jrxml");
    }

    public static String getCaminhoSubRelatorio() {
        return ("relatorio" + File.separator + "designRelatorio"
                + File.separator + "estoque" + File.separator);
    }

    public String getRetorno() {
        return retorno;
    }

    public void setRetorno(String retorno) {
        this.retorno = retorno;
    }

    public CompraVO getCompraVO() {
        return compraVO;
    }

    public void setCompraVO(CompraVO compraVO) {
        this.compraVO = compraVO;
    }
}

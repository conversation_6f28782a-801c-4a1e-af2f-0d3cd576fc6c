/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package controle.estoque;

import br.com.pactosolucoes.comuns.notificacao.RecursoSistema;
import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.estrutura.exportador.controle.ExportadorListaControle;
import controle.arquitetura.FuncionalidadeSistemaEnum;
import controle.arquitetura.SuperControle;

import java.util.*;
import javax.faces.event.ActionEvent;
import javax.faces.model.SelectItem;
import controle.arquitetura.security.LoginControle;
import negocio.comuns.arquitetura.LogVO;
import negocio.comuns.arquitetura.UsuarioPerfilAcessoVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.estoque.BalancoItensVO;
import negocio.comuns.estoque.BalancoVO;
import negocio.comuns.estoque.ProdutoEstoqueVO;
import negocio.comuns.plano.ProdutoVO;
import negocio.comuns.utilitarias.*;

/**
 *
 * <AUTHOR>
 */
public class BalancoControle extends SuperControle {

    private BalancoVO balancoVO;
    public static final  String NOME_ENTIDADE = "BALANCO";
    protected List listaSelectItemEmpresa;
    protected List listaSelectItemSituacao;
    private BalancoItensVO balancoItensVO;
    // Atributos para pesquisa.
    private Date periodoDe = Calendario.hoje();
    private Date periodoAte = Calendario.hoje();
    private int situacao;
    private Integer codigoEmpresa;
    // Atributos para o suggestion Box de Produto.
    private String produtoSelecionado = "";

    public BalancoControle() throws Exception {
        inicializarDados();
    }

    public List getBalancoItens() {
        return getBalancoVO().getItensList();
    }

    private void inicializarDados() throws Exception {
        obterUsuarioLogado();
        montarListaSelectItemEmpresa();
        montarListaSelectItemSituacao();
        setControleConsulta(new ControleConsulta());
        setBalancoItensVO(new BalancoItensVO());
        getBalancoItensVO().registrarObjetoVOAntesDaAlteracao();
    }

    public void clonar() {
        this.balancoVO.setCodigo(new Integer(0));
        this.balancoVO.setCancelado(false);
        this.balancoVO.setNovoObj(true);
        setMensagemID("msg_entre_dados");
        setSucesso(false);
        setErro(false);
    }

    public void cancelarBalanco() {
        try {
            validarPermissaoCancelarBalanco();
            validarCancelamentoBalanco(balancoVO);
            this.balancoVO.setCancelado(true);
            this.balancoVO.setUsuarioCancelamento(getUsuarioLogado());
            getFacade().getBalanco().cancelar(balancoVO);
            registrarLogObjetoVO(balancoVO,balancoVO.getCodigo(),NOME_ENTIDADE,0);
            setMensagemID("msg_dados_gravados");
            setSucesso(true);
            setErro(false);
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
        }

    }

    public void montarListaSelectItemEmpresa() throws Exception {
        List listaEmpresa = new ArrayList();
        listaEmpresa.add(new SelectItem(new Integer(0), ""));
        List listaConsulta = getFacade().getEmpresa().consultarPorNome("",true, false, Uteis.NIVELMONTARDADOS_TODOS);
        for (Object obj : listaConsulta) {
            EmpresaVO empresa = (EmpresaVO) obj;
            listaEmpresa.add(new SelectItem(empresa.getCodigo(), empresa.getNome()));
        }
        setListaSelectItemEmpresa(listaEmpresa);
    }

    public void montarListaSelectItemSituacao() throws Exception {
        this.listaSelectItemSituacao = new ArrayList();
        this.listaSelectItemSituacao.add(new SelectItem(-1, "Todos"));
        this.listaSelectItemSituacao.add(new SelectItem(0, "Ativo"));
        this.listaSelectItemSituacao.add(new SelectItem(1, "Cancelado"));
    }

    /* M?todo respons?vel por adicionar um novo objeto da classe <code>BalancoItensVO</code>
     * para o objeto <code>BalancoVO</code> da classe <code>Balanco</code>
     */
    public void adicionarItensBalanco() {
        try {
            limparMsg();
            setMsgAlert("");
            balancoItensVO.setBalanco(balancoVO);
            BalancoItensVO.validarDadosTelaInclusao(balancoItensVO);
            this.balancoVO.getItens().add(balancoItensVO);
            this.balancoItensVO = new BalancoItensVO();
            this.produtoSelecionado = "";
            setMsgAlert("document.getElementById('form:nomeProdutoSelecionado').select();");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setMsgAlert("document.getElementById('form:quantidade').select();");
        }
    }

    private void validarParametrosPesquisa() throws Exception {
        if (this.periodoDe != null) {
            if (this.periodoAte == null) {
                throw new ConsistirException("Informe a data final");
            }
        }
        if (this.periodoAte != null) {
            if (this.periodoDe == null) {
                throw new ConsistirException("Informe a data inicial");
            }
        }
        /*if ((periodoDe == null) &&  (periodoAte == null) &&
        ((codigoFornecedor == null) || (codigoFornecedor.intValue() <= 0)) &&
        (numeroNF.equals(""))){
        throw new ConsistirException("ï¿½ necessï¿½rio informar ao menos um parï¿½metro para pesquisa.");
        }*/

    }

    /**
     * Rotina responsavel por executar as consultas disponiveis no JSP BalancoCons.jsp.
     * Define o tipo de consulta a ser executada, por meio de ComboBox denominado campoConsulta, disponivel neste mesmo JSP.
     * Como resultado, disponibiliza um List com os objetos selecionados na sessao da pagina.
     */
    public String consultar() {
        try {
            super.consultar();
            List objs = new ArrayList();
            validarParametrosPesquisa();

            if (!getUsuarioLogado().getAdministrador()) {
                this.codigoEmpresa = getEmpresaLogado().getCodigo();
            }

            List<BalancoVO> listaBalanco = getFacade().getBalanco().consultar(this.codigoEmpresa, periodoDe, periodoAte, situacao, Uteis.NIVELMONTARDADOS_TELACONSULTA);
            objs.addAll(listaBalanco);

            objs = ControleConsulta.obterSubListPaginaApresentar(objs, controleConsulta);
            definirVisibilidadeLinksNavegacao(controleConsulta.getPaginaAtual(), controleConsulta.getNrTotalPaginas());
            setListaConsulta(objs);
            limparMsg();
            return "consultar";
        } catch (Exception e) {
            setListaConsulta(new ArrayList());
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
            return "consultar";
        }
    }

    public String abrirTelaBalanco() {
        try {
            notificarRecursoEmpresa(RecursoSistema.fromDescricao(FuncionalidadeSistemaEnum.BALANCO.toString()));
            validarPermissaoCadastrarBalanco();
            setMsgAlert("abrirPopup('balancoCons.jsp', 'Balanco', 1000, 650);");
            return abrirTelaConsulta();
        } catch (Exception e) {
            montarMsgAlert(e.getMessage());
            return "";
        }
    }

    public String abrirTelaConsulta() {
        setSucesso(true);
        setErro(false);
        this.produtoSelecionado = "";
        setListaConsulta(null);
        setMensagemID("msg_entre_prmconsulta");
        return "consultar";
    }

    public void irPaginaInicial() throws Exception {
        controleConsulta.setPaginaAtual(1);
        this.consultar();
    }

    public void irPaginaAnterior() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getPaginaAtual() - 1);
        this.consultar();
    }

    public void irPaginaPosterior() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getPaginaAtual() + 1);
        this.consultar();
    }

    public void irPaginaFinal() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getNrTotalPaginas());
        this.consultar();
    }

    /**
     * Rotina responsï¿½vel por organizar a paginaï¿½ï¿½o entre as pï¿½ginas resultantes de uma consulta.
     */
    public String inicializarConsultar() {
        setPaginaAtualDeTodas("0/0");
        setListaConsulta(new ArrayList());
        definirVisibilidadeLinksNavegacao(0, 0);
        limparMsg();
        return "consultar";
    }

    public void removerItensBalanco() throws Exception {
        try {
            BalancoItensVO balancoItensVO = (BalancoItensVO) context().getExternalContext().getRequestMap().get("balancoItem");
            this.balancoVO.getItens().remove(balancoItensVO);
            setMensagemID("msg_dados_excluidos");
            setMensagem("");
            setMensagemDetalhada("");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public List getTipoConsultaCombo() {
        List itens = new ArrayList();
        itens.add(new SelectItem("nome", "Nome"));
        itens.add(new SelectItem("codigo", "Código"));

        return itens;
    }

    /**
     * Rotina responsï¿½vel por disponibilizar um novo objeto da classe <code>BalancoVO</code>
     * para ediï¿½ï¿½o pelo usuï¿½rio da aplicaï¿½ï¿½o.
     */
    public String novo() throws Exception {
        try {
            validarPermissaoCadastrarBalanco();
            this.balancoVO = new BalancoVO();
            this.balancoVO.setDataCadastro(Calendario.hoje());
            this.balancoVO.setUsuarioCadastro(getUsuarioLogado());
            this.balancoVO.setEmpresa(getEmpresaLogado());
            this.balancoVO.registrarObjetoVOAntesDaAlteracao();
            setBalancoItensVO(new BalancoItensVO());
            getBalancoItensVO().registrarObjetoVOAntesDaAlteracao();
            setMensagem("");
            setMensagemDetalhada("");
            setSucesso(true);
            setErro(false);
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
            return "consultar";
        }
        return "editar";
    }

    /**
     * Rotina responsï¿½vel por gravar no BD os dados editados de um novo objeto da classe <code>BalancoVO</code>.
     * Caso o objeto seja novo (ainda nï¿½o gravado no BD) ï¿½ acionado a operaï¿½ï¿½o <code>incluir()</code>. Caso contrï¿½rio ï¿½ acionado o <code>alterar()</code>.
     * Se houver alguma inconsistï¿½ncia o objeto nï¿½o ï¿½ gravado, sendo re-apresentado para o usuï¿½rio juntamente com uma mensagem de erro.
     */
    public String gravar() {
        try {
            balancoVO.setDataCadastro(Calendario.hoje());
            if (balancoVO.isNovoObj().booleanValue()) {
                getFacade().getBalanco().incluir(balancoVO);
                balancoVO.setNovoObj(true);
            }
            registrarLog();
            balancoVO.registrarObjetoVOAntesDaAlteracao();
            setMensagemID("msg_dados_gravados");
            setSucesso(true);
            setErro(false);
            return "editar";
        } catch (Exception e) {
            e.printStackTrace();
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
            return "editar";
        }
    }

    private void registrarLog() throws Exception {
        List logS = balancoVO.gerarLogAlteracaoObjetoVO();

        for (BalancoItensVO obj : balancoVO.getItensList()) {
            LogVO log = (LogVO) ((LogVO) logS.get(0)).getClone(true);
            log.setDescricao("Add produto no balanço: " + balancoVO.getCodigo());
            log.setOperacao("INCLUSÃO");
            log.setNomeCampo("Produto");
            log.setValorCampoAnterior("");
            log.setValorCampoAlterado(obj.getProduto().getDescricao() + ". Qtd= " + obj.getQtdeBalanco());
            logS.add(log);
        }

        registrarLogObjetoVO(logS, balancoVO.getCodigo(), NOME_ENTIDADE, 0);
    }

    public void consultarBalanco(Integer codigoBalanco) {
        try {
            setBalancoVO(getFacade().getBalanco().consultarPorChavePrimaria(codigoBalanco, Uteis.NIVELMONTARDADOS_TODOS));
            getBalancoVO().setNovoObj(new Boolean(false));
            setSucesso(true);
            setErro(false);
            setMensagemID("msg_dados_editar");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
        }

    }

    /**
     * Rotina responsï¿½vel por disponibilizar os dados de um objeto da classe <code>BalancoVO</code> para alteraï¿½ï¿½o.
     * O objeto desta classe ï¿½ disponibilizado na session da pï¿½gina (request) para que o JSP correspondente possa disponibilizï¿½-lo para ediï¿½ï¿½o.
     */
    public String editar() {
        Integer codigoConsulta = Integer.parseInt(request().getParameter("chavePrimaria"));
        try {
            validarPermissaoCadastrarBalanco();
            setBalancoVO(getFacade().getBalanco().consultarPorChavePrimaria(codigoConsulta, Uteis.NIVELMONTARDADOS_TODOS));
            getBalancoVO().setNovoObj(new Boolean(false));
            getBalancoVO().registrarObjetoVOAntesDaAlteracao();
            this.balancoItensVO = new BalancoItensVO();
            this.balancoItensVO.registrarObjetoVOAntesDaAlteracao();
        } catch (Exception e) {
            setErro(true);
            setSucesso(false);
            setMensagemDetalhada("msg_erro", e.getMessage());
            return "consultar";
        }
        return "editar";
    }

    public void editarBalancoItem() throws Exception {
        try {
            this.balancoItensVO = (BalancoItensVO) context().getExternalContext().getRequestMap().get("balancoItem");
            this.produtoSelecionado = balancoItensVO.getProduto().getDescricao();
            this.balancoItensVO.registrarObjetoVOAntesDaAlteracao();
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }
    public void realizarConsultaLogObjetoSelecionadoBalanco() {
        LoginControle loginControle = (LoginControle) context().getExternalContext().getSessionMap().get("LoginControle");
        if (balancoVO.getCodigo() != 0) {
            String nomeClasse = "BALANCO";
            loginControle.setNomeClasse(loginControle.getNomeEntidadeCamposLog("prt_" + nomeClasse + "_tituloForm"));
            loginControle.consultarLogObjetoSelecionado(NOME_ENTIDADE.toUpperCase(),
                    balancoVO.getCodigo(), 0);
        }else{
            loginControle.limparListaLog();
        }
    }

    public List<ProdutoVO> executarAutocompletePesqProduto(Object suggest) {
        List<ProdutoVO> listaProdutos = new ArrayList<>();
        try {
            limparMsg();
            if ((this.balancoVO.getEmpresa() == null)
                    || (this.balancoVO.getEmpresa().getCodigo() == null)
                    || (this.balancoVO.getEmpresa().getCodigo() <= 0)) {
                throw new ConsistirException("Antes de incluir os produtos é necessário informar a empresa do balanço.");
            }
            String nomePesq = (String) suggest;
            listaProdutos = getFacade().getProduto().consultarProdutosComControleEstoque(this.balancoVO.getEmpresa().getCodigo(), null, nomePesq, true, Uteis.NIVELMONTARDADOS_TELACONSULTA);
        } catch (Exception e) {
            montarErro(e);
        }
        return listaProdutos;
    }

    public void selecionarProduto() {
        try {
            limparMsg();
            setMsgAlert("");
            ProdutoVO obj = (ProdutoVO) context().getExternalContext().getRequestMap().get("result");
            this.balancoItensVO.setProduto(obj);
            ProdutoEstoqueVO produtoEstoqueVO = getFacade().getProdutoEstoque().consultarPorProduto(obj.getCodigo(), this.balancoVO.getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            this.balancoItensVO.setQtdeEstoqueAnterior(produtoEstoqueVO.getEstoque());
            setMsgAlert("document.getElementById('form:quantidade').select();");
        } catch (Exception e) {
            setSucesso(false);
            setErro(true);
            setMensagemDetalhada("msg_erro", e.getMessage());
            setMsgAlert("document.getElementById('form:nomeProdutoSelecionado').select();");
        }
    }

    public BalancoItensVO getBalancoItensVO() {
        return balancoItensVO;
    }

    public void setBalancoItensVO(BalancoItensVO balancoItensVO) {
        this.balancoItensVO = balancoItensVO;
    }

    public BalancoVO getBalancoVO() {
        return balancoVO;
    }

    public void setBalancoVO(BalancoVO balancoVO) {
        this.balancoVO = balancoVO;
    }

    public List getListaSelectItemEmpresa() {
        return listaSelectItemEmpresa;
    }

    public void setListaSelectItemEmpresa(List listaSelectItemEmpresa) {
        this.listaSelectItemEmpresa = listaSelectItemEmpresa;
    }

    public Date getPeriodoAte() {
        return periodoAte;
    }

    public void setPeriodoAte(Date periodoAte) {
        this.periodoAte = periodoAte;
    }

    public Date getPeriodoDe() {
        return periodoDe;
    }

    public void setPeriodoDe(Date periodoDe) {
        this.periodoDe = periodoDe;
    }

    public String getProdutoSelecionado() {
        return produtoSelecionado;
    }

    public void setProdutoSelecionado(String produtoSelecionado) {
        this.produtoSelecionado = produtoSelecionado;
    }

    public int getSituacao() {
        return situacao;
    }

    public void setSituacao(int situacao) {
        this.situacao = situacao;
    }

    public List getListaSelectItemSituacao() {
        return listaSelectItemSituacao;
    }

    public void setListaSelectItemSituacao(List listaSelectItemSituacao) {
        this.listaSelectItemSituacao = listaSelectItemSituacao;
    }

    public Integer getCodigoEmpresa() {
        return codigoEmpresa;
    }

    public void setCodigoEmpresa(Integer codigoEmpresa) {
        this.codigoEmpresa = codigoEmpresa;
    }

    public void validarPermissaoCadastrarBalanco() throws Exception {
        if (getUsuarioLogado().getUsuarioPerfilAcessoVOs().isEmpty()) {
            if (getUsuarioLogado().getAdministrador()) {
                setMensagemDetalhada("");
                setSucesso(true);
                return;
            }
            throw new Exception("O usuário informado não tem nenhum perfil acesso informado.");
        }
        Iterator i = getUsuarioLogado().getUsuarioPerfilAcessoVOs().iterator();
        while (i.hasNext()) {
            UsuarioPerfilAcessoVO usuarioPerfilAcesso = (UsuarioPerfilAcessoVO) i.next();
            if (getEmpresaLogado().getCodigo().equals(usuarioPerfilAcesso.getEmpresa().getCodigo().intValue())) {
                usuarioPerfilAcesso.getPerfilAcesso().setPermissaoVOs(getFacade().getPermissao().consultarPermissaos(usuarioPerfilAcesso.getPerfilAcesso().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                getFacade().getControleAcesso().verificarPermissaoUsuarioFuncionalidade(usuarioPerfilAcesso.getPerfilAcesso(),
                        getUsuarioLogado(), "CadastrarBalanco", "12.03 - Cadastrar Balanï¿½o");
            }
        }
    }

    public void validarPermissaoCancelarBalanco() throws Exception {
        if (getUsuarioLogado().getUsuarioPerfilAcessoVOs().isEmpty()) {
            if (getUsuarioLogado().getAdministrador()) {
                setMensagemDetalhada("");
                setSucesso(true);
                return;
            }
            throw new Exception("O usuário informado não tem nenhum perfil acesso informado.");
        }
        Iterator i = getUsuarioLogado().getUsuarioPerfilAcessoVOs().iterator();
        while (i.hasNext()) {
            UsuarioPerfilAcessoVO usuarioPerfilAcesso = (UsuarioPerfilAcessoVO) i.next();
            if (getEmpresaLogado().getCodigo().equals(usuarioPerfilAcesso.getEmpresa().getCodigo().intValue())) {
                usuarioPerfilAcesso.getPerfilAcesso().setPermissaoVOs(getFacade().getPermissao().consultarPermissaos(usuarioPerfilAcesso.getPerfilAcesso().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                getFacade().getControleAcesso().verificarPermissaoUsuarioFuncionalidade(usuarioPerfilAcesso.getPerfilAcesso(),
                        getUsuarioLogado(), "CancelarBalanco", "12.04 - Cancelar Balanço");
            }
        }
    }

    private void validarCancelamentoBalanco(BalancoVO balancoVO) throws Exception {
        BalancoVO ultimoBalancoAtivo = getFacade().getBalanco().consultarUltimoBalancoAtivo(balancoVO.getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        if (!ultimoBalancoAtivo.getCodigo().equals(balancoVO.getCodigo())) {
            throw new Exception("É permitido cancelar somente o último balanço.");
        }

        for (BalancoItensVO balancoItensVO : balancoVO.getItens()) {
            if (getFacade().getMovProduto().existeMovprodutoPorProdutoAPartirDe(balancoItensVO.getProduto().getCodigo(), balancoVO.getDataCadastro())) {
                throw new Exception("Não é permitido cancelar o balanço pois existem movimentações de produto realizadas com os itens deste balanço.");
            }
        }

    }

    public void exportar(ActionEvent evt) throws Exception {
        ExportadorListaControle exportadorListaControle = (ExportadorListaControle) JSFUtilities.getFromRequest(ExportadorListaControle.class.getSimpleName());
        String paramsTableFiltrada = context().getExternalContext().getRequestParameterMap().get("paramsTableFiltrada");

        String[] split = paramsTableFiltrada.split(",");
        String campoOrdenacao = split[0].replace("[", "");
        String ordem = split[1];
        String filtro = split[2].replace("''", "");
        filtro = filtro.replace("]", "");
        List listaParaImpressao = getFacade().getBalanco().consultarParaImpressao(filtro, ordem, campoOrdenacao, getEmpresaLogado() == null ? 0 : getEmpresaLogado().getCodigo());
        exportadorListaControle.exportar(evt, listaParaImpressao, filtro, null);

    }
    public void realizarConsultaLogObjetoGeral() {
        balancoVO = new BalancoVO();
        realizarConsultaLogObjetoSelecionadoBalanco();
    }

}

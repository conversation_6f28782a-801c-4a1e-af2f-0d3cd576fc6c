/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package controle.estoque;

import br.com.pactosolucoes.comuns.util.JSFUtilities;
import java.io.File;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;


import relatorio.controle.arquitetura.SuperControleRelatorio;
import java.util.Map;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.estoque.CardexVO;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;


/**
 *
 * <AUTHOR>
 */
public class RelatorioCardexControle extends SuperControleRelatorio {

    private String retorno = "";

    public RelatorioCardexControle() throws Exception {
        obterUsuarioLogado();
    }

    public void imprimirRelatorioPDF() {
        CardexControle cardexControle = (CardexControle) JSFUtilities.getManagedBean("CardexControle");
        try {
            cardexControle.validarParametrosPesquisa();
            List<CardexVO> lista = getFacade().getCardex().consultarCardexAnalitico(cardexControle.getCodigoEmpresa(),
                                   cardexControle.getProdutoVO().getCodigo(), cardexControle.getPeriodoDe(), cardexControle.getPeriodoAte());
            if (lista.size() == 1){
               throw new ConsistirException("Não há dados para serem exibidos ! verifique os parâmetros informados.");
                // foi encontrado somente a data em que o produto foi adicionado ao controle de estoque
                //lista = new ArrayList<CardexVO>();
            }
            
            EmpresaVO  empresaVO = getFacade().getEmpresa().consultarPorChavePrimaria(cardexControle.getCodigoEmpresa(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            SimpleDateFormat sdf = new SimpleDateFormat("dd/MM/yyyy");
            setListaRelatorio(new ArrayList());
            Map<String, Object> parametros  = new HashMap<String, Object>();
            parametros.put("nomeEmpresa",empresaVO.getNome());
            parametros.put("enderecoEmpresa",empresaVO.getEndereco());
            parametros.put("cidadeEmpresa",empresaVO.getCidade().getNome());
            parametros.put("nomeProduto",cardexControle.getProdutoVO().getDescricao());
            parametros.put("dataInicial", sdf.format(cardexControle.getPeriodoDe()));
            parametros.put("dataFinal", sdf.format(cardexControle.getPeriodoAte()));
            parametros.put("listaObjetos", lista);
            parametros.put("nomeRelatorio", "RelatorioCardex");
            parametros.put("nomeDesignIReport", getDesignIReportRelatorio());
            parametros.put("SUBREPORT_DIR", getCaminhoSubRelatorio());
            parametros.put("SUBREPORT_DIR1", getCaminhoSubRelatorio());
            parametros.put("SUBREPORT_DIR2", getCaminhoSubRelatorio());

            apresentarRelatorioObjetos(parametros);
            cardexControle.setMensagemDetalhada("", "");
            cardexControle.setMensagemID("msg_entre_prmrelatorio");
            cardexControle.setErro(false);
            cardexControle.setSucesso(true);

        } catch (Exception e) {
            cardexControle.setMensagemDetalhada("msg_erro", e.getMessage());
            cardexControle.setSucesso(false);
            cardexControle.setErro(true);

            setMensagemDetalhada("msg_erro", e.getMessage());
        } finally {
            //
        }
    }


    public String getIrPara() {
        return retorno;
    }

    public static String getDesignIReportRelatorio() {
        return ("relatorio" + File.separator + "designRelatorio"
                + File.separator + "estoque" + File.separator + "RelatorioCardex.jrxml");
    }

    public static String getCaminhoSubRelatorio() {
        return ("relatorio" + File.separator + "designRelatorio"
                + File.separator + "estoque" + File.separator);
    }

    public String getRetorno() {
        return retorno;
    }

    public void setRetorno(String retorno) {
        this.retorno = retorno;
    }
}

/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package controle.modulos;

import br.com.pactosolucoes.comuns.util.JSFUtilities;
import controle.arquitetura.SuperControle;
import controle.arquitetura.view.ComboBoxEmpresaControle;
import controle.arquitetura.view.TreeViewControle;
import controle.arquitetura.view.TreeViewNode;
import controle.basico.ListaClientesControle;
import controle.crm.TreeViewColaboradorControle;
import java.lang.reflect.Method;
import java.sql.Connection;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Hashtable;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.Vector;
import java.util.logging.Level;
import java.util.logging.Logger;
import javax.faces.event.ActionEvent;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.contrato.Contrato;
import negocio.facade.jdbc.utilitarias.Conexao;
import negocio.modulos.smartbox.SmartBoxThreadCalculator;
import negocio.modulos.smartbox.SmartBoxVO;
import negocio.modulos.smartbox.TotalPorIndicadorTO;
import negocio.modulos.smartbox.enumerador.IndicadorSmartBoxEnum;
import negocio.modulos.smartbox.enumerador.TipoBoxEnum;
import org.jfree.data.category.DefaultCategoryDataset;

/**
 *
 * <AUTHOR>
 */
public class SmartBoxControle extends SuperControle {

    private List<SmartBoxVO> listaCaixas = new ArrayList<SmartBoxVO>();
    private List<SmartBoxThreadCalculator> listaThreads = new ArrayList<SmartBoxThreadCalculator>();
    private Integer totalContratosAtivos = 0;
    private Integer totalContratosAtivosMesAnterior = 0;
    private DefaultCategoryDataset comparativoBarra;
    private SmartBoxVO caixaComparativo = new SmartBoxVO();
    private Map<String, Integer> dadosGrafico = new HashMap<String, Integer>();
    private Boolean percentagem = Boolean.FALSE;
    //flex - CHART
    private Hashtable<String, Integer> data = new Hashtable<String, Integer>();
    private Vector<String> names = new Vector<String>();
    private Vector<String> colors = new Vector<String>();
    private TotalPorIndicadorTO indicadorComparativo = new TotalPorIndicadorTO();

    /**
     * Joao Alcides
     * 01/02/2012
     */
    public void montarComparativo(ActionEvent event) throws Exception {
        dadosGrafico = new HashMap<String, Integer>();
        EmpresaVO empresa = (EmpresaVO) JSFUtilities.getManagedBeanValue(ComboBoxEmpresaControle.class.getSimpleName() + ".empresaVO");
        this.caixaComparativo = (SmartBoxVO) event.getComponent().getAttributes().get("caixa");
        indicadorComparativo = (TotalPorIndicadorTO) event.getComponent().getAttributes().get("totalPorIndicador");
        if (indicadorComparativo == null) {
            indicadorComparativo = caixaComparativo.getListaIndicadores().get(0);
        }
        this.setComparativoBarra(new DefaultCategoryDataset());
        IndicadorSmartBoxEnum.idSelecionada = indicadorComparativo.getIndicador().getId();
        for (ColaboradorVO colaborador : obterColaboradores()) {
            TotalPorIndicadorTO totalPorInd = new TotalPorIndicadorTO();
            totalPorInd.setColaboradoresSelecionados(new ArrayList<ColaboradorVO>());
            totalPorInd.getColaboradoresSelecionados().add(colaborador);
            totalPorInd.setIndicador(indicadorComparativo.getIndicador());
            totalPorInd.setTipoBox(indicadorComparativo.getTipoBox());
            SmartBoxThreadCalculator t = new SmartBoxThreadCalculator(Conexao.getFromSession(),
                    totalPorInd, empresa.getCodigo());
            t.setName("Thread" + caixaComparativo.getTipo() + "_" + totalPorInd.getIndicador());
            listaThreads.add(t);
            t.start();
            // esperar todas threads terminarem
            Thread.sleep(50);
            while (listaThreads.size() > 0 && esperarThreadsAcabarem()) {
            }
            dadosGrafico.put(colaborador.getPessoa().getNome(), totalPorInd.getTotalLista());

        }
        montarGrafico();
    }

    /**
     * Joao Alcides
     * 06/02/2012
     */
    public void montarGrafico() {
        setNames(new Vector<String>());
        setData(new Hashtable<String, Integer>());
        Set<String> keySet = dadosGrafico.keySet();
        SuperControle superControle = (SuperControle) JSFUtilities.getFromSession(SuperControle.class.getSimpleName());
        if (superControle.isSuportaFlash()) {
            for (String key : keySet) {
                String nomeResumido = key;
                if (key.indexOf(" ") > 0) {
                    nomeResumido = Uteis.retirarAcentuacao(key.substring(0, key.indexOf(" ")));
                }
                if (percentagem && (indicadorComparativo.getTotalLista() > 0)) {
                    int valorPercent = (dadosGrafico.get(key) * 100) / indicadorComparativo.getTotalLista();
                    getNames().add(nomeResumido);
                    getData().put(nomeResumido, valorPercent);
                } else {
                    getNames().add(nomeResumido);
                    getData().put(nomeResumido, dadosGrafico.get(key));
                }
            }
            setarCoresGraficoFlash();
        } else {
            for (String key : keySet) {
                String nomeResumido = key;
                if (key.indexOf(" ") > 0) {
                    nomeResumido = Uteis.retirarAcentuacao(key.substring(0, key.indexOf(" ")));
                }
                this.getComparativoBarra().addValue(dadosGrafico.get(key), nomeResumido, "");
            }
        }


    }

    private void setarCoresGraficoFlash() {
        colors = new Vector<String>();
        colors.add("#6CA6CD");
        colors.add("#563857");
        colors.add("#7CFC00");
        colors.add("#D34C31");
        colors.add("#FFD700");
        colors.add("#EE9A00");
        colors.add("#DEB887");
    }

    private void montarCaixas() {
        listaCaixas.clear();
        List<TipoBoxEnum> listaTipoBox = TipoBoxEnum.getListTipoBox();
        try {
            for (TipoBoxEnum tipoBoxEnum : listaTipoBox) {
                SmartBoxVO smart = new SmartBoxVO();
                smart.setTipo(tipoBoxEnum);
                List<IndicadorSmartBoxEnum> listaIndicadoresPossiveis = IndicadorSmartBoxEnum.getListIndicadorSmartBox();
                List<TotalPorIndicadorTO> listaTotalPorIndicador = new ArrayList<TotalPorIndicadorTO>();
                for (IndicadorSmartBoxEnum indicadorSmartBoxEnum : listaIndicadoresPossiveis) {
                    /**
                     * Alguns indicadores não têm lógica de estar em algumas caixas:
                     * 1. Contrato pela metade em uma plano Mensal é um deles;
                     */
                    if (tipoBoxEnum.equals(TipoBoxEnum.MENSAL) && indicadorSmartBoxEnum.equals(IndicadorSmartBoxEnum.CONTRATOS_PELA_METADE)) {
                        continue;
                    }
                    //determinar qual a largura maxima do gráfico em função da resolução de tela do usuário!
                    double larguraTotalTela = getScreenWidthCliente();
                    if (larguraTotalTela != 0.0) {
                        larguraTotalTela = larguraTotalTela > 1024.0 ? larguraTotalTela - 250 : larguraTotalTela;
                        Double larguraMaximaGrafico = (larguraTotalTela / listaTipoBox.size()) - 35;/*margens laterais*/
                        indicadorSmartBoxEnum.setMaxWidthToGraph(larguraMaximaGrafico.intValue());
                    } else {
                        indicadorSmartBoxEnum.setMaxWidthToGraph(0);//usar largura padrão
                    }

                    TotalPorIndicadorTO totalPorInd = new TotalPorIndicadorTO();
                    totalPorInd.setIndicador(indicadorSmartBoxEnum);
                    totalPorInd.setTipoBox(tipoBoxEnum);
                    listaTotalPorIndicador.add(totalPorInd);
                }
                smart.setListaIndicadores(listaTotalPorIndicador);
                listaCaixas.add(smart);
            }

        } catch (Exception e) {
            setMensagemDetalhada("Erro em montarCaixas -> " + e.getMessage());
        }

        setListaCaixas(listaCaixas);
    }

    public void montarArvoreColaboradores() {
        try {
            setMensagemDetalhada("");
            ComboBoxEmpresaControle comboEmpresas = (ComboBoxEmpresaControle) getControlador(
                    ComboBoxEmpresaControle.class);
            comboEmpresas.registrarGatilhos(new String[]{"gatilhoAtualizarArvoreEmpresaSelecionada"});
            //
            TreeViewColaboradorControle control = (TreeViewColaboradorControle) getControlador(
                    TreeViewColaboradorControle.class.getSimpleName());
            control.carregarArvoreGrupoColaboradores();
            //
        } catch (Exception ex) {
            Logger.getLogger(SmartBoxControle.class.getName()).log(Level.SEVERE, null, ex);
            setMensagemDetalhada(ex.getMessage());
        }

    }

    private boolean esperarThreadsAcabarem() {
        for (SmartBoxThreadCalculator t : listaThreads) {
            if (t.isExecutandoCalculo()) {
                return true;
            }
        }
        return false;
    }

    private List<ColaboradorVO> obterColaboradores() {
        List<TreeViewNode> listaNodes = (List<TreeViewNode>) JSFUtilities.getManagedBeanValue(TreeViewControle.class.getSimpleName() + ".nodesMarcados");
        List<ColaboradorVO> listaColaboradores = new ArrayList();
        if (listaNodes != null && !listaNodes.isEmpty()) {
            for (TreeViewNode treeViewNode : listaNodes) {
                ColaboradorVO colab = (ColaboradorVO) treeViewNode.getObjeto();
                if (colab != null) {
                    colab.setColaboradorEscolhidoRenovacao(true);
                    listaColaboradores.add(colab);
                }
            }
        }
        return listaColaboradores;
    }

    /**
     * Preparar a lista de clientes baseado em cada indicador, podendo variar a origem dos dados,
     * a partir de Contrato ou Cliente
     * @param event
     */
    public void carregarClientesIndicador(ActionEvent event) {
        setMensagemDetalhada("");
        TotalPorIndicadorTO totalPorIndicador = (TotalPorIndicadorTO) event.getComponent().getAttributes().get("totalPorIndicador");
        ListaClientesControle listaControl = (ListaClientesControle) getControlador(ListaClientesControle.class.getSimpleName());
        listaControl.setImagem(totalPorIndicador.getTipoBox().getImagem());
        listaControl.setApresentarColunaDuracao(totalPorIndicador.getTipoBox().getDuracao() < 0);
        try {
            if (totalPorIndicador != null) {
                String sql = totalPorIndicador.getIndicador().prepararSQLSelect(
                        totalPorIndicador.getTipoBox(), getEmpresa().getCodigo(),
                        totalPorIndicador.getColaboradoresSelecionados());
                ResultSet rs = SuperFacadeJDBC.criarConsulta(sql, getFacade().getRisco().getCon());
                Object listaGenerica = null;
                int nivelDados = totalPorIndicador.getIndicador().getEntidade() == Contrato.class ? Uteis.NIVELMONTARDADOS_MINIMOS : Uteis.NIVELMONTARDADOS_DADOSBASICOS;
                /**
                 * A Classe facade a ser executada via reflection deve implementar
                 * um método 'montarDadosConsulta' com a mesma assinatura da especificação abaixo
                 * e na mesma ordem.
                 * */
                Method metodoMontarDados = totalPorIndicador.getIndicador().
                        getEntidade().getMethod("montarDadosConsulta",
                        new Class[]{
                            ResultSet.class,
                            Integer.class,
                            Connection.class});
                if (metodoMontarDados != null) {
                    Object invocador = totalPorIndicador.getIndicador().getEntidade().newInstance();
                    listaGenerica = metodoMontarDados.invoke(invocador,
                            new Object[]{rs, nivelDados, Conexao.getFromSession()});

                }
                if (totalPorIndicador.getIndicador().getEntidade() == Contrato.class) {
                    List<ContratoVO> contratos = (List<ContratoVO>) listaGenerica;
                    List<ClienteVO> clientes = new ArrayList();
                    for (ContratoVO contratoVO : contratos) {
                        ClienteVO cliente = getFacade().getCliente().consultarPorCodigoPessoa(
                                contratoVO.getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                        clientes.add(cliente);
                    }

                    listaControl.inicializar(clientes);

                } else {//tratar como uma lista de ClienteVO
                    List<ClienteVO> clientes = (List<ClienteVO>) listaGenerica;
                    listaControl.inicializar(clientes);
                }
            }
        } catch (Exception e) {
            setMensagemDetalhada(e.getMessage());
            listaControl.inicializar(new ArrayList<ClienteVO>());
        }
    }

    public void carregarDadosCaixas() {
        setMensagemDetalhada("");
        listaThreads = new ArrayList();
        totalContratosAtivos = 0;
        totalContratosAtivosMesAnterior = 0;
        try {
            EmpresaVO empresa = (EmpresaVO) JSFUtilities.getManagedBeanValue(ComboBoxEmpresaControle.class.getSimpleName() + ".empresaVO");

            for (SmartBoxVO smartbox : listaCaixas) {
                smartbox.consultarQuantidadeDestaCaixa(empresa.getCodigo());
                totalContratosAtivos += smartbox.********************************();
                totalContratosAtivosMesAnterior += smartbox.********************************MesAnterior();
            }

            for (SmartBoxVO smartbox : listaCaixas) {
                smartbox.limparValores();
                smartbox.setTotalContratosAtivosTodosTipos(totalContratosAtivos);
                smartbox.setTotalContratosAtivosTodosTiposMesAnterior(totalContratosAtivosMesAnterior);
                smartbox.setTendencia(smartbox.calcularTendencia());

                for (TotalPorIndicadorTO totalPorIndicador : smartbox.getListaIndicadores()) {
                    totalPorIndicador.setColaboradoresSelecionados(obterColaboradores());
                    totalPorIndicador.setEmpresa(empresa.getCodigo());
                    SmartBoxThreadCalculator t = new SmartBoxThreadCalculator(Conexao.getFromSession(), totalPorIndicador, empresa.getCodigo());
                    t.setName("Thread" + smartbox.getTipo() + "_" + totalPorIndicador.getIndicador());
                    listaThreads.add(t);
                    t.start();
                }
            }
            //esperar todas threads terminarem
            Thread.sleep(50);
            while (listaThreads.size() > 0 && esperarThreadsAcabarem()) {
            }

        } catch (Exception e) {
            setMensagemDetalhada(e.getMessage());
        }
    }

    public String inicializar() {
        setMensagemDetalhada("");
        if (listaThreads.isEmpty()) {
            montarArvoreColaboradores();
            montarCaixas();
            carregarDadosCaixas();
        }

        return "smartbox";
    }

    public void setComparativoBarra(DefaultCategoryDataset comparativoBarra) {
        this.comparativoBarra = comparativoBarra;
    }

    public DefaultCategoryDataset getComparativoBarra() {
        return comparativoBarra;
    }

    public void setCaixaComparativo(SmartBoxVO caixaComparativo) {
        this.caixaComparativo = caixaComparativo;
    }

    public SmartBoxVO getCaixaComparativo() {
        return caixaComparativo;
    }

    public List<SmartBoxVO> getListaCaixas() {
        return listaCaixas;
    }

    public void setListaCaixas(List<SmartBoxVO> listaCaixas) {
        this.listaCaixas = listaCaixas;
    }

    public Integer getTotalContratosAtivos() {
        return totalContratosAtivos;
    }

    public void setTotalContratosAtivos(Integer totalContratosAtivos) {
        this.totalContratosAtivos = totalContratosAtivos;
    }

    public void setDadosGrafico(Map<String, Integer> dadosGrafico) {
        this.dadosGrafico = dadosGrafico;
    }

    public Map<String, Integer> getDadosGrafico() {
        return dadosGrafico;
    }

    public void setPercentagem(Boolean percentagem) {
        this.percentagem = percentagem;
    }

    public Boolean getPercentagem() {
        return percentagem;
    }

    public void setNames(Vector<String> names) {
        this.names = names;
    }

    public Vector<String> getNames() {
        return names;
    }

    public void setData(Hashtable<String, Integer> data) {
        this.data = data;
    }

    public Hashtable<String, Integer> getData() {
        return data;
    }

    public void setColors(Vector<String> colors) {
        this.colors = colors;
    }

    public Vector<String> getColors() {
        return colors;
    }

    public void setIndicadorComparativo(TotalPorIndicadorTO indicadorComparativo) {
        this.indicadorComparativo = indicadorComparativo;
    }

    public TotalPorIndicadorTO getIndicadorComparativo() {
        return indicadorComparativo;
    }

    public String getTituloComparativo() {
        return percentagem ? "Valor em %" : "Valor em Quantidade";
    }

    public String getAbrirComparativo() {
        return obterColaboradores().isEmpty() ? "alert('Nenhum colaborador selecionado!');" : "Richfaces.showModalPanel('modalComparativo');";
    }

    public Integer getTotalContratosAtivosMesAnterior() {
        return totalContratosAtivosMesAnterior;
    }

    public void setTotalContratosAtivosMesAnterior(Integer totalContratosAtivosMesAnterior) {
        this.totalContratosAtivosMesAnterior = totalContratosAtivosMesAnterior;
    }

    @Override
    public EmpresaVO getEmpresa() {
        return (EmpresaVO) JSFUtilities.getManagedBeanValue(
                ComboBoxEmpresaControle.class.getSimpleName() + ".empresaVO");
    }
}

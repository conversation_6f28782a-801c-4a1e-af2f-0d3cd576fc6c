package controle.modulos.integracao;

import controle.arquitetura.SuperControle;
import negocio.comuns.basico.ColaboradorVO;

import javax.faces.event.ActionEvent;

/**
 * <AUTHOR>
 * @since 22/02/19
 */
public class MovideskControle extends SuperControle {

    private static final String PARAMETRO_COLABORADOR = "colaborador";

    private ColaboradorVO colaboradorVO;

    public void carregarInformacoes(final ActionEvent actionEvent) {
        this.colaboradorVO = (ColaboradorVO) actionEvent.getComponent().getAttributes().get(PARAMETRO_COLABORADOR);
    }

    public ColaboradorVO getColaboradorVO() {
        return colaboradorVO;
    }

    public void setColaboradorVO(ColaboradorVO colaboradorVO) {
        this.colaboradorVO = colaboradorVO;
    }

}

package controle.plano;

import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.contrato.servico.impl.ContratoAssinaturaDigitalServiceImpl;
import br.com.pactosolucoes.contrato.servico.intf.ContratoAssinaturaDigitalServiceInterface;
import br.com.pactosolucoes.estrutura.exportador.controle.ExportadorListaControle;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import controle.basico.clube.MensagemGenericaControle;
import negocio.comuns.basico.ColaboradorVO;

import java.sql.Connection;
import java.util.Iterator;
import java.util.Date;

import negocio.comuns.basico.ConfiguracaoSistemaVO;
import negocio.facade.jdbc.plano.PlanoTextoPadrao;
import negocio.comuns.utilitarias.*;
import negocio.comuns.plano.*;
import javax.faces.model.SelectItem;
import java.util.List;
import java.util.ArrayList;
import controle.arquitetura.SuperControle;
import controle.arquitetura.security.LoginControle;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.util.Collections;
import java.util.Enumeration;
import java.util.Hashtable;
import javax.faces.event.ActionEvent;
import javax.servlet.http.HttpServletRequest;
import negocio.comuns.arquitetura.LogVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.facade.jdbc.utilitarias.Conexao;
import org.json.JSONObject;
import org.richfaces.event.UploadEvent;
import org.richfaces.model.UploadItem;

/**
 * Classe responsável por implementar a interação entre os componentes JSF das páginas 
 * planoTextoPadraoForm.jsp planoTextoPadraoCons.jsp) com as funcionalidades da classe <code>PlanoTextoPadrao</code>.
 * Implemtação da camada controle (Backing Bean).
 * @see SuperControle
 * @see PlanoTextoPadrao
 * @see PlanoTextoPadraoVO
 */
public class PlanoTextoPadraoControle extends SuperControle {

    private PlanoTextoPadraoVO planoTextoPadraoVO;
    private List listaSelectItemResponsavelDefinicao;
    private Boolean existeTexto;
    private EmpresaVO empresa;
    private List listaModalidade;
    private List listaTurma;
    private List listaEmpresa;
    private List listaMovParcela;
    private List listaMovPagamento;
    private List listaReciboPagamento;
    private List listaComposicao;
    private List<SelectItem> listaSelectItemEmpresa = new ArrayList<SelectItem>();
    private String variavelTela;
    private String variavelTelaComposicao;
    private String variavelTelaTurma;
    private String variavelTelaMovParcela;
    private String variavelTelaMovPagamento;
    private String variavelTelaReciboPagamento;
    private Boolean habilitarGestaoArmarios;
    private String textoOriginal = "";
    private String msgAlert = "";

    public PlanoTextoPadraoControle() throws Exception {
        obterUsuarioLogado();
        inicializarConfiguracaoSistema();
        novo();
        setControleConsulta(new ControleConsulta());
        setMensagemID("");
    }
    public void inicializarConfiguracaoSistema() throws Exception{
        ConfiguracaoSistemaVO aux = getFacade().getConfiguracaoSistema().consultarPorChavePrimaria(1, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        setHabilitarGestaoArmarios(aux.getHabilitarGestaoArmarios());
    }

    public final void inicializarEmpresa() throws Exception {
        empresa = new EmpresaVO();
        if (isApresentarEmpresa()) {
            return;
        }
        setEmpresa(getEmpresaLogado());
        if (getEmpresa() == null) {
            throw new Exception("Empresa Não Encontrada. Entre Novamente no Sistema.");
        }
    }

    public void montarListaSelectItemEmpresa() throws Exception {
        List<SelectItem> objs = new ArrayList<SelectItem>();
        if (!isApresentarEmpresa()) {
            return;
        }
        List resultadoConsulta = getFacade().getEmpresa().consultarPorNome("",true, false, Uteis.NIVELMONTARDADOS_TODOS);
        Iterator i = resultadoConsulta.iterator();
        objs.add(new SelectItem(0, ""));
        while (i.hasNext()) {
            EmpresaVO obj = (EmpresaVO) i.next();
            objs.add(new SelectItem(obj.getCodigo().intValue(), obj.getNome()));
        }
        setListaSelectItemEmpresa(objs);
    }

    public void inicializarUsuarioLogado() {
        try {
            planoTextoPadraoVO.setUsuarioVO(getUsuarioLogado());
        } catch (Exception exception) {
        }
    }

    public String novo() throws Exception {
        setPlanoTextoPadraoVO(new PlanoTextoPadraoVO());
        disponibilizarTextoParaEdicaoEditorFCK();
        inicializarUsuarioLogado();
        inicializarResponsavel();
        inicializarEmpresa();
        montarListaSelectItemEmpresa();
        setExisteTexto(false);
        setMensagemID("msg_entre_dados");
        setSucesso(false);
        setErro(false);
        setListaModalidade(new ArrayList());
        setListaTurma(new ArrayList());
        setListaMovParcela(new ArrayList());
        setListaComposicao(new ArrayList());
        setListaEmpresa(new ArrayList());
        setVariavelTela("");
        setVariavelTelaComposicao("");
        setVariavelTelaTurma("");
        setVariavelTelaMovParcela("");
        if (!getUsuarioLogado().getAdministrador()) {
            EmpresaVO emp = getFacade().getEmpresa().consultarPorChavePrimaria(
                    this.getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            JSFUtilities.setManagedBeanValue("EmpresaControle.empresaVO", emp);
        }
        return "editar";
    }

    public void disponibilizarTextoParaEdicaoEditorFCK() {
        HttpServletRequest request = (HttpServletRequest) context().getExternalContext().getRequest();
        request.setAttribute("textoEditorFCK", this.getPlanoTextoPadraoVO().getTexto());
    }

    public void obterTextoEditadoEditorFCK() {
        HttpServletRequest request = (HttpServletRequest) context().getExternalContext().getRequest();
        String textoGravar = request.getParameter("EditorDefault");
        if (textoGravar != null) {
            this.getPlanoTextoPadraoVO().setTexto(textoGravar);
        }
    }

    public String editar() {
        Integer codigoConsulta = Integer.parseInt(request().getParameter("chavePrimaria"));
        try {
            if (!getUsuarioLogado().getAdministrador()) {
                EmpresaVO emp = getFacade().getEmpresa().consultarPorChavePrimaria(
                        this.getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                JSFUtilities.setManagedBeanValue("EmpresaControle.empresaVO", emp);
            }
            PlanoTextoPadraoVO obj = getFacade().getPlanoTextoPadrao().consultarPorChavePrimaria(codigoConsulta, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            getFacade().getPlanoTextoPadrao().realizarCriacaoImagemLogo(obj);
            inicializarAtributosRelacionados(obj);
            obj.setNovoObj(false);
            setPlanoTextoPadraoVO(new PlanoTextoPadraoVO());
            obj.registrarObjetoVOAntesDaAlteracao();
            textoOriginal = obj.getTexto();
            setPlanoTextoPadraoVO(obj);
            inicializarUsuarioLogado();
            setExisteTexto(false);
        } catch (Exception e) {
            setErro(true);
            setSucesso(false);
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
        return "editar";
    }

    public void inicializarAtributosRelacionados(PlanoTextoPadraoVO obj) {
        if (obj.getResponsavelDefinicao() == null) {
            obj.setResponsavelDefinicao(new UsuarioVO());
        }
    }

    public String gravar() {
        try {
            planoTextoPadraoVO.removerComentariosHtml();

            Integer codTermoResponsabilidade = -1;

            if(planoTextoPadraoVO.getTipoContrato().equals("TR")){
                codTermoResponsabilidade = getFacade().getPlanoTextoPadrao().existeTermoResponsabilidade();
                planoTextoPadraoVO.setCodigo(codTermoResponsabilidade);
            }

            if (planoTextoPadraoVO.isNovoObj().booleanValue() && codTermoResponsabilidade == -1) {
                getFacade().getPlanoTextoPadrao().incluir(planoTextoPadraoVO);
                incluirLogInclusao();
            } else {
                inicializarUsuarioLogado();
                inicializarResponsavel();
                getFacade().getPlanoTextoPadrao().alterar(planoTextoPadraoVO);
                incluirLogAlteracao();
            }
            setMensagemID("msg_dados_gravados");
            setSucesso(true);
            setErro(false);
            return "editar";
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
            return "editar";
        }
    }

    public String consultar() {
        try {
            super.consultar();
            List objs = new ArrayList();
            if (getControleConsulta().getCampoConsulta().equals("codigo")) {
                if (getControleConsulta().getValorConsulta().equals("")) {
                    getControleConsulta().setValorConsulta("0");
                }
                int valorInt = Integer.parseInt(getControleConsulta().getValorConsulta());
                objs = getFacade().getPlanoTextoPadrao().consultarPorCodigo(new Integer(valorInt), true, Uteis.NIVELMONTARDADOS_TODOS);
            }
            if (getControleConsulta().getCampoConsulta().equals("descricao")) {
                objs = getFacade().getPlanoTextoPadrao().consultarPorDescricao(getControleConsulta().getValorConsulta(), true, Uteis.NIVELMONTARDADOS_TODOS);
            }
            if (getControleConsulta().getCampoConsulta().equals("dataDefinicao")) {
                Date valorData = Uteis.getDate(getControleConsulta().getValorConsulta());
                objs = getFacade().getPlanoTextoPadrao().consultarPorDataDefinicao(Uteis.getDateTime(valorData, 0, 0, 0), Uteis.getDateTime(valorData, 23, 59, 59), true, Uteis.NIVELMONTARDADOS_TODOS);
            }
            if (getControleConsulta().getCampoConsulta().equals("situacaoColaborador")) {
                objs = getFacade().getPlanoTextoPadrao().consultarPorSituacaoColaborador(getControleConsulta().getValorConsulta(), Uteis.NIVELMONTARDADOS_TODOS);
            }
            objs = ControleConsulta.obterSubListPaginaApresentar(objs, controleConsulta);
            definirVisibilidadeLinksNavegacao(controleConsulta.getPaginaAtual(), controleConsulta.getNrTotalPaginas());
            setListaConsulta(objs);
            setMensagemID("msg_dados_consultados");
            setSucesso(true);
            setErro(false);
            return "consultar";
        } catch (Exception e) {
            setListaConsulta(new ArrayList());
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
            return "consultar";
        }
    }

    public String adicionarMarcador() {
        obterTextoEditadoEditorFCK();
        setExisteTexto(true);
        return "editar";
    }

    public String textoConcatenado(String textoAntigo, String tag) {
        String texto = textoAntigo + " " + tag;
        return texto;
    }

    public void selecionarMarcadorCliente() {
        MarcadorVO obj = (MarcadorVO) context().getExternalContext().getRequestMap().get("marcadorCliente");
        String texto = adicionarMarcador(getPlanoTextoPadraoVO().getTexto(), obj.getTag());
        getPlanoTextoPadraoVO().setTexto(texto);
    }

     public void selecionarMarcadorVenda() {
        MarcadorVO obj = (MarcadorVO) context().getExternalContext().getRequestMap().get("marcadorVenda");
        String texto = adicionarMarcador(getPlanoTextoPadraoVO().getTexto(), obj.getTag());
        getPlanoTextoPadraoVO().setTexto(texto);
    }

       public void selecionarMarcadorItensVenda() {
        MarcadorVO obj = (MarcadorVO) context().getExternalContext().getRequestMap().get("marcadorItensVenda");
        String texto = adicionarMarcador(getPlanoTextoPadraoVO().getTexto(), obj.getTag());
        getPlanoTextoPadraoVO().setTexto(texto);
    }

       public void selecionarMarcadorPacoteVenda() {
        MarcadorVO obj = (MarcadorVO) context().getExternalContext().getRequestMap().get("marcadorPacoteVenda");
        String texto = adicionarMarcador(getPlanoTextoPadraoVO().getTexto(), obj.getTag());
        getPlanoTextoPadraoVO().setTexto(texto);
    }

    public void selecionarMarcadorContrato() {
        MarcadorVO obj = (MarcadorVO) context().getExternalContext().getRequestMap().get("marcadorContrato");
        String texto = adicionarMarcador(getPlanoTextoPadraoVO().getTexto(), obj.getTag());
        getPlanoTextoPadraoVO().setTexto(texto);
    }

    public String adicionarMarcador(String texto, String marcador) {
        int parametro = texto.lastIndexOf("</p>");
        if (parametro == -1) {
            parametro = texto.lastIndexOf("</body>");
        }
        PlanoTextoPadraoTagVO p = new PlanoTextoPadraoTagVO();
        p.setTag(marcador);
        getPlanoTextoPadraoVO().getListaTagUtilizado().add(p);
        String textoAntes = texto.substring(0, parametro);
        String textoDepois = texto.substring(parametro, texto.length());
        texto = textoAntes + " " + marcador + textoDepois;
        return texto;
    }

    public String adicionarLogomarca(String texto) throws Exception {
        int parametro = texto.lastIndexOf("</p>");
        if (parametro == -1) {
            parametro = texto.lastIndexOf("</body>");
        }
        String textoAntes = texto.substring(0, parametro);
        String textoDepois = texto.substring(parametro, texto.length());
        texto = textoAntes + " <img style=\"width:200px;height:56px;border:none;\" src=\"acesso?emp\"/>" + textoDepois;

        return texto;
    }

    public void selecionarMarcadorPlano() {
        MarcadorVO obj = (MarcadorVO) context().getExternalContext().getRequestMap().get("marcadorPlano");
        String texto = adicionarMarcador(getPlanoTextoPadraoVO().getTexto(), obj.getTag());
        getPlanoTextoPadraoVO().setTexto(texto);
    }

    public void selecionarMarcadorUsuario() {
        MarcadorVO obj = (MarcadorVO) context().getExternalContext().getRequestMap().get("marcadorUsuario");
        String texto = adicionarMarcador(getPlanoTextoPadraoVO().getTexto(), obj.getTag());
        getPlanoTextoPadraoVO().setTexto(texto);
    }

    public void setarTagVariavel() {
        try {
            limparMsg();
            String marcadorFinal = "";
            Iterator i = listaModalidade.iterator();
            while (i.hasNext()) {
                MarcadorVO obj = (MarcadorVO) i.next();
                if (obj.getSelecionado().booleanValue()) {
                    marcadorFinal += obj.getTag() + ",";
                }
            }
            if(UteisValidacao.emptyString(marcadorFinal)){
                setVariavelTela("");
                throw new Exception("Nenhuma tag foi selecionada!");
            }
            int tamanho = marcadorFinal.length();
            marcadorFinal = marcadorFinal.substring(0, tamanho - 2) + "]";
            marcadorFinal = marcadorFinal.replace("],[", ",  ");
            setVariavelTela(marcadorFinal);
        } catch(Exception e){
            montarErro(e);
        }      
    }

    public void selecionarMarcadorModalidade() {
        String texto = adicionarMarcador(getPlanoTextoPadraoVO().getTexto(), getVariavelTela());
        getPlanoTextoPadraoVO().setTexto(texto);
    }

    public void selecionarMarcadorComposicao() {
        String texto = adicionarMarcador(getPlanoTextoPadraoVO().getTexto(), getVariavelTelaComposicao());
        getPlanoTextoPadraoVO().setTexto(texto);
    }

    public void setarTagVariavelComposicao() {
        try {
            limparMsg();
            String marcadorFinal = "";
            Iterator i = listaComposicao.iterator();
            while (i.hasNext()) {
                MarcadorVO obj = (MarcadorVO) i.next();
                if (obj.getSelecionado().booleanValue()) {
                    marcadorFinal += obj.getTag() + ",";
                }
            }
            if(UteisValidacao.emptyString(marcadorFinal)){
                setVariavelTelaComposicao("");
                throw new Exception("Nenhuma tag foi selecionada!");
            }
            int tamanho = marcadorFinal.length();
            marcadorFinal = marcadorFinal.substring(0, tamanho - 2) + "]";
            marcadorFinal = marcadorFinal.replace("],[", ", ");
            setVariavelTelaComposicao(marcadorFinal);
        } catch(Exception e){
            montarErro(e);
        }      
    }

    public void selecionarMarcadorTurma() {
        String texto = adicionarMarcador(getPlanoTextoPadraoVO().getTexto(), getVariavelTelaTurma());
        getPlanoTextoPadraoVO().setTexto(texto);
    }

    public void setarTagVariavelTurma() {
        try {
            limparMsg();
            String marcadorFinal = "";
            Iterator i = listaTurma.iterator();
            while (i.hasNext()) {
                MarcadorVO obj = (MarcadorVO) i.next();
                if (obj.getSelecionado().booleanValue()) {
                    marcadorFinal += obj.getTag() + ",";
                }
            }
            if(UteisValidacao.emptyString(marcadorFinal)){
                setVariavelTelaTurma("");
                throw new Exception("Nenhuma tag foi selecionada!");
            }
            int tamanho = marcadorFinal.length();
            marcadorFinal = marcadorFinal.substring(0, tamanho - 2) + "]";
            marcadorFinal = marcadorFinal.replace("],[", ", ");
            setVariavelTelaTurma(marcadorFinal);
        } catch(Exception e){
            montarErro(e);
        }      
    }

    public void selecionarMarcadorMovParcela() {
        String texto = adicionarMarcador(getPlanoTextoPadraoVO().getTexto(), getVariavelTelaMovParcela());
        getPlanoTextoPadraoVO().setTexto(texto);
    }

    public void selecionarMarcadorMovPagamento() {
        String texto = adicionarMarcador(getPlanoTextoPadraoVO().getTexto(), getVariavelTelaMovPagamento());
        getPlanoTextoPadraoVO().setTexto(texto);
    }

    public void selecionarMarcadorReciboPagamento() {
        String texto = adicionarMarcador(getPlanoTextoPadraoVO().getTexto(), getVariavelTelaReciboPagamento());
        getPlanoTextoPadraoVO().setTexto(texto);
    }

    public void setarTagVariavelMovParcela() {
        try {
            limparMsg();
            String marcadorFinal = "";
            Iterator i = listaMovParcela.iterator();
            while (i.hasNext()) {
                MarcadorVO obj = (MarcadorVO) i.next();
                if (obj.getSelecionado().booleanValue()) {
                    marcadorFinal += obj.getTag() + ",";
                }
            }
            if(UteisValidacao.emptyString(marcadorFinal)){
                setVariavelTelaMovParcela("");
                throw new Exception("Nenhuma tag foi selecionada!");
            }
            int tamanho = marcadorFinal.length();
            marcadorFinal = marcadorFinal.substring(0, tamanho - 2) + "]";
            marcadorFinal = marcadorFinal.replace("],[", ", ");
            setVariavelTelaMovParcela(marcadorFinal);
        } catch(Exception e){
            montarErro(e);
        }
    }

    public void setarTagVariavelMovPagamento() {
        try {
            limparMsg();
            String marcadorFinal = "";
            Iterator i = listaMovPagamento.iterator();
            while (i.hasNext()) {
                MarcadorVO obj = (MarcadorVO) i.next();
                if (obj.getSelecionado().booleanValue()) {
                    marcadorFinal += obj.getTag() + ",";
                }
            }
            if(UteisValidacao.emptyString(marcadorFinal)){
                setVariavelTelaMovPagamento("");
                throw new Exception("Nenhuma tag foi selecionada!");
            }
            int tamanho = marcadorFinal.length();
            marcadorFinal = marcadorFinal.substring(0, tamanho - 2) + "]";
            marcadorFinal = marcadorFinal.replace("],[", ", ");
            setVariavelTelaMovPagamento(marcadorFinal);
        } catch(Exception e){
            montarErro(e);
        }    
    }

    public void setarTagVariavelReciboPagamento() {
        try {
            limparMsg();
            String marcadorFinal = "";
            Iterator i = listaReciboPagamento.iterator();
            while (i.hasNext()) {
                MarcadorVO obj = (MarcadorVO) i.next();
                if (obj.getSelecionado().booleanValue()) {
                    marcadorFinal += obj.getTag() + ",";
                }
            }
            if(UteisValidacao.emptyString(marcadorFinal)){
                setVariavelTelaReciboPagamento("");
                throw new Exception("Nenhuma tag foi selecionada!");
            }
            int tamanho = marcadorFinal.length();
            marcadorFinal = marcadorFinal.substring(0, tamanho - 2) + "]";
            marcadorFinal = marcadorFinal.replace("],[", ", ");
            setVariavelTelaReciboPagamento(marcadorFinal);
        } catch(Exception e){
            montarErro(e);
        }      
    }

    public void selecionarMarcadorEmpresa() throws Exception {
        MarcadorVO obj = (MarcadorVO) context().getExternalContext().getRequestMap().get("marcadorEmpresa");
        if (obj.getTag().equals("Logomarca")) {
            String texto = adicionarLogomarca(getPlanoTextoPadraoVO().getTexto());
            getPlanoTextoPadraoVO().setTexto(texto);
        } else {
            String texto = adicionarMarcador(getPlanoTextoPadraoVO().getTexto(), obj.getTag());
            getPlanoTextoPadraoVO().setTexto(texto);
        }

    }

    public String excluir() {
        try {
            getFacade().getPlanoTextoPadrao().excluir(planoTextoPadraoVO);
            incluirLogExclusao();
            setPlanoTextoPadraoVO(new PlanoTextoPadraoVO());
            textoOriginal = "";
            setMensagemID("msg_dados_excluidos");
            setSucesso(true);
            setErro(false);
            return "consultar";
        } catch (Exception e) {
            if(e.getMessage().contains("ERRO: atualização ou exclusão em tabela \"planotextopadrao\" viola restrição de chave estrangeira") || e.getMessage().contains("ERROR: update or delete on table \"planotextopadrao\" violates foreign key")){
                setMensagemDetalhada("Este Modelo de Contrato não pode ser excluído, pois está sendo utilizado!");
            }else {
                setMensagemDetalhada("msg_erro", e.getMessage());
            }
            setSucesso(false);
            setErro(true);
            return "editar";
        }
    }

    public void irPaginaInicial() throws Exception {
        controleConsulta.setPaginaAtual(1);
        this.consultar();
    }

    public void irPaginaAnterior() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getPaginaAtual() - 1);
        this.consultar();
    }

    public void irPaginaPosterior() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getPaginaAtual() + 1);
        this.consultar();
    }

    public void irPaginaFinal() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getNrTotalPaginas());
        this.consultar();
    }

    public void inicializarResponsavel() {
        planoTextoPadraoVO.setResponsavelDefinicao(planoTextoPadraoVO.getUsuarioVO());
    }

    public void montarListaSelectItemResponsavelDefinicao(String prm) throws Exception {
        List resultadoConsulta = consultarColaboradorPorSituacao(prm);
        Iterator i = resultadoConsulta.iterator();
        List objs = new ArrayList();
        objs.add(new SelectItem(new Integer(0), ""));
        while (i.hasNext()) {
            ColaboradorVO obj = (ColaboradorVO) i.next();
            objs.add(new SelectItem(obj.getCodigo(), obj.getPessoa().getNome()));
        }
        setListaSelectItemResponsavelDefinicao(objs);
    }

    public void montarListaSelectItemResponsavelDefinicao() {
        try {
            montarListaSelectItemResponsavelDefinicao("");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public List consultarColaboradorPorSituacao(String situacaoPrm) throws Exception {
        List lista = getFacade().getColaborador().consultarPorSituacao(situacaoPrm, getUsuarioLogado().getColaboradorVO().getEmpresa().getCodigo(), false, Uteis.NIVELMONTARDADOS_TODOS);
        return lista;
    }

    public List getListaSelectItemSituacao() throws Exception {
        List objs = new ArrayList();
        objs.add(new SelectItem("", ""));
        Hashtable situacao = (Hashtable) Dominios.getSituacaoPlanoTextoPadrao();
        Enumeration keys = situacao.keys();
        while (keys.hasMoreElements()) {
            String value = (String) keys.nextElement();
            String label = (String) situacao.get(value);
            objs.add(new SelectItem(value, label));
        }
        SelectItemOrdemValor ordenador = new SelectItemOrdemValor();
        Collections.sort((List) objs, ordenador);
        return objs;
    }

       public List getListaSelectItemTipo() throws Exception {
        List<SelectItem> objs = new ArrayList<SelectItem>();
        objs.add(new SelectItem("", ""));
        Hashtable situacao = (Hashtable) Dominios.getTipoPlanoTextoPadrao();

        Connection con = new DAO().obterConexaoEspecifica(JSFUtilities.getFromSession("key").toString());
        ContratoAssinaturaDigitalServiceInterface cadServiceContrato = new ContratoAssinaturaDigitalServiceImpl(con);
        JSONObject obj = cadServiceContrato.verificaUtilizaTermoResponsabilidade();
        if (obj.getString("termoresponsabilidade").equals("false")){
            situacao.remove("TR");
        } else {
            situacao.put("TR", "Termo de Responsabilidade");
        }

        Enumeration keys = situacao.keys();
        while (keys.hasMoreElements()) {
            String value = (String) keys.nextElement();
            String label = (String) situacao.get(value);
            if(getHabilitarGestaoArmarios() || !value.equals("AM")) {
                objs.add(new SelectItem(value, label));
            }
        }
        SelectItemOrdemValor ordenador = new SelectItemOrdemValor();
        Collections.sort((List) objs, ordenador);
        return objs;
    }

    public List getListaSelectItemMarcadoCliente() throws Exception {
        List objs = new ArrayList();
        Hashtable cliente = (Hashtable) Dominios.getMarcadorCliente();
        MarcadorVO marcador = new MarcadorVO();
        Enumeration keys = cliente.keys();
        while (keys.hasMoreElements()) {
            String value = (String) keys.nextElement();
            String label = (String) cliente.get(value);
            marcador.setTag(value);
            marcador.setNome(label);
            objs.add(marcador);
            marcador = new MarcadorVO();
        }
        Ordenacao.ordenarLista(objs, "nome");
        return objs;
    }

    public List getListaSelectItemMarcadoVenda() throws Exception {
        List objs = new ArrayList();
        Hashtable venda = (Hashtable) Dominios.getMarcadorVenda();
        MarcadorVO marcador = new MarcadorVO();
        Enumeration keys = venda.keys();
        while (keys.hasMoreElements()) {
            String value = (String) keys.nextElement();
            String label = (String) venda.get(value);
            marcador.setTag(value);
            marcador.setNome(label);
            objs.add(marcador);
            marcador = new MarcadorVO();
        }
        Ordenacao.ordenarLista(objs, "nome");
        return objs;
    }

    public List getListaSelectItemMarcadoItensVenda() throws Exception {
        List objs = new ArrayList();
        Hashtable itensVenda = (Hashtable) Dominios.getMarcadorItensVenda();
        MarcadorVO marcador = new MarcadorVO();
        Enumeration keys = itensVenda.keys();
        while (keys.hasMoreElements()) {
            String value = (String) keys.nextElement();
            String label = (String) itensVenda.get(value);
            marcador.setTag(value);
            marcador.setNome(label);
            objs.add(marcador);
            marcador = new MarcadorVO();
        }
        Ordenacao.ordenarLista(objs, "nome");
        return objs;
    }

    public List getListaSelectItemMarcadoPacoteVenda() throws Exception {
        List objs = new ArrayList();
        Hashtable pacoteVenda = (Hashtable) Dominios.getMarcadorPacoteVenda();
        MarcadorVO marcador = new MarcadorVO();
        Enumeration keys = pacoteVenda.keys();
        while (keys.hasMoreElements()) {
            String value = (String) keys.nextElement();
            String label = (String) pacoteVenda.get(value);
            marcador.setTag(value);
            marcador.setNome(label);
            objs.add(marcador);
            marcador = new MarcadorVO();
        }
        Ordenacao.ordenarLista(objs, "nome");
        return objs;
    }

    public List getListaSelectItemMarcadoContrato() throws Exception {
        List objs = new ArrayList();
        Hashtable contrato = (Hashtable) Dominios.getMarcadorContrato();
        MarcadorVO marcador = new MarcadorVO();
        Enumeration keys = contrato.keys();
        while (keys.hasMoreElements()) {
            String value = (String) keys.nextElement();
            String label = (String) contrato.get(value);
            marcador.setTag(value);
            marcador.setNome(label);
            objs.add(marcador);
            marcador = new MarcadorVO();
        }
        Ordenacao.ordenarLista(objs, "nome");
        return objs;
    }

    public List getListaSelectItemMarcadoPlano() throws Exception {
        List objs = new ArrayList();
        Hashtable plano = (Hashtable) Dominios.getMarcadorPlano();
        MarcadorVO marcador = new MarcadorVO();
        Enumeration keys = plano.keys();
        while (keys.hasMoreElements()) {
            String value = (String) keys.nextElement();
            String label = (String) plano.get(value);
            marcador.setTag(value);
            marcador.setNome(label);
            objs.add(marcador);
            marcador = new MarcadorVO();
        }
        Ordenacao.ordenarLista(objs, "nome");
        return objs;
    }

    public List getListaSelectItemMarcadoUsuario() throws Exception {
        List objs = new ArrayList();
        Hashtable plano = (Hashtable) Dominios.getMarcadoUsuario();
        MarcadorVO marcador = new MarcadorVO();
        Enumeration keys = plano.keys();
        while (keys.hasMoreElements()) {
            String value = (String) keys.nextElement();
            String label = (String) plano.get(value);
            marcador.setTag(value);
            marcador.setNome(label);
            objs.add(marcador);
            marcador = new MarcadorVO();
        }
        Ordenacao.ordenarLista(objs, "nome");
        return objs;
    }

    public List getListaSelectItemMarcadoModalidade() throws Exception {
        Hashtable plano = (Hashtable) Dominios.getMarcadorModalidade();
        MarcadorVO marcador = new MarcadorVO();
        Enumeration keys = plano.keys();
        listaModalidade = new ArrayList();
        while (keys.hasMoreElements()) {
            String value = (String) keys.nextElement();
            String label = (String) plano.get(value);
            marcador.setTag(value);
            marcador.setNome(label);
            listaModalidade.add(marcador);
            marcador = new MarcadorVO();
        }
        Ordenacao.ordenarLista(listaModalidade, "nome");
        return listaModalidade;
    }

    public List getListaSelectItemMarcadoComposicao() throws Exception {
        Hashtable plano = (Hashtable) Dominios.getMarcadorComposicao();
        MarcadorVO marcador = new MarcadorVO();
        Enumeration keys = plano.keys();
        listaComposicao = new ArrayList();
        while (keys.hasMoreElements()) {
            String value = (String) keys.nextElement();
            String label = (String) plano.get(value);
            marcador.setTag(value);
            marcador.setNome(label);
            listaComposicao.add(marcador);
            marcador = new MarcadorVO();
        }
        Ordenacao.ordenarLista(listaComposicao, "nome");
        return listaComposicao;
    }

    public List getListaSelectItemMarcadoMovParcela() throws Exception {
        Hashtable plano = (Hashtable) Dominios.getMarcadorMovParcela();
        MarcadorVO marcador = new MarcadorVO();
        Enumeration keys = plano.keys();
        listaMovParcela = new ArrayList();
        while (keys.hasMoreElements()) {
            String value = (String) keys.nextElement();
            String label = (String) plano.get(value);
            marcador.setTag(value);
            marcador.setNome(label);
            listaMovParcela.add(marcador);
            marcador = new MarcadorVO();
        }
        Ordenacao.ordenarLista(listaMovParcela, "nome");
        return listaMovParcela;
    }

    public List getListaSelectItemMarcadorMovPagamento() throws Exception {
        Hashtable plano = (Hashtable) Dominios.getMarcadorMovPagamento();
        MarcadorVO marcador = new MarcadorVO();
        Enumeration keys = plano.keys();
        listaMovPagamento = new ArrayList();
        while (keys.hasMoreElements()) {
            String value = (String) keys.nextElement();
            String label = (String) plano.get(value);
            marcador.setTag(value);
            marcador.setNome(label);
            listaMovPagamento.add(marcador);
            marcador = new MarcadorVO();
        }
        Ordenacao.ordenarLista(listaMovPagamento, "nome");
        return listaMovPagamento;
    }

    public List getListaSelectItemMarcadorReciboPagamento() throws Exception {
        Hashtable plano = (Hashtable) Dominios.getMarcadorReciboPagamento();
        MarcadorVO marcador = new MarcadorVO();
        Enumeration keys = plano.keys();
        listaReciboPagamento = new ArrayList();
        while (keys.hasMoreElements()) {
            String value = (String) keys.nextElement();
            String label = (String) plano.get(value);
            marcador.setTag(value);
            marcador.setNome(label);
            listaReciboPagamento.add(marcador);
            marcador = new MarcadorVO();
        }
        Ordenacao.ordenarLista(listaReciboPagamento, "nome");
        return listaReciboPagamento;
    }

    public List getListaSelectItemMarcadoTurma() throws Exception {
        Hashtable plano = (Hashtable) Dominios.getMarcadorTurma();
        MarcadorVO marcador = new MarcadorVO();
        Enumeration keys = plano.keys();
        listaTurma = new ArrayList();
        while (keys.hasMoreElements()) {
            String value = (String) keys.nextElement();
            String label = (String) plano.get(value);
            marcador.setTag(value);
            marcador.setNome(label);
            listaTurma.add(marcador);
            marcador = new MarcadorVO();
        }
        Ordenacao.ordenarLista(listaTurma, "nome");
        return listaTurma;
    }

    public List getListaSelectItemMarcadoEmpresa() throws Exception {
        Hashtable plano = (Hashtable) Dominios.getMarcadoEmpresa();
        MarcadorVO marcador = new MarcadorVO();

        Enumeration keys = plano.keys();
        listaTurma = new ArrayList();
        marcador.setTag("Logomarca");
        marcador.setNome("Logomarca");
        listaTurma.add(marcador);
        marcador = new MarcadorVO();
        while (keys.hasMoreElements()) {
            String value = (String) keys.nextElement();
            String label = (String) plano.get(value);
            marcador.setTag(value);
            marcador.setNome(label);
            listaTurma.add(marcador);
            marcador = new MarcadorVO();
        }
        Ordenacao.ordenarLista(listaTurma,"nome");
        return listaTurma;
    }

    public List getTipoConsultaCombo() {
        List itens = new ArrayList();
        itens.add(new SelectItem("descricao", "Descrição"));
        itens.add(new SelectItem("codigo", "Código"));
        itens.add(new SelectItem("dataDefinicao", "Data Definição"));
        itens.add(new SelectItem("situacaoColaborador", "Responsável Definição"));
        return itens;
    }

    public String inicializarConsultar() {
        setPaginaAtualDeTodas("0/0");
        setListaConsulta(new ArrayList());
        definirVisibilidadeLinksNavegacao(0, 0);
        setMensagemID("msg_entre_prmconsulta");
        setSucesso(false);
        setErro(false);
        return "consultar";
    }

    public void realizarUpload(UploadEvent upload) {
        UploadItem item = upload.getUploadItem();
        File item1 = item.getFile();
        try {
            ByteArrayOutputStream arrayOutputStream = new ByteArrayOutputStream();
            byte buffer[] = new byte[4096];
            int bytesRead = 0;
            FileInputStream fi = new FileInputStream(item1.getAbsolutePath());
            while ((bytesRead = fi.read(buffer)) != -1) {
                arrayOutputStream.write(buffer, 0, bytesRead);
            }
            getPlanoTextoPadraoVO().setImagemLogo(arrayOutputStream.toByteArray());
            arrayOutputStream.close();
            fi.close();
            getFacade().getPlanoTextoPadrao().realizarCriacaoImagemLogo(getPlanoTextoPadraoVO());
            setMensagemID("msg_entre_prmconsulta");
        } catch (Exception e) {
            setMensagemDetalhada(e.getMessage());
        }
    }

    public void realizarAlteracaoHTML() {
        try {
            getPlanoTextoPadraoVO().setTexto(getFacade().getPlanoTextoPadrao().realizarAlteracaoHTML(getPlanoTextoPadraoVO().getTexto()));
            setMensagemID("msg_entre_prmconsulta");
        } catch (Exception e) {
            setMensagemDetalhada(e.getMessage());
        }
    }

    public void imprimirContrato() {
        try {
            if (empresa == null || empresa.getCodigo() == 0) {
                throw new Exception("Empresa não encontrada. Selecione a empresa ou entre novamente no sistema.");
            }
            getPlanoTextoPadraoVO().substituirTagsTextoEmBranco(empresa.getCodigo(), Conexao.getFromSession());
            setMensagemID("");
            setMensagemDetalhada("", "");
            setSucesso(true);
            setErro(false);
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
        }
    }

    public String getAbrirPDF() {
        if (getSucesso()) {
            return "abrirPopup('VisualizarContrato', 'ContratoEmBranco', 730, 545);";
        } else {
            return "";
        }
    }

    public boolean isApresentarEmpresa() throws Exception {
        return getUsuarioLogado().getAdministrador();
    }

    public Boolean getExisteTexto() {
        return existeTexto;
    }

    public void setExisteTexto(Boolean existeTexto) {
        this.existeTexto = existeTexto;
    }

    public List getListaSelectItemResponsavelDefinicao() {
        return (listaSelectItemResponsavelDefinicao);
    }

    public void setListaSelectItemResponsavelDefinicao(List listaSelectItemResponsavelDefinicao) {
        this.listaSelectItemResponsavelDefinicao = listaSelectItemResponsavelDefinicao;
    }

    public PlanoTextoPadraoVO getPlanoTextoPadraoVO() {
        return planoTextoPadraoVO;
    }

    public void setPlanoTextoPadraoVO(PlanoTextoPadraoVO planoTextoPadraoVO) {
        this.planoTextoPadraoVO = planoTextoPadraoVO;
        disponibilizarTextoParaEdicaoEditorFCK();
    }

    public String getVariavelTela() {
        return variavelTela;
    }

    public void setVariavelTela(String variavelTela) {
        this.variavelTela = variavelTela;
    }

    public String getVariavelTelaComposicao() {
        return variavelTelaComposicao;
    }

    public void setVariavelTelaComposicao(String variavelTelaComposicao) {
        this.variavelTelaComposicao = variavelTelaComposicao;
    }

    public String getVariavelTelaTurma() {
        return variavelTelaTurma;
    }

    public void setVariavelTelaTurma(String variavelTelaTurma) {
        this.variavelTelaTurma = variavelTelaTurma;
    }

    public String getVariavelTelaMovParcela() {
        return variavelTelaMovParcela;
    }

    public void setVariavelTelaMovParcela(String variavelTelaMovParcela) {
        this.variavelTelaMovParcela = variavelTelaMovParcela;
    }

    public void setVariavelTelaMovPagamento(String variavelTelaMovPagamento) {
        this.variavelTelaMovPagamento = variavelTelaMovPagamento;
    }

    public String getVariavelTelaMovPagamento() {
        return variavelTelaMovPagamento;
    }

    public List getListaMovParcela() {
        return listaMovParcela;
    }

    public void setListaMovParcela(List listaMovParcela) {
        this.listaMovParcela = listaMovParcela;
    }

    public List getListaComposicao() {
        return listaComposicao;
    }

    public void setListaComposicao(List listaComposicao) {
        this.listaComposicao = listaComposicao;
    }

    public List getListaEmpresa() {
        return listaEmpresa;
    }

    public void setListaEmpresa(List listaEmpresa) {
        this.listaEmpresa = listaEmpresa;
    }

    public List getListaModalidade() {
        return listaModalidade;
    }

    public void setListaModalidade(List listaModalidade) {
        this.listaModalidade = listaModalidade;
    }

    public List getListaTurma() {
        return listaTurma;
    }

    public void setListaTurma(List listaTurma) {
        this.listaTurma = listaTurma;
    }

    public void setVariavelTelaReciboPagamento(String variavelTelaReciboPagamento) {
        this.variavelTelaReciboPagamento = variavelTelaReciboPagamento;
    }

    public String getVariavelTelaReciboPagamento() {
        return variavelTelaReciboPagamento;
    }

    public List<SelectItem> getListaSelectItemEmpresa() {
        return listaSelectItemEmpresa;
    }

    public void setListaSelectItemEmpresa(List<SelectItem> listaSelectItemEmpresa) {
        this.listaSelectItemEmpresa = listaSelectItemEmpresa;
    }

    public Boolean getHabilitarGestaoArmarios() {
        return habilitarGestaoArmarios;
    }

    public void setHabilitarGestaoArmarios(Boolean habilitarGestaoArmarios) {
        this.habilitarGestaoArmarios = habilitarGestaoArmarios;
    }

    public EmpresaVO getEmpresa() {
        return empresa;
    }

    public void setEmpresa(EmpresaVO empresa) {
        this.empresa = empresa;
    }

    public void exportar(ActionEvent evt) throws Exception {
        ExportadorListaControle exportadorListaControle = (ExportadorListaControle) JSFUtilities.getFromRequest(ExportadorListaControle.class.getSimpleName());
        String paramsTableFiltrada = context().getExternalContext().getRequestParameterMap().get("paramsTableFiltrada");

        String[] split = paramsTableFiltrada.split(",");
        String campoOrdenacao = split[0].replace("[", "");
        String ordem = split[1];
        String filtro = split[2].replace("''", "");
        filtro = filtro.replace("]", "");
        List listaParaImpressao = getFacade().getPlanoTextoPadrao().consultarParaImpressao(filtro, ordem, campoOrdenacao, getEmpresaLogado() == null ? 0 : getEmpresaLogado().getCodigo());
        exportadorListaControle.exportar(evt, listaParaImpressao, filtro, null);

    }
    
   public void incluirLogInclusao() throws Exception {
        try {
            planoTextoPadraoVO.setObjetoVOAntesAlteracao(new PlanoTextoPadraoVO());
            planoTextoPadraoVO.setNovoObj(true);
            registrarLogObjetoVO(planoTextoPadraoVO, planoTextoPadraoVO.getCodigo(), "PLANOTEXTOPADRAO", 0);
            if(!planoTextoPadraoVO.getTexto().equals(((PlanoTextoPadraoVO)planoTextoPadraoVO.getObjetoVOAntesAlteracao()).getTexto())){
                try {
                    List<LogVO> logs = new ArrayList<LogVO>();
                    LogVO objBase = new LogVO();
                    objBase.setChavePrimaria(planoTextoPadraoVO.getCodigo().toString());
                    objBase.setNomeEntidade("PLANOTEXTOPADRAO");
                    objBase.setNomeEntidadeDescricao("Modelo de Contrato");
                    objBase.setOperacao("INCLUSÃO");
                    objBase.setResponsavelAlteracao(getUsuarioLogado().getNome());
                    objBase.setUserOAMD(getUsuarioLogado().getUserOamd());
                    objBase.setNomeCampo("TEXTO");
                    objBase.setValorCampoAlterado("");
                    objBase.setDataAlteracao(negocio.comuns.utilitarias.Calendario.hoje());
                    int partes = (planoTextoPadraoVO.getTexto().length() / 4500) +1;
                    int adicionadas = 0;
                    while (adicionadas < partes) {
                        LogVO logVO = (LogVO) objBase.getClone(true);
                        if(adicionadas == 0){
                            logVO.setValorCampoAlterado("CODIGO MODELO: " + planoTextoPadraoVO.getCodigo().toString() + "\nTexto: \n");
                        }
                        logVO.setValorCampoAlterado(logVO.getValorCampoAlterado()+planoTextoPadraoVO.getTexto().substring(( 4500 * adicionadas), ( 4500 * (adicionadas + 1) > planoTextoPadraoVO.getTexto().length() ? planoTextoPadraoVO.getTexto().length() : (4500 * (adicionadas + 1)))));
                        logs.add(logVO);
                        adicionadas++;
                    }
                    Collections.reverse(logs);
                    registrarLogObjetoVO(logs, 0);
                } catch (Exception e) {
                    registrarLogErroObjetoVO("PLANOTEXTOPADRAO", planoTextoPadraoVO.getCodigo(), "ERRO AO GERAR LOG DE INCLUSÃO TEXTO DE PLANOTEXTOPADRAO", this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
                    e.printStackTrace();
                }
            }
        } catch (Exception e) {
            registrarLogErroObjetoVO("PLANOTEXTOPADRAO", planoTextoPadraoVO.getCodigo(), "ERRO AO GERAR LOG DE INCLUSÃO DE PLANOTEXTOPADRAO", this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
        planoTextoPadraoVO.setNovoObj(new Boolean(false));
        planoTextoPadraoVO.registrarObjetoVOAntesDaAlteracao();
        textoOriginal = planoTextoPadraoVO.getTexto();
    }
    
    public void incluirLogExclusao() throws Exception {
        try {
            planoTextoPadraoVO.setObjetoVOAntesAlteracao(new PlanoTextoPadraoVO());
            planoTextoPadraoVO.setNovoObj(true);
            registrarLogExclusaoTodosDadosObjetoVO(planoTextoPadraoVO, planoTextoPadraoVO.getCodigo(), "PLANOTEXTOPADRAO", 0);
        } catch (Exception e) {
            registrarLogErroObjetoVO("PLANOTEXTOPADRAO", planoTextoPadraoVO.getCodigo(), "ERRO AO GERAR LOG DE EXCLUSÃO DE PLANOTEXTOPADRAO ", this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
    }

    /**
     * Inclui o log de alteração de categoria
     * @throws Exception
     */
    public void incluirLogAlteracao() throws Exception {
        try {
            registrarLogObjetoVO(planoTextoPadraoVO, planoTextoPadraoVO.getCodigo(), "PLANOTEXTOPADRAO", 0);
            if(!planoTextoPadraoVO.getTexto().equals(textoOriginal)){
                try {
                    List<LogVO> logs = new ArrayList<LogVO>();
                    LogVO objBase = new LogVO();
                    objBase.setChavePrimaria(planoTextoPadraoVO.getCodigo().toString());
                    objBase.setNomeEntidade("PLANOTEXTOPADRAO");
                    objBase.setNomeEntidadeDescricao("Modelo de Contrato");
                    objBase.setOperacao("ALTERAÇÃO");
                    objBase.setResponsavelAlteracao(getUsuarioLogado().getNome());
                    objBase.setUserOAMD(getUsuarioLogado().getUserOamd());
                    objBase.setNomeCampo("TEXTO");
                    objBase.setValorCampoAlterado("  ");
                    objBase.setValorCampoAnterior("  ");
                    objBase.setDataAlteracao(negocio.comuns.utilitarias.Calendario.hoje());
                    int partesNovo = (planoTextoPadraoVO.getTexto().length() / 4500) +1;
                    int partesAntigo = (textoOriginal.length() / 4500) +1; 
                    int adicionadas = 0;
                    int partes = partesAntigo > partesNovo ? partesAntigo : partesNovo;
                    while (adicionadas < partes) {
                        LogVO logVO = (LogVO) objBase.getClone(true);
                         if(adicionadas == 0){
                            logVO.setValorCampoAlterado("CODIGO MODELO: " + planoTextoPadraoVO.getCodigo().toString() + "\nTexto: \n");
                            logVO.setValorCampoAnterior("CODIGO MODELO: " + planoTextoPadraoVO.getCodigo().toString() + "\nTexto: \n");
                        }
                        if(adicionadas < partesNovo){
                            logVO.setValorCampoAlterado(logVO.getValorCampoAlterado()+planoTextoPadraoVO.getTexto().substring(( 4500 * adicionadas), ( 4500 * (adicionadas + 1) > planoTextoPadraoVO.getTexto().length() ? planoTextoPadraoVO.getTexto().length() : (4500 * (adicionadas + 1)))));
                        }
                        if (adicionadas < partesAntigo){
                            logVO.setValorCampoAnterior(logVO.getValorCampoAnterior()+textoOriginal.substring(( 4500 * adicionadas), ( 4500 * (adicionadas + 1) >textoOriginal.length() ? textoOriginal.length() : (4500 * (adicionadas + 1)))));
                        }
                        logs.add(logVO);
                        adicionadas++;
                    }
                    Collections.reverse(logs);
                    registrarLogObjetoVO(logs, 0);
                } catch (Exception e) {
                    registrarLogErroObjetoVO("PLANOTEXTOPADRAO", planoTextoPadraoVO.getCodigo(), "ERRO AO GERAR LOG DE ALTERACAO TEXTO DE PLANOTEXTOPADRAO", this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
                    e.printStackTrace();
                }
            }
        } catch (Exception e) {
            registrarLogErroObjetoVO("PLANOTEXTOPADRAO", planoTextoPadraoVO.getCodigo(), "ERRO AO GERAR LOG DE ALTERAÇÃO DE PLANOTEXTOPADRAO ", this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
        textoOriginal = planoTextoPadraoVO.getTexto();
        planoTextoPadraoVO.registrarObjetoVOAntesDaAlteracao();
    }
    
     public void realizarConsultaLogObjetoSelecionado() {
        LoginControle loginControle = (LoginControle) context().getExternalContext().getSessionMap().get("LoginControle");
        String nomeClasse = planoTextoPadraoVO.getClass().getSimpleName();
        nomeClasse = nomeClasse.substring(0, nomeClasse.length() - 2);
        loginControle.setNomeClasse(loginControle.getNomeEntidadeCamposLog("prt_" + nomeClasse + "_tituloForm"));
        loginControle.consultarLogObjetoSelecionado(nomeClasse.toUpperCase(), planoTextoPadraoVO.getCodigo(), 0);
    }
    public void realizarConsultaLogObjetoGeral() {
       planoTextoPadraoVO = new PlanoTextoPadraoVO();
       realizarConsultaLogObjetoSelecionado();
    }

    public void setMsgAlert(String msgAlert) {
        this.msgAlert = msgAlert;
    }

    public String getMsgAlert() {
        if (msgAlert == null) {
            return "";
        }
        return msgAlert;
    }


    public void confirmarExcluir(){
        MensagemGenericaControle control = (MensagemGenericaControle) getControlador(MensagemGenericaControle.class);
        control.setMensagemDetalhada("", "");
        setMsgAlert("Richfaces.showModalPanel('mdlMensagemGenerica');");
        control.init("Exclusão de Modelo de Contrato",
                "Deseja excluir o Modelo de Contrato?",
                this, "excluir", "", "", "", "grupoMensagem");
    }

}

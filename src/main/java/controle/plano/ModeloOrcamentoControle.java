package controle.plano;

import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.estrutura.exportador.controle.ExportadorListaControle;
import controle.arquitetura.SuperControle;
import controle.arquitetura.security.LoginControle;
import negocio.comuns.arquitetura.LogVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.plano.ComposicaoVO;
import negocio.comuns.plano.ModalidadeVO;
import negocio.comuns.plano.ModeloOrcamentoVO;
import negocio.comuns.utilitarias.*;
import negocio.facade.jdbc.utilitarias.Conexao;

import javax.faces.event.ActionEvent;
import javax.faces.model.SelectItem;
import javax.servlet.http.HttpServletRequest;
import java.util.*;

public class ModeloOrcamentoControle extends SuperControle {

    private ModeloOrcamentoVO modeloOrcamentoVO;
    private Boolean existeTexto;
    private List listaModalidade;
    private List listaPacote;
    private List listaTurma;
    private String variavelTela;
    private String variavelTelaTurma;
    private String textoOriginal = "";
    private EmpresaVO empresa;


    public ModeloOrcamentoControle() throws Exception {
        obterUsuarioLogado();
        novo();
        setControleConsulta(new ControleConsulta());
        setMensagemID("");
    }

    public String novo() throws Exception {
        setModeloOrcamentoVO(new ModeloOrcamentoVO());
        disponibilizarTextoParaEdicaoEditorFCK();
        inicializarUsuarioLogado();
        inicializarResponsavel();
        inicializarEmpresa();
        setExisteTexto(false);
        setMensagemID("msg_entre_dados");
        setSucesso(false);
        setErro(false);
        setListaModalidade(new ArrayList());
        setListaPacote(new ArrayList());
        setListaTurma(new ArrayList());
        setVariavelTela("");
        setVariavelTelaTurma("");
        if (!getUsuarioLogado().getAdministrador()) {
            EmpresaVO emp = getFacade().getEmpresa().consultarPorChavePrimaria(
                    this.getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            JSFUtilities.setManagedBeanValue("EmpresaControle.empresaVO", emp);
        }
        return "editar";
    }

    public String excluir() {
        try {
            getFacade().getModeloOrcamento().excluir(modeloOrcamentoVO);
            setModeloOrcamentoVO(new ModeloOrcamentoVO());
            setMensagemID("msg_dados_excluidos");
            setSucesso(true);
            setErro(false);
            return "editar";
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
            return "editar";
        }
    }

    public String gravar() {
        try {

            if(UteisValidacao.emptyNumber(modeloOrcamentoVO.getCodModalidade())) {
                throw new Exception("A Modalidade deve ser informado.");
            }

            if (modeloOrcamentoVO.isNovoObj().booleanValue()) {
                getFacade().getModeloOrcamento().incluir(modeloOrcamentoVO);
                incluirLogInclusao();
            } else {
                inicializarUsuarioLogado();
                inicializarResponsavel();
                getFacade().getModeloOrcamento().alterar(modeloOrcamentoVO);
                incluirLogAlteracao();
            }
            setMensagemID("msg_dados_gravados");
            setSucesso(true);
            setErro(false);
            return "editar";
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
            return "editar";
        }
    }

    public String getAbrirPDF() {
        if (getSucesso()) {
            return "abrirPopup('VisualizarContrato', 'ContratoEmBranco', 730, 545);";
        } else {
            return "";
        }
    }

    public void incluirLogInclusao() throws Exception {
        try {
            modeloOrcamentoVO.setObjetoVOAntesAlteracao(new ModeloOrcamentoVO());
            modeloOrcamentoVO.setNovoObj(true);
            registrarLogObjetoVO(modeloOrcamentoVO, modeloOrcamentoVO.getCodigo(), "MODELOORCAMENTO", 0);
            if(!modeloOrcamentoVO.getTexto().equals(((ModeloOrcamentoVO)modeloOrcamentoVO.getObjetoVOAntesAlteracao()).getTexto())){
                try {
                    List<LogVO> logs = new ArrayList<LogVO>();
                    LogVO objBase = new LogVO();
                    objBase.setChavePrimaria(modeloOrcamentoVO.getCodigo().toString());
                    objBase.setNomeEntidade("MODELOORCAMENTO");
                    objBase.setNomeEntidadeDescricao("Modelo de Orcamento");
                    objBase.setOperacao("INCLUSÃO");
                    objBase.setResponsavelAlteracao(getUsuarioLogado().getNome());
                    objBase.setUserOAMD(getUsuarioLogado().getUserOamd());
                    objBase.setNomeCampo("TEXTO");
                    objBase.setValorCampoAlterado("");
                    objBase.setDataAlteracao(negocio.comuns.utilitarias.Calendario.hoje());
                    int partes = (modeloOrcamentoVO.getTexto().length() / 4500) +1;
                    int adicionadas = 0;
                    while (adicionadas < partes) {
                        LogVO logVO = (LogVO) objBase.getClone(true);
                        if(adicionadas == 0){
                            logVO.setValorCampoAlterado("CODIGO MODELO: " + modeloOrcamentoVO.getCodigo().toString() + "\nTexto: \n");
                        }
                        logVO.setValorCampoAlterado(logVO.getValorCampoAlterado()+modeloOrcamentoVO.getTexto().substring(( 4500 * adicionadas), ( 4500 * (adicionadas + 1) > modeloOrcamentoVO.getTexto().length() ? modeloOrcamentoVO.getTexto().length() : (4500 * (adicionadas + 1)))));
                        logs.add(logVO);
                        adicionadas++;
                    }
                    Collections.reverse(logs);
                    registrarLogObjetoVO(logs, 0);
                } catch (Exception e) {
                    registrarLogErroObjetoVO("MODELOORCAMENTO", modeloOrcamentoVO.getCodigo(), "ERRO AO GERAR LOG DE INCLUSÃO TEXTO DE MODELOORCAMENTO", this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
                    e.printStackTrace();
                }
            }
        } catch (Exception e) {
            registrarLogErroObjetoVO("MODELOORCAMENTO", modeloOrcamentoVO.getCodigo(), "ERRO AO GERAR LOG DE INCLUSÃO DE MODELOORCAMENTO", this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
        modeloOrcamentoVO.setNovoObj(new Boolean(false));
        modeloOrcamentoVO.registrarObjetoVOAntesDaAlteracao();
        textoOriginal = modeloOrcamentoVO.getTexto();
    }

    public void incluirLogExclusao() throws Exception {
        try {
            modeloOrcamentoVO.setObjetoVOAntesAlteracao(new ModeloOrcamentoVO());
            modeloOrcamentoVO.setNovoObj(true);
            registrarLogExclusaoTodosDadosObjetoVO(modeloOrcamentoVO, modeloOrcamentoVO.getCodigo(), "MODELOORCAMENTO", 0);
        } catch (Exception e) {
            registrarLogErroObjetoVO("MODELOORCAMENTO", modeloOrcamentoVO.getCodigo(), "ERRO AO GERAR LOG DE EXCLUSÃO DE MODELOORCAMENTO ", this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
    }

    /**
     * Inclui o log de alteração de categoria
     * @throws Exception
     */
    public void incluirLogAlteracao() throws Exception {
        try {
            registrarLogObjetoVO(modeloOrcamentoVO, modeloOrcamentoVO.getCodigo(), "MODELOORCAMENTO", 0);
            if(!modeloOrcamentoVO.getTexto().equals(textoOriginal)){
                try {
                    List<LogVO> logs = new ArrayList<LogVO>();
                    LogVO objBase = new LogVO();
                    objBase.setChavePrimaria(modeloOrcamentoVO.getCodigo().toString());
                    objBase.setNomeEntidade("MODELOORCAMENTO");
                    objBase.setNomeEntidadeDescricao("Modelo de Orcamento");
                    objBase.setOperacao("ALTERAÇÃO");
                    objBase.setResponsavelAlteracao(getUsuarioLogado().getNome());
                    objBase.setUserOAMD(getUsuarioLogado().getUserOamd());
                    objBase.setNomeCampo("TEXTO");
                    objBase.setValorCampoAlterado("  ");
                    objBase.setValorCampoAnterior("  ");
                    objBase.setDataAlteracao(negocio.comuns.utilitarias.Calendario.hoje());
                    int partesNovo = (modeloOrcamentoVO.getTexto().length() / 4500) +1;
                    int partesAntigo = (textoOriginal.length() / 4500) +1;
                    int adicionadas = 0;
                    int partes = partesAntigo > partesNovo ? partesAntigo : partesNovo;
                    while (adicionadas < partes) {
                        LogVO logVO = (LogVO) objBase.getClone(true);
                        if(adicionadas == 0){
                            logVO.setValorCampoAlterado("CODIGO MODELO: " + modeloOrcamentoVO.getCodigo().toString() + "\nTexto: \n");
                            logVO.setValorCampoAnterior("CODIGO MODELO: " + modeloOrcamentoVO.getCodigo().toString() + "\nTexto: \n");
                        }
                        if(adicionadas < partesNovo){
                            logVO.setValorCampoAlterado(logVO.getValorCampoAlterado()+modeloOrcamentoVO.getTexto().substring(( 4500 * adicionadas), ( 4500 * (adicionadas + 1) > modeloOrcamentoVO.getTexto().length() ? modeloOrcamentoVO.getTexto().length() : (4500 * (adicionadas + 1)))));
                        }
                        if (adicionadas < partesAntigo){
                            logVO.setValorCampoAnterior(logVO.getValorCampoAnterior()+textoOriginal.substring(( 4500 * adicionadas), ( 4500 * (adicionadas + 1) >textoOriginal.length() ? textoOriginal.length() : (4500 * (adicionadas + 1)))));
                        }
                        logs.add(logVO);
                        adicionadas++;
                    }
                    Collections.reverse(logs);
                    registrarLogObjetoVO(logs, 0);
                } catch (Exception e) {
                    registrarLogErroObjetoVO("MODELOORCAMENTO", modeloOrcamentoVO.getCodigo(), "ERRO AO GERAR LOG DE ALTERACAO TEXTO DE MODELOORCAMENTO", this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
                    e.printStackTrace();
                }
            }
        } catch (Exception e) {
            registrarLogErroObjetoVO("MODELOORCAMENTO", modeloOrcamentoVO.getCodigo(), "ERRO AO GERAR LOG DE ALTERAÇÃO DE MODELOORCAMENTO ", this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
        textoOriginal = modeloOrcamentoVO.getTexto();
        modeloOrcamentoVO.registrarObjetoVOAntesDaAlteracao();
    }

    public void disponibilizarTextoParaEdicaoEditorFCK() {
        HttpServletRequest request = (HttpServletRequest) context().getExternalContext().getRequest();
        request.setAttribute("textoEditorFCK", this.getModeloOrcamentoVO().getTexto());
    }

    public void inicializarUsuarioLogado() {
        try {
            modeloOrcamentoVO.setUsuarioVO(getUsuarioLogado());
        } catch (Exception exception) {
        }
    }

    public void inicializarResponsavel() {
        modeloOrcamentoVO.setResponsavelDefinicao(modeloOrcamentoVO.getUsuarioVO());
    }

    public String inicializarConsultar() {
        setPaginaAtualDeTodas("0/0");
        setListaConsulta(new ArrayList());
        definirVisibilidadeLinksNavegacao(0, 0);
        setMensagemID("msg_entre_prmconsulta");
        setSucesso(false);
        setErro(false);
        return "consultar";
    }

    public void realizarConsultaLogObjetoSelecionado() {
        LoginControle loginControle = (LoginControle) context().getExternalContext().getSessionMap().get("LoginControle");
        String nomeClasse = modeloOrcamentoVO.getClass().getSimpleName();
        nomeClasse = nomeClasse.substring(0, nomeClasse.length() - 2);
        loginControle.setNomeClasse(loginControle.getNomeEntidadeCamposLog("prt_" + nomeClasse + "_tituloForm"));
        loginControle.consultarLogObjetoSelecionado(nomeClasse.toUpperCase(), modeloOrcamentoVO.getCodigo(), 0);
    }
    public void realizarConsultaLogObjetoGeral() {
        modeloOrcamentoVO = new ModeloOrcamentoVO();
        realizarConsultaLogObjetoSelecionado();
    }

    public void inicializarAtributosRelacionados(ModeloOrcamentoVO obj) {
        if (obj.getResponsavelDefinicao() == null) {
            obj.setResponsavelDefinicao(new UsuarioVO());
        }
    }

    public boolean isApresentarEmpresa() throws Exception {
        return getUsuarioLogado().getAdministrador();
    }

    public final void inicializarEmpresa() throws Exception {
        empresa = new EmpresaVO();
        if (isApresentarEmpresa()) {
            return;
        }
        setEmpresa(getEmpresaLogado());
        if (getEmpresa() == null) {
            throw new Exception("Empresa Não Encontrada. Entre Novamente no Sistema.");
        }
    }

    public String editar() {
        Integer codigoConsulta = Integer.parseInt(request().getParameter("chavePrimaria"));
        try {
            if (!getUsuarioLogado().getAdministrador()) {
                EmpresaVO emp = getFacade().getEmpresa().consultarPorChavePrimaria(
                        this.getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                JSFUtilities.setManagedBeanValue("EmpresaControle.empresaVO", emp);
            }
            ModeloOrcamentoVO obj = getFacade().getModeloOrcamento().consultarPorChavePrimaria(codigoConsulta, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            inicializarAtributosRelacionados(obj);
            obj.setNovoObj(false);
            setModeloOrcamentoVO(new ModeloOrcamentoVO());
            obj.registrarObjetoVOAntesDaAlteracao();
            textoOriginal = obj.getTexto();
            setModeloOrcamentoVO(obj);
            inicializarUsuarioLogado();
            setExisteTexto(false);
        } catch (Exception e) {
            setErro(true);
            setSucesso(false);
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
        return "editar";
    }

    public void exportar(ActionEvent evt) throws Exception {
        ExportadorListaControle exportadorListaControle = (ExportadorListaControle) JSFUtilities.getFromRequest(ExportadorListaControle.class.getSimpleName());
        String paramsTableFiltrada = context().getExternalContext().getRequestParameterMap().get("paramsTableFiltrada");

        String[] split = paramsTableFiltrada.split(",");
        String campoOrdenacao = split[0].replace("[", "");
        String ordem = split[1];
        String filtro = split[2].replace("''", "");
        filtro = filtro.replace("]", "");
        List listaParaImpressao = getFacade().getModeloOrcamento().consultarParaImpressao(filtro, ordem, campoOrdenacao, getEmpresaLogado() == null ? 0 : getEmpresaLogado().getCodigo());
        exportadorListaControle.exportar(evt, listaParaImpressao, filtro, null);

    }

    public void imprimirOrcamento() {
        try {
            if (getEmpresa() == null || getEmpresa().getCodigo() == 0) {
                throw new Exception("Empresa não encontrada. Selecione a empresa ou entre novamente no sistema.");
            }
            getModeloOrcamentoVO().substituirTagsTextoEmBranco(null, getEmpresa(), Conexao.getFromSession(), false);
            setMensagemID("");
            setMensagemDetalhada("", "");
            setSucesso(true);
            setErro(false);
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
        }
    }

    public List getListaSelectItemSituacao() throws Exception {
        List objs = new ArrayList();
        objs.add(new SelectItem("", ""));
        Hashtable situacao = (Hashtable) Dominios.getSituacaoPlanoTextoPadrao();
        Enumeration keys = situacao.keys();
        while (keys.hasMoreElements()) {
            String value = (String) keys.nextElement();
            String label = (String) situacao.get(value);
            objs.add(new SelectItem(value, label));
        }
        SelectItemOrdemValor ordenador = new SelectItemOrdemValor();
        Collections.sort((List) objs, ordenador);
        return objs;
    }

    public List getListaSelectItemModalidade() throws Exception {
        List objs = new ArrayList();
        objs.add(new SelectItem(0, ""));
        List<ModalidadeVO> listModalidades = getFacade().getModalidade().consultarPorUtilizaTurma(true, false, Uteis.NIVELMONTARDADOS_TODOS);
        for (ModalidadeVO mod : listModalidades){
            objs.add(new SelectItem(mod.getCodigo(), mod.getNome()));
        }
        SelectItemOrdemValor ordenador = new SelectItemOrdemValor();
        Collections.sort((List) objs, ordenador);
        return objs;
    }

    public List getListaSelectItemPacote() throws Exception {
        List objs = new ArrayList();
        objs.add(new SelectItem(0, ""));
        List<ComposicaoVO> listPacotes = getFacade().getComposicao().consultarTodos(getEmpresaLogado().getCodigo().intValue(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        for (ComposicaoVO pacote : listPacotes){
            objs.add(new SelectItem(pacote.getCodigo(), pacote.getDescricao()));
        }
        SelectItemOrdemValor ordenador = new SelectItemOrdemValor();
        Collections.sort((List) objs, ordenador);
        return objs;
    }

    public ModeloOrcamentoVO getModeloOrcamentoVO() {
        return modeloOrcamentoVO;
    }

    public void setModeloOrcamentoVO(ModeloOrcamentoVO modeloOrcamentoVO) {
        this.modeloOrcamentoVO = modeloOrcamentoVO;
    }

    public Boolean getExisteTexto() {
        return existeTexto;
    }

    public void setExisteTexto(Boolean existeTexto) {
        this.existeTexto = existeTexto;
    }

    public List getListaModalidade() {
        return listaModalidade;
    }

    public void setListaModalidade(List listaModalidade) {
        this.listaModalidade = listaModalidade;
    }

    public List getListaTurma() {
        return listaTurma;
    }

    public void setListaTurma(List listaTurma) {
        this.listaTurma = listaTurma;
    }

    public String getVariavelTela() {
        return variavelTela;
    }

    public void setVariavelTela(String variavelTela) {
        this.variavelTela = variavelTela;
    }

    public String getVariavelTelaTurma() {
        return variavelTelaTurma;
    }

    public void setVariavelTelaTurma(String variavelTelaTurma) {
        this.variavelTelaTurma = variavelTelaTurma;
    }

    public String getTextoOriginal() {
        return textoOriginal;
    }

    public void setTextoOriginal(String textoOriginal) {
        this.textoOriginal = textoOriginal;
    }

    public List getListaPacote() {
        return listaPacote;
    }

    public void setListaPacote(List listaPacote) {
        this.listaPacote = listaPacote;
    }
}

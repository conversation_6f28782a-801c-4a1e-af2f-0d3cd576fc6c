package controle.plano;
import java.util.Iterator;
import negocio.facade.jdbc.plano.PlanoModalidadeVezesSemana;
import negocio.comuns.utilitarias.*;
import negocio.comuns.plano.*;
import javax.faces.model.SelectItem;
import java.util.List;
import java.util.ArrayList;
import controle.arquitetura.SuperControle;

/**
 * Classe responsável por implementar a interação entre os componentes JSF das páginas 
 * planoModalidadeVezesSemanaForm.jsp planoModalidadeVezesSemanaCons.jsp) com as funcionalidades da classe <code>PlanoModalidadeVezesSemana</code>.
 * Implemtação da camada controle (Backing Bean).
 * @see SuperControle
 * @see PlanoModalidadeVezesSemana
 * @see PlanoModalidadeVezesSemanaVO
*/
public class PlanoModalidadeVezesSemanaControle extends SuperControle {
    private PlanoModalidadeVezesSemanaVO planoModalidadeVezesSemanaVO;
    protected List listaSelectItemVezesSemana;
    /**
    * Interface <code>PlanoModalidadeVezesSemanaInterfaceFacade</code> responsável pela interconexão da camada de controle com a camada de negócio.
    * Criando uma independência da camada de controle com relação a tenologia de persistência dos dados (DesignPatter: Façade).
    */

    public PlanoModalidadeVezesSemanaControle() throws Exception {
        obterUsuarioLogado();
        inicializarFacades();
        setControleConsulta(new ControleConsulta());
        setMensagemID("msg_entre_prmconsulta");
    }

    /**
    * Rotina responsável por disponibilizar um novo objeto da classe <code>PlanoModalidadeVezesSemana</code>
    * para edição pelo usuário da aplicação.
    */
    public String novo() {
        setPlanoModalidadeVezesSemanaVO(new PlanoModalidadeVezesSemanaVO());
        inicializarListasSelectItemTodosComboBox();
        setMensagemID("msg_entre_dados");
        return "editar";
    }

    /**
    * Rotina responsável por disponibilizar os dados de um objeto da classe <code>PlanoModalidadeVezesSemana</code> para alteração.
    * O objeto desta classe é disponibilizado na session da página (request) para que o JSP correspondente possa disponibilizá-lo para edição.
    */
    public String editar() {
        PlanoModalidadeVezesSemanaVO obj = (PlanoModalidadeVezesSemanaVO)context().getExternalContext().getRequestMap().get("planoModalidadeVezesSemana");
        inicializarAtributosRelacionados(obj);
        obj.setNovoObj(new Boolean(false));
        setPlanoModalidadeVezesSemanaVO(obj);
        inicializarListasSelectItemTodosComboBox();
        setMensagemID("msg_dados_editar");
        return "editar";
    }

    /**
    * Método responsável inicializar objetos relacionados a classe <code>PlanoModalidadeVezesSemanaVO</code>.
    * Esta inicialização é necessária por exigência da tecnologia JSF, que não trabalha com valores nulos para estes atributos.
    */
    public void inicializarAtributosRelacionados(PlanoModalidadeVezesSemanaVO obj) {
//        if (obj.getVezesSemana() == null) {
//            obj.setVezesSemana(new VezesSemanaVO());
//        }
    }

    /**
    * Rotina responsável por gravar no BD os dados editados de um novo objeto da classe <code>PlanoModalidadeVezesSemana</code>.
    * Caso o objeto seja novo (ainda não gravado no BD) é acionado a operação <code>incluir()</code>. Caso contrário é acionado o <code>alterar()</code>.
    * Se houver alguma inconsistência o objeto não é gravado, sendo re-apresentado para o usuário juntamente com uma mensagem de erro.
    */
    public String gravar() {
        try {
            if (planoModalidadeVezesSemanaVO.isNovoObj().booleanValue()) {
                getFacade().getPlanoModalidadeVezesSemana().incluir(planoModalidadeVezesSemanaVO);
            } else {
                getFacade().getPlanoModalidadeVezesSemana().alterar(planoModalidadeVezesSemanaVO);
            }
            setMensagemID("msg_dados_gravados");
            return "editar";
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            return "editar";
        }
    }

    /**
    * Rotina responsavel por executar as consultas disponiveis no JSP PlanoModalidadeVezesSemanaCons.jsp.
    * Define o tipo de consulta a ser executada, por meio de ComboBox denominado campoConsulta, disponivel neste mesmo JSP.
    * Como resultado, disponibiliza um List com os objetos selecionados na sessao da pagina.
    */
    public String consultar() {
        try {
            super.consultar();
            List objs = new ArrayList();
            if (getControleConsulta().getCampoConsulta().equals("codigo")) {
                if (getControleConsulta().getValorConsulta().equals("")) {
                    getControleConsulta().setValorConsulta("0");
                }
                int valorInt = Integer.parseInt(getControleConsulta().getValorConsulta());
                objs = getFacade().getPlanoModalidadeVezesSemana().consultarPorCodigo(new Integer(valorInt), true, Uteis.NIVELMONTARDADOS_TODOS);
            }
            if (getControleConsulta().getCampoConsulta().equals("nrVezesVezesSemana")) {
                if (getControleConsulta().getValorConsulta().equals("")) {
                    getControleConsulta().setValorConsulta("0");
                }
                int valorInt = Integer.parseInt(getControleConsulta().getValorConsulta());
                objs = getFacade().getPlanoModalidadeVezesSemana().consultarPorNrVezesVezesSemana(new Integer(valorInt), Uteis.NIVELMONTARDADOS_TODOS);
            }
            if (getControleConsulta().getCampoConsulta().equals("planoModalidade")) {
                if (getControleConsulta().getValorConsulta().equals("")) {
                    getControleConsulta().setValorConsulta("0");
                }
                int valorInt = Integer.parseInt(getControleConsulta().getValorConsulta());
                objs = getFacade().getPlanoModalidadeVezesSemana().consultarPorPlanoModalidade(new Integer(valorInt), true, Uteis.NIVELMONTARDADOS_TODOS);
            }
            objs = ControleConsulta.obterSubListPaginaApresentar(objs, controleConsulta);
            definirVisibilidadeLinksNavegacao(controleConsulta.getPaginaAtual(), controleConsulta.getNrTotalPaginas());
            setListaConsulta(objs);
            setMensagemID("msg_dados_consultados");
            return "consultar";
        } catch (Exception e) {
            setListaConsulta(new ArrayList());
            setMensagemDetalhada("msg_erro", e.getMessage());
            return "consultar";
        }
    }

    /**
     * Operação responsável por processar a exclusão um objeto da classe <code>PlanoModalidadeVezesSemanaVO</code>
     * Após a exclusão ela automaticamente aciona a rotina para uma nova inclusão.
     */
    public String excluir() {
        try {
            getFacade().getPlanoModalidadeVezesSemana().excluir(planoModalidadeVezesSemanaVO);
            setPlanoModalidadeVezesSemanaVO( new PlanoModalidadeVezesSemanaVO());
            setMensagemID("msg_dados_excluidos");
            return "editar";
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            return "editar";
        }
    }

    public void irPaginaInicial() throws Exception{
        controleConsulta.setPaginaAtual(1);
        this.consultar();
    }

    public void irPaginaAnterior() throws Exception{
        controleConsulta.setPaginaAtual(controleConsulta.getPaginaAtual() - 1);
        this.consultar();
    }

    public void irPaginaPosterior() throws Exception{
        controleConsulta.setPaginaAtual(controleConsulta.getPaginaAtual() + 1);
        this.consultar();
    }

    public void irPaginaFinal() throws Exception{
        controleConsulta.setPaginaAtual(controleConsulta.getNrTotalPaginas());
        this.consultar();
    }

    /**
     * Método responsável por gerar uma lista de objetos do tipo <code>SelectItem</code> para preencher
     * o comboBox relativo ao atributo <code>VezesSemana</code>.
    */
    public void montarListaSelectItemVezesSemana(Integer prm) throws Exception {
        List resultadoConsulta = consultarVezesSemanaPorNrVezes(prm);
        Iterator i = resultadoConsulta.iterator();
        List objs = new ArrayList();
        objs.add(new SelectItem(new Integer(0), ""));
        while (i.hasNext()) {
            VezesSemanaVO obj = (VezesSemanaVO)i.next();
            objs.add(new SelectItem(obj.getCodigo(), obj.getNrVezes().toString()));
        }
        setListaSelectItemVezesSemana(objs);
    }

    /**
     * Método responsável por atualizar o ComboBox relativo ao atributo <code>VezesSemana</code>.
     * Buscando todos os objetos correspondentes a entidade <code>VezesSemana</code>. Esta rotina não recebe parâmetros para filtragem de dados, isto é
     * importante para a inicialização dos dados da tela para o acionamento por meio requisições Ajax.
    */
    public void montarListaSelectItemVezesSemana() {
        try {
            montarListaSelectItemVezesSemana(new Integer(0));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * Método responsável por consultar dados da entidade <code><code> e montar o atributo <code>nrVezes</code>
     * Este atributo é uma lista (<code>List</code>) utilizada para definir os valores a serem apresentados no ComboBox correspondente
    */
    public List consultarVezesSemanaPorNrVezes(Integer nrVezesPrm) throws Exception {
        List lista = getFacade().getVezesSemana().consultarPorNrVezes(nrVezesPrm, false, Uteis.NIVELMONTARDADOS_TODOS);
        return lista;
    }

    /**
     * Método responsável por inicializar a lista de valores (<code>SelectItem</code>) para todos os ComboBox's.
    */
    public void inicializarListasSelectItemTodosComboBox() {
        montarListaSelectItemVezesSemana();
    }

    /**
    * Rotina responsável por preencher a combo de consulta da telas.
    */
    public List getTipoConsultaCombo() {
        List itens = new ArrayList();
        itens.add(new SelectItem("codigo", "Código"));
        itens.add(new SelectItem("nrVezesVezesSemana", "Vezes na Semana"));
        itens.add(new SelectItem("planoModalidade", "Plano Modalidade"));
        return itens;
    }

    /**
    * Rotina responsável por organizar a paginação entre as páginas resultantes de uma consulta.
    */
    public String inicializarConsultar() {
        setPaginaAtualDeTodas("0/0");
        setListaConsulta(new ArrayList());
        definirVisibilidadeLinksNavegacao(0, 0);
        setMensagemID("msg_entre_prmconsulta");
        return "consultar";
    }

    /**
    * Operação que inicializa as Interfaces Façades com os respectivos objetos de
    * persistência dos dados no banco de dados. 
    */
    protected boolean inicializarFacades() {
        try {
            super.inicializarFacades();
            return true;
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro_conectarBD", e.getMessage());
            return false;
        }
    }

    public List getListaSelectItemVezesSemana() {
        return (listaSelectItemVezesSemana);
    }
     
    public void setListaSelectItemVezesSemana( List listaSelectItemVezesSemana ) {
        this.listaSelectItemVezesSemana = listaSelectItemVezesSemana;
    }

    public PlanoModalidadeVezesSemanaVO getPlanoModalidadeVezesSemanaVO() {
        return planoModalidadeVezesSemanaVO;
    }
     
    public void setPlanoModalidadeVezesSemanaVO(PlanoModalidadeVezesSemanaVO planoModalidadeVezesSemanaVO) {
        this.planoModalidadeVezesSemanaVO = planoModalidadeVezesSemanaVO;
    }
}
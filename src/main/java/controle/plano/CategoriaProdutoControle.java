package controle.plano;

import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.estrutura.exportador.controle.ExportadorListaControle;
import controle.arquitetura.SuperControle;
import controle.arquitetura.security.LoginControle;
import controle.basico.clube.MensagemGenericaControle;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.contrato.ComissaoProdutoConfiguracaoVO;
import negocio.comuns.financeiro.FormaPagamentoVO;
import negocio.comuns.plano.AmbienteVO;
import negocio.comuns.plano.CategoriaProdutoVO;
import negocio.comuns.utilitarias.ControleConsulta;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.plano.CategoriaProduto;

import javax.faces.event.ActionEvent;
import javax.faces.model.SelectItem;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

/**
 * Classe responsável por implementar a interação entre os componentes JSF das páginas 
 * categoriaProdutoForm.jsp categoriaProdutoCons.jsp) com as funcionalidades da classe <code>CategoriaProduto</code>.
 * Implemtação da camada controle (Backing Bean).
 * @see SuperControle
 * @see CategoriaProduto
 * @see CategoriaProdutoVO
 */
public class CategoriaProdutoControle extends SuperControle {

    private CategoriaProdutoVO categoriaProdutoVO;
    private ComissaoProdutoConfiguracaoVO comissaoVO = new ComissaoProdutoConfiguracaoVO();
    private boolean apresentarEmpresaComissao = false;
    private String msgAlert;
    protected List listaSelectItemFormaPagamento = new ArrayList();

    /**
     * Interface <code>CategoriaProdutoInterfaceFacade</code> responsável pela interconexão da camada de controle com a camada de negócio.
     * Criando uma independência da camada de controle com relação a tenologia de persistência dos dados (DesignPatter: Façade).
     */
    public CategoriaProdutoControle() throws Exception {
        obterUsuarioLogado();
        inicializarFacades();
        montarListaEmpresas();
        inicializarComissao();
        setControleConsulta(new ControleConsulta());
        montarListaSelectItemFormaPagamento("");
        setMensagemID("");
    }

    /**
     * Rotina responsável por disponibilizar um novo objeto da classe <code>CategoriaProduto</code>
     * para edição pelo usuário da aplicação.
     */
    public String novo() throws Exception {
        setCategoriaProdutoVO(new CategoriaProdutoVO());
        montarListaSelectItemFormaPagamento("");
        setSucesso(false);
        setErro(false);
        return "editar";
    }

    /**
     * Rotina responsável por disponibilizar os dados de um objeto da classe <code>CategoriaProduto</code> para alteração.
     * O objeto desta classe é disponibilizado na session da página (request) para que o JSP correspondente possa disponibilizá-lo para edição.
     */
    public String editar() {
        Integer codigoConsulta = Integer.parseInt(request().getParameter("chavePrimaria"));
        try {
            CategoriaProdutoVO obj = getFacade().getCategoriaProduto().consultarPorChavePrimaria(codigoConsulta, Uteis.NIVELMONTARDADOS_TODOS);
            obj.setNovoObj(false);
            obj.registrarObjetoVOAntesDaAlteracao();
            montarListaSelectItemFormaPagamento("");
            setCategoriaProdutoVO(obj);
        } catch (Exception e) {
            setErro(true);
            setSucesso(false);
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
        return "editar";
    }

    /**
     * Rotina responsável por gravar no BD os dados editados de um novo objeto da classe <code>CategoriaProduto</code>.
     * Caso o objeto seja novo (ainda não gravado no BD) é acionado a operação <code>incluir()</code>. Caso contrário é acionado o <code>alterar()</code>.
     * Se houver alguma inconsistência o objeto não é gravado, sendo re-apresentado para o usuário juntamente com uma mensagem de erro.
     */
    public String gravar(boolean centralEventos) {
        try {
            if (centralEventos) {
                this.verificarAutorizacao();
                if (categoriaProdutoVO.isNovoObj().booleanValue()) {
                    getFacade().getCategoriaProduto().incluir(categoriaProdutoVO, true);

                    //LOG - INICIO
                    try {
                        categoriaProdutoVO.setObjetoVOAntesAlteracao(new AmbienteVO());
                        categoriaProdutoVO.setNovoObj(true);
                        registrarLogObjetoVO(categoriaProdutoVO, categoriaProdutoVO.getCodigo(), "CATEGORIA PRODUTO", 0);
                    } catch (Exception e) {
                        registrarLogErroObjetoVO("CATEGORIA PRODUTO", categoriaProdutoVO.getCodigo(), "ERRO AO GERAR LOG DE INCLUSÃO DE CATEGORIA PRODUTO", this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
                        e.printStackTrace();
                    }
                     categoriaProdutoVO.setNovoObj(false);
                    //LOG - FIM
                } else {
                    getFacade().getCategoriaProduto().alterar(categoriaProdutoVO, true);

                    //LOG - INICIO
                    try {
                        registrarLogObjetoVO(categoriaProdutoVO, categoriaProdutoVO.getCodigo(), "CATEGORIA PRODUTO", 0);
                    } catch (Exception e) {
                        registrarLogErroObjetoVO("CATEGORIAPRODUTO", categoriaProdutoVO.getCodigo(), "ERRO AO GERAR LOG DE ALTERAÇÃO DE CATEGORIA PRODUTO", this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
                        e.printStackTrace();
                    }
                    //LOG - FIM
                }
            } else {
                if (categoriaProdutoVO.isNovoObj().booleanValue()) {
                    getFacade().getCategoriaProduto().incluir(categoriaProdutoVO);
                    incluirLogInclusao();
                } else {
                    getFacade().getCategoriaProduto().alterar(categoriaProdutoVO);
                    incluirLogAlteracao();
                }
            }
            setMensagemID("msg_dados_gravados");
            setSucesso(true);
            setErro(false);
            return "editar";
        } catch (Exception e) {
            setSucesso(false);
            setErro(true);
            setMensagemDetalhada("msg_erro", e.getMessage());
            return "editar";
        }
    }

    /**
     * Responsável por gravar no BD os dados editados de um novo objeto da classe CategoriaProduto
     * usado pelo ZW
     * <AUTHOR>
     * 22/03/2011
     * @return
     */
    public String gravar() {
        return this.gravar(false);
    }

    /**
     * Responsável por gravar no BD os dados editados de um novo objeto da classe CategoriaProduto
     * usado pelo CE
     * <AUTHOR>
     * 22/03/2011
     * @return
     */
    public String gravarCE() {
        return this.gravar(true);
    }

    /**
     * Rotina responsavel por executar as consultas disponiveis no JSP CategoriaProdutoCons.jsp.
     * Define o tipo de consulta a ser executada, por meio de ComboBox denominado campoConsulta, disponivel neste mesmo JSP.
     * Como resultado, disponibiliza um List com os objetos selecionados na sessao da pagina.
     */
    public String consultar() {
        try {
            super.consultar();
            List objs = new ArrayList();
            if (getControleConsulta().getCampoConsulta().equals("codigo")) {
                if (getControleConsulta().getValorConsulta().equals("")) {
                    getControleConsulta().setValorConsulta("0");
                }
                int valorInt = Integer.parseInt(getControleConsulta().getValorConsulta());
                objs = getFacade().getCategoriaProduto().consultarPorCodigo(new Integer(valorInt), true, Uteis.NIVELMONTARDADOS_TODOS);
            }
            if (getControleConsulta().getCampoConsulta().equals("descricao")) {
                objs = getFacade().getCategoriaProduto().consultarPorDescricao(getControleConsulta().getValorConsulta(), true, Uteis.NIVELMONTARDADOS_TODOS);
            }
            objs = ControleConsulta.obterSubListPaginaApresentar(objs, controleConsulta);
            definirVisibilidadeLinksNavegacao(controleConsulta.getPaginaAtual(), controleConsulta.getNrTotalPaginas());
            setListaConsulta(objs);
            setSucesso(true);
            setErro(false);
            return "consultar";
        } catch (Exception e) {
            setSucesso(false);
            setErro(true);
            setListaConsulta(new ArrayList());
            setMensagemDetalhada("msg_erro", e.getMessage());
            return "consultar";
        }
    }

    /**
     * Operação responsável por processar a exclusão um objeto da classe <code>CategoriaProdutoVO</code>
     * Após a exclusão ela automaticamente aciona a rotina para uma nova inclusão.
     */
    public String excluir(boolean centralEventos) {
        try {
            if (centralEventos) {
                this.verificarAutorizacao();
                getFacade().getCategoriaProduto().excluir(categoriaProdutoVO, true);

                //LOG - INICIO
                try {
                    registrarLogExclusaoObjetoVO(categoriaProdutoVO, categoriaProdutoVO.getCodigo().intValue(), "CATEGORIA PRODUTO", 0);
                } catch (Exception e) {
                    registrarLogErroObjetoVO("CATEGORIA PRODUTO", categoriaProdutoVO.getCodigo(), "ERRO AO GERAR LOG DE EXCLUSÃO DE CATEGORIA PRODUTO", this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
                    e.printStackTrace();
                }
                //LOG - FIM
            } else {
                getFacade().getCategoriaProduto().excluir(categoriaProdutoVO);
                incluirLogExclusao();
            }
            setCategoriaProdutoVO(new CategoriaProdutoVO());
            setSucesso(true);
            setErro(false);
            setMensagemID("msg_dados_excluidos");
            return "consultar";
        } catch (Exception e) {
            setSucesso(false);
            setErro(true);
            if(e.getMessage().contains("ERRO: atualização ou exclusão em tabela \"categoriaproduto\" viola restrição de chave estrangeira") || e.getMessage().contains("ERROR: update or delete on table \"categoriaproduto\" violates foreign key")){
                setMensagemDetalhada("Esta categoria de produto não pode ser excluída, pois já foi utilizada!");
            }else {
                setMensagemDetalhada("msg_erro", e.getMessage());
            }
            return "editar";
        }
    }

    /**
     * Operação responsável por processar a exclusão um objeto da classe <code>CategoriaProdutoVO</code>
     * Após a exclusão ela automaticamente aciona a rotina para uma nova inclusão.
     */
    public String excluir() {
        return this.excluir(false);
    }

    /**
     * Responsável por por processar a exclusão um objeto da classe <code>CategoriaProdutoVO</code> pelo CE  
     * <AUTHOR>
     * 22/03/2011
     * @return
     */
    public String excluirCE() {
        return this.excluir(true);
    }

    public void irPaginaInicial() throws Exception {
        controleConsulta.setPaginaAtual(1);
        this.consultar();
    }

    public void irPaginaAnterior() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getPaginaAtual() - 1);
        this.consultar();
    }

    public void irPaginaPosterior() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getPaginaAtual() + 1);
        this.consultar();
    }

    public void irPaginaFinal() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getNrTotalPaginas());
        this.consultar();
    }

    /**
     * Rotina responsável por preencher a combo de consulta da telas.
     */
    public List getTipoConsultaCombo() {
        List itens = new ArrayList();
        itens.add(new SelectItem("descricao", "Descrição"));
        itens.add(new SelectItem("codigo", "Código"));

        return itens;
    }

    /**
     * Rotina responsável por organizar a paginação entre as páginas resultantes de uma consulta.
     */
    public String inicializarConsultar() {
        setPaginaAtualDeTodas("0/0");
        setListaConsulta(new ArrayList());
        setSucesso(false);
        setErro(false);
        definirVisibilidadeLinksNavegacao(0, 0);
        setMensagemID("msg_entre_prmconsulta");
        return "consultar";
    }

    /**
     * Operação que inicializa as Interfaces Façades com os respectivos objetos de
     * persistência dos dados no banco de dados. 
     */
    protected boolean inicializarFacades() {
        try {
            super.inicializarFacades();
            return true;
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro_conectarBD", e.getMessage());
            return false;
        }
    }

    public CategoriaProdutoVO getCategoriaProdutoVO() {
        return categoriaProdutoVO;
    }

    public void setCategoriaProdutoVO(CategoriaProdutoVO categoriaProdutoVO) {
        this.categoriaProdutoVO = categoriaProdutoVO;
    }

    public void exportar(ActionEvent evt) throws Exception {
        ExportadorListaControle exportadorListaControle = (ExportadorListaControle) JSFUtilities.getFromRequest(ExportadorListaControle.class.getSimpleName());
        String paramsTableFiltrada = context().getExternalContext().getRequestParameterMap().get("paramsTableFiltrada");

        String[] split = paramsTableFiltrada.split(",");
        String campoOrdenacao = split[0].replace("[", "");
        String ordem = split[1];
        String filtro = split[2].replace("''", "");
        filtro = filtro.replace("]", "");
        List listaParaImpressao = getFacade().getCategoriaProduto().consultarParaImpressao(filtro, ordem, campoOrdenacao, getEmpresaLogado() == null ? 0 : getEmpresaLogado().getCodigo());
        exportadorListaControle.exportar(evt, listaParaImpressao, filtro, null);

    }
    
    
    public void incluirLogInclusao() throws Exception {
        try {
            categoriaProdutoVO.setObjetoVOAntesAlteracao(new CategoriaProdutoVO());
            categoriaProdutoVO.setNovoObj(true);
            registrarLogObjetoVO(categoriaProdutoVO, categoriaProdutoVO.getCodigo(), "CATEGORIAPRODUTO", 0);
        } catch (Exception e) {
            registrarLogErroObjetoVO("CATEGORIAPRODUTO", categoriaProdutoVO.getCodigo(), "ERRO AO GERAR LOG DE INCLUSÃO DE CATEGORIAPRODUTO", this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
        categoriaProdutoVO.setNovoObj(new Boolean(false));
        categoriaProdutoVO.registrarObjetoVOAntesDaAlteracao();
    }
    
    public void incluirLogExclusao() throws Exception {
        try {
            categoriaProdutoVO.setObjetoVOAntesAlteracao(new CategoriaProdutoVO());
            categoriaProdutoVO.setNovoObj(true);
            registrarLogExclusaoTodosDadosObjetoVO(categoriaProdutoVO, categoriaProdutoVO.getCodigo(), "CATEGORIAPRODUTO", 0);
        } catch (Exception e) {
            registrarLogErroObjetoVO("CATEGORIAPRODUTO", categoriaProdutoVO.getCodigo(), "ERRO AO GERAR LOG DE EXCLUSÃO DE CATEGORIAPRODUTO ", this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
    }

    /**
     * Inclui o log de alteração de categoria
     * @throws Exception
     */
    public void incluirLogAlteracao() throws Exception {
        try {
            registrarLogObjetoVO(categoriaProdutoVO, categoriaProdutoVO.getCodigo(), "CATEGORIAPRODUTO", 0);
            categoriaProdutoVO.registrarObjetoVOAntesDaAlteracao();
        } catch (Exception e) {
            registrarLogErroObjetoVO("CATEGORIAPRODUTO", categoriaProdutoVO.getCodigo(), "ERRO AO GERAR LOG DE ALTERAÇÃO DE CATEGORIAPRODUTO ", this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
        categoriaProdutoVO.registrarObjetoVOAntesDaAlteracao();
    }
    
    @SuppressWarnings("unchecked")
    public void realizarConsultaLogObjetoSelecionado() {
        LoginControle loginControle = (LoginControle) context().getExternalContext().getSessionMap().get("LoginControle");
        loginControle.setNomeClasse("Categoria de Produto");
        loginControle.consultarLogObjetoSelecionado("CATEGORIAPRODUTO", categoriaProdutoVO.getCodigo(), null);
    }
    @SuppressWarnings("unchecked")
    public void realizarConsultaLogObjetoGeral() {
      categoriaProdutoVO = new CategoriaProdutoVO();
      realizarConsultaLogObjetoSelecionado();
    }


    public ComissaoProdutoConfiguracaoVO getComissaoVO() {
        return comissaoVO;
    }

    public void setComissaoVO(ComissaoProdutoConfiguracaoVO comissaoVO) {
        this.comissaoVO = comissaoVO;
    }

    public void adicionarComissao() {
        try {
            montarSucesso("");
            getComissaoVO().setCategoriaProduto(categoriaProdutoVO);
            if (isApresentarEmpresaComissao()) {
                if (!UteisValidacao.emptyNumber(getComissaoVO().getEmpresa().getCodigo())) {
                    EmpresaVO empresaVO = getFacade().getEmpresa().consultarPorCodigo(getComissaoVO().getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_MINIMOS);
                    getComissaoVO().setEmpresa(empresaVO);
                }
            }

            boolean problema = false;
            ComissaoProdutoConfiguracaoVO comissaoProblema = null;
            for (ComissaoProdutoConfiguracaoVO comissaoCadastrada : getCategoriaProdutoVO().getComissaoCategoriaProdutos()) {
                if (getComissaoVO().getEmpresa().getCodigo().equals(comissaoCadastrada.getEmpresa().getCodigo()) &&
                        ((getComissaoVO().getVigenciaInicio_Long() >= comissaoCadastrada.getVigenciaInicio_Long() && getComissaoVO().getVigenciaInicio_Long() <= comissaoCadastrada.getVigenciaFinal_Long())
                        || (getComissaoVO().getVigenciaFinal_Long() >= comissaoCadastrada.getVigenciaInicio_Long() && getComissaoVO().getVigenciaInicio_Long() <= comissaoCadastrada.getVigenciaFinal_Long()))) {

                    if (!getComissaoVO().isNovoObj() && getComissaoVO().getCodigo().equals(comissaoCadastrada.getCodigo())) {
                        continue;
                    }

                    problema = true;
                    comissaoProblema = comissaoCadastrada;
                    break;
                }

                comissaoCadastrada.ajustarDataFinal(getComissaoVO().getVigenciaInicio());
            }

            if (!problema) {
                if (getComissaoVO().isNovoObj()) {
                    getCategoriaProdutoVO().getComissaoCategoriaProdutos().add(getComissaoVO());
                } else {
                    ComissaoProdutoConfiguracaoVO configRemover = null;
                    for (ComissaoProdutoConfiguracaoVO comissaoCadastrada : getCategoriaProdutoVO().getComissaoCategoriaProdutos()) {
                        if (comissaoCadastrada.getCodigo().equals(getComissaoVO().getCodigo())) {
                            configRemover = comissaoCadastrada;
                            break;
                        }
                    }
                    getCategoriaProdutoVO().getComissaoCategoriaProdutos().remove(configRemover);
                    getCategoriaProdutoVO().getComissaoCategoriaProdutos().add(getComissaoVO());

                }
                inicializarComissao();
            } else {
                montarErro("A configuração está conflitando com outra (" + String.valueOf(comissaoProblema) + " [" + comissaoProblema.getVigenciaInicio_Apresentar() + "-" + comissaoProblema.getVigenciaFinal_Apresentar() + "]" + "). Por favor, reveja a nova configuração.");
            }
        } catch (Exception ex) {
            montarErro("Problemas ao adicionar nova configuração de comissão.");
        }
    }

    public void inicializarComissao() throws Exception {
        List<EmpresaVO> empresaVOs = getFacade().getEmpresa().consultarTodas(true, Uteis.NIVELMONTARDADOS_MINIMOS);
        setComissaoVO(new ComissaoProdutoConfiguracaoVO());
        setApresentarEmpresaComissao(true);
        if (empresaVOs.size() == 1) {
            getComissaoVO().setEmpresa(empresaVOs.get(0));
            setApresentarEmpresaComissao(false);
        }
    }

    public void removerComissao() {
        ComissaoProdutoConfiguracaoVO comissaoRemover = (ComissaoProdutoConfiguracaoVO) JSFUtilities.getFromRequest("comissaoProduto");
        getCategoriaProdutoVO().getComissaoCategoriaProdutos().remove(comissaoRemover);
    }

    public void editarComissao() throws Exception {
        ComissaoProdutoConfiguracaoVO comissaoRemover = (ComissaoProdutoConfiguracaoVO) JSFUtilities.getFromRequest("comissaoProduto");
        if (comissaoRemover == null) {
            setErro(true);
            setMensagemDetalhada("", "Erro ao selecionar configuração");
            return;
        }
        setComissaoVO((ComissaoProdutoConfiguracaoVO) comissaoRemover.getClone(true));
        if (getComissaoVO().getCodigo() > 0) {
            getComissaoVO().setNovoObj(false);
        }
        setMensagemID("msg_dados_editar");
        setSucesso(true);
        setAtencao(false);
        setErro(false);
    }


    public boolean isApresentarEmpresaComissao() {
        return apresentarEmpresaComissao;
    }

    public void setApresentarEmpresaComissao(boolean apresentarEmpresaComissao) {
        this.apresentarEmpresaComissao = apresentarEmpresaComissao;
    }

    public boolean isApresentarAbaComissao() {
        return !getCategoriaProdutoVO().isNovoObj();
}

    public void setMsgAlert(String msgAlert) {
        this.msgAlert = msgAlert;
    }

    public String getMsgAlert() {
        if (msgAlert == null) {
            return "";
        }
        return msgAlert;
    }


    public void confirmarExcluir(){
        MensagemGenericaControle control = (MensagemGenericaControle) getControlador(MensagemGenericaControle.class);
        control.setMensagemDetalhada("", "");
        setMsgAlert("Richfaces.showModalPanel('mdlMensagemGenerica');");
        control.init("Exclusão de Categoria de Produto",
                "Deseja excluir a Categoria de Produto?",
                this, "excluir", "", "", "", "grupoBtnExcluir");
    }
    public void montarListaSelectItemFormaPagamento(String prm) throws Exception {
        List resultadoConsulta = consultarFormaPagamentoPorDescricao(prm);
        Iterator i = resultadoConsulta.iterator();
        List objs = new ArrayList();
        objs.add(new SelectItem(new Integer(0), "NENHUM"));
        while (i.hasNext()) {
            FormaPagamentoVO obj = (FormaPagamentoVO) i.next();
            objs.add(new SelectItem(obj.getCodigo(), obj.getDescricao().toString()));
        }
        setListaSelectItemFormaPagamento(objs);
        montarListaEmpresas();
    }

    public List getListaSelectItemFormaPagamento() {
        return listaSelectItemFormaPagamento;
    }

    public void setListaSelectItemFormaPagamento(List listaSelectItemFormaPagamento) {
        this.listaSelectItemFormaPagamento = listaSelectItemFormaPagamento;
    }

    public List consultarFormaPagamentoPorDescricao(String descricaoPrm) throws Exception {
        List lista = getFacade().getFormaPagamento().consultarPorDescricao(descricaoPrm, false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        return lista;
    }

}

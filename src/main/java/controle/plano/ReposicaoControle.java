/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package controle.plano;

import br.com.pacto.priv.sms.SMSControle;
import br.com.pacto.priv.sms.beans.BeanSMSExternal;
import br.com.pacto.priv.utils.Tricks;
import br.com.pactosolucoes.comuns.notificacao.RecursoSistema;
import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.linhatempocontrato.controle.LinhaTempoContratoControle;
import controle.arquitetura.SuperControle;
import controle.basico.ClienteControle;
import java.util.ArrayList;
import java.util.List;
import java.util.TimeZone;
import java.util.UUID;
import java.util.logging.Level;
import java.util.logging.Logger;
import javax.faces.event.ActionEvent;

import controle.basico.TelaClienteControle;
import java.net.ConnectException;
import java.sql.Connection;
import negocio.comuns.basico.AulaDesmarcadaVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.ReposicaoVO;
import negocio.comuns.crm.ConfiguracaoSistemaCRMVO;
import negocio.comuns.plano.HorarioTurmaVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisEmail;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.utilitarias.Conexao;
import servicos.impl.gestaoaula.GestaoAulaService;
import servicos.integracao.sms.Message;
import servicos.integracao.sms.SmsController;

/**
 *
 * <AUTHOR>
 */
public class ReposicaoControle extends SuperControle {

    private List<ReposicaoVO> reposicoes = new ArrayList();
    private UteisEmail uteisEmail = null;
    private ConfiguracaoSistemaCRMVO configCRM = null;

    public ReposicaoControle() {
        try {
            uteisEmail = new UteisEmail();
            configCRM = getConfiguracaoSMTPNoReply();
            uteisEmail.novo("", configCRM);
        } catch (Exception ex) {
            setMensagemDetalhada(ex);
            Logger.getLogger(ReposicaoControle.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    public List<ReposicaoVO> getReposicoes() {
        return reposicoes;
    }

    public void setReposicoes(List<ReposicaoVO> reposicoes) {
        this.reposicoes = reposicoes;
    }

    public void exibirReposicoes(ActionEvent evt) {
        notificarRecursoEmpresa(RecursoSistema.TURMA_REPOSICOES_CLIENTE);
        String turma = null;
        Integer contrato = null;
        HorarioTurmaVO horarioTurma = null;
        boolean sairam = false;
        if (evt.getComponent().getAttributes().get("turmaReposicao") != null) {
            turma = Integer.toString((Integer) evt.getComponent().getAttributes().get("turmaReposicao"));
            contrato = (Integer) evt.getComponent().getAttributes().get("contrato");
        } else if (evt.getComponent().getAttributes().get("horarioTurmaAtual") != null) {
            horarioTurma = (HorarioTurmaVO) evt.getComponent().getAttributes().get("horarioTurmaAtual");
            sairam = Boolean.parseBoolean((String) evt.getComponent().getAttributes().get("sairam"));
        }
        try {
            String semana = Calendario.semanaAtualSQL(Calendario.hoje());
            if (horarioTurma != null) {
                reposicoes = getFacade().getReposicao().consultarReposicoesPorHorarioTurma(
                        horarioTurma.getCodigo(),
                        sairam, semana, Uteis.NIVELMONTARDADOS_TODOS, null);
                if (sairam) {
                    String reposicoesId = "";
                    for (ReposicaoVO reposicaoVO : reposicoes) {
                        reposicoesId += "," + reposicaoVO.getCodigo();
                    }
                    reposicoesId = reposicoesId.equals("") ? "" : reposicoesId.substring(1);
                    List<AulaDesmarcadaVO> aulaDesmarcadaVOS = getFacade().getAulaDesmarcada().consultarAulaDesmarcadaPorHorarioTurmaEPeriodo(horarioTurma.getCodigo(), semana, Uteis.NIVELMONTARDADOS_TODOS, null, reposicoesId);
                    for (AulaDesmarcadaVO aulaDesmarcadaVO : aulaDesmarcadaVOS) {
                        ReposicaoVO reposicaoVO = new ReposicaoVO();
                        ClienteVO clienteVO = getFacade().getCliente().consultarPorCodigo(aulaDesmarcadaVO.getClienteVO().getCodigo(), false, Uteis.NIVELMONTARDADOS_TODOS);
                        reposicaoVO.setCliente(clienteVO);
                        reposicaoVO.setOrigemSistemaEnum(aulaDesmarcadaVO.getOrigemSistemaEnum());
                        reposicaoVO.setDataOrigem(aulaDesmarcadaVO.getDataOrigem());
                        reposicaoVO.setHorarioTurmaOrigem(aulaDesmarcadaVO.getHorarioTurmaVO());
                        reposicaoVO.setUsuario(aulaDesmarcadaVO.getUsuarioVO());
                        reposicoes.add(reposicaoVO);
                    }
                }
            } else if (turma != null) {
                reposicoes = getFacade().getReposicao().consultarReposicoes(
                        contrato.toString(),
                        turma, "", false, null, null, true);
            }
        } catch (Exception ex) {
            Logger.getLogger(ClienteControle.class.getName()).log(Level.SEVERE, null, ex);
            setMensagemDetalhada(ex);
        }
    }

    public String getOnComplete() {
        return !reposicoes.isEmpty() ? "Richfaces.showModalPanel('modalReposicoes');" : "";
    }
    public void atualizarConsultaturma(){
        ConsultarTurmaControle cons = (ConsultarTurmaControle) JSFUtilities.getManagedBeanValue("ConsultarTurmaControle");
        cons.consultarTurmas(Calendario.hoje(),true,false);
    }
    public void deleteListener(ActionEvent evt) throws Exception {
        ReposicaoVO repo = (ReposicaoVO) evt.getComponent().getAttributes().get("reposicao");
        setMsgAlert("");
        GestaoAulaService gestaoAulaService = new GestaoAulaService(Conexao.getFromSession(), (String) JSFUtilities.getFromSession("key"));
        try {
            if (repo != null) {
                reposicoes.remove(repo);
                repo = getFacade().getReposicao().consultarPorChavePrimaria(repo.getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);
                repo.getCliente().setPessoa(getFacade().getPessoa().consultarPorChavePrimaria(
                        repo.getCliente().getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS));
                repo.setUsuario(getUsuarioLogado());
                boolean ignorarAntecedencia = true;
                try{
                     permissaoFuncionalidade(getUsuarioLogado(), "DesmarcarAulaForaTolerancia", " 2.64 - Permitir desmarcar aula fora da tolerância da turma ");
                } catch (Exception e){
                    ignorarAntecedencia = false;
                }
                gestaoAulaService.desmarcarReposicao(repo, ignorarAntecedencia);
                ClienteControle control = (ClienteControle) getControlador(ClienteControle.class);
                control.preencherInformacoesPresencaTurmas();
                //
                StringBuilder mensagemAlerta = new StringBuilder();
                mensagemAlerta.append("Reposição Excluída com Sucesso!");
                try {
                    enviarSMS(repo, true);
                }catch (Exception erro ){
                    mensagemAlerta.append("\nErro ao Enviar SMS;");
                }
                try{
                    enviarEmail(repo, true);
                }catch (Exception erro){
                    mensagemAlerta.append("\nErro ao Enviar E-mail;");
                }
                atualizarConsultaturma();
                montarMsgAlert(mensagemAlerta.toString());
                LinhaTempoContratoControle linhaControle = (LinhaTempoContratoControle) context().getExternalContext().getRequestMap().get(LinhaTempoContratoControle.class.getSimpleName());
                if (linhaControle != null) {
                    TelaClienteControle telaClienteControle = (TelaClienteControle)JSFUtilities.getManagedBean("TelaClienteControle");
                    telaClienteControle.carregarDadosContrato(repo.getContrato().getCodigo());
                }
            }
        } catch (Exception ex) {
            if (repo != null && reposicoes != null) {
                if (reposicoes.indexOf(repo) == -1) {
                    reposicoes.add(repo);
                }
            }
            montarMsgAlert(ex.getMessage());
            Logger.getLogger(ReposicaoControle.class.getName()).log(Level.SEVERE, null, ex);
        } finally {
            gestaoAulaService = null;
        }
    }

    public void enviarEmailESMS(ActionEvent evt) {
        ReposicaoVO repo = (ReposicaoVO) evt.getComponent().getAttributes().get("reposicao");
        setMsgAlert("");
        if (repo != null) {
            try {
                repo = getFacade().getReposicao().consultarPorChavePrimaria(repo.getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);
                repo.getCliente().setPessoa(getFacade().getPessoa().consultarPorChavePrimaria(repo.getCliente().getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS));
                enviarEmail(repo, false);
                enviarSMS(repo, false);
                montarMsgAlert("Email e/ou SMS enviados com sucesso!");
            } catch (Exception ex) {
                montarMsgAlert(ex.getMessage());
                Logger.getLogger(ReposicaoControle.class.getName()).log(Level.SEVERE, null, ex);
            }
        }
    }

    public void enviarEmail(final ReposicaoVO repo, boolean exclusao) throws Exception {
        getFacade().getReposicao().enviarEmail(repo, exclusao);
    }

    public void enviarSMS(final ReposicaoVO repo, boolean exclusao) throws Exception {
        getFacade().getReposicao().enviarSMS(repo, exclusao);
    }
}

package controle.plano;

import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.estrutura.exportador.controle.ExportadorListaControle;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Enumeration;
import java.util.Hashtable;
import java.util.List;

import javax.faces.model.SelectItem;

import controle.basico.clube.MensagemGenericaControle;
import negocio.comuns.contrato.JustificativaOperacaoVO;
import negocio.comuns.plano.DescontoEmpresaVO;
import negocio.comuns.plano.DescontoRenovacaoVO;
import negocio.comuns.plano.DescontoVO;
import negocio.comuns.plano.enumerador.TipoDesconto;
import negocio.comuns.plano.enumerador.TipoIntervalo;
import negocio.comuns.utilitarias.ControleConsulta;
import negocio.comuns.utilitarias.Dominios;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.SelectItemOrdemValor;
import controle.arquitetura.SuperControle;
import controle.arquitetura.security.LoginControle;
import javax.faces.event.ActionEvent;

public class DescontoControle extends SuperControle {

    private DescontoVO descontoVO;
    private DescontoRenovacaoVO descontoRenovacaoVO;
    private DescontoRenovacaoVO edicao;
    private List<JustificativaOperacaoVO> justificativas;
    private List<DescontoRenovacaoVO> listaAntecipados = new ArrayList<DescontoRenovacaoVO>();
    private List<DescontoRenovacaoVO> listaAtrasados = new ArrayList<DescontoRenovacaoVO>();
    private List<DescontoEmpresaVO> empresas = new ArrayList<DescontoEmpresaVO>();
    private boolean checkTodasEmpresas;
    private boolean atualizarEmpresas;
    private String msgAlert;

    public DescontoControle() throws Exception {
        novo();
        obterUsuarioLogado();
        inicializarFacades();
        setControleConsulta(new ControleConsulta());
        setMensagemID("");
    }

    public String novo() throws Exception {
        setDescontoVO(new DescontoVO());
        getDescontoVO().setEmpresas(getFacade().getDescontoEmpresa().consultarTodas(0));
        setDescontoRenovacaoVO(new DescontoRenovacaoVO());
        setJustificativas(new ArrayList<JustificativaOperacaoVO>());
        setListaAntecipados(new ArrayList<DescontoRenovacaoVO>());
        setListaAtrasados(new ArrayList<DescontoRenovacaoVO>());
        setEdicao(null);
        limparMsg();
        return "editar";
    }

    public String editar() {
        Integer codigoConsulta = Integer.parseInt(request().getParameter("chavePrimaria"));
        try {
            DescontoVO obj = getFacade().getDesconto().consultarPorChavePrimaria(codigoConsulta, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            obj.setNovoObj(false);
            obj.registrarObjetoVOAntesDaAlteracao();
            obj.setEmpresas(getFacade().getDescontoEmpresa().consultarTodas(obj.getCodigo()));
            setDescontoVO(obj);
            separaListasIntervalos();
            setEdicao(null);
        } catch (Exception e) {
            setErro(true);
            setSucesso(false);
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
        return "editar";
    }

    private void separaListasIntervalos() {
        setListaAntecipados(new ArrayList<DescontoRenovacaoVO>());
        setListaAtrasados(new ArrayList<DescontoRenovacaoVO>());
        for (DescontoRenovacaoVO intervalo : descontoVO.getListaIntervalos()) {
            if (intervalo.getTipoIntervalo().equals(TipoIntervalo.AN)) {
                listaAntecipados.add(intervalo);
            } else {
                listaAtrasados.add(intervalo);
            }
        }
    }

    /**
     * Responsável por gravar no BD um objeto da classe DescontoVO, usado pelo CE
     * <AUTHOR>
     * 22/03/2011
     * @return
     */
    public String gravarCE() {
        return this.gravar(true);
    }

    /**
     * Responsável por gravar no BD um objeto da classe DescontoVO, usado pelo ZW
     * <AUTHOR>
     * 22/03/2011
     * @return
     */
    public String gravar() {
        return this.gravar(false);
    }

    public String gravar(boolean centralEventos) {
        try {
            if (centralEventos) {
                if (descontoVO.isNovoObj().booleanValue()) {
                    getFacade().getDesconto().incluir(descontoVO, true);

                    //LOG - INICIO
                    try {
                        descontoVO.setObjetoVOAntesAlteracao(new DescontoVO());
                        descontoVO.setNovoObj(true);
                        registrarLogObjetoVO(descontoVO, descontoVO.getCodigo(), "DESCONTO CE", 0);
                    } catch (Exception e) {
                        registrarLogErroObjetoVO("DESCONTO CE", descontoVO.getCodigo(), "ERRO AO GERAR LOG DE INCLUSÃO DE DESCONTO CE", this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
                        e.printStackTrace();
                    }
                    descontoVO.setNovoObj(false);
                    //LOG - FIM
                } else {
                    getFacade().getDesconto().alterar(descontoVO, true);

                    //LOG - INICIO
                    try {
                        registrarLogObjetoVO(descontoVO, descontoVO.getCodigo(), "DESCONTO CE", 0);
                    } catch (Exception e) {
                        registrarLogErroObjetoVO("DESCONTO CE", descontoVO.getCodigo(), "ERRO AO GERAR LOG DE ALTERAÇÃO DE DESCONTO CE", this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
                        e.printStackTrace();
                    }
                    //LOG - FIM
                }
            } else {
                if (descontoVO.isNovoObj().booleanValue()) {
                    getFacade().getDesconto().incluir(descontoVO);
                    incluirLogInclusao();
                } else {
                    getFacade().getDesconto().alterar(descontoVO);
                    incluirLogAlteracao();
                }
            }

            setMensagemID("msg_dados_gravados");
            setSucesso(true);
            setErro(false);
            return "editar";
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
            return "editar";
        }
    }

    public String consultar() {
        try {
            super.consultar();
            List objs = new ArrayList();
            if (getControleConsulta().getCampoConsulta().equals("codigo")) {
                if (getControleConsulta().getValorConsulta().equals("")) {
                    getControleConsulta().setValorConsulta("0");
                }
                int valorInt = Integer.parseInt(getControleConsulta().getValorConsulta());
                objs = getFacade().getDesconto().consultarPorCodigo(new Integer(valorInt), true, Uteis.NIVELMONTARDADOS_TODOS);
            }
            if (getControleConsulta().getCampoConsulta().equals("descricao")) {
                objs = getFacade().getDesconto().consultarPorDescricao(getControleConsulta().getValorConsulta(), true, Uteis.NIVELMONTARDADOS_TODOS);
            }
            if (getControleConsulta().getCampoConsulta().equals("valor")) {
                if (getControleConsulta().getValorConsulta().equals("")) {
                    getControleConsulta().setValorConsulta("0");
                }
                double valorDouble = Double.parseDouble(getControleConsulta().getValorConsulta());
                objs = getFacade().getDesconto().consultarPorValor(new Double(valorDouble), true, Uteis.NIVELMONTARDADOS_TODOS);
            }
            if (getControleConsulta().getCampoConsulta().equals("tipoDesconto")) {
                objs = getFacade().getDesconto().consultarPorTipoDesconto(getControleConsulta().getValorConsulta(), true, Uteis.NIVELMONTARDADOS_TODOS);
            }
            if (getControleConsulta().getCampoConsulta().equals("tipoProduto")) {
                objs = getFacade().getDesconto().consultarPorTipoProduto(getControleConsulta().getValorConsulta(), true, Uteis.NIVELMONTARDADOS_TODOS);
            }
            objs = ControleConsulta.obterSubListPaginaApresentar(objs, controleConsulta);
            definirVisibilidadeLinksNavegacao(controleConsulta.getPaginaAtual(), controleConsulta.getNrTotalPaginas());
            setListaConsulta(objs);
            limparMsg();
            return "consultar";
        } catch (Exception e) {
            setListaConsulta(new ArrayList());
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
            return "consultar";
        }
    }

    public String excluir() {
        return this.excluir(false);
    }

    public String excluirCE() {
        return this.excluir(true);
    }

    public String excluir(boolean centralEventos) {
        try {
            if (centralEventos) {
                this.verificarAutorizacao();
                getFacade().getDesconto().excluir(descontoVO, true);

                //LOG - INICIO
                try {
                    registrarLogExclusaoObjetoVO(descontoVO, descontoVO.getCodigo().intValue(), "DESCONTO CE", 0);
                } catch (Exception e) {
                    registrarLogErroObjetoVO("DESCONTO CE", descontoVO.getCodigo(), "ERRO AO GERAR LOG DE EXCLUSÃO DE DESCONTO CE", this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
                    e.printStackTrace();
                }
                //LOG - FIM
            } else {
                getFacade().getDesconto().excluir(descontoVO);
                incluirLogExclusao();
            }
            setDescontoVO(new DescontoVO());
            setMensagemID("msg_dados_excluidos");
            setSucesso(true);
            setErro(false);
            return "consultar";
        } catch (Exception e) {
            if (e.getMessage().contains("ERRO: atualização ou exclusão em tabela \"desconto\" viola restrição de chave estrangeira")){
                setMensagemDetalhada("Este Desconto não pode ser excluído, pois está sendo utilizado!");
            }else {
                setMensagemDetalhada("msg_erro", e.getMessage());
            }
//            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
            return "editar";
        }
    }

    public void irPaginaInicial() throws Exception {
        controleConsulta.setPaginaAtual(1);
        this.consultar();
    }

    public void irPaginaAnterior() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getPaginaAtual() - 1);
        this.consultar();
    }

    public void irPaginaPosterior() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getPaginaAtual() + 1);
        this.consultar();
    }

    public void irPaginaFinal() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getNrTotalPaginas());
        this.consultar();
    }

    public List getListaSelectItemTipoProdutoDesconto() throws Exception {
        List objs = new ArrayList();
        objs.add(new SelectItem("", ""));
        Hashtable desconto_tipoProdutos = (Hashtable) Dominios.getTipoProduto();
        Enumeration keys = desconto_tipoProdutos.keys();
        while (keys.hasMoreElements()) {
            String value = (String) keys.nextElement();
            String label = (String) desconto_tipoProdutos.get(value);
            objs.add(new SelectItem(value, label));
        }
        SelectItemOrdemValor ordenador = new SelectItemOrdemValor();
        Collections.sort((List) objs, ordenador);
        return objs;
    }
    public Boolean getDescontoRenovacaoContrato(){
        if(descontoRenovacaoVO.getTipoDesconto().equals(TipoDesconto.BO)){
            return true;
        }else{
            return false;
        }
    }

    public List getListaSelectItemTipoDescontoDesconto() throws Exception {
        List objs = new ArrayList();
        for (TipoDesconto td : TipoDesconto.values()) {
            if (!td.equals(TipoDesconto.BO)) {
                objs.add(new SelectItem(td, td.getDescricao()));
            }
        }
        return objs;
    }

    public List getListaSelectItemTipoDescontoAntecipado() throws Exception {
        List objs = new ArrayList();
        for (TipoDesconto td : TipoDesconto.values()) {
            objs.add(new SelectItem(td, td.getDescricao()));
        }
        return objs;
    }

    public List getListaSelectItemTipoIntervaloAntecipado() throws Exception {
        List objs = new ArrayList();
        for (TipoIntervalo ti : TipoIntervalo.values()) {
            objs.add(new SelectItem(ti, ti.getDescricao()));
        }
        return objs;
    }

    public List getListaSelectItemJustificativaOperacao() throws Exception {
        justificativas = getFacade().getJustificativaOperacao().consultarPorTipoOperacao("BO", getEmpresaLogado().getCodigo().intValue(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        List objs = new ArrayList();
        objs.add(new SelectItem(0, ""));
        for (JustificativaOperacaoVO obj : justificativas) {
            objs.add(new SelectItem(obj.getCodigo(), obj.getDescricao()));
        }
        return objs;
    }

    public List getTipoConsultaCombo() {
        List itens = new ArrayList();
        itens.add(new SelectItem("descricao", "Descrição"));
        itens.add(new SelectItem("codigo", "Código"));
        itens.add(new SelectItem("valor", "Valor"));
        itens.add(new SelectItem("tipoDesconto", "Tipo de Desconto"));
        itens.add(new SelectItem("tipoProduto", "Tipo de Produto"));
        return itens;
    }

    public String inicializarConsultar() {
        setPaginaAtualDeTodas("0/0");
        setListaConsulta(new ArrayList());
        definirVisibilidadeLinksNavegacao(0, 0);
        setDescontoRenovacaoVO(new DescontoRenovacaoVO());
        setEdicao(null);
        limparMsg();
        return "consultar";
    }

    public void adicionarIntervalo() throws Exception {
        try {
            descontoRenovacaoVO.validarDados();
            buscaJustificativa();
            adicionaIntervalo(descontoRenovacaoVO);
            descontoRenovacaoVO = new DescontoRenovacaoVO();
            setEdicao(null);
            setMensagemDetalhada("");
            setSucesso(true);
            setErro(false);
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
        }
    }

    private void buscaJustificativa() {
        // procura qual a justificativa selecionada
        for (JustificativaOperacaoVO just : justificativas) {
            if (just.getCodigo().intValue() == descontoRenovacaoVO.getJustificativaBonus().getCodigo()) {
                descontoRenovacaoVO.setJustificativaBonus(just);
            }
        }
    }

    private void adicionaIntervalo(DescontoRenovacaoVO desconto) throws Exception {
        int primeiroDia = 9999;
        int ultimoDia = 0;
        List<DescontoRenovacaoVO> listaIntervalos;
        if (desconto.getTipoIntervalo().equals(TipoIntervalo.AN)) {
            listaIntervalos = listaAntecipados;
        } else {
            listaIntervalos = listaAtrasados;
        }
        // percorre a lista de intervalos até encontrar o ultimo intervalo, do mesmo tipo, inserido
        for (DescontoRenovacaoVO aux : listaIntervalos) {
            if (!aux.equals(getEdicao())) {
                primeiroDia = aux.getIntervaloDe();
                ultimoDia = aux.getIntervaloAte();
                // inicio do novo intervalo nao pode ser menor que o fim do ultimo intervalo conhecido
                if (desconto.getIntervaloDe() < ultimoDia) {
                    if (desconto.getIntervaloAte() < primeiroDia) {
                        throw new Exception("O Intervalo Sendo Informado Não Pode Ser Menor Que um Intervalo Existente.");
                    } else {
                        throw new Exception("O Intervalo Sendo Informado Está em Conflito com um Intervalo Existente.");
                    }
                }
            }
        }
        // se o intervalo sendo informado não é o primeiro
        if (primeiroDia == 9999) {
            // primeiro intervalo do desconto para atrasado começa com 1 dia de atraso
            if (descontoRenovacaoVO.getTipoIntervalo().equals(TipoIntervalo.AT)
                    && desconto.getIntervaloDe() == 0) {
                desconto.setIntervaloDe(1);
            }
        } else {
            // inicio do novo intervalo deve ser exatamente igual ao ultimoDia do intervalo anterior + 1
            if (desconto.getIntervaloDe() == ultimoDia) {
                desconto.setIntervaloDe(ultimoDia + 1);
            } // se o inicio do novo intervalo estiver errado apenas corrige
            else if (desconto.getIntervaloDe() > ultimoDia + 1) {
                desconto.setIntervaloDe(ultimoDia + 1);
            }

            if (desconto.getIntervaloAte() == ultimoDia) {
                desconto.setIntervaloAte(ultimoDia + 1);
            }
//            // se o inicio do novo intervalo estiver errado apenas corrige
//            if (desconto.getIntervaloDe() == desconto.getIntervaloAte()) {
//                desconto.setIntervaloAte(desconto.getIntervaloDe() + 1);
//            }
        }
        // se é edicao
        if (getEdicao() != null) {
            // remove o intervalo sendo editado das listas
            listaIntervalos.remove(listaIntervalos.indexOf(getEdicao()));
            descontoVO.getListaIntervalos().remove(descontoVO.getListaIntervalos().indexOf(getEdicao()));
        }
        listaIntervalos.add(desconto);
        descontoVO.getListaIntervalos().add(desconto);
        setMensagem("");
        setMensagemDetalhada("");
        setMensagemID("msg_entre_dados");
        setSucesso(true);
        setErro(false);
    }

    public void editarIntervalo() {
        try {
            DescontoRenovacaoVO obj = (DescontoRenovacaoVO) context().getExternalContext().getRequestMap().get("intervalo");
            if (obj == null) {
                throw new Exception("Erro ao Posicionar Dados do Desconto. Contate Suporte Técnico.");
            }
            if (obj.getTipoIntervalo().equals(TipoIntervalo.AN)) {
                // so é possivel editar o ultimo intervalo disponivel
                if (listaAntecipados.indexOf(obj) != listaAntecipados.size() - 1) {
                    throw new Exception("Só é possível Editar o último Intervalo Cadastrado.");
                }
            } else {
                // so é possivel editar o ultimo intervalo disponivel
                if (listaAtrasados.indexOf(obj) != listaAtrasados.size() - 1) {
                    throw new Exception("Só é possível Editar o último Intervalo Cadastrado.");
                }
            }
            DescontoRenovacaoVO aux = (DescontoRenovacaoVO) obj.getClone(false);
            setDescontoRenovacaoVO(aux);
            setEdicao(obj);
            setMensagemDetalhada("");
            setSucesso(true);
            setErro(false);
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
        }
    }

    public String removerIntervalo() throws Exception {
        DescontoRenovacaoVO obj = (DescontoRenovacaoVO) context().getExternalContext().getRequestMap().get("intervalo");
        if (obj.getTipoIntervalo().equals(TipoIntervalo.AN)) {
            listaAntecipados.remove(listaAntecipados.indexOf(obj));
            descontoVO.getListaIntervalos().remove(descontoVO.getListaIntervalos().indexOf(obj));
        } else {
            listaAtrasados.remove(listaAtrasados.indexOf(obj));
            descontoVO.getListaIntervalos().remove(descontoVO.getListaIntervalos().indexOf(obj));
        }
        setDescontoRenovacaoVO(new DescontoRenovacaoVO());
        setEdicao(null);
        setMensagemID("msg_dados_excluidos");
        return "editar";
    }

    public DescontoVO getDescontoVO() {
        return descontoVO;
    }

    public void setDescontoVO(DescontoVO descontoVO) {
        this.descontoVO = descontoVO;
    }

    public DescontoRenovacaoVO getDescontoRenovacaoVO() {
        return descontoRenovacaoVO;
    }

    public void setDescontoRenovacaoVO(DescontoRenovacaoVO descontoRenovacaoVO) {
        this.descontoRenovacaoVO = descontoRenovacaoVO;
    }

    public boolean getMostrarDesconto() {
        return descontoVO.getTipoProduto() != null
                && !descontoVO.getTipoProduto().trim().isEmpty()
                && !descontoVO.getTipoProduto().equals("DR");
    }

    public boolean getMostrarDescontoRenovacao() {
        return descontoVO.getTipoProduto() != null
                && descontoVO.getTipoProduto().equals("DR");
    }

    public boolean getMostrarJustificativa() {
        return descontoRenovacaoVO.getTipoDesconto() != null
                && descontoRenovacaoVO.getTipoDesconto().equals(TipoDesconto.BO);
    }

    public DescontoRenovacaoVO getEdicao() {
        return edicao;
    }

    public void setEdicao(DescontoRenovacaoVO edicao) {
        this.edicao = edicao;
    }

    public List<JustificativaOperacaoVO> getJustificativas() {
        return justificativas;
    }

    public void setJustificativas(List<JustificativaOperacaoVO> justificativas) {
        this.justificativas = justificativas;
    }

    public List<DescontoRenovacaoVO> getListaAntecipados() {
        return listaAntecipados;
    }

    public void setListaAntecipados(List<DescontoRenovacaoVO> listaAntecipados) {
        this.listaAntecipados = listaAntecipados;
    }

    public List<DescontoRenovacaoVO> getListaAtrasados() {
        return listaAtrasados;
    }

    public void setListaAtrasados(List<DescontoRenovacaoVO> listaAtrasados) {
        this.listaAtrasados = listaAtrasados;
    }

    public void exportar(ActionEvent evt) throws Exception {
        ExportadorListaControle exportadorListaControle = (ExportadorListaControle) JSFUtilities.getFromRequest(ExportadorListaControle.class.getSimpleName());
        String paramsTableFiltrada = context().getExternalContext().getRequestParameterMap().get("paramsTableFiltrada");

        String[] split = paramsTableFiltrada.split(",");
        String campoOrdenacao = split[0].replace("[", "");
        String ordem = split[1];
        String filtro = split[2].replace("''", "");
        filtro = filtro.replace("]", "");
        List listaParaImpressao = getFacade().getDesconto().consultarParaImpressao(filtro, ordem, campoOrdenacao, getEmpresaLogado() == null ? 0 : getEmpresaLogado().getCodigo());
        exportadorListaControle.exportar(evt, listaParaImpressao, filtro, null);

    }
    
    public void incluirLogInclusao() throws Exception {
        try {
            descontoVO.setObjetoVOAntesAlteracao(new DescontoVO());
            descontoVO.setNovoObj(true);
            registrarLogObjetoVO(descontoVO, descontoVO.getCodigo(), "DESCONTO", 0);
        } catch (Exception e) {
            registrarLogErroObjetoVO("DESCONTO", descontoVO.getCodigo(), "ERRO AO GERAR LOG DE INCLUSÃO DE DESCONTO", this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
        descontoVO.setNovoObj(new Boolean(false));
        descontoVO.registrarObjetoVOAntesDaAlteracao();
    }
    
    public void incluirLogExclusao() throws Exception {
        try {
            descontoVO.setObjetoVOAntesAlteracao(new DescontoVO());
            descontoVO.setNovoObj(true);
            registrarLogExclusaoTodosDadosObjetoVO(descontoVO, descontoVO.getCodigo(), "DESCONTO", 0);
        } catch (Exception e) {
            registrarLogErroObjetoVO("DESCONTO", descontoVO.getCodigo(), "ERRO AO GERAR LOG DE EXCLUSÃO DE DESCONTO ", this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
    }

    /**
     * Inclui o log de alteração de categoria
     * @throws Exception
     */
    public void incluirLogAlteracao() throws Exception {
        try {
            registrarLogObjetoVO(descontoVO, descontoVO.getCodigo(), "DESCONTO", 0);
            descontoVO.registrarObjetoVOAntesDaAlteracao();
        } catch (Exception e) {
            registrarLogErroObjetoVO("DESCONTO", descontoVO.getCodigo(), "ERRO AO GERAR LOG DE ALTERAÇÃO DE DESCONTO ", this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
        descontoVO.registrarObjetoVOAntesDaAlteracao();
    }
    
     public void realizarConsultaLogObjetoSelecionado() {
        LoginControle loginControle = (LoginControle) context().getExternalContext().getSessionMap().get("LoginControle");
        String nomeClasse = descontoVO.getClass().getSimpleName();
        nomeClasse = nomeClasse.substring(0, nomeClasse.length() - 2);
        loginControle.setNomeClasse(loginControle.getNomeEntidadeCamposLog("prt_" + nomeClasse + "_tituloForm"));
        loginControle.consultarLogObjetoSelecionado(nomeClasse.toUpperCase(), descontoVO.getCodigo(), 0);
    }
    public void realizarConsultaLogObjetoGeral() {
       descontoVO = new DescontoVO();
       realizarConsultaLogObjetoSelecionado();
    }

    public List<DescontoEmpresaVO> getEmpresas() {
        return empresas;
    }

    public void setEmpresas(List<DescontoEmpresaVO> empresas) {
        this.empresas = empresas;
    }

    public boolean isCheckTodasEmpresas() {
        return checkTodasEmpresas;
    }

    public void setCheckTodasEmpresas(boolean checkTodasEmpresas) {
        this.checkTodasEmpresas = checkTodasEmpresas;
    }

    public boolean isAtualizarEmpresas() {
        return atualizarEmpresas;
    }

    public void setAtualizarEmpresas(boolean atualizarEmpresas) {
        this.atualizarEmpresas = atualizarEmpresas;
    }

    public void setMsgAlert(String msgAlert) {
        this.msgAlert = msgAlert;
    }

    public String getMsgAlert() {
        if (msgAlert == null) {
            return "";
        }
        return msgAlert;
    }


    public void confirmarExcluir(){
        MensagemGenericaControle control = (MensagemGenericaControle) getControlador(MensagemGenericaControle.class);
        control.setMensagemDetalhada("", "");
        setMsgAlert("Richfaces.showModalPanel('mdlMensagemGenerica');");
        control.init("Exclusão de Desconto",
                "Deseja excluir o Desconto?",
                this, "excluir", "", "", "", "grupoBtnExcluir");
    }

}

package controle.plano;

import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.estrutura.ItemExportacaoEnum;
import br.com.pactosolucoes.estrutura.exportador.controle.ExportadorListaControle;
import controle.arquitetura.SuperControle;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.plano.OrcamentoVO;
import negocio.comuns.utilitarias.Uteis;

import javax.faces.event.ActionEvent;
import java.util.List;

public class OrcamentoControleRel extends SuperControle {

    private OrcamentoVO orcamentoVO;

    public void exportar(ActionEvent evt) throws Exception {
        try {
            limparMsg();
            setMsgAlert("");
            ExportadorListaControle exportadorListaControle = (ExportadorListaControle) JSFUtilities.getFromRequest(ExportadorListaControle.class.getSimpleName());
            String paramsTableFiltrada = context().getExternalContext().getRequestParameterMap().get("paramsTableFiltrada");

            String[] split = paramsTableFiltrada.split(",");
            String campoOrdenacao = split[0].replace("[", "");
            String ordem = split[1];
            String filtro = split[2].replace("''", "");
            filtro = filtro.replace("]", "");
            List listaParaImpressao = getFacade().getOrcamentoRel().consultarParaImpressao(filtro, ordem, campoOrdenacao, getEmpresaLogado() == null ? 0 : getEmpresaLogado().getCodigo());
            exportadorListaControle.exportar(evt, listaParaImpressao, filtro, ItemExportacaoEnum.REL_ORCAMENTOS);
            if (exportadorListaControle.getErro()) {
                throw new Exception(exportadorListaControle.getMensagemDetalhada());
            }
            String tipo = (String) JSFUtilities.getFromActionEvent("tipo", evt);
            if(tipo.equals("pdf")){
                setMsgAlert("abrirPopup('UpdateServlet?op=downloadfile&file="+exportadorListaControle.getFileName()+"&mimetype=application/pdf','Transacoes', 800,200);");
            } else {
                setMsgAlert("abrirPopup('UpdateServlet?op=downloadfile&file="+exportadorListaControle.getFileName()+"&mimetype=application/vnd.ms-excel','Transacoes', 800,200);");
            }

        } catch (Exception e){
            montarErro(e);
        }


    }

    public String editar() {
        String retorno = "";
        Integer codigoConsulta = Integer.parseInt(request().getParameter("chavePrimaria"));
        try {
            if (!getUsuarioLogado().getAdministrador()) {
                EmpresaVO emp = getFacade().getEmpresa().consultarPorChavePrimaria(
                        getEmpresaLogado().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                JSFUtilities.setManagedBeanValue("EmpresaControle.empresaVO", emp);
            }
            OrcamentoVO obj = getFacade().getOrcamento().consultarPorChavePrimaria(codigoConsulta, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            obj.setNovoObj(false);
            setOrcamentoVO(new OrcamentoVO());
            obj.registrarObjetoVOAntesDaAlteracao();
            if(prepararTelaEdicaoOrcamento(obj)){
                retorno = "editar";
            }

        } catch (Exception e) {
            e.getStackTrace();
        }

        return retorno;
    }

    private boolean prepararTelaEdicaoOrcamento(OrcamentoVO obj){
        boolean podeEditar = false;
        try{
            OrcamentoControle orcamentoControle = (OrcamentoControle) context().getExternalContext().getSessionMap().get("OrcamentoControle");
            orcamentoControle.setNomeCliente(getFacade().getCliente().consultarPorChavePrimaria(obj.getCliente(), Uteis.NIVELMONTARDADOS_MINIMOS).getPessoa().getNomeAbreviado());
            orcamentoControle.setClienteSelecionado(obj.getCliente());
            orcamentoControle.setConsultorSelecionado(obj.getConsultor());
            orcamentoControle.setModeloOrcamentoSelecionado(obj.getModeloOrcamento());
            orcamentoControle.setPeriodoSelecionado(obj.getPeriodo());
            orcamentoControle.setSituacaoSelecionada(obj.getSituacao());
            orcamentoControle.setTipoTurmaSelecionada(obj.getTipoTurma());
            orcamentoControle.setOrcamentoVO(obj);
            podeEditar = true;
        }catch (Exception e){
            e.getStackTrace();
        }
        return podeEditar;
    }

    public OrcamentoVO getOrcamentoVO() {
        if(orcamentoVO == null){
            orcamentoVO = new OrcamentoVO();
        }
        return orcamentoVO;
    }

    public void setOrcamentoVO(OrcamentoVO orcamentoVO) {
        this.orcamentoVO = orcamentoVO;
    }
}

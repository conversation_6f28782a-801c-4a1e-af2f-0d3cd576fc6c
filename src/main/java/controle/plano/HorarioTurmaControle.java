//package controle.plano;
//import negocio.interfaces.plano.NivelTurmaInterfaceFacade;
//import negocio.interfaces.plano.AmbienteInterfaceFacade;
//import negocio.interfaces.basico.ColaboradorInterfaceFacade;
//import negocio.interfaces.plano.HorarioTurmaInterfaceFacade;
//import negocio.comuns.utilitarias.Dominios;
//import java.util.Hashtable;
//import java.util.Enumeration;
//import controle.arquitetura.SelectItemOrdemValor;
//import java.util.Collections;
//import negocio.comuns.plano.NivelTurmaVO;
//import negocio.comuns.plano.AmbienteVO;
//import negocio.comuns.basico.ColaboradorVO;
//import java.util.Iterator;
//import negocio.comuns.utilitarias.ControleConsulta;
//import negocio.facade.jdbc.plano.NivelTurma;
//import negocio.facade.jdbc.plano.Ambiente;
//import negocio.facade.jdbc.basico.Colaborador;
//import negocio.facade.jdbc.plano.HorarioTurma;
//import negocio.comuns.utilitarias.*;
//import negocio.comuns.plano.*;
//import javax.faces.model.SelectItem;
//import java.util.List;
//import java.util.ArrayList;
//import controle.arquitetura.SuperControle;
//
///**
// * Classe responsável por implementar a interação entre os componentes JSF das páginas 
// * horarioTurmaForm.jsp horarioTurmaCons.jsp) com as funcionalidades da classe <code>HorarioTurma</code>.
// * Implemtação da camada controle (Backing Bean).
// * @see SuperControle
// * @see HorarioTurma
// * @see HorarioTurmaVO
//*/
//public class HorarioTurmaControle extends SuperControle {
//    private HorarioTurmaVO horarioTurmaVO;
//    protected List listaSelectItemProfessor;
//    protected List listaSelectItemAmbiente;
//    protected List listaSelectItemNivelTurma;
//    /**
//    * Interface <code>HorarioTurmaInterfaceFacade</code> responsável pela interconexão da camada de controle com a camada de negócio.
//    * Criando uma independência da camada de controle com relação a tenologia de persistência dos dados (DesignPatter: Façade).
//    */
//   
//    private ColaboradorInterfaceFacade colaboradorFacade = null;
//    private AmbienteInterfaceFacade ambienteFacade = null;
//    private NivelTurmaInterfaceFacade nivelTurmaFacade = null;
//
//    public HorarioTurmaControle() throws Exception {
//        obterUsuarioLogado();
//        inicializarFacades();
//        setControleConsulta(new ControleConsulta());
//        setMensagemID("msg_entre_prmconsulta");
//    }
//
//    /**
//    * Rotina responsável por disponibilizar um novo objeto da classe <code>HorarioTurma</code>
//    * para edição pelo usuário da aplicação.
//    */
//    public String novo() {
//        setHorarioTurmaVO(new HorarioTurmaVO());
//        inicializarListasSelectItemTodosComboBox();
//        setMensagemID("msg_entre_dados");
//        return "editar";
//    }
//
//    /**
//    * Rotina responsável por disponibilizar os dados de um objeto da classe <code>HorarioTurma</code> para alteração.
//    * O objeto desta classe é disponibilizado na session da página (request) para que o JSP correspondente possa disponibilizá-lo para edição.
//    */
//    public String editar() {
//        HorarioTurmaVO obj = (HorarioTurmaVO)context().getExternalContext().getRequestMap().get("horarioTurma");
//        inicializarAtributosRelacionados(obj);
//        obj.setNovoObj(new Boolean(false));
//        setHorarioTurmaVO(obj);
//        inicializarListasSelectItemTodosComboBox();
//        setMensagemID("msg_dados_editar");
//        return "editar";
//    }
//
//    /**
//    * Método responsável inicializar objetos relacionados a classe <code>HorarioTurmaVO</code>.
//    * Esta inicialização é necessária por exigência da tecnologia JSF, que não trabalha com valores nulos para estes atributos.
//    */
//    public void inicializarAtributosRelacionados(HorarioTurmaVO obj) {
//        if (obj.getProfessor() == null) {
//            obj.setProfessor(new ColaboradorVO());
//        }
//        if (obj.getAmbiente() == null) {
//            obj.setAmbiente(new AmbienteVO());
//        }
//        if (obj.getNivelTurma() == null) {
//            obj.setNivelTurma(new NivelTurmaVO());
//        }
//    }
//
//    /**
//    * Rotina responsável por gravar no BD os dados editados de um novo objeto da classe <code>HorarioTurma</code>.
//    * Caso o objeto seja novo (ainda não gravado no BD) é acionado a operação <code>incluir()</code>. Caso contrário é acionado o <code>alterar()</code>.
//    * Se houver alguma inconsistência o objeto não é gravado, sendo re-apresentado para o usuário juntamente com uma mensagem de erro.
//    */
//    public String gravar() {
//        try {
//            if (horarioTurmaVO.isNovoObj().booleanValue()) {
//                horarioTurmaFacade.incluir(horarioTurmaVO);
//            } else {
//                horarioTurmaFacade.alterar(horarioTurmaVO);
//            }
//            setMensagemID("msg_dados_gravados");
//            return "editar";
//        } catch (Exception e) {
//            setMensagemDetalhada("msg_erro", e.getMessage());
//            return "editar";
//        }
//    }
//
//    /**
//    * Rotina responsavel por executar as consultas disponiveis no JSP HorarioTurmaCons.jsp.
//    * Define o tipo de consulta a ser executada, por meio de ComboBox denominado campoConsulta, disponivel neste mesmo JSP.
//    * Como resultado, disponibiliza um List com os objetos selecionados na sessao da pagina.
//    */
//    public String consultar() {
//        try {
//            super.consultar();
//            List objs = new ArrayList();
//            if (getControleConsulta().getCampoConsulta().equals("codigo")) {
//                if (getControleConsulta().getValorConsulta().equals("")) {
//                    getControleConsulta().setValorConsulta("0");
//                }
//                int valorInt = Integer.parseInt(getControleConsulta().getValorConsulta());
//                objs = horarioTurmaFacade.consultarPorCodigo(new Integer(valorInt), true, Uteis.NIVELMONTARDADOS_TODOS);
//            }
//            if (getControleConsulta().getCampoConsulta().equals("turma")) {
//                if (getControleConsulta().getValorConsulta().equals("")) {
//                    getControleConsulta().setValorConsulta("0");
//                }
//                int valorInt = Integer.parseInt(getControleConsulta().getValorConsulta());
//                objs = horarioTurmaFacade.consultarPorTurma(new Integer(valorInt), true, Uteis.NIVELMONTARDADOS_TODOS);
//            }
//            if (getControleConsulta().getCampoConsulta().equals("identificador")) {
//                objs = horarioTurmaFacade.consultarPorIdentificador(getControleConsulta().getValorConsulta(), true, Uteis.NIVELMONTARDADOS_TODOS);
//            }
//            if (getControleConsulta().getCampoConsulta().equals("horaInicial")) {
//                objs = horarioTurmaFacade.consultarPorHoraInicial(getControleConsulta().getValorConsulta(), true, Uteis.NIVELMONTARDADOS_TODOS);
//            }
//            if (getControleConsulta().getCampoConsulta().equals("situacaoColaborador")) {
//                objs = horarioTurmaFacade.consultarPorSituacaoColaborador(getControleConsulta().getValorConsulta(), Uteis.NIVELMONTARDADOS_TODOS);
//            }
//            if (getControleConsulta().getCampoConsulta().equals("descricaoAmbiente")) {
//                objs = horarioTurmaFacade.consultarPorDescricaoAmbiente(getControleConsulta().getValorConsulta(), Uteis.NIVELMONTARDADOS_TODOS);
//            }
//            if (getControleConsulta().getCampoConsulta().equals("descricaoNivelTurma")) {
//                objs = horarioTurmaFacade.consultarPorDescricaoNivelTurma(getControleConsulta().getValorConsulta(), Uteis.NIVELMONTARDADOS_TODOS);
//            }
//            if (getControleConsulta().getCampoConsulta().equals("situacao")) {
//                objs = horarioTurmaFacade.consultarPorSituacao(getControleConsulta().getValorConsulta(), true, Uteis.NIVELMONTARDADOS_TODOS);
//            }
//            objs = ControleConsulta.obterSubListPaginaApresentar(objs, controleConsulta);
//            definirVisibilidadeLinksNavegacao(controleConsulta.getPaginaAtual(), controleConsulta.getNrTotalPaginas());
//            setListaConsulta(objs);
//            setMensagemID("msg_dados_consultados");
//            return "consultar";
//        } catch (Exception e) {
//            setListaConsulta(new ArrayList());
//            setMensagemDetalhada("msg_erro", e.getMessage());
//            return "consultar";
//        }
//    }
//
//    /**
//     * Operação responsável por processar a exclusão um objeto da classe <code>HorarioTurmaVO</code>
//     * Após a exclusão ela automaticamente aciona a rotina para uma nova inclusão.
//     */
//    public String excluir() {
//        try {
//            horarioTurmaFacade.excluir(horarioTurmaVO);
//            setHorarioTurmaVO( new HorarioTurmaVO());
//            setMensagemID("msg_dados_excluidos");
//            return "editar";
//        } catch (Exception e) {
//            setMensagemDetalhada("msg_erro", e.getMessage());
//            return "editar";
//        }
//    }
//
//    public void irPaginaInicial() throws Exception{
//        controleConsulta.setPaginaAtual(1);
//        this.consultar();
//    }
//
//    public void irPaginaAnterior() throws Exception{
//        controleConsulta.setPaginaAtual(controleConsulta.getPaginaAtual() - 1);
//        this.consultar();
//    }
//
//    public void irPaginaPosterior() throws Exception{
//        controleConsulta.setPaginaAtual(controleConsulta.getPaginaAtual() + 1);
//        this.consultar();
//    }
//
//    public void irPaginaFinal() throws Exception{
//        controleConsulta.setPaginaAtual(controleConsulta.getNrTotalPaginas());
//        this.consultar();
//    }
//
//    /* Método responsável por inicializar List<SelectItem> de valores do 
//     * ComboBox correspondente ao atributo <code>situacao</code>
//     */ 
//    public List getListaSelectItemSituacaoHorarioTurma() throws Exception {
//        List objs = new ArrayList();
//        objs.add(new SelectItem("", ""));
//        Hashtable situacaoHorarioTurmas = (Hashtable)Dominios.getSituacaoHorarioTurma();
//        Enumeration keys = situacaoHorarioTurmas.keys();
//        while (keys.hasMoreElements()) {
//            String value = (String)keys.nextElement();
//            String label = (String)situacaoHorarioTurmas.get(value);
//            objs.add(new SelectItem( value, label));
//        }
//        SelectItemOrdemValor ordenador = new SelectItemOrdemValor();
//        Collections.sort((List) objs, ordenador);
//        return objs;
//    }
//
//    /**
//     * Método responsável por gerar uma lista de objetos do tipo <code>SelectItem</code> para preencher
//     * o comboBox relativo ao atributo <code>NivelTurma</code>.
//    */
//    public void montarListaSelectItemNivelTurma(String prm) throws Exception {
//        List resultadoConsulta = consultarNivelTurmaPorDescricao(prm);
//        Iterator i = resultadoConsulta.iterator();
//        List objs = new ArrayList();
//        objs.add(new SelectItem(new Integer(0), ""));
//        while (i.hasNext()) {
//            NivelTurmaVO obj = (NivelTurmaVO)i.next();
//            objs.add(new SelectItem(obj.getCodigo(), obj.getDescricao().toString()));
//        }
//        setListaSelectItemNivelTurma(objs);
//    }
//
//    /**
//     * Método responsável por atualizar o ComboBox relativo ao atributo <code>NivelTurma</code>.
//     * Buscando todos os objetos correspondentes a entidade <code>NivelTurma</code>. Esta rotina não recebe parâmetros para filtragem de dados, isto é
//     * importante para a inicialização dos dados da tela para o acionamento por meio requisições Ajax.
//    */
//    public void montarListaSelectItemNivelTurma() {
//        try {
//            montarListaSelectItemNivelTurma("");
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//    }
//
//    /**
//     * Método responsável por consultar dados da entidade <code><code> e montar o atributo <code>descricao</code>
//     * Este atributo é uma lista (<code>List</code>) utilizada para definir os valores a serem apresentados no ComboBox correspondente
//    */
//    public List consultarNivelTurmaPorDescricao(String descricaoPrm) throws Exception {
//        List lista = nivelTurmaFacade.consultarPorDescricao(descricaoPrm, false, Uteis.NIVELMONTARDADOS_TODOS);
//        return lista;
//    }
//
//    /**
//     * Método responsável por gerar uma lista de objetos do tipo <code>SelectItem</code> para preencher
//     * o comboBox relativo ao atributo <code>Ambiente</code>.
//    */
//    public void montarListaSelectItemAmbiente(String prm) throws Exception {
//        List resultadoConsulta = consultarAmbientePorDescricao(prm);
//        Iterator i = resultadoConsulta.iterator();
//        List objs = new ArrayList();
//        objs.add(new SelectItem(new Integer(0), ""));
//        while (i.hasNext()) {
//            AmbienteVO obj = (AmbienteVO)i.next();
//            objs.add(new SelectItem(obj.getCodigo(), obj.getDescricao().toString()));
//        }
//        setListaSelectItemAmbiente(objs);
//    }
//
//    /**
//     * Método responsável por atualizar o ComboBox relativo ao atributo <code>Ambiente</code>.
//     * Buscando todos os objetos correspondentes a entidade <code>Ambiente</code>. Esta rotina não recebe parâmetros para filtragem de dados, isto é
//     * importante para a inicialização dos dados da tela para o acionamento por meio requisições Ajax.
//    */
//    public void montarListaSelectItemAmbiente() {
//        try {
//            montarListaSelectItemAmbiente("");
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//    }
//
//    /**
//     * Método responsável por consultar dados da entidade <code><code> e montar o atributo <code>descricao</code>
//     * Este atributo é uma lista (<code>List</code>) utilizada para definir os valores a serem apresentados no ComboBox correspondente
//    */
//    public List consultarAmbientePorDescricao(String descricaoPrm) throws Exception {
//        List lista = ambienteFacade.consultarPorDescricao(descricaoPrm, false, Uteis.NIVELMONTARDADOS_TODOS);
//        return lista;
//    }
//
//    /**
//     * Método responsável por gerar uma lista de objetos do tipo <code>SelectItem</code> para preencher
//     * o comboBox relativo ao atributo <code>Professor</code>.
//    */
//    public void montarListaSelectItemProfessor(String prm) throws Exception {
//        List resultadoConsulta = consultarColaboradorPorSituacao(prm);
//        Iterator i = resultadoConsulta.iterator();
//        List objs = new ArrayList();
//        objs.add(new SelectItem(new Integer(0), ""));
//        while (i.hasNext()) {
//            ColaboradorVO obj = (ColaboradorVO)i.next();
//            objs.add(new SelectItem(obj.getCodigo(), obj.getSituacao().toString()));
//        }
//        setListaSelectItemProfessor(objs);
//    }
//
//    /**
//     * Método responsável por atualizar o ComboBox relativo ao atributo <code>Professor</code>.
//     * Buscando todos os objetos correspondentes a entidade <code>Colaborador</code>. Esta rotina não recebe parâmetros para filtragem de dados, isto é
//     * importante para a inicialização dos dados da tela para o acionamento por meio requisições Ajax.
//    */
//    public void montarListaSelectItemProfessor() {
//        try {
//            montarListaSelectItemProfessor("");
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//    }
//
//    /**
//     * Método responsável por consultar dados da entidade <code><code> e montar o atributo <code>situacao</code>
//     * Este atributo é uma lista (<code>List</code>) utilizada para definir os valores a serem apresentados no ComboBox correspondente
//    */
//    public List consultarColaboradorPorSituacao(String situacaoPrm) throws Exception {
//        List lista = colaboradorFacade.consultarPorSituacao(situacaoPrm, false, Uteis.NIVELMONTARDADOS_TODOS);
//        return lista;
//    }
//
//    /**
//     * Método responsável por inicializar a lista de valores (<code>SelectItem</code>) para todos os ComboBox's.
//    */
//    public void inicializarListasSelectItemTodosComboBox() {
//        montarListaSelectItemProfessor();
//        montarListaSelectItemAmbiente();
//        montarListaSelectItemNivelTurma();
//    }
//
//    /**
//    * Rotina responsável por preencher a combo de consulta da telas.
//    */
//    public List getTipoConsultaCombo() {
//        List itens = new ArrayList();
//        itens.add(new SelectItem("codigo", "Código"));
//        itens.add(new SelectItem("turma", "Turma"));
//        itens.add(new SelectItem("identificador", "Identificador"));
//        itens.add(new SelectItem("horaInicial", "Hora Inicial"));
//        itens.add(new SelectItem("situacaoColaborador", "Professor"));
//        itens.add(new SelectItem("descricaoAmbiente", "Ambiente"));
//        itens.add(new SelectItem("descricaoNivelTurma", "Nível da Turma"));
//        itens.add(new SelectItem("situacao", "Situação"));
//        return itens;
//    }
//
//    /**
//    * Rotina responsável por organizar a paginação entre as páginas resultantes de uma consulta.
//    */
//    public String inicializarConsultar() {
//        setPaginaAtualDeTodas("0/0");
//        setListaConsulta(new ArrayList());
//        definirVisibilidadeLinksNavegacao(0, 0);
//        setMensagemID("msg_entre_prmconsulta");
//        return "consultar";
//    }
//
//    /**
//    * Operação que inicializa as Interfaces Façades com os respectivos objetos de
//    * persistência dos dados no banco de dados. 
//    */
//    protected boolean inicializarFacades() {
//        try {
//            super.inicializarFacades();          
//            colaboradorFacade = new Colaborador();
//            ambienteFacade = new Ambiente();
//            nivelTurmaFacade = new NivelTurma();
//            return true;
//        } catch (Exception e) {
//            setMensagemDetalhada("msg_erro_conectarBD", e.getMessage());
//            return false;
//        }
//    }
//
//    public List getListaSelectItemNivelTurma() {
//        return (listaSelectItemNivelTurma);
//    }
//     
//    public void setListaSelectItemNivelTurma( List listaSelectItemNivelTurma ) {
//        this.listaSelectItemNivelTurma = listaSelectItemNivelTurma;
//    }
//
//    public List getListaSelectItemAmbiente() {
//        return (listaSelectItemAmbiente);
//    }
//     
//    public void setListaSelectItemAmbiente( List listaSelectItemAmbiente ) {
//        this.listaSelectItemAmbiente = listaSelectItemAmbiente;
//    }
//
//    public List getListaSelectItemProfessor() {
//        return (listaSelectItemProfessor);
//    }
//     
//    public void setListaSelectItemProfessor( List listaSelectItemProfessor ) {
//        this.listaSelectItemProfessor = listaSelectItemProfessor;
//    }
//
//    public HorarioTurmaVO getHorarioTurmaVO() {
//        return horarioTurmaVO;
//    }
//     
//    public void setHorarioTurmaVO(HorarioTurmaVO horarioTurmaVO) {
//        this.horarioTurmaVO = horarioTurmaVO;
//    }
//}
/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package controle.plano;

import br.com.pactosolucoes.ce.comuns.enumerador.Mes;
import br.com.pactosolucoes.comuns.util.Formatador;
import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.estrutura.exportador.controle.ExportadorListaControle;
import controle.arquitetura.SuperControle;
import controle.arquitetura.security.AutorizacaoFuncionalidadeControle;
import controle.arquitetura.security.AutorizacaoFuncionalidadeListener;
import controle.arquitetura.security.LoginControle;
import java.util.ArrayList;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;
import javax.faces.event.ActionEvent;
import javax.faces.model.SelectItem;

import controle.basico.clube.MensagemGenericaControle;
import negocio.comuns.basico.AmostraClienteTO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.LancamentoProdutoColetivoVO;
import negocio.comuns.basico.enumerador.TipoLancamentoProdutoColetivoEnum;
import negocio.comuns.plano.ModalidadeVO;
import negocio.comuns.plano.PlanoVO;
import negocio.comuns.plano.ProdutoVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;

/**
 *
 * <AUTHOR>
 */
public class LancamentoProdutoColetivoControle extends SuperControle {

    private LancamentoProdutoColetivoVO lancamento = new LancamentoProdutoColetivoVO();
    private List<SelectItem> tipos;
    private List<SelectItem> meses;
    private List<SelectItem> parcelas;
    private List<SelectItem> nrParcelas;
    private List<SelectItem> empresas;
    private List<AmostraClienteTO> listaAmostra = new ArrayList<AmostraClienteTO>();
    private Boolean lancarAoGravar = false;
    private Boolean ignorarJaLancados = true;
    private String msgAlert;

    public String novo() {
        lancamento = new LancamentoProdutoColetivoVO();
        lancarAoGravar = false;
        return "editar";
    }

    public void montarEmpresas() {
        try {
            empresas = new ArrayList<SelectItem>();
            List<EmpresaVO> empresasVO = getFacade().getEmpresa().consultarEmpresas();
            for (EmpresaVO emp : empresasVO) {
                empresas.add(new SelectItem(emp.getCodigo(), emp.getNome()));
            }
        } catch (Exception ex) {
            Logger.getLogger(LancamentoProdutoColetivoControle.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    public List<SelectItem> getTipos() {
        if (tipos == null) {
            tipos = JSFUtilities.getSelectItemListFromEnum(TipoLancamentoProdutoColetivoEnum.class, "descricao", false);
        }
        return tipos;
    }

    public List<SelectItem> getMeses() {
        if (meses == null) {
            meses = JSFUtilities.getSelectItemListFromEnum(Mes.class, "descricao", false);
        }
        return meses;
    }

    public List<SelectItem> getParcelas() {
        if (parcelas == null) {
            parcelas = JSFUtilities.listaDeNumerais(1, 12);
        }
        return parcelas;
    }

    public List<SelectItem> getNrParcelas() {
        if (nrParcelas == null) {
            nrParcelas = JSFUtilities.listaDeNumerais(1, 12);
        }
        return nrParcelas;
    }

    @SuppressWarnings("unchecked")
    public List<ProdutoVO> executarAutocompleteProduto(Object suggest) {
        List<ProdutoVO> result = new ArrayList<ProdutoVO>();
        List<String> tiposDesconsiderar = new ArrayList<String>();
        tiposDesconsiderar.add("TR");
        try {
            String pref = (String) suggest;
            result = getFacade().getProduto().consultarPorDescricaoDiferenteTipoProdutoAtivo(pref, tiposDesconsiderar, true,false,0, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
        } catch (Exception ex) {
            setMensagemDetalhada("msg_erro", ex.getMessage());
        }
        return result;
    }

    public String excluir() {
        try {
            getFacade().getLancamentoProdutoColetivo().excluir(getLancamento());
            incluirLogExclusao();
            setSucesso(true);
            setAtencao(false);
            montarSucesso("msg_dados_excluidos");
            novo();
            return "consultar";
        } catch (Exception e) {
            montarErro(e);
            if(e.getMessage().contains("ERRO: atualização ou exclusão em tabela \"lancamentoprodutocoletivo\" viola restrição de chave estrangeira") || e.getMessage().contains("ERROR: update or delete on table \"lancamentoprodutocoletivo\" violates foreign key")){
                setMensagemDetalhada("Este Lançamento de Produto Coletivo não pode ser excluído, pois está sendo utilizado!");
            }else {
                setMensagemDetalhada("msg_erro", e.getMessage());
            }
            return "editar";
        }
    }

    public String getDescricaoFiltros() throws Exception {
        limparCamposSugestion();
        if (UteisValidacao.emptyNumber(getLancamento().getProdutoVO().getCodigo())) {
            return "";
        }
        StringBuilder descricao = new StringBuilder("Lançar o produto ");
        descricao.append(getLancamento().getProdutoNome()).append(" no valor de ").append(Formatador.formatarValorMonetario(getLancamento().getValor()));
        descricao.append(" para todos os alunos ativos");
        if (!UteisValidacao.emptyNumber(getLancamento().getPlanoVO().getCodigo())) {
            descricao.append(" do plano ").append(getLancamento().getPlanoVO().getDescricao());
            if(getLancamento().getVigenciaContratoFim()!=null && getLancamento().getVigenciaContratoInicio()!=null) {
                descricao.append(" com Inicio em ").append(Uteis.getData(getLancamento().getVigenciaContratoFim()));
                descricao.append(" e Fim em ").append(Uteis.getData(getLancamento().getVigenciaContratoInicio()));
            }
        }
        if (!UteisValidacao.emptyNumber(getLancamento().getModalidadeVO().getCodigo())) {
            descricao.append(",  que tem a modalidade ").append(getLancamento().getModalidadeVO().getNome());
        }
        if (getLancamento().getTipoData()) {
            getLancamento().setDataEspecifica(getLancamento().getDataEspecifica() == null ? Calendario.hoje() : getLancamento().getDataEspecifica());
            descricao.append(", pro dia ").append(Uteis.getData(getLancamento().getDataEspecifica())).append(".");
        }
        if (getLancamento().getTipoMes() && getLancamento().getMes() != null) {
            descricao.append(", a cada mês de ").append(getLancamento().getMes().getDescricao()).append(".");
        }
        if (getLancamento().getTipoParcela()) {
            getLancamento().setParcela(UteisValidacao.emptyNumber(getLancamento().getParcela()) ? 1 : getLancamento().getParcela());
            descricao.append(", a cada parcela de número ").append(getLancamento().getParcela()).append(".");
        }
        if (getLancamento().getDataFim() != null) {
            descricao.append(" Válido até ").append(Uteis.getData(getLancamento().getDataEspecifica())).append(".");
        }
        if (lancarAoGravar && ignorarJaLancados) {
            descricao.append(" Ignorar alunos que já tem produto gerado por este lançamento.");
        }
        if(!UteisValidacao.emptyString(lancamento.getMatriculas())){
            descricao.append(" Somente para as matrículas informadas.");
        }
        return descricao.toString();
    }

    public void selecionarProdutoSuggestionBox() throws Exception {
        ProdutoVO produtoVO = (ProdutoVO) request().getAttribute("result");
        if (produtoVO != null) {
            lancamento.setProdutoVO(produtoVO);
            lancamento.setValor(produtoVO.getValorFinal());
        }
    }

    @SuppressWarnings("unchecked")
    public List<PlanoVO> executarAutocompletePlano(Object suggest) {
        List<PlanoVO> result = new ArrayList<PlanoVO>();
        try {
            String pref = (String) suggest;
            result = getFacade().getPlano().consultarPorDescricaoBolsaVigente(pref,true, lancamento.getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
        } catch (Exception ex) {
            setMensagemDetalhada("msg_erro", ex.getMessage());
        }
        return result;
    }

    public void selecionarPlanoSuggestionBox() throws Exception {
        PlanoVO plano = (PlanoVO) request().getAttribute("result");
        if (plano != null) {
            lancamento.setPlanoVO(plano);
        }
    }

    @SuppressWarnings("unchecked")
    public List<ModalidadeVO> executarAutocompleteModalidade(Object suggest) {
        List<ModalidadeVO> result = new ArrayList<ModalidadeVO>();
        try {
            String pref = (String) suggest;
            result = getFacade().getModalidade().consultarPorNome(pref, lancamento.getEmpresa().getCodigo(), true, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
        } catch (Exception ex) {
            setMensagemDetalhada("msg_erro", ex.getMessage());
        }
        return result;
    }

    public void selecionarModalidadeSuggestionBox() throws Exception {
        ModalidadeVO modalidade = (ModalidadeVO) request().getAttribute("result");
        if (modalidade != null) {
            lancamento.setModalidadeVO(modalidade);
        }
    }

    public void consultarAmostraClientes() {
        try {
            setMsgAlert("");
            limparCamposSugestion();
            listaAmostra = getFacade().getLancamentoProdutoColetivo().consultarAmostraLancamentoColetivo(lancamento, lancarAoGravar && ignorarJaLancados);
            setMsgAlert("Richfaces.showModalPanel('panelAmostraClientes');");
        } catch (Exception e) {
            montarErro(e);
            montarMsgAlert(getMensagemDetalhada());
        }
    }

    public void consultarJaAlcancados() {
        try {
            setMsgAlert("");
            limparCamposSugestion();
            listaAmostra = getFacade().getLancamentoProdutoColetivo().consultarJaAlcancadosLancamentoColetivo(lancamento);
            setMsgAlert("Richfaces.showModalPanel('panelAmostraClientes');");
        } catch (Exception e) {
            montarErro(e);
            montarMsgAlert(getMensagemDetalhada());
        }
    }

    public void limparCamposSugestion() throws Exception {
        if (!getUsuarioLogado().getAdministrador()) {
            getLancamento().setEmpresa(getEmpresaLogado());
        }
        limparCamposPlano();
        limparCamposProduto();
        limparCamposModalidade();
    }

    public void limparCamposProduto() throws Exception {
        if (UteisValidacao.emptyString(getLancamento().getProdutoVO().getDescricao())) {
            getLancamento().setProdutoVO(new ProdutoVO());
        }
    }

    public void limparCamposPlano() throws Exception {
        if (UteisValidacao.emptyString(getLancamento().getPlanoVO().getDescricao())) {
            getLancamento().setPlanoVO(new PlanoVO());
        }
    }

    public void limparCamposModalidade() throws Exception {
        if (UteisValidacao.emptyString(getLancamento().getModalidadeVO().getNome())) {
            getLancamento().setModalidadeVO(new ModalidadeVO());
        }
    }

    public void limparProduto() throws Exception {
        getLancamento().getProdutoVO().setDescricao("");
        limparCamposProduto();
    }

    public void limparPlano() throws Exception {
        getLancamento().getPlanoVO().setDescricao("");
        limparCamposPlano();
    }

    public void limparModalidade() throws Exception {
        getLancamento().getModalidadeVO().setNome("");
        limparCamposModalidade();
    }

    public void validarMatriculas() throws Exception{
        if(!UteisValidacao.emptyString(lancamento.getMatriculas())){
            if(lancamento.getMatriculas().trim().lastIndexOf(",") == (lancamento.getMatriculas().trim().length() - 1)){
                throw new Exception("Não adicione vírgula ao final.");
            }

            String[] split = lancamento.getMatriculas().trim().split("\\,");
            for(String s : split){
                try {
                    Integer.valueOf(s);
                }catch (Exception e){
                    throw new Exception("As matrículas devem conter apenas números e estarem separadas por vírgula, não adicione vírgula ao final.");
                }
            }
        }
    }

    public void gravar() {
        AutorizacaoFuncionalidadeControle auto = (AutorizacaoFuncionalidadeControle) getControlador(AutorizacaoFuncionalidadeControle.class);
        auto.setPedirPermissao(false);
        setMsgAlert("");
        try {
            validarMatriculas();
            AutorizacaoFuncionalidadeListener listener = new AutorizacaoFuncionalidadeListener() {

                @Override
                public void onAutorizacaoComSucesso() throws Exception {
                    limparCamposSugestion();

                    if(!UteisValidacao.emptyNumber(getLancamento().getPlanoVO().getCodigo()) &&
                            !getFacade().getPlano().planoDentroVigencia(getLancamento().getPlanoVO().getCodigo()))
                        throw new Exception("msg_err_plano_info_encontr_fora_vigencia");

                    getLancamento().setUsuario(getUsuarioLogado());
                    if (UteisValidacao.emptyNumber(getLancamento().getCodigo())) {
                        getFacade().getLancamentoProdutoColetivo().incluir(getLancamento());
                        incluirLogInclusao();
                    } else {
                        getFacade().getLancamentoProdutoColetivo().alterar(getLancamento());
                        incluirLogAlteracao();
                    }
                    montarSucesso("msg_dados_gravados");
                    if (lancarAoGravar) {
                        int lancarProdutos = getFacade().getLancamentoProdutoColetivo().lancarProdutos(lancamento, ignorarJaLancados);
                        montarMsgAlert(lancarProdutos + " " + getMensagemInternalizacao("msg_produtos_lancados_com_sucesso"));
                        lancamento.setJaFoiLancado(true);
                    }
                }

                @Override
                public void onAutorizacaoComErro(Exception e) {
                }

                @Override
                public String getExecutarAoCompletar() {
                    return getOnComplete();
                }
            };
            if (lancarAoGravar) {
                auto.autorizar("Lançamento de produto coletivo", "LancamentoProdutoColetivo",
                        "Você precisa da permissão \"Lançamento de produto coletivo\"",
                        "form", listener);
            } else {
                listener.onAutorizacaoComSucesso();
            }
        } catch (Exception e) {
            montarErro(e);
        }
    }

    public void estornar() {
        AutorizacaoFuncionalidadeControle auto = (AutorizacaoFuncionalidadeControle) getControlador(AutorizacaoFuncionalidadeControle.class);
        auto.setPedirPermissao(false);
        setMsgAlert("");
        AutorizacaoFuncionalidadeListener listener = new AutorizacaoFuncionalidadeListener() {

            @Override
            public void onAutorizacaoComSucesso() {
                try {
                    getFacade().getLancamentoProdutoColetivo().estornarProdutosAbertosLancadosColetivamente(lancamento, getUsuario());
                    montarMsgAlertID("msg_produtos_estornados_com_sucesso_log");
                } catch (Exception e) {
                    montarErro(e);
                    montarMsgAlert(getMensagemDetalhada());
                }
            }

            @Override
            public String getExecutarAoCompletar() {
                return getOnComplete();
            }
        };
        auto.autorizar("Estorno de produtos em aberto deste produto coletivo", "EstornoProdutoColetivo",
                "Você precisa da permissão \"Estorno de produtos em aberto deste produto coletivo\"",
                "form", listener);
    }

    public void realizarConsultaLogObjetoSelecionado() {
        LoginControle loginControle = (LoginControle) context().getExternalContext().getSessionMap().get("LoginControle");
        String nomeClasse = getLancamento().getClass().getSimpleName();
        nomeClasse = nomeClasse.substring(0, nomeClasse.length() - 2);
        loginControle.setNomeClasse(loginControle.getNomeEntidadeCamposLog("prt_" + nomeClasse + "_tituloForm"));
        loginControle.consultarLogObjetoSelecionado(nomeClasse.toUpperCase(), getLancamento().getCodigo(), 0);
    }
    
    public void realizarConsultaLogObjetoGeral() {
        lancamento = new LancamentoProdutoColetivoVO();
        realizarConsultaLogObjetoSelecionado();
    }

    public String editar() throws Exception {
        lancarAoGravar = false;
        Integer codigoConsulta = Integer.parseInt(request().getParameter("chavePrimaria"));
        novo();
        LancamentoProdutoColetivoVO obj = getFacade().getLancamentoProdutoColetivo().consultarPorChavePrimaria(codigoConsulta, Uteis.NIVELMONTARDADOS_TODOS);
        try {
            setLancamento(obj);
            getLancamento().setNovoObj(false);
            //analisa qual aba de recorrencia ou de duração deverá aparecer
            getLancamento().registrarObjetoVOAntesDaAlteracao();
        } catch (Exception e) {
            setErro(true);
            setSucesso(false);
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
        return "editar";
    }

    public String inicializarConsultar() {
        limparMsg();
        return "consultar";
    }

    public LancamentoProdutoColetivoVO getLancamento() {
        return lancamento;
    }

    public void setLancamento(LancamentoProdutoColetivoVO lancamento) {
        this.lancamento = lancamento;
    }

    public void setTipos(List<SelectItem> tipos) {
        this.tipos = tipos;
    }

    public List<SelectItem> getEmpresas() {
        if (empresas == null || empresas.isEmpty()) {
            montarEmpresas();
        }
        return empresas;
    }

    public void setEmpresas(List<SelectItem> empresas) {
        this.empresas = empresas;
    }

    public List<AmostraClienteTO> getListaAmostra() {
        return listaAmostra;
    }

    public void setListaAmostra(List<AmostraClienteTO> listaAmostra) {
        this.listaAmostra = listaAmostra;
    }

    public Boolean getLancarAoGravar() {
        return lancarAoGravar;
    }

    public void setLancarAoGravar(Boolean lancarAoGravar) {
        this.lancarAoGravar = lancarAoGravar;
    }

    public String getOnComplete() {//chamada implícita por 'AutorizacaoFuncionalidadeControle.control.onComplete'
        return getMsgAlert();
    }

    public Boolean getIgnorarJaLancados() {
        return ignorarJaLancados;
    }

    public void setIgnorarJaLancados(Boolean ignorarJaLancados) {
        this.ignorarJaLancados = ignorarJaLancados;
    }

    public void exportar(ActionEvent evt) throws Exception {
        ExportadorListaControle exportadorListaControle = (ExportadorListaControle) JSFUtilities.getFromRequest(ExportadorListaControle.class.getSimpleName());
        String paramsTableFiltrada = context().getExternalContext().getRequestParameterMap().get("paramsTableFiltrada");

        String[] split = paramsTableFiltrada.split(",");
        String campoOrdenacao = split[0].replace("[", "");
        String ordem = split[1];
        String filtro = split[2].replace("''", "");
        filtro = filtro.replace("]", "");
        List listaParaImpressao = getFacade().getLancamentoProdutoColetivo().consultarParaImpressao(filtro, ordem, campoOrdenacao, getEmpresaLogado() == null ? 0 : getEmpresaLogado().getCodigo());
        exportadorListaControle.exportar(evt, listaParaImpressao, filtro,null );
    }
    
    public void incluirLogInclusao() throws Exception {
        try {
            lancamento.setObjetoVOAntesAlteracao(new LancamentoProdutoColetivoVO());
            lancamento.setNovoObj(true);
            registrarLogObjetoVO(lancamento, lancamento.getCodigo(), "LANCAMENTOPRODUTOCOLETIVO", 0);
        } catch (Exception e) {
            registrarLogErroObjetoVO("LANCAMENTOPRODUTOCOLETIVO", lancamento.getCodigo(), "ERRO AO GERAR LOG DE INCLUSÃO DE LANCAMENTOPRODUTOCOLETIVO", this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
        lancamento.setNovoObj(new Boolean(false));
        lancamento.registrarObjetoVOAntesDaAlteracao();
    }
    
    public void incluirLogExclusao() throws Exception {
        try {
            lancamento.setObjetoVOAntesAlteracao(new LancamentoProdutoColetivoVO());
            lancamento.setNovoObj(true);
            registrarLogExclusaoTodosDadosObjetoVO(lancamento, lancamento.getCodigo(), "LANCAMENTOPRODUTOCOLETIVO", 0);
        } catch (Exception e) {
            registrarLogErroObjetoVO("LANCAMENTOPRODUTOCOLETIVO", lancamento.getCodigo(), "ERRO AO GERAR LOG DE EXCLUSÃO DE LANCAMENTOPRODUTOCOLETIVO ", this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
    }

    /**
     * Inclui o log de alteração de categoria
     * @throws Exception
     */
    public void incluirLogAlteracao() throws Exception {
        try {
            registrarLogObjetoVO(lancamento, lancamento.getCodigo(), "LANCAMENTOPRODUTOCOLETIVO", 0);
            lancamento.registrarObjetoVOAntesDaAlteracao();
        } catch (Exception e) {
            registrarLogErroObjetoVO("LANCAMENTOPRODUTOCOLETIVO", lancamento.getCodigo(), "LANCAMENTOPRODUTOCOLETIVO ", this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
        lancamento.registrarObjetoVOAntesDaAlteracao();
    }

    public void setMsgAlert(String msgAlert) {
        this.msgAlert = msgAlert;
    }

    public String getMsgAlert() {
        if (msgAlert == null) {
            return "";
        }
        return msgAlert;
    }

    public void confirmarExcluir(){
        MensagemGenericaControle control = (MensagemGenericaControle) getControlador(MensagemGenericaControle.class);
        control.setMensagemDetalhada("", "");
        setMsgAlert("Richfaces.showModalPanel('mdlMensagemGenerica');");
        control.init("Exclusão de Lançamento de Produto Coletivo",
                "Deseja excluir o Lançamento de Produto Coletivo?",
                this, "excluir", "", "", "", "grupoBtnExcluir");
    }

}

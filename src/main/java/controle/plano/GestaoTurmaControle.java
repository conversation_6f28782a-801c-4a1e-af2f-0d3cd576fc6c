package controle.plano;

import br.com.pactosolucoes.comuns.notificacao.RecursoSistema;
import br.com.pactosolucoes.enumeradores.TipoManutencaoTurmaEnum;
import br.com.pactosolucoes.enumeradores.TipoColaboradorEnum;
import controle.arquitetura.FuncionalidadeSistemaEnum;
import negocio.comuns.arquitetura.LogVO;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.contrato.MatriculaAlunoHorarioTurmaVO;
import negocio.comuns.plano.ConsultarAlunosTurmaVO;
import negocio.comuns.plano.HorarioTurmaVO;
import negocio.comuns.plano.ModalidadeVO;
import negocio.comuns.plano.TurmaVO;
import negocio.comuns.utilitarias.*;
import negocio.comuns.utilitarias.validator.PredicadoHorarioTurma;
import negocio.interfaces.arquitetura.LogInterfaceFacade;
import org.apache.commons.collections4.IterableUtils;
import relatorio.controle.arquitetura.SuperControleRelatorio;

import javax.faces.model.SelectItem;
import java.util.ArrayList;
import java.util.List;

public class GestaoTurmaControle extends SuperControleRelatorio {


    private List<HorarioTurmaVO> listaHorarioTurma;
    private List<HorarioTurmaVO> horariosSelecionados;
    private boolean marcarTodosHorario = false;
    private List<SelectItem> selectItensEmpresa;
    private List<SelectItem> selectItensTurma;
    private List<SelectItem> selectItensProfessor;
    private List<SelectItem> selectItensProfReceberHorarios;
    private List<SelectItem> selectItensModalidade;
    private int turmaSelecionada;
    private int professorSelecionado;
    private int modalidadeSelecionada;
    private int profReceberHorariosSelecionado;
    private String situacaoSelecionada = "AT";
    private List<HorarioTurmaVO> horariosConflitantes = new ArrayList<HorarioTurmaVO>();
    private List<HorarioTurmaVO> horariosComConflito = new ArrayList<HorarioTurmaVO>();
    private List<HorarioTurmaVO> horariosSemConflito = new ArrayList<HorarioTurmaVO>();
    private HorarioTurmaVO horarioTurmaVOTemporaria = new HorarioTurmaVO();
    private HorarioTurmaVO horarioTurmaVO;
    private boolean transferenciaOK = false;
    private boolean turmaVigente = true;
    private boolean turmaNaoVigente = false;

    private Integer tipoManutencaoTurma;
    private Integer modalidadeTransferencia;

    private Integer turmaOrigem;
    private Integer horarioOrigem;
    private List<SelectItem> listaTurmaOrigem;
    private List<SelectItem> listaHorarioTurmaOrigem;

    private Integer turmaDestino;
    private Integer horarioDestino;
    private List<SelectItem> listaTurmaDestino;
    private List<SelectItem> listaHorarioTurmaDestino;

    private List<MatriculaAlunoHorarioTurmaVO> alunos;
    private List<ConsultarAlunosTurmaVO> alunosTurma;
    private boolean selecionarTodos = false;
    private String onComplete;
    private List<ConsultarAlunosTurmaVO> alunosTransferir;
    private String retornoTransferencia;
    private boolean apresentarTransferir = false;


    private void inicializarDados() {
        limparDados();
        montarTurmaSelectItem();
        montarModalidadeSelectItem();
        montarProfessorSelectItem();
        montarProfReceberHorariosSelectItem();
    }

    private void limparDados() {
        setTurmaSelecionada(0);
        setProfessorSelecionado(0);
        setModalidadeSelecionada(0);
        setSituacaoSelecionada("AT");
        setListaHorarioTurma(null);
        setListaConsulta(null);
        setMarcarTodosHorario(false);
        setProfReceberHorariosSelecionado(0);
        setHorariosSelecionados(null);
        setTransferenciaOK(false);
        setTurmaVigente(true);
    }

    public void atualizaSituacao() {
        if (getSituacaoSelecionada().equals("AT")) {
            setSituacaoSelecionada("IN");
            setTurmaVigente(false);
            setTurmaNaoVigente(true);
        } else {
            setSituacaoSelecionada("AT");
            setTurmaVigente(true);
            setTurmaNaoVigente(false);
        }

        obterDadosGestaoTurma();
    }

    public void validarHorario() throws Exception {
        montarMensagemExceptionControle("msg_erro", null);
        setMensagemDetalhada("msg_erro", "");
        setSucesso(true);
        setErro(false);

        try {

            //Seta o colaborador informado
            if (getHorarioTurmaVOTemporaria().getProfessor().getCodigo() != 0) {
                ColaboradorVO colaboradorVO = getFacade().getColaborador().consultarPorChavePrimaria(getProfReceberHorariosSelecionado(), Uteis.NIVELMONTARDADOS_TODOS);
                getHorarioTurmaVOTemporaria().setProfessor(colaboradorVO);
            }

            if (getHorarioTurmaVOTemporaria().getCodigo() != 0) {
                validarExistenciaRegistroHorarioTurma(getHorarioTurmaVOTemporaria());
            }

            if (getHorariosComConflito().size() == 0) {
                transferenciaOK = true;
            } else {
                transferenciaOK = false;
            }

        } catch (Exception e) {
            montarMensagemExceptionControle("msg_erro", e);
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
        }
    }

    public void recarregarDestruirObjetos() throws Exception {

        obterDadosGestaoTurma();

        //Destruir objetos.
        limparCheckBox();
        setHorarioTurmaVO(null);
        setHorarioTurmaVOTemporaria(null);
        setHorariosSemConflito(null);
        setHorariosComConflito(null);
        setProfReceberHorariosSelecionado(0);
        setTransferenciaOK(false);

    }

    public void btnVoltar() throws Exception {

        recarregarDestruirObjetos();

    }

    public void btnApenasSemConflitos() throws Exception {
        notificarRecursoEmpresa(RecursoSistema.TRANSFERIR_TURMA_PROFESSOR_GESTAO_TURMA_SEM_CONFLITO);
        for (HorarioTurmaVO horarioSemConfli : getHorariosSemConflito()) {
            setMarcarTodosHorario(false);
            horarioSemConfli.setHorarioSelecionado(false);
            gravarHorario(horarioSemConfli);

        }
        recarregarDestruirObjetos();

    }

    public void btnTransferirMesmoAssim() throws Exception {
        List<HorarioTurmaVO> todos = new ArrayList<HorarioTurmaVO>();

        for (HorarioTurmaVO horarioConflito : getHorariosComConflito()) {
            todos.add(horarioConflito);
        }

        for (HorarioTurmaVO horarioSemConflito : getHorariosSemConflito()) {
            todos.add(horarioSemConflito);
        }

        for (HorarioTurmaVO gravarTodosHorarios : todos) {
            setMarcarTodosHorario(false);
            gravarTodosHorarios.setHorarioSelecionado(false);
            gravarHorario(gravarTodosHorarios);
        }

        recarregarDestruirObjetos();
    }

    public void gravarHorario(HorarioTurmaVO htv) throws Exception {
        getFacade().getHorarioTurma().alterarHorariosGestaoTurma(htv);
        trocarHistorico(htv.getTurma());
        montarMensagemExceptionControle("msg_dados_gravados", null);
        try {
            horarioTurmaVO.setUsuarioVO(getEmpresaLogado().getUsuarioVO());
            incluirLogDescricao(htv);
        } catch (Exception e) {
            registrarLogErroObjetoVO("TURMA", horarioTurmaVO.getCodigo(), "ERRO AO GERAR LOG DE ALTERAÇÃO DE HORÁRIO TURMA", this.getUsuarioLogado().getNome(), this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }

        setSucesso(true);
        setErro(false);
    }

    public void incluirLogDescricao(HorarioTurmaVO horario) throws Exception {

        LogInterfaceFacade logFacade = getFacade().getLog();

        LogVO logDescricao = new LogVO();
        logDescricao.setChavePrimaria(horario.getTurma().toString());
        logDescricao.setOperacao("GESTÃO DE TURMA - TRANSFERÊNCIA");
        logDescricao.setNomeEntidade("TURMA");
        logDescricao.setNomeEntidadeDescricao("Turma");
        logDescricao.setPessoa(0);
        logDescricao.setResponsavelAlteracao(getUsuarioLogado().getNome());
        logDescricao.setUserOAMD(getUsuarioLogado().getUserOamd());
        //LOG DESCRIÇÃO TURMA TURMA CODIGO
        logDescricao.setNomeCampo("*Horario código:");
        logDescricao.setValorCampoAnterior(horario.getCodigo().toString());
        logDescricao.setValorCampoAlterado(horario.getCodigo().toString());
        logFacade.incluirSemCommit(logDescricao);
        //LOG DESCRIÇÃO TURMA TURMA PROFESSOR
        logDescricao.setNomeCampo("*Professor:");
        logDescricao.setValorCampoAnterior("Código: " + horario.getProfessorAnterior());
        logDescricao.setValorCampoAlterado(horario.getProfessor().getPessoa_Apresentar());
        logFacade.incluirSemCommit(logDescricao);
    }

    public void trocarHistorico(int codigoTurma) throws Exception {
        try {
            TurmaVO turma = (TurmaVO) getFacade().getTurma().consultarPorCodigo(codigoTurma, getEmpresaLogado().getCodigo(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            boolean trocaDeProfessor = !getHorarioTurmaVOTemporaria().getProfessor().getCodigo()
                    .equals(getHorarioTurmaVO().getProfessor().getCodigo());
            if (trocaDeProfessor) {
                getFacade().getHpService().inserirHistoricoMudancaProfessor(getHorarioTurmaVOTemporaria(), getHorarioTurmaVO().getProfessor().getCodigo(),
                        turma.getDataInicialVigencia());
            }
        } catch (Exception e) {
            //
        }

    }

    private void validarExistenciaRegistroHorarioTurma(HorarioTurmaVO htVO) throws Exception {
        List<HorarioTurmaVO> horariosTurma = getFacade().getHorarioTurma().consultarHorariosConflitantesParaProfessorSemAmbiente(htVO, getEmpresaLogado().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS);
        if (horariosTurma.size() > 0) {
            getHorariosComConflito().add(htVO);
        } else {
            getHorariosSemConflito().add(htVO);
        }
    }

    public void btnTransfere() throws Exception {
        notificarRecursoEmpresa(RecursoSistema.TRANSFERIR_TURMA_PROFESSOR_GESTAO_TURMA);
        if (isTransferenciaOK() && getHorariosComConflito().size() == 0) {
            for (HorarioTurmaVO horariosTransfere : getHorariosSelecionados()) {
                gravarHorario(horariosTransfere);
            }
            recarregarDestruirObjetos();
        }
    }

    public void transferirHorarios() {
        try {

            //Criando a lista de horários que irão ser inseridos
            List<HorarioTurmaVO> horarioTurmaVOsCriados = getHorariosSelecionados();

            for (HorarioTurmaVO htvo : horarioTurmaVOsCriados) {
                setHorarioTurmaVO(htvo);
                setHorarioTurmaVOTemporaria(getHorarioTurmaVO());
                validarHorario();
            }

        } catch (Exception e) {
            e.getStackTrace();
        }

    }

    public void obterDadosGestaoTurma() {
        try {
            limparCheckBox();

            if (!(getTurmaSelecionada() == 0 && getProfessorSelecionado() == 0 && getModalidadeSelecionada() == 0)) {
                setListaHorarioTurma(getFacade().getHorarioTurma().consultarGestaoTurma(isTurmaVigente(), isTurmaNaoVigente(), getEmpresaLogado().getCodigo(), getTurmaSelecionada(), getProfessorSelecionado(), getModalidadeSelecionada(), Uteis.NIVELMONTARDADOS_TODOS));
                setListaConsulta(getListaHorarioTurma());
            } else {
                setListaHorarioTurma(null);
            }

            montarModalidadeSelectItem();
            montarTurmaSelectItem();
            montarProfessorSelectItem();
        } catch (Exception e) {
            e.getStackTrace();
        }
    }

    private void limparCheckBox() {
        marcarTodosHorario = false;
        setHorariosSelecionados(null);
    }

    private void montarTurmaSelectItem() {
        List<TurmaVO> turmas = new ArrayList<TurmaVO>();
        this.selectItensTurma = new ArrayList<SelectItem>();

        try {
            if (getModalidadeSelecionada() != 0 || getProfessorSelecionado() != 0) {
                turmas = getFacade().getTurma().consultarPorCodigoModalidadeEOuProfessor(isTurmaVigente(), isTurmaNaoVigente(), getModalidadeSelecionada(), getProfessorSelecionado(), getEmpresaLogado().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            } else {
                turmas = getFacade().getTurma().consultar(isTurmaVigente(), false, 0, getEmpresaLogado().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS, isTurmaNaoVigente());
            }

            for (TurmaVO turma : turmas) {
                this.selectItensTurma.add(new SelectItem(turma.getCodigo().intValue(), turma.getIdentificador()));
            }

            this.selectItensTurma = Ordenacao.ordenarLista(selectItensTurma, "label");
            this.selectItensTurma.add(0, new SelectItem(0, "Id. Turma"));
        } catch (Exception e) {
            e.getStackTrace();
            this.selectItensTurma = new ArrayList<SelectItem>();
            this.selectItensTurma.add(new SelectItem(0, "Id. Turma"));
        }
    }

    private void montarProfessorSelectItem() {
        List<ColaboradorVO> professores = new ArrayList<ColaboradorVO>();
        this.selectItensProfessor = new ArrayList<SelectItem>();
        try {

            if (getModalidadeSelecionada() != 0 || getTurmaSelecionada() != 0) {
                professores = getFacade().getColaborador().consultarPorCodigoTurmaEOuModalidade(getTurmaSelecionada(), getModalidadeSelecionada(), getEmpresaLogado().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);
            } else {
                professores = getFacade().getColaborador().consultarPorTipoColaboradorAtivo(TipoColaboradorEnum.PROFESSOR, "AT", getEmpresaLogado().getCodigo(), false, Uteis.NIVELMONTARDADOS_TODOS);
            }

            for (ColaboradorVO prof : professores) {
                this.selectItensProfessor.add(new SelectItem(prof.getCodigo().intValue(), prof.getPessoa().getNomeAbreviado()));
            }

            this.selectItensProfessor = Ordenacao.ordenarLista(selectItensProfessor, "label");
            this.selectItensProfessor.add(0, new SelectItem(0, "Professor"));
        } catch (Exception e) {
            e.getStackTrace();
            this.selectItensProfessor = new ArrayList<SelectItem>();
            this.selectItensProfessor.add(new SelectItem(0, "Professor"));
        }
    }

    private void montarModalidadeSelectItem() {
        List<ModalidadeVO> modalidades = new ArrayList<ModalidadeVO>();
        this.selectItensModalidade = new ArrayList<SelectItem>();

        try {
            if (getProfessorSelecionado() != 0 || getTurmaSelecionada() != 0) {
                modalidades = getFacade().getModalidade().consultarPorCodigoTurmaEOuProfessor(getTurmaSelecionada(), getProfessorSelecionado(), getEmpresaLogado().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);
            } else {
                modalidades = getFacade().getModalidade().consultarTodasModalidadesComLimite(getEmpresaLogado().getCodigo(), true, true);
            }
            for (ModalidadeVO modalidade : modalidades) {
                this.selectItensModalidade.add(new SelectItem(modalidade.getCodigo().intValue(), modalidade.getNome()));
            }

            this.selectItensModalidade = Ordenacao.ordenarLista(selectItensModalidade, "label");
            this.selectItensModalidade.add(0, new SelectItem(0, "Modalidade"));
        } catch (Exception e) {
            e.getStackTrace();
            this.selectItensModalidade = new ArrayList<SelectItem>();
            this.selectItensModalidade.add(new SelectItem(0, "Modalidade"));
        }
    }

    public void checkHorario() {
        HorarioTurmaVO horarioTurm = (HorarioTurmaVO) context().getExternalContext().getRequestMap().get("horarioTurma");
        HorarioTurmaVO selecionado = IterableUtils.find(getHorariosSelecionados(), new PredicadoHorarioTurma(horarioTurm.getCodigo()));
        //sempre remover pra nunca ter a possibilidade de duplicar
        if (selecionado != null) {
            getHorariosSelecionados().remove(selecionado);
        }
        //adicionar se selecionado
        if (horarioTurm.isHorarioSelecionado()) {
            getHorariosSelecionados().add(horarioTurm);
        } else if (marcarTodosHorario) {
            try {
                setHorariosSelecionados(getFacade().getHorarioTurma().consultarGestaoTurma(isTurmaVigente(), isTurmaNaoVigente(), getEmpresaLogado().getCodigo(), getTurmaSelecionada(), getProfessorSelecionado(), getModalidadeSelecionada(), Uteis.NIVELMONTARDADOS_TODOS));
                selecionado = IterableUtils.find(getHorariosSelecionados(), new PredicadoHorarioTurma(horarioTurm.getCodigo()));
                if (selecionado != null) {
                    getHorariosSelecionados().remove(selecionado);
                }
                marcarTodosHorario = false;
            } catch (Exception e) {
                montarErro(e);
            }

        }
    }

    private void montarProfReceberHorariosSelectItem() {
        List<ColaboradorVO> professoresReceber = new ArrayList<ColaboradorVO>();
        this.selectItensProfReceberHorarios = new ArrayList<SelectItem>();

        try {
            professoresReceber = getFacade().getColaborador().consultarPorTipoColaboradorAtivo(TipoColaboradorEnum.PROFESSOR, "AT", getEmpresaLogado().getCodigo(), false, Uteis.NIVELMONTARDADOS_TODOS);

            for (ColaboradorVO prof : professoresReceber) {
                this.selectItensProfReceberHorarios.add(new SelectItem(prof.getCodigo().intValue(), prof.getPessoa().getNomeAbreviado()));
            }

            this.selectItensProfReceberHorarios = Ordenacao.ordenarLista(selectItensProfReceberHorarios, "label");
            this.selectItensProfReceberHorarios.add(0, new SelectItem(0, "SELECIONE UM PROFESSOR"));
        } catch (Exception e) {
            e.getStackTrace();
            this.selectItensProfReceberHorarios = new ArrayList<SelectItem>();
            this.selectItensProfReceberHorarios.add(new SelectItem(0, "PROFESSOR"));
        }
    }

    public void marcarTodosHorarios() {
        try {

            for (Object obj : listaConsulta) {
                HorarioTurmaVO horarioTurma = (HorarioTurmaVO) obj;
                horarioTurma.setHorarioSelecionado(marcarTodosHorario);
                getHorariosSelecionados().add(horarioTurma);
            }

            if (marcarTodosHorario == false) {
                setHorariosSelecionados(new ArrayList<HorarioTurmaVO>());
            }

        } catch (Exception e) {
            setMsgAlert("Não existe horario para ser selecionado.");
        }
    }

    public String abrirGestaoTurma() {
        setTipoManutencaoTurma(TipoManutencaoTurmaEnum.PROFESSOR.getCodigo());
        notificarRecursoEmpresa(RecursoSistema.fromDescricao(FuncionalidadeSistemaEnum.GESTAO_DE_TURMA.toString()));
        inicializarDados();
        return "gestaoTurma";
    }

    public List<HorarioTurmaVO> getListaHorarioTurma() {
        if (listaHorarioTurma == null) {
            listaHorarioTurma = new ArrayList<HorarioTurmaVO>();
        }
        return listaHorarioTurma;
    }

    public String getListaHorarioTurmaSize() {
        return String.valueOf(getListaHorarioTurma().size());
    }

    public String getListaConsultaSize() {
        if (listaConsulta == null) {
            return "0";
        } else {
            return String.valueOf(getListaConsulta().size());
        }
    }

    public String getHorariosSelecionadosSize() {
        return String.valueOf(getHorariosSelecionados().size());
    }

    public void setListaHorarioTurma(List<HorarioTurmaVO> listaHorarioTurma) {
        this.listaHorarioTurma = listaHorarioTurma;
    }

    public List<HorarioTurmaVO> getHorariosSelecionados() {
        if (horariosSelecionados == null) {
            horariosSelecionados = new ArrayList<HorarioTurmaVO>();
        }
        return horariosSelecionados;
    }

    public void setHorariosSelecionados(List<HorarioTurmaVO> horariosSelecionados) {
        this.horariosSelecionados = horariosSelecionados;
    }

    public boolean isMarcarTodosHorario() {
        return marcarTodosHorario;
    }

    public void setMarcarTodosHorario(boolean marcarTodosHorario) {
        this.marcarTodosHorario = marcarTodosHorario;
    }

    public List<SelectItem> getSelectItensTurma() {
        return selectItensTurma;
    }

    public void setSelectItensTurma(List<SelectItem> selectItensTurma) {
        this.selectItensTurma = selectItensTurma;
    }

    public List<SelectItem> getSelectItensProfessor() {
        return selectItensProfessor;
    }

    public void setSelectItensProfessor(List<SelectItem> selectItensProfessor) {
        this.selectItensProfessor = selectItensProfessor;
    }

    public List<SelectItem> getSelectItensModalidade() {
        return selectItensModalidade;
    }

    public void setSelectItensModalidade(List<SelectItem> selectItensModalidade) {
        this.selectItensModalidade = selectItensModalidade;
    }

    public int getTurmaSelecionada() {
        return turmaSelecionada;
    }

    public void setTurmaSelecionada(int turmaSelecionada) {
        this.turmaSelecionada = turmaSelecionada;
    }

    public int getProfessorSelecionado() {
        return professorSelecionado;
    }

    public void setProfessorSelecionado(int professorSelecionado) {
        this.professorSelecionado = professorSelecionado;
    }

    public int getModalidadeSelecionada() {
        return modalidadeSelecionada;
    }

    public void setModalidadeSelecionada(int modalidadeSelecionada) {
        this.modalidadeSelecionada = modalidadeSelecionada;
    }

    public String getSituacaoSelecionada() {
        return situacaoSelecionada;
    }

    public void setSituacaoSelecionada(String situacaoSelecionada) {
        this.situacaoSelecionada = situacaoSelecionada;
    }

    public List<SelectItem> getSelectItensProfReceberHorarios() {
        if (selectItensProfReceberHorarios == null) {
            selectItensProfReceberHorarios = new ArrayList<SelectItem>();
        }
        return selectItensProfReceberHorarios;
    }

    public void setSelectItensProfReceberHorarios(List<SelectItem> selectItensProfReceberHorarios) {
        this.selectItensProfReceberHorarios = selectItensProfReceberHorarios;
    }

    public int getProfReceberHorariosSelecionado() {
        return profReceberHorariosSelecionado;
    }

    public void setProfReceberHorariosSelecionado(int profReceberHorariosSelecionado) {
        this.profReceberHorariosSelecionado = profReceberHorariosSelecionado;
    }

    public HorarioTurmaVO getHorarioTurmaVOTemporaria() {
        return horarioTurmaVOTemporaria;
    }

    public void setHorarioTurmaVOTemporaria(HorarioTurmaVO horarioTurmaVOTemporaria) {
        this.horarioTurmaVOTemporaria = horarioTurmaVOTemporaria;
    }

    public HorarioTurmaVO getHorarioTurmaVO() {
        return horarioTurmaVO;
    }

    public void setHorarioTurmaVO(HorarioTurmaVO horarioTurmaVO) {
        this.horarioTurmaVO = horarioTurmaVO;
    }

    public String getStringHorariosConflitantesSize() {
        return "Este professor possui " + getHorariosComConflitoSize() + " horarios em conflito. O que deseja fazer?";
    }

    public int getHorariosComConflitoSize() {
        if (getHorariosComConflito() == null) {
            setHorariosComConflito(new ArrayList<HorarioTurmaVO>());
        }
        return getHorariosComConflito().size();
    }

    public List<HorarioTurmaVO> getHorariosSemConflito() {
        if (horariosSemConflito == null) {
            horariosSemConflito = new ArrayList<HorarioTurmaVO>();
        }
        return horariosSemConflito;
    }

    public void setHorariosSemConflito(List<HorarioTurmaVO> horariosSemConflito) {
        this.horariosSemConflito = horariosSemConflito;
    }

    public List<HorarioTurmaVO> getHorariosComConflito() {
        return horariosComConflito;
    }

    public void setHorariosComConflito(List<HorarioTurmaVO> horariosComConflito) {
        this.horariosComConflito = horariosComConflito;
    }

    public boolean isTransferenciaOK() {
        return transferenciaOK;
    }

    public void setTransferenciaOK(boolean transferenciaOK) {
        this.transferenciaOK = transferenciaOK;
    }

    public boolean isTurmaVigente() {
        return turmaVigente;
    }

    public void setTurmaVigente(boolean turmaVigente) {
        this.turmaVigente = turmaVigente;
    }

    public boolean isTurmaNaoVigente() {
        return turmaNaoVigente;
    }

    public void setTurmaNaoVigente(boolean turmaNaoVigente) {
        this.turmaNaoVigente = turmaNaoVigente;
    }

    public List<SelectItem> getSelectItemTipoManutencaoTurma() {
        try {
            List<SelectItem> lista = new ArrayList<SelectItem>();
            for (TipoManutencaoTurmaEnum tipo : TipoManutencaoTurmaEnum.values()) {
                if (!tipo.equals(TipoManutencaoTurmaEnum.NENHUM) && !tipo.equals(TipoManutencaoTurmaEnum.REMOVER_ALUNOS)) {
                    lista.add(new SelectItem(tipo.getCodigo(), tipo.getDescricao()));
                }
            }
            return lista;
        } catch (Exception e) {
            return new ArrayList<SelectItem>();
        }
    }

    public Integer getTipoManutencaoTurma() {
        if (tipoManutencaoTurma == null) {
            tipoManutencaoTurma = 0;
        }
        return tipoManutencaoTurma;
    }

    public void setTipoManutencaoTurma(Integer tipoManutencaoTurma) {
        this.tipoManutencaoTurma = tipoManutencaoTurma;
    }

    public boolean isManutencaoProfessor() {
        return getTipoManutencaoTurma().equals(TipoManutencaoTurmaEnum.PROFESSOR.getCodigo());
    }

    public boolean isManutencaoAlunos() {
        return getTipoManutencaoTurma().equals(TipoManutencaoTurmaEnum.REMOVER_ALUNOS.getCodigo());
    }

    public boolean isManutencaoTransferencia() {
        return getTipoManutencaoTurma().equals(TipoManutencaoTurmaEnum.TRANSFERIR_ALUNOS.getCodigo());
    }

    public Integer getTurmaDestino() {
        if (turmaDestino == null) {
            turmaDestino = 0;
        }
        return turmaDestino;
    }

    public void setTurmaDestino(Integer turmaDestino) {
        this.turmaDestino = turmaDestino;
    }

    public Integer getTurmaOrigem() {
        if (turmaOrigem == null) {
            turmaOrigem = 0;
        }
        return turmaOrigem;
    }

    public void setTurmaOrigem(Integer turmaOrigem) {
        this.turmaOrigem = turmaOrigem;
    }

    public Integer getModalidadeTransferencia() {
        if (modalidadeTransferencia == null) {
            modalidadeTransferencia = 0;
        }
        return modalidadeTransferencia;
    }

    public void setModalidadeTransferencia(Integer modalidadeTransferencia) {
        this.modalidadeTransferencia = modalidadeTransferencia;
    }

    public Integer getHorarioOrigem() {
        if (horarioOrigem == null) {
            horarioOrigem = 0;
        }
        return horarioOrigem;
    }

    public void setHorarioOrigem(Integer horarioOrigem) {
        this.horarioOrigem = horarioOrigem;
    }

    public Integer getHorarioDestino() {
        if (horarioDestino == null) {
            horarioDestino = 0;
        }
        return horarioDestino;
    }

    public void setHorarioDestino(Integer horarioDestino) {
        this.horarioDestino = horarioDestino;
    }

    public void alterarTipoManutencao() {
        if (isManutencaoTransferencia()) {
            setModalidadeTransferencia(0);
            setAlunosTurma(new ArrayList<ConsultarAlunosTurmaVO>());
            setTurmaOrigem(0);
            setHorarioOrigem(0);
            setTurmaDestino(0);
            setHorarioDestino(0);
            setListaTurmaOrigem(new ArrayList<SelectItem>());
            setListaTurmaDestino(new ArrayList<SelectItem>());
            setListaHorarioTurmaOrigem(new ArrayList<SelectItem>());
            setListaHorarioTurmaDestino(new ArrayList<SelectItem>());
            montarModalidade();
        } else if (isManutencaoAlunos()) {
            montarModalidade();
            setAlunosTurma(new ArrayList<ConsultarAlunosTurmaVO>());
            setTurmaOrigem(0);
            setHorarioOrigem(0);
        } else {
            inicializarDados();
        }
    }

    public void consultarAlunos() {
        try {
            setOnComplete("");
            limparMsg();
            setAlunosTurma(new ArrayList<ConsultarAlunosTurmaVO>());
            List<ConsultarAlunosTurmaVO> listaAlunosTurma;

            if (UteisValidacao.emptyNumber(getModalidadeTransferencia())) {
                throw new Exception("Selecione uma modalidade.");
            }

            if (!UteisValidacao.emptyNumber(getHorarioOrigem())) {

                listaAlunosTurma = getFacade().getMatriculaAlunoHorarioTurma().
                        consultarPorHorarioTurmaPeriodo(getHorarioOrigem(), Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS, Calendario.hoje(), null, false, false);

            } else if (!UteisValidacao.emptyNumber(getTurmaOrigem())) {
                List<HorarioTurmaVO> horarioTurmas = getFacade().getHorarioTurma().consultarHorarioTurmas(getTurmaOrigem(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                listaAlunosTurma = getFacade().getMatriculaAlunoHorarioTurma().
                        consultarPorHorarioTurmaPeriodoGestaoTurma(horarioTurmas, Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS, Calendario.hoje(), null, false, false);

            } else {
                throw new Exception("Selecione pelo uma turma.");
            }

            setAlunosTurma(listaAlunosTurma);

            if (UteisValidacao.emptyList(getAlunosTurma())) {
                montarInfo("Nenhum aluno encontrado.");
            } else {
                setOnComplete("mostrarTela2();");
            }
        } catch (Exception ex) {
            setAlunosTurma(new ArrayList<ConsultarAlunosTurmaVO>());
            montarErro(ex);
        }
    }

    private void montarModalidade() {
        try {
            setSelectItensModalidade(new ArrayList<SelectItem>());

            List<ModalidadeVO> modalidades = getFacade().getModalidade().consultarTodasModalidadesComLimite(getEmpresaLogado().getCodigo(), true, null);
            for (ModalidadeVO modalidade : modalidades) {
                this.selectItensModalidade.add(new SelectItem(modalidade.getCodigo(), modalidade.getNome()));
            }
            this.selectItensModalidade = Ordenacao.ordenarLista(selectItensModalidade, "label");
            this.selectItensModalidade.add(0, new SelectItem(0, "MODALIDADE"));
        } catch (Exception e) {
            e.getStackTrace();
            this.selectItensModalidade = new ArrayList<SelectItem>();
        }
    }

    public void montarTurmas() {
        try {
            setHorarioOrigem(0);
            setHorarioDestino(0);
            setListaTurmaOrigem(new ArrayList<SelectItem>());
            setListaTurmaDestino(new ArrayList<SelectItem>());
            setListaHorarioTurmaOrigem(new ArrayList<SelectItem>());
            setListaHorarioTurmaDestino(new ArrayList<SelectItem>());

            if (UteisValidacao.emptyNumber(getModalidadeTransferencia())) {
                return;
            }

            List<SelectItem> lista = new ArrayList<SelectItem>();
            List<TurmaVO> turmas = getFacade().getTurma().consultarPorCodigoModalidade(getModalidadeTransferencia(), getEmpresaLogado().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            for (TurmaVO turma : turmas) {
                lista.add(new SelectItem(turma.getCodigo(), turma.getIdentificador()));
            }

            Ordenacao.ordenarLista(lista, "label");
            lista.add(0, new SelectItem(0, "TURMA"));
            setListaTurmaOrigem(lista);
            setListaTurmaDestino(lista);
        } catch (Exception e) {
            e.getStackTrace();
            this.listaTurmaOrigem = new ArrayList<SelectItem>();
            this.listaTurmaDestino = new ArrayList<SelectItem>();
        }
    }

    public void montarHorarioTurmaOrigem() {
        try {
            setHorarioOrigem(0);
            setListaHorarioTurmaOrigem(new ArrayList<SelectItem>());

            if (UteisValidacao.emptyNumber(getTurmaOrigem())) {
                return;
            }

            List<SelectItem> lista = new ArrayList<SelectItem>();
            List<HorarioTurmaVO> horarioTurmas = getFacade().getHorarioTurma().consultarHorarioTurmas(getTurmaOrigem(), Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS);
            for (HorarioTurmaVO hora : horarioTurmas) {
                String descricao = hora.getProfessor().getPessoa_Apresentar() + " | " + hora.getDiaSemana_Apresentar() + " - " + hora.getHoraInicial() + " ás " + hora.getHoraFinal();
                lista.add(new SelectItem(hora.getCodigo(), descricao));
            }

            Ordenacao.ordenarLista(lista, "label");
            lista.add(0, new SelectItem(0, "HORÁRIO"));
            setListaHorarioTurmaOrigem(lista);
        } catch (Exception e) {
            e.getStackTrace();
            this.listaHorarioTurmaOrigem = new ArrayList<SelectItem>();
        }
    }

    public void alterouHorarioTurmaDestino() {
        setRetornoTransferencia("");
        setApresentarTransferir(false);
        setOnComplete("mostrarTela3();");
    }

    public void montarHorarioTurmaDestino() {
        try {
            setRetornoTransferencia("");
            setHorarioDestino(0);
            setListaHorarioTurmaDestino(new ArrayList<SelectItem>());
            setApresentarTransferir(false);

            if (UteisValidacao.emptyNumber(getTurmaDestino())) {
                return;
            }

            List<HorarioTurmaVO> horarioTurmas = getFacade().getHorarioTurma().consultarHorarioTurmas(getTurmaDestino(), Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS);
            for (HorarioTurmaVO hora : horarioTurmas) {
                String descricao = hora.getProfessor().getPessoa_Apresentar() + " | " + hora.getDiaSemana_Apresentar() + " - " + hora.getHoraInicial() + " ás " + hora.getHoraFinal();
                getListaHorarioTurmaDestino().add(new SelectItem(hora.getCodigo(), descricao));
            }

            Ordenacao.ordenarLista(getListaHorarioTurmaDestino(), "label");
            getListaHorarioTurmaDestino().add(0, new SelectItem(0, "HORÁRIO"));
            setOnComplete("mostrarTela3();");
        } catch (Exception e) {
            e.getStackTrace();
            setListaHorarioTurmaDestino(new ArrayList<SelectItem>());
        }
    }


    public List<MatriculaAlunoHorarioTurmaVO> getAlunos() {
        if (alunos == null) {
            alunos = new ArrayList<MatriculaAlunoHorarioTurmaVO>();
        }
        return alunos;
    }

    public void setAlunos(List<MatriculaAlunoHorarioTurmaVO> alunos) {
        this.alunos = alunos;
    }

    public List<SelectItem> getListaHorarioTurmaOrigem() {
        if (listaHorarioTurmaOrigem == null) {
            listaHorarioTurmaOrigem = new ArrayList<SelectItem>();
        }
        return listaHorarioTurmaOrigem;
    }

    public void setListaHorarioTurmaOrigem(List<SelectItem> listaHorarioTurmaOrigem) {
        this.listaHorarioTurmaOrigem = listaHorarioTurmaOrigem;
    }

    public List<SelectItem> getListaHorarioTurmaDestino() {
        if (listaHorarioTurmaDestino == null) {
            listaHorarioTurmaDestino = new ArrayList<SelectItem>();
        }
        return listaHorarioTurmaDestino;
    }

    public void setListaHorarioTurmaDestino(List<SelectItem> listaHorarioTurmaDestino) {
        this.listaHorarioTurmaDestino = listaHorarioTurmaDestino;
    }

    public List<SelectItem> getListaTurmaDestino() {
        if (listaTurmaDestino == null) {
            listaTurmaDestino = new ArrayList<SelectItem>();
        }
        return listaTurmaDestino;
    }

    public void setListaTurmaDestino(List<SelectItem> listaTurmaDestino) {
        this.listaTurmaDestino = listaTurmaDestino;
    }

    public List<SelectItem> getListaTurmaOrigem() {
        if (listaTurmaOrigem == null) {
            listaTurmaOrigem = new ArrayList<SelectItem>();
        }
        return listaTurmaOrigem;
    }

    public void setListaTurmaOrigem(List<SelectItem> listaTurmaOrigem) {
        this.listaTurmaOrigem = listaTurmaOrigem;
    }

    public List<ConsultarAlunosTurmaVO> getAlunosTurma() {
        if (alunosTurma == null) {
            alunosTurma = new ArrayList<ConsultarAlunosTurmaVO>();
        }
        return alunosTurma;
    }

    public void setAlunosTurma(List<ConsultarAlunosTurmaVO> alunosTurma) {
        this.alunosTurma = alunosTurma;
    }

    public boolean isSelecionarTodos() {
        return selecionarTodos;
    }

    public void setSelecionarTodos(boolean selecionarTodos) {
        this.selecionarTodos = selecionarTodos;
    }

    public void acaoSelecionarTodos() {
        for (ConsultarAlunosTurmaVO alu : getAlunosTurma()) {
            alu.setSelecionado(isSelecionarTodos());
        }
    }

    public void removerAlunos() {
        try {
            List<ConsultarAlunosTurmaVO> listaRemover = new ArrayList<ConsultarAlunosTurmaVO>();
            for (ConsultarAlunosTurmaVO alu : getAlunosTurma()) {
                if (alu.isSelecionado()) {
                    listaRemover.add(alu);
                }
            }

            if (UteisValidacao.emptyList(listaRemover)) {
                throw new Exception("Nenhum aluno selecionado.");
            }

            consultarAlunos();
            montarSucessoGrowl("Alunos removidos.");
        } catch (Exception ex) {
            montarErro(ex);
        }
    }

    public void verificarAlunosParaTransferir() {
        try {
            setRetornoTransferencia("");
            setOnComplete("");
            limparMsg();
            setTurmaDestino(0);
            setHorarioDestino(0);
            setListaHorarioTurmaDestino(new ArrayList<SelectItem>());
            setApresentarTransferir(false);

            setAlunosTransferir(new ArrayList<ConsultarAlunosTurmaVO>());

            for (ConsultarAlunosTurmaVO alu : getAlunosTurma()) {
                if (alu.isSelecionado()) {
                    getAlunosTransferir().add(alu);
                }
            }

            if (UteisValidacao.emptyList(getAlunosTransferir())) {
                throw new Exception("Nenhum aluno selecionado.");
            }
            montarTurmas();
            setOnComplete("mostrarTela3()");
        } catch (Exception ex) {
            montarErro(ex);
        }
    }

    public void validarTransferencia() {
        try {
            setRetornoTransferencia("");
            setOnComplete("");
            limparMsg();

            if (UteisValidacao.emptyNumber(getTurmaDestino())) {
                throw new Exception("Selecione uma turma.");
            }

            if (UteisValidacao.emptyNumber(getHorarioDestino())) {
                throw new Exception("Selecione um horário para transferir.");
            }

            String retorno = getFacade().getMatriculaAlunoHorarioTurma().transferirAlunos(getAlunosTransferir(), getHorarioDestino(), getUsuarioLogado(), false);
            retorno = retorno.replaceFirst("ERRO:", "");
            retorno = retorno.replaceAll("\n", "<br/>");
            setRetornoTransferencia(retorno);

            if (!retorno.isEmpty()) {
                throw new Exception("Não foi possível transferir os alunos. Verifique as informações abaixo.");
            }

            setRetornoTransferencia("");
            setApresentarTransferir(true);
            setOnComplete("jQuery('.growl-container').empty();");
        } catch (Exception ex) {
            setOnComplete("mostrarTela3();");
            montarErro(ex);
        }
    }

    public void transferirAlunos() {
        try {
            setRetornoTransferencia("");
            setOnComplete("");
            limparMsg();

            if (UteisValidacao.emptyNumber(getTurmaDestino())) {
                throw new Exception("Selecione uma turma.");
            }

            if (UteisValidacao.emptyNumber(getHorarioDestino())) {
                throw new Exception("Selecione um horário para transferir.");
            }

            String retorno = getFacade().getMatriculaAlunoHorarioTurma().transferirAlunos(getAlunosTransferir(), getHorarioDestino(), getUsuarioLogado(), true);
            boolean erro = retorno.startsWith("ERRO:");
            retorno = retorno.replaceAll("\n", "<br/>");
            retorno = retorno.replaceFirst("ERRO:", "");
            setRetornoTransferencia(retorno);

            if (erro) {
                throw new Exception("Não foi possível transferir os alunos. Verifique as informações abaixo.");
            }

            montarSucessoGrowl("Alunos transferidos com sucesso.");
            setOnComplete("mostrarTela4();");
            setModalidadeTransferencia(0);
            setTurmaOrigem(0);
            setHorarioOrigem(0);
            setAlunosTurma(new ArrayList<ConsultarAlunosTurmaVO>());
            notificarRecursoEmpresa(RecursoSistema.TRANSFERIR_ALUNOS_TURMA_EM_MASSA_SUCESSO);
        } catch (Exception ex) {
            setOnComplete("mostrarTela3();");
            montarErro(ex);
        }
    }

    public String getOnComplete() {
        if (onComplete == null) {
            onComplete = "";
        }
        return onComplete;
    }

    public void setOnComplete(String onComplete) {
        this.onComplete = onComplete;
    }

    public List<ConsultarAlunosTurmaVO> getAlunosTransferir() {
        if (alunosTransferir == null) {
            alunosTransferir = new ArrayList<ConsultarAlunosTurmaVO>();
        }
        return alunosTransferir;
    }

    public void setAlunosTransferir(List<ConsultarAlunosTurmaVO> alunosTransferir) {
        this.alunosTransferir = alunosTransferir;
    }

    public String getRetornoTransferencia() {
        if (retornoTransferencia == null) {
            retornoTransferencia = "";
        }
        return retornoTransferencia;
    }

    public void setRetornoTransferencia(String retornoTransferencia) {
        this.retornoTransferencia = retornoTransferencia;
    }

    public boolean isApresentarTransferir() {
        return apresentarTransferir;
    }

    public void setApresentarTransferir(boolean apresentarTransferir) {
        this.apresentarTransferir = apresentarTransferir;
    }
}

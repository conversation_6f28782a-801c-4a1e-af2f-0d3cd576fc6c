package controle.plano;

import java.util.ArrayList;
import java.util.List;

import javax.faces.model.SelectItem;

import controle.basico.clube.MensagemGenericaControle;
import negocio.comuns.acesso.ColetorVO;
import negocio.comuns.plano.AmbienteVO;
import negocio.comuns.utilitarias.ControleConsulta;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.plano.Ambiente;
import br.com.pactosolucoes.ce.comuns.enumerador.SituacaoAmbiente;
import br.com.pactosolucoes.ce.comuns.to.TipoAmbienteTO;
import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.estrutura.exportador.controle.ExportadorListaControle;
import negocio.comuns.utilitarias.SelectItemOrdemValor;
import controle.arquitetura.SuperControle;
import controle.arquitetura.security.LoginControle;
import org.json.JSONArray;
import org.json.JSONObject;

import javax.faces.event.ActionEvent;

/**
 * Classe responsável por implementar a interação entre os componentes JSF das páginas 
 * ambienteForm.jsp ambienteCons.jsp) com as funcionalidades da classe <code>Ambiente</code>.
 * Implemtação da camada controle (Backing Bean).
 * @see SuperControle
 * @see Ambiente
 * @see AmbienteVO
 */
public class AmbienteControle extends SuperControle {

    private AmbienteVO ambienteVO;
    private int nivelMontarDados;
    public List<SelectItem> listaColetores = new ArrayList();
    private String msgAlert;
    private List listaSelectItemCodigoPiscinaMgb;
    private Boolean mgbIntegrado = false;

    /**
     * @return the nivelMontarDados
     */
    public int getNivelMontarDados() {
        return nivelMontarDados;
    }

    /**
     * @param nivelMontarDados the nivelMontarDados to set
     */
    public void setNivelMontarDados(int nivelMontarDados) {
        this.nivelMontarDados = nivelMontarDados;
    }

    /**
     * Interface <code>AmbienteInterfaceFacade</code> responsável pela interconexão da camada de controle com a camada de negócio.
     * Criando uma independência da camada de controle com relação a tenologia de persistência dos dados (DesignPatter: Façade).
     */
    public AmbienteControle() throws Exception {
        obterUsuarioLogado();
        inicializarFacades();
        verificarIntegracao();
        setControleConsulta(new ControleConsulta());
        setMensagemID("");
    }

    private void verificarIntegracao(){
        try {
            mgbIntegrado = getFacade().getMgbService().integradoMgb(getEmpresaLogado().getCodigo());
        }catch (Exception e){
            mgbIntegrado = false;
        }
    }

    public List<SelectItem> getListaColetores(){
        List<ColetorVO> coletores = new ArrayList();
        List<SelectItem> itens = new ArrayList<SelectItem>();

        try {
            coletores = new ArrayList<ColetorVO>(getFacade().getColetor().consultarColetores().values());
        }catch (Exception e){
            montarErro(e);
        }

        for (ColetorVO coletor : coletores) {
            itens.add(new SelectItem(coletor.getCodigo(), coletor.getDescricao()));
        }
        return itens;
    }

    /**
     * Rotina responsável por disponibilizar um novo objeto da classe <code>Ambiente</code>
     * para edição pelo usuário da aplicação.
     */
    public String novo() {
        setAmbienteVO(new AmbienteVO());
        limparMsg();
        listaColetores = getListaColetores();
        try {
            if (getFacade().getMgbService().integradoMgb(getEmpresaLogado().getCodigo())) {
                montarSelectItemPiscinaMgb();
            }
        } catch (Exception e) {
            inicializarListasSelectItemTodosComboBoxVazias();
        }
        return "editar";
    }

    /**
     * Rotina responsável por disponibilizar os dados de um objeto da classe <code>Ambiente</code> para alteração.
     * O objeto desta classe é disponibilizado na session da página (request) para que o JSP correspondente possa disponibilizá-lo para edição.
     */
    public String editar() {
        Integer codigoConsulta = Integer.parseInt(request().getParameter("chavePrimaria"));
        try {
            AmbienteVO obj = getFacade().getAmbiente().consultarPorChavePrimaria(codigoConsulta, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            obj.setNovoObj(false);
            obj.registrarObjetoVOAntesDaAlteracao();
            setAmbienteVO(obj);
            if (getFacade().getMgbService().integradoMgb(getEmpresaLogado().getCodigo())) {
                montarSelectItemPiscinaMgb();
            }
        } catch (Exception e) {
            setErro(true);
            setSucesso(false);
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
        return "editar";
    }

    /**
     * Método que retorna uma lista com tipos de ambiente
     *
     * @return
     * @throws Exception
     */
    public List<SelectItem> getListaAmbiente() throws Exception {
        List<SelectItem> itens = new ArrayList<SelectItem>();
        List<TipoAmbienteTO> tiposAmbiente = getFacade().getCentralEventosFacade().consultarTiposAmbiente();
        for (TipoAmbienteTO tipoAmbiente : tiposAmbiente) {
            itens.add(new SelectItem(tipoAmbiente.getCodigo(), tipoAmbiente.getDescricao()));
        }
        return itens;
    }

    /**
     * Método que retorna uma lista com tipos de ambiente referente ao módulo
     *
     * @return
     * @throws Exception
     */
    public List<SelectItem> getListaAmbienteModulo() throws Exception {
        List<SelectItem> itens = new ArrayList<SelectItem>();
        itens.add(new SelectItem("SS", "Sessão"));
        itens.add(new SelectItem("TU", "Turma"));
        return itens;
    }

    /**
     * Rotina responsável por gravar no BD os dados editados de um novo objeto da classe <code>Ambiente</code>.
     * Caso o objeto seja novo (ainda não gravado no BD) é acionado a operação <code>incluir()</code>. Caso contrário é acionado o <code>alterar()</code>.
     * Se houver alguma inconsistência o objeto não é gravado, sendo re-apresentado para o usuário juntamente com uma mensagem de erro.
     */
    public String gravar() {
        try {
            if (ambienteVO.isNovoObj().booleanValue()) {
                getFacade().getAmbiente().incluir(ambienteVO);
                incluirLogInclusao();
            } else {
                getFacade().getAmbiente().alterar(ambienteVO);
                incluirLogAlteracao();
            }
            setSucesso(true);
            setErro(false);
            setMensagemID("msg_dados_gravados");
            return "editar";
        } catch (Exception e) {
            setSucesso(false);
            setErro(true);
            setMensagemDetalhada("msg_erro", e.getMessage());
            return "editar";
        }
    }

    /**Método responsável pelas mesmas ações do método gravar(), mas será acessado pelo Central de Eventos
     * @return
     */
    public String gravarCE() {
        try {
            //verifica autorização
            super.verificarAutorizacao();
            //verifica se o objeto é novo
            if (ambienteVO.isNovoObj().booleanValue()) {
                //se for novo, inclui o objeto no banco
                //tipo de modulo referente ao central de eventos
                getFacade().getAmbiente().incluirCE(ambienteVO);
                //LOG - INICIO
                try {
                    ambienteVO.setObjetoVOAntesAlteracao(new AmbienteVO());
                    ambienteVO.setNovoObj(true);
                    registrarLogObjetoVO(ambienteVO, ambienteVO.getCodigo(), "AMBIENTE CE", 0);
                } catch (Exception e) {
                    registrarLogErroObjetoVO("AMBIENTE CE", ambienteVO.getCodigo(), "ERRO AO GERAR LOG DE INCLUSÃO DE AMBIENTE", this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
                    e.printStackTrace();
                }
                //LOG - FIM
            } else {
                //se não for novo, edita o registro do objeto no banco
                getFacade().getAmbiente().alterarCE(ambienteVO);
                //LOG - INICIO
                try {
                    registrarLogObjetoVO(ambienteVO, ambienteVO.getCodigo(), "AMBIENTE CE", 0);
                } catch (Exception e) {
                    registrarLogErroObjetoVO("AMBIENTE CE", ambienteVO.getCodigo(), "ERRO AO GERAR LOG DE ALTERAÇÃO DE AMBIENTE", this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
                    e.printStackTrace();
                }
                //LOG - FIM
            }
            // configura a mensagem
            setSucesso(true);
            setErro(false);
            setMensagemID("msg_dados_gravados");
            //retorna a string para direcionar a pagina
            return "editar";
        } catch (Exception e) {
            // se ocorrerem erros, configura a mensagem e retorna a string
            setSucesso(false);
            setErro(true);
            setMensagemDetalhada("msg_erro", e.getMessage());
            return "editar";
        }
    }

    /**
     * Rotina responsavel por executar as consultas disponiveis no JSP AmbienteCons.jsp.
     * Define o tipo de consulta a ser executada, por meio de ComboBox denominado campoConsulta, disponivel neste mesmo JSP.
     * Como resultado, disponibiliza um List com os objetos selecionados na sessao da pagina.
     */
    public String consultar() {
        try {
            super.consultar();
            List objs = new ArrayList();
            if (getControleConsulta().getCampoConsulta().equals("codigo")) {
                if (getControleConsulta().getValorConsulta().equals("")) {
                    getControleConsulta().setValorConsulta("0");
                }
                int valorInt = Integer.parseInt(getControleConsulta().getValorConsulta());
                objs = getFacade().getAmbiente().consultarPorCodigo(new Integer(valorInt), true, this.getNivelMontarDados());
            }
            if (getControleConsulta().getCampoConsulta().equals("descricao")) {
                objs = getFacade().getAmbiente().consultarTodosPorDescricao(getControleConsulta().getValorConsulta(), true, this.getNivelMontarDados());
            }
            objs = ControleConsulta.obterSubListPaginaApresentar(objs, controleConsulta);
            definirVisibilidadeLinksNavegacao(controleConsulta.getPaginaAtual(), controleConsulta.getNrTotalPaginas());
            setListaConsulta(objs);
            limparMsg();
            return "consultar";
        } catch (Exception e) {
            setSucesso(false);
            setErro(true);
            setListaConsulta(new ArrayList());
            setMensagemDetalhada("msg_erro", e.getMessage());
            return "consultar";
        }
    }

    /**
     * Operação responsável por processar a exclusão um objeto da classe <code>AmbienteVO</code>
     * Após a exclusão ela automaticamente aciona a rotina para uma nova inclusão.
     */
    public String excluir() {
        return this.excluir(false);
    }

    /**
     * Responsável por excluir um ambiente pelo CE
     * <AUTHOR>
     * 22/03/2011
     * @return
     */
    public String excluirCE() {
        return this.excluir(true);
    }

    /**
     * Operação responsável por processar a exclusão um objeto da classe <code>AmbienteVO</code>
     * Após a exclusão ela automaticamente aciona a rotina para uma nova inclusão.
     */
    public String excluir(boolean centralEventos) {
        try {
            if (centralEventos) {
                super.verificarAutorizacao();
                getFacade().getAmbiente().excluir(ambienteVO, true);
                //registrar log
                registrarLogExclusaoObjetoVO(ambienteVO, ambienteVO.getCodigo().intValue(), "AMBIENTE CE", 0);
            } else {
                getFacade().getAmbiente().excluir(ambienteVO);
                incluirLogExclusao();
            }

            setAmbienteVO(new AmbienteVO());
            setSucesso(true);
            setErro(false);
            setMensagemID("msg_dados_excluidos");
            return "consultar";
        } catch (Exception e) {
            setSucesso(false);
            setErro(true);
            if (e.getMessage().contains("ERRO: atualização ou exclusão em tabela \"ambiente\" viola restrição de chave estrangeira")){
                setMensagemDetalhada("Este ambiente não pode ser excluído, pois está sendo utilizado!");
            }
            return "editar";
        }
    }

    public void irPaginaInicial() throws Exception {
        controleConsulta.setPaginaAtual(1);
        this.consultar();
    }

    public void irPaginaAnterior() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getPaginaAtual() - 1);
        this.consultar();
    }

    public void irPaginaPosterior() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getPaginaAtual() + 1);
        this.consultar();
    }

    public void irPaginaFinal() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getNrTotalPaginas());
        this.consultar();
    }

    /**
     * Rotina responsável por preencher a combo de consulta da telas.
     */
    public List getTipoConsultaCombo() {
        List itens = new ArrayList();
        itens.add(new SelectItem("descricao", "Descrição"));
        itens.add(new SelectItem("codigo", "Código"));
        return itens;
    }

    /**
     * Rotina responsável por organizar a paginação entre as páginas resultantes de uma consulta.
     */
    public String inicializarConsultar() {
        setPaginaAtualDeTodas("0/0");
        setListaConsulta(new ArrayList());
        definirVisibilidadeLinksNavegacao(0, 0);
        setSucesso(false);
        setErro(false);
        setMensagemID("msg_entre_prmconsulta");
        return "consultar";
    }

    /**
     * Operação que inicializa as Interfaces Façades com os respectivos objetos de
     * persistência dos dados no banco de dados. 
     */
    protected boolean inicializarFacades() {
        try {
            super.inicializarFacades();
            return true;
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro_conectarBD", e.getMessage());
            return false;
        }
    }

    public AmbienteVO getAmbienteVO() {
        return ambienteVO;
    }

    public void setAmbienteVO(AmbienteVO ambienteVO) {
        this.ambienteVO = ambienteVO;
        if (ambienteVO.getCodigoPiscinaMgb() == null) {
            ambienteVO.setCodigoPiscinaMgb(new AmbienteVO().getCodigoPiscinaMgb());
        }
    }

    /**
     * Operação que será chamada pelo botão consultar do Zillyon Web, seta o nivelmontardados e chama o método irPaginaInicial
     * @throws Exception 
     */
    public void defineConsulta() throws Exception {
        this.setNivelMontarDados(Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        this.irPaginaInicial();
    }

    /**
     *Operação que será chamada pelo botão consultar do Central de Eventos, seta o nivelmontardados e chama o método irPaginaInicial 
     * @throws Exception 
     */
    public void defineConsultaCE() throws Exception {
        this.setNivelMontarDados(Uteis.NIVELMONTARDADOS_TODOS);
        this.irPaginaInicial();
    }

    /**
     * Metodo que cria o arraylist de situacoes do ambiente
     * para ser exibido na tela de ambienteForm.jsp
     * Obs.: Este metodo so é utilizado na Central de Eventos
     *
     * Autor: Pedro Y. Saito
     * Criado em 31/03/2011
     */
    public List getListaSituacaoAmbiente() throws Exception {
        List<SelectItem> objs = new ArrayList<>();
        for (SituacaoAmbiente sit : SituacaoAmbiente.values()) {
            objs.add(new SelectItem(sit.getCodigo(), sit.getDescricao()));
        }
        SelectItemOrdemValor ordenador = new SelectItemOrdemValor();
        objs.sort(ordenador);
        return objs;
    }
      public void exportar(ActionEvent evt) throws Exception {
        ExportadorListaControle exportadorListaControle = (ExportadorListaControle) JSFUtilities.getFromRequest(ExportadorListaControle.class.getSimpleName());
        String paramsTableFiltrada = context().getExternalContext().getRequestParameterMap().get("paramsTableFiltrada");

        String[] split = paramsTableFiltrada.split(",");
        String campoOrdenacao = split[0].replace("[", "");
        String ordem = split[1];
        String filtro = split[2].replace("''", "");
        filtro = filtro.replace("]", "");
        List listaParaImpressao = getFacade().getAmbiente().consultarParaImpressao(filtro, ordem, campoOrdenacao, getEmpresaLogado() == null ? 0 : getEmpresaLogado().getCodigo());
        exportadorListaControle.exportar(evt, listaParaImpressao, filtro, null);

    }
      
    public void incluirLogInclusao() throws Exception {
        try {
            ambienteVO.setObjetoVOAntesAlteracao(new AmbienteVO());
            ambienteVO.setNovoObj(true);
            registrarLogObjetoVO(ambienteVO, ambienteVO.getCodigo(), "AMBIENTE", 0);
        } catch (Exception e) {
            registrarLogErroObjetoVO("AMBIENTE", ambienteVO.getCodigo(), "ERRO AO GERAR LOG DE INCLUSÃO DE AMBIENTE", this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
        ambienteVO.setNovoObj(new Boolean(false));
        ambienteVO.registrarObjetoVOAntesDaAlteracao();
    }
    
    public void incluirLogExclusao() throws Exception {
        try {
            ambienteVO.setObjetoVOAntesAlteracao(new AmbienteVO());
            ambienteVO.setNovoObj(true);
            registrarLogExclusaoTodosDadosObjetoVO(ambienteVO, ambienteVO.getCodigo(), "AMBIENTE", 0);
        } catch (Exception e) {
            registrarLogErroObjetoVO("AMBIENTE", ambienteVO.getCodigo(), "ERRO AO GERAR LOG DE EXCLUSÃO DE AMBIENTE ", this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
    }

    /**
     * Inclui o log de alteração de categoria
     * @throws Exception
     */
    public void incluirLogAlteracao() throws Exception {
        try {
            registrarLogObjetoVO(ambienteVO, ambienteVO.getCodigo(), "AMBIENTE", 0);
            ambienteVO.registrarObjetoVOAntesDaAlteracao();
        } catch (Exception e) {
            registrarLogErroObjetoVO("AMBIENTE", ambienteVO.getCodigo(), "ERRO AO GERAR LOG DE ALTERAÇÃO DE AMBIENTE ", this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
        ambienteVO.registrarObjetoVOAntesDaAlteracao();
    }
    
     public void realizarConsultaLogObjetoSelecionado() {
         try {
             LoginControle loginControle = (LoginControle) context().getExternalContext().getSessionMap().get("LoginControle");
             String nomeClasse = ambienteVO.getClass().getSimpleName();
             nomeClasse = nomeClasse.substring(0, nomeClasse.length() - 2);
             loginControle.setNomeClasse(loginControle.getNomeEntidadeCamposLog("prt_" + nomeClasse + "_tituloForm"));
             loginControle.consultarLogObjetoSelecionado(nomeClasse.toUpperCase(), ambienteVO.getCodigo(), 0);
         } catch (Exception e) {
             setMensagemDetalhada("msg_erro", "Operação não realizada erro:" + e.getMessage());
             Uteis.logar(e.getMessage());
         }
     }


    public void realizarConsultaLogObjetoGeral() {
       ambienteVO = new AmbienteVO();
       realizarConsultaLogObjetoSelecionado();
    }

    public void setMsgAlert(String msgAlert) {
        this.msgAlert = msgAlert;
    }

    public String getMsgAlert() {
        if (msgAlert == null) {
            return "";
        }
        return msgAlert;
    }


    public void confirmarExcluir(){
        MensagemGenericaControle control = (MensagemGenericaControle) getControlador(MensagemGenericaControle.class);
        control.setMensagemDetalhada("", "");
        setMsgAlert("Richfaces.showModalPanel('mdlMensagemGenerica');");
        control.init("Exclusão de Ambiente",
                "Deseja excluir o Ambiente?",
                this, "excluir", "", "", "", "grupoMensagem");
    }

    public Boolean getMgbIntegrado() {
        return mgbIntegrado;
    }

    public void setMgbIntegrado(Boolean mgb) {
        this.mgbIntegrado = mgb;
    }

    public List getListaSelectItemCodigoPiscinaMgb() {
        if (listaSelectItemCodigoPiscinaMgb == null) {
            listaSelectItemCodigoPiscinaMgb = new ArrayList();
        }
        return listaSelectItemCodigoPiscinaMgb;
    }

    public void setListaSelectItemCodigoPiscinaMgb(List listaSelectItemCodigoPiscinaMgb) {
        this.listaSelectItemCodigoPiscinaMgb = listaSelectItemCodigoPiscinaMgb;
    }

    public void montarSelectItemPiscinaMgb() throws Exception {
        List objs = new ArrayList();
        objs.add(new SelectItem(0, ""));

        JSONObject jsonObject = getFacade().getMgbService().consultarPiscinas(getEmpresaLogado().getCodigo());
        JSONArray lista = new JSONObject(jsonObject.get("data").toString()).getJSONArray("items");

        for (int e = 0; e < lista.length(); e++) {
            JSONObject obj = lista.getJSONObject(e);
            AmbienteVO ambienteVO = new AmbienteVO();
            ambienteVO.setCodigoPiscinaMgb(obj.getString("publicId"));
            ambienteVO.setDescricao(obj.getString("name"));
            objs.add(new SelectItem(ambienteVO.getCodigoPiscinaMgb(), ambienteVO.getDescricao()));
        }
        setListaSelectItemCodigoPiscinaMgb(objs);
    }

    private void inicializarListasSelectItemTodosComboBoxVazias() {
        List objs = new ArrayList();
        objs.add(new SelectItem(0, ""));

        if (getListaSelectItemCodigoPiscinaMgb() == null) {
            setListaSelectItemCodigoPiscinaMgb(objs);
        }
    }

    public Boolean getIntegradoMgb(){
        try {
            return getFacade().getMgbService().integradoMgb(getEmpresaLogado().getCodigo());
        }catch (Exception e){
            return false;
        }
    }

}

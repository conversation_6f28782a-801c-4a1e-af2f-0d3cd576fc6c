package controle.plano;

import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.estrutura.exportador.controle.ExportadorListaControle;
import controle.arquitetura.SuperControle;
import controle.arquitetura.security.LoginControle;
import controle.basico.clube.MensagemGenericaControle;
import negocio.comuns.arquitetura.LogVO;
import negocio.comuns.financeiro.enumerador.TipoConvenioCobrancaEnum;
import negocio.comuns.plano.CondicaoPagamentoParcelaVO;
import negocio.comuns.plano.CondicaoPagamentoVO;
import negocio.comuns.plano.PlanoDuracaoVO;
import negocio.comuns.plano.PlanoVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.CondicaoPagamentoPlanoTO;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.ControleConsulta;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UtilReflection;
import negocio.facade.jdbc.plano.CondicaoPagamento;

import javax.faces.event.ActionEvent;
import javax.faces.model.SelectItem;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import negocio.comuns.utilitarias.UteisValidacao;

/**
 * Classe responsável por implementar a interação entre os componentes JSF das páginas 
 * condicaoPagamentoForm.jsp condicaoPagamentoCons.jsp) com as funcionalidades da classe <code>CondicaoPagamento</code>.
 * Implemtação da camada controle (Backing Bean).
 * @see SuperControle
 * @see CondicaoPagamento
 * @see CondicaoPagamentoVO
 */
public class CondicaoPagamentoControle extends SuperControle {

    private CondicaoPagamentoVO condicaoPagamentoVO;
    /**
     * Interface <code>CondicaoPagamentoInterfaceFacade</code> responsável pela interconexão da camada de controle com a camada de negócio.
     * Criando uma independência da camada de controle com relação a tenologia de persistência dos dados (DesignPatter: Façade).
     */
    private CondicaoPagamentoParcelaVO condicaoPagamentoParcelaVO;
    private Boolean visualizarEntrada;
    private List<SelectItem> tiposConvenioCobranca;
    private List<SelectItem> listaPlano;
    private List<SelectItem> listaDuracao;
    private static final Integer CODIGO_PLANO_TODOS = 9999;
    private static final Integer CODIGO_DURACAO_TODOS = 9999;
    private CondicaoPagamentoPlanoTO condicaoPagamentoPlanoTO = new CondicaoPagamentoPlanoTO();
    private  String msgAlert;

    public CondicaoPagamentoControle() throws Exception {
        obterUsuarioLogado();
        inicializarFacades();
        carregarTiposConvenioCobranca();
        setControleConsulta(new ControleConsulta());
        consultarPlanos();
        consultarDuracoesPlano();
        setMensagemID("");
    }

    private void carregarTiposConvenioCobranca() throws Exception {
        tiposConvenioCobranca = JSFUtilities.getSelectItemListFromEnum(TipoConvenioCobrancaEnum.class, "descricao", false);
    }

    private void consultarPlanos() throws Exception{
        List<PlanoVO> lista = getFacade().getPlano().consultarIngressoAte(Calendario.hoje(),true,getEmpresaLogado().getCodigo(),false,Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        this.listaPlano = new ArrayList<SelectItem>();
        this.listaPlano.add(new SelectItem(0, ""));
        this.listaPlano.add(new SelectItem(CODIGO_PLANO_TODOS, "TODOS"));
        for (PlanoVO planoVO: lista){
            this.listaPlano.add(new SelectItem(planoVO.getCodigo(), planoVO.getDescricao()));
        }
    }

    public void consultarDuracoesPlano()throws Exception{
        List<Integer> listaDuracao;
        this.listaDuracao = new ArrayList<SelectItem>();
        this.listaDuracao.add(new SelectItem(0, ""));
        this.listaDuracao.add(new SelectItem(CODIGO_DURACAO_TODOS, "TODOS"));
        if (UtilReflection.objetoMaiorQueZero(this.condicaoPagamentoPlanoTO, "getPlanoVO().getCodigo()")){

            //Para validar planos do tipo site ao inserir condição de pagamento na duração do plano
            montarDadosValidarPlanoSite();

            if (this.condicaoPagamentoPlanoTO.getPlanoVO().getCodigo().equals(CODIGO_PLANO_TODOS)){
                listaDuracao = getFacade().getPlanoDuracao().consultarDuracaoMesesVigente(getEmpresaLogado().getCodigo(), null);
            }else{
                listaDuracao = getFacade().getPlanoDuracao().consultarDuracaoMesesVigente(getEmpresaLogado().getCodigo(), this.condicaoPagamentoPlanoTO.getPlanoVO().getCodigo());
            }
        }else{
            listaDuracao = getFacade().getPlanoDuracao().consultarDuracaoMesesVigente(getEmpresaLogado().getCodigo(), null);
        }
        for (Integer duracao : listaDuracao){
            this.listaDuracao.add(new SelectItem(duracao, String.valueOf(duracao)));
        }
    }

    private void montarDadosValidarPlanoSite() throws Exception {
        if(this.condicaoPagamentoPlanoTO.getPlanoVO().getCodigo() != 0 && !this.condicaoPagamentoPlanoTO.getPlanoVO().getCodigo().equals(CODIGO_PLANO_TODOS)) {
            this.condicaoPagamentoPlanoTO.setPlanoVO(getFacade().getPlano().consultarPorChavePrimaria(this.condicaoPagamentoPlanoTO.getPlanoVO().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            this.condicaoPagamentoPlanoTO.getPlanoVO().setPlanoDuracaoVOs(getFacade().getPlanoDuracao().consultarPlanoDuracaos(this.condicaoPagamentoPlanoTO.getPlanoVO().getCodigo(), false, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));
        }
    }


    /**
     * Rotina responsável por disponibilizar um novo objeto da classe <code>CondicaoPagamento</code>
     * para edição pelo usuário da aplicação.
     */
    public String novo() {
        setCondicaoPagamentoVO(new CondicaoPagamentoVO());
        setCondicaoPagamentoParcelaVO(new CondicaoPagamentoParcelaVO());
        setVisualizarEntrada(false);
        limparMsg();
        return "editar";
    }

    /**
     * Rotina responsável por disponibilizar os dados de um objeto da classe <code>CondicaoPagamento</code> para alteração.
     * O objeto desta classe é disponibilizado na session da página (request) para que o JSP correspondente possa disponibilizá-lo para edição.
     */
    public String editar() {
        Integer codigoConsulta = Integer.parseInt(request().getParameter("chavePrimaria"));
        try {
            //      verificarCondicaoPagamento();
            CondicaoPagamentoVO obj = getFacade().getCondicaoPagamento().consultarPorChavePrimaria(codigoConsulta, Uteis.NIVELMONTARDADOS_TODOS);
            obj.setNovoObj(false);
            obj.registrarObjetoVOAntesDaAlteracao();
            setCondicaoPagamentoVO(obj);
            //        if (obj.getEntrada()) {
            //            setVisualizarEntrada(true);
            //        } else {
            //            setVisualizarEntrada(false);
            //        }
            setCondicaoPagamentoParcelaVO(new CondicaoPagamentoParcelaVO());
            setVisualizarEntrada(true);
            this.condicaoPagamentoPlanoTO = new CondicaoPagamentoPlanoTO();
            consultarCondicaoPagamentoPlano();
        } catch (Exception e) {
            setErro(true);
            setSucesso(false);
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
        return "editar";
    }

    private void consultarCondicaoPagamentoPlano()throws Exception{
        getCondicaoPagamentoVO().setListaCondicaoPagamentoPlano(getFacade().getCondicaoPagamento().consultarCondicaoPagamentoPlano(getCondicaoPagamentoVO().getCodigo() ,getEmpresaLogado().getCodigo()));
        getCondicaoPagamentoVO().setListaCondicaoPagamentoPlanoAntesDeAlterar(new ArrayList<CondicaoPagamentoPlanoTO>());
        getCondicaoPagamentoVO().getListaCondicaoPagamentoPlanoAntesDeAlterar().addAll(getCondicaoPagamentoVO().getListaCondicaoPagamentoPlano());
    }

    /**
     * Rotina responsável por gravar no BD os dados editados de um novo objeto da classe <code>CondicaoPagamento</code>.
     * Caso o objeto seja novo (ainda não gravado no BD) é acionado a operação <code>incluir()</code>. Caso contrário é acionado o <code>alterar()</code>.
     * Se houver alguma inconsistência o objeto não é gravado, sendo re-apresentado para o usuário juntamente com uma mensagem de erro.
     */
    public void gravar() {
        try {
            CondicaoPagamentoVO.validarDados(condicaoPagamentoVO);
            verificarCondicaoPagamento();
            if (!condicaoPagamentoVO.getMsgErroGerarParcela()) {
                String infoAlteracoes= "";
                if (condicaoPagamentoVO.isNovoObj().booleanValue()) {
                    getFacade().getCondicaoPagamento().incluir(condicaoPagamentoVO);
                    incluirLogInclusao();
                    registrarLogCondPagamentoPlano(true);
                } else {
                    getFacade().getCondicaoPagamento().alterar(condicaoPagamentoVO);
                    infoAlteracoes = processarAlteracoesPlanos();
                    incluirLogAlteracao();
                    registrarLogCondPagamentoPlano(false);
                }
                consultarCondicaoPagamentoPlano();
                montarSucesso("msg_dados_gravados");
                if(!UteisValidacao.emptyString(infoAlteracoes)){
                    montarAviso(infoAlteracoes);
                }
                
            } else {
                throw new ConsistirException("O NÚMERO de PARCELA(S) é diferente da quantidade de PARCELA(S) GERADA(S). Gere a(s) parcela(s) na CONDIÇÃO DE PAGAMENTO DA PARCELA.");
            }
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);

        }
    }

    public void verificarCondicaoPagamento() {
        try {
            if ((condicaoPagamentoVO.getNrParcelas().intValue() != 0) && (condicaoPagamentoVO.getCondicaoPagamentoParcelaVOs().size() == 0)) {
                condicaoPagamentoVO.setMsgErroGerarParcela(true);
            } else {
                condicaoPagamentoVO.setMsgErroGerarParcela(false);
            }
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
        }
    }

    public void ErroGerarParcela() {
        condicaoPagamentoVO.setMsgErroGerarParcela(false);
        setMensagem("");
        setMensagemDetalhada("", "");
        setMensagemID("");
        setSucesso(false);
        setErro(false);

    }

    /**
     * Rotina responsavel por executar as consultas disponiveis no JSP CondicaoPagamentoCons.jsp.
     * Define o tipo de consulta a ser executada, por meio de ComboBox denominado campoConsulta, disponivel neste mesmo JSP.
     * Como resultado, disponibiliza um List com os objetos selecionados na sessao da pagina.
     */
    public String consultar() {
        try {
            super.consultar();
            List objs = new ArrayList();
            if (getControleConsulta().getCampoConsulta().equals("codigo")) {
                if (getControleConsulta().getValorConsulta().equals("")) {
                    getControleConsulta().setValorConsulta("0");
                }
                int valorInt = Integer.parseInt(getControleConsulta().getValorConsulta());
                objs = getFacade().getCondicaoPagamento().consultarPorCodigo(new Integer(valorInt), true, Uteis.NIVELMONTARDADOS_TODOS);
            }
            if (getControleConsulta().getCampoConsulta().equals("descricao")) {
                objs = getFacade().getCondicaoPagamento().consultarPorDescricao(getControleConsulta().getValorConsulta(), true, Uteis.NIVELMONTARDADOS_TODOS);
            }
            if (getControleConsulta().getCampoConsulta().equals("nrParcelas")) {
                if (getControleConsulta().getValorConsulta().equals("")) {
                    getControleConsulta().setValorConsulta("0");
                }
                int valorInt = Integer.parseInt(getControleConsulta().getValorConsulta());
                objs = getFacade().getCondicaoPagamento().consultarPorNrParcelas(new Integer(valorInt), true, Uteis.NIVELMONTARDADOS_TODOS);
            }
            objs = ControleConsulta.obterSubListPaginaApresentar(objs, controleConsulta);
            definirVisibilidadeLinksNavegacao(controleConsulta.getPaginaAtual(), controleConsulta.getNrTotalPaginas());
            setListaConsulta(objs);
            limparMsg();
            return "consultar";
        } catch (Exception e) {
            setListaConsulta(new ArrayList());
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
            return "consultar";
        }
    }

    /**
     * Operação responsável por processar a exclusão um objeto da classe <code>CondicaoPagamentoVO</code>
     * Após a exclusão ela automaticamente aciona a rotina para uma nova inclusão.
     */
    public String excluir() {
        try {
            getFacade().getCondicaoPagamento().excluir(condicaoPagamentoVO);
            incluirLogExclusao();
            setCondicaoPagamentoVO(new CondicaoPagamentoVO());

            setCondicaoPagamentoParcelaVO(new CondicaoPagamentoParcelaVO());
            setMensagemID("msg_dados_excluidos");
            setSucesso(true);
            setErro(false);
            return "consultar";
        } catch (Exception e) {
            if (e.getMessage().contains("ERRO: atualização ou exclusão em tabela \"condicaopagamento\" viola restrição de chave estrangeira")){
                setMensagemDetalhada("Esta condição de pagamento não pode ser excluída, pois está sendo utilizada!");
            }else {
                setMensagemDetalhada("msg_erro", e.getMessage());
            }
            setSucesso(false);
            setErro(true);
            return "editar";
        }
    }

    public void limparValorEntrada() {
        getCondicaoPagamentoVO().setPercentualValorEntrada(0.0);
    }

    public void validarEntrada() {
        if (getCondicaoPagamentoVO().getNrParcelas() <= 1) {
            setVisualizarEntrada(false);
            getCondicaoPagamentoVO().setEntrada(false);
        } else {
            setVisualizarEntrada(true);

        }

    }

    /* Método responsável por adicionar um novo objeto da classe <code>CondicaoPagamentoParcela</code>
     * para o objeto <code>condicaoPagamentoVO</code> da classe <code>CondicaoPagamento</code>
     */
    public void adicionarCondicaoPagamentoParcela() throws Exception {
        try {
            getCondicaoPagamentoVO().setCondicaoPagamentoParcelaVOs(new ArrayList());
            condicaoPagamentoVO.validarDadosAdiconar(condicaoPagamentoVO);
            if (!getCondicaoPagamentoVO().getCodigo().equals(new Integer(0))) {
                condicaoPagamentoParcelaVO.setCondicaoPagamento(getCondicaoPagamentoVO().getCodigo());
            }

            if (!getCondicaoPagamentoVO().getNrParcelas().equals(new Integer(0))) {
                int contadorParcela = 1;
                int contadorIntervalo = 0;
                while (contadorParcela <= getCondicaoPagamentoVO().getNrParcelas().intValue()) {
                    getCondicaoPagamentoParcelaVO().setNrParcela(contadorParcela);
                    if (this.getCondicaoPagamentoVO().getEntrada().equals(true) && contadorParcela == 1 && !getCondicaoPagamentoVO().getNrParcelas().equals(new Integer(1))) {
                        contadorIntervalo = contadorIntervalo;
                    } else {
                        contadorIntervalo = contadorIntervalo + getCondicaoPagamentoVO().getIntervaloEntreParcela().intValue();
                    }

                    getCondicaoPagamentoParcelaVO().setNrDiasParcela(new Integer(contadorIntervalo));
                    getCondicaoPagamentoVO().adicionarObjCondicaoPagamentoParcelaVOs(getCondicaoPagamentoParcelaVO());
                    contadorParcela++;
                    this.setCondicaoPagamentoParcelaVO(new CondicaoPagamentoParcelaVO());
                }
            }
            getCondicaoPagamentoVO().setMsgErroGerarParcela(false);
            setMensagemID("msg_dados_adicionados");
            setErro(false);
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    /* Método responsável por disponibilizar dados de um objeto da classe <code>CondicaoPagamentoParcela</code>
     * para edição pelo usuário.
     */
    public void editarCondicaoPagamentoParcela() throws Exception {
        CondicaoPagamentoParcelaVO obj = (CondicaoPagamentoParcelaVO) context().getExternalContext().getRequestMap().get("condicaoPagamentoParcela");
        setCondicaoPagamentoParcelaVO(obj);
    }

    /* Método responsável por remover um novo objeto da classe <code>CondicaoPagamentoParcela</code>
     * do objeto <code>condicaoPagamentoVO</code> da classe <code>CondicaoPagamento</code>
     */
    public void removerCondicaoPagamentoParcela() throws Exception {
        CondicaoPagamentoParcelaVO obj = (CondicaoPagamentoParcelaVO) context().getExternalContext().getRequestMap().get("condicaoPagamentoParcela");
        getCondicaoPagamentoVO().excluirObjCondicaoPagamentoParcelaVOs(obj);
        setMensagemID("msg_dados_excluidos");
    }

    public void irPaginaInicial() throws Exception {
        controleConsulta.setPaginaAtual(1);
        this.consultar();
    }

    public void irPaginaAnterior() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getPaginaAtual() - 1);
        this.consultar();
    }

    public void irPaginaPosterior() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getPaginaAtual() + 1);
        this.consultar();
    }

    public void irPaginaFinal() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getNrTotalPaginas());
        this.consultar();
    }

    /**
     * Rotina responsável por preencher a combo de consulta da telas.
     */
    public List getTipoConsultaCombo() {
        List itens = new ArrayList();
        itens.add(new SelectItem("descricao", "Descrição"));
        itens.add(new SelectItem("codigo", "Código"));
        itens.add(new SelectItem("nrParcelas", "Número de Parcelas"));
        //itens.add(new SelectItem("percentualValorEntrada", "Percentual do Valor de Entrada"));
        return itens;
    }

    /**
     * Rotina responsável por organizar a paginação entre as páginas resultantes de uma consulta.
     */
    public String inicializarConsultar() {
        setPaginaAtualDeTodas("0/0");
        setListaConsulta(new ArrayList());
        definirVisibilidadeLinksNavegacao(0, 0);
        limparMsg();
        return "consultar";
    }

    /**
     * Operação que inicializa as Interfaces Façades com os respectivos objetos de
     * persistência dos dados no banco de dados. 
     */
    protected boolean inicializarFacades() {
        try {
            super.inicializarFacades();
            return true;
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro_conectarBD", e.getMessage());
            return false;
        }
    }

    public CondicaoPagamentoParcelaVO getCondicaoPagamentoParcelaVO() {
        return condicaoPagamentoParcelaVO;
    }

    public void setCondicaoPagamentoParcelaVO(CondicaoPagamentoParcelaVO condicaoPagamentoParcelaVO) {
        this.condicaoPagamentoParcelaVO = condicaoPagamentoParcelaVO;
    }

    public CondicaoPagamentoVO getCondicaoPagamentoVO() {
        return condicaoPagamentoVO;
    }

    public void setCondicaoPagamentoVO(CondicaoPagamentoVO condicaoPagamentoVO) {
        this.condicaoPagamentoVO = condicaoPagamentoVO;
    }

    public Boolean getVisualizarEntrada() {
        return visualizarEntrada;
    }

    public void setVisualizarEntrada(Boolean visualizarEntrada) {
        this.visualizarEntrada = visualizarEntrada;
    }

    public List<SelectItem> getTiposConvenioCobranca() {
        return tiposConvenioCobranca;
    }

    public void setTiposConvenioCobranca(List<SelectItem> tiposConvenioCobranca) {
        this.tiposConvenioCobranca = tiposConvenioCobranca;
    }

    public void exportar(ActionEvent evt) throws Exception {
        ExportadorListaControle exportadorListaControle = (ExportadorListaControle) JSFUtilities.getFromRequest(ExportadorListaControle.class.getSimpleName());
        String paramsTableFiltrada = context().getExternalContext().getRequestParameterMap().get("paramsTableFiltrada");

        String[] split = paramsTableFiltrada.split(",");
        String campoOrdenacao = split[0].replace("[", "");
        String ordem = split[1];
        String filtro = split[2].replace("''", "");
        filtro = filtro.replace("]", "");
        List listaParaImpressao = getFacade().getCondicaoPagamento().consultarParaImpressao(filtro, ordem, campoOrdenacao, getEmpresaLogado() == null ? 0 : getEmpresaLogado().getCodigo());
        exportadorListaControle.exportar(evt, listaParaImpressao, filtro, null);

    }

    public Boolean getVisualizarCobrancaPrePago() {
        return (getCondicaoPagamentoVO().getTipoConvenioCobranca().getCodigo() == TipoConvenioCobrancaEnum.DCC.getCodigo()
                || getCondicaoPagamentoVO().getTipoConvenioCobranca().getCodigo() == TipoConvenioCobrancaEnum.DCC_BIN.getCodigo()
                || getCondicaoPagamentoVO().getTipoConvenioCobranca().getCodigo() == TipoConvenioCobrancaEnum.DCC_GETNET.getCodigo()
                || getCondicaoPagamentoVO().getTipoConvenioCobranca().getCodigo() == TipoConvenioCobrancaEnum.DCC_GETNET_ONLINE.getCodigo()
                || getCondicaoPagamentoVO().getTipoConvenioCobranca().getCodigo() == TipoConvenioCobrancaEnum.DCC_VINDI.getCodigo()
                || getCondicaoPagamentoVO().getTipoConvenioCobranca().getCodigo() == TipoConvenioCobrancaEnum.DCC_CIELO_ONLINE.getCodigo()
                || getCondicaoPagamentoVO().getTipoConvenioCobranca().getCodigo() == TipoConvenioCobrancaEnum.DCC_E_REDE.getCodigo()
                || getCondicaoPagamentoVO().getTipoConvenioCobranca().getCodigo() == TipoConvenioCobrancaEnum.DCC_MAXIPAGO.getCodigo()
                || getCondicaoPagamentoVO().getTipoConvenioCobranca().getCodigo() == TipoConvenioCobrancaEnum.DCC_STONE_ONLINE.getCodigo()
                || getCondicaoPagamentoVO().getTipoConvenioCobranca().getCodigo() == TipoConvenioCobrancaEnum.DCC_MUNDIPAGG.getCodigo()
                || getCondicaoPagamentoVO().getTipoConvenioCobranca().getCodigo() == TipoConvenioCobrancaEnum.DCC_PAGAR_ME.getCodigo()
                || getCondicaoPagamentoVO().getTipoConvenioCobranca().getCodigo() == TipoConvenioCobrancaEnum.DCC_PAGBANK.getCodigo()
                || getCondicaoPagamentoVO().getTipoConvenioCobranca().getCodigo() == TipoConvenioCobrancaEnum.DCC_STRIPE.getCodigo());
    }

    public void alterarRecebimentoPrePago() {
        if (getCondicaoPagamentoVO().getRecebimentoPrePago()){
            if (getCondicaoPagamentoVO().getTipoConvenioCobranca().getCodigo() != TipoConvenioCobrancaEnum.DCC.getCodigo() &&
                    getCondicaoPagamentoVO().getTipoConvenioCobranca().getCodigo() != TipoConvenioCobrancaEnum.DCC_GETNET.getCodigo() &&
                    getCondicaoPagamentoVO().getTipoConvenioCobranca().getCodigo() != TipoConvenioCobrancaEnum.DCC_BIN.getCodigo()){
                getCondicaoPagamentoVO().setRecebimentoPrePago(null);
            }
        }
    }
    
         public void incluirLogInclusao() throws Exception {
        try {
            condicaoPagamentoVO.setObjetoVOAntesAlteracao(new CondicaoPagamentoVO());
            condicaoPagamentoVO.setNovoObj(true);
            registrarLogObjetoVO(condicaoPagamentoVO, condicaoPagamentoVO.getCodigo(), "CONDICAOPAGAMENTO", 0);
        } catch (Exception e) {
            registrarLogErroObjetoVO("CONDICAOPAGAMENTO", condicaoPagamentoVO.getCodigo(), "ERRO AO GERAR LOG DE INCLUSÃO DE CONDICAOPAGAMENTO", this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
        condicaoPagamentoVO.setNovoObj(new Boolean(false));
        condicaoPagamentoVO.registrarObjetoVOAntesDaAlteracao();
    }
    
    public void incluirLogExclusao() throws Exception {
        try {
            condicaoPagamentoVO.setObjetoVOAntesAlteracao(new CondicaoPagamentoVO());
            condicaoPagamentoVO.setNovoObj(true);
            registrarLogExclusaoTodosDadosObjetoVO(condicaoPagamentoVO, condicaoPagamentoVO.getCodigo(), "CONDICAOPAGAMENTO", 0);
        } catch (Exception e) {
            registrarLogErroObjetoVO("CONDICAOPAGAMENTO", condicaoPagamentoVO.getCodigo(), "ERRO AO GERAR LOG DE EXCLUSÃO DE CONDICAOPAGAMENTO ", this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
    }

    /**
     * Inclui o log de alteração de categoria
     * @throws Exception
     */
    public void incluirLogAlteracao() throws Exception {
        try {
            registrarLogObjetoVO(condicaoPagamentoVO, condicaoPagamentoVO.getCodigo(), "CONDICAOPAGAMENTO", 0);
            condicaoPagamentoVO.registrarObjetoVOAntesDaAlteracao();
        } catch (Exception e) {
            registrarLogErroObjetoVO("CONDICAOPAGAMENTO", condicaoPagamentoVO.getCodigo(), "ERRO AO GERAR LOG DE ALTERAÇÃO DE CONDICAOPAGAMENTO ", this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
        condicaoPagamentoVO.registrarObjetoVOAntesDaAlteracao();
    }
    
     public void realizarConsultaLogObjetoSelecionado() {
        LoginControle loginControle = (LoginControle) context().getExternalContext().getSessionMap().get("LoginControle");
        String nomeClasse = condicaoPagamentoVO.getClass().getSimpleName();
        nomeClasse = nomeClasse.substring(0, nomeClasse.length() - 2);
        loginControle.setNomeClasse(loginControle.getNomeEntidadeCamposLog("prt_" + nomeClasse + "_tituloForm"));
        loginControle.consultarLogObjetoSelecionado(nomeClasse.toUpperCase(), condicaoPagamentoVO.getCodigo(), 0);
    }
    public void realizarConsultaLogObjetoGeral() {
       condicaoPagamentoVO = new CondicaoPagamentoVO();
       realizarConsultaLogObjetoSelecionado();
    }

    public void adicionarCondicaoAoCadastroPlano(){
        try{
            if ((this.condicaoPagamentoPlanoTO.getPlanoVO() == null) || (this.condicaoPagamentoPlanoTO.getPlanoVO().getCodigo() <= 0)){
                throw new Exception("Informe o plano.");
            }
            if ((this.condicaoPagamentoPlanoTO.getDuracaoPlano() == null) || (this.condicaoPagamentoPlanoTO.getDuracaoPlano() <= 0)){
                throw new Exception("Informe a duração do plano.");
            }
            if(this.condicaoPagamentoPlanoTO.getPlanoVO().getSite() && this.condicaoPagamentoPlanoTO.getPlanoVO().getPlanoDuracaoVOs().get(0).getPlanoCondicaoPagamentoVOs().size() >= 1){
                throw new Exception("Só é permitido uma condição de pagamento configurada para planos do tipo site.");
            }
            boolean todos = true;
            if (this.condicaoPagamentoPlanoTO.getPlanoVO().getCodigo().equals(CODIGO_PLANO_TODOS)){
                for (SelectItem itemPlano: this.listaPlano){
                    Integer plano = (Integer) itemPlano.getValue();
                    if ((plano.equals(0)) || (plano.equals(CODIGO_PLANO_TODOS))){
                        continue;
                    }
                    adicionarCondicaoDuracao(plano, todos);
                }
            }else{
                todos = false;
                adicionarCondicaoDuracao(this.condicaoPagamentoPlanoTO.getPlanoVO().getCodigo(), todos);
            }
            this.condicaoPagamentoPlanoTO = new CondicaoPagamentoPlanoTO();
        }catch (Exception e){
            montarErro(e);
        }
    }

    private void adicionarCondicaoDuracao(Integer codigoPlano, boolean todos)throws Exception{
        if (this.condicaoPagamentoPlanoTO.getDuracaoPlano().equals(CODIGO_DURACAO_TODOS)){
            for (SelectItem itemDuracao: this.listaDuracao){
                Integer duracao = (Integer) itemDuracao.getValue();
                if ((duracao.equals(0)) || (duracao.equals(CODIGO_DURACAO_TODOS))){
                    continue;
                }
                adicionarCondicaoDuracaoPlano(codigoPlano, duracao, todos);
            }
        }else{
            adicionarCondicaoDuracaoPlano(codigoPlano, this.condicaoPagamentoPlanoTO.getDuracaoPlano(), todos);
        }
    }

    private void adicionarCondicaoDuracaoPlano(Integer codigoPlano, Integer duracaoPlano, boolean todos)throws Exception {
        PlanoVO plano = getFacade().getPlano().consultarPorChavePrimaria(codigoPlano, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        if (todos && plano.getSite()) {
            List<PlanoDuracaoVO> duracoesPlano = getFacade().getPlanoDuracao().consultarPlanoDuracaos(plano.getCodigo(), false, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            if (duracoesPlano.size() >= 1) { // Plano Site só permite uma condição de pagamento.
                return;
            }
        }
        this.condicaoPagamentoPlanoTO = new CondicaoPagamentoPlanoTO();
        this.condicaoPagamentoPlanoTO.setCondicaoPagamentoVO(getCondicaoPagamentoVO());
        this.condicaoPagamentoPlanoTO.setPlanoVO(plano);
        this.condicaoPagamentoPlanoTO.setDuracaoPlano(duracaoPlano);
        if (!(getCondicaoPagamentoVO().getListaCondicaoPagamentoPlano().contains(this.condicaoPagamentoPlanoTO))) {
            getCondicaoPagamentoVO().getListaCondicaoPagamentoPlano().add(this.condicaoPagamentoPlanoTO);
            Collections.sort(getCondicaoPagamentoVO().getListaCondicaoPagamentoPlano(), CondicaoPagamentoPlanoTO.COMPARATOR_PLANO_DURACAO);
        }
        limparMsg();
    }

    public void removerCondicaoPagamentoPlano(){
        getCondicaoPagamentoVO().getListaCondicaoPagamentoPlano().remove(this.condicaoPagamentoPlanoTO);
        this.condicaoPagamentoPlanoTO = new CondicaoPagamentoPlanoTO();
        limparMsg();
    }


    private void registrarLogCondPagamentoPlano(boolean inclusao)throws Exception{
        // registrar log das inclusões.
        Map<Integer, StringBuilder> mapaLogPlano = new HashMap<Integer, StringBuilder>();
        List<CondicaoPagamentoPlanoTO> listaIncluir = CondicaoPagamentoPlanoTO.consultarListaIncluir(condicaoPagamentoVO.getListaCondicaoPagamentoPlanoAntesDeAlterar(),condicaoPagamentoVO.getListaCondicaoPagamentoPlano());

        if (listaIncluir.size() >0 ){
            for (CondicaoPagamentoPlanoTO obj: listaIncluir){
                StringBuilder msg = mapaLogPlano.get(obj.getPlanoVO().getCodigo());
                if (msg == null){
                    msg = new StringBuilder();
                    msg.append("\nInclusão de condição de pagamento através da tela de cadastro de Condição de Pagamento: \n\n");
                    msg.append("Condição de Pagamento: ").append(this.condicaoPagamentoVO.getCodigo()).append(" - ").append(this.condicaoPagamentoVO.getDescricao()).append("\n");
                    msg.append("Durações do plano (");
                    mapaLogPlano.put(obj.getPlanoVO().getCodigo(), msg);
                }
                msg.append(obj.getDuracaoPlano() + "," );
            }
        }
        // registrar log das exclusões.
        List<CondicaoPagamentoPlanoTO> listaExcluir = CondicaoPagamentoPlanoTO.consultarListaExcluir(condicaoPagamentoVO.getListaCondicaoPagamentoPlanoAntesDeAlterar(),condicaoPagamentoVO.getListaCondicaoPagamentoPlano());
        if (listaExcluir.size() >0 ){
            for (CondicaoPagamentoPlanoTO obj: listaExcluir){
                StringBuilder msg = mapaLogPlano.get(obj.getPlanoVO().getCodigo());
                if (msg == null){
                    msg = new StringBuilder();
                    msg.append("\nExclusão de condição de pagamento através da tela de cadastro de Condição de Pagamento: \n\n");
                    msg.append("Condição de Pagamento: ").append(this.condicaoPagamentoVO.getCodigo()).append(" - ").append(this.condicaoPagamentoVO.getDescricao()).append("\n");
                    msg.append("Durações do plano (");
                    mapaLogPlano.put(obj.getPlanoVO().getCodigo(), msg);
                }
                msg.append(obj.getDuracaoPlano() + "," );
            }
        }
        for (Integer codigoPlano: mapaLogPlano.keySet()){
            String msg = mapaLogPlano.get(codigoPlano).toString();
            msg = msg.substring(0, msg.length() -1);
            registrarLogCondicaoPagamentoPlano(inclusao, codigoPlano, msg + ")");
        }


    }

    private void registrarLogCondicaoPagamentoPlano(boolean inclusao, Integer codigoPlano, String msg)throws Exception{
        LogVO obj = new LogVO();
        obj.setChavePrimaria(String.valueOf(codigoPlano));
        obj.setNomeEntidade("PLANO");
        obj.setNomeEntidadeDescricao("PLANO");
        obj.setResponsavelAlteracao(getUsuarioLogado().getNome());
        obj.setUserOAMD(getUsuarioLogado().getUserOamd());
        obj.setNomeCampo("MENSAGEM");
        if (inclusao){
            obj.setOperacao("INCLUSÃO");
        } else{
            obj.setOperacao("ALTERAÇÃO");
        }
        obj.setValorCampoAlterado(msg.toString());
        obj.setDataAlteracao(negocio.comuns.utilitarias.Calendario.hoje());
        registrarLogObjetoVO(obj, codigoPlano);
    }

    public List<SelectItem> getListaPlano() {
        return listaPlano;
    }

    public void setListaPlano(List<SelectItem> listaPlano) {
        this.listaPlano = listaPlano;
    }

    public CondicaoPagamentoPlanoTO getCondicaoPagamentoPlanoTO() {
        return condicaoPagamentoPlanoTO;
    }

    public void setCondicaoPagamentoPlanoTO(CondicaoPagamentoPlanoTO condicaoPagamentoPlanoTO) {
        this.condicaoPagamentoPlanoTO = condicaoPagamentoPlanoTO;
    }

    public List<SelectItem> getListaDuracao() {
        return listaDuracao;
    }

    public void setListaDuracao(List<SelectItem> listaDuracao) {
        this.listaDuracao = listaDuracao;
    }

    private String processarAlteracoesPlanos() throws Exception {
        String msgCondicoes = "";
        List<String> condicoesNaoExcluidas = getFacade().getPlanoCondicaoPagamento().alterarPlanoCondicaoPagamento(condicaoPagamentoVO);
        if(!condicoesNaoExcluidas.isEmpty()){
           
            for(String condicoes: condicoesNaoExcluidas){
                msgCondicoes += (msgCondicoes.equals("") ? "" : ", ") + condicoes;
            }
            return "Por ter(em) apenas essa condição de pagamento, não foi possível excluir a condição do(s) seguinte(s) plano(s): "+msgCondicoes;
        }
        return msgCondicoes;
    }

    public void setMsgAlert(String msgAlert) {
        this.msgAlert = msgAlert;
    }

    public String getMsgAlert() {
        if (msgAlert == null) {
            return "";
        }
        return msgAlert;
    }


    public void confirmarExcluir(){
        MensagemGenericaControle control = (MensagemGenericaControle) getControlador(MensagemGenericaControle.class);
        control.setMensagemDetalhada("", "");
        setMsgAlert("Richfaces.showModalPanel('mdlMensagemGenerica');");
        control.init("Exclusão de Condição de Pagamento",
                "Deseja excluir a Condição de Pagamento?",
                this, "excluir", "", "", "", "grupoBtnExcluir,msgColaboradorDet");
    }

}

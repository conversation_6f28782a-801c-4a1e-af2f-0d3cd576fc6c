package controle.plano;

import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.estrutura.exportador.controle.ExportadorListaControle;
import controle.basico.clube.MensagemGenericaControle;
import negocio.facade.jdbc.plano.Horario;
import negocio.comuns.utilitarias.*;
import negocio.comuns.plano.*;
import javax.faces.model.SelectItem;
import java.util.List;
import java.util.ArrayList;
import controle.arquitetura.SuperControle;
import controle.arquitetura.security.LoginControle;
import java.util.Collections;
import java.util.Enumeration;
import java.util.Hashtable;
import javax.faces.event.ActionEvent;
import negocio.comuns.arquitetura.LogVO;

/**
 * Classe responsável por implementar a interação entre os componentes JSF das páginas 
 * horarioForm.jsp horarioCons.jsp) com as funcionalidades da classe <code><PERSON>rario</code>.
 * Implemtação da camada controle (Backing Bean).
 * @see SuperControle
 * @see Horario
 * @see HorarioVO
 */
public class HorarioControle extends SuperControle {

    private HorarioVO horarioVO;
    /**
     * Interface <code>HorarioInterfaceFacade</code> responsável pela interconexão da camada de controle com a camada de negócio.
     * Criando uma independência da camada de controle com relação a tenologia de persistência dos dados (DesignPatter: Façade).
     */
    private HorarioDisponibilidadeVO horarioDisponibilidadeVODomingo;
    private HorarioDisponibilidadeVO horarioDisponibilidadeVOSegunda;
    private HorarioDisponibilidadeVO horarioDisponibilidadeVOTerca;
    private HorarioDisponibilidadeVO horarioDisponibilidadeVOQuarta;
    private HorarioDisponibilidadeVO horarioDisponibilidadeVOQuinta;
    private HorarioDisponibilidadeVO horarioDisponibilidadeVOSexta;
    private HorarioDisponibilidadeVO horarioDisponibilidadeVOSabado;
    private HorarioDisponibilidadeVO todos;
    private Boolean desabilitarPreenchimento;
    private String msgAlert;
    private String situacaoFiltro;

    public HorarioControle() throws Exception {
        obterUsuarioLogado();
        inicializarFacades();
        setControleConsulta(new ControleConsulta());
        setMensagemID("");
    }

    /**
     * Rotina responsável por disponibilizar um novo objeto da classe <code>Horario</code>
     * para edição pelo usuário da aplicação.
     */
    public String novo() throws Exception {
        setHorarioVO(new HorarioVO());
        setHorarioDisponibilidadeVODomingo(new HorarioDisponibilidadeVO());
        setHorarioDisponibilidadeVOSegunda(new HorarioDisponibilidadeVO());
        setHorarioDisponibilidadeVOTerca(new HorarioDisponibilidadeVO());
        setHorarioDisponibilidadeVOQuarta(new HorarioDisponibilidadeVO());
        setHorarioDisponibilidadeVOQuinta(new HorarioDisponibilidadeVO());
        setHorarioDisponibilidadeVOSexta(new HorarioDisponibilidadeVO());
        setHorarioDisponibilidadeVOSabado(new HorarioDisponibilidadeVO());
        setTodos(new HorarioDisponibilidadeVO());
        inicializarHorariosDisponivel();
        setDesabilitarPreenchimento(false);
        setSucesso(false);
        setErro(false);
        setMensagemID("msg_entre_dados");
        return "editar";
    }

    public void clonar() {
        horarioVO.setCodigo(new Integer(0));
        horarioVO.setNovoObj(true);
        horarioVO.setDescricao("Cópia " + getHorarioVO().getDescricao());
        setMensagemID("msg_entre_dados");
        setSucesso(true);
        setErro(false);
    }

    /**
     * Rotina responsável por disponibilizar os dados de um objeto da classe <code>Horario</code> para alteração.
     * O objeto desta classe é disponibilizado na session da página (request) para que o JSP correspondente possa disponibilizá-lo para edição.
     */
    public String editar() throws Exception {
        Integer codigoConsulta = Integer.parseInt(request().getParameter("chavePrimaria"));
        try {
            HorarioVO obj = getFacade().getHorario().consultarPorChavePrimaria(codigoConsulta, Uteis.NIVELMONTARDADOS_TODOS);
            obj.setNovoObj(false);
            setHorarioVO(obj);
            getHorarioVO().registrarObjetoVOAntesDaAlteracao();
            getHorarioVO().registrarDisponibilidadesAntesAlteracao();
            if (obj.getLivre()) {
                setDesabilitarPreenchimento(true);
            } else {
                setDesabilitarPreenchimento(false);
            }
            setHorarioDisponibilidadeVODomingo(new HorarioDisponibilidadeVO());
            setHorarioDisponibilidadeVOSegunda(new HorarioDisponibilidadeVO());
            setHorarioDisponibilidadeVOTerca(new HorarioDisponibilidadeVO());
            setHorarioDisponibilidadeVOQuarta(new HorarioDisponibilidadeVO());
            setHorarioDisponibilidadeVOQuinta(new HorarioDisponibilidadeVO());
            setHorarioDisponibilidadeVOSexta(new HorarioDisponibilidadeVO());
            setHorarioDisponibilidadeVOSabado(new HorarioDisponibilidadeVO());
            setTodos(new HorarioDisponibilidadeVO());
        } catch (Exception e) {
            setErro(true);
            setSucesso(false);
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
        return "editar";
    }

    /**
     * Rotina responsável por gravar no BD os dados editados de um novo objeto da classe <code>Horario</code>.
     * Caso o objeto seja novo (ainda não gravado no BD) é acionado a operação <code>incluir()</code>. Caso contrário é acionado o <code>alterar()</code>.
     * Se houver alguma inconsistência o objeto não é gravado, sendo re-apresentado para o usuário juntamente com uma mensagem de erro.
     */
    public String gravar() {
        try {
            if (horarioVO.isNovoObj()) {
                getFacade().getHorario().incluir(horarioVO);
                incluirLogInclusao();
            } else {
                getFacade().getHorario().alterar(horarioVO);
                incluirLogAlteracao();
            }
            setMensagemID("msg_dados_gravados");
            setSucesso(true);
            setErro(false);
            return "editar";
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
            montarErro(e.getMessage());
            return "editar";
        }
    }

    /**
     * Rotina responsavel por executar as consultas disponiveis no JSP HorarioCons.jsp.
     * Define o tipo de consulta a ser executada, por meio de ComboBox denominado campoConsulta, disponivel neste mesmo JSP.
     * Como resultado, disponibiliza um List com os objetos selecionados na sessao da pagina.
     */
    public String consultar() {
        try {
            super.consultar();
            List objs = new ArrayList();
            if (getControleConsulta().getCampoConsulta().equals("codigo")) {
                if (getControleConsulta().getValorConsulta().equals("")) {
                    getControleConsulta().setValorConsulta("0");
                }
                int valorInt = Integer.parseInt(getControleConsulta().getValorConsulta());
                objs = getFacade().getHorario().consultarPorCodigo(valorInt, true, Uteis.NIVELMONTARDADOS_TODOS);
            }
            if (getControleConsulta().getCampoConsulta().equals("descricao")) {
                objs = getFacade().getHorario().consultarPorDescricao(getControleConsulta().getValorConsulta(), true, Uteis.NIVELMONTARDADOS_TODOS);
            }
//            if (getControleConsulta().getCampoConsulta().equals("percentualDesconto")) {
//                if (getControleConsulta().getValorConsulta().equals("")) {
//                    getControleConsulta().setValorConsulta("0");
//                }
//                double valorDouble = Double.parseDouble(getControleConsulta().getValorConsulta());
//                objs = horarioFacade.consultarPorPercentualDesconto(new Double(valorDouble), true, Uteis.NIVELMONTARDADOS_TODOS);
//            }
//            if (getControleConsulta().getCampoConsulta().equals("valorEspecifico")) {
//                if (getControleConsulta().getValorConsulta().equals("")) {
//                    getControleConsulta().setValorConsulta("0");
//                }
//                double valorDouble = Double.parseDouble(getControleConsulta().getValorConsulta());
//                objs = horarioFacade.consultarPorValorEspecifico(new Double(valorDouble), true, Uteis.NIVELMONTARDADOS_TODOS);
//            }
            objs = ControleConsulta.obterSubListPaginaApresentar(objs, controleConsulta);
            definirVisibilidadeLinksNavegacao(controleConsulta.getPaginaAtual(), controleConsulta.getNrTotalPaginas());
            setListaConsulta(objs);
            setMensagemID("msg_dados_consultados");
            setSucesso(true);
            setErro(false);
            return "consultar";
        } catch (Exception e) {
            setListaConsulta(new ArrayList());
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
            return "consultar";
        }
    }

    public void desabilitarGrade() throws Exception {
        if (!getHorarioVO().isNovoObj()) {
            if (getHorarioVO().getLivre()) {
                setDesabilitarPreenchimento(true);
            } else {
                setDesabilitarPreenchimento(false);
                inicializarHorariosDisponivel();
            }
        } else {
            if (getHorarioVO().getLivre()) {
                setDesabilitarPreenchimento(true);
            } else {
                setDesabilitarPreenchimento(false);
            }
        }
    }

    public HorarioDisponibilidadeVO getHorarioDisponibilidadeVO(){
        return (HorarioDisponibilidadeVO) context().getExternalContext().getRequestMap().get("horarioDisponibilidadeVO");
    }

    public void MarcaTodaLinhaMatutino() {
        HorarioDisponibilidadeVO obj = getHorarioDisponibilidadeVO();
        if (obj.getMatutino()) {
            obj.alterarTodoPeriodo(HorarioDisponibilidadeVO.MATUTINO, true);
            ((HorarioDisponibilidadeVO) getHorarioVO().getHorarioDisponibilidadeVOs().get(0)).alterarTodoPeriodo(HorarioDisponibilidadeVO.MATUTINO, true);

            for (Object object : getHorarioVO().getHorarioDisponibilidadeVOs()) {
                HorarioDisponibilidadeVO horarioDisponibilidade = (HorarioDisponibilidadeVO) object;
                if(!horarioDisponibilidade.getHora0500()) {
                    ((HorarioDisponibilidadeVO) getHorarioVO().getHorarioDisponibilidadeVOs().get(0)).setHora0500(false);
                }
                if(!horarioDisponibilidade.getHora0530()) {
                    ((HorarioDisponibilidadeVO) getHorarioVO().getHorarioDisponibilidadeVOs().get(0)).setHora0530(false);
                }
                if(!horarioDisponibilidade.getHora0600()) {
                    ((HorarioDisponibilidadeVO) getHorarioVO().getHorarioDisponibilidadeVOs().get(0)).setHora0600(false);
                }
                if(!horarioDisponibilidade.getHora0630()) {
                    ((HorarioDisponibilidadeVO) getHorarioVO().getHorarioDisponibilidadeVOs().get(0)).setHora0630(false);
                }
                if(!horarioDisponibilidade.getHora0700()) {
                    ((HorarioDisponibilidadeVO) getHorarioVO().getHorarioDisponibilidadeVOs().get(0)).setHora0700(false);
                }
                if(!horarioDisponibilidade.getHora0730()) {
                    ((HorarioDisponibilidadeVO) getHorarioVO().getHorarioDisponibilidadeVOs().get(0)).setHora0730(false);
                }
                if(!horarioDisponibilidade.getHora0800()) {
                    ((HorarioDisponibilidadeVO) getHorarioVO().getHorarioDisponibilidadeVOs().get(0)).setHora0800(false);
                }
                if(!horarioDisponibilidade.getHora0830()) {
                    ((HorarioDisponibilidadeVO) getHorarioVO().getHorarioDisponibilidadeVOs().get(0)).setHora0830(false);
                }
                if(!horarioDisponibilidade.getHora0900()) {
                    ((HorarioDisponibilidadeVO) getHorarioVO().getHorarioDisponibilidadeVOs().get(0)).setHora0900(false);
                }
                if(!horarioDisponibilidade.getHora0930()) {
                    ((HorarioDisponibilidadeVO) getHorarioVO().getHorarioDisponibilidadeVOs().get(0)).setHora0930(false);
                }
                if(!horarioDisponibilidade.getHora1000()) {
                    ((HorarioDisponibilidadeVO) getHorarioVO().getHorarioDisponibilidadeVOs().get(0)).setHora1000(false);
                }
                if(!horarioDisponibilidade.getHora1030()) {
                    ((HorarioDisponibilidadeVO) getHorarioVO().getHorarioDisponibilidadeVOs().get(0)).setHora1030(false);
                }
                if(!horarioDisponibilidade.getHora1100()) {
                    ((HorarioDisponibilidadeVO) getHorarioVO().getHorarioDisponibilidadeVOs().get(0)).setHora1100(false);
                }
                if(!horarioDisponibilidade.getHora1130()) {
                    ((HorarioDisponibilidadeVO) getHorarioVO().getHorarioDisponibilidadeVOs().get(0)).setHora1130(false);
                }
            }
        }

        if (!obj.getMatutino()) {
            obj.alterarTodoPeriodo(HorarioDisponibilidadeVO.MATUTINO, false);
            ((HorarioDisponibilidadeVO) getHorarioVO().getHorarioDisponibilidadeVOs().get(0)).alterarTodoPeriodo(HorarioDisponibilidadeVO.MATUTINO, false);
        }
    }

    public void MarcaTodaLinhaVespertino() {
        HorarioDisponibilidadeVO obj = getHorarioDisponibilidadeVO();

        if (obj.getVespertino()) {
            obj.alterarTodoPeriodo(HorarioDisponibilidadeVO.VESPERTINO, true);
            ((HorarioDisponibilidadeVO) getHorarioVO().getHorarioDisponibilidadeVOs().get(0)).alterarTodoPeriodo(HorarioDisponibilidadeVO.VESPERTINO, true);

            for (Object object : getHorarioVO().getHorarioDisponibilidadeVOs()) {
                HorarioDisponibilidadeVO horarioDisponibilidade = (HorarioDisponibilidadeVO) object;
                if(!horarioDisponibilidade.getHora1200()) {
                    ((HorarioDisponibilidadeVO) getHorarioVO().getHorarioDisponibilidadeVOs().get(0)).setHora1200(false);
                }
                if(!horarioDisponibilidade.getHora1230()) {
                    ((HorarioDisponibilidadeVO) getHorarioVO().getHorarioDisponibilidadeVOs().get(0)).setHora1230(false);
                }
                if(!horarioDisponibilidade.getHora1300()) {
                    ((HorarioDisponibilidadeVO) getHorarioVO().getHorarioDisponibilidadeVOs().get(0)).setHora1300(false);
                }
                if(!horarioDisponibilidade.getHora1330()) {
                    ((HorarioDisponibilidadeVO) getHorarioVO().getHorarioDisponibilidadeVOs().get(0)).setHora1330(false);
                }
                if(!horarioDisponibilidade.getHora1400()) {
                    ((HorarioDisponibilidadeVO) getHorarioVO().getHorarioDisponibilidadeVOs().get(0)).setHora1400(false);
                }
                if(!horarioDisponibilidade.getHora1430()) {
                    ((HorarioDisponibilidadeVO) getHorarioVO().getHorarioDisponibilidadeVOs().get(0)).setHora1430(false);
                }
                if(!horarioDisponibilidade.getHora1500()) {
                    ((HorarioDisponibilidadeVO) getHorarioVO().getHorarioDisponibilidadeVOs().get(0)).setHora1500(false);
                }
                if(!horarioDisponibilidade.getHora1530()) {
                    ((HorarioDisponibilidadeVO) getHorarioVO().getHorarioDisponibilidadeVOs().get(0)).setHora1530(false);
                }
                if(!horarioDisponibilidade.getHora1600()) {
                    ((HorarioDisponibilidadeVO) getHorarioVO().getHorarioDisponibilidadeVOs().get(0)).setHora1600(false);
                }
                if(!horarioDisponibilidade.getHora1630()) {
                    ((HorarioDisponibilidadeVO) getHorarioVO().getHorarioDisponibilidadeVOs().get(0)).setHora1630(false);
                }
                if(!horarioDisponibilidade.getHora1700()) {
                    ((HorarioDisponibilidadeVO) getHorarioVO().getHorarioDisponibilidadeVOs().get(0)).setHora1700(false);
                }
                if(!horarioDisponibilidade.getHora1730()) {
                    ((HorarioDisponibilidadeVO) getHorarioVO().getHorarioDisponibilidadeVOs().get(0)).setHora1730(false);
                }
            }
        }

        if (!obj.getVespertino()) {
            obj.alterarTodoPeriodo(HorarioDisponibilidadeVO.VESPERTINO, false);
            ((HorarioDisponibilidadeVO) getHorarioVO().getHorarioDisponibilidadeVOs().get(0)).alterarTodoPeriodo(HorarioDisponibilidadeVO.VESPERTINO, false);
        }
    }

    public void marcarTodaLinhaMadrugada(){
        HorarioDisponibilidadeVO horaDispVO = getHorarioDisponibilidadeVO();

        if( horaDispVO.getMadrugada() ){
            horaDispVO.alterarTodoPeriodo(HorarioDisponibilidadeVO.MADRUGADA, true);
        }else{
            horaDispVO.alterarTodoPeriodo(HorarioDisponibilidadeVO.MADRUGADA, false);
        }
    }

    public void MarcaTodaLinhaNoturno() {
        HorarioDisponibilidadeVO obj = (HorarioDisponibilidadeVO) context().getExternalContext().getRequestMap().get("horarioDisponibilidadeVO");

        if (obj.getNoturno()) {
            obj.alterarTodoPeriodo(HorarioDisponibilidadeVO.NOTURNO, true);
            ((HorarioDisponibilidadeVO) getHorarioVO().getHorarioDisponibilidadeVOs().get(0)).alterarTodoPeriodo(HorarioDisponibilidadeVO.NOTURNO,true);

            for (Object object : getHorarioVO().getHorarioDisponibilidadeVOs()) {
                HorarioDisponibilidadeVO horarioDisponibilidade = (HorarioDisponibilidadeVO) object;
                if(!horarioDisponibilidade.getHora1800()) {
                    ((HorarioDisponibilidadeVO) getHorarioVO().getHorarioDisponibilidadeVOs().get(0)).setHora1800(false);
                }
                if(!horarioDisponibilidade.getHora1830()) {
                    ((HorarioDisponibilidadeVO) getHorarioVO().getHorarioDisponibilidadeVOs().get(0)).setHora1830(false);
                }
                if(!horarioDisponibilidade.getHora1900()) {
                    ((HorarioDisponibilidadeVO) getHorarioVO().getHorarioDisponibilidadeVOs().get(0)).setHora1900(false);
                }
                if(!horarioDisponibilidade.getHora1930()) {
                    ((HorarioDisponibilidadeVO) getHorarioVO().getHorarioDisponibilidadeVOs().get(0)).setHora1930(false);
                }
                if(!horarioDisponibilidade.getHora2000()) {
                    ((HorarioDisponibilidadeVO) getHorarioVO().getHorarioDisponibilidadeVOs().get(0)).setHora2000(false);
                }
                if(!horarioDisponibilidade.getHora2030()) {
                    ((HorarioDisponibilidadeVO) getHorarioVO().getHorarioDisponibilidadeVOs().get(0)).setHora2030(false);
                }
                if(!horarioDisponibilidade.getHora2100()) {
                    ((HorarioDisponibilidadeVO) getHorarioVO().getHorarioDisponibilidadeVOs().get(0)).setHora2100(false);
                }
                if(!horarioDisponibilidade.getHora2130()) {
                    ((HorarioDisponibilidadeVO) getHorarioVO().getHorarioDisponibilidadeVOs().get(0)).setHora2130(false);
                }
                if(!horarioDisponibilidade.getHora2200()) {
                    ((HorarioDisponibilidadeVO) getHorarioVO().getHorarioDisponibilidadeVOs().get(0)).setHora2200(false);
                }
                if(!horarioDisponibilidade.getHora2230()) {
                    ((HorarioDisponibilidadeVO) getHorarioVO().getHorarioDisponibilidadeVOs().get(0)).setHora2230(false);
                }
                if(!horarioDisponibilidade.getHora2300()) {
                    ((HorarioDisponibilidadeVO) getHorarioVO().getHorarioDisponibilidadeVOs().get(0)).setHora2300(false);
                }
                if(!horarioDisponibilidade.getHora2330()) {
                    ((HorarioDisponibilidadeVO) getHorarioVO().getHorarioDisponibilidadeVOs().get(0)).setHora2330(false);
                }
            }
        }

        if (!obj.getNoturno()) {
            obj.alterarTodoPeriodo(HorarioDisponibilidadeVO.NOTURNO, false);
            ((HorarioDisponibilidadeVO) getHorarioVO().getHorarioDisponibilidadeVOs().get(0)).alterarTodoPeriodo(HorarioDisponibilidadeVO.NOTURNO, false);
        }
    }
    private void deveSetarMatutino(HorarioDisponibilidadeVO obj, boolean valorParaSetar) {
        if(obj.getHora0500() && obj.getHora0530() && obj.getHora0600()
                && obj.getHora0630() && obj.getHora0700() && obj.getHora0730()
                && obj.getHora0800() && obj.getHora0830() && obj.getHora0900()
                && obj.getHora0930() && obj.getHora1000() && obj.getHora1030()
                && obj.getHora1100() && obj.getHora1130()) {
            obj.setMatutino(valorParaSetar);
        }
    }

    private void deveSetarVespertino(HorarioDisponibilidadeVO horarioDisponibilidade) {
        if (horarioDisponibilidade.getHora1200() && horarioDisponibilidade.getHora1230() && horarioDisponibilidade.getHora1300()
                && horarioDisponibilidade.getHora1330() && horarioDisponibilidade.getHora1400() && horarioDisponibilidade.getHora1430()
                && horarioDisponibilidade.getHora1500() && horarioDisponibilidade.getHora1530() && horarioDisponibilidade.getHora1600()
                && horarioDisponibilidade.getHora1630() && horarioDisponibilidade.getHora1700() && horarioDisponibilidade.getHora1730()) {
            horarioDisponibilidade.setVespertino(true);
        }
    }

    private void deveSetarNoturno(HorarioDisponibilidadeVO horarioDisponibilidade) {
        if (horarioDisponibilidade.getHora1800() && horarioDisponibilidade.getHora1830() && horarioDisponibilidade.getHora1900()
                && horarioDisponibilidade.getHora1930() && horarioDisponibilidade.getHora2000() && horarioDisponibilidade.getHora2030()
                && horarioDisponibilidade.getHora2100() && horarioDisponibilidade.getHora2130() && horarioDisponibilidade.getHora2200()
                && horarioDisponibilidade.getHora2230() && horarioDisponibilidade.getHora2300() && horarioDisponibilidade.getHora2330()) {
            horarioDisponibilidade.setNoturno(true);
        }
    }

    private void deveSetarMadrugada(HorarioDisponibilidadeVO horDispVO) {
        if (horDispVO.getHora0000() && horDispVO.getHora0030()
                && horDispVO.getHora0100() && horDispVO.getHora0130()
                && horDispVO.getHora0200() && horDispVO.getHora0230()
                && horDispVO.getHora0300() && horDispVO.getHora0330()
                && horDispVO.getHora0400() && horDispVO.getHora0430()
                && horDispVO.getHora0500() && horDispVO.getHora0530()) {
            horDispVO.setMadrugada(true);
        }
    }

    public void MarcaTodaColuna0500() {
        HorarioDisponibilidadeVO obj = (HorarioDisponibilidadeVO) context().getExternalContext().getRequestMap().get("horarioDisponibilidadeVO");        
        if (obj.getIdentificador().equals("")) {
            if (obj.getHora0500()) {
                for (Object o : horarioVO.getHorarioDisponibilidadeVOs()) {
                    HorarioDisponibilidadeVO horarioDisponibilidade = (HorarioDisponibilidadeVO) o;
                    horarioDisponibilidade.setHora0500(true);
                    deveSetarMatutino(horarioDisponibilidade, true);
                }
            }
            if (!obj.getHora0500()) {
                for (Object o : horarioVO.getHorarioDisponibilidadeVOs()) {
                    HorarioDisponibilidadeVO horarioDisponibilidade = (HorarioDisponibilidadeVO) o;
                    horarioDisponibilidade.setHora0500(false);
                    horarioDisponibilidade.setMatutino(false);
                }
            }
        } else {
            // se campo foi marcado, validar se toda a coluna ou toda a linha está marcada
            if(obj.getHora0500()) {
                ((HorarioDisponibilidadeVO) getHorarioVO().getHorarioDisponibilidadeVOs().get(0)).setHora0500(true);
                for(HorarioDisponibilidadeVO horarioDisponibilidade : (ArrayList<HorarioDisponibilidadeVO>) horarioVO.getHorarioDisponibilidadeVOs()) {
                    if(!horarioDisponibilidade.getHora0500()) {
                        ((HorarioDisponibilidadeVO) getHorarioVO().getHorarioDisponibilidadeVOs().get(0)).setHora0500(false);
                    }
                }
                deveSetarMatutino(obj, true);
            } else {
                obj.setMatutino(false);
                ((HorarioDisponibilidadeVO) getHorarioVO().getHorarioDisponibilidadeVOs().get(0)).setHora0500(false);
            }
        }
    }

    public void MarcaTodaColuna0530() {
        HorarioDisponibilidadeVO obj = (HorarioDisponibilidadeVO) context().getExternalContext().getRequestMap().get("horarioDisponibilidadeVO");
        if (obj.getIdentificador().equals("")) {
            if (obj.getHora0530()) {
                for (Object o : horarioVO.getHorarioDisponibilidadeVOs()) {
                    HorarioDisponibilidadeVO horarioDisponibilidade = (HorarioDisponibilidadeVO) o;
                    horarioDisponibilidade.setHora0530(true);

                    deveSetarMatutino(horarioDisponibilidade, true);
                }
            }
            if (!obj.getHora0530()) {
                for (Object o : horarioVO.getHorarioDisponibilidadeVOs()) {
                    HorarioDisponibilidadeVO horarioDisponibilidade = (HorarioDisponibilidadeVO) o;
                    horarioDisponibilidade.setHora0530(false);
                    horarioDisponibilidade.setMatutino(false);
                }
            }
        } else {
            // se campo foi marcado, validar se toda a coluna ou toda a linha está marcada
            if(obj.getHora0530()) {
                ((HorarioDisponibilidadeVO) getHorarioVO().getHorarioDisponibilidadeVOs().get(0)).setHora0530(true);
                for(HorarioDisponibilidadeVO horarioDisponibilidade : (ArrayList<HorarioDisponibilidadeVO>) horarioVO.getHorarioDisponibilidadeVOs()) {
                    if(!horarioDisponibilidade.getHora0530()) {
                        ((HorarioDisponibilidadeVO) getHorarioVO().getHorarioDisponibilidadeVOs().get(0)).setHora0530(false);
                    }
                }
                deveSetarMatutino(obj, true);
            } else {
                obj.setMatutino(false);
                ((HorarioDisponibilidadeVO) getHorarioVO().getHorarioDisponibilidadeVOs().get(0)).setHora0530(false);
            }
        }
    }

    public void MarcaTodaColuna0600() {
        HorarioDisponibilidadeVO obj = (HorarioDisponibilidadeVO) context().getExternalContext().getRequestMap().get("horarioDisponibilidadeVO");        
        if (obj.getIdentificador().equals("")) {
            if (obj.getHora0600()) {
                for (Object o : horarioVO.getHorarioDisponibilidadeVOs()) {
                    HorarioDisponibilidadeVO horarioDisponibilidade = (HorarioDisponibilidadeVO) o;
                    horarioDisponibilidade.setHora0600(true);

                    deveSetarMatutino(horarioDisponibilidade, true);
                }
            }
            if (!obj.getHora0600()) {
                for (Object o : horarioVO.getHorarioDisponibilidadeVOs()) {
                    HorarioDisponibilidadeVO horarioDisponibilidade = (HorarioDisponibilidadeVO) o;
                    horarioDisponibilidade.setHora0600(false);
                    horarioDisponibilidade.setMatutino(false);
                }
            }
        } else {
            // se campo foi marcado, validar se toda a coluna ou toda a linha está marcada
            if(obj.getHora0600()) {
                ((HorarioDisponibilidadeVO) getHorarioVO().getHorarioDisponibilidadeVOs().get(0)).setHora0600(true);
                for(HorarioDisponibilidadeVO horarioDisponibilidade : (ArrayList<HorarioDisponibilidadeVO>) horarioVO.getHorarioDisponibilidadeVOs()) {
                    if(!horarioDisponibilidade.getHora0600()) {
                        ((HorarioDisponibilidadeVO) getHorarioVO().getHorarioDisponibilidadeVOs().get(0)).setHora0600(false);
                    }
                }
                deveSetarMatutino(obj, true);
            } else {
                obj.setMatutino(false);
                ((HorarioDisponibilidadeVO) getHorarioVO().getHorarioDisponibilidadeVOs().get(0)).setHora0600(false);
            }
        }
    }

    public void MarcaTodaColuna0630() {
        HorarioDisponibilidadeVO obj = (HorarioDisponibilidadeVO) context().getExternalContext().getRequestMap().get("horarioDisponibilidadeVO");        
        if (obj.getIdentificador().equals("")) {
            if (obj.getHora0630()) {
                for (Object o : horarioVO.getHorarioDisponibilidadeVOs()) {
                    HorarioDisponibilidadeVO horarioDisponibilidade = (HorarioDisponibilidadeVO) o;
                    horarioDisponibilidade.setHora0630(true);

                    deveSetarMatutino(horarioDisponibilidade, true);
                }
            }
            if (!obj.getHora0630()) {
                for (Object o : horarioVO.getHorarioDisponibilidadeVOs()) {
                    HorarioDisponibilidadeVO horarioDisponibilidade = (HorarioDisponibilidadeVO) o;
                    horarioDisponibilidade.setHora0630(false);
                    horarioDisponibilidade.setMatutino(false);
                }
            }
        } else {
            // se campo foi marcado, validar se toda a coluna ou toda a linha está marcada
            if(obj.getHora0630()) {
                ((HorarioDisponibilidadeVO) getHorarioVO().getHorarioDisponibilidadeVOs().get(0)).setHora0630(true);
                for(HorarioDisponibilidadeVO horarioDisponibilidade : (ArrayList<HorarioDisponibilidadeVO>) horarioVO.getHorarioDisponibilidadeVOs()) {
                    if(!horarioDisponibilidade.getHora0630()) {
                        ((HorarioDisponibilidadeVO) getHorarioVO().getHorarioDisponibilidadeVOs().get(0)).setHora0630(false);
                    }
                }
                deveSetarMatutino(obj, true);
            } else {
                obj.setMatutino(false);
                ((HorarioDisponibilidadeVO) getHorarioVO().getHorarioDisponibilidadeVOs().get(0)).setHora0630(false);
            }
        }
    }

    public void MarcaTodaColuna0700() {
        HorarioDisponibilidadeVO obj = (HorarioDisponibilidadeVO) context().getExternalContext().getRequestMap().get("horarioDisponibilidadeVO");        
        if (obj.getIdentificador().equals("")) {
            if (obj.getHora0700()) {
                for (Object o : horarioVO.getHorarioDisponibilidadeVOs()) {
                    HorarioDisponibilidadeVO horarioDisponibilidade = (HorarioDisponibilidadeVO) o;
                    horarioDisponibilidade.setHora0700(true);
                    deveSetarMatutino(horarioDisponibilidade, true);
                }
            }
            if (!obj.getHora0700()) {
                for (Object o : horarioVO.getHorarioDisponibilidadeVOs()) {
                    HorarioDisponibilidadeVO horarioDisponibilidade = (HorarioDisponibilidadeVO) o;
                    horarioDisponibilidade.setHora0700(false);
                    horarioDisponibilidade.setMatutino(false);
                }
            }
        } else {
            // se campo foi marcado, validar se toda a coluna ou toda a linha está marcada
            if(obj.getHora0700()) {
                ((HorarioDisponibilidadeVO) getHorarioVO().getHorarioDisponibilidadeVOs().get(0)).setHora0700(true);
                for(HorarioDisponibilidadeVO horarioDisponibilidade : (ArrayList<HorarioDisponibilidadeVO>) horarioVO.getHorarioDisponibilidadeVOs()) {
                    if(!horarioDisponibilidade.getHora0700()) {
                        ((HorarioDisponibilidadeVO) getHorarioVO().getHorarioDisponibilidadeVOs().get(0)).setHora0700(false);
                    }
                }
                deveSetarMatutino(obj, true);
            } else {
                obj.setMatutino(false);
                ((HorarioDisponibilidadeVO) getHorarioVO().getHorarioDisponibilidadeVOs().get(0)).setHora0700(false);
            }
        }
    }

    public void MarcaTodaColuna0730() {
        HorarioDisponibilidadeVO obj = (HorarioDisponibilidadeVO) context().getExternalContext().getRequestMap().get("horarioDisponibilidadeVO");        
        if (obj.getIdentificador().equals("")) {
            if (obj.getHora0730()) {
                for (Object o : horarioVO.getHorarioDisponibilidadeVOs()) {
                    HorarioDisponibilidadeVO horarioDisponibilidade = (HorarioDisponibilidadeVO) o;
                    horarioDisponibilidade.setHora0730(true);
                    deveSetarMatutino(horarioDisponibilidade, true);
                }
            }
            if (!obj.getHora0730()) {
                for (Object o : horarioVO.getHorarioDisponibilidadeVOs()) {
                    HorarioDisponibilidadeVO horarioDisponibilidade = (HorarioDisponibilidadeVO) o;
                    horarioDisponibilidade.setHora0730(false);
                    horarioDisponibilidade.setMatutino(false);
                }
            }
        } else {
            // se campo foi marcado, validar se toda a coluna ou toda a linha está marcada
            if(obj.getHora0730()) {
                ((HorarioDisponibilidadeVO) getHorarioVO().getHorarioDisponibilidadeVOs().get(0)).setHora0730(true);
                for(HorarioDisponibilidadeVO horarioDisponibilidade : (ArrayList<HorarioDisponibilidadeVO>) horarioVO.getHorarioDisponibilidadeVOs()) {
                    if(!horarioDisponibilidade.getHora0730()) {
                        ((HorarioDisponibilidadeVO) getHorarioVO().getHorarioDisponibilidadeVOs().get(0)).setHora0730(false);
                    }
                }
                deveSetarMatutino(obj, true);
            } else {
                obj.setMatutino(false);
                ((HorarioDisponibilidadeVO) getHorarioVO().getHorarioDisponibilidadeVOs().get(0)).setHora0730(false);
            }
        }
    }

    public void MarcaTodaColuna0800() {
        HorarioDisponibilidadeVO obj = (HorarioDisponibilidadeVO) context().getExternalContext().getRequestMap().get("horarioDisponibilidadeVO");        
        if (obj.getIdentificador().equals("")) {
            if (obj.getHora0800()) {
                for (Object o : horarioVO.getHorarioDisponibilidadeVOs()) {
                    HorarioDisponibilidadeVO horarioDisponibilidade = (HorarioDisponibilidadeVO) o;
                    horarioDisponibilidade.setHora0800(true);
                    deveSetarMatutino(horarioDisponibilidade, true);
                }
            }
            if (!obj.getHora0800()) {
                for (Object o : horarioVO.getHorarioDisponibilidadeVOs()) {
                    HorarioDisponibilidadeVO horarioDisponibilidade = (HorarioDisponibilidadeVO) o;
                    horarioDisponibilidade.setHora0800(false);
                    horarioDisponibilidade.setMatutino(false);
                }
            }
        } else {
            // se campo foi marcado, validar se toda a coluna ou toda a linha está marcada
            if(obj.getHora0800()) {
                ((HorarioDisponibilidadeVO) getHorarioVO().getHorarioDisponibilidadeVOs().get(0)).setHora0800(true);
                for(HorarioDisponibilidadeVO horarioDisponibilidade : (ArrayList<HorarioDisponibilidadeVO>) horarioVO.getHorarioDisponibilidadeVOs()) {
                    if(!horarioDisponibilidade.getHora0800()) {
                        ((HorarioDisponibilidadeVO) getHorarioVO().getHorarioDisponibilidadeVOs().get(0)).setHora0800(false);
                    }
                }
                deveSetarMatutino(obj, true);
            } else {
                obj.setMatutino(false);
                ((HorarioDisponibilidadeVO) getHorarioVO().getHorarioDisponibilidadeVOs().get(0)).setHora0800(false);
            }
        }
    }

    public void MarcaTodaColuna0830() {
        HorarioDisponibilidadeVO obj = (HorarioDisponibilidadeVO) context().getExternalContext().getRequestMap().get("horarioDisponibilidadeVO");        
        if (obj.getIdentificador().equals("")) {
            if (obj.getHora0830()) {
                for (Object o : horarioVO.getHorarioDisponibilidadeVOs()) {
                    HorarioDisponibilidadeVO horarioDisponibilidade = (HorarioDisponibilidadeVO) o;
                    horarioDisponibilidade.setHora0830(true);
                    deveSetarMatutino(horarioDisponibilidade, true);
                }
            }
            if (!obj.getHora0830()) {
                for (Object o : horarioVO.getHorarioDisponibilidadeVOs()) {
                    HorarioDisponibilidadeVO horarioDisponibilidade = (HorarioDisponibilidadeVO) o;
                    horarioDisponibilidade.setHora0830(false);
                    horarioDisponibilidade.setMatutino(false);
                }
            }
        } else {
            // se campo foi marcado, validar se toda a coluna ou toda a linha está marcada
            if(obj.getHora0830()) {
                ((HorarioDisponibilidadeVO) getHorarioVO().getHorarioDisponibilidadeVOs().get(0)).setHora0830(true);
                for(HorarioDisponibilidadeVO horarioDisponibilidade : (ArrayList<HorarioDisponibilidadeVO>) horarioVO.getHorarioDisponibilidadeVOs()) {
                    if(!horarioDisponibilidade.getHora0830()) {
                        ((HorarioDisponibilidadeVO) getHorarioVO().getHorarioDisponibilidadeVOs().get(0)).setHora0830(false);
                    }
                }
                deveSetarMatutino(obj, true);
            } else {
                obj.setMatutino(false);
                ((HorarioDisponibilidadeVO) getHorarioVO().getHorarioDisponibilidadeVOs().get(0)).setHora0830(false);
            }
        }
    }

    public void MarcaTodaColuna0900() {
        HorarioDisponibilidadeVO obj = (HorarioDisponibilidadeVO) context().getExternalContext().getRequestMap().get("horarioDisponibilidadeVO");        
        if (obj.getIdentificador().equals("")) {
            if (obj.getHora0900()) {
                for (Object o : horarioVO.getHorarioDisponibilidadeVOs()) {
                    HorarioDisponibilidadeVO horarioDisponibilidade = (HorarioDisponibilidadeVO) o;
                    horarioDisponibilidade.setHora0900(true);
                    deveSetarMatutino(horarioDisponibilidade, true);
                }
            }
            if (!obj.getHora0900()) {
                for (Object o : horarioVO.getHorarioDisponibilidadeVOs()) {
                    HorarioDisponibilidadeVO horarioDisponibilidade = (HorarioDisponibilidadeVO) o;
                    horarioDisponibilidade.setHora0900(false);
                    horarioDisponibilidade.setMatutino(false);
                }
            }
        } else {
            // se campo foi marcado, validar se toda a coluna ou toda a linha está marcada
            if(obj.getHora0900()) {
                ((HorarioDisponibilidadeVO) getHorarioVO().getHorarioDisponibilidadeVOs().get(0)).setHora0900(true);
                for(HorarioDisponibilidadeVO horarioDisponibilidade : (ArrayList<HorarioDisponibilidadeVO>) horarioVO.getHorarioDisponibilidadeVOs()) {
                    if(!horarioDisponibilidade.getHora0900()) {
                        ((HorarioDisponibilidadeVO) getHorarioVO().getHorarioDisponibilidadeVOs().get(0)).setHora0900(false);
                    }
                }
                deveSetarMatutino(obj, true);
            } else {
                obj.setMatutino(false);
                ((HorarioDisponibilidadeVO) getHorarioVO().getHorarioDisponibilidadeVOs().get(0)).setHora0900(false);
            }
        }
    }

    public void MarcaTodaColuna0930() {
        HorarioDisponibilidadeVO obj = (HorarioDisponibilidadeVO) context().getExternalContext().getRequestMap().get("horarioDisponibilidadeVO");        
        if (obj.getIdentificador().equals("")) {
            if (obj.getHora0930()) {
                for (Object o : horarioVO.getHorarioDisponibilidadeVOs()) {
                    HorarioDisponibilidadeVO horarioDisponibilidade = (HorarioDisponibilidadeVO) o;
                    horarioDisponibilidade.setHora0930(true);
                    deveSetarMatutino(horarioDisponibilidade, true);
                }
            }
            if (!obj.getHora0930()) {
                for (Object o : horarioVO.getHorarioDisponibilidadeVOs()) {
                    HorarioDisponibilidadeVO horarioDisponibilidade = (HorarioDisponibilidadeVO) o;
                    horarioDisponibilidade.setHora0930(false);
                    horarioDisponibilidade.setMatutino(false);
                }
            }
        } else {
            // se campo foi marcado, validar se toda a coluna ou toda a linha está marcada
            if(obj.getHora0930()) {
                ((HorarioDisponibilidadeVO) getHorarioVO().getHorarioDisponibilidadeVOs().get(0)).setHora0930(true);
                for(HorarioDisponibilidadeVO horarioDisponibilidade : (ArrayList<HorarioDisponibilidadeVO>) horarioVO.getHorarioDisponibilidadeVOs()) {
                    if(!horarioDisponibilidade.getHora0930()) {
                        ((HorarioDisponibilidadeVO) getHorarioVO().getHorarioDisponibilidadeVOs().get(0)).setHora0930(false);
                    }
                }
                deveSetarMatutino(obj, true);
            } else {
                obj.setMatutino(false);
                ((HorarioDisponibilidadeVO) getHorarioVO().getHorarioDisponibilidadeVOs().get(0)).setHora0930(false);
            }
        }
    }

    public void MarcaTodaColuna1000() {
        HorarioDisponibilidadeVO obj = (HorarioDisponibilidadeVO) context().getExternalContext().getRequestMap().get("horarioDisponibilidadeVO");        
        if (obj.getIdentificador().equals("")) {
            if (obj.getHora1000()) {
                for (Object o : horarioVO.getHorarioDisponibilidadeVOs()) {
                    HorarioDisponibilidadeVO horarioDisponibilidade = (HorarioDisponibilidadeVO) o;
                    horarioDisponibilidade.setHora1000(true);
                    deveSetarMatutino(horarioDisponibilidade, true);
                }
            }
            if (!obj.getHora1000()) {
                for (Object o : horarioVO.getHorarioDisponibilidadeVOs()) {
                    HorarioDisponibilidadeVO horarioDisponibilidade = (HorarioDisponibilidadeVO) o;
                    horarioDisponibilidade.setHora1000(false);
                    horarioDisponibilidade.setMatutino(false);
                }
            }
        } else {
            // se campo foi marcado, validar se toda a coluna ou toda a linha está marcada
            if(obj.getHora1000()) {
                ((HorarioDisponibilidadeVO) getHorarioVO().getHorarioDisponibilidadeVOs().get(0)).setHora1000(true);
                for(HorarioDisponibilidadeVO horarioDisponibilidade : (ArrayList<HorarioDisponibilidadeVO>) horarioVO.getHorarioDisponibilidadeVOs()) {
                    if(!horarioDisponibilidade.getHora1000()) {
                        ((HorarioDisponibilidadeVO) getHorarioVO().getHorarioDisponibilidadeVOs().get(0)).setHora1000(false);
                    }
                }
                deveSetarMatutino(obj, true);
            } else {
                obj.setMatutino(false);
                ((HorarioDisponibilidadeVO) getHorarioVO().getHorarioDisponibilidadeVOs().get(0)).setHora1000(false);
            }
        }
    }

    public void MarcaTodaColuna1030() {
        HorarioDisponibilidadeVO obj = (HorarioDisponibilidadeVO) context().getExternalContext().getRequestMap().get("horarioDisponibilidadeVO");        
        if (obj.getIdentificador().equals("")) {
            if (obj.getHora1030()) {
                for (Object o : horarioVO.getHorarioDisponibilidadeVOs()) {
                    HorarioDisponibilidadeVO horarioDisponibilidade = (HorarioDisponibilidadeVO) o;
                    horarioDisponibilidade.setHora1030(true);
                    deveSetarMatutino(horarioDisponibilidade, true);
                }
            }
            if (!obj.getHora1030()) {
                for (Object o : horarioVO.getHorarioDisponibilidadeVOs()) {
                    HorarioDisponibilidadeVO horarioDisponibilidade = (HorarioDisponibilidadeVO) o;
                    horarioDisponibilidade.setHora1030(false);
                    horarioDisponibilidade.setMatutino(false);
                }
            }
        } else {
            // se campo foi marcado, validar se toda a coluna ou toda a linha está marcada
            if(obj.getHora1030()) {
                ((HorarioDisponibilidadeVO) getHorarioVO().getHorarioDisponibilidadeVOs().get(0)).setHora1030(true);
                for(HorarioDisponibilidadeVO horarioDisponibilidade : (ArrayList<HorarioDisponibilidadeVO>) horarioVO.getHorarioDisponibilidadeVOs()) {
                    if(!horarioDisponibilidade.getHora1030()) {
                        ((HorarioDisponibilidadeVO) getHorarioVO().getHorarioDisponibilidadeVOs().get(0)).setHora1030(false);
                    }
                }
                deveSetarMatutino(obj, true);
            } else {
                obj.setMatutino(false);
                ((HorarioDisponibilidadeVO) getHorarioVO().getHorarioDisponibilidadeVOs().get(0)).setHora1030(false);
            }
        }
    }

    public void MarcaTodaColuna1100() {
        HorarioDisponibilidadeVO obj = (HorarioDisponibilidadeVO) context().getExternalContext().getRequestMap().get("horarioDisponibilidadeVO");        
        if (obj.getIdentificador().equals("")) {
            if (obj.getHora1100()) {
                for (Object o : horarioVO.getHorarioDisponibilidadeVOs()) {
                    HorarioDisponibilidadeVO horarioDisponibilidade = (HorarioDisponibilidadeVO) o;
                    horarioDisponibilidade.setHora1100(true);
                    deveSetarMatutino(horarioDisponibilidade, true);
                }
            }
            if (!obj.getHora1100()) {
                for (Object o : horarioVO.getHorarioDisponibilidadeVOs()) {
                    HorarioDisponibilidadeVO horarioDisponibilidade = (HorarioDisponibilidadeVO) o;
                    horarioDisponibilidade.setHora1100(false);
                    horarioDisponibilidade.setMatutino(false);
                }
            }
        } else {
            // se campo foi marcado, validar se toda a coluna ou toda a linha está marcada
            if(obj.getHora1100()) {
                ((HorarioDisponibilidadeVO) getHorarioVO().getHorarioDisponibilidadeVOs().get(0)).setHora1100(true);
                for(HorarioDisponibilidadeVO horarioDisponibilidade : (ArrayList<HorarioDisponibilidadeVO>) horarioVO.getHorarioDisponibilidadeVOs()) {
                    if(!horarioDisponibilidade.getHora1100()) {
                        ((HorarioDisponibilidadeVO) getHorarioVO().getHorarioDisponibilidadeVOs().get(0)).setHora1100(false);
                    }
                }
                deveSetarMatutino(obj, true);
            } else {
                obj.setMatutino(false);
                ((HorarioDisponibilidadeVO) getHorarioVO().getHorarioDisponibilidadeVOs().get(0)).setHora1100(false);
            }
        }
    }

    public void MarcaTodaColuna1130() {
        HorarioDisponibilidadeVO obj = (HorarioDisponibilidadeVO) context().getExternalContext().getRequestMap().get("horarioDisponibilidadeVO");        
        if (obj.getIdentificador().equals("")) {
            if (obj.getHora1130()) {
                for (Object o : horarioVO.getHorarioDisponibilidadeVOs()) {
                    HorarioDisponibilidadeVO horarioDisponibilidade = (HorarioDisponibilidadeVO) o;
                    horarioDisponibilidade.setHora1130(true);
                    deveSetarMatutino(horarioDisponibilidade, true);
                }
            }
            if (!obj.getHora1130()) {
                for (Object o : horarioVO.getHorarioDisponibilidadeVOs()) {
                    HorarioDisponibilidadeVO horarioDisponibilidade = (HorarioDisponibilidadeVO) o;
                    horarioDisponibilidade.setHora1130(false);
                    horarioDisponibilidade.setMatutino(false);
                }
            }
        } else {
            // se campo foi marcado, validar se toda a coluna ou toda a linha está marcada
            if(obj.getHora1130()) {
                ((HorarioDisponibilidadeVO) getHorarioVO().getHorarioDisponibilidadeVOs().get(0)).setHora1130(true);
                for(HorarioDisponibilidadeVO horarioDisponibilidade : (ArrayList<HorarioDisponibilidadeVO>) horarioVO.getHorarioDisponibilidadeVOs()) {
                    if(!horarioDisponibilidade.getHora1130()) {
                        ((HorarioDisponibilidadeVO) getHorarioVO().getHorarioDisponibilidadeVOs().get(0)).setHora1130(false);
                    }
                }
                deveSetarMatutino(obj, true);
            } else {
                obj.setMatutino(false);
                ((HorarioDisponibilidadeVO) getHorarioVO().getHorarioDisponibilidadeVOs().get(0)).setHora1130(false);
            }
        }
    }

    public void MarcaTodaColuna1200() {
        HorarioDisponibilidadeVO obj = (HorarioDisponibilidadeVO) context().getExternalContext().getRequestMap().get("horarioDisponibilidadeVO");        
        if (obj.getIdentificador().equals("")) {
            if (obj.getHora1200()) {
                for (Object o : horarioVO.getHorarioDisponibilidadeVOs()) {
                    HorarioDisponibilidadeVO horarioDisponibilidade = (HorarioDisponibilidadeVO) o;
                    horarioDisponibilidade.setHora1200(true);

                    deveSetarVespertino(horarioDisponibilidade);
                }
            }
            if (!obj.getHora1200()) {
                for (Object o : horarioVO.getHorarioDisponibilidadeVOs()) {
                    HorarioDisponibilidadeVO horarioDisponibilidade = (HorarioDisponibilidadeVO) o;
                    horarioDisponibilidade.setHora1200(false);
                    horarioDisponibilidade.setVespertino(false);
                }
            }
        } else {
            // se campo foi marcado, validar se toda a coluna ou toda a linha está marcada
            if(obj.getHora1200()) {
                ((HorarioDisponibilidadeVO) getHorarioVO().getHorarioDisponibilidadeVOs().get(0)).setHora1200(true);
                for(HorarioDisponibilidadeVO horarioDisponibilidade : (ArrayList<HorarioDisponibilidadeVO>) horarioVO.getHorarioDisponibilidadeVOs()) {
                    if(!horarioDisponibilidade.getHora1200()) {
                        ((HorarioDisponibilidadeVO) getHorarioVO().getHorarioDisponibilidadeVOs().get(0)).setHora1200(false);
                    }
                }
                deveSetarVespertino(obj);
            } else {
                obj.setVespertino(false);
                ((HorarioDisponibilidadeVO) getHorarioVO().getHorarioDisponibilidadeVOs().get(0)).setHora1200(false);
            }
        }
    }

    public void MarcaTodaColuna1230() {
        HorarioDisponibilidadeVO obj = (HorarioDisponibilidadeVO) context().getExternalContext().getRequestMap().get("horarioDisponibilidadeVO");        
        if (obj.getIdentificador().equals("")) {
            if (obj.getHora1230()) {
                for (Object o : horarioVO.getHorarioDisponibilidadeVOs()) {
                    HorarioDisponibilidadeVO horarioDisponibilidade = (HorarioDisponibilidadeVO) o;
                    horarioDisponibilidade.setHora1230(true);

                    deveSetarVespertino(horarioDisponibilidade);
                }
            }
            if (!obj.getHora1230()) {
                for (Object o : horarioVO.getHorarioDisponibilidadeVOs()) {
                    HorarioDisponibilidadeVO horarioDisponibilidade = (HorarioDisponibilidadeVO) o;
                    horarioDisponibilidade.setHora1230(false);
                    horarioDisponibilidade.setVespertino(false);
                }
            }
        } else {
            // se campo foi marcado, validar se toda a coluna ou toda a linha está marcada
            if(obj.getHora1230()) {
                ((HorarioDisponibilidadeVO) getHorarioVO().getHorarioDisponibilidadeVOs().get(0)).setHora1230(true);
                for(HorarioDisponibilidadeVO horarioDisponibilidade : (ArrayList<HorarioDisponibilidadeVO>) horarioVO.getHorarioDisponibilidadeVOs()) {
                    if(!horarioDisponibilidade.getHora1230()) {
                        ((HorarioDisponibilidadeVO) getHorarioVO().getHorarioDisponibilidadeVOs().get(0)).setHora1230(false);
                    }
                }
                deveSetarVespertino(obj);
            } else {
                obj.setVespertino(false);
                ((HorarioDisponibilidadeVO) getHorarioVO().getHorarioDisponibilidadeVOs().get(0)).setHora1230(false);
            }
        }
    }

    public void MarcaTodaColuna1300() {
        HorarioDisponibilidadeVO obj = (HorarioDisponibilidadeVO) context().getExternalContext().getRequestMap().get("horarioDisponibilidadeVO");        
        if (obj.getIdentificador().equals("")) {
            if (obj.getHora1300()) {
                for (Object o : horarioVO.getHorarioDisponibilidadeVOs()) {
                    HorarioDisponibilidadeVO horarioDisponibilidade = (HorarioDisponibilidadeVO) o;
                    horarioDisponibilidade.setHora1300(true);

                    deveSetarVespertino(horarioDisponibilidade);
                }
            }
            if (!obj.getHora1300()) {
                for (Object o : horarioVO.getHorarioDisponibilidadeVOs()) {
                    HorarioDisponibilidadeVO horarioDisponibilidade = (HorarioDisponibilidadeVO) o;
                    horarioDisponibilidade.setHora1300(false);
                    horarioDisponibilidade.setVespertino(false);
                }
            }
        } else {
            // se campo foi marcado, validar se toda a coluna ou toda a linha está marcada
            if(obj.getHora1300()) {
                ((HorarioDisponibilidadeVO) getHorarioVO().getHorarioDisponibilidadeVOs().get(0)).setHora1300(true);
                for(HorarioDisponibilidadeVO horarioDisponibilidade : (ArrayList<HorarioDisponibilidadeVO>) horarioVO.getHorarioDisponibilidadeVOs()) {
                    if(!horarioDisponibilidade.getHora1300()) {
                        ((HorarioDisponibilidadeVO) getHorarioVO().getHorarioDisponibilidadeVOs().get(0)).setHora1300(false);
                    }
                }
                deveSetarVespertino(obj);
            } else {
                obj.setVespertino(false);
                ((HorarioDisponibilidadeVO) getHorarioVO().getHorarioDisponibilidadeVOs().get(0)).setHora1300(false);
            }
        }
    }

    public void MarcaTodaColuna1330() {
        HorarioDisponibilidadeVO obj = (HorarioDisponibilidadeVO) context().getExternalContext().getRequestMap().get("horarioDisponibilidadeVO");        
        if (obj.getIdentificador().equals("")) {
            if (obj.getHora1330()) {
                for (Object o : horarioVO.getHorarioDisponibilidadeVOs()) {
                    HorarioDisponibilidadeVO horarioDisponibilidade = (HorarioDisponibilidadeVO) o;
                    horarioDisponibilidade.setHora1330(true);

                    deveSetarVespertino(horarioDisponibilidade);
                }
            }
            if (!obj.getHora1330()) {
                for (Object o : horarioVO.getHorarioDisponibilidadeVOs()) {
                    HorarioDisponibilidadeVO horarioDisponibilidade = (HorarioDisponibilidadeVO) o;
                    horarioDisponibilidade.setHora1330(false);
                    horarioDisponibilidade.setVespertino(false);
                }
            }
        } else {
            // se campo foi marcado, validar se toda a coluna ou toda a linha está marcada
            if(obj.getHora1330()) {
                ((HorarioDisponibilidadeVO) getHorarioVO().getHorarioDisponibilidadeVOs().get(0)).setHora1330(true);
                for(HorarioDisponibilidadeVO horarioDisponibilidade : (ArrayList<HorarioDisponibilidadeVO>) horarioVO.getHorarioDisponibilidadeVOs()) {
                    if(!horarioDisponibilidade.getHora1330()) {
                        ((HorarioDisponibilidadeVO) getHorarioVO().getHorarioDisponibilidadeVOs().get(0)).setHora1330(false);
                    }
                }
                deveSetarVespertino(obj);
            } else {
                obj.setVespertino(false);
                ((HorarioDisponibilidadeVO) getHorarioVO().getHorarioDisponibilidadeVOs().get(0)).setHora1330(false);
            }
        }
    }

    public void MarcaTodaColuna1400() {
        HorarioDisponibilidadeVO obj = (HorarioDisponibilidadeVO) context().getExternalContext().getRequestMap().get("horarioDisponibilidadeVO");        
        if (obj.getIdentificador().equals("")) {
            if (obj.getHora1400()) {
                for (Object o : horarioVO.getHorarioDisponibilidadeVOs()) {
                    HorarioDisponibilidadeVO horarioDisponibilidade = (HorarioDisponibilidadeVO) o;
                    horarioDisponibilidade.setHora1400(true);

                    deveSetarVespertino(horarioDisponibilidade);
                }
            }
            if (!obj.getHora1400()) {
                for (Object o : horarioVO.getHorarioDisponibilidadeVOs()) {
                    HorarioDisponibilidadeVO horarioDisponibilidade = (HorarioDisponibilidadeVO) o;
                    horarioDisponibilidade.setHora1400(false);
                    horarioDisponibilidade.setVespertino(false);
                }
            }
        } else {
            // se campo foi marcado, validar se toda a coluna ou toda a linha está marcada
            if(obj.getHora1400()) {
                ((HorarioDisponibilidadeVO) getHorarioVO().getHorarioDisponibilidadeVOs().get(0)).setHora1400(true);
                for(HorarioDisponibilidadeVO horarioDisponibilidade : (ArrayList<HorarioDisponibilidadeVO>) horarioVO.getHorarioDisponibilidadeVOs()) {
                    if(!horarioDisponibilidade.getHora1400()) {
                        ((HorarioDisponibilidadeVO) getHorarioVO().getHorarioDisponibilidadeVOs().get(0)).setHora1400(false);
                    }
                }
                deveSetarVespertino(obj);
            } else {
                obj.setVespertino(false);
                ((HorarioDisponibilidadeVO) getHorarioVO().getHorarioDisponibilidadeVOs().get(0)).setHora1400(false);
            }
        }
    }

    public void MarcaTodaColuna1430() {
        HorarioDisponibilidadeVO obj = (HorarioDisponibilidadeVO) context().getExternalContext().getRequestMap().get("horarioDisponibilidadeVO");        
        if (obj.getIdentificador().equals("")) {
            if (obj.getHora1430()) {
                for (Object o : horarioVO.getHorarioDisponibilidadeVOs()) {
                    HorarioDisponibilidadeVO horarioDisponibilidade = (HorarioDisponibilidadeVO) o;
                    horarioDisponibilidade.setHora1430(true);

                    deveSetarVespertino(horarioDisponibilidade);
                }
            }
            if (!obj.getHora1430()) {
                for (Object o : horarioVO.getHorarioDisponibilidadeVOs()) {
                    HorarioDisponibilidadeVO horarioDisponibilidade = (HorarioDisponibilidadeVO) o;
                    horarioDisponibilidade.setHora1430(false);
                    horarioDisponibilidade.setVespertino(false);
                }
            }
        } else {
            // se campo foi marcado, validar se toda a coluna ou toda a linha está marcada
            if(obj.getHora1430()) {
                ((HorarioDisponibilidadeVO) getHorarioVO().getHorarioDisponibilidadeVOs().get(0)).setHora1430(true);
                for(HorarioDisponibilidadeVO horarioDisponibilidade : (ArrayList<HorarioDisponibilidadeVO>) horarioVO.getHorarioDisponibilidadeVOs()) {
                    if(!horarioDisponibilidade.getHora1430()) {
                        ((HorarioDisponibilidadeVO) getHorarioVO().getHorarioDisponibilidadeVOs().get(0)).setHora1430(false);
                    }
                }
                deveSetarVespertino(obj);
            } else {
                obj.setVespertino(false);
                ((HorarioDisponibilidadeVO) getHorarioVO().getHorarioDisponibilidadeVOs().get(0)).setHora1430(false);
            }
        }
    }

    public void MarcaTodaColuna1500() {
        HorarioDisponibilidadeVO obj = (HorarioDisponibilidadeVO) context().getExternalContext().getRequestMap().get("horarioDisponibilidadeVO");        
        if (obj.getIdentificador().equals("")) {
            if (obj.getHora1500()) {
                for (Object o : horarioVO.getHorarioDisponibilidadeVOs()) {
                    HorarioDisponibilidadeVO horarioDisponibilidade = (HorarioDisponibilidadeVO) o;
                    horarioDisponibilidade.setHora1500(true);

                    deveSetarVespertino(horarioDisponibilidade);
                }
            }
            if (!obj.getHora1500()) {
                for (Object o : horarioVO.getHorarioDisponibilidadeVOs()) {
                    HorarioDisponibilidadeVO horarioDisponibilidade = (HorarioDisponibilidadeVO) o;
                    horarioDisponibilidade.setHora1500(false);
                    horarioDisponibilidade.setVespertino(false);
                }
            }
        } else {
            // se campo foi marcado, validar se toda a coluna ou toda a linha está marcada
            if(obj.getHora1500()) {
                ((HorarioDisponibilidadeVO) getHorarioVO().getHorarioDisponibilidadeVOs().get(0)).setHora1500(true);
                for(HorarioDisponibilidadeVO horarioDisponibilidade : (ArrayList<HorarioDisponibilidadeVO>) horarioVO.getHorarioDisponibilidadeVOs()) {
                    if(!horarioDisponibilidade.getHora1500()) {
                        ((HorarioDisponibilidadeVO) getHorarioVO().getHorarioDisponibilidadeVOs().get(0)).setHora1500(false);
                    }
                }
                deveSetarVespertino(obj);
            } else {
                obj.setVespertino(false);
                ((HorarioDisponibilidadeVO) getHorarioVO().getHorarioDisponibilidadeVOs().get(0)).setHora1500(false);
            }
        }
    }

    public void MarcaTodaColuna1530() {
        HorarioDisponibilidadeVO obj = (HorarioDisponibilidadeVO) context().getExternalContext().getRequestMap().get("horarioDisponibilidadeVO");        
        if (obj.getIdentificador().equals("")) {
            if (obj.getHora1530()) {
                for (Object o : horarioVO.getHorarioDisponibilidadeVOs()) {
                    HorarioDisponibilidadeVO horarioDisponibilidade = (HorarioDisponibilidadeVO) o;
                    horarioDisponibilidade.setHora1530(true);

                    deveSetarVespertino(horarioDisponibilidade);
                }
            }
            if (!obj.getHora1530()) {
                for (Object o : horarioVO.getHorarioDisponibilidadeVOs()) {
                    HorarioDisponibilidadeVO horarioDisponibilidade = (HorarioDisponibilidadeVO) o;
                    horarioDisponibilidade.setHora1530(false);
                    horarioDisponibilidade.setVespertino(false);
                }
            }
        } else {
            // se campo foi marcado, validar se toda a coluna ou toda a linha está marcada
            if(obj.getHora1530()) {
                ((HorarioDisponibilidadeVO) getHorarioVO().getHorarioDisponibilidadeVOs().get(0)).setHora1530(true);
                for(HorarioDisponibilidadeVO horarioDisponibilidade : (ArrayList<HorarioDisponibilidadeVO>) horarioVO.getHorarioDisponibilidadeVOs()) {
                    if(!horarioDisponibilidade.getHora1530()) {
                        ((HorarioDisponibilidadeVO) getHorarioVO().getHorarioDisponibilidadeVOs().get(0)).setHora1530(false);
                    }
                }
                deveSetarVespertino(obj);
            } else {
                obj.setVespertino(false);
                ((HorarioDisponibilidadeVO) getHorarioVO().getHorarioDisponibilidadeVOs().get(0)).setHora1530(false);
            }
        }
    }

    public void MarcaTodaColuna1600() {
        HorarioDisponibilidadeVO obj = (HorarioDisponibilidadeVO) context().getExternalContext().getRequestMap().get("horarioDisponibilidadeVO");        
        if (obj.getIdentificador().equals("")) {
            if (obj.getHora1600()) {
                for (Object o : horarioVO.getHorarioDisponibilidadeVOs()) {
                    HorarioDisponibilidadeVO horarioDisponibilidade = (HorarioDisponibilidadeVO) o;
                    horarioDisponibilidade.setHora1600(true);

                    deveSetarVespertino(horarioDisponibilidade);
                }
            }
            if (!obj.getHora1600()) {
                for (Object o : horarioVO.getHorarioDisponibilidadeVOs()) {
                    HorarioDisponibilidadeVO horarioDisponibilidade = (HorarioDisponibilidadeVO) o;
                    horarioDisponibilidade.setHora1600(false);
                    horarioDisponibilidade.setVespertino(false);
                }
            }
        } else {
            // se campo foi marcado, validar se toda a coluna ou toda a linha está marcada
            if(obj.getHora1600()) {
                ((HorarioDisponibilidadeVO) getHorarioVO().getHorarioDisponibilidadeVOs().get(0)).setHora1600(true);
                for(HorarioDisponibilidadeVO horarioDisponibilidade : (ArrayList<HorarioDisponibilidadeVO>) horarioVO.getHorarioDisponibilidadeVOs()) {
                    if(!horarioDisponibilidade.getHora1600()) {
                        ((HorarioDisponibilidadeVO) getHorarioVO().getHorarioDisponibilidadeVOs().get(0)).setHora1600(false);
                    }
                }
                deveSetarVespertino(obj);
            } else {
                obj.setVespertino(false);
                ((HorarioDisponibilidadeVO) getHorarioVO().getHorarioDisponibilidadeVOs().get(0)).setHora1600(false);
            }
        }
    }

    public void MarcaTodaColuna1630() {
        HorarioDisponibilidadeVO obj = (HorarioDisponibilidadeVO) context().getExternalContext().getRequestMap().get("horarioDisponibilidadeVO");        
        if (obj.getIdentificador().equals("")) {
            if (obj.getHora1630()) {
                for (Object o : horarioVO.getHorarioDisponibilidadeVOs()) {
                    HorarioDisponibilidadeVO horarioDisponibilidade = (HorarioDisponibilidadeVO) o;
                    horarioDisponibilidade.setHora1630(true);

                    deveSetarVespertino(horarioDisponibilidade);
                }
            }
            if (!obj.getHora1630()) {
                for (Object o : horarioVO.getHorarioDisponibilidadeVOs()) {
                    HorarioDisponibilidadeVO horarioDisponibilidade = (HorarioDisponibilidadeVO) o;
                    horarioDisponibilidade.setHora1630(false);
                    horarioDisponibilidade.setVespertino(false);
                }
            }
        } else {
            // se campo foi marcado, validar se toda a coluna ou toda a linha está marcada
            if(obj.getHora1630()) {
                ((HorarioDisponibilidadeVO) getHorarioVO().getHorarioDisponibilidadeVOs().get(0)).setHora1630(true);
                for(HorarioDisponibilidadeVO horarioDisponibilidade : (ArrayList<HorarioDisponibilidadeVO>) horarioVO.getHorarioDisponibilidadeVOs()) {
                    if(!horarioDisponibilidade.getHora1630()) {
                        ((HorarioDisponibilidadeVO) getHorarioVO().getHorarioDisponibilidadeVOs().get(0)).setHora1630(false);
                    }
                }
                deveSetarVespertino(obj);
            } else {
                obj.setVespertino(false);
                ((HorarioDisponibilidadeVO) getHorarioVO().getHorarioDisponibilidadeVOs().get(0)).setHora1630(false);
            }
        }
    }

    public void MarcaTodaColuna1700() {
        HorarioDisponibilidadeVO obj = (HorarioDisponibilidadeVO) context().getExternalContext().getRequestMap().get("horarioDisponibilidadeVO");        
        if (obj.getIdentificador().equals("")) {
            if (obj.getHora1700()) {
                for (Object o : horarioVO.getHorarioDisponibilidadeVOs()) {
                    HorarioDisponibilidadeVO horarioDisponibilidade = (HorarioDisponibilidadeVO) o;
                    horarioDisponibilidade.setHora1700(true);

                    deveSetarVespertino(horarioDisponibilidade);
                }
            }
            if (!obj.getHora1700()) {
                for (Object o : horarioVO.getHorarioDisponibilidadeVOs()) {
                    HorarioDisponibilidadeVO horarioDisponibilidade = (HorarioDisponibilidadeVO) o;
                    horarioDisponibilidade.setHora1700(false);
                    horarioDisponibilidade.setVespertino(false);
                }
            }
        } else {
            // se campo foi marcado, validar se toda a coluna ou toda a linha está marcada
            if(obj.getHora1700()) {
                ((HorarioDisponibilidadeVO) getHorarioVO().getHorarioDisponibilidadeVOs().get(0)).setHora1700(true);
                for(HorarioDisponibilidadeVO horarioDisponibilidade : (ArrayList<HorarioDisponibilidadeVO>) horarioVO.getHorarioDisponibilidadeVOs()) {
                    if(!horarioDisponibilidade.getHora1700()) {
                        ((HorarioDisponibilidadeVO) getHorarioVO().getHorarioDisponibilidadeVOs().get(0)).setHora1700(false);
                    }
                }
                deveSetarVespertino(obj);
            } else {
                obj.setVespertino(false);
                ((HorarioDisponibilidadeVO) getHorarioVO().getHorarioDisponibilidadeVOs().get(0)).setHora1700(false);
            }
        }
    }

    public void MarcaTodaColuna1730() {
        HorarioDisponibilidadeVO obj = (HorarioDisponibilidadeVO) context().getExternalContext().getRequestMap().get("horarioDisponibilidadeVO");        
        if (obj.getIdentificador().equals("")) {
            if (obj.getHora1730()) {
                for (Object o : horarioVO.getHorarioDisponibilidadeVOs()) {
                    HorarioDisponibilidadeVO horarioDisponibilidade = (HorarioDisponibilidadeVO) o;
                    horarioDisponibilidade.setHora1730(true);
                    deveSetarVespertino(horarioDisponibilidade);
                }
            }
            if (!obj.getHora1730()) {
                for (Object o : horarioVO.getHorarioDisponibilidadeVOs()) {
                    HorarioDisponibilidadeVO horarioDisponibilidade = (HorarioDisponibilidadeVO) o;
                    horarioDisponibilidade.setHora1730(false);
                    horarioDisponibilidade.setVespertino(false);
                }
            }
        } else {
            // se campo foi marcado, validar se toda a coluna ou toda a linha está marcada
            if(obj.getHora1730()) {
                ((HorarioDisponibilidadeVO) getHorarioVO().getHorarioDisponibilidadeVOs().get(0)).setHora1730(true);
                for(HorarioDisponibilidadeVO horarioDisponibilidade : (ArrayList<HorarioDisponibilidadeVO>) horarioVO.getHorarioDisponibilidadeVOs()) {
                    if(!horarioDisponibilidade.getHora1730()) {
                        ((HorarioDisponibilidadeVO) getHorarioVO().getHorarioDisponibilidadeVOs().get(0)).setHora1730(false);
                    }
                }
                deveSetarVespertino(obj);
            } else {
                obj.setVespertino(false);
                ((HorarioDisponibilidadeVO) getHorarioVO().getHorarioDisponibilidadeVOs().get(0)).setHora1730(false);
            }
        }
    }

    public void marcarTodaColuna0000(){
        if(getHorarioDisponibilidadeVO().getIdentificador().equals("")){
            for (HorarioDisponibilidadeVO horDispVO : horarioVO.getHorarioDisponibilidadeVOs()) {
                horDispVO.setHora0000(getHorarioDisponibilidadeVO().getHora0000());
                deveSetarMadrugada(horDispVO);
            }
        }
    }

    public void marcarTodaColuna0030(){
        if(getHorarioDisponibilidadeVO().getIdentificador().equals("")){
            for (HorarioDisponibilidadeVO horDispVO : horarioVO.getHorarioDisponibilidadeVOs()) {
                horDispVO.setHora0030(getHorarioDisponibilidadeVO().getHora0030());
                deveSetarMadrugada(horDispVO);
            }
        }
    }

    public void marcarTodaColuna0100(){
        if(getHorarioDisponibilidadeVO().getIdentificador().equals("")){
            for (HorarioDisponibilidadeVO horDispVO : horarioVO.getHorarioDisponibilidadeVOs()) {
                horDispVO.setHora0100(getHorarioDisponibilidadeVO().getHora0100());
                deveSetarMadrugada(horDispVO);
            }
        }
    }

    public void marcarTodaColuna0130(){
        if(getHorarioDisponibilidadeVO().getIdentificador().equals("")){
            for (HorarioDisponibilidadeVO horDispVO : horarioVO.getHorarioDisponibilidadeVOs()) {
                horDispVO.setHora0130(getHorarioDisponibilidadeVO().getHora0130());
                deveSetarMadrugada(horDispVO);
            }
        }
    }

    public void marcarTodaColuna0200(){
        if(getHorarioDisponibilidadeVO().getIdentificador().equals("")){
            for (HorarioDisponibilidadeVO horDispVO : horarioVO.getHorarioDisponibilidadeVOs()) {
                horDispVO.setHora0200(getHorarioDisponibilidadeVO().getHora0200());
                deveSetarMadrugada(horDispVO);
            }
        }
    }

    public void marcarTodaColuna0230(){
        if(getHorarioDisponibilidadeVO().getIdentificador().equals("")) {
            for (HorarioDisponibilidadeVO horDispVO : horarioVO.getHorarioDisponibilidadeVOs()) {
                horDispVO.setHora0230(getHorarioDisponibilidadeVO().getHora0230());
                deveSetarMadrugada(horDispVO);
            }
        }
    }

    public void marcarTodaColuna0300(){
        if(getHorarioDisponibilidadeVO().getIdentificador().equals("")) {
            for (HorarioDisponibilidadeVO horDispVO : horarioVO.getHorarioDisponibilidadeVOs()) {
                horDispVO.setHora0300(getHorarioDisponibilidadeVO().getHora0300());
                deveSetarMadrugada(horDispVO);
            }
        }
    }

    public void marcarTodaColuna0330(){
        if(getHorarioDisponibilidadeVO().getIdentificador().equals("")) {
            for (HorarioDisponibilidadeVO horDispVO : horarioVO.getHorarioDisponibilidadeVOs()) {
                horDispVO.setHora0330(getHorarioDisponibilidadeVO().getHora0330());
                deveSetarMadrugada(horDispVO);
            }
        }
    }

    public void marcarTodaColuna0400(){
        if(getHorarioDisponibilidadeVO().getIdentificador().equals("")) {
            for (HorarioDisponibilidadeVO horDispVO : horarioVO.getHorarioDisponibilidadeVOs()) {
                horDispVO.setHora0400(getHorarioDisponibilidadeVO().getHora0400());
                deveSetarMadrugada(horDispVO);
            }
        }
    }

    public void marcarTodaColuna0430(){
        if(getHorarioDisponibilidadeVO().getIdentificador().equals("")) {
            for (HorarioDisponibilidadeVO horDispVO : horarioVO.getHorarioDisponibilidadeVOs()) {
                horDispVO.setHora0430(getHorarioDisponibilidadeVO().getHora0430());
                deveSetarMadrugada(horDispVO);
            }
        }
    }

    public void MarcaTodaColuna1800() {
        HorarioDisponibilidadeVO obj = (HorarioDisponibilidadeVO) context().getExternalContext().getRequestMap().get("horarioDisponibilidadeVO");
        if (obj.getIdentificador().equals("")) {
            if (obj.getHora1800()) {
                for (Object o : horarioVO.getHorarioDisponibilidadeVOs()) {
                    HorarioDisponibilidadeVO horarioDisponibilidade = (HorarioDisponibilidadeVO) o;
                    horarioDisponibilidade.setHora1800(true);

                    deveSetarNoturno(horarioDisponibilidade);
                }
            }
            if (!obj.getHora1800()) {
                for (Object o : horarioVO.getHorarioDisponibilidadeVOs()) {
                    HorarioDisponibilidadeVO horarioDisponibilidade = (HorarioDisponibilidadeVO) o;
                    horarioDisponibilidade.setHora1800(false);
                    horarioDisponibilidade.setNoturno(false);
                }
            }
        } else {
            // se campo foi marcado, validar se toda a coluna ou toda a linha está marcada
            if(obj.getHora1800()) {
                ((HorarioDisponibilidadeVO) getHorarioVO().getHorarioDisponibilidadeVOs().get(0)).setHora1800(true);
                for(HorarioDisponibilidadeVO horarioDisponibilidade : (ArrayList<HorarioDisponibilidadeVO>) horarioVO.getHorarioDisponibilidadeVOs()) {
                    if(!horarioDisponibilidade.getHora1800()) {
                        ((HorarioDisponibilidadeVO) getHorarioVO().getHorarioDisponibilidadeVOs().get(0)).setHora1800(false);
                    }
                }
                deveSetarNoturno(obj);
            } else {
                obj.setNoturno(false);
                ((HorarioDisponibilidadeVO) getHorarioVO().getHorarioDisponibilidadeVOs().get(0)).setHora1800(false);
            }
        }
    }

    public void MarcaTodaColuna1830() {
        HorarioDisponibilidadeVO obj = (HorarioDisponibilidadeVO) context().getExternalContext().getRequestMap().get("horarioDisponibilidadeVO");        
        if (obj.getIdentificador().equals("")) {
            if (obj.getHora1830()) {
                for (Object o : horarioVO.getHorarioDisponibilidadeVOs()) {
                    HorarioDisponibilidadeVO horarioDisponibilidade = (HorarioDisponibilidadeVO) o;
                    horarioDisponibilidade.setHora1830(true);

                    deveSetarNoturno(horarioDisponibilidade);
                }
            }
            if (!obj.getHora1830()) {
                for (Object o : horarioVO.getHorarioDisponibilidadeVOs()) {
                    HorarioDisponibilidadeVO horarioDisponibilidade = (HorarioDisponibilidadeVO) o;
                    horarioDisponibilidade.setHora1830(false);
                    horarioDisponibilidade.setNoturno(false);
                }
            }
        } else {
            // se campo foi marcado, validar se toda a coluna ou toda a linha está marcada
            if(obj.getHora1830()) {
                ((HorarioDisponibilidadeVO) getHorarioVO().getHorarioDisponibilidadeVOs().get(0)).setHora1830(true);
                for(HorarioDisponibilidadeVO horarioDisponibilidade : (ArrayList<HorarioDisponibilidadeVO>) horarioVO.getHorarioDisponibilidadeVOs()) {
                    if(!horarioDisponibilidade.getHora1830()) {
                        ((HorarioDisponibilidadeVO) getHorarioVO().getHorarioDisponibilidadeVOs().get(0)).setHora1830(false);
                    }
                }
                deveSetarNoturno(obj);
            } else {
                obj.setNoturno(false);
                ((HorarioDisponibilidadeVO) getHorarioVO().getHorarioDisponibilidadeVOs().get(0)).setHora1830(false);
            }
        }
    }

    public void MarcaTodaColuna1900() {
        HorarioDisponibilidadeVO obj = (HorarioDisponibilidadeVO) context().getExternalContext().getRequestMap().get("horarioDisponibilidadeVO");        
        if (obj.getIdentificador().equals("")) {
            if (obj.getHora1900()) {
                for (Object o : horarioVO.getHorarioDisponibilidadeVOs()) {
                    HorarioDisponibilidadeVO horarioDisponibilidade = (HorarioDisponibilidadeVO) o;
                    horarioDisponibilidade.setHora1900(true);

                    deveSetarNoturno(horarioDisponibilidade);
                }
            }
            if (!obj.getHora1900()) {
                for (Object o : horarioVO.getHorarioDisponibilidadeVOs()) {
                    HorarioDisponibilidadeVO horarioDisponibilidade = (HorarioDisponibilidadeVO) o;
                    horarioDisponibilidade.setHora1900(false);
                    horarioDisponibilidade.setNoturno(false);
                }
            }
        } else {
            // se campo foi marcado, validar se toda a coluna ou toda a linha está marcada
            if(obj.getHora1900()) {
                ((HorarioDisponibilidadeVO) getHorarioVO().getHorarioDisponibilidadeVOs().get(0)).setHora1900(true);
                for(HorarioDisponibilidadeVO horarioDisponibilidade : (ArrayList<HorarioDisponibilidadeVO>) horarioVO.getHorarioDisponibilidadeVOs()) {
                    if(!horarioDisponibilidade.getHora1900()) {
                        ((HorarioDisponibilidadeVO) getHorarioVO().getHorarioDisponibilidadeVOs().get(0)).setHora1900(false);
                    }
                }
                deveSetarNoturno(obj);
            } else {
                obj.setNoturno(false);
                ((HorarioDisponibilidadeVO) getHorarioVO().getHorarioDisponibilidadeVOs().get(0)).setHora1900(false);
            }
        }
    }

    public void MarcaTodaColuna1930() {
        HorarioDisponibilidadeVO obj = (HorarioDisponibilidadeVO) context().getExternalContext().getRequestMap().get("horarioDisponibilidadeVO");        
        if (obj.getIdentificador().equals("")) {
            if (obj.getHora1930()) {
                for (Object o : horarioVO.getHorarioDisponibilidadeVOs()) {
                    HorarioDisponibilidadeVO horarioDisponibilidade = (HorarioDisponibilidadeVO) o;
                    horarioDisponibilidade.setHora1930(true);

                    deveSetarNoturno(horarioDisponibilidade);
                }
            }
            if (!obj.getHora1930()) {
                for (Object o : horarioVO.getHorarioDisponibilidadeVOs()) {
                    HorarioDisponibilidadeVO horarioDisponibilidade = (HorarioDisponibilidadeVO) o;
                    horarioDisponibilidade.setHora1930(false);
                    horarioDisponibilidade.setNoturno(false);
                }
            }
        } else {
            // se campo foi marcado, validar se toda a coluna ou toda a linha está marcada
            if(obj.getHora1930()) {
                ((HorarioDisponibilidadeVO) getHorarioVO().getHorarioDisponibilidadeVOs().get(0)).setHora1930(true);
                for(HorarioDisponibilidadeVO horarioDisponibilidade : (ArrayList<HorarioDisponibilidadeVO>) horarioVO.getHorarioDisponibilidadeVOs()) {
                    if(!horarioDisponibilidade.getHora1930()) {
                        ((HorarioDisponibilidadeVO) getHorarioVO().getHorarioDisponibilidadeVOs().get(0)).setHora1930(false);
                    }
                }
                deveSetarNoturno(obj);
            } else {
                obj.setNoturno(false);
                ((HorarioDisponibilidadeVO) getHorarioVO().getHorarioDisponibilidadeVOs().get(0)).setHora1930(false);
            }
        }
    }

    public void MarcaTodaColuna2000() {
        HorarioDisponibilidadeVO obj = (HorarioDisponibilidadeVO) context().getExternalContext().getRequestMap().get("horarioDisponibilidadeVO");        
        if (obj.getIdentificador().equals("")) {
            if (obj.getHora2000()) {
                for (Object o : horarioVO.getHorarioDisponibilidadeVOs()) {
                    HorarioDisponibilidadeVO horarioDisponibilidade = (HorarioDisponibilidadeVO) o;
                    horarioDisponibilidade.setHora2000(true);

                    deveSetarNoturno(horarioDisponibilidade);
                }
            }
            if (!obj.getHora2000()) {
                for (Object o : horarioVO.getHorarioDisponibilidadeVOs()) {
                    HorarioDisponibilidadeVO horarioDisponibilidade = (HorarioDisponibilidadeVO) o;
                    horarioDisponibilidade.setHora2000(false);
                    horarioDisponibilidade.setNoturno(false);
                }
            }
        } else {
            // se campo foi marcado, validar se toda a coluna ou toda a linha está marcada
            if(obj.getHora2000()) {
                ((HorarioDisponibilidadeVO) getHorarioVO().getHorarioDisponibilidadeVOs().get(0)).setHora2000(true);
                for(HorarioDisponibilidadeVO horarioDisponibilidade : (ArrayList<HorarioDisponibilidadeVO>) horarioVO.getHorarioDisponibilidadeVOs()) {
                    if(!horarioDisponibilidade.getHora2000()) {
                        ((HorarioDisponibilidadeVO) getHorarioVO().getHorarioDisponibilidadeVOs().get(0)).setHora2000(false);
                    }
                }
                deveSetarNoturno(obj);
            } else {
                obj.setNoturno(false);
                ((HorarioDisponibilidadeVO) getHorarioVO().getHorarioDisponibilidadeVOs().get(0)).setHora2000(false);
            }
        }
    }

    public void MarcaTodaColuna2030() {
        HorarioDisponibilidadeVO obj = (HorarioDisponibilidadeVO) context().getExternalContext().getRequestMap().get("horarioDisponibilidadeVO");        
        if (obj.getIdentificador().equals("")) {
            if (obj.getHora2030()) {
                for (Object o : horarioVO.getHorarioDisponibilidadeVOs()) {
                    HorarioDisponibilidadeVO horarioDisponibilidade = (HorarioDisponibilidadeVO) o;
                    horarioDisponibilidade.setHora2030(true);

                    deveSetarNoturno(horarioDisponibilidade);
                }
            }
            if (!obj.getHora2030()) {
                for (Object o : horarioVO.getHorarioDisponibilidadeVOs()) {
                    HorarioDisponibilidadeVO horarioDisponibilidade = (HorarioDisponibilidadeVO) o;
                    horarioDisponibilidade.setHora2030(false);
                    horarioDisponibilidade.setNoturno(false);
                }
            }
        } else {
            // se campo foi marcado, validar se toda a coluna ou toda a linha está marcada
            if(obj.getHora2030()) {
                ((HorarioDisponibilidadeVO) getHorarioVO().getHorarioDisponibilidadeVOs().get(0)).setHora2030(true);
                for(HorarioDisponibilidadeVO horarioDisponibilidade : (ArrayList<HorarioDisponibilidadeVO>) horarioVO.getHorarioDisponibilidadeVOs()) {
                    if(!horarioDisponibilidade.getHora2030()) {
                        ((HorarioDisponibilidadeVO) getHorarioVO().getHorarioDisponibilidadeVOs().get(0)).setHora2030(false);
                    }
                }
                deveSetarNoturno(obj);
            } else {
                obj.setNoturno(false);
                ((HorarioDisponibilidadeVO) getHorarioVO().getHorarioDisponibilidadeVOs().get(0)).setHora2030(false);
            }
        }
    }

    public void MarcaTodaColuna2100() {
        HorarioDisponibilidadeVO obj = (HorarioDisponibilidadeVO) context().getExternalContext().getRequestMap().get("horarioDisponibilidadeVO");        
        if (obj.getIdentificador().equals("")) {
            if (obj.getHora2100()) {
                for (Object o : horarioVO.getHorarioDisponibilidadeVOs()) {
                    HorarioDisponibilidadeVO horarioDisponibilidade = (HorarioDisponibilidadeVO) o;
                    horarioDisponibilidade.setHora2100(true);

                    deveSetarNoturno(horarioDisponibilidade);
                }
            }
            if (!obj.getHora2100()) {
                for (Object o : horarioVO.getHorarioDisponibilidadeVOs()) {
                    HorarioDisponibilidadeVO horarioDisponibilidade = (HorarioDisponibilidadeVO) o;
                    horarioDisponibilidade.setHora2100(false);
                    horarioDisponibilidade.setNoturno(false);
                }
            }
        } else {
            // se campo foi marcado, validar se toda a coluna ou toda a linha está marcada
            if(obj.getHora2100()) {
                ((HorarioDisponibilidadeVO) getHorarioVO().getHorarioDisponibilidadeVOs().get(0)).setHora2100(true);
                for(HorarioDisponibilidadeVO horarioDisponibilidade : (ArrayList<HorarioDisponibilidadeVO>) horarioVO.getHorarioDisponibilidadeVOs()) {
                    if(!horarioDisponibilidade.getHora2100()) {
                        ((HorarioDisponibilidadeVO) getHorarioVO().getHorarioDisponibilidadeVOs().get(0)).setHora2100(false);
                    }
                }
                deveSetarNoturno(obj);
            } else {
                obj.setNoturno(false);
                ((HorarioDisponibilidadeVO) getHorarioVO().getHorarioDisponibilidadeVOs().get(0)).setHora2100(false);
            }
        }
    }

    public void MarcaTodaColuna2130() {
        HorarioDisponibilidadeVO obj = (HorarioDisponibilidadeVO) context().getExternalContext().getRequestMap().get("horarioDisponibilidadeVO");        
        if (obj.getIdentificador().equals("")) {
            if (obj.getHora2130()) {
                for (Object o : horarioVO.getHorarioDisponibilidadeVOs()) {
                    HorarioDisponibilidadeVO horarioDisponibilidade = (HorarioDisponibilidadeVO) o;
                    horarioDisponibilidade.setHora2130(true);

                    deveSetarNoturno(horarioDisponibilidade);
                }
            }
            if (!obj.getHora2130()) {
                for (Object o : horarioVO.getHorarioDisponibilidadeVOs()) {
                    HorarioDisponibilidadeVO horarioDisponibilidade = (HorarioDisponibilidadeVO) o;
                    horarioDisponibilidade.setHora2130(false);
                    horarioDisponibilidade.setNoturno(false);
                }
            }
        } else {
            // se campo foi marcado, validar se toda a coluna ou toda a linha está marcada
            if(obj.getHora2130()) {
                ((HorarioDisponibilidadeVO) getHorarioVO().getHorarioDisponibilidadeVOs().get(0)).setHora2130(true);
                for(HorarioDisponibilidadeVO horarioDisponibilidade : (ArrayList<HorarioDisponibilidadeVO>) horarioVO.getHorarioDisponibilidadeVOs()) {
                    if(!horarioDisponibilidade.getHora2130()) {
                        ((HorarioDisponibilidadeVO) getHorarioVO().getHorarioDisponibilidadeVOs().get(0)).setHora2130(false);
                    }
                }
                deveSetarNoturno(obj);
            } else {
                obj.setNoturno(false);
                ((HorarioDisponibilidadeVO) getHorarioVO().getHorarioDisponibilidadeVOs().get(0)).setHora2130(false);
            }
        }
    }

    public void MarcaTodaColuna2200() {
        HorarioDisponibilidadeVO obj = (HorarioDisponibilidadeVO) context().getExternalContext().getRequestMap().get("horarioDisponibilidadeVO");        
        if (obj.getIdentificador().equals("")) {
            if (obj.getHora2200()) {
                for (Object o : horarioVO.getHorarioDisponibilidadeVOs()) {
                    HorarioDisponibilidadeVO horarioDisponibilidade = (HorarioDisponibilidadeVO) o;
                    horarioDisponibilidade.setHora2200(true);

                    deveSetarNoturno(horarioDisponibilidade);
                }
            }
            if (!obj.getHora2200()) {
                for (Object o : horarioVO.getHorarioDisponibilidadeVOs()) {
                    HorarioDisponibilidadeVO horarioDisponibilidade = (HorarioDisponibilidadeVO) o;
                    horarioDisponibilidade.setHora2200(false);
                    horarioDisponibilidade.setNoturno(false);
                }
            }
        } else {
            // se campo foi marcado, validar se toda a coluna ou toda a linha está marcada
            if(obj.getHora2200()) {
                ((HorarioDisponibilidadeVO) getHorarioVO().getHorarioDisponibilidadeVOs().get(0)).setHora2200(true);
                for(HorarioDisponibilidadeVO horarioDisponibilidade : (ArrayList<HorarioDisponibilidadeVO>) horarioVO.getHorarioDisponibilidadeVOs()) {
                    if(!horarioDisponibilidade.getHora2200()) {
                        ((HorarioDisponibilidadeVO) getHorarioVO().getHorarioDisponibilidadeVOs().get(0)).setHora2200(false);
                    }
                }
                deveSetarNoturno(obj);
            } else {
                obj.setNoturno(false);
                ((HorarioDisponibilidadeVO) getHorarioVO().getHorarioDisponibilidadeVOs().get(0)).setHora2200(false);
            }
        }
    }

    public void MarcaTodaColuna2230() {
        HorarioDisponibilidadeVO obj = (HorarioDisponibilidadeVO) context().getExternalContext().getRequestMap().get("horarioDisponibilidadeVO");        
        if (obj.getIdentificador().equals("")) {
            if (obj.getHora2230()) {
                for (Object o : horarioVO.getHorarioDisponibilidadeVOs()) {
                    HorarioDisponibilidadeVO horarioDisponibilidade = (HorarioDisponibilidadeVO) o;
                    horarioDisponibilidade.setHora2230(true);

                    deveSetarNoturno(horarioDisponibilidade);
                }
            }
            if (!obj.getHora2230()) {
                for (Object o : horarioVO.getHorarioDisponibilidadeVOs()) {
                    HorarioDisponibilidadeVO horarioDisponibilidade = (HorarioDisponibilidadeVO) o;
                    horarioDisponibilidade.setHora2230(false);
                    horarioDisponibilidade.setNoturno(false);
                }
            }
        } else {
            // se campo foi marcado, validar se toda a coluna ou toda a linha está marcada
            if(obj.getHora2230()) {
                ((HorarioDisponibilidadeVO) getHorarioVO().getHorarioDisponibilidadeVOs().get(0)).setHora2230(true);
                for(HorarioDisponibilidadeVO horarioDisponibilidade : (ArrayList<HorarioDisponibilidadeVO>) horarioVO.getHorarioDisponibilidadeVOs()) {
                    if(!horarioDisponibilidade.getHora2230()) {
                        ((HorarioDisponibilidadeVO) getHorarioVO().getHorarioDisponibilidadeVOs().get(0)).setHora2230(false);
                    }
                }
                deveSetarNoturno(obj);
            } else {
                obj.setNoturno(false);
                ((HorarioDisponibilidadeVO) getHorarioVO().getHorarioDisponibilidadeVOs().get(0)).setHora2230(false);
            }
        }
    }

    public void MarcaTodaColuna2300() {
        HorarioDisponibilidadeVO obj = (HorarioDisponibilidadeVO) context().getExternalContext().getRequestMap().get("horarioDisponibilidadeVO");        
        if (obj.getIdentificador().equals("")) {
            if (obj.getHora2300()) {
                for (Object o : horarioVO.getHorarioDisponibilidadeVOs()) {
                    HorarioDisponibilidadeVO horarioDisponibilidade = (HorarioDisponibilidadeVO) o;
                    horarioDisponibilidade.setHora2300(true);

                    deveSetarNoturno(horarioDisponibilidade);
                }
            }
            if (!obj.getHora2300()) {
                for (Object o : horarioVO.getHorarioDisponibilidadeVOs()) {
                    HorarioDisponibilidadeVO horarioDisponibilidade = (HorarioDisponibilidadeVO) o;
                    horarioDisponibilidade.setHora2300(false);
                    horarioDisponibilidade.setNoturno(false);
                }
            }
        } else {
            // se campo foi marcado, validar se toda a coluna ou toda a linha está marcada
            if(obj.getHora2300()) {
                ((HorarioDisponibilidadeVO) getHorarioVO().getHorarioDisponibilidadeVOs().get(0)).setHora2300(true);
                for(HorarioDisponibilidadeVO horarioDisponibilidade : (ArrayList<HorarioDisponibilidadeVO>) horarioVO.getHorarioDisponibilidadeVOs()) {
                    if(!horarioDisponibilidade.getHora2300()) {
                        ((HorarioDisponibilidadeVO) getHorarioVO().getHorarioDisponibilidadeVOs().get(0)).setHora2300(false);
                    }
                }
                deveSetarNoturno(obj);
            } else {
                obj.setNoturno(false);
                ((HorarioDisponibilidadeVO) getHorarioVO().getHorarioDisponibilidadeVOs().get(0)).setHora2300(false);
            }
        }
    }

    public void MarcaTodaColuna2330() {
        HorarioDisponibilidadeVO obj = (HorarioDisponibilidadeVO) context().getExternalContext().getRequestMap().get("horarioDisponibilidadeVO");        
        if (obj.getIdentificador().equals("")) {
            if (obj.getHora2330()) {
                for (Object o : horarioVO.getHorarioDisponibilidadeVOs()) {
                    HorarioDisponibilidadeVO horarioDisponibilidade = (HorarioDisponibilidadeVO) o;
                    horarioDisponibilidade.setHora2330(true);

                    deveSetarNoturno(horarioDisponibilidade);
                }
            }
            if (!obj.getHora2330()) {
                for (Object o : horarioVO.getHorarioDisponibilidadeVOs()) {
                    HorarioDisponibilidadeVO horarioDisponibilidade = (HorarioDisponibilidadeVO) o;
                    horarioDisponibilidade.setHora2330(false);
                    horarioDisponibilidade.setNoturno(false);
                }
            }
        } else {
            // se campo foi marcado, validar se toda a coluna ou toda a linha está marcada
            if(obj.getHora2330()) {
                ((HorarioDisponibilidadeVO) getHorarioVO().getHorarioDisponibilidadeVOs().get(0)).setHora2330(true);
                for(HorarioDisponibilidadeVO horarioDisponibilidade : (ArrayList<HorarioDisponibilidadeVO>) horarioVO.getHorarioDisponibilidadeVOs()) {
                    if(!horarioDisponibilidade.getHora2330()) {
                        ((HorarioDisponibilidadeVO) getHorarioVO().getHorarioDisponibilidadeVOs().get(0)).setHora2330(false);
                    }
                }
                deveSetarNoturno(obj);
            } else {
                obj.setNoturno(false);
                ((HorarioDisponibilidadeVO) getHorarioVO().getHorarioDisponibilidadeVOs().get(0)).setHora2330(false);
            }
        }
    }

    /**
     * Operação responsável por processar a exclusão um objeto da classe <code>HorarioVO</code>
     * Após a exclusão ela automaticamente aciona a rotina para uma nova inclusão.
     */
    public String excluir() {
        try {
            getFacade().getHorario().excluir(horarioVO);
            incluirLogExclusao();
            setHorarioVO(new HorarioVO());

            setHorarioDisponibilidadeVODomingo(new HorarioDisponibilidadeVO());
            setHorarioDisponibilidadeVOSegunda(new HorarioDisponibilidadeVO());
            setHorarioDisponibilidadeVOTerca(new HorarioDisponibilidadeVO());
            setHorarioDisponibilidadeVOQuarta(new HorarioDisponibilidadeVO());
            setHorarioDisponibilidadeVOQuinta(new HorarioDisponibilidadeVO());
            setHorarioDisponibilidadeVOSexta(new HorarioDisponibilidadeVO());
            setHorarioDisponibilidadeVOSabado(new HorarioDisponibilidadeVO());
            setMensagemID("msg_dados_excluidos");
            setSucesso(true);
            setErro(false);
            return "consultar";
        } catch (Exception e) {
            if(e.getMessage().contains("ERRO: atualização ou exclusão em tabela \"horario\" viola restrição de chave estrangeira") || e.getMessage().contains("ERROR: update or delete on table \"horario\" violates foreign key")){
                setMensagemDetalhada("Este Horário não pode ser excluído, pois está sendo utilizado!");
            }else {
                setMensagemDetalhada("msg_erro", e.getMessage());
            }
            setSucesso(false);
            setErro(true);
            return "editar";
        }
    }

    /* Método responsável por adicionar um novo objeto da classe <code>HorarioDisponibilidade</code>
     * para o objeto <code>horarioVO</code> da classe <code>Horario</code>
     */
    public String adicionarHorarioDisponibilidade() throws Exception {
        try {
            getHorarioVO().adicionarObjHorarioDisponibilidadeVOs(getTodos());
            getHorarioVO().adicionarObjHorarioDisponibilidadeVOs(getHorarioDisponibilidadeVODomingo());
            getHorarioVO().adicionarObjHorarioDisponibilidadeVOs(getHorarioDisponibilidadeVOSegunda());
            getHorarioVO().adicionarObjHorarioDisponibilidadeVOs(getHorarioDisponibilidadeVOTerca());
            getHorarioVO().adicionarObjHorarioDisponibilidadeVOs(getHorarioDisponibilidadeVOQuarta());
            getHorarioVO().adicionarObjHorarioDisponibilidadeVOs(getHorarioDisponibilidadeVOQuinta());
            getHorarioVO().adicionarObjHorarioDisponibilidadeVOs(getHorarioDisponibilidadeVOSexta());
            getHorarioVO().adicionarObjHorarioDisponibilidadeVOs(getHorarioDisponibilidadeVOSabado());
            this.setTodos(new HorarioDisponibilidadeVO());
            this.setHorarioDisponibilidadeVODomingo(new HorarioDisponibilidadeVO());
            this.setHorarioDisponibilidadeVOSegunda(new HorarioDisponibilidadeVO());
            this.setHorarioDisponibilidadeVOTerca(new HorarioDisponibilidadeVO());
            this.setHorarioDisponibilidadeVOQuarta(new HorarioDisponibilidadeVO());
            this.setHorarioDisponibilidadeVOQuinta(new HorarioDisponibilidadeVO());
            this.setHorarioDisponibilidadeVOSexta(new HorarioDisponibilidadeVO());
            this.setHorarioDisponibilidadeVOSabado(new HorarioDisponibilidadeVO());
            setMensagemID("msg_dados_adicionados");
            return "editar";
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            return "editar";
        }
    }

    /* Método responsável por disponibilizar dados de um objeto da classe <code>HorarioDisponibilidade</code>
     * para edição pelo usuário.
     */
    public String editarHorarioDisponibilidade() throws Exception {
        HorarioDisponibilidadeVO obj = (HorarioDisponibilidadeVO) context().getExternalContext().getRequestMap().get("horarioDisponibilidade");
        if (obj.getIdentificador().equals("")) {
            setTodos(obj);
        }
        if (obj.getIdentificador().equals("Domingo")) {
            setHorarioDisponibilidadeVODomingo(obj);
        }
        if (obj.getIdentificador().equals("Segunda")) {
            setHorarioDisponibilidadeVOSegunda(obj);
        }
        if (obj.getIdentificador().equals("Terca")) {
            setHorarioDisponibilidadeVOTerca(obj);
        }
        if (obj.getIdentificador().equals("Quarta")) {
            setHorarioDisponibilidadeVOQuarta(obj);
        }
        if (obj.getIdentificador().equals("Quinta")) {
            setHorarioDisponibilidadeVOQuinta(obj);
        }
        if (obj.getIdentificador().equals("Sexta")) {
            setHorarioDisponibilidadeVOSexta(obj);
        }
        if (obj.getIdentificador().equals("Sábado")) {
            setHorarioDisponibilidadeVOSabado(obj);
        }

        return "editar";
    }

    /* Método responsável por remover um novo objeto da classe <code>HorarioDisponibilidade</code>
     * do objeto <code>horarioVO</code> da classe <code>Horario</code>
     */
    public String removerHorarioDisponibilidade() throws Exception {
        HorarioDisponibilidadeVO obj = (HorarioDisponibilidadeVO) context().getExternalContext().getRequestMap().get("horarioDisponibilidade");
        getHorarioVO().excluirObjHorarioDisponibilidadeVOs(obj.getIdentificador());
        setMensagemID("msg_dados_excluidos");
        return "editar";
    }

    public void irPaginaInicial() throws Exception {
        controleConsulta.setPaginaAtual(1);
        this.consultar();
    }

    public void irPaginaAnterior() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getPaginaAtual() - 1);
        this.consultar();
    }

    public void irPaginaPosterior() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getPaginaAtual() + 1);
        this.consultar();
    }

    public void irPaginaFinal() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getNrTotalPaginas());
        this.consultar();
    }

    /* Método responsável por inicializar List<SelectItem> de valores do 
     * ComboBox correspondente ao atributo <code>tipoValor</code>
     */
    public List<SelectItem> getListaSelectItemTipoValor() throws Exception {
        List<SelectItem> objs = new ArrayList<SelectItem>();
        objs.add(new SelectItem("", ""));
        Hashtable valor = (Hashtable) Dominios.getTipoValor();
        Enumeration keys = valor.keys();
        while (keys.hasMoreElements()) {
            String value = (String) keys.nextElement();
            String label = (String) valor.get(value);
            objs.add(new SelectItem(value, label));
        }

        SelectItemOrdemValor ordenador = new SelectItemOrdemValor();
        Collections.sort((List) objs, ordenador);
        return objs;
    }

    /**
     * Rotina responsável por preencher a combo de consulta da telas.
     */
    public List<SelectItem> getTipoConsultaCombo() {
        List<SelectItem> itens = new ArrayList<SelectItem>();
        itens.add(new SelectItem("descricao", "Descrição"));
        itens.add(new SelectItem("codigo", "Código"));
        return itens;
    }

    /**
     * Rotina responsável por organizar a paginação entre as páginas resultantes de uma consulta.
     */
    public String inicializarConsultar() {
        setPaginaAtualDeTodas("0/0");
        setListaConsulta(new ArrayList());
        definirVisibilidadeLinksNavegacao(0, 0);
        setMensagemID("msg_entre_prmconsulta");
        setSucesso(false);
        setErro(false);
        return "consultar";
    }

    public void inicializarHorariosDisponivel() throws Exception {
        getHorarioDisponibilidadeVODomingo().setIdentificador("Domingo");
        getHorarioDisponibilidadeVODomingo().setDesenharTodos(true);
        getHorarioDisponibilidadeVOSegunda().setIdentificador("Segunda");
        getHorarioDisponibilidadeVOSegunda().setDesenharTodos(true);
        getHorarioDisponibilidadeVOTerca().setIdentificador("Terça");
        getHorarioDisponibilidadeVOTerca().setDesenharTodos(true);
        getHorarioDisponibilidadeVOQuarta().setIdentificador("Quarta");
        getHorarioDisponibilidadeVOQuarta().setDesenharTodos(true);
        getHorarioDisponibilidadeVOQuinta().setIdentificador("Quinta");
        getHorarioDisponibilidadeVOQuinta().setDesenharTodos(true);
        getHorarioDisponibilidadeVOSexta().setIdentificador("Sexta");
        getHorarioDisponibilidadeVOSexta().setDesenharTodos(true);
        getHorarioDisponibilidadeVOSabado().setIdentificador("Sábado");
        getHorarioDisponibilidadeVOSabado().setDesenharTodos(true);
        getTodos().setIdentificador("");
        adicionarHorarioDisponibilidade();
    }

    /**
     * Operação que inicializa as Interfaces Façades com os respectivos objetos de
     * persistência dos dados no banco de dados. 
     */
    protected boolean inicializarFacades() {
        try {
            super.inicializarFacades();
            return true;
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro_conectarBD", e.getMessage());
            return false;
        }
    }

    public HorarioDisponibilidadeVO getHorarioDisponibilidadeVODomingo() {
        return horarioDisponibilidadeVODomingo;
    }

    public void setHorarioDisponibilidadeVODomingo(HorarioDisponibilidadeVO horarioDisponibilidadeVODomingo) {
        this.horarioDisponibilidadeVODomingo = horarioDisponibilidadeVODomingo;
    }

    public HorarioDisponibilidadeVO getHorarioDisponibilidadeVOQuarta() {
        return horarioDisponibilidadeVOQuarta;
    }

    public void setHorarioDisponibilidadeVOQuarta(HorarioDisponibilidadeVO horarioDisponibilidadeVOQuarta) {
        this.horarioDisponibilidadeVOQuarta = horarioDisponibilidadeVOQuarta;
    }

    public HorarioDisponibilidadeVO getHorarioDisponibilidadeVOQuinta() {
        return horarioDisponibilidadeVOQuinta;
    }

    public void setHorarioDisponibilidadeVOQuinta(HorarioDisponibilidadeVO horarioDisponibilidadeVOQuinta) {
        this.horarioDisponibilidadeVOQuinta = horarioDisponibilidadeVOQuinta;
    }

    public HorarioDisponibilidadeVO getHorarioDisponibilidadeVOSabado() {
        return horarioDisponibilidadeVOSabado;
    }

    public void setHorarioDisponibilidadeVOSabado(HorarioDisponibilidadeVO horarioDisponibilidadeVOSabado) {
        this.horarioDisponibilidadeVOSabado = horarioDisponibilidadeVOSabado;
    }

    public HorarioDisponibilidadeVO getHorarioDisponibilidadeVOSegunda() {
        return horarioDisponibilidadeVOSegunda;
    }

    public void setHorarioDisponibilidadeVOSegunda(HorarioDisponibilidadeVO horarioDisponibilidadeVOSegunda) {
        this.horarioDisponibilidadeVOSegunda = horarioDisponibilidadeVOSegunda;
    }

    public HorarioDisponibilidadeVO getHorarioDisponibilidadeVOSexta() {
        return horarioDisponibilidadeVOSexta;
    }

    public void setHorarioDisponibilidadeVOSexta(HorarioDisponibilidadeVO horarioDisponibilidadeVOSexta) {
        this.horarioDisponibilidadeVOSexta = horarioDisponibilidadeVOSexta;
    }

    public HorarioDisponibilidadeVO getHorarioDisponibilidadeVOTerca() {
        return horarioDisponibilidadeVOTerca;
    }

    public void setHorarioDisponibilidadeVOTerca(HorarioDisponibilidadeVO horarioDisponibilidadeVOTerca) {
        this.horarioDisponibilidadeVOTerca = horarioDisponibilidadeVOTerca;
    }

    public HorarioVO getHorarioVO() {
        return horarioVO;
    }

    public void setHorarioVO(HorarioVO horarioVO) {
        this.horarioVO = horarioVO;
    }

    public Boolean getDesabilitarPreenchimento() {
        return desabilitarPreenchimento;
    }

    public void setDesabilitarPreenchimento(Boolean desabilitarPreenchimento) {
        this.desabilitarPreenchimento = desabilitarPreenchimento;
    }

    public HorarioDisponibilidadeVO getTodos() {
        return todos;
    }

    public void setTodos(HorarioDisponibilidadeVO todos) {
        this.todos = todos;
    }

    public void exportar(ActionEvent evt) throws Exception {
        ExportadorListaControle exportadorListaControle = (ExportadorListaControle) JSFUtilities.getFromRequest(ExportadorListaControle.class.getSimpleName());
        String paramsTableFiltrada = context().getExternalContext().getRequestParameterMap().get("paramsTableFiltrada");

        String[] split = paramsTableFiltrada.split(",");
        String campoOrdenacao = split[0].replace("[", "");
        String ordem = split[1];
        String filtro = split[2].replace("''", "");
        filtro = filtro.replace("]", "");
        List listaParaImpressao = getFacade().getHorario().consultarParaImpressao(filtro, ordem, campoOrdenacao, getEmpresaLogado() == null ? 0 : getEmpresaLogado().getCodigo());
        exportadorListaControle.exportar(evt, listaParaImpressao, filtro, null);

    }
    
    public void incluirLogInclusao() throws Exception {
        try {
            horarioVO.setObjetoVOAntesAlteracao(new HorarioVO());
            horarioVO.setNovoObj(true);
            registrarLogObjetoVO(horarioVO, horarioVO.getCodigo(), "HORARIO", 0);
            incluirLogAlteracoesDisponibilidades();
        } catch (Exception e) {
            registrarLogErroObjetoVO("HORARIO", horarioVO.getCodigo(), "ERRO AO GERAR LOG DE INCLUSÃO DE HORARIO", this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
        horarioVO.setNovoObj(new Boolean(false));
        horarioVO.registrarObjetoVOAntesDaAlteracao();
        horarioVO.registrarDisponibilidadesAntesAlteracao();
    }
    
    public void incluirLogExclusao() throws Exception {
        try {
            horarioVO.setObjetoVOAntesAlteracao(new HorarioVO());
            horarioVO.setNovoObj(true);
            registrarLogExclusaoTodosDadosObjetoVO(horarioVO, horarioVO.getCodigo(), "HORARIO", 0);
        } catch (Exception e) {
            registrarLogErroObjetoVO("HORARIO", horarioVO.getCodigo(), "ERRO AO GERAR LOG DE EXCLUSÃO DE HORARIO ", this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
    }

    /**
     * Inclui o log de alteração de categoria
     * @throws Exception
     */
    public void incluirLogAlteracao() throws Exception {
        try {
            registrarLogObjetoVO(horarioVO, horarioVO.getCodigo(), "HORARIO", 0);
            incluirLogAlteracoesDisponibilidades();
        } catch (Exception e) {
            registrarLogErroObjetoVO("HORARIO", horarioVO.getCodigo(), "ERRO AO GERAR LOG DE ALTERAÇÃO DE HORARIO ", this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
        horarioVO.registrarObjetoVOAntesDaAlteracao();
        horarioVO.registrarDisponibilidadesAntesAlteracao();
    }
    
     public void realizarConsultaLogObjetoSelecionado() {
        LoginControle loginControle = (LoginControle) context().getExternalContext().getSessionMap().get("LoginControle");
        String nomeClasse = horarioVO.getClass().getSimpleName();
        nomeClasse = nomeClasse.substring(0, nomeClasse.length() - 2);
        loginControle.setNomeClasse(loginControle.getNomeEntidadeCamposLog("prt_" + nomeClasse + "_tituloForm"));
        loginControle.consultarLogObjetoSelecionado(nomeClasse.toUpperCase(), horarioVO.getCodigo(), 0);
    }
    public void realizarConsultaLogObjetoGeral() {
       horarioVO = new HorarioVO();
       realizarConsultaLogObjetoSelecionado();
    }

    private void incluirLogAlteracoesDisponibilidades() throws Exception {
        if(!horarioVO.getLivre()){
            if(horarioVO.getNovoObj() || ((HorarioVO) horarioVO.getObjetoVOAntesAlteracao()).getLivre()){
                for(HorarioDisponibilidadeVO atual : horarioVO.getHorarioDisponibilidadeVOs()){
                    if(!UteisValidacao.emptyString(atual.getIdentificador())){
                        atual.setNovoObj(true);
                        incluirLogsAlteracoesHorarioDisponibilidade(new HorarioDisponibilidadeVO(), atual, horarioVO.getNovoObj());
                        atual.setNovoObj(false);
                    }
                }
            } else {
                for(HorarioDisponibilidadeVO atual : horarioVO.getHorarioDisponibilidadeVOs()){
                    for(HorarioDisponibilidadeVO anterior : horarioVO.getHorarioDisponibilidadeVOsAntesAlteracao()){
                        if(!UteisValidacao.emptyString(atual.getIdentificador()) && atual.getIdentificador().equals(anterior.getIdentificador())){
                            incluirLogsAlteracoesHorarioDisponibilidade(anterior, atual ,false);
                            break;
                        }
                    }
                }
            }
            
        }
    }

    private void incluirLogsAlteracoesHorarioDisponibilidade(HorarioDisponibilidadeVO anterior, HorarioDisponibilidadeVO atual, boolean inclusao) throws Exception {
        try {
            List logs = new ArrayList();
            LogVO logVO = new LogVO();
            logVO.setOperacao(inclusao ? "INCLUSÃO" : "ALTERAÇÃO");
            logVO.setChavePrimaria(String.valueOf(horarioVO.getCodigo()));
            logVO.setNomeEntidade("HORARIO");
            logVO.setNomeEntidadeDescricao("Horario - HorarioDisponibilidade");
            logVO.setResponsavelAlteracao(getUsuarioLogado().getNome());
            logVO.setUserOAMD(getUsuarioLogado().getUserOamd());
            logVO.setDataAlteracao(negocio.comuns.utilitarias.Calendario.hoje());
            logVO.setPessoa(0);
            
            if(!anterior.getHora0000().equals(atual.getHora0000())){
                LogVO log =  (LogVO) logVO.getClone(true);
                log.setNomeCampo(atual.getIdentificador() +" - 00:00 - 00:00");
                log.setValorCampoAnterior(anterior.getHora0000().toString());
                log.setValorCampoAlterado(atual.getHora0000().toString());
                logs.add(log);
            }
            if(!anterior.getHora0030().equals(atual.getHora0030())){
                LogVO log =  (LogVO) logVO.getClone(true);
                log.setNomeCampo(atual.getIdentificador() +" - 00:30 - 01:00");
                log.setValorCampoAnterior(anterior.getHora0030().toString());
                log.setValorCampoAlterado(atual.getHora0030().toString());
                logs.add(log);
            }
            if(!anterior.getHora0100().equals(atual.getHora0100())){
                LogVO log =  (LogVO) logVO.getClone(true);
                log.setNomeCampo(atual.getIdentificador() +" - 01:00 - 01:30");
                log.setValorCampoAnterior(anterior.getHora0100().toString());
                log.setValorCampoAlterado(atual.getHora0100().toString());
                logs.add(log);
            }
            if(!anterior.getHora0130().equals(atual.getHora0130())){
                LogVO log =  (LogVO) logVO.getClone(true);
                log.setNomeCampo(atual.getIdentificador() +" - 01:30 - 02:00");
                log.setValorCampoAnterior(anterior.getHora0130().toString());
                log.setValorCampoAlterado(atual.getHora0130().toString());
                logs.add(log);
            }
            if(!anterior.getHora0200().equals(atual.getHora0200())){
                LogVO log =  (LogVO) logVO.getClone(true);
                log.setNomeCampo(atual.getIdentificador() +" - 02:00 - 02:30");
                log.setValorCampoAnterior(anterior.getHora0200().toString());
                log.setValorCampoAlterado(atual.getHora0200().toString());
                logs.add(log);
            }
            if(!anterior.getHora0230().equals(atual.getHora0230())){
                LogVO log =  (LogVO) logVO.getClone(true);
                log.setNomeCampo(atual.getIdentificador() +" - 02:30 - 03:00");
                log.setValorCampoAnterior(anterior.getHora0230().toString());
                log.setValorCampoAlterado(atual.getHora0230().toString());
                logs.add(log);
            }
            if(!anterior.getHora0300().equals(atual.getHora0300())){
                LogVO log =  (LogVO) logVO.getClone(true);
                log.setNomeCampo(atual.getIdentificador() +" - 03:00 - 03:30");
                log.setValorCampoAnterior(anterior.getHora0300().toString());
                log.setValorCampoAlterado(atual.getHora0300().toString());
                logs.add(log);
            }
            if(!anterior.getHora0000().equals(atual.getHora0330())){
                LogVO log =  (LogVO) logVO.getClone(true);
                log.setNomeCampo(atual.getIdentificador() +" - 03:30 - 04:00");
                log.setValorCampoAnterior(anterior.getHora0330().toString());
                log.setValorCampoAlterado(atual.getHora0330().toString());
                logs.add(log);
            }
            if(!anterior.getHora0400().equals(atual.getHora0400())){
                LogVO log =  (LogVO) logVO.getClone(true);
                log.setNomeCampo(atual.getIdentificador() +" - 04:00 - 04:30");
                log.setValorCampoAnterior(anterior.getHora0400().toString());
                log.setValorCampoAlterado(atual.getHora0400().toString());
                logs.add(log);
            }
            if(!anterior.getHora0430().equals(atual.getHora0430())){
                LogVO log =  (LogVO) logVO.getClone(true);
                log.setNomeCampo(atual.getIdentificador() +" - 04:30 - 05:00");
                log.setValorCampoAnterior(anterior.getHora0430().toString());
                log.setValorCampoAlterado(atual.getHora0430().toString());
                logs.add(log);
            }
            if(!anterior.getHora0500().equals(atual.getHora0500())){
                LogVO log =  (LogVO) logVO.getClone(true);
                log.setNomeCampo(atual.getIdentificador() +" - 05:00 - 05:30");
                log.setValorCampoAnterior(anterior.getHora0500().toString());
                log.setValorCampoAlterado(atual.getHora0500().toString());
                logs.add(log);
            }
            if(!anterior.getHora0530().equals(atual.getHora0530())){
                LogVO log =  (LogVO) logVO.getClone(true);
                log.setNomeCampo(atual.getIdentificador() +" - 05:30 - 06:00");
                log.setValorCampoAnterior(anterior.getHora0530().toString());
                log.setValorCampoAlterado(atual.getHora0530().toString());
                logs.add(log);
            }
            if(!anterior.getHora0600().equals(atual.getHora0600())){
                LogVO log =  (LogVO) logVO.getClone(true);
                log.setNomeCampo(atual.getIdentificador() +" - 06:00 - 06:30");
                log.setValorCampoAnterior(anterior.getHora0600().toString());
                log.setValorCampoAlterado(atual.getHora0600().toString());
                logs.add(log);
            }
            if(!anterior.getHora0630().equals(atual.getHora0630())){
                LogVO log =  (LogVO) logVO.getClone(true);
                log.setNomeCampo(atual.getIdentificador() +" - 06:30 - 07:00");
                log.setValorCampoAnterior(anterior.getHora0630().toString());
                log.setValorCampoAlterado(atual.getHora0630().toString());
                logs.add(log);
            }
            if(!anterior.getHora0700().equals(atual.getHora0700())){
                LogVO log =  (LogVO) logVO.getClone(true);
                log.setNomeCampo(atual.getIdentificador() +" - 07:00 - 07:30");
                log.setValorCampoAnterior(anterior.getHora0700().toString());
                log.setValorCampoAlterado(atual.getHora0700().toString());
                logs.add(log);
            }
            if(!anterior.getHora0730().equals(atual.getHora0730())){
                LogVO log =  (LogVO) logVO.getClone(true);
                log.setNomeCampo(atual.getIdentificador() +" - 07:30 - 08:00");
                log.setValorCampoAnterior(anterior.getHora0730().toString());
                log.setValorCampoAlterado(atual.getHora0730().toString());
                logs.add(log);
            }
            
            if(!anterior.getHora0800().equals(atual.getHora0800())){
                LogVO log =  (LogVO) logVO.getClone(true);
                log.setNomeCampo(atual.getIdentificador() +" - 08:00 - 08:30");
                log.setValorCampoAnterior(anterior.getHora0800().toString());
                log.setValorCampoAlterado(atual.getHora0800().toString());
                logs.add(log);
            }
            if(!anterior.getHora0830().equals(atual.getHora0830())){
                LogVO log =  (LogVO) logVO.getClone(true);
                log.setNomeCampo(atual.getIdentificador() +" - 08:30 - 09:00");
                log.setValorCampoAnterior(anterior.getHora0830().toString());
                log.setValorCampoAlterado(atual.getHora0830().toString());
                logs.add(log);
            }
            if(!anterior.getHora0900().equals(atual.getHora0900())){
                LogVO log =  (LogVO) logVO.getClone(true);
                log.setNomeCampo(atual.getIdentificador() +" - 09:00 - 09:30");
                log.setValorCampoAnterior(anterior.getHora0900().toString());
                log.setValorCampoAlterado(atual.getHora0900().toString());
                logs.add(log);
            }
            if(!anterior.getHora0930().equals(atual.getHora0930())){
                LogVO log =  (LogVO) logVO.getClone(true);
                log.setNomeCampo(atual.getIdentificador() +" - 09:30 - 10:00");
                log.setValorCampoAnterior(anterior.getHora0930().toString());
                log.setValorCampoAlterado(atual.getHora0930().toString());
                logs.add(log);
            }
            if(!anterior.getHora1000().equals(atual.getHora1000())){
                LogVO log =  (LogVO) logVO.getClone(true);
                log.setNomeCampo(atual.getIdentificador() +" - 10:00 - 10:30");
                log.setValorCampoAnterior(anterior.getHora1000().toString());
                log.setValorCampoAlterado(atual.getHora1000().toString());
                logs.add(log);
            }
            if(!anterior.getHora1030().equals(atual.getHora1030())){
                LogVO log =  (LogVO) logVO.getClone(true);
                log.setNomeCampo(atual.getIdentificador() +" - 10:30 - 11:00");
                log.setValorCampoAnterior(anterior.getHora1030().toString());
                log.setValorCampoAlterado(atual.getHora1030().toString());
                logs.add(log);
            }
            if(!anterior.getHora1100().equals(atual.getHora1100())){
                LogVO log =  (LogVO) logVO.getClone(true);
                log.setNomeCampo(atual.getIdentificador() +" - 11:00 - 11:30");
                log.setValorCampoAnterior(anterior.getHora1100().toString());
                log.setValorCampoAlterado(atual.getHora1100().toString());
                logs.add(log);
            }
            if(!anterior.getHora1130().equals(atual.getHora1130())){
                LogVO log =  (LogVO) logVO.getClone(true);
                log.setNomeCampo(atual.getIdentificador() +" - 11:30 - 12:00");
                log.setValorCampoAnterior(anterior.getHora1130().toString());
                log.setValorCampoAlterado(atual.getHora1130().toString());
                logs.add(log);
            }
            if(!anterior.getHora1200().equals(atual.getHora1200())){
                LogVO log =  (LogVO) logVO.getClone(true);
                log.setNomeCampo(atual.getIdentificador() +" - 12:00 - 12:30");
                log.setValorCampoAnterior(anterior.getHora1200().toString());
                log.setValorCampoAlterado(atual.getHora1200().toString());
                logs.add(log);
            }
            if(!anterior.getHora1230().equals(atual.getHora1230())){
                LogVO log =  (LogVO) logVO.getClone(true);
                log.setNomeCampo(atual.getIdentificador() +" - 12:30 - 13:00");
                log.setValorCampoAnterior(anterior.getHora1230().toString());
                log.setValorCampoAlterado(atual.getHora1230().toString());
                logs.add(log);
            }
            if(!anterior.getHora1300().equals(atual.getHora1300())){
                LogVO log =  (LogVO) logVO.getClone(true);
                log.setNomeCampo(atual.getIdentificador() +" - 13:00 - 13:30");
                log.setValorCampoAnterior(anterior.getHora1300().toString());
                log.setValorCampoAlterado(atual.getHora1300().toString());
                logs.add(log);
            }
            if(!anterior.getHora1330().equals(atual.getHora1330())){
                LogVO log =  (LogVO) logVO.getClone(true);
                log.setNomeCampo(atual.getIdentificador() +" - 13:30 - 14:00");
                log.setValorCampoAnterior(anterior.getHora1330().toString());
                log.setValorCampoAlterado(atual.getHora1330().toString());
                logs.add(log);
            }
            if(!anterior.getHora1400().equals(atual.getHora1400())){
                LogVO log =  (LogVO) logVO.getClone(true);
                log.setNomeCampo(atual.getIdentificador() +" - 14:00 - 14:30");
                log.setValorCampoAnterior(anterior.getHora1400().toString());
                log.setValorCampoAlterado(atual.getHora1400().toString());
                logs.add(log);
            }
            if(!anterior.getHora1430().equals(atual.getHora1430())){
                LogVO log =  (LogVO) logVO.getClone(true);
                log.setNomeCampo(atual.getIdentificador() +" - 14:30 - 15:00");
                log.setValorCampoAnterior(anterior.getHora1430().toString());
                log.setValorCampoAlterado(atual.getHora1430().toString());
                logs.add(log);
            }
            if(!anterior.getHora1500().equals(atual.getHora1500())){
                LogVO log =  (LogVO) logVO.getClone(true);
                log.setNomeCampo(atual.getIdentificador() +" - 15:00 - 15:30");
                log.setValorCampoAnterior(anterior.getHora1500().toString());
                log.setValorCampoAlterado(atual.getHora1500().toString());
                logs.add(log);
            }
            if(!anterior.getHora1530().equals(atual.getHora1530())){
                LogVO log =  (LogVO) logVO.getClone(true);
                log.setNomeCampo(atual.getIdentificador() +" - 15:30 - 15:00");
                log.setValorCampoAnterior(anterior.getHora1530().toString());
                log.setValorCampoAlterado(atual.getHora1530().toString());
                logs.add(log);
            }
            if(!anterior.getHora1600().equals(atual.getHora1600())){
                LogVO log =  (LogVO) logVO.getClone(true);
                log.setNomeCampo(atual.getIdentificador() +" - 16:00 - 16:30");
                log.setValorCampoAnterior(anterior.getHora1600().toString());
                log.setValorCampoAlterado(atual.getHora1600().toString());
                logs.add(log);
            }
            if(!anterior.getHora1630().equals(atual.getHora1630())){
                LogVO log =  (LogVO) logVO.getClone(true);
                log.setNomeCampo(atual.getIdentificador() +" - 16:30 - 17:00");
                log.setValorCampoAnterior(anterior.getHora1630().toString());
                log.setValorCampoAlterado(atual.getHora1630().toString());
                logs.add(log);
            }
            if(!anterior.getHora1700().equals(atual.getHora1700())){
                LogVO log =  (LogVO) logVO.getClone(true);
                log.setNomeCampo(atual.getIdentificador() +" - 17:00 - 17:30");
                log.setValorCampoAnterior(anterior.getHora1700().toString());
                log.setValorCampoAlterado(atual.getHora1700().toString());
                logs.add(log);
            }
            if(!anterior.getHora1730().equals(atual.getHora1730())){
                LogVO log =  (LogVO) logVO.getClone(true);
                log.setNomeCampo(atual.getIdentificador() +" - 17:30 - 18:00");
                log.setValorCampoAnterior(anterior.getHora1730().toString());
                log.setValorCampoAlterado(atual.getHora1730().toString());
                logs.add(log);
            }
            if(!anterior.getHora1800().equals(atual.getHora1800())){
                LogVO log =  (LogVO) logVO.getClone(true);
                log.setNomeCampo(atual.getIdentificador() +" - 18:00 - 18:30");
                log.setValorCampoAnterior(anterior.getHora1800().toString());
                log.setValorCampoAlterado(atual.getHora1800().toString());
                logs.add(log);
            }
            if(!anterior.getHora1830().equals(atual.getHora1830())){
                LogVO log =  (LogVO) logVO.getClone(true);
                log.setNomeCampo(atual.getIdentificador() +" - 18:30 - 19:00");
                log.setValorCampoAnterior(anterior.getHora1830().toString());
                log.setValorCampoAlterado(atual.getHora1830().toString());
                logs.add(log);
            }
            if(!anterior.getHora1900().equals(atual.getHora1900())){
                LogVO log =  (LogVO) logVO.getClone(true);
                log.setNomeCampo(atual.getIdentificador() +" - 19:00 - 19:30");
                log.setValorCampoAnterior(anterior.getHora1900().toString());
                log.setValorCampoAlterado(atual.getHora1900().toString());
                logs.add(log);
            }
            if(!anterior.getHora1930().equals(atual.getHora1930())){
                LogVO log =  (LogVO) logVO.getClone(true);
                log.setNomeCampo(atual.getIdentificador() +" - 19:30 - 20:00");
                log.setValorCampoAnterior(anterior.getHora1930().toString());
                log.setValorCampoAlterado(atual.getHora1930().toString());
                logs.add(log);
            }
            if(!anterior.getHora2000().equals(atual.getHora2000())){
                LogVO log =  (LogVO) logVO.getClone(true);
                log.setNomeCampo(atual.getIdentificador() +" - 20:00 - 20:30");
                log.setValorCampoAnterior(anterior.getHora2000().toString());
                log.setValorCampoAlterado(atual.getHora2000().toString());
                logs.add(log);
            }
            if(!anterior.getHora2030().equals(atual.getHora2030())){
                LogVO log =  (LogVO) logVO.getClone(true);
                log.setNomeCampo(atual.getIdentificador() +" - 20:30 - 21:00");
                log.setValorCampoAnterior(anterior.getHora2030().toString());
                log.setValorCampoAlterado(atual.getHora2030().toString());
                logs.add(log);
            }
            if(!anterior.getHora2100().equals(atual.getHora2100())){
                LogVO log =  (LogVO) logVO.getClone(true);
                log.setNomeCampo(atual.getIdentificador() +" - 21:00 - 21:30");
                log.setValorCampoAnterior(anterior.getHora2100().toString());
                log.setValorCampoAlterado(atual.getHora2100().toString());
                logs.add(log);
            }
            if(!anterior.getHora2130().equals(atual.getHora2130())){
                LogVO log =  (LogVO) logVO.getClone(true);
                log.setNomeCampo(atual.getIdentificador() +" - 21:30 - 22:00");
                log.setValorCampoAnterior(anterior.getHora2130().toString());
                log.setValorCampoAlterado(atual.getHora2130().toString());
                logs.add(log);
            }
            if(!anterior.getHora2200().equals(atual.getHora2200())){
                LogVO log =  (LogVO) logVO.getClone(true);
                log.setNomeCampo(atual.getIdentificador() +" - 22:00 - 22:30");
                log.setValorCampoAnterior(anterior.getHora2200().toString());
                log.setValorCampoAlterado(atual.getHora2200().toString());
                logs.add(log);
            }
            if(!anterior.getHora2230().equals(atual.getHora2230())){
                LogVO log =  (LogVO) logVO.getClone(true);
                log.setNomeCampo(atual.getIdentificador() +" - 22:30 - 23:00");
                log.setValorCampoAnterior(anterior.getHora2230().toString());
                log.setValorCampoAlterado(atual.getHora2230().toString());
                logs.add(log);
            }
            if(!anterior.getHora2300().equals(atual.getHora2300())){
                LogVO log =  (LogVO) logVO.getClone(true);
                log.setNomeCampo(atual.getIdentificador() +" - 23:00 - 23:30");
                log.setValorCampoAnterior(anterior.getHora2300().toString());
                log.setValorCampoAlterado(atual.getHora2300().toString());
                logs.add(log);
            }
            if(!anterior.getHora2330().equals(atual.getHora2330())){
                LogVO log =  (LogVO) logVO.getClone(true);
                log.setNomeCampo(atual.getIdentificador() +" - 23:30 - 00:00");
                log.setValorCampoAnterior(anterior.getHora2330().toString());
                log.setValorCampoAlterado(atual.getHora2330().toString());
                logs.add(log);
            }
             
            
            
            if(!logs.isEmpty()){
                registrarLogObjetoVO(logs, horarioVO.getCodigo(),"HORARIO", 0);
            }
        } catch (Exception ex) {
             registrarLogErroObjetoVO("HORARIO", horarioVO.getCodigo(), "ERRO AO GERAR LOG DE ALTERAÇÕS DE HORARIO DISPONIBILIDADES ", this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
        }
    }

    public void setMsgAlert(String msgAlert) {
        this.msgAlert = msgAlert;
    }

    public String getMsgAlert() {
        if (msgAlert == null) {
            return "";
        }
        return msgAlert;
    }


    public void confirmarExcluir(){
        MensagemGenericaControle control = (MensagemGenericaControle) getControlador(MensagemGenericaControle.class);
        control.setMensagemDetalhada("", "");
        setMsgAlert("Richfaces.showModalPanel('mdlMensagemGenerica');");
        control.init("Exclusão de Horário",
                "Deseja excluir o Horário?",
                this, "excluir", "", "", "", "grupoBtnExcluir");
    }

    public String getSituacaoFiltro() {
        if (situacaoFiltro == null) {
            situacaoFiltro = "AT";
        }
        return situacaoFiltro;
    }
    public void setSituacaoFiltro(String situacaoFiltro) {
        this.situacaoFiltro = situacaoFiltro;
    }

    public List<SelectItem> getListaSelectItemSituacao() throws Exception {
        List<SelectItem> objs = new ArrayList<SelectItem>();
        objs.add(new SelectItem("AT", "Ativo"));
        objs.add(new SelectItem("NA", "Inativo"));
        objs.add(new SelectItem("TD", "Todos"));
        return objs;
    }

}

//relatorio
package controle.plano;

import java.awt.Graphics;
import java.awt.print.PageFormat;
import java.awt.Color;
import java.awt.Graphics2D;
import java.awt.print.Printable;
import java.awt.print.PrinterException;
import java.awt.print.PrinterJob;
import javax.print.PrintService;
import javax.print.attribute.HashPrintRequestAttributeSet;
import javax.print.attribute.PrintRequestAttributeSet;
import javax.print.attribute.standard.Copies;
import javax.print.attribute.standard.JobName;
import javax.print.attribute.standard.OrientationRequested;

public class ImprimirContrato implements Printable {

        private String text;

        /** 
         * Efetua impressão do texto passado como parâmetro. 
         * @param texto 
         */
        public ImprimirContrato(String texto) {
            text += texto; 
            PrintRequestAttributeSet aset = new HashPrintRequestAttributeSet();
            aset.add(OrientationRequested.PORTRAIT);
            aset.add(new Copies(1));
            aset.add(new JobName("Relatório", null));
            /* 
             * Crai um "Printer job" 
             */
            PrinterJob pj = PrinterJob.getPrinterJob();
            pj.setPrintable(this);

            /* 
             * Localiza um serviço de impressão 
             * que possa tratar esta requisção. 
             */
            PrintService[] services = PrinterJob.lookupPrintServices();

            if (services.length > 0) {
                System.out.println("Impressora seleionada" + services[0].getName());
                try {
                    pj.setPrintService(services[0]);
                    pj.pageDialog(aset);
                    if (pj.printDialog(aset)) {
                        pj.print(aset);
                    }
                } catch (PrinterException pe) {
                    System.err.println(pe);
                }
            }
        }

        public int print(Graphics g, PageFormat pf, int pageIndex) throws PrinterException {
            if (pageIndex == 0) {
                Graphics2D g2d = (Graphics2D) g;
                g2d.translate(pf.getImageableX(), pf.getImageableY());
                g2d.setColor(Color.black);

                /* 
                 * Imprime o texto passado, na coluna 10 e na linha 5 
                 * da página a ser impressa 
                 */
                g2d.drawString(text, 10, 5);

                return Printable.PAGE_EXISTS;
            } else {
                return Printable.NO_SUCH_PAGE;
            }
        }
    }

package controle.plano;

import br.com.pactosolucoes.ce.comuns.enumerador.DiaSemana;
import br.com.pactosolucoes.ce.comuns.ex.ValidacaoException;
import br.com.pactosolucoes.comuns.notificacao.RecursoSistema;
import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.enumeradores.TipoColaboradorEnum;
import br.com.pactosolucoes.enumeradores.TipoFiltroData;
import br.com.pactosolucoes.estrutura.ItemExportacaoEnum;
import br.com.pactosolucoes.mapaturmas.modelo.AlunoMapaTurmasTO;
import br.com.pactosolucoes.mapaturmas.modelo.DiaSemanaMapaTO;
import br.com.pactosolucoes.mapaturmas.modelo.HorarioMapaTurmaTO;
import br.com.pactosolucoes.mapaturmas.modelo.ItemMapaTurmasTO;
import br.com.pactosolucoes.turmas.servico.impl.TurmasServiceImpl;
import controle.arquitetura.security.AutorizacaoFuncionalidadeControle;
import controle.arquitetura.security.AutorizacaoFuncionalidadeListener;
import controle.arquitetura.security.LoginControle;
import controle.basico.clube.MensagemGenericaControle;
import controle.contrato.ContratoControle;
import controle.contrato.ManutencaoModalidadeControle;
import controle.crm.HistoricoContatoControle;
import negocio.comuns.arquitetura.LogVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.AulaDesmarcadaVO;
import negocio.comuns.basico.ChamadaTO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.basico.PresencaVO;
import negocio.comuns.contrato.ContratoModalidadeHorarioTurmaVO;
import negocio.comuns.contrato.ContratoModalidadeTurmaVO;
import negocio.comuns.contrato.ContratoModalidadeVO;
import negocio.comuns.contrato.HorarioTurmaConcatenadoTO;
import negocio.comuns.contrato.MatriculaAlunoHorarioTurmaVO;
import negocio.comuns.plano.AmbienteVO;
import negocio.comuns.plano.ConsultarAlunosTurmaVO;
import negocio.comuns.plano.ConsultarTurmaTO;
import negocio.comuns.plano.HorarioTurmaVO;
import negocio.comuns.plano.ModalidadeVO;
import negocio.comuns.plano.NivelTurmaVO;
import negocio.comuns.plano.PlanoModalidadeVezesSemanaVO;
import negocio.comuns.plano.TurmaVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.utilitarias.Conexao;
import relatorio.controle.arquitetura.SuperControleRelatorio;
import relatorio.controle.basico.FiltroDatasTO;
import relatorio.negocio.jdbc.contrato.MatriculaAlunoHorarioTurmaRel;
import servicos.impl.gestaoaula.GestaoAulaService;

import javax.faces.event.ActionEvent;
import javax.faces.event.PhaseId;
import javax.faces.event.ValueChangeEvent;
import javax.faces.model.SelectItem;
import java.io.File;
import java.sql.Connection;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR> Lhoji Shiozawa
 */
public class ConsultarTurmaControle extends SuperControleRelatorio {

    private Connection con;
    public static final int ORIGEM_CONTRATO = 1;
    public static final int ORIGEM_MANUTENCAO_MODALIDADE = 2;
    public static final int ORIGEM_CONTRATO_NOVO = 3;
    public static final int ORIGEM_REPOSICAO = 4;
    private boolean mostrarEmpresa = false;
    private String NOME_ENTIDADE = "PRESENCA";
    private PresencaVO presenca = new PresencaVO();
    private PessoaVO pessoa = new PessoaVO();
    private ConsultarTurmaTO consultarTurma = new ConsultarTurmaTO();
    private HorarioTurmaVO horarioTurma = new HorarioTurmaVO();
    private List<PresencaVO> colunas = new ArrayList<PresencaVO>();
    private List<ChamadaTO> listaChamada = new ArrayList<>();
    private HorarioTurmaConcatenadoTO horarioTurmaConcatenado = new HorarioTurmaConcatenadoTO();
    private HorarioTurmaConcatenadoTO horarioTurmaListaChamada = new HorarioTurmaConcatenadoTO();
    private List<ConsultarAlunosTurmaVO> listaAlunosTurma = new ArrayList<ConsultarAlunosTurmaVO>();
    private List<HorarioTurmaConcatenadoTO> listaHorarioTurmaConcatenado = new ArrayList<HorarioTurmaConcatenadoTO>();
    private UsuarioVO usuario = new UsuarioVO();
    private int totalizadorAlunos = 0;
    private Date mesReferencia = Calendario.getDataComHoraZerada(Calendario.hoje());
    private MatriculaAlunoHorarioTurmaRel matriculaAlunoHorarioTurmaRel = new MatriculaAlunoHorarioTurmaRel();
    private List listaRegistro;
    private List listaMatriculas;
    private boolean consultaTurma = true;
    private int origem = 0;
    private Date dataNasc = Calendario.hoje();
    private ContratoModalidadeVO contratoModalidade = new ContratoModalidadeVO();
    private Date dataContrato = null;
    private FiltroDatasTO filtroDatas = new FiltroDatasTO(TipoFiltroData.DATA, TipoFiltroData.INTERVALO_DE_DATA);
    private boolean todasTurmas = false;
    private boolean emProcessamento = false;
    private boolean pollEnabled = true;
    private boolean marcarTodos = true;
    private boolean renovacaoContrato = false;
    private boolean empresaPermiteRenovarContratosEmTurmasLotadas = false;
    private boolean apresentarModalTelefoneAluno = Boolean.FALSE;
    private ChamadaTO alunoTelefone;
    private String ordenacaoSelecionada = "nomeTurma";
    private String nomesAlunos = "abreviado";
    private List<SelectItem> itensOrdenacao = new ArrayList<SelectItem>();
    private List<SelectItem> itensNomes = new ArrayList<SelectItem>();
    private String vezesSemanaModalidade;
    private boolean planoCreditoTreino = false;
    private int nrVezesCreditoTreino = 0;
    private boolean considerarDesmarcoes = false;
    private Date dataReferenciaMapa = Calendario.hoje();
    public void atualizarSemanas() {
        filtroDatas.atualizarSemanas();
    }
    private List<HorarioMapaTurmaTO> mapaTurmas = new ArrayList<HorarioMapaTurmaTO>();
    private List listaSelectItemProfessor;
    private boolean permiteAlterarProfessorConsultaTurma;
    private Date dataInicio;
    private Date dataFim;

    private String frequenciaVagasMediaOcupacao = "0";
    private Integer frequenciaVagasLivres = 0;
    private Integer frequenciaVagasTotal = 0;
    private Integer frequenciaVagasOcupacao = 0;
    private boolean exibeRelatorioFrequenciaTurmas = false;

    public ConsultarTurmaControle() throws Exception {
    }

    public void novo() {
        try {
            consultarTurma = new ConsultarTurmaTO();
            obterUsuarioLogado();
            dataContrato = Calendario.hoje();
            dataNasc = null;
            inicializarUsuarioLogado();
            inicializarEmpresaLogado();
            montarListaEmpresa();
            montarListaModalidade();
            montarListaProfessor();
            inicializarColunas(false,Calendario.hoje(),Calendario.hoje());
            setMensagemDetalhada("", "");
            considerarDesmarcoes = false;
            dataReferenciaMapa = Calendario.hoje();
            gerarPeriodoConsultaMapa();
            consultarTurma.setModoReposicao(false);
            montarListaSelectItemProfessor();
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    private void inicializarUsuarioLogado() throws Exception {
        usuario = (UsuarioVO) getUsuarioLogado().getClone(true);
        if (usuario == null) {
            throw new Exception("Usuário Não Encontrado. Entre Novamente no Sistema.");
        }
        if(!exibeRelatorioFrequenciaTurmas) {
            mostrarEmpresa = usuario.getAdministrador();
        }
    }

    private void inicializarEmpresaLogado() throws Exception {
        // se usuario administrador
        if (mostrarEmpresa) {
            consultarTurma.setEmpresa(new EmpresaVO());
        } else {
            consultarTurma.setEmpresa((EmpresaVO) getEmpresaLogado().getClone(true));
        }
        // se empresa logada esta null
        if (consultarTurma.getEmpresa() == null) {
            throw new Exception("Empresa Não Encontrada. Entre Novamente no Sistema.");
        }
    }

    public void montarListaEmpresa() throws Exception {
        consultarTurma.setListaEmpresa(new ArrayList<>());
        if (mostrarEmpresa) {
            consultarTurma.getListaEmpresa().add(new SelectItem(0, exibeRelatorioFrequenciaTurmas ? "TODAS" : " "));
            List resultadoConsulta = getFacade().getEmpresa().consultarPorNome("",true, false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            Iterator i = resultadoConsulta.iterator();
            while (i.hasNext()) {
                EmpresaVO obj = (EmpresaVO) i.next();
                consultarTurma.getListaEmpresa().add(new SelectItem(obj.getCodigo(), obj.getNome()));
            }
        }
        montarListaModalidade();
        montarListaProfessor();
    }

    public void montarListaModalidade() throws Exception {
        consultarTurma.setModalidade(new ModalidadeVO());
        consultarTurma.setTurma(new TurmaVO());
        if(!exibeRelatorioFrequenciaTurmas) {
            consultarTurma.setProfessor(new ColaboradorVO());
        }
        consultarTurma.setListaModalidade(new ArrayList<>());
        consultarTurma.setListaTurma(new ArrayList<>());
        if (consultarTurma.getEmpresa().getCodigo() > 0 || exibeRelatorioFrequenciaTurmas) {
            consultarTurma.getListaModalidade().add(new SelectItem(0, ""));
            List resultadoConsulta;
            if (exibeRelatorioFrequenciaTurmas) {
                resultadoConsulta = getFacade().getModalidade().consultarPorNomeUtilizaTurmaV2("", consultarTurma.getEmpresa().getCodigo(), false, true, true, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            } else {
                resultadoConsulta = getFacade().getModalidade().consultarPorNomeUtilizaTurma("", consultarTurma.getEmpresa().getCodigo(), false, true, true, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            }
            for (Object aResultadoConsulta : resultadoConsulta) {
                ModalidadeVO obj = (ModalidadeVO) aResultadoConsulta;
                consultarTurma.getListaModalidade().add(new SelectItem(obj.getCodigo(), exibeRelatorioFrequenciaTurmas ? (obj.getCodigo() + " - " + obj.getNome()) : obj.getNome()));
            }
        }

        if(exibeRelatorioFrequenciaTurmas){
            montarListaTurma();
        }
    }

    public void montarListaTurma() throws Exception {
        if(!exibeRelatorioFrequenciaTurmas) {
            consultarTurma.setListaProfessor(new ArrayList<>());
            consultarTurma.setProfessor(new ColaboradorVO());
        }
        consultarTurma.setTurma(new TurmaVO());
        consultarTurma.setListaTurma(new ArrayList<>());

        if (consultarTurma.getModalidade().getCodigo() > 0 || exibeRelatorioFrequenciaTurmas) {
            consultarTurma.getListaTurma().add(new SelectItem(0, ""));
            List resultadoConsulta = getFacade().getTurma().consultarPorCodigoModalidadeVigenciaMaiorQueHoje(consultarTurma.getModalidade().getCodigo(), consultarTurma.getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);

            for (Object o : resultadoConsulta) {
                TurmaVO obj = (TurmaVO) o;
                if (!UteisValidacao.dataMenorDataAtual(Uteis.getDataJDBCTimestamp(obj.getDataFinalVigencia()))) {
                    consultarTurma.getListaTurma().add(new SelectItem(obj.getCodigo(), exibeRelatorioFrequenciaTurmas ? (obj.getCodigo() + " - " + obj.getIdentificador()) : obj.getIdentificador()));
                }
            }
        }
        if(!exibeRelatorioFrequenciaTurmas) {
            montarListaProfessor();
        }
    }

    public void montarListaAmbiente() throws Exception {
        consultarTurma.setListaAmbiente(new ArrayList<SelectItem>());
        List<AmbienteVO> ambs = getFacade().getAmbiente().consultarPorTurma(consultarTurma.getTurma().getCodigo(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        consultarTurma.getListaAmbiente().add(new SelectItem(0, ""));
        for (AmbienteVO n : ambs) {
            consultarTurma.getListaAmbiente().add(new SelectItem(n.getCodigo(), n.getDescricao()));
        }
    }

    public void montarListaNivel() throws Exception {
        consultarTurma.setListaNivel(new ArrayList<SelectItem>());
        List<NivelTurmaVO> niveis = getFacade().getNivelTurma().consultarPorTurma(consultarTurma.getTurma().getCodigo(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        consultarTurma.getListaNivel().add(new SelectItem(0, ""));
        for (NivelTurmaVO n : niveis) {
            consultarTurma.getListaNivel().add(new SelectItem(n.getCodigo(), n.getDescricao()));
        }
    }

    public void montarListaProfessor() throws Exception {
        montarListaNivel();
        montarListaAmbiente();
        consultarTurma.setProfessor(new ColaboradorVO());
        consultarTurma.setListaProfessor(new ArrayList<SelectItem>());

        Integer codigoEmpresa = getEmpresaLogado().getCodigo();

        if (consultarTurma.getEmpresa().getCodigo() > 0) {
            codigoEmpresa = consultarTurma.getEmpresa().getCodigo();
        }

        boolean apresentarSomenteTurmaQueForResponsavel = false;
        try {
            permissaoFuncionalidade(getUsuarioLogado(),
                    "PermitirConsultarTurmasUsuarioNaoForResponsavel",
                    "9.53 - Permitir ao usuário consultar turmas até as que não for responsável");
        } catch (Exception ignored) {
            apresentarSomenteTurmaQueForResponsavel = true;
        }

        if (codigoEmpresa > 0 || consultarTurma.getTurma().getCodigo() > 0) {
            consultarTurma.getListaProfessor().add(new SelectItem(0, ""));
            List resultadoConsulta = getFacade().getColaborador().consultarPorCodigoTurmaEOuModalidade(consultarTurma.getTurma().getCodigo(),
                    consultarTurma.getModalidade().getCodigo(), codigoEmpresa, Uteis.NIVELMONTARDADOS_MINIMOS);
            Iterator i = resultadoConsulta.iterator();
            while (i.hasNext()) {
                ColaboradorVO obj = (ColaboradorVO) i.next();
                if (!apresentarSomenteTurmaQueForResponsavel || obj.getPessoa().getCodigo().equals(getUsuarioLogado().getColaboradorVO().getPessoa().getCodigo())) {
                    consultarTurma.getListaProfessor().add(new SelectItem(obj.getCodigo(), obj.getPessoa().getNome()));
                }
            }
        }
    }

// * consulta turmas para mostrar na tela **************************************
    private List<String> filtrodiasSemana() {
        List<String> diasSemana = new ArrayList<String>();
        if (consultarTurma.isDomingo()) {
            diasSemana.add("DM");
        }
        if (consultarTurma.isSegunda()) {
            diasSemana.add("SG");
        }
        if (consultarTurma.isTerca()) {
            diasSemana.add("TR");
        }
        if (consultarTurma.isQuarta()) {
            diasSemana.add("QA");
        }
        if (consultarTurma.isQuinta()) {
            diasSemana.add("QI");
        }
        if (consultarTurma.isSexta()) {
            diasSemana.add("SX");
        }
        if (consultarTurma.isSabado()) {
            diasSemana.add("SB");
        }
        if (diasSemana.isEmpty()) {
            consultarTurma.setDomingo(true);
            consultarTurma.setSegunda(true);
            consultarTurma.setTerca(true);
            consultarTurma.setQuarta(true);
            consultarTurma.setQuinta(true);
            consultarTurma.setSexta(true);
            consultarTurma.setSabado(true);
        }
        return diasSemana;
    }

    private List<String> filtroHorarios() {
        List<String> horarios = new ArrayList<String>();
        if (consultarTurma.isH0001as0200()) {
            horarios.add("00:00 - 01:59");
        }
        if (consultarTurma.isH0201as0400()) {
            horarios.add("02:00 - 03:59");
        }
        if (consultarTurma.isH0401as0600()) {
            horarios.add("04:00 - 05:59");
        }
        if (consultarTurma.isH0601as0800()) {
            horarios.add("06:00 - 07:59");
        }
        if (consultarTurma.isH0801as1000()) {
            horarios.add("08:00 - 09:59");
        }
        if (consultarTurma.isH1001as1200()) {
            horarios.add("10:00 - 11:59");
        }
        if (consultarTurma.isH1201as1400()) {
            horarios.add("12:00 - 13:59");
        }
        if (consultarTurma.isH1401as1600()) {
            horarios.add("14:00 - 15:59");
        }
        if (consultarTurma.isH1601as1800()) {
            horarios.add("16:00 - 17:59");
        }
        if (consultarTurma.isH1801as2000()) {
            horarios.add("18:00 - 19:59");
        }
        if (consultarTurma.isH2001as2200()) {
            horarios.add("20:00 - 21:59");
        }
        if (consultarTurma.isH2201as0000()) {
            horarios.add("22:00 - 23:59");
        }
        return horarios;
    }

    public void consultarTurmas() {
        setEmpresaPermiteRenovarContratosEmTurmasLotadas(false);
        setRenovacaoContrato(false);
        this.frequenciaVagasMediaOcupacao = "0";
        this.frequenciaVagasLivres = 0;
        this.frequenciaVagasTotal = 0;
        this.frequenciaVagasOcupacao = 0;

        consultarTurmas(dataContrato, false, false);
    }

    public void consultarTurmasNegociacao() {
        setEmpresaPermiteRenovarContratosEmTurmasLotadas(false);
        setRenovacaoContrato(false);

        consultarTurmas(dataContrato, true, false);
    }

    public void consultarTurmasMapa() throws Exception {
        setEmpresaPermiteRenovarContratosEmTurmasLotadas(false);
        setRenovacaoContrato(false);

        filtroDatas.setarDatasFiltro();
        consultarTurmas(dataContrato, false, true);
    }

    public void consultarTurmas(Date data, Boolean negociacao, Boolean mapa) {
        setPollEnabled(true);
        setMsgAlert("Richfaces.showModalPanel('panelStatusRealizandoConsulta')");
        if (!isEmProcessamento()) {
            setEmProcessamento(true);
            setMensagemDetalhada("", "");
            setMapaTurmas(new ArrayList<HorarioMapaTurmaTO>());
            try {
                try {
                    permissaoFuncionalidade(getUsuarioLogado(),
                            "PermitirConsultarTurmasUsuarioNaoForResponsavel",
                            "9.53 - Permitir ao usuário consultar turmas até as que não for responsável");
                } catch (Exception ignored) {
                    consultarTurma.getProfessor().setCodigo(getUsuarioLogado().getColaboradorVO().getCodigo());
                }

                if (!exibeRelatorioFrequenciaTurmas) {
                    consultarTurma.validarDados();
                }
                // pega os filtros da tela
                List<String> diasSemana = filtrodiasSemana();
                List<String> horarios = filtroHorarios();
                // inicializa a lista resultado da consulta
                listaHorarioTurmaConcatenado = new ArrayList<HorarioTurmaConcatenadoTO>();
                List lista = new ArrayList();
                // se nao há turmas escolhidas
                if (consultarTurma.getTurma().getCodigo() == 0) {
                    // busca as turmas da modalidade
                    lista = getFacade().getTurma().consultarPorCodigoModalidade(consultarTurma.getModalidade().getCodigo(),
                            consultarTurma.getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                } else {
                    // se há uma turma escolhida pega a turma para consultar na iteração abaixo
                    lista.add(getFacade().getTurma().consultarPorChavePrimaria(consultarTurma.getTurma().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                }
                int idade = (dataNasc == null ? -1 : Calendario.getDataComHoraZerada(dataNasc).equals(Calendario.getDataComHoraZerada(Calendario.hoje())) ? -1
                        : Uteis.calcularIdadePessoa(negocio.comuns.utilitarias.Calendario.hoje(), dataNasc));
                // para cada turma é necessário montar a lista concatenada
                Iterator i = lista.iterator();
                while (i.hasNext()) {
                    TurmaVO turma = (TurmaVO) i.next();

                    if (mapa) {
                        // consulta todos os horarios de uma determinada turma
                        List<HorarioTurmaVO> horariosConsultados = getFacade().getHorarioTurma().consultarPorEmpresaModalidadeTurmaProfessorIdade(
                                consultarTurma.getEmpresa().getCodigo(), consultarTurma.getModalidade().getCodigo(),
                                turma.getCodigo(), consultarTurma.getProfessor().getCodigo(), consultarTurma.getAmbiente().getCodigo(), -1, consultarTurma.getNivel().getCodigo(),
                                diasSemana, horarios, filtroDatas.getInicio(), filtroDatas.getFim(),
                                negociacao, Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS, todasTurmas, turma.isBloquearLotacaoFutura(), null);
                        notificarRecursoEmpresa(RecursoSistema.MAPA_TURMAS_CONSULTOU);
                        montarMapaTurmas(horariosConsultados);
                    } else {
                        // consulta todos os horarios de uma determinada turma
                        List<HorarioTurmaVO> horariosConsultados = getFacade().getHorarioTurma().consultarPorEmpresaModalidadeTurmaProfessorIdade(
                                consultarTurma.getEmpresa().getCodigo(), consultarTurma.getModalidade().getCodigo(),
                                turma.getCodigo(), consultarTurma.getProfessor().getCodigo(), 0, idade, 0,
                                diasSemana, horarios, data, data, negociacao, Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS, false,turma.isBloquearLotacaoFutura(), consultarTurma.getPessoaOperacao());
                        // concatena os horariors da turma
                        for (HorarioTurmaVO ht : horariosConsultados) {
                            getFacade().getReposicao().nrAlunosReposicao(ht, Calendario.semanaAtualSQL(Calendario.hoje()));
                            if(negociacao && turma.isBloquearLotacaoFutura()  && UteisValidacao.notEmptyNumber(origem)
                                    &&(origem  == ORIGEM_CONTRATO || origem  == ORIGEM_MANUTENCAO_MODALIDADE || origem  == ORIGEM_CONTRATO_NOVO )){
                                ht.setNrAlunoMatriculado(ht.getNrAlunoMatriculado() + ht.getNrAlunoMatriculadosFuturo());
                            }
                            int aulasDesmarcadas = getFacade().getAulaDesmarcada().nrAlunosReposicao(ht, Calendario.semanaAtualSQL(Calendario.hoje()));
                            ht.setNrAlunoSairamPorReposicao(ht.getNrAlunoSairamPorReposicao()+aulasDesmarcadas);
                            concatenaHorarioTurma(turma, listaHorarioTurmaConcatenado, ht);
                            marcarTurmaSelecionadas(horariosConsultados);
                        }
                    }

                }
                for (HorarioMapaTurmaTO hmpt : mapaTurmas) {
                    hmpt.getMapa().povoarLista(ordenacaoSelecionada);
                }
                Ordenacao.ordenarLista(mapaTurmas, "horarioNumeral");
                normalizarNomes();
                notificarRecursoEmpresa(RecursoSistema.CONSULTA_DE_TURMAS_CONSULTOU);
                if (listaHorarioTurmaConcatenado.isEmpty() && !mapa) {
                    throw new Exception("A consulta não retornou resultados.");
                }

                if(exibeRelatorioFrequenciaTurmas){
                    processaDadosGraficoDeFrequenciaTurmas();
                }

                setEmProcessamento(false);
                setPollEnabled(false);
                setMsgAlert("Richfaces.hideModalPanel('panelDadosReposicao');");
            } catch (Exception e) {
                setMensagemDetalhada("msg_erro", e.getMessage());
                setEmProcessamento(false);
                setPollEnabled(false);
                setMsgAlert("Richfaces.hideModalPanel('panelDadosReposicao');");
            }
        }
    }
    public void marcarTurmaSelecionadas(List<HorarioTurmaVO> horariosConsultados){
        // verificar se a origem é da tela nova contrato(negociacaoContrato.jsp)
        // e se existe horario já selecionados
        if(origem == ORIGEM_CONTRATO_NOVO || origem == ORIGEM_MANUTENCAO_MODALIDADE && !contratoModalidade.getContratoModalidadeTurmaVOs().isEmpty()){

            List<ContratoModalidadeHorarioTurmaVO> horariosSelecionados = new ArrayList<ContratoModalidadeHorarioTurmaVO>();

            for(ContratoModalidadeTurmaVO turmaVO : (List<ContratoModalidadeTurmaVO>)contratoModalidade.getContratoModalidadeTurmaVOs()) {
               horariosSelecionados.addAll(turmaVO.getContratoModalidadeHorarioTurmaVOs());
            }

            for(HorarioTurmaVO horario : horariosConsultados){
                for(ContratoModalidadeHorarioTurmaVO horarioSelecionado : horariosSelecionados){
                    if(horarioSelecionado.getHorarioTurma().getCodigo().equals(horario.getCodigo())){
                        horario.setHorarioTurmaEscolhida(horarioSelecionado.getHorarioTurma().getHorarioTurmaEscolhida());
                    }
                }
            }
        }
    }
    public void montarMapaTurmas(List<HorarioTurmaVO> horariosConsultados) throws Exception {
        for (HorarioTurmaVO ht : horariosConsultados) {
            ItemMapaTurmasTO item = new ItemMapaTurmasTO();
            item.setCodigoProfessor(ht.getProfessor().getCodigo());
            item.setNomeProfessor(ht.getProfessor().getPessoa().getNome());
            item.setNomeTurma(ht.getIdentificadorTurma());
            item.setNrMaxAlunos(ht.getNrMaximoAluno());
            item.setDiaSemana(ht.getDiaSemana());
            item.setNivel(ht.getNivelTurma().getDescricao());
            item.setAmbiente(ht.getAmbiente().getDescricao());
            item.setHorarioInicial(ht.getHoraInicial());
            item.setHorarioFinal(ht.getHoraFinal());
            Date datapesquisa = filtroDatas.getInicio();
            item.setHorarioAtivo(ht.isAtivo());
            while(!Uteis.obterDiaSemanaData(datapesquisa).equals(ht.getDiaSemana())){
                datapesquisa = Uteis.somarDias(datapesquisa, 1);
            }
            item.setModalidade(getFacade().getTurma().consultarPorChavePrimaria(ht.getTurma(), Uteis.NIVELMONTARDADOS_DADOSBASICOS).getModalidade_Apresentar());
//            item.setAlunos(getFacade().getMatriculaAlunoHorarioTurma().consultarPorHorarioTurmaPeriodoMapaTurmas(ht.getCodigo(), filtroDatas.getInicio(), filtroDatas.getFim()));

            List<AlunoMapaTurmasTO> listaAlunosTemp = getFacade().getMatriculaAlunoHorarioTurma().consultarPorHorarioTurmaPeriodoMapaTurmas(ht.getCodigo(), datapesquisa, datapesquisa);

            //CONSULTAR REPOSICOES
            List<AlunoMapaTurmasTO> reposicoes = getFacade().getMatriculaAlunoHorarioTurma().consultarReposicoesPorHorarioTurmaPeriodoMapaTurmas(ht.getCodigo(), datapesquisa, datapesquisa);
            if (!UteisValidacao.emptyList(reposicoes)) {
                listaAlunosTemp.addAll(reposicoes);
            }

            List<AlunoMapaTurmasTO> listaAlunos = new ArrayList<AlunoMapaTurmasTO>();
            
            //Verificar se o aluno desmarcou
            Ordenacao.ordenarLista(listaAlunosTemp, "nome");
            int i = 1;
            for (AlunoMapaTurmasTO alu : listaAlunosTemp) {
                boolean desmarcou = false;
                if(considerarDesmarcoes){
                    desmarcou  = getFacade().getMatriculaAlunoHorarioTurma().alunoDesmarcouAula(alu.getCodigoCliente(), ht.getCodigo(), datapesquisa, datapesquisa);
                }
                if (!desmarcou) {
                    alu.setCodigo(i);
                    i++;
                    listaAlunos.add(alu);
                }
            }

            item.setAlunos(listaAlunos);

            int vagas = item.getNrMaxAlunos() - item.getAlunos().size();
            item.setVagas(vagas > 0 ? vagas : 0);
//    		item.completarAlunos();
            HorarioMapaTurmaTO horarioMapaTurmaTO = HorarioMapaTurmaTO.obterHorario(ht.getHoraInicial(), mapaTurmas);
            horarioMapaTurmaTO.getMapa().adicionarItem(item);
        }
    }

    public void consultarHorarios() {
        consultarHorarios(dataContrato, Boolean.FALSE);
    }

    public void consultarHorarios(Date data, Boolean negociacao) {
        setMensagemDetalhada("", "");
        try {
            // pega os filtros da tela
            List<String> diasSemana = filtrodiasSemana();
            List<String> horarios = filtroHorarios();
            // inicializa a lista resultado da consulta
            listaHorarioTurmaConcatenado = new ArrayList<HorarioTurmaConcatenadoTO>();
            int idade = Uteis.calcularIdadePessoa(negocio.comuns.utilitarias.Calendario.hoje(), dataNasc);
            List<HorarioTurmaVO> horariosConsultados = getFacade().getHorarioTurma().consultarPorEmpresaModalidadeTurmaIdade(
                    consultarTurma.getEmpresa().getCodigo(), consultarTurma.getModalidade().getCodigo(),
                    consultarTurma.getTurma().getCodigo(), idade, diasSemana, horarios,
                    data, negociacao, Uteis.NIVELMONTARDADOS_TODOS, consultarTurma.getTurma().isBloquearLotacaoFutura(), consultarTurma.getPessoaOperacao());
            for (HorarioTurmaVO ht : horariosConsultados) {
                if(negociacao && consultarTurma.getTurma().isBloquearLotacaoFutura()){
                    ht.setNrAlunoMatriculado(ht.getNrAlunoMatriculado() + ht.getNrAlunoMatriculadosFuturo());
                }
                concatenaHorarioTurma(consultarTurma.getTurma(), listaHorarioTurmaConcatenado, ht);
            }
            if (listaHorarioTurmaConcatenado.isEmpty()) {
                throw new Exception("A consulta não retornou resultados.");
            }
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    private void concatenaHorarioTurma(TurmaVO turma, List<HorarioTurmaConcatenadoTO> lista, HorarioTurmaVO ht) throws Exception {
        // percorre a lista verificando se os dados ja estao la
        for (HorarioTurmaConcatenadoTO htc : lista) {
            // se dados encontrados
            if (turma.getCodigo().equals(htc.getTurma().getCodigo()) && htc.igual(ht) && !htc.existeHorarioTurma(ht)) {
                htc.adicionaSimples(ht);
                return;
            }
        }
        // se nao encontrou adiciona um novo na lista
        HorarioTurmaConcatenadoTO htc = new HorarioTurmaConcatenadoTO();
        htc.setTurma(turma);
        htc.adiciona(ht);
        htc.setEmpresaPermiteRenovarContratosEmTurmasLotadas(isEmpresaPermiteRenovarContratosEmTurmasLotadas());
        htc.setRenovacaoContrato(isRenovacaoContrato());
        htc.setCodigo(ht.getCodigo());
        lista.add(htc);
    }

// * controle de modalidades escolhidas ****************************************
    public void preparaConsulta(ConsultarTurmaTO consulta, Date dataNasc, boolean consultaTurma, int origem, ContratoModalidadeVO contratoModalidade, Date data) {
        this.consultarTurma = consulta;
        this.dataNasc = dataNasc;
        this.consultaTurma = consultaTurma;
        this.origem = origem;
        this.contratoModalidade = contratoModalidade;
        this.dataContrato = data;
        if(origem == ORIGEM_CONTRATO_NOVO || origem == ORIGEM_MANUTENCAO_MODALIDADE){
            carregarVezesSemana();
        }
        if (consultarTurma.getTurma().getCodigo() == 0) {
            consultarTurmas(dataContrato, Boolean.TRUE, Boolean.FALSE);
        } else {
            consultarHorarios(dataContrato, Boolean.TRUE);
        }
    }

    public void selecionarVezesSemanaPorTurma() {
        setMensagemDetalhada("", "");
        try {
            validarHorarios();
        } catch (Exception e) {
            montarErro(e);
            setMsgAlert(getMensagemNotificar());
        }

    }
    public void validarHorarios() throws Exception{
        contratoModalidade.setAdvertenciaMatriculasFuturas(false);
        // percorre a lista de horarios consultados
        for (HorarioTurmaConcatenadoTO htc : listaHorarioTurmaConcatenado) {
            // se horario marcado
            if (htc.getHorarioTurmaDom().getHorarioTurmaEscolhida()) {
                adicionaNaModalidade(htc.getTurma(), htc.getHorarioTurmaDom());
            }
            if (htc.getHorarioTurmaSeg().getHorarioTurmaEscolhida()) {
                adicionaNaModalidade(htc.getTurma(), htc.getHorarioTurmaSeg());
            }
            if (htc.getHorarioTurmaTer().getHorarioTurmaEscolhida()) {
                adicionaNaModalidade(htc.getTurma(), htc.getHorarioTurmaTer());
            }
            if (htc.getHorarioTurmaQua().getHorarioTurmaEscolhida()) {
                adicionaNaModalidade(htc.getTurma(), htc.getHorarioTurmaQua());
            }
            if (htc.getHorarioTurmaQui().getHorarioTurmaEscolhida()) {
                adicionaNaModalidade(htc.getTurma(), htc.getHorarioTurmaQui());
            }
            if (htc.getHorarioTurmaSex().getHorarioTurmaEscolhida()) {
                adicionaNaModalidade(htc.getTurma(), htc.getHorarioTurmaSex());
            }
            if (htc.getHorarioTurmaSab().getHorarioTurmaEscolhida()) {
                adicionaNaModalidade(htc.getTurma(), htc.getHorarioTurmaSab());
            }
        }
        retornaSelecao();
        setMsgAlert("Richfaces.hideModalPanel('panelConsultarTurma');Notifier.cleanAllOnType(Notifier.MENS_ERRO);");
    }
    private void adicionaNaModalidade(TurmaVO turma, HorarioTurmaVO horario) {
        boolean incluirTurma = true;
        boolean incluirHorario = true;
        boolean horarioSelecionado = false;
        try {
            if(turma.getBloquearMatriculasAcimaLimite()
                    && !turma.isBloquearLotacaoFutura()){ // validação de marcações nesse caso, já coloca as matriculas futuras no número de matriculados
                getFacade().getHorarioTurma().nrMatriculasFuturas(horario, dataContrato);
                if(!UteisValidacao.emptyString(horario.getMsgMatriculasFuturas())){
                    contratoModalidade.setAdvertenciaMatriculasFuturas(true);
                }
            } else {
                horario.setMsgMatriculasFuturas("");
            }
        } catch (Exception ignored){
        }
        Iterator i = contratoModalidade.getContratoModalidadeTurmaVOs().iterator();
        // percorre a lista de turmas da modalidade
        while (i.hasNext()) {
            ContratoModalidadeTurmaVO cm = (ContratoModalidadeTurmaVO) i.next();
            // verifica se turma ja existe
            if (cm.getTurma().getCodigo().intValue() == turma.getCodigo()) {
                Iterator j = cm.getContratoModalidadeHorarioTurmaVOs().iterator();
                // percorre a lista de horarios da turma
                while (j.hasNext()) {
                    ContratoModalidadeHorarioTurmaVO ht = (ContratoModalidadeHorarioTurmaVO) j.next();
                    // verifica se horario ja existe
                    if (ht.getHorarioTurma().getCodigo().intValue() == horario.getCodigo()) {
                        // se ja existir nao precisa incluir
                        incluirHorario = false;
                        break;
                    }
                }
                if (incluirHorario) {
                    // cria um novo contrato modalidade horario turma e prepara os dados
                    ContratoModalidadeHorarioTurmaVO cmht = new ContratoModalidadeHorarioTurmaVO();
                    cmht.setHorarioTurma(horario);
                    cmht.setContratoModalidadeTurma(cm.getCodigo());
                    cm.getContratoModalidadeHorarioTurmaVOs().add(cmht);
                }
                incluirTurma = false;
                break;
            }
        }
        if (incluirTurma) {
            turma.setTurmaEscolhida(true);
            // cria um novo contrato modalidade horario turma e prepara os dados
            ContratoModalidadeHorarioTurmaVO cmht = new ContratoModalidadeHorarioTurmaVO();
            cmht.setHorarioTurma(horario);
            // cria um novo contrato modalidade turma e prepara os dados
            ContratoModalidadeTurmaVO cmt = new ContratoModalidadeTurmaVO();
            cmt.setTurma(turma);
            cmt.setContratoModalidade(getContratoModalidade().getCodigo());
            cmt.getContratoModalidadeHorarioTurmaVOs().add(cmht);
            // adiciona à modalidade
            contratoModalidade.getContratoModalidadeTurmaVOs().add(cmt);
        }
    }
    private void retornaSelecao() throws Exception {
        switch (origem) {
            // se negociacao de contrato
            case ORIGEM_CONTRATO:
                // transfere os dados pro controlador de origem
                ContratoControle contrato = (ContratoControle) context().getExternalContext().getSessionMap().get("ContratoControle");
                if (contrato == null) {
                    throw new Exception("Erro ao retornar dados selecionados. Contate Suporte Técnico.");
                }
                contrato.setContratoModalidade(contratoModalidade);
                context().getExternalContext().getSessionMap().put("ContratoControle", contrato);
                break;
            // se manutencao modalidade
            case ORIGEM_MANUTENCAO_MODALIDADE:
                // transfere os dados pro controlador de origem
                ManutencaoModalidadeControle manutencao = (ManutencaoModalidadeControle) context().getExternalContext().getSessionMap().get("ManutencaoModalidadeControle");
                if (manutencao == null) {
                    throw new Exception("Erro ao retornar dados selecionados. Contate Suporte Técnico.");
                }
                manutencao.validarOcupacao(contratoModalidade);
                manutencao.setContratoModalidade(contratoModalidade);
                context().getExternalContext().getSessionMap().put("ManutencaoModalidadeControle", manutencao);
                break;
            // se negociacao de contrato em tela nova(negociacaoContrato.jsp)
            case ORIGEM_CONTRATO_NOVO:
                // transfere os dados pro controlador de origem
                ContratoControle contratoDinamico = (ContratoControle) context().getExternalContext().getSessionMap().get("ContratoControle");
                if (contratoDinamico == null) {
                    throw new Exception("Erro ao retornar dados selecionados. Contate Suporte Técnico.");
                }
                contratoModalidade.getModalidade().setEditarModalidade(false);
                contratoDinamico.setContratoModalidade(contratoModalidade);
                contratoDinamico.setEditandoModalidade(true);
                contratoDinamico.salvarHorarios();
                context().getExternalContext().getSessionMap().put("ContratoControle", contratoDinamico);
                break;
        }
    }
    public void mostrarDadosTurma(ActionEvent evt) {
        mostrarDadosTurma(
                (HorarioTurmaConcatenadoTO) evt.getComponent().getAttributes().get("htc"),
                (HorarioTurmaVO) evt.getComponent().getAttributes().get("ht"),
                (String) evt.getComponent().getAttributes().get("diaSemana"),
                (Date) evt.getComponent().getAttributes().get("dataBase")
        );
    }

    public void mostrarDadosTurma(HorarioTurmaConcatenadoTO htc, HorarioTurmaVO ht, String diaSemana, Date dataBase) {
        mostrarDadosTurma(null, htc, ht, diaSemana, dataBase);
    }

    public void mostrarDadosTurma(Integer codHorarioTurma, HorarioTurmaConcatenadoTO htc, HorarioTurmaVO ht, String diaSemana, Date dataBase) {
        setMensagemDetalhada("", "");
        boolean consultarReposicoes = getConsultarTurma().isExibirReposicoes();
        try {
            if (codHorarioTurma != null) {
                ht = getFacade().getHorarioTurma().consultarPorChavePrimaria(codHorarioTurma, 0);
            }
            if (ht != null && htc == null) {
                htc = new HorarioTurmaConcatenadoTO();
                htc.adiciona(ht);
            }
            if (htc == null) {
                throw new Exception("Não foi possível encontrar o Horário correto. Contate suporte técnico.");
            } else {
                // pega o horario de acordo com o dia da semana
                if (diaSemana.equals("DM")) {
                    horarioTurma = htc.getHorarioTurmaDom();
                } else if (diaSemana.equals("SG")) {
                    horarioTurma = htc.getHorarioTurmaSeg();
                } else if (diaSemana.equals("TR")) {
                    horarioTurma = htc.getHorarioTurmaTer();
                } else if (diaSemana.equals("QA")) {
                    horarioTurma = htc.getHorarioTurmaQua();
                } else if (diaSemana.equals("QI")) {
                    horarioTurma = htc.getHorarioTurmaQui();
                } else if (diaSemana.equals("SX")) {
                    horarioTurma = htc.getHorarioTurmaSex();
                } else {
                    horarioTurma = htc.getHorarioTurmaSab();
                }
                if (consultarTurma.isModoReposicao() && ht == null) {
                    if(Calendario.getDiaDaSemanaAbreviado(Calendario.hoje()).equals(horarioTurma.getDiaSemana())){
                        consultarTurma.getReposicao().setDataReposicao(Calendario.hoje());
                    } else {
                        consultarTurma.getReposicao().setDataReposicao(Calendario.proximoDiaSemana(horarioTurma.getDiaSemanaNumero(),
                                Calendario.hoje()));
                        listaAlunosTurma = getFacade().getMatriculaAlunoHorarioTurma().
                            consultarPorHorarioTurmaPeriodo(horarioTurma.getCodigo(),
                            Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS,
                            consultarTurma.getReposicao().getDataReposicao(), consultarTurma.getReposicao().getDataReposicao(), false, consultarReposicoes);
                    }
                } else if (dataBase != null) {
                    listaAlunosTurma = getFacade().getMatriculaAlunoHorarioTurma().
                            consultarPorHorarioTurmaPeriodo(horarioTurma.getCodigo(),
                            Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS,
                            dataBase, Calendario.proximoDiaSemana(dataBase), false, consultarReposicoes);
                } else {
                    listaAlunosTurma = getFacade().getMatriculaAlunoHorarioTurma().
                            consultarPorHorarioTurmaPeriodo(horarioTurma.getCodigo(),
                            Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS,
                            Calendario.hoje(), Calendario.proximoDiaSemana(Calendario.hoje()), false, consultarReposicoes);

                }
                horarioTurmaConcatenado = htc;
                totalizadorAlunos = listaAlunosTurma.size();
                // coloca o numero de alunos correto na turma
                horarioTurma.setNrAlunoMatriculado(totalizadorAlunos);
                horarioTurma.setNrAlunoReposicao(0);
                // atualiza a mensagem da tela
//                if (totalizadorAlunos == 0) {
//                    setMensagemDetalhada("msg_dados_consultados", "Não existe aluno matriculado nessa turma");
//                } else {
                List<ConsultarAlunosTurmaVO> lista = listaAlunosTurma;
                for (ConsultarAlunosTurmaVO consultarAlunosTurmaVO : lista) {
                    if (consultarAlunosTurmaVO.getMatriculaAlunoHorarioTurmaVO().getCodigo() == 0) {
                        horarioTurma.setNrAlunoReposicao(horarioTurma.getNrAlunoReposicao() + 1);
                    }

                }
                if (consultarTurma.isModoReposicao() && ht == null) {
                    consultarTurma.getReposicao().setHorarioTurma(horarioTurma.clone());
                    consultarTurma.getReposicao().setTurmaDestino(getFacade().
                            getTurma().consultarPorChavePrimaria(horarioTurma.getTurma(),
                            Uteis.NIVELMONTARDADOS_DADOSENTIDADESUBORDINADAS));
                    
                    
                }
//                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

// * lista de chamada **********************************************************
    private void inicializarColunas(boolean filtroEditado, Date dataInicio, Date dataFim) throws Exception {
        if (!filtroEditado){
            colunas = new ArrayList<PresencaVO>();
            Date dataObtida = Calendario.getDataComHoraZerada(Uteis.obterDataAnterior(mesReferencia, 30));
            // prepara uma lista com todos dias do mes
            for (int cont = 0; cont < 31; cont++) {
                PresencaVO presenca = new PresencaVO();
                presenca.setDiaSemana(Uteis.obterDiaSemanaData(dataObtida));
                presenca.setDataPresenca(dataObtida);
                presenca.setHoje(dataObtida.equals(mesReferencia));
                getColunas().add(presenca);
                dataObtida = Calendario.getDataComHoraZerada(Uteis.obterDataFutura2(dataObtida, 1));
            }
        } else {
            colunas = new ArrayList<>();
            long dias = Uteis.nrDiasEntreDatas(dataInicio, dataFim)+1;
            if (dias > 31) {
                setErro(true);
                setSucesso(false);
                setMensagemDetalhada("Período filtrado maior que 1 mês, Informe um período menor para realizar a consulta!");
                dataFim = mesReferencia;
                dataInicio = Calendario.getDataComHoraZerada(Uteis.obterDataAnterior(mesReferencia, 30));
                dias = 31;
            }
            for (int cont = 0; cont < dias; cont++) {
                PresencaVO presenca = new PresencaVO();
                presenca.setDiaSemana(Uteis.obterDiaSemanaData(dataInicio));
                presenca.setDataPresenca(dataInicio);
                presenca.setHoje(dataInicio.equals(dataFim));
                getColunas().add(presenca);
                dataInicio = Calendario.getDataComHoraZerada(Uteis.obterDataFutura2(dataInicio, 1));

            }
        }
    }

    public void selecionarMesReferenciaTela() {
        boolean atualizar = true;
        selecionarMesReferencia(atualizar);
    }

    public void selecionarMesReferencia(boolean atualizar) {
        setMensagemDetalhada("", "");
        try {
            /*if (mesReferencia.after(Calendario.hoje())) {
             throw new Exception("Data para chamada não pode ser no futuro.");
             }*/
            if (atualizar){
                inicializarColunas(true, dataInicio, dataFim);
                validarPrepararListaAlunos(horarioTurmaListaChamada, getDataInicio(),getDataFim(),true);

            }else {
                inicializarColunas(false,Calendario.hoje(),Calendario.hoje());
                validarPrepararListaAlunos(horarioTurmaListaChamada,Calendario.hoje(),Calendario.hoje(),false);
            }
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public String prepararListaChamada() {
        setMensagemDetalhada("", "");
        try {
            // pega o elemento escolhido na tela
            HorarioTurmaConcatenadoTO htc = (HorarioTurmaConcatenadoTO) context().getExternalContext().getRequestMap().get("horarioTurmaConcatenado");
            if (htc == null) {
                throw new Exception("Não foi possível encontrar o Horário correto. Contate suporte técnico.");
            } else {
                mesReferencia = Calendario.hoje();
                horarioTurmaListaChamada = (HorarioTurmaConcatenadoTO) htc.getClone(true);
                selecionarMesReferencia(false);
                setMensagem("Verifique se os dados da turma estão corretos antes de fazer a chamada.");
            }
            return "chamada";
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            return "";
        }
    }

    private void validarPrepararListaAlunos(HorarioTurmaConcatenadoTO htc, Date dataInicio, Date dataFim,boolean filtroeditado) throws Exception {
        // busca os alunos do horario para todos os dias da semana
        StringBuilder horarios = new StringBuilder();
        boolean virgula = false;
        horarios.append("(");
        if (htc.getHorarioTurmaDom().getCodigo() > 0) {
            horarios.append(htc.getHorarioTurmaDom().getCodigo());
            virgula = true;
        }
        if (htc.getHorarioTurmaSeg().getCodigo() > 0) {
            if (virgula) {
                horarios.append(",");
            }
            horarios.append(htc.getHorarioTurmaSeg().getCodigo());
            virgula = true;
        }
        if (htc.getHorarioTurmaTer().getCodigo() > 0) {
            if (virgula) {
                horarios.append(",");
            }
            horarios.append(htc.getHorarioTurmaTer().getCodigo());
            virgula = true;
        }
        if (htc.getHorarioTurmaQua().getCodigo() > 0) {
            if (virgula) {
                horarios.append(",");
            }
            horarios.append(htc.getHorarioTurmaQua().getCodigo());
            virgula = true;
        }
        if (htc.getHorarioTurmaQui().getCodigo() > 0) {
            if (virgula) {
                horarios.append(",");
            }
            horarios.append(htc.getHorarioTurmaQui().getCodigo());
        }
        if (htc.getHorarioTurmaSex().getCodigo() > 0) {
            if (virgula) {
                horarios.append(",");
            }
            horarios.append(htc.getHorarioTurmaSex().getCodigo());
            virgula = true;
        }
        if (htc.getHorarioTurmaSab().getCodigo() > 0) {
            if (virgula) {
                horarios.append(",");
            }
            horarios.append(htc.getHorarioTurmaSab().getCodigo());
        }
        horarios.append(") ");
        if (dataInicio.equals(dataFim)) {
            if (!filtroeditado) {
                setDataFim(Calendario.hoje());
                setDataInicio(Uteis.obterDataAnterior(mesReferencia, 30));
            }
        }
        listaMatriculas = getFacade().getMatriculaAlunoHorarioTurma().consultarPorHorariosTurma(horarios.toString(), Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS, dataInicio, dataFim);
        listaChamada = prepararListaAlunos(listaMatriculas);
    }

    public void realizarConsultaLogObjetoSelecionadoPresenca() {
        LoginControle loginControle = (LoginControle) context().getExternalContext().getSessionMap().get("LoginControle");
        String nomeClasse = NOME_ENTIDADE;
        loginControle.setNomeClasse(loginControle.getNomeEntidadeCamposLog("prt_" + nomeClasse + "_tituloForm"));
        loginControle.consultarLogObjetoSelecionado(nomeClasse,getHorarioTurmaListaChamada().getCodigo(), 0);
    }

    private List<ChamadaTO> prepararListaAlunos(List<MatriculaAlunoHorarioTurmaVO> lista) throws Exception {
        // lista preparada ao final do processo
        List<ChamadaTO> listaPreparada = new ArrayList<>();
        // percorre todos os elementos da lista
        for (MatriculaAlunoHorarioTurmaVO matricula : lista) {
            ChamadaTO aluno = new ChamadaTO();
            // verifica se o aluno ja esta na lista
            for (ChamadaTO alunoPreparado : listaPreparada) {
                // se encontrou o aluno
                if (alunoPreparado.getAluno().getCodigo().intValue() == matricula.getPessoa().getCodigo()) {
                    aluno = alunoPreparado;
                }
            }
            // se o aluno nao esta na lista
            if (aluno.getAluno().getCodigo() == 0) {
                aluno.setAluno(matricula.getPessoa());
                HorarioTurmaVO hor = matricula.getHorarioTurma();
                Date dataObtida = dataInicio;
                long dias = Uteis.nrDiasEntreDatas(dataInicio, dataFim) +1;
                if (dias > 31) {
                    setMensagemDetalhada("Período filtrado maior que 1 mês, Informe um período menor para realizar a consulta!");
                    dataFim = mesReferencia;
                    dataInicio = Calendario.getDataComHoraZerada(Uteis.obterDataAnterior(mesReferencia, 30));
                    dias = 31;
                }

                // prepara uma lista com todos dias do mes
                for (int cont = 0; cont <= dias; cont++) {
                    //verifica se a pessoa tem uma reposicao para acontecer
                    PresencaVO presencaRepo = getFacade().getReposicao().preencherPresencaEmFuncaoReposicao(
                            aluno.getAluno().getCodigo(), hor.getCodigo(),
                            dataObtida, dataObtida);

                    // verifica se a pessoa tem presenca nessa turma para esse dia no BD
                    PresencaVO pr = getFacade().getPresenca().consultarPorPessoaHorarioPeriodoPresenca(aluno.getAluno().getCodigo(), hor.getCodigo(),
                            dataObtida, dataObtida, Uteis.NIVELMONTARDADOS_DADOSBASICOS);

                    // verifica se existe carencia para esse dia
                    boolean existeCarencia = getFacade().getContratoOperacao().existeCarencia(dataObtida,
                            matricula.getContrato().getCodigo());
                    pr.setCarencia(existeCarencia);

                    // se nao encontrou uma presenca no BD
                    if (pr.isNovoObj()) {
                        pr.setDiaSemana(Uteis.obterDiaSemanaData(dataObtida));
                        pr.setDataPresenca(dataObtida);
                        // verifica se a pessoa tem aula nesse dia
                        if(Calendario.menorOuIgual(matricula.getDataInicio(), dataObtida) && Calendario.maiorOuIgual(matricula.getDataFim(), dataObtida)){
                            if (hor.getDiaSemana().equals(pr.getDiaSemana())) {
                                pr.setAula(matricula.getCodigo() != 0 || presencaRepo.getRepo() != null);
                                pr.setRepo(presencaRepo.getRepo());
                                pr.setDadosTurma(matricula.getCodigo());
                                pr.setPresente(presencaRepo.getRepo() != null && presencaRepo.getRepo().getDataPresenca() != null);
                            }
                        }
                        // se encontrou no BD
                    } else {
                        // muda a data da chamada para hoje
                        pr.setDataChamada(Calendario.hoje());
                    }
                    aluno.getListaPresencas().add(pr);
                    dataObtida = Uteis.obterDataFutura2(dataObtida, 1);
                }
                listaPreparada.add(aluno);
            } else {
                HorarioTurmaVO hor = matricula.getHorarioTurma();
                // marca as aulas que a pessoa tera no mes para esse horario turma
                for (PresencaVO pr : aluno.getListaPresencas()) {
                    if(Calendario.menorOuIgual(matricula.getDataInicio(), pr.getDataPresenca()) && Calendario.maiorOuIgual(matricula.getDataFim(), pr.getDataPresenca())){
                        if (hor.getDiaSemana().equals(pr.getDiaSemana())) {
                            PresencaVO aux = null;
                            //verifica se a pessoa tem uma presença em Reposição para este dia nessa turma para acontecer
                            PresencaVO presencaRepo = getFacade().getReposicao().preencherPresencaEmFuncaoReposicao(
                                    aluno.getAluno().getCodigo(), hor.getCodigo(),
                                    pr.getDataPresenca(), pr.getDataPresenca());
                            pr.setRepo(presencaRepo.getRepo());
                            if (presencaRepo.getRepo() != null && presencaRepo.getRepo().getDataPresenca() != null) {
                                presencaRepo.setNovoObj(false);
                                aux = presencaRepo;
                            } else {
                                // verifica se a pessoa tem presenca nessa turma para esse dia no BD
                                aux = getFacade().getPresenca().consultarPorPessoaHorarioPeriodoPresenca(aluno.getAluno().getCodigo(), hor.getCodigo(),
                                        pr.getDataPresenca(), pr.getDataPresenca(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                            }
                            // se nao encontrou uma presenca no BD
                            if (aux.isNovoObj()) {
                                pr.setDadosTurma(matricula.getCodigo());
                                // se encontrou
                            } else {
                                pr.setCodigo(aux.getCodigo());
                                pr.setDadosTurma(aux.getDadosTurma());
                                pr.setDataPresenca(aux.getDataPresenca());
                                pr.setPresente(true);
                                pr.setNovoObj(false);
                            }
                            pr.setAula(true);
                        }
                    }
                }
            }
        }
        return listaPreparada;
    }

    public void gravarPresenca() {
        limparMsg();
        try {
            if(Calendario.maior(presenca.getDataPresenca(), Calendario.hoje())){
                presenca.setPresente(false);
                throw new Exception("Não é permitido marcar presença para datas futuras");
            }
            gerarLogPresenca();
            // se esta presente inclui
            TurmasServiceImpl turmaService = new TurmasServiceImpl(Conexao.getFromSession());
            ClienteVO cliente = getFacade().getCliente().consultarPorCodigoPessoa(pessoa.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            int diaSemana = Calendario.getDiaSemana(presenca.getDataPresenca());
            int idHorarioTurma =  horarioTurmaListaChamada.obterHorarioTurmaDiaSemana(diaSemana).getCodigo();
            if (presenca.isPresente()) {
                getFacade().getPresenca().incluir(presenca);
                if (presenca.getRepo() != null) {
                    getFacade().getReposicao().atualizarPresenca(presenca.getRepo().getCodigo(), presenca.getDataPresenca());
                }
                turmaService.processarAcesso((String) JSFUtilities.getFromSession(JSFUtilities.KEY), cliente.getCodigo(), presenca.getDataPresenca(), false, idHorarioTurma);
                // se nao esta presente exclui
            } else {
                getFacade().getPresenca().excluir(presenca);
                if (presenca.getRepo() != null) {
                    presenca.getRepo().setDataPresenca(null);
                    getFacade().getReposicao().atualizarPresenca(presenca.getRepo().getCodigo(), null);
                }
                turmaService.processarAcesso((String) JSFUtilities.getFromSession(JSFUtilities.KEY), cliente.getCodigo(), presenca.getDataPresenca(), true, idHorarioTurma);
            }
            turmaService = null;
            setMensagemDetalhada("msg_dados_gravados", "");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void montarRealizarContato() throws Exception {
        try {
            ConsultarAlunosTurmaVO consultarAlunosTurmaVO = (ConsultarAlunosTurmaVO) context().getExternalContext().getRequestMap().get("aluno");
            consultarAlunosTurmaVO.getClienteVO().getPessoa().setTelefoneVOs(getFacade().getTelefone().consultarTelefones(consultarAlunosTurmaVO.getClienteVO().getPessoa().getCodigo().intValue(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            HistoricoContatoControle historicoContatoControle = (HistoricoContatoControle) JSFUtilities.getFromSession(HistoricoContatoControle.class.getSimpleName());
            Date data = negocio.comuns.utilitarias.Calendario.hoje();

            historicoContatoControle.inicializarCliente(data, consultarAlunosTurmaVO.getClienteVO());
            historicoContatoControle.selecionarObjetivoFase();

        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }
// * imprimir PDF modelo antigo ************************************************
    public void imprimirPDF() {
        try {
            setTipoRelatorio("PDF");
            setRelatorio("sim");
            // prepara os dados da lista de chamada
            consultarTurma.validarDados();
            if (consultarTurma.getTurma().getCodigo() != 0) {
                matriculaAlunoHorarioTurmaRel.setTurma(getFacade().getTurma().consultarPorChavePrimaria(consultarTurma.getTurma().getCodigo(), Uteis.NIVELMONTARDADOS_TELACONSULTA));
            }
            if (consultarTurma.getProfessor().getCodigo() != 0) {
                matriculaAlunoHorarioTurmaRel.setProfessor(getFacade().getColaborador().consultarPorChavePrimaria(consultarTurma.getProfessor().getCodigo(), Uteis.NIVELMONTARDADOS_TELACONSULTA));
            }
            matriculaAlunoHorarioTurmaRel.setEmpresa(getFacade().getEmpresa().consultarPorChavePrimaria(consultarTurma.getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS));
            matriculaAlunoHorarioTurmaRel.setDataInicio(getDataInicio());
            matriculaAlunoHorarioTurmaRel.setDataTermino(getDataFim());
            listaRegistro = matriculaAlunoHorarioTurmaRel.consultarAlunoHorarioTurma(listaMatriculas, true);
            setMensagemDetalhada("", "Dados preparados com SUCESSO! Aguarde a Impressão do Relatório.");
        } catch (Exception e) {
            setRelatorio("nao");
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void imprimirRelatorio() throws Exception {
        try {
            String nomeRelatorio = matriculaAlunoHorarioTurmaRel.getIdEntidade();
            EmpresaVO empre = matriculaAlunoHorarioTurmaRel.getEmpresa();
            String titulo = "Lista de Chamada";
            String barra = (empre.getCidade().getNome().trim().isEmpty() ? "" : "/");
            String design = matriculaAlunoHorarioTurmaRel.getDesignIReportRelatorio();
            apresentarRelatorioObjetos(nomeRelatorio, titulo, empre.getNome(), "", "", getTipoRelatorio(),
                    "/" + matriculaAlunoHorarioTurmaRel.getIdEntidade() + "/registros", design,
                    usuario.getNome(), matriculaAlunoHorarioTurmaRel.getDescricaoFiltros(),
                    Uteis.getData(matriculaAlunoHorarioTurmaRel.getDataInicio()),
                    Uteis.getData(matriculaAlunoHorarioTurmaRel.getDataTermino()),
                    empre.getEndereco() + " " + empre.getNumero() + " " + empre.getSetor(),
                    empre.getCidade().getNome() + barra + empre.getCidade().getEstado().getSigla(),
                    MatriculaAlunoHorarioTurmaRel.getCaminhoSubRelatorio(), "", listaRegistro);
            setMensagemDetalhada("", "");
            setMensagemID("msg_entre_prmrelatorio");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    private Map<String, Object> prepareParams() throws Exception {
        Integer emp = getUsuarioLogado().getAdministrador() ? 0 : getEmpresaLogado().getCodigo();
        EmpresaVO empre = new EmpresaVO();
        if (emp != 0) {
            empre = getFacade().getEmpresa().consultarPorChavePrimaria(emp, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        }

        Map<String, Object> params = new HashMap<String, Object>();
        params.put("nomeRelatorio", "MapaTurmas");
        params.put("nomeEmpresa", empre.getNome());

        params.put("tituloRelatorio", "Relatório Comissão para Professor");
        params.put("nomeDesignIReport", getDesignIReportRelatorio());
        params.put("nomeUsuario", getUsuarioLogado().getNomeAbreviado());
        List<HorarioMapaTurmaTO> lista = new ArrayList<HorarioMapaTurmaTO>();
        lista.addAll(mapaTurmas);

        params.put("listaObjetos", lista);

        params.put("filtros", getFiltrosDescricao());
        params.put("dataIni", Uteis.getData(filtroDatas.getInicio()));
        params.put("dataFim", Uteis.getData(filtroDatas.getFim()));
        params.put("enderecoEmpresa", empre.getEndereco());
        params.put("cidadeEmpresa", empre.getCidade().getNome());
        params.put("formatoRel", getTipoRelatorio());

        params.put("SUBREPORT_DIR", getCaminhoSubRelatorio());
        return params;
    }



    public void gerarLogPresenca()
    {
        try {
            StringBuilder valorCampo = new StringBuilder();
            LogVO log = new LogVO();
            int diaSemana = Calendario.getDiaSemana(presenca.getDataPresenca());
            log.setChavePrimaria(getHorarioTurmaListaChamada().obterHorarioTurmaDiaSemana(diaSemana).getCodigo().toString());
        log.setNomeEntidade(NOME_ENTIDADE);
        log.setNomeEntidadeDescricao("Presença - Chamada de Turma");
        log.setResponsavelAlteracao(getUsuarioLogado().getNome());
        log.setUserOAMD(getUsuarioLogado().getUserOamd());
        log.setOperacao("EXCLUSÃO");
        log.setPessoa(0);
        log.setValorCampoAlterado("Sem Presença");
        log.setValorCampoAnterior("Com Presença");
        log.setDataAlteracao(Calendario.hoje());
        valorCampo.append("*Presença ").append(pessoa.getNome());
        valorCampo.append(" dia ").append(presenca.getDiaChamada());
        valorCampo.append(" Dia Semana ").append(presenca.getDiaSemana_Apresentar());
        log.setNomeCampo(valorCampo.toString());

            if(presenca.isPresente()) {
                log.setOperacao("INCLUSÃO");
                log.setValorCampoAlterado("Com Presença");
                log.setValorCampoAnterior("Sem Presença");
            }
        getFacade().getLog().incluirSemCommit(log);

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public boolean isMarcarTodos() {
        return marcarTodos;
    }

    public void setMarcarTodos(boolean marcarTodos) {
        this.marcarTodos = marcarTodos;
    }
    public String getRotuloLimparFiltros(){
        if(marcarTodos){
            return "Limpar";
        }else{
            return "Marcar todos";
        }
    }

    public void limparFiltros(){
        Boolean opcao = marcarTodos == false;
        consultarTurma.setDomingo(opcao);
        consultarTurma.setSegunda(opcao);
        consultarTurma.setTerca(opcao);
        consultarTurma.setQuarta(opcao);
        consultarTurma.setQuinta(opcao);
        consultarTurma.setSexta(opcao);
        consultarTurma.setSabado(opcao);
        consultarTurma.setH0001as0200(opcao);
        consultarTurma.setH0201as0400(opcao);
        consultarTurma.setH0401as0600(opcao);
        consultarTurma.setH0601as0800(opcao);
        consultarTurma.setH0801as1000(opcao);
        consultarTurma.setH1001as1200(opcao);
        consultarTurma.setH1201as1400(opcao);
        consultarTurma.setH1401as1600(opcao);
        consultarTurma.setH1601as1800(opcao);
        consultarTurma.setH1801as2000(opcao);
        consultarTurma.setH2001as2200(opcao);
        consultarTurma.setH2201as0000(opcao);
       marcarTodos = (opcao);

    }
    public String getFiltrosDescricao() throws Exception {
        String filtros = "Empresa:\t";
        if (!UteisValidacao.emptyNumber(consultarTurma.getEmpresa().getCodigo())) {
            EmpresaVO empresa = getFacade().getEmpresa().consultarPorChavePrimaria(consultarTurma.getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            filtros += empresa.getNome();
        }

        filtros += " - Modalidade:\t";
        if (!UteisValidacao.emptyNumber(consultarTurma.getModalidade().getCodigo())) {
            ModalidadeVO modalidade = getFacade().getModalidade().consultarPorChavePrimaria(consultarTurma.getModalidade().getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            filtros += modalidade.getNome();
        }
        if (!UteisValidacao.emptyNumber(consultarTurma.getTurma().getCodigo())) {
            try {
                TurmaVO turma = getFacade().getTurma().consultarPorChavePrimaria(consultarTurma.getTurma().getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                filtros += " - Turma:\t" + turma.getDescricao();
            } catch (Exception e) {
            }

        }
        if (!UteisValidacao.emptyNumber(consultarTurma.getProfessor().getCodigo())) {
            try {
                NivelTurmaVO nivel = getFacade().getNivelTurma().consultarPorChavePrimaria(consultarTurma.getNivel().getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                filtros += " - Nível turma:\t" + nivel.getDescricao();
            } catch (Exception e) {
                // TODO: handle exception
            }

        }
        if (!UteisValidacao.emptyNumber(consultarTurma.getNivel().getCodigo())) {
            try {
                ColaboradorVO prof = getFacade().getColaborador().consultarPorChavePrimaria(consultarTurma.getProfessor().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSENTIDADESUBORDINADAS);
                filtros += " - Professor:\t" + prof.getPessoa().getNome();
            } catch (Exception e) {
                // TODO: handle exception
            }

        }
        if (!UteisValidacao.emptyNumber(consultarTurma.getAmbiente().getCodigo())) {
            try {
                AmbienteVO ambiente = getFacade().getAmbiente().consultarPorChavePrimaria(consultarTurma.getAmbiente().getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                filtros += " - Ambiente:\t" + ambiente.getDescricao();
            } catch (Exception e) {
                // TODO: handle exception
            }

        }

        if (consultarTurma.isDomingo()) {
            filtros += " - DOMINGO\t";
        }
        if (consultarTurma.isSegunda()) {
            filtros += " - SEGUNDA\t";
        }
        if (consultarTurma.isTerca()) {
            filtros += " - TERÇA\t";
        }
        if (consultarTurma.isQuarta()) {
            filtros += " - QUARTA\t";
        }
        if (consultarTurma.isQuinta()) {
            filtros += " - QUINTA\t";
        }
        if (consultarTurma.isSexta()) {
            filtros += " - SEXTA\t";
        }
        if (consultarTurma.isSabado()) {
            filtros += " - SÁBADO\t";
        }

        filtros += " - Período:\t" + Uteis.getData(filtroDatas.getInicio()) + " até " + Uteis.getData(filtroDatas.getFim());

        return filtros;
    }

    public String getNomeRefPastaRelatorioRelatorioGeradoAgora() {
        String hRef = getNomeArquivoRelatorioGeradoAgora();
        if (!hRef.isEmpty()) {
            return "location.href=\"../../relatorio/" + hRef + "\"";
        } else {
            return "";
        }
    }

    public String getDesignIReportRelatorio() {
        return ("relatorio" + File.separator + "designRelatorio" + File.separator + "outros" + File.separator + "MapaTurmas.jrxml");
    }

    public static String getCaminhoSubRelatorio() {
        return ("relatorio" + File.separator + "designRelatorio" + File.separator + "outros" + File.separator);
    }

    public void imprimirMapaPDF() throws Exception {
        imprimirMapa("PDF");

    }

    public void imprimirMapaExcel() throws Exception {
        imprimirMapa("EXCEL");
    }

    private void imprimirMapa(String tipoRelatorio) throws Exception {
        try {
            setTipoRelatorio(tipoRelatorio);
            setMsgAlert("");
            validarPermissaoEIncluirLogExportacao(ItemExportacaoEnum.MAPA_TURMA, mapaTurmas.size(), getFiltrosDescricao(),tipoRelatorio , "", "" );
            if (mapaTurmas.isEmpty()) {
                throw new ConsistirException("Nenhum registro a ser impresso, faça a consulta novamente.");
            }
            apresentarRelatorioObjetos(prepareParams());
            if ("PDF".equals(tipoRelatorio)) {
                setMsgAlert("abrirPopupPDFImpressao('relatorio/" + getNomeArquivoRelatorioGeradoAgora() + "','', 780, 595);");
            } else {
                setMsgAlert("location.href='DownloadSV?mimeType=application/vnd.ms-excel&relatorio=" + getNomeArquivoRelatorioGeradoAgora() + "'");
            }
            setMensagemDetalhada("", "");
            setMensagemID("msg_entre_prmrelatorio");
        } catch (Exception e) {
            montarErro(e);
        } finally {
            setTipoRelatorio("PDF");
        }
    }

// * gets e sets ***************************************************************
    public boolean isMostrarEmpresa() {
        return mostrarEmpresa;
    }

    public void setMostrarEmpresa(boolean mostrarEmpresa) {
        this.mostrarEmpresa = mostrarEmpresa;
    }

    public ConsultarTurmaTO getConsultarTurma() {
        return consultarTurma;
    }

    public void setConsultarTurma(ConsultarTurmaTO consultarTurma) {
        this.consultarTurma = consultarTurma;
    }

    public HorarioTurmaVO getHorarioTurma() {
        return horarioTurma;
    }

    public void setHorarioTurma(HorarioTurmaVO horarioTurma) {
        this.horarioTurma = horarioTurma;
    }

    public List<HorarioTurmaConcatenadoTO> getListaHorarioTurmaConcatenado() {
        return listaHorarioTurmaConcatenado;
    }

    public void setListaHorarioTurmaConcatenado(List<HorarioTurmaConcatenadoTO> listaHorarioTurmaConcatenado) {
        this.listaHorarioTurmaConcatenado = listaHorarioTurmaConcatenado;
    }

    public boolean isContemNivelDesconto(){
        if(null != listaHorarioTurmaConcatenado) {
            for (HorarioTurmaConcatenadoTO h : getListaHorarioTurmaConcatenado()) {
                if(h.getTurma().getQtdeNivelOcupacao() > 0){
                    return true;
                }
            }
        }
        return false;
    }

    public HorarioTurmaConcatenadoTO getHorarioTurmaConcatenado() {
        return horarioTurmaConcatenado;
    }

    public void setHorarioTurmaConcatenado(HorarioTurmaConcatenadoTO horarioTurmaConcatenado) {
        this.horarioTurmaConcatenado = horarioTurmaConcatenado;
    }

    public List<ConsultarAlunosTurmaVO> getListaAlunosTurma() {
        return listaAlunosTurma;
    }

    public void setListaAlunosTurma(List<ConsultarAlunosTurmaVO> listaAlunosTurma) {
        this.listaAlunosTurma = listaAlunosTurma;
    }

    @Override
    public UsuarioVO getUsuario() {
        return usuario;
    }
    public void povoarItensOrdenacao(){

        itensOrdenacao.add(new SelectItem("nomeTurma","Idenficador Turma"));
        itensOrdenacao.add(new SelectItem("nomeProfessor","Nome Professor"));
    }

    public List<SelectItem> getItensOrdenacao() {
        if(itensOrdenacao.isEmpty())
            povoarItensOrdenacao();
        return itensOrdenacao;
    }

    public void setItensOrdenacao(List<SelectItem> itensOrdenacao) {
        this.itensOrdenacao = itensOrdenacao;
    }

    public String getOrdenacaoSelecionada() {
        return ordenacaoSelecionada;
    }

    public void setOrdenacaoSelecionada(String ordenacaoSelecionada) {
        this.ordenacaoSelecionada = ordenacaoSelecionada;
    }

    @Override
    public void setUsuario(UsuarioVO usuario) {
        this.usuario = usuario;
    }

    public int getTotalizadorAlunos() {
        return totalizadorAlunos;
    }

    public void setTotalizadorAlunos(int totalizadorAlunos) {
        this.totalizadorAlunos = totalizadorAlunos;
    }

    public List<ChamadaTO> getListaChamada() {
        return listaChamada;
    }

    public void setListaChamada(List<ChamadaTO> listaChamada) {
        this.listaChamada = listaChamada;
    }

    public List<PresencaVO> getColunas() {
        return colunas;
    }

    public void setColunas(List<PresencaVO> colunas) {
        this.colunas = colunas;
    }

    public Date getMesReferencia() {
        return mesReferencia;
    }

    public void setMesReferencia(Date mesReferencia) {
        this.mesReferencia = mesReferencia;
    }

    public String getApresentarMesReferencia() {
        return Uteis.getMesNomeReferencia(mesReferencia) + "/" + Uteis.getAnoData(mesReferencia);
    }

    public PresencaVO getPresenca() {
        return presenca;
    }

    public void setPresenca(PresencaVO presenca) {
        this.presenca = presenca;
    }

    public MatriculaAlunoHorarioTurmaRel getMatriculaAlunoHorarioTurmaRel() {
        return matriculaAlunoHorarioTurmaRel;
    }

    public void setMatriculaAlunoHorarioTurmaRel(MatriculaAlunoHorarioTurmaRel matriculaAlunoHorarioTurmaRel) {
        this.matriculaAlunoHorarioTurmaRel = matriculaAlunoHorarioTurmaRel;
    }

    public PessoaVO getPessoa() {
        return pessoa;
    }

    public void setPessoa(PessoaVO pessoa) {
        this.pessoa = pessoa;
    }

    public boolean isConsultaTurma() {
        return consultaTurma;
    }

    public void setConsultaTurma(boolean consultaTurma) {
        this.consultaTurma = consultaTurma;
    }

    public Date getDataNasc() {
        return dataNasc;
    }

    public void setDataNasc(Date dataNasc) {
        this.dataNasc = dataNasc;
    }

    public ContratoModalidadeVO getContratoModalidade() {
        return contratoModalidade;
    }

    public void setContratoModalidade(ContratoModalidadeVO contratoModalidade) {
        this.contratoModalidade = contratoModalidade;
    }

    public void gravarReposicao() throws Exception {
        gravarReposicao(false, false);
    }

    private void gravarReposicao(boolean permissaoJaFoiValidada, boolean exception) throws Exception {
        try {
            validarAulaDesmarcada();
            setMsgAlert("");
            if (Calendario.menor(consultarTurma.getReposicao().getDataOrigem(), Calendario.hoje())
                    && !permissaoJaFoiValidada
                    && UteisValidacao.emptyNumber(getConsultarTurma().getReposicao().getHorarioTurmaOrigem().getAulaDesmarcadaVO().getCodigo())) {
                if (Calendario.entre(consultarTurma.getReposicao().getDataOrigem(), Uteis.obterDataAnterior(Calendario.hoje(), getEmpresaLogado().getTempoAposFaltaReposicao()), Calendario.hoje())) {
                    AutorizacaoFuncionalidadeControle auto = (AutorizacaoFuncionalidadeControle) getControlador(AutorizacaoFuncionalidadeControle.class);
                    AutorizacaoFuncionalidadeListener listener = new AutorizacaoFuncionalidadeListener() {

                        @Override
                        public void onAutorizacaoComSucesso() throws Exception {
                            gravarReposicao(true, false);
                        }

                        @Override
                        public String getExecutarAoCompletar() {
                            return getOnComplete();
                        }
                    };
                    auto.autorizar("Autorização Especial", "RemarcarAulaPerdida_Autorizar",
                            "\"3.23 - Remarcar Aula Perdida - Autorizar\"", "panelDadosReposicao", listener);
                } else {
                    montarMsgAlert("Não é possível repôr para esta data.\n\r"
                            + "A falta do aluno foi há mais de " + getEmpresaLogado().getTempoAposFaltaReposicao() + " dias.");
                }
            } else {
                AulaDesmarcadaVO aulaDesmarcadaVO = getFacade().getAulaDesmarcada().consultarAulaDesmarcada(consultarTurma.getReposicao().getHorarioTurmaOrigem(),consultarTurma.getReposicao().getDataOrigem(),consultarTurma.getReposicao().getContrato().getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                if ((aulaDesmarcadaVO != null) && (!UteisValidacao.emptyNumber(aulaDesmarcadaVO.getCodigo())) && (aulaDesmarcadaVO.getDataReposicao() != null) ){
                    throw new ValidacaoException("Operação não permitida. A aula do dia " + Calendario.getData(consultarTurma.getReposicao().getDataOrigem(),"dd/MM/yyyy") + " ja foi reposta para o dia " +
                            Calendario.getData(aulaDesmarcadaVO.getDataReposicao(),"dd/MM/yyyy"));
                }

                List<Date> diasFeriados = getFacade().getFeriado().consultarPorPeriodoEmpresa(consultarTurma.getReposicao().getDataReposicao(),
                        consultarTurma.getReposicao().getDataReposicao(),
                        getEmpresaLogado());

                if (isAulaFeriado(getEmpresaLogado().isPermMarcarAulaFeriado(), diasFeriados, consultarTurma.getReposicao().getDataReposicao())) {

                    final Long horaAbertura = Calendario.pegaHoraEmMilisegundos(getEmpresaLogado().getHoraAberturaFeriado());
                    final Long horaFechamento = Calendario.pegaHoraEmMilisegundos(getEmpresaLogado().getHoraFechamentoFeriado());
                    final Long horaInicio = Calendario.pegaHoraEmMilisegundos(consultarTurma.getReposicao().getHorarioTurma().getHoraInicial());
                    final Long horaFim = Calendario.pegaHoraEmMilisegundos(consultarTurma.getReposicao().getHorarioTurma().getHoraFinal());

                    if (isHorarioIntervaloNaoPermitido(horaAbertura, horaFechamento, horaInicio, horaFim)) {

                        montarMsgAlert("Não é possível repôr para esta data.\n\r"
                                + "O Horario da aula no dia de feriado está fora do horario de trabalho da academia");

                    } else if (consultarTurma.getReposicao().getCodigo() == 0) {
                        gravaReposicao();
                    }
                } else if (consultarTurma.getReposicao().getCodigo() == 0 && diasFeriados.isEmpty()) {
                    gravaReposicao();
                } else {
                    montarMsgAlert("Não é possível repôr para esta data.\n\r"
                            + "O dia da reposição, é um dia de feriado");
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            montarMsgAlert(e.getMessage());
            if (exception) {
                throw e;
            }
        }
    }

    private void gravaReposicao() throws Exception {
        GestaoAulaService gestaoAulaService = new GestaoAulaService(Conexao.getFromSession(), (String) JSFUtilities.getFromSession("key"));
        gestaoAulaService.reporAula(consultarTurma.getReposicao(), true, null, null);
        consultarTurmas();
        montarMsgAlert("Reposição gravada com sucesso!");
        setMsgAlert("Richfaces.hideModalPanel('panelDadosReposicao');" + getMsgAlert());
    }

    private boolean isHorarioIntervaloNaoPermitido(final Long horaAbertura, final Long horaFechamento,
                                                   final Long horaInicio, final Long horaFim) {
        return (horaAbertura != null && horaFechamento != null && horaInicio != null && horaFim != null)
                && (horaInicio < horaAbertura || horaInicio  >= horaFechamento || horaFim > horaFechamento);
    }

    private boolean isAulaFeriado(Boolean isPermMarcarAulaFeriado, List<Date> diasFeriados, Date d) {
        return isPermMarcarAulaFeriado && diasFeriados != null && diasFeriados.contains(d);
    }

    public void doValidarReposicao() throws Exception {
        try {
            setMsgAlert("");
            if (consultarTurma.getReposicao().getHorarioTurma().getQuantidadeVagasDisponiveis() > 0) {
                gravarReposicao(false, true);
            } else if (consultarTurma.getReposicao().getTurmaDestino().isBloquearReposicaoAcimaLimite()) {
                StringBuilder sbValida = new StringBuilder("A turma está lotada e não são permitidas reposições acima da capacidade!");
                MensagemGenericaControle control = (MensagemGenericaControle) getControlador(MensagemGenericaControle.class);
                control.setMensagemDetalhada("", "");
                setMsgAlert("Richfaces.showModalPanel('mdlMensagemGenerica');");
                control.init("Reposição com divergência(s)",
                        sbValida.toString(),
                        this,
                        "Ok",
                        "",
                        "form,panelAutorizacaoFuncionalidade,mdlMensagemGenerica");
            } else {
                StringBuilder sbValida = new StringBuilder("Turma sem vagas, Mesmo assim deseja continuar?");
                MensagemGenericaControle control = (MensagemGenericaControle) getControlador(MensagemGenericaControle.class);
                control.setMensagemDetalhada("", "");
                setMsgAlert("Richfaces.showModalPanel('mdlMensagemGenerica');");
                control.init("Reposição com divergência(s)",
                        sbValida.toString(),
                        this, "gravarReposicao", "Richfaces.hideModalPanel('panelDadosReposicao');alert('Reposição gravada com sucesso!');", "", "", "form,panelAutorizacaoFuncionalidade,mdlMensagemGenerica");
            }
        }catch(Exception e){
            montarErro(e);
            setMsgAlert(getMensagemNotificar());
        }

    }


    public void prepararListaTelefones() throws Exception {
        try {
            ChamadaTO chamadaTO = (ChamadaTO) context().getExternalContext().getRequestMap().get("aluno");
            setAlunoTelefone(chamadaTO);
            setApresentarModalTelefoneAluno(Boolean.TRUE);
        } catch (Exception e) {
            setMensagemID("msg_erro");
            setMensagemDetalhada(e);
            montarMsgAlert(getMensagemDetalhada());
        }
    }

    public void acaoFecharModalTelefoneAluno() {
        setApresentarModalTelefoneAluno(Boolean.FALSE);
    }

    public String getOnComplete() {//chamada implícita por 'AutorizacaoFuncionalidadeControle.control.onComplete'
        return getMsgAlert();
    }

    public void setMapaTurmas(List<HorarioMapaTurmaTO> mapaTurmas) {
        this.mapaTurmas = mapaTurmas;
    }

    public List<HorarioMapaTurmaTO> getMapaTurmas() {
        return this.mapaTurmas;
    }

    public void carregarVezesSemana(){
        if(isPlanoCreditoTreino()){
            setVezesSemanaModalidade(getNrVezesCreditoTreino()+"X");
        } else {
            StringBuilder vezesSemana = new StringBuilder();
            int last = contratoModalidade.getPlanoModalidade().getPlanoModalidadeVezesSemanaVOs().size();
            for (int i = 0; i < last; i++) {
                PlanoModalidadeVezesSemanaVO vezesSemanaVO = (PlanoModalidadeVezesSemanaVO) contratoModalidade.getPlanoModalidade().getPlanoModalidadeVezesSemanaVOs().get(i);
                if (i > 0 && !(i == last - 1)) {
                    vezesSemana.append(", ");
                } else if (i == (last - 1) && last > 1) {
                    vezesSemana.append(" OU ");
                }
                vezesSemana.append(vezesSemanaVO.getNrVezes()).append("X");
            }
            setVezesSemanaModalidade(vezesSemana.toString());
        }
    }
    public List<String> getDiasSemanaLabels() {
        List<String> returns = new ArrayList<String>();
        returns.add(DiaSemana.SEGUNDA_FEIRA.getDescricaoSimples());
        returns.add(DiaSemana.TERCA_FEIRA.getDescricaoSimples());
        returns.add(DiaSemana.QUARTA_FEIRA.getDescricaoSimples());
        returns.add(DiaSemana.QUINTA_FEIRA.getDescricaoSimples());
        returns.add(DiaSemana.SEXTA_FEIRA.getDescricaoSimples());
        returns.add(DiaSemana.SABADO.getDescricaoSimples());
        returns.add(DiaSemana.DOMINGO.getDescricaoSimples());
        return returns;
    }
    public void setFiltroDatas(FiltroDatasTO filtroDatas) {
        this.filtroDatas = filtroDatas;
    }

    public FiltroDatasTO getFiltroDatas() {
        return filtroDatas;
    }

    public void setTodasTurmas(boolean todasTurmas) {
        this.todasTurmas = todasTurmas;
    }

    public boolean getTodasTurmas() {
        return todasTurmas;
    }

    public boolean isEmProcessamento() {
        return emProcessamento;
    }

    public void setEmProcessamento(boolean emProcessamento) {
        this.emProcessamento = emProcessamento;
    }

    public boolean isPollEnabled() {
        return pollEnabled;
    }

    public void setPollEnabled(boolean pollEnabled) {
        this.pollEnabled = pollEnabled;
    }

    public boolean isRenovacaoContrato() {
        return renovacaoContrato;
    }

    public void setRenovacaoContrato(boolean renovacaoContrato) {
        this.renovacaoContrato = renovacaoContrato;
    }

    public boolean isEmpresaPermiteRenovarContratosEmTurmasLotadas() {
        return empresaPermiteRenovarContratosEmTurmasLotadas;
    }

    public void setEmpresaPermiteRenovarContratosEmTurmasLotadas(boolean empresaPermiteRenovarContratosEmTurmasLotadas) {
        this.empresaPermiteRenovarContratosEmTurmasLotadas = empresaPermiteRenovarContratosEmTurmasLotadas;
    }

    public boolean isApresentarModalTelefoneAluno() {
        return apresentarModalTelefoneAluno;
    }

    public void setApresentarModalTelefoneAluno(boolean apresentarModalTelefoneAluno) {
        this.apresentarModalTelefoneAluno = apresentarModalTelefoneAluno;
    }

    public ChamadaTO getAlunoTelefone() {
        return alunoTelefone;
    }

    public void setAlunoTelefone(ChamadaTO alunoTelefone) {
        this.alunoTelefone = alunoTelefone;
    }

    private void validarAulaDesmarcada() throws Exception {
        if (!consultarTurma.getReposicao().getTurmaDestino().getModalidade().getCodigo().equals(consultarTurma.getReposicao().getTurmaOrigem().getModalidade().getCodigo())
                && !consultarTurma.getReposicao().getTurmaDestino().getModalidade().getTipo().equals(consultarTurma.getReposicao().getTurmaOrigem().getModalidade().getTipo())){
            throw new Exception("Modalidade da aula a ser reposta deve ser igual ou do mesmo tipo da modalidade da aula de origem!");
        }
        if(!UteisValidacao.emptyNumber(getConsultarTurma().getReposicao().getHorarioTurmaOrigem().getAulaDesmarcadaVO().getCodigo()) && !Calendario.igual(getConsultarTurma().getReposicao().getHorarioTurmaOrigem().getAulaDesmarcadaVO().getDataOrigem(), consultarTurma.getReposicao().getDataOrigem())){
                getConsultarTurma().getReposicao().getHorarioTurmaOrigem().setAulaDesmarcadaVO(new AulaDesmarcadaVO());
        }
        if(UteisValidacao.emptyNumber(getConsultarTurma().getReposicao().getHorarioTurmaOrigem().getAulaDesmarcadaVO().getCodigo())){
            getConsultarTurma().getReposicao().getHorarioTurmaOrigem().setAulaDesmarcadaVO(getFacade().getAulaDesmarcada().consultarAulaDesmarcadaPorDiaHorarioContrato(consultarTurma.getReposicao().getCliente().getCodigo(), consultarTurma.getReposicao().getContrato().getEmpresa().getCodigo(),
                    consultarTurma.getReposicao().getContrato().getCodigo(), consultarTurma.getReposicao().getTurmaOrigem().getCodigo(), consultarTurma.getReposicao().getHorarioTurmaOrigem().getCodigo(),
                    consultarTurma.getReposicao().getDataOrigem(), Uteis.NIVELMONTARDADOS_TODOS,true,true));
        }
    }

    public String getVezesSemanaModalidade() {
        return vezesSemanaModalidade;
    }

    public void setVezesSemanaModalidade(String vezesSemanaModalidade) {
        this.vezesSemanaModalidade = vezesSemanaModalidade;
    }

    public boolean isPlanoCreditoTreino() {
        return planoCreditoTreino;
    }

    public void setPlanoCreditoTreino(boolean planoCreditoTreino) {
        this.planoCreditoTreino = planoCreditoTreino;
    }

    public int getNrVezesCreditoTreino() {
        return nrVezesCreditoTreino;
    }

    public void setNrVezesCreditoTreino(int nrVezesCreditoTreino) {
        this.nrVezesCreditoTreino = nrVezesCreditoTreino;
    }

    public HorarioTurmaConcatenadoTO getHorarioTurmaListaChamada() {
        return horarioTurmaListaChamada;
    }

    public void setHorarioTurmaListaChamada(HorarioTurmaConcatenadoTO horarioTurmaListaChamada) {
        this.horarioTurmaListaChamada = horarioTurmaListaChamada;
    }

    public boolean isConsiderarDesmarcoes() {
        return considerarDesmarcoes;
    }

    public void setConsiderarDesmarcoes(boolean considerarDesmarcoes) {
        this.considerarDesmarcoes = considerarDesmarcoes;
    }

    public Date getDataReferenciaMapa() {
        return dataReferenciaMapa;
    }

    public void setDataReferenciaMapa(Date dataReferenciaMapa) {
        this.dataReferenciaMapa = dataReferenciaMapa;
    }
    
    public void gerarPeriodoConsultaMapa(){
        filtroDatas.preencherSemanaDataReferencia(dataReferenciaMapa);
    }

    public List getListaSelectItemProfessor() {
        return listaSelectItemProfessor;
    }

    public void setListaSelectItemProfessor(List listaSelectItemProfessor) {
        this.listaSelectItemProfessor = listaSelectItemProfessor;
    }

    /**
     * Método responsável por gerar uma lista de objetos do tipo <code>SelectItem</code> para preencher
     * o comboBox relativo ao atributo <code>Professor</code>.
     */
    public void montarListaSelectItemProfessor() throws Exception {
        permiteAlterarProfessorConsultaTurma = false;
        try {
            permissaoFuncionalidade(getUsuarioLogado(), "PermiteAlterarProfessorConsultaTurma"
                    ,"2.72 - Permite alterar professor através da tela de turma");


            List resultadoConsulta = consultarColaboradorPorTipoColaborador("");
            Iterator i = resultadoConsulta.iterator();
            List objs = new ArrayList();
            objs.add(new SelectItem(0, ""));
            while (i.hasNext()) {
                ColaboradorVO obj = (ColaboradorVO) i.next();
                objs.add(new SelectItem(obj.getCodigo(), obj.getPessoa().getNome()));
            }
            Ordenacao.ordenarLista(objs, "label");
            setListaSelectItemProfessor(objs);

            permiteAlterarProfessorConsultaTurma = true;
        }catch (Exception e){

        }
    }

    public boolean isPermiteAlterarProfessorConsultaTurma() {
        return permiteAlterarProfessorConsultaTurma;
    }

    public void setPermiteAlterarProfessorConsultaTurma(boolean permiteAlterarProfessorConsultaTurma) {
        this.permiteAlterarProfessorConsultaTurma = permiteAlterarProfessorConsultaTurma;
    }

    /**
     * Método responsável por consultar dados da entidade <code><code> e montar o atributo <code>situacao</code>
     * Este atributo é uma lista (<code>List</code>) utilizada para definir os valores a serem apresentados no ComboBox correspondente
     */
    public List consultarColaboradorPorTipoColaborador(String tipoColaboradorPrm) throws Exception {
        List lista;
        if (getEmpresaLogado().getCodigo() == 0) {
            lista = getFacade().getColaborador().consultarPorTipoColaboradorAtivo(TipoColaboradorEnum.PROFESSOR, "AT", consultarTurma.getEmpresa().getCodigo(), false, Uteis.NIVELMONTARDADOS_TODOS);
        } else {
            lista = getFacade().getColaborador().consultarPorTipoColaboradorAtivo(TipoColaboradorEnum.PROFESSOR, "AT", getEmpresaLogado().getCodigo(), false, Uteis.NIVELMONTARDADOS_TODOS);
        }

        return lista;
    }

    public void alterarProfessor(ValueChangeEvent event) throws Exception {
        PhaseId phase = event.getPhaseId();

        if (phase.equals(PhaseId.INVOKE_APPLICATION) && permiteAlterarProfessorConsultaTurma) {

            HorarioTurmaVO localTurma = getFacade().getHorarioTurma().consultarPorChavePrimaria(horarioTurma.getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            if(null != localTurma) {
                localTurma.getProfessor().setCodigo((Integer) event.getNewValue());
                localTurma.setUsuarioVO(getUsuarioLogado());
                getFacade().getHorarioTurma().alterar(localTurma);
            }else{
                montarErro("Turma não encontrada");
            }

        } else {
            event.setPhaseId(PhaseId.INVOKE_APPLICATION);
            event.queue();
        }
    }

    public void irParaTelaCliente() {
        ConsultarAlunosTurmaVO obj = (ConsultarAlunosTurmaVO) context().getExternalContext().getRequestMap().get("aluno");
        try {
            if (obj == null) {
                throw new Exception("Cliente Não Encontrado.");
            } else {
                if (UteisValidacao.emptyNumber(obj.getClienteVO().getCodigo())) {
                    return;
                }
                ClienteVO clienteVO = new ClienteVO();
                clienteVO.setCodigo(obj.getClienteVO().getCodigo());
                irParaTelaCliente(clienteVO);
            }
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public Date getDataInicio() {
        return dataInicio;
    }

    public void setDataInicio(Date dataInicio) {
        this.dataInicio = dataInicio;
    }

    public Date getDataFim() {
        return dataFim;
    }

    public void setDataFim(Date dataFim) {
        this.dataFim = dataFim;
    }

    public String getNomesAlunos() {
        return nomesAlunos;
    }

    public void setNomesAlunos(String nomesAlunos) {
        this.nomesAlunos = nomesAlunos;
    }

    public List<SelectItem> getItensNomes() {
        if(itensNomes.isEmpty()){
            povoarItensNomes();
        }
        return itensNomes;
    }

    public void povoarItensNomes(){
        itensNomes = new ArrayList<>();
        itensNomes.add(new SelectItem("abreviado","Abreviados"));
        itensNomes.add(new SelectItem("completo","Completos"));
        itensNomes.add(new SelectItem("primeiro","Primeiro nome"));
    }

    private void normalizarNomes(){
        try {
            for(HorarioMapaTurmaTO hm : mapaTurmas){
                for(DiaSemanaMapaTO dm : hm.getMapa().getLista()){
                    for(ItemMapaTurmasTO it : dm.getItens()){
                        for(AlunoMapaTurmasTO al : it.getAlunos()){
                            switch (nomesAlunos){
                                case "abreviado":
                                    al.setNome(Uteis.getNomeAbreviado(al.getNome()));
                                    break;
                                case "primeiro":
                                    al.setNome(Uteis.getPrimeiroNome(al.getNome()));
                                    break;
                            }
                        }
                    }
                }
            }
        }catch (Exception e){
            e.printStackTrace();
        }
    }

    public void setItensNomes(List<SelectItem> itensNomes) {
        this.itensNomes = itensNomes;
    }

    public Integer getFrequenciaVagasTotal() {
        return frequenciaVagasTotal;
    }

    public Integer getFrequenciaVagasOcupacao() {
        return frequenciaVagasOcupacao;
    }

    public Integer getFrequenciaVagasLivres() {
        return frequenciaVagasLivres;
    }

    public String getFrequenciaVagasMediaOcupacao() {
        return frequenciaVagasMediaOcupacao;
    }

    public void processaDadosGraficoDeFrequenciaTurmas() {
        this.frequenciaVagasTotal = 0;
        this.frequenciaVagasOcupacao = 0;
        this.frequenciaVagasMediaOcupacao = "0";
        for (HorarioTurmaConcatenadoTO item : this.listaHorarioTurmaConcatenado) {
            int frequenciaVagasTotal;
            int frequenciaVagasOcupacao;

            frequenciaVagasTotal = item.getHorarioTurmaDom().getNrMaximoAluno() +
                    item.getHorarioTurmaSeg().getNrMaximoAluno() +
                    item.getHorarioTurmaTer().getNrMaximoAluno() +
                    item.getHorarioTurmaQua().getNrMaximoAluno() +
                    item.getHorarioTurmaQui().getNrMaximoAluno() +
                    item.getHorarioTurmaSex().getNrMaximoAluno() +
                    item.getHorarioTurmaSab().getNrMaximoAluno();

            frequenciaVagasOcupacao = (item.getHorarioTurmaDom().getNrAlunoMatriculado() + item.getHorarioTurmaDom().getNrAlunoEntraramPorReposicao() - item.getHorarioTurmaDom().getNrAlunoSairamPorReposicao()) +
                    (item.getHorarioTurmaSeg().getNrAlunoMatriculado() + item.getHorarioTurmaSeg().getNrAlunoEntraramPorReposicao() - item.getHorarioTurmaSeg().getNrAlunoSairamPorReposicao()) +
                    (item.getHorarioTurmaTer().getNrAlunoMatriculado() + item.getHorarioTurmaTer().getNrAlunoEntraramPorReposicao() - item.getHorarioTurmaTer().getNrAlunoSairamPorReposicao()) +
                    (item.getHorarioTurmaQua().getNrAlunoMatriculado() + item.getHorarioTurmaQua().getNrAlunoEntraramPorReposicao() - item.getHorarioTurmaQua().getNrAlunoSairamPorReposicao()) +
                    (item.getHorarioTurmaQui().getNrAlunoMatriculado() + item.getHorarioTurmaQui().getNrAlunoEntraramPorReposicao() - item.getHorarioTurmaQui().getNrAlunoSairamPorReposicao()) +
                    (item.getHorarioTurmaSex().getNrAlunoMatriculado() + item.getHorarioTurmaSex().getNrAlunoEntraramPorReposicao() - item.getHorarioTurmaSex().getNrAlunoSairamPorReposicao()) +
                    (item.getHorarioTurmaSab().getNrAlunoMatriculado() + item.getHorarioTurmaSab().getNrAlunoEntraramPorReposicao() - item.getHorarioTurmaSab().getNrAlunoSairamPorReposicao());

            Integer qtdDividir = obterPorQuantoDividir(item);
            this.frequenciaVagasTotal += (frequenciaVagasTotal / qtdDividir);
            this.frequenciaVagasOcupacao += (frequenciaVagasOcupacao / qtdDividir);
        }

        this.frequenciaVagasLivres = frequenciaVagasTotal - frequenciaVagasOcupacao;

        this.frequenciaVagasMediaOcupacao = this.frequenciaVagasTotal <= 0 ?
                "0" :
                String.valueOf((this.frequenciaVagasOcupacao * 100) / frequenciaVagasTotal);
    }

    private Integer obterPorQuantoDividir(HorarioTurmaConcatenadoTO item) {
        int total = 0;
        if (item.getHorarioTurmaDom().getNrMaximoAluno() > 0) {
            total++;
        }
        if (item.getHorarioTurmaSeg().getNrMaximoAluno() > 0) {
            total++;
        }
        if (item.getHorarioTurmaTer().getNrMaximoAluno() > 0) {
            total++;
        }
        if (item.getHorarioTurmaQua().getNrMaximoAluno() > 0) {
            total++;
        }
        if (item.getHorarioTurmaQui().getNrMaximoAluno() > 0) {
            total++;
        }
        if (item.getHorarioTurmaSex().getNrMaximoAluno() > 0) {
            total++;
        }
        if (item.getHorarioTurmaSab().getNrMaximoAluno() > 0) {
            total++;
        }
        return ((total == 0) ? 1 : total);
    }

    public boolean isExibeRelatorioFrequenciaTurmas() {
        return exibeRelatorioFrequenciaTurmas;
    }

    public void setExibeRelatorioFrequenciaTurmas(boolean exibeRelatorioFrequenciaTurmas) {
        this.exibeRelatorioFrequenciaTurmas = exibeRelatorioFrequenciaTurmas;
    }
}

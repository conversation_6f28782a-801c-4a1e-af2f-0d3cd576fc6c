package controle.plano;

import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.estrutura.exportador.controle.ExportadorListaControle;
import controle.arquitetura.SuperControle;
import static controle.arquitetura.SuperControle.registrarLogErroObjetoVO;
import controle.arquitetura.security.LoginControle;
import controle.basico.clube.MensagemGenericaControle;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.plano.ComposicaoModalidadeVO;
import negocio.comuns.plano.ComposicaoVO;
import negocio.comuns.plano.ModalidadeVO;
import negocio.comuns.utilitarias.ControleConsulta;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.plano.Composicao;

import javax.faces.event.ActionEvent;
import javax.faces.model.SelectItem;
import java.text.NumberFormat;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;
import negocio.comuns.arquitetura.LogVO;
import negocio.comuns.plano.HorarioVO;
import negocio.comuns.utilitarias.UteisValidacao;

/**
 * Classe responsável por implementar a interação entre os componentes JSF das
 * páginas composicaoForm.jsp composicaoCons.jsp) com as funcionalidades da
 * classe
 * <code>Composicao</code>. Implemtação da camada controle (Backing Bean).
 *
 * @see SuperControle
 * @see Composicao
 * @see ComposicaoVO
 */
public class ComposicaoControle extends SuperControle {

    protected List listaSelectItemModalidade;
    protected List listaSelectItemEmpresa;
    protected String campoConsultaModalidade;
    protected String valorConsultaModalidade;
    protected List listaConsultaModalidade;
    protected boolean desenharModalidade;
    private ComposicaoVO composicaoVO;
    protected List listaSelectItemHorario;
    /**
     * Interface
     * <code>ComposicaoInterfaceFacade</code> responsável pela interconexão da
     * camada de controle com a camada de negócio. Criando uma independência da
     * camada de controle com relação a tenologia de persistência dos dados
     * (DesignPatter: Façade).
     */
    private ComposicaoModalidadeVO composicaoModalidadeVO;

    public ComposicaoControle() throws Exception {
        obterUsuarioLogado();
        inicializarFacades();
        inicializarEmpresaLogado();
        setControleConsulta(new ControleConsulta());
        setMensagemID("");
    }

    public void inicializarUsuarioLogado() {
        try {
            composicaoVO.setUsuarioVO(getUsuarioLogado());
        } catch (Exception ignored) {
        }
    }

    public void inicializarEmpresaLogado() {
        try {
            setComposicaoVO(new ComposicaoVO());
            composicaoVO.getEmpresa().setCodigo(getEmpresaLogado().getCodigo());
        } catch (Exception ignored) {
        }
    }

    public void montarListaSelectItemHorario() {
        List resultadoConsulta;
        try {
            resultadoConsulta = getFacade().getHorario().consultarPorDescricao("",
                    false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            Iterator i = resultadoConsulta.iterator();
            List<SelectItem> objs = new ArrayList<SelectItem>();
            objs.add(new SelectItem(0, ""));
            while (i.hasNext()) {
                HorarioVO obj = (HorarioVO) i.next();
                objs.add(new SelectItem(obj.getCodigo(), obj.getCodigo() + " - " + obj.getDescricao()));
            }
            setListaSelectItemHorario(objs);
        } catch (Exception ex) {
            Logger.getLogger(ComposicaoControle.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    /**
     * Rotina responsável por disponibilizar um novo objeto da classe
     * <code>Composicao</code> para edição pelo usuário da aplicação.
     */
    public String novo() throws Exception {
        setComposicaoVO(new ComposicaoVO());
        inicializarUsuarioLogado();
        if (getEmpresaLogado().getCodigo() != 0) {
            composicaoVO.getEmpresa().setCodigo(getEmpresaLogado().getCodigo().intValue());
        }
        setDesenharModalidade(false);
        inicializarListasSelectItemTodosComboBox();
        setComposicaoModalidadeVO(new ComposicaoModalidadeVO());
        setCampoConsultaModalidade("");
        setValorConsultaModalidade("");
        setListaConsultaModalidade(new ArrayList());
        validarEmpresa();
        limparMsg();
        return "editar";
    }

    public void novoSemRedirect() throws Exception{
            novo();
    }

    /**
     * Rotina responsável por disponibilizar os dados de um objeto da classe
     * <code>Composicao</code> para alteração. O objeto desta classe é
     * disponibilizado na session da página (request) para que o JSP
     * correspondente possa disponibilizá-lo para edição.
     */
    public String editar() {
        Integer codigoConsulta = Integer.parseInt(request().getParameter("chavePrimaria"));
        try {
            ComposicaoVO obj = getFacade().getComposicao().consultarPorChavePrimaria(codigoConsulta, Uteis.NIVELMONTARDADOS_DADOSENTIDADESUBORDINADAS);
            obj.setNovoObj(false);
            obj.registrarObjetoVOAntesDaAlteracao();
            obj.registrarModalidadesAntesDaAlteracao();
            setComposicaoVO(obj);
            inicializarUsuarioLogado();
            inicializarListasSelectItemTodosComboBox();
            setComposicaoModalidadeVO(new ComposicaoModalidadeVO());
            setCampoConsultaModalidade("");
            setValorConsultaModalidade("");
            setListaConsultaModalidade(new ArrayList());
            limparMsg();
        } catch (Exception e) {
            setErro(true);
            setSucesso(false);
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
        return "editar";
    }

    /**
     * Rotina responsável por gravar no BD os dados editados de um novo objeto
     * da classe
     * <code>Composicao</code>. Caso o objeto seja novo (ainda não gravado no
     * BD) é acionado a operação
     * <code>incluir()</code>. Caso contrário é acionado o
     * <code>alterar()</code>. Se houver alguma inconsistência o objeto não é
     * gravado, sendo re-apresentado para o usuário juntamente com uma mensagem
     * de erro.
     */
    public String gravar() {
        try {
            if (composicaoVO.isNovoObj()) {
                getFacade().getComposicao().incluir(composicaoVO);
                incluirLogInclusao();
            } else {
                ComposicaoVO compAuxVO = getFacade().getComposicao().consultarPorChavePrimaria(composicaoVO.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                getFacade().getComposicao().alterar(composicaoVO);
                incluirLogAlteracao();
            }
            setMensagemID("msg_dados_gravados");
            setSucesso(true);
            setErro(false);
            return "editar";
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
            return "editar";
        }
    }

    /**
     * Rotina responsavel por executar as consultas disponiveis no JSP
     * ComposicaoCons.jsp. Define o tipo de consulta a ser executada, por meio
     * de ComboBox denominado campoConsulta, disponivel neste mesmo JSP. Como
     * resultado, disponibiliza um List com os objetos selecionados na sessao da
     * pagina.
     */
    @Override
    public String consultar() {
        try {
            super.consultar();
            List objs = new ArrayList();
            if (getControleConsulta().getCampoConsulta().equals("codigo")) {
                if (getControleConsulta().getValorConsulta().equals("")) {
                    getControleConsulta().setValorConsulta("0");
                }
                int valorInt = Integer.parseInt(getControleConsulta().getValorConsulta());
                objs = getFacade().getComposicao().consultarPorCodigo(valorInt, composicaoVO.getEmpresa().getCodigo(), true, Uteis.NIVELMONTARDADOS_TODOS);
            }
            if (getControleConsulta().getCampoConsulta().equals("descricao")) {
                objs = getFacade().getComposicao().consultarPorDescricao(getControleConsulta().getValorConsulta(), composicaoVO.getEmpresa().getCodigo(), true, Uteis.NIVELMONTARDADOS_TODOS);
            }
            objs = ControleConsulta.obterSubListPaginaApresentar(objs, controleConsulta);
            definirVisibilidadeLinksNavegacao(controleConsulta.getPaginaAtual(), controleConsulta.getNrTotalPaginas());
            setListaConsulta(objs);
            limparMsg();
            return "consultar";
        } catch (Exception e) {
            setListaConsulta(new ArrayList());
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
            return "consultar";
        }
    }

    public void consultarModalidade() {
        try {
            super.consultar();
            List objs = new ArrayList();
            List lista = new ArrayList();
            if (getCampoConsultaModalidade().equals("codigo")) {
                if (getValorConsultaModalidade().equals("")) {
                    setValorConsultaModalidade("0");
                }
                int valorInt = Integer.parseInt(getValorConsultaModalidade());
                objs = getFacade().getModalidade().consultarPorCodigo(valorInt, composicaoVO.getEmpresa().getCodigo(), true, Uteis.NIVELMONTARDADOS_TODOS);
                if (composicaoVO.getEmpresa().getCodigo() != 0) {
                    lista = getFacade().getModalidade().consultarPorCodigo(valorInt, 0, true, Uteis.NIVELMONTARDADOS_TODOS);
                    Iterator i = lista.iterator();
                    while (i.hasNext()) {
                        ModalidadeVO object = (ModalidadeVO) i.next();
                        if (object.getModalidadeEmpresaVOs().isEmpty()) {
                            objs.add(object);
                        }
                    }
                }

            }
            if (getCampoConsultaModalidade().equals("nome")) {
                objs = getFacade().getModalidade().consultarPorNome(getValorConsultaModalidade(), composicaoVO.getEmpresa().getCodigo(), true, Uteis.NIVELMONTARDADOS_TODOS);
                if (composicaoVO.getEmpresa().getCodigo() != 0) {
                    lista = getFacade().getModalidade().consultarPorNome(getValorConsultaModalidade(), 0, true, Uteis.NIVELMONTARDADOS_TODOS);
                    Iterator i = lista.iterator();
                    while (i.hasNext()) {
                        ModalidadeVO object = (ModalidadeVO) i.next();
                        if (object.getModalidadeEmpresaVOs().isEmpty()) {
                            objs.add(object);
                        }
                    }
                }
            }
            setListaConsultaModalidade(objs);
            setMensagemID("msg_dados_consultados");

        } catch (Exception e) {
            setListaConsultaModalidade(new ArrayList());
            setMensagemDetalhada("msg_erro", e.getMessage());

        }
    }

    public void selecionarModalidade() {
        ModalidadeVO obj = (ModalidadeVO) context().getExternalContext().getRequestMap().get("modalidade");
        getComposicaoModalidadeVO().setModalidade(obj);
        getComposicaoModalidadeVO().setPrecoModalidade(obj.getValorMensal());
        obj = null;
        listaConsultaModalidade.clear();
        campoConsultaModalidade = null;
        valorConsultaModalidade = null;
    }

    public void validarEmpresa() {
        if (composicaoVO.getEmpresa() == null || composicaoVO.getEmpresa().getCodigo() == 0) {
            setDesenharModalidade(false);
            montarListaSelectItemModalidade();
            this.composicaoVO.setComposicaoModalidadeVOs(new ArrayList<ComposicaoModalidadeVO>());
        } else {
            listaConsultaModalidade.clear();
            campoConsultaModalidade = null;
            valorConsultaModalidade = null;
            getComposicaoModalidadeVO().setModalidade(new ModalidadeVO());
            getComposicaoModalidadeVO().setPrecoModalidade(0.0);
            montarListaSelectItemModalidade();
            this.composicaoVO.setComposicaoModalidadeVOs(new ArrayList<ComposicaoModalidadeVO>());
            setDesenharModalidade(true);
        }
    }

    public void confirmarExcluir(){
        MensagemGenericaControle control = (MensagemGenericaControle) getControlador(MensagemGenericaControle.class);
        control.setMensagemDetalhada("", "");
        setMsgAlert("Richfaces.showModalPanel('mdlMensagemGenerica');");
        control.init("Exclusão de Pacote",
                "Deseja excluir o pacote?",
                this, "excluir", "", "", "", "");
    }

    /**
     * Operação responsável por processar a exclusão um objeto da classe
     * <code>ComposicaoVO</code> Após a exclusão ela automaticamente aciona a
     * rotina para uma nova inclusão.
     */
    public String excluir() {
        try {
            getFacade().getComposicao().excluir(composicaoVO);
            incluirLogExclusao();
            setComposicaoVO(new ComposicaoVO());
            setComposicaoModalidadeVO(new ComposicaoModalidadeVO());
            setCampoConsultaModalidade("");
            setValorConsultaModalidade("");
            setListaConsultaModalidade(new ArrayList());
            setMensagemID("msg_dados_excluidos");
            setSucesso(true);
            setErro(false);
            return "editar";
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
            return "editar";
        }
    }

    /**
     * Método responsável por adicionar um novo objeto da classe
     * <code>ComposicaoModalidade</code> para o objeto
     * <code>composicaoVO</code> da classe
     * <code>Composicao</code>
     */
    public void adicionarComposicaoModalidade() throws Exception {
        try {
            if(UteisValidacao.emptyNumber(getComposicaoModalidadeVO().getNrVezes())){
                getComposicaoModalidadeVO().setNrVezes(0);
            }
            if (!getComposicaoVO().getCodigo().equals(0)) {
                composicaoModalidadeVO.setComposicao(getComposicaoVO().getCodigo());
            }
            if (getComposicaoModalidadeVO().getModalidade().getCodigo() != 0) {
                Integer campoConsulta = getComposicaoModalidadeVO().getModalidade().getCodigo();
                ModalidadeVO modalidade = getFacade().getModalidade().consultarPorChavePrimaria(campoConsulta, Uteis.NIVELMONTARDADOS_TODOS);
                getComposicaoModalidadeVO().setModalidade(modalidade);
            }
            getComposicaoVO().adicionarObjComposicaoModalidadeVOs(getComposicaoModalidadeVO());
            Iterator j = getComposicaoVO().getComposicaoModalidadeVOs().iterator();
            if (getComposicaoVO().isModalidadesEspecificas()) {
                getComposicaoVO().setPrecoComposicao(0.0);
            }
            if (getComposicaoVO().isModalidadesEspecificas()) {
                while (j.hasNext()) {
                    ComposicaoModalidadeVO objExistente = (ComposicaoModalidadeVO) j.next();
                    if (objExistente.getNrVezes().equals(0)) {//Max: 05/12/2014 só pode somar as modalidades que são referência, as específicas não podem compor o valor do pacote
                        getComposicaoVO().setPrecoComposicao(getComposicaoVO().getPrecoComposicao() + objExistente.getValorMensalComposicao());
                    }
                }
            }
            this.setComposicaoModalidadeVO(new ComposicaoModalidadeVO());
            setMensagemID("msg_dados_adicionados");

        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());

        }
    }

    /**
     * Método responsável por disponibilizar dados de um objeto da classe
     * <code>ComposicaoModalidade</code> para edição pelo usuário.
     */
    public String editarComposicaoModalidade() throws Exception {
        ComposicaoModalidadeVO obj = (ComposicaoModalidadeVO) context().getExternalContext().getRequestMap().get("composicaoModalidade");
        setComposicaoModalidadeVO(obj);
        return "editar";
    }

    /**
     * Método responsável por remover um novo objeto da classe
     * <code>ComposicaoModalidade</code> do objeto
     * <code>composicaoVO</code> da classe
     * <code>Composicao</code>
     */
    public String removerComposicaoModalidade() throws Exception {
        ComposicaoModalidadeVO obj = (ComposicaoModalidadeVO) context().getExternalContext().getRequestMap().get("composicaoModalidade");
        getComposicaoVO().setPrecoComposicao(getComposicaoVO().getPrecoComposicao() - obj.getValorMensalComposicao());
        getComposicaoVO().getComposicaoModalidadeVOs().remove(obj);
        setMensagemID("msg_dados_excluidos");
        return "editar";
    }

    public void atualizaComposicao() {
        composicaoVO.setComposicaoModalidadeVOs(new ArrayList<ComposicaoModalidadeVO>());
        composicaoVO.setPrecoComposicao(0.0);
        composicaoModalidadeVO = new ComposicaoModalidadeVO();
    }

    public void irPaginaInicial() throws Exception {
        controleConsulta.setPaginaAtual(1);
        this.consultar();
    }

    public void irPaginaAnterior() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getPaginaAtual() - 1);
        this.consultar();
    }

    public void irPaginaPosterior() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getPaginaAtual() + 1);
        this.consultar();
    }

    public void irPaginaFinal() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getNrTotalPaginas());
        this.consultar();
    }

    /**
     * Método responsável por gerar uma lista de objetos do tipo
     * <code>SelectItem</code> para preencher o comboBox relativo ao atributo
     * <code>Modalidade</code>.
     */
    public void montarListaSelectItemModalidade(String prm) throws Exception {
        final NumberFormat nf = NumberFormat.getInstance();
        nf.setMinimumFractionDigits(2);
        if (this.getComposicaoVO().getEmpresa().getCodigo() != 0) {
            List resultadoConsulta = consultarModalidadePorNome(prm);
            Iterator i = resultadoConsulta.iterator();
            List<SelectItem> objs = new ArrayList<SelectItem>();
            objs.add(new SelectItem(0, ""));
            while (i.hasNext()) {
                ModalidadeVO obj = (ModalidadeVO) i.next();
                objs.add(new SelectItem(obj.getCodigo(),
                        String.format("%s - %s X - Valor Mensal: R$ %s",
                        obj.getNome(), obj.getNrVezes(), nf.format(Double.valueOf(obj.getValorMensal().toString())))));

            }
            setListaSelectItemModalidade(objs);

        } else {
            setListaSelectItemModalidade(new ArrayList());
        }
    }

    /**
     * Método responsável por atualizar o ComboBox relativo ao atributo
     * <code>Modalidade</code>. Buscando todos os objetos correspondentes a
     * entidade
     * <code>Modalidade</code>. Esta rotina não recebe parâmetros para filtragem
     * de dados, isto é importante para a inicialização dos dados da tela para o
     * acionamento por meio requisições Ajax.
     */
    public void montarListaSelectItemModalidade() {
        try {
            montarListaSelectItemModalidade("");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * Método responsável por consultar dados da entidade
     * <code><code> e montar o atributo
     * <code>nome</code> Este atributo é uma lista (
     * <code>List</code>) utilizada para definir os valores a serem apresentados
     * no ComboBox correspondente
     */
    public List consultarModalidadePorNome(String nomePrm) throws Exception {
        List<ModalidadeVO> objs = getFacade().getModalidade().consultarPorNomeModalidadeSemDesativado(nomePrm, this.getComposicaoVO().getEmpresa().getCodigo(), false, Uteis.NIVELMONTARDADOS_TODOS);
        if (composicaoVO.getEmpresa().getCodigo() != 0) {
            List lista = getFacade().getModalidade().consultarPorNome("", 0, true, Uteis.NIVELMONTARDADOS_TODOS);
            Iterator i = lista.iterator();
            while (i.hasNext()) {
                ModalidadeVO object = (ModalidadeVO) i.next();
                if (object.getModalidadeEmpresaVOs().isEmpty()) {
                    objs.add(object);
                }
            }
        }
        return objs;
    }

    /**
     * Método responsável por gerar uma lista de objetos do tipo
     * <code>SelectItem</code> para preencher o comboBox relativo ao atributo
     * <code>Empresa</code>.
     */
    public void montarListaSelectItemEmpresa(String prm) throws Exception {
        List<SelectItem> objs = new ArrayList<SelectItem>();
        objs.add(new SelectItem(0, ""));
        List resultadoConsulta = consultarEmpresaPorNome(prm);
        Iterator i = resultadoConsulta.iterator();
        while (i.hasNext()) {
            EmpresaVO obj = (EmpresaVO) i.next();
            objs.add(new SelectItem(obj.getCodigo(), obj.getNome()));
        }
        setListaSelectItemEmpresa(objs);
        if (getEmpresaLogado().getCodigo() != 0) {
            this.getComposicaoVO().getEmpresa().setCodigo(getEmpresaLogado().getCodigo().intValue());
            this.getComposicaoVO().getEmpresa().setNome(getEmpresaLogado().getNome());
            setDesenharModalidade(true);
        }

    }

    /**
     * Método responsável por atualizar o ComboBox relativo ao atributo
     * <code>Empresa</code>. Buscando todos os objetos correspondentes a
     * entidade
     * <code>Empresa</code>. Esta rotina não recebe parâmetros para filtragem de
     * dados, isto é importante para a inicialização dos dados da tela para o
     * acionamento por meio requisições Ajax.
     */
    public void montarListaSelectItemEmpresa() {
        try {
            montarListaSelectItemEmpresa("");
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    /**
     * Método responsável por consultar dados da entidade
     * <code><code> e montar o atributo
     * <code>nome</code> Este atributo é uma lista (
     * <code>List</code>) utilizada para definir os valores a serem apresentados
     * no ComboBox correspondente
     */
    public List consultarEmpresaPorNome(String nomePrm) throws Exception {
        return getFacade().getEmpresa().consultarPorNome(nomePrm,true, false, Uteis.NIVELMONTARDADOS_TODOS);
    }

    /**
     * Método responsável por inicializar a lista de valores
     * (
     * <code>SelectItem</code>) para todos os ComboBox's.
     */
    public void inicializarListasSelectItemTodosComboBox() {
        montarListaSelectItemEmpresa();
        montarListaSelectItemModalidade();
        montarListaSelectItemHorario();
    }

    /**
     * Rotina responsável por preencher a combo de consulta da telas.
     */
    public List getTipoConsultaCombo() {
        List<SelectItem> itens = new ArrayList<SelectItem>();
        itens.add(new SelectItem("descricao", "Descrição"));
        itens.add(new SelectItem("codigo", "Código"));
        return itens;
    }

    public List getTipoConsultaComboModalidade() {
        List<SelectItem> itens = new ArrayList<SelectItem>();
        itens.add(new SelectItem("codigo", "Código"));
        itens.add(new SelectItem("nome", "Nome"));
        return itens;
    }

    /**
     * Rotina responsável por organizar a paginação entre as páginas resultantes
     * de uma consulta.
     */
    public String inicializarConsultar() {
        if (composicaoVO.getUsuarioVO().getAdministrador()) {
            composicaoVO.setEmpresa(new EmpresaVO());
        }
        setPaginaAtualDeTodas("0/0");
        setListaConsulta(new ArrayList());
        definirVisibilidadeLinksNavegacao(0, 0);
        setSucesso(false);
        setErro(false);
        setMensagemID("msg_entre_prmconsulta");
        return "consultar";
    }

    public List getListaSelectItemModalidade() {
        return (listaSelectItemModalidade);
    }

    public void setListaSelectItemModalidade(List listaSelectItemModalidade) {
        this.listaSelectItemModalidade = listaSelectItemModalidade;
    }

    public ComposicaoModalidadeVO getComposicaoModalidadeVO() {
        return composicaoModalidadeVO;
    }

    public void setComposicaoModalidadeVO(ComposicaoModalidadeVO composicaoModalidadeVO) {
        this.composicaoModalidadeVO = composicaoModalidadeVO;
    }

    public ComposicaoVO getComposicaoVO() {
        return composicaoVO;
    }

    public void setComposicaoVO(ComposicaoVO composicaoVO) {
        this.composicaoVO = composicaoVO;
    }

    public String getCampoConsultaModalidade() {
        return campoConsultaModalidade;
    }

    public void setCampoConsultaModalidade(String campoConsultaModalidade) {
        this.campoConsultaModalidade = campoConsultaModalidade;
    }

    public List getListaConsultaModalidade() {
        return listaConsultaModalidade;
    }

    public void setListaConsultaModalidade(List listaConsultaModalidade) {
        this.listaConsultaModalidade = listaConsultaModalidade;
    }

    public String getValorConsultaModalidade() {
        return valorConsultaModalidade;
    }

    public void setValorConsultaModalidade(String valorConsultaModalidade) {
        this.valorConsultaModalidade = valorConsultaModalidade;
    }

    public List getListaSelectItemEmpresa() {
        return listaSelectItemEmpresa;
    }

    public void setListaSelectItemEmpresa(List listaSelectItemEmpresa) {
        this.listaSelectItemEmpresa = listaSelectItemEmpresa;
    }

    public boolean isDesenharModalidade() {
        return desenharModalidade;
    }

    public void setDesenharModalidade(boolean desenharModalidade) {
        this.desenharModalidade = desenharModalidade;
    }

    public List getListaSelectItemHorario() {
        return listaSelectItemHorario;
    }

    public void setListaSelectItemHorario(List listaSelectItemHorario) {
        this.listaSelectItemHorario = listaSelectItemHorario;
    }

    public void exportar(ActionEvent evt) throws Exception {
        ExportadorListaControle exportadorListaControle = (ExportadorListaControle) JSFUtilities.getFromRequest(ExportadorListaControle.class.getSimpleName());
        String paramsTableFiltrada = context().getExternalContext().getRequestParameterMap().get("paramsTableFiltrada");

        String[] split = paramsTableFiltrada.split(",");
        String campoOrdenacao = split[0].replace("[", "");
        String ordem = split[1];
        String filtro = split[2].replace("''", "");
        filtro = filtro.replace("]", "");
        List listaParaImpressao = getFacade().getComposicao().consultarParaImpressao(filtro, ordem, campoOrdenacao, getEmpresaLogado() == null ? 0 : getEmpresaLogado().getCodigo());
        exportadorListaControle.exportar(evt, listaParaImpressao, filtro, null);

    }
    
     public void incluirLogInclusao() throws Exception {
        try {
            composicaoVO.setObjetoVOAntesAlteracao(new ComposicaoVO());
            composicaoVO.setNovoObj(true);
            registrarLogObjetoVO(composicaoVO, composicaoVO.getCodigo(), "COMPOSICAO", 0);
            incluirLogAlteracoesComposicaoModalidades();
        } catch (Exception e) {
            registrarLogErroObjetoVO("COMPOSICAO", composicaoVO.getCodigo(), "ERRO AO GERAR LOG DE INCLUSÃO DE COMPOSICAO", this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
        composicaoVO.setNovoObj(new Boolean(false));
        composicaoVO.registrarObjetoVOAntesDaAlteracao();
        composicaoVO.registrarModalidadesAntesDaAlteracao();
    }
    
    public void incluirLogExclusao() throws Exception {
        try {
            composicaoVO.setObjetoVOAntesAlteracao(new ComposicaoVO());
            composicaoVO.setNovoObj(true);
            registrarLogExclusaoTodosDadosObjetoVO(composicaoVO, composicaoVO.getCodigo(), "COMPOSICAO", 0);
        } catch (Exception e) {
            registrarLogErroObjetoVO("COMPOSICAO", composicaoVO.getCodigo(), "ERRO AO GERAR LOG DE EXCLUSÃO DE COMPOSICAO ", this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
    }

    /**
     * Inclui o log de alteração de categoria
     * @throws Exception
     */
    public void incluirLogAlteracao() throws Exception {
        try {
            registrarLogObjetoVO(composicaoVO, composicaoVO.getCodigo(), "COMPOSICAO", 0);
            incluirLogAlteracoesComposicaoModalidades();
        } catch (Exception e) {
            registrarLogErroObjetoVO("COMPOSICAO", composicaoVO.getCodigo(), "ERRO AO GERAR LOG DE ALTERAÇÃO DE COMPOSICAO ", this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
        composicaoVO.registrarObjetoVOAntesDaAlteracao();
        composicaoVO.registrarModalidadesAntesDaAlteracao();
    }
    
     public void realizarConsultaLogObjetoSelecionado() {
        LoginControle loginControle = (LoginControle) context().getExternalContext().getSessionMap().get("LoginControle");
        String nomeClasse = composicaoVO.getClass().getSimpleName();
        nomeClasse = nomeClasse.substring(0, nomeClasse.length() - 2);
        loginControle.setNomeClasse(loginControle.getNomeEntidadeCamposLog("prt_" + nomeClasse + "_tituloForm"));
        loginControle.consultarLogObjetoSelecionado(nomeClasse.toUpperCase(), composicaoVO.getCodigo(), 0);
    }
    public void realizarConsultaLogObjetoGeral() {
       composicaoVO = new ComposicaoVO();
       realizarConsultaLogObjetoSelecionado();
    }
    
    private void incluirLogAlteracoesComposicaoModalidades() throws Exception {
        for(ComposicaoModalidadeVO atual : composicaoVO.getComposicaoModalidadeVOs()){
            boolean nova = true;
            for(ComposicaoModalidadeVO anterior : composicaoVO.getComposicaoModalidadeVOsAntesAlteracao()){
                if(anterior.getCodigo().equals(atual.getCodigo())){
                    if(!anterior.getModalidade().getCodigo().equals(atual.getModalidade().getCodigo()) 
                            || Uteis.arredondarForcando2CasasDecimais(anterior.getValorMensalComposicao()) != Uteis.arredondarForcando2CasasDecimais(atual.getValorMensalComposicao()) || anterior.getNrVezes() != atual.getNrVezes()){
                        incluirLogAlteracaoComposicaoModalidade(anterior ,atual);
                    }
                    nova = false;
                    break;
                }
            }
            if(nova){
                incluirLogInclusaoComposicaoModalidade(atual);
            }
        }
        for(ComposicaoModalidadeVO anterior : composicaoVO.getComposicaoModalidadeVOsAntesAlteracao()){
            boolean excluida = true;
            for(ComposicaoModalidadeVO atual : composicaoVO.getComposicaoModalidadeVOs()){
                 if(anterior.getCodigo().equals(atual.getCodigo())){
                     excluida = false;
                     break;
                 }
            }
            if(excluida){
                incluirLogExclusaoComposicaoModalidade(anterior);
            }
        }
    }

    private void incluirLogAlteracaoComposicaoModalidade(ComposicaoModalidadeVO anterior, ComposicaoModalidadeVO atual) throws Exception {
         try{
            LogVO logVO = new LogVO();
            logVO.setOperacao("ALTERAÇÃO");
            logVO.setChavePrimaria(String.valueOf(composicaoVO.getCodigo()));
            logVO.setNomeEntidade("COMPOSICAO");
            logVO.setNomeEntidadeDescricao("Pacote - modalidade");
            logVO.setResponsavelAlteracao(getUsuarioLogado().getNome());
            logVO.setUserOAMD(getUsuarioLogado().getUserOamd());
            logVO.setNomeCampo("Campo(s)");
            logVO.setDataAlteracao(negocio.comuns.utilitarias.Calendario.hoje());
            logVO.setValorCampoAnterior("Modalidade: "+anterior.getModalidade().getCodigo() +"\n  NumeroVezes= "+anterior.getNrVezes()+"\n  Valor Mensal= "+anterior.getValorMensalComposicao());
            logVO.setValorCampoAlterado("Modalidade: "+atual.getModalidade().getCodigo() +"\n  NumeroVezes= "+atual.getNrVezes()+"\n  Valor Mensal= "+atual.getValorMensalComposicao());

            if (logVO != null) {
                logVO.setPessoa(0);
                getFacade().getLog().incluirSemCommit(logVO);
            }
        } catch (Exception e) {
            registrarLogErroObjetoVO("COMPOSICAO", composicaoVO.getCodigo(), "ERRO AO GERAR LOG DE ALTERAÇÃO DE MODALIDADE", this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
    }

    private void incluirLogInclusaoComposicaoModalidade(ComposicaoModalidadeVO atual) throws Exception {
        try{
            LogVO logVO = new LogVO();
            logVO.setOperacao("INCLUSÃO");
            logVO.setChavePrimaria(String.valueOf(composicaoVO.getCodigo()));
            logVO.setNomeEntidade("COMPOSICAO");
            logVO.setNomeEntidadeDescricao("Pacote - modalidade");
            logVO.setResponsavelAlteracao(getUsuarioLogado().getNome());
            logVO.setUserOAMD(getUsuarioLogado().getUserOamd());
            logVO.setNomeCampo("Campo(s)");
            logVO.setDataAlteracao(negocio.comuns.utilitarias.Calendario.hoje());
            logVO.setValorCampoAnterior("");
            logVO.setValorCampoAlterado("Modalidade: "+atual.getModalidade().getCodigo() +"\n  NumeroVezes= "+atual.getNrVezes()+"\n  Valor Mensal= "+atual.getValorMensalComposicao());

            if (logVO != null) {
                logVO.setPessoa(0);
                getFacade().getLog().incluirSemCommit(logVO);
            }
        } catch (Exception e) {
            registrarLogErroObjetoVO("COMPOSICAO", composicaoVO.getCodigo(), "ERRO AO GERAR LOG DE INCLUSÃO DE MODALIDADE", this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
    }

    private void incluirLogExclusaoComposicaoModalidade(ComposicaoModalidadeVO anterior) throws Exception {
        try{
            LogVO logVO = new LogVO();
            logVO.setOperacao("EXCLUSÃO");
            logVO.setChavePrimaria(String.valueOf(composicaoVO.getCodigo()));
            logVO.setNomeEntidade("COMPOSICAO");
            logVO.setNomeEntidadeDescricao("Pacote - modalidade");
            logVO.setResponsavelAlteracao(getUsuarioLogado().getNome());
            logVO.setUserOAMD(getUsuarioLogado().getUserOamd());
            logVO.setNomeCampo("Campo(s)");
            logVO.setDataAlteracao(negocio.comuns.utilitarias.Calendario.hoje());
            logVO.setValorCampoAnterior("");
            logVO.setValorCampoAlterado("Modalidade: "+anterior.getModalidade().getCodigo() +"\n  NumeroVezes= "+anterior.getNrVezes()+"\n  Valor Mensal= "+anterior.getValorMensalComposicao());

            if (logVO != null) {
                logVO.setPessoa(0);
                getFacade().getLog().incluirSemCommit(logVO);
            }
         } catch (Exception e) {
            registrarLogErroObjetoVO("COMPOSICAO", composicaoVO.getCodigo(), "ERRO AO GERAR LOG DE EXCLUSÃO DE MODALIDADE", this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
    }
}

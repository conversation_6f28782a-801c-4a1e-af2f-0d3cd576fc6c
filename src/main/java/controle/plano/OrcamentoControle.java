package controle.plano;

import br.com.pactosolucoes.enumeradores.TipoColaboradorEnum;
import controle.basico.ClienteControle;
import controle.basico.EnvioEmailContratoReciboControle;
import controle.basico.TelaClienteControle;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.plano.ModeloOrcamentoVO;
import negocio.comuns.plano.OrcamentoVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.utilitarias.Conexao;
import org.apache.commons.lang.StringUtils;
import relatorio.controle.basico.InclusaoVendaRapidaControle;

import javax.faces.model.SelectItem;
import java.util.ArrayList;
import java.util.List;

public class OrcamentoControle extends ClienteControle {

    private int consultorSelecionado = 0;
    private OrcamentoVO orcamentoVO = null;
    private int clienteSelecionado = 0;
    private int modeloOrcamentoSelecionado = 0;
    private int periodoSelecionado = 0;
    private int situacaoSelecionada = 0;
    private int tipoTurmaSelecionada = 0;
    private List<SelectItem> prospects = new ArrayList<>();
    private List<SelectItem> selectPeriodos = new ArrayList<>();
    private List<SelectItem> selectSituacoes = new ArrayList<>();
    private List<SelectItem> selectTipoTurmas = new ArrayList<>();
    private List<SelectItem> selectModeloOrcamentos = new ArrayList<>();
    private ModeloOrcamentoVO modeloOrcamentoVO = null;
    private boolean realizarImpressao = true;
    private String nomeCliente = "";
    private OrcamentoVO fakeOrcamento = null;

    public OrcamentoControle() throws Exception {
        super();
    }

    private void inicializar() throws Exception {
        montarListaSelectItemConsultor();
        montarProspects();
        montarOrcamentos();
        montarPeriodo();
        montarSituacao();
        montarFaixaEtariaIdade();
    }

    public String abrirRealizarOrcamento() throws Exception {
        modeloOrcamentoSelecionado = 0;
        clean();
        inicializar();
        return "realizarOrcamento";
    }

    public void gravarImprimindo() {
        salvar(true);
        if (realizarImpressao) {
            imprimirOrcamento();
        }
    }

    public void gravar() {
        salvar(true);
    }

    public void imprimirOrcamento() {
        try {
            setMsgAlert("");
            if (getEmpresa() == null || getEmpresa().getCodigo() == 0) {
                throw new Exception("Empresa não encontrada. Selecione a empresa ou entre novamente no sistema.");
            }
            setModeloOrcamentoVO(getFacade().getModeloOrcamento().consultarPorChavePrimaria(getFakeOrcamento().getModeloOrcamento(), Uteis.NIVELMONTARDADOS_TODOS));
            getModeloOrcamentoVO().substituirTagsTextoEmBranco(getFakeOrcamento(), getEmpresa(), Conexao.getFromSession(), false);
            setFakeOrcamento(null);
            setSucesso(true);
        } catch (Exception e) {
            montarErro(e.getMessage());
            setMsgAlert(getMensagemNotificar());
        }
    }

    public String getAbrirPDF() {
        if (getSucesso()) {
            return "abrirPopup('VisualizarContrato', 'ContratoEmBranco', 730, 545);";
        } else {
            return "";
        }
    }

    public void salvar(boolean limpar) {
        realizarImpressao = true;
        setMsgAlert("");
        try {
            prepararAntesIncluir();
            validarDados();
            if (getOrcamentoVO().isNovoObj().booleanValue()) {
                getFacade().getOrcamento().incluir(getOrcamentoVO());
            } else {
                getFacade().getOrcamento().alterar(getOrcamentoVO());
            }

            if (limpar) {
                clean();
            }
            montarSucesso("msg_dados_gravados");
            setMsgAlert(getMensagemNotificar());
        } catch (Exception e) {
            realizarImpressao = false;
            montarErro(e.getMessage());
            setMsgAlert(getMensagemNotificar());
        }
    }

    private void clean() {
        getFakeOrcamento().setPeriodo(getOrcamentoVO().getPeriodo());
        getFakeOrcamento().setCliente(getOrcamentoVO().getCliente());
        getFakeOrcamento().setModeloOrcamento(getOrcamentoVO().getModeloOrcamento());
        getFakeOrcamento().setTipoTurma(getOrcamentoVO().getTipoTurma());
        setOrcamentoVO(null);
        clienteSelecionado = 0;
        consultorSelecionado = 0;
        periodoSelecionado = 0;
        situacaoSelecionada = 0;
        modeloOrcamentoSelecionado = 0;
        tipoTurmaSelecionada = 0;
    }

    private void prepararAntesIncluir() {
        getOrcamentoVO().setCliente(clienteSelecionado);
        getOrcamentoVO().setConsultor(consultorSelecionado);
        getOrcamentoVO().setPeriodo(periodoSelecionado);
        getOrcamentoVO().setSituacao(situacaoSelecionada);
        getOrcamentoVO().setModeloOrcamento(modeloOrcamentoSelecionado);
        getOrcamentoVO().setTipoTurma(tipoTurmaSelecionada);
    }

    private void validarDados() throws Exception {

        if (getOrcamentoVO().getConsultor() == 0) {
            throw new Exception("Informe o consultor.");
        }

        if (getOrcamentoVO().getCliente() == 0) {
            throw new Exception("Informe o prospecto.");
        }

        if (getOrcamentoVO().getParaQuem() != 0) {
            if (StringUtils.isBlank(getOrcamentoVO().getNomeProspecto())) {
                throw new Exception("Informe nome do prospecto.");
            }
        }

        if (getOrcamentoVO().getParaQuem() != 0) {
            if (getOrcamentoVO().getIdade() <= 0) {
                throw new Exception("Informe a idade do prospecto.");
            }
        }

        if (getOrcamentoVO().getModeloOrcamento() == 0) {
            throw new Exception("Informe o orçamento.");
        }

        if (getOrcamentoVO().getPeriodo() == 0) {
            throw new Exception("Informe o periodo.");
        }

        if (getOrcamentoVO().getSituacao() == 0) {
            throw new Exception("Informe a situação.");
        }

        if (getOrcamentoVO().getTipoTurma() == 0) {
            throw new Exception("Informe o tipo de turma.");
        }

    }

    public void montarListaSelectItemConsultor() {
        List resultadoConsulta = consultarColaboradorPorSituacao();
        preencherListaSelectConsultor(resultadoConsulta);
    }

    public String preencherCliente() throws Exception {
        return preencherCliente(null);
    }

    public String preencherCliente(Integer codCliente) throws Exception {
        String tela = "";
        try {
            if (codCliente == null) {
                TelaClienteControle Telacliente = (TelaClienteControle) request().getAttribute("TelaClienteControle");
                codCliente = Telacliente.getCliente().getCodigo();
            }
            ClienteVO cli = getFacade().getCliente().consultarPorChavePrimaria(codCliente, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            clienteSelecionado = cli.getCodigo();
            setNomeCliente(cli.getPessoa().getNomeAbreviado());
            inicializar();
            tela = "realizarOrcamento";
        } catch (Exception e) {
            e.getStackTrace();
        }
        return tela;
    }

    public List<ClienteVO> executarAutocompleteConsultaCliente(Object suggest) {
        String pref = (String) suggest;
        ArrayList<ClienteVO> result = null;
        try {
            if (!pref.equals("%")) {
                result = (ArrayList<ClienteVO>) getFacade().getCliente().consultarPorNomePessoa(pref, getEmpresaLogado().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS, 30);
            }
        } catch (Exception e) {
            result = (new ArrayList<ClienteVO>());
            montarErro(e.getMessage());
        }
        return result;
    }

    public void selecionarClienteSuggestionBox() {
        try {
            ClienteVO cliente = (ClienteVO) request().getAttribute("result");
            clienteSelecionado = getFacade().getCliente().consultarPorChavePrimaria(cliente.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS).getCodigo();
        } catch (Exception e) {
            montarErro(e);
        }
    }

    public List consultarColaboradorPorSituacao() {
        try {
            if (getEmpresa() == null) {
                if (getUsuarioLogado().getAdministrador()) {
                    return new ArrayList();
                }
                setEmpresa(getEmpresaLogado());
            }

            return getFacade().getColaborador().consultarPorTipoColaboradorAtivo(TipoColaboradorEnum.CONSULTOR, "AT",
                    getEmpresa().getCodigo(), false, Uteis.NIVELMONTARDADOS_MINIMOS);
        } catch (Exception ex) {
            Uteis.logar(ex, InclusaoVendaRapidaControle.class);
            montarErro(ex);
            return new ArrayList<ColaboradorVO>();
        }
    }

    public void montarProspects() {
        List<SelectItem> prospects = new ArrayList<>();

        prospects.add(new SelectItem(0, "Prospecto"));
        prospects.add(new SelectItem(1, "Filho(a)"));
        prospects.add(new SelectItem(2, "Pai"));
        prospects.add(new SelectItem(3, "Mãe"));
        prospects.add(new SelectItem(4, "Outro"));

        setProspects(prospects);
    }

    public void montarPeriodo() {
        List<SelectItem> periodo = new ArrayList<>();

        periodo.add(new SelectItem(0, "Informe o período"));
        periodo.add(new SelectItem(4, "Todos os períodos"));
        periodo.add(new SelectItem(1, "Manhã"));
        periodo.add(new SelectItem(2, "Tarde"));
        periodo.add(new SelectItem(3, "Noite"));

        setSelectPeriodos(periodo);
    }

    public void montarSituacao() {
        List<SelectItem> situacao = new ArrayList<>();

        situacao.add(new SelectItem(0, "Informe a situação"));
        situacao.add(new SelectItem(1, "Andamento"));
        situacao.add(new SelectItem(2, "Cancelado"));
        situacao.add(new SelectItem(3, "Finalizado"));

        setSelectSituacoes(situacao);
    }

    public void montarFaixaEtariaIdade() {
        List<SelectItem> tiposTurma = new ArrayList<>();

        tiposTurma.add(new SelectItem(0, "Informe tipo de turma"));
        tiposTurma.add(new SelectItem(1, "Todas"));
        tiposTurma.add(new SelectItem(2, "Infantil"));
        tiposTurma.add(new SelectItem(3, "Adulto"));

        setSelectTipoTurmas(tiposTurma);
    }

    public void montarOrcamentos() throws Exception {
        try {
            List<SelectItem> orcamentos = new ArrayList<>();
            List<ModeloOrcamentoVO> listModelosOrcamento = getFacade().getModeloOrcamento().consultarTodosAtivos();
            orcamentos.add(new SelectItem(0, ""));
            for (ModeloOrcamentoVO modelo : listModelosOrcamento) {
                orcamentos.add(new SelectItem(modelo.getCodigo(), modelo.getDescricao()));
            }

            setSelectModeloOrcamentos(orcamentos);
        } catch (Exception e) {
            e.getStackTrace();
        }
    }

    public void prepararEnvioNovoOrcamentoPorEmail() throws Exception {
        setMsgAlert("");
        setMensagemDetalhada("");
        try {
            salvar(false);
            ClienteVO cliente = getFacade().getCliente().consultarPorChavePrimaria(getOrcamentoVO().getCliente(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);

            EnvioEmailContratoReciboControle envioEmailContratoReciboControle = (EnvioEmailContratoReciboControle) getControlador(EnvioEmailContratoReciboControle.class.getSimpleName());
            envioEmailContratoReciboControle.setOrcamentoVO(getOrcamentoVO());
            envioEmailContratoReciboControle.prepararListaEmail(cliente.getPessoa().getCodigo(), true);
            setMsgAlert("Richfaces.showModalPanel('modalEnviarOrcamentoEmail');");
            clean();
        } catch (Exception e) {
            montarErro(e);
        }
    }


    public int getConsultorSelecionado() {
        return consultorSelecionado;
    }

    public void setConsultorSelecionado(int consultorSelecionado) {
        this.consultorSelecionado = consultorSelecionado;
    }

    public List<SelectItem> getProspects() {
        return prospects;
    }

    public void setProspects(List<SelectItem> prospects) {
        this.prospects = prospects;
    }

    public OrcamentoVO getOrcamentoVO() {
        if (orcamentoVO == null) {
            orcamentoVO = new OrcamentoVO();
        }
        return orcamentoVO;
    }

    public void setOrcamentoVO(OrcamentoVO orcamentoVO) {
        this.orcamentoVO = orcamentoVO;
    }

    public int getClienteSelecionado() {
        return clienteSelecionado;
    }

    public void setClienteSelecionado(int clienteSelecionado) {
        this.clienteSelecionado = clienteSelecionado;
    }

    public List<SelectItem> getSelectModeloOrcamentos() {
        return selectModeloOrcamentos;
    }

    public void setSelectModeloOrcamentos(List<SelectItem> selectModeloOrcamentos) {
        this.selectModeloOrcamentos = selectModeloOrcamentos;
    }

    public int getModeloOrcamentoSelecionado() {
        return modeloOrcamentoSelecionado;
    }

    public void setModeloOrcamentoSelecionado(int modeloOrcamentoSelecionado) {
        this.modeloOrcamentoSelecionado = modeloOrcamentoSelecionado;
    }

    public int getPeriodoSelecionado() {
        return periodoSelecionado;
    }

    public void setPeriodoSelecionado(int periodoSelecionado) {
        this.periodoSelecionado = periodoSelecionado;
    }

    public int getSituacaoSelecionada() {
        return situacaoSelecionada;
    }

    public void setSituacaoSelecionada(int situacaoSelecionada) {
        this.situacaoSelecionada = situacaoSelecionada;
    }

    public List<SelectItem> getSelectPeriodos() {
        return selectPeriodos;
    }

    public void setSelectPeriodos(List<SelectItem> selectPeriodos) {
        this.selectPeriodos = selectPeriodos;
    }

    public List<SelectItem> getSelectSituacoes() {
        return selectSituacoes;
    }

    public void setSelectSituacoes(List<SelectItem> selectSituacoes) {
        this.selectSituacoes = selectSituacoes;
    }

    public ModeloOrcamentoVO getModeloOrcamentoVO() {
        if (modeloOrcamentoVO == null) {
            modeloOrcamentoVO = new ModeloOrcamentoVO();
        }
        return modeloOrcamentoVO;
    }

    public void setModeloOrcamentoVO(ModeloOrcamentoVO modeloOrcamentoVO) {
        this.modeloOrcamentoVO = modeloOrcamentoVO;
    }

    public boolean isRealizarImpressao() {
        return realizarImpressao;
    }

    public void setRealizarImpressao(boolean realizarImpressao) {
        this.realizarImpressao = realizarImpressao;
    }

    public String getNomeCliente() {
        return nomeCliente;
    }

    public void setNomeCliente(String nomeCliente) {
        this.nomeCliente = nomeCliente;
    }

    public OrcamentoVO getFakeOrcamento() {
        if (fakeOrcamento == null) {
            fakeOrcamento = new OrcamentoVO();
        }
        return fakeOrcamento;
    }

    public void setFakeOrcamento(OrcamentoVO fakeOrcamento) {
        this.fakeOrcamento = fakeOrcamento;
    }

    public int getTipoTurmaSelecionada() {
        return tipoTurmaSelecionada;
    }

    public void setTipoTurmaSelecionada(int tipoTurmaSelecionada) {
        this.tipoTurmaSelecionada = tipoTurmaSelecionada;
    }

    public List<SelectItem> getSelectTipoTurmas() {
        return selectTipoTurmas;
    }

    public void setSelectTipoTurmas(List<SelectItem> selectTipoTurmas) {
        this.selectTipoTurmas = selectTipoTurmas;
    }
}

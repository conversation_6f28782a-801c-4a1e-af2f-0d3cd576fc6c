package controle.plano;

import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.estrutura.exportador.controle.ExportadorListaControle;
import controle.arquitetura.SuperControle;
import controle.arquitetura.security.LoginControle;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.plano.TipoModalidadeVO;
import negocio.comuns.utilitarias.ControleConsulta;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.plano.TipoModalidade;

import javax.faces.event.ActionEvent;
import javax.faces.model.SelectItem;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

/**
 * Classe responsável por implementar a interação entre os componentes JSF das páginas 
 * tipoModalidadeForm.jsp tipoModalidadeCons.jsp) com as funcionalidades da classe <code>TipoModalidade</code>.
 * Implemtação da camada controle (Backing Bean).
 * @see SuperControle
 * @see TipoModalidade
 * @see TipoModalidadeVO
 */
public class TipoModalidadeControle extends SuperControle {

    private static final String VERIFICA_CHAVE_UNICIDADE = "duplicar valor da chave viola a";
    private static final String VERIFICA_CHAVE_ALREADY = "already exists";

    private TipoModalidadeVO tipoModalidadeVO;
    protected List listaSelectItemTipoModalidade;
    private EmpresaVO empresaVO;

    /**
     * Interface <code>TipoModalidadeInterfaceFacade</code> responsável pela interconexão da camada de controle com a camada de negócio.
     * Criando uma independência da camada de controle com relação a tenologia de persistência dos dados (DesignPatter: Façade).
     */

    public TipoModalidadeControle() throws Exception {
        obterUsuarioLogado();
        this.setEmpresaVO(getEmpresaLogado());
        inicializarFacades();
        setControleConsulta(new ControleConsulta());
        limparMsg();
        setMensagemID("");
    }

    /**
     * Rotina responsável por disponibilizar um novo objeto da classe <code>TipoModalidade</code>
     * para edição pelo usuário da aplicação.
     */
    public String novo() {
        setTipoModalidadeVO(new TipoModalidadeVO());
        limparMsg();
        return "editar";
    }
    public void novoSemRedirect() throws Exception{
        novo();
    }

    /**
     * Rotina responsável por disponibilizar os dados de um objeto da classe <code>TipoModalidade</code> para alteração.
     * O objeto desta classe é disponibilizado na session da página (request) para que o JSP correspondente possa disponibilizá-lo para edição.
     */
    public String editar() {
        Integer codigoConsulta = Integer.parseInt(request().getParameter("chavePrimaria"));
        try {
            TipoModalidadeVO obj = getFacade().getTipoModalidade().consultarPorChavePrimaria(codigoConsulta, Uteis.NIVELMONTARDADOS_TODOS);
            setTipoModalidadeVO(obj);
            getTipoModalidadeVO().setNovoObj(false);
            getTipoModalidadeVO().registrarObjetoVOAntesDaAlteracao();
            limparMsg();
        } catch (Exception e) {
            setErro(true);
            setSucesso(false);
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
        return "editar";
    }

    /**
     * Rotina responsável por gravar no BD os dados editados de um novo objeto da classe <code>TipoModalidade</code>.
     * Caso o objeto seja novo (ainda não gravado no BD) é acionado a operação <code>incluir()</code>. Caso contrário é acionado o <code>alterar()</code>.
     * Se houver alguma inconsistência o objeto não é gravado, sendo re-apresentado para o usuário juntamente com uma mensagem de erro.
     */
    public String gravar() throws Exception {
        try {

            if (tipoModalidadeVO.isNovoObj().booleanValue()) {
                getFacade().getTipoModalidade().incluir(tipoModalidadeVO);
                incluirLogInclusao();
            } else {
                getFacade().getTipoModalidade().alterar(tipoModalidadeVO);
                incluirLogAlteracao();
            }
            setMensagemID("msg_dados_gravados");
            setSucesso(true);
            setErro(false);
            return "editar";
        } catch (Exception e) {
            if (isIdentificadorDuplicado(e)) {
                TipoModalidadeVO tipoModalidade = getFacade().getTipoModalidade().consultarPorIdentificador(tipoModalidadeVO.getIdentificador(), Uteis.NIVELMONTARDADOS_TODOS);
                setMensagemDetalhada("msg_erro", "O Identificador " +tipoModalidadeVO.getIdentificador() + " já está cadastrado para o tipo de modalidade " + tipoModalidade.getNome().toUpperCase());
            } else {
                setMensagemDetalhada("msg_erro", e.getMessage());
            }
            setSucesso(false);
            setErro(true);
            return "editar";
        }
    }

    private boolean isIdentificadorDuplicado(Exception e) {
        return e.getMessage().toLowerCase().contains(VERIFICA_CHAVE_UNICIDADE) || e.getMessage().toLowerCase().contains(VERIFICA_CHAVE_ALREADY);
    }

    /**
     * Rotina responsavel por executar as consultas disponiveis no JSP TipoModalidadeCons.jsp.
     * Define o tipo de consulta a ser executada, por meio de ComboBox denominado campoConsulta, disponivel neste mesmo JSP.
     * Como resultado, disponibiliza um List com os objetos selecionados na sessao da pagina.
     */
    public String consultar() {
        try {
            super.consultar();
            List objs = new ArrayList();
            List lista = new ArrayList();
            if (getControleConsulta().getCampoConsulta().equals("codigo")) {
                if (getControleConsulta().getValorConsulta().equals("")) {
                    getControleConsulta().setValorConsulta("0");
                }
                int valorInt = Integer.parseInt(getControleConsulta().getValorConsulta());
                objs = getFacade().getTipoModalidade().consultarPorCodigo(new Integer(valorInt), true, Uteis.NIVELMONTARDADOS_TODOS);
            }
            if (getControleConsulta().getCampoConsulta().equals("nome")) {
                objs = getFacade().getTipoModalidade().consultarPorNome(getControleConsulta().getValorConsulta(), true, Uteis.NIVELMONTARDADOS_TODOS);
            }

            objs = ControleConsulta.obterSubListPaginaApresentar(objs, controleConsulta);
            definirVisibilidadeLinksNavegacao(controleConsulta.getPaginaAtual(), controleConsulta.getNrTotalPaginas());
            setListaConsulta(objs);
            limparMsg();
            return "consultar";
        } catch (Exception e) {
            setListaConsulta(new ArrayList());
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
            return "consultar";
        }
    }

    /**
     * Operação responsável por processar a exclusão um objeto da classe <code>TipoModalidadeVO</code>
     * Após a exclusão ela automaticamente aciona a rotina para uma nova inclusão.
     */
    public String excluir() {
        try {
            if (!verificaSePodeRemoverTipoModalidade(tipoModalidadeVO.getIdentificador())) {
                getFacade().getTipoModalidade().excluir(tipoModalidadeVO);
                incluirLogExclusao();
                setTipoModalidadeVO(new TipoModalidadeVO());
                setMensagemID("msg_dados_excluidos");
                setSucesso(true);
                setErro(false);
                return "editar";
            } else {
                //setMensagemID("msg_err_excluir_tipo_modalidade");
                setSucesso(false);
                setMensagemDetalhada("msg_err_excluir_tipo_modalidade","Existe modalidade(s) com este tipo cadastrado!");
                setErro(true);
                return "editar";
            }
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
            return "editar";
        }
    }

    public Boolean verificaSePodeRemoverTipoModalidade(Integer tipo) throws Exception {
        return getFacade().getModalidade().consultarPorTipo(tipo);
    }

    public void irPaginaInicial() throws Exception {
        controleConsulta.setPaginaAtual(1);
        this.consultar();
    }

    public void irPaginaAnterior() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getPaginaAtual() - 1);
        this.consultar();
    }

    public void irPaginaPosterior() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getPaginaAtual() + 1);
        this.consultar();
    }

    public void irPaginaFinal() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getNrTotalPaginas());
        this.consultar();
    }

    /**
     * Método responsável por gerar uma lista de objetos do tipo <code>SelectItem</code> para preencher
     * o comboBox relativo ao atributo <code>TipoModalidade</code>.
     */
    public void montarListaSelectItemTipoModalidade(String prm) throws Exception {
        List resultadoConsulta = consultarTipoModalidadePorNome(prm);
        Iterator i = resultadoConsulta.iterator();
        List objs = new ArrayList();
        objs.add(new SelectItem(new Integer(0), ""));
        while (i.hasNext()) {
            TipoModalidadeVO obj = (TipoModalidadeVO)i.next();
            objs.add(new SelectItem(obj.getCodigo(), obj.getNome().toString()));
        }
        setListaSelectItemTipoModalidade(objs);
    }

    /**
     * Método responsável por atualizar o ComboBox relativo ao atributo <code>TipoModalidade</code>.
     * Buscando todos os objetos correspondentes a entidade <code>TipoModalidade</code>. Esta rotina não recebe parâmetros para filtragem de dados, isto é
     * importante para a inicialização dos dados da tela para o acionamento por meio requisições Ajax.
    */
    public void montarListaSelectItemTipoModalidade() {
        try {
            montarListaSelectItemTipoModalidade("");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * Método responsável por consultar dados da entidade <code><code> e montar o atributo <code>nome</code>
     * Este atributo é uma lista (<code>List</code>) utilizada para definir os valores a serem apresentados no ComboBox correspondente
    */
    public List consultarTipoModalidadePorNome(String nomePrm) throws Exception {
        List lista = getFacade().getTipoModalidade().consultarPorNome(nomePrm, false, Uteis.NIVELMONTARDADOS_TODOS);
        return lista;
    }

    public void realizarConsultaLogObjetoSelecionado() {
        LoginControle loginControle = (LoginControle) context().getExternalContext().getSessionMap().get("LoginControle");
        String nomeClasse = tipoModalidadeVO.getClass().getSimpleName();
        nomeClasse = nomeClasse.substring(0, nomeClasse.length() - 2);
        loginControle.setNomeClasse(loginControle.getNomeEntidadeCamposLog("prt_" + nomeClasse + "_tituloForm"));
        loginControle.consultarLogObjetoSelecionado(nomeClasse.toUpperCase(), tipoModalidadeVO.getCodigo(), 0);
    }
    
    public void realizarConsultaLogObjetoGeral() {
        tipoModalidadeVO = new TipoModalidadeVO();
        realizarConsultaLogObjetoSelecionado();
    }

    /**
     * Rotina responsável por preencher a combo de consulta da telas.
     */
    public List getTipoConsultaCombo() {
        List itens = new ArrayList();
        itens.add(new SelectItem("nome", "Nome"));
        itens.add(new SelectItem("codigo", "Código"));

        return itens;
    }

    public List getTipoConsultaComboProduto() {
        List itens = new ArrayList();
        // itens.add(new SelectItem("codigo", "Código"));
        itens.add(new SelectItem("nome", "Nome"));
        return itens;
    }

    /**
     * Rotina responsável por organizar a paginação entre as páginas resultantes de uma consulta.
     */
    public String inicializarConsultar() {
        setPaginaAtualDeTodas("0/0");
        setListaConsulta(new ArrayList());
        definirVisibilidadeLinksNavegacao(0, 0);
        limparMsg();
        return "consultar";
    }

    /**
     * Operação que inicializa as Interfaces Façades com os respectivos objetos de
     * persistência dos dados no banco de dados. 
     */
    protected boolean inicializarFacades() {
        try {
            super.inicializarFacades();
            return true;
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro_conectarBD", e.getMessage());
            return false;
        }
    }

    public List getListaSelectItemTipoModalidade() {
        return (listaSelectItemTipoModalidade);
    }

    public void setListaSelectItemTipoModalidade(List listaSelectItemTipoModalidade) {
        this.listaSelectItemTipoModalidade = listaSelectItemTipoModalidade;
    }

    public TipoModalidadeVO getTipoModalidadeVO() {
        return tipoModalidadeVO;
    }

    public void setTipoModalidadeVO(TipoModalidadeVO tipoModalidadeVO) {
        this.tipoModalidadeVO = tipoModalidadeVO;
    }


    public void incluirLogInclusao() throws Exception {
        try {
            tipoModalidadeVO.setObjetoVOAntesAlteracao(new TipoModalidadeVO());
            tipoModalidadeVO.setNovoObj(true);
            registrarLogObjetoVO(tipoModalidadeVO, tipoModalidadeVO.getCodigo(), "TIPOMODALIDADE", 0);
        } catch (Exception e) {
            registrarLogErroObjetoVO("TIPOMODALIDADE", tipoModalidadeVO.getCodigo(), "ERRO AO GERAR LOG DE INCLUSÃO DE tipo MODALIDADE", this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
        tipoModalidadeVO.setNovoObj(Boolean.FALSE);
        tipoModalidadeVO.registrarObjetoVOAntesDaAlteracao();
    }
    
    public void incluirLogExclusao() throws Exception {
        try {
            tipoModalidadeVO.setObjetoVOAntesAlteracao(new TipoModalidadeVO());
            tipoModalidadeVO.setNovoObj(true);
            registrarLogExclusaoTodosDadosObjetoVO(tipoModalidadeVO, tipoModalidadeVO.getCodigo(), "TIPOMODALIDADE", 0);
        } catch (Exception e) {
            registrarLogErroObjetoVO("TIPOMODALIDADE", tipoModalidadeVO.getCodigo(), "ERRO AO GERAR LOG DE EXCLUSÃO DE TIPOMODALIDADE ", this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
    }

    /**
     * Inclui o log de alteração de categoria
     * @throws Exception
     */
    public void incluirLogAlteracao() throws Exception {
        try {
            registrarLogObjetoVO(tipoModalidadeVO, tipoModalidadeVO.getCodigo(), "TIPOMODALIDADE", 0);
            tipoModalidadeVO.registrarObjetoVOAntesDaAlteracao();
        } catch (Exception e) {
            registrarLogErroObjetoVO("TIPOMODALIDADE", tipoModalidadeVO.getCodigo(), "ERRO AO GERAR LOG DE ALTERAÇÃO DE TIPOMODALIDADE ", this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
        tipoModalidadeVO.registrarObjetoVOAntesDaAlteracao();
    }
    public EmpresaVO getEmpresaVO() {
        return empresaVO;
    }

    public void setEmpresaVO(EmpresaVO empresaVO) {
        this.empresaVO = empresaVO;
    }

    public void exportar(ActionEvent evt) throws Exception {
        ExportadorListaControle exportadorListaControle = (ExportadorListaControle) JSFUtilities.getFromRequest(ExportadorListaControle.class.getSimpleName());
        String paramsTableFiltrada = context().getExternalContext().getRequestParameterMap().get("paramsTableFiltrada");

        String[] split = paramsTableFiltrada.split(",");
        String campoOrdenacao = split[0].replace("[", "");
        String ordem = split[1];
        String filtro = split[2].replace("''", "");
        filtro = filtro.replace("]", "");
        List listaParaImpressao = getFacade().getTipoModalidade().consultarParaImpressao(filtro, ordem, campoOrdenacao, getEmpresaLogado() == null ? 0 : getEmpresaLogado().getCodigo());
        exportadorListaControle.exportar(evt, listaParaImpressao, filtro, null);

    }
}

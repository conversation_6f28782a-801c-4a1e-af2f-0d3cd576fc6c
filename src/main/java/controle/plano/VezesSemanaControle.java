package controle.plano;

import negocio.comuns.utilitarias.SelectItemOrdemValor;
import negocio.facade.jdbc.plano.VezesSemana;
import negocio.comuns.utilitarias.*;
import negocio.comuns.plano.*;
import javax.faces.model.SelectItem;
import java.util.List;
import java.util.ArrayList;
import controle.arquitetura.SuperControle;
import java.util.Collections;
import java.util.Enumeration;
import java.util.Hashtable;

/**
 * Classe responsável por implementar a interação entre os componentes JSF das páginas 
 * vezesSemanaForm.jsp vezesSemanaCons.jsp) com as funcionalidades da classe <code>VezesSemana</code>.
 * Implemtação da camada controle (Backing Bean).
 * @see SuperControle
 * @see VezesSemana
 * @see VezesSemanaVO
 */
public class VezesSemanaControle extends SuperControle {

    private VezesSemanaVO vezesSemanaVO;
    /**
     * Interface <code>VezesSemanaInterfaceFacade</code> responsável pela interconexão da camada de controle com a camada de negócio.
     * Criando uma independência da camada de controle com relação a tenologia de persistência dos dados (DesignPatter: Façade).
     */

    public VezesSemanaControle() throws Exception {
        obterUsuarioLogado();
        inicializarFacades();
        setControleConsulta(new ControleConsulta());
        setMensagemID("msg_entre_prmconsulta");
    }

    /**
     * Rotina responsável por disponibilizar um novo objeto da classe <code>VezesSemana</code>
     * para edição pelo usuário da aplicação.
     */
    public String novo() {
        setVezesSemanaVO(new VezesSemanaVO());
        setMensagemID("msg_entre_dados");
        setSucesso(false);
        setErro(false);
        return "editar";
    }

    /**
     * Rotina responsável por disponibilizar os dados de um objeto da classe <code>VezesSemana</code> para alteração.
     * O objeto desta classe é disponibilizado na session da página (request) para que o JSP correspondente possa disponibilizá-lo para edição.
     */
    public String editar() {
        VezesSemanaVO obj = (VezesSemanaVO) context().getExternalContext().getRequestMap().get("vezesSemana");
        obj.setNovoObj(new Boolean(false));
        setVezesSemanaVO(obj);
        setSucesso(false);
        setErro(false);
        setMensagemID("msg_dados_editar");
        return "editar";
    }

    /**
     * Rotina responsável por gravar no BD os dados editados de um novo objeto da classe <code>VezesSemana</code>.
     * Caso o objeto seja novo (ainda não gravado no BD) é acionado a operação <code>incluir()</code>. Caso contrário é acionado o <code>alterar()</code>.
     * Se houver alguma inconsistência o objeto não é gravado, sendo re-apresentado para o usuário juntamente com uma mensagem de erro.
     */
    public String gravar() {
        try {
            if (vezesSemanaVO.isNovoObj().booleanValue()) {
                getFacade().getVezesSemana().incluir(vezesSemanaVO);
            } else {
                getFacade().getVezesSemana().alterar(vezesSemanaVO);
            }
            setMensagemID("msg_dados_gravados");
            setSucesso(true);
            setErro(false);
            return "editar";
        } catch (Exception e) {
            setSucesso(false);
            setErro(true);
            setMensagemDetalhada("msg_erro", e.getMessage());
            return "editar";
        }
    }

    /**
     * Rotina responsavel por executar as consultas disponiveis no JSP VezesSemanaCons.jsp.
     * Define o tipo de consulta a ser executada, por meio de ComboBox denominado campoConsulta, disponivel neste mesmo JSP.
     * Como resultado, disponibiliza um List com os objetos selecionados na sessao da pagina.
     */
    public String consultar() {
        try {
            super.consultar();
            List objs = new ArrayList();
            if (getControleConsulta().getCampoConsulta().equals("codigo")) {
                if (getControleConsulta().getValorConsulta().equals("")) {
                    getControleConsulta().setValorConsulta("0");
                }
                int valorInt = Integer.parseInt(getControleConsulta().getValorConsulta());
                objs = getFacade().getVezesSemana().consultarPorCodigo(new Integer(valorInt), true, Uteis.NIVELMONTARDADOS_TODOS);
            }
            if (getControleConsulta().getCampoConsulta().equals("nrVezes")) {
                if (getControleConsulta().getValorConsulta().equals("")) {
                    getControleConsulta().setValorConsulta("0");
                }
                int valorInt = Integer.parseInt(getControleConsulta().getValorConsulta());
                objs = getFacade().getVezesSemana().consultarPorNrVezes(new Integer(valorInt), true, Uteis.NIVELMONTARDADOS_TODOS);
            }
//            if (getControleConsulta().getCampoConsulta().equals("percentualDesconto")) {
//                if (getControleConsulta().getValorConsulta().equals("")) {
//                    getControleConsulta().setValorConsulta("0");
//                }
//                double valorDouble = Double.parseDouble(getControleConsulta().getValorConsulta());
//                objs = vezesSemanaFacade.consultarPorPercentualDesconto(new Double(valorDouble), true, Uteis.NIVELMONTARDADOS_TODOS);
//            }
//            if (getControleConsulta().getCampoConsulta().equals("valorEspecifico")) {
//                if (getControleConsulta().getValorConsulta().equals("")) {
//                    getControleConsulta().setValorConsulta("0");
//                }
//                double valorDouble = Double.parseDouble(getControleConsulta().getValorConsulta());
//                objs = vezesSemanaFacade.consultarPorValorEspecifico(new Double(valorDouble), true, Uteis.NIVELMONTARDADOS_TODOS);
//            }
            objs = ControleConsulta.obterSubListPaginaApresentar(objs, controleConsulta);
            definirVisibilidadeLinksNavegacao(controleConsulta.getPaginaAtual(), controleConsulta.getNrTotalPaginas());
            setListaConsulta(objs);
            setMensagemID("msg_dados_consultados");
            setSucesso(true);
            setErro(false);
            return "consultar";
        } catch (Exception e) {
            setListaConsulta(new ArrayList());
            setSucesso(false);
            setErro(true);
            setMensagemDetalhada("msg_erro", e.getMessage());
            return "consultar";
        }
    }

    /**
     * Operação responsável por processar a exclusão um objeto da classe <code>VezesSemanaVO</code>
     * Após a exclusão ela automaticamente aciona a rotina para uma nova inclusão.
     */
    public String excluir() {
        try {
            getFacade().getVezesSemana().excluir(vezesSemanaVO);
            setVezesSemanaVO(new VezesSemanaVO());
            setMensagemID("msg_dados_excluidos");
            setSucesso(true);
            setErro(false);
            return "editar";
        } catch (Exception e) {
            setSucesso(false);
            setErro(true);
            setMensagemDetalhada("msg_erro", e.getMessage());
            return "editar";
        }
    }

    public void irPaginaInicial() throws Exception {
        controleConsulta.setPaginaAtual(1);
        this.consultar();
    }

    public void irPaginaAnterior() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getPaginaAtual() - 1);
        this.consultar();
    }

    public void irPaginaPosterior() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getPaginaAtual() + 1);
        this.consultar();
    }

    public void irPaginaFinal() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getNrTotalPaginas());
        this.consultar();
    }
      /* Método responsável por inicializar List<SelectItem> de valores do 
     * ComboBox correspondente ao atributo <code>tipoValor</code>
     */
    public List getListaSelectItemTipoValor() throws Exception {
        List objs = new ArrayList();
        objs.add(new SelectItem("", ""));
        Hashtable valor = (Hashtable) Dominios.getTipoValor();
        Enumeration keys = valor.keys();
        while (keys.hasMoreElements()) {
            String value = (String) keys.nextElement();
            String label = (String) valor.get(value);
            objs.add(new SelectItem(value, label));
        }

        SelectItemOrdemValor ordenador = new SelectItemOrdemValor();
        Collections.sort((List) objs, ordenador);
        return objs;
    }

    /**
     * Rotina responsável por preencher a combo de consulta da telas.
     */
    public List getTipoConsultaCombo() {
        List itens = new ArrayList();
        itens.add(new SelectItem("codigo", "Código"));
        itens.add(new SelectItem("nrVezes", "Número de Vezes"));
//        itens.add(new SelectItem("percentualDesconto", "Percentual de Desconto"));
//        itens.add(new SelectItem("valorEspecifico", "Valor Especifico"));
        return itens;
    }

    /**
     * Rotina responsável por organizar a paginação entre as páginas resultantes de uma consulta.
     */
    public String inicializarConsultar() {
        setPaginaAtualDeTodas("0/0");
        setListaConsulta(new ArrayList());
        definirVisibilidadeLinksNavegacao(0, 0);
        setSucesso(false);
        setErro(false);
        setMensagemID("msg_entre_prmconsulta");
        return "consultar";
    }

    /**
     * Operação que inicializa as Interfaces Façades com os respectivos objetos de
     * persistência dos dados no banco de dados. 
     */
    protected boolean inicializarFacades() {
        try {
            super.inicializarFacades();
            return true;
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro_conectarBD", e.getMessage());
            return false;
        }
    }

    public VezesSemanaVO getVezesSemanaVO() {
        return vezesSemanaVO;
    }

    public void setVezesSemanaVO(VezesSemanaVO vezesSemanaVO) {
        this.vezesSemanaVO = vezesSemanaVO;
    }
}

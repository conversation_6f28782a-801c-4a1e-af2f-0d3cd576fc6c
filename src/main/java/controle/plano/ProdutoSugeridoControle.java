//package controle.plano;
//import negocio.interfaces.plano.ProdutoInterfaceFacade;
//import negocio.interfaces.plano.ModalidadeInterfaceFacade;
//import negocio.interfaces.plano.ProdutoSugeridoInterfaceFacade;
//import negocio.comuns.plano.ProdutoVO;
//import negocio.comuns.plano.ModalidadeVO;
//import java.util.Iterator;
//import negocio.comuns.utilitarias.ControleConsulta;
//import negocio.facade.jdbc.plano.Produto;
//import negocio.facade.jdbc.plano.Modalidade;
//import negocio.facade.jdbc.plano.ProdutoSugerido;
//import negocio.comuns.utilitarias.*;
//import negocio.comuns.plano.*;
//import javax.faces.model.SelectItem;
//import java.util.List;
//import java.util.ArrayList;
//import controle.arquitetura.SuperControle;
//
///**
// * Classe responsável por implementar a interação entre os componentes JSF das páginas 
// * produtoSugeridoForm.jsp produtoSugeridoCons.jsp) com as funcionalidades da classe <code>ProdutoSugerido</code>.
// * Implemtação da camada controle (Backing Bean).
// * @see SuperControle
// * @see ProdutoSugerido
// * @see ProdutoSugeridoVO
//*/
//public class ProdutoSugeridoControle extends SuperControle {
//    private ProdutoSugeridoVO produtoSugeridoVO;
//    protected List listaSelectItemModalidade;
//    protected List listaSelectItemProduto;
//    /**
//    * Interface <code>ProdutoSugeridoInterfaceFacade</code> responsável pela interconexão da camada de controle com a camada de negócio.
//    * Criando uma independência da camada de controle com relação a tenologia de persistência dos dados (DesignPatter: Façade).
//    */
//    private ProdutoSugeridoInterfaceFacade produtoSugeridoFacade = null;
//    private ModalidadeInterfaceFacade modalidadeFacade = null;
//    private ProdutoInterfaceFacade produtoFacade = null;
//
//    public ProdutoSugeridoControle() throws Exception {
//        obterUsuarioLogado();
//        inicializarFacades();
//        setControleConsulta(new ControleConsulta());
//        setMensagemID("msg_entre_prmconsulta");
//    }
//
//    /**
//    * Rotina responsável por disponibilizar um novo objeto da classe <code>ProdutoSugerido</code>
//    * para edição pelo usuário da aplicação.
//    */
//    public String novo() {
//        setProdutoSugeridoVO(new ProdutoSugeridoVO());
//        inicializarListasSelectItemTodosComboBox();
//        setMensagemID("msg_entre_dados");
//        return "editar";
//    }
//
//    /**
//    * Rotina responsável por disponibilizar os dados de um objeto da classe <code>ProdutoSugerido</code> para alteração.
//    * O objeto desta classe é disponibilizado na session da página (request) para que o JSP correspondente possa disponibilizá-lo para edição.
//    */
//    public String editar() {
//        ProdutoSugeridoVO obj = (ProdutoSugeridoVO)context().getExternalContext().getRequestMap().get("produtoSugerido");
//        inicializarAtributosRelacionados(obj);
//        obj.setNovoObj(new Boolean(false));
//        setProdutoSugeridoVO(obj);
//        inicializarListasSelectItemTodosComboBox();
//        setMensagemID("msg_dados_editar");
//        return "editar";
//    }
//
//    /**
//    * Método responsável inicializar objetos relacionados a classe <code>ProdutoSugeridoVO</code>.
//    * Esta inicialização é necessária por exigência da tecnologia JSF, que não trabalha com valores nulos para estes atributos.
//    */
//    public void inicializarAtributosRelacionados(ProdutoSugeridoVO obj) {
//        if (obj.getModalidade() == null) {
//            obj.setModalidade(new ModalidadeVO());
//        }
//        if (obj.getProduto() == null) {
//            obj.setProduto(new ProdutoVO());
//        }
//    }
//
//    /**
//    * Rotina responsável por gravar no BD os dados editados de um novo objeto da classe <code>ProdutoSugerido</code>.
//    * Caso o objeto seja novo (ainda não gravado no BD) é acionado a operação <code>incluir()</code>. Caso contrário é acionado o <code>alterar()</code>.
//    * Se houver alguma inconsistência o objeto não é gravado, sendo re-apresentado para o usuário juntamente com uma mensagem de erro.
//    */
//    public String gravar() {
//        try {
//            if (produtoSugeridoVO.isNovoObj().booleanValue()) {
//                produtoSugeridoFacade.incluir(produtoSugeridoVO);
//            } else {
//                produtoSugeridoFacade.alterar(produtoSugeridoVO);
//            }
//            setMensagemID("msg_dados_gravados");
//            return "editar";
//        } catch (Exception e) {
//            setMensagemDetalhada("msg_erro", e.getMessage());
//            return "editar";
//        }
//    }
//
//    /**
//    * Rotina responsavel por executar as consultas disponiveis no JSP ProdutoSugeridoCons.jsp.
//    * Define o tipo de consulta a ser executada, por meio de ComboBox denominado campoConsulta, disponivel neste mesmo JSP.
//    * Como resultado, disponibiliza um List com os objetos selecionados na sessao da pagina.
//    */
//    public String consultar() {
//        try {
//            super.consultar();
//            List objs = new ArrayList();
//            if (getControleConsulta().getCampoConsulta().equals("codigo")) {
//                if (getControleConsulta().getValorConsulta().equals("")) {
//                    getControleConsulta().setValorConsulta("0");
//                }
//                int valorInt = Integer.parseInt(getControleConsulta().getValorConsulta());
//                objs = produtoSugeridoFacade.consultarPorCodigo(new Integer(valorInt), true, Uteis.NIVELMONTARDADOS_TODOS);
//            }
//            if (getControleConsulta().getCampoConsulta().equals("nomeModalidade")) {
//                objs = produtoSugeridoFacade.consultarPorNomeModalidade(getControleConsulta().getValorConsulta(), Uteis.NIVELMONTARDADOS_TODOS);
//            }
//            if (getControleConsulta().getCampoConsulta().equals("descricaoProduto")) {
//                objs = produtoSugeridoFacade.consultarPorDescricaoProduto(getControleConsulta().getValorConsulta(), Uteis.NIVELMONTARDADOS_TODOS);
//            }
//            objs = ControleConsulta.obterSubListPaginaApresentar(objs, controleConsulta);
//            definirVisibilidadeLinksNavegacao(controleConsulta.getPaginaAtual(), controleConsulta.getNrTotalPaginas());
//            setListaConsulta(objs);
//            setMensagemID("msg_dados_consultados");
//            return "consultar";
//        } catch (Exception e) {
//            setListaConsulta(new ArrayList());
//            setMensagemDetalhada("msg_erro", e.getMessage());
//            return "consultar";
//        }
//    }
//
//    /**
//     * Operação responsável por processar a exclusão um objeto da classe <code>ProdutoSugeridoVO</code>
//     * Após a exclusão ela automaticamente aciona a rotina para uma nova inclusão.
//     */
//    public String excluir() {
//        try {
//            produtoSugeridoFacade.excluir(produtoSugeridoVO);
//            setProdutoSugeridoVO( new ProdutoSugeridoVO());
//            setMensagemID("msg_dados_excluidos");
//            return "editar";
//        } catch (Exception e) {
//            setMensagemDetalhada("msg_erro", e.getMessage());
//            return "editar";
//        }
//    }
//
//    public void irPaginaInicial() throws Exception{
//        controleConsulta.setPaginaAtual(1);
//        this.consultar();
//    }
//
//    public void irPaginaAnterior() throws Exception{
//        controleConsulta.setPaginaAtual(controleConsulta.getPaginaAtual() - 1);
//        this.consultar();
//    }
//
//    public void irPaginaPosterior() throws Exception{
//        controleConsulta.setPaginaAtual(controleConsulta.getPaginaAtual() + 1);
//        this.consultar();
//    }
//
//    public void irPaginaFinal() throws Exception{
//        controleConsulta.setPaginaAtual(controleConsulta.getNrTotalPaginas());
//        this.consultar();
//    }
//
//    /**
//     * Método responsável por gerar uma lista de objetos do tipo <code>SelectItem</code> para preencher
//     * o comboBox relativo ao atributo <code>Produto</code>.
//    */
//    public void montarListaSelectItemProduto(String prm) throws Exception {
//        List resultadoConsulta = consultarProdutoPorDescricao(prm);
//        Iterator i = resultadoConsulta.iterator();
//        List objs = new ArrayList();
//        objs.add(new SelectItem(new Integer(0), ""));
//        while (i.hasNext()) {
//            ProdutoVO obj = (ProdutoVO)i.next();
//            objs.add(new SelectItem(obj.getCodigo(), obj.getDescricao().toString()));
//        }
//        setListaSelectItemProduto(objs);
//    }
//
//    /**
//     * Método responsável por atualizar o ComboBox relativo ao atributo <code>Produto</code>.
//     * Buscando todos os objetos correspondentes a entidade <code>Produto</code>. Esta rotina não recebe parâmetros para filtragem de dados, isto é
//     * importante para a inicialização dos dados da tela para o acionamento por meio requisições Ajax.
//    */
//    public void montarListaSelectItemProduto() {
//        try {
//            montarListaSelectItemProduto("");
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//    }
//
//    /**
//     * Método responsável por consultar dados da entidade <code><code> e montar o atributo <code>descricao</code>
//     * Este atributo é uma lista (<code>List</code>) utilizada para definir os valores a serem apresentados no ComboBox correspondente
//    */
//    public List consultarProdutoPorDescricao(String descricaoPrm) throws Exception {
//        List lista = produtoFacade.consultarPorDescricao(descricaoPrm, false, Uteis.NIVELMONTARDADOS_TODOS);
//        return lista;
//    }
//
//    /**
//     * Método responsável por gerar uma lista de objetos do tipo <code>SelectItem</code> para preencher
//     * o comboBox relativo ao atributo <code>Modalidade</code>.
//    */
//    public void montarListaSelectItemModalidade(String prm) throws Exception {
//        List resultadoConsulta = consultarModalidadePorNome(prm);
//        Iterator i = resultadoConsulta.iterator();
//        List objs = new ArrayList();
//        objs.add(new SelectItem(new Integer(0), ""));
//        while (i.hasNext()) {
//            ModalidadeVO obj = (ModalidadeVO)i.next();
//            objs.add(new SelectItem(obj.getCodigo(), obj.getNome().toString()));
//        }
//        setListaSelectItemModalidade(objs);
//    }
//
//    /**
//     * Método responsável por atualizar o ComboBox relativo ao atributo <code>Modalidade</code>.
//     * Buscando todos os objetos correspondentes a entidade <code>Modalidade</code>. Esta rotina não recebe parâmetros para filtragem de dados, isto é
//     * importante para a inicialização dos dados da tela para o acionamento por meio requisições Ajax.
//    */
//    public void montarListaSelectItemModalidade() {
//        try {
//            montarListaSelectItemModalidade("");
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//    }
//
//    /**
//     * Método responsável por consultar dados da entidade <code><code> e montar o atributo <code>nome</code>
//     * Este atributo é uma lista (<code>List</code>) utilizada para definir os valores a serem apresentados no ComboBox correspondente
//    */
//    public List consultarModalidadePorNome(String nomePrm) throws Exception {
//        List lista = modalidadeFacade.consultarPorNome(nomePrm, false, Uteis.NIVELMONTARDADOS_TODOS);
//        return lista;
//    }
//
//    /**
//     * Método responsável por inicializar a lista de valores (<code>SelectItem</code>) para todos os ComboBox's.
//    */
//    public void inicializarListasSelectItemTodosComboBox() {
//        montarListaSelectItemModalidade();
//        montarListaSelectItemProduto();
//    }
//
//    /**
//    * Rotina responsável por preencher a combo de consulta da telas.
//    */
//    public List getTipoConsultaCombo() {
//        List itens = new ArrayList();
//        itens.add(new SelectItem("codigo", "Código"));
//        itens.add(new SelectItem("nomeModalidade", "Modalidade"));
//        itens.add(new SelectItem("descricaoProduto", "Produto"));
//        return itens;
//    }
//
//    /**
//    * Rotina responsável por organizar a paginação entre as páginas resultantes de uma consulta.
//    */
//    public String inicializarConsultar() {
//        setPaginaAtualDeTodas("0/0");
//        setListaConsulta(new ArrayList());
//        definirVisibilidadeLinksNavegacao(0, 0);
//        setMensagemID("msg_entre_prmconsulta");
//        return "consultar";
//    }
//
//    /**
//    * Operação que inicializa as Interfaces Façades com os respectivos objetos de
//    * persistência dos dados no banco de dados. 
//    */
//    protected boolean inicializarFacades() {
//        try {
//            super.inicializarFacades();
//            produtoSugeridoFacade = new ProdutoSugerido();
//            modalidadeFacade = new Modalidade();
//            produtoFacade = new Produto();
//            return true;
//        } catch (Exception e) {
//            setMensagemDetalhada("msg_erro_conectarBD", e.getMessage());
//            return false;
//        }
//    }
//
//    public List getListaSelectItemProduto() {
//        return (listaSelectItemProduto);
//    }
//     
//    public void setListaSelectItemProduto( List listaSelectItemProduto ) {
//        this.listaSelectItemProduto = listaSelectItemProduto;
//    }
//
//    public List getListaSelectItemModalidade() {
//        return (listaSelectItemModalidade);
//    }
//     
//    public void setListaSelectItemModalidade( List listaSelectItemModalidade ) {
//        this.listaSelectItemModalidade = listaSelectItemModalidade;
//    }
//
//    public ProdutoSugeridoVO getProdutoSugeridoVO() {
//        return produtoSugeridoVO;
//    }
//     
//    public void setProdutoSugeridoVO(ProdutoSugeridoVO produtoSugeridoVO) {
//        this.produtoSugeridoVO = produtoSugeridoVO;
//    }
//}
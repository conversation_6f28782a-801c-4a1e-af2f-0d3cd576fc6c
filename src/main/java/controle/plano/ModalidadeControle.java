package controle.plano;

import br.com.pactosolucoes.comuns.notificacao.RecursoSistema;
import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.estrutura.exportador.controle.ExportadorListaControle;
import controle.arquitetura.SuperControle;
import controle.arquitetura.security.LoginControle;
import controle.basico.clube.MensagemGenericaControle;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.plano.ModalidadeEmpresaVO;
import negocio.comuns.plano.ModalidadeVO;
import negocio.comuns.plano.ProdutoSugeridoVO;
import negocio.comuns.plano.ProdutoVO;
import negocio.comuns.plano.TipoModalidadeVO;
import negocio.comuns.utilitarias.ControleConsulta;
import negocio.comuns.utilitarias.ImageBase64Util;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.plano.Modalidade;
import servicos.operacoes.midias.MidiaService;
import servicos.operacoes.midias.commons.MidiaEntidadeEnum;

import javax.faces.context.FacesContext;
import javax.faces.event.ActionEvent;
import javax.faces.model.SelectItem;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.io.OutputStream;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Classe responsável por implementar a interação entre os componentes JSF das páginas 
 * modalidadeForm.jsp modalidadeCons.jsp) com as funcionalidades da classe <code>Modalidade</code>.
 * Implemtação da camada controle (Backing Bean).
 * @see SuperControle
 * @see Modalidade
 * @see ModalidadeVO
 */
public class ModalidadeControle extends SuperControle {

    private ModalidadeVO modalidadeVO;
    /**
     * Interface <code>ModalidadeInterfaceFacade</code> responsável pela interconexão da camada de controle com a camada de negócio.
     * Criando uma independência da camada de controle com relação a tenologia de persistência dos dados (DesignPatter: Façade).
     */
    private ProdutoSugeridoVO produtoSugeridoVO;
    protected List listaSelectItemProduto;
    protected List<SelectItem> listaSelectItemTipoModalidade;
    private ModalidadeEmpresaVO modalidadeEmpresaVO;
    protected List<SelectItem> listaSelectItemEmpresa;
    //  protected List listaSelectItemModalidade;
    protected String campoConsultaProduto;
    protected String valorConsultaProduto;
    protected List listaConsultaProduto;
    private EmpresaVO empresaVO;
    private int filtroEmpresa;
    private Boolean eraCrossfit = Boolean.FALSE;
    private Boolean temContratoVendido = Boolean.FALSE;
    private String fileBase64;
    private boolean permiteConsultarMultiplasEmpresas;
    private String situacaoFiltro;

    public String getFileBase64() {
        return fileBase64;
    }

    public void setFileBase64(String fileBase64) {
        this.fileBase64 = fileBase64;
    }

    public ModalidadeControle() throws Exception {
        obterUsuarioLogado();
        inicializarFacades();
        montarComboTiposModalidade();
        setControleConsulta(new ControleConsulta());
        inicializarPermissaoConsultarMultiplasEmpresas();
        inicializarDadosEmpresa();
        setMensagemID("");
    }

    public void resetarComboTiposModalidade() {
        try {
            listaSelectItemTipoModalidade = new ArrayList<SelectItem>();
            listaSelectItemTipoModalidade.add(new SelectItem(null, ""));
            List<TipoModalidadeVO> tipos = getFacade().getTipoModalidade().consultarPorNome("");
            for (TipoModalidadeVO tipo : tipos) {
                listaSelectItemTipoModalidade.add(new SelectItem(tipo.getIdentificador(), tipo.getNome()));
            }

        } catch (Exception e) {
            Uteis.logar(e, ProdutoControle.class);
            listaSelectItemTipoModalidade = new ArrayList<SelectItem>();
        }
    }
    public void montarComboTiposModalidade() {
        if (listaSelectItemTipoModalidade == null || listaSelectItemTipoModalidade.isEmpty()) {
            resetarComboTiposModalidade();
        }
    }

    public void inicializarDadosEmpresa() {

        if(isPermiteConsultarMultiplasEmpresas()){
            setFiltroEmpresa(0);
        }else{
            try {
                setFiltroEmpresa(getEmpresaLogado().getCodigo());
            } catch (Exception e) {
                Uteis.logar(e, ModalidadeControle.class);
                setFiltroEmpresa(0);
            }
        }
    }

    public void inicializarUsuarioLogado() {
        try {
            modalidadeVO.setUsuarioVO(getUsuarioLogado());
        } catch (Exception exception) {
        }
    }

    public void paint(OutputStream stream, Object object) throws IOException {

            try {
                byte[] b = MidiaService.getInstance()
                        .downloadObjectAsByteArray(getKey(), MidiaEntidadeEnum.FOTO_MODADLIDADE, modalidadeVO.getCodigo().toString(), modalidadeVO.getFotokey());
                stream.write(b);
            } catch (Exception e) {
            }

        stream.close();
    }

    private void gerarArquivo() throws Exception {

        ImageBase64Util image = new ImageBase64Util(getFileBase64());

        String timeStamp = "?time=" + getTimeStamp();
        String fotokey = MidiaService.getInstance().uploadObjectFromByteArray(getKey(),
                MidiaEntidadeEnum.FOTO_MODADLIDADE,
                modalidadeVO.getCodigo().toString() + timeStamp,
                image.getImageByteArray());

        modalidadeVO.setFotokey(fotokey);
    }

    public void limparImage(){
        fileBase64 = "";
    }

    /**
     * Rotina responsável por disponibilizar um novo objeto da classe <code>Modalidade</code>
     * para edição pelo usuário da aplicação.
     */
    public String novo() {
        setModalidadeVO(new ModalidadeVO());
        inicializarUsuarioLogado();
        inicializarListasSelectItemTodosComboBox();
        setProdutoSugeridoVO(new ProdutoSugeridoVO());
        setModalidadeEmpresaVO(new ModalidadeEmpresaVO());
        setValorConsultaProduto("");
        setCampoConsultaProduto("");
        setListaConsultaProduto(new ArrayList());
        inicializarPermissaoConsultarMultiplasEmpresas();
        inicializarDadosEmpresa();
        limparMsg();
        limparImage();
        return "editar";
    }
    public void novoSemRedirect() throws Exception{
        novo();
    }

    public void inicializarPermissaoConsultarMultiplasEmpresas(){
        if( (getModalidadeVO() != null &&
                getModalidadeVO().getUsuarioVO() != null
                && getModalidadeVO().getUsuarioVO().getAdministrador() ) ||
            permissao("ConsultarInfoTodasEmpresas") ||
            permissao("ModalidadeEmpresa")){
            setPermiteConsultarMultiplasEmpresas(true);
        }else{
            setPermiteConsultarMultiplasEmpresas(false);
        }
    }

    /**
     * Rotina responsável por disponibilizar os dados de um objeto da classe <code>Modalidade</code> para alteração.
     * O objeto desta classe é disponibilizado na session da página (request) para que o JSP correspondente possa disponibilizá-lo para edição.
     */
    public String editar() {
        Integer codigoConsulta = Integer.parseInt(request().getParameter("chavePrimaria"));
        try {
            ModalidadeVO obj = getFacade().getModalidade().consultarPorChavePrimaria(codigoConsulta, Uteis.NIVELMONTARDADOS_TODOS);
            temContratoVendido = getFacade().getModalidade().modalidadeTemContratoVendido(codigoConsulta);
            eraCrossfit = obj.getCrossfit();
            setModalidadeVO(obj);
            getModalidadeVO().setNovoObj(false);
            getModalidadeVO().registrarObjetoVOAntesDaAlteracao();
            inicializarUsuarioLogado();
            inicializarListasSelectItemTodosComboBox();
            setProdutoSugeridoVO(new ProdutoSugeridoVO());
            setModalidadeEmpresaVO(new ModalidadeEmpresaVO());
            setValorConsultaProduto("");
            setCampoConsultaProduto("");
            setListaConsultaProduto(new ArrayList());
            limparMsg();
            limparImage();
            inicializarPermissaoConsultarMultiplasEmpresas();
            inicializarDadosEmpresa();
        } catch (Exception e) {
            setErro(true);
            setSucesso(false);
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
        return "editar";
    }

    /**
     * Rotina responsável por gravar no BD os dados editados de um novo objeto da classe <code>Modalidade</code>.
     * Caso o objeto seja novo (ainda não gravado no BD) é acionado a operação <code>incluir()</code>. Caso contrário é acionado o <code>alterar()</code>.
     * Se houver alguma inconsistência o objeto não é gravado, sendo re-apresentado para o usuário juntamente com uma mensagem de erro.
     */
    public String gravar() {
        try {

            if(null != getFileBase64() && !getFileBase64().isEmpty()){
                gerarArquivo();
            }

            if (modalidadeVO.isNovoObj().booleanValue()) {
                getFacade().getModalidade().incluir(modalidadeVO);
                incluirLogInclusao();
            } else {
                getFacade().getModalidade().alterar(modalidadeVO);
                incluirLogAlteracao();
            }
            if(!modalidadeVO.isNovoObj().booleanValue() && !eraCrossfit && modalidadeVO.getCrossfit()){
                getFacade().getModalidade().atualizarAlunosCrossfit(modalidadeVO.getCodigo(), modalidadeVO.getCrossfit(), getKey());
            }
            setMensagemID("msg_dados_gravados");
            setSucesso(true);
            setErro(false);
            montarSucessoGrowl("Dados Gravados com Sucesso!");
            return "editar";
        } catch (Exception e) {
            Logger.getLogger(Modalidade.class.getName()).log(Level.INFO, "#### ERRO AO SALVAR MODALIDADE PARA A CHAVE - " + getKey());
            Logger.getLogger(Modalidade.class.getName()).log(Level.INFO, "#### EXCEPTION: - " + e.getMessage());
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
            return "editar";
        }
    }

    /**
     * Rotina responsavel por executar as consultas disponiveis no JSP ModalidadeCons.jsp.
     * Define o tipo de consulta a ser executada, por meio de ComboBox denominado campoConsulta, disponivel neste mesmo JSP.
     * Como resultado, disponibiliza um List com os objetos selecionados na sessao da pagina.
     */
    public String consultar() {
        try {
            super.consultar();
            List objs = new ArrayList();
            List lista = new ArrayList();
            if (getControleConsulta().getCampoConsulta().equals("codigo")) {
                if (getControleConsulta().getValorConsulta().equals("")) {
                    getControleConsulta().setValorConsulta("0");
                }
                int valorInt = Integer.parseInt(getControleConsulta().getValorConsulta());
                objs = getFacade().getModalidade().consultarPorCodigo(new Integer(valorInt), getEmpresaLogado().getCodigo(), true, Uteis.NIVELMONTARDADOS_TELACONSULTA);
                if (getEmpresaLogado().getCodigo() != 0) {
                    lista = getFacade().getModalidade().consultarPorCodigo(valorInt, 0, true, Uteis.NIVELMONTARDADOS_TELACONSULTA);
                    Iterator i = lista.iterator();
                    while (i.hasNext()) {
                        ModalidadeVO object = (ModalidadeVO) i.next();
                        if (object.getModalidadeEmpresaVOs().isEmpty()) {
                            objs.add(object);
                        }
                    }
                }

            }
            if (getControleConsulta().getCampoConsulta().equals("nome")) {
                objs = getFacade().getModalidade().consultarPorNome(getControleConsulta().getValorConsulta(), getEmpresaLogado().getCodigo(), true, Uteis.NIVELMONTARDADOS_TELACONSULTA);
                if (getEmpresaLogado().getCodigo() != 0) {
                    lista = getFacade().getModalidade().consultarPorNome(getControleConsulta().getValorConsulta(), 0, true, Uteis.NIVELMONTARDADOS_TELACONSULTA);
                    Iterator i = lista.iterator();
                    while (i.hasNext()) {
                        ModalidadeVO object = (ModalidadeVO) i.next();
                        if (object.getModalidadeEmpresaVOs().isEmpty()) {
                            objs.add(object);
                        }
                    }
                }
            }

            objs = ControleConsulta.obterSubListPaginaApresentar(objs, controleConsulta);
            definirVisibilidadeLinksNavegacao(controleConsulta.getPaginaAtual(), controleConsulta.getNrTotalPaginas());
            setListaConsulta(objs);
            limparMsg();
            return "consultar";
        } catch (Exception e) {
            setListaConsulta(new ArrayList());
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
            return "consultar";
        }
    }

    public void consultarProduto() {
        try {
            super.consultar();
            List<String> listaTipoProdutos = new ArrayList<String>();
            listaTipoProdutos.add("PM");
            listaTipoProdutos.add("SS");
            listaTipoProdutos.add("AA");
            listaTipoProdutos.add("AC");
            listaTipoProdutos.add("AH");
            listaTipoProdutos.add("CC");
            listaTipoProdutos.add("DE");
            listaTipoProdutos.add("DI");
            listaTipoProdutos.add("DR");
            listaTipoProdutos.add("DV");
            listaTipoProdutos.add("FR");
            listaTipoProdutos.add("MA");
            listaTipoProdutos.add("MM");
            listaTipoProdutos.add("QU");
            listaTipoProdutos.add("RD");
            listaTipoProdutos.add("RE");
            listaTipoProdutos.add("RN");
            listaTipoProdutos.add("TP");
            listaTipoProdutos.add("TR");
            listaTipoProdutos.add("MC");

            List objs = new ArrayList();
//            if (getCampoConsultaProduto().equals("codigo")) {
//                if (getValorConsultaProduto().equals("")) {
//                    setValorConsultaProduto("0");
//                }
//                int valorInt = Integer.parseInt(getValorConsultaProduto());
//                objs = produtoFacade.consultarPorCodigo(new Integer(valorInt), true, Uteis.NIVELMONTARDADOS_TODOS);
//            }
            if (getCampoConsultaProduto().equals("nome")) {
                objs = getFacade().getProduto().consultarPorDescricaoDiferenteTipoProdutoAtivo(getValorConsultaProduto(), listaTipoProdutos, true,false,0, Uteis.NIVELMONTARDADOS_MINIMOS);
            }
            setListaConsultaProduto(objs);
            setMensagemID("msg_dados_consultados");

        } catch (Exception e) {
            setListaConsultaProduto(new ArrayList());
            setMensagemDetalhada("msg_erro", e.getMessage());

        }
    }

    public void selecionarProduto() {
        ProdutoVO obj = (ProdutoVO) context().getExternalContext().getRequestMap().get("produto");
        getProdutoSugeridoVO().setProduto(obj);
        obj = null;
        listaConsultaProduto.clear();
        campoConsultaProduto = null;
        valorConsultaProduto = null;
    }

    /**
     * Operação responsável por processar a exclusão um objeto da classe <code>ModalidadeVO</code>
     * Após a exclusão ela automaticamente aciona a rotina para uma nova inclusão.
     */
    public void excluir() {
        try {
            getFacade().getModalidade().excluir(modalidadeVO);
            incluirLogExclusao();
            setModalidadeVO(new ModalidadeVO());
            setProdutoSugeridoVO(new ProdutoSugeridoVO());
            redirect("/faces/modalidadeCons.jsp");
            montarSucesso("msg_dados_excluidos");
            setMsgAlert(getMensagemNotificar());
        } catch (Exception e) {
            String[] msg = e.getMessage().split("em");
            String[] tabela = msg[1].split("_");
            if (e.getMessage().contains("ERRO: atualização ou exclusão em tabela \"modalidade\" viola restrição de chave estrangeira") || e.getMessage().contains("ERROR: update or delete on table \"modalidade\" violates foreign key")){
                montarErro("Esta modalidade não pode ser excluída, pois está sendo utilizada em " + tabela[1]);
                setMensagemDetalhada("Esta modalidade não pode ser excluída, pois está sendo utilizada em " + tabela[1]);
            }else {
                montarErro(e.getMessage());
            }
        }
    }

    /* Método responsável por adicionar um novo objeto da classe <code>ModalidadeEmpresa</code>
     * para o objeto <code>modalidadeVO</code> da classe <code>Modalidade</code>
     */
    public void adicionarModalidadeEmpresa() throws Exception {
        try {
            notificarRecursoEmpresa(RecursoSistema.CADASTRO_MODALIDADE_ADICIONAR_EMPRESA);
            if (!getModalidadeVO().getCodigo().equals(new Integer(0))) {
                modalidadeEmpresaVO.setModalidade(getModalidadeVO().getCodigo());
            }
            if (getModalidadeEmpresaVO().getEmpresa().getCodigo().intValue() != 0) {
                Integer campoConsulta = getModalidadeEmpresaVO().getEmpresa().getCodigo();
                EmpresaVO empresa = getFacade().getEmpresa().consultarPorChavePrimaria(campoConsulta, Uteis.NIVELMONTARDADOS_MINIMOS);
                getModalidadeEmpresaVO().setEmpresa(empresa);
            }
            getModalidadeVO().adicionarObjModalidadeEmpresaVOs(getModalidadeEmpresaVO());
            this.setModalidadeEmpresaVO(new ModalidadeEmpresaVO());
            setMensagemID("msg_dados_adicionados");
            //          return "editar";
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            //        return "editar";
        }
    }

    /* Método responsável por disponibilizar dados de um objeto da classe <code>ModalidadeEmpresa</code>
     * para edição pelo usuário.
     */
    public void editarModalidadeEmpresa() throws Exception {
        ModalidadeEmpresaVO obj = (ModalidadeEmpresaVO) context().getExternalContext().getRequestMap().get("modalidadeEmpresa");
        setModalidadeEmpresaVO(obj);
        //   return "editar";
    }

    /* Método responsável por remover um novo objeto da classe <code>ModalidadeEmpresa</code>
     * do objeto <code>modalidadeVO</code> da classe <code>Modalidade</code>
     */
    public void removerModalidadeEmpresa() throws Exception {
        try {
            if(getModalidadeVO().getModalidadeEmpresaVOs().size() <= 1){
                throw new Exception("Empresa não pode ser removida da modalidade, pois ao menos uma é necessario.");
            }

            ModalidadeEmpresaVO obj = (ModalidadeEmpresaVO) context().getExternalContext().getRequestMap().get("modalidadeEmpresa");
            List listaComposicao = getFacade().getComposicao().consultarComposicaoPorModalidadeEmpresa(obj.getModalidade(), obj.getEmpresa().getCodigo(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if (!listaComposicao.isEmpty()) {
                throw new Exception("Empresa não pode ser removida da modalidade, pois a mesma faz parte de um pacote nessa empresa.");
            }

            List listaTurmas = getFacade().getTurma().consultarPorCodigoModalidade(obj.getModalidade(), obj.getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if (!listaTurmas.isEmpty()) {
                throw new Exception("Empresa não pode ser removida da modalidade, pois a mesma faz parte de alguma turma nessa empresa.");
            }

            getModalidadeVO().excluirObjModalidadeEmpresaVOs(obj.getEmpresa().getCodigo());
            montarListaSelectItemEmpresa();
            setMensagemID("msg_dados_excluidos");
            setMensagem("");
            setMensagemDetalhada("");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
        //return "editar";
    }

    /* Método responsável por adicionar um novo objeto da classe <code>ProdutoSugerido</code>
     * para o objeto <code>modalidadeVO</code> da classe <code>Modalidade</code>
     */
    public void adicionarProdutoSugerido() throws Exception {
        try {
            if (!getModalidadeVO().getCodigo().equals(new Integer(0))) {
                produtoSugeridoVO.setModalidade(getModalidadeVO().getCodigo());
            }

            if (getProdutoSugeridoVO().getProduto().getCodigo().intValue() != 0) {
                Integer campoProduto = getProdutoSugeridoVO().getProduto().getCodigo();
                ProdutoVO produto = getFacade().getProduto().consultarPorChavePrimaria(campoProduto, Uteis.NIVELMONTARDADOS_TODOS);
                getProdutoSugeridoVO().setProduto(produto);
            }
            getModalidadeVO().adicionarObjProdutoSugeridoVOs(getProdutoSugeridoVO());
            this.setProdutoSugeridoVO(new ProdutoSugeridoVO());
            setMensagemID("msg_dados_adicionados");

        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());

        }
    }

    /* Método responsável por disponibilizar dados de um objeto da classe <code>ProdutoSugerido</code>
     * para edição pelo usuário.
     */
    public void editarProdutoSugerido() throws Exception {
        ProdutoSugeridoVO obj = (ProdutoSugeridoVO) context().getExternalContext().getRequestMap().get("produtoSugerido");
        setProdutoSugeridoVO(obj);

    }

    /* Método responsável por remover um novo objeto da classe <code>ProdutoSugerido</code>
     * do objeto <code>modalidadeVO</code> da classe <code>Modalidade</code>
     */
    public void removerProdutoSugerido() throws Exception {
        ProdutoSugeridoVO obj = (ProdutoSugeridoVO) context().getExternalContext().getRequestMap().get("produtoSugerido");
        getModalidadeVO().excluirObjProdutoSugeridoVOs(obj.getProduto().getCodigo());
        setMensagemID("msg_dados_excluidos");
    }

    public void irPaginaInicial() throws Exception {
        controleConsulta.setPaginaAtual(1);
        this.consultar();
    }

    public void irPaginaAnterior() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getPaginaAtual() - 1);
        this.consultar();
    }

    public void irPaginaPosterior() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getPaginaAtual() + 1);
        this.consultar();
    }

    public void irPaginaFinal() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getNrTotalPaginas());
        this.consultar();
    }

    /**
     * Método responsável por gerar uma lista de objetos do tipo <code>SelectItem</code> para preencher
     * o comboBox relativo ao atributo <code>Modalidade</code>.
     */
//    public void montarListaSelectItemModalidade(String prm) throws Exception {
//        List resultadoConsulta = consultarModalidadePorNome(prm);
//        Iterator i = resultadoConsulta.iterator();
//        List objs = new ArrayList();
//        objs.add(new SelectItem(new Integer(0), ""));
//        while (i.hasNext()) {
//            ModalidadeVO obj = (ModalidadeVO)i.next();
//            objs.add(new SelectItem(obj.getCodigo(), obj.getNome().toString()));
//        }
//        setListaSelectItemModalidade(objs);
//    }
//
//    /**
//     * Método responsável por atualizar o ComboBox relativo ao atributo <code>Modalidade</code>.
//     * Buscando todos os objetos correspondentes a entidade <code>Modalidade</code>. Esta rotina não recebe parâmetros para filtragem de dados, isto é
//     * importante para a inicialização dos dados da tela para o acionamento por meio requisições Ajax.
//    */
//    public void montarListaSelectItemModalidade() {
//        try {
//            montarListaSelectItemModalidade("");
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//    }
//
//    /**
//     * Método responsável por consultar dados da entidade <code><code> e montar o atributo <code>nome</code>
//     * Este atributo é uma lista (<code>List</code>) utilizada para definir os valores a serem apresentados no ComboBox correspondente
//    */
//    public List consultarModalidadePorNome(String nomePrm) throws Exception {
//        List lista = modalidadeFacade.consultarPorNome(nomePrm, false, Uteis.NIVELMONTARDADOS_TODOS);
//        return lista;
//    }
    /**
     * Método responsável por gerar uma lista de objetos do tipo <code>SelectItem</code> para preencher
     * o comboBox relativo ao atributo <code>Empresa</code>.
     */
    public void montarListaSelectItemEmpresa(String prm) throws Exception {
        setListaSelectItemEmpresa(new ArrayList<SelectItem>());
        getListaSelectItemEmpresa().add(new SelectItem(new Integer(0), ""));

        List<EmpresaVO> empresas = consultarEmpresaPorNome(prm);

        for (EmpresaVO empresa: empresas) {
                getListaSelectItemEmpresa().add(new SelectItem(empresa.getCodigo(), empresa.getNome()));
        }

        if (getEmpresaLogado().getCodigo() != 0) {
            ModalidadeEmpresaVO modalidadEmpresa = new ModalidadeEmpresaVO();
            modalidadEmpresa.setEmpresa(getEmpresaLogado());
            if (getModalidadeVO().getModalidadeEmpresaVOs().isEmpty()) {
                this.getModalidadeVO().getModalidadeEmpresaVOs().add(modalidadEmpresa);
            }
        }
    }

    /**
     * Método responsável por atualizar o ComboBox relativo ao atributo <code>Empresa</code>.
     * Buscando todos os objetos correspondentes a entidade <code>Empresa</code>. Esta rotina não recebe parâmetros para filtragem de dados, isto é
     * importante para a inicialização dos dados da tela para o acionamento por meio requisições Ajax.
     */
    public void montarListaSelectItemEmpresa() {
        try {
            montarListaSelectItemEmpresa("");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * Método responsável por consultar dados da entidade <code><code> e montar o atributo <code>nome</code>
     * Este atributo é uma lista (<code>List</code>) utilizada para definir os valores a serem apresentados no ComboBox correspondente
     */
    public List<EmpresaVO> consultarEmpresaPorNome(String nomePrm) throws Exception {
        List lista = getFacade().getEmpresa().consultarPorNome(nomePrm,true, false, Uteis.NIVELMONTARDADOS_MINIMOS);
        return lista;
    }

    /**
     * Método responsável por gerar uma lista de objetos do tipo <code>SelectItem</code> para preencher
     * o comboBox relativo ao atributo <code>TipoModalidade</code>.
     */
    public void montarListaSelectItemTipoModalidade(String prm) throws Exception {
        List resultadoConsulta = consultarTipoModalidadePorNome(prm);
        Iterator i = resultadoConsulta.iterator();
        List objs = new ArrayList();
        objs.add(new SelectItem(new Integer(0), ""));
        while (i.hasNext()) {
            TipoModalidadeVO obj = (TipoModalidadeVO) i.next();
            objs.add(new SelectItem(obj.getIdentificador(), obj.getNome()));
        }
        setListaSelectItemTipoModalidade(objs);
    }

    /**
     * Método responsável por atualizar o ComboBox relativo ao atributo <code>Produto</code>.
     * Buscando todos os objetos correspondentes a entidade <code>Produto</code>. Esta rotina não recebe parâmetros para filtragem de dados, isto é
     * importante para a inicialização dos dados da tela para o acionamento por meio requisições Ajax.
     */
    public void montarListaSelectItemTipoModalidade() {
        try {
            montarListaSelectItemTipoModalidade("");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
    /**
     * Método responsável por consultar dados da entidade <code>TipoModalidade<code> e montar o atributo <code>nome</code>
     * Este atributo é uma lista (<code>List</code>) utilizada para definir os valores a serem apresentados no ComboBox correspondente
     */
    public List consultarTipoModalidadePorNome(String nomePrm) throws Exception {
        List lista = getFacade().getTipoModalidade().consultarPorNome(nomePrm, false, Uteis.NIVELMONTARDADOS_TODOS);
        //List lista = getFacade().getProduto().consultarPorDescricao(descricaoPrm, false, Uteis.NIVELMONTARDADOS_MINIMOS);
        return lista;
    }

    /**
     * Método responsável por gerar uma lista de objetos do tipo <code>SelectItem</code> para preencher
     * o comboBox relativo ao atributo <code>Produto</code>.
     */
    public void montarListaSelectItemProduto(String prm) throws Exception {
        List resultadoConsulta = consultarProdutoPorDescricao(prm);
        Iterator i = resultadoConsulta.iterator();
        List objs = new ArrayList();
        objs.add(new SelectItem(new Integer(0), ""));
        while (i.hasNext()) {
            ProdutoVO obj = (ProdutoVO) i.next();
            objs.add(new SelectItem(obj.getCodigo(), obj.getDescricao().toString()));
        }
        setListaSelectItemProduto(objs);
    }

    /**
     * Método responsável por atualizar o ComboBox relativo ao atributo <code>Produto</code>.
     * Buscando todos os objetos correspondentes a entidade <code>Produto</code>. Esta rotina não recebe parâmetros para filtragem de dados, isto é
     * importante para a inicialização dos dados da tela para o acionamento por meio requisições Ajax.
     */
    public void montarListaSelectItemProduto() {
        try {
            montarListaSelectItemProduto("");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * Método responsável por consultar dados da entidade <code><code> e montar o atributo <code>descricao</code>
     * Este atributo é uma lista (<code>List</code>) utilizada para definir os valores a serem apresentados no ComboBox correspondente
     */
    public List consultarProdutoPorDescricao(String descricaoPrm) throws Exception {
        List lista = getFacade().getProduto().consultarPorDescricao(descricaoPrm, false, Uteis.NIVELMONTARDADOS_MINIMOS);
        return lista;
    }

    /**
     * Método responsável por inicializar a lista de valores (<code>SelectItem</code>) para todos os ComboBox's.
     */
    public void inicializarListasSelectItemTodosComboBox() {
        montarListaSelectItemProduto();
        montarListaSelectItemEmpresa();
        //     montarListaSelectItemModalidade();
    }

    public void realizarConsultaLogObjetoSelecionado() {
        LoginControle loginControle = (LoginControle) context().getExternalContext().getSessionMap().get("LoginControle");
        String nomeClasse = modalidadeVO.getClass().getSimpleName();
        nomeClasse = nomeClasse.substring(0, nomeClasse.length() - 2);
        loginControle.setNomeClasse(loginControle.getNomeEntidadeCamposLog("prt_" + nomeClasse + "_tituloForm"));
        loginControle.consultarLogObjetoSelecionado(nomeClasse.toUpperCase(), modalidadeVO.getCodigo(), 0);
    }
    
    public void realizarConsultaLogObjetoGeral() {
        modalidadeVO = new ModalidadeVO();
        realizarConsultaLogObjetoSelecionado();
    }

    /**
     * Rotina responsável por preencher a combo de consulta da telas.
     */
    public List getTipoConsultaCombo() {
        List itens = new ArrayList();
        itens.add(new SelectItem("nome", "Nome"));
        itens.add(new SelectItem("codigo", "Código"));

        return itens;
    }

    public List getTipoConsultaComboProduto() {
        List itens = new ArrayList();
        // itens.add(new SelectItem("codigo", "Código"));
        itens.add(new SelectItem("nome", "Nome"));
        return itens;
    }

    /**
     * Rotina responsável por organizar a paginação entre as páginas resultantes de uma consulta.
     */
    public String inicializarConsultar() {
        setPaginaAtualDeTodas("0/0");
        setListaConsulta(new ArrayList());
        definirVisibilidadeLinksNavegacao(0, 0);
        limparMsg();
        return "consultar";
    }

    /**
     * Operação que inicializa as Interfaces Façades com os respectivos objetos de
     * persistência dos dados no banco de dados. 
     */
    protected boolean inicializarFacades() {
        try {
            super.inicializarFacades();
            return true;
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro_conectarBD", e.getMessage());
            return false;
        }
    }

    public List<SelectItem> getListaSelectItemEmpresa() {
        if(listaSelectItemEmpresa == null){
            listaSelectItemEmpresa = new ArrayList<SelectItem>();
        }
        return listaSelectItemEmpresa;
    }

    public void setListaSelectItemEmpresa(List listaSelectItemEmpresa) {
        this.listaSelectItemEmpresa = listaSelectItemEmpresa;
    }

    public ModalidadeEmpresaVO getModalidadeEmpresaVO() {
        return modalidadeEmpresaVO;
    }

    public void setModalidadeEmpresaVO(ModalidadeEmpresaVO modalidadeEmpresaVO) {
        this.modalidadeEmpresaVO = modalidadeEmpresaVO;
    }

//    public List getListaSelectItemModalidade() {
//        return (listaSelectItemModalidade);
//    }
//     
//    public void setListaSelectItemModalidade( List listaSelectItemModalidade ) {
//        this.listaSelectItemModalidade = listaSelectItemModalidade;
//    }
    public List getListaSelectItemProduto() {
        return (listaSelectItemProduto);
    }

    public void setListaSelectItemProduto(List listaSelectItemProduto) {
        this.listaSelectItemProduto = listaSelectItemProduto;
    }
    public List getListaSelectItemTipoModalidade() {
        return listaSelectItemTipoModalidade;
    }

    public void setListaSelectItemTipoModalidade(List listaSelectItemTipoModalidade) {
        this.listaSelectItemTipoModalidade = listaSelectItemTipoModalidade;
    }

    public ProdutoSugeridoVO getProdutoSugeridoVO() {
        return produtoSugeridoVO;
    }

    public void setProdutoSugeridoVO(ProdutoSugeridoVO produtoSugeridoVO) {
        this.produtoSugeridoVO = produtoSugeridoVO;
    }

    public ModalidadeVO getModalidadeVO() {
        if(modalidadeVO == null){
            modalidadeVO = new ModalidadeVO();
        }
        return modalidadeVO;
    }

    public void setModalidadeVO(ModalidadeVO modalidadeVO) {
        this.modalidadeVO = modalidadeVO;
    }

    public String getCampoConsultaProduto() {
        return campoConsultaProduto;
    }

    public void setCampoConsultaProduto(String campoConsultaProduto) {
        this.campoConsultaProduto = campoConsultaProduto;
    }

    public List getListaConsultaProduto() {
        return listaConsultaProduto;
    }

    public void setListaConsultaProduto(List listaConsultaProduto) {
        this.listaConsultaProduto = listaConsultaProduto;
    }

    public String getValorConsultaProduto() {
        return valorConsultaProduto;
    }

    public void setValorConsultaProduto(String valorConsultaProduto) {
        this.valorConsultaProduto = valorConsultaProduto;
    }

    public void exportar(ActionEvent evt) throws Exception {
        ExportadorListaControle exportadorListaControle = (ExportadorListaControle) JSFUtilities.getFromRequest(ExportadorListaControle.class.getSimpleName());
        String paramsTableFiltrada = context().getExternalContext().getRequestParameterMap().get("paramsTableFiltrada");

        String[] split = paramsTableFiltrada.split(",");
        String campoOrdenacao = split[0].replace("[", "");
        String ordem = split[1];
        String filtro = split[2].replace("''", "");
        filtro = filtro.replace("]", "");
        List listaParaImpressao = getFacade().getModalidade().consultarParaImpressao(filtro, ordem, campoOrdenacao, getEmpresaLogado() == null ? 0 : getEmpresaLogado().getCodigo(),getSituacaoFiltro());
        exportadorListaControle.exportar(evt, listaParaImpressao, filtro, null);

    }

    public EmpresaVO getEmpresaVO() {
        return empresaVO;
    }

    public void setEmpresaVO(EmpresaVO empresaVO) {
        this.empresaVO = empresaVO;
    }
    
    public void incluirLogInclusao() throws Exception {
        try {
            modalidadeVO.setObjetoVOAntesAlteracao(new ModalidadeVO());
            modalidadeVO.setNovoObj(true);
            registrarLogObjetoVO(modalidadeVO, modalidadeVO.getCodigo(), "MODALIDADE", 0);
        } catch (Exception e) {
            registrarLogErroObjetoVO("MODALIDADE", modalidadeVO.getCodigo(), "ERRO AO GERAR LOG DE INCLUSÃO DE MODALIDADE", this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
        modalidadeVO.setNovoObj(new Boolean(false));
        modalidadeVO.registrarObjetoVOAntesDaAlteracao();
    }
    
    public void incluirLogExclusao() throws Exception {
        try {
            modalidadeVO.setObjetoVOAntesAlteracao(new ModalidadeVO());
            modalidadeVO.setNovoObj(true);
            registrarLogExclusaoTodosDadosObjetoVO(modalidadeVO, modalidadeVO.getCodigo(), "MODALIDADE", 0);
        } catch (Exception e) {
            registrarLogErroObjetoVO("MODALIDADE", modalidadeVO.getCodigo(), "ERRO AO GERAR LOG DE EXCLUSÃO DE MODALIDADE ", this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
    }

    /**
     * Inclui o log de alteração de categoria
     * @throws Exception
     */
    public void incluirLogAlteracao() throws Exception {
        try {
            registrarLogObjetoVO(modalidadeVO, modalidadeVO.getCodigo(), "MODALIDADE", 0);
            modalidadeVO.registrarObjetoVOAntesDaAlteracao();
        } catch (Exception e) {
            registrarLogErroObjetoVO("MODALIDADE", modalidadeVO.getCodigo(), "ERRO AO GERAR LOG DE ALTERAÇÃO DE MODALIDADE ", this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
        modalidadeVO.registrarObjetoVOAntesDaAlteracao();
    }

    public Boolean getTemContratoVendido() {
        return temContratoVendido;
    }

    public void setTemContratoVendido(Boolean temContratoVendido) {
        this.temContratoVendido = temContratoVendido;
    }

    public Boolean getEraCrossfit() {
        return eraCrossfit;
    }

    public void setEraCrossfit(Boolean eraCrossfit) {
        this.eraCrossfit = eraCrossfit;
    }

    public int getFiltroEmpresa() {
        return filtroEmpresa;
    }

    public void setFiltroEmpresa(int filtroEmpresa) {
        this.filtroEmpresa = filtroEmpresa;
    }

    public boolean isPermiteConsultarMultiplasEmpresas() {
        return permiteConsultarMultiplasEmpresas;
    }

    public void setPermiteConsultarMultiplasEmpresas(boolean permiteConsultarMultiplasEmpresas) {
        this.permiteConsultarMultiplasEmpresas = permiteConsultarMultiplasEmpresas;
    }

    public void adicionarTodasEmpresas() throws Exception {
        List<ModalidadeEmpresaVO> empresasAdicionar = new ArrayList<>();
        notificarRecursoEmpresa(RecursoSistema.CADASTRO_MODALIDADE_ADICIONAR_TODAS_EMPRESAS);
        List<EmpresaVO> empresasMo = consultarEmpresaPorNome("");
        if (empresasMo.size() > 0) {
            for(EmpresaVO emp : empresasMo) {
                empresasAdicionar.add(new ModalidadeEmpresaVO(emp.getCodigo(), emp.getNome(), getModalidadeVO().getCodigo()));
            }
            getModalidadeVO().setModalidadeEmpresaVOs(null);
            getModalidadeVO().setModalidadeEmpresaVOs(empresasAdicionar);
        }
    }

    public List<SelectItem> getListaSelectItemSituacao() throws Exception {
        List<SelectItem> objs = new ArrayList<SelectItem>();
        objs.add(new SelectItem("AT", "Ativo"));
        objs.add(new SelectItem("NA", "Inativo"));
        objs.add(new SelectItem("TD", "Todos"));
        return objs;
    }

    public String getSituacaoFiltro() {
        if (situacaoFiltro == null) {
            situacaoFiltro = "AT";
        }
        return situacaoFiltro;
    }

    public void setSituacaoFiltro(String situacaoFiltro) {
        this.situacaoFiltro = situacaoFiltro;
    }

    public void confirmarExcluir(){
        HttpServletRequest request = (HttpServletRequest) FacesContext.getCurrentInstance().getExternalContext().getRequest();
        String obj = request.getParameter("metodochamar");

        setMensagemDetalhada("", "");
        MensagemGenericaControle control = (MensagemGenericaControle) getControlador(MensagemGenericaControle.class);
        control.setMensagemDetalhada("", "");
        setMsgAlert("Richfaces.showModalPanel('mdlMensagemGenerica');");
        if (obj.equals("excluir")){
            control.init("Exlusão de Modalidade",
                    "Deseja Excluir a Modalidade?",
                    this, obj, "", "", "", "grupoBtnExcluir,mdlMensagemGenerica");
        }else {
            control.init("Cancelamento de Compra",
                    "Deseja Cancelar esta Compra?",
                    this, obj, "", "", "", "grupoClonarCompra,mdlMensagemGenerica");

        }

    }

    public void confirmarExcluirImagem() {
        MensagemGenericaControle control = (MensagemGenericaControle) getControlador(MensagemGenericaControle.class);
        control.setMensagemDetalhada("", "");
        setMsgAlert("Richfaces.showModalPanel('mdlMensagemGenerica');");
        control.init("Exclusão de imagem",
                "Deseja excluir a imagem da modalidade?",
                this, "excluirImagem", "", "", "", "imgUploaded");
    }

    public String excluirImagem() {
        try {
            MidiaService.getInstance()
                    .deleteObject(getKey(), MidiaEntidadeEnum.FOTO_MODADLIDADE, modalidadeVO.getCodigo().toString());
            modalidadeVO.setFotokey(null);
            if (!UteisValidacao.emptyNumber(modalidadeVO.getCodigo())) {
                getFacade().getModalidade().excluirImagem(modalidadeVO.getCodigo());
            }

            setMensagemID("msg_dados_gravados");
            setSucesso(true);
            setErro(false);
            montarSucessoGrowl("Imagem excluida com Sucesso!");
            return "editar";
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
            return "editar";
        }
    }

}

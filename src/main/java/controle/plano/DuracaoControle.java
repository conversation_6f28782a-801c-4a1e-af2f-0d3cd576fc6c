package controle.plano;

import negocio.comuns.utilitarias.SelectItemOrdemValor;
import negocio.interfaces.plano.DuracaoInterfaceFacade;
import java.util.Iterator;
import negocio.comuns.utilitarias.ControleConsulta;
import negocio.facade.jdbc.plano.Duracao;
import negocio.comuns.utilitarias.*;
import negocio.comuns.plano.*;
import javax.faces.model.SelectItem;
import java.util.List;
import java.util.ArrayList;
import controle.arquitetura.SuperControle;
import java.util.Collections;
import java.util.Enumeration;
import java.util.Hashtable;

/**
 * Classe responsável por implementar a interação entre os componentes JSF das páginas 
 * duracaoForm.jsp duracaoCons.jsp) com as funcionalidades da classe <code>Duracao</code>.
 * Implemtação da camada controle (Backing Bean).
 * @see SuperControle
 * @see Duracao
 * @see DuracaoVO
 */
public class DuracaoControle extends SuperControle {

    private DuracaoVO duracaoVO;
    /**
     * Interface <code>DuracaoInterfaceFacade</code> responsável pela interconexão da camada de controle com a camada de negócio.
     * Criando uma independência da camada de controle com relação a tenologia de persistência dos dados (DesignPatter: Façade).
     */
      public DuracaoControle() throws Exception {
        obterUsuarioLogado();
        inicializarFacades();
        setControleConsulta(new ControleConsulta());
        setMensagemID("msg_entre_prmconsulta");
    }

    /**
     * Rotina responsável por disponibilizar um novo objeto da classe <code>Duracao</code>
     * para edição pelo usuário da aplicação.
     */
    public String novo() {
        setDuracaoVO(new DuracaoVO());
        setMensagemID("msg_entre_dados");
        setSucesso(false);
        setErro(false);
        return "editar";
    }

    /**
     * Rotina responsável por disponibilizar os dados de um objeto da classe <code>Duracao</code> para alteração.
     * O objeto desta classe é disponibilizado na session da página (request) para que o JSP correspondente possa disponibilizá-lo para edição.
     */
    public String editar() {
        DuracaoVO obj = (DuracaoVO) context().getExternalContext().getRequestMap().get("duracao");
        obj.setNovoObj(new Boolean(false));
        setDuracaoVO(obj);
        setMensagemID("msg_dados_editar");
        setSucesso(false);
        setErro(false);
        return "editar";
    }

    /**
     * Rotina responsável por gravar no BD os dados editados de um novo objeto da classe <code>Duracao</code>.
     * Caso o objeto seja novo (ainda não gravado no BD) é acionado a operação <code>incluir()</code>. Caso contrário é acionado o <code>alterar()</code>.
     * Se houver alguma inconsistência o objeto não é gravado, sendo re-apresentado para o usuário juntamente com uma mensagem de erro.
     */
    public String gravar() {
        try {
            if (duracaoVO.isNovoObj().booleanValue()) {
                getFacade().getDuracao().incluir(duracaoVO);
            } else {
                getFacade().getDuracao().alterar(duracaoVO);
            }
            setMensagemID("msg_dados_gravados");
            setSucesso(true);
            setErro(false);
            return "editar";
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
            return "editar";
        }
    }

    /**
     * Rotina responsavel por executar as consultas disponiveis no JSP DuracaoCons.jsp.
     * Define o tipo de consulta a ser executada, por meio de ComboBox denominado campoConsulta, disponivel neste mesmo JSP.
     * Como resultado, disponibiliza um List com os objetos selecionados na sessao da pagina.
     */
    public String consultar() {
        try {
            super.consultar();
            List objs = new ArrayList();
            if (getControleConsulta().getCampoConsulta().equals("codigo")) {
                if (getControleConsulta().getValorConsulta().equals("")) {
                    getControleConsulta().setValorConsulta("0");
                }
                int valorInt = Integer.parseInt(getControleConsulta().getValorConsulta());
                objs = getFacade().getDuracao().consultarPorCodigo(new Integer(valorInt), true, Uteis.NIVELMONTARDADOS_TODOS);
            }
            if (getControleConsulta().getCampoConsulta().equals("numeroMeses")) {
                if (getControleConsulta().getValorConsulta().equals("")) {
                    getControleConsulta().setValorConsulta("0");
                }
                int valorInt = Integer.parseInt(getControleConsulta().getValorConsulta());
                objs = getFacade().getDuracao().consultarPorNumeroMeses(new Integer(valorInt), true, Uteis.NIVELMONTARDADOS_TODOS);
            }
//            if (getControleConsulta().getCampoConsulta().equals("percentualDesconto")) {
//                if (getControleConsulta().getValorConsulta().equals("")) {
//                    getControleConsulta().setValorConsulta("0");
//                }
//                double valorDouble = Double.parseDouble(getControleConsulta().getValorConsulta());
//                objs = duracaoFacade.consultarPorPercentualDesconto(new Double(valorDouble), true, Uteis.NIVELMONTARDADOS_TODOS);
//            }
//            if (getControleConsulta().getCampoConsulta().equals("valorEspecifico")) {
//                if (getControleConsulta().getValorConsulta().equals("")) {
//                    getControleConsulta().setValorConsulta("0");
//                }
//                double valorDouble = Double.parseDouble(getControleConsulta().getValorConsulta());
//                objs = duracaoFacade.consultarPorValorEspecifico(new Double(valorDouble), true, Uteis.NIVELMONTARDADOS_TODOS);
//            }
            objs = ControleConsulta.obterSubListPaginaApresentar(objs, controleConsulta);
            definirVisibilidadeLinksNavegacao(controleConsulta.getPaginaAtual(), controleConsulta.getNrTotalPaginas());
            setListaConsulta(objs);
            setMensagemID("msg_dados_consultados");
            setSucesso(true);
            setErro(false);
            return "consultar";
        } catch (Exception e) {
            setListaConsulta(new ArrayList());
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
            return "consultar";
        }
    }

    /**
     * Operação responsável por processar a exclusão um objeto da classe <code>DuracaoVO</code>
     * Após a exclusão ela automaticamente aciona a rotina para uma nova inclusão.
     */
    public String excluir() {
        try {
            getFacade().getDuracao().excluir(duracaoVO);
            setDuracaoVO(new DuracaoVO());
            setMensagemID("msg_dados_excluidos");
            setSucesso(true);
            setErro(false);
            return "editar";
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
            return "editar";
        }
    }

    public void irPaginaInicial() throws Exception {
        controleConsulta.setPaginaAtual(1);
        this.consultar();
    }

    public void irPaginaAnterior() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getPaginaAtual() - 1);
        this.consultar();
    }

    public void irPaginaPosterior() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getPaginaAtual() + 1);
        this.consultar();
    }

    public void irPaginaFinal() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getNrTotalPaginas());
        this.consultar();
    }   
   

    /**
     * Rotina responsável por preencher a combo de consulta da telas.
     */
    public List getTipoConsultaCombo() {
        List itens = new ArrayList();
        itens.add(new SelectItem("codigo", "Código"));
        itens.add(new SelectItem("numeroMeses", "Número de Meses"));
//        itens.add(new SelectItem("percentualDesconto", "Percentual de Desconto"));
//        itens.add(new SelectItem("valorEspecifico", "Valor Especifico"));
        return itens;
    }

    /**
     * Rotina responsável por organizar a paginação entre as páginas resultantes de uma consulta.
     */
    public String inicializarConsultar() {
        setPaginaAtualDeTodas("0/0");
        setListaConsulta(new ArrayList());
        definirVisibilidadeLinksNavegacao(0, 0);
        setMensagemID("msg_entre_prmconsulta");
        setSucesso(false);
        setErro(false);
        return "consultar";
    }

    /**
     * Operação que inicializa as Interfaces Façades com os respectivos objetos de
     * persistência dos dados no banco de dados. 
     */
    protected boolean inicializarFacades() {
        try {
            super.inicializarFacades();
            return true;
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro_conectarBD", e.getMessage());
            return false;
        }
    }

    public DuracaoVO getDuracaoVO() {
        return duracaoVO;
    }

    public void setDuracaoVO(DuracaoVO duracaoVO) {
        this.duracaoVO = duracaoVO;
    }
    
}

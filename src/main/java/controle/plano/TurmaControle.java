package controle.plano;

import acesso.webservice.DaoAuxiliar;
import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.enumeradores.TipoAntecedenciaMarcarAulaEnum;
import br.com.pactosolucoes.enumeradores.TipoColaboradorEnum;
import br.com.pactosolucoes.estrutura.exportador.controle.ExportadorListaControle;
import br.com.pactosolucoes.spivi.EventJSON;
import br.com.pactosolucoes.spivi.SpiviService;
import controle.arquitetura.SuperControle;
import controle.arquitetura.security.LoginControle;
import controle.basico.clube.MensagemGenericaControle;
import negocio.comuns.arquitetura.LogVO;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.TipoColaboradorVO;
import negocio.comuns.plano.AmbienteVO;
import negocio.comuns.plano.HorarioTurmaVO;
import negocio.comuns.plano.ModalidadeEmpresaVO;
import negocio.comuns.plano.ModalidadeVO;
import negocio.comuns.plano.NivelTurmaVO;
import negocio.comuns.plano.TurmaVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.ContratoModalidadeReferenceException;
import negocio.comuns.utilitarias.ControleConsulta;
import negocio.comuns.utilitarias.Dominios;
import negocio.comuns.utilitarias.HorariosTurma;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.ReposicaoReferenceException;
import negocio.comuns.utilitarias.SelectItemOrdemValor;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.plano.Turma;
import negocio.interfaces.arquitetura.LogInterfaceFacade;

import javax.faces.event.ActionEvent;
import javax.faces.model.SelectItem;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.Enumeration;
import java.util.Hashtable;
import java.util.Iterator;
import java.util.List;

/**
 * Classe responsável por implementar a interação entre os componentes JSF das páginas 
 * turmaForm.jsp turmaCons.jsp) com as funcionalidades da classe <code>Turma</code>.
 * Implemtação da camada controle (Backing Bean).
 * @see SuperControle
 * @see Turma
 * @see TurmaVO
 */
public class TurmaControle extends SuperControle {

    private TurmaVO turmaVO;
    protected List listaSelectItemModalidade;
    protected List listaSelectItemEmpresa;
    /**
     * Interface <code>TurmaInterfaceFacade</code> responsável pela interconexão da camada de controle com a camada de negócio.
     * Criando uma independência da camada de controle com relação a tenologia de persistência dos dados (DesignPatter: Façade).
     */
    private HorarioTurmaVO horarioTurmaVO;
    protected List listaSelectItemProfessor;
    protected List listaSelectItemAmbiente;
    protected List listaSelectItemNivelTurma;
    private String campoConsultaModalidade;
    private String valorConsultaModalidade;
    private List listaConsultaModalidade;
    private String campoConsultaColaborador;
    private String valorConsultaColaborador;
    private List listaConsultaColaborador;
    private ColaboradorVO colaborador;
    private AmbienteVO ambiente = new AmbienteVO();
    private NivelTurmaVO nivelTurma = new NivelTurmaVO();
    private TipoColaboradorVO tipoColaboradorVO;
    private HorarioTurmaVO horarioTurmaVOTemporaria = new HorarioTurmaVO();
    private String fecharModalHorarioTurma = "";
    private List<String> diasSelecionados = new ArrayList<String>();
    private boolean clone = false;
    private String ht_horaInicio = "";
    private String ht_horaFim = "";
    private List<HorariosTurma> horariosIncluidos = new ArrayList<HorariosTurma>();
    private List<HorarioTurmaVO> horariosParaGravar = new ArrayList<HorarioTurmaVO>();
    private List<TurmaVO> horariosConflitantes = new ArrayList<TurmaVO>();
    private boolean permiteAlterarModalidade = true;
    private List<SelectItem> listaSelectItemTipoAntecedenciaMarcarAula;
    private String abaSelecionada = "abaDadosTurma";
    private String tipoTurma;
    private List<SelectItem> listaTipoTurma = new ArrayList<SelectItem>();
    private String msgAlert;
    private EventJSON aulaSpiviSelecionada;
    private boolean integracaoSpivi = false;

    public TurmaControle() throws Exception {
        obterUsuarioLogado();
        inicializarFacades();
        setControleConsulta(new ControleConsulta());
        montarMensagemExceptionControle("");
        setSucesso(false);
        setErro(false);
        montarListaTipoTurma();
        integracaoSpivi = isIntegracaoSpiviHabilitada();
    }

    public void inicializarUsuarioLogado() {
        try {
            turmaVO.setUsuarioVO(getUsuarioLogado());
        } catch (Exception ignored) {
        }
    }

    private void montarSelectItemTipoAntecedenciaMarcarAula() throws Exception{
        this.listaSelectItemTipoAntecedenciaMarcarAula = new ArrayList<SelectItem>();
        for (TipoAntecedenciaMarcarAulaEnum tipo: TipoAntecedenciaMarcarAulaEnum.values()){
            this.listaSelectItemTipoAntecedenciaMarcarAula.add(new SelectItem(tipo.getCodigo(), tipo.getDescricao()));
        }
    }

    private void montarListaTipoTurma(){
        if (this.listaTipoTurma.isEmpty()) {
            this.listaTipoTurma.add(new SelectItem("VI", "Vigente"));
            this.listaTipoTurma.add(new SelectItem("NVI",  "Não Vigente"));
            this.listaTipoTurma.add(new SelectItem("TD",  "Todos"));
        }
    }

    private EmpresaVO getEmpresaVO() throws Exception {
        return DaoAuxiliar.retornarAcessoControle(getKey()).getEmpresaDao().consultarPorChavePrimaria(getUsuarioLogado().getCodEmpresaLogada(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
    }

    /**
     * Rotina responsável por disponibilizar um novo objeto da classe <code>Turma</code>
     * para edição pelo usuário da aplicação.
     */
    public String novo() {
        try {
            setTurmaVO(new TurmaVO());
            inicializarUsuarioLogado();
            inicializarListasSelectItemTodosComboBox();
            setHorarioTurmaVO(new HorarioTurmaVO());
            setAmbiente(new AmbienteVO());
            setColaborador(new ColaboradorVO());
            setNivelTurma(new NivelTurmaVO());
            setCampoConsultaModalidade("");
            setValorConsultaModalidade("");
            setListaConsultaModalidade(new ArrayList());
            setCampoConsultaColaborador("");
            setValorConsultaColaborador("");
            setTipoColaboradorVO(new TipoColaboradorVO());
            setListaConsultaColaborador(new ArrayList());
            setDiasSelecionados(new ArrayList<String>());
            limparMsg();
            setPermiteAlterarModalidade(true);
            return "editar";
        } catch (Exception e) {
            montarMensagemExceptionControle("msg_erro", e);
            setSucesso(false);
            setErro(true);
            return "";
        }
    }

    public void inativarTurmas(){

        try {
            getFacade().getMatriculaAlunoHorarioTurma().inativarMatriculaAlunosHorarioTurmas();
            getFacade().getTurma().inativarTurmas();
            montarSucessoGrowl("Turmas Inativas com Sucesso!");
        }catch(Exception e){
            montarMensagemExceptionControle("msg_erro", e);
            setSucesso(false);
            setErro(true);
            montarErro(e);
        }
    }

    public void clonar() throws CloneNotSupportedException {
        TurmaVO turmaClone = turmaVO.clone();
        setTurmaVO(turmaClone);
        turmaVO.setCodigo(0);
        turmaVO.setNovoObj(true);
        turmaVO.setDescricao("Cópia " + getTurmaVO().getDescricao());
        turmaVO.setIdentificador("Cópia " + getTurmaVO().getIdentificador());
    }

    public void clonarHorarioTurma() throws Exception {
        HorarioTurmaVO obj = (HorarioTurmaVO) context().getExternalContext().getRequestMap().get("horarioTurma");
        HorarioTurmaVO objClone = obj.clone();
        setClone(true);
        objClone.setBotaoEditar(true);
        objClone.setCodigo(0);
        objClone.setNovoObj(true);
        setHorarioTurmaVO(objClone);
    }

    public boolean isIntegracaoSpiviHabilitada() throws Exception {
        return getFacade().getEmpresa().isIntegracaoSpivi(getEmpresaLogado().getCodigo());
    }

    /**
     * Rotina responsável por disponibilizar os dados de um objeto da classe <code>Turma</code> para alteração.
     * O objeto desta classe é disponibilizado na session da página (request) para que o JSP correspondente possa disponibilizá-lo para edição.
     */
    public String editar() {
        Integer codigoConsulta = Integer.parseInt(request().getParameter("chavePrimaria"));
        try {
            TurmaVO obj = getFacade().getTurma().consultarPorChavePrimaria(codigoConsulta, Uteis.NIVELMONTARDADOS_TODOS);
            inicializarAtributosRelacionados(obj);
            obj.setNovoObj(false);
            setTurmaVO(obj);
            getTurmaVO().registrarObjetoVOAntesDaAlteracao();
            setPermiteAlterarModalidade(!getFacade().getTurma().existemMatriculasNaTurma(getTurmaVO())); // a modalidade não pode ser alterada se ja existe matricula na turma
            inicializarUsuarioLogado();
            inicializarListasSelectItemTodosComboBox();
            setHorarioTurmaVO(new HorarioTurmaVO());
            setCampoConsultaModalidade("");
            setValorConsultaModalidade("");
            setListaConsultaModalidade(new ArrayList());
            setCampoConsultaColaborador("");
            setValorConsultaColaborador("");
            setListaConsultaColaborador(new ArrayList());
            setDiasSelecionados(new ArrayList<String>());
            setClone(false);
            integracaoSpivi = isIntegracaoSpiviHabilitada();
            montarMensagemExceptionControle("msg_dados_consultados");
        } catch (Exception e) {
            setErro(true);
            setSucesso(false);
            montarMensagemExceptionControle("msg_erro", e);
            return "";
        }
        return "editar";
    }

    /**
     * Método responsável inicializar objetos relacionados a classe <code>TurmaVO</code>.
     * Esta inicialização é necessária por exigência da tecnologia JSF, que não trabalha com valores nulos para estes atributos.
     */
    public void inicializarAtributosRelacionados(TurmaVO obj) {
        if (obj.getModalidade() == null) {
            obj.setModalidade(new ModalidadeVO());
        }
    }

    public void gravarAmbiente() {
        try {
            getFacade().getAmbiente().incluir(ambiente);
            montarListaSelectItemAmbiente();
            horarioTurmaVO.getAmbiente().setCodigo(ambiente.getCodigo());
            setAmbiente(new AmbienteVO());
            montarMensagemExceptionControle("");
            setSucesso(true);
            setErro(false);
        } catch (Exception e) {
            montarMensagemExceptionControle("msg_erro", e);
            setSucesso(false);
            setErro(true);
        }
    }

    public void gravarNivelTurma() {
        try {
            getFacade().getNivelTurma().incluir(nivelTurma);
            montarListaSelectItemNivelTurma();
            horarioTurmaVO.getNivelTurma().setCodigo(nivelTurma.getCodigo());
            setNivelTurma(new NivelTurmaVO());
            montarMensagemExceptionControle("");
            setSucesso(true);
            setErro(false);
        } catch (Exception e) {
            montarMensagemExceptionControle("msg_erro", e);
            setSucesso(false);
            setErro(true);
        }
    }

    /**
     * Rotina responsavel por executar as consultas disponiveis no JSP TurmaCons.jsp.
     * Define o tipo de consulta a ser executada, por meio de ComboBox denominado campoConsulta, disponivel neste mesmo JSP.
     * Como resultado, disponibiliza um List com os objetos selecionados na sessao da pagina.
     */
    public String consultar() {
        try {
            super.consultar();
            List objs = new ArrayList();
            if (getControleConsulta().getCampoConsulta().equals("codigo")) {
                if (getControleConsulta().getValorConsulta().equals("")) {
                    getControleConsulta().setValorConsulta("0");
                }
                int valorInt = Integer.parseInt(getControleConsulta().getValorConsulta());
                objs = getFacade().getTurma().consultarPorCodigo(valorInt, getEmpresaLogado().getCodigo(), true, Uteis.NIVELMONTARDADOS_TODOS);
            }
            if (getControleConsulta().getCampoConsulta().equals("identificador")) {
                objs = getFacade().getTurma().consultarPorIdentificador(getControleConsulta().getValorConsulta(), getEmpresaLogado().getCodigo(), true, Uteis.NIVELMONTARDADOS_TODOS);
            }
            if (getControleConsulta().getCampoConsulta().equals("nome")) {
                objs = getFacade().getTurma().consultarPorDescricaoTurma(getControleConsulta().getValorConsulta(), getEmpresaLogado().getCodigo(), true, Uteis.NIVELMONTARDADOS_TODOS);
            }
            if (getControleConsulta().getCampoConsulta().equals("nomeModalidade")) {
                objs = getFacade().getTurma().consultarPorNomeModalidade(getControleConsulta().getValorConsulta(), getEmpresaLogado().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);
            }
            if (getControleConsulta().getCampoConsulta().equals("dataInicialVigencia")) {
                Date valorData = Uteis.getDate(getControleConsulta().getValorConsulta());
                objs = getFacade().getTurma().consultarPorDataInicialVigencia(Uteis.getDateTime(valorData, 0, 0, 0), Uteis.getDateTime(valorData, 23, 59, 59), getEmpresaLogado().getCodigo(), true, Uteis.NIVELMONTARDADOS_TODOS);
            }
            if (getControleConsulta().getCampoConsulta().equals("idadeMinima")) {
                if (getControleConsulta().getValorConsulta().equals("")) {
                    getControleConsulta().setValorConsulta("0");
                }
                int valorInt = Integer.parseInt(getControleConsulta().getValorConsulta());
                objs = getFacade().getTurma().consultarPorIdadeMinima(valorInt, getEmpresaLogado().getCodigo(), true, Uteis.NIVELMONTARDADOS_TODOS);
            }
            if (getControleConsulta().getCampoConsulta().equals("idadeMaxima")) {
                if (getControleConsulta().getValorConsulta().equals("")) {
                    getControleConsulta().setValorConsulta("0");
                }
                int valorInt = Integer.parseInt(getControleConsulta().getValorConsulta());
                objs = getFacade().getTurma().consultarPorIdadeMaxima(valorInt, getEmpresaLogado().getCodigo(), true, Uteis.NIVELMONTARDADOS_TODOS);
            }
            if (getControleConsulta().getCampoConsulta().equals("nivel")) {
                objs = getFacade().getTurma().consultarPorNivelTurma(getControleConsulta().getValorConsulta(), getEmpresaLogado().getCodigo(), true, Uteis.NIVELMONTARDADOS_TODOS);
            }
            objs = ControleConsulta.obterSubListPaginaApresentar(objs, controleConsulta);
            definirVisibilidadeLinksNavegacao(controleConsulta.getPaginaAtual(), controleConsulta.getNrTotalPaginas());
            setListaConsulta(objs);
            limparMsg();
            return "consultar";
        } catch (Exception e) {
            setListaConsulta(new ArrayList());
            montarMensagemExceptionControle("msg_erro", e);
            setSucesso(false);
            setErro(true);
            return "consultar";
        }
    }

    /**
     * Operação responsável por processar a exclusão um objeto da classe <code>TurmaVO</code>
     * Após a exclusão ela automaticamente aciona a rotina para uma nova inclusão.
     */
    public String excluir() {
        try {
            getFacade().getTurma().excluir(turmaVO);
            incluirLogExclusaoTurma();
            novo();
            montarMensagemExceptionControle("msg_dados_excluidos");
            setSucesso(true);
            setErro(false);
            return "consultar";
        } catch (ReposicaoReferenceException rrex) {
            montarMensagemExceptionControle("msg_turma_reposicao_associdada", rrex);
            return "";
        } catch (ContratoModalidadeReferenceException cmex) {
            montarMensagemExceptionControle("msg_turma_contrato_associdado", cmex);
            return "";
        } catch (Exception e) {
            montarMensagemExceptionControle("msg_erro", e);
            return "editar";
        }
    }

    /**
     * Rotina responsável por gravar no BD os dados editados de um novo objeto da classe <code>Turma</code>.
     * Caso o objeto seja novo (ainda não gravado no BD) é acionado a operação <code>incluir()</code>. Caso contrário é acionado o <code>alterar()</code>.
     * Se houver alguma inconsistência o objeto não é gravado, sendo re-apresentado para o usuário juntamente com uma mensagem de erro.
     */
    public String gravar() {
        try {
            boolean existe = false;
            EmpresaVO empresaTurma = getTurmaVO().getEmpresa();
            ModalidadeVO modalidadeVO = getFacade().getModalidade().consultarPorChavePrimaria(getTurmaVO().getModalidade().getCodigo(), Uteis.NIVELMONTARDADOS_TELACONSULTA);
            for (Object obj : modalidadeVO.getModalidadeEmpresaVOs()) {
                ModalidadeEmpresaVO modalidadeEmpresaVO = (ModalidadeEmpresaVO) obj;
                if (modalidadeEmpresaVO.getEmpresa().getCodigo().equals(empresaTurma.getCodigo())) {
                    existe = true;
                    break;
                }
            }

            if (!existe) {
                throw new Exception("A modalidade desta turma não está configurado na empresa");
            }


            turmaVO.setNivelDescontos();

            //Método que valida os horários das turmas para não haver duplicidade ou conflitos de horários.
            setHorarioTurmaVO(new HorarioTurmaVO());
            setHorarioTurmaVOTemporaria(null);
            if (turmaVO.isNovoObj()) {
                getFacade().getTurma().incluir(turmaVO);
                incluirLogInclusaoTurma();
                if (getHorariosParaGravar().size() > 0) {
                    for (HorarioTurmaVO ht : getHorariosParaGravar()) {
                        ht.setTurma(turmaVO.getCodigo());
                        ht.setIdentificadorTurma(turmaVO.getIdentificador());
//                        validarNoBancoHorariosIguais(ht);
//                        validacoesHorarioTurmaBancoDados(ht);
                        validarExistenciaRegistroHorarioTurma(ht);
                        getFacade().getHorarioTurma().incluir(ht);
                    }
                }
            } else {
                if (getTurmaVO().getObjetoVOAntesAlteracao() != null && !((TurmaVO) getTurmaVO().getObjetoVOAntesAlteracao()).getDataFinalVigencia().equals(getTurmaVO().getDataFinalVigencia())){
                    if (getTurmaVO().getDataFinalVigencia().before(Calendario.hoje()) && existeAlunosNaTurma(getTurmaVO())){
                        throw new ConsistirException("Não é possível alterar a vigência da turma, existem alunos ativos.");
                    }
                }
                getFacade().getTurma().alterar(turmaVO);
                incluirLogAlteracao();
            }
            setHorariosParaGravar(new ArrayList<HorarioTurmaVO>());
            montarMensagemExceptionControle("msg_dados_gravados", null);
            return "editar";
        } catch (NumberFormatException e) {
            montarMensagemExceptionControle("msg_erro", new NumberFormatException("Informe horas válidas para os horários de turma"));
            return "editar";
        } catch (Exception e) {
            montarMensagemExceptionControle("msg_erro", e);
            return "editar";
        }
    }

    private void relacionarAulaSpivi(HorarioTurmaVO horarioTurmaVO) throws Exception {
        List<HorarioTurmaVO> horarioList = new ArrayList<HorarioTurmaVO>();
        horarioList.add(horarioTurmaVO);
        relacionarAulasSpivi(horarioList);
    }

    private void relacionarAulasSpivi(List<HorarioTurmaVO> horarios) throws Exception {

        boolean encontrouAulaSpiviParaHorarioTurma = false;
        List<EventJSON> aulasSpivi = consultarAulasSpiviSemana();
        for (HorarioTurmaVO horario: horarios) {
            encontrouAulaSpiviParaHorarioTurma = false;
            for (EventJSON aulaSpivi: aulasSpivi) {
                String spiviHorarioIndice = aulaSpivi.getStartDateDayOfWeek() + "-" + aulaSpivi.getStartTime();
                String horarioTurmaIndice = horario.diaSemanaNumero().toString() + "-"+ horario.getHoraInicial();
                if( spiviHorarioIndice.equals(horarioTurmaIndice) ){
                    horario.setSpiviEventID(aulaSpivi.getEventID());
                    horario.setSpiviEvent(aulaSpivi);
                    encontrouAulaSpiviParaHorarioTurma = true;
                    continue;
                }
            }

            if(!encontrouAulaSpiviParaHorarioTurma){
                throw new Exception("Não foi encontrada uma aula SPIVI com este horário: Hora Inicial "+horario.getHoraInicial()+" e Hora Final "+horario.getHoraFinal()+". " +
                        "Todos os horários desta turma devem estar cadastrados na plataforma SPIVI. ");
            }
        }
    }

    private List<EventJSON> consultarAulasSpiviSemana() throws Exception {
        SpiviService spiviService = DaoAuxiliar.retornarAcessoControle(getKey()).getSpiviService();
        return spiviService.obterAulas(Uteis.obterPrimeiroDiaSemanaAtual(), Uteis.obterUltimoDiaSemanaAtual(), getEmpresaLogado());
    }

    private boolean existeAlunosNaTurma(TurmaVO turma) throws Exception {
        return getFacade().getTurma().existemAlunosNaTurma(turma);
    }

        public void adicionarNovoHorarioTurma() throws Exception {
        setHorarioTurmaVO(new HorarioTurmaVO());
        setHorarioTurmaVOTemporaria(new HorarioTurmaVO());
        setDiasSelecionados(new ArrayList<String>());
        montarMensagemExceptionControle("msg_entre_dados");
        setHorariosIncluidos(new ArrayList<HorariosTurma>());
     }
     /**
     * Operação responsável por adicionar um novo objeto da classe <code>HorarioTurmaVO</code>
     * ao List <code>horarioTurmaVOs</code>. Utiliza o atributo padrão de consulta
     * da classe <code>HorarioTurma</code> - getIdentificador() - como identificador (key) do objeto no List.
     * @param obj    Objeto da classe <code>HorarioTurmaVO</code> que será adiocionado ao Hashtable correspondente.
     */
    public void adicionarObjHorarioTurmaVOs(HorarioTurmaVO obj, HorarioTurmaVO objTemporarioAnterior) throws Exception {
        HorarioTurmaVO.validarDados(obj);
        if (!obj.isBotaoEditar() || isClone()) {
            turmaVO.getHorarioTurmaVOs().add(obj);
        }
    }

    public void adicionarHorarioNaLista(HorarioTurmaVO hTVO) throws Exception {
        if (!getTurmaVO().getCodigo().equals(0)) {
            hTVO.setTurma(getTurmaVO().getCodigo());
        }
        adicionarObjHorarioTurmaVOs(hTVO, getHorarioTurmaVOTemporaria());
    }

    public void gravarHorario() throws Exception {
        montarMensagemExceptionControle("msg_erro", null);
        setMensagemDetalhada("msg_erro", "");
        setSucesso(true);
        setErro(false);

        try {
            if(turmaVO.getCodigo() == null || turmaVO.getCodigo() == 0){
                throw new Exception("Antes de adicionar o horário, grave as informações da turma.");
            }
            setHorariosConflitantes(new ArrayList<TurmaVO>());
            //Criando a lista de horários que irão ser inseridos
            List<HorarioTurmaVO> horarioTurmaVOsCriados = new ArrayList<HorarioTurmaVO>();
            //Faz validação das horas informadas
            if (getHorariosIncluidos().size() < 1) {
                Calendario.validarHoras(getHorarioTurmaVOTemporaria().getHoraInicial(), getHorarioTurmaVOTemporaria().getHoraFinal());
            }
            //Seta o colaborador informado
            if (getHorarioTurmaVOTemporaria().getProfessor().getCodigo() != 0) {
                Integer campoProfessor = getHorarioTurmaVOTemporaria().getProfessor().getCodigo();
                ColaboradorVO colaboradorVO = getFacade().getColaborador().consultarPorChavePrimaria(campoProfessor, Uteis.NIVELMONTARDADOS_TODOS);
                getHorarioTurmaVOTemporaria().setProfessor(colaboradorVO);
            }
            //Seta o ambiente escolhido
            if (getHorarioTurmaVOTemporaria().getAmbiente().getCodigo() != 0) {
                Integer campoAmbiente = getHorarioTurmaVOTemporaria().getAmbiente().getCodigo();
                AmbienteVO ambienteVO = getFacade().getAmbiente().consultarPorChavePrimaria(campoAmbiente, Uteis.NIVELMONTARDADOS_TODOS);
                getHorarioTurmaVOTemporaria().setAmbiente(ambienteVO);
            }
            //Seta o nivel de turma escolhido
            if (getHorarioTurmaVOTemporaria().getNivelTurma().getCodigo() != 0) {
                Integer campoNivelTurma = getHorarioTurmaVOTemporaria().getNivelTurma().getCodigo();
                NivelTurmaVO nivelTurmaVO = getFacade().getNivelTurma().consultarPorChavePrimaria(campoNivelTurma, Uteis.NIVELMONTARDADOS_TODOS);
                getHorarioTurmaVOTemporaria().setNivelTurma(nivelTurmaVO);
            }
            getHorarioTurmaVOTemporaria().setTurma(turmaVO.getCodigo());
            getHorarioTurmaVOTemporaria().setIdentificadorTurma(turmaVO.getIdentificador());

            Integer codigoHorarioExistente = getFacade().getHorarioTurma().existeHorarioTurma(turmaVO.getEmpresa().getCodigo(), getHorarioTurmaVOTemporaria());

            if(codigoHorarioExistente > 0
                    && (getHorarioTurmaVOTemporaria().getCodigo().equals(0)|| !codigoHorarioExistente.equals(getHorarioTurmaVOTemporaria().getCodigo()))
                    && (!turmaVO.getEmpresa().isPermiteHorariosConcorrentesParaProfessor() && !turmaVO.getEmpresa().isProfessorEmAmbientesDiferentesMesmoHorario())){
                throw new Exception("Horário turma já cadastrado (Não é permitido horário com mesmo professor, horário, nível e ambiente)" );
            }else{
                boolean trocaDeProfessor = false;
                if (getHorarioTurmaVOTemporaria().isBotaoEditar() && getHorarioTurmaVOTemporaria().getCodigo() != 0) {
                    getHorarioTurmaVOTemporaria().setNovoObj(false);
//                validarNoBancoHorariosIguais(getHorarioTurmaVOTemporaria());
//              validacoesHorarioTurmaBancoDados(getHorarioTurmaVOTemporaria());
                    validarExistenciaRegistroHorarioTurma(getHorarioTurmaVOTemporaria());
                    if (getHorariosConflitantes().size() > 0) {
                        setFecharModalHorarioTurma("Richfaces.showModalPanel('conflitos');");
                    } else {
                        if(getHorarioTurmaVOTemporaria().getHoraInicial().length() < 5 || getHorarioTurmaVOTemporaria().getHoraFinal().length() < 5){
                            throw new Exception("Horário inicial ou Horário final inválido!");
                        }

                        getFacade().getHorarioTurma().alterar(getHorarioTurmaVOTemporaria());
                        trocarHistorico();
                        getTurmaVO().getHorarioTurmaVOs().set(getTurmaVO().getHorarioTurmaVOs().indexOf(getHorarioTurmaVO()), getHorarioTurmaVOTemporaria());
                        setFecharModalHorarioTurma("Richfaces.hideModalPanel('panelHorarioTurma');");
                        montarMensagemExceptionControle("msg_dados_gravados", null);
                        try {
                            horarioTurmaVO.setUsuarioVO(turmaVO.getUsuarioVO());
                            montarLogDadosHorario(getHorarioTurmaVOTemporaria());
                            //    registrarLogObjetoVO(getHorarioTurmaVOTemporaria(), turmaVO.getCodigo(), "TURMA", 0);
                        } catch (Exception e) {
                            registrarLogErroObjetoVO("TURMA", horarioTurmaVO.getCodigo(), "ERRO AO GERAR LOG DE ALTERAÇÃO DE HORÁRIO TURMA", this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
                            e.printStackTrace();
                        }
                        setSucesso(true);
                        setErro(false);
                    }
                } else {
                    //Adicionando o tratamento para a criação de vários dias.
                    for (String diaSelecionado : getDiasSelecionados()) {
                        for (HorariosTurma horariosTurma : getHorariosIncluidos()) {
                            HorarioTurmaVO htVO = getHorarioTurmaVOTemporaria().clone();
                            htVO.setHoraInicial(horariosTurma.getHoraInicial());
                            htVO.setHoraFinal(horariosTurma.getHoraFinal());

                            htVO.setDiaSemana(diaSelecionado);
                            HorarioTurmaVO.montarDadosDiaSemanaNumero(htVO);

//                        validarNoBancoHorariosIguais(htVO);


                            validarExistenciaRegistroHorarioTurma(htVO);
                            // validacoesHorarioTurmaBancoDados(htVO);
                            horarioTurmaVOsCriados.add(htVO);
                        }
                    }

                    if (getHorariosConflitantes().size() > 0) {
                        setFecharModalHorarioTurma("Richfaces.showModalPanel('conflitos');");
                    } else {

                        //Tratando o clone.
                        if (getHorarioTurmaVOTemporaria().isBotaoEditar() && getHorarioTurmaVOTemporaria().getCodigo() == 0) {
                            if (getTurmaVO().getCodigo() != 0) {
                                getFacade().getHorarioTurma().incluir(getHorarioTurmaVOTemporaria());
                                getFacade().getHpService().inserirHistoricoMudancaProfessor(getHorarioTurmaVOTemporaria(), null, getTurmaVO().getDataInicialVigencia());
                                montarLogDadosHorario(getHorarioTurmaVOTemporaria());
                            } else {
                                getHorariosParaGravar().add(getHorarioTurmaVOTemporaria());
                            }
                            adicionarHorarioNaLista(getHorarioTurmaVOTemporaria());
                        }

                        for (HorarioTurmaVO ht : horarioTurmaVOsCriados) {
                            if (getTurmaVO().getCodigo() != 0) {
                                getFacade().getHorarioTurma().incluir(ht);
                                getFacade().getHpService().inserirHistoricoMudancaProfessor(ht, null, getTurmaVO().getDataInicialVigencia());
                                //  montarLogDadosHorario(ht);
                                //    montarLogDadosHorario(getHorarioTurmaVOTemporaria());
                            } else {
                                getHorariosParaGravar().add(ht);
                            }
                            adicionarHorarioNaLista(ht);
                            //setLogAdicionarNovoHorarioTurma();
                        }

                        setFecharModalHorarioTurma("Richfaces.hideModalPanel('panelHorarioTurma');");
                        this.setHorarioTurmaVO(new HorarioTurmaVO());
                        getHorarioTurmaVO().setCodigo(0);
                        getHorarioTurmaVO().setNovoObj(true);
                        //alterando a situacao para ativo para o recurso de exclusao
                        getHorarioTurmaVO().setSituacao("AT");
                    }
                    montarMensagemExceptionControle("msg_dados_gravados", null);
                    setSucesso(true);
                    setErro(false);
                }
            }




        } catch (Exception e) {
            montarMensagemExceptionControle("msg_erro", e);
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
            setFecharModalHorarioTurma("Richfaces.showModalPanel('panelHorarioTurma');");
        }
    }

    public void trocarHistorico() throws Exception{
        try {
            boolean trocaDeProfessor = !getHorarioTurmaVOTemporaria().getProfessor().getCodigo()
                    .equals(getHorarioTurmaVO().getProfessor().getCodigo());
            if(trocaDeProfessor){
                getFacade().getHpService().inserirHistoricoMudancaProfessor(getHorarioTurmaVOTemporaria(), getHorarioTurmaVO().getProfessor().getCodigo(),
                        getTurmaVO().getDataInicialVigencia());
            }
        }catch (Exception e){
            //
        }

    }

    public void incluirLogDescricao(HorarioTurmaVO horario,Boolean dSemana,Boolean professor) throws Exception{

        LogInterfaceFacade logFacade = getFacade().getLog();

        LogVO logDescricao = new LogVO();
        logDescricao.setChavePrimaria(horario.getTurma().toString());
        logDescricao.setOperacao("ALTERAÇÃO");
        if(horario.getNovoObj())
        logDescricao.setOperacao("INCLUSÃO");
        logDescricao.setNomeEntidade("TURMA");
        logDescricao.setNomeEntidadeDescricao("Turma");
        logDescricao.setPessoa(0);
        logDescricao.setResponsavelAlteracao(turmaVO.getUsuarioVO().getNome());
        logDescricao.setUserOAMD(getUsuarioLogado().getUserOamd());
        //LOG DESCRIÇÃO TURMA TURMA CODIGO
        logDescricao.setNomeCampo("*Codigo Horario Turma:");
        logDescricao.setValorCampoAnterior(horario.getCodigo().toString());
        logDescricao.setValorCampoAlterado(horario.getCodigo().toString());
        logFacade.incluirSemCommit(logDescricao);
        //LOG DESCRIÇÃO TURMA TURMA DIA DA SEMANA
        if(dSemana) {
            logDescricao.setNomeCampo("*Dia da Semana:");
            logDescricao.setValorCampoAnterior(horario.getDiaSemana_Apresentar());
            logDescricao.setValorCampoAlterado(horario.getDiaSemana_Apresentar());
            logFacade.incluirSemCommit(logDescricao);
        }
        //LOG DESCRIÇÃO TURMA TURMA PROFESSOR
        if(professor) {
            logDescricao.setNomeCampo("*Professor:");
            logDescricao.setValorCampoAnterior((horario.getProfessor().getPessoa_Apresentar()));
            logDescricao.setValorCampoAlterado(horario.getProfessor().getPessoa_Apresentar());
            logFacade.incluirSemCommit(logDescricao);
        }
    }
    public void montarLogDadosHorario(HorarioTurmaVO objeto) throws Exception{
        HorarioTurmaVO itemAntesAlteracao = null;
        HorarioTurmaVO horario = objeto;
        itemAntesAlteracao = (HorarioTurmaVO)objeto.getObjetoVOAntesAlteracao();
        if(objeto.getObjetoVOAntesAlteracao()== null){
          objeto.setObjetoVOAntesAlteracao(new HorarioTurmaVO());
          objeto.setNovoObj(true);
            itemAntesAlteracao = null;
        }
        List<LogVO> lista = horario.gerarLogAlteracaoObjetoVO();
        LogInterfaceFacade logFacade = getFacade().getLog();
        Boolean dSemana = true;
        Boolean professor = true;
        for(LogVO log : lista) {

            log.setChavePrimaria(turmaVO.getCodigo().toString());
            log.setNomeEntidade("TURMA");
            log.setNomeEntidadeDescricao("TURMA");

            if (log.getNomeCampo().replace("*","").replaceAll(":","").trim().equals("Professor") ) {


                if(itemAntesAlteracao==null)
                    log.setValorCampoAnterior(horario.getProfessor().getPessoa_Apresentar());
                else
                    log.setValorCampoAnterior(itemAntesAlteracao.getProfessor().getPessoa_Apresentar());
                log.setValorCampoAlterado(horario.getProfessor().getPessoa_Apresentar());
                logFacade.incluirSemCommit(log);
                professor = false;
            }else
            if (log.getNomeCampo().replace("*","").replaceAll(":", "").trim().equals("Dia da Semana")) {


                if(itemAntesAlteracao==null)
                    log.setValorCampoAnterior(horario.getDiaSemana_Apresentar());
                else
                    log.setValorCampoAnterior(itemAntesAlteracao.getDiaSemana_Apresentar());

                    log.setValorCampoAlterado(horario.getDiaSemana_Apresentar());
                logFacade.incluirSemCommit(log);
                dSemana= false;

            }else
            if (log.getNomeCampo().replace("*","").replaceAll(":","").trim().equals("Nível da Turma")) {


                if(itemAntesAlteracao==null)
                    log.setValorCampoAnterior(horario.getNivelTurma().getDescricao());
                else
                    log.setValorCampoAnterior(itemAntesAlteracao.getNivelTurma().getDescricao());

                log.setValorCampoAlterado(horario.getNivelTurma().getDescricao());
                logFacade.incluirSemCommit(log);

            }else
            if (log.getNomeCampo().replace("*","").replaceAll(":","").trim().equals("Ambiente")) {


                if(itemAntesAlteracao==null)
                    log.setValorCampoAnterior(horario.getAmbiente().getDescricao());
                else
                    log.setValorCampoAnterior(itemAntesAlteracao.getAmbiente().getDescricao());

                log.setValorCampoAlterado(horario.getAmbiente().getDescricao());
                logFacade.incluirSemCommit(log);

            }else
            if (log.getNomeCampo().replace("*","").replaceAll(":","").trim().equals("Hora Final") && !log.getValorCampoAlterado().trim().equals(log.getValorCampoAnterior().trim())){
                log.setValorCampoAnterior(((HorarioTurmaVO)horario.getObjetoVOAntesAlteracao()).getHoraFinal());
                        logFacade.incluirSemCommit(log);
            }else
            if (log.getNomeCampo().replace("*","").replaceAll(":","").trim().equals("Hora Inicial") && !log.getValorCampoAlterado().trim().equals(log.getValorCampoAnterior().trim())){
                log.setValorCampoAnterior(((HorarioTurmaVO) horario.getObjetoVOAntesAlteracao()).getHoraInicial());
                logFacade.incluirSemCommit(log);
            }else
            if (log.getNomeCampo().replace("*","").replaceAll(":","").trim().equals("identificadorTurma")){
                log.setValorCampoAnterior(log.getValorCampoAlterado());
                logFacade.incluirSemCommit(log);
            }else
            if (log.getNomeCampo().replace("*","").replaceAll(":","").trim().equals("Turma")){
                log.setValorCampoAnterior(log.getValorCampoAlterado());
                logFacade.incluirSemCommit(log);
            }else
            if (log.getNomeCampo().replace("*","").replaceAll(":","").trim().equals(" Nº Máx. de Alunos")){
                log.setValorCampoAnterior(log.getValorCampoAlterado());
                logFacade.incluirSemCommit(log);
            }else
            if(!log.getNomeCampo().replace("*","").replaceAll(":","").trim().equals("diaSemanaNumero")){
                logFacade.incluirSemCommit(log);
            }


        }
        incluirLogDescricao(horario,dSemana,professor);


    }
    private void validarSeProfessorEstaEmOutroAmbiente(HorarioTurmaVO htVO) throws Exception {
        HorarioTurmaVO horarioTurma = getFacade().getHorarioTurma().consultarPorProfessorDiaDaSemanaHorarioInicialFinalForaDoAmbiente(htVO, turmaVO.getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        if (horarioTurma != null && !htVO.getCodigo().equals(horarioTurma.getCodigo())) {
            TurmaVO turmaVO1 = getFacade().getTurma().consultarPorChavePrimaria(horarioTurma.getTurma(), Uteis.NIVELMONTARDADOS_TODOS);
            adicionarListaDeErros(turmaVO1, htVO);
        }
    }

    private void validarSeProfessorEstaComHorarioConcocorrente(HorarioTurmaVO htVO) throws Exception {
        List<HorarioTurmaVO> horariosTurma = getFacade().getHorarioTurma().consultarHorariosConflitantesParaProfessorSemAmbiente(htVO, turmaVO.getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);
        if ((horariosTurma.size() == 1 && (!horariosTurma.get(0).getCodigo().equals(htVO.getCodigo()))) || horariosTurma.size() > 1) {
            for (HorarioTurmaVO ht : horariosTurma) {
                TurmaVO turmaVO1 = getFacade().getTurma().consultarPorChavePrimaria(ht.getTurma(), Uteis.NIVELMONTARDADOS_TODOS);
                adicionarListaDeErros(turmaVO1, ht);
            }
        }
    }

    private void adicionarListaDeErros(TurmaVO turmaVO, HorarioTurmaVO horarioTurmaVO) {
        boolean turmaEncontrada = false;
        TurmaVO turmaTemp = null;
        for (TurmaVO turma : getHorariosConflitantes()) {
            if (turma.getCodigo().equals(turmaVO.getCodigo())) {
                turmaEncontrada = true;
                turmaTemp = turma;
            }
        }
        if (!turmaEncontrada) {
            turmaTemp = turmaVO;
            getHorariosConflitantes().add(turmaTemp);
        }
        turmaVO.setHorarioTurmaVOs(new ArrayList<HorarioTurmaVO>());
        boolean horarioEncontrado = false;
        for (HorarioTurmaVO horarioTurma : turmaTemp.getHorarioTurmaVOs()) {
            if (horarioTurma.getTurma().equals(horarioTurmaVO.getTurma())
                    && horarioTurma.getHoraInicial().equals(horarioTurmaVO.getHoraInicial())
                    && horarioTurma.getHoraFinal().equals(horarioTurmaVO.getHoraFinal())
                    && horarioTurma.getDiaSemana().equals(horarioTurmaVO.getDiaSemana())
                    && horarioTurma.getIdentificadorTurma().equals(horarioTurmaVO.getIdentificadorTurma())
                    ) {
                horarioEncontrado = true;
            }
        }

        if (!horarioEncontrado) {
            turmaTemp.getHorarioTurmaVOs().add(horarioTurmaVO);
        }
    }

    private void validarExistenciaRegistroHorarioTurma(HorarioTurmaVO htVO) throws Exception {
        List<HorarioTurmaVO> horariosTurma = getFacade().getHorarioTurma().consultarHorariosConflitantesParaProfessorSemAmbiente(htVO, turmaVO.getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS);
        for (HorarioTurmaVO existente : horariosTurma) {
            if (!htVO.getCodigo().equals(existente.getCodigo())){
                if((!turmaVO.getEmpresa().isPermiteHorariosConcorrentesParaProfessor() && !turmaVO.getEmpresa().isProfessorEmAmbientesDiferentesMesmoHorario())
                        || (!(turmaVO.getEmpresa().isPermiteHorariosConcorrentesParaProfessor() && htVO.getAmbiente().getCodigo().equals(existente.getAmbiente().getCodigo())
                        && (!htVO.getNivelTurma().getCodigo().equals(existente.getNivelTurma().getCodigo()) || !htVO.getTurma().equals(existente.getTurma())))
                        && (!turmaVO.getEmpresa().isProfessorEmAmbientesDiferentesMesmoHorario() && !htVO.getAmbiente().getCodigo().equals(existente.getAmbiente().getCodigo())))){
                    TurmaVO turmaVO1 = getFacade().getTurma().consultarPorChavePrimaria(existente.getTurma(), Uteis.NIVELMONTARDADOS_MINIMOS);
                    if (Calendario.maiorOuIgual(turmaVO1.getDataFinalVigencia(), Calendario.hoje())) {
                        adicionarListaDeErros(turmaVO1, existente);
                    }
                }
            }
        }
    }

    private void validacoesHorarioTurmaBancoDados(HorarioTurmaVO htVO) throws Exception {
        validarExistenciaRegistroHorarioTurma(htVO);
        if (!turmaVO.getEmpresa().isProfessorEmAmbientesDiferentesMesmoHorario()) {
            validarSeProfessorEstaEmOutroAmbiente(htVO);
        }

        if (!turmaVO.getEmpresa().isPermiteHorariosConcorrentesParaProfessor()) {
            validarSeProfessorEstaComHorarioConcocorrente(htVO);
        }
    }

    public void validarNoBancoHorariosIguais(HorarioTurmaVO horarioTurmaVO) throws Exception {
        //busca turmas de acordo com o horário com o mesmo dia da semana, horario, ambiente e nivel de turma de determinada empresa
        TurmaVO turma = getFacade().getTurma().consultarPorDiaSemanaHoraInicialHoraFinalAmbienteNivelTurmaProfessorEmpresa(
                horarioTurmaVO.getDiaSemana(),
                horarioTurmaVO.getHoraInicial(),
                horarioTurmaVO.getHoraFinal(),
                horarioTurmaVO.getAmbiente().getCodigo(),
                horarioTurmaVO.getProfessor().getCodigo(),
                turmaVO.getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);

        boolean horarioEstaDisponivel = true;
        if (turma != null) {
            horarioEstaDisponivel = (turma.consultarObjHorarioTurmaVO(horarioTurmaVO) == null);
        }

        if (turma != null && !turma.getCodigo().equals(turmaVO.getCodigo()) && !horarioEstaDisponivel) {
            if (horarioTurmaVO.isBotaoEditar()) {
                int posicao = turmaVO.getHorarioTurmaVOs().indexOf(getHorarioTurmaVO());
                String msg = setarMensagem(horarioTurmaVO, turma);
                if (posicao != -1) {
                    horarioTurmaVO = getHorarioTurmaVOTemporaria();
                    horarioTurmaVO.setNovoObj(false);
                    horarioTurmaVO.setBotaoEditar(true);
                }
//                adicionarListaDeErros(turma, horarioTurmaVO);
//                throw new Exception(msg);
            }
//            adicionarListaDeErros(turma, horarioTurmaVO);
//            throw new Exception(setarMensagem(horarioTurmaVO, turma));
        }
    }

    public void editarHorarioTurma() throws Exception {
        HorarioTurmaVO obj = (HorarioTurmaVO) context().getExternalContext().getRequestMap().get("horarioTurma");
        setHorarioTurmaVO(obj);
        getHorarioTurmaVO();
        setHorarioTurmaVOTemporaria((HorarioTurmaVO) obj.getClone(true));
        getHorarioTurmaVOTemporaria().setBotaoEditar(true);
        getHorarioTurmaVOTemporaria();
        getHorarioTurmaVOTemporaria().setObjetoVOAntesAlteracao(getHorarioTurmaVO());
        montarMensagemExceptionControle("msg_dados_editar");
    }

    /* Método responsável por remover um novo objeto da classe <code>HorarioTurma</code>
     * do objeto <code>turmaVO</code> da classe <code>Turma</code>
     */
    public String removerHorarioTurma() throws Exception {
        try {
            HorarioTurmaVO obj = (HorarioTurmaVO) context().getExternalContext().getRequestMap().get("horarioTurma");
            //procura por alunos matriculados na turma
            Long alunoMatriculadoHorario = getFacade().getMatriculaAlunoHorarioTurma().consultarPorHorarioTurmaPorPeriodoCount(obj.getCodigo(), Calendario.hoje(), Calendario.hoje(), false);
            if (alunoMatriculadoHorario.intValue() != 0) {
                throw new Exception("Há alunos matriculados no horário de turma de " + obj.getDiaSemana_Apresentar() + " das " + obj.getHoraInicial() + " às " + obj.getHoraFinal() + " com o professor(a) " + obj.getProfessor().getPessoa().getNome() + ". Por favor, transfira os alunos para outro horário antes de excluir esse horário.");
            }

            getFacade().getReposicao().nrAlunosReposicao(obj, Calendario.semanaAtualSQL(Calendario.hoje()));
            if(obj.getNrAlunoEntraramPorReposicao() != 0 || obj.getNrAlunoSairamPorReposicao()!= 0){
                throw new Exception("Existem reposições para o horário. Acesse a consulta de turmas e apague as reposições para excluir esse horário.");
            }
            //procura por alunos do historico que nao estao nesse horario mais
            Long alunoMatriculadoHorarioHistorico = getFacade().getMatriculaAlunoHorarioTurma().consultarPorHorarioTurmaCount(obj.getCodigo(), false);
            boolean existeFaltaCreditoTreino = getFacade().getControleCreditoTreino().existeControleCredito(obj.getCodigo());
            boolean existemReposicoesFutura = getFacade().getReposicao().existemReposicoesParaHorarioTurma(obj,true);
            if (existemReposicoesFutura) {
                throw new ConsistirException("Existem reposições futuras para este horário");
            }

            boolean existeModHorarioTurma = getFacade().getContratoModalidadeHorarioTurma().existeContratoModalidadeHorarioTurma(obj.getCodigo());
            boolean existemReposicoes = getFacade().getReposicao().existemReposicoesParaHorarioTurma(obj, false);
            boolean existeAulaDesmarcadaSemRepor = getFacade().getAulaDesmarcada().existeAulaDesmarcadaSemReporParaHorarioTurma(obj.getCodigo());
            if (alunoMatriculadoHorarioHistorico.intValue() == 0 && !existeFaltaCreditoTreino && !existemReposicoes && !existeModHorarioTurma && !existeAulaDesmarcadaSemRepor) {
                //horario novo sem alunos
                getFacade().getHorarioTurma().excluir(obj);
                getTurmaVO().excluirObjHorarioTurmaVOs(obj);
            } else {
                //horario com alunos em historico
                obj.registrarObjetoVOAntesDaAlteracao();
                obj.setSituacao("IN");
                getFacade().getHorarioTurma().desativar(obj);
                getTurmaVO().excluirObjHorarioTurmaVOs(obj);
            }

            montarMensagemExceptionControle("msg_dados_excluidos");
            incluirLogExclusaoHorario(obj);
            //para evitar que o objeto venha setado ao clicar em outro botão (adicionar)
            setHorarioTurmaVO(new HorarioTurmaVO());
            setSucesso(true);
            setErro(false);

            return "editar";
        } catch (Exception e) {
            montarMensagemExceptionControle("msg_erro", e);
            return "editar";
        }
    }

    public void setarMsgConflitoHorario(HorarioTurmaVO objExistente) throws Exception {
        if (getHorarioTurmaVO().isBotaoEditar()) {
            int posicao = turmaVO.getHorarioTurmaVOs().indexOf(getHorarioTurmaVO());
            if (posicao != -1) {
                setHorarioTurmaVO(getHorarioTurmaVOTemporaria());
                getHorarioTurmaVOTemporaria().setBotaoEditar(true);
                getHorarioTurmaVOTemporaria().setNovoObj(false);
                turmaVO.getHorarioTurmaVOs().set(posicao, getHorarioTurmaVO());
                setHorarioTurmaVOTemporaria(null);
            }
        }
        throw new Exception("O horário escolhido entra em conflito com o horário (DIA SEMANA: " + objExistente.getDiaSemana_Apresentar()
                + ", HORA INICIAL: " + objExistente.getHoraInicial()
                + ", HORA FINAL: " + objExistente.getHoraFinal()
                + ", AMBIENTE: " + objExistente.getAmbiente().getDescricao()
                + ", PROFESSOR: " + objExistente.getProfessor().getPessoa().getNome()
                + ") nessa turma");
    }
    /* Método responsável por adicionar um novo objeto da classe <code>HorarioTurma</code>
     * para o objeto <code>turmaVO</code> da classe <code>Turma</code>
     */

    public void validarHorarioTurma() throws Exception {
        try {
            Iterator i = turmaVO.getHorarioTurmaVOs().iterator();
            while (i.hasNext()) {
                HorarioTurmaVO horarioTurma = (HorarioTurmaVO) i.next();
                //Método que valida novamente os horários da turma em termos de padrão de formato de horas
                Calendario.validarHoras(horarioTurma.getHoraInicial(), horarioTurma.getHoraFinal());
                validarHorarioTurma(turmaVO.getHorarioTurmaVOs(), horarioTurma, getHorarioTurmaVOTemporaria());
            }
        } catch (Exception e) {
            setSucesso(false);
            setErro(true);
            throw e;
        }
    }

    public void validarHorarioTurma(List horariosTurma, HorarioTurmaVO obj, HorarioTurmaVO objTemporarioAnterior) throws Exception {
        Iterator i = horariosTurma.iterator();
        while (i.hasNext()) {
            //pesquisa na lista de horarios da turma antes de cadastrar no banco se há horários em conflito
            HorarioTurmaVO objExistente = (HorarioTurmaVO) i.next();
            if (objExistente.getDiaSemana().equals(obj.getDiaSemana())
                    && TurmaVO.validarSeEstaNoIntervaloHoras(objExistente.getHoraInicial(), objExistente.getHoraFinal(), obj.getHoraInicial(), obj.getHoraFinal())
                    && objExistente.getAmbiente().getCodigo().equals(obj.getAmbiente().getCodigo().intValue())
                    && objExistente.getProfessor().getPessoa().getCodigo().equals(obj.getProfessor().getPessoa().getCodigo().intValue())) {
                if (obj.isBotaoEditar() && objTemporarioAnterior != null) {
                    if (horariosTurma.indexOf(objExistente) != horariosTurma.indexOf(obj)) {
                        setarMsgConflitoHorario(objExistente);
                    } else {
                        continue;
                    }
                } else {
                    if (horariosTurma.indexOf(objExistente) != horariosTurma.indexOf(obj)) {
                        setarMsgConflitoHorario(objExistente);
                    } else {
                        continue;
                    }
                }
            }
        }
    }

    public String setarMensagem(HorarioTurmaVO horarioTurmaVO, TurmaVO turma) {
        return "Não é possivel gravar essa turma, pois o Horário das "
                + horarioTurmaVO.getHoraInicial() + " às "
                + horarioTurmaVO.getHoraFinal() + " do dia "
                + horarioTurmaVO.getDiaSemana_Apresentar() + " do ambiente " + horarioTurmaVO.getAmbiente().getDescricao()
                + " com o professor " + horarioTurmaVO.getProfessor().getPessoa().getNome()
                + " já está cadastrado para turma " + turma.getIdentificador()
                + " da modalidade " + turma.getModalidade().getNome() + " da empresa " + turma.getEmpresa().getNome();
    }

    public void irPaginaInicial() throws Exception {
        controleConsulta.setPaginaAtual(1);
        this.consultar();
    }

    public void irPaginaAnterior() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getPaginaAtual() - 1);
        this.consultar();
    }

    public void irPaginaPosterior() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getPaginaAtual() + 1);
        this.consultar();
    }

    public void irPaginaFinal() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getNrTotalPaginas());
        this.consultar();
    }

    /**
     * Método responsável por gerar uma lista de objetos do tipo <code>SelectItem</code> para preencher
     * o comboBox relativo ao atributo <code>Empresa</code>.
     */
    public void montarListaSelectItemEmpresa(String prm) throws Exception {
        List objs = new ArrayList();
        objs.add(new SelectItem(0, ""));
        List resultadoConsulta = consultarEmpresaPorNome(prm);
        Iterator i = resultadoConsulta.iterator();
        while (i.hasNext()) {
            EmpresaVO obj = (EmpresaVO) i.next();
            objs.add(new SelectItem(obj.getCodigo(), obj.getNome()));
        }
        setListaSelectItemEmpresa(objs);

        if (getEmpresaLogado().getCodigo() != 0) {
            this.getTurmaVO().setEmpresa(getEmpresaLogado());
        }
    }

    /**
     * Método responsável por atualizar o ComboBox relativo ao atributo <code>Empresa</code>.
     * Buscando todos os objetos correspondentes a entidade <code>Empresa</code>. Esta rotina não recebe parâmetros para filtragem de dados, isto é
     * importante para a inicialização dos dados da tela para o acionamento por meio requisições Ajax.
     */
    public void montarListaSelectItemEmpresa() {
        try {
            montarListaSelectItemEmpresa("");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * Método responsável por consultar dados da entidade <code><code> e montar o atributo <code>nome</code>
     * Este atributo é uma lista (<code>List</code>) utilizada para definir os valores a serem apresentados no ComboBox correspondente
     */
    public List consultarEmpresaPorNome(String nomePrm) throws Exception {
        return getFacade().getEmpresa().consultarPorNome(nomePrm,true, false, Uteis.NIVELMONTARDADOS_TODOS);
    }

    /* Método responsável por inicializar List<SelectItem> de valores do 
     * ComboBox correspondente ao atributo <code>situacao</code>
     */
    public List getListaSelectItemSituacaoHorarioTurma() throws Exception {
        List objs = new ArrayList();
        objs.add(new SelectItem("", ""));
        Hashtable situacaoHorarioTurmas = (Hashtable) Dominios.getSituacaoHorarioTurma();
        Enumeration keys = situacaoHorarioTurmas.keys();
        while (keys.hasMoreElements()) {
            String value = (String) keys.nextElement();
            String label = (String) situacaoHorarioTurmas.get(value);
            objs.add(new SelectItem(value, label));
        }
        SelectItemOrdemValor ordenador = new SelectItemOrdemValor();
        Collections.sort(objs, ordenador);
        return objs;
    }

    public List getListaSelectItemDiaSemana() throws Exception {
        List<SelectItem> objs = new ArrayList<SelectItem>();
        objs.add(new SelectItem("DM", "Domingo"));
        objs.add(new SelectItem("SG", "Segunda"));
        objs.add(new SelectItem("TR", "Terça"));
        objs.add(new SelectItem("QA", "Quarta"));
        objs.add(new SelectItem("QI", "Quinta"));
        objs.add(new SelectItem("SX", "Sexta"));
        objs.add(new SelectItem("SB", "Sábado"));
        return objs;
    }

    /**
     * Método responsável por gerar uma lista de objetos do tipo <code>SelectItem</code> para preencher
     * o comboBox relativo ao atributo <code>NivelTurma</code>.
     */
    public void montarListaSelectItemNivelTurma(String prm) throws Exception {
        List resultadoConsulta = consultarNivelTurmaPorDescricao(prm);
        Iterator i = resultadoConsulta.iterator();
        List objs = new ArrayList();
        objs.add(new SelectItem(0, ""));
        while (i.hasNext()) {
            NivelTurmaVO obj = (NivelTurmaVO) i.next();
            objs.add(new SelectItem(obj.getCodigo(), obj.getDescricao()));
        }
        setListaSelectItemNivelTurma(objs);
    }

    /**
     * Método responsável por atualizar o ComboBox relativo ao atributo <code>NivelTurma</code>.
     * Buscando todos os objetos correspondentes a entidade <code>NivelTurma</code>. Esta rotina não recebe parâmetros para filtragem de dados, isto é
     * importante para a inicialização dos dados da tela para o acionamento por meio requisições Ajax.
     */
    public void montarListaSelectItemNivelTurma() {
        try {
            montarListaSelectItemNivelTurma("");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * Método responsável por consultar dados da entidade <code><code> e montar o atributo <code>descricao</code>
     * Este atributo é uma lista (<code>List</code>) utilizada para definir os valores a serem apresentados no ComboBox correspondente
     */
    public List consultarNivelTurmaPorDescricao(String descricaoPrm) throws Exception {
        return getFacade().getNivelTurma().consultarPorDescricao(descricaoPrm, false, Uteis.NIVELMONTARDADOS_TODOS);
    }

    /**
     * Método responsável por gerar uma lista de objetos do tipo <code>SelectItem</code> para preencher
     * o comboBox relativo ao atributo <code>Ambiente</code>.
     */
    public void montarListaSelectItemAmbiente(String prm) throws Exception {
        List resultadoConsulta = consultarAmbientePorDescricao(prm);
        Iterator i = resultadoConsulta.iterator();
        List objs = new ArrayList();
        objs.add(new SelectItem(0, ""));
        while (i.hasNext()) {
            AmbienteVO obj = (AmbienteVO) i.next();
            objs.add(new SelectItem(obj.getCodigo(), obj.getDescricao()));
        }
        setListaSelectItemAmbiente(objs);
    }

    /**
     * Método responsável por atualizar o ComboBox relativo ao atributo <code>Ambiente</code>.
     * Buscando todos os objetos correspondentes a entidade <code>Ambiente</code>. Esta rotina não recebe parâmetros para filtragem de dados, isto é
     * importante para a inicialização dos dados da tela para o acionamento por meio requisições Ajax.
     */
    public void montarListaSelectItemAmbiente() {
        try {
            montarListaSelectItemAmbiente("");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * Método responsável por consultar dados da entidade <code><code> e montar o atributo <code>descricao</code>
     * Este atributo é uma lista (<code>List</code>) utilizada para definir os valores a serem apresentados no ComboBox correspondente
     */
    public List consultarAmbientePorDescricao(String descricaoPrm) throws Exception {
        return getFacade().getAmbiente().consultarPorDescricao(descricaoPrm, false, Uteis.NIVELMONTARDADOS_TODOS);
    }

    /**
     * Método responsável por gerar uma lista de objetos do tipo <code>SelectItem</code> para preencher
     * o comboBox relativo ao atributo <code>Professor</code>.
     */
    public void montarListaSelectItemProfessor(String prm) throws Exception {

        List resultadoConsulta = consultarColaboradorPorTipoColaborador(prm);
        Iterator i = resultadoConsulta.iterator();
        List objs = new ArrayList();
        objs.add(new SelectItem(0, ""));
        while (i.hasNext()) {
            ColaboradorVO obj = (ColaboradorVO) i.next();
            objs.add(new SelectItem(obj.getCodigo(), obj.getPessoa().getNome()));
        }
        Ordenacao.ordenarLista(objs, "label");
        setListaSelectItemProfessor(objs);
    }

    /**
     * Método responsável por atualizar o ComboBox relativo ao atributo <code>Professor</code>.
     * Buscando todos os objetos correspondentes a entidade <code>Colaborador</code>. Esta rotina não recebe parâmetros para filtragem de dados, isto é
     * importante para a inicialização dos dados da tela para o acionamento por meio requisições Ajax.
     */
    public void montarListaSelectItemProfessor() {
        try {
            montarListaSelectItemProfessor("");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * Método responsável por consultar dados da entidade <code><code> e montar o atributo <code>situacao</code>
     * Este atributo é uma lista (<code>List</code>) utilizada para definir os valores a serem apresentados no ComboBox correspondente
     */
    public List consultarColaboradorPorTipoColaborador(String tipoColaboradorPrm) throws Exception {
        List lista;
        if (getEmpresaLogado().getCodigo() == 0) {
            lista = getFacade().getColaborador().consultarPorTipoColaboradorAtivo(TipoColaboradorEnum.PROFESSOR, "AT", getTurmaVO().getEmpresa().getCodigo(), false, Uteis.NIVELMONTARDADOS_TODOS);
        } else {
            lista = getFacade().getColaborador().consultarPorTipoColaboradorAtivo(TipoColaboradorEnum.PROFESSOR, "AT", getEmpresaLogado().getCodigo(), false, Uteis.NIVELMONTARDADOS_TODOS);
        }

        return lista;
    }

    /**
     * Método responsável por gerar uma lista de objetos do tipo <code>SelectItem</code> para preencher
     * o comboBox relativo ao atributo <code>Modalidade</code>.
     */
    public void montarListaSelectItemModalidade(String prm) throws Exception {
        if (getTurmaVO().getEmpresa().getCodigo() != 0) {
            List resultadoConsulta = consultarModalidadePorUtilizaTurma(true);
            Iterator i = resultadoConsulta.iterator();
            List objs = new ArrayList();
            objs.add(new SelectItem(0, ""));
            while (i.hasNext()) {
                ModalidadeVO obj = (ModalidadeVO) i.next();
                objs.add(new SelectItem(obj.getCodigo(), obj.getNome()));
            }
            setListaSelectItemModalidade(objs);
        } else {
            setListaSelectItemModalidade(new ArrayList());
        }
        montarListaSelectItemProfessor();
    }

    /**
     * Método responsável por atualizar o ComboBox relativo ao atributo <code>Modalidade</code>.
     * Buscando todos os objetos correspondentes a entidade <code>Modalidade</code>. Esta rotina não recebe parâmetros para filtragem de dados, isto é
     * importante para a inicialização dos dados da tela para o acionamento por meio requisições Ajax.
     */
    public void montarListaSelectItemModalidade() throws Exception {
        montarListaSelectItemModalidade("");
    }

    /**
     * Método responsável por consultar dados da entidade <code><code> e montar o atributo <code>nome</code>
     * Este atributo é uma lista (<code>List</code>) utilizada para definir os valores a serem apresentados no ComboBox correspondente
     */
    public List consultarModalidadePorUtilizaTurma(Boolean turma) throws Exception {
        List objs = getFacade().getModalidade().consultarPorNomeUtilizaTurma("", this.getTurmaVO().getEmpresa().getCodigo(), false, turma, null, Uteis.NIVELMONTARDADOS_TODOS);
        if (this.getTurmaVO().getEmpresa().getCodigo() != 0) {
            List lista = getFacade().getModalidade().consultarPorNome("", 0, true, Uteis.NIVELMONTARDADOS_TODOS);
            Iterator i = lista.iterator();
            while (i.hasNext()) {
                ModalidadeVO object = (ModalidadeVO) i.next();
                if (object.getModalidadeEmpresaVOs().size() == 0 && object.getUtilizarTurma()) {
                    objs.add(object);
                }
            }
        }
        return objs;
    }

    public void inicializarListasSelectItemTodosComboBox() throws Exception{
        montarListaSelectItemEmpresa();
        montarListaSelectItemModalidade();
        montarListaSelectItemProfessor();
        montarListaSelectItemAmbiente();
        montarListaSelectItemNivelTurma();
        montarSelectItemTipoAntecedenciaMarcarAula();
    }

    public List getTipoConsultaCombo() {
        List<SelectItem> itens = new ArrayList<SelectItem>();
        itens.add(new SelectItem("nome", "Nome da Turma"));
        itens.add(new SelectItem("identificador", "Identificador da Turma"));
        itens.add(new SelectItem("codigo", "Código"));
        itens.add(new SelectItem("nomeModalidade", "Modalidade"));
        itens.add(new SelectItem("dataInicialVigencia", "Data Inicial de Vigência"));
        itens.add(new SelectItem("idadeMinima", "Idade Mínima"));
        itens.add(new SelectItem("idadeMaxima", "Idade Máxima"));
        itens.add(new SelectItem("nivel", "Nivel"));
        return itens;
    }

    public List getTipoConsultaComboModalidade() {
        List<SelectItem> itens = new ArrayList<SelectItem>();
        itens.add(new SelectItem("codigo", "Código"));
        itens.add(new SelectItem("nome", "Nome"));
        return itens;
    }

    public List getTipoConsultaComboColaborador() {
        List<SelectItem> itens = new ArrayList<SelectItem>();
        itens.add(new SelectItem("codigo", "Código"));
        itens.add(new SelectItem("nome", "Nome"));
        return itens;
    }

    /**
     * Rotina responsável por organizar a paginação entre as páginas resultantes de uma consulta.
     */
    public String inicializarConsultar() {
        if (turmaVO.getUsuarioVO().getAdministrador()) {
            turmaVO.setEmpresa(new EmpresaVO());
        }
        setPaginaAtualDeTodas("0/0");
        setListaConsulta(new ArrayList());
        setSucesso(false);
        setErro(false);
        definirVisibilidadeLinksNavegacao(0, 0);
        montarMensagemExceptionControle("msg_entre_prmconsulta");
        return "consultar";
    }

    /**
     * Operação que inicializa as Interfaces Façades com os respectivos objetos de
     * persistência dos dados no banco de dados. 
     */
    protected boolean inicializarFacades() {
        try {
            super.inicializarFacades();
            return true;
        } catch (Exception e) {
            montarMensagemExceptionControle("msg_erro_conectarBD", e);
            return false;
        }
    }

    public Object incluirHorarioNaListaParaAdicionar()  {
        try {
            setErro(false);
            setSucesso(false);
            montarMensagemExceptionControle("");

            HorariosTurma ht = new HorariosTurma();

            ht.setHoraInicial(getHt_horaInicio());
            setHt_horaInicio("");
            ht.setHoraFinal(getHt_horaFim());
            setHt_horaFim("");

            if(ht.getHoraInicial().length() < 5 || ht.getHoraFinal().length() < 5){
                throw new Exception("Horário inicial ou Horário final inválido!");
            }
            if (ht.getHoraFinal().equals("") ||
                    ht.getHoraFinal().equals("") ||
                    ht.getHoraInicial().equals(ht.getHoraFinal()) ||
                    !Calendario.horasMenor(ht.getHoraInicial(), ht.getHoraFinal())) {
                return null;
            }

            try {
                Calendario.validarHoras(ht.getHoraInicial(), ht.getHoraFinal());
            } catch (Exception ignored) {

            }

            List<HorariosTurma> horariosTurmaList = new ArrayList<HorariosTurma>(getHorariosIncluidos());
            boolean horarioExiste = false;
            for (HorariosTurma horariosTurma : horariosTurmaList) {
                if (horariosTurma.getHoraInicial().equals(ht.getHoraInicial())
                        && horariosTurma.getHoraFinal().equals(ht.getHoraFinal())) {
                    horarioExiste = true;
                }
                if (!getEmpresaLogado().isPermiteHorariosConcorrentesParaProfessor()) {
                    horarioExiste = horarioExiste ||
                            Calendario.horaEstaEntreIntervaloHoras(ht.getHoraInicial(), horariosTurma.getHoraInicial(), horariosTurma.getHoraFinal()) ||
                            Calendario.horaEstaEntreIntervaloHoras(ht.getHoraFinal(), horariosTurma.getHoraInicial(), horariosTurma.getHoraFinal()) ||
                            Calendario.horaEstaEntreIntervaloHoras(horariosTurma.getHoraInicial(), ht.getHoraInicial(), ht.getHoraFinal()) ||
                            Calendario.horaEstaEntreIntervaloHoras(horariosTurma.getHoraFinal(), ht.getHoraInicial(), ht.getHoraFinal());
                    setErro(true);
                    setSucesso(false);
                    montarMensagemExceptionControle("", new ConsistirException("Não pode ter horários concorrentes."));
                }
            }
            if (!horarioExiste) {
                getHorariosIncluidos().add(ht);
                setErro(false);
                setSucesso(false);
                montarMensagemExceptionControle("");
            }
        }catch (Exception ex){
            montarMensagemExceptionControle("msg_erro", ex);
            setMensagemDetalhada("msg_erro", ex.getMessage());
        }
        return null;
    }

    public Object removerHorarioDaListaParaAdicionar(){
        HorariosTurma obj = (HorariosTurma) context().getExternalContext().getRequestMap().get("horario");
        getHorariosIncluidos().remove(obj);
        return null;
    }

    public void exportar(ActionEvent evt) throws Exception {
        ExportadorListaControle exportadorListaControle = (ExportadorListaControle) JSFUtilities.getFromRequest(ExportadorListaControle.class.getSimpleName());
        String paramsTableFiltrada = context().getExternalContext().getRequestParameterMap().get("paramsTableFiltrada");

        String[] split = paramsTableFiltrada.split(",");
        String campoOrdenacao = split[0].replace("[", "");
        String ordem = split[1];
        String filtro = split[2].replace("''", "");
        filtro = filtro.replace("]", "");
        List listaParaImpressao = getFacade().getTurma().consultarParaImpressao(filtro, ordem, campoOrdenacao, getEmpresaLogado() == null ? 0 : getEmpresaLogado().getCodigo(),getTipoTurma());
        exportadorListaControle.exportar(evt, listaParaImpressao, filtro, null);
    }

    private void incluirLogInclusaoTurma() throws Exception {
        try {
            turmaVO.setObjetoVOAntesAlteracao(new TurmaVO());
            turmaVO.setNovoObj(true);
            registrarLogObjetoVO(turmaVO, turmaVO.getCodigo(), "TURMA ", 0);
        } catch (Exception e) {
            registrarLogErroObjetoVO("TURMA", turmaVO.getCodigo(), "ERRO AO GERAR LOG DE INCLUSÃO DE TURMA", this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
        turmaVO.setNovoObj(false);
        turmaVO.registrarObjetoVOAntesDaAlteracao();
    }

    private void setLogAdicionarHorarioTurma(HorarioTurmaVO horarioIncluir) throws Exception {
        try {
            horarioIncluir.setObjetoVOAntesAlteracao(new HorarioTurmaVO());
            horarioIncluir.setNovoObj(true);
            registrarLogObjetoVO(horarioIncluir, turmaVO.getCodigo(), "TURMA ", 0);
            horarioIncluir.setNovoObj(false);
        } catch (Exception e) {
            registrarLogErroObjetoVO("TURMA", turmaVO.getCodigo(), "ERRO AO GERAR LOG DE INCLUSÃO DE TURMA", this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
    }

    public void realizarConsultaLogObjetoSelecionado() {
        LoginControle loginControle = (LoginControle) context().getExternalContext().getSessionMap().get("LoginControle");
        String nomeClasse = turmaVO.getClass().getSimpleName();
        nomeClasse = nomeClasse.substring(0, nomeClasse.length() - 2);
        loginControle.setNomeClasse(loginControle.getNomeEntidadeCamposLog("prt_" + nomeClasse + "_tituloForm"));
        loginControle.consultarLogObjetoSelecionado(nomeClasse.toUpperCase(),
                turmaVO.getCodigo(), 0);

    }
    
    public void realizarConsultaLogObjetoGeral() {
        turmaVO = new TurmaVO();
        realizarConsultaLogObjetoSelecionado();
    }

    public List getListaSelectItemNivelTurma() {
        return listaSelectItemNivelTurma;
    }

    public void setListaSelectItemNivelTurma(List listaSelectItemNivelTurma) {
        this.listaSelectItemNivelTurma = listaSelectItemNivelTurma;
    }

    public List getListaSelectItemAmbiente() {
        return listaSelectItemAmbiente;
    }

    public void setListaSelectItemAmbiente(List listaSelectItemAmbiente) {
        this.listaSelectItemAmbiente = listaSelectItemAmbiente;
    }

    public List getListaSelectItemProfessor() {
        return listaSelectItemProfessor;
    }

    public void setListaSelectItemProfessor(List listaSelectItemProfessor) {
        this.listaSelectItemProfessor = listaSelectItemProfessor;
    }

    public HorarioTurmaVO getHorarioTurmaVO() {
        return horarioTurmaVO;
    }

    public void setHorarioTurmaVO(HorarioTurmaVO horarioTurmaVO) {
        this.horarioTurmaVO = horarioTurmaVO;
    }

    public List getListaSelectItemModalidade() {
        return listaSelectItemModalidade;
    }

    public void setListaSelectItemModalidade(List listaSelectItemModalidade) {
        this.listaSelectItemModalidade = listaSelectItemModalidade;
    }

    public TurmaVO getTurmaVO() {
        return turmaVO;
    }

    public void setTurmaVO(TurmaVO turmaVO) {
        this.turmaVO = turmaVO;
    }

    public String getCampoConsultaColaborador() {
        return campoConsultaColaborador;
    }

    public void setCampoConsultaColaborador(String campoConsultaColaborador) {
        this.campoConsultaColaborador = campoConsultaColaborador;
    }

    public String getCampoConsultaModalidade() {
        return campoConsultaModalidade;
    }

    public void setCampoConsultaModalidade(String campoConsultaModalidade) {
        this.campoConsultaModalidade = campoConsultaModalidade;
    }

    public List getListaConsultaColaborador() {
        return listaConsultaColaborador;
    }

    public void setListaConsultaColaborador(List listaConsultaColaborador) {
        this.listaConsultaColaborador = listaConsultaColaborador;
    }

    public List getListaConsultaModalidade() {
        return listaConsultaModalidade;
    }

    public void setListaConsultaModalidade(List listaConsultaModalidade) {
        this.listaConsultaModalidade = listaConsultaModalidade;
    }

    public String getValorConsultaColaborador() {
        return valorConsultaColaborador;
    }

    public void setValorConsultaColaborador(String valorConsultaColaborador) {
        this.valorConsultaColaborador = valorConsultaColaborador;
    }

    public String getValorConsultaModalidade() {
        return valorConsultaModalidade;
    }

    public void setValorConsultaModalidade(String valorConsultaModalidade) {
        this.valorConsultaModalidade = valorConsultaModalidade;
    }

    public List getListaSelectItemEmpresa() {
        return listaSelectItemEmpresa;
    }

    public void setListaSelectItemEmpresa(List listaSelectItemEmpresa) {
        this.listaSelectItemEmpresa = listaSelectItemEmpresa;
    }

    public AmbienteVO getAmbiente() {
        return ambiente;
    }

    public void setAmbiente(AmbienteVO ambiente) {
        this.ambiente = ambiente;
    }

    public ColaboradorVO getColaborador() {
        return colaborador;
    }

    public void setColaborador(ColaboradorVO colaborador) {
        this.colaborador = colaborador;
    }

    public NivelTurmaVO getNivelTurma() {
        return nivelTurma;
    }

    public void setNivelTurma(NivelTurmaVO nivelTurma) {
        this.nivelTurma = nivelTurma;
    }

    public TipoColaboradorVO getTipoColaboradorVO() {
        return tipoColaboradorVO;
    }

    public void setTipoColaboradorVO(TipoColaboradorVO tipoColaboradorVO) {
        this.tipoColaboradorVO = tipoColaboradorVO;
    }

    public HorarioTurmaVO getHorarioTurmaVOTemporaria() {
        return horarioTurmaVOTemporaria;
    }

    public void setHorarioTurmaVOTemporaria(HorarioTurmaVO horarioTurmaVOTemporaria) {
        this.horarioTurmaVOTemporaria = horarioTurmaVOTemporaria;
    }

    public String getFecharModalHorarioTurma() {
        return fecharModalHorarioTurma;
    }

    public void setFecharModalHorarioTurma(String fecharModalHorarioTurma) {
        this.fecharModalHorarioTurma = fecharModalHorarioTurma;
    }

    public List<String> getDiasSelecionados() {
        return diasSelecionados;
    }

    public void setDiasSelecionados(List<String> diasSelecionados) {
        this.diasSelecionados = diasSelecionados;
    }

    public boolean isClone() {
        return clone;
    }

    public void setClone(boolean clone) {
        this.clone = clone;
    }

    public String getHt_horaInicio() {
        return ht_horaInicio;
    }

    public void setHt_horaInicio(String ht_horaInicio) {
        this.ht_horaInicio = ht_horaInicio;
    }

    public String getHt_horaFim() {
        return ht_horaFim;
    }

    public void setHt_horaFim(String ht_horaFim) {
        this.ht_horaFim = ht_horaFim;
    }

    public List<HorariosTurma> getHorariosIncluidos() {
        return horariosIncluidos;
    }

    public void setHorariosIncluidos(List<HorariosTurma> horariosIncluidos) {
        this.horariosIncluidos = horariosIncluidos;
    }

    public List<HorarioTurmaVO> getHorariosParaGravar() {
        return horariosParaGravar;
    }

    public void setHorariosParaGravar(List<HorarioTurmaVO> horariosParaGravar) {
        this.horariosParaGravar = horariosParaGravar;
    }

    public List<TurmaVO> getHorariosConflitantes() {
        return horariosConflitantes;
    }

    public void setHorariosConflitantes(List<TurmaVO> horariosConflitantes) {
        this.horariosConflitantes = horariosConflitantes;
    }

    public boolean isPermiteAlterarModalidade() {
        return permiteAlterarModalidade;
    }

    public void setPermiteAlterarModalidade(boolean permiteAlterarModalidade) {
        this.permiteAlterarModalidade = permiteAlterarModalidade;
    }

    public List<SelectItem> getListaSelectItemTipoAntecedenciaMarcarAula() {
        return listaSelectItemTipoAntecedenciaMarcarAula;
    }

    public void setListaSelectItemTipoAntecedenciaMarcarAula(List<SelectItem> listaSelectItemTipoAntecedenciaMarcarAula) {
        this.listaSelectItemTipoAntecedenciaMarcarAula = listaSelectItemTipoAntecedenciaMarcarAula;
    }
    
     public void incluirLogExclusaoTurma() throws Exception {
        try {
            turmaVO.setObjetoVOAntesAlteracao(new TurmaVO());
            turmaVO.setNovoObj(true);
            registrarLogExclusaoTodosDadosObjetoVO(turmaVO, turmaVO.getCodigo(), "TURMA", 0);
        } catch (Exception e) {
            registrarLogErroObjetoVO("TURMA", turmaVO.getCodigo(), "ERRO AO GERAR LOG DE EXCLUSÃO DE TURMA ", this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
    }

    /**
     * Inclui o log de alteração de categoria
     * @throws Exception
     */
    public void incluirLogAlteracao() throws Exception {
        try {
            registrarLogObjetoVO(turmaVO, turmaVO.getCodigo(), "TURMA", 0);
        } catch (Exception e) {
            registrarLogErroObjetoVO("TURMA", turmaVO.getCodigo(), "ERRO AO GERAR LOG DE ALTERAÇÃO DE TURMA ", this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
        turmaVO.registrarObjetoVOAntesDaAlteracao();
    }
    
    public void incluirLogExclusaoHorario(HorarioTurmaVO horarioVO) throws Exception {
        try {
            if(!horarioVO.getSituacao().equals("IN")){
                horarioVO.setObjetoVOAntesAlteracao(new HorarioTurmaVO());
                horarioVO.setNovoObj(true);
                registrarLogExclusaoTodosDadosObjetoVO(horarioVO, turmaVO.getCodigo(), "TURMA", 0);
            } else {
                LogVO logVO = new LogVO();
                logVO.setOperacao("ALTERAÇÃO");
                logVO.setChavePrimaria(String.valueOf(horarioVO.getTurma()));
                logVO.setChavePrimariaEntidadeSubordinada(horarioVO.getCodigo().toString());
                logVO.setNomeEntidade("TURMA");
                logVO.setNomeEntidadeDescricao("Horário Turma");
                logVO.setResponsavelAlteracao(getUsuarioLogado().getNome());
                logVO.setUserOAMD(getUsuarioLogado().getUserOamd());
                logVO.setNomeCampo("TODOS");
                logVO.setDataAlteracao(negocio.comuns.utilitarias.Calendario.hoje());
                logVO.setValorCampoAnterior("-");
                logVO.setValorCampoAlterado("INATIVADO O HORÁRIO DE CÓDIGO " + horarioVO.getCodigo());

                logVO.setPessoa(0);
                getFacade().getLog().incluirSemCommit(logVO);

                horarioVO.registrarObjetoVOAntesDaAlteracao();
            }
        } catch (Exception e) {
            registrarLogErroObjetoVO("TURMA", turmaVO.getCodigo(), "ERRO AO GERAR LOG DE EXCLUSÃO DE HORARIOTURMA ", this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
    }


    public void alterarAba() {
        montarMensagemExceptionControle("");
    }

    public String getAbaSelecionada() {
        return abaSelecionada;
    }

    public void setAbaSelecionada(String abaSelecionada) {
        this.abaSelecionada = abaSelecionada;
    }

    public String getTipoTurma() {
        if (this.tipoTurma == null) {
            this.tipoTurma = "VI";
        }
        return tipoTurma;
    }

    public void setTipoTurma(String tipoTurma) {
        this.tipoTurma = tipoTurma;
    }

    public List<SelectItem> getListaTipoTurma() {
        return listaTipoTurma;
    }

    public void setListaTipoTurma(List<SelectItem> listaTipoTurma) {
        this.listaTipoTurma = listaTipoTurma;
    }

    public void setMsgAlert(String msgAlert) {
        this.msgAlert = msgAlert;
    }

    public String getMsgAlert() {
        if (msgAlert == null) {
            return "";
        }
        return msgAlert;
    }


    public void confirmarExcluir(){
        MensagemGenericaControle control = (MensagemGenericaControle) getControlador(MensagemGenericaControle.class);
        control.setMensagemDetalhada("", "");
        setMsgAlert("Richfaces.showModalPanel('mdlMensagemGenerica');");
        control.init("Exclusão de Turma",
                "Deseja excluir a Turma?",
                this, "excluir", "", "", "", "grupoBtnExcluir,containerMensagemException");
    }

    public EventJSON getAulaSpiviSelecionada() {
        return aulaSpiviSelecionada;
    }

    public void setAulaSpiviSelecionada(EventJSON aulaSpiviSelecionada) {
        this.aulaSpiviSelecionada = aulaSpiviSelecionada;
    }

    public boolean isIntegracaoSpivi() {
        return integracaoSpivi;
    }

    public void setIntegracaoSpivi(boolean integracaoSpivi) {
        this.integracaoSpivi = integracaoSpivi;
    }

    public void sincronizarGympassBooking() {
        try {
            limparMsg();

            getFacade().getTurma().enviarGymPassBooking(turmaVO);
            montarSucessoGrowl("Turma sincronizada.");
        } catch (Exception ex) {
            montarErro(ex);
        }
    }
}

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package controle.crm;

import br.com.pactosolucoes.comuns.util.JSFUtilities;
import controle.arquitetura.SuperControle;
import controle.arquitetura.view.ComboBoxEmpresaControle;
import controle.arquitetura.view.TreeViewControle;
import controle.arquitetura.view.TreeViewNode;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.crm.GrupoColaboradorParticipanteVO;
import negocio.comuns.crm.GrupoColaboradorVO;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.Uteis;

/**
 * Classe encapsula obtenção de grupo de colaboradores para o componente TreeViewControle
 * @see controle.arquitetura.view.TreeViewControle
 * <AUTHOR>
 */
public class TreeViewColaboradorControle extends SuperControle {

    @Override
    public EmpresaVO getEmpresa() {
        return (EmpresaVO) JSFUtilities.getManagedBeanValue(
                ComboBoxEmpresaControle.class.getSimpleName() + ".empresaVO");
    }

    public void carregarArvoreGrupoColaboradores() throws Exception {
        TreeViewNode nodeMarcado = null;
        ColaboradorVO colabLogado = null;
        //
        if (!getUsuarioLogado().getAdministrador()) {
            Uteis.logar(null, "TreeViewColaboradorControle -> Colaborador  Logado -> "
                    + getUsuarioLogado().getColaboradorVO().getPessoa().getNome());
            final UsuarioVO u = getUsuarioLogado();
            colabLogado = u.getColaboradorVO() != null && u.getColaboradorVO().getCodigo().intValue() != 0 ? u.getColaboradorVO() : null;
        }
        List<GrupoColaboradorVO> listaGrupos = getFacade().
                getGrupoColaborador().consultarPorCodigo(0,
                false, Uteis.NIVELMONTARDADOS_TODOS, getEmpresa().getCodigo());
        listaGrupos = Ordenacao.ordenarLista(listaGrupos, "descricao");
        Map<String, TreeViewNode> props = new HashMap<String, TreeViewNode>();
        int grupo = 1;
        for (GrupoColaboradorVO grupoColaboradorVO : listaGrupos) {
            //apenas adicionar grupos que possuam colaboradores relacionados
            if (grupoColaboradorVO.getGrupoColaboradorParticipanteVOs().isEmpty()) {
                continue;
            }
            TreeViewNode node = new TreeViewNode(null, grupoColaboradorVO.getDescricao());
            props.put(String.valueOf(grupo), node);

            List<GrupoColaboradorParticipanteVO> listaParticipantes =
                    grupoColaboradorVO.getGrupoColaboradorParticipanteVOs();
            int participante = 1;
            List<ColaboradorVO> listaColaboradorGrupo = new ArrayList();

            for (GrupoColaboradorParticipanteVO grupoColaboradorParticipanteVO : listaParticipantes) {
                listaColaboradorGrupo.add(grupoColaboradorParticipanteVO.getColaboradorParticipante());
            }
            listaColaboradorGrupo = Ordenacao.ordenarLista(listaColaboradorGrupo, "pessoa");
            for (ColaboradorVO colaborador : listaColaboradorGrupo) {
                node = new TreeViewNode(colaborador, colaborador.getPessoa().getNome());
                if (colabLogado != null) {
                    if (colaborador.getCodigo().intValue() == colabLogado.getCodigo().intValue()) {
                        node.setMarcado(true);
                        nodeMarcado = node;
                    }
                }
                props.put(String.valueOf(grupo) + "." + String.valueOf(participante),
                        node);
                participante++;
            }
            grupo++;
        }

        TreeViewControle treeView = (TreeViewControle) getControlador(
                TreeViewControle.class.getSimpleName());

        if (treeView != null) {
            treeView.limpar();
            treeView.setProps(props);
            if (colabLogado != null && nodeMarcado != null) {
                treeView.getNodesMarcados().add(nodeMarcado);
            }
        }
    }
}
